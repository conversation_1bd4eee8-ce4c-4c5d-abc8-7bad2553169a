<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="167 30 723 948"><!--oldViewBox="0 0 1023 1024"--><style>.B{fill:#e2e0e0}.C{fill:#cbc9c9}.D{fill:#ebeaea}.E{fill:#dbdad9}.F{fill:#b7b5b4}.G{fill:#a5a3a2}.H{fill:#d2d0d0}.I{fill:#c4c2c1}.J{fill:#e6e5e5}.K{fill:#bebcbc}.L{fill:#9c9b99}.M{fill:#92908f}.N{fill:#f5f4f4}.O{fill:#afadac}.P{fill:#191817}.Q{fill:#1e1d1c}.R{fill:#444342}.S{fill:#d4d3d2}.T{fill:#010101}.U{fill:#3b3a3a}.V{fill:#686665}.W{fill:#fbfafa}.X{fill:#7b7a78}.Y{fill:#858483}.Z{fill:#8b8988}.a{fill:#4c4b4a}.b{fill:#7f7e7d}.c{fill:#0b0b0a}.d{fill:#62615f}.e{fill:#2e2d2d}.f{fill:#292828}.g{fill:#323130}.h{fill:#131211}.i{fill:#f1f0f0}.j{fill:#545352}.k{fill:#242323}.l{fill:#767574}.m{fill:#353433}.n{fill:#737171}.o{fill:#595856}.p{fill:#6e6d6b}</style><path d="M293 592l1-1c1 1 1 1 1 2v2c-1 0-2 0-3-1v-1l1-1z" class="X"></path><path d="M295 319h1l1 1c-1 2-1 2-2 3h-1c-1-1-1-1-1-3l2-1z" class="Z"></path><path d="M842 854c1-1 1-1 2-1 1 1 1 1 1 3l-1 1h-1l-2-1c0-1 0-1 1-2z" class="M"></path><path d="M710 350h3l2 1c0 2 0 2-1 3s-1 1-2 1l-2-2v-3zm37 172h3l1 1v2c-1 1-1 2-3 3h-1c-1 0-2-1-2-1v-2c1-2 1-2 2-3zm55 79h1c1 1 2 2 3 4h-1c0 2 0 2-1 2-1 1-2 1-3 0-1 0-2-2-2-3l1-1c0-2 1-2 2-2z" class="i"></path><path d="M333 279l1-1c-2 3-3 6-6 7-4 1-8 1-11-1l-1-1c2-1 3-1 5-1l1 1 1 1 1-1 2 1 1-2 2 1c2-1 2-2 4-4z" class="m"></path><path d="M160 494h1c1-1 2-1 4 0 1 1 1 2 1 3s-1 2-1 3h-2c-2 0-3-1-4-3v-1c0-1 0-2 1-2zm190-104c1-1 2-1 3-1l3 3v2c-1 1-1 1-3 2l-1 1s-1-1-2-1-2-2-2-3c0-2 1-2 2-3zm342-95h4c1 1 2 2 2 3-1 2-1 2-3 3h-1c-3 0-3 0-5-2v-1c1-2 2-2 3-3z" class="N"></path><path d="M368 777l2 1c-1 1-1 3-1 3 0 1 1 1 1 2s-1 1 0 3h7 0c-2 0-7 0-8 1l2 1v1c1 0 2 1 3 1-1 2 0 5-1 7l-2-3c-3-5-4-13-3-17z" class="m"></path><path d="M666 575l3-2 2-2h0c1-1 2-2 2-3v-1c2-2 5-5 8-5 1 1 1 1 1 2-1 2-2 3-3 4-3 0-5 3-7 5h0c-2 1-3 1-4 1l-2 1z" class="B"></path><path d="M300 749c-2-1-3-3-4-4-1-2-1-5 0-7s2-3 4-4c3-1 5-1 7 0-3 0-5 0-8 2h1c1 0 2-1 4 0h-1c-1 0-2 1-2 2l-1 2 1 1c0 1 0 2-1 2l-2 1v1c1 1 0 1 2 1h0v3z" class="g"></path><path d="M307 734c3 1 4 2 5 5 1 2 0 3-1 5 0 3-2 4-4 5-3 1-5 1-7 0v-3h0c-2 0-1 0-2-1v-1l2-1c1 0 1-1 1-2l1 1v2 1l1-1h-1 3c-1 1-1 1-1 2 2 1 2 1 4 1 2-2 3-4 4-7-2-2-3-4-5-6z" class="T"></path><path d="M362 620c-2 1-10 2-11 2l-2-1v1c-1 1-2 1-3 1-1-1-4-2-4-3 1 0 2-1 4-2h0 1l-1 2h1l1-1h2c2-1 9-1 12-1v2z" class="V"></path><path d="M349 608c3 1 11 5 14 4-1-1-2-2-4-2s-3-1-5-1l-5-2v-1c1 0 2 1 3 1 2 1 5 1 7 2s4 1 4 3h1c1-1 1-2 1-3l2 2-3 3h0 0l1 1-1 1v1c2 0 4-1 6-2h3v1h-1v1 1c-2 0-3 0-4 1h-1l1-1-1-1-2 2h0c1 1 1 1 1 2l-2-1h-2v-2-3c-3-3-10-5-14-6l1-1z" class="h"></path><path d="M196 938l1-1c7 1 12 3 19 3l6 2 7 2-2 2c-2 0-5-1-7-2l-24-6z" class="Q"></path><path d="M709 742l3 2h0c0 2 1 2 1 4h1c0 1 2 3 3 4h2 0c-1 1-1 1-1 2l1 1h0c2-1 3-1 4-1l1-1-1-1v-2l1 1 2 1h1 1v1c-3 2-6 3-9 3s-6-2-8-4c-2-3-2-6-2-10z" class="h"></path><path d="M348 609l-6-3c0-1 0 0-1 0s-1 0-2 1h-4c-1-1-2-2-3-4 0-1 0-2 1-4 1-1 2-2 4-2 1 0 2 0 3 1s2 3 2 4v1l2 1-1 1c2 2 4 2 6 3l-1 1z" class="U"></path><path d="M336 598c1-1 1-1 2-1l2 2c1 1 1 2 1 4l-2 1h-1c-2-1-4-1-4-3s1-2 2-3z" class="N"></path><path d="M301 741l-1-1 1-2c0-1 1-2 2-2h1c-2-1-3 0-4 0h-1c3-2 5-2 8-2 2 2 3 4 5 6-1 3-2 5-4 7-2 0-2 0-4-1 0-1 0-1 1-2h-3 1l-1 1v-1-2l-1-1z" class="J"></path><path d="M413 961l32 2h3 6l-3 1h2c1 0 1 0 2 1l-55-2v-2h12 1z" class="c"></path><path d="M625 655l16 3v3 36 15l-2 2h0v-2l-5-1 1-1 4 1v-14-34-2l-2-1c1 0 1 0 2-1h0c-3-1-5-1-8-2l-6-1v-1z" class="e"></path><path d="M688 897l41 3h0v2l-10-1-13-1c0 1 0 1 1 1v1h-3v-2h-2c-1 1-1 3-2 3h-3c-3 0-5 0-8-1l1-1-2-1 1-1h1l-2-2z" class="V"></path><path d="M550 963v-1c1 0 1 0 2 1 2 1 3 1 6 1l14-1 14 1h10v1h-2l-63 1v-1c3-1 9 0 13-1h0c2 0 4 1 6 0v-1z" class="h"></path><path d="M313 280c-1-2-1-5 0-8 0-2 2-4 5-6 2-1 5-2 8-1h1 0c3 1 5 3 6 6 2 2 2 5 1 7l-1 1c1-2 1-2 0-4 0-3-1-6-3-7-1-1-2-1-3-2h-1-5 0l-1 1c-1 0 0 0-1 1l1 1h-1c-1 1-1 2-1 3l-1 1c1 1 3 1 4 1v1h0-1l-1 1c1 1 1 2 3 3v1c1 1 0 2 0 3l-1-1c-2 0-3 0-5 1l-3-3z" class="R"></path><path d="M313 280c1-1 2-2 4-3v1c1 1 3 2 4 4-2 0-3 0-5 1l-3-3z" class="Q"></path><path d="M184 933l-3-2c-2-1-5-3-5-5-1-1 0-2 1-3 6-8 34-14 44-15l1 1-9 2c-2 0-4 1-6 1l1 3c-2 0-4 1-6 1v1h-2l-4-1-1-1c-2 1-4 2-5 3-4 1-7 2-10 4l-2 2c1 2 1 3 3 5 1 0 2 1 2 1h1l3 2c-1 0-2 1-3 1z" class="P"></path><path d="M207 912l1 3c-2 0-4 1-6 1v1h-2l-4-1-1-1 12-3z" class="G"></path><path d="M371 788h1c2 0 6-1 7 0l-1 6h0c0 7-2 13-4 19-4 10-10 19-17 27-5 5-12 10-17 15l-1-1c6-6 13-11 19-18 9-9 15-22 18-34l-3-5c1-2 0-5 1-7-1 0-2-1-3-1v-1z" class="c"></path><path d="M237 793h3c2 1 4 2 5 5 1 1 0 4 0 6-2 2-4 3-6 4-1 0-2 0-3-1-2 0-4-2-5-4s0-4 0-6c1-2 4-3 6-4z" class="f"></path><path d="M237 794c1 0 1-1 2 0h1l2 1c1 1 2 3 2 4s0 2-1 3l-1-1h0l-2 2c-1-2-3-3-4-4s-1-1 0-3h-2c1-2 2-2 3-2z" class="D"></path><path d="M374 813l1 1v2l-2 1c0 1 1 2 0 3 0 1-1 1-2 2v2c0 1-2 3-2 4l-1 1c-2 3-6 7-7 11h0c1 0 1 0 2-1l1 1-2 2c-1-1-1 0-2-1h-1l-1 1h-1c0 1 0 1 1 2l-2 2c-1 0-1 1-1 1l-2 2-12 12c0 1-1 3-2 4 0-1 0-1-1-2l3-3c-1-1-1-2-1-2-1 0-2 0-3 1l-1 1-1-1c1-1 3-3 5-4 5-5 12-10 17-15 7-8 13-17 17-27z" class="K"></path><path d="M697 903h3c1 0 1-2 2-3h2v2h3v-1c-1 0-1 0-1-1l13 1h-6l5 2c-2 0-2 0-3 1 1 1 2 1 3 2h0c-2 1-3 1-4 1v-1c-1 1-3 1-3 2h-4l-4 1c-2 1-4 2-6 2-2-2-3-2-4-2h-1c-1-1-1-2-2-3 0-1-1-2-1-2v-2c3 1 5 1 8 1z" class="L"></path><path d="M714 906h-3c0-1-1-2-1-2h1 1c1 1 1 1 2 0-1-1-2-1-3-2h2v-1l5 2c-2 0-2 0-3 1 1 1 2 1 3 2h0c-2 1-3 1-4 1v-1z" class="M"></path><path d="M689 902c3 1 5 1 8 1 3 1 7 1 11 2h1v1h-3-1-1-2 0-6-3-3c0-1-1-2-1-2v-2z" class="B"></path><path d="M690 906h3 3 6 0 2 1l2 2-4 1c-2 1-4 2-6 2-2-2-3-2-4-2h-1c-1-1-1-2-2-3z" class="M"></path><path d="M322 280v-1c-2-1-2-2-3-3l1-1h1 0v-1c-1 0-3 0-4-1l1-1c0-1 0-2 1-3h1l-1-1c1-1 0-1 1-1l1-1h0 5 1c1 1 2 1 3 2 2 1 3 4 3 7 1 2 1 2 0 4-2 2-2 3-4 4l-2-1-1 2-2-1-1 1-1-1c0-1 1-2 0-3z" class="D"></path><path d="M322 280c1-1 1-2 2-3 1 0 2 1 3 1 1-1 1-1 1-2v-1l2-1 1-1h0 1v2h0v2 1c1-1 1-2 1-3 1 2 1 2 0 4-2 2-2 3-4 4l-2-1-1 2-2-1-1 1-1-1c0-1 1-2 0-3z" class="S"></path><defs><linearGradient id="A" x1="731.676" y1="959.921" x2="732.324" y2="949.579" xlink:href="#B"><stop offset="0" stop-color="#171313"></stop><stop offset="1" stop-color="#373938"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M765 949c2-1 3-1 5-1h1 3l1 1h-4l-5 2h-2-3l2 1h-1-1v1c-5 1-66 8-68 7s-7-1-10-1h7c3 0 5-1 8-1h1c0-1 1-1 2-1l9-1 20-3c2 1 4 0 6 0 6-1 13-1 18-3h1l1 1c3 0 6-1 9-2z"></path><defs><linearGradient id="C" x1="610.748" y1="594.329" x2="615.649" y2="581.932" xlink:href="#B"><stop offset="0" stop-color="#1b1b1e"></stop><stop offset="1" stop-color="#43403d"></stop></linearGradient></defs><path fill="url(#C)" d="M596 590l-1-1v-1-2h2l1-1 1 1c3 1 8 0 11 0 6 0 12 0 18 1 2 0 5 0 7 1 3 1 5 2 8 2l1 1-2 2h0-3l1 1c-4 0-8-2-12-2-5-1-10 0-16 0l-8 1c-2 0-4 0-5-1l2-2h-5z"></path><path d="M601 590h1c4 0 8-1 12-1h6c7 0 16-1 22 4h0-3l1 1c-4 0-8-2-12-2-5-1-10 0-16 0l-8 1c-2 0-4 0-5-1l2-2z" class="Z"></path><path d="M666 575l2-1c1 0 2 0 4-1h0c2-2 4-5 7-5-8 9-18 15-29 18-2 1-5 2-7 3-1 0-2 0-3-1-2-1-4-1-6-1-1-1-1-2-1-2 1-1 1-2 1-2h-1v-7h0l-1-1 3-3c3 1 6 2 7 4s1 8 1 10c4-2 10-6 14-7h3l1-1c2-1 4-2 5-3z" class="Y"></path><path d="M640 577v1l1 1c0 2 0 6-1 7h-1c0-2 1-5 0-7h0l1-2z" class="C"></path><path d="M633 576h2c1 1 2 1 3 2 0 2 1 5 0 7h-1-4c1-1 1-2 1-2h-1v-7h0z" class="D"></path><path d="M633 576h2c-1 1-1 2-1 4-1-2-1-3-1-4h0z" class="B"></path><path d="M621 570c3 1 6 1 10 1 1 1 3 1 4 1l-3 3 1 1h0v7h1s0 1-1 2c0 0 0 1 1 2-4-1-7-2-11-2h-17 1c2-1 2-1 4-1l1-1h3c-2-4-2-5-1-9h-4c1 0 2-1 3-1h0l1-1 2 1h4l1-3z" class="M"></path><path d="M632 575l1 1h0v7 1h-3l1-1v-2c0-2 0-4 1-6z" class="N"></path><g class="i"><path d="M623 575h3c1 2-1 6 1 8-2 1-3 0-4 0v-8z"></path><path d="M627 575c2 0 2 0 3 1 1 2 0 5 0 7h0l-1 1-2-1v-8z"></path></g><path d="M616 575h1l2-1h0c1 1 3 1 4 1v8h-11 3c-2-4-2-5-1-9h2v1z" class="F"></path><path d="M614 574h2v1 1 2h0c-1 2 0 3 0 5h-1c-2-4-2-5-1-9z" class="H"></path><path d="M709 742c1-3 2-6 5-7 3-2 6-2 9-1h1c2 1 5 3 6 6s1 6 0 10l-2 2h-1-1l-2-1-1-1v2l1 1-1 1c-1 0-2 0-4 1h0l-1-1c0-1 0-1 1-2h0-2c-1-1-3-3-3-4h-1c0-2-1-2-1-4h0l-3-2z" class="U"></path><path d="M715 740l1-3c2 0 3-1 4-1l2 2c0 2 0 2-2 3-2 0-3 0-5-1z" class="D"></path><path d="M722 738c2-1 2-1 3-1s3 2 4 3c0 0 0 3 1 4 0 3-1 5-3 8h-1l-2-1-1-1v2h0l-1-1-1 1-1-1h1c-2-1-3-3-5-4v-1l-2-2 1-1-1-1v-1l1-1c2 1 3 1 5 1 2-1 2-1 2-3h0z" class="B"></path><path d="M722 738c2-1 2-1 3-1s3 2 4 3c0 0 0 3 1 4 0 3-1 5-3 8h-1l-2-1h1c1-1 1-2 2-3v-1l-2-1c-1 0-2 0-3-1v-1h2c0-1-1-2-1-3h0v-1c0-1 0-1-1-2z" class="H"></path><path d="M377 612h19 13l2 2c-5-1-10-1-14 0l-1 2v-2c-2-1-4 0-6-1l-1 2h-9-1c-2 2-6 2-7 4h-1c-1 1-2 1-3 1l-1 1v7h-2v18l1-1c0 1-1 1 0 2l2 2 1 2v15h0-1-3l-1 1v-9c-2 0-7 0-8 2l-2 2c-2-1-4-3-6-4v-1c2-2 3-3 5-4h2l2 4h1c2 0 5 1 6 0 1-4 0-9 0-13v-24l2 1c0-1 0-1-1-2h0l2-2 1 1-1 1h1c1-1 2-1 4-1v-1-1h1v-1h-3c-2 1-4 2-6 2v-1l1-1-1-1h0 0c3 0 7-1 10-2v1c1 0 2 0 3-1z" class="U"></path><path d="M348 657c2-2 3-3 5-4l2 1v2c-1 1-1 1-3 1h-1s-1 0-2 1l-1-1z" class="J"></path><path d="M640 594l-1-1h3l-1 20c1 1 2 1 4 1 7 2 14 5 21 7 1 1 4 1 5 2s3 1 4 1l1-1c2-1 3-2 5-1 2 0 4 1 5 3v4c-1 1-3 3-5 4-1 0-3 0-4-1-2-1-2-2-3-4l-33-11v26c0 4 0 9-1 13h-2v-1h0l1-1v-10l1-50z" class="g"></path><path d="M670 624c2 0 4 0 5 1l1 1 1-2c1-1 2-2 3-2 2 0 3 1 4 2s1 2 1 4c-1 1-1 2-2 3-2 0-2 0-3-1h-1l1-1c1 0 2-1 4-2-1 0-3 0-4 1l-1 1h0c-3-3-8-3-11-4 1-1 1-1 2-1z" class="N"></path><path d="M645 614c7 2 14 5 21 7 1 1 4 1 5 2l-1 1c-1 0-1 0-2 1l-13-5c-4-1-12-2-14-5v-1h3 1z" class="K"></path><path d="M190 918c1-1 3-2 5-3l1 1 4 1h2c3 0 5 0 7 1v1h2v1 1c2 0 1 0 3-1 3 0 6 0 9-1h1v1l-11 2 2 2h0l-1 1-1 1-2 1c-1 1 0 1-1 2l-3-2h-1c-1-1-2-1-4-1-2 1-4 1-5 2h-3l-1 1h-1c-1-1-2 0-3 0h-3l-1 1h-1-1s-1-1-2-1c-2-2-2-3-3-5l2-2c3-2 6-3 10-4z" class="N"></path><path d="M190 918c1-1 3-2 5-3l1 1 4 1h2c3 0 5 0 7 1v1h2v1 1c2 0 1 0 3-1 3 0 6 0 9-1h1v1l-11 2c-8-1-15-5-23-4z" class="p"></path><path d="M202 917c3 0 5 0 7 1v1h-2c-3 0-5 0-7-2h2z" class="Z"></path><path d="M180 922c1 0 1 1 1 1 2 1 6 1 8 1 4 0 9 0 13 2-2 1-4 1-5 2h-3l-1 1h-1c-1-1-2 0-3 0h-3l-1 1h-1-1s-1-1-2-1c-2-2-2-3-3-5l2-2z" class="O"></path><defs><linearGradient id="D" x1="644.616" y1="958.185" x2="644.384" y2="965.315" xlink:href="#B"><stop offset="0" stop-color="#11130e"></stop><stop offset="1" stop-color="#322e30"></stop></linearGradient></defs><path fill="url(#D)" d="M635 957h1 1l4-1c1 1 1 2 2 3 1 0 1 0 2-1h1l1 2c1 0 1 0 3-1v1h3l2-2 1 1c0 1 0 0 1 1h0 3l9-1h14c3 0 8 0 10 1l-99 5h2v-1h-10l17-1v-1l1-1c1 0 4 1 5 1 3 1 7 0 11 0-1-1-1-1-1-2 1-1 1 0 2-1 1-2 3-1 4-2l1 1c1 0 2-1 4-1 0 1 1 1 1 2 2-1 3-1 4-2z"></path><path d="M635 957h1 1l4-1c1 1 1 2 2 3 1 0 1 0 2-1h1l1 2c1 0 1 0 3-1v1h3l2-2 1 1c0 1 0 0 1 1h0l-25 2c-2 0-3 0-5-1h0c-3 0-5-1-7 1-1-1-1-1-1-2 1-1 1 0 2-1 1-2 3-1 4-2l1 1c1 0 2-1 4-1 0 1 1 1 1 2 2-1 3-1 4-2z" class="O"></path><defs><linearGradient id="E" x1="186.364" y1="934.882" x2="196.74" y2="933.297" xlink:href="#B"><stop offset="0" stop-color="#1d1d1a"></stop><stop offset="1" stop-color="#3a3839"></stop></linearGradient></defs><path fill="url(#E)" d="M216 925c1 1 1 2 3 2v1l-3 1c0 1 0 2 1 4h5l2 2 2 1h1l-1 1h0c-3 0-9 0-12 2l2 1c-7 0-12-2-19-3l-1 1-12-5c1 0 2-1 3-1l-3-2h1l1-1h3c1 0 2-1 3 0h1l1-1h3c1-1 3-1 5-2 2 0 3 0 4 1h1l3 2c1-1 0-1 1-2l2-1h2l1-1z"></path><path d="M216 936c-1 0-3 0-4-1s-1-2-1-4c1 1 3 2 3 2h-1c1 1 3 1 3 2v1z" class="F"></path><path d="M211 931c1-1 3-2 5-2 0 1 0 2 1 4h-3s-2-1-3-2z" class="B"></path><path d="M213 926h2c-1 2-2 2-4 3l-1 1c-1 1-2 1-4 1 0-1 0-2 1-4l3 2c1-1 0-1 1-2l2-1z" class="M"></path><path d="M217 933h5l2 2-8 1v-1c0-1-2-1-3-2h1 3z" class="O"></path><path d="M202 926c2 0 3 0 4 1h1c-1 2-1 3-1 4v1l-1 1c1 0 3-1 3 1h-5c-1 0 0 0-1 1-2 0-5-1-7-2-3 0-6-1-8-1l-3-2h1l1-1h3c1 0 2-1 3 0h1l1-1h3c1-1 3-1 5-2z" class="C"></path><path d="M184 930h1l1-1h3l2 2 1 1h1c1-1 2-1 3-1v1l-1 1c-3 0-6-1-8-1l-3-2z" class="K"></path><path d="M206 932l-1-1h-3-5c1-1 2-1 3-1h1v-1c1-2 2-2 5-2h1c-1 2-1 3-1 4v1z" class="O"></path><path d="M318 880c4-10 13-19 21-26l1 1c-2 1-4 3-5 4l1 1 1-1c1-1 2-1 3-1 0 0 0 1 1 2l-3 3c1 1 1 1 1 2l-1 1c0 1-1 1-1 2 0 2 0 1-1 2-1 2-3 6-3 9 0 1 0 0-1 1 0 2-1 4-1 6l-1 1h1v1c-2 1-4 1-6 1h-1-2-3l-1-2h-1l1-2v-5z" class="F"></path><path d="M338 866l-1-1c-1 0-1 1-2 1l-2-1v-1c1 0 2 0 2-1l-1-1 1-1h2l1 2c1 1 1 1 1 2l-1 1z" class="I"></path><path d="M318 880c4-10 13-19 21-26l1 1c-2 1-4 3-5 4-5 5-11 12-13 18-1 4-2 7-4 10h-1l1-2v-5z" class="U"></path><defs><linearGradient id="F" x1="339.318" y1="959.022" x2="339.748" y2="954.837" xlink:href="#B"><stop offset="0" stop-color="#151110"></stop><stop offset="1" stop-color="#2e2c2a"></stop></linearGradient></defs><path fill="url(#F)" d="M324 950l2 1c2 0 3 1 5 2s3 1 6 1c8-1 18-2 26 1 1 1 3 1 4 2h1l4 1 9 1 4 1h6 1l-2 2c-3 0-6 0-9-1l-23-1-70-5c1-1 1 0 1-1l-1-1h3 3l20 1c1 0 2-1 3 0 1 0 0 0 1-1h6v-3z"></path><path d="M324 950l2 1c2 0 3 1 5 2s3 1 6 1v2c-7 0-16-1-23-2 1 0 2-1 3 0 1 0 0 0 1-1h6v-3z" class="H"></path><path d="M324 950l2 1c2 0 3 1 5 2-2 1-5 1-7 0v-3z" class="J"></path><path d="M337 954c8-1 18-2 26 1 1 1 3 1 4 2h1l-31-1v-2z" class="D"></path><path d="M354 944c4 1 8 2 12 4l6 2c1 1 1 1 1 3h2c1-1 3-1 4-1s3 0 4 1c1 0 2 1 4 1 0 0 1 1 2 1l-1 1c2 1 5 3 7 3v1h-4-6l-4-1-9-1-4-1h-1c-1-1-3-1-4-2-8-3-18-2-26-1-3 0-4 0-6-1s-3-2-5-2v-1h2c2 1 2 1 4 1l5-2 1-1 1 2c2-2 7-4 11-5 1 0 2 0 4-1z" class="X"></path><path d="M366 948l6 2c1 1 1 1 1 3-6-1-13-1-19-1 4-1 9-1 12-4z" class="E"></path><path d="M372 958l-3-1v-1-1c3-1 4 0 7 0 4 0 7-1 12 1 2 1 5 3 7 3v1h-4-6l-4-1-9-1z" class="K"></path><path d="M380 955c2 0 1 0 2 1v1l-1 1c-1-1-2-1-3-1v-1l2-1zm1 4c2-1 5-2 7-1v1l-3 1-4-1z" class="C"></path><path d="M354 944c4 1 8 2 12 4-3 3-8 3-12 4-5 0-12-1-17 0h-2 1l3-2c2-2 7-4 11-5 1 0 2 0 4-1z" class="N"></path><path d="M608 545c11-1 24-1 33 4h1c-1 3-5 4-7 6-3 1-5 3-7 5 0 2 1 7 0 9v-1c-1-3-1-5-1-8l-10-1-1 8v-1-1c-3-1-5 0-7 1l-1 1c0-1 0-1-1-1h-1v-1h-2l-1-2h-3c1-1 2-2 2-3v-1l2-2h2v2c1-1 2-1 3-2h6c1 1 0 1 1 1l1-2v-8l-1-1h-2l-1-1-5-1z" class="Y"></path><path d="M626 552l1 1h1 2 1l-2 2-1 1h0-3c-1 0-4 1-5 0-1 0-1-1-2-1h2v-2c1 1 3 1 5 1 0-1 1-1 1-2z" class="X"></path><path d="M606 565l1-1h0l-1-1-1-1h2c2-2 5-2 8-2 1 2 1 3 1 5-3-1-5 0-7 1l-1 1c0-1 0-1-1-1h-1v-1z" class="E"></path><path d="M608 545c11-1 24-1 33 4h1c-1 3-5 4-7 6-3 1-5 3-7 5h-1c2-5 7-7 11-9l-8-1c-1 1-1 1-1 2-1-2-2-2-4-3 1 1 1 2 1 3s-1 1-1 2c-2 0-4 0-5-1v2h-2v-1c-1 1-1 2-1 2v-8l-1-1h-2l-1-1-5-1z" class="U"></path><path d="M613 546c2 0 5-1 7 0l-2 1v2l-1-1-1-1h-2l-1-1z" class="g"></path><path d="M620 546h2c4 0 9 1 12 2h1 2c1 1 2 1 2 1l1 1h-3l-1-1c-2 0-5-1-7-1h-6 0c1 1 1 1 2 1 1 1 1 2 1 3s-1 1-1 2c-2 0-4 0-5-1v2h-2v-1c-1 1-1 2-1 2v-8l1 1v-2l2-1z" class="a"></path><path d="M280 899h0c4-1 8-1 12-1 7-1 13-1 20-1h0l-1 2c-3 1-7 0-11 1-2 1-5 1-7 1h0c-3 2-7 2-11 2-1 1-1 1-2 1v1h0c-2 0-3 1-4 1-4 0-7 1-10 3v1h-1l-1-1c-2 0-4-1-6-1l-1 1h0c-2 1-3 1-4 2l-2-1h-3l-2 1h0-1l-1-2c-1 0 0 0-1 1l-3-1c-2 1-5 0-7-1h-1l-10 1-1-1h0l59-9z" class="g"></path><path d="M266 906c-1 0-3 1-5 0-2 0-3 0-5-1v-1l8-1c2 2 6 2 9 2l-7 1z" class="M"></path><path d="M240 909l-6-2c4-1 9-1 13-1-1 1-1 1-1 3h1l1 1-2 1h0-1l-1-2c-1 0 0 0-1 1l-3-1z" class="I"></path><path d="M247 906l11 2-1 1h0c-2 1-3 1-4 2l-2-1h-3l-1-1h-1c0-2 0-2 1-3z" class="H"></path><path d="M280 899h0c4-1 8-1 12-1 7-1 13-1 20-1h0l-1 2c-3 1-7 0-11 1-2 1-5 1-7 1h0c-3 2-7 2-11 2-1 1-1 1-2 1v1h0c-2 0-3 1-4 1-4 0-7 1-10 3v-3l7-1c-3 0-7 0-9-2 1-1 2-1 4-1l9-1h1l2-2z" class="V"></path><path d="M268 902v1h3c2-1 4 0 6-1 4-1 9 0 12-2l1 1h0c-3 1-5 0-8 2-1 1-1 1-2 1v1h0c-2 0-3 1-4 1-4 0-7 1-10 3v-3l7-1c-3 0-7 0-9-2 1-1 2-1 4-1z" class="j"></path><path d="M222 909l10-1h1c2 1 5 2 7 1l3 1c1-1 0-1 1-1l1 2h1 0l2-1h3l2 1c1-1 2-1 4-2h0l1-1c2 0 4 1 6 1l1 1h1 0l-2 1h3v2c1 0 3 1 4 1h1 4v1c-1 0-4 0-5 1-1 0 0 0-1-1-3 0-6-1-8-1h-5-1-5v1h-2c-1 0-1 0-2 1-2 0-4 0-5 1v1h-3c-2 1-4 2-7 2h-8v-1h-1c-3 1-6 1-9 1-2 1-1 1-3 1v-1-1h-2v-1c-2-1-4-1-7-1v-1c2 0 4-1 6-1l-1-3c2 0 4-1 6-1l9-2z" class="a"></path><path d="M248 910h3l2 1h8c-3 1-6 0-9 2h0l-2-1c-1-1-2-1-3-1h-1l2-1z" class="Z"></path><path d="M258 908c2 0 4 1 6 1l1 1h1 0l-2 1h-3-8c1-1 2-1 4-2h0l1-1z" class="M"></path><path d="M222 909l10-1h1c2 1 5 2 7 1l3 1h-1-3c-1 1-1 1-2 0-3-1-9 2-12 2-2 0-1-1-2-1-1-1-3 0-4 0h-3c-1 1-2 1-3 1v-1l9-2z" class="O"></path><path d="M231 912l6-1c3 0 7 1 10 2l4 1v1h-2c-1 0-1 0-2 1-2 0-4 0-5 1-4 0-7 1-10 0-2 0-3-1-4-1 1-1 2-2 3-4z" class="D"></path><path d="M209 914l9-1c3-1 3-1 5 0 2-1 6 0 8-1-1 2-2 3-3 4 1 0 2 1 4 1 3 1 6 0 10 0v1h-3c-2 1-4 2-7 2h-8v-1h-1c-3 1-6 1-9 1-2 1-1 1-3 1v-1-1h-2v-1c-2-1-4-1-7-1v-1c2 0 4-1 6-1l1-1z" class="I"></path><path d="M209 914l9-1c3-1 3-1 5 0l-3 1c-1 0-2 0-3 1-2 1-4 1-5 1-1-1-2-1-3-2z" class="F"></path><path d="M209 918c3-1 7 0 10-1 1-1 3-1 4-1l1 1 3-1c1 1 2 1 4 1h1c3 1 6 0 10 0v1h-3c-2 1-4 2-7 2h-8v-1h-1c-3 1-6 1-9 1-2 1-1 1-3 1v-1-1h-2v-1z" class="L"></path><path d="M590 575c3-1 5-1 8-2h0c-2-1-2-2-3-3v-6h0c0-1 0-1 1-2l1 2c1 0 1 0 2-1v1h1v-1h3l1 2h2v1h1c1 0 1 0 1 1-2 0-2 1-4 2l1 1c4 1 11 1 16 0l-1 3h-4l-2-1-1 1h0c-1 0-2 1-3 1h4c-1 4-1 5 1 9h-3l-1 1c-2 0-2 0-4 1h-1c-2 0-5 1-7 1l-1-1-1 1h-2v2 1l1 1h0c0 1 0 2-1 3 0 0-1 1-1 2s1 0 0 2v-3l-2-2h0l-2-2c-2-1-5-1-7 0-2 0-3-1-5-1h5l1-1-2-1c-1-2-1-4 0-5l2-1v-1c0-1 0 0-2-1v-1c1 0 3-2 4-2 1-1 3-1 4-1z" class="V"></path><path d="M592 581h1v-4h1c0 1 0 2 1 3v3l1 1h1 1c-2 1-5 0-7 1l1-4z" class="d"></path><path d="M604 581c-1-2-1-5 0-7h3v3 7h-2c-1-1-1-2-1-3z" class="K"></path><path d="M597 584v-2c1-1 1-2 1-3v-1h1v2h1l1-4 1-1c2 2-1 6 1 8l1-2c0 1 0 2 1 3h-7-1z" class="O"></path><path d="M604 565h2v1h1c1 0 1 0 1 1-2 0-2 1-4 2l1 1c4 1 11 1 16 0l-1 3h-4l-2-1-1 1c-1-1-3-1-4-1h-3c-2-1-2-1-3-1s-2 0-2-1l-1-2 1-1 1 1c1-1 1-1 1-2-1 0-2 0-3-1h4z" class="G"></path><path d="M610 574h4c-1 4-1 5 1 9h-3l-1 1h-4v-7-3h2v1l1-1z" class="I"></path><path d="M607 584h4c-2 0-2 0-4 1h-1c-2 0-5 1-7 1l-1-1-1 1h-2v2 1l1 1h0c0 1 0 2-1 3 0 0-1 1-1 2s1 0 0 2v-3l-2-2h0l-2-2c-2-1-5-1-7 0-2 0-3-1-5-1h5l1-1-2-1 2-2 1 1v1h2v-1h1c1-1 2-1 3-1 2-1 5 0 7-1h7 2z" class="Q"></path><path d="M582 587c-1-2-1-4 0-5l2-1v-1c0-1 0 0-2-1v-1c1 0 3-2 4-2 1-1 3-1 4-1 2 1 4 0 6 0h2c-1 2-2 1-4 2h-1v4h-1l-1 4c-1 0-2 0-3 1h-1v1h-2v-1l-1-1-2 2z" class="e"></path><path d="M585 586v-2l1-1c0-1-1-1 0-3l1 1h0v-2l2-1c0-1 1-1 2-1l1 4-1 4c-1 0-2 0-3 1h-1v1h-2v-1z" class="V"></path><path d="M312 897c1 1 1 1 1 2 0 0-1 1-1 2v2 3l1 3-1-1v-1c-2 2-1 4-4 5l-1 1c2 0 3 0 5 1l-1 1h-5c-2 1-5 1-6 2v1l1 1-1 1c0 1-1 1-2 1l-1-1v-1h-3c-2 0-5-1-8-1-1 0-1 0-2-1h0c-2 0-5-1-7 0h-3l-6 1v-1c1 0 2-1 2-1h1c1-1 4-1 5-1v-1h-4-1c-1 0-3-1-4-1v-2h-3l2-1h0v-1c3-2 6-3 10-3 1 0 2-1 4-1h0v-1c1 0 1 0 2-1 4 0 8 0 11-2h0c2 0 5 0 7-1 4-1 8 0 11-1l1-2z" class="n"></path><path d="M277 909h1l1 1h5c-2 1-6 1-8 1h0l-3-1h0l4-1z" class="R"></path><path d="M266 910h0c2-1 4 0 7 0h0l3 1c-2 0-4 0-5 1 0 1 1 1 1 2h-1c-1 0-3-1-4-1v-2h-3l2-1z" class="j"></path><path d="M266 909c3-2 6-3 10-3 0 1 1 2 1 3h0l-4 1c-3 0-5-1-7 0h0 0v-1z" class="L"></path><path d="M286 911c2-2 4-1 6-2v1l-1 1 1 1-1 2v1h1-3c-2 1-3 1-4 1l-1 1c-2 0-5-1-7 0h-3l-6 1v-1c1 0 2-1 2-1h1c1-1 4-1 5-1v-1c1-1 1-1 2-1h0c0 1-1 1-1 2h1l1-1v-1c2 0 4-1 6 0v1l-2 1h1 5v-1h-3c1-2 1-2 0-3z" class="o"></path><path d="M283 905l2 2h0c1 0 1 0 2-1h0c1-1 3-1 5-1h6v2l-1 1c-2 0-3 1-5 1-2 1-4 0-6 2l-2-1h-5l-1-1h-1 0c0-1-1-2-1-3 1 0 2-1 4-1h3z" class="U"></path><path d="M283 905l2 2h0c1 0 1 0 2-1 2 1 6 1 8 2-3 0-6 0-9 1h-8-1 0c0-1-1-2-1-3 1 0 2-1 4-1h3z" class="M"></path><path d="M298 907c1 1 1 2 1 4h4 2l1 1v1h1c2 0 3 0 5 1l-1 1h-5c-2 1-5 1-6 2v1l1 1-1 1c0 1-1 1-2 1l-1-1v-1h-3c-2 0-5-1-8-1-1 0-1 0-2-1h0l1-1c1 0 2 0 4-1h3-1v-1l1-2-1-1 1-1v-1c2 0 3-1 5-1l1-1z" class="U"></path><path d="M297 915v1h-1l1 1h-3 0v1 1c-2 0-5-1-8-1-1 0-1 0-2-1h5c2 0 5-1 8-2z" class="a"></path><path d="M299 915h7c-2 1-5 1-6 2v1l1 1-1 1c0 1-1 1-2 1l-1-1v-1h-3v-1-1h0 3l-1-1h1v-1h2z" class="R"></path><path d="M303 911h2l1 1v1h1c2 0 3 0 5 1l-1 1h-5-7l-1-2h0c2 0 3 0 5-1v-1z" class="f"></path><path d="M298 907c1 1 1 2 1 4h4v1c-2 1-3 1-5 1h0-1c-2 1-3 2-5 2h-1v-1l1-2-1-1 1-1v-1c2 0 3-1 5-1l1-1z" class="j"></path><path d="M297 908l1 2-4 1c-1 0-1-1-2-1v-1c2 0 3-1 5-1z" class="d"></path><path d="M312 897c1 1 1 1 1 2 0 0-1 1-1 2v2 3l1 3-1-1v-1c-2 2-1 4-4 5l-1 1h-1v-1l-1-1h-2-4c0-2 0-3-1-4v-2h-6c-2 0-4 0-5 1h0c-1 1-1 1-2 1h0l-2-2h-3 0v-1c1 0 1 0 2-1 4 0 8 0 11-2h0c2 0 5 0 7-1 4-1 8 0 11-1l1-2z" class="a"></path><path d="M283 905c3-1 6-1 9-2 1 0 2 0 2 1h0c-1 0-2 1-2 1-2 0-4 0-5 1h0c-1 1-1 1-2 1h0l-2-2z" class="b"></path><path d="M292 903c3-1 6-2 8-1h0c3-1 8-1 10-1 0 1-1 1-1 1l-2 2h-2l-1-1c-1 0-1 1-3 2h-3-6s1-1 2-1h0c0-1-1-1-2-1z" class="l"></path><path d="M312 897c1 1 1 1 1 2 0 0-1 1-1 2v2 3l1 3-1-1v-1c-2 2-1 4-4 5l-1 1h-1v-1l-1-1h-2-4c0-2 0-3-1-4v-2h3c2-1 2-2 3-2l1 1h2 3l1-1v-2-2l1-2z" class="j"></path><path d="M298 905h3c2-1 2-2 3-2l1 1h2 3v4-2c-4-1-7 0-10 0v1l3 3-4 1c0-2 0-3-1-4v-2z" class="R"></path><path d="M312 897c1 1 1 1 1 2 0 0-1 1-1 2v2 3l1 3-1-1v-1c-2 2-1 4-4 5l-1 1h-1v-1l-1-1h-2-4l4-1 5-1c2 0 2 0 2-1v-4l1-1v-2-2l1-2z" class="P"></path><path d="M365 609c-1-3 0-8 0-11h0c2-11 6-21 14-28 3-3 6-4 8-7 1-1 3-2 3-3v-5-17c-1 0-1-1-1-2l1-1h1l1 2c0 1-1 2-1 4l1 18c0 2 2 3 3 5h-1l5 4c2 1 3 2 5 3l-2 1-2-3-2 1 3 2h0s1 1 1 2 1 1 1 2v1 2h-1c-2-2-7-1-10-1-1 1-1 4-1 5l1 1-2 2h0c1 2 1 4 0 5v1 4h-3v1l-2-1 1-2-1-1c-1 1-3 1-4 1h-5c-2 0-3 1-4 2v1l1-1 1 1h0c-1 1-1 2-2 2h3c0 1-3 2-4 3h3v1h-1l-1-1-1 1h-2l-1 1c-1 2 1 6-1 8v-1l-2-2z" class="j"></path><path d="M400 569c-2-1-3-2-5-3h-7-1-1 0l5-5 3 3 5 4c2 1 3 2 5 3l-2 1-2-3z" class="J"></path><path d="M380 584c0-2 0-3 1-4 1 0 1-1 2-1 1-1 1 0 2 0-1 0-1 0-2 2h0c1 2 0 5-1 6v1 2c1 1 2 0 3 1h1 1c1-1 2 0 3 0v1 4h-3v1l-2-1 1-2-1-1c-1 1-3 1-4 1l-1-2c-3 0-5 1-7 1h0c2-2 4-2 7-2v-7z" class="F"></path><path d="M390 592v4h-3v-1l3-3z" class="S"></path><path d="M385 579l5-1c1 2 0 4 0 5s1 0 1 0l1 1-2 2h0c1 2 1 4 0 5-1 0-2-1-3 0h-1-1c-1-1-2 0-3-1v-2-1c1-1 2-4 1-6h0c1-2 1-2 2-2z" class="D"></path><path d="M383 571c1-1 2-2 4-3h1c4-1 7 0 10 2l3 2h0s1 1 1 2 1 1 1 2v1 2h-1c-2-2-7-1-10-1-1 1-1 4-1 5 0 0-1 1-1 0s1-3 0-5l-5 1c-1 0-1-1-2 0-1 0-1 1-2 1-1 1-1 2-1 4l-3 1 3-3-1-1v-4c1-2 0-2 1-4 0-1 2-2 2-3l1 1z" class="T"></path><path d="M383 571c1-1 2-2 4-3h1c4-1 7 0 10 2l3 2h0v5c-1-1-2-1-3-1v-1c-1-1-1-1-1-2v-2l-1 1c0 1 0 2 1 3v1l-1 1c-2 0-3 0-4-1 0-1 0-2 1-3h0l1 2 1-2-1-1h-2l-1-2-1 2c-1 0-2 0-3-1l-1-1c0 1-1 2-2 2h-2l1-1z" class="K"></path><path d="M447 947c4-1 7-1 11 0l-1 3 7-1-4 3 1 1v1h1c-1 1-2 1-3 1l-2 2c-2 0-3 0-4 1h-3v1h-3c1 2 3 3 5 3l-4 1h-3l-32-2h-1-12v2l-10-1 2-2h-1 4v-1c-2 0-5-2-7-3l1-1h1l-1-1v-1h2l9-3c1 0 4-1 5-1 5 0 9-1 14-1l8 3c2 0 4-1 6-1 1-1 2-1 3-2l5-1h6z" class="G"></path><path d="M390 955l10 2c2 1 3 2 5 2 1 0 1 0 2-1h0c1 1 2 1 2 3h4-1-12v2l-10-1 2-2h-1 4v-1c-2 0-5-2-7-3l1-1h1z" class="M"></path><path d="M392 960l17 1h4-1-12v2l-10-1 2-2z" class="Q"></path><path d="M445 954h2c2 1 5 0 7 1h5l-2 2c-2 0-3 0-4 1h-3v1h-3c1 2 3 3 5 3l-4 1h-3l-1-1c-2-1-3-2-5-2-1 0-2-1-3-1l-1 1c-1-1-2-1-3-1s-1 1-2 1l-1-2c-1 0-1 0-1-1h1c2 0 5 0 6-1 3-1 7 0 10-2z" class="K"></path><path d="M447 947c4-1 7-1 11 0l-1 3 7-1-4 3 1 1v1h1c-1 1-2 1-3 1h-5c-2-1-5 0-7-1h-2l-4-1-1 1c-2 0-5-1-6 0l-9 3c-1 1-2 1-4 1s-3 1-5 1h-2l-1-1-1 1c-1 0-2-1-2-2h1c2 1 4 0 6 0h1l1-1h1c2 1 3 0 4-1 2-1 3-1 5-1l-1-1c0-1 0-1-1-2h0c2 0 4-1 6-1 1-1 2-1 3-2l5-1h6z" class="H"></path><path d="M436 948l5-1v1 2c1 1 0 1 1 1h2c-1 1-1 2-2 2l-1-2c-2 0-2 0-4-1l-1-2z" class="B"></path><path d="M457 950l7-1-4 3s-1 1-2 1c-2-1-4-1-7-2 2-1 4-1 6-1z" class="F"></path><path d="M447 947c4-1 7-1 11 0l-1 3c-2 0-4 0-6 1v1c-1 0-2-1-2-1-2-1-2-1-4-1 1-1 1-2 2-3z" class="D"></path><path d="M400 950c1 0 4-1 5-1 5 0 9-1 14-1l8 3h0c1 1 1 1 1 2l1 1c-2 0-3 0-5 1-1 1-2 2-4 1h-1l-1 1h-1c-2 0-4 1-6 0h-1-10l-10-2-1-1v-1h2l9-3z" class="N"></path><path d="M427 951c1 1 1 1 1 2l1 1c-2 0-3 0-5 1-1 1-2 2-4 1h-1l-1 1h-1c-2 0-4 1-6 0h-1-10l-10-2-1-1v-1h2c1 0 1 1 2 1 2 1 5 0 8 0 6 0 13 0 20-1 2 0 4 0 6-1v-1z" class="J"></path><path d="M631 757v1 13h1 10 6c0 1 1 1 1 1 1 2 0 5 0 7v6c4 0 10 0 14 1 0 0 1 0 1 1 1 1 0 3 0 4-1 2-4 2-6 2l-2 1v2 9l-1-1v-1h-8l-3 1h-1c-1 1-1 1-1 2h-1c0 1 0 3-1 4v2 3 1l1 1c0 2 0 2-2 2v-3-3c0-2 0-3-1-5 1-1 0-3 0-5-1 0-2 0-3 2l-1-1-1 3h-1v-6l-2-1v-16c-2-1-3-1-5-1 1-1 1-2 1-3l-1-1v-1h-3l-1-2h2l1-1c1-1 1-4 1-6h0l1 6h-1l1 1v-1c1-1 1-2 2-4v-1 3 2h2 1v-18z" class="d"></path><path d="M634 775h1c1 3 1 7 0 10h-1v-10z" class="H"></path><path d="M644 774l1-2h0c1 2 1 3 1 5l-1 8h0-1c0-3-1-7 0-9v-1-1z" class="N"></path><path d="M636 777l2-1c1 2 0 7 0 9h-1-1v-8z" class="B"></path><path d="M625 778h5v1 1c1 1 0 3 0 4-2-1-3-1-5-1 1-1 1-2 1-3l-1-1v-1zm18-1h0c0 3 1 6 0 8-2 0-3 1-4 0l1-9c1 0 2 1 3 1z" class="N"></path><path d="M631 757v1 13h1 10 6c-2 0-3 0-4 1v2 1c-1 1-1 1-1 2-1 0-2-1-3-1l-1-1-1 1-2 1v-4-1h0l-4-1v2c1 0 1 1 1 2-1 3 1 8-1 10h0c0-2 0-5-1-7v-1-1-1-18z" class="H"></path><path d="M642 789c-3 0-8 1-11-1l1-1c10 1 21-2 31 0 1 1 1 2 0 3l-21-1z" class="D"></path><path d="M663 786s1 0 1 1c1 1 0 3 0 4-1 2-4 2-6 2l-2 1v2 9l-1-1v-1-7h0v-3l-14-1c-2 0-3 0-4 1h-2-1l-1 1v-1-3l9-1 21 1c1-1 1-2 0-3v-1z" class="T"></path><path d="M641 792l14 1v3h0v7h-8l-3 1h-1c-1 1-1 1-1 2h-1c0 1 0 3-1 4v2 3 1l1 1c0 2 0 2-2 2v-3-3c0-2 0-3-1-5 1-1 0-3 0-5-1 0-2 0-3 2l-1-1v-11h1 2c1-1 2-1 4-1z" class="V"></path><path d="M647 803c-1-1-2-1-3-1l1-1c-1-1 0-1-1-2 1 0 3 0 4-1h-4l3-2c-1 0-2 0-3-1 3-1 7-1 10 0l1 1v7h-8z" class="H"></path><path d="M635 793h2c1-1 2-1 4-1-1 5-2 13-1 18v2 3 1l1 1c0 2 0 2-2 2v-3-3c0-2 0-3-1-5 1-1 0-3 0-5-1 0-2 0-3 2l-1-1v-11h1z" class="k"></path><path d="M635 793c1 0 2 0 3 1 1 2 0 7 0 9-1 0-2 0-3 2l-1-1v-11h1z" class="Y"></path><path d="M392 935h6 3v1l1 1c2-1 2-1 4 0h1c1 0 1 1 2 2l2-1h0c2-1 2-1 3-3 1 1 3 1 5 0l-1 1v1l-2 1c0 1 0 1 1 1h4v1c2 1 3 0 5 0 0 2 0 2-1 3-2-1-3 0-5 0v1c2 1 5 0 7 1h1l-1 4h-3l-3-1h-1-1c-5 0-9 1-14 1-1 0-4 1-5 1l-9 3h-2v1l1 1h-1c-1 0-2-1-2-1-2 0-3-1-4-1-1-1-3-1-4-1s-3 0-4 1h-2c0-2 0-2-1-3l-6-2c-4-2-8-3-12-4-2 1-3 1-4 1-4 1-9 3-11 5l-1-2 2-1h1l2-1v-2h3 0c1 1 2 1 3 1l1-1-1-1c-1 0-2 0-2-1h-1 5l1-2h3l2 1c0-1 1-1 1-1h7v-1h2l2-1h3 8v-1c2 0 5 0 7-1l5-1z" class="O"></path><path d="M412 945c2-1 7 0 10 0l-4 1h0 2 0l1 1h-1c-3-1-6 0-9 0l1-2z" class="E"></path><path d="M427 945h1l-1 4h-3c-1-2-1-2-3-2l-1-1h0-2 0l4-1h5z" class="B"></path><path d="M411 947h-1c-2 2-5 0-8 1h-1-1c2-1 2-2 3-1h1l2-2h3c2 1 2 1 3 0l-1 2z" class="C"></path><path d="M381 948h-3c-1-1-1-1-2-1h0v-1c2-1 8-1 11-1 2 2 5 1 7 1-4 1-9 2-13 2z" class="I"></path><path d="M380 938c2 0 4 0 6 1v1l-9 2c-4 1-5 0-9 0-1-1-2-1-3-2v-1h2l2-1h3 8zm14 8l1 1 1-1v1c-1 1-3 2-4 3 2 0 4 0 6-1l2 1-9 3h-2v1l1 1h-1c-1 0-2-1-2-1-2 0-3-1-4-1-1-1-3-1-4-1-1-1 0-1-1-2l3-2c4 0 9-1 13-2z" class="J"></path><path d="M392 950c2 0 4 0 6-1l2 1-9 3h-2v1l1 1h-1c-1 0-2-1-2-1-2 0-3-1-4-1l1-1c1 0 1-1 3-1s3-1 5-1z" class="L"></path><path d="M355 940l2 1c0-1 1-1 1-1h7c1 1 2 1 3 2 4 0 5 1 9 0h2v1h-1l-1 1h-6v1c1 1 2 0 3 1l-1 1 4 1-1 1c-1 0-3 0-4 1l-6-2c-4-2-8-3-12-4-2 1-3 1-4 1-4 1-9 3-11 5l-1-2 2-1h1l2-1v-2h3 0c1 1 2 1 3 1l1-1-1-1c-1 0-2 0-2-1h-1 5l1-2h3z" class="G"></path><path d="M354 944v-1c1 0 1 0 2 1 3 1 6 1 9 1h2v2l3 1c0-1 1-1 2-1h1l4 1-1 1c-1 0-3 0-4 1l-6-2c-4-2-8-3-12-4z" class="n"></path><path d="M392 935h6 3v1l1 1c2-1 2-1 4 0h1c1 0 1 1 2 2l2-1h0c2-1 2-1 3-3 1 1 3 1 5 0l-1 1v1l-2 1c0 1 0 1 1 1h4v1c2 1 3 0 5 0 0 2 0 2-1 3-2-1-3 0-5 0v1c-4-1-11 1-15-1h-1c0 1 0 1-1 1s-1 0-2-1h-1c-4 1-9 2-13 1-2 0-2-1-3-2 1-1 5-1 7-1h0c0-1-1-1-2-2h0v-2l-2-1 5-1z" class="B"></path><path d="M398 938l3-2 1 1c2-1 2-1 4 0h1c1 0 1 1 2 2h-2c-1 1-2 1-4 1v1h-5v1l-1-1v-1h0l1-1v-1z" class="C"></path><path d="M414 935c1 1 3 1 5 0l-1 1v1l-2 1c0 1 0 1 1 1h4v1c-3 1-6 1-9 1v1l-3-1-2-2h2l2-1h0c2-1 2-1 3-3z" class="Y"></path><path d="M411 938c1 0 2 0 2 1s0 1-1 2h0 0v1l-3-1-2-2h2l2-1z" class="S"></path><path d="M392 935h6 3v1l-3 2v1l-1 1c-1 0-2 1-3 1s-2-1-3 0c0-1-1-1-2-2h0v-2l-2-1 5-1z" class="D"></path><path d="M389 937c3 1 6 2 9 1v1l-1 1c-1 0-2 1-3 1s-2-1-3 0c0-1-1-1-2-2h0v-2z" class="L"></path><path d="M276 930l2 1-3 3h7v1c-2 0-4-1-5 0 1 1 3 1 4 2v1c-1 0-1 1-1 1l-1 1c0 1 1 1 2 1h0 2c0-1 0-1 1-1 2 0 2 0 3 1-2 1-5 1-7 2v1c3 2 6 2 9 2-1 0-1 1-2 2h-5v1h0c3 0 7 2 9 4h-3l1 1c0 1 0 0-1 1l-61-9 2-2-7-2-6-2-2-1c3-2 9-2 12-2 6-3 13 1 19 1 2 0 5-1 8-1l4-1c0-1 0 0-1-1 1-1 2-2 4-3 2 2 2 2 4 2h0c2-2 6-3 8-3l4-1z" class="N"></path><path d="M276 930l2 1-3 3h7v1c-2 0-4-1-5 0-3 1-6 0-9 0-4 0-7 0-11 1 0-1 0 0-1-1 1-1 2-2 4-3 2 2 2 2 4 2h0c2-2 6-3 8-3l4-1z" class="Z"></path><path d="M276 930l2 1-3 3h-11c2-2 6-3 8-3l4-1z" class="D"></path><path d="M222 942c4-1 9 0 13 1h6c2-1 5-1 7 0 1 0 9 1 9 2l-1 1 2 1v1h-1-3c-8 0-17-2-25-4l-7-2z" class="O"></path><path d="M235 943h6c2-1 5-1 7 0l-1 2c-4 0-8-1-12-2z" class="E"></path><path d="M279 940c0 1 1 1 2 1h0 2c0-1 0-1 1-1 2 0 2 0 3 1-2 1-5 1-7 2v1c3 2 6 2 9 2-1 0-1 1-2 2h-5v1h0c3 0 7 2 9 4h-3l1 1c0 1 0 0-1 1l-61-9 2-2c8 2 17 4 25 4h3 1v-1l-2-1 1-1 8 2 3-1h2c2-1 3-1 4-3l-8-2c4 0 9 1 12 0l1-1z" class="T"></path><path d="M257 945l8 2c1 0 5 0 5 1h1c2 1 4 1 6 0l1-1v1c0 2-1 2-2 3-3 0-6-1-10-1-4-1-8-1-12-2h3 1v-1l-2-1 1-1z" class="M"></path><path d="M279 940c0 1 1 1 2 1h0 2c0-1 0-1 1-1 2 0 2 0 3 1-2 1-5 1-7 2v1c3 2 6 2 9 2-1 0-1 1-2 2h-5v1h0c3 0 7 2 9 4h-3c-4-1-8-1-12-2 1-1 2-1 2-3v-1l-1 1c-2 1-4 1-6 0h-1c0-1-4-1-5-1l3-1h2c2-1 3-1 4-3l-8-2c4 0 9 1 12 0l1-1z" class="G"></path><path d="M274 943c1 1 2 1 2 2s-1 1-1 2h-3l-2 1c0-1-4-1-5-1l3-1h2c2-1 3-1 4-3z" class="J"></path><path d="M464 949h3l2 1h7l1 1c1 1 1 1 1 2s0 1-1 1c-2 0-4 1-6 1-3 0-6 1-9 2 2 1 5 0 8 0l2-1h3c4-1 9-1 14-1 3 0 6-1 10 0 7 2 14-1 21 0 3 1 6 2 9 2v3c2-1 5-1 7-1l1 1c3 0 7 0 10 1 1 1 2 1 3 2h0v1c-2 1-4 0-6 0h0c-4 1-10 0-13 1v1l-76-1c-1-1-1-1-2-1h-2l3-1h-6l4-1c-2 0-4-1-5-3h3v-1h3c1-1 2-1 4-1l2-2c1 0 2 0 3-1h-1v-1l-1-1 4-3z" class="Q"></path><path d="M450 959h2c2-1 9-1 11 0l1 1c1 1 3 2 4 2l1 1h-1-11c-2 0-4 0-5-1-2 0-4-1-5-3h3z" class="E"></path><path d="M472 956h3c0 1 0 1 2 2 1 1 3 1 4 1 2-1 3 0 4 0 2 1 4 1 6 1 0 1 1 1 2 1h-4v1c2 1 5-1 8 1l1-1h1c1 1 1 1 2 1l1 1c-6 0-12-1-17-1-3 0-5 1-7 0l-1-1-2-2c-1-1-1-2-2-2-1-1-1-1-1-2z" class="D"></path><path d="M537 960c3 0 7 0 10 1 1 1 2 1 3 2h0v1c-2 1-4 0-6 0h-26l6-2 13-2z" class="H"></path><path d="M464 949h3l2 1h7l1 1c1 1 1 1 1 2s0 1-1 1c-2 0-4 1-6 1-3 0-6 1-9 2 2 1 5 0 8 0l2-1c0 1 0 1 1 2 1 0 1 1 2 2l2 2h-6-1c-1 0-1-1-2-1v1c-1 0-3-1-4-2l-1-1c-2-1-9-1-11 0h-2v-1h3c1-1 2-1 4-1l2-2c1 0 2 0 3-1h-1v-1l-1-1 4-3z" class="O"></path><path d="M470 957l2-1c0 1 0 1 1 2 1 0 1 1 2 2h-6v-1l1-2zm-6-8h3l2 1c-2 2-6 2-8 3l-1-1 4-3z" class="C"></path><path d="M475 956c4-1 9-1 14-1 3 0 6-1 10 0 7 2 14-1 21 0 3 1 6 2 9 2v3c-2 0-5 1-8 1-7 1-14 0-22 0h-6c-1 0-2 0-2-1-2 0-4 0-6-1-1 0-2-1-4 0-1 0-3 0-4-1-2-1-2-1-2-2z" class="i"></path><path d="M719 901l10 1h1l8 1h6c3 1 6 1 8 1s5 0 7 1h-4 0l1 2c-2 0-2-1-3-1-2 0-6 2-7 3h-1l1 2-1 1 1 1 1 2-8 1h3v2 1h-2l-2 1c1 0 1 1 1 1v1c-2 0-5 0-7 1h0c1 1 1 1 1 3h-3-3c-1 0-2-1-3-1h-6l-12 1v-2c2 0 4 0 6-1h0c-2-1-3-1-4-1-1 1-1 1-1 2l-2-2h-3 0c-2-1-4-1-6-2v-1h0l-1-1h-3c-1 0-1 1-2 1-2 0-4 0-5-2l1-3 1-1h4l1-2h5c2 0 4-1 6-2l4-1h4c0-1 2-1 3-2v1c1 0 2 0 4-1h0c-1-1-2-1-3-2 1-1 1-1 3-1l-5-2h6z" class="B"></path><path d="M703 919l1-1 1 1v2h-2l-1-1 1-1z" class="D"></path><path d="M708 914c2 1 4 0 6 0 4 0 7-1 10-1h8 1c-2 1-3 0-4 1-3 0-5 1-7 1l-20 3-1-1c1-2 1-2 3-2 0 0 1 1 2 1s1-1 2-2z" class="Y"></path><path d="M735 907l8 1c1 0 1 0 2 1l1 2-1 1 1 1 1 2-8 1c-2-1-3-1-5-1s-3 0-5-1c1-1 2 0 4-1h-1l1-1h0c1-2 2-2 3-3v-1l-1-1z" class="E"></path><path d="M743 908c1 0 1 0 2 1l1 2-1 1c-1 0-2 1-4 1h0v-1c0-1 0-2 2-3v-1z" class="F"></path><path d="M733 912h8v1h0c2 0 3-1 4-1l1 1 1 2-8 1c-2-1-3-1-5-1s-3 0-5-1c1-1 2 0 4-1h-1l1-1h0z" class="p"></path><path d="M733 912h8v1l-1 1c-3 0-5-1-7-1h-1l1-1h0z" class="O"></path><path d="M707 908h4l-1 1-1 1h-4v3l-2 1h5c-1 1-1 2-2 2s-2-1-2-1c-2 0-2 0-3 2l1 1-6 1-1-1h-3c-1 0-1 1-2 1-2 0-4 0-5-2l1-3 1-1h4l1-2h5c2 0 4-1 6-2l4-1z" class="B"></path><path d="M688 915c2 0 2 0 4 1v1h-3l-1-2z" class="D"></path><path d="M691 913c2 0 4 0 6-1 2 1 4 2 6 2h5c-1 1-1 2-2 2s-2-1-2-1c-2 0-2 0-3 2l1 1-6 1-1-1c1 0 1-1 2-1h0c1-2 1-1 0-2l-1-1c-2 0-8-1-10 0 0 2 1 1 2 2l-3 1 1-3 1-1h4z" class="M"></path><path d="M707 908h4l-1 1-1 1h-4v3l-2 1c-2 0-4-1-6-2-2 1-4 1-6 1l1-2h5c2 0 4-1 6-2l4-1z" class="d"></path><path d="M697 912l8-2v3l-2 1c-2 0-4-1-6-2z" class="H"></path><path d="M721 907h14l1 1v1c-1 1-2 1-3 3h0l-1 1h-8c-3 0-6 1-10 1-2 0-4 1-6 0h-5l2-1v-3h4l1-1h1 2c3-1 6-2 8-2z" class="D"></path><path d="M710 909h1v1c1 1 1 2 3 2h2c1 0 1-1 2-1 0 0 3 1 4 1h2c2-2 5-1 8-1l1 1-1 1h-8c-3 0-6 1-10 1-2 0-4 1-6 0h-5l2-1v-3h4l1-1z" class="I"></path><path d="M710 909h1v1c1 1 1 2 3 2-2 0-3 0-5 1v-3l1-1z" class="H"></path><path d="M705 910h4v3h-4v-3z" class="B"></path><path d="M726 918c2 0 5 1 8 0 2 0 2 0 3-1h1l1 2h1l-2 1c1 0 1 1 1 1v1c-2 0-5 0-7 1h0c1 1 1 1 1 3h-3-3c-1 0-2-1-3-1h-6l-12 1v-2c2 0 4 0 6-1h0c-2-1-3-1-4-1-1 1-1 1-1 2l-2-2h-3c1 0 3-1 4-1 2 0 7-1 9 0h0c1 0 1 0 2-1h0c3-1 6-1 9-2z" class="G"></path><path d="M717 920h0c3-1 6-1 9-2v1h1c0 1 0 1-1 2-3-1-6-1-9-1z" class="E"></path><path d="M726 918c2 0 5 1 8 0 2 0 2 0 3-1h1l1 2h1l-2 1c-1-1-3-1-5 0 0 0-1 0-1 1-1 0-2-1-3-1-2 1-1 2-3 1 1-1 1-1 1-2h-1v-1z" class="H"></path><path d="M712 923h2 4c3 0 6 0 9 1l2-2s1 1 2 1h1c1 1 1 1 1 3h-3-3c-1 0-2-1-3-1h-6l-12 1v-2c2 0 4 0 6-1h0z" class="o"></path><path d="M719 901l10 1h1l8 1h6c3 1 6 1 8 1s5 0 7 1h-4 0l1 2c-2 0-2-1-3-1-2 0-6 2-7 3h-1c-1-1-1-1-2-1l-8-1h-14c-2 0-5 1-8 2h-2-1l1-1c0-1 2-1 3-2v1c1 0 2 0 4-1h0c-1-1-2-1-3-2 1-1 1-1 3-1l-5-2h6z" class="p"></path><path d="M744 903c3 1 6 1 8 1-1 1-4 2-5 2s-1-1-2-1h-1v-2z" class="Z"></path><path d="M738 903h6v2c-1 1-1 1-3 1h-3-2l2-3z" class="M"></path><path d="M714 906v1c1 0 2 0 4-1h0c-1-1-2-1-3-2 1-1 1-1 3-1h1c1 1 2 1 4 2h9-1c-2 1-7 1-9 0l-1 1h-2v1h2c-2 0-5 1-8 2h-2-1l1-1c0-1 2-1 3-2z" class="l"></path><path d="M719 901l10 1h1l8 1-2 3c-1 0-2 0-3-1h0-1-9c-2-1-3-1-4-2h-1l-5-2h6z" class="I"></path><path d="M719 901l10 1h1l1 1c-2 0-3 1-4 0h-3c-1 1-3 0-5 0h-1l-5-2h6z" class="J"></path><path d="M655 804l1 1v1c2 10 2 20 6 30 4 9 11 17 17 26 6 10 9 24 9 35l2 2h-1l-1 1 2 1-1 1v2s1 1 1 2c1 1 1 2 2 3h1c1 0 2 0 4 2h-5l-1 2h-4l-1 1-1 3-1 2c-2 0-4 1-5 2l-1 1h1v2c-2 1-3 1-5 2-2 0-5 1-6 1l-2 2-1 1v1h-1-3l-1-3h-3l-5 1h-4c-3 1-6 1-8 1h-2l-1 1-3-1h-1l1-2h-1l-1-1v-1c5-1 10 0 15-2 2 1 5 0 7 0v-3c-1 0-1 0-2-1v-1l1-2h0c1 0 2 0 2-1h1l1 1-1 1h6 1c1-1 2-1 3-2h5c3-1 6-1 9-1h1l2-2c-2-2-7-1-10-1v-1c1-1 1-1 1-2 1-1 1 0 2-1h1c-1-1-2-1-2-1v-1l3-1h0l-2-1v-1h1 6v-1l2-1 1-1h0c1-3 1-6 0-8v-4l-2 1c-2-1-5-1-7 0h-1v-1c-1-5-2-11-4-15-1-3-2-5-3-7 0-2-1-3-2-4 1 0 1-1 1-1 3 0 4 1 8-1l-9-12-5-8c-3-6-5-14-6-22l-1-1 1-1-1-4v-3-5z" class="R"></path><path d="M660 928h1 3c2-1 2-1 4-1l-2 2-1 1v1h-1-3l-1-3z" class="K"></path><path d="M654 924v-3c-1 0-1 0-2-1v-1c1 0 2 0 3 1h0c2 1 3 1 5 1v3h-6z" class="m"></path><path d="M688 909c-1-1-1-2-1-3 1-2 1-3 2-4v2s1 1 1 2c1 1 1 2 2 3h1c1 0 2 0 4 2h-5l-1 2h-4v-1l-1-1 2-2h0z" class="Z"></path><path d="M688 909l1-1v1l-1 1c1 1 2 1 4 1l-1 2h-4v-1l-1-1 2-2h0z" class="X"></path><path d="M681 908c1 1 2 1 3 1h1c-1 4-2 6-3 9l-5 2c-1-1-2-2-3-2-1 1-1 0-2 0 0-1 0 0-1-1v-1c3-1 6-1 9-1h1l2-2c-1-2-3-2-3-4l1-1z" class="S"></path><path d="M666 916h5v1c1 1 1 0 1 1 1 0 1 1 2 0 1 0 2 1 3 2-4 0-10 2-13 0h-1c-3 1-6 0-8 0h0c-1-1-2-1-3-1l1-2h0c1 0 2 0 2-1h1l1 1-1 1h6 1c1-1 2-1 3-2z" class="C"></path><path d="M686 900h0c0 3 0 6-1 9h-1c-1 0-2 0-3-1l-1 1c0 2 2 2 3 4-2-2-7-1-10-1v-1c1-1 1-1 1-2 1-1 1 0 2-1h1c-1-1-2-1-2-1v-1l3-1h0l-2-1v-1h1 6v-1l2-1 1-1z" class="E"></path><path d="M686 900h0c0 3 0 6-1 9h-1c-1 0-2 0-3-1 0 0-1-1-2-1h2c1 0 1 0 1-1v-1c-2 0-2-1-4 0l-2-1v-1h1 6v-1l2-1 1-1z" class="D"></path><path d="M660 924h6c-3 1-7 1-10 2h4c-1 1-2 2-3 2l-5 1h-4c-3 1-6 1-8 1h-2l-1 1-3-1h-1l1-2h-1l-1-1v-1c5-1 10 0 15-2 2 1 5 0 7 0h6z" class="O"></path><path d="M648 927c3-1 5-1 8-1h4c-1 1-2 2-3 2l-5 1 1-1c-2-1-3-1-5-1z" class="U"></path><path d="M641 928l7-1c2 0 3 0 5 1l-1 1h-4-5l-2-1z" class="g"></path><path d="M634 928h7l2 1h5c-3 1-6 1-8 1h-2l-1 1-3-1h-1l1-2z" class="U"></path><path d="M676 860c4 7 10 18 9 27l1 1-2 1c-2-1-5-1-7 0h-1v-1c-1-5-2-11-4-15-1-3-2-5-3-7 0-2-1-3-2-4 1 0 1-1 1-1 3 0 4 1 8-1z" class="B"></path><path d="M677 884c2 0 4 0 5 1h0c-1 0-3 0-4 1h0l-1-1v-1zm-1 4c2-1 6-1 9-1l1 1-2 1c-2-1-5-1-7 0h-1v-1z" class="i"></path><path d="M729 900c20 2 110 11 120 23 1 2 1 3 0 4-1 3-5 6-8 7-7 3-15 5-23 7l-40 9c-5 1-11 2-17 3v-1h1 1l-2-1h3 2l5-2h4c0-1 1-1 2-1l13-3c-3 1-4 0-6 0 2-1 5-1 7-1 1 0 2 0 4-1h-1v-1h3c1-1 1-1 1-3h2v-1c3 0 5 1 8 1-2-1-2-2-4-1h-1v-1l-6-3 3-2 3-1c6-2 12-2 17-4v-1c1 0 2 0 3-1h2c-1-2-1-2-2-3h-2l1-1v-1h1v-1h-1c-3-1-4-2-6-3v-1c-3-1-4-2-7-2l-4-1h-3 0c-1-1-2-1-3-1h-2v-1h-5l-6-2c-3 0-6-1-9-1l-18-2c-2-1-5-1-7-1s-5 0-8-1h-6l-8-1h-1v-2z" class="f"></path><path d="M800 938c3 0 5 1 8 1 2 0 3-1 5 0h1 3c-2 1-5 2-7 3h-4v-1c-2-1-5-1-6-2v-1z" class="j"></path><path d="M798 939h2c1 1 4 1 6 2v1c-5 1-11 3-16 3-3 1-4 0-6 0 2-1 5-1 7-1 1 0 2 0 4-1h-1v-1h3c1-1 1-1 1-3z" class="K"></path><path d="M797 910c5 0 10 2 15 3 8 1 17 2 25 6h0-5l1 1h-1-4l2 1c-1 1-3 2-5 1-1 0-2-1-3-1v-1h1v-1h-1c-3-1-4-2-6-3v-1c-3-1-4-2-7-2l-4-1h-3 0c-1-1-2-1-3-1h-2v-1z" class="L"></path><path d="M832 929l3 1v-2h0l-4-1h-1c-1-1-2-1-3-2 2-2 10-3 13-4 1 0 2 0 3 1h0c-1 1-2 1-3 1h0c-2 0-2 0-2 1h-2c-2 0-2 0-2 1h-2 1 0 3l2-1h3l1-1v1h2 1l-1 1c-2 1-4 0-4 1h4l1 1h-2l1 1v1c-1 3-3 3-6 4h-1-1v-1c-2 1-3 4-5 4-1 0-2 0-3 1-1 0-3 1-5 1 1-1 2-1 2-2 3-1 5-2 7-4v-1l-5-2h2 3z" class="j"></path><path d="M838 933c0-1 1-2 1-3 1-1 3-1 5-1-1 3-3 3-6 4z" class="a"></path><path d="M820 927c3 0 5-1 7 0h1l4 2h-3-2l5 2v1c-2 2-4 3-7 4 0 1-1 1-2 2l-5 1h-1-3-1c-2-1-3 0-5 0-2-1-2-2-4-1h-1v-1l-6-3 3-2 3-1c6-2 12-2 17-4z" class="C"></path><path d="M803 937h2v-2l1-1v2c2 0 5 0 6 1l1 1 2 1 2-1 1 1h-1-3-1c-2-1-3 0-5 0-2-1-2-2-4-1h-1v-1z" class="F"></path><path d="M821 932c2-1 3-3 6-3l5 2v1c-2 2-4 3-7 4 0 1-1 1-2 2l-5 1-1-1-2 1-1-3 1-1c2 1 1 1 3 2 1 0 3 0 4-1v-2h0l-1-2z" class="K"></path><path d="M820 927c3 0 5-1 7 0h1l4 2h-3-2c-3 0-4 2-6 3-5 1-11 2-16 2-1-1-1-2-2-3 6-2 12-2 17-4z" class="B"></path><path d="M759 905l18 2c3 0 6 1 9 1l6 2h5v1h2c1 0 2 0 3 1h0 3l4 1c3 0 4 1 7 2v1c2 1 3 2 6 3h1v1h-1v1l-1 1h2c1 1 1 1 2 3h-2c-1 1-2 1-3 1v1c-5 2-11 2-17 4l-3 1-1-1h1c0-1 1-1 2-2 2-1 2 0 4 0v-1c-1-1-1-1-2-1h-4l-10-1-1-1h4c1-1 2-1 3-2-2 0-5-1-7-1v-1l-6 3-1-1h0 0l-2-1h-1l-1-1c-1-1-2-1-3-2h-1c-5-1-9-1-14 0l-16 3c-2-1-3-1-5-1 0 0 0-1-1-1l2-1h2v-1-2h-3l8-1-1-2-1-1 1-1-1-2h1c1-1 5-3 7-3 1 0 1 1 3 1l-1-2h0 4z" class="l"></path><path d="M790 913c3 0 6 0 8 1v2h-1-1c-1 0-2-1-3-1-2-1-2 0-3 0v-1h3l-2-1h-1z" class="E"></path><path d="M790 915c1 0 1-1 3 0 1 0 2 1 3 1h-3c0 1 0 1 1 1l-2 2c-1 0-2-1-3-2l1-1v-1z" class="F"></path><path d="M811 915c1 0 3 0 5 1h0c2 1 3 2 6 3h1v1h-1c-1 0-3-1-4-1-2-1-5-1-7-2v-2z" class="I"></path><path d="M811 917h-2l-9-1h0 1c4-2 6-2 10-1v2z" class="E"></path><path d="M777 919c4-1 7-1 11 0h1c-2 2-5 2-7 4l-2-1h-1l-1-1c-1-1-2-1-3-2h2z" class="K"></path><path d="M775 919h2 1c1 1 2 1 3 1 0 1 0 2-1 2h-1l-1-1c-1-1-2-1-3-2zm-7-3c4-1 6 0 9-1 1 0 1-1 2-1 2 0 4 0 6 2h5l-1 1c-4 0-8 1-12 0h-1c-3 0-5 0-8-1z" class="C"></path><path d="M766 915c3 0 6-2 9-2 5-1 11 0 15 0h1l2 1h-3v1 1h-5c-2-2-4-2-6-2-1 0-1 1-2 1-3 1-5 0-9 1h-3l1-1z" class="J"></path><path d="M755 917h2v-1c2 0 3-1 4-1l1-1c2 0 2 0 4 1l-1 1h-3c-1 1-1 1-2 3l-16 3c-2-1-3-1-5-1 0 0 0-1-1-1l2-1h2v-1-2h-3l8-1c1 0 2 1 3 0l1 1v1h3 1z" class="X"></path><path d="M747 915c1 0 2 1 3 0l1 1v1h3 1c-2 1-4 1-6 2-1 0-2 0-3 1h-1c-1-1-2-1-3-1v-1-2h-3l8-1z" class="L"></path><path d="M742 916h5v1c-2 1-3 1-5 1v-2z" class="E"></path><path d="M759 905l18 2c3 0 6 1 9 1l6 2c-1 1-2 1-4 1-4 0-8 1-12 1-2-1-1-1-2 0h-4l-1-1-6 1-7 1c-1 2-2 2-2 4h-3v-1l-1-1c-1 1-2 0-3 0l-1-2-1-1 1-1-1-2h1c1-1 5-3 7-3 1 0 1 1 3 1l-1-2h0 4z" class="G"></path><path d="M756 909l1 1h1c1 1 2 1 2 1-2 0-4 0-6 1l-3-1c1 0 2-1 4-1h0l1-1z" class="K"></path><path d="M751 911l3 1v1c-2 1-4 1-6 1l1-2 2-1zm5-2c3-2 6-1 9-1l1 1c-3 1-6 1-9 1l-1-1z" class="H"></path><path d="M760 911h3v1l-7 1h-2v-1c2-1 4-1 6-1z" class="C"></path><path d="M754 913h2c-1 2-2 2-2 4h-3v-1l-1-1c-1 1-2 0-3 0l-1-2 2 1c2 0 4 0 6-1z" class="b"></path><path d="M777 907c3 0 6 1 9 1-1 1-2 1-3 1l-4 1h-6c-1-1-1-1-2-1s-1 0-2-1h1 3 1c1 0 2 0 3-1h0z" class="Z"></path><path d="M789 921h1c5-2 11-3 17-3 1 1 4 1 6 1 3 1 5 1 8 3h2c1 1 1 1 2 3h-2c-1 1-2 1-3 1v1c-5 2-11 2-17 4l-3 1-1-1h1c0-1 1-1 2-2 2-1 2 0 4 0v-1c-1-1-1-1-2-1h-4l-10-1-1-1h4c1-1 2-1 3-2-2 0-5-1-7-1v-1z" class="J"></path><path d="M807 918c1 1 4 1 6 1 3 1 5 1 8 3h2c-1 2-4 2-6 2l-1-1c-1 0-3-1-4-2h-2-1c-2 0-7 1-8 1 2-2 4-1 6-2v-2z" class="C"></path><path d="M823 922c1 1 1 1 2 3h-2c-1 1-2 1-3 1v1c-5 2-11 2-17 4l-3 1-1-1h1c0-1 1-1 2-2 2-1 2 0 4 0v-1c-1-1-1-1-2-1h-4l-10-1-1-1h4c5 0 10 1 14 1 3-1 7-1 10-2 2 0 5 0 6-2z" class="b"></path><path d="M593 944h3l2 2v1c2 1 4 1 6 1s2 0 4-1c0 0 1-1 2-1h1s1 1 2 1v1l1 1 1 1h0v1h1 2c0 1 0 2 1 4 1 0 2-1 3 0 2 0 3 0 5 1l8 1c-1 1-2 1-4 2 0-1-1-1-1-2-2 0-3 1-4 1l-1-1c-1 1-3 0-4 2-1 1-1 0-2 1 0 1 0 1 1 2-4 0-8 1-11 0-1 0-4-1-5-1l-1 1v1l-17 1-14-1-14 1c-3 0-4 0-6-1-1-1-1-1-2-1v1h0c-1-1-2-1-3-2-3-1-7-1-10-1l-1-1c-2 0-5 0-7 1v-3h2l-2-2c3-2 9-2 13-2l-1-2h7c2 1 4 1 6 1s4 0 6-1h2v1c3 1 7 1 10 3l1-1v-3c3 0 6 0 9 1 1 0 1 0 2-1h0l2 1v-2-1h0 6c-1 0-3 0-4-1 1-1 2-1 3-2l1 1c1 0 1-1 1-1v-2z" class="F"></path><path d="M592 949c-1 0-3 0-4-1 1-1 2-1 3-2l1 1c2 1 4 1 6 1v1h-6z" class="E"></path><path d="M586 949c2 1 3 1 5 1 1 1 3 1 4 2h-6v1c-1 0-2 1-3 1-1-1-2-2-2-3h0l2 1v-2-1z" class="L"></path><path d="M573 951c3 0 6 0 9 1 1 0 1 0 2-1 0 1 1 2 2 3h-4c1 0 1 0 2-1-1-1-2 0-4 0-1 0-3 0-4 1v1l-1-2-2 1v-3z" class="C"></path><path d="M595 952l4 1h2v1c-3 1-5 1-7 2h-3l-1-1c1 0 3 0 4-1 1 0 1 0 1-1h-6v-1h6z" class="B"></path><path d="M604 948c2 0 2 0 4-1v2l1 1c-3 1-5 1-6 3l-1-1v-1h-4l-1-1c1 0 3 0 4-1 1 0 2-1 3-1z" class="I"></path><path d="M589 953h6c0 1 0 1-1 1-1 1-3 1-4 1-2 0-4 1-5 1-2-1-5 0-7-1l1-1h3 4c1 0 2-1 3-1z" class="D"></path><path d="M608 956c1 0 2 0 3 1h2c1 1 2 2 4 2h0 1v1c-1 0-5-1-5 0-2 0-2 1-3 0l-1 1c-2 0-5-1-7 0h0v-1h0 2c0-1 1-1 1-2h1l2-2z" class="H"></path><path d="M608 947s1-1 2-1h1s1 1 2 1v1l1 1 1 1h0v1h1 2c0 1 0 2 1 4l-10-2h-2c-1 1-3 0-4 0 1-2 3-2 6-3l-1-1v-2z" class="J"></path><path d="M608 947s1-1 2-1h1s1 1 2 1v1l1 1 1 1h0v1c-2 0-4 0-6-1l-1-1v-2z" class="F"></path><path d="M608 947s1-1 2-1h1v3l-2-1-1 1v-2z" class="K"></path><path d="M602 961c-2 1-5 0-8 0h-5c-1-1-3-1-3-2 5-1 11-2 16-4 2-1 4 0 6 1l-2 2h-1c0 1-1 1-1 2h-2 0v1h0z" class="B"></path><path d="M541 951h7c2 1 4 1 6 1s4 0 6-1h2v1c3 1 7 1 10 3h0l-2 1v1c2-1 3-1 4-1 3 1 6 3 9 4 0 0 1 0 2 1h1v1c-2 1-2 0-4-1l-1 2h-9l-14 1c-3 0-4 0-6-1-1-1-1-1-2-1v1h0c-1-1-2-1-3-2-3-1-7-1-10-1l-1-1c-2 0-5 0-7 1v-3h2l-2-2c3-2 9-2 13-2l-1-2z" class="D"></path><path d="M531 957h3c2-1 5 0 7 1l-5 1c-2 0-5 0-7 1v-3h2z" class="C"></path><path d="M541 951h7c2 1 4 1 6 1s4 0 6-1h2v1c3 1 7 1 10 3h0l-2 1c-4-2-9-2-14-3-5 0-10-1-14 0l-1-2z" class="p"></path><path d="M570 957c2-1 3-1 4-1 3 1 6 3 9 4 0 0 1 0 2 1h1v1c-2 1-2 0-4-1-1-1-5-3-7-4-1 0-3 1-4 1l-14 3c-2 0-3 0-5 2-1-1-1-1-2-1v1h0c-1-1-2-1-3-2-3-1-7-1-10-1l-1-1 5-1c7 0 14 2 20 1h1l8-2z" class="d"></path><path d="M552 963c2-2 3-2 5-2l14-3c1 0 3-1 4-1 2 1 6 3 7 4l-1 2h-9l-14 1c-3 0-4 0-6-1z" class="D"></path><path d="M400 569l2 3 2-1c2 2 7 6 7 10v1l1 1c2 3 3 6 4 10v1c0 1 1 3 1 4-1 2 0 5-1 7h-1l-8-1v3h1 1l1 1h0v2l-1 1h-1-3l-1-1-2 1v-2c-1 0-2 1-3 1-2 1-4 0-5 0-4 0-8 2-11 1h-6v1h0c-1 1-2 1-3 1v-1c-3 1-7 2-10 2l3-3v1c2-2 0-6 1-8l1-1h2l1-1 1 1h1v-1h-3c1-1 4-2 4-3h-3c1 0 1-1 2-2h0l-1-1-1 1v-1c1-1 2-2 4-2h5c1 0 3 0 4-1l1 1-1 2 2 1v-1h3v-4-1c1-1 1-3 0-5h0l2-2-1-1c0-1 0-4 1-5 3 0 8-1 10 1h1v-2-1c0-1-1-1-1-2s-1-2-1-2h0l-3-2 2-1z" class="X"></path><path d="M381 594c1 0 3 0 4-1l1 1-1 2 2 1v-1h3v2c-2 1-7 1-9 0l-1-1 3-1h0c-2 0-2 0-3-1h-1-3v-1h5z" class="C"></path><path d="M406 600l-1-8 11 2c0 1 1 3 1 4-1 2 0 5-1 7h-1l-8-1v-2l-1-2z" class="W"></path><path d="M373 603l-2 2h3c0 2-4 1-1 4 2-1 3-2 4-5 1 1 0 2 0 3h1c1-1 1-2 1-3s-1-1-1-2l-1 1h-1c0-1 0-1 2-2l1 1 1-1v1 2 1 2h1l1-1 1 1h1l1 1s0 1 1 1l-1 1c-1 1-2 0-2 1h-6v1h0c-1 1-2 1-3 1v-1c-3 1-7 2-10 2l3-3v1c2-2 0-6 1-8l1-1h2l1-1 1 1z" class="R"></path><path d="M404 571c2 2 7 6 7 10v1l1 1c2 3 3 6 4 10l-11-2c0-4-2-10-2-14 2 0 3 2 4 2l1-2c-1 0-1-1-1-1-2-2-3-3-5-4l2-1z" class="W"></path><path d="M391 601c-3 0-9 1-11 0v-1c3-1 9-1 11 0h0c2-1 0-2 1-3v-1c-1-1-1-3 0-4 3 0 5-1 8 0 2 0 3 0 4 2l1 4c-1 1 0 1-2 1h-1 2c1 1 2 1 2 1l1 2-5-1v1 3h-1v-1-3h-10z" class="E"></path><path d="M400 592c2 0 3 0 4 2l1 4c-1 1 0 1-2 1l-1-2c-2-1-5-1-8-1v-1l4-1s1 0 1 1c1 0 3 0 4-1-3-2-8 1-11-1 1-1 6-1 8-1zm-9-9c0-1 0-4 1-5 3 0 8-1 10 1l2 12h-12v-7l-1-1z" class="W"></path><path d="M391 601h10v3 1h1v-3-1l5 1v2 3h1 1l1 1h0v2l-1 1h-1-3l-1-1-2 1v-2c-1 0-2 1-3 1-2 1-4 0-5 0-4 0-8 2-11 1 0-1 1 0 2-1l1-1c-1 0-1-1-1-1l-1-1 3-1v1c1 0 1-1 2-1 0 1 0 1 1 2h0l1-2v-5z" class="a"></path><path d="M760 919c5-1 9-1 14 0h1c1 1 2 1 3 2l1 1h1l2 1h0 0l1 1 6-3v1c2 0 5 1 7 1-1 1-2 1-3 2h-4l1 1 10 1h4c1 0 1 0 2 1v1c-2 0-2-1-4 0-1 1-2 1-2 2h-1l1 1-3 2 6 3v1h1c2-1 2 0 4 1-3 0-5-1-8-1v1h-2c0 2 0 2-1 3h-3v1h1c-2 1-3 1-4 1-2 0-5 0-7 1h-6v-1c1-2 3 0 4-2-3-1-7 1-9 0l1-1v-1l-3-2h-4l1-2c-1 0-2-1-4 0h0c-2-1-3-2-5-1 0 0-1 0-2-1h0c-1 1-2 1-3 1h-4v1h-2-2c-2 0-3 0-4 1-2-1-5-1-8-1v-1h-2c-1-1-1-1-2-1h-1v-1l4-2v1c1-1 2-1 4-1v-1l-1-1h-7l1-1 1-1-1-1h3c0-2 0-2-1-3h0c2-1 5-1 7-1v-1c2 0 3 0 5 1l16-3z" class="F"></path><path d="M774 919h1c1 1 2 1 3 2l-2 1-2-1v-2z" class="E"></path><path d="M743 931h5c-1-1-1-1-2-1 2-1 2-1 3-1l1 1c1-1 3-2 4-3v-1c2 0 5 0 7 1l-1 1h-2v1h2v2h1l-1 1c3 1 5 1 7 1h2l5 1h-8-3c-3-1-4-1-7 0 1-1 2-2 3-2l-1-1c-2 0-4-1-6 0-3 1-6 1-9 0h0z" class="C"></path><path d="M739 921c2 0 3 0 5 1l-7 2c5 1 9 1 14 1h2c0 1 0 1-1 2h-10-2c-2 0-3 0-5-1h-1-1c0-2 0-2-1-3h0c2-1 5-1 7-1v-1z" class="b"></path><path d="M732 923c2 0 3 0 4 1 0 1-1 2-2 2h-1c0-2 0-2-1-3h0z" class="d"></path><path d="M737 930c2 0 4 1 6 1h0c3 1 6 1 9 0 2-1 4 0 6 0l1 1c-1 0-2 1-3 2h-5-1c-2 0-4 1-6 1-2-1-4 0-6-1h-4v1h-2c-1-1-1-1-2-1h-1v-1l4-2v1c1-1 2-1 4-1v-1z" class="B"></path><path d="M737 931h1v1c-1 1-1 1-2 1h-3v-1c1-1 2-1 4-1zm23-12c5-1 9-1 14 0v2c-1 1-3 2-5 2l-4-1-1 1 1 2c-2 0-3 0-4-2h0c-3 1-6 2-10 2-5 0-9 0-14-1l7-2 16-3zm22 4h0 0l1 1 6-3v1c2 0 5 1 7 1-1 1-2 1-3 2h-4l1 1 10 1h0l1 1h-1c-1 0-2 1-3 1l-4 2c-1 0-1 0-2 1h-1c-2 1-9 2-12 2h-4l-5-1h-2c-2 0-4 0-7-1l1-1 4-2c3-1 5-2 7-2v-1c2-2 2-1 4-2h1 1l1-2h1l2 1z" class="D"></path><path d="M790 926l10 1h0l1 1h-1c-1 0-2 1-3 1l-4 2c-1 0-1 0-2 1h-1c0-1-1-1-1-2h-1l1-1h3v1c1 0 1-1 2-1-2 0-3-1-4-2-2-1-11 0-13 0 4-1 8-1 13-1z" class="B"></path><path d="M782 923h0 0l1 1 6-3v1c2 0 5 1 7 1-1 1-2 1-3 2h-4l1 1c-5 0-9 0-13 1h-5v-1c2-2 2-1 4-2h1 1l1-2h1l2 1z" class="L"></path><path d="M800 927h4c1 0 1 0 2 1v1c-2 0-2-1-4 0-1 1-2 1-2 2h-1l1 1-3 2 6 3v1h1c2-1 2 0 4 1-3 0-5-1-8-1v1h-2c0 2 0 2-1 3h-3v1h1c-2 1-3 1-4 1-2 0-5 0-7 1h-6v-1c1-2 3 0 4-2-3-1-7 1-9 0l1-1v-1l-3-2h-4l1-2c-1 0-2-1-4 0h0c-2-1-3-2-5-1 0 0-1 0-2-1h0c-1 1-2 1-3 1h-4v1h-2-2c-2 0-3 0-4 1-2-1-5-1-8-1v-1-1h4c2 1 4 0 6 1 2 0 4-1 6-1h1 5c3-1 4-1 7 0h3 8 4c3 0 10-1 12-2h1c1-1 1-1 2-1l4-2c1 0 2-1 3-1h1l-1-1h0z" class="K"></path><path d="M771 936c2-1 3-2 5-1 2 0 5-1 6 0v1h-1c-1 0-1 0-2 1h-3l-1-1h-4z" class="G"></path><path d="M771 936h4l1 1h3 6v2l-14-1h-4l1-2h3z" class="b"></path><path d="M799 931l1 1-3 2 6 3v1h1c2-1 2 0 4 1-3 0-5-1-8-1v1h-2-13v-2c5-1 9-4 14-6z" class="g"></path><path d="M798 938l-5-1v-1l2-2 3 4z" class="M"></path><path d="M795 934h2l6 3v1h1c2-1 2 0 4 1-3 0-5-1-8-1h-2l-3-4z" class="L"></path><defs><linearGradient id="G" x1="786.214" y1="943.533" x2="784.217" y2="936.213" xlink:href="#B"><stop offset="0" stop-color="#c9c8c8"></stop><stop offset="1" stop-color="#edecec"></stop></linearGradient></defs><path fill="url(#G)" d="M771 938l14 1h13c0 2 0 2-1 3h-3v1h1c-2 1-3 1-4 1-2 0-5 0-7 1h-6v-1c1-2 3 0 4-2-3-1-7 1-9 0l1-1v-1l-3-2z"></path><path d="M789 941h1 1c1 1 0 1 1 1l-2 1h-2 0-1l2-2zm-15 0h1c3 0 7-1 9 0l-2 1c-3-1-7 1-9 0l1-1z" class="C"></path><path d="M644 804l3-1h8v1 5 3l1 4-1 1 1 1c1 8 3 16 6 22l5 8 9 12c-4 2-5 1-8 1 0 0 0 1-1 1 1 1 2 2 2 4h-1c-4-8-10-13-17-18-2-1-3-1-5-3h-2c-1-1-2-1-4-2-1-1-1-1-1-3h-1l-1-1c-1-1-2-1-3-1v-1c-1 0-1 0-2 1h-3l-1-1c-2-1-4-2-5-3h-1c-1 0-2-1-3-2v-2c-3-1-5-1-7-2h-1l-1-1h0c1 0 2 0 2-1h-2-1l-1-1c3-1 6-1 8-1h2 14c2 0 5-1 7 0l1-2h-1v-3c2 0 2 0 2-2l-1-1v-1-3-2c1-1 1-3 1-4h1c0-1 0-1 1-2h1z" class="F"></path><path d="M662 840l5 8c-3-1-6-1-9-1h0c-1-1-2-1-4-1h0c2-1 5-1 7-2h0c-2 0-7 1-9 0v-1c2 0 4 0 6-1 1 0 2 0 2-1h1l1-1zm-14-28h7l1 4-1 1 1 1c-2 1-4 1-6 2-1 1-2 1-3 1 0-1-1-2-1-2h0c0-1 1-1 1-2h1-2c0-2 1-1 3-2-1 0-2 0-3-1l-1-1 3-1z" class="B"></path><path d="M649 815c0 1 0 1 1 1 2-1 2-1 4 0h2l-1 1h-7-2c0-2 1-1 3-2z" class="W"></path><path d="M648 812h7l1 4h-2c-2-1-2-1-4 0-1 0-1 0-1-1-1 0-2 0-3-1l-1-1 3-1z" class="i"></path><path d="M658 847c3 0 6 0 9 1l9 12c-4 2-5 1-8 1 0 0 0 1-1 1l-6-8c2 0 3-1 5-1 1 0 2 0 2-1v-1c-2 1-6 2-8 1 1 0 2-1 2-1-1-1-1-1-2-1v-2c-1 1-2 1-3 1l-1-1c1-1 1-1 2-1z" class="D"></path><path d="M647 821c1 0 2 0 3-1 2-1 4-1 6-2 1 8 3 16 6 22l-1 1h-3c-2 1-4 1-6 1v-1l-2-1v-1h1c3-1 6-1 8-1h0c-3 0-8 1-10 0l2-2h-3v-1c2-2 5-2 8-3h-8v-1l1-1s1-1 3-1h2l-8-1 1-1c2 0 5-1 7-1h-1-6l-1-1c1 0 2-1 2-2h1c-1-1-2-1-2-1h-1l-1-1h2z" class="N"></path><path d="M644 804l3-1h8v1 5 3h-7l-3 1h-1v1c0 1 0 0 1 1v1h-2l1 1h1v1h-2c0 2 2 1 1 3l-1 1h1c1 0 0 0 1 1 0 2-2 1-1 3h2c0 1 0 1-1 2 1 1 1 1 0 3h-2v1h3v1h-1-2v1c-1-1-1-1-1-2-1-1-1-1-1-2h2v-1h-1l-2-2c1 0 1-1 1-1 0-2 0-2-1-4h-1v-3c2 0 2 0 2-2l-1-1v-1-3-2c1-1 1-3 1-4h1c0-1 0-1 1-2h1z" class="X"></path><path d="M655 809v3h-7-1c-1 0-1 0-2-1 1-1 2-2 3-2h7z" class="D"></path><path d="M640 810c1-1 1-3 1-4h1c0-1 0-1 1-2h1l1 1 2 1c-1 0-1 1-2 1l1 1-1 1h-2c-1 1-1 2-1 3v1l-2 2v-3-2z" class="p"></path><path d="M644 804l3-1h8v1 5h-7l-2-1-1-1c1 0 1-1 2-1l-2-1-1-1z" class="E"></path><path d="M640 822c1 2 1 2 1 4 0 0 0 1-1 1l2 2h1v1h-2c0 1 0 1 1 2 0 1 0 1 1 2 0 1 1 1 2 1 0 2-1 1 0 3l1 1h1v2c4 5 9 9 14 13l6 8c1 1 2 2 2 4h-1c-4-8-10-13-17-18-2-1-3-1-5-3h-2c-1-1-2-1-4-2-1-1-1-1-1-3h-1l-1-1c-1-1-2-1-3-1v-1c-1 0-1 0-2 1h-3l-1-1c-2-1-4-2-5-3h-1c-1 0-2-1-3-2v-2c-3-1-5-1-7-2h-1l-1-1h0c1 0 2 0 2-1h-2-1l-1-1c3-1 6-1 8-1h2 14c2 0 5-1 7 0l1-2z" class="U"></path><path d="M639 840h6l6 8c-2-1-3-1-5-3h-2c-1-1-2-1-4-2-1-1-1-1-1-3z" class="J"></path><path d="M611 828c7-1 15-1 21 0h-7l1 1 1 1-6 1h0l1 1 1 2h-1c-1 0-2-1-3-2v-2c-3-1-5-1-7-2h-1z" class="G"></path><path d="M618 824h14c2 0 5-1 7 0v2h-8-22l-1-1c3-1 6-1 8-1h2z" class="F"></path><path d="M632 828h6l1 1c0 2 1 3 2 4v2c1 1 1 2 2 3 0 1 1 1 2 2h-6-1l-1-1c-1-1-2-1-3-1v-1c-1 0-1 0-2 1h-3l-1-1c-2-1-4-2-5-3l-1-2-1-1h0l6-1-1-1-1-1h7z" class="D"></path><path d="M632 828h6l1 1c-3 0-5 1-8 1-2 0-2 1-4 0l-1-1-1-1h7z" class="O"></path><path d="M622 832h3c1 1 2 1 3 3l2-2c1 1 1 2 1 3 3 0 7-1 10-1 1 1 1 2 2 3 0 1 1 1 2 2h-6-1l-1-1c-1-1-2-1-3-1v-1c-1 0-1 0-2 1h-3l-1-1c-2-1-4-2-5-3l-1-2z" class="H"></path><path d="M634 837c3 0 6 0 9 1 0 1 1 1 2 2h-6-1l-1-1c-1-1-2-1-3-1v-1z" class="i"></path><path d="M372 619c1-2 5-2 7-4h1 9 0c-1 1-2 2-3 2v2c-2 0-4 1-6 1-1 0-3 1-3 2h1 0l-1 1s-1 0-1 1h1v1l-1 2h0v1c-1 0-1 0-1 1l1 1c0 1 0 1-1 2l1 2v1h2l-1 1c0 2 1 4 1 5l-1 1c1 1 0 1 1 1v1c-1 1-1 1-1 2v1c1 3 1 5 0 8 2 1 4 1 5 2l1 2c-1 0-1 1-2 1h-2l-1 1 3 2h-1-2v4l1 1v1c1 1 2 1 3 1l1 1h-1c1 1 0 1 1 1h2c-2 2-2 2-2 4h1l1 1h2 1l2-1 2 1v1l1 1v4h1c1 1 2 1 4 1h4c-3 2-8 2-11 3h-5l-18 1c2 1 2 1 3 2-3 1-5 2-7 3-1 0-2 0-3-1v-1h-3v-2c-3 1-5 1-8 2h0c-1-3 0-9-1-11 0-2 2-5 4-6 1-1 3-2 5-1v1c-3 0-3 0-5 2 2 0 2 0 3-1h3 0l2 1c1-1 1-1 2-1l1-1c1-2 0-5 0-7l1-1h3 1 0v-15l-1-2-2-2c-1-1 0-1 0-2l-1 1v-18h2v-7l1-1c1 0 2 0 3-1h1z" class="L"></path><path d="M380 620l-1-1-1-1v-1h2c2 0 4-1 6 0v2c-2 0-4 1-6 1z" class="D"></path><path d="M371 675c1 0 1 1 2 0v-2c1-1 1-1 2 0h3 2v2c-2 0-2-1-4-1l1 1-2 2c-1-1-1-1-2 0h-2c-2 0-4 1-6 0v-2h1 5z" class="P"></path><path d="M384 676l1 1h2 1l2-1 2 1v1h-2v2c0 1 0 2-1 3h0l-1-2h0s-1 1-1 2c-1 0-1-1-2-2l-1 1-1-1v-3c-2 0-3 1-4 0v-1c1 0 2 0 3-1h1 1z" class="I"></path><path d="M370 642s2 0 2-1h2 0v2 1 1c-1 0-1 1-1 1l2 2-2 1c0 1 0 3 1 4v2c-1 2 0 3 0 5-1 1 0 2 0 3l1 1c-1 1-1 0-2 0-1-1-1-3-1-5 0-4-1-10 0-14v-1h-1-2l-1 2h-1l-2-2 3-2h1 1z" class="Q"></path><path d="M372 619l3 1v1l-2 1h0l2 1c-1 1-2 1-4 1h-2l1 1h3v1l-1 1h3c-1 1-1 1-2 1-2 0-3 1-4 2h0 1c1 1 1 1 2 1v1c-1 0-2 0-3 1 1 1 2 1 4 1-1 1-2 1-3 1v2l-1 2h2l2-1v1l-1 1c-1 1-2 0-3 1l1 1h-1-1l-3 2 2 2-1 1c-1-1 0-1 0-2l-1 1v-18h2v-7l1-1c1 0 2 0 3-1h1z" class="g"></path><path d="M368 646l1-2h2c-1 3-1 8-1 11 1 6 0 13 1 20h-5-1v2c2 1 4 0 6 0h2c1-1 1-1 2 0l2-2 1 1v1 3c1 0 1 1 2 1 0 0 0-1 1 0-1 1-1 1-2 1v1c2 0 2-1 4-2v3h1c1 0 2 0 3-1h0c0-1 1-2 1-2h0l1 2h0c1-1 1-2 1-3v-2h2l1 1v4h1c1 1 2 1 4 1h4c-3 2-8 2-11 3h-5l-18 1c2 1 2 1 3 2-3 1-5 2-7 3-1 0-2 0-3-1v-1h-3v-2c-3 1-5 1-8 2h0c-1-3 0-9-1-11 0-2 2-5 4-6 1-1 3-2 5-1v1c-3 0-3 0-5 2 2 0 2 0 3-1h3 0l2 1c1-1 1-1 2-1l1-1c1-2 0-5 0-7l1-1h3 1 0v-15l-1-2-2-2 1-1h1z" class="f"></path><path d="M368 646h1v5l-1-2-2-2 1-1h1z" class="o"></path><path d="M377 675l1 1v1 3c1 0 1 1 2 1 0 0 0-1 1 0-1 1-1 1-2 1v1c2 0 2-1 4-2v3h1c1 0 2 0 3-1h0c0-1 1-2 1-2h0l1 2h0c1-1 1-2 1-3v-2h2l1 1v4h1c1 1 2 1 4 1h4c-3 2-8 2-11 3h-5l-18 1c2 1 2 1 3 2-3 1-5 2-7 3-1 0-2 0-3-1v-1h-3v-2c-3 1-5 1-8 2l1-1c5-2 11-2 17-3 1 0 3 0 4-1 3 0 8 1 10-1h-4c-2 1-4 0-6 1-3 0-6 0-8 1h-4v-1h3c3-1 6 0 8-2 0-2 0-2-1-4 1-1 1 0 2-1-2 0-2 0-3 1-2-1-2-1-3-1l-1 1s1 0 1 1h-3l1-1v-1h-1v2c-1 1-1 2-1 3h0v1l-1-1v-6-1c2 0 3 1 4 1h1 4l1-1h2c1-1 1-1 2 0l2-2z" class="M"></path><path d="M358 689h7l-1 2h-3-3v-2z" class="P"></path><path d="M365 689l3-1c2 1 2 1 3 2-3 1-5 2-7 3-1 0-2 0-3-1v-1h3l1-2z" class="h"></path><path d="M251 914h5 1 5c2 0 5 1 8 1 1 1 0 1 1 1h-1s-1 1-2 1v1l6-1h3c2-1 5 0 7 0h0c1 1 1 1 2 1 3 0 6 1 8 1h3v1l1 1c1 0 2 0 2-1l1-1 1-1c1 1 1 2 1 3h0l4 1c0 1 0 1-1 2l-1 1 2 1h3l-2 2-3 1-8-1-4 1 1 2-6 1-1 1h-1c-2 0-3 0-4 1h-7l3-3-2-1-4 1c-2 0-6 1-8 3h0c-2 0-2 0-4-2-2 1-3 2-4 3 1 1 1 0 1 1l-4 1c-3 0-6 1-8 1-6 0-13-4-19-1h0l1-1h-1l-2-1-2-2h-5c-1-2-1-3-1-4l3-1v-1c-2 0-2-1-3-2l-1 1h-2l1-1 1-1h0l-2-2 11-2h8c3 0 5-1 7-2h3v-1c1-1 3-1 5-1 1-1 1-1 2-1h2v-1z" class="I"></path><path d="M254 918h0c2-1 4-1 6-1l1 1c-1 1-1 2-2 2h-3l-2-1v-1z" class="S"></path><path d="M244 920v-1l10-1v1 1l1 1v1h-2v1h3c-1 0-3 1-4 0l-1-1h-2 0-1l-1-1c-1-1-2-1-3-1z" class="K"></path><path d="M244 920c1 0 2 0 3 1l1 1h1 0 2l1 1v1c-4 0-9 0-12 1l-1 1h-1-2c0-2 0-2-1-3h4 2l3-3z" class="O"></path><path d="M274 917h3l4 2-4 1v1l2 1h-4l-4 2-1-1-5-1c-3 0-7 0-9 1h-3v-1h2v-1l-1-1v-1l2 1 2 1h3 1 5c1 0 3 0 5-1 1 1 1 1 2 1 1-1 2-1 2-2l-2-2z" class="L"></path><path d="M256 923c2-1 6-1 9-1l5 1 1 1c-1 1-2 1-3 2l5 1 6 1h0c-1 1-2 1-3 2h0l-4 1c-2 0-6 1-8 3h0c-2 0-2 0-4-2v-1h0c-2 0-3-1-5-1s-4 0-5-1l-2-2c-1 1 0 1-1 2-1-1-1-2-2-2h-4l-2-1 1-1c3-1 8-1 12-1v-1c1 1 3 0 4 0z" class="F"></path><path d="M251 927h4 3l2 1c-1 1-1 1-3 1-3 0-4-1-6-2z" class="I"></path><path d="M270 923l1 1c-1 1-2 1-3 2l5 1 6 1h0c-1 1-2 1-3 2h0l-4 1c-3-2-6-1-9-3 1-1 1-1 1-2 2-1 4-1 6-3z" class="G"></path><path d="M256 923c2-1 6-1 9-1l5 1c-2 2-4 2-6 3 0 1 0 1-1 2h-3l-2-1h-3-4-2-2c-2-1-4-1-6 0l-2-1 1-1c3-1 8-1 12-1v-1c1 1 3 0 4 0z" class="J"></path><path d="M258 927c2-1 4-1 6-1 0 1 0 1-1 2h-3l-2-1z" class="L"></path><path d="M277 917c2-1 5 0 7 0h0c1 1 1 1 2 1 3 0 6 1 8 1h3v1l1 1c1 0 2 0 2-1l1-1 1-1c1 1 1 2 1 3h0l4 1c0 1 0 1-1 2l-1 1 2 1h3l-2 2-3 1-8-1-4 1 1 2-6 1-1 1h-1c-2 0-3 0-4 1h-7l3-3-2-1h0c1-1 2-1 3-2h0l-6-1-5-1c1-1 2-1 3-2l4-2h4l-2-1v-1l4-1-4-2z" class="X"></path><path d="M286 919c1 0 3 0 4 1 2 1 3 1 4 1h2l-6 1c-2-1-4-1-5-1 0-1 0-1 1-2z" class="L"></path><path d="M281 919h5c-1 1-1 1-1 2 1 0 3 0 5 1h0c-2 0-2 0-3 2l2 1-2 1c-1-1-1-1-2-1-1-1-2-1-3-1-3-2-5-1-7-2h4l-2-1v-1l4-1z" class="G"></path><path d="M296 921h7l4 1c0 1 0 1-1 2l-1 1-2-1c-2 1-4 1-7 0-2 1-5 1-7 1h0l-2-1c1-2 1-2 3-2h0l6-1z" class="H"></path><path d="M290 922c1 0 2 0 4 1h1l1 1c-2 1-5 1-7 1h0l-2-1c1-2 1-2 3-2z" class="B"></path><path d="M296 924c3 1 5 1 7 0l2 1 2 1h3l-2 2-3 1-8-1-4 1c0-1 0-1-1-1l1-2h0c-1 0-2 0-3-1h-1c2 0 5 0 7-1z" class="M"></path><path d="M307 926h3l-2 2-3 1-8-1c4-1 7-2 10-2z" class="C"></path><path d="M275 922c2 1 4 0 7 2 1 0 2 0 3 1 1 0 1 0 2 1l2-1h0 1c1 1 2 1 3 1h0l-1 2c1 0 1 0 1 1l1 2-6 1-1 1h-1c-2 0-3 0-4 1h-7l3-3-2-1h0c1-1 2-1 3-2h0l-6-1-5-1c1-1 2-1 3-2l4-2z" class="I"></path><path d="M289 925h0 1c1 1 2 1 3 1h0l-1 2c-1 0-2 1-3 1l1-1c-1-1-2-1-3-2l2-1z" class="Y"></path><path d="M278 931c1 0 3-1 5 0 1 0 2 1 3 1v1c-2 0-3 0-4 1h-7l3-3z" class="E"></path><path d="M275 922c2 1 4 0 7 2 1 0 2 0 3 1-1 1-1 2-3 2h-9l-5-1c1-1 2-1 3-2l4-2z" class="D"></path><path d="M224 920h8l1 1h7c0 1 0 1-1 2h-4c1 1 1 1 1 3h2 1l2 1h4c1 0 1 1 2 2 1-1 0-1 1-2l2 2c1 1 3 1 5 1s3 1 5 1h0v1c-2 1-3 2-4 3 1 1 1 0 1 1l-4 1c-3 0-6 1-8 1-6 0-13-4-19-1h0l1-1h-1l-2-1-2-2h-5c-1-2-1-3-1-4l3-1v-1c-2 0-2-1-3-2l-1 1h-2l1-1 1-1h0l-2-2 11-2z" class="L"></path><path d="M247 931l2 1v1c-1 1-2 1-4 1h-3 0l2-1c2 0 2-1 3-2z" class="B"></path><path d="M226 925l2-1h1c2 0 4-1 6-1 1 1 1 1 1 3h-2c-2-2-5-1-8-1z" class="K"></path><path d="M238 935h1c1-1 1-1 3-1h3v3h-2c-1 0-1-1-2-1s-2 0-3-1z" class="G"></path><path d="M255 930c2 0 3 1 5 1h0v1c-2 1-3 2-4 3 1 1 1 0 1 1l-4 1c0-2 0-3 2-4-1-1-2-1-3-1v-1c1 0 2 0 2-1h1z" class="O"></path><path d="M224 920h8l1 1h7c0 1 0 1-1 2h-4c-2 0-4 1-6 1h-1l-2 1c-2 0-3 0-5-1l-2 3c-2 0-2-1-3-2l-1 1h-2l1-1 1-1h0l-2-2 11-2z" class="B"></path><path d="M216 925s1-1 2-1h3l-2 3c-2 0-2-1-3-2z" class="O"></path><path d="M219 928c10-2 18-1 28 3-1 1-1 2-3 2l-2 1h0c-2 0-2 0-3 1h-1-10l-2 1-2-1-2-2h-5c-1-2-1-3-1-4l3-1z" class="D"></path><path d="M222 933c6-1 14-1 20 1h0c-2 0-2 0-3 1h-1-10l-2 1-2-1-2-2z" class="F"></path><path d="M375 829c1 1 1 0 2 0 0-2 1-3 2-5l1 1-3 7-2 2-2 3-3 4c0 1-2 3-2 4l-3 3-2 2-2 3v1c-1 1-2 1-2 2-1 2-2 2-3 4h0c-2 3-4 5-5 7-2 4-4 8-5 12 0 1 0 3-1 4v5c-1 2-1 5-1 7 1 2 1 4 1 6v2c2 0 3-1 5-2h2c-1 1-1 2-2 2l1 2-1 1h1l-1 1h-2l-1 1-1-1c-1-2 0 0-1-1l1-1-1-1h-1v3c0 1-1 1-2 2-1 0-2 0-3-1h-4 0-1c0-1 0-1-1-2h0c-1 3 2 8 3 11l-9-1-1-1c-2 0-3-1-4-2l-1-1-1-2c-1 1-2 1-4 1h-1-1l-1-2h0l-1-3v-3-2c0-1 1-2 1-2 0-1 0-1-1-2h0c2-6 3-12 6-17v5l-1 2h1l1 2h3 2 1c2 0 4 0 6-1v-1h-1l1-1c0-2 1-4 1-6 1-1 1 0 1-1 0-3 2-7 3-9 1-1 1 0 1-2 0-1 1-1 1-2l1-1c1-1 2-3 2-4l12-12 2-2s0-1 1-1l2-2c-1-1-1-1-1-2h1l1-1h1c1 1 1 0 2 1l2-2-1-1c-1 1-1 1-2 1h0c1-4 5-8 7-11 1 1 1 1 1 2h-1c-1 1-1 1-1 2h1 0 1c1-1 2-1 3-2v-1l3-1z" class="C"></path><path d="M338 876h4l1 1h-5v-1z" class="H"></path><path d="M337 873h6l1 1-1 1c-2-1-4 0-6-2z" class="E"></path><path d="M353 858l2 1-3 5-1-1-1 1-1-1 1-1v-1h-1-3 0l2-1 5-2z" class="F"></path><path d="M333 894c-1-2 0-3 0-5 0-4 1-8 2-12v1c1 1 1 0 2 1h-1v2c1 0 1 1 2 1-1 0-2 1-3 1 1 1 1 1 2 1-2 3-2 3-2 6 1 1 1 1 2 1l2 1 2-1h1v3 1h-1-2-4-2v-1z" class="O"></path><path d="M333 894c2-1 5-1 7 0 1 0 1 0 1 1h-2-4-2v-1z" class="I"></path><path d="M351 852c2 1 2 2 4 1l1-1c2-1 3-1 5-1l-1 2-5 6-2-1-5 2h-4l4-5 3-3z" class="G"></path><path d="M353 857l1-2c2-1 3-2 6-2l-5 6-2-1h1l-1-1z" class="C"></path><path d="M348 855l5 2 1 1h-1l-5 2h-4l4-5z" class="H"></path><path d="M353 849l1 1-3 2-3 3-4 5c-3 3-5 7-7 12-1 1-1 4-2 5-1 4-2 8-2 12 0 2-1 3 0 5v1 2 3 2h1c-1 2 0 2 0 4h2l-1 1v1h0-1c0-1 0-1-1-2h0c-1 3 2 8 3 11l-9-1-1-1c-2 0-3-1-4-2l-1-1-1-2v-1l-1-3 1 1c2 0 0-1 2 0 1 1 5 0 6 0v-1h4v-2c-1-2 0-4-1-6l-1-1 1-2-1-1c1-1 1-3 1-4v-2-1h-1l1-1c0-2 1-4 1-6 1-1 1 0 1-1 0-3 2-7 3-9 1-1 1 0 1-2 0-1 1-1 1-2l1-1c1-1 2-3 2-4l12-12z" class="d"></path><path d="M328 907v-1h4v2c-1 1-2 1-3 1h0l1 2h-2c-2 0-3 1-5 0 1-1 1-1 2-1v-1h-3c0 2 0 2-1 3l-1-2v-1l-1-3 1 1c2 0 0-1 2 0 1 1 5 0 6 0z" class="n"></path><g class="M"><path d="M319 906l1 1c2 0 0-1 2 0 1 1 5 0 6 0l1 1c-3 1-6 0-9 1l-1-3z"></path><path d="M312 897c2-6 3-12 6-17v5l-1 2h1l1 2h3 2 1c2 0 4 0 6-1v2c0 1 0 3-1 4l1 1-1 2 1 1c1 2 0 4 1 6v2h-4v1c-1 0-5 1-6 0-2-1 0 0-2 0l-1-1 1 3v1c-1 1-2 1-4 1h-1-1l-1-2h0l-1-3v-3-2c0-1 1-2 1-2 0-1 0-1-1-2h0z"></path></g><path d="M319 889h3 2 1c2 0 4 0 6-1v2l-3 2h1l1 1v1c-3 1-9 0-12 0 0-2 0-4 1-5z" class="G"></path><path d="M331 898c1 2 0 4 1 6v2h-4v1c-1 0-5 1-6 0-2-1 0 0-2 0l-1-1h0c-1-2-1-4-1-7h3v2h2l1 1h-3v1c2 0 3 2 6 2l1-1-1-2c0-3 3-2 4-4z" class="b"></path><path d="M312 897c2-6 3-12 6-17v5l-1 2h1l1 2c-1 1-1 3-1 5v4 1c0 3 0 5 1 7h0l1 3v1c-1 1-2 1-4 1h-1-1l-1-2h0l-1-3v-3-2c0-1 1-2 1-2 0-1 0-1-1-2h0z" class="j"></path><path d="M317 887h1l1 2c-1 1-1 3-1 5v4c-1-1-1-1-2-1 0-3 1-7 1-10z" class="g"></path><path d="M316 897c1 0 1 0 2 1v1c0 3 0 5 1 7h0l1 3v1c-1 1-2 1-4 1h-1-1l-1-2c2-1 0 0 0-1s1-1 1-1v-2h1l1 1 1-1h-1c0-1 0-2-1-3l-1-1c1-2 1-3 2-4z" class="f"></path><path d="M375 829c1 1 1 0 2 0 0-2 1-3 2-5l1 1-3 7-2 2-2 3-3 4c0 1-2 3-2 4l-3 3-2 2-2 3v1c-1 1-2 1-2 2-1 2-2 2-3 4h0c-2 3-4 5-5 7-2 4-4 8-5 12 0 1 0 3-1 4v5c-1 2-1 5-1 7 1 2 1 4 1 6v2c2 0 3-1 5-2h2c-1 1-1 2-2 2l1 2-1 1h1l-1 1h-2l-1 1-1-1c-1-2 0 0-1-1l1-1-1-1h-1v3c0 1-1 1-2 2-1 0-2 0-3-1h-4v-1l1-1h-2c0-2-1-2 0-4h-1v-2-3-2h2 4 2 1v-1-3h1v-1c1-3 0-6 1-9l3-10c1-3 3-5 5-7l3-5 5-6 1-2c-2 0-3 0-5 1l-1 1c-2 1-2 0-4-1l3-2-1-1 2-2s0-1 1-1l2-2c-1-1-1-1-1-2h1l1-1h1c1 1 1 0 2 1l2-2-1-1c-1 1-1 1-2 1h0c1-4 5-8 7-11 1 1 1 1 1 2h-1c-1 1-1 1-1 2h1 0 1c1-1 2-1 3-2v-1l3-1z" class="n"></path><path d="M371 833c1 0 2-1 2-1 1-1 0-1 2-2v2c0 1-1 3-2 4-1-1-1-1-2-3z" class="K"></path><path d="M371 833c1 2 1 2 2 3-1 2-3 4-4 5v-2-2c0-1 1-3 1-4h1z" class="F"></path><path d="M354 850h1c1-2 3-4 4-6 2 0 2 0 3-1 2-2 4-4 7-4v2c-1 1-2 3-3 4s-2 2-3 4c-1 0-1 1-1 2h-1c-2 0-3 0-5 1l-1 1c-2 1-2 0-4-1l3-2z" class="C"></path><path d="M342 894c1 1 1 2 1 4-1 0-1 0-2 1 1 0 1 1 2 0v3h-1-2c2 1 3 0 4 2v3c0 1-1 1-2 2-1 0-2 0-3-1h-4v-1l1-1h-2c0-2-1-2 0-4h-1v-2-3-2h2 4 2 1v-1z" class="G"></path><path d="M335 895h4c1 1 1 0 2 1h1v1c-2 0-2 1-3 2h-3l-2-2 1-2z" class="K"></path><path d="M706 926l12-1h6c1 0 2 1 3 1h3l1 1-1 1-1 1h7l1 1v1c-2 0-3 0-4 1v-1l-4 2v1h1c1 0 1 0 2 1h2v1c3 0 6 0 8 1 1-1 2-1 4-1h2 2v-1h4c1 0 2 0 3-1h0c1 1 2 1 2 1 2-1 3 0 5 1h0c2-1 3 0 4 0l-1 2h4l3 2v1l-1 1c2 1 6-1 9 0-1 2-3 0-4 2v1h6c2 0 3 1 6 0l-13 3c-1 0-2 0-2 1l-1-1h-3-1c-2 0-3 0-5 1-3 1-6 2-9 2l-1-1h-1c-5 2-12 2-18 3-2 0-4 1-6 0l-20 3-2-1 2-3h-2c-2-1-4-1-6-2v-1l1-1-1-1h0l1-1h5v-1l-1-1c0-1 0-2 1-3l-1-1-1-1c0 2 0 2-1 2l-5-1v-1l1-1-4-1v-1h5v-2h0c0-1-1-1-1-2h-1-1-2c-2 0-1-1-2-1-2 0-4 1-6 1h0c-2-1-2-2-4-3l9-2c3 0 6 0 9-1h3z" class="X"></path><path d="M710 932c1 0 4 0 5 1v1h-1-1l-2 2h-1l-1-1h-1c1-1 1-2 2-3zm-2 9c2 0 3 0 5 1 1 1 3 0 5 1-3 0-7 0-10 1v1l-1-1c0-1 0-2 1-3z" class="L"></path><path d="M721 939c1 1 1 1 1 2h0v1h-1c-1-1-2 0-3 0v1c-2-1-4 0-5-1 3-2 4-2 7-2l1-1z" class="G"></path><path d="M701 932h2c2 1 3 0 5 0h2c-1 1-1 2-2 3l-1 1c-2-1-2-1-3-1l-2 1v-2h0c0-1-1-1-1-2z" class="F"></path><path d="M716 937c8 0 15 0 23 1-5 3-11 3-17 3h0c0-1 0-1-1-2h0v-1c-2 0-3 0-5-1z" class="S"></path><path d="M701 938c2-1 4-1 7-1h8c2 1 3 1 5 1v1h0l-1 1c-3 0-4 0-7 2-2-1-3-1-5-1l-1-1-1-1c0 2 0 2-1 2l-5-1v-1l1-1z" class="E"></path><path d="M708 945c4 0 9-1 13-2s8-1 12-1l5-1c-1 1-2 1-4 1 1 1 0 1 1 1s1 0 2 1h-3c-2 1-2 2-4 1-2 0-4 0-6 1h-4c0 1-1 1-1 1-3-1-5 0-8 0h-2l-1-1v-1z" class="H"></path><path d="M724 925c1 0 2 1 3 1h3l1 1-1 1-1 1h7l1 1v1c-2 0-3 0-4 1v-1l-4 2v1h1c1 0 1 0 2 1h2v1h0-5c-2-1-1-1-2 0-2 0-3-1-5 0h0l-2-2c-2 1-3 1-5 1h0l-1-1h1v-1c-1-1-4-1-5-1h-2c-2 0-3 1-5 0l2-1c1 1 4 0 7 0h2 1c1-1 1-1 3-1l2-1c2 0 4 0 5-1v-1l-1-1v-1z" class="M"></path><path d="M723 931h-1l1-1c2-1 4-1 6-1h7l1 1v1c-2 0-3 0-4 1v-1c-2 0-4 0-5 1h-2c-1-1-2-1-3-1z" class="H"></path><path d="M724 925c1 0 2 1 3 1h3l1 1-1 1-1 1c-2 0-4 0-6 1l-1 1h1v1h-1-2c-1 1-2 1-3 1l-1 1h-1v-1c-1-1-4-1-5-1h-2c-2 0-3 1-5 0l2-1c1 1 4 0 7 0h2 1c1-1 1-1 3-1l2-1c2 0 4 0 5-1v-1l-1-1v-1z" class="C"></path><path d="M706 926l12-1h6v1l1 1v1c-1 1-3 1-5 1l-2 1c-2 0-2 0-3 1h-1-2c-3 0-6 1-7 0l-2 1h-2-1-1-2c-2 0-1-1-2-1-2 0-4 1-6 1h0c-2-1-2-2-4-3l9-2c3 0 6 0 9-1h3z" class="S"></path><path d="M703 930v-1l3-2h1l1 2-3 1h-2z" class="C"></path><path d="M724 926l1 1v1c-1 1-3 1-5 1-1 0-1 0-1-1v-1l5-1z" class="J"></path><path d="M685 929l9-2c0 1 0 2 1 2h2c1-1 3-1 4 0l1 1h1 2l3-1c1 0 2-1 3 0h2l1 1v1h-2c-3 0-6 1-7 0l-2 1h-2-1-1-2c-2 0-1-1-2-1-2 0-4 1-6 1h0c-2-1-2-2-4-3z" class="B"></path><path d="M738 941c1-1 4-1 5-1 4-1 9-2 13-3 4 0 8 0 11 1h4l3 2v1l-1 1c-6 0-12 1-17 1-1 1-3 1-4 2h0-5l-10-1c-1-1-1-1-2-1s0 0-1-1c2 0 3 0 4-1z" class="W"></path><path d="M752 945h0c1-1 3-1 4-2 5 0 11-1 17-1 2 1 6-1 9 0-1 2-3 0-4 2v1h6c2 0 3 1 6 0l-13 3c-1 0-2 0-2 1l-1-1h-3-1c-2 0-3 0-5 1-3 1-6 2-9 2l-1-1h-1c-5 2-12 2-18 3-2 0-4 1-6 0l-20 3-2-1 2-3h-2c-2-1-4-1-6-2v-1l1-1-1-1h0l1-1h5l1 1h2c3 0 5-1 8 0 0 0 1 0 1-1h4c2-1 4-1 6-1 2 1 2 0 4-1h3l10 1h5z" class="O"></path><path d="M744 949l12-2c3 2 5 2 9 2-3 1-6 2-9 2l-1-1h-1c-5 2-12 2-18 3-2 0-4 1-6 0h-2-1-3c-3 1-4 1-6 1v-1c2 0 4 0 6-1l20-3z" class="Y"></path><path d="M703 948l-1-1h0l1-1h5l1 1h2c3 0 5-1 8 0 0 0 1 0 1-1 2 1 3 1 4 2h1c3 1 7 0 11 0-7 2-13 3-19 4h-1-1c-1-1-2-1-3 0h-2-2c-2-1-4-1-6-2v-1l1-1z" class="Z"></path><path d="M703 948l-1-1h0l1-1h5l1 1h2c3 0 5-1 8 0 0 0 1 0 1-1 2 1 3 1 4 2l-15 1c-2-1-4-1-6-1z" class="I"></path><path d="M703 948c2 0 4 0 6 1h5c0 1 1 1 2 2l-1 1c-1-1-2-1-3 0h-2-2c-2-1-4-1-6-2v-1l1-1z" class="V"></path><path d="M752 945h0c1-1 3-1 4-2 5 0 11-1 17-1 2 1 6-1 9 0-1 2-3 0-4 2v1h6c2 0 3 1 6 0l-13 3c-1 0-2 0-2 1l-1-1h-3-1c-2 0-3 0-5 1-4 0-6 0-9-2l-12 2c-3 0-5 0-7-1-1 0-2-1-2-1v-1c1 0 2 0 3-1l-1-1 10 1h5z" class="C"></path><path d="M737 944l10 1-3 2c-2 0-5 0-7 1-1 0-2-1-2-1v-1c1 0 2 0 3-1l-1-1z" class="S"></path><path d="M778 945h6c2 0 3 1 6 0l-13 3c-1 0-2 0-2 1l-1-1h-3-1c-2 0-3 0-5 1-4 0-6 0-9-2 5 1 10 1 14-1h0 2c3 1 5 0 6-1z" class="G"></path><path d="M752 945h0c1-1 3-1 4-2 5 0 11-1 17-1 2 1 6-1 9 0-1 2-3 0-4 2l-16 1c-2 0-3 1-5 0s-3 0-4 0h-1z" class="B"></path><path d="M328 923h1v1c1 1 4 1 5 1 3 0 6 1 9-1l1 1c2 0 4 1 7 1h2c1 0 3 0 4 1l-1 1c2 1 6 0 7 2v1h2l-3 1h-2v1c1 2 3 2 5 2h2l5 1h0v2h-3l-2 1h-2v1h-7s-1 0-1 1l-2-1h-3l-1 2h-5 1c0 1 1 1 2 1l1 1-1 1c-1 0-2 0-3-1h0-3v2l-2 1h-1l-2 1-1 1-5 2c-2 0-2 0-4-1h-2v1l-2-1v3h-6c-1 1 0 1-1 1-1-1-2 0-3 0l-20-1h-3c-2-2-6-4-9-4h0v-1h5c1-1 1-2 2-2-3 0-6 0-9-2v-1c2-1 5-1 7-2-1-1-1-1-3-1-1 0-1 0-1 1h-2 0c-1 0-2 0-2-1l1-1s0-1 1-1v-1c-1-1-3-1-4-2 1-1 3 0 5 0v-1c1-1 2-1 4-1h1l1-1 6-1-1-2 4-1 8 1h5 0l5 1h0c1-1 2-1 2-2v-1c2 0 4 0 6-2h-2v-1c2 0 4 0 6-1h1z" class="O"></path><path d="M331 933l1 1 1 1h-4l-1-1v-1h3z" class="L"></path><path d="M294 939h0-7c1-2 2-3 3-4v1h1l3 3z" class="H"></path><path d="M282 935h7c-3 1-5 2-8 2-1-1-3-1-4-2 1-1 3 0 5 0z" class="b"></path><path d="M310 935h7c0 1-1 2-2 3-2 1-4 1-5 1l-2-1-1-2 3-1z" class="i"></path><path d="M338 936l2 1-1 1c2 2 2 3 5 3 1 0 2-1 3-2 2 0 3 1 5 1l-1 2h-5c-2 0-3 0-5-1-1 0 0 0-1 1h0v1h-4l-1-1c1-1 1-2 2-2s0 0 1-1c0 0-1 0-1-1l1-2z" class="C"></path><path d="M326 950c-1-1-2-1-2-2 1-1 1-2 3-2 2-1 4-1 6-1h0c1 2 4 0 6 1-2 1-3 1-5 1 1 1 1 2 3 2l-5 2c-2 0-2 0-4-1h-2z" class="G"></path><path d="M328 950c0-1 0-1-1-1h0c2-2 4-2 7-2 1 1 1 2 3 2l-5 2c-2 0-2 0-4-1z" class="F"></path><path d="M291 935c5 0 11-2 15 0l1 1 1 2c-2 2-2 1-5 1h1c1-1 2-1 2-1-1-1 0-1-1-1-1 1-2 1-3 2-1 0-1 0-1-1h-3v1h0c-1 0-2 1-4 0l-3-3h-1v-1h1z" class="B"></path><path d="M340 937c2-2 6-1 9-1 2 1 3 1 5 1l2 1c-1 0-1 1-1 2h-3c-2 0-3-1-5-1-1 1-2 2-3 2-3 0-3-1-5-3l1-1z" class="J"></path><path d="M297 928l8 1h5 0l5 1v1c-2 1-2 0-4 0 0 0-1 1-2 1v1l-2 1c1 0 2 1 3 1l-3 1-1-1c-4-2-10 0-15 0l-1-2h5c2-1 3-1 5-1v-1h-1-5l-1-2 4-1z" class="K"></path><path d="M306 932l2-2c1-1 1-1 2-1l5 1v1c-2 1-2 0-4 0 0 0-1 1-2 1v1c-1-1-3-1-4 0h-4v-1h4 1z" class="E"></path><path d="M297 928l8 1h5 0c-1 0-1 0-2 1l-2 2-1-1c-2-1-4-1-6 0h-5l-1-2 4-1z" class="S"></path><path d="M328 923h1v1c1 1 4 1 5 1 3 0 6 1 9-1l1 1c2 0 4 1 7 1h2c1 0 3 0 4 1l-1 1c2 1 6 0 7 2v1h2l-3 1h-2v1c1 2 3 2 5 2h2l5 1h0v2h-3l-2 1h-2v1h-7s-1 0-1 1l-2-1c0-1 0-2 1-2l-2-1c-2 0-3 0-5-1-3 0-7-1-9 1l-2-1h-1l1-1v-1h-1-2c-1 1-1 1-2 1l-1-1-1-1h-3-4c-3 1-5 1-8 1-1-1 0-1-2-1l-5-1c1 0 2-1 2-1 2 0 2 1 4 0v-1h0c1-1 2-1 2-2v-1c2 0 4 0 6-2h-2v-1c2 0 4 0 6-1h1z" class="K"></path><path d="M331 933l1-1c3 0 4 0 6 1h0c-2 1-3 1-6 1l-1-1z" class="C"></path><path d="M326 926h1l1 1v1c-2 1-4 1-6 2v-2h0c1 0 2-1 4-2h0z" class="O"></path><path d="M335 929c4 0 9-1 14 0l-1 1c-2 1-2 1-4 1-3-1-8 0-10-1l1-1z" class="S"></path><path d="M323 925l3 1h0c-2 1-3 2-4 2h0v2h-7c1-1 2-1 2-2v-1c2 0 4 0 6-2z" class="M"></path><path d="M338 933c2-1 3-1 5-2 1 1 2 1 2 2 1 0 3 0 4 1h-2v1h2v1c-3 0-7-1-9 1l-2-1h-1l1-1v-1h-1-2c-1 1-1 1-2 1l-1-1c3 0 4 0 6-1z" class="G"></path><path d="M334 925c3 0 6 1 9-1l1 1c2 0 4 1 7 1h2c1 0 3 0 4 1l-1 1c2 1 6 0 7 2v1h2l-3 1c-1-1-3-1-4-2l-9-1c-5-1-10 0-14 0l-2-1h2v-1h-3c-1 0-1-1-1-1 1 0 2-1 3-1z" class="M"></path><path d="M334 925c3 0 6 1 9-1l1 1c2 0 4 1 7 1h2c1 0 1 1 2 2h0-1c0-1-1-1-1-1h-1c-2 1-3 1-4 1-4-1-8-1-12-1h-1-3c-1 0-1-1-1-1 1 0 2-1 3-1z" class="C"></path><path d="M358 930c1 1 3 1 4 2h-2v1c1 2 3 2 5 2h2l5 1h0v2h-3l-2 1h-2v1h-7s-1 0-1 1l-2-1c0-1 0-2 1-2l-2-1c-2 0-3 0-5-1v-1l2-1c1-1 3-1 4-1l-1-1h-1v-1c2 0 3 1 5-1z" class="G"></path><path d="M351 934c1 1 2 1 3 1s2 1 3 1v1h-3c-2 0-3 0-5-1v-1l2-1z" class="M"></path><path d="M356 938c3-1 5 0 8 1h1v1h-7s-1 0-1 1l-2-1c0-1 0-2 1-2z" class="I"></path><path d="M287 941h1c4 0 9 1 14 1 6-1 12-2 19-2 2 0 4 0 7 1h4v1 2c1-1 2-1 3-1v1c-1 0-1 0-2 1-2 0-4 0-6 1-2 0-2 1-3 2 0 1 1 1 2 2v1l-2-1v3h-6c-1 1 0 1-1 1-1-1-2 0-3 0l-20-1h-3c-2-2-6-4-9-4h0v-1h5c1-1 1-2 2-2-3 0-6 0-9-2v-1c2-1 5-1 7-2z" class="N"></path><path d="M328 941h4v1 2c1-1 2-1 3-1v1c-1 0-1 0-2 1-2 0-4 0-6 1-2 0-2 1-3 2 0 1 1 1 2 2v1l-2-1c-6-1-12-1-19-1l-13-1h-5c1-1 1-2 2-2h3c2 1 4 1 6 1 4 1 11 1 15 0 4-2 7-2 11-3h0c1 0 1 0 1-1 1 0 2-1 3-2z" class="M"></path><path d="M328 941h4v1 2c-3 0-5 0-7 1-1 1-1 1-2 1l2-1-1-1h0c1 0 1 0 1-1 1 0 2-1 3-2z" class="C"></path><path d="M287 948h5l13 1c7 0 13 0 19 1v3h-6c-1 1 0 1-1 1-1-1-2 0-3 0l-20-1h-3c-2-2-6-4-9-4h0v-1h5z" class="i"></path><path d="M287 948h5l13 1c-2 1-7 0-10 0-1 0-3 0-4 1 0 1 1 1 2 2l1 1h-3c-2-2-6-4-9-4h0v-1h5z" class="B"></path><path d="M411 728h0l8-1v2h2l-4 10c0 1 0 2 1 4-1 3-3 7-3 11h0l1-1v3 4h0c-2 0-4 0-6 1-2 0-4 1-6 1h0l-2-1-3 3v1h-1v4 3c0 1 0 2 1 3h1c0 1 1 1 0 1 0 1-1 3-1 4v-1c-1 1-1 1-2 1h0l-1 2 2 2c-1 0-2 1-2 2l2 1c-1 0-1 0-2 1h-3-7v2c-2 0-3-1-5 1v2l-1-1v1h0c0 1 0 1-1 2l-1-1h0l1-6c-1-1-5 0-7 0h-1l-2-1c1-1 6-1 8-1h0-7c-1-2 0-2 0-3s-1-1-1-2c0 0 0-2 1-3l-2-1c0-1 0-1 1-2s6-2 9-2v-10l-1-9c0-5 0-11-3-15-2-2-6-5-8-7v-1h2l4-1 12-1h8 5 3l4-1h7z" class="U"></path><path d="M384 762c-1-1-2-1-3-2h0c1 0 2 0 3-1h-3v-1l5-1c3 1 5 1 9 1v3h-1v1s-1 0-1 1c-2 0-7 0-9-1z" class="F"></path><path d="M368 731l4-1c2 1 3 2 5 2 2-1 2-1 4-1l1 1-1 1-6 1v1l1 2c1-1 1-1 1-2h2v1c0 1 1 2 1 2 0 2-1 3-1 4h2v1c-1 0 0-1-1 0v1 1 3h0c-1-5-3-7-5-11l-1-1-2-2h-1v-1-1h-1-1l-1-1z" class="T"></path><path d="M372 730l12-1c1 1 1 1 2 3l-5 2h1 1c1 0 2-1 3 0h0v2c1 1 1 1 1 0h2l1 1-6 1h-4s-1-1-1-2v-1h-2c0 1 0 1-1 2l-1-2v-1l6-1 1-1-1-1c-2 0-2 0-4 1-2 0-3-1-5-2z" class="P"></path><path d="M400 729l4-1-1 2c-1 1-3 1-4 1 0 1-1 1-1 2-3 2-6 2-9 2v1h-2c0 1 0 1-1 0v-2h0c-1-1-2 0-3 0h-1-1l5-2c-1-2-1-2-2-3h8 5 3z" class="U"></path><path d="M392 729h5l-2 2h-2l-1-2z" class="g"></path><path d="M388 731c1 1 1 1 2 1 1 1 1 1 2 1v1c-2 0-3 0-5-1l-1 1c-1-1-2 0-3 0h-1-1l5-2 2-1z" class="j"></path><path d="M384 729h8l1 2h-5l-2 1c-1-2-1-2-2-3z" class="k"></path><path d="M398 733c1 2 1 3 3 4 1 2 2 5 5 6h2v1c-1 0-4 0-5-1v-1c-1 2-1 2-1 4v2l-1 2c0 2 1 2 0 4h-2l-2 1c1 1 1 1 1 2h0v2c-1 0-2 0-2 1-1 1-1 0-1 1v-3c-4 0-6 0-9-1l-4-1-1-1c2 0 1 0 2-2 1 0 2 0 2-1l1-1c-1-1-1-1-2-1h-1c1-1 2-1 3-2v-1l2-1-2-2c-1 1-1 1-2 1v-1h2l-1-1v-1l-4 1v-1h-2c0-1 1-2 1-4h4l6-1-1-1v-1c3 0 6 0 9-2z" class="Y"></path><path d="M380 738h4l2 1v2h1 3v1h-5l-4 1v-1h-2c0-1 1-2 1-4z" class="Q"></path><path d="M398 733c1 2 1 3 3 4 1 2 2 5 5 6h2v1c-1 0-4 0-5-1v-1c-1 2-1 2-1 4v2l-1 2c0 2 1 2 0 4h-2 0l-1-1 1-1v-1h-2c1-1 2-1 3-2h-2l-1-1h3v-1l-2-1v-1h2l-1-2h-2 0 3v-1-1h-4v-1c2 0 3 0 4-1l-1-1c-3-1-5 0-8 0l-1-1-1-1v-1c3 0 6 0 9-2z" class="d"></path><path d="M398 746l2 1v1h-3l1 1h2c-1 1-2 1-3 2h2v1l-1 1 1 1h0l-2 1c1 1 1 1 1 2h0v2c-1 0-2 0-2 1-1 1-1 0-1 1v-3c-4 0-6 0-9-1l-4-1-1-1c2 0 1 0 2-2 1 0 2 0 2-1l1-1c-1-1-1-1-2-1h-1c1-1 2-1 3-2 1 0 2 0 2-1 1-1 2 0 3 0l7-1z" class="G"></path><path d="M390 752h7 0l-1 1h-7v-1h1z" class="C"></path><path d="M398 746l2 1v1h-3-1-5v-1l7-1z" class="K"></path><path d="M411 728h0l8-1v2h2l-4 10c0 1 0 2 1 4-1 3-3 7-3 11h0l1-1v3 4h0c-2 0-4 0-6 1-2 0-4 1-6 1 1-1 0-1 0-2v-2l-1 1-1-1c2-3 1-2 4-2 0 0 2 0 3-1h-1l-1-2 2-2h0-2c-3 0-4-1-5-3v-2c0-2 0-2 1-4v1c1 1 4 1 5 1v-1h-2c-3-1-4-4-5-6-2-1-2-2-3-4 0-1 1-1 1-2 1 0 3 0 4-1l1-2h7z" class="h"></path><path d="M410 733h5v1c0 1 0 1-1 1-2 1-3 1-4 0v-2z" class="j"></path><path d="M410 735h-3c-2 0-5-1-7-2l1-1c3 1 6 1 9 1v2z" class="n"></path><path d="M411 746c-1 1-1 2-1 3h2l2-3-1 4-1 4-2 1h-1-1l-1-2 2-2v-4l2-1z" class="P"></path><path d="M410 755c0-1 0-3 1-4l2-1-1 4-2 1z" class="m"></path><path d="M411 728h0l8-1v2c-5 1-11 1-16 1l1-2h7z" class="R"></path><path d="M401 737l4 2h0c2-1 5 0 7 0l1 1-1 1s0 1-1 2v1 2h0l-2 1v4h0-2c-3 0-4-1-5-3v-2c0-2 0-2 1-4v1c1 1 4 1 5 1v-1h-2c-3-1-4-4-5-6z" class="k"></path><path d="M402 746h2l1 1c1-1 1-1 3-1l1 1v4h0-2c-3 0-4-1-5-3v-2z" class="U"></path><path d="M417 739c0 1 0 2 1 4-1 3-3 7-3 11h0l1-1v3 4h0c-2 0-4 0-6 1-2 0-4 1-6 1 1-1 0-1 0-2v-2l-1 1-1-1c2-3 1-2 4-2 0 0 2 0 3-1h1l2-1 1-4 1-4 3-7z" class="G"></path><path d="M410 755l2-1-2 5 1 1 3-3h0v2l2 1h0c-2 0-4 0-6 1-2 0-4 1-6 1 1-1 0-1 0-2v-2l-1 1-1-1c2-3 1-2 4-2 0 0 2 0 3-1h1z" class="U"></path><path d="M402 748c1 2 2 3 5 3h2 0l-2 2 1 2h1c-1 1-3 1-3 1-3 0-2-1-4 2l1 1 1-1v2c0 1 1 1 0 2h0l-2-1-3 3v1h-1v4 3c0 1 0 2 1 3h1c0 1 1 1 0 1 0 1-1 3-1 4v-1c-1 1-1 1-2 1h0l-1 2 2 2c-1 0-2 1-2 2l2 1c-1 0-1 0-2 1h-3-7v2c-2 0-3-1-5 1v2l-1-1v1h0c0 1 0 1-1 2l-1-1h0l1-6c-1-1-5 0-7 0h-1l-2-1c1-1 6-1 8-1h0-7c-1-2 0-2 0-3s-1-1-1-2c0 0 0-2 1-3l-2-1c0-1 0-1 1-2s6-2 9-2v-10c0 1 0 1 1 2v-3h2 3c2 1 7 1 9 1 0-1 1-1 1-1v-1h1c0-1 0 0 1-1 0-1 1-1 2-1v-2h0c0-1 0-1-1-2l2-1h2c1-2 0-2 0-4l1-2z" class="R"></path><path d="M394 762h1l-2 2c1 1 1 1 2 1l-1 1h-1-2l2 2h-1c-1 0-1 1-2 1 1 1 2 1 3 1l-1 1c-1 0-2 0-3 1 1 0 3 0 4 1h0c-4 1-9 2-12 0h0c1 0 2 0 3-1l-3-1v-1h1c1-1 2-1 3-1-1 0-3 0-4-1 2-1 3-1 4-1l1-1h-4l-1-1 2-1c0-1-1-1-2-2h3c2 1 7 1 9 1 0-1 1-1 1-1z" class="I"></path><path d="M402 748c1 2 2 3 5 3h2 0l-2 2 1 2h1c-1 1-3 1-3 1-3 0-2-1-4 2l1 1 1-1v2c0 1 1 1 0 2h0l-2-1-3 3v1h-1v4 3c0 1 0 2 1 3h1c0 1 1 1 0 1 0 1-1 3-1 4v-1c-1 1-1 1-2 1h0l-1 2 2 2c-1 0-2 1-2 2-4 0-7 1-10-1v-2c3-1 7 0 10-1h0l-1-1h-4l2-2c-2-1-7 0-10 0l-1-1h-2v-1h7l1 1 1-1h2c1 0 1 0 2 1h1l2-2c0-2-2-1-3-2 2 0 2 0 3-1l-1-1h1l1-1h-2c0-1 1-1 1-1v-1h-2l-1-1c2 0 2 0 4-1h-1c-1-1-1-1-2-1h0l1-1c-1 0-1 0-2-1l2-2h-1v-1h1c0-1 0 0 1-1 0-1 1-1 2-1v-2h0c0-1 0-1-1-2l2-1h2c1-2 0-2 0-4l1-2z" class="P"></path><path d="M402 748c1 2 2 3 5 3h2 0l-2 2 1 2h1c-1 1-3 1-3 1-3 0-2-1-4 2l1 1 1-1v2c-1-1-1-1-2-1-2 0-3 3-6 3v3h-1c-1 0-1 0-2-1l2-2h-1v-1h1c0-1 0 0 1-1 0-1 1-1 2-1v-2h0c0-1 0-1-1-2l2-1h2c1-2 0-2 0-4l1-2z" class="g"></path><path d="M408 755h-1c-2-1-4-1-5-2 1-1 3-1 4-1l1 1 1 2z" class="a"></path><path d="M370 778l2-1h0l-1 1 1 1c2-1 1-1 2-3h2 1 1c0-1 0-1 1-2v3h1v1h2l1 1c3 0 8-1 10 0l-2 2h4l1 1h0c-3 1-7 0-10 1v2c3 2 6 1 10 1l2 1c-1 0-1 0-2 1h-3-7v2c-2 0-3-1-5 1v2l-1-1v1h0c0 1 0 1-1 2l-1-1h0l1-6c-1-1-5 0-7 0h-1l-2-1c1-1 6-1 8-1h0-7c-1-2 0-2 0-3s-1-1-1-2c0 0 0-2 1-3z" class="R"></path><path d="M370 778l2-1h0l-1 1 1 1c2-1 1-1 2-3h2 1 1c0-1 0-1 1-2v3h1v1h-1c-1 1-3 1-4 1l-1 1 1 1h2 1 0 1c0 1 0 1-1 2h0c1 0 2 0 3-1v1h1 2v1h-1c0 1-1 1-2 2-2-1-2 0-4 0h-7c-1-2 0-2 0-3s-1-1-1-2c0 0 0-2 1-3z" class="c"></path><path d="M394 683l1-2h0v2h1l1-1c1-1 3-1 4-1h2c1 1 1 2 1 3l1 1-1 1 1 1c-1 1-1 1-2 1l1 1h1v2l1 1 3-1h4 1 0 2c1-1 2-1 3-1l-2 3h3c2-1 6 1 8 1l1 2 2 1-1 2v1 2h0l-1 1-3 7c-1 6-3 13-5 19h-2v-2l-8 1h0-7l-4 1h-3-5-8l-12 1-4 1h-2v-1-11c1-4 1-9 0-12l-2-3c-2-2-4-4-7-6-2-2-7-4-7-7h0c3-1 5-1 8-2v2h3v1c1 1 2 1 3 1 2-1 4-2 7-3-1-1-1-1-3-2l18-1h5c3-1 8-1 11-3h-4c-2 0-3 0-4-1z" class="M"></path><path d="M386 702c2 0 4 0 5 1-1 0-2 0-3 1-2 1-5 1-7 1v1h2v1l-6 1c0-1 0 0-1-1h0l1-1c1-2 0-1 0-3h3l1-1c2 1 3 1 5 0z" class="X"></path><path d="M396 695l2 7v3l-1 1h0v2c-1 1-2 1-4 1v-3l-1-1v-2h-1c-1-1-3-1-5-1h1c2 0 3 0 4-1v-2c2 0 3 0 4-1l-1-1 2-2z" class="g"></path><path d="M396 695l2 7c-1 0-2 1-3 1l-1-1c-1 0-2 0-3-1v-2c2 0 3 0 4-1l-1-1 2-2z" class="Q"></path><path d="M376 714l1-1c1-1 5-4 6-4h6v1c-1 0-2 0-3 1h1 4v1l1-1c0 2 0 2 1 3l1-1v-1c1 0 1-1 2-1h1 3 10 0c1 1 1 2 2 2h3c1 0 3 0 4 1l-13 1c-10 0-20 1-30 0v-1z" class="G"></path><path d="M400 711h6v1h-4c-2 0-1 1-1 2-1 0-2-1-2-1-2 0-2-1-2-2h3z" class="F"></path><path d="M420 698s2 0 2 1l2 2c1 0 0-2 2 0v1 4c-1 2-2 1-2 3 1 1 1 1 2 1-1 6-3 13-5 19h-2v-2-1h0v-2c1-1 1-3 1-5l-3-1v-1h2l1-1c1-1 2 0 3-1v-1c-1 0-1-1-2-2h-1-1c-1-1-3-1-4-1v-1h5v-1h-4v-1c2 0 4 0 7-1l-8-1-1-2h3 0c2 0 2 0 4-1v-2l-1-3z" class="Q"></path><path d="M414 691h0 2c1-1 2-1 3-1l-2 3h3c2-1 6 1 8 1l1 2 2 1-1 2v1 2h0l-1 1-3 7c-1 0-1 0-2-1 0-2 1-1 2-3v-4-1c-2-2-1 0-2 0l-2-2c0-1-2-1-2-1h-2c-3-2-5-1-8-2-2 0-3-1-4-3v-1l3-1h4 1z" class="h"></path><path d="M414 691h0 2c1-1 2-1 3-1l-2 3c-2 0-7 1-8 0 1-1 3-2 5-2z" class="G"></path><path d="M373 714h-1l1-1-1-1h-1l3-1v-1c-1 0 0 0-1-1 1-1 2-1 3-1v2h0v1 1h-1c0 1 1 1 1 2v1c10 1 20 0 30 0l13-1c-1-1-3-1-4-1 1-1 3 0 4-1h1 1c1 1 1 2 2 2v1c-1 1-2 0-3 1l-1 1h-2v1h-1-1-1-4 0-9-3-2v2-1l-1-1h-1l-1 1-1 1v-2h-2-5c-3 0-7 1-10 0v-3l-2-1z" class="j"></path><path d="M419 712h1 1c1 1 1 2 2 2v1c-1 1-2 0-3 1l-1 1h-2v1h-1-1-1-4c-2-4-10 0-14-3h9 1l13-1c-1-1-3-1-4-1 1-1 3 0 4-1z" class="m"></path><path d="M410 718h4 1 1 1l3 1c0 2 0 4-1 5v2h0v1l-8 1h0-7l-4 1h-3-5v-1h-1v-1c1 1 1 0 2 0v-2-6l1-1h1l1 1v1-2h2 3 9 0z" class="M"></path><path d="M401 721l4-1h1v3 2l-1 1c0-2 1-3 0-4-1 1-1 2-1 3h-1c-1 0-1 0-1 1v1c-1-2-1-5-1-6z" class="G"></path><path d="M396 719v1-2h2v1c1 0 1 0 2 1v2c-1 0-2 0-2 2v2l2 3h-3-5v-1h-1v-1c1 1 1 0 2 0v-2-6l1-1h1l1 1z" class="F"></path><path d="M393 719l1-1h1l1 1c-1 2 0 4-2 6h-1v-6z" class="I"></path><path d="M410 718h4 1 1 1l3 1c0 2 0 4-1 5v2h0v1l-8 1h0l-3-2h-1v-3-1l-1 1v-3h-1l-4 1v-3h9 0z" class="o"></path><path d="M401 718h9 0v8h-1c-1-1 0-5 0-7-2 2-1 5-1 7h-1v-3-1l-1 1v-3h-1l-4 1v-3zm-26 0c3 1 7 0 10 0h5 2v2l1-1v6 2c-1 0-1 1-2 0v1h1v1h-8l-12 1-4 1h-2v-1-11h3l3-1h3z" class="L"></path><path d="M390 718h2v2l1-1v6 2c-1 0-1 1-2 0v1h1v1h-8l-12 1-4 1h-2v-1c2 1 9-1 12-1 2-1 4-1 6-1h1c1 0 1-1 3-1h2v-1c1-3 0-5 0-8z" class="O"></path><path d="M374 722c1 0 2-1 3 0 1 0 1 1 2 2 2 0 2-1 4 0v-1h1v5c-2 0-4 0-6 1v-2h-1c-1 1-2 1-3 1l-1-1-1-1 2-4z" class="M"></path><path d="M379 724c2 0 2-1 4 0v-1h1v5c-2 0-4 0-6 1v-2c2 0 3 0 5-1h-1-5l-1-1v-1h3 0z" class="n"></path><path d="M369 719l3-1c1 2 1 3 2 4l-2 4 1 1 1 1c1 0 2 0 3-1h1v2c-3 0-10 2-12 1v-11h3z" class="j"></path><path d="M394 683l1-2h0v2h1l1-1c1-1 3-1 4-1h2c1 1 1 2 1 3l1 1-1 1 1 1c-1 1-1 1-2 1l1 1h1v2l1 1v1c1 2 2 3 4 3 3 1 5 0 8 2h2l1 3v2c-2 1-2 1-4 1h0-3l1 2h-16l-2 2v-2h0l1-1v-3l-2-7c-1-2-1-3-3-5 0-1-1-2-2-3 3-1 8-1 11-3h-4c-2 0-3 0-4-1z" class="V"></path><path d="M405 689v2l1 1v1c-1 1-2 1-3 1s-5 1-6 0v-1h5c0-1 1-1 1-2h1l-1-2h2z" class="l"></path><path d="M408 702h7c1 0 2 1 2 2h-3-4c0-1-1-1-2-2z" class="L"></path><path d="M410 704c-3 1-7 1-10 0h0c2-2 5-2 8-2 1 1 2 1 2 2z" class="G"></path><path d="M410 696c3 1 5 0 8 2h2l1 3h-9l-1-1h4v-1c-1 0-2 0-3-1s-1 0-1-1l-1-1z" class="U"></path><path d="M386 687h5c1 1 2 2 2 3 2 2 2 3 3 5l-2 2 1 1c-1 1-2 1-4 1v2c-1 1-2 1-4 1h-1c-2 1-3 1-5 0l-1 1h-3c0 2 1 1 0 3l-1 1h0c1 1 1 0 1 1h-1c-1 0-2 0-3 1 1 1 0 1 1 1v1l-3 1h1l1 1-1 1h1l2 1v3h-3l-3 1h-3c1-4 1-9 0-12l-2-3c-2-2-4-4-7-6-2-2-7-4-7-7h0c3-1 5-1 8-2v2h3v1c1 1 2 1 3 1 2-1 4-2 7-3-1-1-1-1-3-2l18-1z" class="g"></path><path d="M382 701h5l1-1h0l3-1v2c-1 1-2 1-4 1h-1c-2 1-3 1-5 0l1-1z" class="f"></path><path d="M386 687h5c1 1 2 2 2 3 2 2 2 3 3 5l-2 2-3-3c-2-1-3-2-4-4h-1v-3z" class="h"></path><path d="M386 687h5c1 1 2 2 2 3-1 1-1 2-3 1h-1l-2-1h-1v-3z" class="T"></path><path d="M368 688l18-1v3l-2-1v1l1 2c-3 0-5-1-7-1h-4-3v-1c-1-1-1-1-3-2z" class="P"></path><path d="M371 691c1-1 2-2 3-2l1 1-1 1h-3z" class="h"></path><path d="M364 693l7-1c3 0 7 0 11 1h3c1 0 3 3 5 3v1c0 1 1 1 1 2h-6-6c-1 0-1-1-2-1-3 0-4 0-6-1-2 0-5-1-7-1h-1c-1-1-2-2-2-4 1 1 2 1 3 1z" class="M"></path><path d="M382 693h3c1 0 3 3 5 3v1c0 1 1 1 1 2h-6-6c-1 0-1-1-2-1-3 0-4 0-6-1 5-2 13 0 18 0-2-1-5-2-7-4z" class="o"></path><path d="M350 691c3-1 5-1 8-2v2h3v1c0 2 1 3 2 4h1c2 0 5 1 7 1 2 1 3 1 6 1 1 0 1 1 2 1h6c-2 0-5 0-7 1 1 0 3 1 4 1l-1 1-1 1h-3c0 2 1 1 0 3l-1 1h0c1 1 1 0 1 1h-1c-1 0-2 0-3 1 1 1 0 1 1 1v1l-3 1h1l1 1-1 1h1l2 1v3h-3l-3 1h-3c1-4 1-9 0-12l-2-3c-2-2-4-4-7-6-2-2-7-4-7-7h0z" class="T"></path><path d="M364 696c2 0 5 1 7 1 2 1 3 1 6 1 1 0 1 1 2 1h6c-2 0-5 0-7 1-3 1-6-1-9 0h-1c-1 0-3-3-4-4z" class="a"></path><path d="M364 704h0c0-2-1-3-2-4h-1l1-1c1 1 2 1 2 2h1 2c2 1 1 2 3 1l1 1h2 1l1-1h6l-1 1h-3c0 2 1 1 0 3l-1 1h0c1 1 1 0 1 1h-1c-1 0-2 0-3 1 1 1 0 1 1 1v1l-3 1h1l1 1-1 1h1l2 1v3h-3l-3 1h-3c1-4 1-9 0-12l-2-3z" class="Q"></path><path d="M367 705c2-1 2-1 3-1l1 2v1h-3l-1-2z" class="T"></path><path d="M364 704h0c0-2-1-3-2-4h-1l1-1c1 1 2 1 2 2h1c1 1 1 2 2 3v1l1 2c1 3 0 5 3 7l1 1c1 0 0 0 1-1l2 1v3h-3l-3 1h-3c1-4 1-9 0-12l-2-3z" class="U"></path><path d="M369 719c0-2-1-2 0-4h3c1 0 0 0 1-1l2 1v3h-3l-3 1z" class="h"></path><path d="M685 917c1 2 3 2 5 2 1 0 1-1 2-1h3l1 1h0v1c2 1 4 1 6 2h0 3l2 2c0-1 0-1 1-2 1 0 2 0 4 1h0c-2 1-4 1-6 1v2h-3c-3 1-6 1-9 1l-9 2c2 1 2 2 4 3h0c2 0 4-1 6-1 1 0 0 1 2 1h2 1 1c0 1 1 1 1 2h0v2h-5v1l4 1-1 1v1l5 1c1 0 1 0 1-2l1 1 1 1c-1 1-1 2-1 3l1 1v1h-5l-1 1h0l1 1-1 1v1c2 1 4 1 6 2h2l-2 3 2 1-9 1c-1 0-2 0-2 1h-1c-3 0-5 1-8 1h-7-14l-9 1h-3 0c-1-1-1 0-1-1l-1-1-2 2h-3v-1c-2 1-2 1-3 1l-1-2h-1c-1 1-1 1-2 1-1-1-1-2-2-3l-4 1h-1-1l-8-1c-2-1-3-1-5-1-1-1-2 0-3 0-1-2-1-3-1-4h-2-1v-1h0l-1-1-1-1v-1c3 0 6 0 9-1h-1c-1 0-3-1-4-1-2 0-3 1-5 0l-1-1c0-1-2-2-3-2h0l-1-1 4-1h2c1-1 2-1 3-1 2 0 5 1 7 0v-1h-7-1c-2 0-3-1-5-2 1-1 1-1 2-1l1-1h1 1c1 0 2 0 3-1h6 3l2-2c2 1 4 0 6 0l1 1 1-1 1-1h2c2 0 5 0 8-1h4l5-1h3l1 3h3 1v-1l1-1 2-2c1 0 4-1 6-1 2-1 3-1 5-2v-2h-1l1-1c1-1 3-2 5-2l1-2z" class="F"></path><path d="M637 931l1-1c0 1 0 1 1 2-2 1-3 0-5 1s-5 1-7 1c-1 0-2-1-3-1h3l2-2c2 1 4 0 6 0l1 1 1-1z" class="G"></path><path d="M676 939l-1-1h-2-2c-2 0-2 0-3-1h-2v-1h0 1c2-1 4-1 6-1 1 1 1 2 2 3l2 1h-1z" class="E"></path><path d="M611 944l4-2c1 0 3 0 4 1h5v1c-2 1-3 1-4 1h-8l-1-1z" class="H"></path><path d="M679 935l6-1 1 3-1 1-3 1c-1 0-2-1-3 0h-1-1l-2-1c-1-1-1-2-2-3h6z" class="J"></path><path d="M645 941c3-3 10-1 14-2 1-1 2-1 3 0h0l1-1c1 0 2 1 3 1l-3 1h-2l-1 1v1 2l-5-1c1 0 1-1 1-2h-1-3l-1 1c-2-1-4-1-6-1z" class="G"></path><path d="M645 941c2 0 4 0 6 1l1-1h3 1c0 1 0 2-1 2-1 1-1 1-2 1-6 0-11-1-16-2h-2v-1c1-1 5-1 6 0h4 0z" class="C"></path><path d="M677 939h1 1c1-1 2 0 3 0l7 1-1 1h-3-6-9c-4-1-6-1-10 1v-1l1-1h2l3-1c1 0 2 0 4 1 1-1 1-1 2-1h4 1z" class="d"></path><path d="M614 934h1c1 0 2 0 3-1h6c1 0 2 1 3 1h5v1c-2 0-2 0-4 1l1 1c-1 1-2 1-3 1h-3l8 3c-3 0-8 1-12 0-1-1-3 1-5 0v-1l2-1c2 0 5 1 7 0v-1h-7-1c-2 0-3-1-5-2 1-1 1-1 2-1l1-1h1z" class="H"></path><path d="M621 934c2 0 3 0 4 1 0 1 0 2-1 2h-6 1c1 0 2 0 3-1l-1-2z" class="E"></path><path d="M614 934h3 4l1 2c-1 1-2 1-3 1h-1l-3 1c-2 0-3-1-5-2 1-1 1-1 2-1l1-1h1z" class="B"></path><path d="M614 934h3l-2 2h-2l-1-1 1-1h1z" class="D"></path><path d="M657 928h3l1 3-1 2-8 1h-1c-3 1-5 1-8 1h-2l-4-1v-1l2-1 1-2c2 0 5 0 8-1h4l5-1z" class="I"></path><path d="M648 932l4-2c1 1 0 1 1 2l-1 1c-2 0-2 0-4-1z" class="E"></path><path d="M653 932v1c-1 1-1 1-2 1h0c-3 1-5 1-8 1 1-1 1-1 2-1h1c0-1 0-1 1-2v1l1-1c2 1 2 1 4 1l1-1z" class="H"></path><path d="M689 932c2 0 4-1 6-1 1 0 0 1 2 1h2 1 1c0 1 1 1 1 2h0v2h-5v1l4 1-1 1v1l-1 1-1 1-2 2-2 1-2 2h-1v-1h-5l-1-5h3l1-1-7-1 3-1 1-1-1-3 5 1h0l1-1c-2 0-2 0-3-1l1-1z" class="G"></path><path d="M685 934l5 1 2 1c-1 1-1 1-2 1-1 1-3 1-5 1l1-1-1-3z" class="E"></path><path d="M690 937l1 1h0c2-1 4-1 5 0s2 1 2 3c-1-1-1-1-2-1-3 0-4-1-7 0l-7-1 3-1c2 0 4 0 5-1z" class="I"></path><path d="M689 940c3-1 4 0 7 0 1 0 1 0 2 1h1l-1 1-2 2-2 1-2 2h-1v-1h-5l-1-5h3l1-1z" class="C"></path><path d="M689 940c3-1 4 0 7 0 1 0 1 0 2 1h1l-1 1h-5c-1 0-1 1-1 1s0-1-1-1c-1-1-2-1-3-1l1-1z" class="M"></path><path d="M660 942c4-2 6-2 10-1h9 6l1 5h5v1c-5 0-10-1-15 1h-1 0c-2 1-2 1-2 2-2 0-2-1-3-1l-2 1c-2 1-5 0-8 0h-3l-1-1h0c1-1 1-2 1-3h-5c-3-1-6-2-9-2-1 1-3 1-4 0h-3 0l-1-1h0l2-1c5 1 10 2 16 2 1 0 1 0 2-1l5 1v-2z" class="Z"></path><path d="M657 946c2 0 4 1 6 1h3v1l-10 1h0c1-1 1-2 1-3zm3-4c4-2 6-2 10-1h0c-1 1-2 1-3 2l-1 1c1 0 2 1 4 1h2c-4 1-8 0-12-1v-2z" class="K"></path><path d="M670 941h9 6l1 5c-3-1-5-1-8 0-2 0-4 0-6-1h-2c-2 0-3-1-4-1l1-1c1-1 2-1 3-2h0z" class="E"></path><path d="M683 942c1 0 1 0 2 1v1h-2l-1-1 1-1z" class="B"></path><path d="M670 945c2-1 4-1 6-1l2 2c-2 0-4 0-6-1h-2z" class="C"></path><path d="M685 917c1 2 3 2 5 2 1 0 1-1 2-1h3l1 1h0v1c2 1 4 1 6 2h0 3l2 2c0-1 0-1 1-2 1 0 2 0 4 1h0c-2 1-4 1-6 1v2h-3c-3 1-6 1-9 1l-9 2c2 1 2 2 4 3h0l-1 1c1 1 1 1 3 1l-1 1h0l-5-1-6 1-5-1c-4 1-7 0-10 0-3 1-9 1-12 0l8-1 1-2h3 1v-1l1-1 2-2c1 0 4-1 6-1 2-1 3-1 5-2v-2h-1l1-1c1-1 3-2 5-2l1-2z" class="Y"></path><path d="M666 929l3 1 1-1h0v2h-1c-3 0-6 1-9 2l1-2h3 1v-1l1-1z" class="O"></path><path d="M696 919v1c2 1 4 1 6 2l-6 1c-1 0-2 0-3-1l-2 1-1-1c1-2 4-2 6-3z" class="C"></path><path d="M664 934l12-3c1 1 2 1 4 1-3 1-4 1-6 2-4 1-7 0-10 0z" class="G"></path><path d="M676 931c3 0 6-1 9-2 2 1 2 2 4 3h0l-1 1c-3 0-5 0-8-1-2 0-3 0-4-1z" class="C"></path><path d="M688 933c1 1 1 1 3 1l-1 1h0l-5-1-6 1-5-1c2-1 3-1 6-2 3 1 5 1 8 1z" class="Y"></path><path d="M679 922c3 2 6 1 9 1h3c1 0 2 1 3 2h-2c-1 0-1 1-2 1s-2-1-3-1h-1l-1-1-1 1h-3c-3 1-5 1-7 1 2-1 3-1 5-2v-2z" class="V"></path><path d="M685 917c1 2 3 2 5 2 1 0 1-1 2-1h3l1 1h0c-2 1-5 1-6 3l1 1h-3c-3 0-6 1-9-1h-1l1-1c1-1 3-2 5-2l1-2z" class="Y"></path><path d="M684 919c0 1 1 1 1 2-1 1-2 1-3 1l-1-1h-2c1-1 3-2 5-2z" class="V"></path><path d="M681 925l2 2h0c2-1 3-1 4 0-2 1-6 2-9 2-1 1-3 1-4 2h-1c-1-1-2 0-3 0v-2h0l-1 1-3-1 2-2c1 0 4-1 6-1s4 0 7-1z" class="I"></path><path d="M652 946h5c0 1 0 2-1 3h0l1 1h3l-3 1-1 1c0 1-1 1-2 1v2h0c1 1 2 1 2 2l2 1h1l2 1h0l-1 1h-3 0c-1-1-1 0-1-1l-1-1-2 2h-3v-1c-2 1-2 1-3 1l-1-2h-1c-1 1-1 1-2 1-1-1-1-2-2-3l-4 1h-1-1l-8-1c-2-1-3-1-5-1-1-1-2 0-3 0-1-2-1-3-1-4h-2-1v-1h0l-1-1-1-1v-1c3 0 6 0 9-1 1 1 1 1 2 0h7 6c0 1 1 1 2 1 3-1 6-1 10-1h3z" class="G"></path><path d="M627 952h5 1l-3 2h-1l-2 2c-2-1-3-1-5-1 1 0 2 0 3-1 2 0 2-1 2-2z" class="C"></path><path d="M654 953h0c-2-1-4 0-6-1-1 0-2 1-3 1v-1c2-1 4-1 6-1h6l-1 1c0 1-1 1-2 1z" class="K"></path><path d="M622 950c1-1 1-1 1-2h5c1-1 2-1 3-1l2 2h-1c-1 0-1 0-2 1h-2-6z" class="F"></path><path d="M618 951l9 1c0 1 0 2-2 2-1 1-2 1-3 1-1-1-2 0-3 0-1-2-1-3-1-4z" class="E"></path><path d="M631 946h6c0 1 1 1 2 1 3-1 6-1 10-1h-1c-1 1-4 1-5 1l-1 1v1l1 1c-1 1-3 1-5 1l3-2v-1h-2c-2 1-3 1-6 1h0l-2-2v-1z" class="S"></path><path d="M622 946c1 1 1 1 2 0h7v1c-1 0-2 0-3 1h-5c0 1 0 1-1 2-1 0-4-1-5 0h-1v1h-1v-1h0l-1-1-1-1v-1c3 0 6 0 9-1z" class="K"></path><path d="M652 946h5c0 1 0 2-1 3l-13 1-1-1v-1l1-1c1 0 4 0 5-1h1 3z" class="B"></path><path d="M627 956l2-2h1c2 0 3 1 5 0 2 0 3 0 5 1h6s1 0 2 1h1 0c1 1 2 1 4 1h1 2l2 1h1l2 1h0l-1 1h-3 0c-1-1-1 0-1-1l-1-1-2 2h-3v-1c-2 1-2 1-3 1l-1-2h-1c-1 1-1 1-2 1-1-1-1-2-2-3l-4 1h-1-1l-8-1z" class="K"></path><path d="M700 940l5 1c1 0 1 0 1-2l1 1 1 1c-1 1-1 2-1 3l1 1v1h-5l-1 1h0l1 1-1 1v1c2 1 4 1 6 2h2l-2 3 2 1-9 1c-1 0-2 0-2 1h-1c-3 0-5 1-8 1h-7-14l-9 1 1-1h0l-2-1h-1l-2-1c0-1-1-1-2-2h0v-2c1 0 2 0 2-1l1-1 3-1c3 0 6 1 8 0l2-1c1 0 1 1 3 1 0-1 0-1 2-2h0 1c5-2 10-1 15-1h1l2-2 2-1 2-2 1-1 1-1z" class="l"></path><path d="M708 952h2l-2 3c-3 0-6 0-8-1l3-1h5v-1z" class="F"></path><path d="M669 959s1 0 1-1c7-2 15-4 23 0l-3 1h-7-14zm23-10l6-1c2 0 3 0 4 1v1c2 1 4 1 6 2v1h-5l-3 1-2 1c-1 0-2 0-3-1-3 0-6-1-9-2l1-2c1 0 3-1 4-1h1z" class="J"></path><path d="M692 949l6-1c2 0 3 0 4 1v1c2 1 4 1 6 2v1h-5v-1l-1-1h0c-3-2-7 1-10-2z" class="E"></path><path d="M660 950c3 0 6 1 8 0l9 3c1 0 2 0 2 1-2 1-8 3-11 3-2 0-3 1-4 1-2 0-4-1-5-1h-1v1l-2-1c0-1-1-1-2-2h0v-2c1 0 2 0 2-1l1-1 3-1z" class="F"></path><path d="M660 950c3 0 6 1 8 0l9 3c-3 2-9 2-13 1-2 0-3 0-5-1l-3-1 1-1 3-1z" class="B"></path><path d="M700 940l5 1c1 0 1 0 1-2l1 1 1 1c-1 1-1 2-1 3l1 1v1h-5l-1 1h0l1 1-1 1c-1-1-2-1-4-1l-6 1h-1c-1 0-3 1-4 1l-1 2-11-4h0 1c5-2 10-1 15-1h1l2-2 2-1 2-2 1-1 1-1z" class="G"></path><path d="M700 940l5 1c1 0 1 0 1-2l1 1 1 1c-1 1-1 2-1 3h-3l1-1 1-1c-2 0-4 1-6 1v2h-1c-1 0-2 0-3 1h3v1h-2c-2 0-2 0-3 1v-1l1-1-1-1 2-1 2-2 1-1 1-1z" class="K"></path><path d="M691 947h1v1h-6-2l-1 1c1 1 6 0 8 0-1 0-3 1-4 1l-1 2-11-4h0 1c5-2 10-1 15-1z" class="I"></path><path d="M396 616l1-2c4-1 9-1 14 0 2 0 3 0 5 1l6 2v16c0 1 0 2-1 3h1v8 21c2 0 6-3 8-4l15-9 9-6c2-1 3-3 5-3h0c-3 5-9 8-14 11-9 5-18 10-28 15l-10 4v7h-1s0-1-1-1c0 1 0 1-2 2h-2c-1 0-3 0-4 1l-1 1h-1v-2h0l-1 2h-1v-4l-1-1v-1l-2-1-2 1h-1-2l-1-1h-1c0-2 0-2 2-4h-2c-1 0 0 0-1-1h1l-1-1c-1 0-2 0-3-1v-1l-1-1v-4h2 1l-3-2 1-1h2c1 0 1-1 2-1l-1-2c-1-1-3-1-5-2 1-3 1-5 0-8v-1c0-1 0-1 1-2v-1c-1 0 0 0-1-1l1-1c0-1-1-3-1-5l1-1h-2v-1l-1-2c1-1 1-1 1-2l-1-1c0-1 0-1 1-1v-1h0l1-2v-1h-1c0-1 1-1 1-1l1-1h0-1c0-1 2-2 3-2 2 0 4-1 6-1v-2c1 0 2-1 3-2h0l1-2c2 1 4 0 6 1v2z" class="W"></path><path d="M404 637c1 1 0 4 0 5v-5z" class="E"></path><path d="M385 630c3-1 10-1 13 0v1c-3 1-9 1-13 0v-1z" class="h"></path><path d="M386 662c3-1 6-1 8-1l1 1h3l1 1c-1 1-1 2-2 2-2 1-4 0-6 1h-4-1c-2 2-3 1-5 2h-1v1c1 0 1 0 2 1-1 0-2 0-3-1v-1l-1-1v-4h2 1l2 2c1 0 2 0 3-1v-2h0z" class="B"></path><path d="M385 672c3 1 6 1 10 1h11 1v7h-1s0-1-1-1c0 1 0 1-2 2h-2c-1 0-3 0-4 1l-1 1h-1v-2h0l-1 2h-1v-4l-1-1v-1l-2-1-2 1h-1-2l-1-1h-1c0-2 0-2 2-4z" class="G"></path><path d="M406 673h1v7h-1s0-1-1-1c0 1 0 1-2 2h-2c1-2 1-2 1-4l-1-1c0-1 0-1 1-2l4-1z" class="l"></path><path d="M401 676c2-1 2-1 4 0h0v3c0 1 0 1-2 2h-2c1-2 1-2 1-4l-1-1z" class="L"></path><path d="M385 672c3 1 6 1 10 1-1 1-1 2-1 3l1 1c1-1 1-1 1-2v1c1 0 2 0 3 1h1c-2 3-3-1-3 5l-1 1h-1v-2h0l-1 2h-1v-4l-1-1v-1l-2-1-2 1h-1-2l-1-1h-1c0-2 0-2 2-4z" class="K"></path><path d="M384 676l1-1h1c1 0 1 1 1 2h-2l-1-1z" class="L"></path><path d="M389 659h0c1-7 0-14 0-20-1-1-2-1-3-2h1 11 0c-1 7-1 15-1 23l1 2h-3l-1-1c-2 0-5 0-8 1l1-1 2-2z" class="T"></path><path d="M389 659c1 1 1 1 3 1v-5c1 1 1 3 1 4 1 1 3 1 4 1l1 2h-3l-1-1c-2 0-5 0-8 1l1-1 2-2z" class="D"></path><path d="M396 616l1-2c4-1 9-1 14 0 2 0 3 0 5 1l6 2v16h0c-2-1-5-1-8-3h1l2 1h3l1-1-3-1c1-1 2 0 3 0-2-3-5-3-8-4h0c2-1 5 0 7 0v1-1l2-2-2-1c-7-3-13-4-20-5h-3v11h-8v1c-1-1-2-1-3-2v-8-2c1 0 2-1 3-2h0l1-2c2 1 4 0 6 1v2z" class="G"></path><path d="M400 617c6-2 9 1 15 1 1 0 3 0 4 1h1v3c-7-3-13-4-20-5z" class="F"></path><path d="M389 615l1-2c2 1 4 0 6 1v2h0v11l-7 1h0v1c-1-1-2-1-3-2v-8-2c1 0 2-1 3-2h0z" class="T"></path><path d="M389 615c1 4 1 9 0 13h0v1c-1-1-2-1-3-2v-8-2c1 0 2-1 3-2z" class="B"></path><path d="M596 590h5l-2 2c1 1 3 1 5 1l8-1c6 0 11-1 16 0 4 0 8 2 12 2l-1 50v10l-1 1h0v1c-8-2-16-2-24-3l-16 1h-3c-3 0-6 1-10 1v-1c1-4 0-8 0-12v-33c1 0 1 0 2 1 0 1-1 1 1 2v2l1 1c1 0 2-1 3-1l2-1v-7l-1-4 1-8v3c1-2 0-1 0-2s1-2 1-2c1-1 1-2 1-3h0z" class="W"></path><path d="M637 649h0c0-2 0-4 1-5h1v10l-1 1 1-1c-2 1-3 1-5 0h0-4v-3c0 1 0 1 1 2h1v-3c1-1 2-1 5-1z" class="J"></path><path d="M637 649h0c0-2 0-4 1-5h1v10l-1 1 1-1c0-1 0-3-1-4h0v1l-1 2h-1c0-1 0-3 1-4z" class="D"></path><path d="M609 631h9c-1 1-2 1-3 2-1 0-2 0-3 1h1 0 2c-1 1-2 1-3 1h0-2l-1 1h3 0 1 1c3 1 4 1 6 3h0l-1 1h-3c-1-2-4 0-6-2-1 0-1 0-2-1-1 0-2 0-2-1l3-3h1c-1 0-1-1-1-2z" class="B"></path><defs><linearGradient id="H" x1="633.059" y1="617.838" x2="628.966" y2="618.771" xlink:href="#B"><stop offset="0" stop-color="#e2dee3"></stop><stop offset="1" stop-color="#eaede7"></stop></linearGradient></defs><path fill="url(#H)" d="M627 606h5c2 1 2 1 4 0v1l-1 1h-1c-1 2 0 4 0 5v12 2s0 1 1 1v2l-2-1c-2-1-1 0-3-2l1-17s1-1 1-2c-1-2-3 0-5-2z"></path><path d="M604 593l8-1-3 1c0 1-1 1 0 3h1l-1 1v1c2 1 4 1 6 1s5 0 7 1l-1 2h-1c-1 1 0 14-1 17v-17l-12 1c-2-1-2 0-3-2 1-1 2-1 4-2h-4l-1-1c2 0 3 0 4-1-1-1-4-1-5-1l2-3z" class="S"></path><path d="M620 639l3 1c0 2-1 1 1 3l1-1h0l1 1c0 1 0 1 1 2 0 2-1 3 0 4h1c1 0 2 1 3 1v1h-1 0v3h4 0c2 1 3 1 5 0l-1 1h0v1c-8-2-16-2-24-3h0v-1h-2l-1-2c1-1 1-2 1-4l1 1 2-1c0-2-1-2-2-3l-1-2c1-1 1-1 2-1h2 3l1-1z" class="E"></path><path d="M614 640h2 3l-1 2c0 1-1 1-1 2v1h-1v-1c0-2-1-2-2-3v-1z" class="N"></path><path d="M612 646l1 1c1 0 2 0 2 1v4h-3l-1-2c1-1 1-2 1-4z" class="I"></path><path d="M615 652h3 1c1-1 1-2 1-3v-1c0 2 0 3 2 4h3c0-1 1-1 1-2h0v2c2 1 3 1 4 2h4 0c2 1 3 1 5 0l-1 1h0v1c-8-2-16-2-24-3h0v-1h-2 3z" class="j"></path><path d="M607 603l12-1v17 7c-2 1-8 0-12 0l1-13c0-2 0-5-1-7v-1-2z" class="T"></path><path d="M596 590h5l-2 2c1 1 3 1 5 1l-2 3c1 0 4 0 5 1-1 1-2 1-4 1l1 1h4c-2 1-3 1-4 2 1 2 1 1 3 2v2 1c1 2 1 5 1 7l-1 13c4 0 10 1 12 0v1l1 1 2 1h0c-2 2-2 2-4 2h-9c0 1 0 2 1 2h-1l-3 3c-2 0-3 0-4 1-1 0-1-1-2-1h0-5l1-1-3-1 1-2-1-1 1-1h1l4-1h0-5v-1l5-1-3-1h0c1-1 2-1 2-2-1 0-1 0-3 1l-1-1c2-2 2-1 4-2h-2c0-1 1-1 2-2h-1l1-2c-2-1-3-1-5-2h0l-1-1h-3 0c1 0 2-1 3-1l2-1v-7l-1-4 1-8v3c1-2 0-1 0-2s1-2 1-2c1-1 1-2 1-3h0z" class="O"></path><path d="M595 636l1-1-3-1 1-2-1-1 1-1h1l1 1h3c0 1-1 1-2 2l1 1c1-1 2-1 3-2h1c-1 1-1 1-1 2 0 0 1 0 1 1h0l-2 1h-5z" class="b"></path><path d="M609 631h-2v-1c2-2 5-1 7-1h8c-2 2-2 2-4 2h-9z" class="k"></path><path d="M607 605v1c1 2 1 5 1 7l-1 13c4 0 10 1 12 0v1l1 1 2 1h0-8v-1h-7v-1c-1 1-1 1-2 1-1 1-2 1-2 2l-1 1h-1c0-1 0-1 1-2h0c1-1 1-1 2-1 0-1 1-1 1-2h1v-1l-1-2c0-1 0-2 1-3h1c-1-1-1-1-1-2v-3c-1-1-1 0-1-2 1 0 1 0 1-1h-1l2-2v-5z" class="Z"></path><path d="M607 627c3 0 9-1 12 0l1 1 2 1h0-8v-1h-7v-1z" class="S"></path><path d="M594 606v2h1c0-1 0-2 1-3h1v2l-1 1h1c1 1 1 0 1 1h0c1 1 3 1 4 1h4 1l-2 2h1c0 1 0 1-1 1 0 2 0 1 1 2v3c0-1-1-2-1-3l-7 1h-5 0l-1-1h-3 0c1 0 2-1 3-1l2-1v-7z" class="L"></path><path d="M602 610h4 1l-2 2h1c0 1 0 1-1 1h-1c-1 0-2-1-3-1h0-1l2-2z" class="F"></path><path d="M594 606v2h1c0-1 0-2 1-3h1v2l-1 1h1c1 1 1 0 1 1h0l-2 1c1 1 1 1 2 1 0 1-1 1-2 1v1h1l-1 2c1 0 1 1 2 1h-5 0l-1-1h-3 0c1 0 2-1 3-1l2-1v-7z" class="U"></path><path d="M596 590h5l-2 2c1 1 3 1 5 1l-2 3c1 0 4 0 5 1-1 1-2 1-4 1l1 1h4c-2 1-3 1-4 2 1 2 1 1 3 2v2 5h-1-4c-1 0-3 0-4-1h0c0-1 0 0-1-1h-1l1-1v-2h-1c-1 1-1 2-1 3h-1v-2l-1-4 1-8v3c1-2 0-1 0-2s1-2 1-2c1-1 1-2 1-3h0z" class="G"></path><path d="M598 609h1c2 0 3-1 4-2l3 1v1 1h-4c-1 0-3 0-4-1h0z" class="O"></path><path d="M607 605c-1 1-2 1-3 1-3-1-1-1-2-3 0 0-1 0-2-1l4-1c1 2 1 1 3 2v2z" class="F"></path><path d="M596 590h5l-2 2-2 1c1 1 0 1 1 1l-1 2 1 1-1 1v6 1h0-1c-1 1-1 2-1 3h-1v-2l-1-4 1-8v3c1-2 0-1 0-2s1-2 1-2c1-1 1-2 1-3h0z" class="g"></path><path d="M585 609c1 0 1 0 2 1 0 1-1 1 1 2v2l1 1h0 3l1 1h0c2 1 3 1 5 2l-1 2h1c-1 1-2 1-2 2h2c-2 1-2 0-4 2l1 1c2-1 2-1 3-1 0 1-1 1-2 2h0l3 1-5 1v1h5 0l-4 1h-1l-1 1 1 1-1 2 3 1-1 1h5 0c1 0 1 1 2 1 1-1 2-1 4-1 0 1 1 1 2 1 1 1 1 1 2 1 2 2 5 0 6 2h-2c-1 0-1 0-2 1l1 2c1 1 2 1 2 3l-2 1-1-1c0 2 0 3-1 4l1 2h2v1h0l-16 1h-3c-3 0-6 1-10 1v-1c1-4 0-8 0-12v-33z" class="a"></path><path d="M585 654l5-1c1-1 3 0 4 0s1 0 1 1c-3 0-6 1-10 1v-1z" class="P"></path><path d="M589 615h3l1 1c-2 0-4-1-6 1l2 2c1-1 1-1 3-1 0 1 0 0 1 1h2c-1 1-2 1-3 1l1 2-1 1v1c-1 0-3 0-5 1v-2h4c-1-2-3 0-4-2v-3-3h2z" class="m"></path><path d="M596 639h4c-1 1-2 1-3 1 0 1 0 1 1 1 0 1-1 1-1 3s1 4-1 6h1l2 2-1 1h-2c-1 0 0 0-2-1l1-2s-1 0-1-1 0-2 1-3c0-2 0-2-1-3 1-1 2-3 2-4h0z" class="X"></path><path d="M595 636h5 0c1 0 1 1 2 1h3l-1 1h-3v1h2v1 1 3h1c0-1 0-1 1-2v3c-2 0-2 1-3 2s-1 2-1 3h-1v1l-1 1-2-2h-1c2-2 1-4 1-6s1-2 1-3c-1 0-1 0-1-1 1 0 2 0 3-1h-4-1v-1l3-1h-4l1-1z" class="L"></path><path d="M599 643c1 0 0 0 1-1v-1h3v3c-1 0-1 0-2 1l-2-2z" class="M"></path><path d="M603 644h1c0-1 0-1 1-2v3c-2 0-2 1-3 2s-1 2-1 3h-1v1c-1-1-1-1 0-2 0-2-1-4-1-6l2 2c1-1 1-1 2-1z" class="b"></path><path d="M602 637c1-1 2-1 4-1 0 1 1 1 2 1 1 1 1 1 2 1 2 2 5 0 6 2h-2c-1 0-1 0-2 1l1 2c1 1 2 1 2 3l-2 1-1-1c0 2 0 3-1 4l-2 2h0l-3-1c-1 1-2 0-3 0-2 0-2 0-2-1s0-2 1-3 1-2 3-2v-3c-1 1-1 1-1 2h-1v-3-1-1h-2v-1h3l1-1h-3z" class="G"></path><path d="M601 650c0-1 0-2 1-3s1-2 3-2v3l-1 1 2 2c-1 1-2 0-3 0-2 0-2 0-2-1z" class="L"></path><path d="M608 642h2c1 1 2 2 2 3v1h0c0 2 0 3-1 4l-2 2h0c-2-3-1-6-1-9v-1z" class="O"></path><path d="M602 637c1-1 2-1 4-1 0 1 1 1 2 1 1 1 1 1 2 1 2 2 5 0 6 2h-2c-1 0-1 0-2 1l1 2c1 1 2 1 2 3l-2 1-1-1h0v-1c0-1-1-2-2-3h-2l1-1 1-1h0l-4-1-2 1c2 1 2 0 2 1v2l-1-1c-1 1-1 1-1 2h-1v-3-1-1h-2v-1h3l1-1h-3z" class="S"></path><path d="M486 620v-1-1c-1-1-1 0-2 0l-1-1 1-1h1c2 2 5 1 8 2h2 2c4 0 8 1 11 1 1 0 3 0 4-1l2 1-2 1v1c0 1-1 1-1 1h0-1c-2 1 1-1-1 1-1 1-5 1-7 1l1 2-1 2 1 1h-1l-1-1c0 5 0 11-1 16l-2-1c0 6-2 12-4 17 1 0 3 1 3 2v1 5 1c-1 1-1 1-1 2v1c1 0 1 0 2 2l-1 3 1 1v1 2c-5 1-13 1-18 1h-22-4v1h5l-1 1h0c-4 0-7 1-11 1-7 1-14 1-21 3l-2-1-1 1c1 1 3 1 5 1-2 2-3 1-5 2l-4-1c-1 0-2 0-3 1h-2 0-1-4l-3 1-1-1v-2h-1l-1-1c1 0 1 0 2-1l-1-1 1-1-1-1c0-1 0-2-1-3 2-1 2-1 2-2 1 0 1 1 1 1h1v-7l10-4c10-5 19-10 28-15 5-3 11-6 14-11h0c0-1 1-1 1-2 1-2 1-11 1-13v-2h5l1 1h0l2 1 1-1c1-1 2-1 3-2v-1h1v-1-1c3-2 7-1 10-2h2z" class="k"></path><path d="M472 631h1c1-1 2-1 3-2v5c-1 1-1 2-2 3-1-1-1-4-2-5v-1z" class="Q"></path><path d="M460 656c2-1 3-3 4-5 0-1 1-1 1-2l1 1c-1 4-3 7-5 11-1 2-2 5-4 7-2 0-2 0-3-1 3-3 5-7 6-11z" class="H"></path><path d="M469 628l1-1c1-1 2-1 3-2v-1h1c0 2-1 5-2 7v1c0 2-2 5-2 8-1 2-2 4-2 6 0 1-1 3-2 4l-1-1c0 1-1 1-1 2-1 2-2 4-4 5l1-5v-2-7l-2 1h0c0-1 1-1 1-2 1-2 1-11 1-13v-2h5l1 1h0l2 1z" class="G"></path><path d="M465 634h3l-2 5-1-3v-2z" class="f"></path><path d="M467 627l2 1c0 2 0 4-1 6h-3v-1c0-1 0-2 1-3h1c-1-1-1-2 0-3zm-2 9l1 3c-1 4-3 8-5 12v-2-1c1-1 3-7 3-9l-1-1 2-2z" class="k"></path><path d="M461 626h5l1 1h0c-1 1-1 2 0 3h-1c-1 1-1 2-1 3v1 2l-2 2 1 1c0 2-2 8-3 9v1-7l-2 1h0c0-1 1-1 1-2 1-2 1-11 1-13v-2z" class="X"></path><path d="M486 620h2c0 2-1 4-1 5v1l1 3h-2v5c-1 2 1 8 0 9h-1c-3 0-7-1-10 0l-1-1 2-1c-1 0 0 0-1-1l2-1h0c0-1-1-1-1-1v-1h1l-1-1c1-1 1-1 2-1h0l-1-1h-1v-5c-1 1-2 1-3 2h-1c1-2 2-5 2-7v-1-1c3-2 7-1 10-2h2z" class="I"></path><path d="M480 637h4v1h-4v-1z" class="C"></path><path d="M474 623l2 1v4h1l1-3 1-1 1-1v1c1 1 0 1 1 2h-1l-1 1c1 1 1 1 2 1h0c-2 1-3 1-3 2l-1 1 1 2c-1 0-1 0-1 1h-1v-5c-1 1-2 1-3 2h-1c1-2 2-5 2-7v-1z" class="e"></path><path d="M486 620h2c0 2-1 4-1 5v1l1 3h-2c1-2 1-5 0-8h0c-1 0-2 1-3 2h0-2-1l-1 1-1 1-1 3h-1v-4l-2-1v-1c3-2 7-1 10-2h2z" class="h"></path><path d="M486 629h2l-1 27c0 3 1 7 0 10h-1l-1 1v4h0-2-1l-1-1-1 1h-1v-3h-1v2l-1 1v-3h-1l-1 1-1-1h0l-1 2h-1v-7l3-3-1-1h-1v-1l3-1-1-1-3-1h-1v-1c1 0 2 0 3-1h0-3v-1h3l3-1v-1h-5c1-1 2-2 2-3h-2v-1h3l-1-1h-1l-1-1h2 1v-1c3-1 7 0 10 0h1c1-1-1-7 0-9v-5z" class="O"></path><path d="M479 644h7v1c-3 1-5 0-7 0v-1z" class="I"></path><path d="M484 649h-3-4c1-1 2-1 3-1 2-1 3-1 6-1v2h-2z" class="H"></path><path d="M480 659h6v4h-2l-1 1h2l-1 1h-1c-2-1-4 0-6-1l-1-1 2-1c0-1 1-2 2-3z" class="K"></path><path d="M484 649h2c0 1 1 2 0 4v2c-2 0-5 1-7 0h-2v-1l1-1h1c-1-1-2-1-2-1h-1-2l3-1c1 0 2 0 3-1 2 0 3 0 4-1z" class="C"></path><path d="M472 655l3 1 1 1-3 1v1h1l1 1-3 3v7h1l1-2h0l1 1 1-1h1v3l1-1v-2h1v3h1l1-1 1 1h1 2 0v-4l2 1v4l1 1c1 0 5 0 6-1v-2c-1 0-2 1-3 1v-3l1-3c1-2 2-3 2-5 1 0 3 1 3 2v1 5 1c-1 1-1 1-1 2v1c1 0 1 0 2 2l-1 3 1 1v1 2c-5 1-13 1-18 1h-22c0-2-1-9 1-10v1l1 8h0c1-3 0-6 0-9v-3c-1-1-1-1-1-2 1 0 3-1 4-2l1-1c-1 0-1 0-1-1l1-1c1-1 1-2 2-2h3c-1-1-2-1-3-1l-1-1c3 0 6 0 8-1 0-1 0-1-1-2z" class="O"></path><path d="M490 673c1 0 1 0 2 1 0 0 1 1 0 1v4c0 1 0 1-1 2v-3h1c-1-1-2-1-3-2v-1l1-2z" class="H"></path><path d="M494 660c1 0 3 1 3 2v1 5 1c-1 1-1 1-1 2-1-1 0-3-1-4-2 1-2 1-4 1l1-3c1-2 2-3 2-5z" class="C"></path><path d="M472 655l3 1 1 1-3 1v1h1l1 1-3 3v7h1l1-2h0l1 1 1-1h1v3l1-1v-2h1v3h1l1-1 1 1h1 2 0v-4l2 1v4c-1 0-2 1-3 0h-4-9-4c0 3 1 6-1 8v-7-1l-1 1v6l-1 1c0-3 0-6 1-8h-2v2c0 2 0 5-1 6v-5-3h-2v-3c-1-1-1-1-1-2 1 0 3-1 4-2l1-1c-1 0-1 0-1-1l1-1c1-1 1-2 2-2h3c-1-1-2-1-3-1l-1-1c3 0 6 0 8-1 0-1 0-1-1-2z" class="R"></path><path d="M463 665h6 1 1 0l1 1-1 1v3h0c-1-1-1-2-1-4l-1 1v2h-1v-1l-1 1h-1v-1c-1 2-1 3-1 4h-2v2c0 2 0 5-1 6v-5-3h-2v-3c-1-1-1-1-1-2 1 0 3-1 4-2z" class="o"></path><path d="M486 620v-1-1c-1-1-1 0-2 0l-1-1 1-1h1c2 2 5 1 8 2h2 2c4 0 8 1 11 1 1 0 3 0 4-1l2 1-2 1v1c0 1-1 1-1 1h0-1c-2 1 1-1-1 1-1 1-5 1-7 1l1 2-1 2 1 1h-1l-1-1c0 5 0 11-1 16l-2-1c0 6-2 12-4 17 0 2-1 3-2 5l-1 3v3c1 0 2-1 3-1v2c-1 1-5 1-6 1l-1-1v-4l-2-1 1-1h1c1-3 0-7 0-10l1-27-1-3v-1c0-1 1-3 1-5h-2z" class="X"></path><path d="M496 633v1 5h-3v1c-1-2-1-4-1-6h3l1-1z" class="I"></path><path d="M498 643c1-6 1-12 3-18v3c0 5 0 11-1 16l-2-1z" class="C"></path><path d="M487 656c1-1 2-1 2-2l1-1v2l1 2c0 3 0 6-1 9h-1-2c1-3 0-7 0-10z" class="B"></path><path d="M491 640h4l-1 1v6c-1 3-1 6-2 8l-1 1v1l-1-2v-5c1-4 1-7 1-10z" class="I"></path><path d="M497 618c4 0 8 1 11 1 1 0 3 0 4-1l2 1-2 1v1c0 1-1 1-1 1h0-1c-2 1 1-1-1 1-1 1-5 1-7 1l1-4c-3 1-4 3-5 6l-1 3-1 1h0c0-1 0-2 1-3-1-3 0-6 0-9z" class="f"></path><path d="M503 620c2-1 4-1 7-1l1 1v2h-1c-2 1 1-1-1 1-1 1-5 1-7 1l1-4z" class="E"></path><path d="M486 620v-1-1c-1-1-1 0-2 0l-1-1 1-1h1c2 2 5 1 8 2h2 2c0 3-1 6 0 9l-4 1c0-1 0-1 1-2-1 0-2 1-3 2h0c-1-1-1-2-2-3h-2c0-1 1-3 1-5h-2z" class="F"></path><path d="M487 625h2c1 1 1 2 2 3h0l1 2c-1 1-1 1-1 2v8c0 3 0 6-1 10v5-2l-1 1c0 1-1 1-2 2l1-27-1-3v-1z" class="E"></path><path d="M487 625h2c1 1 1 2 2 3v1h-1c-2 0-2-1-3-3v-1z" class="J"></path><path d="M459 643l2-1v7 2l-1 5c-1 4-3 8-6 11v1c-1 0-2 1-4 1l1-1-1-1-1 1v1c1 1 1 1 3 1h1v1l-2 1v9l1 1c1-2 1-7 1-9v1c0 2 0 5 1 7v1 1h5l-1 1h0c-4 0-7 1-11 1-7 1-14 1-21 3l-2-1-1 1c1 1 3 1 5 1-2 2-3 1-5 2l-4-1c-1 0-2 0-3 1h-2 0-1-4l-3 1-1-1v-2h-1l-1-1c1 0 1 0 2-1l-1-1 1-1-1-1c0-1 0-2-1-3 2-1 2-1 2-2 1 0 1 1 1 1h1v-7l10-4c10-5 19-10 28-15 5-3 11-6 14-11z" class="T"></path><path d="M443 681c0-3 0-7 1-10 1 2 1 3 1 5 1 1 1 2 0 4v2h1-2l-1-1z" class="G"></path><path d="M418 670c1-1 3-2 5-2 1 2 1 3 1 4l-1 1h-1v-1c-1 1-1 2-1 3v1h2 1 0c2-1 5-1 8-2l10-1c1 1 1 1 1 3v1c0 2-1 3 0 4l1 1c-2 1-4 1-6 1 1 1 3 1 4 1 4 0 8 0 12-1h5l-1 1h0c-4 0-7 1-11 1-7 1-14 1-21 3l-2-1-1 1c1 1 3 1 5 1-2 2-3 1-5 2l-4-1c-1 0-2 0-3 1h-2 0-1-4l-3 1-1-1v-2h-1l-1-1c1 0 1 0 2-1l-1-1 1-1-1-1c0-1 0-2-1-3 2-1 2-1 2-2 1 0 1 1 1 1h1v-7l10-4 1 1z" class="X"></path><path d="M426 683c0-1 0-4 1-5 1 1 0 4 1 5-1 1-1 0-2 0z" class="Y"></path><path d="M407 673l10-4 1 1c-1 0-9 3-10 5v5c1 0 2-1 3 0-1 1-1 1-2 1v1c0 1 0 2-1 3v1 1h0c3-1 4-2 5-4h0v-5h1c1 1 0 3 1 4 1-1 2-1 3-2 0 1 0 1 1 2v2h1v-1l1-2h0c0 1 1 2 1 3h1c1-1 0-4 1-5 0 1 0 2-1 3l2 2 1-1c1 0 1 1 2 0h2 7 1c1 1 3 1 4 1 4 0 8 0 12-1h5l-1 1h0c-4 0-7 1-11 1-7 1-14 1-21 3l-2-1-1 1c1 1 3 1 5 1-2 2-3 1-5 2l-4-1c-1 0-2 0-3 1h-2 0-1-4l-3 1-1-1v-2h-1l-1-1c1 0 1 0 2-1l-1-1 1-1-1-1c0-1 0-2-1-3 2-1 2-1 2-2 1 0 1 1 1 1h1v-7z" class="e"></path><path d="M405 691l1-2h1l2 1v1l-3 1-1-1z" class="k"></path><path d="M413 683v-5h1c1 1 0 3 1 4 1-1 2-1 3-2 0 1 0 1 1 2l-2 3-1 2h-3-1c1-1 3-1 4-2v-1l-1-1v2h-1l-1-2z" class="Y"></path><path d="M454 683h5l-1 1h0c-4 0-7 1-11 1-7 1-14 1-21 3l-2-1-1 1c1 1 3 1 5 1-2 2-3 1-5 2l-4-1c-1 0-2 0-3 1h-2 0-1-4v-1h1c2-1 5-1 7-2 1 0 2-1 4-1 2-1 5-1 8-1l13-2c4 0 8 0 12-1z" class="L"></path><path d="M519 619c2-1 4-1 6 0 1 0 2 0 3 1 1-1 2-1 3-2h2 1l11 2c2 0 5 1 7 1-1 3 0 5 1 8 2 6 3 11 6 17 1 2 3 4 3 7 0 0 0 1 1 1l1 5c0 3 1 6 3 8h1v2h1 0c1 0 2-1 4-1v1c1 0 2 1 3 1h0c1-1 2-1 3 0l2-1 1 1v1c-1 1-2 1-3 1 0 1 0 1 1 2h-1-3v1c-1-1-1 0-1-1-1-1-1-1-2-1-1 1-2 0-3 0-1 2 0 6-1 9h0c-4 1-10 1-14 0h-5c-4-1-7 0-11-1-1 0-2 1-3 0l-5 1-11-1h-3l-1-1c0 1 0 1-1 2l-1-1c-2 1-4 0-5 1-4 0-7 0-11-1v-2-1l-1-1 1-3c-1-2-1-2-2-2v-1c0-1 0-1 1-2v-1-5-1c0-1-2-2-3-2 2-5 4-11 4-17l2 1c1-5 1-11 1-16l1 1h1l-1-1 1-2-1-2c2 0 6 0 7-1 2-2-1 0 1-1h1 0s1 0 1-1v-1l2-1-2-1c2 0 5 0 7 1z" class="E"></path><path d="M516 680h1c0-2 0-6-1-7 2-1 3 0 4-1l1 1-1 1v7h-3l-1-1zm-3-30v1c1 2 0 3 1 4v1 1c0 2-1 3-2 4l-2 1h5c1 1 1 1 3 1v-5l1 2v4l-1 1h-2c-1 1 0 1-1 1l-1-1c0-1-1-1-2-1-1-1-1-1-2-1s-1 0-2-1c2-2 2-3 4-3v-1c0-1 0-1-1-1l-1-1c1 0 2 0 2-1h-2v-1h2l-1-1c-1-1-1-1-2-1l1-1h2l1-1z" class="D"></path><path d="M533 676v-1-3h1 3s1 1 2 0h0v9c-1 0-2 1-3 0s-1-1-3-1v-2c1 0 2 0 2-1v-1l-2 1v-1z" class="J"></path><path d="M501 628l1 1v3 1 3c-1 1 0 3 0 4h0c-1 2-1 4-1 7 0 4-1 9-1 13v1c-1 1-1 1-1 3h4c1 1 2 0 3 0 3 2 6 0 8 1v6 1h-2-5-2-1-5c-1 2-1 4-1 6l-1-1 1-3c-1-2-1-2-2-2v-1c0-1 0-1 1-2v-1-5-1c0-1-2-2-3-2 2-5 4-11 4-17l2 1c1-5 1-11 1-16z" class="f"></path><path d="M498 643l2 1c0 4-1 9-2 13 0 2 0 5-1 6v-1c0-1-2-2-3-2 2-5 4-11 4-17z" class="K"></path><path d="M499 664h4c1 1 2 0 3 0 3 2 6 0 8 1v6c-4 0-11 1-15 0v-3c0-1 0-1 1-2-1-1-1-2-1-2z" class="I"></path><path d="M512 618c2 0 5 0 7 1v7l-1 6c1 0 2-1 2-1v7c-2 3-1 7-1 10h-2c0 3 1 7 1 10v5c-2 0-2 0-3-1h-5l2-1c1-1 2-2 2-4v-1-1c-1-1 0-2-1-4v-1h0c-1 0-1 0-2-1 1 0 1 0 2-1-1-1-3-1-5-2h2 1c1 0 1 0 2-1l-1-1h-1v-1c1 0 1 0 2-1h-1l-1-1c-2-1-6 0-8 0l-1-1h0c0-1-1-3 0-4v-3-1-3h1l-1-1 1-2-1-2c2 0 6 0 7-1 2-2-1 0 1-1h1 0s1 0 1-1v-1l2-1-2-1z" class="W"></path><path d="M518 632c1 0 2-1 2-1v7c-2 3-1 7-1 10h-2l1-16z" class="o"></path><path d="M502 633h9v2h1l-1 1c0 1 0 1 1 2h0c-2 2-6 0-8 2h-2c0-1-1-3 0-4v-3z" class="J"></path><path d="M512 618c2 0 5 0 7 1v7l-1-1c-2-1-4-1-6-1l-1 1v2l3 2h-1l-2 1v2h1v1h-1-9v-1-3h1l-1-1 1-2-1-2c2 0 6 0 7-1 2-2-1 0 1-1h1 0s1 0 1-1v-1l2-1-2-1z" class="C"></path><path d="M503 629h6v3h0-1c-2-1-4 0-6 0v-3h1zm0-3c1-1 2-1 3-1l1-1h2v4h-7l1-2z" class="D"></path><path d="M519 619c2-1 4-1 6 0 1 0 2 0 3 1 1-1 2-1 3-2 1 6 0 13 0 19v7s0 2 1 2c0 2 0 3-1 5l1 6 2 9h-1c-2 0-3 0-4 1 0 1 0 1-1 3 1 0 1 1 1 2l-1 1-1-1h-2v-2h0-1l-1-1c0-1-1-2-1-3l-1 1c-2 0 0-1-2 0v3h-1v-4c1-1 2-1 4-1h0l-3-5-1-2c0-3-1-7-1-10h2c0-3-1-7 1-10v-7s-1 1-2 1l1-6v-7z" class="E"></path><path d="M527 621c1 0 1 0 2 1v2l-1 2 1 2h-1c-1 1 0 1 0 2l-1 1-2-2c0-2 0-2 1-4h0l-1-1v-1l1-1 1-1z" class="J"></path><path d="M522 624v-1l-2-1v-1l3-2h1v2c-1 4 0 8 0 11v11c0 1 0 2-1 3l-1-22z" class="f"></path><path d="M519 619c2-1 4-1 6 0 1 0 2 0 3 1 1-1 2-1 3-2 1 6 0 13 0 19v7s0 2 1 2c0 2 0 3-1 5l-1-16c0-3-1-7-1-11v-2c-1-1-1-1-2-1h-3v-2h-1l-3 2v1l2 1v1c-1 1-2 4-2 6v1s-1 1-2 1l1-6v-7z" class="R"></path><path d="M526 644h-1c1-2 2-1 3-2l-2-1 1-1-1-1 1-2c0-1 1-1 3-2l1 16 1 6 2 9h-1c-2 0-3 0-4 1-1-1-2-1-2-1l-2-14c0-2-1-4 0-6l2-1-1-1z" class="W"></path><path d="M527 645h1l-2 2c0 1 1 1 0 2v2h0l-1 1c0-2-1-4 0-6l2-1zm-1-1h-1c1-2 2-1 3-2l-2-1 1-1-1-1 1-2c0-1 1-1 3-2l1 16 1 6c-2-3-2-5-2-9h0c-1-2 0-3-2-5h-1l-1 1z" class="D"></path><path d="M522 624l1 22c1-1 1-2 1-3l1 3c-1 2 0 4 0 6l2 14s1 0 2 1c0 1 0 1-1 3 1 0 1 1 1 2l-1 1-1-1h-2v-2h0-1l-1-1c0-1-1-2-1-3l-1 1c-2 0 0-1-2 0v3h-1v-4c1-1 2-1 4-1h0l-3-5-1-2c0-3-1-7-1-10h2c0-3-1-7 1-10v-7-1c0-2 1-5 2-6z" class="V"></path><path d="M522 624l1 22c0 5 1 10 1 15 0 2 1 4 0 5l-1-1v-1c-3-9-3-17-3-26v-7-1c0-2 1-5 2-6z" class="D"></path><path d="M533 618h1l11 2c2 0 5 1 7 1-1 3 0 5 1 8 2 6 3 11 6 17 1 2 3 4 3 7 0 0 0 1 1 1l1 5c0 3 1 6 3 8h1v2h1 0c1 0 2-1 4-1v1c1 0 2 1 3 1h0c1-1 2-1 3 0l2-1 1 1v1c-1 1-2 1-3 1 0 1 0 1 1 2h-1-3v1c-1-1-1 0-1-1-1-1-1-1-2-1-1 1-2 0-3 0-1 2 0 6-1 9h0c-4 1-10 1-14 0h-5c-4-1-7 0-11-1v-9h0c-1 1-2 0-2 0h-3-1v3 1c-1-1-1-2-1-2v-1c-1-1-1-1-2-1h-1c1-1 2-1 2-2v-2c0-1 1-2 2-2h1l-2-9-1-6c1-2 1-3 1-5-1 0-1-2-1-2v-7c0-6 1-13 0-19h2z" class="Q"></path><path d="M567 667v2l-1 1c-3-3-5-10-7-15s-5-10-7-15c-2-6-3-13-3-19 2 4 2 8 4 12v1c2 7 6 13 9 19 0 0 0 1 1 1l1 5c0 3 1 6 3 8z" class="B"></path><path d="M545 635l2-12 1 6c1 4 2 8 4 12 1 4 3 8 5 11l4 12v3c-2 0-3-1-5-1v1h-1l-1-1c-1-1-2 0-3 0s-1 0-2 1c-1-1-1-1-2-1l-1-1v-2l-1-1v-1c1-2 1-5 2-6 0-1 1-2 1-2v-1h-3v-3c-1-1 0-4 0-5v-2-7z" class="W"></path><path d="M545 649h1c-1-1-1-2-1-3l1-1 2-2c0 2 1 7 0 9h-3v-3z" class="C"></path><path d="M545 635l2-12 1 6h-1v1c0 1 0 2-1 3 0 2 1 6 2 8v2h0l-2 2-1 1c0 1 0 2 1 3h-1c-1-1 0-4 0-5v-2-7z" class="K"></path><path d="M532 646l1 1 2 8v2c1 1 0 1 1 2v2l1 3v1c1 0 1 1 1 1 0 1 0 1-1 2h1l2 3 1-2 2 1v-2-1-4c1-1 0-1 0-2 2-3 1-6 2-9h3v1s-1 1-1 2c-1 1-1 4-2 6v1l1 1v2l1 1c1 0 1 0 2 1 1-1 1-1 2-1s2-1 3 0l1 1h1v-1c2 0 3 1 5 1v-3l1 1v5l-2 2h1c1 0 3 0 4 1h1c1 1 1 0 2 0 1 2 1 6 1 9h0c-4 1-10 1-14 0h-5c-4-1-7 0-11-1v-9h0c-1 1-2 0-2 0h-3-1v3 1c-1-1-1-2-1-2v-1c-1-1-1-1-2-1h-1c1-1 2-1 2-2v-2c0-1 1-2 2-2h1l-2-9-1-6c1-2 1-3 1-5z" class="F"></path><path d="M532 646l1 1 2 8v2c1 1 0 1 1 2v2l1 3v1c1 0 1 1 1 1 0 1 0 1-1 2h1l2 3 1-2 2 1v-2c2 0 3-1 4 0h2v3h0c1-1 0-3 2-3h2l1 4h-4v7h0v3c-4-1-7 0-11-1v-9h0c-1 1-2 0-2 0h-3-1v3 1c-1-1-1-2-1-2v-1c-1-1-1-1-2-1h-1c1-1 2-1 2-2v-2c0-1 1-2 2-2h1l-2-9-1-6c1-2 1-3 1-5z" class="X"></path><path d="M539 672h11v7h0v3c-4-1-7 0-11-1v-9z" class="H"></path><path d="M533 618h1l11 2-1 3-1 12h2v7 2c0 1-1 4 0 5v3c-1 3 0 6-2 9 0 1 1 1 0 2v4 1 2l-2-1-1 2-2-3h-1c1-1 1-1 1-2 0 0 0-1-1-1v-1l-1-3v-2c-1-1 0-1-1-2v-2l-2-8-1-1c-1 0-1-2-1-2v-7c0-6 1-13 0-19h2z" class="N"></path><path d="M533 618h1l11 2-1 3h-2c-1 1-5 1-6 1s-2 2-2 2l-1 1v-9z" class="O"></path><path d="M533 618h1l2 2h3v1h-4l-1 1c2 1 5-1 6 1h2c-1 1-5 1-6 1s-2 2-2 2l-1 1v-9z" class="F"></path><path d="M531 618h2v9l1 1c0 6-1 12 0 19l4 14c0 1 1 3 2 4l1-1c0-2 1-5 1-7v-1-4c1-3 0-5 1-7v-4-6h2v7 2c0 1-1 4 0 5v3c-1 3 0 6-2 9 0 1 1 1 0 2v4 1 2l-2-1-1 2-2-3h-1c1-1 1-1 1-2 0 0 0-1-1-1v-1l-1-3v-2c-1-1 0-1-1-2v-2l-2-8-1-1c-1 0-1-2-1-2v-7c0-6 1-13 0-19z" class="V"></path><path d="M619 832c1 1 2 2 3 2h1c1 1 3 2 5 3l1 1h3c1-1 1-1 2-1v1c1 0 2 0 3 1l1 1h1c0 2 0 2 1 3 2 1 3 1 4 2h2c2 2 3 2 5 3 7 5 13 10 17 18h1c1 2 2 4 3 7 2 4 3 10 4 15v1h1c2-1 5-1 7 0l2-1v4c1 2 1 5 0 8h0l-1 1-2 1v1h-6-1v1l2 1h0l-3 1v1s1 0 2 1h-1c-1 1-1 0-2 1 0 1 0 1-1 2v1c3 0 8-1 10 1l-2 2h-1c-3 0-6 0-9 1h-5c-1 1-2 1-3 2h-1-6l1-1-1-1h-1c0 1-1 1-2 1h0l-1 2v1c1 1 1 1 2 1v3c-2 0-5 1-7 0-5 2-10 1-15 2v1l1 1h-1c-1 0-2-1-3 0h-1c-3 0-5 0-7-1h-1l-2-1v-1l-1-1c3 0 3 0 5-2l3-5c0-1 0-2 1-3v-2h0l1-1c1-2 2-11 1-14v-1l-1-2-1-3c0-2-1-3-2-5h0v-1c-2-2-3-5-5-8h0l1-1v-2l1-1h-1-3l-1-3c0-1-2-2-3-2-1-1-2 0-3 0l-6-4h5v-2l-2-1 3 1c1-1 3-1 4-2s2-2 2-4h1v-1l3-3c-2-2-1-7-2-9l1-4c-1-2 0-5 0-7z" class="R"></path><path d="M625 874l1-1 1 1h0 4l-1 1 2 1 3-3 1 2c-1 0-2 1-3 1-2 0-3 0-5 1v3h-1c-1-2-1-4-2-6z" class="Y"></path><path d="M626 870h1c2 2 5 1 7 2v1h1l-3 3-2-1 1-1h-4 0l-1-1v-1-1-1z" class="l"></path><path d="M626 871c1 1 1 1 2 1 1 1 2 1 3 1v1h-4 0l-1-1v-1-1z" class="Z"></path><path d="M624 886c2-1 2-2 3-1l1 1c2 1 3-1 4 2h1c0 2 0 1-1 3 1 0 2 0 2 1s0 1-1 2h-1c0-2-1-2-1-3-1-1-1-2-1-3h0v3c-1 1-2 3-2 5l-1-2-1-3c0-2-1-3-2-5h0z" class="h"></path><path d="M639 889c-2-1-2 0-3 0h-1l2-2-1-1c-1 0-1 1-2 0v-1h2v-1l-1-1v-1c2-1 3-1 5 1 4 2 7 7 9 12-1 0-2 1-4 0 1-4-1-6-3-9h-1c0 1 0 1-1 2l-1 1z" class="J"></path><path d="M686 888v4c-1-1-2-1-3-1h-3c-1 0-1 0-2 1l1 1-2 1v1h1v1l-1 1 1 2h0l-1 1v2h1 0l-1 1h-1v1l2 1h0l-3 1v1s1 0 2 1h-1c-1 1-1 0-2 1 0 1 0 1-1 2v1c-3 1-6 1-9 0h4c2-1 4-4 5-6l2-5v-1c1-3 1-6 1-10v-1h1c2-1 5-1 7 0l2-1z" class="O"></path><path d="M686 888v4c-1-1-2-1-3-1h-3c-1 0-1 0-2 1h-1v-1c3-2 4-2 7-2h0l2-1z" class="D"></path><path d="M632 861h4c1 2 2 3 2 5l-1 1v4 1h-1-2c-2-1-5 0-7-2 2 0 1 0 2-1l-1-1h-2l1-3c1-2 2-2 3-2 1-1 0 0 2-1v-1z" class="Z"></path><path d="M632 861h4c1 2 2 3 2 5l-3-1-1 1h-1c0-2 0-3-1-5z" class="F"></path><path d="M678 892c1-1 1-1 2-1h3c1 0 2 0 3 1 1 2 1 5 0 8h0l-1 1-2 1v1h-6l1-1h0-1v-2l1-1h0l-1-2 1-1v-1h-1v-1l2-1-1-1z" class="E"></path><path d="M683 902c0-1-1-1-2-1v-1l1-1c2 0 2 0 4 1l-1 1-2 1z" class="N"></path><path d="M680 893h6v4c-2 1-4 1-5 1-1-2-1-4-1-5z" class="i"></path><path d="M643 883c-2-2-4-3-6-4v-1-1c0-1 1-1 1-2h2s-1-2-2-3l1-2v-1-1l1-1h1v1l1 1v1c1 1 1 2 2 3l5 5-1 4-1 1-1-2-1 1h-1l-1 1z" class="j"></path><path d="M639 889l1-1c1-1 1-1 1-2h1c2 3 4 5 3 9 2 1 3 0 4 0 3 5 6 13 11 15 1 1 4 2 4 2 3 1 6 1 9 0 3 0 8-1 10 1l-2 2h-1c-3 0-6 0-9 1h-5c-3 0-7 0-9-1-2 0-3-1-4-2h-1l2-2-1-1h-2c-2 1 0 1-2 1l1-1-1-1h-1l-1 1-5 1h0c1-1 3-2 4-3h0c-1-1-2 0-4 0h0c1-2 3-3 4-4v-1l-3 1v-1c0-1 1-1 1-2-1 0-1 0-2-1l1-2h0l-4-1c1 0 1-1 1-1h2v-1c-1-2-2-1-3-1l-1-1 2-2h0-2-1c0-1 1-2 2-2z" class="i"></path><path d="M620 852c1-2 3-4 6-5l3 1c2 0 3 1 5 3 2 0 2 1 3 2v2h1c1 0 1 0 1 1v1l1 1h0l1 1 1 1c1 0 1 0 1 1l-2 1h-1 0c-2-1-3-2-3-4-1 1-1 1-1 2v1h-4v1c-2 1-1 0-2 1-1 0-2 0-3 2l-1 3h2l1 1c-1 1 0 1-2 1h-1v1 1 1l-1 1c-1 1 0 3-2 3h-4 0l1-1v-2l1-1h-1-3l-1-3c0-1-2-2-3-2-1-1-2 0-3 0l-6-4h5v-2l-2-1 3 1c1-1 3-1 4-2s2-2 2-4h1v-1l3-3z" class="o"></path><path d="M626 870c-1-1-2-2-4-2h0l-1-1 1-1h1c2 0 0-1 1-2l1 1v3h1 2l1 1c-1 1 0 1-2 1h-1z" class="M"></path><path d="M629 861c-1 0-1 2-3 2 0 1-1 0-1 0-1-1-2-1-2-3 1-2 1-2 4-3v3 1c1-1 1-2 2-2h0v2z" class="X"></path><path d="M627 857h0c1-1 2-1 4-3l1 1v3-1l1 3c-1 0-2 1-4 1v-2h0c-1 0-1 1-2 2v-1-3z" class="M"></path><path d="M616 870l3 1h0v-1l1-1s1 0 2 1v1h0l2-1h0l1 2h1v1l-1 1c-1 1 0 3-2 3h-4 0l1-1v-2l1-1h-1-3l-1-3z" class="l"></path><path d="M632 857c1-1 2-3 3-4l-1-1-1 2h-2-2-1c-1 0-3 2-4 3h0 0c0-2 2-5 4-6v-2h1c0 1 0 2 1 2l1-1c1 1 1 1 2 1h1c2 0 2 1 3 2v2h1c1 0 1 0 1 1v1l1 1h0l1 1 1 1c1 0 1 0 1 1l-2 1h-1 0c-2-1-3-2-3-4l-1-1h-1v2h0l-2 1-1-3z" class="L"></path><path d="M616 856l3 1v2h0c1 1 1 2 2 2l-1 1h-1c0 1 1 2 2 2-1 2-2 1-3 3l-1-1c-1 1-2 2-4 2-1-1-2 0-3 0l-6-4h5v-2l-2-1 3 1c1-1 3-1 4-2s2-2 2-4z" class="Q"></path><path d="M615 863h1l1 1-1 1h-2c0-1 0-1 1-2z" class="h"></path><path d="M628 896c0-2 1-4 2-5v-3h0c0 1 0 2 1 3 0 1 1 1 1 3h1l2 1-1 1c1 1 1 1 3 1v1h-1v1l2 1h0c0-1 1-2 1-2v-1l4 1h0l-1 2c1 1 1 1 2 1 0 1-1 1-1 2v1l3-1v1c-1 1-3 2-4 4h0c2 0 3-1 4 0h0c-1 1-3 2-4 3h0l5-1 1-1h1l1 1-1 1c2 0 0 0 2-1h2l1 1-2 2h1c1 1 2 2 4 2 2 1 6 1 9 1-1 1-2 1-3 2h-1-6l1-1-1-1h-1c0 1-1 1-2 1h0l-1 2v1c1 1 1 1 2 1v3c-2 0-5 1-7 0-5 2-10 1-15 2v1l1 1h-1c-1 0-2-1-3 0h-1c-3 0-5 0-7-1h-1l-2-1v-1l-1-1c3 0 3 0 5-2l3-5c0-1 0-2 1-3v-2h0l1-1c1-2 2-11 1-14v-1z" class="T"></path><path d="M653 917l-1 2v1c1 1 1 1 2 1v3c-2 0-5 1-7 0h-6c2-1 4-1 6-2v-1h-1l-1-1h0c0-1 1-1 2-1h1c1-1 0-1 1-1 2 1 3 0 4-1z" class="f"></path><path d="M632 894h1l2 1-1 1c1 1 1 1 3 1v1h-1v1l2 1h0c0-1 1-2 1-2v-1l4 1h0l-1 2c1 1 1 1 2 1 0 1-1 1-1 2v1l3-1v1c-1 1-3 2-4 4h0 0l-1 1h-1l1-1c-1-1 0-1-1-1h0c0-1 0-1 1-2l-1-1-1 1h-1c1-1 1-2 2-2l1-1h-2s-1 0-1 1h-1l-1-1-3-3c-1-1-1-4-1-5z" class="Q"></path><path d="M647 910l1-1h1l1 1-1 1c2 0 0 0 2-1h2l1 1-2 2h1c1 1 2 2 4 2 2 1 6 1 9 1-1 1-2 1-3 2h-1-6l1-1-1-1h-1c0 1-1 1-2 1h0c-1 1-2 2-4 1 1 0 2-1 3-2-3 1-5 2-8 1v-1c1 0 2-1 3-2 0-1-5 0-6 0 1-1 4-1 5-3l1-1z" class="S"></path><path d="M617 924c3 0 3 0 5-2 1 1 1 1 2 1h4c2 0 3-1 5 0 2 0 5 0 8 1h6c-5 2-10 1-15 2v1l1 1h-1c-1 0-2-1-3 0h-1c-3 0-5 0-7-1h-1l-2-1v-1l-1-1z" class="d"></path><path d="M617 924c3 0 3 0 5-2 1 1 1 1 2 1 0 1 0 1-1 2h0c-1 1-2 1-2 2h-1l-2-1v-1l-1-1z" class="f"></path><path d="M619 832c1 1 2 2 3 2h1c1 1 3 2 5 3l1 1h3c1-1 1-1 2-1v1c1 0 2 0 3 1l1 1h1c0 2 0 2 1 3 2 1 3 1 4 2h2c2 2 3 2 5 3 7 5 13 10 17 18h1c1 2 2 4 3 7 2 4 3 10 4 15v1 1c0 1-1 3-2 4v4l-1 2c0 1 0 3-1 5-1 1-3 4-6 5h0c-2 0-4 0-5-1-5-1-7-8-9-13l-4-7c-2-3-3-4-5-6l1-1h1l1-1 1 2 1-1 1-4-5-5c-1-1-1-2-2-3v-1l-1-1v-1c1 0 3 0 4 1l1-1c-1 0-2-1-3-1 0-1 0-1-1-2 0-1-1-1-1-2l2-1c0-1 0-1-1-1l-1-1-1-1h0l-1-1v-1c0-1 0-1-1-1h-1v-2c-1-1-1-2-3-2-2-2-3-3-5-3l-3-1c-3 1-5 3-6 5-2-2-1-7-2-9l1-4c-1-2 0-5 0-7z" class="R"></path><path d="M660 895l1 2v1c0 1-1 2 0 3 0 1 0 3-1 5h0-1c-1-1-2-3-2-4 2-3 2-4 3-7z" class="h"></path><path d="M649 868l1 2c-2 1-1 1-1 3h0l1-1 1 1c-1 1-1 1 0 3l3 3v1h1l1-1c2 3-3 5 2 7 1 1 1 1 1 2l-1 4c2 1 2 1 2 3-1 3-1 4-3 7-1-1-1-2-2-3 0-1 0-2-1-3l-1-1-1-2c-1-1-2-2-2-3v-1l2 1h0v-1h0 1c0-2 0-3-1-4 0-1 0-2-1-3v-1c-1-1-1-2-2-3l-5-5c0-1 1-2 1-3 1-2 2-2 4-2z" class="T"></path><path d="M649 878c1 1 1 2 2 3v1c1 1 1 2 1 3 1 1 1 2 1 4h-1 0v1h0l-2-1v1c0 1 1 2 2 3l1 2 1 1c1 1 1 2 1 3 1 1 1 2 2 3 0 1 1 3 2 4h1l1 1 1-3 2-1c-1-1-1-1-2-1h0v-1h1c2-1 1-2 2-3l-1-1c0-1 0-2 1-2v-1h-1v-1l1-1v-1h-2v-1c0-1 1-1 2-2v-1h-2-1l3-3h0-3c1-1 3-2 4-3h1v-1h1c0 1 0 2 2 3v1c0 1-1 2 0 3h1v2c-1 1-1 1-1 2v1h0l2 1h2v1 4l-1 2c0 1 0 3-1 5-1 1-3 4-6 5h0c-2 0-4 0-5-1-5-1-7-8-9-13l-4-7c-2-3-3-4-5-6l1-1h1l1-1 1 2 1-1 1-4z" class="K"></path><path d="M650 890h0c-1-1-2-2-2-3l-3-3 1-1c2 2 2 1 4 1 1 1 1 1 2 1h0c1 1 1 2 1 4h-1 0v1h0l-2-1v1z" class="L"></path><path d="M667 880h1c0 1 0 2 2 3v1c0 1-1 2 0 3h1v2c-1 1-1 1-1 2v1h0l2 1h2v1 4l-1 2c0 1 0 3-1 5-1 1-3 4-6 5h0l-1-1v-2c0-1 1-1 1-2h0c1-2 2-3 4-4h1v-1h-3v-1-4h2l-1-1c-1 0-1-1-2-1 1-2 1-3 0-4v-1l1-1h-1c0-1-1-1-1-1 0-1 1-1 2-2l-1-1h-1l-1 1h-3c1-1 3-2 4-3h1v-1z" class="B"></path><path d="M673 900c-1 0-3 0-4-1 0-2 0-2 1-3h3l1 2-1 2z" class="i"></path><path d="M665 909c2-1 1-2 2-3s3-1 5-1c-1 1-3 4-6 5h0l-1-1z" class="I"></path><path d="M619 832c1 1 2 2 3 2h1c1 1 3 2 5 3l1 1h3c1-1 1-1 2-1v1c1 0 2 0 3 1l1 1h1c0 2 0 2 1 3 2 1 3 1 4 2-2 0-3 1-5 2v1c2 1 3 3 5 4 0 0 1 1 1 2h1v1l-1 1c0 1 1 2 2 3h1c1 2 0 3 0 4h-1c1 2 2 2 1 3v1l1 1c-2 0-3 0-4 2 0 1-1 2-1 3-1-1-1-2-2-3v-1l-1-1v-1c1 0 3 0 4 1l1-1c-1 0-2-1-3-1 0-1 0-1-1-2 0-1-1-1-1-2l2-1c0-1 0-1-1-1l-1-1-1-1h0l-1-1v-1c0-1 0-1-1-1h-1v-2c-1-1-1-2-3-2-2-2-3-3-5-3l-3-1c-3 1-5 3-6 5-2-2-1-7-2-9l1-4c-1-2 0-5 0-7z" class="H"></path><path d="M639 856c1-1 1-1 2 0v1 1h-1l-1-1v-1z" class="B"></path><path d="M619 832c1 1 2 2 3 2 2 2 4 3 6 5l-1 2c1 0 2 1 3 1 1 1 1 2 1 3-1 0-4 0-5 1v-1h-3-1-3l-1-2 1-4c-1-2 0-5 0-7z" class="D"></path><path d="M619 832c1 1 2 2 3 2 2 2 4 3 6 5l-1 2-1-1c-1 0-1-1-1-1-2-1-1 0-2 0h-4c-1-2 0-5 0-7z" class="B"></path><path d="M621 836h2v1l-1 1-2-1 1-1z" class="D"></path><path d="M623 834c1 1 3 2 5 3l1 1h3c1-1 1-1 2-1v1c1 0 2 0 3 1l1 1h1c0 2 0 2 1 3 2 1 3 1 4 2-2 0-3 1-5 2v1c2 1 3 3 5 4 0 0 1 1 1 2h1v1l-1 1c0 1 1 2 2 3h1c1 2 0 3 0 4h-1c1 2 2 2 1 3v1l1 1c-2 0-3 0-4 2 0 1-1 2-1 3-1-1-1-2-2-3v-1l-1-1v-1c1 0 3 0 4 1l1-1c0-2 0-3-1-5v-1c0-2 0-2-1-3 0-1-1-2-1-2-2-6-10-14-15-17-2-2-4-3-6-5h1z" class="Q"></path><path d="M629 838h3c1-1 1-1 2-1v1c1 0 2 0 3 1l1 1h1c0 2 0 2 1 3 2 1 3 1 4 2-2 0-3 1-5 2-3-4-8-6-10-9z" class="B"></path><path d="M644 845h2c2 2 3 2 5 3 7 5 13 10 17 18h1c1 2 2 4 3 7 2 4 3 10 4 15v1 1c0 1-1 3-2 4v-1h-2l-2-1h0v-1c0-1 0-1 1-2v-2h-1c-1-1 0-2 0-3v-1c-2-1-2-2-2-3h-1v1h-1-3-1l-1-1c1-1 1-1 1-2h0-2v-2c0-1 0 0-1-1h-1l2-2h-2l-1-2c0-1 0-1 1-2h-1-1-1c0-1 1-2 1-2v-2c-1 0-2 0-3-1 1-2-1-3-1-5-1-1-1 0-3-1l1-1v-2s-1 0-2-1c1-1 2-1 3-2l-2-1c0 1-1 1-2 1-1-1-2-2-3-2h-1l-1-2c0-1-2 0-3 0v-1c2-1 3-2 5-2z" class="N"></path><path d="M650 855c3 0 4-1 5 1 2 1 3 2 4 4v1 1l-1-1-1-1h-4l1-1-1-1-3-1v-2z" class="E"></path><path d="M668 866h1c1 2 2 4 3 7 2 4 3 10 4 15v1 1c0 1-1 3-2 4v-1c0-9-2-18-6-27z" class="a"></path><path d="M650 857l3 1 1 1-1 1h4l1 1-2 2h0 3l1 1-1 1h0 1 1v1l-1 1h0 2l1 2h0c-1 0-1 1-2 1 1 1 1 1 2 1l2 1h0c-1 1-2 1-3 2h1 2c1 1 1 2 2 4l-1 1h0l1 1v1h-1-3-1l-1-1c1-1 1-1 1-2h0-2v-2c0-1 0 0-1-1h-1l2-2h-2l-1-2c0-1 0-1 1-2h-1-1-1c0-1 1-2 1-2v-2c-1 0-2 0-3-1 1-2-1-3-1-5-1-1-1 0-3-1l1-1z" class="K"></path><path d="M542 887l1-1-1-1v-1l2-1c-1-1 0-1 0-2 1-1 1-1 3-2h3c1 0 3 1 4 2 1-1 2-2 3-1 2 0 3 1 5 2-1 1-1 1 0 2 3 3 7 6 10 9 1 2 2 3 3 5 1 1 2 2 2 4h3 1v-1-1s2-1 3-1 1 0 2-1h0l2-1h2l2-1c3-1 5 0 8-2v2 2l1 1-1 1 1 1v3 2l2-1v2h1c1 1 1 1 2 1l2 1c0 1 0 1 1 1l1-1v-2l3-1h1 5v2l2 1c0 1-1 2-1 3v3h-1c0 1 0 2-1 2v2 1l2-2 1 1c1 0 0 0 1-1 1 0 2-1 3-1l-3 5c-2 2-2 2-5 2l1 1v1l2 1h1c2 1 4 1 7 1h1c1-1 2 0 3 0h1 1l-1 2h1l3 1-1 1-1-1c-2 0-4 1-6 0l-2 2h-3-6c-1 1-2 1-3 1h-1-1l-1 1c-1 0-1 0-2 1 2 1 3 2 5 2h1 7v1c-2 1-5 0-7 0-1 0-2 0-3 1h-2l-4 1 1 1h0c1 0 3 1 3 2l1 1c2 1 3 0 5 0 1 0 3 1 4 1h1c-3 1-6 1-9 1-1 0-2-1-2-1h-1c-1 0-2 1-2 1-2 1-2 1-4 1s-4 0-6-1v-1l-2-2h-3v2s0 1-1 1l-1-1c-1 1-2 1-3 2 1 1 3 1 4 1h-6 0v1 2l-2-1h0c-1 1-1 1-2 1-3-1-6-1-9-1v3l-1 1c-3-2-7-2-10-3v-1h-2c-2 1-4 1-6 1v-2c-2-1-6-2-8-3-2 0-3 0-5-1h0c-1-1-2-1-2-2h2 1v-1h-1v-1h3c-2-2-5-1-8-1l-4-3h2v-1-1l-1-1 4-2h5v-4-15-8h0v-2c0-1-1-3-1-4l-1-10v-3-1l2 1z" class="T"></path><path d="M561 916c1 0 1 1 2 2-1 1-2 2-3 2l-1 1c0-3 0-3 2-5z" class="k"></path><path d="M559 921c-2 0-4 0-5 2h0c1 0 2-1 3-1v1c-2 1-3 1-6 1 1-1 2-2 3-4s2-3 4-4c1-1 1 0 3 0-2 2-2 2-2 5z" class="U"></path><path d="M540 886l2 1-1 1h1v2c0 1 1 2 2 4 1 0 0 0 1 1h-2v1 1c1 0 1 1 1 2h0l-1-2-1 1v4 4h0v-2c0-1-1-3-1-4l-1-10v-3-1zm39 38h-4 0c-2-1-3-2-4-3v-1h3v-1h-2c-1 0-3 0-4-1 1 0 1-1 2-1h-2c-2-1-2-2-2-4h1l6 3 3 2c2 1 3 3 5 5-1 0-1 1-2 1z" class="Q"></path><path d="M559 896l2-1c1 0 3 0 4 1 1 2 2 3 3 5s2 4 4 6h-1-3l-1-1 1-2c-3 1-4-1-6-2s-3-2-4-3v1 1h-2c0-2 1-3 3-5z" class="p"></path><path d="M559 896l2-1c1 0 3 0 4 1 1 2 2 3 3 5h-2-1c-2-1-3-1-4-2 0-1-1-1-1-2-1 0-1 0-1-1z" class="L"></path><path d="M542 933c-2 1-4 1-5 2 1 1 2 1 4 1 1-2 2-2 2-4 1-1 2-2 3-2h1c3 0 7 1 9 0h2v1h1c-2 2-4 1-7 2 3 0 3 0 5 1v1h0c-2 0-3 0-4 1h-5c-5 1-9 1-14 2v-1-1l-1-1 4-2h5z" class="X"></path><path d="M548 936v-1h-2v-1c1 0 2-1 3-2 1 0 2-1 2 0v1h1c3 0 3 0 5 1v1h0c-2 0-3 0-4 1h-5z" class="F"></path><path d="M542 887l1-1-1-1v-1l2-1c-1-1 0-1 0-2 1-1 1-1 3-2h3c1 0 3 1 4 2 1-1 2-2 3-1 2 0 3 1 5 2-1 1-1 1 0 2 3 3 7 6 10 9-3 0-5-1-7 0l-1 1-3 1-2 1c-2 2-3 3-3 5-1 0-2 1-2 1h-1v-4l-3 3c-1-1-3-3-4-5l1-1 2-2-1-1v1l-2 2h-1c-1-1 0-1-1-1-1-2-2-3-2-4v-2h-1l1-1z" class="R"></path><path d="M549 893h2c1 1 1 2 1 3h0-1c-1 0-3 0-4-1l2-2z" class="V"></path><path d="M547 879h3c1 0 3 1 4 2 1 0 2 1 3 2 1 0 1-1 2 0l-2 1-1 2c-2 2-4 4-6 4l-2-2c1-1 1-2 1-3-1 0-2-1-3-2 1-1 1 0 1-1v-1l-1-1 1-1z" class="G"></path><path d="M549 880c1 0 2 1 3 1h1l-1 2h-4v-1l1-2z" class="F"></path><path d="M556 886c-2 0-3 0-4-2v-1c2 0 4 0 5 1l-1 2z" class="I"></path><path d="M562 884c3 3 7 6 10 9-3 0-5-1-7 0l-1 1-3 1-2 1c-2 2-3 3-3 5-1 0-2 1-2 1h-1v-4l2-2v-1c1-1 1-1 1-2h-1c0 1-1 1-1 1-1-1-2-1-2-2 2-2 3-5 6-5 0-1 2-2 4-3z" class="G"></path><path d="M555 896c1-1 2-2 3-2l1-1-1-2 1-1c0 1 1 1 1 2l1 1h4l-1 1-3 1-2 1c-2 2-3 3-3 5-1 0-2 1-2 1h-1v-4l2-2z" class="Q"></path><path d="M565 893c2-1 4 0 7 0 1 2 2 3 3 5 1 1 2 2 2 4h3 1v-1-1s2-1 3-1 1 0 2-1h0l2-1h2l2-1c3-1 5 0 8-2v2 2l1 1-1 1 1 1v3 2l2-1v2h1c1 1 1 1 2 1l2 1c0 1 0 1 1 1l1-1v-2l3-1h1 5v2l2 1c0 1-1 2-1 3v3h-1c0 1 0 2-1 2v2 1l2-2 1 1c1 0 0 0 1-1 1 0 2-1 3-1l-3 5c-2 2-2 2-5 2l-2 1h0v-1l-1-1c-2 1-3 2-5 2-1 1-3 0-4 0-2 1-4 0-6 0-3-1-5-1-8-1l3 3c-2 0-3 0-4 1h-1l-3-1h-3-1l-1-1c1-1 1-1 1-2 1-1 2 0 3 0v-1l-3-2c-1 1 0 2-1 4l-2-1c1 0 1-1 2-1-2-2-3-4-5-5v-2-1h-1c-1-1-2-1-2-3-1 0-1-1-1-2h-2l-1-1c-1-1-1-1-1-2h3 1c-2-2-3-4-4-6s-2-3-3-5c-1-1-3-1-4-1l3-1 1-1z" class="P"></path><path d="M583 927l-1-2h2 0 2l1 1h-1v1h-3z" class="f"></path><path d="M565 893c2-1 4 0 7 0 1 2 2 3 3 5 1 1 2 2 2 4l3 3c1 1 1 2 2 3 1 0 1 1 2 1l-2 1c0 1-1 1-1 0h-1l1-1c-2-2-3 0-5-1l1-1-1-1h-1c-3 0-3-3-5-5-1-2-2-3-3-4 0-1 0 0-1-1h-1c-1-1-3-1-4-1l3-1 1-1z" class="b"></path><path d="M576 906l1 1-1 1c2 1 3-1 5 1l-1 1h1c0 1 1 1 1 0l2-1 1 2c2 1 3 2 5 3 1 1 2 1 4 1-3 1-3-1-5 2h2c2 0 3 0 5 1h-1v1h0v2 1c-3-1-4-1-6-1v2c-1 0-2 0-3-1-2-1-4-1-6-3l2-1v-1c-3-1-4-2-6-5 1 0 1 0 2-1-1 0-2 0-3-1h0c1-1 1 0 1-1h-2v-1c0-1 1-1 2-2h0z" class="l"></path><path d="M585 911c2 1 3 2 5 3-3 1-5 0-8 0l-1-1c1 0 2 0 3-1l1-1z" class="G"></path><path d="M591 917c2 0 3 0 5 1h-1v1h0v2 1c-3-1-4-1-6-1l-1-1c1-1 3 0 4-1h-1v-1-1z" class="V"></path><path d="M590 897l2-1c3-1 5 0 8-2v2 2l1 1-1 1 1 1v3 2c0 1 1 2 0 3-1 2 0 2 0 4h-2v1c-2 0-2 1-2 2v1l-1 1c-2-1-3-1-5-1h-2c2-3 2-1 5-2-2 0-3 0-4-1-2-1-3-2-5-3l-1-2c-1 0-1-1-2-1-1-1-1-2-2-3l-3-3h3 1v-1-1s2-1 3-1 1 0 2-1h0l2-1h2z" class="L"></path><path d="M589 906h2 1c-1 1-2 1-3 2h3l1 1 3 2 1 1h-1v1h3v1c-2 0-2 1-2 2v1l-1 1c-2-1-3-1-5-1h-2c2-3 2-1 5-2v-2c-1-1-2-2-3-2l-1-1v-1l-3-1v-1l2-1z" class="b"></path><path d="M581 901h5l-1 1-2 1h0l3 1-1 1v1h4l-2 1v1l3 1v1l1 1c1 0 2 1 3 2v2c-2 0-3 0-4-1-2-1-3-2-5-3l-1-2c-1 0-1-1-2-1-1-1-1-2-2-3l-3-3h3 1v-1z" class="Q"></path><path d="M590 897l2-1c3-1 5 0 8-2v2 2l1 1-1 1 1 1v3c-2 0-3 1-6 0v-1c-1-1-5 0-6 0l-1-1c2 0 4 0 6-1 1 0 1-1 1-1-1-1-2-1-4 0h-4v-1l2-1h1v-1z" class="I"></path><path d="M601 906l2-1v2h1c1 1 1 1 2 1l2 1c0 1 0 1 1 1l1-1v-2l3-1h1 5v2l2 1c0 1-1 2-1 3v3h-1c0 1 0 2-1 2v2 1l2-2 1 1c1 0 0 0 1-1 1 0 2-1 3-1l-3 5c-2 2-2 2-5 2l-2 1h0v-1l-1-1c-2 1-3 2-5 2-1 1-3 0-4 0-2 1-4 0-6 0-3-1-5-1-8-1l-2-1v-2c2 0 3 0 6 1v-1-2h0v-1h1l1-1v-1c0-1 0-2 2-2v-1h2c0-2-1-2 0-4 1-1 0-2 0-3z" class="Y"></path><path d="M601 906l2-1v2 3c1 2-1 6-2 8l-2 1h-4 0v-1h1l1-1v-1c0-1 0-2 2-2v-1h2c0-2-1-2 0-4 1-1 0-2 0-3z" class="R"></path><path d="M601 913h0l-2 4h-2v-1c0-1 0-2 2-2v-1h2z" class="Z"></path><path d="M599 919h1c1 1 1 0 2 1l1-2v1c2 1 11-1 12 1-1 1-2 0-3 1 0 1-1 2-2 3l-2-1h-5-1c-2-1-5-2-7-2v-2h4z" class="G"></path><path d="M614 906h5v2c0 2-1 4-1 6v1l-2 2c-2-1-3-1-5-1l1 1h-7 0c1-1 3-1 4-1-1-1-3-1-5-1l1-1h5v-1c-2 0-3-1-5-1h-1l4-3c0 1 0 1 1 1l1-1v-2l3-1h1z" class="O"></path><path d="M614 906h5v2c0 2-1 4-1 6-2-2-4-1-6-1v-1h3l1-1h-3c1-1 0-1 1-1v-1c0-1 0-1-1-1v-2h1z" class="H"></path><path d="M591 924c3 0 5 0 8 1 2 0 4 1 6 0 1 0 3 1 4 0 2 0 3-1 5-2l1 1v1h0l2-1 1 1v1l2 1h1c2 1 4 1 7 1h1c1-1 2 0 3 0h1 1l-1 2h1l3 1-1 1-1-1c-2 0-4 1-6 0l-2 2h-3-6c-1 1-2 1-3 1h-1-1l-1 1c-1 0-1 0-2 1 2 1 3 2 5 2h1 7v1c-2 1-5 0-7 0-1 0-2 0-3 1h-2l-4 1 1 1h0c1 0 3 1 3 2l1 1c2 1 3 0 5 0 1 0 3 1 4 1h1c-3 1-6 1-9 1-1 0-2-1-2-1h-1c-1 0-2 1-2 1-2 1-2 1-4 1s-4 0-6-1v-1l-2-2h-3v2s0 1-1 1l-1-1c-1 1-2 1-3 2 1 1 3 1 4 1h-6 0v1 2l-2-1h0c-1 1-1 1-2 1-3-1-6-1-9-1v3l-1 1c-3-2-7-2-10-3v-1h-2c-2 1-4 1-6 1v-2c-2-1-6-2-8-3-2 0-3 0-5-1h0c-1-1-2-1-2-2h2 1v-1h-1v-1h3c-2-2-5-1-8-1l-4-3h2c5-1 9-1 14-2h5c1-1 2-1 4-1h0v-1c-2-1-2-1-5-1 3-1 5 0 7-2h-1v-1h13c1-1 2 0 3-1 2-1 4 1 6 0s4 0 6 0c1-1 0 0 2-1h1 1c1-1 2-1 4-1l-3-3z" class="L"></path><path d="M596 944h4l2 2h-4l-2-2z" class="E"></path><path d="M578 937l3 1 1 1 1 1h1l1 2h-6-1 0l1-2-2-1 1-2z" class="Z"></path><path d="M578 937l3 1 1 1-3 1-2-1 1-2z" class="S"></path><path d="M600 944c3-1 5-1 8-1l1 1c0 1 0 1-1 1-1 1-4 1-6 1l-2-2z" class="B"></path><path d="M583 934c1 1 2 1 3 2 1-1 1-1 2 0h4l1 1c-2 1-4 1-5 1-3-1-5-1-7 0l-3-1-6-1v-1h9l2-1z" class="X"></path><path d="M588 933h3c1 0 2 0 3 1h0 3 2l2 2c1 0 2 0 3 1h-1c-3-1-5 1-7-1 0 0 1 0 2-1h0-5c-1 1-3 1-5 1-1-1-1-1-2 0-1-1-2-1-3-2h1c0-1 2-1 3 0h0l1-1z" class="K"></path><path d="M569 945v-1c4-1 10-2 14 0l1 1-1 1c-3 1-7 1-10 0-2 0-3 0-4-1z" class="J"></path><path d="M555 939h1v-1c2 1 3 1 5 1h0c4 2 10 0 13 2-2 1-4 1-6 1-1 1-2 1-4 2l-4 1h0c-2-1-4-1-6-1l12-2c-2 0-5-2-7-1-1 0-2-1-3-1v1h-5v-1l4-1z" class="I"></path><path d="M551 944h3c2 0 4 0 6 1h0l-2 1c3 0 7-1 10 0l1 1-4 1c0 1 0 1-1 1v-1h-7c-3 0-7-1-10-2v-1c2-1 3-1 4-1z" class="C"></path><path d="M551 944h3c2 0 4 0 6 1h0l-2 1v-1c-3 1-5 1-7 0v-1z" class="E"></path><path d="M556 941v-1c1 0 2 1 3 1 2-1 5 1 7 1l-12 2h-3c-1 0-2 0-4 1v1c3 1 7 2 10 2l-3 2c-2-1-6-2-8-3-2 0-3 0-5-1h0v-1h1c1 0 2 0 3-1v-2h3l2 1c2 1 4-1 6-2z" class="M"></path><path d="M581 938c2-1 4-1 7 0h12c-3 1-5 2-8 2l1 1h3c1-1 4 0 5 0l1 1c-3 1-9 0-12 0h0-5l-1-2h-1l-1-1-1-1z" class="I"></path><path d="M584 940h0c2 0 2 0 3-1 1 0 1 0 3 2v1h-5l-1-2z" class="H"></path><defs><linearGradient id="I" x1="534.482" y1="935.134" x2="576.478" y2="941.148" xlink:href="#B"><stop offset="0" stop-color="#b5b3b2"></stop><stop offset="1" stop-color="#e5e3e5"></stop></linearGradient></defs><path fill="url(#I)" d="M560 936h2c4-2 6-2 10 0l6 1-1 2h-16c-2 0-3 0-5-1v1h-1l-4 1v1h5c-2 1-4 3-6 2l-2-1h-3v2c-1 1-2 1-3 1h-1v1c-1-1-2-1-2-2h2 1v-1h-1v-1h3c-2-2-5-1-8-1l-4-3h2c5-1 9-1 14-2h5c2 1 5 1 7 0z"></path><path d="M544 942l3-1v-1h-4l1-1c4-2 7-1 11 0l-4 1v1h5c-2 1-4 3-6 2l-2-1h-3v2c-1 1-2 1-3 1h-1v1c-1-1-2-1-2-2h2 1v-1h-1v-1h3z" class="G"></path><path d="M584 945c3 0 6-1 9-1v2s0 1-1 1l-1-1c-1 1-2 1-3 2 1 1 3 1 4 1h-6 0v1 2l-2-1h0c-1 1-1 1-2 1-3-1-6-1-9-1v3l-1 1c-3-2-7-2-10-3v-1h-2c-2 1-4 1-6 1v-2l3-2h7v1c1 0 1 0 1-1l4-1-1-1 1-1c1 1 2 1 4 1 3 1 7 1 10 0l1-1z" class="O"></path><path d="M569 945c1 1 2 1 4 1l-2 2h1l-1 1-2-1h-4l4-1-1-1 1-1z" class="F"></path><path d="M562 952c4-2 7-2 11-1v3l-1 1c-3-2-7-2-10-3z" class="B"></path><path d="M584 945c3 0 6-1 9-1v2s0 1-1 1l-1-1c-1 1-2 1-3 2 1 1 3 1 4 1h-6-3c-2 1-4 2-6 1h-4v-1c1-2 6 0 8-1 2 0 4 1 6-1l-1-1h-3l1-1z" class="C"></path><path d="M591 924c3 0 5 0 8 1 2 0 4 1 6 0 1 0 3 1 4 0 2 0 3-1 5-2l1 1v1h0l2-1 1 1v1l2 1h1c2 1 4 1 7 1h1c1-1 2 0 3 0h1 1l-1 2h1l3 1-1 1-1-1c-2 0-4 1-6 0l-2 2h-3-6c-1 1-2 1-3 1h-1-1l-1-1c-1 0-1 0-2 1s-2 1-4 1c0 1 0 1 1 2h-3c-1-1-2-1-3-1l-2-2h-2-3 0c-1-1-2-1-3-1h-3l-1 1h0c-1-1-3-1-3 0h-1l-2 1h-9v1c-4-2-6-2-10 0h-2c-2 1-5 1-7 0 1-1 2-1 4-1h0v-1c-2-1-2-1-5-1 3-1 5 0 7-2h-1v-1h13c1-1 2 0 3-1 2-1 4 1 6 0s4 0 6 0c1-1 0 0 2-1h1 1c1-1 2-1 4-1l-3-3z" class="M"></path><path d="M610 934c-1 1-2 1-4 1 0 1 0 1 1 2h-3c-1-1-2-1-3-1l-2-2h5 6z" class="C"></path><path d="M560 936h0l-2-1 1-1c2 0 3 1 6 0l1-1s1-1 2-1l2 2c1 0 2 1 2 1v1c-4-2-6-2-10 0h-2z" class="b"></path><path d="M581 935c-1 0 0 0-1-1-2 0-3 0-4-1l1-1h5 1 1c1-1 3 0 4 1l-1 1h0c-1-1-3-1-3 0h-1l-2 1z" class="H"></path><path d="M620 927h1c2 1 4 1 7 1h1c1-1 2 0 3 0h1 1l-1 2h1l3 1-1 1-1-1c-2 0-4 1-6 0-3 0-6 0-9-1-1 0-1 1-2 1 0 0-1-1-2-1 1 1 0 1 1 2-2 1-7 0-10 0 2-1 3-1 5-1 1 0 1-1 2-1h1c1-1 2-1 3-1h1v-1l1-1z" class="m"></path><path d="M633 928h1l-1 2h1l3 1-1 1-1-1c-2 0-4 1-6 0-3 0-6 0-9-1h6v-1l1 1c1 1 3 1 4 0l1-2h1z" class="R"></path><path d="M591 924c3 0 5 0 8 1 2 0 4 1 6 0 1 0 3 1 4 0 2 0 3-1 5-2l1 1v1h0l2-1 1 1v1l2 1-1 1v1h-1c-1 0-2 0-3 1h-1c-1 0-1 1-2 1-2 0-3 0-5 1-4 0-8 0-12-1-3 0-6-1-9-1-5-1-10 0-15 0 1-1 2 0 3-1 2-1 4 1 6 0s4 0 6 0c1-1 0 0 2-1h1 1c1-1 2-1 4-1l-3-3z" class="U"></path><path d="M618 926l2 1-1 1v1h-1c-1 0-2 0-3 1h-1c-1 0-1 1-2 1-2 0-3 0-5 1-4 0-8 0-12-1 4-1 7 1 10-2h0 2l-1-2h0 1 2c0 1 0 1 1 2l1-1 1-1h4l2-1z" class="Q"></path><path d="M618 926l2 1-1 1c-1 0-2 0-3-1l2-1z" class="P"></path><path d="M594 927l1 1-1 1c2 0 2 0 3-1l-1-1h0l9 2c-3 3-6 1-10 2-3 0-6-1-9-1-5-1-10 0-15 0 1-1 2 0 3-1 2-1 4 1 6 0s4 0 6 0c1-1 0 0 2-1h1 1c1-1 2-1 4-1z" class="c"></path><path d="M485 882c-1 2-1 4-1 6h1c2-1 4-1 6-2 1-1 3-1 5-1h2c0 1 0 2 1 3h8 4 3l2 1v6 10l1 1c8-1 17-1 25 0h0v8 15 4h-5l-4 2 1 1v1 1h-2l4 3c3 0 6-1 8 1h-3v1h1v1h-1-2c0 1 1 1 2 2h0c2 1 3 1 5 1 2 1 6 2 8 3v2c-2 0-4 0-6-1h-7l1 2c-4 0-10 0-13 2l2 2h-2c-3 0-6-1-9-2-7-1-14 2-21 0-4-1-7 0-10 0-5 0-10 0-14 1h-3l-2 1c-3 0-6 1-8 0 3-1 6-2 9-2 2 0 4-1 6-1 1 0 1 0 1-1s0-1-1-2l-1-1h-7l-2-1h-3l-7 1 1-3c-4-1-7-1-11 0h-6l-5 1c-1 1-2 1-3 2-2 0-4 1-6 1l-8-3h1 1l3 1h3l1-4h-1c-2-1-5 0-7-1v-1c2 0 3-1 5 0 1-1 1-1 1-3-2 0-3 1-5 0v-1h-4c-1 0-1 0-1-1l2-1v-1l1-1c2 0 3 0 5-1v-1h2 4c2-1 3-1 5-2h1c1-1 2-1 3-1h0 0c1-1 5-1 6-1v-1h0c-3-1-6 0-8 0h-3l-1-1v1h-1v-1-1c2-1 5 0 8-1l1-1 4-1v-1l-1-1h0v-1l-1-1c0-1 1-2 2-2v-1l3-5 2-3h0 2l1-1c3 1 5 2 9 2h1l2-2h1s0-1 1-1c0-1 1-1 2-2l-2-1 1-1 1-1c-1-1-1-2-1-2v-1l1-1c3-1 3-2 5-4-1-1-1-1-1-3 1-1 2-2 3-2 1-1 2-1 2-2 1 0 1-1 2-1l4-2 1-1z" class="C"></path><path d="M498 950c1-2 5-2 7-2l6 3-1 1c-4-1-9-1-12-2z" class="B"></path><path d="M506 944c4-1 6-2 10-2 1 2 1 2 0 4h-7c-2 0-2-1-3-2z" class="D"></path><path d="M486 951c1-1 3-2 4-2h1c2 1 5 1 7 1 3 1 8 1 12 2l-1 1c-1 0-2-1-3-1h-1l-2-1-1 1h0c-1 0-2 1-2 1-3 1-4-1-6 0l-1-2c-3-1-4 0-7 1v-1z" class="I"></path><path d="M516 948c4 0 11-2 15 0-1 2-2 2-4 3-3 0-8 0-10-1h-3-1-1c-1-1-2-1-3-2 1 0 1 1 2 1v-1h5z" class="D"></path><path d="M486 952c3-1 4-2 7-1l1 2c2-1 3 1 6 0l1 1c3 1 10 1 13 0l-2-2 1-1c5 1 11 1 17 0h11l1 2c-4 0-10 0-13 2l2 2h-2c-3 0-6-1-9-2-7-1-14 2-21 0-4-1-7 0-10 0-5 0-10 0-14 1h-3l-2 1c-3 0-6 1-8 0 3-1 6-2 9-2 2 0 4-1 6-1 1 0 1 0 1-1s0-1-1-2h9v1z" class="Y"></path><path d="M486 952c3-1 4-2 7-1l1 2c-3 1-10 1-13 0v-1h5z" class="E"></path><path d="M532 938l4 3c3 0 6-1 8 1h-3v1h1v1h-1-2c0 1 1 1 2 2h0c2 1 3 1 5 1 2 1 6 2 8 3v2c-2 0-4 0-6-1h-7-11-3c2-1 3-1 4-3-4-2-11 0-15 0-2 0-4 0-6-1l1-1h6 1-2c1-2 1-2 0-4h4 4c-3-2-7 0-10-2v-1c3-1 7 0 10 0h1l7-1z" class="F"></path><path d="M516 942h4c1 1 1 2 1 3-1 0-2 1-3 1h-2c1-2 1-2 0-4z" class="E"></path><path d="M532 938l4 3v1h-3-3c-1 0-3 0-5-1v-2l7-1z" class="S"></path><path d="M536 941c3 0 6-1 8 1h-3v1h1v1h-1-2c0 1 1 1 2 2h0c2 1 3 1 5 1-4 0-7 0-11 1v-1h-4v-1c0-1 0-1-1-1s0 0-1-1c2 0 4 0 5-1h1c-2-1-3-1-5-1h3 3v-1z" class="C"></path><path d="M546 947c2 1 6 2 8 3v2c-2 0-4 0-6-1h-7-11-3c2-1 3-1 4-3h4c4-1 7-1 11-1z" class="B"></path><defs><linearGradient id="J" x1="498.759" y1="925.556" x2="507.438" y2="943.348" xlink:href="#B"><stop offset="0" stop-color="#110e12"></stop><stop offset="1" stop-color="#2e2e2a"></stop></linearGradient></defs><path fill="url(#J)" d="M516 913c1 2 0 3 1 5l-1 6c-1 2 0 7 0 10h8v3c3 0 6 0 9-2l1 1v1 1h-2l-7 1h-1c-3 0-7-1-10 0v1c3 2 7 0 10 2h-4-4c-4 0-6 1-10 2l-1 1c-2 0-3-2-5-2-3 0-3 1-6 0-1 1-2 1-3 1-2-1-3-1-5-1-1 1-2 1-3 1-4-1-8-1-12-2h-3v-1-1-1h0c2 0 3-1 5-1h0l1-1 2 1 4-1c-1 0-2 0-2-1l2-2c1-1 1-1 1-2h1l1 1 1-1c3 1 5 1 9 1 1 1 4 1 6 1h1c3 0 12 1 14 0l-1-1h-1c1-1 2-1 3-1v-4-4l1-8v-3z"></path><path d="M481 932h1l1 1-1 2c1 0 2 0 3 1h0c-2 0-3 0-4 1s-1 0-1 0c-1 0-2 0-2-1l2-2c1-1 1-1 1-2z" class="e"></path><path d="M485 936l1 1c1 0 3 0 4-1h1c5 2 10 0 15 1l-1 1c-3 0-8-1-10 0v1c-1 2-2 1-4 1l3 3h0c-1 1-2 1-3 1-2-1-3-1-5-1-1 1-2 1-3 1-4-1-8-1-12-2h-3v-1-1-1h0c2 0 3-1 5-1h0l1-1 2 1 4-1s0 1 1 0 2-1 4-1z" class="Y"></path><path d="M486 938h5v1h-5v1h-5c2-2 2-2 5-2z" class="L"></path><path d="M481 940h5c2 0 3 1 5 0l3 3h0c-1 1-2 1-3 1-2-1-3-1-5-1h-1-1c-1 0-4-1-5-2l2-1z" class="H"></path><path d="M485 936l1 1v1c-3 0-3 0-5 2l-2 1c1 1 4 2 5 2h1 1c-1 1-2 1-3 1-4-1-8-1-12-2h-3v-1-1-1h0c2 0 3-1 5-1h0l1-1 2 1 4-1s0 1 1 0 2-1 4-1z" class="M"></path><path d="M474 937l2 1c0 1-1 1-2 2h-6v-1h0c2 0 3-1 5-1h0l1-1z" class="K"></path><path d="M533 935l1 1v1 1h-2l-7 1h-1c-3 0-7-1-10 0v1c3 2 7 0 10 2h-4-4c-4 0-6 1-10 2l-1 1c-2 0-3-2-5-2-3 0-3 1-6 0h0l-3-3c2 0 3 1 4-1v-1c2-1 7 0 10 0l1-1h9 4 5c3 0 6 0 9-2z" class="G"></path><path d="M506 937h9 4c-3 1-6 0-8 2v1h-1c-2-1-3-2-5-2l1-1z" class="L"></path><path d="M517 906c8-1 17-1 25 0h0v8 15 4h-5l-4 2c-3 2-6 2-9 2v-3h-8c0-3-1-8 0-10l1-6h0v-4-2-6z" class="D"></path><path d="M531 923h5v-1h-11l-1 1c-2 0-5 0-6-1 1-1 2 0 4 0 1-1 5-1 7-1 2-1 9 0 12 0h0v6h-1v-1h-8 0v-1c0-1 0-1-1-2z" class="B"></path><path d="M517 924c2-1 5 0 8 0 1-1 0-1 1-1h5c1 1 1 1 1 2v1h-1l-1 2h0c-3 0-6 0-8 1h-1-1-2c-1-2-1-3-1-5z" class="E"></path><path d="M541 914h1v15h-1-1c-3 1-5 1-8 1-5 1-10 2-15 1v-1h1 2v-1h1 1c2-1 5-1 8-1h0l1-2h1 0 8v1h1v-6-7h0z" class="C"></path><path d="M530 928l1-2h1 0 8v1l-10 1z" class="D"></path><path d="M516 924h1c0 2 0 3 1 5h2v1h-2-1v1c5 1 10 0 15-1 3 0 5 0 8-1h1 1v4h-5l-4 2c-3 2-6 2-9 2v-3h-8c0-3-1-8 0-10z" class="L"></path><path d="M524 934c2 0 6 0 8-1h5l-4 2c-3 2-6 2-9 2v-3z" class="U"></path><path d="M517 906c8-1 17-1 25 0h0v8h-1 0c-2-1-5 0-7 0h-17v-2-6z" class="D"></path><path d="M517 912h16c2 0 5 0 8 1v1h0c-2-1-5 0-7 0h-17v-2z" class="N"></path><path d="M436 931c1-1 2-1 3-1 3 1 6 1 8 1h4v1l-2 1 2 2 2-1c2 1 4 1 7 2h0c4 0 11-1 13 0v1h1l-1 1h0c-2 0-3 1-5 1h0v1 1 1h3c4 1 8 1 12 2l-1 3 1-1h2 3 2v1c-3 1-4 1-7 1l-2 2h-5-7l-2-1h-3l-7 1 1-3c-4-1-7-1-11 0h-6l-5 1c-1 1-2 1-3 2-2 0-4 1-6 1l-8-3h1 1l3 1h3l1-4h-1c-2-1-5 0-7-1v-1c2 0 3-1 5 0 1-1 1-1 1-3-2 0-3 1-5 0v-1h-4c-1 0-1 0-1-1l2-1v-1l1-1c2 0 3 0 5-1v-1h2 4c2-1 3-1 5-2h1z" class="L"></path><path d="M428 945l8 2c-3 1-5 1-9 2l1-4z" class="S"></path><path d="M458 947h6c2 1 4 0 5 0l-2 2h-3l-7 1 1-3z" class="E"></path><path d="M426 940l5 1s1 0 2-1l-1 1 1 2c-1 0-2 0-2 1h-3-1l-2-1h-2c-1 1-2 0-3 0 2 0 3-1 5 0 1-1 1-1 1-3z" class="C"></path><path d="M469 947c5 1 10 0 14 1l-2 2h-5-7l-2-1 2-2zm-36-7c4 0 6-1 10 1h1c0 2 0 2-1 3h-1c-3-1-6 0-9-1l-1-2 1-1z" class="D"></path><path d="M471 942c4 1 8 1 12 2l-1 3c-2 0-5 0-7-1-2 0-4-1-7 0-2 0-3 0-5-1l2-1c1-1 1-1 2-1 2 0 3 0 4-1z" class="E"></path><path d="M453 942h5l1 1h1s1 0 2-1c1 1 2 1 2 2h1l-2 1c-1 0-2 0-3 1-2 0-4-1-6-2-2 1-4 0-6 1v1h-8c1-1 1-2 2-2h1c1-1 1-1 1-3 3 1 6 0 9 1h0 0z" class="K"></path><path d="M444 941c3 1 6 0 9 1v2h0l-4-1-1 1h-5c1-1 1-1 1-3z" class="I"></path><path d="M436 931c1-1 2-1 3-1 3 1 6 1 8 1h4v1l-2 1 2 2 2-1c2 1 4 1 7 2h0c4 0 11-1 13 0v1h1l-1 1h0c-2 0-3 1-5 1h0v1 1 1h3c-1 1-2 1-4 1-1 0-1 0-2 1h-1c0-1-1-1-2-2-1 1-2 1-2 1h-1l-1-1h-5 0 0c-3-1-6 0-9-1h-1c-4-2-6-1-10-1-1 1-2 1-2 1l-5-1c-2 0-3 1-5 0v-1h-4c-1 0-1 0-1-1l2-1v-1l1-1c2 0 3 0 5-1v-1h2 4c2-1 3-1 5-2h1z" class="O"></path><path d="M453 942l-2-1c3-1 7-1 10 0l2 1h5 0 3c-1 1-2 1-4 1-1 0-1 0-2 1h-1c0-1-1-1-2-2-1 1-2 1-2 1h-1l-1-1h-5z" class="C"></path><path d="M436 931c1-1 2-1 3-1 3 1 6 1 8 1h4v1l-2 1 2 2 2-1c2 1 4 1 7 2h0l1 1v1h0c-2-1-4 0-6-1s-3-1-4-1c-2 0-5 0-6 1-2-1-3-1-4-1v2h0c-2-1-2-1-4 0h0c0-1 0-1 1-1v-2l-1 1-1-1h-6c-1 2-2 4-4 4h0v1c-2 0-3 1-5 0v-1h-4c-1 0-1 0-1-1l2-1v-1l1-1c2 0 3 0 5-1v-1h2 4c2-1 3-1 5-2h1z" class="X"></path><path d="M418 937c4 0 8-1 12-2-1 2-2 4-4 4h0v1c-2 0-3 1-5 0v-1h-4c-1 0-1 0-1-1l2-1z" class="C"></path><path d="M436 931c1-1 2-1 3-1 3 1 6 1 8 1h4v1l-2 1 2 2c-1 0-2 0-3-1h0-4l-1 1c-3-2-6 0-8-1 1-2 3-1 5-2-1 0-3-1-4-1z" class="U"></path><path d="M474 893l2 2c-1 1-1 3-2 4-1 2-1 2 0 4v1c0 1 0 2 1 2h1l1-1 1-1c1 1 2 2 3 2v4c-1 5-1 9-1 14 1 3 1 5 1 8 0 1 0 1-1 2l-2 2c0 1 1 1 2 1l-4 1-2-1h-1v-1c-2-1-9 0-13 0h0c-3-1-5-1-7-2l-2 1-2-2 2-1v-1h-4c-2 0-5 0-8-1h0 0c1-1 5-1 6-1v-1h0c-3-1-6 0-8 0h-3l-1-1v1h-1v-1-1c2-1 5 0 8-1l1-1 4-1v-1l-1-1h0v-1l-1-1c0-1 1-2 2-2v-1l3-5 2-3h0 2l1-1c3 1 5 2 9 2h1l2-2h1s0-1 1-1c0-1 1-1 2-2l-2-1 1-1 1-1c-1-1-1-2-1-2v-1l1-1c3-1 3-2 5-4z" class="T"></path><path d="M475 906h1l1-1 1-1c1 1 2 2 3 2v4l-1-1h-1l-1 2-1-1c0-1-1-2-2-3v-1z" class="Q"></path><path d="M453 907c3 1 5 2 9 2h1l-1 1 1 1h1v1c1 0 2 0 3-1v1h1l-1 1v2h0 2c-1 1-1 2-2 3-2 1-3 1-5 1h-2c-1 1-1 1-1 0h-3l-1 1h0l-2 1-2-2c-1-1-2-2-4-2v2c1 1 2 0 2 2h-1l-2 2h-1v-1l-1-1h0v-1l-1-1c0-1 1-2 2-2v-1l3-5 2-3h0 2l1-1z" class="c"></path><path d="M448 911l2-3h0 2c3 1 4 1 6 2v1s-1 0-1 1c-2 3-5 4-6 7-1-1-2-2-4-2v2c1 1 2 0 2 2h-1l-2 2h-1v-1l-1-1h0v-1l-1-1c0-1 1-2 2-2v-1l3-5z" class="V"></path><path d="M448 911c1 0 2 1 2 2l-1 1 2 1h0v-2h2v1c-1 1-1 1-1 2-2 1-5 0-7 0l3-5z" class="b"></path><path d="M445 928c3 0 8 2 11 1 1-1 2-2 3 0 1 0 1-1 2-1 1-1 3 0 3 0 4 0 10 0 14 2v1h-3c-1 0-1 0-2 1h3c1 0 2 0 3 1l1 1-2 2c0 1 1 1 2 1l-4 1-2-1h-1v-1c-2-1-9 0-13 0h0c-3-1-5-1-7-2l-2 1-2-2 2-1v-1h-4c-2 0-5 0-8-1h0 0c1-1 5-1 6-1v-1z" class="P"></path><path d="M447 931c2 0 3-1 5-1 3-1 5 0 8 1 3 0 6-1 8 0-1 1-3 1-4 1-1 2 0 1-1 2-2 0-4-1-6-1h-3l1 1h-2l-2 1-2-2 2-1v-1h-4z" class="g"></path><path d="M453 934h2l-1-1h3c2 0 4 1 6 1 2 1 5 0 7 1h5v-1h-3v-1h7l1 1-2 2c0 1 1 1 2 1l-4 1-2-1h-1v-1c-2-1-9 0-13 0h0c-3-1-5-1-7-2z" class="a"></path><path d="M485 882c-1 2-1 4-1 6h1c2-1 4-1 6-2 1-1 3-1 5-1h2c0 1 0 2 1 3h8 4 3l2 1v6 10l1 1v6 2 4h0c-1-2 0-3-1-5v3l-1 8v4 4c-1 0-2 0-3 1h1l1 1c-2 1-11 0-14 0h-1c-2 0-5 0-6-1-4 0-6 0-9-1l-1 1-1-1h-1c0-3 0-5-1-8 0-5 0-9 1-14v-4c-1 0-2-1-3-2l-1 1-1 1h-1c-1 0-1-1-1-2v-1c-1-2-1-2 0-4 1-1 1-3 2-4l-2-2c-1-1-1-1-1-3 1-1 2-2 3-2 1-1 2-1 2-2 1 0 1-1 2-1l4-2 1-1z" class="M"></path><path d="M482 913c0-2 0-4 1-5l-1-2c2-2 9-1 11-1h1c-2 1-4 2-7 2l-1 1c1 1 2 1 4 1v1c-2 1-4 1-7 2l-1 1z" class="e"></path><path d="M501 899c5 0 10 0 15 1l-1 4h-5-11c0-1 0-2-1-3 0-1 0-1 1-1l1-1c0 1 0 1 1 1v-1z" class="B"></path><path d="M487 891c1-1 3 0 4-1 3-1 6-1 9-1-2 1-3 2-4 2h0c1 2 3 2 4 3-1 1-3 2-2 3h0c-1 1-1 1-2 1v1h2 3v1c-1 0-1 0-1-1l-1 1c-1 0-1 0-1 1 1 1 1 2 1 3h-6-1c0-1 0-1 1-2-2-1-2-1-4-1 1-2 0-2 0-2 0-2 2-3 3-5h-4l-1-1c1 0 2 0 3-1h0-3v-1z" class="G"></path><path d="M493 904c0-1 1-1 1-2v-1h-2v-1h2v-1h-2v-1h3v-1h-1v-1c1 0 2 0 2-1-1 0-1-1-2-2l2-2h0c1 2 3 2 4 3-1 1-3 2-2 3h0c-1 1-1 1-2 1v1h2 3v1c-1 0-1 0-1-1l-1 1c-1 0-1 0-1 1 1 1 1 2 1 3h-6z" class="K"></path><path d="M506 889l8-1 2 1v6 5c-5-1-10-1-15-1h-3-2v-1c1 0 1 0 2-1h0c-1-1 1-2 2-3-1-1-3-1-4-3h0c1 0 2-1 4-2h6z" class="D"></path><path d="M500 889h6c-2 1-3 2-4 3l1 1 1 1c-1 1-3 1-3 2l1 1v1h-4v1h-2v-1c1 0 1 0 2-1h0c-1-1 1-2 2-3-1-1-3-1-4-3h0c1 0 2-1 4-2z" class="S"></path><path d="M507 905h9l-1 7 1 1v3h-1-8c-3 0-11 1-13 0l3-1c-3-1-7 0-10 0v-1l7-1c2 1 7 0 10-1h0l-8-1v-1h2l-2-2 1-1c2-2 7-2 10-2z" class="E"></path><path d="M494 916l3-1c6 1 12 0 18 0v1h-8c-3 0-11 1-13 0z" class="D"></path><path d="M507 905h9l-1 7c-2-1-5-1-8-1-1-1-2-1-3-2-1 0-1 0-2-1 1-1 3-2 5-3z" class="N"></path><path d="M485 882c-1 2-1 4-1 6h1c2-1 4-1 6-2 1-1 3-1 5-1h2c0 1 0 2 1 3h8 4 3l-8 1h-6c-3 0-6 0-9 1-1 1-3 0-4 1h-2c-1 1-1 1-2 1-1 2-1 5-1 7l-1 7c-1 0-2-1-3-2l-1 1-1 1h-1c-1 0-1-1-1-2v-1c-1-2-1-2 0-4 1-1 1-3 2-4l-2-2c-1-1-1-1-1-3 1-1 2-2 3-2 1-1 2-1 2-2 1 0 1-1 2-1l4-2 1-1z" class="Q"></path><path d="M485 882c-1 2-1 4-1 6l-1 1h0-2l-3-3c1 0 1-1 2-1l4-2 1-1z" class="k"></path><path d="M496 885h2c0 1 0 2 1 3l-9 1c-2 0-5 1-7 0l1-1h1c2-1 4-1 6-2 1-1 3-1 5-1z" class="F"></path><path d="M474 904c1-1 1-1 1-3 1 1 1 0 1 1l1 1 1-1c0-2 0-2-1-3 0-1 0-1 1-2 0 0 1 1 2 1h1l1 1-1 7c-1 0-2-1-3-2l-1 1-1 1h-1c-1 0-1-1-1-2z" class="P"></path><path d="M483 912c3 0 9-1 11 1l-7 1v1c3 0 7-1 10 0l-3 1c2 1 10 0 13 0h8 1l-1 8v4 4c-1 0-2 0-3 1h1l1 1c-2 1-11 0-14 0h-1c-2 0-5 0-6-1-4 0-6 0-9-1l-1 1-1-1v-5-2h0v-2-10l1-1z" class="k"></path><path d="M504 929c1-1 2-1 3-1h8v4c-1 0-2 0-3 1-1-1-2-1-3-1-5 0-9-1-13 0h0c-3-1-6-1-9-2h13c2 0 4 0 6-1h-2z" class="M"></path><path d="M504 929c-4 1-9 0-13 0v-1l10-1c-3 0-7 1-10-1 3-1 6 0 8-1h0l-10-1h0c1-1 4 0 5-1-2-1-5-1-7-1v-1c2 0 5 0 7-1h-9c4-1 9-1 13-3h0-13c2 0 7-2 9-1s10 0 13 0h8 1l-1 8v4h-8c-1 0-2 0-3 1z" class="O"></path><path d="M515 916h1l-1 8h-8v-1c-2 0-2-1-3-1v-2h2 2c1 0 2 0 3-1h1c-2 0-5 0-7-1 1 0 1 0 2-1v-1h8z" class="I"></path><path d="M507 917l8 1v1h-3c-2 0-5 0-7-1 1 0 1 0 2-1z" class="B"></path><path d="M373 842h5 3l1-1c1 0 3 1 3 1v1 1h-1c-1 1-1 2-1 2v2l2-1c0 1 0 2 1 3 2 0 4 1 6 2v1h1 0l7 7 4 4 2 1 3 1c1-3 3-5 4-9h2v2h4l1 1-1 1c-1 0-1 1-2 1 0 2 0 2 1 3 0 1-2 3-3 3l1 1h2c1-1 3-1 5-2h1s0-1 1 0h1v-2h3c0-1 1-1 2-1l-2 2h-2v2c-1 0-2 1-2 1-1 2-1 2-3 3h-1l-1 2 2-1 1 1-7 9-1 2c-1 3-2 4-2 7-1 2-2 3-2 5h0v6h0c2 0 5 0 7-1v1 1 1 1h8v4h0l1 1c1 1 1 4 2 5l1 4c0 1 2 3 3 4h4 4l-1 1c-3 1-6 0-8 1v1 1h1v-1l1 1h3c2 0 5-1 8 0h0v1c-1 0-5 0-6 1h0 0c-1 0-2 0-3 1h-1c-2 1-3 1-5 2h-4-2v1c-2 1-3 1-5 1-2 1-4 1-5 0-1 2-1 2-3 3h0l-2 1c-1-1-1-2-2-2h-1c-2-1-2-1-4 0l-1-1v-1h-3-6l-5 1c-2 1-5 1-7 1v1h-8v-2h0l-5-1h-2c-2 0-4 0-5-2v-1h2l3-1h-2v-1c-1-2-5-1-7-2l1-1c-1-1-3-1-4-1h-2c-3 0-5-1-7-1l-1-1c-3 2-6 1-9 1-1 0-4 0-5-1v-1h-1-1c-2 1-4 1-6 1v1h2c-2 2-4 2-6 2v1c0 1-1 1-2 2h0l-5-1h0-5l3-1 2-2h-3l-2-1 1-1c1-1 1-1 1-2l-4-1h0c0-1 0-2-1-3l-1 1-1-1v-1c1-1 4-1 6-2h5l1-1c-2-1-3-1-5-1l1-1c3-1 2-3 4-5v1l1 1h0l1 2h1 1c2 0 3 0 4-1l1 2 1 1c1 1 2 2 4 2l1 1 9 1c-1-3-4-8-3-11h0c1 1 1 1 1 2h1 0 4c1 1 2 1 3 1 1-1 2-1 2-2v-3h1l1 1-1 1c1 1 0-1 1 1l1 1 1-1h2l1-1h-1l1-1-1-2c1 0 1-1 2-2h-2c-2 1-3 2-5 2v-2c0-2 0-4-1-6 0-2 0-5 1-7v-5c1-1 1-3 1-4 1-4 3-8 5-12 1-2 3-4 5-7h0c1-2 2-2 3-4 0-1 1-1 2-2v-1l2-3 2-2 3-3c0-1 2-3 2-4l3 1z" class="c"></path><path d="M314 911h1 1c2 0 3 0 4-1l1 2 1 1h-2c0 1-1 1-1 1-3-1-4-1-5-3zm42 10l1-1c1 0 1-1 1-1l1-1c1 0 1 1 2 2h0 2 1 4c2 1 6 0 8 1h-1c1 0 1 1 1 1 1 1 2 0 2 1l-1 1v-1h-6-2l-1-1-1-1h-5-2-4z" class="P"></path><path d="M367 921h8c1 0 1 1 1 1 1 1 2 0 2 1l-1 1v-1h-6-2l-1-1-1-1z" class="R"></path><path d="M359 910c3 1 4 4 7 2v3c-1 1-1 1-2 1l-1-1c-2 0-2 1-3 1v-1l-1 1c-1 0-2 1-3 1h-1-2 0l1-1v-2c1 1 1 1 2 0 0 0 0-1 1-1v-1l2-2h0z" class="Q"></path><path d="M376 921c5 0 10-1 15 0h1l1 1v1l1 1h0 3l1 1c-3 1-5 0-7 1h-1v-1c-2-1-4 0-6 0h-2-6l1-1 1-1c0-1-1 0-2-1 0 0 0-1-1-1h1z" class="m"></path><path d="M384 925c2-2 7-1 10-1h3l1 1c-3 1-5 0-7 1h-1v-1c-2-1-4 0-6 0z" class="n"></path><path d="M379 894h3 0c1 2 3 2 4 3v2l-1 1c0 1 0 2 1 2 0 0-1 1-1 2s1 2 2 3l-2 2v1h0l-2-1-1 2h1 1v1l-1 1h-1c-1-1-2-1-3-1-1 1-1 2-2 4h-1c-1 0-3-2-3-3-1-1-1-3 0-4h1c-1 1-1 1-1 3h1c1-1 2-1 2-3h2c1-1 0-2-1-2l1-2v-1c0-1 1-2 1-3 1 0 0-1-1-1l2-2c0-2-1-3-1-4z" class="Q"></path><path d="M355 893c1 0 1 1 1 2h1l2 2-4 4-1 2c0 1 0 1 1 1l2 3 2 2v1h0l-2 2v1c-1 0-1 1-1 1-1 1-1 1-2 0v2l-1 1h0l-2 1h-1v-2h0c0-1-1-2-2-2-1 1 0 2-1 3-3 0-5 0-8-1-1 0-1-1-2-2 0-1-1-1-1-2h-1l1-2-1-2h0 4c1 1 2 1 3 1 1-1 2-1 2-2v-3h1l1 1-1 1c1 1 0-1 1 1l1 1 1-1h2l1-1h-1l1-1-1-2c1 0 1-1 2-2l1-1h0l-2-1h-1l2-1v-2h1 1l-2-1 1-1h2v-1z" class="f"></path><path d="M355 893c1 0 1 1 1 2h1l2 2-4 4-1-2c-1-1-1-1-1-2 1 0 1 0 2-1h0-1l-2-1 1-1h2v-1z" class="Q"></path><path d="M357 912h-1-1v1 1l-1-1v-2s-1-1-1-2c-1-1 0-2 0-3h1l5 4-2 2z" class="V"></path><path d="M344 904h1l1 1-1 1c1 1 0-1 1 1l1 1 1-1h2l-1 2 1 1 1 1-2 2c-1-1-2-1-2-2l-1-2h-1v1c-1 0 0 0-1-1 1-1 0-1 0-2v-3z" class="p"></path><path d="M344 907c0 1 1 1 0 2 1 1 0 1 1 1 1 2 2 3 1 6h-7c-1 0-1-1-2-2 0-1-1-1-1-2h-1l1-2-1-2h0 4c1 1 2 1 3 1 1-1 2-1 2-2z" class="b"></path><path d="M312 914l1 1v1h-1l2 2h1c3 0 7 1 10 1h1c1 1 2 1 4 1h0 3c3 1 5 0 7 1 1-1 1-1 2-1l14 1h4 2c-1 1-3 1-5 1-1 1-2 1-3 1-1 1-1 1-2 1v2h-1c-3 0-5-1-7-1l-1-1c-3 2-6 1-9 1-1 0-4 0-5-1v-1h-1-1c-2 1-4 1-6 1v1h2c-2 2-4 2-6 2v1c0 1-1 1-2 2h0l-5-1h0-5l3-1 2-2h-3l-2-1 1-1c1-1 1-1 1-2l-4-1h0c0-1 0-2-1-3l-1 1-1-1v-1c1-1 4-1 6-2h5l1-1z" class="R"></path><defs><linearGradient id="K" x1="308.583" y1="914.182" x2="302.151" y2="918.655" xlink:href="#B"><stop offset="0" stop-color="#1c1e1c"></stop><stop offset="1" stop-color="#383434"></stop></linearGradient></defs><path fill="url(#K)" d="M312 914l1 1v1h-1l2 2c-4-1-8 0-11 0h-1l-1 1-1-1v-1c1-1 4-1 6-2h5l1-1z"></path><path d="M315 918c3 0 7 1 10 1h1c1 1 2 1 4 1-1 1-2 1-3 1-3-1-5 0-8 0-2-1-5 0-8-2h4v-1z" class="k"></path><path d="M320 923c2-1 4-1 6-1 3 0 5 1 8 1 1 0 4-1 5 0 1 0 1 1 3 1l1-2c3 1 6 1 9 1h2c-1 1-1 1-2 1v2h-1c-3 0-5-1-7-1l-1-1c-3 2-6 1-9 1-1 0-4 0-5-1v-1h-1-8z" class="n"></path><path d="M303 921h7 3c0 1 0 2 1 2h6 0 8-1c-2 1-4 1-6 1v1h2c-2 2-4 2-6 2v1c0 1-1 1-2 2h0l-5-1h0-5l3-1 2-2h-3l-2-1 1-1c1-1 1-1 1-2l-4-1h0z" class="G"></path><path d="M308 928l7-1c0 1 0 1-1 2h-4-5l3-1z" class="Z"></path><path d="M303 921h7l2 2h0l1 1c-1 1-2 2-3 2h-3l-2-1 1-1c1-1 1-1 1-2l-4-1h0z" class="F"></path><path d="M363 891c1-1 3-2 4-3 2 0 3 1 5 1v1c3 2 3 1 6 2l-2 2h1 2c0 1 1 2 1 4l-2 2c1 0 2 1 1 1 0 1-1 2-1 3v1l-1 2c1 0 2 1 1 2h-2c0 2-1 2-2 3h-1c0-2 0-2 1-3h-1v-1l-1-1c-1 0-1 1-2 1s-2-1-3 0l1 1v2l-1 1-1-1v1c-3 2-4-1-7-2v-1l-2-2-2-3c-1 0-1 0-1-1l1-2 4-4c1-2 3-4 4-6z" class="a"></path><path d="M357 907c2-2 2-2 5-2v2 1c-1 0-2 0-3 1l-2-2z" class="l"></path><path d="M373 908l1-1c0-1-2-1-2-2 0 0 0-1 1-2h-2 0c2-3 4-5 6-7l1 1v2 1h0c1 0 2 1 1 1 0 1-1 2-1 3v1l-1 2c1 0 2 1 1 2h-2c0 2-1 2-2 3h-1c0-2 0-2 1-3h-1v-1z" class="d"></path><path d="M378 899v1l-1 1 1 1v1h-4v-1l4-3z" class="l"></path><path d="M363 891c1-1 3-2 4-3 2 0 3 1 5 1v1h0l-2 2v1c1 0 2-1 3 0l-1 1v1c1 1 1 1 2 1 0 2-2 3-3 4-1 0-1 0-2-1v2h-1c-1-2 0-4-1-5-1 0-1 0-1-1h-1v1l-1 1h0l1 2c-1 1-1 1-3 1h0c-1 1-2 1-3 2h3c1-1 1-1 2 0v1 1c-1-1-2-1-3-1-2 1-4 1-6 1-1 0-1 0-1-1l1-2 4-4c1-2 3-4 4-6z" class="V"></path><path d="M362 900l-1-1c1-2 2-2 3-2l1 2c-1 1-1 1-3 1h0zm1-9c1-1 3-2 4-3 2 0 3 1 5 1v1h0l-2 2v1c1 0 2-1 3 0l-1 1h-2c-2 0-1 1-3 1v-2h-2l-2-2z" class="L"></path><path d="M362 921h5l1 1 1 1h2 6v1l-1 1h6 2c2 0 4-1 6 0v1h1c2-1 4 0 7-1l-1-1h-3 0 5 6l-1 2h-1l1 1h1l1 1c0 1 1 2 3 2h1l1 1c1 0 2 0 3-1l1-1v1h2 3c2 0 4 1 6 0v3h-2v1c-2 1-3 1-5 1-2 1-4 1-5 0-1 2-1 2-3 3h0l-2 1c-1-1-1-2-2-2h-1c-2-1-2-1-4 0l-1-1v-1h-3-6l-5 1c-2 1-5 1-7 1v1h-8v-2h0l-5-1h-2c-2 0-4 0-5-2v-1h2l3-1h-2v-1c-1-2-5-1-7-2l1-1c-1-1-3-1-4-1h-2 1v-2c1 0 1 0 2-1 1 0 2 0 3-1 2 0 4 0 5-1z" class="l"></path><path d="M376 930c4 2 12-2 16 2h-6-9-5c0-1 0-1 1-2h3z" class="V"></path><path d="M390 926h1 1l-2 2h3c1 1 1 0 1 1-5 0-10 0-14-1l-1-1c2 0 4-1 6-1h5z" class="R"></path><path d="M390 926h1 1l-2 2c-2 0-3 0-5-2h5z" class="j"></path><path d="M409 933h0l-1 1 6 1c-1 2-1 2-3 3h0l-2 1c-1-1-1-2-2-2h-1c-2-1-2-1-4 0l-1-1v-1h-3c1-1 3-1 4-1h1 1c2 0 3-1 5-1z" class="L"></path><path d="M411 938c-1-1-3-2-4-3v-1h1l6 1c-1 2-1 2-3 3z" class="n"></path><path d="M394 924h5 6l-1 2h-1l1 1h1l1 1h-3l1 1h1 0c-1 1-2 1-4 1-3 1-4 1-7-1 0-1 0 0-1-1h-3l2-2h-1c2-1 4 0 7-1l-1-1h-3 0z" class="a"></path><path d="M394 924h5 6l-1 2h-1-11-1c2-1 4 0 7-1l-1-1h-3 0z" class="d"></path><path d="M406 928c0 1 1 2 3 2h1l1 1c1 0 2 0 3-1l1-1v1h2 3c2 0 4 1 6 0v3h-2v1c-2 1-3 1-5 1-2 1-4 1-5 0l-6-1 1-1h0c-1 0-2 0-2-1l2-1h0c-2 0-5-1-8-1 2 0 3 0 4-1h0-1l-1-1h3z" class="o"></path><path d="M409 933c6-1 10-1 15 0v1c-2 1-3 1-5 1-2 1-4 1-5 0l-6-1 1-1z" class="S"></path><path d="M365 931c2 1 4 1 7 1h0 5 9 6 1v1c-1 0-1 1-1 1v1l-5 1c-2 1-5 1-7 1v1h-8v-2h0l-5-1h-2c-2 0-4 0-5-2v-1h2l3-1z" class="Z"></path><path d="M372 932h5c1 1 2 1 3 2h2v1h-1c-1 1-2 1-2 2h1v1h-8v-2h0l-5-1h6c1 0 1-1 2-2l-3-1h0z" class="E"></path><path d="M365 931c2 1 4 1 7 1l3 1c-1 1-1 2-2 2h-6-2c-2 0-4 0-5-2v-1h2l3-1z" class="B"></path><path d="M362 921h5l1 1 1 1h2 6v1l-1 1h6 2c2 0 4-1 6 0v1h-5c-2 0-4 1-6 1h-7v1h1c1 0 2 1 3 2h-3c-1 1-1 1-1 2h0c-3 0-5 0-7-1h-2v-1c-1-2-5-1-7-2l1-1c-1-1-3-1-4-1h-2 1v-2c1 0 1 0 2-1 1 0 2 0 3-1 2 0 4 0 5-1z" class="b"></path><path d="M371 923h6v1l-1 1h-2c-1 0-2 0-3 1h-1-1c-2-1-4-1-6-2 2-1 5 0 8-1z" class="Z"></path><path d="M362 921h5l1 1 1 1h2c-3 1-6 0-8 1-2 0-6 0-8 1-1 0-2 0-3 1v-2c1 0 1 0 2-1 1 0 2 0 3-1 2 0 4 0 5-1z" class="V"></path><path d="M406 898c2-1 3-2 5-1v6h0c2 0 5 0 7-1v1 1 1 1h8v4h0l1 1c1 1 1 4 2 5l1 4c0 1 2 3 3 4h4 4l-1 1c-3 1-6 0-8 1v1 1h1v-1l1 1h3c2 0 5-1 8 0h0v1c-1 0-5 0-6 1h0 0c-1 0-2 0-3 1h-1c-2 1-3 1-5 2h-4v-3c-2 1-4 0-6 0h-3-2v-1l-1 1c-1 1-2 1-3 1l-1-1h-1c-2 0-3-1-3-2l-1-1h-1l-1-1h1l1-2h-6-5l-1-1v-1h3c1-1 1-1 2-1h0l1-1-1-2c-1-1-1-2-1-3-1-1-1-3-1-4h0v-8-2c1 1 1 2 2 2h5l2-2c1-1 1-2 1-3z" class="e"></path><path d="M437 928c2 0 5-1 8 0h0v1c-1 0-5 0-6 1h0-3c-2-1-3-1-5-1h-3l1-1h1c2 1 2 1 4 1 1 0 2-1 3-1z" class="k"></path><path d="M426 930h6l3 1c-2 1-3 1-5 2h-4v-3z" class="R"></path><path d="M424 920l1 1h2 0l-2 1c1 1 2 0 3 1 1 0 0 1 2 1h-1c-2 1-3 1-5 1l-1 1s-1 0-1 1h0 2 4c-3 2-6 0-9 1v1l-1-1h1c-1-1-2-1-3-2l2-2h1v-1l1-2c2 0 3 0 4-1z" class="P"></path><path d="M420 923h1l2 2-1 1h-2l-1-1 1-1v-1z" class="e"></path><path d="M426 910l1 1c1 1 1 4 2 5l1 4c0 1 2 3 3 4h4 4l-1 1c-3 1-6 0-8 1v1 1h1v-1l1 1h3c-1 0-2 1-3 1-2 0-2 0-4-1l1-1h-3-4-2 0c0-1 1-1 1-1l1-1c2 0 3 0 5-1h1c-1-1-1-2-1-3-1-2-1-4-2-6 0-2 0-3-1-5z" class="c"></path><path d="M410 923l5 1-1 1h-2l-1 1c1 1 3 0 4 1l-2 1v1h2l-1 1c-1 1-2 1-3 1l-1-1h-1c-2 0-3-1-3-2l-1-1h-1l-1-1h1l1-2h1l4-1z" class="R"></path><path d="M404 926c2-1 4-1 5-1v1c-1 1-1 1-3 1h-1-1l-1-1h1z" class="m"></path><path d="M418 902v1 1 1 1h8v4h0c1 2 1 3 1 5 1 2 1 4 2 6 0 1 0 2 1 3-2 0-1-1-2-1-1-1-2 0-3-1l2-1h0-2l-1-1c-1 1-2 1-4 1l-1 2-1-1c-1-1-2-2-3-4-1-3-3-7-4-11v-4c2 0 5 0 7-1z" class="M"></path><path d="M418 916l4 1h0l-2 1v1h3c2-1 2-1 4-1-1 1-2 1-3 1v1c-1 1-2 1-4 1l-1 2-1-1 1-1-2-3c0-1 1-1 1-2z" class="k"></path><path d="M418 910l2 2-1 1c1 1 1 1 2 1v1h-1c-1 0-1 1-2 1 0 1-1 1-1 2l2 3-1 1c-1-1-2-2-3-4l1-1v-1c-1-1-1-1-1-2s0-1-1-1v-2h2v2l1-1c0-1 0-1 1-2z" class="R"></path><path d="M418 902v1 1 1 1l2 2c-2 0-2 0-2 2-1 1-1 1-1 2l-1 1v-2h-2v2c1 0 1 0 1 1s0 1 1 2v1l-1 1c-1-3-3-7-4-11v-4c2 0 5 0 7-1z" class="j"></path><path d="M406 898c2-1 3-2 5-1v6h0v4c1 4 3 8 4 11 1 2 2 3 3 4l1 1v1h-1-3l-5-1-4 1h-1-6-5l-1-1v-1h3c1-1 1-1 2-1h0l1-1-1-2c-1-1-1-2-1-3-1-1-1-3-1-4h0v-8-2c1 1 1 2 2 2h5l2-2c1-1 1-2 1-3z" class="c"></path><path d="M406 898c2-1 3-2 5-1v6h0v4h-3l2-1v-3c-1 0-2 0-2 1-1 1-1 3-2 4h1c0 1 1 2 1 3-1 1-1 1-1 2h-2c0-1-1-2-1-3v-1h0v-6s1-1 1-2c1-1 1-2 1-3z" class="Q"></path><path d="M405 916c2 0 2-1 3 0v4h-1c1 1 2 1 4 1v1h-1v1l-4 1h-1-6-5l-1-1v-1h3c1-1 1-1 2-1h0l1-1-1-2c1 0 1-1 3-1h4l1-1h-1z" class="P"></path><path d="M399 924l3-1 1-1c1-1 1-1 3-1v2 1h-1-6z" class="Q"></path><path d="M401 917h4c0 1 0 1 1 2l-1 1c-1-1-4-1-5 0h-1l-1-2c1 0 1-1 3-1z" class="U"></path><path d="M396 903v-2c1 1 1 2 2 2h5l2-2c0 1-1 2-1 2v6h0v1c0 1 1 2 1 3l-1 1c1 0 1 1 1 2h1l-1 1h-4c-2 0-2 1-3 1-1-1-1-2-1-3-1-1-1-3-1-4h0v-8z" class="p"></path><path d="M396 903v-2c1 1 1 2 2 2h5l2-2c0 1-1 2-1 2 0 1-1 2-1 3-1 1-3 0-4 0l-3-3z" class="l"></path><path d="M396 911h3c2 0 1 0 2 1-1 0-1 0-1 1h3v-2l1-1c0 1 1 2 1 3l-1 1c1 0 1 1 1 2h1l-1 1h-4c-2 0-2 1-3 1-1-1-1-2-1-3-1-1-1-3-1-4z" class="o"></path><path d="M397 915h1c1 0 2 0 3-1h1c-1 1-2 1-2 2l1 1c-2 0-2 1-3 1-1-1-1-2-1-3z" class="V"></path><path d="M415 857v2h4l1 1-1 1c-1 0-1 1-2 1 0 2 0 2 1 3 0 1-2 3-3 3l1 1h2c1-1 3-1 5-2h1s0-1 1 0h1v-2h3c0-1 1-1 2-1l-2 2h-2v2c-1 0-2 1-2 1-1 2-1 2-3 3h-1l-1 2 2-1 1 1-7 9-1 2c-1 3-2 4-2 7-1 2-2 3-2 5h0c-2-1-3 0-5 1 0 1 0 2-1 3l-2 2h-5c-1 0-1-1-2-2v2 8h0c0 1 0 3 1 4 0 1 0 2 1 3l1 2-1 1h0c-1 0-1 0-2 1h-3l-1-1h2c-1-1-3-2-3-3-2-2-1-5-1-7-1-2-2-7-1-9s0-4 1-6c0-1 1-2 1-4l1-2h0c1-3 2-5 4-8l1-2 3-4 1-2h0l2-2h0v-1l2-1h0l4-4c1-3 3-5 4-9h2z" class="a"></path><path d="M391 901v-1s0-1 1-2c1 0 1 0 2 1l1 1c0 1 0 1-1 3l-1-2h-2z" class="R"></path><path d="M400 879h3v1l-2 4v1h0c-1 1-2 2-2 4 0 0-1 1-1 2-2 0-2 0-3-1l1-1h1 0c-1-1-2-1-3-1v1c0 1-1 1-1 1-1 2 2 0-1 2v-2h0c1-3 2-5 4-8l1-2h3v-1z" class="V"></path><defs><linearGradient id="L" x1="400.718" y1="917.558" x2="389.313" y2="904.863" xlink:href="#B"><stop offset="0" stop-color="#0b0b08"></stop><stop offset="1" stop-color="#2e2c2e"></stop></linearGradient></defs><path fill="url(#L)" d="M398 896c0 1 0 2-1 3 0 1 0 1-1 2v2 8h0c0 1 0 3 1 4 0 1 0 2 1 3l1 2-1 1h0c-1 0-1 0-2 1h-3l-1-1h2l1-1c1-2-1-1-1-2v-1s-1 0-1-1c1 0 1 0 1-1l-1-1c0-1 1-1 1-2-1-1-1-2-2-3h1c0-1-1-1-1-3s0-3-1-5h2l1 2c1-2 1-2 1-3 1-2 1-3 3-4z"></path><path d="M405 870c1-1 2-1 3 0h1 1v1h-1l2 1v2l-2 1-3 4v1l-1-2-1 1-1 1v-1h-3v1h-3l3-4 1-2h0l2-2h0v-1l2-1z" class="n"></path><path d="M403 872h0c1 1 2 1 3 1h1c-1 1-2 2-2 4l-1-1v-1h0l-3-1 2-2z" class="V"></path><path d="M409 871l2 1v2l-2 1-3 4c-1-1-1-1-1-2 0-2 1-3 2-4l2-2z" class="R"></path><path d="M401 874h0l3 1h0v1l1 1c0 1 0 1 1 2v1l-1-2-1 1-1 1v-1h-3v1h-3l3-4 1-2z" class="p"></path><path d="M400 879h0v-1h2v-2h2l1 1c0 1 0 1 1 2v1l-1-2-1 1-1 1v-1h-3z" class="d"></path><path d="M415 857v2h4l1 1-1 1c-1 0-1 1-2 1 0 2 0 2 1 3 0 1-2 3-3 3-1 2-2 3-3 4 0 1 0 1-1 2h0v-2l-2-1h1v-1h-1-1c-1-1-2-1-3 0h0l4-4c1-3 3-5 4-9h2z" class="V"></path><path d="M415 868l1 1h2c1-1 3-1 5-2h1s0-1 1 0h1v-2h3c0-1 1-1 2-1l-2 2h-2v2c-1 0-2 1-2 1-1 2-1 2-3 3h-1l-1 2 2-1 1 1-7 9-1 2c-1 3-2 4-2 7-1 2-2 3-2 5h0c-2-1-3 0-5 1 0 1 0 2-1 3l-2 2h-5c-1 0-1-1-2-2 1-1 1-1 1-2 1-1 1-2 1-3l1-2c0-2 0-3 2-5 0-2 2-3 2-5 1-1 0-1 1-1 1-1 1-2 2-3v-1l3-4 2-1h0c1-1 1-1 1-2 1-1 2-2 3-4z" class="M"></path><path d="M420 874l2-1 1 1-7 9h-1c-1 1-2 1-2 1h-1c0-1 1-2 2-3 1 0 1 0 2-1 0-2 1-2 2-3l1-1v-2h1z" class="g"></path><path d="M415 868l1 1h2c1-1 3-1 5-2h1s0-1 1 0h1v-2h3c0-1 1-1 2-1l-2 2h-2v2c-1 0-2 1-2 1-1 2-1 2-3 3h-1l-1 2h-1-1c-1 1-2 1-3 2v1c-1 1-2 2-2 3-3 0-3 0-4-1v-1-3l2-1h0c1-1 1-1 1-2 1-1 2-2 3-4z" class="G"></path><path d="M412 884h1s1 0 2-1h1l-1 2c-1 3-2 4-2 7-1 2-2 3-2 5h0c-2-1-3 0-5 1 0 1 0 2-1 3l-2 2h-5c-1 0-1-1-2-2 1-1 1-1 1-2 1-1 1-2 1-3l1-2c0-2 0-3 2-5l2 2 1-1c1 0 1 0 2-1 0 0-1 0-2-1h3c0-2-1-1-2-2h1 2c1 1 1-1 2-1h2v-1z" class="Y"></path><path d="M412 884h1s1 0 2-1h1l-1 2c-1 3-2 4-2 7-1 2-2 3-2 5h0c-2-1-3 0-5 1 1-1 1-3 1-4v-1c2-2 2-4 4-6 0-1 0-1 1-2v-1z" class="f"></path><path d="M373 842h5 3l1-1c1 0 3 1 3 1v1 1h-1c-1 1-1 2-1 2v2l2-1c0 1 0 2 1 3 2 0 4 1 6 2v1h1 0l7 7 4 4 2 1 3 1-4 4h0l-2 1v1h0l-2 2h0l-1 2-3 4-1 2c-2 3-3 5-4 8-1-1-1-1-3-1 0 0-1 0-1 1-1 1 0 2-2 3l-2-1h0c-1-1-2-1-3-1s-2 0-3 1c-3-1-3 0-6-2v-1c-2 0-3-1-5-1-1 1-3 2-4 3-1 2-3 4-4 6l-2-2h-1c0-1 0-2-1-2v1h-2l-1 1 2 1h-1-1v2l-2 1h1l2 1h0l-1 1h-2c-2 1-3 2-5 2v-2c0-2 0-4-1-6 0-2 0-5 1-7v-5c1-1 1-3 1-4 1-4 3-8 5-12 1-2 3-4 5-7h0c1-2 2-2 3-4 0-1 1-1 2-2v-1l2-3 2-2 3-3c0-1 2-3 2-4l3 1z" class="Z"></path><path d="M366 864l2 1v2l-1 2h1v-1c1 2 1 3 1 4-2 1-3 1-4 2-1 2-1 2-2 3h-1l-2 2v2c-1 1-1 2-1 2 0 1-1 1-1 1l-2-1 3-3-1-2v-1h2c1-1 1-2 1-3 1 0 2 0 3-1l-2-1v-1c1-1 2-1 3-1v-1l-3-1h0c1 0 3 0 3-1l-1-1 2-2z" class="e"></path><path d="M354 877l1-1h1l1 3-1 1-2 3h1v1c-1 0-2 0-2 1l1 2h-8v1h5v1h-2c-1 1 0 2-1 3h-2 0c1 1 1 1 2 1v1h-1c-1 0-2 0-3 1 0-2 0-5 1-7v-5l1 1h6v-1h-5v-1c1 0 2 0 2-1v-1c2-2 3-1 5-2v-1z" class="O"></path><path d="M356 860l8 1v1c-1 1-2 1-4 1l-1-1h-2c0 1 0 1 1 1 3 0 4 0 6 1h1 1l-2 2 1 1c0 1-2 1-3 1h0l3 1v1c-1 0-2 0-3 1 0-1-2-1-3-1 1 0 1 1 1 2 1 0 1-1 2 1h-4l-1 1 3 2c-1 1-2 0-4 0h-1l-1 1v1c-2 1-3 0-5 2v1c0 1-1 1-2 1v1h5v1h-6l-1-1c1-1 1-3 1-4 1-4 3-8 5-12 1-2 3-4 5-7h0z" class="H"></path><path d="M354 877c-2 0-3 0-5-2 0-1 0-2 1-3l1 1c-1 1-1 1-1 2h2 0c1-1 1-2 1-3-1-2-1-1-2-2l1-1h1 1c1 1 3 0 5 1 1 0 1 1 1 2 1 0 1-1 2 1h-4l-1 1 3 2c-1 1-2 0-4 0h-1l-1 1z" class="I"></path><path d="M369 872c1 1 1 1 3 2l1 1c-2 1-4 2-5 4l1 1 3-2v1 1l-1 2 1 1-2 1c1 1 0 1 1 1 0 1 0 1 1 2h-2 1 1l1 2h-1c-2 0-3-1-5-1-1 1-3 2-4 3-1 2-3 4-4 6l-2-2h-1c0-1 0-2-1-2h-1l-1-1c3-1 2-1 3-2v-2l1-1c1-1-1 0-1-2l2-1s1 0 1-1c0 0 0-1 1-2v-2l2-2h1c1-1 1-1 2-3 1-1 2-1 4-2z" class="c"></path><path d="M368 879l1 1 3-2v1 1l-1 2 1 1-2 1c1 1 0 1 1 1 0 1 0 1 1 2h-2c-1-2-1-3-2-4h0c0-1-1-2 0-4z" class="R"></path><path d="M373 842h5 3l1-1c1 0 3 1 3 1v1 1h-1c-1 1-1 2-1 2v2l-5 4-6 6-3 3-3 3h-1-1c-2-1-3-1-6-1-1 0-1 0-1-1h2l1 1c2 0 3 0 4-1v-1l-8-1c1-2 2-2 3-4 0-1 1-1 2-2v-1l2-3 2-2 3-3c0-1 2-3 2-4l3 1z" class="K"></path><path d="M363 858c2 0 4-1 6 1-2 0-3 0-4 1-2 0-4 0-5-1h-1c2-1 3-1 4-1z" class="H"></path><path d="M361 854c2 0 4 2 6 2h0 2v1h-1l-9-1c0-1 1-1 2-2z" class="E"></path><path d="M363 850l6 1h4c-1 1-1 2 0 3-1 2-2 2-4 3v-1h-2 0c-2 0-4-2-6-2v-1l2-3z" class="H"></path><path d="M363 850l6 1c-1 1-2 1-3 1l-1 1h-4l2-3z" class="B"></path><path d="M373 842h5 3l1-1c1 0 3 1 3 1v1 1h-1c-1 1-1 2-1 2v2l-5 4c-1 0-1-1-2-1l-3 3c-1-1-1-2 0-3h-4l-6-1 2-2 3-3c0-1 2-3 2-4l3 1z" class="S"></path><path d="M365 848l2 1v1h1l1-1h-1c1-1 2-1 3-1 1 1 2 2 3 2l-1 1h-4l-6-1 2-2z" class="I"></path><path d="M373 842h5 3l1-1c1 0 3 1 3 1v1h-12v1h3v1h-8c0-1 2-3 2-4l3 1z" class="D"></path><path d="M374 850h1v-2c1-1 2-1 3-1h1v-1-1c2 0 2 1 4 1v2l-5 4c-1 0-1-1-2-1l-3 3c-1-1-1-2 0-3l1-1z" class="C"></path><path d="M385 847c0 1 0 2 1 3 2 0 4 1 6 2v1h1 0l7 7-2 2c0 1 1 1 1 3-2-1-1-1-2-2v-2l-1-1h-1v1h-1v-2c0-1 0-1-1-2h-2l-3 4h-1 0c-2 2-3 4-5 5l-2 1c-1-1-1-3-3-4l-1-1-2 2c1 2 2 3 3 4h-1l-1 1h-1l-2 1v2 1 1c-2-1-2-1-3-2 0-1 0-2-1-4v1h-1l1-2v-2l-2-1h-1 1l3-3 3-3 6-6 5-4 2-1z" class="Q"></path><path d="M386 850c2 0 4 1 6 2v1h1 0l7 7-2 2c0 1 1 1 1 3-2-1-1-1-2-2v-2l-1-1h-1v1h-1v-2c0-1 0-1-1-2h-2l-3 4h-1c-1-1 0-1-1-1l-1-1c1-1 1-2 1-3l-2-1h0c0 1 1 2 1 3l-2 2c-1-1-2-2-3-2v2c-1-1-1-1-2-1 1-2 3-3 3-5 2-1 3-3 5-4z" class="o"></path><path d="M374 869h1l1-1h1c-1-1-2-2-3-4l2-2 1 1c2 1 2 3 3 4l2-1v2l1 2v2 2l2-1 1 1c-2 1-4 2-5 5h0 1 1v1c2 0 2 0 4 1 1 1 2 1 3 1l1 1-1 1c-1 1-1 1-1 3v2s-1 0-1 1c-1 1 0 2-2 3l-2-1h0c-1-1-2-1-3-1s-2 0-3 1c-3-1-3 0-6-2v-1h1l-1-2h-1-1 2c-1-1-1-1-1-2-1 0 0 0-1-1l2-1-1-1 1-2v-1-1l-3 2-1-1c1-2 3-3 5-4l-1-1v-1-1-2l2-1z" class="f"></path><path d="M377 876l2-3h3c-1 2-1 3-2 5-1-1-1 0-2-1 0-1 0-1-1-1z" class="j"></path><path d="M373 887l1-1c1 0 2 1 2 1l2-2 1 1v1h-1c2 1 2 1 3 2s2 0 3 1v1 1c-1-1-2-1-3-1s-2 0-3 1c-3-1-3 0-6-2v-1h1l-1-2h-1-1 2 1z" class="U"></path><path d="M372 887h1c1 1 1 2 3 2 1 0 1 1 2 1 1 1 2 1 3 1-1 0-2 0-3 1-3-1-3 0-6-2v-1h1l-1-2h-1-1 2z" class="P"></path><path d="M373 875c2 0 3 2 5 3v1l-1 1c1 0 0 0 1 1l-1 1 2 1v1h-3v-1h0c0-1-1-2-2-3l-1 3h-1v-3-1-1l-3 2-1-1c1-2 3-3 5-4z" class="V"></path><path d="M374 869h1l1-1h1c-1-1-2-2-3-4l2-2 1 1c2 1 2 3 3 4 0 2 1 3 1 4 1 1 1 1 1 2h-3l-2 3c-1-2-2-5-3-7z" class="p"></path><path d="M381 881c1 0 2-1 2-1 2 0 2 0 4 1 1 1 2 1 3 1l1 1-1 1c-1 1-1 1-1 3v2c-1-1-3-1-4-1-1-2-3-3-4-4h-1c0-1 0-2 1-3z" class="d"></path><path d="M387 883c1 1 2 1 3 1-1 1-1 1-1 3v2c-1-1-3-1-4-1l2-2v-3h0z" class="U"></path><path d="M381 881c1 0 2-1 2-1 2 0 2 0 4 1 1 1 2 1 3 1l1 1-1 1c-1 0-2 0-3-1v-1h-3c-1 0-1 1-3 1v-2z" class="V"></path><path d="M388 861l3-4h2c1 1 1 1 1 2v2h1v-1h1l1 1v2c1 1 0 1 2 2 0-2-1-2-1-3l2-2 4 4 2 1 3 1-4 4h0l-2 1v1h0l-2 2h0l-1 2-3 4-1 2c-2 3-3 5-4 8-1-1-1-1-3-1v-2c0-2 0-2 1-3l1-1-1-1c-1 0-2 0-3-1-2-1-2-1-4-1v-1h-1-1 0c1-3 3-4 5-5l-1-1-2 1v-2-2l-1-2v-2c2-1 3-3 5-5h0 1z" class="d"></path><path d="M393 876c2-2 0-4 1-5 0-1 1-2 2-2l-1 2h1c0 1 1 1 1 2h0v4h-2l-2-1z" class="a"></path><path d="M382 866c2-1 3-3 5-5 1 1 2 1 2 3-2 1-3 1-4 3h1v-1h3 1c-1 2-4 3-6 3v-1h-2v-2z" class="b"></path><path d="M388 861l3-4h2c1 1 1 1 1 2v2 1l-1 1c1 0 1 0 1 1l-1 1c-1 0-1-1-2-2 0-1-2-2-3-2z" class="Y"></path><path d="M386 874c2 0 3 0 4 1v4l-1 1c-1 0-3-1-4-1h-2-1-1 0c1-3 3-4 5-5z" class="X"></path><path d="M385 879l1-2h1c1 1 1 1 1 2h2l-1 1c-1 0-3-1-4-1z" class="p"></path><path d="M397 873l3 3-3 4-1 2c-2 3-3 5-4 8-1-1-1-1-3-1v-2c0-2 0-2 1-3l1-1-1-1 1-1c0-1 0-1 1-1 0 0 1-1 1-2l-2-3h1l1 1 2 1h2v-4z" class="k"></path><path d="M397 873l3 3-3 4-1 2-2-1v-3l1-1h2v-4z" class="P"></path><path d="M395 861v-1h1l1 1v2c1 1 0 1 2 2 0-2-1-2-1-3l2-2 4 4 2 1 3 1-4 4h0l-2 1v1h0l-2 2h0l-1 2-3-3h0c0-1-1-1-1-2h-1l1-2h0c2-3 0-6-1-8z" class="e"></path><path d="M401 867c1 2 1 2 2 3v1 1h0l-1-2h-2l-1 2-1-2 2-2 1-1z" class="P"></path><path d="M399 872l1-2h2l1 2-2 2h0c-1 0-1 0-2-1v-1z" class="h"></path><path d="M401 867h0l3-3 2 1 3 1-4 4h0l-2 1v-1c-1-1-1-1-2-3z" class="g"></path><path d="M406 865l3 1-4 4-1-1v-2l2-2z" class="P"></path><path d="M614 653c8 1 16 1 24 3h2l1 2-16-3v1l6 1c3 1 5 1 8 2h0c-1 1-1 1-2 1l2 1v2 34 14l-4-1-1 1 5 1v2h0c-2 4-5 8-7 11v1c-3 4-2 25-1 31v18h-1-2v-2-3 1c-1 2-1 3-2 4v1l-1-1h1l-1-6h0c0 2 0 5-1 6l-1 1h-2l1 2h3v1l1 1c0 1 0 2-1 3s-2 1-4 1c-3-1-4-1-7 0h-1c-1 0-2-1-3-1h0c0-1 2-1 3-1v-1c-1 0-2 0-4-1l-55-1-1-3c0-1 0-2-1-2l2-1 1 1c1-1 2-1 2-3l-2-2c0 1 0 0-1 0v-1-3c0-1 1-2 1-3 0-2 0-2 2-4-1 0-2-1-4-1-1 1-1 1-3 1l1-15 1 1h1l1-3h0c1-2 2-6 2-8l3-9c-2-1-2-1-2-2-1 0-1 5-2 6h0c0-4 1-8 2-12l4-12c0-1-1-2-2-2l2-2-1-2 2-3 4-3 1-1s1-1 2-1c0-1 0 0 1-1l1 1c1-1 2-1 2-1l1-1c-3-1-5 0-7-2l3-1c1-1 3-1 4-1h0l-1-1c-1 0-3 0-4-1h0c1-3 0-7 1-9 1 0 2 1 3 0 1 0 1 0 2 1 0 1 0 0 1 1v-1h3 1c-1-1-1-1-1-2 1 0 2 0 3-1v-1c2 1 5 0 7 1 1 2 2 2 4 3l1 1h0c0-1 1-2 1-3l1 1c2 1 3 1 5 3 1 0 1 0 2-1l1 1c-1 1-1 0-1 1l1 2c1-1 2-2 4-2 1 0 1 0 2-1v-1h-1v-1-1h-1-2l-1-1s-1 0-1-1l-6-3c1-2 2-2 4-3 1 0 1-1 1-1h-7c0-1 0-1-1-1l1-2c-3 1-5 0-7-2-1-1-2 0-3 0l-2-2 1-2h0c4 0 7-1 10-1h3l16-1z" class="j"></path><path d="M579 773v1h-2v-3l1-1v-1h1v4z" class="Z"></path><path d="M578 762h4 1c0 1-1 2-1 4h-2v1h2v2h-1c-1 1-1 2-1 3l-1 1v-4c0-3 0-4-1-7z" class="g"></path><path d="M582 762c1-1 2-1 3 0h1 0c-1 4 0 8 1 12h0-4l-1-5v-2h-2v-1h2c0-2 1-3 1-4h-1z" class="X"></path><path d="M612 777c2 1 4 1 6 1h2l1 1 1-1h3v1l1 1c0 1 0 2-1 3s-2 1-4 1c-3-1-4-1-7 0h-1c-1 0-2-1-3-1h0c0-1 2-1 3-1 0 0 0 1 1 1h1v-2-1l2 2h1v-2c-2-1-5-1-6-1l-1-1 1-1z" class="D"></path><path d="M622 778h3v1 3c-1 1-1 1-2 1-1-1-2-3-2-4l1-1z" class="W"></path><path d="M603 771v-3l-1-1c0-2 0-3 1-4h2 3c2 0 4 0 5 1 1 4 0 8 1 11 1 0 1 0 1-1 1 0 1 0 2 1v-4h1c1 1 1 3 1 4 1 0 2-1 3-1v-1c-1-1 0-3 0-4h0c2 2-1 5 2 6l-1 1h-2l1 2-1 1-1-1h-2c-2 0-4 0-6-1h6 0v-1h-2-1-2-2v-1c1-2 0-4 0-6 1-1 0-2 0-4h0c-2 1-1 4-1 6-1 1-1 0-2 1h-2l-1 1-2-2z" class="l"></path><path d="M578 753c2 0 1-2 2-4 1 1 1 3 1 5h1v-5l3 8v1l-1 2h1l-8 1c-4-1-8-1-11-1l-1 1 1 1c0 1 1 5 1 7h-1 0l-1-1h0c-1 1 0 2-1 3l-1-1v-1l-2 1v-2l-1-1c0-1 1-4 0-5-1 1-1 2-1 3 0 3 0 3-1 5-1-1-1-1-2-1h-1c0 1 0 0-1 0v-1-3c0-1 1-2 1-3 0-2 0-2 2-4 1-1 1-2 1-3 0 2 0 3-1 4 1 1 5 1 6 1h16l-1-7z" class="a"></path><path d="M598 760c2 0 5 1 7 0l-1-1 1-1c3-1 6-1 9 0h-4l-1 2-1-1h-1v1h0c1 0 1 1 1 1v1 1h-3-2c-1 1-1 2-1 4l1 1v3l-1 1h-1c0-1 0-2 1-2l-1-1h-2v3h-3v1l1 1h-10 0c-1-4-2-8-1-12h0-1c-1-1-2-1-3 0h-4l-1-1 8-1h3v1h0l2 1c1-1 2-1 3-1 2 1 3 1 4 0l1-1h0z" class="M"></path><path d="M589 763l2-1c1 2 0 6 0 8l1 2c-1 1-1 0-3 0-1 1-1 2-2 2-1-4-2-8-1-12l2 1h1z" class="L"></path><path d="M588 763h1v5h1c-1 1-1 2-2 2h0v-7z" class="F"></path><path d="M598 760c2 0 5 1 7 0l-1-1 1-1c3-1 6-1 9 0h-4l-1 2-1-1h-1v1h0c1 0 1 1 1 1v1 1h-3-2c-1 1-1 2-1 4l1 1v3l-1 1h-1c0-1 0-2 1-2l-1-1h-2v3h-3v-3c1 0 2 0 3-1v-2-3c2-2 4 0 5-1v-1h-7l1-1h0z" class="b"></path><path d="M553 776c6 0 12 0 18-1l26 1c4 0 9-1 12 1v3h0l-55-1-1-3z" class="D"></path><path d="M622 757c2 1 3 1 5 1 2-1 0 0 1-1h1l1 18h-2v-2-3 1c-1 2-1 3-2 4v1l-1-1h1l-1-6h0c0 2 0 5-1 6-3-1 0-4-2-6h0c0 1-1 3 0 4v1c-1 0-2 1-3 1 0-1 0-3-1-4h-1v4c-1-1-1-1-2-1 0 1 0 1-1 1-1-3 0-7-1-11-1-1-3-1-5-1v-1-1s0-1-1-1h0v-1h1l1 1 1-2h4 3 0 4l1-1z" class="W"></path><path d="M568 711l1-1c2 2 4 5 5 8l4 14c2 6 4 12 4 17v5h-1c0-2 0-4-1-5-1 2 0 4-2 4l1 7h-16c-1 0-5 0-6-1 1-1 1-2 1-4 0 1 0 2-1 3-1 0-2-1-4-1-1 1-1 1-3 1l1-15 1 1h1l1-3h0c1-2 2-6 2-8l3-9v-1-1c1-1 1-2 2-2h1c1-1 2-5 4-6 1 1 1 2 2 3h2l-1-2c0-1 0-3-1-4z" class="T"></path><path d="M567 745h1l1 1c1 1 0 2 0 3h-2l-1-1c0-1 0-2 1-3z" class="S"></path><path d="M559 723v-1c1-1 1-2 2-2h1c-2 4-3 8-4 12v5c-1 5 0 10 0 15v3c0 1 0 2-1 3-1 0-2-1-4-1-1 1-1 1-3 1l1-15 1 1h1l1-3h0c1-2 2-6 2-8l3-9v-1z" class="I"></path><path d="M554 754c1-2 1-2 4-2v3c0 1 0 2-1 3-1 0-2-1-4-1v-2l1-1z" class="H"></path><path d="M554 741c0 2 0 3-1 4v1 2l1 1-1 2c0 1 1 2 1 3l-1 1v2c-1 1-1 1-3 1l1-15 1 1h1l1-3z" class="j"></path><defs><linearGradient id="M" x1="571.097" y1="735.67" x2="576.949" y2="732.537" xlink:href="#B"><stop offset="0" stop-color="#7e7d7d"></stop><stop offset="1" stop-color="#9d9c9b"></stop></linearGradient></defs><path fill="url(#M)" d="M568 711l1-1c2 2 4 5 5 8l4 14c2 6 4 12 4 17v5h-1c0-2 0-4-1-5-1 2 0 4-2 4-1-13-7-23-10-36h2l-1-2c0-1 0-3-1-4z"></path><path d="M568 711l1-1c2 2 4 5 5 8l4 14v1h-1l-6-13c0-1 0-2-1-3l-1-2c0-1 0-3-1-4z" class="K"></path><path d="M568 711c0 1-1 1-1 2l-1 1v-2c1-4 7-8 10-10 2-1 3-2 4-3 1 0 1-1 2-1 2-1 3-2 6-2h1c0 1 1 2 0 3-2 3-1 10 0 14 1 1 1 1 2 1 0 0-1 1-1 2 0 0 1 0 1 1 0 2-1 1 2 2h2v-1-1c3 2 5 2 8 2h0l1 1 1 3h-1c-1 2-1 3-2 3h2l-1 1h-2l2 1c0 2-2 2-3 3v6 11h0v6c0 1 0 2 1 3l1 1v1c-2 0-2 0-4 1h0l-1 1c-1 1-2 1-4 0-1 0-2 0-3 1l-2-1h0v-1h-3-1l1-2v-1c-1-2-2-5-3-8 0-5-2-11-4-17l-4-14c-1-3-3-6-5-8l-1 1z" class="c"></path><path d="M595 719v-1-1c3 2 5 2 8 2h0l1 1 1 3h-1c-1 2-1 3-2 3h2l-1 1h-2l-2-1c-1 0-1-1-1-2-1-1-1-1-1-2-1-1 0-1 0-3h-2z" class="a"></path><path d="M602 722c1 1 1 1 2 1-1 2-1 3-2 3h-1c0-2 1-3 1-4z" class="e"></path><path d="M603 719l1 1 1 3h-1c-1 0-1 0-2-1 0-1-1-1-1-2l2-1z" class="Q"></path><path d="M575 717c-1-3-3-6-4-9 1 1 2 1 2 1l1 1v-1s-1-1-1-2h1c0-2 1-2 2-3 1 1 1 2 2 3v2 2c0 2-2 3 0 5 1 3 1 5 0 8l-3-7z" class="Z"></path><defs><linearGradient id="N" x1="579.729" y1="736.495" x2="593.879" y2="735.093" xlink:href="#B"><stop offset="0" stop-color="#aeacac"></stop><stop offset="1" stop-color="#e4e4e3"></stop></linearGradient></defs><path fill="url(#N)" d="M578 709c7 13 13 25 17 39l3 12-1 1c-1 1-2 1-4 0-1 0-2 0-3 1l-2-1h0v-1h-3-1l1-2v-1c-1-2-2-5-3-8 0-5-2-11-4-17l-4-14 1-1 3 7c1-3 1-5 0-8-2-2 0-3 0-5v-2z"></path><path d="M582 724h1c1 2 2 5 4 8 1 3 1 6 2 9l5 18v1c-1 1-3 0-4 0-1-13-2-24-8-36z" class="G"></path><path d="M581 728v-3l1-1c6 12 7 23 8 36l1 1-1 1-2-1h0v-1c-2-11-4-21-7-32z" class="S"></path><defs><linearGradient id="O" x1="585.911" y1="736.18" x2="575.236" y2="743.048" xlink:href="#B"><stop offset="0" stop-color="#272422"></stop><stop offset="1" stop-color="#3a3c3e"></stop></linearGradient></defs><path fill="url(#O)" d="M575 717l3 7 3 4c3 11 5 21 7 32h-3-1l1-2v-1c-1-2-2-5-3-8 0-5-2-11-4-17l-4-14 1-1z"></path><path d="M582 670c2 1 5 0 7 1 1 2 2 2 4 3l1 1h0c0-1 1-2 1-3l1 1c2 1 3 1 5 3 1 0 1 0 2-1l1 1c-1 1-1 0-1 1l1 2c1-1 2-2 4-2l2 2-1 1 1 1v4 4h-1-1c0 1 0 1 1 2l1 2v1l-5 1-5 1-1-1c-3 2-4 3-5 5v1l-2-1c0-1 0-1-1-2 0 1-1 1-2 1 1-1 0-2 0-3h-1c-3 0-4 1-6 2-1 0-1 1-2 1-1 1-2 2-4 3-3 2-9 6-10 10v2l1-1c0-1 1-1 1-2 1 1 1 3 1 4l1 2h-2c-1-1-1-2-2-3-2 1-3 5-4 6h-1c-1 0-1 1-2 2v1 1c-2-1-2-1-2-2-1 0-1 5-2 6h0c0-4 1-8 2-12l4-12c0-1-1-2-2-2l2-2-1-2 2-3 4-3 1-1s1-1 2-1c0-1 0 0 1-1l1 1c1-1 2-1 2-1l1-1c-3-1-5 0-7-2l3-1c1-1 3-1 4-1h0l-1-1c-1 0-3 0-4-1h0c1-3 0-7 1-9 1 0 2 1 3 0 1 0 1 0 2 1 0 1 0 0 1 1v-1h3 1c-1-1-1-1-1-2 1 0 2 0 3-1v-1z" class="Y"></path><path d="M574 695l10-2v1h-3c-1 0-4 3-5 3h-1c0-1-1-1-1-2z" class="f"></path><path d="M584 688c5 0 10 0 16 1h-1l-1 1h-5c-1 0-2-1-3-1-2-1-5 0-6-1z" class="p"></path><path d="M559 714h2v-1l1-1 2 2c-1 1-2 3-3 4h-1-1v1h1v1h-1v3 1c-2-1-2-1-2-2 1-2 1-5 2-8z" class="P"></path><path d="M597 694l2 1c-3 2-4 3-5 5v1l-2-1c0-1 0-1-1-2 0 1-1 1-2 1 1-1 0-2 0-3h-1 3c2-1 4-2 6-2z" class="Q"></path><path d="M598 693l-1-1h1c3 1 8 0 12 1v1l-5 1-5 1-1-1-2-1 1-1z" class="e"></path><path d="M598 693c1-1 4 0 6 0h1v2l-5 1-1-1-2-1 1-1zm-22 4c-1 1-3 2-4 4 2 0 6-3 8-4 1-1 1-1 2-1 2-1 3-2 5-3h1l1 1-1 1c-3 0-5 1-7 2 0 1 0 1-1 1-2 0-6 4-8 6h-1c-1 0-1 0-2-1 1-1 2-1 2-3 0-1 0 0 1-1v-1l-1-1-2-2c0-1 0-1 2-1l3 1c0 1 1 1 1 2h1z" class="P"></path><path d="M571 694l3 1c0 1 1 1 1 2h0-4l-2-2c0-1 0-1 2-1z" class="g"></path><path d="M573 689l16 2c2 0 4 0 6 1-2 1-7-1-8-1-4 1-12 4-16 2h0v-3c1-1 2-1 2-1z" class="T"></path><path d="M569 695l2 2 1 1v1c-1 1-1 0-1 1 0 2-1 2-2 3 1 1 1 1 2 1-1 1-2 2-2 3l-2 2c-1 1-2 3-3 5l-2-2-1 1v1h-2l3-6c1-5 3-8 6-12l1-1z" class="P"></path><path d="M562 708h0c1 1 1 0 1 1l2-2 2 2c-1 1-2 3-3 5l-2-2-1 1v1h-2l3-6z" class="c"></path><path d="M569 695l2 2 1 1v1c-1 1-1 0-1 1 0 2-1 2-2 3 1 1 1 1 2 1-1 1-2 2-2 3h-1c-1 0-2 0-3-1 0-1 0-2 1-3 0-1 1-1 1-2h1v-5l1-1z" class="T"></path><path d="M569 690c0-1 0 0 1-1l1 1v3h0v1c-2 0-2 0-2 1l-1 1c-3 4-5 7-6 12l-3 6c-1 3-1 6-2 8-1 0-1 5-2 6h0c0-4 1-8 2-12l4-12c0-1-1-2-2-2l2-2-1-2 2-3 4-3 1-1s1-1 2-1z" class="O"></path><path d="M562 695l4-3 1-1s1-1 2-1c-3 5-6 9-8 14h0c0-1-1-2-2-2l2-2-1-2 2-3z" class="h"></path><path d="M582 670c2 1 5 0 7 1 1 2 2 2 4 3l1 1h0c0-1 1-2 1-3l1 1c2 1 3 1 5 3 1 0 1 0 2-1l1 1c-1 1-1 0-1 1l1 2c1-1 2-2 4-2l2 2-1 1 1 1v4 4h-1-1c0 1 0 1 1 2l-9-2c-6-1-11-1-16-1h-2-8c-3-1-5 0-7-2l3-1c1-1 3-1 4-1h0l-1-1c-1 0-3 0-4-1h0c1-3 0-7 1-9 1 0 2 1 3 0 1 0 1 0 2 1 0 1 0 0 1 1v-1h3 1c-1-1-1-1-1-2 1 0 2 0 3-1v-1z" class="X"></path><path d="M590 675c1-1 2-1 4 0h0l2 1 1-1 1 1h1 0c0 1 0 4 1 5v-1c0-1 1-2 1-3h1v1c0 1 0 1 1 2l-1 1 1 1-2 2h-1-2-1-1 0l-1-1v2h0l-2-1 1-6v1h1v-1h-1c-1 0-2-1-3-1v-2h-1z" class="Z"></path><path d="M586 674h1 0l3 1h1v2c1 0 2 1 3 1h1v1h-1v-1l-1 6 2 1h0v-2l1 1h0 1 1v3c-2 0-5 0-7-1s-4-1-6-2c1-3 1-7 1-10z" class="X"></path><path d="M590 675h1v2c1 0 2 1 3 1h1v1h-1v-1c-1 1-3 2-4 3l-1-3h0l1-1v-2z" class="L"></path><path d="M586 674h1 0l-1 2 1 2c0 1-1 3-1 5l1 1c1-1 1 0 2-1 1 1 2 0 4 0h0v1l2 1h0v-2l1 1h0 1 1v3c-2 0-5 0-7-1s-4-1-6-2c1-3 1-7 1-10z" class="p"></path><path d="M573 683h6l6 1c2 1 4 1 6 2s5 1 7 1c4 0 8 1 11 1v-1c-1-1-2-1-3-2v-1l1-1c1 1 2 2 3 2v4h-1-1c0 1 0 1 1 2l-9-2c-6-1-11-1-16-1h-2-8c-3-1-5 0-7-2l3-1c1-1 3-1 4-1h0l-1-1z" class="g"></path><path d="M582 670c2 1 5 0 7 1 1 2 2 2 4 3l1 1c-2-1-3-1-4 0l-3-1h0-1c0 3 0 7-1 10l-6-1h-6c-1 0-3 0-4-1h0c1-3 0-7 1-9 1 0 2 1 3 0 1 0 1 0 2 1 0 1 0 0 1 1v-1h3 1c-1-1-1-1-1-2 1 0 2 0 3-1v-1z" class="G"></path><path d="M575 676l1 1v2c-1 0-1-1-2-1l1-2z" class="O"></path><path d="M582 670c2 1 5 0 7 1 1 2 2 2 4 3l1 1c-2-1-3-1-4 0l-3-1h0-1-2v1l-1-1c-1 1-1 1-2 1v1l-1-2c-1-1-1-1-1-2 1 0 2 0 3-1v-1z" class="h"></path><path d="M628 713c3 0 7 1 11 1h0c-2 4-5 8-7 11v1c-3 4-2 25-1 31v18h-1l-1-18h-1c-1 1 1 0-1 1-2 0-3 0-5-1l-1 1h-4 0-3c-3-1-6-1-9 0l-1 1 1 1c-2 1-5 0-7 0 2-1 2-1 4-1v-1l-1-1c-1-1-1-2-1-3v-6h0v-11-6c1-1 3-1 3-3l-2-1h2l1-1h-2c1 0 1-1 2-3h1l-1-3-1-1h0c-3 0-5 0-8-2l-1-1 1-1c2 0 5 1 7 0s5-1 7-1c6-1 12-1 18-1h1z" class="W"></path><path d="M629 745v7l-1-1v-1l-1-4s1-1 2-1z" class="i"></path><path d="M608 727c1 0 2 1 3 1 0 1 0 1-1 1 0 2-1 3-1 4h-2v1c1 0 3 1 4 1-1 1-3 1-4 2 1 0 2 0 3 1-1 1-2 1-3 1l-1 1c-1 0-1 0-2-1h1v-1l-1-1c1-1 1-1 1-2v-4c0-1 1-1 1-2 1 0 2-1 2-1v-1z" class="d"></path><path d="M610 745h1c1 1 1 3 1 4h1 3 1v-3h1c1 1 1 1 1 3l2-1h0l1 2 1 1h-6-6-1c-2 0 0 1-2 0-1-1-1-1-1-2 1 0 1 0 2-1-2 0-2 0-4-1h1c1 0 2 0 3-1l1-1z" class="i"></path><path d="M607 727h1v1s-1 1-2 1c0 1-1 1-1 2v4c0 1 0 1-1 2l1 1v1h-1c1 1 1 1 2 1h4c-2 1-3 1-4 3h3c-1 1-2 1-3 1v1h4l-1 1c-1 1-2 1-3 1h-1c2 1 2 1 4 1-1 1-1 1-2 1h-1-1-3l-1-1h0-1 0v-11h0l1 1 1-1c1-1 1-2 0-3 1-1 1-2 1-2 1-1 1-1 1-2 1-1 1-1 3-2v-1z" class="R"></path><path d="M600 748h1 0l1 1h3 1 1c0 1 0 1 1 2 2 1 0 0 2 0h1 6 6 5l1 1v5h-1c-1 1 1 0-1 1-2 0-3 0-5-1l-1 1h-4 0-3c-3-1-6-1-9 0l-1 1 1 1c-2 1-5 0-7 0 2-1 2-1 4-1v-1l-1-1c-1-1-1-2-1-3v-6z" class="a"></path><path d="M617 751h6l-1 1h0c-1 0-2 0-3 1h0c0 1 0 1 1 1v2c1 0 1 0 2-1v1 1l-1 1h-4 0l1-1-1-3v1l-1 1h0l-1-2h-1l-1 1c-2 1-1 0-3-1l-1 1-1-1c-1 0-1 0-2 1v-2h2c3-1 6-1 9-1v-1z" class="E"></path><path d="M623 751h5l1 1v5h-1c-1 1 1 0-1 1-2 0-3 0-5-1v-1-1c-1 1-1 1-2 1v-2c-1 0-1 0-1-1h0c1-1 2-1 3-1h0l1-1z" class="B"></path><path d="M628 713c3 0 7 1 11 1h0c-2 4-5 8-7 11v1c-3 4-2 25-1 31v18h-1l-1-18v-5-7-10c0-2 1-4 0-6h-8-2l-1 1h0c0 1 1 1 0 2v1l-1 1-1-1 1-2c0-1 0-2-1-2h-5-1c1 0 1 0 1-1-1 0-2-1-3-1h-1v1c-2 1-2 1-3 2 0 1 0 1-1 2 0 0 0 1-1 2 1 1 1 2 0 3l-1 1-1-1h0v-6c1-1 3-1 3-3l-2-1h2l1-1h-2c1 0 1-1 2-3h1l-1-3-1-1h0c-3 0-5 0-8-2l-1-1 1-1c2 0 5 1 7 0s5-1 7-1c6-1 12-1 18-1h1z" class="T"></path><path d="M602 715c2-1 5-1 7-1 6-1 12-1 18-1l9 2 1 1-1 1h-2c-4-1-8-1-12-1l-20-1z" class="H"></path><path d="M603 719h15 1v5c1 0 1 0 1-1 1-1 1-1 1-2l1-2h1 1c-1 1-1 2-2 3l1 1h0 5c1 0 1 0 2-1v-1c-1 0-1 1-3 1l-1 1-1-1v-3h7l1 1c0 2-3 5-4 7-2 0-3 0-5 1 2 0 4 0 5 1h-8-2l-1 1h0c0 1 1 1 0 2v1l-1 1-1-1 1-2c0-1 0-2-1-2h-5-1c1 0 1 0 1-1-1 0-2-1-3-1h-1v1c-2 1-2 1-3 2 0 1 0 1-1 2 0 0 0 1-1 2 1 1 1 2 0 3l-1 1-1-1h0v-6c1-1 3-1 3-3l-2-1h2l1-1h-2c1 0 1-1 2-3h1l-1-3-1-1h0z" class="F"></path><path d="M607 727c-1 0-1 0-2-1 2-1 3-1 5-1h1c2-1 4 0 6 0 1 1 1 1 3 1h0l1 2-1 1-1-1-1-2-1 1c1 2 1 2 0 4 0-1 0-2-1-2h-5-1c1 0 1 0 1-1-1 0-2-1-3-1h-1z" class="V"></path><path d="M605 723v1h2c1 0 7 1 9 0l1-1-1-1v-1c0-1 0-1 2-2l-1 6c-2 0-4-1-6 0h-1c-2 0-3 0-5 1 1 1 1 1 2 1v1c-2 1-2 1-3 2 0 1 0 1-1 2 0 0 0 1-1 2 1 1 1 2 0 3l-1 1-1-1h0v-6c1-1 3-1 3-3l-2-1h2l1-1h-2c1 0 1-1 2-3h1z" class="m"></path><path d="M603 719h15 0c-2 1-2 1-2 2v1l1 1-1 1c-2 1-8 0-9 0h-2v-1l-1-3-1-1h0z" class="R"></path><path d="M614 653c8 1 16 1 24 3h2l1 2-16-3v1l6 1c3 1 5 1 8 2h0c-1 1-1 1-2 1l2 1v2 34 14l-4-1-1 1 5 1v2c-4 0-8-1-11-1h-1c-6 0-12 0-18 1-2 0-5 0-7 1s-5 0-7 0l-1 1 1 1v1 1h-2c-3-1-2 0-2-2 0-1-1-1-1-1 0-1 1-2 1-2-1 0-1 0-2-1-1-4-2-11 0-14 1 0 2 0 2-1 1 1 1 1 1 2l2 1v-1c1-2 2-3 5-5l1 1 5-1 5-1v-1l-1-2c-1-1-1-1-1-2h1 1v-4-4l-1-1 1-1-2-2c1 0 1 0 2-1v-1h-1v-1-1h-1-2l-1-1s-1 0-1-1l-6-3c1-2 2-2 4-3 1 0 1-1 1-1h-7c0-1 0-1-1-1l1-2c-3 1-5 0-7-2-1-1-2 0-3 0l-2-2 1-2h0c4 0 7-1 10-1h3l16-1z" class="W"></path><path d="M619 683c1 2 1 5 1 8v3s3 1 4 2h0c0-3 1-5 1-8h1v1h0c-1 4 0 7-1 10h-5c1-1 1-1 0-2l-1-1c-1-1-1-1-1-2l1-1c1-3 0-7 0-10z" class="D"></path><path d="M615 656c3 0 7 1 10 0l6 1c3 1 5 1 8 2h0c-1 1-1 1-2 1l2 1v2h-2c-4-2-9-2-13-2h-9-1l1-1c2 0 5 0 6-1h-1c-1-1-2-1-3-2-1 0-1 0-2-1z" class="F"></path><path d="M615 656c3 0 7 1 10 0l6 1-1 1 1 1c-2 1-7-1-10 0h-1c-1-1-2-1-3-2-1 0-1 0-2-1z" class="L"></path><path d="M616 663c2 0 4 0 6 1v1c-1 1-3 1-4 1l-4 2-1 2c-1 0-3 1-5 0l-1 2 1 1h-2l-1-1s-1 0-1-1l-6-3c1-2 2-2 4-3 1 0 1-1 1-1v-1h13z" class="o"></path><path d="M608 670c-1 1-1 0-2 1h-1l-1-1c-1 0-1-1-1-2l-1 1-1-1v-1h7c3-1 6-1 10-1l-4 2-1 2c-1 0-3 1-5 0z" class="B"></path><path d="M613 670h5c1 1 1 3 1 5v8c0 3 1 7 0 10l-1 1h-8v-1l-1-2c-1-1-1-1-1-2h1 1v-4-4l-1-1 1-1-2-2c1 0 1 0 2-1v-1h-1v-1-1h-1l-1-1 1-2c2 1 4 0 5 0z" class="T"></path><path d="M613 670h5c1 1 1 3 1 5l-1-3c-2 0-3 0-5 1 0 2 0 2 1 3v5 12h5l-1 1h-8v-1l-1-2c-1-1-1-1-1-2h1 1v-4-4l-1-1 1-1-2-2c1 0 1 0 2-1v-1h-1v-1-1h-1l-1-1 1-2c2 1 4 0 5 0z" class="h"></path><path d="M614 653c8 1 16 1 24 3h2l1 2-16-3v1c-3 1-7 0-10 0 1 1 1 1 2 1 1 1 2 1 3 2h1c-1 1-4 1-6 1l-1 1v1l2 1h-13v1h-7c0-1 0-1-1-1l1-2c-3 1-5 0-7-2-1-1-2 0-3 0l-2-2 1-2h0c4 0 7-1 10-1h3l16-1z" class="n"></path><path d="M584 657l1-2 1 1c2 0 10 0 11 1 0 0 0 1-1 1-1 1-3-1-4 1 1 1 2 0 3 0 1 1 1 1 1 2-3 1-5 0-7-2-1-1-2 0-3 0l-2-2z" class="c"></path><path d="M614 653c8 1 16 1 24 3h2l1 2-16-3h-7-20v-1l16-1z" class="I"></path><path d="M596 664v-2c1 0 1 0 2-1 2-2 6-3 9-3l1-1c2-1 5-1 7-1 1 1 1 1 2 1 1 1 2 1 3 2h1c-1 1-4 1-6 1l-1 1v1l2 1h-13v1h-7z" class="Y"></path><path d="M615 660c-3 0-6 0-9-1 5-1 10-1 14 0h1c-1 1-4 1-6 1zm-12 3h-4v-1c5 0 10-1 15-1v1l2 1h-13z" class="K"></path><path d="M610 694h8c0 1 0 1 1 2l1 1c1 1 1 1 0 2-1 0-3 1-4 1l-7 3c2 0 5 1 7 0h1l1 1c-1 2-2 0-3 2v1h3s1-1 2-1c4 1 13 0 17 3h1c0-3 0-8 1-11v-1 14l-4-1-1 1 5 1v2c-4 0-8-1-11-1h-1c-6 0-12 0-18 1-2 0-5 0-7 1s-5 0-7 0l-1 1 1 1v1 1h-2c-3-1-2 0-2-2 0-1-1-1-1-1 0-1 1-2 1-2-1 0-1 0-2-1-1-4-2-11 0-14 1 0 2 0 2-1 1 1 1 1 1 2l2 1v-1c1-2 2-3 5-5l1 1 5-1 5-1z" class="B"></path><path d="M607 700h1l-1 1c-1 1-2 1-3 1v1c1 1 1 1 2 1 2 1 3-1 4 1-1 1-2 1-3 1v1c4 3 9 0 13 2h-12-10v-1h-2l1-1 2 1h1l3-2h1c-2-2-4-2-6-2h-1v-1h4v-1c-1 0-2 0-3-1 2 0 5-1 7-1h2z" class="F"></path><path d="M599 695l1 1 1 1h0c2-1 4-1 6-1-1 2-2 3-2 4-2 0-5 1-7 1 1 1 2 1 3 1v1h-4v1h1c2 0 4 0 6 2h-1l-3 2h-1l1-2h-1-1c-2 0-2-1-3-2s-1-2-1-3v-1c1-2 2-3 5-5z" class="V"></path><path d="M610 694h8c0 1 0 1 1 2l1 1c1 1 1 1 0 2-1 0-3 1-4 1h-8-1-2c0-1 1-2 2-4-2 0-4 0-6 1h0l-1-1 5-1 5-1z" class="U"></path><path d="M607 696l2 1h1v2l-3 1h-2c0-1 1-2 2-4z" class="g"></path><path d="M610 694h8c0 1 0 1 1 2-2 1-3 1-4 1-2-1-5 0-6 0l-2-1c-2 0-4 0-6 1h0l-1-1 5-1 5-1z" class="C"></path><path d="M595 704c1 1 1 2 3 2h1 1l-1 2-2-1-1 1h2v1h10 12c5 0 11 0 15 1l-1 1c-2 1-5 0-8 0-3-1-6 0-9 0-1-1-3 0-4-1-2 0-3 0-4 1-2 1-5 1-7 1h-1c0-2-5-1-7-2 0-1 1-2 0-3 0-1 1-2 1-3z" class="j"></path><path d="M589 699c1 0 2 0 2-1 1 1 1 1 1 2l2 1c0 1 0 2 1 3 0 1-1 2-1 3 1 1 0 2 0 3 2 1 7 0 7 2h1c2 0 5 0 7-1 1-1 2-1 4-1 1 1 3 0 4 1 3 0 6-1 9 0 3 0 6 1 8 0l5 1v2c-4 0-8-1-11-1h-1c-6 0-12 0-18 1-2 0-5 0-7 1s-5 0-7 0l-1 1 1 1v1 1h-2c-3-1-2 0-2-2 0-1-1-1-1-1 0-1 1-2 1-2-1 0-1 0-2-1-1-4-2-11 0-14z" class="V"></path><path d="M617 711c3 0 6-1 9 0 3 0 6 1 8 0l5 1v2c-4 0-8-1-11-1l-15-1v-1h4z" class="O"></path><path d="M589 699c1 0 2 0 2-1 1 1 1 1 1 2l2 1c0 1 0 2 1 3 0 1-1 2-1 3 1 1 0 2 0 3 2 1 7 0 7 2h-8v1h7v1c-2 0-7 0-9 1h0 4l-1 1 1 1v1 1h-2c-3-1-2 0-2-2 0-1-1-1-1-1 0-1 1-2 1-2-1 0-1 0-2-1-1-4-2-11 0-14z" class="f"></path><path d="M498 679v2c4 1 7 1 11 1 1-1 3 0 5-1l1 1c1-1 1-1 1-2l1 1h3l11 1 5-1c1 1 2 0 3 0 4 1 7 0 11 1h5c4 1 10 1 14 0 1 1 3 1 4 1l1 1h0c-1 0-3 0-4 1l-3 1c2 2 4 1 7 2l-1 1s-1 0-2 1l-1-1c-1 1-1 0-1 1-1 0-2 1-2 1l-1 1-4 3-2 3 1 2-2 2c1 0 2 1 2 2l-4 12c-1 4-2 8-2 12h0c1-1 1-6 2-6 0 1 0 1 2 2l-3 9c0 2-1 6-2 8h0l-1 3h-1l-1-1-1 15c2 0 2 0 3-1 2 0 3 1 4 1-2 2-2 2-2 4 0 1-1 2-1 3v3 1c1 0 1 1 1 0l2 2c0 2-1 2-2 3l-1-1-2 1c1 0 1 1 1 2l1 3h-1v-1l-1 1c-2 1-13 1-15 0l-1-1h0v-2h-26c-6 0-12 0-18 1-1 1-2 1-3 1l-5 1h-1c-2 1-11 1-13 0l-1 1v-1-1h-1v1l-1-1-1-1v-1l-18-2c-11 0-23 0-34 2-3 0-8 0-11 2l1 2h-5c0-1 1-3 1-4 1 0 0 0 0-1h-1c-1-1-1-2-1-3v-3-4h1v-1l3-3 2 1h0c2 0 4-1 6-1 2-1 4-1 6-1h0v-4-3l-1 1h0c0-4 2-8 3-11-1-2-1-3-1-4l4-10c2-6 4-13 5-19l3-7 1-1h0v-2-1l1-2-2-1-1-2c-2 0-6-2-8-1h-3l2-3 4 1c2-1 3 0 5-2-2 0-4 0-5-1l1-1 2 1c7-2 14-2 21-3 4 0 7-1 11-1h0l1-1h-5v-1h4 22c5 0 13 0 18-1v-2z" class="d"></path><path d="M503 713v-1c-1-1-1-2-1-4v-6l-1 1c-2-1-1-2-2-3-1-2-1-3-1-6l-2-2 1-1 3 3h1v2h-1c0 1 1 1 2 2l2 1v1h-1-1v1c1 1 2 1 3 2-1 1 0 1-1 2v1l-1 1 1 1h2v1c0 1-2 1-2 2-1 1 0 1-1 2z" class="f"></path><path d="M501 728c1 1 1 2 2 2h1v-2c0-3 0-4 1-7 2 0 4-1 7-1v2c-2 0-4 2-6 2 0 0-1 0 0 1-1 1-1 2-1 3v9c-1 1-1 1-1 3h1-1-2c-1 2 0 5 0 7v2s-1 1-2 1l1-21v-1z" class="Z"></path><path d="M512 722v2 5 6 1l-4-1c-1 1-2 1-3 2v-9c0-1 0-2 1-3-1-1 0-1 0-1 2 0 4-2 6-2z" class="G"></path><path d="M512 729v6h-1c-2 0-2 0-3-1 0-1 1-2 2-2v-1-1l2-1z" class="C"></path><path d="M512 722v2 5l-2 1h-3l2-2-1-1c0-1 0-2-1-3l-1 1c-1-1 0-1 0-1 2 0 4-2 6-2z" class="L"></path><path d="M506 704h1c1 0 1 1 3 1-1-1 0-1-1-2h1c1 1 1 4 2 4 1-1 2-1 3-2h1c-1 1-1 2-2 3l1 1-1 1h0l-1 2c1 0 1 1 2 1l-2 2h1 0l1 2h-1l-1 1 1 1c-1 1-1 2-1 3v2h-1v-2-2c-3 0-5 1-7 1-1 3-1 4-1 7v2h-1c-1 0-1-1-2-2l2-2c0-1 0-3-1-4v-2c1 0 2 0 3-1h-1l-1-1v-2c0-1-1-2 0-3s0-1 1-2c0-1 2-1 2-2v-1h-2l-1-1 1-1c1 0 1 0 2-2z" class="j"></path><path d="M511 709l1 1c0 1 0 1-1 2-1 0-2 0-3 1h-1-1v-1h0c1-2 2-3 5-3z" class="Z"></path><path d="M504 719c1-2 2-4 5-4 1-1 2-1 2-1 1 1 1 3 1 4-1 1-4 1-5 1h-2-1z" class="L"></path><path d="M505 737c1-1 2-1 3-2l4 1v5 1 14 1h-8-4v-1h1c-1-2-1-4-1-6 1 0 2-1 2-1v-2c0-2-1-5 0-7h2 1-1c0-2 0-2 1-3z" class="F"></path><path d="M508 735l4 1v5h0-2c-2 0-3 1-4-1h0c0-2 1-3 2-5z" class="I"></path><path d="M500 750c1 0 2-1 2-1v-2c0-2-1-5 0-7h2 1c0 1 0 3-1 4v1 2 1c1 2 1 6 1 7-1 1-3 1-4 1-1-2-1-4-1-6z" class="b"></path><path d="M505 755c1 0 1-2 1-3h2 1 1c-1-1-1-3-1-3-2 0-2 0-3-1 0-1 0-1 1-2 1-3 2-3 5-4v14 1h-8-4v-1h1c1 0 3 0 4-1z" class="K"></path><path d="M527 695c-1 2-1 4-2 7h0v6c-1 1-1 0 0 1-1 4-1 7-1 10v4h-1v-10c-1 0-2 0-2 1-3 0-5 1-7 1h0 0-1l2-2c-1 0-1-1-2-1l1-2h0l1-1-1-1c1-1 1-2 2-3h-1c-1 1-2 1-3 2-1 0-1-3-2-4h-1c1 1 0 1 1 2-2 0-2-1-3-1h-1 0v-4h2v-2l-1-1 1-1h2l12-1c2 1 3 0 5 0z" class="P"></path><path d="M514 703h-2v-1c1-1 2-2 3-2l-1 3h0zm13-8c-1 2-1 4-2 7h0c0 1-1 2-1 3h0v-3c-2-1-2-1-3-1 0-2 1-4 0-5-2 0-3-1-4 0v1c0 1 0 2-1 2l-1-1h-1c-1 1-2 0-4 0v-2l12-1c2 1 3 0 5 0z" class="h"></path><path d="M514 703c1-1 2-1 4-1v-1c1-1 2 0 3 0s1 0 3 1v3h0c0-1 1-2 1-3v6c-1 1-1 0 0 1-1 4-1 7-1 10v4h-1v-10h0c0-1 0-3-1-4h-1v-2-2h-2-2-1-1s-1-1-1-2h0z" class="m"></path><path d="M516 705h1 2 2v2 2h1c1 1 1 3 1 4h0c-1 0-2 0-2 1-3 0-5 1-7 1h0 0-1l2-2c-1 0-1-1-2-1l1-2h0l1-1-1-1c1-1 1-2 2-3z" class="n"></path><path d="M496 689c0-1-1-3-1-4h23c1 1 3 1 4 1 4 0 10-1 14 0-2 2-2 2-4 3 0 0 0 1 1 1v2h-1-2l-1 1-1-1h-2-1c1 1 2 1 2 2v1c-2 0-3 1-5 0l-12 1h-2l-1 1 1 1v2h-2v4h0c-1 2-1 2-2 2v-1c1-1 0-1 1-2-1-1-2-1-3-2v-1h1 1v-1l-2-1c-1-1-2-1-2-2h1v-2c-1-2-3-4-5-5h0z" class="T"></path><path d="M496 689c0-1-1-3-1-4h23c1 1 3 1 4 1 4 0 10-1 14 0-2 2-2 2-4 3 0 0 0 1 1 1v2h-1-2l-1 1-1-1 1-2c1-1 1 0 2-2-2-1-3-1-5-1-7-1-16 2-24 1h-5l-1 1h0z" class="e"></path><path d="M521 714c0-1 1-1 2-1v10h1v6l-1 2c0 2 0 5 1 7v1c1 1 1 2 2 3-1 2-1 4 0 6v8 1h-10c-2 0-2-1-2-2v-3l-1 6h0-1v-2h0v-14-1-5-1-6-5h1v-2c0-1 0-2 1-3l-1-1 1-1h1l-1-2h0c2 0 4-1 7-1z" class="Z"></path><path d="M516 724l-2-1c1-2 6-1 8-2v1 3-1h-6z" class="I"></path><path d="M521 714v1h-1l2 2h-2v1h2v1h-8l-1-1 1-1h1l-1-2h0c2 0 4-1 7-1z" class="F"></path><path d="M516 724h6v1c1 2 1 4 1 6s0 5 1 7v1c1 1 1 2 2 3-2 0-3-1-5-1v-1c-2 0-3 0-5 1 0 2 0 2-2 3 0-4-1-8 0-12v-1c0-2 0-3 1-4 0-1 1-1 1-2v-1z" class="C"></path><path d="M514 732c1-1 2-1 4-1v1l-1 1c-1 0-2-1-3-1zm5 1c2 0 1 0 2 1v1h-1-4l-1-1 4-1zm-5 11c2-1 2-1 2-3 2-1 3-1 5-1v1c2 0 3 1 5 1-1 2-1 4 0 6v8 1h-10c-2 0-2-1-2-2v-3-2-1-2h0v-3z" class="E"></path><path d="M526 748v8 1h-10c-2 0-2-1-2-2h9c-1-1-1-2-2-3h-1-3v-1h4v-1h-2l1-1c2 0 4-1 6-1z" class="H"></path><path d="M521 752h3v3h-1c-1-1-1-2-2-3z" class="J"></path><path d="M497 700l2 20v22 13c0 1 1 1 1 1v1h4 8v-1h0v2h1 0l1-6v3c0 1 0 2 2 2h10v-1h1v4l-1-1h-1v1h-1l-1-1h-1 0l1 2v1h0l1-1c1 0 3 1 5 1v-2 5c0 2 0 4 1 6h-9-16-3-3v-2h-1v2h-1v-8l-1-1c0 1-1 2-2 3 0 1 1 2 0 3-1-1-1-2-2-3h0-3l2-2c1-1 1-1 2-1-1-1-1-2-1-3h0 0c-1 0-1 0-2-1l1-1v-1c-1 0-2 0-2-1h-1c-1-2 0-4-1-6v-8-1-2l1-1v-1c0-1 1-2 1-3v-1h0 1 1l1-19c0 3 0 5 1 8 0 1 0 2-1 3h0c1 0 2 1 3 0l-1-1c1-1 1-1 1-2l1-1-1-1 2-2-1-13c1-2 1-3 1-4z" class="G"></path><path d="M517 769c0-3-1-7 0-11h1v3c-1 3-1 5-1 8h0z" class="S"></path><path d="M505 760v-2h7c0 1 0 1 1 2l1 1 1-2c2 3-1 10 1 12l1-2h0c0-3 0-5 1-8h0c1-1 1-2 2-2l1 1 1-1 1 2v1h0l1-1c1 0 3 1 5 1v-2 5c0 2 0 4 1 6h-9-16v-2c0-3-1-6 0-9z" class="K"></path><path d="M510 761h1 0c0 3-1 5 0 8h-1-1c1-3 0-6 1-8z" class="H"></path><path d="M505 760c1 1 2 0 4 1l-1 8h0-1l-1-1v1h-1c0-3-1-6 0-9z" class="I"></path><path d="M492 713c0 3 0 5 1 8 0 1 0 2-1 3h0c1 0 2 1 3 0l-1-1c1-1 1-1 1-2l1-1-1-1 2-2v1c1 11-1 23 1 33 0 2 0 4-1 6h1v3l-2 2c0 1-1 2-2 3 0 1 1 2 0 3-1-1-1-2-2-3h0-3l2-2c1-1 1-1 2-1-1-1-1-2-1-3h0 0c-1 0-1 0-2-1l1-1v-1c-1 0-2 0-2-1h-1c-1-2 0-4-1-6v-8-1-2l1-1v-1c0-1 1-2 1-3v-1h0 1 1l1-19z" class="d"></path><path d="M492 755c1 0 2 1 2 2 1 0 0 1 0 2h-2 0v-4z" class="X"></path><path d="M488 737v-1c0-1 1-2 1-3v-1c0 3 0 7 2 9l-1 2h0c1 1 1 2 1 3h-2l1 1h1c1 2 0 6 1 8v4c-1 0-1 0-2-1l1-1v-1c-1 0-2 0-2-1h-1c-1-2 0-4-1-6v-8-1-2l1-1z" class="U"></path><path d="M488 737l1 2c0 1 0 1-1 2h-1v-1-2l1-1z" class="f"></path><path d="M497 717v1c1 11-1 23 1 33l-3 2h-1v-2c0-2-1-4-1-6v-2-5-3c-1-1 0-2 0-4v-3h-1l1-1v-1l-1-2c1 0 2 1 3 0l-1-1c1-1 1-1 1-2l1-1-1-1 2-2z" class="R"></path><path d="M493 738c1-1 1-1 2 0l-1 1 1 1c0 1 0 1-1 2 1 1 1 1 1 3h-2v-2-5zm4-21v1 9h-1 0-2l1 1h1c-1 1-2 0-2 2l1 1h-2v-3h-1l1-1v-1l-1-2c1 0 2 1 3 0l-1-1c1-1 1-1 1-2l1-1-1-1 2-2z" class="m"></path><path d="M536 686s1 1 2 1c2 1 4 1 7 1h23c-2 1-6 3-6 4v1l-1 1c-1 1-5 4-5 5-3 0-4 1-6 3 0 1-1 1-2 2v-1l-1-1c-1 1-1 1-1 3-1-1-2-1-2-2-1 1 0 2-1 3 0 1 0 1-1 2l1 1c-1 1-2 1-3 2-1 2-1 4 0 7 0 1 0 1-1 2 0 2 0 2 1 4h0c-1 2-1 2-1 4 0 1-1 1-1 2v4h-1-1v-2h-1v4l1 1c-2 1-2 1-4 1l1 1-1 1v2 3h-1-1-1 0-1c0 2 0 5 1 7v8 5-5 2c-2 0-4-1-5-1l-1 1h0v-1l-1-2h0 1l1 1h1v-1h1l1 1v-4h-1v-8c-1-2-1-4 0-6-1-1-1-2-2-3v-1c-1-2-1-5-1-7l1-2v-6-4c0-3 0-6 1-10-1-1-1 0 0-1v-6h0c1-3 1-5 2-7v-1c0-1-1-1-2-2h1 2l1 1 1-1h2 1v-2c-1 0-1-1-1-1 2-1 2-1 4-3z" class="e"></path><path d="M532 720c1 1 3 1 4 1l-1 2v1l1-1 1 1-2 2h1 1v1l-2 1c-1 0-2 0-3-1v-1-2-4z" class="n"></path><path d="M534 705c0 2 0 3 1 4h0v5l1 1c0 1 0 1-1 2 0 1 0 1 1 2l-1 1 2 1h0-1c-1 0-3 0-4-1l2-15z" class="X"></path><path d="M536 686s1 1 2 1l-1 3c-1 5 0 10-1 16l-1 3h0c-1-1-1-2-1-4l-2 15v4 2 1l-1 1h0l-2-1 4-28h-1l1-7v-2c-1 0-1-1-1-1 2-1 2-1 4-3z" class="R"></path><path d="M536 686s1 1 2 1l-1 3c-1 5 0 10-1 16l-1 3h0c-1-1-1-2-1-4 0-3 1-6 1-10 0-1 1-3 0-5h-1c-1 3-1 6-1 9h-1l1-7v-2c-1 0-1-1-1-1 2-1 2-1 4-3z" class="Z"></path><path d="M538 687c2 1 4 1 7 1h23c-2 1-6 3-6 4v1l-1 1c-1 1-5 4-5 5-3 0-4 1-6 3 0 1-1 1-2 2v-1l-1-1c-1 1-1 1-1 3-1-1-2-1-2-2-1 1 0 2-1 3 0 1 0 1-1 2l1 1c-1 1-2 1-3 2v-2-1-7c0-1-1-3-1-4v-1h4 10l1-1c3 0 5 0 7-2l-1-1h-1l-1 1h-5c-4 0-11 1-15 0l-1-3 1-3z" class="T"></path><path d="M527 694c0-1-1-1-2-2h1 2l1 1 1-1h2 1l-1 7h1l-4 28 2 1h0l1-1c1 1 2 1 3 1l1 1c0 1 1 1 0 3h-1v4l1 1c-2 1-2 1-4 1l1 1-1 1v2 3h-1-1-1 0-1c0 2 0 5 1 7v8 5-5 2c-2 0-4-1-5-1l-1 1h0v-1l-1-2h0 1l1 1h1v-1h1l1 1v-4h-1v-8c-1-2-1-4 0-6-1-1-1-2-2-3v-1c-1-2-1-5-1-7l1-2v-6-4c0-3 0-6 1-10-1-1-1 0 0-1v-6h0c1-3 1-5 2-7v-1z" class="F"></path><path d="M532 727c1 1 2 1 3 1l1 1c0 1 1 1 0 3h-1v4l1 1c-2 1-2 1-4 1l1 1-1 1v2 3h-1-1-1 0-1l1-18 2 1h0l1-1z" class="j"></path><path d="M533 732c-1-1-1-1-2-3h5c0 1 1 1 0 3h-1l-1-1-1 1z" class="X"></path><path d="M533 732l1-1 1 1v4l1 1c-2 1-2 1-4 1-2-2 0-2 0-4 0-1 1-1 1-2h0z" class="Z"></path><path d="M527 694c0-1-1-1-2-2h1 2l1 1 1-1h2 1l-1 7-2 8-2 16c-1-1-1-2-1-4-2-5 1-14-2-17 1-3 1-5 2-7v-1z" class="m"></path><path d="M527 694c0-1-1-1-2-2h1 2l1 1 1-1h2 1l-1 7-2 8c-1-1-1-2-2-3v-1c-4-4 1-5 0-9h-1z" class="P"></path><path d="M529 693l1-1h2v2l-2 1h-1v-2z" class="c"></path><path d="M525 702c3 3 0 12 2 17 0 2 0 3 1 4l-1 19v14h-1v-8c-1-2-1-4 0-6-1-1-1-2-2-3v-1c-1-2-1-5-1-7l1-2v-6-4c0-3 0-6 1-10-1-1-1 0 0-1v-6h0z" class="a"></path><path d="M525 709c0 1 1 1 1 2v4l-1 12c0-3 1-6-1-8 0-3 0-6 1-10z" class="b"></path><path d="M524 719c2 2 1 5 1 8l1 9c0 2-1 4 0 6h1v14h-1v-8c-1-2-1-4 0-6-1-1-1-2-2-3v-1c-1-2-1-5-1-7l1-2v-6-4zm-26-40v2c4 1 7 1 11 1 1-1 3 0 5-1l1 1c1-1 1-1 1-2l1 1h3l11 1 5-1c1 1 2 0 3 0 4 1 7 0 11 1h5c4 1 10 1 14 0 1 1 3 1 4 1l1 1h0c-1 0-3 0-4 1l-3 1c2 2 4 1 7 2l-1 1s-1 0-2 1l-1-1c-1 1-1 0-1 1-1 0-2 1-2 1l-1 1-4 3-1-1 1-1v-1c0-1 4-3 6-4h-23c-3 0-5 0-7-1-1 0-2-1-2-1-4-1-10 0-14 0-1 0-3 0-4-1h-23c0 1 1 3 1 4v8c1 1 1 2 1 3s0 2-1 4l1 13-2 2 1 1-1 1c0 1 0 1-1 2l1 1c-1 1-2 0-3 0h0c1-1 1-2 1-3-1-3-1-5-1-8l-1 19h-1-1 0v1c0 1-1 2-1 3v1l-1 1v2c-1-1 0-3 0-4-1-2-2-3-3-3-1-5-3-10-4-15-2-7-6-13-9-19l-1-3c-2-2-3-4-5-5v1l-1-1-1 1-2-1c0-1-1-2-2-3l-1-1h-4c-5 1-10 1-15 2-1-2-3-1-5-2-3 1-5 1-8 1 7-2 14-2 21-3 4 0 7-1 11-1h0l1-1h-5v-1h4 22c5 0 13 0 18-1v-2z" class="M"></path><path d="M477 686c5 0 12-1 16 0h0l-1 1h-1c-2 0-4 1-6 0h0l-1 1c-2 0-3 0-5-1l-2-1z" class="e"></path><path d="M493 701v-3c0-1 1-1 1-1v-5c1 2 1 3 1 5 1 2 1 4 1 7l1 13-2 2 1 1-1 1c0 1 0 1-1 2l1 1c-1 1-2 0-3 0h0c1-1 1-2 1-3-1-3-1-5-1-8v-7c0-2 0-3 1-5z" class="f"></path><path d="M493 701v-3c0-1 1-1 1-1v-5c1 2 1 3 1 5 1 2 1 4 1 7l1 13-2 2v-4-6c0-1-1-2-1-2l2-1v-2h-1c-1-1-1-2-2-3zm25-16l42 1c2 0 6-1 7 0 2 2 4 1 7 2l-1 1s-1 0-2 1l-1-1c-1 1-1 0-1 1-1 0-2 1-2 1l-1 1-4 3-1-1 1-1v-1c0-1 4-3 6-4h-23c-3 0-5 0-7-1-1 0-2-1-2-1-4-1-10 0-14 0-1 0-3 0-4-1z" class="U"></path><path d="M498 679v2c4 1 7 1 11 1 1-1 3 0 5-1l1 1c1-1 1-1 1-2l1 1h3l11 1 5-1c1 1 2 0 3 0 4 1 7 0 11 1h5c4 1 10 1 14 0 1 1 3 1 4 1l1 1h0c-1 0-3 0-4 1l-108-1h-4 0l1-1h-5v-1h4 22c5 0 13 0 18-1v-2z" class="h"></path><path d="M426 688c7-2 14-2 21-3 4 0 7-1 11-1h4l8 2h7l2 1c2 1 3 1 5 1l1-1h0c2 1 4 0 6 0h1v1 4c-1 1-5 2-6 2l-3-1h-2v2h3c1 1 1 1 3 1h0c-1 1-2 2-4 1h0c-2-1-4 0-6-1-2 0-5-1-7 0-2-2-3-4-5-5v1l-1-1-1 1-2-1c0-1-1-2-2-3l-1-1h-4c-5 1-10 1-15 2-1-2-3-1-5-2-3 1-5 1-8 1z" class="T"></path><path d="M470 686h7l2 1c-1 1-3 0-5 0-1-1-2-1-4-1h0z" class="k"></path><path d="M434 687c7-1 16-1 23-2h3c1 2 1 3 3 5h2v1 1l-1-1-1 1-2-1c0-1-1-2-2-3l-1-1h-4c-5 1-10 1-15 2-1-2-3-1-5-2z" class="X"></path><path d="M460 685h1l1 1c4 2 8 6 13 7h0c2 1 5 1 6 0v2h3c1 1 1 1 3 1h0c-1 1-2 2-4 1h0c-2-1-4 0-6-1-2 0-5-1-7 0-2-2-3-4-5-5v-1h-2c-2-2-2-3-3-5z" class="L"></path><path d="M460 685h1c1 2 2 3 4 4 2 2 4 4 5 6 3 0 8 1 11 0h3c1 1 1 1 3 1h0c-1 1-2 2-4 1h0c-2-1-4 0-6-1-2 0-5-1-7 0-2-2-3-4-5-5v-1h-2c-2-2-2-3-3-5z" class="Q"></path><path d="M484 695l8-1c0 2 0 3-1 5v3l-1 6v1c2-1 2-1 2-3v7l-1 19h-1-1 0v1c0 1-1 2-1 3v1l-1 1v2c-1-1 0-3 0-4-1-2-2-3-3-3-1-5-3-10-4-15-2-7-6-13-9-19l-1-3c2-1 5 0 7 0 2 1 4 0 6 1h0c2 1 3 0 4-1h0c-2 0-2 0-3-1z" class="n"></path><path d="M487 721h-2l-1-1c-1-2-1-2-1-4l2-1c1 1 2 1 3 2-1 2 0 3-1 4z" class="b"></path><path d="M471 699l2-2 1 1-1 1c2 1 3 3 4 4 0 1 0 2 1 2l-1 2v1h3v1l-1 1c0 1 0 2 2 3h1v1h-1v-1c-1 0-1 0-1 1v2c1 1 2-1 2 1l-1 1h-1c-2-7-6-13-9-19z" class="k"></path><path d="M480 718h1v1l2 1c1 1 1 1 1 2v1 5h1c1 0 2-1 2-2s0-2 1-4c0 6 0 11-1 16v2c-1-1 0-3 0-4-1-2-2-3-3-3-1-5-3-10-4-15z" class="V"></path><path d="M492 706v7l-1 19h-1-1 0v1c0 1-1 2-1 3v1l-1 1c1-5 1-10 1-16l-1-1c1-1 0-2 1-4v-2l-1-1 1-2v-1l-1-1-2 1-1-1 3-1c1 0 2-1 3-1v1c2-1 2-1 2-3z" class="m"></path><path d="M492 706v7l-1 19h-1-1c0-5 1-10 1-15v-8c2-1 2-1 2-3z" class="V"></path><path d="M484 695l8-1c0 2 0 3-1 5v3l-1 6c-1 0-2 1-3 1l-3 1-1-1-1 1c-1-1-3-4-4-6-1-3-2-4-4-6l-1-1-2 2-1-3c2-1 5 0 7 0 2 1 4 0 6 1h0c2 1 3 0 4-1h0c-2 0-2 0-3-1z" class="T"></path><path d="M488 701h0v-1l1-1h2v3l-1 6c-1 0-2 1-3 1l-3 1-1-1-1 1c-1-1-3-4-4-6l1-1c-1-1-1-2-1-3h1c0 1 0 2 2 3h2c1 1 1 2 1 3l1 1h0l1-2c0-1-1-1-1-2 1-1 2-1 3-2z" class="g"></path><path d="M488 701h0v-1l1-1h2v3c-1 1-2 1-2 3l-1-1v-3z" class="c"></path><path d="M561 694l1 1-2 3 1 2-2 2c1 0 2 1 2 2l-4 12c-1 4-2 8-2 12h0c1-1 1-6 2-6 0 1 0 1 2 2l-3 9c0 2-1 6-2 8h0l-1 3h-1l-1-1-1 15c2 0 2 0 3-1 2 0 3 1 4 1-2 2-2 2-2 4 0 1-1 2-1 3v3 1c1 0 1 1 1 0l2 2c0 2-1 2-2 3l-1-1-2 1c1 0 1 1 1 2l1 3h-1v-1l-1 1c-2 1-13 1-15 0l-1-1h0v-2h-26c-6 0-12 0-18 1h0c0-2 0-2 1-3v-2c-1 0-1-1-2-1-1-1 0-2 0-4l1-1v-1c1 1 1 2 2 3 1-1 0-2 0-3 1-1 2-2 2-3l1 1v8h1v-2h1v2h3 3 16 9c-1-2-1-4-1-6v-5-8c-1-2-1-5-1-7h1 0 1 1 1v-3-2l1-1-1-1c2 0 2 0 4-1l-1-1v-4h1v2h1 1v-4c0-1 1-1 1-2 0-2 0-2 1-4h0c-1-2-1-2-1-4 1-1 1-1 1-2-1-3-1-5 0-7 1-1 2-1 3-2l-1-1c1-1 1-1 1-2 1-1 0-2 1-3 0 1 1 1 2 2 0-2 0-2 1-3l1 1v1c1-1 2-1 2-2 2-2 3-3 6-3 0-1 4-4 5-5z" class="o"></path><path d="M540 738l1 2c1 1 1 1 2 1-1 1 0 2-1 3h-2v-3-3h0z" class="R"></path><path d="M541 776c-2 0-3 1-4 0v-3h2l1 1 2 1c-1 0-1 1-1 1z" class="C"></path><path d="M539 773c3 0 6 0 10 1-3 1-5 1-7 1l-2-1-1-1z" class="F"></path><path d="M549 773l1 1v3l-9-1s0-1 1-1c2 0 4 0 7-1v-1z" class="H"></path><path d="M542 734c1 0 2 0 3 1s1 2 2 3h-2c-1 1 0 1-1 2v1h-1c-1 0-1 0-2-1l-1-2c1-1 1-2 2-3v-1z" class="m"></path><path d="M542 734c1 0 2 0 3 1s1 2 2 3h-2-2c-1-1-1-2-1-3v-1z" class="f"></path><path d="M548 717c1 1 1 2 2 3h-1c0 1-1 2 0 4v1 1 3h-2c0 2-1 3-1 5h1l-2 1c-1-1-2-1-3-1v-2l1-2c0-1 1-2 1-3s1-3 2-4c0-2 1-3 1-5l1-1z" class="e"></path><path d="M544 732c1-1 1-2 2-3h1c0 2-1 3-1 5h0l-2-2z" class="Q"></path><path d="M543 730c0 1 1 1 1 2l2 2h0 1l-2 1c-1-1-2-1-3-1v-2l1-2z" class="R"></path><path d="M550 723l3-1c-2 7-3 12-4 19v3c-1 0-2 0-2-1h-2v-1h2v-1h-3v-1c1-1 0-1 1-2h2c-1-1-1-2-2-3l2-1h-1c0-2 1-3 1-5h2v-3-1l1-2z" class="a"></path><path d="M547 734c1 3 1 5 1 7l-1 1v-1h-3v-1c1-1 0-1 1-2h2c-1-1-1-2-2-3l2-1z" class="d"></path><path d="M544 741h3v1h-2v1h2c0 1 1 1 2 1v-3l-1 16h-1-1c-2 0-2 0-4-1v-1c-2 0-2 0-3-1 1-1 1-1 1-2v-3h2c0-1-1-1-1-2l2-1-1-2h0c1-1 0-2 1-3h1z" class="b"></path><path d="M533 739h1v2c0 1 1 1 0 2v2h0l1 1-1 11h-2-2v1h0 2l-1 2h2c-1 1-1 2-2 3v1h0c1 1 2 1 3 2-1 0-1 0-2-1l-1 1h0l1 2c0 1-1 1 0 2h1l-1 1h-1-1 0c-1-2-1-4-1-6v-5-8c-1-2-1-5-1-7h1 0 1 1 1v-3-2l1-1z" class="Z"></path><path d="M553 757c2 0 3 1 4 1-2 2-2 2-2 4 0 1-1 2-1 3v3 1c1 0 1 1 1 0l2 2c0 2-1 2-2 3l-1-1-2 1c1 0 1 1 1 2l1 3h-1v-1l-1 1c-2 1-13 1-15 0v-2h2c3 1 7 0 11 1h1l-1-1v-3-16c2 0 2 0 3-1z" class="V"></path><path d="M553 722h0v-3l1-1h0l-3 25-1 15v16l-1-1v-1h-2c-2 0-8 0-9-1 0-1 1-1 2-2v-1h1c0 1 0 2 1 3h4c1-1 0-1 0-2l2-1c1-1 0-2 0-3l-1-1-1-1c1-1 2-1 2-2v-4l1-16c1-7 2-12 4-19z" class="I"></path><path d="M539 760c0-2 0-3 1-4h2c2 1 2 1 4 1h1 1v4c0 1-1 1-2 2l1 1 1 1c0 1 1 2 0 3l-2 1c0 1 1 1 0 2h-4c-1-1-1-2-1-3h-1v1h-2l-1-1 1-1h1v-1h0l-1-1c0-3 0-3 1-5z" class="L"></path><path d="M539 760c0-2 0-3 1-4h2c2 1 2 1 4 1h1 1v4c-1 1-4 0-6 0-1 0-1-1-2-1h-1z" class="M"></path><path d="M493 774c8-2 16-2 23-2s13 0 19 1c1 1 0 3 1 3h-26c-6 0-12 0-18 1h0c0-2 0-2 1-3z" class="D"></path><path d="M559 702c1 0 2 1 2 2l-4 12c-1 4-2 8-2 12h0c1-1 1-6 2-6 0 1 0 1 2 2l-3 9c0 2-1 6-2 8h0l-1 3h-1l-1-1 3-25h0l-1 1v3h0l-3 1-1 2v-1c-1-2 0-3 0-4h1c-1-1-1-2-2-3 1-1 2-2 2-4 0-1 1-1 1-3 1-1 2-2 2-3 3-2 5-3 6-5z" class="R"></path><path d="M559 702c1 0 2 1 2 2l-4 12-1-1v-3l-2-2h-1c1 1 2 2 1 3 0 1-1 2-1 4h-1c-1 2-1 4-2 6l-1 2v-1c-1-2 0-3 0-4h1c-1-1-1-2-2-3 1-1 2-2 2-4 0-1 1-1 1-3 1-1 2-2 2-3 3-2 5-3 6-5z" class="c"></path><path d="M561 694l1 1-2 3 1 2-2 2c-1 2-3 3-6 5 0 1-1 2-2 3 0 2-1 2-1 3 0 2-1 3-2 4l-1 1h-1c-1 2-2 5-3 7-1 1-1 1-1 2s0 1-1 2h-1v-2h1v-1c0-2 0-1-1-2h0c-1-2-1-2-1-4 1-1 1-1 1-2-1-3-1-5 0-7 1-1 2-1 3-2l-1-1c1-1 1-1 1-2 1-1 0-2 1-3 0 1 1 1 2 2 0-2 0-2 1-3l1 1v1c1-1 2-1 2-2 2-2 3-3 6-3 0-1 4-4 5-5z" class="a"></path><path d="M560 698l1 2-2 2c-1 2-3 3-6 5 1-1 1-2 2-3h0l2-3 2-3h1z" class="P"></path><path d="M540 711c1-1 2-1 3-2l-1-1c1-1 1-1 1-2 1-1 0-2 1-3 0 1 1 1 2 2 0-2 0-2 1-3l1 1v1c1-1 2-1 2-2 2-2 3-3 6-3l-2 3h0c-1 2-3 5-4 7l-1-1h-1l2-1-1-1-3 1s0 1-1 2c-1 2-3 3-3 5l-1 2 2 1-1 2h0l-1 1v1l-1 3c-1-2-1-2-1-4 1-1 1-1 1-2-1-3-1-5 0-7z" class="f"></path><path d="M439 689c5-1 10-1 15-2h4l1 1c1 1 2 2 2 3l2 1 1-1 1 1v-1c2 1 3 3 5 5l1 3c3 6 7 12 9 19 1 5 3 10 4 15 1 0 2 1 3 3 0 1-1 3 0 4v1 8c1 2 0 4 1 6h1c0 1 1 1 2 1v1l-1 1c1 1 1 1 2 1h0 0c0 1 0 2 1 3-1 0-1 0-2 1l-2 2h3 0v1l-1 1c0 2-1 3 0 4 1 0 1 1 2 1v2c-1 1-1 1-1 3h0c-1 1-2 1-3 1l-5 1h-1c-2 1-11 1-13 0l-1 1v-1-1h-1v1l-1-1-1-1v-1l-18-2c-11 0-23 0-34 2-3 0-8 0-11 2l1 2h-5c0-1 1-3 1-4 1 0 0 0 0-1h-1c-1-1-1-2-1-3v-3-4h1v-1l3-3 2 1h0c2 0 4-1 6-1 2-1 4-1 6-1h0v-4-3l-1 1h0c0-4 2-8 3-11-1-2-1-3-1-4l4-10c2-6 4-13 5-19l3-7 1-1h0v-2-1l1-2-2-1-1-2c-2 0-6-2-8-1h-3l2-3 4 1c2-1 3 0 5-2-2 0-4 0-5-1l1-1 2 1c3 0 5 0 8-1 2 1 4 0 5 2z" class="T"></path><path d="M440 706l-2-1c1-1 1-2 2-2l2 2c1 1 2 1 3 2l1 1v2h1v4l1 1c0-1 0-1 1-2l1 1h-1v4h-1l-1 3-1-1c-1-2 0-4-1-6-1 1-1 1-1 3-1 3-2 6-2 9-1 2-1 4-2 6 0-3 0-7 1-10v-1c-1 0-2 1-3 1h0c0-3 2-7 2-10v-6z" class="P"></path><path d="M440 706l1-1v1 3h1v-2h1c1 4-1 10-2 15v-1c-1 0-2 1-3 1h0c0-3 2-7 2-10v-6z" class="Y"></path><path d="M430 719l1 1v-1l1-1c1 3-3 13-4 16l-6 11c-2 4-3 8-6 11v-3l-1 1h0c0-4 2-8 3-11 0-2 1-3 2-5h1 0l2-4c3-5 4-11 7-15z" class="R"></path><path d="M418 743c0-2 1-3 2-5h1l-5 15-1 1h0c0-4 2-8 3-11z" class="K"></path><path d="M429 707h0l5-7-3 9c0 1-1 2-1 3 3-2 4-8 6-11v1l-6 17c-3 4-4 10-7 15l-2 4h0-1c-1 2-2 3-2 5-1-2-1-3-1-4l4-10c2-6 4-13 5-19l3-7 1-1-1 5z" class="Y"></path><path d="M426 710l3-7 1-1-1 5c-2 1-3 7-3 10l-3 12c0 1-1 2-1 3l1 2-2 4h0-1c-1 2-2 3-2 5-1-2-1-3-1-4l4-10c2-6 4-13 5-19z" class="O"></path><path d="M444 717c0-2 0-2 1-3 1 2 0 4 1 6l1 1 1-3h1 0c1 2-1 5-1 7-2 5-4 10-5 15s-3 16-1 20l1-1h-1c-1-2 0-9 0-11l1-4v-2c3-9 5-19 10-27h0c1 2 0 3-1 5s-1 4-2 6c-1 4-3 8-4 12-1 3-1 6-2 9s-1 7 0 10v-8c1-6 3-11 5-17l4-11c1-1 1-2 1-2 0-2 0-1 1-2v1l-4 12c-1 4-3 7-4 10-2 7-2 13-1 20h10c2 0 5 1 7 1h4l-1 1h4 1c0 1-1 2-2 3h1c1 1 0 1 1 2v2c0 1 0 3-1 4h-2v-3h-1l-1 1v3c1 1 3 0 5 0v1c-2 0-2 0-3 1-1 0-1 0-2 1v-1l-18-2h-4c1 0 1-1 2-1h-1v-12h-1c-1 3 0 8 0 12h-1-1c1-3 0-8 1-11l-1-1c-1 2 0 9 0 12h-1-1v-10-1-1-1c-1-1-1-2-1-2l-1-6 3-18v-1c1-4 5-13 3-16z" class="G"></path><path d="M444 717c0-2 0-2 1-3 1 2 0 4 1 6l1 1h0l-3 9c-2 5-3 10-4 16-1 3 0 6 0 10v4c-1-1-1-2-1-2l-1-6 3-18v-1c1-4 5-13 3-16z" class="g"></path><g class="T"><path d="M446 773c1-3 0-8 1-12v1 11c1 0 3 1 4 0 0-3-1-10 1-12 1 1 0 10 1 12 3 1 7 1 10 1 0-2-1-4 0-6v-3-3l1-1c0 1 1 1 1 1h1 4 1c0 1-1 2-2 3h1c1 1 0 1 1 2v2c0 1 0 3-1 4h-2v-3h-1l-1 1v3c1 1 3 0 5 0v1c-2 0-2 0-3 1-1 0-1 0-2 1v-1l-18-2h-4c1 0 1-1 2-1z"></path><path d="M455 718h2c0 1 1 2 2 3 0 1 3 5 3 6 2 4 4 9 5 13s2 9 3 13c0 2-1 4 0 6l1 1c-2 1-5 0-8 1-2 0-5-1-7-1h-10c-1-7-1-13 1-20 1-3 3-6 4-10l4-12z"></path></g><path d="M454 739h0l1 1c0 1 0 2-1 3h-1l-1-1c0-2 1-2 2-3z" class="I"></path><path d="M467 740c-2 0-3 0-4-2v-2c-1-1 0-3-2-5v-1c0-1 0-2 1-3 2 4 4 9 5 13z" class="P"></path><path d="M444 717c2 3-2 12-3 16v1l-3 18 1 6s0 1 1 2v1 1 1 10h1 1c0-3-1-10 0-12l1 1c-1 3 0 8-1 11h1 1c0-4-1-9 0-12h1v12h1c-1 0-1 1-2 1h4c-11 0-23 0-34 2-3 0-8 0-11 2l1 2h-5c0-1 1-3 1-4 1 0 0 0 0-1h-1c-1-1-1-2-1-3v-3-4h1v-1l3-3 2 1h0c2 0 4-1 6-1 2-1 4-1 6-1h17c-1-12 3-26 5-38h0c1 0 2-1 3-1v1c-1 3-1 7-1 10 1-2 1-4 2-6 0-3 1-6 2-9z" class="O"></path><path d="M438 752l-2 9c-2-4-1-9 0-13 1-2 0-5 1-8v-1c0-2 1-3 1-5h1l1-1 1 1-3 18z" class="H"></path><path d="M417 761h2c-1 1-1 1-1 2 1 3 1 6 0 9h1c1-3 0-8 1-11h1v11h1 0v-1c0-2-1-9 1-10 1 2 0 9 0 12h1v-1c-1-2 0-8 0-10l1-1c0 3 1 9 0 12h2l-1-1v-11h1c1 3 0 8 1 12h1v-1c-1-3-1-8 0-11h1c0 4-1 8 0 12h1v-1c0-3-1-8 0-11h1v1 11h7c0-3-1-9 0-11l1-1v1 1 10h1 1c0-3-1-10 0-12l1 1c-1 3 0 8-1 11h1 1c0-4-1-9 0-12h1v12h1c-1 0-1 1-2 1h4c-11 0-23 0-34 2-3 0-8 0-11 2l1 2h-5c0-1 1-3 1-4 1 0 0 0 0-1h-1c-1-1-1-2-1-3v-3-4h1v-1l3-3 2 1h0l2 1c1 1 1 2 1 3 1-1 1-3 2-4v1c0 2 0 9 1 9 1-2-1-7 1-9h0v3c0 2 0 4 1 5h0c0-2 1-6 0-8v-1h2c1-1 2-1 3-1z" class="m"></path><path d="M411 766c0 2 0 4 1 5h0c0-2 1-6 0-8v-1h2c1-1 2-1 3-1l1 2h-1c-1 3 1 8-1 11h-5c-1-1 0-5 0-6v-2z" class="a"></path><path d="M439 689c5-1 10-1 15-2h4l1 1c1 1 2 2 2 3l2 1 9 32 1 8 1 15c0 1 0 1-1 2v1 5l-1-3-1 1h0v2 1c-1-1-1-2-1-3-1-4-2-9-3-13s-3-9-5-13c0-1-3-5-3-6-1-1-2-2-2-3h-2v-1l1-1s-1-1-1-2c-3-3-5-6-8-10-1 0-2 0-2-1v-1c-4-3-9-5-14-7h0l1 1c3 1 5 2 7 4h0c-3 0-5-2-8-3l-2-1-1-2c-2 0-6-2-8-1h-3l2-3 4 1c2-1 3 0 5-2-2 0-4 0-5-1l1-1 2 1c3 0 5 0 8-1 2 1 4 0 5 2z" class="T"></path><path d="M468 712c0 2 1 4 1 6-1-1-3-2-3-3l2-3z" class="c"></path><path d="M465 701l2 10c0-1-1-2-2-3l-2-4c0-1 0-1-1-2l3-1z" class="P"></path><path d="M460 693l1 1 1 1c-2 1-8 1-10 1l-2-1c1 0 1-1 2-2v1l2-1 1 1c1-1 3-1 5-1z" class="M"></path><path d="M461 691l2 1 9 32 1 8 1 15c0 1 0 1-1 2v1 5l-1-3c1-3 0-7 0-10-1 0-1 0-2-1v-2h0v-1c-1 0-2 1-2 1l-3-9 2-1 1 1 1 2 1-1c1-1-1-2-2-4h1c1-1 1-2 1-3l-1-6c0-2-1-4-1-6l-1-1-2-10c-2-3-3-6-4-10z" class="F"></path><path d="M470 724l2 18c-1 0-1 0-2-1v-2h0v-1c-1 0-2 1-2 1l-3-9 2-1 1 1 1 2 1-1c1-1-1-2-2-4h1c1-1 1-2 1-3z" class="k"></path><path d="M419 690l4 1c2-1 3 0 5-2-2 0-4 0-5-1l1-1 2 1c3 0 5 0 8-1 2 1 4 0 5 2-3 0-7 1-11 2h-3c1 1 0 1 1 1 10 1 21 8 27 16 4 6 10 15 12 22l3 9s1-1 2-1v1h0v2c1 1 1 1 2 1 0 3 1 7 0 10l-1 1h0v2 1c-1-1-1-2-1-3-1-4-2-9-3-13s-3-9-5-13c0-1-3-5-3-6-1-1-2-2-2-3h-2v-1l1-1s-1-1-1-2c-3-3-5-6-8-10-1 0-2 0-2-1v-1c-4-3-9-5-14-7h0l1 1c3 1 5 2 7 4h0c-3 0-5-2-8-3l-2-1-1-2c-2 0-6-2-8-1h-3l2-3z" class="M"></path><path d="M468 739s1-1 2-1v1h0v2c1 1 1 1 2 1 0 3 1 7 0 10l-1 1-3-14z" class="g"></path><path d="M463 692l1-1 1 1v-1c2 1 3 3 5 5l1 3c3 6 7 12 9 19 1 5 3 10 4 15 1 0 2 1 3 3 0 1-1 3 0 4v1 8c1 2 0 4 1 6h1c0 1 1 1 2 1v1l-1 1c1 1 1 1 2 1h0 0c0 1 0 2 1 3-1 0-1 0-2 1l-2 2h3 0v1l-1 1c0 2-1 3 0 4 1 0 1 1 2 1v2c-1 1-1 1-1 3h0c-1 1-2 1-3 1l-5 1h-1c-2 1-11 1-13 0l-1 1v-1-1h-1v1l-1-1-1-1c1-1 1-1 2-1 1-1 1-1 3-1v-1c-2 0-4 1-5 0v-3l1-1h1v3h2c1-1 1-3 1-4v-2c-1-1 0-1-1-2h-1c1-1 2-2 2-3h-1-4l1-1h-4c3-1 6 0 8-1l-1-1c-1-2 0-4 0-6 0 1 0 2 1 3v-1-2h0l1-1 1 3v-5-1c1-1 1-1 1-2l-1-15-1-8-9-32z" class="n"></path><path d="M488 755h1c0 1 1 1 2 1v1l-1 1c1 1 1 1 2 1h0 0c0 1 0 2 1 3-1 0-1 0-2 1l-2 2h0c-1-3-1-7-1-10z" class="g"></path><path d="M483 737v3h0c0 2 0 5 1 7l-1 1 1 1-1 1 1 1-1 1-1-1v-1-2-1c-1-2-1-3-1-4l-1-1v-2l-2-1h1 2l-1-1 3-1z" class="R"></path><path d="M489 765h3 0v1l-1 1c0 2-1 3 0 4 1 0 1 1 2 1v2c-1 1-1 1-1 3h0c-1 1-2 1-3 1s-2-1-3-1h0c0-1 1-1 2-1v-1h-2-1c-1-1-1-1-2-1h0l4-1h0c-1-1-2-1-2-1l2-1 1 1 1-1v-6h0z" class="Q"></path><path d="M473 723c1 2 2 2 1 4v1c1 1 0 2 1 3v4h3c0 1 1 1 2 2v1l1 1h-2-1c0 1-1 1-2 1v5 2h1 0c-1 2-1 2-1 5h1 1 0-1c-1 1 0 1-1 2v2c1 1 0 2 0 3v2c1 3 0 8 1 11 0 1 0 1 1 2h5 0c1 0 1 0 2 1h1 2v1c-1 0-2 0-2 1h0c1 0 2 1 3 1l-5 1h-1c-2 1-11 1-13 0l-1 1v-1-1h-1v1l-1-1-1-1c1-1 1-1 2-1 1-1 1-1 3-1v-1c-2 0-4 1-5 0v-3l1-1h1v3h2c1-1 1-3 1-4v-2c-1-1 0-1-1-2h-1c1-1 2-2 2-3h-1-4l1-1h-4c3-1 6 0 8-1l-1-1c-1-2 0-4 0-6 0 1 0 2 1 3v-1-2h0l1-1 1 3v-5-1c1-1 1-1 1-2l-1-15-1-8 1-1z" class="U"></path><path d="M475 735h3c0 1 1 1 2 2v1l1 1h-2-1c0 1-1 1-2 1l-1-5z" class="V"></path><path d="M473 755v-5-1c1-1 1-1 1-2l1 19c0 3 1 7 0 10l-2-2v-11c0-2-1-6 0-8z" class="K"></path><path d="M463 692l1-1 1 1v-1c2 1 3 3 5 5l1 3c3 6 7 12 9 19 1 5 3 10 4 15 0 3 1 7 0 10h0l-1-3h0v-3l-3 1v-1c-1-1-2-1-2-2h-3v-4c-1-1 0-2-1-3v-1c1-2 0-2-1-4l-1 1-9-32z" class="e"></path><path d="M470 706h1c0 1 0 2 1 2 0 2 0 3-1 5v-1c-1-2-2-4-1-6z" class="V"></path><path d="M472 713h2c0 1 0 2-1 4l1 2h-1c-1-2-1-3-2-5l1-1z" class="j"></path><path d="M473 723l1-1h1c1 2 1 4 1 6l3 6-1 1h-3v-4c-1-1 0-2-1-3v-1c1-2 0-2-1-4z" class="d"></path><path d="M465 691c2 1 3 3 5 5l1 3c3 6 7 12 9 19 1 5 3 10 4 15 0 3 1 7 0 10h0l-1-3h0v-3c-3-11-7-20-11-31 0-2-2-4-2-6-1-3-4-6-5-8v-1z" class="F"></path><path d="M448 774l18 2v1l1 1 1 1v-1h1v1 1l1-1c2 1 11 1 13 0h1l5-1c1 0 2 0 3-1 6-1 12-1 18-1h26v2h0l1 1c2 1 13 1 15 0l1-1v1h1l55 1c2 1 3 1 4 1v1c-1 0-3 0-3 1h0c1 0 2 1 3 1h1c3-1 4-1 7 0 2 0 3 0 4-1 2 0 3 0 5 1v16l2 1v6h1l1-3 1 1c1-2 2-2 3-2 0 2 1 4 0 5 1 2 1 3 1 5v3 3 3h1l-1 2c-2-1-5 0-7 0h-14-2c-2 0-5 0-8 1l1 1h1 2c0 1-1 1-2 1h0l1 1h1c2 1 4 1 7 2v2c0 2-1 5 0 7l-1 4c1 2 0 7 2 9l-3 3v1h-1c0 2-1 3-2 4s-3 1-4 2l-3-1 2 1v2h-5l6 4c1 0 2-1 3 0 1 0 3 1 3 2l1 3h3 1l-1 1v2l-1 1h0c2 3 3 6 5 8v1h0c1 2 2 3 2 5l1 3 1 2v1c1 3 0 12-1 14l-1 1h0v2c-1 1-1 2-1 3-1 0-2 1-3 1-1 1 0 1-1 1l-1-1-2 2v-1-2c1 0 1-1 1-2h1v-3c0-1 1-2 1-3l-2-1v-2h-5-1l-3 1v2l-1 1c-1 0-1 0-1-1l-2-1c-1 0-1 0-2-1h-1v-2l-2 1v-2-3l-1-1 1-1-1-1v-2-2c-3 2-5 1-8 2l-2 1h-2l-2 1h0c-1 1-1 1-2 1s-3 1-3 1v1 1h-1-3c0-2-1-3-2-4-1-2-2-3-3-5-3-3-7-6-10-9-1-1-1-1 0-2-2-1-3-2-5-2-1-1-2 0-3 1-1-1-3-2-4-2h-3c-2 1-2 1-3 2 0 1-1 1 0 2l-2 1v1l1 1-1 1-2-1v1 3l1 10c0 1 1 3 1 4v2c-8-1-17-1-25 0l-1-1v-10-6l-2-1h-3-4-8c-1-1-1-2-1-3h-2c-2 0-4 0-5 1-2 1-4 1-6 2h-1c0-2 0-4 1-6l-1 1-4 2c-1 0-1 1-2 1 0 1-1 1-2 2-1 0-2 1-3 2 0 2 0 2 1 3-2 2-2 3-5 4l-1 1v1s0 1 1 2l-1 1-1 1 2 1c-1 1-2 1-2 2-1 0-1 1-1 1h-1l-2 2h-1c-4 0-6-1-9-2l-1 1h-2 0l-2 3-3 5v1c-1 0-2 1-2 2l1 1v1h0l1 1v1l-4 1h-4-4c-1-1-3-3-3-4l-1-4c-1-1-1-4-2-5l-1-1h0v-4h-8v-1-1-1-1c-2 1-5 1-7 1h0v-6h0c0-2 1-3 2-5 0-3 1-4 2-7l1-2 7-9-1-1-2 1 1-2h1c2-1 2-1 3-3 0 0 1-1 2-1v-2h2l2-2c-1 0-2 0-2 1h-3v2h-1c-1-1-1 0-1 0h-1c-2 1-4 1-5 2h-2l-1-1c1 0 3-2 3-3-1-1-1-1-1-3 1 0 1-1 2-1l1-1-1-1h-4v-2h-2c-1 4-3 6-4 9l-3-1-2-1-4-4-7-7h0-1v-1c-2-1-4-2-6-2-1-1-1-2-1-3l-2 1v-2s0-1 1-2h1v-1-1s-2-1-3-1l-1 1h-3-5l-3-1 3-4 2-3 2-2 3-7-1-1c-1 2-2 3-2 5-1 0-1 1-2 0l-3 1v1c-1 1-2 1-3 2h-1 0-1c0-1 0-1 1-2h1c0-1 0-1-1-2l1-1c0-1 2-3 2-4v-2c1-1 2-1 2-2 1-1 0-2 0-3l2-1v-2l-1-1c2-6 4-12 4-19l1 1c1-1 1-1 1-2h0v-1l1 1v-2c2-2 3-1 5-1v-2h7 3c1-1 1-1 2-1l-2-1c0-1 1-2 2-2l-2-2 1-2h0c1 0 1 0 2-1v1h5l-1-2c3-2 8-2 11-2 11-2 23-2 34-2z" class="T"></path><path d="M460 807c0-1-1-2-1-3l1-1h2v1c0 1-1 2-2 3z" class="f"></path><path d="M415 821c-1 0-2-1-3-1-1-1-1-1-1-2v-1c2 1 2 1 4 1v3z" class="c"></path><path d="M407 822c2 0 4 1 5 3-1 1-1 1 0 2-3-1-5-2-5-5zm55-18l3 3h1c-2 1-3 1-5 1l-1-1c1-1 2-2 2-3z" class="h"></path><path d="M407 805v-2c-1-2 0-2 1-3 0 1 1 2 1 3 1 1 2 2 2 3l1 2h-1c-2 0-2-1-4-3z" class="e"></path><path d="M597 816l1 4h1 1l-1 7v5-4h-2c0-2 0-2 1-3-1-3-1-6-1-9z" class="c"></path><path d="M463 801v-1h2 0l1 1c1 1 1 2 1 3-1 1-1 2-2 3l-3-3v-1h-2l3-1h0v-1z" class="P"></path><path d="M415 789c0-2 0-3 1-4h15-7c-2 0-2 0-4 1v1h2c0 1 0 1-1 1-2 0-4 1-6 1z" class="f"></path><path d="M584 809l-2-2v-1h3l8 5-1 1-1-1c-3 0-5-1-7-2z" class="d"></path><path d="M453 817l1-1v1c1 1 1 1 3 2 0 0 1 1 2 1 1 1 1 1 1 2 0 0-1 0-1 1h1c1-1 2-1 4-1l1 1c-1 1-2 1-3 1l-2 1c-3-3-5-5-7-8z" class="a"></path><path d="M431 785c2 0 7 0 9 1-2 1-5 1-7 1-2 1-6 1-8 1-1-1-1-2-1-3h7z" class="L"></path><path d="M433 787c2 0 4 1 6 1v1c-3 0-6 0-8 1h-1 0v1h-6c0-1 1-2 1-3 2 0 6 0 8-1z" class="F"></path><path d="M407 798h0c0-1-1-1 0-2 2 0 2 1 4 0 0 1 0 1 1 2v3 1c-1 1 0 3-1 4 0-1-1-2-2-3 0-1-1-2-1-3s-1-1-1-2z" class="Q"></path><path d="M463 801c-2-1-3-1-3-3s0-2 2-4l1 1v1h1l1-1h0c1 2 1 3 1 5h-1-2v1z" class="j"></path><path d="M576 788h2v19h-1c-1-2-1-15-1-19z" class="L"></path><path d="M542 855l-1-1c0-2-1-2-2-2 0-1-1-1 0-2l2-2c1-1 1-1 2-1l3 2 2-1 1 1c-1 1-2 2-3 2-2 0-3 2-4 4z" class="c"></path><path d="M586 888c1 0 2 0 3-1 1 1 2 1 2 2l-1 1v1c1 0 2 0 4-1v1c-1 1-2 1-3 1-1 1-3 1-3 0-3-1-10 0-13 0 2 0 3-1 5-1 1 0 1-1 2-1h2l2-1v-1z" class="K"></path><path d="M599 892s1 1 1 2c-3 2-5 1-8 2l-2 1h-2l-2 1h0-8c3-1 6-1 10-3h-1l2-1c1 0 2 0 4-1h3 1c0-1 1-1 2-1z" class="E"></path><path d="M499 783c-1-2-1-1 0-3 6 0 11-1 17 0 2 0 5-1 7 0 2 0 3 0 4 2h-3c-2 0-4 0-6 1l-2-1v-1c-5 0-11 1-15 0l-1 2h0v1l-1-1z" class="g"></path><path d="M404 780l-1-2c3-2 8-2 11-2 2 2 4 2 7 2 6 1 13-1 19 1h-21c-5 0-11 0-15 1z" class="J"></path><path d="M593 875l1 3c-1 2-2 2-4 2-1 1-3 1-4 2h-1c-1 1-3 1-5 0l6-3h-1l-1 1c-2 0-9 1-10 1 0-1 1-1 2-2h3l2-1h1 2c1 0 2-1 2-1h1 1l1-1c1 0 2 0 4-1z" class="D"></path><path d="M518 783c2-1 4-1 6-1h3l1 5v2h-1v-1h1l-1-1h-10-1v1l-1-1c1-2 1-3 1-5l2 1z" class="j"></path><path d="M518 783c2-1 4-1 6-1h3l1 5c0-1-1-3-1-3h-2c-3 0-5 0-7-1z" class="R"></path><path d="M591 889c2 0 3 0 4-2h-2v-1c1-1 3-1 4-2l2 8c-1 0-2 0-2 1h-1-3c-2 1-3 1-4 1l-2 1-9 1c3-2 6-3 10-4 0 1 2 1 3 0 1 0 2 0 3-1v-1c-2 1-3 1-4 1v-1l1-1z" class="J"></path><path d="M421 788c1 0 1 0 1-1h-2v-1c2-1 2-1 4-1 0 1 0 2 1 3h-1l-3 3h0c-1 1-1 2-1 3v1c-1 1-2 2-2 3l1 1-2 2h-2c0 3 1 8 0 11v-15-8c2 0 4-1 6-1z" class="a"></path><path d="M415 789c2 0 4-1 6-1 0 0-1 1-1 2-2 0-3 0-3 3h0l-1 1c-1 1-1 1-1 3v-8z" class="R"></path><path d="M533 817c1 1 1 1 1 2v1l2 2-1 1c1 1 1 1 2 1v1c-1 0-1 1-2 2 0 1 0 1 1 2l1-1v1 1l-1 1c1 1 1 1 2 1 0 1 0 1-1 2h1 0 2l1 1-1 1c2 1 4-1 5 1l-3 1c-1 1-1 1-1 2h3l-3 3h0-1v-1l-2 2v-2c-2 0-1 0-2-1v-4c-1-1-1-2-1-2 0-3-1-5-1-8l-1-10z" class="c"></path><path d="M475 806c0-2 0-3 1-5 0-3-1-6 1-8v-2c0-1 1-1 1-1 1-2 2-2 4-2v1c-1 0-2 0-3 1 1 0 2 0 3 1s0 2 0 3h-1l-1 1h2l-1 1 1 2v2 4c-1 0-1 0-2 1h0c-2 0-3 0-5 1z" class="d"></path><defs><linearGradient id="P" x1="412.478" y1="811.254" x2="419.154" y2="813.322" xlink:href="#B"><stop offset="0" stop-color="#5b5a58"></stop><stop offset="1" stop-color="#767574"></stop></linearGradient></defs><path fill="url(#P)" d="M415 812c1-3 0-8 0-11h2l2-2-1-1c0-1 1-2 2-3h0v4c0 2-1 3-1 5-1 2 0 4-1 6-1 3-1 8-1 11 1 2 1 3 0 5 0 1-1 2-1 2-1 2 1 3-1 4v-3-8-3-6z"></path><path d="M590 831c-2 0-2-1-3-2h0c2 0 3 0 5 1h0v-1c1 1 2 1 2 2h0-1-2v1h1c1 1 1 1 2 1-1 1-1 1-2 1l2 3h0l-2-1-1 1c1 1 2 2 3 2v1l-1 1v2l-1-1-2 1c1 1 3 2 4 3l-1 1-1 1-1-1v-1h-2l-1 1 1 1h-1l-1-1h-1c-1-1-1-1-2-1v-1c-1-1 0-1-1-2 2 0 3 0 4 1v-2h0c1 0 2 1 3 0-2-2-3-2-3-5h3c-1-1-2-1-3-2l-1-2 3 1c1 0 1-1 2-2l-1-1z" class="h"></path><path d="M405 809l1 1c1 1 0 2 0 3v1l2 1c0 3 0 4-2 6l1 1c0 3 2 4 5 5v1l-2 1 3 3h0c-2-1-3-2-5-2l-1 1h0-2l3-2h0c-1-2-2-2-3-3h0v-2h-1l-1-2c-1 0-1-1-2-1l1-2v-1l1-1v-2h-1l1-1h-3l2-2h-2v-1h0l2-1c1 0 2 0 3-1h0z" class="e"></path><path d="M555 814h1l1-1v-1c1-1 3 0 4 0v1h1 1c1 3 1 5 2 8s2 6 3 10c-1-1-2-1-3-2 0-1-1-2-1-3s-1-2-1-3h-1c0-1-1-1-2-1h0-2c-1-1-2-2-2-3-1-2-1-3-1-5z" class="S"></path><path d="M477 881c2 1 1 0 3 0 0 0 3 1 4 2l-4 2c-1 0-1 1-2 1 0 1-1 1-2 2-1 0-2 1-3 2 0 2 0 2 1 3-2 2-2 3-5 4l-1 1c-1-3-2-6 0-9h0c1-1 2-1 3-1h2c-1-2-1-2-1-4 2-2 3-2 5-3z" class="a"></path><path d="M468 889c1-1 2-1 3-1v3 1h-2c0-1 0-2-1-3h0z" class="d"></path><path d="M477 881c2 1 1 0 3 0 0 0 3 1 4 2l-4 2c-1 0-1 1-2 1 0 1-1 1-2 2v-3h-2v-1c1-1 2-1 3-3z" class="U"></path><path d="M442 815h1c1 1 1 1 2 1v1l1 1c0 1 0 1 1 1 0 1 0 2 1 3 0 0 0 1 1 2l1 1c2 2 4 5 6 6h1c0 2-1 1-3 1v1l2 2-1 1-6-6c-2 0-3-1-4-3l-1-1c-1-1-1-2-1-3-1-1-1-2-1-3-1-2 0-3 0-5z" class="h"></path><path d="M549 787h0 3v2h0c1 1 0 2 0 3v1c-2-1-2-1-3 0h3l1 1c-1 1-1 1 0 2v3 1c0 2 1 4 0 7l-1-2c1-1 1-1 1-2h-3v1c-1 1-1 1-2 1v1h1v1c-1 0-1 0-2 1-1-4 0-7-1-11 0-2 0-8 1-10h2z" class="Z"></path><path d="M594 878l3 6c-1 1-3 1-4 2v1h2c-1 2-2 2-4 2 0-1-1-1-2-2-1 1-2 1-3 1-3 1-7 2-10 1h-1c4-1 8-2 11-3 1 0 0 0 1-1-2 0-4 1-7 1l-8 1c3-1 5-2 8-3 2 0 4-1 6-1v-1c1-1 3-1 4-2 2 0 3 0 4-2z" class="C"></path><path d="M536 787c1-1 0-1 0-3l1-1h-1c-1 0-2 0-3-1l1-1c3 2 8 0 11 2v3h0 2c-1-1-1-2 0-2h1c0-1 1-1 1-1 2-1 2 0 3 0-1 1 0 1-1 1-2 0-3 0-4 2l2 1h-2c-1 2-1 8-1 10 1 4 0 7 1 11 0 2 0 4 1 6v4l-1-3h-1c-1 1-2 2-3 4h2v1h-3l-1-2h0c1-1 1-1 1-2v-1c1-1 2-2 3-2l1-1v-2c-1-3 0-6-1-9-1-2 0-1 0-2v-2-1l-1-1s1-1 1-2h-2v-1h2v-1l-1-1 1-1c0-1 0-1-1-2-3 0-5-1-8 0z" class="g"></path><path d="M399 787h7 6c1 1 0 1 0 3v4 1l-1 1c-2 1-2 0-4 0-1 1 0 1 0 2h0-1c-3 0-2-1-3-2h-1-4l1-2h0c0-2 1-2 2-3h0c-1 0-1-1-2-2v-2z" class="k"></path><path d="M399 789h5v2h-3c-1 0-1-1-2-2z" class="e"></path><path d="M401 791l1 2c0 1 0 1-1 2-1 0-1 0-2-1h0c0-2 1-2 2-3z" class="c"></path><path d="M406 793v-2-2c1 0 2 0 3 1-1 0-1 0-1 1h1v1c-1 1-2 1-3 1z" class="e"></path><path d="M412 790v4 1c-1 0-3 0-4-1h-4-1v-1h3c1 0 2 0 3-1h2v-2h1z" class="P"></path><path d="M570 830l-1-2v-1c0-1-1-1-1-2v-2-2c1-1 1-1 2-1l1-1c1 0 0-1 2 0h2c0-1-1-1-2-2l-1-1 2-2s0-1 1-1c0-2 1-3 3-4h1c0 3 0 3-1 5l1 1c0 1 0 2-1 3h-2c1 2 3 0 2 3v2 1c-2 1-3 1-5 0h-3 1l2 1h3l1 2c0 1-1 1-2 2l-2 1c-1-1-2 0-3 0z" class="n"></path><path d="M558 822h2 0c1 0 2 0 2 1h1c0 1 1 2 1 3s1 2 1 3c1 1 2 1 3 2l1 1c0 1 1 3 1 5l1 1 1 1 1 2c0 2-1 4-2 5h-2c0-1 0-1-1-2v1l-2-2-1-3-1-1c0-1-1-2-1-3v-1c0-1 0-2-1-3l-1-3-1-2v-3s-2-1-2-2z" class="H"></path><path d="M577 841l4 3-1 2h0c1 1 1 3 1 4 6 5 11 7 17 11l2 2-1 1c-4-3-9-5-13-7l-5-2-1-1c-1-1-1-1-2-1-1-1-1-1-2-1-1-1-2-2-3-2l-1 1c-1-1-2-2-2-3-1-1-2-2-2-3v-1c1 1 1 1 1 2h2c1-1 2-3 2-5l2 2c0-1 1-2 2-2z" class="I"></path><path d="M573 846h0 1c1 0 1 1 2 1v1l-1 1h-3 0c0-2 0-2 1-3z" class="J"></path><path d="M577 841l4 3-1 2h0c1 1 1 3 1 4l-6-7c0-1 1-2 2-2zm7-32c2 1 4 2 7 2l1 1 1-1c0 1-1 2 0 3 0 0 1 1 1 2v3c-2 2-1 2-1 4l1 1h0c-1 1-2 0-3 0v1c-3 0-5-1-7-2-2 0-2 0-3 1l-1-2c1-1 1-2 2-3v-1-2c-1-1-1-1-1-2h1v-1h1s0-1 1 0h0v-1c-1 0-1-1-2-2 1-1 1-1 2-1z" class="P"></path><path d="M469 808l3-1v-1h-2l1-1 1-1h-2v-1h2v-1h-1c0-1-1-1 0-2h3v4 2 6c0 1-1 0-1 2 0 3-1 7-2 10 0 2-2 3-1 6l-2 2h0c-1-1-1-2-1-3l-1-1c-1 1-1 1-1 2 0 2-1 3-2 4 0 1-1 2-2 3 0 0 0-1-1-1l4-9 1-1c1-2 1-3 2-5v-2c1-1 0-1 1-2v-1l2-1-2-1 1-2h-3l1-2v-2h2z" class="G"></path><path d="M467 808h2v4h-3l1-2v-2z" class="h"></path><path d="M542 820h3v-1h-2c1-2 2-3 3-4h1l1 3c-1 1-1 1 0 2v3h0c1 1 1 2 1 3l1 3c-1 0-2 0-3 1h-3l-2 2h0l2 1c0 1-2 1-3 2l-1-1h-2 0-1c1-1 1-1 1-2-1 0-1 0-2-1l1-1v-1-1l-1 1c-1-1-1-1-1-2 1-1 1-2 2-2v-1c-1 0-1 0-2-1l1-1-2-2v-1c3-1 5-3 8-4v1c0 1 0 1-1 2h0l1 2z" class="Q"></path><path d="M542 820h3v-1h-2c1-2 2-3 3-4h1l1 3c-1 1-1 1 0 2v3h0c1 1 1 2 1 3l1 3c-1 0-2 0-3 1h-3-3c1-1 2-2 2-3h-2 0c1-1 2-2 2-3l-1-1 1-1c-1-1-1-1-1-2h0z" class="E"></path><path d="M553 807h1v1h1c0-1 0-3-1-4l1-4h2l-1-1c-1 0-1 0-2-1 1-2 0-5 1-6h8c-2 1-3 2-5 2h-2c1 1 3 0 5 0h0v1l-1 1h-2c-1 1-1 1-2 1h0c1 0 4-1 6 0l-2 2h0 2c0 1-1 1-2 1-1 1-3 1-4 2h5c1 1 1 1 1 2h-1 0c1 1 0 1 1 2l1 7h-1-1v-1c-1 0-3-1-4 0v1l-1 1h-1-2c-1 0-1-1-1-1h-2l-1 1h-1c-1-2-1-4-1-6 1-1 1-1 2-1v-1h-1v-1c1 0 1 0 2-1v-1h3c0 1 0 1-1 2l1 2z" class="F"></path><path d="M448 774l18 2v1l1 1 1 1c-3 2-7 1-11 0h-17c-6-2-13 0-19-1-3 0-5 0-7-2 11-2 23-2 34-2z" class="W"></path><path d="M460 892c1 0 1 0 2-1 1 0 1-1 2-1s3-1 4-1h0c-2 3-1 6 0 9v1s0 1 1 2l-1 1-1 1 2 1c-1 1-2 1-2 2-1 0-1 1-1 1h-1l-2 2h-1c-4 0-6-1-9-2l-1-1c0-1 0-2 1-3 2-4 4-7 7-11z" class="e"></path><path d="M460 905v-1h1l1 1 1-1-1-1-1-1h1l2 1v-1-2h1l3 2-1 1 2 1c-1 1-2 1-2 2-1 0-1 1-1 1h-1l-2 2h-1l-1-2h-1v-2z" class="Q"></path><path d="M460 892l2 2v-1h2c0 1 0 2 1 3h-1-2c0 1-1 3 0 5h-3c-1 1 0 2 0 3l1 1v2h1l1 2c-4 0-6-1-9-2l-1-1c0-1 0-2 1-3 2-4 4-7 7-11z" class="l"></path><path d="M453 903h3l1 1-2 2h-3c0-1 0-2 1-3z" class="M"></path><path d="M542 855c1-2 2-4 4-4l2 1c1 1 2 1 3 0v1l-1 2 2 3 3-1h3v1l-1 1 2 1h0c0 1-1 1-2 1v1h2 1l1-1h2 0l-2 2h1 2 0v1l-2 1h1c1 0 3 0 5-1v1l-3 2h0 3c0-1 1-1 1 0h0c-1 1-1 2-3 2-2-1-5 0-7 1l-1 1h-2l-1-1v-1c-1 0-2 0-3-1l-1-1c-1 0-1-1-2-1v-1-1h-2v1l-1-1 3-3c-2 0-4 1-6 1v-1-2c1-1 1 0 1-2h-1c-1 0-1 0-2-1l1-1z" class="Q"></path><path d="M555 857h3v1l-1 1 2 1h0c0 1-1 1-2 1v1h2 1l1-1h2 0l-2 2h1 2 0v1l-2 1h1c1 0 3 0 5-1v1l-3 2h0 3c0-1 1-1 1 0h0c-1 1-1 2-3 2-2-1-5 0-7 1l-1-1c1-1 3-1 3-2-2 0-4 1-6 0v-1h-2l3-3h-2l-1-1 3-1v-1h-2v-1l1-1v-1z" class="O"></path><path d="M474 859h0 2l1-1-2-1v-2c-1 0-1-1-2-1 2-2 4 0 5-1v-1l1-1c1 1 3 1 4 1v-1c-2 0-4-1-5-2l1-1 2 1h1c3 0-1 0 2 0-1 0-2-1-2-1-1-1-2-1-2-1 2-1 2 0 4-1-1-1-1-1-1-2h2 0c1 1 1 2 2 3l-1 2 2 2v1h-3c0 1 1 2 2 4-2 1-2 1-4 1 0-1-1-2-1-3h1v-1l-2 2c-2 0-1-1-3 0l2 2c0 1 0 1 2 2l1-2 3 3v3c-2 1-2 2-3 4v2c0 1 1 3-1 4-1-1-2-3-3-4-2 0-2 1-3 1-1 1-2 1-2 1l1 3h-4s0-1-1-1l-1 1v1l2 1-1 1c-1-1-1 0-2-1 1-2-2-3-3-5 1 0 2 1 2 1h1 3c-1-1-2-2-2-4 1 0 3 1 5 1v-1l-4-2 1-1 2 1h1v-1c-1-1-1-1-1-2s1-2 1-3h1l-1-1z" class="P"></path><path d="M475 860c1 0 2 1 4 2l1 1h0c-1 1-2 2-3 2 0 2 1 2 2 3h-2c-1-1-2-1-3-1h-1l1 1-4-2 1-1 2 1h1v-1c-1-1-1-1-1-2s1-2 1-3h1z" class="U"></path><path d="M448 804c1-1 1-2 2-3 2-3 4-5 7-8l1 1c0 2 0 3-2 6 1 1 1 1 1 2l-2 1 1 1h1v2 1h0c0 1 1 1 1 2l3-1c2 0 3 0 5-1l1 1v2l-1 2h3l-1 2 2 1-2 1v1c-1 1 0 1-1 2v2c-1 0-3-1-4-1l-1 1c-1 0-2-1-3-1s-2-1-2-1c-2-1-2-1-3-2v-1l-1 1h0l-2-2v-1c-1-2-2-2-1-4 0 1 1 1 2 2-1-2-2-3-4-5l-1-1v-1l1-1z" class="e"></path><path d="M448 804h2v-2c2 1 2 3 4 4l-1 1h0l-1-1-1 1 3 3v1c-1-1-2-1-3-2s0-1-1-2h-2l-1-1v-1l1-1z" class="h"></path><path d="M466 807l1 1v2l-1 2h3l-1 2c-1 2-1 2-3 2-1 0-2 0-3-1-2-1-3-1-4-3 1 0 1 0 1-1l-1-2 3-1c2 0 3 0 5-1z" class="P"></path><path d="M462 815l-2-4h2c1 1 2 1 3 1h1 3l-1 2c-1 2-1 2-3 2-1 0-2 0-3-1z" class="T"></path><path d="M581 824c1-1 1-1 3-1 2 1 4 2 7 2l1 1c-2 1-4 0-5 0h1c1 1 2 2 4 3v1h0c-2-1-3-1-5-1h0c1 1 1 2 3 2l1 1c-1 1-1 2-2 2l-3-1 1 2c1 1 2 1 3 2h-3c0 3 1 3 3 5-1 1-2 0-3 0h0v2c-1-1-2-1-4-1 1 1 0 1 1 2v1l-3-2-4-3c-1 0-2 1-2 2l-2-2-1-2-1-1-1-1c0-2-1-4-1-5h1l1-1-1-1c1 0 2-1 3 0v1c1 0 3 0 5-1 0 0 1-1 1-2 1-2 1-2 2-3v-1z" class="R"></path><path d="M590 831l1 1c-1 1-1 2-2 2l-3-1 1 2-1-1-2-2-4-1h4c1 1 3 1 5 1l1-1z" class="k"></path><path d="M573 838l1-1h-2v-1c1-1 2-1 3-1l2 1c2 1 5 1 8 1v1l-3 1c1 0 1 1 2 1h0c-2 2-5 0-7 1h0c-1 0-2 1-2 2l-2-2-1-2c0-1 0-1 1-1z" class="V"></path><path d="M572 839c0-1 0-1 1-1 1 1 3 2 4 3h0c-1 0-2 1-2 2l-2-2-1-2z" class="k"></path><path d="M536 787c3-1 5 0 8 0 1 1 1 1 1 2l-1 1 1 1v1h-2v1h2c0 1-1 2-1 2l1 1v1 2c0 1-1 0 0 2 1 3 0 6 1 9v2l-1 1c-1 0-2 1-3 2-3 1-5 3-8 4 0-1 0-1-1-2-1-2 1-1 1-3-1-1-1-1-1-2 0-2 1-2 2-3-1-1-1 0-2-1 0-3 0-3 3-5-1 0-2 1-3 0 1-1 1-2 1-3s0-1-1-2h3v-1h-2v-1l2-1c-1 0-2-1-3-1 1-1 3-1 4-1l-1-1v-1c1 0 1 0 2-1h-2l1-1h0c0-1 0-1-1-1v-1z" class="L"></path><path d="M545 801v7 1l-4 2c-2 1-3 1-5 1l-1-1h1l1-1-1-1h2l2-2h3c1 0 1-1 2-1l-1-1-6 1c2-2 4-5 7-5z" class="F"></path><path d="M541 811l1-2c1-2 1-1 3-1v1l-4 2z" class="H"></path><path d="M545 801c1 3 0 6 1 9v2l-1 1c-1 0-2 1-3 2-3 1-5 3-8 4 0-1 0-1-1-2-1-2 1-1 1-3-1-1-1-1-1-2 0-2 1-2 2-3h1l1 1-1 1h-1l1 1c2 0 3 0 5-1l4-2v-1-7z" class="G"></path><path d="M573 857c1 1 2 1 3 2 4 2 10 6 13 11 1 0 1 1 1 1 1 1 2 2 3 4h0c-2 1-3 1-4 1l-1 1h-1-1s-1 1-2 1h-2-1 0c-2-1-3-1-4 0h-4-1l7-2c-2-2-8 0-10 1l6-3-7 1v-1c1 0 1-1 2-1v-1c-2 1-3 1-5 1v-1h2c1 0 1-1 1-2-2 0-5 1-8 2 2-2 4-2 6-3 2 0 2-1 3-2h0c0-1-1-1-1 0h-3 0l3-2v-1c-2 1-4 1-5 1h-1l2-1h1c1-1 2-1 2-3-1 0-2 0-3-1 1 0 1-1 2-1v-1c1 0 1 1 1 2h0v1h2c1 0 2 0 3-1 0-1 0-1 1-2v-1z" class="B"></path><path d="M573 857c1 1 2 1 3 2h-1c-1 1-1 1-2 1s-1 1-1 1h-2v1 1c2 0 5-1 7-2h0l1 1h-2c-1 1-1 1-2 1v1l1 1c1 0 1-1 2-1l1 1c2-1 2-1 4 0v1c-1 1-1 1-2 1-1 1-2 1-3 2l-3 1v1l2-1 2-1v1c0 1-1 1-2 1-1 1-1 1-1 2 1 0 1 0 2-1 0 0 1 0 1 1h0l-1 1h3l1 1c-1 1-1 0-2 1-2-2-8 0-10 1l6-3-7 1v-1c1 0 1-1 2-1v-1c-2 1-3 1-5 1v-1h2c1 0 1-1 1-2-2 0-5 1-8 2 2-2 4-2 6-3 2 0 2-1 3-2h0c0-1-1-1-1 0h-3 0l3-2v-1c-2 1-4 1-5 1h-1l2-1h1c1-1 2-1 2-3-1 0-2 0-3-1 1 0 1-1 2-1v-1c1 0 1 1 1 2h0v1h2c1 0 2 0 3-1 0-1 0-1 1-2v-1z" class="C"></path><path d="M578 865c2-1 2-1 4 0v1c-2 0-5 1-6 1v-1l2-1z" class="i"></path><path d="M430 790h0 1c2-1 5-1 8-1v1h1v1 8c0 3-1 5 0 7v2c-1 2 0 4 0 5v10c1 2 1 7 1 9 0 7 0 16-1 23 1 1 0 2-1 4 0 0 0 1-1 2 1 1 1 1 2 1-1 1-2 1-3 2-2 0-3 2-5 3l-1 1c-1 0-2 1-3 2s0 1-1 1-2 1-3 2l-1 1-1-1-2 1 1-2h1c2-1 2-1 3-3 0 0 1-1 2-1v-2h2l2-2h0c0-2 1-2 2-3v-1-1h2c0-1 0-1 1-2v-2h1l-1-2c2-3 1-6 1-9 1-2 0-5 1-7 1-1 1-4 0-6h0l1-1-1-1c1-1 0-1 1-2h0c-1-1-1-1-1-2h1v-1c-1 0-2 0-4-1h3v-1l-4-1h0c2 0 3-1 5-1-1-1-4-1-5-1v-1h5c-2-1-3-1-4-2 1 0 2 0 3-1-1-1-2-1-4-1l1-1h2v-2c-1-1-1-1-2-1-1-2 0-3-1-5 0-1-1-2-1-3v-3h-1c1-1 1-1 0-2v-1c1 0 1 0 2-1-2 0-2 0-2-1v-1c-1-1-2 0-4-1h4c0-1 1-1 2-2h-4z" class="n"></path><path d="M440 823c1 2 1 7 1 9 0 7 0 16-1 23 1 1 0 2-1 4 0 0 0 1-1 2 1 1 1 1 2 1-1 1-2 1-3 2-2 0-3 2-5 3l-1 1c-1 0-2 1-3 2s0 1-1 1-2 1-3 2l-1 1-1-1-2 1 1-2h1c2-1 2-1 3-3 0 0 1-1 2-1v-2h2l2-2h0c1 0 2 0 2-1l2-1c0-1 1-2 2-2 1-1 1-2 1-3h1l-1-2h0c1-1 1-1 1-2 1-2 0-3 0-4l1-1-1-1c0-1 1-1 1-1l-1-1 1-2c0-3 1-9 0-12h0c1-2 0-6 0-8z" class="a"></path><path d="M550 829l3 9c1 1 1 2 1 3 1 1 1 2 2 3s1 3 2 4c2 3 3 5 7 6h1c2 2 5 2 7 3v1c-1 1-1 1-1 2-1 1-2 1-3 1h-2v-1h0c0-1 0-2-1-2v1c-1 0-1 1-2 1 1 1 2 1 3 1 0 2-1 2-2 3h-1v-1h0-2-1l2-2h0-2l-1 1h-1-2v-1c1 0 2 0 2-1h0l-2-1 1-1v-1h-3l-3 1-2-3 1-2v-1c-1 1-2 1-3 0l-2-1c1 0 2-1 3-2l-1-1-2 1-3-2 3-2h-3 0c0-1 1-1 2-2h0-4 0l3-3h-3c0-1 0-1 1-2l3-1c-1-2-3 0-5-1l1-1c1-1 3-1 3-2l-2-1h0l2-2h3c1-1 2-1 3-1z" class="I"></path><path d="M553 838c1 1 1 2 1 3 1 1 1 2 2 3s1 3 2 4c-2 1-2 1-3 1v-1c-1-1-1-1-1-2h-4v-3c1 0 1-1 1-1v-1c-1-1-2-1-3-1h0-1l2-2h4z" class="i"></path><path d="M550 829l3 9h-4c-1 0-1-1-2 0h0-2l-1 1-2-1 3-1c-1-2-3 0-5-1l1-1c1-1 3-1 3-2l-2-1h0l2-2h3c1-1 2-1 3-1z" class="J"></path><path d="M558 848c2 3 3 5 7 6h1c2 2 5 2 7 3v1c-1 1-1 1-1 2-1 1-2 1-3 1h-2v-1h0c0-1 0-2-1-2h-2c-1-1-1-2-2-2h-1c-1 0-1-1-2 0h0l-1-1c-1-1-1-1-3-1v-1c1 0 1 0 2-1l-1-1h-4v-2c1-1 2-1 3-1v1c1 0 1 0 3-1z" class="E"></path><path d="M459 856h3 2c1 1 1 1 1 0l4 2h2c1 1 2 1 3 1l1 1h-1c0 1-1 2-1 3s0 1 1 2v1h-1l-2-1-1 1 4 2v1c-2 0-4-1-5-1 0 2 1 3 2 4h-3-1s-1-1-2-1c1 2 4 3 3 5l-3-2h-1l1 3c0 1 0 1 1 2h-1c-1-1-2-2-4-2 0 1 0 2-1 3h-1l-1 1v1c-2 0-3-1-4-2h-1c-2-1-5-2-7-2 1 1 3 1 4 3h-2c-1-1-3-1-5-1l1 1h-5c-1 0-1-1-2-2l1-2c2-1 3-3 5-4s2-3 4-4 3-3 5-5l3-3c1-2 2-3 3-4s0-1 1-1z" class="a"></path><path d="M464 864c2 0 2 0 3 1h-3c0 1 0 2 1 3l-1 1-1-1h-2l1 2c-1 1-3 1-4 1 0 1 0 1 1 1 1 1 2 1 3 3-2 0-4 0-5-1h0v-2c-1 0-2 0-3-1h2v-1l-3-1h-1l1-1c2 1 4 0 6 0 0-1-3-1-5-2l2-1 4 1v-1l-3-1v-1c2 1 6 2 7 1z" class="G"></path><path d="M459 856h3 2c1 1 1 1 1 0l4 2c-1 1-1 2-2 3v1h-3v2c-1 1-5 0-7-1v1l3 1v1l-4-1-2 1-2-2 3-3c1-2 2-3 3-4s0-1 1-1z" class="H"></path><path d="M457 863h0l-1-1 1-2 1 2c1-1 1-1 2-1s3 0 4 1v2c-1 1-5 0-7-1z" class="S"></path><path d="M452 864l2 2c2 1 5 1 5 2-2 0-4 1-6 0l-1 1h1l3 1v1h-2c1 1 2 1 3 1v2h-3 0 0c2 1 4 3 5 4-2-1-4-1-6-1 0 1 1 1 2 2-1 1 0 1-1 1h-1c-2-1-5-2-7-2 1 1 3 1 4 3h-2c-1-1-3-1-5-1l1 1h-5c-1 0-1-1-2-2l1-2c2-1 3-3 5-4s2-3 4-4 3-3 5-5z" class="K"></path><path d="M599 827c1 0 2 1 2 2v1c1 0 1 1 2 1 1 1 0 0 1 0h1l-1-1h-1c1-1 1-1 1-2h2c1 0 2-1 4-1h0l1 1h1c2 1 4 1 7 2v2c0 2-1 5 0 7l-1 4c1 2 0 7 2 9l-3 3v1h-1c0 2-1 3-2 4s-3 1-4 2l-3-1c-1 0-3-1-4-2s-2-2-4-2v-1h0c1 0 1 0 2-1v-1h-2c0-2 1 0 2-2-1 0 0 0-1-1h-1c1-6 1-13 0-19v-5z" class="l"></path><path d="M604 828h2c1 0 2-1 4-1v3h-1c-2 1-3 1-4 3l-1-1-1 1-1 1-1-1 1-1h0l1-1c1 1 0 0 1 0h1l-1-1h-1c1-1 1-1 1-2z" class="U"></path><path d="M610 838v-3c1-1 0 0-1-2h1l1-2 6-1v2h-1l-2 1c0 1 0 1 1 2-2 1-3-1-4 2l-1 1z" class="H"></path><path d="M610 838l1-1c1-3 2-1 4-2-1-1-1-1-1-2l2-1h1c0 2 1 5 0 7h0-5-3l-1-1h2z" class="S"></path><path d="M612 839h5l1 12c-1 0-2-1-3-1h0l-1-1h1l1-1c-2-2-2-1-4-2v-1-2l1-2h0-2-2l3-2z" class="C"></path><path d="M611 841h2 0l-1 2v2 1c2 1 2 0 4 2l-1 1h-1l1 1h0c1 0 2 1 3 1l-1 4v1c-2 0-2 0-3 1h0c-2-1-1-2-4-3l1-1-1-1 1-2h-1l1-2c-1-1 0-3-1-5h0c1-1 1-1 1-2h0z" class="I"></path><path d="M599 827c1 0 2 1 2 2v1c1 0 1 1 2 1l-1 1h0l-1 1 1 1 1-1 1-1 1 1h0c-1 1-2 2-3 2v1h1l1 1h-1c0 1 0 2-1 3 0 1 0 1-1 2h2v1l-1 1c1 1 2 1 3 1-1 1-1 1-1 2s1 1 1 2h-2v1h1 2l-1 1v1h1v1h-2l1 1h1v2c1 1 2 1 2 2v1h1 1l4 1c-1 1-3 1-4 2l-3-1c-1 0-3-1-4-2s-2-2-4-2v-1h0c1 0 1 0 2-1v-1h-2c0-2 1 0 2-2-1 0 0 0-1-1h-1c1-6 1-13 0-19v-5z" class="m"></path><path d="M500 784v-1h0l1-2c4 1 10 0 15 0v1c0 2 0 3-1 5l1 1v-1h1 10l1 1h-1v1h1v7l1 1v3h0v1 6c-1 0-3 0-4-1h-4c-2-1-3-1-5-1-1 0-1 0-1-1l-1-1v-2h1c-1-1-1-2-1-3h-3c-2 0-3 1-5 1l-1 1h1 0l-2 3c-2 2-3 2-5 2-1 0-2 1-3 1l3-23 1 1z" class="R"></path><path d="M499 786h2c1-1 3-2 4-3 2 1 3 1 5 1v1h-2c-3 1-5 3-8 3l-1-1v-1z" class="U"></path><path d="M500 784v-1h0l1-2c4 1 10 0 15 0v1c0 2 0 3-1 5l1 1v1l-1 1h0v-2c-1-2-1 0-1-2v-3c-3-1-6 0-9 0-1 1-3 2-4 3h-2l1-2z" class="P"></path><path d="M515 804c0-2 0-4 1-6h3 2c3 1 5 1 8 2v1 6c-1 0-3 0-4-1h-4c-2-1-3-1-5-1-1 0-1 0-1-1z" class="N"></path><path d="M515 804c0-2 0-4 1-6h3 2c3 1 5 1 8 2v1c-2 0-12-1-14 1h1l4 1h1c-2 1-4 1-6 1z" class="C"></path><path d="M516 788v-1h1 10l1 1h-1v1h1v7l1 1-1 2c-1 0-4-1-5-1-3-1-5-1-7-1-1-2-1-6 0-8v-1z" class="G"></path><path d="M516 797v-2s1 0 2-1h1 2c2 1 4 2 6 1h1v1l1 1-1 2c-1 0-4-1-5-1-3-1-5-1-7-1z" class="C"></path><path d="M499 783l1 1-1 2v1 6 1 1c0 1-1 2 0 3 1 0 1 0 2-1h0l6-3-8 1c3-1 5-3 8-4l-8 1c4-2 8-4 13-5h2v7 3h-3v1c-2 0-3 1-5 1l-1 1h1 0l-2 3c-2 2-3 2-5 2-1 0-2 1-3 1l3-23z" class="M"></path><path d="M511 797h-1-5 0c1-1 2-1 3-1 2-2 3-2 6-2h0v3h-3z" class="I"></path><path d="M460 836c1 0 1 1 1 1 1-1 2-2 2-3 1-1 2-2 2-4 0-1 0-1 1-2l1 1c0 1 0 2 1 3h0c0 2-1 5-2 7 0 2 0 2-1 4v1l-2 4c0 1 0 0-1 1l-3 7c-1 0 0 0-1 1s-2 2-3 4l-3 3c-2 2-3 4-5 5s-2 3-4 4-3 3-5 4l-1 2c-1 2-2 3-3 4v-1-2h-1c-2 0-3 0-5-1h0c-1 2 0 1 0 3h-3c0 1-1 2-1 3-1 1-2 2-2 3l-1 2-1 1c0 2-2 3-1 6h2c-1 1-2 1-2 0-1 2-1 3-1 5-2 1-5 1-7 1h0v-6h0c0-2 1-3 2-5 0-3 1-4 2-7l1-2 7-9 1-1c1-1 2-2 3-2s0 0 1-1 2-2 3-2l1-1c2-1 3-3 5-3 1-1 2-1 3-2l2-1c1-2 3-3 5-5 5-6 9-12 13-20z" class="O"></path><path d="M432 874s0 1-1 1l-1 2 1 1h1c2-2 3-3 5-4v1 1h1l4-4 1 1c-2 1-3 3-5 4l-1 2c-1 2-2 3-3 4v-1-2h-1c-2 0-3 0-5-1h0c-1 2 0 1 0 3h-3c1-1 1-3 3-4h0l4-4z" class="G"></path><path d="M442 861h1c1 0 1-1 2-1v1l-2 2h0c1 1 0 1 0 1v1l-1-1-1 1-1 1v1 1h-1c-1 0-2 1-3 2v1c2 0 3-2 4-1-1 1-2 2-4 2h-1c-1 0-2 1-3 2l-4 4h0c-2 1-2 3-3 4 0 1-1 2-1 3-1 1-2 2-2 3l-1 2-1 1c0 2-2 3-1 6h2c-1 1-2 1-2 0-1 2-1 3-1 5-2 1-5 1-7 1h0v-6h0c0-2 1-3 2-5 0-3 1-4 2-7l1-2 7-9 1-1c1-1 2-2 3-2s0 0 1-1 2-2 3-2l1-1c2-1 3-3 5-3 1-1 2-1 3-2l2-1z" class="R"></path><path d="M431 868c2 0 3 1 4 1-2 1-3 3-5 5l-3-3c1 0 0 0 1-1s2-2 3-2z" class="L"></path><path d="M411 897h3 0l-1-1 1-1h3c-1 1-2 2-2 3s1 2 1 2c0 1-4 2-4 2l-1 1v-6h0z" class="Z"></path><path d="M442 861h1c1 0 1-1 2-1v1l-2 2h0c1 1 0 1 0 1v1l-1-1-1 1v-2c-2 2-4 3-5 5 0 1 0 1-1 1s-2-1-4-1l1-1c2-1 3-3 5-3 1-1 2-1 3-2l2-1z" class="G"></path><path d="M415 885c1 1 2 2 2 4v1l1 1-1 3v1h0-3l-1 1 1 1h0-3c0-2 1-3 2-5 0-3 1-4 2-7z" class="L"></path><path d="M424 873c1-1 2-2 3-2l3 3-1 1c-2 1-2 2-3 3-1 2-3 4-4 6s-2 5-4 7l-1-1v-1c0-2-1-3-2-4l1-2 7-9 1-1z" class="M"></path><path d="M424 873c1-1 2-2 3-2l3 3-1 1c-2-1-4-1-5-2z" class="G"></path><path d="M485 797h0v-6l2-1-1-1v-1c1-1 3-1 4-1h1c-1 1-1 1-3 1l1 1h0 3v1h-2c1 1 2 1 3 1l1 2v1 1 1h-2c1 2 2 1 2 2-1 2-1 3 0 5-1 2-2 1-2 4 1 1 0 4 0 5l-1 1v1c-1 0-1-1-1 0l1 2v1h-2v1l1 2h0v2c-1 0-2 0-2-1-1 1-1 1-1 2h0v2h-2c0 1 0 1 1 2l1 1v1l-2-1v2c0 1-1 2-1 2 1 2 1 2 1 4-1 1-1 1-2 0-1 0-1 0-3 1l2 2h0-2l-1 1c1 0 1 0 2 1 2 0 3 2 4 3h0-2c0 1 0 1 1 2-2 1-2 0-4 1 0 0 1 0 2 1 0 0 1 1 2 1-3 0 1 0-2 0h-1l-2-1-1 1c1 1 3 2 5 2v1c-1 0-3 0-4-1l-1 1v1c-1 1-3-1-5 1 1 0 1 1 2 1v2l2 1-1 1h-2 0c-1 0-2 0-3-1h-2l-4-2c0 1 0 1-1 0h-2-3l3-7c1-1 1 0 1-1l2-4v-1c1-2 1-2 1-4 3-5 5-12 7-17 0-1 1-3 1-4v-2l1-1v-3h-1c1-1 1-3 1-4v-2c2-1 3-1 5-1h0c1-1 1-1 2-1v-4-2c1 0 2 0 3-1z" class="U"></path><path d="M480 837l-1-1c0-2 1-1 2-2v-2c0-1 0-1-1-2h0v-1l1 1 1-1-1-1c1-1 1 0 3 0l-1-2h0l2 1h1l1 1v1l-2-1v2c0 1-1 2-1 2 1 2 1 2 1 4-1 1-1 1-2 0-1 0-1 0-3 1zm5-40v8h0v1c-1 2 0 3 0 5v2c-1 0-2 0-2-1-1-2-1-3-1-5l-1-1-1-1h0c1-1 1-1 2-1v-4-2c1 0 2 0 3-1z" class="h"></path><path d="M475 806c2-1 3-1 5-1l1 1c0 1-1 0-2 2h2v1h-2c1 1 1 1 2 1v1h-2l1 1h1l1 1h-5v1h1c3 0 4 0 6 2v1h0c-2-1-3-2-5-1h1c2 1 5 2 6 5h-1c-1-1-1-1-2-1 0 1 1 1 2 2l-1 1-1-1c-1-1-2-1-4-2 1 1 3 2 4 4h-1c-1 0-2-1-3-1l2 2-1 1h-1l1 2h-2v1l1 1c-1 0-1 1-2 1 0 1 0 1 1 2h0-2l-1-1c-1 0-1 0-2-1l3-1v-1c-1 0-2 0-3-1h3v-1l-2-1h2l1-1c-1 0-2 0-2-1l2-1h0c-2-1-3-1-4-1 0-1 1-3 1-4v-2l1-1v-3h-1c1-1 1-3 1-4v-2z" class="b"></path><path d="M475 806c2-1 3-1 5-1l1 1c0 1-1 0-2 2-1-1-2-1-4 0v-2z" class="n"></path><path d="M473 822c1 0 2 0 4 1h0l-2 1c0 1 1 1 2 1l-1 1h-2l2 1v1h-3c1 1 2 1 3 1v1l-3 1c1 1 1 1 2 1l1 1h-3c1 1 2 1 3 1v1h-3v1l2 1h0l-2 1c1 1 3 1 2 2h-2c1 2 2 1 2 3h-2c0 1 0 1 1 2h-1v1 2c-1 1-1 2-2 3h-1c-1 1-1 1-2 1 1 2 1 1 2 3h-2c-1 1-1 1-3 1 0 1 0 1-1 0h-2-3l3-7c1-1 1 0 1-1l2-4v-1c1-2 1-2 1-4 3-5 5-12 7-17z" class="K"></path><path d="M444 881l-1-1c2 0 4 0 5 1h2c-1-2-3-2-4-3 2 0 5 1 7 2h1c1 1 2 2 4 2v-1l3 3v1l-2-1c-1 1 1 2 0 4h-2c-1 1-2 1-2 2h-1v2h0 2v1h-1c-3 2-4 6-7 8 0 1 0 1-1 2l-2 2c-1 1-1 2-1 4l-3 3 1 1h1l-1 1v2c1 1 2 1 3 1-1 0-2 1-2 2l1 1v1h0l1 1v1l-4 1h-4-4c-1-1-3-3-3-4l-1-4c-1-1-1-4-2-5l-1-1h0v-4h-8v-1-1-1-1c0-2 0-3 1-5 0 1 1 1 2 0h-2c-1-3 1-4 1-6l1-1 1-2c0-1 1-2 2-3 0-1 1-2 1-3h3c0-2-1-1 0-3h0c2 1 3 1 5 1h1v2 1c1-1 2-2 3-4 1 1 1 2 2 2h5z" class="Z"></path><path d="M437 879c1 1 1 2 2 2h5l4 1v1h-1c-3 0-6-1-8 0h-2c2 1 3 0 5 1l-1 1-3 1c-2-1-3-1-4-2v-1c1-1 2-2 3-4z" class="E"></path><path d="M443 919l-2 2c-1 0-2 0-3 1h-3c1-1 0-1 1-2-1 0-2 0-3-1 1 0 2 0 3-1h0c-1 0-2 0-3-1l2-1c-1-1-1-1 0-2h2c2 1 3 0 5 0v2c1 1 2 1 3 1-1 0-2 1-2 2z" class="P"></path><path d="M453 880h1c1 1 2 2 4 2v-1l3 3v1l-2-1c-1 1 1 2 0 4h-2c-1 1-2 1-2 2h-1v2h0 2v1h-1c-3 2-4 6-7 8 0 1 0 1-1 2l-2 2c-1 1-1 2-1 4l-3 3 1 1h1l-1 1c-2 0-3 1-5 0l1-1c-1-1-2-1-2-2 1 0 2 0 3-1v-2l1-1-1-1c-1 0-2 0-3-1v-1h2v-2c1-1 3-1 5-1 1-2 1-1 2-3l-2-1c1 0 3 0 4-1-1 0-1 0-1-1l3-1h0c-1-1-1-1-2-1h-1v-1h4c-1-1-2-1-3-2 1-1 2 0 4 0-1-1-2-1-2-2-1 0-2 0-2-1h4 0l-1-2h-1 4c1-1-1-2-2-3 1 1 2 0 3 0h0l-1-1v-1z" class="k"></path><path d="M425 882h3c0-2-1-1 0-3h0c2 1 3 1 5 1h1v2 1 1c1 1 2 1 4 2h0 1c2 0 3 0 5 1v1h-2-1l2 1 1 1c-1 1-2 1-3 1h0l2 2c-1 1-2 1-4 1 1 0 1 1 1 2-1 0-1 1-2 1s-2 1-3 2-2 2-4 2v1c1 1 1 1 2 1v1h-2c1 1 2 1 3 2-2 1-5 1-6 1v-6h-1-1v4 1h-8v-1-1-1-1c0-2 0-3 1-5 0 1 1 1 2 0h-2c-1-3 1-4 1-6l1-1 1-2c0-1 1-2 2-3 0-1 1-2 1-3z" class="O"></path><path d="M431 889l3 3h2v1c-2 0-3 1-5-1l-1-1 1-2zm-1 4c1 0 4 1 5 1l1 1-2 1s0 1-1 1-3 0-4-1c0-1 0-2 1-3z" class="C"></path><path d="M434 884c1 1 2 1 4 2h0 1c2 0 3 0 5 1v1h-2-1l2 1c-2 2-5 1-7 1-1 1-2 1-2 2l-3-3 1-2v-1l2-2z" class="H"></path><path d="M432 887c1 1 2 2 4 3-1 1-2 1-2 2l-3-3 1-2z" class="I"></path><path d="M421 897h1l1 1-1 2h2l1 1h1v4 1h-8v-1-1-1-1c0-2 0-3 1-5 0 1 1 1 2 0z" class="G"></path><path d="M418 905h8v1h-8v-1z" class="F"></path><path d="M424 788h1c0 1-1 2-1 3h6v-1h4c-1 1-2 1-2 2h-4c2 1 3 0 4 1v1c0 1 0 1 2 1-1 1-1 1-2 1v1c1 1 1 1 0 2h1v3c0 1 1 2 1 3 1 2 0 3 1 5 1 0 1 0 2 1v2h-2l-1 1c2 0 3 0 4 1-1 1-2 1-3 1 1 1 2 1 4 2h-5v1c1 0 4 0 5 1-2 0-3 1-5 1h0l4 1v1h-3c2 1 3 1 4 1v1h-1c0 1 0 1 1 2h0c-1 1 0 1-1 2l1 1-1 1h0c1 2 1 5 0 6-1 2 0 5-1 7 0 3 1 6-1 9l1 2h-1v2c-1 1-1 1-1 2h-2v1 1c-1 1-2 1-2 3h0c-1 0-2 0-2 1h-3v2h-1c-1-1-1 0-1 0h-1c-2 1-4 1-5 2h-2l-1-1c1 0 3-2 3-3-1-1-1-1-1-3 1 0 1-1 2-1l1-1v-2c1-1 0-1 0-1 0-1 2-3 2-4v-1l1-2c-2-1-2-2-4-4 0 1 0 1-1 0h0c1-1 1-2 1-3v-1-1c1-2 0-1-1-2s0-3-1-4c1-3 0-6 0-9 1-2 1-3 0-5 0-3 0-8 1-11 1-2 0-4 1-6 0-2 1-3 1-5v-4h0v-1c0-1 0-2 1-3h0l3-3z" class="J"></path><path d="M430 790h4c-1 1-2 1-2 2h-4c2 1 3 0 4 1v1c0 1 0 1 2 1-1 1-1 1-2 1v1c1 1 1 1 0 2h1v3c0 1 1 2 1 3 1 2 0 3 1 5 1 0 1 0 2 1v2h-2l-1 1c2 0 3 0 4 1-1 1-2 1-3 1h-2l-1-1-1-1c0-1 1-1 1-2h0c-1-1-1-2-1-2l-2-1v-1h0c0-2 0-3-1-4h-2c0 2-1 2 0 4l-1 1c-1 1-1 2-1 3l1 1c0 1 0 1-1 2v7h0l1 2h0c-1 2 0 6-1 8l-1 3-1-2h1c0-2 0-6-1-8-1-1-1-1-1-2 2-4 2-11 3-15l-1-2v-2h2c-1-2-1-7-1-9v-4h6v-1z" class="C"></path><path d="M435 813c0-1 0 0-1-1v-1h0c-1-2-1-2-1-4-1-1-1-2-2-3h-3v-1c1 0 2 0 2-1-1 0-1 0-1-1 1 0 0 0 1-1h-2v-1l3-1v-1h-2v-1h2c-1-2-1-1-3-2h4c0 1 0 1 2 1-1 1-1 1-2 1v1c1 1 1 1 0 2h1v3c0 1 1 2 1 3 1 2 0 3 1 5 1 0 1 0 2 1v2h-2z" class="K"></path><path d="M424 788h1c0 1-1 2-1 3v4c0 2 0 7 1 9h-2v2l1 2c-1 4-1 11-3 15 0 1 0 1 1 2 1 2 1 6 1 8h-1c0 3 0 5-1 8l-1 1 2 2h-3l1 1h1c0 1-1 1-2 1 0 1 0 1-1 0h0c1-1 1-2 1-3v-1-1c1-2 0-1-1-2s0-3-1-4c1-3 0-6 0-9 1-2 1-3 0-5 0-3 0-8 1-11 1-2 0-4 1-6 0-2 1-3 1-5v-4h0v-1c0-1 0-2 1-3h0l3-3z" class="Z"></path><path d="M424 788h1c0 1-1 2-1 3v4c0 1-1 1-1 2-1 1-1 5-2 7 0 1-1 2-1 4 0 1 0 1-1 3s-1 7-2 10c0-3 0-8 1-11 1-2 0-4 1-6 0-2 1-3 1-5v-4h0v-1c0-1 0-2 1-3h0l3-3z" class="X"></path><path d="M424 788v6c-1 1-2 1-3 1h-1 0v-1c0-1 0-2 1-3h0l3-3z" class="V"></path><path d="M421 841v-2l-1-1c1-1 1-1 1-2h-1v-1h1c-1-1-1-2 0-3-1 0-1-1-2-1v-3h2v-1h-1v-2c-1-1 0-3 0-4 1-1 0-3 0-5 1-1 1-1 1-2 1-1 0-3 0-4l1-1c0-1 0-2-1-2v-1l2-2v2l1 2c-1 4-1 11-3 15 0 1 0 1 1 2 1 2 1 6 1 8h-1c0 3 0 5-1 8z" class="M"></path><path d="M433 838c0-2-1-5 0-7v-5-1h2v-1h-2l1-1h1c2 1 3 1 4 1v1h-1c0 1 0 1 1 2h0c-1 1 0 1-1 2l1 1-1 1h0c1 2 1 5 0 6-1 2 0 5-1 7 0 3 1 6-1 9l1 2h-1v2c-1 1-1 1-1 2h-2v1 1c-1 1-2 1-2 3h0c-1 0-2 0-2 1h-3v2h-1c-1-1-1 0-1 0h-1c-2 1-4 1-5 2h-2l-1-1c1 0 3-2 3-3-1-1-1-1-1-3 1 0 1-1 2-1l1-1v-2c1-1 0-1 0-1 0-1 2-3 2-4v-1l1-2c-2-1-2-2-4-4 1 0 2 0 2-1h-1l-1-1h3l-2-2 1-1c1-3 1-5 1-8l1 2v5l1 1-1 1h0l1 7c1 0 1 0 2 1 1-1 3-1 4-1 0-1 1-3 1-4l1-1v-4h-1l2-2z" class="F"></path><path d="M426 850c1-1 3-1 4-1 0-1 1-3 1-4l1-1v-4h-1l2-2c0 3 0 6-1 9v3c0 1 1 1 1 2 0 0-1 3-1 4-1 1-1 2-1 3h-2v2h-2v1 2c-2 1-4 1-7 1l-1-1c0-1 1-2 2-3 0-1 1-1 1-3l2-2c0-1 1-2 0-3v-2l2-1h0z" class="E"></path><path d="M424 856c0-1 1-2 0-3v-2l2-1c1 1 4 1 4 2-1 2-2 6-4 7-1 1-1 1-2 1l-1-1c1 0 1-2 1-3z" class="D"></path><path d="M625 783c2 0 3 0 5 1v16l2 1v6h1l1-3 1 1c1-2 2-2 3-2 0 2 1 4 0 5 1 2 1 3 1 5v3 3 3h1l-1 2c-2-1-5 0-7 0h-14-2c-2 0-5 0-8 1l1 1h1 2c0 1-1 1-2 1h0 0c-2 0-3 1-4 1h-2c0 1 0 1-1 2h1l1 1h-1c-1 0 0 1-1 0-1 0-1-1-2-1v-1c0-1-1-2-2-2l1-7h-1-1l-1-4c0-2 1-3 0-6v-3h1v-1-2c0-1-1-2 0-3l1 1h1c-1-3-1-7 0-9h0v-2h3c3 0 5 0 8-1-3 0-6-1-10-1l9-2-6-1 9-2h1c3-1 4-1 7 0 2 0 3 0 4-1z" class="P"></path><path d="M611 790c1-1 2-1 4 0l1 1c0 1-1 1-2 2h-1c0 1-1 1-2 1 1 0 1 0 3 1 0 1 0 1-1 2h0c1 0 2 0 2 1-1 1-3 0-4 0h0l-9-2 10-1-9-1c2-1 4-1 6-1v-1c-1 0-4 0-5-1h-1c3 0 5 0 8-1z" class="N"></path><path d="M600 820v-2-4-6l1 2c1 1 2 3 3 4l2-1 2 2v4 2 4l1 1h1 2c0 1-1 1-2 1h0 0c-2 0-3 1-4 1h-2c0 1 0 1-1 2h1l1 1h-1c-1 0 0 1-1 0-1 0-1-1-2-1v-1c0-1-1-2-2-2l1-7z" class="R"></path><path d="M606 813l2 2v4 2 4l1 1h1 2c0 1-1 1-2 1h0 0c-2 0-3 1-4 1h-2c0-1 0-1 1-1l1-1c0-1 0-2-1-3 0-1 0-1 1-2-1-1-2-1-3-2 2 0 2-1 2-1 0-1-1-2-1-3v-1l2-1z" class="e"></path><path d="M606 800h4v1h-4c1 1 4 2 6 2 1-1 1-1 2 0h1 3c2 0 2 0 4 1 1-1 1-1 2 0 1 0 2 1 3 2l1 1h1c-2 1-4 1-6 2v-1h-4v1 3s-1 0-2 1c-1 0-1-1-2 0h-4l-1 1 2 2 1 1v1 1h1c0 1 0 1-1 1l1 1-1 2c1 0 1 0 2 1l1-1v1c-2 0-5 0-8 1v-4-2-4l-2-2v-1l-1-1c1-1 2-1 3-1v-1c-2 0-3 0-5-1l-2-3 1-1h1l-2-2h3c-1 0-1-1-2-1v-1h2 2 0z" class="V"></path><path d="M612 816l1 1v1 1h1c0 1 0 1-1 1 0 1-1 2-1 2h-1v-4h-2l3-2z" class="F"></path><path d="M612 803c1-1 1-1 2 0h1 3c2 0 2 0 4 1 1-1 1-1 2 0 1 0 2 1 3 2l1 1h1c-2 1-4 1-6 2v-1h-4c-5 0-10 0-14-2h0 1c2 0 2 0 3-1l-5-2c2 0 6 1 8 0h0z" class="J"></path><path d="M618 803c2 0 2 0 4 1 1-1 1-1 2 0 1 0 2 1 3 2l1 1h1c-2 1-4 1-6 2v-1c-1 0-1 0-2-1-2-2-7 1-10-2 2-1 4-1 6-1l1-1z" class="N"></path><path d="M625 783c2 0 3 0 5 1v16c0 2 0 6-1 7h-1l-1-1c-1-1-2-2-3-2-1-1-1-1-2 0-2-1-2-1-4-1h-3-1c-1-1-1-1-2 0-2 0-5-1-6-2h4v-1h-4l-3-1v-1h8 0c1 0 3 1 4 0 0-1-1-1-2-1h0c1-1 1-1 1-2-2-1-2-1-3-1 1 0 2 0 2-1h1c1-1 2-1 2-2l-1-1c-2-1-3-1-4 0-3 0-6-1-10-1l9-2-6-1 9-2h1c3-1 4-1 7 0 2 0 3 0 4-1z" class="W"></path><path d="M630 800l2 1v6h1l1-3 1 1c1-2 2-2 3-2 0 2 1 4 0 5 1 2 1 3 1 5v3 3 3h1l-1 2c-2-1-5 0-7 0h-14-2v-1l-1 1c-1-1-1-1-2-1l1-2-1-1c1 0 1 0 1-1h-1v-1-1l-1-1-2-2 1-1h4c1-1 1 0 2 0 1-1 2-1 2-1v-3-1h4v1c2-1 4-1 6-2 1-1 1-5 1-7z" class="O"></path><path d="M634 813h1c1 0 2 0 3-1l1 1v3 3 3h1l-1 2c-2-1-5 0-7 0h-14l1-1h1 8 5l1-1h-1c-2-1-7-1-9 0h-1-1c-2 0-2 0-4-1l1-1h1c2 1 7 1 8 0l2 1 1-1h1l1-1h0v-1c-1-1-1-3-1-4l1-1h1z" class="b"></path><path d="M634 813h1c1 0 2 0 3-1l1 1v3h-1c-1 0-1 1-2 2h0l-1 1v-3h1c-1-1-1-2-2-3z" class="I"></path><path d="M630 800l2 1v6h1l1-3 1 1c1-2 2-2 3-2 0 2 1 4 0 5 1 2 1 3 1 5l-1-1c-1 1-2 1-3 1h-1-1-2-4c-2 0-4-1-6 1l-1-1h-3c1-1 2-1 2-1v-3-1h4v1c2-1 4-1 6-2 1-1 1-5 1-7z" class="p"></path><path d="M635 805c1-2 2-2 3-2 0 2 1 4 0 5s-1 1-2 1l1 2h-2v-2l1-1c-1-1-1-2-1-3z" class="K"></path><path d="M630 800l2 1v6h0v2l1 2c-1 1-2 1-3 1l-1-2-2 1c-1 0-2 0-3 1-1-1-1-2-1-3 2-1 4-1 6-2 1-1 1-5 1-7z" class="b"></path><path d="M549 814l1-1h2s0 1 1 1h2c0 2 0 3 1 5 0 1 1 2 2 3 0 1 2 2 2 2v3l1 2 1 3c1 1 1 2 1 3v1c0 1 1 2 1 3l1 1 1 3 2 2c0 1 1 2 2 3 0 1 1 2 2 3l1-1c1 0 2 1 3 2 1 0 1 0 2 1 1 0 1 0 2 1l1 1 5 2c4 2 9 4 13 7l1-1-2-2 6 3 6 4c1 0 2-1 3 0 1 0 3 1 3 2l1 3h3 1l-1 1v2l-1 1h0c2 3 3 6 5 8v1h0c1 2 2 3 2 5l1 3 1 2v1c1 3 0 12-1 14l-1 1h0v2c-1 1-1 2-1 3-1 0-2 1-3 1-1 1 0 1-1 1l-1-1-2 2v-1-2c1 0 1-1 1-2h1v-3c0-1 1-2 1-3l-2-1v-2h-5-1l-3 1v2l-1 1c-1 0-1 0-1-1l-2-1c-1 0-1 0-2-1h-1v-2l-2 1v-2-3l-1-1 1-1-1-1v-2-2c0-1-1-2-1-2l-2-8-3-6-1-3h0c-1-2-2-3-3-4 0 0 0-1-1-1-3-5-9-9-13-11-1-1-2-1-3-2-2-1-5-1-7-3h-1c-4-1-5-3-7-6-1-1-1-3-2-4s-1-2-2-3c0-1 0-2-1-3l-3-9-1-3c0-1 0-2-1-3h0v-3c-1-1-1-1 0-2v-4h1z" class="l"></path><path d="M604 907h6v2l-1 1c-1 0-1 0-1-1l-2-1c-1 0-1 0-2-1z" class="C"></path><path d="M615 891h1l1 1c1 3 1 6 2 9v3c-1 1-2 0-4 1l-10-1h-1c-1-1-1-3-1-4h2v-1h-2v-1l1-1h1 0c-1-1-1-1-2-1l1-1h2 1l1-1h3c1-1 2-1 4-2v-1z" class="B"></path><path d="M605 904c2-1 3 0 5 0h2v-3h2c1 0 1 1 2 1l2-1h1v3c-1 1-2 0-4 1l-10-1z" class="E"></path><path d="M594 875l1-1c3-1 4-1 7-1v1c2 0 2 0 4 2v1h1l1 1c1-1 1-1 2-1l2 1c1 2 2 5 3 7 0 1 0 1 1 2 0 2 1 3 1 5l-1-1h-1v1c-2 1-3 1-4 2h-3l-1 1h-1-2l-1 1c1 0 1 0 2 1h0-1l-1 1c-1-2-1-4-2-6v-3c-1-1-1-3-2-4v-1c-1-2-2-3-2-5l-1-2-2-2z" class="D"></path><path d="M599 879h7c1 0 2 1 3 1h2v1 1h-3l-1-1-1 2v-2c-2-1-3 0-5 0l-2-2z" class="J"></path><path d="M610 877l2 1-6 1h-7-2l-1-2c4 1 8 0 12 1 1-1 1-1 2-1z" class="K"></path><path d="M601 881c2 0 3-1 5 0v2h0-2v1h5 1c-1 1-2 1-2 2h1c-2 1-4 1-6 1-1-1-1-2-1-3l-1-1h-1c0-1 0-2 1-2z" class="H"></path><path d="M594 875l1-1c3-1 4-1 7-1v1c2 0 2 0 4 2v1h1l1 1c-4-1-8 0-12-1l-2-2z" class="B"></path><path d="M597 879h2l2 2c-1 0-1 1-1 2h1l1 1c0 1 0 2 1 3 2 0 4 0 6-1l1 1h0 0l-4 2h-1v-1h-1c-1 1-1 1-1 2h0c-1 1-1 1-1 2h-1v-3c-1-1-1-3-2-4v-1c-1-2-2-3-2-5z" class="C"></path><path d="M601 892h1c0-1 0-1 1-2h0c0-1 0-1 1-2h1v1h1l4-2c0 1 0 2 1 3l-1 1 1 1 4-1v1c-2 1-3 1-4 2h-3l-1 1h-1-2l-1 1c1 0 1 0 2 1h0-1l-1 1c-1-2-1-4-2-6z" class="E"></path><path d="M549 814l1-1h2s0 1 1 1h2c0 2 0 3 1 5 0 1 1 2 2 3 0 1 2 2 2 2v3l1 2 1 3c1 1 1 2 1 3v1c0 1 1 2 1 3l1 1 1 3 2 2c0 1 1 2 2 3 0 1 1 2 2 3s3 2 4 2l1 1-1 1 1 1 2-1h2v2c1 0 2 0 3 1h1c6 3 12 6 18 10h0l-1 2-1 1c0 1 1 1 1 2-3 0-4 0-7 1l-1 1v-2c-1-1-2-2-2-3v-1c-1 0-2-1-3-1v-1c-1 0-1-1-1-1 0-1 1-1 1-2h-1l-1 1c-3-2-4-4-7-5-2-2-5-3-7-5-2 0-4 0-5-2h-3c-1 0-3-2-4-3l1-1-1-1v1l-2-1c0-1 0-1-1-2-1-2-2-3-2-5 0-1-1-1-2-2 1-2 0-2 0-4h1 0l-2-1c-1-1-1-3-2-5l1-1-1-1c0-1-1-3-1-4s0-2-1-4v-3-1-1z" class="B"></path><path d="M581 857c1 0 2 0 3 1 0 0 0 1 1 1v1h-1-1c-1-1-1-2-2-3z" class="D"></path><path d="M561 848l1-1c1-1 1-1 2 0h1c1 1 2 3 3 3h1v1h0c-1 1-1 2-1 2h-3c-1 0-3-2-4-3l1-1-1-1z" class="E"></path><path d="M589 867l2-1 1 1c1-1 1-1 2 0h0l-1 1c2 0 3 0 5 1 2 0 2 0 5-1h0l-1 2-1 1c0 1 1 1 1 2-3 0-4 0-7 1l-1 1v-2c-1-1-2-2-2-3v-1c-1 0-2-1-3-1v-1z" class="H"></path><path d="M549 814l1-1h2s0 1 1 1h2c0 2 0 3 1 5 0 1 1 2 2 3 0 1 2 2 2 2v3l1 2 1 3c1 1 1 2 1 3v1c-1-1-2-3-2-5-1 0-1-1-1-1h-2c-2-2-2-2-4-2-1 0-1 0-1 1 1 0 2 0 3 1l-1 1 1 1h0c1-1 3-1 4 0h-1c-2 1-4 1-5 1l-1 1c-1-1-1-3-2-5l1-1-1-1c0-1-1-3-1-4s0-2-1-4v-3-1-1z" class="K"></path><path d="M549 814l1-1h2s0 1 1 1h2c0 2 0 3 1 5h-1-1l1-2h-1c-1 1-2 0-3 1-1 0-2 1-2 1v-3-1-1z" class="C"></path><path d="M572 851l1-1c1 0 2 1 3 2 1 0 1 0 2 1 1 0 1 0 2 1l1 1 5 2c4 2 9 4 13 7l1-1-2-2 6 3 6 4c1 0 2-1 3 0 1 0 3 1 3 2l1 3h3 1l-1 1v2l-1 1h0c2 3 3 6 5 8v1h0c1 2 2 3 2 5l1 3 1 2v1c1 3 0 12-1 14l-1 1h0v2c-1 1-1 2-1 3-1 0-2 1-3 1-1 1 0 1-1 1l-1-1-2 2v-1-2c1 0 1-1 1-2h1v-3c0-1 1-2 1-3l-2-1v-2h-5l1-1c2-1 3 0 4-1v-3c-1-3-1-6-2-9 0-2-1-3-1-5-1-1-1-1-1-2-1-2-2-5-3-7l-2-1c-1 0-1 0-2 1l-1-1h-1v-1c-2-2-2-2-4-2v-1c0-1-1-1-1-2l1-1 1-2h0c-6-4-12-7-18-10h-1c-1-1-2-1-3-1v-2h-2l-2 1-1-1 1-1-1-1c-1 0-3-1-4-2z" class="G"></path><path d="M603 868c3 2 5 5 7 8v1c-1 0-1 0-2 1l-1-1h-1v-1c-2-2-2-2-4-2v-1c0-1-1-1-1-2l1-1 1-2z" class="N"></path><path d="M603 868c3 2 5 5 7 8h-3c-1-1-1-1-2-3h0c-1-2-1-3-3-3l1-2z" class="B"></path><path d="M620 895l1-1c2-1 4 0 6 0l1 2v1l-2 3 1 1v1h-4v1h2l-1 1c-1-1-1 0-2 0h-1v-3-1c-1-2-1 0-1-2v-3h0z" class="E"></path><path d="M616 883c2 0 3 0 5 1h0c1 1 1 2 2 2h1c1 2 2 3 2 5l1 3c-2 0-4-1-6 0l-1 1c-1-2-1-3 0-4h1 0v-1h-2l-1-1 1-1h0 1l-1-1h-2v-2s-1-1-1-2z" class="C"></path><path d="M616 883c2 0 3 0 5 1h0c1 1 1 2 2 2h1c1 2 2 3 2 5h0-1c-1-1-1-1-1-2l-1-2c-1 0-2-1-4-1h-1l-1-1s-1-1-1-2z" class="I"></path><path d="M598 861l6 3 6 4c1 0 2-1 3 0 1 0 3 1 3 2l1 3h3 1l-1 1v2l-1 1h0c2 3 3 6 5 8v1h0-1c-1 0-1-1-2-2h0c-2-1-3-1-5-1-1-1-1-3-3-4h0v-2c-2-1-2-2-4-4-2-3-5-5-8-7l-2-2 1-1-2-2z" class="F"></path><path d="M610 868c1 0 2-1 3 0 1 0 3 1 3 2l1 3h3 1l-1 1v2l-1 1c-1-1-2-3-3-4-2-1-4-3-6-5z" class="a"></path><defs><linearGradient id="Q" x1="401.324" y1="808.295" x2="394.853" y2="809.91" xlink:href="#B"><stop offset="0" stop-color="#6f7070"></stop><stop offset="1" stop-color="#868382"></stop></linearGradient></defs><path fill="url(#Q)" d="M398 787h1v2c1 1 1 2 2 2h0c-1 1-2 1-2 3h0l-1 2h4 1c1 1 0 2 3 2h1c0 1 1 1 1 2-1 1-2 1-1 3v2l-1 1c-1 0-3 1-4 1h-1l1 1c1 1 2 1 3 1h0c-1 1-2 1-3 1l-2 1h0v1h2l-2 2h3l-1 1h1v2l-1 1v1l-1 2c1 0 1 1 2 1l1 2h1v2h0c1 1 2 1 3 3h0l-3 2h2 0l1-1c2 0 3 1 5 2h0l-3-3 2-1 1 2c1 0 1 0 2-1v3c2-1 0-2 1-4 0 0 1-1 1-2 0 3 1 6 0 9 1 1 0 3 1 4s2 0 1 2v1 1c0 1 0 2-1 3h0c1 1 1 1 1 0 2 2 2 3 4 4l-1 2v1c0 1-2 3-2 4 0 0 1 0 0 1v2l-1-1h-4v-2h-2c-1 4-3 6-4 9l-3-1-2-1-4-4-7-7h0-1v-1c-2-1-4-2-6-2-1-1-1-2-1-3l-2 1v-2s0-1 1-2h1v-1-1s-2-1-3-1l-1 1h-3-5l-3-1 3-4 2-3 2-2 3-7-1-1c-1 2-2 3-2 5-1 0-1 1-2 0l-3 1v1c-1 1-2 1-3 2h-1 0-1c0-1 0-1 1-2h1c0-1 0-1-1-2l1-1c0-1 2-3 2-4v-2c1-1 2-1 2-2 1-1 0-2 0-3l2-1v-2l-1-1c2-6 4-12 4-19l1 1c1-1 1-1 1-2h0v-1l1 1v-2c2-2 3-1 5-1v-2h7 3c1-1 1-1 2-1z"></path><path d="M390 805c1-1 1-1 1-2v-1h0l1-1 1 2c-1 1 0 2 0 4v1c2-1 2-1 3-1v1l2 1h-5c-1 2 0 3 0 6v4l-1 1v2h0-1v-3-9-1c0-2 0-2-1-4z" class="d"></path><path d="M385 811c1 1 0 1 1 1v-1-1c2-1 3-1 5 0v9 3h1c-1 1-1 3-1 4-1-1-1-3-2-4-1-2-1-3-2-5h-1l-1 1-2 1c0-2 1-3 1-5 1-1 1-1 1-3z" class="F"></path><path d="M385 818l-1-1c2-1 2-2 3-4h2l-1 1 1 1v1l2 3v3h1c-1 1-1 3-1 4-1-1-1-3-2-4-1-2-1-3-2-5h-1l-1 1z" class="C"></path><path d="M385 847c1-2 5-4 7-4h1c1 0 2 0 3-1v-3h-1v-1h2v-1l-1-1c1 0 2 1 4 0l-1-1c-1 0-1 0-2-1h5v-1l-1-1h-1 2v-1s-1 0-2-1h-1c1 0 2 0 4-1-1-1-1-1-2-1l1-1c0-1-1-1-1-2v-1c0-1 0-2-1-2h-3v-1l2-1v-2l-3-1h0c1-1 2-1 3-2h3 1v2l-1 1v1l-1 2c1 0 1 1 2 1l1 2h1v2h0c1 1 2 1 3 3h0l-3 2h-1c0 1 1 1 2 2-1 1-2 0-2 1l1 1 1 1c-1 1-2 0-3 0v1l1 2h-2v1l-2 1v1l-1 1c-2 0-2 1-4 2-1 1-2 1-2 2l-1-1h-4c-1 0-2 1-3 1z" class="d"></path><path d="M385 818l1-1h1c1 2 1 3 2 5 1 1 1 3 2 4v2 2c0 2 1 6 0 8l-2 1h0v1l1 1h-1v1h1c-1 1-1 1-2 1 0 0-1 0-2 1h-1v-1-1s-2-1-3-1l-1 1h-3-5l-3-1 3-4 2-3 2-2 3-7c1-1 1-2 2-3 0-1 1-2 1-3l2-1z" class="E"></path><path d="M373 837c5 2 13 1 18 1l-2 1h0v1c-5-1-10-1-15 0l-1 2-3-1 3-4z" class="C"></path><path d="M373 842l1-2c5-1 10-1 15 0l1 1h-1v1h1c-1 1-1 1-2 1 0 0-1 0-2 1h-1v-1-1s-2-1-3-1l-1 1h-3-5z" class="B"></path><path d="M377 832c1 0 2 0 3-1s2-2 3-2v-1h-3l1-1c2 0 2 1 4 0 0-2 0-2 1-3 2 1 3 2 4 4-1 1 0 1 0 3h-1l-1 1h1c0 1 1 1 1 3-3 0-12 1-14-1h-1l2-2z" class="J"></path><path d="M415 832c2-1 0-2 1-4 0 0 1-1 1-2 0 3 1 6 0 9 1 1 0 3 1 4s2 0 1 2v1 1c0 1 0 2-1 3h0c1 1 1 1 1 0 2 2 2 3 4 4l-1 2v1c0 1-2 3-2 4 0 0 1 0 0 1v2l-1-1h-4v-2h-2c-1 4-3 6-4 9l-3-1-2-1-4-4-7-7h0-1v-1c-2-1-4-2-6-2-1-1-1-2-1-3 1 0 2-1 3-1h4l1 1c0-1 1-1 2-2 2-1 2-2 4-2l1-1v-1l2-1v-1h2l-1-2v-1c1 0 2 1 3 0l-1-1-1-1c0-1 1 0 2-1-1-1-2-1-2-2h1 2 0l1-1c2 0 3 1 5 2h0l-3-3 2-1 1 2c1 0 1 0 2-1v3z" class="c"></path><path d="M412 834c2 1 2 3 2 4l1 3c-1 3 0 5-1 7 0-2 1-6-1-8-1 0-2 0-2-1l2-1c0-2 0-2-1-4z" class="P"></path><path d="M405 831h2 0l1-1c2 0 3 1 5 2h0l-3-3 2-1 1 2c1 0 1 0 2-1v3l-1 6c0-1 0-3-2-4l-4-2-1 1 3 2h-1l-3-1-1 1-1-1c0-1 1 0 2-1-1-1-2-1-2-2h1zm-17 15h4l1 1c0-1 1-1 2-2 2-1 2-2 4-2l1-1v-1l2-1v4c-1 1-2 1-3 1 0 1 1 2 1 4v1l1 2c-1 0-2 0-3 1v-1c-1-1-2-1-3-2-1 0-1 0-2-1-2-1-3-1-5-3z" class="Q"></path><path d="M415 832c2-1 0-2 1-4 0 0 1-1 1-2 0 3 1 6 0 9 1 1 0 3 1 4s2 0 1 2v1 1c0 1 0 2-1 3h0c1 1 1 1 1 0 2 2 2 3 4 4l-1 2v1c0 1-2 3-2 4 0 0 1 0 0 1v2l-1-1h-4v-2h-2c1-3 1-6 1-9 1-2 0-4 1-7l-1-3 1-6z" class="X"></path><path d="M415 841c1 3 1 5 2 7l-1 1 1 1c-1 1-1 0-1 2l1 1-1 1h1 1l1 1c-2 1-3 1-4 2h-2c1-3 1-6 1-9 1-2 0-4 1-7z" class="d"></path><path d="M398 787h1v2c1 1 1 2 2 2h0c-1 1-2 1-2 3h0l-1 2h4 1c1 1 0 2 3 2h1c0 1 1 1 1 2-1 1-2 1-1 3v2l-1 1c-1 0-3 1-4 1h-1l1 1c-1 1-3 1-4 1l-2-1v-1c-1 0-1 0-3 1v-1c0-2-1-3 0-4l-1-2-1 1h0v1c0 1 0 1-1 2 1 2 1 2 1 4v1c-2-1-3-1-5 0v1 1c-1 0 0 0-1-1 0 2 0 2-1 3 0 2-1 3-1 5 0 1-1 2-1 3-1 1-1 2-2 3l-1-1c-1 2-2 3-2 5-1 0-1 1-2 0l-3 1v1c-1 1-2 1-3 2h-1 0-1c0-1 0-1 1-2h1c0-1 0-1-1-2l1-1c0-1 2-3 2-4v-2c1-1 2-1 2-2 1-1 0-2 0-3l2-1v-2l-1-1c2-6 4-12 4-19l1 1c1-1 1-1 1-2h0v-1l1 1v-2c2-2 3-1 5-1v-2h7 3c1-1 1-1 2-1z" class="U"></path><path d="M387 804c1-1 0-1 2 0l1 1c1 2 1 2 1 4v1c-2-1-3-1-5 0v1 1c-1 0 0 0-1-1 0-2 1-4 1-5 1-1 0 0 1-2h0z" class="L"></path><path d="M378 794l1 1c0 1 0 2 1 3l2-2h1c2 0 1-1 2 0l1 1-1 1h-1l-1 1h1l1-1c1 3 0 4 0 7h0v1c0 1 0 1-1 2h-4-2l-1 2v2h0l-2 2-1-1c2-6 4-12 4-19z" class="Z"></path><path d="M398 787h1v2c1 1 1 2 2 2h0c-1 1-2 1-2 3h0l-1 2h4l-1 2c-2-2-6-1-8-1 1-1 2-1 4-1h0c-1-1-1-1-2-1h0l-3-1h-5c-2-1-4-1-6-1v-2c2-2 3-1 5-1v-2h7 3c1-1 1-1 2-1z" class="d"></path><path d="M398 787h1v2c1 1 1 2 2 2h0c-1 1-2 1-2 3l-1-1c0-1-1-1-1-1v-1-1c-2 0-5-1-7-1-1 1-2 1-4 1v-2h7 3c1-1 1-1 2-1z" class="P"></path><path d="M381 793v-2c2-2 3-1 5-1h1c2 0 3 0 5 1v1h-2c2 1 4 1 5 2v1h0l-3-1h-5c-2-1-4-1-6-1z" class="Y"></path><path d="M402 796h1c1 1 0 2 3 2h1c0 1 1 1 1 2-1 1-2 1-1 3v2l-1 1c-1 0-3 1-4 1h-1l1 1c-1 1-3 1-4 1l-2-1v-1c-1 0-1 0-3 1v-1c0-2-1-3 0-4l-1-2-1 1h0v1c0 1 0 1-1 2l-1-1c-2-1-1-1-2 0v-4c1-2 1-2 2-2s2 1 3 2h0c0-2 0-2 1-3 2 0 6-1 8 1l1-2z" class="b"></path><path d="M402 796h1c1 1 0 2 3 2h1c0 1 1 1 1 2-1 1-2 1-1 3v2l-1 1c-1 0-3 1-4 1h-1l1 1c-1 1-3 1-4 1l-2-1v-1c1 0 1 0 2-1h-3v-1h6 1v-1c-2-1-4-1-7-1h0-1 1c3-1 6-1 8-1v-2c-1-1-1-2-2-2l1-2z" class="R"></path><path d="M402 796h1c1 1 0 2 3 2h1c0 1 1 1 1 2-1 1-2 1-1 3v2l-1 1-1-2c-1-2-1-2-1-4h-1c-1-1-1-2-2-2l1-2z" class="P"></path><path d="M404 800l1-1 1 2c0 1 0 1-1 3-1-2-1-2-1-4z" class="c"></path><path d="M375 814l2-2h0v-2l1-2h2 4v3c0 1-1 2-1 4 0 1-1 2-1 4l-1 1-2 4c-1 2-2 3-2 5-1 0-1 1-2 0l-3 1v1c-1 1-2 1-3 2h-1 0-1c0-1 0-1 1-2h1c0-1 0-1-1-2l1-1c0-1 2-3 2-4v-2c1-1 2-1 2-2 1-1 0-2 0-3l2-1v-2z" class="Y"></path><path d="M374 829l3-4c0-2 1-4 1-5 1-1 2 0 3 0l-2 4c-1 2-2 3-2 5-1 0-1 1-2 0h-1z" class="K"></path><path d="M372 830l-1-1v-2c1-1 1-1 1-2l4-8h0c0 3 1 4-1 7v1 1h-1c-1 0-1 1-2 2l2 1h1l-3 1z" class="F"></path><path d="M511 798h3c0 1 0 2 1 3h-1v2l1 1c0 1 0 1 1 1 2 0 3 0 5 1h4c1 1 3 1 4 1l1 5v2 4l1 5v2 4 2c1 6 2 12 2 17l1 8 3 18 1 1 1 7 1 4v1 3l1 10c0 1 1 3 1 4v2c-8-1-17-1-25 0l-1-1v-10-6l-2-1h-3-4-8c-1-1-1-2-1-3h-2c-2 0-4 0-5 1-2 1-4 1-6 2h-1c0-2 0-4 1-6l2-14 9-62c1 0 2-1 3-1 2 0 3 0 5-2l2-3h0-1l1-1c2 0 3-1 5-1z" class="Y"></path><path d="M506 818h1 7l-1 3c-2 1-6 1-9 1 1-1 1-2 2-2v-1-1z" class="B"></path><path d="M495 873v-1c1-1 1-1 2-1 3 2 5 0 7 2v1h10c-6 1-13 0-20 1h-1l1-1h2c1 0 1 0 2-1h0-3z" class="S"></path><path d="M500 870c2 1 6 1 8 0h7c0 2 0 3-1 4h-10v-1c-2-2-4 0-7-2l3-1z" class="D"></path><path d="M516 805c2 0 3 0 5 1h4c1 1 3 1 4 1l1 5c-1-1-2-1-3-1-2-1-4-1-7-1l-4-1c-1-1-1-1-1-2l1-2z" class="E"></path><path d="M515 807h5l1 1-1 2-4-1c-1-1-1-1-1-2z" class="J"></path><path d="M515 854h6c2-1 4 0 6 0v1h4c1 0 1 0 1 1s0 2 1 2c-4 1-9 0-13 0-1 0-3 1-4 0l-1-4z" class="W"></path><path d="M515 827v-4c5-1 11 0 16 2v4h-4c-4-1-8-2-12-2z" class="D"></path><path d="M500 813c1-1 2-1 4-1v1l2-2h7l1 1v4 2h-7-1-2-5l1-1c2 0 3-1 5-1l-2-1h-1c-1 0-1 1-2 1v-1-2z" class="E"></path><path d="M514 816v2h-7-1-2-5l1-1c2 0 3-1 5-1h0c3-1 6 0 9 0z" class="i"></path><path d="M507 832c2 0 3-1 6-1h0 0c0 2 1 5 0 7h-1l-16 1v-1h1c1-1 1-1 3-2 1 0 1 0 2-1v-2l5-1z" class="B"></path><path d="M512 838c-2-1-5 0-7 0v-2c1-2 5-2 8-3v5h-1z" class="N"></path><path d="M487 868c2 0 2 0 3 1l-1 2c2 0 3-1 4 1-1 0 0 0-1 1h3 3 0c-1 1-1 1-2 1h-2l-1 1-1 1c-2 1-3 0-5 2 1 1 1 1 2 1v-1h2c-1 1-1 2-2 3s-1 0-2 2h1l1 1-2 1v1c1 0 2-1 2-1l1-1v1c0 1 0 0 1 1-2 1-4 1-6 2h-1c0-2 0-4 1-6l2-14z" class="L"></path><path d="M532 849l1-1 1 8c0 1 0 2-1 2h0c-1 0-1-1-1-2s0-1-1-1h-4v-1c-2 0-4-1-6 0h-6v-4l1-1h9c2 0 5 0 6 1h1v-1z" class="i"></path><path d="M504 840l9-1c0 3 1 7 0 9-4 1-8 0-12 0-2 0-3 1-5 0 1-1 1-1 1-2v-1h2c0-2 1-4 2-5h3z" class="D"></path><path d="M501 840h3l-2 2c1 1 2 1 2 2h-3c-1 1-1 1-2 1 0-2 1-4 2-5z" class="C"></path><path d="M499 845v1h3 0c1 1 2 1 2 2 3 0 6-1 9 0-4 1-8 0-12 0-2 0-3 1-5 0 1-1 1-1 1-2v-1h2z" class="E"></path><path d="M517 880c0-1-1-4 0-5h21l1 7h-2c-7-1-14-1-20-2z" class="W"></path><path d="M518 889v10c5 0 12-1 17 1 2 0 4 1 6 0h0c0 1 1 3 1 4h-25v-9c0-2 0-4 1-6z" class="J"></path><path d="M527 823c-4-2-8-2-12-2v-6l1-4h3v1l2 1v-1h1 2 2c1 1 2 1 4 2h0v4l1 5h-4z" class="D"></path><path d="M530 818l1 5h-4l1-1h1l1-1h-2l-2-2c-1 0-1 0-1-1h5z" class="B"></path><path d="M517 880c6 1 13 1 20 2h2l1 4v1l-1 1h-22v-8z" class="J"></path><path d="M502 860c3-1 7 0 11 0h1l1 5v5h-7c-2 1-6 1-8 0-1-1-2-1-4-1v-1h2l3-1h0c-2 0-3 0-4-1l2-2h-2l-1-1h6v-3z" class="D"></path><path d="M515 865v5h-7c-2 1-6 1-8 0-1-1-2-1-4-1v-1h2l1 1h2c3 1 11 1 13 0l1-4z" class="E"></path><path d="M502 860c3-1 7 0 11 0h-3l1 1c1 0 2 0 3 1-3 1-10 1-12 1v-3z" class="H"></path><path d="M518 889l22 1 1 10h0c-2 1-4 0-6 0-5-2-12-1-17-1v-10z" class="N"></path><path d="M503 876l12-1v12c-1 1-2 1-4 1h-4-8c-1-1-1-2-1-3h-2c-2 0-4 0-5 1-1-1-1 0-1-1v-1l-1 1s-1 1-2 1v-1l2-1-1-1h-1c1-2 1-1 2-2s1-2 2-3c1 0 2 0 3-1 1 0 2 0 3-1h6z" class="i"></path><path d="M497 876h6c-2 1-1 3-4 3-2 0-3 2-5 3h1 2v-1h1v1c3 3 5 0 6 5l3 1h-8c-1-1-1-2-1-3h-2c-2 0-4 0-5 1-1-1-1 0-1-1v-1l-1 1s-1 1-2 1v-1l2-1-1-1h-1c1-2 1-1 2-2s1-2 2-3c1 0 2 0 3-1 1 0 2 0 3-1z" class="S"></path><path d="M491 878c1 0 2 0 3-1 1 0 2 0 3-1 0 1 0 2 1 3-2 0-3 1-5 1l1 1-1 2h1v1h-2l1 1s2-1 3 0c-2 0-4 0-5 1-1-1-1 0-1-1v-1l-1 1s-1 1-2 1v-1l2-1-1-1h-1c1-2 1-1 2-2s1-2 2-3z" class="K"></path><path d="M534 856l3 18h-21v-7-4-3c2-1 14 0 17-1v-1c1 0 1-1 1-2z" class="J"></path><path d="M516 863h17l1 2c0 1 0 1 1 2-2 1-5 0-7 0-3-1-9-1-12 0v-4z" class="D"></path><path d="M515 827c4 0 8 1 12 2h4v2c1 6 2 12 2 17l-1 1-17-2c-1-2 0-5 0-8l10 1c2 1 5 1 6 0h0c-5-2-11-2-16-2 0-1-1-4 0-5v-1-3-2z" class="N"></path><path d="M515 827c4 0 8 1 12 2h4v2c-4-1-12-2-16-2v-2z" class="C"></path><path d="M511 798h3c0 1 0 2 1 3h-1v2 2 2 2c-2 1-7 0-9 1h-1c-1 0-2 0-3 1h-3s-1 0-1 1v3h0c1 0 1-1 2-2h1v2 1c1 0 1-1 2-1h1l2 1c-2 0-3 1-5 1l-1 1h5 2v1 1c-1 0-1 1-2 2-2 0-4 0-6 1h-2l-1 1c1 1 2 1 3 1l1-1h4c2-1 4-1 7-1h3v2 3 3h0 0c-3 0-4 1-6 1l-5 1v2c-1 1-1 1-2 1-2 1-2 1-3 2h-1v1c-1 0-2 0-3 1s-1 1-1 2l1 1c1 0 1-1 1-2h1 1 2l3-1c-1 1-2 3-2 5h-2v1c0 1 0 1-1 2 2 1 3 0 5 0h-1c-2 1-4 0-7 1h-1c-1 1-1 1 0 2l6-1c3 0 6-1 8-1s5-1 7 0h1v4 1 4c-2 1-6 0-9 0l-3 1h-10l-1-1c-1 1-1 1-1 2s0 2-1 3v2h1c1-1 2-2 5-3 2-2 5-2 7-2v3h-6l1 1h2l-2 2c1 1 2 1 4 1h0l-3 1h-2v1c2 0 3 0 4 1l-3 1c-1 0-1 0-2 1v1h-3c1-1 0-1 1-1-1-2-2-1-4-1l1-2c-1-1-1-1-3-1l9-62c1 0 2-1 3-1 2 0 3 0 5-2l2-3h0-1l1-1c2 0 3-1 5-1z" class="F"></path><path d="M505 810v-3c3-1 6-1 9 0v2c-2 1-7 0-9 1zm9-5c-3 0-7 1-9 0l1-1c1 0 1 0 2-1 2-1 2-2 4-2h2v2 2z" class="B"></path><path d="M498 823v-2c1-1 2-1 3-1-1-2-3-1-4-1 1-2 1-2 3-2l-1 1h5 2v1 1c-1 0-1 1-2 2-2 0-4 0-6 1z" class="C"></path><path d="M492 859c0-1 1-1 1-1v-1h-1l2-2h0-2l1-1h1c1-1 1-1 3-1v1c1 1 2 1 3 1-1 1-1 1-2 1l-1 1 1 1 4 1h-10z" class="K"></path><path d="M497 853h2 3v1h5c2-1 5-1 7-1v1h-6c-2 0-3 1-5 1l-1 2v1h3l-3 1-4-1-1-1 1-1c1 0 1 0 2-1-1 0-2 0-3-1v-1z" class="B"></path><path d="M505 858h-3v-1l1-2c2 0 3-1 5-1h6v4c-2 1-6 0-9 0z" class="N"></path><path d="M498 850c3 0 6-1 8-1s5-1 7 0h1v4c-2 0-5 0-7 1h-5v-1c0-2-4-1-5-2l1-1z" class="D"></path><path d="M510 823h3v2 3 3h0 0c-3 0-4 1-6 1-1-1-3-1-4-1v-1c1 0 2 0 3-1-2-1-2 0-4 0v-2-1l3 1c1-1 1-2 1-3v-1h4z" class="E"></path><path d="M505 827c1 0 2-1 3-1 2-1 4-1 5-1v3h-8v-1z" class="D"></path><path d="M510 823h3v2c-1 0-3 0-5 1-1 0-2 1-3 1 1-1 1-2 1-3v-1h4z" class="S"></path><path d="M508 33c2-1 3-2 4-1 1 0 2 0 2 1 1 0 1 1 1 2-1 1-2 1-3 3v20l1 1c2 4 2 9 3 13l6 21 303 1c2 21 1 42 1 63l1 41v61l1 50-1 51-2 36c0 6 0 13-2 18-2-7-4-15-6-23-3-9-6-19-10-27-12-33-30-64-51-92-10-13-19-25-31-35-7-6-14-11-22-16-8-4-15-9-24-14-6-3-13-6-21-8-15-5-31-7-47-9l1 211v1c3 2 5 4 7 7 3 3 5 6 8 9s6 5 8 8c5 7 8 15 9 24l1 10c4 2 8 4 10 9 0 3 0 7-2 10-1 2-3 4-6 5h-1v1c0 3-1 15 1 17h1v1l1 1c1-1 1-1 1-2 1 0 2 1 4 1v1l-1 2h0l3 2v8c0 2 1 4 0 5-1 4-10 8-13 9-1 4-1 10-1 14 0 1 1 3 0 5h-1c-9-5-22-5-33-4l5 1 1 1h2l1 1v8l-1 2c-1 0 0 0-1-1h-6c-1 1-2 1-3 2v-2h-2l-2 2v1c0 1-1 2-2 3v1h-1v-1c-1 1-1 1-2 1l-1-2c-1 1-1 1-1 2h0v6c1 1 1 2 3 3h0c-3 1-5 1-8 2-1 0-3 0-4 1-1 0-3 2-4 2v1c2 1 2 0 2 1v1l-2 1c-1 1-1 3 0 5l2 1-1 1h-5c2 0 3 1 5 1 2-1 5-1 7 0l2 2h0l2 2-1 8 1 4v7l-2 1c-1 0-2 1-3 1l-1-1v-2c-2-1-1-1-1-2-1-1-1-1-2-1v33c0 4 1 8 0 12v1h0l-1 2 2 2c1 0 2-1 3 0 2 2 4 3 7 2l-1 2c1 0 1 0 1 1h7s0 1-1 1c-2 1-3 1-4 3l6 3c0 1 1 1 1 1l1 1h2 1v1 1h1v1c-1 1-1 1-2 1-2 0-3 1-4 2l-1-2c0-1 0 0 1-1l-1-1c-1 1-1 1-2 1-2-2-3-2-5-3l-1-1c0 1-1 2-1 3h0l-1-1c-2-1-3-1-4-3-2-1-5 0-7-1l-1-1-2 1c-1-1-2-1-3 0h0c-1 0-2-1-3-1v-1c-2 0-3 1-4 1h0-1v-2h-1c-2-2-3-5-3-8l-1-5c-1 0-1-1-1-1 0-3-2-5-3-7-3-6-4-11-6-17-1-3-2-5-1-8-2 0-5-1-7-1l-11-2h-1-2c-1 1-2 1-3 2-1-1-2-1-3-1-2-1-4-1-6 0-2-1-5-1-7-1-1 1-3 1-4 1-3 0-7-1-11-1h-2-2c-3-1-6 0-8-2h-1l-1 1 1 1c1 0 1-1 2 0v1 1h-2c-3 1-7 0-10 2v1 1h-1v1c-1 1-2 1-3 2l-1 1-2-1h0l-1-1h-5v2c0 2 0 11-1 13 0 1-1 1-1 2-2 0-3 2-5 3l-9 6-15 9c-2 1-6 4-8 4v-21-8h-1c1-1 1-2 1-3v-16l-6-2c-2-1-3-1-5-1l-2-2h-13-19 0v-1h6c3 1 7-1 11-1 1 0 3 1 5 0 1 0 2-1 3-1v2l2-1 1 1h3 1l1-1v-2h0l-1-1h-1-1v-3l8 1h1c1-2 0-5 1-7 0-1-1-3-1-4v-1c-1-4-2-7-4-10l-1-1v-1c0-4-5-8-7-10-2-1-3-2-5-3l-5-4h1c6 4 14 8 18 15h0V189c-12 1-24 4-36 6l-16 4c-10 3-20 7-30 12-15 7-29 17-40 29s-21 26-30 40l-7 12-28 52c-11 23-22 46-30 70-3-20-6-40-7-60l-1-51 1-69 1-105 2-35 308-1 9-30h0l-9 33c0 2-1 6-1 7 3-7 5-15 7-22 1-6 4-13 4-18 0-3 1-5 1-7V42v-4s-3-2-4-3v-1l1-1z" class="T"></path><path d="M581 556h1l1 1v1h-2l-1-1 1-1z" class="F"></path><path d="M429 446h2l1 1-1 2h-2v-1-2z" class="S"></path><path d="M682 143h1c1 1 0 2 0 3l-1 1c-1-1-1-1-1-2l1-2z" class="H"></path><path d="M747 213c1 0 1 0 2 1 0 1 0 1-1 2h-1c-1-1-1-1-1-2l1-1z" class="B"></path><path d="M406 111h2c0 2-1 2-2 3h0c-2 0-1-1-2-2l2-1z" class="E"></path><path d="M352 181l2 1c0 1-1 1-2 2h-2v-1l2-2zm-115 95c1 0 1 0 2 1v1l-1 2h-1c-1-1-1-1-1-2l1-2zm523-165h1c1 0 1 0 2 1l-1 2h-2l-1-1c0-1 0-1 1-2z" class="H"></path><path d="M428 327h1c1 1 1 1 1 2l-1 1h-2l-1-2 2-1z" class="C"></path><path d="M397 108c1 0 1 0 3 1 0 1 0 1-1 2h-2c-1 0-1 0-1-1l1-2zm211 264h1v1c0 2 0 2-1 3-1 0-1 0-2-1 0-1 1-2 2-3z" class="D"></path><path d="M441 550h1l2 2-1 1c-1 1-2 0-3 0v-2l1-1z" class="H"></path><path d="M439 520c1 0 1 0 2 1s0 1 0 2l-2 1c-1-2-1-1-1-3l1-1z" class="C"></path><path d="M425 391h0c2 1 2 1 2 2l-1 1-1 1-2-1c1-2 1-2 2-3z" class="F"></path><path d="M443 538l1-1c1 1 1 1 2 3l-1 1-1 1-1-1c-1-2-1-2 0-3zM342 132h2l1 1-2 2c-2 0-2 0-3-1 0-1 1-1 2-2z" class="J"></path><path d="M799 154l1-1 1 2c0 1 0 1-1 2h-1c-1 0-1-1-2-2l2-1z" class="H"></path><path d="M206 202h1c1 1 1 1 1 2l-1 2-1 1-1-1c0-2 0-3 1-4zm35 59c1 0 1-1 2 0 0 2 0 2-1 3h-1c-1 0-1 0-1-2l1-1z" class="B"></path><path d="M570 372h1c1 0 1 0 1 1 1 1 0 2 0 3-2 0-2 0-3-1 0-1 0-2 1-3z" class="N"></path><path d="M578 368h2c1 2 1 2 0 4h-1c-2-1-2-2-2-3l1-1z" class="J"></path><path d="M758 169c2 0 2 0 3 1 0 1 0 1-1 3h-1l-1-1c-1-2-1-2 0-3z" class="D"></path><path d="M454 325h2l1 1c0 1 0 1-1 3h-1l-1-1c-1-2-1-2 0-3z" class="C"></path><path d="M438 307h1c1 1 1 1 1 3l-2 1h-1l-1-1c0-2 1-2 2-3z" class="J"></path><path d="M399 149h2c1 1 1 1 1 3l-2 1c-1 0-1-1-2-2v-1l1-1z" class="D"></path><path d="M558 127c1 0 1 0 2 1s1 1 0 3h-2c-1-1-1-1-1-2l1-2z" class="I"></path><path d="M605 354h1l1 1c0 1-1 2-2 3-1 0-2-1-2-2s0-1 2-2z" class="N"></path><path d="M777 123h1c1 0 2 0 2 1l-1 2c-1 1-1 1-2 1l-1-1c0-2 0-2 1-3zM444 488h1l2 1c0 2-1 2-2 3h0c-2 0-2 0-2-1 0-2 0-2 1-3z" class="D"></path><path d="M509 429v-2h6v2l-6 1v-1z" class="H"></path><path d="M434 482c1 0 2 1 2 2s0 2-1 3h-1c-1-1-2-1-2-2 1-2 1-2 2-3z" class="i"></path><path d="M211 151c1 0 1 0 2 1 0 2 0 2-1 3l-2 1c-1-1-1-2-1-3l2-2z" class="E"></path><path d="M451 582l6 2-3 3h0-3c0-1 0 0 1-1v-1c0-1 0-1-1-3zm106-47l3-3 2 1h0l1 5c-1 0-2-1-2-1-2-1-3-1-4-2z" class="C"></path><path d="M814 181h3l1 1c0 2 0 2-1 3s-1 1-2 1c-1-2-1-2-2-3l1-2z" class="B"></path><path d="M577 589h1c2 0 3 1 5 1s3 0 4 1c-4 1-10 0-14 0l4-1v-1z" class="Y"></path><path d="M457 584c1 0 2 0 2 1 1 1 1 1 1 3-2 0-5 0-6-1h0l3-3z" class="M"></path><path d="M459 186c1 0 2 0 3 1v2l-2 1-1 1-2-2c1-2 1-2 2-3z" class="N"></path><path d="M196 176c1 0 1 0 3 1v1c0 1-1 1-2 2l-1 1-2-2c0-1 1-2 2-3z" class="D"></path><path d="M567 220h2l1 1c0 2-1 2-1 3h-3c-1-1-1-2 0-3l1-1z" class="N"></path><path d="M308 182c1-1 2-1 3-1 1 1 1 1 2 3l-2 1c0 1-1 1-1 1-2-1-2-1-3-3l1-1z" class="E"></path><path d="M812 113h1c1 0 1 1 2 2 0 1 0 1-1 2s-1 1-2 1l-2-2c1-1 1-2 2-3zm-183 5h1c1 1 2 1 2 2-1 1-1 2-2 3h-2l-1-2c0-1 1-2 2-3z" class="N"></path><path d="M821 266h2c0 1 1 1 1 2 0 2 0 3-1 4h-1c-1-1-2-1-2-2 0-2 1-3 1-4z" class="W"></path><path d="M794 202h1c1 1 2 1 2 3 0 1 0 1-2 2h0c-2 0-2-1-3-2 0-1 1-2 2-3z" class="i"></path><path d="M581 265h1c1 1 2 1 2 2 0 2 0 2-1 4l-1 1v-1c-1-1-2-3-2-4l1-2zm34-108h3v1c0 2-1 3-2 4h-1c-1 0-1 0-2-2 0-1 1-2 2-3z" class="N"></path><path d="M821 372h2c0 1 1 2 1 3l-2 2h-1c-2-1-2-1-2-3 0-1 0-1 2-2z" class="W"></path><path d="M474 536l7-1v1h1 2l1 1c-2 0-4 1-5 1h-1c-1-1-2-1-4-1h-1v-1z" class="O"></path><path d="M723 180h3c2 1 2 2 2 4l-1 1c-1 0-2 0-3-1-1 0-2-1-2-2l1-2z" class="D"></path><path d="M508 33c2 0 4-1 5 0l1 1c-1 2-1 2-3 3-1 0-2-1-3-2l-1-1 1-1z" class="N"></path><path d="M325 174h1c1 1 1 1 2 3 0 1-1 2-2 3h-1c-1-1-2-2-2-3s1-2 2-3z" class="B"></path><path d="M811 211h1l2 2c0 2 0 2-1 3-1 0-2 1-3 0s-1-1-1-2c0-2 1-2 2-3z" class="W"></path><path d="M198 352h1l2 2-2 4h-2c-1-1-1-2-1-4 0-1 1-1 2-2z" class="N"></path><path d="M774 135h2c1 1 1 2 1 3 0 2 0 2-2 3h-1c-2-1-2-1-2-3 0-1 1-2 2-3z" class="D"></path><path d="M661 136h1c1 1 1 1 2 3-1 2-1 2-3 3h-2c-1-1-1-1-1-2 1-2 1-3 3-4z" class="N"></path><path d="M455 623v-1c1 0 2 0 3 1h2 1v2 1 2l-2-1h-1 0l-1 1h-4l2-1c0-2 0-2 1-3v-1h-1z" class="c"></path><path d="M341 122h3c1 0 2 1 2 2 0 2 1 2-1 4h-1c-2-1-2-1-3-3 0-1-1-1 0-3z" class="N"></path><path d="M573 591c-2 0-5 0-7-1l-1-1v-2h2c1 1 1 1 3 1s5 1 7 1v1l-4 1z" class="M"></path><path d="M208 172l1-1c2 1 2 2 3 3 0 2-1 3-2 4l-2 1c-1-1-1-3-2-4 0-2 1-2 2-3z" class="N"></path><path d="M481 535c1-1 2-1 2-1 4 0 7-1 11-1-1 1-2 1-2 1 0 1 1 1 1 2l-5 1h-3l-1-1h-2-1v-1z" class="F"></path><path d="M805 193h2c1 0 2 1 2 2-1 2-1 3-3 4h-1c-2-1-2-2-2-3 0-2 1-2 2-3z" class="W"></path><path d="M442 461h2c1 1 1 1 2 3-1 1-1 2-3 3h-1c-2-1-2-2-3-3 1-2 1-2 3-3z" class="N"></path><path d="M744 186h2c1 1 2 2 2 3-1 2-1 2-2 3h-2c-2-1-2-1-2-3s1-2 2-3z" class="W"></path><path d="M584 608c-4 1-14 1-19-1l-1-1c3-1 8 0 12 0 3 1 6 0 8 0v2z" class="C"></path><path d="M469 101c2 0 2 0 4 1v2c0 1-1 2-2 3h-2c-2-1-2-1-2-3s0-2 2-3z" class="W"></path><path d="M462 539l12-3v1h1c2 0 3 0 4 1-1 0-2 1-4 1h-3c-1 1-2 1-2 1l-1-1h-1-2l-1 1h-1-1-2v-1h1z" class="M"></path><path d="M423 365h3c1 1 1 1 2 3l-3 3c-1 0-1 0-2 1l-2-2c0-3 0-3 2-5zM242 147h3c1 0 2 1 2 2 0 2 0 2-2 3-1 1-2 1-3 1-1-1-2-1-2-2 0-2 1-2 2-4z" class="W"></path><path d="M468 532c2-1 4 0 6-1h3v1h-2s-1 1-2 1c0 1 0 1 1 1-3 1-11 2-12 4v1h-1v-2c1 0 2-1 3-1 2-1 3-2 5-3l-1-1z" class="X"></path><path d="M648 100c1-1 1-1 2 0 1 0 2 1 3 2-1 2 0 2-2 4h-3c-1-1-1-2-2-3 0-2 1-2 2-3zm-82 97h1c2 0 2 0 3 1s1 2 1 3c-1 2-1 2-3 3h-2c-1-1-2-2-2-4 1-1 1-2 2-3z" class="W"></path><path d="M586 521h3l1-1-1 1v2h1v1h-1 0l1 1-1 7c-1-1-1-1-2-1v-3c0-2 0-2-2-3v-1c-1 0-2 0-4-1l1-1 4-1z" class="P"></path><path d="M403 127h2c1 0 2 1 3 2 0 1 0 2-1 3 0 1-1 1-2 2-1 0-2 0-3-1-1 0-1-1-1-2 0-2 1-2 2-4z" class="N"></path><path d="M588 514l1 1 1 1-10 3v1h1-1c-2 1-3 2-5 2h-1c1-2 1-3 1-4l2-2h2l-1 1 1 1 2-1v-1l2-1h1c1 0 3 0 4-1z" class="Q"></path><path d="M574 286h2l1 1c1 1 1 2 1 4 0 1-1 2-2 3-1-1-2-1-3-1-1-2-1-2-1-4s1-2 2-3z" class="W"></path><path d="M600 255c1 0 2 0 3 1s0 3 0 4c-1 2-1 2-3 2-1 0-1 0-2-1s-1-2-1-3c1-2 1-2 3-3z" class="N"></path><path d="M718 203c1-1 1-1 3-1 1 2 2 2 3 4-1 2-1 2-3 3l-2 1-3-3c0-2 0-2 2-4zm-490 7c1 0 1-1 2-1s1 1 2 1c1 1 1 2 1 3-1 2-2 2-4 3-1 0-2 0-3-1s-1-1-1-3c1-1 2-2 3-2z" class="W"></path><path d="M444 585h-2c-4 1-22-8-25-11v-1c1 1 2 1 4 2 1 0 1 1 2 2 2 0 4 2 6 2 1 1 1 1 3 0l12 6z" class="U"></path><path d="M484 319h1v1c2 1 4 0 6 1 1 1 1 0 2 1h0a126.62 126.62 0 0 0-28 6h-1l5-3 4-1 7-2h3 1l1-1-1-1v-1z" class="L"></path><path d="M552 268l9 3c0 3 1 5 0 8h-1v-2h-2l-2-2h-2 0l1-2 1-1h0c-1-1-2-2-3-4h-1z" class="n"></path><path d="M679 188h2c2 1 2 3 3 4-1 2-2 3-3 4h-1c-1 0-2 0-3-1-1-2-1-3-1-4 1-2 1-2 3-3zm120 93c1 0 2 0 3 1s1 2 1 4c0 1-1 2-2 3h-1c-2 0-3-1-3-1-1-1-1-3-1-4 0-2 2-2 3-3z" class="N"></path><path d="M575 508v-1l1 1c2 1 4-1 5 1s0 2 0 4v3 1l-2 1-1-1 1-1h-2l-2 2v-10z" class="m"></path><path d="M552 247l4 2s2 2 3 2 1-1 2 0l-2 2h1 1v7l-1-2v-1s-1-1-2-1h0c-2-1-3-1-4-1-2 0-2 0-3-1v-4h0c0-1 0-2 1-3z" class="B"></path><path d="M552 247l4 2-1 1v2h-1l-2 1-1-3h0c0-1 0-2 1-3z" class="D"></path><path d="M563 621c0 1 0 1 1 2 3 0 6 1 8 2 4 0 7 1 10 3l1 1c-3 1-15-1-17-3-1 0-2-1-2-2h-1v-3z" class="X"></path><path d="M416 588h0c2 0 5 1 7 1 7 1 14 3 21 3 2 1 7 0 9 2h-9c-6-1-13-1-19-2-3-1-7-1-9-4z" class="E"></path><path d="M417 593c2 3 3 11 3 14h0l1 1c3 1 6 0 9 0 10-1 19-1 28-3v1c-11 4-24 3-36 4-1 0-1 0-2-1l-1-2v-1-3c-1-3-2-7-2-10z" class="D"></path><defs><linearGradient id="R" x1="565.081" y1="287.705" x2="556.168" y2="297.521" xlink:href="#B"><stop offset="0" stop-color="#92908d"></stop><stop offset="1" stop-color="#b0b0b0"></stop></linearGradient></defs><path fill="url(#R)" d="M561 271l1 1v35l-1-1v-3l-2-2c0-7 0-15 1-22h1c1-3 0-5 0-8z"></path><path d="M529 252h1c3 1 5 1 8 1l6 1c1 0 2 0 4 1l8 2h0 2v1c1 2 1 2 3 3 0 1 0 1-1 2-4-2-8-3-13-5-1 0-2 0-3-1-4-2-9-2-14-3v-1l-1-1z" class="H"></path><path d="M417 598v10l-1 1c-1 0-1 0-2 1h-1l-1-2c-1 1-1 1-1 3h4c3 1 6 1 9 2l1 1-1 1-15-3h-13-19 0v-1h6c3 1 7-1 11-1 1 0 3 1 5 0 1 0 2-1 3-1v2l2-1 1 1h3 1l1-1v-2h0l-1-1h-1-1v-3l8 1h1c1-2 0-5 1-7z" class="F"></path><path d="M558 277h2v2c-1 7-1 15-1 22l2 2v3l1 1v10-1c-1 0-1 0-1-1-1-1-2-1-3-2l-1-1c-2 0-3-1-4-2h4-1l1-6v-14c0-4 1-9-1-13h2z" class="S"></path><path d="M559 301l2 2v3l1 1v10-1c-1 0-1 0-1-1-1-1-2-1-3-2l-1-1c-2 0-3-1-4-2h4l1 1h2l-1-10z" class="F"></path><path d="M455 623h1v1c-1 1-1 1-1 3l-2 1h-6c-6 1-12 1-18 0-2 0-3 0-5-2v-1l1-1c1 0 4 1 6 0l18-1h6z" class="Y"></path><path d="M449 623c1 0 3 0 4 1h0c-3 3-23 3-29 2v-1l1-1c1 0 4 1 6 0l18-1z" class="B"></path><path d="M432 579c-3-1-16-7-17-8v-3c3 0 8 4 11 5 2 1 5 3 8 4 5 2 11 4 17 5 1 2 1 2 1 3v1c-1 1-1 0-1 1l-7-2-12-6z" class="J"></path><path d="M492 528v1c1 1 3 1 4 0 1 0 1-1 2-2v1 1h1v-1-2l1-1c1 1 0 4 2 4 2 1 2-1 3-1 0 0 4 2 5 2 1-2 0-4 1-5 1 2 1 3 1 4h0l1-1 2 2c1-1 1-1 1-2l2 2h1l1-3h0v3h2l1-2c1 1 0 1 2 2-3 0-5 1-7 0-3 0-5 1-7 1-13 0-25 1-37 3-1 0-1 0-1-1 1 0 2-1 2-1h2v-1h-3c-2 1-4 0-6 1l-2 2c-1 0-2 1-3 0v-1c2 0 2 0 3-1h-2-1c1-2 4 0 5-1 1 0 1-1 1-1h2l1-1c2-1 4 0 6 0s4-2 6-1v1l1 1 1-1 1 1 1 1h2v-1c1-2 1-2 2-2z" class="O"></path><path d="M482 519c2 1 6 0 9 0-1 0-2 1-3 2h1l1 1h-1c-1 1-3 1-4 1-1 1-2 1-3 1 1 1 2 1 2 2 1 1 1 2 2 2h0 2l-2 1-1 1-1-1v-1c-2-1-4 1-6 1s-4-1-6 0l-1 1h-2c-1 0-2 0-3-1l4-1v-1h-3 0c0-1 1-1 2-1s2 0 3-1h-6v-1c1 0 1-1 2-1 0-1 1-2 2-2h0l1-1h3 0c3 0 5 0 8-1z" class="I"></path><path d="M478 529c0-1 1-2 2-3h1v-1h-2l-1-1c3 0 5-1 7-1-1 1-2 1-3 1 1 1 2 1 2 2 1 1 1 2 2 2h0 2l-2 1-1 1-1-1v-1c-2-1-4 1-6 1z" class="S"></path><path d="M482 519c2 1 6 0 9 0-1 0-2 1-3 2h1l1 1h-1v-1c-4 0-7 1-10 1h-3l-2-2c3 0 5 0 8-1z" class="H"></path><path d="M504 255c3-1 12-1 15 0h2c5-1 10 0 14 1 7 1 13 3 20 6 4 2 7 3 10 6-7-2-14-5-21-7-9-2-18-3-27-4l-13 1v-3z" class="E"></path><path d="M504 255c3-1 12-1 15 0-5 0-9-1-14 0 4 1 8 2 12 2l-13 1v-3z" class="C"></path><path d="M583 590c2-1 5-1 7 0l2 2h0l2 2-1 8 1 4v7l-2 1c-1 0-2 1-3 1l-1-1v-2c-2-1-1-1-1-2-1-1-1-1-2-1l-1-1v-2c2-1 1-7 2-9 0-1 0-3 1-4l1-1c1 0 0 0 1-1h-2c-1-1-2-1-4-1z" class="X"></path><path d="M584 606c2-1 1-7 2-9 0-1 0-3 1-4 1 6 0 13 1 19-2-1-1-1-1-2-1-1-1-1-2-1l-1-1v-2z" class="k"></path><path d="M583 590c2-1 5-1 7 0l2 2h0l2 2-1 8 1 4v7l-2 1v-2c0 1 0 1-1 2l-1-1v-5c0-1-1-2 0-3h0v-3-2-1h-1l1-1c1-1 0-3 0-5l-2-1c1 0 0 0 1-1h-2c-1-1-2-1-4-1z" class="g"></path><path d="M592 592l2 2-1 8 1 4v7l-2 1v-2-20z" class="G"></path><path d="M477 309l5-1v1h-1v2c-1 1-4 2-6 2h-1l1 1-3 1 1 1v1h2v1l-1 1v4l-1 1-4 1-5 3h1l-3 2v-8c0-1-1-1-1-2s1-2 1-2c1-1 2-2 3-2v-1l-2 1c0-1 0-1 1-2 1 0 2-1 3-1h1c1-1 0-1 1-1h2c2-1 4-1 6-3z" class="M"></path><path d="M465 323c1-1 2-2 4-2l1 1c0 1 0 2-1 3l-5 3h-1c0-2-1-3 0-5h1v3l1-1v-2z" class="h"></path><path d="M472 315l1 1v1h2v1l-1 1v4l-1 1-4 1c1-1 1-2 1-3l-1-1c-2 0-3 1-4 2l-2-2h1c-1-1 0-1-1-1h-1c2-2 8-4 10-5z" class="e"></path><path d="M461 505h0c1 2 0 10 1 11 2 0 10-1 12-2h-1c2-1 3-1 5-1h1v-1l-2-1c2-2 3-2 5-3l-1 7h9v2h2l1 1-2 1c-3 0-7 1-9 0-3 1-5 1-8 1h0-3l-1 1h0c-1 0-2 1-2 2-1 0-1 1-2 1v1l-1 1 1 1h-3c-2 0 0 7-1 9-2-2-1-27-1-31z" class="O"></path><path d="M490 517h2l1 1-2 1c-3 0-7 1-9 0h-5l1-1h2c3-1 6-1 9-1h1z" class="U"></path><path d="M526 428l28 4c3 0 6 1 9 2l-1 7-19-5c-6-2-14-2-21-2 1-1 2-1 3-1l-1-2v-1h2l-1-1h-1v-1h2z" class="B"></path><path d="M526 430c1 0 2 0 3 1v1l-4 1-1-2v-1h2z" class="N"></path><path d="M511 57l12 43c-2 1-4 0-5 0-2-1-4-1-6-1 0 3 0 6 1 8v1h-1l-1-8 1-1h-4v4h-1v-3l1-1h-2c-1 0-2 1-3 0-1 0-1 0-1-1l2-1-1-2 2-2v-1-1h1c0-1 0-1-1-1l1-2v-1c0-2 0-4 2-5h1c0-1-1 0-2-1 2-8 4-16 4-24zm-13 61l3-1h-3v-1c1-1 3-1 4-2h-3 0c1-1 2-1 3-2h-3v-1h1c1 0 3 0 4-1l-1-1h2c3 0 5-1 8 0 3 0 6 0 10 1h0v-1h-1c-2 0-3-1-5-1v-1h1 1l1-1 1 1c0 1 1 1 1 2h1 1l9 23h0c-1 0-1-1-2-1-1-2-7-3-10-3v-1l-1-1c-5-1-11 0-16-1-1-1-1-2-1-3 1-2 2-2 4-2l-2-1h-6-1v-1z" class="W"></path><path d="M486 303c0 1 0 2 1 3v1 2h1 1l1-3h1c1-1 4-1 5-1l-1 1c-1 1-1 1-2 3h-1 0c1 2 3 2 5 2 8 0 16-1 23 0l5 1v1h-5c-2 0-4 0-6 1-1 1-1 1 0 3-1 1-1 2-1 2l-2 1h-1l-17 2c-1-1-1 0-2-1-2-1-4 0-6-1v-1h-1v1l1 1-1 1h-1-3l-7 2 1-1v-4l1-1v-1h-2v-1l-1-1 3-1-1-1h1c2 0 5-1 6-2v-2h1v-1l-1-2c1 0 3 0 4-1l-1-1h0 1l1-1z" class="V"></path><path d="M491 306c1-1 4-1 5-1l-1 1c-1 1-1 1-2 3h-1 0c1 2 3 2 5 2l-8 1c0-1 0-2 1-3s1-1 1-3z" class="H"></path><path d="M484 319v-3c2 0 3-1 5 0h1c0-1 0-1 1-2h0l1-1v2c-1 2-1 3-1 6-2-1-4 0-6-1v-1h-1z" class="X"></path><path d="M473 316h3c1 1 1 2 3 2h2 2c0 1 1 2 1 3v-1l1 1-1 1h-1-3l-7 2 1-1v-4l1-1v-1h-2v-1z" class="l"></path><path d="M481 318h2c0 1 1 2 1 3v-1l1 1-1 1h-1-3c-1-1-2-1-2-2 1-1 2-2 3-2z" class="b"></path><path d="M486 303c0 1 0 2 1 3v1 2h1 1l1-3h1c0 2 0 2-1 3s-1 2-1 3l-6 1-8 1-1-1h1c2 0 5-1 6-2v-2h1v-1l-1-2c1 0 3 0 4-1l-1-1h0 1l1-1z" class="I"></path><path d="M486 303c0 1 0 2 1 3v1c-1 2-1 2-2 3-2 0-2 0-3-1v-1l-1-2c1 0 3 0 4-1l-1-1h0 1l1-1z" class="F"></path><path d="M493 315c0-1 0-1 1-2l17-1c3 0 6 0 9 1-2 0-4 0-6 1-1 1-1 1 0 3-1 1-1 2-1 2l-2 1h-1l-17 2c-1-1-1 0-2-1 0-3 0-4 1-6h1z" class="Z"></path><path d="M505 317v-3c1 0 2 0 2 1l1 1c2-1 2-2 3-2 0 2 1 5 2 5l-2 1c-2-1-4 0-6-2v-1z" class="B"></path><path d="M500 315c2 0 3 1 4 1v2l1-1v1c2 2 4 1 6 2h-1l-17 2c-1-1-1 0-2-1 0-3 0-4 1-6h1c1 1 2 0 3 0v1l4-1z" class="F"></path><path d="M500 315c2 0 3 1 4 1v2l1-1v1c-1 1-3 1-4 1l-1-4z" class="M"></path><path d="M538 264c5 0 10 2 14 4h1c1 2 2 3 3 4h0l-1 1-1 2h0 2l2 2h-2c2 4 1 9 1 13v14l-1 6h1-4 0-2l-5-2h-2c-2-1-3-1-4-2v-1l1-3v-24c0-2-1-4-2-6v-3c0-1 0-4-1-5z" class="D"></path><path d="M542 278c1-2 2-3 2-4h1c0 2 1 3 1 5h-2l-2-1z" class="P"></path><path d="M538 264c5 0 10 2 14 4h1c1 2 2 3 3 4h0l-1 1-3-1v1c-1 6 1 30-1 34h-1 0v-35c-1-1-2-1-4-2h-1c-1 0-4 0-5 1v1c-1-1-1-2-1-3s0-4-1-5z" class="b"></path><path d="M542 278l2 1h2v25 1l-5-3v-24h1z" class="T"></path><path d="M550 307h0 1c2-4 0-28 1-34v-1l3 1-1 2h0 2l2 2h-2c2 4 1 9 1 13v14l-1 6h1-4 0-2l-5-2h-2c-2-1-3-1-4-2v-1c1 1 2 1 3 1h1c2 1 3 1 5 1h1z" class="C"></path><path d="M554 306v-3c-1-7-1-14 0-21 0-2 0-4 1-5h1c2 4 1 9 1 13v14l-1 6c-1-1 0-1-1-2-1 0-1-1-1-2z" class="P"></path><path d="M554 306c0-1 0-3 1-4h1l1 2-1 6c-1-1 0-1-1-2-1 0-1-1-1-2z" class="T"></path><path d="M591 434l1 2v1c-1 1-1 1-1 2s-1 2-1 3c-1 3-2 7-3 11-1 3 0 5-1 8l1 2h-1v2 1h0v1 1c-1 4 0 11 0 16l1-1 1 1 3-1c1-1 5-1 7 0-2 1-3 0-5 2h1 4v1c-1 0-2 1-4 1v2c2 0 4 0 6-1h1 2 0c2-1 1-3 1-4h2v1 4c0 1-1 3 0 4v1 4h2v1 1s1 0 1 1l2 1h-1-4l-1-1h-1v4l1 1c-1 2-2 6-1 8h-1l-10 1c-1 0-3 1-3 1l-1-1-1-1c-1 1-3 1-4 1h-1l-2 1v-3c0-2 1-2 0-4s-3 0-5-1l-1-1v1c0-3 5-4 7-5 1 0 2-1 2-1 1-1 0-34 0-38 1-4 1-7 1-10 1-7 3-14 6-20z" class="o"></path><path d="M584 515h0l1-1 1-1c0-1-1 0-1-2l2-1v-1l1 1v4h0c-1 1-3 1-4 1z" class="R"></path><path d="M587 504c2 0 3-1 4 0v2c-2 1-4 1-6 1v-2c1 0 2 0 2-1z" class="Q"></path><path d="M596 507h1l1 1h1c2 1 3 1 3 2v2l-2-1h-1l1-1c-1 0-3 1-5 1h0l-3-1 1-2h1s0 1 1 1l2-1-1-1z" class="n"></path><path d="M590 495h4c0 1 0 2-1 2s-1 0-2 1h-1c-1 0-2 1-3 1v3 1 1c0 1-1 1-2 1 0-2 0-2 1-3 0-1-1-2 0-3 0-1-1-1 0-2s3-1 4-2h0z" class="m"></path><path d="M575 507l9-4c-1 4-1 8-1 12l-2 1v-3c0-2 1-2 0-4s-3 0-5-1l-1-1z" class="c"></path><path d="M595 511h0c2 0 4-1 5-1l-1 1h1l2 1 1 2-10 1c-1 0-3 1-3 1l-1-1-1-1h0 3c1-1 3-2 4-3z" class="U"></path><path d="M595 511h0c2 0 4-1 5-1l-1 1h1c-2 2-6 3-9 3 1-1 3-2 4-3z" class="a"></path><path d="M606 484v1 4c0 1-1 3 0 4v1 4l-3 1h0s-1 1-2 1h0l-1-1c-2-1-5 1-7 0v-2c1 0 1-1 1-2h-4 0c1-1 1-1 3-1 1 0 2-1 2-2h-1-3l-1 1h-3c1-1 1-1 2-1v-1h2l5-1c2-1 5-1 7-2h0c2-1 1-3 1-4h2z" class="L"></path><path d="M606 484v1 4c0 1-1 3 0 4v1 4l-3 1h0c-1-1-2-1-3-2l1-1c2 0 1 0 2-1v-1h-2-1 0l1-1h2v-1h-3c-1 1-1 1-2 1 0-2-1-2-2-3 2-1 5-1 7-2h0c2-1 1-3 1-4h2z" class="F"></path><path d="M587 504v-1-1-3c1 0 2-1 3-1h1c1-1 1-1 2-1v2c2 1 5-1 7 0l1 1h0c1 0 2-1 2-1h0l3-1h2v1 1s1 0 1 1l2 1h-1-4l-1-1h-1v4l1 1c-1 2-2 6-1 8h-1l-1-2v-2c0-1-1-1-3-2h-1l-1-1h-1c-2 0-3 0-4-1h-1v-2c-1-1-2 0-4 0z" class="U"></path><path d="M592 506l4-1c2-1 3-1 5 0v1h1c-1 1-2 1-3 2h-1l-1-1h-1c-2 0-3 0-4-1z" class="l"></path><path d="M606 498h2v1 1s1 0 1 1l2 1h-1-4l-1-1h-1v4l1 1c-1 2-2 6-1 8h-1l-1-2v-2c0-1-1-1-3-2 1-1 2-1 3-2h-1v-1l1-1c0-1 1-1 0-3l-1 1c-1 0-1-1-2 0-1 0-1 0-2-1-1 1-2 1-3 1h-1c1-1 0-1 1-1s2 0 3-1h4 0c1 0 2-1 2-1h0l3-1z" class="G"></path><path d="M604 514h1v1s1 0 2 1c8-1 16-1 24 0-3 1-6 0-9 1l3 1h-19v1 2 4h-1v8 5 3 1 3h2 1l5 1 1 1h2l1 1v8l-1 2c-1 0 0 0-1-1h-6c-1 1-2 1-3 2v-2h-2c-1-1-2-3-3-4l-1-1c-1-1-2-2-3-2l-1-2c1 0 1 0 1-1h-3c-1 0-5 1-6 1v-1c-1-2 0-4 0-6h0c0 2 0 3 1 5v-1c0-2 1-7 0-8l-1 1v-4c0-1 1-1 2-2h-1l1-7-1-1h0 1v-1h-1v-2l1-1-1 1h-3-2l-3-1h-1v-1l10-3s2-1 3-1l10-1h1z" class="b"></path><path d="M607 545h1l5 1 1 1h-5-2l-4 1c-1 0-2 0-3 1 0 0-1-1-2-1l-1 1v1l-1-2c1 0 1 0 1-1h-3c1 0 3 0 4-1l9-1z" class="Q"></path><path d="M607 545h1l5 1 1 1h-5-2l-1-1c-2 1-5 1-8 0l9-1z" class="k"></path><path d="M591 534l5 2-1 1c-1 0-1 0-2 1 0 1 0 1-1 2 0 0 1 0 2 1 0 1 1 1 1 1 1 0 1 1 1 1 0 1 1 2 1 2-2 0-4 1-6 1v-3-3-6z" class="Q"></path><path d="M604 519h0l2-1v1 2 4h-1v8 5h-1c-2 1-3 1-4 1v-1-1l-1 1c-1 1-1 2-2 3 1 0 3-1 4 0h1v1c1 1 1 1 2 1v1c-2 0-5 0-7 1 0 0-1-1-1-2 0 0 0-1-1-1 0 0-1 0-1-1-1-1-2-1-2-1 1-1 1-1 1-2 1-1 1-1 2-1l1-1v-1h1v-1-1c0-1 0-1 1-2 0-1 0-1 1-2 0-1 1-1 1-2l1 1 1-2h1v1h1c1-2 0-5 0-8z" class="a"></path><path d="M600 527l1 1c1 1 1 1 1 2-2 1-3 1-4 1 0-1 0-1 1-2 0-1 1-1 1-2z" class="P"></path><path d="M604 538c-1-1-2-2-2-3l-1-1h1v-2h1s1 0 2 1v5h-1z" class="n"></path><path d="M614 547h2l1 1v8l-1 2c-1 0 0 0-1-1h-6c-1 1-2 1-3 2v-2h-2c-1-1-2-3-3-4l-1-1c-1-1-2-2-3-2v-1l1-1c1 0 2 1 2 1 1-1 2-1 3-1l4-1h2 5z" class="j"></path><path d="M617 548v8l-1 2c-1 0 0 0-1-1h0c-1 0-2-1-3-1l2-2h1v-1l-1-1c0-1 0-1-1-2v-1l4-1z" class="m"></path><path d="M597 550v-1l1-1c1 0 2 1 2 1 1-1 2-1 3-1 1 2 3 2 3 3 2 0 1-1 3-2v1c0 1 1 1 1 2-1 0-2 1-3 1h-1c-1 1-1 1-2 1s-1-1-2-2l-1 1-1-1c-1-1-2-2-3-2z" class="U"></path><path d="M601 553l1-1c1 1 1 2 2 2s1 0 2-1h1c1 0 2-1 3-1 2 1 3 0 4 2l-2 2c1 0 2 1 3 1h0-6c-1 1-2 1-3 2v-2h-2c-1-1-2-3-3-4z" class="o"></path><defs><linearGradient id="S" x1="598.092" y1="509.41" x2="610.344" y2="526.137" xlink:href="#B"><stop offset="0" stop-color="#0f0a0a"></stop><stop offset="1" stop-color="#373938"></stop></linearGradient></defs><path fill="url(#S)" d="M604 514h1v1s1 0 2 1c8-1 16-1 24 0-3 1-6 0-9 1l3 1h-19l-2 1h0c0 3 1 6 0 8h-1v-1h-1l-1 2-1-1c0 1-1 1-1 2-1 1-1 1-1 2-1 1-1 1-1 2v1 1h-1v1l-5-2v-5c-1-1 0-2-1-4l-1-1h0 1v-1h-1v-2l1-1-1 1h-3-2l-3-1h-1v-1l10-3s2-1 3-1l10-1h1z"></path><path d="M604 514h1v1s1 0 2 1c-9 0-18 1-27 4v-1l10-3s2-1 3-1l10-1h1z" class="K"></path><path d="M593 521v-1h1c1 0 1-1 2-1h4v1c2 1 3 0 4-1 0 3 1 6 0 8h-1v-1h-1l-1 2-1-1 1-1c0-1 0-2-1-3 0 1-1 1-2 2-1-2-1-2-2-3h-4l1-1z" class="Q"></path><path d="M593 521h4 5v2s-1 1-2 0c0 1-1 1-2 2-1-2-1-2-2-3h-4l1-1z" class="h"></path><path d="M592 522h4c1 1 1 1 2 3 1-1 2-1 2-2 1 1 1 2 1 3l-1 1c0 1-1 1-1 2-1 1-1 1-1 2-1 1-1 1-1 2v1 1h-1v1l-5-2v-5c-1-1 0-2-1-4l-1-1h0 1v-1h-1v-2l3 1z" class="k"></path><path d="M592 522h4c1 1 1 1 2 3h-2-1l-1 1h-2-1l-1-2v-1h-1v-2l3 1z" class="c"></path><path d="M552 621c1 0 2 0 3 1l-1 1c0 1 1 1 2 1l2 5 1 3v2h1c0-2-1-3-1-5-1-2-1-3-1-4l1-1c1 0 2 0 3 1l1 4c1 3 0 5 0 8v3c1 0 3 2 3 2 6 6 12 11 18 15l2 2c1 0 2-1 3 0 2 2 4 3 7 2l-1 2c1 0 1 0 1 1h7s0 1-1 1c-2 1-3 1-4 3l6 3c0 1 1 1 1 1l1 1h2 1v1 1h1v1c-1 1-1 1-2 1-2 0-3 1-4 2l-1-2c0-1 0 0 1-1l-1-1c-1 1-1 1-2 1-2-2-3-2-5-3l-1-1c0 1-1 2-1 3h0l-1-1c-2-1-3-1-4-3-2-1-5 0-7-1l-1-1-2 1c-1-1-2-1-3 0h0c-1 0-2-1-3-1v-1c-2 0-3 1-4 1h0-1v-2h-1c-2-2-3-5-3-8l-1-5c-1 0-1-1-1-1 0-3-2-5-3-7-3-6-4-11-6-17-1-3-2-5-1-8z" class="I"></path><path d="M559 632l-1 1c-1-1-1-1-2-1h-1v-1c1-1 2-1 3-2l1 3z" class="E"></path><path d="M586 659c1 0 2-1 3 0 2 2 4 3 7 2l-1 2c1 0 1 0 1 1h7s0 1-1 1c-2 1-3 1-4 3l-12-9z" class="R"></path><path d="M563 642c4 2 9 7 12 10l1 1 1 1c1 1 2 1 3 2 9 7 17 14 27 19h0l1-1-2-1h2 1v1 1h1v1c-1 1-1 1-2 1-2 0-3 1-4 2l-1-2c0-1 0 0 1-1l-1-1c-1 1-1 1-2 1-2-2-3-2-5-3l-1-1c0 1-1 2-1 3h0l-1-1c-2-1-3-1-4-3l2-2c0-3-5-6-8-8-2-2-4-4-6-5-5-4-11-8-14-14z" class="X"></path><path d="M591 669l2 1h0l-1-1 1-1 6 3c1 0 1 1 2 2h0c2 1 3 2 5 3l1-1 1-1-2-1h2 1v1 1h1v1c-1 1-1 1-2 1-2 0-3 1-4 2l-1-2c0-1 0 0 1-1l-1-1c-1 1-1 1-2 1-2-2-3-2-5-3l-1-1c0 1-1 2-1 3h0l-1-1c-2-1-3-1-4-3l2-2z" class="Y"></path><path d="M563 654v-1l1-1v-3c-1-1-1-1-1-2 1-3-2-5-3-7 1 0 2 2 3 2 3 6 9 10 14 14 2 1 4 3 6 5 3 2 8 5 8 8l-2 2c-2-1-5 0-7-1l-1-1-2 1c-1-1-2-1-3 0h0c-1 0-2-1-3-1v-1c-2 0-3 1-4 1h0-1v-2h-1c-2-2-3-5-3-8l-1-5z" class="T"></path><path d="M476 266c2-1 5-2 7-1h0l1-1 1-1v1c0 1 0 1-1 2v5 15 4c1 4 1 10 0 14h0l1 1c-1 1-3 1-4 1l1 2-5 1c-2 2-4 2-6 3h-2c-1 0 0 0-1 1h-1c-1 0-2 1-3 1-1 1-1 1-1 2h-1c-1-4 0-9 0-13v-32c1-1 2-1 3-2l4-1 3-1h1c1-1 1-1 3-1z" class="e"></path><path d="M469 268l3-1h0c1 1 1 1 2 1h1l1 2h-1c-2 0-4-1-6-2z" class="m"></path><path d="M465 309c1-9 0-18 0-27 0-2 0-5 1-7l1 1v32 1c-1 1-1 1-2 0z" class="O"></path><path d="M467 276h2v1c2 2 1 7 1 9v22l-2 1-1-1v-32z" class="c"></path><path d="M462 271h1l1 1c0 1 0 1 1 2l-2 1 1 1c1 2 0 5 0 8v17 10 1h0l1-3c1 1 1 1 2 0v-1l1 1 2-1 2 1c2 1 2 0 5 0-2 2-4 2-6 3h-2c-1 0 0 0-1 1h-1c-1 0-2 1-3 1-1 1-1 1-1 2h-1c-1-4 0-9 0-13v-32z" class="X"></path><path d="M470 276h0v-1l-1-1 1-1h1l2-1c1-1 2-1 3 0v2l-1 1h1c0 1 0 2-1 3v9 12 6c1 1 1 1 2 1 1-1 2-1 4-1l3-1 1 1c-1 1-3 1-4 1l1 2-5 1c-3 0-3 1-5 0l-2-1v-22c0-2 1-7-1-9l1-1z" class="Y"></path><path d="M472 307v-1c1-3 1-7 1-10h1v9c0 1 1 2 2 2h2c1-1 2 0 3-1l1 2-5 1c-3 0-3 1-5 0v-2z" class="L"></path><path d="M470 276l3 3c-3 4-1 12-1 16s-1 8 0 12v2l-2-1v-22c0-2 1-7-1-9l1-1z" class="G"></path><path d="M476 266c2-1 5-2 7-1h0l1-1 1-1v1c0 1 0 1-1 2v5 15 4c1 4 1 10 0 14h0l-3 1c-2 0-3 0-4 1-1 0-1 0-2-1v-6-12-9c1-1 1-2 1-3l2-5s0-1 1-1c1-1 2-1 3 0h1v-1c-2-1-4-1-7-1v-1z" class="T"></path><path d="M478 270h2c-1 3-1 3-1 6h-1c-3 4-2 21-2 27v-1c0-1 0-2-1-3v-12-9c1-1 1-2 1-3l2-5z" class="G"></path><path d="M476 266c2-1 5-2 7-1h0l1-1 1-1v1c0 1 0 1-1 2v5 15 4c1 4 1 10 0 14h0l-3 1c-2 0-3 0-4 1-1 0-1 0-2-1v-6c1 1 1 2 1 3v1l1 1 5-1-1-18v-7c0-1-1-2-2-2 0-3 0-3 1-6h-2s0-1 1-1c1-1 2-1 3 0h1v-1c-2-1-4-1-7-1v-1z" class="K"></path><path d="M476 266c2-1 5-2 7-1h0l1-1 1-1v1c0 1 0 1-1 2v5 15 4c-1-5 0-10-1-15 0-2 1-4 0-5h-3-2s0-1 1-1c1-1 2-1 3 0h1v-1c-2-1-4-1-7-1v-1z" class="a"></path><path d="M615 420h5c1 0 2 0 3 1v2h-3l-1 1v2c1 1 0 2 0 3 2 1 3 1 5 1h-4c-1 0-1 1-1 2h-5l-1-1h-2l-1 2c2 1 6 1 9 1-3 2-9 0-13 2v3h0v3 4 3 3l-1 1v5 2 4 4 14l1 2h-2c0 1 1 3-1 4h0-2-1c-2 1-4 1-6 1v-2c2 0 3-1 4-1v-1h-4-1c2-2 3-1 5-2-2-1-6-1-7 0l-3 1-1-1-1 1c0-5-1-12 0-16v-1-1h0v-1-2h1l-1-2c1-3 0-5 1-8 1-4 2-8 3-11 0-1 1-2 1-3s0-1 1-2v-1l-1-2h0c2-3 4-7 6-9 1-1 0-1 1-2h0l2-3c1 2 1 1 1 2h1l1-2 1 1c-1 0 0 0 0 1h0c1 0 3-1 3-2h1 1 2 1 3z" class="e"></path><path d="M611 430l1-5 1-1v4l-1 1c2 1 3 1 5 0h2c2 1 3 1 5 1h-4c-1 0-1 1-1 2h-5l-1-1h-2v-1z" class="B"></path><path d="M606 452l-1 1-1 1c-1 1-2 1-4 1-3 0-7 1-10 2h-2c1-2 8-3 9-4l1-1h2 1c1 0 3-1 5 0z" class="I"></path><path d="M600 420c1 2 1 1 1 2h1l1-2 1 1c-1 0 0 0 0 1h0c1 0 3-1 3-2h1 1 2 1 3 0-2l-1 3c-2 0-2 0-3 1 0 1 0 2-1 4h-1c-1-2 0-3 0-5-2 0-5 1-6 1 0 0 0-1-1-1h-2l2-3z" class="G"></path><path d="M615 420h5c1 0 2 0 3 1v2h-3l-1 1v2c1 1 0 2 0 3h-2v-4h-1l1-1c-1-1-4-1-5-1l1-3h2 0z" class="I"></path><path d="M600 461h-1c-3-1-8 1-11 2 0-1 0-1 1-1 2-1 6-2 8-4-3 1-7 1-9 2h0c4-3 10-3 14-5h2v1s1 1 1 2h-1v2h-3v1h-1z" class="C"></path><path d="M601 461c2 0 2 0 4 1l-1 1h0l1 2c-2 2-15 3-18 4v-1c1 0 2 0 3-1 2 0 4-1 6-2s5 0 7-1v-1c-5 1-10 1-15 3h0v-1c2-1 5-2 7-3l5-1h1z" class="H"></path><path d="M598 447h0 2c2-1 4 0 6-1v3 3c-2-1-4 0-5 0h-1-2c-2 0-6 1-8 2h-1v-1h1 1c3-1 5-2 7-3l-7 1v-1h2c2-1 3-1 5-2v-1z" class="F"></path><path d="M606 431l1-1h4v1l-1 2c2 1 6 1 9 1-3 2-9 0-13 2v3c0-1 0-3-1-4h0-1c-2 0-4 0-7 1v-1-1-1-1c1 0 2-1 3-1l1-1 1 1 2-1c1 1 0 3 1 4h1v-3z" class="S"></path><path d="M606 431h2l1 1-1 1s-1 1-2 1v-3z" class="E"></path><path d="M597 436c3-1 5-1 7-1h1 0c1 1 1 3 1 4h0v3 4c-2 1-4 0-6 1h-2 0l-7 1-1-1c3 0 5-1 8-2h-1c-1 1-2 1-3 0h0l2-2h-4v-1c2 0 3-1 5-1v-1h-3 0l4-2c-1 0-1 0-2-1l1-1h0zm-1 41c-1 0-3-1-4-1s-4 2-5 1c4-2 9-2 12-3h-6c-2-1-4 0-6 0 1-1 8-2 9-3-3 0-5 0-8 1h-1v-1c2-1 4-1 6-2 4-1 7-2 11-2l1 1s0 1-1 2v2c1 3 1 6 1 10h0l1 2h-2c0 1 1 3-1 4h0-2-1c-2 1-4 1-6 1v-2c2 0 3-1 4-1v-1h-4-1c2-2 3-1 5-2-2-1-6-1-7 0l-3 1-1-1 9-3c-1 0-3 0-4-1v-1l4-1z" class="K"></path><path d="M596 477l1 1 1-1h1 1 4v1c-2 0-4 0-5 1h-1c-1 0-1 0-2 1-1 0-3 0-4-1v-1l4-1z" class="H"></path><path d="M624 430c1 0 2 0 2 1 1 4 3 8 3 12l1 14c1 3 1 10 0 12l-2 1h-1l2 2c-1 1-1 0-2 1v2l1 2-2 1c0 1 0 1 1 2v1l-1 1 2 2h-4c0 1 0 2 1 3l-1 1c0 1-1 2 0 3v3h0l-1 1h-4c-1 1-1 2-2 3h-5-3-1-2v-4-1c-1-1 0-3 0-4v-4-1l-1-2v-14-4-4-2-5l1-1v-3-3-4-3h0v-3c4-2 10 0 13-2-3 0-7 0-9-1l1-2h2l1 1h5c0-1 0-2 1-2h4z" class="W"></path><path d="M606 439l1 1h0c1-1 3-1 5-1l11 1h0c-3 0-5 0-8 1-2 1-6-1-8 1h-1v-3z" class="C"></path><path d="M605 460c3-2 7-1 11-1h2v1c-1 0-2 1-3 1l-1 1c2 1 3 0 4 0v1c-1 0-2 1-3 1h-2c-1 1-6 0-8 0v-4zm1-11h4l1 1h-1-1l-1 1c2 0 4 0 6-1 1 0 3 1 4 1l1 1-1 1h-4v2 1 1c-2 1-4 0-6 1h-3v-5l1-1v-3z" class="D"></path><path d="M605 468c2 0 3 1 3 2v2 1h0v1 1h-1v1c1 0 3 0 4-1 1 0 2 1 2 0 1 0 2-1 3-1l2 1v1c0 1 0 1 1 2-2 1-3 1-5 1-2 1-4 1-6 1l1 2h1 6c3 0 6 1 9 0 1-2 0-4 1-5s0-1 1-2l1 2-2 1c0 1 0 1 1 2v1l-1 1 2 2h-4c0 1 0 2 1 3l-1 1c0 1-1 2 0 3v3h0l-1 1h-4c-1 1-1 2-2 3h-5-3-1-2v-4-1c-1-1 0-3 0-4v-4-1l-1-2v-14z" class="J"></path><path d="M620 483h3v1c-1 1 0 3-1 4h-5-2l1-1 4-4z" class="N"></path><path d="M606 489c2-1 5 0 7-1l2 1h0c-2 1-3 1-5 1-1 0-1 0-1 1h-2 1c3 1 2 2 6 1 0 0 1 1 2 1 2 1 5 1 8 1l-1 1h-4c-1 1-1 2-2 3h-5-3-1-2v-4-1c-1-1 0-3 0-4z" class="E"></path><path d="M606 494h8l1 1c-2 1-6-1-7 1l1 2h-1-2v-4z" class="I"></path><path d="M600 420l6-8c1-2 1-2 2-3l2 1v-1c0-1-1-1-2-1l3-6c2 2 5 4 7 7h1c3 3 5 6 8 9s6 5 8 8c5 7 8 15 9 24l1 10c4 2 8 4 10 9 0 3 0 7-2 10-1 2-3 4-6 5h-1v1h-1l-1-1c-2 0-4 0-5 1v1l-1-1c-1 0-2 1-2 2l1 1 1 1h0c-1 0-2 0-3 1v2h-1c0 1-1 2-1 3l-1 1h-1l-1-1c-1 1-1 0-2 1-1 0-1 0-1-1h-4l1-1h0v-3c-1-1 0-2 0-3l1-1c-1-1-1-2-1-3h4l-2-2 1-1v-1c-1-1-1-1-1-2l2-1-1-2v-2c1-1 1 0 2-1l-2-2h1l2-1c1-2 1-9 0-12l-1-14c0-4-2-8-3-12 0-1-1-1-2-1-2 0-3 0-5-1 0-1 1-2 0-3v-2l1-1h3v-2c-1-1-2-1-3-1h-5-3-1-2-1-1c0 1-2 2-3 2h0c0-1-1-1 0-1l-1-1-1 2h-1c0-1 0 0-1-2z" class="a"></path><path d="M637 488l1 1h0c-1 0-2 0-3 1v2h-1c0 1-1 2-1 3l-1 1h-1l-1-1c-1 1-1 0-2 1-1 0-1 0-1-1h-4l1-1h0v-3l3 2h5v-1l1-1v-1c1-1 2-2 3-2h1z" class="F"></path><path d="M627 481c0-1 1-2 1-2h2l1-1-2-1c0-2 2-3 3-4h1c0 2 0 4 1 6 0 0 2 3 2 4-1 2-2 2-3 2l-1-1h-1-3 0l-2-2 1-1z" class="f"></path><path d="M640 470l-2-1-1 1h-1c1-1 0-1 1-2h0-1l-2 2-1-1c1 0 1-1 1-2v-1c1-3 4-4 6-6 5 1 6 1 10 4h-1-4-1-1c0 1-1 2-1 2h-3v2l1-1 1 1c-1 1-1 1-1 2h0z" class="E"></path><path d="M621 419l-8-1h-2l-1-2-1 2c-2 0-3 1-4 0 1-1 1-2 2-3v-1c2 0 2 0 3 1l1-2c3 0 7-1 10 1l1 4-1 1z" class="W"></path><path d="M621 414l2 1c3 3 11 11 12 15l1 2c-3-1-5-2-8-2h0l-2 1c0-1-1-1-2-1-2 0-3 0-5-1 0-1 1-2 0-3v-2l1-1h3v-2-2h-2l1-1-1-4z" class="C"></path><path d="M619 429c0-1 1-2 0-3v-2l1-1h3v1c0 2-1 3 1 5l1-1c1-1 0-2 0-4 1 0 1 0 2 1h0c-1 1 0 2 1 3h0l1-1-1-1v-1c2 0 2 0 3 2 0 1 0 0-1 1 1 1 3 1 5 2l1 2c-3-1-5-2-8-2h0l-2 1c0-1-1-1-2-1-2 0-3 0-5-1z" class="c"></path><path d="M640 470h0c0-1 0-1 1-2l-1-1-1 1v-2h3s1-1 1-2h1 1 4 1c3 3 4 5 4 9 0 3-1 5-3 7-1 1-2 2-4 1h0 0 1c2-1 3-3 4-5v-1c-2 1-1 2-3 3h-1v-1h-1-2l1-1-1-1-1-1h-4v-1-1-2zm-12-40h0c3 0 5 1 8 2h0c3 4 5 9 6 14 1 4 1 9 2 13-4 1-8 2-11 5l-1 3c0-10-1-21-3-31l-1-6z" class="W"></path><path d="M628 430h0c3 0 5 1 8 2v1c-1 1-2 2-3 2h0-3c-1 0-1 1-1 1l-1-6z" class="N"></path><path d="M478 240c2 1 4 1 7 0 3 0 7 0 11-1 20-1 42-2 61 7 1 0 1 0 2 1v-1c1 0 1 0 2 1l-1 1h-1l1 1-1 1c-1-1-2-1-4-2h0l-5-2 2 1c-1 1-1 2-1 3h0v4c1 1 1 1 3 1 1 0 2 0 4 1h0c1 0 2 1 2 1v1h-2v-1h-2 0l-8-2c-2-1-3-1-4-1l-6-1c-3 0-5 0-8-1h-1l1 1v1l-11-1 2 2h-2c-3-1-12-1-15 0v3h-1c-3 0-7 0-10 1-7 1-14 2-21 4-3 1-7 3-11 4 0 0-1 1-2 0h0c-1-2-1-12 0-14 2-4 14-7 18-9l-8 1c4-2 8-2 11-3-3-1-7 0-11 1l2-2c2-1 4-1 7-1z" class="M"></path><path d="M488 245h3v8l-1 1-1-1c0-1 0-2-1-3v-1-4z" class="J"></path><path d="M480 247h2c1 0 2 0 3-1 1 0 2-1 3-1v4h-2c-2 1-2 1-4 1-1-1-1-1-2-3z" class="F"></path><path d="M502 252c9-1 18 0 27 0l1 1v1l-11-1h-6-6c-2 0-4 0-5-1z" class="B"></path><path d="M514 245v-2h5l11 1v7l-12-1h-2c-1 0-2 0-3-1 1-1 1-2 1-4zm18-2h1c0 1 1 1 2 1l15 3v7l-12-2c-2-1-5 0-7-1 0-3 0-5 1-8z" class="W"></path><path d="M461 263v-4h0l1-1-1-1c1-2 1-2 1-3h0c0-1 1-2 2-2 0 1 0 1 1 1h0v-2l1 1h0l2-2h2c1-1 1-1 2-1s2 0 3-1h0c2 0 3 0 4-1h1c1 2 1 2 2 3 2 0 2 0 4-1h2v1c1 1 1 2 1 3-1 0-2 0-3 1l-7 1c-1 1-2 1-3 2h1c-2 1-3 1-4 1-1 1-2 1-3 1 0 0-1 1-2 1h-1c-2 1-3 1-5 2l-1 1z" class="G"></path><path d="M488 250c1 1 1 2 1 3-1 0-2 0-3 1l-7 1c-1 1-2 1-3 2l-2-1v-3c2 0 3-1 5-2h0c3 0 6-1 9 0v-1z" class="H"></path><path d="M490 254l1 1 2-1-1-1h1c3 0 5 0 9-1 1 1 3 1 5 1h6 6l2 2h-2c-3-1-12-1-15 0v3h-1c-3 0-7 0-10 1-7 1-14 2-21 4-3 1-7 3-11 4v-1l2-2h-2v-1l1-1c2-1 3-1 5-2h1c1 0 2-1 2-1 1 0 2 0 3-1 1 0 2 0 4-1h-1c1-1 2-1 3-2l7-1c1-1 2-1 3-1l1 1z" class="F"></path><path d="M503 258c-1-1-2-1-2-2 1-1 1-1 3-1v3h-1z" class="I"></path><path d="M463 264s1-1 2-1c0-1 0-1 1-1h1c1-1 1-1 2-1h2l2-1h1c5-2 12-4 17-4h3c0 1 0 1-1 2v1c-7 1-14 2-21 4-3 1-7 3-11 4v-1l2-2z" class="G"></path><path d="M518 250c-2 0-5 1-7 0 0-1 0-3-1-4l-1 1 1 1v2l-18 1v-6l10-1c3 0 7-1 9 0v-1-1l-15 1c-3 0-6 0-9 1-1 0-1 0-2-1v-1l26-2c9-1 20 0 29 2 6 1 11 2 16 5l-1 1-5-2c-2-2-5-2-8-2-2-1-7-2-9-1h-1c-2-1-4-1-5-1h-13c-1 1-1 1-1 3h1c0 2 0 3-1 4 1 1 2 1 3 1h2z" class="D"></path><path d="M639 485c1-1 3-1 5-1l1 1h1c0 3-1 15 1 17h1v1l1 1c1-1 1-1 1-2 1 0 2 1 4 1v1l-1 2h0l3 2v8c0 2 1 4 0 5-1 4-10 8-13 9-1 4-1 10-1 14 0 1 1 3 0 5h-1c-9-5-22-5-33-4h-1-2v-3-1-3-5-8h1v-4-2-1h19l-3-1c3-1 6 0 9-1-8-1-16-1-24 0-1-1-2-1-2-1v-1h-1c-1-2 0-6 1-8l-1-1v-4h1l1 1h4 1l-2-1c0-1-1-1-1-1v-1-1h1 3 5c1-1 1-2 2-3h4 4c0 1 0 1 1 1 1-1 1 0 2-1l1 1h1l1-1c0-1 1-2 1-3h1v-2c1-1 2-1 3-1h0l-1-1-1-1c0-1 1-2 2-2l1 1v-1z" class="O"></path><path d="M614 535h10 1l1 2c-1 0-2 0-2 1-1-1-2-1-3-1-3-1-5-1-8 0-2-1-3-1-5-1v-1h4 2z" class="K"></path><path d="M610 532h3c0 2 0 2 1 3h-2-4v1c2 0 3 0 5 1h-1c-1 1-4 1-5 1-1-2 0-4-1-5 2-1 3-1 4-1z" class="l"></path><path d="M610 532h3c0 2 0 2 1 3h-2l-2-1v-2z" class="Y"></path><path d="M613 532h11v1l1 1v1h-1-10c-1-1-1-1-1-3z" class="L"></path><path d="M625 534l1-2h1c1 2 1 3 1 5h0l1-3c1 1 1 2 1 3 1 1 5 1 6 2h1 4l-1 4v1l-1-1c-2-1-5-2-7-3h0-3c-1-1-1-1-2-1 0 2 0 3-1 4h0v-6l-1-2v-1z" class="S"></path><path d="M628 523c1 2 0 3 0 5h1 1 1 0c3 1 6 2 9 2v1l-2 2 3 1v1c0 2-1 3 0 4h-4-1c-1-1-5-1-6-2 0-1 0-2-1-3l-1 3h0c0-2 0-3-1-5h-1l-1 2-1-1v-1h-11-3 1c0-1 1-1 1-2h5c2-1 4-1 5-1h1c-1-1-1-1-1-2l1-1 1 1 2 2v-1-1l2-4z" class="l"></path><path d="M629 534l-1-1c1-1 1-1 1-2v-1h1v2c3 0 5 0 8 1l3 1v1c0 2-1 3 0 4h-4-1c-1-1-5-1-6-2 0-1 0-2-1-3z" class="L"></path><path d="M641 535c0 2-1 3 0 4h-4v-1-1c-1-2-3 0-4-2h8z" class="Z"></path><path d="M606 518h19 8v1h-2c-1 0-1 0-2 1h-2l1 1v2l-2 4v1 1l-2-2-1-1-1 1c0 1 0 1 1 2h-1c-1 0-3 0-5 1h-5c0 1-1 1-1 2h-1c-1 0-2 0-4 1l1-3c-1-2-1-4-1-5v-4-2-1z" class="o"></path><path d="M606 519c1 1 3 1 4 1v1c1 1 3 0 3 0h2 2v2h-6l-1 1-1 2h-1v1 2l-1 1c-1-2-1-4-1-5v-4-2z" class="Q"></path><path d="M624 522l1-2h2l1 1v2l-2 4v1 1l-2-2-1-1c-1 0-1 0-2-1-3 1-8 0-12 1l1-2 1-1h6v-2c2 0 3 0 5 1h2z" class="m"></path><path d="M610 524c3 1 10-1 12 1h-1c-3 1-8 0-12 1l1-2z" class="f"></path><path d="M624 522l1-2h2l1 1v2l-2 4h-1v-3c-1 0-1-1-1-2z" class="k"></path><path d="M609 526c4-1 9 0 12-1 1 1 1 1 2 1l-1 1c0 1 0 1 1 2h-1c-1 0-3 0-5 1h-5c0 1-1 1-1 2h-1c-1 0-2 0-4 1l1-3 1-1v-2-1h1z" class="a"></path><path d="M612 530l1-2c1-1 1-2 3-2 1 1 1 2 1 4h-5z" class="V"></path><path d="M627 510v4c2 1 8 0 11 1 2 0 3 1 5 0l1-1 5 2c2 0 4 1 5 2 2 2 2 2 2 3-1 4-10 8-13 9-1 4-1 10-1 14 0 1 1 3 0 5h-1c0-2-1-4-1-5h-1c-1-1-2-1-3-1-2-1-3-1-4-3 2 1 5 2 7 3l1 1v-1l1-4c-1-1 0-2 0-4v-1l-3-1 2-2v-1c-3 0-6-1-9-2h0-1-1-1c0-2 1-3 0-5v-2l-1-1h2c1-1 1-1 2-1h2v-1h-8l-3-1c3-1 6 0 9-1-8-1-16-1-24 0-1-1-2-1-2-1v-1c6-1 14-1 20-1l1-1 1-2z" class="o"></path><path d="M633 524l2 2 3 1h-10c2-1 3-2 5-3z" class="m"></path><path d="M635 526v-1c3 0 4 0 7 2l-1 2h0l-1-1c-1-1-1-1-2-1l-3-1z" class="P"></path><path d="M638 523c2-1 1-1 3-2 1 0 3 0 4 1v2 1l-1 1c-1 0-1 0-2 1-3-2-4-2-7-2v1l-2-2c2-1 3-1 5-1z" class="f"></path><path d="M633 518c2 0 5 0 7 1h2c1 0 0 0 2 1h2c1 1 1 1 1 3l-2 1v-2c-1-1-3-1-4-1-2 1-1 1-3 2l1-1-1-1h-3l-2 1c-1 0-2 1-3 1l1-2v-1c-1 0-2 1-3 1l-1-1h2c1-1 1-1 2-1h2v-1z" class="R"></path><path d="M643 515l1-1 5 2c2 0 4 1 5 2 2 2 2 2 2 3-1 4-10 8-13 9h0c1-4 7-6 10-8h-1v-1c-1 1-2 1-3 1-1 1-2 2-3 2l-1 1v-1l2-1c0-2 0-2-1-3h-2c-2-1-1-1-2-1h-2c-2-1-5-1-7-1h-8l-3-1c3-1 6 0 9-1 8 0 15 2 23 5v-1c-3-3-7-4-11-5z" class="e"></path><path d="M627 510v4c2 1 8 0 11 1 2 0 3 1 5 0 4 1 8 2 11 5v1c-8-3-15-5-23-5-8-1-16-1-24 0-1-1-2-1-2-1v-1c6-1 14-1 20-1l1-1 1-2z" class="H"></path><path d="M639 485c1-1 3-1 5-1l1 1h1c0 3-1 15 1 17h1v1l1 1c1-1 1-1 1-2 1 0 2 1 4 1v1l-1 2h0l3 2v8c0 2 1 4 0 5 0-1 0-1-2-3-1-1-3-2-5-2l-5-2-1 1c-2 1-3 0-5 0-3-1-9 0-11-1v-4l-1 2-1 1c-6 0-14 0-20 1h-1c-1-2 0-6 1-8l-1-1v-4h1l1 1h4 1l-2-1c0-1-1-1-1-1v-1-1h1 3 5c1-1 1-2 2-3h4 4c0 1 0 1 1 1 1-1 1 0 2-1l1 1h1l1-1c0-1 1-2 1-3h1v-2c1-1 2-1 3-1h0l-1-1-1-1c0-1 1-2 2-2l1 1v-1z" class="d"></path><path d="M633 499h1l2 2h0 2 3 1v2c-1 1-3 1-4 1l-1-2h-1-1-3-3v-2l4-1z" class="G"></path><path d="M646 508l1-1c2 0 3 0 4 1 2 2-2 7 3 8 1 1 1 1 0 2-1-1-3-2-5-2l-5-2c0-1 1-2 1-4-1 1-1 1-3 1v-2h1c2 0 2-1 3-1z" class="l"></path><path d="M646 508l1 2 2-1 1 1c-1 1-1 2-2 3-1 0-1 0-2-1l-1 2h1 2c0 1 1 1 1 2l-5-2c0-1 1-2 1-4-1 1-1 1-3 1v-2h1c2 0 2-1 3-1z" class="b"></path><path d="M639 485c1-1 3-1 5-1l1 1-1 9v8c-1 0-1 0-2-1h-1-3-2 0l-2-2h2l-1-1c-2-1-2-1-3-2l1-1c0-1 1-2 1-3h1v-2c1-1 2-1 3-1h0l-1-1-1-1c0-1 1-2 2-2l1 1v-1z" class="C"></path><path d="M639 485c1-1 3-1 5-1v4h-3v-1l-2-2z" class="E"></path><path d="M634 492h1c2 1 5 1 7 1h2v3h-2c-3-1-5-1-8-1h-1c0-1 1-2 1-3z" class="J"></path><path d="M633 495h1c3 0 5 0 8 1h2v3l-1 1c-2-2-4-1-7-1l-1-1c-2-1-2-1-3-2l1-1z" class="i"></path><path d="M627 503v-4l2 1v2h3 3 1v1c1 2 0 3 1 5v2h0v-2c2-1 2 0 4 0l1 1v2c2 0 2 0 3-1 0 2-1 3-1 4l-1 1c-2 1-3 0-5 0-3-1-9 0-11-1v-4-3-4z" class="Z"></path><path d="M632 502h3c0 2 0 2-1 3s-1 1-2 0v-2-1zm-5 5l2 1s-1 1-1 2 1 2 1 2h1 0c0-1 0-1-1-1 1-1 1-1 2-1s1 0 2-1c0 2-1 3 1 4h2 1 0 1l2-1 1 1 1-2c2 0 2 0 3-1 0 2-1 3-1 4l-1 1c-2 1-3 0-5 0-3-1-9 0-11-1v-4-3z" class="o"></path><path d="M617 498c1-1 1-2 2-3h4 4c0 1 0 1 1 1 1-1 1 0 2-1l1 1h1c1 1 1 1 3 2l1 1h-2-1l-4 1-2-1v4 4 3l-1 2-1 1c-6 0-14 0-20 1h-1c-1-2 0-6 1-8l-1-1v-4h1l1 1h4 1l-2-1c0-1-1-1-1-1v-1-1h1 3 5z" class="Y"></path><path d="M627 503v4 3l-1 2v-2l-1-6 1-1h1z" class="K"></path><path d="M605 506l1-1 1 1h1 0c1-1 3 0 5 0l11-1 1 1-1 2c-1 0-2 0-3-1l-2 2c-2-2-4-2-7-1-2 0-3 0-4-1l-3 2v-3h0z" class="G"></path><path d="M611 502v-1l1-1 1 1-1 1h3c1-1 1 0 1-2l1 1v1h8l1 1-1 1h0-1c-3-1-6 1-9 0-1-1-2 0-3 0h-6c0 1 0 1-1 2h0 0l-1-1v-4h1l1 1h4 1z" class="a"></path><path d="M605 506h0v3 1h3 2c3 1 6 0 9 0h7v2l-1 1c-6 0-14 0-20 1h-1c-1-2 0-6 1-8z" class="j"></path><path d="M617 498c1-1 1-2 2-3h4 4c0 1 0 1 1 1 1-1 1 0 2-1l1 1h1c1 1 1 1 3 2l1 1h-2-1l-4 1-2-1v4h-1l-1-1h-8v-1l-1-1c0 2 0 1-1 2h-3l1-1-1-1-1 1v1l-2-1c0-1-1-1-1-1v-1-1h1 3 5z" class="F"></path><path d="M617 498c1-1 1-2 2-3h4 4c0 1 0 1 1 1 1-1 1 0 2-1l1 1h1c1 1 1 1 3 2l1 1h-2-1c-2 0-5-1-7-1h-9z" class="J"></path><path d="M626 498h2l1-1c1 1 4 1 6 1l1 1h-2-1c-2 0-5-1-7-1z" class="E"></path><path d="M497 427h11c0 1 0 1 1 2v1l6-1v-2h4 3c1 1 3 0 4 1h-2v1h1l1 1h-2v1l1 2c-1 0-2 0-3 1s-4 0-6 0h-13l-1 1c8 0 16-1 24 1h8c2 1 3 0 5 1 1 0 3 0 5 1h3l3 1 10 3h1c1 0 1 0 2 1h-3-1c-1-1-2-1-3-1s-2-1-2-1h-1-1-2v-1h-2l-2-1h-5l-1-1h-2v1c-2 0-5-1-7-1l-15-1c-9 0-19 1-28 2v2h1l1 1c-1 1-3 1-4 2h0 1v1h6l-1 1c-2 0-3 0-5 2-2 1-4 1-6 2h3 1c1 0 1 0 2-1 0 0 1 0 2 1h1c2-1 4-1 6-1-2 1-4 2-6 2-1 2-3 1-4 1v2c-1 1-2 1-4 1 0 1-1 1-1 1v1c1-1 2-1 4-1 0-1 2 0 2 0h1 4c0 1-1 1-1 2l-6 1h-1c-1 0-3 1-4 1h-2 0c-2 0-2 0-4 1l-3 2h0 1 2v1c-2 1-3 2-5 2l-1 1-2 1 9-2c-2 2-4 3-7 3l1 1h7-1c-2 1-7 1-8 3h2c2-1 3-1 4 0 0 1-2 1-2 2 1 1 2 0 3 1h0l1 1c1 0 4-1 5 0l-2 2h1 1 3l-1 6 1 1h0-3 0l2 2v1h3 2l3 1h1c0 5 1 22 0 25h-1-9l1-7c-2 1-3 1-5 3l2 1v1h-1c-2 0-3 0-5 1h1c-2 1-10 2-12 2-1-1 0-9-1-11h0c-1-3 0-7 0-10v-36-13c-1-3-1-6 0-10l1-1c0-1 0-2-1-3 2-2 2-2 4-3l1-1h4l-2 2c-2 0-3 0-5 1v1c2 0 2 0 3-1 2 0 3-1 5-1l1 1v-1h2l1 1h1l1 1h0c2 0 3 0 4-1 1 0 1 0 1-1l-1-1c-1 1-1 1-2 1l1-2c2 0 2 0 3 2v-1h0v-1h3l-1 1v1h1l1-1h2l1-1h2l1 1c0-1 1-1 2-1v1h1 0c1-1 1-1 1-2h0z" class="R"></path><path d="M480 428c2 0 2 0 3 2v-1h0v-1h3l-1 1v1h1l1-1h2l1-1 1 2h0c-1 0-2 1-3 1s-1 1-2 1l-3 1h-4 0c-2 0-4 1-6 1l-1-1-1 1-2-1h1 0 1c2 0 3-1 4-2h1l1 1h0c2 0 3 0 4-1 1 0 1 0 1-1l-1-1c-1 1-1 1-2 1l1-2z" class="O"></path><path d="M474 502v2h2l-1 2v1c1 1 1 2 1 4h1l2 1v1h-1c-2 0-3 0-5 1l-1-1c0-1 1-1 1-2h-3-2 0v-1c1 0 2-1 4-1l1-1c-1-1-7 0-9 0v-1h5l1-1c1 0 2 0 4-1-1 0-2 0-3-1 1 0 1-1 3-2z" class="F"></path><path d="M474 459c-1-1-7 0-9 0 2-1 6-1 8-2-1 0-2-1-3-1l1-1c1 0 2 0 3-1h-2l-1 1-1-1 1-1c2 0 4-1 6-1v1h8v1h-1c-1 0-2 0-4 1-1 1-1 1-2 1l-3 1v1h2c1-1 2-1 2 0h0c-1 1-3 1-5 1z" class="I"></path><path d="M470 494l2-1 1 2h1l-1 1c-1 1-1 1-1 3h1v1h-1v1h2v1c-2 1-2 2-3 2 1 1 2 1 3 1-2 1-3 1-4 1-3 0-5 0-7-1l1-1h2c0-1 1-1 1-3h-3l-1-1c2 0 4 0 7-1h-1-4c-1 0-1 0-2-1 2-1 5 0 7-1v-1h-6-1c2-2 5 0 7-2z" class="V"></path><path d="M483 440v1c2 0 3 0 4 1-1 1-1 1-3 1l-1 1h0c1 1 3 2 5 2-2 2-3 2-5 3h-1-1 0-2c-3-1-7 0-9 0 1-1 3-1 5-2 1 0 2 0 3-1l-8 1c2-3 9-4 12-5-2 0-7 2-9 1 2-2 7-2 10-3z" class="H"></path><path d="M483 440l5-1v2h1l1 1c-1 1-3 1-4 2h0 1v1h6l-1 1c-2 0-3 0-5 2-2 1-4 1-6 2h3 1c1 0 1 0 2-1 0 0 1 0 2 1h1c2-1 4-1 6-1-2 1-4 2-6 2-1 2-3 1-4 1v2c-1 1-2 1-4 1 0 1-1 1-1 1v1c1-1 2-1 4-1 0-1 2 0 2 0h1 4c0 1-1 1-1 2l-6 1h-1c-1 0-3 1-4 1h-2 0c-2 0-2 0-4 1h-5v-1c2 0 4 0 5-1 2 0 4 0 5-1h0c0-1-1-1-2 0h-2v-1l3-1c1 0 1 0 2-1 2-1 3-1 4-1h1v-1h-8v-1l1-1h-7c2-1 5-2 8-2h2 0 1 1c2-1 3-1 5-3-2 0-4-1-5-2h0l1-1c2 0 2 0 3-1-1-1-2-1-4-1v-1z" class="J"></path><path d="M474 483h-8l-1-1c3 0 6 0 8-1l-3-1h0c1-1 3-1 4-2h0-3v-1h0l3-1 1 1c1 0 4-1 5 0l-2 2h1 1 3l-1 6 1 1h0-3 0l2 2v1h3 2l3 1h1-9c0 1 0 1-1 2v1c-1 1-3 2-4 2 0-1 0-2-1-3h-1l1-2c-1-1-4 0-5 0l-1-1c2 0 2 0 4-1-1-1-2 0-3 0v-1c1-1 2-1 3-1v-1h-7v-1l7-1z" class="E"></path><path d="M474 483c2 1 2 1 4 0h3l1 1c-1 0-1 0-2 1h-1c-1 0-4-1-5 0h-7v-1l7-1z" class="J"></path><path d="M470 494h-4v-1h3c2 0 3-1 4-2-2-1-2 0-3 0-2 1-5 1-7 0h1c2 0 4 0 5-1-1-1-3-1-5-1v-1h5c0-1 0-1 2-1v1c1 0 2-1 3 0-2 1-2 1-4 1l1 1c1 0 4-1 5 0l-1 2h1c1 1 1 2 1 3 1 0 3-1 4-2v-1l1 16c-2 1-3 1-5 3h-1c0-2 0-3-1-4v-1l1-2h-2v-2-1h-2v-1h1v-1h-1c0-2 0-2 1-3l1-1h-1l-1-2-2 1z" class="I"></path><path d="M477 496l1-1 1 2c1 0 1 0 2 1h-2l-1 1c-1 0-2 0-3-1l2-2z" class="S"></path><path d="M481 492c1-1 1-1 1-2h9c0 5 1 22 0 25h-1-9l1-7-1-16z" class="T"></path><path d="M497 427h11c0 1 0 1 1 2v1l6-1v-2h4 3c1 1 3 0 4 1h-2v1h1l1 1h-2v1l1 2c-1 0-2 0-3 1s-4 0-6 0h-13-5c-5 0-9 0-13 1s-9 2-13 2c-2 0-3 1-5 2 1-1 1-2 2-3 1 0 2-1 4-1h1 1 1c1 0 2-1 2-1 2 0 3 0 4-1h1l3-1c1 0 1-1 2-1s2-1 3-1h0l-1-2h2l1 1c0-1 1-1 2-1v1h1 0c1-1 1-1 1-2h0z" class="G"></path><path d="M509 430l6-1v2c-1 1-3 1-4 1l-1-1-1-1z" class="B"></path><path d="M519 427h3v4 1h0c-2 0-4 0-6-1l1-3 2-1z" class="J"></path><path d="M497 427h11c0 1 0 1 1 2v1l1 1 1 1c-4 0-8-1-12 0h-3c-2-1-7 0-10 0 1 0 1-1 2-1s2-1 3-1h0l-1-2h2l1 1c0-1 1-1 2-1v1h1 0c1-1 1-1 1-2h0z" class="I"></path><path d="M508 261h15c5 0 11 0 14 2l1 1c1 1 1 4 1 5v3c1 2 2 4 2 6v24l-1 3v1c1 1 2 1 4 2h2l5 2h2 0c1 1 2 2 4 2l1 1c-1 1-1 2-3 3h-1-1l2 1-1 1-4-1c-2-1-4-1-6-1h-1-3l-4-1-11-2v-1l-5-1c-7-1-15 0-23 0-2 0-4 0-5-2h0 1c1-2 1-2 2-3l1-1c-1 0-4 0-5 1h-1l-1 3h-1-1v-2-1c-1-1-1-2-1-3l-1 1h-1c1-4 1-10 0-14v-4-15-5c1-1 1-1 1-2v-1h3c2 0 3 0 6-1 2 0 5 0 7-1h7z" class="N"></path><path d="M525 298v2c1 0 1 1 1 1 2 1 5 1 7 2l-2 2c-2 0-4 0-6-1-1-1 0-4 0-6z" class="Q"></path><path d="M535 273l1 1v32l-5-1 2-2h0v2h2c1-2 0-10 0-12v-20z" class="Y"></path><path d="M519 266c0-2 0-2 1-3 1 1 1 1 2 0h-1l1-1h1c0 1 1 4 0 5v2 1c1 1 0 2 0 3 0 0-1-1-1-2h-1c-1 1-1 3-2 5v-2-8z" class="J"></path><path d="M533 286h0c1 4-1 10 1 13-1 2-1 2-1 3s0 1 1 2v-2c0-1 0 0 1-1 0-1-1-8 0-8 0 2 1 10 0 12h-2v-2h0c-2-1-5-1-7-2 0 0 0-1-1-1h8v-14z" class="B"></path><path d="M518 273l1 1v2 8 20c-3 1-7 0-11 0h-2c0-1 0-1 1-2l11 1v-30z" class="Y"></path><path d="M493 309c12-1 23-1 35 1-3 1-5 1-8 1-7-1-15 0-23 0-2 0-4 0-5-2h0 1z" class="I"></path><path d="M523 261c5 0 11 0 14 2-1 2-1 6-1 8v3l-1-1v-5l-12-1c1-1 0-4 0-5v-1z" class="b"></path><path d="M527 266c-1 0-2 1-2 1l-1-2 1-1h4l-2 2z" class="M"></path><path d="M529 264c1 0 2 0 3 1v1h-5l2-2z" class="L"></path><path d="M528 310l11 2c5 1 11 1 15 4h-1l2 1-1 1-4-1c-2-1-4-1-6-1h-1-3l-4-1-11-2v-1l-5-1c3 0 5 0 8-1z" class="X"></path><path d="M528 310l11 2h-2l-1 1c1 0 1 0 2 1-2 0-6-1-8-2h-5l-5-1c3 0 5 0 8-1z" class="Y"></path><path d="M508 261h15v1h-1l-1 1h1c-1 1-1 1-2 0-1 1-1 1-1 3v8l-1-1v-5c-4-1-8 0-12-1 0-1 1-2 0-3h-3l-1 7c0-2 0-3-1-5v-2h-2c1-1 2-2 2-3h7z" class="l"></path><path d="M508 265c3-2 7-1 10 0v1h-9 0l-1-1z" class="O"></path><path d="M508 261h15v1h-1l-1 1h1c-1 1-1 1-2 0-1 1-1 1-1 3h-1v-1c-3-1-7-2-10 0v-4z" class="Y"></path><path d="M525 298v-18c0-2 0-4 2-6h2l3 3c1 3 0 6 1 9v14h-8v-2zm-14-25l2 1c1 0 1 1 2 2 1 2 1 21 0 23h-1-6c-1-1-1-19 0-22 0-2 1-3 3-4z" class="T"></path><path d="M485 263h3c2 0 3 0 6-1 2 0 5 0 7-1 0 1-1 2-2 3h2v2c1 2 1 3 1 5v34c-1 1-1 1-2 1h-2-3l1-1c-1 0-4 0-5 1h-1l-1 3h-1-1v-2-1c-1-1-1-2-1-3l-1 1h-1c1-4 1-10 0-14v-4-15-5c1-1 1-1 1-2v-1z" class="B"></path><path d="M487 280c1 1 1 2 1 4 0 3-1 7 0 10s1 4 1 7h0c2-4 0-10 1-14l1 14c2 0 4 0 6-1l1-3v4h-3c1 0 2 0 3 1h0v1 1l-2 1c-1 0-4 0-5 1h-1l-1 3h-1-1v-2-1-26z" class="S"></path><path d="M490 306v-3c2-1 5-1 7-1l1 1v1l-2 1c-1 0-4 0-5 1h-1z" class="a"></path><path d="M490 287l1-10c0-2 1-3 2-4h2c1 1 2 2 2 3 1 1 0 3 0 4l1 11c0 2-1 4 0 6l-1 3c-2 1-4 1-6 1l-1-14z" class="T"></path><path d="M485 263h3c2 0 3 0 6-1 2 0 5 0 7-1 0 1-1 2-2 3h2v2c1 2 1 3 1 5v34c-1-2-1-33-1-37-4-1-7 0-10 1-1 1-1 1-2 0v-4h-1v1c-1 4-1 10-1 14v26c-1-1-1-2-1-3l-1 1h-1c1-4 1-10 0-14v-4-15-5c1-1 1-1 1-2v-1z" class="V"></path><path d="M499 264h2v2c-2 1-7 0-9 0 2-1 5-1 7-2z" class="b"></path><path d="M484 271l1-3h1v35l-1 1h-1c1-4 1-10 0-14v-4-15z" class="C"></path><path d="M488 439c9-1 19-2 28-2l15 1c2 0 5 1 7 1 8 1 17 3 25 6v62 17c0 3 0 6-1 9h0l-2-1-3 3-1-1 1-1 1-1h-1c-1 1-1 1-1 2h-1c-1 0-2-1-4-1h-2v-3l-1 3c-3-1-7-1-9-2v-2h-1v1 1c-2 1-5 0-7-1l-1-2v2h-5c-2-1-1-1-2-2l-1 2h-2v-3h0l-1 3h-1l-2-2c0 1 0 1-1 2l-2-2-1 1h0c0-1 0-2-1-4-1 1 0 3-1 5-1 0-5-2-5-2-1 0-1 2-3 1-2 0-1-3-2-4l-1 1v2 1h-1v-1-1c-1 1-1 2-2 2-1 1-3 1-4 0v-1c-1 0-1 0-2 2v1h-2l-1-1-1-1 2-1h-2 0c-1 0-1-1-2-2 0-1-1-1-2-2 1 0 2 0 3-1 1 0 3 0 4-1h1l-1-1h-1c1-1 2-2 3-2l2-1-1-1h-2v-2h1c1-3 0-20 0-25h-1l-3-1h-2-3v-1l-2-2h0 3 0l-1-1 1-6h-3-1-1l2-2c-1-1-4 0-5 0l-1-1h0c-1-1-2 0-3-1 0-1 2-1 2-2-1-1-2-1-4 0h-2c1-2 6-2 8-3h1-7l-1-1c3 0 5-1 7-3l-9 2 2-1 1-1c2 0 3-1 5-2v-1h-2-1 0l3-2c2-1 2-1 4-1h0 2c1 0 3-1 4-1h1l6-1c0-1 1-1 1-2h-4-1s-2-1-2 0c-2 0-3 0-4 1v-1s1 0 1-1c2 0 3 0 4-1v-2c1 0 3 1 4-1 2 0 4-1 6-2-2 0-4 0-6 1h-1c-1-1-2-1-2-1-1 1-1 1-2 1h-1-3c2-1 4-1 6-2 2-2 3-2 5-2l1-1h-6v-1h-1 0c1-1 3-1 4-2l-1-1h-1v-2z" class="W"></path><path d="M511 462c3-1 7 0 9 0-2 1-6 1-9 0z" class="D"></path><path d="M488 528h0l1-1-1-1c1 0 2-1 3 0l1 2c-1 0-1 0-2 2v1h-2l-1-1-1-1 2-1z" class="I"></path><path d="M496 499c1 1 1 2 2 3 0 2-1 2 0 3 0 1 1 3 0 4h0v2c0 1 0 2-1 3l-1-1v-14z" class="S"></path><path d="M510 514c2-1 6-1 9 0v2l-2 1h-9v-1-1l2-1zm21 3h3c1 1 4 0 6 0 2 2 5 0 8 2v2c-2 0-8 0-10-1l-1-1h-1-3c-1-1-2-1-2-2z" class="D"></path><path d="M496 482c1 0 2 0 3 1l1 1c0 2 2 3 3 4 1 2 1 4 1 6l-5 1c-1 1-1 2-1 3v-3-1c1-1 1-2 2-3 0-2 0-4-1-6l-3-1v-2z" class="i"></path><path d="M563 507v17c0 3 0 6-1 9h0v-1c1-2 0-9-1-11v-1c-1-2-1-3-2-4h0v-4c0-1 1-1 1-2l-2-1v-1h2c1-1 2-1 3-1z" class="B"></path><path d="M496 499h0c-1-3 0-6-1-8 0-2-1-8 0-10l1 1v2l3 1c1 2 1 4 1 6-1 1-1 2-2 3v1 3 4c-1-1-1-2-2-3z" class="E"></path><path d="M487 477c1 0 3 1 3 2l1 4v1c-2 2-5 1-8 2l-1-1 1-6 2-2h2z" class="c"></path><path d="M487 477c1 0 3 1 3 2l1 4c-2-2-3-1-5-2l1-3h0c-1 0-1-1-2-1h2z" class="f"></path><defs><linearGradient id="T" x1="533.07" y1="483.143" x2="540.128" y2="480.904" xlink:href="#B"><stop offset="0" stop-color="#2b2b2a"></stop><stop offset="1" stop-color="#454343"></stop></linearGradient></defs><path fill="url(#T)" d="M536 478c1 0 2-1 3 0 1 0 2 1 2 2 1 2 1 5 0 6h-2-5c-1-1-1-3-1-5s1-2 3-3z"></path><path d="M489 468h5l1 1c-2 1-5 1-8 1l-3 1v1c1 0 0 0 1-1h1c1 0 3-1 4 0 2 1 5 1 7 2h0c-1 1-4 1-6 1h-2-2c0 1-1 0-2 0l-1 1v1l3 1h-2l-2 2h-3-1-1l2-2c-1-1-4 0-5 0l-1-1h0c-1-1-2 0-3-1 0-1 2-1 2-2-1-1-2-1-4 0h-2c1-2 6-2 8-3h1c2 0 6 0 9-1l4-1z" class="B"></path><path d="M485 469l4-1 1 1c-2 1-6 0-8 2-1 0-1 2-2 3 2 0 6-3 8-1l-1 1h0c-2-1-3 0-4 0-3 2-6 2-9 2h0c-1-1-2 0-3-1 0-1 2-1 2-2-1-1-2-1-4 0h-2c1-2 6-2 8-3h1c2 0 6 0 9-1z" class="I"></path><path d="M492 456h11 0l-11 1c2 1 4 2 7 1v1 1c-1 0-11 2-13 2h-2v1c-2 1-3 1-4 2h-1 4c6-1 13-1 19 0h0-11c-2 0-5 2-7 2-2 1-4 0-6 1h-1c-1 0-1 0-2 1h3 1c1 0 2 0 3-1 1 0 2 1 2 0l1 1c-3 1-7 1-9 1h-7l-1-1c3 0 5-1 7-3l-9 2 2-1 1-1c2 0 3-1 5-2v-1h-2-1 0l3-2c2-1 2-1 4-1h0 2c1 0 3-1 4-1h1l6-1c0-1 1-1 1-2z" class="B"></path><path d="M532 490h10v17 7 1h-4-6v-25z" class="T"></path><path d="M490 131l7-19c0-1 0-2 1-2 0 1-1 2-1 4-1 1-2 3-2 5 1-1 2-1 3-1v1h1 6l2 1c-2 0-3 0-4 2 0 1 0 2 1 3 5 1 11 0 16 1l1 1v1c3 0 9 1 10 3 1 0 1 1 2 1h0c1 1 1 2 1 3h-2c1 1 1 1 2 1h0l1 1h-3 0c-1-1-3-1-4-1h-1c4 1 6 2 9 3l3 7 2 9 2 6 9 35 1 8 5 28 1 14v1c-1-1-1-1-2-1-19-9-41-8-61-7-4 1-8 1-11 1-3 1-5 1-7 0-3 0-5 0-7 1l-2 2-2 1c-1 0-1-1-1-1h-1v2c-1 1 0 1-1 2h-1c1-2 1-5 1-7 2-9 2-19 4-28 1-2 0-4 1-6s1-4 1-5l1-5v-1l2-8 1-2v-1-3l1-2v-2l1-2v-2l2-7 2-4 1-4 5-15 2-7 1-1c0-2 0-3 1-4z" class="W"></path><path d="M538 153c1 1 2 1 3 2l2 6c-2-1-2-3-4-5-1-1-1-2-1-3z" class="J"></path><path d="M510 215c3 0 9 0 12 1l-2 1c-2 0-9 1-11-1l1-1z" class="D"></path><path d="M539 146l2 9c-1-1-2-1-3-2h0c1-2 0-4-1-5 0 0-1-1-2-1l1-1h3z" class="N"></path><path d="M530 183c1 2 0 6 0 8h-6v-2h2v-1h-1v-4l5-1z" class="H"></path><path d="M527 173l9-1 1 8c-1 1-2 1-3 2l-2-2h-1c1-1 0-5 0-7h-4z" class="J"></path><path d="M529 181c-1 0-2 0-3-1v-3c-1 0-1-1-2-1h0v-1c1-2 1-2 3-2h4c0 2 1 6 0 7s-1 1-2 1z" class="E"></path><path d="M537 180c0 1 1 2 1 3l-2 1c-1-1-1-1-2-1h-1c0 2 1 3 1 5v1h-1c-1 0-1 1-2 1 1 1 3 0 3 2 0 1 0 2-1 2 0 1 0 1 1 2h-1-5v-1c1-1 1-2 0-3-1 0-2 0-3-1h-1 6c0-2 1-6 0-8l-1-1h3v-1h-2-1c1 0 1 0 2-1h1l2 2c1-1 2-1 3-2z" class="D"></path><path d="M531 190l-1-2c1-1 1-2 1-4v-1h2c0 2 1 3 1 5v1h-1c-1 0-1 1-2 1zm-3 10v2 3h-1-1l-1 2v1c2-1 3-1 4-1 0 1 0 2 1 3v3 3l1 1-1 1s0 1-1 2c0-2-1-3-2-4s-1-1-2-1v-3c1 0 1-1 2-1h1v-1c-2 0-3 0-5-2-1-1-1-3-1-4 1-2 2-2 3-3l3-1z" class="B"></path><path d="M528 200v2 3h-1v-1c0-1-1-2-2-3l3-1z" class="D"></path><path d="M539 228h0c1 0 2 1 3 1h0 1c5 0 10 2 15 3l1 14v1c-1-1-1-1-2-1v-2l-4-5s1-1 1-2v-1-2c0-1-2-2-2-2-2 0-3-1-5-1-1 0-2 0-3-1h-1 0c0 1 0 2-1 2-2 0-3-1-4-2 0 0 0-1 1-2z" class="i"></path><path d="M533 183h1c1 0 1 0 2 1l2-1c0 2 1 3 2 4v1l1 1h1c2 1 3 3 5 5 1 0 2 1 3 2h2l1 8 5 28c-5-1-10-3-15-3h-1 0c-1 0-2-1-3-1h0c-1 1-1 2-1 2-2-2-4-3-6-4v-2c-1 0-1-1-1-2l-2-2c1-1 1-2 1-2l1-1-1-1v-3-3c-1-1-1-2-1-3-1 0-2 0-4 1v-1l1-2h1 1v-3-2-4h5 1c-1-1-1-1-1-2 1 0 1-1 1-2 0-2-2-1-3-2 1 0 1-1 2-1h1v-1c0-2-1-3-1-5z" class="C"></path><path d="M546 203h0v2c-2 0-4 0-5 1-1-1-1-2 0-3h4 1z" class="H"></path><path d="M528 196h5l-1 2c1 1 1 1 2 1 0 1 0 3 1 3-2 1-3 1-5 1v2h-1c0-1 0-2-1-3v-2-4z" class="B"></path><path d="M541 206c1-1 3-1 5-1l1 5h1c0 1 1 1 1 2-1 1-1 1-2 1v1h1v1h-4l1-1h-2l-1-1v-1s1 0 2 1c1 0 1 0 2-1l-1-1h-2-1c-1 0-1 0-2-1 0-2 0-3 1-4z" class="J"></path><path d="M540 188l1 1h1c2 1 3 3 5 5 1 0 2 1 3 2h2l1 8c-1-1-2-2-4-2v2h-1l-1-1h-1 0v-2-2h-2c-2-1-2-1-3-1h-1l1-1-1-1c1-2 1-2 1-3-1-2-1-4-1-5zm-9 18v-2l1 1 2 4v1h0c1-2 1-5 1-7 0-1-1-2 0-3v-6-3h2 0c0 6 1 10 2 16l1 1h-1v7 1c1 0 2-1 3-1l1-1h2l-1 1h-2l1 3-2 1c0 1 0 0 1 1h-1-1c-1-1-2-1-3-1v1h0c1 2 0 7 0 7 1 1 1 1 2 1-1 1-1 2-1 2-2-2-4-3-6-4v-2c-1 0-1-1-1-2l-2-2c1-1 1-2 1-2l1-1-1-1v-3-3c-1-1-1-2-1-3l2-1z" class="E"></path><path d="M531 206v6h1c1 0 1 0 2-1l1 1v5c-1 1-1 0-1 1v4h0-1-1v2c-1 0-1-1-1-2l-2-2c1-1 1-2 1-2l1-1-1-1v-3-3c-1-1-1-2-1-3l2-1z" class="S"></path><path d="M546 203h1l1 1h1v-2c2 0 3 1 4 2l5 28c-5-1-10-3-15-3h-1 0c-1 0-2-1-3-1h0c-1 0-1 0-2-1 0 0 1-5 0-7h0v-1c1 0 2 0 3 1h1 1c-1-1-1 0-1-1l2-1-1-3h2 4v-1h-1v-1c1 0 1 0 2-1 0-1-1-1-1-2h-1l-1-5v-2z" class="W"></path><path d="M539 228c-1 0-1 0-2-1 0 0 1-5 0-7h0v-1c1 0 2 0 3 1h1 1c1 1 3 1 4 2v1h-2-2c1 2 0 4 1 6h-1 0c-1 0-2-1-3-1h0z" class="H"></path><path d="M490 131l7-19c0-1 0-2 1-2 0 1-1 2-1 4-1 1-2 3-2 5 1-1 2-1 3-1v1h1 6l2 1c-2 0-3 0-4 2 0 1 0 2 1 3 5 1 11 0 16 1l1 1v1c3 0 9 1 10 3 1 0 1 1 2 1h0c1 1 1 2 1 3h-2c1 1 1 1 2 1h0l1 1h-3 0c-1-1-3-1-4-1h-1-12c0-1 1-1 1-1 1 0 2 0 4-1v1h1v-1-2c-1-1-3 1-4 2v-2c-1 0-2 1-2 2l-2-2c0 1 0 1-1 3l1 1h-3c1-1 1-1 1-2l-1-1v-1h-1c0 2 0 2-1 4-4 1-8 1-12 2-3 1-5 2-7 4v1l6-3h8c1-1 1-1 2 0h0 0c-1 1-1 2-2 3l-3 1c2 1 2-1 3 0 1 0 1 0 2 1h0c-1 0-1 1-3 0-5 2-11 3-16 6 5-1 10-3 15-3-4 2-13 3-16 6 4-1 10-3 15-2-5 1-9 2-13 4-1 0-2 1-3 1h0 1l13-2-14 5h-1c2 0 5-1 7-1 2-1 5-2 8-2-3 1-15 4-16 7 1 0 0 0 1-1 4-2 11-3 15-3l-4 1 2 1h2v1h0l-4 1v1c1 1 2 1 3 1h0c-1 1-1 1-2 1h0c-6 2-12 3-17 7 5-2 11-3 16-4 0 1 0 1-1 1l-7 2c-1 1-2 1-3 1s-2 1-3 1h-2l1 1h0c2-1 4-2 6-1 3 0 6-2 8-2-3 2-6 2-8 4s-5 2-7 3c5 0 9-3 14-4-3 2-6 4-9 5l-5 1v1c4-1 8-2 11-3h0c-3 2-7 3-11 5h1l8-2c-3 2-7 4-10 4h0 5c2-1 4-1 6-2h2c0 1-2 1-2 1-4 1-8 3-12 3v1c5 0 9-2 14-2h0 0c-4 1-7 2-10 3h-1c-1 1-2 1-3 1h-1c5 1 10-1 15-2-4 2-9 3-13 4 4 0 9-1 14-2h2c0-1 1-1 2-1h1c1 0 2 0 4-1h2v1h-2l-1 1 1 1c1 0 3-1 4 0-1 1-2 1-3 1s-3 0-4 1v1h3 0c-1 1-1 1-2 1-2 0-3 0-4 1h-3c-1 1-1 0-2 1h-2c-4 1-11 2-14 5h0 1c4-2 10-3 15-3 1-1 1-1 3-1 0-1 1-1 2-1 1-1 1-1 2-1h1 4c2-1 3 0 5 0h7l1 1c0 1 0 1-1 1-1 1-2 0-3 0l-1-1c-2 0-3 0-5 1-2 2-5 2-7 3-1 1-2 3-3 3h-1c-2 1-6 3-7 3l-15 6 17-3c-5 2-11 2-15 5h0c6 0 12-2 18-3-6 3-15 3-20 8 7-3 13-4 19-5-2 2-5 2-8 3-4 1-9 2-11 5 5-2 9-3 14-4h4c-1 0-2 1-3 1v2h3c1-1 1-1 2-1l1 1h-3v1c-2 0-6 2-6 2h0c1 0 3-1 5 0 3 1 21-3 22 0v3c-2 0-5 0-7 1-6 0-11 1-16 3-1 1-3 0-5 0h-1l-11 3c4 0 8-1 12 0-1 0-7 1-7 2h1c2 0 5-1 6 0-1 1-2 1-4 1-3 0-5 0-7 1l-2 2-2 1c-1 0-1-1-1-1h-1v2c-1 1 0 1-1 2h-1c1-2 1-5 1-7 2-9 2-19 4-28 1-2 0-4 1-6s1-4 1-5l1-5v-1l2-8 1-2v-1-3l1-2v-2l1-2v-2l2-7 2-4 1-4 5-15 2-7 1-1c0-2 0-3 1-4z" class="U"></path><path d="M485 225c-5 1-10 1-15 3h0 0c4-3 11-6 16-6v2l-1 1zm13-62c-6 0-13 3-19 4 4-2 8-4 12-5l3-1 2 1h2v1h0z" class="N"></path><path d="M495 167c-3-1-7 1-10 2-1 0-3 0-4 1l-1 1v-2c2-1 4-1 6-2 3-1 5-2 8-3v1c1 1 2 1 3 1h0c-1 1-1 1-2 1h0z" class="D"></path><path d="M503 140c1-1 1-1 2 0h0 0c-1 1-1 2-2 3l-3 1c2 1 2-1 3 0 1 0 1 0 2 1h0c-1 0-1 1-3 0-3-1-10 1-14 2h-1l2-1c3 0 7-2 10-3h1c-2-1-4-1-6 0h-1-1c2-1 6-2 8-2 0-1 1-1 2-1h1z" class="N"></path><path d="M490 131l7-19c0-1 0-2 1-2 0 1-1 2-1 4-1 1-2 3-2 5 1-1 2-1 3-1v1h-3c-1 1-1 2-1 3-1 1-1 2-2 3v2c0 1-1 2-1 3v1l1-1h4c5-1 9-1 13-1 8 0 15-1 22 2v1 1c-4-2-6-2-9-2-11-2-22 1-32 0zm9-12h6l2 1c-2 0-3 0-4 2 0 1 0 2 1 3 5 1 11 0 16 1l1 1v1c-9-1-18-1-27 1h0v-1h1s2-1 2-2h-1c-1 0-1 1-2 1v-1h1l1-1h2 0c-1-1-2-1-3-1 0-1 3-1 4-2h-3l-1-1c2 0 4-1 5-1l-1-1z" class="S"></path><path d="M491 192h2c0-1 1-1 2-1h1c1 0 2 0 4-1h2v1h-2l-1 1 1 1c1 0 3-1 4 0-1 1-2 1-3 1s-3 0-4 1v1h3 0c-1 1-1 1-2 1-2 0-3 0-4 1h-3c-1 1-1 0-2 1h-2c-5-1-9 1-13 2 3-3 8-4 12-5h-6v1c-2 0-4 1-6 2l1-1c2-2 5-3 9-3 2-1 4-2 7-3z" class="i"></path><path d="M489 201c1-1 1-1 3-1 0-1 1-1 2-1 1-1 1-1 2-1h1 4c2-1 3 0 5 0h7l1 1c0 1 0 1-1 1-1 1-2 0-3 0l-1-1c-2 0-3 0-5 1-2 2-5 2-7 3-1 1-2 3-3 3h-1c-2 1-6 3-7 3-4-1-10 2-14 3h0c4-2 8-3 13-5-4 0-9 3-13 2 4-2 9-3 13-5-3-1-9 2-13 3 5-3 10-5 15-6h1 1z" class="J"></path><path d="M493 206c-1 0-2 0-3-1h0c1-1 1-1 2-1h1c1-1 2-1 4-1-1 1-2 3-3 3h-1z" class="N"></path><path d="M486 224h3c1-1 1-1 2-1l1 1h-3v1c-2 0-6 2-6 2h0c1 0 3-1 5 0 3 1 21-3 22 0v3c-2 0-5 0-7 1-6 0-11 1-16 3-1 1-3 0-5 0-4 0-8 1-12 1 4-2 9-2 12-4l-10 2h-1v-1h1l12-4c-4 0-8 2-13 2h0c4-2 8-3 12-4 1 0 1-1 2-1h0l1-1z" class="D"></path><path d="M505 533c16-1 33-1 49 3 2 1 5 1 7 3 1 0 1 1 1 1 1 4 1 9 1 13l-1 26 1 6-1 14c0 3 1 5 0 7l1 15v3l-8-2c-1-1-2-1-3-1-2 0-5-1-7-1l-11-2h-1-2c-1 1-2 1-3 2-1-1-2-1-3-1-2-1-4-1-6 0-2-1-5-1-7-1-1 1-3 1-4 1-3 0-7-1-11-1h-2-2c-3-1-6 0-8-2h-1l-1 1 1 1c1 0 1-1 2 0v1 1h-2c-3 1-7 0-10 2v1 1h-1v1c-1 1-2 1-3 2l-1 1-2-1h0l-1-1h-5v-1-2-7-2-7h0v-5-62h2 1 1l1-1h2 1l1 1s1 0 2-1h3c2 0 3-1 4-1h1c1 0 3-1 5-1h3l5-1c0-1-1-1-1-2 0 0 1 0 2-1h11z" class="W"></path><path d="M541 560c1 2 1 4 1 5-2 0-7 1-9 0h4c1-2 2-2 3-3s1-1 1-2z" class="e"></path><path d="M526 568h2v4h-3v-1c-2 0-2 0-3-1 1-1 3-1 4-2z" class="J"></path><path d="M496 566v-1l1-1c1 1 4 1 5 2v5h-2v-2l-1-1c-1-1-1-1-1-2l-1-1-1 1z" class="E"></path><path d="M496 566l1-1 1 1c0 1 0 1 1 2l1 1v2l-1 1h0 1l-1 1-2 2v1l-1-10z" class="C"></path><path d="M500 571h2c0 1 0 2 1 4-1 1-5 1-5 1-1 2-1 3 0 5h-1-1l1-5v-1l2-2 1-1h-1 0l1-1zm57-17l-1-2c0-1 1 0 2-1l-1-1 1-1c1 0 1 0 2 1h1l1 1v3c-2 1-3 0-5 0z" class="D"></path><path d="M494 533h11-4v2c-2 1-5 1-8 1 0-1-1-1-1-2 0 0 1 0 2-1z" class="I"></path><path d="M529 598h0c3 3 10 1 14 2v1h-1c-3 1-10 1-13-1v-2z" class="T"></path><path d="M562 606h0v-7c-1-1-1-2-2-3-1 0-1 0-2-1 1-1 1-1 2-1v-1l1-2v-1h1v-2l-2-2c1-1 1-1 3-1l-1 14c0 3 1 5 0 7z" class="D"></path><path d="M562 540c1 4 1 9 1 13l-1 26v-17c-1-1-2-3-3-4 0-1-1-3-2-4 2 0 3 1 5 0v-3-11zm-77-3h3-1c-1 1-1 1-3 1h-1 1c2 1 4 2 7 2 3 1 8 0 11 1-2 0-6 2-8 2-2-2-4 0-6-1-3-1-3-3-6-2h-7v-1c2 0 3-1 4-1h1c1 0 3-1 5-1z" class="B"></path><path d="M533 565v-1c-1-1 0-3 0-4 1-2 2-3 3-3 1-1 2 0 4 0 1 1 1 2 1 3s0 1-1 2-2 1-3 3h-4z" class="Q"></path><path d="M485 557c1-1 2-2 4-1l1 1c1 2 2 5 1 6 0 1 0 1-1 1l-7 1v-2c0-3 0-4 2-6z" class="k"></path><path d="M484 559c2 1 5 1 6 2l-1 1h-5v-3z" class="c"></path><path d="M482 540c3-1 3 1 6 2 2 1 4-1 6 1l-12 3c6 0 11-1 16 0-2 1-11 2-13 2l-1-1h0c-1 0-2-1-4 0h-6l2-1c2-1 4-1 6-1 1 0 0 0 1-1-1-2-3 0-4 0-1-2-1-2 0-3h2l1-1z" class="H"></path><path d="M483 565l7-1v1c-2 1-5 0-7 1-1 0-3 4-3 4l-1 1 2 1h0l1-3c1-1 4-1 6-1h2l1 1h-2l-7 1v6l-1-1c-1 1-2 1-2 2l-2-1 1-2-1-1c0-1-1-2-1-3h-3v-1-1c1-2 2-2 4-3h0c2-1 4-1 6 0z" class="E"></path><path d="M473 570v-1-1c1-2 2-2 4-3v1c0 1-1 1-1 2l1 1h1c1-1 1-1 2-3h1v1c-1 1-1 1-1 2-1 1-1 2-2 3 1 1 2 1 3 2v1c-1 1-2 1-2 2l-2-1 1-2-1-1c0-1-1-2-1-3h-3z" class="F"></path><path d="M482 563h-2c-1-1-1-1-1-2-1 0-2 1-3 0v-1l1-1c-1-1-1-2-2-3l1-1v-1c1-2 2-2 3-3h0v-1h-2-1l1-1c2-1 3-1 5-1v1c3 2 9 1 12 0l9 1h1v1 3c-1 1-3 1-4 1h-2c-1 0-3 0-4 1h-3l-1 1-1-1c-2-1-3 0-4 1-2 2-2 3-2 6h-1z" class="S"></path><path d="M503 550h1v1 3c-1 1-3 1-4 1h-2c-1 0-3 0-4 1h-3l-1 1-1-1c-2-1-3 0-4 1h-4c0-1-1-1-1-2 1-1 2-1 2-1 1-1 2-2 3-2 3-1 5-1 8-1 3-1 7 0 10-1z" class="D"></path><path d="M533 570h9l-1 26-1 1h-8l1-27zm-51 6v-6l7-1h2c1 3 1 6 1 8 0 6-1 13 0 19l-1 2c-2 1-3 1-5 1v-3h-4l1-20h-1 0z" class="T"></path><path d="M486 596h2 4l-1 2c-2 1-3 1-5 1v-3z" class="B"></path><path d="M482 576v-6l7-1-1 1c0 3 1 7-1 9l-1-1v-5l-1-1h-2v4h-1 0z" class="P"></path><path d="M479 598c2 1 5 1 7 0v1c2 0 3 0 5-1l2 1h1l-1 1c-1 2-2 3-3 4v3l-1 1h-2c-2 1-3 2-6 2v3c1 1 3 1 4 1l6 1h1c-1 1-1 1-2 1-1-1-2 0-3 0h-2-1l-1 1 1 1c1 0 1-1 2 0v1 1h-2c-2 0-3-1-4-2v-1c-3 0-4 0-7-1h-3v-1h2c1-1 1 0 2-1h0l1-1c-2 0-2 0-3-1v-1h2v-1h-2v-1c2 0 3-2 3-3v-1h-2v-1h2v-2-2h3v-1l1-1z" class="O"></path><path d="M482 605c3 0 6 0 8-1v3l-1 1c-1-1-1-1-2-1-2 0-4 0-6-1v-1h1z" class="B"></path><path d="M485 614l-3 1h-1c-1 0-1 0-2-1v-1c-1 0-2-1-3-1v-1s2 0 2-1h-1v-1c2-2 7-1 10-1-2 1-3 2-6 2v3c1 1 3 1 4 1z" class="K"></path><path d="M479 598c2 1 5 1 7 0v1c2 0 3 0 5-1l2 1h1l-1 1c-1 2-2 3-3 4-2 1-5 1-8 1h-1 0-4v-1h-2v-2-2h3v-1l1-1z" class="F"></path><path d="M475 602c3 1 4 2 7 1v2h-1 0-4v-1h-2v-2z" class="M"></path><path d="M479 598c2 1 5 1 7 0v1c2 0 3 0 5-1l2 1h1l-1 1h-4-2-2v1c-1-1-2-1-2-1h-5v-1l1-1zm-9-58s1 0 2-1h3v1h7l-1 1h-2c-1 1-1 1 0 3 1 0 3-2 4 0-1 1 0 1-1 1-2 0-4 0-6 1l-2 1h6c2-1 3 0 4 0h0l-2 1c-2 0-3 0-5 1l-1 1h1 2v1h0c-1 1-2 1-3 3v1l-1 1c1 1 1 2 2 3l-1 1v1c1 1 2 0 3 0 0 1 0 1 1 2h2c-2 1-3 1-4 0h-5-1c0-1 0-1 1-2v-1l1-1-1-1c-1 0-2-1-4 0-1 1-2 0-3 1l-2 1h0l1 1h1 0v1h-3c-1-1 0-2 0-3v-1-2c0-3-1-8 0-10v-1l-1-1v-1c2 0 4 0 5-1h1-1v-1c2 0 2 0 3-1z" class="R"></path><path d="M471 544h1 0c1 0 1 0 1 1h1l-3 3h1c1-1 2-1 3 0h0l-4 2-1 1s-1 0-1 1l6-1v1c-1 1-2 1-2 2s0 1-1 2l2 2v1l-1-1c-1 0-2-1-4 0l-3-1h0c1-1 2-1 4-2h-4c0-1 4-1 5-3h0-5 0c1-1 2-1 3-2h-3v-1l4-1h-4v-1c2-1 4 0 5-2v-1z" class="Z"></path><path d="M470 540s1 0 2-1h3v1h7l-1 1h-2c-1 1-1 1 0 3 1 0 3-2 4 0-1 1 0 1-1 1-2 0-4 0-6 1l-2 1h6c2-1 3 0 4 0h0l-2 1c-2 0-3 0-5 1l-1 1h1 2v1h0c-1 1-2 1-3 3v1l-1 1c1 1 1 2 2 3l-1 1v1c1 1 2 0 3 0 0 1 0 1 1 2h2c-2 1-3 1-4 0h-5-1c0-1 0-1 1-2v-1l1-1v-1l-2-2c1-1 1-1 1-2s1-1 2-2v-1l-6 1c0-1 1-1 1-1l1-1 4-2h0c-1-1-2-1-3 0h-1l3-3h-1c0-1 0-1-1-1h0-1c-1 1-3 1-4 1l-1-1h1c2-1 4-1 6-2v-1c-2 0-4 1-5 1h-1v-1c2 0 2 0 3-1z" class="G"></path><path d="M473 570h3c0 1 1 2 1 3l1 1-1 2 2 1c0-1 1-1 2-2l1 1h0 1l-1 20h4v3-1c-2 1-5 1-7 0l-1 1v1h-3v2 2h-2v1l-1-1v-1-1-10c-2 0-2 1-3 0l-1-1c1 0 1 0 1-1v-3-2l1-2-1-1v-1c1-1 0-1 0-2l-2-1v-5c2 0 3-1 4-1h1l1-1v-1z" class="X"></path><path d="M476 595l1-1c1 1 2 3 2 4l-1 1-3-1c0-1 1-2 1-3z" class="F"></path><path d="M476 592h1s1-1 2-1v1l1 4h1c0 1 0 1 1 2h4c-2 1-5 1-7 0 0-1-1-3-2-4l-1 1v-3z" class="M"></path><path d="M479 585c1 2 0 3 1 5s1 4 0 6l-1-4v-1c-1 0-2 1-2 1h-1c0-2 0-5 1-6h1l1-1z" class="G"></path><path d="M476 570c0 1 1 2 1 3l1 1-1 2 2 1c0-1 1-1 2-2l1 1h0c0 2-1 2-2 3l-1 1v5c-1-1-2-1-3-2v-3c-2-3 0-6 0-9v-1z" class="M"></path><path d="M479 577c0-1 1-1 2-2l1 1h0c0 2-1 2-2 3l-1-1v-1z" class="J"></path><path d="M472 572l1-1v16c0 5 1 12 0 17v1l-1-1v-1-1-10l-1-20h1z" class="F"></path><path d="M467 578v-5c2 0 3-1 4-1l1 20c-2 0-2 1-3 0l-1-1c1 0 1 0 1-1v-3-2l1-2-1-1v-1c1-1 0-1 0-2l-2-1z" class="U"></path><path d="M482 576h1l-1 20h4v3-1h-4c-1-1-1-1-1-2h-1c1-2 1-4 0-6s0-3-1-5v-5l1-1c1-1 2-1 2-3z" class="E"></path><path d="M466 599c1-2 1-5 1-8h1l1 1c1 1 1 0 3 0v10 1 1l1 1h2v1c0 1-1 3-3 3v1h2v1h-2v1c1 1 1 1 3 1l-1 1h0c-1 1-1 0-2 1h-2v1h3c3 1 4 1 7 1v1c1 1 2 2 4 2-3 1-7 0-10 2v1 1h-1v1c-1 1-2 1-3 2l-1 1-2-1h0l-1-1h-5v-1-2-7-2-7h0v-5c1-1 2-2 3-2v1l-1 1v1h2l1-1v-3z" class="Y"></path><path d="M473 616c3 1 4 1 7 1v1l-1 1h-7l-1-1-1 1-1 1c-1 0-1 1-2 1v-1c1 0 1-1 2-1v-1c-1 1-2 0-3 0h-1 2v-1h4v1c1-1 1-1 2-1v-1z" class="L"></path><path d="M469 601l3 2v1l1 1h2l-1 1c-1 0-2 0-3 1l-2 2h-2c0-2 0-4 1-6v1h1v-3z" class="U"></path><path d="M472 592v10 1l-3-2c-1-2 0-6 0-9 1 1 1 0 3 0z" class="a"></path><path d="M461 625c4-2 9-2 13-3v1 1h-1v1c-1 1-2 1-3 2l-1 1-2-1h0l-1-1h-5v-1z" class="g"></path><path d="M461 540h2 1 1l1-1h2 1l1 1c-1 1-1 1-3 1v1h1-1c-1 1-3 1-5 1v1l1 1v1c-1 2 0 7 0 10v2 1c0 1-1 2 0 3h3v-1h0-1l-1-1h0l2-1c1-1 2 0 3-1 2-1 3 0 4 0l1 1-1 1v1c-1 1-1 1-1 2h1 5c1 1 2 1 4 0h1v2c-2-1-4-1-6 0h0c-2 1-3 1-4 3v1 1 1l-1 1h-1c-1 0-2 1-4 1v5l2 1c0 1 1 1 0 2v1l1 1-1 2v2 3c0 1 0 1-1 1h-1c0 3 0 6-1 8v3l-1 1h-2v-1l1-1v-1c-1 0-2 1-3 2v-62z" class="n"></path><path d="M463 589h-1c0-1 0 0 1-1 0-1-1-4-1-4v-4l1-1h0c1 1 1 2 1 3 0 2 0 4-1 6l1 1h-1z" class="d"></path><path d="M464 582h1s0 1 1 1c0 3 0 7-1 10l-2-4h1l-1-1c1-2 1-4 1-6z" class="X"></path><path d="M466 570c0 3-1 8 0 10 1-1 1-1 1-2l2 1c0 1 1 1 0 2v1l1 1-1 2v2 3c0 1 0 1-1 1h-1c0 3 0 6-1 8h-2c-1 0-1 0-2-1h2c0-1-1-1-1-2 1-1 1 0 2-1h1v-1l-2-1h1c1-3 1-7 1-10v-2l-1-1v-3-4c0-1 1-2 1-3z" class="a"></path><path d="M473 563h5c1 1 2 1 4 0h1v2c-2-1-4-1-6 0h0c-2 1-3 1-4 3v1 1 1l-1 1h-1c-1 0-2 1-4 1v5c0 1 0 1-1 2-1-2 0-7 0-10l-1-1c1 0 1-1 2-1 0-1 1-2 1-3h2c1-1 2-1 3-1h1l-1-1z" class="b"></path><path d="M472 572c-1-1-1-1-1-2h-3c-1-1 1-1 2-2v-2c1 0 2 0 3-1h4 0c-2 1-3 1-4 3v1 1 1l-1 1z" class="L"></path><path d="M525 313l11 2 4 1h3 1c2 0 4 0 6 1l4 1 1-1-2-1h1 1c2-1 2-2 3-3 1 1 2 1 3 2 0 1 0 1 1 1v1l1 1c1 4 0 7 0 11h-1l-9-2h-1v1c3 1 8 1 10 3v1c-3-1-16-4-18-4 6 3 13 3 18 5v2h-2s1 1 2 1v1 5h-1c-3-1-5-2-8-2l9 3v1c-1 1-6-1-8-2 2 1 8 2 9 4l-1 1c-2 0-4-2-6-1l6 2c1 1 0 2 0 3v25 57h0c-3-2-6-2-9-3-18-4-37-4-55-4l-1 1h0c0 1 0 1-1 2h0-1v-1c-1 0-2 0-2 1l-1-1h-2l-1 1h-2l-1 1h-1v-1l1-1h-3v1h0v1c-1-2-1-2-3-2l-1 2c1 0 1 0 2-1l1 1c0 1 0 1-1 1-1 1-2 1-4 1h0l-1-1h-1l-1-1h-2v1l-1-1c-2 0-3 1-5 1-1 1-1 1-3 1v-1c2-1 3-1 5-1l2-2h-4l-1 1c-2 1-2 1-4 3 1 1 1 2 1 3l-1 1V332c9-4 20-9 30-9h2v-1h0l17-2h1l2-1s0-1 1-2c-1-2-1-2 0-3 2-1 4-1 6-1h5z" class="W"></path><path d="M494 373l2 1c0 1-1 2-1 3-1 1-1 1-2 1l-2-1 1-2c1 0 1-1 2-1v-1z" class="J"></path><path d="M507 385c3-1 7-1 10 1 1 0 1 0 3 1h1 0l-14-1h-1l1-1z" class="i"></path><path d="M494 405h0v6 3c3 0 4 0 5-2h1v2 2l-1 1c-2 0-4 1-5 0-1-2-1-4-1-5v-1h1v-3c-1-1-1-2 0-3z" class="N"></path><path d="M493 354c4 0 7 1 10 1-3 1-7 3-10 4-1 0-2-1-3 0-2 0-4 1-6 1 3-3 5-4 9-6z" class="i"></path><path d="M553 340h-1l-1-1h2l1-1c-2 0-3 0-4-1 1-1 3 0 5 0v-1h-1-2c-1 0-1-1-2-1l1-1 2 1h1 1c1 0 2 1 2 1 2 0 3 0 4 1v1l1-1v5h-1c-3-1-5-2-8-2z" class="S"></path><path d="M490 333c2-1 3 0 5-1 1 0 2 1 4 0h3l1 1c0 1 0 1-1 2v1c-2 0-6 0-7 1h-3c-1-1-2-1-3-1h0l-1-1 2-2z" class="i"></path><path d="M494 405c-1-2-1-3-1-4 1-1 2-1 3-1l3 2v1 9c-1 2-2 2-5 2v-3-6h0z" class="J"></path><path d="M562 351v25c-1-4 0-11 0-15h-1c-2 0-3-1-4-2l1-1h3 1 0l-1-1c-1 0-1 0-2-1h0c-1 0-2 0-3-1s-1-2-2-3l1-1c2 0 4-1 6 1l1-1z" class="D"></path><path d="M536 365h1c2 0 2 0 4 1 1 3 1 6 0 9h-2-2c-2 0-3 0-4-1 1-4 1-6 3-9z" class="P"></path><path d="M510 320c5 0 9 1 14 2 4 0 7 0 11 1l18 4h0-1v1l-17-4c-8-1-16-1-23-1-1 0-1 1-2 1-1 1-7 1-8 1h-2v-1l-12 1v-1h2l1-1h2v-1h0l17-2z" class="V"></path><path d="M493 323h19c-1 0-1 1-2 1-1 1-7 1-8 1h-2v-1l-12 1v-1h2l1-1h2z" class="D"></path><path d="M533 396l10 1v23l-11-1 1-23z" class="T"></path><path d="M478 398s1 0 1-1h0 0l-1-1c2-1 3-1 3-2l1-1c2-1 5-1 7-2l-1 1c-1 1-2 1-3 1s-2 1-3 2l-1 1h3c1-1 1-1 1-2h1l1-1h1c1 2 4 2 4 3v5 16c0 1 0 2 1 3h1c1 1 1 2 2 3h7l20 1h8c-1-2-1-1 0-2 1-2 6-1 8-1h1c1 1 2 1 3 1l1 1-1 1-2-1v1h-1c-1 0-1 0-2 1v1c4 0 8 1 12 2 2 0 2 1 3 2-18-4-37-4-55-4l-1 1h0c0 1 0 1-1 2h0-1v-1c-1 0-2 0-2 1l-1-1h-2l-1 1h-2l-1 1h-1v-1l1-1h-3v1h0v1c-1-2-1-2-3-2h0l-1-1h-4v-1l1-1h3v-1h-1v-1c2 0 2-2 4-1 1 0 2 0 3-1h4l1-1v-2s1 0 1-1v-4-17c-2 0-6 1-9 1v7c-3 1-5 1-7 1h-2c1-1 2-2 3-2h1l1-1c-2 0-4 1-5 0 1-2 5-1 7-2l-3-1 1-1z" class="B"></path><path d="M490 420h2v1c-1 1-2 2-3 4l-1-1v-1l-1-1-1 3c0 1 0 1 1 1 3 1 6 0 9 0h2l-1 1h0c0 1 0 1-1 2h0-1v-1c-1 0-2 0-2 1l-1-1h-2l-1 1h-2l-1 1h-1v-1l1-1h-3v1h0v1c-1-2-1-2-3-2h0l-1-1h-4v-1l1-1h3v-1h-1v-1c2 0 2-2 4-1 1 0 2 0 3-1h4l1-1z" class="G"></path><path d="M525 313l11 2 4 1h3 1c2 0 4 0 6 1l4 1 1-1-2-1h1 1c2-1 2-2 3-3 1 1 2 1 3 2 0 1 0 1 1 1v1l1 1c1 4 0 7 0 11h-1l-9-2h0l-18-4c-4-1-7-1-11-1-5-1-9-2-14-2h1l2-1s0-1 1-2c-1-2-1-2 0-3 2-1 4-1 6-1h5z" class="L"></path><path d="M514 317l2-2h1l1 4c-1 1-3 1-5 0 0 0 0-1 1-2z" class="B"></path><path d="M529 316c2 0 3 0 5 1 0 1 0 2-1 3-1 0-4 0-5-1l1-3z" class="C"></path><path d="M522 315c1 0 3 0 4 1l1 3c-2 1-4 0-6 0 0-1 0-2 1-4z" class="N"></path><path d="M536 315l4 1v5l-1 1-1-1c-1 0-2 0-3-1 0-1 1-3 1-5z" class="W"></path><path d="M540 316h3 1l3 1 2 1c2 0 3 1 5 2h1l-1 2c2 1 2 0 3 2v1h-1v1c-1-1-3-1-4-1l-1-1c-4 1-8-1-11-1l-1-1 1-1v-5z" class="Y"></path><path d="M547 317l2 1-1 5h-2v-1l1-5z" class="C"></path><path d="M554 320h1l-1 2c2 1 2 0 3 2v1h-1s-1-1-2-1-2 0-4-1v-1c1 0 1-1 1-1l1-1h2z" class="S"></path><path d="M540 316h3 1l3 1-1 5v1c-2 0-3-1-5-1l-1-1v-5z" class="O"></path><path d="M544 316l3 1-1 5c-2-2-2-3-3-6h1z" class="M"></path><path d="M558 313c1 1 2 1 3 2 0 1 0 1 1 1v1l1 1c1 4 0 7 0 11h-1v-1c-1-1-4-2-6-2v-1h1v-1c-1-2-1-1-3-2l1-2h-1c-2-1-3-2-5-2l-2-1-3-1c2 0 4 0 6 1l4 1 1-1-2-1h1 1c2-1 2-2 3-3z" class="d"></path><path d="M558 313c1 1 2 1 3 2 0 1 0 1 1 1v1l1 1-1 1c-1 0-5-3-7-3 2-1 2-2 3-3z" class="E"></path><path d="M461 436V332c9-4 20-9 30-9l-1 1h-2v1l12-1v1l-19 3v1l11-2c2-1 12 0 15 0h0c0 1-1 1-1 1-1 0-2 1-3 2-1 0-3-1-4 0h-5 0c-4 1-7 2-11 3-2 0-3 0-4 1h-2l-1 1c1 0 1 0 3-1h0 3l8-1-2 2 1 1h0c1 0 2 0 3 1h-4c-2 1-4 1-6 1h-2c-1 1-2 1-3 1h-2v1h0 3c2-1 6-1 8 0-3 1-6 1-9 2h-2 0c-2 0-2 0-3 1-1 0-1 0-2 1h2c0-1 1-1 2-1h1 4 1c1-1 2-1 4-1l1-1h2c1-1 1-2 2-2 1-1 4 0 6-1 1 0 2 1 4 0h0 0c0 1 0 1-1 1s-2 0-3 1h-1l2 2 1 1-1 1c-1 0 0 0-1 1s-2 1-3 2h-2v1c1 0 1 0 2 1h-1c-1 1-2 1-3 1l-3 1h-1c-1 2-4 1-5 3 1 0 1 0 2-1h2 3c1 1 2 1 3 1h4c-4 2-6 3-9 6 2 0 4-1 6-1 1-1 2 0 3 0-3 1-7 2-10 3v1l3 2c2 0 3 0 4 1 0 1 0 2 1 2l-1 5h4v1c-1 0-1 1-2 1l-1 2 2 1v1c0 1 1 1 2 1-1 1-1 1-2 1l-5 1v1h0 2l4 1h-1l-1 2v1h2v1h-1c0 1-1 1-1 2h5v1h-8c-2 1-5 1-7 2l-1 1c0 1-1 1-3 2l1 1h0 0c0 1-1 1-1 1l-1 1 3 1c-2 1-6 0-7 2 1 1 3 0 5 0l-1 1h-1c-1 0-2 1-3 2h2c2 0 4 0 7-1v-7c3 0 7-1 9-1v17 4c0 1-1 1-1 1v2l-1 1h-4c-1 1-2 1-3 1-2-1-2 1-4 1v1h1v1h-3l-1 1v1h4l1 1h0l-1 2c1 0 1 0 2-1l1 1c0 1 0 1-1 1-1 1-2 1-4 1h0l-1-1h-1l-1-1h-2v1l-1-1c-2 0-3 1-5 1-1 1-1 1-3 1v-1c2-1 3-1 5-1l2-2h-4l-1 1c-2 1-2 1-4 3 1 1 1 2 1 3l-1 1z" class="M"></path><path d="M476 372h1c1-1 3-1 4-2 1 1 1 2 1 3h-8 0-4c-1 1-3 1-5 1v-1h4 0 4c1-1 2-1 3-1z" class="B"></path><path d="M488 337c-1-1-6-1-7 0h-1-2c-1 1-3 1-4 1h-1-1-1c1-1 1-1 2-1l3-1h2 1c3-1 6-2 9-1l1 1h0c1 0 2 0 3 1h-4z" class="J"></path><path d="M488 347c1 1 1 0 2 0v1c1 0 1 0 2 1h-1c-1 1-2 1-3 1-4 0-9 0-13 1h0-1l1-1 13-3z" class="N"></path><path d="M485 387l-8 1h-4 0c6-2 13-3 20-4l-1 2v1h2v1h-1c0 1-1 1-1 2h-4v-1l2-1v-1c-2 0-4-1-5 0z" class="J"></path><path d="M475 405c2 0 4 0 7-1v8c-2 0-5 1-6 0 0-1 0-1 1-1l1-1h-2-3l-1-1c2 0 3 0 4-1-1-1-3-1-4-1h0c1-1 1-1 2-1l1-1z" class="S"></path><path d="M492 327c2-1 12 0 15 0h0c0 1-1 1-1 1-1 0-2 1-3 2-1 0-3-1-4 0h-5 0c-1-1-3 0-5 0l-10 1h0l12-3 1-1z" class="D"></path><path d="M479 362h4v1l3 2h0l-3 1v2l-6 3h-1-5l2-1c2 0 4-1 5-2-2 0-4 1-5 0 0-1 1-1 1-1l2-2c-1-1-2 0-3 0v-1c2 0 2-1 3-1v-1h2 1z" class="C"></path><path d="M479 362h4v1l3 2h0l-3 1c-1 0-2 0-2 1h-2c1-1 2-2 2-3h-3l1-2z" class="B"></path><path d="M485 387c1-1 3 0 5 0v1l-2 1v1h4 5v1h-8c-2 1-5 1-7 2l-1 1c0 1-1 1-3 2l1 1h0 0c0 1-1 1-1 1-1 0-2-1-4-1h-2l1-1c1-1 2-1 4-2h0-2-2-2v1h-2-1 0c1-1 1-2 2-2h3c2-1 8-1 9-3h-3c-3 1-6 1-9 1 2-1 4-1 6-2h1c3-1 5 0 8-1v-1z" class="H"></path><path d="M476 410h2l-1 1c-1 0-1 0-1 1 1 1 4 0 6 0v5l-1-2-2-1c-2 1-2 3-3 4l1 1h-1c0 2 1 0 0 2h-1c-1-1 0 0 0-2h-1v-1h-2l-3 1h-1l-4 1-1 1v-1c-1 0-1 0-1-1 1-1 1-1 1-2v-2h1v-1s1-1 2-1h2c2-1 3-1 4-1 2-1 3-1 4-2z" class="O"></path><path d="M464 415c1 0 2-1 3-1h1 1c1 0 1-1 2-1h2v1c-1 1-2 0-3 2v1l2-1c1 1 1 1 2 1v1h-2l-3 1h-1l-4 1-1 1v-1c-1 0-1 0-1-1 1-1 1-1 1-2v-2h1z" class="l"></path><path d="M480 343c1-1 2-1 4-1l1-1h2c1-1 1-2 2-2 1-1 4 0 6-1 1 0 2 1 4 0h0 0c0 1 0 1-1 1s-2 0-3 1h-1l2 2 1 1-1 1c-1 0 0 0-1 1s-2 1-3 2h-2c-1 0-1 1-2 0h-4c-3 1-11 3-14 2l1-1h2l4-2c2 0 3 0 4-1h0-1c-2 0-4 1-6 1h-2-1l-1 1h-1c1-2 3-2 5-2 1-1 1-1 2-1h2 1l1-1z" class="D"></path><path d="M476 362c-1 0-3 0-4 1h-1v-1h2c0-1 1-1 2-2h-1-2l-1 1-1-1 2-1c1 0 2 0 2-1h0-3 0v-1h2c1 0 1-1 1-1v-1c-1 0-3 1-4 1-2 1-3 1-5 1h0c1 0 2-1 2-1l4-1c1 0 1 0 2-1 1 0 1-1 2-1 3 0 6-1 10-2h-1c-1 2-4 1-5 3 1 0 1 0 2-1h2 3c1 1 2 1 3 1h4c-4 2-6 3-9 6 2 0 4-1 6-1 1-1 2 0 3 0-3 1-7 2-10 3h-4-1-2z" class="S"></path><path d="M482 404v-7c3 0 7-1 9-1v17 4c0 1-1 1-1 1v2h-11l-1-1c1 0 1 0 2-1l-1-1-2 2h0l-1-1c1-1 1-3 3-4l2 1 1 2v-5-8z" class="T"></path><path d="M482 417v2c2 0 3 0 4-1v-18c1 1 1 4 1 5v13h3v2h-11l-1-1c1 0 1 0 2-1l-1-1-2 2h0l-1-1c1-1 1-3 3-4l2 1 1 2z" class="C"></path><path d="M486 365c2 0 3 0 4 1 0 1 0 2 1 2l-1 5h4v1c-1 0-1 1-2 1l-1 2 2 1v1c0 1 1 1 2 1-1 1-1 1-2 1l-5 1v1h0 2l-8 1c-1 1-10 0-10 0-2 2-3 2-5 3l-4 2v-13h3l1-1h2c2-1 3 0 5-2h0 8c0-1 0-2-1-3-1 1-3 1-4 2h-1v-1h1l6-3v-2l3-1h0z" class="K"></path><path d="M482 384h-1l-1-1c3-1 6-1 8-1v1h0 2l-8 1z" class="B"></path><path d="M493 378v1c0 1 1 1 2 1-1 1-1 1-2 1h-1-9l1-1c3-1 6-1 9-2z" class="J"></path><path d="M484 380l-3-1h0c1-2 1-2 4-2h6l2 1c-3 1-6 1-9 2z" class="P"></path><path d="M486 365c2 0 3 0 4 1 0 1 0 2 1 2l-1 5v1c-1 1-5 1-7 1v-7-2l3-1h0z" class="T"></path><path d="M486 365c2 0 3 0 4 1 0 1 0 2 1 2-2 1-2 1-3 1l-2-1v-3h0z" class="Q"></path><path d="M472 384c-2 2-3 2-5 3l-4 2v-13h3 7c-2 2-6 0-9 2h0 1 5c1 0 1 0 1 1 1 0 3-1 3-1v1l-1 1v1l1 1c-1 1-2 0-3 0-2 1-4 2-5 2h-2 0c2 1 5 1 8 0z" class="U"></path><path d="M473 381h-11c3-1 7-3 11-1v1z" class="M"></path></svg>