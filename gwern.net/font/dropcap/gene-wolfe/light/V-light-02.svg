<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="80 68 600 604"><!--oldViewBox="0 0 740 752"--><style>.B{fill:#cbcbcb}.C{fill:#2c2c2c}.D{fill:#3c3b3c}.E{fill:#504f50}.F{fill:#deddde}.G{fill:#030303}.H{fill:#b9b9b8}.I{fill:#aeaeae}.J{fill:#9f9e9f}.K{fill:#616161}.L{fill:#2a292a}.M{fill:#858485}.N{fill:#0f0f0f}.O{fill:#1f1e1f}.P{fill:#484848}.Q{fill:#302f2f}.R{fill:#f4f4f4}.S{fill:#7b7a7b}.T{fill:#444344}.U{fill:#727273}.V{fill:#525152}.W{fill:#919091}.X{fill:#e7e6e7}.Y{fill:#fefefe}.Z{fill:#1a1b1b}.a{fill:#686768}</style><g class="B"><path d="M435 296c0 1 1 1 1 1-1 0-1 0-1-1zm-4-8v1 1-2zm7 13c1 1 1 1 1 2-1-1-1-1-1-2z"></path><path d="M437 299c1 1 1 1 1 2-1-1-1-1-1-2zm2 4c1 1 1 1 1 3-1-1-1-2-1-3z"></path></g><path d="M446 247l3 2h-2-2c0-1 0-1 1-2z" class="R"></path><path d="M637 199c1-1 3-1 4-2v4l-1-1s-2 0-3-1z" class="C"></path><path d="M425 281l7 6v1l-1-1v1c-1 0-1-1-2-2h0l-1-1c-2-1-3-2-3-4z" class="B"></path><path d="M494 420c1 2 2 5 4 7l-2 1-1-2c0-2-1-3-2-5l1-1z" class="F"></path><path d="M498 427c1 2 2 5 3 8l-4-5v-2h-1l2-1z" class="R"></path><path d="M470 582c-2 1-6 1-9 1l7-3h1 0c0 2 0 2 1 2z" class="D"></path><path d="M529 519v-2l1 1c1 0 3 1 4 2-1 1-2 3-1 4v1l-1-1v-1c-1-2-1-3-3-4z" class="H"></path><path d="M542 520c2-1 3-2 5-3 2 1 3 1 4 2h-2c-1 0-1 1-2 1s-1 1-2 1c-1-1-2-1-3-1z" class="D"></path><path d="M529 369l-1-1c0-1 0-1-1-2h0c-1 0-1 1-1 1l-1-1h-2c-1-1-1 0-1-1h1v-1l-1-1h1c1 2 4 2 6 3v1 2z" class="B"></path><path d="M517 496v2c1 1 1 2 1 3 0 0 0 1 1 2v6c-1 2 0 4 0 6-1-2-1-4-1-5 0-2-1-4-1-6h1v-1c0-1-1-2-1-3h0v-1-3z" class="F"></path><path d="M374 186h1v1l-1 14c0-2 1-5 0-7l-1-1c0-2 0-5 1-7z" class="N"></path><path d="M489 411l5 9-1 1c-2-3-4-5-5-9 1 0 1 0 1-1z" class="I"></path><path d="M626 179c1 1 2 2 2 3v1 1 1h0v-1h-1 0l-1 1-1-1v1l-3 3-2 1h0l1-1c2-2 4-5 5-9z" class="F"></path><path d="M456 249l12 1h-1c-1 1-1 1-3 1h2 5c-3 1-5 1-7 1l-9-2 1-1z" class="N"></path><path d="M642 167l6 5h-2l-1-1h-1c0-1-1-1-1-2l-2-1c0 1-1 1-2 1h-2c-1-1-4-1-5-1h0-1-1c-1 0-1 1-2 1h-1v1 1h0v1h0l-1-2c1-1 1-2 2-2 3-1 6 0 8 0s4 0 6-1z" class="B"></path><path d="M350 238c1 2 1 2 1 4-2 2-3 5-5 6-1 0-1-1-2-1 3-3 5-6 6-9z" class="X"></path><path d="M203 524c-2-2-5-3-7-4s-3-1-4-1v-1h4c3 0 6 3 8 5l-1 1z" class="D"></path><path d="M373 193l1 1c1 2 0 5 0 7l1 6c0 1 0 2-1 3h-1v-6-11z" class="G"></path><path d="M367 188c2 2 4 4 5 8v4c0-1-1-2-2-3 0-1 0-2-1-3h0 0c0-1-1-1-1-2-1-1-2-2-2-3s0-1 1-1z" class="F"></path><path d="M637 199c1 1 3 1 3 1-2 1-3 3-5 4h-7l-2-1h1c4 0 7-1 10-4z" class="Q"></path><path d="M652 196h-1c-5-3-6-10-7-15 3 4 4 10 8 14h0v1z" class="D"></path><path d="M375 207l1 14c-1 0-1 0-1 2l-2-13h1c1-1 1-2 1-3z" class="C"></path><path d="M437 240l9 7c-1 1-1 1-1 2l-5-3c-1-1-1-1-2-1v-1c-1 0-2-1-3-2h2c1 1 3 2 4 3 0-1 0-1-1-1-1-1-2-2-2-3l-1-1z" class="X"></path><path d="M256 432h2c2-1 5 0 7 0h0c0 2 0 4-1 6h-5c1-1 1-1 2-1v-2h0c1-1 0-1 0-1 0-1 1-1 2-2h-7zm215-181h1 0c1 1 2 1 3 1 1 1 1 1 1 2h-1l-1 1h-4c0-1-1-1-2-2h0 0 3v-1h-1c-1 1-2 0-3 0h-3c2 0 4 0 7-1z" class="B"></path><path d="M484 491v-1l-6-3-2-2c6 2 12 4 17 8h-2 0c-1 0-2-1-3-1h0c-1-1-3-1-4-1z" class="L"></path><path d="M139 240l1-1c0 1-2 4-3 5-1 2-1 4-1 6 0 4 2 7 3 10-2-3-5-8-5-11h0c1-1 1-3 2-4 0-2 2-4 3-5z" class="G"></path><path d="M640 200l1 1v1l-1 1-3 5v-2h0c-2-1-3-1-5-1-1 0-3 0-4-1h7c2-1 3-3 5-4z" class="J"></path><path d="M478 510l1-1c-2-2-3-4-3-7h0c1 0 1 0 1 1 1 3 4 5 5 8 1 1 1 1 1 2h0l3 3 1-1v3l-1-1c-2-3-5-4-8-7z" class="F"></path><path d="M479 424c3-1 5-1 8 0h-3 0l-1 1s0 1-1 0v1h0v1l-1 1c-1 1 0 1-1 1 1 1 1 1 2 1h0 0 2c-2 1-4 1-6 1h0c0-3 1-5 1-7zm50 95c2 1 2 2 3 4v1l1 1c-2 2-3 4-5 6h-1c2-4 1-8 2-12zm-271-96h5c0 1 1 3 1 4 1 1 2 0 1 1h-1c1 1 1 2 1 3h-6c1-1 1-1 3-1v-1h-1l1-1c-1-1-1-1-1-2h0c0 1-1 1-1 2h0-1c0-1 0-2 1-3v-1c-1 0-1 0-2-1z" class="B"></path><path d="M123 178c0-1 0-2 1-2v8c0 2-1 3-1 4s1 1 1 2h0c-4-3-6-8-7-13l5 9 1-8z" class="C"></path><path d="M296 493v-1c3 4 6 9 6 13l-1 1-9-4h4v1h0 1 0c1 1 2 1 3 1 0-1 0-1-1-2 0-2-1-4-2-6 0-1-1-2-1-3h0z" class="B"></path><path d="M334 267c-2-3-4-5-6-8 7 2 13 2 20 0l1 1c-2 1-5 1-7 2h-8-3c1 0 1 0 1 1l3 3-1 1z" class="N"></path><path d="M252 496c2-2 2-2 4-2-3 5-4 9-3 15l2 5-1 1c-2-5-4-9-3-14 1-2 1-3 1-5z" class="E"></path><path d="M491 506v-4h0l1-1c0 6-2 9-3 15-1 1 0 3-1 4-1 0-1-1-1-2v-3c-2-4-4-7-6-11l3 3c1 0 2 1 3 2 0 1 0 4 1 5 1-2 2-6 3-8z" class="L"></path><path d="M119 201c-1 0-3-1-3-2 1 1 3 1 4 1l1-1c-2-1-4-3-6-5-1-2-2-4-1-7v1c1 2 2 3 2 5 3 4 9 6 14 7h0c-1 1-7-1-8-1-1 1-1 2-2 2h-1z" class="G"></path><path d="M156 256h0v-2s-1 0-1-1v-1h1v1h0c1 1 0 1 1 2l1 1v1s1 1 1 2v1h0v1l1 1h2 0 0 3 0v-1h1c1 1 2 1 3 1h1v-1c-1 0-3-1-4-2h0l-1-1v-1c-1 0-1 0-1-1l-1-3c0 1 1 1 1 2 1 2 2 3 4 4h0c2 2 3 2 5 3-1 1-4 1-6 1h0c-1 0-1 0-1 1h-5l-1-1c-1 0-1-1-1-2s-1-2-1-3c-1-1-1-1-1-2h-1z" class="B"></path><path d="M263 423h1 2c1 1 1 2 1 3v2c1 1 1 2 1 3v1h-3c-2 0-5-1-7 0h-2c-1 1-1 1-2 1s-2 0-3 1h-1 0c1-1 2-2 4-2l1-1h2 1 1 6c0-1 0-2-1-3h1c1-1 0 0-1-1 0-1-1-3-1-4z" class="I"></path><path d="M263 423h1c1 2 3 5 3 7-1 1-1 1-2 1 0-1 0-2-1-3h1c1-1 0 0-1-1 0-1-1-3-1-4z" class="H"></path><path d="M601 182c0 1 1 1 1 1-1 4-1 8 0 11v2c1 5 4 9 7 12v1c-3-3-5-5-7-9-4-5-3-11-1-18h0z" class="K"></path><path d="M405 244h1c2 2 4 5 5 7h0-1-2c-2 0-3 2-4 4l2 2h-1c-2-1-5-1-7-1-2 1-4 2-6 4l-1 1h-1c2-3 6-6 9-6h3c1 0 1-1 1-2 1 0 1 0 2-1h-1l-1 1-1-1c1-1 4-3 6-4 0-1-1-1-2-2l-1-1v-1z" class="G"></path><path d="M513 440c1 2 1 4 1 5 1 4 0 9-1 13-1 2-1 4-1 6l1 1h0c-1 2-2 5-5 6h-1 1-2 0c4-6 7-14 7-21v-9-1z" class="H"></path><path d="M136 233h1 1 1c-1 1-3 2-3 3 0 2-1 3-1 4h1 3c-1 1-3 3-3 5-1 1-1 3-2 4l-2-4c0-4 2-9 4-12z" class="F"></path><path d="M338 144c6 1 11 2 17 2 2 0 5-1 7-1l2 1h-1c-8 3-17 2-25 0v-2z" class="Q"></path><path d="M212 518h3v2 8c0 1 1 1 1 2h-1c0-1-3-4-3-5-1-1-1-2-1-2-1-2-2-3-3-5h4z" class="F"></path><path d="M208 518h4c1 1 0 3 0 4l-1 1c-1-2-2-3-3-5z" class="H"></path><path d="M488 492c1 0 2 1 3 1h0 2c5 4 8 10 10 16l-1 1v-2c-1-1-1-2-2-2 0-1-1-1-1-2-1 0-1-1-2-2-1-3-4-4-6-7-1-1-1-2-3-3z" class="D"></path><path d="M141 214a79.93 79.93 0 0 0 13-13c-1 3-2 7-2 11l-2-2-1 1c-2 2-5 3-8 5v-2z" class="C"></path><path d="M253 416h0l2-3v1h0c-1 1-1 1-1 2h1c0 2-3 4-3 7-3 5-7 9-10 14 0-3 2-5 3-8 2-3 4-7 6-10 0-1 1-2 2-3z" class="F"></path><path d="M495 466v2c-4 4-8 5-14 7h4v1s1 0 1 1c-7-1-14 0-20 1 0-1 1-2 2-2 2 1 4 0 6-1 6-1 12-2 17-5 2-1 3-3 4-4z" class="Z"></path><path d="M485 475c11 1 20 4 27 12 3 3 4 7 6 11h-1v-2c-1-2-3-5-5-7-7-8-16-11-26-12 0-1-1-1-1-1v-1z" class="G"></path><path d="M141 216c3-2 6-3 8-5v4c0 1 0 2-1 3 0 1-4 3-5 4l1-2h1c0-1 2-2 2-3-1 1-3 0-5 1h1c1-1 1-1 2 0h0-1v1h-1c-1 1-3 0-4 0-1 1-2 1-3 1l-1-1h-4c4-1 7-2 10-3z" class="B"></path><path d="M500 544c1 0 1 0 2 1-2 5-5 10-9 15-5 7-10 12-18 16-1 1-3 2-4 3 1-2 4-4 6-5 11-8 18-17 23-30z" class="G"></path><path d="M573 262c1 0 2-1 2-1h1l2-2 1-1h0 1v-1c0-1 1-2 1-3s0-2 1-2v-1c0-1 1-1 1-1 0 1 1 3 0 5h0c-1 1-2 2-2 3-1 1-2 2-4 3h0-1c2 0 2 0 4-1 1-1 1-1 2-1h0 1l1 1c-1 0 0 0-1 1v-1h-1v1 3c1 0 2 0 2-1v-1c1-1 2-4 4-5 0 0 0-1 1-1 0-1 0-1 1-2v1l-1 1v1 1l-1 1h0l-1 1h0c-1 1-1 1-1 2h-1c-1 2-2 3-3 4l-3-3c-2-1-4 1-6-1zm-214-80c5 1 9 3 14 3l1 1c-1 2-1 5-1 7v11c-1-1-1-3-1-4v-4c-1-4-3-6-5-8l-2-2v-1l-1-1h-1s-1 0-1-1c-1 0-2-1-3-1z" class="B"></path><path d="M156 256h1c0 1 0 1 1 2 0 1 1 2 1 3s0 2 1 2l1 1h5 3 3l1 1-9 5c-1-2-1-2-3-3-1 0-2 0-3-1 0-1-1-2-1-3v-2-1-1c-1-1-1-2-1-3z" class="H"></path><path d="M518 498c1 0 1 1 1 1 0 1 1 2 1 3 1 2 0 5 1 7 0 7 0 15-3 22l-1 4s0 1-1 2v-2-1c2-6 3-13 3-19 0-2-1-4 0-6v-6c-1-1-1-2-1-2 0-1 0-2-1-3h1z" class="B"></path><path d="M149 211l1-1 2 2-2 7c-6 4-11 8-14 14-2 3-4 8-4 12l2 4h0l-3-4c0-9 6-17 12-23 1-1 5-3 5-4 1-1 1-2 1-3v-4z" class="O"></path><path d="M373 138l1-1c2-3 3-6 4-9l2 2c-1 1-1 1-1 2-1 1-1 3-1 5-1 6-5 11-10 14h-1c-1 1-1 1-2 1l-1 1h-2-2c-1 1-2 1-2 1h-1-6l-1-1h-2c-1 0-1 0-1-1h-2v-1h0 1c1 1 1 1 2 1h2c2 1 3 0 4 1 2 0 5 0 8-1h0c1 0 2 0 2-1h1c4-1 7-4 9-8v-1h0c1-1 1-2 1-3 1-1 0-1 2-2v1c0-2 1-4 1-5v-1l-1 2c-1 1-1 2-1 2l-2 2h-1z" class="X"></path><path d="M640 203l1 1c-4 6-11 13-18 15-7 1-15-1-21-4 0-1 1-1 1-2 2 1 4 2 7 3s7 2 11 2c6-1 12-6 16-10l3-5z" class="G"></path><path d="M484 430l2 1h2c1 1 3 1 4 2h-1 0c-2 0-3-1-5-1h-3v2l-1 1v2h-3l-2-1c-2-1-2-1-2-3l-2 2c-1 1-2 2-2 3h1l-1 1 1 1-1 1c-2 1-2 3-4 4 1-4 4-9 6-13v1c1 0 1 0 2-1h1 1c0-1 0-1 1-1h0c2 0 4 0 6-1z" class="J"></path><path d="M475 433h1c1-1 1-1 3-1h7-3v2l-1 1v2h-3l-2-1c-2-1-2-1-2-3z" class="F"></path><path d="M484 491c1 0 3 0 4 1h0c2 1 2 2 3 3 2 3 5 4 6 7 1 1 1 2 2 2v1c0 1 2 3 2 5h1v5c0 4 1 9 0 13 0-8 0-15-4-22h0c-2-3-4-5-6-8l-1 1c0 1 0 2 1 2l-1 1h0v4c0-6-3-11-7-15z" class="V"></path><path d="M359 182c-4-2-8-2-13-1-3 1-5 2-7 4 3-5 7-7 12-9v1c-2 1-5 2-7 3 4 0 7-1 11-1 8 1 15 8 21 1l-1 7v-1h-1l-1-1c-5 0-9-2-14-3z" class="O"></path><path d="M441 306c0 1 1 2 1 4v5c0 4 0 7-1 10v1c0 3-1 6-1 9-2 8-6 15-8 23-1 2-3 5-5 7l-4 7h0c-1-1-3-2-4-1h0l-1-1c-1-1-2-2-3-2h2 0 1c0 1 1 1 1 1l2 1h0c1 1 1 1 2 1l2-3c1-2 2-4 3-5l2-5c1-3 2-5 3-8l2-2v-2l1-1v-1-1c1-1 1-2 1-2l1-3v-2l1-1v-3c1 0 0-1 0-2 1-1 1-1 1-2v-3c1 0 1-1 1-2v-2c1-1 0-4 0-5 1-1 1-3 1-4l-1-1v-2h0v-1c-1-1 0-1 0-2z" class="B"></path><path d="M376 221h1c3-7 6-14 10-21 1-3 3-6 5-8 1-2 3-3 5-4l5-5c-2 4-7 7-10 11-3 3-4 7-6 10-3 6-8 14-8 21-1 2 0 4 1 6l1 1c-3-3-4-5-5-9 0-2 0-2 1-2z" class="Z"></path><path d="M573 262c2 2 4 0 6 1l3 3c1-1 2-2 3-4h1c0-1 0-1 1-2h0l1-1v1 3h1 1 0c1 0 2-1 3 0h-2l1 1s0-1 1-1h0v1c-3 2-8 5-11 4l-1 1c-4-2-7-4-11-5 1-1 2-1 3-2z" class="H"></path><path d="M225 501h1v1h0c-1 1-1 1-1 2v2 1s-1 1 0 1v1h-1v3h0l1 1h0v-1-1l1-1v-2-1c1-1 1-2 1-3v2c-2 8-3 18-1 26 1 3 2 7 3 10v1c-1-1-1-3-2-4l-2-6c-2-7-3-14-3-21 1-4 1-8 3-11h0z" class="B"></path><path d="M348 239v-7c-1 0-1-1-1-2v-2-1c0-1 0-1-1-1-1-3-2-5-4-7-1-1-3-2-5-3-1-1-1 0-2-1v-1c1 0 1 0 2 1h0c1 0 2 0 3 1h0l2 2h2c3 1 6 4 7 7v7c-1 2-2 5-3 7z" class="F"></path><defs><linearGradient id="A" x1="639.716" y1="200.729" x2="648.392" y2="204.203" xlink:href="#B"><stop offset="0" stop-color="#131414"></stop><stop offset="1" stop-color="#333032"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M652 195v1c3 1 8 0 11 0-4-2-6-4-8-8 3 2 4 5 7 6l1 1h1v1h-1 0c-3 2-7 1-10 2-1 1-3 2-4 3-1 0-2 1-2 2-1 2 3 7 4 8-4-2-6-5-9-8l-1 1-1-1 1-1v-1-4c-1-3-1-6 0-9 1 5 1 9 3 14 3-1 6-3 8-5v-1-1z"></path><path d="M435 242c-1 0-2-2-3-3h0l-3-3c0-1 1 1 0-1h-1v-1s-1-1-1-2h0c-1-1 0-2 0-3-1-1-1-5-1-6l1-1h1v4-4 1c0-2 1-3 1-4 0 9 2 14 8 21l1 1c0 1 1 2 2 3 1 0 1 0 1 1-1-1-3-2-4-3h-2z" class="R"></path><path d="M429 219c1-6 2-11 7-15 2-1 5-3 6-2h0c-5 2-8 6-10 11-2 6-2 12 1 18 3 8 11 15 19 17l4 1-1 1-6-1-3-2-9-7c-6-7-8-12-8-21z" class="G"></path><path d="M497 396c-1-2 0-5-1-8l1-1h0v-2c1-1 0 0 0-1l1-1v-1h1l-1-1h0c0-1 0-2 1-3v-1c0-1 1-3 2-4h0c1-2 2-3 3-3l3-3 3-2h1c1-1 2-1 3-1 0-1 1-1 1 0h0 1l-1 1h-1l-1 1c-1 0-1 0-2 1-2 1-3 3-5 4v1h1l1-1h0 1v-1h1c1 0 1 0 2-1h2c1-1 1-1 2-1h4c1 1 3 0 4 1h1c1 0 1 0 2 1h0c-8-2-13-1-20 3l-3 2-4 6c-3 5-3 9-3 15z" class="B"></path><path d="M500 381c-1-3 0-5 2-7v-1c1-1 1-2 3-3v1l-1 1c-1 1-1 1-1 3h1l-4 6z" class="H"></path><defs><linearGradient id="C" x1="249.194" y1="456.417" x2="243.286" y2="454.735" xlink:href="#B"><stop offset="0" stop-color="#292726"></stop><stop offset="1" stop-color="#404041"></stop></linearGradient></defs><path fill="url(#C)" d="M247 445v1l3-3h1 1v1h0v2c-2 1-3 2-4 4 0 1-1 2-1 3-1 3-1 8 1 11 1 4 5 7 9 8l-1 1c-2-1-4-2-6-4-4-2-6-7-7-11 0-4 1-9 4-13z"></path><defs><linearGradient id="D" x1="163.866" y1="186.545" x2="158.582" y2="207.816" xlink:href="#B"><stop offset="0" stop-color="#bdbcbd"></stop><stop offset="1" stop-color="#f0f2ef"></stop></linearGradient></defs><path fill="url(#D)" d="M161 180c1 3 3 7 4 11v1c1 3 0 6-1 9l-3 14c0-2 1-4 0-6v1c-1-1 0-6 0-7 0-6 0-12-1-17h0v-3c0 2 1 3 2 5v-1c0-1-1-2-1-3v-2h0v-2z"></path><defs><linearGradient id="E" x1="619.824" y1="171.056" x2="626.194" y2="179.235" xlink:href="#B"><stop offset="0" stop-color="#b9b8b8"></stop><stop offset="1" stop-color="#ebeaeb"></stop></linearGradient></defs><path fill="url(#E)" d="M624 168l2 2 1 2h0s1 1 1 2h0v1c0 1 0 1-1 2 0 0 0 1 1 2h0c0 1 1 1 1 2v1s0 1-1 1v-1c0-1-1-2-2-3-1 4-3 7-5 9-1-1 0-2 0-3h1c-1-3-1-7-2-10v-2h0v-1c1-1 1-2 2-3 1 0 1 0 2-1z"></path><path d="M622 185c2-3 3-5 4-8h0 1l-1 2c-1 4-3 7-5 9-1-1 0-2 0-3h1z" class="C"></path><path d="M487 398l9-20c1-3 3-8 6-11h0c2-3 4-3 6-5 6-2 12-2 18 0h-3-1s-1 0-1-1c-1 0-4 0-5 1h-3c-1 0-1 0-2 1h-1l-2 1s-1 0-1 1h0c-1 0-1 1-1 1l-1 1c0 1-1 1-2 1-2 2-3 3-4 6-1 1 0-1-1 1h0c0 1-1 2-1 2v1h0l-1 2-1 1v1c0 1-1 2-1 3-1 1-1 2-2 4h0c-1 2-2 4-2 5v1c0 1 0 2-1 3v2 6 5h0c0 1 0 1-1 1-1-2-2-5-2-8 0-1 0-1-1-2 0-1 2-3 2-4z" class="B"></path><path d="M485 402c0-1 2-3 2-4 2 4 0 8 2 13h0c0 1 0 1-1 1-1-2-2-5-2-8 0-1 0-1-1-2z" class="J"></path><defs><linearGradient id="F" x1="482.242" y1="406.527" x2="496.06" y2="424.998" xlink:href="#B"><stop offset="0" stop-color="#cbcaca"></stop><stop offset="1" stop-color="#f2f1f2"></stop></linearGradient></defs><path fill="url(#F)" d="M485 402c1 1 1 1 1 2 0 3 1 6 2 8 1 4 3 6 5 9 1 2 2 3 2 5v1c-1 0-1 0-1 1l-2-2c0-1-1-2-2-3-3-3-6-7-7-11l-1-2c1-2 2-6 3-8z"></path><path d="M455 256h-2c-1 0-2-1-2-1-3 0-5-1-7-2l-2-1c-1 0-3 0-4-1l1-1 9 3 1 1h3c1 1 2 1 3 1h4c4 1 8 0 11 0h4l7-2c1 0 2-1 3-1 0 1 1 1 1 1l-1 1v2l-1 1c-1 1-3 4-5 5l-2 2v-1-4c0-2-1-2-2-3-3 1-6 0-8 0-4 1-8 1-11 0z" class="J"></path><path d="M474 256c1 0 2-1 4 0 1 2 1 4 0 6l-2 2v-1-4c0-2-1-2-2-3z" class="I"></path><defs><linearGradient id="G" x1="106.593" y1="206.133" x2="117.285" y2="210.456" xlink:href="#B"><stop offset="0" stop-color="#a9a8a9"></stop><stop offset="1" stop-color="#d7d6d6"></stop></linearGradient></defs><path fill="url(#G)" d="M119 201h1c1 0 1-1 2-2 1 0 7 2 8 1h1v1h-4-1s-1 0-1-1h-3v1c0 1-1 1-2 1h-1l1 1c0 1-1 1-1 2h0l-1 1v1 1c0 1 0 2-1 2 0 1 1 1 0 2h0c0 1 0 2-1 2 0 1 1 1 2 1 1 1 2 2 3 2h2 0 2l1 1h-3c-7-1-13-6-17-11 0-1 0-1 1-1 2 0 3 0 5-1h5 0l1-1v-1c1-1 1-1 1-2z"></path><defs><linearGradient id="H" x1="336.547" y1="264.864" x2="345.613" y2="267.142" xlink:href="#B"><stop offset="0" stop-color="#b9b7b8"></stop><stop offset="1" stop-color="#dad9d9"></stop></linearGradient></defs><path fill="url(#H)" d="M349 260c4-1 7-2 10-4-1 1-1 2-2 2h-1c-1 1-2 2-3 2l-4 1c-1 1-3 2-4 3h0c0 1-1 1-1 2h0c-1 1-1 1-1 2v1c-1 0-1 0-1 1v2h1l-1 1v-1 2h0 0v1 2h0v1 3c-1 2 0 4 0 5-1-3-1-6-1-9 0-1 0-3-1-4h-1l-1-1c0-1-1-1-1-1-1-1 0-1-1-2h-1l2-2c1-1 1-2 1-3l-1-1h-5c0-1 0-1-1-1h3 8c2-1 5-1 7-2z"></path><path d="M332 263c0-1 0-1-1-1h3 3v1h-5z" class="F"></path><path d="M507 422c3-4 6-7 11-10 0 1 0 1-1 1v3c-1 1-2 5-1 7 6 11 5 26 1 38l-3 6v-2h-1 0c2-2 2-5 3-8 2-7 3-14 2-21 0-5-1-8-2-12-2 3-1 7-1 11l-1 10c0-1 0-3-1-5 0-8 1-17 3-26-3 3-5 6-7 11v-1l-1-2h0-1z" class="N"></path><defs><linearGradient id="I" x1="495.236" y1="529.097" x2="508.446" y2="523.263" xlink:href="#B"><stop offset="0" stop-color="#1e1d21"></stop><stop offset="1" stop-color="#484846"></stop></linearGradient></defs><path fill="url(#I)" d="M499 504c0 1 1 1 1 2 1 0 1 1 2 2v2l1-1c1 1 1 3 1 4 2 8 3 17 1 24l-3 8c-1-1-1-1-2-1l1-8c1-2 1-5 1-8 1-4 0-9 0-13v-5h-1c0-2-2-4-2-5v-1z"></path><path d="M505 537c-1 0-1-1-1-1v-3-7c0-4-1-8 0-11v-2c2 8 3 17 1 24z" class="L"></path><path d="M513 465h1v2l-2 4c11 4 19 10 24 20 3 7 4 15 2 22 1 1 3 0 4 0 3 1 6 2 9 4-2 0-4-2-7-2h-1c-1-1-3-1-5-1-1 4-3 7-5 10-1-1 0-3 1-4 2-4 4-8 4-13 0-10-4-19-11-26-4-4-12-10-19-10h-1 1c3-1 4-4 5-6z" class="G"></path><path d="M238 520h0v3c0 3 1 9 3 12 2 6 3 11 6 16 4 12 15 22 27 28l-1 1c-2-2-5-3-7-5-7-4-11-9-16-15-9-12-14-25-12-40z" class="T"></path><path d="M429 265h-1c-1-1-1-2-2-3-2-3-5-5-7-8-1-3-2-5-4-8 0-1-1-1-1-2l-2-2h0v-1c-1-1-2 0-4-1h0c1-1 1-1 2-1h0c2 0 3 0 4 1h1c1 1 1 1 2 1 1 2 3 4 5 5 0 4 1 6 3 10 1 1 1 2 2 3h1c0 2 0 4 1 5h0v1z" class="B"></path><defs><linearGradient id="J" x1="318.286" y1="264.687" x2="328.113" y2="269.224" xlink:href="#B"><stop offset="0" stop-color="#282626"></stop><stop offset="1" stop-color="#545354"></stop></linearGradient></defs><path fill="url(#J)" d="M314 259c1 2 3 3 5 3 3 2 10 6 14 5h1l1-1-3-3h5l1 1c0 1 0 2-1 3l-2 2h1c1 1 0 1 1 2 0 0 1 0 1 1l1 1v1c-2 0-4 0-5-1h-1v1c-2 0-8-4-10-5l-9-8h0l-1-1 1-1z"></path><path d="M332 263h5v1c-1 0-1 1 0 1-1 1-1 2-2 3l-2-1h1l1-1-3-3zm3 6h1c1 1 0 1 1 2 0 0 1 0 1 1l1 1v1c-2 0-4 0-5-1h0c-2-1-2-2-3-2v-1c1 0 2-1 4-1zm104 240c3-4 5-9 7-14 1-3 2-7 4-9 1-2 2-2 4-3v2 1l3-1 1 1h-1-2c0 1-1 1-2 1l1 1h0v1-1h2l1-1h1 0 2-1c-2 1-3 1-5 3-1 2 1-1-1 1h0l-1 1h0l-1 1-1 1h0c0 1-1 1-1 2l-2 5v1 2s1 0 1-1h2c1-1 1-1 3 0-5 1-9 3-13 7l-1-1z" class="B"></path><defs><linearGradient id="K" x1="489.269" y1="253.284" x2="474.783" y2="252.652" xlink:href="#B"><stop offset="0" stop-color="#8a8a8a"></stop><stop offset="1" stop-color="#bebdbd"></stop></linearGradient></defs><path fill="url(#K)" d="M490 244l1 1h1c-1 2-2 3-3 5l-4 10v-1l-1 1-1-1-1 1 1-3 1-1v-2l1-1s-1 0-1-1c-1 0-2 1-3 1l-7 2 1-1h1c0-1 0-1-1-2-1 0-2 0-3-1h0-1-5-2c2 0 2 0 3-1h1l6-1c6 0 11-2 16-5z"></path><path d="M490 244l1 1c-5 4-13 5-19 6h-1-5-2c2 0 2 0 3-1h1l6-1c6 0 11-2 16-5z" class="G"></path><defs><linearGradient id="L" x1="177.713" y1="257.769" x2="169.045" y2="260.499" xlink:href="#B"><stop offset="0" stop-color="#a1a0a1"></stop><stop offset="1" stop-color="#c1c0c1"></stop></linearGradient></defs><path fill="url(#L)" d="M161 248l1-1c1 2 1 4 3 6v-1h1v-1-1c1 1 2 2 3 2 0 1 0 1 1 2l1-1h0l1-1h0v-2c2 6 5 10 10 13-3 1-6 1-9 2l-1-1h-3-3c0-1 0-1 1-1h0c2 0 5 0 6-1-2-1-3-1-5-3h0c-2-1-3-2-4-4 0-1-1-1-1-2s-2-3-2-5z"></path><path d="M161 248l1-1c1 2 1 4 3 6 2 0 3 3 4 4s2 1 3 1l1 1h-2c1 1 2 0 3 1v1h1v1h0s-2-1-2 0c-2-1-3-1-5-3h0c-2-1-3-2-4-4 0-1-1-1-1-2s-2-3-2-5z" class="J"></path><defs><linearGradient id="M" x1="584.356" y1="170.058" x2="584.587" y2="194.926" xlink:href="#B"><stop offset="0" stop-color="#9d9c9c"></stop><stop offset="1" stop-color="#d5d4d4"></stop></linearGradient></defs><path fill="url(#M)" d="M581 214l-3-16c0-4 0-9 1-12 2-7 8-14 12-19l1 2c-2 3-4 6-6 10 0 2-1 4-1 6-1 5-2 10-1 15v11h0c0-2 0-5-1-6 0-2 1-6 0-7v-4h-2l-1 1c-1 1-1 3-1 4l1 1h-1l1 1h0v2 3 1h1v3 4z"></path><path d="M514 445l1-10c0-4-1-8 1-11 1 4 2 7 2 12 1 7 0 14-2 21-1 3-1 6-3 8l-1-1c0-2 0-4 1-6 1-4 2-9 1-13z" class="Y"></path><path d="M610 238l-2-3c-1-1-4-3-5-4 3 1 5 3 7 6-1-4-4-9-8-12-2-2-4-3-6-5 0 0-1 0-1-1-1-1-1-4-1-5-1-5-3-10-3-15 1 1 1 3 2 4 2 4 6 8 10 10 0 1-1 1-1 2-3-2-5-3-7-5l1 7s0 1 1 2c1 2 4 4 6 5 4 4 8 10 9 15 0 2 1 4 1 5-1 2-2 3-2 4-2 3-3 6-4 9 1-6 2-10-1-16h0l3 3c1-1 0-3 0-4s1-2 1-2z" class="C"></path><path d="M610 238c1 1 1 3 2 4v1c0 1 0 2-1 3 0 1 0 1-1 1 0-1-1-2-1-3 1-1 0-3 0-4s1-2 1-2z" class="R"></path><path d="M259 438h5l8 1c-2 1-4 3-6 4-3 4-5 7-5 12h-1l-1-1h0c-1-2 1-7 2-9v-1c-3 0-6 0-9 2v-2h0v-1h-1-1l-3 3v-1h0c3-5 7-6 12-7z" class="L"></path><path d="M425 281c-7-6-11-13-18-19-2-1-3-2-5-3-1 0-3 0-5-1 6 0 11 2 15 6 3 4 6 8 9 11 7 8 16 14 26 15h5c2 0 3-1 5-1-2 4-5 10-5 15h0-1c0-4 1-8 3-12-5 0-8 0-12-1-2 0-5-2-7-3l-3-1-7-6z" class="C"></path><path d="M204 523c5 7 8 15 13 22 12 18 34 33 55 36h3l6 2c-4 0-8 0-12-1-15-2-31-12-41-22-6-6-12-13-16-20-3-5-5-11-9-16l1-1z" class="G"></path><path d="M304 82c4 4 7 9 9 14l2 3-2 2c3 5 6 11 10 15h-74 5 11 36 8c1 0 2 0 3-1h5 2c-1 0-1 0-2-1 0-1-1-1-1-2l-1-1v-1-1c-1 0-1 0-1-1v-2h-1c0-1 1-2 0-3v-1c-2-3-3-5-5-8 0-1-1-2-2-2 0 0 0-1-1-1-1-3-3-4-3-7h0l-2-1h3l1-1z" class="B"></path><path d="M304 82c4 4 7 9 9 14l2 3-2 2c-3-6-6-12-11-17l-2-1h3l1-1z" class="L"></path><path d="M469 580c24-2 45-18 58-37 3-5 5-10 8-14 2-3 5-6 7-9 1 0 2 0 3 1-9 5-12 15-17 23-8 13-21 24-35 31-5 3-9 5-14 6-3 1-6 1-9 1-1 0-1 0-1-2z" class="C"></path><path d="M324 142c0-1-1-2-1-3 1 0 2 0 3 1 3 2 8 3 12 4v2c-6-2-9-3-14-6 7 11 18 20 31 23 7 2 14 1 20-2l3-3c-1 2-2 4-3 5-2 2-5 3-8 4s-6 5-9 7l13-3h0c-4 2-8 2-11 3l-9 3v-1l6-3c3-1 3-5 4-7-2-1-4-1-6-1-5-1-10-4-14-6s-8-5-10-9l-3-3c-1-2-3-4-4-5z" class="L"></path><path d="M348 259c6-2 10-5 16-7 3-2 7-3 10-2h1 0c-2-3-6-7-8-9-5-2-9-2-14-1 2-1 4-3 5-5 1-4 1-9 0-12-2-5-6-9-11-12-4-2-10-2-15 0-1 1-2 1-3 2h0c2-2 4-3 6-4 5-1 10-1 14 2 5 3 9 7 10 13 1 5 1 9-1 13h0c3 0 6 1 9 3 2 1 4 3 6 5 4 5 8 10 10 16-2-3-5-8-10-9s-10 1-14 4h0c-3 2-6 3-10 4l-1-1zm181 108c1 0 1 0 2-1 1 1 2 3 3 4 3 5 6 11 6 17 0 1 0 1-1 2 0-5-1-10-6-14-4-3-11-4-17-4-4 1-10 4-13 8-4 5-5 12-4 19 1 6 3 13 6 18 0 2 1 4 2 6h1 0l1 2v1l-1 2c-1-1-1-3-2-4-1-3-2-5-3-7-3-6-5-13-6-20 0-6 0-10 3-15l4-6 3-2c7-4 12-5 20-3 2 1 4 2 5 3 2 1 3 2 5 3l-1-1c-1-2-4-6-7-6v-2z" class="G"></path><path d="M221 371c6 0 11 1 16 5s8 10 8 16c1 4 0 9-2 13v-2c1 0 0-1 0-2l1-1h0v-1c-1-1-2-2-2-4l-2-2v-1c0-1-1-2-1-3l-2-2v-2h0l-1-2c-1-1-1-2-2-3h0c-1-2-2-3-3-3h0v1c-1-1-1-1-2-1h0-1 1l-1-1h0l-5-4s-1 0-2-1z" class="B"></path><defs><linearGradient id="N" x1="151.316" y1="166.186" x2="143.243" y2="199.628" xlink:href="#B"><stop offset="0" stop-color="#afaeae"></stop><stop offset="1" stop-color="#e7e7e7"></stop></linearGradient></defs><path fill="url(#N)" d="M143 162c5 5 7 10 8 17 1 1 1 0 1 1v1 1 2l1 1h0v2c1 3 1 10-1 13v1l-2 2h-1v-1-5c0-1 1-5 0-6v-4l1-1c-1-1-1-1-2-1l-1-1c0 1-1 0 0 1 0 1-1 3 0 3v2 1c0 1 0 2-1 3v1h-1c1-2 1-4 1-7s0-6-1-9c-1-4-2-8-2-12 0-2-1-4 0-5z"></path><path d="M131 219c-5 1-9 1-14-1-7-3-12-8-15-15-3 4-5 6-9 8 2-3 4-5 5-8-2-2-5-4-7-5h-6c-1 0-3 0-4-1h0 7c7 0 8-8 12-13-2 5-4 10-8 13l8 5c1-1 1-2 2-4 2-2 0-6 1-9 1 4 0 7 3 10 3 4 6 5 11 6h0 0-5c-2 1-3 1-5 1-1 0-1 0-1 1 4 5 10 10 17 11h3c5 0 10-2 15-4v2c-3 1-6 2-10 3z" class="G"></path><path d="M103 203l-1-1c0-1 0-1 1-2 2 1 4 3 6 4h-3c-1 0-1-1-3-1z" class="M"></path><path d="M103 203c2 0 2 1 3 1h3l3 1c-2 1-3 1-5 1-1 0-1 0-1 1-1-1-2-3-3-4z" class="J"></path><defs><linearGradient id="O" x1="243.345" y1="508.219" x2="250.031" y2="519.074" xlink:href="#B"><stop offset="0" stop-color="#b1b0b0"></stop><stop offset="1" stop-color="#d8d8d7"></stop></linearGradient></defs><path fill="url(#O)" d="M241 521c0-5 2-9 5-14 1-2 3-5 5-6-1 5 1 9 3 14v1c1 1 0 4 1 6 1-1 1-2 1-3 0 0 1 0 2-1l-1 2c0 1-1 2-1 2 0 1 0 1-1 1l-1-1v-1-3c-1-1-2 0-3 0h0-1c-1 0-2 1-2 1l-1 1c-1 0-1 1-2 1-1 2-1 2-1 3h1l-1 1h1v3h0 0v1 3h0v3 2l-1 1c-3-5-3-11-3-17z"></path><defs><linearGradient id="P" x1="476.016" y1="260.367" x2="455.673" y2="262.11" xlink:href="#B"><stop offset="0" stop-color="#b5b4b4"></stop><stop offset="1" stop-color="#e9e8e8"></stop></linearGradient></defs><path fill="url(#P)" d="M455 256c3 1 7 1 11 0 2 0 5 1 8 0 1 1 2 1 2 3v4 1c-7 3-14 5-22 5v-1l1-1c1 0 1-1 1-2l1-1c0-1 0-1 1-2v-1l-5-1h4 1v-1c-2-1-2-1-3-3h0z"></path><defs><linearGradient id="Q" x1="614.343" y1="172.295" x2="614.818" y2="185.061" xlink:href="#B"><stop offset="0" stop-color="#9a9999"></stop><stop offset="1" stop-color="#d1d0d1"></stop></linearGradient></defs><path fill="url(#Q)" d="M619 172l1 1v2c1 3 1 7 2 10h-1c0 1-1 2 0 3l-1 1h0l-1 1-1 1v-1l-2 1c0-1-1-1-1-2h-1l1-1h-1c0-1 1-1 1-1 0-1-1-2-1-2h-1-1c-1 0-1 0-2 1l-1 1c0 1-1 2-2 3v1l-2 3h1l-2 2h-2v-2c0-2 0-5 2-8 2-4 5-7 8-9l3-3c1-1 2-2 4-2z"></path><path d="M620 189l-2 1-1-1c1 0 2-1 2-2v-3c0-3 0-7 1-9 1 3 1 7 2 10h-1c0 1-1 2 0 3l-1 1zM473 435l2-2c0 2 0 2 2 3l2 1h3 3c5 2 9 4 12 10 3 4 3 9 2 14-1 2-2 5-4 7v-2h0c2-5 3-10 1-14-1-4-4-7-7-8-2-1-4-2-6-1-1 2 0 6 1 8h-2l-2-4c-1-2-5-6-8-7l-1-1 1-1h-1c0-1 1-2 2-3z" class="Q"></path><path d="M473 435l2-2c0 2 0 2 2 3l2 1h3 3c-3 0-6 0-9 1-1 0-3 1-4 0h-1c0-1 1-2 2-3z" class="H"></path><path d="M472 438h-1c0-1 1-2 2-3v3h2 1c-1 0-3 1-4 0z" class="I"></path><defs><linearGradient id="R" x1="636.072" y1="203.687" x2="619.467" y2="209.958" xlink:href="#B"><stop offset="0" stop-color="#989697"></stop><stop offset="1" stop-color="#d6d6d6"></stop></linearGradient></defs><path fill="url(#R)" d="M610 216h3c1 0 1 1 2 1 1-1 1 0 1 0h5 1c1 0 1-1 2-1 0-1-1-1-1-2-1 0-2-1-3-2v-2-1l1-2h0c0-2 1-4 2-5v-1h-1v-1h-1-2c-2-1-5 1-7 0v-1l8-3h1c1-1 3-1 4-2h0l3-3 1-1c0-1 1-2 1-3h1c0 2 0 3-2 5h0c0 2-2 3-3 3v1c-2 1-3 1-4 2h-2 2c1 1 5 1 7 1l-4 2v1c1 0 1 0 2 1h-1l2 1c1 1 3 1 4 1 2 0 3 0 5 1h0v2c-4 4-10 9-16 10-4 0-8-1-11-2z"></path><defs><linearGradient id="S" x1="249.656" y1="494.01" x2="252.56" y2="498.409" xlink:href="#B"><stop offset="0" stop-color="#2d2b2d"></stop><stop offset="1" stop-color="#444543"></stop></linearGradient></defs><path fill="url(#S)" d="M241 508c2-5 4-10 8-13 4-4 8-7 14-8 1-1 3-1 4-1 0 1-4 2-5 2l-6 6c-2 0-2 0-4 2 0 2 0 3-1 5-2 1-4 4-5 6-3 5-5 9-5 14 0 6 0 12 3 17 1 1 2 3 2 5l1 3v5c-3-5-4-10-6-16-2-3-3-9-3-12v-3h0c-1-4 1-9 3-12z"></path><path d="M252 496c0 2 0 3-1 5-2 1-4 4-5 6-3 5-5 9-5 14-1-2 0-5 0-7 1-2 1-4 2-6h0c2-4 6-8 9-12z" class="S"></path><path d="M241 508l1 1-1 3c-1 2-1 4-1 6 0 6 0 12 1 17-2-3-3-9-3-12v-3h0c-1-4 1-9 3-12z" class="E"></path><path d="M170 222v4h1 1v5c-1 2-1 4-1 6 1 2 0 3 1 5 1 7 3 15 9 20l2 1-1 1c1 0 3-1 4 0-8 0-16 3-23 8-4 3-8 7-12 9 2 1 5 1 8 1 1 0 3-1 3-1h-1c-3 4-6 9-5 13 0 2 0 4 1 5 2 6 5 11 10 14l4 3-3-12c1 2 2 4 3 5 1-3 2-5 3-8s4-6 6-8h0l-1 2c-3 4-5 8-7 12 0 1-1 3-1 4v1c1 2 1 5 3 7h0c-4-2-8-5-11-8-5-5-8-11-8-18-1-2-1-3-1-5v-2h-2c-2-1-3-2-3-4-1 0-2-1-2-1 6 1 12-9 17-11l9-5c3-1 6-1 9-2-5-3-8-7-10-13-1-3-1-7-1-10-1-6-2-12-1-18z" class="O"></path><path d="M149 282c2 1 5 2 8 3 0 2-2 6-2 8-1-2-1-3-1-5v-2h-2c-2-1-3-2-3-4z" class="F"></path><path d="M226 414l1 1v1c1 2 1 4 2 6 1 4 2 8 2 12 0 5-1 10 0 15v1c-1-1-1-2-1-3-1-5-1-10-1-15 0-3-1-5-1-8-4 12-4 27 1 38 1 2 4 8 7 9h1 0c-11 3-20 8-26 17-5 7-7 16-5 24 0 2 1 4 2 6s2 3 3 5c0 0 0 1 1 2-4-3-5-6-7-10-5-1-9 1-14 2 4-3 8-4 13-3h1c-2-8-1-16 3-23 5-10 13-17 24-20-8-10-9-26-7-38 0-3 2-7 2-11 0-2-1-6-1-8z" class="D"></path><path d="M492 501c-1 0-1-1-1-2l1-1c2 3 4 5 6 8h0c4 7 4 14 4 22 0 3 0 6-1 8 0-2 1-5 0-7v-2-1c-1-1 0-3-1-5l-1-1c0-1-1-1-1-2-1-1 1 1-1-1l-2-2c-2-1-2-1-5-1v2c-1 0-1 0-1 1v1c0 2 0 3-1 4h-1c-1-1-1 0-2 0-1-1-1-1-2-1-2 0-2 0-2-1-1-1-1-1-2-1 0 0-1 0-1-1h-1v-1c-1 0-1 0-2-1h-1l-1-1h-1 0 0c-1-1-2-1-2-1h-1c-1-1-2-1-3-2 0-1 0-2 1-3l14 6c2 1 3 2 5 2l1 1c0 1 0 2 1 2 1-1 0-3 1-4 1-6 3-9 3-15z" class="B"></path><path d="M213 366l3 1c-1 1-1 1-2 1 1 0 3 0 4 1v1c6-2 12-1 18 2 3 2 5 4 7 7 3 4 4 11 4 16l-1 6c-2 10-7 17-10 27v-1c-2-5-5-9-9-12l-1-1-1-1 1-1c4 3 7 6 10 11 2-6 6-12 7-18 2-4 3-9 2-13 0-6-3-12-8-16s-10-5-16-5c-2 1-4 1-6 2-5 1-9 4-10 10-1 1-1 3-2 5 0-4 1-9 3-13 2-3 5-6 7-9z" class="G"></path><path d="M218 370c-5 2-8 3-12 7v-1c2-2 5-7 8-8 1 0 3 0 4 1v1z" class="X"></path><path d="M533 292c3-4 10-6 15-7 2 0 9 0 10 1s2 2 2 3c2 2 3 4 4 5h0c-1 0-2 0-2-1h-1l-1-1v1c-1-1 0-1-1-1h-1c-1 0-6-1-7 0h-3c-1 0-1 0-2 1h-1-1v1h-1-1l-2 1c-1 1-2 2-3 2-1 2 0 1-1 1-1 1-3 3-3 4-1 0-2 1-2 2l-1 1c-1 2-2 3-3 3v1l-1 1v1l-1 1c-1 2-2 5-3 7 0 2-1 3-1 4l-2 3v1l-1 1 15-36z" class="B"></path><path d="M453 503c7-2 14-2 20 3 1 0 3 2 3 3l2 1c3 3 6 4 8 7-2 0-3-1-5-2l-14-6h-2c-4-1-7-1-10-2l-9 3c-6 3-9 7-12 12l-1-1 5-12h0 1l1 1c4-4 8-6 13-7z" class="Q"></path><path d="M455 507c1-1 6-1 7 0h1 1c1 0 1 0 2 1h2c1 1 2 1 3 1h1v1h1 1 1c1 1 2 1 3 2l1-1h-1l-2-2 2 1c3 3 6 4 8 7-2 0-3-1-5-2l-14-6h-2c-4-1-7-1-10-2z" class="D"></path><path d="M343 316c1-4 0-9-1-13 1 0 1-1 2-2v5c-1 0 0 1 0 1v2c0 1 1 7 0 7v3c0 1 0 2 1 2 0 1-1 2 0 3v2c0 1 0 1 1 2 0 1-1 3 0 4v3c1 1 0-1 1 1h0c0 1 1 2 1 3l1 2s0 1 1 1v1c0 1 0 1 1 1v1 1l1 2 1 2 1 2c0 1 1 3 1 4l1 1v2c1 0 1 1 1 1l1 2h0v1l1 1v1 1h1v2h1v2l1 1h0v1l1 2c0 1 1 1 1 2v1l2 1v1c1 0 2 1 2 2 1 0 0 0 1 1l1 1s0 1 1 1v1c1 2 2 4 5 5h1l1-3v-1s1 0 1-1h1c1 2 1 6 1 9-1 0-1 0-1 1s-1 1-1 2h0c1 2 3 3 4 4v1l-1-1c-2-1-3-2-5-4h0c-1-1-1-2-1-4 0 2 0 5 1 6v1s-1 0-1 1c-1 1 0 4 0 5h0l-33-78-3-7c2-1 2-1 2-3v-6l1 4z" class="B"></path><path d="M376 393l1-2 1-4v3c0 2 0 5 1 7h0c1 2 3 3 4 4v1l-1-1c-2-1-3-2-5-4h0c-1-1-1-2-1-4z" class="G"></path><path d="M342 312l1 4c0 4 1 9 0 12l-3-7c2-1 2-1 2-3v-6z" class="H"></path><path d="M591 167c3-2 6-6 9-8 2-1 5-3 8-4v1l-1 1c-1 1-1 2-2 3v2c1 2 1 3 1 5 1 3-2 8-4 12 0 1-1 2-1 3h0s-1-1 0-1v-1-1l1-2v-1c1 0 1 0 1-1-2 1-3 2-5 3h0v1 3h-1v3h0v1c-2 2-2 7-2 10 0 1 1 3 0 3v-3c0-1-1-5 0-6v-2h0c-1 1-1 2-2 3h0c0 1 0 2-1 3 0 0 0 2 1 3v1 1c-3-6 1-12 1-18h0v-1h0v-1h1v-1h0v-1-1h0l1-1v-1l1-1v-1c-1 0-2 1-2 0h-2c-1 0-2 1-3 2l-1 2h-1v2c0 1-1 1-1 2h0v1c-1 1-1 1-1 2l-1 2c0-2 1-4 1-6 2-4 4-7 6-10l-1-2z" class="B"></path><defs><linearGradient id="T" x1="605.033" y1="167.494" x2="618.488" y2="169.83" xlink:href="#B"><stop offset="0" stop-color="#6f6e6e"></stop><stop offset="1" stop-color="#b1b0b0"></stop></linearGradient></defs><path fill="url(#T)" d="M607 157v1l2-1c2-1 4-1 7-1l2 2h0v2c-1 0-1 1-1 2h1v1c2 2 3 3 6 5-1 1-1 1-2 1-1 1-1 2-2 3v1h0l-1-1c-2 0-3 1-4 2l-3 3c-3 2-6 5-8 9-2 3-2 6-2 8-1-3-1-7 0-11 0 0-1 0-1-1s1-2 1-3c2-4 5-9 4-12 0-2 0-3-1-5v-2c1-1 1-2 2-3z"></path><path d="M618 163c2 2 3 3 6 5-1 1-1 1-2 1-1 1-1 2-2 3v1h0l-1-1c-1 0-1-1-2-2h-1v-1c0-1 2-3 3-4-1-1-1-1-1-2z" class="I"></path><path d="M314 261h0l9 8c2 1 8 5 10 5v-1h1c1 1 3 1 5 1v-1h1c1 1 1 3 1 4 0 3 0 6 1 9 0 3 1 7 2 10v5c-1 1-1 2-2 2 1 4 2 9 1 13l-1-4c0-6-1-13-5-18-1-2-3-3-4-5h0l-1-1c-2-2-4-4-5-6v-1c-1 0-1-1-1-2-1 0-2-1-3-1v-1l-2-2v1 1-1l-7-15z" class="Y"></path><path d="M332 284c1 0 2 1 4 2 1 0 2 0 2 1h0c-3 0-5-1-7-3h0 1z" class="X"></path><path d="M338 275h1c1 3 1 7 1 10-1-1-2-3-3-5l-1-1h1c0-1 1-2 1-3v-1z" class="F"></path><path d="M314 261h0l9 8-2-1h0v1 2c0 3 3 6 5 8s4 4 6 5h-1c-1-1-3-2-4-3-1 0-1-1-1-2-1 0-2-1-3-1v-1l-2-2v1 1-1l-7-15z" class="B"></path><path d="M339 273h1c1 1 1 3 1 4 0 3 0 6 1 9 0 3 1 7 2 10v5c-1 1-1 2-2 2 1 4 2 9 1 13l-1-4c0-6-1-13-5-18-1-2-3-3-4-5h0c4 3 7 7 10 12 0-6-2-10-3-16 0-3 0-7-1-10h-1c-2 0-4 0-5-1v-1h1c1 1 3 1 5 1v-1z" class="G"></path><path d="M484 451c-1-2-2-6-1-8 2-1 4 0 6 1 3 1 6 4 7 8 2 4 1 9-1 14h0c-1 1-2 3-4 4-5 3-11 4-17 5-2 1-4 2-6 1h1c5-4 11-8 13-14 1-2 2-4 2-6v-5z" class="F"></path><path d="M484 456c0-2 1-3 0-4 1-1 1-1 2-1s3 1 4 2c1 2 0 6-1 8-1 5-7 10-12 12h-1l-2 2c-2 1-4 2-6 1h1c5-4 11-8 13-14 1-2 2-4 2-6z" class="Y"></path><defs><linearGradient id="U" x1="250.95" y1="469.016" x2="265.162" y2="461.288" xlink:href="#B"><stop offset="0" stop-color="#cfcece"></stop><stop offset="1" stop-color="#fafafa"></stop></linearGradient></defs><path fill="url(#U)" d="M252 446c3-2 6-2 9-2v1c-1 2-3 7-2 9h0l1 1h1c0 2 1 5 2 7l-1 1c4 6 9 10 15 14l-20-5c-4-1-8-4-9-8-2-3-2-8-1-11 0-1 1-2 1-3 1-2 2-3 4-4z"></path><path d="M259 454h0l1 1h1c0 2 1 5 2 7l-1 1c-2-2-3-5-3-9z" class="N"></path><path d="M138 156v-1c9 1 17 9 22 15 7 10 12 22 16 33 1 1 1 1 1 2 0 2 1 3 2 5l2 1 1 4h-2l-4-7-1 1v1 1s-1-1-1-2h-1c0-2-1-4-1-6h0c0-1-1-1-1-2v-1c-1-1-1-2-1-3-1-1-2-4-3-5h-2v-1c-1-4-3-8-4-11v2h0v2c0 1 1 2 1 3v1c-1-2-2-3-2-5v3h0 0c-1-1-1-2-1-3-1-1-1-2-2-3 0-1-1-3-1-4h-2v1l-1-1c0 1 0 1-1 1v1 1c1 1 1 1 1 2v2h0v2l-1-1v-2-1-1c0-1 0 0-1-1-1-7-3-12-8-17l-2-2-3-4z" class="B"></path><path d="M161 180v-2c1 1 3 6 4 8 2 4 4 9 7 12 1 3 3 5 5 7 0 2 1 3 2 5l2 1 1 4h-2l-4-7-1 1v1 1s-1-1-1-2h-1c0-2-1-4-1-6h0c0-1-1-1-1-2v-1c-1-1-1-2-1-3-1-1-2-4-3-5h-2v-1c-1-4-3-8-4-11z" class="Z"></path><path d="M172 203c1 1 3 4 4 5l-1 1v1 1s-1-1-1-2h-1c0-2-1-4-1-6z" class="K"></path><path d="M223 360c1-1 3-1 4-1h5 0c2 0 2 0 3 1h1c1 0 1 0 2 1h2 0c5 3 9 10 11 16l6 14c1 2 3 5 4 8-1 0-1 0-1 1h0c-1 0-1 1-2 1 0 5-2 10-3 15h-1c0-1 0-1 1-2h0v-1l-2 3h0l2-5v-3-2c1 0 1-1 0-2v-1h1v-7-3h-1c0-1 0-1-1-2l-1-1v-1l-3-3h0l-1-1c0-1-1-2-1-2l-1-2v-2l-1-2v-1c-1-1-1-3-1-4-1-1-2-1-2-2-1-1-2-3-3-3-1-1-2-1-2-2-1 0-1 0-2-1h-1 0v-1l-1-1v-1h-7c-1 0-4 1-6 2h-1-1v1h-1-1 0l-2 2h0c2 1 3 1 5 1 1-1 1-1 2-1h0 6 0 2 0l1 1h1c2 1 3 1 4 2l-1 1 2 2h1 0l1 1 2 2c-1-2-4-6-6-7l-1-1h-1c-1 0-2-2-3-2s-1 1-2 0h-1c-1 0-3-1-4 0h-4 0v-1h1s0-1 1-1h1 0 1 2 1 1c2 0 4 0 6 1v1-1l1 1c2 1 5 2 6 4v1l2 2c0 1 1 1 1 2v1c1 1 1 2 2 4h0l1 1c-1 2 0 3 0 4l1 1v1 1c1 1 0 3 0 4 1 1 1 0 1 1v1 3c-1 1-1 2-1 3v1l-1 1 1-6c0-5-1-12-4-16-2-3-4-5-7-7-6-3-12-4-18-2v-1c-1-1-3-1-4-1 1 0 1 0 2-1l-3-1 3-3h0l2-1 3-1c1-1 1-1 2-1z" class="B"></path><path d="M238 362c1 0 1 0 2 1s3 2 4 4v3l-6-6h-1c1-1 1-1 1-2zm19 29c1 2 3 5 4 8-1 0-1 0-1 1h0c-1 0-1 1-2 1 0 5-2 10-3 15h-1c0-1 0-1 1-2h0v-1l-2 3h0l2-5c1-4 2-8 2-12 0-3-1-6 0-8z" class="H"></path><path d="M223 360c1-1 3-1 4-1h5 0c2 0 2 0 3 1h1c1 0 1 0 2 1h2 0c5 3 9 10 11 16h-1c-2-1-5-5-6-7v-3c-1-2-3-3-4-4s-1-1-2-1c0 0-1 0-1-1h-1-1-1c-1-1-1-1-2-1h-3c-1 1-3 0-4 1h0-2c-1 1-2 1-4 1 0 1-1 0-1 0l3-1c1-1 1-1 2-1z" class="I"></path><path d="M260 507l1 1c1 0 5-3 6-4s1-2 2-3h1l-3 6 1 1 1-1c8-5 14-6 23-5l9 4c1 1 6 4 7 6 0 2 1 2 2 3v1c1 1 1 2 1 2v1c-1-2-2-3-4-3-1-1-2-1-3-2-1 1-5-3-7-3h-1c0-1-1-1-1-1h-1v1c-1 0-1-1-2-1-2 1-4 1-6 0-2 0-4 0-6 1-5 0-8 2-13 3-1 0-1 1-2 1-2 2-5 2-7 3-1 1-2 1-2 1 0 1 0 2-1 3-1-2 0-5-1-6v-1l1-1s0-3 1-4c1-2 3-4 5-6v1s0 1-1 2z" class="S"></path><path d="M268 508l1 1c1-1 1 0 2-1h2c1 0 1 0 2-1 2 0 5 1 6 0h3 4 1c1 0 2 0 3 1-4 0-7-1-11 0-6 1-11 4-16 5-2 1-3 2-5 1 3-2 6-4 8-6z" class="D"></path><path d="M260 507l1 1c1 0 5-3 6-4s1-2 2-3h1l-3 6 1 1h0c-2 2-5 4-8 6l-4 4c0-1 0-4 1-5 0-2 2-5 3-6z" class="B"></path><path d="M268 508l1-1c8-5 14-6 23-5l9 4c1 1 6 4 7 6 0 2 1 2 2 3v1c1 1 1 2 1 2v1c-1-2-2-3-4-3-1-1-2-1-3-2h0c-3-3-7-4-12-6-1-1-2-1-3-1h-1-4-3c-1 1-4 0-6 0-1 1-1 1-2 1h-2c-1 1-1 0-2 1l-1-1h0z" class="Q"></path><path d="M406 145h-3c1 1 2 1 2 1 3 0 6 0 9-1 3 0 8-1 12-2s7-3 11-4c-2 2-5 4-8 5-7 3-13 3-20 5h0c4 2 12 2 15 1-1 4-3 8-6 11s-7 6-11 8c-4 4-9 10-11 16-1 1-1 2-2 2h0l2-5-1-2 1-1 6-16c1-1 1-2 1-3s0-1-1-2h-1 0v-1h0c1-1 1-2 2-3 0-3 0-4-2-6v-1l-2-1c1-1 1-1 2-1 1-1 2-1 3-1h0l2 1z" class="C"></path><path d="M401 148l1-1c2 0 3 0 4 1s1 1 1 2v3 6h0 0l1 1c-2 3-3 6-5 10l-7 12-1-2 1-1 6-16c1-1 1-2 1-3s0-1-1-2h-1 0v-1h0c1-1 1-2 2-3 0-3 0-4-2-6z" class="F"></path><path d="M401 148l1-1c2 0 3 0 4 1s1 1 1 2v3c-1-2-1-2-3-3h-1v2 2c0-3 0-4-2-6z" class="B"></path><path d="M407 150c1 0 1 0 2 1h0c4 1 10 0 15 0-2 3-4 7-7 10-2 2-5 4-7 6l-6 3c2-2 3-5 4-7l6-6-6 3-1-1h0 0v-6-3z" class="R"></path><path d="M407 150c1 0 1 0 2 1v2c0 2-1 4-2 6h0 0v-6-3z" class="K"></path><path d="M533 289v3l-15 36-17 37c5-4 11-6 18-6 3 1 6 2 9 4h-1l-1-1c-6-2-12-2-18 0-2 2-4 2-6 5h0c-3 3-5 8-6 11l-9 20c0 1-2 3-2 4-1 2-2 6-3 8l1 2c1 4 4 8 7 11 1 1 2 2 2 3l-5-2c-3-1-5-1-8 0 0 2-1 4-1 7h0 0c-1 0-1 0-1 1h-1-1c-1 1-1 1-2 1v-1c-2 4-5 9-6 13l-13 29-3 8h0l2 1v-1h1v1h0c-2 1-3 1-4 3-2 2-3 6-4 9-2 5-4 10-7 14h-1 0l95-220z" class="G"></path><path d="M473 432c1-4 2-7 6-8 0 2-1 4-1 7h0 0c-1 0-1 0-1 1h-1-1c-1 1-1 1-2 1v-1z" class="H"></path><path d="M481 411c1 2 2 5 3 7l5 5h-1c-1-1-3-1-5-1s-4 0-7 1c2-4 4-8 5-12z" class="J"></path><path d="M417 241l9 6c-1-3-3-6-3-9v-7c0 2 1 5 1 7 2 8 5 13 12 17 5 3 12 6 17 5l5 1v1c-1 1-1 1-1 2l-1 1c0 1 0 2-1 2l-1 1v1c8 0 15-2 22-5l2-2c2-1 4-4 5-5l-1 3c-5 7-12 11-20 13l-14 3h-1c-1 0-3 0-4-1h-3v-1h-1 0c-1 0-1 0-1-1-2-1-4-3-5-4s-2-2-3-2v-2h0-1v-1h0c-1-1-1-3-1-5h-1c-1-1-1-2-2-3-2-4-3-6-3-10-2-1-4-3-5-5z" class="G"></path><path d="M454 268v-5l-1-1h1 4c-1 1-1 1-1 2l-1 1c0 1 0 2-1 2l-1 1z" class="F"></path><path d="M449 273c2-1 5-1 6 0-2 1-5 1-7 3h-1 0c-1-1-2-2-3-2 1-1 3-1 5-1z" class="R"></path><path d="M422 246c4 4 5 8 6 13h-1c-1-1-1-2-2-3-2-4-3-6-3-10z" class="I"></path><path d="M449 273c1-1 3-2 5-3 3-1 6-1 10-1l7-1c-5 2-11 5-16 5-1-1-4-1-6 0z" class="H"></path><path d="M429 264c2 0 3 0 5 1 1 0 2 2 3 3 1 2 3 4 5 5 0 1 0 1 1 1v1h-3v-1h-1 0c-1 0-1 0-1-1-2-1-4-3-5-4s-2-2-3-2v-2h0-1v-1zm-1-13c8 7 15 10 25 11v1 6s-1 1-2 1l-8 3c-2-2-4-4-5-6-3-3-5-4-9-5l-1-11z" class="F"></path><defs><linearGradient id="V" x1="181.641" y1="215.211" x2="155.238" y2="233.058" xlink:href="#B"><stop offset="0" stop-color="#c2c2c3"></stop><stop offset="1" stop-color="#f2f1f1"></stop></linearGradient></defs><path fill="url(#V)" d="M165 192h2c1 1 2 4 3 5 0 1 0 2 1 3v1c0 1 1 1 1 2h0c0 2 1 4 1 6h1c0 1 1 2 1 2l3 3h-1-1v1 6l-1 9h-1l-2 2v-1-5h-1-1v-4c-1 6 0 12 1 18 0 3 0 7 1 10v2h0l-1 1h0l-1 1c-1-1-1-1-1-2-1 0-2-1-3-2v1 1h-1v1c-2-2-2-4-3-6l-1 1h0c-1-3-1-7-1-11h0v-5c-1-2-1-5 0-6v-3-1l1-7 3-14c1-3 2-6 1-9z"></path><path d="M160 237h0 1v1 1c1 1 0 1 1 2h0c0 2 1 3 1 4h1 0c0 1 2 4 2 5v1 1h-1v1c-2-2-2-4-3-6l-1 1h0c-1-3-1-7-1-11z" class="B"></path><path d="M165 192h2v6c-1 6-3 13-5 19 0 1-1 4-1 4l-1 1 1-7 3-14c1-3 2-6 1-9zm6 9c0 1 1 1 1 2h0c0 2 1 4 1 6l-1 17h-1-1v-4l1-21z" class="G"></path><defs><linearGradient id="W" x1="178.203" y1="213.995" x2="167.203" y2="225.351" xlink:href="#B"><stop offset="0" stop-color="#6a6869"></stop><stop offset="1" stop-color="#929292"></stop></linearGradient></defs><path fill="url(#W)" d="M173 209h1c0 1 1 2 1 2l3 3h-1-1v1 6l-1 9h-1l-2 2v-1-5l1-17z"></path><path d="M176 215v6l-1 9h-1v-11c0-1 0-3 1-3 0-1 0-1 1-1z" class="K"></path><path d="M328 129c2-1 4-3 6-3s4 0 6 1c3 3 7 4 10 6l12 8 3 2c0 1-1 1-3 2-2 0-5 1-7 1-6 0-11-1-17-2-4-1-9-2-12-4-1-1-2-1-3-1 0 1 1 2 1 3 1 1 3 3 4 5l3 3v1l-1-1c-6-3-14-3-20-3h-2c3-3 7-3 10-6l1-2h0-2l1-1h0c-1 0-1 1-2 1h0s-1 0-1-1l1-1 1-1c1 0 2-1 3-1 2-2 5-3 6-4l2-2z" class="R"></path><path d="M324 142l-1-1c0-1-1-1-1-2h0-2-1v-1s0-1 1-1v1c0-1 1-1 2-2 1 0 2-1 3-1l1-1c1-1 2-1 3-1h0c-1 1-1 2-2 2-1 1-2 1-3 2h-1l-1 1c2 0 3 0 4 2-1-1-2-1-3-1 0 1 1 2 1 3z" class="F"></path><defs><linearGradient id="X" x1="464.123" y1="456.047" x2="480.454" y2="460.374" xlink:href="#B"><stop offset="0" stop-color="#b2b1b1"></stop><stop offset="1" stop-color="#f1f1f0"></stop></linearGradient></defs><path fill="url(#X)" d="M472 440c3 1 7 5 8 7l2 4h2v5c0 2-1 4-2 6-2 6-8 10-13 14h-1c-1 0-2 1-2 2-4 2-8 4-12 5v-1h-1v1l-2-1h0l3-8 13-29c2-1 2-3 4-4l1-1z"></path><path d="M482 451h2v5c0 2-1 4-2 6-2 6-8 10-13 14h-1c-1 0-2 1-2 2-4 2-8 4-12 5v-1h-1v1l-2-1c3-2 6-3 9-4l1-1c3-1 6-2 8-4 1 0 2-1 2-1 2-1 5-3 7-5 4-5 5-10 4-16z" class="N"></path><defs><linearGradient id="Y" x1="454.618" y1="459.428" x2="470.066" y2="460.332" xlink:href="#B"><stop offset="0" stop-color="#949293"></stop><stop offset="1" stop-color="#c1c0c0"></stop></linearGradient></defs><path fill="url(#Y)" d="M471 441v1c-1 3-3 7-5 10s-3 7-3 11c-1 2 0 4 0 6v1c0 1 1 3 2 4h1c1 0 1 0 1-1h2c-2 2-5 3-8 4l-1 1c-3 1-6 2-9 4h0l3-8 13-29c2-1 2-3 4-4z"></path><path d="M454 474v1 3 1c1 0 2-1 3-1 1-1 2 0 3 0-3 1-6 2-9 4h0l3-8z" class="M"></path><defs><linearGradient id="Z" x1="298.443" y1="521.544" x2="263.532" y2="536.058" xlink:href="#B"><stop offset="0" stop-color="#989797"></stop><stop offset="1" stop-color="#e5e5e5"></stop></linearGradient></defs><path fill="url(#Z)" d="M258 518c2-1 5-1 7-3 1 0 1-1 2-1 5-1 8-3 13-3 6 0 10-1 15 4 3 3 4 8 5 12v1c1 3-1 7-3 10l-2-2h-1c-1 0-2-2-3-3v1l1 1v4 4c-2 0-4 1-5 1h-2c-2 1-5 3-6 4v1c-1 0-1 1-2 1 0 1 0 1 1 1h-1l9 3c-2 1-3 1-5 3h0v1 1c1 0 1 0 2 1h-1 0c-1 0-2-1-3-2 0 0 1 0 1-1v-1h1v-1h2v-1c-1 0-2-1-3-1h0-1l-1-1h-1c-1 0-1 0-2-1v-1h0c-1-1 0-8 0-9h0v-1c1-1 1-1 1-2v-2h0 0c1-1 0-1 0-2l1-1c0-1 0-4-1-6h0c0-1 0-1-1-2h0c0-1-1-1-2-2h-1c-1-1-2-1-3-1h-3-10s1-1 1-2l1-2z"></path><path d="M285 544c1-1 3-2 4-3l1-1c1 0 1-1 1-1h1v4c-2 0-4 1-5 1h-2z" class="K"></path><defs><linearGradient id="a" x1="130.075" y1="164.01" x2="115.096" y2="180.016" xlink:href="#B"><stop offset="0" stop-color="#abaaaa"></stop><stop offset="1" stop-color="#dcdbdc"></stop></linearGradient></defs><path fill="url(#a)" d="M131 156c2-1 5 0 7 0l3 4 2 2c-1 1 0 3 0 5 0 4 1 8 2 12 1 3 1 6 1 9s0 5-1 7l-1 4c-2 4-6 8-10 10h-1 0 0c3-2 5-4 7-7v-4c-1-2-1-3-1-4 0-2-1-2-2-3v-1l-3-3v-2c0-1-3-2-4-2 0-1-1-1-2-1l-1 1v1 2s-1 0-1 1v1 1 1h0l-1 1-1-1h0c0-1-1-1-1-2s1-2 1-4v-8c-1 0-1 1-1 2v-1-3h0v-1h1c-1-1-1-1-1-2h0-1c0-1-1-2-2-3h-1c-2-1-7 0-9 0-1 1-2 0-3 0-1 1-2 1-3 1s-1 0-3 1h0l-1 1h-1l-1 1-1 1h-1v1h-1l-1 1h-3l21-14c4-2 9-3 13-5h6z"></path><path d="M125 156c4 1 6 4 8 6s2 5 3 7c4 7 9 18 8 26 0 1-1 3-1 3l1 1c-2 4-6 8-10 10h-1 0 0c3-2 5-4 7-7 0-1 1-2 1-2 0-1 1-1 1-2 1-8-3-14-7-21 0 0-1-1-1-2s-1-1-1-2h0c0-2-1-5-2-6s-1-1-1-2c-1-1-1-3-3-4h-1c0-1 0-1-1-1l-3-2-9 3h-1c4-2 9-3 13-5z" class="J"></path><path d="M131 156c2-1 5 0 7 0l3 4 2 2c-1 1 0 3 0 5 0 4 1 8 2 12 1 3 1 6 1 9s0 5-1 7l-1 4-1-1s1-2 1-3c1-8-4-19-8-26-1-2-1-5-3-7s-4-5-8-6h6z" class="K"></path><path d="M131 156c2-1 5 0 7 0l3 4-1 1c-1-1-2-1-3-1-2-1-4-4-6-4z" class="T"></path><path d="M287 200v-1l1 1c0 1 1 2 1 2l11 26c2 5 4 10 7 14 2 3 6 6 10 7 4 2 8 3 13 4 2 0 4-1 5-1 6-2 10-8 13-13 1-2 2-5 3-7l-1 6c-1 3-3 6-6 9-1 1-2 3-4 4s-5 2-8 3h10l12-3c-7 4-13 5-21 4-2 0-4-1-7-1h-2-1c-1 0-1 0-2-1h-1c-1 0-1 1-1 1v2h-1c0 1-1 2-1 2v1c-1 1-1 0-1 1h1l2 2c-2 0-4-1-5-3l-1 1 1 1 7 15v1-1-1l2 2v1c1 0 2 1 3 1 0 1 0 2 1 2v1c1 2 3 4 5 6l1 1h0c1 2 3 3 4 5 4 5 5 12 5 18v6c0 2 0 2-2 3l3 7 33 78c0 1 1 2 1 4 2 3 3 7 4 10 2 4 4 7 5 11l1 1h0c2 0 4 1 5 3 0 0 0 2 1 3 0 1 1 2 1 4h2v1c0 1-1 3-1 4v1l-1 1-3-6-1 1-103-244z" class="G"></path><path d="M391 443l-3-6 6 5h0 2v1c0 1-1 3-1 4v1l-1 1-3-6z" class="M"></path><path d="M314 259c-3-4-5-10-7-15 2 2 5 5 9 6 3 2 7 3 10 4h-2-1c-1 0-1 0-2-1h-1c-1 0-1 1-1 1v2h-1c0 1-1 2-1 2v1c-1 1-1 0-1 1h1l2 2c-2 0-4-1-5-3z" class="B"></path><defs><linearGradient id="b" x1="329.767" y1="296.429" x2="336.272" y2="293.546" xlink:href="#B"><stop offset="0" stop-color="#d0cfd0"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#b)" d="M321 277v-1-1l2 2v1c1 0 2 1 3 1 0 1 0 2 1 2v1c1 2 3 4 5 6l1 1h0c1 2 3 3 4 5 4 5 5 12 5 18v6c0 2 0 2-2 3l-1-2-9-22-6-13c-1-2-2-5-3-7z"></path><path d="M339 319v-2-1c0-1-1-2-1-3h1c0-1 0-1-1-2v-1c0-1 0-1-1-1v-1l-1-4-1-1v-2c-1-1-1 0-1-1 2 3 4 8 5 11v4h0l1 1c0 1 1 2 2 2 0 2 0 2-2 3l-1-2z" class="F"></path><defs><linearGradient id="c" x1="226.492" y1="315.141" x2="207.987" y2="327.586" xlink:href="#B"><stop offset="0" stop-color="#8c8b8c"></stop><stop offset="1" stop-color="#e6e5e4"></stop></linearGradient></defs><path fill="url(#c)" d="M180 293c1-1 2-3 3-4 1 0 3-1 5-1 4-1 8 0 13 1 1 0 3 1 4 2 2 1 4 3 6 5 1 1 2 2 3 2v1c1 0 3 2 3 3h1c0 1 1 1 1 2l21 48c-1 1 0 2 0 4l-1 1-1-1-1 1c1 1 2 3 3 4h0-2c-1-1-1-1-2-1h-1c-1-1-1-1-3-1h0-5c-1 0-3 0-4 1 1-1 1-1 2-1 2-1 4 0 5-1v-1c0-1 0-1-1-2v-2l-1-1-2-4-8-14-7-11v-1c-1 0-1-1-2-1v-1l-1-2c-1-1-1-2-2-3s-1-2-2-3l-1-1v-1l-1-1c-1-2-2-4-3-5v-1l-2-2c-1-1-3-4-5-5-1-1-2-1-3-2h0c-1 0-2-1-2-1h-1c-1-1-2-1-3-1v2c-2-1-2 0-3 1h-1l1-2z"></path><defs><linearGradient id="d" x1="439.026" y1="522.542" x2="476.753" y2="532.047" xlink:href="#B"><stop offset="0" stop-color="#989797"></stop><stop offset="1" stop-color="#ececec"></stop></linearGradient></defs><path fill="url(#d)" d="M455 507c3 1 6 1 10 2h2c-1 1-1 2-1 3 1 1 2 1 3 2h1s1 0 2 1h0 0 1l1 1h1c1 1 1 1 2 1v1h1c0 1 1 1 1 1 1 0 1 0 2 1-1 0-2 0-3-1v1l-1-1s-1 1-2 0h-1-1-2-1-1c-1 0-1 0-1 1h-2 0l-1 1c-1 0-2 1-2 2h0c-1 1-1 3-1 4l-1 1c0 2 0 7 1 8v4c0 1 0 1 1 1 0 1-1 1 0 2 1 2 0 3 2 4 1 1 2 1 2 2v1 1h-1v1c-1 1-3 2-4 2-1 1-1 0-1 1l1 1 1 1 1-1v2c0 1 0 1-1 1-1 1-1 0-2 1h-1-1c0 1-1 1-1 1h-1 0c1-1 3-1 4-3l1-1h-1c-1 0-1 0-1-1-1 0-3 0-4 1h0l-1-1 1-1 2-1c1 0 1-1 2-1h1l6-3c-1 0-2-1-2-2h-1c-3-2-5-2-8-1v-1c0-1 0-1 1-1h0c0-2-1-2-2-2 0-1-1-1-1-2h-1v-1h1v-2l-1-1h0l-1-2c-3 3-6 6-10 8-1 1-2 2-3 2l-5 3-1-1-3 1c-2-1-5 0-7 0h0c0-3 1-4 2-6 0-1 2-4 2-4 2-6 4-11 7-16s6-9 12-12l9-3z"></path><path d="M456 547v-1c0-1 0-1 1-1h0c0-2-1-2-2-2 0-1-1-1-1-2h-1v-1h1l4 3h0c1 1 6 3 6 5-3-2-5-2-8-1z" class="W"></path><defs><linearGradient id="e" x1="420.943" y1="537.017" x2="454.838" y2="522.27" xlink:href="#B"><stop offset="0" stop-color="#696868"></stop><stop offset="1" stop-color="#aeadad"></stop></linearGradient></defs><path fill="url(#e)" d="M446 510h1 3 0c-1 1 0 1-1 1h0-1c-2 0-5 2-6 4s-2 4-2 7c-1 2-1 6 0 9h0c1 2 1 3 2 5h0c1 1 3 2 4 2s1-1 2-1c1-1 2-1 2-1 1-1 2-3 4-3v1l-2 1c-3 3-6 6-10 8-1 1-2 2-3 2l-5 3-1-1-3 1c-2-1-5 0-7 0h0c0-3 1-4 2-6 0-1 2-4 2-4 2-6 4-11 7-16s6-9 12-12z"></path><path d="M423 548h0c0-3 1-4 2-6h0v3 1h3l1-1h0c1 1 2 1 2 1h1 2 1c0-1 1-1 2-1h1 0 1l-5 3-1-1-3 1c-2-1-5 0-7 0z" class="K"></path><path d="M452 535l1 2h0l1 1v2h-1v1h1c0 1 1 1 1 2 1 0 2 0 2 2h0c-1 0-1 0-1 1v1c3-1 5-1 8 1h1c0 1 1 2 2 2l-6 3h-1c-1 0-1 1-2 1l-2 1-1 1 1 1h0c1-1 3-1 4-1 0 1 0 1 1 1h1l-1 1c-1 2-3 2-4 3-6 1-11 4-16 8h1v1h-1c-4 2-7 4-11 5-1 0-3 1-4 2-2 2-4 4-4 8 0 1 1 2 1 3h1 1c2 2 7 1 10 1v1c-5 3-10 4-15 3-2 0-4-1-5-2-2-1-3-2-5-3l-3-4 7-16v2c0 5 1 9 3 13 0 1 1 2 1 3v-2c-1-3-2-5-2-8h-1v-1-9c0-2 1-4 2-6 1-4 3-8 6-12 2 0 5-1 7 0l3-1 1 1 5-3c1 0 2-1 3-2 4-2 7-5 10-8z" class="R"></path><path d="M453 537l1 1v2h-1v1h1c0 1 1 1 1 2 1 0 2 0 2 2h0c-1 0-1 0-1 1v1c-3 1-5 2-7 3 1-2 3-4 3-6 1-2 1-5 1-7z" class="J"></path><path d="M450 559l2-1 3-2 1 1h0c1-1 3-1 4-1 0 1 0 1 1 1h1l-1 1c-1 2-3 2-4 3-6 1-11 4-16 8l-8 2c4-5 11-9 17-12z" class="T"></path><path d="M450 559l2-1 3-2 1 1h0c1-1 3-1 4-1 0 1 0 1 1 1h1l-1 1c-4 1-7 2-11 2v-1z" class="X"></path><path d="M430 548l3-1 1 1c-1 1-3 3-5 3 0 1-1 2-2 3h-1l-6 16v3h0c1-1 0-1 1-1v1 1h-1 0v4h0 0c0-2 1-3 2-5h0l1-2v-1l1-1c0-1 1-2 2-2 1-2 3-3 4-4 3-2 5-4 8-5 1 0 2-1 3-1-2 1-5 3-6 4s-1 2-1 3c-3 2-5 5-6 8-1 1-2 2-2 3v2c-2 2-4 4-4 8 0 1 1 2 1 3h1 1c2 2 7 1 10 1v1c-5 3-10 4-15 3-2 0-4-1-5-2-2-1-3-2-5-3l-3-4 7-16v2c0 5 1 9 3 13 0 1 1 2 1 3v-2c-1-3-2-5-2-8h-1v-1-9c0-2 1-4 2-6 1-4 3-8 6-12 2 0 5-1 7 0z" class="B"></path><path d="M416 576h0v-3c1-3 1-6 2-8 1-4 3-8 6-10-1 3-2 6-3 8l-1 1v1c-1 1 0 1 0 2 0 2-1 5-2 7v7c-1 1 0 2 0 3-1-3-2-5-2-8z" class="H"></path><path d="M430 548l3-1 1 1c-1 1-3 3-5 3l-3 2c-1 0-1 1-2 2-3 2-5 6-6 10-1 2-1 5-2 8v3h0-1v-1-9c0-2 1-4 2-6 1-4 3-8 6-12 2 0 5-1 7 0z" class="J"></path><path d="M430 548l3-1 1 1c-1 1-3 3-5 3l-3 2v-1c0-2 3-3 4-4z" class="C"></path><path d="M478 147c1 1 3 2 4 4 7 5 16 11 19 19 5 8 6 18 6 27 0 2-2 7-1 9h0l1 1-10 26v1c1-1 1-1 2-1l-4 10c-1 0-1 2-2 2h-1-1l-1-1c-5 3-10 5-16 5l1-1h1 1 2v-1c1 0 1 0 2-1v-1c1-1 0-1 1-1v-1l1-1v-2l1-1c1-1 1-2 2-3v-2l2-3v-1-1l1-1v-1-2l1-1v-1h0c1-1 0-1 0-2 1-1 1-1 1-2v-2c1 0 1-1 1-1v-3c1-1 0-2 0-3 1 0 1-11 1-13-1 0-1-1-1-2v-2c-1-1 0-1 0-2h-1v-2h0v-1c-1-1 0-2-1-3v-1l-1-2v-2c-1-1-1-1-1-2v-1c-1 0-1 0-1-1v-1c0-1-1-2-1-3v-1s0-1-1-1v-1-2c-1-1-1-2-1-2v-5h-1v-1c-1 0-1-1-2-1l-2-1-1-1h-1l-1-1c-1 0-2-1-3-1h0c-1-1-1-1-2-1h0c-2 0-2 0-3-1h-2c-2 0-4 1-6 1-1 0-2 0-3 1-2 1-4 3-7 4 4-4 7-7 12-9 1-1 2-1 4-1l1-1h3c2-1 5 0 8 0z" class="B"></path><path d="M497 233v1c1-1 1-1 2-1l-4 10c-1 0-1 2-2 2h-1-1l-1-1c4-2 5-8 7-11z" class="O"></path><path d="M556 265c5-2 13 0 18 2 3 2 6 3 9 5 3 3 6 7 10 9h1 1 2c-1 1-3 2-5 3-1 0-1 1-2 1-2 0-2 0-3-1 0 2 2 4 3 6 1 6-1 12-5 17-2 2-6 5-9 8-2 2-5 3-8 4h1c2-2 2-4 3-6 1-5-2-11-5-15 0-1-2-3-3-4s-2-3-4-5c0-1-1-2-2-3s-8-1-10-1c-5 1-12 3-15 7v-3-1h0l2-1 6-15 1 1c1 0 2-1 3-1v-2c2-1 2-2 3-3l5-2h3z" class="Y"></path><path d="M553 265h3c-1 0-1 1-2 1h1c-1 1-2 1-3 2-1 0-3 1-4 2 0-1 2-2 3-2h0c1-1 1-2 2-3z" class="R"></path><path d="M548 267l5-2c-1 1-1 2-2 3h0c-1 0-3 1-3 2h0 1 1l-5 2v-2c2-1 2-2 3-3z" class="F"></path><path d="M550 270h10c1 0 2 1 3 1h1c6 2 11 6 14 11l1 1c2 4 5 11 4 16l-1 1h0v-2c1-5-2-13-6-17-4-6-10-9-17-10-3 0-7 0-11 1h0l-2 2v2 1h0l1 1c-2 0-3 1-4 2h0v2h0 1l-2 1c-3 1-5 3-7 5v-1l6-15 1 1c1 0 2-1 3-1l5-2z" class="W"></path><path d="M542 283l-1-1c0-1 1-1 2-2v2h0 1l-2 1z" class="I"></path><path d="M543 280c-1 0-2 1-3 1 0-1 0-3 1-4v-1c2-2 5-4 7-4h0l-2 2v2 1h0l1 1c-2 0-3 1-4 2z" class="B"></path><path d="M547 278h10c6 1 10 5 13 10l1 1c2 1 4 6 4 8 1 1 1 4 0 5 0 3-1 5-1 8l-2 6c4-1 7-5 9-7 1-1 3-2 4-4 4-7 6-14 1-21-1-1-3-2-3-3h0c2 1 7 1 9 0h1 1 1 2c-1 1-3 2-5 3-1 0-1 1-2 1-2 0-2 0-3-1 0 2 2 4 3 6 1 6-1 12-5 17-2 2-6 5-9 8-2 2-5 3-8 4h1c2-2 2-4 3-6 1-5-2-11-5-15 0-1-2-3-3-4s-2-3-4-5c0-1-1-2-2-3s-8-1-10-1c-5 1-12 3-15 7v-3-1h0l2-1v1c2-2 4-4 7-5l2-1h-1 0v-2h0c1-1 2-2 4-2z" class="O"></path><defs><linearGradient id="f" x1="543.919" y1="278.553" x2="551.467" y2="281.006" xlink:href="#B"><stop offset="0" stop-color="#acadac"></stop><stop offset="1" stop-color="#d0cdd0"></stop></linearGradient></defs><path fill="url(#f)" d="M547 278h10l-1 1c-2 0-3 0-4 1h3c-4 0-7 1-11 2h-1 0v-2h0c1-1 2-2 4-2z"></path><path d="M557 278c6 1 10 5 13 10l1 1c-1-1-1-1-2-1 0 0-1 0-1-1v1c-4-4-7-7-13-8h-3c1-1 2-1 4-1l1-1z" class="F"></path><path d="M574 305c0 1 0 3-1 5-1-4-2-7-4-10-3-5-9-11-10-16l1-1c3 1 6 4 8 6v-1-1c0 1 1 1 1 1 1 0 1 0 2 1 2 1 4 6 4 8 1 1 1 4 0 5 0 1 0 2-1 3z" class="R"></path><path d="M568 288v-1c0 1 1 1 1 1 1 0 1 0 2 1 2 1 4 6 4 8 1 1 1 4 0 5 0 1 0 2-1 3v-7c-1-1-2-4-3-6-1-1-1 0-2-1 0 0 0-1-1-1v-1-1z" class="X"></path><path d="M186 264h1c6 1 13 3 18 6l11 23-5-5v2c4 4 7 8 8 14 0-1-1-1-1-2h-1c0-1-2-3-3-3v-1c-1 0-2-1-3-2-2-2-4-4-6-5-1-1-3-2-4-2-5-1-9-2-13-1-2 0-4 1-5 1-1 1-2 3-3 4h0c-2 2-5 5-6 8s-2 5-3 8c-1-1-2-3-3-5l3 12-4-3c-5-3-8-8-10-14-1-1-1-3-1-5-1-4 2-9 5-13h1s-2 1-3 1c-3 0-6 0-8-1 4-2 8-6 12-9 7-5 15-8 23-8z" class="Y"></path><defs><linearGradient id="g" x1="207.538" y1="276.683" x2="200.213" y2="280.262" xlink:href="#B"><stop offset="0" stop-color="#a9a8a9"></stop><stop offset="1" stop-color="#dad8d8"></stop></linearGradient></defs><path fill="url(#g)" d="M187 264c6 1 13 3 18 6l11 23-5-5c-5-4-10-6-16-7 1-1 1-1 2-1h0c1 0 1 1 2 1h2 0v-2c0-1-1-1-2-1h-1c-2 0-4 0-6-1h-4c2-1 5-1 8-1 1-2 2-1 4-1l1-1c-2-1-4-1-5-2h-2-1 0c-1-1-3-1-4-1v-1c2-1 5 0 7 0l2-2c-1-1-1-1-3-1h-1l-1-1h-1c-2 0-4 0-5-2z"></path><path d="M201 274h0c1 0 2 0 2 1 1 1 2 1 2 2h-1c-1-1 0-1-1-1h-7c1-2 2-1 4-1l1-1z" class="B"></path><path d="M192 277h-4c2-1 5-1 8-1h7l2 2 1 1c-2 0-5-1-6-1-3-1-6-1-8-1z" class="I"></path><path d="M177 285c2-1 3-2 5-3 4-2 9-2 13-1 6 1 11 3 16 7v2c4 4 7 8 8 14 0-1-1-1-1-2h-1c0-1-2-3-3-3v-1c-1 0-2-1-3-2-2-2-4-4-6-5-1-1-3-2-4-2-5-1-9-2-13-1-2 0-4 1-5 1-1 1-2 3-3 4h0c-2 2-5 5-6 8s-2 5-3 8c-1-1-2-3-3-5v-5c1-1 0 0 0-1 1-1 1-2 1-3l1-1c0-1 1-2 2-4h0c2-3 3-4 5-5z" class="M"></path><path d="M177 285c2-1 3-2 5-3 4-2 9-2 13-1 6 1 11 3 16 7v2c-6-3-12-4-18-5-3 0-5-1-8 1l-1 1-1-1 2-3c-3 1-5 2-7 4 0 1 0 1-1 1 1-1 1-2 1-3h-1z" class="O"></path><path d="M177 285h1c0 1 0 2-1 3 1 0 1 0 1-1 2-2 4-3 7-4l-2 3c0 1-3 6-3 7-2 2-5 5-6 8l-3 8c-1-1-2-3-3-5v-5c1-1 0 0 0-1 1-1 1-2 1-3l1-1c0-1 1-2 2-4h0c2-3 3-4 5-5z" class="R"></path><path d="M310 147c6 0 14 0 20 3l1 1v-1c2 4 6 7 10 9-3 0-3-1-5-3-1 0-3-3-4-3h-1l-1 1s-1 0-2 1h-1l-2 1h-1c-4 1-8 4-10 6-1 1 0 0-1 0l-2 2v1l-1 1h0c0 1-1 1-1 2l-1 1v1 1c-1 1-1 2-1 3 0 0 0 1-1 2v1 3c-1 0-1 1-1 2h0v2c0 1 0 1-1 2 0 1 1 2 0 3v4c0 2-1 9 0 10v4c0 1 0 2 1 3 0 1-1 1 0 2v2c0 1 0 1 1 2v1 1c1 1 1 2 1 3l2 6 4 11h1l1 2 1 3h1v2l2 1c0 1 0 3 1 4h1c1 0 2 1 3 1h1 2v1c2 0 2 0 3 1-5-1-9-2-13-4-4-1-8-4-10-7-3-4-5-9-7-14l-11-26s-1-1-1-2l-1-1v1c-1-2-1-3-2-5-2-5-2-12-1-19v2h0c1-1 1-3 1-4h1 0 0c1-2 1-3 2-5 1-4 3-7 5-10 1-1 4-3 4-4h0c-1 0-2 1-3 1 4-5 10-7 16-9z" class="B"></path><defs><linearGradient id="h" x1="302.311" y1="172.549" x2="287.118" y2="172.605" xlink:href="#B"><stop offset="0" stop-color="#cecbcc"></stop><stop offset="1" stop-color="#f1f0f2"></stop></linearGradient></defs><path fill="url(#h)" d="M310 147c6 0 14 0 20 3l1 1h-1-1c-7-2-15-2-21 2-9 4-13 14-15 22-2 6-3 12-4 18v4 5s-1-1-1-2l-1-1v1c-1-2-1-3-2-5-2-5-2-12-1-19v2h0c1-1 1-3 1-4h1 0 0c1-2 1-3 2-5 1-4 3-7 5-10 1-1 4-3 4-4h0c-1 0-2 1-3 1 4-5 10-7 16-9z"></path><defs><linearGradient id="i" x1="289.922" y1="190.492" x2="280.511" y2="185.326" xlink:href="#B"><stop offset="0" stop-color="#2e2e2c"></stop><stop offset="1" stop-color="#4e4c50"></stop></linearGradient></defs><path fill="url(#i)" d="M288 169l-1 4h0 0 1c-2 8-2 17 0 25 1-2 0-5 1-8v3h0v4 5s-1-1-1-2l-1-1v1c-1-2-1-3-2-5-2-5-2-12-1-19v2h0c1-1 1-3 1-4h1 0 0c1-2 1-3 2-5z"></path><path d="M310 147c6 0 14 0 20 3l1 1h-1-1c-7-2-15-2-21 2-9 4-13 14-15 22-2 6-3 12-4 18h0v-3-6c1-11 6-23 15-30v-1h-3v1c-2 1-4 2-5 4-4 3-7 9-8 15h-1 0 0l1-4c1-4 3-7 5-10 1-1 4-3 4-4h0c-1 0-2 1-3 1 4-5 10-7 16-9z" class="G"></path><path d="M301 153c1-1 1-1 2-1 5-2 9-5 15-3-5 0-9 2-14 5v-1h-3z" class="J"></path><path d="M175 211v-1-1l1-1 4 7h2l86 196c2 3 3 8 5 12l36 85h-1c1 2 3 5 3 7l-3-3c-1-2-6-5-7-6l1-1c0-4-3-9-6-13v1l-1-1v-2c-1 0-1-1-1-1v-1c-1-1 0-1 0-2l-1-1-5-3h-2 0c-1-1-1-1-2-1h0l-1-2c-4-1-7-2-11-2-10-1-19-1-28 3s-15 10-18 19c0 1-1 1-1 2h0c2-5 3-10 7-14 8-9 20-12 31-12l-7-2 1-1 20 5c-6-4-11-8-15-14l1-1c-1-2-2-5-2-7 0-5 2-8 5-12 2-1 4-3 6-4l-8-1c1-2 1-4 1-6h0 3v-1c0-1 0-2-1-3v-2c0-1 0-2-1-3h-2-1-5c-1 1-2 1-4 1h0v-1h-2c0-3 3-5 3-7 1-5 3-10 3-15 1 0 1-1 2-1h0c0-1 0-1 1-1-1-3-3-6-4-8l-6-14c-2-6-6-13-11-16-1-1-2-3-3-4l1-1 1 1 1-1c0-2-1-3 0-4l-21-48c-1-6-4-10-8-14v-2l5 5-11-23c-5-3-12-5-18-6h-1c-1-1-3 0-4 0l1-1-2-1c-6-5-8-13-9-20-1-2 0-3-1-5 0-2 0-4 1-6v1l2-2h1l1-9v-6-1h1 1l-3-3z" class="G"></path><path d="M240 352l7 13c-3-3-5-6-8-8l1-1c0-2-1-3 0-4z" class="J"></path><path d="M194 254l11 14c-4-1-7-3-10-4h2 0 1s-1 0-1-1c0-3-2-5-3-9z" class="M"></path><path d="M190 243l-1-2 2-2c0 1 0 2 1 2h1c3 6 6 13 8 19l-11-15v-2z" class="I"></path><defs><linearGradient id="j" x1="262.312" y1="401.06" x2="251.686" y2="421.568" xlink:href="#B"><stop offset="0" stop-color="#bdbcbd"></stop><stop offset="1" stop-color="#e3e2e2"></stop></linearGradient></defs><path fill="url(#j)" d="M255 416c1-5 3-10 3-15 1 0 1-1 2-1h0c0-1 0-1 1-1 2 4 3 7 0 12-1 5-3 9-7 13v-1h-2c0-3 3-5 3-7z"></path><defs><linearGradient id="k" x1="267.225" y1="415.002" x2="257.941" y2="419.056" xlink:href="#B"><stop offset="0" stop-color="#6a6969"></stop><stop offset="1" stop-color="gray"></stop></linearGradient></defs><path fill="url(#k)" d="M271 424c-2-1-5-2-7-3-2 0-5 0-7 1 4-6 6-11 7-17l3 7c0-1 0-1 1-1 2 3 3 8 5 12 0 1 0 1-1 1h-1z"></path><path d="M268 411c2 3 3 8 5 12 0 1 0 1-1 1h-1c1 0 1 0 0-1 0-1-1-2-1-3-1-2-2-6-3-8 0-1 0-1 1-1z" class="L"></path><defs><linearGradient id="l" x1="305.312" y1="497.533" x2="296.305" y2="499.444" xlink:href="#B"><stop offset="0" stop-color="#aaabab"></stop><stop offset="1" stop-color="#d3d2d3"></stop></linearGradient></defs><path fill="url(#l)" d="M283 479c5 2 12 5 16 9 1 2 2 4 3 5l6 15c1 2 3 5 3 7l-3-3c-1-2-6-5-7-6l1-1c0-4-3-9-6-13v1l-1-1v-2c-1 0-1-1-1-1v-1c-1-1 0-1 0-2l-1-1-5-3h-2 0c-1-1-1-1-2-1h0l-1-2z"></path><defs><linearGradient id="m" x1="173.06" y1="217.485" x2="191.982" y2="237.461" xlink:href="#B"><stop offset="0" stop-color="#7d7b7c"></stop><stop offset="1" stop-color="#a9aaaa"></stop></linearGradient></defs><path fill="url(#m)" d="M175 211v-1-1l1-1 4 7 13 26h-1c-1 0-1-1-1-2l-2 2 1 2v2c0 2 2 4 2 5l-1-1c-1-1-2-3-4-4-4-3-10-4-14-7h0-1v4c-1-2 0-3-1-5 0-2 0-4 1-6v1l2-2h1l1-9v-6-1h1 1l-3-3z"></path><path d="M179 225v1c2 4 4 7 6 10 2 2 3 5 5 7v2c0 2 2 4 2 5l-1-1c-1-2-2-3-3-5 0-1-1-3-2-4-1 0-1-1-1-1v-1c-1 0-1 0-1-1s-1-2-2-2l-2-2-1-8z" class="O"></path><path d="M176 221h1v1s1 3 2 3l1 8 2 2c1 0 2 1 2 2s0 1 1 1v1s0 1 1 1c1 1 2 3 2 4 1 2 2 3 3 5-1-1-2-3-4-4-4-3-10-4-14-7h0-1v4c-1-2 0-3-1-5 0-2 0-4 1-6v1l2-2h1l1-9z" class="E"></path><path d="M174 230h1v2c2 3 4 4 7 5-1-1-1-3-2-4l2 2c1 0 2 1 2 2s0 1 1 1v1s0 1 1 1c1 1 2 3 2 4-2-1-4-2-6-4l-6-3-3-3-1-1v-1l2-2z" class="L"></path><path d="M174 230h1v2c-1 1-1 1-3 1v-1l2-2z" class="O"></path><path d="M176 221h1v1s1 3 2 3l1 8c1 1 1 3 2 4-3-1-5-2-7-5v-2l1-9z" class="J"></path><defs><linearGradient id="n" x1="188.619" y1="249.687" x2="178.814" y2="258.365" xlink:href="#B"><stop offset="0" stop-color="#919090"></stop><stop offset="1" stop-color="#b6b6b5"></stop></linearGradient></defs><path fill="url(#n)" d="M172 242v-4h1 0c4 3 10 4 14 7 2 1 3 3 4 4l1 1 2 4c1 4 3 6 3 9 0 1 1 1 1 1h-1 0-2c-2 0-4-1-6-1h-6l-2-1c-6-5-8-13-9-20z"></path><defs><linearGradient id="o" x1="289.384" y1="453.306" x2="268.013" y2="458.293" xlink:href="#B"><stop offset="0" stop-color="#717172"></stop><stop offset="1" stop-color="#c7c6c6"></stop></linearGradient></defs><path fill="url(#o)" d="M266 423c3 2 6 3 8 6l25 58c-2-1-4-3-6-5-5-3-10-4-15-7-3-1-5-3-8-5s-5-4-7-8c-1-2-2-5-2-7 0-5 2-8 5-12 2-1 4-3 6-4l-8-1c1-2 1-4 1-6h0 3v-1c0-1 0-2-1-3v-2c0-1 0-2-1-3z"></path><path d="M266 443h1c1 2 1 4 1 5 1 1 1 2 1 2 1 3 2 6 2 9 1 3 0 6 0 9h0c0 1-1 2-1 2-3-2-5-4-7-8-1-2-2-5-2-7 0-5 2-8 5-12z" class="B"></path><path d="M315 99l3 7c3 4 5 9 9 12 1 0 1 1 2 1 2 2 5 5 8 6 1 1 3 1 3 2-2-1-4-1-6-1s-4 2-6 3l-2 2c-1 1-4 2-6 4-1 0-2 1-3 1l-1 1-1 1c0 1 1 1 1 1h0c1 0 1-1 2-1h0l-1 1h2 0l-1 2c-3 3-7 3-10 6h2c-6 2-12 4-16 9 1 0 2-1 3-1h0c0 1-3 3-4 4-2 3-4 6-5 10-1 2-1 3-2 5h0 0-1c0 1 0 3-1 4h0v-2c0-2 0-5 1-8 4-10 11-20 19-27 8-6 17-10 25-14h-13-18-83-91-24c-7 0-14 0-21-1 7 4 13 9 19 14 2 1 4 3 5 3l3-1h-1c0-1 1-2 1-2 2-2 3-3 5-3l12 3c3 1 6 3 9 4 10 5 19 11 26 19 2 2 4 5 6 7 3 5 7 10 9 15-1-1-1-2-3-3l-2-5-1-1c0 1 0 2 1 2l1 2v1l1 2h0l1 1c1 1 0 2 1 3l6 15-2 1c-4-11-9-23-16-33-5-6-13-14-22-15v1c-2 0-5-1-7 0h-6c-4 2-9 3-13 5l-21 14s-1 1-2 1c-2 1-4 2-7 3-1 0-3 1-5 1-4 0-10-3-13-6 2 2 4 3 7 3 11 2 21-3 30-9 0-1 0-2 1-2h0l4-3c0-1-1-1-1-1l-2-2h0l-1-1s-1-1-1-2l-1-1v-1h0c-1-1-1-1-1-2v-4-1l1-1v-1-2s-1-1-2-1l-1-1-1-1h0l-4-3h-1c-1-1-1-1-1-2l-8-6c-3-2-6-3-8-5l9-6 3-3h3 7 29 124 74c-4-4-7-10-10-15l2-2z" class="G"></path><path d="M306 143c1-2 3-3 5-5l17-9-2 2c-1 1-4 2-6 4-1 0-2 1-3 1l-1 1-1 1c0 1 1 1 1 1h-1c-1 1-3 2-5 3v-1l1-1h1 0v-1l-1 1c-1 1-4 2-5 3z" class="H"></path><path d="M158 163c2 2 4 5 6 7 3 5 7 10 9 15-1-1-1-2-3-3l-2-5-1-1c0 1 0 2 1 2l1 2v1l1 2h0l1 1c1 1 0 2 1 3-5-8-9-16-15-22l1-2z" class="B"></path><path d="M121 148c6 1 11 4 16 6-5 0-9 0-14 2-1-2-2-4-2-5v-2l-1-1h1 0z" class="J"></path><path d="M316 139h0c1 0 1-1 2-1h0l-1 1h2 0l-1 2c-3 3-7 3-10 6h2c-6 2-12 4-16 9 1 0 2-1 3-1h0c0 1-3 3-4 4-2 3-4 6-5 10-1 2-1 3-2 5h0 0c0-4 1-7 3-11 1-2 3-4 4-6 3-6 7-11 13-14 1-1 4-2 5-3l1-1v1h0-1l-1 1v1c2-1 4-2 5-3h1z" class="B"></path><path d="M111 137l12 3c3 1 6 3 9 4 10 5 19 11 26 19l-1 2c-4-4-8-8-13-10-3-1-6-2-8-3-6-2-11-5-16-7s-9-2-14-3h-1c0-1 1-2 1-2 2-2 3-3 5-3z" class="F"></path><defs><linearGradient id="p" x1="121.428" y1="150.681" x2="99.431" y2="154.133" xlink:href="#B"><stop offset="0" stop-color="#a6a5a5"></stop><stop offset="1" stop-color="#d6d5d5"></stop></linearGradient></defs><path fill="url(#p)" d="M90 136l9 6c0 1 2 2 3 2 1 1 4 0 5 0 6 0 10 2 14 4h0-1l1 1v2c0 1 1 3 2 5-6 1-10 4-15 7-2 1-5 3-7 5 0-1 0-2 1-2h0l4-3c0-1-1-1-1-1l-2-2h0l-1-1s-1-1-1-2l-1-1v-1h0c-1-1-1-1-1-2v-4-1l1-1v-1-2s-1-1-2-1l-1-1-1-1h0l-4-3h-1c-1-1-1-1-1-2z"></path><path d="M89 118h165 46 15c3 0 6-1 9 0 1 0 1 1 2 1l7 5v1c-3 1-9 1-13 1H153h-48c-10 0-20 0-29-1 3-2 9-7 13-7h0z" class="Y"></path><defs><linearGradient id="q" x1="445.066" y1="378.368" x2="393.706" y2="346.064" xlink:href="#B"><stop offset="0" stop-color="#a4a3a3"></stop><stop offset="1" stop-color="#f2f1f1"></stop></linearGradient></defs><path fill="url(#q)" d="M432 287l3 1c2 1 5 3 7 3 4 1 7 1 12 1-2 4-3 8-3 12h1c0 2-1 5 0 8h-1v4h0c0 5 2 11 4 16 0 2-2 7-3 9l-29 67c-1 4-3 8-5 12l-9 21c-2 5-3 11-6 16-1 2-2 5-3 6h-1-1l-3-7 1-2c-1-1-2-3-2-5l1-1v-1c0-1 1-3 1-4v-1h-2c0-2-1-3-1-4-1-1-1-3-1-3-1-2-3-3-5-3h0l-1-1c-1-4-3-7-5-11-1-3-2-7-4-10 0-2-1-3-1-4h0c0-1-1-4 0-5 0-1 1-1 1-1v-1c-1-1-1-4-1-6 0 2 0 3 1 4h0c2 2 3 3 5 4l1 1v-1c-1-1-3-2-4-4h0c0-1 1-1 1-2s0-1 1-1c0-3 0-7-1-9 2-4 1-9 1-13 0 0 1 4 1 5 1 1 2 2 3 4 3-5 7-10 13-12 5-3 11-3 17-1 1 0 2 1 3 2l1 1h0c1-1 3 0 4 1h0l4-7c2-2 4-5 5-7 2-8 6-15 8-23 0-3 1-6 1-9v-1c1-3 1-6 1-10v-5c0-2-1-3-1-4h-1c0-2 0-2-1-3 0-1 0-1-1-2 0-1 0-1-1-2h-1v-2s-1 0-1-1h-1v-1c0-1-1-1-1-2l-1-1c0-1 0-1-1-2v-1-1-1l1 1v-1z"></path><path d="M436 299h1-1zm-2-4h1v1h-1v-1z" class="B"></path><path d="M432 287l3 1c1 3 2 5 4 7l-1 1h-1c0-1-1-1-1-3h-2l1-1 1 1-1-1h0l-1-2c-1 0-1-1-1-2l-1 1v-1-1z" class="H"></path><path d="M435 288c2 1 5 3 7 3 4 1 7 1 12 1-2 4-3 8-3 12h1c0 2-1 5 0 8h-1v4h0c0 5 2 11 4 16 0 2-2 7-3 9 0-1 0-2-1-3v-3l-2-2v-1h0v-1-3l-1-1v-3h0c-1-1-1-1-1-2h0v-1-2-5-1-2h-1l-1-1v-1h0v-2c0-1-1-1-2-2-1-2-1-4-3-5h0l1-1c0 1 1 1 1 1h0l-3-3v-1-1c-2-2-3-4-4-7z" class="I"></path><path d="M442 291c4 1 7 1 12 1-2 4-3 8-3 12h1c0 2-1 5 0 8h-1v4c-1-1-1-2-1-3v-7c-1-7-3-12-8-15z" class="K"></path><path d="M451 304h1c0 2-1 5 0 8h-1v4c-1-1-1-2-1-3l1-9z" class="O"></path><defs><linearGradient id="r" x1="398.131" y1="462.004" x2="407.418" y2="427.339" xlink:href="#B"><stop offset="0" stop-color="#504f50"></stop><stop offset="1" stop-color="#848383"></stop></linearGradient></defs><path fill="url(#r)" d="M386 394h0 0c0 2 1 4 1 5v1l1 1v1c0 1 0 1 1 2v1 1h1l3 6c2 3 3 6 5 10l1 3v1c0 1 1 2 1 3 1 1 1 2 0 4h2s1-1 1-2c1 0 1-1 2-1h2c1 0 6-1 7-1v-1s0-1 1-2v-2l1-1 1-2c0-1 1-2 1-3l1-1c0-2 1-3 2-5 0-1 0-1 1-2v-2h1c-1 4-3 8-5 12l-9 21c-2 5-3 11-6 16-1 2-2 5-3 6h-1-1l-3-7 1-2c-1-1-2-3-2-5l1-1v-1c0-1 1-3 1-4v-1l1-1v-1c1-3 1-5 1-7v-6-1c0-1 0-2-1-2v-1c-1-3-2-6-4-9-1-2-3-4-4-6v-1c-1 0-1-1-1-2l-1-3-1-1v-1c0-1 0-3-1-4 0-1 0-2 1-2z"></path><path d="M396 454c1 2 3 6 3 9h-1l-3-7 1-2z" class="E"></path><path d="M398 433c0 1 0 3 1 4h0l1-1h1v-1l3-2v2l-1 1h0c0 1-2 3-3 4h-1s-1 0-2 1v-1c1-3 1-5 1-7z" class="S"></path><path d="M396 443l2-1 2-1 2-2c0 1 0 2-1 3h0l-6 6v-1c0-1 1-3 1-4z" class="W"></path><defs><linearGradient id="s" x1="393.555" y1="441.426" x2="384.717" y2="401.339" xlink:href="#B"><stop offset="0" stop-color="#929191"></stop><stop offset="1" stop-color="#dfdedf"></stop></linearGradient></defs><path fill="url(#s)" d="M376 393c0 2 0 3 1 4h0c2 2 3 3 5 4l-2 1v1c2 0 4 3 7 3v-1-3l1 3c0 1 0 2 1 2v1c1 2 3 4 4 6 2 3 3 6 4 9v1c1 0 1 1 1 2v1 6c0 2 0 4-1 7v1l-1 1h-2c0-2-1-3-1-4-1-1-1-3-1-3-1-2-3-3-5-3h0l-1-1c-1-4-3-7-5-11-1-3-2-7-4-10 0-2-1-3-1-4h0c0-1-1-4 0-5 0-1 1-1 1-1v-1c-1-1-1-4-1-6z"></path><path d="M376 393c0 2 0 3 1 4h0c0 1 0 1 1 2 2 2 1 5 1 8l-1 1h1c2 1 4 1 6 2 4 2 9 10 8 15 0 2-3 4-5 5 0 1 0 1-1 0 1-2 5-3 5-6 1-1 0-3-1-5-3-6-8-8-14-10l-1-3c0-1-1-4 0-5 0-1 1-1 1-1v-1c-1-1-1-4-1-6z" class="Z"></path><path d="M376 406l1 3c6 2 11 4 14 10 1 2 2 4 1 5 0 3-4 4-5 6l-1 1c-1-4-3-7-5-11-1-3-2-7-4-10 0-2-1-3-1-4h0z" class="I"></path><path d="M380 385c2-4 1-9 1-13 0 0 1 4 1 5 1 1 2 2 3 4 3-5 7-10 13-12 5-3 11-3 17-1 1 0 2 1 3 2l1 1h0c1 0 2 1 2 2 0 3-1 5-2 8-3 6-7 12-11 17l-7 7c-2 1-4 1-6 1h-5s0-1-1-2c0-1-1-5-1-6-1-3 0-5-1-8 0-1 1-2 0-3-1 1-1 1-1 2v1 4c-1 0-1 1-1 2 1 1 1 3 1 4v1l1 1v3 1c-3 0-5-3-7-3v-1l2-1 1 1v-1c-1-1-3-2-4-4h0c0-1 1-1 1-2s0-1 1-1c0-3 0-7-1-9z" class="F"></path><path d="M380 385c2-4 1-9 1-13 0 0 1 4 1 5 1 1 2 2 3 4 3-5 7-10 13-12 5-3 11-3 17-1 1 0 2 1 3 2-5-2-11-3-17-1-3 2-5 4-8 6v1c-1 0-2 0-2 1v1c-1 0-1 1-2 1v1 1h-1v2s-1 1-1 2v-2-1c0-1 0 0 1-1v-1h0c-1 1-2 2-2 3h0c-2 0-2 0-3-1-2 1-2 7-2 9 1 1 1 3 1 4l1 3c-1-1-1-3-2-4 0-3 0-7-1-9z" class="D"></path><path d="M386 383v-1c1-4 7-10 11-11l-4 4v1c-1 0-2 0-2 1v1c-1 0-1 1-2 1v1 1h-1v2s-1 1-1 2v-2-1c0-1 0 0 1-1v-1h0c-1 1-2 2-2 3h0z" class="X"></path><path d="M478 147l-32-25h1c2-1 4-3 6-4 1-1 2-2 3-2h3 10 42 94 51c3 3 7 5 11 8l-31 20h-2c-2 0-4 1-6 2-5 2-11 6-16 8 6 0 13 2 19 5 5 3 10 7 15 10 3 3 7 6 10 7 7 3 14 1 20-2-4 3-8 5-13 5-6 0-10-3-15-7l-6-5c-2 1-4 1-6 1s-5-1-8 0c-1 0-1 1-2 2l-2-2c-3-2-4-3-6-5v-1h-1c0-1 0-2 1-2v-2h0l-2-2c-3 0-5 0-7 1l-2 1v-1l1-1v-1c-3 1-6 3-8 4-3 2-6 6-9 8-4 5-10 12-12 19-1 3-1 8-1 12l3 16 3 14v2c1 2 0 3 0 5 1 2 1 6 1 8-1 1-1 2-1 3h0v3 1h-1s-1 0-1 1v1c-1 0-1 1-1 2s-1 2-1 3v1h-1 0l-1 1-2 2h-1s-1 1-2 1c-1 1-2 1-3 2 4 1 7 3 11 5 5 4 8 9 14 12h-1-1c-4-2-7-6-10-9-3-2-6-3-9-5-5-2-13-4-18-2h-3l-5 2c-1 1-1 2-3 3v2c-1 0-2 1-3 1l-1-1-6 15-2 1h0c1-4 3-8 5-12l19-44c8-17 14-36 23-52 5-8 11-17 19-22 4-2 8-3 12-5 8-5 15-8 23-10 6-2 12-7 17-10l10-7c-9 2-19 1-28 1h-53-85-25c-5 0-10 0-15-1h-1l38 30c3 4 6 7 9 10 5 7 7 17 7 26 0 4 0 10-2 14h0c-1-2 1-7 1-9 0-9-1-19-6-27-3-8-12-14-19-19-1-2-3-3-4-4z" class="G"></path><path d="M541 272c2-3 4-4 7-5-1 1-1 2-3 3v2c-1 0-2 1-3 1l-1-1z" class="B"></path><defs><linearGradient id="t" x1="567.815" y1="224.651" x2="573.584" y2="228.092" xlink:href="#B"><stop offset="0" stop-color="#7e7d7e"></stop><stop offset="1" stop-color="#949594"></stop></linearGradient></defs><path fill="url(#t)" d="M570 218c1 3 3 14 3 16l-1-1-5-5 3-10z"></path><defs><linearGradient id="u" x1="572.894" y1="206.944" x2="577.439" y2="215.093" xlink:href="#B"><stop offset="0" stop-color="#7f7f80"></stop><stop offset="1" stop-color="#a3a1a1"></stop></linearGradient></defs><path fill="url(#u)" d="M573 226c-1-3-1-5-2-7 0-3 5-14 6-18l3 14c-2 1-3 2-4 4-2 2-3 4-3 7z"></path><path d="M559 241c1-5 5-9 7-12 2 2 5 4 5 6 1 1 1 3 2 4h0c0 4-1 7-2 11h0l-1-1v-1c1-2 2-5 1-7s-3-3-4-3c-2-1-4 0-5 1l-3 2z" class="D"></path><defs><linearGradient id="v" x1="627.591" y1="158.142" x2="628.874" y2="168.791" xlink:href="#B"><stop offset="0" stop-color="#a8a7a8"></stop><stop offset="1" stop-color="#d3d2d2"></stop></linearGradient></defs><path fill="url(#v)" d="M616 156c4 0 9 1 13 3s9 5 13 8c-2 1-4 1-6 1s-5-1-8 0c-1 0-1 1-2 2l-2-2c-3-2-4-3-6-5v-1h-1c0-1 0-2 1-2v-2h0l-2-2z"></path><path d="M578 188c0 4-1 9-2 13s-2 7-4 10l-6 13-6 11c-3 7-7 14-11 20h-1c1-2 2-3 3-5l7-15 7-16 13-31zm-19 53l3-2c1-1 3-2 5-1 1 0 3 1 4 3s0 5-1 7v1c-2 6-6 11-11 13-3 2-6 2-8 2-3 1-6 2-8 4 0-2 6-11 6-11l10-16z" class="H"></path><path d="M559 241l3-2c1-1 3-2 5-1 1 0 3 1 4 3s0 5-1 7c0-2-2-4-4-6h-3 0c-1 0-3 1-4 2v1l-1 1v1c-1 2-3 5-5 6v-1c-1 2-2 4-4 5l10-16z" class="I"></path><defs><linearGradient id="w" x1="557.361" y1="243.951" x2="584.548" y2="237.202" xlink:href="#B"><stop offset="0" stop-color="#a6a4a6"></stop><stop offset="1" stop-color="#e8e7e7"></stop></linearGradient></defs><path fill="url(#w)" d="M573 226c0-3 1-5 3-7 1-2 2-3 4-4l2 9c1 2 1 3 2 4v2c1 2 0 3 0 5 1 2 1 6 1 8-1 1-1 2-1 3h0v3 1h-1s-1 0-1 1v1c-1 0-1 1-1 2s-1 2-1 3v1h-1 0l-1 1-2 2h-1s-1 1-2 1c-1 1-2 1-3 2l-11-1c6-3 10-7 12-13h0 0c1-4 2-7 2-11l1-5v-5l-1-3z"></path><path d="M573 226c0-3 1-5 3-7 1-2 2-3 4-4l2 9c1 2 1 3 2 4v2l-1-1c0-1 0-1-1-2h0l-1-4v-1c-1-1-1-1-1-2s0-2-1-3c0 1 0 2-1 2h0c-1 0-1 1-2 2l1 1h-1c0 1-1 3-1 3-1 2-1 2-1 4l-1-3z" class="B"></path><path d="M574 234l1-2v4c-1 1-1 1 0 1 1 1 2 1 3 2 0-1 0-2 1-3 0 3 0 5-1 9-1 0-1 1-2 2l-1 1v-1l-1-1v-2c-1 1-1 1-1 2h1v1l-1 1v-1l-1 1v2h-1c1-4 2-7 2-11l1-5z" class="R"></path><path d="M462 118h192c2 0 9 5 10 6-2 2-9 1-12 1l-141 1h-39c-5 0-12 0-17-1-2 0-4-1-6-3 2-1 5-2 7-4 0-1 5 0 6 0z" class="Y"></path><path d="M277 552h1l1 1h1 0c1 0 2 1 3 1v1h-2v1h-1v1c0 1-1 1-1 1 1 1 2 2 3 2h0 1c-1-1-1-1-2-1 4 1 8 1 12 1l-1 1c1 1 2 1 3 2l2 1c1 1 2 1 3 2 1 0 2 2 2 3l4 1h-3l8 4c2 1 5 1 8 3 2 2 3 4 3 7 1 1 1 3 0 4-1 0-1 1-2 1l-2-1h0-6l-6-1 8 6c1 1 1 2 3 2h1 1c1 1 3 2 3 3v1h1l3 4v1c1 1 1 1 1 2h1l1 2h0c0 1 0 1 1 2l1 1 3 6c1 1 1 1 1 2 1 1 1 1 2 1v2l1 1 1 1c0 1 1 1 1 2h0l1 1c2 3 4 5 7 8 1 1 2 2 4 3l2 2c1 0 3 2 4 3h2c1 1 2 1 2 1l1 1h3v1c0 1 1 3 2 4l1 1c1 3 3 9 4 10 2-1 3-4 3-6 1 0 1-1 1-1 2-7 6-13 8-19l13-31c3-7 6-13 8-19 1 1 3 3 4 3 2 1 3 2 5 3 1 1 3 2 5 2 5 1 10 0 15-3v-1c-3 0-8 1-10-1h-1-1c0-1-1-2-1-3 0-4 2-6 4-8 1-1 3-2 4-2 4-1 7-3 11-5h1v-1h-1c5-4 10-7 16-8h0 1s1 0 1-1h1 1l-2 3c-1 2-2 3-3 4l-1 3-3 6-1 1c-1 1-1 1-1 2l-3 3-2 3c0 1 0 1-1 2v1l-6 8c-1 1-1 2-2 3l-1 1c0 1-1 2-1 3l-1 2-2 2s0 1-1 1v1l-1 1c0 1 0 2-1 2 0 1-1 2-1 2-1 1-1 2-2 3l-1 3-3 6h-1v1l-2 2v1l-5 7-9 10c0 1-1 2-2 3s1 0-1 1c0 0 0 1-1 1-1 2-2 3-3 4-1 2-4 4-5 6-1 0-2 2-2 2h-1l-1 1c-1 1-2 2-3 2h0l-2 2h-1l-3 1h-1v1h-1-2c-1 1-2 1-3 1-1 1-5 0-7 0h-2c-1-1-1-1-2-1h-1-1l-1-1h0-2l-1-1c-1 0-1 0-2-1-1 0-1 0-2-1h0c-1-1-2-2-3-2-2-1-4-4-5-5s-2-1-2-2c-1-1-2-1-2-2s-1-1-1-2c-1 0-1-1-2-2l-1-1c0-1 0-1-1-1-1-2 1 0 0-1l-3-3c0-1 0-1-1-2l-1-1-2-3-1-2c-1-1-1-2-2-3l-1-1-1-1c0-1 0-1-1-2 0-1-1-1-1-2-1-1-1-1-1-2l-1-1-2-3-3-6v-1h0l-1-1-1-1c0-1 0-2-1-3 0 0-1-1-1-2l-3-4c-1-1-1-1-1-2-1-1-1-2-2-3h0l-1-1-1-1v-1h-1c0-1 1-1 0-1l-1-1c0-1 0-1-1-1v-1l-1-1c-1-1 0-1-1-1l-3-5-1-1-2-3-3-4-3-5v-1l-1-1-1-1v-1c-1-1 0-1-1-2l-1-1v-1-1l-1-1-1-2v-2c-1-1 0-2-1-3h0v-1-1h0v-2z" class="H"></path><path d="M281 559c4 1 8 1 12 1l-1 1c1 1 2 1 3 2l2 1c1 1 2 1 3 2 1 0 2 2 2 3-3-1-7-4-10-6-1-1-3-1-5-2s-3-1-4-1c-1-1-1-1-2-1z" class="E"></path><path d="M441 570v1c-1 2-3 5-5 7 0 1-2 2-2 2l-7 7c0 1 1 1 2 1 2 0 4 0 6 1h1 1c-2 2-4 3-6 4 0 0-4 1-5 2-3 0-6-1-9-1h-1s-1-1-2-1v-1l1-1c1 1 3 2 5 2 5 1 10 0 15-3v-1c-3 0-8 1-10-1h-1-1c0-1-1-2-1-3 0-4 2-6 4-8 1-1 3-2 4-2 4-1 7-3 11-5z" class="S"></path><path d="M426 577c1-1 3-2 4-2h3s1 0 0 1c-1 4-7 7-8 11l2 1h-3-1c0-1-1-2-1-3 0-4 2-6 4-8z" class="C"></path><path d="M312 588c2 0 4 0 6-1l-6-3v-1c-1 0-2 0-3-1h0 0c-2-1-5-3-6-4v-2h0l-1-1v-2h-1v-1h1v-1-1h1l8 4c2 1 5 1 8 3 2 2 3 4 3 7 1 1 1 3 0 4-1 0-1 1-2 1l-2-1h0-6z" class="S"></path><path d="M311 574c2 1 5 1 8 3 2 2 3 4 3 7 1 1 1 3 0 4-1 0-1 1-2 1l-2-1h0c0-2-2-4-4-6-1-2-3-4-4-6 0-1 0-1 1-2z" class="C"></path><defs><linearGradient id="x" x1="391.617" y1="604.225" x2="403.6" y2="614.007" xlink:href="#B"><stop offset="0" stop-color="#555455"></stop><stop offset="1" stop-color="#8f8f8f"></stop></linearGradient></defs><path fill="url(#x)" d="M377 654c2-7 6-13 8-19l13-31c3-7 6-13 8-19 1 1 3 3 4 3 2 1 3 2 5 3l-1 1v1c1 0 2 1 2 1h1c3 0 6 1 9 1l-1 1c-1 0-1-1-2 0l-1 1h-1c-1-1-1 0-2-1-1 0-3 1-3 2l-1 1c0 1-1 1-1 2-1 1-2 1-2 2s-1 1-1 2c-1 0-1 1-2 1 0 1 0 1-1 2l-1 1v1l-3 4-1 2c-1 0-1 1-1 1l-1 1-1 1-1 1c-1 1 1-1 0 1-1 0-1 0-1 1-2 2-4 3-5 6-1 1-3 2-4 2 0 1-1 2-1 2l-2 4-1 2h0l-1 1-2 5v2c-1 1-1 2-2 3v1l-1 1v1h0l-2 2z"></path><path d="M301 66c1 1 2 1 3 1 11 1 18-9 26-15 6-5 13-8 20-11 17-8 35-11 54-9 1 1 3 1 4 1l2 1 8 3c14 6 25 17 30 32 5 14 3 28-3 40-7 17-23 29-39 36l-2-1h0c-1 0-2 0-3 1-1 0-1 0-2 1l2 1v1c2 2 2 3 2 6-1 1-1 2-2 3h0v-1-4h-2c-1 0-1 0-2 2l-1 3c0-3 0-6-1-9v-1c-1 1-1 2-1 3-1 2-1 3-1 4s0 7-1 7l-1 10v2-4c1-9 0-19-3-27-2-4-5-8-8-12l-2-2c-1 3-2 6-4 9l-1 1c-3 4-5 6-9 8l-2-1c2-1 3-1 3-2l-3-2-12-8c-3-2-7-3-10-6 0-1-2-1-3-2-3-1-6-4-8-6-1 0-1-1-2-1-4-3-6-8-9-12l-3-7-2-3c-2-5-5-10-9-14l-1 1h-3c-2-2-5-4-8-5-1-1-3-1-4-1s-3 1-4 2l2-2c4-3 9-6 14-6h1 0c-1-1-1-2-1-3h0l-1-1 1-1h1z" class="R"></path><path d="M408 33l2 1c0 1 0 1-1 2l-3-1h1 0l1-2z" class="Z"></path><path d="M404 32c1 1 3 1 4 1l-1 2h0-1l-3-1 1-1v-1z" class="N"></path><path d="M410 34l8 3c-1 0-1 1-2 1l-7-2c1-1 1-1 1-2z" class="C"></path><path d="M388 141h1c4 1 7 0 12-1-1 2-2 2-2 3v1c1 0 1 0 2 1-1 0-1 0-2 1-1-1-2-1-3-2-2-1-3-1-4-1-2-1-3-1-4-2z" class="U"></path><path d="M323 101c-2-6-2-12-1-18 1-8 5-15 9-22 2-3 4-6 6-8 1 1 0 1 0 2-4 4-8 10-10 16l-3 7c-3 8-2 19 1 26v1l-1 1-1-5z" class="J"></path><path d="M347 124c5 4 10 7 15 10 4 1 7 2 11 3l-6 6c-1-1-3-2-4-3h0c-1-1-2-1-2-2-1 0-1-1-2-1l-1-1h0 3c1 0 2 1 3 1 2 1 3 1 5 1l1 1 1-1c-1-1-3-1-5-2-1 0-3-1-5-2-4-2-10-5-13-9 0 0 0-1-1-1z" class="H"></path><path d="M300 68c2 2 3 4 5 5l1 1c-4 0-8-1-12 0-2 0-3 1-5 1 5 1 11 4 15 7l-1 1h-3c-2-2-5-4-8-5-1-1-3-1-4-1s-3 1-4 2l2-2c4-3 9-6 14-6h1 0c-1-1-1-2-1-3h0z" class="N"></path><path d="M324 78v1c1 0 0 1 0 2v1c0 1-1 3 0 4v-1l1-1v-3s0-1 1-2v-2-1h1c0 1-1 2-1 3v1c-1 2-2 5-1 7s0 5 1 8v4c1 0 1 1 1 2l1 2-1 1v-1c-1 1-2 1-2 1-3-7-4-18-1-26z" class="X"></path><path d="M358 94c5-4 10-8 11-15v-4 1c1 3 0 6 0 9 4-6 5-11 5-19h0c2 4 2 7 2 11h5c-1 1-1 1-1 2-1 0-2 0-2 1-1 1-3 2-5 2l-3 3-10 8-3 2s0-1 1-1z" class="D"></path><path d="M337 102h0c0-5 0-10 2-15 1-4 5-9 9-11h1c-1-4-2-6-1-10 0-4 4-8 7-11v1c-1 2-3 3-4 5-3 8 3 17 5 24h0 0c-1-2-3-5-5-7-1 0-3 0-5 1l-3 3c-4 7-5 14-4 22l-2-2z" class="O"></path><defs><linearGradient id="y" x1="330.912" y1="120.701" x2="337.591" y2="109.702" xlink:href="#B"><stop offset="0" stop-color="#b5b4b5"></stop><stop offset="1" stop-color="#e8e8e6"></stop></linearGradient></defs><path fill="url(#y)" d="M328 103c2 6 4 12 8 17 1 2 2 3 4 4 1 1 2 1 3 1 1 1 2 1 3 2l2 1c-1 0-1 0-2-1v1h0c3 1 11 5 12 8h0l1 1c1 0 1 1 2 1 0 1 1 1 2 2h0l-1 1-12-8c-3-2-7-3-10-6 0-1-2-1-3-2-3-1-6-4-8-6v-1-1-1c-1-2-2-5-3-7 0-1 0-3-1-4v-1s1 0 2-1v1l1-1z"></path><path d="M301 66c1 1 2 1 3 1 11 1 18-9 26-15 6-5 13-8 20-11 17-8 35-11 54-9v1l-1 1c-18-3-38 2-55 9-5 3-11 5-16 9-6 5-11 11-18 14-2 2-6 3-9 3-1 0-3-1-4-3z" class="G"></path><defs><linearGradient id="z" x1="318.845" y1="113.559" x2="324.913" y2="90.32" xlink:href="#B"><stop offset="0" stop-color="#bcbbbc"></stop><stop offset="1" stop-color="#e2e3e1"></stop></linearGradient></defs><path fill="url(#z)" d="M313 96c1-2 1-3 1-5 0-1 0-1 1-1 0-1-1-1 0-2v-1-1c1 0 1-1 2-2v-1-1h1v3h0c0 2-1 7 0 8 0 1 0 1 1 2h0c0 3 1 4 3 6h1l1 5 1-1c1 1 1 3 1 4 1 2 2 5 3 7v1 1 1c-1 0-1-1-2-1-4-3-6-8-9-12l-3-7-2-3z"></path><path d="M318 106c4 1 4 3 6 6l1 1 1 2c1 1 1 2 1 2v1c-4-3-6-8-9-12z" class="H"></path><path d="M418 37c14 6 25 17 30 32 5 14 3 28-3 40-7 17-23 29-39 36l-2-1h0c7-3 13-6 19-11 14-11 24-25 26-43 1-7 0-14-2-20-3-9-9-17-16-23-5-4-10-7-15-9 1 0 1-1 2-1zm-25 19c6-3 12-4 18-3 9 3 16 9 20 17 5 9 5 19 2 29-4 14-13 25-26 31-7 4-14 6-21 6 1 2 2 3 2 5 1 1 2 1 4 2 1 0 2 0 4 1 1 1 2 1 3 2l2 1v1c2 2 2 3 2 6-1 1-1 2-2 3h0v-1-4h-2c-1 0-1 0-2 2l-1 3c0-3 0-6-1-9v-1c-1 1-1 2-1 3-1 2-1 3-1 4s0 7-1 7l-1 10v2-4c1-9 0-19-3-27-2-4-5-8-8-12l-2-2c-1 3-2 6-4 9l-1 1c-3 4-5 6-9 8l-2-1c2-1 3-1 3-2l-3-2 1-1c1 1 3 2 4 3l6-6c-4-1-7-2-11-3-5-3-10-6-15-10v-1c-6-6-9-13-10-21l2 2c1 8 5 15 11 20 6 7 16 9 24 12l4-10c3 3 5 6 7 9 7-1 15-2 21-6 12-6 21-16 26-30 2-8 2-19-2-27s-10-14-18-16c-6-3-12-2-18 1l-1-1z" class="G"></path><path d="M397 154v-8c1 1 3 2 4 3 0 2 1 4 0 7v-4h-2c-1 0-1 0-2 2zm-5 7v-5c0-4-1-8-2-12 1 0 4 0 5 1v2c-1 1-1 2-1 3-1 2-1 3-1 4s0 7-1 7z" class="B"></path><path d="M394 57c6-3 12-4 18-1 8 2 14 8 18 16s4 19 2 27c-5 14-14 24-26 30-6 4-14 5-21 6-2-3-4-6-7-9l-4 10c-8-3-18-5-24-12-6-5-10-12-11-20s0-15 4-22l3-3c2-1 4-1 5-1 2 2 4 5 5 7h0 0c3 2 0 6 2 9-1 0-1 1-1 1l3-2 10-8 3-3c2 0 4-1 5-2 0-1 1-1 2-1 0-1 0-1 1-2h0c0-3 1-5 1-7 2-6 6-11 11-14l1 1z" class="Y"></path><path d="M381 77h0c1 5 2 10 6 13l2 1c-1 0-3 0-4-1-2-1-3-3-5-3 0-2 0-2 1-2-1 0-2 0-2 1h-2c-1 0-1 0-1 1-1 1-3 2-4 2-3 2-7 4-10 4h-2l10-8 3-3c2 0 4-1 5-2 0-1 1-1 2-1 0-1 0-1 1-2z" class="B"></path><path d="M381 77c0-3 1-5 1-7 2-6 6-11 11-14l1 1c-4 3-7 6-7 12 0 2 0 4 2 6s4 2 7 2h1v1c-1 3-1 7-2 11v5c2 1 3 2 5 2l5-1c-3 1-6 2-9 1-1 0-2-1-3-3v-1c-1-1-3-1-4-1l-2-1c-4-3-5-8-6-13z" class="N"></path><path d="M386 63h1c-1 2-2 3-2 5 0 4 1 6 3 9 2 2 5 2 7 2 0 2-1 5-1 7v4h0c-2 0-4-1-6-2-3-1-4-5-5-8-1-6 0-12 3-17z" class="Y"></path><path d="M339 104c-1-8 0-15 4-22l3-3c2-1 4-1 5-1 2 2 4 5 5 7h0 0c3 2 0 6 2 9-1 0-1 1-1 1v1h-2v-1-2c-1-1 0 0 0-1v-1l-1-2c-2-1-3-1-5-1l-3 3v1 2c1 1 0 1 0 2h0c0 1-1 2-1 3v-1h0-1 0v3 1h0v1 1 1 2h0c1 1 1 1 1 2s1 2 1 3h-1-1v-2h0v1c0 1 0 2 1 3v3l2 2h0l1 1s1 1 1 2l1 2c-6-5-10-12-11-20z" class="B"></path><path d="M492 156l-38-30h1c5 1 10 1 15 1h25 85 53c9 0 19 1 28-1l-10 7c-5 3-11 8-17 10-8 2-15 5-23 10-4 2-8 3-12 5-8 5-14 14-19 22-9 16-15 35-23 52l-19 44c-2 4-4 8-5 12v1l-95 220-5 12 1 1c-3 5-5 10-7 16 0 0-2 3-2 4-1 2-2 3-2 6h0c-3 4-5 8-6 12-1 2-2 4-2 6v9 1h1c0 3 1 5 2 8v2c0-1-1-2-1-3-2-4-3-8-3-13v-2l-7 16 3 4c-1 0-3-2-4-3-2 6-5 12-8 19l-13 31c-2 6-6 12-8 19 0 0 0 1-1 1 0 2-1 5-3 6-1-1-3-7-4-10l-1-1c-1-1-2-3-2-4v-1h-3l-1-1s-1 0-2-1h-2c-1-1-3-3-4-3l-2-2c-2-1-3-2-4-3-3-3-5-5-7-8l-1-1h0c0-1-1-1-1-2l-1-1-1-1v-2c-1 0-1 0-2-1 0-1 0-1-1-2l-3-6-1-1c-1-1-1-1-1-2h0l-1-2h-1c0-1 0-1-1-2v-1l-3-4h-1v-1c0-1-2-2-3-3h-1-1c-2 0-2-1-3-2l-8-6 6 1h6 0l2 1c1 0 1-1 2-1 1-1 1-3 0-4 0-3-1-5-3-7-3-2-6-2-8-3l-8-4h3l-4-1c0-1-1-3-2-3-1-1-2-1-3-2l-2-1c-1-1-2-1-3-2l1-1c-4 0-8 0-12-1v-1-1h0c2-2 3-2 5-3l-9-3h1c-1 0-1 0-1-1 1 0 1-1 2-1v-1c1-1 4-3 6-4h2c1 0 3-1 5-1v-4-4l-1-1v-1c1 1 2 3 3 3h1l2 2c2-3 4-7 3-10v-1c-1-4-2-9-5-12-5-5-9-4-15-4 2-1 4-1 6-1 2 1 4 1 6 0 1 0 1 1 2 1v-1h1s1 0 1 1h1c2 0 6 4 7 3 1 1 2 1 3 2 2 0 3 1 4 3v-1s0-1-1-2v-1c-1-1-2-1-2-3l3 3c0-2-2-5-3-7h1l-36-85c-2-4-3-9-5-12l-86-196-1-4-2-1c-1-2-2-3-2-5 0-1 0-1-1-2l2-1-6-15c-1-1 0-2-1-3l-1-1h0l-1-2v-1l-1-2c-1 0-1-1-1-2l1 1 2 5c2 1 2 2 3 3-2-5-6-10-9-15-2-2-4-5-6-7-7-8-16-14-26-19-3-1-6-3-9-4l-12-3c-2 0-3 1-5 3 0 0-1 1-1 2h1l-3 1c-1 0-3-2-5-3-6-5-12-10-19-14 7 1 14 1 21 1h24 91 83 18 13c-8 4-17 8-25 14-8 7-15 17-19 27-1 3-1 6-1 8-1 7-1 14 1 19 1 2 1 3 2 5l103 244 1-1 3 6c0 2 1 4 2 5l-1 2 3 7h1 1c1-1 2-4 3-6 3-5 4-11 6-16l9-21c2-4 4-8 5-12l29-67c1-2 3-7 3-9-2-5-4-11-4-16h0v-4h1c-1-3 0-6 0-8h0c0-5 3-11 5-15-2 0-3 1-5 1v-1h1c-1-1-1-2-3-3v-1h0c1-1 1-1 2-1h0c1 0 1 0 1-1 3 0 7-2 10-3 0-2-1-4-2-5v-1h0l1-1c8-2 15-6 20-13l1-1 1 1 1-1v1l4-10c1-2 2-3 3-5h1c1 0 1-2 2-2l4-10c-1 0-1 0-2 1v-1l10-26-1-1c2-4 2-10 2-14 0-9-2-19-7-26-3-3-6-6-9-10z" class="R"></path><path d="M377 497c1-2 1-5 1-7v-1l2 1c-1 3-1 6-2 9v1-1h-1v-2z" class="X"></path><path d="M178 202l3 9-2-1c-1-2-2-3-2-5 0-1 0-1-1-2l2-1z" class="V"></path><path d="M399 463h1c1-1 2-4 3-6l1 1-4 10-2-5h1z" class="T"></path><path d="M391 443l3 6c0 2 1 4 2 5l-1 2-5-12 1-1zM183 175c1 2 2 5 3 6 1 2 1 4 1 6l-5-9c0-1 1-2 1-3z" class="C"></path><path d="M309 508c3 3 4 8 6 12-1 0-2 0-2-1-1 0-2-2-2-4s-2-5-3-7h1z" class="D"></path><path d="M103 143l1-3c1-3 0-5 0-8h1c1 0 2 1 2 2 1 1 1 2 2 2l2 1c-2 0-3 1-5 3 0 0-1 1-1 2h1l-3 1z" class="H"></path><path d="M298 402h1c2 1 5 1 7 1-1 0-1 1-2 1h0v1c1 0 2 1 2 1v1h0 2c-3 1-5 1-8 1-1-1-1-1-2-1-1-1-1-3-1-5h1z" class="F"></path><path d="M353 570c1-1 2-4 3-6 0 1 1 2 2 2 1 1 1 2 2 3l-3 8c-2-2-3-5-4-7z" class="J"></path><path d="M492 245h1c0 2-1 4-2 6l-12 28c-1 0-1 0-1-1l6-17 1-1 4-10c1-2 2-3 3-5z" class="G"></path><path d="M492 245h1c0 2-1 4-2 6v-2-1c0 1 0 1-1 2h-1c1-2 2-3 3-5z" class="N"></path><path d="M485 142c9 8 21 17 25 29 1 3 2 6 2 9 1 9-1 19-5 27l-1-1c2-4 2-10 2-14 0-9-2-19-7-26-3-3-6-6-9-10 2 1 4 3 6 5 1 1 3 4 4 5l2 4v1c1 0 1 1 1 1 0 1 1 1 1 2h0v2l1 1v2c0 1 1 2 1 3v2c1 0 0 0 0 1 1 1 1 4 1 5v5c-1 0 0 3 0 4 0-1 0-1 1-2v-2h0v-3c1-1 1-1 1-2v-2-5-3-3l-1-1v-1-1-2h-1v-2h0l-1-1v-2h-1v-1l-1-2c-1-1-1-1-1-2-1 0 0 0-1-1l-1-1-3-3c-1-2-2-3-3-4-2-1-4-3-5-4-2-2-6-5-7-7z" class="F"></path><path d="M485 142l-11-11c3 2 7 5 10 8 7 6 15 13 21 20 3 3 5 7 6 10 7 18-1 37-8 53l-4 11c-1 0-1 0-2 1v-1l10-26c4-8 6-18 5-27 0-3-1-6-2-9-4-12-16-21-25-29zm-52 379l1 1c-3 5-5 10-7 16 0 0-2 3-2 4-1 2-2 3-2 6h0c-3 4-5 8-6 12-1 2-2 4-2 6v9 1h1c0 3 1 5 2 8v2c0-1-1-2-1-3-2-4-3-8-3-13v-2l-7 16 3 4c-1 0-3-2-4-3-2 6-5 12-8 19l-13 31c-2 6-6 12-8 19 0 0 0 1-1 1 0 2-1 5-3 6-1-1-3-7-4-10l-12-28c0-1-1-2-1-3-2-4-4-8-5-12l1-1 21 51c2-1 3-6 4-8l13-31 14-32 5-13 7-15 17-38zm25-205c1-1 1-3 2-5 0-2 1-4 1-7 2-5 5-9 9-13 1-1 3-4 4-4s1 0 1 1l-71 170-1-1c3-5 4-11 6-16l9-21c2-4 4-8 5-12l29-67c1-2 3-7 3-9-2-5-4-11-4-16h0v-4h1v4c1 1 1 1 1 2l1 2c0 2 1 6 2 8v-1-1-1c1-2 2-6 2-9h0z" class="G"></path><defs><linearGradient id="AA" x1="467.502" y1="307.249" x2="461.24" y2="305.776" xlink:href="#B"><stop offset="0" stop-color="#969798"></stop><stop offset="1" stop-color="#b4b3b3"></stop></linearGradient></defs><path fill="url(#AA)" d="M458 316c1-1 1-3 2-5 0-2 1-4 1-7 2-5 5-9 9-13 1-1 3-4 4-4l-18 44c-1-5-3-10-4-15 1 1 1 1 1 2l1 2c0 2 1 6 2 8v-1-1-1c1-2 2-6 2-9h0z"></path><path d="M308 512l3 3c0 2 1 4 2 4 0 1 1 1 2 1l24 56v2l1 4c-1 3-3 6-6 7-2 2-3 3-5 4h-9c1 1 3 1 4 1h-2s1 0 2 1c-4 0-6-1-10-2l-8-6 6 1h6 0l2 1h0 2 2c1-1 2-2 2-3l1-1c1-3 2-6 3-8v-2c-1-4-1-8-3-11-1-4-3-9-8-11 0-3-4-3-4-5h9c-1 0-1 0-2-1h2 1c-1-4-3-7-5-10-2-6-4-11-8-15-1-3-3-4-5-6 2 0 3 1 4 3v-1s0-1-1-2v-1c-1-1-2-1-2-3z" class="O"></path><path d="M319 553c0-3-4-3-4-5h9c1 1 2 2 3 4 1 3 3 6 4 10v3c0 4 0 8-1 12v-2c-1-4-1-8-3-11-1-4-3-9-8-11z" class="I"></path><path d="M326 586c6-5 6-14 6-21l5 9 2 4 1 4c-1 3-3 6-6 7-2 2-3 3-5 4h-9c1 1 3 1 4 1h-2s1 0 2 1c-4 0-6-1-10-2l-8-6 6 1h6 0l2 1h0 2 2c1-1 2-2 2-3z" class="F"></path><path d="M314 593l-8-6 6 1h6 0l2 1h0-10c2 1 4 3 6 3 1 1 2 1 4 1 1 1 3 1 4 1h-2s1 0 2 1c-4 0-6-1-10-2z" class="a"></path><path d="M337 574l2 4 1 4c-1 3-3 6-6 7 0 0 0-1 1-2 2-4 2-7 2-13z" class="I"></path><defs><linearGradient id="AB" x1="325.341" y1="531.074" x2="287.205" y2="526.595" xlink:href="#B"><stop offset="0" stop-color="#565656"></stop><stop offset="1" stop-color="#9f9e9e"></stop></linearGradient></defs><path fill="url(#AB)" d="M280 511c2-1 4-1 6-1 2 1 4 1 6 0 1 0 1 1 2 1v-1h1s1 0 1 1h1c2 0 6 4 7 3 1 1 2 1 3 2 2 2 4 3 5 6 4 4 6 9 8 15 2 3 4 6 5 10h-1-2c1 1 1 1 2 1h-9c0 2 4 2 4 5l-4-2c-3-2-6-3-9-5-5-3-10-6-14-11l-1-1v-1c1 1 2 3 3 3h1l2 2c2-3 4-7 3-10v-1c-1-4-2-9-5-12-5-5-9-4-15-4z"></path><defs><linearGradient id="AC" x1="474.112" y1="300.586" x2="447.856" y2="295.909" xlink:href="#B"><stop offset="0" stop-color="#a8a7a7"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#AC)" d="M482 260l1-1 1 1 1-1v1l-1 1-6 17c0 1 0 1 1 1l-3 9h-1c0-1 0-1-1-1s-3 3-4 4c-4 4-7 8-9 13 0 3-1 5-1 7-1 2-1 4-2 5h0c0 3-1 7-2 9v1 1 1c-1-2-2-6-2-8l-1-2c0-1 0-1-1-2v-4c-1-3 0-6 0-8h0c0-5 3-11 5-15-2 0-3 1-5 1v-1h1c-1-1-1-2-3-3v-1h0c1-1 1-1 2-1h0c1 0 1 0 1-1 3 0 7-2 10-3 0-2-1-4-2-5v-1h0l1-1c8-2 15-6 20-13z"></path><path d="M458 316c1-9 3-18 9-25 3-2 5-4 7-6s3-5 4-7c0 1 0 1 1 1l-3 9h-1c0-1 0-1-1-1s-3 3-4 4c-4 4-7 8-9 13 0 3-1 5-1 7-1 2-1 4-2 5z" class="N"></path><path d="M457 289l2-1c1 2 1 7 0 9v2l-4 10c-1 1-1 0-1 1 0 0 0 1-1 1h0c0-1 0-2-1-2v-5c0-5 3-11 5-15z" class="Y"></path><defs><linearGradient id="AD" x1="484.886" y1="266.488" x2="460.703" y2="270.785" xlink:href="#B"><stop offset="0" stop-color="#a1a0a0"></stop><stop offset="1" stop-color="#fbfbfa"></stop></linearGradient></defs><path fill="url(#AD)" d="M482 260l1-1 1 1 1-1v1l-1 1c-1 1-2 3-3 4l-6 6c-1 1-2 3-3 4-3 2-7 3-9 5 0-2-1-4-2-5v-1h0l1-1c8-2 15-6 20-13z"></path><path d="M292 535c4 5 9 8 14 11 3 2 6 3 9 5l4 2c5 2 7 7 8 11 2 3 2 7 3 11v2c-1 2-2 5-3 8l-1 1c0 1-1 2-2 3h-2-2 0c1 0 1-1 2-1 1-1 1-3 0-4 0-3-1-5-3-7-3-2-6-2-8-3l-8-4h3l-4-1c0-1-1-3-2-3-1-1-2-1-3-2l-2-1c-1-1-2-1-3-2l1-1c-4 0-8 0-12-1v-1-1h0c2-2 3-2 5-3l-9-3h1c-1 0-1 0-1-1 1 0 1-1 2-1v-1c1-1 4-3 6-4h2c1 0 3-1 5-1v-4-4z" class="Y"></path><path d="M278 551c2 0 4 1 5 1 2 1 5 3 7 4l3 3c-1-1 0-1-1-1h-2c-3-1-6-2-9-1h0c2-2 3-2 5-3l-9-3h1z" class="V"></path><path d="M281 557c3-1 6 0 9 1h2c1 0 0 0 1 1h1c-1 1 0 1-1 1-4 0-8 0-12-1v-1-1z" class="R"></path><path d="M292 543l1 4 1 2h-1c-3-2-6-3-10-2l-4 1c1-1 4-3 6-4h2c1 0 3-1 5-1z" class="S"></path><path d="M292 543l1 4c-2-1-5-1-6-3 1 0 3-1 5-1z" class="J"></path><path d="M293 559c5 1 8 4 12 7 3 1 6 3 7 6l-6-2-4-1c0-1-1-3-2-3-1-1-2-1-3-2l-2-1c-1-1-2-1-3-2l1-1c1 0 0 0 1-1h-1z" class="T"></path><path d="M319 577v-1c1-3-2-7-4-9-2-4-5-5-8-8h1c1 1 3 2 5 3 4 3 8 7 11 12 0 2 2 5 2 8v2s1 0 1 1l-1 1c0 1-1 2-2 3h-2-2 0c1 0 1-1 2-1 1-1 1-3 0-4 0-3-1-5-3-7z" class="H"></path><path d="M315 551l4 2c5 2 7 7 8 11 2 3 2 7 3 11v2c-1 2-2 5-3 8 0-1-1-1-1-1v-2c0-3-2-6-2-8h0c1 1 2 4 2 6h0 1c-1-2 0-4-1-6h-1v-1-1-1-1c-1-1-1-2-2-3 0-2-1-3-2-4 0-1 0-2-1-2v-2h1 1l-1-1v-1c-1 0-1-1-2-2h-1c1 0 1 0 1-1h0v-1h-1c-1 0-2-1-3-2z" class="B"></path><defs><linearGradient id="AE" x1="347.888" y1="615.249" x2="338.066" y2="623.64" xlink:href="#B"><stop offset="0" stop-color="#676667"></stop><stop offset="1" stop-color="#919090"></stop></linearGradient></defs><path fill="url(#AE)" d="M339 576l6 14 6 13c0 1 1 3 1 4l-1 1c1 4 3 8 5 12 0 1 1 2 1 3l12 28-1-1c-1-1-2-3-2-4v-1h-3l-1-1s-1 0-2-1h-2c-1-1-3-3-4-3l-2-2c-2-1-3-2-4-3-3-3-5-5-7-8l-1-1h0c0-1-1-1-1-2l-1-1-1-1v-2c-1 0-1 0-2-1 0-1 0-1-1-2l-3-6-1-1c-1-1-1-1-1-2h0l-1-2h-1c0-1 0-1-1-2v-1l-3-4h-1v-1c0-1-2-2-3-3h-1-1c-2 0-2-1-3-2 4 1 6 2 10 2-1-1-2-1-2-1h2c-1 0-3 0-4-1h9c2-1 3-2 5-4 3-1 5-4 6-7l-1-4v-2z"></path><defs><linearGradient id="AF" x1="355.215" y1="599.819" x2="340.967" y2="611.727" xlink:href="#B"><stop offset="0" stop-color="#3a393a"></stop><stop offset="1" stop-color="#686868"></stop></linearGradient></defs><path fill="url(#AF)" d="M339 576l6 14 6 13c0 1 1 3 1 4l-1 1c1 4 3 8 5 12 0 1 1 2 1 3 0 2 1 2 1 4-2 0-5-2-6-3s-2-2-3-2c-1-1-1-2-2-3s-4-3-4-5c0-1-2-4-3-4h-1c0-1-1-2-1-2v-2h-1c0-1-1-2-2-3-1-3-3-7-5-9-1 0-1 0-1-1 2-1 3-2 5-4 3-1 5-4 6-7l-1-4v-2z"></path><path d="M335 366c1-11 0-21-3-32 2 2 3 6 4 9l9 20 27 65 18 42c2 4 4 8 5 12l-16 32c5-17 6-38 1-54-2-8-6-16-10-23-7-11-18-20-21-32-2-5-1-10-1-14-1-3-1-5-2-7 0-1 0-1-1-2l-5-8h-1v1l-1-3s0-1-1-1h0v3l-2-2c0-1 0-1 1-2 0-1 0-2-1-3v-1z" class="G"></path><defs><linearGradient id="AG" x1="354.609" y1="390.893" x2="315.027" y2="432.058" xlink:href="#B"><stop offset="0" stop-color="#3a3a3a"></stop><stop offset="1" stop-color="#595858"></stop></linearGradient></defs><path fill="url(#AG)" d="M310 407c3-1 6-3 9-5 11-9 14-23 16-36v1c1 1 1 2 1 3-1 1-1 1-1 2l2 2v-3h0c1 0 1 1 1 1l1 3v-1h1l5 8c1 1 1 1 1 2-1 1-1 3-1 5 0 4-1 9 0 13 0 3 2 7 4 10 3 7 8 12 12 19 1 1 3 3 3 5-4-1-9-5-13-8-1 0-1 1-2 0 0-1 0 0-1-1h0-1c0 1 0 2 1 3 1 2 2 4 2 6h2-7c-7 0-16 1-22 5v-1c-1-1 0-1 0-2v-2-1c0-1 0-1-1-2v-1h0v-1-1l-1-1c1-2 2-5 2-7l-1-1 1-1h0v-5l-1-1h-1-1-2l-1 1h-1c-1 1-2 0-4 1l-1 1c0-1 1-1 1-2 1 0 2 0 2-1l2-1c0-1 1-1 2-2v-1c-1-1-1-1-1-2v-2l-1-1h-1c-1 1-1 1-2 1l-1 1h0-2z"></path><path d="M325 425l1 1s1 1 1 2v2 1h0c-1-2-2-4-2-6z" class="E"></path><path d="M324 422c0-1 1-1 1-1v-1c0-2 0-6 1-8v7 6h0v1l-1-1-1-3z" class="U"></path><path d="M327 428h1c1 2 2 4 4 6v1c-2-1-2-2-3-2l-2-3v-2zm-5-17l2-1c0 1 0 2-1 3-1 0-2 0-3 1h-2l-1 1h-1c-1 1-2 0-4 1l-1 1c0-1 1-1 1-2 1 0 2 0 2-1l2-1c0-1 1-1 2-2h2l-3 1 1 1c1-1 3-1 4-2z" class="P"></path><path d="M322 411v-1c1 0 2-1 2-1 1-1 2-4 3-6l-1 9c-1 2-1 6-1 8v1s-1 0-1 1c-1-3 0-6-1-9l-1 1h-1-1c1-1 2-1 3-1 1-1 1-2 1-3l-2 1z" class="M"></path><defs><linearGradient id="AH" x1="353.4" y1="400.602" x2="334.556" y2="410.981" xlink:href="#B"><stop offset="0" stop-color="#090909"></stop><stop offset="1" stop-color="#444445"></stop></linearGradient></defs><path fill="url(#AH)" d="M338 372l1 3v-1h1l5 8c1 1 1 1 1 2-1 1-1 3-1 5 0 4-1 9 0 13 0 3 2 7 4 10 3 7 8 12 12 19 1 1 3 3 3 5-4-1-9-5-13-8-5-4-12-4-16-11-1-2-1-3-1-5 1 0 1-1 2-1s3 1 4 1h-3c1 1 1 1 2 1s1 0 1 1c1 0 2 0 3-1-1-1-1 0-1-1h2v-1l-1-1c0-1-1-2-2-4v-2l-1-3-3-11h0c1 0 1 1 2 1 0 1 1 1 1 2v1l-1-2h-1 0l1 3h2v-1l-1-1h1c-1-2 0-4 0-6-1-5-3-10-3-14v-1z"></path><path d="M339 374h1l5 8c1 1 1 1 1 2-1 1-1 3-1 5-1-1-1-3-1-4s0-2-1-3c-1-3-3-5-4-8z" class="Q"></path><path d="M351 428c-5-4-12-4-16-11-1-2-1-3-1-5 1 0 1-1 2-1s3 1 4 1h-3c-1 1-1 0-2 1 1 3 3 6 6 8s7 4 10 6c3 1 6 3 9 4h1c1 1 3 3 3 5-4-1-9-5-13-8z" class="I"></path><path d="M323 522s1 2 1 3l6 14 18 42 20 46 6 14 199-458c4-7 8-14 14-20 14-15 34-25 54-31-5 4-12 6-18 8-3 2-7 4-10 6-11 6-20 13-28 22-7 9-12 19-16 29l-12 25-19 44-76 174-34 79-38 88-11 25c-2 4-3 8-5 12L173 185c-2-5-6-10-9-15-2-2-4-5-6-7-7-8-16-14-26-19-3-1-6-3-9-4l-12-3-2-1c-1 0-1-1-2-2 18 3 37 13 50 26 4 4 8 9 11 13 7 11 12 22 17 33l16 36 31 69 7 17 26 60 8 19 7 15c0 2 1 4 2 5l10 24 19 44 8 19c1 2 3 5 4 8zM182 178c-6-11-13-20-22-28s-19-14-31-17h171c-11 6-20 16-24 28-2 9-2 17-2 25 0 0 0-1-1-1 0-2 1-5 1-7l-1-8c-1 2-2 5-4 6h0c-2 1-4 4-5 6h-1l1-1c0-2 0-2 1-4v-1-1l-2 2c0 1 0 1-1 1v1h-1 0c0-4 1-8 2-11 1-4 1-8 0-12-3-7-9-11-15-14-3-1-6-2-10-2-8-2-17-2-25-1l-6 1c-2 0-3 0-5 1-5 2-10 5-12 10-3 3-4 7-5 12l1 3c-1 5-1 10 0 15-1-1-2-4-3-6 0 1-1 2-1 3z" class="G"></path><path d="M185 138c-1 2-1 4-2 5v2c-1-1-1-3-1-4h0l1-1-1-1h1l1 1v-2h1z" class="N"></path><path d="M276 148h0 0c-2 3-4 5-7 7h-1c1-3 5-5 8-7h0zm-91-10c0-1 0-2 1-3h0c0 2 2 6 0 8v1h-2l-1-1c1-1 1-3 2-5z" class="T"></path><path d="M170 146l-1-2c-1-2-3-5-4-7l4 2 1-1c1 0 2 1 3 1-1 2-2 3-3 5v2z" class="C"></path><path d="M260 135l1-1c1 0 2 1 3 2s1 2 0 3c0 1 0 1-2 2-1 0-2 0-3-1v-1h-1c1 0 1 0 2-1v-3z" class="L"></path><path d="M265 175c1-2 6-6 9-7-2 3-4 5-5 8-2 1-4 4-5 6h-1l1-1c0-2 0-2 1-4v-1-1z" class="E"></path><path d="M186 144h-1c-1 3-3 5-6 5-1 0-2-1-2-2v-3c1-2 3-3 5-3 0 1 0 3 1 4v-2l1 1h2z" class="C"></path><path d="M183 143l1 1c-1 2-2 4-5 4 0 0-1 0-1-1v-1h1c1 1 1 1 3 1 0-1 0-1 1-2v-2z" class="D"></path><path d="M266 154c3 6 1 12-2 19 0 1-1 3-1 4s0 1-1 1v1h-1 0c0-4 1-8 2-11h1c1-1 1-3 1-5 1-3 1-6 1-9z" class="J"></path><path d="M173 139l4-4c0 1-1 2-1 3-2 2-3 4-2 7v2c1 4 1 9 1 14-2-5-4-10-5-15v-2c1-2 2-3 3-5z" class="D"></path><path d="M251 139c6 2 12 7 14 13l1 2c0 3 0 6-1 9 0 2 0 4-1 5h-1c1-4 1-8 0-12-3-7-9-11-15-14h0l3 1 1-1h0c-1 0-1-1-2-1h-2c-1-1 0-1-1-1h-1v-1c1 0 1 0 2 1h1 2v1h1c1 0 1 0 2 1l-1-1s-1-1-2-1h0v-1z" class="H"></path><path d="M251 139c6 2 12 7 14 13l1 2c0 3 0 6-1 9 0-3 1-8 0-11-3-5-8-8-13-10h0c-1 0-1-1-2-1h-2c-1-1 0-1-1-1h-1v-1c1 0 1 0 2 1h1 2v1h1c1 0 1 0 2 1l-1-1s-1-1-2-1h0v-1z" class="I"></path><path d="M210 135c10-1 22-2 32 1 3 0 6 2 9 3h0v1h0c1 0 2 1 2 1l1 1c-1-1-1-1-2-1h-1v-1h-2-1c-1-1-1-1-2-1v1h1c1 0 0 0 1 1h2c1 0 1 1 2 1h0l-1 1-3-1h0c-3-1-6-2-10-2-8-2-17-2-25-1l-6 1c-2 0-3 0-5 1-5 2-10 5-12 10-3 3-4 7-5 12l1 3c-1 5-1 10 0 15-1-1-2-4-3-6-2-4-2-11-1-15s2-8 5-11c3-6 8-9 13-12l1-1c3-1 6-2 9-1z" class="W"></path><path d="M200 137l1-1c3-1 6-2 9-1-1 0-2 1-2 1-1 0-2 0-3 1-1 0-1 1-2 1 0 0-1 0-2 1-1-1 0-1 0-1 0-1-1-1-1-1z" class="M"></path><path d="M183 175c-2-4-2-11-1-15 0 1 1 3 2 4v1 3 1 1c1 0 1 0 1-1v-1c0-1-1-3 0-3v-2l1 3c-1 5-1 10 0 15-1-1-2-4-3-6z" class="E"></path><defs><linearGradient id="AI" x1="299.679" y1="231.325" x2="271.216" y2="243.525" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#373637"></stop></linearGradient></defs><path fill="url(#AI)" d="M269 176h0c2-1 3-4 4-6l1 8c0 2-1 5-1 7 1 0 1 1 1 1l3 15c3 9 7 17 10 26l17 39 12 29 8 23c5 14 9 27 8 42v7c-1-1-1-2-1-3 1-1 0-2 0-3 0-2 1-4 1-5 0-2-1-4-1-5l1-1c-1-1-1-1-1-2v-2c0-2-1-2-1-3l1-1c-1 0-1 0-1-1v-1h0v-2c-1 0-1-1-1-2h0v-1c0-1 0-1-1-2h0v-1h-1c1-1 1-2 0-2v-1-1l-1-1c0-1 0-2-1-3v-1-2c-1 0-1-1-1-1v-1c0-1-1-2-1-3l-1-1v-2c0-1 0-1-1-2v-1c0-1-1-2-1-3l-1-3c-1-1-1-2-2-4h0s0 1-1 2-4 2-5 3v1l3 5s1 1 1 2h0c-3-2-4-6-6-9h0l1-1-2-2-1-1h-3-3-2c1 1 2 1 2 2-1 1-1 1-2 1h0-4l-5-1s-2 0-3-1c-2 0-5-1-7-3-10-4-17-12-24-20-1-1-1-2-2-3l-1-1c-1 0-3-2-3-3l-8-6-4-2-1-2 2-1h0l1-2c1-1 2-3 3-5l3-1h2 2c1 1 1 1 2 1h1c-1-3-1-6-2-10-2-14 0-29 4-44 0-1 1-3 2-4v-1c0-1-1-1 0-2h0c0-2 1-4 2-6v-1h0v-1-1h0l2-4h1v-1c1 0 1 0 1-1l2-2v1 1c-1 2-1 2-1 4l-1 1h1c1-2 3-5 5-6z"></path><path d="M291 258h1c1-1-1-4-1-5 2 3 4 5 4 8l-2-2c-1 0-1-1-2-1z" class="L"></path><path d="M310 299c2 2 2 3 2 4-1 1-2 1-3 1l1-1-2-2-1-1h0l3-1z" class="E"></path><path d="M282 246h0l1-1h0 0c1 1 2 2 2 4 1 1 1 1 1 2l-2 1-2-6z" class="Q"></path><path d="M276 222c1-1 2-1 3-2 1 1 2 2 2 3v1s-1 0-1 1c-1-2-2-1-3-1v-1l-1-1z" class="Z"></path><path d="M277 232h2 3 1c-1 1-2 1-2 2-2 0-4 1-5 2l-1 1v-1c0-1 1-1 2-2l-4-1h3l1-1z" class="a"></path><path d="M310 299c2 0 3 0 5-1 1 1 1 1 1 2 0 2-3 2-4 3 0-1 0-2-2-4z" class="C"></path><path d="M275 237l1-1v2h0c0 1-1 3 0 3l1-1c1-1 1-1 2-1-2 2-3 3-4 6v2h0c-1-1-1-3-1-4v-3l-1-1 2-2z" class="T"></path><path d="M296 262c0 2 0 6 1 7v1l2 2h-1 0l1 3c-1 1-1 2-1 3-1-2-1-4-2-6v-2h-4l3-1v-2c1-1 1-3 1-5zm-37-66c1-1 2-1 3-1h2l-1-1v-1-1-1h2l1-1c0 1 1 1 1 1 1 1 0 2 0 3-1 1-1 1-2 1l-1 1h-5z" class="C"></path><path d="M279 239l5-3c-1 1-2 2-4 3-1 3-4 6-4 9 1 0 1 1 1 1h0c-1 0-2-1-2-2h0v-2c1-3 2-4 4-6z" class="U"></path><path d="M262 230c3-1 5 0 8 1l7 1-1 1h-3l-9-1c0-1-1-1-1-1l-1-1z" class="J"></path><path d="M264 182h1c1-1 2-2 4-2h1c-1 1-1 0-2 1s-3 3-4 5c-1 1-2 1-2 2-2 1-2 2-3 3l4-9h1z" class="L"></path><path d="M264 232l9 1 4 1c-1 1-2 1-2 2l-2 1c-1 0-1-1-2-2l-3-1h-2l-2-2h0z" class="E"></path><path d="M265 175v1 1c-1 2-1 2-1 4l-1 1-4 9c-1 1-1 2-2 3 0-1-1-1 0-2h0c0-2 1-4 2-6v-1h0v-1-1h0l2-4h1v-1c1 0 1 0 1-1l2-2z" class="I"></path><path d="M284 255h2 1c-3 3-6 5-6 10 0 3 1 6 3 8l3 3c-2 0-4-3-6-4l-1-1c-2-3-2-6-1-9s2-5 5-7z" class="X"></path><path d="M283 252v1l-3 3v1c0 1-1 1-1 2h0l1-1h1v-1c1-1 1-1 3-2-3 2-4 4-5 7s-1 6 1 9l1 1-1 1-4-3c-1-1-2-3-2-5 0 0 1 1 2 1 0-6 2-10 7-14z" class="L"></path><defs><linearGradient id="AJ" x1="271.661" y1="200.694" x2="263.098" y2="206.176" xlink:href="#B"><stop offset="0" stop-color="#6f6f70"></stop><stop offset="1" stop-color="#949395"></stop></linearGradient></defs><path fill="url(#AJ)" d="M259 196h5c4 1 7 2 10 6 2 3 2 8 1 12l-1 2-1 2v-2c0-1 0-1-1-2l1-2v-1c0-3 0-5-2-7-2-4-6-5-10-5-1 0-3 0-4-1 0-1 0-1 1-2h1z"></path><path d="M273 212c1 0 0 0 1 1v3l-1 2v-2c0-1 0-1-1-2l1-2z" class="W"></path><defs><linearGradient id="AK" x1="293.055" y1="263.431" x2="284.603" y2="263.494" xlink:href="#B"><stop offset="0" stop-color="#343334"></stop><stop offset="1" stop-color="#525152"></stop></linearGradient></defs><path fill="url(#AK)" d="M285 268c-1-1-1-3-1-4 0-2 1-3 2-5 1-1 3-1 5-1h0c1 0 1 1 2 1l2 2s0 1 1 1c0 2 0 4-1 5v2l-3 1h-3c-2 0-3-1-4-2z"></path><path d="M285 268l1-1c2 0 3 3 6 2l1-1 2 1-3 1h-3c-2 0-3-1-4-2z" class="K"></path><path d="M291 259c1 1 2 2 3 4v1h0-1c-1 0-2-1-2-2-1 0 0 0-1-1h-2v-1c1 0 1-1 3-1z" class="L"></path><path d="M291 258h0c1 0 1 1 2 1l2 2s0 1 1 1c0 2 0 4-1 5v2l-2-1c1-1 1-3 1-4h0v-1c-1-2-2-3-3-4v-1z" class="D"></path><path d="M294 264l1-1v4 2l-2-1c1-1 1-3 1-4h0zm-10 9c2-1 4-1 6-1h1c0-1 1-1 2-1 1 1 1 1 3 1 1 2 1 4 2 6v1 2c1 1 1 2 1 2 1 1 1 2 1 4 0 1 1 2 1 3v-4h1l-1 1c0 1 2 2 2 2l7 1c-1 0-2 1-3 2-2-2-5-1-7-1l-1-1v-2h-1v-1c0-2 0-2-1-3h0l-1-1-1-1h-2v-2c-1 1-2 1-2 1h-2c-1-1-1-1-1-2l1-1c2 1 3 1 5 1l2 1h0l-1-1c-2-1-3-1-5-2-1 0-2-1-3-1l-3-3z" class="T"></path><path d="M294 279l2 1v1h-1-1 0l-1-1 1-1z" class="D"></path><path d="M276 270l4 3 1-1c2 1 4 4 6 4 1 0 2 1 3 1 2 1 3 1 5 2l1 1h0l-2-1c-2 0-3 0-5-1l-1 1c0 1 0 1 1 2h2s1 0 2-1v2h2l1 1 1 1h0c1 1 1 1 1 3v1h1v2l1 1c2 0 5-1 7 1l-2 1h0l-1-1c-1-1-2-1-4-1l1 1v1c-6 0-10-2-14-4-1-1-2-1-2-2-2-2-3-6-3-9h2l-1-1c-2-1-3-3-4-3-1-1-1-1-2-1l-1-2v-1z" class="V"></path><path d="M293 280v2h2l1 1 1 1h0v3c-1 1-3 1-4 1s-1-1-2-1h-1c0-1-1-2-1-2 0-2 1-3 2-4 0 0 1 0 2-1z" class="a"></path><path d="M291 281h0v1 3l1 1v1h-1-1c0-1-1-2-1-2 0-2 1-3 2-4z" class="W"></path><path d="M293 282h2l1 1 1 1-3 3-1-1v-4z" class="V"></path><path d="M287 289c-1-1-2-1-2-2-2-2-3-6-3-9h2c0 2 1 4 2 6 1 1 1 2 3 3 2 2 7 4 10 4h1l1 1v1c-6 0-10-2-14-4z" class="M"></path><defs><linearGradient id="AL" x1="282.829" y1="248.65" x2="269.573" y2="251.025" xlink:href="#B"><stop offset="0" stop-color="#39393a"></stop><stop offset="1" stop-color="#5e5d5d"></stop></linearGradient></defs><path fill="url(#AL)" d="M273 239l1 1v3c0 1 0 3 1 4h0 0c0 1 1 2 2 2h0s0-1-1-1c0-3 3-6 4-9 1 3 1 5 2 7h0l2 6h-1c-5 4-7 8-7 14-1 0-2-1-2-1 0 2 1 4 2 5v1l-2-2h0l-5-9h2l-1-1h0l3 3c0-2-1-4-3-6l-1-4c-1-5 1-9 4-13z"></path><defs><linearGradient id="AM" x1="265.658" y1="223.787" x2="264.75" y2="244.219" xlink:href="#B"><stop offset="0" stop-color="#403f3f"></stop><stop offset="1" stop-color="#777677"></stop></linearGradient></defs><path fill="url(#AM)" d="M276 222l1 1v1c1 0 2-1 3 1-2 1-4 1-6 2l-5 1h0v1s1 1 1 2c-3-1-5-2-8-1l1 1s1 0 1 1h0l2 2h2l3 1c1 1 1 2 2 2l-2 2c-1 0-1 0-3 1h0c-4 3-6 4-11 4 0 1-1 1-1 1 0 1 0 1-1 2v-1c-1-2-4-15-2-17v-3-1l1 2v1c5-1 9-2 13-4v1l6-1h0l3-2z"></path><path d="M263 235s1 0 1-1l1 1h0c0 1 0 1-1 2v1h-1v-3z" class="K"></path><path d="M276 222l1 1v1c1 0 2-1 3 1-2 1-4 1-6 2l1-1c1-1 2-1 3-1-1-1-2 0-3 0 0 0-1 0-2-1h0 0l3-2z" class="L"></path><path d="M262 235c0-1-1-3 0-5l1 1s1 0 1 1h0l2 2v1h-1l-1-1c0 1-1 1-1 1l-2 2 1-2z" class="W"></path><path d="M260 238h-1c0 1 0 1-1 1h-1c-1-2-1-3-1-4s0-1 1-2c2 0 2 0 3 1l2 1-1 2-1 1z" class="M"></path><path d="M260 234l2 1-1 2-1 1c-1-1-1-2-1-4h1z" class="J"></path><defs><linearGradient id="AN" x1="291.862" y1="272.676" x2="251.756" y2="257.561" xlink:href="#B"><stop offset="0" stop-color="#5b5b5b"></stop><stop offset="1" stop-color="#7d7b7c"></stop></linearGradient></defs><path fill="url(#AN)" d="M275 236v1l-2 2c-3 4-5 8-4 13l1 4c2 2 3 4 3 6l-3-3h0l1 1h-2l5 9h0l2 2 1 2c1 0 1 0 2 1 1 0 2 2 4 3l1 1h-2c0 3 1 7 3 9 0 1 1 1 2 2l-1 1h-1 0c-1 0-2 0-3-1-4-1-8-4-12-7-1-1-2-2-3-4-3-2-3-5-5-7-1-1-2-2-2-4 0-1 0-2-1-3-1-4-1-7-3-11 0-2-1-4-1-6 1-1 1-1 1-2 0 0 1 0 1-1 5 0 7-1 11-4h0c2-1 2-1 3-1l2-2 2-1z"></path><path d="M270 271c1 1 1 1 1 2l-1 1-2-1v-2-1l1 1h1z" class="S"></path><path d="M261 251h1c1 1 2 1 2 2v1h0l-2 2c-1 0-1 0-2-1s-1-1-1-3c0 0 1-1 2-1z" class="M"></path><path d="M268 240c0 2 0 3-1 4-3 1-7 2-9 2l-1-1v-1c5 0 7-1 11-4z" class="H"></path><path d="M275 236v1l-2 2c-3 4-5 8-4 13l1 4h-1c-2-4-2-7-1-11 0-2 2-4 3-6l2-2 2-1z" class="I"></path><path d="M268 273l2 1c2 2 4 3 7 4l2 2 2 6-6-6c-2-1-4-2-6-4 0-1-1-2-1-3z" class="M"></path><path d="M275 280l-2-2c1 0 3 2 4 2v-2l2 2 2 6-6-6z" class="U"></path><path d="M266 275v-2-1c-1-1 0-4 0-5-1-1-1-2-2-2v-1-1c-1-1-1-4 0-5h1c0 1 0 2 1 2 0 1 0 1 1 2v3c1 2 1 3 2 5l1 1h-1l-1-1v1l-1 1-1 3z" class="M"></path><path d="M266 267l-1-3c0-1-1-2 0-3 1 1 1 2 2 4v3l-1-1z" class="W"></path><path d="M266 267l1 1c1 0 1 1 2 2l1 1h-1l-1-1v1l-1 1c0-2 0-3-1-5z" class="J"></path><defs><linearGradient id="AO" x1="280.156" y1="289.372" x2="273.415" y2="280.3" xlink:href="#B"><stop offset="0" stop-color="#7e7e7e"></stop><stop offset="1" stop-color="#9b999a"></stop></linearGradient></defs><path fill="url(#AO)" d="M268 271v2c0 1 1 2 1 3 2 2 4 3 6 4l6 6c1 1 2 3 4 4h0c-1 0-2 0-3-1-4-1-8-4-12-7-1-1-2-2-3-4l-1-3 1-3 1-1z"></path><path d="M268 271v2c0 1 1 2 1 3 1 2 1 3 3 4h0l1 1-3 1c-1-1-2-2-3-4l-1-3 1-3 1-1z" class="I"></path><defs><linearGradient id="AP" x1="287.98" y1="246.541" x2="253.215" y2="260.244" xlink:href="#B"><stop offset="0" stop-color="#201f20"></stop><stop offset="1" stop-color="#504f4f"></stop></linearGradient></defs><path fill="url(#AP)" d="M255 199c0-1 1-3 2-4 0 0 0 1 1 1-1 1-1 1-1 2 1 1 3 1 4 1 4 0 8 1 10 5 2 2 2 4 2 7v1l-1 2c1 1 1 1 1 2v2c-2 3-4 5-6 6-4 2-8 3-13 4v-1l-1-2v1 3c-2 2 1 15 2 17v1c0 2 1 4 1 6 2 4 2 7 3 11 1 1 1 2 1 3 0 2 1 3 2 4 2 2 2 5 5 7 1 2 2 3 3 4 4 3 8 6 12 7 1 1 2 1 3 1h0 1l1-1c4 2 8 4 14 4v-1l-1-1c2 0 3 0 4 1l1 1h0l-1 1h1l1-1c1 2 2 3 2 6h0-2v1h1 0-3-3-2c1 1 2 1 2 2-1 1-1 1-2 1h0-4l-5-1s-2 0-3-1c-2 0-5-1-7-3-10-4-17-12-24-20-1-1-1-2-2-3l-1-1c-1 0-3-2-3-3l-8-6-4-2-1-2 2-1h0l1-2c1-1 2-3 3-5l3-1h2 2c1 1 1 1 2 1h1c-1-3-1-6-2-10-2-14 0-29 4-44z"></path><path d="M271 204c2 2 2 4 2 7v1l-1 2c-1 1-1 3-3 4h0v-1l1-1h0v-1c3-1 1-2 1-3v-4-2-1-1h0z" class="N"></path><path d="M266 213h1v2c-2 2-3 3-6 3-1 1-2 2-4 2v-2h0l1 1c1 0 1 0 2-1v-1-1h0 1c-1-1-1-1-2-1h0-2c0-1 0-1-1-1 0-1 0 0 1-1v-2-3h1c0 1-1 4 0 5 1 0 2 1 2 1h2 1 2v-1h1z" class="D"></path><path d="M265 213h1v1h-2-1 2v-1z" class="T"></path><path d="M260 214c0-1-1-2-1-2-1-2-1-2 0-3 0-2 1-4 3-5l4 2c0 1 1 1 1 2l-1 2h0c0 1 0 2-1 3v1h-2-1-2z" class="K"></path><path d="M266 206c0 1 1 1 1 2l-1 2c-1 0-2-1-3-1h-3l1-1c1-2 2-2 5-2z" class="C"></path><path d="M260 209h3c1 0 2 1 3 1h0c0 1 0 2-1 3v1h-2-1v-1c-1-1-1-2-2-3h0v-1z" class="E"></path><path d="M260 209h3c1 0 2 1 3 1h0 0c-1 1-2 1-3 1h-1l1-1-1-1-1 1h-1v-1z" class="T"></path><defs><linearGradient id="AQ" x1="264.382" y1="214.732" x2="261.618" y2="227.964" xlink:href="#B"><stop offset="0" stop-color="#a9a8a9"></stop><stop offset="1" stop-color="#e3e2e3"></stop></linearGradient></defs><path fill="url(#AQ)" d="M272 214c1 1 1 1 1 2v2c-2 3-4 5-6 6-4 2-8 3-13 4v-1l-1-2c6-1 13-2 16-7 2-1 2-3 3-4z"></path><path d="M256 253c2 4 2 7 3 11 1 1 1 2 1 3 0 2 1 3 2 4 2 2 2 5 5 7 1 2 2 3 3 4 4 3 8 6 12 7 1 1 2 1 3 1h0 1l1-1c4 2 8 4 14 4v-1l-1-1c2 0 3 0 4 1l1 1-2 1c-8 2-16 0-23-3-3-2-5-3-7-4v-1l2 1h0v-1l-2-1c0-1-4-3-5-3l1 1h-1c-6-6-8-14-10-22-1-2-2-5-2-8z" class="K"></path><path d="M256 273v1h1v-1c7 12 19 22 32 25 1 1 3 1 4 1l6 1c1 1 2 1 2 2-1 1-1 1-2 1h0-4l-5-1s-2 0-3-1c-2 0-5-1-7-3-10-4-17-12-24-20-1-1-1-2-2-3l-1-1v-1l2 1 1-1z" class="F"></path><path d="M256 273v1c0 1 1 2 1 3l-1 1c-1-1-1-2-2-3l-1-1v-1l2 1 1-1z" class="H"></path><path d="M293 299l6 1c1 1 2 1 2 2-1 1-1 1-2 1h0c-2-1-3-1-4-2l-2-2z" class="I"></path><path d="M250 252c1 1 1 1 2 1h1v3c1 2 2 4 2 7 1 3 1 7 2 10v1h-1v-1l-1 1-2-1v1c-1 0-3-2-3-3l-8-6-4-2-1-2 2-1h0l1-2c1-1 2-3 3-5l3-1h2 2z" class="Y"></path><path d="M242 261c1 1 1 2 2 2h1c1 0 2 2 4 3 2 2 5 4 7 7l-1 1-2-1v1c-1 0-3-2-3-3l-8-6v-2-2z" class="J"></path><path d="M250 252c1 1 1 1 2 1h1v3l-6 1c-1 1-2 2-3 4 0 0 1 1 1 2h-1c-1 0-1-1-2-2v2 2l-4-2-1-2 2-1h0l1-2c1-1 2-3 3-5l3-1h2 2z" class="X"></path><path d="M240 258c0 2-1 3 1 5h1v2l-4-2-1-2 2-1h0l1-2z" class="H"></path><path d="M242 261h0c1-3 3-4 5-6v2c-1 1-2 2-3 4 0 0 1 1 1 2h-1c-1 0-1-1-2-2z" class="U"></path><path d="M250 252c1 1 1 1 2 1h1v3l-6 1v-2c1 0 3 0 4-1-1-1-2-1-3-2h2z" class="S"></path><defs><linearGradient id="AR" x1="189.431" y1="188.666" x2="251.699" y2="174.339" xlink:href="#B"><stop offset="0" stop-color="#1e1d1d"></stop><stop offset="1" stop-color="#777676"></stop></linearGradient></defs><path fill="url(#AR)" d="M213 139c8-1 17-1 25 1 4 0 7 1 10 2 6 3 12 7 15 14 1 4 1 8 0 12-1 3-2 7-2 11h0l-2 4h0v1 1h0v1c-1 2-2 4-2 6h0c-1 1 0 1 0 2v1c-1 1-2 3-2 4-4 15-6 30-4 44 1 4 1 7 2 10h-1c-1 0-1 0-2-1h-2-2l-3 1c-1 2-2 4-3 5l-1 2h0c-2-1-4-1-5-2l-10-4c-2-1-5-2-7-2v-1h-1 0l-16-35-1-1c-1-4-3-7-5-10l-7-18c0-2 0-4-1-6-1-5-1-10 0-15l-1-3c1-5 2-9 5-12 2-5 7-8 12-10 2-1 3-1 5-1l6-1z"></path><path d="M211 208v-1h1c1 1 2 2 2 3v1c-1-1-1-1-1-2l-1-1-1 1v-1z" class="K"></path><path d="M219 208v3h1v4l-1-1c-1 0 0-5 0-6zm-18-41c1-3 2-4 4-5l1 1-1 1h0l-3 3h0-1zm12 42h-1c-1 4 2 5 2 8h0 0c-1 0-1-1-1-1l-2-3c-1-2-1-3 0-5v1l1-1 1 1z" class="E"></path><path d="M203 199h4l-2 1h0c2 2 4 1 6 2-2 0-3-1-4 0-2 0-4-1-6-1l-1-1 3-1z" class="P"></path><path d="M205 162c3-1 6-2 8 0h1v2l-1-1h-7l-1-1z" class="K"></path><path d="M205 185c1 0 2 1 2 2 1 0 1 0 2 1h0c-1 1 0 3-1 4-1-2-2-5-3-7z" class="S"></path><path d="M214 162s1 1 2 1c1 2 0 3 3 3l-2 1h0 3c-2 1-3 1-5 0-1-1-1-2-1-3v-2z" class="B"></path><path d="M231 153h4l-3 1v1c1 1 3 1 5 1l1 1 3-2v1c0 1-1 1-2 3l-1-1c0-1 0-1-1-1-2 0-2 0-3 1-1 0-2 1-2 2v2-3c0-1 0-2-1-2 0-2-1-2 0-4z" class="a"></path><path d="M207 199c3 1 5 1 8 2 2 1 4 2 5 5l-1 1c-1-1-1-2-1-3-2-1-5-2-7-2-2-1-4 0-6-2h0l2-1z" class="U"></path><path d="M219 155c2 2 4 4 4 7 0 2 0 3-1 4s-1 1-2 1h-3 0l2-1s1-1 2-1v-3c0-2-2-4-3-5l1-2z" class="F"></path><path d="M194 185h3v1c2 0 4-2 6-1v4h-5l-3-3-1-1z" class="E"></path><path d="M247 153v-3h1l1 1v1c0 2-2 4-2 5-1 1 0 2 0 3 0 3-1 5-2 8l-1-1c1-1 1-2 1-3l1-1v-3c0-2-2-3-3-4h0v-1l2 2c0-1 1-1 1-2l1-2z" class="W"></path><path d="M202 141h1 1c1 1 3 2 4 2-1 1-4 1-6 1-2 1-4 1-6 2s-4 4-6 5c2-5 7-8 12-10z" class="C"></path><path d="M205 167h1c1 0 2 0 3 1s1 1 1 2c0 2-1 3-2 4h-1c-1 0-2-1-3-1 0-1-1-2-1-3s1-2 2-3z" class="U"></path><path d="M206 168h1c1 0 1 1 2 2 0 1 0 1-1 2s-1 1-2 1c0-1-1-1-1-2 0-2 0-2 1-3z" class="E"></path><path d="M200 200s-1 0-1 1v-1l1-1h2 0c0-1-1-1-2-1 0-1 0-1-1-1h-1s-1 0-2-1h-2v-1c-1-1-1-1 0-2 0 0 1 0 2-1 0-1 0-1 1-2h0c0-1 0-1-1-2v-1h-1 0c0 1 0 2-1 2s-1 0-1-1c-1-1-1-2 0-2v-2h-1v-1c1 0 1 0 2 1v1h0l1 1 3 3h5v1h-3-1 0c-1 0-2 1-2 2-1 1 0 3 0 4l4 1h1 0l1 1s1 0 0 1l-3 1z" class="O"></path><path d="M194 185h0l1 1c-1 1-1 1-1 3 0-1-1-2-1-2 0-1 0-2 1-2z" class="C"></path><defs><linearGradient id="AS" x1="207.846" y1="139.338" x2="219.428" y2="152.025" xlink:href="#B"><stop offset="0" stop-color="#6b6a6a"></stop><stop offset="1" stop-color="#929190"></stop></linearGradient></defs><path fill="url(#AS)" d="M202 141c2-1 3-1 5-1h-1c2 1 4 1 6 2 3 1 6 4 9 6 1 1 1 2 2 3l-1 1c-4-4-9-7-14-9-1 0-3-1-4-2h-1-1z"></path><path d="M234 158c1-1 1-1 3-1 1 0 1 0 1 1l1 1h0c1 1 1 1 1 3 0 1-1 2-2 2l-1 1h-2c-1-1-2-1-2-2l-1-1v-2c0-1 1-2 2-2z" class="J"></path><path d="M233 163h1 0c1 1 2 1 3 0 3-1 1-2 2-4h0c1 1 1 1 1 3 0 1-1 2-2 2l-1 1h-2c-1-1-2-1-2-2z" class="I"></path><path d="M234 158c1-1 1-1 3-1 1 0 1 0 1 1v1 1h-2c-1 0-1 1-2 0v-2h0z" class="M"></path><defs><linearGradient id="AT" x1="208.283" y1="139.728" x2="224.106" y2="144.767" xlink:href="#B"><stop offset="0" stop-color="#212022"></stop><stop offset="1" stop-color="#4b4b4b"></stop></linearGradient></defs><path fill="url(#AT)" d="M207 140l6-1v1h0 8c1 2 3 3 5 5l-2 1c-2 1-2 1-3 2-3-2-6-5-9-6-2-1-4-1-6-2h1z"></path><defs><linearGradient id="AU" x1="207.92" y1="166.964" x2="206.445" y2="153.893" xlink:href="#B"><stop offset="0" stop-color="#b5b5b6"></stop><stop offset="1" stop-color="#e1dfe1"></stop></linearGradient></defs><path fill="url(#AU)" d="M196 167c0-3 1-5 2-7 2-4 7-6 10-7 4 0 7 0 11 2l-1 2c-4-2-7-2-11-1-3 1-6 3-7 6-2 3-2 6-2 9v-3h-1l-1-1z"></path><defs><linearGradient id="AV" x1="207.741" y1="187.236" x2="214.001" y2="175.574" xlink:href="#B"><stop offset="0" stop-color="#8d8c8c"></stop><stop offset="1" stop-color="#bfbebf"></stop></linearGradient></defs><path fill="url(#AV)" d="M196 167l1 1h1v3l1 1c1 5 5 9 9 11 2 1 3 1 5 2h0c1 1 3 1 5 1 5 1 11 0 16-4v1c-1 1-2 2-3 4h-1c-3 3-8 3-12 2l-4-1v1h1l-1 1-2 1c-1-2-1-2-3-3h0c-1-1-1-1-2-1 0-1-1-2-2-2h0c-6-5-9-10-9-18z"></path><defs><linearGradient id="AW" x1="198.912" y1="169.306" x2="220.975" y2="166.51" xlink:href="#B"><stop offset="0" stop-color="#262526"></stop><stop offset="1" stop-color="#4c4b4b"></stop></linearGradient></defs><path fill="url(#AW)" d="M198 171c0-3 0-6 2-9 1-3 4-5 7-6 4-1 7-1 11 1 1 1 3 3 3 5v3c-1 0-2 1-2 1-3 0-2-1-3-3-1 0-2-1-2-1h-1c-2-2-5-1-8 0-2 1-3 2-4 5v4c1 4 5 8 8 11 0 1 1 1 1 1l2 1 1 1c-2-1-3-1-5-2-4-2-8-6-9-11l-1-1z"></path><defs><linearGradient id="AX" x1="228.887" y1="146.711" x2="229.575" y2="173.349" xlink:href="#B"><stop offset="0" stop-color="#595859"></stop><stop offset="1" stop-color="#6f6f6f"></stop></linearGradient></defs><path fill="url(#AX)" d="M226 145l1 1 1 1h0l2 2v1l1 3c-1 2 0 2 0 4 1 0 1 1 1 2v3l1 1c0 1 1 1 2 2h2c-1 0-1 1 0 2h0c-1 2-1 4-3 5-1 1-1 1-3 2 0-1-1-1-1-2-1-1-1-2-2-3v-1c-1-1-1-2-1-3 1-1 1-2 1-3-1 1-1 0-1 1l-1 2v-1c1-2 1-3 1-5h0c-1-1-2-3-3-4 0-1-1-2-2-3l1-1c-1-1-1-2-2-3 1-1 1-1 3-2l2-1z"></path><path d="M226 145l1 1 1 1h0l2 2h-1l-1-1c-1 0-2 1-2 1-1 1-1 0-2 1v-4l2-1zm-3 6c1 1 3 2 3 4 1 1 2 3 3 4h-2 0c-1-1-2-3-3-4 0-1-1-2-2-3l1-1z" class="S"></path><path d="M186 166h0v3l1 1v-2c1 4 1 8 2 12 0 1 0 2 1 3 0 1 1 1 1 2v3c-1 1-1 2-1 3h1c0 1 1 3 1 4 0 0-1 0-1 1l1 1v1c1 0 1 1 2 2 0 0 1 1 2 1 1 1 1 2 2 3l2 2c2 3 4 5 6 9v-1h0c0 1 1 1 1 1h0v-1h0l1 1v1l1 1c1 1 3 3 5 3l-1 1-1-1-1 1h1c1 0 1 1 2 1v1l-1-1c-1 0-3-1-3-2-2-1-4-1-5-2-2-2-4-3-6-3-1-4-3-7-5-10l-7-18c0-2 0-4-1-6-1-5-1-10 0-15z" class="N"></path><path d="M244 167l1 1c-1 2-2 4-4 5l1 1c0 2-4 4-5 6l-3 2c-5 4-11 5-16 4-2 0-4 0-5-1h0l-1-1-2-1s-1 0-1-1h1 0v-1l1 1v-1h0 1 5c1-1 1-1 2-1h2c1-1 1-1 2-1h2 0c8-2 15-5 19-12z" class="V"></path><path d="M244 167l1 1c-1 2-2 4-4 5-6 5-14 9-22 8h-1c3 0 5-1 7-2h0c8-2 15-5 19-12z" class="I"></path><defs><linearGradient id="AY" x1="255.032" y1="177.557" x2="235.03" y2="178.488" xlink:href="#B"><stop offset="0" stop-color="#5a595a"></stop><stop offset="1" stop-color="#747373"></stop></linearGradient></defs><path fill="url(#AY)" d="M249 152c1 1 1 1 1 2v2h2v-1l2 2h1 1l-1 3c-1 2-2 4-2 6-1 0-1 1-1 2h0 0c2 2 2 2 4 3h1l-1 1s-1 1-1 2v2s-1 1-1 2h-1v1h0c-1 1-1 1-1 2h2 0v1h-1-1l-1 1c-5 2-11 4-17 5h-1l-1 1-1-2c1-2 2-3 3-4v-1l3-2c1-2 5-4 5-6l-1-1c2-1 3-3 4-5 1-3 2-5 2-8 0-1-1-2 0-3 0-1 2-3 2-5z"></path><path d="M252 168c2 2 2 2 4 3h1l-1 1h-1c-1 0-2-1-3-2v-2z" class="W"></path><path d="M248 174h1s1 2 0 2c0 1-1 2-2 2-1 1-1 0-2 0 1-2 2-3 3-4z" class="H"></path><path d="M234 183c1 0 2-1 3-1l-3 6h0-1l-1 1-1-2c1-2 2-3 3-4z" class="P"></path><defs><linearGradient id="AZ" x1="253.741" y1="158.641" x2="240.5" y2="168.233" xlink:href="#B"><stop offset="0" stop-color="#525252"></stop><stop offset="1" stop-color="#706f6f"></stop></linearGradient></defs><path fill="url(#AZ)" d="M249 152c1 1 1 1 1 2v2h2v-1l2 2h1 1l-1 3c-1 2-2 4-2 6-1 0-1 1-1 2h0 0v2l-1-1c-1 1-2 2-2 3v2h-1v-3c-1 1-3 2-4 3l-6 6h-1c1-2 5-4 5-6l-1-1c2-1 3-3 4-5 1-3 2-5 2-8 0-1-1-2 0-3 0-1 2-3 2-5z"></path><path d="M255 157h1l-1 3c-1 2-2 4-2 6-1 0-1 1-1 2h0 0v2l-1-1c-1 1-2 2-2 3h0c-1-2 3-7 3-10 1-1 1-3 2-5h1z" class="S"></path><defs><linearGradient id="Aa" x1="235.03" y1="160.359" x2="245.265" y2="141.41" xlink:href="#B"><stop offset="0" stop-color="#181718"></stop><stop offset="1" stop-color="#343333"></stop></linearGradient></defs><path fill="url(#Aa)" d="M213 139c8-1 17-1 25 1 4 0 7 1 10 2 6 3 12 7 15 14 1 4 1 8 0 12-1 3-2 7-2 11-1-1-1-2-1-3h-1l-2 3h0l-1 3v-3l-1 1v-1-3-2c0-1 1-2 1-2l1-1h-1c-2-1-2-1-4-3h0 0c0-1 0-2 1-2 0-2 1-4 2-6l1-3h-1-1l-2-2v1h-2v-2c0-1 0-1-1-2v-1l-1-1h-1v3c0-3 0-4-2-6l-3 3c-2 2-4 3-7 3h-4l-1-3v-1l-2-2h0l-1-1-1-1c-2-2-4-3-5-5h-8 0v-1z"></path><path d="M242 147l1-1v-2l-1-1h1l2 1c-1 1-1 2 0 3l-3 3-1-1c0-1 1-1 1-2z" class="S"></path><path d="M238 146h1v-2c1 0 1 1 2 1l1 2c0 1-1 1-1 2h-1c-1 0-2 0-3-1h1c-1-1-1-1-1-2h1z" class="U"></path><path d="M241 145l1 2c0 1-1 1-1 2h-1c-1 0-2 0-3-1h1 2c1-1 1-1 1-3z" class="K"></path><path d="M233 143l-1-2h1c2 1 3 3 5 5h-1c0 1 0 1 1 2h-1c1 1 2 1 3 1h0-2 0v1l-1 1-2 1c0-1-1-3-1-4s-1-1-1-2h0 1l-1-1v-2z" class="a"></path><path d="M233 143c1 2 1 5 4 6v1h1l-1 1-2 1c0-1-1-3-1-4s-1-1-1-2h0 1l-1-1v-2z" class="P"></path><path d="M245 144c1 2 3 3 4 5 2 1 3 2 4 4v1c1 1 1 2 2 3h0-1l-2-2v1h-2v-2c0-1 0-1-1-2v-1l-1-1h-1v3c0-3 0-4-2-6-1-1-1-2 0-3z" class="M"></path><path d="M249 149c2 1 3 2 4 4v1c1 1 1 2 2 3h0-1l-2-2c0-2-1-4-3-6z" class="V"></path><path d="M221 140c3 0 6 0 9 2v1c1 0 2 1 2 1 0 1 1 1 1 2h0l-1-1c-1 1-2 1-3 1-1-1-1 0-2 0l-1-1c-2-2-4-3-5-5z" class="D"></path><path d="M227 146c1 0 1-1 2 0 1 0 2 0 3-1l1 1c0 1 1 1 1 2s1 3 1 4l2-1 1-1v-1h0 2 0 1l1 1c-2 2-4 3-7 3h-4l-1-3v-1l-2-2h0l-1-1z" class="W"></path><path d="M234 149v-1c0 1 1 3 1 4h-3l-1-1c1-1 1-1 2-1h1v-1z" class="K"></path><path d="M227 146c1 0 1-1 2 0 1 0 2 0 3-1l1 1c0 1 1 1 1 2v1 1h-1c-1 0-1 0-2 1l-1-1v-1l-2-2h0l-1-1z" class="U"></path><path d="M228 147c1 0 1 0 2 1 1 0 3 0 4 1v1h-1c-1 0-1 0-2 1l-1-1v-1l-2-2z" class="P"></path><path d="M253 154c1 1 2 2 3 2s1 0 2 1h1v8c0 2 1 4 0 7v2c1 1 1 1 0 2l-2 3h0l-1 3v-3l-1 1v-1-3-2c0-1 1-2 1-2l1-1h-1c-2-1-2-1-4-3h0 0c0-1 0-2 1-2 0-2 1-4 2-6l1-3h-1 0c-1-1-1-2-2-3z" class="T"></path><path d="M255 157h1c1 1 1 1 1 2l-1 2h0v1h0v2c-1 2-1 4 0 6v1c-2-1-2-1-4-3h0 0c0-1 0-2 1-2 0-2 1-4 2-6l1-3h-1 0z" class="E"></path><path d="M258 157h1v8c0 2 1 4 0 7v2c1 1 1 1 0 2l-2 3h0v-1-1-5c1-2 1-4 1-6v-9z" class="C"></path><defs><linearGradient id="Ab" x1="216.955" y1="235.519" x2="246.79" y2="178.752" xlink:href="#B"><stop offset="0" stop-color="#272626"></stop><stop offset="1" stop-color="#595959"></stop></linearGradient></defs><path fill="url(#Ab)" d="M253 182h1v-1h0-2c0-1 0-1 1-2h0v-1h1c0-1 1-2 1-2v3 1l1-1v3l1-3h0l2-3h1c0 1 0 2 1 3h0l-2 4h0v1 1h0v1c-1 2-2 4-2 6h0c-1 1 0 1 0 2v1c-1 1-2 3-2 4-4 15-6 30-4 44 1 4 1 7 2 10h-1c-1 0-1 0-2-1h-2-2l-3 1c-1 2-2 4-3 5l-1 2h0c-2-1-4-1-5-2l-10-4c-2-1-5-2-7-2v-1h-1 0l-16-35-1-1c2 0 4 1 6 3 1 1 3 1 5 2 0 1 2 2 3 2l1 1v-1c-1 0-1-1-2-1h-1l1-1 1 1 1-1c1 0 2 2 3 2s1 1 2 1h0 1v-8-4h-1v-3-1l1-1c-1-3-3-4-5-5-3-1-5-1-8-2h-4c1-1 0-1 0-1l-1-1h0-1l-4-1c0-1-1-3 0-4 0-1 1-2 2-2h0 1c1 0 1 1 2 1l-1-1c2 0 2 0 3-1v-3c1 1 1 2 2 3 0 1 0 1 1 2 0 1 1 2 2 3-1 0-1 0-2 1v1c1 0 2-2 2-2 0-1 0-1-1-2 1-1 0-3 1-4 2 1 2 1 3 3l2-1 1-1h-1v-1l4 1c4 1 9 1 12-2h1l1 2 1-1h1c6-1 12-3 17-5l1-1h1z"></path><path d="M215 201c2 0 4 1 5 3 0 1 1 2 1 3h0c0 1-1 2-1 4h-1v-3-1l1-1c-1-3-3-4-5-5z" class="K"></path><path d="M199 190h1c1 0 1 1 2 1l2 2h1l-1 1-1-1h0c0 1 1 1 1 2v1c-1 0-1 1-2 1h-1l1-1h-3 0l2-1c1-1 0-1-1-2v-1c0-1 0-1-1-2z" class="C"></path><path d="M200 192l3 3v1h-1-3 0l2-1c1-1 0-1-1-2v-1z" class="E"></path><path d="M199 190h0c1 1 1 1 1 2v1c1 1 2 1 1 2l-2 1h0 3l-1 1-4-1c0-1-1-3 0-4 0-1 1-2 2-2z" class="D"></path><path d="M199 190h0c1 1 1 1 1 2v1c1 1 2 1 1 2h-3v-1c0-1 1-1 0-2h0c1-1 1-1 1-2h0z" class="L"></path><path d="M259 176h1c0 1 0 2 1 3h0l-2 4h0c-2 2-3 4-4 6l-1 2h0v-1-1h1v-1h1l-1-1 1-1h0l1-1h0 0v-1c-1 1-1 1-1 2l-3 3h0c1-2 2-5 3-7l1-3h0l2-3z" class="N"></path><path d="M253 182h1v-1h0-2c0-1 0-1 1-2h0v-1h1c0-1 1-2 1-2v3 1l1-1v3c-1 2-2 5-3 7s-2 4-4 5l1-5 1-1h0v-1h0c0-2 1-4 2-5z" class="V"></path><path d="M215 189h-1v-1l4 1c4 1 9 1 12-2 1 4 4 7 6 10-1 1-1 1-3 2-1 0-2 1-3 2l-4 4v-1c1-1 1-1 1-2h1v-1-1c-1-1-1-1-1-2-1 1-2 2-4 3h-1c0-1 0-2-1-2 0-1-1-1-1-1v-1l-1-1v-1c-1 0-2-1-3-1-2 0-3-2-4-3l2-1 1-1z" class="K"></path><path d="M224 199c0-1 0-2-1-3l1-1c1 1 2 1 2 3-1 0-1 1-2 1z" class="J"></path><path d="M215 189c1 1 2 3 2 4l-1 1c-2 0-3-2-4-3l2-1 1-1zm9 10l-1 1c-1-1-3-2-3-4 0-1 0-1-1-1v-1h0c2 0 3 0 5 1l-1 1c1 1 1 2 1 3z" class="M"></path><path d="M255 189c1-2 2-4 4-6v1 1h0v1c-1 2-2 4-2 6h0c-1 1 0 1 0 2v1c-1 1-2 3-2 4-4 15-6 30-4 44 1 4 1 7 2 10h-1c-1 0-1 0-2-1-1-3-1-5-2-7-2-13-1-29 2-42 1-4 3-8 4-12l1-2z" class="F"></path><path d="M255 189c1-2 2-4 4-6v1 1h0v1c-1 2-2 4-2 6h0c-1 1 0 1 0 2v1c-1 1-2 3-2 4 0-2 0-4 1-6 0-2 1-3 1-5 0 0-1 1-2 1z" class="H"></path><path d="M251 183l1-1h1c-1 1-2 3-2 5h0v1h0l-1 1-1 5c-1 4-3 10-3 14-1 0-1 0-1-1v-1l-2-2h0c-1 0-1-1-2-1-1-1-1-1-2-3h0-1l-1 2c-1-1-1-1-1-2v-1h-2-1c2-1 2-1 3-2-2-3-5-6-6-10h1l1 2 1-1h1c6-1 12-3 17-5z" class="a"></path><path d="M236 197h1v2h1v-1c2 3 4 5 6 6 1 1 1 1 1 2l-2-2h0c-1 0-1-1-2-1-1-1-1-1-2-3h0-1l-1 2c-1-1-1-1-1-2v-1h-2-1c2-1 2-1 3-2z" class="M"></path><path d="M251 183c0 1-1 2-2 2-4 2-7 3-11 3-1 0-3 1-4 1s-1 0-1-1h1c6-1 12-3 17-5z" class="I"></path><path d="M230 187h1l1 2 1 1c2 1 5 3 8 4 0 1 1 1 1 2-1 1-1 0-2 0 0 1-1 2-2 2v1h-1v-2h-1c-2-3-5-6-6-10z" class="J"></path><path d="M233 190c2 1 5 3 8 4-2 1-2 2-4 2l-4-4v-2z" class="W"></path><path d="M242 217l2-1v2 5 1 2c1 3 0 5 1 8v7l-1 5c-3-3-6-7-9-11-2-1-6-3-7-5v-2-1h-1c2-2 4-3 6-4 3-2 5-4 8-7 0 1 0 1 1 1z" class="a"></path><path d="M243 219c-1 2 0 3 0 5l-1 10c0 2 0 4-1 5-1-1-1-5-1-6-1-3-1-12 1-14l1-1c1 0 1 0 1 1h0z" class="U"></path><path d="M241 216c0 1 0 1 1 1l-1 1-6 6c-1 1-3 2-3 4-1 0-1 1-2 1h-1c1 1 1 1 2 1h1v1 1c2 1 3 2 4 3l1 1h1c0 1 1 1 1 2 1 1 2 2 2 3 1 0 1-1 2-1 0-7 1-14 0-21h0l1-1v5 1 2c1 3 0 5 1 8v7l-1 5c-3-3-6-7-9-11-2-1-6-3-7-5v-2-1h-1c2-2 4-3 6-4 3-2 5-4 8-7z" class="V"></path><defs><linearGradient id="Ac" x1="235.617" y1="218.278" x2="228.732" y2="203.302" xlink:href="#B"><stop offset="0" stop-color="#3e3d3d"></stop><stop offset="1" stop-color="#605f60"></stop></linearGradient></defs><path fill="url(#Ac)" d="M233 199h1 2v1c0 1 0 1 1 2l1-2h1 0c1 2 1 2 2 3 1 0 1 1 2 1h0l2 2v1l-2-1-2 2h1 1c1 1 0 1 0 2v2c1 1 1 2 1 4l-2 1c-1 0-1 0-1-1-3 3-5 5-8 7-2 1-4 2-6 4-2-3-4-7-4-11 0-2 0-6 2-8 0-1 1-2 1-3l4-4c1-1 2-2 3-2z"></path><path d="M233 199h1l-1 2c-3 2-5 5-7 8 0-1 0-2 1-3v-1c0 1-1 2-2 2v1c0-1 1-2 1-3l4-4c1-1 2-2 3-2z" class="F"></path><path d="M234 199h2v1c0 1 0 1 1 2-1 2-3 4-3 6h-1c0 2 0 5-2 7h-1l-1-1c1-2 1-4 2-5l2-5v-1h1 0l-1-2 1-2z" class="S"></path><defs><linearGradient id="Ad" x1="230.68" y1="210.08" x2="241.438" y2="203.687" xlink:href="#B"><stop offset="0" stop-color="#535353"></stop><stop offset="1" stop-color="#747373"></stop></linearGradient></defs><path fill="url(#Ad)" d="M237 202l1-2h1 0c1 2 1 2 2 3 1 0 1 1 2 1h0c-2 4-6 9-10 11h-1c-1 1-1 1-2 1-1-1-1-1-1-2l1 1h1c2-2 2-5 2-7h1c0-2 2-4 3-6z"></path><defs><linearGradient id="Ae" x1="224.105" y1="222.945" x2="234.73" y2="208.789" xlink:href="#B"><stop offset="0" stop-color="#bfbebf"></stop><stop offset="1" stop-color="#e3e1e3"></stop></linearGradient></defs><path fill="url(#Ae)" d="M225 208v-1c1 0 2-1 2-2v1c-1 1-1 2-1 3-1 4-2 7 0 11 0 1 1 2 2 3 3 0 9-6 11-8s3-3 4-6v1 2c1 1 1 2 1 4l-2 1c-1 0-1 0-1-1-3 3-5 5-8 7-2 1-4 2-6 4-2-3-4-7-4-11 0-2 0-6 2-8z"></path><path d="M241 216c1-1 2-2 2-4 1 1 1 2 1 4l-2 1c-1 0-1 0-1-1z" class="E"></path><defs><linearGradient id="Af" x1="212.347" y1="243.678" x2="230.471" y2="233.586" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#343333"></stop></linearGradient></defs><path fill="url(#Af)" d="M199 215c2 0 4 1 6 3 1 1 3 1 5 2 0 1 2 2 3 2l1 1c1 2 5 3 6 5 6 4 11 8 16 13l6 6c1 2 2 4 4 5l-3 1c-1 2-2 4-3 5l-1 2h0c-2-1-4-1-5-2l-10-4c-2-1-5-2-7-2v-1h-1 0l-16-35-1-1z"></path><path d="M199 215c2 0 4 1 6 3 1 1 3 1 5 2l-1 1 2 4c-2-1-4-3-6-4-1-1-4-5-5-5l-1-1z" class="E"></path><path d="M211 225l-2-4 1-1c0 1 2 2 3 2l1 1c1 2 5 3 6 5h-1 0c0 1 0 1 1 2l1 2-6-4-4-3z" class="M"></path><path d="M211 225l-2-4 1-1c0 1 2 2 3 2l-1 1c1 2 2 3 3 5l-4-3z" class="U"></path><path d="M229 251l-1 1c-2 0-3-1-5-2-3-1-4-4-5-7-1-2-2-5-3-7 0-1-1-2 0-4 1 0 1 0 1 1h-1c0 1 1 3 1 5 1 2 3 5 5 6h0l7 7h1z" class="Z"></path><path d="M221 240l1-1c1 0 2 1 3 2 3 1 5 3 7 5 1 3 3 5 3 7v2c-1 0-3-1-4-2l-2-2h-1l-7-7v-1l-1-1 1-1v-1z" class="D"></path><path d="M221 240h1l1 1v2c-1 0-2-1-2-2v-1z" class="P"></path><path d="M223 241c2 2 3 5 5 7-2-1-4-3-5-5v-2z" class="E"></path><path d="M220 228c6 4 11 8 16 13l6 6c1 2 2 4 4 5l-3 1-1-1c-7-6-13-14-21-20l-1-2c-1-1-1-1-1-2h0 1z" class="B"></path><defs><linearGradient id="Ag" x1="256.921" y1="294.346" x2="331.068" y2="385.688" xlink:href="#B"><stop offset="0" stop-color="#131313"></stop><stop offset="1" stop-color="#2f2e2f"></stop></linearGradient></defs><path fill="url(#Ag)" d="M216 251h1v1c2 0 5 1 7 2l10 4c1 1 3 1 5 2l-2 1 1 2 4 2 8 6c0 1 2 3 3 3l1 1c1 1 1 2 2 3 7 8 14 16 24 20 2 2 5 3 7 3 1 1 3 1 3 1l5 1h4 0c1 0 1 0 2-1 0-1-1-1-2-2h2 3 3l1 1 2 2-1 1h0c2 3 3 7 6 9h0c0-1-1-2-1-2l-3-5v-1c1-1 4-2 5-3s1-2 1-2h0c1 2 1 3 2 4l1 3c0 1 1 2 1 3v1c1 1 1 1 1 2v2l1 1c0 1 1 2 1 3v1s0 1 1 1v2 1c1 1 1 2 1 3l1 1v1 1c1 0 1 1 0 2h1v1h0c1 1 1 1 1 2v1h0c0 1 0 2 1 2v2h0v1c0 1 0 1 1 1l-1 1c0 1 1 1 1 3v2c0 1 0 1 1 2l-1 1c0 1 1 3 1 5 0 1-1 3-1 5 0 1 1 2 0 3 0 1 0 2 1 3-1 10-5 22-12 29-5 4-9 6-14 7-2 0-5 0-7-1h-1-1c-5-2-9-4-12-7h0c-2-1-5-4-7-5h-2l-20-45-18-42c-2-6-5-11-7-17l-15-35z"></path><path d="M254 328h1c0 1-1 3-2 4h0l-1-1c0-1 1-2 2-3z" class="C"></path><path d="M323 378c0 4-2 5-2 8 0 1-2 2-2 4l-3 2c1-1 1-2 1-3 3-3 4-7 6-11z" class="Q"></path><path d="M240 304c2 0 4 1 6 2 0 1 0 1 1 2 0 1 1 1 1 2-3-1-6-3-8-5v-1z" class="E"></path><defs><linearGradient id="Ah" x1="250.175" y1="310.555" x2="254.804" y2="310.342" xlink:href="#B"><stop offset="0" stop-color="#868485"></stop><stop offset="1" stop-color="#9d9d9e"></stop></linearGradient></defs><path fill="url(#Ah)" d="M246 306c1 0 3 1 4 2l5 2c0 1 2 3 2 3h0l-1-1h-1c2 2 1 2 1 3l-5-3-3-2c0-1-1-1-1-2-1-1-1-1-1-2z"></path><path d="M246 306c1 0 3 1 4 2h0v1h-1c1 1 1 2 2 3l-3-2c0-1-1-1-1-2-1-1-1-1-1-2z" class="S"></path><path d="M263 338l1 1c2 0 3 1 3 2h0 0v2c1 2-1 3-1 5 1 2 0 3 0 5 0 0 1 1 1 2h-1v-1c-1-2-4-3-3-6h0v-1s0-1 1-1l-1-1h0c0-2-3-3-4-4h-1l-2-1v-1c3 1 5 2 7 4 1 0 2 1 2 1v2c-1 1-1 1-2 3h1c2-2 2-4 3-6-1-2-2-3-4-5z" class="N"></path><path d="M256 339v-1c1-1 1-1 2-1 2-1 4 1 5 1 2 2 3 3 4 5-1 2-1 4-3 6h-1c1-2 1-2 2-3v-2s-1-1-2-1c-2-2-4-3-7-4z" class="L"></path><path d="M319 338c1-1 1-1 1-2 1 2 0 3 0 5v4h0c0 3 2 6 3 9h0 0c0-1 1-1 1-2v-1c1 1 0 1 1 2h0c1 1 1 3 1 4 0 4 0 8-1 12l-1 1v-10c-2-6-5-12-5-19v-2h0-1l1-1z" class="C"></path><defs><linearGradient id="Ai" x1="271.149" y1="363.469" x2="278.855" y2="361.722" xlink:href="#B"><stop offset="0" stop-color="#777"></stop><stop offset="1" stop-color="#a5a4a5"></stop></linearGradient></defs><path fill="url(#Ai)" d="M270 351v3-3h1v-1l1-1h0c0 7 0 15 5 20 2 3 6 5 10 5 3 0 5 0 7-1-1 1-2 2-4 2h-4c2 0 4 0 6 1h0c-2 0-4 1-5 0-5 0-9-2-12-5-4-6-6-12-5-20z"></path><path d="M256 345l1-1v2c1 3 3 5 4 8l1 4c1 2 3 4 3 7l7 15 2 5c1 0 2 0 3 1h2c1 1 2 1 2 1h1 2 0 2c0 1 0 0-1 1h3v1h-1c-2 1-5 1-8 1 1 1 2 1 3 2l2 2c1 0 1 1 2 1 0 0 0 1 1 1l1 1h1 0c1 1 1 1 2 1 0 1 0 0 1 1h1c0 1 0 1 1 1h0l1 1 1-1v1h1 1 0 2v1h1c0-1 1 0 1 0h-1-2-1-1c-5-2-9-4-12-7h0c-2-1-5-4-7-5h-2l-20-45z" class="Z"></path><path d="M287 389c-3 1-7 1-9-1-1 0-1-1-1-1h1c2 0 5 1 7 1h3v1h-1z" class="Q"></path><path d="M279 342h0l2-1c4-2 10-3 14-2 5 1 10 6 12 11 4 7 1 15 0 23l-1 1c-2 2-3 5-4 8 1 2 1 3 2 5h1c1 2 3 4 5 4h4l3-2c0 1 0 2-1 3-2 2-5 4-8 5l-1-1h0l1-1h0 1c1 0 3-1 4-1l1-1 1-1h-1c-1 1-2 2-4 2h0c-2 1-3 1-4 0-2-1-3-4-4-6v-1c0-1-1-7 0-8h0c0-1 0-1 1-2h0c0-1 1-2 2-3 0-1 0-2 1-3l1-1v-2c1 0 1 0 1-1h0v-5-3c0-1 1-3 0-4v-1-1c0-1-1-2-1-3h0l-1-1c-2-4-6-9-10-9-6-2-12 0-17 3v-1z" class="V"></path><defs><linearGradient id="Aj" x1="260.261" y1="333.057" x2="269.584" y2="327.633" xlink:href="#B"><stop offset="0" stop-color="#8a8a8a"></stop><stop offset="1" stop-color="#bfbebe"></stop></linearGradient></defs><path fill="url(#Aj)" d="M256 315c0-1 1-1-1-3h1l1 1h0c8 5 13 12 15 21 1 4 1 7 1 11l-1 4h0l-1 1v1h-1v3-3-13c-1-10-6-16-14-23z"></path><path d="M294 373h0c4-3 7-5 9-10 0-4 0-9-2-12s-5-5-8-5c-3-1-7-1-10 1-1 1-3 3-3 5-1 2 0 4 1 6l3 3c-2-1-5-3-6-6-1-2 0-5 1-7 2-3 5-4 8-5 5-1 10 0 13 3 3 2 5 6 5 9 1 5 0 11-3 15-3 3-6 5-10 6h0c-2-1-4-1-6-1h4c2 0 3-1 4-2z" class="X"></path><defs><linearGradient id="Ak" x1="284.542" y1="360.622" x2="297.547" y2="355.259" xlink:href="#B"><stop offset="0" stop-color="#4f4f4f"></stop><stop offset="1" stop-color="#666566"></stop></linearGradient></defs><path fill="url(#Ak)" d="M286 348h0 1c4 0 7 2 10 4 1 2 2 4 2 6 0 3-1 6-4 9s-5 3-9 3c-2 0-4-1-5-2 1 0 2 1 3 1h0c2 1 3 1 4 1v-2c-1-2-1-3-2-4h0c-1-4 0-7 0-10-1-2-1-3 0-4v-1h-2c1-1 1 0 2-1z"></path><defs><linearGradient id="Al" x1="247.271" y1="288.516" x2="291.853" y2="299.115" xlink:href="#B"><stop offset="0" stop-color="#333332"></stop><stop offset="1" stop-color="#575756"></stop></linearGradient></defs><path fill="url(#Al)" d="M253 278h0c0-1-1-1-2-2h0v-2l2 2 1-1c1 1 1 2 2 3 7 8 14 16 24 20 2 2 5 3 7 3 1 1 3 1 3 1v1h1v1c-1 1-2 2-2 3-1 1-2 1-3 2v-2c-4-3-9-5-13-8-4-2-7-5-10-7-1-1-3-4-5-4-1 1-2 2-2 4 0 1 0 4-1 5 0 1-1 1-1 2h-1 0c-1 1-1 1 0 2 0 2 1 4 2 5l1 1h1l2 2c2 1 4 3 5 5 2 1 3 2 4 4 6 7 8 15 9 23v1h1 1v1h0c-1 0-2 1-3 1s-1-1-1-2v-2h0c0-1-1 0 0-1v-1h0v-3-2-1h-1v-2-1l-1-1c0-1-1-3-2-4h0c-1-2-2-3-3-5s-4-5-7-6c0 0-1 0-1-1h-2 0-1l2 1 5 4 3 3 2 4c1 2 3 5 3 7 1 4 2 8 1 12v2c0-4 0-7-1-11-2-9-7-16-15-21 0 0-2-2-2-3s-7-6-8-7-1-2-1-3v-1c0 1 1 1 2 1v-1c-1-1-1-2-2-2l1-1v-1l2-1h0 0c1-1 2-1 3-2s3-4 3-6 0-4-2-6v-2z"></path><path d="M247 296h2c1 0 1 0 2-1h0 1l-2 1c0 1-1 1-1 2l-1 1c-1-1-1-2-2-2l1-1z" class="Q"></path><defs><linearGradient id="Am" x1="227.832" y1="281.437" x2="249.078" y2="270.501" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2f2e2f"></stop></linearGradient></defs><path fill="url(#Am)" d="M216 251h1v1c2 0 5 1 7 2l10 4c1 1 3 1 5 2l-2 1 1 2 4 2 8 6c0 1 2 3 3 3l1 1-1 1-2-2v2h0c1 1 2 1 2 2h0v2c2 2 2 4 2 6s-2 5-3 6-2 1-3 2h0 0l-2 1v1l-1 1c1 0 1 1 2 2v1c-1 0-2 0-2-1v1c0 1 0 2 1 3s8 6 8 7l-5-2c-1-1-3-2-4-2-2-1-4-2-6-2 0 0-2 0-2-1-2-6-5-11-7-17l-15-35z"></path><path d="M246 299l-1-1 1-1c1 0 1 1 2 2v1c-1 0-2 0-2-1z" class="P"></path><path d="M217 252c2 0 5 1 7 2h-1c-1 1 0 1 0 2-1 0-2-1-3-1-2-1-2-2-3-3z" class="U"></path><path d="M237 280c-1 0-1-1-1-1l2-2v-1c-1 1 0 1-1 1v-1h0c1-1 2-1 4-1h0 0l1-1 1 1v-1h4l1 1h1-1c-3 0-6 0-9 2-1 1-2 2-2 3z" class="N"></path><path d="M236 268h1c1 0 3 0 4 1 0 1 0 1-1 2-1 0-2 1-3 0-1 0-1-1-2-2l1-1z" class="Q"></path><defs><linearGradient id="An" x1="223.384" y1="257.298" x2="237.472" y2="258.632" xlink:href="#B"><stop offset="0" stop-color="#868584"></stop><stop offset="1" stop-color="#adadae"></stop></linearGradient></defs><path fill="url(#An)" d="M223 256c0-1-1-1 0-2h1l10 4c1 1 3 1 5 2l-2 1 1 2-15-7z"></path><path d="M237 280c0-1 1-2 2-3 3-2 6-2 9-2 2 1 3 2 5 3v2c2 2 2 4 2 6s-2 5-3 6-2 1-3 2h0c-3 0-5 0-8-1-1 0-3-2-3-3-2-3-2-6-1-10z" class="Z"></path><path d="M242 284h0c0-2 0-2 1-3s2-1 3 0c1 0 1 1 1 2v1c-1 0-1-1-2-1 0-1-1-1-1-1-1 1-1 2-2 2z" class="E"></path><path d="M242 284c1 0 1-1 2-2 0 0 1 0 1 1 1 0 1 1 2 1v-1c1 1 1 2 0 3v1c-2 0-3 0-4-2-1 0-1 0-1-1z" class="D"></path><path d="M252 281v-2h0l1 1c2 2 2 4 2 6s-2 5-3 6-2 1-3 2h0c-3 0-5 0-8-1l1-1c1 0 3 1 4 0 2 0 5-2 6-4s1-5 0-7z" class="R"></path><path d="M237 280c0-1 1-2 2-3 3-2 6-2 9-2 2 1 3 2 5 3v2l-1-1h0v2c-2-2-3-4-6-4-2 0-5 0-6 2-2 2-3 3-3 6 0 2 1 4 3 6l2 1-1 1c-1 0-3-2-3-3-2-3-2-6-1-10z" class="F"></path><path d="M279 342h-1-1v-1c-1-8-3-16-9-23-1-2-2-3-4-4-1-2-3-4-5-5l-2-2h-1l-1-1c-1-1-2-3-2-5-1-1-1-1 0-2h0 1c0-1 1-1 1-2 1-1 1-4 1-5 0-2 1-3 2-4 2 0 4 3 5 4 3 2 6 5 10 7 4 3 9 5 13 8v2c1-1 2-1 3-2 0-1 1-2 2-3v-1h-1v-1l5 1h4 0c1 0 1 0 2-1 0-1-1-1-2-2h2 3 3l1 1 2 2-1 1h0c2 3 3 7 6 9v1l3 3h0 0c-3 0-4 1-5 3l-2 3c1 1 1 1 2 0l2-1h0v2c1 0 1 0 2 1 1 0 1 0 1 1v2h2v1c-1 0-1 1-1 1v1h-1 0l-3-1c2 2 3 6 4 8l-1 1h1 0v2c0 7 3 13 5 19v10 3l-1 5c-2 4-3 8-6 11l-3 2h-4c-2 0-4-2-5-4h-1c-1-2-1-3-2-5 1-3 2-6 4-8l1-1c1-8 4-16 0-23-2-5-7-10-12-11-4-1-10 0-14 2l-2 1h0z" class="K"></path><path d="M281 318c-1 0-2 1-3 1h0c0-1 1-2 1-3l1 1 1 1z" class="E"></path><path d="M277 323c1 0 3 0 4 1l-1 1h-2c-1 0-1-1-1-2z" class="W"></path><path d="M281 314l3 2-2 1-1 1-1-1-1-1 2-2z" class="D"></path><path d="M305 380h2c-1 2-1 4 0 5s1 2 2 2l-1 1c0-1-1-1-1-1l-1-2c-1-1-1-4-1-5zm2-7c2-1 5-2 8-2 1 0 1 1 2 1v1l-1-1c-2 0-4 0-6 1-1 0-2 1-4 1h0l1-1z" class="S"></path><path d="M282 318c2-1 3 0 4 0l1 1-1 1h-3-2v-1c0-1 1-1 1-1z" class="D"></path><path d="M305 380c1-2 1-3 3-4s6-1 8 0c1 0 1 0 1 1s0 1-1 1h-1 0l-1-1h-4c-2 0-2 1-3 3h-2z" class="M"></path><path d="M306 374h0c2 0 3-1 4-1l-3 2c-1 1-2 3-3 5v1 1c0 2 0 3 1 4s1 1 2 1c0 0 1 0 1 1h-1 0-1l2 1c1 1 1 1 2 1h1c1 1 2 0 3 1h-4c-2 0-4-2-5-4h-1c-1-2-1-3-2-5 1-3 2-6 4-8z" class="U"></path><path d="M303 326h0c2 0 3-1 4-2l2 2-2 4c-1 2-3 3-5 5v-8l1-1z" class="B"></path><path d="M311 323c1 1 1 1 2 0l2-1h0v2c1 0 1 0 2 1 1 0 1 0 1 1v2h2v1c-1 0-1 1-1 1v1h-1 0l-3-1h0l-1-1-1-2h1l-1-1c-2 0-3 1-4 1 0 1-1 2-1 3l1-1h1c-1 1-1 1-2 1h-1l2-4 1-3h1z" class="D"></path><path d="M315 330c-1-1-1-2-1-3s0-1 1-1v-1 1 2l2 1v1h-2z" class="E"></path><path d="M290 302l5 1v1 1l-2 1-5 7-4 3-3-2c-1 0-2-1-2-2h4l3-3c1-1 2-1 3-2 0-1 1-2 2-3v-1h-1v-1z" class="L"></path><path d="M293 306v2h0l3-1-2 2h1l-1 2c-2 2-3 3-3 6 0 1 0 2 1 4v1c0 1 1 1 1 3l-1 1c-3-1-3-5-5-7l-1-1c-1 0-2-1-4 0v-1l2-1 4-3 5-7z" class="P"></path><path d="M293 306v2h0c-1 3-3 5-5 7l1-1v-1h0c0-1 0-1 1-1v-1l-2 2h0l5-7z" class="H"></path><path d="M288 313h0l2-2v1c-1 0-1 0-1 1h0v1l-1 1v1s-1 2-2 2-2-1-4 0v-1l2-1 4-3z" class="B"></path><path d="M286 318c1 0 2-2 2-2 1 2 2 4 4 6 0 1 1 1 1 3l-1 1c-3-1-3-5-5-7l-1-1z" class="I"></path><defs><linearGradient id="Ao" x1="304.946" y1="313.911" x2="296.681" y2="328.344" xlink:href="#B"><stop offset="0" stop-color="#252424"></stop><stop offset="1" stop-color="#555454"></stop></linearGradient></defs><path fill="url(#Ao)" d="M304 322h0c3-1 4-5 5-7 0 1 0 2 1 3 1 0 1-1 1-1v-1l1 1v-1h1v3 1l-2 3h-1l-1 3-2-2c-1 1-2 2-4 2h0c-3-1-5-1-8-1-1 1-1 2-3 2v-1l1-1c0-2-1-2-1-3v-1c2 1 3 2 4 2h1c2 1 4 1 6 0l1-1z"></path><path d="M309 321h1v2l-1 3-2-2 2-3z" class="H"></path><path d="M312 316h1v3 1l-2 3h-1v-2h-1c1-2 2-3 3-4v-1z" class="I"></path><path d="M324 373h0c-2-2-2-5-3-8-3-6-7-12-5-20h0l1-2 2-2c0 7 3 13 5 19v10 3z" class="M"></path><defs><linearGradient id="Ap" x1="308.824" y1="307.983" x2="299.274" y2="315.391" xlink:href="#B"><stop offset="0" stop-color="#232323"></stop><stop offset="1" stop-color="#555455"></stop></linearGradient></defs><path fill="url(#Ap)" d="M304 300h3l1 1 2 2-1 1h0c2 3 3 7 6 9v1l3 3h0 0c-3 0-4 1-5 3v-1-3h-1v1l-1-1v1s0 1-1 1c-1-1-1-2-1-3-1 2-2 6-5 7h0l-1 1c-2 1-4 1-6 0h-1c-1 0-2-1-4-2-1-2-1-3-1-4 0-3 1-4 3-6l1-2h-1l2-2-3 1h0v-2l2-1v-1-1h4 0c1 0 1 0 2-1 0-1-1-1-2-2h2 3z"></path><path d="M305 304c1 0 2 0 2 1l-1 1s-1 0-1-1v-1z" class="O"></path><path d="M295 312l2-2h0s-1 1 0 1l1-1h2c-2 1-3 1-3 2-1 1-1 2 0 3 0 1 0 1 1 1l1 1v1c-2-1-3-2-4-2v-4z" class="P"></path><path d="M309 304c2 3 3 7 6 9v1l3 3h0 0c-3 0-4 1-5 3v-1-3h-1v-1c1-3-3-7-4-10l1-1zm-11 12c-1 0-1 0-1-1-1-1-1-2 0-3 0-1 1-1 3-2 1 0 2 1 3 1 1 1 1 1 1 2s-1 2-1 3c-1 1-2 1-3 1h-1l-1-1z" class="S"></path><path d="M301 313l1 1-3 1c-1 0-1 0-1-1v-1h3z" class="W"></path><path d="M302 314l1 2c-1 1-2 1-3 1h-1l-1-1s1 0 1-1l3-1z" class="U"></path><path d="M303 311c1 1 1 1 1 2s-1 2-1 3l-1-2-1-1v-1l2-1z" class="M"></path><path d="M304 300h3l1 1 2 2-1 1h0l-1 1h-1c0-1-1-1-2-1h0-4l-2 1c-1 1-3 1-3 2l-3 1h0v-2l2-1v-1-1h4 0c1 0 1 0 2-1 0-1-1-1-2-2h2 3z" class="W"></path><path d="M295 305c1 0 2-1 4 0-1 1-3 1-3 2l-3 1h0v-2l2-1z" class="B"></path><path d="M301 300h3c1 1 3 2 3 3-1 1-1 1-2 1h-4c1 0 2 0 3-1v-2l-3-1z" class="M"></path><path d="M304 300h3l1 1 2 2-1 1h0l-1 1h-1c0-1-1-1-2-1h0c1 0 1 0 2-1 0-1-2-2-3-3z" class="U"></path><path d="M294 311l2-1h0-1v2 4c1 0 2 1 4 2h0 2v-1h1 2c1 1 1 2 1 3s-2 2-1 2l-1 1c-2 1-4 1-6 0h-1c-1 0-2-1-4-2-1-2-1-3-1-4 0-3 1-4 3-6z" class="K"></path><defs><linearGradient id="Aq" x1="286.739" y1="513.383" x2="355.125" y2="429.472" xlink:href="#B"><stop offset="0" stop-color="#040504"></stop><stop offset="1" stop-color="#2a2929"></stop></linearGradient></defs><path fill="url(#Aq)" d="M276 390h2c2 1 5 4 7 5h0c3 3 7 5 12 7h1-1c0 2 0 4 1 5 1 0 1 0 2 1 3 0 5 0 8-1h2 2 0l1-1c1 0 1 0 2-1h1l1 1v2c0 1 0 1 1 2v1c-1 1-2 1-2 2l-2 1c0 1-1 1-2 1 0 1-1 1-1 2l1-1c2-1 3 0 4-1h1l1-1h2 1 1l1 1v5h0l-1 1 1 1c0 2-1 5-2 7l1 1v1 1h0v1c1 1 1 1 1 2v1 2c0 1-1 1 0 2v1c6-4 15-5 22-5h7-2c0-2-1-4-2-6-1-1-1-2-1-3h1 0c1 1 1 0 1 1 1 1 1 0 2 0 4 3 9 7 13 8 8 8 13 20 15 31 0 3 0 6 1 8 1 5 1 11 0 15l-2-1v1c0 2 0 5-1 7v2c0 4-1 8-1 12-1 6-3 11-4 16-2 5-3 9-5 14l-11 23c-1 2-2 5-3 6l-18-44c-2-4-4-10-7-15v1l-1-1-1-3v-1c-1 0-1 0-1-1s-1-3-2-5v14c0 2-1 5 0 6v1c-1-3-3-6-4-8l-8-19-19-44-10-24v-1 1h1v-14-5l-1-1v-2c-1 0-1-1 0-2l-6-13z"></path><path d="M352 537h0l-1 2v1c-1-1-1-1-1-2l2-1z" class="N"></path><path d="M317 476h6v2l-2-1c-1 0-2 0-4-1z" class="O"></path><path d="M360 480c2 0 2 2 4 3h0-1l1 1v1l-1-1c0 1-1 2-1 2h-1l1-1h0c0-1 0-2-1-3h0c0-1-1-2-1-2z" class="Q"></path><path d="M316 433h0 1c2-1 3-2 4-4l1 1-4 4h0-2v1c-1 0-2 0-3-1v-1c2 0 2 0 3-1v1z" class="P"></path><path d="M327 498h1c1 2 2 3 2 5h-1l-2-2v-3z" class="L"></path><path d="M320 450h1 0c-1 2-4 1-6 2h-2v-1c2-1 4-1 7-1z" class="D"></path><path d="M361 461c1 1 1 3 1 5v-1c-1 1-1 0-1 1l-1 1-2 2v1s0 1 1 1v1c1 0 2 0 3 1l2 1v1c-1-1-3-2-4-2s-2-1-3-1h0l-2-1c1 0 2-1 3-2 1-2 3-5 3-8z" class="V"></path><path d="M341 495h1c3 0 7-1 10 1h-3 0l-2-1c-1 0-2 0-3 1h-1c-2 0-2 1-3 2h0c1 1 1 1 1 2h0v1l2 2v1c-2-1-4-3-5-5 0-1 0-2 1-3s2-1 2-1z" class="C"></path><path d="M347 514c2 1 3 3 4 6 1 2 1 3 0 5v2 1c0 1-1 2-2 3-2 2-4 2-6 2v-1c1 0 2-1 3-1l3-3 2-2v-1-4c0-1 0-1-1-2v-1c-1-2-2-2-3-4h0z" class="Z"></path><path d="M308 420c2-2 4-2 5-3-1 2-2 5-3 7v5c0 1 1 2 2 2l1 1c1 0 2-1 3 0h0c-1 1-1 1-3 1v1c-1 0-2-2-3-3-1 0-1 0-1-1v-1h0c-2 0-3 1-5 2 2-2 5-4 6-7v-1-1l1-1v-1l1-2-4 2z" class="D"></path><path d="M343 503l1 1-1 1h0c-2-1-4-3-5-4v-1c-1-2 0-1 0-3-1 1-1 1-2 1h-1c1-1 0-2 1-3h0v2h1c0-1 1-2 2-3h1c0-1 0-1 1-1l1-1h2 0c1-1 1-1 2-1 0 1-1 1-1 1h2 2 0c1 0 1 0 2 1h1c1 0 2 1 3 1h1v1c-1 0-4-1-5-2-4-1-6 0-10 2 0 0-1 0-2 1s-1 2-1 3c1 2 3 4 5 5v-1z" class="L"></path><path d="M311 417l1-1c2-1 3 0 4-1h1l1-1h2 1 0c0 1-1 1-1 1h-1c0 1-1 1-1 1-1 0-3 1-3 1-1 1-1 2-2 3-1 3-1 6-2 9 1 1 1 1 2 1l1 1-1 1-1-1c-1 0-2-1-2-2v-5c1-2 2-5 3-7-1 1-3 1-5 3l-4 1c2-2 4-3 7-4z" class="E"></path><path d="M358 535h3c1 1 2 2 2 4h0l-3-2-1 1c-1 0-1 1-1 2-1 1 0 3 0 4l2 2h0l-1-1c-2-1-5-3-6-5v-3c1-1 3-2 5-2z" class="C"></path><path d="M378 471c1 2 1 4 1 6l1-2c1 5 1 11 0 15l-2-1v1c0 2 0 5-1 7l-1-21-1-4 3 1v-2z" class="H"></path><path d="M378 471c1 2 1 4 1 6v6c-1-2-1-4-3-7h0l-1-4 3 1v-2z" class="F"></path><path d="M342 511c-3-2-6-5-7-7l-1-1c-1-5-2-10-5-14-2-3-5-5-6-8 3 2 5 6 8 8v-1-1c0-2 1-7 2-8h0v4c-1 2-1 4-1 6v3c1 1 2 3 2 4v1c1 5 5 9 9 11h0c-3 0-5-3-7-5l-1-1v-1s0-1-1-1v-1c0 4 3 7 6 9v1c1 0 1 1 2 2z" class="D"></path><path d="M285 395c3 3 7 5 12 7h1-1c0 2 0 4 1 5 1 0 1 0 2 1-4 0-8-2-12-4-1-2-1-4-2-6-1-1-1-1-1-3z" class="S"></path><path d="M329 470h1l4 4c1 0 2 1 2 1l1 1h2 1 0c1 0 1-1 1-1h0c0-1-1-1-2-1 0-1-1-2-2-2-1-2-3-4-5-6h0c-1-1-1-3-2-4v-2-1l1 1v1h0v1c0 1 1 2 1 2l1 1c0 1 1 2 2 3 1 0 0 0 1 1l1 1 1 1h1l-1-1v-1c0-1 0 0-1-1v-1c0-1-1-1-1-2 2 1 3 2 4 2l-2-2c3 1 7 6 9 9-3 0-5 2-8 3h-1c-3-1-7-4-9-7z" class="D"></path><path d="M337 470l1 1h1c1 0 2 2 3 2v1h-1c-2-1-3-3-4-4z" class="E"></path><path d="M359 471c1 0 2 1 3 1 1 1 2 1 3 2s3 1 5 2v-1c1 0 1 1 1 1v1c0 1 0 1 1 1 0 1 0 2-1 3v1 1l-1-1v-1h0c0 1 0 1-1 1 0 2-1 2-2 2-2 0-2 0-3-1h0c-2-1-2-3-4-3h0c-1-1-2-3-2-4-1-1-1-2-1-3l-1-1h1c1 0 2 1 3 1s3 1 4 2v-1l-2-1c-1-1-2-1-3-1v-1z" class="D"></path><path d="M370 476v-1c1 0 1 1 1 1v1c0 1 0 1 1 1 0 1 0 2-1 3v1 1l-1-1v-1-1c0 1 0 1-1 2h-2c-1-1-2-1-2-2v-3c2-1 3-1 4-1h1z" class="O"></path><path d="M355 471l2 1h0-1l1 1c0 1 0 2 1 3 0 1 1 3 2 4h0s1 1 1 2h-1v1 2 1h-2l-1-1v-1c1 0 1-1 2-1v-1c-1-1-2-1-2-1h-1-3c-1 0 0-1-2-1v1l-1-1h0-1-1c-1-1-1-1-1-3 1-1 2-2 3-2l1-1h-1 0c-1 1-3 1-4 2s-2 1-3 1l-1 1h-1c2 2 6 5 9 7 0 0 1 0 1 1 1 0 1 1 2 1l1 1 2 2v1c-2 1-3 0-5-1h0l-1-1-2-1-1-1c3 1 6 3 9 4-2-2-5-5-8-7s-7-5-10-7h0 1 1c2 1 13-5 15-6z" class="E"></path><path d="M321 414h1l1 1v5h0l-1 1 1 1c0 2-1 5-2 7s-2 3-4 4h-1 0v-1h0c-1-1-2 0-3 0l1-1-1-1c-1 0-1 0-2-1 1-3 1-6 2-9 1-1 1-2 2-3 0 0 2-1 3-1 0 0 1 0 1-1h1s1 0 1-1h0z" class="T"></path><path d="M323 422c0 2-1 5-2 7s-2 3-4 4h-1 0c3-4 5-7 7-11z" class="E"></path><defs><linearGradient id="Ar" x1="352.667" y1="533.914" x2="348.369" y2="492.879" xlink:href="#B"><stop offset="0" stop-color="#302f30"></stop><stop offset="1" stop-color="#595959"></stop></linearGradient></defs><path fill="url(#Ar)" d="M341 495c4-2 6-3 10-2 1 1 4 2 5 2 3 1 6 5 6 7l1 3c0 1 0 0 1 1 1 0 2 0 3-1 0 1-1 1-1 2-6 6-6 15-6 24l1 2-1 1c-1 0-2-1-4-2v-3c0-1 1-1 1-2l-1-1h-2c-1 0-2 0-3 1v-2c1-2 1-3 0-5-1-3-2-5-4-6s-4-2-5-3-1-2-2-2v-1c-3-2-6-5-6-9v1c1 0 1 1 1 1v1l1 1c2 2 4 5 7 5h0l2 1c2 1 4 1 7 1 1 0 3 0 4-1s1-3 1-4c0-4-3-7-5-9-3-2-7-1-10-1h-1z"></path><path d="M335 479h0c-5-3-11-8-13-13-1-5 0-11 2-15 2-5 7-9 12-11 7-2 14-1 20 2 12 6 18 17 22 29v2l-3-1c-2-6-4-13-9-18-5-6-12-11-21-12-5 0-10 1-15 5-3 4-5 8-5 13 0 4 1 7 4 10 2 3 6 6 9 7h0c3 2 7 5 10 7s6 5 8 7c-3-1-6-3-9-4-4-3-8-5-12-8z" class="R"></path><path d="M338 477c3 2 7 5 10 7s6 5 8 7c-3-1-6-3-9-4-4-3-8-5-12-8h1 1c2 1 5 3 7 5l2 1c1 0 1 1 1 1 1 0 2 1 3 1l-3-3c-3-2-7-4-9-6v-1z" class="B"></path><path d="M338 465h0c-1 0-1 0-2-1h0-1l-1-1 1-1c0 1 0 1 1 1l1-1h1l1 1c2 0 4 1 5 3l2 3 2 2v1c1 0 2-1 3-1h1v-1h-1l-2-1c-2-1-4-5-6-6h-1c-1-1-6-2-8-1h0l-1-1h-1l-1-1h0v-1h0l2 1 1-1h2l1-1h1 0c1 0 2 0 3-1h-1-1-3-3 0 2v-1h0-1-2c1 0 1-1 2-1 1-1 2 0 4 0v-1 1c2 0 1-1 2-1l2-1h2v-1h2 1c1-1 1-1 2 0h0c1-1 1-1 2-1h-1 0v-1c1 0 2 1 4 2s3 3 4 5l2 2 1 2c0 3-2 6-3 8-1 1-2 2-3 2-2 1-13 7-15 6h-1c3-1 5-3 8-3-2-3-6-8-9-9z" class="P"></path><path d="M356 465h1v1c-1 0-1 1-2 1 0 0-1 0-1-1l2-1z" class="E"></path><path d="M353 464v1h2 1l-2 1c0 1 1 1 1 1-1 1-2 1-3 2v-1c1-1 1-3 1-4z" class="V"></path><path d="M349 453c1-1 1-1 2 0v4h-1l-1-1v-3zm3 16c-3 1-6-5-9-6-3-2-6-2-8-2 3-1 6-1 9-1l1 2c1 0 1-1 2-2l13-1 1 2c0 3-2 6-3 8-1 1-2 2-3 2-2 1-13 7-15 6h-1c3-1 5-3 8-3 3-2 7-3 10-6 2-2 3-4 3-7 0 0 0-1-1-1-2 0-4 0-5 1-1 2 0 2 1 4h-2v-1c0 1 0 3-1 4v1z" class="K"></path><path d="M352 468c-1 0-2 0-2-1-2-1-2-2-3-3 0-1 1-2 1-3h2l3 3c0 1 0 3-1 4z" class="P"></path><path d="M282 403l4 10 18 43 17 38c2 6 5 12 7 17v1l-1-1-1-3v-1c-1 0-1 0-1-1s-1-3-2-5v14c0 2-1 5 0 6v1c-1-3-3-6-4-8l-8-19-19-44-10-24v-1 1h1v-14-5l-1-1v-2c-1 0-1-1 0-2z" class="Y"></path><defs><linearGradient id="As" x1="499.09" y1="377.274" x2="400.466" y2="331.27" xlink:href="#B"><stop offset="0" stop-color="#050504"></stop><stop offset="1" stop-color="#282728"></stop></linearGradient></defs><path fill="url(#As)" d="M401 491h-1c0-1 1-2 2-4l3-7 11-26 12-29 13-29 22-53 3-7 18-44 12-27 8-19 9-21c2-7 5-13 7-19l3-16c1-9 0-20-3-29-2-4-5-9-9-12-6-8-13-12-22-16h129c-3 1-5 1-8 2-20 9-38 26-47 46l-8 18-35 81-14 30-6 15-35 81-3 6-19 46-10 22-36 83-14 34c-3 7-7 14-9 22l-17-42 3-8 20-38c6-12 11-25 20-35v-1-1l2-2-1-1z"></path><path d="M527 248c1-1 1-1 2-1 1-1 1-1 2-1h0v1l-2 1s-1 0-2 1v-1z" class="N"></path><path d="M523 147l1 3c-1 1 0 2-1 2 0-1-1-1-2-2l1-1c0-1 1-2 1-2z" class="Z"></path><path d="M542 145v1l-2 3-1-1 3-3z" class="E"></path><path d="M375 548s1 1 2 1 1 1 2 2h-2c-1 0-2-1-2-2v-1z" class="D"></path><path d="M544 143h2l1 1h-1l-4 2v-1l2-2z" class="P"></path><path d="M391 534c1 0 2-1 3 0 1 0 1-2 2-2l2 2h1v1l-5-1h0-1c-1 1-1 0-2 0z" class="O"></path><path d="M485 331h1c3 1 5-1 8-1-2 1-4 3-6 4-1-2-1-2-3-3h0z" class="Q"></path><path d="M457 386c-1-1-1-2 0-2 0-1 2-3 4-3 1-1 2 0 2 0v1h0c-2 0-4 1-5 2 0 1-1 1-1 2z" class="T"></path><path d="M502 280l1 1 4 1h-3c-1 0-1 0-1 1h3v1l-1 1h-1l-1-1v-1h-3 0v-1l2-2z" class="O"></path><path d="M390 543l1-1 3 2-1 1h-2c0 1-1 2-1 3l-1-2c0-1 0-2 1-3z" class="C"></path><path d="M402 492l3-2s0 1 1 2l-6 4v-1-1l2-2z" class="B"></path><path d="M466 384c0 2 0 3-1 4v2h-1 0l-1 1h0c-1 1-2 3-4 3v-1c1 0 2 0 2-1h-2c2 0 4-2 5-3 1-2 1-2 1-4 0 0 0 1 1 1v-2zm25-65h0c0 2 0 3-1 4 0 0-1 0-2 1h0l5 1c-2 1-5 0-6 1-1 0-1 2-1 3s-1 1-1 1h-2 0 2c1-1 0-2 1-4h0c0-1 1-2 2-3 0 0 1 0 2-1v-2l1-1z" class="Q"></path><path d="M509 240v-1s1-1 2-1c3-1 7-1 10 0-3 0-9 0-11 1l-1 1z" class="D"></path><path d="M540 190c1 0 3 0 4 1 0-1 1-1 1-1 1-1 2-1 3-2 0 2 0 4 1 6-1 0-1-1-2-1-2-1 0 0-1 0s-1-1-1-1h-1l-1-1h-1l-2-1zM391 534v-1c3-1 4-5 8-5v1c-1 1-1 2-3 3-1 0-1 2-2 2-1-1-2 0-3 0z" class="C"></path><path d="M586 143h1v1h-1 0l-6 3h0c-2 1-4 3-5 5h-1l1-1 5-5h-1l-1 1h-1c1-1 2-2 4-3h1c1-1 2-1 4-1z" class="G"></path><path d="M463 381c1 1 3 2 3 3h0v2c-1 0-1-1-1-1h-1l-2 1h0l1-2-1-1c-1 0-1 0-2 1-1 0-2 1-2 2s1 1 1 2h-1 0 0c1 1 1 1 2 1-1 1-1 1-2 0s-2-2-1-3c0-1 1-1 1-2 1-1 3-2 5-2h0v-1z" class="C"></path><path d="M527 240h1v1c-1 1-2 1-3 1h-1c-1 1 0 1-1 0h-2v1c1 0 4-1 5 0h1l-2 1c-1-1-3-1-4-1h-10c1-1 1-1 2-1h1 0 1 0c-1 0-2-1-2-1 4 0 9 1 13 0l1-1z" class="Z"></path><path d="M535 190h1c1 0 2 0 3 1l-1 1v1c0 1-1 1-1 1l-2 1s-1 0-1-1c-1-1-1-1-1-3 0 0 1-1 2-1z" class="P"></path><path d="M533 191h1l2 2v1h-1s-1-1-2-1v-2z" class="V"></path><path d="M415 469c2 0 2 1 4 2l-3 4v1c-1 0-1 0-2-1v-3c0-1 0-2 1-3z" class="C"></path><path d="M520 210c1-1 1-2 2-2 2-2 5-4 8-4 2-1 3-1 4-1h1 4 2c1 1 1 1 2 1l3 2c-2 0-4 0-6 1h-1l1-2c-1-1-3-1-4-1-4 0-9 1-12 4l-4 2z" class="L"></path><path d="M379 566h1c1 0 2 1 2 2h0-2v1c0 2 1 3 2 4l1 1h-1c-2 0-4-1-5-3 0-2 0-4 2-5zm172-393l2 1v1 1h0c2-2 4-3 6-4h0 0 2 0l-6 3c-2 1-3 5-5 8h0c0 1-1 1-2 2l-1-1h-1v-1c0-1 0-1-1-2-1 0-1-1-2-2v-1c1 1 2 1 2 2s1 1 2 2v1h1 0c2-1 4-5 4-7 0-1 0-2-1-3h0zM429 439h2c1 0 1 0 1 1 0 2-1 4-2 6-1 1-2 1-2 1-1-1-1 0-1-1 1-2 0-3 0-5l2-2z" class="C"></path><path d="M537 164l2 3c1 2 3 4 5 5h2l-2 3c-1 2-1 2-3 2-1 1-1 1-1 0-1 0-1 0-2-1 1-1 1-1 1-2 2 0 2-1 3-2-1-1-2-2-2-3-1 0-1-1-1-1-2-1-2-2-2-4z" class="T"></path><path d="M464 360l1 1v3 1h0l-1 1s-1 0-1 1l-4 7c-1 2-3 4-4 6s-2 3-3 5h-1l6-9c3-5 5-10 7-15v-1z" class="P"></path><path d="M527 177c1 0 6 0 7 1s1 2 0 3c0 0 0 1-1 1h-1-1c-1 0-3-1-5-2v-2l1-1z" class="K"></path><defs><linearGradient id="At" x1="420.139" y1="483.983" x2="412.853" y2="486.368" xlink:href="#B"><stop offset="0" stop-color="#575659"></stop><stop offset="1" stop-color="#7e7d7e"></stop></linearGradient></defs><path fill="url(#At)" d="M414 484c2 1 9-2 11-3h2c-5 4-12 6-19 9l1-1s0-1 1-1c2-1 2-3 4-4z"></path><defs><linearGradient id="Au" x1="537.85" y1="224.912" x2="532.163" y2="222.257" xlink:href="#B"><stop offset="0" stop-color="#191819"></stop><stop offset="1" stop-color="#3d3e3d"></stop></linearGradient></defs><path fill="url(#Au)" d="M529 228c0-1 1-1 1-2 0-2 4-2 5-4-1 0-3 0-4-1l1-1v1c2 1 6-1 8 0h1 0v1h-1l-1 1c-2 2-7 4-10 5z"></path><path d="M464 360c0-4 2-8 3-12 1-2 1-4 2-6v3c-1 1-2 4-2 6h1 0v-1-2h1v-2l1-2v-1-1c0 2 0 4-1 5v3 1h-1v1 1h0v1h0c0 1-1 1-1 2h0v1c0 1 0 1-1 2v1 1 1 1c-1 0-1 1-1 1v-3l-1-1z" class="D"></path><path d="M486 293c2 1 5 3 6 5 2 2 4 6 4 9h-1l-1-1c-1-1-2-1-2-2v-2h-1c0-1 0-1-1-1v-2c-2-2-3-4-4-6z" class="P"></path><path d="M499 277l-4 1h-1c3-4 8-4 12-6l1 1c-1 1-1 2-1 3h0c-1 1-2 1-3 1h-2-2z" class="K"></path><path d="M371 560h1c6-1 11 3 15 6v1l-2-1c-2-1-5-3-8-3l-1-1h-9l4-2z" class="Q"></path><defs><linearGradient id="Av" x1="421.916" y1="476.334" x2="416.876" y2="482.073" xlink:href="#B"><stop offset="0" stop-color="#646464"></stop><stop offset="1" stop-color="#807f82"></stop></linearGradient></defs><path fill="url(#Av)" d="M414 482c2-2 4-3 6-4s4-2 7-3c3-2 5-4 8-6h0c-4 6-13 10-20 14v-1h-1z"></path><path d="M528 194h2c1 0 2 2 3 3h0 0c-1 2-1 2-3 3v1c-1 0-2 0-3-1s-1-3-1-4 1-2 2-2zm-34 94c3 0 6-1 9 1 0 1-1 2-2 3-2 0-4 0-6-1h-1v-1c-1 0-2-1-3-2h1 2z" class="E"></path><path d="M521 150c-1-1-1-2-2-2l-1-1c-1 0-2-2-3-3-1 0-1 1-2 0v-1h2 5 1s1 0 2 1c1 0 1 1 1 2l-1 1s-1 1-1 2l-1 1z" class="O"></path><path d="M521 238c2 1 4 1 6 2l-1 1c-4 1-9 0-13 0l-4-1 1-1c2-1 8-1 11-1z" class="P"></path><path d="M535 137c1 1 2 2 2 3 1 0 2-1 3 0-2 2-6 5-9 7 0 1-1 1-1 1-2 0-2-2-3-3l6-3c2-1 1-3 2-5z" class="C"></path><path d="M525 156c2 3 3 7 4 10l-1 2 5 4c1 1 3 2 4 2-1-2-4-5-5-8h0 0c1 1 1 2 2 3h0l3 3v2c1 1 1 1 1 2-1 0-1 0-1 1h0v-3c-1 0-2 0-3-1l-5-3-2-2c-1-1-1-3-2-4-1-3-1-5 0-8z" class="Q"></path><defs><linearGradient id="Aw" x1="413.636" y1="484.93" x2="403.118" y2="487.382" xlink:href="#B"><stop offset="0" stop-color="#9c989b"></stop><stop offset="1" stop-color="#d0d1d1"></stop></linearGradient></defs><path fill="url(#Aw)" d="M414 482h1v1l-1 1c-2 1-2 3-4 4-1 0-1 1-1 1l-1 1-2 2c-1-1-1-2-1-2l-3 2-1-1c4-3 8-7 13-9z"></path><path d="M425 446h0v1h1v2c0 1 2 2 3 2l2 2v1h-1c-1 1-1 2-2 4s-2 5-5 6h-1c-2 0-3 0-4-1h0 5c1-1 2-2 2-3 2-3 2-5 1-8-1-1-1-2-2-3 0-1 1-2 1-3z" class="Q"></path><path d="M527 228c-1 1-1 1-2 1-1 1-1 1-2 1h-2 0-1c-1 1-2 1-3 2h0v1c1 0 2 0 3 1 1 0 2 1 3 1l4 3c-5 0-11-4-16-7 2 0 3 0 4 1 2-1 3-1 4-3v-1h1v-1l1-1c0-1-1 0 0-1l2-2c1-1 2-2 3-2 1-2 2-3 4-3h-1c-1 2-3 3-4 4-2 3-4 5-5 8 2-1 5-2 7-2z" class="D"></path><path d="M539 148l1 1c-2 3-2 7-1 10l1 4h0c0 1 0 1 1 2-1 1-1 2-2 2l-2-3c-1-2-2-4-2-7 0-4 1-6 4-9z" class="U"></path><defs><linearGradient id="Ax" x1="510.655" y1="279.751" x2="504.135" y2="276.779" xlink:href="#B"><stop offset="0" stop-color="#1b1c1c"></stop><stop offset="1" stop-color="#373536"></stop></linearGradient></defs><path fill="url(#Ax)" d="M510 276c0-1 1-1 1-2l3-1c0 1-1 2-1 2 0 2-2 5-3 6-1 0-2 1-3 1l-4-1-1-1 1-1h-1c0-1-1-1-1-2h0 2c1 0 2 0 3-1h4z"></path><path d="M506 276h4c-2 2-5 2-7 3h-1c0-1-1-1-1-2h0 2c1 0 2 0 3-1z" class="E"></path><defs><linearGradient id="Ay" x1="548.808" y1="167.396" x2="540.647" y2="169.964" xlink:href="#B"><stop offset="0" stop-color="#464646"></stop><stop offset="1" stop-color="#696868"></stop></linearGradient></defs><path fill="url(#Ay)" d="M540 163c3 3 7 6 11 6s7 0 11-1h0 0-1c-1 1-2 1-3 1s-2 0-3 1h-5v-1 2c-1 0-1 1-1 1 1 1 2 1 2 1h0c-2 0-3-1-5-1h-2c-2-1-4-3-5-5 1 0 1-1 2-2-1-1-1-1-1-2z"></path><path d="M482 306h0c1 1 1 2 2 2 3 2 7 3 10 3h0 1l-2 3-1 1c0 1-1 1-2 1-2 0-4-1-6-2-2-2-2-5-2-8z" class="V"></path><path d="M477 357h-2-1-1s-1-1-1-2l-1-1v-1-1c-1-1-1-1-1-2h0v-3c1-1 1-2 1-3 0-4-1-6 1-9h1v1c0 1 0 2 1 3v5h0c2 1 3 1 4 1h1-4c-1 1-1 1-1 2v1l-1-1c-1 3-1 6 1 8 0 1 0 1-1 1 1 1 3 1 4 1z" class="Q"></path><path d="M473 356l-1-1c-1-1-1-5-1-6 0-3 0-6 1-8 0-2-1-3 0-4v-1c1 3 1 8 1 10v1c-1 3-1 6 1 8 0 1 0 1-1 1z" class="T"></path><path d="M501 263v1l2-2h0c3 1 6 1 8 1l4-1c-1 0-1 1-2 1l3 1h-2l-1 1h-1l-3 1-1 1h0-2l-1 1c-1 1-2 1-2 1h-1c-1 0-1 0-1-1h0l1-1v-1c-1 0-1 0-2-1-1 0-1 1-2 1v-1s1-1 1-2h2z" class="O"></path><path d="M526 209c2 0 5-2 7-2h1c0 1 0 2-1 3l1 1-4 4h0l1-1-1-1 1-1c1-1 2-2 2-3v-1h-2l-1 1h1v2c-1 1 0 1-1 2s0 0 0 1c-1 2-3 3-5 4v-1c-1 0-1 0-2 1h0 0l-1 1h0c1-2 2-2 3-3l-2-3h-2v-1c1-1 3-1 5-2v-1z" class="C"></path><path d="M523 213c1-1 1-1 1-2h4v1h-1c0 1 0 1 1 2h0c0 1-1 2-2 3l-1-1-2-3z" class="P"></path><path d="M528 214h-2 0l-1-1 2-1c0 1 0 1 1 2z" class="L"></path><path d="M511 243h10c1 0 3 0 4 1-2 1-4 2-7 3-1 0-2 0-3 1-2 0-6 3-8 3 0-2 2-3 3-5h1 0l-1-1v1c-1 0-1 0-1-1s1-2 2-2zm33-100l2-1c1 0 2 2 2 3 1 3 1 7 0 10l-4 7c-2-2-3-5-2-8 0-1 1-3 2-3 1-2 4-3 3-5 0-1 0-1-1-2h1l-1-1h-2z" class="D"></path><defs><linearGradient id="Az" x1="559.749" y1="178.323" x2="542.12" y2="188.969" xlink:href="#B"><stop offset="0" stop-color="#151415"></stop><stop offset="1" stop-color="#3f3f3f"></stop></linearGradient></defs><path fill="url(#Az)" d="M550 183c1-1 2-1 4-2 2 0 3-1 5-2l1-1 1 1c0 1-1 1-2 2-3 3-8 4-11 7-1 1-2 1-3 2 0 0-1 0-1 1-1-1-3-1-4-1l-1-1 1-1 2-2c1 0 1 0 1-1 1 0 0 1 1 0s1-1 2-1h1l1 1c1-1 2-1 2-2z"></path><path d="M524 208c3-3 8-4 12-4 1 0 3 0 4 1l-1 2c-2 1-3 2-5 4l-1-1c1-1 1-2 1-3h-1c-2 0-5 2-7 2v1c-2 1-4 1-5 2v1l-1 1-1-1 1-3 4-2z" class="V"></path><path d="M524 208h0l-3 3c1-1 2-1 2-1 1-1 2 0 3-1v1c-2 1-4 1-5 2v1l-1 1-1-1 1-3 4-2zm0-62c7 5 4 13 8 20h0c1 3 4 6 5 8-1 0-3-1-4-2l-5-4 1-2c-1-3-2-7-4-10-1-1-1-4-1-6l-1-3 1-1z" class="E"></path><path d="M506 254c-1 0-1-1-1-2l1-2s0-1 1-1c1-1 2-2 4-3h-1c-1 2-3 3-3 5 2 0 6-3 8-3 1-1 2-1 3-1l-2 3h1l2 1h0c-1 0-1 0-1 1v1h0c-2 1-3 2-5 3-3 1-6 2-9 4 1-1 1-3 2-4v-2z" class="C"></path><path d="M506 254c2-1 5-2 7-3-2 2-4 3-7 5h0v-2z" class="E"></path><path d="M474 344h4 0c1 0 1 0 2 1h0c2 0 2 0 3 2 0 2 0 5-2 7l-2 2-2 1c-1 0-3 0-4-1 1 0 1 0 1-1-2-2-2-5-1-8l1 1v-1c0-1 0-1 1-2h4-1c-1 0-2 0-4-1z" class="O"></path><path d="M474 347c0-1 0-1 1-2h4v1h0c-1 1-3 1-4 3v4h0-1v-6z" class="T"></path><path d="M473 347l1 1v-1 6h1 0c1 0 2 1 2 1 1 1 2 0 2 1v1l-2 1c-1 0-3 0-4-1 1 0 1 0 1-1-2-2-2-5-1-8z" class="D"></path><path d="M501 277h0c0 1 1 1 1 2h1l-1 1-2 2v1h0 3v1c-3 0-6 0-9 3v1h-2-1c1 1 2 2 3 2v1c0 1 1 1 1 2 3 3 6 6 8 9l-1 1c-3-1-8-7-10-9-1-2-3-4-3-6 1-2 1-1 2-2 1 0 2-1 2-2 2-2 4-4 6-5l1-1-1-1h2z" class="P"></path><path d="M488 334l-7 2c-1 1-1 2-1 3h-1c-3-2-5-5-7-7l4-9s2 3 2 4c2 2 3 4 6 4h1 0c2 1 2 1 3 3z" class="E"></path><defs><linearGradient id="BA" x1="536.891" y1="223.854" x2="524.449" y2="218.974" xlink:href="#B"><stop offset="0" stop-color="#2e2d2f"></stop><stop offset="1" stop-color="#626161"></stop></linearGradient></defs><path fill="url(#BA)" d="M530 218c5-4 8-4 14-4l-12 6-1 1c1 1 3 1 4 1-1 2-5 2-5 4 0 1-1 1-1 2h-2c-2 0-5 1-7 2 1-3 3-5 5-8 1-1 3-2 4-4h1z"></path><defs><linearGradient id="BB" x1="524.068" y1="245.141" x2="507.117" y2="263.458" xlink:href="#B"><stop offset="0" stop-color="#0d0d0d"></stop><stop offset="1" stop-color="#515151"></stop></linearGradient></defs><path fill="url(#BB)" d="M517 250h2c3-1 5-2 8-2v1c-2 2-6 3-7 6l-2 3c-1 1-1 2-1 3-1 0-2 1-2 1l-4 1c-2 0-5 0-8-1h0l-2 2v-1l3-3c3-2 6-3 9-4 2-1 3-2 5-3h0v-1c0-1 0-1 1-1h0l-2-1z"></path><path d="M511 263v-1c2 0 3-1 5-3 0 0 1 0 2-1-1 1-1 2-1 3-1 0-2 1-2 1l-4 1z" class="C"></path><path d="M396 525h5l1 1h0c-2 0-3 0-5 1-6 2-8 5-10 11-3 7-3 16 0 23 1 1 1 1 1 2h-1c-2-4-4-7-5-11-1-3-1-8 0-12h0c1-4 3-9 6-11h1c2-2 5-3 7-4z" class="D"></path><defs><linearGradient id="BC" x1="446.043" y1="425.706" x2="437.91" y2="419.864" xlink:href="#B"><stop offset="0" stop-color="#212122"></stop><stop offset="1" stop-color="#424141"></stop></linearGradient></defs><path fill="url(#BC)" d="M457 410h1c-1 3-7 5-10 8-2 2-5 4-8 7-2 2-4 5-5 9l-3 3h-4-1v-1c3-2 5-6 6-10 5-10 15-13 24-16z"></path><defs><linearGradient id="BD" x1="459.586" y1="400.989" x2="449.637" y2="393.664" xlink:href="#B"><stop offset="0" stop-color="#1d1d1d"></stop><stop offset="1" stop-color="#444343"></stop></linearGradient></defs><path fill="url(#BD)" d="M464 390h1v-1h1v1 1c-1 1-1 1-1 2h0v1c0 1 0 2-1 3-2 1-3 1-5 2-4 5-4 11-12 10-2 0-3-1-3-2-1-1-1-2-1-3s1-4 3-5c2-2 6-3 8-4-1-2-2-4-1-7h0c2 1 3 3 5 4h1 2c0 1-1 1-2 1v1c2 0 3-2 4-3h0l1-1h0z"></path><path d="M450 401h1v1c-1 1-1 3-1 5h-1c-1 0-1 0-2-1v-2c1-2 2-2 3-3z" class="N"></path></svg>