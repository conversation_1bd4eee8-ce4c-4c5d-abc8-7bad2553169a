<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="76 20 564 672"><!--oldViewBox="0 0 704 752"--><style>.B{fill:#020202}.C{fill:#afadae}.D{fill:#b8b7b7}.E{fill:#383838}.F{fill:#0d0d0d}.G{fill:#8a898a}.H{fill:#9b9a9b}.I{fill:#c1c0c0}.J{fill:#1e1d1e}.K{fill:#828182}.L{fill:#4e4e4e}.M{fill:#252425}.N{fill:#d1d0d0}.O{fill:#606060}.P{fill:#a7a6a6}.Q{fill:#2e2d2e}.R{fill:#5b5b5b}.S{fill:#444344}.T{fill:#767575}.U{fill:#3f3f3f}.V{fill:#171717}.W{fill:#929191}.X{fill:#6b6a6b}.Y{fill:#dfdede}.Z{fill:#cbcaca}.a{fill:#545354}.b{fill:#d7d6d6}.c{fill:#7c7b7c}.d{fill:#a2a1a1}.e{fill:#656465}.f{fill:#29292a}.g{fill:#494849}.h{fill:#323232}.i{fill:#706f70}.j{fill:#efeeef}.k{fill:#c7c6c6}.l{fill:#e7e6e6}.m{fill:#f7f7f7}</style><path d="M206 510c2 0 3-1 4-1l1 1v1c-2 0-4 0-5-1h0z" class="P"></path><path d="M211 535h-2l-1-1c1-1 2-1 2-2h2l-1 3z" class="O"></path><path d="M215 525c1-2 2-3 4-4l2 2c-1 0-2 1-3 2h-2-1zm-15-20v-1h1c2 2 3 4 5 6h0c-3-1-5-3-6-5z" class="F"></path><path d="M105 192l1-1c1 3 0 8-1 11-1-2 0-7 0-10z" class="l"></path><path d="M84 344c1-2 2-3 4-3 1 0 1 0 2 1 1 0 1 0 1 1-2 0-2-1-4 1l-1 1h-1l-1-1z" class="M"></path><path d="M227 589c0-1 1-2 2-2 1 1 1 3 3 4-1 1-1 1-2 1-2-1-2-2-3-3z" class="f"></path><path d="M212 532c1 0 3-1 4 0 1 0 1 0 1 1v1c-2 0-4 0-6 1l1-3z" class="X"></path><path d="M632 244c1 3 1 6 2 9v1c-2-1-2-2-3-3l-1-2c1 0 1 0 2-1s0-3 0-4z" class="O"></path><path d="M245 149c1 0 3-1 4-1l-4 4-3 1h-1c1-2 2-3 4-4z" class="X"></path><path d="M391 93l1-4h1v5c0 1 1 3 0 4l-2 1 1-3-1-3z" class="G"></path><path d="M391 93l1-4h1v5-1h-1v3l-1-3z" class="d"></path><path d="M326 124l3 6 1 3-1-1-3-3 1-1-2-3 1-1z" class="R"></path><path d="M254 589h0c-2 3-5 6-9 8 1-2 3-4 5-6 1-1 2-2 4-2z" class="K"></path><path d="M428 627c-2 1-4 2-6 1s-2-2-3-3h1c2 1 3 1 6 0h0l2 2z" class="E"></path><path d="M261 599c0 1 1 2 1 2v1l-1 3c0 1 0 1 1 2v2c-2-2-3-5-4-8h2 0c1 0 1-1 1-2z" class="d"></path><path d="M261 599c0 1 1 2 1 2v1l-1 3c0-1-1-3-1-4 1 0 1-1 1-2z" class="g"></path><path d="M121 59c-1-2-1-5-1-7h1c0 4 1 7 3 10l-2 1c-1-1-1-2-1-4z" class="e"></path><path d="M195 496h2l4 8h-1v1c-2-1-4-7-5-9z" class="J"></path><path d="M428 613h2c1 2 3 4 2 7 0 2-1 4-2 5v-4c1-3 0-5-2-8z" class="D"></path><path d="M607 234c-1 2-3 4-5 5l-3 2c-2-2-4-1-6-3h7c3 0 5-2 7-4z" class="h"></path><path d="M413 74c-1-3-4-5-6-7 4 1 7 3 10 6h-2v2l-2-1z" class="T"></path><path d="M469 542h1c1 4 2 9 3 13v1l-1-1h0l-2-5c0-1-1-4-1-5v-3z" class="F"></path><path d="M409 91h1l2 2v1h1v1l2-1 1 1c-1 1-1 1-3 2-1 0-3 0-4-1v-2-3z" class="e"></path><path d="M409 91h1l2 2v1c-1 1-1 2-2 2l-1-2v-3z" class="J"></path><path d="M636 260c-1-1-2-3-3-4-1-2-3-3-4-5h2c1 1 1 2 3 3v-1h0l4 6c-1 1-1 1-2 1z" class="H"></path><path d="M125 198l2 1c1 1 1 2 2 3v4c1 2 2 3 2 5h-1c-2-4-4-8-5-13z" class="e"></path><path d="M442 589l1 1c1 1 2 2 2 4l1-1h0v6l-1 1c-1-4-3-7-3-11z" class="Q"></path><path d="M509 499c1-2 3-6 5-8h1c-2 5-4 10-7 14l-1-1 2-5z" class="J"></path><path d="M420 80c2 4 1 8-1 12l-3 3-1-1-2 1v-1l2-1c2-2 3-3 4-6 1-2 2-4 1-7z" class="K"></path><path d="M410 617c1-1 2-2 4-3 0 2 1 2 2 2l-6 6v-5z" class="i"></path><path d="M362 41l3-2c0 3 1 12-1 14-2-1 0-10-2-12z" class="g"></path><path d="M249 144l2-3c2 1 3 1 5 1l-3 2-4 4c-1 0-3 1-4 1l4-5z" class="a"></path><path d="M249 144l2-3c2 1 3 1 5 1l-3 2c-1 0-2 0-3 1h0l-1-1z" class="U"></path><path d="M496 529l4 6-8-1 1-1-1-1v-3h1 3z" class="g"></path><path d="M239 537l1 2h0c0 3-1 6-1 9h1v1c-1 2-2 4-1 5l-2 1h-2s1-2 1-3c2-5 3-10 3-15z" class="T"></path><path d="M239 548h1v1c-1 2-2 4-1 5l-2 1c0-2 1-5 2-7z" class="G"></path><path d="M215 525h1 2 0l-3 3c0 1 2 2 3 3h1 1c-1 1-2 2-3 2 0-1 0-1-1-1-1-1-3 0-4 0h-2c2-3 3-5 5-7z" class="U"></path><path d="M619 279l1 1v-1l4-2v1c-2 3-3 7-2 10l1 2h-2c-1-3-3-6-4-10l2-1z" class="J"></path><defs><linearGradient id="A" x1="607.645" y1="205.866" x2="610.959" y2="200.447" xlink:href="#B"><stop offset="0" stop-color="#2e2e34"></stop><stop offset="1" stop-color="#41403d"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M606 197l5 3-2 1c1 1 1 2 1 2 1 1 3 4 3 5l1 2s-1 1-2 1v-1l-2-3c-2-3-3-6-4-10z"></path><path d="M612 210c0-1 0-1 1-2l1 2s-1 1-2 1v-1z" class="M"></path><path d="M127 98c1 1 3 3 4 5 2 2 4 5 6 7 1 2 1 3 1 5h0v-1c-5-3-10-10-11-16z" class="H"></path><path d="M507 499l1-1 1 1-2 5 1 1c-2 3-4 4-7 5v-1c1-2 2-5 3-7h1 0c1-1 2-2 2-3z" class="a"></path><path d="M515 481c1 3 2 7 0 10h0-1c-2 2-4 6-5 8l-1-1c2-4 5-9 6-14 0-1 1-2 1-3z" class="e"></path><path d="M420 611c2-1 3-1 5 0 1 0 2 1 3 2 2 3 3 5 2 8v4l-2 2-2-2c1-1 3-3 3-5s0-4-1-5c-2-2-5-3-7-3l-1-1z" class="O"></path><path d="M103 193c0 1-1 4-1 4-3 1-8 6-10 8-1 1-2 2-2 3-1 0-2 1-3 1h-1c5-6 11-11 17-16z" class="N"></path><path d="M464 567l1-1c0 7 1 14 5 21h-2c-3-2-4-9-5-12v-5c-1-1-1-1 0-1v-1l1 2v2-2-1-2z" class="Q"></path><path d="M119 44l1-1c0 2 1 7 1 9h-1c0 2 0 5 1 7l-1 1v-1-1 2h-1 0v-3l-1 1c-1-3 0-7-1-9l2-5z" class="Z"></path><path d="M119 44s1 0 0 1c-1 3 0 9 0 12l-1 1c-1-3 0-7-1-9l2-5z" class="R"></path><path d="M208 494h10 1l1 1c-1 0-1 0-2 1-2 0-5-1-7 1-2 1-3 3-4 5-1-2 0-5 0-7l1-1z" class="V"></path><path d="M277 623v-1l2 2c0-1-1-5-1-7 1-2 3-4 5-5s3-2 6-1h0l-1 1c-3 0-5 1-7 2-2 2-2 3-2 5s1 4 3 5c1 1 3 2 4 2s2-1 3-1h0c-1 2-2 2-4 3-2 0-4-1-6-2-1-1-2-2-2-3z" class="E"></path><path d="M418 608v-1c3-1 6-1 8 0 0 1 0 2 2 4l2 2h-2c-1-1-2-2-3-2-2-1-3-1-5 0l-1 1-3-3h1c1 0 1-1 1-1z" class="l"></path><path d="M287 80c1 1 1 1 1 2v3c1 2 3 4 4 5l2 3 2 1c-1 1-2 1-3 1l-1 1c-1-1-2-2-3-4-3-4-3-7-2-12z" class="c"></path><path d="M631 222l2 5c1 3 0 7 0 10h0-1c-1 3-2 6-2 9h-1l-1-1 1-4 1-4c1-3 1-6 1-10v-5z" class="D"></path><path d="M606 197v-1c0-1-1-2-2-3l-1-1c0 2 0 5 1 7 0 1 1 5 1 6-2-2-3-8-3-10v-3l2 1c3 1 5 3 7 4 4 3 7 6 9 9h-1l-2-1s-5-4-6-5l-5-3z" class="Z"></path><path d="M360 41h0 2c2 2 0 11 2 12h0c-1 1-1 1-2 1v1h0-3c0-2 0-3-1-5v-1c0-2 0-3 1-5 0-1 0-1 1-2h0v-1z" class="l"></path><path d="M343 57c1-1 3-1 3-2h0c-1-1-1-1-1-2-2-3-1-8-1-10s-1-3 0-5l1 2c2 2 6 1 9 1-2 1-3 0-4 0h-3v2c1 3 1 7 1 11h1c0 2 0 2 1 3v2 1h-1v-2c0-1 0-1-1-1-1-1-3-1-4 0h-1 0z" class="b"></path><path d="M497 152c2 2 3 4 4 5l5 7 6 9c2 3 4 8 8 10h0l-2 1h0l-3-3c-1-1-2-3-3-4l-5-8c-1-2-2-4-4-6l-1-1 1-1h-1l-1-1h0v-1 1l-1-1v-2c-1 0-1-1-2-2v-1c-1-1-1-1-1-2z" class="N"></path><defs><linearGradient id="C" x1="259.022" y1="129.036" x2="257.448" y2="141.643" xlink:href="#B"><stop offset="0" stop-color="#0d060b"></stop><stop offset="1" stop-color="#303633"></stop></linearGradient></defs><path fill="url(#C)" d="M266 127h3c1 0 1 0 1 1h0c-2 1-3 2-4 4v1l-1-1h-1c-1 1-5 5-6 7l-2 3c-2 0-3 0-5-1 5-5 10-10 15-14z"></path><defs><linearGradient id="D" x1="474.341" y1="589.497" x2="463.265" y2="597.38" xlink:href="#B"><stop offset="0" stop-color="#111011"></stop><stop offset="1" stop-color="#353435"></stop></linearGradient></defs><path fill="url(#D)" d="M465 591c3 3 5 4 10 4 1 0 3-1 4-1l2-2v1c-1 3-4 4-7 5-4 0-8 0-12-2l-2-2v-2h0c1 0 2 1 3 1h0 1l1-2z"></path><path d="M288 77c3-5 8-8 13-10l-3 3c-1 1-2 1-2 1-2 1-4 5-4 6-1 3-1 7 0 10 1 2 2 3 3 4l-1 2-2-3c-1-1-3-3-4-5v-3c0-1 0-1-1-2l1-3z" class="L"></path><path d="M288 77c1 1 1 3 1 4l-1 1c0-1 0-1-1-2l1-3z" class="i"></path><path d="M251 124c4-2 7-5 11-8 0 1-1 2-2 2l-1 1c-1 1-3 2-4 4-1 0 0-1-1 0l-1 1-2 2s0-1-1 0v1l-16 13 1 1h0c-2 2-4 4-6 5-1 0-3 2-4 3v2h1c1-1 0 0 1 0 1-1 2-1 3-1 0-1 1-1 1-1 2-1 3-2 4-2 0 1 0 1-1 1 0 1 1 0-1 1h0l-3 2c-1 0-2 0-4 1h-1-2-1v1h0l-1 1c0-1-1-3 0-5h2 1l7-7 13-12c2-2 5-3 7-6z" class="Y"></path><path d="M553 410v1h0c3 1 4-2 6-4h0c0 3 0 6 1 9 0 3 1 6 3 8 0 1 1 2 2 3-5-3-11-8-12-13 0-2-1-3 0-4z" class="L"></path><path d="M401 97h0l-4 1c0-4-1-9-1-14h9c1 0 2 0 3 1v3l1 1h0l-1 1c-1-1 0-3-1-4-2-1-7-1-9 0h0l1 2c1 0 3 0 4 1h0c0 2 1 5 1 7-1 1-2 1-3 1z" class="b"></path><path d="M401 97h-1v-4-4h0 3c0 2 1 5 1 7-1 1-2 1-3 1z" class="g"></path><path d="M149 407c1 2 3 3 5 4h2 0v2c-2 7-7 11-12 14 1-2 2-3 3-5 2-4 2-10 2-15z" class="R"></path><defs><linearGradient id="E" x1="82.351" y1="235.159" x2="74.763" y2="231.504" xlink:href="#B"><stop offset="0" stop-color="#8f8d8e"></stop><stop offset="1" stop-color="#bbb"></stop></linearGradient></defs><path fill="url(#E)" d="M79 221h1 0 0v3c-1 2-2 4-1 7v4c0 1 0 3 1 4v3h-1c1 1 1 2 2 3-1 2-2 5-3 7l-2 2c-1-1 0-3 0-4 1-5 1-9 0-13v-9c1-2 2-5 3-7z"></path><path d="M79 235c0 1 0 3 1 4v3h-1v2c-1-2-1-4-1-6h0c0-1 1-2 1-3z" class="P"></path><path d="M79 242c1 1 1 2 2 3-1 2-2 5-3 7l-2 2c-1-1 0-3 0-4h1c1-1 1-4 2-6v-2z" class="Z"></path><path d="M367 150v-4c-1-8 2-18 6-25 1-1 2-2 3-4 2-2 5-4 7-6l1-1 1-1 2-1c2-1 5-2 7-3l4-1h2c1 0 1 0 2-1h1 4 8c1 1 2 0 2 1h2 1c1 0 1 0 2 1h-2-2c-1-1-2 0-4-1h-3-6c-2 0-4 0-5 1h-1c-1 0-2 0-3 1h-1-1-1l-3 2-3 1-2 1-3 3c-6 5-10 12-12 19-1 3-2 6-2 9v7 3l-1-1z" class="b"></path><defs><linearGradient id="F" x1="235.195" y1="568.566" x2="246.213" y2="583.243" xlink:href="#B"><stop offset="0" stop-color="#1c1b1c"></stop><stop offset="1" stop-color="#4b4a4c"></stop></linearGradient></defs><path fill="url(#F)" d="M243 561h3l1 1s1 1 2 1c-1 1-2 1-2 2h1s-1 0-1 1c-2 2-1 7-2 10 0 3-2 7-3 9-1 1-1 2-1 3-2 2-3 4-6 4l-1 1c0-1 0-1 1-2h-1 0c2-1 3-2 4-4 1-1 2-3 3-5 3-7 3-13 2-21z"></path><path d="M281 607h1c1 0 1-1 2-1 2 0 5 0 7 1 1 0 1 1 2 2-2 0-3 0-4 2-3-1-4 0-6 1s-4 3-5 5c0 2 1 6 1 7l-2-2v1l-1-1c-2-3 1-9 3-12 1-1 2-2 2-3z" class="b"></path><path d="M295 91c-1-1-2-2-3-4-1-3-1-7 0-10 0-1 2-5 4-6 0 0 1 0 2-1-2 4-4 8-3 12 0 3 2 5 4 7l2 1c0 1 0 2-1 3-1 0-1 0-2 1h-2 0l-2-1 1-2z" class="C"></path><path d="M295 91l2 2 1 1h-2 0l-2-1 1-2z" class="E"></path><path d="M297 93c1-1 1-2 1-4h0 1l2 1c0 1 0 2-1 3-1 0-1 0-2 1l-1-1z" class="U"></path><path d="M305 181h1 2l1-1h1 0 2v-1c1 0 2-1 3-1s1 0 2-1c1 0 2 0 3-1 0 0 1 0 2-1h0c2-1 3-3 4-4 3-1 5-4 6-7 1-1 2-3 3-4v1h0v1c0-1 0-2 1-2s0 3-1 4v1h0c0 1-1 1-1 1 0 1-1 2-2 3v1c1-1 2-1 2-2l1-1h0v2c-2 2-3 3-5 4s-3 2-4 3c-4 2-7 3-11 4-3 1-6 2-10 2v-1z" class="b"></path><path d="M325 174h2 0c1 0 0 0 1-1h2c-2 1-3 2-4 3-4 2-7 3-11 4 2-2 3-2 6-3 1-1 2-2 4-3z" class="D"></path><path d="M325 174l6-6 1-2 2-2c-1 1-2 4-2 5v1c1-1 2-1 2-2l1-1h0v2c-2 2-3 3-5 4h-2c-1 1 0 1-1 1h0-2z" class="Z"></path><path d="M117 49c1 2 0 6 1 9l1-1v3c0 4 1 7 2 10-1 1-1 3-2 4h-1v-1c0-1 0-1-1-2h0 0c-1 0-1 1-1 1v1c-2-8-3-17 1-24z" class="X"></path><path d="M617 232c4 2 8 5 11 9h1l-1 4 1 1h1c0-3 1-6 2-9h1l-1 7c0 1 1 3 0 4s-1 1-2 1-1-1-2-2c-2-3-6-5-9-8-1-1-2-1-4-2h0l-1-2h1 2v-3z" class="L"></path><path d="M617 232c4 2 8 5 11 9h1l-1 4c-3-3-6-5-9-7-1-1-2-2-3-2l-1 1-1-2h1 2v-3z" class="W"></path><path d="M249 123h1c2-2 4-3 7-5 14-9 32-17 50-15 10 2 19 7 26 15v1h-1 0c-1-2-3-4-5-5-5-5-11-7-18-9-4-1-8-1-12-1-13 2-23 5-35 12-4 3-7 6-11 8h0c0-1 1-1 1-1 1-1 1 0 1-1l2-1c-1-1-4 2-5 2h-1z" class="P"></path><defs><linearGradient id="G" x1="321.98" y1="102.614" x2="336.556" y2="101.203" xlink:href="#B"><stop offset="0" stop-color="#a6a4a5"></stop><stop offset="1" stop-color="#c3c2c3"></stop></linearGradient></defs><path fill="url(#G)" d="M321 94v-1h-1v1c0-1-1-3 0-4l1 1h1c1 0 2 0 2 2 1 1 1 2 2 3 0 1 2 4 3 5h0c1 1 1 3 3 4 1 1 2 1 2 1 2 1 2 2 4 2l-1 2c1 0 2 1 2 1-1 1-1 1-1 2s0 1 1 2h1l-2 1c-2-3-4-6-7-9-2-2-5-3-8-5v-1h-1l-1-7z"></path><path d="M321 94h1l2 7h-1-1l-1-7z" class="G"></path><defs><linearGradient id="H" x1="389.667" y1="636.643" x2="387.288" y2="653.056" xlink:href="#B"><stop offset="0" stop-color="#393739"></stop><stop offset="1" stop-color="#737473"></stop></linearGradient></defs><path fill="url(#H)" d="M386 644l-1 1h0l2-1 1-1c1-1 1-2 2-3h0c3-2 4-3 7-3l-15 18h-1c1-1 1-2 2-3h0-1c-1 0-2 1-3 2h0v-1h-1c-1 0-2 1-3 1l2-4c1-2 3-3 3-6h1v1h0 2l-1 1 1 1 3-3z"></path><path d="M381 644v1h0 2l-1 1 1 1c-1 1-4 2-6 3 1-2 3-3 3-6h1z" class="E"></path><defs><linearGradient id="I" x1="386.668" y1="631.175" x2="407.579" y2="633.868" xlink:href="#B"><stop offset="0" stop-color="#2c282b"></stop><stop offset="1" stop-color="#404341"></stop></linearGradient></defs><path fill="url(#I)" d="M410 617v5l-13 15c-3 0-4 1-7 3h0c-1 1-1 2-2 3l-1 1-2 1h0l1-1s1-2 2-2v-1h0l3-4 19-20z"></path><defs><linearGradient id="J" x1="244.244" y1="574.323" x2="250.395" y2="576.732" xlink:href="#B"><stop offset="0" stop-color="#545354"></stop><stop offset="1" stop-color="#767676"></stop></linearGradient></defs><path fill="url(#J)" d="M241 588c0-1 0-2 1-3 1-2 3-6 3-9 1-3 0-8 2-10 0-1 1-1 1-1 1 1 2 1 2 3l1 6v2c1 1 2 1 1 3l-1 1-5 6v-1c-2 0-4 1-5 3z"></path><defs><linearGradient id="K" x1="123.367" y1="242.443" x2="107.576" y2="231.301" xlink:href="#B"><stop offset="0" stop-color="#232324"></stop><stop offset="1" stop-color="#3e3c3d"></stop></linearGradient></defs><path fill="url(#K)" d="M123 233c1 2 3 3 6 4h0c-6 4-12 5-19 5-3-2-9-5-10-8 4 2 7 3 11 3s7-1 11-3l1-1z"></path><path d="M111 237c4 0 7-1 11-3 0 1 0 2-1 2-1 1-4 2-6 2l-1-1h-3z" class="E"></path><defs><linearGradient id="L" x1="622.78" y1="254.121" x2="627.36" y2="242.922" xlink:href="#B"><stop offset="0" stop-color="#807f80"></stop><stop offset="1" stop-color="#999797"></stop></linearGradient></defs><path fill="url(#L)" d="M615 237c2 1 3 1 4 2 3 3 7 5 9 8 1 1 1 2 2 2l1 2h-2c1 2 3 3 4 5 1 1 2 3 3 4-1 1-3 2-4 3v-2h0c0-1 0-2-1-2 0-2-6-7-7-9l-1-1c-1-1-3-4-5-4h-1c1-1 1-1 2-1h0c-1-1-1-2-2-2-1-1-1-3-2-5z"></path><path d="M619 239c3 3 7 5 9 8h-2c-1-1-1-1-2-1 0 0-1-1-1-2h-2c-1-1-2-3-2-5z" class="K"></path><path d="M463 575c1 3 2 10 5 12h2c1 2 3 3 6 4 1 0 1 0 1-1 1-1 1-2 1-3h1c1 0 1 1 2 2s1 2 1 4h-1v-1l-2 2c-1 0-3 1-4 1-5 0-7-1-10-4-2-2-3-4-3-6v-1c1 0 1 0 1 1v-8-2z" class="S"></path><path d="M481 589c1 1 1 2 1 4h-1v-1l-2 2c-1 0-3 1-4 1v-1h-1 2c3-1 4-3 5-5z" class="X"></path><path d="M463 577c0 3 2 6 3 10 1 2 1 3 2 5h1v1c-3-2-4-5-6-8v-8z" class="a"></path><path d="M462 585v-1c1 0 1 0 1 1 2 3 3 6 6 8 2 0 3 1 5 1h1v1c-5 0-7-1-10-4-2-2-3-4-3-6z" class="T"></path><path d="M415 75v-2h2c1 2 3 4 3 7 1 3 0 5-1 7-1 3-2 4-4 6l-2 1h-1v-1l-2-2h-1l-1-1 1-1h0l3-3c3-4 2-7 1-12l2 1z" class="O"></path><path d="M410 91h0 3l2 2-2 1h-1v-1l-2-2z" class="g"></path><path d="M413 74l2 1c1 3 2 6 1 9-1 2-2 3-3 3-1 2-2 2-4 2h0l3-3c3-4 2-7 1-12z" class="I"></path><path d="M446 593c0-2 1-4 2-6h0c0-3 0-5 1-7 1 1 2 1 3 2 3 2 6 7 8 10v2c-2-1-4-4-6-6-1 2-2 5-2 7-1 3-2 6-4 8 0 1 0 2-1 2v-1l-2-4 1-1v-6z" class="g"></path><path d="M449 594v-5c0-3-1-5 1-7l1 1 1 1c-1 0-1 1-1 1-1 0-1 3-1 4 0 2-1 3-1 5z" class="O"></path><path d="M449 594c0-2 1-3 1-5 0-1 0-4 1-4 0 0 0-1 1-1 1 1 1 3 2 4-1 2-2 5-2 7-1 3-2 6-4 8 0-3 1-6 1-9z" class="c"></path><defs><linearGradient id="M" x1="371.621" y1="149.923" x2="359.83" y2="148.821" xlink:href="#B"><stop offset="0" stop-color="#868587"></stop><stop offset="1" stop-color="#b8b6b6"></stop></linearGradient></defs><path fill="url(#M)" d="M363 129h1v2c-2 10-1 23 5 31 1 1 1 2 2 3h0l-1 1c0 1 1 3 2 4l1 1s-1 0-1 1c-1-1-2-1-3-2-3-4-6-7-8-11h-1l-1-3 3-3c-4-7-1-16 1-24z"></path><path d="M362 153v1c0 1 1 2 2 3 0 1 0 2 1 3l-1 1c-1-1-1-1-3-2h0-1l-1-3 3-3z" class="D"></path><defs><linearGradient id="N" x1="629.689" y1="305.1" x2="616.725" y2="300.025" xlink:href="#B"><stop offset="0" stop-color="#797878"></stop><stop offset="1" stop-color="#908f90"></stop></linearGradient></defs><path fill="url(#N)" d="M623 290c1 2 1 3 2 5 3 6 4 10 3 17-2 10-6 16-15 22l-9 6v-1-1c5-2 10-5 14-9s8-9 8-15c-1-5-3-9-7-13v-7c0-2 0-2 1-3l2 1h0l-1-2h2z"></path><defs><linearGradient id="O" x1="370.9" y1="136.784" x2="358.725" y2="129.615" xlink:href="#B"><stop offset="0" stop-color="#c2c0c1"></stop><stop offset="1" stop-color="#e5e5e5"></stop></linearGradient></defs><path fill="url(#O)" d="M367 115c2-1 3-2 4-3 1 3-3 8-4 11h0c-1 3-2 5-3 8v-2h-1c-2 8-5 17-1 24l-3 3c-1-5-3-12-2-17h0l1-7c1-6 6-12 9-17z"></path><path d="M363 129c0-2 1-5 3-7l1 1-3 8v-2h-1z" class="k"></path><path d="M81 245c4-4 8-7 12-10l1 1-1 2c-1 1-1 2-1 4v1c-1 0-1 1-2 2s-4 4-4 6h0c-2 2-6 6-7 9-1-1-2-1-4 0 0 0-1 0-1 1h0l-2-1c0-2 3-4 4-6l2-2c1-2 2-5 3-7z" class="P"></path><path d="M81 245c4-4 8-7 12-10l1 1-1 2c-3 2-7 5-10 8-2 2-3 4-5 6h0c1-2 2-5 3-7z" class="f"></path><path d="M470 93c0 2-2 3-3 4-2 2-6 5-6 8l-2 1 1 1 1-2v2h0l-1 1c0 1-1 3 1 4 0 1 1 1 1 1l1 1c1 1 2 1 4 1 0 1-1 3-2 4h-1v1c-1-1-3-2-4-3l-12-8c6-7 14-11 22-16z" class="R"></path><path d="M301 90v-5c0-1 1-1 1-1h10 0v1 8c0 1-1 3 0 5h-3c-1 0-1-1-2-1h-2-5-4c-2 0-3 0-4-1l1-1c1 0 2 0 3-1h0 2c1-1 1-1 2-1 1-1 1-2 1-3z" class="j"></path><path d="M298 94c1-1 1-1 2-1v3c-2 1-3 1-4 1-2 0-3 0-4-1l1-1c1 0 2 0 3-1h0 2z" class="O"></path><path d="M298 94c1-1 1-1 2-1v3h-2c-1 0-1-1-2-2h2z" class="L"></path><path d="M305 97v-1c0-2 0-5 1-7h2c1 1 0 6 0 7l-1 1h-2z" class="f"></path><defs><linearGradient id="P" x1="325.316" y1="651.13" x2="347.709" y2="677.543" xlink:href="#B"><stop offset="0" stop-color="#6f6e6f"></stop><stop offset="1" stop-color="#959495"></stop></linearGradient></defs><path fill="url(#P)" d="M323 650h0l1 1c1-1 3 0 3-1 1 0 1 0 2-1l4 6 1 2s1 1 1 2l3 4h0c-1-1-2-1-2-2v2c1 0 1 1 1 1 2 3 4 5 5 8l7 12c2 4 4 7 5 11h0 1v-1c1-2 2-4 4-6l4-6v-1h0l2-2h0l-10 18c-6-12-15-24-22-34l-10-13z"></path><path d="M334 660h0v-1c-1 0-2-2-2-3h1l1 1s1 1 1 2c-1 0-1 0-1 1z" class="G"></path><path d="M334 660c0-1 0-1 1-1l3 4h0c-1-1-2-1-2-2v2c1 0 1 1 1 1-1-1-2-3-3-4z" class="H"></path><path d="M351 141v-1c1 1 1 2 2 2l3-3h1c-1 5 1 12 2 17l1 3h0c-2 0-2 2-3 4l-2 1-1 1-1-1h-1l-2-4h0l-2-2c3-5 3-12 3-17z" class="F"></path><path d="M266 127c9-8 22-15 33-15 8-1 17 2 23 7-1 1-3 0-4 0l-4-2c-10-3-20-2-30 2l-14 9c0-1 0-1-1-1h-3z" class="B"></path><defs><linearGradient id="Q" x1="521.675" y1="421.311" x2="534.803" y2="432.681" xlink:href="#B"><stop offset="0" stop-color="#4a4949"></stop><stop offset="1" stop-color="#707070"></stop></linearGradient></defs><path fill="url(#Q)" d="M520 430v-1c0-2 1-4 1-6h1c0-1 0-1 1-2 1-2 1-3 2-4s1-1 3-1c1 0 1 0 1 2h1v-1 1l1 1v9 15c-1-2-11-11-11-13z"></path><path d="M350 59v-2c-1-1-1-1-1-3h-1c0-4 0-8-1-11v-2h3c1 0 2 1 4 0 2 0 4 1 6 0v1h0c-1 1-1 1-1 2-1 2-1 3-1 5v6c0 1-1 2 0 4 1 1 3 0 5 1 7 2 13 7 18 12h-1l-4-4c-4-3-9-6-14-7-4-2-7-2-12-1v-1z" class="F"></path><path d="M360 41v1h0c-1 1-1 1-1 2-2 0-7 0-9 1 0-1 0-1-1-1h-1v-2l2-1c1 0 2 1 4 0 2 0 4 1 6 0z" class="P"></path><path d="M350 59v-2c-1-1-1-1-1-3h-1c0-4 0-8-1-11v-2h3l-2 1v2h1c1 0 1 0 1 1 0 4 1 10 0 14z" class="D"></path><path d="M289 611c2 2 4 2 7 2h-1c0 1 1 2 2 3 7 5 12 12 18 19l4 4c3 3 7 6 10 10-1 1-1 1-2 1 0 1-2 0-3 1l-1-1h0l-34-37-1-1 1-1z" class="L"></path><path d="M289 611c2 2 4 2 7 2h-1c0 1 1 2 2 3h-1c-2 0-3-1-5-2-1-1-1-1-2-1l-1-1 1-1z" class="H"></path><defs><linearGradient id="R" x1="99.484" y1="340.755" x2="109.704" y2="365.959" xlink:href="#B"><stop offset="0" stop-color="#333132"></stop><stop offset="1" stop-color="#6a6a6a"></stop></linearGradient></defs><path fill="url(#R)" d="M84 344l1 1h1c-1 4 0 7 2 10s7 5 11 5c4 1 9 0 13-2h2 1 7c1-1 3 0 4-1h0c0 1-1 1-1 1h0l-3 3h1c1-1 1-1 2-1-9 5-18 7-28 4l-2-1v1h0c-4-2-9-6-11-10-1-4-1-7 0-10z"></path><path d="M358 49v1c1 2 1 3 1 5h3c11 4 23 13 28 24 1 2 2 4 2 6 0 1 1 3 1 4h-1l-1 4 1 3-1 3-2 1c1-7 0-15-2-20-2-3-4-7-6-8-5-5-11-10-18-12-2-1-4 0-5-1-1-2 0-3 0-4v-6z" class="N"></path><path d="M391 93v-7-1h1c0 1 1 3 1 4h-1l-1 4z" class="C"></path><defs><linearGradient id="S" x1="187.404" y1="429.141" x2="178.846" y2="428.649" xlink:href="#B"><stop offset="0" stop-color="#555454"></stop><stop offset="1" stop-color="#6d6c6c"></stop></linearGradient></defs><path fill="url(#S)" d="M177 416l1-2c0 1 0 1 1 2 0-1 1-2 2-1 1 0 3 1 4 2s1 3 1 5 1 3 2 4 1 3 2 4l-1 1c-4 4-7 7-11 10l-1 1h0v-26z"></path><path d="M177 416l1-2c0 1 0 1 1 2 0-1 1-2 2-1 1 0 3 1 4 2s1 3 1 5 1 3 2 4 1 3 2 4l-1 1h-1c0-1-1-2-1-4-2-3-3-9-7-10-1 1-1 1-1 3l-1 21-1 1h0v-26z" class="S"></path><path d="M329 130c1 0 2 1 2 2v2l1 4c0 1 1 2 1 3h0l1 1v3c0 1 1 1 0 2v5c0 1 0 2-1 3v2c-1 1-1 2-1 3h0c0 1-1 2-1 2 0 1-1 2-1 3h-1c0 1 0 1-1 2s0 0 0 1c-1 1-4 3-5 4l-1 1-1 1-3 2h0c-1 0-2 1-3 1l-2 1h0c-1 0-1 0-2 1h-2v-1h-2c5-1 9-3 13-6l1-2h1-5l1-1 3-1v-1l3-3c1-1 1-2 2-2l-1-2-6-3 5 1h1c1 0 2-1 3-2l-1-1h-2 0 1c2-1 2-1 3-2s1-3 1-5-2-3-4-4c1 0 3-1 4-2h1c0-1-1-1-1-2l-1-8 1 1-1-3z" class="N"></path><path d="M332 150l1-8c0 3 1 6 0 10v-4l-1 2z" class="I"></path><path d="M332 150l1-2v4 1l-1-1h0v1c-1 0-1 1-1 2h-1v-1l1-1v-1h0c0-1 0-1 1-2z" class="D"></path><path d="M329 132l1 1c1 2 2 7 1 9h0c0-1-1-1-1-2l-1-8z" class="E"></path><path d="M331 155c0-1 0-2 1-2v-1h0l1 1c-1 4-2 9-5 12v-2c1-2 2-5 3-8z" class="d"></path><path d="M330 155h1l-3 8v2c-4 7-11 11-19 13h-2c5-1 9-3 13-6l1-2h1-5l1-1 3-1v-1l3-3c1-1 1-2 2-2l-1-2c2 0 4-3 5-5z" class="C"></path><path d="M321 168h1c2 0 2-2 3-1-1 2-3 4-5 5l1-2h1-5l1-1 3-1z" class="I"></path><path d="M249 123h1c1 0 4-3 5-2l-2 1c0 1 0 0-1 1 0 0-1 0-1 1h0c-2 3-5 4-7 6l-13 12-7 7h-1-2c-1 2 0 4 0 5l-12 15v1c-1 0-4 3-4 3l-1 3h0c-1 0-1 1-1 1v1c-1 2-1 3-2 4s-2 1-3 2c-2 1-3 3-4 4l-1-1c4-4 7-10 10-14 5-7 10-15 16-22 5-7 12-13 19-19 3-3 7-6 11-9z" class="D"></path><path d="M256 579h2v3-1c1 0 1 0 2-1-1 2-1 3-2 4h-1c-1 2-2 3-3 5-2 0-3 1-4 2-2 2-4 4-5 6-3 1-6 2-9 1-4-1-6-2-9-4 0-1-1-1-1-2 0-2 0-2 1-3 1 1 1 2 3 3 1 0 1 0 2-1h2 1c-1 1-1 1-1 2l1-1c3 0 4-2 6-4 1-2 3-3 5-3v1l5-6c1 2 0 3 0 5 1-2 3-4 5-6z" class="L"></path><path d="M241 593c1 1 3-1 5-2-2 2-3 4-5 5-2 0-3 0-5-1 2 0 3-1 5-2z" class="V"></path><path d="M227 594l1-1h0c2 1 4 2 6 2h2c2 1 3 1 5 1-1 0-2 1-2 1-1 1-2 1-3 1-4-1-6-2-9-4z" class="Q"></path><defs><linearGradient id="T" x1="254.246" y1="580.991" x2="256.585" y2="585.197" xlink:href="#B"><stop offset="0" stop-color="#5b595a"></stop><stop offset="1" stop-color="#6f6f6f"></stop></linearGradient></defs><path fill="url(#T)" d="M256 579h2v3-1c1 0 1 0 2-1-1 2-1 3-2 4h-1c-1 2-2 3-3 5-2 0-3 1-4 2 1-2 2-4 3-5 1-3 3-4 3-7z"></path><path d="M251 580c1 2 0 3 0 5-2 2-3 4-5 6-2 1-4 3-5 2h0v-1c2-2 4-4 5-6l5-6z" class="F"></path><path d="M241 588c1-2 3-3 5-3v1c-1 2-3 4-5 6v1h0c-2 1-3 2-5 2h-2c-2 0-4-1-6-2h0l-1 1c0-1-1-1-1-2 0-2 0-2 1-3 1 1 1 2 3 3 1 0 1 0 2-1h2 1c-1 1-1 1-1 2l1-1c3 0 4-2 6-4z" class="W"></path><path d="M232 591h2 1c-1 1-1 1-1 2-2 0-3 0-4-1 1 0 1 0 2-1z" class="E"></path><defs><linearGradient id="U" x1="578.317" y1="207.611" x2="574.348" y2="196.581" xlink:href="#B"><stop offset="0" stop-color="#484748"></stop><stop offset="1" stop-color="#7b7a7a"></stop></linearGradient></defs><path fill="url(#U)" d="M582 192h3v1c0 4-1 7-3 11h0c-1 3-3 5-4 8v1c-1 0-3-1-4-2l-1-1c-2 0-4 0-6 1 1-3 2-5 4-7 0-4-1-6-2-9h-2-1 0l1-2c5 1 10 0 15-1z"></path><defs><linearGradient id="V" x1="576.108" y1="211.472" x2="577.814" y2="205.847" xlink:href="#B"><stop offset="0" stop-color="#242325"></stop><stop offset="1" stop-color="#3c3c3c"></stop></linearGradient></defs><path fill="url(#V)" d="M573 210c2 0 5-2 6-4l3-3v1c-1 3-3 5-4 8v1c-1 0-3-1-4-2l-1-1z"></path><path d="M567 193c5 1 10 0 15-1-1 2-1 4-3 5h0c-1 0-3 1-4 2-1 0-1 0-2-1 0-1-1-3-2-3h-2-2-1 0l1-2z" class="G"></path><path d="M219 478c2 1 2 5 3 6l1 4c0 1 0 2 1 3s1 2 1 4h0c1 1 1 3 2 4 2 3 2 6 3 9h0v12h-1 0-1v-2c-2 1-3 3-4 4-2 1-4 3-6 3h0c1-1 2-2 3-2l-2-2c1-1 2-2 3-4 2-3 3-7 3-11-1-4-4-7-7-10 1-1 1-1 2-1l-1-1h-1l-2-1c1-2 2-4 2-6-1-2 0-5 0-6l1-2s0 1 1 1l-1-2z" class="B"></path><path d="M223 488c0 1 0 2 1 3s1 2 1 4h0c0 2 1 3 1 5l-1 1h0c-2-4-2-8-2-13z" class="L"></path><path d="M218 481l1-2s0 1 1 1v9c1 2 0 4 1 6h-1l-1-1h-1l-2-1c1-2 2-4 2-6-1-2 0-5 0-6z" class="H"></path><path d="M218 487l1 1c0 2 1 4 0 6h-1l-2-1c1-2 2-4 2-6z" class="C"></path><path d="M226 500c0-2-1-3-1-5 1 1 1 3 2 4 2 3 2 6 3 9h0v12h-1 0-1v-2c-2 1-3 3-4 4-2 1-4 3-6 3h0c1-1 2-2 3-2 2-2 4-4 5-6 2-4 1-12-1-16l1-1z" class="D"></path><path d="M226 500c0-2-1-3-1-5 1 1 1 3 2 4 2 3 2 6 3 9h0v12h-1 0l-1-14c-1-2-1-4-2-6z" class="E"></path><path d="M562 377c1-1 1-1 1-3h2c1 1 1 1 2 0 0-1 1-3 1-4 1-1 5-1 6-1 2-1 3-1 5 0v1c-1 5-1 11 2 16 2 2 4 5 7 5 1 0 2 0 3 1h0c-1 2-3 2-5 2-3-1-5-2-8-4-6-3-11-8-16-13z" class="J"></path><path d="M124 194v-2l16 2c1 0 2 1 3 1v1h1-2c0 1 0 1-1 2 1 4 3 8 5 12v2c-1-1-3-1-5-1h-2c-3 0-6 1-8 0 0-2-1-3-2-5v-4c-1-1-1-2-2-3l-2-1-1-4z" class="O"></path><path d="M130 201h0c2 2 3 4 5 6h0c-1 0-1 0-2-1v1c-1-1-2-3-3-4 0 0 0-1-1-2h1z" class="U"></path><path d="M136 199c1 0 1-1 2-1-1-1-1-1-1-2l1-1h1l-1 1v2l-1 1c0 1 0 1 1 2 2 2 3 3 3 6l-1 1-1-1v-1c0-3-2-4-3-7z" class="i"></path><path d="M136 208h5s0-1 1-1c0-3-1-3-2-5-1-1-1-3 0-5 1-1 1-1 2-1 0 1 0 1-1 2 1 4 3 8 5 12h-1c-2 0-4 0-6-1l-3-1z" class="a"></path><path d="M139 209h4c1-3-1-4-2-6v-1h0v-4c1 4 3 8 5 12h-1c-2 0-4 0-6-1z" class="S"></path><path d="M129 202l1 1c1 1 2 3 3 4v-1c1 1 1 1 2 1l1 1 3 1c2 1 4 1 6 1h1v2c-1-1-3-1-5-1h-2c-3 0-6 1-8 0 0-2-1-3-2-5v-4z" class="L"></path><path d="M133 207v-1c1 1 1 1 2 1l1 1 3 1c2 1 4 1 6 1h1v2c-1-1-3-1-5-1h-5l-3-4z" class="Q"></path><defs><linearGradient id="W" x1="140.357" y1="196.617" x2="128.508" y2="200.638" xlink:href="#B"><stop offset="0" stop-color="#6d6c6d"></stop><stop offset="1" stop-color="#929091"></stop></linearGradient></defs><path fill="url(#W)" d="M124 194v-2l16 2c1 0 2 1 3 1v1c-1 0-3-1-4-1h-1l-1 1c0 1 0 1 1 2-1 0-1 1-2 1 1 3 3 4 3 7v1c-1 0-2-1-3-2s-3-2-4-3v-1h-2 0-1c1 1 1 2 1 2l-1-1c-1-1-1-2-2-3l-2-1-1-4z"></path><path d="M127 199l1-1c1 1 2 2 2 3h-1c1 1 1 2 1 2l-1-1c-1-1-1-2-2-3z" class="g"></path><path d="M139 206c0-1-1-1-1-2h-1c-1-2-1-3-1-5h0c1 3 3 4 3 7z" class="T"></path><path d="M124 194l1-1c1 1 3 4 3 5l-1 1-2-1-1-4z" class="L"></path><path d="M130 371h0c2-1 2-2 4-3h3c1 1 1 1 1 2l1 1c2 1 4 0 6 1 0 1-1 3 0 5h1c-5 5-19 18-27 17-1 0-2-1-3-2h1c3-1 6-2 8-4 4-5 5-11 5-17z" class="M"></path><path d="M221 154l1-1h0v-1h1 2v1 1c0 1 0 1 1 2v2 1c0 1 0 1 1 1v2h0v2l-1 1c-1 2-3 4-5 6-2 0-4 0-6 1-1 0-1 0-2 1v4c0 1-1 2-2 4v-1c-1 2-1 3-2 4h-1c0-1 0-2-1-2h0-1c0-2-1-3-2-4h-1v-1s0-1 1-1h0l1-3s3-3 4-3v-1l12-15z" class="l"></path><path d="M203 178v-1s0-1 1-1h0l1-3c0 4 1 6 2 9h0-1c0-2-1-3-2-4h-1z" class="I"></path><path d="M210 174c1 0 1 1 2 2 0 0 1 0 1 1s-1 2-2 4v-1c-1-2-2-3-1-6z" class="E"></path><path d="M223 152h2v1 1c0 1 0 1 1 2v2 1c0 1 0 1 1 1v2h0v2l-1 1c-1 2-3 4-5 6-2 0-4 0-6 1-1 0-1 0-2 1v4c0-1-1-1-1-1-1-1-1-2-2-2v-1c2-3 6-2 9-3h0c3-1 4-2 5-4v-2c-2-3-1-8-1-12z" class="Z"></path><path d="M223 152h2v1 1c0 1 0 1 1 2v2 1c0 1 0 1 1 1v2h0v2l-1 1h0v-2-1-1-1l-1-1v-1l-1 1h0v1h0c1 1 1 4 0 6v-2c-2-3-1-8-1-12z" class="b"></path><defs><linearGradient id="X" x1="543.533" y1="199.984" x2="511.62" y2="208.337" xlink:href="#B"><stop offset="0" stop-color="#828181"></stop><stop offset="1" stop-color="#b0aeaf"></stop></linearGradient></defs><path fill="url(#X)" d="M520 183h3c2 0 4 0 6 1h2l1 1 2 1c4 3 6 5 9 8l3 6c-3-2-4-4-6-6v1l-1-1v1c-3-3-6-6-9-7-4-1-7 0-10 1l-4 4v1c-2 2-3 6-3 9s1 6 3 7l1 1c2 1 4 4 5 5h0c-1 1-1 1-1 2 1 2 4 5 6 6 1 1 2 2 3 2 0 1-1 1-2 1h-2c-6-5-14-13-16-21-1-4 0-11 2-15 2-3 3-5 6-7h0l2-1h0z"></path><path d="M520 183h3c2 0 4 0 6 1h-4c-2 0-3-1-5 0h0c2 1 4 1 5 1l1-1h1v1c-2 0-4 0-6 1-3 2-6 4-9 5h0c2-3 3-5 6-7h0l2-1h0z" class="G"></path><path d="M343 57h0 1c1-1 3-1 4 0 1 0 1 0 1 1v2h1c5-1 8-1 12 1h0c-2 0-3 0-5-1-2 0-5 0-7 1s-3 1-5 2h-1 0 1c-1-1-2-1-3-1-4 2-10 6-13 9s-6 7-8 11c-1 3-1 5-1 8-1 1 0 3 0 4v-1h1v1l1 7-4-2-4-1v-1-5c1-5 2-10 5-15 5-9 14-17 24-20z" class="Y"></path><path d="M318 86c1-2 1-3 2-5 4-9 14-17 23-21h3c0 1 0 1-1 3-1-1-2-1-3-1-4 2-10 6-13 9s-6 7-8 11c-1 3-1 5-1 8-1 1 0 3 0 4v-1h1v1l1 7-4-2-4-1v-1c2-3 2-6 3-8 1-1 1-2 1-3z" class="H"></path><path d="M314 97c2-3 2-6 3-8 1-1 1-2 1-3v13l-4-1v-1z" class="D"></path><defs><linearGradient id="Y" x1="256.449" y1="96.469" x2="228.193" y2="100.992" xlink:href="#B"><stop offset="0" stop-color="#525251"></stop><stop offset="1" stop-color="#767676"></stop></linearGradient></defs><path fill="url(#Y)" d="M224 81c4 4 8 8 13 11 8 5 18 9 23 16l-10 7c-1 1-3 2-4 2l-3 3-2 2-1-5-1-3s-1-3-1-4v-1h0 1v1l1 2h4c1 0 2-1 3-3 0-5-9-8-11-13-1-1-2-1-2-3-2-1-4-3-5-4-2-2-3-4-5-5 0-1-1-1-1-2h1v-1z"></path><path d="M239 114h1c1 1 2 2 4 3h2 0l-3 3-2 2-1-5-1-3z" class="g"></path><path d="M240 117h1l2 2v1l-2 2-1-5z" class="U"></path><defs><linearGradient id="Z" x1="247.639" y1="100.8" x2="239.01" y2="101.487" xlink:href="#B"><stop offset="0" stop-color="#777576"></stop><stop offset="1" stop-color="#969696"></stop></linearGradient></defs><path fill="url(#Z)" d="M234 93c2 2 14 9 15 11 1 0 1 2 1 2 1 2 0 4-1 5-1 2-2 3-4 3-1 1-2 0-4 0 0-1-1-1-1-2h4c1 0 2-1 3-3 0-5-9-8-11-13-1-1-2-1-2-3z"></path><path d="M562 193c1 1 4 0 5 0l-1 2h0 1 2c1 3 2 5 2 9-2 2-3 4-4 7h-4c1 0 1 1 1 2l-3 1c0 1-2 1-2 2s-1 2-1 2v-2h-1l-1-3c-1-4-4-11-8-13l1-1c1-1 2-2 3-2 3-2 6-3 10-4z" class="W"></path><path d="M552 197c0 2 2 1 3 2h0c3 0 5-2 7-3 1 0 3-1 4-1h0c-1 1-1 2-1 3-3 1-5 2-7 3l-1 1v-1c-1 0-1 0-2 1l-2-2c-1 0-3 0-4-1 1-1 2-2 3-2z" class="P"></path><path d="M562 193c1 1 4 0 5 0l-1 2c-1 0-3 1-4 1-2 1-4 3-7 3h0c-1-1-3 0-3-2 3-2 6-3 10-4z" class="D"></path><path d="M566 195h1 2c1 3 2 5 2 9-2 2-3 4-4 7h-4c1 0 1 1 1 2l-3 1c0 1-2 1-2 2s-1 2-1 2v-2c1-2 3-5 4-8s2-7 3-10c0-1 0-2 1-3z" class="B"></path><path d="M567 195h2c1 3 2 5 2 9-2 2-3 4-4 7h-4c2-3 5-7 6-10 0-2-1-4-2-6z" class="E"></path><defs><linearGradient id="a" x1="334.68" y1="143.081" x2="350.058" y2="140.783" xlink:href="#B"><stop offset="0" stop-color="#a2a1a1"></stop><stop offset="1" stop-color="#e0dfdf"></stop></linearGradient></defs><path fill="url(#a)" d="M340 115h0 1c3 6 7 10 9 17-1 3 0 6 1 9h0c0 5 0 12-3 17 0 2-1 3-2 4-2 3-4 7-7 9h-1l-4 2-1-1 2-2v-1-2l2-2c1-1 2-3 3-5 2-2 2-4 3-7 2-8 2-16 0-25h0c-1-4-2-8-5-12l2-1z"></path><path d="M335 167l2-2c0 1 1 1 1 2-1 1-1 2-1 2l2 2h-1l-4 2-1-1 2-2v-1-2z" class="H"></path><path d="M343 128h1c3 9 5 19 2 28 0-1-1-1 0-1v-2-1l1-1c0-1-1-3 0-4v-4c-1-1 0-2-1-4v-1-2c-1-1-1-2-1-3v-1 4c1 3 0 6 0 10v2l-1 4v2l-1-1c2-8 2-16 0-25z" class="D"></path><defs><linearGradient id="b" x1="593.832" y1="337.763" x2="596.602" y2="366.491" xlink:href="#B"><stop offset="0" stop-color="#141314"></stop><stop offset="1" stop-color="#3f3f3f"></stop></linearGradient></defs><path fill="url(#b)" d="M593 367l2-1h1c-1-1-2-1-3-1-8-3-17-8-22-14-1-1-2-2-2-3l-1-1v1 2 1h-1v-4-1l-1-1v-1h1 1c0-1-1-2-1-3l-1-1h1c0 1 1 2 2 3l3 3c1 1 2 2 2 3s1 1 1 1h0v-1h1l1 1 1 1h1c0 1 1 2 1 3h0 1c1 1 1 2 2 2s1 1 2 1h1 2c0 1 1 1 1 1h4 1l-1-1c-1 0-1-1-2-2h-1c-1-1-2-2-2-4 4 5 9 10 17 10 4 0 11-2 14-5 2-3 3-6 3-9 0-1 0-2-2-3 0-1 0-1-1-1s-1 0-2 1h-1l3-3c1 0 2 1 4 2 1 1 2 3 2 5 0 5-3 8-6 11-6 6-13 8-21 8h-5z"></path><path d="M140 194h6c0 1 1 1 1 1l1 1c3 1 8 4 11 3l1-1 1 1 2 2 1-2c-1 4-4 8-4 12 0 2-2 5-3 7-1 3-1 5-3 8v-1l-1 1h-1l-1-1h0l1-3s0-1-1-1c0-1 1-2 1-3h-1l-5-6v-2c-2-4-4-8-5-12 1-1 1-1 1-2h2-1v-1c-1 0-2-1-3-1z" class="G"></path><path d="M147 195l1 1c3 1 8 4 11 3l1-1 1 1v1c-3 0-3 0-5 2v2c-1 1-1 1-2 1-2-1-1-3-2-5l-2-2-1 1h-1l-1-1h1 0l1 1 1-1-3-3z" class="W"></path><path d="M142 196h2c1 3 1 7 2 10 1 4 4 7 5 11v1l-5-6v-2c-2-4-4-8-5-12 1-1 1-1 1-2z" class="F"></path><path d="M164 199c-1 4-4 8-4 12 0 2-2 5-3 7-1 3-1 5-3 8v-1l-1 1h-1l-1-1h0l1-3s0-1-1-1c0-1 1-2 1-3h-1v-1l1-1c1-2 2-3 2-5l5-9c0-1 1-2 2-2v-1l2 2 1-2z" class="K"></path><path d="M161 199l2 2-1 2c-1 0-1 1-2 2h-1 0v-3c0-1 1-2 2-2v-1z" class="E"></path><path d="M154 218l1 1-1 6-1 1h-1l-1-1h0l1-3c1 0 2-3 2-4z" class="P"></path><path d="M151 217l1-1c1-2 2-3 2-5l5-9v3h0 1c-1 2-2 4-2 6s-3 5-4 7c0 1-1 4-2 4 0 0 0-1-1-1 0-1 1-2 1-3h-1v-1z" class="U"></path><defs><linearGradient id="c" x1="362.381" y1="647.472" x2="344.144" y2="689.135" xlink:href="#B"><stop offset="0" stop-color="#858383"></stop><stop offset="1" stop-color="#c6c5c6"></stop></linearGradient></defs><path fill="url(#c)" d="M375 654c1 0 2-1 3-1h1v1h0c1-1 2-2 3-2h1 0c-1 1-1 2-2 3h1l-9 12c-2 4-5 9-8 12h0l-2 2h0v1l-4 6c-2 2-3 4-4 6v1h-1 0c-1-4-3-7-5-11l-7-12c-1-3-3-5-5-8 0 0 0-1-1-1v-2c0 1 1 1 2 2h0l8 11c2 1 3 3 5 5l1 2h0l1-4c1 1 2 2 2 4v1h1v-1l1-1 1-2c4-7 9-14 14-21l3-3z"></path><path d="M375 654c1 0 2-1 3-1h1v1h0c1-1 2-2 3-2h1 0c-1 1-1 2-2 3h1l-9 12h0c0-1 1-2 1-3l3-3c0-1 1-2 2-3s1-2 2-2v-1h-1 0 0c-1-1-1 0-1 0-1 0-2 2-3 2h-4l3-3z" class="c"></path><path d="M221 171c-1 2-5 4-6 7 0-1-1 0-1 0v1h-1c0 1 0 1-1 2h0c-1 1-1 1-1 2h0c-2 5-8 10-9 15 0 1 0 2 1 2h0l1-1c1-1 2-3 3-5s3-4 4-6c4-4 8-9 11-13l4-4h0l1-1 2-1-1 2c1 0 1 1 1 1-2 3-5 6-7 10l-1 2-3 1-12 17-4 7c-1 0-1 0-2-1v1h-1c0-4 0-8-1-12 0-2-1-4-2-5-3-5-6-6-10-9h2c1 1 3 3 5 4l1 1c1-1 2-3 4-4 1-1 2-1 3-2s1-2 2-4h1c1 1 2 2 2 4h1 0c1 0 1 1 1 2h1c1-1 1-2 2-4v1c1-2 2-3 2-4v-4c1-1 1-1 2-1 2-1 4-1 6-1z" class="N"></path><path d="M228 171c1 0 1 1 1 1-2 3-5 6-7 10l-1 2-3 1 10-14z" class="f"></path><path d="M186 183h2c1 1 3 3 5 4l1 1c5 4 7 9 7 15v2h1c1-2 3-3 4-3l-4 7c-1 0-1 0-2-1v1h-1c0-4 0-8-1-12 0-2-1-4-2-5-3-5-6-6-10-9z" class="G"></path><path d="M199 191h0c-1-1-1-2-2-2s-1 0-1-1 1-1 1-1c2-1 6-6 6-8h1 0c1 1 1 2 2 3h0c0 1 1 2 2 3-2 4-5 7-7 12 0-2-1-4-2-6z" class="Y"></path><defs><linearGradient id="d" x1="607.378" y1="217.728" x2="631.817" y2="221.504" xlink:href="#B"><stop offset="0" stop-color="#656364"></stop><stop offset="1" stop-color="#818181"></stop></linearGradient></defs><path fill="url(#d)" d="M611 200c1 1 6 5 6 5l2 1h1l4 5c3 3 5 7 7 11v5c0 4 0 7-1 10l-1 4h-1c-3-4-7-7-11-9-1 0-2-1-3-2h-2l-3-3 1-1c3-4 4-7 4-12-1-1-1-2-2-3 1 0 2-1 2-1l-1-2c0-1-2-4-3-5 0 0 0-1-1-2l2-1z"></path><path d="M617 205l2 1h1l4 5-2-1-5-5z" class="I"></path><path d="M622 210l2 1c3 3 5 7 7 11v5c-2-6-5-11-9-17z" class="C"></path><path d="M624 216h0c1 1 2 2 2 4h1c1 2 2 7 2 9h-1c-1-2-3-5-3-7l-1-2c0-1-1-2 0-4z" class="G"></path><path d="M610 203h1l5 9c1 1 1 2 2 3 0 2 1 3 1 5 0 3-1 10 0 12 1 1 3 2 4 2 1 2 3 2 4 3 0 0 1 1 1 2h1c0-1 0-1 1-3v1l-1 4h-1c-3-4-7-7-11-9-1 0-2-1-3-2h-2l-3-3 1-1c3-4 4-7 4-12-1-1-1-2-2-3 1 0 2-1 2-1l-1-2c0-1-2-4-3-5z" class="O"></path><path d="M612 211c1 0 2-1 2-1l2 4c-1-1-1-1-2-1v1c-1-1-1-2-2-3z" class="J"></path><path d="M614 214v-1c1 0 1 0 2 1 2 5 2 8 0 13-1 1-1 2-1 3h-1-2l-3-3 1-1c3-4 4-7 4-12z" class="B"></path><defs><linearGradient id="e" x1="215.317" y1="81.576" x2="229.738" y2="77.125" xlink:href="#B"><stop offset="0" stop-color="#848384"></stop><stop offset="1" stop-color="#aaa9a9"></stop></linearGradient></defs><path fill="url(#e)" d="M211 65c1-1 3-3 5-4 1-1 2-1 3-1v3c-1 4 0 9 2 13l3 5v1h-1c0 1 1 1 1 2 2 1 3 3 5 5 1 1 3 3 5 4 0 2 1 2 2 3 2 5 11 8 11 13-1 2-2 3-3 3h-4l-1-2v-1c1 0 1 0 2-1v-2h1c0-1-1-2-2-3l2 1c-2-2-4-4-5-6-2-1-3-2-5-2-1-1-2-1-3-1l-1-1h0l-3 3c0 1 0 2 1 3l-1 1h-1c-1 0-2 1-3 2h-1v5c-2-2-1-7-2-10-2-6-5-13-7-19v-1c0-1-1-3-1-4l-1-1h0c0-3 1-5 2-8z"></path><path d="M240 103l2 1c1 1 2 2 2 4 0 1-1 1-1 2-1 1-2 1-3 0h-1v-1c1 0 1 0 2-1v-2h1c0-1-1-2-2-3z" class="I"></path><path d="M211 65l1 1h0c-1 4 0 7 0 11h0 0c0-1-1-2-1-2v-1h0v3 1c0-1-1-3-1-4l-1-1h0c0-3 1-5 2-8z" class="h"></path><path d="M223 82c-3-5-7-10-6-16 0-1 0-3 2-4v1c-1 4 0 9 2 13l3 5v1h-1z" class="e"></path><path d="M213 78v-1-1l3 6 4 9c0 2 1 6 2 7 1 0 2 0 3-1 0 1 0 2 1 3l-1 1h-1c-1 0-2 1-3 2-1-3-1-5-2-7l-6-18z" class="b"></path><path d="M211 78v-1-3h0v1s1 1 1 2h0 0l1 1 6 18c1 2 1 4 2 7h-1v5c-2-2-1-7-2-10-2-6-5-13-7-19v-1z" class="E"></path><path d="M210 102c-1-9-2-20-1-29h0l1 1c0 1 1 3 1 4v1c2 6 5 13 7 19 1 3 0 8 2 10v1c1 8-2 18 5 24l1 1c-1 0-1 1-2 1-1 2-2 4-4 5l-7 8v-7c-1-5-1-10 0-16-3-2 0-10-2-14v1c-1 0-1 0-2-1h1v-9z" class="G"></path><defs><linearGradient id="f" x1="219.913" y1="99.626" x2="201.679" y2="92.045" xlink:href="#B"><stop offset="0" stop-color="#32302b"></stop><stop offset="1" stop-color="#4c4b53"></stop></linearGradient></defs><path fill="url(#f)" d="M210 102c-1-9-2-20-1-29h0l1 1c0 1 1 3 1 4v1h-1c0 4 1 8 2 11 1 4 1 7 2 11 0 5 0 11-1 16v8c-3-2 0-10-2-14v1c-1 0-1 0-2-1h1v-9z"></path><path d="M210 102c1 1 1 4 1 4l1 1v3 9l1 1v-3 8c-3-2 0-10-2-14v1c-1 0-1 0-2-1h1v-9z" class="L"></path><path d="M90 208c0-1 1-2 2-3 2-2 7-7 10-8-1 3-1 6-3 9 0 2-1 3-2 4-2 3-2 6-2 9 1 2 2 5 3 8l-1 1c0 2-2 5-3 8h0l-1-1c-4 3-8 6-12 10-1-1-1-2-2-3h1v-3c-1-1-1-3-1-4v-4c-1-3 0-5 1-7v-3h0 0-1c2-4 5-8 7-12h1c1 0 2-1 3-1z" class="T"></path><path d="M92 220l1-2c0-1 0-2 1-3v2c0 1 1 1 1 2 1 2 2 5 3 8l-1 1-4 3v-1-2c-1-3-1-5-1-8z" class="f"></path><path d="M97 228c0 2-2 5-3 8h0l-1-1c-4 3-8 6-12 10-1-1-1-2-2-3h1v-3h1c1-1 1-2 1-3h0 1c2-2 4-2 5-3s1-2 2-2l3-1v1l4-3z" class="C"></path><path d="M93 230v1c-4 3-10 6-13 11h0v-3h1c1-1 1-2 1-3h0 1c2-2 4-2 5-3s1-2 2-2l3-1z" class="a"></path><defs><linearGradient id="g" x1="98.091" y1="207.584" x2="89.318" y2="204.567" xlink:href="#B"><stop offset="0" stop-color="#343535"></stop><stop offset="1" stop-color="#5f5c5e"></stop></linearGradient></defs><path fill="url(#g)" d="M90 208c0-1 1-2 2-3 2-2 7-7 10-8-1 3-1 6-3 9 0 2-1 3-2 4-2 3-2 6-2 9 0-1-1-1-1-2v-2c-1 1-1 2-1 3l-1 2v-5c0-2 2-4 1-6-1 0-2 0-3 1s-1 3-2 4-2 2-2 3c-2 2-3 5-4 7s-2 4-3 7h0c-1-3 0-5 1-7v-3h0 0-1c2-4 5-8 7-12h1c1 0 2-1 3-1z"></path><path d="M86 209h1c1 0 2-1 3-1-3 3-5 7-8 11 0 1-1 3-2 5v-3h0 0-1c2-4 5-8 7-12z" class="C"></path><path d="M497 456v-1c-1-2-1-5-1-8v-2l1-1c0 1 0 4 1 5 0 1 0 1 1 1v2h4 1c0-1 1-1 2-1 1 5 1 9 2 14 1 2 1 4 2 5h0l1 1v-1l4 11c0 1-1 2-1 3-1 5-4 10-6 14l-1 1c0 1-1 2-2 3h0-1c-1 2-2 5-3 7v1h0c-2 0-2 1-3 0v-1c2-3 3-5 3-8v-1c1-2 1-4 0-6l-4-38z" class="U"></path><path d="M507 499v-1h0c1-1 0-1 1-2h-1v-1c3-3 4-8 6-12h1v1c-1 5-4 10-6 14l-1 1z" class="M"></path><defs><linearGradient id="h" x1="497.189" y1="481.906" x2="504.758" y2="482.029" xlink:href="#B"><stop offset="0" stop-color="#959395"></stop><stop offset="1" stop-color="#b6b5b5"></stop></linearGradient></defs><path fill="url(#h)" d="M497 456v-4c1 1 2 6 2 8 2 5 4 11 5 16 1 3 1 5 1 8 1 5 1 11 0 16l-1 2c-1 2-2 5-3 7v1h0c-2 0-2 1-3 0v-1c2-3 3-5 3-8v-1c1-2 1-4 0-6l-4-38z"></path><path d="M225 97l3-3h0l1 1c1 0 2 0 3 1 2 0 3 1 5 2 1 2 3 4 5 6l-2-1c1 1 2 2 2 3h-1v2c-1 1-1 1-2 1h-1 0v1c0 1 1 4 1 4l1 3 1 5c-3 2-6 5-9 7l-6 5-1-1c-7-6-4-16-5-24v-1-5h1c1-1 2-2 3-2h1l1-1c-1-1-1-2-1-3z" class="D"></path><path d="M225 97l3-3h0l1 1c1 0 2 0 3 1h-1-1c-1-1-2-1-3 0h0c0 1 2 2 2 3 1 0 1 1 1 2l1 6c-1-2-2-3-2-5v-1l-1-1c-1-1-1-1-2 0-1-1-1-2-1-3z" class="C"></path><path d="M238 109v-2h-1v3h0v1c-2 0-2 0-3-1 0-1-1-2-1-3v-1h-1v-4h0l-1-1v-1c0-1 0-1 1-2 3 1 5 2 8 5 1 1 2 2 2 3h-1v2c-1 1-1 1-2 1h-1z" class="N"></path><path d="M232 100h1c1 0 2 0 2 1 3 1 4 2 5 5-1 0-1 1-2 1h-1l-1 1c-1 1-1 0-2 0-1-3-2-5-2-8z" class="j"></path><defs><linearGradient id="i" x1="236.763" y1="125.776" x2="233.711" y2="115.455" xlink:href="#B"><stop offset="0" stop-color="#737273"></stop><stop offset="1" stop-color="#999898"></stop></linearGradient></defs><path fill="url(#i)" d="M238 110c0 1 1 4 1 4l1 3 1 5c-3 2-6 5-9 7l-3-14 1 1v2h1s1 0 1-1v-1s1 0 1-1c0 0 0-1 1-1h2l2-2v-2z"></path><path d="M224 101c1 1 2 1 2 2 1 3 2 6 2 9 0 1 1 3 1 3l3 14-6 5-1-1c-7-6-4-16-5-24v-1-5h1c1-1 2-2 3-2z" class="B"></path><path d="M195 109v-1l5 3h0v-2h-1c-1-1-1-1-2-1v-1l12 4c1 1 1 1 2 1v-1c2 4-1 12 2 14-1 6-1 11 0 16v7h0l-2 3-3 4-5 7c-1-1-2-3-2-4l-1-3-1 1-3-5 1-1c-1-3-1-4 0-6v-3h-1-1v-1-1c1 0 2-1 3-1h0c1 0 1-1 2-1v-1h0c0-1 0-2 1-3 1 0 2-1 3-1l-1-1c-2-1-3-1-5-2l-1 1h0v-4h2l1 1c0-1 1-1 1-2h1 1c-1-1-2-2-4-3h0-1l-3-2h1 2 0 1 1l-1-1h0l-1-3 4 2c1 0 0 0 1-1l3 1h1v-1c0-2-1-3-2-4-1 1-2 0-3-1l-7-3z" class="X"></path><path d="M200 140h1v1l-1 2s-1 0-1-1v-1l1-1z" class="e"></path><defs><linearGradient id="j" x1="204.123" y1="128.447" x2="197.638" y2="128.951" xlink:href="#B"><stop offset="0" stop-color="#797879"></stop><stop offset="1" stop-color="#929090"></stop></linearGradient></defs><path fill="url(#j)" d="M197 126h2l1 1c2 0 3 1 5 2v2c-1 1 0 1-1 1l-1-1c-2-1-3-1-5-2l-1 1h0v-4z"></path><path d="M196 141c1 0 1-1 2 0 0 2 0 1 1 2h1l1-1c0 1 1 1 0 2v1h0c-1 3-1 5-2 8l1 2-1 1-3-5 1-1c-1-3-1-4 0-6v-3h-1z" class="c"></path><path d="M196 151l1-1c-1-3-1-4 0-6 0 3 0 6 2 9l1 2-1 1-3-5z" class="d"></path><defs><linearGradient id="k" x1="205.894" y1="157.004" x2="202.117" y2="155.417" xlink:href="#B"><stop offset="0" stop-color="#2e2d2e"></stop><stop offset="1" stop-color="#474647"></stop></linearGradient></defs><path fill="url(#k)" d="M201 158c0-2-1-4 0-5 1-5 1-9 1-13h1v2l1 1c1 1 0 3 0 4h0 1 0v-2h1l1-4c0 2 1 4 0 6v3 2 2c0 1 0 1 1 1l-5 7c-1-1-2-3-2-4z"></path><path d="M198 116l4 2h1c3 1 3 2 4 4 1 1 1 2 1 4 0 0-1 0-1 1h0c1 1 1 1 1 3l1 1c-1 3-1 7-2 10l-1 4h-1v2h0-1 0c0-1 1-3 0-4l-1-1v-2-4c1-1 1-1 1-2v-1c1-1 1-1 2-1 0-2 0-3-1-4v-2l-1-1h-1c-1-1-2-2-4-3h0-1l-3-2h1 2 0 1 1l-1-1h0l-1-3z" class="L"></path><path d="M205 126c2 1 2 2 2 4v1 1c-1 0-1 1-1 1 0 3-2 4-2 7v3l-1-1v-2-4c1-1 1-1 1-2v-1c1-1 1-1 2-1 0-2 0-3-1-4v-2z" class="R"></path><path d="M198 116l4 2h1c1 2 1 2 1 3 1 0 1 1 1 2l2 2h-1l-1-1-1 1h-1c-1-1-2-2-4-3h0-1l-3-2h1 2 0 1 1l-1-1h0l-1-3z" class="O"></path><path d="M199 119c2 1 3 2 5 4l-5-2v1h-1l-3-2h1 2 0 1 1l-1-1h0z" class="K"></path><defs><linearGradient id="l" x1="210.805" y1="131.226" x2="201.44" y2="131.777" xlink:href="#B"><stop offset="0" stop-color="#151616"></stop><stop offset="1" stop-color="#363435"></stop></linearGradient></defs><path fill="url(#l)" d="M195 109v-1l5 3h0v-2h-1c-1-1-1-1-2-1v-1l12 4c1 1 1 1 2 1v-1c2 4-1 12 2 14-1 6-1 11 0 16v7h0l-2 3-3 4c-1 0-1 0-1-1v-2-2-3c1-2 0-4 0-6 1-3 1-7 2-10l-1-1c0-2 0-2-1-3h0c0-1 1-1 1-1 0-2 0-3-1-4-1-2-1-3-4-4h-1c1 0 0 0 1-1l3 1h1v-1c0-2-1-3-2-4-1 1-2 0-3-1l-7-3z"></path><path d="M205 113h0c1 0 1 0 2 1 1 0 1 1 2 2 0 1 0 2-1 3 0 2 1 3 1 4v8l-1-1c0-2 0-2-1-3h0c0-1 1-1 1-1 0-2 0-3-1-4-1-2-1-3-4-4h-1c1 0 0 0 1-1l3 1h1v-1c0-2-1-3-2-4z" class="M"></path><path d="M211 112v-1c2 4-1 12 2 14-1 6-1 11 0 16v7h0l-2 3c0-1 0-2-1-3v-5-15l1-16z" class="O"></path><path d="M210 148c0-1 0-3 1-4v-1c0 1 1 5 1 5h1l-2 3c0-1 0-2-1-3z" class="i"></path><path d="M131 211c2 1 5 0 8 0h2c2 0 4 0 5 1l5 6h1c0 1-1 2-1 3 1 0 1 1 1 1l-1 3s-1 3-2 3v-1l-1-1h0-1c-1 1-2 1-3 0l-2 1h0-3v1c0 1-1 2-2 3s-3 2-4 3l1 1-5 2h0c-3-1-5-2-6-4-2-3-6-6-8-9-1 0-3-3-4-4l15-6 3-1 1-1v-1h1z" class="c"></path><path d="M133 227h5 1v1c0 1-1 2-2 3-1-2-2-3-4-4z" class="X"></path><path d="M123 223l2 2c2 1 6 3 8 5l-1 1c-2-1-3-3-5-4-3-1-6-2-8-4 1 0 3 1 4 0z" class="O"></path><path d="M116 224c4 3 8 7 13 9 1 1 2 1 3 1h1l1 1-5 2h0c-3-1-5-2-6-4-2-3-6-6-8-9h1z" class="B"></path><defs><linearGradient id="m" x1="122.058" y1="214.228" x2="119.993" y2="222.955" xlink:href="#B"><stop offset="0" stop-color="#474747"></stop><stop offset="1" stop-color="#6d6c6c"></stop></linearGradient></defs><path fill="url(#m)" d="M126 214l3-1v1c0 1 1 1 1 2 0 0 1 1 1 2l5 6 1 1h-1v1l2 1h-5c-1-1-2-1-3-2-2 0-3-1-5 0l-2-2c-1 1-3 0-4 0-1-1-2-1-4-2v1h0l1 2h-1c-1 0-3-3-4-4l15-6z"></path><path d="M115 221v-1h0 1 0c3 1 5 2 7 3-1 1-3 0-4 0-1-1-2-1-4-2z" class="a"></path><path d="M129 220c2 2 4 4 7 6l2 1h-5c-1-1-2-1-3-2l-1-1c-1-1-3-2-4-4h4z" class="R"></path><path d="M126 214l3-1v1c0 1 1 1 1 2 0 0 1 1 1 2l5 6 1 1h-1v1c-3-2-5-4-7-6l-2-2c-1-2-1-3-1-4z" class="B"></path><defs><linearGradient id="n" x1="149.618" y1="221.602" x2="132.945" y2="212.645" xlink:href="#B"><stop offset="0" stop-color="#5b5a5b"></stop><stop offset="1" stop-color="#999899"></stop></linearGradient></defs><path fill="url(#n)" d="M131 211c2 1 5 0 8 0h2c2 0 4 0 5 1l5 6h1c0 1-1 2-1 3 1 0 1 1 1 1l-1 3s-1 3-2 3v-1l-1-1h0-1c-1 1-2 1-3 0l-2 1h0-3-1l-2-1v-1h1l-1-1-5-6c0-1-1-2-1-2 0-1-1-1-1-2v-1l1-1v-1h1z"></path><path d="M151 221c1 0 1 1 1 1l-1 3s-1 3-2 3v-1l-1-1 3-5z" class="M"></path><path d="M131 211c2 1 5 0 8 0-1 1-1 2-2 2h-6 0c0-1 0-1-1-1v-1h1z" class="d"></path><path d="M130 212c1 0 1 0 1 1h0c2 4 8 10 11 12 1 1 1 1 2 1l-2 1h0-3-1l-2-1v-1h1l-1-1-5-6c0-1-1-2-1-2 0-1-1-1-1-2v-1l1-1z" class="J"></path><path d="M131 213h0c2 4 8 10 11 12v1c-1 1-1 1-2 0l-6-6v-1l-1-1h0c0-1-1-1-1-2l-1-1v-2z" class="F"></path><defs><linearGradient id="o" x1="178.149" y1="95.099" x2="172.922" y2="103.198" xlink:href="#B"><stop offset="0" stop-color="#414041"></stop><stop offset="1" stop-color="#6e6e6d"></stop></linearGradient></defs><path fill="url(#o)" d="M146 89c0-2-3-4-4-5 1 0 3 2 4 3h1v1c0-1 1-1 1-1 1 1 2 1 3 2v-1h1l-3-2h0v-1c12 8 25 13 38 19 1 0 10 3 10 3v1c1 0 1 0 2 1h1v2h0l-5-3v1l7 3c1 1 2 2 3 1 1 1 2 2 2 4v1h-1l-3-1c-1 1 0 1-1 1l-4-2 1 3h0l1 1h-1-1 0-2-1v1 1c-3 1-7-3-10-4v-1c-6-3-12-6-17-10-2-2-5-4-7-6l-8-6-4-4v1l-1-1-2-2h0z"></path><path d="M146 89h3c5 3 10 6 15 8 0 1 1 2 2 2l1 1h0-1c-1 0-3-1-5-1l1 1-1 1-8-6-4-4v1l-1-1-2-2h0z" class="K"></path><path d="M149 91c2 0 3 2 4 3l8 5 1 1-1 1-8-6-4-4z" class="N"></path><defs><linearGradient id="p" x1="188.598" y1="101.055" x2="178.66" y2="111.509" xlink:href="#B"><stop offset="0" stop-color="#5c5c5b"></stop><stop offset="1" stop-color="#828081"></stop></linearGradient></defs><path fill="url(#p)" d="M164 97c1 0 1 0 3 1l1 1c5 2 10 5 15 7l12 3 7 3c1 1 2 2 3 1 1 1 2 2 2 4v1h-1l-3-1c-1 1 0 1-1 1l-4-2-5-2c-1 0-3-1-4-2l-6-4c-3-1-5-2-8-3-1-1-2-1-2-1-2-1-5-2-7-4h1 0l-1-1c-1 0-2-1-2-2z"></path><path d="M202 112c1 1 2 2 3 1 1 1 2 2 2 4v1h-1l-3-1c-1 1 0 1-1 1l-4-2-5-2-1-1c2 0 7 2 9 3h1 1v-1-1l-1-2z" class="S"></path><path d="M161 99c2 0 4 1 5 1 2 2 5 3 7 4 0 0 1 0 2 1l8 3 6 4c1 1 3 2 4 2l5 2 1 3h0l1 1h-1-1 0-2-1v1 1c-3 1-7-3-10-4v-1c-6-3-12-6-17-10-2-2-5-4-7-6l1-1-1-1z" class="W"></path><path d="M193 114l5 2 1 3h0c-2-1-3-2-5-3 0-1 0-1-1-2h0z" class="T"></path><path d="M161 101l1-1c11 8 22 14 33 21v1c-3 1-7-3-10-4v-1c-6-3-12-6-17-10-2-2-5-4-7-6z" class="Y"></path><defs><linearGradient id="q" x1="580.587" y1="221.632" x2="583.141" y2="225.515" xlink:href="#B"><stop offset="0" stop-color="#696869"></stop><stop offset="1" stop-color="#807e7f"></stop></linearGradient></defs><path fill="url(#q)" d="M567 211c2-1 4-1 6-1l1 1c1 1 3 2 4 2v-1s1 0 1 1l6 2 2 1 7 3 2 1c0 1-2 3-3 4h0c-3 4-5 7-9 10 1 1 3 1 4 2l5 2c2 2 4 1 6 3l-2 1c-2 1-3 2-5 3l-1-1c-2-1-6-1-9-2-6-2-11-5-16-10l-3-3c0-2-1-4-2-6s-1-4-2-7c0-1 2-1 2-2l3-1c0-1 0-2-1-2h4z"></path><path d="M585 215l2 1v4c-2 2-5 3-7 4-3 1-5 3-8 3 0 1 0 1-1 1 0 1 1 1 2 2h-1c-2 0-2 0-3-1 2-2 5-4 7-6 3-1 5-2 7-3 2-2 2-3 2-5z" class="R"></path><path d="M594 219l2 1c0 1-2 3-3 4h0l-1-1-1 1-1-1c-1 1-2 2-3 2-2 1-3 2-5 3l-2 1c-1 0-1 0-2-1l7-5c2-1 4-2 7-3 1 0 1 0 2-1z" class="e"></path><path d="M579 213l6 2c0 2 0 3-2 5-2 1-4 2-7 3-2 2-5 4-7 6h-1l-1-1a30.44 30.44 0 0 0 8-8c1-1 2-3 3-5 0-1 1-2 1-2z" class="B"></path><path d="M567 211c2-1 4-1 6-1l1 1c1 1 3 2 4 2v-1s1 0 1 1c0 0-1 1-1 2-1 2-2 4-3 5a30.44 30.44 0 0 1-8 8l-2-1c0 2 1 3 1 5l-3-3c0-2-1-4-2-6s-1-4-2-7c0-1 2-1 2-2l3-1c0-1 0-2-1-2h4z" class="G"></path><path d="M572 219c0-1 2-2 2-2 1-2 2-3 4-4-1 2-3 4-4 7h-1l-1-1z" class="E"></path><path d="M567 211c2-1 4-1 6-1l1 1h-1c-1 2-1 3-3 3v1c0 1 0 1-1 1h-1c-1 1-2 1-4 1v-1s1-1 1-2h0c0-1 0-1 1-2l-2 1c0-1 0-2-1-2h4z" class="H"></path><path d="M567 211c2-1 4-1 6-1l1 1h-1c-2 1-6 0-7 1l-2 1c0-1 0-2-1-2h4z" class="M"></path><path d="M559 216c0-1 2-1 2-2 0 1 0 2 1 3v1c0 2 1 3 2 3 2 1 4 1 5 1v-1l2-2h1l1 1h1 1a30.44 30.44 0 0 1-8 8l-2-1c0 2 1 3 1 5l-3-3c0-2-1-4-2-6s-1-4-2-7z" class="S"></path><path d="M559 216c0-1 2-1 2-2 0 1 0 2 1 3v1c0 2 1 3 2 3 2 1 4 1 5 1-1 1-2 1-3 1-2 0-2-1-4-1 0 1 1 2 2 3l1 2c0 2 1 3 1 5l-3-3c0-2-1-4-2-6s-1-4-2-7z" class="R"></path><path d="M582 228c2-1 3-2 5-3 1 0 2-1 3-2l1 1 1-1 1 1c-3 4-5 7-9 10 1 1 3 1 4 2l5 2c2 2 4 1 6 3l-2 1c-2 1-3 2-5 3l-1-1c-2-1-6-1-9-2-6-2-11-5-16-10 0-2-1-3-1-5l2 1 1 1h1c1 1 1 1 3 1h1l5-2c1 1 1 1 2 1l2-1z" class="B"></path><path d="M588 236l5 2c2 2 4 1 6 3l-2 1h-3c1-1 1-1 2-1-1-1-5-2-6-3-2 0-2-1-2-2z" class="M"></path><path d="M582 242c1 0 2 0 2-1h3c2 1 5 1 7 1h3c-2 1-3 2-5 3l-1-1c-2-1-6-1-9-2z" class="C"></path><path d="M582 228c2-1 3-2 5-3 1 0 2-1 3-2l1 1c-3 3-8 7-12 8v-2c1 0 2-1 3-2z" class="W"></path><defs><linearGradient id="r" x1="571.424" y1="229.945" x2="577.263" y2="230.08" xlink:href="#B"><stop offset="0" stop-color="#595857"></stop><stop offset="1" stop-color="#717172"></stop></linearGradient></defs><path fill="url(#r)" d="M573 230l5-2c1 1 1 1 2 1l2-1c-1 1-2 2-3 2v2l-3 1c-2 0-2 0-4-1h0c-1 0-3-2-4-3h1c1 1 1 1 3 1h1z"></path><path d="M580 229l2-1c-1 1-2 2-3 2v2l-3 1-1-1s0-1 1-1c1-1 2-2 4-2z" class="G"></path><defs><linearGradient id="s" x1="573.383" y1="226.694" x2="576.13" y2="242.868" xlink:href="#B"><stop offset="0" stop-color="#747173"></stop><stop offset="1" stop-color="#acadac"></stop></linearGradient></defs><path fill="url(#s)" d="M565 227l2 1 1 1c1 1 3 3 4 3 4 5 9 7 15 9h-3c0 1-1 1-2 1-6-2-11-5-16-10 0-2-1-3-1-5z"></path><path d="M227 499h1v-1-1l1-1 6 7c2 2 4 4 5 6l3 1h1v2c2 2 5 3 7 4l3 3h2v2h0-1c-1 0-2 0-3-1h-1v-1l-3 3h0c-2 3-4 5-5 8l-3 9h0l-1-2c1-1 1-3 1-5-1 1-3 2-4 3 0 2 0 3-1 5-1 4-3 8-6 11-1 2-4 4-6 4-1 0-2 0-3-1 0 0 0-1 1-1s2-1 3-2c1-2 1-4 1-6-1-4-4-8-8-11h0v-1c1 0 2-1 3-2h-1-1c-1-1-3-2-3-3l3-3c2 0 4-2 6-3 1-1 2-3 4-4v2h1 0 1v-12h0c-1-3-1-6-3-9z" class="B"></path><path d="M228 526l1 2c-1 1-1 2-2 3 0-1 0-2 1-2h-2l2-3z" class="C"></path><path d="M228 520h1 0c1 1 1 3 2 3v2l-2 3-1-2c1-2 1-4 0-6z" class="P"></path><path d="M226 529h2c-1 0-1 1-1 2h0c-2 2-4 3-7 3 2-2 4-3 6-5z" class="D"></path><path d="M240 515h1l-1 7-3 2h-1c1-3 2-7 4-9z" class="M"></path><path d="M227 499h1v-1-1l1-1c0 2 1 4 1 5 1 2 1 4 2 6l-1 2c0-1 0-1-1-1h0c-1-3-1-6-3-9z" class="O"></path><path d="M232 507c1 2 2 4 2 6 1 4 2 9-1 13-1 2-3 4-6 5h0c1-1 1-2 2-3l2-3v-2c-1 0-1-2-2-3h1v-12c1 0 1 0 1 1l1-2z" class="R"></path><path d="M232 507c1 2 2 4 2 6h-1c0 3 1 8 0 11l-1 1s0 1-1 1h0c-1-1 0-1 0-1 0-1 0-3 1-4 0-4 0-8-1-12l1-2z" class="g"></path><path d="M230 508c1 0 1 0 1 1 1 4 1 8 1 12-1 1-1 3-1 4h0v-2c-1 0-1-2-2-3h1v-12z" class="G"></path><defs><linearGradient id="t" x1="360.567" y1="111.859" x2="345.502" y2="62.051" xlink:href="#B"><stop offset="0" stop-color="#a9a7a8"></stop><stop offset="1" stop-color="#efeeef"></stop></linearGradient></defs><path fill="url(#t)" d="M344 63h1c2-1 3-1 5-2s5-1 7-1c2 1 3 1 5 1h0c5 1 10 4 14 7l4 4h1c2 1 4 5 6 8 2 5 3 13 2 20l-11 6-3 3-3-1c3-2 5-6 5-10 1-3-4-3-5-5 1-3 5-4 7-7 0-2 0-4-1-6-3-5-9-10-14-12-8-2-15-2-22 2-6 2-11 7-13 13 1 4 4 6 7 9 0 2-4 2-5 3l-1 1c0 2-1 3-1 5h0c-1-1-3-4-3-5-1-1-1-2-2-3 0-2-1-2-2-2h-1l-1-1c0-3 0-5 1-8 2-4 5-8 8-11s9-7 13-9c1 0 2 0 3 1h-1 0z"></path><path d="M376 68l4 4c0 1 1 1 1 2 0 0 0 1 1 2 1 2 0 5 0 8v2c-1 0-1 1-1 1v-1-1-4c-1-1-1-2-1-3l1-1-1-1-1-1v-1l-3-6z" class="D"></path><path d="M329 71c3-3 9-7 13-9 1 0 2 0 3 1h-1 0c-4 2-9 4-11 8-1 1-3 5-5 6v-1l1-1 2-2c0-1 0-2 1-2 0-1 0-1 1-2h-1l-2 3-1-1z" class="Z"></path><path d="M381 72c2 1 4 5 6 8h-2 0v1c0 1-1 2-1 3-1 1 0 2 0 3v3c0 4-3 11-6 14v-2-1h0c2-3 5-9 4-12l-1-2s0-1 1-1v-2c0-3 1-6 0-8-1-1-1-2-1-2 0-1-1-1-1-2h1z" class="C"></path><path d="M329 71l1 1 2-3h1c-1 1-1 1-1 2-1 0-1 1-1 2l-2 2-1 1v1c-2 2-4 5-4 8 0 1 0 1 1 1l1 1c0 1-1 1-2 2v1c0 1 1 1 1 1l1 1h-1v1c1 1 1 2 1 3-1-1-1-2-2-3 0-2-1-2-2-2h-1l-1-1c0-3 0-5 1-8 2-4 5-8 8-11z" class="I"></path><path d="M324 89v-1c-1-1-1-1 0-2h1l1 1c0 1-1 1-2 2z" class="J"></path><defs><linearGradient id="u" x1="388.519" y1="98.429" x2="376.866" y2="87.318" xlink:href="#B"><stop offset="0" stop-color="#757474"></stop><stop offset="1" stop-color="#b1b1b1"></stop></linearGradient></defs><path fill="url(#u)" d="M378 104c3-3 6-10 6-14v-3c0-1-1-2 0-3 0-1 1-2 1-3v-1h0 2c2 5 3 13 2 20l-11 6v-1-1z"></path><defs><linearGradient id="v" x1="479.856" y1="64.635" x2="488.789" y2="87.212" xlink:href="#B"><stop offset="0" stop-color="#585457"></stop><stop offset="1" stop-color="#818280"></stop></linearGradient></defs><path fill="url(#v)" d="M476 88c0-1 2-2 3-3 4-4 8-9 10-16 0-1 1-2 0-4v-5h1c2 0 5 3 6 4 1 2 3 4 3 6l-1 1v3c-1 1-1 1-1 2s-1 3-1 4h0l-6 13c-1 1-2 3-2 4 0 2-1 4-2 5l-3-1c-2 3-3 15-4 19l-3 10-10-9-2-1v-1h1c1-1 2-3 2-4-2 0-3 0-4-1l-1-1s-1 0-1-1c-2-1-1-3-1-4l1-1h0v-2l-1 2-1-1 2-1c0-3 4-6 6-8 1-1 3-2 3-4l6-5z"></path><path d="M470 93l6-5c0 1-1 1-1 2h0c-2 3-5 6-8 9l-6 6c0-3 4-6 6-8 1-1 3-2 3-4z" class="e"></path><path d="M482 91l1 1h0l-1 1c0 1-1 2-2 2h-2c-2 1-3 3-5 4-2 2-5 3-7 6v1 1 2l1 1 1-1v-1c2 0 3 1 5 1-1 1-1 2-2 3l-1-3h-1l-1 2v1h-4c0-1-1-2-1-3 0-3 1-3 2-5 3-3 5-6 9-8 2-1 5-3 7-5h1z" class="I"></path><path d="M473 109c-2 0-3-1-5-1v1l-1 1-1-1v-2-1-1c2-3 5-4 7-6 2-1 3-3 5-4h2l-1 2-2 2-4 10z" class="j"></path><defs><linearGradient id="w" x1="469.717" y1="123.221" x2="482.852" y2="107.736" xlink:href="#B"><stop offset="0" stop-color="#888787"></stop><stop offset="1" stop-color="#c4c2c3"></stop></linearGradient></defs><path fill="url(#w)" d="M479 97c1 1 2 1 2 2v1c-1 3-2 6-2 9v4l-1 3c0 2 1 2 1 4l-3 10-10-9-2-1v-1h1c1-1 2-3 2-4s0-2 1-3v-1l1-2h1l1 3c1-1 1-2 2-3l4-10 2-2z"></path><path d="M468 111l1-2h1l1 3 1 1-2 3h-1v-2-4l-1 1z" class="P"></path><path d="M468 111l1-1v4c-1 2-1 5-3 7l-2-1v-1h1c1-1 2-3 2-4s0-2 1-3v-1z" class="L"></path><path d="M479 97c1 1 2 1 2 2v1c-1 3-2 6-2 9v4l-1 3v-1c-1-1-1-2-1-3h0v1h0v-3c1-4 1-7 0-11l2-2z" class="C"></path><defs><linearGradient id="x" x1="496.994" y1="77.916" x2="479.087" y2="81.987" xlink:href="#B"><stop offset="0" stop-color="#727071"></stop><stop offset="1" stop-color="#a5a4a5"></stop></linearGradient></defs><path fill="url(#x)" d="M496 64c1 2 3 4 3 6l-1 1v3c-1 1-1 1-1 2s-1 3-1 4h0l-6 13c-1 1-2 3-2 4 0 2-1 4-2 5l-3-1c-2 3-3 15-4 19 0-2-1-2-1-4l1-3v-4c0-3 1-6 2-9v-1c0-1-1-1-2-2l1-2c1 0 2-1 2-2l1-1h0l-1-1c0-1 3-4 4-5 1-2 2-4 3-7 1-2 2-5 3-7 0-2 0-4 1-5 0-1 1-2 1-2 1 1 1 3 1 5h0c0-2 0-4 1-6z"></path><defs><linearGradient id="y" x1="490.969" y1="77.827" x2="488.835" y2="94.337" xlink:href="#B"><stop offset="0" stop-color="#514f50"></stop><stop offset="1" stop-color="#7d7e7d"></stop></linearGradient></defs><path fill="url(#y)" d="M496 64c1 2 3 4 3 6l-1 1v3c-1 1-1 1-1 2s-1 3-1 4h0c-2 1-3 2-3 4-3 3-5 7-6 11-1-1-1-1-1-2l3-9c3-4 6-8 6-14h0c0-2 0-4 1-6z"></path><path d="M496 64c1 2 3 4 3 6l-1 1v3l-1-1c0 1-1 1-1 2h0v-3c0-1 0-1-1-2 0-2 0-4 1-6z" class="E"></path><defs><linearGradient id="z" x1="487.907" y1="104.881" x2="481.372" y2="100.208" xlink:href="#B"><stop offset="0" stop-color="#9f9fa0"></stop><stop offset="1" stop-color="#d6d4d6"></stop></linearGradient></defs><path fill="url(#z)" d="M487 95c1-4 3-8 6-11 0-2 1-3 3-4l-6 13c-1 1-2 3-2 4 0 2-1 4-2 5l-3-1c-2 3-3 15-4 19 0-2-1-2-1-4l1-3v-4c0-3 1-6 2-9v-1c0-1-1-1-2-2l1-2c1 0 2-1 2-2v1l1 1 1-1 1 1v1h0c0-1 0-2 1-3 0 1 0 1 1 2z"></path><path d="M486 93c0 1 0 1 1 2-1 1-1 3-2 3h0v-2h0c0-1 0-2 1-3z" class="K"></path><path d="M480 95c1 0 2-1 2-2v1l1 1 1-1 1 1v1 2h-1c-1 0-2 1-3 2v-1c0-1-1-1-2-2l1-2z" class="D"></path><path d="M610 207l2 3v1c1 1 1 2 2 3 0 5-1 8-4 12l-1 1 3 3h2c1 1 2 2 3 2v3h-2-1l1 2h0c1 2 1 4 2 5 1 0 1 1 2 2h0c-1 0-1 0-2 1-2 2-6 4-8 5-1 1-3 2-4 3-1 0-3 1-4 1-7 4-17 4-25 4h-5-4c-3-1-6-2-8-3l-10-6h1-1c-3-2-6-3-10-4-1 0-10 1-11 0v-3h4c2-1 4-2 6-4 1-1 4-4 5-4l1 1h1 0v-1c1 0 2-1 3-1 0 0 1 0 2 1h2c3 3 6 5 9 8l8 4c6 1 12 2 17 0 1 0 2-1 3-1l2-1 1 1c2-1 3-2 5-3l2-1 3-2c2-1 4-3 5-5h0v-3-1c-1 0 0 0-1-1 0-1-1-1-1-2 0-2 1-2 2-3 3-4 3-7 3-11v-1l-1-1v-1c0-1-1-1-1-1l-1-1h0l3-1z" class="j"></path><path d="M571 256l6 1h-3v1c-2 0-2-1-3-1v-1z" class="i"></path><path d="M568 255l3 1h0v1c1 0 1 1 3 1h2 0-5c-1-1-1-1-2-1s-1-1-1-2z" class="T"></path><path d="M607 249h0l5-3h0c-1 2-3 3-3 4-1 1-3 2-4 3l2-3v-1z" class="e"></path><path d="M612 230h2c1 1 2 2 3 2v3h-2-1c0-1-2-4-2-5z" class="P"></path><path d="M617 242c1 0 1 1 2 2h0c-1 0-1 0-2 1-2 2-6 4-8 5 0-1 2-2 3-4h0l5-4z" class="i"></path><path d="M594 254c5-1 9-3 13-5v1l-2 3c-1 0-3 1-4 1h-2c-2 1-3 1-5 0z" class="O"></path><path d="M544 235h1c0 1 1 2 1 3h-1v-1c-1 1 0 1-1 2h0v1c-1 0-1 0-1 1l1 1h-4c-3 0-5 1-7 1 1-1 2-1 3-1v-1c1 0 1 0 2-1l1-1h1c1-1 3-2 3-3 1-1 0-1 1-1z" class="b"></path><path d="M550 249h0c3 1 5 2 7 3 4 2 7 2 11 3 0 1 0 2 1 2s1 0 2 1h-4c-3-1-6-2-8-3l-10-6h1z" class="K"></path><path d="M528 242h4c2-1 4-2 6-4 1-1 4-4 5-4l1 1c-1 0 0 0-1 1 0 1-2 2-3 3h-1l-1 1c-1 1-1 1-2 1v1c-1 0-2 0-3 1-1 0-1 1-2 1 2 1 3 0 5 0h0l3 1c-1 0-10 1-11 0v-3z" class="Z"></path><path d="M594 254c2 1 3 1 5 0h2c-7 4-17 4-25 4h0-2v-1h3 1l16-3z" class="X"></path><path d="M577 257h1 5-1c-1 0 0 0-1 1h-5-2v-1h3z" class="O"></path><path d="M571 252h11c6 0 11-3 17-5 3-1 7-3 10-5 1-1 2-2 2-4v-1h1c1 1 1 2 2 4-12 8-28 13-42 12v-1h-1z" class="N"></path><path d="M545 235h0v-1c1 0 2-1 3-1 0 0 1 0 2 1h2c3 3 6 5 9 8-1 0-1 0-2-1v1c-4-2-5-4-8-5h-1l1 1 1 1c1 3 4 4 5 7 2 3 11 5 14 6h0 1c-7-1-14-2-19-7-3-2-5-4-7-7 0-1-1-2-1-3z" class="k"></path><path d="M150 160c-1-3-3-5-5-7-3-3-8-5-13-6v-1c2-2 4-4 6-5l-15-6c-3-1-7-1-10-3l1-1c1-1 3-2 5-3 6-3 11-4 18-5l2 1s0 1 1 2c1 0 1 2 2 3 1 3 3 7 5 10 1 2 3 4 5 6l9 12h0c1 3 4 8 3 12 1 2 2 5 2 8 1 3 1 7 1 10v6c0 1-1 2-2 3l-1 2v1l-1 2-2-2-1-1-1 1c-3 1-8-2-11-3l-1-1s-1 0-1-1h1v-1l2-12v-2s1 0 0-1c0 0-2-2-3-2-1-2-3-3-4-5l6-1c1-1 3-2 3-3 1-2 0-5-1-7z" class="B"></path><path d="M164 170v-1c1 2 2 5 2 8 1 3 1 7 1 10v6c0 1-1 2-2 3 1-4 0-10 0-14s-1-8-1-12z" class="X"></path><path d="M140 126c1 0 1 2 2 3 1 3 3 7 5 10 1 2 3 4 5 6l9 12h0c1 3 4 8 3 12v1c-1-4-2-9-4-12-3-4-6-7-9-11-5-6-9-13-11-21z" class="U"></path><defs><linearGradient id="AA" x1="161.099" y1="188.869" x2="147.68" y2="191.551" xlink:href="#B"><stop offset="0" stop-color="#161616"></stop><stop offset="1" stop-color="#333232"></stop></linearGradient></defs><path fill="url(#AA)" d="M147 193l2-12c1 1 3 1 4 2 2 1 8 5 9 6 1 2 1 7 0 9v1c1 0 1-1 2-1v1l-1 2-2-2-1-1-1 1c-3 1-8-2-11-3l-1-1s-1 0-1-1h1v-1z"></path><path d="M147 193s0 1 1 1h2c4 1 8 3 10 4l-1 1c-3 1-8-2-11-3l-1-1s-1 0-1-1h1v-1z" class="D"></path><defs><linearGradient id="AB" x1="161.211" y1="167.715" x2="150.752" y2="178.789" xlink:href="#B"><stop offset="0" stop-color="#0a0a0a"></stop><stop offset="1" stop-color="#2e2d2e"></stop></linearGradient></defs><path fill="url(#AB)" d="M150 160c1 0 3-1 4 0 0 0 0 1 1 1 1 2 3 3 4 5 1 1 1 3 2 5 1 5 1 10 1 16l-5-4c-2-1-5-4-8-4 0 0 1 0 0-1 0 0-2-2-3-2-1-2-3-3-4-5l6-1c1-1 3-2 3-3 1-2 0-5-1-7z"></path><path d="M499 70c1 4 1 10 0 14-1 9-2 19 0 28l5-2h0 1v1l-1 4h0c0 1 0 1 1 2h0 1c1-1 3-2 4-2l-1 2-2 1h0c0 1-1 2-2 3v2 3s0 1-1 1c1 0 1 1 1 1h1l-2 1c1 1 1 2 3 3h1 1 1l1 2v3c1-1 2-1 3-1s1 0 2-1h1l-1 1h-2v2l2-1h1 0l-2 2c-1 1-2 1-2 3l-2 1c0 1 1 3 1 4 0 2-3 7-3 8l1-1c0 2-1 3-1 4l-3 5v1l-5-7c-1-1-2-3-4-5v-1l-3-3-2-3-3-3-2-2c-2-2-4-4-6-7-2-1-3-2-5-3l3-10c1-4 2-16 4-19l3 1c1-1 2-3 2-5 0-1 1-3 2-4l6-13h0c0-1 1-3 1-4s0-1 1-2v-3l1-1z" class="F"></path><path d="M497 128c1 5 1 10 1 15 0 3-1 6-1 8l-3-3c1-3 2-6 2-10 0-1 1-2 1-4v-6z" class="g"></path><path d="M499 70c1 4 1 10 0 14 0-1-1-2 0-4v-1-5 1c-1 2-3 4-3 6h1 1v1 1c-1 1-1 3-2 5v-1-2c-1 2-1 4-2 5-2 2-4 6-5 9 0-1 0-1-1-2 0-1 1-3 2-4l6-13h0c0-1 1-3 1-4s0-1 1-2v-3l1-1z" class="Q"></path><path d="M504 110h0 1v1l-1 4h0c0 1 0 1 1 2h0 1c1-1 3-2 4-2l-1 2-2 1c-1 0-1 1-2 1h0c-3 2-3 4-3 7-1 5 1 10 0 15-1 4 0 8 0 12 1 1 2 3 2 5-1-1-1-1-1-2-1 0-1 0-1-1-1 0-1-1-1-2-1-1-1-1-1-3 0-4 1-8 1-12 0-3-1-6-1-9v-3c1-3 1-5 1-7 1-3 3-6 3-9z" class="J"></path><path d="M504 158c0-2-1-4-2-5 0-4-1-8 0-12 1-5-1-10 0-15 0-3 0-5 3-7h0c1 0 1-1 2-1h0c0 1-1 2-2 3v2 3s0 1-1 1c1 0 1 1 1 1h1l-2 1v1c-1 1-1 1-1 2v1c1 2 1 7 1 8v9c1 3 1 5 2 8v2 3 1l-5-7 1 1h1s0 1 1 1v-1z" class="f"></path><defs><linearGradient id="AC" x1="504.907" y1="143.651" x2="511.592" y2="144.921" xlink:href="#B"><stop offset="0" stop-color="#545454"></stop><stop offset="1" stop-color="#6f6e6f"></stop></linearGradient></defs><path fill="url(#AC)" d="M504 130v-1c1 1 1 2 3 3h1 1 1l1 2v3c1-1 2-1 3-1s1 0 2-1h1l-1 1h-2v2l2-1h1 0l-2 2c-1 1-2 1-2 3l-2 1c0 1 1 3 1 4 0 2-3 7-3 8l1-1c0 2-1 3-1 4l-3 5v-3-2c-1-3-1-5-2-8v-9c0-1 0-6-1-8v-1c0-1 0-1 1-2z"></path><path d="M511 143v-3c1-1 3-1 4-1-1 1-2 1-2 3l-2 1z" class="T"></path><path d="M506 160v-3c1 0 1-1 1-2v-1c1-1 0-1 0-2h1v4c0 1 0 1 1 2l-3 5v-3z" class="O"></path><path d="M504 130v-1c1 1 1 2 3 3h1 1 1l1 2v3c-1 1-2 1-3 1-1 1-2 1-2 2h0 0-1l1-2h0c0-1-1-1-1-1v-3l1-1-2-1v-2z" class="R"></path><path d="M510 132l1 2v3c-1 1-2 1-3 1l1-2v-4h1zm-21-33c1-3 3-7 5-9 1-1 1-3 2-5v2 1c-3 14 1 27 1 40v6c0 2-1 3-1 4 0 4-1 7-2 10l-2-3-3-3-2-2c-2-2-4-4-6-7-2-1-3-2-5-3l3-10c1-4 2-16 4-19l3 1c1-1 2-3 2-5 1 1 1 1 1 2z" class="T"></path><defs><linearGradient id="AD" x1="491.928" y1="144.275" x2="492.525" y2="133.292" xlink:href="#B"><stop offset="0" stop-color="#474647"></stop><stop offset="1" stop-color="#676666"></stop></linearGradient></defs><path fill="url(#AD)" d="M489 142c1-1 1 0 2 0v-2c-1-1-1-2 0-3v-1-1h0 0c0-1 0-1 1-2h1l1 5c0 1 0 6-1 7h-1l-3-3z"></path><path d="M488 119l1-4v2h1v-2c1 3 1 5 1 8h-1c0 2 0 3-1 4v1c-1 3-2 4-5 6h-2 0l1-1 2-1c3-3 3-9 3-13z" class="d"></path><path d="M496 87v1c-3 14 1 27 1 40v6c0 2-1 3-1 4 0 4-1 7-2 10l-2-3h1c1-1 1-6 1-7v1c1 1 1 5 0 6v1h0 0v-3c1-1 1-4 1-6 1-1 1-4 1-6l-1-19c0-3-1-7-1-11v-6c1 0 0-2 0-3 1-1 1-2 1-2v-1s0-1 1-2z" class="R"></path><path d="M488 97c1 1 1 1 1 2-1 3 0 7 0 10s-1 6-1 10 0 10-3 13l-2 1-1 1h0l-1-1c-2-1-3-2-5-3l3-10c1-4 2-16 4-19l3 1c1-1 2-3 2-5z" class="B"></path><defs><linearGradient id="AE" x1="165.506" y1="94.174" x2="151.341" y2="113.009" xlink:href="#B"><stop offset="0" stop-color="#747373"></stop><stop offset="1" stop-color="#919090"></stop></linearGradient></defs><path fill="url(#AE)" d="M119 60h0 1v-2 1 1l1-1c0 2 0 3 1 4l2-1c4 5 7 10 12 14 4 3 9 6 13 9v1h0l3 2h-1v1c-1-1-2-1-3-2 0 0-1 0-1 1v-1h-1c-1-1-3-3-4-3 1 1 4 3 4 5h0l2 2 1 1v-1l4 4 8 6c2 2 5 4 7 6 5 4 11 7 17 10v1c3 1 7 5 10 4v-1-1l3 2h1 0c2 1 3 2 4 3h-1-1c0 1-1 1-1 2l-1-1h-2v4h0l1-1c2 1 3 1 5 2l1 1c-1 0-2 1-3 1-1 1-1 2-1 3h0v1c-1 0-1 1-2 1h0c-1 0-2 1-3 1v1 1c-2-1-5-1-7-1-1 0-1 0-2 1v-1c-1-2-2-3-3-4s-3-1-4-2l-17-10h-1 0c-3-1-5-4-7-7v1l-1-1c0-2-2-7-4-9-2-1-4-3-5-5-2-1-3-2-4-4h0l-1-1 1-1c-1-1-3-2-3-3l-6-6v-1h-1c-4-4-7-11-9-17-1-3-2-6-2-10z"></path><path d="M160 120l4 3-2 1h-1c0-1-1-2-2-2l1-2z" class="k"></path><path d="M180 132c1 1 3 2 4 3l-1 1c-1-1-3-1-4-2 0 0 0-1 1-2z" class="b"></path><path d="M184 135c2 2 3 2 4 5-1 0-1 0-2 1v-1c-1-2-2-3-3-4l1-1z" class="Y"></path><path d="M151 107c1-1 0-1 2-2 1 1 2 1 2 2 0 2-1 3-3 4l-1-4z" class="j"></path><path d="M153 112c1 0 1 0 2 1 2 3 3 5 5 7l-1 2c-3-3-5-6-6-10z" class="N"></path><path d="M159 104v1l-1 1v1c1 0 2 0 3 1-1 0-1 0-1 1s0 3 1 4l3 3s1 1 2 1c-1 1 0 1-1 1v1c-1-1-2-3-3-4s-4-2-4-5v-1l-1-1c0-2 1-3 2-4z" class="W"></path><path d="M122 64c2 4 3 8 5 11 1 3 4 5 5 8-3-2-5-5-7-8-1-1 0-1-1-1h0l-1-1c-1-2-1-6-1-9z" class="d"></path><path d="M164 123c5 4 10 6 16 9h0c-1 1-1 2-1 2l-17-10 2-1z" class="C"></path><path d="M198 122h1 0c2 1 3 2 4 3h-1-1c0 1-1 1-1 2l-1-1h-2v4h0c1 1 1 2 0 3 0 1-1 2-3 3-1 0-1-1-1-1-1-1-2-1-2-1-1 0-2-1-2-1h5 1c2-2 0-3 2-5l-1-3c0-1 1-1 1-2 1 0 0 0 1-1h0z" class="H"></path><path d="M199 122c2 1 3 2 4 3h-1-1c0 1-1 1-1 2l-1-1h-2c0-1 0-1 1-2h1v-2z" class="G"></path><path d="M119 60h0 1v-2 1 1l1-1c0 2 0 3 1 4v1c0 3 0 7 1 9l1 1c2 5 5 10 9 14l-2-1h-1c-4-4-7-11-9-17-1-3-2-6-2-10z" class="D"></path><path d="M131 87l2 1 16 15 4 2c-2 1-1 1-2 2l-3-3-8-7c-1-1-3-2-3-3l-6-6v-1z" class="Z"></path><path d="M148 104c-1 0-1-1-1-1v-1c0 1 1 1 2 1h0l4 2c-2 1-1 1-2 2l-3-3z" class="b"></path><path d="M140 97l8 7 3 3 1 4 1 1c1 4 3 7 6 10 1 0 2 1 2 2h0c-3-1-5-4-7-7v1l-1-1c0-2-2-7-4-9-2-1-4-3-5-5-2-1-3-2-4-4h0l-1-1 1-1z" class="F"></path><path d="M153 95l8 6c2 2 5 4 7 6 5 4 11 7 17 10v1c3 1 7 5 10 4v-1-1l3 2h0c-1 1 0 1-1 1 0 1-1 1-1 2l1 3c-2 2 0 3-2 5h-1-5l-4-2c-2-1-5-2-7-4 2 0 3 2 4 2h0 2 1c1 1 0 1 1 0-3-2-6-3-9-5l-1-1c-2-1-4-3-6-3-1-1-1-1-2-1l-2-2c-1 0-2-1-2-1l-3-3c-1-1-1-3-1-4s0-1 1-1c-1-1-2-1-3-1v-1l1-1v-1h-1c0-2 0-3-1-5h-1c-1-2-2-2-3-4z" class="d"></path><path d="M170 113c0 1 1 1 2 2 1 0 2 1 3 2h1 1 0c2 0 4 0 6 1h1l1 1h1-3-2c-1-1-3-1-4-1s-2 0-3-1l-1 1c-1-1-1-1-2-1-1-1-2-1-3-2h1c1-1 1-1 1-2z" class="D"></path><path d="M171 117h-3l-1-1h-1l-1-1-2-2c-1-2-2-2-3-4l1-1c2 1 5 1 6 1 0 1 0 2-1 2v1h3l1 1c0 1 0 1-1 2h-1c1 1 2 1 3 2z" class="C"></path><path d="M168 115c-2-1-4-2-6-4l1-1h0c1 0 2 1 3 2h3l1 1c0 1 0 1-1 2h-1z" class="Z"></path><path d="M173 118l1-1c1 1 2 1 3 1s3 0 4 1h2 3s1 0 1 1v-1c-1 0-1 0-2-1v-1 1c3 1 7 5 10 4v-1-1l3 2h0c-1 1 0 1-1 1 0 1-1 1-1 2l1 3c-2 2 0 3-2 5h-1l-2-1-3-1c0-1-1-1-1-1-4-3-9-5-12-10l-3-2z" class="I"></path><path d="M194 124c2 1 1 3 1 5h0-1c-1-1-3-1-5-2 1 0 2 0 3-1l2 2v-4z" class="b"></path><path d="M173 118l1-1c1 1 2 1 3 1 3 2 6 2 9 3-3 0-6 0-8-1h-1 0l1 1-2-1-3-2z" class="Y"></path><path d="M178 121l-1-1h0 1c2 1 5 1 8 1 1 1 1 0 2 0 1 1 2 1 3 1 1 1 2 2 3 2v4l-2-2c-1 1-2 1-3 1l-11-6z" class="l"></path><defs><linearGradient id="AF" x1="543.681" y1="81.021" x2="557.329" y2="106.189" xlink:href="#B"><stop offset="0" stop-color="#616161"></stop><stop offset="1" stop-color="#949393"></stop></linearGradient></defs><path fill="url(#AF)" d="M586 57h1c0 6-1 13-4 18 1 0 1 1 2 1-1 2-3 5-5 7-1 2-3 4-4 5l-8 9c-1 1-3 4-4 4-1 1-2 3-3 3l-3 3-2 2c-1 0-1 0-2-1h-1c-1 1-1 1-2 1s-1 1-2 1v1h-1v1c-1 0-1 1-2 1l-1 1c0 1-1 1-1 2-2 0-4 2-6 3-5 3-9 8-16 11l-2 2c0 1-1 1-2 2l-1 1h-1c-1 1-1 1-2 1s-2 0-3 1v-3l-1-2h-1-1-1c-2-1-2-2-3-3l2-1h-1s0-1-1-1c1 0 1-1 1-1v-3-2c1-1 2-2 2-3h0l2-1 1-2c-1 0-3 1-4 2h-1 0c-1-1-1-1-1-2h0l1-4v-1h-1l16-6 3-1 18-8c17-8 38-19 45-38z"></path><path d="M520 104l3-1h1c-1 2-3 2-4 3v-1h0l1-1h-1z" class="L"></path><path d="M560 103l3-3 1 1c-1 1-2 3-3 3 0 0-1 0-1-1z" class="C"></path><path d="M560 103c0 1 1 1 1 1l-3 3-1-2 3-2z" class="D"></path><path d="M554 108v-3h3l1 2-2 2c-1 0-1 0-2-1z" class="N"></path><path d="M580 83h0v-1c1-1 1 0 1-1-1 0-1 0-2 1l4-7c1 0 1 1 2 1-1 2-3 5-5 7z" class="d"></path><path d="M512 111l-3 3s-1 0-1 1h1v-1c1 0 1 0 2-1h1 0v-1c4 0 9-4 12-6-1 2-3 3-4 4-4 3-8 5-11 7l1-2c-1 0-3 1-4 2v-1c0-2 1-3 2-4s2-1 4-1z" class="U"></path><defs><linearGradient id="AG" x1="509.034" y1="105.52" x2="514.098" y2="110.019" xlink:href="#B"><stop offset="0" stop-color="#221f23"></stop><stop offset="1" stop-color="#4a4949"></stop></linearGradient></defs><path fill="url(#AG)" d="M504 110l16-6h1l-1 1h0v1c-2 1-6 3-8 5h0c-2 0-3 0-4 1s-2 2-2 4v1h-1 0c-1-1-1-1-1-2h0l1-4v-1h-1z"></path><path d="M520 110l-2 2c0 2-2 2-3 3-2 2-5 3-5 6v1l-1 1c0 1 0 1-1 2h2 1v1c-1 0-1 1-2 1h0l-1 2 2 2v1h-1-1-1c-2-1-2-2-3-3l2-1h-1s0-1-1-1c1 0 1-1 1-1v-3-2c1-1 2-2 2-3h0l2-1c3-2 7-4 11-7z" class="T"></path><path d="M505 128c1-1 2-2 3-2h0c0 1 0 1-1 2 0 0 0 3 1 3s1 0 2 1h-1-1-1c-2-1-2-2-3-3l2-1h-1z" class="X"></path><path d="M522 115c2-1 4-2 7-3 9-6 16-12 25-19-2 3-4 4-7 6-6 6-12 11-19 15-5 3-11 7-17 9 3-4 7-6 11-8z" class="Y"></path><path d="M532 115c2-2 5-3 8-5 2-1 5-4 7-4 1 1 1 2 0 4 0 2-2 4-3 6-2 0-4 2-6 3-5 3-9 8-16 11l-2 2c0 1-1 1-2 2l-1 1h-1c-1 1-1 1-2 1s-2 0-3 1v-3l-1-2v-1l-2-2 1-2h0c1 0 1-1 2-1v-1h-1v-1h0v-1c1-1 2-2 2-3h1c0-1 1-1 2-2 1 0 2-1 3-2h-1c1-1 2-1 3-1h2c-4 2-8 4-11 8 6-2 12-6 17-9v1l-1 1h3v-1h2z" class="P"></path><path d="M520 128h0c-1 1-1 1-2 1s-1 1-2 1c-2 0-2 0-3-1 0-2 0-4 1-5s2-1 3-1l-3 4 2 2 1-1h3z" class="C"></path><path d="M532 115h2l1 1c0-1 0 0 1-1l1 1c-1 1-1 2-2 3l-2 1-1-2c-1 1-2 1-3 2h-1c0-1 0-1-1-1 0 1-1 2-2 2h0l-1-2c1 0 1-1 2 0 2-2 4-2 6-4z" class="D"></path><path d="M532 118l1-1c1 0 1 1 2 2l-2 1-1-2z" class="N"></path><defs><linearGradient id="AH" x1="512.829" y1="136.57" x2="517.314" y2="129.788" xlink:href="#B"><stop offset="0" stop-color="#7e7d7e"></stop><stop offset="1" stop-color="#9e9d9d"></stop></linearGradient></defs><path fill="url(#AH)" d="M510 124h2v1c2 2-1 6 2 6h3 1 1l1-1h2 0l-2 2c0 1-1 1-2 2l-1 1h-1c-1 1-1 1-2 1s-2 0-3 1v-3l-1-2v-1l-2-2 1-2h0c1 0 1-1 2-1v-1h-1v-1z"></path><path d="M510 124h2v1c-1 1-2 2-1 4 0 1 0 2 1 3 0 1-1 2-1 2l-1-2v-1l-2-2 1-2h0c1 0 1-1 2-1v-1h-1v-1z" class="K"></path><path d="M524 119l1 2h0c1 0 2-1 2-2 1 0 1 0 1 1h1c1-1 2-1 3-2l1 2c-3 2-6 4-10 6-1 1-2 1-3 2h-3l-1 1-2-2 3-4c1-1 2-1 3-2 2 0 3-1 4-2z" class="Y"></path><defs><linearGradient id="AI" x1="199.696" y1="210.916" x2="164.129" y2="230.811" xlink:href="#B"><stop offset="0" stop-color="#c0bfc0"></stop><stop offset="1" stop-color="#fbfafa"></stop></linearGradient></defs><path fill="url(#AI)" d="M182 183h4c4 3 7 4 10 9 1 1 2 3 2 5 1 4 1 8 1 12h1v-1c1 1 1 1 2 1 0 1-1 3-2 4-1 3-2 6-4 8l-1 2v6 5 1c1 2 1 3 2 4h1v5c-1-1 0-2-2-3h-2l-4-1h-10c-5-1-10-3-14-7h0c0-2-3-5-5-6-1 1-1 1-1 2l-1 2h-2c0 1 0 1-1 1 1-1 1-1 1-3h0l3-7v-4l-1 1h-1l-1 2c0 1 0 2-1 2l2-4-1-1c1-2 3-5 3-7 0-4 3-8 4-12v-1l1-2c1-1 2-2 2-3l7-7h1l3-1 4-2z"></path><path d="M195 200c2 5 0 11-3 16h0-1-1v-1c0-1 1-4 2-5v-1c1 0 1 1 2 1v-1c0-1 1-1 1-2v-7z" class="N"></path><path d="M192 210h1c0 2-1 4-2 6h-1v-1c0-1 1-4 2-5z" class="Y"></path><path d="M183 229l6 9-9-2c0-1-1-3-1-4h2c1-1 1-2 2-3z" class="D"></path><path d="M182 197c2 1 3 2 4 4v2c-1 1-2 2-3 4l-6 8c0-2 3-8 4-10h1v-1l1-1c-1-2-1-2-3-3-1 1-2 3-3 4h-1c2-3 4-5 6-7z" class="I"></path><path d="M190 199v3-1 6 2 2h-1c-1 2 0 4-3 5-3 3-7 5-11 6-2 2-3 3-5 4l7-11 6-8c1-2 2-3 3-4v-2h0c1-1 2-2 4-2z" class="k"></path><path d="M190 199v3-1 6h-1v-2h0c-2 1-3 1-5 3 0-1-1-1-1-1 1-2 2-3 3-4v-2h0c1-1 2-2 4-2z" class="R"></path><path d="M190 199v3h-1c-1 0-2 1-2 1h-1v-2h0c1-1 2-2 4-2z" class="f"></path><defs><linearGradient id="AJ" x1="177.708" y1="208.725" x2="177.063" y2="222.721" xlink:href="#B"><stop offset="0" stop-color="#6d6b6c"></stop><stop offset="1" stop-color="#959493"></stop></linearGradient></defs><path fill="url(#AJ)" d="M177 215l6-8s1 0 1 1c-2 3-4 7-6 10-1 1-2 3-3 4-2 2-3 3-5 4l7-11z"></path><path d="M192 191c1 0 2 0 2 1h1 0 1c1 1 2 3 2 5 1 4 1 8 1 12h1v-1c1 1 1 1 2 1 0 1-1 3-2 4-1 3-2 6-4 8l-1 2v6 5 1c1 2 1 3 2 4h1v5c-1-1 0-2-2-3h-2l-4-1-1-2-6-9h-1v-1c4-3 7-8 10-12h0c3-5 5-11 3-16v-1c-1-3-2-4-2-6-1-1-2-2-3-2h2z" class="m"></path><path d="M192 191c1 0 2 0 2 1h1 0 1c1 1 2 3 2 5-1-1-3-1-3-2-1-2-3-3-3-4z" class="Y"></path><path d="M199 209h1v-1c1 1 1 1 2 1 0 1-1 3-2 4l-3 2 2-6z" class="X"></path><path d="M197 215l3-2c-1 3-2 6-4 8l-3 2 4-8z" class="U"></path><path d="M196 241c0-4-7-7-8-11 0 0 1-2 2-2 0 1 1 2 2 3s2 3 3 4c1 2 1 3 2 4h1v5c-1-1 0-2-2-3z" class="Q"></path><path d="M196 221l-1 2v6 5 1c-1-1-2-3-3-4s-2-2-2-3l3-5 3-2z" class="J"></path><path d="M195 223v6 5c-1-2-1-4-3-5h0c0-3 2-4 3-6z" class="V"></path><defs><linearGradient id="AK" x1="177.786" y1="211.908" x2="166.814" y2="190.916" xlink:href="#B"><stop offset="0" stop-color="#b3b1b2"></stop><stop offset="1" stop-color="#d5d4d4"></stop></linearGradient></defs><path fill="url(#AK)" d="M182 183h4c4 3 7 4 10 9h-1 0-1c0-1-1-1-2-1h-2c1 0 2 1 3 2 0 2 1 3 2 6h0v2h-1-1c-1-1-2-1-3-1v1 1-3c-2 0-3 1-4 2h0c-1-2-2-3-4-4-2 2-4 4-6 7l-15 23c-1 1-1 1-1 2l-1 2h-2c0 1 0 1-1 1 1-1 1-1 1-3h0l3-7v-4l-1 1h-1l-1 2c0 1 0 2-1 2l2-4-1-1c1-2 3-5 3-7 0-4 3-8 4-12v-1l1-2c1-1 2-2 2-3l7-7h1l3-1 4-2z"></path><path d="M186 192c2 2 3 4 4 7-2 0-3 1-4 2h0c-1-2-2-3-4-4 0-1 1-2 1-3 1-1 2-1 3-2z" class="Q"></path><defs><linearGradient id="AL" x1="163.571" y1="199.109" x2="175.065" y2="223.17" xlink:href="#B"><stop offset="0" stop-color="#020004"></stop><stop offset="1" stop-color="#40413d"></stop></linearGradient></defs><path fill="url(#AL)" d="M179 191c3-1 3-1 5 0l2 1c-1 1-2 1-3 2 0 1-1 2-1 3-2 2-4 4-6 7l-15 23c-1 1-1 1-1 2l-1 2h-2c0 1 0 1-1 1 1-1 1-1 1-3h0l3-7v-4c2-3 3-6 5-9 3-6 7-11 11-16 1-1 2-2 3-2z"></path><path d="M176 193c1-1 2-2 3-2v5c-1 2-2 4-4 6 0 1-1 2-2 2 1-1 4-5 4-6 0-2-1-4-1-5z" class="J"></path><path d="M160 218c2-3 3-6 5-9 3-6 7-11 11-16 0 1 1 3 1 5 0 1-3 5-4 6-4 6-9 11-12 17l-1 1v-4z" class="U"></path><path d="M270 128l14-9c10-4 20-5 30-2l4 2c1 0 3 1 4 0 1 2 3 3 4 5l-1 1 2 3-1 1 3 3 1 8c0 1 1 1 1 2h-1c-1 1-3 2-4 2 2 1 4 2 4 4s0 4-1 5-1 1-3 2h-1v-1l-17-5-7-1h-7-5l-1-1-1 1h-1c-1-1-2-1-3-1l-1 1h-2c-1 0-1 0-2 1-1 0-2-1-3 0h-2l-1-1-5-3v-1l1-1c-1-2-2-4-2-7-1-1-1-2-1-3l-1-1h1l1 1v-1c1-2 2-3 4-4h0z" class="B"></path><path d="M317 147h1v2c-1 0-1-1-1-1v-1z" class="Q"></path><path d="M323 126l3 3 3 3 1 8c-2-4-5-6-7-10-1-1-2-2-2-3l2-1z" class="J"></path><path d="M277 147v-12l6 6c-3-1-3-2-5-4 0 1 3 4 4 5h0l2 2-3 1-4 2z" class="P"></path><path d="M279 140c1 0 2 2 3 3 1 0 0 0 1 1h-2c0 1-1 1-2 1h0v-2-3z" class="H"></path><path d="M308 149h0c1 0 2 0 3-1 2-3 3-10 7-11h2c2 1 2 3 3 4l-1 1c-1-1-1-1-1-2-1-1 0-2-1-3-1 0-2 1-3 1v1h1v1c-1 1-1 1-2 3 0 1-1 2-1 3-1 1-3 2-3 3s1 1 1 1h1c1 0 3 1 4 1s4 1 4 2c1 0 2 0 2 1h1l-17-5z" class="V"></path><defs><linearGradient id="AM" x1="314.938" y1="117.888" x2="309.158" y2="126.27" xlink:href="#B"><stop offset="0" stop-color="#777676"></stop><stop offset="1" stop-color="#acabac"></stop></linearGradient></defs><path fill="url(#AM)" d="M314 117l4 2c1 0 3 1 4 0 1 2 3 3 4 5l-1 1 2 3-1 1-3-3c-8-5-14-6-23-6l-2-1 2-1c3 0 6-1 8 0s4 0 5 0c0 0 1 0 1-1z"></path><path d="M322 119c1 2 3 3 4 5l-1 1c-2-3-5-4-7-6 1 0 3 1 4 0z" class="F"></path><path d="M283 141h2c2 0 3 0 4-1 1-2 1-4 1-6h0c1 1 2 3 4 4h2c2-1 5-3 6-6 1-2 0-3-1-5h0 0c2 0 2 0 3 1h0l2-4h0l1 4h0l1-1c2-1 4 0 5 0 0 2 0 5-1 7-1-1-1-2-2-2h-3-1c-5 7-12 11-20 13h-5l3-1-2-2h0c-1-1-4-4-4-5 2 2 2 3 5 4z" class="D"></path><path d="M281 145h5c8-2 15-6 20-13h1 3c1 0 1 1 2 2-3 6-5 10-11 14h-7-5l-1-1-1 1h-1c-1-1-2-1-3-1l-1 1h-2c-1 0-1 0-2 1-1 0-2-1-3 0h-2l-1-1-5-3v-1l1-1c2 2 4 4 6 4h3l4-2z" class="Y"></path><path d="M270 128l14-9c10-4 20-5 30-2 0 1-1 1-1 1-1 0-3 1-5 0s-5 0-8 0l-2 1 2 1-8 2c-8 3-14 7-21 11-1 1-3 3-5 3h0c-1-1-1-2-1-3l-1-1h1l1 1v-1c1-2 2-3 4-4h0z" class="c"></path><path d="M271 133v-1h0c0-1 1-2 2-3s3-2 5-4c6-4 15-7 22-7l-2 1 2 1-8 2c-8 3-14 7-21 11z" class="d"></path><path d="M292 122l-1-1h1 0c-2 0-5 1-6 2h-2 1c2-2 6-3 9-3 1-1 3-1 4-1l2 1-8 2z" class="P"></path><path d="M520 189c3-1 6-2 10-1 3 1 6 4 9 7v-1l1 1v-1c2 2 3 4 6 6v1l2-1h0c4 2 7 9 8 13l1 3h1v2s1-1 1-2c1 3 1 5 2 7s2 4 2 6l3 3c5 5 10 8 16 10 3 1 7 1 9 2l-2 1c-1 0-2 1-3 1-5 2-11 1-17 0l-8-4c-3-3-6-5-9-8h-2c-1-1-2-1-2-1-1 0-2 1-3 1v1h0-1l-1-1c-1 0-4 3-5 4-2 2-4 3-6 4h-4c1-1 1-1 0-2h-5-3c0-4 2-6 4-9 1-1 1-2 2-4h2c1 0 2 0 2-1-1 0-2-1-3-2-2-1-5-4-6-6 0-1 0-1 1-2h0c-1-1-3-4-5-5l-1-1c-2-1-3-4-3-7s1-7 3-9v-1l4-4z" class="B"></path><path d="M535 198l1-1c1 0 1 1 2 2v1c-1 1-2 1-3 1v1 1c-1 0-1-1-1-1v-1c1 0 1-1 2-2l-1-1z" class="X"></path><path d="M535 203v-1-1c1 0 2 0 3-1 3 3 5 6 7 10 0 1 1 1 0 2h0c-3-4-6-8-10-9z" class="N"></path><path d="M520 189c3-1 6-2 10-1 3 1 6 4 9 7 2 3 4 7 5 11 1 0 2 2 2 3-1 0-1 0-1 1-2-4-4-7-7-10v-1c-1-1-1-2-2-2l-1 1c0-1-1-2-1-2-2-3-4-4-8-6-2 1-4 2-6 4-1 1-1 3-2 4v-2c0-1 0-2 1-3v-1-1l-1 1 2-2h0v-1z" class="O"></path><path d="M526 190c2 0 2 0 3 1l2 1v-2l2 2 5 5v2c-1-1-1-2-2-2l-1 1c0-1-1-2-1-2-2-3-4-4-8-6z" class="R"></path><path d="M539 195v-1l1 1v-1c2 2 3 4 6 6v1l10 23-1-1h-1s0 1-1 1c0-1-1-1-2-2h-2 0l-4-10h0c1-1 0-1 0-2s0-1 1-1c0-1-1-3-2-3-1-4-3-8-5-11z" class="G"></path><path d="M548 200c4 2 7 9 8 13l1 3h1v2s1-1 1-2c1 3 1 5 2 7s2 4 2 6l3 3c5 5 10 8 16 10 3 1 7 1 9 2l-2 1c-1 0-2 1-3 1-5 2-11 1-17 0l-8-4c-3-3-6-5-9-8v-1h0l2 2s1 0 1 1v-1-1h-1c-1-2-1-2-1-4v-1l-4-7h2c1 1 2 1 2 2 1 0 1-1 1-1h1l1 1-10-23 2-1h0z" class="B"></path><path d="M548 200c4 2 7 9 8 13l1 3h1v2s1-1 1-2c1 3 1 5 2 7s2 4 2 6c-1-2-2-3-4-5 0-1 0-3-1-4s-2-2-3-4c-2-5-4-11-7-16h0z" class="g"></path><defs><linearGradient id="AN" x1="570.008" y1="224.045" x2="567.97" y2="245.15" xlink:href="#B"><stop offset="0" stop-color="#8c8a8c"></stop><stop offset="1" stop-color="#c6c6c6"></stop></linearGradient></defs><path fill="url(#AN)" d="M553 224c1 0 1-1 1-1h1l1 1c2 3 5 7 8 10 5 6 11 9 18 10l7 1c-1 0-2 1-3 1-5 2-11 1-17 0l2-1h0 1 4c-9-4-16-9-21-18-1-1-1-2-2-3z"></path><path d="M549 222h2c1 1 2 1 2 2 1 1 1 2 2 3 5 9 12 14 21 18h-4-1 0l-2 1-8-4c-3-3-6-5-9-8v-1h0l2 2s1 0 1 1v-1-1h-1c-1-2-1-2-1-4v-1l-4-7z" class="Z"></path><path d="M549 222h2c1 1 2 1 2 2 1 1 1 2 2 3h-1c0 3 4 6 4 10-2-3-3-5-5-8l-4-7z" class="P"></path><path d="M553 229l5 8c4 3 8 7 13 8l-2 1-8-4c-3-3-6-5-9-8v-1h0l2 2s1 0 1 1v-1-1h-1c-1-2-1-2-1-4v-1z" class="E"></path><defs><linearGradient id="AO" x1="515.373" y1="226.331" x2="541.293" y2="215.817" xlink:href="#B"><stop offset="0" stop-color="#b8b7b8"></stop><stop offset="1" stop-color="#eae9e9"></stop></linearGradient></defs><path fill="url(#AO)" d="M520 189v1h0l-2 2 1-1v1 1c-1 1-1 2-1 3v2c1-1 1-3 2-4h2c1 0 1 0 2 1s3 2 3 4l15 20 5 7 5 7v1h-2c-1-1-2-1-2-1-1 0-2 1-3 1v1h0-1l-1-1c-1 0-4 3-5 4-2 2-4 3-6 4h-4c1-1 1-1 0-2h-5-3c0-4 2-6 4-9 1-1 1-2 2-4h2c1 0 2 0 2-1-1 0-2-1-3-2-2-1-5-4-6-6 0-1 0-1 1-2h0c-1-1-3-4-5-5l-1-1c-2-1-3-4-3-7s1-7 3-9v-1l4-4z"></path><path d="M522 216l3 3h0c2 1 4 4 5 5l-4-2-3-3h0c1 2 3 3 4 5-2-1-5-4-6-6 0-1 0-1 1-2z" class="D"></path><path d="M522 205l1 1c1 2 3 5 4 7v2c-1-1-1-2-2-3l-3-3h0c-1 2 1 5 1 7-2-2-4-5-4-7 1-1 1-2 1-3 1-1 2-1 2-1z" class="C"></path><defs><linearGradient id="AP" x1="531.908" y1="214.252" x2="532.674" y2="223.923" xlink:href="#B"><stop offset="0" stop-color="#8a8889"></stop><stop offset="1" stop-color="#a6a5a4"></stop></linearGradient></defs><path fill="url(#AP)" d="M527 213l2 1h1l1 1 7 10c-1 0-1 0-2-1-5-2-6-5-9-9v-2z"></path><path d="M547 226l5 7v1h-2c-1-1-2-1-2-1-1 0-2 1-3 1v1h0-1l-1-1c-1 0-4 3-5 4-2 2-4 3-6 4h-4c1-1 1-1 0-2h-5c4-1 8 0 12-1 4-2 6-5 8-8 2-2 3-3 4-5z" class="a"></path><path d="M524 205l-2-3v-3c2-1 3-1 5 0l15 20c-2-1-3-1-4-2-2-3-10-15-13-16v1 1 1c1 1 1 1 1 2s1 2 1 2h1c1 2 3 4 3 6v1l-1-1h-1l-2-1c-1-2-3-5-4-7 1 0 1 0 1-1z" class="I"></path><path d="M524 205l6 9h-1l-2-1c-1-2-3-5-4-7 1 0 1 0 1-1z" class="c"></path><defs><linearGradient id="AQ" x1="521.491" y1="198.071" x2="512.575" y2="204.055" xlink:href="#B"><stop offset="0" stop-color="#737171"></stop><stop offset="1" stop-color="#8b8b8b"></stop></linearGradient></defs><path fill="url(#AQ)" d="M520 189v1h0l-2 2 1-1v1 1c-1 1-1 2-1 3v2c1-1 1-3 2-4h2c1 0 1 0 2 1s3 2 3 4c-2-1-3-1-5 0v3l2 3c0 1 0 1-1 1l-1-1s-1 0-2 1c0 1 0 2-1 3 0 2 2 5 4 7l2 3h0l-3-3h0c-1-1-3-4-5-5l-1-1c-2-1-3-4-3-7s1-7 3-9v-1l4-4z"></path><path d="M520 194h2c1 0 1 0 2 1s3 2 3 4c-2-1-3-1-5 0v3l2 3c0 1 0 1-1 1l-1-1c0-1-1-2-2-3l-1 1-2 6c-1-3 0-8 1-11h0c1-1 1-3 2-4z" class="U"></path><path d="M520 194h2c1 0 1 0 2 1-2 0-3 0-4 2-1 1-1 2-1 3l-1 1v-1-2h0c1-1 1-3 2-4z" class="V"></path><path d="M103 193c1-1 1-1 2-1 0 3-1 8 0 10h0c-1 4-2 5-5 8-1 0 0 0 0 0l-1 1c-1 2-1 5-1 7l1 1v1c0 1 1 2 1 3h1c0 1 2 3 2 4s-1 2-1 2l-1 2-1 1v1 1c1 3 7 6 10 8 7 0 13-1 19-5l5-2-1-1c1-1 3-2 4-3s2-2 2-3v-1h3 0l2-1c1 1 2 1 3 0h1 0l1 1v1c1 0 2-3 2-3h0l1 1h1l1-1v1c2-3 2-5 3-8l1 1-2 4c1 0 1-1 1-2l1-2h1l1-1v4l-3 7h0c0 2 0 2-1 3 1 0 1 0 1-1h2l1-2c0-1 0-1 1-2 2 1 5 4 5 6h0c4 4 9 6 14 7 1 1 1 3 1 5l-1 1h-3v3c2 0 4 1 5 2l-1 1-3-2c-4-2-11-3-15-1-4 1-8 3-12 5-1 1-3 3-5 3-5 3-14 4-20 3-6 0-11-2-17-3-3-1-5-2-8-3s-6-3-8-3l-1 1h1v1c-1-1-1-1-2-1v1h1c-1 1-2 2-3 2l-1-1v-1l-2-2h0c0-2 3-5 4-6s1-2 2-2v-1c0-2 0-3 1-4l1-2h0c1-3 3-6 3-8l1-1c-1-3-2-6-3-8 0-3 0-6 2-9 1-1 2-2 2-4 2-3 2-6 3-9 0 0 1-3 1-4z" class="j"></path><path d="M144 240h2l1 1c-3 2-7 4-11 4h0c1-1 3-1 4-2 2-1 3-2 4-3z" class="W"></path><path d="M160 229c0-1 0-1 1-2 2 1 5 4 5 6h0v2h-2v-1c0-1-1-2-1-2h-1c0-2-1-2-2-3z" class="R"></path><path d="M166 233c4 4 9 6 14 7 1 1 1 3 1 5l-1 1v-1h1v-1c-1-1-4-2-5-2l-9-6c0-1-1-1-1-1v-2z" class="e"></path><path d="M97 234h1c0 1-1 2-1 4v1c6 7 16 10 24 12 1 1 2 1 3 1h1-3c-6-1-10-3-16-5-1-1-3-2-5-3s-4-2-6-4c0-1 0-2 1-2 1-2 1-3 1-4z" class="Z"></path><path d="M160 248c7-3 13-3 20-3v1h-3v3c2 0 4 1 5 2l-1 1-3-2c-4-2-11-3-15-1l-3-1z" class="B"></path><path d="M157 218l1 1-2 4c1 0 1-1 1-2l1-2h1l1-1v4l-3 7h0c0 2 0 2-1 3 1 0 1 0 1-1h2c-1 1-2 3-4 4l-3 3-5 3-1-1h-2c3-3 5-6 7-9l2-5 1-1v1c2-3 2-5 3-8z" class="X"></path><path d="M155 235h0v-1h-1c1-2 2-4 3-5h0c0 2 0 2-1 3 1 0 1 0 1-1h2c-1 1-2 3-4 4z" class="R"></path><path d="M157 218l1 1-2 4c1 0 1-1 1-2l1-2h1l-4 9c-2 3-2 5-4 7l-1 1c0 1 0 2-1 2h1 2l-5 3-1-1h-2c3-3 5-6 7-9l2-5 1-1v1c2-3 2-5 3-8z" class="d"></path><path d="M146 240c2-1 3-3 4-4 0 1 0 2-1 2h1 2l-5 3-1-1z" class="c"></path><path d="M102 251c0-1-1-2-2-2l-2-2h0l1 1c1 0 1 1 2 0l8 4c11 5 27 6 39 2 4-2 7-4 11-5l1-1 3 1c-4 1-8 3-12 5-12 5-29 6-42 1h0l-7-4z" class="e"></path><path d="M128 257h7 0l-1 1c-2 0-5 0-6-1z" class="R"></path><path d="M151 225h0l1 1h1l-2 5c-2 3-4 6-7 9-1 1-2 2-4 3-1 1-3 1-4 2h0c-1 2-7 2-9 2-3-1-6-1-8-2h7c8-2 16-7 21-14l2-3c1 0 2-3 2-3z" class="N"></path><path d="M151 225h0l1 1h1l-2 5v-2-1h0v1c-1 1-2 1-3 2h-1l2-3c1 0 2-3 2-3zm-59 18l7 4c1 0 2 1 2 1-1 1-1 0-2 0l-1-1h0l2 2c1 0 2 1 2 2l7 4h0c13 5 30 4 42-1-1 1-3 3-5 3-5 3-14 4-20 3-6 0-11-2-17-3-3-1-5-2-8-3s-6-3-8-3l-1 1h1v1c-1-1-1-1-2-1v1h1c-1 1-2 2-3 2l-1-1v-1l-2-2h0c0-2 3-5 4-6s1-2 2-2z" class="D"></path><path d="M92 243l7 4c1 0 2 1 2 1-1 1-1 0-2 0l-1-1h0l2 2c1 0 2 1 2 2l7 4c-2 0-4-1-5-2-4-1-8-4-12-3l-1 2v1h1c-1 1-2 2-3 2l-1-1v-1l-2-2h0c0-2 3-5 4-6s1-2 2-2z" class="G"></path><path d="M92 243l7 4c1 0 2 1 2 1-1 1-1 0-2 0l-1-1h0l2 2c1 0 2 1 2 2h0c-3-2-7-5-10-6h-1-1c1-1 1-2 2-2z" class="i"></path><path d="M88 253l1-1v-2l-1-1c0-1 1-1 1-1h3c3 0 6 2 10 3h0l7 4c-2 0-4-1-5-2-4-1-8-4-12-3l-1 2v1h1c-1 1-2 2-3 2l-1-1v-1z" class="P"></path><defs><linearGradient id="AR" x1="130.265" y1="231.267" x2="132.158" y2="239.684" xlink:href="#B"><stop offset="0" stop-color="#8b8b8a"></stop><stop offset="1" stop-color="#bdbcbd"></stop></linearGradient></defs><path fill="url(#AR)" d="M144 226c1 1 2 1 3 0h1 0l1 1v1l-2 3c-5 7-13 12-21 14h-7l-6-2c-1 0-2-1-3-1 7 0 13-1 19-5l5-2-1-1c1-1 3-2 4-3s2-2 2-3v-1h3 0l2-1z"></path><path d="M139 227h3 0a30.44 30.44 0 0 1-8 8l-1-1c1-1 3-2 4-3s2-2 2-3v-1z" class="f"></path><path d="M113 243h10v1c1 1 1 1 3 1h-7l-6-2z" class="V"></path><path d="M148 226l1 1v1l-2 3c-5 7-13 12-21 14-2 0-2 0-3-1v-1c10-2 18-9 25-17h0zM330 96l1-1c1-1 5-1 5-3-3-3-6-5-7-9 2-6 7-11 13-13 7-4 14-4 22-2 5 2 11 7 14 12 1 2 1 4 1 6-2 3-6 4-7 7 1 2 6 2 5 5 0 4-2 8-5 10l3 1-4 3c-1 1-2 2-4 3-3 5-8 11-9 17l-1 7h0-1l-3 3c-1 0-1-1-2-2v1h0c-1-3-2-6-1-9-2-7-6-11-9-17h-1 0-1c-1-1-1-1-1-2s0-1 1-2c0 0-1-1-2-1l1-2c-2 0-2-1-4-2 0 0-1 0-2-1-2-1-2-3-3-4 0-2 1-3 1-5z" class="B"></path><path d="M343 86h1c1 1 2 2 2 3 1 1 0 1 0 2l-1 1h-1c-1-2-1-4-1-6zm22 0v1c0 2 0 3-1 5h-1c-1-1-2-1-2-2 1-2 2-3 4-4z" class="T"></path><path d="M330 96c1 4 3 7 4 10 0 0-1 0-2-1-2-1-2-3-3-4 0-2 1-3 1-5z" class="H"></path><path d="M338 108c2 0 4-1 6-2 3-3 6-8 6-13 0-2 1-6-1-7-1-1-3-2-4-4-1-1-1-4 0-5 0-1 0-2 1-3 1 0 2-1 3 0 0 1 0 3 1 4 1 2 2 2 3 2 2 0 4 0 5-1s1-4 1-6l3 2c1 1 1 2 1 3 1 5-2 5-4 8-2 1-1 3-1 5 0 6 3 11 8 14 2 2 3 3 6 3l3 1-4 3c-1 1-2 2-4 3-3 5-8 11-9 17l-1 7h0-1l-3 3c-1 0-1-1-2-2v1h0c-1-3-2-6-1-9-2-7-6-11-9-17h-1 0-1c-1-1-1-1-1-2s0-1 1-2c0 0-1-1-2-1l1-2z" class="l"></path><path d="M354 109h1c1 1 2 1 2 2 1 1 1 2 0 3l-3 3h0c-2-1-3-2-3-3-1 0-1-1-1-2 1-1 3-2 4-3z" class="B"></path><path d="M366 105c2 2 3 3 6 3l3 1-4 3c-1 1-2 2-4 3-1-1-3-1-5-2h1l3-3v-5z" class="I"></path><path d="M362 113c2 1 4 1 5 2-3 5-8 11-9 17-1-1-2-2-2-4 1-2 1-4 2-7 1-2 3-5 4-8z" class="B"></path><path d="M352 128c1 0 2 1 3 1l1-1c0 2 1 3 2 4l-1 7h0-1l-3 3c-1 0-1-1-2-2v1h0c-1-3-2-6-1-9 1-1 2-2 2-4z" class="I"></path><path d="M340 115c2-1 3-2 5-2 3 4 6 9 7 15 0 2-1 3-2 4-2-7-6-11-9-17h-1z" class="B"></path><defs><linearGradient id="AS" x1="168.469" y1="129.773" x2="155.808" y2="141.118" xlink:href="#B"><stop offset="0" stop-color="#1e1c1d"></stop><stop offset="1" stop-color="#474647"></stop></linearGradient></defs><path fill="url(#AS)" d="M116 73v-1s0-1 1-1h0 0c1 1 1 1 1 2v1h1c1-1 1-3 2-4 2 6 5 13 9 17h1v1l6 6c0 1 2 2 3 3l-1 1 1 1h0c1 2 2 3 4 4 1 2 3 4 5 5 2 2 4 7 4 9l1 1v-1c2 3 4 6 7 7h0 1l17 10c1 1 3 1 4 2s2 2 3 4v1c1-1 1-1 2-1 2 0 5 0 7 1h1 1v3c-1 2-1 3 0 6l-1 1 3 5 1-1 1 3c0 1 1 3 2 4-1 1-1 2-2 2l-5 7-1 2c-2 3-4 6-6 8l-1 2h-2-4l-4 2-3 1h-1l-7 7v-6c0-3 0-7-1-10 0-3-1-6-2-8 1-4-2-9-3-12h0l-9-12c-2-2-4-4-5-6-2-3-4-7-5-10-1-1-1-3-2-3-1-1-1-2-1-2l-2-1h1c0-1-1-2-1-2h-1c-1-2-1-3-2-4 1-2 3-2 4-2h0c0-2 0-3-1-5-2-2-4-5-6-7-1-2-3-4-4-5-2-2-4-5-5-8l-5-14-1-3z"></path><path d="M137 121c1-1 2-1 3-1v1h1c0 1 1 1 1 2 1 1 3 2 3 4h-1v-1 1c-2-2-3-5-5-6l-1 1h0v1c0-1-1-2-1-2z" class="S"></path><path d="M177 154h1 0c1 2 4 2 6 3l-3-3v-1l3 3c1 1 3 2 4 3l2 2v1c0 1 0 1 1 2h0c-3-3-6-6-10-7l-2-1-2-2z" class="E"></path><path d="M137 105s1 1 2 1v-1l-1-1h1c3 1 8 4 9 8 1 0 1 1 1 1l1 3h0v3 1h0v1l-1-1v1c-2-1-4-3-5-5l-4-6h-1 0c-1-2-1-3-2-5z" class="L"></path><path d="M139 105l4 4c2 2 2 4 4 7v1s0 1 1 1c0 1 0 1 1 2-5-4-9-9-10-15z" class="R"></path><path d="M150 112c-4-7-19-17-20-23l1-1 6 6c0 1 2 2 3 3l-1 1 1 1h0c1 2 2 3 4 4 1 2 3 4 5 5 2 2 4 7 4 9l1 1 1 1v1c0 1 0 1 1 2l2 2h0l15 12c3 2 6 4 8 7 1 0 2 1 2 2 2 2 3 3 4 5 4 3 7 10 8 15 0 1 0 4 1 6l-1 2h-3l-1-1-1-1v-1c0-1-1-1-2-2h1c-1-2-2-3-3-4l1-1h0c-3-3-6-6-10-6 1-1 1-1 2-1l2 1c4 1 7 4 10 7h0c-1-1-1-1-1-2 1 1 1 2 3 4 0-2 0-2-1-3 0-1 0-2-1-3 0-1-1-1-1-2v-2c-1-2-3-4-4-5s-1-3-2-4l-3-3c-1-1-2-2-4-3-1-1-2-2-4-2h0c-3-2-5-4-8-6h1l-3-3c-4-3-8-8-10-12-1-2-2-5-3-6z" class="J"></path><path d="M187 163c2 2 3 5 5 8l-1 1-1-1v-1c0-1-1-1-2-2h1c-1-2-2-3-3-4l1-1z" class="e"></path><path d="M187 150c4 3 7 10 8 15 0 1 0 4 1 6l-1 2h-3c1-1 1-1 2-1l1-2c1-2-1-4-1-6-1-3-2-5-3-8-1-2-3-4-4-6h0z" class="F"></path><defs><linearGradient id="AT" x1="143.257" y1="92.525" x2="115.306" y2="84.732" xlink:href="#B"><stop offset="0" stop-color="#363536"></stop><stop offset="1" stop-color="#656565"></stop></linearGradient></defs><path fill="url(#AT)" d="M116 73v-1s0-1 1-1h0 0c1 1 1 1 1 2v1h1c1-1 1-3 2-4 2 6 5 13 9 17h1v1l-1 1c1 6 16 16 20 23l-1 1s0-1-1-1c-1-4-6-7-9-8h-1l1 1v1c-1 0-2-1-2-1h-1c0 2 2 3 1 5-2-2-4-5-6-7-1-2-3-4-4-5-2-2-4-5-5-8l-5-14-1-3z"></path><path d="M117 76v-2c1 2 1 3 2 4 1 3 2 6 4 9h-1l-1-1c0 1 1 1 1 2v2l-5-14z" class="J"></path><defs><linearGradient id="AU" x1="119.231" y1="95.699" x2="138.939" y2="99.645" xlink:href="#B"><stop offset="0" stop-color="#0b0d11"></stop><stop offset="1" stop-color="#302d2b"></stop></linearGradient></defs><path fill="url(#AU)" d="M122 90v-2c0-1-1-1-1-2l1 1h1c2 3 3 6 5 8 3 3 6 6 8 10 0 2 2 3 1 5-2-2-4-5-6-7-1-2-3-4-4-5-2-2-4-5-5-8z"></path><path d="M154 118v-1c2 3 4 6 7 7h0 1l17 10c1 1 3 1 4 2s2 2 3 4v1c1-1 1-1 2-1 2 0 5 0 7 1h1 1v3c-1 2-1 3 0 6l-1 1 3 5 1-1 1 3c0 1 1 3 2 4-1 1-1 2-2 2l-5 7c-1-2-1-5-1-6-1-5-4-12-8-15-1-2-2-3-4-5 0-1-1-2-2-2-2-3-5-5-8-7l-15-12h0l-2-2c-1-1-1-1-1-2v-1l-1-1z" class="B"></path><path d="M200 155l1 3c0 1 1 3 2 4-1 1-1 2-2 2 1-1 1-1 0-1l-2-7 1-1z" class="G"></path><path d="M186 140v1c1-1 1-1 2-1 2 0 5 0 7 1h1 1v3c-1 2-1 3 0 6l-1 1c-2-3-2-5-2-8-3-1-6-1-9-1 0-1 0-1 1-2z" class="b"></path><defs><linearGradient id="AV" x1="168.409" y1="146.101" x2="146.249" y2="174.232" xlink:href="#B"><stop offset="0" stop-color="#060606"></stop><stop offset="1" stop-color="#3d3d3e"></stop></linearGradient></defs><path fill="url(#AV)" d="M138 123v-1h0l1-1c2 1 3 4 5 6 5 7 13 12 20 17l13 10 2 2c-1 0-1 0-2 1 4 0 7 3 10 6h0l-1 1c1 1 2 2 3 4h-1c1 1 2 1 2 2v1l1 1 1 1h3c-2 3-4 6-6 8l-1 2h-2-4l-4 2-3 1h-1l-7 7v-6c0-3 0-7-1-10 0-3-1-6-2-8 1-4-2-9-3-12h0l-9-12c-2-2-4-4-5-6-2-3-4-7-5-10-1-1-1-3-2-3-1-1-1-2-1-2l-2-1h1z"></path><path d="M184 172l-1-1c1-1 4-2 5-3 1 1 2 1 2 2v1c-2 0-4 0-6 1z" class="i"></path><path d="M169 164c2 3 2 5 2 8v1l-1-1h-1v-1c0-2-1-4-1-6 1 0 1 0 1-1z" class="E"></path><path d="M167 187v-5h0v9h2v-1-1h1v-2c1 0 3 0 4-1h0l-7 7v-6z" class="h"></path><path d="M173 160c2 0 2 1 3 2h1c2 3 2 7 3 10h-1v-1s0-1-1-2v-1-1c0-1-1-2-1-2s0 1-1 1c0-2-2-4-3-6z" class="f"></path><path d="M160 152c3 4 7 7 9 12 0 1 0 1-1 1-1-2-3-5-4-6-1-3-4-4-4-7z" class="U"></path><path d="M152 145h1c2 1 5 5 6 6s0 1 1 1c0 3 3 4 4 7-1-1-2-2-3-2h0 0l-9-12z" class="f"></path><path d="M177 157c4 0 7 3 10 6h0l-1 1c-3-3-5-4-9-5-1 0-2 0-3-1v-1h3z" class="c"></path><path d="M173 168c-1-2-1-5-2-7v-1h0 2c1 2 3 4 3 6l1 1v4h0c0 1 1 2 2 4s2 5 1 7l1 1h1 0l-4 2c0-2 0-4-1-6 0-4 0-9-1-12l-3-6v2 5z" class="E"></path><path d="M184 172c2-1 4-1 6-1l1 1 1 1h3c-2 3-4 6-6 8 0-1-1-2-1-3-2-3-4-4-4-6z" class="B"></path><path d="M173 168v-5-2l3 6c1 3 1 8 1 12 1 2 1 4 1 6l-3 1c-1-6-1-12-2-18z" class="L"></path><path d="M296 613l51 2c6 1 13 0 20 0l42-2h5v1c-2 1-3 2-4 3l-19 20-3 4h0v1c-1 0-2 2-2 2l-3 3-1-1 1-1h-2 0v-1h-1c0 3-2 4-3 6l-2 4-3 3c-5 7-10 14-14 21l-1 2-1 1v1h-1v-1c0-2-1-3-2-4l-1 4h0l-1-2c-2-2-3-4-5-5l-8-11-3-4c0-1-1-2-1-2l-1-2-4-6c-3-4-7-7-10-10l-4-4c-6-7-11-14-18-19-1-1-2-2-2-3h1z" class="B"></path><path d="M354 637c1-1 1-1 1 0s0 2-1 3h-1v-3h1z" class="J"></path><path d="M383 645v-1h0l1-1c1-2-1 1 0-1 1 0 1 0 1-1 1-1 1-1 2-1l-2 3c1 0 2-1 3-2v1c-1 0-2 2-2 2l-3 3-1-1 1-1z" class="h"></path><path d="M387 640c1-2 1-3 2-5l1-1h1c-1 1-1 2-2 4l2-1-3 4h0c-1 1-2 2-3 2l2-3z" class="F"></path><path d="M376 642h2c-1-1-1-1-1-3v1l2 1v3h0l1-1c1-1 1-4 2-6v2 1 1c-1 1-1 2-1 3h-1v1c-1 1-2 3-4 4-1 0-1 1-2 1 1-2 3-3 4-6v-1c0-1-1 0-2-1z" class="M"></path><path d="M315 635c1-1 2-2 4-1h1c1-1 1-2 3-1h4c1 2 0 4 1 6 0 0 1 0 1 1v1 1 1h0c-1-1-1-2-1-3h-1c-2 0-4-3-5-4v-1h-1c-1 1-1 2-1 3h0l-1 1-4-4z" class="F"></path><defs><linearGradient id="AW" x1="327.127" y1="635.893" x2="326.324" y2="651.821" xlink:href="#B"><stop offset="0" stop-color="#1f1e1f"></stop><stop offset="1" stop-color="#424242"></stop></linearGradient></defs><path fill="url(#AW)" d="M319 639l1-1h0c0-1 0-2 1-3h1v1c1 1 3 4 5 4h1c0 1 0 2 1 3 0 3 2 5 4 7 1 1 1 1 1 3 2 1 2 2 3 4l-4-2-4-6c-3-4-7-7-10-10z"></path><defs><linearGradient id="AX" x1="354.1" y1="634.638" x2="352.876" y2="655.038" xlink:href="#B"><stop offset="0" stop-color="#1b1b1b"></stop><stop offset="1" stop-color="#807f7f"></stop></linearGradient></defs><path fill="url(#AX)" d="M353 652c-1-1-2-3-3-4-4-4-9-7-13-11 2-2 4-2 6-2-1-1-3-2-3-3 5 3 9 7 14 9l1-1c5-2 9-6 13-8h1c-2 1-4 2-5 3 1 1 2 1 4 1 1 0 2-1 3 0h0l2-2h1l-12 11c-2 1-4 3-6 6h0v1c-1 0-1 0-1 1v2h-1 0-1v-3z"></path><defs><linearGradient id="AY" x1="334.379" y1="662.385" x2="351.84" y2="662.648" xlink:href="#B"><stop offset="0" stop-color="#353535"></stop><stop offset="1" stop-color="#4f4f4f"></stop></linearGradient></defs><path fill="url(#AY)" d="M329 643c1 1 1 2 2 3 0-1-1-2-1-3-1 0-1-1-1-2v-1c0-1 0-1 1-2v1l1 1h0l-1 1c0 1 0 1 1 2s1 3 1 4h1c1-1 3 0 4-1 0 0 1-1 1-2-1 0-1-1-2-1h-1s0-1 1-1l-1-1h0c1 0 2 0 3 1v1c1 2 2 2 2 4 0-1 1-1 1-2l1 1h0c0 1 1 1 1 1h1 0c-1-1-1-2-2-3l9 9h2v24l-1 4h0l-1-2c-2-2-3-4-5-5l-8-11-3-4c0-1-1-2-1-2l-1-2 4 2c-1-2-1-3-3-4 0-2 0-2-1-3-2-2-4-4-4-7h0z"></path><path d="M340 657c-3-3-4-6-6-8l1-1c1 2 2 3 3 4s2 2 2 3h0v2z" class="L"></path><path d="M340 655l4 5c3 2 5 5 6 8v2h0l-10-13v-2zm-7 0l4 2 11 14 3 6v2c-2-2-3-4-5-5l-8-11-3-4c0-1-1-2-1-2l-1-2z" class="T"></path><defs><linearGradient id="AZ" x1="331.346" y1="650.9" x2="351.779" y2="658.067" xlink:href="#B"><stop offset="0" stop-color="#2e2e2e"></stop><stop offset="1" stop-color="#646363"></stop></linearGradient></defs><path fill="url(#AZ)" d="M329 643c1 1 1 2 2 3 0-1-1-2-1-3-1 0-1-1-1-2v-1c0-1 0-1 1-2v1l1 1h0l-1 1c0 1 0 1 1 2s1 3 1 4h1c1-1 3 0 4-1 0 0 1-1 1-2-1 0-1-1-2-1h-1s0-1 1-1l-1-1h0c1 0 2 0 3 1v1c1 2 2 2 2 4v1c1 0 1 0 2-1 3 4 8 8 8 13h0c1 3 0 6 0 8-1-3-3-6-6-8l-4-5h0c0-1-1-2-2-3s-2-2-3-4h-1-1 0v1 1c-2-2-4-4-4-7h0z"></path><defs><linearGradient id="Aa" x1="368.614" y1="641.417" x2="359.984" y2="675.385" xlink:href="#B"><stop offset="0" stop-color="#333"></stop><stop offset="1" stop-color="#6e6d6e"></stop></linearGradient></defs><path fill="url(#Aa)" d="M376 642c1 1 2 0 2 1v1c-1 3-3 4-4 6 1 0 1-1 2-1 2-1 3-3 4-4v-1c0 3-2 4-3 6l-2 4-3 3c-5 7-10 14-14 21l-1 2-1 1v1h-1v-1c0-2-1-3-2-4v-24-1 3h1 0 1v-2c0-1 0-1 1-1v-1l1 1c3-2 6-6 9-9v1h0l1 1h0-2l-2 2c2-1 2-1 3-1s1-1 1-1c1-1 2-1 3-1h1c1 0 2 0 2-1 1 0 2 0 3-1z"></path><path d="M365 659c2-3 4-5 7-7h0c-1 3-3 5-5 7h-2z" class="X"></path><path d="M376 642c1 1 2 0 2 1v1h-1 0c-1 0-3 0-4 1l-2 1s-1-1-2-1-2 1-2 2h-1v-1c1 0 1-1 1-1 1-1 2-1 3-1h1c1 0 2 0 2-1 1 0 2 0 3-1z" class="Q"></path><path d="M365 659h2c-3 3-6 7-8 11 0-1 0-1-1-2 0-2 5-7 7-9z" class="T"></path><defs><linearGradient id="Ab" x1="359.541" y1="659.772" x2="348.096" y2="672.386" xlink:href="#B"><stop offset="0" stop-color="#737372"></stop><stop offset="1" stop-color="#939294"></stop></linearGradient></defs><path fill="url(#Ab)" d="M356 652v19 10h0v1h-1v-1c0-2-1-3-2-4v-24-1 3h1 0 1v-2c0-1 0-1 1-1z"></path><defs><linearGradient id="Ac" x1="380.338" y1="653.379" x2="353.662" y2="671.264" xlink:href="#B"><stop offset="0" stop-color="#2a2b2a"></stop><stop offset="1" stop-color="#565455"></stop></linearGradient></defs><path fill="url(#Ac)" d="M374 650c1 0 1-1 2-1 2-1 3-3 4-4v-1c0 3-2 4-3 6l-2 4-3 3c-5 7-10 14-14 21l-1 2-1-1v-7c1 0 0 2 1 3 1-1 1-1 1-3 0 0 0-1 1-2h0c2-4 5-8 8-11 2-2 4-4 5-7l2-2z"></path><defs><linearGradient id="Ad" x1="533.085" y1="194.959" x2="585.847" y2="47.79" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#232323"></stop></linearGradient></defs><path fill="url(#Ad)" d="M588 47v-2h1c3 3 4 12 4 17 0 16-8 33-17 46-1 1-4 5-4 5v1h1l1 1-4 8c9 1 19 2 25 9-4 1-9 2-14 4-4 1-8 3-12 5 2 2 4 3 6 6-7 3-14 6-17 13-1 2-1 5 0 7 0 1 1 2 2 3 2 1 4 1 6 1-1 2-3 3-4 4s-3 2-4 3c1 5 2 11 4 15-4 1-7 2-10 4-1 0-2 1-3 2l-1 1h0l-2 1v-1l-3-6c-3-3-5-5-9-8l-2-1-1-1h-2c-2-1-4-1-6-1h-3c-4-2-6-7-8-10l-6-9v-1l3-5c0-1 1-2 1-4l-1 1c0-1 3-6 3-8 0-1-1-3-1-4l2-1c0-2 1-2 2-3l2-2h0-1l-2 1v-2h2l1-1 1-1c1-1 2-1 2-2l2-2c7-3 11-8 16-11 2-1 4-3 6-3 0-1 1-1 1-2l1-1c1 0 1-1 2-1v-1h1v-1c1 0 1-1 2-1s1 0 2-1h1c1 1 1 1 2 1l2-2 3-3c1 0 2-2 3-3 1 0 3-3 4-4l8-9c1-1 3-3 4-5 2-2 4-5 5-7-1 0-1-1-2-1 3-5 4-12 4-18h-1c1-4 2-7 2-10z"></path><path d="M517 169c2 0 4 0 5 1 1 0 1 0 1 1-2 0-4-1-6-1v-1z" class="h"></path><path d="M588 47c1 3 1 7 1 10v-4l-1 1c0 1 0 2-1 3h-1c1-4 2-7 2-10z" class="D"></path><path d="M531 176v-2-1c-1-2-1-3 0-5h0l-1-1c1 0 1-1 1-1v-4c2 0 3-1 4-1l-1 2v1c-1 0-2 1-2 2-1 3 0 7-1 10z" class="F"></path><path d="M548 138l1 2-12 9c3-4 7-7 11-11z" class="E"></path><path d="M529 157h5c0 1 0 1-1 1-2 1-4 1-7 2-2 1-4 3-5 5-1 1-2 1-3 2 2-4 6-8 11-10z" class="S"></path><path d="M535 182v3c0 1 4 4 5 5h1v-7c-1 0-1 0 0 0v-4h0v-1 1c1 3 0 5 1 8v4-1l1-3v-4 11c-3-3-5-5-9-8l1-4z" class="M"></path><path d="M587 57c1-1 1-2 1-3l1-1v4c-1 6-2 14-4 19-1 0-1-1-2-1 3-5 4-12 4-18z" class="I"></path><path d="M548 138c3-3 7-5 11-9l4-4 3-3v1c-5 6-10 12-17 17l-1-2z" class="h"></path><path d="M513 142c2-1 5-2 7-2h1 0v-3c1-2 5-4 7-5l1 1h0c-2 1-5 3-6 5v4h-1c-2 0-5 0-8 1h0v4l-4 7-1 1c0-1 3-6 3-8 0-1-1-3-1-4l2-1z" class="Y"></path><defs><linearGradient id="Ae" x1="539.693" y1="161.325" x2="530.056" y2="183.129" xlink:href="#B"><stop offset="0" stop-color="#1a1a1b"></stop><stop offset="1" stop-color="#373737"></stop></linearGradient></defs><path fill="url(#Ae)" d="M535 161c1-1 2-1 3-1 0 2-2 3-2 5-1 4-1 7-1 11v6l-1 4-2-1-1-1 1-1c-1-1-1-5 0-7 0-1 0-3 1-4 0-3 1-6 1-9l1-2z"></path><path d="M531 184l1-1c-1-1-1-5 0-7 0-1 0-3 1-4v5c-1 2 1 3-1 6v1 1l-1-1z" class="f"></path><path d="M531 176c1-3 0-7 1-10 0-1 1-2 2-2v-1c0 3-1 6-1 9-1 1-1 3-1 4-1 2-1 6 0 7l-1 1h-2c-2-1-4-1-6-1 0-1 1-1 1-1 0-2 0-4 1-6v6h1v-4c1-1 0-1 0-2 1-1 0-2 1-3 1-2 1-4 1-6v-2h0 1c1 3 1 8 1 12-1 2-1 4-1 6h2 0l-1-1 1-6z" class="V"></path><path d="M541 178c1-5 1-12 4-17 1-4 3-6 6-10 1-2 2-4 4-6 3-3 6-6 9-10h0c-3 6-8 10-11 15 1 1 1 0 2 1-1 0-1 1-2 1h-2c-7 8-7 21-8 31v4l-1 3v1-4c-1-3 0-5-1-8v-1z" class="Q"></path><defs><linearGradient id="Af" x1="535.139" y1="129.225" x2="531.264" y2="123.473" xlink:href="#B"><stop offset="0" stop-color="#747374"></stop><stop offset="1" stop-color="#8a898a"></stop></linearGradient></defs><path fill="url(#Af)" d="M551 109c1 0 1 0 2-1h1c1 1 1 1 2 1v1h-2c1 1 1 1 2 1-1 3-3 6-5 8h0c-5 5-12 9-18 12-1 1-3 2-4 2h0l-1-1c-2 1-6 3-7 5v3h0-1c-2 0-5 1-7 2 0-2 1-2 2-3l2-2h0-1l-2 1v-2h2l1-1 1-1c1-1 2-1 2-2l2-2c7-3 11-8 16-11 2-1 4-3 6-3 0-1 1-1 1-2l1-1c1 0 1-1 2-1v-1h1v-1c1 0 1-1 2-1z"></path><path d="M551 109c1 0 1 0 2-1h1c1 1 1 1 2 1v1h-2c1 1 1 1 2 1h-2c0 1-1 1-1 2l-1-1c0-1-1-2-1-3z" class="W"></path><path d="M553 113c0-1 1-1 1-2h2c-1 3-3 6-5 8h0c0-1 0-1-1-1 1-2 2-3 3-5z" class="D"></path><path d="M522 130c7-3 11-8 16-11-1 1-2 3-4 4v1c-1 1-2 2-3 2-2 2-5 5-8 6-2 1-3 1-5 2 1-1 2-1 2-2l2-2z" class="W"></path><path d="M550 118c1 0 1 0 1 1-5 5-12 9-18 12-1 1-3 2-4 2h0l-1-1c8-4 15-8 22-14z" class="k"></path><path d="M367 150l1 1v-3-7c0-3 1-6 2-9 2-7 6-14 12-19l3-3 2-1 3-1 3-2h1 1 1c1-1 2-1 3-1h1c1-1 3-1 5-1h6 3c2 1 3 0 4 1h2 2 2c1 1 3 1 4 1l5 3h2c3 1 7 3 9 5l2 1 3 2s1 0 1 1c2 1 4 2 6 4 1 0 2 1 3 1l11 9c7 7 15 14 21 22h0l14 20 3 5c2 1 4 4 5 6v2c0 1-1 1-2 2-2 3-3 7-4 10l1 12 1 3s1 2 1 3l-1 1-4-4-4-7c0-1 0-1 1-1l-1-5-4-6c-3-2-4-6-7-7v1c-2-3-4-7-5-9-3-5-7-9-10-13-1-2-2-4-4-6l-3-4s1 0 2 1l-1-2c-3-5-8-11-12-15l-1 1c-2 0-3-1-5-2l-3-4c0-1-2-3-2-3-1-1-2-1-3-2 0 2 1 3 0 5 0 3-1 5-4 8l-3 2h0c-1 1-3 1-4 1h-2l1 1c-3 0-13-2-14-1h0c-3 0-6 1-9 1h0c-6 1-11 2-16 4-3 1-6 2-8 3h-3c0-1 0-1-1-1-2-2-2-4-3-7v-2h-1c0 3-1 5-1 9h0c0 2 0 3 1 5h0c0 2 3 6 3 7h-3c-1-2-2-4-3-5h-1c2 3 5 6 7 10-1 0-1 0-2 1l1 1c-2 0-4 0-6-1 0-1 1-1 1-1l-1-1c-1-1-2-3-2-4l1-1h0c1 0 1 1 2 1 0 1 2 3 3 4h0c1 1 1 1 2 1v-1l-2-2c0-1-1-1-2-2v-1l-2-3-2-4c-1-1-1-2-1-3-2-1-2-3-2-5z" class="j"></path><path d="M469 156l4 5h-2l-3-4s1 0 2 1l-1-2z" class="K"></path><path d="M375 154v-1c-1-2-1-10 0-12 1-1 1-2 1-3v7c0 3-1 5-1 9z" class="I"></path><path d="M372 172c0-1 1-1 1-1l-1-1c-1-1-2-3-2-4l1-1c1 3 3 5 6 7l1 1c-2 0-4 0-6-1z" class="h"></path><path d="M501 201c3 3 5 6 7 10l1 3-1-1h0c-2-1-2-2-3-3l-3-4-1-5z" class="e"></path><path d="M500 185s0-1 1-2c-1 1-1 3-1 5l1 1c1 2 2 5 1 6l-2-3c-2-2-3-4-4-5l1-1h0l1 1h1l1-2z" class="W"></path><path d="M500 185s0-1 1-2c-1 1-1 3-1 5l1 1c1 2 2 5 1 6l-2-3v-1-6z" class="d"></path><path d="M501 207c0-1 0-1 1-1l3 4c1 1 1 2 3 3h0l1 1s1 2 1 3l-1 1-4-4-4-7zm-125-69c1-4 3-9 6-13h1c-1 2-1 2-1 4h0c-2 3-3 5-4 7 0 1-1 1-1 2 0 2-1 4 0 6v1h-1v-7z" class="Q"></path><path d="M473 161c5 6 9 13 13 19 2 3 4 7 6 9 2 1 4 5 5 6-3-2-4-6-7-7v1c-2-3-4-7-5-9-3-5-7-9-10-13-1-2-2-4-4-6h2z" class="P"></path><path d="M471 152l-25-29 1-1v1l1-1c1 0 1 1 2 1l2 2h0s1 0 1 1c2 3 5 6 7 9 4 4 8 10 13 14 1 1 2 1 3 1 2 0 5 0 7-2l1 1c-2 1-3 2-4 2s-3 1-4 1c-2 0-1-1-3 1h0 0l-2-1z" class="N"></path><path d="M484 149c1 1 2 2 2 3v8 1c-1 2 0 4-1 7v-5h-1v3 1c-1-1-1-2-2-3l1 4c-2-1-3-4-4-5-2-1-3-3-4-5s-3-3-4-6l2 1h0 0c2-2 1-1 3-1 1 0 3-1 4-1s2-1 4-2z" class="I"></path><path d="M482 151l1 1v1l-1 1h-1c0-2 0-2 1-3z" class="d"></path><path d="M483 159c0-1 0-2 1-3h1v7h-1c0-1 0-2-1-4z" class="H"></path><path d="M483 159c1 2 1 3 1 4v3 1c-1-1-1-2-2-3 0-1 1-3 1-5z" class="G"></path><path d="M474 154h1c1 0 1 1 2 1 0-1 0-2 1-2 1-2 2-2 4-2-1 1-1 1-1 3h1c-1 1-2 1-3 1-1 1-1 1-1 2h-1v1c-1-2-2-3-3-4z" class="D"></path><path d="M471 152l2 1 1 1c1 1 2 2 3 4v-1h1c0-1 0-1 1-2h1c1 2 1 2 1 3 0 2-1 3-2 5-2-1-3-3-4-5s-3-3-4-6z" class="l"></path><path d="M499 178v-2h0l1 1h1l1-1h0v-1c-1-1-3-2-3-4-1 0-1 0-1-1-1 0 0 0-1-1s-2-3-2-4l-5-7h1c1 2 1 2 2 3h0l-2-3-6-9h1 0c-1-2-2-3-3-4l-6-6c-1 1 1 1 0 2h0l-2-1-6-6c-3-3-6-6-10-9h1l-1-2 11 9c7 7 15 14 21 22h0l14 20-3 6-1-1 1-2c-1 1-1 1-2 1h-1z" class="Z"></path><path d="M502 177s1 0 1-1c1-4-10-17-12-21v-1l14 20-3 6-1-1 1-2z" class="d"></path><path d="M484 163h1v5c1 0 1 1 2 1 1 1 6 2 8 2v-1l2 1 1 1h0c1 3-1 5-1 7l1 1 1-2h1c1 0 1 0 2-1l-1 2 1 1-1 3c-1 1-1 2-1 2l-1 2h-1l-1-1h0l-1 1c-1-2-3-4-4-6s-2-3-3-4c-1-3-4-6-6-9l-1-4c1 1 1 2 2 3v-1-3z" class="C"></path><path d="M491 175l1-1-1-2h1 0c1 0 0 2 0 3h-1z" class="P"></path><path d="M501 179l1 1-1 3c-1 1-1 2-1 2l-1 2h-1l-1-1c1-2 2-3 3-5l1-2z" class="c"></path><path d="M491 175h1l2 1c0 2 0 4-1 4l-1 1c-1-2-2-3-3-4l2-2z" class="h"></path><path d="M495 170l2 1 1 1h0c1 3-1 5-1 7l1 1 1-2h1c1 0 1 0 2-1l-1 2-1 2c-1 0-2 1-3 1l-1-4h0c1-1 0-2 1-3h0c0-3 0-3-2-5z" class="I"></path><path d="M505 174l3 5c2 1 4 4 5 6v2c0 1-1 1-2 2-2 3-3 7-4 10h0v7c-1-3-2-5-3-8 0-1-1-2-2-3 1-1 0-4-1-6l-1-1c0-2 0-4 1-5l1-3 3-6z" class="N"></path><path d="M505 174l3 5h0l-3-3c0 1-1 1-1 2l1 1-1 2c-3 2-3 4-4 7 0-2 0-4 1-5l1-3 3-6z" class="C"></path><path d="M504 181c-1 2-3 4-3 7 1 2 3 3 4 5 1 1 1 3 2 5v-1 2 7l-3-8c0-1-1-2-2-3 1-1 0-4-1-6l-1-1c1-3 1-5 4-7z" class="I"></path><path d="M501 189c2 3 4 6 4 9h-1c0-1-1-2-2-3 1-1 0-4-1-6z" class="C"></path><path d="M382 125c5-7 11-11 20-13 12-2 24 3 34 10 3 2 6 4 8 6 1 1 6 5 6 6l7 7-1 1c-2 0-3-1-5-2l-3-4c0-1-2-3-2-3-1-1-2-1-3-2h-2v-1c-2-1-5-2-7-4-4-2-8-5-12-7-6-2-14-4-20-3-4 0-8 2-12 4-2 1-5 3-7 5h-1z" class="B"></path><path d="M402 116c10-3 18 1 26 6l14 7h0c0-1-1-1-2-2h1c1 1 1 1 3 2v-1c1 1 6 5 6 6l7 7-1 1c-2 0-3-1-5-2l-3-4c0-1-2-3-2-3-1-1-2-1-3-2h-2v-1c-2-1-5-2-7-4-4-2-8-5-12-7-6-2-14-4-20-3z" class="J"></path><path d="M450 134l7 7-1 1c-2 0-3-1-5-2l-3-4h2l1 2h1 0c-1-2-2-2-2-4h0z" class="E"></path><path d="M402 116c6-1 14 1 20 3 4 2 8 5 12 7 2 2 5 3 7 4v1h2c0 2 1 3 0 5 0 3-1 5-4 8l-3 2h0c-1 1-3 1-4 1h-2l1 1c-3 0-13-2-14-1h0c-3 0-6 1-9 1h0c-6 1-11 2-16 4-3 1-6 2-8 3h-3c0-1 0-1-1-1-2-2-2-4-3-7v-2-1c-1-2 0-4 0-6 0-1 1-1 1-2 1-2 2-4 4-7h0c0-2 0-2 1-4 2-2 5-4 7-5 4-2 8-4 12-4z" class="B"></path><path d="M426 140c0 1 0 1 1 2l-3 1v-1h-2c1-1 2-1 4-2z" class="H"></path><path d="M415 138c1-1 2-2 4-3l-1 4h0c-1 0-2 0-3-1z" class="G"></path><path d="M377 144h0c2-1 2-2 4-1l1 1h0l-1 1c-1 0-3 1-4 2v-2-1z" class="M"></path><path d="M426 140l6-5v5c-1-1-1-1-1-2v-1c0 1-1 1-1 1v1 2l-1-1-2 2c-1-1-1-1-1-2z" class="G"></path><path d="M415 138c1 1 2 1 3 1h0c1 1 2 2 4 3h2v1h-3l-1 1c-1 0-3-1-4-1-1-2-1-2-3-3v-1l2-1z" class="P"></path><path d="M408 118c5 0 11 2 16 5v2c-5-3-11-5-17-6h1 1l-1-1z" class="K"></path><path d="M430 141v-2-1s1 0 1-1v1c0 1 0 1 1 2-1 2-1 4 0 7h-2l-5-2-5-1 1-1h3l3-1 2-2 1 1z" class="H"></path><path d="M427 142l2-2 1 1c-2 2-3 4-5 4h0l-5-1 1-1h3l3-1z" class="K"></path><defs><linearGradient id="Ag" x1="386.231" y1="118.753" x2="405.172" y2="126.896" xlink:href="#B"><stop offset="0" stop-color="#615f60"></stop><stop offset="1" stop-color="#858686"></stop></linearGradient></defs><path fill="url(#Ag)" d="M390 120c3 0 5-1 7-1 4-1 7-1 11-1l1 1h-1-1c-9 0-18 4-24 10h-1 0c0-2 0-2 1-4 2-2 5-4 7-5z"></path><defs><linearGradient id="Ah" x1="403.868" y1="128.73" x2="408.669" y2="139.139" xlink:href="#B"><stop offset="0" stop-color="#898787"></stop><stop offset="1" stop-color="#a8a9a9"></stop></linearGradient></defs><path fill="url(#Ah)" d="M396 132c-1-1-1-3-1-5h2v-1c1-1 3-1 4-1s1 1 2 2h0v-2c2 0 1 2 3 3l1-1h0c-1 2-1 3-1 5 2 3 5 6 7 7v1c-1 0-2-1-4-1-1-1-4-4-5-6-1-1-1-2-2-4-1 0-1 0-2 1v2l-1-1c-2 0-2 0-3 1z"></path><path d="M402 116c6-1 14 1 20 3 4 2 8 5 12 7 2 2 5 3 7 4v1h2c0 2 1 3 0 5 0-1-1 0-2 0l-2-1c-4-4-10-7-15-10v-2c-5-3-11-5-16-5-4 0-7 0-11 1-2 0-4 1-7 1 4-2 8-4 12-4z" class="O"></path><path d="M441 131h2c0 2 1 3 0 5 0-1-1 0-2 0l1-1c0-2 0-3-1-4z" class="Q"></path><defs><linearGradient id="Ai" x1="434.999" y1="126.705" x2="428.72" y2="130.839" xlink:href="#B"><stop offset="0" stop-color="#726f74"></stop><stop offset="1" stop-color="#858682"></stop></linearGradient></defs><path fill="url(#Ai)" d="M424 123c4 2 8 4 11 6 2 1 4 2 5 4 0 1 0 1-1 2-4-4-10-7-15-10v-2z"></path><path d="M396 132c1-1 1-1 3-1l1 1v-2c1-1 1-1 2-1 1 2 1 3 2 4 1 2 4 5 5 6 2 0 3 1 4 1 2 1 2 1 3 3 1 0 3 1 4 1l5 1 5 2 1 1c-3 0-13-2-14-1h0c-3 0-6 1-9 1h0l-1-1c-6-4-8-8-11-15z" class="Y"></path><path d="M409 139c2 0 3 1 4 1 2 1 2 1 3 3-3-1-4-2-7-4z" class="C"></path><path d="M396 132c1-1 1-1 3-1l1 1v-2c1-1 1-1 2-1 1 2 1 3 2 4-2 1-1 2-2 3 0 0-1 0-2-1l-1-1h-1v1c0 1 1 1 1 2 2 1 3 3 4 5 2 2 6 3 6 5l-1 1h0l-1-1c-6-4-8-8-11-15z" class="D"></path><path d="M264 132l1 1c0 1 0 2 1 3 0 3 1 5 2 7l-1 1v1l5 3 1 1h2c1-1 2 0 3 0 1-1 1-1 2-1h2l1-1c1 0 2 0 3 1h1l1-1 1 1h5 7l7 1 17 5v1h0 2l1 1c-1 1-2 2-3 2h-1l-5-1 6 3 1 2c-1 0-1 1-2 2l-3 3v1l-3 1-1 1h5-1l-1 2c-4 3-8 5-13 6h2v1c-1 0-1 0-2 1h0-1-1v1h0v1c-2 0-6 1-9 0h-1c-3 2-5 4-8 5h-1c2 2 4 1 6 1v1l1 1c-2 1-4 2-7 3-7 3-18 3-25 1-5-1-7-4-11-6-2-1-4-1-6-1 0-3 2-5 2-8 0-2-1-4-1-6-8 7-14 16-19 25-2 3-3 6-4 9v1c1 2 4 2 6 2 1 0 2 0 3 1s2 5 2 7c1 6 0 12 0 18v39 23l-4 1h0c-2 1-7 3-8 4-2 2-4 3-6 5l-2 1c-1 1-5 7-5 7-3 6-6 12-6 18v3l-1 1h-1l-2-2-19-19-3-3-2-2c-4-3-7-5-11-7h-1v-2h-1c-1-1-3-2-5-3-5-4-10-9-12-14h-1-2c-1 0-2 1-3 1s-4 1-4 1h0l-2 2v1h0 1c-1 0-1 0-2 1 0 1 0 1 1 1l-2 1c-2 0-5 0-8 1l-21-8h0l-1-1c-2-1-3-3-5-5-1 1-2 2-4 2h1v-1h-1c1-1 1-2 1-3-3-2-6-6-9-9-2-3-5-7-9-9 0-1 1-1 1-1 2-1 3-1 4 0 1-3 5-7 7-9l2 2v1l1 1c1 0 2-1 3-2h-1v-1c1 0 1 0 2 1v-1h-1l1-1c2 0 5 2 8 3s5 2 8 3c6 1 11 3 17 3 6 1 15 0 20-3 2 0 4-2 5-3 4-2 8-4 12-5 4-2 11-1 15 1l3 2 1-1c-1-1-3-2-5-2v-3h3l1-1c0-2 0-4-1-5h10l4 1h2c2 1 1 2 2 3v-5h-1c-1-1-1-2-2-4v-1-5-6l1-2c2-2 3-5 4-8 1-1 2-3 2-4l4-7 12-17 3-1 1-2c2-4 5-7 7-10 0 0 0-1-1-1l1-2c2-2 3-4 4-6l8-10h1l3-1 4-4 4-4 3-2 2-3c1-2 5-6 6-7z" class="j"></path><path d="M138 287c1-1 3-2 4-3 1 0 1 1 2 2h-1-2c-1 0-2 1-3 1z" class="k"></path><path d="M101 268l3 3 1 4c0-1 0-1-1-1-1-1-3-2-3-3v-3z" class="b"></path><path d="M106 264h0v1l-2 3v-2c-1 0-1 0-2-1l-1 1-1 1-2-1c1 0 1-1 2-1 2 0 4 0 6-1z" class="G"></path><path d="M258 187h0 2v1c1 0 1 1 2 1l2 1h1 1c1 1 0 1 1 1l3 1h-1l1 1h-2c-4-2-7-3-10-5h0 1l-1-1z" class="b"></path><path d="M104 271c2 2 3 3 5 4l-1 1c0 1 1 1 2 2 3 3 5 4 10 5-2 0-4 0-6-1h0c-4-1-6-4-9-7l-1-4z" class="Z"></path><path d="M125 265c5 0 9 0 13 1h1l-2 2v-1c-1-1-3 0-4 0-2-1-4-1-6-1-2-1-4 0-6-1h4z" class="i"></path><path d="M91 258l1-1v-2c1 0 2 0 3 1h2l1 1h2 0c1 0 1 0 1 1h-2c-2 0-3-2-5 0 1 1 2 3 3 4 0 1 1 2 2 3h1c-1 0-1 1-2 1-2-3-5-6-7-8z" class="N"></path><path d="M106 264c1-1 2-1 3-1v1c1 1 1 2 0 4 0 1-2 3-2 4-1 0-1-1-2-1 0-1-1-1-1-2v-1l2-3v-1h0z" class="D"></path><path d="M106 264c1-1 2-1 3-1v1 1h-1c-1 0-1-1-2-1h0z" class="C"></path><path d="M106 265l1 1c1 1 1 1 0 2 0 1-1 2-2 3 0-1-1-1-1-2v-1l2-3z" class="l"></path><path d="M255 173c0-1 0-1 1-2 1 1 1 2 2 4s2 3 3 5l2 1c1 2 4 4 6 5-2 0-3 0-5-1s-6-4-6-6c0-1-2-4-3-6z" class="C"></path><path d="M200 262c1-2 0-3 0-5v-8c1-1 0-3 1-4v-1c0-1 0-1 1-2v4c0 3 1 6 0 9 0 1 1 3 0 5 0 1-1 1-1 2v1h0c1 3 0 4-1 6v-7z" class="N"></path><path d="M273 149c-2 1-5 2-7 1l-2-2v-3c0-1 1-1 1-1h2v1l5 3 1 1z" class="G"></path><path d="M267 145l5 3c0 1-1 1-1 1l-2-1c-2 0-2-1-3-3h1z" class="g"></path><path d="M101 266l1-1c1 1 1 1 2 1v2 1c0 1 1 1 1 2 1 0 1 1 2 1l1 1c2 0 5-3 7-4v1l-6 5c-2-1-3-2-5-4l-3-3-1-1 1-1z" class="C"></path><path d="M101 266l1-1c1 1 1 1 2 1 0 1 0 2-1 3 0 0-1 0-1-1-1 0-1-1-1-2z" class="Y"></path><path d="M252 167c2-2 3-3 5-4v2h0-1c-1 2-1 6-1 8 1 2 3 5 3 6h-1c-2-2-4-6-4-9 0-1 0-1-1-2-2 1-3 2-5 3h0l5-4z" class="F"></path><path d="M210 216s0 1 1 1c0 1 0 3-1 5-1 3-2 6-3 10v4 6 10 1h0l-1-26c1-1 1-2 1-3v-1-1l1-1v-1-1l1-1s0-1 1-2z" class="k"></path><path d="M115 269c2-1 3-3 4-5l6 1h-4c2 1 4 0 6 1 2 0 4 0 6 1h0-7c-1 0-2-1-3 0-1 0-2 1-3 2-2 1-4 2-5 4-1 1-2 2-4 3h-2 0v1l1 1c-1-1-2-1-2-2l1-1 6-5v-1z" class="b"></path><path d="M115 269c2-1 3-3 4-5l6 1h-4c2 1 4 0 6 1-3 0-5 0-8 1v1c-1 0-2 1-4 2v-1z" class="c"></path><path d="M104 279c-1-1-1-2-2-3 0-1-1-1-1-2l-1-1h0l-1-2-1 1-5-5c-1 0-3-3-4-4-1-2-1-3-3-5-1 0-1-1-1-2l1-1s1 0 1 1c1 0 1 0 1 1 1 2 3 3 4 5 1 4 5 6 7 9 2 2 4 5 6 7l-1 1zm45-21h0c0 2-3 5-3 7-1 1-2 3-1 5v-2c1-1 0-1 1-2v-1-1c1 0 0 0 1-1 0 1-1 2-1 3-1 3-1 6 0 8 1 4 2 10 5 13 2 3 5 5 7 7 1 1 3 2 4 3h0c-5-3-11-7-14-12-3-4-5-12-4-17 0-2 1-3 2-5s2-3 3-5z" class="N"></path><defs><linearGradient id="Aj" x1="92.133" y1="256.001" x2="104.925" y2="252.388" xlink:href="#B"><stop offset="0" stop-color="#b5b5b6"></stop><stop offset="1" stop-color="#e7e5e5"></stop></linearGradient></defs><path fill="url(#Aj)" d="M91 252c1 0 1 0 2 1v-1h-1l1-1c2 0 5 2 8 3l8 3c-1 1-1 1-3 1h-1-4c0-1 0-1-1-1h0-2l-1-1h-2c-1-1-2-1-3-1v2l-1 1-2-3c1 0 2-1 3-2h-1v-1z"></path><path d="M151 267c1-2 2-5 4-7 3-3 8-4 12-4 5 0 9 3 12 6-1 0-2 1-4 0h2c-2-2-7-3-10-3-5 0-8 1-11 4-1 2-3 5-4 5l-1-1z" class="V"></path><path d="M137 268c-1 2-3 4-4 6l-3 4c1-1 2-3 4-3 1-1 2-2 4-3 1 1 1 1 1 2s-1 1-2 2c-4 5-10 7-16 7h0c3-1 5-5 8-7 2-3 5-6 8-9v1z" class="D"></path><path d="M252 167v-2c2-3 15-8 19-10 3 0 6-1 8-2 2 0 5-1 7-1 5-1 11-1 16 0 2 0 5 0 7 1h0c1 1 2 1 2 2-18-3-37-2-54 8-2 1-3 2-5 4z" class="N"></path><path d="M214 197c0 1 0 2-1 3 0 1-1 2-1 4-1 3-3 6-4 9-2 6-2 11-3 16 0 1 0 1-1 2 0 1 0 2-1 3l-1 8c-1 1-1 1-1 2v1c-1 1 0 3-1 4v8c0 2 1 3 0 5v-4c0-5-1-9-2-14v-5-4c1-2 1-3 1-5 2-1 3-3 5-4v1c1-1 1-2 1-3l2-8c1-6 4-12 6-18l1-1z" class="Z"></path><path d="M151 267l1 1c1 0 3-3 4-5 3-3 6-4 11-4 3 0 8 1 10 3h-2c-1 0-3 0-4 1h-2l-1-1c-3 0-6 1-9 3-2 1-4 4-4 7 0 4 2 8 2 12h0c-4-4-6-9-6-15v-2z" class="a"></path><path d="M169 263h2c1-1 3-1 4-1 2 1 3 0 4 0 3 3 4 6 3 11 0 2-1 4-3 5s-5 1-7 1c-3-1-5-3-7-5-1-2-1-4 0-7 0-1 2-3 4-4z" class="B"></path><defs><linearGradient id="Ak" x1="240.85" y1="154.892" x2="250.184" y2="160.663" xlink:href="#B"><stop offset="0" stop-color="#c1c0c0"></stop><stop offset="1" stop-color="#e7e6e7"></stop></linearGradient></defs><path fill="url(#Ak)" d="M258 139c0 3 0 8 1 10v2h0c-1 1-1 0-1 1-1 1-1 1-2 1l-3 1-1 1c-1 1-3 2-4 2l-5 4-1 1c-2 1-3 3-4 4l-1 1-9 12v-1c1 0 1-1 1-1 2-2 4-5 5-7-3 1-4 2-5 4l-4 6c-1 1-2 1-3 2 2-4 5-7 7-10 0 0 0-1-1-1l1-2c2-2 3-4 4-6l8-10h1l3-1 4-4 4-4 3-2 2-3z"></path><path d="M241 153h1l3-1-10 13c-2 2-4 5-6 7 0 0 0-1-1-1l1-2c2-2 3-4 4-6l8-10z" class="K"></path><defs><linearGradient id="Al" x1="198.647" y1="208.978" x2="212.919" y2="212.252" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2a2a2b"></stop></linearGradient></defs><path fill="url(#Al)" d="M218 185l3-1-5 8c0 2-2 4-2 5l-1 1c-2 6-5 12-6 18l-2 8c0 1 0 2-1 3v-1c-2 1-3 3-5 4 0 2 0 3-1 5v4h-1c-1-1-1-2-2-4v-1-5-6l1-2c2-2 3-5 4-8 1-1 2-3 2-4l4-7 12-17z"></path><defs><linearGradient id="Am" x1="214.89" y1="207.098" x2="196.419" y2="216.957" xlink:href="#B"><stop offset="0" stop-color="#272a29"></stop><stop offset="1" stop-color="#6a6768"></stop></linearGradient></defs><path fill="url(#Am)" d="M198 235c0-4 0-9 1-13 1-6 5-11 8-16 1-1 1-4 3-5 0-1 1-2 1-3h0 1 1c-2 6-5 12-6 18l-2 8c0 1 0 2-1 3v-1c-2 1-3 3-5 4 0 2 0 3-1 5z"></path><defs><linearGradient id="An" x1="131.175" y1="292.19" x2="99.329" y2="282.179" xlink:href="#B"><stop offset="0" stop-color="#a7a6a6"></stop><stop offset="1" stop-color="#c8c7c8"></stop></linearGradient></defs><path fill="url(#An)" d="M79 260c1-3 5-7 7-9l2 2v1c-1 0-1-1-2-2-1 1-3 4-4 5l-2 2 13 16c0 1 0 0 1 1 2 2 3 5 5 7h0c2-2 4-3 5-4l1-1c4 6 9 7 15 9l8 2c2 0 4-1 6-1h0l-2 2v1h0 1c-1 0-1 0-2 1 0 1 0 1 1 1l-2 1c-2 0-5 0-8 1l-21-8h0l-1-1c-2-1-3-3-5-5-1 1-2 2-4 2h1v-1h-1c1-1 1-2 1-3-3-2-6-6-9-9-2-3-5-7-9-9 0-1 1-1 1-1 2-1 3-1 4 0z"></path><path d="M120 287l8 2h3v1h-2-7c-1-1-2-1-3-2l1-1z" class="c"></path><defs><linearGradient id="Ao" x1="79.479" y1="258.531" x2="90.186" y2="281.234" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2d2d2d"></stop></linearGradient></defs><path fill="url(#Ao)" d="M74 261c0-1 1-1 1-1 2-1 3-1 4 0l18 22c1 1 3 3 3 4-2-1-3-3-5-5-1 1-2 2-4 2h1v-1h-1c1-1 1-2 1-3-3-2-6-6-9-9-2-3-5-7-9-9z"></path><path d="M258 175l3-3 12-6c2-1 4-2 6-2-1 3-1 6 0 10l1 1c4 3 7 5 12 6 1 1 2 1 3 1-3 2-5 4-8 5h-1-1c-2 1-7 1-9 1h0c-3 0-5-1-7-2s-5-3-6-5l-2-1c-1-2-2-3-3-5z" class="B"></path><path d="M261 180c3-2 9-5 13-5 1 1 1 3 1 4l-1-1c-3 0-5 0-7 1-2 0-3 0-4 1h0v1h0l-2-1z" class="V"></path><path d="M278 182v-9c1 1 2 3 3 4l3 9c1 0 2 0 3 1h-1-1c-2 1-7 1-9 1h0l1-1c1-1 1-3 1-5z" class="a"></path><path d="M278 182l1-2h0c1 1 1 1 1 2 1 2 2 4 3 5h2c-2 1-7 1-9 1h0l1-1c1-1 1-3 1-5z" class="T"></path><path d="M263 181h0v-1h0c1-1 2-1 4-1 2-1 4-1 7-1l1 1c0 3 0 6 1 9-3 0-5-1-7-2s-5-3-6-5z" class="M"></path><path d="M180 240h10l4 1h2c2 1 1 2 2 3 1 5 2 9 2 14v4 7c0 3 0 6-1 9-2 5-6 9-10 12-2 0-4 2-5 1-2 1-4 1-6 1-5 0-8-1-11-5-2-1-3-3-3-6h0c3 1 5 2 7 3h6c2-1 4-1 6-2 3-4 5-7 6-12 0-5-1-9-3-13l-2-3-3-2 1-1c-1-1-3-2-5-2v-3h3l1-1c0-2 0-4-1-5z" class="S"></path><path d="M183 289c5-1 7-4 11-8-1 2-2 4-3 5h0l1 1 1-2h0c1-1 3-2 3-4h1v-2h0l2-1c-2 5-6 9-10 12-2 0-4 2-5 1h-3l-1 1v-1h1c0-1 1-1 2-2z" class="R"></path><defs><linearGradient id="Ap" x1="172.178" y1="288.041" x2="174.13" y2="291.748" xlink:href="#B"><stop offset="0" stop-color="#3b393b"></stop><stop offset="1" stop-color="#515150"></stop></linearGradient></defs><path fill="url(#Ap)" d="M167 287h6c2 1 3 0 5 0-1 1-1 1-2 1l-1 1h0v1h2c2 0 4 0 6-1-1 1-2 1-2 2h-1v1l1-1h3c-2 1-4 1-6 1-5 0-8-1-11-5z"></path><defs><linearGradient id="Aq" x1="182.246" y1="277.635" x2="192.345" y2="272.791" xlink:href="#B"><stop offset="0" stop-color="#0f0e11"></stop><stop offset="1" stop-color="#2c2c2d"></stop></linearGradient></defs><path fill="url(#Aq)" d="M189 270v-2l1 1h1v-1c1 2 1 8 0 10 0 2-2 4-3 5v-1h-1v-1c-1 0-2 0-2 1h-2c3-4 5-7 6-12z"></path><path d="M167 287c-2-1-3-3-3-6h0c3 1 5 2 7 3h6c2-1 4-1 6-2h2c0-1 1-1 2-1v1h1v1c-3 3-6 4-10 4-2 0-3 1-5 0h-6z" class="V"></path><path d="M183 282h2c-2 1-4 3-7 2h-1c2-1 4-1 6-2z" class="F"></path><defs><linearGradient id="Ar" x1="183.748" y1="239.413" x2="192.663" y2="258.387" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#343434"></stop></linearGradient></defs><path fill="url(#Ar)" d="M180 240h10l4 1h2c2 1 1 2 2 3 1 5 2 9 2 14-1 1 0 2 0 3-1-2 0-3-1-5h-1c-1 0-2-2-2-3l-1 1c-2-3-3-6-7-7h-2v1c0 2 1 6 0 7h0v2l-2-3-3-2 1-1c-1-1-3-2-5-2v-3h3l1-1c0-2 0-4-1-5z"></path><path d="M180 240h10l4 1h-1v2c-1 0-3 0-4-1 0 0-1 0-1-1-1 0 0 0-1 1-1-1-1-1-2-1-1-1-2 0-3 0h0c0 2 0 3-1 4 0-2 0-4-1-5z" class="B"></path><path d="M184 254h0v-5c-1-1-1-3-1-4 5 0 8 1 12 4l-1 2 2 2-1 1c-2-3-3-6-7-7h-2v1c0 2 1 6 0 7h0v2l-2-3z" class="f"></path><path d="M257 163c17-10 36-11 54-8l8 2 6 3 1 2c-1 0-1 1-2 2l-3 3v1l-3 1-1 1h5-1l-1 2c-4 3-8 5-13 6h2v1c-1 0-1 0-2 1h0-1-1v1h0v1c-2 0-6 1-9 0h-1c-1 0-2 0-3-1-5-1-8-3-12-6l-1-1c-1-4-1-7 0-10-2 0-4 1-6 2l-12 6-3 3c-1-2-1-3-2-4-1 1-1 1-1 2 0-2 0-6 1-8h1 0v-2z" class="B"></path><path d="M286 158c3-1 7-1 10-1 8-1 15-1 22 2l-1 1h-4-2c-4-1-8-1-13-1l-7 1h-4c-1 1-3 1-3 1 0-1-1-1-1-1 1-1 2-1 3-2z" class="H"></path><path d="M257 165c-1 2-1 5 0 8 0-2 1-3 2-5 3-2 6-3 8-5 6-3 12-4 19-5-1 1-2 1-3 2-2 1-3 3-4 4-2 0-4 1-6 2l-12 6-3 3c-1-2-1-3-2-4-1 1-1 1-1 2 0-2 0-6 1-8h1z" class="L"></path><path d="M287 160h4l7-1c5 0 9 0 13 1h2 4l1-1c2 1 4 2 5 4l1 1-3 3v1l-3 1-1 1h0c-2 0-6-3-8-2v1l-2-2c-7-5-12-7-20-7z" class="T"></path><path d="M303 161h3c2 0 7 3 9 4l2 2 1 1 1-1v-1c1 0 1 1 2 1v1l-3 1h-1c-1-2-2-2-4-3-3-2-6-4-10-5z" class="L"></path><defs><linearGradient id="As" x1="313.453" y1="160.272" x2="300.573" y2="162.486" xlink:href="#B"><stop offset="0" stop-color="#2d2c2b"></stop><stop offset="1" stop-color="#535155"></stop></linearGradient></defs><path fill="url(#As)" d="M298 159c5 0 9 0 13 1 1 1 1 2 2 2 2 1 4 3 5 5h-1l-2-2c-2-1-7-4-9-4h-3l-5-1v-1z"></path><path d="M318 159c2 1 4 2 5 4l1 1-3 3c-1 0-1-1-2-1v1l-1 1-1-1h1c-1-2-3-4-5-5-1 0-1-1-2-2h2 4l1-1z" class="E"></path><path d="M318 159c2 1 4 2 5 4v1c-3 0-8-3-10-4h4l1-1z" class="K"></path><defs><linearGradient id="At" x1="295.207" y1="157.081" x2="303.541" y2="178.922" xlink:href="#B"><stop offset="0" stop-color="#b8b7b6"></stop><stop offset="1" stop-color="#e6e5e6"></stop></linearGradient></defs><path fill="url(#At)" d="M283 160s1 0 1 1c0 0 2 0 3-1 8 0 13 2 20 7l2 2v-1c2-1 6 2 8 2h0 5-1l-1 2c-4 3-8 5-13 6h2v1c-1 0-1 0-2 1h0-1-1v1h0v1c-2 0-6 1-9 0h-1c-1 0-2 0-3-1-5-1-8-3-12-6l-1-1c-1-4-1-7 0-10 1-1 2-3 4-4z"></path><path d="M309 169v-1c2-1 6 2 8 2-1 1-1 1-2 1-1 1-1 2-2 2-1 1-3 1-4 2s-2 1-3 1h-1-1-2 0c2-1 4-1 5-2 2-1 2-1 3-2h1c0-1-1-2-2-3z" class="b"></path><path d="M279 174h2 1c2 1 6 4 8 4l1-1v1c3 2 10 1 13 1 1 0 3-1 3-1h2v1c-1 0-1 0-2 1h0-1-1v1h0v1c-2 0-6 1-9 0h-1c-1 0-2 0-3-1-5-1-8-3-12-6l-1-1z" class="k"></path><path d="M280 175h2c1 0 2 1 2 1l4 2c2 1 3 2 4 3-5-1-8-3-12-6z" class="C"></path><path d="M282 170c1 0 2 1 2 0h1c1-1 1-1 3-1 6-1 10 0 16 2 1 1 2 1 3 2v1c-1 1-3 1-5 2-3 0-6 1-9 0-4 0-9-2-11-6z" class="B"></path><path d="M287 160c8 0 13 2 20 7l2 2c1 1 2 2 2 3h-1c-1 1-1 1-3 2v-1c-1-1-2-1-3-2-6-2-10-3-16-2-2 0-2 0-3 1h-1c0 1-1 0-2 0s-1-1-1-2v-2c1-3 1-4 3-5 0 0 2 0 3-1z" class="i"></path><path d="M287 160c8 0 13 2 20 7l2 2c1 1 2 2 2 3h-1c-1 0-2-1-3-2-5-4-9-6-15-6-3 0-7 0-10 2l-1 2v-2c1-3 1-4 3-5 0 0 2 0 3-1z" class="B"></path><path d="M222 208c1 2 4 2 6 2 1 0 2 0 3 1s2 5 2 7c1 6 0 12 0 18v39 23l-4 1h0c-2 1-7 3-8 4-2 2-4 3-6 5l-2 1c-1 1-5 7-5 7-3 6-6 12-6 18v3l-1 1h-1l-2-2-19-19-3-3-2-2c-4-3-7-5-11-7h-1v-2c4 2 8 4 12 5 11 1 22-2 30-9 5 2 9 3 14 4-4-3-6-5-8-8v-1c8-11 8-24 6-37-1-4-1-9-1-13-1-10 0-20 4-29 0-2 1-6 3-7z" class="F"></path><path d="M176 314c8-1 15-2 22-4 3 0 5-1 7-2h6l2 1c-1 1-5 7-5 7h-1l1-1h0l1-1v-1c-1-1-1-1-1-2s0-1-1 0h-2l-9 3-11 2c-2 0-4 0-6 1l-3-3z" class="J"></path><path d="M219 215c0 1 0 2-1 3h1c1 2 1 4 1 6 1 5 1 11 6 15h0l-1 2c0 2-2 3-3 5s-1 5-2 7v3h-1c-1-2 0-3-1-4-1-2 0-5 0-7 0 0-2-1-3-3 0 5 2 10 1 15-1-4-1-9-1-13-1-10 0-20 4-29z" class="h"></path><defs><linearGradient id="Au" x1="209.678" y1="312.59" x2="188.793" y2="328.14" xlink:href="#B"><stop offset="0" stop-color="#3e3d3e"></stop><stop offset="1" stop-color="#5f5e5f"></stop></linearGradient></defs><path fill="url(#Au)" d="M179 317c2-1 4-1 6-1l11-2 9-3h2c1-1 1-1 1 0s0 1 1 2v1l-1 1h0l-1 1h1c-3 6-6 12-6 18v3l-1 1h-1l-2-2-19-19z"></path><path d="M443 131c1 1 2 1 3 2 0 0 2 2 2 3l3 4c2 1 3 2 5 2l1-1c4 4 9 10 12 15l1 2c-1-1-2-1-2-1l3 4c2 2 3 4 4 6 3 4 7 8 10 13 1 2 3 6 5 9v-1c3 1 4 5 7 7l4 6 1 5c-1 0-1 0-1 1l4 7 4 4 1-1 10 13h0l-7 11 7-1h3 5c1 1 1 1 0 2v3c1 1 10 0 11 0 4 1 7 2 10 4h1-1l10 6c2 1 5 2 8 3h4 5c8 0 18 0 25-4 1 0 3-1 4-1 1-1 3-2 4-3 2-1 6-3 8-5h1c2 0 4 3 5 4l1 1c1 2 7 7 7 9 1 0 1 1 1 2h0v2c-3 3-5 6-8 9-2 2-4 4-5 7l-2 1v-3h-1c-2 4-5 7-8 10-1 0-2 1-2 1l-1 1-21 6h-1c-2 0-3-1-4-1-4-1-8-3-11-5h-2c-2 2-3 5-6 7-1 2-2 5-5 6l-6 4-2 1c-2 1-4 2-6 4-2 1-4 3-6 3h-2-1 0l-3 4c-5 5-10 10-15 14l-2 2v1s0 1 1 2v2 2l-4 4-3 5h-1c2-5 1-10 0-16 0-1-1-3-2-4l-2-3s0-1 1-1l-2-2c-2-1-3-3-5-4l-1 1-3-2h-1c-2 0-4-1-6-1h-1c0 2-1 3-2 3v1h3l3 1c-2 1-2 1-4 1h-2s-1 0-2 1c-1 0-4 1-5 0l1-14c0-3-1-6 0-8v-4-55c0-9-1-19 1-28 0-1 0-3 1-4 2-3 6-3 9-3l1-1c-1 0-1-1-1-2h-8-7c-1-1-2-1-4-1h-16l-1-2-2-2-2 1c-1-1-2-1-3-2-1 0-2 0-4-1-2 1-4 3-6 5h0v2h-10-15-5-1-4-8c-3 0-8 1-10 0l-2-1h-2c-1-1-2-1-3-1v-1l-1-2c0-1 0-3-1-4 0-1 0-2-1-3-1-3-1-6-1-9 0-2-1-5-1-8 0-2 1-4 1-6v-1c1 1 2 1 3 2 2 1 4 1 6 1l-1-1c1-1 1-1 2-1-2-4-5-7-7-10h1c1 1 2 3 3 5h3c0-1-3-5-3-7h0c-1-2-1-3-1-5h0c0-4 1-6 1-9h1v2c1 3 1 5 3 7 1 0 1 0 1 1h3c2-1 5-2 8-3 5-2 10-3 16-4h0c3 0 6-1 9-1h0c1-1 11 1 14 1l-1-1h2c1 0 3 0 4-1h0l3-2c3-3 4-5 4-8 1-2 0-3 0-5z" class="l"></path><path d="M565 286h0c0-1 0-2 1-3h1c0 2-1 2-1 3h-1 0z" class="I"></path><path d="M530 260l1 1s-1 1-1 2h0 1c-2 1-3 2-5 2 1-2 2-3 4-5z" class="E"></path><path d="M571 277c2-1 3 0 5 0h0c1 2 4 3 6 4h1c1 1 2 2 3 2l1-1h3-2l-1 1h1c1 0 1 1 1 1-2 0-4-1-6-1-5-1-9-4-12-6z" class="C"></path><path d="M567 283l1 1c1 0 2 1 3 2s3 2 4 2c2 1 5 2 7 1v-1l1 2h0c-1 0-4 0-5 1h0c-2 1-6-1-8-2h-1l-3-3c0-1 1-1 1-3z" class="Z"></path><path d="M499 206c1 0 2 0 2 1l4 7v2l-1 1c0 3 2 6 1 9h0l-1-3-5-17z" class="O"></path><path d="M565 286h0 1l3 3h-1-2c-2 2-3 5-6 7l-4 3v-1h0v-1c3-4 6-7 9-11z" class="K"></path><path d="M565 286h1l3 3h-1-2-1v-3z" class="X"></path><path d="M561 278c1-6 1-10-2-15-1-2-4-6-6-8-2-1-4-2-6-2h-1c2-1 4 1 6 1v-1c1 1 2 2 3 2 3 3 6 7 8 11 1 3 1 5 0 7l-1 1c0 2-1 3-1 4z" class="b"></path><defs><linearGradient id="Av" x1="497.192" y1="204.793" x2="497.628" y2="194.754" xlink:href="#B"><stop offset="0" stop-color="#444345"></stop><stop offset="1" stop-color="#5c5a58"></stop></linearGradient></defs><path fill="url(#Av)" d="M490 189v-1c3 1 4 5 7 7l4 6 1 5c-1 0-1 0-1 1 0-1-1-1-2-1-3-6-6-12-9-17z"></path><path d="M530 260c4-3 8-5 13-4 5 0 9 2 11 6 1 1 2 2 3 4h-1v1c-2-3-6-8-9-8h-1-1c-1-1-2-1-3-1h-1-2c-3 1-5 2-8 3l-1-1z" class="B"></path><path d="M539 258h2 1c1 0 2 0 3 1h1 1c3 0 7 5 9 8v-1h1c1 3 1 7 0 10 0 2-2 5-3 6h-1l1-3v-1c1-4 1-8-1-12-4-5-9-6-14-8z" class="V"></path><path d="M478 179v-1h-1l-2-4c2 2 4 4 5 6l6 8c1 2 2 4 3 5h1c0 1 1 2 2 3 0 2 1 3 2 5 1 1 1 2 1 3s1 2 1 3c-1 0-2 0-2-1h-1c-1-2-1-4-2-6-1-1-2-3-2-4l-11-17z" class="I"></path><path d="M556 299l4-3c-1 2-2 5-5 6l-6 4-2 1c-2 1-4 2-6 4-2 1-4 3-6 3h-2-1c3-3 8-6 12-8 1-1 1-1 1-2 1-1 3-2 5-3s4-3 6-4v1h0v1z" class="C"></path><path d="M545 304c1-1 3-2 5-3s4-3 6-4v1h0v1l-7 5c-2 1-3 2-5 2 1-1 1-1 1-2z" class="T"></path><defs><linearGradient id="Aw" x1="532.316" y1="260.843" x2="535.667" y2="261.881" xlink:href="#B"><stop offset="0" stop-color="#535456"></stop><stop offset="1" stop-color="#6a6a6c"></stop></linearGradient></defs><path fill="url(#Aw)" d="M531 261l8-3c5 2 10 3 14 8 2 4 2 8 1 12v1c-1 1-1 1-1 2l-1 1v1c-1 1-2 2-2 3 0-1-1-1-1-1-1-1-1-3-1-4l1-1v-1c0-3 1-7 0-9s-2-3-4-4c-1 0-1 0-2 1-1-1-1-2-2-3-4-2-7-2-10-1h-1 0c0-1 1-2 1-2z"></path><defs><linearGradient id="Ax" x1="504.713" y1="279.506" x2="548.952" y2="280.579" xlink:href="#B"><stop offset="0" stop-color="#c2c2c1"></stop><stop offset="1" stop-color="#e8e7e8"></stop></linearGradient></defs><path fill="url(#Ax)" d="M506 254v3 10 4h2 0c0 7 3 13 9 17 4 3 10 5 15 5 3-1 5-2 7-3 1-1 2-2 3-4 1-1 2-3 2-5h1l1-1v-1c1-3 2-5 1-8h-2-1c0-1 0-3-1-4 1-1 1-1 2-1 2 1 3 2 4 4s0 6 0 9v1l-1 1c0 1 0 3 1 4 0 0 1 0 1 1 0 0-1 1-2 1s-1 1-1 2v1c-4 2-6 3-10 4-1 1-1 1-2 1h-7 0c-1 0-3 0-4-1-8-3-13-7-17-14-3-7-3-13-2-20 0-2 0-4 1-6z"></path><path d="M531 263c3-1 6-1 10 1 1 1 1 2 2 3s1 3 1 4v1c-1 3-2 5-4 6-3 2-6 2-9 1-2-1-4-3-5-6-1-2-1-5 0-8 2 0 3-1 5-2z" class="B"></path><path d="M571 277v-1c-2-1-4-2-5-4-3 11-7 19-17 25-8 6-18 7-28 5-5-2-11-4-15-8-1-2-2-3-4-5-1-1-2-2-2-3-3-5-5-10-5-15v-7-3-1c0-1 0-2 1-2 0-1-1-2 0-3v-3s0-2 1-2c0-1-1-2 0-3v-2h1c0 1 1 1 1 2v11 3c0-2 1-3 0-5 0-2-1-4 0-5 0-1 0-4 1-5v-3h0 1v-2 2 3 5c-1 10-2 19 1 29 2 7 6 14 13 18 6 3 15 4 21 2 10-3 18-9 23-18l2-4c0-1 1-2 1-4l1-1c1-2 1-4 0-7-2-4-5-8-8-11-1 0-2-1-3-2 0 0-1 0-1-1h0c-2-1-4-2-5-4 1 1 2 1 3 1l10 6h-2l3 3v1c4 3 4 8 7 12h0c0 1 0 1 1 1 1 1 2 2 3 2h0 2 1 1c0-2-1-2-2-3 2 0 3 2 5 3l5 7h-1c-2-1-5-2-6-4h0c-2 0-3-1-5 0z" class="I"></path><path d="M505 214l4 4 1-1 10 13h0l-7 11 7-1h3 5c1 1 1 1 0 2v3c1 1 10 0 11 0 4 1 7 2 10 4h1-1c-1 0-2 0-3-1-6-1-12-1-18 2 0 1-1 1-2 2 0-1-1-2-1-3v-1-2c-1 0-1-1-2-1-2-1-4 0-6 1-1 1-2 3-3 4v1c-1 1-2 2-2 3 0 0 0 1-1 1v3s-1 1-1 2 0 3-1 4c0 1 0 2-1 3v5-1h0-2v-4-10-3c1-5 1-10 1-15-1-5-3-10-3-16l1 3h0c1-3-1-6-1-9l1-1v-2z" class="B"></path><defs><linearGradient id="Ay" x1="496.488" y1="233.221" x2="515.627" y2="244.49" xlink:href="#B"><stop offset="0" stop-color="#878587"></stop><stop offset="1" stop-color="#b6b7b6"></stop></linearGradient></defs><path fill="url(#Ay)" d="M505 214l4 4c1 4 1 8 1 12s1 9 1 13-1 8-2 11c-1 6-2 11-1 17h-2v-4-10-3c1-5 1-10 1-15-1-5-3-10-3-16l1 3h0c1-3-1-6-1-9l1-1v-2z"></path><defs><linearGradient id="Az" x1="508.106" y1="216.781" x2="506.212" y2="222.008" xlink:href="#B"><stop offset="0" stop-color="#565657"></stop><stop offset="1" stop-color="#6c6e6e"></stop></linearGradient></defs><path fill="url(#Az)" d="M505 214l4 4c1 4 1 8 1 12-1-1 0-3-1-5 0 0-1 0-1-1-1-3-2-5-3-8v-2z"></path><path d="M624 251v-1c1 2 7 7 7 9 1 0 1 1 1 2h0v2c-3 3-5 6-8 9-2 2-4 4-5 7l-2 1v-3h-1c-2 4-5 7-8 10-1 0-2 1-2 1l-1 1-21 6h-1c-2 0-3-1-4-1-4-1-8-3-11-5h1 1c2 1 6 3 8 2h0c1-1 4-1 5-1h0l-1-2h0c1-1 3-1 4-1 4 0 9-1 12-3s5-4 7-6 19-23 19-26v-1z" class="C"></path><path d="M606 288h-1 0l1-1h-1l1-1s1 0 1-1v-1c2 0 3-2 4-2l5-5h0c-2 4-5 7-8 10-1 0-2 1-2 1z" class="H"></path><path d="M631 259c1 0 1 1 1 2h0v2c-3 3-5 6-8 9-2 2-4 4-5 7l-2 1v-3h-1 0c4-7 9-13 15-18z" class="F"></path><defs><linearGradient id="BA" x1="505.39" y1="262.32" x2="539.731" y2="285.082" xlink:href="#B"><stop offset="0" stop-color="#0e0e0e"></stop><stop offset="1" stop-color="#474646"></stop></linearGradient></defs><path fill="url(#BA)" d="M508 271v1-5c1-1 1-2 1-3 1-1 1-3 1-4s1-2 1-2v-3c1 0 1-1 1-1 0-1 1-2 2-3v-1c1-1 2-3 3-4 2-1 4-2 6-1 1 0 1 1 2 1v2 1c0 1 1 2 1 3l-2 2c-4 5-5 8-5 15 2 5 4 9 8 12 6 3 11 2 17 1v-1c0 2-1 4-2 5-1 2-2 3-3 4-2 1-4 2-7 3-5 0-11-2-15-5-6-4-9-10-9-17z"></path><path d="M517 260v-1c0-1 0-1-1-1h0v-2s0 1-1 1h-1c1-2 1-5 2-7 0-2 2-4 3-4h1 5v2 1c0 1 1 2 1 3l-2 2c-1-2 2-4-1-5-1-1-2-1-2-1-2 1-3 2-3 3-1 3-1 6-1 9z" class="M"></path><path d="M517 260c0-3 0-6 1-9 0-1 1-2 3-3 0 0 1 0 2 1 3 1 0 3 1 5-4 5-5 8-5 15v-4c-1-1-1-1-1-2l1-1c0-1 0-1-1-1h0l-1-1z" class="U"></path><path d="M527 281c6 3 11 2 17 1v-1c0 2-1 4-2 5-1 2-2 3-3 4l-1-1h0c-3-1-7 0-10-2v-2l-1-1v-1c1 1 1 1 2 0 1 1 1 1 2 1h1c1 1 2 0 2 1h3l-2-1c-1 0-2 0-2-1h-3l-1-1h-2v-1z" class="B"></path><path d="M528 285c5 1 9 2 14 1-1 2-2 3-3 4l-1-1h0c-3-1-7 0-10-2v-2z" class="E"></path><path d="M395 155c10-2 21-3 31-1 10 1 19 5 27 10 3 1 5 3 6 5 1 0 4 3 4 4v1 5-3c-1-1-3-2-4-3v-2c-1-1-2-2-3-2h-1c0 2 0 3-1 4l-1 2h-1l1-4-1-1-1 2c0 1-1 2-1 3-1-1-2-2-3-2-5-3-10-5-14-8l-2-1h-1 0c-1-1-1-2-2-3h-1v1c-4-2-7-2-11-1 0-1-1-1-2-1-2-1-6-1-8 0h1c-5 2-11 5-15 10l-3-2c-3-2-5-4-7-7l2-1c4-2 7-4 11-5z" class="F"></path><path d="M452 170v-5c3 1 1 3 2 4 1 0 1-1 2-2 1 0 1 1 2 2h1c1 0 4 3 4 4v1 5-3c-1-1-3-2-4-3v-2c-1-1-2-2-3-2h-1c0 2 0 3-1 4l-1 2h-1l1-4-1-1z" class="X"></path><path d="M424 158c2 1 3 1 5 1 8 2 17 7 22 13h0c0 1-1 2-1 3-1-1-2-2-3-2-5-3-10-5-14-8l-2-1h-1 0c-1-1-1-2-2-3-2-1-3-2-4-3z" class="E"></path><path d="M424 158c2 1 3 1 5 1 1 2 3 3 3 4s0 1 1 2l-2-1h-1 0c-1-1-1-2-2-3-2-1-3-2-4-3z" class="g"></path><defs><linearGradient id="BB" x1="407.343" y1="154.959" x2="409.735" y2="164.004" xlink:href="#B"><stop offset="0" stop-color="#828182"></stop><stop offset="1" stop-color="#9f9e9f"></stop></linearGradient></defs><path fill="url(#BB)" d="M386 161l9-3c10-3 19-2 29 0 1 1 2 2 4 3h-1v1c-4-2-7-2-11-1 0-1-1-1-2-1-2-1-6-1-8 0h1c-5 2-11 5-15 10l-3-2c1 0 2-1 2-2 0-2-6-3-7-4 1-1 1-1 2-1z"></path><path d="M406 160h1c-5 2-11 5-15 10l-3-2c1 0 2-1 2-2 0-2-6-3-7-4 1-1 1-1 2-1h1c1 1 4 0 5 0l14-1z" class="M"></path><defs><linearGradient id="BC" x1="450.422" y1="190.124" x2="406.195" y2="135.901" xlink:href="#B"><stop offset="0" stop-color="#d5d4d5"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#BC)" d="M443 131c1 1 2 1 3 2 0 0 2 2 2 3l3 4c2 1 3 2 5 2l1-1c4 4 9 10 12 15l1 2c-1-1-2-1-2-1l3 4c2 2 3 4 4 6-2-1-3-2-4-4h0 0l1 2c1 1 3 3 4 6v1h0v1c1 0 1 1 2 2h0l-1-1 2 3c1 1 2 2 3 4 1 1 2 3 3 5 2 2 4 4 4 7-1-1-2-3-3-5l-6-8c-1-2-3-4-5-6l2 4h1v1h-1c-5-7-10-15-18-18h-1l-1-1c-1 0-2-1-4-2h-1 0c-2 0-2-1-3-1v-1h1c-2 0-3 0-4-1h-2c-3-1-5-1-8-1-1-1-2 0-4-1h-1-1c-1 0 0 0-1-1h-2c-1 0-3 0-5-1-5 0-11 0-16 1h-3 0-1c-1 0-1 1-2 1-2 0-3 1-5 2-4 1-7 3-11 5-1-1-3-2-4-3v-2h1 3c2-1 5-2 8-3 5-2 10-3 16-4h0c3 0 6-1 9-1h0c1-1 11 1 14 1l-1-1h2c1 0 3 0 4-1h0l3-2c3-3 4-5 4-8 1-2 0-3 0-5z"></path><path d="M432 147c1 0 3 0 4-1l1 1v1l-2 1h1c-1 1-4-1-5-1l-1-1h2z" class="N"></path><path d="M453 154l-4-1s-1 0-1-1 1-1 1-2h1c1 1 2 1 3 2l2 2h-2z" class="f"></path><path d="M453 152c6 2 12 6 16 11h0c-4-4-11-7-16-9h2l-2-2z" class="h"></path><path d="M443 131c1 1 2 1 3 2-1 1-2 2-2 3 0 2 1 3 0 5 1 0 2 1 2 2 0 2 1 4 0 5s-1 1-1 2c-3 1-6 0-9-1h-1l2-1v-1l-1-1h0l3-2c3-3 4-5 4-8 1-2 0-3 0-5z" class="D"></path><path d="M437 148c1-1 5-4 6-4v2c1 1 1 2 2 4-3 1-6 0-9-1h-1l2-1z" class="M"></path><defs><linearGradient id="BD" x1="470.49" y1="154.032" x2="450.128" y2="154.802" xlink:href="#B"><stop offset="0" stop-color="#bab9bd"></stop><stop offset="1" stop-color="#f0eeec"></stop></linearGradient></defs><path fill="url(#BD)" d="M451 140c2 1 3 2 5 2l1-1c4 4 9 10 12 15l1 2c-1-1-2-1-2-1l3 4c2 2 3 4 4 6-2-1-3-2-4-4h0 0l1 2s0 1 1 2h0c1 1 2 2 1 3h0c-1-1-2-3-3-4 0-1-2-2-2-3h0c-4-5-10-9-16-11-1-1-2-1-3-2v-1-1c1-2 1-5 1-7v-1z"></path><path d="M451 140c2 1 3 2 5 2l1-1c4 4 9 10 12 15l1 2c-1-1-2-1-2-1-3-2-5-5-7-7-3-2-6-3-8-5-1-2-2-3-2-4v-1z" class="c"></path><path d="M376 145h1v2c1 3 1 5 3 7 1 0 1 0 1 1h-1v2c1 1 3 2 4 3l-2 1c2 3 4 5 7 7l3 2c4-5 10-8 15-10h-1c2-1 6-1 8 0 1 0 2 0 2 1 4-1 7-1 11 1v-1h1c1 1 1 2 2 3h0c0 3 0 5-1 7 0 2-1 4-2 5-3 3-6 4-10 5-1 1-2 1-3 1h-8c-3 0-6 0-8-1-6-1-10-3-16-5l-4-3-1-1c1-1 1-1 2-1-2-4-5-7-7-10h1c1 1 2 3 3 5h3c0-1-3-5-3-7h0c-1-2-1-3-1-5h0c0-4 1-6 1-9z" class="I"></path><path d="M382 176c4 0 7 1 11 2 1 1 3 1 4 3h1 0c-6-1-10-3-16-5z" class="C"></path><path d="M376 145h1v2c1 3 1 5 3 7 1 0 1 0 1 1h-1v2c1 1 3 2 4 3l-2 1v1l-1-1v1h-1 1l-1 1c0 1 1 3 2 4 4 6 13 10 20 12h-3c-5-2-9-4-13-7h0-1c-6-4-8-12-10-18h0c0-4 1-6 1-9z" class="H"></path><defs><linearGradient id="BE" x1="397.781" y1="162.046" x2="397.299" y2="178.178" xlink:href="#B"><stop offset="0" stop-color="#989799"></stop><stop offset="1" stop-color="#eeeded"></stop></linearGradient></defs><path fill="url(#BE)" d="M402 179c-7-2-16-6-20-12-1-1-2-3-2-4l1-1h-1 1v-1l1 1v-1c2 3 4 5 7 7l3 2c2 1 4 1 6 2 4 2 7 4 12 4h0c2 1 5 0 7 1-2 1-3 2-5 2h-1-6c-1 0-3-1-3 0z"></path><path d="M406 160c2-1 6-1 8 0 1 0 2 0 2 1 4-1 7-1 11 1 1 2 1 3 1 5l-1 2c-1 2-3 5-5 6h-2c-3 2-7 1-10 1-5 0-8-2-12-4-2-1-4-1-6-2 4-5 10-8 15-10h-1z" class="B"></path><defs><linearGradient id="BF" x1="403.836" y1="160.629" x2="405.197" y2="169.119" xlink:href="#B"><stop offset="0" stop-color="#787876"></stop><stop offset="1" stop-color="#acabad"></stop></linearGradient></defs><path fill="url(#BF)" d="M406 160c2-1 6-1 8 0 1 0 2 0 2 1-7 1-13 4-17 10-1 0-1 1-1 1-2-1-4-1-6-2 4-5 10-8 15-10h-1z"></path><path d="M420 175c1-1 3-2 4-3v-1c-3-4-11-3-15-2-2 1-6 4-8 3h0c4-5 8-7 15-8 2 0 7 0 9 2 1 1 1 2 1 3h1c-1 2-3 5-5 6h-2z" class="X"></path><path d="M617 245h1c2 0 4 3 5 4l1 1v1l-2 2-3 3c-1 2-2 3-3 5-2 1-3 3-5 4h0l-3 3c-1 2-1 3-2 4-4 5-8 11-15 12h-2s0-1-1-1h-1l1-1h2-3l-1 1c-1 0-2-1-3-2l-5-7c-2-1-3-3-5-3 1 1 2 1 2 3h-1-1-2 0c-1 0-2-1-3-2-1 0-1 0-1-1h0c-3-4-3-9-7-12v-1l-3-3h2c2 1 5 2 8 3h4 5c8 0 18 0 25-4 1 0 3-1 4-1 1-1 3-2 4-3 2-1 6-3 8-5z" class="Y"></path><path d="M560 258l-3-3h2c2 1 5 2 8 3 1 0 3 1 4 1 4 1 7 1 11 1-3 1-7 1-9 1h-1l-12-3h0z" class="N"></path><path d="M570 268l-2-3h9 8c2 0 3-1 5 0v1h-1c-2 0-4 1-6 1h-11c-1 0-1 0-2 1z" class="W"></path><path d="M609 254c2-2 6-4 8-4l1 1v1s-1 1-1 2v2h1 1c-1 2-2 3-3 5-2 1-3 3-5 4h0l-3 3c-1 2-1 3-2 4h-1l-1-1c-1-1-1 1-3 1s-5 0-6-2h1v1h2 3 1l1-1-1-2c-1 0-1 0-1-1v-3h1 1 1 3 2 1 0 1 1c0-1 2-2 3-3v-1c-1 1-2 1-3 1 1-1 4-2 5-3h-1c-3 0-7 1-10 2l11-8h-2c-2 0-4 1-6 2z" class="k"></path><path d="M608 268h0v-3h3l-3 3z" class="T"></path><path d="M617 245h1c2 0 4 3 5 4l1 1v1l-2 2-3 3h-1-1v-2c0-1 1-2 1-2v-1l-1-1c-2 0-6 2-8 4l-7 2c-2 0-4 1-6 2-1 0-3 0-4 1-3 1-7 1-10 1-4 0-7 0-11-1-1 0-3-1-4-1h4 5c8 0 18 0 25-4 1 0 3-1 4-1 1-1 3-2 4-3 2-1 6-3 8-5z" class="d"></path><path d="M602 256l1-1c1-1 3-2 5-2 4-3 8-6 13-5l1 1h1l1 1v1l-2 2-3 3h-1-1v-2c0-1 1-2 1-2v-1l-1-1c-2 0-6 2-8 4l-7 2z" class="D"></path><path d="M623 249l1 1v1l-2 2-3 3h-1v-1c1 0 1-1 1-2h1c0-2 0-3 2-3v-1h1z" class="C"></path><path d="M623 249l1 1v1l-2 2c-1-1-1-2 0-3v-1h1z" class="H"></path><defs><linearGradient id="BG" x1="599.578" y1="280.041" x2="573.618" y2="266.313" xlink:href="#B"><stop offset="0" stop-color="#c5c4c5"></stop><stop offset="1" stop-color="#efeeee"></stop></linearGradient></defs><path fill="url(#BG)" d="M590 265c1 2 2 4 4 5h1c1 2 4 2 6 2s2-2 3-1l1 1h1c-4 5-8 11-15 12h-2s0-1-1-1h-1l1-1h2-3l-1 1c-1 0-2-1-3-2l-5-7c-2-1-3-3-5-3l-3-3c1-1 1-1 2-1h11c2 0 4-1 6-1h1v-1z"></path><path d="M594 270h1c1 2 4 2 6 2s2-2 3-1l1 1c-2 0-2 0-3 1h0c-4 1-5 0-8-2v-1z" class="G"></path><path d="M590 265c1 2 2 4 4 5v1l-5-2c-2-2-4-2-6-2 2 0 4-1 6-1h1v-1z" class="H"></path><path d="M570 268c1-1 1-1 2-1v1c1 2 4 3 6 5 3 3 5 6 9 9l-1 1c-1 0-2-1-3-2l-5-7c-2-1-3-3-5-3l-3-3z" class="P"></path><path d="M487 208c1 1 1 4 2 5 3 8 5 16 5 24 0 14-4 27-1 40 0 4 1 7 3 10l3 5c0 1 1 2 1 3 0 0-6 7-6 8 4-1 6-2 10-4h0c2 2 5 3 7 4 6 3 12 5 19 4 6 0 10-1 15-3 0 1 0 1-1 2-4 2-9 5-12 8h0l-3 4c-5 5-10 10-15 14l-2 2v1s0 1 1 2v2 2l-4 4-3 5h-1c2-5 1-10 0-16 0-1-1-3-2-4l-2-3s0-1 1-1l-2-2c-2-1-3-3-5-4l-1 1-3-2h-1c-2 0-4-1-6-1h-1c0 2-1 3-2 3v1h3l3 1c-2 1-2 1-4 1h-2s-1 0-2 1c-1 0-4 1-5 0l1-14c0-3-1-6 0-8v-4-55c0-9-1-19 1-28 0-1 0-3 1-4 2-3 6-3 9-3l1-1z" class="B"></path><path d="M475 299l2-1c3 1 6 2 9 4h-5l-1 1c0 1 0 1 1 1 0 1 1 2 1 3l-5-2-2-2v-4z" class="E"></path><path d="M475 299l2-1v7l-2-2v-4zm30 12l-1-1h-1c0-1-1-2-3-2h0c9 0 17 3 25 5 3 0 5 1 7 1l-3 4-2-1c-1-1-3-2-5-2-4-1-10-2-14-3-1-1-2-1-3-1z" class="F"></path><path d="M499 313l2 2c0 1 1 2 2 3 0 1 0 1 1 2 1 2 1 3 2 6h0v3l1 1v7h2l2-1 1-2v1s0 1 1 2v2 2l-4 4-3 5h-1c2-5 1-10 0-16 0-1-1-3-2-4l-2-3s0-1 1-1h1 0 1c0-2-1-4-1-6-1-2-3-5-4-7z" class="M"></path><path d="M503 320c2 3 2 6 3 10v3l-1 1c0-1-1-3-2-4l-2-3s0-1 1-1h1 0 1c0-2-1-4-1-6z" class="O"></path><path d="M503 326h1c0 1 0 2-1 4l-2-3s0-1 1-1h1 0z" class="i"></path><path d="M512 334v1s0 1 1 2v2 2l-4 4v-8l2-1 1-2z" class="W"></path><defs><linearGradient id="BH" x1="494.665" y1="316.312" x2="497.949" y2="312.634" xlink:href="#B"><stop offset="0" stop-color="#676666"></stop><stop offset="1" stop-color="#8a8a8a"></stop></linearGradient></defs><path fill="url(#BH)" d="M482 307c0-1-1-2-1-3-1 0-1 0-1-1l1-1h5c3 2 5 4 8 6l4 4 1 1c1 2 3 5 4 7 0 2 1 4 1 6h-1 0-1l-2-2c-2-1-3-3-5-4l-6-6c-2-3-4-5-7-7z"></path><path d="M495 312c1 2 2 3 3 5l-1-1c-1-1-1-1-2-1l-1-1h1v-2z" class="P"></path><defs><linearGradient id="BI" x1="497.676" y1="323.07" x2="500.484" y2="317.673" xlink:href="#B"><stop offset="0" stop-color="#8f8c8e"></stop><stop offset="1" stop-color="#aaaaa9"></stop></linearGradient></defs><path fill="url(#BI)" d="M495 315c1 0 1 0 2 1l1 1c2 2 4 6 5 9h0-1l-2-2c-1-2-2-3-2-4-1-1-2-3-3-5z"></path><defs><linearGradient id="BJ" x1="483.966" y1="305.213" x2="495.046" y2="313.05" xlink:href="#B"><stop offset="0" stop-color="#868484"></stop><stop offset="1" stop-color="#afacad"></stop></linearGradient></defs><path fill="url(#BJ)" d="M490 311l-7-6h0c3-1 6 2 9 4l3 3v2h-1l-4-3z"></path><path d="M490 311l4 3 1 1c1 2 2 4 3 5 0 1 1 2 2 4-2-1-3-3-5-4l-6-6c0-1 0-2 1-3z" class="W"></path><path d="M475 303l2 2 5 2c3 2 5 4 7 7l6 6-1 1-3-2h-1c-2 0-4-1-6-1h-1c0 2-1 3-2 3v1h3l3 1c-2 1-2 1-4 1h-2s-1 0-2 1c-1 0-4 1-5 0l1-14c0-3-1-6 0-8z" class="B"></path><path d="M475 303h0v12h9c2 1 5 2 7 4h-1c-2 0-4-1-6-1h-1c0 2-1 3-2 3v1h3l3 1c-2 1-2 1-4 1h-2s-1 0-2 1c-1 0-4 1-5 0l1-14c0-3-1-6 0-8z" class="J"></path><path d="M477 323l-1-1v-1-1s0-1 1-2c0-1 2-1 4-1-2 0-3 1-3 2l-1 3 1 1h-1z" class="Q"></path><path d="M484 322l3 1c-2 1-2 1-4 1h-2s-1 0-2 1c-2 0-3 0-4-1v-1h0 2 1l6-1z" class="V"></path><path d="M484 315c2 1 5 2 7 4h-1c-2 0-4-1-6-1h-1c0 2-1 3-2 3v1h3l-6 1-1-1 1-3c0-1 1-2 3-2 0 0 2-1 3-2zm21-4c1 0 2 0 3 1 4 1 10 2 14 3 2 0 4 1 5 2l2 1c-5 5-10 10-15 14l-2 2-1 2-1-1c-1-2-1-4-2-7-1-1-1-2-2-3v-3c-1-1-1-2-2-3v-1s-1-1-1-2h1c1 1 1 2 2 3l-1-4v-2c0-1 0-1 1-1l-1-1z" class="E"></path><path d="M511 336l-1-1c-1-2-1-4-2-7-1-1-1-2-2-3v-3c-1-1-1-2-2-3v-1s-1-1-1-2h1c1 1 1 2 2 3 1 2 1 4 2 6v-1c1 2 2 5 2 7h1 1 0v1h-2c1 1 1 1 1 2l1-1h1l1-1h0l-2 2-1 2z" class="f"></path><path d="M430 164h1l2 1c4 3 9 5 14 8 1 0 2 1 3 2 0-1 1-2 1-3l1-2 1 1-1 4h1l1-2c1-1 1-2 1-4h1c1 0 2 1 3 2v2c1 1 3 2 4 3v3-5-1c8 9 15 18 21 28 0 1 2 4 2 5h-8-7c-1-1-2-1-4-1h-16l-1-2-2-2-2 1c-1-1-2-1-3-2-1 0-2 0-4-1-2 1-4 3-6 5h0v2h-10-15-5-1-4-8c-3 0-8 1-10 0l-2-1h-2c-1-1-2-1-3-1v-1l-1-2c0-1 0-3-1-4 0-1 0-2-1-3-1-3-1-6-1-9 0-2-1-5-1-8 0-2 1-4 1-6v-1c1 1 2 1 3 2 2 1 4 1 6 1l4 3c6 2 10 4 16 5 2 1 5 1 8 1h8c1 0 2 0 3-1 4-1 7-2 10-5 1-1 2-3 2-5 1-2 1-4 1-7z" class="B"></path><path d="M430 202l1-3v-1c1 1 2 1 2 2v3 1h0 0c0-1 0-2-1-2-1-1-1-1-2 0z" class="R"></path><path d="M406 202l1-1c1 2 1 3 2 4h0l-1 1h-5c1 0 2 0 3-1v-3z" class="f"></path><path d="M461 193c0-1-1-1-1-2 1-1 2-1 4-2 2 1 3 1 5 2-1 0-2 1-3 0h-1v-1c-1 1-3 2-4 2v1z" class="F"></path><path d="M439 199c3 0 6-1 9 1v1l-2 1c-1-1-2-1-3-2-1 0-2 0-4-1z" class="e"></path><path d="M423 206v-4-6c1 1 3 3 4 5v4c1 1 2 1 3 0v-3c1-1 1-1 2 0 1 0 1 1 1 2h0v2h-10 0z" class="T"></path><path d="M369 185c2 7 5 13 9 20h-2c-1-1-2-1-3-1v-1l-1-2c0-1 0-3-1-4 0-1 0-2-1-3-1-3-1-6-1-9z" class="e"></path><path d="M448 191h0c4-2 6-6 9-9 2-1 2-1 4-1l2 2h1c1 1 1 2 2 3-5 1-9 1-12 4 0 1-1 2-2 3 0 0-3 0-4 1h0c0-1-1-1-1-1h-1-2c0 1-1 1-1 1v-1c1-1 3-2 5-2z" class="m"></path><path d="M383 193l-1-1c-1-1 0-3-1-4h0v-1c-1 1 0 3 0 4l-1 4c-1-2-3-4-5-5l-2-1c-1-2 0-4 0-6h0 1 1c1 0 1 1 2 1 2 0 3 2 4 4l1-8c2 1 4 1 7 2v5h1c0 3 0 5 1 7l-1 1-1-2c0-3-1-5-1-8l-6 1c0 2 0 4 1 7z" class="F"></path><path d="M375 185h1c0 1 1 1 1 1l2 2h0c1 0 1 0 1 1v2c-1 1 0 0-1 0l-3-3c-1 0-1-1-1-3z" class="J"></path><path d="M454 173c1-1 1-2 1-4h1c1 0 2 1 3 2v2c1 1 3 2 4 3v3c0 1 0 2 1 3h0v1h-1l-2-2c-2 0-2 0-4 1-3 3-5 7-9 9h0l-1-1h-3l-1 1 5-3 2-2c2-1 6-6 5-8 0-1 0-2-1-3l-1 1v-1l1-2z" class="Y"></path><path d="M454 173c1-1 1-2 1-4h1c1 0 2 1 3 2v2c1 1 3 2 4 3v3c0 1 0 2 1 3h0-2v-2c-1-1-2-1-3-1h-1c-1 1-1 0-2 0 1-1 1-2 1-3l-3-3z" class="N"></path><path d="M383 193c-1-3-1-5-1-7l6-1c0 3 1 5 1 8l1 2c0 2 1 3 1 4v4c-1 0-1 0-2 1l1 2c-3 0-8 1-10 0h8c-3-5-4-8-5-13z" class="d"></path><path d="M389 204c-2-4-5-9-5-13 1-1 2-1 3 0 1 0 2 1 2 2l1 2c0 2 1 3 1 4v4c-1 0-1 0-2 1z" class="k"></path><defs><linearGradient id="BK" x1="470.947" y1="195.3" x2="464.533" y2="200.502" xlink:href="#B"><stop offset="0" stop-color="#0f0f0f"></stop><stop offset="1" stop-color="#383839"></stop></linearGradient></defs><path fill="url(#BK)" d="M461 193v-1c1 0 3-1 4-2v1h1c1 1 2 0 3 0 3 4 5 9 6 14h3v1h-7c-1-1-2-1-4-1-1-4-4-8-6-12z"></path><path d="M406 202c0-3 1-4 3-7h1 2c1 1 1 1 2 1l1 1h0v-1c1 0 2 2 4 2 1 1 1 2 2 3 0 1-1 4 0 4l2 1h0-15l1-1h0c-1-1-1-2-2-4l-1 1z" class="Q"></path><path d="M407 201c2 0 3-2 5-1 1 0 4 4 5 5-3 0-5 1-8 0h0c-1-1-1-2-2-4z" class="F"></path><path d="M390 187h1c3-1 5-1 7 1h1c-1 1-1 1-2 1l-2 2c-1 2 0 5 1 6 1 4 4 7 6 9h-4-8l-1-2c1-1 1-1 2-1v-4c0-1-1-2-1-4l1-1c-1-2-1-4-1-7z" class="d"></path><path d="M391 199c1 3 3 5 6 6l1 1h-8l-1-2c1-1 1-1 2-1v-4z" class="I"></path><defs><linearGradient id="BL" x1="426.449" y1="181.773" x2="428.115" y2="195.289" xlink:href="#B"><stop offset="0" stop-color="#b1afb1"></stop><stop offset="1" stop-color="#e8e8e8"></stop></linearGradient></defs><path fill="url(#BL)" d="M430 164h1l2 1c4 3 9 5 14 8 1 0 2 1 3 2 0-1 1-2 1-3l1-2 1 1-1 4h1v1l1-1c1 1 1 2 1 3 1 2-3 7-5 8l-2 2-5 3 1-1h3l1 1c-2 0-4 1-5 2v1s1 0 1-1h2 1s1 0 1 1c-2 0-5 0-8 1-8 1-15 0-22-4-5-2-6-4-8-9h-4 8c1 0 2 0 3-1 4-1 7-2 10-5 1-1 2-3 2-5 1-2 1-4 1-7z"></path><path d="M427 176v2c-1 2 0 7-2 9h-2c-3 0-6-1-8-3-1 0-1-1-2-1h-1l2-1c1 0 2 0 3-1 4-1 7-2 10-5z" class="B"></path><path d="M452 170l1 1-1 4h1v1l1-1c1 1 1 2 1 3 1 2-3 7-5 8l-2 2-5 3h-2c-5 1-9 1-14 0-1 0-2 0-3-1l-1-1c1 0 2-1 3 0 3 0 8 0 11-1h0c2 0 3-1 4-2h-4c2-1 5-2 7-4l1-1c2-2 3-4 5-6 0-1 1-2 1-3l1-2z" class="j"></path><defs><linearGradient id="BM" x1="448.329" y1="172.689" x2="443.763" y2="186.562" xlink:href="#B"><stop offset="0" stop-color="#9c999b"></stop><stop offset="1" stop-color="#bfbdbe"></stop></linearGradient></defs><path fill="url(#BM)" d="M452 170l1 1-1 4h1v1c-4 4-6 7-12 10h-4c2-1 5-2 7-4l1-1c2-2 3-4 5-6 0-1 1-2 1-3l1-2z"></path><path d="M430 164h1l2 1c4 3 9 5 14 8 1 0 2 1 3 2-2 2-3 4-5 6l-1 1c-2 2-5 3-7 4h4c-1 1-2 2-4 2h-5 0-1-1c-2-1-5 0-7-1h2c2-2 1-7 2-9v-2c1-1 2-3 2-5 1-2 1-4 1-7z" class="B"></path><path d="M433 178v-2h1 2 1c2 0 6 2 7 4h0l1 1-1 1c-1 0-1-1-1-2-1 0-1-1-2-1s-2-1-3-1-3 0-4-1h0 0v1h-1z" class="F"></path><defs><linearGradient id="BN" x1="427.186" y1="174.669" x2="429.031" y2="184.678" xlink:href="#B"><stop offset="0" stop-color="#404040"></stop><stop offset="1" stop-color="#626161"></stop></linearGradient></defs><path fill="url(#BN)" d="M427 176c1-1 2-3 2-5 1 3 1 5 1 7v7 2s1 0 1 1h-1c-2-1-5 0-7-1h2c2-2 1-7 2-9v-2z"></path><path d="M433 178h1v-1h0 0c1 1 3 1 4 1s2 1 3 1 1 1 2 1c0 1 0 2 1 2-2 2-5 3-7 4h4c-1 1-2 2-4 2h-5 0l1-2c-1-2 0-5 0-8z" class="J"></path><path d="M437 186h4c-1 1-2 2-4 2h-5l5-2z" class="I"></path><path d="M85 293c0-2 1-3 1-4v-1c1-1 1-1 1-2v-1c1-1 0-3 1-4 0 0 1 0 1 1h0c1 0 2 0 3 1h-1c2 0 3-1 4-2 2 2 3 4 5 5l1 1h0l21 8c3-1 6-1 8-1l2-1c-1 0-1 0-1-1 1-1 1-1 2-1h-1 0v-1l2-2h0s3-1 4-1 2-1 3-1h2 1c2 5 7 10 12 14 2 1 4 2 5 3h1v2h1c4 2 7 4 11 7l2 2 3 3 19 19 2 2h1l1-1v12c0 1 1 2 1 3h1v1c1 0 1 1 1 1l1 1 1 1 6 5h0l3 2 6 3c4 3 5 6 8 9l1-1c1 0 2-1 3-2l2 1 1 1c-2 1-3 3-3 5 0 1 1 2 1 3l1 1h1c0 1 1 1 2 2l-2 2h1 1l5 6 2-2 1 1c1 1 1 3 0 4 1 3 1 5 1 8 0 1 1 2 2 3l1 2 1 2 1-1 1 1h1v-1c0-1 0-2-1-4h1l3 4c1 1 1 1 2 0 0 2 0 4 1 6 1 5 4 9 4 14 0 3 3 6 5 9l3 2 5 3c1 1 5 4 6 4l3 3-2 1c1 1 2 2 2 3s0 2-1 2v1 1 1s1 0 2 1h0l-1 1-3 1-3 3c-1 1-1 3 0 4v1c1 1 0 3 0 4v1c-1-1-1-1-1-2h-1 0l2 5c2 4 4 8 5 12 1 3 2 5 2 8 0 0 1 1 1 2s-1 2 0 3l2 2 2 10 3 12 2 11c2 9 3 18 3 27-1 11 0 21-3 32 0 2-1 4-1 5-1 2-3 3-3 5 0 0-1 0-1 1-1-1-1-2-2-2-2-1-5-1-7-1-1 0-1 1-2 1h-1c0-2-2-3-3-4-2-1-3-3-5-6-3-4-3-9-2-15 1 3 1 6 3 9 1 0 1 1 2 1h3c0-2 0-4-1-6 0-2-2-5-4-6h-2c-2 2-4 4-5 7l-2 8c-1 2-2 5-3 7v-1s-1-1-1-2c0 1 0 2-1 2h0-2l-4-12h0c1-2 2-3 3-5h1c1-1 1-2 2-4-1 1-1 1-2 1v1-3h-2c-2 2-4 4-5 6 0-2 1-3 0-5l1-1c1-2 0-2-1-3v-2l-1-6c0-2-1-2-2-3h-1c0-1 1-1 2-2-1 0-2-1-2-1l-1-1h-3c0-3 0-4-2-7h-1-1c-1-1 0-3 1-5v-1h-1c0-3 1-6 1-9l3-9c1-3 3-5 5-8h0l3-3v1h1c1 1 2 1 3 1h1 0v-2h-2l-3-3c-2-1-5-2-7-4v-2h-1l-3-1c-1-2-3-4-5-6l-6-7-1 1v1 1h-1c-1-1-1-3-2-4h0c0-2 0-3-1-4s-1-2-1-3l-1-4c-1-1-1-5-3-6l1 2c-1 0-1-1-1-1l-1 2c0 1-1 4 0 6 0 2-1 4-2 6l2 1h-10l-1 1c0 2-1 5 0 7 0 2 0 5 3 7h0c-1 0-2 1-4 1-2-2-3-4-5-6l-4-8h-2c-1-3-2-6-1-9-1-2 0-3 0-5l1-4 3-12c3-14 2-29-1-42-1 1-3 2-4 3l-3 3c-1-1-1-3-2-4s-2-2-2-4 0-4-1-5-3-2-4-2c-1-1-2 0-2 1-1-1-1-1-1-2l-1 2v-8c0-6-1-18-4-22l-6 4c-6 3-9 8-11 15v6h0-2c-2-1-4-2-5-4-1-7-5-12-4-20 1-2 2-5 2-8 1-1 1-2 0-3l-1 1h-1c-1-2 0-4 0-5-2-1-4 0-6-1l-1-1c0-1 0-1-1-2h-3c-2 1-2 2-4 3h0c-1-3-3-5-5-8l-5 2c-8 3-17 3-25-1v-1l2 1c10 3 19 1 28-4-1 0-1 0-2 1h-1l3-3h0s1 0 1-1h0c-1 1-3 0-4 1h-7-1-2c3-2 6-4 8-6 3-3 4-6 5-10 1-3 1-6 0-8-2-9-10-16-17-21-2 2-4 5-5 8l-1 4c0 3 0 5 1 7 0 2 0 3 1 4v2l1 1-1 1-13-9c-7-5-10-12-11-20-1-6 0-12 4-17l1-1z" class="B"></path><path d="M143 340c1-2 3-3 4-4 0 2-1 3-2 5v-2s-1 1-2 1z" class="F"></path><path d="M164 358l1-1v3 1h-2c1-1 1-2 1-3z" class="Q"></path><path d="M164 355c-2-1-2-2-3-2l-1-1h1 1c1 1 3 2 3 3l-1-1v1z" class="F"></path><path d="M164 355v-1l1 1c1 2 1 4 0 5v-3l-1 1v-3z" class="J"></path><path d="M143 346h0c-1 3-2 4-4 6v-2l1-1v-1c1-1 2-2 3-2z" class="Q"></path><path d="M143 340c1 0 2-1 2-1v2c-1 1-1 2-1 3s0 2-1 2h0-1c0-2 1-2 1-4h0c-1 0-2 1-3 2l1-2h1c0-1 1-2 1-2z" class="V"></path><path d="M280 526h5c0 1 1 3 0 4 0-1-1-2-2-2v3l-3-5z" class="F"></path><path d="M277 502l1-4v14-2l-2-1c0-1 0-2 1-3v-4z" class="M"></path><path d="M277 506v1l1 3-2-1c0-1 0-2 1-3z" class="E"></path><path d="M140 344c1-1 2-2 3-2h0c0 2-1 2-1 4h1c-1 0-2 1-3 2v1l-1 1h-1l3-5h-1l-1 1h-1c1-1 1-1 1-2h1z" class="M"></path><path d="M205 443c1 1 3 2 3 3l1 1v1h1v-2l1-1v-2 5h0-2c-1-1-1-1-2-1h0c-1-1-2-1-3-1v-1-2h1z" class="V"></path><defs><linearGradient id="BO" x1="276.579" y1="510.762" x2="276.089" y2="516.626" xlink:href="#B"><stop offset="0" stop-color="#494848"></stop><stop offset="1" stop-color="#606060"></stop></linearGradient></defs><path fill="url(#BO)" d="M276 509l2 1v2 3c-1 0-1 4-1 5v-2h-2v-1-2c1-2 1-4 1-6z"></path><path d="M260 470c1 1 3 1 3 2h-1c-1 0-1 0-1 1s0 1 1 0l1 1c-2 0-3 0-3 1-2-1-3-2-5-3l1-1 2 1 2-2z" class="f"></path><path d="M163 361h2c3 1 5 1 7 2 0 1 0 2-1 2h0l-1-1c-2-2-6 0-9-1l2-2z" class="X"></path><path d="M170 364l1 1h0l1 1h0c2 0 5 3 6 4v1c-1 0-2-1-4-2-1 0-4 0-4-1v-4z" class="O"></path><path d="M217 457c0-1 1-4 1-5h0c1-1 1-3 1-5 1 0 2-1 3-1l1 4v2l-4-3c0 1-1 4 0 5l-1 1-1 2z" class="F"></path><path d="M125 342v1c1 2-1 5-2 7h1 1c1 0 1-1 2-1h1 0c1-1 1-1 2-1-2 2-6 5-8 4h-2c3-3 4-6 5-10z" class="Q"></path><defs><linearGradient id="BP" x1="276.351" y1="504.291" x2="273.887" y2="513.854" xlink:href="#B"><stop offset="0" stop-color="#0e0e0f"></stop><stop offset="1" stop-color="#3b3b3b"></stop></linearGradient></defs><path fill="url(#BP)" d="M275 501h0c0 1 1 2 1 3 0-1 0-1 1-2h0v4c-1 1-1 2-1 3 0 2 0 4-1 6-1 0-1-1-1-2l1-6v-6z"></path><path d="M249 563v-2l1-1c1 1 1 4 2 6 0 1-1 3-1 4 1 2 1 3 1 4h-1l-1-6c0-2-1-2-2-3h-1c0-1 1-1 2-2z" class="M"></path><path d="M213 449s2 2 3 2c1 1 1 3 1 4h0c0 2 0 3-1 5v1-1-4-1h0c-1-1-2-1-3-1-1-2 0-4 0-5z" class="f"></path><path d="M258 496h0c0 3 0 5 1 7 0 2-1 7 0 8 1-1 1 0 1-1s1-1 1-1h2c0-1 0-2 1-3v-2c0-1 0-1 1-2 0 3-1 6-1 8-2 0-2-1-3 0l-2 3c-1-5-1-11-1-17z" class="Q"></path><path d="M240 477l2 1v1c2 1 4 2 5 4 1 1 2 4 3 6h0l-1-1c-3-4-6-6-9-9h-1c1-1 1-1 1-2z" class="S"></path><path d="M178 370l2 2c2 3 2 5 2 8 0 2 0 3 1 4h-1l-1-1c0 1 0 2-1 3 0-5 2-9-2-15v-1z" class="a"></path><path d="M263 587h0c1-3 2-4 4-6 0 1-1 2-2 4h0c0 1-1 3 0 3 0 0 1 0 2-1h0l-2 8c0-1-1-1-1-2v-1h-1c0-1 0-3 1-4l-1-1z" class="f"></path><path d="M99 325c0-1-1-1-1-1l-1-1v-1h-1l-1-1-2-2h-1v-1c2 0 5 4 7 4l1-1c1-1 2 0 3 0l-1 4h-3zm171 172c2 0 4 1 5 2v2 6-3h0-1l-3-1c0-1 0-2-1-3v-1-2z" class="V"></path><path d="M211 443v-3c2 2 1 5 1 7v2h0c-1 1-1 4-1 5-3 0-5-1-8-2 0 1 0 1-1 2v-3c1 0 3 0 4 1 0 1 1 0 1 1 1-1 1-1 2-1l1-1c1 0 1-1 1-2v-1h0v-5z" class="S"></path><path d="M264 477c2 1 4 2 5 3-1 0-4 0-5 1h0c2 1 5 2 7 4-3-1-8-2-10-4h0l-1-1v-1c2 0 3-1 4-2z" class="f"></path><path d="M239 459h1c1 0 1-1 2-2 0 0 1 0 1 1v-1l1-1h2c0 1 1 2 2 2h2c0-1-1-2 0-3 1 1 1 4 1 5h-1v1c-1-1-2-1-3-2-1 0-2-1-2-2h-1l3 3h-1 0l-1-1v1l1 1c-1 0-1-1-2-1-2 0-2 2-3 3-1-1-1-3-2-4z" class="Q"></path><path d="M180 386c1-1 1-2 1-3l1 1c0 5 0 11 1 16 1 3 3 6 3 8h0c-5-6-6-14-6-22z" class="W"></path><path d="M253 557c1-1 0-1 1-2l1 1v2h2l-1 1h1c0 1 0 1 1 2h0c2 0 2 0 3-1 0 2-1 3-3 4-1-1-1-1-2-1v1l-2 2-1 2v-1-10z" class="h"></path><path d="M253 557h1c2 3-1 6 0 9l-1 2v-1-10z" class="S"></path><defs><linearGradient id="BQ" x1="227.155" y1="447.431" x2="226.354" y2="439.367" xlink:href="#B"><stop offset="0" stop-color="#717271"></stop><stop offset="1" stop-color="#898788"></stop></linearGradient></defs><path fill="url(#BQ)" d="M233 448c-3-2-5-4-8-6-2 0-4 0-5-1v-1c1-1 3-1 4-1 2 1 1 2 2 1l1-1h1c3 3 4 4 6 8l-1 1z"></path><path d="M224 430l1-1h6v1h-3v1h1l1 1c2 0 2-1 3 0l1 1c1 1 2 1 3 1 0 1-1 1-1 1h-1c-1 1-4 1-6 1 1-1 1-1 0-1-2-1-4-1-6-2h1v-1h3l-3-2z" class="Q"></path><path d="M227 432c1 0 2 1 3 2 2 0 4 0 5 1-1 1-4 1-6 1 1-1 1-1 0-1-2-1-4-1-6-2h1v-1h3z" class="L"></path><defs><linearGradient id="BR" x1="265.581" y1="559.209" x2="261.299" y2="564.521" xlink:href="#B"><stop offset="0" stop-color="#151415"></stop><stop offset="1" stop-color="#313131"></stop></linearGradient></defs><path fill="url(#BR)" d="M258 561l1-1v-1h1l-1-1c2 0 3-1 4-1s2 0 3 1h1l2-1c1 1 2 1 2 1l2 2-1 1-1-1h0c-1 0-1 0-2 1h-3 0l-1 2-1 2h-1-2v-1l1-1c1-1 2-3 2-4h-2l-1 1c-1 1-1 1-3 1h0z"></path><defs><linearGradient id="BS" x1="128.554" y1="352.428" x2="130.391" y2="357.831" xlink:href="#B"><stop offset="0" stop-color="#484847"></stop><stop offset="1" stop-color="#6e6e70"></stop></linearGradient></defs><path fill="url(#BS)" d="M139 346l1-1h1l-3 5h1v2l-2 2h-2c-3 2-7 6-10 6-1 0-1 0-2 1h-1l3-3h1s1 0 1-1c0 0 0-1 1-1s2-1 2-2c1 0 1 0 2-1 2-2 5-4 7-7z"></path><path d="M138 350h1v2l-2 2h-2l3-4z" class="F"></path><path d="M263 587l1 1c-1 1-1 3-1 4h1v1c0 1 1 1 1 2-1 2-2 5-3 7v-1s-1-1-1-2c0 1 0 2-1 2h0l-1-5c1-1 1-2 1-4h1l2-5z" class="a"></path><path d="M261 592v5 2c0 1 0 2-1 2h0l-1-5c1-1 1-2 1-4h1z" class="U"></path><defs><linearGradient id="BT" x1="205.415" y1="441.994" x2="201.893" y2="450.185" xlink:href="#B"><stop offset="0" stop-color="#110f12"></stop><stop offset="1" stop-color="#414241"></stop></linearGradient></defs><path fill="url(#BT)" d="M202 451c0-3 0-9 1-11h1c0 1 1 2 1 3h-1v2 1c1 0 2 0 3 1h0c1 0 1 0 2 1h2v1c0 1 0 2-1 2l-1 1c-1 0-1 0-2 1 0-1-1 0-1-1-1-1-3-1-4-1z"></path><path d="M204 446c1 0 2 0 3 1h0c1 0 1 0 2 1h2v1c-1 1-2 1-3 2h0-1c0-2-2-3-3-5h0z" class="M"></path><defs><linearGradient id="BU" x1="262.99" y1="559.624" x2="257.734" y2="567.627" xlink:href="#B"><stop offset="0" stop-color="#2f2e2e"></stop><stop offset="1" stop-color="#494949"></stop></linearGradient></defs><path fill="url(#BU)" d="M261 560l1-1h2c0 1-1 3-2 4l-1 1v1h2l-1 1-1 1-3 2-1 1-4 2 1 1h-1v-5l1-2 2-2v-1c1 0 1 0 2 1 2-1 3-2 3-4z"></path><path d="M262 566h-2v-1l1-1v1h2l-1 1z" class="E"></path><path d="M256 564h1l1 1-1 4h1l-1 1-4 2 1 1h-1v-5l1-2 2-2z" class="O"></path><path d="M253 572v-1c0-1 0-2 1-3h2s0 1 1 1v1l-4 2z" class="T"></path><defs><linearGradient id="BV" x1="209.245" y1="424.446" x2="222.997" y2="437.71" xlink:href="#B"><stop offset="0" stop-color="#959393"></stop><stop offset="1" stop-color="#c6c5c8"></stop></linearGradient></defs><path fill="url(#BV)" d="M202 425h1l3 3 2-1c5 3 9 5 15 6 2 1 4 1 6 2 1 0 1 0 0 1-10 0-20-3-27-11z"></path><defs><linearGradient id="BW" x1="274.192" y1="506.229" x2="269.635" y2="512.427" xlink:href="#B"><stop offset="0" stop-color="#2b2a2b"></stop><stop offset="1" stop-color="#505050"></stop></linearGradient></defs><path fill="url(#BW)" d="M270 499v1c1 1 1 2 1 3l3 1h1 0v3l-1 6c0 1 0 2 1 2v2 1h-1c-1 1-1 2-3 2 0-1 0-2-1-3h0c-1-2 0-4 0-5v-13z"></path><path d="M274 513c0 1 0 2 1 2v2l-1-1-1-1s0-1 1-2z" class="U"></path><path d="M270 512c0 1 1 2 1 3h1v2h1 0l1-1 1 1v1h-1c-1 1-1 2-3 2 0-1 0-2-1-3h0c-1-2 0-4 0-5z" class="O"></path><path d="M256 500l1-11 1 7c0 6 0 12 1 17 0 3 1 6 0 8v-3 1h-1v-1c0 1-1 1-1 1v1l-1 1v-2h-2l1-1c1-2 1-4 0-6v-2-2-2l-1-2c1 0 1 0 2-1v-3z" class="L"></path><path d="M256 500v6h-1l-1-2c1 0 1 0 2-1v-3z" class="V"></path><path d="M256 506l1 4v1h-1l-1-1v-2-2h1z" class="J"></path><path d="M255 510l1 1h1v-1c0 3 1 7 0 10l-1 1v-2h-2l1-1c1-2 1-4 0-6v-2z" class="Q"></path><path d="M161 376c1-1 3-4 5-5-2 4-5 7-7 11-4 6-8 15-5 23l1 2v1h1v-3 6h0c-1-1-2-3-3-4-1-2-2-4-3-5 1-2 1-6 2-8 1-7 5-13 9-18z" class="O"></path><path d="M177 408l1 3h5c2 1 6 7 7 10 1 1 1 5 3 6l-3 3c-1-1-1-3-2-4s-2-2-2-4 0-4-1-5-3-2-4-2c-1-1-2 0-2 1-1-1-1-1-1-2l-1 2v-8z" class="E"></path><defs><linearGradient id="BX" x1="189.978" y1="381.935" x2="181.508" y2="389.235" xlink:href="#B"><stop offset="0" stop-color="#2d2e2d"></stop><stop offset="1" stop-color="#4c4c4c"></stop></linearGradient></defs><path fill="url(#BX)" d="M180 372l2 1 1 5c2 1 4 2 5 1s1-1 1-2v-1c1 0 1 0 1-1v-1l1-1h1v2h-1c-1 1-1 2-1 3-2 7-3 13-2 21l-1-1c-1-2-1-3-2-5v-5c-1-3-1-3-2-4s-1-2-1-4c0-3 0-5-2-8z"></path><path d="M241 463c1-1 1-3 3-3 1 0 1 1 2 1l-1-1v-1l1 1h0 1l-3-3h1c0 1 1 2 2 2 1 1 2 1 3 2v-1h1v1l1 2c2 3 4 5 8 7l-2 2-2-1-1 1h0l-4-2-2-2c-1-1-1-2-2-3 2 0 3 1 4 1-1-1-2-2-4-2 0-1-1-1-2-2-2-1-1 1-3 2l-1-1z" class="U"></path><path d="M251 461l1 2h-1c-1 0-1 0-1-1l1-1z" class="S"></path><path d="M249 468c-1-1-1-2-2-3 2 0 3 1 4 1 1 1 3 2 4 3v1c-1 0-2 0-3-1l-1 1-2-2z" class="e"></path><path d="M161 376c-4 5-8 11-9 18-1 2-1 6-2 8-1-2-1-4-3-5v-2h0c-1-1 0-2-1-4v-2c1-1 1-1 1-2h1l1-1c1 0 3-1 4-2 1 0 2 0 2-1 1-1 2-2 2-3 2-2 2-3 4-4z" class="h"></path><defs><linearGradient id="BY" x1="266.168" y1="584.491" x2="253.642" y2="594.056" xlink:href="#B"><stop offset="0" stop-color="#69696a"></stop><stop offset="1" stop-color="#939292"></stop></linearGradient></defs><path fill="url(#BY)" d="M260 580h0c0-1 0-2 1-2h1l1 1h1l-3 7c0 2-1 4-1 6h0c0 2 0 3-1 4l1 5h-2l-4-12h0c1-2 2-3 3-5h1c1-1 1-2 2-4z"></path><path d="M259 596l-2-5 4-5c0 2-1 4-1 6h0c0 2 0 3-1 4z" class="R"></path><path d="M223 452h3c0 1 2 3 2 4 4 5 6 10 9 16l-2-2c-1 0-1 1-2 1-1-1-2-3-4-4l-3-6c0-1-1-3-2-3h-1v-3-3h0z" class="F"></path><path d="M229 467c2 1 3 3 4 4 1 0 1-1 2-1l2 2c2 2 4 4 5 6l-2-1c0 1 0 1-1 2h1c3 3 6 5 9 9l-1 1c-1 0-2-1-3-1l-4-4h0-1c0-2-3-3-4-5 0-1-1-1-2-1 0-1 0-1-1-1l1-1-2-2v1s0 1 1 1v3h0l-1-2c0-2-2-5-3-7l-1-3h0 1z" class="J"></path><path d="M229 467c2 1 3 3 4 4 1 0 1-1 2-1l2 2c2 2 4 4 5 6l-2-1c0 1 0 1-1 2-4-4-8-6-11-12h1z" class="O"></path><path d="M233 471c1 0 1-1 2-1l2 2c2 2 4 4 5 6l-2-1-7-6z" class="V"></path><path d="M288 558v-1c0-1-2-2-2-4 0-1-1-2-1-3-1-2-1-4-1-5-1-3-3-6-3-9-1-2-1-5-1-7v1c2 5 3 9 6 13h1c0 1 0 2 1 3 1 3 1 6 1 9v4c1 2 1 6 0 8l-6 8 4 4h3 0c-1 1-2 1-3 1l-1-1h0c-1 0-2-1-3-2v-4c1-1 1-1 1-2 1-3 2-6 5-9 0-2-1-3-1-4h0z" class="M"></path><path d="M289 555c-2-3-3-6-4-10l1 1h0v-2c1 1 0 1 2 2 1 3 1 6 1 9z" class="F"></path><defs><linearGradient id="BZ" x1="125.641" y1="347.708" x2="126.297" y2="357.078" xlink:href="#B"><stop offset="0" stop-color="#2c2c2d"></stop><stop offset="1" stop-color="#626161"></stop></linearGradient></defs><path fill="url(#BZ)" d="M138 346h1c-2 3-5 5-7 7-1 1-1 1-2 1 0 1-1 2-2 2s-1 1-1 1c0 1-1 1-1 1h-1 0s1 0 1-1h0c-1 1-3 0-4 1h-7-1-2c3-2 6-4 8-6h2c2 1 6-2 8-4 1 0 1-1 2-1 2-1 4 1 6-1z"></path><path d="M277 520c0-1 0-5 1-5 0 1-1 5 0 6 0 2 1 3 2 4v1l3 5 4 12h-1c-3-4-4-8-6-13v-1c0 2 0 5 1 7 0 3 2 6 3 9 0 1 0 3 1 5 0 1 1 2 1 3 0 2 2 3 2 4v1l-3-3c0-3-3-3-4-5-1-4-2-9-3-13 1-2 1-4 1-6-1-3-2-6-2-9v-2z" class="L"></path><defs><linearGradient id="Ba" x1="267.051" y1="494.72" x2="268.338" y2="513.972" xlink:href="#B"><stop offset="0" stop-color="#1a191a"></stop><stop offset="1" stop-color="#6d6d6e"></stop></linearGradient></defs><path fill="url(#Ba)" d="M265 500l1-9h0v2l2 1h1c1 1 1 1 1 3v2 13c0 1-1 3 0 5h0v3-1-2c-1-2-1-5-1-7h0l-1 4v1l-1 1h-1l-1 2v-1c-2 5-3 9-5 14h-1c2-7 4-14 5-21 0-2 1-5 1-8v-2z"></path><path d="M265 500l1-9h0v2c1 5 2 11 0 15 0-1 1-4 0-5v2l-1-5z" class="c"></path><defs><linearGradient id="Bb" x1="269.583" y1="498.673" x2="268.719" y2="509.311" xlink:href="#B"><stop offset="0" stop-color="#434243"></stop><stop offset="1" stop-color="#696868"></stop></linearGradient></defs><path fill="url(#Bb)" d="M268 514c0-4 0-8 1-13v-4c1 1 1 1 1 2v13c0 1-1 3 0 5h0v3-1-2c-1-2-1-5-1-7h0l-1 4z"></path><defs><linearGradient id="Bc" x1="260.158" y1="516.679" x2="264.726" y2="518.673" xlink:href="#B"><stop offset="0" stop-color="#8a8a89"></stop><stop offset="1" stop-color="#bcbbbc"></stop></linearGradient></defs><path fill="url(#Bc)" d="M265 500l1 5v-2c1 1 0 4 0 5l-1 9c-2 5-3 9-5 14h-1c2-7 4-14 5-21 0-2 1-5 1-8v-2z"></path><defs><linearGradient id="Bd" x1="246.095" y1="463.332" x2="242.085" y2="468.678" xlink:href="#B"><stop offset="0" stop-color="#626161"></stop><stop offset="1" stop-color="#919090"></stop></linearGradient></defs><path fill="url(#Bd)" d="M238 462v-3h0c0-1 0-1 1-1v-1-4 6c1 1 1 3 2 4l1 1c2-1 1-3 3-2 1 1 2 1 2 2 2 0 3 1 4 2-1 0-2-1-4-1 1 1 1 2 2 3l2 2 4 2h0c2 1 3 2 5 3h0l1 1 3 1c-1 1-2 2-4 2v1l1 1h0c-9-3-19-10-23-19z"></path><path d="M247 470l2 1v1l-2-2z" class="K"></path><path d="M247 470c0-1-3-4-3-5 1 1 3 2 4 3h1l2 2 4 2c0 1 0 1-1 1s-3-1-5-2l-2-1z" class="d"></path><defs><linearGradient id="Be" x1="255.552" y1="476.629" x2="251.607" y2="472.538" xlink:href="#B"><stop offset="0" stop-color="#4f4e4f"></stop><stop offset="1" stop-color="#6f6e70"></stop></linearGradient></defs><path fill="url(#Be)" d="M249 471c2 1 4 2 5 2s1 0 1-1h0c2 1 3 2 5 3h0l1 1 3 1c-1 1-2 2-4 2v1l-2-1-2-1c-3-1-5-3-7-6v-1z"></path><path d="M260 475l1 1 3 1c-1 1-2 2-4 2v1l-2-1-2-1 1-1c1 0 1 0 2-1v-1l1 1v-1z" class="U"></path><path d="M261 476l3 1c-1 1-2 2-4 2v1l-2-1v-1h3s1 0 1-1l-1-1z" class="E"></path><path d="M227 439l24-2-6 4c-2 1-3 2-4 4s-2 6-2 8v4 1c-1 0-1 0-1 1h0v3c-1-2-1-5-2-7v-4l-1-1-2-2 1-1c-2-4-3-5-6-8h-1l-1 1c-1 1 0 0-2-1h1 2z" class="L"></path><path d="M239 439c1-1 1-1 2-1h0v2c-1 0-2-1-2-1z" class="R"></path><path d="M239 439s1 1 2 1l-2 1-2 2-1-2c1-1 2-1 3-2z" class="e"></path><path d="M235 448c0-1 0-5 1-7h0l1 2 2-2 1 1c-3 4-3 8-4 13v-4l-1-1v-2z" class="S"></path><path d="M227 439h5c1 1 2 1 2 3v3c0 1 0 2 1 3v2l-2-2 1-1c-2-4-3-5-6-8h-1l-1 1c-1 1 0 0-2-1h1 2zm13 3c0-1 1-2 2-2 1-1 3-1 4-1-1 0-1 1-1 2-2 1-3 2-4 4s-2 6-2 8v4 1c-1 0-1 0-1 1h0v3c-1-2-1-5-2-7 1-5 1-9 4-13z" class="O"></path><defs><linearGradient id="Bf" x1="181.192" y1="405.156" x2="207.697" y2="406.361" xlink:href="#B"><stop offset="0" stop-color="#555556"></stop><stop offset="1" stop-color="#929091"></stop></linearGradient></defs><path fill="url(#Bf)" d="M183 384c1 1 1 1 2 4v5c1 2 1 3 2 5l1 1c1 3 2 6 4 9h1v1c3 7 9 14 15 18l-2 1-3-3h-1c-4-2-6-6-9-9-2-3-5-5-7-8h0c0-2-2-5-3-8-1-5-1-11-1-16h1z"></path><defs><linearGradient id="Bg" x1="236.703" y1="472.929" x2="243.301" y2="500.316" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#515152"></stop></linearGradient></defs><path fill="url(#Bg)" d="M233 479h0v-3c-1 0-1-1-1-1v-1l2 2-1 1c1 0 1 0 1 1 1 0 2 0 2 1 1 2 4 3 4 5h1 0l4 4c1 0 2 1 3 1l1-1 1 1h0c1 1 1 3 1 5l3 8v2l1 2v2 2 2l-1-1c-1-2-2-3-3-4h0l-6-6c-4-4-6-10-9-15-1-2-2-4-3-7z"></path><path d="M241 484h0l4 4c1 0 2 1 3 1l1-1 1 1h0c1 1 1 3 1 5-1 0-1-1-2-2h0c-1 0-1 0-2-1h-1c-1 0-1-1-2-1s-2-1-3-1h0 1v-1c0-1-1-3-1-4z" class="Q"></path><path d="M241 484h0l4 4v-1c-1 0-2-1-3-2v1 2c0-1-1-3-1-4z" class="M"></path><defs><linearGradient id="Bh" x1="246.163" y1="495.725" x2="250.745" y2="503.525" xlink:href="#B"><stop offset="0" stop-color="#4d4c4d"></stop><stop offset="1" stop-color="#777676"></stop></linearGradient></defs><path fill="url(#Bh)" d="M248 495c-1-1-1-1-2-1h0l1-1-1-1h1 1 1c1 1 1 2 2 2l3 8v2l1 2v2 2 2l-1-1c-1-2-2-3-3-4h0v-1c0-2-2-3-3-5l-3-3v-1c0-1 1-1 2-1l1-1z"></path><path d="M251 507c1 0 1-2 1-2l3 3v2 2l-1-1c-1-2-2-3-3-4z" class="W"></path><path d="M248 495c-1-1-1-1-2-1h0l1-1-1-1h1 1 1c1 1 1 2 2 2l3 8c-2-3-4-5-6-7z" class="U"></path><path d="M272 561c-2 3-5 5-8 7h0 2v-1h0c2 0 3-3 4-3h1c1 0 0-1 1-1 2 0 0 1 1 0l7-1c2 0 4-2 5-3-1 2-4 6-6 7h-1l-7 5c-2 3-5 5-7 8h-1l-1-1h-1c-1 0-1 1-1 2h0c-1 1-1 1-2 1v1-3h-2c-2 2-4 4-5 6 0-2 1-3 0-5l1-1c1-2 0-2-1-3v-2h1 0v-1c1-1 0-4 1-6v1 5h1l-1-1 4-2 1-1 3-2 1-1 1-1h1l1-2 1-2h0 3c1-1 1-1 2-1h0l1 1z" class="Q"></path><path d="M264 565c1-1 3-2 5-2l-2 2-4 3h-1c0-1 0-1-1-1l1-1 1-1h1z" class="G"></path><path d="M256 579h0c1-2 3-2 5-3l1 1v1h-1c-1 0-1 1-1 2h0c-1 1-1 1-2 1v1-3h-2z" class="a"></path><path d="M252 574h0v-1c1-1 0-4 1-6v1 5h1l2-1v1h6l-1 1c-3 0-6 3-9 1v-1z" class="F"></path><path d="M266 572c2-3 8-5 12-7v1l-7 5v-1c-2 1-3 2-5 2z" class="H"></path><path d="M266 572c2 0 3-1 5-2v1c-2 3-5 5-7 8h-1c1-2 1-4 1-5s1-1 1-1l1-1z" class="P"></path><path d="M261 567c1 0 1 0 1 1h1c1 1 0 3 0 5v2 1c-1-1-1-2-1-3h-6v-1l-2 1-1-1 4-2 1-1 3-2z" class="B"></path><path d="M261 567c1 0 1 0 1 1l-6 4-2 1-1-1 4-2 1-1 3-2z" class="H"></path><path d="M259 513l2-3c1-1 1 0 3 0-1 7-3 14-5 21-2 3-3 6-6 9l-3 5-4 3-6 6h-1c-1-1 0-3 1-5v-1h-1c0-3 1-6 1-9l3-9c1-3 3-5 5-8h0l3-3v1h1c1 1 2 1 3 1h1 0l1-1v-1s1 0 1-1v1h1v-1 3c1-2 0-5 0-8z" class="c"></path><path d="M257 520v-1s1 0 1-1v1h1v-1 3 4l-1-3h0c-1 0-2 1-2 2h-1 0c-1 0-2 0-2 1l-1 1c0-1 0-1-1-2h-1c2-1 2-1 3-1s2-1 3-2h0l1-1z" class="T"></path><path d="M248 522h0l3-3v1h1c1 1 2 1 3 1h1c-1 1-2 2-3 2s-1 0-3 1c0 0-1 1-2 1v1c0-1 0-2 1-3l-1-1z" class="g"></path><path d="M240 548c1-2 3-4 4-6l4-8c0-2 1-3 2-4h0c-2 4-3 8-4 12v2l2-1c-1 1-2 3-2 4v1l-6 6h-1c-1-1 0-3 1-5v-1z" class="i"></path><path d="M240 549v3 1h0c1-1 0-2 1-3l3-3c1-2 1-4 2-5v2l2-1c-1 1-2 3-2 4v1l-6 6h-1c-1-1 0-3 1-5z" class="K"></path><path d="M250 530c1 0 2-3 3-4 1 1 0 2 0 3 2-2 2-4 4-6h1v1h0v3c-2 5-4 9-7 13l-3 3-2 1v-2c1-4 2-8 4-12z" class="W"></path><defs><linearGradient id="Bi" x1="252.039" y1="511.421" x2="257.179" y2="544.774" xlink:href="#B"><stop offset="0" stop-color="#393738"></stop><stop offset="1" stop-color="#757474"></stop></linearGradient></defs><path fill="url(#Bi)" d="M259 513l2-3c1-1 1 0 3 0-1 7-3 14-5 21-2 3-3 6-6 9l-3 5-4 3v-1c0-1 1-3 2-4l3-3c3-4 5-8 7-13l1-2v-4c1-2 0-5 0-8z"></path><path d="M281 479c2 4 4 8 5 12 1 3 2 5 2 8 0 0 1 1 1 2s-1 2 0 3l2 2 2 10 3 12 2 11c2 9 3 18 3 27-1 11 0 21-3 32 0 2-1 4-1 5-1 2-3 3-3 5 0 0-1 0-1 1-1-1-1-2-2-2v-1c1-2 2-4 3-7 1-7 2-14 2-22 1-14 0-27-2-41-3-19-10-36-16-54 2 1 2 3 3 5l3 9 1 1c0 3 1 5 2 7h1v-2c-1-1 0-4-1-5 0-1 0-2-1-2v-2l-1-1v-2c-1-1-1-1-1-2v-1h-1l-1-3v-2c-1-1-1-1-1-3z" class="Z"></path><path d="M268 514l1-4h0c0 2 0 5 1 7v2 1-3c1 1 1 2 1 3 2 0 2-1 3-2h1 2v2 2c0 3 1 6 2 9 0 2 0 4-1 6 1 4 2 9 3 13 1 2 4 2 4 5l-6-4c-2-1-5-3-7-4-1-1-3-4-5-4-4 3-10 5-16 6-3 1-7 2-10 4v1h-1l6-6 4-3 3-5c3-3 4-6 6-9h1c2-5 3-9 5-14v1l1-2h1l1-1v-1z" class="G"></path><path d="M258 537v1c0 2-2 5-3 7h-1l1-2c1-1 1-2 2-3h-1l-3 5v-1l2-3c1-1 2-3 3-4zm11-18h0v1c-1 1-1 3-2 5l-1 4s0 1-1 1v-1h0c1-3 2-6 2-8h-2c1 0 1-1 1-1h2l1-1z" class="K"></path><path d="M259 531h1l-2 6c-1 1-2 3-3 4 0 0-1 0-2-1 3-3 4-6 6-9z" class="D"></path><path d="M267 542c2 1 3 1 4 2 3 2 6 4 8 6v1c-2-1-5-3-7-4-1-1-3-4-5-4v-1z" class="d"></path><path d="M251 547v1c1-1 2-1 3-1 3-1 5-2 7-3s4-2 6-2v1c-4 3-10 5-16 6h0v-1-1z" class="C"></path><defs><linearGradient id="Bj" x1="267.261" y1="525.154" x2="275.68" y2="530.631" xlink:href="#B"><stop offset="0" stop-color="#69676c"></stop><stop offset="1" stop-color="#81817d"></stop></linearGradient></defs><path fill="url(#Bj)" d="M270 517c1 1 1 2 1 3 2 0 2-1 3-2-2 3-2 4-1 7h0v14c-1-3-1-5-1-8 0-1-1-2-1-4 0 0 0-1-1-1v-1h0l-1-1c0-1 0-2 1-3v-1-3z"></path><path d="M270 521c0 1 1 2 1 3 0 0-1 1-1 2v-1h0l-1-1c0-1 0-2 1-3z" class="K"></path><path d="M253 540c1 1 2 1 2 1l-2 3-2 3h0v1 1h0c-3 1-7 2-10 4v1h-1l6-6 4-3 3-5z" class="b"></path><path d="M253 540c1 1 2 1 2 1l-2 3-2 3c0-1 1-2 1-3l-2 1 3-5z" class="I"></path><defs><linearGradient id="Bk" x1="279.316" y1="521.17" x2="272.535" y2="527.319" xlink:href="#B"><stop offset="0" stop-color="#6a6869"></stop><stop offset="1" stop-color="#858586"></stop></linearGradient></defs><path fill="url(#Bk)" d="M275 518h2v2 2c0 3 1 6 2 9 0 2 0 4-1 6 0-1-1-2-2-3v-2c-1-1-1-1-1-2v-1c-1-2-1-4-1-6 0-1 0-1-1-2v4h0c-1-3-1-4 1-7h1z"></path><path d="M191 373c0-1 0-1 1-2 1 1 1 4 1 5 0-1 0-2 1-2s1 0 2 1v-1 2l1 1h1l1 1v2c-1 1-1 2-1 4v8l1 4c2 5 4 10 7 15 5 4 10 7 16 10 3 1 6 2 9 4-4 0-9-2-12-3h-1 0c4 4 7 5 12 6h6v1h-5-6l-1 1 3 2h-3v1h-1c-6-1-10-3-15-6-6-4-12-11-15-18v-1h-1c-2-3-3-6-4-9-1-8 0-14 2-21 0-1 0-2 1-3h1v-2h-1z" class="e"></path><path d="M194 390c0-3 0-6 1-8v5l1-1v1h0v7 1h0v-6l-1-1v5l-1-3z" class="E"></path><path d="M217 430v-1l-1-1c2 0 8 2 8 2l3 2h-3l-7-2z" class="K"></path><path d="M217 426c-1 0-1 0-2-1h-1c-3-2-4-6-7-9h0c0 1 1 2 2 2h1s1 0 1 1v-2c1 1 3 2 4 3l-1 1c0 1 2 4 3 5z" class="i"></path><path d="M197 396l1-1s1 0 1 1c2 5 4 10 7 15h0c-5-3-7-10-9-15z" class="W"></path><path d="M217 426c-1-1-3-4-3-5l1-1 3 2h0c4 4 7 5 12 6v1c-2 0-5 0-8-1-1-1-3-1-4-2h-1z" class="C"></path><path d="M193 409c1 0 1 1 2 1 4 8 10 15 18 19l4 1 7 2v1h-1c-6-1-10-3-15-6-6-4-12-11-15-18z" class="f"></path><defs><linearGradient id="Bl" x1="194.662" y1="376.374" x2="193.173" y2="381.949" xlink:href="#B"><stop offset="0" stop-color="#212021"></stop><stop offset="1" stop-color="#404041"></stop></linearGradient></defs><path fill="url(#Bl)" d="M191 373c0-1 0-1 1-2 1 1 1 4 1 5 0-1 0-2 1-2s1 0 2 1v-1 2c0 2 0 4-1 6s-1 5-1 8c-1-3 0-5 0-8h-1c-1 0-1 1-1 2-1-1-1-1-1-2 1-1 1-1 1-2v-2l-1 3c0 1 0 1-1 2h0l1-5h-1c0-1 0-2 1-3h1v-2h-1z"></path><defs><linearGradient id="Bm" x1="197.746" y1="377.904" x2="194.169" y2="385.335" xlink:href="#B"><stop offset="0" stop-color="#0a0b0b"></stop><stop offset="1" stop-color="#353535"></stop></linearGradient></defs><path fill="url(#Bm)" d="M196 376l1 1h1l1 1v2c-1 1-1 2-1 4v8l1 4c0-1-1-1-1-1l-1 1v-3l-1 1v-7h0v-1l-1 1v-5c1-2 1-4 1-6z"></path><path d="M196 387c1-1 1-2 1-3 1 1 1 5 1 6h0v2l1 4c0-1-1-1-1-1l-1 1v-3l-1 1v-7z" class="i"></path><path d="M197 393c0-1 0-1 1-3v2l1 4c0-1-1-1-1-1l-1 1v-3z" class="c"></path><path d="M190 378h1l-1 5h0c1-1 1-1 1-2l1-3v2c0 1 0 1-1 2 0 1 0 1 1 2v3c0 1 0 0-1 1-1 2 0 7 0 10v2c1 1 1 1 1 2 1 2 3 5 3 8-1 0-1-1-2-1v-1h-1c-2-3-3-6-4-9-1-8 0-14 2-21z" class="g"></path><path d="M190 378h1l-1 5c0 2 0 4-1 6 0 7 1 13 4 19h-1c-2-3-3-6-4-9-1-8 0-14 2-21z" class="G"></path><path d="M219 454c-1-1 0-4 0-5l4 3h0 0v3 3h1c1 0 2 2 2 3l3 6h-1 0l1 3c1 2 3 5 3 7l1 2c1 3 2 5 3 7 3 5 5 11 9 15l6 6h0c1 1 2 2 3 4l1 1c1 2 1 4 0 6l-1 1-3-3c-2-1-5-2-7-4v-2h-1l-3-1c-1-2-3-4-5-6l-6-7-1 1v1 1h-1c-1-1-1-3-2-4h0c0-2 0-3-1-4s-1-2-1-3l-1-4c-1-1-1-5-3-6l1 2c-1 0-1-1-1-1l-1 2c-1-2 0-4-1-6 0-7 0-13 1-20l1-1z" class="Q"></path><path d="M229 480l1 3 2 5v3h0l-1-1c-2-3-2-6-2-10z" class="S"></path><path d="M234 496c2 1 4 5 7 5l2 2c-2 0-2 0-4 1-2-2-3-5-4-7h-1v-1z" class="V"></path><path d="M226 461l3 6h-1 0l1 3h-1c0-1-1-1-1-2v4l-1-5c0-2-1-4-2-5l2-1z" class="X"></path><path d="M232 488c2 3 4 7 7 10l2 3c-3 0-5-4-7-5v-1c-1-1-2-3-2-4v-3z" class="L"></path><path d="M227 472v-4c0 1 1 1 1 2h1c1 2 3 5 3 7-1 1-1 1-3 0v2c-1-2-2-4-2-7z" class="T"></path><path d="M224 491v-2l1-1 1 2c2 4 6 9 10 13h-1l-6-7-1 1v1 1h-1c-1-1-1-3-2-4h0c0-2 0-3-1-4z" class="X"></path><path d="M229 479v-2c2 1 2 1 3 0l1 2c1 3 2 5 3 7 3 5 5 11 9 15h0l2 2v1l-2-1c0 1 0 1-1 1l-1-1-2-2-2-3c-3-3-5-7-7-10l-2-5-1-3v-1z" class="C"></path><path d="M239 498c2 1 3 1 4 2l2 3c0 1 0 1-1 1l-1-1-2-2-2-3z" class="G"></path><path d="M229 479v-2c2 1 2 1 3 0l1 2c1 3 2 5 3 7-1 0-1-1-2-2l-1-1-1-1-2 1-1-3v-1z" class="W"></path><path d="M245 501l6 6h0c1 1 2 2 3 4l1 1c1 2 1 4 0 6l-1 1-3-3c-2-1-5-2-7-4v-2h-1l1-1c-1-1-1-1-2-1-1-1-2-3-3-4 2-1 2-1 4-1l1 1c1 0 1 0 1-1l2 1v-1l-2-2h0z" class="F"></path><path d="M251 511l1-2c1 0 1 1 2 2l1 1c1 2 1 4 0 6l-1 1-3-3h1 1c-1-2-1-3-2-5z" class="Y"></path><defs><linearGradient id="Bn" x1="245.176" y1="504.393" x2="250.507" y2="507.467" xlink:href="#B"><stop offset="0" stop-color="#8f9090"></stop><stop offset="1" stop-color="#adabab"></stop></linearGradient></defs><path fill="url(#Bn)" d="M245 501l6 6h0c1 1 2 2 3 4-1-1-1-2-2-2l-1 2-7-7c1 0 1 0 1-1l2 1v-1l-2-2h0z"></path><path d="M219 454c-1-1 0-4 0-5l4 3h0 0v3 3h1c1 0 2 2 2 3l-2 1c0 1 3 24 4 27 0 1 1 3 2 5-1-1-3-3-4-5v1l-1-2-1 1v2c-1-1-1-2-1-3l-1-4c-1-1-1-5-3-6l1 2c-1 0-1-1-1-1l-1 2c-1-2 0-4-1-6 0-7 0-13 1-20l1-1z" class="T"></path><path d="M222 484c2 1 3 3 4 5h0v1l-1-2-1 1v2c-1-1-1-2-1-3l-1-4z" class="O"></path><path d="M219 454c-1-1 0-4 0-5l4 3h0 0v3c-1 2-1 3-1 4-1 4-1 9 0 12v5c-1-2-2-4-2-6-1 1-1 2-1 3s1 1 1 2l1 2h-1 0l-1 1h0l1 2c-1 0-1-1-1-1l-1 2c-1-2 0-4-1-6 0-7 0-13 1-20l1-1z" class="K"></path><path d="M219 454c-1-1 0-4 0-5l4 3h0 0c-1 1-1 1-1 2-1 1-1 3-2 4v-3l-1-1z" class="U"></path><path d="M212 447l1 2c0 1-1 3 0 5 1 0 2 0 3 1h0v1 4 1l-1 6 1-1s0-1 1-2v-7l1-2c-1 7-1 13-1 20 1 2 0 4 1 6 0 1-1 4 0 6 0 2-1 4-2 6l2 1h-10l-1 1c0 2-1 5 0 7 0 2 0 5 3 7h0c-1 0-2 1-4 1-2-2-3-4-5-6l-4-8h-2c-1-3-2-6-1-9-1-2 0-3 0-5l1-4v2h1c2-3 3-7 4-10 2-5 2-11 2-16 1-1 1-1 1-2 3 1 5 2 8 2 0-1 0-4 1-5h0v-2z" class="D"></path><path d="M194 487h0 0c0 3 2 6 3 9h-2c-1-3-2-6-1-9z" class="M"></path><path d="M201 487v-2c1 2 0 7 1 8 0 4 1 7 2 11l-3-7v-3-7z" class="L"></path><path d="M195 478v2h1c1 2 1 5 1 7l3 6s0 1 1 1v3c-2-3-4-6-5-9-1-2 0-5-2-6l1-4z" class="O"></path><path d="M208 478h2v2c0 1 0 3-1 4v4h0c1 0 1 1 1 1l-1 2c0 1 0 2-1 3l-1 1v-6l1-11z" class="K"></path><path d="M201 487c0-2 0-5 1-7l4-14 1-2h1c-2 6-4 12-5 18-1 3-1 7-1 11-1-1 0-6-1-8v2z" class="O"></path><defs><linearGradient id="Bo" x1="219.498" y1="460.716" x2="205.065" y2="469.951" xlink:href="#B"><stop offset="0" stop-color="#363636"></stop><stop offset="1" stop-color="#6b6a6b"></stop></linearGradient></defs><path fill="url(#Bo)" d="M212 447l1 2c0 1-1 3 0 5 1 0 2 0 3 1h0v1 4c-1 3-1 7-2 10h-2c0 1 1 2 0 2 0 3-1 6-1 8 1 2 0 7-1 9 0 0 0-1-1-1h0v-4c1-1 1-3 1-4v-2h-2c3-9 4-19 4-29v-2z"></path><path d="M217 457l1-2c-1 7-1 13-1 20 1 2 0 4 1 6 0 1-1 4 0 6 0 2-1 4-2 6l2 1h-10c1-1 1-2 1-3l1-2c1-2 2-7 1-9 0-2 1-5 1-8 1 0 0-1 0-2h2c1-3 1-7 2-10v1l-1 6 1-1s0-1 1-2v-7z" class="h"></path><path d="M210 489c1-2 2-7 1-9 0-2 1-5 1-8 1 0 0-1 0-2h2c-1 5 0 10 0 15 0 2 0 4-1 7 1 1 2 1 3 1l2 1h-10c1-1 1-2 1-3l1-2z" class="a"></path><defs><linearGradient id="Bp" x1="207.925" y1="453.503" x2="194.7" y2="489.815" xlink:href="#B"><stop offset="0" stop-color="#4c4c4d"></stop><stop offset="1" stop-color="#959394"></stop></linearGradient></defs><path fill="url(#Bp)" d="M202 454c1-1 1-1 1-2 3 1 5 2 8 2-1 4-2 7-3 10h-1l-1 2-4 14c-1 2-1 5-1 7v7c-1 0-1-1-1-1l-3-6c0-2 0-5-1-7 2-3 3-7 4-10 2-5 2-11 2-16z"></path><path d="M85 293c0-2 1-3 1-4v-1c1-1 1-1 1-2v-1c1-1 0-3 1-4 0 0 1 0 1 1h0c1 0 2 0 3 1h-1c2 0 3-1 4-2 2 2 3 4 5 5l1 1h0l21 8c3-1 6-1 8-1l2-1c-1 0-1 0-1-1 1-1 1-1 2-1h-1 0v-1l2-2h0s3-1 4-1 2-1 3-1h2 1c2 5 7 10 12 14 2 1 4 2 5 3h1v2h1c4 2 7 4 11 7l2 2 3 3 19 19 2 2h1l1-1v12c0 1 1 2 1 3h1v1c1 0 1 1 1 1l1 1 1 1 6 5h0l3 2 6 3c4 3 5 6 8 9l1-1c1 0 2-1 3-2l2 1 1 1c-2 1-3 3-3 5 0 1 1 2 1 3l1 1h1c0 1 1 1 2 2l-2 2h1 1l5 6 2-2 1 1c1 1 1 3 0 4 1 3 1 5 1 8 0 1 1 2 2 3l1 2 1 2 1-1 1 1h1v-1c0-1 0-2-1-4h1l3 4c1 1 1 1 2 0 0 2 0 4 1 6 1 5 4 9 4 14 0 3 3 6 5 9l3 2 5 3c1 1 5 4 6 4l3 3-2 1c1 1 2 2 2 3s0 2-1 2v1 1 1s1 0 2 1h0l-1 1-3 1-3 3c-1 1-1 3 0 4v1c1 1 0 3 0 4v1c-1-1-1-1-1-2h-1 0l2 5c0 2 0 2 1 3v2l1 3h1v1c0 1 0 1 1 2v2l1 1v2c1 0 1 1 1 2 1 1 0 4 1 5v2h-1c-1-2-2-4-2-7l-1-1-3-9c-1-2-1-4-3-5l-13-28c-13-25-30-49-47-72-9-11-19-22-29-32-18-17-37-32-59-42-6-3-12-5-19-6s-16-1-22 4c-3 2-4 6-4 10l3 3c1 1 1 1 2 3 0 1 1 1 2 2 2 0 6 1 7 1h0 3c0 3 0 5 1 7 0 2 0 3 1 4v2l1 1-1 1-13-9c-7-5-10-12-11-20-1-6 0-12 4-17l1-1z" class="j"></path><path d="M92 328h2l1 1v-1c2 2 5 3 8 4 0 2 0 3 1 4-2 0-3-2-5-3-2-2-5-3-7-5z" class="L"></path><path d="M88 291c2-1 7-3 10-3h0c-2 1-4 1-5 4-1 1-1 2-1 4 0 0 0 2-1 2 0 1-2 2-3 3 0-2 1-4 1-5-1-2 0-4-1-5z" class="Z"></path><path d="M198 336l2 2h1l1-1v12l-3-5-4-5v-1c1 0 2-1 3-2z" class="e"></path><path d="M198 336l2 2c-1 1-1 3-1 5v1l-4-5v-1c1 0 2-1 3-2z" class="I"></path><path d="M122 295c3-1 6-1 8-1h1c1 2 4 3 5 3 5 3 10 6 14 9h-1c-8-4-17-8-27-11z" class="Y"></path><path d="M85 316l3 3c1 1 1 1 2 3 0 1 1 1 2 2 2 0 6 1 7 1h0 3c0 3 0 5 1 7-3-1-6-2-8-4v1l-1-1h-2c-2-1-3-3-4-4-2-1-3-6-3-8z" class="E"></path><path d="M88 324h2c0 1 1 1 2 2 1 0 2 1 3 2v1l-1-1h-2c-2-1-3-3-4-4z" class="S"></path><defs><linearGradient id="Bq" x1="77.86" y1="305.488" x2="87.398" y2="300.738" xlink:href="#B"><stop offset="0" stop-color="#858384"></stop><stop offset="1" stop-color="#a1a1a0"></stop></linearGradient></defs><path fill="url(#Bq)" d="M88 291c1 1 0 3 1 5 0 1-1 3-1 5l-4 4c-2 4-3 7-2 12 2 8 9 13 15 17l7 4 1 1-1 1-13-9c-7-5-10-12-11-20-1-6 0-12 4-17l1-1 3-2z"></path><path d="M88 291c1 1 0 3 1 5 0 1-1 3-1 5l-4 4c0-2 0-3 1-5 1-1 0-3 0-5l-1-1 1-1 3-2z" class="D"></path><defs><linearGradient id="Br" x1="187.592" y1="344.475" x2="140.388" y2="286.029" xlink:href="#B"><stop offset="0" stop-color="#c3c3c4"></stop><stop offset="1" stop-color="#f6f6f5"></stop></linearGradient></defs><path fill="url(#Br)" d="M138 287c1 0 2-1 3-1h2 1c2 5 7 10 12 14 2 1 4 2 5 3h1v2h1c4 2 7 4 11 7l2 2 3 3 19 19c-1 1-2 2-3 2v1c-10-10-21-20-32-29-2 1-4 1-6 0h0c-3-1-5-2-8-3v-1h1c-4-3-9-6-14-9-1 0-4-1-5-3h-1l2-1c-1 0-1 0-1-1 1-1 1-1 2-1h-1 0v-1l2-2h0s3-1 4-1z"></path><path d="M163 305c4 2 7 4 11 7h-2 0c-3-1-7-4-9-7z" class="Z"></path><path d="M138 287c1 0 2-1 3-1h2 1c2 5 7 10 12 14 2 1 4 2 5 3h1v2c-8-3-15-11-19-17-4 2-7 4-11 5-1 0-1 0-1-1 1-1 1-1 2-1h-1 0v-1l2-2h0s3-1 4-1z" class="d"></path><path d="M138 287c1 0 2-1 3-1h2l-2 1c-2 2-5 3-8 4h-1 0v-1l2-2h0s3-1 4-1z" class="I"></path><defs><linearGradient id="Bs" x1="271.011" y1="411.483" x2="218.121" y2="421.967" xlink:href="#B"><stop offset="0" stop-color="#807e7e"></stop><stop offset="1" stop-color="#d9d8da"></stop></linearGradient></defs><path fill="url(#Bs)" d="M279 474c-1 0-3-6-3-7l-14-27c-12-22-26-42-42-62l-14-16-6-6c0-1-1-1-1-2 2 1 3 3 4 4 2 2 2 3 4 3v-1c1-1 1-1 1-2s-1-1-1-2l6 5h0l3 2 6 3c4 3 5 6 8 9l1-1c1 0 2-1 3-2l2 1 1 1c-2 1-3 3-3 5 0 1 1 2 1 3l1 1h1c0 1 1 1 2 2l-2 2h1 1l5 6 2-2 1 1c1 1 1 3 0 4 1 3 1 5 1 8 0 1 1 2 2 3l1 2 1 2 1-1 1 1h1v-1c0-1 0-2-1-4h1l3 4c1 1 1 1 2 0 0 2 0 4 1 6 1 5 4 9 4 14 0 3 3 6 5 9l3 2 5 3c1 1 5 4 6 4l3 3-2 1c1 1 2 2 2 3s0 2-1 2v1 1 1s1 0 2 1h0l-1 1-3 1-3 3c-1 1-1 3 0 4v1c1 1 0 3 0 4v1c-1-1-1-1-1-2h-1 0z"></path><path d="M281 463v-1h0v-1l1-1 1 1c-1 1-1 2-2 2z" class="K"></path><path d="M251 409l1 2 1 1 1 3-1-1c-2-2-2-3-2-5z" class="H"></path><path d="M283 461l1 2-3 3v-2h-1l1-1c1 0 1-1 2-2z" class="c"></path><path d="M216 363l6 3h-1c-1 1 0 1 0 2v1l-5-6z" class="G"></path><path d="M277 453h1 0c1 1 3 2 4 2 0 1 1 2 2 2-1 1-1 1-1 2l-1 1v-1l-5-6z" class="Q"></path><path d="M253 412l5 6h0c0 2 0 3 1 4v2l-5-9-1-3z" class="c"></path><path d="M284 457v1h2v1 1s1 0 2 1h0l-1 1-3 1-1-2-1-1v-1 1l1-1c0-1 0-1 1-2z" class="S"></path><path d="M284 458h2v1 1h-1c-1-1-1-2-1-2z" class="f"></path><path d="M282 459v1c2 0 4 1 6 1h0l-1 1-3 1-1-2-1-1v-1z" class="X"></path><path d="M258 418c4 5 4 11 7 16-3-2-5-7-6-10v-2c-1-1-1-2-1-4z" class="T"></path><path d="M254 406h1l3 4c0 2 1 3 1 4-1 1 0 1 0 2s-1 1-1 2l-5-6-1-1 1-1 1 1h1v-1c0-1 0-2-1-4z" class="S"></path><path d="M267 437l3 4c1 3 3 6 6 8s5 4 8 7c-1 0-2 0-2-1-1 0-3-1-4-2h0-1-1c-3-4-8-10-9-16z" class="O"></path><path d="M260 410c0 2 0 4 1 6 1 5 4 9 4 14 0 3 3 6 5 9h0c0 1 1 2 1 2h-1l-3-4c-1-1-2-2-2-3-3-5-3-11-7-16h0c0-1 1-1 1-2s-1-1 0-2c0-1-1-2-1-4 1 1 1 1 2 0z" class="Q"></path><path d="M271 441s-1-1-1-2h0l3 2 5 3c1 1 5 4 6 4l3 3-2 1c1 1 2 2 2 3s0 2-1 2v1h-2v-1c-1 0-2-1-2-2 0 1 1 1 2 1-3-3-5-5-8-7s-5-5-6-8h1z" class="L"></path><path d="M271 441s-1-1-1-2h0l3 2v1c1 2 2 3 3 5-2-2-4-4-5-6z" class="V"></path><path d="M273 441l5 3c1 1 5 4 6 4l3 3-2 1c-3-2-6-3-9-5-1-2-2-3-3-5v-1z" class="F"></path><path d="M222 366c4 3 5 6 8 9l1-1c1 0 2-1 3-2l2 1 1 1c-2 1-3 3-3 5 0 1 1 2 1 3l1 1h1c0 1 1 1 2 2l-2 2h1 1v1h0-3l1 1s0 1 1 1v1l1 1v1h-1s0-1-1-1v-1l-1-1c0-1-1-2-2-3-1 0-1-1-2-2 0-1-1-1-1-2-1-1-1-1-1-2l-1-1s-1 0-1-1h-1c0-1-1-1-2-2 0-1 1 0 0-1l-1-1c-1-2-3-4-3-6v-1c0-1-1-1 0-2h1z" class="D"></path><path d="M236 383h1c0 1 1 1 2 2l-2 2h1 1v1h0-3l1 1s0 1 1 1v1l1 1v1l-4-5c-1-1-2-2-2-3h1l2-2z" class="C"></path><path d="M616 277h1v3c1 4 3 7 4 10l1 2h0l-2-1c-1 1-1 1-1 3v7c4 4 6 8 7 13 0 6-4 11-8 15s-9 7-14 9c0-1 0-2 1-3 1-2 1-5 1-7v-2-2c-1-4-2-8-5-11h-1c-7 5-14 12-16 21-2 5-1 10 2 15l2 2c0 2 1 3 2 4h1c1 1 1 2 2 2l1 1h-1-4s-1 0-1-1h-2-1c-1 0-1-1-2-1s-1-1-2-2h-1 0c0-1-1-2-1-3h-1l-1-1-1-1h-1v1h0s-1 0-1-1-1-2-2-3l-3-3c-1-1-2-2-2-3h-1l1 1c0 1 1 2 1 3h-1-1v1l1 1v1 4h1v-1-2-1l1 1c0 1 1 2 2 3 5 6 14 11 22 14 1 0 2 0 3 1h-1l-2 1c-3-1-7-3-11-3-1 2-2 3-3 5-2-1-3-1-5 0-1 0-5 0-6 1 0 1-1 3-1 4-1 1-1 1-2 0h-2c0 2 0 2-1 3h-1c0 3 1 7 2 10 1 7-2 13-4 20h0c-2 2-3 5-6 4h0v-1-5c-2-7-4-11-10-15-2-2-5-3-7-4-3 7-5 13-5 21v12l-1-1v-1 1h-1c0-2 0-2-1-2-2 0-2 0-3 1s-1 2-2 4c-1 1-1 1-1 2h-1c0 2-1 4-1 6v1l-3-3-2-1c-1-1-1-1-2-1-1 1-1 2-2 4-1 5-1 10-2 15-1 9 0 17 2 26v1l-1-1h0c-1-1-1-3-2-5-1-5-1-9-2-14-1 0-2 0-2 1h-1-4v-2c-1 0-1 0-1-1-1-1-1-4-1-5l-1 1v2c0 3 0 6 1 8v1l4 38c1 2 1 4 0 6v1c-1-2-3-5-5-6-3 0-7 1-9 3s-3 5-4 8c0 4 1 9 4 12l6 5 3 6h-3-1v3l1 1-1 1h0c-4 2-7 6-9 10 0 2 0 5 1 7 1 1 2 2 4 3h0c-1 1-2 1-3 1-5-1-8-7-11-10-2-4-2-9-4-11h-1l1 8h-1v3c0 1 1 4 1 5-1 1 0 1 0 2s1 2 2 3h-1c-1 0-3 0-4-1-1 3-2 6-2 10v2l-1 1v2 1 2-2l-1-2v1c-1 0-1 0 0 1v5 2 8c0-1 0-1-1-1v1c0 2 1 4 3 6l-1 2h-1 0c-1 0-2-1-3-1h0c-2-3-5-8-8-10-1-1-2-1-3-2-1 2-1 4-1 7h0c-1 2-2 4-2 6h0l-1 1c0-2-1-3-2-4l-1-1c-1-2-2-7-4-9h-2c-2 0-4 3-5 5s-1 3-1 5c1 1 1 1 2 1s2 0 3-1c1-2 2-5 2-7 2 5 2 8 0 12-2 5-7 8-11 12-2-1-5-1-8 0v1s0 1-1 1h-1c-2-2-4-3-6-5l-1-1c-2-2-5-4-7-6 1-2 1-4 1-6 1-2 1-4 0-5h0v-1c1 0 0-2 0-3l1-1c-1 0-1-1-1-2v-1-4s1-1 1-2c0-3-1-6 0-9v-2c1-1 1-3 0-4-1-3-1-6-1-9 1-3 2-7 3-11s2-7 3-11l6-23c2-1 2 0 3 0l1-3s1-1 1-2l11-30 9-16 13-22c0-3 1-5 3-7 1-3 2-6 3-8l1-1h-1l-1 1v1l-2 2-1 1v2 1h-1v2h-1c1-1 0-3 1-5v-2c0-1 1-2 2-4h0c0-1 2-3 2-4l3-5c1-1 2-3 3-4l-1-1c0-1 1-3 2-4v-1c1-2 3-6 5-7h1 1l-3-3c-3-2-7-7-7-11 0-2 4-3 6-4 3 0 7 1 9 3v1c-1 0-1-1-2-1v1l3 2c2-2 4-6 7-9 2-1 3 0 5 0 3-2 8-5 9-8 0 0 2-2 3-2l1-2c1-1 1-3 2-4h1l3-5 4-4v-2-2c-1-1-1-2-1-2v-1l2-2c5-4 10-9 15-14l3-4h0 1 2c2 0 4-2 6-3 2-2 4-3 6-4l2-1 6-4c3-1 4-4 5-6 3-2 4-5 6-7h2c3 2 7 4 11 5 1 0 2 1 4 1h1l21-6 1-1s1-1 2-1c3-3 6-6 8-10z" class="B"></path><path d="M464 567v-1h0c-1-1 0-2 0-2h1v2l-1 1z" class="F"></path><path d="M456 578h1l1 1c1 1 2 3 2 5-1-2-3-4-4-6z" class="O"></path><path d="M456 578c-1 0-2-2-3-3l4 1 1 1-1 1h-1z" class="L"></path><path d="M458 509h1 0c-1 2-2 3-3 4v-1c0-1 0-2 1-2l1-1z" class="W"></path><path d="M451 561c-1-1-1 0-2 0v-1h1v-1c0-1 2-2 3-3 0 2-1 4-2 5z" class="F"></path><path d="M457 570l1-1 2 1v3l-1-1c-2 2 0 5-1 7l-1-1 1-1h0c0-3 0-5-1-7z" class="S"></path><path d="M537 363c1-1 2-1 4-2 1 0 3 0 4 1l1 1h0-1c-2 0-5-1-7 0h-1z" class="E"></path><path d="M471 504h1c-1 1-2 3-2 4l1 1-1 1-2-3 2-2 1-1z" class="S"></path><path d="M492 532c-2-1-4-4-6-6l6 3v3z" class="M"></path><path d="M511 388v-2l1-3c1 0 1 1 1 2h0v2 1 3l-1 1v-3l-1-1z" class="f"></path><path d="M493 523l3 6h-3v-1c0-1 0-1-1-2 0-1 0-2 1-3z" class="V"></path><path d="M456 470c-1 2-2 2-3 3s-2 1-4 2c1-1 1-2 2-3h0-3c1-1 2-1 3-1h2 0l3-1z" class="J"></path><path d="M469 516c1 1 2 3 3 4l1 5-3-3c-1-2-1-4-1-6z" class="h"></path><path d="M504 410l4-8 1 1-3 6-2 2v-1z" class="T"></path><path d="M484 453v1c0 2-3 5-4 8h-1v-1c0-3 3-6 5-8z" class="V"></path><path d="M456 470h2c-1 1-2 2-2 3l-1 1v1c-1 1-2 1-3 2v-1c0-1 0-2 1-3s2-1 3-3z" class="O"></path><path d="M537 363h1c0 2 1 4 1 6h-6v-1l4-1v-4z" class="S"></path><path d="M456 512v1 2 1l2-1c-1 2-2 2-3 3 0 0-1 0-1-1-1 0-1 0-1-1 0-2 1-3 3-4z" class="D"></path><path d="M453 521h1c2 0 3 0 5-2l2 3-1 1h-5c0-1-1-1-2-2z" class="U"></path><path d="M487 452v-5c2 3 4 6 4 10-1-2-1-2-3-3 0-1 0-1-1-2z" class="J"></path><path d="M494 417c4-2 7-4 10-7v1 1c-3 2-5 4-9 5h-1z" class="X"></path><path d="M453 473c-1 1-1 2-1 3v1c-3 1-7 1-11 2 3-2 5-3 8-4 2-1 3-1 4-2z" class="L"></path><path d="M479 526v-1-1h1 0c1 4 3 7 7 9h-1c-1 0-2 0-4-1l-1-1-2-5z" class="Z"></path><path d="M445 579v-2l1-1c1 0 3 0 4-1 1 2 3 3 3 5v-1c-1 0-3-1-4 0-2 0-2-1-3-1l-1 1z" class="J"></path><path d="M458 579c1-2-1-5 1-7l1 1v5l1 1h0v2c1 1 0 1 1 2v2l-2-1c0-2-1-4-2-5z" class="R"></path><path d="M453 510c1-1 2-2 3-2l1 2c-1 0-1 1-1 2-2 1-3 2-3 4 0 1 0 1 1 1 0 1 1 1 1 1 0 1-1 1-1 1h-1v-9z" class="G"></path><path d="M438 560c2 1 2 1 3 1 1-1 1-1 2-1 0 0 1 1 1 2h1 1v1 1 1h1-2c-1 0-1 0-1-1-1 0-1 0-2-1s-1-1-2-1h-1l-1-1v-1z" class="V"></path><path d="M447 567c1 0 1 1 2 1l1 1c0 1 1 0 2 1 1-1 1-1 3-1v5c-2-2-5-3-8-5 0-1 0-1-1-2h1z" class="L"></path><path d="M456 497c0-5 2-11 6-15h1v1c-2 2-3 5-4 8h0 1l-1 1v1c0 1 0 1-1 2 0 1-1 2-2 2z" class="J"></path><path d="M453 487v1c0 7 0 13-2 20-1-8 0-14 2-21z" class="E"></path><path d="M431 515c-1-4 0-9 0-13 1 3 1 6 1 8 1 2 1 3 2 5h0v-1h0c0-2 0-9 1-11v5 8h-2l-1-2h0l-1 1z" class="J"></path><path d="M459 456h1c0 3 0 5-2 7-2 3-4 6-7 8-1 0-2 0-3 1h-2 0c2-1 4-2 6-4 4-3 6-7 7-12z" class="g"></path><defs><linearGradient id="Bt" x1="470.24" y1="451.415" x2="470.997" y2="460.311" xlink:href="#B"><stop offset="0" stop-color="#4f4e4f"></stop><stop offset="1" stop-color="#676968"></stop></linearGradient></defs><path fill="url(#Bt)" d="M469 457c1-1 1-2 1-3 1-1 1-1 1-2l3-3v1c-3 3-2 8-4 12l-1 2c-1 0-1 0-2-1l1-1c1-2 1-3 1-5z"></path><path d="M468 462l1-1 1 1-1 2c-1 0-1 0-2-1l1-1z" class="T"></path><path d="M454 503h3c1 0 2 0 3 1h0v2l-2 3-1 1-1-2c-1 0-2 1-3 2l1-5v-2z" class="O"></path><path d="M454 505c1 0 2 0 3 1v2l1 1-1 1-1-2c-1 0-2 1-3 2l1-5z" class="i"></path><defs><linearGradient id="Bu" x1="454.652" y1="542.557" x2="453.043" y2="547.514" xlink:href="#B"><stop offset="0" stop-color="#777576"></stop><stop offset="1" stop-color="#929293"></stop></linearGradient></defs><path fill="url(#Bu)" d="M450 542h2 0c1 1 1 1 2 1l2 1 2 2c1 2 1 3 2 4-4-1-9-2-13-4 1 0 1 0 2-1l5 1-4-4z"></path><path d="M440 541c1 1 3 1 3 2h1v1c2 0 3 1 5 1-1 1-1 1-2 1h0l-6-3-15 10h0c0-2 2-3 3-4 1 0 2-1 3-2l8-6z" class="H"></path><path d="M506 451c-1-1-1-2 0-3h0c0 1 0 1 1 2h0v8h1v2 2c0 1 0 1 1 1-1 0-1 0 0-1h0l-1-1v-2-1c0-2-1-5 0-8v-2-2h0l1-2c-1 9 0 17 2 26v1l-1-1h0c-1-1-1-3-2-5-1-5-1-9-2-14z" class="J"></path><defs><linearGradient id="Bv" x1="491.354" y1="429.646" x2="491.764" y2="434.145" xlink:href="#B"><stop offset="0" stop-color="#a1a1a0"></stop><stop offset="1" stop-color="#d1d1d3"></stop></linearGradient></defs><path fill="url(#Bv)" d="M489 433c4-1 7-3 10-5 1 1 2 1 2 1-7 5-14 7-23 6 1 0 3 0 4-1 2 0 4-1 7-1z"></path><path d="M552 384c2 1 2 2 2 3l1 1v1l1 1v1 2h0c1 1 1 0 1 1v1 1 3c0-1 0-3 1-3v-2h0l1 1h0v-1l1 1v-1l1-1v-2l1-1v5h-1c0 1 0 2-1 3 0 2-1 6-3 7-1-2 0-5 0-8-1-4-3-9-5-13z" class="h"></path><path d="M462 468l1 1c1-1 2-2 2-3 1 0 1 0 2-1v1c-4 7-13 14-21 15l-1 1c3-2 6-4 9-5 1-1 3-2 3-3h0c2-2 4-4 5-6z" class="W"></path><path d="M464 440l-5-3c5 1 9 0 14 1 3 0 6 1 9 1-3 0-4 0-6 2l-1 4-1-1c1-1 1 0 1-1v-1h0v-1h-1v1 4h-1v-5l-2-2c-1 0-4 1-6 1h-1z" class="E"></path><path d="M515 426c1 0 2 0 2-1 2-2 3-8 4-10s1-3 2-4v1 1c1-1 1-1 1-2 1 0 1 0 2 1h0c1-1 3-1 4-1v4h0l-4-1c-2 1-3 2-4 4s-2 4-2 5c-1 2-3 3-3 4l-2-1z" class="M"></path><path d="M530 411c0-1 0-3 1-4v12l-1-1v-1 1h-1c0-2 0-2-1-2-2 0-2 0-3 1s-1 2-2 4c-1 1-1 1-1 2h-1c0 2-1 4-1 6v1l-3-3c0-1 2-2 3-4 0-1 1-3 2-5s2-3 4-4l4 1h0v-4z" class="U"></path><defs><linearGradient id="Bw" x1="450.422" y1="558.641" x2="454.559" y2="567.744" xlink:href="#B"><stop offset="0" stop-color="#1b1a1b"></stop><stop offset="1" stop-color="#4d4d4d"></stop></linearGradient></defs><path fill="url(#Bw)" d="M453 556v-1h1l1 14c-2 0-2 0-3 1-1-1-2 0-2-1l-1-1c-1 0-1-1-2-1 0-1-2-1-2-2h2c0 1 1 1 2 2l1-1-1-1v-1l1-1v-1-1h1c1-1 2-3 2-5z"></path><path d="M449 564c1 0 2 0 3-1h1 0c0 1-1 2-2 3v2h-1v1l-1-1c-1 0-1-1-2-1 0-1-2-1-2-2h2c0 1 1 1 2 2l1-1-1-1v-1z" class="Q"></path><defs><linearGradient id="Bx" x1="461.068" y1="568.037" x2="461.089" y2="581.994" xlink:href="#B"><stop offset="0" stop-color="#2d2d2d"></stop><stop offset="1" stop-color="#6a6a6b"></stop></linearGradient></defs><path fill="url(#Bx)" d="M457 568c0-2-1-4 0-5 0-1 0-1 1 0h1c0 1 1 2 0 2l1 1h-1l1 1h0v1h1c1 0 1 0 2 1-1 0-1 0 0 1v5 2 8c0-1 0-1-1-1v1-2c-1-1 0-1-1-2v-2h0l-1-1v-5-3l-2-1-1 1v-2z"></path><path d="M457 568v-2l3 3v1l-2-1-1 1v-2z" class="E"></path><path d="M459 466c1-1 3-1 4-2l3-3 1 1-2 3h1l1-2c1 1 1 1 2 1l-2 2v-1c-1 1-1 1-2 1 0 1-1 2-2 3l-1-1c-1 2-3 4-5 6l-2 1v-1l1-1c0-1 1-2 2-3h-2l-3 1c0-1 5-4 6-5z" class="c"></path><path d="M458 470c2-2 4-4 7-6-1 2-2 3-3 4-1 2-3 4-5 6l-2 1v-1l1-1c0-1 1-2 2-3z" class="H"></path><path d="M553 405v3h1v-2c2-9 0-18-6-26-2-3-4-5-5-8 4 4 7 8 9 12s4 9 5 13c0 3-1 6 0 8-1 2-3 4-4 5v-5z" class="U"></path><path d="M482 501l1-1v2l-1 4c0 4-1 9 2 13 1 2 4 4 6 6-4-2-6-4-9-7-1 1-1 1-1 2v1 3h-1v1 1l-1-3v-6c1-5 3-11 4-16z" class="K"></path><path d="M478 523c1 0 0-2 1-3h1v1 3h-1v1 1l-1-3z" class="H"></path><path d="M445 579l1-1c1 0 1 1 3 1 1-1 3 0 4 0v1c3 4 6 9 10 13h0c-1 0-2-1-3-1h0c-2-3-5-8-8-10-1-1-2-1-3-2-1 2-1 4-1 7h0c-1 2-2 4-2 6h0c0-3-1-8-1-11v-3z" class="h"></path><defs><linearGradient id="By" x1="436.252" y1="560.887" x2="439.398" y2="567.396" xlink:href="#B"><stop offset="0" stop-color="#0b0a0b"></stop><stop offset="1" stop-color="#2f2e2e"></stop></linearGradient></defs><path fill="url(#By)" d="M428 564h6 1v-1c1-2 1-2 2-3h1v1l1 1h1c1 0 1 0 2 1s1 1 2 1c0 1 0 1 1 1 0 1 2 1 2 2h-1c1 1 1 1 1 2-2-1-3-3-5-4h-1l1 2h-1c0 1 0 1-1 2-2-1-4-1-6-1h-1l-5-4z"></path><path d="M486 439h3 1v1c-1 2-4 3-5 3s-2 0-2-1h-1c0 2 0 2-1 3l-7 5v-1l1-3v-1l1-4c2-2 3-2 6-2h4z" class="T"></path><path d="M482 439h4c-2 1-5 1-7 3v2h-2l-2 2v-1l1-4c2-2 3-2 6-2z" class="a"></path><path d="M494 417h1c4-1 6-3 9-5v2c-1 1-6 4-6 5-2 1-4 1-5 2l-3 3c-1 1-3 2-4 3 1-1 1-2 1-3-3 0-6 1-9 1l16-8zm-30 23h1c2 0 5-1 6-1l2 2v5h1v-4-1h1v1h0v1c0 1 0 0-1 1l1 1v1l-1 3-3 3c0 1 0 1-1 2 0 1 0 2-1 3v-7c-1-5-2-7-5-10z" class="S"></path><path d="M442 567c3 3 2 9 2 13h-1l-1-1c-1-2-2-3-3-4-4-4-8-7-12-11v-1l1 1 5 4h1c2 0 4 0 6 1 1-1 1-1 1-2h1z" class="U"></path><defs><linearGradient id="Bz" x1="517.59" y1="413.747" x2="510.866" y2="411.507" xlink:href="#B"><stop offset="0" stop-color="#5a5a5a"></stop><stop offset="1" stop-color="#8d8c8c"></stop></linearGradient></defs><path fill="url(#Bz)" d="M520 400h1c1 1 0 2 0 3v1h1c0-1 0-2 1-2v-2h0l1-1c-3 10-9 20-17 26 0-1 0-1-1-2 6-5 9-12 12-18 1-2 1-3 2-5z"></path><path d="M469 450v7c0 2 0 3-1 5l-1 1-1 2h-1l2-3-1-1-3 3c-1 1-3 1-4 2s-6 4-6 5h0-2c3-2 5-5 7-8 2-2 2-4 2-7h-1v-2l1 1 1 1v2h0 1l-1 1h0l-1 1v1c0 1-1 1-1 2h0l-4 5 3-3c1 0 1-1 2-2h0v-1c1-1 1-1 1-2l1-1 1-1c0-1 1-2 1-2l2-1c0 1 0 0 1 0v-1c1 1 1 2 1 3v2c1-1 1-2 0-2v-1h1l-1-1c1-1 1-4 1-5z" class="M"></path><path d="M463 459v-1c1-1 3-1 4-1v2h-4z" class="f"></path><path d="M463 459h4v3l-1-1-3 3c-1 1-3 1-4 2 1-3 3-4 4-7z" class="U"></path><defs><linearGradient id="CA" x1="429.815" y1="541.1" x2="422.112" y2="531.881" xlink:href="#B"><stop offset="0" stop-color="#111112"></stop><stop offset="1" stop-color="#323232"></stop></linearGradient></defs><path fill="url(#CA)" d="M421 555v-1-3l1-1 1-1h0v-2c-1 0-1 0-1-1v-4c1-4 2-9 5-13 1-2 2-4 4-7 0 4-2 8-2 12l-4 12c-1 2-2 5-3 7 0 1 0 2-1 2z"></path><defs><linearGradient id="CB" x1="456.257" y1="525.506" x2="448.675" y2="531.385" xlink:href="#B"><stop offset="0" stop-color="#2b2b2b"></stop><stop offset="1" stop-color="#535354"></stop></linearGradient></defs><path fill="url(#CB)" d="M443 496c1 2 1 3 1 5v3l1 1-1 1c0 1 0 2 1 3h0v1 1c0 1 0 2 1 3v1l2-1v-2s1-1 0-2h0v-1l-1-2v-1-4c-1-1 0-1-1-2v-1h0c-1 0 0 0-1 1h0c0-1 0-2 1-2h0c1 0 1 1 1 1v1c0 2 1 11 3 12 0 1 0 1-1 1-1 4 1 7 1 11 0 1 0 2 1 3 0 1 0 2 1 3v2l1 1c2 2 2 4 4 6 0 1 1 3 1 4v1c-1-1-2-3-4-4-4-8-6-16-9-25-1-6-2-12-2-19z"></path><defs><linearGradient id="CC" x1="424.498" y1="544.739" x2="430.805" y2="544.708" xlink:href="#B"><stop offset="0" stop-color="#424242"></stop><stop offset="1" stop-color="#5c5b5b"></stop></linearGradient></defs><path fill="url(#CC)" d="M429 534v3c-1 1-1 2-1 3h1 0l3-3h0c0 2-1 4-2 6s-1 4-2 6c1 1 0 0 0 0h1c-1 1-3 2-3 4h0c-2 3-7 4-6 9v1h0c0 2 2 3 2 5 2 2 3 5 4 7-1 2-2 4-4 5-1 0-2 0-3-1h0 0 0c2 0 3-1 5-2v-3l-4-10c-1-2-1-4 0-6 0-1 1-2 1-3 1 0 1-1 1-2 1-2 2-5 3-7l4-12z"></path><defs><linearGradient id="CD" x1="474.205" y1="499.881" x2="482.395" y2="518.193" xlink:href="#B"><stop offset="0" stop-color="#4e4f4e"></stop><stop offset="1" stop-color="#6a686b"></stop></linearGradient></defs><path fill="url(#CD)" d="M481 497h0v-1l2 1c-1 1-1 2-2 3l1 1c-1 5-3 11-4 16v6l1 3 2 5h-1c-2-1-3-3-4-5-1-3-3-9-2-13 0-3 3-6 4-9 1-2 2-5 3-7z"></path><path d="M481 500l1 1c-1 5-3 11-4 16v6l1 3 2 5h-1c-2-1-3-3-4-5v-1c0-4 0-9 2-14 0-1 1-3 2-5 0-2 1-4 1-6z" class="M"></path><defs><linearGradient id="CE" x1="437.18" y1="505.859" x2="441.893" y2="509.834" xlink:href="#B"><stop offset="0" stop-color="#292929"></stop><stop offset="1" stop-color="#484747"></stop></linearGradient></defs><path fill="url(#CE)" d="M442 496l1-7v7c0 7 1 13 2 19 0 2 0 2 1 4v1c-1-1-1-1-1-2l-1-2h0 0v-1l-2-2v-2h0l-4 2c0 1 0 2 1 3v1c-1 1 0 5-1 7v1h-1v-1-6c0-1-1-1-2-2v1l-1 1-1-2h2v-8l1-3v-1h1c0-1 1-2 2-3v1c-1 1-2 3-2 4h0l1-1 2-1h1v-5c-1 0-1 0-2-1h0 2l1-2z"></path><defs><linearGradient id="CF" x1="433.297" y1="513.772" x2="440.316" y2="519.864" xlink:href="#B"><stop offset="0" stop-color="#2e2c2d"></stop><stop offset="1" stop-color="#4a4c4b"></stop></linearGradient></defs><path fill="url(#CF)" d="M436 505c1 2 0 4 1 6v2c1 4 1 7 1 11v1h-1v-1-6c0-1-1-1-2-2v1l-1 1-1-2h2v-8l1-3z"></path><path d="M442 496l1-7v7c0 7 1 13 2 19 0 2 0 2 1 4v1c-1-1-1-1-1-2l-1-2h0 0v-1c-2-6-2-13-2-19z" class="K"></path><path d="M519 381c1 0 1-1 2-1 1 2 1 3 2 5h0c1 2 0 5 0 6h1v-1-4c1-3 1-8 2-11 2-4 4-5 7-7v1c-2 2-5 4-6 7v8c1 5-1 11-3 15l-1 1h0v2c-1 0-1 1-1 2h-1v-1c0-1 1-2 0-3h-1 0l-1-1h0v-1-7-2c0-1 0-1 1-2v1-4l-1-3z" class="h"></path><path d="M519 389c0-1 0-1 1-2v1 10l-1 1h0v-1-7-2z" class="O"></path><path d="M520 398v1c1-2 2-3 3-4 1-2 2-6 3-8 0-2-1-3 0-5v-3 2 1l1 2c1 5-1 11-3 15l-1 1h0v2c-1 0-1 1-1 2h-1v-1c0-1 1-2 0-3h-1 0l-1-1 1-1z" class="L"></path><path d="M431 515l1-1h0l1 2 1 2 1-1v-1c1 1 2 1 2 2v6 1h1l-2 17 3-1h1l-8 6c-1 1-2 2-3 2h-1s1 1 0 0c1-2 1-4 2-6s2-4 2-6h0l-3 3h0-1c0-1 0-2 1-3v-3c0-4 2-8 2-12h0v-7z" class="O"></path><path d="M431 515l1-1h0l1 2 1 2 1-1c0 1 0 2-1 3-1 0-1-1-1-1h-1v1c0 1-1 1-1 2v-7z" class="S"></path><path d="M432 537c2-4 2-10 3-15 0-1 0-2 1-4v2c1 1 1 3 1 4-1 6-1 12-3 17-1 1-1 2-2 3-1 0-1 0-2-1 1-2 2-4 2-6z" class="T"></path><path d="M437 524h0v1h1l-2 17 3-1h1l-8 6c-1 1-2 2-3 2h-1s1 1 0 0c1-2 1-4 2-6 1 1 1 1 2 1 1-1 1-2 2-3 2-5 2-11 3-17z" class="R"></path><path d="M477 469c1 1 1 1 1 2h1l-1 4-3 6-2 5-4 9c-2 4-4 6-8 10l-1 1v-2h0c-1-1-2-1-3-1h-3l2-6c1 0 2-1 2-2 1-1 1-1 1-2v-1l1-1h-1 0c1-3 2-6 4-8v1l1-1 1-2 12-12z" class="F"></path><path d="M477 469c1 1 1 1 1 2-3 5-7 6-11 10-1 0-1 0-1 1l-1-1 12-12z" class="L"></path><path d="M460 491l1-2c1-2 2-3 4-3 0 1-2 2-2 4v2h0c0-1 0-1 1-1h0 1c1 0 1 0 2-1h1c1-2 2-3 5-4l-4 9c-2 4-4 6-8 10l-1 1v-2h0c-1-1-2-1-3-1h-3l2-6c1 0 2-1 2-2 1-1 1-1 1-2v-1l1-1z" class="g"></path><path d="M468 490c1-2 2-3 5-4l-4 9c-2 4-4 6-8 10v-1c0-1 1-2 1-2l4-5c1-1 2-3 2-5v-1-1z" class="f"></path><defs><linearGradient id="CG" x1="463.558" y1="486.906" x2="461.183" y2="502.44" xlink:href="#B"><stop offset="0" stop-color="#1e1d1e"></stop><stop offset="1" stop-color="#403f40"></stop></linearGradient></defs><path fill="url(#CG)" d="M460 491l1-2c1-2 2-3 4-3 0 1-2 2-2 4v2h0c0-1 0-1 1-1h0 1c1 0 1 0 2-1h1v1c-2 4-5 6-6 9-1 1-2 2-3 2h-1c0-2 2-2 2-4v-4h0l-2 1c1-1 1-1 1-2v-1l1-1z"></path><defs><linearGradient id="CH" x1="450.403" y1="525.576" x2="433.156" y2="535.355" xlink:href="#B"><stop offset="0" stop-color="#595959"></stop><stop offset="1" stop-color="#8e8d8d"></stop></linearGradient></defs><path fill="url(#CH)" d="M438 524c1-2 0-6 1-7v-1c-1-1-1-2-1-3l4-2h0v2l2 2v1h0 0l1 2c0 1 0 1 1 2v-1c-1-2-1-2-1-4 3 9 5 17 9 25 0 2 1 3 2 4l-2-1c-1 0-1 0-2-1h0-2l4 4-5-1c-2 0-3-1-5-1v-1h-1c0-1-2-1-3-2h-1l-3 1 2-17v-1z"></path><path d="M444 516h0 0l1 2c0 1 0 1 1 2v-1c-1-2-1-2-1-4 3 9 5 17 9 25 0 2 1 3 2 4l-2-1c-1 0-1 0-2-1h0-2c-1-2-2-4-2-6v-1-2h0c-1-1 0-1-1-2v-1h0c-1-2-1-3-1-5l-2-9z" class="W"></path><path d="M446 525c2 5 3 10 5 14 1 2 2 3 3 4-1 0-1 0-2-1h0-2c-1-2-2-4-2-6v-1-2h0c-1-1 0-1-1-2v-1h0c-1-2-1-3-1-5z" class="c"></path><defs><linearGradient id="CI" x1="469.66" y1="550.673" x2="448.971" y2="515.628" xlink:href="#B"><stop offset="0" stop-color="#4b4b4c"></stop><stop offset="1" stop-color="#707070"></stop></linearGradient></defs><path fill="url(#CI)" d="M453 533l-1-1v-2c-1-1-1-2-1-3-1-1-1-2-1-3 0-4-2-7-1-11 1 1 1 2 2 3 0 2 1 4 2 5s2 1 2 2h5l1-1 1 2c3 6 5 11 7 18v3c0 1 1 4 1 5-1 1 0 1 0 2s1 2 2 3h-1c-1 0-3 0-4-1h0v-1c-2-2-5-2-7-3-1-1-1-2-2-4l-2-2c-1-1-2-2-2-4 2 1 3 3 4 4v-1c0-1-1-3-1-4-2-2-2-4-4-6z"></path><path d="M454 540c2 1 3 3 4 4l1 1c3 2 6 5 9 7l-1 1c-2-2-5-2-7-3-1-1-1-2-2-4l-2-2c-1-1-2-2-2-4z" class="C"></path><path d="M454 540c2 1 3 3 4 4l1 1c-1 0-2 0-2-1l-1-1s0 1 1 1c0 1 0 2 1 2l-2-2c-1-1-2-2-2-4z" class="H"></path><path d="M453 533l-1-1v-2c-1-1-1-2-1-3-1-1-1-2-1-3 0-4-2-7-1-11 1 1 1 2 2 3 0 2 1 4 2 5s2 1 2 2h5l1-1 1 2c-2 0-4 0-6-1 0 1 0 1 1 2h1 0-2c-1 0-2-1-3-1v1c0-1-1-2-1-3v-1c-1-1-1-2-2-3v-2h-1c0 1 1 3 1 4s1 2 1 3v3c1 3 2 5 2 7z" class="L"></path><path d="M485 458l1-1c0 4-1 7-2 11-1 6-2 11-2 16 0 2 1 5 0 7v1l-1 4v1h0c-2 0-3 1-3 2l-6 5h-1l-1 1-1-1c-2 0-3 1-4 2s-2 1-3 1l-3 2h0-1l2-3 1-1c4-4 6-6 8-10l4-9 2-5 3-6 1-4h-1c0-1 0-1-1-2l5-6v-1l3-4z" class="M"></path><path d="M485 458c1 2 0 4-1 6-1 0-1-1-2-2l3-4z" class="K"></path><path d="M476 496c3-4 5-7 6-12 0 2 1 5 0 7v-1c-2 1-4 6-6 6h0z" class="U"></path><path d="M468 500v1c0 1-2 2-1 3h0l1-1h0l1 1c1-1 1-2 4-2l-2 2-1 1-1-1c-2 0-3 1-4 2s-2 1-3 1l6-7z" class="F"></path><path d="M469 495c0 1 1 2 1 3l-2 2-6 7-3 2h0-1l2-3 1-1c4-4 6-6 8-10z" class="c"></path><path d="M476 496h0c2 0 4-5 6-6v1 1l-1 4v1h0c-2 0-3 1-3 2l-6 5h-1l2-2h0v-2c0-2 2-3 3-4z" class="E"></path><path d="M478 499l-1-1 5-6-1 4v1h0c-2 0-3 1-3 2z" class="S"></path><path d="M482 463v-1c1 1 1 2 2 2-2 4-3 9-4 13-1 5-1 10-4 13l-6 8c0-1-1-2-1-3l4-9 2-5 3-6 1-4h-1c0-1 0-1-1-2l5-6z" class="R"></path><path d="M477 469l5-6c0 3-2 6-3 8h-1c0-1 0-1-1-2z" class="E"></path><path d="M478 475h1c1 4-1 7-2 10v-6c0 1-1 1-2 2h0l3-6z" class="i"></path><path d="M475 481h0c1-1 2-1 2-2v6c-1 1-1 3-1 4v1l-6 8c0-1-1-2-1-3l4-9 2-5z" class="W"></path><defs><linearGradient id="CJ" x1="493.305" y1="395.719" x2="506.366" y2="422.857" xlink:href="#B"><stop offset="0" stop-color="#353437"></stop><stop offset="1" stop-color="#616061"></stop></linearGradient></defs><path fill="url(#CJ)" d="M514 390v-1-2-4h0c0-1-1-2 0-4h0v-3l1-1h1v-3h1v1c1 2 1 2 1 3 1 2 1 3 1 5l1 3v4-1c-1 1-1 1-1 2v2 7 1h0l1 1h0c-1 2-1 3-2 5-3 6-6 13-12 18 1 1 1 1 1 2l-3 3-3 1s-1 0-2-1c-3 2-6 4-10 5-3 0-5 1-7 1-1 1-3 1-4 1-1 1-1 1-2 0 1-1 2 0 3-1 1 0 3-2 3-3v-1c-1 0-2-1-3-1h-2 0l9-2c1-1 3-2 4-3l3-3c1-1 3-1 5-2 0-1 5-4 6-5v-2-1l2-2 3-6-1-1 1-3 1-6c0-2 1-3 1-5l1 1v3l1-1 1-1z"></path><path d="M510 393c1-1 1-1 1-2 1 3 0 5 0 8v2c-2 3-2 6-5 8l3-6-1-1 1-3 1-6z" class="a"></path><path d="M509 399l1 1v-1 1l-1 3-1-1 1-3z" class="X"></path><path d="M498 419c-1 2-2 3-3 4-5 5-9 6-16 6h-2 0l9-2c1-1 3-2 4-3l3-3c1-1 3-1 5-2z" class="H"></path><defs><linearGradient id="CK" x1="513.246" y1="377.662" x2="518.202" y2="383.425" xlink:href="#B"><stop offset="0" stop-color="#0c0c0c"></stop><stop offset="1" stop-color="#292929"></stop></linearGradient></defs><path fill="url(#CK)" d="M514 390v-1-2-4h0c0-1-1-2 0-4h0v-3l1-1h1v-3h1v1c1 2 1 2 1 3 1 2 1 3 1 5l1 3v4-1c-1 1-1 1-1 2v2c-1-2 0-4-1-6-1 0-2 0-2 1-1 0-1 0 0 1h0c-1 2-1 3-1 4s-1 4 0 5c0 1-1 4-2 5h0l1-4v-3-4z"></path><path d="M518 384l1-1h0c0 1 0 1 1 1v4-1c-1 1-1 1-1 2 0-2 0-4-1-5z" class="R"></path><path d="M518 376c1 2 1 3 1 5l1 3c-1 0-1 0-1-1h0l-1 1h0c0-3-1-6 0-8z" class="L"></path><path d="M489 433l-1-1c0-1 9-4 10-4 2-1 4-3 6-4 2-2 3-4 5-6h0c2-3 3-6 4-9l4-12h1 0v1h1v1h0l1 1h0c-1 2-1 3-2 5-3 6-6 13-12 18 1 1 1 1 1 2l-3 3-3 1s-1 0-2-1c-3 2-6 4-10 5z" class="M"></path><path d="M504 426l2-3c1 1 1 1 1 2l-3 3v-2z" class="G"></path><path d="M499 428l5-2v2l-3 1s-1 0-2-1z" class="H"></path><path d="M518 398h1v1h0l1 1h0c-1 2-1 3-2 5-1-3 0-4 0-7z" class="R"></path><path d="M494 454c0-2 0-3 2-5 1-2-1-7 1-8h0v3l-1 1v2c0 3 0 6 1 8v1l4 38c1 2 1 4 0 6v1c-1-2-3-5-5-6-3 0-7 1-9 3s-3 5-4 8h-1l1-4v-2l-1 1-1-1c1-1 1-2 2-3l-2-1 1-4v-1c1-2 0-5 0-7 0-5 1-10 2-16 1-4 2-7 2-11v-2l1-3c1 1 1 1 1 2 2 1 2 1 3 3v1h2 0c0-1 1-2 1-4h0z" class="J"></path><path d="M489 460c0-1 0-2 1-3l1 1v4h-2v-2z" class="R"></path><path d="M483 497l1-4c0 2 0 5-1 7l1 1-1 1v-2l-1 1-1-1c1-1 1-2 2-3z" class="S"></path><path d="M497 477c1 5 1 11 2 16h0c-4-4-2-11-2-16z" class="E"></path><path d="M489 477h1c0 6 0 11 1 16l-3 2 1-18z" class="K"></path><defs><linearGradient id="CL" x1="486.997" y1="462.531" x2="508.111" y2="478.484" xlink:href="#B"><stop offset="0" stop-color="#212323"></stop><stop offset="1" stop-color="#5b5959"></stop></linearGradient></defs><path fill="url(#CL)" d="M494 454c0-2 0-3 2-5 1-2-1-7 1-8h0v3l-1 1v2c0 3 0 6 1 8v1l4 38c-1 0-1 0-2-1-1-5-1-11-2-16l-2-6c0-1 0-3-1-3-1-2 0-4 0-6v-8h0z"></path><defs><linearGradient id="CM" x1="483.267" y1="474.864" x2="488.715" y2="475.874" xlink:href="#B"><stop offset="0" stop-color="#5b5a5a"></stop><stop offset="1" stop-color="#757575"></stop></linearGradient></defs><path fill="url(#CM)" d="M487 452c1 1 1 1 1 2 2 1 2 1 3 3v1h0l-1-1c-1 1-1 2-1 3v2h2c-1 5 0 10-1 15h-1 0c-3 5-5 10-5 16l-1 4-2-1 1-4v-1c1-2 0-5 0-7 0-5 1-10 2-16 1-4 2-7 2-11v-2l1-3z"></path><path d="M487 452c1 1 1 1 1 2 2 1 2 1 3 3v1h0l-1-1c-1 1-1 2-1 3v2l-2 8c-1-2 1-5 1-7v-6l-1-2h-1l1-3z" class="L"></path><path d="M488 454c2 1 2 1 3 3v1h0l-1-1c-1 1-1 2-1 3-1-1-1-4-1-6z" class="U"></path><path d="M440 452v1c-1 3-4 10-6 13-1 1-1 1-1 3 1 0 1 1 1 2l1-1h0v-1l1-2c0-1 1-2 1-3 1-1 1-2 2-3v-1c1 0 1 0 1-1s0-1 1-2l2-2c-19 39-34 81-31 125 1 7 2 16 4 23l2 4v1s0 1-1 1h-1c-2-2-4-3-6-5l-1-1c-2-2-5-4-7-6 1-2 1-4 1-6 1-2 1-4 0-5h0v-1c1 0 0-2 0-3l1-1c-1 0-1-1-1-2v-1-4s1-1 1-2c0-3-1-6 0-9v-2c1-1 1-3 0-4-1-3-1-6-1-9 1-3 2-7 3-11s2-7 3-11l6-23c2-1 2 0 3 0l1-3s1-1 1-2l11-30 9-16z" class="D"></path><path d="M408 591v-1h1l1 9c0 2 1 4 0 5l-1-1-1-12z" class="J"></path><path d="M407 585c0-2-1-6 1-8v4l1 9h-1v1c-1-2-1-4-1-6z" class="f"></path><path d="M409 540h1v1l-2 35v-5-9c0-1 0-3-1-5 0-1 0-3 1-5l1-12z" class="O"></path><path d="M407 557c1 2 1 4 1 5v9 5 5-4c-2 2-1 6-1 8v-5c-1-1-1-2-1-3-2-7 1-13 1-20z" class="S"></path><path d="M403 578h2v-1h1c0 1 0 2 1 3v5c0 2 0 4 1 6l1 12c-2-2-5-4-7-6 1-2 1-4 1-6 1-2 1-4 0-5h0v-1c1 0 0-2 0-3l1-1c-1 0-1-1-1-2v-1z" class="K"></path><path d="M404 581v-2h1c1 1 1 4 1 5 0 3 0 5-1 7v1l-2-1c1-2 1-4 0-5h0v-1c1 0 0-2 0-3l1-1z" class="H"></path><defs><linearGradient id="CN" x1="422.035" y1="515.852" x2="402.954" y2="523.517" xlink:href="#B"><stop offset="0" stop-color="#393939"></stop><stop offset="1" stop-color="#636263"></stop></linearGradient></defs><path fill="url(#CN)" d="M406 537c1-4 2-7 3-11l6-23c2-1 2 0 3 0 0 3-1 6-2 8l-3 13c-2 6-2 11-3 17v-1h-1v-4l-1-1-2 2z"></path><path d="M406 537l2-2 1 1v4l-1 12c-1 2-1 4-1 5 0 7-3 13-1 20h-1v1h-2v-4s1-1 1-2c0-3-1-6 0-9v-2c1-1 1-3 0-4-1-3-1-6-1-9 1-3 2-7 3-11z" class="T"></path><path d="M616 277h1v3c1 4 3 7 4 10l1 2h0l-2-1c-1 1-1 1-1 3v7c4 4 6 8 7 13 0 6-4 11-8 15s-9 7-14 9c0-1 0-2 1-3 1-2 1-5 1-7v-2-2h1 1c1-1 3-3 5-3 2-1 5-2 6-4 1-1 2-1 3-1h0l1-2c-1-4-3-7-5-9-6-4-14-4-21-3-6 1-13 4-19 7-30 13-54 36-75 60-5 6-11 13-16 20l-17 21-18 29c-3 5-7 11-9 16l-2 2c-1 1-1 1-1 2s0 1-1 1v1c-1 1-1 2-2 3 0 1-1 2-1 3l-1 2v1h0l-1 1c0-1 0-2-1-2 0-2 0-2 1-3 2-3 5-10 6-13v-1l13-22c0-3 1-5 3-7 1-3 2-6 3-8l1-1h-1l-1 1v1l-2 2-1 1v2 1h-1v2h-1c1-1 0-3 1-5v-2c0-1 1-2 2-4h0c0-1 2-3 2-4l3-5c1-1 2-3 3-4l-1-1c0-1 1-3 2-4v-1c1-2 3-6 5-7h1 1l-3-3c-3-2-7-7-7-11 0-2 4-3 6-4 3 0 7 1 9 3v1c-1 0-1-1-2-1v1l3 2c2-2 4-6 7-9 2-1 3 0 5 0 3-2 8-5 9-8 0 0 2-2 3-2l1-2c1-1 1-3 2-4h1l3-5 4-4v-2-2c-1-1-1-2-1-2v-1l2-2c5-4 10-9 15-14l3-4h0 1 2c2 0 4-2 6-3 2-2 4-3 6-4l2-1 6-4c3-1 4-4 5-6 3-2 4-5 6-7h2c3 2 7 4 11 5 1 0 2 1 4 1h1l21-6 1-1s1-1 2-1c3-3 6-6 8-10z" class="j"></path><path d="M459 415c0 1 0 1 1 2v-1h1c-3 4-5 10-8 14 0-3 1-5 3-7 1-3 2-6 3-8z" class="P"></path><defs><linearGradient id="CO" x1="612.095" y1="319.497" x2="615.488" y2="330.106" xlink:href="#B"><stop offset="0" stop-color="#302e31"></stop><stop offset="1" stop-color="#616160"></stop></linearGradient></defs><path fill="url(#CO)" d="M623 314c-1 5-2 9-6 13l-12 8h0c1-2 1-5 1-7v-2-2h1 1c1-1 3-3 5-3 2-1 5-2 6-4 1-1 2-1 3-1h0l1-2z"></path><path d="M606 326h1 2v-1h1 1c1 0 2-1 3 0 0 1-4 4-6 4 0 1-1 1-1 1l-1-2v-2z" class="S"></path><defs><linearGradient id="CP" x1="618.554" y1="292.071" x2="609.738" y2="292.92" xlink:href="#B"><stop offset="0" stop-color="#a2a0a1"></stop><stop offset="1" stop-color="#d1d0d1"></stop></linearGradient></defs><path fill="url(#CP)" d="M616 277h1v3c1 4 3 7 4 10l1 2h0l-2-1c-1 1-1 1-1 3v7l-2-2c-2-1-4-1-6-2h-1c-2-1-4-1-6-1h1 4 0 2l1 1v-1c-1-2 0-3 0-5l-2-2h-2-3l1-1s1-1 2-1c3-3 6-6 8-10z"></path><path d="M617 299v-3c1-2 1-2 2-3v1 7l-2-2z" class="d"></path><path d="M616 277h1v3c1 4 3 7 4 10l1 2h0l-2-1h0c-2-2-9-3-12-4 3-3 6-6 8-10z" class="j"></path><defs><linearGradient id="CQ" x1="513.338" y1="322.176" x2="545.163" y2="324.701" xlink:href="#B"><stop offset="0" stop-color="#aba9a9"></stop><stop offset="1" stop-color="#d0d1d1"></stop></linearGradient></defs><path fill="url(#CQ)" d="M532 314h1 2c2 0 4-2 6-3 2-2 4-3 6-4 0 2-1 3 0 5-3 1-5 3-7 5-5 3-9 7-13 11l-14 13v-2-2c-1-1-1-2-1-2v-1l2-2c5-4 10-9 15-14l3-4h0z"></path><path d="M566 289h2c3 2 7 4 11 5 1 0 2 1 4 1h1l-16 7c-1 1-5 3-6 3-5 3-10 5-15 7-1-2 0-3 0-5l2-1 6-4c3-1 4-4 5-6 3-2 4-5 6-7z" class="b"></path><path d="M549 306c-1 1-1 2-1 3 3 1 4-1 7-1h0 1 0c2-1 3-2 4-2 1-1 1-1 2-1-5 3-10 5-15 7-1-2 0-3 0-5l2-1z" class="N"></path><defs><linearGradient id="CR" x1="459.321" y1="379.451" x2="489.844" y2="393.161" xlink:href="#B"><stop offset="0" stop-color="#898888"></stop><stop offset="1" stop-color="#cccbcb"></stop></linearGradient></defs><path fill="url(#CR)" d="M527 328c-1 2-3 4-5 5l-4 4c-2 2-4 3-5 5-1 1-2 2-2 3-2 2-4 5-5 8 1 0 1 1 2 2 0 0 1-1 1-2h1v-1s1 0 1-1h1 0c0-1 0-1 1-1 2-2 5-5 7-8 1 0 2-1 3-2-2 2-4 4-5 6l-11 10c-2 3-5 6-8 10l-10 12-17 23c-3 5-7 10-11 15h-1v1c-1-1-1-1-1-2l1-1h-1l-1 1v1l-2 2-1 1v2 1h-1v2h-1c1-1 0-3 1-5v-2c0-1 1-2 2-4h0c0-1 2-3 2-4l3-5c1-1 2-3 3-4l-1-1c0-1 1-3 2-4v-1c1-2 3-6 5-7h1 1l-3-3c-3-2-7-7-7-11 0-2 4-3 6-4 3 0 7 1 9 3v1c-1 0-1-1-2-1v1l3 2c2-2 4-6 7-9 2-1 3 0 5 0 3-2 8-5 9-8 0 0 2-2 3-2l1-2c1-1 1-3 2-4h1l3-5 4-4 14-13z"></path><path d="M467 398h1 0c0 1-1 2-2 4h-1c0-1 2-3 2-4z" class="H"></path><path d="M472 387l1-1c1 0 1 0 2-1 0 2-2 3-2 4l-5 5c-2 2-3 4-4 6l-1-1c0-1 1-3 2-4v-1c1-2 3-6 5-7h1 1z" class="K"></path><path d="M469 374h1c1 0 3 0 3 1 2 1 2 3 2 5 0 1 0 2-1 3-2 0-2-1-4-2-1-1-2-2-2-4 0-1 1-2 1-3z" class="B"></path><path d="M348 158l2 2h0l2 4h1l1 1 1-1 2-1c1-2 1-4 3-4h0 1c2 4 5 7 8 11v1c0 2-1 4-1 6 0 3 1 6 1 8 0 3 0 6 1 9 1 1 1 2 1 3 1 1 1 3 1 4l1 2v1c1 0 2 0 3 1h2l2 1c2 1 7 0 10 0h8 4 1 5 15 10v-2h0c2-2 4-4 6-5 2 1 3 1 4 1 1 1 2 1 3 2l2-1 2 2 1 2h16c2 0 3 0 4 1h7 8c0 1 0 2 1 2l-1 1c-3 0-7 0-9 3-1 1-1 3-1 4-2 9-1 19-1 28v55 4c-1 2 0 5 0 8l-1 14c1 1 4 0 5 0 1-1 2-1 2-1h2c2 0 2 0 4-1l-3-1h-3v-1c1 0 2-1 2-3h1c2 0 4 1 6 1h1l3 2 1-1c2 1 3 3 5 4l2 2c-1 0-1 1-1 1l2 3c1 1 2 3 2 4 1 6 2 11 0 16-1 1-1 3-2 4l-1 2c-1 0-3 2-3 2-1 3-6 6-9 8-2 0-3-1-5 0-3 3-5 7-7 9l-3-2v-1c1 0 1 1 2 1v-1c-2-2-6-3-9-3-2 1-6 2-6 4 0 4 4 9 7 11l3 3h-1-1c-2 1-4 5-5 7v1c-1 1-2 3-2 4l1 1c-1 1-2 3-3 4l-3 5c0 1-2 3-2 4h0c-1 2-2 3-2 4v2c-1 2 0 4-1 5h1v-2h1v-1-2l1-1 2-2v-1l1-1h1l-1 1c-1 2-2 5-3 8-2 2-3 4-3 7l-13 22-9 16-11 30c0 1-1 2-1 2l-1 3c-1 0-1-1-3 0l-6 23c-1 4-2 7-3 11s-2 8-3 11c0 3 0 6 1 9 1 1 1 3 0 4v2c-1 3 0 6 0 9 0 1-1 2-1 2v4 1c0 1 0 2 1 2l-1 1c0 1 1 3 0 3v1h0c1 1 1 3 0 5 0 2 0 4-1 6 2 2 5 4 7 6l1 1c2 2 4 3 6 5l3 3 1-1 1 1c-2 1-3 3-5 4-1 0-2 0-2-2v-1h-5l-42 2c-7 0-14 1-20 0l-51-2c-3 0-5 0-7-2h0c1-2 2-2 4-2 0-1 1-1 1-1 0-2 2-3 3-5 0-1 1-3 1-5 3-11 2-21 3-32 0-9-1-18-3-27l-2-11-3-12-2-10-2-2c-1-1 0-2 0-3s-1-2-1-2c0-3-1-5-2-8-1-4-3-8-5-12l-2-5h0 1c0 1 0 1 1 2v-1c0-1 1-3 0-4v-1c-1-1-1-3 0-4l3-3 3-1 1-1h0c-1-1-2-1-2-1v-1-1-1c1 0 1-1 1-2s-1-2-2-3l2-1-3-3c-1 0-5-3-6-4l-5-3-3-2c-2-3-5-6-5-9 0-5-3-9-4-14-1-2-1-4-1-6-1 1-1 1-2 0l-3-4h-1c1 2 1 3 1 4v1h-1l-1-1-1 1-1-2-1-2c-1-1-2-2-2-3 0-3 0-5-1-8 1-1 1-3 0-4l-1-1-2 2-5-6h-1-1l2-2c-1-1-2-1-2-2h-1l-1-1c0-1-1-2-1-3 0-2 1-4 3-5l-1-1-2-1c-1 1-2 2-3 2l-1 1c-3-3-4-6-8-9l-6-3-3-2h0l-6-5-1-1-1-1s0-1-1-1v-1h-1c0-1-1-2-1-3v-12-3c0-6 3-12 6-18 0 0 4-6 5-7l2-1c2-2 4-3 6-5 1-1 6-3 8-4h0l4-1v-23-39c0-6 1-12 0-18 0-2-1-6-2-7s-2-1-3-1c-2 0-5 0-6-2v-1c1-3 2-6 4-9 5-9 11-18 19-25 0 2 1 4 1 6 0 3-2 5-2 8 2 0 4 0 6 1 4 2 6 5 11 6 7 2 18 2 25-1 3-1 5-2 7-3l-1-1v-1c-2 0-4 1-6-1h1c3-1 5-3 8-5h1c3 1 7 0 9 0 4 0 7-1 10-2 4-1 7-2 11-4 1-1 2-2 4-3s3-2 5-4v1l-2 2 1 1 4-2h1c3-2 5-6 7-9 1-1 2-2 2-4z" class="m"></path><path d="M346 585c0-1 0-2 1-3 1 1 0 2 0 4h0l-1-1z" class="j"></path><path d="M335 254c2 0 2 1 3 1v1l-1 1h-1c0-1-1-2-1-3z" class="X"></path><path d="M344 315h0v6h-1c-1-2 0-5 1-6z" class="k"></path><path d="M337 573h1c1 1 1 1 1 2v1h-2c0-1 0-1-1-2l1-1z" class="H"></path><path d="M340 605l1-2 3 3-1 1c-1-1-2-1-3-2z" class="l"></path><path d="M335 419v-1l2 1c1 1 1 1 3 1h3 0v2c-2 0-3-1-5-2-1 0-2 0-3-1z" class="C"></path><path d="M345 415c-1 2-3 3-2 5h-3c0-1 1-2 2-3 1 0 1 0 1-1 1 0 1 0 2-1z" class="Y"></path><path d="M334 586h0v2h3 0l-3 2-2-4h2z" class="N"></path><path d="M338 496v-1h4c-1 1-1 2-1 2l-1 1c0 1 0 1-1 2v-3s-1 0-1-1z" class="j"></path><path d="M346 585l1 1-1 3-3 4-2-1c2-2 4-4 5-7zm-19 19c2 0 4 2 5 3l-5-1h-5l5-2z" class="Y"></path><path d="M341 592l2 1c-1 1-1 5-2 6l-3-4 3-3z" class="N"></path><path d="M335 419l-1-1 1-3h2 0c1 1 1 1 2 1v1c-1 1-1 1-3 1l1 1h0l-2-1v1z" class="j"></path><path d="M337 394l1 1c-1 1-1 2-1 3v1c2 4 5 8 7 11-2-1-4-3-5-5-2-4-3-7-2-11z" class="N"></path><path d="M248 254c1 0 2 1 3 1 3 2 6 1 9 1h1s-1 1-2 1l-4 1c-3-1-5-2-7-4z" class="C"></path><path d="M335 600v-2c1 1 2 1 2 1h1l3 4-1 2c-1 0-2 0-2-1v-1c-1 0-2-1-3-3z" class="j"></path><path d="M320 413s4-2 5-3v8l-1-1-4-4z" class="E"></path><path d="M240 301v4h1v-2h0v13c-1 3 0 7-1 9v-12-12z" class="G"></path><path d="M379 582l5 5 3 3h-4 0c0-1 0-1-1-2h-2 0v-2h0c-1-2-1-2-1-4z" class="D"></path><path d="M380 586l2 2h-2 0v-2z" class="H"></path><path d="M256 253c4-2 7-3 11-3-1 1-3 4-4 4s-2 0-2 1c-1-1-3-2-4-2h-1z" class="l"></path><path d="M334 598c0 1 1 1 1 2h0c1 2 2 3 3 3v1l-11-5h2c2-1 4-1 5-1z" class="b"></path><path d="M427 237l2-2c1 0 0 0 1 1 1 2 3 3 5 5 0 0 1 1 1 2-3 0-7-5-9-6z" class="G"></path><path d="M448 201l2 2 1 2h16c2 0 3 0 4 1h-25l-1-3 1-1h0l2-1z" class="K"></path><path d="M448 201l2 2c-2 0-3 0-4-1h0l2-1z" class="T"></path><path d="M337 277v1c0 1-1 2-1 3l-1 2s0 1-1 1v1 2c-1 4-1 9-1 12v5c-1-6-2-12 0-17v-1-1c1-3 2-5 4-8z" class="l"></path><path d="M240 285l2 1c1 1 2 2 2 4h-1v-1h-1l-1 14h0v2h-1v-4-16z" class="e"></path><path d="M318 594l9-10c0 2 0 4 1 5 1 2 3 4 5 5l5 5h-1s-1 0-2-1v2h0c0-1-1-1-1-2 0-2-5-5-6-7-1-1-2-3-3-4l-1 4v-1-1c-1 0-1 0-1 1-2 1-3 3-4 4h-1z" class="Y"></path><path d="M465 329c1 1 1 2 1 3h1c0 1 1 1 1 1 0 2 0 4-1 6v3c-1 1-1 1-1 2l1-2c1 0 1 0 2-1-1 2-3 4-4 5v-17z" class="D"></path><path d="M334 586c-1-1-1-2-1-3 1-1 1-1 2-1l1 1 2 1h3l2 1c-2 1-4 3-6 3h0-3v-2h0z" class="l"></path><path d="M233 275c1 7 1 15 1 23v6c-2 1-3 1-5 2 1-2 2-5 1-7h-1 0l4-1v-23z" class="O"></path><path d="M343 336h1v1c0 3-1 4-2 6-3 0-6 0-8-1 0-1-1-2-1-2v-1c1 1 2 1 3 2 4 0 5-3 7-5z" class="N"></path><path d="M337 394c1-2 2-4 4-7 3-5 5-11 5-17h1c0 6-1 12-4 17-1 3-4 5-5 8l-1-1z" class="l"></path><path d="M343 593l3-4-1 13h0c-1-1-3-2-4-3 1-1 1-5 2-6z" class="j"></path><path d="M462 262h1c1 0 2 3 3 3h1c1 2 1 14 1 17h0-1v-4l-1-6c0-1 0-2-1-3l-3-7z" class="Y"></path><path d="M338 496h-2c0-1 0-1-1-1v-1h2l2-2-1-1c-1-1-1-1-1-2h0c0-1 1-1 1-1 1 0 2 1 2 2v2c0 1-1 1 0 2h1s1-1 1-2c1-2 1-5 0-7v-1c-1 0-1-1-2-1 0-1 1 0 0-1-1 0-1-1-1-1l-1-1v-1-1h1c0 1-1 1 0 1 0 1 1 1 1 2 1 0 0 0 1 1l1 1c1 2 2 5 1 8h0c0 2-1 3-1 4h-4v1z" class="b"></path><path d="M342 501h1l-3 5c-1 2-3 4-3 6-1 1-1 2-1 2-1 3-3 6-3 9-1 3 0 8-1 11v-4h0c0 1 0 3-1 4 0-4 0-9 1-14 0-2 2-3 2-5 2-4 4-7 6-11l2-3h0z" class="Z"></path><path d="M322 601c1 0 2 0 4 1h-1c-4 2-8 4-13 5-2 0-3 0-5-1h1 0c5-1 9-4 14-5z" class="Y"></path><path d="M240 313v12c1-2 0-6 1-9 1 10-1 20 0 29h1-1c-1-1-1-2-2-3v-12c0-6 0-11 1-17z" class="P"></path><path d="M373 203v1c1 0 2 0 3 1h2l2 1h-21c-1-1-1-1-2-1l10-2c1 0 2 0 3 1h1c1-1 1-1 2-1z" class="i"></path><path d="M345 415c0-1 0-1-1-2v-2l1 2v1c2 3 1 6-1 9-1 2-2 5-3 8-1 2-3 3-4 6l-1 1v1c0 1-1 2-1 3-1 2-2 3-2 5v1h-1v-3c1-2 2-4 2-5 1-2 2-3 3-4v-1c2-4 5-7 6-10v-3h0 0v-2h0c-1-2 1-3 2-5z" class="N"></path><path d="M466 249c1 1 1 2 1 3v1c0 4-1 8 0 12h-1c-1 0-2-3-3-3h-1v-2-1-2s2-3 3-4l1-4z" class="W"></path><path d="M466 249c1 1 1 2 1 3v1c-1 1-1 2-2 2 0 1-1 2-1 3h1c0 2 0 2-1 3-1 0-1-1-2-2v-2s2-3 3-4l1-4z" class="T"></path><path d="M466 249v-6c0-7-2-13-6-18v-1c-1-1-2-2-4-3l-2-1h0c1 0 1 0 2 1h1s1 0 2 1h0c4 3 7 7 7 12 2 6 2 12 1 18 0-1 0-2-1-3zM332 586c0-3-1-7 0-10h1c1 0 1 0 2 1l3 3h1l1 1h1 0l1 2-1 1h-3l-2-1-1-1c-1 0-1 0-2 1 0 1 0 2 1 3h-2z" class="k"></path><path d="M336 583l1-2c-1-1-2-2-2-3-1 0-1 0 0-1l3 3h1l1 1h1 0l1 2-1 1h-3l-2-1z" class="X"></path><path d="M340 581h1 0l1 2-1 1h-3 1v-1c1-1 1-1 1-2z" class="d"></path><path d="M345 288h1c-1 3-3 5-3 8 0 1-1 2-1 3 0 5-3 8-5 13-2 3 2 7 3 10 1 2 1 3 2 5h-1l-1-1c-1 0-1-1-2-1v-1l2 1h0v-1c-1-1-1-2-2-3 0-1-1-3-2-4 0-2-1-4 0-6 1-5 5-8 6-13l3-10z" class="l"></path><path d="M425 233l2-4c1 0 1-1 2-1 1-1 1-2 2-2 1-1 1-1 2-1l2-1v-1h2c1-1 2-1 3-1h1c1-1 1 0 1 0-1 0-2 1-3 2l-1-1c-2 2-5 5-7 8-1 2-3 2-4 4h0v2l-1-2-1-2z" class="I"></path><path d="M248 254l-1-1c-1-1-1-2-1-3s0-1-1-2h0l1-1c1 1 1 2 2 2l1 1 1 1h1c2 1 3 1 5 2h1c1 0 3 1 4 2 0-1 1-1 2-1l-2 2h-1c-3 0-6 1-9-1-1 0-2-1-3-1z" class="b"></path><path d="M427 237v-2h0c1-2 3-2 4-4 2-3 5-6 7-8l1 1c-2 3-5 6-5 10l1 7c-2-2-4-3-5-5-1-1 0-1-1-1l-2 2h0z" class="C"></path><path d="M445 255s-1-1-2-1c2-1 4 0 6 0 5-1 8-3 13-5h0c-1 2-2 2-4 3l-1 1c-1 1-1 1-2 1s-2 1-3 1-1 0-1 1h3 1c1-1 1-1 2-1v-1h2v1l-1 1c-3 2-5 3-8 2h-1l-2-1-2-2z" class="k"></path><path d="M445 255h1c3 2 6 3 9 1l2-1 1 1c-3 2-5 3-8 2h-1l-2-1-2-2z" class="W"></path><defs><linearGradient id="CS" x1="437.857" y1="199.637" x2="436.506" y2="205.531" xlink:href="#B"><stop offset="0" stop-color="#757475"></stop><stop offset="1" stop-color="#8d8c8b"></stop></linearGradient></defs><path fill="url(#CS)" d="M433 204c2-2 4-4 6-5 2 1 3 1 4 1 1 1 2 1 3 2h0l-1 1 1 3h-6-7v-2h0z"></path><path d="M440 206l-2-1v-2c1-1 2-1 4-2 1 1 2 1 3 2l1 3h-6zm-192 53l5 4c-1 3-3 6-5 9h0c-1-1-2-2-4-3l1-3v-1l3-5v-1z" class="B"></path><path d="M244 269l1-3 1 1h1c1 1 1 3 1 4v1h0c-1-1-2-2-4-3z" class="X"></path><path d="M338 580h3c1-1 3-3 3-5 1-3-3-6-5-8l1-2c0-1 0-2 1-3 0-2 1-3 2-5v-1h1c0-1 0-2 1-3h0v2c-1 3-3 6-4 9v2l2 3c2 2 3 5 3 8 0 2 0 4-1 5s-1 2-2 3l-2-1 1-1-1-2h0-1l-1-1h-1z" class="b"></path><path d="M339 580h3l1 1v1l-1 1h0l-1-2h0-1l-1-1z" class="C"></path><path d="M343 581c1-2 1-4 2-6 0 1 1 1 1 2 0 2 0 4-1 5s-1 2-2 3l-2-1 1-1h0l1-1v-1z" class="I"></path><path d="M275 226c2 2 4 6 4 8v2 3l-8 5 2-3c3-5 2-9 0-14l2-1z" class="P"></path><path d="M357 200l1-2c2-1 2 0 4 0 1 1 3 1 4 2l2 1v1l-1 1-10 2c1 0 1 0 2 1h-5l-2-2 5-4z" class="C"></path><path d="M357 200l1-2c2-1 2 0 4 0 1 1 3 1 4 2l2 1v1h-2l-1-1h-1-4c-1 0-2 0-3-1z" class="I"></path><path d="M318 594h1c1-1 2-3 4-4 0-1 0-1 1-1v1 1l1-4c1 1 2 3 3 4 1 2 6 5 6 7-1 0-3 0-5 1h-2c-2 0-3-1-5-2-2 0-4 0-5-1 0-1 1-1 1-2z" class="j"></path><path d="M377 241l-1-1v-2h0c1 0 1 1 1 1v1c1 0 1 1 1 1v1 1c1 1 1 1 1 2h0c1 3 0 7 0 10h0c0 2-1 12 0 13 0 0 0 1 1 2 0 1-1 4 0 5v1c0 2 1 9 0 10v1 4h-1v-3c0 1-1 6 0 6 0 3 1 8 0 10 0 2-1 13 0 14v2l1-1h0c0 1 1 5 0 6v3c0 1 0 3-1 3v1c1 1 0 3 1 4v2 1 2 1h0 0-1v-2c1-1 0-7-1-9 0-6 1-12 0-19v-5-11-22-26c1-3 0-4-1-7z" class="Y"></path><path d="M246 225c3-2 7-5 11-5h0c-1 1-1 1-2 1-4 1-9 4-11 8-1 2-2 3-2 5-2 5 0 11 0 17l1 2c1 2 3 4 5 6v1l-3 5v1l-1 3-2-3v20l-2-1v-1c0-9 0-19 1-29v-13c0-3-1-5 0-8 1-4 2-6 5-9z" class="d"></path><path d="M245 265v-1c-1 1-2 1-4 1v-3-11h1l1 2c1 2 3 4 5 6v1l-3 5z" class="G"></path><path d="M248 260l-1-1c-2 0-1 1-2 2-2-2-2-5-3-7l1-1c1 2 3 4 5 6v1z" class="a"></path><path d="M246 225c0-2 1-2 2-3 1 0 1-1 2-1h0c1-1 1-1 2-1h1l1-1s1 1 1 0h6c1-1 2 0 2 0h3c1 1 2 0 3 0 1 1 1 1 2 1h2c0 1 0 0 1 1h2v1h2l1 1c1 0 2 1 3 2 0 1 0 0 0 0l1 1s1 1 2 1c1 1 1 1 2 1 1 1 2 1 2 1 2 1 2 1 3 3h-1c-4 2-7 4-10 6l-2 1v-3-2c0-2-2-6-4-8l-2 1-3-4c-1 0-3-1-3-2h-3-3-2c-1 0-2 0-3 1-1 0-1-1-1 0-1 0-2 0-2 1-2 0-3 1-4 2-1 0-1 1-1 1h-1c-1 1-2 3-3 4h0v1c0 1-1 2-2 3 0-2 1-3 2-5 2-4 7-7 11-8 1 0 1 0 2-1h0c-4 0-8 3-11 5z" class="Y"></path><path d="M276 222h2l1 1c1 0 2 1 3 2 0 1 0 0 0 0l1 1s1 1 2 1c1 1 1 1 2 1 1 1 2 1 2 1 2 1 2 1 3 3h-1c-1 0-2 0-2-1h-1l-1-1c-4-2-8-6-12-7h0c0-1 0 0 1-1h0 0 0z" class="N"></path><path d="M267 221v-1l2 1c1 0 2 1 3 2 4 2 8 4 10 8 1 1 1 2 1 3h-1l-1 2v2l-2 1v-3-2c0-2-2-6-4-8l-2 1-3-4c-1 0-3-1-3-2z" class="Z"></path><path d="M270 223h1c1 1 2 2 4 3l-2 1-3-4z" class="W"></path><defs><linearGradient id="CT" x1="453.206" y1="324.659" x2="477.382" y2="308.372" xlink:href="#B"><stop offset="0" stop-color="#a8a6a6"></stop><stop offset="1" stop-color="#dddddc"></stop></linearGradient></defs><path fill="url(#CT)" d="M463 289c1 0 2-1 3-1v1l1-1c0 4 0 8 1 12 1 8 1 16 1 25v11 5c-1 1-1 1-2 1l-1 2c0-1 0-1 1-2v-3c1-2 1-4 1-6 0 0-1 0-1-1h-1c0-1 0-2-1-3l-4-35v-1-1h1 0l1-3z"></path><path d="M466 288v1l1-1c0 4 0 8 1 12v1h-1v-5c-1-3-1-6-1-8z" class="T"></path><path d="M380 421v8c0 1 0 3 1 4 0 2 1 15 0 17v12c0 1 0 5-1 6 0 1 0 9 1 10 0 1 0 2-1 2v10c0 1 0 2 1 3 0 3-1 8 0 10h0c0 1 1 1 1 2v1 3h-1c0 1 1 1 0 2h0l-1 1h0v1 3h0c0 1 0 3-1 4h1c0 2 1 14 0 15v1c-2 2 0 7-1 9 0 1-1 3 0 4v23h-1v-2-6-37-57-12c1-3 0-6 1-8 0 0 0 1 1 2v-31z" class="k"></path><defs><linearGradient id="CU" x1="251.608" y1="231.151" x2="262.597" y2="244.69" xlink:href="#B"><stop offset="0" stop-color="#c4c3c3"></stop><stop offset="1" stop-color="#f7f6f6"></stop></linearGradient></defs><path fill="url(#CU)" d="M267 235v1 3s0 1-1 1v1 1h0c-1 1-2 1-2 2h0-1c0 1 0 1-1 1h0l-1 1h-1 0-1l-1 1c-1 0-3 0-4-1-2 0-4-4-5-5-1-3-1-5 0-7s3-5 6-5h1c2 0 3-1 5 0 1 1 2 2 3 2v1c2 1 2 2 3 3z"></path><path d="M256 229c2 0 3-1 5 0 1 1 2 2 3 2-1 1-1 1-2 1l-1-1h-1c-1 1-2 1-2 2h-3 0c0-2 1-2 2-3h0c0 1-1 1-1 2h1c1 0 2 0 2-1v-1-1h-3z" class="k"></path><path d="M264 232c2 1 2 2 3 3l-1 1h0c-1-1-1-1-1-2-1 0-2 1-2 1s-1-1-2-1l-4 4h2l-2 2c-1-1-1-1 0-2v-1l5-5 2 1v-1z" class="l"></path><path d="M258 233c0-1 1-1 2-2h1l1 1c1 0 1 0 2-1v1 1l-2-1-5 5h-1l-1-1h-1c0-2 1-2 1-3h3z" class="Z"></path><path d="M258 233h0c-1 2-2 2-3 3h-1c0-2 1-2 1-3h3z" class="b"></path><path d="M377 447v-2h1v25h0v57 6c-1 0-1-9-1-10v-1-4l-1 1-1-1-1-3h-1l-3-6c1-2 0-4 0-6v-1l-1-1h0c0-2 1-3 2-4l3-3c1-3 1-5 1-7v-1h1c0-1 0-3 1-5 0-1-1-2-1-3v-15l1-16z" class="K"></path><path d="M374 495c1-1 1-2 2-3v6-1h-1l-1 2-1 2h-1l-2 1-1-1h0c0-2 1-3 2-4l1 1 1-1c0-1 1-1 1-2z" class="P"></path><path d="M371 497l1 1 1-1c0-1 1-1 1-2v2l-3 3-1 1h-1c0-2 1-3 2-4z" class="G"></path><path d="M375 497h1v1 18c0-1-1-1-1-2 0-3-2-8-1-10s1-4 1-7z" class="C"></path><path d="M375 497c0 3 0 5-1 7s1 7 1 10c0 1 1 1 1 2l1 2-1 1-1-1-1-3h-1l-3-6c1-2 0-4 0-6v-1l2-1h1l1-2 1-2z" class="D"></path><path d="M370 503c0 1 1 2 1 3s1 1 1 2c1 2 3 5 2 7h0-1l-3-6c1-2 0-4 0-6z" class="I"></path><path d="M384 576l-1-1v-52-9-6-2s-1 0-1-1c1-1 1-4 1-5-1-1-1-5-1-6 1-2 1-23 1-27V364c-1-1-1 0-1-1v-29c1-3 1-6 1-9v-18-8c-1-2-1-5-1-7l1-16-1-24c0-4 0-8 1-11 0-1 3-1 4-1 2-1 4 0 6 0 4 0 9-1 13 0l-23 1 1 176c0 1-1 3 0 5v93 3 43 10 5z" class="Z"></path><defs><linearGradient id="CV" x1="454.814" y1="230.744" x2="443.003" y2="244.079" xlink:href="#B"><stop offset="0" stop-color="#c3c2c2"></stop><stop offset="1" stop-color="#eaeaea"></stop></linearGradient></defs><path fill="url(#CV)" d="M447 228c3 0 6 0 8 2 1 1 3 4 3 6h1c0 2 0 3-1 5-2 4-3 5-7 7h-3c-2-1-4-1-5-3l-1-1v-1c-1 0 0 0-1-1h0v-1l-1-1v-1c0-2-1-3 0-5v-1c2-2 4-4 7-5z"></path><path d="M451 241h0v2c1 0 2-1 3 0h0-3c0 1-1 1-2 1v-1c1 0 1-1 2-1v-1z" class="b"></path><path d="M324 319v-1c1 0 0-2 1-2 0-3-1-6 0-8v84c-1 0-2-1-3-3l-3-2c1-1 1-1 0-2-2-2-3-4-6-5h0c0-2 1-3 2-4l1-2c2-3 4-6 5-10l1-4c1-6 0-15-1-21v-1l1-1h0c0 1 0 2 1 2v-2c0-1-1-1-1-2v-14-2h1l1 1c-1 0-1 0-1 1h1 0v-2z" class="f"></path><path d="M316 374c2-3 4-6 5-10l1-4c1 5 1 9 0 13v16l-3-2c1-1 1-1 0-2-2-2-3-4-6-5h0c0-2 1-3 2-4l1-2z" class="e"></path><defs><linearGradient id="CW" x1="316.244" y1="381.779" x2="314.888" y2="376.514" xlink:href="#B"><stop offset="0" stop-color="#7b797b"></stop><stop offset="1" stop-color="#939292"></stop></linearGradient></defs><path fill="url(#CW)" d="M315 376l1-2v1h1 1c1 1 1 3 1 4v3 3c-2-2-3-4-6-5h0c0-2 1-3 2-4z"></path><path d="M457 254h1c0-1 1-1 2-1l1 1-2 2-1 1c-1 0-1 1-2 1l-1 1h-1-5l-1-1v1c1 1 1 2 2 3h1v-1s1-1 2-1h0c0 1-1 1-1 2h3v-1c1 0 2 0 3-1h0c3-2 5-5 7-7-1 1-3 4-3 4v2 1 2l3 7c1 1 1 2 1 3l1 6v4h1l-1 6-1 1v-1c-1 0-2 1-3 1l-1 3h0-1v1 1c-2-8-4-15-7-22-2-5-5-10-7-15l2 1h1c3 1 5 0 8-2l1-1v-1h-2z" class="N"></path><path d="M462 257v2 1 2l3 7-4 4v-1-4-1l-1 1h0v6c-1-1-1-2-2-3l-3-8c0-1 2-1 3-2s3-2 4-4z" class="B"></path><path d="M460 274v-6h0l1-1v1 4 1l4-4c1 1 1 2 1 3l1 6v4h1l-1 6-1 1v-1c-1 0-2 1-3 1h0c-1 1-1 1-2 1 0-2 0-5-1-7 0-3-1-7 0-9z" class="j"></path><path d="M467 282h1l-1 6-1 1v-1c-1 0-2 1-3 1h0c1-2 4-4 4-7zM348 158l2 2h0l2 4h1l1 1 1-1 2-1c1-2 1-4 3-4h0 1c2 4 5 7 8 11v1c0 2-1 4-1 6 0 3 1 6 1 8 0 3 0 6 1 9 1 1 1 2 1 3 1 1 1 3 1 4l1 2c-1 0-1 0-2 1h-1c-1-1-2-1-3-1l1-1v-1l-2-1c-1-1-3-1-4-2-2 0-2-1-4 0l-1 2-5 4 2 2h-14-16c1 0 3 0 4-1l5-5c4-5 6-13 6-19v-6l-1-4h1c3-2 5-6 7-9 1-1 2-2 2-4z" class="i"></path><path d="M357 163c1-2 1-4 3-4 0 2 0 3-1 5l-1 1h0c-1 0 0-1-1-2z" class="G"></path><path d="M362 176h1c1 2 1 3 1 6v4 1c-1 0-1 1-1 1v-1-3l-1-1v-7z" class="L"></path><path d="M360 165c2 1 4 2 5 3v3l-1 1h0v4h-1-1-1l-1-3h1c1 0 1 1 2 2 0-2 0-3-1-4v-1h0c0-2-1-3-2-5z" class="G"></path><path d="M366 200c1-1 1-1 2-1v-2h2 1c1 1 1 3 1 4l1 2c-1 0-1 0-2 1h-1c-1-1-2-1-3-1l1-1v-1l-2-1z" class="c"></path><path d="M371 204l-1-3v-1l2 1 1 2c-1 0-1 0-2 1z" class="j"></path><path d="M357 163c1 1 0 2 1 2h2c1 2 2 3 2 5h0v1c1 1 1 2 1 4-1-1-1-2-2-2h-1c-3-1-5-2-8-2s-4 2-6 4c-1-4 3-6 4-10v-1h2 1l1 1 1-1 2-1z" class="d"></path><path d="M353 164l1 1 1-1 1 1 1 1c-1 1-1 1-2 1h-3 0c-1-1-1-1 0-2l1-1z" class="C"></path><path d="M353 164l1 1v1c-1 1-1 1-2 1-1-1-1-1 0-2l1-1z" class="D"></path><defs><linearGradient id="CX" x1="350.611" y1="186.194" x2="356.227" y2="172.066" xlink:href="#B"><stop offset="0" stop-color="#c0bfc0"></stop><stop offset="1" stop-color="#e5e4e5"></stop></linearGradient></defs><path fill="url(#CX)" d="M346 175c2-2 3-4 6-4s5 1 8 2l1 3h1v7l-1 2c0 1-1 1-2 2-1 2-3 3-5 2-4 0-6-2-8-5l-1-2v-5h0l1-2z"></path><path d="M352 174c1 0 2 0 4 1 1 0 2 1 3 3 0 1 0 3-1 4-1 2-2 2-3 3h-3c-1 0-2-1-3-3-1-1-1-3 0-4 0-2 1-3 3-4z" class="B"></path><defs><linearGradient id="CY" x1="356.024" y1="189.075" x2="352.538" y2="203.232" xlink:href="#B"><stop offset="0" stop-color="#545353"></stop><stop offset="1" stop-color="#767676"></stop></linearGradient></defs><path fill="url(#CY)" d="M344 185c0-1 0-2 1-3l1 2c2 3 4 5 8 5 2 1 4 0 5-2 1-1 2-1 2-2l1-2 1 1v3 1 3l1 1c0 1 0 2 1 2v1h1v1l1 1h0-1c-2-1-3-1-4-1v1h0v1c-2 0-2-1-4 0l-1 2-5 4c-4-2-7-6-10-10 2-3 2-5 2-9z"></path><path d="M361 185h1v2c0 2-3 3-4 4-5 1-8 0-12-2 0-1-1-3-1-4l1-1c2 3 4 5 8 5 2 1 4 0 5-2 1-1 2-1 2-2z" class="F"></path><defs><linearGradient id="CZ" x1="349.073" y1="181.333" x2="325.582" y2="199.445" xlink:href="#B"><stop offset="0" stop-color="#767577"></stop><stop offset="1" stop-color="#9e9c9c"></stop></linearGradient></defs><path fill="url(#CZ)" d="M348 158l2 2h0l2 4h-2v1c-1 4-5 6-4 10l-1 2h0v5c-1 1-1 2-1 3 0 4 0 6-2 9 3 4 6 8 10 10l2 2h-14-16c1 0 3 0 4-1l5-5c4-5 6-13 6-19v-6l-1-4h1c3-2 5-6 7-9 1-1 2-2 2-4z"></path><path d="M344 185v-5c-1-1 0-3 1-4v1 5c-1 1-1 2-1 3z" class="a"></path><path d="M348 158l2 2h0c-1 0-1 1-1 1v1h-1v3s0 1-1 1c-2 1-3 4-4 6 0 1-1 3-2 4 0 0-1 0-1 1 0 0 0 3-1 4v-6l-1-4h1c3-2 5-6 7-9 1-1 2-2 2-4z" class="R"></path><path d="M346 162c1 1 1 1 1 2s0 1-1 1c-2 2-3 5-5 6s-2 3-2 4l-1-4h1c3-2 5-6 7-9z" class="g"></path><path d="M342 194c3 4 6 8 10 10l2 2h-14 0l-1-1c-2 1-4 1-5 1l8-9-1-1 1-2z" class="E"></path><path d="M339 205c0-1 1-2 2-3 2 0 2 2 4 2h1v1c1-1 1-1 1-2l2 3h-9l-1-1z" class="L"></path><path d="M342 194c3 4 6 8 10 10l2 2h-14 0 9l-2-3-5-6-1-1 1-2z" class="k"></path><path d="M355 288v-71c9 1 20 0 30 0h55 9c1 1 2 0 4 1h-3c-1 1-7 0-9 0-1-1-12 0-15 0-1-1-21-1-24-1l1 1c1 0 1 1 2 1 0 0 0 1 1 1s1 0 2 1c1 0 1 1 1 2h0c1 0 1 0 2 1h0c1 1 1 0 1 1l2 2 1 1h1c1 1 3 2 4 3v1h1l2-2c1-1 2-2 3-2l3-3h1 0c-1 1-2 3-3 3s-1 0-1 1l-3 3s1 0 1 1h1l1 2c-1 0-1 0-1-1h0-1-1c-1 0-2 1-3 0h0c-1 0 0 0-1-1h-1c-1-1-1-2-2-2-2-1-3-2-4-3h-1c0-1 0-1-1-1h-1-2-1s-1 0-1 1h-1l-1 1h-1l-2 2h0c-1 0-1 1-1 1h6c2 1 4 0 5 1h3c1 0 2 0 3 1h1 0c0 1-1 0-2 0h-4c-2-1-5 0-7 0-1-1-8-1-10-1-1 0-4 1-5 0h-1c1-1 1-1 1-2v1h2c0-1 2-1 3-2a30.44 30.44 0 0 0 8-8c-1 0-1 0-2 1 0 1 0 1-1 1-1 1-1 0-1 1h-1l-3 2h-4-1s-1 0-2 1h-1c0 1-1 2-2 2l-3 2c-1 1-1 1-2 1-1 1-2 2-4 3h0-1 0c-1 1-1 0-2 0 0 1 0 1 1 2h0c0 1 1 3 2 3s1 0 2 1v-1c1 3 2 4 1 7l-2-4-1 1c1 2 1 4 0 6l-1 2v2-1c1 1 0 2 0 3-1 1-1 1-2 1v1c-2 0-3 1-4 0-2 0-3-1-4-2v-1c-1 0-1-1-1-1h-1l-1 3v-1l-3-3c1 3 2 6 2 8 0 3-1 6-1 8v10c0 2 0 4 1 7 0 4 0 8 1 12 0 1 0 2-1 2h0l-2-3v-1c-1 0-2-1-2-2v-1h-1v-6h0z" class="b"></path><path d="M424 233h-1 0c-1 1-1 1-2 1v-1c0-1 1-1 2-1 0 0 1 0 1 1z" class="k"></path><path d="M374 253v2-1c1 1 0 2 0 3-1 1-1 1-2 1v1c-2 0-3 1-4 0-2 0-3-1-4-2 1 0 0-1 1 0 1 0 2 1 2 1h4l3-5z" class="P"></path><path d="M404 222h0c-1 3-3 5-5 7s-4 4-7 4v-1l12-10z" class="C"></path><path d="M375 245c0-1-1-2-2-3h-2l-1-1c-1-1-3-1-4-1v-4s1-1 1-2 0-2-1-3l-5-8c-1-2-2-3-2-4h3l6 3 10 4c2 1 4 1 5 2h2 0c-1 2-4 3-6 5-1 1-2 2-4 3h0-1 0c-1 1-1 0-2 0 0 1 0 1 1 2h0c0 1 1 3 2 3s1 0 2 1v-1c1 3 2 4 1 7l-2-4-1 1z" class="Z"></path><path d="M383 228h2 0c-1 2-4 3-6 5-1 1-2 2-4 3h0-1 0c-1 1-1 0-2 0 1-1 3-2 4-3l7-5z" class="Y"></path><defs><linearGradient id="Ca" x1="383.735" y1="344.888" x2="362.678" y2="343.474" xlink:href="#B"><stop offset="0" stop-color="#939292"></stop><stop offset="1" stop-color="#cac9ca"></stop></linearGradient></defs><path fill="url(#Ca)" d="M375 245l1-1 2 4v26 22 11 5c1 7 0 13 0 19 1 2 2 8 1 9v-2 7 1c0 1 0 3 1 4v3h0c0 1 0 5-1 6v1 2h0v3c0 2 0 7 1 8 0 1 0 1-1 2h0 1v1 10 1l-1-1v12 5c0 1-1 5 0 6v1 1 2l1-1c1 2-1 7 1 9 0 1-1 5 0 5v7c-1-1-1-3-1-4v-8 31c-1-1-1-2-1-2-1 2 0 5-1 8v12h0v-25h-1v2-4l-1-3v-8c0-4 1-9 0-13h-1 0l-1 1c1-1 1-2 1-3h0v-1c0-3-1-5-3-7-1-1-3-1-4-2 1-1 1-1 1-2 2-2 4-4 5-6v-1l-7-9c-3-5-5-11-6-17 1 1 1 2 1 3h0 1c1-1 1-3 1-5 1-1 1-1 1-2h0c-1-4 0-8-1-11-2-2-2-8-3-10 0-2-1-3 0-4 1 2 3 5 4 7l9 16h0l1 1c0 1 0 2 1 2v-2-2-6-15-9c0-1 0-2-1-4s-2-3-4-4h-1c-2-2-1-4-1-6 0-1 0-2-1-2 0-2 0-4-1-5v-2l-1-2-6-9c1 0 1-1 1-2-1-4-1-8-1-12-1-3-1-5-1-7v-10c0-2 1-5 1-8 0-2-1-5-2-8l3 3v1l1-3h1s0 1 1 1v1c1 1 2 2 4 2 1 1 2 0 4 0v-1c1 0 1 0 2-1 0-1 1-2 0-3v1-2l1-2c1-2 1-4 0-6z"></path><path d="M372 259v-1c1 0 1 0 2-1 1 2-1 4 0 5-1 0-1 0-1 1h-1v-2-2z" class="D"></path><path d="M367 306c0 1 0 2 1 3 0 1 1 1 1 2 0 2 2 3 2 5l-4-4-1-2 1-1c-1-1 0-2 0-3h0z" class="P"></path><path d="M373 374c1 1 1 1 1 3v1l1 1v7 1 4c1 1 0 2 0 2 0 2 0 3 1 4h0v3h0c-1 0-1-1-2-1v-1-1c1-4 1-10 0-14 0-3 0-6-1-9z" class="C"></path><path d="M364 357c-2-2-2-8-3-10 0-2-1-3 0-4 1 2 3 5 4 7v1-1l-1-1c-1-1-1-2-1-2v3 1l1 1v-1l1 1v3 4c-1-1-1-1-1-2z" class="I"></path><path d="M367 312l4 4c1 2 2 4 2 6-1 1-2 4-3 5h1-1c-2-2-1-4-1-6 0-1 0-2-1-2 0-2 0-4-1-5v-2zm6-49c1 0 1 1 1 2 1 3 0 5 1 9h0v-6h1v11 4c-1-2-2-3-3-5 1-3-2-8-3-11l2-2c0-1 1-2 1-2z" class="N"></path><path d="M378 414c1 3 2 5 2 7v31c-1-1-1-2-1-2-1 2 0 5-1 8v-12-32z" class="I"></path><path d="M367 389c-1-1-1-3 0-4v-1c-1 0-1 0-1-1h0v-6-2-4l1-2v-3c0-1 1-1 2-3 1 1 1 3 1 4v3c1 0 2 0 2 1l-2 2v1c0 5-3 9-2 14h0c0 3 3 6 5 8 0-1 0-2 1-3v-5-5c1 4 1 10 0 14v1l-7-9z" class="N"></path><path d="M370 367v3c1 0 2 0 2 1l-2 2c-1-1 0-4 0-6z" class="I"></path><path d="M361 258l1-3h1s0 1 1 1v1c1 1 2 2 4 2 1 1 2 0 4 0v2 2h1c0-1 0-1 1-1l-1 1s-1 1-1 2l-2 2c1 3 4 8 3 11l-1-2-1-1-3-5-7-12z" class="j"></path><path d="M368 263c-1 0-1 0 0-1h1c1 0 1 0 3-1v2h1c0-1 0-1 1-1l-1 1s-1 1-1 2l-2 2-2-4z" class="I"></path><path d="M368 263h1c1 0 1 1 2 1l1 1-2 2-2-4z" class="C"></path><path d="M370 373l2-2 1 3c1 3 1 6 1 9v5 5c-1 1-1 2-1 3-2-2-5-5-5-8h0c-1-5 2-9 2-14v-1z" class="Y"></path><path d="M371 384l1-1 1 1-3 3v1l-1-1 2-3zm7 30v-64c0-12-1-25 0-38 1 7 0 13 0 19 1 2 2 8 1 9v-2 7 1c0 1 0 3 1 4v3h0c0 1 0 5-1 6v1 2h0v3c0 2 0 7 1 8 0 1 0 1-1 2h0 1v1 10 1l-1-1v12 5c0 1-1 5 0 6v1 1 2l1-1c1 2-1 7 1 9 0 1-1 5 0 5v7c-1-1-1-3-1-4v-8c0-2-1-4-2-7z" class="N"></path><path d="M368 301s2-13 2-15c2 5 4 9 4 13 0 7 1 14 1 20-2-3-4-6-5-9h0l-1-1-1-2c-1-1 0-4 0-5v-1z" class="b"></path><path d="M360 287c-1-3-1-5-1-7v-10c0-2 1-5 1-8 0-2-1-5-2-8l3 3v1l7 12 3 5 1 1c0 1 0 1 1 2v2h0v1c3 3 3 7 3 11 0 2 0 5-1 7h-1c0-4-2-8-4-13 0 2-2 15-2 15v1l-1 4h0 0c0 1-1 2 0 3l-1 1-6-9c1 0 1-1 1-2-1-4-1-8-1-12z" class="I"></path><path d="M361 278l-1-10h1v1h1c2 3 1 5 2 7v3c0 3-1 5 0 7v1c0 1 0 2 1 3s1 2 2 3c-1 1-1 1-2 1-1-1-2-2-3-4-2-3-1-8-1-12z" class="b"></path><defs><linearGradient id="Cb" x1="359.679" y1="296.932" x2="365.047" y2="294.767" xlink:href="#B"><stop offset="0" stop-color="#959495"></stop><stop offset="1" stop-color="#b3b1b1"></stop></linearGradient></defs><path fill="url(#Cb)" d="M360 287v-10h0l1 1c0 4-1 9 1 12 1 4 3 7 3 10 1 2 0 3 1 5 1-2 1-3 1-4v-1l1 1v1l-1 4h0 0c0 1-1 2 0 3l-1 1-6-9c1 0 1-1 1-2-1-4-1-8-1-12z"></path><path d="M335 169v1l-2 2 1 1 4-2 1 4v6c0 6-2 14-6 19l-5 5c-1 1-3 1-4 1h-4-9-5-3-18v-6s0-1-1-2c-1 2 0 5 0 8h-5 0-2-8-7-5-15-4-11c-1 0-4 0-5 1 1-3 2-6 4-9 5-9 11-18 19-25 0 2 1 4 1 6 0 3-2 5-2 8 2 0 4 0 6 1 4 2 6 5 11 6 7 2 18 2 25-1 3-1 5-2 7-3l-1-1v-1c-2 0-4 1-6-1h1c3-1 5-3 8-5h1c3 1 7 0 9 0 4 0 7-1 10-2 4-1 7-2 11-4 1-1 2-2 4-3s3-2 5-4z" class="B"></path><path d="M277 200v-2h1v7l1 1h0-2v-6z" class="H"></path><path d="M328 188v-2c1-1 5-3 7-4v1c0 1 1 1 0 2v-1c-2 1-3 1-5 2h0c0 1-1 1-2 2h0z" class="F"></path><path d="M335 169v1l-2 2 1 1c-2 1-4 4-6 4-1 0-2-1-2-1 1-1 2-2 4-3s3-2 5-4z" class="g"></path><path d="M242 205h5l3-3c2 0 4 0 5 1l1 2h1v1h-15v-1z" class="F"></path><path d="M295 182h1c1 2 1 3 0 5l-3 3-1-1v-1c-2 0-4 1-6-1h1c3-1 5-3 8-5z" class="D"></path><path d="M328 188h0c0 4-1 7-2 10 0 2-2 5-2 7h2c2 0 2-1 3-1l2-3c1 0 1-1 2-1l-5 5c-1 1-3 1-4 1h-4c2-4 4-7 5-11v2c2-2 2-5 2-6s1-2 1-3z" class="V"></path><path d="M330 186h0c2-1 3-1 5-2v1c0 3 0 5-1 7s-2 3-2 5c-1 2-2 3-3 5v-1h0c0-1 0-1 1-2 1-3 4-5 3-8-1 0-1 1-3 1 0 0-1-1-1-2 1-1 1-2 2-3h-1l1-1h0-1z" class="Q"></path><path d="M241 196c1-1 1-2 2-2h3v1c-1 2-5 8-4 10v1h-4c0-2 1-5 1-7 1-1 2-2 2-3z" class="R"></path><path d="M227 206c3-1 7-1 10-1-1-1-3-4-2-6h1c0-1 0-1-1-2v-1c0-1 1-2 2-3 0 0 1 0 2-1h0c1 0 1 0 2 1v2h-1l1 1c0 1-1 2-2 3 0 2-1 5-1 7h-11zm83-17h0l-1-1c1-1 2-2 4-2v-1h4c2 0 2-1 3-2l1-1c2 0 2-1 3-2l1-1c1 2 1 4 1 6h0c-1 1-2 1-3 1-1-1-1-1-2-1-1 1-1 4-2 5 0 2-1 4-1 6-1-1 0-3 0-4s0-3-1-5c-1 0-1 0-2 1h-2c-2 0-2 0-3 1z" class="V"></path><path d="M258 203c1 0 1 0 1-1 1-1 3-2 4-2 5-1 8 0 12 2l1 3h0c0-2 0-3 1-5h0v6h-8-7-5v-1l1-2z" class="e"></path><path d="M258 203h3l1-1h3c-2 1-3 2-3 4h-5v-1l1-2z" class="D"></path><path d="M265 202c1 0 2-1 4 0 1 1 1 1 1 3l-1 1h-7c0-2 1-3 3-4z" class="B"></path><path d="M310 189c1-1 1-1 3-1h2c1-1 1-1 2-1 1 2 1 4 1 5s-1 3 0 4c0 2-2 6-4 7l-3 3h-5c2-2 3-4 5-6 1-2 3-6 2-8 0-2-2-3-3-3z" class="C"></path><path d="M318 196c0-2 1-4 1-6 1-1 1-4 2-5 1 0 1 0 2 1 1 0 2 0 3-1h0c0 3 0 7-1 10-1 4-3 7-5 11h-9l3-3c2-1 4-5 4-7z" class="k"></path><defs><linearGradient id="Cc" x1="287.67" y1="206.878" x2="301.926" y2="197.259" xlink:href="#B"><stop offset="0" stop-color="#1d1c1d"></stop><stop offset="1" stop-color="#393839"></stop></linearGradient></defs><path fill="url(#Cc)" d="M295 195c2-1 5-1 7 1 1 1 1 2 2 2-1 2-1 1-1 3l1 1c-1 1-1 1-1 2v1 1h-18v-6 1c1 0 2 0 2-1 1 0 2-1 2-1 2-2 3-3 6-4z"></path><path d="M285 201c1 0 2 0 2-1 1 0 2-1 2-1 2-2 3-3 6-4-1 1-1 3-3 4h-1 0c-2 1-3 2-5 2h-1z" class="J"></path><path d="M295 201h3c0 1 1 1 1 2s0 1-1 2h-3v-1c0-1-1-2 0-3zm23 250l2-2c1 0 3 1 5 1v127c0 2-3 6-5 7l-10 10-4 3-12 11c0-2 2-3 3-5 0-1 1-3 1-5 3-11 2-21 3-32 0-9-1-18-3-27l-2-11-3-12-2-10-2-2c-1-1 0-2 0-3s-1-2-1-2l2-3v-1-3c1-1 1-1 2-1l1 1 3-3 6-5v-1l1 1 2-2c-1-4-2-7-2-10 3-10 8-15 15-21z" class="B"></path><path d="M305 482h2 0l-1 4c-1-1-1-2-1-4z" class="O"></path><path d="M320 527l1-1c1 1 0 2 1 3 0 0 1 0 1 1v2c-1 0-1 1-1 1l-1-1c-1-1-1-3-1-4v-1z" class="S"></path><path d="M309 491l6 6-3 2c0-1 0-2-1-3l-1-1c1-1 0-1-1-2v-2zm7 61c0 1 0 1 1 1l-1 9c-1-1 0-2-1-3h-1l-1-1 1-1c1-1 1-4 2-5z" class="R"></path><path d="M309 464l3 3h0c-2 1-2 3-3 4v2c-1-1-3-2-3-4 0-1 2-4 3-5z" class="M"></path><path d="M309 464c1-1 1-2 2-2 3 1 4 4 4 7 1 2 3 4 3 7 0 1 1 1 1 2-1 0-2-2-2-3l-5-8h0l-3-3z" class="F"></path><defs><linearGradient id="Cd" x1="309.229" y1="489.796" x2="302.138" y2="486.961" xlink:href="#B"><stop offset="0" stop-color="#656464"></stop><stop offset="1" stop-color="#7b7b7c"></stop></linearGradient></defs><path fill="url(#Cd)" d="M305 482h0c0 2 0 3 1 4 1 2 2 3 3 4v1 2c1 1 2 1 1 2h0c0-1-1-2-2-2-1-1-1-1-2 0-2-3-3-5-4-8v-1-1l1 1 2-2z"></path><path d="M322 533s0-1 1-1c0 0 0 1-1 1 0 1 1 2 1 3s-1 8-1 9c-1 1-2 1-2 1h0v-2c0-1 0-1-1-1 0 0-1 0-2 1v-1c0-3 1-4 3-7l1-1 1-2z" class="M"></path><path d="M309 473v-2c1-1 1-3 3-4l5 8c0 1 1 3 2 3 1 2 3 6 3 8 0-1-1-2-1-3h0c-1-1-1-1-1-2l-1 2c-1 0-2-1-3-2v1c1 2 1 4 2 5s1 2 1 3l-6-12-3-3-1-2z" class="h"></path><path d="M310 475l1-3 1-1c3 2 5 7 6 10-2-1-3-3-5-3l-3-3z" class="L"></path><path d="M315 516l4-5c2 0 4 1 5 3 0 4-4 7-7 10l-3 3v-1h-1c1-2 2-3 3-5h-1c0-1 2-2 2-2h-2c0-1-1-1-1-1h0l-1-1 2-1z" class="c"></path><path d="M313 517l2-1c2 1 3 0 5 0l-3 3h-2c0-1-1-1-1-1h0l-1-1z" class="K"></path><path d="M320 516h1c-1 3-3 4-4 5v3l-3 3v-1h-1c1-2 2-3 3-5h-1c0-1 2-2 2-2l3-3z" class="C"></path><defs><linearGradient id="Ce" x1="313.428" y1="518.38" x2="308.183" y2="529.851" xlink:href="#B"><stop offset="0" stop-color="#959494"></stop><stop offset="1" stop-color="#c7c6c5"></stop></linearGradient></defs><path fill="url(#Ce)" d="M314 518h0s1 0 1 1h2s-2 1-2 2h1c-1 2-2 3-3 5h1v1l-1 2c-2 2-4 5-6 8-1-1-1-3-1-4v-3c1-4 3-8 6-12h2z"></path><path d="M315 521h1c-1 2-2 3-3 5h1v1l-1 2-1-2h-2c1-2 4-4 5-6z" class="N"></path><path d="M310 527h2l1 2c-2 2-4 5-6 8-1-1-1-3-1-4l2-1c1-2 1-3 2-5z" class="Y"></path><defs><linearGradient id="Cf" x1="314.802" y1="529.911" x2="315.029" y2="542.276" xlink:href="#B"><stop offset="0" stop-color="#605e5f"></stop><stop offset="1" stop-color="#818080"></stop></linearGradient></defs><path fill="url(#Cf)" d="M316 530c1-1 2-2 4-3v1c0 1 0 3 1 4l1 1-1 2-1 1h-3c0 1-1 2-2 4l-3 3-3 3h1l-1-2v-2c0-4 1-5 3-7h0c1-1 2-2 4-3v-2z"></path><path d="M310 546v-3l1-1c0 1 0 1 1 1h0l-3 3h1z" class="W"></path><path d="M316 532c-1 3-2 5-4 7h0v-4h0c1-1 2-2 4-3z" class="J"></path><path d="M316 530c1-1 2-2 4-3v1c0 1 0 3 1 4l1 1-1 2-1-1-1 1h-1l2-2c0-1 0-2-1-3h-3z" class="L"></path><path d="M312 535v4h0v1c-1 1-1 1-3 1v3-2c0-4 1-5 3-7z" class="X"></path><path d="M312 556h1l1 1-1 1 1 1h1c1 1 0 2 1 3h-1c1 2 0 3 1 4h1 0c1-1 2-1 3-2v1c-1 0-2 2-3 3s-1 3-1 5c-2 3-3 7-5 10l-3 3h-1c0-1 0-1-1-1 2-3 2-6 4-9v-7l-1-2s1-1 1-2l1-6 1-3z" class="J"></path><defs><linearGradient id="Cg" x1="314.013" y1="563.7" x2="310.046" y2="570.61" xlink:href="#B"><stop offset="0" stop-color="#919091"></stop><stop offset="1" stop-color="#acaaab"></stop></linearGradient></defs><path fill="url(#Cg)" d="M312 556h1l1 1-1 1 1 1h1c1 1 0 2 1 3h-1l-1 4-3 8c0 1 0 1-1 2v-7l-1-2s1-1 1-2l1-6 1-3z"></path><path d="M312 565c0-2 1-5 1-7l1 1h1c1 1 0 2 1 3h-1l-1 4v-3h-1c0 1 0 2-1 2z" class="G"></path><path d="M312 556h1l1 1-1 1c0 2-1 5-1 7-1 1-1 3-2 4l-1-2s1-1 1-2l1-6 1-3z" class="C"></path><path d="M315 540c1-2 2-3 2-4h3c-2 3-3 4-3 7v1c1-1 2-1 2-1 1 0 1 0 1 1v2c-1 1-3 3-2 4 0 1 0 2-1 3-1 0-1 0-1-1-1 1-1 4-2 5l-1-1h-1l-1 3h0v-3l-1-1-4 5h0c-1-3 1-7 2-10 0-1 1-2 1-4l3-3 3-3z" class="S"></path><path d="M312 556c1-1 2-3 2-5 1-1 2-3 3-3 0 1-1 3-1 4-1 1-1 4-2 5l-1-1h-1z" class="P"></path><path d="M315 540c1-2 2-3 2-4h3c-2 3-3 4-3 7-1 1-1 1-1 2l1 1c-1 2-3 3-4 4s-1 2-2 3c0-3 1-4 2-6v-3h0l-1-1 3-3z" class="G"></path><path d="M315 540h1c0 1-1 2-1 3v2l-2 2v-3h0l-1-1 3-3z" class="C"></path><path d="M312 543l1 1h0v3c-1 2-2 3-2 6l-1 2-4 5h0c-1-3 1-7 2-10 0-1 1-2 1-4l3-3z" class="I"></path><path d="M302 484v1c1 3 2 5 4 8 1-1 1-1 2 0 1 0 2 1 2 2h0l1 1c1 1 1 2 1 3-2 1-4 3-5 6 1 2 3 4 5 5h1 1c1 2 0 4 0 6l-1 1 1 1h-2c-3 4-5 8-6 12v1h-1c0-3 0-7-1-10-2-8-5-16-7-24l-4 3c-1 0-1 1-2 1h-1l1 5-2-2c-1-1 0-2 0-3s-1-2-1-2l2-3v-1-3c1-1 1-1 2-1l1 1 3-3 6-5z" class="B"></path><path d="M306 493c1-1 1-1 2 0 1 0 2 1 2 2h0l1 1c0 2-1 2-2 3h0l-1-1h0c0-2-1-4-2-5z" class="c"></path><defs><linearGradient id="Ch" x1="300.002" y1="492.785" x2="290.112" y2="491.639" xlink:href="#B"><stop offset="0" stop-color="#908c92"></stop><stop offset="1" stop-color="#a4a5a1"></stop></linearGradient></defs><path fill="url(#Ch)" d="M302 484v1c0 1-1 2-1 3-2 1-11 11-12 12 0 1 1 1 1 1l1 5-2-2c-1-1 0-2 0-3s-1-2-1-2l2-3v-1-3c1-1 1-1 2-1l1 1 3-3 6-5z"></path><path d="M290 492c1-1 1-1 2-1l1 1-3 4v-1-3z" class="F"></path><path d="M311 496c1 1 1 2 1 3-2 1-4 3-5 6 1 2 3 4 5 5h1 1c1 2 0 4 0 6l-1 1 1 1h-2l-3-6c-2-2-5-4-6-7 1-3 3-5 5-7l1 1h0c1-1 2-1 2-3z" class="e"></path><path d="M293 500l4-3c2 8 5 16 7 24 1 3 1 7 1 10h1v-1 3c0 1 0 3 1 4-2 1-2 2-1 4 0 3 0 6 1 9h1c-1 3-3 7-2 10h0l4-5 1 1v3h0l-1 6c0 1-1 2-1 2l1 2v7c-2 3-2 6-4 9 1 0 1 0 1 1h1l3-3c0 3-1 6-2 9 0 1 1 1 1 2l-4 3-12 11c0-2 2-3 3-5 0-1 1-3 1-5 3-11 2-21 3-32 0-9-1-18-3-27l-2-11-3-12-2-10-1-5h1c1 0 1-1 2-1z" class="R"></path><path d="M306 570l1 1s1-1 2-1c-1 1-1 2-1 2l-1 1h-1v-3z" class="a"></path><path d="M303 572h1c1 3 1 5 1 8h-1c-1 0-2 0-2-1 1-2 0-5 1-7z" class="U"></path><defs><linearGradient id="Ci" x1="312.331" y1="573.873" x2="302.797" y2="578.362" xlink:href="#B"><stop offset="0" stop-color="#b2aeaf"></stop><stop offset="1" stop-color="#e2e1e4"></stop></linearGradient></defs><path fill="url(#Ci)" d="M309 567l1 2v7c-2 3-2 6-4 9l-1 1v-2c0-2 0-4 1-5v-4c1-1 1-1 2-1v-2s0-1 1-2v-3z"></path><path d="M310 555l1 1v3h0l-1 6c0 1-1 2-1 2v3c-1 0-2 1-2 1l-1-1h0l-1-2v-1c0-2 0-5 1-7l4-5z" class="h"></path><path d="M305 568c1-1 1-3 1-4 0 0 1-1 2-1l1 1-2 5-1 1-1-2z" class="J"></path><path d="M311 559h0l-1 6c0 1-1 2-1 2v3c-1 0-2 1-2 1l-1-1h0l1-1 2-5 2-5z" class="g"></path><defs><linearGradient id="Cj" x1="304.372" y1="515.064" x2="292.026" y2="527.014" xlink:href="#B"><stop offset="0" stop-color="#242320"></stop><stop offset="1" stop-color="#3d3d42"></stop></linearGradient></defs><path fill="url(#Cj)" d="M293 500l1 3c1 2 2 5 3 8 2 6 4 13 5 19 1 3 1 6 1 9l-1 1-2-11c-1-2-1-4-2-6l-1-2v-1-1-1h-1v-2c0-1 0-1-1-2 0-1 1 0 0-1l-1 1h-1c1 1 1 1 1 2h-1l-2-10-1-5h1c1 0 1-1 2-1z"></path><path d="M293 500l1 3v1l-2-2-1-1c1 0 1-1 2-1z" class="J"></path><defs><linearGradient id="Ck" x1="292.593" y1="501.906" x2="292.802" y2="513" xlink:href="#B"><stop offset="0" stop-color="#3a393a"></stop><stop offset="1" stop-color="#5d5d5d"></stop></linearGradient></defs><path fill="url(#Ck)" d="M290 501h1l1 1h0c0 1 1 2 1 3 3 5 4 12 5 18l-1-2v-1-1-1h-1v-2c0-1 0-1-1-2 0-1 1 0 0-1l-1 1h-1c1 1 1 1 1 2h-1l-2-10-1-5z"></path><defs><linearGradient id="Cl" x1="295.649" y1="588.394" x2="305.091" y2="597.11" xlink:href="#B"><stop offset="0" stop-color="#5d5c5d"></stop><stop offset="1" stop-color="#8b8a8a"></stop></linearGradient></defs><path fill="url(#Cl)" d="M298 598l1 1h0c1-3 3-6 3-9v-4c0-2-1-4 1-5 0 1 0 1 1 2s0 1 1 1h0v2l1-1c1 0 1 0 1 1h1l3-3c0 3-1 6-2 9 0 1 1 1 1 2l-4 3-12 11c0-2 2-3 3-5 0-1 1-3 1-5z"></path><defs><linearGradient id="Cm" x1="307.426" y1="584.898" x2="307.277" y2="595.215" xlink:href="#B"><stop offset="0" stop-color="#171616"></stop><stop offset="1" stop-color="#313031"></stop></linearGradient></defs><path fill="url(#Cm)" d="M311 583c0 3-1 6-2 9 0 1 1 1 1 2l-4 3v-1c-1-2-1-3-1-5v-2-2-1l1-1c1 0 1 0 1 1h1l3-3z"></path><path d="M305 591c1 0 1 0 1 1l1 3-1 1c-1-2-1-3-1-5z" class="V"></path><defs><linearGradient id="Cn" x1="297.033" y1="543.012" x2="301.917" y2="542.211" xlink:href="#B"><stop offset="0" stop-color="#5e5d5e"></stop><stop offset="1" stop-color="#878686"></stop></linearGradient></defs><path fill="url(#Cn)" d="M293 516h1c0-1 0-1-1-2h1l1-1c1 1 0 0 0 1 1 1 1 1 1 2v2h1v1 1 1l1 2c1 2 1 4 2 6l2 11c1 4 2 8 2 12v8 1h0c0 2 0 4 1 5 0 1-1 2 0 3h0v2h0v1c0-2 0-4-1-5h-2l-1 1h0v-2c0-9-1-18-3-27l-2-11-3-12z"></path><path d="M298 369l3-2h2l6 1 6 4c1 1 1 1 1 2l-1 2c-1 1-2 2-2 4h0c3 1 4 3 6 5 1 1 1 1 0 2l3 2c1 2 2 3 3 3v18c-1 1-5 3-5 3l4 4 1 1v3 20 8 1c-2 0-4-1-5-1l-2 2c-7 6-12 11-15 21 0 3 1 6 2 10l-2 2-1-1v1l-6 5-3 3-1-1c-1 0-1 0-2 1v3 1l-2 3c0-3-1-5-2-8-1-4-3-8-5-12l-2-5h0 1c0 1 0 1 1 2v-1c0-1 1-3 0-4v-1c-1-1-1-3 0-4l3-3 3-1 1-1h0c-1-1-2-1-2-1v-1-1-1c1 0 1-1 1-2s-1-2-2-3l2-1-3-3c-1 0-5-3-6-4l-5-3-3-2c-2-3-5-6-5-9 0-5-3-9-4-14-1-2-1-4-1-6 0-6-1-13 0-20 1-4 2-7 2-12 1 2 0 4 2 6h0c1-4 0-10-1-14h1 1v-1c0 3 0 5 1 7 0-1 0-3 1-4v3 2c1 0 2 1 3 2h0l1-1-1-1c-1-2-1-3-1-5 3 4 6 7 10 11 1 0 2 2 3 2v-1l-1-1c1-1 5-2 6-2 2-1 6-4 6-6v-1-1l3-2 2-2z" class="B"></path><path d="M299 429l3 5v2h-1l-3-3 1-1v-3z" class="E"></path><path d="M289 392c2 1 4 3 6 4h0-4l-3-3 1-1z" class="P"></path><path d="M303 416c1 1 2 3 4 4h0-1c-1 1-2 3-2 4 0-1 0-3-1-5v-3z" class="V"></path><path d="M311 379l1 1v-1c0-2 1-2 3-3-1 1-2 2-2 4h0c3 1 4 3 6 5 1 1 1 1 0 2-3-3-6-6-9-7h-1-1l1-1h2z" class="k"></path><path d="M302 413l1-1 2 1c2 2 3 4 3 6l-1 1h0c-2-1-3-3-4-4l-1-3z" class="a"></path><defs><linearGradient id="Co" x1="300.332" y1="423.828" x2="295.332" y2="428.047" xlink:href="#B"><stop offset="0" stop-color="#312e31"></stop><stop offset="1" stop-color="#484949"></stop></linearGradient></defs><path fill="url(#Co)" d="M298 419l1 1h-1c0 3 0 6 1 9v3l-1 1c-1-2-2-5-2-8 0-1 0-4 2-6z"></path><path d="M298 419h1l3 3c0 1-2 4-2 5s0 2 1 3v1c1 1 1 2 1 3l-3-5c-1-3-1-6-1-9h1l-1-1z" class="V"></path><path d="M316 390h2c-6 4-11 5-17 4-1 0-2 0-3-1h5c4 0 8-2 13-3z" class="X"></path><path d="M300 469v4c0 1 1 1 2 2h0c1-1 0-2 1-3 0 3 1 6 2 10l-2 2-1-1c-2-4-3-9-2-14z" class="H"></path><path d="M287 451l2 3h0v-3c1 2 1 3 1 4-1 3 2 5 2 7h-1-4l1-1h0c-1-1-2-1-2-1v-1-1-1c1 0 1-1 1-2s-1-2-2-3l2-1z" class="E"></path><defs><linearGradient id="Cp" x1="298.415" y1="408.05" x2="303.706" y2="409.196" xlink:href="#B"><stop offset="0" stop-color="#2c2a2c"></stop><stop offset="1" stop-color="#444545"></stop></linearGradient></defs><path fill="url(#Cp)" d="M297 407c1 1 1 1 3 1v-1c0-1-1-2-1-2v-1c2 1 5 6 6 8h0l1 1h-1l-2-1-1 1c-1-1-4-2-5-3v-1-2z"></path><path d="M297 409c1 1 1 1 2 1s1 0 2 1l1 1h3l1 1h-1l-2-1-1 1c-1-1-4-2-5-3v-1z" class="L"></path><path d="M324 422l1-1v20-11l-4 3-2 1h-1c0-1 0-1-1-1-1 1-3 1-4 0h0c3-3 7-5 11-5h1c0-2 0-5-1-6z" class="U"></path><path d="M308 438h2 0v1 3l-3 4-3 6c-1 1-2 2-2 3-1-5 3-13 6-17z" class="D"></path><path d="M307 410v3h1c2-2 5-4 6-6 0-1 0-2 1-2h1 0c0 2 0 4-1 6s-4 4-5 5-2 2-2 3c0-2-1-4-3-6h1 0l1-3zm-29 10l1 4h0c1-3 2-8 4-11h1l1-2c1-1 3-1 4-1 0 2-1 5-3 6h-1c-2 3-4 7-7 11v-7z" class="J"></path><defs><linearGradient id="Cq" x1="326.654" y1="436.847" x2="321.035" y2="440.155" xlink:href="#B"><stop offset="0" stop-color="#050303"></stop><stop offset="1" stop-color="#242426"></stop></linearGradient></defs><path fill="url(#Cq)" d="M321 433l4-3v11 8c0-1-1-1-1-1-1-3-2-7-2-10-1-1-2-2-3-2l-2 1c-1-1 0-1-1-2l2-1h1l2-1z"></path><path d="M321 433v1h0v1h1v3c-1-1-2-2-3-2l-2 1c-1-1 0-1-1-2l2-1h1l2-1z" class="J"></path><path d="M321 433v1h0v1c-1 0-2 0-2-1l2-1z" class="V"></path><defs><linearGradient id="Cr" x1="315.967" y1="402.963" x2="305.257" y2="408.715" xlink:href="#B"><stop offset="0" stop-color="#191819"></stop><stop offset="1" stop-color="#393838"></stop></linearGradient></defs><path fill="url(#Cr)" d="M307 410c1-3 3-8 6-11v1 1c1 1 2 0 3 0v4h0-1c-1 0-1 1-1 2-1 2-4 4-6 6h-1v-3z"></path><path d="M313 433c1 1 3 1 4 0 1 0 1 0 1 1l-2 1c1 1 0 1 1 2l-2 3v1l-3 3h0c-1 1-3 1-4 2h-1l3-4v-3-1h0-2l5-5z" class="H"></path><path d="M312 439c1-1 3-2 4-4 1 1 0 1 1 2l-2 3c-1 0-2 0-3-1z" class="E"></path><path d="M312 439c1 1 2 1 3 1v1l-3 3h0c-1 1-3 1-4 2h-1l3-4 2-3z" class="R"></path><path d="M289 410c2-1 3-1 5-2v-1c1-1 2 0 3 0v2 1l-3 5-1 2c-4 7-7 12-6 20h-2l-4-4c-1-1-2-3-3-4v-2c3-4 5-8 7-11h1c2-1 3-4 3-6z" class="E"></path><path d="M292 415l1-2s1 1 1 2l-1 2c-1-1-1-1-1-2z" class="G"></path><path d="M293 413v-1c1-2 2-3 3-3h1v1l-3 5c0-1-1-2-1-2z" class="H"></path><path d="M285 437c0-8 3-15 7-22 0 1 0 1 1 2-4 7-7 12-6 20h-2z" class="K"></path><path d="M278 427c3-4 5-8 7-11h1c-2 5-4 10-5 16v1h0c-1-1-2-3-3-4v-2z" class="B"></path><path d="M320 413l4 4 1 1v3l-1 1c-6 3-10 5-14 10l-4 5-4 7c0-3 1-5 2-7 1-5 1-10 4-14 2-4 5-6 9-8h0l3-2z" class="G"></path><path d="M307 431h1v-1c1 1 1 2 2 2l-4 5c-1 0-1 0-1-1 1-1 1-4 2-5z" class="C"></path><path d="M317 415h1c0 1-1 2-1 3-3 2-6 3-8 6l-1-1c2-4 5-6 9-8z" class="I"></path><defs><linearGradient id="Cs" x1="325.065" y1="420.976" x2="310.113" y2="426.852" xlink:href="#B"><stop offset="0" stop-color="#888889"></stop><stop offset="1" stop-color="#a8a6a6"></stop></linearGradient></defs><path fill="url(#Cs)" d="M324 417l1 1v3l-1 1c-6 3-10 5-14 10-1 0-1-1-2-2v1h-1c0-2 1-4 3-6v-1c2-1 4-3 5-4 2-1 5-2 7-2h1l1-1z"></path><path d="M310 425h3l-5 5v1h-1c0-2 1-4 3-6z" class="k"></path><path d="M315 420l8-1c-1 1-4 3-6 4-1 0-2 1-3 2h-1-3v-1c2-1 4-3 5-4z" class="C"></path><path d="M310 424c2 0 3-1 4-2 2 0 2 0 3 1-1 0-2 1-3 2h-1-3v-1z" class="I"></path><path d="M279 474h0 1c0 1 0 1 1 2v-1c0-1 1-3 0-4v-1c-1-1-1-3 0-4v1c2 0 2 0 3-1l1 1c1 0 2 1 3 2 3 4 8 13 7 19 0 0 0 1 1 1l-3 3-1-1c-1 0-1 0-2 1v3 1l-2 3c0-3-1-5-2-8-1-4-3-8-5-12l-2-5z" class="a"></path><path d="M281 467c2 0 2 0 3-1l1 1c1 0 2 1 3 2l-2 1c1 3 2 5 2 8l5 13 2-3s0 1 1 1l-3 3-1-1c-1 0-1 0-2 1h0c0-2-1-5-2-7l-7-18z" class="V"></path><path d="M285 467c1 0 2 1 3 2l-2 1c1 3 2 5 2 8-2-4-3-7-4-11h1z" class="Q"></path><defs><linearGradient id="Ct" x1="288.902" y1="483.352" x2="280.664" y2="483.458" xlink:href="#B"><stop offset="0" stop-color="#5e5e5e"></stop><stop offset="1" stop-color="#797879"></stop></linearGradient></defs><path fill="url(#Ct)" d="M279 474h0 1c0 1 0 1 1 2v-1c0-1 1-3 0-4v-1c-1-1-1-3 0-4v1l7 18c1 2 2 5 2 7h0v3 1l-2 3c0-3-1-5-2-8-1-4-3-8-5-12l-2-5z"></path><path d="M288 485c1 2 2 5 2 7l-1-1c-1-1-1-1-1-2v-4z" class="a"></path><path d="M317 437l2-1c1 0 2 1 3 2 0 3 1 7 2 10 0 0 1 0 1 1v1c-2 0-4-1-5-1l-2 2c-7 6-12 11-15 21-1 1 0 2-1 3h0c-1-1-2-1-2-2v-4c0-4 2-8 3-12v-1c-1 0-1 0-1 1v-2c0-1 1-2 2-3l3-6h1c1-1 3-1 4-2h0l3-3v-1l2-3z" class="b"></path><path d="M312 444l1 1h0v1c-2 0-2 0-3 1l-3 5c0-2 0-4 1-6 1-1 3-1 4-2z" class="K"></path><path d="M307 446h1c-1 2-1 4-1 6l-4 5v-1c-1 0-1 0-1 1v-2c0-1 1-2 2-3l3-6z" class="i"></path><defs><linearGradient id="Cu" x1="320.898" y1="442.045" x2="314.884" y2="443.894" xlink:href="#B"><stop offset="0" stop-color="#535253"></stop><stop offset="1" stop-color="#6b6a6a"></stop></linearGradient></defs><path fill="url(#Cu)" d="M317 437l2-1c1 0 2 1 3 2 0 3 1 7 2 10 0 0 1 0 1 1v1c-2 0-4-1-5-1l-2 2c0-1-1-1-1-1v-1c1-1 1-1 2-1l1-1h-1c-1 0-2-1-3 0-2 1-4 3-6 5 0 1-1 2-1 2 0-1 3-5 4-7v-1-1h0l-1-1h0l3-3v-1l2-3z"></path><path d="M315 441h1c0 2 0 3-1 4 0 1-1 2-2 2v-1-1h0l-1-1h0l3-3z" class="T"></path><defs><linearGradient id="Cv" x1="273.447" y1="415.95" x2="299.94" y2="435.402" xlink:href="#B"><stop offset="0" stop-color="#848383"></stop><stop offset="1" stop-color="#c7c6c6"></stop></linearGradient></defs><path fill="url(#Cv)" d="M290 399c1 0 2-1 4-1-8 5-13 10-15 19l-1 3v7 2c1 1 2 3 3 4l4 4h2c1 1 2 1 2 3 0 3-1 8 0 11v3h0l-2-3-3-3c-1 0-5-3-6-4v-1h-1v-1-1h0c0-2-3-4-4-5s-1-2-2-3v-5-4c1-1 2-1 2-2 0 0 1-1 1-2l1-1v-1c2-2 1-6 2-9h1c0-1 0-1 1-2 3-2 5-5 8-7 1 0 2 0 3-1z"></path><path d="M275 435c0-1-1-1-1-2 0 0-1-1-1-2h0v-1c1-2 3-5 4-6l-1 3v3l-1 1v4z" class="H"></path><path d="M277 424l1 1-1 1c0 1 1 2 1 2v1c1 1 2 3 3 4l4 4h2c1 1 2 1 2 3 0 3-1 8 0 11v3h0l-2-3-3-3c1 0 2 1 3 1v1l1-1v1 1 1-3-2l-2-1h0l-1 1c-1-1-2-1-3-2v-1c0-1-3-3-4-4-1-2-2-4-3-5v-4l1-1v-3l1-3z" class="D"></path><path d="M298 369l3-2h2l6 1 6 4c1 1 1 1 1 2l-1 2c-2 1-3 1-3 3v1l-1-1h-2l-1 1h1c-5 4-9 6-14 8l-6 3v1l-1 1-4-5-2-3v-1l-1-1c1-1 5-2 6-2 2-1 6-4 6-6v-1-1l3-2 2-2z" class="N"></path><path d="M284 388h1s1 0 2-1h3c1 0 1 1 2 1h3l-6 3v1l-1 1-4-5z" class="k"></path><path d="M296 371v1c1 1 1 2 1 3 1 2 4 6 6 7-1 1-5 3-6 3-1-1-2-2-2-3v-2h-1l-12 4-1-1c1-1 5-2 6-2 2-1 6-4 6-6v-1-1l3-2z" class="C"></path><path d="M298 369l3-2h2l6 1 6 4c1 1 1 1 1 2l-1 2c-2 1-3 1-3 3v1l-1-1h-2l-1 1-5 2c-2-1-5-5-6-7 0-1 0-2-1-3v-1l2-2z" class="Y"></path><path d="M300 372c1 0 3 0 3 1 1 1 2 2 4 2 1 1 0 1 1 1l-1 1h1c1 1 2 1 3 2h-2c-3-2-6-4-9-7z" class="N"></path><path d="M296 371l2-2c0 2 1 2 2 3h0c3 3 6 5 9 7l-1 1-5 2c-2-1-5-5-6-7 0-1 0-2-1-3v-1z" class="J"></path><defs><linearGradient id="Cw" x1="281.241" y1="390.907" x2="262.275" y2="411.398" xlink:href="#B"><stop offset="0" stop-color="#444344"></stop><stop offset="1" stop-color="#908f90"></stop></linearGradient></defs><path fill="url(#Cw)" d="M269 372c3 4 6 7 10 11 1 0 2 2 3 2l2 3 4 5 3 3h4 0 1l-1 1h-2 1-1 1l1 1h-1c-2 0-3 1-4 1-1 1-2 1-3 1-3 2-5 5-8 7-1 1-1 1-1 2h-1c-1 3 0 7-2 9v1l-1 1c0 1-1 2-1 2 0-1 1-3 1-3-1-1-1-1-1-3l1-4-1-1v-2c0-1 1-3 1-4 0-4 0-6-1-10-1-1-1-2-1-3h-1l-2-5-1-5-1-5c1 0 2 1 3 2h0l1-1-1-1c-1-2-1-3-1-5z"></path><path d="M279 390c3 3 5 5 8 7l3 2c-1 1-2 1-3 1h0 0c-1-2-3-3-4-4l-4-5v-1z" class="U"></path><path d="M273 395v-1c1 0 0 0 1 1 2 2 3 5 2 8l-1 6-1 3-1-1v-2c0-1 1-3 1-4 0-4 0-6-1-10z" class="C"></path><path d="M274 405v4h1l-1 3-1-1v-2c0-1 1-3 1-4z" class="P"></path><path d="M267 377c1 0 2 1 3 2h-1l3 9c1 1 2 3 2 4h-1l-1-1v1h-1l-2-5-1-5-1-5z" class="h"></path><path d="M271 378c3 4 6 7 10 10 2 2 4 5 7 7 1 0 2 0 3 1h4 0 1l-1 1h-2 1-1 1l1 1h-1c-2 0-3 1-4 1l-3-2c-3-2-5-4-8-7-1-1-2-3-4-4l-5-7 1-1z" class="F"></path><path d="M269 372c3 4 6 7 10 11 1 0 2 2 3 2l2 3 4 5 3 3c-1-1-2-1-3-1-3-2-5-5-7-7-4-3-7-6-10-10l-1-1c-1-2-1-3-1-5z" class="a"></path><defs><linearGradient id="Cx" x1="273.858" y1="395.751" x2="256.241" y2="402.306" xlink:href="#B"><stop offset="0" stop-color="#545454"></stop><stop offset="1" stop-color="#908f90"></stop></linearGradient></defs><path fill="url(#Cx)" d="M265 369c0 3 0 5 1 7 0-1 0-3 1-4v3 2l1 5 1 5 2 5h1c0 1 0 2 1 3 1 4 1 6 1 10 0 1-1 3-1 4v2l1 1-1 4c0 2 0 2 1 3 0 0-1 2-1 3s-1 1-2 2v4 5c1 1 1 2 2 3s4 3 4 5h0v1 1h1v1l-5-3-3-2c-2-3-5-6-5-9 0-5-3-9-4-14-1-2-1-4-1-6 0-6-1-13 0-20 1-4 2-7 2-12 1 2 0 4 2 6h0c1-4 0-10-1-14h1 1v-1z"></path><path d="M267 383l1-1 1 5v1-1c0 1 0 2-1 2h0l-1-6z" class="d"></path><path d="M266 376c0-1 0-3 1-4v3 2l1 5-1 1c-1-3-1-5-1-7z" class="P"></path><defs><linearGradient id="Cy" x1="261.174" y1="424.374" x2="278.819" y2="439.915" xlink:href="#B"><stop offset="0" stop-color="#989798"></stop><stop offset="1" stop-color="#bbbaba"></stop></linearGradient></defs><path fill="url(#Cy)" d="M261 416h1v1 1l1 1c0 1 0 2 1 2v1-2l-1-3v-2l-1-2h0c2 5 3 10 6 15 1 2 2 3 3 5 1 1 1 2 2 3s4 3 4 5h0v1 1h1v1l-5-3-3-2c-2-3-5-6-5-9 0-5-3-9-4-14z"></path><path d="M268 389h0c1 0 1-1 1-2v1-1l2 5h1c0 1 0 2 1 3 1 4 1 6 1 10 0 1-1 3-1 4v2l1 1-1 4c0 2 0 2 1 3 0 0-1 2-1 3s-1 1-2 2v4 5c-1-2-2-3-3-5h1l-1-39z" class="H"></path><path d="M271 424v-11l1 1v-1 1l1 2h0c0 2 0 2 1 3 0 0-1 2-1 3s-1 1-2 2z" class="G"></path><path d="M271 392h1c0 1 0 2 1 3 1 4 1 6 1 10 0 1-1 3-1 4v2l1 1-1 4h0l-1-2v-1 1l-1-1c1-7 0-14 0-21z" class="V"></path><path d="M273 409v2l1 1-1 4h0l-1-2v-1c0-1 1-3 1-4z" class="d"></path><defs><linearGradient id="Cz" x1="351.035" y1="454.375" x2="414.234" y2="452.902" xlink:href="#B"><stop offset="0" stop-color="#cccbcb"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#Cz)" d="M355 288h0v6h1v1c0 1 1 2 2 2v1l2 3h0l6 9 1 2v2c1 1 1 3 1 5 1 0 1 1 1 2 0 2-1 4 1 6h1c2 1 3 2 4 4s1 3 1 4v9 15 6 2 2c-1 0-1-1-1-2l-1-1h0l-9-16c-1-2-3-5-4-7-1 1 0 2 0 4 1 2 1 8 3 10 1 3 0 7 1 11h0c0 1 0 1-1 2 0 2 0 4-1 5h-1 0c0-1 0-2-1-3 1 6 3 12 6 17l7 9v1c-1 2-3 4-5 6 0 1 0 1-1 2 1 1 3 1 4 2 2 2 3 4 3 7v1h0c0 1 0 2-1 3l1-1h0 1c1 4 0 9 0 13v8l1 3v4l-1 16v15c0 1 1 2 1 3-1 2-1 4-1 5h-1v1c0 2 0 4-1 7l-3 3c-1 1-2 2-2 4h0l1 1v1c0 2 1 4 0 6l3 6h1l1 3 1 1 1-1v4 1c0 1 0 10 1 10v-6 37 6 2c0 4 0 7 1 10 0 2 0 2 1 4h0v2h0 2c1 1 1 1 1 2h0 4l-3-3h1c-2-2-6-5-6-7l1-1h3 1v-1l18 19c2 2 5 4 7 6l1 1c2 2 4 3 6 5l3 3 1-1 1 1c-2 1-3 3-5 4-1 0-2 0-2-2v-1h-5l-42 2c-7 0-14 1-20 0 2-1 5 0 7 0l1-1V288z"></path><path d="M359 419v-1h0l3 4-1 1s-1-1-1-2 0-1-1-2z" class="C"></path><path d="M376 605l3-1c0 1 1 1 2 1l-2 1c-2 0-2 0-3-1z" class="W"></path><path d="M370 339l-2-2v-1l1-1c1 1 2 2 2 3l-1 1z" class="e"></path><path d="M370 603v-1c0-1-1-1-1-2l1-1v1l2-1v2l-2 2z" class="Z"></path><path d="M377 607h-8l7-2c1 1 1 1 3 1l-2 1z" class="P"></path><path d="M371 595v2l-1 2v-1c-2-1-2 2-4 2-1-1-1-1-1-2 1-1 2-1 3-2 0 1 0 1 1 1s1-1 2-2z" class="Z"></path><path d="M366 421c-2-2-3-4-5-6v-3l1 1 2 3c1 2 1 3 2 5h0z" class="N"></path><path d="M372 340h1v1c-1 1-2 2-4 3-1 1-1 0-2 0l-1-3h0c1 1 2 1 3 1 2 0 2-1 3-2z" class="i"></path><path d="M419 612l1-1 1 1c-2 1-3 3-5 4-1 0-2 0-2-2v-1h-5c4 0 6 0 10-1z" class="W"></path><path d="M367 587c1 0 3 0 4 1 2 1 3 1 5 1h-1c-1 1-2 1-2 2l-1 1h-1c-1-2-3-3-4-5z" class="N"></path><path d="M372 601l5-2c1 0 2 0 3 1l-3 1c-3 1-6 2-8 4l-6 3c1-1 1-2 2-3 2-1 3-2 5-2l2-2z" class="P"></path><path d="M383 603l8 3h0l-1 1h-13l2-1 2-1 2-2z" class="C"></path><path d="M382 588c1 1 1 1 1 2-2 0-3 0-4 1l-1 2c-1 1-3 2-4 3s-1 2-2 3h0l-2 1v-1h0l1-2 7-7 2-2h2z" class="P"></path><path d="M363 535c1 1 1 2 2 3 0 1 1 1 1 2l2 4-2-2h-1c-1 2-1 3-1 5l1 2-1 2v-3-2c-1-1-1 0-1-1s-1-2-1-4v-1-1c0-1 0-3 1-4z" class="k"></path><path d="M368 596l-5-7c-1-1-2-2-2-3v-1l2 3c2 3 5 5 8 7-1 1-1 2-2 2s-1 0-1-1z" class="I"></path><path d="M366 391c3 3 5 6 8 8-1 2-3 4-5 6v-2c1-2 1-3 0-5-1-3-2-4-3-7z" class="Z"></path><path d="M390 594v-1l1 1c-3 2-7 4-10 6h-1c-1-1-2-1-3-1l-5 2v-2h0c1-1 1-2 2-3 1 1 1 2 3 2 1 1 2 0 3 0h2c1 0 1 0 2-1l6-3z" class="D"></path><path d="M368 472l1 2c0 1 1 2 1 4 0 1-1 3-1 4h1c-2 1-2 2-4 4h0v-1l2-2c-1-1-2-1-2-2 0-2 0-4-1-5l1-1h1 1v-3z" class="Z"></path><path d="M369 474c0 1 1 2 1 4 0 1-1 3-1 4h-1-1c0-2 1-3 2-5v-2-1z" class="I"></path><path d="M380 600h1 2l1 1h0c1 0 2 0 2 1h0c1 1 3 1 4 2h1v1c2 0 3 1 4 2h-5l1-1h0l-8-3-2 2c-1 0-2 0-2-1h0c0-1 1-1 1-1 1-1 2-1 3-1 0 1 1 1 2 1l-2-1c-2 0-4 0-5-1h-1l3-1z" class="Y"></path><path d="M379 604c1-1 3-1 4-1l-2 2c-1 0-2 0-2-1h0z" class="G"></path><path d="M365 315c0 1 0 3-1 4v7h-1c-2-2 0-6-1-8h-1c-1-2-1-3-1-5 0-1 0-1 1-2l1 1 3 3z" class="b"></path><path d="M362 312l3 3-1 2h0l-1-1c-1-2-1-2-1-4z" class="I"></path><path d="M387 590l3 3v1c-3-2-6-2-9-1-1 1-2 1-4 2 0 1 0 1-1 1 1 1 3 1 4 1h4c-1 1-1 1-2 1h-2c-1 0-2 1-3 0-2 0-2-1-3-2 1-1 3-2 4-3l1-2c1-1 2-1 4-1h0 4z" class="Z"></path><path d="M384 597h-4c-1 0-3 0-4-1 1 0 1 0 1-1 2-1 3-1 4-2 3-1 6-1 9 1l-6 3z" class="l"></path><path d="M361 578l1-1c2 1 3 4 5 4v2l1 1 2 1c1 1 0 1 2 2 0-1 1-2 2-3 0 1 0 2-1 3h0c-1 0-1 1-2 1-1-1-3-1-4-1-2-2-3-3-4-5h-1c-1-2-1-3-1-4z" class="C"></path><path d="M367 568c0-1 0-1-1-1 0-2 0-3-1-5-2-2-3-6-4-8-1-3-3-4-3-7v-1l1-1c2 2 2 4 3 6l1 2v1c1 1 2 2 2 4 1 2 2 6 4 8l-2 2z" class="D"></path><path d="M374 575l1 1h0c0 2-1 4 0 6l-1 2c-1 1-2 2-2 3-2-1-1-1-2-2l-2-1-1-1v-2c1-1 2-1 3-2l3-2 1-2h0z" class="R"></path><path d="M367 581c1-1 2-1 3-2v2c0 1-2 2-2 3l-1-1v-2z" class="c"></path><path d="M374 575l1 1h0c0 2-1 4 0 6l-1 2c-1 1-2 2-2 3-2-1-1-1-2-2 1 0 1 0 2-1 0-1 0-2 1-3 1-2 1-2 1-4h-1l1-2h0z" class="W"></path><path d="M359 419c1 1 1 1 1 2s1 2 1 2c1 1 1 2 1 3h-1c-1 1-1 1-1 2l1 1c-1 5 0 10 0 15h0c-1 2 0 5 0 7l1 11c-1-1-1-2-2-2h0v-1-4c-1-1 0-2 0-3v-8c-1-1-1-3-1-4v-6-1-4-6-4z" class="D"></path><path d="M360 442l1 2h0c-1 2 0 5 0 7-1-3-1-6-1-9z" class="d"></path><path d="M360 442h0v-2-3c0-2-1-8 0-9l1 1c-1 5 0 10 0 15l-1-2z" class="C"></path><path d="M359 380c0-3 0-7 1-10l1-1v3c1 6 3 12 6 17l7 9v1c-3-2-5-5-8-8-2-3-5-7-7-11z" class="W"></path><path d="M372 409c2 2 3 4 3 7v1c-1 1-2 3-3 4-2 0-2 0-3 2l-3-2c-1-2-1-3-2-5 1-2 1-3 3-5 0 0 1 0 2-1l3-1z" class="l"></path><path d="M368 415h1l2 2v1c-1 1-1 1-2 1-1-1-1-2-1-4z" class="c"></path><path d="M375 417h0c0 1 0 2-1 3l1-1h0 1c1 4 0 9 0 13v8c-1 0-1-1-1-2-1-1-1-1-1-2l-2-3h0l-2-5-4-7h0l3 2c1-2 1-2 3-2 1-1 2-3 3-4z" class="D"></path><path d="M375 417h0c0 1 0 2-1 3l1-1h0c-1 3-3 5-5 7 0-2 1-4 2-5s2-3 3-4z" class="G"></path><path d="M370 428l1-1v-1c0-1 1-1 2-1l1-1c1 1 0 5 0 7-1 1-1 0-2 1v1h0l-2-5z" class="I"></path><path d="M371 566l2 1h0c1 2 2 3 3 6l-2 2h0l-1 2-3 2c-1 1-2 1-3 2-2 0-3-3-5-4l-1 1v-1c2 0 2 0 3 1v1l1-1c-1-2-1-4-1-6v1c1-1 2-4 3-5l2-2h1 1z" class="j"></path><path d="M365 578c-1-2-1-4-1-6v1c0 1 0 2 1 4h5 0c-2 1-3 2-4 3-1 0-1-1-1-2z" class="Z"></path><path d="M371 576c-2 0-2 0-4-1v-1c0-1 1-2 1-2 1 0 2 1 3 2v2z" class="H"></path><path d="M371 574c1 1 2 1 3 1l-1 2-3 2c-1 1-2 1-3 2-2 0-3-3-5-4l-1 1v-1c2 0 2 0 3 1v1l1-1c0 1 0 2 1 2 1-1 2-2 4-3h0l1-1h0v-2zm-1-92c2 1 4 2 5 5h0c0 2 0 4-1 7l-3 3c-1 1-2 2-2 4h0c-2-3-4-6-5-10v-2c0 1 1 1 1 2v-3c1-1 1-1 1-2h0c2-2 2-3 4-4z" class="I"></path><path d="M366 486h0l2 2h0c1 0 1 0 2 1v1l1 1v1c0 1 0 1-1 2h-2-2c-1-1-1-2-1-3v-3c1-1 1-1 1-2z" class="Y"></path><path d="M368 494v-1c-1-1-1-1-1-2s0-1 1-1l3 1v1c0 1 0 1-1 2h-2z" class="H"></path><path d="M370 482c2 1 4 2 5 5h0c0 2 0 4-1 7-1-1-2-1-3-2v-1l-1-1v-1c-1-1-1-1-2-1h0l-2-2c2-2 2-3 4-4z" class="j"></path><defs><linearGradient id="DA" x1="372.568" y1="433.82" x2="365.466" y2="435.825" xlink:href="#B"><stop offset="0" stop-color="#8a8988"></stop><stop offset="1" stop-color="#a5a4a5"></stop></linearGradient></defs><path fill="url(#DA)" d="M361 423l1-1c4 5 8 11 11 16 1-1 1-1 1-2 0 1 0 1 1 2 0 1 0 2 1 2l1 3h-1c0 2 0 2-2 4h0c-1 0-1 0-1-1l-2 2h-1c-1-4-3-9-4-13-1-2-2-3-2-5-1-1-2-2-2-4 0-1 0-2-1-3z"></path><path d="M374 436c0 1 0 1 1 2 0 1 0 2 1 2l1 3h-1c-1-2-2-3-3-5 1-1 1-1 1-2z" class="I"></path><path d="M371 338c1 1 1 1 2 1 1-1 2-2 3-4v9 15 6 2 2c-1 0-1-1-1-2l-1-1h0l1-1c0-1-1-3-2-5s-2-5-3-7l-3-9c1 0 1 1 2 0 2-1 3-2 4-3v-1h-1l-2-1 1-1z" class="D"></path><path d="M367 344c1 0 1 1 2 0h4c1 1 1 3 1 4v11s0 1 1 1v2l-1-2h0-1c-1-2-2-5-3-7l-3-9z" class="N"></path><path d="M391 594c3 3 6 6 9 8 2 2 5 4 7 6-2-1-5-1-7-1h-5c-1-1-2-2-4-2v-1h-1c-1-1-3-1-4-2h0c0-1-1-1-2-1h0l-1-1h-2c3-2 7-4 10-6z" class="j"></path><path d="M386 602c2 0 3 1 5 1 1 1 3 2 4 2h1v-1l-1-1v-1c2 1 4 2 5 4v1h-5c-1-1-2-2-4-2v-1h-1c-1-1-3-1-4-2h0zm-27-225h0v-7l-1-1v-4l1-1c-1-1-1-2-1-3v-10c1-1 1-10 1-12l2 4c-1 1 0 2 0 4 1 2 1 8 3 10 1 3 0 7 1 11h0c0 1 0 1-1 2 0 2 0 4-1 5h-1 0c0-1 0-2-1-3v-3l-1 1c-1 3-1 7-1 10v-3z" class="N"></path><path d="M359 377c1-2 0-5 0-8v-20c1 1 0 1 1 1h0v14c0 1 1 3 1 5l-1 1c-1 3-1 7-1 10v-3z" class="I"></path><path d="M365 549l-1-2c0-2 0-3 1-5h1l2 2 1 3 3 6c1 1 1 2 2 4v-1-2c1 1 1 3 1 5-1 2 0 7-1 8h-1 0l-2-1h-1-1c-2-2-3-6-4-8v-9z" class="Y"></path><path d="M369 547l3 6c0 1 0 2-1 4v1c-1-4-1-8-2-11z" class="N"></path><path d="M372 553c1 1 1 2 2 4-1 0-1 2-1 3-2 1-2 4-2 6h-1 0c-1-2 0-6 1-8v-1c1-2 1-3 1-4z" class="C"></path><path d="M374 554c1 1 1 3 1 5-1 2 0 7-1 8h-1 0l-2-1c0-2 0-5 2-6 0-1 0-3 1-3v-1-2z" class="H"></path><path d="M371 566c0-2 0-5 2-6 0 2-1 5 0 7l-2-1z" class="P"></path><path d="M360 428c0-1 0-1 1-2h1c0 2 1 3 2 4 0 2 1 3 2 5 1 4 3 9 4 13v2 1h2 1c1 1 1 2 1 3-1-1-1-2-2-2h-1c-1 1-1 2-2 4-1 1-1 2-1 4s-1 2-1 4v1l-1-1h0c0 1-1 1 0 2v1-1c-1 0-1-1-2-2-1 0-2-2-2-2l-1-11c0-2-1-5 0-7h0c0-5-1-10 0-15l-1-1z" class="I"></path><path d="M366 462c1-2 1-5 1-7v-1c-1-2-1-7 0-9h1c1 2 1 3 2 5v1h2 1c1 1 1 2 1 3-1-1-1-2-2-2h-1c-1 1-1 2-2 4-1 1-1 2-1 4s-1 2-1 4v1l-1-1v-2z" class="N"></path><path d="M361 429v-1h0c0 1 0 1 1 2 1 3 2 8 3 11v5 5h-1 0c0-1-2-2-2-3l-1-4h0c0-5-1-10 0-15z" class="b"></path><defs><linearGradient id="DB" x1="360.643" y1="457.128" x2="364.954" y2="455.428" xlink:href="#B"><stop offset="0" stop-color="#868586"></stop><stop offset="1" stop-color="#a1a09f"></stop></linearGradient></defs><path fill="url(#DB)" d="M361 444l1 4 1 5 1 2c2 2 2 3 2 5v2 2h0c0 1-1 1 0 2v1-1c-1 0-1-1-2-2-1 0-2-2-2-2l-1-11c0-2-1-5 0-7z"></path><path d="M363 453l1 2c2 2 2 3 2 5v2 2h0v-2c-2-3-3-6-3-9z" class="P"></path><defs><linearGradient id="DC" x1="380.285" y1="476.328" x2="364.628" y2="456.256" xlink:href="#B"><stop offset="0" stop-color="#a9a7a8"></stop><stop offset="1" stop-color="#e7e6e7"></stop></linearGradient></defs><path fill="url(#DC)" d="M376 443h1v4l-1 16v15c0 1 1 2 1 3-1 2-1 4-1 5h-1v1h0c-1-3-3-4-5-5h-1c0-1 1-3 1-4 0-2-1-3-1-4l-1-2-4-8c1 1 1 2 2 2v1-1c-1-1 0-1 0-2h0l1 1v-1c0-2 1-2 1-4s0-3 1-4c1-2 1-3 2-4h1c1 0 1 1 2 2 0-1 0-2-1-3h-1-2v-1-2h1l2-2c0 1 0 1 1 1h0c2-2 2-2 2-4z"></path><path d="M376 463h0v15c0 1 1 2 1 3-1 2-1 4-1 5h-1v1h0c-1-3-3-4-5-5h-1c0-1 1-3 1-4 0-2-1-3-1-4l-1-2-4-8c1 1 1 2 2 2v1-1c-1-1 0-1 0-2h0l1 1c0 3 2 4 3 6 1 1 1 3 1 4v1 1c1 0 1 0 1 1h0c1 1 1 1 2 1h0 0l-1 1c1 0 2 1 3 0 0-1-1-1-1-2v-3-1c1-3 1-8 1-11z" class="W"></path><path d="M376 443h1v4l-1 16h0c0 3 0 8-1 11h-1v-20c0-1 0-2-1-3h-1-2v-1-2h1l2-2c0 1 0 1 1 1h0c2-2 2-2 2-4z" class="D"></path><path d="M376 443h1v4l-1 16h0v-9c-1-1 0-2 0-3-1 0-1-1-1-1v-1h-1c-1 0-1 1-2 2h-2v-1-2h1l2-2c0 1 0 1 1 1h0c2-2 2-2 2-4z" class="P"></path><path d="M362 533c0 3-1 8 0 10v2h-1v-2s0-1-1-1v-1h0c1-1 0-1 0-2 1-1 1-2 1-3h0c0-2 1-5 0-6v-1-1c-1-1-1-2-1-3v-2-1-2-1-4-6c1-4 2-11 1-14-1-1-1 0-1-1v-1c1 1 2 2 2 3 2 3 4 5 5 8 1 1 2 3 3 5l3 6s-1 1-2 1v1c0 1-1 2-1 4h0l-1 5-1 2v10c0 2 1 5 3 7 1 2 3 6 3 9v2 1c-1-2-1-3-2-4l-3-6-1-3-2-4c0-1-1-1-1-2-1-1-1-2-2-3l-1-2z" class="D"></path><path d="M362 521l1-1c0 2 1 3 1 5h-1v1c-1-2-1-3-1-5z" class="H"></path><path d="M361 519v-6c0 1 2 6 2 7l-1 1-1-2z" class="P"></path><path d="M363 500c1 1 2 2 2 3 1 3 1 6 2 9 0 2-1 6 0 7 0 1 1 1 1 2v5h1l-1 2-1-1v-2-4h0 0-1c-1-2 0-6 0-9 0 0-1-1-1-2s1-2 0-3c-1-2-1-5-2-7zm-2 19l1 2c0 2 0 3 1 5 1 4 2 9 3 14 0-1-1-1-1-2-1-1-1-2-2-3l-1-2c-1-4-1-9-1-14z" class="C"></path><defs><linearGradient id="DD" x1="364.301" y1="543.415" x2="374.587" y2="544.562" xlink:href="#B"><stop offset="0" stop-color="#80817b"></stop><stop offset="1" stop-color="#908c91"></stop></linearGradient></defs><path fill="url(#DD)" d="M363 526v-1h1l4 13c0 2 1 5 3 7 1 2 3 6 3 9v2 1c-1-2-1-3-2-4l-3-6-1-3-2-4-3-14z"></path><defs><linearGradient id="DE" x1="372.147" y1="514.703" x2="364.553" y2="518.378" xlink:href="#B"><stop offset="0" stop-color="#7e7d7e"></stop><stop offset="1" stop-color="#a8a8a7"></stop></linearGradient></defs><path fill="url(#DE)" d="M362 496c2 3 4 5 5 8 1 1 2 3 3 5l3 6s-1 1-2 1v1c0 1-1 2-1 4h0l-1 5h-1v-5c0-1-1-1-1-2-1-1 0-5 0-7-1-3-1-6-2-9 0-1-1-2-2-3l-1-3v-1z"></path><path d="M365 503c3 3 4 11 4 15l-1 2v1h0c0-1-1-1-1-2-1-1 0-5 0-7-1-3-1-6-2-9z" class="H"></path><defs><linearGradient id="DF" x1="384.48" y1="555.388" x2="361.209" y2="531.674" xlink:href="#B"><stop offset="0" stop-color="#a7a5a5"></stop><stop offset="1" stop-color="#e3e3e4"></stop></linearGradient></defs><path fill="url(#DF)" d="M373 515h1l1 3 1 1 1-1v4 1c0 1 0 10 1 10v-6 37 6 2c0 4 0 7 1 10 0 2 0 2 1 4h0v2h0l-2 2v-3l-2 2c-2 0-3 0-5-1 1 0 1-1 2-1h0c1-1 1-2 1-3l1-2c-1-2 0-4 0-6h0l-1-1 2-2c-1-3-2-4-3-6h1c1-1 0-6 1-8 0-2 0-4-1-5 0-3-2-7-3-9-2-2-3-5-3-7v-10l1-2 1-5h0c0-2 1-3 1-4v-1c1 0 2-1 2-1z"></path><path d="M373 515h1l1 3 1 1 1-1v4c0 4 0 8-1 11v27c-1-2 0-4-1-6h0v-5-2c-1-5-1-10-1-15-1-3 0-6-1-8s-1-2-3-3h0c0-2 1-3 1-4v-1c1 0 2-1 2-1z" class="C"></path><path d="M373 515h1l1 3 1 1 1-1v4c0 4 0 8-1 11 0-2 1-5 0-7v-6h0-2-3l-1 1c0-2 1-3 1-4v-1c1 0 2-1 2-1z" class="H"></path><path d="M373 515h1l1 3v1h0c-2 0-2-1-3-2v-1l-1 1v-1c1 0 2-1 2-1z" class="W"></path><defs><linearGradient id="DG" x1="390.503" y1="549.29" x2="365.645" y2="564.616" xlink:href="#B"><stop offset="0" stop-color="#898785"></stop><stop offset="1" stop-color="#aaa9ab"></stop></linearGradient></defs><path fill="url(#DG)" d="M377 522v1c0 1 0 10 1 10v-6 37 6 2c0 4 0 7 1 10 0 2 0 2 1 4h0v2h0l-2 2v-3l-2 2c-2 0-3 0-5-1 1 0 1-1 2-1h0c1-1 1-2 1-3l1-2c-1-2 0-4 0-6h0l-1-1 2-2c-1-3-2-4-3-6h1c1-1 0-6 1-8-1 2-1 4 0 6l1-1v-4-27c1-3 1-7 1-11z"></path><path d="M376 573v2 3 1h0l-1 1v2c-1-2 0-4 0-6h0l-1-1 2-2z" class="H"></path><path d="M375 580h1c1 2 1 4 2 6l2 2h0l-2 2v-3l-2 2c-2 0-3 0-5-1 1 0 1-1 2-1h0c1-1 1-2 1-3l1-2v-2z" class="I"></path><path d="M234 331l1 9c-3 0-7 0-9 1l-1 1 2 2c3 1 6 1 10 2s9 4 13 6l-1-19c0-25 6-60 24-78 6-6 13-12 21-14 5-1 9-1 14-1l17 1v67c-1 2 0 5 0 8-1 0 0 2-1 2v1 2h0-1c0-1 0-1 1-1l-1-1h-1v2 14c0 1 1 1 1 2v2c-1 0-1-1-1-2h0l-1 1v1c1 6 2 15 1 21l-1 4c-1 4-3 7-5 10 0-1 0-1-1-2l-6-4-6-1h-2l-3 2-2 2-3 2v1 1c0 2-4 5-6 6-1 0-5 1-6 2l1 1v1c-1 0-2-2-3-2-4-4-7-7-10-11 0 2 0 3 1 5l1 1-1 1h0c-1-1-2-2-3-2v-2-3c-1 1-1 3-1 4-1-2-1-4-1-7v1h-1-1c1 4 2 10 1 14h0c-2-2-1-4-2-6 0 5-1 8-2 12-1 7 0 14 0 20-1 1-1 1-2 0l-3-4h-1c1 2 1 3 1 4v1h-1l-1-1-1 1-1-2-1-2c-1-1-2-2-2-3 0-3 0-5-1-8 1-1 1-3 0-4l-1-1-2 2-5-6h-1-1l2-2c-1-1-2-1-2-2h-1l-1-1c0-1-1-2-1-3 0-2 1-4 3-5l-1-1-2-1c-1 1-2 2-3 2l-1 1c-3-3-4-6-8-9l-6-3-3-2h0l-6-5-1-1-1-1s0-1-1-1v-1h-1c0-1-1-2-1-3v-12-3c0-6 3-12 6-18 0 0 4-6 5-7l2-1c2-2 4-3 6-5 1-1 6-3 8-4h1c1 2 0 5-1 7 2-1 3-1 5-2v3 10 8 6z" class="B"></path><path d="M251 371h0v3-3z" class="F"></path><path d="M261 332c-1 0-2 2-3 2 0-1 0-2 1-3 1 0 1 1 2 1z" class="V"></path><path d="M259 331l1-3c0-1 0-1 1-2h0c1 2 1 4 0 6-1 0-1-1-2-1z" class="M"></path><path d="M288 274l-3-6c2 2 5 5 7 8-2-1-2-2-4-2z" class="S"></path><path d="M253 403h0c2 0 4-2 7-3-2 2-4 4-5 6h-1l-1-3z" class="Q"></path><path d="M300 339c3 2 5 5 7 8l-3-3v1c-2-2-3-3-5-4 0-1 0-1 1-1h0 0v-1z" class="a"></path><path d="M279 331h0c-2 0-6-1-8-2v-1h0l11 3h-3z" class="J"></path><path d="M306 282c2 0 5-1 7 0v1c-3 1-7 0-10 0v-1h0 3z" class="T"></path><path d="M304 345v-1l3 3 3 6-1 2-5-10z" class="O"></path><path d="M279 296l-1-5c1-1 3-2 4-2l1-1c2 0 3 0 5 2h0c2 1 2 2 3 3-2-1-4-4-7-4-2 0-2 1-3 2s-1 3-1 4l-1 1z" class="L"></path><path d="M275 347c1-1 5-1 6 0 2 0 2 1 3 2l-1 1c-2 0-7 0-9-1 0-1 0-1 1-2z" class="E"></path><path d="M303 282c-4-3-5-9-6-13 2 3 4 5 5 9 1 1 1 2 1 3l3 1h-3 0z" class="g"></path><path d="M261 332l1 1s0 1 1 1c0 3-1 9-2 12h-1c-1-3-1-6 0-8 0-2 0-4 1-6z" class="F"></path><path d="M279 296l1-1c0-1 0-3 1-4l1 17h0-2-1v-12z" class="E"></path><path d="M281 354h1c1 2 0 19 0 23h-1c-1-2-1-5-1-6l1-17z" class="J"></path><path d="M279 331h3c5 1 10 2 15 6 1 1 2 1 3 2v1h0 0c-1 0-1 0-1 1-5-3-9-6-15-8l-5-2z" class="S"></path><path d="M258 359c-1-1-2-4-2-6-1-4-3-7-6-11 2 0 4 4 5 5l4 6c1 2 2 3 4 4l-1 1h0v2h0c-2 0-2-1-4 0v-1z" class="a"></path><path d="M258 359v-1l1-1c-1-2-1-3-1-5l1 1c1 2 2 3 4 4l-1 1h0v2h0c-2 0-2-1-4 0v-1z" class="e"></path><path d="M288 280h2c1 2 3 4 3 7h0l1 1c0 1 0 3-1 3l-1 1-1-1c0-1-1-1-1-2-1-1-2-1-3-1-1-1-1-1-2-1l1-2 1-1c0-1 1-1 1-2h0v-1-1z" class="Q"></path><path d="M308 305h1v-17c1-1 1-1 2 0s1 17 0 18v1c-1-1-2-1-3-2z" class="F"></path><path d="M267 350c4-7 11-13 18-16-3 4-7 6-10 10-2 2-3 4-4 6 0 1-1 3-2 4v-1h0c0 1 0 1-1 1v1 1l-2-2c1-1 1-2 1-4z" class="D"></path><defs><linearGradient id="DH" x1="292.669" y1="288.743" x2="296.097" y2="287.944" xlink:href="#B"><stop offset="0" stop-color="#4a494a"></stop><stop offset="1" stop-color="#6a6a6a"></stop></linearGradient></defs><path fill="url(#DH)" d="M288 274c2 0 2 1 4 2l4 7c1 6 3 14 2 20l1 1c-1 0-2 0-4 1 1-1 0-1 1-1h0c0-5 0-10-1-14-1-6-4-11-7-16z"></path><path d="M318 311v-30-8l1 1 1 22c0 6 0 13 1 18l-3-3z" class="a"></path><path d="M266 354l2 2v-1-1c1 0 1 0 1-1h0v1c-1 7 0 14 4 20 2 3 5 5 7 7 4-1 7-3 10-5l1-1 2-1v1c0 2-4 5-6 6-1 0-5 1-6 2l1 1v1c-1 0-2-2-3-2-4-4-7-7-10-11-1-2-2-4-2-6s-1-6-2-7l1-1v-4z" class="I"></path><defs><linearGradient id="DI" x1="257.656" y1="392.517" x2="246.796" y2="402.541" xlink:href="#B"><stop offset="0" stop-color="#656467"></stop><stop offset="1" stop-color="#838282"></stop></linearGradient></defs><path fill="url(#DI)" d="M253 389c2 0 3 1 5 2-4 2-5 3-7 7v1 2l2 2 1 3c1 2 1 3 1 4v1h-1l-1-1-1 1-1-2-1-2c-1-1-2-2-2-3 0-3 0-5-1-8 1-1 1-3 0-4l-1-1h0c3-2 5-2 7-2z"></path><path d="M246 391h0c3-2 5-2 7-2v1c-3 2-5 4-6 6 1-1 1-3 0-4l-1-1z" class="K"></path><path d="M250 407v-5l1-3v2l2 2 1 3c1 2 1 3 1 4v1h-1l-1-1-1 1-1-2-1-2z" class="X"></path><path d="M251 401l2 2 1 3c1 2 1 3 1 4-3-3-3-5-4-9z" class="L"></path><path d="M293 352v-1c0-1-1-4 0-5l1 2h2c1 1 2 2 3 2l-1-3c3 2 6 3 7 6 1 2 0 7 1 8l-1 2-2 1c0-2-2-4-4-6l-6-6z" class="U"></path><path d="M301 353c2 3 1 7 4 10l-2 1c0-2-2-4-4-6 1 0 2 0 2 1l1 1v-1h-1v-2-4z" class="S"></path><path d="M293 352v-1c0-1-1-4 0-5l1 2h2c1 1 2 2 3 2 1 1 1 2 2 3v4 2h1v1l-1-1c0-1-1-1-2-1l-6-6z" class="R"></path><path d="M297 282l-3-8c2 2 3 4 4 6 0 1 1 3 1 3 1 1 1 0 1 1l1-1c1 1 2 1 2 2 1 4 1 10 1 14 0 2 0 4 1 5h-4-2l-1-1c1-6-1-14-2-20l1-1z" class="S"></path><path d="M297 282l3 9c1 4 1 8 1 12v1h-2l-1-1c1-6-1-14-2-20l1-1z" class="J"></path><path d="M226 353c1-1 2-2 3-2 2 1 4 3 5 5h1c1 0 3 1 4 2v1c0 1-3 3-5 3-3 1-5 2-8 1h-5-2l-3-1c2-1 5-1 7-2 0-1 1-1 2-1v-1h-2c-1-2-1-3-3-4 2 0 4 0 6-1z" class="O"></path><path d="M229 360c1-1 2-1 4-1h0c0 1 0 2-1 2s-2 1-3 1l-3 1h-5 4c1-1 1-1 2-1s2-1 3-2h-1z" class="K"></path><path d="M219 363c1-1 1-1 2-1s3-1 4-2c1 0 1-1 2-1 1 1 2 1 2 1h1c-1 1-2 2-3 2s-1 0-2 1h-4-2z" class="G"></path><path d="M226 353h0c1 2 2 2 3 3 1 0 1 0 1 1s-1 1-2 2h-1c-1 0-1 1-2 1-1 1-3 2-4 2s-1 0-2 1l-3-1c2-1 5-1 7-2 0-1 1-1 2-1v-1h-2c-1-2-1-3-3-4 2 0 4 0 6-1z" class="D"></path><path d="M231 374c0-1 0-2 1-3 2-1 5-2 7-2 3 1 6 2 8 4 0 2 0 4-2 6-1 1-5 6-6 6-1-1-2-1-2-2h-1l-1-1c0-1-1-2-1-3 0-2 1-4 3-5l-1-1-2-1c-1 1-2 2-3 2z" class="W"></path><path d="M234 372h1c2-1 2-1 4-1h1v2c-1 0-2 0-3 1h0l-1-1-2-1z" class="H"></path><path d="M237 374c1 0 2 0 3 1 1 0 1 1 1 2-1 2-2 4-4 5h-2c0-1-1-2-1-3 0-2 1-4 3-5h0z" class="B"></path><defs><linearGradient id="DJ" x1="272.313" y1="361.799" x2="256.834" y2="374.081" xlink:href="#B"><stop offset="0" stop-color="#686568"></stop><stop offset="1" stop-color="#7c7c7c"></stop></linearGradient></defs><path fill="url(#DJ)" d="M267 350c0 2 0 3-1 4v4l-1 1c1 1 2 5 2 7s1 4 2 6c0 2 0 3 1 5l1 1-1 1h0c-1-1-2-2-3-2v-2-3c-1 1-1 3-1 4-1-2-1-4-1-7v1h-1-1c1 4 2 10 1 14h0c-2-2-1-4-2-6l-4-18c2-1 2 0 4 0h0v-2h0l1-1 4-7z"></path><path d="M263 370c0-4 0-8 2-12h1l-1 1c-1 3 0 7 0 10v1h-1-1z" class="E"></path><path d="M265 369c0-3-1-7 0-10 1 1 2 5 2 7h-1c0 2 1 5 1 6-1 1-1 3-1 4-1-2-1-4-1-7z" class="G"></path><path d="M267 372c0-1-1-4-1-6h1c0 2 1 4 2 6 0 2 0 3 1 5l1 1-1 1h0c-1-1-2-2-3-2v-2-3z" class="S"></path><path d="M267 375h1l2 2 1 1-1 1h0c-1-1-2-2-3-2v-2z" class="Q"></path><defs><linearGradient id="DK" x1="268.967" y1="314.549" x2="273.432" y2="312.572" xlink:href="#B"><stop offset="0" stop-color="#6f6e6f"></stop><stop offset="1" stop-color="#878686"></stop></linearGradient></defs><path fill="url(#DK)" d="M264 330c0-2-1-3-1-5v-1c0-2 1-4 2-6 2-4 7-7 11-8 1 1 3 1 5 1h-3c-1 0-2 0-2 1h-2v1h0 1c1 0 2 0 3-1h0 2 2c-1 0-4 1-4 2-2 1-3 5-5 7-1 0-1 1-2 2v1c-1 1-2 3-3 4 0 3 0 5-1 8l-3-6z"></path><path d="M264 330c0-1 0-2 1-3 1-2 2-4 3-7 1-1 1-3 3-5l1 2v1 1c1 0 1 1 1 2-1 0-1 1-2 2v1c-1 1-2 3-3 4 0 3 0 5-1 8l-3-6z" class="T"></path><path d="M289 360v-5l1-12h0c3 2 5 3 8 4l1 3c-1 0-2-1-3-2h-2l-1-2c-1 1 0 4 0 5v1l6 6c2 2 4 4 4 6l-2 3-3 2-2 2-3 2v1l-2 1-1 1c-1-5-1-11-1-16z" class="L"></path><path d="M289 360h1v-3-7-3c2 3 1 10 2 14v8 3c0 1-1 2-1 2v1l-1 1c-1-5-1-11-1-16z" class="e"></path><path d="M293 373v-5c1-5 0-11 0-16l6 6c2 2 4 4 4 6l-2 3-3 2-2 2-3 2z" class="B"></path><path d="M279 308h1v2c4 1 10-1 13 2h0c2 2 1 6 3 8 0 0 2 1 3 1 1 1 2 2 4 3h-2-1c1 1 3 3 4 3 2 2 3 3 4 5 3 2 4 6 5 10h0c1 6 0 14-4 19-1 1-2 2-2 3-1 1-2 1-4 2v1h-2l2-3 2-1 1-2 3-6 1-2v-1c0-1 1-2 1-3 1-6-1-12-4-17-2-2-5-4-8-6-5-3-11-5-17-6-4 0-7 1-11 4v-1c1-1 1-2 2-2 2-2 3-6 5-7 0-1 3-2 4-2h-2-2 0c-1 1-2 1-3 1h-1 0v-1h2c0-1 1-1 2-1h3c-2 0-4 0-5-1l3-2z" class="D"></path><path d="M283 313c1 0 3 0 5 1l-1 2h-1c-1 0-3 0-4-1v-1l1-1z" class="Y"></path><defs><linearGradient id="DL" x1="231.356" y1="345.973" x2="204.345" y2="331.593" xlink:href="#B"><stop offset="0" stop-color="#d0d0d1"></stop><stop offset="1" stop-color="#f7f6f6"></stop></linearGradient></defs><path fill="url(#DL)" d="M229 299h1c1 2 0 5-1 7 2-1 3-1 5-2v3 10 8 6c-2-3-5-3-9-3-4 1-9 3-11 7-2 3-3 6-2 10 0 3 2 6 5 8 1 1 2 1 3 1 2 1 2 2 3 4h2v1c-1 0-2 0-2 1-2 1-5 1-7 2l-3-1h0l-6-5-1-1-1-1s0-1-1-1v-1h-1c0-1-1-2-1-3v-12-3c0-6 3-12 6-18 0 0 4-6 5-7l2-1c2-2 4-3 6-5 1-1 6-3 8-4z"></path><path d="M227 323h0c2 0 5 0 7-1v3c-2 0-4-1-6-1h-6 0c1-1 3-1 5-1z" class="g"></path><path d="M228 324c2 0 4 1 6 1h0v6c-2-3-5-3-9-3l3-1c1-1 0-1 0-3z" class="X"></path><path d="M217 316l1 1 5-2c2 0 2 1 3 2h8v8h0v-3c-2 1-5 1-7 1h0c0-1 1-1 1-2 1-1 1-2 0-4-2 0-4 0-6 1-1 0-2-1-3-1-2 1-2 1-4 1l2-2z" class="O"></path><path d="M213 309l2-1h0v1h0c0 1-1 1-1 2-2 2-3 4-4 7l-3 6 5-3 3-3c2 0 2 0 4-1 1 0 2 1 3 1-2 1-5 1-7 2-4 3-9 8-11 12l-1 2h-1 0c0-6 3-12 6-18 0 0 4-6 5-7z" class="P"></path><path d="M229 306c2-1 3-1 5-2v3 10h-8c-1-1-1-2-3-2l-5 2-1-1c4-4 7-7 12-10z" class="B"></path><path d="M234 307v10h-8c-1-1-1-2-3-2l11-1v-7z" class="E"></path><path d="M229 299h1c1 2 0 5-1 7-5 3-8 6-12 10l-2 2-3 3-5 3 3-6c1-3 2-5 4-7 0-1 1-1 1-2h0v-1h0c2-2 4-3 6-5 1-1 6-3 8-4z" class="I"></path><path d="M215 308c2-2 4-3 6-5-2 3-5 6-7 8 0-1 1-1 1-2h0v-1h0z" class="C"></path><defs><linearGradient id="DM" x1="318.557" y1="339.14" x2="296.799" y2="341.428" xlink:href="#B"><stop offset="0" stop-color="#5a595a"></stop><stop offset="1" stop-color="#acabac"></stop></linearGradient></defs><path fill="url(#DM)" d="M301 304h4c1 1 2 1 2 1h1c1 1 2 1 3 2h1c2 1 4 3 6 4l3 3v1c1 1 2 3 3 4v2h0-1c0-1 0-1 1-1l-1-1h-1v2 14c0 1 1 1 1 2v2c-1 0-1-1-1-2h0l-1 1v1c1 6 2 15 1 21l-1 4c-1 4-3 7-5 10 0-1 0-1-1-2l-6-4-6-1v-1c2-1 3-1 4-2 0-1 1-2 2-3 4-5 5-13 4-19h0c-1-4-2-8-5-10-1-2-2-3-4-5-1 0-3-2-4-3h1 2c-2-1-3-2-4-3-1 0-3-1-3-1-2-2-1-6-3-8h0c-3-3-9-1-13-2v-2h2l6-1 7-2c2-1 3-1 4-1h2z"></path><path d="M320 345v3l-1-2h0c0-1-1-1-1-1 0-2 1-3 1-4 1 1 1 3 1 4z" class="a"></path><path d="M301 313c3 2 5 4 8 5l2 3s0 1-1 1c-2-2-4-4-7-5l2-1h-1c-1-1-2-1-2-2l-1-1z" class="N"></path><path d="M315 351l1-3h0c1 1 1 2 2 4s1 4 0 6v-1h-2c0-2 1-4 0-6h-1z" class="K"></path><path d="M282 308l6-1c1 2 2 2 4 3h1 1c2 1 5 2 7 3l1 1c0 1 1 1 2 2h1l-2 1c-3-2-6-4-10-5-3-3-9-1-13-2v-2h2z" class="Y"></path><defs><linearGradient id="DN" x1="321.624" y1="354.747" x2="316.256" y2="354.98" xlink:href="#B"><stop offset="0" stop-color="#757375"></stop><stop offset="1" stop-color="#a1a1a2"></stop></linearGradient></defs><path fill="url(#DN)" d="M311 321l3 3 2 3 4 11 1 1c1 6 2 15 1 21l-1 4c-1 4-3 7-5 10 0-1 0-1-1-2l1-1v-1h0c1-1 1-2 2-4 3-6 3-14 2-21 0-1 0-3-1-4 1-1 0-4-1-5-2-6-4-10-8-14 1 0 1-1 1-1z"></path><defs><linearGradient id="DO" x1="318.002" y1="359.351" x2="304.603" y2="366.392" xlink:href="#B"><stop offset="0" stop-color="#8f8e8f"></stop><stop offset="1" stop-color="#b0aeae"></stop></linearGradient></defs><path fill="url(#DO)" d="M315 351h1c1 2 0 4 0 6h2v1l-2 8c0 1-1 3 0 4v1l-1 1-6-4-6-1v-1c2-1 3-1 4-2h0c4-3 7-8 8-13z"></path><defs><linearGradient id="DP" x1="309.456" y1="356.85" x2="312.843" y2="361.643" xlink:href="#B"><stop offset="0" stop-color="#9c969b"></stop><stop offset="1" stop-color="#bdbdbd"></stop></linearGradient></defs><path fill="url(#DP)" d="M315 351h1c0 4-1 7-3 10-2 2-4 5-6 5h-1l1-2c4-3 7-8 8-13z"></path><path d="M309 368c3-1 4-5 6-6 1 1 1 3 1 4s-1 3 0 4v1l-1 1-6-4z" class="P"></path><defs><linearGradient id="DQ" x1="312.948" y1="315.676" x2="290.563" y2="303.236" xlink:href="#B"><stop offset="0" stop-color="#7b7a7b"></stop><stop offset="1" stop-color="#e0dfdf"></stop></linearGradient></defs><path fill="url(#DQ)" d="M301 304h4c1 1 2 1 2 1h1c1 1 2 1 3 2h1c2 1 4 3 6 4l3 3v1c1 1 2 3 3 4v2h0-1c0-1 0-1 1-1l-1-1h-1v2 14c0 1 1 1 1 2v2c-1 0-1-1-1-2h0l-1 1v1l-1-1-4-11-2-3-3-3-2-3c-3-1-5-3-8-5-2-1-5-2-7-3h-1-1c-2-1-3-1-4-3l7-2c2-1 3-1 4-1h2z"></path><path d="M305 304c1 1 2 1 2 1 1 1 1 2 1 4h0l-1-1c-2-1-2-1-2-4z" class="H"></path><defs><linearGradient id="DR" x1="314.63" y1="310.523" x2="309.458" y2="311.153" xlink:href="#B"><stop offset="0" stop-color="#6e6d6d"></stop><stop offset="1" stop-color="#888788"></stop></linearGradient></defs><path fill="url(#DR)" d="M308 305c1 1 2 1 3 2h1c0 2 0 3 1 4l1 1c1 1 2 2 3 4h0c-1 0-2-1-3 0h0l-3-3h0c1 0 1 0 0-1h0l-3-3h0c0-2 0-3-1-4h1z"></path><path d="M314 316h0l2 2c1 1 2 3 3 5 0 0-1 0-1-1-1 0-1-1-2-1v2 4l-2-3-3-3-2-3c1 0 1 0 2 1v-1-1h0v-1h2 1z" class="c"></path><path d="M309 318c1 0 1 0 2 1v-1-1h0c1 1 1 2 2 4 1 1 1 2 1 3l-3-3-2-3z" class="K"></path><path d="M316 318c1 1 2 3 3 5 0 0-1 0-1-1-1 0-1-1-2-1v2l-1-2-2-2c1-1 2-1 3-1z" class="X"></path><path d="M315 321c0-1-1-1 0-2 1 0 1 1 1 2v2l-1-2z" class="O"></path><defs><linearGradient id="DS" x1="320.693" y1="316.987" x2="313.685" y2="315.959" xlink:href="#B"><stop offset="0" stop-color="#3e3e3e"></stop><stop offset="1" stop-color="#606060"></stop></linearGradient></defs><path fill="url(#DS)" d="M312 307c2 1 4 3 6 4l3 3v1c1 1 2 3 3 4v2h0-1c0-1 0-1 1-1l-1-1h-1v2 14c0 1 1 1 1 2v2c-1 0-1-1-1-2h0l-1 1v1l-1-1-4-11v-4-2c1 0 1 1 2 1 0 1 1 1 1 1-1-2-2-4-3-5l-2-2c1-1 2 0 3 0h0c-1-2-2-3-3-4l-1-1c-1-1-1-2-1-4z"></path><path d="M312 307c2 1 4 3 6 4l3 3v1s0 1-1 1l-1-1c-1-2-3-4-5-4v1l-1-1c-1-1-1-2-1-4z" class="X"></path><defs><linearGradient id="DT" x1="321.381" y1="327.354" x2="316.573" y2="323.712" xlink:href="#B"><stop offset="0" stop-color="#3e3c3e"></stop><stop offset="1" stop-color="#525352"></stop></linearGradient></defs><path fill="url(#DT)" d="M316 321c1 0 1 1 2 1 0 1 1 1 1 1 2 1 2 3 3 5v7c0 1 1 1 1 2v2c-1 0-1-1-1-2h0l-1 1v1l-1-1-4-11v-4-2z"></path><path d="M316 321c1 0 1 1 2 1h-1v3l1 1v1c0 1 1 2 1 3 1 2 1 5 1 8l-4-11v-4-2z" class="R"></path><path d="M406 240c3 1 6 1 9 2 8 2 16 8 21 14 6 8 10 16 14 25 5 14 8 28 8 43 1 9 0 18 1 28 4-3 9-5 14-6 3-1 6-2 9-2l2-2-1-1c-3-1-6 0-9-1v-8-7c1 1 4 0 5 0 1-1 2-1 2-1h2c2 0 2 0 4-1l-3-1h-3v-1c1 0 2-1 2-3h1c2 0 4 1 6 1h1l3 2 1-1c2 1 3 3 5 4l2 2c-1 0-1 1-1 1l2 3c1 1 2 3 2 4 1 6 2 11 0 16-1 1-1 3-2 4l-1 2c-1 0-3 2-3 2-1 3-6 6-9 8-2 0-3-1-5 0-3 3-5 7-7 9l-3-2v-1c1 0 1 1 2 1v-1c-2-2-6-3-9-3-2 1-6 2-6 4 0 4 4 9 7 11l3 3h-1-1c-2 1-4 5-5 7v1c-1 1-2 3-2 4l1 1c-1 1-2 3-3 4l-3 5c0 1-2 3-2 4h0c-1 2-2 3-2 4v2c-1 2 0 4-1 5h1v-2h1v-1-2l1-1 2-2v-1l1-1h1l-1 1c-1 2-2 5-3 8-2 2-3 4-3 7l-13 22-9 16-11 30c0 1-1 2-1 2l-1 3c-1 0-1-1-3 0l-6 23c-1 4-2 7-3 11s-2 8-3 11c0 3 0 6 1 9 1 1 1 3 0 4v2c-1 3 0 6 0 9 0 1-1 2-1 2v4 1c0 1 0 2 1 2l-1 1c0 1 1 3 0 3v1h0c1 1 1 3 0 5 0 2 0 4-1 6l-18-19v-2-5-10-43-3-93c-1-2 0-4 0-5l-1-176 23-1z" class="B"></path><path d="M393 404h1c1 0 1 0 2 1 0 1 0 1-1 2h0c-1-1-1-2-2-3z" class="F"></path><path d="M456 405c-1-1-4-2-6-2 2-1 4 0 6 0v2z" class="f"></path><path d="M406 496h0v6s-1-1-1-2 0-3 1-4z" class="V"></path><path d="M418 399h2l-2 2c-1 0-2-1-3-2h0 3z" class="T"></path><path d="M416 496h0c1-1 1 0 2 0l2 2c0 1-1 2-1 2-1-1-2-2-3-4z" class="H"></path><path d="M400 586c0 1 0 1 1 1v1 4h-1v-6z" class="Q"></path><path d="M405 478v-2c1 1 0 2 2 3h0l-1 4-1-1v-4z" class="H"></path><path d="M400 394l4 1h-1c-2 1-5 1-6 0v-1h2 1z" class="F"></path><path d="M389 564l2-1v4c1 1 2 3 2 4-1 0-1-1-2-2 0-2-1-3-2-4v-1z" class="M"></path><path d="M397 470v2c0 1 0 1 1 2h0c0 2-1 3-2 4-1-2-1-3 0-4s1-3 1-4z" class="V"></path><path d="M419 273c1-2 4-5 6-7-1 1-3 7-4 8-1-1-1-1-2-1z" class="Q"></path><path d="M390 555l1 2c0 1 0 3 1 4l-1 2-2 1c0-1 1-1 0-2 0-2 1-4 1-7z" class="V"></path><path d="M406 408l3-3h0l-1 7-3 3v-2c0-2 2-2 2-4h0c1 0 1 0 1-1v-2c0 1-1 1-1 2h-1z" class="g"></path><path d="M446 320c1 2 2 4 3 7h-1c0-1 0-1-1-2v2h0l-1-1c0 1-1 2-1 3v-2c1-2 0-5 1-7z" class="M"></path><path d="M389 333c1-2 1-4 3-6v1h1l1-1c0 2-1 3-2 5l-2 2h0l-1-1z" class="h"></path><path d="M427 347c2 0 4 0 6 2h0-1c-2 0-5 1-7 0 0-1 0-1 1-1l1-1z" class="g"></path><path d="M406 284c-2 0-8 0-9-1v-1c2-1 6-1 8-1h2v2s0 1-1 1z" class="E"></path><path d="M405 281h1c0-5 3-8 6-12l-3 8c-1 2-1 4-2 6v-2h-2z" class="M"></path><path d="M398 420l2-1v-1c0 1 0 1 2 2-1 2 0 2 1 4 1 1 1 3 2 5-1 0-1 1-1 1v2l-3-9-3-3z" class="J"></path><path d="M408 304c0-6 0-12 2-18v4c-1 2-1 3-1 5v8h0v-2-2c0-1 1-3 0-4 0-1 1-1 1-2v-3h0c0-1 0-1 1-2-1 5-1 10-1 16h-2z" class="Q"></path><path d="M439 348c2 1 3 3 4 4 1 2 2 3 3 4v1l-2 1v2h0l-1-2v-1c-1-3-3-6-4-9z" class="a"></path><path d="M384 515c0-1 0-1 1-2v1c1 0 0 0 0-1 1-1 1-1 2-1s2 1 2 2h1c0-1 0-1-1-2v-1-1h0 1v1c0 2 1 3 0 4l-2-1c-1 1-2 3-3 4h-1v-3z" class="V"></path><path d="M490 328c3 2 5 3 6 5v1c2 3 2 6 1 9v1c0-6-1-8-5-12l-2-4z" class="d"></path><path d="M421 456h2c1 0 2-1 2 0l-2 2c0 1 0 1 1 1 0 0 0 1 1 1-1 0-4 0-4 1-2 1-3 1-5 2 1-2 4-4 4-5 1-1 1-2 1-2z" class="V"></path><path d="M427 355c1 2 1 18 0 22l-1-1v-11c0-3 0-7 1-10zm-18-78h1c2-2 4-6 7-7-2 3-3 7-6 10l1-3c0-1 1-1 1-1 1-2 0 0 1-1v-2c-1 2-2 3-4 5-1 2-1 4-2 6-1 0-2 1-3 1l1-1c1 0 1-1 1-1 1-2 1-4 2-6z" class="J"></path><path d="M412 422l1 1v4c0 3-3 6-5 9h-1c0-2 1-3 1-4 2-3 4-6 4-10z" class="E"></path><defs><linearGradient id="DU" x1="393.295" y1="420.06" x2="397.85" y2="418.561" xlink:href="#B"><stop offset="0" stop-color="#6c6b69"></stop><stop offset="1" stop-color="#818083"></stop></linearGradient></defs><path fill="url(#DU)" d="M391 414c2 1 4 3 6 5l1 1 3 3-1 1c-1 0-5-5-7-6l-3-3 1-1z"></path><path d="M442 429c0 1 1 2 1 2l9-13c-1 1-1 2-1 3 0 3-3 6-4 9h0v-1l-3 3v-1l-2 1h0v-3z" class="S"></path><path d="M401 489l2-2c0-2-2-4-1-5 1 0 2-1 2-1 1-1 1-2 1-3v4l1 1 1 2h-1c-1 2-1 3-2 5-1 0-2 0-3-1z" class="c"></path><path d="M447 327h0v-2c1 1 1 1 1 2h1c2 4 0 11-1 15v1 1l-1 1h0v-2-5c1-2 1-3 1-6-1-1-1-3-1-5z" class="J"></path><defs><linearGradient id="DV" x1="407.596" y1="488.212" x2="416.859" y2="493.647" xlink:href="#B"><stop offset="0" stop-color="#7d7e7b"></stop><stop offset="1" stop-color="#989798"></stop></linearGradient></defs><path fill="url(#DV)" d="M407 485c2 0 8 7 10 9l1 2c-1 0-1-1-2 0h0 0l-9-9c0-1-1-1-1-2h1z"></path><path d="M406 408h1c0-1 1-1 1-2v2c0 1 0 1-1 1h0c0 2-2 2-2 4v2c-1 1-2 3-3 5-2-1-2-1-2-2l2-4 2-2 2-4z" class="L"></path><path d="M448 332c0 3 0 4-1 6v5 2h0l1-1v-1-1 5l-3-6c-1-3-1-5 1-8 1 0 1 0 2-1z" class="F"></path><path d="M435 347c-2-2-3-4-5-6s-5-4-7-6c4 2 11 5 13 9v2l-1 1z" class="H"></path><path d="M479 325c1-1 2-1 2-1h2 0v2 1c1 1 1 1 2 1-4 0-7 0-10 1l-1 3h0v-7c1 1 4 0 5 0z" class="Q"></path><path d="M397 419l2-1s0-1 1-1v-1-1h0c0-1 1-3 0-4h0v-1c0-1 0-1-1-2v1 1l-1-1c1-4-2-5-1-9h0c1 3 3 5 4 8l3 4-2 2-2 4v1l-2 1-1-1z" class="F"></path><path d="M402 414l-1-5-1-1h1l3 4-2 2z" class="M"></path><path d="M400 394l-4-2c-1 0-2-1-4-2h0c2-1 7 1 9 2 4 1 9 1 13 1-3 1-6 1-10 2l-4-1z" class="E"></path><path d="M452 348c1-3 4-6 6-8-1 2-1 5-2 7 0 2-1 4-2 6-1 1-2 2-2 3-1-3 0-5 1-8h-1zm-30 107c2-1 4-2 6-2 0 0 1 1 2 1 1-1 1-1 2-1l-3 4c-1 1-3 2-4 3-1 0-1-1-1-1-1 0-1 0-1-1l2-2c0-1-1 0-2 0h-2l1-1z" class="U"></path><path d="M430 454c1-1 1-1 2-1l-3 4c-1 1-3 2-4 3-1 0-1-1-1-1 2-2 4-3 6-5z" class="B"></path><path d="M386 448v-1c-1-1 0-2 0-3 1-2 1-4 2-7h0 0v-1l1-1h0 0c0-1 1-1 1-1h1l-2-2-3-3 6 3-1 1 1 1v1l-2 1h1v2c0 1-1 1-1 2v1l-1 1h-1c-1 2-2 4-2 6z" class="J"></path><path d="M457 403l-1 2c0 2 1 3 2 4 0 1-2 3-2 4l-3 3 1-2 1-1h-1c-1 1-2 1-3 2v-1c0-2 1-3 2-5 1-1 2-2 3-4v-2h1z" class="U"></path><path d="M399 441c1-1 1-1 3-1 2 3 6 10 6 14h-1c-1-1-1-2-2-3l-1-2-5-8z" class="W"></path><path d="M420 412c1 1 3 2 4 3l-3-7c3 2 5 5 5 9l1 8-1-3h-1c-1-3-3-7-5-10z" class="V"></path><path d="M393 418c2 1 6 6 7 6l1-1 3 9v4c0-1-2-3-2-3v-2c-1-2-3-4-5-6l-5-6 1-1z" class="e"></path><path d="M411 410h0c2 0 3 1 4 3 3 5 6 11 7 17 1 2 1 4 1 6h0l-1 1-1-4c0-5-2-9-4-13s-3-7-6-10zm45 3h0c-1 2-2 3-2 4v2c-1 2 0 4-1 5h1c-1 3-6 6-7 9 0 1-1 1-1 2 0-2 1-3 1-5 1-3 4-6 4-9 0-1 0-2 1-3l1-2 3-3z" class="G"></path><path d="M384 417l1-1v1c0-2 1-3 2-4 1 0 3 1 4 1l-1 1 3 3-1 1c-1-1-2-2-3-2v1 1l-3 3h0-2c-1-2 0-4 0-5z" class="J"></path><path d="M394 309c1 1 1 2 1 3h1c1 1 0 1 0 1 0 2 0 3 1 5v3l-5 5 1-2h0c0-1 0-2 1-2h0 1c-1-1-1-1-1-2h0v-1h0c-1-2 0-3-1-5l-1 1v-1l-1 1v-1c0-2 2-4 3-5z" class="E"></path><path d="M411 410h-2c1-1 3-3 5-3l6 5c2 3 4 7 5 10h0c-2 0-2-2-3-3-2-2-4-5-6-6h-1c-1-2-2-3-4-3h0z" class="U"></path><path d="M392 561v1c0 1 1 3 1 4 0 4 3 10 6 12l1 3c1 2 2 3 3 5h0c-1 0-1 0-2 1-1 0-1 0-1-1-2-5-5-10-7-15 0-1-1-3-2-4v-4l1-2z" class="E"></path><defs><linearGradient id="DW" x1="443.187" y1="315.148" x2="436.335" y2="315.323" xlink:href="#B"><stop offset="0" stop-color="#646363"></stop><stop offset="1" stop-color="#898989"></stop></linearGradient></defs><path fill="url(#DW)" d="M430 308c7 2 12 5 15 11v1h1c-1 2 0 5-1 7v-4h-1c-1-2-2-4-3-5s-3-3-5-3c0-1 0 0-1-1 0 0-1-1-2-1h0s-2 0-2-1h2 0 1 1c0-1 0-1-1-1-1-1-3-1-3-2l-1-1z"></path><path d="M456 395c2 0 2 2 4 1 2 3 1 5 1 8l-3 5c-1-1-2-2-2-4l1-2v-1c1-3 0-5-1-7z" class="R"></path><defs><linearGradient id="DX" x1="395.123" y1="530.976" x2="401.26" y2="529.858" xlink:href="#B"><stop offset="0" stop-color="#bbbdbc"></stop><stop offset="1" stop-color="#dedadb"></stop></linearGradient></defs><path fill="url(#DX)" d="M391 523l3-1 8 10c1 2 1 3 1 5h0v1 1l-6-9c-2-2-4-5-6-7z"></path><path d="M447 408c1-1 0-8 0-9 1-3 0-5 1-8 1 8 2 18 0 26-1 3-3 7-4 10-1-1-1-1-1-2h-1c0-1 1-4 2-5v-1c2-2 3-6 3-9v-2z" class="c"></path><path d="M397 536c1-1 1-1 2 0s2 3 3 5c0 0-1 3-1 4 0 2 0 3-1 5l-5-7c0-3 1-5 2-7z" class="S"></path><defs><linearGradient id="DY" x1="432.899" y1="415.669" x2="426.088" y2="430.505" xlink:href="#B"><stop offset="0" stop-color="#717171"></stop><stop offset="1" stop-color="#a5a2a3"></stop></linearGradient></defs><path fill="url(#DY)" d="M428 412c1 1 2 1 3 3 0 1 1 3 1 4h1c0 2 1 3 1 4 0 3-1 5-2 8l-2 2h-2v-1c3-6 2-15 0-20z"></path><path d="M432 431v-4-6c0-1 0-1 1-2 0 2 1 3 1 4 0 3-1 5-2 8z" class="T"></path><defs><linearGradient id="DZ" x1="392.399" y1="470.463" x2="397.31" y2="473.648" xlink:href="#B"><stop offset="0" stop-color="#070807"></stop><stop offset="1" stop-color="#292728"></stop></linearGradient></defs><path fill="url(#DZ)" d="M396 478l-7 6c1-2 2-5 2-7 1-2 1-4 2-6 1-1 1-2 2-4 0-2 1-4 3-5v1c0 2 0 4-1 6l-1 1h1c0 1 0 3-1 4s-1 2 0 4z"></path><path d="M390 555v-8l-3-3v-3h1c1 1 2 1 3 2 1 0 2 1 3 2-1 1-1 2-1 3l-2-1c0 3 0 5 2 8h0v-1l2 2c0 2 1 3 2 5v2l-1 1c1 1 1 2 1 4l-1-1c0-1-1-3-2-5l-2-5h-1l-1-2z" class="J"></path><path d="M391 543c1 0 2 1 3 2-1 1-1 2-1 3l-2-1c-1-1-1-1-1-2s0-1 1-2z" class="h"></path><path d="M393 555v-1l2 2c0 2 1 3 2 5v2l-1 1-1-2c0-2-1-5-2-7z" class="O"></path><path d="M395 562c1-1 1-1 2-1v2l-1 1-1-2z" class="X"></path><path d="M417 287h0c2-2 1-3 4-3 0 1 1 1 1 2h2c1 1 1 2 2 2h1 0c1 0 2 1 2 1h0c0-1-1-2-2-3h-2c-1 0-1 0-2-1h2c1 0 1 1 2 0 1 1 2 2 3 4v2 3h-1c0-2-1-3-2-4-2 0-4 0-6 1l-2 1-3 3h0c0-1-1-2-1-3 0-2 1-4 2-5z" class="Q"></path><path d="M419 292v-1c1-1 3-2 5-2 2-1 4 0 6 2v3h-1c0-2-1-3-2-4-2 0-4 0-6 1l-2 1z" class="O"></path><defs><linearGradient id="Da" x1="453.717" y1="391.584" x2="464.15" y2="396.424" xlink:href="#B"><stop offset="0" stop-color="#555455"></stop><stop offset="1" stop-color="#807f7f"></stop></linearGradient></defs><path fill="url(#Da)" d="M452 391v-1c2-1 5-1 8-1 2 1 4 3 5 5v1c-1 1-2 3-2 4l1 1c-1 1-2 3-3 4 0-3 1-5-1-8-2 1-2-1-4-1-1 0-2-2-2-2-1-1-2-1-2-2z"></path><path d="M452 391c2 0 3 0 5 1l-1 1c-1 0-1 0-2-1v1c-1-1-2-1-2-2z" class="Q"></path><path d="M457 392c1 1 2 2 3 4-2 1-2-1-4-1-1 0-2-2-2-2v-1c1 1 1 1 2 1l1-1z" class="U"></path><path d="M391 557h1l2 5c1 2 2 4 2 5l1 1c0-2 0-3-1-4l1-1 3 5 1 6h0v1l-1 1v5l-1-3c-3-2-6-8-6-12 0-1-1-3-1-4v-1c-1-1-1-3-1-4z" class="R"></path><path d="M399 578c-1-4-2-7-4-11v-1l1 1 1 1c0 1 1 4 2 5l2 1v1l-1 1v5l-1-3z" class="H"></path><path d="M396 564l1-1 3 5 1 6h0l-2-1c-1-1-2-4-2-5 0-2 0-3-1-4z" class="T"></path><defs><linearGradient id="Db" x1="430.46" y1="356.608" x2="442.892" y2="362.926" xlink:href="#B"><stop offset="0" stop-color="#a8a9aa"></stop><stop offset="1" stop-color="#cdcbcd"></stop></linearGradient></defs><path fill="url(#Db)" d="M435 347l1-1v-2c2 1 3 3 3 4 1 3 3 6 4 9v1 3c-1 2-2 6-2 7l-3 6h0-2l1-1v-1c1-2 1-3 2-5 2-7 0-14-4-20z"></path><path d="M403 362l1-1c-1-2-1-6 0-8 2-4 7-6 11-7l-3 3c-1 1-2 2-2 4h0l-1 1c1-1 2-1 3-2v1l1-1c0 1-1 2-1 2h0l-4 5c0 1-1 1-1 2l-1 3h-1l-2-2z" class="M"></path><path d="M406 364c-1-1-1-1-1-2s0-2 1-2l1 1-1 3z" class="S"></path><path d="M412 352v1l1-1c0 1-1 2-1 2h0l-4 5h-1c0-3 1-3 2-5 1-1 2-1 3-2z" class="U"></path><path d="M394 545l3 3c-1 1-1 2-1 3l5 6-1 1c1 1 1 2 1 3-2 3-1 5-1 7l-3-5v-2c-1-2-2-3-2-5l-2-2v1h0c-2-3-2-5-2-8l2 1c0-1 0-2 1-3z" class="M"></path><path d="M394 545l3 3c-1 1-1 2-1 3l-3-3c0-1 0-2 1-3z" class="a"></path><path d="M396 555l-1-2v-1c2 1 3 4 5 6 1 1 1 2 1 3-1 0-4-4-5-6z" class="L"></path><path d="M396 555c1 2 4 6 5 6-2 3-1 5-1 7l-3-5v-2c-1-2-2-3-2-5l1-1z" class="Q"></path><defs><linearGradient id="Dc" x1="424.012" y1="421.419" x2="417.961" y2="425.308" xlink:href="#B"><stop offset="0" stop-color="#1d1d1d"></stop><stop offset="1" stop-color="#434244"></stop></linearGradient></defs><path fill="url(#Dc)" d="M415 413h1c2 1 4 4 6 6 1 1 1 3 3 3h0 1l1 3c0 2 0 6-1 7v1h1c-1 1-2 2-4 3h0c0-2 0-4-1-6-1-6-4-12-7-17z"></path><path d="M439 326v1c-4 4-11 5-17 7-4 1-9 4-13 7s-6 7-8 11c0 2-1 3-2 5 0-1-1-2-1-3l1 1c0-2 0-4 1-5 4-9 12-14 20-17 4-2 7-3 11-4 2-1 6-2 8-3z" class="L"></path><path d="M386 422h0l3-3v-1-1c1 0 2 1 3 2l5 6c2 2 4 4 5 6v2s2 2 2 3c1 3 3 5 3 8-1-2-3-5-4-7-4-7-10-12-17-15z" class="c"></path><path d="M390 511l3 3 6 9 3 6v3l-8-10-3 1-3-3s-2-1-3-2c1-1 2-3 3-4l2 1c1-1 0-2 0-4z" class="T"></path><path d="M390 511l3 3-1 1c2 3 3 5 5 7-1 0-2-1-3-1 0-1-3-3-3-3h-1v2h-1-1s-2-1-3-2c1-1 2-3 3-4l2 1c1-1 0-2 0-4z" class="Q"></path><path d="M477 363c-2-1-4-2-5-3h0v-1c1-5 4-3 7-5l1-1h3c2 1 4 1 6 1-1 0-3 0-3 1-2 1-3 0-5 2l3 3h1c3 0 5 1 8 1-5 2-11 3-16 2z" class="R"></path><path d="M477 363c-1-2-1-3-1-5 0 0 0-1 1-1 2 0 4 2 6 3h0 2c3 0 5 1 8 1-5 2-11 3-16 2z" class="X"></path><path d="M477 358h1c1 1 1 2 1 3s0 0-1 1c-1 0-1-1-2-2 0-1 0-1 1-2z" class="i"></path><path d="M405 285c1 0 2-1 3-1 1-2 1-4 2-6 2-2 3-3 4-5v2c-1 1 0-1-1 1 0 0-1 0-1 1l-1 3-1 6c-2 6-2 12-2 18h-4v-10c0-3 0-6 1-9z" class="E"></path><defs><linearGradient id="Dd" x1="398.568" y1="469.803" x2="407.134" y2="462.272" xlink:href="#B"><stop offset="0" stop-color="#a9a7a8"></stop><stop offset="1" stop-color="#d6d5d6"></stop></linearGradient></defs><path fill="url(#Dd)" d="M395 452h1c1 0 2 1 3 1 1 1 2 1 3 2l5 6v2c1 3 2 5 2 8s-1 6-2 8h0c-2-1-1-2-2-3 1-7-2-12-6-17l-1-1c-1-1-1-2-1-3 0 0-1-2-2-3z"></path><path d="M395 452h1c1 0 2 1 3 1 1 1 2 1 3 2l5 6v2l-1-1h0l-1-1c0-1 0-2-1-3-1 0-2-1-3-2s-2-1-3-1h0l1 2v2l-1-1c-1-1-1-2-1-3 0 0-1-2-2-3z" class="I"></path><defs><linearGradient id="De" x1="443.921" y1="322.126" x2="432.554" y2="320.944" xlink:href="#B"><stop offset="0" stop-color="#6e6d6e"></stop><stop offset="1" stop-color="#989797"></stop></linearGradient></defs><path fill="url(#De)" d="M436 315c2 0 4 2 5 3s2 3 3 5h1v4 2c-1 2-2 5-4 7 0-1-1-3-1-4l-1-5h0v-1c-1-1-2-2-2-3l1-1c-2-1-4-3-6-4 2-1 3-2 4-3h0z"></path><path d="M438 322c1 1 2 2 2 3s0 2-1 2h0v-1c-1-1-2-2-2-3l1-1z" class="H"></path><path d="M440 325c2 2 2 3 2 5v2h-1-1l-1-5c1 0 1-1 1-2z" class="G"></path><path d="M401 489c1 1 2 1 3 1l-4 7c1 3 4 6 5 8l-7 9c0 1-1 2-2 3-1-1-2-3-1-4 0-4 4-5 7-7-2-4-4-6-8-9l6-7 1-1z" class="L"></path><defs><linearGradient id="Df" x1="399.25" y1="491.278" x2="402.061" y2="496.818" xlink:href="#B"><stop offset="0" stop-color="#565455"></stop><stop offset="1" stop-color="#6c6c6d"></stop></linearGradient></defs><path fill="url(#Df)" d="M401 489c1 1 2 1 3 1l-4 7v2h0-1s0-1-1-1c0-2 2-4 2-6v-2l1-1z"></path><path d="M446 383c0-1 0-2 1-3l1 1v10c-1 3 0 5-1 8 0 1 1 8 0 9v2c0 3-1 7-3 9v1c1-4 1-7 1-11-1-1-1-2-1-3v-4-2c-1 0-1 1-1 1l-1-1c0-1 1-1 1-2v-7h0v-1h1c0-2 0-4 1-6v1-2c0 1 0 2 1 2v-2z" class="U"></path><path d="M445 406v2l1-2v3-1 1c1 0 1 1 1 1 0 3-1 7-3 9v1c1-4 1-7 1-11v-3z" class="g"></path><path d="M443 390h1c2 3 1 7 1 10v1 5 3c-1-1-1-2-1-3v-4-2c-1 0-1 1-1 1l-1-1c0-1 1-1 1-2v-7h0v-1z" class="e"></path><path d="M443 398l1 1v3-2c-1 0-1 1-1 1l-1-1c0-1 1-1 1-2z" class="i"></path><path d="M443 390h1c2 3 1 7 1 10v1-5h-1-1v-5h0v-1z" class="a"></path><path d="M446 383c0-1 0-2 1-3l1 1v10c-1 3 0 5-1 8 0 1 1 8 0 9l-1-25z" class="i"></path><path d="M387 362c0 1 0 2 1 3l3 7h0l2 3c1 1 2 3 3 4-1 1-3 2-3 3v1l-2 2-3 2-1 1h-1c0-1 1-1 0-1v-2-1c0-4 0-8 1-13v-9z" class="h"></path><path d="M391 372h0l2 3c1 1 2 3 3 4-1 1-3 2-3 3v1l-2 2-3 2v-1l1-1v-3c0-3 0-5 1-8h1v-1-1z" class="S"></path><path d="M393 375c1 1 2 3 3 4-1 1-3 2-3 3v1l-2 2-1-1 2-2-2-4 3-3z" class="R"></path><path d="M397 548c2 1 3 3 4 5 1 1 2 3 3 4s1 3 0 4v2c-1 3 0 6 0 9 0 1-1 2-1 2v4 1c0 1 0 2 1 2l-1 1c0 1 1 3 0 3v1c-1-2-2-3-3-5v-5l1-1v-1h0l-1-6c0-2-1-4 1-7 0-1 0-2-1-3l1-1-5-6c0-1 0-2 1-3z" class="X"></path><path d="M401 574h1l1-3v8c0 1 0 2 1 2l-1 1-2-7v-1h0z" class="E"></path><path d="M400 581v-5l1-1 2 7c0 1 1 3 0 3v1c-1-2-2-3-3-5z" class="D"></path><path d="M401 557c0 1 1 2 2 3 1 3 0 8 0 11l-1 3h-1l-1-6c0-2-1-4 1-7 0-1 0-2-1-3l1-1z" class="B"></path><defs><linearGradient id="Dg" x1="434.392" y1="436.411" x2="421.116" y2="434.947" xlink:href="#B"><stop offset="0" stop-color="#a5a4a4"></stop><stop offset="1" stop-color="#c4c3c3"></stop></linearGradient></defs><path fill="url(#Dg)" d="M436 414v4c0 3 1 6 0 8 0 3-2 5-3 7-2 2-3 3-5 4-1 1-5 2-5 4l1 8-2 3-3 5c0-6 2-12 1-18-1 0-1-1-1-1 1 0 2-1 3-1l1-1c2-1 3-2 4-3h1 2l2-2c1-3 2-5 2-8 1-2 1-4 1-7h0l1-2z"></path><path d="M421 469c0-1 1-2 2-2s1 1 1 3v1l1 1-6 15-1-1h0c-1 2-2 4-3 5l-1-2c0-2-1-3-1-4 1-6 4-11 8-16z" class="M"></path><path d="M421 469c0-1 1-2 2-2s1 1 1 3v1l1 1-6 15-1-1h0c0-1 0-2 1-3 1-4 3-7 3-11 1 0 0 0 1-1h0v-3h-1l-1 1z" class="F"></path><path d="M402 529c0 1 0 2 1 2h0c1-5 1-9 2-14 1-4 3-8 4-13 1-2 1-5 3-7h1c1 1 1 2 1 3-1 4-2 7-3 11-3 9-4 18-8 26h0c0-2 0-3-1-5v-3z" class="E"></path><defs><linearGradient id="Dh" x1="489.162" y1="348.436" x2="498.599" y2="349.844" xlink:href="#B"><stop offset="0" stop-color="#a1a0a1"></stop><stop offset="1" stop-color="#c4c3c3"></stop></linearGradient></defs><path fill="url(#Dh)" d="M496 333c3 2 4 7 4 10v4c-1 4-3 7-6 8l-3 1c0 1 1 1 1 1 1 1 1 2 2 2h3l-2 2h-2c-3 0-5-1-8-1h-1l-3-3c2-2 3-1 5-2 0-1 2-1 3-1l2-1c3-1 4-4 5-7l1-2v-1c1-3 1-6-1-9v-1z"></path><path d="M495 361c-1 0-2 0-2-1h-6l1-1c1-2 2-2 3-3 0 1 1 1 1 1 1 1 1 2 2 2h3l-2 2z" class="D"></path><defs><linearGradient id="Di" x1="419.439" y1="287.786" x2="410.728" y2="287.593" xlink:href="#B"><stop offset="0" stop-color="#3f4042"></stop><stop offset="1" stop-color="#6e6d6d"></stop></linearGradient></defs><path fill="url(#Di)" d="M419 273c1 0 1 0 2 1l-4 6h1l1-2c1 1 1 1 0 2v1c0 2-2 4-2 6-1 1-2 3-2 5 0 1 1 2 1 3h0c-1 4-1 6-1 10l-4-1h-1c0-6 0-11 1-16 2-5 5-10 8-15z"></path><path d="M417 280h1l1-2c1 1 1 1 0 2v1c0 2-2 4-2 6-1 1-2 3-2 5 0 1 1 2 1 3h0c-1 4-1 6-1 10l-4-1c1-8 2-17 6-24z" class="M"></path><path d="M441 408c0-2 1-5 1-8l1 1s0-1 1-1v2 4c0 1 0 2 1 3 0 4 0 7-1 11-1 1-2 4-2 5h1c0 1 0 1 1 2l-2 2v3h-1v-2-4-1-1c-2 1-2 3-2 5-1 1 0 3-1 5 0 1-1 0-1 2h0v-2c-2 0-3 0-4-1 1-2 3-4 3-7 1-2 0-5 0-8l1 3h0l1-2v4h1c1-1 1-1 1-2v-2l1-10v-1z" class="G"></path><path d="M441 408c0-2 1-5 1-8l1 1s0-1 1-1v2 4-3h-1v5l-1 1-1-1z" class="K"></path><path d="M441 408l1 1c0 3 1 11-1 14h-1v-2-2l1-10v-1z" class="R"></path><path d="M433 433c1 1 2 1 4 1v2h0c0-2 1-1 1-2 1-2 0-4 1-5 0-2 0-4 2-5v1 1 4 2c0 1-1 2-1 4h0l-8 8c-3 2-6 3-8 5l-1-8c0-2 4-3 5-4 2-1 3-2 5-4z" class="P"></path><path d="M398 368c3-1 7-1 11 0 1 0 1 0 2 1l-2 3-10 7-6 4v-1c0-1 2-2 3-3-1-1-2-3-3-4l-2-3 1-1h1c1 0 3-2 5-3z" class="Z"></path><defs><linearGradient id="Dj" x1="413.234" y1="481.215" x2="432.491" y2="474.829" xlink:href="#B"><stop offset="0" stop-color="#3b3b3a"></stop><stop offset="1" stop-color="#5f5e5f"></stop></linearGradient></defs><path fill="url(#Dj)" d="M425 460c1-1 3-2 4-3l-1 4c0 2 1 3 2 4 0 1 0 1 1 3l-11 30-2-2-1-2v-1l2-6 6-15 2-4c0-1 1-2 0-2-1-3-4-4-6-5 0-1 3-1 4-1z"></path><path d="M437 372v1l-1 1h2 0l1 1c0 4-3 8-6 11-1 1-2 3-3 3-4 4-8 7-12 10h-3c2-1 3-1 4-2h0l1-1c-2 1-3 1-5 1h-1v-1c2-1 4-3 5-5 1 1 1 0 1 0h1c-1-1-1-2-2-2-1-1 0-1-1-1l3 1c1 0 2-1 3-2s3-2 4-3c-1 0-1 0-1-1l1-1v-1c4-3 7-5 9-9z" class="Q"></path><path d="M417 396h2l1-1c1-1 2-2 3-2 3-1 4-3 7-4-4 4-8 7-12 10h-3c2-1 3-1 4-2h0l1-1c-2 1-3 1-5 1l2-1z" class="V"></path><path d="M437 372v1l-1 1h2c-3 4-7 7-10 10h0c-1 0-1 0-1-1l1-1v-1c4-3 7-5 9-9z" class="Z"></path><path d="M428 384h0v2c0 1-2 2-3 3l-8 7-2 1h-1v-1c2-1 4-3 5-5 1 1 1 0 1 0h1c-1-1-1-2-2-2-1-1 0-1-1-1l3 1c1 0 2-1 3-2s3-2 4-3z" class="K"></path><path d="M444 432l3-3v1h0c0 2-1 3-1 5-4 6-8 13-12 17l-2 1c-1 0-1 0-2 1-1 0-2-1-2-1-2 0-4 1-6 2 0 0 1-1 1-2 0 0 1 0 1-1l3-2c-1 0-2 0-3 1l-1 1h-1l2-3c2-2 5-3 8-5l8-8h0c0-2 1-3 1-4h1 0l2-1v1z" class="O"></path><path d="M436 444l-1 2c0 2-4 4-3 6h0 2l-2 1c-1 0-1 0-2 1-1 0-2-1-2-1l4-4c1-1 2-2 2-4l2-1z" class="a"></path><path d="M427 450c1 0 2-1 3-1 1-1 3-2 4-4 0 2-1 3-2 4l-4 4c-2 0-4 1-6 2 0 0 1-1 1-2 0 0 1 0 1-1l3-2z" class="Q"></path><defs><linearGradient id="Dk" x1="435.314" y1="447.126" x2="431.74" y2="437.428" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#282526"></stop></linearGradient></defs><path fill="url(#Dk)" d="M444 432l3-3v1c-1 2-2 4-4 6-2 3-5 5-7 8l-2 1c-1 2-3 3-4 4-1 0-2 1-3 1s-2 0-3 1l-1 1h-1l2-3c2-2 5-3 8-5l8-8h0c0-2 1-3 1-4h1 0l2-1v1z"></path><path d="M442 432h0l2-1v1c-1 1-2 3-4 4h0c0-2 1-3 1-4h1z" class="X"></path><path d="M452 348h1c-1 3-2 5-1 8-2 2-2 5-3 8l-1 5c0 1 0 2-1 2 1 3 1 7 1 10l-1-1c-1 1-1 2-1 3v2c-1 0-1-1-1-2v2-1c-1 2-1 4-1 6h-1v1-1c0-1-1-1-1-2v-1-1h-1c0-1 1-2 1-3l1-7-1-1v-1h-1v-4c1-1 1-1 0-2 0-1 1-5 2-7v-3l1 2h0v-2l2-1v-1c2-2 4-5 6-8z" class="i"></path><path d="M442 387h1v3 1-1c0-1-1-1-1-2v-1z" class="K"></path><path d="M446 357h1c-1 1-1 3-2 4l-1-1v-2l2-1z" class="T"></path><path d="M444 360l1 1c1 1 0 2 0 3 0 2 0 5-1 7h-1c1-3 1-7 1-11h0z" class="M"></path><path d="M447 371c1 3 1 7 1 10l-1-1c-1 1-1 2-1 3v2c-1 0-1-1-1-2v2-1c0-4 1-8 2-13z" class="h"></path><path d="M443 358l1 2c0 4 0 8-1 11 0 1 0 1-1 2h0l-1-3c1-1 1-1 0-2 0-1 1-5 2-7v-3z" class="G"></path><path d="M419 292l2-1c2-1 4-1 6-1 1 1 2 2 2 4h1c-2 4-1 9-1 14h-2-2l-10-3c0-4 0-6 1-10l3-3z" class="B"></path><path d="M427 290c1 1 2 2 2 4h1c-2 4-1 9-1 14h-2-2l1-1c1-1 0-4 0-5 1-4 1-8 1-12z" class="M"></path><defs><linearGradient id="Dl" x1="428.774" y1="464.995" x2="449.508" y2="428.211" xlink:href="#B"><stop offset="0" stop-color="#7b7a7b"></stop><stop offset="1" stop-color="#949393"></stop></linearGradient></defs><path fill="url(#Dl)" d="M454 424v-2h1v-1-2l1-1 2-2v-1l1-1h1l-1 1c-1 2-2 5-3 8-2 2-3 4-3 7l-13 22-9 16c-1-2-1-2-1-3-1-1-2-2-2-4l1-4 3-4 2-1c4-4 8-11 12-17 0-1 1-1 1-2 1-3 6-6 7-9z"></path><path d="M392 432c1 1 2 1 4 2 1 0 2 1 2 2l4 4c-2 0-2 0-3 1l5 8 1 2c-1 1-1 1-1 2 1 2 3 5 3 8l-5-6c-1-1-2-1-3-2-1 0-2-1-3-1h-1c1 1 2 3 2 3 0 1 0 2 1 3l-5-5-3-2c-1-1-2-2-4-3 0-2 1-4 2-6h1l1-1v-1c0-1 1-1 1-2v-2h-1l2-1v-1l-1-1 1-1z" class="M"></path><path d="M399 441c-1-2-4-4-5-6v-1l4 2h0l4 4c-2 0-2 0-3 1z" class="K"></path><path d="M390 451h0v-3c-1-2-1-4 0-5h1c1-2 1-4 1-6l3 3 3 3-2 1 2 2h-1c-1 0-2-1-3-1h-2-1v3c0 1 1 2 1 3 0 0 0 1 1 2l-3-2z" class="U"></path><path d="M395 440l3 3-2 1c-1 0-1-2-2-3l1-1z" class="R"></path><path d="M398 443c1 2 3 5 5 7 0 0 0-1 1-1l1 2c-1 1-1 1-1 2 1 2 3 5 3 8l-5-6c-1-1-2-1-3-2-1 0-2-1-3-1h-1c1 1 2 3 2 3 0 1 0 2 1 3l-5-5c-1-1-1-2-1-2 0-1-1-2-1-3v-3h1 2c1 0 2 1 3 1h1l-2-2 2-1z" class="X"></path><path d="M403 450s0-1 1-1l1 2c-1 1-1 1-1 2 0 0-1-2-1-3z" class="F"></path><path d="M395 452l-1-1c-1-1-1-3-2-4v-1c1 0 2 2 3 2 3 2 5 4 7 7-1-1-2-1-3-2-1 0-2-1-3-1h-1z" class="C"></path><defs><linearGradient id="Dm" x1="400.939" y1="313.18" x2="405.528" y2="312.174" xlink:href="#B"><stop offset="0" stop-color="#6e6c6d"></stop><stop offset="1" stop-color="#8a898a"></stop></linearGradient></defs><path fill="url(#Dm)" d="M408 304h2 1l4 1 10 3h2l1 1c-1 1-4 2-6 2h-1c-1 0-3 0-5 1l-6 1v1c-1 0-3 1-4 2l-6 4h-2s-1 0-1 1v-3c-1-2-1-3-1-5 0 0 1 0 0-1h-1c0-1 0-2-1-3l5-3 3-1 2-1h4z"></path><path d="M399 306l3-1v1c2 1 3 0 5 0l-1 1v2c-1 0-2 1-2 2l-1 1-1-1c1-1 2-2 2-3-2-2-3 0-5-1v-1z" class="W"></path><path d="M407 314l2-2 1 1v1c-1 0-3 1-4 2l-6 4h-2c3-3 5-5 9-6z" class="b"></path><defs><linearGradient id="Dn" x1="410.486" y1="314.064" x2="418.684" y2="302.655" xlink:href="#B"><stop offset="0" stop-color="#979697"></stop><stop offset="1" stop-color="#dad9d9"></stop></linearGradient></defs><path fill="url(#Dn)" d="M408 304h2 1l4 1 10 3h2l1 1c-1 1-4 2-6 2h-1c-1 0-3 0-5 1l-6 1-1-1-2 2v-3-1l-3 1c0-1 1-2 2-2v-2l1-1c-2 0-3 1-5 0v-1l2-1h4z"></path><path d="M417 310c1 0 2-1 3 0h0l1 1c-1 0-3 0-5 1l1-2z" class="Y"></path><path d="M409 312c3-1 5-1 8-2l-1 2-6 1-1-1z" class="l"></path><path d="M441 368c1 1 1 1 0 2v4h1v1l1 1-1 7c0 1-1 2-1 3h1v1 1c0 1 1 1 1 2v1h0v7c0 1-1 1-1 2 0 3-1 6-1 8v1l-1 10v2c0 1 0 1-1 2h-1v-4l-1 2h0l-1-3v-4l-1-3v-1c-1-3-1-8-1-11 1-2 1-4 2-6v-2c-1 0-1-2-1-2 1-1 2-3 2-4h0l-2 1h0c-1 0-1 1-1 1h-1v-1c3-3 6-7 6-11l-1-1 3-6z" class="d"></path><path d="M440 419v-3c0-1-1-1-1-2v-16c0 1 0 5 1 5v-1l1 8v-1l-1 10z" class="K"></path><defs><linearGradient id="Do" x1="443.565" y1="398.409" x2="439.281" y2="399.122" xlink:href="#B"><stop offset="0" stop-color="#858686"></stop><stop offset="1" stop-color="#9e999d"></stop></linearGradient></defs><path fill="url(#Do)" d="M441 386h1v1 1c0 1 1 1 1 2v1h0v7c0 1-1 1-1 2 0 3-1 6-1 8v1 1l-1-8v1c-1 0-1-4-1-5l1-5c0-2 1-5 1-7z"></path><path d="M441 386h1v1 1c-1 5-2 10-2 14v1c-1 0-1-4-1-5l1-5c0-2 1-5 1-7z" class="a"></path><path d="M436 393c1-2 2-6 3-7h0c0 1 0 3-1 4v7 14c0 3-1 6 0 8l-1 2h0l-1-3v-4l-1-3v-1c-1-3-1-8-1-11 1-2 1-4 2-6z" class="F"></path><path d="M435 410l1 1c0 1 0 1 1 2 0 1-1 1 0 1v-2h0c1-1-1-13 1-15v14c0 3-1 6 0 8l-1 2h0l-1-3v-4l-1-3v-1z" class="E"></path><path d="M441 368c1 1 1 1 0 2v4h1v1l1 1-1 7c0 1-1 2-1 3 0 2-1 5-1 7h-1v-1c1-1 1-2 0-3v4l-1-3c1-1 1-3 1-4h0c-1 1-2 5-3 7v-2c-1 0-1-2-1-2 1-1 2-3 2-4h0l-2 1h0c-1 0-1 1-1 1h-1v-1c3-3 6-7 6-11l-1-1 3-6z" class="a"></path><path d="M442 375l1 1-1 7c0 1-1 2-1 3 0 2-1 5-1 7h-1v-1c1-1 1-2 0-3v4l-1-3c1-1 1-3 1-4h0l3-11z" class="D"></path><defs><linearGradient id="Dp" x1="427.322" y1="394.474" x2="440.944" y2="415.342" xlink:href="#B"><stop offset="0" stop-color="#4b4a4b"></stop><stop offset="1" stop-color="#878686"></stop></linearGradient></defs><path fill="url(#Dp)" d="M435 386l2-1h0c0 1-1 3-2 4 0 0 0 2 1 2v2c-1 2-1 4-2 6 0 3 0 8 1 11v1l1 3-1 2h0c0 3 0 5-1 7 0-1-1-2-1-4h-1c0-1-1-3-1-4-1-2-2-2-3-3-1-5-6-9-10-11l2-2h-2c4-3 8-6 12-10 1 0 2-2 3-3v1h1s0-1 1-1h0z"></path><path d="M434 411h1l1 3-1 2h0l-1-5z" class="C"></path><path d="M423 401l3-3 5-4c0 1 0 3-1 3 0 1 0 1-1 1s-1 1-2 2c0 1-1 1-2 1l-1 1-1-1z" class="S"></path><defs><linearGradient id="Dq" x1="437.015" y1="400.206" x2="430.407" y2="407.606" xlink:href="#B"><stop offset="0" stop-color="#908e94"></stop><stop offset="1" stop-color="#adaeab"></stop></linearGradient></defs><path fill="url(#Dq)" d="M436 391v2c-1 2-1 4-2 6 0 3 0 8 1 11v1h-1l-1-6c-1-5 0-10 3-14z"></path><path d="M420 399l3 2 1 1c5 4 8 10 9 16 0 0 0 1-1 1 0-1-1-3-1-4-1-2-2-2-3-3-1-5-6-9-10-11l2-2h0z" class="G"></path><path d="M435 386l2-1h0c0 1-1 3-2 4-2 1-3 4-4 5l-5 4-3 3-3-2h0-2c4-3 8-6 12-10 1 0 2-2 3-3v1h1s0-1 1-1h0z" class="Q"></path><path d="M433 386v1h1s0-1 1-1h0l-3 3c-1 2-2 4-4 5-1 1-3 1-4 2-2 1-3 2-4 3h0 0-2c4-3 8-6 12-10 1 0 2-2 3-3z" class="S"></path><defs><linearGradient id="Dr" x1="418.242" y1="309.731" x2="426.184" y2="324.292" xlink:href="#B"><stop offset="0" stop-color="#a9a7a7"></stop><stop offset="1" stop-color="#c9c9ca"></stop></linearGradient></defs><path fill="url(#Dr)" d="M427 308h2 1l1 1c0 1 2 1 3 2 1 0 1 0 1 1h-1-1 0-2c0 1 2 1 2 1h0c1 0 2 1 2 1 1 1 1 0 1 1h0c-1 1-2 2-4 3 2 1 4 3 6 4l-1 1c-2-1-4-2-7-3-2-1-4-1-6-1-7 3-15 6-21 11h0v-1l2-1c-2-4 2-8 2-11l-1-1c1-1 3-2 4-2v-1l6-1c2-1 4-1 5-1h1c2 0 5-1 6-2l-1-1z"></path><path d="M433 313c1 0 2 1 2 1 1 1 1 0 1 1h0c-1 1-2 2-4 3 2 1 4 3 6 4l-1 1c-2-1-4-2-7-3 1 0 2-1 3 0h1c-1 0-2 0-2-1l-1-1c-1 0-1 0-1-1l1 1 1-1h0l-1-1c1-1 2-1 2-3z" class="P"></path><path d="M411 323h1c2-1 8-5 9-4-3 1-7 3-10 4v1h1l4-2 1-1c1 0 2-1 3-1l1-1h1 2c-7 3-15 6-21 11h0v-1l2-1c2-2 4-4 6-5z" class="Z"></path><path d="M410 314h0v1h0c2 2 1 4 0 6 0 1 0 1-1 2h2c-2 1-4 3-6 5-2-4 2-8 2-11l-1-1c1-1 3-2 4-2z" class="W"></path><defs><linearGradient id="Ds" x1="489.007" y1="339.763" x2="504.807" y2="336.971" xlink:href="#B"><stop offset="0" stop-color="#c9c7c8"></stop><stop offset="1" stop-color="#f4f3f3"></stop></linearGradient></defs><path fill="url(#Ds)" d="M484 322h-3v-1c1 0 2-1 2-3h1c2 0 4 1 6 1h1l3 2 1-1c2 1 3 3 5 4l2 2c-1 0-1 1-1 1l2 3c1 1 2 3 2 4 1 6 2 11 0 16-1 1-1 3-2 4l-1 2c-1 0-3 2-3 2l-2 1h-3c-1 0-1-1-2-2 0 0-1 0-1-1l3-1c3-1 5-4 6-8v-4c0-3-1-8-4-10-1-2-3-3-6-5l2 4c-3-2-5-3-7-4-1 0-1 0-2-1v-1-2h0c2 0 2 0 4-1l-3-1z"></path><path d="M495 320c2 1 3 3 5 4l2 2c-1 0-1 1-1 1-2-2-4-4-7-6l1-1z" class="K"></path><path d="M487 323v-2l3 2c3 1 6 2 8 5v3c1 1 2 3 3 4v5c0 2 0 4-1 6v1-4c0-3-1-8-4-10-1-2-3-3-6-5l2 4c-3-2-5-3-7-4-1 0-1 0-2-1v-1-2h0c2 0 2 0 4-1h0z" class="Z"></path><path d="M490 323c3 1 6 2 8 5v3l-3-3c-1-1-1-2-2-2h-1v-1l-2-1h0v-1z" class="N"></path><path d="M487 323h0c1 0 2 1 3 1l2 2h-1c-1 0-2-1-4-1-1 0 0 0-1 1 1 0 1 1 2 1l2 1 2 4c-3-2-5-3-7-4-1 0-1 0-2-1v-1-2h0c2 0 2 0 4-1z" class="P"></path><path d="M424 319c2 0 4 0 6 1 3 1 5 2 7 3 0 1 1 2 2 3-2 1-6 2-8 3-4 1-7 2-11 4-8 3-16 8-20 17-1 1-1 3-1 5l-1-1h0c-1 0-1 1-1 2-1-1-1-4-1-5l-1-1c0-3 0-7 1-11h0c1-4 3-7 7-10v1h0c6-5 14-8 21-11z" class="B"></path><path d="M396 339h0c1-4 3-7 7-10v1h0c-4 5-6 9-6 16v4l1 4c-1 0-1 1-1 2-1-1-1-4-1-5l-1-1c0-3 0-7 1-11z" class="K"></path><path d="M395 350l1-2h0c0 1 0 2 1 2l1 4c-1 0-1 1-1 2-1-1-1-4-1-5l-1-1z" class="G"></path><defs><linearGradient id="Dt" x1="388.287" y1="341.246" x2="398.705" y2="343.942" xlink:href="#B"><stop offset="0" stop-color="#262626"></stop><stop offset="1" stop-color="#5c5b5c"></stop></linearGradient></defs><path fill="url(#Dt)" d="M406 316l1 1c0 3-4 7-2 11l-2 1c-4 3-6 6-7 10h0c-1 4-1 8-1 11l1 1c0 1 0 4 1 5 0-1 0-2 1-2h0c0 1 1 2 1 3 1 2 2 4 4 5l2 2 1 2c1 1 1 1 3 2-4-1-8-1-11 0-2 1-4 3-5 3h-1l-1 1h0l-3-7c-1-1-1-2-1-3-1-5-1-10-1-14 0-5 2-10 3-15l1 1h0l2-2c1-2 2-3 2-5l-1 1h-1v-1-1l5-5c0-1 1-1 1-1h2l6-4z"></path><path d="M389 355v-2l1-1 2 1h-2l-1 2z" class="S"></path><path d="M388 365v-1s1 0 1 1v-1h1c0 3 1 5 2 7h0l-1 1h0l-3-7zm9-44c0-1 1-1 1-1h2c-2 2-4 4-6 7h0l-1 1h-1v-1-1l5-5z" class="e"></path><path d="M392 332c0 3-1 7-1 11v1c-1 1-1 1-1 2v1h-1-1c-1 0-1 0-2 1 0-5 2-10 3-15l1 1h0l2-2z" class="M"></path><defs><linearGradient id="Du" x1="391.231" y1="350.823" x2="400.403" y2="365.464" xlink:href="#B"><stop offset="0" stop-color="#474647"></stop><stop offset="1" stop-color="#767575"></stop></linearGradient></defs><path fill="url(#Du)" d="M392 353v-1c1 0 2-2 2-3 1-2 0-4 0-6 1-1 1-2 1-3v-1-1l1 1c-1 4-1 8-1 11l1 1c0 1 0 4 1 5 0-1 0-2 1-2h0c0 1 1 2 1 3 1 2 2 4 4 5l2 2 1 2c1 1 1 1 3 2-4-1-8-1-11 0-2 1-4 3-5 3h0c0-2 0-4-1-5-1-4-2-8-3-11l1-2h2z"></path><path d="M396 351c0 1 0 4 1 5l1 2-2 2c-1-3-1-6 0-9z" class="K"></path><path d="M398 358s2 2 2 3l-1 1c0 2 1 2 2 4h0-1c-2-2-3-4-4-6l2-2z" class="G"></path><path d="M398 354h0c0 1 1 2 1 3 1 2 2 4 4 5l2 2 1 2h-2c-2-1-3-3-4-5 0-1-2-3-2-3l-1-2c0-1 0-2 1-2z" class="H"></path><path d="M392 366v-8l2 2 3 7c0 1 0 1 1 1-2 1-4 3-5 3h0c0-2 0-4-1-5z" class="G"></path><path d="M417 345l2-2v6 18c0 3 1 6 0 9 3 2 6 4 9 5v1l-1 1c0 1 0 1 1 1-1 1-3 2-4 3s-2 2-3 2l-3-1c1 0 0 0 1 1 1 0 1 1 2 2h-1s0 1-1 0c-2-1-5-3-8-4-4-2-7-4-10-6h-1c0-1 0-1-1-2l10-7 2-3c-1-1-1-1-2-1-2-1-2-1-3-2l-1-2h1l1-3c0-1 1-1 1-2l4-5h0s1-1 1-2l-1 1v-1c-1 1-2 1-3 2l1-1h0c0-2 1-3 2-4l3-3 2-1z" class="g"></path><path d="M417 345l2-2v6-1c0-1-1-1 0-2 0-1 0-1-1-1h-1l1 1c-1 1-1 1-2 1-1 1-3 4-4 5s-2 1-3 2l1-1h0c0-2 1-3 2-4l3-3 2-1z" class="E"></path><path d="M415 346l2-1c0 1-1 2-2 3l-3 1 3-3z" class="f"></path><path d="M409 372v1c2-1 2-1 4-1h0v1c-1 1-2 1-2 2s-1 1-1 2l-1 1-1 1h-1c0 1-1 2-1 2l-1 1c-1-1-3-1-4-1h-1c0-1 0-1-1-2l10-7z" class="F"></path><path d="M412 354l4-3h0c-1 7 0 14-1 21h-2c-2 0-2 0-4 1v-1l2-3c-1-1-1-1-2-1-2-1-2-1-3-2l-1-2h1l1-3c0-1 1-1 1-2l4-5z" class="B"></path><path d="M405 382c1 0 3 1 3 1h1c2-2 4-4 5-6v-1h1c3 2 7 7 11 7v-1c-6-1-10-6-13-10 2 1 4 2 6 4 3 2 6 4 9 5v1l-1 1c0 1 0 1 1 1-1 1-3 2-4 3s-2 2-3 2l-3-1c1 0 0 0 1 1 1 0 1 1 2 2h-1s0 1-1 0c-2-1-5-3-8-4-4-2-7-4-10-6 1 0 3 0 4 1z" class="I"></path><path d="M418 388v-1c-1 0-3-1-4-2l1-1c2 0 4 1 6 2 1 0 2 1 3 1-1 1-2 2-3 2l-3-1z" class="Y"></path></svg>