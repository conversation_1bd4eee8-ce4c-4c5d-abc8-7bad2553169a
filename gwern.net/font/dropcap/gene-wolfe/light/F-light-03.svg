<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="60 32 572 648"><!--oldViewBox="0 0 696 752"--><style>.B{fill:#3a3838}.C{fill:#554f4a}.D{fill:#494340}.E{fill:#4e4845}.F{fill:#353333}.G{fill:#232021}.H{fill:#454140}.I{fill:#272526}.J{fill:#201c1e}.K{fill:#611b18}.L{fill:#321012}.M{fill:#706359}.N{fill:#302d2e}.O{fill:#6a615a}.P{fill:#5d544f}.Q{fill:#431011}.R{fill:#5e5752}.S{fill:#8e221c}.T{fill:#3b0f11}.U{fill:#514b48}.V{fill:#fff}.W{fill:#1d1b1d}.X{fill:#751f1a}.Y{fill:#601b17}.Z{fill:#645b53}.a{fill:#343031}.b{fill:#413e3d}.c{fill:#3f3b3a}.d{fill:#766c62}.e{fill:#5b1615}.f{fill:#9a221d}.g{fill:#84251b}.h{fill:#301315}.i{fill:#4e1716}.j{fill:#a02b24}.k{fill:#c4b7ab}.l{fill:#691514}.m{fill:#8a7d70}.n{fill:#ece6e1}.o{fill:#511413}.p{fill:#bdb3a9}.q{fill:#371110}.r{fill:#cbc3ba}.s{fill:#897e73}.t{fill:#220f10}.u{fill:#e7dad0}.v{fill:#491413}.w{fill:#171316}.x{fill:#80756c}.y{fill:#9a9083}.z{fill:#731a14}.AA{fill:#221718}.AB{fill:#260c0f}.AC{fill:#150e14}.AD{fill:#ab3027}.AE{fill:#d24238}.AF{fill:#94877b}.AG{fill:#752018}.AH{fill:#f6e2d8}.AI{fill:#bd3427}.AJ{fill:#7d7067}.AK{fill:#a19789}.AL{fill:#a89b8e}.AM{fill:#aca297}.AN{fill:#b82724}.AO{fill:#7e7064}.AP{fill:#d8453b}.AQ{fill:#9a8d7f}.AR{fill:#e96e67}.AS{fill:#ca6b59}.AT{fill:#eb867b}.AU{fill:#111012}.AV{fill:#f0a096}.AW{fill:#f68e82}.AX{fill:#f29a91}.AY{fill:#4e342f}.AZ{fill:#d5b3a0}.Aa{fill:#c79583}.Ab{fill:#d2372d}.Ac{fill:#ee7b73}.Ad{fill:#b0a99c}.Ae{fill:#ca9d87}.Af{fill:#f5b8ae}.Ag{fill:#995744}.Ah{fill:#f6c4ba}.Ai{fill:#a85e4b}.Aj{fill:#b47d6a}</style><path d="M265 200v-2h1v5c-1 1-1 2-1 4v-1-5-1z" class="E"></path><path d="M404 154c2-2 3-3 4-5h0 1v1l1 1-2 2v-1l-3 2h-1z" class="O"></path><path d="M494 62h6c-1 1-2 2-3 2v1l-1-1h-2 0v-2z" class="C"></path><path d="M420 124v2c1 1 0 4 0 5h-1l-1-1c0-2 1-4 2-6z" class="P"></path><path d="M352 676c1 1 2 5 2 6-1 1-1 1-2 1v-5h0v-2z" class="l"></path><path d="M500 62l7-1c-3 1-6 2-8 4l-1-1h-1c1 0 2-1 3-2z" class="D"></path><path d="M177 491h1 1l-3 3-2 1-1-2 4-2z" class="Y"></path><path d="M455 612h1l2 1h1c-2 2-4 2-6 2v-1h-1c1-1 2-1 3-1v-1z" class="f"></path><path d="M346 678c1 1 1 1 2 1-1 2 0 4 0 6l-3-3c0-2 1-2 1-4z" class="K"></path><path d="M497 64h1l1 1c-1 1-1 1-1 3 1 0 1 1 2 2h-1l-2-1c-1-2-1-3 0-4v-1z" class="N"></path><path d="M352 678v5l-2 4h-1c1-1 1-2 1-3v-3s0 1 1 1l1-4z" class="K"></path><path d="M160 431l2-8c1 1 1 2 1 3l-1 4c0 1-1 1-2 1z" class="f"></path><path d="M271 167l5 5v1h-1c0-1-1-1-1-2v2c1 0 1 1 1 2-2-2-3-5-5-7l1-1z" class="W"></path><path d="M127 349c0-5-2-7-6-10 1 0 2 1 3 1 3 2 4 5 5 8l-2 1z" class="AY"></path><path d="M237 602c1 0 1-1 2-1v-3c0 1 1 2 1 3 1 2 2 4 4 6-3-1-5-3-7-5z" class="g"></path><path d="M464 53l1-1c2-2 3-4 4-6 0 3 0 5 1 8h0v1l-2-4c-1 0-2 1-3 2h-1z" class="P"></path><path d="M302 160l1-1h1l5 5c-1 0-4 0-5-1s0-1-2-3z" class="AL"></path><path d="M273 644h0 1c1 3 2 5 0 7 0 1-1 2-1 3v-1l-1-1v-1c2-2 1-4 1-7z" class="K"></path><path d="M455 54c3 0 6 0 9-1h1l-1 1c-1 0-2 1-3 2h-2-3c0 1-1 1-1 1l-1-1h2 1c2-1 0 0 1-1h0-2l-1-1z" class="O"></path><path d="M246 613s1 0 2-1c1 0 1 1 2 1-2 1-5 2-8 1h-2c0-1 0-1 1-1 1-1 2 0 3-1l2 1z" class="S"></path><path d="M504 536v-1c2 1 2 1 3 1l1 1 1 1c2 3 2 4 2 6s0 2-1 3l-1 1v-1c1-2 1-5 0-7s-3-3-5-4z" class="L"></path><path d="M264 197l1-11c0 4 1 8 1 12h-1v2c-1-1-1-2-1-3z" class="C"></path><path d="M211 593c1 0 1 1 2 1l1 1c0 1 1 1 2 1 1 1 3 2 5 2h2l-1 1h0-1l-1 1c-3-2-7-3-9-7h0z" class="q"></path><path d="M312 165c1-2 0-3-1-5l3 4c1 0 2 1 2 2h-1l1 1v1h-2l-1-1c-1-1-1-1-2-1-1-1 0 0 0-1h1 0z" class="Z"></path><path d="M312 165h1l1 1-1 1c-1-1-1-1-2-1-1-1 0 0 0-1h1z" class="P"></path><path d="M261 206c1 1 1 1 1 3s-2 3-3 5h0l-1-2 3-6z" class="U"></path><path d="M208 559l1 1h0 1c1 3 3 5 5 7-4-2-6-3-9-7h1 1v-1z" class="g"></path><path d="M271 203c2 3 3 4 6 6h-1 0c-2 0-2 0-4-1h-2v-1c0-1 0-3 1-4z" class="j"></path><path d="M455 57s1 0 1-1h3l-3 3v2h-4 0l1-1c0-1 1-2 1-3h1z" class="P"></path><path d="M348 679l1-4h1v3 3 3c0 1 0 2-1 3 0-1-1-1-1-2 0-2-1-4 0-6z" class="j"></path><path d="M280 613v2c-1 0-2 1-3 2l1 1c-2 0-2-2-4 0h-1 0c0-1 0-1-1-2h0v-1c3 0 5 0 7-1l1-1z" class="a"></path><path d="M238 595l2-2h2c-2 2-2 3-3 5v3c-1 0-1 1-2 1 0-1-1-2-1-4h1c1 0 1-2 1-3z" class="j"></path><path d="M173 509h1v1c-1 5 2 8 4 13-2-3-6-8-7-12l2-2zm72 82v2h3c-3 0-5 2-7 4-1 1-1 2-1 3h0v1c0-1-1-2-1-3 1-2 1-3 3-5 1-1 2-1 3-2z" class="Y"></path><path d="M562 460c2 3 3 6 2 9l-1 4v-4c0-2-1-4-2-5h0c0-1 0-2-1-3l1-1 1 1h0 0v-1z" class="g"></path><path d="M147 539c-1 0-2 0-4-1-3-1-7-5-9-8 4 4 7 7 13 7h1l-2 1h1v1z" class="S"></path><path d="M277 55l10 3-8-1h-4l-1-1c-2-1-6 0-8 0 2-1 6 0 8-1h3z" class="M"></path><path d="M263 54c5 0 9 0 14 1h-3c-2 1-6 0-8 1h-1v-1h0-6 6v-1h-2z" class="AL"></path><path d="M521 492v-1-1c1 2 4 3 6 4h1c0 1 0 1-1 2l-5-2c-1 0-1 0-1-1v-1zm-15-106h0c2 0 4-2 6-3l2-2c-1 3-2 5-5 7l1 1h-1 0v-1l-2-1c0 1 0 1-1 1l-1-2h1z" class="K"></path><path d="M191 386h1c4-2 6-6 8-10 1 2 1 2 0 4s-3 5-5 6h-1-3z" class="S"></path><path d="M384 163l3-2c0 1-1 2-1 3 1 0 2 1 3 0h2v1 2h1c-1 1-1 2-2 2h-1v-2h-1l-2-1c-1-1 0-1-1-3h-1z" class="O"></path><path d="M266 150c-2-4-1-9-1-14l1 3h1c1 1 1 5 1 6l-2 2c-1 1 0 2 0 3z" class="C"></path><path d="M428 203l1-6c0 5 1 10 3 14h-3v-3h0 0c0-2 0-4-1-5z" class="X"></path><path d="M162 546l1-1c0 1 1 2 2 3h0l-1-3h0c3 6 6 9 11 13-6-2-10-6-12-11l-1-1z" class="g"></path><path d="M207 62c6-1 10-2 16-4v1l-6 2c-3 1-5 2-8 2h-1 0 1-2v-1z" class="B"></path><path d="M538 525c0 2 1 4 3 7l1 1-1 1c1 1 1 2 2 3h0c-1 0-3-2-3-3l-1 1s-1 0-1-1c-1-1-1-4 0-6h0v-1c-1-1 0-1 0-2z" class="X"></path><path d="M329 160c2 1 3 2 5 3v1h4l-2 1h0l1 1v1h0c-1 0-1 1-2 1v1-1c-1-1-1-1-2-1-1-1-3-3-3-5 0-1 0-1-1-2z" class="AK"></path><path d="M261 206l3-9c0 1 0 2 1 3v1 5h-1c-1 1-1 2-1 2l-1 1c0-2 0-2-1-3z" class="M"></path><path d="M265 201v5h-1c-1 1-1 2-1 2l-1-2c0-2 2-4 3-5z" class="d"></path><path d="M320 158l-1 5 1 4v1c-1-1-2-2-3-2v1h-1l-1-1h1c0-1-1-2-2-2l2-1c2-1 3-3 4-5z" class="M"></path><path d="M319 163l1 4v1c-1-1-2-2-3-2v1h-1l-1-1h1 1v-2c1-1 1-1 2-1zM85 234c-1 3-1 7-2 10-1 2-1 5-2 7l-1 1 3-17 2-1z" class="H"></path><path d="M282 101c3-3 10-7 15-8-4 2-7 5-11 7h0l-3 3-1-1v-1z" class="AF"></path><path d="M364 164l5-4-2 5-2 4v1l-1-1h-1v1-2h-1v-1-1h-1 2l2-2h-1z" class="AM"></path><path d="M363 166c1 0 2 1 2 2l-1 1h1v1l-1-1h-1v1-2h-1v-1-1h-1 2z" class="AK"></path><path d="M487 586c0-4-2-6-4-9 3 2 6 4 7 8v5h-3 0v-4z" class="f"></path><path d="M266 156h4c0 1 0 3 1 4h-3c0 1 1 1 1 2 0 2 2 3 2 5l-1 1c-2-4-3-8-4-12z" class="P"></path><path d="M184 501c2-1 3-1 5-1h0c-1 1-1 1-2 1l-2 2h0-2c-4 1-7 3-9 6h0-1l-1-1 1-1 1 1c1-1 1-2 2-2v-1h-1-1 0 0 2c0-1 1-1 1-2 3 0 5-1 7-2z" class="l"></path><path d="M374 136c0-4-1-8-2-13 3 5 6 12 6 18h-1l-1-4h0-1c0-1 0-2-1-4h0v3z" class="AO"></path><path d="M188 498s1 1 2 1c2 0 3 1 4 3-3-1-6 1-8 3h-1l-2-1 2-1h0l2-2c1 0 1 0 2-1h0c-2 0-3 0-5 1l4-3z" class="h"></path><path d="M246 613c3-1 6-2 7-5 1-2 1-3 1-5h0c-1-2-2-3-3-4 1 0 1 0 2 1 2 1 2 3 2 4 0 3 0 5-2 7-1 1-2 1-3 2-1 0-1-1-2-1-1 1-2 1-2 1z" class="X"></path><path d="M418 614l1-1 1 1c2 1 3 1 6 1h0l-1 2c0 1 1 2 2 2v1l-1 1c-1 0-1-1-2-2h-1l-3-2c0-2-1-2-2-3z" class="i"></path><path d="M423 619h4v1l-1 1c-1 0-1-1-2-2h-1z" class="T"></path><path d="M438 634s1-1 1-2c1 3 3 5 5 7 1 1 1 0 2 0h1c-1 2-2 3-3 4l-2-2-4-7z" class="D"></path><path d="M159 495c5 1 9 0 14-2l1 2c-4 2-11 4-15 2h-1 5c-1-1-2-1-4-2z" class="z"></path><path d="M141 453h2c5-4 11-4 17-2h-3-2c-1 0-4 0-5 1-2 1-5 1-6 2-1 0-2 0-3-1z" class="AG"></path><path d="M185 546c0-2-1-3 0-5v-2c-3-1-4-1-6-3h0 2c3 1 5 0 8-1-1 2-1 3-2 5-1 1-1 2-1 4 0 0 0 1-1 1v1z" class="Ag"></path><path d="M267 211v-1-1c2-3 2-8 2-11l2 5c-1 1-1 3-1 4v1h2c0 1-1 2-1 3h-1v1c-1 0-1 0-2 1h0v-1c-1 0-1-1-1-1z" class="X"></path><path d="M268 213v-1c0-1 1-2 2-3v1 1 1c-1 0-1 0-2 1h0z" class="j"></path><path d="M336 654l2 2c1 0 1 1 2 1 0 2 0 2-1 4v1s-1 0-1 1c1 1 1 1 2 1-2 1-5 1-7 1l3-2c1-3 1-6 0-8v-1z" class="e"></path><path d="M465 599h0v3h1c-1 4-2 8-6 11h-1-1l-2-1c1 0 3-1 5-2 3-3 4-7 4-11z" class="S"></path><path d="M201 67c-2-3-6-4-9-6 5 1 10 1 15 1v1h2-1 0 1c-2 1-6 1-7 2h-1v2z" class="D"></path><path d="M74 273c1 1 1 1 1 2l-3 6v2 1c-1 0-1 0-1 1v1h0c-2 3-5 4-8 5 5-5 8-11 11-18z" class="N"></path><path d="M459 56h2c1-1 2-2 3-2l1 1h1v1h0c-1 1-2 1-2 2 1 0 1 1 1 2-2 1-6 0-9-1l3-3z" class="m"></path><path d="M465 55h1v1h0c-1 1-2 1-2 2h0-4c0-1 4-2 5-3h0z" class="E"></path><path d="M268 145h0c2 3 1 7 2 11h-4v-6c0-1-1-2 0-3l2-2z" class="H"></path><path d="M271 167c0-2-2-3-2-5 0-1-1-1-1-2h3l4 7v3c1 1 2 1 2 2h-1l-5-5z" class="AQ"></path><path d="M470 55v-1c6 7 16 7 24 8v2c-5-1-11-2-16-4-3-1-5-2-7-3 0 0-1-1-1-2z" class="D"></path><path d="M233 599s0 1 1 0c1 4 2 9 5 11 2 2 4 2 5 2-1 1-2 0-3 1-1 0-1 0-1 1-1-1-2-1-3-2-4-4-4-8-4-13z" class="f"></path><path d="M453 615c-2-1-3-1-5-2s-4-4-5-7c0-2 0-3 1-5 1-1 2-2 4-2h-1c-1 2-2 3-2 4 0 2 0 4 1 6 3 2 5 3 8 3h1v1c-1 0-2 0-3 1h1v1z" class="g"></path><path d="M159 534h1v1c1-1 2-2 2-3 1 5 1 8 2 13h0l1 3h0c-1-1-2-2-2-3l-1 1h0c-1-4-3-7-3-11h0v-1z" class="S"></path><path d="M444 213v-6h0c2 2 3 6 6 7h1 4c-1 1-1 2-2 2-2 1-4 2-6 2h0v-5h0-1-2z" class="M"></path><path d="M447 218h0v-5c2 2 3 2 5 2 0 1 1 1 1 1-2 1-4 2-6 2z" class="n"></path><path d="M181 497l2-2 5 3-4 3c-2 1-4 2-7 2 2-2 3-4 4-6z" class="V"></path><path d="M183 504l2 1c-1 2-2 4-2 6l-1 1h0c-1 2 0 5 0 7-1 0-1 1-2 1v-1c-1-2-1-4-1-7 1-3 2-5 4-8z" class="g"></path><path d="M179 512c1 0 2 1 2 2v4l-1 1c-1-2-1-4-1-7z" class="f"></path><path d="M540 451c6-2 11-1 16 2 2 2 5 4 6 7v1h0 0l-1-1c-2-3-5-4-8-6v-1c-1 0-3-2-4-2h-5c-1-1-3 0-4 0z" class="X"></path><path d="M541 86h4c1-1 2-2 2-3l3-2c-1 5-1 7 1 12h-1c-1 0-1-1-1-2h0c-1-1-1-1-1-2v-1h-4-1c-3 0-5 0-8-1h3c1 0 2 0 3-1z" class="E"></path><path d="M158 497h-1c-5-1-9-3-12-7-2-3-2-7-2-10 1 3 2 7 4 9 3 4 7 6 12 6 2 1 3 1 4 2h-5z" class="AG"></path><path d="M322 136c1-4 2-8 4-12 0 2-1 4-1 6-1 7 0 14 1 21h-1c0-1 0-2-1-3 1-1 1-1 0-1v-2-1 4l1 3v3l-1-2c-1-5-1-9-1-14v-3l-1 1h0z" class="D"></path><path d="M189 545h0c0 3 2 5 4 6 0 1 0 1 1 1 2-1 4-3 5-3-2 2-4 4-5 6v1c-1 2 0 5 1 8l1 2c-2-2-4-5-4-9 0-2 0-3 1-4-2-1-4-2-6-4l1-2c0-1 0-1 1-2z" class="S"></path><path d="M477 598h0c2-1 4-1 6-2s3-2 5-2c-3 3-7 6-11 6-2 1-3 1-5 0-1 0-3 0-5-1h5l-1-1v-1c2 0 4 1 6 1z" class="K"></path><path d="M471 597c2 0 4 1 6 1-1 1-2 1-4 1h-1l-1-1v-1z" class="T"></path><path d="M421 106c3 3 8 11 9 15h-1v4c-1-1-1-2-3-3l1-2v-2c-3-3-5-7-7-10h0c1-1 1-1 1-2z" class="x"></path><path d="M179 491c1 1 1 1 1 2 0 2 0 2 1 4-1 2-2 4-4 6 0 1-1 1-1 2h-2c1-2 3-3 3-5v-2l-3 1 2-3 1-2h-1l3-3z" class="K"></path><path d="M179 491c1 1 1 1 1 2-1 0-2 1-3 1h0-1l3-3zm-3 5h2v2h-1l-3 1 2-3z" class="AG"></path><path d="M491 538c1 1 1 0 1 1 1-1 2-1 3-2 1 0 2-1 3-1 1-1 2-1 4 0-4 1-8 4-10 7-1 1-1 2-2 3l-1-1s-1 0-1 1v-1c0-2 1-5 3-7z" class="K"></path><path d="M259 634h0c0 1 0 1 1 1l-2 3c0 1-2 4-1 5h0c-1 2-2 3-4 4-1 2-1 5-1 8 0 1-1 2-1 3v-9-1c1-2 2-3 4-5-1-1-2-2-3-4h3l2-2 2-3z" class="AA"></path><path d="M129 348c0 4 0 7-3 10s-7 5-11 5c-2 0-4 0-6-1h1 1c0 1 2 0 3 0h0 4l2-1h1c1-1 2-1 3-2s3-3 3-4v-2h0l-2 2c-1 2-2 3-4 4-1 1-2 1-3 2h0c-2 0-5 1-6 0h-1c5 0 8-1 11-4 3-2 4-5 5-8l2-1z" class="a"></path><path d="M515 503l1 2c3 4 4 7 3 12 0 1 0 2-1 4l-1-1h-2c1-3 2-6 1-9l-1-2c0-2-1-4 0-6z" class="AG"></path><path d="M189 388c-2-2-3-4-5-7 2 2 4 4 7 5h3l-1 1v1c2 0 4-2 5-1l-2 2c0 1 0 2 1 2h-1c-1 1-1 2-2 3h-2l1-2-1-1-1-1 1-1h2v-1c-1 0-3 1-4 1l-1-1z" class="o"></path><path d="M252 212v-1c1-1 2-2 3-4 0 2-1 6 0 7l3-2 1 2-1 2c-1 1-1 2-1 3h-1 0c-1-1-1-1-2-1v1l-1 1v1h-1v-1-1-3c0-1 0-2 1-3h0c0-1 0 0-1-1h0z" class="E"></path><path d="M255 214l3-2 1 2-1 2c-1 1-1 2-1 3h-1 0c-1-1-1-1-2-1h1l-1-2h-1v-1l2-1z" class="F"></path><path d="M465 53c1-1 2-2 3-2l2 4c0 1 1 2 1 2v1c1 1 2 2 3 2 0 1 0 1-1 1h-2c-1 0-4 0-5-1 1-1 1-1 1-2h-1s0 1-1 2c0-1 0-2-1-2 0-1 1-1 2-2h0v-1h-1l-1-1 1-1z" class="AF"></path><path d="M466 58l1-1h0c2 1 2 1 2 3l2 1c-1 0-4 0-5-1 1-1 1-1 1-2h-1z" class="AL"></path><path d="M180 520c1 0 1-1 2-1 0-2-1-5 0-7h0c0 4 0 8 3 11l3 3s0 1 1 1c1 1 1 1 2 1l-1 1h1c1 0 1 1 2 1v1c-1 0-1 0-1-1-6-1-9-5-12-10z" class="Y"></path><path d="M262 643c1 0 2-1 3-1-1 2-2 4-2 6s1 4 3 5c1 1 2 1 3 0 1 0 2 0 3-1l1 1v1c-1 0-2 1-3 2h-1l1 6v-2c-1-1-1-3-2-4-1 0-3-1-3-1-1 0-1 1-2 1l-1-3h1l1 1v-1c-1-1-2-3-2-4-1-1 0-2-1-3l-1-1 2-2z" class="AG"></path><path d="M270 117l1 2c-1 4-1 7-2 11 0 1-1 1-1 2v6 7h0c0-1 0-5-1-6h-1l-1-3c1-6 3-13 5-19z" class="U"></path><path d="M420 55c7-1 13-1 20-1h15l1 1h2 0c-1 1 1 0-1 1h-1-2c-3 1-6 0-9-1-4 0-14-2-16 0 1 0 3 0 4 1h-11s-1 0-1-1h-1z" class="M"></path><defs><linearGradient id="A" x1="429.4" y1="147.134" x2="431.651" y2="159.509" xlink:href="#B"><stop offset="0" stop-color="#453d3b"></stop><stop offset="1" stop-color="#5e5750"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M430 150h0c0-2 0-4 1-6v2h1 1v3c0 6-1 10-3 15h-1c0-2 1-3 1-4h0c-1 1-2 1-3 1 1-2 2-3 2-5l1-6h0z"></path><path d="M461 591c1 1 2 2 4 3h1l5 3v1l1 1h-5-1v3h-1v-3h0l-1-1h-1c-1 1-1 2-1 4-1-2-1-4-2-6 1 0 1 0 1-1v-3-1z" class="Q"></path><path d="M433 56c-1-1-3-1-4-1 2-2 12 0 16 0 3 1 6 2 9 1l1 1h-1c0 1-1 2-1 3l-1 1-3-2c-5-1-10-3-16-3z" class="V"></path><path d="M451 58l1-1h2c0 1-1 2-1 3l-2-2z" class="n"></path><path d="M451 58l2 2-1 1-3-2c1-1 1-1 2-1z" class="u"></path><path d="M542 533c4 3 5 4 10 4 5-1 8-4 12-7-2 4-6 7-10 8l-6 1v1l1 2-2-1c-1 0-4 4-5 5 1-1 1-3 2-4s2-2 2-3c-1 0-2-1-3-2h0c-1-1-1-2-2-3l1-1z" class="S"></path><path d="M418 109c-2-4-5-8-9-11-2-2-4-3-6-5 3 1 6 3 10 5a30.44 30.44 0 0 1 8 8c0 1 0 1-1 2h0l-1 1h0-1z" class="P"></path><path d="M413 98a30.44 30.44 0 0 1 8 8c0 1 0 1-1 2h0l-1 1h0c-1-4-5-7-6-11z" class="AF"></path><path d="M455 65l1 1h0 1c1 1 2 1 3 2 1 0 4 2 5 3s-1 3 0 4h0c0 2-1 3-1 5l-2-5c-1-2-2-4-4-5l-1 1-1-1c-1-2-1-3-3-4l2-1z" class="a"></path><path d="M520 76c6 8 12 8 21 10-1 1-2 1-3 1h-3c-1 0-2-1-3-2-1 0-2 0-3-1h-2l-1 1c-1 0-4-2-6-2v1l-1-1v-1c-1-1-1-1-1-2h0l1 1h1v-2-3z" class="C"></path><path d="M487 586v4h0 3c-1 1-1 2-2 4-2 0-3 1-5 2s-4 1-6 2h0c-2 0-4-1-6-1l-5-3h1 0c2 1 4 2 5 2h1c2 0 4 0 6-1 0-1 3-2 4-2 3-2 3-4 4-7z" class="AI"></path><path d="M487 586v4h0c-1 3-3 5-6 6-2 1-6 1-9 0h1c2 0 4 0 6-1 0-1 3-2 4-2 3-2 3-4 4-7z" class="X"></path><defs><linearGradient id="C" x1="498.036" y1="533.522" x2="498.034" y2="537.197" xlink:href="#B"><stop offset="0" stop-color="#43161a"></stop><stop offset="1" stop-color="#3f261d"></stop></linearGradient></defs><path fill="url(#C)" d="M504 532l8 4v2 1c-1 0-1-1-3-1l-1-1-1-1c-1 0-1 0-3-1v1h-2c-2-1-3-1-4 0-1 0-2 1-3 1-1 1-2 1-3 2 0-1 0 0-1-1 3-3 6-4 9-5h2 1s1 0 1-1z"></path><path d="M515 520h2l1 1c-2 3-4 7-8 9-2 0-3 1-4 1-2 0-4-1-6-1 1 0 2-1 2-1l3-1c5-2 8-3 10-8z" class="Y"></path><path d="M528 494c6 2 13 2 19-1 4-2 6-6 7-10v-3h1c0 4-1 8-4 12s-10 5-15 6c-3 0-6-1-9-2 1-1 1-1 1-2z" class="z"></path><defs><linearGradient id="D" x1="627.414" y1="286.916" x2="629.59" y2="281.047" xlink:href="#B"><stop offset="0" stop-color="#433f3d"></stop><stop offset="1" stop-color="#61554d"></stop></linearGradient></defs><path fill="url(#D)" d="M621 270v-2h0s0 1 1 1c0 2 1 4 2 6v1c1 0 1 0 1-1 3 6 6 12 10 16-3-1-7-3-10-7l-2-3 2-1-4-10z"></path><path d="M625 280c0 1 1 2 1 3 0 0 0 1-1 1l-2-3 2-1z" class="a"></path><path d="M282 101v1l1 1c-2 4-5 8-7 12v1h-1v-1l1-1-2 2h-1c0 1-1 1-1 2l-1 1-1-2c3-6 7-11 12-16z" class="s"></path><path d="M282 102l1 1c-2 4-5 8-7 12v1h-1v-1l1-1-2 2c1-3 3-6 4-8s3-4 4-6z" class="y"></path><path d="M466 583l1 4c1 3 3 6 7 7 2 0 3 0 5 1-2 1-4 1-6 1h-1c-1 0-3-1-5-2h0-1-1l1-1-4-5c1-1 1-3 2-3h2v-2z" class="e"></path><path d="M466 583l1 4h-1v-1h-2c0 1 0 2 1 3h0c1 1 2 2 2 4h-1l-4-5c1-1 1-3 2-3h2v-2z" class="i"></path><path d="M526 85l1-1h2c1 1 2 1 3 1 1 1 2 2 3 2 3 1 5 1 8 1l1 1-1 2-1 1h-1l-2-1-1 1-2 1c-2-2-5-3-6-5-1-1-2-2-4-3z" class="d"></path><path d="M532 85c1 1 2 2 3 2 3 1 5 1 8 1l1 1-1 2-1 1h-1l-2-1c-2-1-4-3-6-4-1 0-1 0-2-1l1-1z" class="V"></path><path d="M200 537h1c2 1 5 3 6 3l1-1c1 2 2 3 2 5s-1 4-2 6l1 2-1 3v4 1h-1-1c-5-8 3-9 2-14-1-4-5-7-8-9z" class="i"></path><path d="M207 553l1-3 1 2-1 3c-1-1-1-1-1-2z" class="AN"></path><path d="M207 553c0 1 0 1 1 2v4 1h-1c-1-3-1-5 0-7z" class="AI"></path><path d="M236 592h1l-2 2h1s1 0 1-1l1 2c0 1 0 3-1 3h-1l-1-1-1 1v1c-1 1-1 0-1 0h-1 0 0-1c-3 2-8 2-11 1l1-1h1 0l1-1h-2c1 0 2 0 3-1h1 1 1c3-1 6-3 9-5z" class="T"></path><path d="M81 251h1c0 1 0 1 1 2l-2 3c-1 3-1 6-2 8-1 6-2 12-6 17 0 1-1 2-1 2v-2l3-6c0-1 0-1-1-2l6-21 1-1z" class="Z"></path><path d="M137 464l-1 1h0c-1 3-1 5 0 8-2-3-2-6-2-9 1-4 4-8 7-11 1 1 2 1 3 1v1h0 1c1 0 2 0 3 1h0l-4 1v1c-3 2-5 3-7 6z" class="X"></path><path d="M137 461v-1c1-2 5-5 7-5h0 1c1 0 2 0 3 1h0l-4 1c-2 0-2 0-3 1h0c-2 1-3 2-4 3z" class="Ai"></path><path d="M137 461c1-1 2-2 4-3h0c1-1 1-1 3-1v1c-3 2-5 3-7 6l-1-1 1-2z" class="Y"></path><path d="M534 523l1 1h0c0 1 1 3 1 4v1l2-1c-1 2-1 5 0 6 0 1 1 1 1 1 0 3-1 7-2 9-3 7-7 11-14 14 4-3 7-6 9-10 5-8 5-16 2-25z" class="f"></path><defs><linearGradient id="E" x1="190.129" y1="358.399" x2="196.358" y2="363.025" xlink:href="#B"><stop offset="0" stop-color="#504742"></stop><stop offset="1" stop-color="#71655c"></stop></linearGradient></defs><path fill="url(#E)" d="M193 361l-1 1c-4 3-6 5-7 10h-1c0-5 2-10 5-13 2-3 6-4 10-4h3v1l1 1-2 1c-3 0-5-1-7 2 0 0-1 0-1 1z"></path><path d="M512 536h0c3 1 6 0 8-1-2 2-4 3-7 4 0 0 0 1 1 1 0 2-1 6-1 7-2 3-5 5-7 6h-1v1c1 1 1 4 1 6-1 2-2 4-4 7 1-3 3-6 3-10h0c0-3-4-6-6-8l5 3c2-1 4-3 5-5v1l1-1c1-1 1-1 1-3s0-3-2-6c2 0 2 1 3 1v-1-2z" class="S"></path><path d="M426 122c2 1 2 2 3 3v-4h1c3 9 5 19 3 28v-3h-1-1v-2c0-6-1-13-3-19-1 2 0 4-1 6 0-3 0-6-1-9z" class="U"></path><path d="M500 70c5 1 11 5 15 2 2-1 2-2 3-3 0 2 1 5 2 7v3h-1-1l-1-1v2h-1l-1-1-2-2h0l-14-7h1z" class="H"></path><path d="M516 75h1c1 0 1 2 2 4h-1l-1-1v2h-1l-1-1-2-2h0c1-1 2-1 3-2z" class="AZ"></path><path d="M513 77c1-1 2-1 3-2 1 1 0 2 0 3l-1 1-2-2h0z" class="n"></path><path d="M618 166v-1l8 15c2 3 4 6 5 9 2 5 3 11 4 16-2-4-3-8-5-12v-2l-12-20c0-1 0-2 1-2l-1-3z" class="a"></path><path d="M618 171c0-1 0-2 1-2l10 18c1 1 1 2 1 4l-12-20z" class="s"></path><path d="M266 56c2 0 6-1 8 0l1 1c-10 1-20 3-29 8h-1v-1l2-3c0-1 1-1 2-2l16-3h1zm284 37h1c3 3 7 6 10 9l13 11h-1-1l2 2h-1l2 3h-1l1 2-9-7v-3c-1-2-2-2-2-4l-8-7c-3-2-4-4-6-6z" class="W"></path><defs><linearGradient id="F" x1="567.092" y1="112.527" x2="573.737" y2="115.478" xlink:href="#B"><stop offset="0" stop-color="#85796d"></stop><stop offset="1" stop-color="#a3968c"></stop></linearGradient></defs><path fill="url(#F)" d="M564 106l9 9 2 3h-1l1 2-9-7v-3c-1-2-2-2-2-4z"></path><path d="M433 642c2 0 4 2 6 1v1c0 1 1 2 0 3l-1 2v2c0 1-2 4-3 5-1 0-2-1-3 0-2 1-2 2-3 3 0 1 0 1-1 2 0-1 2-3 1-5-2-1-3-2-4-4-2-3-1-5 0-8h1 0v1c-1 2-1 4 0 5 1 2 2 2 3 3 2 0 3 1 4 0s2-2 3-4c0-3-1-4-3-7z" class="X"></path><path d="M438 649h-1c0-1 1-2 0-3v-1l2-1c0 1 1 2 0 3l-1 2z" class="u"></path><path d="M163 85l5-1c4-1 9-4 10-7l1-1c1-2 1-5 1-7 1 1 2 1 2 2l-1 1c-1 0-1 1 0 2h0 1c-1 2-1 4-2 5h0l-1 4c-1 0-1 1-2 1l-4 2-5 3v-1-1h0c0-1-1-1-2-1s-2 0-2 1l-2 1h-2l3-2v-1h0z" class="E"></path><path d="M166 86l2-1h2l2-1c4-1 5-2 8-5l-1 4c-1 0-1 1-2 1l-4 2-5 3v-1-1h0c0-1-1-1-2-1z" class="M"></path><path d="M320 644v2c1 0 2 0 2 1h1 0c1 0 2-1 2-2s1-1 2-2v1h0c0 1 0 1-1 2h0v1l-4 4h-1l-1 1-1 1c-1 0-2 1-3 1h0c-1 1-2 0-2 0l-1 1c-2 1-7-1-10 0-1 0-3-2-5-3h0c5 1 11 2 16 0 3-2 5-5 6-8h0z" class="Y"></path><defs><linearGradient id="G" x1="80.569" y1="279.184" x2="76.995" y2="257.277" xlink:href="#B"><stop offset="0" stop-color="#1d1a1b"></stop><stop offset="1" stop-color="#464440"></stop></linearGradient></defs><path fill="url(#G)" d="M85 247v3h1l-3 11-2 6c0 1-1 2-1 3s1 1 1 1c-2 5-6 11-10 15v-1c0-1 0-1 1-1v-1s1-1 1-2c4-5 5-11 6-17 1-2 1-5 2-8h1c2-2 2-6 3-9z"></path><path d="M497 532h2l1 1c-3 1-6 2-9 5-2 2-3 5-3 7v1c0-1 1-1 1-1l1 1v1c1 2 2 5 3 7 0 0 1 1 1 2 0 2-2 4-3 5 0 1 0 1-1 2h0c-2 2-4 3-6 3 1-1 3-2 4-4v-1-1c5-4 0-9-1-14 0-2 0-3 1-5 1-4 5-7 9-9z" class="AI"></path><path d="M488 546c0-1 1-1 1-1l1 1v1c1 2 2 5 3 7 0 0 1 1 1 2 0 2-2 4-3 5 0 1 0 1-1 2v-1l1-1c0-2 1-3 1-4 0-4-3-8-4-11h0z" class="v"></path><path d="M189 535c3-2 6-3 9-3 4 2 7 4 10 7l-1 1c-1 0-4-2-6-3h-1c-3-1-4-1-7 0-2 1-4 2-4 5-1 1-1 2 0 3-1 1-1 1-1 2l-1 2c-1-1-1-2-2-3v-1c1 0 1-1 1-1 0-2 0-3 1-4 1-2 1-3 2-5z" class="o"></path><path d="M185 546v-1c1 0 1-1 1-1 0-2 0-3 1-4 0 2 0 4 1 7l-1 2c-1-1-1-2-2-3z" class="AD"></path><path d="M471 57c2 1 4 2 7 3 5 2 11 3 16 4h0 2l1 1c-1 1-1 2 0 4l-3-1-1 1h0v1l-1-1-4-2c-3-1-5-1-8-2h-1l1-1v-1c-2 0-4-1-6-2h-1c1 0 1 0 1-1-1 0-2-1-3-2v-1z" class="AM"></path><path d="M480 63h1l1 1c1 0 3 0 4 1 1 0 2 1 4 1-1 0-2 1-2 1-3-1-5-1-8-2h-1l1-1v-1z" class="p"></path><path d="M494 64h2l1 1c-1 1-1 2 0 4l-3-1-1 1h0v1l-1-1-4-2s1-1 2-1l2 1c1 0 1 0 2-1v-2h0z" class="k"></path><path d="M211 593l-1-1c-1-2-2-4-1-7 1-4 3-6 7-8-2 2-3 4-4 6s-1 5 0 7c2 2 3 3 6 4s6 1 9-1 5-5 6-9c1 1 2 2 2 4 0 0-1 1-1 2h1c-1 2-3 3-4 4h-1l-2 1s-1 0-1 1h-1-3v1c-1-1-2 0-3-1h-1-1l-1-1h0c-1 0-1 0-2-1-1 0-3-2-3-3h0c-1-1-1-1-1-2-1 1-1 1-1 2l1 1v1h0z" class="e"></path><path d="M426 644c1 0 1 0 1-1l1-1h1c1 0 2 0 3-1 1 0 1 0 1 1 2 3 3 4 3 7-1 2-2 3-3 4s-2 0-4 0c-1-1-2-1-3-3-1-1-1-3 0-5v-1z" class="V"></path><path d="M427 131c1-2 0-4 1-6 2 6 3 13 3 19-1 2-1 4-1 6h0-1c0 1-1 1-1 3-1 1-1 2-2 3 0 1-1 3-2 4v-3c-1-1-2-1-3-2h0c2-2 2-4 3-6l2 1c1-7 2-12 1-19z" class="F"></path><path d="M424 149l2 1-2 7h0c-1-1-2-1-3-2h0c2-2 2-4 3-6z" class="M"></path><path d="M228 47l1 3s1 0 1 2h0v5c-2 1-4 3-6 3v1c-1 1-3 1-5 2l-8 3-2 1h-1l-2 1h-1c-1-1-1-1-3-1l1-1-1-1c1-1 5-1 7-2 3 0 5-1 8-2l6-2v-1c1-2 3-2 4-4l1-7z" class="AK"></path><path d="M227 54c1 0 1 0 2 1 0 1 0 1-1 2s-3 2-5 2v-1c1-2 3-2 4-4z" class="P"></path><path d="M217 61l1 1c-1 1-4 2-5 2-2 1-3 1-4 1-2 1-3 1-4 2l1 1h-1c-1-1-1-1-3-1l1-1-1-1c1-1 5-1 7-2 3 0 5-1 8-2z" class="p"></path><path d="M466 58h1c0 1 0 1-1 2 1 1 4 1 5 1h2 1c2 1 4 2 6 2v1l-1 1h1-4-5-2l-2 3-8-6h-2l-1-1v-2c3 1 7 2 9 1 1-1 1-2 1-2z" class="V"></path><path d="M474 61c2 1 4 2 6 2v1l-1 1-8-2h3 0 2 0l-2-1v-1z" class="u"></path><path d="M459 62c2 1 5 1 7 1h5l8 2h1-4-5-2l-2 3-8-6z" class="R"></path><path d="M265 642c1-1 4-1 5 0 1 0 1 0 2 1l1 1h0c0 3 1 5-1 7v1c-1 1-2 1-3 1-1 1-2 1-3 0-2-1-3-3-3-5s1-4 2-6z" class="n"></path><path d="M240 212c1 1 3 2 4 2 3 1 5-1 8-2h0c1 1 1 0 1 1h0c-1 1-1 2-1 3v3 1l-2-2c-1 1-1 1-1 3v1c-1-1-2-1-4-2h0-1l3 3-1 1s-1-1-2-1h0c-1-1-1-2-2-3-1 1-1 1-2 0h0c-2-1-3-2-4-4v-1l1-1v1l1-1h1c1 0 2 1 2 1h1 1 0c-1-1-2-1-3-2v-1z" class="P"></path><path d="M238 214h1l2 2h-1 0l-3-1 1-1z" class="M"></path><path d="M250 218c-1-1-2-2-4-3 2 0 3-1 5-1 0-1 1-1 2-1h0c-1 1-1 2-1 3v3 1l-2-2z" class="V"></path><path d="M244 223h0c-1-1-1-2-2-3-1 1-1 1-2 0h0c-2-1-3-2-4-4v-1l1-1v1l3 1c0 1 2 2 2 2 1 0 2-1 2 0 2 1 3 2 4 2l1 1v1c-1-1-2-1-4-2h0-1l3 3-1 1s-1-1-2-1z" class="U"></path><path d="M265 207c0-2 0-3 1-4v8l-1 1h0c1 2 2 2 3 3l2 3-1 1v-1l-2 2c1 2 1 2 2 3l-1 1-1-1h-1l-3-3c-1-1-1-1-3-1v-1l-2 1h-1c0-1 0-2 1-3l1-2h0c1-2 3-3 3-5l1-1s0-1 1-2h1v1z" class="D"></path><path d="M263 217h1l-1 1h0v2c-1-1-1-1-3-1v-1h0l1-1h2z" class="R"></path><path d="M267 223c-1-2-1-4-1-6h1s1 1 2 1l-2 2c1 2 1 2 2 3l-1 1-1-1z" class="F"></path><path d="M263 208s0-1 1-2h1v1c-1 2-1 5 0 8v2 1l-1-1h-1-2l-1 1h0l-2 1h-1c0-1 0-2 1-3l1-2h0c1-2 3-3 3-5l1-1z" class="m"></path><path d="M259 214c1 1 1 2 2 2s1-3 1-5c1 2 1 4 1 6h-2l-1 1h0l-2 1h-1c0-1 0-2 1-3l1-2h0z" class="M"></path><path d="M453 66c-8-4-17-8-26-9h-12s4-1 5-2h1c0 1 1 1 1 1h11c6 0 11 2 16 3l3 2h0c1 1 2 3 3 4l-2 1z" class="J"></path><path d="M236 573h1l1 2h1l1 2c1 0 2 1 2 1l1 2c0 1 1 1 0 2 0 2-1 2-2 4s-3 4-4 6h-1c-3 2-6 4-9 5h-1-1-1c-1 1-2 1-3 1-2 0-4-1-5-2-1 0-2 0-2-1l-1-1c-1 0-1-1-2-1v-1l-1-1c0-1 0-1 1-2 0 1 0 1 1 2h0c0 1 2 3 3 3 1 1 1 1 2 1h0l1 1h1 1c1 1 2 0 3 1v-1h3 1c0-1 1-1 1-1l2-1h1c1-1 3-2 4-4h0c3-5 3-10 2-15l-1-2z" class="z"></path><path d="M238 575h1l1 2c1 0 2 1 2 1l1 2c0 1 1 1 0 2 0 2-1 2-2 4s-3 4-4 6h-1c3-5 3-11 2-17z" class="L"></path><path d="M240 577c1 0 2 1 2 1l1 2c0 1 1 1 0 2 0 2-1 2-2 4-1-2-1-4-1-6v-3z" class="e"></path><path d="M242 578l1 2c0 1 1 1 0 2h0l-1-1v-3z" class="T"></path><path d="M240 580l1 3h0c1-1 1-1 1-2l1 1h0c0 2-1 2-2 4-1-2-1-4-1-6z" class="Y"></path><defs><linearGradient id="H" x1="244.247" y1="60.22" x2="245.939" y2="52.945" xlink:href="#B"><stop offset="0" stop-color="#524a45"></stop><stop offset="1" stop-color="#826f64"></stop></linearGradient></defs><path fill="url(#H)" d="M228 47h1c2 4 4 6 8 7s9 0 13 0h13 2v1h-6 6 0v1l-16 3c-1 1-2 1-2 2-2 1-2 1-4 0l-1-2v-1c-1-1-2-2-3-2l-3-1c-1-1-2-1-3-2-1 0-1 0-1-1-1 0-1 0-1 1v1h-1v-2h0c0-2-1-2-1-2l-1-3z"></path><path d="M259 55h6 0v1l-16 3-2 1h-1c-1-1-1-2-1-3 1-1 3 0 4-1 4 0 7-1 10-1z" class="AH"></path><path d="M245 57h3c0 1-1 2-1 3h-1c-1-1-1-2-1-3z" class="n"></path><path d="M360 651c1-1 2-3 3-4 0-1 0-1 1-1 0 1-3 5-4 7h0 3l2-4c1-1 2-1 3-1l-6 7v7l3 3c-2 0-3 0-5-1-1 0-2-1-3-1l-3 2h0c-3 3-2 6-2 11v2h0l-1 4c-1 0-1-1-1-1v-3c1-3 0-7 0-10v-3c1 0 1-1 1-2l9-12z" class="q"></path><path d="M362 662h-1v-1h-1v-1-3l2-2v7z" class="t"></path><path d="M351 663c0 4 1 9 0 13h1v-1h0c-1-1-1-1-1-2h0v-1c1-2 0-4 1-5v-1c0-1 1-1 2-1-3 3-2 6-2 11v2h0l-1 4c-1 0-1-1-1-1v-3c1-3 0-7 0-10v-3c1 0 1-1 1-2z" class="v"></path><path d="M448 585c1-1 3-1 5-1 1 0 3 1 5 3l3 4v1 3c0 1 0 1-1 1 1 2 1 4 2 6-2 2-4 4-7 5l3-3c0-2 1-3 0-5-1-4-3-5-6-6h3 0c-1 0-1-1-1-2-2-1-4-1-5-1s-2 0-3-1v-1h1l2-1v-1c-1 0-1-1-1-1z" class="AD"></path><path d="M457 591c1 0 2 0 4 1v3c0 1 0 1-1 1-1-2-2-3-3-5zm-3 0c2 2 4 3 5 5 1 3 1 5 0 7l-1 1c0-2 1-3 0-5-1-4-3-5-6-6h3 0c-1 0-1-1-1-2z" class="L"></path><defs><linearGradient id="I" x1="455.868" y1="585.825" x2="453.768" y2="589.432" xlink:href="#B"><stop offset="0" stop-color="#1c0d0e"></stop><stop offset="1" stop-color="#3f1416"></stop></linearGradient></defs><path fill="url(#I)" d="M448 585c1-1 3-1 5-1 1 0 3 1 5 3l3 4v1c-2-1-3-1-4-1-4-2-6-3-10-3l2-1v-1c-1 0-1-1-1-1z"></path><path d="M448 585c1-1 3-1 5-1l-2 1 1 1h-3c-1 0-1-1-1-1z" class="h"></path><path d="M250 218l2 2v1h1c2 1 1 3 2 4h3c1 2 2 2 4 2v1c-1 1-2 1-3 3h0l-1 1-2 4h-2 0c-1-2-1-3-2-5-2-2-3-3-6-5l-1-2-1-1h0c1 0 2 1 2 1l1-1-3-3h1 0c2 1 3 1 4 2v-1c0-2 0-2 1-3z" class="O"></path><path d="M258 225c1 2 2 2 4 2v1c-1 1-2 1-3 3v-2c-1 0-1 0-2-1 0 0-1-1-2-1 2 0 2-1 3-2z" class="M"></path><path d="M250 218l2 2v1h1c2 1 1 3 2 4h3c-1 1-1 2-3 2 0-1 0-1-1-1-2-1-3-3-5-4v-1c0-2 0-2 1-3z" class="H"></path><path d="M252 231c0-1 0-2-1-4h2l3 3c1 0 1 0 2 1h1l-1 1-2 4h-2 0c-1-2-1-3-2-5z" class="u"></path><path d="M254 236v-1-3c0-1 0 0 1-1h2l1 1-2 4h-2 0z" class="n"></path><path d="M130 109l16-14c4-4 4-9 4-14 1 2 2 3 4 5l9-1h0v1l-3 2h-7 0c0 1 1 1 1 2l-1 1-1 1c0 2 1 3-1 4h0c-1 1-3 1-4 2l-11 9-1-1h1l-5 3h-1z" class="D"></path><path d="M136 106l7-7c4-3 7-7 8-13v-1c1 1 1 2 2 3h0c0 1 1 1 1 2l-1 1-1 1c0 2 1 3-1 4h0c-1 1-3 1-4 2l-11 9-1-1h1z" class="k"></path><path d="M147 98c1-1 2-3 3-5 0-1 1-3 2-3h1v1l-1 1c0 2 1 3-1 4h0c-1 1-3 1-4 2z" class="M"></path><path d="M230 52v2h1v-1c0-1 0-1 1-1 0 1 0 1 1 1 1 1 2 1 3 2l3 1c1 0 2 1 3 2v1l1 2-3 1-1 1c-2 0-3 0-5 1-3 0-6-1-8 0h-4c-2 0-2 0-3-1 2-1 4-1 5-2v-1c2 0 4-2 6-3v-5z" class="x"></path><path d="M234 56c2 0 4 1 6 1v1h-5c-1-1-1-1-1-2z" class="O"></path><path d="M228 61v-1l3-3c0 1 1 1 2 2 0 0 1 1 2 1-2 1-5 1-7 1z" class="r"></path><path d="M235 60c2 0 5-1 7-1l1 2-3 1-1 1c-2 0-3 0-5 1-3 0-6-1-8 0h-4c-2 0-2 0-3-1 2-1 4-1 5-2h4c2 0 5 0 7-1z" class="n"></path><path d="M166 86c1 0 2 0 2 1h0v1 1l-8 4c-1 0-2 0-3 1-1 0-1 1-2 2h-1c-1 1-2 2-4 3l1 1h0c-4 2-8 4-11 7-2 1-4 4-6 4h0 0-1c1-1 2-3 3-4l11-9c1-1 3-1 4-2h0c2-1 1-2 1-4l1-1 1-1c0-1-1-1-1-2h0 7 2l2-1c0-1 1-1 2-1z" class="C"></path><path d="M166 86c1 0 2 0 2 1h0v1 1l-8 4c-1 0-2 0-3 1-1 0-1 1-2 2v-5-3c2 0 5 1 7 0l2-1c0-1 1-1 2-1z" class="O"></path><path d="M164 87h2v1c-1 0-1 0-2 1-2 0-7 4-8 4v-1l-1-1v-3c2 0 5 1 7 0l2-1z" class="n"></path><path d="M346 666h2c1-4-6-8-8-11-1-1-5-10-5-11h1l7 8c0 1 2 2 2 3 1 2 3 4 5 5 1-1 2-2 4-3 2-2 3-5 5-6h1l-9 12c0 1 0 2-1 2v3c0 3 1 7 0 10v-3h-1l-1 4c-1 0-1 0-2-1 1-3 1-8 0-12z" class="l"></path><defs><linearGradient id="J" x1="501.053" y1="357.215" x2="498.646" y2="365.07" xlink:href="#B"><stop offset="0" stop-color="#3f3636"></stop><stop offset="1" stop-color="#5e554d"></stop></linearGradient></defs><path fill="url(#J)" d="M495 354c2 0 5 1 8 1h0c4 2 6 3 8 6 2 4 3 8 4 12-1 0-1-1-1-2-3-5-5-8-10-10-2-1-4 0-6 0v-2h-1c-4 0-8 1-11 3v-1h-3l-1-1c1-1 2-2 4-2 1 0 1-1 1-1 3-1 4-1 7-1 0-1 0-1 1-2z"></path><path d="M497 359c2 0 4-1 6 1 1 0 1 0 1 1-2-1-4 0-6 0v-2h-1z" class="J"></path><path d="M503 355c4 2 6 3 8 6h-1c-1-1-3-2-4-2l-1-1c-2-1-7-1-10-1h8v-1-1z" class="P"></path><path d="M495 354c2 0 5 1 8 1h0v1 1h-8c-2 0-5 0-7 1-1 1-1 1-2 1v-1c1 0 1-1 1-1 3-1 4-1 7-1 0-1 0-1 1-2z" class="AQ"></path><path d="M495 354c2 0 5 1 8 1h0v1h-9c0-1 0-1 1-2z" class="E"></path><path d="M433 208v-15c0-2 0-4 1-6 1 7 2 14 5 20 0 2 1 3 2 4l-1 1c0 1 1 4 1 5 0 0 0 1-1 1 0 1 0 1-1 1h-2l-1-1-1 1-1 1-1 2-1-1v-2l-1-1c-1 0-2 1-3 2l-1-1c2-1 2-1 3-2h1v-1c1-1 1-2 2-2 0-2 0-2-1-3h1v-3z" class="d"></path><path d="M434 206c0-1 0-2 1-4 1 1 1 1 1 2v4 1l-2-3z" class="AJ"></path><path d="M436 204c2 3 2 5 4 8 0 1 1 4 1 5 0 0 0 1-1 1 0 1 0 1-1 1h-2l-1-1h0 0c1-2 1-5 0-7v-2h0v-1-4z" class="AK"></path><path d="M437 214c1 0 1 0 1-1h1v2c-1 1-1 1-1 2 1 0 2 0 2 1s0 1-1 1l-1-1-1-2v-2z" class="M"></path><path d="M436 208c1 1 2 2 1 3h0v3 2l1 2 1 1h-2l-1-1h0 0c1-2 1-5 0-7v-2h0v-1z" class="O"></path><path d="M433 208v-1l1-1 2 3h0v2c1 2 1 5 0 7h0 0l-1 1-1 1-1 2-1-1v-2l-1-1c-1 0-2 1-3 2l-1-1c2-1 2-1 3-2h1v-1c1-1 1-2 2-2 0-2 0-2-1-3h1v-3z" class="C"></path><path d="M431 217h1l1-1c1 0 2 0 2-1 1 1 1 2 1 3h0l-1 1-1 1-1 2-1-1v-2l-1-1c-1 0-2 1-3 2l-1-1c2-1 2-1 3-2h1z" class="D"></path><path d="M246 575v4c1 1 3 2 4 3s10 6 10 7 0 1 1 2h0c-1 0-2-1-2-1l-2-1v1l-1 1c-4-1-8-1-11 0-1 1-2 1-3 2h-2l-2 2-1-2c0 1-1 1-1 1h-1l2-2c1-2 3-4 4-6s2-2 2-4c1-1 0-1 0-2 1 0 2 0 3-1v-4z" class="L"></path><path d="M240 593c1-1 2-3 4-3 4-2 8-2 13-1v1l-1 1c-4-1-8-1-11 0-1 1-2 1-3 2h-2z" class="AI"></path><path d="M246 575v4c1 1 3 2 4 3s10 6 10 7 0 1 1 2h0c-1 0-2-1-2-1v-1l-13-8c-3 5-6 8-9 12 0 1-1 1-1 1h-1l2-2c1-2 3-4 4-6s2-2 2-4c1-1 0-1 0-2 1 0 2 0 3-1v-4z" class="l"></path><path d="M441 211c1 1 1 2 3 3v1l1 2-1 1v3 3 1c-1 1-1 2-2 2-2 1-3 1-4 1h-2 0c0 1 0 3 1 4h-1l-1 2h0v1 2l-1-1h0c-1-2-1-3-3-5-1 0-1 0-2-1h0 0v-1h-1l1-1c0-1 1-2 1-3l3-3 1-2 1-1 1-1 1 1h2c1 0 1 0 1-1 1 0 1-1 1-1 0-1-1-4-1-5l1-1z" class="F"></path><path d="M442 220h0 1c-1 3-2 4-4 6l-1-1c0-1 3-2 3-4 1-1 0 1 1-1h0z" class="C"></path><path d="M434 220h1v2c-1 2-2 3-3 4s-1 2-2 2h-1 0c0-1 1-2 1-3l3-3 1-2z" class="N"></path><path d="M432 226c0 1 1 3 1 4 0 2 1 3 2 5v2l-1-1h0c-1-2-1-3-3-5-1 0-1 0-2-1h0 0v-1h-1l1-1h0 1c1 0 1-1 2-2z" class="D"></path><path d="M543 88h1 4v1c0 1 0 1 1 2h0c0 1 0 2 1 2 2 2 3 4 6 6l8 7c0 2 1 2 2 4v3l-6-6-11-7c-3-1-5-1-7-1s-2 0-2-1v-1h2v-1c-1 0-1-1-2-1-1-1-3-2-4-2l2-1 1-1 2 1h1l1-1 1-2-1-1z" class="M"></path><path d="M549 100c0-1-3-4-3-5v-1-3c0 1 0 2 1 3 2 1 3 3 5 4l2 2c1 0 1 1 2 1 0 1-1 0 0 1l2 1c1 0 1 1 1 1 0 1 1 2 1 3l-11-7z" class="E"></path><path d="M543 88h1 4v1h-1v3h0v2c-1-1-1-2-1-3v3 1c0 1 3 4 3 5-3-1-5-1-7-1s-2 0-2-1v-1h2v-1c-1 0-1-1-2-1-1-1-3-2-4-2l2-1 1-1 2 1h1l1-1 1-2-1-1z" class="Z"></path><path d="M541 92h1l1-1c0 1 1 2 2 3v1c0 1-1 2-2 2v1l-1 1c-2 0-2 0-2-1v-1h2v-1c-1 0-1-1-2-1-1-1-3-2-4-2l2-1 1-1 2 1z" class="x"></path><path d="M541 92h1l1-1c0 1 1 2 2 3l-1 1h-3c-1-1-1-1-1-2l1-1z" class="AL"></path><path d="M325 154v-3l-1-3v-4 1 2c1 0 1 0 0 1 1 1 1 2 1 3h1c1 3 1 6 3 9 1 1 1 1 1 2 0 2 2 4 3 5l2 9c1 1 1 2 1 3 0 0 0 1 1 2v1c-1 1-1 1-2 0-1 0-1 0-2-1 0 1 0 1-1 2-1-1-1-2-1-3l-4-4-3-10h1v-4c0-4-2-6-1-10l1 2z" class="G"></path><path d="M330 173c1 2 3 6 5 7v-1-3c1 1 1 2 1 3 0 0 0 1 1 2v1c-1 1-1 1-2 0-1 0-1 0-2-1-1 0-2-2-2-3-1-1-2-3-3-4l2-1z" class="m"></path><path d="M324 152l1 2 1 9c1 3 2 7 4 10l-2 1c1 1 2 3 3 4 0 1 1 3 2 3 0 1 0 1-1 2-1-1-1-2-1-3l-4-4-3-10h1v-4c0-4-2-6-1-10z" class="k"></path><path d="M326 163c1 3 2 7 4 10l-2 1c0-1 0-1-1-2 0-2-1-4-2-5h0c1-1 0-1 1-1h1c-1-1-1-2-1-3z" class="AL"></path><path d="M189 388l1 1c1 0 3-1 4-1v1h-2l-1 1 1 1 1 1-1 2c0 1-1 2-2 3 0 1-2 2-2 2l-2-1h0l-4-2c-6 5-12 12-14 20 0 1 1 2 1 3-1 0-1-1-2-1-1 1-1 4-2 5s-1 2-2 3c0-1 0-2-1-3 1-3 2-7 3-10 3-8 10-15 17-20 2-2 5-3 7-5z" class="z"></path><path d="M192 391l1 1-1 2c0 1-1 2-2 3 0 1-2 2-2 2l-2-1c-1-1-2-2-3-2 3-2 5-4 9-5z" class="V"></path><path d="M269 218v1l1-1 1 1 2 1h8-1c-1 1-2 2-3 2l-1 1c-1 1-3 2-5 3l-3 3-1 1c-2 3-3 5-5 6v1l-1-1-1 1h-1c0-3 2-5 4-7 0-1 0-1-1-2v-1c-2 0-3 0-4-2h-3c-1-1 0-3-2-4v-1l1-1v-1c1 0 1 0 2 1h0 1 1l2-1v1c2 0 2 0 3 1l3 3h1l1 1 1-1c-1-1-1-1-2-3l2-2z" class="B"></path><path d="M280 220h-5l-2 2h-3c-1-1-1-1 0-2h3 8-1z" class="I"></path><path d="M254 218c1 0 1 0 2 1h0 1 1l2-1v1h-1v2h1 0c1 2 1 3 2 4h3c0-1 0-1 1-1l1 1c-1 1-2 1-3 2 0 1 0 2-1 3 0-1 0-1-1-2v-1c-2 0-3 0-4-2h-3c-1-1 0-3-2-4v-1l1-1v-1z" class="N"></path><path d="M260 218v1h-1v2h-1 0v-2l2-1z" class="D"></path><path d="M174 499l3-1v2c0 2-2 3-3 5h0 0 1 1v1c-1 0-1 1-2 2l-1-1-1 1 1 1-2 2c-1 1-2 3-3 4-3 6-5 11-6 17 0 1-1 2-2 3v-1h-1c-2 2-4 4-7 6h1c1 2 2 3 3 5l-4-4c-1 1-3 1-4 1l3-2-4-1v-1h-1l2-1c2 0 4-1 6-1 6-4 7-12 9-18 1-3 3-6 3-9l-1-1h0 0 2 0c1-1 2-3 2-3 0-1-1-1-1-2h3l3-4z" class="z"></path><path d="M518 491c2 0 2 0 3 1v1c0 1 0 1 1 1 0 3 3 7 6 9h0 2 1c-1 1-1 1-1 2s1 2 1 3c1 1 1 1 2 0 0 1 0 1-1 2l6 15c0 1-1 1 0 2v1h0l-2 1v-1c0-1-1-3-1-4h0l-1-1c-2-5-3-8-6-12-1 4-4 8-7 11 1-3 3-5 4-8 0-2 1-3 0-5s-4-5-7-5l-1-2h0 3l-1-2c-1 0-3-4-3-5 1 0 2 0 3 1h1c-1-2-2-4-2-5z" class="AG"></path><path d="M519 500h3c0 1 3 4 3 6-1 0-1 0-2-1h0l-3-3-1-2z" class="L"></path><path d="M516 495c1 0 2 0 3 1h1c1 1 2 2 2 4h-3c-1 0-3-4-3-5z" class="q"></path><defs><linearGradient id="K" x1="226.547" y1="185.264" x2="216.857" y2="196.953" xlink:href="#B"><stop offset="0" stop-color="#3f3536"></stop><stop offset="1" stop-color="#554f4a"></stop></linearGradient></defs><path fill="url(#K)" d="M200 161c0-1 1-1 1-2 2 6 4 11 7 16 3 6 5 12 9 17 3 3 6 7 10 10s8 7 13 10v1c1 1 2 1 3 2h0-1-1s-1-1-2-1h-1-1c-3-1-6-4-8-6-4-4-7-8-11-11-2-2-3-4-5-6v1c-1 0-1 0-2-1l-4-7-1-3h-1c1-1 1-1 3-2l-5-10-3-8z"></path><path d="M205 181c1-1 1-1 3-2l1 4 4 8v1c-1 0-1 0-2-1l-4-7-1-3h-1z" class="V"></path><path d="M205 181c1-1 1-1 3-2l1 4c-1-1-1-1-2-1h0v2l-1-3h-1z" class="r"></path><path d="M327 644h1c0-2 0-3 1-4h1 1 1c1 0 3 2 3 3l1 1h-1c0 1 4 10 5 11 2 3 9 7 8 11h-2l-3-3c-1 0-3 1-3 1-1 0-1 0-2-1 0-1 1-1 1-1v-1c1-2 1-2 1-4-1 0-1-1-2-1l-2-2-6-8c-3 5-7 10-14 11-4 0-9 0-13-2 3-1 8 1 10 0l1-1s1 1 2 0h0c1 0 2-1 3-1l1-1 1-1h1l4-4v-1h0c1-1 1-1 1-2h0z" class="AB"></path><path d="M428 203c1 1 1 3 1 5h0 0v3h3c1 1 1 1 1 3-1 0-1 1-2 2v1h-1v-1l1-1c-1 1-1 0-2 1h0-2c-1-1-4 0-7-1-2 0-5-2-8-2l-2-1h0c-3 0-6-2-8-4l-1-1c-1 0-1 0-2-1 1-1 1-2 2-2h1 1 1l7 2c3 1 9 4 12 2l3-3 2-2z" class="i"></path><path d="M427 209l2 1v1l-2 1h-7 0v-1h0c2 0 2 0 3-1 2 0 3 0 4-1z" class="T"></path><path d="M428 203c1 1 1 3 1 5h0 0l-2 1c-1 1-2 1-4 1v-1l2-1c1-1 1-1 1-3l2-2z" class="j"></path><path d="M410 212v-1c2-1 6 1 8 1 2 1 3 1 5 1h0l-2 1h0l-1 1h0c-2 0-5-2-8-2l-2-1z" class="z"></path><path d="M429 208v3h3c1 1 1 1 1 3-1 0-1 1-2 2v1h-1v-1l1-1c-1 1-1 0-2 1h0-2c-1-1-4 0-7-1h0l1-1h0l2-1h0l4-1 2-1v-1l-2-1 2-1z" class="S"></path><path d="M429 208v3c0 1 0 2 1 2-2 1-5 1-7 0h0l4-1 2-1v-1l-2-1 2-1z" class="e"></path><path d="M429 211h3c1 1 1 1 1 3-1 0-1 1-2 2v1h-1v-1l1-1c-1 1-1 0-2 1h0-2c2-1 2-1 3-2v-1h0c-1 0-1-1-1-2z" class="K"></path><path d="M424 160c1-1 2-3 2-4 1-1 1-2 2-3 0-2 1-2 1-3h1 0l-1 6c0 2-1 3-2 5l-6 9c-1 0-3 2-3 3-1 1-3 2-5 4l-1-1v-3c0-1 0-2-1-3 0-1-1-1-2-2l1-1c0-2 1-3 2-5l4-3 2-2v1c1 0 1-1 1-2 1 0 2-1 2-1 1 1 2 1 3 2v3z" class="G"></path><path d="M412 173c0-1 0-1 1-2v1c1 0 1-1 2-1h2c-1 2-3 3-5 5v-3z" class="Z"></path><path d="M415 165h0c1 1 0 2 2 3h0l2 1-2 2h-2c-1 0-1 1-2 1v-1c1-2 1-5 2-6z" class="AJ"></path><path d="M416 159l2-2v1 1c-1 2-2 3-3 6h0c-1 1-1 4-2 6-1 1-1 1-1 2 0-1 0-2-1-3 0-1-1-1-2-2l1-1c0-2 1-3 2-5l4-3z" class="U"></path><path d="M416 159l2-2v1 1c-1 2-2 3-3 6h0c0-2 0-2-1-2h-1c-1 1-1 1-1 2s-1 2-2 2c0-2 1-3 2-5l4-3z" class="R"></path><path d="M421 155c1 1 2 1 3 2v3l-1 2-1 1-3 6-2-1h0c-2-1-1-2-2-3 1-3 2-4 3-6v-1c1 0 1-1 1-2 1 0 2-1 2-1z" class="s"></path><path d="M417 168c2-2 3-4 5-5l-3 6-2-1zm4-13c1 1 2 1 3 2v3l-1 2c-1 0-1-2-1-3h-1-1-1-1v-1c1 0 1-1 1-2 1 0 2-1 2-1z" class="x"></path><defs><linearGradient id="L" x1="321.154" y1="170.845" x2="326.201" y2="169.857" xlink:href="#B"><stop offset="0" stop-color="#161618"></stop><stop offset="1" stop-color="#423937"></stop></linearGradient></defs><path fill="url(#L)" d="M322 136l1-1v3c0 5 0 9 1 14-1 4 1 6 1 10v4h-1l3 10c0 1 0 2 1 2 0 2 0 3 1 4l-1 2h0c1 1 1 2 2 3l1 2v2h-1c0 1-1 1-1 1l-2-3h1c0-1-1-3-1-4-1-2-1-3-2-4h-1 0c0 1-1 1-1 1v1c-1 1-1 1-2 1h0v-3c1-1 1-1 1-2h-1v-4-3h1l-2-5-1-4 1-5c0-8 0-15 2-22h0z"></path><path d="M321 175v-3h1c1 2 2 4 2 6h1-1l-2-2c0 1 0 2-1 3v-4z" class="B"></path><path d="M321 179c1-1 1-2 1-3l2 2h1v3h-1 0c0 1-1 1-1 1v1c-1 1-1 1-2 1h0v-3c1-1 1-1 1-2h-1z" class="H"></path><path d="M322 136l1-1v3c0 5 0 9 1 14-1 4 1 6 1 10v4h-1c-1-3-2-7-2-11-1-4-1-9 0-14v-5z" class="n"></path><path d="M322 136l1-1v3 1c-1 2 0 5 0 7-1-1-1-2-1-4v6c-1-1 0-5 0-7v-5z" class="V"></path><path d="M411 611c0-1 0-1 1-1 1 1 1 1 2 1l1-1h0c0 1 1 1 1 2 1 1 2 1 2 2 1 1 2 1 2 3l3 2h1c1 1 1 2 2 2l1-1c3 2 6 4 8 7l3 3c2 1 3 1 4 2h-3c0 1-1 2-1 2-2 0-3-2-5-3-1-1-3-3-5-4-2-2-4-4-7-5-1 0-2-1-2-1h-3 1c1 1 1 1 1 2h0c-1-1-1-1-2-1s-2 0-3-1-3-1-5-2l-4-2-2-1-1-1h-1c-2-2-3-2-5-2l1-1 9 3h1c1 1 0 0 1 0 0-1 1-2 1-4h2 1z" class="w"></path><path d="M412 615v1c1 1 1 1 2 1v1c-2-1-4 0-5-1 1-1 2-1 3-2z" class="I"></path><path d="M408 611h2l-1 1c0 1 0 3-1 4l-1-1c0-1 1-2 1-4z" class="F"></path><path d="M412 615h2l1 1h1v1h-2c-1 0-1 0-2-1v-1z" class="W"></path><path d="M411 611c0-1 0-1 1-1 1 1 1 1 2 1l1-1h0c0 1 1 1 1 2 1 1 2 1 2 2 1 1 2 1 2 3-1 0-2-1-3-2-1 0-1 0-2-1v-2h-1v1c-2 0-2-1-3-2z" class="B"></path><path d="M421 622c-1-1-2-2-3-2-2 1-3 0-4 0v-1h0l3-1 6 3h0 0 0v-1h0c-2-1-5-3-7-2h0v-2c3 0 5 3 8 3 1 1 1 2 2 2l1-1c3 2 6 4 8 7l3 3c2 1 3 1 4 2h-3c0 1-1 2-1 2-2 0-3-2-5-3-1-1-3-3-5-4-2-2-4-4-7-5z" class="AA"></path><path d="M427 620c3 2 6 4 8 7-1 0-1 0-2-1h0-1c-1-2-4-3-6-5l1-1z" class="h"></path><defs><linearGradient id="M" x1="255.603" y1="607.401" x2="273.067" y2="595.141" xlink:href="#B"><stop offset="0" stop-color="#200808"></stop><stop offset="1" stop-color="#4d1414"></stop></linearGradient></defs><path fill="url(#M)" d="M260 589h1l14 10c4 4 8 7 11 11l-1 1h0l-5 4v-2c-1-2-3-3-4-5-8-8-17-15-28-15h-3v-2c3-1 7-1 11 0l1-1v-1l2 1s1 1 2 1h0c-1-1-1-1-1-2z"></path><path d="M260 589h1l14 10c4 4 8 7 11 11l-1 1h0c-5-7-12-13-19-16-3-2-7-3-10-4l1-1v-1l2 1s1 1 2 1h0c-1-1-1-1-1-2z" class="f"></path><path d="M410 151l4-2h0c-1 3-4 4-5 6 1 0 2 1 3 1h0l3 1 3-3c0 2-1 3-2 5l-4 3-2-1c-3 2-5 4-8 6l-4 2-4 4-1-1h0l-2-1 2-1c0-1-1-1-1-1-1 0-1 1-2 1h-1v-1h1c1 0 1-1 2-2h-1v-2-1c1-2 3-4 4-5 1 0 1 0 1 1h0c2 0 3-2 4-3 2-1 3-2 4-3h1l3-2v1l2-2z" class="x"></path><path d="M392 167v-1c1 1 1 2 2 2-1 0-2 1-2 1-1 0-1 1-2 1h-1v-1h1c1 0 1-1 2-2z" class="M"></path><path d="M397 166c1 0 2-1 3-1h1c0 1 0 1 1 2l-4 2c0-1 0-2-1-3z" class="R"></path><path d="M418 154c0 2-1 3-2 5l-4 3-2-1c2-1 3-3 5-4l3-3z" class="r"></path><path d="M397 166c1 1 1 2 1 3l-4 4-1-1h0l-2-1 2-1c0-1-1-1-1-1s1-1 2-1h0 1c0-1 1-1 2-2h0z" class="C"></path><path d="M395 168c0-1 1-1 2-2v1 1l-1 1-1-1z" class="E"></path><path d="M393 170c1 0 1 1 1 1 0 1-1 1-1 1l-2-1 2-1z" class="d"></path><defs><linearGradient id="N" x1="365.015" y1="152.928" x2="377.353" y2="150.364" xlink:href="#B"><stop offset="0" stop-color="#382f2c"></stop><stop offset="1" stop-color="#60564e"></stop></linearGradient></defs><path fill="url(#N)" d="M374 136v-3h0c1 2 1 3 1 4h1 0l1 4c0 8 0 17-2 24l-1 3-4 10-1 4c-1-1-2-1-2-2l1-2-1-2-3 4c-1-1 0-3 0-5v-2l1-3v-1l2-4 2-5c5-5 4-17 5-24z"></path><path d="M367 165c1 0 1 1 2 1h0c1 2 1 4 0 6v2l-2 2-3 4c-1-1 0-3 0-5v-2l1-3v-1l2-4z" class="G"></path><path d="M376 137h0l1 4c0 8 0 17-2 24l-1 3-4 10-1 4c-1-1-2-1-2-2l1-2-1-2 2-2 1-2c3-11 7-23 6-35z" class="n"></path><path d="M370 172c0 2 0 3-1 5v1h1l-1 4c-1-1-2-1-2-2l1-2-1-2 2-2 1-2z" class="p"></path><path d="M445 586c1-1 2-1 3-1 0 0 0 1 1 1v1l-2 1h-1v1c1 1 2 1 3 1s3 0 5 1c0 1 0 2 1 2h0-3c-7-1-11 2-16 5-3 2-6 4-9 7-3 2-5 5-8 8l-1 1c0-1-1-1-2-2 0-1-1-1-1-2 1-2 3-3 5-5 0-1 4-4 5-4l1-3v-1s3-3 4-3l2-1-1-2 1-1 3-2v1 1h0l4-2 2-1 1 1s2-2 3-2z" class="Q"></path><path d="M415 610c1-2 3-3 5-5v1l-2 2h1l-3 3v1c0-1-1-1-1-2z" class="L"></path><path d="M439 588l2-1 1 1c-2 1-4 2-6 4h0v2l-1 1c-4 1-7 4-10 6l1-3v-1s3-3 4-3l2-1-1-2 1-1 3-2v1 1h0l4-2z" class="AG"></path><path d="M435 588v1 1h0l-3 3-1-2 1-1 3-2zm10-2c1-1 2-1 3-1 0 0 0 1 1 1v1l-2 1h-1v1c1 1 2 1 3 1s3 0 5 1c0 1 0 2 1 2h0-3c-7-1-11 2-16 5-1-1 0-1 0-1l1-1c1-1 2-1 2-1l1-1h0 0l-1-1c-1 1-3 2-4 2l1-1v-2h0c2-2 4-3 6-4 0 0 2-2 3-2z" class="t"></path><path d="M436 592l3-1c0 1 0 1 1 2l-4 1v-2z" class="g"></path><path d="M443 589l3-1v1c1 1 2 1 3 1l-5 1-1-1v-1z" class="j"></path><path d="M440 590l3-1v1l1 1-3 1-1 1c-1-1-1-1-1-2l1-1z" class="S"></path><path d="M439 591l1-1c1 1 1 1 1 2l-1 1c-1-1-1-1-1-2z" class="f"></path><path d="M445 586c1-1 2-1 3-1 0 0 0 1 1 1v1l-2 1h-1l-3 1-3 1-1 1-3 1h0c2-2 4-3 6-4 0 0 2-2 3-2z" class="Q"></path><path d="M445 586c1-1 2-1 3-1 0 0 0 1 1 1v1c-2 0-3 0-4-1z" class="T"></path><path d="M338 164h1c3-1 7-2 10-4v-2c1 1 1 1 1 2-1 2-1 6-1 9v16h-3v-1h0c-1 0-1 0-1 1v2l-1-1c-1-1-1-1-1-2l-1-1-1 2h-2c-1 0-1-1-1-2h-1v-2h0c-1-1-1-2-1-2 0-1 0-2-1-3l-2-9c1 0 1 0 2 1v1-1c1 0 1-1 2-1h0v-1l-1-1h0l2-1z" class="AL"></path><path d="M343 169h1c1 1 1 1 1 2h-2l-1-2h1z" class="Ad"></path><path d="M342 169h0l1 2v1l-2-1h0c0-1 1-1 1-2h0z" class="AQ"></path><path d="M342 169l-1-1h0c0-1 0-1 1-2l1 1v2h-1 0 0z" class="AF"></path><path d="M347 163c0 1-1 2 0 3v1 1l-1-1h-1v-1h-1l-1-1h1l1-1 2-1z" class="y"></path><path d="M337 169h0 2 3 0 0c0 1-1 1-1 2h0c1 1 1 1 2 1l1 1c-1-1-1-1-2-1 0 1 0 1-1 1s-2-1-2-2h-1-1v-2z" class="p"></path><path d="M338 164h1c3-1 7-2 10-4v-2c1 1 1 1 1 2-1 2-1 6-1 9v16h-3v-1h1l-1-1-1-2-1-1-1-1v-1c1 0 1 1 2 1h0 1c1-1 1-2 1-4v-1-2c-1-1-2-1-2-2l2-2v-1-1c-1-1 0-2 0-3l1-1h0l-11 4-1-1h0l2-1z" class="x"></path><path d="M343 179v-1c1 0 1 1 2 1h0 1l1 2s-1 1-1 2l-1-2-1-1-1-1z" class="AM"></path><path d="M337 167v2 2h1 1c0 1 1 2 2 2s1 0 1-1c1 0 1 0 2 1l-1 1c1 1 2 1 1 2v1s1 1 1 2h0c-1 0-1-1-2-1v1l1 1 1 1 1 2 1 1h-1 0c-1 0-1 0-1 1v2l-1-1c-1-1-1-1-1-2l-1-1-1 2h-2c-1 0-1-1-1-2h-1v-2h0c-1-1-1-2-1-2 0-1 0-2-1-3l-2-9c1 0 1 0 2 1v1-1c1 0 1-1 2-1z" class="Ad"></path><path d="M337 167v2 2h-1l-1-1v-1-1c1 0 1-1 2-1z" class="y"></path><path d="M339 179h-1 0-1v-1l2-2 2 1c0 1 0 1-1 1l1 1v1h0c-1 0-1-1-2-1z" class="V"></path><path d="M338 171h1c0 1 1 2 2 2s1 0 1-1c1 0 1 0 2 1l-1 1c1 1 2 1 1 2v1s1 1 1 2h0c-1 0-1-1-2-1v1l1 1-1 1c-1 0-1-1-2-1h0v-1l-1-1c1 0 1 0 1-1l-2-1 1-1v-1c-1 0-2 1-2 2l-1-1h-1l2-4z" class="r"></path><path d="M340 174v-1c1 0 1 0 1 1l2 2v1l-1 1h0l1 1 1 1-1 1c-1 0-1-1-2-1h0v-1l-1-1c1 0 1 0 1-1l-2-1 1-1v-1z" class="n"></path><path d="M339 179c1 0 1 1 2 1h0 0c1 0 1 1 2 1l1-1 1 1 1 2 1 1h-1 0c-1 0-1 0-1 1v2l-1-1c-1-1-1-1-1-2l-1-1-1 2h-2c-1 0-1-1-1-2v-2-1c1 0 1-1 1-1z" class="k"></path><path d="M339 179c1 0 1 1 2 1l-1 1v2h-1c-1-1-1-1-1-2v-1c1 0 1-1 1-1z" class="n"></path><path d="M345 181l1 2 1 1h-1 0c-1 0-1 0-1 1v2l-1-1c-1-1-1-1-1-2l-1-1h0v-1l2 1 1-2z" class="p"></path><defs><linearGradient id="O" x1="379.497" y1="165.169" x2="373.517" y2="163.44" xlink:href="#B"><stop offset="0" stop-color="#141314"></stop><stop offset="1" stop-color="#3a3333"></stop></linearGradient></defs><path fill="url(#O)" d="M377 141h1c0 4 1 8 1 11s0 6 1 8v7s1 0 2 1v2l-1 1c-1 0-2 1-3 2h0c0 1 0 2-1 2h1l1 1c-1 1-2 2-1 3v1l-1 1-3 4h0v2l-1 1h-1v2c1 1 1 1 1 2 0 0 1 1 2 1l-4 6v1h-1v-1h0c-1-1 0-1-1-2-1 1-1 1-2 1v-1h-1l1-1v-1h-1-1 0v-2c-1 0-1 0-2-1 0-1 2-2 2-3l-1-1h1v-2h0 1l2-2 1-2 1-4 4-10 1-3c2-7 2-16 2-24z"></path><path d="M375 165c1 1 1 1 1 3 0 1-1 3-1 4l-1-4 1-3z" class="F"></path><path d="M380 167s1 0 2 1v2l-1 1c-1 0-2 1-3 2 1-2 1-5 2-6z" class="H"></path><path d="M378 173c0 1 0 2-1 2h1l1 1c-1 1-2 2-1 3v1l-1 1-3 4h0v2l-1 1h-1l1-2c-1-1-1-2-1-3s1-2 1-3l1-1c2-1 2-2 2-4 0-1 1-1 2-2z" class="C"></path><path d="M378 173c0 1 0 2-1 2l-1 4c0 1 0 0-1 1-1 2-1 4-2 6-1-1-1-2-1-3s1-2 1-3l1-1c2-1 2-2 2-4 0-1 1-1 2-2z" class="F"></path><path d="M374 168l1 4-2 8c0 1-1 2-1 3s0 2 1 3l-1 2v2c1 1 1 1 1 2 0 0 1 1 2 1l-4 6v1h-1v-1h0c-1-1 0-1-1-2-1 1-1 1-2 1v-1h-1l1-1v-1h-1-1 0v-2c-1 0-1 0-2-1 0-1 2-2 2-3l-1-1h1v-2h0 1l2-2 1-2 1-4 4-10z" class="b"></path><path d="M368 184l1 1-1 1c0 1 0 1-1 1 0 1 0 1-1 2v-3l2-2z" class="C"></path><path d="M367 196v-1c1 0 1 0 1-1 1 0 1-1 2-1v1l-3 3h-1l1-1z" class="F"></path><path d="M371 191h-1 0c0-2 1-3 2-4h0v-2h0c-1-1-1-1 0-2 0 1 0 2 1 3l-1 2v2l-1 1z" class="B"></path><path d="M366 186v3s1 2 0 2c0 1-1 1-1 2-1 0-1 0-2-1 0-1 2-2 2-3l-1-1h1v-2h0 1zm6 4c1 1 1 1 1 2 0 0 1 1 2 1l-4 6v1h-1v-1c0-1-1-3 0-4v-2s1-1 1-2l1-1z" class="R"></path><defs><linearGradient id="P" x1="202.919" y1="79.445" x2="200.045" y2="72.701" xlink:href="#B"><stop offset="0" stop-color="#16171b"></stop><stop offset="1" stop-color="#413b39"></stop></linearGradient></defs><path fill="url(#P)" d="M206 68l2-1h1l2-1 8-3c1 1 1 1 3 1h4 0 0c0 1-1 1-1 1l-1 1h2v1l-4 1h1v-1c-2 0-5 1-6 2-8 2-16 5-23 9-3 1-5 2-7 3-1 1-1 2-2 3v2h-1c0 2 2 2 3 4l-2 1c0-1 0-2-1-2l-2 1-1-2v-1l-1-1v-2l-1-1 1-4h0c1-1 1-3 2-5h-1 0c-1-1-1-2 0-2l1-1h0 1c1 2 3 2 5 2 5-1 10-3 13-6v-2h1l1 1-1 1c2 0 2 0 3 1h1z"></path><path d="M206 68l2-1h1l2-1 8-3c1 1 1 1 3 1-8 2-16 6-24 9v-2c2-1 3-2 4-4 2 0 2 0 3 1h1z" class="k"></path><path d="M185 77c1 0 3-1 4-1 3-2 6-3 9-5v2l-7 3c-3 1-6 2-8 5l-3 3-1-1 1-4h0c1-1 1-3 2-5 0 1 1 1 1 1l2 2z" class="p"></path><path d="M183 75l2 2h-1-1v-2z" class="k"></path><path d="M180 79v1c1 1 1 0 2 0l1 1-3 3-1-1 1-4h0z" class="AF"></path><path d="M201 67v-2h1l1 1-1 1c-1 2-2 3-4 4-3 2-6 3-9 5-1 0-3 1-4 1l-2-2s-1 0-1-1h-1 0c-1-1-1-2 0-2l1-1h0 1c1 2 3 2 5 2 5-1 10-3 13-6zM66 194c1-4 3-8 5-12l5-11c2-3 4-7 6-10 1-2 1-3 2-5v-2c1 0 2-1 3-1 1-2 3-3 5-5l11-14c8-9 17-18 27-25h1c-2 3-6 5-8 8l-4 5-8 8-3 2c-3 3-6 7-9 11-2 2-5 5-6 8-1 1-2 2-2 3v2h1 0l-23 34c-1 2-2 3-3 4z" class="H"></path><path d="M69 190l-1-1s1-1 1-2l16-29c1-1 2-3 4-3l1-1h1v2h1 0l-23 34z" class="s"></path><defs><linearGradient id="Q" x1="199.15" y1="73.406" x2="205.029" y2="81.958" xlink:href="#B"><stop offset="0" stop-color="#080709"></stop><stop offset="1" stop-color="#29282a"></stop></linearGradient></defs><path fill="url(#Q)" d="M185 86v-2c1-1 1-2 2-3 2-1 4-2 7-3 7-4 15-7 23-9 1-1 4-2 6-2v1h-1c-3 2-6 3-9 4 0 1-1 1-2 2h1c2 0 3 0 5-1h2c0 1-1 1-1 2h1l-6 4h0l-1 1-4 2-7 4h0l-8 3c0 1-2 1-2 2l-1-1h-3c-1-2-3-2-3-4h1z"></path><path d="M207 80l4-2s1 1 1 2l-4 2 1-2h-2z" class="AM"></path><path d="M218 75h1l-6 4h0l-1 1c0-1-1-2-1-2l7-3z" class="AQ"></path><defs><linearGradient id="R" x1="198.757" y1="82.73" x2="201.433" y2="85.996" xlink:href="#B"><stop offset="0" stop-color="#b2a894"></stop><stop offset="1" stop-color="#bdb2ae"></stop></linearGradient></defs><path fill="url(#R)" d="M185 86l3 3c3 0 17-8 19-9h2l-1 2-7 4h0l-8 3c0 1-2 1-2 2l-1-1h-3c-1-2-3-2-3-4h1z"></path><path d="M489 535c2-2 5-3 8-3h0c-4 2-8 5-9 9-1 2-1 3-1 5 1 5 6 10 1 14 1-2 1-4 0-6-1-1-2-2-4-3 0 0-1 0-2 1-2 2-4 6-5 8-3 5-7 9-9 14-1 2-2 5-2 7v2 2h-2c-1 0-1 2-2 3l4 5-1 1c-2-1-3-2-4-3l-3-4 1-1-2-1h1v-2c-1-1-1-1-1-2l1-1c0-1 0-2 1-4h0c0-1 1-2 1-2 1-1 0-3 0-4h1v-1c0 1 0 3 1 4v-3h1v3h0c1-2 2-4 3-5 2-2 3-3 4-5l9-16v-1l4 2 1-1v-1c0-5 2-8 5-11z" class="T"></path><path d="M460 570h1v4c1 1 0 3 0 3v1h-1c0-1 0-1-1-2 0-1 1-2 1-2 1-1 0-3 0-4z" class="e"></path><path d="M459 576c1 1 1 1 1 2h1c0 3 0 6 1 10l4 5-1 1c-2-1-3-2-4-3l-3-4 1-1-2-1h1v-2c-1-1-1-1-1-2l1-1c0-1 0-2 1-4h0z" class="K"></path><path d="M459 576c1 1 1 1 1 2v1c-1 5 1 8 3 12-2-1-4-4-5-6v-2c-1-1-1-1-1-2l1-1c0-1 0-2 1-4h0z" class="L"></path><defs><linearGradient id="S" x1="594.704" y1="134.504" x2="591.56" y2="137.366" xlink:href="#B"><stop offset="0" stop-color="#332d2d"></stop><stop offset="1" stop-color="#5d5853"></stop></linearGradient></defs><path fill="url(#S)" d="M574 113c8 7 15 15 22 23 6 6 11 13 18 18v1c0 4 2 7 4 10v1l1 3c-1 0-1 1-1 2-2-2-4-5-6-8l-22-27-5-5-10-11-1-2h1l-2-3h1l-2-2h1 1z"></path><path d="M585 131h3 1l5 5c0 1 0 1-1 1-1-1-2-1-3-1h0l-5-5z" class="AL"></path><defs><linearGradient id="T" x1="614.589" y1="160.184" x2="615.857" y2="169.176" xlink:href="#B"><stop offset="0" stop-color="#6e645f"></stop><stop offset="1" stop-color="#817767"></stop></linearGradient></defs><path fill="url(#T)" d="M610 158h1l1 2h1c0-1-1-1-1-2-1-1-1-1-1-2h0c3 3 4 7 7 10l1 3c-1 0-1 1-1 2-2-2-4-5-6-8 1-2-1-3-1-4l-1-1z"></path><path d="M575 120l-1-2h1l14 13h-1-3l-10-11z" class="k"></path><path d="M590 136h0c1 0 2 0 3 1 1 0 1 0 1-1 4 5 8 10 11 15l5 7 1 1c0 1 2 2 1 4l-22-27z" class="m"></path><path d="M275 167l8 8c1 1 3 2 3 3 3 4 6 6 9 9h0v1c1 1 1 1 3 2 0 0 1 1 1 2 2 2 5 4 7 7h0v1c1 1 3 2 3 4 0 1 0 0-1 1l-1-1-1 1h0c0 2 1 2 0 4l-6-6h-1 0-1c0-1-1-1-1-2-5-3-7-8-11-12l-7-7c-2-2-3-5-4-7 0-1 0-2-1-2v-2c0 1 1 1 1 2h1v-1h1c0-1-1-1-2-2v-3z" class="G"></path><path d="M275 167l8 8c1 1 3 2 3 3 3 4 6 6 9 9h0c-7-3-13-9-19-14v-1h1c0-1-1-1-2-2v-3z" class="Z"></path><path d="M297 201h0c1-2 1-4 1-5l-1-1 1-1c4 2 7 6 9 10l-1 1h0c0 2 1 2 0 4l-6-6h-1 0-1c0-1-1-1-1-2z" class="N"></path><defs><linearGradient id="U" x1="409.907" y1="644.201" x2="450.155" y2="654.952" xlink:href="#B"><stop offset="0" stop-color="#190b0b"></stop><stop offset="1" stop-color="#292828"></stop></linearGradient></defs><path fill="url(#U)" d="M418 623c0-1 0-1-1-2h-1 3s1 1 2 1c3 1 5 3 7 5 2 1 4 3 5 4 2 1 3 3 5 3l4 7 2 2v1c1 2 3 2 4 4l-1 1c0 3 1 7 1 9 0 4 0 7-1 10 0 4-1 7-3 10 0 2-1 5-3 6 0-2 1-5 1-7 1-3 3-15-1-17h0c0-1 0-1 1-2 0-3 0-6-2-9-1 0-1 1-2 2v-2l1-2c1-1 0-2 0-3v-1c-2 1-4-1-6-1 0-1 0-1-1-1-1 1-2 1-3 1h-1l-1 1c0 1 0 1-1 1h0c0-1 0 0 1-1l2-3h0c0-2-1-1-3-3v-1h3v-2-1c-1-1-2-3-3-4s-1-2-2-3h0c-1-1-2-2-3-2s-2-1-2-1h-1z"></path><path d="M429 636c0-1 2-2 3-1 0 0 1 1 2 1 2 3 3 5 5 7-2 1-4-1-6-1 0-1 0-1-1-1-1 1-2 1-3 1h-1l-1 1c0 1 0 1-1 1h0c0-1 0 0 1-1l2-3h0c0-2-1-1-3-3v-1h3z" class="AH"></path><defs><linearGradient id="V" x1="163.524" y1="477.048" x2="171.58" y2="468.556" xlink:href="#B"><stop offset="0" stop-color="#49120f"></stop><stop offset="1" stop-color="#2e1414"></stop></linearGradient></defs><path fill="url(#V)" d="M164 450v1c1 1 1 2 0 3 1 0 1 1 1 2h0c1 2 2 3 3 4s0 1 0 3c1 0 1 1 1 1l1 3v1h-2v1c1 3 1 6 2 9 0 0-1 0-1 1l18 12h0c-1 0-1 1-2 1v1h-1v-1c-1-1-1-1-2-1-1-1-2-1-4-2v1 1h-1c0-2-2-3-3-3-3-3-9-7-10-12-1-2-1-4-1-6-1-4-3-8-6-10-4-3-9-2-13-2v-1l4-1h0c-1-1-2-1-3-1h-1 0v-1c1-1 4-1 6-2 1-1 4-1 5-1h2 3 1v-1h3z"></path><path d="M167 466c1 1 1 2 1 3 1 3 1 6 2 9 0 0-1 0-1 1v-1c-2-2-2-9-2-12z" class="AI"></path><path d="M164 450v1c1 1 1 2 0 3 1 0 1 1 1 2h0c1 2 2 3 3 4s0 1 0 3c1 0 1 1 1 1l1 3v1h-2v1c0-1 0-2-1-3l-1-3-2-2-1 1c0-1-1-2-2-2-4-4-8-4-13-4h0c-1-1-2-1-3-1h-1 0v-1c1-1 4-1 6-2 1-1 4-1 5-1h2 3 1v-1h3z" class="Ae"></path><path d="M162 455c1 0 2-1 2-1 1 0 1 1 1 2h0c1 2 2 3 3 4s0 1 0 3l-6-8z" class="L"></path><path d="M154 454c4 1 8 3 11 6 0 1 0 2 1 3l-2-2-1 1c0-1-1-2-2-2v-1c-1-3-5-3-7-5h0z" class="v"></path><path d="M145 455c3-1 6-2 9-1h0c2 2 6 2 7 5v1c-4-4-8-4-13-4h0c-1-1-2-1-3-1z" class="Ag"></path><path d="M164 450v1c1 1 1 2 0 3 0 0-1 1-2 1-4-2-8-3-12-3 1-1 4-1 5-1h2 3 1v-1h3z" class="Q"></path><defs><linearGradient id="W" x1="461.406" y1="199.944" x2="467.11" y2="218.138" xlink:href="#B"><stop offset="0" stop-color="#3f3832"></stop><stop offset="1" stop-color="#5b5658"></stop></linearGradient></defs><path fill="url(#W)" d="M481 193s1 1 1 2l-2 2c-2 1-3 3-4 4l-9 9c-1 2-2 2-3 3l1 1c1-1 1-1 2-1-1 0-2 2-3 2l-2 2 1 1-3 3h-2c-3 2-5 4-7 6h1c0 2 0 4-1 5h1 1c-1 2-1 2 0 4l-3 1-4 2c-1 0-1 1-2 1-1-1-1-3-1-5h0c-1-1-1-2-2-3h0l-2-2-1-2c1 0 2 0 4-1 1 0 1-1 2-2v-1-3-3l1-1-1-2v-1-1h2 1 0v5h0c2 0 4-1 6-2 1 0 1-1 2-2 4-1 8-4 11-7 5-5 10-9 15-14z"></path><path d="M450 224l-1-1h0l3-3c3-1 6-3 9-4 1-1 2-1 3-2v1l-2 2 1 1-3 3h-2l-1-1c-1 1-3 2-5 3-1 0-1 1-2 1z" class="D"></path><path d="M457 220c1-1 4-3 5-3l1 1-3 3h-2l-1-1z" class="M"></path><path d="M446 213h1 0v5h0c0 1 0 3-1 3 2 2 2 2 2 4 1 0 2-1 2-1 1 0 1-1 2-1 2-1 4-2 5-3l1 1c-3 2-5 4-7 6-1 1-2 1-3 2v-3h0l-1 1c-2 0-2 1-3 2h-1l-1-1c-1 1-2 1-3 2l-1-2c1 0 2 0 4-1 1 0 1-1 2-2v-1-3-3l1-1-1-2v-1-1h2z" class="d"></path><path d="M444 213h2c0 1-1 1-1 2v2h0l-1-2v-1-1z" class="P"></path><path d="M446 221c2 2 2 2 2 4 0 1-2 0-3 0h0l1-4z" class="C"></path><path d="M447 227l1-1h0v3c1-1 2-1 3-2h1c0 2 0 4-1 5h1 1c-1 2-1 2 0 4l-3 1-4 2c-1 0-1 1-2 1-1-1-1-3-1-5h0c-1-1-1-2-2-3h0l-2-2c1-1 2-1 3-2l1 1h1c1-1 1-2 3-2z" class="I"></path><path d="M444 234c1 1 1 1 1 2v2c-1 0-2-2-2-3h0l1-1z" class="V"></path><path d="M451 232h1 1c-1 2-1 2 0 4l-3 1-4 2c1-3 3-5 5-7z" class="P"></path><path d="M447 227l1-1h0v3c-2 2-2 5-3 7 0-1 0-1-1-2l-1 1c-1-1-1-2-2-3h0l-2-2c1-1 2-1 3-2l1 1h1c1-1 1-2 3-2z" class="u"></path><path d="M444 229c1-1 1-2 3-2h0v1c-1 0-1 1-1 1 0 1 0 1-1 1l-1-1z" class="r"></path><path d="M441 232v-1h3v3l-1 1c-1-1-1-2-2-3h0z" class="AH"></path><path d="M277 209c3 0 6-2 9-2l12-4h1 0c2 2 3 4 4 6v1c-1 0-2 1-3 2v2c-1 0-2 0-2 1h1v1l-7 3c-1-1-1-1-2-1l-6 2h-3-8l-2-1-1-1-2-3c-1-1-2-1-3-3h0l1-1h1s0 1 1 1v1h0c1-1 1-1 2-1v-1h1c0-1 1-2 1-3 2 1 2 1 4 1h0 1z" class="Q"></path><path d="M268 213c1-1 1-1 2-1 1 1 2 1 2 1 1 1 1 1 1 2h-1-1c-1 0-2-1-3-2z" class="S"></path><path d="M291 211v1c4-1 8-3 12-3v1c-1 0-2 1-3 2l-1-1 1-1c-2 0-3 1-5 1-2 1-5 3-7 3v-1h1s1 0 1-1c-1 0-2 1-4 1l5-2z" class="q"></path><path d="M272 213c5 0 9-1 13-1h1c-4 2-8 3-13 3 0-1 0-1-1-2z" class="f"></path><path d="M299 211l1 1v2c-1 0-2 0-2 1h1v1l-7 3c-1-1-1-1-2-1l-6 2h-3-8l-2-1c2-1 3-1 4-1 2 1 4 0 5 0s3 1 4 1c2-1 5-2 7-3 0-1 1-1 1-1v1c1 0 2-1 4-1 1 0 1 0 2-1s1-1 1-2h0l-2 1h-1v-1c1 0 2 0 3-1z" class="h"></path><path d="M298 215h1v1l-7 3c-1-1-1-1-2-1l8-3z" class="AO"></path><path d="M277 209c3 0 6-2 9-2l12-4h1 0c2 2 3 4 4 6-4 0-8 2-12 3v-1l-5 2v-1h-1c-4 0-8 1-13 1 0 0-1 0-2-1v-1h1c0-1 1-2 1-3 2 1 2 1 4 1h0 1z" class="v"></path><path d="M299 203c2 2 3 4 4 6-4 0-8 2-12 3v-1l9-5c1-1-1-1-1-2v-1h0z" class="AB"></path><path d="M513 77l2 2 1 1h1v-2l1 1h1 1v2h-1l-1-1h0c0 1 0 1 1 2v1l1 1v-1c2 0 5 2 6 2 2 1 3 2 4 3 1 2 4 3 6 5 1 0 3 1 4 2 1 0 1 1 2 1v1h-2v1c-1 1-1 1-1 2v1c0 2 1 4 1 6 0 1 0 2-1 3h-1l-7-5c-1-1-3-2-4-2-3-2-6-4-8-6 1-1 1-1 1-2-1-2-1-3-1-5-1 1-1 0-1 1h-1s-1 0-2-1c0 1-1 1-1 1-1 0-2-1-2-1l2-1h0l2-2-1-1-1-3c-1-1-2-3-3-4v-1h1-1l1-1h1z" class="w"></path><path d="M516 80h1v-2l1 1h1 1v2h-1l-1-1h0c0 1 0 1 1 2v1l1 1v-1c2 0 5 2 6 2 2 1 3 2 4 3 1 2 4 3 6 5 1 0 3 1 4 2 1 0 1 1 2 1v1h-2c-4-3-8-6-12-8-2-1-3-1-4-2h-1l1-1-3-1c-1-1-1-1-2-1h-1c0-1-1-2-2-4z" class="AF"></path><path d="M513 77l2 2 1 1c1 2 2 3 2 4h1c1 0 1 0 2 1l3 1-1 1c-1 0-2 0-3 1-1 0-1 1-1 2-1 1-1 0-1 1h-1s-1 0-2-1c0 1-1 1-1 1-1 0-2-1-2-1l2-1h0l2-2-1-1-1-3c-1-1-2-3-3-4v-1h1-1l1-1h1z" class="a"></path><path d="M514 83v-1h1c1 1 1 2 2 3v1h-2l-1-3z" class="J"></path><path d="M519 84c1 0 1 0 2 1l3 1-1 1c-1 0-2 0-3 1-1 0-1 1-1 2-1 1-1 0-1 1h-1s-1 0-2-1c1 0 2-1 2-1h1c0-2 1-3 1-5z" class="p"></path><path d="M520 95l15 9s1 0 1-1c2-1 2-1 3-2 0 2 1 4 1 6 0 1 0 2-1 3h-1l-7-5c-1-1-3-2-4-2-3-2-6-4-8-6 1-1 1-1 1-2z" class="AM"></path><path d="M538 104s0 1 1 1v1 2 1l-1-1c-1 0-2-1-2-2l2-2z" class="r"></path><path d="M188 526c2 1 4 1 6 2h1c2 1 5 1 7 1h3l-1 1h0c1 1 2 1 3 1 1 1 2 1 3 0 0 1 0 2 1 2h1c0-1 1-2 1-4h0l1-1c0 2 1 4 1 6 0 0-1 2-1 3 1 2 1 4 1 6v3l1 1c1 0 2-1 3 0 2 2 3 4 4 7s4 7 6 10c0 1 1 3 1 5l2 1h2l2 3 1 2c1 5 1 10-2 15h0-1c0-1 1-2 1-2 0-2-1-3-2-4v-2c0-1 0-3-1-5-2-6-6-11-9-16-2-3-3-6-5-9-1 0-2-1-3-1s-3 1-4 3c-2 1-2 3-2 6h0l-1-1v-4l1-3-1-2c1-2 2-4 2-6s-1-3-2-5c-3-3-6-5-10-7h0c-1 0-4-1-5-1v-1c-1 0-1-1-2-1h-1l1-1c-1 0-1 0-2-1-1 0-1-1-1-1z" class="T"></path><path d="M230 569l2 1h2l2 3 1 2h-1c-2-1-5-4-6-6zm-20-32l1-1v-2h0 1c1 0 1 1 2 1v2c1 2 1 4 1 6 0 0-1-2-1-3-1-1-3-2-3-3h-1z" class="i"></path><path d="M214 528c0 2 1 4 1 6 0 0-1 2-1 3v-2c-1 0-1-1-2-1h-1 0v2l-1 1-1-2c-1 0-2-1-2-2-1-1-2-1-3-2v-1c1 1 2 1 3 1 1 1 2 1 3 0 0 1 0 2 1 2h1c0-1 1-2 1-4h0l1-1z" class="e"></path><path d="M198 532c3 0 5 1 7 3 3 2 6 5 7 10 0 2-2 5-3 7l-1-2c1-2 2-4 2-6s-1-3-2-5c-3-3-6-5-10-7h0z" class="j"></path><path d="M188 526c2 1 4 1 6 2h1c2 1 5 1 7 1h3l-1 1h0v1c1 1 2 1 3 2l-1 1 3 3h0c-1 0-2-2-4-2-2-2-4-3-7-3-1 0-4-1-5-1v-1c-1 0-1-1-2-1h-1l1-1c-1 0-1 0-2-1-1 0-1-1-1-1z" class="Q"></path><path d="M380 160v1l3 2h1 1c1 2 0 2 1 3l2 1h1v2 1h1c1 0 1-1 2-1 0 0 1 0 1 1l-2 1 2 1h0l1 1 4-4 4-2c3-2 5-4 8-6l2 1c-1 2-2 3-2 5l-1 1c-1-1-1-1-3-1l-1 1-6 3-6 6-7 8h0c-3 3-11 11-12 15h0l-1 2c-1 0-2-1-2-2v-1l4-6c-1 0-2-1-2-1 0-1 0-1-1-2v-2h1l1-1v-2h0l3-4 1-1v-1c-1-1 0-2 1-3l-1-1h-1c1 0 1-1 1-2h0c1-1 2-2 3-2l1-1v-2c-1-1-2-1-2-1v-7z" class="p"></path><path d="M371 199h2l1 1-1 2c-1 0-2-1-2-2v-1z" class="k"></path><path d="M377 181c1 1 2 2 4 3h1v1h-1-3c0-1-1-1-1-1l-1 1h-2l3-4z" class="D"></path><path d="M374 185h2l1-1s1 0 1 1h3l-4 6-2 2c-1 0-2-1-2-1 0-1 0-1-1-2v-2h1l1-1v-2h0z" class="H"></path><path d="M374 187l1 1c1 1 0 2 2 3l-2 2c-1 0-2-1-2-1 0-1 0-1-1-2v-2h1l1-1z" class="D"></path><path d="M389 172c1 0 1 0 2-1l2 1h0l1 1c-2 2-5 5-9 7l-1-1v-1c-1 0-2-1-2-2s1-2 1-3c1 0 1 0 2-1h0l1 1c-1 1-1 1-1 2h1l1-1h-1c1-1 2-1 3-2z" class="E"></path><path d="M389 172c1 0 1 0 2-1l2 1h0c-1 0-1 0-2 1 0 1-1 1-2 1v-2z" class="O"></path><path d="M385 172h0l1 1c-1 1-1 1-1 2h1l1-1h-1c1-1 2-1 3-2v2h0l-2 2c-1 0-1 0-2 1h0c-2-1-1-2-1-3l-1-1c1 0 1 0 2-1z" class="P"></path><path d="M382 170l1-1c0 1 1 1 1 2v1h1c-1 1-1 1-2 1 0 1-1 2-1 3s1 2 2 2v1 1 2c-1 1-1 1-3 1l-3-3v-1c-1-1 0-2 1-3l-1-1h-1c1 0 1-1 1-2h0c1-1 2-2 3-2l1-1z" class="O"></path><path d="M378 173c1-1 2-2 3-2 0 1 0 2-1 3v1 1h0-1l-1-1h-1c1 0 1-1 1-2h0z" class="U"></path><path d="M379 176h1l1 2-1 1c1 1 1 0 2 0s1 1 2 1v2c-1 1-1 1-3 1l-3-3v-1c-1-1 0-2 1-3z" class="G"></path><path d="M380 160v1l3 2h1 1c1 2 0 2 1 3l2 1h1v2 1h1c1 0 1-1 2-1 0 0 1 0 1 1l-2 1c-1 1-1 1-2 1-1 1-2 1-3 2h1l-1 1h-1c0-1 0-1 1-2l-1-1h0-1v-1c0-1-1-1-1-2l-1 1v-2c-1-1-2-1-2-1v-7z" class="P"></path><path d="M388 167h1v2 1h1l-1 1-1-1h1c-1-1-1-2-2-2l1-1z" class="E"></path><path d="M392 169s1 0 1 1l-2 1c-1 1-1 1-2 1-1 1-2 1-3 2 0-2 0-4-1-5l3 1 1 1 1-1c1 0 1-1 2-1z" class="B"></path><path d="M380 160v1l3 2-2 1c1 1 1 1 1 3h-1l1 1h0c1 0 3 1 3 1 1 1 1 3 1 5h1l-1 1h-1c0-1 0-1 1-2l-1-1h0-1v-1c0-1-1-1-1-2l-1 1v-2c-1-1-2-1-2-1v-7z" class="C"></path><path d="M380 161l3 2-2 1h0c-1-1-1-2-1-3z" class="M"></path><defs><linearGradient id="X" x1="427.378" y1="128.864" x2="405.036" y2="137.951" xlink:href="#B"><stop offset="0" stop-color="#423e3d"></stop><stop offset="1" stop-color="#6c6257"></stop></linearGradient></defs><path fill="url(#X)" d="M419 109l1-1c2 3 4 7 7 10v2l-1 2c1 3 1 6 1 9 1 7 0 12-1 19l-2-1c-1 2-1 4-3 6h0s-1 1-2 1c0 1 0 2-1 2v-1l-2 2c1-2 2-3 2-5l-3 3-3-1h0c-1 0-2-1-3-1 1-2 4-3 5-6h0l-4 2-1-1c6-5 8-12 10-19h1c0-1 1-4 0-5v-2-3-4h0c0-3-1-5-2-8h1 0z"></path><path d="M420 117l1 1c1 1 1 2 0 2l-1 1v-4z" class="E"></path><path d="M424 128c0-2-1-4 1-6v5l-1 1z" class="B"></path><defs><linearGradient id="Y" x1="427.855" y1="130.974" x2="421.302" y2="135.971" xlink:href="#B"><stop offset="0" stop-color="#3a3639"></stop><stop offset="1" stop-color="#484843"></stop></linearGradient></defs><path fill="url(#Y)" d="M424 128l1-1c1 3 1 6 1 9 0 2 0 4-1 6-1-1-1-1-1-2v-2-10z"></path><path d="M425 146l-1 3c-1 2-1 4-3 6h0s-1 1-2 1c0 1 0 2-1 2v-1l-2 2c1-2 2-3 2-5h0c0-2 4-6 5-7 1 0 1-1 2-1z" class="Z"></path><defs><linearGradient id="Z" x1="427.781" y1="132.383" x2="420.598" y2="131.306" xlink:href="#B"><stop offset="0" stop-color="#7e6d5e"></stop><stop offset="1" stop-color="#949187"></stop></linearGradient></defs><path fill="url(#Z)" d="M419 109l1-1c2 3 4 7 7 10v2l-1 2c1 3 1 6 1 9 1 7 0 12-1 19l-2-1 1-3v-3-1h0c1-2 1-4 1-6 0-3 0-6-1-9v-5c-1-5-3-9-6-13z"></path><path d="M201 360h2l1 1v-1l2 2h2c0 1 0 1 1 1h0-1l1 1 1 1c0 1 0 2-1 2 0 2-5 10-5 12-1 2-2 4-2 6 0 0 0 1 1 1 0 0 0 1 1 1l-2 2-1 1h1c0 2 0 3-1 5-1 0-1 0-1 1-2 6-3 13-7 18-1 0-1 1-2 1l-1 1-3-1v-1c-1-1-2-2-2-4-1-2-1-5 0-8l3-3s2-1 2-2c1-1 2-2 2-3h2c1-1 1-2 2-3h1c-1 0-1-1-1-2l2-2c-1-1-3 1-5 1v-1l1-1h1c2-1 4-4 5-6s1-2 0-4l-3-6 6-3c1-1 2-3 2-5-1-1-2-1-4-2z" class="h"></path><path d="M201 390h1c0 2 0 3-1 5-1 0-1 0-1 1 0-3 0-4 1-6z" class="e"></path><path d="M196 391h1c0 2 0 3-1 4h0 1v1h1c0 3-2 10-4 12v2c-1 1-2 2-2 3 0 0 1 0 1 1-1 0-1 1-2 1l-1 1-3-1v-1c-1-1-2-2-2-4-1-2-1-5 0-8l3-3s2-1 2-2c1-1 2-2 2-3h2c1-1 1-2 2-3z" class="V"></path><path d="M196 391h1c0 2 0 3-1 4h0c1 1 1 2 1 3l-1 2s-1-1-2-1c1-2 2-5 2-8z" class="e"></path><path d="M188 399s2-1 2-2c1-1 2-2 2-3h2c-1 2-2 4-4 5-1 1-3 2-3 4v2 1c0 1 0 2 1 3l-2 2-1-1c-1-2-1-5 0-8l3-3z" class="K"></path><path d="M196 395h1v1h1c0 3-2 10-4 12v2c-1 1-2 2-2 3 0 0 1 0 1 1-1 0-1 1-2 1l-1 1-3-1v-1c-1-1-2-2-2-4l1 1 2-2 1 1 2-2c1-2 2-4 2-6l1-3c1 0 2 1 2 1l1-2c0-1 0-2-1-3z" class="Q"></path><path d="M194 399c1 0 2 1 2 1 0 2-1 4-1 5-1-1-1-2-2-3l1-3z" class="Y"></path><path d="M191 408c1-2 2-4 2-6 1 1 1 2 2 3-1 1-1 2-2 3h-2z" class="AG"></path><path d="M193 408c0 2-1 4-3 5v2h1l-1 1-3-1v-1c-1-1-2-2-2-4l1 1 2-2 1 1 2-2h2z" class="Y"></path><path d="M187 415c1-1 2-1 3-2v2h1l-1 1-3-1z" class="v"></path><path d="M185 410l1 1c1 0 2 1 3 2-1 1-1 0-2 1-1-1-2-2-2-4z" class="S"></path><path d="M401 203l3 1h-1-1-1c-1 0-1 1-2 2 1 1 1 1 2 1l1 1c2 2 5 4 8 4h0l2 1c3 0 6 2 8 2 3 1 6 0 7 1h2 0c1-1 1 0 2-1l-1 1v1c-1 1-1 1-3 2l1 1c1-1 2-2 3-2l1 1v2l1 1-3 3c0 1-1 2-1 3l-1 1h1v1h0c-1 0-2 1-4 2v2h0l-2-2v-2c-2-2-4-4-7-6-1-1-3-2-5-3h-4 0c-2 1-3 1-4 2l-1-2c-2 0-2-1-3-2-1 0-1 0-1-1h-1l-6-3v-1c1 0 1 0 2-1s1-2 3-3h1c1-2 2-5 3-7h1z" class="q"></path><path d="M413 217c-1-1-2-1-3-2l1-1 2 1h2v1l-2 1z" class="K"></path><path d="M397 210c1 0 1 1 2 2v1h-2c-1-1-1-2-1-3h1z" class="D"></path><path d="M427 219h0l1 1h-2v1c-1 0-2 0-3 1h-1c-1-1-3-1-4-2h1 6l2-1z" class="a"></path><path d="M401 215l12 4-1 1h-1-1c-2-1-7-3-9-5z" class="O"></path><path d="M412 213c3 0 6 2 8 2 3 1 6 0 7 1h2 0c1-1 1 0 2-1l-1 1v1c-1 1-1 1-3 2h0c-1-1-2-1-3-1-2 0-4 0-5-1h-6l2-1v-1h-2l1-1h0l-2-1z" class="Y"></path><path d="M415 215c2 1 3 1 4 2h-1 1 0-6l2-1v-1z" class="e"></path><path d="M391 215v-1c1 0 1 0 2-1s1-2 3-3c0 1 0 2 1 3h2l2 2c2 2 7 4 9 5h1v1h0-4 0c-2 1-3 1-4 2l-1-2c-2 0-2-1-3-2-1 0-1 0-1-1h-1l-6-3z" class="G"></path><path d="M399 219c2 0 5 1 7 1-2 1-3 1-4 1-2 0-2-1-3-2z" class="K"></path><path d="M406 220h4 1v1h0-4 0c-2 1-3 1-4 2l-1-2c1 0 2 0 4-1z" class="Q"></path><path d="M428 220c1-1 2-2 3-2l1 1v2l1 1-3 3c0 1-1 2-1 3l-1 1h1v1h0c-1 0-2 1-4 2v2h0l-2-2v-2c-2-2-4-4-7-6-1-1-3-2-5-3h0v-1h1l1-1 5 1c1 1 3 1 4 2h1c1-1 2-1 3-1v-1h2z" class="P"></path><path d="M411 221c5 2 10 4 15 8 0 1-1 2-1 3v2h0l-2-2v-2c-2-2-4-4-7-6-1-1-3-2-5-3h0z" class="t"></path><path d="M428 220c1-1 2-2 3-2l1 1v2l1 1-3 3c-1 0-1 1-2 1h-1c-2-2-3-3-5-4h1c1-1 2-1 3-1v-1h2z" class="W"></path><path d="M432 221l1 1-3 3c-1 0-1 1-2 1h-1c-2-2-3-3-5-4h1c2 0 4 0 6 1 0 1 0 1 1 1 0-1 1-2 2-3z" class="C"></path><path d="M535 451l1-1c1 0 1 0 1 1h1 2c1 0 3-1 4 0h5c1 0 3 2 4 2v1c3 2 6 3 8 6l-1 1c1 1 1 2 1 3-3-4-5-5-9-6-5 0-9 0-13 4-2 2-4 6-4 9-1 4-1 7-3 9-3 4-7 7-11 10v1 1c-1-1-1-1-3-1l1-1c1-1 1-1 1-2-1-1-1 0-2 0 1-1 2-2 3-2 1-1 2-2 2-3h0c0-1-1-1-1-1v-1h-1l3-3c1-2 3-8 2-10h1c0 1 0 1 1 1v-2c0-1 0-2 1-3v-1c-1 1-2 1-3 2h0c0-1 1-2 2-3v-1l2-3v-1c2-1 2-1 3-3 0-1 1-1 2-3h0z" class="AZ"></path><path d="M535 451l1-1c1 0 1 0 1 1l1 3c-2 1-3 2-4 3-1-1-1-1-1-3 0-1 1-1 2-3h0z" class="Q"></path><path d="M526 468h1c0 1 0 1 1 1-1 4-2 10-6 12h0-1l3-3c1-2 3-8 2-10z" class="i"></path><path d="M548 455c4-1 6 1 9 3l3 3c1 1 1 2 1 3-3-4-5-5-9-6v-1l-4-2z" class="Y"></path><path d="M530 457c2-1 2-1 3-3 0 2 0 2 1 3l-1 1-5 9c0-1 0-2 1-3v-1c-1 1-2 1-3 2h0c0-1 1-2 2-3v-1l2-3v-1z" class="v"></path><path d="M530 457c2-1 2-1 3-3 0 2 0 2 1 3l-1 1c-2 1-2 3-5 3l2-3v-1z" class="o"></path><path d="M538 451h2c1 0 3-1 4 0h5c1 0 3 2 4 2v1c-2-1-3-1-5-2-3 0-7 1-10 2l-1-3h1z" class="z"></path><path d="M523 483l1-1c5-3 6-7 7-12v-1c-1 1-1 2-2 2 0-3 2-7 3-9 2-4 5-6 9-7 2-1 4-2 6-1-1 0-3 0-5 1-2 0-5 3-7 4 0 1-1 1 0 2 3-4 8-6 13-6l4 2v1c-5 0-9 0-13 4-2 2-4 6-4 9-1 4-1 7-3 9-3 4-7 7-11 10v1 1c-1-1-1-1-3-1l1-1c1-1 1-1 1-2-1-1-1 0-2 0 1-1 2-2 3-2 1-1 2-2 2-3h0z" class="q"></path><path d="M427 161c1 0 2 0 3-1h0c0 1-1 2-1 4h1c-2 4-4 8-6 11-1 2-2 5-4 7l-10 10c-2 2-3 5-5 7-1 1-3 2-4 4h-1c-1 2-2 5-3 7h-1c-2 1-2 2-3 3s-1 1-2 1v1 1c-1-1-1-1-1-2l-1 1h0c0-1 1-2 2-3 0 0 1-2 2-2-1-1-2-2-3-2l-1 1-2 1h0-2 0l-1-1 1-1 1-2c1 0 3-2 3-2 0-1 1-2 2-3 3-4 8-8 12-12v-1s0-1 1-1l2-3c2-2 6-5 7-7 2-2 4-3 5-4 0-1 2-3 3-3l6-9z" class="AC"></path><defs><linearGradient id="a" x1="406.31" y1="181.408" x2="414.897" y2="180.608" xlink:href="#B"><stop offset="0" stop-color="#4d463f"></stop><stop offset="1" stop-color="#695e5b"></stop></linearGradient></defs><path fill="url(#a)" d="M413 177c2-2 4-3 5-4h0c-1 2-2 3-4 4l1 1h1c0-1 1-1 1-2h1 1l-16 12s0-1 1-1l2-3c2-2 6-5 7-7z"></path><path d="M427 161c1 0 2 0 3-1h0c0 1-1 2-1 4-2 4-6 9-10 12h-1-1c0 1-1 1-1 2h-1l-1-1c2-1 3-2 4-4h0c0-1 2-3 3-3l6-9z" class="AO"></path><path d="M391 201v1c-1 2-3 4-4 5-1 0-2 1-2 1v1c1 0 1 1 2 0h0 1l1-1v-1h1v-1c1 0 1-1 1-2 1 0 1 0 1-1 0 0 1 0 1-1 1-3 5-6 7-8 1 2 2 3 2 5l-1 1h0c0 1-1 1-1 2h-1l1 1c-1 2-2 5-3 7h-1c-2 1-2 2-3 3s-1 1-2 1v1 1c-1-1-1-1-1-2l-1 1h0c0-1 1-2 2-3 0 0 1-2 2-2-1-1-2-2-3-2l-1 1-2 1h0-2 0l-1-1 1-1 1-2c1 0 3-2 3-2 0-1 1-2 2-3z" class="N"></path><path d="M390 208c0-1 1-2 2-3l1 1h2c-1 2-2 3-2 4-1-1-2-2-3-2z" class="B"></path><path d="M399 202l1 1c-1 2-2 5-3 7h-1c-2 1-2 2-3 3s-1 1-2 1v1 1c-1-1-1-1-1-2l-1 1h0c0-1 1-2 2-3 0 0 1-2 2-2 0-1 1-2 2-4l4-4z" class="W"></path><path d="M350 160l1 1c4 2 9 4 13 3h1l-2 2h-2 1v1 1h1v2-1h1l1 1-1 3v2c0 2-1 4 0 5l3-4 1 2-1 2c0 1 1 1 2 2l-1 2-2 2h-1 0v2h-1c-1 1-2 2-2 3h-1v2h-2c0 1-1 2-1 2l-1 1h-1c-1 0-1 1-1 1-1 0-1-1-2-2l-1 1h-1v2l-1 1-1-1v-7-6-16c0-3 0-7 1-9z" class="O"></path><path d="M351 196v-3h2c-1 1-1 2-1 3h-1z" class="R"></path><path d="M356 194c1-1 1-1 3-1 0 1-1 2-1 2l-1 1c0-1-1-1-1-2z" class="Z"></path><path d="M352 190h1l2 1-1 1c-1 1-1 1-2 1v-3z" class="d"></path><path d="M353 195c1 0 2-1 2-1h1c0 1 1 1 1 2h-1c-1 0-1 1-1 1-1 0-1-1-2-2zm-1-11l2 1c1 0 1 1 2 2l-3 1h-1v-1-3z" class="s"></path><path d="M360 184h0c0 2 0 3-2 4l-1 1c0 1 0 1 1 1l-1 1v-1l-1 1c-1-1-1-1-1-2l1-2h1l3-3z" class="m"></path><path d="M352 174c0-1 1-2 0-3v-2-4h1c1 1 3 1 4 1v2l-1 1v1h-2v3l-2 1z" class="AM"></path><path d="M354 180l4 4h1c0-1 0-2 1-3v3h0l-3 3h-1 0c-1-1-1-2-2-2l-2-1v-2l2-2z" class="k"></path><path d="M352 174l2-1v-3h2s-1 2-1 3 1 1 1 3h-1s0 1 1 1c0 2 0 2-2 3l-2 2v-5l1-1c-1-1-1-1-1-2z" class="p"></path><path d="M357 166h-1c-2-2-4-1-5-4h0c1 0 2 1 4 1l6 3h1v1 1h1v2h-3c0-1 0-1 1-1l-1-1c-1 0-1 1-2 1h-2l1-1v-2z" class="AL"></path><path d="M362 180c1-2 1-4 2-5 0 2-1 4 0 5l3-4 1 2-1 2c0 1 1 1 2 2l-1 2c-1 0-1 0-2-1-1 1 0 1 0 2h-1c0-1 0-1-1 0v1c-1 0-1 1-3 1v-1l1-1v-1h-1-1 0 0c1-1 1-2 1-3l1-1z" class="AF"></path><path d="M362 180c1-2 1-4 2-5 0 2-1 4 0 5v1c0 1-1 1-1 2h-1s0-1-1-2l1-1z" class="O"></path><path d="M356 176h2c1-1 1-1 2-1h0c0 1 0 1 1 2h1v3l-1 1c0 1 0 2-1 3v-3c-1 1-1 2-1 3h-1l-4-4c2-1 2-1 2-3-1 0-1-1-1-1h1z" class="V"></path><path d="M362 177v3l-1 1c0 1 0 2-1 3v-3c-1 1-1 2-1 3h-1l-4-4c2-1 2-1 2-3 0 1 0 3 1 4 1-1 2-1 3-1h1v-1h-1v-1h1c1 0 0 0 1-1z" class="r"></path><path d="M350 160l1 1c-1 0-1 1-1 1v8 19c0 1 0 5 1 6v1 2l-1 1-1-1v-7-6-16c0-3 0-7 1-9z" class="n"></path><path d="M356 169h2c1 0 1-1 2-1l1 1c-1 0-1 0-1 1h3v-1h1l1 1-1 3v2c-1 1-1 3-2 5v-3h-1c-1-1-1-1-1-2h0c-1 0-1 0-2 1h-2c0-2-1-2-1-3s1-3 1-3v-1z" class="Ad"></path><path d="M363 170v-1h1l1 1-1 3c0-1-2-1-3-1h-1l1 1h-1v-1c-2-1-2 0-4-1h0c1 0 2-1 3-1h1 3z" class="y"></path><path d="M327 176l4 4c0 1 0 2 1 3 1-1 1-1 1-2 1 1 1 1 2 1 1 1 1 1 2 0v-1h0v2h1c0 1 0 2 1 2h2l1-2 1 1c0 1 0 1 1 2l1 1v-2c0-1 0-1 1-1h0v1h3v6 7l1 1c1 1 1 2 1 3h0c0 1 0 3-1 4h-1v2 1l-1 1v1h-2-1-2l-1 1c0 1 0 2-1 3v1c-1-1-2-2-2-3l-2-2h0l-2-2v-1l-2-2-2-2c0-1-1-1-2-2 1-1 1-1 1-2 1-1 1-1 1-2-1 0-1 0-2-1v-2-3s1 0 1-1h1v-2l-1-2c-1-1-1-2-2-3h0l1-2c-1-1-1-2-1-4-1 0-1-1-1-2z" class="Z"></path><path d="M335 188l2-1c0 1 0 1 1 2l-1 1v1l-1 1v3c-1-1-1-1-2-1h0c1-1 1-1 1-2l1-1c-1-1-1-1-1-2v-1z" class="C"></path><path d="M337 183h1c0 1 0 2 1 2v1l-1 1h0-1l-1-1h-1c-1-1-1-1-1-2h2l1-1z" class="AJ"></path><path d="M342 183l1 1c0 1 0 1 1 2l1 1h-1s-1 0-1 1c-1 0 0 1 0 1l-1 1-1-1h0c0-2-1-2-2-3h0v-1h2l1-2z" class="AF"></path><path d="M342 191l1-1v1 1h1c0 1 0 1 1 2l-1 2h-1v-1h-1 0c1 1 1 1 1 2v1h0-1c-1 0-1 0-1-1-1 0-1-1-2-1v-2l1-1c0-1 1-2 2-2z" class="R"></path><path d="M342 191h1v1c-1 1-2 1-3 1h0c0-1 1-2 2-2z" class="C"></path><path d="M336 195v-3l1-1c1 1 2 2 2 3v2c1 0 1 1 2 1 0 1 0 1 1 1h1c1 1 2 2 2 3h1v1l-1 1h0v-1c-1 1-2 1-2 2-1 0-1 1-1 1v-2l-1-2v-1l-2-2c-1-1-2-1-3-2v-1z" class="H"></path><path d="M341 200l1-1 1 1v1h2 1v1l-1 1h0v-1c-1 1-2 1-2 2-1 0-1 1-1 1v-2l-1-2v-1z" class="B"></path><path d="M336 196c1 1 2 1 3 2l2 2v1l1 2v2l-1 2-1-1h-2v-2h0c-1-1-1-2-1-3l-1-1s0-1-1-2c1 0 1-1 1-2z" class="y"></path><path d="M336 196c1 1 2 1 3 2-1 0-1 0-2 1h0c0 1-1 1-1 1s0-1-1-2c1 0 1-1 1-2z" class="O"></path><path d="M341 201l1 2v2l-1 2-1-1h-2v-2h0c1 0 2 0 3-1v-2z" class="E"></path><path d="M342 205s0-1 1-1c0-1 1-1 2-2v1h0l1 1s0-1 1 0h2l-1 2h1 0v2 1l-1 1v1h-2-1-2l-1 1v-3-1l-1-1 1-2z" class="W"></path><path d="M346 209c1 1 1 1 2 1v1h-2-1c0-1 1-1 1-2z" class="F"></path><path d="M342 205s0-1 1-1c0-1 1-1 2-2v1 3l-2 1h0v1 1h0v1 1l-1 1v-3-1l-1-1 1-2z" class="N"></path><path d="M345 203l1 1s0-1 1 0h2l-1 2h1 0v2 1l-1 1c-1 0-1 0-2-1h0v-1l-1-2v-3h0z" class="b"></path><path d="M346 208c0-2 0-2 1-3l1 1h1c-1 1-1 2-3 3v-1z" class="a"></path><path d="M349 206h0v2 1l-1 1c-1 0-1 0-2-1h0c2-1 2-2 3-3z" class="B"></path><path d="M335 201h2c0 1 0 2 1 3h0v2h2l1 1 1 1v1 3c0 1 0 2-1 3v1c-1-1-2-2-2-3l-2-2h0l-2-2v-1l-2-2c1-1 1-1 1-2s0-1 1-1v-1-1z" class="H"></path><path d="M334 204c1 0 2 1 3 1 0 1 0 1-1 2 0 1 1 1-1 2v-1l-2-2c1-1 1-1 1-2z" class="G"></path><path d="M340 206l1 1 1 1v1 3c0 1 0 2-1 3v1c-1-1-2-2-2-3l-2-2h0c1-1 1-1 1-2v-1h1l1-2z" class="B"></path><path d="M340 206l1 1 1 1v1c0 1-1 1-1 2h0-1l-1-3 1-2z" class="C"></path><path d="M329 192s1 0 1-1h1c0 1 1 2 1 3h2c1 0 1 0 2 1v1c0 1 0 2-1 2 1 1 1 2 1 2l1 1h-2v1 1c-1 0-1 0-1 1s0 1-1 2l-2-2c0-1-1-1-2-2 1-1 1-1 1-2 1-1 1-1 1-2-1 0-1 0-2-1v-2-3z" class="a"></path><path d="M333 196c1 0 1 1 2 1v1c1 1 1 2 1 2l1 1h-2c-1 0-1-1-2-2v-3z" class="E"></path><path d="M332 194h2c1 0 1 0 2 1v1c0 1 0 2-1 2v-1c-1 0-1-1-2-1h-1v-2z" class="C"></path><path d="M334 194c1 0 1 0 2 1v1c0 1 0 2-1 2v-1c0-1 0-2-1-3z" class="d"></path><path d="M331 198v-1c1 1 2 1 2 2 1 1 1 2 2 2v1 1c-1 0-1 0-1 1s0 1-1 2l-2-2c0-1-1-1-2-2 1-1 1-1 1-2 1-1 1-1 1-2z" class="I"></path><path d="M327 176l4 4c0 1 0 2 1 3 1-1 1-1 1-2 1 1 1 1 2 1 1 1 1 1 2 0v-1h0v2l-1 1h-2c0 1 0 1 1 2l-1 1 1 1v1c0 1 0 1 1 2l-1 1c0 1 0 1-1 2h0-2c0-1-1-2-1-3v-2l-1-2c-1-1-1-2-2-3h0l1-2c-1-1-1-2-1-4-1 0-1-1-1-2z" class="D"></path><path d="M330 187l1-1c1 1 1 2 1 3h-1l-1-2z" class="C"></path><path d="M331 189h1c0 2 0 2 1 3 0 1 1 1 1 2h0-2c0-1-1-2-1-3v-2z" class="U"></path><path d="M327 176l4 4c0 1 0 2 1 3 1-1 1-1 1-2 1 1 1 1 2 1 1 1 1 1 2 0v-1h0v2l-1 1h-2c0 1 0 1 1 2l-1 1c-1-1-1-2-2-3-1-2-3-3-4-6-1 0-1-1-1-2z" class="s"></path><path d="M345 187v-2c0-1 0-1 1-1h0v1h3v6 7l1 1c1 1 1 2 1 3h0c0 1 0 3-1 4h-1 0-1l1-2h-2c-1-1-1 0-1 0l-1-1 1-1v-1h-1c0-1-1-2-2-3h0v-1c0-1 0-1-1-2h0 1v1h1l1-2v-1h2v-3l-1-1-1-1-1-1h1z" class="d"></path><path d="M345 187v-2c0-1 0-1 1-1h0v1 1h1v1l-2 1-1-1h1z" class="AO"></path><path d="M349 191v7l1 1c1 1 1 2 1 3h0c-1 1-1 2-1 2h-1v-4-9z" class="r"></path><path d="M343 198h1 1v-2c0-1 1-1 2-2h0c0 1 0 2-1 3l2 2-1 1c-1 0-1 1-1 1h-1c0-1-1-2-2-3h0z" class="Z"></path><path d="M346 201s0-1 1-1l1-1 1 1v4h1s0-1 1-2c0 1 0 3-1 4h-1 0-1l1-2h-2c-1-1-1 0-1 0l-1-1 1-1v-1z" class="C"></path><defs><linearGradient id="b" x1="497.563" y1="68.453" x2="483.259" y2="82.649" xlink:href="#B"><stop offset="0" stop-color="#020004"></stop><stop offset="1" stop-color="#4b4745"></stop></linearGradient></defs><path fill="url(#b)" d="M467 68l2-3h2 5 4c3 1 5 1 8 2l4 2 1 1v-1h0l1-1 3 1 2 1 14 7h0-1l-1 1h1-1v1c1 1 2 3 3 4l1 3 1 1-2 2h0l-2 1c-1 0-2 0-2 1-2-1-3-1-5-2s-4-2-5-3h-2s-1 0-2-1h0-1l-4-2h0l3 2h0v1c-3-3-7-5-11-8l-4-4-12-6z"></path><path d="M497 78l9 3v1c-3 1-6-2-9-4h0z" class="I"></path><path d="M479 74c1 0 2 0 3 1 1 0 4 2 4 2v2l-3-1-4-4z" class="P"></path><path d="M500 85c3 1 8 4 11 3h1c1 0 1 0 2 1l-2 1c-1 0-2 0-2 1-2-1-3-1-5-2s-4-2-5-3v-1z" class="k"></path><path d="M480 65c3 1 5 1 8 2l4 2c1 1 2 2 2 3l-18-7h4z" class="H"></path><path d="M486 77l14 8v1h-2s-1 0-2-1h0-1l-4-2h0l3 2h0v1c-3-3-7-5-11-8l3 1v-2z" class="u"></path><path d="M483 78l3 1 12 7s-1 0-2-1h0-1l-4-2h0l3 2h0v1c-3-3-7-5-11-8z" class="b"></path><path d="M493 70v-1h0l1-1 3 1 2 1 14 7h0-1l-1 1h1-1v1c1 1 2 3 3 4l1 3 1 1-2 2h0c-1-1-1-1-2-1 0-1 1-1 1-1 1-1 1-2 0-3-1-5-14-9-19-12 0-1-1-2-2-3l1 1z" class="C"></path><path d="M493 70v-1h0l1-1 3 1 2 1 14 7h0-1c-2-1-4-1-6-2-4-1-8-3-13-5z" class="r"></path><path d="M248 570l3 4c1 1 0 1 1 1l3-3v1l2-1 1 1c3 4 7 7 11 10 3 2 11 6 12 8 1 0 2 1 3 1l2-1c0 1 0 1 1 2l1 1c3 2 4 4 7 5 1 0 1 0 1 1v1h0c-1 1-2 2-3 2l-1 1v1l-3 3c-1 1-2 1-3 2-3-4-7-7-11-11l-14-10h-1c0-1-9-6-10-7s-3-2-4-3v-4l-1-3 1-1h0l2-1z" class="t"></path><path d="M263 586l5 3h-2v1l-3-2v-1h0c1 1 1 1 2 1l-2-2z" class="AN"></path><path d="M281 591c1 0 2 1 3 1l2-1c0 1 0 1 1 2l1 1c3 2 4 4 7 5 1 0 1 0 1 1v1c-3-1-6-3-9-4-1-1-3-2-4-2 0-1 1-2 1-3-1 0-2 0-3-1z" class="o"></path><defs><linearGradient id="c" x1="279.298" y1="602.908" x2="272.36" y2="586.413" xlink:href="#B"><stop offset="0" stop-color="#87231e"></stop><stop offset="1" stop-color="#b6382a"></stop></linearGradient></defs><path fill="url(#c)" d="M268 589l19 12v1c-1 0-1-1-2-1l-1 1-7-4c-3-3-7-6-11-8v-1h2z"></path><path d="M255 573l2-1 1 1c3 4 7 7 11 10 3 2 11 6 12 8 1 1 2 1 3 1 0 1-1 2-1 3-1-1-2-1-3-2-4-2-7-4-10-7-2-1-4-2-5-3-2-2-3-4-5-5-2-2-4-3-5-5z" class="l"></path><path d="M275 599c0-1-1-2-1-2-1-1-2-1-2-2 0 1 1 1 2 1 0 1 1 1 2 2h1l7 4 1-1c1 0 1 1 2 1v-1c2 0 5 1 6 2l-1 1v1l-3 3c-1 1-2 1-3 2-3-4-7-7-11-11z" class="AB"></path><path d="M287 601c2 0 5 1 6 2l-1 1-2 1c-2 0-4-2-6-3l1-1c1 0 1 1 2 1v-1z" class="z"></path><path d="M248 570l3 4c1 1 0 1 1 1v1c0 1 2 2 2 2l7 6c1 0 2 1 2 2l2 2c-1 0-1 0-2-1h0v1l3 2c4 2 8 5 11 8h-1c-1-1-2-1-2-2-1 0-2 0-2-1 0 1 1 1 2 2 0 0 1 1 1 2l-14-10h-1c0-1-9-6-10-7s-3-2-4-3v-4l-1-3 1-1h0l2-1z" class="h"></path><path d="M263 588l3 2c4 2 8 5 11 8h-1c-1-1-2-1-2-2-1 0-2 0-2-1 0 1 1 1 2 2 0 0 1 1 1 2l-14-10c2 0 2 1 4 2l1 1c1 1 2 2 3 2 0-1-4-4-5-5-1 0-1-1-1-1z" class="L"></path><path d="M248 570l3 4c1 1 0 1 1 1v1c0 1 2 2 2 2l7 6c1 0 2 1 2 2l2 2c-1 0-1 0-2-1h0c-1 0-2-1-3-1-3-3-6-5-9-8-2-3-4-4-5-7h0l2-1z" class="AD"></path><path d="M185 505h1c2-2 5-4 8-3h0 2c4 1 6 3 9 5 1 0 2 1 2 1l1 1c1 0 2 1 2 2 1 1 2 2 2 3s1 2 1 3h-1v1 2c1 1 1 3 1 5l1 3-1 1h0c0 2-1 3-1 4h-1c-1 0-1-1-1-2-1 1-2 1-3 0-1 0-2 0-3-1h0l1-1h-3c-2 0-5 0-7-1h-1c-2-1-4-1-6-2l-3-3c-3-3-3-7-3-11l1-1c0-2 1-4 2-6z" class="V"></path><path d="M192 506l2-1c2 0 3 0 4 1 1 0 2 1 2 1 0 1-1 1-1 2h0c-2-2-4-2-7-2-1 0 0 0 0-1z" class="L"></path><path d="M185 513l1 1c0 4 0 7 4 10 1 1 2 1 3 2l1 1c-2-1-3-1-5-2s-4-4-5-6c1-2 0-3 0-5l1-1z" class="AG"></path><path d="M189 507c1-1 2-1 3-1 0 1-1 1 0 1-3 2-5 4-6 7l-1-1-1 1c0 2 1 3 0 5v-6c0-2 1-4 2-5l2-3 1 2z" class="i"></path><path d="M188 505l1 2-2 2-1-1 2-3z" class="X"></path><path d="M186 508l1 1c0 1-1 3-2 4l-1 1c0 2 1 3 0 5v-6c0-2 1-4 2-5z" class="S"></path><path d="M200 507c2 1 4 3 4 5v3h-1l-1-1c-1-1-1-1-1-2-2-1-4-1-5-1-1 1-2 1-2 2 0 0 0 1-1 1h-1v-1c1-1 1-2 2-2 1-1 2-1 4-1h1 1l-1-1c0-1 1-1 1-2z" class="J"></path><path d="M183 511c0 1 0 1 1 2v6c1 2 3 5 5 6s3 1 5 2h5c1 2 2 1 3 2-2 0-5 0-7-1h-1c-2-1-4-1-6-2l-3-3c-3-3-3-7-3-11l1-1z" class="AZ"></path><path d="M185 505h1c2-2 5-4 8-3h0 2 0l-1 1v1h1l1 1h-3l-2 1c-1 0-2 0-3 1l-1-2-2 3c-1 1-2 3-2 5-1-1-1-1-1-2 0-2 1-4 2-6z" class="u"></path><path d="M194 502h2 0l-1 1v1h1l1 1h-3l-2 1c-1 0-2 0-3 1l-1-2c2-1 5-2 6-3z" class="S"></path><path d="M202 514l1 1 2 7 1 7h0-1-3c-1-1-2 0-3-2h-5l-1-1h5c1 0 3-1 3-3 1 0 2-2 2-3s-1-5-1-6z" class="g"></path><path d="M199 527h0 1c1-1 3-2 4-4 0 0 0-1 1-1l1 7h0-1-3c-1-1-2 0-3-2z" class="V"></path><path d="M196 502c4 1 6 3 9 5 1 0 2 1 2 1l1 1c1 0 2 1 2 2 1 1 2 2 2 3s1 2 1 3h-1v1 2c1 1 1 3 1 5l1 3-1 1h0c0 2-1 3-1 4h-1c-1 0-1-1-1-2-1 1-2 1-3 0-1 0-2 0-3-1h0l1-1h1 0l-1-7-2-7h1v-3c0-2-2-4-4-5 0 0-1-1-2-1-1-1-2-1-4-1h3l-1-1h-1v-1l1-1h0z" class="l"></path><path d="M213 525l1 3-1 1h0l-1-4h1z" class="o"></path><path d="M212 520c1 1 1 3 1 5h-1l-1-5h0l1 1v-1z" class="i"></path><path d="M204 512l3 7c-1 1-1 1-1 2v1h-1l-2-7h1v-3z" class="t"></path><path d="M206 511c1 1 2 1 2 2l1-1 2 3 1-1c0 1 1 2 1 3h-1v1 2 1l-1-1h0c-1-4-3-6-5-9z" class="Q"></path><path d="M211 515l1-1c0 1 1 2 1 3h-1v1l-1-3z" class="K"></path><path d="M207 519l3 12h0c-1 1-2 1-3 0-1 0-2 0-3-1h0l1-1h1 0l-1-7h1v-1c0-1 0-1 1-2z" class="T"></path><path d="M196 502c4 1 6 3 9 5 1 0 2 1 2 1l1 1c1 0 2 1 2 2 1 1 2 2 2 3l-1 1-2-3-1 1c0-1-1-1-2-2-2-2-3-4-5-5h-2v-1h-1v1c-1-1-2-1-4-1h3l-1-1h-1v-1l1-1h0z" class="e"></path><path d="M201 506c1 0 2 0 4 1 1 1 3 4 4 5l-1 1c0-1-1-1-2-2-2-2-3-4-5-5z" class="T"></path><path d="M405 168l1-1c2 0 2 0 3 1s2 1 2 2c1 1 1 2 1 3v3l1 1c-1 2-5 5-7 7l-2 3c-1 0-1 1-1 1v1c-4 4-9 8-12 12-1 1-2 2-2 3 0 0-2 2-3 2l-1 2-1 1 1 1h0 2 0l2-1 1-1c1 0 2 1 3 2-1 0-2 2-2 2-1 1-2 2-2 3l-1 2-2-1c-1 0-1 0-1 1 0-1-1-2-1-2v-1c-1-1-3-1-5-1 1 0 1-1 2-1l-1-1v-1h-4 0l-1-1h-1v-3c0-1-1-1-1-1-1 0-1-1-1-2l1-1 1-2h0c1-4 9-12 12-15h0l7-8 6-6 6-3z" class="w"></path><path d="M405 183l1 1-2 3h-2c1-2 1-3 3-4z" class="N"></path><path d="M384 192c0-1 1-1 1-2h0l1-1h2c0 1-2 3-3 4 0 0-1 0-1-1z" class="B"></path><path d="M405 168l1-1c2 0 2 0 3 1s2 1 2 2c-1 0-2 1-4 0 0-1-1-2-2-2z" class="J"></path><path d="M384 192c0 1 1 1 1 1 0 1-1 3-2 3-1 3-2 5 0 8l1 1h2v1l-1 2c-1-1-2-1-2-1-1-1 0-1-1-1-2-2-2-4-2-7 1-1 1-2 2-3 0-1 1-2 2-4z" class="E"></path><path d="M384 192c0 1 1 1 1 1 0 1-1 3-2 3h0l1-2-2 2h0c0-1 1-2 2-4z" class="b"></path><path d="M402 187h2c-1 0-1 1-1 1v1c-4 4-9 8-12 12-1 1-2 2-2 3 0 0-2 2-3 2v-1h0c3-4 7-8 10-12l6-6z" class="F"></path><path d="M411 170c1 1 1 2 1 3v3l1 1c-1 2-5 5-7 7l-1-1v-2l1-1c1-1 2-4 4-5v-2l1-1v-2z" class="J"></path><path d="M410 175s1 1 1 2c-1 1-1 2-2 2l-1 1h0l-1 2h-1v-2c1-1 2-4 4-5z" class="I"></path><path d="M374 200c1-4 9-12 12-15 0 2-2 4-4 5l-3 5c-1 0-2 1-2 2h1v1c-1 0-1 1-1 2v1h-1c-1 0 0 0-1 1v3c1 0 2-2 2-3l1-1v-1c1-1 1 0 1-1s1-1 1-1v-1l2-3 2-2c-1 2-2 3-2 4-1 1-1 2-2 3l-2 4c-1 1-2 2-2 4l1-1c1 1 1 3 1 4h-2l-1-1h-1v-3c0-1-1-1-1-1-1 0-1-1-1-2l1-1 1-2h0z" class="F"></path><path d="M374 200h0v1c0 1 0 2-1 3 1 1 1 1 2 1 0 1 0 1 1 2l1-1c1 1 1 3 1 4h-2l-1-1h-1v-3c0-1-1-1-1-1-1 0-1-1-1-2l1-1 1-2z" class="D"></path><path d="M380 199c0 3 0 5 2 7 1 0 0 0 1 1 0 0 1 0 2 1l-1 1 1 1h0 2 0l2-1 1-1c1 0 2 1 3 2-1 0-2 2-2 2-1 1-2 2-2 3l-1 2-2-1c-1 0-1 0-1 1 0-1-1-2-1-2v-1c-1-1-3-1-5-1 1 0 1-1 2-1l-1-1v-1h-4 0 2c0-1 0-3-1-4l-1 1c0-2 1-3 2-4l2-4z" class="c"></path><path d="M378 210s1-1 2-1v1h2c0 1 1 1 1 1 0 1 1 1 2 1l1 2v2c-1 0-1 0-1 1 0-1-1-2-1-2v-1c-1-1-3-1-5-1 1 0 1-1 2-1l-1-1v-1h-4 0 2z" class="m"></path><path d="M390 208c1 0 2 1 3 2-1 0-2 2-2 2-1 1-2 2-2 3l-1 2-2-1v-2h1v-2c1-1 1-2 2-3l1-1z" class="E"></path><path d="M387 214c0-1 1-1 1-2h3c-1 1-2 2-2 3l-1 2-2-1v-2h1z" class="M"></path><path d="M165 423c1-1 1-4 2-5 1 0 1 1 2 1 0-1-1-2-1-3 2-8 8-15 14-20l4 2h0l2 1-3 3c-1 3-1 6 0 8 0 2 1 3 2 4v1l3 1-1 1-2-1v1h-1v2h0l-1 1h2l1 1v5l1 1c0 3-1 6-3 7h0c-5 1-15 4-18 8-1 1-2 3-3 4 0 2 0 4-1 5v-1h-3v1l-1-3c-1-5-1-11 0-17 1 0 2 0 2-1l1-4c1-1 1-2 2-3z" class="V"></path><path d="M188 426l1 1c0 3-1 6-3 7l2-8zm-28 22l1-1c1 0 1-1 2-1l1 4h-3v1l-1-3z" class="l"></path><path d="M163 426c1-1 1-2 2-3 0 3-1 9 0 11 1 1 3 2 4 3v1c-2-1-4-3-5-4l-1-1c-1-1-1-1-1-3l1-4z" class="o"></path><path d="M160 431c1 0 2 0 2-1 0 2 0 2 1 3l1 1c-2 3-1 8-1 12-1 0-1 1-2 1l-1 1c-1-5-1-11 0-17z" class="S"></path><path d="M186 398l2 1-3 3c-1 3-1 6 0 8 0 2 1 3 2 4v1l3 1-1 1-2-1v1h-1v2h0l-1 1-2-2c-2-4-2-8-1-12-2 3-3 6-6 7h0c4-4 5-10 10-14h0v-1h0z" class="AG"></path><path d="M187 416c-1 0-4-2-4-4 0-3 0-7 2-10h0c-1 3-1 6 0 8 0 2 1 3 2 4v1l3 1-1 1-2-1z" class="V"></path><defs><linearGradient id="d" x1="255.596" y1="638.939" x2="279.012" y2="657.442" xlink:href="#B"><stop offset="0" stop-color="#191517"></stop><stop offset="1" stop-color="#3f3533"></stop></linearGradient></defs><path fill="url(#d)" d="M259 631c2 0 5 0 7-1l13-3c-1 1-1 1-2 1h0 1c1 0 2 0 3 1h1l24 1 15 1v2h-3 0c-5 1-11 0-16 1-4 0-7 0-10 1l-2 2c-1 0-2 0-3 1h-5 0c1 0 1 0 2 1 1 0 3-1 4 0h2 1 5c2 1 4 1 7 1h11 7v2h0l-1 2h0 0l-4-1h-3-9 0-1-1-1c-1-1-6 0-8 0h-3-9c-2 1-5-1-7 1h-1 0 0l-1-1c-1-1-1-1-2-1-1-1-4-1-5 0-1 0-2 1-3 1l-2 2 1 1c1 1 0 2 1 3 0 1 1 3 2 4v1l-1-1h-1l-3-4h-1c-1 3-1 6-2 9 1 1 1 1 1 2s-1 1-2 2v3c0 5 0 9 1 13 1 2 1 4 1 7-4-6-6-14-6-22 0-1-1-3 0-5 0-1 1-2 1-3 0-3 0-6 1-8 2-1 3-2 4-4h0c-1-1 1-4 1-5l2-3c-1 0-1 0-1-1h0l-1-1h-4l5-2z"></path><path d="M262 640c1-2 2-3 4-4 1-2 3-1 5-1-1 2-3 2-4 3l-3 2h-2z" class="n"></path><path d="M263 653c-2-2-2-4-4-6v-1c1-2 1-4 3-6h2c-2 1-2 2-2 3l-2 2 1 1c1 1 0 2 1 3 0 1 1 3 2 4v1l-1-1z" class="u"></path><path d="M306 630l15 1v2h-3c-5-1-12 0-18 0 0-1 0-1-1-1h-1c3-1 6-1 8-2z" class="M"></path><path d="M282 629l24 1c-2 1-5 1-8 2-5 1-13 3-19 2-4 0-7-1-10-3 4-2 8-2 13-2z" class="V"></path><path d="M271 635l3 1c2 0 6 0 8 1-2 1-5 0-6 1s-1 1-2 1l-1 1v1c9-1 18 1 27 1h15 6l-1 2h0 0l-4-1h-3-9 0-1-1-1c-1-1-6 0-8 0h-3-9c-2 1-5-1-7 1h-1 0 0l-1-1c-1-1-1-1-2-1-1-1-4-1-5 0-1 0-2 1-3 1 0-1 0-2 2-3l3-2c1-1 3-1 4-3z" class="k"></path><path d="M271 635l3 1 1 1c-2 1-6 1-8 1 1-1 3-1 4-3z" class="AH"></path><defs><linearGradient id="e" x1="267.808" y1="123.267" x2="300.291" y2="151.315" xlink:href="#B"><stop offset="0" stop-color="#363435"></stop><stop offset="1" stop-color="#776a62"></stop></linearGradient></defs><path fill="url(#e)" d="M283 103l3-3h0c-4 6-8 14-8 21l1 3c1 3 1 6 1 10 2 6 4 11 9 15h2v1l5 5 6 5c2 2 1 2 2 3s4 1 5 1l3 1h0-1c0 1-1 0 0 1h0c0 1 0 2 1 3h-1-4v1h-1-1-1 0v1 1c0 1-1 1-1 1v2c-1-1-1-1-2-1-2-2-5-3-8-4 0-1-1-2-2-3h0-1c-1 0-1-1-1-1h-1v-2l-1-1c-1 0-2 1-3 2l1 1 1 2v2h-1v-1l-1 1 1 1v2l1 1v2 2c0-1-2-2-3-3l-8-8-4-7c-1-1-1-3-1-4-1-4 0-8-2-11v-7-6c0-1 1-1 1-2 1-4 1-7 2-11l1-1c0-1 1-1 1-2h1l2-2-1 1v1h1v-1c2-4 5-8 7-12z"></path><path d="M279 124c1 3 1 6 1 10-1-1-2-2-2-3 0-2 0-4 1-5v-2z" class="O"></path><path d="M274 116l2-2-1 1v1h1l-2 7-1 5v13l2 1h0-1l-1 1v2h1-2v3c-1-5-1-10-1-15l-2-1v-1-1c1-4 1-7 2-11l1-1c0-1 1-1 1-2h1z" class="M"></path><path d="M272 123s0-1 1-1v1h1l-1 5h0c-1-1-1-3-1-4v-1z" class="AJ"></path><path d="M271 125c1 2 0 6 0 8l-2-1v-1c1-1 2-4 2-6z" class="H"></path><path d="M274 116l2-2-1 1v1h1l-2 7h-1v-1c-1 0-1 1-1 1 0 1-1 2-1 2 0 2-1 5-2 6v-1c1-4 1-7 2-11l1-1c0-1 1-1 1-2h1z" class="m"></path><defs><linearGradient id="f" x1="270.152" y1="156.242" x2="279.306" y2="153.434" xlink:href="#B"><stop offset="0" stop-color="#212021"></stop><stop offset="1" stop-color="#4e4948"></stop></linearGradient></defs><path fill="url(#f)" d="M269 130v1 1l2 1c0 5 0 10 1 15v-3h2c0 2 0 4 1 5l2 4c0 1 1 2 2 3l1 1 2 2h0c1 2 0 4 1 5 0 0 1 1 2 1l1 2v2h-1v-1l-1 1 1 1v2l1 1v2 2c0-1-2-2-3-3l-8-8-4-7c-1-1-1-3-1-4-1-4 0-8-2-11v-7-6c0-1 1-1 1-2z"></path><path d="M272 148v-3h2c0 2 0 4 1 5l2 4c0 1 1 2 2 3l1 1 2 2h0c1 2 0 4 1 5 0 0 1 1 2 1l1 2v2h-1v-1l-1 1 1 1v2c-1 0-3-2-4-3-6-7-7-13-9-22z" class="d"></path><path d="M286 168v2h-1v-1l-1 1 1 1v2c-1 0-3-2-4-3l2-2v1c1 0 2 0 3-1z" class="Z"></path><path d="M282 160c1 2 0 4 0 5s-1 1-2 1l-1-1v1l-1-1v-2c-1 0-1-1-1-2s0-1 1-1c1-1 0-2 1-3l1 1 2 2h0z" class="x"></path><path d="M290 157v-1c-1-2-3-4-5-5 0-1-1-2-2-2 6 1 9 6 12 10h1v-4l6 5c2 2 1 2 2 3s4 1 5 1l3 1h0-1c0 1-1 0 0 1h0c0 1 0 2 1 3h-1-4v1h-1-1-1 0v1 1c0 1-1 1-1 1v2c-1-1-1-1-2-1-2-2-5-3-8-4 0-1-1-2-2-3h0-1c-1 0-1-1-1-1h-1v-2l-1-1c-1 0-2 1-3 2l1 1c-1 0-2-1-2-1-1-1 0-3-1-5h0l-2-2h2l-1-2-1-1h0l1-1v1h2s0-1 1-1c1 1 4 3 5 5l1-2z" class="m"></path><path d="M290 157c1 1 1 2 1 3h-1l-1-1 1-2z" class="O"></path><path d="M296 162c1 0 1-1 3 0l1 1v1h0v1h0-2 0c-1-1-1-1-2-3h0z" class="AF"></path><path d="M296 162c1 1 3 2 4 2v1h0-2 0c-1-1-1-1-2-3z" class="x"></path><path d="M281 156v-1c1 1 2 2 3 2 2 2 5 4 7 6 0 1-1 1-1 2-1 0-1 0-1-1h-1l-1-1c-1 0-2 1-3 2l1 1c-1 0-2-1-2-1-1-1 0-3-1-5h0l-2-2h2l-1-2z" class="r"></path><path d="M282 158c2 1 4 3 5 5-1 0-2 1-3 2l1 1c-1 0-2-1-2-1-1-1 0-3-1-5h0l-2-2h2z" class="O"></path><defs><linearGradient id="g" x1="300.622" y1="169.674" x2="299.613" y2="164.596" xlink:href="#B"><stop offset="0" stop-color="#574e4e"></stop><stop offset="1" stop-color="#6b665f"></stop></linearGradient></defs><path fill="url(#g)" d="M293 164h0c1 0 1 0 2 1h1v-2l-1-1h1 0c1 2 1 2 2 3h0l3 1h3c2 1 3 1 5 1 0 0 1 0 2-1 0 1 0 2 1 3h-1-4v1h-1-1-1 0v1l-2-1c-1-1-2-1-3-2l-6-4z"></path><path d="M309 167s1 0 2-1c0 1 0 2 1 3h-1-4v1h-1-1-1c0-1 1-1 1-2s3-1 4-1z" class="C"></path><path d="M291 163l2 1 6 4c1 1 2 1 3 2l2 1v1c0 1-1 1-1 1v2c-1-1-1-1-2-1-2-2-5-3-8-4 0-1-1-2-2-3h0-1c-1 0-1-1-1-1h-1v-2h1c0 1 0 1 1 1 0-1 1-1 1-2z" class="I"></path><path d="M302 170l2 1v1c0 1-1 1-1 1-1 0-2-1-2-2 1 0 1 0 1-1z" class="Ad"></path><path d="M299 168c1 1 2 1 3 2 0 1 0 1-1 1l-3-2s0-1 1-1z" class="r"></path><path d="M291 163l2 1 6 4c-1 0-1 1-1 1-3-1-5-2-7-2h-1c-1 0-1-1-1-1h-1v-2h1c0 1 0 1 1 1 0-1 1-1 1-2z" class="k"></path><path d="M364 188l1 1c0 1-2 2-2 3 1 1 1 1 2 1v2h0 1 1v1l-1 1h1v1c1 0 1 0 2-1 1 1 0 1 1 2h0v1h1c0 1 1 2 2 2l-1 1c0 1 0 2 1 2 0 0 1 0 1 1v3h1l1 1h0 4v1l1 1c-1 0-1 1-2 1l-2 1c-2 0-3 1-4 2-2 1-3 3-5 4l-1 1-1 2c-1 0-2 1-3 2l-2 2c-1 2-2 3-3 4l-2 1h0-3c-1 1-1 1-1 2l-1 1 1 1-1 1-1 2-1-1v4 1 1l-1 1v4l-1-1c-1-1-1-2-2-3h-1l-1-2c-1-1-2-2-2-3h-4s0-1-1-1v-2s0-1-1-2c2 0 3 1 5 0v-2l1-2v-1c-1 0-2 0-3-1 0-1 0-1-1-1v-1h0v-2c0-1 0-2 1-3 1 0 1-1 1-2l-1-1h1c1 0 1 0 2 1l1-1-1-3v-1c1-1 1-2 1-3l1-1h2 1 2v-1l1-1v-1-2h1c1-1 1-3 1-4h0c0-1 0-2-1-3l1-1v-2h1l1-1c1 1 1 2 2 2 0 0 0-1 1-1h1l1-1s1-1 1-2h2v-2h1c0-1 1-2 2-3z" class="E"></path><path d="M360 197h1c1-1 1-2 1-3h1s1 1 1 2c0 0-1 2-1 3v1l-2-2-1-1zm-8-1l1-1c1 1 1 2 2 2 0 0 0-1 1-1h0v2l-2 3h-1c0-1-1-1-1-2v-1h-1v-2h1z" class="d"></path><path d="M360 197l1 1 2 2-1 2h1v3h-1l-1-1-1 1-1-2h-1-1c1-1 1-1 1-2v-1c0-1 1-2 2-3z" class="y"></path><path d="M361 204v-1l2-1v3h-1l-1-1z" class="B"></path><path d="M361 198l2 2-1 2h-1-1v1h-1c1-2 1-3 2-5z" class="p"></path><path d="M349 209c0-1 1-2 1-2 1-1 0-2 2-3h0l1 2h1v-4l3-1v2 2c-1 1 0 2-1 4h0v2c-1-1-1-1-1-2h0l-1 1c-1 0-2 1-3 1h-1c0-1 0-1-1-1l-1 1v-1l1-1z" class="N"></path><path d="M357 203h0 1 1l1 2 1-1 1 1c0 1-1 3 0 4h0v1l-1 1-2 4-1 1c-1 0-1 0-1-1l1-1h-2 0l1-1v-1l-1-1v-2h0c1-2 0-3 1-4v-2z" class="H"></path><path d="M357 210c1-1 1-2 2-2h1c0 1 0 2-1 2l-1 1h0l-1-1z" class="a"></path><path d="M357 203h0 1 1l1 2h0l-2 2-1-2v-2z" class="d"></path><path d="M356 209l1 1 1 1 1 2-1 1h0-2 0l1-1v-1l-1-1v-2z" class="I"></path><path d="M354 210l1-1h0c0 1 0 1 1 2l1 1v1l-1 1h0 2l-1 1c0 1 0 1 1 1l-1 3c-1 2-2 3-3 4h-1v-1l-2 1v-1-3-1c-1-1-1-2-1-3l-1-1h-1v-2l-2-1h2l1-1c1 0 1 0 1 1h1c1 0 2-1 3-1z" class="G"></path><path d="M354 210l1-1h0c0 1 0 1 1 2l1 1v1l-1 1-2-4z" class="F"></path><path d="M348 211l1-1c1 0 1 0 1 1h1c-1 3 0 5 0 7-1-1-1-2-1-3l-1-1h-1v-2l-2-1h2z" class="I"></path><path d="M356 214h2l-1 1c0 1 0 1 1 1l-1 3c-1 2-2 3-3 4h-1v-1l-2 1v-1-3c0 1 0 2 1 2 0 1 0 1 1 0h1 0c1-1 2-2 2-3v-1-3z" class="N"></path><path d="M343 211h2 1l2 1v2h1l1 1c0 1 0 2 1 3v1 3h-1c0 1-1 1-1 1l-2-1h0c-1 1-2 1-2 1-1 0-2-3-3-4l-1-3v-1c1-1 1-2 1-3l1-1z" class="J"></path><path d="M350 215c0 1 0 2 1 3v1 3h-1c0 1-1 1-1 1l-2-1h0s2 0 2-1c0-2-1-4 1-6z" class="R"></path><path d="M347 222l2 1s1 0 1-1h1v1l2-1v1h1l2 2 1 1s0 1-1 1l1 1h1c0 1-1 1-1 2 1 0 1 0 1 1l-2 1h0-3c-1 1-1 1-1 2l-1 1c0-1-1-2-2-3v-1h-1c0-1 1-1 0-3l-1-1c0-2 0-2-1-3l-1-1s1 0 2-1h0z" class="s"></path><path d="M351 225c1 0 2 0 3 1v1 1c-1 0-1-1-1-1l-1 1c0-1-1-1-2-2l1-1z" class="x"></path><path d="M348 226h1 1c1 1 2 1 2 2s-1 2-1 3l-1-1c0-1 1-2 0-3h-1l-1-1z" class="O"></path><path d="M345 223s1 0 2-1h0v2 1 1l1 1v-1l1 1h1c1 1 0 2 0 3l-1 2v-1h-1c0-1 1-1 0-3l-1-1c0-2 0-2-1-3l-1-1z" class="m"></path><path d="M347 222l2 1s1 0 1-1h1v1l2 1c-1 1-1 1-2 1l-1 1h-1-1v1l-1-1v-1-1-2z" class="AF"></path><path d="M342 219c1 1 2 4 3 4l1 1c1 1 1 1 1 3l1 1c1 2 0 2 0 3h1l-2 1v3h0c-1 0-2-1-2-1v-1l-1 1h0l-2-2h0l-1-1v-1c-1 0-2 0-3-1 0-1 0-1-1-1v-1h0v-2c0-1 0-2 1-3 1 0 1-1 1-2l-1-1h1c1 0 1 0 2 1l1-1z" class="AJ"></path><path d="M339 220v1 1c1 0 1 0 1-1 1 1 1 1 1 2v1h0v2c-1-1-1-1-2-1v2 1l-1 1c0-1 0-1-1-1v-1h0v-2c0-1 0-2 1-3 1 0 1-1 1-2z" class="E"></path><path d="M342 219c1 1 2 4 3 4l1 1c1 1 1 1 1 3l1 1h-1l-1-1-1 1v3h-1v-1-2h-2 0l2-2v-1c-1-1-2-1-3-1h0v-1c0-1 0-1-1-2 0 1 0 1-1 1v-1-1l-1-1h1c1 0 1 0 2 1l1-1z" class="Z"></path><path d="M346 224c1 1 1 1 1 3-1-1-2-1-2-1v-1l1-1z" class="R"></path><path d="M342 228h2v2 1h1v-3l1-1 1 1h1c1 2 0 2 0 3h1l-2 1v3h0c-1 0-2-1-2-1v-1l-1 1h0l-2-2h0l-1-1v-1-1l-1-1h1 1z" class="AF"></path><path d="M341 229c1 1 1 1 2 1 0 1 0 1-1 2h0l-1-1v-1-1z" class="y"></path><path d="M347 228h1c1 2 0 2 0 3-1 0-1 0-2-1 1 0 1 0 1-1v-1z" class="R"></path><path d="M361 211l2 2h0l-1 2c1 1 1 2 1 3l2 1 2 2-1 2c-1 0-2 1-3 2l-2 2c-1 2-2 3-3 4 0-1 0-1-1-1 0-1 1-1 1-2h-1l-1-1c1 0 1-1 1-1l-1-1-2-2c1-1 2-2 3-4l1-3 1-1 2-4z" class="B"></path><path d="M363 218l2 1 2 2-1 2-3-4v-1z" class="s"></path><path d="M359 223v-1h1 0v2 1h1c1-1 1-1 2 0l-2 2h-2c0-1 1-2 0-4h0z" class="D"></path><path d="M357 219l1 1c-1 1-1 2-1 3v2l2-2h0 0c1 2 0 3 0 4h2c-1 2-2 3-3 4 0-1 0-1-1-1 0-1 1-1 1-2h-1l-1-1c1 0 1-1 1-1l-1-1-2-2c1-1 2-2 3-4z" class="M"></path><defs><linearGradient id="h" x1="360.161" y1="220.632" x2="360.054" y2="215.937" xlink:href="#B"><stop offset="0" stop-color="#645c5a"></stop><stop offset="1" stop-color="#796f62"></stop></linearGradient></defs><path fill="url(#h)" d="M361 211l2 2h0l-1 2c1 1 1 2 1 3v1h-1c-2 1-3 3-5 4 0-1 0-2 1-3l-1-1 1-3 1-1 2-4z"></path><path d="M358 216l1-1 1 1c0 2-1 3-2 4l-1-1 1-3z" class="y"></path><path d="M361 211l2 2h0l-1 2h0c-1 0-1 1-2 1l-1-1 2-4z" class="AM"></path><path d="M341 231l1 1h0l2 2h0l1-1v1s1 1 2 1h0v-3l2-1v1c1 1 2 2 2 3l1 1-1 1-1 2-1-1v4 1 1l-1 1v4l-1-1c-1-1-1-2-2-3h-1l-1-2c-1-1-2-2-2-3h-4s0-1-1-1v-2s0-1-1-2c2 0 3 1 5 0v-2l1-2z" class="O"></path><path d="M343 243l1-1c1-1 1-1 2-1 0 1 0 2-1 4h-1l-1-2z" class="R"></path><path d="M346 241l1 1c1 1 1 1 1 3v4l-1-1c-1-1-1-2-2-3 1-2 1-3 1-4z" class="B"></path><path d="M342 232h0l2 2h0s1 1 1 2v3c-1 0-1 1-2 1s-1-2-2-3c0-2-1-3 1-5z" class="I"></path><path d="M341 231l1 1c-2 2-1 3-1 5l-1 1 1 2h-4s0-1-1-1v-2s0-1-1-2c2 0 3 1 5 0v-2l1-2z" class="R"></path><path d="M340 235h0v2 1c-2 0-2 0-3-1h-1s0-1-1-2c2 0 3 1 5 0z" class="F"></path><path d="M349 231v1c1 1 2 2 2 3l1 1-1 1-1 2-1-1v4h0c-2-1-1-2-2-4l-2 1v-3c0-1-1-2-1-2l1-1v1s1 1 2 1h0v-3l2-1z" class="k"></path><path d="M350 235h1l1 1-1 1-1-1h-2l2-1z" class="r"></path><path d="M349 231v1c1 1 2 2 2 3h-1c-1 0-1 0-3 1v-1h0v-3l2-1z" class="p"></path><path d="M345 236c2 1 2 1 4 1l1-1 1 1-1 2-1-1v4h0c-2-1-1-2-2-4l-2 1v-3z" class="Ad"></path><path d="M366 197h1v1c1 0 1 0 2-1 1 1 0 1 1 2h0v1h1c0 1 1 2 2 2l-1 1c0 1 0 2 1 2 0 0 1 0 1 1v3h1l1 1h0 4v1l1 1c-1 0-1 1-2 1l-2 1c-2 0-3 1-4 2-2 1-3 3-5 4l-1 1-2-2-2-1c0-1 0-2-1-3l1-2h0l-2-2 1-1v-1h0c-1-1 0-3 0-4h1v-3h-1l1-1c1-1 1-1 2-1 0-1 1-2 1-3z" class="y"></path><path d="M365 218c2 0 2 1 3 2l-1 1-2-2v-1z" class="E"></path><path d="M373 216h-1s-1-1-1-2c1-1 1-2 2-3h1c1 1 3 1 3 3-2 0-3 1-4 2z" class="V"></path><path d="M370 208c1 0 2 0 3-1 0 1 0 1-1 2h0c-1 1-1 2-1 2 0 2-1 3-2 5 0 0-1 1-2 1 0 0-1 0-2-1v2 1l-2-1c0-1 0-2-1-3l1-2h0l-2-2 1-1v-1h1c1 0 2 0 2 1h2 1l1 1s0-1 1-2v-1z" class="d"></path><path d="M363 209c1 0 2 0 2 1l-2 2v1l-2-2 1-1v-1h1z" class="y"></path><path d="M362 210c1 1 1 1 1 2v1l-2-2 1-1z" class="AK"></path><path d="M363 213l2 2v1 2 1l-2-1c0-1 0-2-1-3l1-2z" class="Ad"></path><path d="M367 210h1l1 1c0 2-1 3-2 4h-1c-1-1-1-1-1-2s1-2 2-3z" class="V"></path><path d="M366 197h1v1c1 0 1 0 2-1 1 1 0 1 1 2h0v1h1c0 1 1 2 2 2l-1 1v1s-1 1-2 1v3 1c-1 1-1 2-1 2l-1-1h-1-2c0-1-1-1-2-1h-1 0c-1-1 0-3 0-4h1v-3h-1l1-1c1-1 1-1 2-1 0-1 1-2 1-3z" class="W"></path><path d="M365 200c0 2-2 3-2 5v-3h-1l1-1c1-1 1-1 2-1z" class="F"></path><path d="M370 199v1c0 1 0 2-1 3-1 0-2 2-3 3 0-2 1-3 1-5 1 0 2-1 2-1l1-1h0z" class="E"></path><path d="M371 200c0 1 1 2 2 2l-1 1v1s-1 1-2 1v3 1c-1 1-1 2-1 2l-1-1h-1-2c0-1-1-1-2-1l3-3c1-1 2-3 3-3 1-1 1-2 1-3h1z" class="AF"></path><path d="M371 200c0 1 1 2 2 2l-1 1v1s-1 1-2 1v3 1c-1 0-1 0-1-1h-1-1c1-1 1-2 2-3v-2h0c1-1 1-2 1-3h1z" class="r"></path><defs><linearGradient id="i" x1="500.261" y1="90.084" x2="470.161" y2="114.167" xlink:href="#B"><stop offset="0" stop-color="#0a090c"></stop><stop offset="1" stop-color="#434040"></stop></linearGradient></defs><path fill="url(#i)" d="M452 61h4l1 1h2l8 6 12 6 4 4c4 3 8 5 11 8 1 1 5 4 6 4l7 8c3 5 3 10 4 16-1 1-2 3-4 4l1 1c-1 1-4 1-5 3v1c1 1 1 3 1 5 1 7 3 14 1 21h0 0c-2 1-2 0-3 1 0 0-1 1-2 1v-2c0-3 0-7-1-10-1-2-2-4-4-5 0-1 0-1-1-1l3-2v-7c-1-9-6-23-14-29-1-1-2-2-4-2-1 0-2 1-4 0 2-1 3-2 4-4-2-5-9-11-14-14h0c-1-1 1-3 0-4s-4-3-5-3c-1-1-2-1-3-2h-1 0l-1-1c-1-1-2-3-3-4z"></path><path d="M479 93l2-3c1 1 2 2 2 3v2c-1-1-2-2-4-2z" class="C"></path><path d="M452 61h4l1 1h-1c-1 1 0 3 0 4h0l-1-1c-1-1-2-3-3-4z" class="c"></path><path d="M503 123v1-1l-1 2c0 1-1 2-1 3h-1 0c-1-2-1-4-1-5 1-1 3-2 4-3 2-1 3-2 4-2l1 1c-1 1-4 1-5 3v1z" class="H"></path><defs><linearGradient id="j" x1="197.59" y1="90.068" x2="228.541" y2="113.188" xlink:href="#B"><stop offset="0" stop-color="#0a080b"></stop><stop offset="1" stop-color="#484645"></stop></linearGradient></defs><path fill="url(#j)" d="M243 61c2 1 2 1 4 0l-2 3v1h1c-2 2-3 2-3 5h-1-1c-2 1-4 2-5 4v1l-1 4h-1 0c0-1 0-2-1-3h-1c-3 2-11 8-12 12v1 2l4 3c-1-1-4-1-5-1s-2 1-2 1c-8 5-13 17-15 26-1 3-2 8 0 11 0 1 1 1 2 2h0 0c-3 1-3 3-4 5-2 5-1 9-1 13l2 8c0 1-1 1-1 2l-1-2h-3l-1-3s-1-2-1-3c-2-8-1-17 0-25 1-2 1-4 2-6h0l-5-3 1-1c-2-1-3-2-4-5 0-2 0-4 1-7-1-1-1-2-1-4v-1l3-3c4-4 7-7 11-10l-1-1v-1h0 0l7-4 4-2 1-1h0l6-4h-1c0-1 1-1 1-2h-2c-2 1-3 1-5 1h-1c1-1 2-1 2-2 3-1 6-2 9-4l4-1v-1h-2l1-1s1 0 1-1h0 0c2-1 5 0 8 0 2-1 3-1 5-1l1-1 3-1z"></path><path d="M220 89v2c0 1 0 1-1 1v-2l1-1z" class="I"></path><path d="M243 61c2 1 2 1 4 0l-2 3-1 1h0l1-1-2-2h-2-1l3-1z" class="H"></path><path d="M188 101l3-3c-1 3-2 5-2 8-1-1-1-2-1-4v-1z" class="G"></path><path d="M200 123h0c-1 2-1 5-3 6l-1-1v-1l1-1h-1v-1c0-1 1-1 1-1 1-1 1-1 2 0l1-1z" class="D"></path><path d="M212 80l1-1c-2 4-7 6-11 9l-1-1v-1h0 0l7-4 4-2z" class="c"></path><path d="M194 153c0-1 0-2 1-3l2 1c1 0 1 1 2 2h0 0v-1-1l2 8c0 1-1 1-1 2l-1-2h-3l-1-3s-1-2-1-3z" class="H"></path><path d="M195 156h1 0v-1c2 1 3 2 3 3v1h-3l-1-3z" class="O"></path><path d="M192 118c1 1 2 2 4 2 3-7 6-16 11-23h0v1 1c0 1-1 2-2 3-1 3-2 5-3 8-1 4 0 7-1 11l-1 1h0v-3-3s1-1 1-2v-3c0-1 1-1 1-2-2 1-2 3-3 5v1c-1 1-1 1-1 2h1c0 1-1 3-2 4-2 3-1 5-2 8h0l-1-1c1-2 1-4 2-6h0l-5-3 1-1z" class="F"></path><defs><linearGradient id="k" x1="223.194" y1="73.338" x2="223.431" y2="63.991" xlink:href="#B"><stop offset="0" stop-color="#3c393b"></stop><stop offset="1" stop-color="#5e534b"></stop></linearGradient></defs><path fill="url(#k)" d="M234 64c2-1 3-1 5-1-4 3-9 5-14 8l-5 3-1 1h-1c0-1 1-1 1-2h-2c-2 1-3 1-5 1h-1c1-1 2-1 2-2 3-1 6-2 9-4l4-1v-1h-2l1-1s1 0 1-1h0 0c2-1 5 0 8 0z"></path><path d="M226 64c2-1 5 0 8 0-2 1-5 3-6 3l-1-1-1 1v-1h-2l1-1s1 0 1-1h0 0z" class="E"></path><path d="M226 64h3l1 1-3 1-1 1v-1h-2l1-1s1 0 1-1h0z" class="N"></path><path d="M505 502c0-1 0 0 1-1 1 0 2-1 2-2 2 1 6 2 7 4-1 2 0 4 0 6l1 2c1 3 0 6-1 9-2 5-5 6-10 8l-3 1s-1 1-2 1c2 0 4 1 6 1h-4l1 1h1c0 1-1 1-1 1h-1-2l-1-1h-2 0c-3 0-6 1-8 3-3 3-5 6-5 11v1l-1 1-4-2v1l-9 16c-1 2-2 3-4 5-1 1-2 3-3 5h0v-3h-1v3c-1-1-1-3-1-4v1h-1v-4h-1 0v-1h0v-1c0-1 1-2 1-3h-1l1-2c0-1 1-2 1-2l3-5c4-6 7-12 10-19 2-2 3-7 4-10 1-2 1-3 1-4l1-3h3l1-2v1c1 0 1 0 1-1 2-3 6-7 9-8h0c1-1 3-2 4-2 1-1 2-1 3-1l4-1z" class="Y"></path><path d="M482 539h1l-4 6h-1c0-3 3-4 4-6z" class="g"></path><path d="M488 535c-2 1-3 3-5 5 2-4 4-7 5-11v2l1 1c0 1-1 2-1 3z" class="Q"></path><path d="M480 536l2-2h1c0 2 0 3-1 4h-1l1 1c-1 2-4 3-4 6v-1c-1 1-1 1-2 1l4-9z" class="X"></path><path d="M494 506h0c1-1 3-2 4-2 1-1 2-1 3-1l1 1h2v1h0c-1 1-1 1-1 2h0-1v1h-1l-2 1-1-1h0l-2 1c0 1 0 2-1 3l-3 6c0 1 0 2-1 3 0-1 0-2 1-4h0v-1l-1 1h0c-3 2-3 8-4 10 0 2-1 4-2 6 0 1-1 2-1 3l-1-1c0-2 2-4 2-6l4-11v-2c0-2 1-3 2-4l3-6z" class="v"></path><path d="M494 506h0c1-1 3-2 4-2 1-1 2-1 3-1l1 1c-2 1-4 1-5 3-3 2-3 5-5 7-1 1-2 2-3 4v-2c0-2 1-3 2-4l3-6z" class="o"></path><path d="M491 512l1-1h1c-1 1-1 2-1 3-1 1-2 2-3 4v-2c0-2 1-3 2-4z" class="K"></path><path d="M502 508c2 1 5 3 6 6h-1c-2 0-3 0-4-1l-1-1c2 0 2 1 4 1h0c-1-2-3-3-5-3s-3 1-4 3c0 1-1 4-2 5s-1 3-1 4v2c1 2 4 3 5 4h-3l6 1s-1 1-2 1c2 0 4 1 6 1h-4l1 1h1c0 1-1 1-1 1h-1-2l-1-1h-2 0c-3 0-6 1-8 3h-1c0-1 1-2 1-3l-1-1v-2l3-8c1-1 1-2 1-3l3-6c1-1 1-2 1-3l2-1h0l1 1 2-1h1z" class="T"></path><path d="M494 522v2c1 2 4 3 5 4h-3c-1 1-3 0-4 0 0-2 1-4 2-6z" class="V"></path><path d="M489 532c3-2 4-1 7-2 1-1 3 0 4 0 2 0 4 1 6 1h-4l1 1h1c0 1-1 1-1 1h-1-2l-1-1h-2 0c-3 0-6 1-8 3h-1c0-1 1-2 1-3z" class="X"></path><path d="M494 506l-3 6c-1 1-2 2-2 4l-6 9c-1 3-2 8-3 11l-4 9c-3 4-6 9-8 13 0 1-1 2-1 3-1 2-4 6-5 8l1 1h-1v3c-1-1-1-3-1-4v1h-1v-4h-1 0v-1h0v-1c0-1 1-2 1-3h-1l1-2c0-1 1-2 1-2l3-5c4-6 7-12 10-19 2-2 3-7 4-10 1-2 1-3 1-4l1-3h3l1-2v1c1 0 1 0 1-1 2-3 6-7 9-8z" class="Q"></path><path d="M480 516h3l-2 6c0-1 0-1-1-1l-2 2c1-2 1-3 1-4l1-3z" class="Ae"></path><defs><linearGradient id="l" x1="465.175" y1="539.623" x2="474.255" y2="542.193" xlink:href="#B"><stop offset="0" stop-color="#b67465"></stop><stop offset="1" stop-color="#d5a794"></stop></linearGradient></defs><path fill="url(#l)" d="M478 523l2-2c1 0 1 0 1 1-2 6-5 13-8 19-3 7-8 14-13 20h-1l1-2c0-1 1-2 1-2l3-5c4-6 7-12 10-19 2-2 3-7 4-10z"></path><path d="M505 502c0-1 0 0 1-1 1 0 2-1 2-2 2 1 6 2 7 4-1 2 0 4 0 6l1 2c1 3 0 6-1 9-2 5-5 6-10 8l-3 1-6-1h3c-1-1-4-2-5-4v-2c0-1 0-3 1-4s2-4 2-5c1-2 2-3 4-3s4 1 5 3h0c-2 0-2-1-4-1l1 1c1 1 2 1 4 1h1c-1-3-4-5-6-6v-1h1 0c0-1 0-1 1-2h0v-1h-2l-1-1 4-1z" class="V"></path><path d="M505 502c0-1 0 0 1-1 1 0 2-1 2-2 2 1 6 2 7 4-1 2 0 4 0 6l1 2c1 3 0 6-1 9-2 5-5 6-10 8l-3 1-6-1h3c-1-1-4-2-5-4v-2c0-1 0-3 1-4l1 1v1h-1c0 2 1 3 2 4 1 2 3 2 5 2 3 0 6-1 8-4 3-3 3-5 2-9-1-1-2-3-3-4-2-1-4-2-7-2h1 0c0-1 0-1 1-2h0v-1h-2l-1-1 4-1z" class="S"></path><path d="M514 506l1 3 1 2c1 3 0 6-1 9-2 5-5 6-10 8l-3 1-6-1h3c1-1 1-1 2-1 3 0 7-1 10-3 2-2 4-5 4-9 0-3-1-5-3-8l2-1z" class="AH"></path><path d="M505 502c0-1 0 0 1-1 1 0 2-1 2-2 2 1 6 2 7 4-1 2 0 4 0 6l-1-3-2 1c-2-2-5-2-7-4v-1h0z" class="X"></path><path d="M505 502c2 1 4 1 6 2h0c1 1 2 2 3 2l-2 1c-2-2-5-2-7-4v-1z" class="u"></path><path d="M529 454s1 0 1 1l-1 3h1l-2 3v1c-1 1-2 2-2 3h0c1-1 2-1 3-2v1c-1 1-1 2-1 3v2c-1 0-1 0-1-1h-1c1 2-1 8-2 10l-3 3h1v1s1 0 1 1h0c0 1-1 2-2 3-1 0-2 1-3 2 1 0 1-1 2 0 0 1 0 1-1 2l-1 1c0 1 1 3 2 5h-1c-1-1-2-1-3-1 0 1 2 5 3 5l1 2h-3 0l1 2-1-1-1 2-1-2c-1-2-5-3-7-4 0 1-1 2-2 2-1 1-1 0-1 1l-4 1c-1 0-2 0-3 1-1 0-3 1-4 2h0c-3 1-7 5-9 8 0 1 0 1-1 1v-1l-1 2h-3l-1 3v-2h0v-1h-1l2-2v1c0-1 1-1 1-2s0-1-1-1c1-1 1-1 1-3h0c0 1 0 1 1 2l2-2c0-1 0-1 1-2l4-4 4-3c-1 0-1 0-2-1 0 1-1 1-1 1h-1-1v-1c-1-1-1-2-1-4v-8c-1-2-1-5-1-7l-1-25h1v1c3 3 7 6 8 9h5c2 2 6 4 9 4h0c3 1 6 1 8 0h2c1-1 4-3 5-4h1v-1l2-2-1-1c0-1 1-2 1-3 1 0 1 0 2-1 0-1 0-2 1-3z" class="W"></path><path d="M492 484l1 1c-1 2-1 4-2 6v-1h-1c1-2 1-4 2-6z" class="L"></path><path d="M490 490h1v1c0 2-1 4 0 6h0c-1 1-1 2-2 3h-1v-1c1-3 1-6 2-9zm2-6c1-2 1-6 3-8 1-1 2 0 3 1-1 1-2 5-2 5-1 0-1 0-2 1h0c0 1-1 2-1 2l-1-1z" class="h"></path><path d="M498 477v1l-1 2c2-1 2-2 4-2 1 0 3 2 4 4h0 0c0 1-1 3-1 3 0 1-2 1-2 2v1l-6 6-3 2s-1 0-1 1h-1 0c-1-2 0-4 0-6 1-2 1-4 2-6 0 0 1-1 1-2h0c1-1 1-1 2-1 0 0 1-4 2-5z" class="t"></path><path d="M498 481l2-2c1 0 3 1 4 3h0c-1 0-1 1-2 1l-1-1c-1-1-2-1-3-1z" class="e"></path><path d="M498 481c1 0 2 0 3 1l1 1c1 0 1-1 2-1h1c0 1-1 3-1 3 0 1-2 1-2 2-1-2-1-3-1-4v-1c-2 0-2 2-2 3h-2-1l2-4z" class="K"></path><path d="M499 485c0-1 0-3 2-3v1c0 1 0 2 1 4v1l-6 6-3 2s-1 0-1 1v-2l2-4 1-3c0-1 1-2 1-3h1 2z" class="g"></path><path d="M499 485c-1 1-2 2-4 3 0-1 1-2 1-3h1 2z" class="X"></path><path d="M494 491h1 0 2v1c-1 0-1 1-1 2l-3 2s-1 0-1 1v-2l2-4z" class="f"></path><path d="M485 455h1v1c3 3 7 6 8 9 4 4 8 8 13 12 1 2 3 4 5 5l-3 3v1c-2 2-4 3-6 5 0 1 0 1 1 2l-5 3-1-1c-1 1-2 1-4 1h-1l3-2 6-6v-1c0-1 2-1 2-2 0 0 1-2 1-3h0c0 1 0 1 1 1v-2-1c-1-1-1-1-2-1v-2l-12-13c-2-2-4-4-5-6 1 9 1 19 0 29-1-2-1-5-1-7l-1-25z" class="u"></path><path d="M504 477l6 6-8 5v-1c0-1 2-1 2-2 0 0 1-2 1-3h0c0 1 0 1 1 1v-2-1c-1-1-1-1-2-1v-2z" class="AA"></path><path d="M494 496l15-11v1c-2 2-4 3-6 5 0 1 0 1 1 2l-5 3-1-1c-1 1-2 1-4 1z" class="S"></path><path d="M503 491c0 1 0 1 1 2l-5 3-1-1c2-1 3-3 5-4z" class="v"></path><path d="M529 454s1 0 1 1l-1 3h1l-2 3v1c-1 1-2 2-2 3h0c1-1 2-1 3-2v1c-1 1-1 2-1 3v2c-1 0-1 0-1-1h-1c1 2-1 8-2 10l-3 3h1v1l-18 11c-1-1-1-1-1-2 2-2 4-3 6-5v-1l3-3c-2-1-4-3-5-5-5-4-9-8-13-12h5c2 2 6 4 9 4h0c3 1 6 1 8 0h2c1-1 4-3 5-4h1v-1l2-2-1-1c0-1 1-2 1-3 1 0 1 0 2-1 0-1 0-2 1-3z" class="AY"></path><path d="M529 454s1 0 1 1l-1 3-1 1c0 1-1 1-1 2l-1 1-1-1c0-1 1-2 1-3 1 0 1 0 2-1 0-1 0-2 1-3z" class="K"></path><path d="M506 473c-2-1-7-5-8-7 3 2 7 4 10 5l1 1c-1 0-1 0-2-1h-1l2 2h1 0-3z" class="G"></path><path d="M526 465h0c1-1 2-1 3-2v1c-1 1-1 2-1 3v2c-1 0-1 0-1-1h-1c1 2-1 8-2 10l-3 3c-1 0-2 1-3 1-1-1-2-2-2-4-1 1-1 1-1 2h0-1l-2-1-3-3-3-3h3 0-1l-2-2h1c1 1 1 1 2 1l-1-1h7c2-1 3-1 4-2h1l1-1h1l1-1c1 0 2-1 3-2z" class="AA"></path><path d="M526 468c1 2-1 8-2 10l-3 3c-1 0-2 1-3 1-1-1-2-2-2-4h0c3-3 7-6 10-10z" class="V"></path><path d="M522 482s1 0 1 1h0c0 1-1 2-2 3-1 0-2 1-3 2 1 0 1-1 2 0 0 1 0 1-1 2l-1 1c0 1 1 3 2 5h-1c-1-1-2-1-3-1 0 1 2 5 3 5l1 2h-3 0l1 2-1-1-1 2-1-2c-1-2-5-3-7-4 0 1-1 2-2 2-1 1-1 0-1 1l-4 1c-1 0-2 0-3 1-1 0-3 1-4 2h0c-3 1-7 5-9 8 0 1 0 1-1 1v-1l-1 2h-3l-1 3v-2h0v-1h-1l2-2v1c0-1 1-1 1-2s0-1-1-1c1-1 1-1 1-3h0c0 1 0 1 1 2l2-2c0-1 0-1 1-2l4-4 4-3c-1 0-1 0-2-1 0 1-1 1-1 1h-1c1-1 1-2 2-3h1c0-1 1-1 1-1h1c2 0 3 0 4-1l1 1 5-3 18-11z" class="J"></path><path d="M480 516l1-1c1-1 1-2 3-2v1l-1 2h-3z" class="Aa"></path><path d="M516 495c0 1 2 5 3 5l1 2h-3 0l1 2-1-1-1 2-1-2c-1-2-5-3-7-4 1 0 2-1 3-1 1 2 2 2 4 3v-1l-2-1s-1-1-1-2c1 0 2-1 4-2z" class="n"></path><path d="M522 482s1 0 1 1h0c0 1-1 2-2 3-1 0-2 1-3 2-3 2-6 4-10 4l-24 17c0-1 0-1 1-2l4-4 4-3c-1 0-1 0-2-1 0 1-1 1-1 1h-1c1-1 1-2 2-3h1c0-1 1-1 1-1h1c2 0 3 0 4-1l1 1 5-3 18-11z" class="V"></path><path d="M494 496c2 0 3 0 4-1l1 1-6 4c-1 0-1 0-2-1 0 1-1 1-1 1h-1c1-1 1-2 2-3h1c0-1 1-1 1-1h1z" class="K"></path><path d="M508 492l15-9c0 1-1 2-2 3-1 0-2 1-3 2-3 2-6 4-10 4z" class="Y"></path><defs><linearGradient id="m" x1="473.745" y1="536.613" x2="451.439" y2="515.012" xlink:href="#B"><stop offset="0" stop-color="#421010"></stop><stop offset="1" stop-color="#1b1414"></stop></linearGradient></defs><path fill="url(#m)" d="M481 449c1 2 1 7 2 9 0 1 1 1 1 3h0v2h0 0c1-1 1-2 1-3l-1-1h1v-3-1l1 25c0 2 0 5 1 7v8c0 2 0 3 1 4v1h1 1s1 0 1-1c1 1 1 1 2 1l-4 3-4 4c-1 1-1 1-1 2l-2 2c-1-1-1-1-1-2h0c0 2 0 2-1 3 1 0 1 0 1 1s-1 1-1 2v-1l-2 2h1v1h0v2c0 1 0 2-1 4-1 3-2 8-4 10-3 7-6 13-10 19l-3 5s-1 1-1 2l-1 2h1c0 1-1 2-1 3v1h0v1h0 1v4c0 1 1 3 0 4 0 0-1 1-1 2h0c-1 2-1 3-1 4l-1 1c0 1 0 1 1 2v2h-1l2 1-1 1c-2-2-4-3-5-3-2 0-4 0-5 1-1 0-2 0-3 1-1 0-3 2-3 2l-1-1-2 1-4 2h0v-1-1h0l-1-1 8-6c1-2 3-4 5-5-2-1-3-3-4-4l-1-2-3-4-1-1-1 1c-1-1-1-1-2-1l6-6 10-11c12-15 22-34 27-52l2-14h2v-2c0-1 1-1 1-1v-2l-1-6-1-8v-1-6-4c-1-1-1-1-1-2l1-1z"></path><path d="M446 565c1-2 4-6 7-8-2 4-4 6-7 8z" class="Q"></path><path d="M474 511l2 1c0 1-1 3-1 4l-1-2v1c-1 2-2 5-4 6l4-10z" class="K"></path><path d="M478 502c0 4-1 6-2 10l-2-1 4-9z" class="Y"></path><path d="M483 486h0c1 2 0 4 1 6 1 0 1-1 1-2v8h0c-1 0-2 1-2 1v-13z" class="Aa"></path><path d="M483 499s1-1 2-1c-1 2-2 8 0 9-1 1-1 1-1 2l-2 2 1-12z" class="Ae"></path><path d="M480 482h2l-3 16v-2h-1l2-14z" class="Aj"></path><path d="M453 557l7-8c2-2 3-3 4-6 1 0 1-1 2-2h1v2c-5 9-10 16-17 24-2 1-3 3-5 4 0-1-1-1-1-2s1-2 2-4c3-2 5-4 7-8z" class="K"></path><path d="M478 496h1v2l-1 4-4 9-4 10c-7 13-15 26-25 37l-6 6v1 1h0l-1-1-1 1c-1-1-1-1-2-1l6-6 10-11c12-15 22-34 27-52z" class="Aa"></path><path d="M481 449c1 2 1 7 2 9 0 1 1 1 1 3h0v2h0 0c1-1 1-2 1-3l-1-1h1v-3-1l1 25c0 2 0 5 1 7v8c0 2 0 3 1 4v1h1 1s1 0 1-1c1 1 1 1 2 1l-4 3-4 4c-2-1-1-7 0-9h0v-8c0 1 0 2-1 2-1-2 0-4-1-6h0v-7-2l-1-6-1-8v-1-6-4c-1-1-1-1-1-2l1-1z" class="h"></path><path d="M487 495c0 2 0 3 1 4v1l-1 2h-1c0-1 0-6 1-7z" class="AC"></path><path d="M484 468v7 4h0l-1-2-1-6c1-2 1-2 2-3z" class="u"></path><path d="M488 500h1 1s1 0 1-1c1 1 1 1 2 1l-4 3h-2v-1l1-2z" class="AY"></path><path d="M484 475c1 5 1 10 1 15 0 1 0 2-1 2-1-2 0-4-1-6h0v-7-2l1 2h0v-4z" class="k"></path><path d="M481 449c1 2 1 7 2 9 0 3 1 7 1 10-1 1-1 1-2 3l-1-8v-1-6-4c-1-1-1-1-1-2l1-1z" class="AH"></path><path d="M467 543c3-3 3-8 6-10h1c-3 7-6 13-10 19l-3 5s-1 1-1 2l-1 2h1c0 1-1 2-1 3-2 2-3 4-4 7l-1-1 1-1-1-1v1l-5 6-2 1c-2-1-3-3-4-4l-1-2-3-4h0l6 6v-1c2-1 3-3 5-4 7-8 12-15 17-24z" class="t"></path><path d="M445 572l2 2 13-15-1 2h1c0 1-1 2-1 3-2 2-3 4-4 7l-1-1 1-1-1-1v1l-5 6-2 1c-2-1-3-3-4-4l-1-2-3-4h0l6 6z" class="AS"></path><path d="M459 564v1h0v1h0 1v4c0 1 1 3 0 4 0 0-1 1-1 2h0c-1 2-1 3-1 4l-1 1c0 1 0 1 1 2v2h-1l2 1-1 1c-2-2-4-3-5-3-2 0-4 0-5 1-1 0-2 0-3 1-1 0-3 2-3 2l-1-1-2 1-4 2h0v-1-1h0l-1-1 8-6c1-2 3-4 5-5l2-1 5-6v-1l1 1-1 1 1 1c1-3 2-5 4-7z" class="X"></path><path d="M455 580l1 1-1 1c1 1 2 1 2 2-2-1-3-1-5-2 1-1 2-1 3-2zm-7 2c0-2 3-4 5-6 0 2 0 3-2 4-1 1-2 2-3 2z" class="q"></path><path d="M459 576c-1 2-1 3-1 4l-1 1c0 1 0 1 1 2v2h-1v-1c0-1-1-1-2-2l1-1-1-1 4-4z" class="v"></path><path d="M459 564v1h0v1h0c0 3 1 7-2 10l-3 3 1-8h0c1-3 2-5 4-7zm-5 5c0 2-1 5-1 7-2 2-5 4-5 6l-9 6-4 2h0v-1-1h0l-1-1 8-6c1-2 3-4 5-5l2-1 5-6z" class="L"></path><path d="M447 576l2-1c-3 4-9 10-14 13l-1-1 8-6c1-2 3-4 5-5z" class="AE"></path><path d="M498 377c2 4 4 6 8 8v1h-1l1 2c1 0 1 0 1-1l2 1v1h0 1c2 0 4 2 6 3 6 4 11 10 15 16 2 4 4 8 4 12 2 5 3 10 4 15 0 5 0 11-1 16h-1c0-1 0-1-1-1l-1 1h0c-1 2-2 2-2 3-1 2-1 2-3 3v1h-1l1-3c0-1-1-1-1-1-1 1-1 2-1 3-1 1-1 1-2 1l1-5c1-2 1-4 0-7h-1c0-2-1-3-2-4v-2h-1c-1-1-2-1-4-1l-7-3v-2c0-1-1-2-1-4l-1-9h0v-2c0-1-1-2-2-3h-1-1v1l-2-1c-2-2-2-5-3-8l-2-7h-1c-1-2-1-3-2-4h-1 0c1-2 0-5 1-6h1v-1l-1-1c1-1 1-1 1-2v-1l1-1 2 1h0c1 0 3 1 4 1v-1h-1c-2-1-3-3-4-4s-1-2-2-3l1-2z" class="V"></path><path d="M516 403c2 3 3 7 6 10-1 0-2 0-3-1l-1-3-1-1v5l-1-1v-1h-1v-5c0-1 0-2 1-3z" class="g"></path><path d="M506 395c2 2 3 3 5 4 1-1 3-3 5-3h0c-1 1-2 2-4 3l4 4c-1 1-1 2-1 3-1-1-1-3-2-4-1 0-2-1-2-1h0c-2-1-5-3-6-5l1-1z" class="AG"></path><path d="M515 411h1v1l1 1c-1 2-2 5-4 6l-2 1h0l-1 1h0v-2c0-1-1-2-2-3h1 0c1 1 2 1 3 0 2-1 3-3 3-5z" class="l"></path><path d="M536 434v1l1-1 2 1c0 5 0 11-1 16h-1c0-1 0-1-1-1l-1 1c0-6 1-12 0-17h1z" class="AG"></path><path d="M532 419h1c1 1 2 1 2 1 2 5 3 10 4 15l-2-1-1 1v-1h-1v-1c-2 2-3 3-5 4h0l3-3c2-4 0-11-1-15z" class="z"></path><path d="M535 433s0-2 1-2c0 0 1 1 0 2v1h-1v-1z" class="l"></path><defs><linearGradient id="n" x1="505.525" y1="411.197" x2="513.005" y2="407.373" xlink:href="#B"><stop offset="0" stop-color="#511314"></stop><stop offset="1" stop-color="#642f22"></stop></linearGradient></defs><path fill="url(#n)" d="M511 401s1 1 2 1c1 1 1 3 2 4v5c0 2-1 4-3 5-1 1-2 1-3 0h0-1-1v-1-1l-2-2v-1c-1-1-1-3-1-5l2 1c1 1 1 2 3 3 0-1 1-1 1-2 2-2 1-5 1-7z"></path><path d="M509 416c1-1 2-1 3-2 3-3 1-8 1-11v-1c1 1 1 3 2 4v5c0 2-1 4-3 5-1 1-2 1-3 0h0zm-5-10c-2-3-4-9-3-12 0-1 0-3 1-4l3 6c1 2 4 4 6 5h0c0 2 1 5-1 7 0 1-1 1-1 2-2-1-2-2-3-3l-2-1z" class="V"></path><path d="M504 406c-2-3-4-9-3-12 0-1 0-3 1-4 0 4 1 7 2 11l2 5v1l-2-1z" class="e"></path><path d="M504 401l2 5c-1 0-1 0-2-1v-4z" class="Y"></path><path d="M497 387l4 1 1 1v1c-1 1-1 3-1 4-1 3 1 9 3 12 0 2 0 4 1 5v1l2 2v1 1h-1v1l-2-1c-2-2-2-5-3-8l-2-7h-1c-1-2-1-3-2-4h-1 0c1-2 0-5 1-6h1v-1l-1-1c1-1 1-1 1-2z" class="L"></path><path d="M497 387l4 1v1h0l-1 1h0c-1 1-1 1-1 2s-1 1-1 1l-1-2v-1l-1-1c1-1 1-1 1-2z" class="AB"></path><path d="M495 397c1-2 0-5 1-6h1l1 2 2 7c-1 0-1 0-1 1h-1c-1-2-1-3-2-4h-1 0z" class="l"></path><path d="M499 401c0-1 0-1 1-1 1 4 2 9 4 13 1 1 1 2 2 3v1l-2-1c-2-2-2-5-3-8l-2-7z" class="z"></path><path d="M512 434l6 2h0c4 0 7 2 10 4 3 1 4 3 5 5 0 1 0 2 1 3 0 1 0 2 1 3-1 2-2 2-2 3-1 2-1 2-3 3v1h-1l1-3c0-1-1-1-1-1-1 1-1 2-1 3-1 1-1 1-2 1l1-5c1-2 1-4 0-7h-1c0-2-1-3-2-4v-2h-1c-1-1-2-1-4-1l-7-3v-2z" class="j"></path><path d="M529 454c-1-1-1-2-1-3 1-2 0-4 1-5v1c0 1 1 1 1 2h0v6c0-1-1-1-1-1z" class="Y"></path><path d="M524 440c2 1 3 2 4 4h0c0 1 0 1 1 2-1 1 0 3-1 5 0 1 0 2 1 3-1 1-1 2-1 3-1 1-1 1-2 1l1-5c1-2 1-4 0-7h-1c0-2-1-3-2-4v-2z" class="S"></path><path d="M518 436c4 0 7 2 10 4 3 1 4 3 5 5 0 1 0 2 1 3 0 1 0 2 1 3-1 2-2 2-2 3-1 2-1 2-3 3l1-1v-1-3-3-1-1l-1-1c0-1 0-1-1-2h0c-1-2-2-3-3-4h0c-3-2-6-2-8-4h0z" class="AG"></path><path d="M528 440c3 1 4 3 5 5h-1l-1-1c-1 0-1-1-2-1v-1c0-1 0-1-1-2z" class="g"></path><path d="M531 452h1v-3c0-1 1-1 2-1 0 1 0 2 1 3-1 2-2 2-2 3-1 2-1 2-3 3l1-1v-1-3z" class="K"></path><defs><linearGradient id="o" x1="517.878" y1="403.957" x2="523.299" y2="399.549" xlink:href="#B"><stop offset="0" stop-color="#62160f"></stop><stop offset="1" stop-color="#8c281b"></stop></linearGradient></defs><path fill="url(#o)" d="M498 377c2 4 4 6 8 8v1h-1l1 2c1 0 1 0 1-1l2 1v1h0 1c2 0 4 2 6 3 6 4 11 10 15 16 2 4 4 8 4 12 0 0-1 0-2-1h-1c0-1 0-1-1-1h-1c0-1 1-2 1-3l-3-6c-3-5-7-9-12-13h0c-2 0-4 2-5 3-2-1-3-2-5-4l-1 1-3-6v-1l-1-1-4-1v-1l1-1 2 1h0c1 0 3 1 4 1v-1h-1c-2-1-3-3-4-4s-1-2-2-3l1-2z"></path><path d="M497 386l1-1 2 1h0c1 1 2 1 2 2h1c1 1 1 1 2 1v3h0 0l1 3-1 1-3-6v-1l-1-1-4-1v-1z" class="o"></path><path d="M505 392l1-1c4 1 7 2 10 5-2 0-4 2-5 3-2-1-3-2-5-4l-1-3z" class="V"></path><defs><linearGradient id="p" x1="300.282" y1="190.552" x2="311.391" y2="182.774" xlink:href="#B"><stop offset="0" stop-color="#08050b"></stop><stop offset="1" stop-color="#272525"></stop></linearGradient></defs><path fill="url(#p)" d="M285 166l-1-1c1-1 2-2 3-2l1 1v2h1s0 1 1 1h1 0c1 1 2 2 2 3 3 1 6 2 8 4 1 0 1 0 2 1v-2s1 0 1-1v-1-1h0 1 1 1v-1h4 1c-1-1-1-2-1-3h0c1 0 1 0 2 1l1 1h2v-1h1v-1c1 0 2 1 3 2v-1l2 5h-1v3 4h1c0 1 0 1-1 2v3h0c1 0 1 0 2-1v-1s1 0 1-1h0 1c1 1 1 2 2 4 0 1 1 3 1 4h-1l2 3v3 2c1 1 1 1 2 1 0 1 0 1-1 2 0 1 0 1-1 2 1 1 2 1 2 2l2 2 2 2v1l2 2h0l2 2c0 1 1 2 2 3l1 3-1 1c-1-1-1-1-2-1h-1l1 1c0 1 0 2-1 2-1 1-1 2-1 3v2h0v1c1 0 1 0 1 1 1 1 2 1 3 1v1l-1 2v2c-2 1-3 0-5 0l-2-1-3-2c-1 0-2-1-4-1l-3-2-4-3c0-1-1-1-1-2h0-2v1l-1-1c0-1-1-2-2-4-1 0-1-1-1-2v-4-1h-1-1v-1c0-1-1-1-2-2l-1 1-1-2c1-2 0-2 0-4h0l1-1 1 1c1-1 1 0 1-1 0-2-2-3-3-4v-1h0c-2-3-5-5-7-7 0-1-1-2-1-2-2-1-2-1-3-2v-1h0c-3-3-6-5-9-9v-2-2l-1-1v-2l-1-1 1-1v1h1v-2l-1-2z"></path><path d="M304 172c1 1 4 3 5 4l-1 1v1h0c0 1 1 2 2 2h0c0 1 0 2 1 3-3-2-6-6-8-8v-2s1 0 1-1z" class="AM"></path><path d="M316 185l2 1s3 3 3 4h-1-1c1 1 2 3 3 4h0v1c-4-3-6-7-9-10 1 1 2 1 3 1v-1z" class="p"></path><path d="M317 201h0c-1-5-5-10-8-14 1 0 2 1 3 2v1l3 4c1 2 2 4 2 6h1c0-1 0-1-1-2v-2c-1-1-1-2-2-3 0-2-2-3-3-5h0c3 3 5 6 6 10 1 2 3 4 4 5v2s0-1-1-1c-1-1-1 0-2-1l-2-2h0z" class="B"></path><path d="M320 176l1-1v4h1c0 1 0 1-1 2v3 1l-1-1h-1l-1 2-2-1v1c-1 0-2 0-3-1l-2-2c-1-1-1-2-1-3h0c-1 0-2-1-2-2h0v-1l1-1 1 1 1-1h1l1 1h0 0 2v1l1 1 1-1 2-2h1z" class="k"></path><path d="M310 177l1-1h1l1 1h0 0 2v1l-2 2c0-1-1-1-1-1 0-1-1-1-2-2z" class="B"></path><path d="M310 177l1-1h1l1 1h0 0-1v2c0-1-1-1-2-2z" class="H"></path><path d="M310 180l1-1h1v1c0 1 2 2 3 3 0 1 0 1 1 2v1c-1 0-2 0-3-1l-2-2c-1-1-1-2-1-3z" class="r"></path><path d="M320 176l1-1v4h1l-4 4c-1-1-3-2-3-3l1-1 1-1 2-2h1z" class="W"></path><path d="M321 184h0c1 0 1 0 2-1v-1s1 0 1-1h0 1c1 1 1 2 2 4 0 1 1 3 1 4h-1l2 3v3 2c1 1 1 1 2 1 0 1 0 1-1 2 0 1 0 1-1 2v-1l-1 1h-1c0-2-1-3-3-4h0c-1-1-2-2-2-3v-1h0c-1-1-2-3-3-4h1 1c0-1-3-4-3-4l1-2h1l1 1v-1z" class="E"></path><path d="M329 197v2l-1-2h-1c0-2 0-2-1-3h0v-1h1c0 1 1 2 2 2v2z" class="y"></path><path d="M322 195v-1h0c-1-1-2-3-3-4h1 1l1 1 7 10-1 1h-1c0-2-1-3-3-4h0c-1-1-2-2-2-3z" class="k"></path><path d="M318 186l1-2h1l1 1h0s0 1 1 1v-1l1 1c1 0 2 0 2 1 1 1 1 1 1 2v1h-2c-1 1-1 1-2 1l-1-1c0-1-3-4-3-4z" class="D"></path><path d="M322 186v-1l1 1h0v4h0c-2-2-1-3-1-4z" class="B"></path><path d="M311 166c1 0 1 0 2 1l1 1h2v-1h1v-1c1 0 2 1 3 2v-1l2 5h-1v3l-1 1h-1l-2 2-1 1-1-1v-1h-2 0 0l-1-1h-1l-1 1-1-1c-1-1-4-3-5-4v-1-1h0 1 1 1v-1h4 1c-1-1-1-2-1-3h0z" class="E"></path><path d="M312 169l2 1v2s-1 0-2-1v-2z" class="c"></path><path d="M316 173l-1-2c0-1 0-1 1-2h1 1c-1 1-1 2-1 3 1 1 2 1 2 2s0 1 1 1v1h-1c0-1-1-1-2-1v-2h-1z" class="d"></path><path d="M317 167v-1c1 0 2 1 3 2v-1l2 5h-1v3l-1 1v-1-2c-1-3-1-4-3-6z" class="N"></path><path d="M316 173h1v2c1 0 2 0 2 1l-2 2-1 1-1-1v-1h-2 0 0v-3h0l2-1h1z" class="U"></path><path d="M313 174l2-1v1c-1 1-1 2-2 3h0v-3h0z" class="M"></path><path d="M316 173h1v2c1 0 2 0 2 1l-2 2c0-2-1-2-2-4v-1h1z" class="y"></path><path d="M307 170v-1h4 1v2c1 1 2 1 2 1 0 1 0 1-1 2h0v3l-1-1h-1l-1 1-1-1c-1-1-4-3-5-4v-1-1h0 1 1 1z" class="P"></path><path d="M307 170v-1h4 1v2c1 1 2 1 2 1 0 1 0 1-1 2h0-1v-2h0-1c-1-2-3-2-4-2z" class="D"></path><path d="M304 170h1c1 1 3 3 5 4 0 1 1 1 2 2h-1l-1 1-1-1c-1-1-4-3-5-4v-1-1h0z" class="E"></path><path d="M285 166l-1-1c1-1 2-2 3-2l1 1v2h1s0 1 1 1h1 0l-2 1c0 1-1 2-1 3h0c1 0 1 2 1 2h0c0 1 1 1 2 2h-1c0 1 1 2 2 3 0 1 0 0 1 1 1 2 2 4 4 6 1 2 2 3 3 5l10 12v1c1 2 3 3 4 4s2 2 2 3h0c-1 0-2 1-2 2-1 0-1 0-2 1h-1-1v-1c0-1-1-1-2-2l-1 1-1-2c1-2 0-2 0-4h0l1-1 1 1c1-1 1 0 1-1 0-2-2-3-3-4v-1h0c-2-3-5-5-7-7 0-1-1-2-1-2-2-1-2-1-3-2v-1h0c-3-3-6-5-9-9v-2-2l-1-1v-2l-1-1 1-1v1h1v-2l-1-2z" class="I"></path><path d="M285 171v-1l2 2v2l-1 2v-2l-1-1v-2z" class="E"></path><path d="M293 179c1 2 2 4 4 6 1 2 2 3 3 5-1 0-1-1-2-2l-2-1h0c-1-3-3-5-3-8z" class="G"></path><path d="M285 166l-1-1c1-1 2-2 3-2l1 1v2h1s0 1 1 1h1 0l-2 1c-1 1-1 2-2 4l-2-2v1l-1-1 1-1v1h1v-2l-1-2z" class="C"></path><path d="M306 209c1-2 0-2 0-4h0c1 0 2 0 2 1l2 2c1 2 0 1 1 3l1-1v-2h0c1 0 1-1 2-1 1 1 2 2 2 3h0c-1 0-2 1-2 2-1 0-1 0-2 1h-1-1v-1c0-1-1-1-2-2l-1 1-1-2z" class="F"></path><path d="M310 213c3-1 2-3 3-4 1 0 1 0 3 1h0 0c-1 0-2 1-2 2-1 0-1 0-2 1h-1-1z" class="b"></path><path d="M324 198h0c2 1 3 2 3 4h1l1-1v1c1 1 2 1 2 2l2 2 2 2v1l2 2h0l2 2c0 1 1 2 2 3l1 3-1 1c-1-1-1-1-2-1h-1l1 1c0 1 0 2-1 2-1 1-1 2-1 3v2h0v1c1 0 1 0 1 1 1 1 2 1 3 1v1l-1 2v2c-2 1-3 0-5 0l-2-1-3-2c-1 0-2-1-4-1l-3-2-4-3c0-1-1-1-1-2h0-2v1l-1-1c0-1-1-2-2-4-1 0-1-1-1-2v-4-1c1-1 1-1 2-1 0-1 1-2 2-2h0c0-1-1-2-2-3s-3-2-4-4v-1c2 1 3 3 5 3 1 0 1 0 2-1v-3h0l2 2c1 1 1 0 2 1 1 0 1 1 1 1h0l2-2v-4-1z" class="m"></path><path d="M312 213c1-1 1-1 2-1 0-1 1-2 2-2v2c-1 0-2 1-3 1l-1 1v-1z" class="O"></path><path d="M313 220v-1h1v1h0c1 1 2 1 2 2 1 0 1 1 2 1 1 1 2 1 1 3 0-1-1-1-1-2h0-2v1l-1-1c0-1-1-2-2-4z" class="k"></path><path d="M315 215c0 1 1 2 2 4l3 3s0 1 1 1c0 2 1 3 3 4l-1 2-4-3c1-2 0-2-1-3-1 0-1-1-2-1 0-2-1-3-1-4-1-1 0-2 0-3z" class="b"></path><path d="M314 207c-1-1-3-2-4-4v-1c2 1 3 3 5 3 1 0 1 0 2-1h1v1c0 1 1 3 2 4h2 1 1l-1 1h-2c-2 0-3 0-5 2v-2h0c0-1-1-2-2-3z" class="C"></path><path d="M324 199l2 6v1h-1-1v1l1 1c-2 0-2-1-3-2h-1v1l2 2h-1-2c-1-1-2-3-2-4v-1h-1v-3h0l2 2c1 1 1 0 2 1 1 0 1 1 1 1h0l2-2v-4z" class="b"></path><path d="M324 198h0c2 1 3 2 3 4h1l1-1v1c1 1 2 1 2 2l2 2 2 2v2c-1 0-2-1-2-1-1 0-2 0-2 1h-1l-1 1 1 2v1h-1v1 4l-3-3c-2-1-4-2-5-3-1 0-1 0-1-1v-1h1c2 0 2 0 3-2h2 1c0-2 0-3-1-4l-2-6v-1z" class="AM"></path><path d="M329 206l-1-1v-2h1c0 1 1 2 2 2h0v1h-1-1z" class="y"></path><path d="M327 210h0c0-1 1-2 2-2l1 1v1l-1 1 1 2v1h-1v1l-1-1c0-1 0-2-1-4z" class="s"></path><path d="M325 212c0-1 0-1 1-2h1c1 2 1 3 1 4l1 1v4l-3-3h1v-1-2l-2-1h0z" class="k"></path><path d="M331 204l2 2 2 2v2c-1 0-2-1-2-1-1 0-2 0-2 1h-1v-1-1l-1-2h1 1v-1-1z" class="AQ"></path><path d="M330 208c1-1 1-1 2-1 0 1 0 1 1 2-1 0-2 0-2 1h-1v-1-1z" class="M"></path><path d="M321 213c-1 0-1 0-1-1v-1h1c1 1 2 1 4 1h0l2 1v2 1h-1c-2-1-4-2-5-3z" class="r"></path><path d="M325 212l2 1v2h-1 0c-1-1-2-2-2-3h1z" class="AH"></path><path d="M335 208v1l2 2h0l2 2c0 1 1 2 2 3l1 3-1 1c-1-1-1-1-2-1h-1l1 1c0 1 0 2-1 2-1 1-1 2-1 3v2h0l-2-2c-1-1-2-2-3-4l-3-2v-4-1h1v-1l-1-2 1-1h1c0-1 1-1 2-1 0 0 1 1 2 1v-2z" class="x"></path><path d="M334 212l2 2c-1 1-1 2-1 4h-1v-2-1h-1c1-1 1-2 1-3z" class="AK"></path><path d="M335 208v1l2 2h0l2 2c-1 1-1 1-3 1h0v-2l-1-2v-2z" class="s"></path><path d="M337 211l2 2c-1 1-1 1-3 1v-2l1-1z" class="AK"></path><path d="M331 210c2 0 2 1 3 2 0 1 0 2-1 3-1 0-2 0-3-1v-1l-1-2 1-1h1z" class="AH"></path><path d="M329 215v-1h1c1 1 2 1 3 1h1v1 2l-2-1c-1 1 0 1 0 2v2l-3-2v-4z" class="M"></path><path d="M339 213c0 1 1 2 2 3l1 3-1 1c-1-1-1-1-2-1h-1-1 0v-1c-1 0-1-1-1-1 0-1 0-1 1-2h0l-1-1c2 0 2 0 3-1z" class="s"></path><path d="M337 219v-1c-1 0-1-1-1-1 0-1 0-1 1-2 1 1 1 2 2 4h-1-1 0z" class="O"></path><path d="M332 221v-2c0-1-1-1 0-2l2 1h2c1 1 0 1 1 1h0 1l1 1c0 1 0 2-1 2-1 1-1 2-1 3v2h0l-2-2c-1-1-2-2-3-4z" class="U"></path><path d="M336 222h1c-1 1-1 1-1 3h1v2h0l-2-2h1c-1-1-1-1-1-2-1 0-1 0-1-1l1-1 1 1h0z" class="D"></path><path d="M337 219h1l1 1c0 1 0 2-1 2-1 1-1 2-1 3h-1c0-2 0-2 1-3h-1c0-2 0-2 1-3z" class="a"></path><path d="M315 215l2-2h4c1 1 3 2 5 3l3 3 3 2c1 2 2 3 3 4l2 2v1c1 0 1 0 1 1 1 1 2 1 3 1v1l-1 2v2c-2 1-3 0-5 0l-2-1-3-2c-1 0-2-1-4-1l-3-2 1-2c-2-1-3-2-3-4-1 0-1-1-1-1l-3-3c-1-2-2-3-2-4z" class="w"></path><path d="M337 228c1 0 1 0 1 1 1 1 2 1 3 1v1l-1 2v-2h0-3v-3z" class="AC"></path><path d="M320 222c2 0 3 1 5 3l1 1h0v-3l1-1c1 1 2 2 2 3v1h0c1 0 2 1 2 1l1 1-1 1h-1v1 2c-1 0-2-1-4-1l-3-2 1-2c-2-1-3-2-3-4-1 0-1-1-1-1z" class="I"></path><path d="M324 227s0 1 1 1c2 0 3 2 5 2v2c-1 0-2-1-4-1l-3-2 1-2z" class="a"></path><path d="M204 379l1 1c-1 4 1 6 3 9h0l1-1 2 1h1l-2 5v1c1 0 2 0 2 1l-1 2c-1 5-3 10-4 15l-2 11-2 12c-1 2-1 4-2 6h0c1 1 1 1 1 2 0 3 1 12 0 14-1 0-1 1-1 1v1c-1 1-1 1-3 2l-1 1h0l-4 2h-1l-5 1c1 0 1 1 1 2-2 0-4-1-7-2h0v1h0 0s1 0 1 1v1h1c1 0 1 0 1 1 2 0 4-1 6 1h1 1c1-1 2 0 3-1l-3 3c-2 1-3 1-4 1h-1v1s-1 0-1 1l-2 4h-1 0-1c-2-4-4-6-7-9l-3-3c-1-1-1-1-2-1l-1-3s0-1-1-1c0-2 1-2 0-3s-2-2-3-4h0c0-1 0-2-1-2 1-1 1-2 0-3 1-1 1-3 1-5 1-1 2-3 3-4 3-4 13-7 18-8h0c2-1 3-4 3-7l-1-1v-5l-1-1h-2l1-1h0v-2h1v-1l2 1 1-1 1-1c1 0 1-1 2-1 4-5 5-12 7-18 0-1 0-1 1-1 1-2 1-3 1-5h-1l1-1 2-2c-1 0-1-1-1-1-1 0-1-1-1-1 0-2 1-4 2-6z" class="e"></path><path d="M202 401l2-2h0c0 1 0 2-1 3v-1h-1z" class="Y"></path><path d="M205 389v4c1 1 1 3 0 4 0 1 0 2-1 2h0v-3c1-2 1-3 0-4l1-3z" class="o"></path><path d="M194 422h1v1c-1 3-1 7-1 10v-4h-1c0-2 1-5 1-7z" class="i"></path><path d="M204 392c1 1 1 2 0 4v3l-2 2h0c-1-1-1-1-1-2h0c2-2 2-5 3-7z" class="K"></path><path d="M204 387c0 1 1 2 1 2l-1 3c-1 2-1 5-3 7h0v-4c1-2 1-3 1-5h-1l1-1 2-2z" class="q"></path><path d="M204 414v-3-1s0-1 1-1v-1-1l2-5h-1l1-3h1c0-3 0-3 2-5v1c1 0 2 0 2 1l-1 2h-1l-2 4-4 12z" class="X"></path><path d="M210 395c1 0 2 0 2 1l-1 2h-1l-2 4 2-7z" class="Aa"></path><path d="M198 457c0-1 0-1 1-1v-1h0-1v-1h1 1v-5-8l1-1v2h0c1 1 1 1 1 2 0 3 1 12 0 14-1 0-1 1-1 1v1c-1 1-1 1-3 2l2-2c-1-1-1-2-2-3z" class="o"></path><path d="M194 435c0 1 1 1 1 1-1 3-1 5 0 8v6c0 1 1 1 1 2 0 2-2 4-2 6 1 0 2 0 2 1 1 0 1-1 2-2 1 1 1 2 2 3l-2 2-1 1h0l-4 2c0-3-1-9 0-11h0l1 1v-1h0c1-1 1-3 0-3v-1-11-4z" class="v"></path><path d="M194 458c1 0 2 0 2 1 1 0 1-1 2-2 1 1 1 2 2 3l-2 2-1 1h0v-1c-1-1-2-2-3-4z" class="g"></path><path d="M200 396c0-1 0-1 1-1v4c0 1 0 1 1 2v1c-1 0-1 1-1 1v1c0 2 0 4-1 6-1 1-2 4-4 5v-1c-2 1-3 3-4 4-2 0-2 0-3-1l1-1 1-1c1 0 1-1 2-1 4-5 5-12 7-18z" class="Y"></path><path d="M196 414h0v-1c2-3 2-7 4-11 0 1 1 1 1 2 0 2 0 4-1 6-1 1-2 4-4 5v-1z" class="Q"></path><defs><linearGradient id="q" x1="206.519" y1="416.155" x2="204.067" y2="415.663" xlink:href="#B"><stop offset="0" stop-color="#c69e8e"></stop><stop offset="1" stop-color="#d6bcb1"></stop></linearGradient></defs><path fill="url(#q)" d="M208 402l2-4h1c-1 5-3 10-4 15l-2 11-2 12c-1 2-1 4-2 6v-2c0-5 0-11 1-16 1-4 1-7 2-10l4-12z"></path><path d="M194 418h1v2c-1 1 0 1-1 2 0 2-1 5-1 7h1v4 2 4 11 1c1 0 1 2 0 3h0v1l-1-1h0c-1 2 0 8 0 11h-1c-1-5-2-11-2-16s0-9 1-14c0-4 0-9 1-13 0-2 0-3 2-4z" class="l"></path><path d="M193 429h1v4 2 4 11 1c1 0 1 2 0 3h0v1l-1-1h0v-4c-2-7-1-14 0-21z" class="Q"></path><defs><linearGradient id="r" x1="196.212" y1="445.732" x2="184.357" y2="442.129" xlink:href="#B"><stop offset="0" stop-color="#21090a"></stop><stop offset="1" stop-color="#3f1312"></stop></linearGradient></defs><path fill="url(#r)" d="M196 414v1l-2 3c-2 1-2 2-2 4-1 4-1 9-1 13-1 5-1 9-1 14s1 11 2 16l-5 1c-1 0-3 0-4-1h2l-1-1h2c2-3 3-7 3-11-1 0-1-1-1-2h0v-7l-1-1c-1-2-1-3-1-5h-3-1s1 0 2-1c1 0 1 0 2-1v-1-1h0c2-1 3-4 3-7l-1-1v-5l-1-1h-2l1-1h0v-2h1v-1l2 1c1 1 1 1 3 1 1-1 2-3 4-4z"></path><path d="M185 420l1-1h0v-2h1v-1l2 1c1 1 1 1 3 1h-1c-1 0-3 0-4 1v1h-2zm3 1h1c1 1 0 5 0 6l-1-1v-5z" class="K"></path><path d="M196 414v1l-2 3c-2 1-2 2-2 4h-1l-1-1-1-1v-1c0-1 1-1 2-1h1c1-1 2-3 4-4z" class="h"></path><path d="M164 451c1-1 1-3 1-5 1-1 2-3 3-4 3-4 13-7 18-8v1 1c-1 1-1 1-2 1-1 1-2 1-2 1h1 3c0 2 0 3 1 5l1 1v7h0c0 1 0 2 1 2 0 4-1 8-3 11h-2l1 1h-2c1 1 3 1 4 1s1 1 1 2c-2 0-4-1-7-2h0v1h0 0s1 0 1 1v1h1c1 0 1 0 1 1 2 0 4-1 6 1h1 1c1-1 2 0 3-1l-3 3c-2 1-3 1-4 1h-1v1s-1 0-1 1l-2 4h-1 0-1c-2-4-4-6-7-9l-3-3c-1-1-1-1-2-1l-1-3s0-1-1-1c0-2 1-2 0-3s-2-2-3-4h0c0-1 0-2-1-2 1-1 1-2 0-3z" class="V"></path><path d="M187 443l1 1v7c0 2 0 2-2 3l1-11z" class="l"></path><path d="M174 443l1 2c-1 3-1 6 0 10l-1 1c-1-1-1-2-2-3v-4-1c1-2 1-3 2-5z" class="w"></path><path d="M176 440h0l2-1h1c1-1 2-1 3-1h1c-4 2-6 4-8 7l-1-2c1 0 1-1 0-2 1-1 1-1 2-1z" class="v"></path><path d="M188 451h0c0 1 0 2 1 2 0 4-1 8-3 11h-2l1 1h-2c-2-1-4-3-6-5 0-1-1-2-2-3l-1-1 1-1 3 3c1 1 2 1 3 0 3 0 4-2 5-4 2-1 2-1 2-3z" class="q"></path><path d="M171 447v1l1 1v4c1 1 1 2 2 3l1 1c1 1 2 2 2 3 2 2 4 4 6 5 1 1 3 1 4 1s1 1 1 2c-2 0-4-1-7-2h0v1h0 0s1 0 1 1v1h-1v-1c-1-1-2-1-3-1l-1-1c-1-1-3-2-3-3-1-1-1-2-2-2h0l-2-3v-1s0-1-1-2v-1c0-1 0-4 1-6h0c1 0 1-1 1-1z" class="L"></path><path d="M171 447v1l1 1v4 1c-1-2-2-4-2-6 1 0 1-1 1-1z" class="AI"></path><path d="M172 453c1 1 1 2 2 3l1 1c1 1 2 2 2 3 2 2 4 4 6 5 1 1 3 1 4 1s1 1 1 2c-2 0-4-1-7-2-3-4-8-8-9-12v-1z" class="j"></path><path d="M164 451c1-1 1-3 1-5 1-1 2-3 3-4 3-4 13-7 18-8v1 1c-1 1-1 1-2 1-1 1-2 1-2 1-1 0-2 0-3 1h-1l-2 1h0c-1 0-1 0-2 1 1 1 1 2 0 2-1 2-1 3-2 5v1l-1-1v-1s0 1-1 1h0c-1 2-1 5-1 6v1c1 1 1 2 1 2v1l2 3h-1c-1-2-2-3-3-5h0v-2h-2c0 1 0 1 1 2v1h0c0 1 1 2 1 3-1-1-2-2-3-4h0c0-1 0-2-1-2 1-1 1-2 0-3z" class="i"></path><path d="M168 454c-1-2-1-3-1-4l1-2h0c1-2 1-3 3-5 0-2 1-2 3-3l1-1h0l1 1c-1 0-1 0-2 1 1 1 1 2 0 2-1 2-1 3-2 5v1l-1-1v-1s0 1-1 1h0c-1 2-1 5-1 6v1c1 1 1 2 1 2v1l2 3h-1c-1-2-2-3-3-5h0v-2z" class="S"></path><path d="M171 447l1-1v2 1l-1-1v-1z" class="AD"></path><path d="M168 460c0-1-1-2-1-3h0v-1c-1-1-1-1-1-2h2v2h0c1 2 2 3 3 5h1 0c1 0 1 1 2 2 0 1 2 2 3 3l1 1c1 0 2 0 3 1v1h1 1c1 0 1 0 1 1 2 0 4-1 6 1h1 1c1-1 2 0 3-1l-3 3c-2 1-3 1-4 1h-1v1s-1 0-1 1l-2 4h-1 0-1c-2-4-4-6-7-9l-3-3c-1-1-1-1-2-1l-1-3s0-1-1-1c0-2 1-2 0-3z" class="h"></path><path d="M168 460c0-1-1-2-1-3h0v-1c-1-1-1-1-1-2h2v2h0c1 2 2 3 3 5h0c0 1 2 2 2 3 1 1 2 2 3 2v1h0c1 0 1 0 1 1h-1 0c-2-1-3-2-4-1l-2-2-1-1s0-1-1-1c0-2 1-2 0-3z" class="AA"></path><path d="M175 471c1-1 2-1 4-1h1 0c1 0 1 0 2 1h1 0 2 0 4 1 1 1c1-1 2 0 3-1l-3 3c-2 1-3 1-4 1h-1v1s-1 0-1 1l-2 4h-1 0-1c-2-4-4-6-7-9z" class="t"></path><path d="M495 397h1c1 1 1 2 2 4h1l2 7c1 3 1 6 3 8l2 1v-1h1 1c1 1 2 2 2 3v2h0l1 9c0 2 1 3 1 4v2l7 3c2 0 3 0 4 1h1v2c1 1 2 2 2 4h1c1 3 1 5 0 7l-1 5c0 1-1 2-1 3l1 1-2 2v1h-1c-1 1-4 3-5 4h-2c-2 1-5 1-8 0h0c-3 0-7-2-9-4h-5c-1-3-5-6-8-9v-1h-1v1 3h-1l1 1c0 1 0 2-1 3h0 0v-2h0c0-2-1-2-1-3-1-2-1-7-2-9h-2c1-2 3-4 4-5-1-1-1-1-2-1-1-1-1-2-2-3v-2h-1l-2-5v-2h0v-1-2-1h2c1 0 1-1 2-2 0 1 0 1 1 1 0 0 2 1 2 2l1 1c2 4 4 7 7 10 2 1 3 1 5 1v-1l-4-22-4-15 1-1c1 1 1 2 2 3 0 1 1 2 2 2l-2-3 1-3h1l2-1v-2z" class="e"></path><path d="M501 412c-1 0-1 1-1 1-1 1 0 2-1 3h-1v-2-2l1-2h1l1 2zm-6-15h1c1 1 1 2 2 4h0l-2 4-1-1v-1c-1-1-1-1-2-1 0-1-1-1-1-2h0 1l2-1v-2z" class="Y"></path><path d="M503 418c1-1 1 0 2-1v1c1 2 1 4 2 6l-2 1h0v7 2c0 1-1 2-1 3v2h-1v-1h0c1-2 1-3 1-4 1-2 1-6 0-8v-5c-1-1-1-2-1-3z" class="o"></path><path d="M505 418c1 2 1 4 2 6l-2 1-1-6 1-1z" class="X"></path><path d="M498 440v-4 1h1c1 1 2 1 2 3 1 2 0 3 0 5s1 8-1 10v1c-1 0-1 1-1 2h-1c-1 0-1-1-1-2 0-3 0-7 1-10 0-1 0-3-1-4l1-1h-1 0l1-1z" class="i"></path><path d="M497 441c1-1 1-2 2-3 1 0 1 1 2 2 0 2-1 6-1 8v1h-2v-3c0-1 0-3-1-4l1-1h-1 0z" class="T"></path><path d="M488 402l1-1c1 1 1 2 2 3 0 1 1 2 2 2 1 6 3 11 3 17l2 17-1 1h0l-1-1v-1l-4-22-4-15z" class="AZ"></path><path d="M498 401h1l2 7c1 3 1 6 3 8l2 1v-1h1 1c1 1 2 2 2 3v2h0l1 9c0 2 1 3 1 4v2l7 3h-1c-2 0-3-1-5-1-1 1-1 1-1 2l-1 2v1c-1 3-1 6-1 9v3c-1 2 0 5-1 7v2s-1 1-2 1l-1-1h0v-1c0-1-1-1-2-1 1-2 0-6 1-8 1 0 1-2 1-2v-3c1-2 1-5 1-7 0-5 0-12-2-17h0l2-1c-1-2-1-4-2-6v-1c-1 1-1 0-2 1 0-2-1-4-2-6l-1-2c-1-2-2-6-2-9h0z" class="q"></path><path d="M507 424c1 4 1 8 1 12 0 6 1 11 0 16 0 4-2 8-2 12h0v-1c0-1-1-1-2-1 1-2 0-6 1-8 1 0 1-2 1-2v-3c1-2 1-5 1-7 0-5 0-12-2-17h0l2-1z" class="z"></path><path d="M512 440c0-1 0-1 1-2 2 0 3 1 5 1h1c2 0 3 0 4 1h1v2c1 1 2 2 2 4h1c1 3 1 5 0 7l-1 5c0 1-1 2-1 3l1 1-2 2v-2l1-1h-2c0 1-1 1-1 2v-1h-1c-3 3-8 4-12 4l-3-1v-1h0l1 1c1 0 2-1 2-1v-2c1-2 0-5 1-7v-3c0-3 0-6 1-9v-1l1-2z" class="V"></path><path d="M519 439c2 0 3 0 4 1h1v2c-1 1-1 1-1 2-1-2-3-3-5-5h1z" class="Y"></path><path d="M524 442c1 1 2 2 2 4h1c1 3 1 5 0 7h0c-1 0-1 1-1 2-1 0-1 0-1 1h-2l1-1c1-2 1-4 1-6s-1-3-2-5c0-1 0-1 1-2z" class="Q"></path><path d="M506 464l1 1c1 0 2-1 2-1v-2c1-2 0-5 1-7v-3c0-3 0-6 1-9v-1l1-2c0 5-2 13 2 17 1 1 3 2 4 2 2-1 4-1 5-3h0 2c0-1 0-1 1-1 0-1 0-2 1-2h0l-1 5c0 1-1 2-1 3l1 1-2 2v-2l1-1h-2c0 1-1 1-1 2v-1h-1c-3 3-8 4-12 4l-3-1v-1h0z" class="T"></path><path d="M480 425c0 1 0 1 1 1 0 0 2 1 2 2l1 1c2 4 4 7 7 10 2 1 3 1 5 1l1 1h1l-1 1c1 1 1 3 1 4-1 3-1 7-1 10 0 1 0 2 1 2h1c2 0 2 0 3 2 1 0 1 1 2 2 1 0 2 0 2 1v1 1l3 1c4 0 9-1 12-4h1v1c0-1 1-1 1-2h2l-1 1v2 1h-1c-1 1-4 3-5 4h-2c-2 1-5 1-8 0h0c-3 0-7-2-9-4h-5c-1-3-5-6-8-9v-1h-1v1 3h-1l1 1c0 1 0 2-1 3h0 0v-2h0c0-2-1-2-1-3-1-2-1-7-2-9h-2c1-2 3-4 4-5-1-1-1-1-2-1-1-1-1-2-2-3v-2h-1l-2-5v-2h0v-1-2-1h2c1 0 1-1 2-2z" class="AA"></path><path d="M479 440l1-1h1s0 1 1 1l2 2 3 6c0-1 1-3 1-4h1 0c0 1 1 2 1 3l-1 2h0c3 7 9 13 16 16h1l3 1c4 0 9-1 12-4h1v1c-1 1-2 1-3 2-4 2-8 3-13 2-8-3-13-10-18-16-2-2-4-4-5-7-1-1-1-1-2-1-1-1-1-2-2-3z" class="AD"></path><path d="M491 439c2 1 3 1 5 1l1 1h1l-1 1c1 1 1 3 1 4-1 3-1 7-1 10 0 1 0 2 1 2h1c2 0 2 0 3 2 1 0 1 1 2 2 1 0 2 0 2 1v1 1h-1c-7-3-13-9-16-16h0l1-2c0-1-1-2-1-3h0-1c1-2 2-3 2-4h1v-1z" class="L"></path><path d="M491 444l1 1-1 5h-1l-1-1 1-2 1-3z" class="J"></path><path d="M497 456c0 1 0 2 1 2h1c2 0 2 0 3 2 1 0 1 1 2 2 1 0 2 0 2 1v1 1h-1v-1c0-1-1-1-1-2-2 0-3-2-5-2-1-1-2-1-3-3 1 0 1 0 1-1z" class="AG"></path><path d="M496 443v-1h1c1 1 1 3 1 4-1 3-1 7-1 10 0 1 0 1-1 1-2-2 0-11 0-14z" class="Ai"></path><path d="M491 439c2 1 3 1 5 1l1 1h1l-1 1h-1v1c-1 1-2 1-3 1l-1 1-1-1-1 3c0-1-1-2-1-3h0-1c1-2 2-3 2-4h1v-1z" class="j"></path><path d="M491 439c2 1 3 1 5 1l1 1h1l-1 1h-1v1c-1 1-2 1-3 1l-1 1-1-1c1-1 2-2 3-2 0-2-1-2-3-2v-1z" class="t"></path><path d="M480 425c0 1 0 1 1 1 0 0 2 1 2 2l1 1c2 4 4 7 7 10v1h-1c0 1-1 2-2 4 0 1-1 3-1 4l-3-6-2-2c-1 0-1-1-1-1h-1l-1 1v-2h-1l-2-5v-2h0v-1-2-1h2c1 0 1-1 2-2z" class="L"></path><path d="M480 425c0 1 0 1 1 1 0 0 2 1 2 2v2c-1 1-1 1-2 1l-1-3h0l-1 1h0l-1-2c1 0 1-1 2-2z" class="T"></path><path d="M483 430c1 2 1 3 2 5 0 0 1 0 1 1 1 1 2 3 2 4h0l-1 1c-1-1-1-4-2-4l-1-1c-1-1-2-2-2-4 0 0 0-1-1-1 1 0 1 0 2-1z" class="Q"></path><path d="M476 427h2l1 2h0c1 1 1 2 2 3l3 10-2-2c-1 0-1-1-1-1h-1l-1 1v-2h-1l-2-5v-2h0v-1-2-1z" class="S"></path><path d="M476 433l1-1v1c1 0 1 1 1 2l1 3h-1l-2-5z" class="X"></path><path d="M476 427h2l1 2h0c1 1 1 2 2 3-2-1-3 0-4-1 0-2 1-2 1-3h-1l-1 1v-1-1z" class="K"></path><defs><linearGradient id="s" x1="99.269" y1="136.262" x2="140.319" y2="193.505" xlink:href="#B"><stop offset="0" stop-color="#19181a"></stop><stop offset="1" stop-color="#3f3c3d"></stop></linearGradient></defs><path fill="url(#s)" d="M155 96c1-1 1-2 2-2 1-1 2-1 3-1-1 0-1 1-2 1-1 1-1 2-1 3h1l-1 1c1 2 2 3 2 5v3c-4 8-13 14-19 21-6 8-12 16-18 25-1 2-2 5-4 8-1 3-3 6-5 9l-3 6c-3 5-5 10-7 15h1l-1 4h0c1-1 1-2 1-3l1 1-6 14-1-1-9 26c0 3-1 5-1 7l-3 9c-1 3-1 7-3 9h-1l2-3c-1-1-1-1-1-2h-1c1-2 1-5 2-7 1-3 1-7 2-10l-2 1 6-24c2-8 5-16 6-24 0-3-1-6-2-8s-3-4-5-5c-1 0-3 0-4 1-3 2-6 5-9 9-5 7-9 13-11 22 0-4 1-8 2-12 1-1 2-2 3-4l23-34h0-1v-2c0-1 1-2 2-3 1-3 4-6 6-8 3-4 6-8 9-11l3-2 8-8 4-5c2-3 6-5 8-8l5-3h-1l1 1c-1 1-2 3-3 4h1 0 0c2 0 4-3 6-4 3-3 7-5 11-7h0l-1-1c2-1 3-2 4-3h1z"></path><path d="M99 162l-1-1s1 0 1-1v2z" class="I"></path><path d="M104 180l-1-1s0-1 1-2h1l-1 3z" class="F"></path><path d="M106 165l-1-2 1-1h1l1 2-2 1z" class="N"></path><path d="M108 132l3-2 2 2-3 2h0l-2-2z" class="p"></path><path d="M93 151c1-1 2-2 3-2h0v1l1-1-5 7h0-1v-2c0-1 1-2 2-3z" class="m"></path><path d="M99 160h3l1 1c1 2 0 4 1 5v1c-1 0-1-1-1-1l-3-3c0-1 0-1-1-1v-2z" class="F"></path><path d="M123 117c1 0 2 0 4 1-2 1-4 3-6 5h0v-1-1c-1 0-1 0-2 1l4-5z" class="p"></path><path d="M155 96c1-1 1-2 2-2 1-1 2-1 3-1-1 0-1 1-2 1-1 1-1 2-1 3h1l-1 1-6 2h0l-1-1c2-1 3-2 4-3h1z" class="AQ"></path><path d="M108 164l1 1c-1 2-2 3-1 6v1l-1 1v-1c-1 0-1-2-1-3-1-1-1-2-2-2v-1c1 0 1 0 2-1l2-1z" class="b"></path><path d="M91 165c1 0 1 0 2 1l-3 5h-2-1v-2l4-4z" class="J"></path><path d="M88 169h1v1 1h-1l-1-1 1-1z" class="I"></path><path d="M123 143c0-2 2-3 2-5h1c0-1 1-2 1-2 2-2 5-5 7-8v1l1-1c0-1 7-7 8-9l1 1c-1 1-3 2-4 3v1c-6 6-11 12-16 19h-1z" class="G"></path><path d="M119 122c1-1 1-1 2-1v1 1h0l-8 9-2-2 8-8z" class="r"></path><path d="M136 106h-1l1 1c-1 1-2 3-3 4h1 0 0l-6 5c0 1-1 1-1 2-2-1-3-1-4-1 2-3 6-5 8-8l5-3z" class="s"></path><defs><linearGradient id="t" x1="94.874" y1="142.039" x2="110.063" y2="139.91" xlink:href="#B"><stop offset="0" stop-color="#8d8378"></stop><stop offset="1" stop-color="#b0a396"></stop></linearGradient></defs><path fill="url(#t)" d="M108 132l2 2h0l-13 15-1 1v-1h0c-1 0-2 1-3 2 1-3 4-6 6-8 3-4 6-8 9-11z"></path><defs><linearGradient id="u" x1="97.704" y1="183.559" x2="123.112" y2="189.074" xlink:href="#B"><stop offset="0" stop-color="#111012"></stop><stop offset="1" stop-color="#464343"></stop></linearGradient></defs><path fill="url(#u)" d="M140 124h0l-13 18-6 9c-1 2-2 4-3 7l-1 1h1v1c-1 3-3 6-5 9l-3 6c-3 5-5 10-7 15h1l-1 4h0c1-1 1-2 1-3l1 1-6 14-1-1-9 26c0 3-1 5-1 7l-3 9c-1 3-1 7-3 9h-1l2-3c-1-1-1-1-1-2h-1c1-2 1-5 2-7 1-3 1-7 2-10s2-7 3-10l1 1h0l15-45 1-3 2-4 1-1v-3-1l2-2c1-1 2-1 2-2l1-1v-1l1-2v-2-1c1-1 1-2 1-2 1-1 1-1 1-2h0c1 0 1 0 1-1l1-2h1c0-1 0-1 1-2s1-1 1-3h0v-1c1 0 1-1 2-1h1c5-7 10-13 16-19z"></path><path d="M120 148c1-1 1-1 1-3h0v-1c1 0 1-1 2-1h1c-1 2-3 5-4 6v-1z" class="I"></path><path d="M103 190h1l-1 4h0c1-1 1-2 1-3l1 1-6 14-1-1c1-5 4-10 5-15z" class="AO"></path><path d="M85 234c1-3 2-7 3-10l1 1h0c0 2-1 3-1 5h0c0 5-3 10-3 15v-2c0-1 0-1 1-1v-2c0-1 1-3 1-4h0l1-3c0-1 1-1 1-2h0c0 3-1 5-1 7l-3 9c-1 3-1 7-3 9h-1l2-3c-1-1-1-1-1-2h-1c1-2 1-5 2-7 1-3 1-7 2-10z" class="AC"></path><path d="M83 244l1 1-1 8c-1-1-1-1-1-2h-1c1-2 1-5 2-7z" class="R"></path><path d="M85 234c1-3 2-7 3-10l1 1h0c0 2-1 3-1 5-2 5-3 10-4 15l-1-1c1-3 1-7 2-10z" class="C"></path><path d="M540 98c0 1 0 1 2 1s4 0 7 1l11 7 6 6 9 7 10 11 5 5 22 27c2 3 4 6 6 8l12 20v2c-3-5-11-18-17-19-2 0-4 0-5 1-3 2-4 7-4 10-1 7 2 15 4 22v3c3 7 5 14 6 21 2 11 5 23 8 34 1 3 2 7 3 10 0 1 0 1-1 1v-1c-1-2-2-4-2-6-1 0-1-1-1-1h0v2h-1c0-1-1-1-2-2v-2c1-1 0-1 0-2l-10-35-9-28c-1-1-1-3-2-4l-3-10-1 1h0l-1-1h0c-1-1-5-11-6-13l-10-17c-8-12-16-24-26-35l-11-11-1-1h1c1-1 1-2 1-3 0-2-1-4-1-6v-1c0-1 0-1 1-2z" class="J"></path><path d="M540 107l2 3c-1 1-2 1-3 1l-1-1h1c1-1 1-2 1-3z" class="m"></path><path d="M560 128l1-1c2 2 5 5 6 7h-1-1c-2-2-3-4-5-6z" class="a"></path><path d="M575 145c1 2 2 4 4 6l-1 1c1 1 2 3 3 5l-7-12h1z" class="B"></path><path d="M565 134h1 1c3 3 6 7 8 11h-1l-9-11z" class="c"></path><path d="M591 165h1c1 0 1-1 2-1h0c2-2 3-4 5-5 1 0 2 1 2 2-1 1-2 1-3 2-2 1-2 3-2 4-1 2-3 4-3 5v1l1 2h-1l-2-4h0v-2h1c0-1 0-2 1-3h-1v1h-1c1-1 1-1 0-2z" class="N"></path><path d="M593 166v-1h1c1 0 1 1 1 2h0c-1 1-1 1-1 2s-1 1-1 2h-1v-2c0-1 0-2 1-3zm-2 1v1h-1c0-1 0-2-1-3-3-3-5-7-7-10l1-1 1 2c0 1 1 1 1 3l1-1h1v-1h-1-1l1-1h-1v-1h1c0-1 1-1 1-1l1-1h0c1 1 2 1 3 1 0 0 1 0 1 1 1 1 0 1 0 2v1c0-2 0-2-2-3h-1 1c1 1 1 2 1 2v1c-1-1-1-2-2-2h0c1 3 0 4-1 7 1 0 2 1 3 2h0c1 1 1 1 0 2z" class="B"></path><path d="M559 128c0-1 1-1 1 0 2 2 3 4 5 6l9 11 7 12v3h0l-7-12-15-20z" class="G"></path><path d="M542 110c4 5 8 8 12 12 1 1 4 5 5 6l15 20 7 12 13 27-1 1h0l-1-1h0c-1-1-5-11-6-13l-10-17c-8-12-16-24-26-35l-11-11c1 0 2 0 3-1z" class="R"></path><defs><linearGradient id="v" x1="612.708" y1="204.852" x2="598.589" y2="212.641" xlink:href="#B"><stop offset="0" stop-color="#2e2b2d"></stop><stop offset="1" stop-color="#4e4c4a"></stop></linearGradient></defs><path fill="url(#v)" d="M599 194h0c-1-5-3-9-3-13h0c1-1 1-2 2-3h0c1 0 1 1 1 2s1 1 1 2c0 3 1 6 1 9l5 16h2v3c3 7 5 14 6 21 2 11 5 23 8 34 1 3 2 7 3 10 0 1 0 1-1 1v-1c-1-2-2-4-2-6-1 0-1-1-1-1h0v2h-1c0-1-1-1-2-2v-2c1-1 0-1 0-2l-10-35-9-28v-1c1 1 1 2 2 3v1 1l1 1v2c1 1 1 2 1 4 1 1 1 4 3 5 0-1-1-3-1-5-1-1-1-2-1-3v-1c-1-1-1-2-2-3v-2l-1-4-2-3v-2z"></path><path d="M606 207h2v3h0c-1 0-2-2-2-3z" class="W"></path><path d="M619 263c0 2 1 4 1 7 0-1-1-1-2-2v-2c1-1 0-1 0-2s0 0 1-1z" class="F"></path><path d="M599 194c3 7 5 15 7 22l7 24 6 23c-1 1-1 0-1 1l-10-35-9-28v-1c1 1 1 2 2 3v1 1l1 1v2c1 1 1 2 1 4 1 1 1 4 3 5 0-1-1-3-1-5-1-1-1-2-1-3v-1c-1-1-1-2-2-3v-2l-1-4-2-3v-2z" class="AU"></path><path d="M209 440l2-1h1c1 0 2-1 3-2v2 1l-1 3 1 1 2-1 2 1c0 1 0 1-1 2l1 1c0 1-1 1-1 2-2 3-2 9-3 13h1c0 3 1 5 1 7s-1 5-1 6c2 2 1 3 1 5h0c0 3 0 6 1 9 1 8 2 16 6 24l2-2c1 3 2 6 4 9 4 8 9 16 14 23l12 15 6 6-1 1-4 4c0 1-1 2-2 3l-3 3c-1 0 0 0-1-1l-3-4-2 1h0l-1 1 1 3v4c-1 1-2 1-3 1l-1-2s-1-1-2-1l-1-2h-1l-1-2h-1l-2-3h-2l-2-1c0-2-1-4-1-5-2-3-5-7-6-10s-2-5-4-7c-1-1-2 0-3 0l-1-1v-3c0-2 0-4-1-6 0-1 1-3 1-3 0-2-1-4-1-6l-1-3c0-2 0-4-1-5v-2-1h1c0-1-1-2-1-3s-1-2-2-3c0-1-1-2-2-2l-1-1s-1-1-2-1c-3-2-5-4-9-5h-2 0c-1-2-2-3-4-3-1 0-2-1-2-1l-5-3-2 2c-1-2-1-2-1-4 0-1 0-1-1-2h-1v-1-1c2 1 3 1 4 2 1 0 1 0 2 1v1h1v-1c1 0 1-1 2-1h0l-18-12c0-1 1-1 1-1-1-3-1-6-2-9v-1h2v-1c1 0 1 0 2 1l3 3c3 3 5 5 7 9h1 0 1l2-4c0-1 1-1 1-1v-1h1c1 0 2 0 4-1l3-3c-1 1-2 0-3 1h-1-1c-2-2-4-1-6-1 0-1 0-1-1-1h-1v-1c0-1-1-1-1-1h0 0v-1h0c3 1 5 2 7 2 0-1 0-2-1-2l5-1h1l4-2h0l1-1c2-1 2-1 3-2v-1s0-1 1-1c1-2 0-11 0-14 1-1 1-1 1-2l2-2 1 1 3-1z" class="T"></path><path d="M217 520c-1-2-2-5-2-7h1c2 2 2 4 3 6l-2 1z" class="AZ"></path><path d="M219 519l3 9v1c-1-1-1-1-1-2-1-1-1-1-1-2v3l1 1v1h-1c-1-3-2-7-3-10l2-1z" class="u"></path><path d="M255 559c0-1 1-1 1-1l6 6-1 1-4 4c0 1-1 2-2 3l-3 3c-1 0 0 0-1-1l9-10c-1-2-4-3-5-5z" class="Aa"></path><path d="M244 543l12 15s-1 0-1 1c-2-1-10-12-11-14h0c1-1 0-1 0-2z" class="Aj"></path><path d="M232 549l6 10c3 4 6 7 10 11l-2 1v-1h-1-1l-1 1h0c0-3-2-4-3-6l-1-2-10-14c3 2 4 4 6 6h0l-2-3c0-1-1-2-1-3h0z" class="AS"></path><path d="M239 563c2 0 5 5 7 7h-1-1l-1 1h0c0-3-2-4-3-6l-1-2z" class="AI"></path><path d="M224 513l2-2c1 3 2 6 4 9 4 8 9 16 14 23 0 1 1 1 0 2h0c-8-10-14-21-20-32z" class="AZ"></path><path d="M220 530h1v-1l-1-1v-3c0 1 0 1 1 2 0 1 0 1 1 2v-1l10 21h0c0 1 1 2 1 3l2 3h0c-2-2-3-4-6-6l-9-19z" class="Ae"></path><path d="M238 565h0v-1c1 0 1 1 2 1 1 2 3 3 3 6h0l1-1h1 1v1h0l-1 1 1 3v4c-1 1-2 1-3 1l-1-2s-1-1-2-1l-1-2c-1-1-1-3-1-4s0-2 1-3c0-1-1-2-1-3z" class="L"></path><path d="M238 571c0-1 0-2 1-3 0 4 0 7 4 9l1 1h0v-5l-1-2 1-1h1 1v1h0l-1 1 1 3v4c-1 1-2 1-3 1l-1-2s-1-1-2-1l-1-2c-1-1-1-3-1-4z" class="j"></path><path d="M243 571l1-1h1v2l-1 1-1-2z" class="AE"></path><path d="M212 518v-1h1l6 19c2 4 5 8 7 11 2 5 4 10 7 15h1v-1c1 1 2 2 2 3l1 1v-1l1 1c0 1 1 2 1 3-1 1-1 2-1 3s0 3 1 4h-1l-1-2h-1l-2-3h-2l-2-1c0-2-1-4-1-5-2-3-5-7-6-10s-2-5-4-7c-1-1-2 0-3 0l-1-1v-3c0-2 0-4-1-6 0-1 1-3 1-3 0-2-1-4-1-6l-1-3c0-2 0-4-1-5v-2z" class="l"></path><path d="M229 564c0 1 2 2 2 3h0c2 1 2 2 3 3h-2l-2-1c0-2-1-4-1-5z" class="Y"></path><path d="M233 562h1v-1c1 1 2 2 2 3l1 1v-1l1 1c0 1 1 2 1 3-1 1-1 2-1 3v1h-1c-1-3-2-7-4-10z" class="Q"></path><path d="M215 462h1c0 3 1 5 1 7s-1 5-1 6c2 2 1 3 1 5h0l-1 1v12 6l1 11v1l-30-20-18-12c0-1 1-1 1-1-1-3-1-6-2-9v-1h2v-1c1 0 1 0 2 1l3 3c3 3 5 5 7 9h1 0 1c0 1 0 2 1 2v1h1 1c1 0 2 1 3 0l2 2 6 5 7 5 2 2c1 1 2 3 3 4h0c1 0 2 1 2 2 1-1 0-3 1-4v-1l1-1c-1-2-1-6-1-9 0 0 0 1 1 1h0v-7-10c0-4 1-7 1-10z" class="k"></path><path d="M216 475c2 2 1 3 1 5h0l-1 1v12 6c-1-2 0-4 0-6v-14-4z" class="i"></path><path d="M214 482c1 3 1 6 1 9v5h0v7c-1-1 0-2-1-3v-3c-1-2-1-6-1-9 0 0 0 1 1 1h0v-7z" class="D"></path><path d="M170 467c1 0 1 0 2 1-1 2 1 6 2 8-1 0-1 1-2 1-1-3-2-6-2-9v-1z" class="K"></path><path d="M215 462h1c0 3 1 5 1 7s-1 5-1 6v4h-1l-1-7c0-4 1-7 1-10z" class="V"></path><path d="M174 476c1 4 3 5 7 7l1 1h1c0 1 0 1 1 2 2 1 3 1 5 3h-1c-2-1-4-2-6-4-4-2-7-4-10-8 1 0 1-1 2-1z" class="l"></path><path d="M203 495c2 2 5 5 7 6h0c1 0 2 1 2 2 1-1 0-3 1-4v-1l1-1v3c1 1 0 2 1 3v-7h0c1 4-1 8 0 12h0l-10-8-2-2v-1l-1-1 1-1z" class="AY"></path><path d="M172 468l3 3c3 3 5 5 7 9h1c-1 1-1 2-2 3-4-2-6-3-7-7-1-2-3-6-2-8z" class="V"></path><path d="M183 480h0 1c0 1 0 2 1 2v1h1 1c1 0 2 1 3 0l2 2 6 5 7 5 2 2c1 1 2 3 3 4h0 0c-2-1-5-4-7-6l-1 1 1 1v1l2 2-17-11h1c-2-2-3-2-5-3-1-1-1-1-1-2h-1l-1-1c1-1 1-2 2-3z" class="K"></path><path d="M183 480h0 1c0 1 0 2 1 2v1h1 1c1 0 2 1 3 0l2 2c-1 0-1 1-1 2l5 4c-2 0-3-2-5-3-1 0-2 0-2-1l-1-1h0c-2-1-3-2-5-2h-1l-1-1c1-1 1-2 2-3z" class="L"></path><path d="M186 483h1c1 0 2 1 3 0l2 2c-1 0-1 1-1 2-2-1-3-2-5-4z" class="Aj"></path><path d="M192 485l6 5 7 5 2 2c1 1 2 3 3 4h0 0c-2-1-5-4-7-6-2-1-4-3-7-4l-5-4c0-1 0-2 1-2z" class="Ae"></path><path d="M209 440l2-1h1c1 0 2-1 3-2v2 1l-1 3 1 1 2-1 2 1c0 1 0 1-1 2l1 1c0 1-1 1-1 2-2 3-2 9-3 13 0 3-1 6-1 10v10 7h0c-1 0-1-1-1-1 0 3 0 7 1 9l-1 1v1c-1 1 0 3-1 4 0-1-1-2-2-2h0c-1-1-2-3-3-4l-2-2-7-5-6-5-2-2c-1 1-2 0-3 0h-1-1v-1c-1 0-1-1-1-2l2-4c0-1 1-1 1-1v-1h1c1 0 2 0 4-1l3-3c-1 1-2 0-3 1h-1-1c-2-2-4-1-6-1 0-1 0-1-1-1h-1v-1c0-1-1-1-1-1h0 0v-1h0c3 1 5 2 7 2 0-1 0-2-1-2l5-1h1l4-2h0l1-1c2-1 2-1 3-2v-1s0-1 1-1c1-2 0-11 0-14 1-1 1-1 1-2l2-2 1 1 3-1z" class="W"></path><path d="M204 479c-1-2-1-3-1-4h1l1 2-1 2z" class="AB"></path><path d="M205 477c0 3 2 6 2 9l-1-1v-2l-1-1v-1h0-1v-2l1-2z" class="h"></path><path d="M204 481h1 0v1l1 1v2l1 1c0 4 2 9 4 13l-1 2c-1-1-2-3-3-4-1-2-2-3-2-5l-1-4-1-1 1-2c0-1-1-2-1-4l1 1v-1z" class="L"></path><path d="M205 487h1v5 1l-1-1-1-4 1-1z" class="AA"></path><path d="M204 481h1c0 2 1 3 1 4v2h-1l-1 1-1-1 1-2c0-1-1-2-1-4l1 1v-1z" class="t"></path><path d="M203 487l1-2 1 2-1 1-1-1z" class="L"></path><path d="M198 478h1c1 0 1 0 2-1h1l1 4c0 2 1 3 1 4l-1 2 1 1 1 4c0 2 1 3 2 5l-2-2-7-5-6-5-2-2c1 0 2 0 3-1s2-1 3-2c1 0 1-1 2-2z" class="g"></path><path d="M190 483c1 0 2 0 3-1v1c1 1 1 2 2 2s3 3 3 4c1 0 0 0 0 1l-6-5-2-2z" class="AA"></path><path d="M198 478h1v3c1 1 2 3 3 5v3h0c-1 0-1-1-1-2 0 0-1-1-1-2-1-1-2-3-3-4-1 1 0 2-1 3-1-1-1-2-2-2l-1 1v-1c1-1 2-1 3-2 1 0 1-1 2-2z" class="v"></path><path d="M198 478h1c1 0 1 0 2-1h1l1 4c0 2 1 3 1 4l-1 2 1 1 1 4c0 2 1 3 2 5l-2-2c-1-2-3-5-5-7l1-1c0 1 0 2 1 2h0v-3c-1-2-2-4-3-5v-3h-1z" class="X"></path><path d="M198 478h1c1 0 1 0 2-1h1l1 4c0 2 1 3 1 4l-1 2c-1-2-2-7-4-9h-1z" class="T"></path><path d="M210 459l1 1c-3 3-6 7-9 10-5 4-9 9-15 13h-1-1v-1c-1 0-1-1-1-2l2-4c0-1 1-1 1-1v-1h1c1 0 2 0 4-1l2-1 10-8 1-1 5-4z" class="k"></path><path d="M194 472l10-8c0 1-1 2-1 2 0 1-2 2-3 3l-7 7h-1c1-1 1-3 2-4z" class="Ag"></path><path d="M192 473l2-1c-1 1-1 3-2 4l-4 4c-1 1-2 2-3 2v1-1c-1 0-1-1-1-2l2-4c0-1 1-1 1-1v-1h1c1 0 2 0 4-1z" class="AY"></path><path d="M186 476c1 0 1-1 2 0-1 0-1 1-2 1 1 1 1 2 2 3-1 1-2 2-3 2v1-1c-1 0-1-1-1-2l2-4z" class="K"></path><defs><linearGradient id="w" x1="198.486" y1="454.953" x2="208.945" y2="475.23" xlink:href="#B"><stop offset="0" stop-color="#1b0c0d"></stop><stop offset="1" stop-color="#2f2b29"></stop></linearGradient></defs><path fill="url(#w)" d="M209 440l2-1h1c1 0 2-1 3-2v2 1l-1 3 1 1 2-1 2 1c0 1 0 1-1 2l1 1c0 1-1 1-1 2-2 3-2 9-3 13 0 3-1 6-1 10v10 7h0c-1 0-1-1-1-1 0 3 0 7 1 9l-1 1-1-16c-1-3-1-6-1-10v-12h0 0l-1-1-5 4-1 1-10 8-2 1 3-3c-1 1-2 0-3 1h-1-1c-2-2-4-1-6-1 0-1 0-1-1-1h-1v-1c0-1-1-1-1-1h0 0v-1h0c3 1 5 2 7 2 0-1 0-2-1-2l5-1h1l4-2h0l1-1c2-1 2-1 3-2v-1s0-1 1-1c1-2 0-11 0-14 1-1 1-1 1-2l2-2 1 1 3-1z"></path><path d="M197 463h0l1 1c-3 2-6 3-10 4 0-1 0-2-1-2l5-1h1l4-2z" class="S"></path><path d="M214 443l1 1 2-1 2 1c0 1 0 1-1 2l-1-1c-2 0-4 4-5 5-1 0-1-1-1-1l3-6z" class="Aj"></path><path d="M212 482v-1c1-1 0-3 0-4l1-7c0-1-1-3 0-4v22c0 3 0 7 1 9l-1 1-1-16z" class="AM"></path><path d="M210 459l3-5v12c-1 1 0 3 0 4l-1 7c0 1 1 3 0 4v1c-1-3-1-6-1-10v-12h0 0l-1-1z" class="r"></path><path d="M209 440l2-1h1c1 0 2-1 3-2v2 1l-1 3-3 6s0 1 1 1c-4 5-9 11-14 14l-1-1 1-1c2-1 2-1 3-2v-1s0-1 1-1c1-2 0-11 0-14 1-1 1-1 1-2l2-2 1 1 3-1z" class="AD"></path><path d="M209 440l2-1h1c1 0 2-1 3-2v2 1l-1 3-3 6c-1-2-1-3-1-5 0 0-1-1-1-2v-2zm-6 2l2-2 1 1-1 1c2 3 5 5 5 8-2 4-5 7-9 10v-1s0-1 1-1c1-2 0-11 0-14 1-1 1-1 1-2z" class="L"></path><defs><linearGradient id="x" x1="345.707" y1="615.33" x2="345.92" y2="657.644" xlink:href="#B"><stop offset="0" stop-color="#250a10"></stop><stop offset="1" stop-color="#151314"></stop></linearGradient></defs><path fill="url(#x)" d="M337 602l2-2h0c1 0 2-1 3-1v2c0 1 0 3 1 4v1l2 1 1-1c0 1 1 2 1 3l3 3h1 1v-1c1 1 3 3 4 5l5 4 2 2s2 1 3 1l7 2 3 1c1 0 2-1 3-1 4 1 7 1 11 2l1-1h0c0-1 1-2 1-3h0 1c1-2 4-3 6-5h0c1 0 2-1 2-1l1-1 2 1 4 2c2 1 4 1 5 2s2 1 3 1 1 0 2 1h0 1s1 1 2 1 2 1 3 2h0c1 1 1 2 2 3s2 3 3 4v1 2h-3v1c2 2 3 1 3 3h0l-2 3c-1 1-1 0-1 1h-1c-2-1-7 0-9 0h-7-14-12c-1 1-3 1-4 1h0v1c2 3 4 6 8 7s8 0 13-1c-2 2-4 3-7 4s-6 1-10 1c-4-1-9-3-12-7-1-1-1-2-2-3l-1 1c-1 0-2 0-3 1l-2 4h-3 0c1-2 4-6 4-7-1 0-1 0-1 1-1 1-2 3-3 4h-1c-2 1-3 4-5 6-2 1-3 2-4 3-2-1-4-3-5-5 0-1-2-2-2-3l-7-8-1-1c0-1-2-3-3-3h-1-1-1c-1 1-1 2-1 4h-1v-1c-1 1-2 1-2 2s-1 2-2 2h0-1c0-1-1-1-2-1v-2l1-2h0v-2h-7-11c-3 0-5 0-7-1h-5-1-2c-1-1-3 0-4 0-1-1-1-1-2-1h0 5c1-1 2-1 3-1l2-2c3-1 6-1 10-1 5-1 11 0 16-1h0 3v-2l-15-1-24-1h-1c-1-1-2-1-3-1h-1 0c1 0 1 0 2-1l14-4 1-1c1 0 1 0 2-1l9-6 1-1 2-1h1s1 0 1 1h1 2v1l1 1c1-1 3 0 5-2 0-1 0 0 1-1 0 1 0 1 1 1h0c0-1 1-2 2-2 1-1 2-1 3-2l6-6v2l2-2c0 1 0 1 1 1l2-3z"></path><path d="M326 631c1-1 3 0 4 0h0l-1 1h-3l-1-1h1z" class="h"></path><path d="M347 640h4c1 1 0 2 0 3l-1 1-3-4z" class="Aa"></path><path d="M382 631h1c2 0 4 0 5 1h0c-3 0-5 1-7 0h-7l-9-1h0l13 1c1-1 3-1 4-1z" class="h"></path><path d="M389 630h0c2 1 5 1 7 2l-2 1h-11c-1 1-3 1-4 0h0l2-1c2 1 4 0 7 0h0c-1-1-3-1-5-1h-1 1c1 0 4 0 6-1z" class="L"></path><path d="M338 639c1 1 2 3 3 3 3 4 5 7 8 10 3-3 5-6 8-9h1l-2 2-7 10-11-15v-1z" class="X"></path><path d="M394 633l18 1 1 1h-1 1v1h-1 0c1 0 2 0 3 1l-6-1c-1 0-4 0-6-1l-20-2h11z" class="p"></path><path d="M345 655l3 3 1-1c1 0 2-1 3-2l8-8c2-3 4-5 7-7l-3 6c-1 0-1 0-1 1-1 1-2 3-3 4h-1c-2 1-3 4-5 6-2 1-3 2-4 3-2-1-4-3-5-5z" class="i"></path><path d="M340 622h0c5 0 8 3 12 5v1h-1-12c-1-1-1-4 0-5 0-1 0-1 1-1z" class="J"></path><path d="M393 656l-1-1c0-1-5 0-6 0h-1c-1-1-2-1-3-1l-1-1c-1 0-2-1-3-1l-1-1-3-3v-1c1 0 2 2 3 2h1c-2-2-3-4-3-6-1-1-1 0 0-1 1 2 1 3 3 4h1c2 3 4 6 8 7s8 0 13-1c-2 2-4 3-7 4z" class="K"></path><path d="M409 636l6 1h6c1 2 1 2 0 3-2 1-3 0-5 1-4 1-8 0-12 0l-19 1c-2 0-5 1-7 0h0 0c0-1-1-1 0-2 8 1 16 1 24 1 3-1 7-2 11-2h0 2v-1c-2-1-4-1-6-2z" class="D"></path><path d="M306 614l2-1h1s1 0 1 1h1 2v1l1 1c1-1 3 0 5-2 0-1 0 0 1-1 0 1 0 1 1 1v1l1 1-22 5c-2 1-5 2-7 2l1-1c1 0 1 0 2-1l9-6 1-1z" class="AS"></path><path d="M306 614l2-1h1s1 0 1 1h1 2v1l1 1c-2 1-5 2-7 2h-1 1 2c-1 1-3 1-4 1v-1-3l1-1z" class="AP"></path><path d="M306 614c0 2 1 3 0 4h-1v-3l1-1z" class="AE"></path><path d="M421 637h2c1 0 3 1 4 2h1l1 1-2 3c-1 1-1 0-1 1h-1c-2-1-7 0-9 0h-7-14-12c-1 1-3 1-4 1h0c-1-1-1-2-1-3h0c2 1 5 0 7 0l19-1c4 0 8 1 12 0 2-1 3 0 5-1 1-1 1-1 0-3z" class="k"></path><path d="M392 623l8 4v1l2 1 8 2c1 0 2 1 3 1-1 1-2 1-3 1 1 0 1 0 2 1l-18-1 2-1c-2-1-5-1-7-2h0-1l-2-1c-1 0-3 0-4-1h-1c-1 0-2-1-3-1-2 0-4 0-5-2l3 1c1 0 2-1 3-1 4 1 7 1 11 2l1-1h0c0-1 1-2 1-3z" class="e"></path><path d="M402 629l8 2c1 0 2 1 3 1-1 1-2 1-3 1s-3 0-4-1l-7-1c1-1 2-1 3-1v-1h0z" class="AE"></path><path d="M379 625c4 1 7 1 11 2l1-1h0 1c1 0 2 1 2 2h-2v1l2 1c-6-1-12-3-18-4 1 0 2-1 3-1z" class="Ab"></path><path d="M392 623l8 4v1l2 1h0v1c-1 0-2 0-3 1l-5-1-2-1v-1h2c0-1-1-2-2-2h-1c0-1 1-2 1-3z" class="AP"></path><path d="M392 623l8 4v1c-1 0-2 0-3-1h0-3v-2l-2 1h-1c0-1 1-2 1-3z" class="AS"></path><path d="M402 616l2 1 4 2c2 1 4 1 5 2s2 1 3 1 1 0 2 1v1h-1s-1 1-2 1-1-1-2-1l-1 1v1c-1 1-2 3-2 4v1l-8-2-2-1v-1l-8-4h0 1c1-2 4-3 6-5h0c1 0 2-1 2-1l1-1z" class="o"></path><path d="M404 621l-1-1h1c1 0 1 0 2 1h1 2v1h-2l-3-1z" class="l"></path><path d="M408 630v-3c0-1 1-2 2-2s1 0 2 1c-1 1-2 3-2 4h-2z" class="Q"></path><path d="M402 616l2 1 4 2-2 2c-1-1-1-1-2-1h-1l1 1c-1-1-2-1-4-2-1 0-1 0-1-1 1 0 2-1 2-1l1-1z" class="e"></path><path d="M402 616l2 1c0 1 0 0-1 1h-1l-1-1 1-1z" class="l"></path><path d="M403 625h1 2l1 1-1 2c1 0 1 1 2 2h2v1l-8-2-2-1v-1c1 0 1 1 2 0 0 0 1-1 1-2z" class="AH"></path><path d="M403 625h1 2l1 1-1 2c-1-1-1-2-3-3z" class="L"></path><path d="M392 623h1c1-2 4-3 6-5h0c0 1 0 1 1 1 2 1 3 1 4 2l3 1v2l-1 1h-2-1c0 1-1 2-1 2-1 1-1 0-2 0l-8-4h0z" class="q"></path><path d="M418 623h0 1s1 1 2 1 2 1 3 2h0c1 1 1 2 2 3s2 3 3 4v1 2h-3v1c2 2 3 1 3 3h0l-1-1h-1c-1-1-3-2-4-2h-2-6c-1-1-2-1-3-1h0 1v-1h-1 1l-1-1c-1-1-1-1-2-1 1 0 2 0 3-1-1 0-2-1-3-1v-1c0-1 1-3 2-4v-1l1-1c1 0 1 1 2 1s2-1 2-1h1v-1z" class="V"></path><path d="M413 632l5 2h-1c1 1 1 1 1 2l-5-1-1-1c-1-1-1-1-2-1 1 0 2 0 3-1z" class="j"></path><path d="M418 634c3 0 8 1 11 0v2h-3-2-6c0-1 0-1-1-2h1z" class="S"></path><path d="M413 635l5 1h6 2v1c2 2 3 1 3 3h0l-1-1h-1c-1-1-3-2-4-2h-2-6c-1-1-2-1-3-1h0 1v-1h-1 1z" class="u"></path><path d="M424 636h2v1c2 2 3 1 3 3h0l-1-1h-1c-1-1-3-2-4-2h-1l-3-1c2 1 3 1 4 0h1z" class="n"></path><path d="M418 623h0 1s1 1 2 1 2 1 3 2h0c1 1 1 2 2 3l-3-1c-2-1-4-2-6-2-1 0-2 1-3 1s-1-1-1-1l-1-1 1-1c1 0 1 1 2 1s2-1 2-1h1v-1z" class="h"></path><defs><linearGradient id="y" x1="345.831" y1="606.786" x2="340.721" y2="618.749" xlink:href="#B"><stop offset="0" stop-color="#490f10"></stop><stop offset="1" stop-color="#6e1212"></stop></linearGradient></defs><path fill="url(#y)" d="M337 602l2-2h0c1 0 2-1 3-1v2c0 1 0 3 1 4v1l2 1 1-1c0 1 1 2 1 3l3 3h1 1v-1c1 1 3 3 4 5l5 4 2 2-7-2c-5-1-11-3-15-4-3-1-6-2-9-1h-5c-1 0-2 1-3 1h-2l-1-1v-1h0c0-1 1-2 2-2 1-1 2-1 3-2l6-6v2l2-2c0 1 0 1 1 1l2-3z"></path><path d="M346 606c0 1 1 2 1 3l3 3c1 1 1 1 1 2v1c-3-3-6-5-8-9l2 1 1-1z" class="f"></path><path d="M352 611c1 1 3 3 4 5l5 4 2 2-7-2c-1-2-4-4-5-5v-1c0-1 0-1-1-2h1 1v-1z" class="g"></path><path d="M337 602l2-2h0c1 0 2-1 3-1v2c0 1 0 3 1 4l-4-1c-2 2-4 4-6 5-2 2-3 5-6 6-1 0-2 1-3 1v-1l6-3c2-2 3-4 5-7l2-3z" class="f"></path><path d="M342 601c0 1 0 3 1 4l-4-1 2-3h1z" class="e"></path><path d="M332 606l2-2c0 1 0 1 1 1-2 3-3 5-5 7l-6 3v1h-2l-1-1v-1h0c0-1 1-2 2-2 1-1 2-1 3-2l6-6v2z" class="AE"></path><path d="M332 606l2-2c0 1 0 1 1 1-2 3-3 5-5 7l-6 3h-2 0c5-3 7-5 10-9z" class="Af"></path><path d="M494 86v-1h0l-3-2h0l4 2h1 0c1 1 2 1 2 1h2c1 1 3 2 5 3s3 1 5 2c0-1 1-1 2-1 0 0 1 1 2 1 0 0 1 0 1-1 1 1 2 1 2 1h1c0-1 0 0 1-1 0 2 0 3 1 5 0 1 0 1-1 2 2 2 5 4 8 6 1 0 3 1 4 2l7 5 1 1 11 11c10 11 18 23 26 35l10 17c1 2 5 12 6 13l5 15c0 1 0 0-1 1h0 0l-1-1c0 1 0 1-1 2 0 2-2 4-4 5h-4c-1 0-2-1-4-1v1c-2 0-4 0-5 1v1c-1 0-1 0-2-1 0-1 0-2-1-4h0l-1-1-1 1 1 1v1c-1 2-3 1-4 3l-1 1v-1h-1v-2l-6 3h-2c0 1 1 1 1 2 0 0 0 1-1 1h-3-1l-1 2c-1 0-2-1-3-1l-1-1h-2l-2-1h-1-9v1h-4l-1 1c-2-1-4-2-6-4h-2-1l-2-1v-1l1-1c1 0 2 0 2-1v-1l-4 1c-4 0-10 0-12-2s-2-5-2-7l1-5-1-1h-1v1l-1-1-2 7h-1l-3 3-1 1c-2 0-3 1-4 2v-3h-1 0c-2-1-2-2-3-3h-2s-1 0-1-1l1-2c1-2 2-4 2-6h0 0l-2-1v-1l-1 1v-2h-1c5-9 10-19 13-29l3-10v2c1 0 2-1 2-1 1-1 1 0 3-1h0 0c2-7 0-14-1-21 0-2 0-4-1-5v-1c1-2 4-2 5-3l-1-1c2-1 3-3 4-4-1-6-1-11-4-16l-7-8c-1 0-5-3-6-4z" class="w"></path><path d="M581 181v-1c0-1 0-2 1-3h0c0-2-2-2-3-4l-2-4-1-1 9 12-4 1z" class="J"></path><path d="M583 192v1c1 0 1 0 2 1v1h0c0 1 0 3-1 3h0l-1-1c-1 2-2 3-3 5 1 0 1 0 2 1l1-1v1l-1 1-3-3c2-1 4-3 4-5h-1v-3l1-1z" class="G"></path><path d="M585 180c0 2 0 3-1 5h0l-1 1c-1-2-2-3-3-5l-3-6h1c0 2 1 3 2 4 0 1 1 1 1 2h0l4-1z" class="N"></path><path d="M535 112l3 3-4 2c0 2 4 4 4 7h-1c-1-1-5-5-7-5h0s-1-1-1-2l1-1c1 0 1 0 2 1 1-1 1-1 1-2 1-1 2-2 2-3z" class="J"></path><path d="M494 86v-1h0l-3-2h0l4 2h1 0c1 1 2 1 2 1h2c1 1 3 2 5 3s3 1 5 2c0-1 1-1 2-1 0 0 1 1 2 1 0 0 1 0 1-1 1 1 2 1 2 1h1c0-1 0 0 1-1 0 2 0 3 1 5 0 1 0 1-1 2 2 2 5 4 8 6 1 0 3 1 4 2v1l2 3 2 3c0 1-1 2-2 3 0 1 0 1-1 2-1-1-1-1-2-1l-1 1c0 1 1 2 1 2 1 3 2 5 4 8 1 1 2 2 2 3 1 2 3 3 4 5l8 10c8 11 15 22 20 33l6 15c1 4 3 8 4 12h0c1 1 3 2 4 3v1c-2 0-4 0-5 1v1c-1 0-1 0-2-1 0-1 0-2-1-4h0l1-1-1-4-3-6c-3-8-7-17-11-25l-16-25h-2 0l-12-14-4-4h-1 0c-1-2-1-2-3-3h0c-2-2-4-4-7-6-1 1-2 1-4 0-1 1-2 0-3 1l-1-1c2-1 3-3 4-4-1-6-1-11-4-16l-7-8c-1 0-5-3-6-4z" class="AU"></path><path d="M533 109l2 3c0 1-1 2-2 3 0 1 0 1-1 2-1-1-1-1-2-1v-2h-3c2 0 4 0 6-1v-3-1z" class="G"></path><path d="M512 97c1 1 1 2 2 3l1-1v1c-1 2 0 4 0 7 1 0 1 1 1 1l-1 1h0 0l-2-4c0-3 0-4-1-7v-1z" class="O"></path><path d="M524 122l7 7v1l-1 1-4-4h-1 0c-1-2-1-2-3-3v-1c1 0 1-1 2-1z" class="s"></path><path d="M531 129l13 16h-2 0l-12-14 1-1v-1z" class="d"></path><defs><linearGradient id="z" x1="506.968" y1="102.698" x2="519.769" y2="117.717" xlink:href="#B"><stop offset="0" stop-color="#323031"></stop><stop offset="1" stop-color="#4d4948"></stop></linearGradient></defs><path fill="url(#z)" d="M507 98c2 1 3 2 4 4h0c0 1 1 2 2 3l2 4h0 0l1-1 2 5 6 9c-1 0-1 1-2 1v1h0c-2-2-4-4-7-6-1 1-2 1-4 0-1 1-2 0-3 1l-1-1c2-1 3-3 4-4-1-6-1-11-4-16z"></path><path d="M515 109h0l1-1 2 5v1h-1v1l-2-6z" class="AK"></path><path d="M511 114v-3c2 3 3 5 4 7-1 1-2 1-4 0-1 1-2 0-3 1l-1-1c2-1 3-3 4-4z" class="k"></path><path d="M518 113l6 9c-1 0-1 1-2 1-2-1-4-6-5-8v-1h1v-1z" class="y"></path><path d="M574 201c1 1 1 2 2 4 0 0 0 1 1 1h0l-1-3h0c-1-6-4-11-6-16-2-3-3-7-4-11-3-5-7-11-10-16-5-9-11-17-17-26l-8-9c-2-3-4-5-6-8-2-2-3-5-5-8 2 2 4 4 7 5h0 3v2l-1 1c0 1 1 2 1 2 1 3 2 5 4 8 1 1 2 2 2 3 1 2 3 3 4 5l8 10c8 11 15 22 20 33l6 15c1 4 3 8 4 12h0c1 1 3 2 4 3v1c-2 0-4 0-5 1v1c-1 0-1 0-2-1 0-1 0-2-1-4h0l1-1-1-4z" class="B"></path><path d="M578 205c1 1 3 2 4 3v1c-2 0-4 0-5 1v1c-1 0-1 0-2-1 0-1 0-2-1-4h0l1-1v1c1 1 1 2 2 3l1-1v-3h0z" class="p"></path><path d="M494 86v-1h0l-3-2h0l4 2h1 0c1 1 2 1 2 1h2c1 1 3 2 5 3s3 1 5 2c0-1 1-1 2-1 0 0 1 1 2 1 0 0 1 0 1-1 1 1 2 1 2 1h1c0-1 0 0 1-1 0 2 0 3 1 5 0 1 0 1-1 2 2 2 5 4 8 6 1 0 3 1 4 2v1l2 3v1c-4-4-8-7-14-10 0 0-1-1-2-1l-2 1v-1l-1 1c-1-1-1-2-2-3v1c1 3 1 4 1 7-1-1-2-2-2-3h0c-1-2-2-3-4-4l-7-8c-1 0-5-3-6-4z" class="F"></path><path d="M510 91c0-1 1-1 2-1 0 0 1 1 2 1 0 0 1 0 1-1 1 1 2 1 2 1h1c0-1 0 0 1-1 0 2 0 3 1 5 0 1 0 1-1 2l-9-6z" class="r"></path><path d="M500 90c2 0 3 1 4 3h1c1 1 1 0 2 0v1c2 1 3 3 5 3v1c1 3 1 4 1 7-1-1-2-2-2-3h0c-1-2-2-3-4-4l-7-8z" class="D"></path><path d="M526 127l4 4 12 14h0 2l16 25c4 8 8 17 11 25l3 6 1 4-1 1-1-1-1 1 1 1v1c-1 2-3 1-4 3l-1 1v-1h-1v-2l-6 3h-2c0 1 1 1 1 2 0 0 0 1-1 1h-3-1l-1 2c-1 0-2-1-3-1l-1-1h-2l-2-1h-1-9v1h-4l-1 1c-2-1-4-2-6-4h2c1-1 2-7 2-9h1v-8-4c1-2 1-3 0-4 0-1-1-1-1-1v-5-10-1c0-2 0-3-1-4v-7l-1-2v-3l-3-15 1-1c0-3-1-6 0-9 0-1 0-1 1-2z" class="J"></path><path d="M527 154v-1c0-1 1-1 1-2 0 1 1 3 1 4h-1l-1 2v-3z" class="O"></path><path d="M527 157l1-2h1v2l1 7v2c-1 1-1 2-1 4 0-2 0-3-1-4v-7l-1-2z" class="AO"></path><path d="M527 157l1-2h1v2c-1 1-1 1-1 2l-1-2z" class="d"></path><path d="M528 166c1-1 0-2 1-3l1 1v2c-1 1-1 2-1 4 0-2 0-3-1-4z" class="AK"></path><path d="M563 180c1 3 3 5 4 8h0l1 4v4l-1-3h0c-2-4-4-8-4-13z" class="a"></path><path d="M567 188l1 4v4l-1-3c0-2-1-3 0-5z" class="c"></path><path d="M525 138c1 4 3 9 3 13 0 1-1 1-1 2v1l-3-15 1-1z" class="C"></path><path d="M548 195v-3c-1-2-2-5-2-8 1-1 1 0 2 0 2 6 3 12 3 18h-1v-2l-2-5z" class="P"></path><path d="M545 170c2 2 2 4 3 6l1-1c1 2 3 4 4 6 0 1 0 1 1 2h0c1 1 2 2 4 2 0 1 1 1 1 1 1 1 1 1 1 2v1c-2-1-3-1-5-2h0l-3-3h-1v-1c-1-4-4-9-6-13z" class="c"></path><path d="M548 195h0v-1c-1-1-1-2-1-3v-1c-1 0-1-1-1-1v-1c-1-2-2-4-3-5 0-1 0-1 1-1h1v-1c-1-2-2-2-3-4-1 0 0 0 0-1s-1-2-1-3l-2-4 1 1c1 2 3 4 3 7l2 2c0-2-2-4-3-7h0c0-1 0-2-1-3v-1-2c1 2 1 4 2 6 1 4 3 8 5 12-1 0-1-1-2 0 0 3 1 6 2 8v3z" class="H"></path><path d="M529 170c0-2 0-3 1-4v3 4l2 15 1 10v3l-1 1c-1-2-1-4-1-6v-1h0-1v-4c1-2 1-3 0-4 0-1-1-1-1-1v-5-10-1z" class="R"></path><path d="M529 170c0-2 0-3 1-4v3 4c0 5 1 9 0 13v1c0-1-1-1-1-1v-5-10-1z" class="AF"></path><path d="M529 170c0-2 0-3 1-4v3 6l-1 1v-5-1z" class="AL"></path><path d="M550 202h1c1 3 2 7 5 9 1 1 3 1 5 1h-2c0 1 1 1 1 2 0 0 0 1-1 1h-3-1l-1 2c-1 0-2-1-3-1l-1-1h-2l1-2h0 1c0-1 0-2 1-3v-2l-1-6z" class="O"></path><path d="M551 208h1c0 1 1 2 1 2 0 2 1 3 2 4v1l-1 2c-1 0-2-1-3-1l-1-1h-2l1-2h0 1c0-1 0-2 1-3v-2z" class="n"></path><path d="M551 208h1c0 1 1 2 1 2-1 2-1 4-2 6l-1-1h-2l1-2h0 1c0-1 0-2 1-3v-2z" class="AL"></path><path d="M568 192l2 5 1-2 3 6 1 4-1 1-1-1-1 1 1 1v1c-1 2-3 1-4 3l-1 1v-1h-1v-2c1-2 1-4 1-6v-7-4z" class="E"></path><path d="M569 203v-5l3 8 1 1-1 1c-2-1-2-3-3-5z" class="r"></path><path d="M571 195l3 6 1 4-1 1-1-1c-1-3-1-6-3-8l1-2z" class="AJ"></path><path d="M568 203h1c1 2 1 4 3 5l1-1v1c-1 2-3 1-4 3l-1 1v-1h-1v-2c1-2 1-4 1-6z" class="y"></path><path d="M542 145h0 2l16 25c4 8 8 17 11 25l-1 2-2-5-1-4h0c-1-3-3-5-4-8l-21-35z" class="AO"></path><path d="M530 195h1 0v1c0 2 0 4 1 6l1-1c1 3 2 6 5 7 2 2 4 2 7 3 2 0 4 0 6-1-1 1-1 2-1 3h-1 0l-1 2-2-1h-1-9v1h-4l-1 1c-2-1-4-2-6-4h2c1-1 2-7 2-9h1v-8z" class="H"></path><path d="M542 213h7 0l-1 2-2-1h-1-2l-1-1z" class="s"></path><path d="M546 214h0l3-1-1 2-2-1z" class="AF"></path><path d="M536 214c-1-1-3-1-5-2v-1l3-2c1 1 2 2 4 3 1 0 3 0 4 1l1 1h2-9z" class="O"></path><path d="M538 212c1 0 3 0 4 1l1 1h-4l-2-1 1-1z" class="AJ"></path><path d="M532 202l1-1c1 3 2 6 5 7 2 2 4 2 7 3h4v1c-4 0-9 0-12-1-4-3-4-5-5-9z" class="M"></path><path d="M529 203h1c0 1 0 1 1 1 0 1 1 2 1 3l2 2-3 2v1c2 1 4 1 5 2v1h-4l-1 1c-2-1-4-2-6-4h2c1-1 2-7 2-9z" class="E"></path><path d="M532 215h-1c0-1-1-3 0-4 0-1 1-1 1-2l-1-1 1-1 2 2-3 2v1c2 1 4 1 5 2v1h-4z" class="R"></path><path d="M515 118c3 2 5 4 7 6h0c2 1 2 1 3 3h0 1c-1 1-1 1-1 2-1 3 0 6 0 9l-1 1 3 15v3l1 2v7c1 1 1 2 1 4v1 10 5s1 0 1 1c1 1 1 2 0 4v4 8h-1c0 2-1 8-2 9h-2-2-1l-2-1v-1l1-1c1 0 2 0 2-1v-1l-4 1c-4 0-10 0-12-2s-2-5-2-7l1-5-1-1h-1v1l-1-1-2 7h-1l-3 3-1 1c-2 0-3 1-4 2v-3h-1 0c-2-1-2-2-3-3h-2s-1 0-1-1l1-2c1-2 2-4 2-6h0 0l-2-1v-1l-1 1v-2h-1c5-9 10-19 13-29l3-10v2c1 0 2-1 2-1 1-1 1 0 3-1h0 0c2-7 0-14-1-21 0-2 0-4-1-5v-1c1-2 4-2 5-3s2 0 3-1c2 1 3 1 4 0z" class="w"></path><path d="M518 188h1v2c-1 0-1 0-2-1l1-1zm-1-48h1c1 2 1 4 1 6h0c-1-1-2-5-2-6z" class="J"></path><path d="M510 160c0-1 1-1 1 0h0v6h-1l-1 1c0-2 1-5 1-7z" class="Z"></path><path d="M497 200c-1 1-1 2 0 3l-1 1c-2 0-3 1-4 2v-3c2-1 3-1 5-3zm12-33l1-1h1c-1 2-1 8-2 9 0-1 0-1-1-1l1-7z" class="y"></path><path d="M496 194h1c0 1-1 3-1 4h-3c0-1 0-1-1-1 0-1 0-1 1-2h0l1-1v1l2-1z" class="N"></path><path d="M506 185l3-9c1 3 0 8-1 11l-2-2z" class="P"></path><path d="M498 191c-1 1-3 2-4 3-1-2-1-3-1-5l1-1v1c2 0 2 1 4 2z" class="B"></path><path d="M519 182h0c0 2-3 5-4 7s-1 3-2 5l-1-1c2-4 3-8 7-11z" class="G"></path><path d="M494 188c1-2 3-2 5-3h0c1 2 0 3-1 6-2-1-2-2-4-2v-1z" class="D"></path><path d="M505 187l1-2 2 2-1 3c0 1-1 3-1 4l-1-1h-1v1l-1-1 2-6z" class="AJ"></path><path d="M505 187l1-2 2 2-1 3v-2l-2-1z" class="O"></path><path d="M491 181c1 0 1 0 1 1-1 3-2 6-4 9l-2-1v-1c1-3 3-6 5-8z" class="n"></path><path d="M503 174h1c0 2-2 7-3 9 0 0 0 1-1 1v-1l-1 1c0-1 1-1 1-3-1-1-2-1-3-1h-1v-2c0-1 1-2 2-2 1-1 1 0 2 1h1c1-1 2-2 2-3z" class="W"></path><defs><linearGradient id="AA" x1="496.583" y1="163.624" x2="495.855" y2="176.761" xlink:href="#B"><stop offset="0" stop-color="#8d8274"></stop><stop offset="1" stop-color="#c7bdb3"></stop></linearGradient></defs><path fill="url(#AA)" d="M498 162h2 1l-9 20c0-1 0-1-1-1l7-19z"></path><path d="M508 174c1 0 1 0 1 1v1l-3 9-1 2-2 6-2 7h-1l-3 3c-1-1-1-2 0-3 6-7 9-17 11-26z" class="AL"></path><defs><linearGradient id="AB" x1="512.881" y1="144.427" x2="508.164" y2="145.284" xlink:href="#B"><stop offset="0" stop-color="#252425"></stop><stop offset="1" stop-color="#494645"></stop></linearGradient></defs><path fill="url(#AB)" d="M510 160c0-11 1-20 0-30 0-2-1-4-1-6 1 4 2 7 3 11 1 8 0 16-1 25h0 0c0-1-1-1-1 0z"></path><path d="M515 118c3 2 5 4 7 6h0c2 1 2 1 3 3h0 1c-1 1-1 1-1 2-1 3 0 6 0 9l-1 1c0-4-2-8-3-11-2-4-6-8-10-10 2 1 3 1 4 0z" class="R"></path><path d="M522 124c2 1 2 1 3 3h-2l-1-3h0z" class="D"></path><path d="M500 149v2c1 0 2-1 2-1 1-1 1 0 3-1h0c0 3-1 8-3 10l-1 3h-1-2l1-3h0c-1 1-1 0-2 0l3-10z" class="b"></path><path d="M499 159l1-2c1 0 2 1 2 2h0l-1 3h-1-2l1-3z" class="Z"></path><defs><linearGradient id="AC" x1="487.272" y1="174.587" x2="492.637" y2="180.483" xlink:href="#B"><stop offset="0" stop-color="#3f3234"></stop><stop offset="1" stop-color="#5a5344"></stop></linearGradient></defs><path fill="url(#AC)" d="M497 159c1 0 1 1 2 0h0l-1 3-7 19c-2 2-4 5-5 8l-1 1v-2h-1c5-9 10-19 13-29z"></path><defs><linearGradient id="AD" x1="527.632" y1="199.178" x2="525.402" y2="198.903" xlink:href="#B"><stop offset="0" stop-color="#5a5951"></stop><stop offset="1" stop-color="#7c6a61"></stop></linearGradient></defs><path fill="url(#AD)" d="M529 181v5s1 0 1 1c1 1 1 2 0 4v4 8h-1c0 2-1 8-2 9h-2-2-1l-2-1v-1l1-1c1 0 2 0 2-1v-1s3-5 3-6l2-15 1-5z"></path><path d="M529 181v5s1 0 1 1c1 1 1 2 0 4v4 8h-1c0-1 1-3 0-4l-1-1c1-1 1-2 1-3v-5c0-1 0-3-1-4l1-5z" class="x"></path><path d="M529 199c1 1 0 3 0 4 0 2-1 8-2 9h-2-2v-1l1-1c3-4 4-7 5-11z" class="V"></path><path d="M299 203h1l6 6 1 2 1-1c1 1 2 1 2 2v1h1 1v1 4c0 1 0 2 1 2 1 2 2 3 2 4l1 1v-1h2 0c0 1 1 1 1 2l4 3 3 2c2 0 3 1 4 1l3 2 2 1c1 1 1 2 1 2v2c1 0 1 1 1 1h4c0 1 1 2 2 3l1 2h1c1 1 1 2 2 3l1 1v-4l1-1v-1-1-4l1 1 1-2 1-1-1-1 1-1c0-1 0-1 1-2h3 0l2-1c1-1 2-2 3-4l2-2c1-1 2-2 3-2l1-2 1-1c2-1 3-3 5-4 1-1 2-2 4-2l2-1c2 0 4 0 5 1v1s1 1 1 2c0-1 0-1 1-1l2 1 1-2h0l1-1c0 1 0 1 1 2v-1l6 3h1c0 1 0 1 1 1 1 1 1 2 3 2l1 2c1-1 2-1 4-2h0 4c2 1 4 2 5 3 3 2 5 4 7 6v2l2 2h0v-2c2-1 3-2 4-2h0c1 1 1 1 2 1 2 2 2 3 3 5h0l1 1c1 2 1 4 2 5 1 2 1 3 1 5l1 9v5l1 12v5l1 12 2 17h-1-1c-4 1-9 1-13 1-4 1-8 0-13 0-4 0-8 1-12 0l-29-2c-5 0-10-1-15-1-16-2-31-5-47-9l-28-6-2-1c-7-2-14-4-21-8l-2-1-2-1c0-2 1-6 0-8v-2c1-1 1-2 2-3h0 1c2-1 2-3 2-5v-2-5-7c0-3 1-5 0-7h-1l1-3v-1c2-1 3-3 5-6l1-1 3-3c2-1 4-2 5-3l1-1c1 0 2-1 3-2h1 3l6-2c1 0 1 0 2 1l7-3v-1h-1c0-1 1-1 2-1v-2c1-1 2-2 3-2v-1c-1-2-2-4-4-6z" class="AU"></path><path d="M323 259h1l-1 2c0-1-1-1-1-1l1-1z" class="AC"></path><path d="M309 238c1 0 2-1 3-1v1l-2 1s0-1-1-1z" class="G"></path><path d="M314 255c1 0 1 0 2 1-1 0-1 1-2 1h-1l1-2z" class="a"></path><path d="M379 261h0c1 0 2 0 2 1v1c-1-1-2-1-3-2h1z" class="I"></path><path d="M318 267l2 1h-1v2-1s-1-1-2-1c0-1 1-1 1-1z" class="w"></path><path d="M317 268c1 0 2 1 2 1v1 1h-1l-1-2v-1z" class="AC"></path><path d="M382 253s-1 0-2-1v-1c1 0 2 0 3 1h0l-1 1z" class="I"></path><path d="M315 267l1-1v1l-1 2h-2v-1-1h2z" class="W"></path><path d="M376 270l2 2c1 1 0 1 0 2s0 1-1 1c-1-2-1-4-1-5z" class="G"></path><path d="M297 225l3 3h0l-3 1v1h0l-1-1c1-2 1-2 1-4z" class="AA"></path><path d="M309 214h1l2 1v-1 4l-3-4z" class="P"></path><path d="M322 275h2c1 1 2 2 2 3l-1 1c-1-1-2-2-4-3l1-1z" class="G"></path><path d="M389 221l5 2c-2 1-4 1-5 1l-1-2 1-1z" class="d"></path><path d="M320 271c1 0 1 0 2 1 0 1 0 1-1 2l-1 1-1-1c0-2 0-2 1-3z" class="AC"></path><path d="M354 260h0c1-1 2-1 3-1-1 1-1 2-2 3 0 1-1 2-2 2v-2h1v-2z" class="G"></path><path d="M368 241c1 0 1-1 2-1h0c0 2-1 4-2 6v-1c0-1 0-2-1-3l1-1z" class="M"></path><path d="M318 267l-1-2 1-1c1 0 2 1 3 2 0 1 0 1-1 2l-2-1z" class="J"></path><path d="M307 211l1-1c1 1 2 1 2 2v1h1 1v1 1l-2-1h-1l-2-3z" class="U"></path><path d="M312 282c1 0 2 1 3 1s1 0 2 1h0l1 1v1c-2-1-3-1-5-2-1 0-2-1-2-1l1-1z" class="F"></path><path d="M340 260c1 1 0 3 1 5h1v2l-1 1v2l-1-1v-2-7z" class="I"></path><path d="M313 255c2-1 2-2 4-3l2-1 1 1c-1 1-2 3-4 4-1-1-1-1-2-1h-1z" class="W"></path><path d="M338 250s0 1 1 1v1l1-1c1 1 1 2 1 3l-1 1-1 2-1-6h-1l1-1z" class="H"></path><path d="M334 243h0c1 2 1 5 2 6 0 1 1 1 1 1h1l-1 1v3h-1l-1-4c0-3-2-4-1-7z" class="E"></path><path d="M313 220c1 2 2 3 2 4h-1c-1-1-3 0-5 0l-1-1h0c1-1 3-1 4-2l1-1z" class="AF"></path><path d="M365 242h2c1 1 1 2 1 3v1l-1 1h-1c0-1-1-1-1-2v-3z" class="B"></path><path d="M388 242h1c0 1 1 2 2 2 1 1 1 1 1 2h-3-1v1s-1-1-1-2v-1h-1v-1-1c1 0 1 1 2 1h0v-1z" class="AC"></path><path d="M355 266h1l1 1v1h0v3h0c1 2 0 5 0 6h-1c-1-2-1-8-1-11z" class="E"></path><path d="M364 246l1-1c0 1 1 1 1 2l-3 6h0l-1-1c1-1 1-2 1-3-1 0-1-1-1-2l1-1h1z" class="D"></path><path d="M363 246h1c0 1 0 2-1 3-1 0-1-1-1-2l1-1z" class="m"></path><path d="M330 240l1-1h1v1h0v3h1v-1c0 1 1 1 1 1-1 3 1 4 1 7l-1-1h-2v-2c1-1-1-5-2-7z" class="G"></path><path d="M326 239c0 1 1 3 1 5-2 0-2-4-3-5 0 0-2-1-3-1-2-1-2-2-2-3l1-1c1 2 2 3 3 4h1v-2c1 1 1 2 2 3z" class="B"></path><path d="M321 234v-1h0l1-1s1 0 1 1 1 2 2 3h2l1 3-1-1c-1 0-1-1-1-2h0v3c-1-1-1-2-2-3v2h-1c-1-1-2-2-3-4h1z" class="G"></path><path d="M340 255l1 1s0 1 1 1l1 4c0 1 0 3-1 4h-1c-1-2 0-4-1-5l-1-3 1-2z" class="AC"></path><path d="M382 254c2 1 3 1 4 3l1 1c-1 1-1 2-2 2v-1-1h-1c-2 0-3-1-4-2l-1-1h1 2v-1z" class="J"></path><path d="M380 256v-1c2 0 3 1 4 2v1c-2 0-3-1-4-2z" class="G"></path><path d="M317 259h0c0-2 1-2 2-3l1 1c0 2-3 2-1 4 0 1 0 1-1 2-1 0-1 0-2-1-1 0-1-1-1-2h0l-1-1h2 1z" class="J"></path><path d="M317 259h1c-1 1-1 1-1 2l1 1h0-1l-2-2 1-1h1z" class="a"></path><path d="M380 264h1 1 0l3 3h0c0 1 0 1-1 1l-2-1c0-1 0-1-1-2h-1l-1 2 3 3h0-1c-1-1-3-2-4-3l1-1c1 0 1-1 1-1l1-1z" class="J"></path><path d="M315 251l3-3h0c1 1 1 2 2 3v1l-1-1-2 1c-2 1-2 2-4 3h0-2l-1 1c1-1 1-2 2-3l3-2z" class="G"></path><path d="M297 230v-1l3-1 1 1c0 1 0 2-1 2 1 2 2 2 4 3h-2-1l-1-2h-1c-1 1-1 1-3 2h0 0 0l-1-2h0l1-1h1v-1h0z" class="B"></path><path d="M297 230h0c0 1 0 2 1 3h-1l-1 1h0 0l-1-2h0l1-1h1v-1z" class="I"></path><path d="M303 210l3 3-1 1c-2 0-3 1-5 2h-1v-1h-1c0-1 1-1 2-1v-2c1-1 2-2 3-2z" class="AY"></path><path d="M327 234h3v1 1c-1-1-1-2-2-1 0 1 2 3 2 5 1 2 3 6 2 7-1-1-3-5-3-7-1 0-1-1-1-1l-1-3c0-1-1-1 0-2z" class="M"></path><path d="M262 237v-1c2-1 3-3 5-6v1l-3 6c-1 5-1 11-2 17v-7c0-3 1-5 0-7h-1l1-3z" class="H"></path><path d="M291 283c1-1 1-1 2 0v-1c2 1 2 1 3 2s1 1 2 1h0c1 0 2 1 3 1v1c2 1 4 4 5 6v1s0-1-1-1c0-1 0-1-1-2 0 0-1-1-2-1h-1l-1-1h-1c-3-1-5-4-9-4l1-1c-1-1-1 0-2-1h2z" class="U"></path><path d="M290 285c4 0 6 3 9 4h1l1 1h1 0c-3 1-7-2-9-3-1-1-2-1-2-1-1 0-4 1-4 2v1c2 1 3 2 5 2h1c1 0 1 1 2 1h2c1 0 1 0 2 1h2l1 1h3 1 1c1 1 1 1 2 1h1 1l1 1-28-6-2-1c1 0 1-1 1-1 2-1 5-2 7-3z" class="H"></path><path d="M326 231c2 0 3 1 4 1l3 2 2 1c1 1 1 2 1 2v2c1 0 1 1 1 1v2h-1l-1-1h0-1v2h0s-1 0-1-1v1h-1v-3h0v-1h-1l-1 1c0-2-2-4-2-5 1-1 1 0 2 1v-1-1h-3v-1c-1-1-2-1-3-1 1 0 1 0 2-1z" class="C"></path><path d="M333 238l-1-1c-1 0-1 1-2 1v-1c1 0 1-1 1-2h0c0-1 1-1 2-1l2 1c1 1 1 2 1 2v2c1 0 1 1 1 1v2h-1l-1-1h0-1v-1c0-1-1-2-1-2z" class="O"></path><path d="M333 238v-2h1c0 1 1 2 1 3l1 1v2l-1-1h0-1v-1c0-1-1-2-1-2z" class="AO"></path><path d="M370 230c-1 1-1 2-1 3h1 2v-1h4c-1 1-2 1-3 2-1 2-1 3-2 4v2h-1 0c-1 0-1 1-2 1l-1 1h-2v-1-1h0c0-1-1-2-1-3l-1-1c0-1 1-1 1-2l1-1c1 0 2-1 3-2l2-1z" class="P"></path><path d="M365 233l2 1-1 1-2-1 1-1z" class="U"></path><path d="M363 236c0-1 1-1 1-2l2 1c-1 1-1 2-2 2h0l-1-1z" class="H"></path><path d="M368 231c0 1 0 1 1 2h-1l-1 1-2-1c1 0 2-1 3-2z" class="c"></path><path d="M365 240c1-1 1-2 3-3v1 1h0c2 0 2 0 3-1v2h-1 0c-1 0-1 1-2 1l-1 1h-2v-1-1h0z" class="E"></path><path d="M365 240h3v1l-1 1h-2v-1-1z" class="W"></path><path d="M370 230c-1 1-1 2-1 3h1 2v-1h4c-1 1-2 1-3 2-1 2-1 3-2 4v-1h0c-1-1-1-1-1-2h1v-1h-2l-1-1h1c-1-1-1-1-1-2l2-1z" class="E"></path><path d="M290 218c1 0 1 0 2 1l-8 3c-4 2-7 4-11 6 0 2 1 3 2 4-2-1-3-2-4-2-2 0-3 0-4 1v-1l1-1 3-3c2-1 4-2 5-3l1-1c1 0 2-1 3-2h1 3l6-2z" class="M"></path><path d="M281 220h3c-1 1-2 2-4 2h0c-1 0-2 0-4 1l1-1c1 0 2-1 3-2h1z" class="D"></path><path d="M276 223c2-1 3-1 4-1-1 1-2 2-3 2 0 1-1 1-2 2-2 1-3 2-5 2-1 0-1 1-2 1l3-3c2-1 4-2 5-3z" class="E"></path><path d="M309 224c2 0 4-1 5 0h1l1 1v-1h2 0c0 1 1 1 1 2l4 3 3 2c-1 1-1 1-2 1 1 0 2 0 3 1v1c-1 1 0 1 0 2h-2c-1-1-2-2-2-3s-1-1-1-1l-1 1h0v1c-1-1-1-2-2-3-2-2-7 0-10 0 2-1 3-2 5-2 1 0 1 0 2-1-1-2-3-1-4-3 0 0 1 0 1-1h-4 0z" class="c"></path><path d="M316 225v-1h2 0c0 1 1 1 1 2l4 3 3 2c-1 1-1 1-2 1-2-1-4-1-6-4l-2-3z" class="AH"></path><path d="M310 256l1-1h2 0 1l-1 2h1v2l1 1-1 1h0c1 1 1 1 1 2s-1 2-2 3c-1 0-2 0-3 1 0 1 1 3 1 4-2-3-2-6-3-9h-1 0v-1h1v-2h1l-1-1c-1 0-2-1-4 0 1-1 2-2 3-2h2 1z" class="AC"></path><path d="M312 261h1c1 1 1 1 1 2v1c-1 1-1 1-2 1l-2-1c0-2 1-2 2-3z" class="B"></path><path d="M313 255h1l-1 2h1v2l1 1-1 1h0-1-1v-1l-2-1v-2c1-1 2-2 3-2h0z" class="G"></path><path d="M313 255h1l-1 2v1l-3-1c1-1 2-2 3-2h0z" class="D"></path><path d="M278 228c1 0 3-1 4-2l3-3c5-3 11-4 15-5h1v1l-4 4c-4 1-6 3-10 4-1 0-1 0-2 1-1 0-2 0-4 1h-2c-1 0-1 0-1-1z" class="K"></path><path d="M386 216l2 1-2 2c1 2 1 2 3 2l-1 1 1 2c-3 0-4 2-6 4-1 0-2 2-3 2l-4 2h-4c1-1 3-2 4-4s2-2 3-4 4-3 5-6c0 0 0-1 1-1 0-1 0-1 1-1z" class="R"></path><path d="M379 226c1-1 2-3 4-3l1-2 1 1-3 3v3l-2 2v-2c1 0 1 0 1-1-1-1-1-1-2-1z" class="V"></path><path d="M379 226c1 0 1 0 2 1 0 1 0 1-1 1v2h0l-4 2h-4c1-1 3-2 4-4h1c1 0 1-1 2-2z" class="AH"></path><path d="M379 226c1 0 1 0 2 1 0 1 0 1-1 1l-1 1c-1 1-2 1-3 1 0-1 0-1 1-2 1 0 1-1 2-2z" class="n"></path><path d="M386 216l2 1-2 2c0 1 0 2-1 3h0l-1-1-1 2c-2 0-3 2-4 3s-1 2-2 2h-1c1-2 2-2 3-4s4-3 5-6c0 0 0-1 1-1 0-1 0-1 1-1z" class="AQ"></path><path d="M290 261c0-1 0-1 1-1 0 0 2 1 3 1s2 0 2 1c1 1 3 1 4 1h1 1l1 2h0c1 0 2 1 2 1h1v-2l2-2c1 3 1 6 3 9l1 2h0c4 4 9 8 14 11-3 0-5-2-7-3-1 0-1-1-2-1-2-2-5-4-6-6-1-1-1-2-2-3l-4-1h-1c1 1 2 1 2 2h1c1 1 3 2 4 3 0 1 1 2 2 3 1 0 1 1 2 1v1c1 0 1 0 2 1h1v1c-5-2-6-6-10-9-2-1-5-2-7-4-2-1-3-2-5-2l-1-1h0c-1-1-2-1-3 0h1l2 2v1l-2-1-2-3h-1c1-1 2-1 3-1l-1-1c-2-1-3-2-4-2l-1-1c1 0 3 1 3 1z" class="D"></path><path d="M306 264l2-2c1 3 1 6 3 9l1 2h0c-1 0-1 0-1-1-1 0-2-2-2-3-2-2-3-2-3-5z" class="M"></path><path d="M290 261c0-1 0-1 1-1 0 0 2 1 3 1s2 0 2 1c1 1 3 1 4 1h1 1l1 2h0c1 0 2 1 2 1 0 1 0 1-1 1-2 1-4-2-7-3h-2c-1-1-2-1-3-2l-2-1z" class="C"></path><path d="M290 261c0-1 0-1 1-1 0 0 2 1 3 1s2 0 2 1c1 1 3 1 4 1h1l-1 1c-2 0-3 0-4-1-1 0-2 0-4-1h0l-2-1z" class="O"></path><path d="M343 261l1 1 2 1v1l2 2h0l1-2h1v1h1l1-1c0-1 1-1 1-2h0v2 3c-1 2 0 3 0 5s0 6-1 8-2 4-3 5c-1-2-2-3-3-5 0-2 0-2 1-4l-1-1c0-1 0-2-1-3-1-2 0-4 0-6-1 2 0 4-1 6-1 0-1 1-1 1v2c-1 0 0 1-1 2l-1-1c0-1 0-2 1-2v-2h0l-1-2v-2l1-1v-2c1-1 1-3 1-4z" class="J"></path><path d="M346 271l1-1v2h0l1 2c0 1 0 1 1 1v3h1c1-1 1-3 1-4v1l1 1-1 1c0 2-1 3-2 4 0-1 0-2-1-3 0-1-1-2-1-3-1-1-1-3-1-4z" class="D"></path><path d="M343 261l1 1 2 1v1c0 1 0 2 1 3v3l-1 1v-2c0-1 0-2-2-4h0v5l-2 2-1-2v-2l1-1v-2c1-1 1-3 1-4z" class="a"></path><path d="M349 264h1v1h1l1-1c0-1 1-1 1-2h0v2 3c-1 2 0 3 0 5-1 1-1 2-1 4l-1-1v-1c0 1 0 3-1 4h-1v-3c-1 0-1 0-1-1l-1-2h0v-2-3c-1-1-1-2-1-3l2 2h0l1-2z" class="Z"></path><path d="M348 272h2c-1 1-1 2-1 3-1 0-1 0-1-1l-1-2h1z" class="H"></path><path d="M348 272v-3h3c0 1 0 1-1 2v1h-2z" class="B"></path><path d="M349 264h1v1h1l1-1c0-1 1-1 1-2h0v2 3c-1 0-2 0-2-1-1 0-1 2-1 3l-2-2v2 1h-1v-3c-1-1-1-2-1-3l2 2h0l1-2z" class="AO"></path><path d="M287 227c4-1 6-3 10-4v2c0 2 0 2-1 4l1 1v1h-1l-1 1h0c-1 1-2 1-2 2h-1c-2-1-4 2-7 1v1h-1l-1-1-1 1s-1 0-1 1h-3c-1-1-1-1-2-3h-1l1-1v-1c0-1 1-2 2-4 0 1 0 1 1 1h2c2-1 3-1 4-1 1-1 1-1 2-1z" class="v"></path><path d="M286 229h2l-1 1c0 1 0 1 1 2h-2-2c0-1 1-2 2-3h0z" class="L"></path><path d="M288 229h3c1 0 1 0 2 2-2 0-4 1-5 1-1-1-1-1-1-2l1-1z" class="t"></path><path d="M285 228c-1 1-2 2-4 3l-5 1c0-1 1-2 2-4 0 1 0 1 1 1h2c2-1 3-1 4-1z" class="q"></path><path d="M276 234c2 0 2 1 3 0h1c1 0 2-1 3-1h1c0 1 1 1 2 1v1h-1v1h-1l-1-1-1 1s-1 0-1 1h-3c-1-1-1-1-2-3z" class="e"></path><path d="M287 227c4-1 6-3 10-4v2c0 2 0 2-1 4l1 1v1h-1c0-1 0-1-1-1l-2 1c-1-2-1-2-2-2h-3-2s0-1 1-1v-1z" class="o"></path><path d="M291 229l3-1s1 0 2 1h0l1 1v1h-1c0-1 0-1-1-1l-2 1c-1-2-1-2-2-2z" class="L"></path><path d="M287 227c4-1 6-3 10-4v2c0 2 0 2-1 4h0c0-1 0-2-1-3 0 0-1 0-2 1-2 1-4 1-6 1v-1z" class="q"></path><defs><linearGradient id="AE" x1="306.597" y1="275.212" x2="294.293" y2="286.969" xlink:href="#B"><stop offset="0" stop-color="#272728"></stop><stop offset="1" stop-color="#5a5451"></stop></linearGradient></defs><path fill="url(#AE)" d="M284 277v-1c1 0 2 1 3 0h0l1 1c2 0 3 1 5 1-1 1-1 1-2 1l2 1c1-1 2-1 3-1l1 1h0 5c3 0 6 1 9 3 0 0 1 1 2 1 2 1 3 1 5 2l-3 3c-1 0-3 2-4 2-2 0-4-2-6-3-1-1-2-1-3-2-2 0-2-2-4-2v1h0c-1 0-1 0-2-1s-1-1-3-2v1c-1-1-1-1-2 0-2-2-5-4-7-6z"></path><path d="M337 240h4c0 1 1 2 2 3l1 2h1c1 1 1 2 2 3l1 1h-1 0v2l1 2 1 7v2 2l-1 2h0l-2-2v-1l-2-1-1-1-1-4c-1 0-1-1-1-1l-1-1 1-1c0-1 0-2-1-3l-1 1v-1c-1 0-1-1-1-1h-1s-1 0-1-1c-1-1-1-4-2-6v-2h1 0l1 1h1v-2z" class="m"></path><path d="M340 251v-3c1 0 1 0 1 1s1 1 2 1v3l1 2v3h-1l-1-1h0c-1 0-1-1-1-1l-1-1 1-1c0-1 0-2-1-3z" class="c"></path><path d="M337 240h4c0 1 1 2 2 3l1 2-1 1c-1-1-2-2-3-4h-2 0 0c1 3 0 6 1 9-1 0-1-1-1-1h-1s-1 0-1-1c-1-1-1-4-2-6v-2h1 0l1 1h1v-2z" class="U"></path><path d="M334 241h1 0l1 1h1v3c0 1 0 4-1 4-1-1-1-4-2-6v-2z" class="m"></path><path d="M344 245h1c1 1 1 2 2 3l1 1h-1 0v2l1 2 1 7v2 2l-1 2h0l-2-2v-1l-2-1-1-1-1-4h0l1 1h1v-3l-1-2c0-2 0-4 1-6h1l-2-1 1-1z" class="p"></path><path d="M344 245h1c1 1 1 2 2 3l1 1h-1 0s-1 0-1 1h-1v-3h0l-2-1 1-1z" class="b"></path><path d="M344 255l2 6v2l-2-1-1-1-1-4h0l1 1h1v-3z" class="C"></path><path d="M346 261c0-3-2-7 0-10h1l1 2 1 7v2 2l-1 2h0l-2-2v-1-2z" class="r"></path><path d="M363 236l1 1c0 1 1 2 1 3h0v1 1 3l-1 1h-1l-1 1c0 1 0 2 1 2 0 1 0 2-1 3h-1v2 1l-1 1v1l-1-1h-1v1c0 1-1 1-1 2-1 0-2 0-3 1h0v2h-1 0c0 1-1 1-1 2l-1 1h-1v-1h-1v-2-2l-1-7-1-2v-2h0 1v-4l1-1v-1c1 1 1 2 2 3v1 1h1s1-1 2-1h0c0-1 1-2 2-2 0-1 0 0 1-1l1-1c1-1 1 0 2-1l-1-1c1-1 0-1 1-2s2-1 2-2l1-1z" class="s"></path><path d="M356 252c0-2 1-4 2-5h0v1c0 1 1 3 1 4-1-1-1-1-2-1l-1 1z" class="R"></path><path d="M361 248v-2h1v1c0 1 0 2 1 2 0 1 0 2-1 3h-1v2 1c0-1 0-2-1-3 0-1 1 0 1-1v-3z" class="c"></path><path d="M356 252l1-1c1 0 1 0 2 1 0 1 0 2 1 3h1l-1 1v1l-1-1h-1v1c0 1-1 1-1 2-1 0-2 0-3 1h0c0-1 1-1 1-2 0-2 1-4 1-6z" class="F"></path><path d="M351 252c1-1 1-2 2-2h1c1 0 1 0 1 1s0 1-1 2v3c-1 2-2 2-1 4l-2 2h-1v-2c1-2 1-5 1-8z" class="r"></path><path d="M363 236l1 1c0 1 1 2 1 3h0v1 1 3l-1 1h-1l-1 1v-1h-1v2c-1 0-1 1-1 1h-1v-3l3-5c-2 1-2 2-3 4h-1v-2c1-1 1 0 2-1l-1-1c1-1 0-1 1-2s2-1 2-2l1-1z" class="O"></path><path d="M363 246h0v-1c0-2 0-3 1-4h1v1 3l-1 1h-1z" class="AQ"></path><path d="M349 243c1 1 1 2 2 3v1 1h1s1-1 2-1c-2 2-2 3-3 5 0 3 0 6-1 8h-1l-1-7-1-2v-2h0 1v-4l1-1v-1z" class="c"></path><path d="M348 245l1-1v1c0 1 1 4 1 5-1 1-1 2-2 2v1l-1-2v-2h0 1v-4z" class="U"></path><path d="M288 263h0l2 2h1l2 3 2 1 4 3c2 2 3 3 6 4 3 2 4 4 7 6l-1 1c-3-2-6-3-9-3h-5 0l-1-1c-1 0-2 0-3 1l-2-1c1 0 1 0 2-1-2 0-3-1-5-1l-1-1h0c-1 1-2 0-3 0v1c-2 0-2 1-3 2 0-2 1-3 2-4-2 0-2 1-3 2l-3 3c0-2 1-4 1-6h1c0-1 0-1 1-2v-2c1-2 1-3 1-5 1 0 1 0 2-1h0l1-1h2 2z" class="C"></path><path d="M291 277v-1l5 1v1l-1 1c-2-1-3-2-4-2h0z" class="H"></path><path d="M290 268c-1-1-2-1-2-2h0c1 0 2 0 3-1l2 3c-1 0-2-1-3 0z" class="R"></path><path d="M288 263h0c0 1 0 2-1 2h-2l-1 1-1-2h0l1-1h2 2z" class="Z"></path><path d="M281 265c1 0 1 0 2-1l1 2-2 1c1 1 1 2 1 2l-1 1h-2c1-2 1-3 1-5zm9 3c1-1 2 0 3 0l2 1 4 3-1 2c-1-1-2-1-2-1-3-2-4-4-6-5z" class="d"></path><path d="M299 272c2 2 3 3 6 4-3 2-5 1-8 1-1 0-1-1-1-1-1 0-1 0-1-1-1 0 0-1 0-2h1s1 0 2 1l1-2z" class="D"></path><path d="M283 269c0 1 1 1 1 2h1c1 0 2 0 2 1 0 0-1 0-2-1 0 1 0 1-1 2l-1 2h0c-2 0-2 1-3 2l-3 3c0-2 1-4 1-6h1c0-1 0-1 1-2v-2h2l1-1z" class="R"></path><path d="M305 276c3 2 4 4 7 6l-1 1c-3-2-6-3-9-3h0l-3-1h-3v-1h1v-1c3 0 5 1 8-1z" class="M"></path><path d="M297 278h4-2l1 1h1-2-3v-1h1z" class="m"></path><path d="M296 234h0c2-1 2-1 3-2h1l1 2h1 2c1 1 1 3 1 4 1 1 2 1 2 2v1h2v-3h0c1 0 1 1 1 1v5l4-1h2v1h-1l1 1 1 1s1 1 1 2l-3 3-3 2c-1 1-1 2-2 3h-1-2c1-1 1-2 1-3h0c-1-1-1-1-1 0h-1c-1 0-2 1-4 0 0-1-2-1-2-2l1-1c0-2 0-2-1-3l-2 1h-2l-1-1c-2 0-2 1-3 2v-4l1-2-1-1c1-1 1-2 1-3l1-1 2-4h0 0z" class="I"></path><path d="M306 241v2c-1 0-2 1-3 1h0c0-2 0-2 1-3h2z" class="M"></path><path d="M305 238c1 1 2 1 2 2v1h-1-2l-1-1 1-1c1 0 1 0 1-1z" class="J"></path><path d="M293 243h2v1h1c0 1 0 2-2 2-1 0-1 0-2-1l1-2z" class="B"></path><path d="M306 245c1-1 2 0 2-1 0 2 1 2 2 3l1 1h-1v1h0c-1 1-1 1-2 1s-1 0-1-1v-1h0 0v-3h-1z" class="N"></path><path d="M310 244l4-1v1l-2 2-2 1c-1-1-2-1-2-3h2z" class="a"></path><path d="M314 248v1c0 1 1 1 1 2l-3 2-2 1h-1c1-2 2-3 3-4l2-2z" class="B"></path><path d="M295 243h2v1c1 0 1-1 1-1h1c1-1 1-1 3 0 0 0 0 1-1 2h-1-1 0 0l-1 1-2-2h-1v-1z" class="d"></path><path d="M306 245h1v3h0c-1 1-2 3-3 3h-2v-2c0-1 3-3 4-4z" class="C"></path><path d="M314 243h2v1h-1l1 1 1 1s1 1 1 2l-3 3c0-1-1-1-1-2v-1l-2 2-1-1c0-1 1-2 2-2 0-1 0-2 1-3v-1z" class="G"></path><path d="M314 243h2v1h-1l-2 3c0-1 0-2 1-3v-1z" class="J"></path><path d="M316 245l1 1s1 1 1 2l-3 3c0-1-1-1-1-2v-1l1-2 1-1z" class="N"></path><path d="M315 246l1 1v2h-2v-1l1-2z" class="D"></path><path d="M296 234h0c2-1 2-1 3-2h1l1 2h1 2c1 1 1 3 1 4s0 1-1 1l-1 1c-1 0-1 0-1-1h-1c-1 1-1 1-2 1l-1-1v1c-1 0-2 0-2 1h-1l-1-1-1-1 1-1 2-4h0 0z" class="d"></path><path d="M301 234h1l1 1v1h-1c0 1 0 1-1 1h-2v-1c1-1 1-1 2-1v-1z" class="D"></path><path d="M304 234c1 1 1 3 1 4s0 1-1 1c0-1-1-2-1-3v-1l-1-1h2z" class="H"></path><path d="M296 234h0c2-1 2-1 3-2h1l1 2v1c-1 0-1 0-2 1v1h-1v2 1c-1 0-2 0-2 1h-1l-1-1-1-1 1-1 2-4h0 0z" class="P"></path><path d="M296 234h0v1h1 1c0 1 0 1-1 2h0l-1 1h-2l2-4z" class="E"></path><path d="M379 213c2 0 4 0 5 1v1s1 1 1 2c-1 0-1 1-1 1-1 3-4 4-5 6s-2 2-3 4-3 3-4 4v1h-2-1c0-1 0-2 1-3l-2 1c-1 1-2 2-3 2l-1 1c0 1-1 1-1 2l-1 1c0 1-1 1-2 2s0 1-1 2l1 1c-1 1-1 0-2 1l-1 1c-1 1-1 0-1 1-1 0-2 1-2 2h0c-1 0-2 1-2 1h-1v-1-1c-1-1-1-2-2-3v-1-4l1 1 1-2 1-1-1-1 1-1c0-1 0-1 1-2h3 0l2-1c1-1 2-2 3-4l2-2c1-1 2-2 3-2l1-2 1-1c2-1 3-3 5-4 1-1 2-2 4-2l2-1z" class="w"></path><path d="M359 233h0l1 2s1 1 2 1v1c0 1-1 1-2 2l-1-1v-5z" class="I"></path><path d="M354 236c0 1 0 1 1 2v-1c1-1 1 0 2-1v-2l1-1v3c0 2-2 4-3 6-1-1-1-2-1-2-1-2 0-3 0-4z" class="W"></path><path d="M358 233h1v5h-1v1c0 1-2 4-3 5h-1v-1s0-1 1-1c1-2 3-4 3-6v-3z" class="m"></path><path d="M351 235l1-1c0-1 0-1 1-2h3l-2 2v2c0 1-1 2 0 4h0l-2 1-1 1c0-1 0-2-1-3l1-2 1-1-1-1z" class="p"></path><path d="M352 241v-1-4l2-2v2c0 1-1 2 0 4h0l-2 1z" class="AK"></path><path d="M384 215s1 1 1 2c-1 0-1 1-1 1-1 3-4 4-5 6s-2 2-3 4-3 3-4 4v1h-2-1c0-1 0-2 1-3 1 0 3-1 3-2 1-1 2-3 4-4v-1c1-1 1-1 1-2h2c2-2 4-4 4-6z" class="U"></path><path d="M355 242c-1 0-1 1-1 1v1h1c1-1 3-4 3-5v-1h1l1 1c-1 1 0 1-1 2l1 1c-1 1-1 0-2 1l-1 1c-1 1-1 0-1 1-1 0-2 1-2 2h0c-1 0-2 1-2 1h-1v-1-1c-1-1-1-2-2-3v-1-4l1 1c1 1 1 2 1 3l1-1 2-1h0s0 1 1 2z" class="a"></path><path d="M349 242v-4l1 1c1 1 1 2 1 3l1-1 2-1c0 2-2 3-2 4s0 1-1 2c-1-1-1-2-2-3v-1z" class="E"></path><path d="M295 232l1 2-2 4-1 1c0 1 0 2-1 3l1 1-1 2v4c1-1 1-2 3-2l1 1h2l2-1c1 1 1 1 1 3l-1 1c0 1 2 1 2 2 2 1 3 0 4 0h1c0-1 0-1 1 0h0c0 1 0 2-1 3-1 0-2 1-3 2 2-1 3 0 4 0l1 1h-1v2h-1v1h0 1l-2 2v2h-1s-1-1-2-1h0l-1-2h-1-1c-1 0-3 0-4-1 0-1-1-1-2-1s-3-1-3-1c-1 0-1 0-1 1 0 0-2-1-3-1 0-1-1-1-1-2-2-1-6-3-7-5 1 0 1 0 2-1-1-1-1-1-2-1l-2-2v-4-1l-1-1-1-1v-1c0-1-1-2-1-3v-2-1c0-1 1-2 2-2l-1 1h1c1 2 1 2 2 3h3c0-1 1-1 1-1l1-1 1 1h1v-1c3 1 5-2 7-1h1c0-1 1-1 2-2z" class="AC"></path><path d="M286 250l1-1h1 1c0 1 0 1-1 2l-2-1z" class="W"></path><path d="M286 250c0-2-1-2 0-3h0 1v2l-1 1z" class="G"></path><path d="M286 241h1c1 1 1 1 1 3-2 0-3 1-4 3h-1l1-1c0-2 1-3 2-5z" class="F"></path><path d="M292 234h1c-1 1-1 2-1 3v1h-1v2h-1c-1 0 0 0-1-1v-2c0-1 0-1 1-1l2-2h0z" class="AY"></path><path d="M293 259l7 1h0 1v1h-2l1 1h-4c0-1-1-1-2-1s-3-1-3-1l-1-1h3z" class="E"></path><path d="M294 261c1-1 2 0 3 0h2l1 1h-4c0-1-1-1-2-1z" class="c"></path><path d="M279 253c1 0 1 0 2-1-1-1-1-1-2-1l-2-2v-4c5 6 10 10 16 14h-3l1 1c-1 0-1 0-1 1 0 0-2-1-3-1 0-1-1-1-1-2-2-1-6-3-7-5z" class="P"></path><path d="M274 238v-2-1c0-1 1-2 2-2l-1 1h1c1 2 1 2 2 3h3c0-1 1-1 1-1l1-1 1 1h1l-1 2c-1 2-3 5-5 7h0c-1-1-2-1-2-1l-1-1-1-1v-1c0-1-1-2-1-3z" class="q"></path><path d="M275 234h1c1 2 1 2 2 3h3-1c-2 0-3 1-4 1s-1-1-1-1v-3z" class="i"></path><path d="M275 242c1-1 2-2 2-3h1c0 1 1 1 1 2h0v3 1c-1-1-2-1-2-1l-1-1-1-1z" class="o"></path><path d="M279 241c2-1 2-2 4-3h1c-1 2-3 5-5 7h0v-1-3z" class="e"></path><path d="M292 249c1-1 1-2 3-2l1 1h2l2-1c1 1 1 1 1 3l-1 1c0 1 2 1 2 2 2 1 3 0 4 0h1c0-1 0-1 1 0h0c0 1 0 2-1 3-1 0-2 1-3 2 2-1 3 0 4 0l1 1h-1v2h-1v1h0 1l-2 2v2h-1s-1-1-2-1h0l-1-2h-1-1c-1 0-3 0-4-1h4l-1-1h2v-1h-1 0l1-1-1-3v-2h0 0l-1 3h-1c-2-1-3-1-4-1h-1l-1-2c-1 0-1-1-1-2s0-2 1-3h0z" class="F"></path><path d="M294 249c1 0 1 0 1 1v1h-1v-1-1z" class="W"></path><path d="M298 252h2 0c-1 2-2 3-3 4-1 0-1 0-1-1l2-3z" class="H"></path><path d="M302 253c2 1 3 0 4 0h1c0-1 0-1 1 0h0l-1 1c-1 1-2 2-3 1l-1-1h0l-1 1h-1v-2h1z" class="C"></path><path d="M291 252c0-1 0-2 1-3 0 1 0 1 1 2v1h3 0 2l-2 3c0 1 0 1 1 1l1 1c-2-1-3-1-4-1h-1l-1-2c-1 0-1-1-1-2z" class="I"></path><path d="M291 252l2 1v1h-1c-1 0-1-1-1-2z" class="F"></path><path d="M301 262v-1h2c2-1 2-1 2-2v-1l-2 1s0-1-1-1v-1c2 0 3-1 5-1-1 0-2 1-3 2 2-1 3 0 4 0l1 1h-1v2h-1v1h0 1l-2 2v2h-1s-1-1-2-1h0l-1-2h-1-1c-1 0-3 0-4-1h4 1z" class="G"></path><path d="M301 262h1c1 0 2-1 4-1h0c-1 1-2 2-4 2h-1-1c-1 0-3 0-4-1h4 1z" class="R"></path><defs><linearGradient id="AF" x1="261.449" y1="262.91" x2="288.936" y2="262.251" xlink:href="#B"><stop offset="0" stop-color="#1c1b1d"></stop><stop offset="1" stop-color="#434040"></stop></linearGradient></defs><path fill="url(#AF)" d="M262 259v6c1-1 1 0 1-1 1-3 1-7 2-11s3-10 6-14l2-2 1 1c0 1 1 2 1 3v1l1 1 1 1v1 4l2 2c1 0 1 0 2 1-1 1-1 1-2 1 1 2 5 4 7 5 0 1 1 1 1 2l1 1c1 0 2 1 4 2l1 1c-1 0-2 0-3 1l-2-2h0-2-2l-1 1h0c-1 1-1 1-2 1 0 2 0 3-1 5v2c-1 1-1 1-1 2h-1c0 2-1 4-1 6l3-3c1-1 1-2 3-2-1 1-2 2-2 4 1-1 1-2 3-2 2 2 5 4 7 6h-2c1 1 1 0 2 1l-1 1c-2 1-5 2-7 3 0 0 0 1-1 1-7-2-14-4-21-8l-2-1-2-1c0-2 1-6 0-8v-2c1-1 1-2 2-3h0 1c2-1 2-3 2-5v-2z"></path><path d="M277 280l3-3c1-1 1-2 3-2-1 1-2 2-2 4l-5 6c0-1 0-4 1-5z" class="s"></path><path d="M289 283c1 1 1 0 2 1l-1 1c-2 1-5 2-7 3v-2c1-2 4-2 6-3z" class="d"></path><path d="M262 261l-1 20-2-1-2-1c0-2 1-6 0-8v-2c1-1 1-2 2-3h0 1c2-1 2-3 2-5z" class="c"></path><path d="M257 279c0-2 1-6 0-8v-2c1-1 1-2 2-3v9 5l-2-1z" class="I"></path><path d="M268 259h0c0-4 3-4 4-7h-1-1-1l1-1c1-1 2-1 3-2v-1-1h0v-1c0-1 1-1 2-3 0 0-1-1-1-2h1v1l1 1 1 1v1 4l2 2c1 0 1 0 2 1-1 1-1 1-2 1v1c-1 0-2 0-2 1-3 1-5 2-7 3 0 1-1 1-2 1z" class="B"></path><path d="M279 254c-1-1-2-1-2-2-1 0-2-1-2-1h-1-1c1-2 2-1 3-3v-3c-1-1-1-2 0-2l1 1v1 4l2 2c1 0 1 0 2 1-1 1-1 1-2 1v1z" class="E"></path><defs><linearGradient id="AG" x1="265.937" y1="277.443" x2="285.029" y2="256.809" xlink:href="#B"><stop offset="0" stop-color="#312f30"></stop><stop offset="1" stop-color="#504c4b"></stop></linearGradient></defs><path fill="url(#AG)" d="M279 254v-1c1 2 5 4 7 5 0 1 1 1 1 2l1 1c1 0 2 1 4 2l1 1c-1 0-2 0-3 1l-2-2h0-2-2l-1 1h0c-1 1-1 1-2 1 0 2 0 3-1 5v2c-1 1-1 1-1 2v-4-1l-2 2c-3 3-4 7-8 9-1 0-1 0-2-1-1-2 0-4 0-6s-1-3 0-4v-1c0-1-1-1-1-2 0-2 1-5 2-7 1 0 2 0 2-1 2-1 4-2 7-3 0-1 1-1 2-1z"></path><path d="M281 265s0-1-1-1v-1c0-1 3-2 4-3h1v1c1 0 2 0 3 2h-2-2l-1 1h0c-1 1-1 1-2 1z" class="C"></path><path d="M277 255l1 1h-1v2c1 1 3 1 4 2v1h-2l-1-1c-1-1-2-2-3-2-2 0-3 1-5 0 2-1 4-2 7-3z" class="R"></path><path d="M403 223c1-1 2-1 4-2h0 4c2 1 4 2 5 3 3 2 5 4 7 6v2l2 2h0v2 1l-1 2h0l-1 3-2 4h0l-1 1-4 4-1 2c-3 2-5 4-9 6h-3l-1 1h-3c-1 0-2 1-3 1h0v1c-1-1-2-1-3-1l-1-1h-1l-1 2h0c-1 0-1 1-2 1l1 1v1h0-1l-5-2v-1l2-2c1 0 1-1 2-2l-1-1c-1-2-2-2-4-3v-1l1-1h0 1 1v-1l-4-3-1-1c0-1 1-2 2-3h0 1s1 1 2 1l1 1-1 2 1 1 1-1c1 0 1 0 2 1 0 1 1 1 2 2v-2c-1-1-2-1-3-2v-1h1 3c0-1 0-1-1-2-1 0-2-1-2-2h-1c1-2 4 0 6-1v-4l1-1 1-1c0-1 1-2 2-3s1-2 2-4c1 0 1-1 2-1 1-2 1-2 1-4z" class="J"></path><path d="M399 260c0-1 1-2 1-2 1 0 1 0 2 1h1l-1 1h-3z" class="N"></path><path d="M410 247l1 1c1 1 1 1 1 3v-1c-1-1-2-1-2-3z" class="G"></path><path d="M399 255l3-1v2h-3v-1z" class="B"></path><path d="M411 242c1-1 1-1 2-1-1 1-1 3-1 5l-1-1v-3z" class="a"></path><path d="M405 252l2-1v1l1 1v1h0c-1 0-2-1-3-1v-1z" class="F"></path><path d="M401 228c1 1 1 1 2 1s1 0 2 1c0 1 1 1 0 3 0 0-1-1-2-1v-2h-1c-1 0-1-1-1-2z" class="e"></path><path d="M395 255h1l2 2v2h-1c-1 0-1 0-2-1v-1l-1-2h1z" class="E"></path><path d="M395 255h1v1c0 1 0 1-1 1l-1-2h1z" class="C"></path><path d="M381 248c1-1 2-1 3-1l1 1c0 1 0 1 1 2v2h0l-1-1-4-3z" class="a"></path><path d="M395 258h-1c-1-1-3-2-3-4-1 0 0-1 0-1 1 1 2 1 4 2h-1l1 2v1z" class="B"></path><path d="M403 232c1 2 0 3 1 4s1 2 1 3v1h0-2v-2h0l-1-2h0v-1-2l1-1z" class="E"></path><path d="M415 250h-1 0c0-2 0-3-1-4v-1c1-1 1-2 2-3 1 1 1 2 1 3 0 2 0 3-1 5z" class="I"></path><path d="M394 249c1 0 1 0 2 1 0 0 0 1 1 1 0 1 0 1 1 2l-1 1c-1-1-2-1-3-2-1 0-1-1-2-2h2v-1z" class="C"></path><path d="M406 242l1 2c1 0 1-1 2-1v1c-1 1-1 1-2 1v1 4h-2v-1s0-1 1-2v-1l-2-1s-1 0-2-1h3c0-1 1-1 1-2z" class="N"></path><path d="M385 251l1 1 2 2h-1c1 1 1 1 1 2-1 0-1 0-2 1-1-2-2-2-4-3v-1l1-1h0 1 1v-1z" class="B"></path><path d="M385 251l1 1 2 2h-1c-1 0-2-1-4-2h0 1 1v-1z" class="G"></path><path d="M407 234l4 1v1c-1 1-1 1-1 2l-2 1c0 1 1 1 0 1h-1c0-2-1-4-1-6h1 0z" class="b"></path><path d="M407 234l4 1v1c-1 1-1 1-1 2l-2 1v-3c0-1-1-2-1-2h0z" class="w"></path><path d="M411 235h0l2 1h1v2 1h0l-1 2c-1 0-1 0-2 1v-1c-1-1-1-2-1-3s0-1 1-2v-1z" class="W"></path><path d="M411 235h0l2 1h1v2l-2 2-1-4v-1z" class="AA"></path><path d="M388 256h1c0 1-1 2-1 3-1 1-1 1-1 2-1 0-1 0-1 1 1 1 2 1 3 2l-1 1-5-2v-1l2-2c1 0 1-1 2-2l-1-1c1-1 1-1 2-1z" class="W"></path><path d="M392 246h0c1-1 1-1 1-2l1-1h0l1 2h0c1 1 1 2 2 3 0 1 0 1-1 2-1-1-1-1-2-1-2 0-3-1-4-2-1 0-1 0-1-1h3z" class="E"></path><path d="M395 245c1 1 1 2 2 3-1 0-2-1-3-1v-1l1-1z" class="C"></path><path d="M402 236h0 0 0l1 2h0v2h2 0c1 1 1 2 1 2 0 1-1 1-1 2l-1-2h-3-1 0c1-1 2 0 2-1h-3v-1h0v-1l-1-1h0 0c1 0 1-1 2-1h1l1-1z" class="U"></path><path d="M403 238v1h-1s0-1-1-1l1-2 1 2z" class="Z"></path><path d="M405 240c1 1 1 2 1 2 0 1-1 1-1 2l-1-2h-3-1 0c1-1 2 0 2-1h-3v-1h0c1 0 3 0 4 1h2 0v-1h0z" class="B"></path><path d="M415 239c1 0 1 1 2 2v1c1 1 1 1 1 2s0 2 1 2l1 1-4 4-1-1c1-2 1-3 1-5 0-1 0-2-1-3h0l-1-3h0 1z" class="w"></path><path d="M400 228h1c0 1 0 2 1 2v1c1 0 1 0 1 1l-1 1v2 1h0 0l-1 1h-1c-1 0-1 1-2 1h0c-1-1-2-1-3-2l1-1c0-1 1-2 2-3s1-2 2-4z" class="P"></path><path d="M402 231c1 0 1 0 1 1l-1 1v2h-1c0-1 0-1-1-2 1-1 1-2 2-2z" class="a"></path><path d="M396 235l2 1c1 0 2-1 2-1 1 0 1 0 2 1l-1 1h-1c-1 0-1 1-2 1h0c-1-1-2-1-3-2l1-1z" class="E"></path><path d="M395 236c1 1 2 1 3 2h0l1 1v1h0v1h3c0 1-1 0-2 1h0-3l-1 1c0 1-1 1-1 2l-1-2h0l-1 1c0 1 0 1-1 2h0c0-1 0-1-1-2-1 0-2-1-2-2h-1c1-2 4 0 6-1v-4l1-1z" class="N"></path><path d="M395 236c1 1 2 1 3 2h0l1 1v1h0v1c-1 0-3-1-4 0l-1-1v-3l1-1z" class="Z"></path><path d="M398 238l1 1v1h0c-1 0-1 0-2-1l1-1z" class="O"></path><path d="M414 236c1 1 3 1 5 2 1 0 3 1 4 1h1l-1 3-2 4h0l-1 1-1-1c-1 0-1-1-1-2s0-1-1-2v-1c-1-1-1-2-2-2h-1v-1-2z" class="h"></path><path d="M415 239v-1h1c2 2 4 2 6 4v1l-3 3c-1 0-1-1-1-2s0-1-1-2v-1c-1-1-1-2-2-2z" class="o"></path><path d="M418 244v-1c2-1 2-1 4 0l-3 3c-1 0-1-1-1-2z" class="l"></path><path d="M400 242h1 3l1 2h-3c1 1 2 1 2 1l2 1v1c-1 1-1 2-1 2v1h2v1l-2 1v1 1c0 1 1 1 1 2h0-1c-1-1-1-1-3-2l-3 1v-1h-1-1l1-1c-1-1-1-1-1-2-1 0-1-1-1-1 1-1 1-1 1-2-1-1-1-2-2-3h0c0-1 1-1 1-2l1-1h3z" class="I"></path><path d="M399 254v-2h1l1-1 2 2c0 1 0 1-1 1l-3 1v-1z" class="E"></path><path d="M399 247h2c0 1 1 1 1 2h0c-1 1-2 1-3 2l-1-1c0-1 0-2 1-3h0z" class="b"></path><path d="M399 247h2c0 1 1 1 1 2h-1c-1 0-2-1-2-2h0z" class="Z"></path><path d="M400 242h1 3l1 2h-3c-2 1-3 1-4 1s-2-1-2-2l1-1h3z" class="P"></path><path d="M400 242h1v2h-1c0-1 0-1-1-2h-2 3z" class="U"></path><path d="M404 245l2 1v1c-1 1-1 2-1 2v1h2v1l-2 1c-1 0-2-1-2-2v-1h-1c0-1-1-1-1-2h-2 2c1 0 2-1 3-2z" class="H"></path><path d="M403 223c1-1 2-1 4-2h0 4c2 1 4 2 5 3 3 2 5 4 7 6v2l2 2h0v2 1l-1 2h0-1c-1 0-3-1-4-1-2-1-4-1-5-2h-1l-2-1h0l-4-1c-1-1-1-1-2-1 1-2 0-2 0-3-1-1-1-1-2-1s-1 0-2-1h-1c1 0 1-1 2-1 1-2 1-2 1-4z" class="e"></path><path d="M410 224h1c0 1 1 2 1 3h0l-1-1c-1 0-3 0-4-1h1l2-1z" class="Y"></path><path d="M416 224c3 2 5 4 7 6v2h-1s-1 0-2-1c-1 0-2 0-3-1h2c1 1 1 0 1 0s-1-1-2-1h0 1l2 1-5-6z" class="Q"></path><path d="M400 228c1 0 1-1 2-1h2c1 0 2-1 3 0l5 3h-1-6c-1-1-1-1-2-1s-1 0-2-1h-1z" class="AB"></path><path d="M412 230c2 0 3 1 5 2h3v-1c1 1 2 1 2 1h1l2 2h0v2 1l-1 2h0-1 1v-3c-1 0-2-1-3-1-1-1-3-1-4-2h0c-2 0-4-2-6-3h1z" class="T"></path><path d="M420 231c1 1 2 1 2 1h1l2 2-1 1c-1-1-3-2-4-3v-1zm-3 2c1 1 3 1 4 2 1 0 2 1 3 1v3h-1c-1 0-3-1-4-1-2-1-4-1-5-2h-1l1-1h0c2 0 1-1 2-1 0-1 0-1 1-1z" class="o"></path><path d="M405 230h6c2 1 4 3 6 3h0c-1 0-1 0-1 1-1 0 0 1-2 1h0l-1 1-2-1h0l-4-1c-1-1-1-1-2-1 1-2 0-2 0-3z" class="Q"></path><path d="M411 235h1c0-1 0-1 1-1l1 1-1 1-2-1z" class="e"></path><path d="M429 230h0c1 1 1 1 2 1 2 2 2 3 3 5h0l1 1c1 2 1 4 2 5 1 2 1 3 1 5l1 9v5l1 12v5l1 12 2 17h-1-1c-4 1-9 1-13 1-4 1-8 0-13 0-4 0-8 1-12 0l-29-2h5 1-3-1-2 0c-1 0-2-1-3-1v1c-2-1-3 0-5-1h-1c-1-1 0-2 0-3v-1c0-2 0-2 1-3 1 0 2-1 3-2h0v-1h-1-1l-2 2v1-1c0-2 0-2 1-3 2-3 6-4 8-7h0c-1 1-2 1-2 2h-1s-1 0-1 1c-1 0-1 1-2 1l-1-1v1l-1-1h1c1-1 2-2 3-4h0c1-1 1-2 2-2v1c1-2 2-2 4-3h0l5-4c4-3 7-7 9-12v-1-3l1-2h1l1 1c1 0 2 0 3 1v-1h0c1 0 2-1 3-1h3l1-1h3c4-2 6-4 9-6l1-2 4-4 1-1h0l2-4 1-3h0l1-2v-1-2-2c2-1 3-2 4-2z" class="a"></path><path d="M424 239h1c1 1 0 2 0 3l-1 1h0v-1h-1l1-3h0z" class="F"></path><path d="M416 286l1-3 1 1h3c0 1-1 1-1 2l-1-1-3 2v-1z" class="AJ"></path><path d="M416 287l3-2 1 1c-1 2-1 3-3 3 0-1-1-1-1-2z" class="AQ"></path><path d="M428 293l1 1c1 1 0 2 0 3v-1h-1c-1 0-1 0-2-1v-2h2z" class="D"></path><path d="M405 273h-2-3-1-1c1-1 3-2 5-3 1 1 1 2 2 2v1z" class="Z"></path><path d="M416 294h1l1 3-2 2c-1 0-1 0-2-1 1-2 1-3 2-4z" class="D"></path><path d="M376 282h1c1-1 2-1 3-1h1-1c-1 0-2 1-3 2h0c-2 1-3 3-4 3l-1-1c1-2 2-2 4-3h0z" class="I"></path><path d="M391 280c1 0 2-1 3-1h1l-4 4c-1 1-2 2-4 2h0l2-2c1-1 1-1 2-3z" class="Z"></path><path d="M418 273c1 1 2 0 3 0h0v4s-1 0-2 1c-1-2-1-3-1-4v-1z" class="M"></path><path d="M418 273c1 1 2 0 3 0h0v1 1h-1l-2-1v-1z" class="d"></path><path d="M380 294l1 1h0l1-1c0 1-3 3-4 3-1 1-3 1-4 1l-1-1h3-2 0c1-1 1-1 2-1h0c2 0 3-1 4-2z" class="G"></path><path d="M417 283c1-1 2-3 4-3 0-1 0-1 1 0 0 1-1 2-1 4h-3l-1-1z" class="s"></path><path d="M391 280c1-2 3-4 5-6v1c1 2 0 3-1 4h-1c-1 0-2 1-3 1z" class="P"></path><path d="M416 286v1c0 1 1 1 1 2h-1v1h0c2 0 2 1 3 2v1c-1 0-1-1-2-1s-3 2-5 2h0c0-2 2-3 3-5 0 0 1-2 1-3z" class="d"></path><path d="M412 294h0c0 2 1 2 0 3h-4c-2 1-4 2-6 1v-1c2 0 4-1 5-2 2 0 3 0 4-1h1z" class="M"></path><path d="M391 295c2-3 5-5 8-8h1c0 2-3 5-4 6s-2 1-3 2h-2z" class="C"></path><path d="M391 295h2c-1 0-2 0-2 1 1 2 4 1 6 1h3c-1 1-4 0-6 1 0 0-1 1-1 2-1 1-2 3-4 5l3-6c-1-1-2-2-3-2h-1v-1c1 0 2 0 2-1h1z" class="b"></path><path d="M418 273v-1c0-1 1-3 1-4 2-3 4-6 7-9-2 4-4 6-6 10 0 0 0 1-1 2h0 0c1 1 4 1 5 0-1 1-2 1-3 2-1 0-2 1-3 0z" class="m"></path><path d="M441 307h-1c0-4 0-9-1-13h0l-1-1v-1h1c1 0 0 1 1 2v-1s-1-1 0-2h0c-1-1-1-2-1-3l1 2 2 17h-1z" class="G"></path><path d="M440 290h1l2 17h-1l-2-17z" class="B"></path><path d="M405 277h1v1l-3 6c-1 0-2 1-3 2-1 0-2 1-3 2h-1c0-1 2-3 3-4 2-2 4-5 6-7z" class="s"></path><path d="M403 308h9c2 0 4 0 6-1 1 0 3-1 5-1 2-1 5-2 7-2-1 1-4 2-5 3 1 1 2 1 3 1-4 1-8 0-13 0-4 0-8 1-12 0z" class="U"></path><path d="M390 266h0c1-1 1-2 1-3l1-1c0 1 0 3 1 4h1c0 1-1 1-2 1 0 1-1 2-1 2-2 3-4 6-7 8-1 1-1 1-3 2v-1c4-3 7-7 9-12z" class="Z"></path><path d="M374 297h-1-3 0c1-1 1-1 2-1h2 1s1 0 1-1h1c1 0 1-1 2-1h-1c-1 0-1-1-2 0-1 0-1 0-2-1h0v-1h1c2-1 3-1 4-1 1-1 1-1 1-2s1-2 2-2l-2-1v-1c2-1 4-3 5-3l1-1c1 0 1-1 1-1 1-1 3-3 4-3h0c-1 1-2 2-3 4h0c-2 1-6 3-7 4v1c1 0 2-1 3-2 1 0 2-1 3-1l1-1 1 1-2 2c-2 1-5 2-6 4-1 1 0 1 0 1v1l-5 1c2 0 3 0 5 1h2c-2 1-2 0-3 1s-2 2-4 2h0c-1 0-1 0-2 1h0z" class="J"></path><path d="M429 230h0c1 1 1 1 2 1 2 2 2 3 3 5 2 8 2 15 3 23v4c-1-1-1-1-1-2v-1-1-1-4-3c0-1-1-2 0-3h0c-1-2-1-4-1-5l-1-2v-2 3h0v1c-3-1-2-7-4-9h-1-3v2h0c1 1 1 2 2 2v1c2 0 3 2 3 3v1c1 2 2 5 2 7 0 1 1 3 1 4 1 2 1 4 0 7h0v-3c-1-1 0-3 0-4h-1v-1-3c-1-1-1 0-1-1 0-2-1-4-1-5-1-1-1-1-1-2v-1c-1-1-3-2-4-4h-1v-1-2-2c2-1 3-2 4-2z" class="AU"></path><defs><linearGradient id="AH" x1="431.896" y1="267.44" x2="444.298" y2="262.692" xlink:href="#B"><stop offset="0" stop-color="#3a3537"></stop><stop offset="1" stop-color="#645f57"></stop></linearGradient></defs><path fill="url(#AH)" d="M434 236h0l1 1c1 2 1 4 2 5 1 2 1 3 1 5l1 9v5l1 12v5l1 12h-1l-1-2h1c-1-1-1 0-1-1v-6c0-1 0-2-1-4 0-6 0-12-1-18-1-8-1-15-3-23z"></path><path d="M423 242h1v1l1 2-1 1s-1 1-1 2l-6 6c2 0 3-2 4-3l3-1 1 1c0 1 0 1-1 1 1 1 0 1 1 2 1 0 1 0 2 1l-3 3-3 3c-2 1-5 1-7 2 0 1-1 2-2 3-1 2-4 6-7 7v-1c-1 0-1-1-2-2 2-1 4-3 6-4l2-3h1v-1c-1 0-5 1-6 2-4 2-6 4-10 5v1h-3-1c1-1 3-2 3-4h-2c-1-1-1-3-1-4l-1 1c0 1 0 2-1 3h0v-1-3l1-2h1l1 1c1 0 2 0 3 1v-1h0c1 0 2-1 3-1h3l1-1h3c4-2 6-4 9-6l1-2 4-4 1-1h0l2-4z" class="E"></path><path d="M406 262l1 1c-2 0-3 1-5 1h0v-1h-1 0 1l4-1z" class="M"></path><path d="M397 264h4c-2 2-3 2-5 3v-1l1-2z" class="P"></path><path d="M406 262c2-1 5-2 8-2-2 2-4 2-7 3l-1-1z" class="C"></path><path d="M393 261c1 0 2 0 3 1 2 0 3 1 5 1h1v1h0-1-4c-2-1-3-2-4-3h0z" class="AO"></path><path d="M415 253l1 1c-2 2-5 4-8 5h-2c4-2 6-4 9-6z" class="x"></path><path d="M420 247l1-1c1 1 1 1 0 3-1 1-3 4-5 5l-1-1 1-2 4-4z" class="Z"></path><path d="M390 262l1-2h1l1 1h0c1 1 2 2 4 3l-1 2h-1-2c-1-1-1-3-1-4l-1 1c0 1 0 2-1 3h0v-1-3z" class="G"></path><path d="M409 266l5-3c-3 4-6 7-9 9-1 0-1-1-2-2 2-1 4-3 6-4z" class="x"></path><path d="M403 259h3 2c-2 1-3 2-5 2h0 2c-1 1-1 1-2 1l-1 1h-1 0c-2 0-3-1-5-1v-1h0c1 0 2-1 3-1h3l1-1z" class="b"></path><path d="M403 259h3 2c-2 1-3 2-5 2h0c-1 0-2 0-3-1h2l1-1z" class="Z"></path><defs><linearGradient id="AI" x1="459.878" y1="337.661" x2="572.925" y2="404.209" xlink:href="#B"><stop offset="0" stop-color="#181218"></stop><stop offset="1" stop-color="#2a2928"></stop></linearGradient></defs><path fill="url(#AI)" d="M592 187h0l1 1h0l1-1 3 10c1 1 1 3 2 4l9 28 10 35c0 1 1 1 0 2v2c1 1 2 1 2 2h1l4 10-2 1-4-6c-1-3-4-10-7-11-1-1-2 0-3 0-1 2-1 4-2 7v14l2 20c1 8 2 15 1 23 0 9-1 20-7 27-4 5-11 8-17 8-5 0-9-1-12-4-3-2-4-6-4-9s1-6 3-8c1-1 2-2 3-2h0 0c-2 2-3 3-4 5v1c-1 2 0 5 1 7 2 4 5 7 9 7 4 1 8 0 12-2 4-4 7-9 8-14 1-6 0-12-3-17-4-4-9-7-15-8-5 0-10 1-14 5-14 10-16 30-18 46l-4 37c0 4 0 8-1 12l-1-32c0-7 0-15-2-22-2-4-5-8-8-12-6-6-14-10-22-10-6 0-13 3-18 7-1 1-1 2-1 3v1c-1 1-1 1-1 2-3 0-4 0-7 1 0 0 0 1-1 1-2 0-3 1-4 2l1 1h3v1c3-2 7-3 11-3h1v2c-2 0-3 0-4 1s0 2 0 3l3 3 4 2c-1 2-3 4-3 7l-1 2c1 1 1 2 2 3s2 3 4 4h1v1c-1 0-3-1-4-1h0l-2-1-1 1v1c0 1 0 1-1 2l1 1v1h-1c-1 1 0 4-1 6h0v2l-2 1h-1l-1 3 2 3c-1 0-2-1-2-2-1-1-1-2-2-3l-1 1 4 15 4 22v1c-2 0-3 0-5-1-3-3-5-6-7-10l-1-1c0-1-2-2-2-2-1 0-1 0-1-1-1 1-1 2-2 2h-2v1 2 1h0v2l2 5h1v2c1 1 1 2 2 3 1 0 1 0 2 1-1 1-3 3-4 5h2l-1 1c0 1 0 1 1 2v4 6 1l1 8 1 6v2s-1 0-1 1v2h-2l-2 14c-5 18-15 37-27 52l-10 11-6 6c1 0 1 0 2 1l1-1 1 1 3 4 1 2c1 1 2 3 4 4-2 1-4 3-5 5l-8 6 1 1h0l-3 2-1 1 1 2-2 1c-1 0-4 3-4 3v1l-1 3c-1 0-5 3-5 4-2 2-4 3-5 5h0l-1 1c-1 0-1 0-2-1-1 0-1 0-1 1h-1-2c0 2-1 3-1 4-1 0 0 1-1 0h-1l-9-3-1 1c2 0 3 0 5 2h1l1 1-1 1s-1 1-2 1h0c-2 2-5 3-6 5h-1 0c0 1-1 2-1 3h0l-1 1c-4-1-7-1-11-2-1 0-2 1-3 1l-3-1-7-2c-1 0-3-1-3-1l-2-2-5-4v-2l2 1c-4-4-6-7-8-12-3-6-3-15-2-22-1-3-1-5-1-8l1-24v-22c0-1-1-2 0-4 1 1 2 1 2 2 1 0 2 0 3-1-2-1-3-2-4-3h-1v-2-1-1c-1-5-1-11-1-16v-30-1c2-1 9-1 12-1h-11c-1-1-1-1 0-2h1c3 0 5-2 8-4-4 1-7 2-9 4v-11-2c2-1 3-4 4-6 3-5 4-12 5-18v-4-6l-1-3v-2l-1-7-2-4h2c-1-2-2-4-4-6l-1-2c0 1 0 2-1 3l-1-1h-1c0-1-1-1-1-2v-2h0v-1l2-1-4-4-5-2c-1 1-2 1-3 1 0-1-1-2-1-3s0-1-1-1v-1c0-1 1-3 1-5v-1c1-2 2-3 2-5 1-5 1-10 1-16 0-5 0-10-2-16 0-2 1-5 0-6v-3-3c-1-3-2-4-3-6-3-3-5-6-9-9-1-2-3-3-4-4h-2 1v-2h0 3c-1-1-2-2-3-2l-4-2-18-5-1 1h0-1-2c0-1-1-1-1-2h1c0-1-1-1-1-1-1 0-1 0-2-1h1c-3-2-6-2-9-3l2-1 28 6c16 4 31 7 47 9 5 0 10 1 15 1l29 2c4 1 8 0 12 0 5 0 9 1 13 0 4 0 9 0 13-1h1 1l-2-17-1-12v-5l-1-12v-5l-1-9c0-2 0-3-1-5-1-1-1-3-2-5v-2-1h0l1-2h1c-1-1-1-3-1-4h0 2l1 2 2 2h0c1 1 1 2 2 3h0c0 2 0 4 1 5 1 0 1-1 2-1l4-2 3-1c-1-2-1-2 0-4h-1-1c1-1 1-3 1-5h-1c2-2 4-4 7-6h2l3-3-1-1 2-2c1 0 2-2 3-2-1 0-1 0-2 1l-1-1c1-1 2-1 3-3l9-9c1-1 2-3 4-4l2-2c0-1-1-2-1-2l3-5h1v2l1-1v1l2 1h0 0c0 2-1 4-2 6l-1 2c0 1 1 1 1 1h2c1 1 1 2 3 3h0 1v3c1-1 2-2 4-2l1-1 3-3h1l2-7 1 1v-1h1l1 1-1 5c0 2 0 5 2 7s8 2 12 2l4-1v1c0 1-1 1-2 1l-1 1v1l2 1h1 2c2 2 4 3 6 4l1-1h4v-1h9 1l2 1h2l1 1c1 0 2 1 3 1l1-2h1 3c1 0 1-1 1-1 0-1-1-1-1-2h2l6-3v2h1v1l1-1c1-2 3-1 4-3v-1l-1-1 1-1 1 1h0c1 2 1 3 1 4 1 1 1 1 2 1v-1c1-1 3-1 5-1v-1c2 0 3 1 4 1h4c2-1 4-3 4-5 1-1 1-1 1-2l1 1h0 0c1-1 1 0 1-1l-5-15z"></path><path d="M467 386h0 1 0c0 1-1 1-1 2h0l-1 1c0-1 1-2 1-3z" class="G"></path><path d="M572 303h1l1 1c0 1-1 1-1 2-1 0-2 0-2-1h0c0-1 0-1 1-2z" class="c"></path><path d="M534 321h-1-1v-3l1-1c0 2 1 3 1 4z" class="B"></path><path d="M458 501v-1l1 1h0v2 2s0 1-1 1h0c-1-2-1-3-1-5h1z" class="Y"></path><path d="M594 239h1c1 0 1 0 2 1v1h0-3 0l-1-1 1-1z" class="a"></path><path d="M569 301h2l1 1c-1 0-2 0-3 1 1 1 1 1 1 2v1h-1l1-1c0-1-1-2-2-2 0-1 0-1 1-2z" class="B"></path><path d="M556 215h3c1 0 3 0 5 1-2 0-4-1-6 0l-2 2v-3z" class="c"></path><path d="M483 388s1 0 2-1c1 1 1 2 2 3h0c-2 0-2 0-3 1l-1-3z" class="j"></path><path d="M572 308h1 0 1c0 2 0 2-1 4l-1 1c0-1 0-2-1-3v-1l1-1z" class="F"></path><path d="M449 378c0 3 1 5 2 7v2c-1-1-1-2-1-3h-1v1h0c0-2 0-2-1-4 0-1 0-1-1-1l2-2z" class="J"></path><path d="M527 314h0-1c-1-1-1-1-1-2l1-1h1 1 1c0 1-1 2-2 3z" class="b"></path><path d="M481 384c1 0 2 0 3 1h0l1 2h0c-1 1-2 1-2 1l-2-4z" class="X"></path><path d="M528 321v1c1 1 1 2 1 3v1h-2-1v1h0-1s0-1 1-2h0c1 0 1-1 1-2v-1l1-1z" class="I"></path><path d="M541 266c1-2 2-4 4-6 0 2-1 6-3 7v-1h-1z" class="a"></path><path d="M484 391c1-1 1-1 3-1 0 2 1 3 2 5-1 0-2-1-2-1h-1l-2-3z" class="Ai"></path><path d="M458 468c-1-1-3-2-5-3v-2c1 0 1 1 2 1h0c2 0 2 1 3 2v2z" class="B"></path><path d="M452 470l6 6c-1 1-1 1-2 1v-1c-1-2-3-4-5-5l1-1z" class="P"></path><path d="M527 277c1 0 1 0 1 1v-1 2h0v2 1h-2c-1-1-1-1-1-2l2-3z" class="C"></path><path d="M480 365h0c-2 0-3 1-4 1h0l4-5 1 1c-1 1-1 2-1 3z" class="I"></path><path d="M477 368h2 0 0v1c-1 0-4 3-4 4 0 0-1 0-1 1h-1v-1h-1l5-5z" class="G"></path><path d="M596 203h0 0c1-1 1 0 1-1l4 11h0c-1-2-2-4-2-5s-1-1-1-1v1c1 1 1 2 1 3l-3-8z" class="W"></path><path d="M582 293h1l1-1v1c0 1 0 2 1 2v1h-1l1 1v-1l1 1c-1 0-1 0-2 1v-1h-1-1v-1c0-1-1-1-1-2l1-1z" class="I"></path><path d="M555 215h1v3c-2 5-1 11-1 16-1-4 0-9-1-13v-4l1-2z" class="P"></path><path d="M530 261h2c-1 1-2 2-2 3h-1l-1 1h-1l-1-1c0-1 1-1 1-2h1c1-1 1-1 2-1z" class="F"></path><path d="M528 322c1-1 1 0 1-1v-1 1l1 1h-1v1l1-1v1c1 0 1 1 1 1v2l-2 2h-2v-1h1 1c-1-1-1-1-2-1h2v-1c0-1 0-2-1-3z" class="B"></path><path d="M504 330h1c1 1 1 2 1 3-1 2-1 2-2 3h-1l-1-1c0-1 1-2 1-3 1-1 1-1 1-2z" class="I"></path><path d="M537 308l2-3c0-1 0-3-1-5-1 0-1 0 0-1h0c1 0 1 0 1 1 2 1 1 4 1 6h0c-1 2-1 4-2 4h-1v-2z" class="B"></path><path d="M428 508l2-2h1 0-1c-1 0-2 1-3 1 0 1-1 1-2 2v-1h1c1-2 2-3 4-3 0 0 0-1 1-1h-2l-1-1h4l2 2c-1 1-1 1-2 1 0 0-1 0-1 1-1 0-2 1-3 1z" class="U"></path><path d="M480 361l2-1 1 1h3v1l-6 3c0-1 0-2 1-3l-1-1z" class="B"></path><path d="M480 382c-2-2-2-5-2-8 2 2 3 4 5 7h0c-1 1-1 1-2 1h-1z" class="L"></path><path d="M582 279c1 0 1 1 2 2 0 2 1 3 1 6v5-1c-1 0-1-1-2-1v-2h1 0 1l-1-1h0-1v-2-1-1l-1-1h1s0 1 1 1v-2l-2-2z" class="c"></path><path d="M531 312h0l2 2h1c0 1 0 1-1 1 0 1-1 0-1 2-1 1-1 2-3 3v1c0 1 0 0-1 1v-1c0-3 2-3 3-6v-3z" class="b"></path><path d="M451 371c0 1 1 2 0 4l-1 1c-1 0-2 0-3 1h-1v-2h1c1-1 2-3 4-4z" class="AA"></path><path d="M432 503l2-1c3 0 5-1 7-2l1 1-1 1-2 1c-1 0-4 1-5 2l-2-2z" class="Z"></path><path d="M576 256c1 0 2 0 2 1 2 1 2 1 2 2l-1 1c0-1-1-1-1-1-2 1-2 2-3 3l-3 3v-1-1c1-1 2-2 2-3 1-1 1-1 1-3l1-1z" class="B"></path><path d="M463 440h-1l-3-8c2 2 3 4 5 7 0 1 1 2 1 2l1 3v1h0v1h-1v-2h-1l-1-4z" class="T"></path><path d="M516 339h-11c2-2 3-3 5-4l4 3h0l2 1z" class="W"></path><path d="M576 315h1c2 1 5 0 7 0 2 1 3 2 5 2v1h-1l-2-1h-4-1c-1-1-2-1-3-1-2 0-3 2-5 1v-1l1-1h2z" class="B"></path><path d="M529 266c1-1 1-2 2-2 1-1 1-1 1 0h2c0-1 0-1 1-1 1 1 1 1 2 1v1c1 0 1 1 2 1h1 1 1v1l-1 1c-1 0-2 1-2 0-1 0-1 0-1-1h-1c-1 0-2-1-3-1 0-1-1-1-1-2h-2c-1 1-1 1-1 2h-1z" class="c"></path><path d="M529 266h1 0c0 2-4 5-5 6 0 1-1 3-2 4-1 0-1 1-1 1l-1-1c1-2 2-5 4-6l4-4z" class="H"></path><path d="M569 218c2 0 3 0 4 1l1 1h1c0 2 1 4 2 6l1 1v4c-2-5-5-9-9-13z" class="b"></path><path d="M486 394h1s1 1 2 1l2 8 2 3c-1 0-2-1-2-2-1-1-1-2-2-3l-1 1-2-8z" class="Aa"></path><path d="M535 329h0 1v2c1 1 2 2 3 4h0v1 2l-1-1h0l-1-1c-1-1-4-3-4-5 0-1 1-2 2-2z" class="I"></path><path d="M618 268h0c0-1-1-1-1-2v-1c-1-1-1-2-1-3l-1-1v-1c0-1-1-1-1-2v-1l-1-1v-1-1h0v-1c0-1 0-1-1-2h0v-2s-1 0-1-1v-1c0-1 0 0-1-1v-3c-1-2-1-3-1-4s-1-1-1-1v-1c-1-1 0-2-1-3v2-1c-1-2-1-2 0-3l11 34v2z" class="AC"></path><path d="M444 499c2-1 4-1 5-1 1 1 2 1 2 2v2h-3v1h-1c-1 0-3 0-4-1v-1h0c1 1 1 1 2 1l-1-3z" class="c"></path><path d="M536 214h9 1l2 1h2c1 3 1 4 1 7-1-2-3-4-5-5h0c-4-2-7-2-10-2v-1z" class="W"></path><path d="M441 500c1-1 2-1 3-1l1 3c-1 0-1 0-2-1h0l-1 1-1 1c-1 1-2 0-3 1 0 0-1 0-2 1h-1c-1 1-1 1-2 1h0l-1 1s-1 0-1 1h-1l-2 1c-1 1-2 1-3 2l-1 1h-1l1 1h0v1l-2-2c1-1 2-1 3-2l1-1c1-1 2-1 2-1 1 0 2-1 3-1 0-1 1-1 1-1 1 0 1 0 2-1s4-2 5-2l2-1 1-1-1-1z" class="D"></path><path d="M525 280c0 1 0 1 1 2h2-1l-1 2-2 2h-2l-1 1v-1h-1 0c-1 1-1 2-2 3 0 0-1 1-2 3h0v4h0v1c-1 1-1 1-1 2h0v1-1-5-1c1-4 5-8 8-10l1-2 1-1z" class="b"></path><path d="M525 280c0 1 0 1 1 2h2-1c0 1-1 1-1 1l-1 1c-1-1-1-1-1-3l1-1z" class="U"></path><path d="M455 464c0-2-1-3-1-5h0v-2h1 0v-1l4 5-3-8c-1-1-1-2-1-3h0l5 11c-1 0-3-1-4 0 0 2 2 3 2 5-1-1-1-2-3-2z" class="c"></path><path d="M430 491l1-1 1-1c3 1 7-3 10 0v3h-1c0-1 0-2-1-3h-3-1l1 1h0-1l-1-1-1 1h-1c-1 0-1 0-2 1h0 2l-1 1h0c-4 2-6 5-9 7 0-1 0 0-1-1h-1 0s1 0 1-1c2-1 3-2 4-3s3-2 4-3z" class="B"></path><path d="M530 278h0c2 2 3 4 4 6v1c0 1 1 1 1 2h0 0l1-1v-1c0 1 1 1 2 2s-1-1 1 1c0 0 0 1 1 1v3 1c-1-1-2-1-3-2v-1c-1 0-1-1-2-1v1 2h-1c1-3 0-7-2-9h0c0-1-1-2-1-3h0c0-1-1-1-1-2z" class="c"></path><path d="M524 237l1-1c-1 7-3 14-6 20 0-2 0-4 1-7l4-12z" class="H"></path><path d="M474 393v-2-1l-1-2c0-1 1-2 1-2v1h1c1 1 1 1 1 2h0c0 1 1 1 1 2v1l1 1v1l1 1v-2s-1 0-1-1 0-2-1-3v-1l-1-1h1 0v1l1-1 3 2v1h0v1h1v1h0v1c-1 0-1-1-1-2l-3-3c1 2 1 5 2 7h-1c-2-1-2-3-4-5 0 2 2 3 1 5h-1c0-1 0-1-1-2h0 0z" class="J"></path><path d="M579 216h1c0 1 1 2 1 4 1 0 1 1 1 2v1c1 2 2 5 2 7 1 2 2 4 2 6-1 0-1-2-2-3l-2-5c-1-2-1-5-2-7-1-1-1-2-1-3v-2z" class="I"></path><path d="M505 325c1 0 3-1 4-2h0l1 2h0c1 0 2 2 2 3 0 2-1 3-3 5-2-3-3-5-4-8z" class="N"></path><path d="M485 387c1-1 2-1 3-2l1 1c1 1 1 2 1 2 0 1 0 3 1 3h1l-1 2h-2c1 1 1 2 1 2 1 1 1 2 1 3l2 2h-1l-1 3-2-8c-1-2-2-3-2-5h0c-1-1-1-2-2-3h0z" class="i"></path><path d="M488 389h0v-1h2c0 1 0 3 1 3v1h-1-1c0-1 0-2-1-3z" class="K"></path><path d="M485 387c1-1 2-1 3-2l1 1c1 1 1 2 1 2h-2v1h0c0 1 0 2 1 3h0 0l-2-2h0c-1-1-1-2-2-3h0z" class="v"></path><path d="M514 338c0-2 1-3 1-4h1 0c1 2 1 3 2 4h1c1-1 1-2 2-2 1-1 2-1 3-1 2 2 2 4 3 7-2-1-6-3-8-3h-3l-2-1h0z" class="F"></path><path d="M496 389l1 1v1h-1c-1 1 0 4-1 6h0v2l-2 1-2-2c0-1 0-2-1-3 0 0 0-1-1-2h2l1-2v-2c1 0 2 0 2 1h1 0l1-1z" class="v"></path><path d="M492 389c1 0 2 0 2 1v2l-1 1v6c-1-2-1-4-2-6l1-2v-2z" class="AG"></path><path d="M496 389l1 1v1h-1c-1 1 0 4-1 6h-1c0-2-1-3 0-5v-2h1 0l1-1z" class="q"></path><path d="M520 304h2c-1 1-1 1-2 1l-3 6c0 1-1 2-2 3h0v1 2h0c1 0 1 1 1 1v1l1-1c-1-1-1-1-1-2l-1-1c1 0 1 1 2 1v1c1 1 1 1 2 1v1l1-1v1c-1 1-1 3-2 4h-1s-1 1-2 1v-1c0-3-1-4-2-6v-1c-1-1-1-1-1-2v-1l2 1v-2l1 1h0c1-1 1-2 2-3v-2h0c1-1 2-2 3-4z" class="b"></path><path d="M533 289v5h-2c-1 1-4 3-4 5v2h0v1c-1 1-1 2-1 3v1h0c0 1-1 1-1 2 0-2 0-2 1-3v-2-1-1h-2c-1 0-1 1-1 2l-1 1h-2v-2l3-2c1-3 3-4 5-6s3-4 5-5z" class="C"></path><path d="M523 300c1-3 3-4 5-6l1 1c-1 1-2 1-2 2-1 0-1 1-1 2v1h-3z" class="R"></path><path d="M575 276h1c-1 1-2 2-4 3 1 1 2 2 2 4 1 1 1 1 1 2v1c1 0 0 1 0 1l1 1v1 1l1 2v1 1 1l1 1v1 1h0-1c-1-1-2-2-2-3v-1c0-1 0-1-1-2v-2h0c-1-1-1-1-1-2v-3l-1-1v-1h0c0-1 0-1-1-1v-1-1l-1-1h0c1 0 1 0 2-1 1 0 2-1 3-2z" class="H"></path><path d="M533 294v3c1 1 3 2 3 2v3c1 2 0 5 1 6v2h1c1 0 1-2 2-4h0c0 1 0 2-1 3v1c0 1 0 1-1 1s-1 0-1-1v2l-1 1h0l-1 1h1 1c0 1 0 1-1 3 0 1 0 1-1 2h0v1 1c0 2 1 2 0 4-1 1-2 2-3 2h0l3-3h0c-1-1-1 0-1-1h1c0-1 0-1-1-2 0-1-1-2-1-4l-1-1v1c0-2 1-1 1-2 1 0 1 0 1-1l1-1c1-1 0-2 0-3 1-1 1-2 1-3-1-2 0-3 0-4l-2-2h0c-1 0-1 0-1-1 0 0 0-1 1-1h0v-1c-1-1-2-2-2-3h0l-1-1h2z" class="F"></path><path d="M417 507c3-1 5-3 8-5v1 1l-3 3c-1 0-1 0-2 1h0-1l1 1c0 1-1 2 0 3 0 1 1 1 1 2h0-2c-1 0-1-1-2-2-1 1-1 1-3 1h0-1v-1-1l1-1h0 1l1 1h0c1 0 2 1 3 2h0v-2h0v-2h-3-1c0 1-1 0-2 0s-3 1-4 0h0c1-1 2-1 3-2h5z" class="b"></path><path d="M412 507h5l-1 1h-1v1h-3v-2z" class="H"></path><path d="M417 507c3-1 5-3 8-5v1c-1 2-3 3-4 4-2 1-3 1-5 1l1-1z" class="C"></path><path d="M469 444c1-1 2-2 3-4h0 1v-1c-1-2-2-4-3-7-1-2-3-4-4-6 1 1 2 3 3 4l6 7h0v-1c1 3 2 8 3 12-1 0-3-3-4-4v1l-2-2-2 2v-1h-1z" class="h"></path><path d="M586 209h4c2-1 4-3 4-5 1-1 1-1 1-2l1 1 3 8h0c0 1 0 3 1 4h-1l-2-2c-1 0-1-1-2-1h0c-1-1-1-1-2-1l-3-1h0-3 0 0 2c-1-1-2-1-3-1z" class="J"></path><path d="M460 461l1 2c2 1 3 2 5 3v1h1l1 1c-1 1-2 2-3 2h0l-2 1c-1 0-2 0-3-1l1-2h-3v-2c0-2-2-3-2-5 1-1 3 0 4 0z" class="E"></path><path d="M461 468c1 0 1 0 1-1v-1h0c1 0 2 1 3 1h1 1l1 1c-1 1-2 2-3 2h0l-2 1c-1 0-2 0-3-1l1-2z" class="w"></path><path d="M608 229l10 35c0 1 1 1 0 2l-11-34v-1h1v-2z" class="Z"></path><path d="M468 407c1 0 0 1 0 1 0 1 0 2-1 4v-1c-1-1-1-3 0-4l1-5c1-1 1-1 1-2s1-1 1-2c1 0 1-1 1-1 0-1 0-1 1-2 0-1 1-1 1-2h0 1 0l-1 2c2 1 3 1 3 3 0 1 0 1-1 1l-1-1c0 1-1 2-1 3-1 3-3 7-4 10v3l-1 1c-1-1-2-2-2-3s-1-2-1-2c-1-1-1-2-1-2 1 0 1 0 1 1s0 1 1 1v1h0c1 1 1 2 2 2v-1l1-1v-3h0s0-1 1-1v-1-1c1-1 1-2 1-3l1-1v-1c0-1 1-3 2-3s1 1 1 1h0 1l-1-1h-1v-1h-1c-1 1-1 1-1 2s-1 1-1 2 0 2-1 3v1h0c-1 1-1 2-2 3z" class="AA"></path><path d="M468 407v-1c0-2 2-6 3-7v1c0 1 0 2-1 3v1h0c-1 1-1 2-2 3z" class="J"></path><path d="M529 311c0-1 0-1 1-1 0 1 0 1 1 2h0v3c-1 3-3 3-3 6l-1 1v1l-4 4c-1 2-2 4-4 4-1-1-2-2-2-3 0-2 2-4 4-5l1-1v-2l-1-1h1c0-1 0-1 1-1v2 2l-3 3c-1 2-2 2-2 3s1 1 1 2c1 0 1-1 1-1l1-1h0c1-2 2-3 4-4v-1h-1 0s0-1 1-1l1 1v-1c-1 0-2-1-2-1v-1s0 1 1 1h1v-1-1l1-1v-3-1c1-1 2-2 2-3z" class="G"></path><path d="M465 441v1c1 0 1 1 1 2l1 1h1v-1h0c-1 0-1-1-1-1-1-1 0-1-1-1 1-2 0-2 0-3s-1-1-1-2l-1-2v-1c-1 0-1-1-1-1v-1h0 0c1 0 2 1 2 2s1 1 1 1v1l2 3c0 1 0 1 1 2v2 1h1c-1 1-2 1-3 2-1 0-2 2-3 2l-1-1h-1c1 3 2 5 2 8v1c1 1 0 2 0 3h0v-3c-1-1 0-2-1-3v-2l-1-1v3 2-1c0-2-1-5-1-7h0 0v-2h0c-1-1-1-2-1-3-1 0-1-1-1-1 1-1 1-1 2-1s1 2 2 3v3c0 1 1 1 1 1h1c0-1-1-2-1-3-1-1-1 0-1-1v-2-1l1 4h1v2h1v-1h0v-1l-1-3z" class="L"></path><path d="M474 431h2v2l2 5h1v2c1 1 1 2 2 3 1 0 1 0 2 1-1 1-3 3-4 5h2l-1 1c0 1 0 1 1 2v4 6c-1-4-1-8-3-11-1-2-3-4-4-6v-1c1 1 3 4 4 4-1-4-2-9-3-12v-2l-1-3z" class="Ag"></path><path d="M478 438h1v2c1 1 1 2 2 3 0 1-1 1-1 2v1h-1v-1-1c0-2-1-4-1-6z" class="K"></path><path d="M592 187h0l1 1h0l1-1 3 10c1 1 1 3 2 4l9 28v2h-1c-2-6-3-13-6-18l-4-11-5-15z" class="d"></path><path d="M454 492c1-3 1-5 2-8l1 1h0c0 1 0 1 1 2v2 6c0 2-1 4 0 6h-1-1v4h0-1c-1-1-2-1-4-1h-1c0-1 1-1 2 0h3c-1-1-1 0-1-1h-2-1v-1-2c0-1-1-1-2-2l2-1h0c1 1 1 1 1 2v1c1-1 1-2 2-3l-1-2 1-3z" class="D"></path><path d="M454 492v5l-1-2 1-3z" class="b"></path><path d="M451 500l3 3h-2-1v-1-2z" class="G"></path><path d="M457 485h0c0 1 0 1 1 2v2 6c0 2-1 4 0 6h-1-1l1-16z" class="J"></path><defs><linearGradient id="AJ" x1="582.69" y1="245.05" x2="586.879" y2="243.777" xlink:href="#B"><stop offset="0" stop-color="#3e3c3b"></stop><stop offset="1" stop-color="#5b5854"></stop></linearGradient></defs><path fill="url(#AJ)" d="M578 227c1 2 3 5 4 7 3 5 4 10 6 15 3 6 5 12 6 17h0 0c0-1-1-1-1-2v-1-1l-1-1h0-1l1 1c-1 0-1 1 0 1 1 2 1 4 1 6l1 1v1c0 1 0 1 1 2l-1 1-6-20c-2-4-3-7-4-11l-5-10-1-2v-4z"></path><defs><linearGradient id="AK" x1="496.526" y1="281.624" x2="524.226" y2="263.731" xlink:href="#B"><stop offset="0" stop-color="#3a3338"></stop><stop offset="1" stop-color="#5d5b53"></stop></linearGradient></defs><path fill="url(#AK)" d="M517 253l1-4h2c-1 3-1 5-1 7l-7 17-5 11c-1 3-2 8-5 10h0v-1c1-2 1-5 2-7l3-7c1-4 3-8 4-11 3-5 4-10 6-15z"></path><path d="M475 434h-1c0-2-1-2-1-3v-1-1h-1v-1l-1-1v-1c-1-1 0-1-1-2h-1 0l-3-3c0-1-1-1-1-2v-1l-1-1-1-1v-1c0-1-1-1-1-2v-1-4-1h1 1v1s0 1 1 2c0 0 1 1 1 2s1 2 2 3h0c1 0 1 1 2 1 0 1 1 3 2 4l1 1 1 1s1 0 1 1v1h0 0v2l1 1v1 2 1h0-2l1 3z" class="t"></path><path d="M466 412c0 1 1 2 2 3h0-1v2c0-1-1-1-1-2-1-1-1-2 0-3z" class="Q"></path><path d="M472 427l1-1c1-1 1 0 2 0l1 1v1 2 1h0-2l-2-4z" class="j"></path><path d="M470 421h1 2l1 1s1 0 1 1v1h0 0v2c-1 0-1-1-2 0l-1 1-4-6h2z" class="g"></path><path d="M474 422s1 0 1 1v1h-1v1h-1l-1-2c1 0 1-1 2-1z" class="i"></path><path d="M470 421h1 2l1 1c-1 0-1 1-2 1l-2-2z" class="T"></path><path d="M468 415c1 0 1 1 2 1 0 1 1 3 2 4l1 1h-2-1-2c-1-1-2-1-2-3l1-1v-2h1z" class="v"></path><path d="M553 270v2-2h2v3h0v13l-2 32c0 1 0 6-1 7 0-2 1-6 0-8v1c-1 1-1 5-1 7v-6-12l2-37z" class="O"></path><path d="M550 215l1 1c1 0 2 1 3 1v4c1 4 0 9 1 13v39-3h-2v2-2-19c0-10 0-20-2-29 0-3 0-4-1-7z" class="m"></path><defs><linearGradient id="AL" x1="458.679" y1="424.314" x2="438.853" y2="436.942" xlink:href="#B"><stop offset="0" stop-color="#484a3e"></stop><stop offset="1" stop-color="#9d867d"></stop></linearGradient></defs><path fill="url(#AL)" d="M451 412c1 1 2 1 2 2-5 14-6 27-6 42h-1c0-2 0-2-1-3v-2-2c-1 1-1 2-2 2v-1l1-1c2-3-1-13-2-17 1-1 1-2 1-4h0v-2c1 1 1 1 2 1v4 1c1-6 3-13 6-18v-2z"></path><path d="M445 427v4 1 19-2c-1 1-1 2-2 2v-1l1-1c2-3-1-13-2-17 1-1 1-2 1-4h0v-2c1 1 1 1 2 1z" class="B"></path><path d="M445 427v4l-1 1c-1-1-1-3-1-4h0v-2c1 1 1 1 2 1z" class="D"></path><path d="M505 325c0-1 0-1 1-2v-1l-2-2v-7l1-1c1-1 1-2 2-3l2-2c0-1 1-2 1-3 0 1 0 2 1 3h1v1 1c0 1-1 3-1 4-1 1-1 3-1 4s1 1 1 1h0l1 1v1c0 1 1 2 1 3h0 2v1c1 0 2-1 2-1l-1 2c0 1 1 1 0 2l-1 1h-2c-1-1-1-2-2-3h1v-1h-1-1v1h0l-1-2h0c-1 1-3 2-4 2z" class="W"></path><path d="M511 318l1 1v1 3l-1 1-1-1c1-1 0-1 0-3h0c0-1 1-1 1-2zm2 5h2v1c1 0 2-1 2-1l-1 2v1c-1 1-1 1-2 1l-2-1c0-2 0-2 1-3z" class="B"></path><path d="M510 320h0c-1 0-1-1-2-1 0-1-1-1-1-2-1-2 0-4 1-6h1v-1h0c0-1 0 0 1-1 0 0 0-1 1-1h1v1c0 1-1 3-1 4-1 1-1 3-1 4s1 1 1 1h0c0 1-1 1-1 2z" class="F"></path><path d="M451 496c-1-4-1-9 0-13v-1h-1c1-1 1-1 1-2h1l1-1-2-2s0-1-1-1v-1c-1 0-1-1 0-1v-2h0v-3h0 0l2 1-1 1c2 1 4 3 5 5v1c1 0 1 0 2-1l1 1c0 1-1 3-2 4v4h0l-1-1c-1 3-1 5-2 8l-1 3 1 2c-1 1-1 2-2 3v-1c0-1 0-1-1-2h0v-1z" class="I"></path><path d="M458 476l1 1c0 1-1 3-2 4v4h0l-1-1c-1 3-1 5-2 8l-1 3 1 2c-1 1-1 2-2 3v-1c0-1 0-1-1-2h0v-1-5c1-2 1-4 2-6 0-1 1-2 2-3h0l1-2-1-1c1-1 1-2 1-3v1c1 0 1 0 2-1z" class="B"></path><path d="M458 476l1 1c0 1-1 3-2 4l-1 1h0v-2-3c1 0 1 0 2-1z" class="E"></path><path d="M456 482l1-1v4h0l-1-1c-1 3-1 5-2 8l-1 3h-1c0-5 3-9 4-13z" class="G"></path><path d="M483 381l1 1v-2c0-1 0-1 1-1l2 2c0 1 2 3 3 4 0-1 0-2-1-3 0-1 0-1-1-2-1-2-2-4-4-5h0c2 1 3 2 5 4 0 0 1 1 1 2v2c1 1 0 2 1 3v1h0v1 1-2c1 0 1-1 1-1v-1-2h0v-2l-1-1c0-1 0-1-1-2v-2l-1-2v-1h0c-1-1-1-2-1-3-1-1-1-2-1-3l1-1v1 1l1 2v1s0 1 1 1v1l1 2v2l1 1v1c1 1 1 2 1 2s0-1 1-1l1-1v-1c-1 0-1 0-1-1l-1-1h0c0-2-2-4-3-5v-3-1-1h0s0 1 1 1v1 1 1l2 3c0 1 0 1 1 2 1 2 1 4 3 6v3h0c1 0 2 1 3 2l-2-1-1 1v1c0 1 0 1-1 2l-1 1h0-1c0-1-1-1-2-1v2h-1c-1 0-1-2-1-3 0 0 0-1-1-2l-1-1c-1 1-2 1-3 2l-1-2h0c-1-1-2-1-3-1h0l-1-2h1c1 0 1 0 2-1h0z" class="h"></path><path d="M483 381c0 2 1 3 1 4-1-1-2-1-3-1h0l-1-2h1c1 0 1 0 2-1z" class="T"></path><path d="M484 385l2 1v-1c0-2-2-4-2-6l5 7-1-1c-1 1-2 1-3 2l-1-2z" class="L"></path><path d="M492 389c1-1 1-4 1-6 1-1 1-3 2-4l1 6 1 1v1c0 1 0 1-1 2l-1 1h0-1c0-1-1-1-2-1z" class="z"></path><path d="M496 385l1 1v1c0 1 0 1-1 2l-1 1h0c0-2 1-3 1-5z" class="J"></path><path d="M461 383v-1h1c0 5 0 10-1 15v9c-1 1-2 2-3 2s-2 0-2-1h-1c0-3 0-7-1-11 0-2-1-5 0-6l1 1 2 1v2 1h1 0v-2h0l-1-1c1-1 0-6 1-7v-1s1 0 1-1h1 1z" class="b"></path><path d="M458 384s1 0 1-1h1 1c0 1 0 3-1 4-1-1-1-2-2-3z" class="G"></path><path d="M457 398l2 1v-6c0-1-1-2 0-3v-1c1 1 1 1 1 2 1 1 1 1 1 2v4 9c-1 1-2 2-3 2s-2 0-2-1h1 2c0-3-1-6-2-9z" class="AJ"></path><path d="M454 396c0-2-1-5 0-6l1 1 2 7c1 3 2 6 2 9h-2-1-1c0-3 0-7-1-11z" class="u"></path><path d="M424 490h0c0 2-1 3-2 4s-1 1-1 3h1 0v-1c2-1 2-2 4-3l1-1h1v-1h2 0c-1 1-3 2-4 3s-2 2-4 3c0 1-1 1-1 1h0 1c1 1 1 0 1 1 3-2 5-5 9-7v1c0 1-1 1-2 2-1 0-3 2-3 3v1l-1 1s-1 1-1 2h0c-3 2-5 4-8 5h-5c-1 1-2 1-3 2h0c1 1 3 0 4 0 0 1-1 1-2 1h0-3-3c2-2 5-4 6-6l1-1 2-2c1-1 2-3 3-4l1-2 2-2 1 1 3-4z" class="W"></path><path d="M421 498h1c1 1 1 0 1 1v1c-1 1-2 1-2 1l-1-1 1-2z" class="H"></path><path d="M418 495l2-2 1 1-2 4c-1-1-2-1-2-1l1-2z" class="Z"></path><path d="M417 497s1 0 2 1c-1 2-4 4-6 5h-1l2-2c1-1 2-3 3-4z" class="U"></path><path d="M527 277c1-1 1-1 1-2v1l1 1c1 1 2 3 2 4h0v3c0 1 0 1 1 1v2 1s0 1 1 1c-2 1-3 3-5 5s-4 3-5 6l-3 2v2c-1 2-2 3-3 4h0v2c-1 1-1 2-2 3h0l-1-1v2l-2-1v1c0 1 0 1 1 2v1c1 2 2 3 2 6h-2 0c0-1-1-2-1-3v-1l-1-1h0s-1 0-1-1 0-3 1-4c0-1 1-3 1-4v-1-1h-1c-1-1-1-2-1-3l1-1h1v-3l1-3h1c0 1-1 1-1 2-1 1-1 2 0 4 1-2 3-3 5-5v-1l1-1c1 0 3-2 4-3l2-2c1 0 2-1 2-1 1-1 2-1 4-1l-1 1h0c-1 0-2 0-2 1-1 0-1 0-2 1 0 0-1 0-1 1h-1l-3 3-3 3c1 0 1 1 2 1h0v1h1l1-1c0-1 1-1 1-2l4-4c2-1 3-3 5-5h-1 0v-2h0v-2c-1-1-1-2-1-3s0-2-1-2v-1c0-1-1-1-1-1 0-1 0-1-1-1z" class="I"></path><path d="M510 304l1-1h1v-3 3c1 1 1 1 1 2 0 2-1 3-1 4v-1-1h-1c-1-1-1-2-1-3z" class="b"></path><path d="M512 303c1 1 1 1 1 2h0c-1 0-1 0-2-1l1-1z" class="H"></path><path d="M514 312c1-1 2-3 2-5v-1c1-1 2-3 4-4v2c-1 2-2 3-3 4h0v2c-1 1-1 2-2 3h0l-1-1z" class="R"></path><defs><linearGradient id="AM" x1="434.043" y1="465.417" x2="441.324" y2="468.755" xlink:href="#B"><stop offset="0" stop-color="#2b2a2b"></stop><stop offset="1" stop-color="#4e4b49"></stop></linearGradient></defs><path fill="url(#AM)" d="M443 445c1 1 0 3 0 5v1c1 0 1-1 2-2v2 2c1 1 1 1 1 3h1c0 4 0 8-1 11v2c-3 2-7 4-10 7l-5 6-2-1-2 2h0-1 0l3-4 3-6h2c2-2 5-4 7-7h0c3-6 2-14 2-21z"></path><path d="M429 481l6-6 1 1-5 6-2-1z" class="x"></path><path d="M445 453c1 1 1 1 1 3h1c0 4 0 8-1 11v2c-3 2-7 4-10 7l-1-1 4-4c2-1 4-3 5-5s1-3 1-5v-8z" class="p"></path><path d="M445 453c1 1 1 1 1 3h1c0 4 0 8-1 11 0-2 1-7 0-8-1 0-1 1-1 2v-8z" class="y"></path><defs><linearGradient id="AN" x1="579.861" y1="216.399" x2="577.095" y2="210.698" xlink:href="#B"><stop offset="0" stop-color="#4e4a49"></stop><stop offset="1" stop-color="#6a615a"></stop></linearGradient></defs><path fill="url(#AN)" d="M573 205l1 1h0c1 2 1 3 1 4 1 1 1 1 2 1v-1c1-1 3-1 5-1v-1c2 0 3 1 4 1s2 0 3 1h-2 0c-3 1-7 2-9 5-1 1-2 3-3 5h-1l-1-1c-1-1-2-1-4-1-1-1-3-1-5-2h0c-2-1-4-1-5-1 1 0 1-1 1-1 0-1-1-1-1-2h2l6-3v2h1v1l1-1c1-2 3-1 4-3v-1l-1-1 1-1z"></path><path d="M573 205l1 1h0c1 2 1 3 1 4 1 1 1 1 2 1l-1 1h-1c-2 1-1 3-4 4h-2c1 0 1-1 2-1h0l1-1-3-3c1-2 3-1 4-3v-1l-1-1 1-1z" class="M"></path><path d="M561 212l6-3v2h1v1l1-1 3 3-1 1h0c-1 0-1 1-2 1h0-3-2 0c-2-1-4-1-5-1 1 0 1-1 1-1 0-1-1-1-1-2h2z" class="AJ"></path><path d="M568 211v1c-1 1-2 1-3 2v-1c1-2 1-1 3-2z" class="M"></path><path d="M569 211l3 3-1 1h0c-1 0-1 1-2 1h0c-1-1-3-1-4-1h-2c1-1 1-1 2-1 1-1 2-1 3-2l1-1z" class="k"></path><path d="M417 517c1 0 1-1 2-1 0 0 1-1 2-1h0c1 0 2 0 3-1h1 2 0 1c1-1 0-1 1-1 2 0 4-1 5-2 2 0 2-1 3-1 1 1 0 1 1 1h0 2 0l-1 1h-1l-4 4c2 0 3 1 5 2l2 2 1 1c0-1 1-5 0-6 0 0 0-1-1-1l1-1 1 1c1 1 0 6 0 8l1 1c1 0 1 1 1 2h-1c-1 0-1-1-2-1 0 0 0 1 1 1v3h1l3-6 1-2s1-1 1-2h-1c1-2 0-2 0-3l-2-2c0-1 1-1 1-2h4c-1 2-3 1-4 2 1 0 1 1 2 1l-1 1 1 1h1l1-3h0l1-1 1-1c0-1 1-1 1-2h0l1 1c-3 3-4 7-7 10-1 3-3 6-4 9h0c-2 6-2 12-2 18h0l-2 1h0-1c-1-2-1-4-1-6v-1c1-1 1-3 1-4h0l-1-5v-1-1c0-2 0-5-2-6h-1c0-1-1-3-2-5-2-2-3-2-6-2 2-1 2-1 3-2h1v-1c-4 0-10 1-14 3h0z" class="N"></path><path d="M431 515l5-3c0 1-2 2-2 3h-1l-2 1-1-1h1z" class="R"></path><path d="M439 537v-1 1c1-1 1-1 1-2h0c0 2 0 4 1 5 1 2 0 5 1 7l-2 1h0-1c-1-2-1-4-1-6v-1c1-1 1-3 1-4z" class="E"></path><path d="M438 542l2 1h1v2c-1 1-1 2-1 3h0-1c-1-2-1-4-1-6z" class="P"></path><path d="M440 543v1h-1v-1h1z" class="M"></path><path d="M501 286v6c-1 10-7 20-13 28-6 10-13 19-20 28l-5 7c-1 1-2 3-3 4v-2-1l8-13h0l20-27c0-2 2-3 3-5 3-4 7-10 6-15l-1-1c2-3 4-6 5-9z" class="K"></path><path d="M501 279c1 2 0 4 0 7-1 3-3 6-5 9l1 1c1 5-3 11-6 15-1 2-3 3-3 5l-20 27h0c1-4 2-9 3-13s1-9 4-12l-2 1 7-8-1-1-3 3 1-3h-1c1 0 1-1 1-2h-1 0v-2c4-2 8-4 12-7l2-2s0-1 1-2h0-2c4-2 6-5 9-8s3-5 3-8z" class="AB"></path><path d="M496 295l1 1c1 5-3 11-6 15-1 2-3 3-3 5h-1l-2-2c1 0 1 0 1-1h0 2l4-4v-1l1-1c0-1 1-2 1-2 1-1 1-2 2-3v-1-1c1-1 0-1 0-2 1 0 1-1 1-1h-1c0 1-1 2-2 3-1 2-3 3-4 5-1 1-2 2-2 3l-2 1c-1 1-2 3-4 3h0l10-12c2-2 3-3 4-5z" class="Q"></path><path d="M501 279c1 2 0 4 0 7-1 3-3 6-5 9-1 2-2 3-4 5l-10 12c-2 2-4 3-5 5 0 0-1 1-2 1l-2 1 7-8-1-1-3 3 1-3h-1c1 0 1-1 1-2h-1 0v-2c4-2 8-4 12-7l2-2s0-1 1-2h0-2c4-2 6-5 9-8s3-5 3-8z" class="Ah"></path><path d="M501 279c1 2 0 4 0 7-1 3-3 6-5 9-1 2-2 3-4 5 0-4 3-6 4-9-2 2-4 5-6 6 0 0 0-1 1-2h0-2c4-2 6-5 9-8s3-5 3-8z" class="AW"></path><path d="M476 306c4-2 8-4 12-7 0 2-1 2-2 4l1 1c0 1-3 4-4 5h-1l-2 2-1-1-3 3 1-3h-1c1 0 1-1 1-2h-1 0v-2z" class="AX"></path><path d="M476 308l6-3c1-1 1-1 2 0-2 2-4 4-7 5h-1c1 0 1-1 1-2h-1 0z" class="AW"></path><path d="M486 303l1 1c0 1-3 4-4 5h-1l-2 2-1-1-3 3 1-3c3-1 5-3 7-5l2-2z" class="AH"></path><path d="M474 393h0c1 1 1 1 1 2h1c1-2-1-3-1-5 2 2 2 4 4 5h1c-1-2-1-5-2-7l3 3c0 1 0 2 1 2v1 1c2 3 4 7 5 11 2 4 3 8 5 12h0v-1l4 22v1c-2 0-3 0-5-1-3-3-5-6-7-10l-1-1c0-1-2-2-2-2-1 0-1 0-1-1-1 1-1 2-2 2h-2l-1-1v-2h0 0v-1c0-1-1-1-1-1l-1-1-1-1c-1-1-2-3-2-4-1 0-1-1-2-1h0l1-1v-3c1-3 3-7 4-10 0-1 1-2 1-3l1 1c1 0 1 0 1-1 0-2-1-2-3-3l1-2z" class="q"></path><path d="M470 416c3 1 4 4 6 6h-1v1c0-1-1-1-1-1l-1-1-1-1c-1-1-2-3-2-4z" class="Y"></path><path d="M475 423v-1h1l3 1c0 1 0 1 1 1h-1l1 1 1 1c-1 0-1 0-1-1-1 1-1 2-2 2h-2l-1-1v-2h0 0v-1z" class="X"></path><path d="M475 424c2 1 3 1 5 1-1 1-1 2-2 2h-2l-1-1v-2z" class="h"></path><path d="M478 406v-4c-1-2-1-3-1-5h0l-2-1c2 0 3 0 4 1 0 1 1 2 2 3v-3l-1-1c0-1 0-2 1-3l1 1v1h0l-1 1h0c0 2 1 4 1 6l4 15c1 2 1 4 1 6h-1 0c0 2-1 2-2 2 0 1 1 3 2 4h-2l-1-1c0-1-2-2-2-2l-1-1-1-1h1c-1 0-1 0-1-1s0-2-1-3-3-2-4-2l2-1c1 0 1-1 2-1l1-1c0-2 0-7-1-9z" class="T"></path><path d="M483 415l1 1v2s0 1 1 1h-1l-1 1-1-1c0-1 1-2 1-4z" class="e"></path><path d="M479 419c1 2 2 3 3 3l-2 2c-1 0-1 0-1-1s0-2-1-3l1-1z" class="Q"></path><path d="M482 422c1 0 1-1 2-1h1l-2 2h-1c1 1 1 2 2 2 0 1 1 3 2 4h-2l-1-1c0-1-2-2-2-2l-1-1-1-1h1l2-2z" class="g"></path><path d="M478 406v-2l1-1s1-1 1-2h0c2 2 3 8 3 11 0 1-1 2-1 3-1 1-1 2-2 3l-1 1-1 1c-1-1-3-2-4-2l2-1c1 0 1-1 2-1l1-1c0-2 0-7-1-9z" class="e"></path><path d="M479 415v-5h1c0 1 0 2 1 3s1 1 1 2c-1 1-1 2-2 3l-1 1-1 1c-1-1-3-2-4-2l2-1c1 0 1-1 2-1l1-1z" class="K"></path><defs><linearGradient id="AO" x1="493.765" y1="416.106" x2="482.122" y2="416.674" xlink:href="#B"><stop offset="0" stop-color="#441412"></stop><stop offset="1" stop-color="#761f1d"></stop></linearGradient></defs><path fill="url(#AO)" d="M487 423c0-2 0-4-1-6l-4-15c0-2-1-4-1-6h0l1-1h0c2 3 4 7 5 11 2 4 3 8 5 12h0v-1l4 22v1c-2 0-3 0-5-1-3-3-5-6-7-10h2c-1-1-2-3-2-4 1 0 2 0 2-2h0 1z"></path><path d="M484 425c1 0 2 0 2-2h0 1c1 3 1 7 3 10v2h0c-2-2-3-4-4-6-1-1-2-3-2-4z" class="L"></path><path d="M486 429c1 2 2 4 4 6 1 2 4 3 5 5l1-1v1c-2 0-3 0-5-1-3-3-5-6-7-10h2z" class="S"></path><defs><linearGradient id="AP" x1="441.362" y1="255.757" x2="490.037" y2="262.819" xlink:href="#B"><stop offset="0" stop-color="#1b191b"></stop><stop offset="1" stop-color="#403d3d"></stop></linearGradient></defs><path fill="url(#AP)" d="M484 188h1v2l1-1v1l2 1h0 0c0 2-1 4-2 6l-1 2c0 1 1 1 1 1h2c1 1 1 2 3 3h0 1v3c1-1 2-2 4-2l1-1 3-3h1l2-7 1 1v-1h1l1 1-1 5c0 2 0 5 2 7s8 2 12 2l4-1v1c0 1-1 1-2 1l-1 1v1l2 1h1 2c2 2 4 3 6 4-2 3-2 6-3 10s-2 7-3 10l-1 1-4 12h-2l-1 4c-2 5-3 10-6 15-1 3-3 7-4 11l-3 7c-1 2-1 5-2 7l-1-1v-6c0-3 1-5 0-7 0 3 0 5-3 8l-1-1c-1 1-2 2-3 2-3 5-8 7-13 10-2 1-4 2-6 2-5 2-11 4-16 5l-16 2-2-17-1-12v-5l-1-12v-5l-1-9c0-2 0-3-1-5-1-1-1-3-2-5v-2-1h0l1-2h1c-1-1-1-3-1-4h0 2l1 2 2 2h0c1 1 1 2 2 3h0c0 2 0 4 1 5 1 0 1-1 2-1l4-2 3-1c-1-2-1-2 0-4h-1-1c1-1 1-3 1-5h-1c2-2 4-4 7-6h2l3-3-1-1 2-2c1 0 2-2 3-2-1 0-1 0-2 1l-1-1c1-1 2-1 3-3l9-9c1-1 2-3 4-4l2-2c0-1-1-2-1-2l3-5z"></path><path d="M463 245c2-3 4-4 7-5l5-3c-1 1-3 4-5 5h0c-2 0-4 1-5 2l-1 2-1-1z" class="R"></path><path d="M463 245l1 1c-2 3-4 5-6 7h-1c0 1-1 1-1 2l-4 2c3-4 7-8 11-12z" class="O"></path><path d="M443 245v24h0-1c0-2 0-3-1-4h-1c-1 2 0 8 1 11 0 0 0 1-1 2v-5l-1-12 1 2h1 0v-5h1v-9h1v-4z" class="a"></path><path d="M442 249h1l-1 8v1-9z" class="M"></path><path d="M470 255h1c0 2 0 3-2 4v1s0 1-1 1c0 2-1 3-2 4l-4 6h0l3-6-8 9 6-11c2-2 3-4 4-5l3-3z" class="C"></path><path d="M485 282l1 1h2l1 1h2c1 1 1 1 2 1v3c-2 0-4-3-7-3 0 2-1 4-1 5l1 1h1s1 0 1 1h1 0l1-1c1 0 1-1 2-2 1 0 1-1 2-1h0 0c-3 5-8 7-13 10-2 1-4 2-6 2h-1l2-1c1 0 2-1 3-1l1-1h1c1 0 1 0 1-1h1c1-1 1-1 2-1 0 0 0-1 1-1-1-1 0-1-1-1-1-1-4-3-4-4h1l-1-1h0 1c1 1 1 2 2 2v-1l-2-2v-1c0-1 1-1 1-1h1l1-1v-2z" class="B"></path><path d="M455 262l1-1v-1h1 1v-1h2v-1h1s0 1 0 0l2-1c1 0 2 0 2-1h1l1-1s1-1 2-1c5-4 6-12 10-17 1-1 2-3 3-5 0-1 0-2 1-2v-1l1-1v-1h0c1-1 1-1 1-2v-1c1 0 1-1 1-1 0-1 1-1 1-1 0 2-2 5-3 7h1c-1 3-3 5-4 8l-11 18-3 3c-1 1-2 3-4 5 0-1 0-1-1-1 0 0-3 4-4 4v-1c-1 0-1 0-2-1v-1l-1-1z" class="I"></path><path d="M455 262c1 0 1 1 2 2h0 1l1-1c1-3 6-5 8-5-1 1-2 3-4 5 0-1 0-1-1-1 0 0-3 4-4 4v-1c-1 0-1 0-2-1v-1l-1-1z" class="F"></path><path d="M437 232c-1-1-1-3-1-4h0 2l1 2 2 2h0c1 1 1 2 2 3h0c0 2 0 4 1 5-1 2-1 4-1 5v4h-1v9h-1v5h0-1l-1-2v-5l-1-9c0-2 0-3-1-5-1-1-1-3-2-5v-2-1h0l1-2h1z" class="I"></path><path d="M441 232c1 1 1 2 2 3h0c0 2 0 4 1 5-1 2-1 4-1 5v4h-1c0-4 0-8-1-12v-5z" class="k"></path><path d="M435 235v-1h0l1-2h1c1 3 2 7 1 11v4c0-2 0-3-1-5-1-1-1-3-2-5v-2z" class="b"></path><path d="M484 188h1v2l1-1v1l2 1h0 0c0 2-1 4-2 6l-1 2c0 1 1 1 1 1h2c1 1 1 2 3 3h0 1v3l2 3c0 2-1 4-2 5-2 6-4 11-7 15h-1c1-2 3-5 3-7v-3c0-1 1-3-1-5l-1-1h0c-1-1-4 0-5 0s-3 1-4 2c-3 2-6 4-10 6 0 1-2 3-3 3 0-1 1-2 2-2 0 0 0-1 1-1l4-3c0-1 0 0-1-1v-1s-1 0-1-1c-1 1-1 1-2 1l-6 6c-3 2-5 4-5 8 0 1-1 3 0 5 0 1-1 1-2 1l-2 1h-1l3-1c-1-2-1-2 0-4h-1-1c1-1 1-3 1-5h-1c2-2 4-4 7-6h2l3-3-1-1 2-2c1 0 2-2 3-2-1 0-1 0-2 1l-1-1c1-1 2-1 3-3l9-9c1-1 2-3 4-4l2-2c0-1-1-2-1-2l3-5z" class="W"></path><path d="M453 232h0l1-2c0 1 0 3 1 5-1 0-1 0-2 1-1-2-1-2 0-4z" class="E"></path><path d="M472 209c1-1 2-2 4-3 0 1 0 1 1 1l-4 3-1-1z" class="p"></path><path d="M467 213l1 1-5 4-1-1 2-2c1 0 2-2 3-2h0z" class="s"></path><path d="M467 213l5-4 1 1-5 4-1-1z" class="AK"></path><path d="M458 221h2c-3 3-5 5-6 9l-1 2h0-1-1c1-1 1-3 1-5h-1c2-2 4-4 7-6z" class="C"></path><path d="M484 188h1v2l1-1v1l2 1h0 0c0 2-1 4-2 6l-1 2c0 1 1 1 1 1h2c1 1 1 2 3 3h0 1v3l2 3c0 2-1 4-2 5l-1-2c0-2-1-3-2-5-2-2-4-2-7-2-2 0-3 1-5 2-1 0-1 0-1-1-2 1-3 2-4 3l-5 4h0c-1 0-1 0-2 1l-1-1c1-1 2-1 3-3l9-9c1-1 2-3 4-4l2-2c0-1-1-2-1-2l3-5z" class="x"></path><path d="M482 202h0c1 0 2 1 3 1v1c-1 1-2 0-3 1-2 0-3 1-5 2-1 0-1 0-1-1l6-4z" class="AZ"></path><path d="M484 188h1v2l1-1v1l2 1h0l-2 2c-2 1-4 3-6 4h0l2-2c0-1-1-2-1-2l3-5z" class="u"></path><path d="M484 188h1v2c-1 2-2 3-3 5 0-1-1-2-1-2l3-5z" class="D"></path><path d="M488 191h0c0 2-1 4-2 6l-1 2c0 1 1 1 1 1h2c1 1 1 2 3 3h0 1v3l2 3c0 2-1 4-2 5l-1-2c0-2-1-3-2-5-2-2-4-2-7-2 1-1 2 0 3-1v-1c-1 0-2-1-3-1h0l-1-1h0c2-2 4-5 5-8l2-2z" class="C"></path><path d="M482 202l2-1c2 0 4 3 6 5 0 0 0 1-1 1-2-2-4-2-7-2 1-1 2 0 3-1v-1c-1 0-2-1-3-1z" class="k"></path><defs><linearGradient id="AQ" x1="515.068" y1="210.209" x2="475.998" y2="291.882" xlink:href="#B"><stop offset="0" stop-color="#121014"></stop><stop offset="1" stop-color="#484645"></stop></linearGradient></defs><path fill="url(#AQ)" d="M504 194v-1h1l1 1-1 5c0 2 0 5 2 7s8 2 12 2l4-1v1c0 1-1 1-2 1l-1 1v1l2 1h1 2c2 2 4 3 6 4-2 3-2 6-3 10s-2 7-3 10l-1 1-4 12h-2l-1 4c-2 5-3 10-6 15-1 3-3 7-4 11l-3 7c-1 2-1 5-2 7l-1-1v-6c0-3 1-5 0-7 0 3 0 5-3 8l-1-1c-1 1-2 2-3 2h0 0c-1 0-1 1-2 1-1 1-1 2-2 2l-1 1h0-1c0-1-1-1-1-1h-1l-1-1c0-1 1-3 1-5 3 0 5 3 7 3v-3c-1 0-1 0-2-1h-2l-1-1h-2l-1-1c0-1-1-1-1-2l-3-3-1 1v-1c-3 0-6 3-9 4v-1h0c0-1 0-1 1-1l2-2c2-1 3-3 4-3h0c0-1 1-2 2-2l2-1v-1c1 0 1-1 1-3l1-1 1-5c0-2-6-7-7-9-1 2-1 3-2 4v1l-1 1-2 4-2 3-2 2-2 3c0 1 0 1-1 2l-1 1c-1 2-2 3-3 5-1 0 0 0-1 1-1 0-1 1-2 2 0 0 0 1-1 2 0-1 1-2 1-3 3-5 6-9 9-14l7-12-1-1-4 7c0 2-1 4-3 6 0-1 0-1 1-2 2-2 3-6 4-8 0-1 0 0 1-1 0 0-1-1-1-2-1 1-1 1-1 2h-1l11-18c1-3 3-5 4-8 3-4 5-9 7-15 1-1 2-3 2-5l-2-3c1-1 2-2 4-2l1-1 3-3h1l2-7 1 1z"></path><path d="M481 277c2 0 3-1 5-2-1 1-2 3-2 5h0l-3-3h0zm13-11h1c-1 2-1 2-3 3l-1-1h1c1-2 1-2 2-2z" class="H"></path><path d="M481 277h-1v-1l2-1c2-1 4-2 6-2 0 1-1 1-2 2-2 1-3 2-5 2z" class="M"></path><path d="M509 259c0-1 2-5 2-6-1-1-1-1-1-2 1 0 1 2 2 3l2-2v2h-2l-2 6h-1v-1z" class="J"></path><path d="M495 249h0c3 0 3-1 4-2l3-3v1l-3 5c-2 0-4 1-6 1l2-2z" class="R"></path><path d="M490 255c0-1 1-2 1-3-1-1-1-1-2-1v-1c2-1 4-1 6-1l-2 2h-1c0 2 0 4-1 6v-1l-1-1z" class="Z"></path><path d="M490 255l1 1v1c-3 4-3 10-5 15v-3c1-5 1-9 4-14z" class="m"></path><path d="M497 286l1-2c1-2 2-4 1-6 0-1-1-2-2-2-1-1-1 0-1-1h1c2 1 3 2 4 3v1c0 3 0 5-3 8l-1-1z" class="V"></path><defs><linearGradient id="AR" x1="526.924" y1="213.071" x2="526.205" y2="230.299" xlink:href="#B"><stop offset="0" stop-color="#333233"></stop><stop offset="1" stop-color="#5f5853"></stop></linearGradient></defs><path fill="url(#AR)" d="M523 212h2c2 2 4 3 6 4-2 3-2 6-3 10s-2 7-3 10l-1 1-4 12h-2c3-8 5-15 7-23 0-3 2-7 0-10-1-2-3-3-6-4h3 1z"></path><path d="M524 237c0-2 0-4 1-6 1-1 1-2 1-3v-1l2-1c-1 4-2 7-3 10l-1 1z" class="C"></path><path d="M514 254c1 3-5 12-4 16h0c0-1 0-2 1-2 1-2 2-4 2-7h0c1-1 2-3 2-4v-1c1-1 0-1 1-1v-1-2h0l1 1c-2 5-3 10-6 15-1 3-3 7-4 11l-3 7c-1 2-1 5-2 7l-1-1v-6c0-3 1-5 0-7v-1h0v-1c1-4 3-6 4-10l4-8v1h1l2-6h2z" class="I"></path><path d="M504 194v-1h1l1 1-1 5c0 2 0 5 2 7s8 2 12 2l4-1v1c0 1-1 1-2 1l-1 1v1l2 1h-3-7l-9 3c-4 2-6 3-9 6-4 4-6 8-9 12l-8 15c-1 1-2 3-3 5l-4 7c0 2-1 4-3 6 0-1 0-1 1-2 2-2 3-6 4-8 0-1 0 0 1-1 0 0-1-1-1-2-1 1-1 1-1 2h-1l11-18c1-3 3-5 4-8 3-4 5-9 7-15 1-1 2-3 2-5l-2-3c1-1 2-2 4-2l1-1 3-3h1l2-7 1 1z" class="d"></path><path d="M503 215c1-2 2-3 5-3h0c1 1 2 0 3 0h1l-9 3z" class="O"></path><path d="M504 194v-1h1l1 1-1 5c0 1 0 2-1 2h-1v-5c1 0 1-1 1-2h0z" class="s"></path><path d="M496 204c0 1 0 2 1 2 0 1-2 3-3 3l-2-3c1-1 2-2 4-2z" class="V"></path><path d="M497 203l3-3h1c-1 3-2 5-4 6-1 0-1-1-1-2l1-1z" class="AH"></path><path d="M436 425h3c1-1 1-1 1-2 0 1 1 3 1 5l1 4c1 4 4 14 2 17l-1 1c0-2 1-4 0-5 0 7 1 15-2 21h0c-2 3-5 5-7 7h-2l-3 6-3 4h0 1 0l2-2 2 1c-2 2-6 5-7 8l-3 4-1-1-2 2-1 2c-1 1-2 3-3 4l-2 2-1 1c-1 2-4 4-6 6 0 1-1 2-1 3s0 1-1 2h0c0 1-1 1-1 2h6v1l1-1h3 2c-1 1-2 1-2 1-1 0-1 1-2 1 2 0 4-1 6-2-2-1-3-1-5-1h3 0 1c1 0 1 0 1 1h1 0c4-2 10-3 14-3v1h-1c-1 1-1 1-3 2 3 0 4 0 6 2 1 2 2 4 2 5h1c2 1 2 4 2 6v1 1l1 5h0c0 1 0 3-1 4v1c0 2 0 4 1 6h1 0l2-1h0c0-6 0-12 2-18h0c1-3 3-6 4-9 3-3 4-7 7-10l1-2v-2h2 0c1 0 1-1 1-1v-2-2h0l-1-1v1c-1-2 0-4 0-6v-6-2c-1-1-1-1-1-2v-4c1-1 2-3 2-4v-1l1 1v2c1-2 1-4 1-6-1-1-1-2-1-3 1 1 2 1 3 1l2-1h0c1 0 2-1 3-2l-1-1h-1v-1c-2-1-3-2-5-3l1-1c0-1-1-3-1-5-1-7-4-14-7-20l1-1c2 3 4 8 6 11 0 2 1 5 1 7v1-2-3l1 1v2c1 1 0 2 1 3v3h0c0-1 1-2 0-3v-1c0-3-1-5-2-8h1l1 1c1 0 2-2 3-2 1-1 2-1 3-2v1l2-2 2 2c1 2 3 4 4 6 2 3 2 7 3 11v1l1 8 1 6v2s-1 0-1 1v2h-2l-2 14c-5 18-15 37-27 52l-10 11-6 6c1 0 1 0 2 1l1-1 1 1 3 4 1 2c1 1 2 3 4 4-2 1-4 3-5 5l-8 6 1 1h0l-3 2-1 1 1 2-2 1c-1 0-4 3-4 3v1l-1 3c-1 0-5 3-5 4-2 2-4 3-5 5h0l-1 1c-1 0-1 0-2-1-1 0-1 0-1 1h-1-2c0 2-1 3-1 4-1 0 0 1-1 0h-1l-9-3-1 1c2 0 3 0 5 2h1l1 1-1 1s-1 1-2 1h0c-2 2-5 3-6 5h-1 0c0 1-1 2-1 3h0l-1 1c-4-1-7-1-11-2-1 0-2 1-3 1l-3-1-7-2c-1 0-3-1-3-1l-2-2-5-4v-2l2 1c-4-4-6-7-8-12-3-6-3-15-2-22-1-3-1-5-1-8l1-24v-22c0-1-1-2 0-4 1 1 2 1 2 2 1 0 2 0 3-1-2-1-3-2-4-3h-1v-2-1-1c-1-5-1-11-1-16v-30-1c2-1 9-1 12-1h27c2 0 7 1 9 0l2-2c1 0 2-1 3-2 4-5 10-9 13-15 1-2 2-3 3-5 1-3 3-7 4-10l1-6 1-2 1 1c1 0 3-1 4-2v-1c2-1 4-1 7-1 0 0 1 1 2 1z" class="w"></path><path d="M391 554c-2 1-4 2-6 2h0 1c0-1 0 0 1-1h0-1c1-2 2-2 4-3-1 1-1 1-1 2h2z" class="N"></path><path d="M404 573l1 2h0-1-1c-1 1-6 1-8 1h1c-1-1-1-1-1-2h2 6l1-1z" class="c"></path><path d="M384 593c0-1 1-2 3-3v1h2 1l2 2c-2 1-3 1-5 1l-3-1z" class="G"></path><path d="M416 490l1-1v1h0v1l1 4-1 2c-1 1-2 3-3 4 0-1 0-2 1-3h1-2c-1 0-1 0-2-1 1-1 2-1 3-2h1l1-1-1-2h0v-2z" class="F"></path><path d="M410 519c2 0 4-1 6-2-2-1-3-1-5-1h3 0 1c1 0 1 0 1 1h1 0c-3 2-5 5-9 7 1-2 3-3 4-4l-7 1h4v1l-1 1c-1-1-2-1-3-1s-1 0-1-1c1-1 4-1 5-1l1-1z" class="b"></path><path d="M481 463l1 8 1 6v2s-1 0-1 1v2h-2l1-19z" class="X"></path><path d="M401 578h2c2 0 4 1 5 3-1 1-2 2-4 2v1h-3 1v-2c0-1-1-2-1-2v-1h0v-1z" class="L"></path><path d="M398 497l1-1v2-1h1l-1 1 1 2c-1 5-3 10-5 13l2-10c1-2 1-4 1-6z" class="z"></path><path d="M395 574l-2-1c-1 0-2-1-4-2h-4v-1h4-1c-1 0-1 0-2-1h-5v-1h0 1 0 4s0 1 1 1h1 1l1 1c4 2 9 1 12 3-2 0-3 0-5 1h-2zm25-100c3 0 7 0 11-1 0 1-1 1-1 2l-3 1c-3 0-6 1-9 2v1h0v1 2c0 1 0 0-1 1 0 1 0 2 1 3 0 1 1 3 1 4-1 0-1-1-2-1 0-1 0-1-1-2v1 2 2-2h0l-1-1v-1l-1-1c0-1 0-2 1-3 1-2 1-5 2-6l1-1s1 0 2-1h1 2c1-1 2-1 3-1h2c0-1 1 0 1-1h-1c-2 1-5 1-8 0h0z" class="I"></path><path d="M393 503l1-1h1v2h0l1-1h1l-2 10c-2 4-4 8-7 11 3-7 5-13 5-21z" class="AZ"></path><path d="M397 467c1 0 2-1 3-2h0c-1 1-2 3-3 4v3l2 13h-1c-1 1-1 1-2 1v3h0c0-5 0-13-3-17 0 0-1-1-1-2-1 0-4-1-6-1 2 0 7 1 9 0l2-2z" class="z"></path><path d="M390 552l1-1c2-1 4-3 5-5h0c1-1 0-1 1-1 0-2 0-2 1-2v-2-1s0-1 1-1v-2 3c-1 2-1 4-1 7 1 1 1 2 1 3h0s1 2 0 3 0 1-1 2l1 1-1 1c-1 1 0 1-1 1v1c-2 1-3 1-5 1 1-1 1-1 1-3h0l1-1c0-1 0-1 1-2l-3 3h0-1v-1l3-3h1s1-1 1-2l-5 3h-2c0-1 0-1 1-2z" class="F"></path><path d="M396 550c-1 1-3 2-4 3l2-3c1-1 1-2 2-3s1-2 2-3v2 1c1 1 1 2 1 3h0s1 2 0 3 0 1-1 2v-1c0-1 0-1-1-2v-2-1l-1 1z" class="U"></path><path d="M399 550s1 2 0 3 0 1-1 2v-1c0-1 0-3 1-4z" class="E"></path><path d="M396 550l1-1v1 2c1 1 1 1 1 2v1l1 1-1 1c-1 1 0 1-1 1v1c-2 1-3 1-5 1 1-1 1-1 1-3h0l1-1c0-1 0-1 1-2s1-2 2-4h-1z" class="b"></path><path d="M393 499h0c0-3-1-6-2-9-2-6-5-8-10-10h0 1c2 0 3 1 4 2h0 1v-1c-2-2-5-1-7-3 2 1 4 1 6 2h1l-3-6h0v-1l1 1c2 3 3 6 5 8 1 2 3 5 3 7 1 1 1 2 1 3 1 2 0 3 1 4h0c0-2 1-5 1-7h0v-3c1 0 1 0 2-1h1v1 5c0 2-1 4-1 6s0 4-1 6h-1l-1 1h0v-2h-1l-1 1v-4z" class="l"></path><path d="M396 489v-3c1 0 1 0 2-1h1v1 5c0 2-1 4-1 6s0 4-1 6h-1l-1 1h0v-2h-1l-1 1v-4l1-1h0v1c1 1 1 2 1 3h1v-7-6z" class="j"></path><path d="M396 489v-3c1 0 1 0 2-1h1v1 5l-2-1v1c0 1 0 3-1 4v-6z" class="g"></path><defs><linearGradient id="AS" x1="400.267" y1="469.77" x2="414.365" y2="484.55" xlink:href="#B"><stop offset="0" stop-color="#3d0b08"></stop><stop offset="1" stop-color="#6d1516"></stop></linearGradient></defs><path fill="url(#AS)" d="M400 491c4-13 10-26 15-38l1 1c-1 7-5 14-7 21l-9 25-1-2 1-1h-1v1-2l-1 1c0-2 1-4 1-6v-5c1 1 0 4 1 5z"></path><path d="M399 486c1 1 0 4 1 5 0 2-1 4-1 6v1-2l-1 1c0-2 1-4 1-6v-5z" class="K"></path><defs><linearGradient id="AT" x1="417.955" y1="480.881" x2="426.342" y2="484.306" xlink:href="#B"><stop offset="0" stop-color="#2f2c2d"></stop><stop offset="1" stop-color="#4a4746"></stop></linearGradient></defs><path fill="url(#AT)" d="M427 476l3-1h0c0 1-1 3-1 4l-3 4h0 1 0l2-2 2 1c-2 2-6 5-7 8l-3 4-1-1-2 2-1-4v-1h0v-1l-1 1v-2-1c1 1 1 1 1 2 1 0 1 1 2 1 0-1-1-3-1-4-1-1-1-2-1-3 1-1 1 0 1-1v-2-1h0v-1c3-1 6-2 9-2z"></path><path d="M426 483h0 1 0l-3 4-1 2h0v-2s0 1-1 1l4-5z" class="N"></path><path d="M429 481l2 1c-2 2-6 5-7 8l-3 4-1-1 3-4 1-2 3-4 2-2z" class="d"></path><path d="M418 479h0v-1c3-1 6-2 9-2h0v1c-1 0-1 0-1 1h1 0l-2 3v-1h-1c-1 0-3 1-4 1h0-1 0c1-1 1-1 2-1v-1h-3z" class="b"></path><path d="M399 553c1 0 1 1 2 1v-1c0 1 1 2 1 3l-1 1c1 1 1 2 1 3s1 1 1 2v1 1c1 3 1 5 2 8 1 0 0 1 1 2 1 0 1 2 1 3l-2-2h0l-1-2-1 1h-6c2-1 3-1 5-1-3-2-8-1-12-3l-1-1h-1-1c-1 0-1-1-1-1h-4 0l5-2c1-1 2-1 3-2h0c1-1 3-1 4-2 0-1 1-1 1-1 0-1-3 0-4 0h-1c1-1 1-1 1-2l1-1c1 0 1-1 1-1h0c0 2 0 2-1 3 2 0 3 0 5-1v-1c1 0 0 0 1-1l1-1-1-1c1-1 0-1 1-2z" class="N"></path><path d="M399 553c1 0 1 1 2 1v-1c0 1 1 2 1 3l-1 1c1 1 1 2 1 3s1 1 1 2v1 1c1 3 1 5 2 8 1 0 0 1 1 2 1 0 1 2 1 3l-2-2h0l-1-2-1 1c-1-1-2-2-2-3l-1-2-1-3h0c0-1 1-1 1-1h1 0-1v-1h-1c-1-2-1-3-1-4v-3l1-1-1-1c1-1 0-1 1-2z" class="H"></path><path d="M401 565l1 3-1 1v2l-1-2v-3l1-1z" class="D"></path><path d="M399 553c1 0 1 1 2 1v-1c0 1 1 2 1 3l-1 1c1 1 1 2 1 3v1l-2-3c0 1-1 1-2 2v-3l1-1-1-1c1-1 0-1 1-2z" class="P"></path><path d="M399 556c0 1 1 1 1 2s-1 1-2 2v-3l1-1z" class="U"></path><path d="M401 565v-4c1 0 1 0 2 1v1 1c1 3 1 5 2 8 1 0 0 1 1 2 1 0 1 2 1 3l-2-2h0l-1-2-1 1c-1-1-2-2-2-3v-2l1-1-1-3z" class="G"></path><path d="M401 571v-2l1-1c0 2 1 3 2 5l-1 1c-1-1-2-2-2-3z" class="C"></path><path d="M469 447h1c9 16 8 34 3 51-4 16-12 31-22 44v-1h0v-1-1c2-6 6-11 9-16l3-6c3-5 7-12 7-19 3-13 5-24 3-37-1-5-3-9-4-14z" class="X"></path><path d="M432 551c3-2 4-3 6-6h0v4h0c1 0 1 0 2-1h0l2-1h0v4c0 2-2 4-4 6-8 9-18 16-28 23h-1l1-1c0-1 0-2-1-3 0 0 0 1-1 2l-1-1c0-1 0-3-1-3-1-1 0-2-1-2l1-1c0-1 2-2 4-3l3-2c2-1 3-2 5-3l10-9 2-1h1c0-1 1-2 1-2z" class="K"></path><path d="M442 547h0v4l-1-1h-2l-1-1h0c1 0 1 0 2-1h0l2-1z" class="i"></path><path d="M406 571c0-1 2-2 4-3v2l3-1h0l-6 3 1 1c1 0 1 1 1 2l1-1c0-1 0-2 1-2 0-1 1-2 2-2h1c-2 2-4 3-4 6v2 1c0-1 0-2-1-3 0 0 0 1-1 2l-1-1c0-1 0-3-1-3-1-1 0-2-1-2l1-1z" class="h"></path><path d="M406 571c0-1 2-2 4-3v2l-4 2v-1z" class="g"></path><path d="M432 551c3-2 4-3 6-6h0v4c0 1-1 2-2 2l-3 3c-3 0-6 4-8 6h0c1-3 4-4 6-6l-1-1h1c0-1 1-2 1-2z" class="AG"></path><path d="M430 553l1 1c-2 2-5 3-6 6-3 2-12 8-12 9l-3 1v-2l3-2c2-1 3-2 5-3l10-9 2-1z" class="f"></path><defs><linearGradient id="AU" x1="424.389" y1="577.57" x2="423.274" y2="556.576" xlink:href="#B"><stop offset="0" stop-color="#551313"></stop><stop offset="1" stop-color="#80221e"></stop></linearGradient></defs><path fill="url(#AU)" d="M414 570c1 0 1 0 2-1h2s0-1 1-1l2-1c3-1 6-3 8-5 1-1 2-3 3-3 1-1 2-2 4-3 1 0 1 0 2 1-8 9-18 16-28 23h-1l1-1v-1-2c0-3 2-4 4-6z"></path><path d="M435 524h1c2 1 2 4 2 6v1 1l1 5h0c0 1 0 3-1 4v1c0 2 0 4 1 6h1c-1 1-1 1-2 1h0v-4h0c-2 3-3 4-6 6 0 0-1 1-1 2h-1l-2 1-10 9c-2 1-3 2-5 3l-3 2c-2 1-4 2-4 3l-1 1c-1-3-1-5-2-8v-1-1c0-1-1-1-1-2s0-2-1-3l1-1 5-2c7-6 14-11 21-18 3-3 6-6 7-12z" class="T"></path><path d="M434 534l3-4 1 1v1c-1 1-1 2-2 3l-2-1z" class="g"></path><path d="M434 534l2 1c-2 3-4 5-6 7-1 0-1 1-1 1l-1-1c-1 1-1 0-1 0l7-8z" class="X"></path><path d="M427 542s0 1 1 0l1 1c-3 2-5 4-7 6-3 2-6 5-9 7s-6 3-9 6v-1c7-7 16-12 23-19z" class="f"></path><path d="M438 532l1 5h0c0 1 0 3-1 4v1c0 2 0 4 1 6h1c-1 1-1 1-2 1h0v-4h0c-2 3-3 4-6 6 0 0-1 1-1 2h-1l-2 1c0-1 0-1-1-1l-1-1h0c-1-1-3-1-3-3h-1c2-2 4-4 7-6 0 0 0-1 1-1 2-2 4-4 6-7 1-1 1-2 2-3z" class="L"></path><path d="M438 541v1c0 2 0 4 1 6h1c-1 1-1 1-2 1h0v-4h0c-2 3-3 4-6 6 0 0-1 1-1 2h-1l-2 1c0-1 0-1-1-1l-1-1h0c-1-1-3-1-3-3 1 0 2 1 3 2 2-1 3-2 4-3 1-2 2-2 4-3 1-2 2-3 4-4z" class="K"></path><path d="M429 551l2 2h-1l-2 1c0-1 0-1-1-1l-1-1h1c1 0 2 0 2-1z" class="X"></path><path d="M429 551h0c1-2 3-2 4-3h1l-2 2v1s-1 1-1 2l-2-2z" class="Y"></path><path d="M422 549h1c0 2 2 2 3 3h0l1 1c1 0 1 0 1 1l-10 9c-2 1-3 2-5 3l-3 2c-2 1-4 2-4 3l-1 1c-1-3-1-5-2-8v-1l1-1c3-3 6-4 9-6s6-5 9-7z" class="v"></path><path d="M427 553c1 0 1 0 1 1l-10 9c-2 1-3 2-5 3l2-2 1-1s1-1 2-1c1-1 1-2 2-2 1-1 2-1 2-2h-1c1-2 4-4 6-5z" class="e"></path><defs><linearGradient id="AV" x1="410.646" y1="554.672" x2="416.824" y2="560.174" xlink:href="#B"><stop offset="0" stop-color="#1c0607"></stop><stop offset="1" stop-color="#320d0d"></stop></linearGradient></defs><path fill="url(#AV)" d="M422 549h1c0 2 2 2 3 3-1 0-1 1-2 1-2 0-4 3-7 4-1 0-2 2-4 3-2 2-5 4-7 6l-3-2v-1l1-1c3-3 6-4 9-6s6-5 9-7z"></path><path d="M417 517c4-2 10-3 14-3v1h-1c-1 1-1 1-3 2 3 0 4 0 6 2 1 2 2 4 2 5-1 6-4 9-7 12-7 7-14 12-21 18l-5 2c0-1-1-2-1-3v1c-1 0-1-1-2-1 1-1 0-3 0-3h0c0-1 0-2-1-3 0-3 0-5 1-7v-3c1-2 2-4 3-5v-1h0l2-2h0l1-1c0-1 1-1 1-2 1-1 2-1 2-2 4-2 6-5 9-7z" class="V"></path><path d="M417 517c4-2 10-3 14-3v1h-1c-1 1-1 1-3 2-3 1-9 2-10 6-1 2-1 6-1 8 1 2 3 3 4 3 2 1 6 1 8 0h0c0 1-3 3-3 4-2 1-4 2-6 1-3 0-5-1-6-3l-3-4-7 16-2 5v1c-1 0-1-1-2-1 1-1 0-3 0-3h0c0-1 0-2-1-3 0-3 0-5 1-7v-3c1-2 2-4 3-5v-1h0l2-2h0l1-1c0-1 1-1 1-2 1-1 2-1 2-2 4-2 6-5 9-7z" class="t"></path><path d="M399 550c1 0 1 0 1 1h0 1v-1h0l1-1v-1h1l-2 5v1c-1 0-1-1-2-1 1-1 0-3 0-3h0z" class="L"></path><path d="M435 565c1 0 1 0 2 1l1-1 1 1 3 4 1 2c1 1 2 3 4 4-2 1-4 3-5 5l-8 6 1 1h0l-3 2-1 1 1 2-2 1c-1 0-4 3-4 3v1l-1 3c-1 0-5 3-5 4-2 2-4 3-5 5h0-3c0-1-1-2-2-3l-2-1-3-3h0l-2-1-2-1-1-1c-2-2-5-5-7-6h0l2-2c2-1 3-1 5-2l14-9 10-6 6-5 5-5z" class="AB"></path><path d="M435 565c1 0 1 0 2 1-2 2-4 4-6 5-1 0-1-1-1-1l5-5z" class="AT"></path><path d="M424 575l6-5s0 1 1 1l-6 5-1-1z" class="AS"></path><path d="M414 581l10-6 1 1-9 7c-1-1-2-1-2-2z" class="AE"></path><path d="M400 590l14-9c0 1 1 1 2 2-2 1-4 2-5 3-4 2-7 3-11 5v-1z" class="g"></path><path d="M413 592c4 0 9-5 13-7v1c-1 1-2 2-3 4l-1 1c-1 1-3 2-4 3s-2 1-3 2l-1-2c-1-1-1-1-1-2z" class="Y"></path><defs><linearGradient id="AW" x1="431.718" y1="576" x2="435.008" y2="585.306" xlink:href="#B"><stop offset="0" stop-color="#691311"></stop><stop offset="1" stop-color="#481012"></stop></linearGradient></defs><path fill="url(#AW)" d="M442 570l1 2v1 2h0a30.44 30.44 0 0 0-8 8c-2 2-5 4-7 5-2 2-3 2-5 3h-1l1-1c1-2 2-3 3-4v-1l3-2c0-1 1-1 1-2l5-4c1-1 3-4 4-4h1c0-1 1-2 2-3z"></path><path d="M429 583l1 1-4 2v-1l3-2z" class="K"></path><path d="M442 570l1 2v1h-2l-3 4h-3c1-1 3-4 4-4h1c0-1 1-2 2-3zm-39 28c3-1 6-3 9-5h0c-1-1-1-1-1-2 1 0 1 0 2 1 0 1 0 1 1 2l1 2-2 2 2 1-3 3 1 1c-2 1-3 2-5 3l-3-3h0l-2-1-2-1-1-1c1-1 2-1 3-2z" class="i"></path><path d="M400 600c1-1 2-1 3-2v2s-1 0-2 1l-1-1z" class="K"></path><path d="M414 594l1 2-2 2c-1 1-2 1-3 2h0c-2 1-3 2-5 3l-2-1c1-1 3-2 4-3 3-1 5-2 7-5z" class="J"></path><path d="M413 598l2 1-3 3 1 1c-2 1-3 2-5 3l-3-3h0c2-1 3-2 5-3h0c1-1 2-1 3-2z" class="o"></path><path d="M413 598l2 1-3 3c-1 0-1 1-2 1h-1-1c1-1 2-1 3-2l-1-1h0c1-1 2-1 3-2z" class="v"></path><path d="M443 572c1 1 2 3 4 4-2 1-4 3-5 5l-8 6 1 1h0l-3 2-1 1 1 2-2 1c0-1 0-1-1-1l-3 1h-1 0v-1l-4 2-3 2-3 2-2-1 2-2c1-1 2-1 3-2s3-2 4-3h1c2-1 3-1 5-3 2-1 5-3 7-5a30.44 30.44 0 0 1 8-8h0v-2-1z" class="J"></path><path d="M434 587l1 1h0l-3 2v-2l2-1z" class="Ab"></path><path d="M415 596c1-1 2-1 3-2l-1 2 1 1-3 2-2-1 2-2z" class="h"></path><path d="M432 588v2l-1 1 1 2-2 1c0-1 0-1-1-1l-3 1h-1 0v-1l7-5z" class="AE"></path><path d="M431 591l1 2-2 1c0-1 0-1-1-1l2-2z" class="J"></path><path d="M429 593c1 0 1 0 1 1-1 0-4 3-4 3v1l-1 3c-1 0-5 3-5 4-2 2-4 3-5 5h0-3c0-1-1-2-2-3l-2-1c2-1 3-2 5-3l-1-1 3-3 3-2 3-2 4-2v1h0 1l3-1z" class="t"></path><path d="M426 597v1l-1 3c-1 0-5 3-5 4-2 2-4 3-5 5h0-3c4-5 9-9 14-13z" class="S"></path><path d="M421 595l4-2v1h0 1l-3 3c-3 2-7 4-10 6l-1-1 3-3 3-2 3-2z" class="f"></path><path d="M421 595l4-2v1h0 1l-3 3c-1-1-1 0-1-1h1v-1h-1-1z" class="AD"></path><defs><linearGradient id="AX" x1="415.403" y1="440.571" x2="439.442" y2="464.728" xlink:href="#B"><stop offset="0" stop-color="#1e1c1e"></stop><stop offset="1" stop-color="#3b3939"></stop></linearGradient></defs><path fill="url(#AX)" d="M436 425h3c1-1 1-1 1-2 0 1 1 3 1 5l1 4c1 4 4 14 2 17l-1 1c0-2 1-4 0-5 0 7 1 15-2 21h0c-2 3-5 5-7 7h-2l-3 6c0-1 1-3 1-4h0c0-1 1-1 1-2-4 1-8 1-11 1h-2c0-1 1-3 1-4 0-2 1-3 2-5 0-2 0-3 1-4 0-1 1-3 1-4 0 0 0-1 1-1h0v-2-1c1 0 1-1 1-1l2-2-1-1 1-1c0-1 1-1 1-2l-1-1c-2 1-4 2-5 3 0 1-2 1-2 2l-1 1v-4c1-4 2-7 2-11l-1-1 1-6 1-2 1 1c1 0 3-1 4-2v-1c2-1 4-1 7-1 0 0 1 1 2 1z"></path><path d="M430 445v1c2 4 7 9 5 13h0 0 0c-1-4-3-7-5-11h0v-3z" class="b"></path><path d="M422 432c2 0 3 1 4 3 1 1 2 2 1 3v1h1 0 1v2c0 1 1 3 1 4v3h0l-2 2c-1 2-1 4-3 5v-2c0-1 1-2 2-3l-1-1 1-1c0-1 1-1 1-2l-1-1c-2 1-4 2-5 3 0 1-2 1-2 2l-1 1v-4c1-4 2-7 2-11 0-2 0-2 1-4z" class="N"></path><path d="M428 439h1v2 2c-1-1-1-1-1-2-1-1 0-2 0-2z" class="a"></path><path d="M426 449c1 0 2-1 2-2 1-1 0-1 0-2 1 1 1 1 1 2l-1 1c-1 1-1 2-1 3l2-3h1 0l-2 2c-1 2-1 4-3 5v-2c0-1 1-2 2-3l-1-1z" class="B"></path><defs><linearGradient id="AY" x1="443.645" y1="432.486" x2="419.27" y2="461.312" xlink:href="#B"><stop offset="0" stop-color="#0c0b0d"></stop><stop offset="1" stop-color="#2c2b2d"></stop></linearGradient></defs><path fill="url(#AY)" d="M436 425h3c1-1 1-1 1-2 0 1 1 3 1 5l1 4c1 4 4 14 2 17l-1 1c0-2 1-4 0-5 0 7 1 15-2 21h0c-2 3-5 5-7 7h-2c4-4 6-8 8-14v-1c1-4 0-8 0-12 0-5-1-10-3-15-1-1-3-2-4-2s-4 1-5 1-2 1-3 1-2 0-3 1c-1 2-1 2-1 4l-1-1 1-6 1-2 1 1c1 0 3-1 4-2v-1c2-1 4-1 7-1 0 0 1 1 2 1z"></path><path d="M427 426v-1c2-1 4-1 7-1 0 0 1 1 2 1h0c-3 0-6 0-9 1z" class="E"></path><path d="M436 425h3c1-1 1-1 1-2 0 1 1 3 1 5l1 4c1 4 4 14 2 17l-1 1c0-2 1-4 0-5 0-4-1-7-2-11 0-4-2-7-5-9h0z" class="C"></path><path d="M461 463l1-1c0-1-1-3-1-5-1-7-4-14-7-20l1-1c2 3 4 8 6 11 0 2 1 5 1 7v1-2-3l1 1v2c1 1 0 2 1 3v3h0c0-1 1-2 0-3v-1c0-3-1-5-2-8h1l1 1c1 0 2-2 3-2 1-1 2-1 3-2v1l-2 2h1 0c1 5 3 9 4 14 2 13 0 24-3 37 0 7-4 14-7 19l-3 6c-3 5-7 10-9 16v1 1h0v1c0 1-1 2-2 3l-6 7v-8c0-1 1-2 1-3 0-3-1-5 0-8 4-7 8-15 13-22l2-2 1-1-2-2c1 0 1-1 1-1v-2-2h0l-1-1v1c-1-2 0-4 0-6v-6-2c-1-1-1-1-1-2v-4c1-1 2-3 2-4v-1l1 1v2c1-2 1-4 1-6-1-1-1-2-1-3 1 1 2 1 3 1l2-1h0c1 0 2-1 3-2l-1-1h-1v-1c-2-1-3-2-5-3z" class="v"></path><path d="M457 513l1 1c0 2-1 3-3 5l-1-1c1-1 2-3 3-5z" class="T"></path><path d="M454 518l1 1-4 8-1 2v-2l1-1s1-1 1-2v-1c-1 2-2 4-3 5h0l5-10z" class="L"></path><path d="M460 498c1 0 1 1 1 1h0v3c-1 2 0 4 0 5v1c0 1 0 1 1 1l-1 1h0c-1 0-1-1-2-1l1-1-2-2c1 0 1-1 1-1v-2l1-5z" class="h"></path><path d="M460 498c1 0 1 1 1 1 0 2-1 4-1 7h0l-1-1v-2l1-5z" class="J"></path><path d="M457 511l1 1-1 1c-1 2-2 4-3 5l-5 10c-1 2-3 4-4 6h0l-1-1c4-7 8-15 13-22z" class="o"></path><path d="M463 471l2-1v8c1 1 0 3 0 4l-1 3v3s0 1-1 1v1c0-1 0-3-1-5 0-1 1-3 1-4v-10zm5 4l1-1v1 8l-2 11h-1c0 2 0 3-2 4l-1-1c1-1 1-3 2-5 0-1 0-3 1-4 1-5 1-9 2-13z" class="h"></path><path d="M468 447h1 0c1 5 3 9 4 14 2 13 0 24-3 37v-3-1-2l1-9c1-7 0-14-1-21 0-4-1-9-3-12-1 0-1 0-1-1l2-2z" class="AB"></path><path d="M460 470c1 1 2 1 3 1v10c0 1-1 3-1 4 1 2 1 4 1 5v1 1c-1 2-1 5-2 7h0s0-1-1-1l-1 5v-2h0l-1-1v1c-1-2 0-4 0-6v-6-2c-1-1-1-1-1-2v-4c1-1 2-3 2-4v-1l1 1v2c1-2 1-4 1-6-1-1-1-2-1-3z" class="K"></path><path d="M462 485c1 2 1 4 1 5v1 1l-1-1v-2-4z" class="AB"></path><path d="M462 489c1 1 1 1 1 2v1l-1-1v-2z" class="t"></path><path d="M462 491l1 1c-1 2-1 5-2 7h0s0-1-1-1l2-7z" class="AB"></path><path d="M461 473c0 3 1 4 0 7v1h0v1c1 2 0 4-1 5 0 1-1 1-1 1-1 2 0 5-1 7v-6-2c-1-1-1-1-1-2v-4c1-1 2-3 2-4v-1l1 1v2c1-2 1-4 1-6z" class="i"></path><path d="M459 477v-1l1 1v2c-1 3-1 7-2 10v-2c-1-1-1-1-1-2v-4c1-1 2-3 2-4z" class="AC"></path><defs><linearGradient id="AZ" x1="380.935" y1="616.153" x2="390.556" y2="595.957" xlink:href="#B"><stop offset="0" stop-color="#080005"></stop><stop offset="1" stop-color="#303030"></stop></linearGradient></defs><path fill="url(#AZ)" d="M370 595h0v-2-1-1l-1-2h1c4 0 9 2 14 4l3 1c2 0 3 0 5-1v1h1c2 1 5 4 7 6l1 1 2 1 2 1h0l3 3 2 1c1 1 2 2 2 3h3l-1 1c-1 0-1 0-2-1-1 0-1 0-1 1h-1-2c0 2-1 3-1 4-1 0 0 1-1 0h-1l-9-3-1 1c2 0 3 0 5 2h1l1 1-1 1s-1 1-2 1h0c-2 2-5 3-6 5h-1 0c0 1-1 2-1 3h0l-1 1c-4-1-7-1-11-2-1 0-2 1-3 1l-3-1-7-2c-1 0-3-1-3-1l-2-2-5-4v-2l2 1c-4-4-6-7-8-12 1-2 0-4-1-6l2-2 1 2c2 0 3 2 4 3v1c1 1 2 1 3 1 1 1 3 2 4 3h2l1 1c1-1 1-1 2-3v-1c1-1 2-1 3-4l-1-3z"></path><path d="M400 607h3v2l-3-2z" class="b"></path><path d="M392 593v1 1s-1 0-1 1l-1 1c0-2-2-2-3-3h-1 1c2 0 3 0 5-1z" class="a"></path><path d="M390 597l1-1c0-1 1-1 1-1 1 1 1 2 2 4h0v3c0 1 0 1 1 2v1c-2-1-5-1-7-2h0l-3-1-5-1h2 1 0l-1-1h0 1c1 0 1 0 2 1 1-1 1-1 2-1s3-2 3-3z" class="c"></path><path d="M385 602c3 0 4-2 6-4h1v2c-1 1-2 1-3 2-1 0-1 1-1 1h0l-3-1z" class="I"></path><path d="M392 600l2 2c0 1 0 1 1 2v1c-2-1-5-1-7-2 0 0 0-1 1-1 1-1 2-1 3-2z" class="D"></path><path d="M382 616c1 0 2-1 2-2 1 0 2-1 2-2l1 1s-1 0-1 1-1 2-2 3l2 2c1 0 1 1 2 1s1-1 1-1c1-1 3-1 4-2l2-1h-1c-2 0-4 1-6 1-1 0-1-1-2-2h1c2-1 3-1 5-2h3c2 0 3 0 5 2h1l1 1-1 1s-1 1-2 1h0c-2 2-5 3-6 5h-1l-2-1-3-2-5-4h0z" class="J"></path><path d="M392 613h4v1c-3 2-5 2-8 2l-1-1c2-1 3-1 5-2z" class="L"></path><path d="M400 615h1c-3 2-6 4-10 5v1l-1-1v-1c3-1 6-3 10-4z" class="Q"></path><path d="M401 615l1 1-1 1s-1 1-2 1h0c-2 2-5 3-6 5h-1l-2-1 1-1v-1c4-1 7-3 10-5z" class="AC"></path><path d="M392 594h1c2 1 5 4 7 6l1 1 2 1 2 1h0l3 3 2 1c1 1 2 2 2 3h3l-1 1c-1 0-1 0-2-1-1 0-1 0-1 1h-1-2-1l-4-2v-2h-3l-5-2v-1c-1-1-1-1-1-2v-3h0c-1-2-1-3-2-4v-1z" class="N"></path><path d="M399 602v-1h2l1 1c-1 1-1 1-2 1l-1-1z" class="J"></path><path d="M399 604l-1-1c-1 0-1 0-2-1v-1h0 3v1l1 1c1 0 1 1 2 0l1 1v2h2c-1 0-1 0-2 1h-3l-5-2v-1c1 1 1 1 3 1v-1h1z" class="c"></path><path d="M399 604l1-1v2 1l-1-1-1-1h1z" class="D"></path><path d="M400 605l1-1h0c1 1 1 1 1 2-1 0-1 1-2 0h0v-1z" class="W"></path><path d="M403 604v-1c1 0 1 0 2 1v-1l3 3 2 1c1 1 2 2 2 3h3l-1 1c-1 0-1 0-2-1-1 0-1 0-1 1h-1-2-1l-4-2v-2c1-1 1-1 2-1h-2v-2z" class="U"></path><path d="M403 604v-1c1 0 1 0 2 1v-1l3 3 2 1c-1 1-1 1 0 2h-1c-1 0-2-1-3-2l-1-1h0-2v-2z" class="B"></path><path d="M403 604v-1c1 0 1 0 2 1l1 1v1h-1 0-2v-2z" class="J"></path><path d="M371 598c1 5 3 8 5 12l6 6h0l5 4 3 2 2 1h0c0 1-1 2-1 3h0l-1 1c-4-1-7-1-11-2l-9-3c2-1 3-1 4-3-1-1-1-1-1-3 0 0 0-1-1-2v-1l-1-2v-2c-1-1-1-2-2-3h-3c1-1 1-1 2-3v-1c1-1 2-1 3-4z" class="S"></path><path d="M376 620c0 1 0 1-1 1h-1c0-1 1-1 1-2h1v1z" class="f"></path><path d="M368 603l1 3h-3c1-1 1-1 2-3z" class="g"></path><path d="M376 615c0 1 1 1 2 2h0-1c-1 1-2 1-3 1v-1l2-2z" class="f"></path><path d="M378 617l3 3-2 2c-1-1-1-1-2-1-1-1 0-1-1-1v-1l2-2z" class="g"></path><path d="M376 615c-1 0-2-1-3-3 0-1 0-1-1-2v-1c1-1 1 0 2 1h2l6 6h0c0 1-1 1-1 1v1l-3-2v1c-1-1-2-1-2-2z" class="AN"></path><path d="M378 617v-1l3 2v-1s1 0 1-1l5 4 3 2 2 1h0c0 1-1 2-1 3h0l-3-1c-2-2-5-4-7-5l-3-3h0 0z" class="AP"></path><path d="M387 620l3 2 2 1h0c0 1-1 2-1 3h0l-3-1v-2h0c0-1 0-2-1-3z" class="AE"></path><defs><linearGradient id="Aa" x1="349.269" y1="608.047" x2="372.808" y2="608.081" xlink:href="#B"><stop offset="0" stop-color="#2d0c0e"></stop><stop offset="1" stop-color="#7a1415"></stop></linearGradient></defs><path fill="url(#Aa)" d="M350 603c1-2 0-4-1-6l2-2 1 2c2 0 3 2 4 3v1c1 1 2 1 3 1 1 1 3 2 4 3h2l1 1h3c1 1 1 2 2 3v2l1 2v1c1 1 1 2 1 2 0 2 0 2 1 3-1 2-2 2-4 3l9 3c-1 0-2 1-3 1l-3-1-7-2c-1 0-3-1-3-1l-2-2-5-4v-2l2 1c-4-4-6-7-8-12z"></path><path d="M363 605h2l-1 2h0c-2 0-4-1-5-2h3 1z" class="i"></path><path d="M359 605v-1h-1l-2 1v-1c1-1 1-1 2-1h0l-2-2h0c1 1 2 1 3 1 1 1 3 2 4 3h-1-3z" class="Q"></path><path d="M356 616v-2l2 1c2 3 5 4 8 6h-1 0v1h1v1c-1 0-3-1-3-1l-2-2-5-4z" class="AE"></path><path d="M366 621c2 0 3 1 4 1l9 3c-1 0-2 1-3 1l-3-1-7-2v-1h-1v-1h0 1z" class="AP"></path><path d="M365 605l1 1h3c1 1 1 2 2 3v2l1 2v1c0 1 0 1-1 2l2 1v1c-2 0-2-1-3-1v-2l-1 1c-1 1-1 1-3 2-1-1-1-2-2-3v-1-3h-1s0-1 1-2v-2l1-2z" class="l"></path><path d="M368 614h2v1h0 0l-1 1-1-2z" class="X"></path><path d="M372 613l-1 1-1-1c-1 0-1-1-1-2h1 1l1 2z" class="e"></path><path d="M366 608h1c0 1 0 2 1 3h0l-1 1-1-1v-1c-1-1-1-2 0-2z" class="X"></path><path d="M364 614l1-2h0 1c0 1 1 2 2 2l1 2c-1 1-1 1-3 2-1-1-1-2-2-3v-1z" class="g"></path><path d="M347 471c5 3 10 6 15 8 2 1 5 2 6 3-1 21 0 42 0 63 0 14-1 28 0 42 1 2 1 6 2 8l1 3c-1 3-2 3-3 4v1c-1 2-1 2-2 3l-1-1h-2c-1-1-3-2-4-3-1 0-2 0-3-1v-1c-1-1-2-3-4-3l-1-2-2 2c1 2 2 4 1 6-3-6-3-15-2-22-1-3-1-5-1-8l1-24v-22c0-1-1-2 0-4 1 1 2 1 2 2 1 0 2 0 3-1-2-1-3-2-4-3h-1v-2-1-1c-1-5-1-11-1-16v-30z" class="T"></path><path d="M350 514c1 1 1 2 2 2 2 2 4 5 5 8h-1 0 0l-3-3c-1-1-3-3-5-3v-1h1c0-1 1-2 1-3z" class="K"></path><path d="M356 524h0 1c2 3 4 8 5 13 0 0-1 1-2 1h0v2l-1 1-1-1c1-1 1-3 1-3 0-2-1-5-1-7-1-2-2-4-2-6z" class="l"></path><path d="M348 518c2 0 4 2 5 3l3 3h0c0 2 1 4 2 6h-1-1l-3-6c-2-1-3-2-4-3h-1v-2-1z" class="L"></path><path d="M351 511c0-1 1-3 2-4 2-4 4-9 4-13v-4c0-1-1-2-1-3s1-1 1-1h1v2c1 7 0 12-3 17-1 2-1 4-3 5h0l-1 1zm9 27v1c1 1 2 2 2 4s0 5 1 7c-1 1-2 1-3 1v-1c0-1 1-2 0-3v-4c0-1-1-1-1-2l-1 1v1l-3 3c-1 1-3 3-4 3v-1c2-1 3-3 5-5 1 0 2-1 2-1v-2l1 1 1-1v-2z" class="o"></path><path d="M358 543v-1l1-1c0 1 1 1 1 2v4c1 1 0 2 0 3v1l-2 3c0 3-1 5-2 8-2-2-3-4-5-6l-1-1v-1c-1-1-1-1-1-2v-1l2-2c1 0 3-2 4-3l3-3z" class="K"></path><path d="M358 554c0 3-1 5-2 8-2-2-3-4-5-6h1c1-1 2-1 4 0v1h1c1-1 1-1 0-2l1-1z" class="o"></path><path d="M351 549c1 0 3-2 4-3l3-3c1 2 1 3 1 5 0 1 0 2-1 3-1 2-2 3-4 3-2-1-3-2-5-3l2-2z" class="T"></path><path d="M348 527c0-1-1-2 0-4 1 1 2 1 2 2 1 0 2 0 3-1l3 6h1 1c0 2 1 5 1 7 0 0 0 2-1 3v2s-1 1-2 1c-2 2-3 4-5 5v1l-2 2v1c0 1 0 1 1 2v1l-1-1h0c0-1 0-1-1-1v11 2c0 3 0 5-1 7l1-24v-22z" class="Q"></path><path d="M353 524l3 6c1 2 1 3 1 5 0 1 1 1 1 2h-1v-1c-1 0-1-1-1-1-1-3-3-4-4-6l-1-1c0-1-1-2-1-3 1 0 2 0 3-1z" class="o"></path><path d="M347 471c5 3 10 6 15 8 2 1 5 2 6 3-1 21 0 42 0 63 0 14-1 28 0 42 1 2 1 6 2 8l1 3c-1 3-2 3-3 4v1c-1 2-1 2-2 3l-1-1h-2c-1-1-3-2-4-3h0c2 0 4 1 5 1 1 1 2 0 3 0v-2l-1-3v-8l-1-16c1-1 1-5 1-6s-3-3-4-3c0-1-1-1-1-2l-1-1h0c2 0 5 2 6 3l-1-53c0-6-1-12-2-18-1-3-2-6-3-8s-3-3-4-5l-8-8v40h1c1 0 1-1 2-2l1-1h0l-2 4c0 1-1 2-1 3h-1c-1-5-1-11-1-16v-30z" class="l"></path><path d="M366 598l1-5v6c1 1 0 2 1 3v1c-1 2-1 2-2 3l-1-1h-2c-1-1-3-2-4-3h0c2 0 4 1 5 1 1 1 2 0 3 0v-2l-1-3z" class="z"></path><path d="M361 480h1c1 1 1 2 2 4h0c1 4 1 8 1 12l-1-1c0-2 0-5-1-8 0-2-1-4-2-7z" class="Y"></path><path d="M347 573c1-2 1-4 1-7v-2-11c1 0 1 0 1 1h0l1 1 1 1c2 2 3 4 5 6 1 0 1 0 2 1h3c0 1 1 1 1 2 1 0 4 2 4 3s0 5-1 6l1 16v8l1 3v2c-1 0-2 1-3 0-1 0-3-1-5-1h0c-1 0-2 0-3-1v-1c-1-1-2-3-4-3l-1-2-2 2c1 2 2 4 1 6-3-6-3-15-2-22-1-3-1-5-1-8z" class="L"></path><path d="M355 585c1 1 1 1 1 2-1 1-2 2-3 2v-1c0-2 1-2 2-3z" class="T"></path><path d="M356 600c1 0 3 1 4 1l1-1h2c1 0 2 2 3 2 0-1 0-1 1-1v2c-1 0-2 1-3 0-1 0-3-1-5-1h0c-1 0-2 0-3-1v-1z" class="i"></path><path d="M348 581h0v8h1l1-1v-3c1-2 2-3 3-4 2-2 5-4 6-7v2-1h1c0 1-3 5-4 6 0 1-1 3-1 4-1 1-2 1-2 3v1c-1 1-2 3-2 4 0 2 1 3 1 4l-1-2-2 2c1 2 2 4 1 6-3-6-3-15-2-22z" class="Q"></path><path d="M494 288c1 0 2-1 3-2l1 1c-3 3-5 6-9 8h2 0c-1 1-1 2-1 2l-2 2c-4 3-8 5-12 7v2h0 1c0 1 0 2-1 2h1l-1 3 3-3 1 1-7 8 2-1c-3 3-3 8-4 12s-2 9-3 13l-8 13v1 2l-9 12c-2 1-3 3-4 4h-1v2h1c1-1 2-1 3-1l-1 2-2 2c1 0 1 0 1 1 1 2 1 2 1 4h0v-1h1c0 1 0 2 1 3 1 0 1 1 2 2v-1c1 1 1 1 1 2-1 1 0 4 0 6 1 4 1 8 1 11h1c0 1 1 1 2 1h-1l-3 3-1 3c0-1-1-1-2-2v2c-3 5-5 12-6 18v-1-4c-1 0-1 0-2-1v2h0c0 2 0 3-1 4l-1-4c0-2-1-4-1-5 0 1 0 1-1 2h-3c-1 0-2-1-2-1-3 0-5 0-7 1v1c-1 1-3 2-4 2l-1-1-1 2-1 6c-1 3-3 7-4 10-1 2-2 3-3 5-3 6-9 10-13 15-1 1-2 2-3 2l-2 2c-2 1-7 0-9 0h-27-11c-1-1-1-1 0-2h1c3 0 5-2 8-4-4 1-7 2-9 4v-11-2c2-1 3-4 4-6 3-5 4-12 5-18v-4-6l-1-3v-2l-1-7-2-4h2c-1-2-2-4-4-6l-1-2c0 1 0 2-1 3l-1-1h-1c0-1-1-1-1-2v-2h0v-1l2-1-4-4-5-2c-1 1-2 1-3 1 0-1-1-2-1-3s0-1-1-1v-1c0-1 1-3 1-5v-1c1-2 2-3 2-5 1-5 1-10 1-16 0-5 0-10-2-16 0-2 1-5 0-6v-3-3c-1-3-2-4-3-6-3-3-5-6-9-9-1-2-3-3-4-4h-2 1v-2h0 3c-1-1-2-2-3-2l-4-2-18-5-1 1h0-1-2c0-1-1-1-1-2h1c0-1-1-1-1-1-1 0-1 0-2-1h1c-3-2-6-2-9-3l2-1 28 6c16 4 31 7 47 9 5 0 10 1 15 1l29 2c4 1 8 0 12 0 5 0 9 1 13 0 4 0 9 0 13-1h1 1l16-2c5-1 11-3 16-5 2 0 4-1 6-2 5-3 10-5 13-10z" class="AC"></path><path d="M374 411l-2-1v-1-1c1 1 2 1 3 2l-1 1z" class="AA"></path><path d="M386 392c-1 0-2 0-4-1 3 0 4 0 7 1h-3 0z" class="t"></path><path d="M434 411c1 1 2 1 3 2l-1 1c-1 0-2-1-3-2l1-1z" class="AB"></path><path d="M389 392h1v1 1c-1-1-3-1-4-2h0 3z" class="h"></path><path d="M381 432l1 1-3 3c-1 0-1 1-1 2h0-1c0-1 1-2 1-3 1 0 1 0 1-1h1-2l3-2z" class="w"></path><path d="M443 401v-6l2 4-2 2z" class="F"></path><path d="M381 431v1h0l-3 2h-1c-1-1-1-1-1-2l1-1h4zm46-29v-1c3 2 5 3 7 5h0v1l-1-1c-2-1-5-3-7-3h-1c1-1 2-1 2 0h1c0-1-1-1-1-1z" class="AB"></path><path d="M382 365c-2 0-4-1-6-2h10 3-1c0 1-1 1-1 0-2 0-3 1-5 2z" class="C"></path><path d="M376 411c3-1 5 0 7 1 1 0 1 0 2 1h0 1l-1 1-9-3z" class="W"></path><path d="M423 364h0l2-1-1 2c1 0 2 1 3 2h1c-1 0-1 1-2 0h0l3 3h0c-2 0-5-3-7-4l1-1v-1z" class="B"></path><path d="M422 413c2 1 6 1 7 2 0 1-1 1-1 1-2 0-3 0-5-1h-1v-2zm-41 18v-1l-6-7 1-1 3 3c1 2 2 3 3 4s2 2 3 2c-1 0-1 0-2 1 0 1-1 1-1 1l-1-1h0v-1z" class="AA"></path><path d="M396 419c1 1 3 2 4 3h2 1c1 1 2 2 4 3l-2 1h-3l1-1-2-2-2-1h-1l-3-2 1-1z" class="G"></path><path d="M418 402l6 3c3 2 7 4 10 6l-1 1c-4-3-9-5-14-8h1l-1-1s-1 0-1-1h0z" class="L"></path><path d="M453 404l1 2c1 0 1 1 1 1h1c0 1 1 1 2 1h-1l-3 3-1 3c0-1-1-1-2-2h1v-5l1-3z" class="G"></path><path d="M452 412l1-2h1v1l-1 3c0-1-1-1-2-2h1z" class="F"></path><path d="M453 404l1 2c1 0 1 1 1 1h1c0 1 1 1 2 1h-1-4l-1-1 1-3z" class="H"></path><path d="M422 378c1-1 1 0 2 0h2c1 1 2 1 3 2l7 4c1 1 3 2 4 3h0l-1 1c-5-4-11-7-17-10z" class="D"></path><path d="M446 371v-10c1-1 0-2 1-3 1 2 1 3 2 5v5h1l-3 5-1-2z" class="R"></path><path d="M440 415h1c-1 2-1 6-1 8 0 1 0 1-1 2h-3c-1 0-2-1-2-1v-1c3-2 5-4 6-8z" class="u"></path><path d="M400 395v-1h0 1c1 0 2 0 2 1 2 0 3 0 4 1h1c1 0 2 0 3 1 2 0 3 0 4 1h4 0 1c-1 1-2 1-4 1-1 1-3-1-4 0h0l-8-3c-1 0-3 0-4-1z" class="w"></path><path d="M454 357h1c1 1 1 3 1 4-2 3-3 6-5 9-2 2-3 3-4 5h-1 0 0l1-2 3-5c2-3 3-7 4-11z" class="AH"></path><path d="M434 373c2 1 3 2 4 1h0v-1c0-1-1-1-1-2-1-1-3-1-4-1-2 0-3-1-4-2v-1h1 0 2s1 0 1 1c2 0 3 1 4 1v-2c-1-1-2-1-4-1l-1-1-3-1c-1 0-1 0-1 1l-1-1 1-1c1 0 1 0 2 1h1 1c1 1 1 1 2 1h3c1 1 1 1 1 2 1 3 0 5 1 7v1h-1c-1 0-2-1-4-2z" class="N"></path><path d="M375 410l1 1 9 3 1-1h1l3 1v1 1h-1c1 0 2 1 3 1 0 0 1 1 2 1h0c1 1 1 1 2 1l-1 1-2-1h-2l-1-1-1-1c-6-2-11-3-15-6l1-1z" class="J"></path><path d="M387 413l3 1v1 1h-1l-4-2 1-1h1z" class="AU"></path><path d="M400 411l1-1c1 1 2 1 3 1 1 1 3 2 4 3h0c1 1 3 2 4 3v1h1c-1 1-1 1-2 1-1 1-2 1-3 1s-3-1-4-1l-2-2h0 1c2 0 3 0 5-1v-1c-2-1-3-1-4-2-2 0-3-1-4-2z" class="L"></path><path d="M403 417h6l3 1h1c-1 1-1 1-2 1-1 1-2 1-3 1s-3-1-4-1l-2-2h0 1z" class="X"></path><path d="M409 417l3 1h1c-1 1-1 1-2 1h-2v-2z" class="Y"></path><path d="M449 393h2v1l2 3h0l1-1c1 4 1 8 1 11 0 0 0-1-1-1l-1-2v-1c-2-2-3-4-5-7l-2-2v-1h2 1 0z" class="Z"></path><path d="M449 393h2v1h-1-1l-1 2-2-2v-1h2 1 0z" class="U"></path><path d="M390 392c2 1 5 1 7 2 1 0 2 0 3 1s3 1 4 1l8 3 6 3h0c0 1 1 1 1 1l1 1h-1c-6-2-11-5-17-7l-12-3v-1-1z" class="q"></path><path d="M414 432l3-1-1 3c-1 2-2 5-4 7v-1c1-1 1-2 1-3 1 0 1 0 1-1v-1c-2 1-4 2-7 3-3 2-5 3-9 1v-1l5-1c2 0 3-1 5-2 1-1 2-1 2-2l4-1z" class="R"></path><path d="M445 399c2 2 3 5 4 8 1 1 1 3 1 5-2-1-3-1-4-2h0c-1 0-2-1-2-1h-1v-1-7l2-2z" class="P"></path><path d="M446 407c1-1 1 0 2 0h1c1 1 1 3 1 5-2-1-3-1-4-2h0v-1c0-1-1-1 0-2z" class="R"></path><path d="M446 407c1-1 1 0 2 0v1h-2v-1z" class="M"></path><path d="M437 379c1 1 2 1 3 0 1 0 2-1 3-2v-4c0-5 0-10 1-15 1 5 1 10 1 15h1v-2l1 2-1 2h0c-1 2-4 4-5 6h-4l-1-1h0 1c-1-1-3-2-4-3 0 0-1 0-1-1l-1 1h0c-1-1-2-1-3-1h-1v1h0-1v-1l1-1h-1 3l-1-1h-1l-1-1c1 0 2 1 3 1 2 2 5 3 8 5z" class="H"></path><path d="M387 413v-1c1 0 1 1 2 1 4 1 9 3 12 5l3 2 4 3-1 2c-2-1-3-2-4-3h-1-2c-1-1-3-2-4-3-1 0-1 0-2-1h0c-1 0-2-1-2-1-1 0-2-1-3-1h1v-1-1l-3-1z" class="K"></path><path d="M390 414c4 2 7 3 10 6 1 0 2 1 3 2h-1-2c-1-1-3-2-4-3-1 0-1 0-2-1h0c-1 0-2-1-2-1-1 0-2-1-3-1h1v-1-1z" class="w"></path><path d="M397 431v-1h-2c-2 0-4-3-6-3-3-2-6-4-10-6 5 1 9 4 13 6 1 0 1-1 2-1 1 1 2 1 3 2h2l1-1c-3-1-4-3-6-4-4-2-9-4-14-6h0 0c2 0 3 1 5 1l9 4c3 2 5 3 8 4h0 3c-3 2-5 4-8 5z" class="W"></path><path d="M440 415c0-2 2-5 3-7v1h1s1 1 2 1h0c-1 1-1 1-1 2l1 1c0 1-1 1-1 2-2 3-2 8-2 11v2h0c0 2 0 3-1 4l-1-4c0-2-1-4-1-5 0-2 0-6 1-8h-1z" class="B"></path><path d="M441 428c1-1 1-2 2-3v3h0c0 2 0 3-1 4l-1-4z" class="I"></path><path d="M440 415c0-2 2-5 3-7v1h1s1 1 2 1h0c-1 1-1 1-1 2s-1 1-1 2l-1 1v-1-2h-1c0 1 0 2-1 3h0-1z" class="H"></path><path d="M446 410c1 1 2 1 4 2 0 1-1 2-1 3l2-1c-3 5-5 12-6 18v-1-4c-1 0-1 0-2-1 0-3 0-8 2-11 0-1 1-1 1-2l-1-1c0-1 0-1 1-2z" class="F"></path><path d="M446 413l2-1 1 1c0 1 0 2-1 3 0-1 0-1-1-1h-1-1c0-1 1-1 1-2z" class="D"></path><path d="M445 415h1 1c1 0 1 0 1 1l-3 11c-1 0-1 0-2-1 0-3 0-8 2-11z" class="P"></path><path d="M400 411h-2 0c-1-1-1-1-2-1h2c-1-1-2-1-2-1l-1-1h-1v-1h2-1-1v-1h1c1 0 1 1 2 1h0c1-1 1 0 2 0h1 0 2 1 0l1 1h1c1 0 3 1 4 2s3 2 5 3c1 0 2 1 3 1l1-1h0l-2 2-3 3h-1v-1c-1-1-3-2-4-3h0c-1-1-3-2-4-3-1 0-2 0-3-1l-1 1z" class="J"></path><path d="M408 414c3 1 6 1 8 1l-3 3h-1v-1c-1-1-3-2-4-3h0z" class="K"></path><defs><linearGradient id="Ab" x1="402.603" y1="371.587" x2="403.74" y2="367.513" xlink:href="#B"><stop offset="0" stop-color="#4a453f"></stop><stop offset="1" stop-color="#817368"></stop></linearGradient></defs><path fill="url(#Ab)" d="M382 365c2-1 3-2 5-2 0 1 1 1 1 0h1l3 1c4 1 8 2 13 4 7 3 14 7 21 10h-2c-1 0-1-1-2 0-7-3-13-6-19-8-7-2-14-4-21-5z"></path><path d="M447 380c1 0 1 0 1 1 1 2 1 2 1 4h0v-1h1c0 1 0 2 1 3 1 0 1 1 2 2v-1c1 1 1 1 1 2-1 1 0 4 0 6l-1 1h0l-2-3v-1h-2 0-1-2v1l-7-6 1-1h0l1-1c3-2 4-3 6-6z" class="N"></path><path d="M453 389v-1c1 1 1 1 1 2-1 1 0 4 0 6l-1 1h0l-2-3v-1h-2v-1h-2v-1-1 1c1-1 1-1 1-2l1 1c0 1 1 1 2 2h1v2s0 1 1 1v-1-5z" class="B"></path><path d="M448 386v1c0 1 0 2-1 2v2 1h2v1h0-1-2v1l-7-6 1-1c1 1 2 2 3 2 2 0 3-1 5-3z" class="c"></path><path d="M447 380c1 0 1 0 1 1 1 2 1 2 1 4l-1 1c-2 2-3 3-5 3-1 0-2-1-3-2h0l1-1c3-2 4-3 6-6z" class="G"></path><path d="M392 364h5v1h1c2 1 3 1 5 1l1 1h1c1 1 1 1 2 1h1c1 1 2 1 3 2h1c4 2 9 5 12 7 1-1 1-1 2-1h0v1h1 0v-1h1c1 0 2 0 3 1h0l1-1c0 1 1 1 1 1 1 1 3 2 4 3h-1 0l1 1h4c1-2 4-4 5-6h0v2h1c1-1 2-1 3-1l-1 2-2 2c-2 3-3 4-6 6l-1 1c-1-1-3-2-4-3l-7-4c-1-1-2-1-3-2-7-3-14-7-21-10-5-2-9-3-13-4z" class="c"></path><path d="M429 380c1-1 2-1 3 0h1c2 1 4 3 6 4 0 1 1 1 2 2l-1 1c-1-1-3-2-4-3l-7-4z" class="N"></path><path d="M416 434l2-2v3c-1 2-2 4-3 7l-3 4c-9 9-22 11-34 12h-6c-2 0-4 1-5 1-3 1-5 2-8 4l2-2c0-1 0-1 1-2h0c3-2 5-4 10-4 1 1 15 0 18-1 8-2 17-5 22-13 2-2 3-5 4-7z" class="AH"></path><defs><linearGradient id="Ac" x1="424.42" y1="364.667" x2="415.566" y2="374.364" xlink:href="#B"><stop offset="0" stop-color="#262629"></stop><stop offset="1" stop-color="#514a47"></stop></linearGradient></defs><path fill="url(#Ac)" d="M426 376c-1-1-2-2-3-2-4-3-8-5-11-6-4-2-8-2-10-5 1-1 10 2 12 2l1-1v1h4l4-2h-4c-3 0-5-1-7-1-2-1-4-1-7-1h3 5 1l1 1h2c2 0 4 1 6 0h2 0v1l-2 1h0v1l-1 1c2 1 5 4 7 4 2 1 4 2 5 3h0c2 1 3 2 4 2l-1 1h0c0 1-1 1-1 2l1 1c-3-2-6-3-8-5-1 0-2-1-3-1l1 1h1l1 1h-3 1l-1 1h0z"></path><path d="M423 364v1l-1 1c-1 0-1 1-2 1h-3v-1c1 0 2 1 3 0h2 0l-1-1c-1 1-2 1-3 1 1-1 3-1 5-2h0z" class="b"></path><path d="M418 413h2v-3h0 1c1 1 0 2 1 3v2 9l12-1v1c-3 0-5 0-7 1v1c-1 1-3 2-4 2l-1-1-1 2-1 6c-1 3-3 7-4 10l-1-1 1-1c-1 0-1-1-1-1 1-3 2-5 3-7v-3l-2 2 1-3 2-6s0-3 1-4c-3 1-6 2-8 3h-1c0-1 0-1-1-1v1h-1l-1-1-4-3h1l-1-1c1 0 3 1 4 1s2 0 3-1c1 0 1 0 2-1l3-3 2-2z" class="H"></path><path d="M408 420c1 1 1 1 3 1 1-1 3-2 5-3 1-1 3-3 4-3v4 2l-8 3h-1c0-1 0-1-1-1v1h-1l-1-1-4-3h1l-1-1c1 0 3 1 4 1z" class="N"></path><path d="M420 419l1-3h0c1 1 1 2 1 3-1 3-1 7-1 10l-1 6c-1 3-3 7-4 10l-1-1 1-1c-1 0-1-1-1-1 1-3 2-5 3-7v-3l-2 2 1-3 2-6s0-3 1-4v-2z" class="AW"></path><path d="M420 421v2 2 1c0 3-1 6-2 9v-3l-2 2 1-3 2-6s0-3 1-4z" class="u"></path><path d="M409 424h1v-1c1 0 1 0 1 1h1c2-1 5-2 8-3-1 1-1 4-1 4l-2 6-3 1-4 1c0 1-1 1-2 2-2 1-3 2-5 2-1 0-5 1-5 1v1h-1c-2 0-3 0-4 1h-1-2c-1 0-2 0-3-1h0-1v2c1 0 2-1 3 0h0 1v-1l1 1-1 1 1 1c1 0 1 0 2 1h2 2v1l1-1h0v1h-5-1c-1 0-1 0-2-1-1 1-2 1-3 0 1 0 2 0 3-1-1 0-1-1-2-1h-2c-1-1-1-2-2-3v-1h-2c-1 0-2-1-3-2l3-3s1 0 1-1c1-1 1-1 2-1h1l1-1s0-1 1-2h0v1c1 1 1 2 3 3s4 0 6-1c3-1 5-3 8-5l2-1 1-2 1 1z" class="N"></path><path d="M383 432c1-1 1-1 2-1h1c0 1 1 2 0 3v1c-1 0-2 0-3-1v-2z" class="C"></path><path d="M399 433v1l7-1h0l-1 1c-2 0-5 2-7 2-1 1-3 1-4 2-1 0-2 0-3-1-2-1-2-2-2-4h0c1 1 1 1 2 1 1 1 6 0 8-1z" class="B"></path><path d="M408 423l1 1c0 2-1 3-3 4-2 2-5 4-7 5s-7 2-8 1c-1 0-1 0-2-1h0c-1-1-2-2-1-4h0c1 1 1 2 3 3s4 0 6-1c3-1 5-3 8-5l2-1 1-2z" class="M"></path><defs><linearGradient id="Ad" x1="408.303" y1="433.486" x2="408.549" y2="421.694" xlink:href="#B"><stop offset="0" stop-color="#252529"></stop><stop offset="1" stop-color="#514b49"></stop></linearGradient></defs><path fill="url(#Ad)" d="M409 424h1v-1c1 0 1 0 1 1h1c2-1 5-2 8-3-1 1-1 4-1 4l-2 6-3 1-4 1c0 1-1 1-2 2v-1h0-1-1-1l1-1h0l-7 1v-1c2-1 5-3 7-5 2-1 3-2 3-4z"></path><path d="M407 432h-2 0c4-3 8-5 13-7h1l-2 6-3 1-4 1h-3v-1z" class="G"></path><path d="M407 432c2 0 4-1 7 0l-4 1h-3v-1z" class="F"></path><defs><linearGradient id="Ae" x1="366.043" y1="459.601" x2="368.154" y2="470.126" xlink:href="#B"><stop offset="0" stop-color="#e86660"></stop><stop offset="1" stop-color="#ee8d87"></stop></linearGradient></defs><path fill="url(#Ae)" d="M412 446l3-4s0 1 1 1l-1 1 1 1c-1 2-2 3-3 5-3 6-9 10-13 15-1 1-2 2-3 2l-2 2c-2 1-7 0-9 0h-27-11c-1-1-1-1 0-2h1c3 0 5-2 8-4l5-4c-1 1-1 1-1 2l-2 2c3-2 5-3 8-4 1 0 3-1 5-1h6c12-1 25-3 34-12z"></path><path d="M376 465h3l1 1c2 1 4 0 5 0-1 1-4 1-5 1h-5v-1l1-1z" class="Ac"></path><path d="M372 458h6-1v1 1l-2 2v1l2 1-2 1h1l-1 1h-1v-2-1-1c1-1 1-2 1-3l-1-1h-2z" class="AT"></path><path d="M399 460h1v4l-3 3-2 2c-2 0-4 0-6-1v-1c2 0 4-2 6-3v-1l3-3h1z" class="n"></path><defs><linearGradient id="Af" x1="385.444" y1="468.027" x2="397.153" y2="445.305" xlink:href="#B"><stop offset="0" stop-color="#e85f61"></stop><stop offset="1" stop-color="#f69584"></stop></linearGradient></defs><path fill="url(#Af)" d="M412 446l3-4s0 1 1 1l-1 1 1 1c-1 2-2 3-3 5-3 6-9 10-13 15-1 1-2 2-3 2l3-3v-4h-1-1l-3 3h-2c-3 0-6 2-8 3h0c-1 0-3 1-5 0l-1-1h-3-1l2-1-2-1v-1l2-2v-1-1h1c12-1 25-3 34-12z"></path><path d="M377 464c2-1 3-1 5-1l1 1-4 1h-3-1l2-1z" class="AW"></path><path d="M407 453c1-2 3-4 6-5-2 2-4 5-6 7l1-2h0-1 0z" class="Ah"></path><path d="M412 446l3-4s0 1 1 1l-1 1 1 1c-1 2-2 3-3 5 0-1 0-2 1-3h0l-1-1h-1z" class="AX"></path><path d="M407 453h1 0l-1 2c-1 2-3 4-5 6 0 1-1 2-2 3v-4h-1c2-2 5-5 8-7z" class="AH"></path><path d="M382 463c2-1 3-1 5-2l10-3c2-1 3-2 5-3 0 0 1-1 2-1 1-1 2-1 3-1h0c-3 2-6 5-8 7h-1l-3 3h-2c-3 0-6 2-8 3h0c-1 0-3 1-5 0l-1-1 4-1-1-1z" class="AV"></path><path d="M394 460h1c1 0 3-2 4-1l-1 1-3 3h-2l2-2-1-1z" class="AT"></path><path d="M394 460l1 1-2 2c-3 0-6 2-8 3h0c-1 0-3 1-5 0l-1-1 4-1 11-4z" class="AR"></path><defs><linearGradient id="Ag" x1="420.84" y1="315" x2="397.382" y2="460.847" xlink:href="#B"><stop offset="0" stop-color="#23090f"></stop><stop offset="1" stop-color="#801714"></stop></linearGradient></defs><path fill="url(#Ag)" d="M494 288c1 0 2-1 3-2l1 1c-3 3-5 6-9 8h2 0c-1 1-1 2-1 2l-2 2c-4 3-8 5-12 7v2h0 1c0 1 0 2-1 2h1l-1 3 3-3 1 1-7 8 2-1c-3 3-3 8-4 12s-2 9-3 13l-8 13v1 2l-9 12c-2 1-3 3-4 4 1-2 2-3 4-5 2-3 3-6 5-9 0-1 0-3-1-4h-1c0-3 1-5 1-8-1-1-1-2-1-3-1-4-2-7-5-10-1 0-2-1-3-1-2-1-4-1-6-1h-42c-5 0-11 0-17 1-2 0-5 1-6 3-3 2-5 6-6 10-1 5-1 11-1 17v20 41 15h0c0 1 1 9 0 10h-1c0 1-1 1-1 1l1 1 1-1h0v1h0l-1 1h-1 0c2 1 3 0 6 1-5 0-7 2-10 4h0l-5 4c-4 1-7 2-9 4v-11-2c2-1 3-4 4-6 3-5 4-12 5-18v-4-6l-1-3v-2l-1-7-2-4h2c-1-2-2-4-4-6l-1-2c0 1 0 2-1 3l-1-1h-1c0-1-1-1-1-2v-2h0v-1l2-1-4-4-5-2c-1 1-2 1-3 1 0-1-1-2-1-3s0-1-1-1v-1c0-1 1-3 1-5v-1c1-2 2-3 2-5 1-5 1-10 1-16 0-5 0-10-2-16 0-2 1-5 0-6v-3-3c-1-3-2-4-3-6-3-3-5-6-9-9-1-2-3-3-4-4h-2 1v-2h0 3c-1-1-2-2-3-2l-4-2-18-5-1 1h0-1-2c0-1-1-1-1-2h1c0-1-1-1-1-1-1 0-1 0-2-1h1c-3-2-6-2-9-3l2-1 28 6c16 4 31 7 47 9 5 0 10 1 15 1l29 2c4 1 8 0 12 0 5 0 9 1 13 0 4 0 9 0 13-1h1 1l16-2c5-1 11-3 16-5 2 0 4-1 6-2 5-3 10-5 13-10z"></path><path d="M462 336c0-2 1-4 0-6 0-1-1-3-1-5l3 6v3h-1s0 1-1 2zm-102 13c1 0 2 1 3 2 1 2 2 3 2 4s0 3 1 3h1c0-1 0-5-1-7l1-1v4c0 1 1 4 0 5l-1 1s0-1-1-1v-2c0 2-1 4 0 6 0 3 1 8 0 11v1 6 17h0c-1-2 0-6 0-9 0-5 0-10-1-15v-4-10h0v-1c0-2 0-4-1-5-1-2-2-4-3-5z" class="K"></path><path d="M440 334c-2 0-11 0-11-1h8c1 0 2 0 3 1h2 2 1 1 0 0 1v-2h-3c-3-1-6 0-9-1v-1h0-1 0l1-1c1-1 4-2 5-2l-1 1h0 0c1-1 2 0 3 0h2c0 1 0 2-1 3h-4c1 0 6 1 7 0s1-1 2-1h1-1c-1 1-1 2 0 3l1 3c-1 0-2-1-3-1-2-1-4-1-6-1z" class="Q"></path><path d="M439 331c-1 0-2 0-3-1 1-1 4-2 6-2h2c0 1 0 2-1 3h-4z" class="t"></path><path d="M449 336l-1-3c-1-1-1-2 0-3h1c1 1 3 1 4 3v2l1-1v-3-1c2 3 1 7 2 11v4c1 1 1 2 0 3h1v1h-2c-1-1-1-2-1-3-1-4-2-7-5-10z" class="l"></path><path d="M358 378h0c0-2 1-3 1-4v4h1c2-1 3-2 4-4 1 5 1 10 1 15h-1v8l-2-6-2 1c0-1 0-1-1-2-1-2-3 0-4-1 2-3 2-7 3-11z" class="Q"></path><path d="M364 374c1 5 1 10 1 15h-1v-12c-1 1-2 2-4 2v1c0-1 0-1-1-1v-1h0 1c2-1 3-2 4-4z" class="Y"></path><path d="M358 378h0c0-2 1-3 1-4v4h0v1c1 0 1 0 1 1-1 3-1 6 1 10l1 1-2 1c0-1 0-1-1-2-1-2-3 0-4-1 2-3 2-7 3-11z" class="e"></path><defs><linearGradient id="Ah" x1="473.058" y1="333.389" x2="463.125" y2="334.32" xlink:href="#B"><stop offset="0" stop-color="#72100f"></stop><stop offset="1" stop-color="#9d1e1c"></stop></linearGradient></defs><path fill="url(#Ah)" d="M473 319l2-1c-3 3-3 8-4 12s-2 9-3 13l-8 13v1 2l-9 12c-2 1-3 3-4 4 1-2 2-3 4-5 2-3 3-6 5-9 0-1 0-3-1-4h-1c0-3 1-5 1-8h2v-1l2-2 1-2c1-3 2-5 2-8 1-1 1-2 1-2h1v-3l1 2c1-1 2-1 4-2v-1c0-2 1-7-1-8l-43-1h36c2 0 7 0 9-1 1 0 2-1 3-1z"></path><path d="M465 333c1-1 2-1 4-2 0 1 0 2-1 3l-4 10h-1c0-1 1-2 0-2v-1c1-2 2-4 1-7v-3l1 2z" class="Q"></path><path d="M465 333c1-1 2-1 4-2 0 1 0 2-1 3h-2 0-1v-1z" class="i"></path><path d="M462 336c1-1 1-2 1-2h1c1 3 0 5-1 7v1c1 0 0 1 0 2h1l-1 4-1 2c-1-2-2-3-3-4l1-2c1-3 2-5 2-8z" class="z"></path><path d="M463 348c-1 0-1 0-2-1 0-1 0-1 1-1 0-2 1-3 1-5v1c1 0 0 1 0 2h1l-1 4z" class="K"></path><path d="M459 346c1 1 2 2 3 4-2 2-4 5-5 8h1l-2 3c0-1 0-3-1-4h-1c0-3 1-5 1-8h2v-1l2-2z" class="S"></path><path d="M457 349h1c-2 3-2 6-2 9h1 0 1l-2 3c0-1 0-3-1-4h-1c0-3 1-5 1-8h2z" class="AV"></path><path d="M369 322h19 1 11c-3 0-16 0-17 1-1 2-2 2-2 5h0l-3 3-4 4v1c-1 1-3 2-4 3 0 2-2 5-2 7-1 2-1 3-2 4v1h0c1 2 1 6 1 7h-1c-1 0-1-2-1-3h1v-1c-1-3-6-8-9-10h-1l1-1 1 1h0c0-1-1-2-1-2v-1c2 2 3 2 4 3 0 1 1 1 1 1h0c0-1-1-2-2-2l-2-2c-1-1-4 1-5 1s-3 0-3-1c-1-1-1-2-2-4l1-1c-2 0-3 1-4 1l1-1-1-1c0-2 2-5 3-7 1-1 2-2 2-3h-2 0c2 0 3-1 5-2v2c1 0 1 0 2-1l1 1c1 0 2-1 3-1 3-1 7-1 10-2z" class="J"></path><path d="M374 335v1c-1 1-3 2-4 3 0 2-2 5-2 7-1 2-1 3-2 4v1h0c1 2 1 6 1 7h-1c-1 0-1-2-1-3h1v1-1-1c0-1 0-5-1-6l-1-2s-1-1-1-2v-1-2 1h1l-1-1c1 0 1 1 2 1 2-3 6-4 9-7z" class="Q"></path><path d="M353 323v2c1 0 1 0 2-1l1 1c1 0 2-1 3-1l-1 1c-4 3-8 6-9 11-2 0-3 1-4 1l1-1-1-1c0-2 2-5 3-7 1-1 2-2 2-3h-2 0c2 0 3-1 5-2z" class="AR"></path><path d="M346 336c3-5 6-9 12-11-4 3-8 6-9 11-2 0-3 1-4 1l1-1z" class="AI"></path><path d="M355 389c1 1 3-1 4 1 1 1 1 1 1 2l3 8 2 2v1 1c-1 1 0 2 0 2v14 7c0 5-1 10-3 15l1 1v4l-2 8 2-2h0l5-3v-9h0c0 1 1 9 0 10h-1c0 1-1 1-1 1l1 1 1-1h0v1h0l-1 1h-1 0c2 1 3 0 6 1-5 0-7 2-10 4h0l-5 4c-4 1-7 2-9 4v-11-2c2-1 3-4 4-6 3-5 4-12 5-18v-4-6l-1-3v-2l-1-7-2-4h2c-1-2-2-4-4-6l-1-2c0 1 0 2-1 3l-1-1h-1c0-1-1-1-1-2v-2h0v-1l2-1v-1l1 2 1 1c2-1 3-3 5-5z" class="v"></path><path d="M362 426v1h-1v-2h-1c0-1 0-1-1-2l1-1 2 1v3z" class="o"></path><path d="M356 417l1 2h1v-2 5 4 2l-1-2v-6l-1-3z" class="z"></path><path d="M364 420h1v7l-1-1-1 1v-1h-1v-3c2-1 2-1 2-3zm-1-20l2 2v1 1c-1 1 0 2 0 2h-2c-1-1-1-2-1-4l1-2z" class="K"></path><path d="M346 393c2 1 3 2 4 3 0 1 0 2-1 3l-1-1h-1c0-1-1-1-1-2v-2h0v-1z" class="AP"></path><path d="M363 406h2v14h-1v-12c0 1-1 2-2 3v1c-1 1-1 1-2 1 0-2 2-4 2-6l1-1z" class="o"></path><path d="M352 448l2-2 1 1c-2 4-3 7-7 9v-2c2-1 3-4 4-6z" class="z"></path><path d="M355 404l1 2c1 3 2 7 2 11v2h-1l-1-2v-2l-1-7-2-4h2z" class="X"></path><path d="M351 398c1-1 1-1 2-1 0-2 3-3 4-4-1 2-1 4-1 6-1 2 0 5 0 7l-1-2c-1-2-2-4-4-6zm7 28v3 6l-3 12h0l-1-1-2 2c3-5 4-12 5-18v-4l1 2v-2z" class="l"></path><path d="M322 306c3 1 4 2 6 3 2 2 4 3 7 4l-1 2c2 1 3 1 4 2h1c4 2 8 2 12 2-1 0-2 1-3 2l-1 1h0-1-3v1c1 0 3 1 5 1v1h0 2c0 1-1 2-2 3-1 2-3 5-3 7l1 1-1 1c1 0 2-1 4-1l-1 1c1 2 1 3 2 4 0 1 2 1 3 1s4-2 5-1l2 2c1 0 2 1 2 2h0s-1 0-1-1c-1-1-2-1-4-3v1s1 1 1 2h0l-1-1-1 1h1c3 2 8 7 9 10v1h-1c0-1-1-2-2-4-1-1-2-2-3-2 1 1 2 3 3 5 1 1 1 3 1 5v1h0v10 4c-1 2-2 3-4 4h-1v-4c0 1-1 2-1 4h0c-1 4-1 8-3 11-2 2-3 4-5 5l-1-1-1-2v1l-4-4-5-2c-1 1-2 1-3 1 0-1-1-2-1-3s0-1-1-1v-1c0-1 1-3 1-5v-1c1-2 2-3 2-5 1-5 1-10 1-16 0-5 0-10-2-16 0-2 1-5 0-6v-3-3c-1-3-2-4-3-6-3-3-5-6-9-9-1-2-3-3-4-4h-2 1v-2h0 3z" class="L"></path><path d="M347 360l1 1h3 2l1 1v1h-6v1h0l-1-4z" class="Q"></path><path d="M355 349l-4-5h0 1c2 1 5 2 6 3s1 1 2 1v1c1 1 2 3 3 5h-1-1l-2-2c-1-1-3-2-4-3z" class="AB"></path><path d="M348 379v3l-1 1 1 5v3 1l-4-4-5-2h1v-2c1-2 1-3 1-4 1 0 2-1 3-1 1 1 2 1 3 1l1-1z" class="f"></path><path d="M347 383v1c-1-1-2-1-2-2v-1c1 0 2 1 3 1l-1 1zm-3 5v-1c1-1 2-2 3-2l1 3v3 1l-4-4z" class="AD"></path><path d="M355 349c1 1 3 2 4 3l2 2h1 1c1 1 1 3 1 5v1h0v10 4c-1 2-2 3-4 4h-1v-4c0 1-1 2-1 4h0v-6-6h0l-1-2h0c-1-1-1-1-1-2h-1c1-2-1-4-2-6l1-1-1-5h1l1-1z" class="h"></path><path d="M358 353l1-1 2 2v1 1l-3-3z" class="Q"></path><path d="M355 349c1 1 3 2 4 3l-1 1c-1-1-3-2-4-3l1-1z" class="T"></path><path d="M363 354c1 1 1 3 1 5v1h0c-1-2-2-3-3-4v-1-1h1 1z" class="L"></path><path d="M358 378v-6-6h0l-1-2h0c-1-1-1-1-1-2h-1c1-2-1-4-2-6l1-1c1 3 2 6 4 9s1 7 2 11v2c2-1 3-2 3-3s0-2 1-2v-2h0v4c-1 2-2 3-4 4h-1v-4c0 1-1 2-1 4h0z" class="T"></path><path d="M345 337c1 0 2-1 4-1l-1 1c-1 7-1 16-1 23l1 4h0v13 2l-1 1c-1 0-2 0-3-1-1 0-2 1-3 1 0 1 0 2-1 4v-23-17c1-3 4-4 5-7z" class="AD"></path><path d="M348 364h0v13c-1-1-1-1-1-2v-3h-2c-2-1-3-1-4-2v-1c0-1 0-1 1-1l3-3 1 1 1-1 1-1z" class="f"></path><defs><linearGradient id="Ai" x1="340.466" y1="349.014" x2="330.466" y2="348.652" xlink:href="#B"><stop offset="0" stop-color="#efb4ac"></stop><stop offset="1" stop-color="#ffd4c2"></stop></linearGradient></defs><path fill="url(#Ai)" d="M322 306c3 1 4 2 6 3 2 2 4 3 7 4l-1 2c2 1 3 1 4 2h1c4 2 8 2 12 2-1 0-2 1-3 2l-1 1h0-1-3v1c1 0 3 1 5 1v1h0 2c0 1-1 2-2 3-1 2-3 5-3 7l1 1-1 1c-1 3-4 4-5 7v17 23 2h-1c-1 1-2 1-3 1 0-1-1-2-1-3s0-1-1-1v-1c0-1 1-3 1-5v-1c1-2 2-3 2-5 1-5 1-10 1-16 0-5 0-10-2-16 0-2 1-5 0-6v-3-3c-1-3-2-4-3-6-3-3-5-6-9-9-1-2-3-3-4-4h-2 1v-2h0 3z"></path><path d="M335 377s1 0 1 1v3-1l1-1h0c0-1 1-1 1-1 0 2 1 4 0 6v2h2-1c-1 1-2 1-3 1 0-1-1-2-1-3s0-1-1-1v-1c0-1 1-3 1-5z" class="AE"></path><path d="M335 384h3v2h2-1c-1 1-2 1-3 1 0-1-1-2-1-3z" class="AP"></path><path d="M336 327c2 6 2 12 2 18v24 9h0s-1 0-1 1h0l-1 1v1-3c0-1-1-1-1-1v-1c1-2 2-3 2-5 1-5 1-10 1-16 0-5 0-10-2-16 0-2 1-5 0-6v-3-3z" class="AI"></path><path d="M343 323c1 0 3 1 5 1v1h0 2c0 1-1 2-2 3-1 2-3 5-3 7l1 1-1 1c-1 3-4 4-5 7v17c-1-2 0-5 0-8 0-6 0-14-1-20v-2c0-2 1-2 1-3v-3-1c1-1 1-1 2-1h1z" class="AT"></path><path d="M340 324c1-1 1-1 2-1v5c1 0 1 1 0 1h0c-1-1-1-2-2-4v-1z" class="Ac"></path><path d="M343 323c1 0 3 1 5 1v1h0c-1 0-3 0-4 1v1l-2 1v-5h1z" class="AX"></path><path d="M344 336l1-1 1 1-1 1c-1 3-4 4-5 7v-7c1 0 1-1 2-1h1 1z" class="Ab"></path><path d="M348 325h2c0 1-1 2-2 3-1 2-3 5-3 7l-1 1c-1-3-1-6 0-8v-1-1c1-1 3-1 4-1z" class="AI"></path><path d="M348 325h2c0 1-1 2-2 3v-2c-1 1-3 1-4 2v-1-1c1-1 3-1 4-1z" class="AP"></path><defs><linearGradient id="Aj" x1="325.881" y1="317.421" x2="339.025" y2="312.983" xlink:href="#B"><stop offset="0" stop-color="#f1908c"></stop><stop offset="1" stop-color="#f9b9a8"></stop></linearGradient></defs><path fill="url(#Aj)" d="M322 306c3 1 4 2 6 3 2 2 4 3 7 4l-1 2c2 1 3 1 4 2h1c4 2 8 2 12 2-1 0-2 1-3 2l-1 1h0-1-3v1h-1c-1 0-1 0-2 1v1 3c0 1-1 1-1 3v2-1c0-2 0-3-1-5-1-4-2-8-5-11h-1l-1 1h1c1 1 1 2 1 4-3-3-5-6-9-9-1-2-3-3-4-4h-2 1v-2h0 3z"></path><path d="M320 308h0c2-1 5 2 7 3s4 3 5 3l1 2h-1l-1 1h1c1 1 1 2 1 4-3-3-5-6-9-9-1-2-3-3-4-4z" class="Af"></path><path d="M339 317c4 2 8 2 12 2-1 0-2 1-3 2l-1 1h0-1-3v1h-1c-1 0-1 0-2 1v1 3c0 1-1 1-1 3v2-1c0-2 0-3-1-5 0-1 0-2 1-3v-2-2l-1-1h1 2c-1-1-1-1-2-1v-1z" class="AV"></path><path d="M339 319c1 0 2 0 3 1v1h-2l-1-1-1-1h1z" class="Af"></path><path d="M340 324h0v-2h0 7-1-3v1h-1c-1 0-1 0-2 1z" class="AT"></path><path d="M494 288c1 0 2-1 3-2l1 1c-3 3-5 6-9 8h2 0c-1 1-1 2-1 2l-2 2c-4 3-8 5-12 7v2h0 1c0 1 0 2-1 2h1l-1 3 3-3 1 1-7 8c-1 0-2 1-3 1-2 1-7 1-9 1h-36l-37 1h-19c-3 1-7 1-10 2-1 0-2 1-3 1l-1-1c-1 1-1 1-2 1v-2c-2 1-3 2-5 2v-1c-2 0-4-1-5-1v-1h3 1 0l1-1c1-1 2-2 3-2-4 0-8 0-12-2h-1c-1-1-2-1-4-2l1-2c-3-1-5-2-7-4-2-1-3-2-6-3-1-1-2-2-3-2l-4-2-18-5-1 1h0-1-2c0-1-1-1-1-2h1c0-1-1-1-1-1-1 0-1 0-2-1h1c-3-2-6-2-9-3l2-1 28 6c16 4 31 7 47 9 5 0 10 1 15 1l29 2c4 1 8 0 12 0 5 0 9 1 13 0 4 0 9 0 13-1h1 1l16-2c5-1 11-3 16-5 2 0 4-1 6-2 5-3 10-5 13-10z" class="AH"></path><path d="M345 305l5 1v1c0 1-1 1-1 1h0-1l-1 1-1-2-1-2z" class="Ac"></path><path d="M494 288c1 0 2-1 3-2l1 1c-3 3-5 6-9 8-5 3-10 7-16 9l-10 2c-1 1-3 1-4 1h-2 2v-1h1 2s1 0 2-1h1 2s0-1 1-1h1c2-1 4-1 5-2h1 1l1-1h1c2-1 5-2 6-4h1c1-1 1-1 2-1h0 1c0-1 1-2 2-2v-1c1 0 2-1 3-2h-1c-4 5-11 7-16 10l-9 3c-2 0-6 2-8 1 5-1 11-3 16-5 2 0 4-1 6-2 5-3 10-5 13-10z" class="u"></path><path d="M293 296c0-1-1-1-1-1-1 0-1 0-2-1h1l35 8v2c-2 1-5 0-7 0l-4-2-18-5-1 1h0-1-2c0-1-1-1-1-2h1z" class="AE"></path><path d="M293 296c1 0 3 1 4 1l-1 1h0-1-2c0-1-1-1-1-2h1z" class="AX"></path><path d="M350 306l13 2 8 1c1 0 2 0 3 1-3 0-6 2-8 4-1 2-2 5-3 7h1v1h5c-3 1-7 1-10 2-1 0-2 1-3 1l-1-1c-1 1-1 1-2 1v-2c-2 1-3 2-5 2v-1c-2 0-4-1-5-1v-1h3 1 0l1-1c1-1 2-2 3-2l3-1v-3h0c-1-2-3-5-4-8v-1z" class="AV"></path><path d="M364 310h0c1 1 2 1 2 2-1 0-2 1-4 1 1-1 1-2 2-3z" class="AT"></path><path d="M356 322c1 0 1 0 2 1l-3 1c-1 1-1 1-2 1v-2l3-1z" class="AE"></path><path d="M363 308l8 1c-1 0-2 1-3 1l-2 2c0-1-1-1-2-2h0l-1-2z" class="AW"></path><path d="M356 322c1-2 3-3 5-4 1-1 1-3 3-3 0 1-2 5-3 6 0 1 0 1-1 1s-1 1-2 1c-1-1-1-1-2-1z" class="Ac"></path><path d="M354 318h2 1 1l-4 3-6 3c-2 0-4-1-5-1v-1h3 1 0l1-1c1-1 2-2 3-2l3-1z" class="AR"></path><path d="M354 318h2c-2 2-6 3-9 4l1-1c1-1 2-2 3-2l3-1z" class="AX"></path><path d="M350 306l13 2 1 2c-1 1-1 2-2 3v1c-1 2-3 3-4 4h-1-1-2v-3h0c-1-2-3-5-4-8v-1z" class="AR"></path><path d="M354 315c0 1 0 2 1 2s1 0 2-1v2h-1-2v-3z" class="AW"></path><path d="M357 316h0c2-1 3-2 5-2-1 2-3 3-4 4h-1v-2z" class="AX"></path><path d="M326 302l19 3 1 2 1 2 1-1h1 0s1 0 1-1c1 3 3 6 4 8h0v3l-3 1c-4 0-8 0-12-2h-1c-1-1-2-1-4-2l1-2c-3-1-5-2-7-4-2-1-3-2-6-3-1-1-2-2-3-2 2 0 5 1 7 0v-2z" class="AR"></path><path d="M345 312c-1 0-2 1-3 0-1 0-1 0-2-1 0-1 1-1 1-1h2 0l2 2z" class="Ac"></path><path d="M328 305h0 2 2c0 1-1 2-2 2l-1 1h-1c-1-1-1-1-1-3h1z" class="AV"></path><path d="M346 307l1 2 2 3 1 2h-1l-4-2-2-2v-2c1 0 2-1 3-1z" class="AP"></path><path d="M349 308s1 0 1-1c1 3 3 6 4 8-1 0-1 0-2 1 0 1-1 1-3 1h1c1-1 0-2 0-3h0l-1-2-2-3 1-1h1 0z" class="AX"></path><path d="M351 311c0 1 0 2 1 2l-2 1h0l-1-2 2-1z" class="AW"></path><path d="M350 314l2-1v3c0 1-1 1-3 1h1c1-1 0-2 0-3z" class="Ac"></path><path d="M347 309l1-1h1 0c1 1 1 2 2 3l-2 1-2-3z" class="AR"></path><path d="M335 313c4 2 9 4 14 4 2 0 3 0 3-1 1-1 1-1 2-1h0v3l-3 1c-4 0-8 0-12-2h-1c-1-1-2-1-4-2l1-2z" class="Ah"></path><path d="M489 295h2 0c-1 1-1 2-1 2l-2 2c-4 3-8 5-12 7v2h0 1c0 1 0 2-1 2h1l-1 3 3-3 1 1-7 8c-1 0-2 1-3 1h-1-5 0l1-1c-3-1-6-1-8-1l-14 1c0-1 0-2 1-3l3-1-1-1h-3c0-1 0-3-1-4l17-3c1 0 3 0 4-1l10-2c6-2 11-6 16-9z" class="AR"></path><path d="M459 312c6-2 12-3 17-6v2h0c-4 2-9 2-12 4-1 1-1 2-2 2 0-1 0 0 1-1h-1c-1-1-2-1-3-1z" class="Af"></path><path d="M446 314c3-2 8-2 11-4h0 1c0 1 1 1 1 2h0c1 0 2 0 3 1h1c-6 0-11 0-16 2l-1-1z" class="AX"></path><path d="M476 308h1c0 1 0 2-1 2h1l-1 3 3-3 1 1-7 8c-1 0-2 1-3 1h-1-5 0l1-1c-3-1-6-1-8-1l-14 1c0-1 0-2 1-3l3-1c5-2 10-2 16-2-1 1-1 0-1 1 1 0 1-1 2-2 3-2 8-2 12-4z" class="AR"></path><path d="M476 310h1l-1 3 3-3 1 1-7 8c-1 0-2 1-3 1h-1-5 0l1-1c-3-1-6-1-8-1 2 0 5-1 7-2l12-6z" class="AV"></path><path d="M476 313l3-3 1 1-7 8c-1 0-2 1-3 1h-1-5 0l1-1c4-1 7-5 11-6z" class="V"></path><path d="M374 310h4 5c5 0 11 0 16 1h8 8l25-1h2c1 1 1 3 1 4h3l1 1-3 1c-1 1-1 2-1 3l14-1c2 0 5 0 8 1l-1 1h0 5 1c-2 1-7 1-9 1h-36l-37 1h-19-5v-1h-1c1-2 2-5 3-7 2-2 5-4 8-4z" class="Af"></path><path d="M413 316v1c1 0 1 1 1 1l1 1h0-8 0c1-1 4 0 5-1v-1l1-1z" class="AW"></path><path d="M374 310h4 5l2 1c0 1 0 1-1 2l-3 2c-2 1-5 4-7 4-2 1-4 1-6 1-1 1-2 1-4 1h-1c1-2 2-5 3-7 2-2 5-4 8-4z" class="AR"></path><path d="M378 310h5l2 1c0 1 0 1-1 2-3 0-5 1-8 0l-1-1h0-1v2l-1-1 1-3h0 4z" class="AP"></path><path d="M383 310c5 0 11 0 16 1h8l5 6v1c-1 1-4 0-5 1h0-6-11-4-9c2-1 4-3 6-4h-2l3-2c1-1 1-1 1-2l-2-1z" class="AR"></path><path d="M386 319c1-1 1-2 3-3v1l-1 1c1 1 1 1 2 1h-4zm15 0c2-2 4-5 6-6v1c0 1 0 1-1 1 0 1 0 2-1 3v1h2 0-6zm-18-9c5 0 11 0 16 1-2 0-6-1-8 0 0 1 0 1-1 2h-3c-1 1-2 1-3 2h-1 0-2l3-2c1-1 1-1 1-2l-2-1z" class="Ac"></path><path d="M440 310h2c1 1 1 3 1 4h3l1 1-3 1c-1 1-1 2-1 3h-2-26 0l-1-1s0-1-1-1v-1l-1 1-5-6h8l25-1z" class="AR"></path><path d="M435 318h-1v-2c1-1 1-2 2-3l1 1c0 2-1 2-2 4z" class="AV"></path><path d="M407 311h8-1-2c-1 1-1 2 0 3l1 2-1 1-5-6z" class="Ac"></path><path d="M440 310h2c1 1 1 3 1 4h3l1 1-3 1h-3c-1 1-1 1-2 0h0l1-1 1-1c1-1 1-2 1-4h-2z" class="AV"></path><path d="M415 319h5l5-5c0-1 2-2 3-2h1v1l-2 1c-2 1-3 3-4 5h4c3-1 7-1 10-1h0v-1c-1 0-1 0-2 1 1-2 2-2 2-4 1 0 2 1 3 1l-1 1h0c1 1 1 1 2 0h3c-1 1-1 2-1 3h-2-26 0z" class="AT"></path><path d="M439 316c1 1 1 1 2 0h3c-1 1-1 2-1 3h-2c-1-1-2 0-3 0v-2l1-1z" class="AW"></path><defs><linearGradient id="Ak" x1="112.22" y1="129.187" x2="301.325" y2="518.905" xlink:href="#B"><stop offset="0" stop-color="#1a2224"></stop><stop offset="1" stop-color="#301a1c"></stop></linearGradient></defs><path fill="url(#Ak)" d="M177 84c1 0 1-1 2-1l1 1v2l1 1v1l1 2 2-1c1 0 1 1 1 2l2-1h3l1 1c0-1 2-1 2-2l8-3h0v1l1 1c-4 3-7 6-11 10l-3 3v1c0 2 0 3 1 4-1 3-1 5-1 7 1 3 2 4 4 5l-1 1 5 3h0c-1 2-1 4-2 6-1 8-2 17 0 25 0 1 1 3 1 3l1 3h3l1 2 3 8 5 10c-2 1-2 1-3 2h1l1 3 4 7c1 1 1 1 2 1v-1c2 2 3 4 5 6 4 3 7 7 11 11 2 2 5 5 8 6h1l-1 1v-1l-1 1v1c1 2 2 3 4 4h0c1 1 1 1 2 0 1 1 1 2 2 3h0 0l1 1 1 2c3 2 4 3 6 5 1 2 1 3 2 5h0 2l2-4 1-1h0c1-2 2-2 3-3 1 1 1 1 1 2-2 2-4 4-4 7h1l1-1 1 1-1 3h1c1 2 0 4 0 7v7 5 2c0 2 0 4-2 5h-1 0c-1 1-1 2-2 3v2c1 2 0 6 0 8l2 1 2 1c7 4 14 6 21 8l2 1-2 1c3 1 6 1 9 3h-1c1 1 1 1 2 1 0 0 1 0 1 1h-1c0 1 1 1 1 2h2 1 0l1-1 18 5 4 2c1 0 2 1 3 2h-3 0v2h-1 2c1 1 3 2 4 4 4 3 6 6 9 9 1 2 2 3 3 6v3 3c1 1 0 4 0 6 2 6 2 11 2 16 0 6 0 11-1 16 0 2-1 3-2 5v1c0 2-1 4-1 5v1c1 0 1 0 1 1s1 2 1 3c1 0 2 0 3-1l5 2 4 4-2 1v1h0v2c0 1 1 1 1 2h1l1 1c1-1 1-2 1-3l1 2c2 2 3 4 4 6h-2l2 4 1 7v2l1 3v6 4c-1 6-2 13-5 18-1 2-2 5-4 6v2 11c2-2 5-3 9-4-3 2-5 4-8 4h-1c-1 1-1 1 0 2h11c-3 0-10 0-12 1v1 30c0 5 0 11 1 16v1 1 2h1c1 1 2 2 4 3-1 1-2 1-3 1 0-1-1-1-2-2-1 2 0 3 0 4v22l-1 24c0 3 0 5 1 8-1 7-1 16 2 22 2 5 4 8 8 12l-2-1v2c-1-2-3-4-4-5v1h-1-1l-3-3c0-1-1-2-1-3l-1 1-2-1v-1c-1-1-1-3-1-4v-2c-1 0-2 1-3 1h0l-2 2-2 3c-1 0-1 0-1-1l-2 2v-2l-6 6c-1 1-2 1-3 2-1 0-2 1-2 2h0c-1 0-1 0-1-1-1 1-1 0-1 1-2 2-4 1-5 2l-1-1v-1h-2-1c0-1-1-1-1-1h-1l-2 1-1 1-9 6c-1 1-1 1-2 1l-1 1-14 4-13 3c-2 1-5 1-7 1 1-1 1-1 2-1 2-1 3-4 5-6s5-4 7-6h0 1c2-2 2 0 4 0l-1-1c1-1 2-2 3-2l5-4h0l1-1c1-1 2-1 3-2l3-3v-1l1-1c1 0 2-1 3-2h0v-1c0-1 0-1-1-1-3-1-4-3-7-5l-1-1c-1-1-1-1-1-2l-2 1c-1 0-2-1-3-1-1-2-9-6-12-8-4-3-8-6-11-10l-1-1-2 1v-1c1-1 2-2 2-3l4-4 1-1-6-6-12-15c-5-7-10-15-14-23-2-3-3-6-4-9l-2 2c-4-8-5-16-6-24-1-3-1-6-1-9h0c0-2 1-3-1-5 0-1 1-4 1-6s-1-4-1-7h-1c1-4 1-10 3-13 0-1 1-1 1-2l-1-1c1-1 1-1 1-2l-2-1-2 1-1-1 1-3v-1-2c-1 1-2 2-3 2h-1l-2 1-3 1-1-1-2 2c0 1 0 1-1 2 0-1 0-1-1-2h0c1-2 1-4 2-6l2-12 2-11c1-5 3-10 4-15l1-2c0-1-1-1-2-1v-1l2-5h-1l-2-1-1 1h0c-2-3-4-5-3-9l-1-1c0-2 5-10 5-12 1 0 1-1 1-2l-1-1-1-1h1 0c-1 0-1 0-1-1h-2l-2-2v1l-1-1h-2c-3 1-5 0-8 1 0-1 1-1 1-1 2-3 4-2 7-2l2-1-1-1v-1h-3c2-1 4 0 6-1-2-4-5-7-9-9-7-3-16-2-22 1-9 3-16 11-19 19-2 5-2 9-3 14v30 11l-4-30-3-24c-1-8-1-16-4-23-2-7-7-15-13-20-4-3-9-4-14-4-6 1-12 4-15 9s-4 11-3 17c2 5 5 10 9 13l6 3h1c1 1 4 0 6 0h0c1-1 2-1 3-2 2-1 3-2 4-4l2-2h0v2c0 1-2 3-3 4s-2 1-3 2h-1l-2 1h-4 0c-1 0-3 1-3 0h-1-1c-2 0-4-1-5-1-5-3-9-6-12-11-4-8-4-17-4-25 0-5 0-11 1-16 0-11 2-21 2-32 0-3 0-8-1-11 0-2-1-2-2-3-4 1-5 5-7 8 0 0-1 0-1-1s1-2 1-3l2-6 3-11h-1v-3l3-9c0-2 1-4 1-7l9-26 1 1 6-14-1-1c0 1 0 2-1 3h0l1-4h-1c2-5 4-10 7-15l3-6c2-3 4-6 5-9 2-3 3-6 4-8 6-9 12-17 18-25 6-7 15-13 19-21v-3c0-2-1-3-2-5l1-1h-1c0-1 0-2 1-3 1 0 1-1 2-1l8-4 5-3 4-2z"></path><path d="M214 270h1v2h-1-1v-1-1h1z" class="D"></path><path d="M161 306l2-2v2 1c-1 0-1 0-2-1z" class="c"></path><path d="M228 289c1 1 1 2 1 2 0 1 1 0 1 2h-1v-1h0c-1 0-2-1-2-1h1v-1-1z" class="E"></path><path d="M197 275l2-1v1 1l-1 1h-1v-2z" class="F"></path><path d="M226 289h0 1v2h-1l-1-1 1-1z" class="E"></path><path d="M218 273c1 0 1 0 2 1v2h-1c0-1 0-1-1-2h0v-1z" class="D"></path><path d="M158 280h3v1l-1 1-1 1v-1h0v-1-1h-1z" class="F"></path><path d="M254 342h0c1 1 1 3 1 4v5-2c-1-1-1-3-1-4v-3z" class="N"></path><path d="M193 255v2l1 1v2h-1l-1-1v-3c0 1 0 1 1 1v-2z" class="B"></path><path d="M164 112l2-2h0v3l-1 1-1-2zm-5 206h1l1 1c-1 1-2 1-2 2v1h-1v-1c0-1 0-2 1-3z" class="F"></path><path d="M272 323l2 2-1 1h-3c0-1 1-2 2-3z" class="M"></path><path d="M129 301l1 1v1c-1 0-1 1-2 1h0-1v-1l2-2z" class="c"></path><path d="M106 304h0c1 1 2 4 2 5h0c-1 0-2-1-2-2-1-1-1-2 0-3z" class="N"></path><path d="M254 345c0 1 0 3 1 4v2 9c0-2 0-5-1-7v-4-4z" class="B"></path><path d="M162 287v1l-1 1h0c-1 1-1 1-2 1 0 1 0 1-1 1v-1c0-2 2-2 4-3z" class="c"></path><path d="M240 444h0c1-1 2-2 3-2h0l-2 5h-1v-3z" class="Q"></path><path d="M270 326h3l-1 3h-3l1-3z" class="O"></path><path d="M98 242c1-2 3-4 4-5h2v1c-2 1-3 3-5 4h-1zm22 75l1-2c0-1 1-1 2-1v2h0v1h1c-1 1-3 0-4 0h0z" class="B"></path><path d="M140 216c2-1 2-1 4 0h0l1 1c0 1 0 2-1 3-1-2-2-3-4-4z" class="Z"></path><path d="M112 262h0l2 2c0 1-1 3-2 4 0 0-1 0-1 1 0-1 0-4 1-5v-2z" class="W"></path><path d="M225 285c2 0 3 1 4 2l3 3c1 0 2 1 2 2h1l-1 1c-1-1-1-1-1-2-1 0-2-1-3-2h0-1-1v-1h0v-1c-1 0-1 0-1-1-1 0-1 0-2-1z" class="E"></path><path d="M190 335h1c1 1 2 2 3 4h-1c-2-1-4-1-5-2 0-1 1-1 2-2z" class="N"></path><path d="M142 145h1l-2 3v1 2c0-1 0-2 1-2h0l-2 5-2 3v-1l3-8c0-2 1-2 1-3z" class="AU"></path><path d="M116 268h1v1c0 1 0 2-1 2 0 1 0 1-1 2l1 1v1h-1l-1-1c-1-2 1-4 2-6z" class="B"></path><path d="M168 303h0 1v1c0 1-1 1-1 2s-1 1-1 2h-2v-1-1l3-3z" class="H"></path><path d="M176 239c-1-1 0-2 0-3v-1l4 11h-2c0-2-1-5-2-7z" class="d"></path><path d="M164 112l1 2 2 1c0 1 1 2 1 3h-1c-1 0-1-1-2-1h-1v-2h0c-1 0-1 0-2-1l2-2z" class="I"></path><path d="M247 414l1 2 2 6h-1v3c-1-2-1-5-2-7v-4z" class="R"></path><path d="M163 330h1s1 0 1 1c0 2-2 3-3 4l-1 1v-1c-1-2 1-3 2-5z" class="I"></path><path d="M229 385c0 1 0 2 1 2v2c0 1 0 1 1 2 0 1 2 3 3 4h1v1c-1 1-1 1-2 0h0l-3-3h0c1 0 1 1 2 1h0c-1-1-2-2-3-4v-3-2z" class="G"></path><path d="M229 387l-1-1h0c0-1 0-2-1-3v1h0l1 1v2 1h0 0l-1-2c0-1-1-1-1-2v-1-1h0-1v-2c1-1 1-1 2-1v2l1 1v2h1v1 2z" class="AA"></path><path d="M237 392c-1-3 0-6-1-9 0-2-1-4-1-7l1 1v-1l3 6-1 1c-1 2-1 3-1 6h0v3z" class="H"></path><path d="M217 346c0 1-1 0-1 1h-1-4v-1h-2 0l-1-1h-2 0c-1 0-1 0-2-1h0 2c1 0 2 0 3 1h0 1 5v1h1 1z" class="AC"></path><path d="M112 189h1c1 2 1 3 1 4 0 2 0 2-1 3h-1l-1-2c-1-1 0-3 1-5z" class="N"></path><path d="M206 318h0 0c-1 0-1-2-2-2v1l-4-10h0l1 1h0c0-1 0-2-1-2v-1h0l6 13z" class="w"></path><path d="M85 247l3-9 1 1-3 11h-1v-3z" class="O"></path><path d="M229 281c1 0 3 2 4 3s1 1 3 2h0-2c1 0 2 1 3 2h0-2c0-1-1-1-1-1-2-1-3-2-4-3l-2-2 2 1v-1c-1 0-1-1-1-1z" class="E"></path><path d="M221 363c2 0 4 2 5 3h-5c-1-1-2-1-4-1l1-2h2 1z" class="F"></path><path d="M228 287h-1v1c-1 0-2 0-2-1h-2c-1-1-1-1-2-1s-1 0-1-1 0-1 1-2c1 0 1 0 2 1h0c1 0 1 0 2 1h0c1 1 1 1 2 1 0 1 0 1 1 1z" class="C"></path><path d="M120 171l1-2 3-4c1 0 2 0 3 1 0 0-1 0-1 1-2 1-4 4-6 6v-2z" class="W"></path><path d="M235 439l1-1 1 1c-1 2-1 3-2 4s-1 1-1 2h0 1v2 2h-1-1l1-1-1-2c0-3 1-5 2-7z" class="v"></path><path d="M272 323c1-1 1-1 2-1l1 2c1 0 3-1 3 0h2 2v2h3 0c-4 0-7 0-10-1h-1l-2-2z" class="G"></path><path d="M205 275l-1-1h0-1c1 0 0-1 1 0h1 1 1v2h0c-1 1-1 2-1 3l-2 2c-1-1-1-1-1-2s1-2 2-3v-1z" class="E"></path><path d="M192 265h-1v-4c1 0 1 0 2 1h-2v2h2c1-1 2-2 2-3h0v-1-1c1-1 1 0 1-1h-1v-2c2-2 3-3 5-3-2 2-3 4-4 6-1 3-1 4-4 6h0z" class="N"></path><path d="M235 439c1-2 3-6 5-7h0v1 1 1c0 1-1 1-1 2l-2 2v1c0 1-1 1-1 2s-1 1-1 2v1h-1 0c0-1 0-1 1-2s1-2 2-4l-1-1-1 1z" class="T"></path><path d="M302 338h1 0c0 1 0 3-1 4s-2 1-2 3l1 1h0-1v-2c0-2 1-2 1-3 0 1-1 1-2 2 0 0-1 1-2 1v-1c-2 0-2 1-3 1s-1 0-1-1c-1 0-1-1 0-1v1c2 0 1 0 2-1 1 0 3 1 5 0v-1c1-1 2-2 2-3zm3 22h0c1-1 1-1 2-1 1-1 1-1 1-2 1-1 3-2 4-2h1c0 1 1 2 0 2-1 1-3 2-4 2l-2 1c-1 1-1 1-2 1v-1z" class="I"></path><path d="M240 444h0v3h1l-1 3h-2c0 2-1 5-2 7v-1-3-1s0-1 1-2v-1h0 0 1v1-1c0-2 1-3 1-4l1-1h0z" class="T"></path><path d="M240 447h1l-1 3h-2c1-1 1-2 2-3z" class="i"></path><path d="M240 444c0-1 0-1 1-2v-1h1v-3c2-1 2-3 3-5 1 1 0 1 0 2h1 0v1l-3 6h0c-1 0-2 1-3 2h0 0z" class="q"></path><path d="M170 326h1 1 0c1 1 2 1 2 2v2h-1-1c-1 0-1 1-2 1l-2-2c1-1 2-2 2-3z" class="F"></path><path d="M275 325c3 1 6 1 10 1v1c-1 1-3 1-4 1-2 0-5 0-7-2l1-1z" class="C"></path><path d="M115 196h1c1 1 2 3 3 4 0 1-1 2-1 3h-1c-1-1-2-1-2-3-1-1 0-2 0-4z" class="N"></path><path d="M185 91l2-1h3l1 1-6 3-2 1-1-1c0-1 0-2 1-2 1-1 1-1 2-1z" class="r"></path><path d="M239 382v1h0l1 2v-2h-1l1-1c0-2-1-5 0-7 0-1 2-3 3-4-1 6-3 11-3 17h0l-2-5 1-1z" class="F"></path><path d="M198 322h1c1 2 1 4 1 5s-1 2-1 3l-3-2c0-1 1-2 1-3s1-2 1-3zM97 249l1 1v1c-1 0-1 0-1 1h0c0 1-1 1-1 2 0 2-1 3-2 4h0-1v-1c0-1 1-2 0-3h-1 0c0-1 1-1 2-2l-1-1v-1c1 1 2 2 3 2 1-1 1-1 1-3z" class="W"></path><path d="M244 407s1-1 1-2v5l1 1h0c0 1 0 2 1 2v1 4c-1 0-1-2-1-2-1-4-3-6-5-8 1 0 2 0 3-1z" class="F"></path><path d="M171 309h0c1 1 1 1 1 2l1 1h-1v1c-1 1-1 3-1 5 1-1 2-3 2-4v-1h2 1 0l1 1 1 1h2 1c-1 1-1 1-2 1h-1 0-2 0-1 0v-2h-1v1 1c-1 0-2 1-2 2h0c-1 2-1 3-1 4 0-3-1-7 0-10 0-1 1-2 0-3h0z" class="D"></path><path d="M145 217h2v7 3c0-1 0-3-1-4h0-1c0 3 0 7-1 9 0-4 1-8 0-12 1-1 1-2 1-3z" class="d"></path><path d="M133 213v-1l4 1h1l-1 1c3 1 5-1 8 0l-1 2h0c-2-1-2-1-4 0l-5-1c0-1-1-1-2-2z" class="O"></path><path d="M214 358c3 1 5 2 7 5h-1-2l-1 2-2-2c0-1 0-2 1-3h-1l-2-1 1-1z" class="M"></path><path d="M216 360h1v1 1l1 1-1 2-2-2c0-1 0-2 1-3z" class="D"></path><path d="M205 354v1l9 3-1 1c-2-1-3-1-5-2h-5l-1-1v-1h-3c2-1 4 0 6-1z" class="d"></path><path d="M220 393v-1l1-1v-1c0-1 1-2 1-3 0 1-1 1-1 2h-1v-1h1-2v1c0 1 0 1-1 2-1-1 0-2 0-3h0v-1h0c1-1 2-1 3-1l1-1c0-1 1-2 2-2v-1h1l-1 1h0l-2 7c-1 1-1 2-2 3h0z" class="t"></path><path d="M183 333h1c0 1-1 1 0 2 1 2 3 2 3 4h-2-5c1-2 3-4 3-6h0zm-59-77h1-1c-1 1-1 1-1 2l1-1v1l-1 2h0v1c0 1 0 1-1 2h0l-2 2-2-1c0-1-1-2-1-3h0 1l1 1c2-2 3-4 5-6z" class="F"></path><path d="M285 326h4c-1 0-1 1-2 2s-2 2-4 2h0-5v-1c1-1 2-1 3-1s3 0 4-1v-1h0z" class="N"></path><path d="M98 242h1c2-1 3-3 5-4l-3 3-2 2c0 2-1 5 0 6 0 1 0 1-1 1l-1-1c0-1-1-3-1-4s1-3 2-3z" class="F"></path><path d="M173 235l2 3 1 1c1 2 2 5 2 7 1 3 0 5 1 8v1c-1-2-1-5-2-7-1-4-3-8-4-13z" class="R"></path><path d="M175 302l1-1h-1l-1-2c-1-1-5-4-4-5v-1l-1-2v-1-1 1h1c0 5 7 7 7 12h-2z" class="U"></path><path d="M174 336h5v3c-1 1-1 1-2 1l-5 1c1-2 1-4 2-5z" class="c"></path><path d="M166 291l1-1h0l1-1v2 2h0c0 3-1 5-3 7 0 1-1 2-2 3h0c-1-3 3-5 3-8 0-1 0-2-1-3v-1h1z" class="E"></path><path d="M165 258c1-3 2-4 4-6 1-2 1-4 2-6 0 3-1 6-1 10h0c0 1 0 2-1 3 0 1 1 2 1 3v1l1 1c-1-1-2-3-3-4l1-1c0-2 0-2-1-3v1h0v-1h-1l-1 2h-1zm61 74c4 0 7 5 10 7l5-5c1 0 3 1 5 2h0c-5 0-6 1-10 4-1-1-2-3-4-4-1-1-4-2-6-2 0-1 0-1-1-2h1z" class="F"></path><path d="M277 335c2 0 3 0 4-1l1 1h2v-1h0c2 0 5 0 7-1h0c0 1-1 2-1 2 1 1 1 0 2 0h5c-1 1-2 1-4 2h0-5-1l1-1h-8v-1h-3z" class="U"></path><path d="M288 336h0 3v1h-3-1l1-1z" class="C"></path><path d="M254 327c1-2 1-5 2-8 0 8-2 16-2 23h0 0v-3-2-1-1 3c-1 2-1 4-1 6v-3-4l1-10z" class="AM"></path><path d="M219 348v-1c1 1 1 2 2 2v1c0 1 1 1 1 2v1h0l3 7v2h-1c0-1 0-1-1-2 0 0-1-1-1-2h0v-2h-1 0v-1c-1-1-1-2-2-3v-1h0c0-1 0-1-1-1v-1c0-1 0-1 1-1z" class="J"></path><path d="M236 263c1 1 2 2 3 4s3 4 3 7v1l-3-3h0c-1-1-1-2-2-2l-3-6h1l2 2h0 1l-2-3z" class="F"></path><path d="M159 311h0c-2-1-1-7-2-9l1-1c1-1 2-3 3-4l1 1c-1 1-2 2-2 3-2 2-1 4 0 6h0l1-1c1 1 1 1 2 1h-1s-1 0-1 1h1v2s-1 0-1 1h-2z" class="N"></path><path d="M159 311v-1h1c0-1 0-1 1-2h0v3h-2z" class="B"></path><path d="M120 171v2 2c-2 2-2 3-2 5-1 2-2 4-3 4-1 1-1 1-2 1 0-5 5-10 7-14z" class="G"></path><path d="M218 268c1 0 1 0 2 1l2 1c1 1 1 1 2 1v1l2 1c0 1 1 2 1 3h0l1 1h-1-1c-1-1-1-1-2-1v-1c-1-1-1-1-3-1v-1c-1 0-1 0-1-1-1-1-2-2-2-4z" class="E"></path><path d="M203 357h5c2 1 3 1 5 2l2 1h1c-1 1-1 2-1 3-4-2-9-4-13-4l-1-1 2-1z" class="P"></path><path d="M192 322c1 1 1 1 2 1h1c1 1 1 1 1 3-1 2-2 2-3 4-1 0-1 1-2 1-1 1-1 1-2 1l-1-1h1v-1l2-2v-2c1-1 1-2 1-4z" class="G"></path><path d="M193 324h2l-1 1h-1-1l1-1z" class="F"></path><path d="M210 430h-1c-1 1-1 2-2 3v-1l1-2c1-1 0-3 1-5h1v-2h0c0-1 1-2 1-3l1-3h0c0-1 1-1 1-2s-1 0 0-1c0-1 0-1 1-2v-2c1-1 1-1 1-2v-1l1-2c0-1 0-1 1-2v-2l1-1c0 2-1 3-1 4v2l-2 5c-1 1-1 2-2 3v2 1h0l-1 1c-1 3-2 5-1 7v1h1v1c-1 1-2 1-2 3z" class="Q"></path><defs><linearGradient id="Al" x1="256.571" y1="358.158" x2="249.911" y2="362.998" xlink:href="#B"><stop offset="0" stop-color="#52413f"></stop><stop offset="1" stop-color="#635b53"></stop></linearGradient></defs><path fill="url(#Al)" d="M252 367l2-18v4c1 2 1 5 1 7v9l-1-1h-2v-1z"></path><path d="M225 468h1v9 2 1c0 1 1 2 0 4l1 1h0v2 2h0v1l1 1v1 1c0 1 1 1 1 2v1c0-1 0-2-1-3v-3h1v2l1 1h0v3c0 1 1 2 1 3h0v3c0 1 0 2-1 3-3-12-5-24-5-37z" class="L"></path><path d="M155 214h3c-1 1-2 1-3 2h-1c0 1-1 2-2 2l-1 1c-1 0-2 2-3 3 0 1-1 1-1 2h0v-7c0-1 1-1 1-2l7-1z" class="J"></path><path d="M181 321c-1 0-1-1-2-2v-1l1-1c0-1 0-1 2-1l1 1h0c0 2 0 3-1 4h1 0 1c1 0 1 1 1 1 1 1 2 2 1 3l-1 1c-1 1-1 1-2 1l-2-2c0-1 0-1 1-2 0-1-1-1-1-2z" class="c"></path><path d="M182 323l1-1h1l-1 1c0 1 0 1-1 1v-1z" class="D"></path><path d="M181 317h1v3l-1 1c-1-1-1-1-1-3 0 0 0-1 1-1z" class="E"></path><path d="M294 355l2-2c1 0 1 1 1 1 1 0 1-1 2 0l1 2c1 2 0 3-1 4 0 1 0 1-1 2h-1-2s-1 0-1 1h-1 0v-1s1-1 2-1h0 2c1-1 3-2 3-3s0-1-1-2l-1-1h0v2s-1 0-1 1h-1v1h-2-1 0c0-1 0-1-1-1 0 0-1 1 0 2h0v4c0-1 0-3-1-3v-3c1-1 3-2 3-3z" class="D"></path><path d="M296 358h-2c1-1 2-2 3-2h0v2h-1z" class="E"></path><path d="M177 302c1 1 4 2 5 3v1c1 1 1 2 1 3 1 2 3 4 3 6l-2 2h-1v-3-1l-2-5c0-1-1-2-2-2-1-1-2-1-2-1-1-1-1-1-2-1h-1v-1-1h1 2z" class="C"></path><path d="M283 349h2 3l1 1c1 0 2 1 2 2 1 1 1 1 1 2h1 0l-1 1v1l2-1c0 1-2 2-3 3v-6h-1v1c-1 0-1 0-1-1l-1 1c-2 0-3 1-5 1l-2-2c0-1 1-2 2-3z" class="F"></path><path d="M229 404v-1h-1v-1h0c-1-1 0-3 0-4h-1v-2l2-2c0 1 1 1 1 2h0l1 1c0 1-1 2 0 3v2 2h0v1s0 1 1 1v2 1l-1 1h0c-1-1-1-2-1-3s1-1 1-1l-1-1c0 2 1 4 0 5h0 0c0-2 0-4-1-6z" class="AB"></path><path d="M213 264h0v1c1 0 1 1 1 2v1h-1l-3 3h-1c-1 1-1 1-2 1l-2-2c1-1 1-1 2-1h0 1l1-1c1 0 2-1 2-2v-1-1c-1-1-1-2-2-3l-1 1v-1c0-1-1-2-2-2s-1 0-1-1v-1h2 2l1-1v1l-1 1h-1c-1 0-2 0-2-1v1c1 0 3 2 4 3v-1c1 2 1 3 3 4z" class="U"></path><path d="M302 307c1 0 2 1 2 1h1c1 1 1 2 2 3 1 0 1 0 1 1 1 1 2 1 2 2l1 1 1 2h0c1 1 1 2 2 3h1s0 1 1 1v-2-1-1h0l-1-1-1-1c1 1 2 1 2 2h0c0 1 1 3 1 4 1 1 1 2 2 2l2 1 2 2v1h-1-1c0-1-1-1-2-1v-1h-4c0-1-1-2-1-3-2-3-3-6-6-9l-6-6z" class="i"></path><path d="M215 397h1 0v1h-1v1h0 1v2c-1 1-1 1-1 2l1-1h0v2l-1 1v2l-1-1c0 1 0 1-1 2h0v2h-1c0 1 0 1-1 2v-1c1-1 0-1 1-2v-1h0-1v1l-1 2-1 5v1c-1 1-1 1-1 3h0c-1 1 0 3 0 4-1 1-1 3-1 5v1h-1c2-4 0-9 2-13l3-10c1-3 3-7 4-10z" class="X"></path><path d="M203 249h1 1c0 3-4 6-6 9l-3 4v-1h0v1s1 1 1 0c1 0 2-1 2-2l1 1c-2 2-4 4-6 5h-1c-1 0-1 0-1-1h0c3-2 3-3 4-6 1-2 2-4 4-6 1-2 2-3 3-4z" class="D"></path><path d="M203 249l1 1c-2 2-3 4-5 6 0 1-1 2-2 3h-1c1-2 2-4 4-6 1-2 2-3 3-4z" class="Z"></path><path d="M234 246h1c1 1 1 1 2 1 1-2 1-3 2-4v2c0 1-1 1-1 2h-2-1-1c-2-1-5-1-6-1-1 1-2 1-2 3h1v2c0 1 1 1 1 2l1 1c1 0 1 1 1 1l3 2 3 6 2 3h-1 0l-2-2h-1l-4-5c0-1-1-2-1-2l-3-7v-2l1-2h6 1z" class="H"></path><path d="M228 282c-1 0-3-1-4-1h0l-1-1h-1c-1 0-1 1-2 1v1h-1l1 1h-1c-1-1-2-1-3-3v-2l1-1h1 1v1h1 2v-1l2 1c2 0 3 1 5 2v1s0 1 1 1v1l-2-1z" class="E"></path><path d="M224 278c2 0 3 1 5 2v1s0 1 1 1v1l-2-1h0c-2-2-5-3-7-2h-2v-1c2-1 4-1 5-1z" class="C"></path><path d="M119 207l-1-1c3-3 5-9 7-14v-2s1 0 1-1v-2c1-1 1-2 1-3 1-1 1-1 1-2v-2 1c-1 3-3 5-4 8 0 2-1 3-1 4l-3 6h-1 0l1-1 3-6c2-6 4-12 7-17 1-3 3-5 5-8l1 1c-3 4-6 9-7 13-3 6-4 13-7 19 0 1-2 4-2 5l-1 2z" class="AU"></path><path d="M180 246l5 17v3h-1c0-1-1-2-1-2-2-3-3-6-4-9v-1c-1-3 0-5-1-8h2z" class="O"></path><path d="M252 367v1h2l1 1c0 4 1 7 4 9v1h-1v1h-2-1 0-1-3l1-1c1-4 0-8 0-12z" class="d"></path><path d="M256 380h0c-1-1-1-1-1-2v-1l3 2v1h-2z" class="U"></path><path d="M168 257h0v-1c1 1 1 1 1 3l-1 1c1 1 2 3 3 4v1 1c0 1 0 2 1 3h0c1 1 1 2 2 3 1 0 1 1 1 1l5 8v1l-2-1-6-10c-2-2-4-4-5-6v-3-1c1-1 1-2 1-4z" class="c"></path><defs><linearGradient id="Am" x1="235.614" y1="276.105" x2="236.621" y2="267.037" xlink:href="#B"><stop offset="0" stop-color="#2d2c2f"></stop><stop offset="1" stop-color="#4c4745"></stop></linearGradient></defs><path fill="url(#Am)" d="M230 259l4 5 3 6c1 0 1 1 2 2 1 2 2 3 1 5v1l-1-1-2-3c-1-2-3-4-5-6l-4-8c1 0 2 1 2 2h1c1 1 3 5 4 6l-1-2-1-2c-1-2-2-3-3-5z"></path><path d="M98 205l1 1-10 33-1-1c0-2 1-4 1-7l9-26z" class="m"></path><path d="M201 87l1 1c-4 3-7 6-11 10l-3 3-1-1c-1 1-2 2-3 2v-2c0-1-1-1 0-2s3-1 4-1c2-3 6-5 8-7 2-1 3-2 5-3z" class="H"></path><path d="M230 445l3 4h1-2c-1 1-1 2-2 3 0 1 1 1 0 2v5c-1 0-1 1-1 2h0v2h0v1c-1 1 0 2 0 3l-2 2h0 0l-1-1h0-1l1-3 2-12c1-2 2-5 2-6l-1-1 1-1z" class="AB"></path><path d="M139 163l-1-1c3-6 6-12 10-18 2-3 6-7 8-11 1-2 2-3 4-4v1h-1c0 1-1 1-1 2l-1 1v1l1-1c0-1 2-2 2-3l1-1c1-1 2-2 2-3h0l1-1 1-1h0l-1 3-12 14c-1 2-3 4-5 7l-5 9-1 2-2 4z" class="AU"></path><path d="M129 181v1c0 1 0 2-1 3l-1 3c1 0 1-1 1-2h0c1-1 1-1 1-2l1-1-2 7 1 1c-1 2-2 5-3 8-1 1-3 9-4 10l-2-1c0-1 1-2 0-3 0-1 2-4 2-5 3-6 4-13 7-19z" class="N"></path><path d="M126 199c-1-1-1-2 0-3 0-2 1-4 2-6l1 1c-1 2-2 5-3 8z" class="G"></path><path d="M261 283c-5-2-10-5-13-9-1-1-2-2-2-4 2 3 5 6 9 8l2 1 2 1 2 1c7 4 14 6 21 8l2 1-2 1c-5-1-9-3-14-5-1 0-3-1-5-2-1 0-1-1-2-1z" class="V"></path><path d="M261 283c1 0 1 1 2 1 2 1 4 2 5 2 5 2 9 4 14 5 3 1 6 1 9 3h-1c1 1 1 1 2 1 0 0 1 0 1 1h-1c0 1 1 1 1 2-2 0-4-2-6-2l-15-7c-4-1-8-3-11-6z" class="AT"></path><path d="M314 315h0c-1 0-3-1-3-2h1c0-1-1-1-1-2h-1c0-1-1-1-1-1-1 0-1-1-2-1h0 2 0c1 1 2 1 3 2 1 0 1 0 2 1h0 2 1s1 0 1 1v1h1c1 2 1 3 3 5 1 0 2 2 3 2 0 0 1 0 2-1 1 1 2 1 2 2h0l1 1c1 1 1 2 2 3 0 0 0 1 1 2h-1s-1 0-1-2h0c0-2-2-2-3-4h-2c-1 0-1 0-1 1l-1-1v1h1v1c0 1 0 1 1 1h2-2c-1 1 0 3 0 4v-1c-1-5-6-9-10-11 0-1-1-1-2-2z" class="K"></path><path d="M224 383l1-1v2c0 1 1 2 1 2v1 4h0c0 2 1 3 1 4s-1 1-1 1h-1c0 1 1 1 1 1v2l1 1h0v2h0l1 1v1l-1-1c0-1-1-3-1-4s0-1-1-2h0l-2 2h0l-1-1v2h-1v-3h0l-1 2h-1c0-2 1-3 1-5v-1c1-1 1-2 2-3l2-7z" class="J"></path><path d="M222 394h1v2l-1 2v2h-1v-3h0l-1 2 2-5z" class="i"></path><path d="M222 394l3-7v6c1 1 2 0 1 2l-3 1v-2h-1z" class="T"></path><path d="M169 306h1s1 0 1 1-1 3-1 4h-1c0 1-1 3-2 4v1l-1 1c1 0 1-1 2-1 1 1 1 0 2 1 0 3-1 6-2 9 0 1-1 1-1 2l-2-2v-3h1c0-1 0-1 1-1 0-1 1-1 1-2l-2 1c0 1-1 1-2 1-3-3-1-7-2-11l1-1h0v-1h1l-1 2h0v7h1s0-1 1-1c1-1 1-2 2-3l1-2h0c0-1 0-1 1-1v-2h1-1v-1h0 1v-1h0-1v1h-1l1-2z" class="F"></path><path d="M167 317h3v1c-1 0-2 1-2 1l-1 1h0-2c0-2 1-2 2-3z" class="B"></path><path d="M201 442c1-2 1-4 2-6l2-12 2-11v2 1 1s0 1-1 1l1 1h-1v2 2c-1 1-1 3-1 4v2h-1v2c0 1 0 2-1 3l1 1h-1v2 2l3-3c1-1 2-2 2-3 1 0 2-2 2-3h0c0-2 1-2 2-3 1 0 1 0 2-1h0 1c1 1 1 1 1 2-3 5-7 9-11 12l-2 2c0 1 0 1-1 2 0-1 0-1-1-2h0z" class="T"></path><path d="M215 426c1 1 1 1 1 2-3 5-7 9-11 12l-2 2v-1c0-1 1-2 2-2l6-8c1-2 2-3 3-5h1z" class="S"></path><defs><linearGradient id="An" x1="249.775" y1="355.94" x2="254.423" y2="356.826" xlink:href="#B"><stop offset="0" stop-color="#9e9487"></stop><stop offset="1" stop-color="#d6cbc5"></stop></linearGradient></defs><path fill="url(#An)" d="M253 341v3c0-2 0-4 1-6v-3 1 1 2 3h0v3 4l-2 18c0 4 1 8 0 12l-1 1-1-1c-1-1-1-1-1-3h0v-6c2-9 2-19 4-29z"></path><path d="M231 499c0 1 0 2 1 3v1 1-1h0v-2l3 8c1 3 1 6 3 9h0v1h0l-1 1 2 2c1 1 0 1 1 2l1 1 1 2c1 1 1 2 2 3-3-2-5-6-7-8l1 2-1-1v1 1l-3-6-1-2h0l1-2-1-2-1-3-2-5c1-1 1-2 1-3v-3h0z" class="K"></path><path d="M234 515h0l1 3-1 1-1-2h0l1-2z" class="S"></path><path d="M234 512c1 1 1 2 1 3h0-1 0l-1-2c0-1 1-1 1-1z" class="v"></path><path d="M233 507l1 5s-1 0-1 1l-1-3h1v-3z" class="Q"></path><path d="M235 518l2 4 1 2-1-1v1 1l-3-6 1-1z" class="f"></path><path d="M231 499c0 1 0 2 1 3v1 1l1 3v3h-1l-2-5c1-1 1-2 1-3v-3h0z" class="T"></path><path d="M277 335h0 3v1h8l-1 1h1 5 0c-1 1-1 0-2 1-1 0-1 1-2 1s-2 1-3 1c0 1-3 1-4 1l-1-1v-1c1 0 1 0 2-1l1 1 1 1v-1l1-1h-2-2s-2-1-3 0v-1h-1v2c1 1 1 1 2 1v1h1c1 0 1 1 2 1l-1 1c-1 0-2 0-2 1h-5l-1 1c-1 0-1 0-1-1 1-1 2-2 2-4 1-1 1-3 2-5z" class="E"></path><path d="M280 336h8l-1 1h0-1-4-3v-1h1z" class="AO"></path><path d="M277 335h0 3v1h-1v1h-1c-1 1-1 2-1 3v3 1l1-1s1 0 1-1l1 1v1h-5l-1 1c-1 0-1 0-1-1 1-1 2-2 2-4 1-1 1-3 2-5z" class="M"></path><path d="M315 307c3 1 6 3 9 5 4 3 6 6 9 9 1 2 2 3 3 6v3 3c1 1 0 4 0 6 0-1 0-2-1-3h0c-2-2-3-2-5-3h-2l1-1s-1-2-1-3l-2 2v-2c0-1-1-3 0-4h2 1c0 1 0 1 1 2h0l-1 1c0 3 2 3 3 4h0-2v-1h-1l1 1h0 1 0l2 2h1 0l1 1 1-1c-1 0-1 0-1-1v-2-1l-1-1v-2c-1 0 0 0 0-1l-1-1v-1c0-3-3-3-4-5 0-2-3-4-4-5h-1l-2-2c-1-1-2-2-3-2-2-1-3-2-4-3h0z" class="z"></path><path d="M110 210c4 1 7 1 10 4 1 2 2 4 4 6l-3 6-6 11c-2 4-3 8-5 12v-2-1h0l-1 1h0l3-7c0-1 2-3 2-5l1-1 1-2v-1l1-2c1-4 3-9 3-13l-1-1h-1v2c0 1 0 1-1 2v-2c0-1 0-1 1-1v-2c0-1-1-1-1-1l-1-1h0c-1 0-2 0-3-1h-3c0 1-1 0-1 1h-1-1-1c1-1 3-1 4-1v-1z" class="G"></path><path d="M291 304c0-1 1-1 2-1h1 1 0l1 1c0-1 1-1 1-2l2 2 3 3 6 6c3 3 4 6 6 9 0 1 1 2 1 3l1 3h0c3 11 3 21 3 33 0 4 0 8-1 12-1-2 0-9 0-11v-1h-1 0c-1 0-2 0-3-1h4v-9c-1 0 0-1 0-2v-2-1-4c-1-1 0-4-1-6v-3-2c-1-1-1-1-1-2s-1-2-1-3v-1h0c-1-3-2-5-4-7v-1l-1-1-1-1c-1-1-3-4-5-5l-2-2h-1c0-1 0-1-1-1l-1-1-1-1c-1 1-4 0-6 1h0c-1 0-3 1-4 2l-1 1-1-1c1-2 3-3 5-4z" class="AC"></path><path d="M291 304c1 0 2-1 3-1s1 1 2 2h0-1c-2 0-3 0-5 1-1 0-3 2-4 2 1-2 3-3 5-4z" class="W"></path><defs><linearGradient id="Ao" x1="166.733" y1="218.877" x2="179.537" y2="226.847" xlink:href="#B"><stop offset="0" stop-color="#333335"></stop><stop offset="1" stop-color="#69605a"></stop></linearGradient></defs><path fill="url(#Ao)" d="M171 213c1-1 1-2 2-2v1h5c-2 1-3 2-4 4h-1c-2 4 2 14 3 18v1 1c0 1-1 2 0 3l-1-1-2-3c-1-3-1-6-2-9-1-4-1-7-4-10 1-1 2-1 3-1 0-1 1-2 1-2z"></path><path d="M175 238v-3l1-1v1 1c0 1-1 2 0 3l-1-1z" class="O"></path><path d="M115 299v-1h1v1c1 1 2 2 2 4 0 1 1 1 1 2 0 0 0 1 1 2 0 1 1 2 2 2l1 1 1-1v1l1 1 2-2v1h0c-1 0-2 1-2 1v1h1 1c0 1 0 1 1 1-1 1-1 2-1 2-1 1-1 1-2 1 0-1-1-1 0-2v-1h-1c0 1 0 1-1 1s-2 0-2 1l-1 2c0-1-1-1-1-1h-1v-1c-1 0-1 0-2-1v-1l1 1v-1s-1-1-1-2v-1h1v-1l-1-1 1-1c-1-1-2-2-2-4h0c-1-1 0-2 0-4z" class="J"></path><path d="M297 297l18 5 4 2c1 0 2 1 3 2h-3 0v2h-1 2c1 1 3 2 4 4-3-2-6-4-9-5l-13-5c-3-1-7-2-9-4h2 1 0l1-1z" class="AT"></path><path d="M315 302l4 2c1 0 2 1 3 2h-3s-1 0-1-1c-1 0-2-1-4-1 1 0 1 0 1-1h0v-1z" class="AX"></path><path d="M297 297l18 5v1h0c0 1 0 1-1 1-2-1-4-2-6-2h-1c-1-1-1 0-2-1l-9-3 1-1z" class="AV"></path><path d="M205 285h1 0s1 0 1 1c2 2 4 5 7 7 1 0 1 1 2 1v1l2 1c1 0 2 2 3 3 0 1 1 1 1 1l3 3s1 1 2 1l3 6h0c1 2 1 3 1 5h-1s0-1-1-1c0-2-4-7-5-8h-3-2 0c-2 0-4-1-5-1-1-1 0-1-1-1s-1 0-1-1h1c3 1 6 1 9 1v-1c-1-2-3-4-5-5-3-3-7-5-9-8v-1c-1-1-2-2-3-4z" class="H"></path><path d="M219 306c-2 0-4-1-5-1-1-1 0-1-1-1s-1 0-1-1h1c3 1 6 1 9 1 1 0 1 0 1-1 2 0 3 2 4 3l3 5v1c-1 0 0 1-1 0l-1-1v-1l-3-5-6 1z" class="C"></path><defs><linearGradient id="Ap" x1="322.912" y1="323.402" x2="315.569" y2="329.75" xlink:href="#B"><stop offset="0" stop-color="#3b0c0f"></stop><stop offset="1" stop-color="#6b1718"></stop></linearGradient></defs><path fill="url(#Ap)" d="M316 317c4 2 9 6 10 11v1 2c-1 0-1 1-2 2l-2 3-2 2c-1-1-1-2-1-3-1-3-1-5-2-8l-1 1h0l-1-3h4v1c1 0 2 0 2 1h1 1v-1l-2-2-2-1c-1 0-1-1-2-2 0-1-1-3-1-4h0z"></path><path d="M210 260l3-4h1l1-1c1-2 3-3 4-4 0-1 0-2-1-2v-1c-1-1-1-1-1-2-1 0-1 0-1-1-1-2-3-3-5-6v-1h-1v-1c2 3 9 11 9 13 0 1 0 2-1 3h2l1-1h1l6 12c0 1 1 3 2 4h2c2 2 4 4 5 6l-1 1c1 2 2 3 2 5l-1 1v1l3-2h0c0 1 0 2-1 3h0c0 1 0 0-1 1 0 1 0 1-1 2h1 0c1 1 1 1 1 2 1 0 1 1 2 1v2l-1 1-1-1h1v-1c-1-1-1-2-2-2v-1h-1c-1-2 0-2 1-3v-1h0l-2 3c-2-1-2-1-3-2 1 0 1 0 1 1h1 1l1-2c0-1 0-1-1-2v-1h1c0-2 0-3-1-4l-2-2-1-1c-1-2-2-3-3-5-1 0-1-1-2-2h0c-1-1-1-3-2-4-2-3-3-6-5-9h0c-1 0-1 1-1 1l-3 4c-1 1-2 2-2 3-1 1-1 2-2 3-2-1-2-2-3-4z" class="B"></path><path d="M218 253h2c-1 2-2 3-4 5-1-1-1-1-1-2 1-1 2-3 3-3z" class="I"></path><path d="M230 268h2c2 2 4 4 5 6l-1 1-6-7z" class="G"></path><defs><linearGradient id="Aq" x1="146.112" y1="247.738" x2="143.758" y2="247.603" xlink:href="#B"><stop offset="0" stop-color="#84796e"></stop><stop offset="1" stop-color="#9f9588"></stop></linearGradient></defs><path fill="url(#Aq)" d="M144 232c1-2 1-6 1-9h1 0c1 1 1 3 1 4l-1 47c-1 2 0 5 0 8h-1v-9l-1-1v4-24-20z"></path><path d="M156 271h1c1 0 3-2 4-2 0 0 1 1 1 2 2 1 5 3 6 5h0v1c1 0 1 0 2 2-1 1-1 2-1 3-1 1-1 1-1 2h0l-1 1v2l-1 4h-1c0-3 1-6 2-8 1-1 2-3 1-4-2 1-3 6-5 8v1c-1 1-1 1-2 1l1-1v-1c1-1 0-3 0-5v-2-1c1-1 1-1 1-2l-9-3c1-1 1-2 2-3z" class="F"></path><defs><linearGradient id="Ar" x1="115.886" y1="245.242" x2="111.92" y2="243.869" xlink:href="#B"><stop offset="0" stop-color="#373638"></stop><stop offset="1" stop-color="#605953"></stop></linearGradient></defs><path fill="url(#Ar)" d="M124 220s0 1 1 2c-10 17-16 36-22 56v-2-2l1-2v-1c0-1 0 0 1-1 0-1 0-2-1-3v1 1c-1 1-1 2-1 2v2h-1c2-5 3-10 5-15l2-7 1-2c2-4 3-8 5-12l6-11 3-6z"></path><path d="M146 274c1 4 0 9 0 13l1 37h0v-5h-1v5l-3-45v-17c0-3 0-6 1-10v24-4l1 1v9h1c0-3-1-6 0-8z" class="R"></path><path d="M170 204v2c1 2 2 4 3 5-1 0-1 1-2 2 0 0-1 1-1 2-1 0-2 0-3 1-3-2-6-2-9-2h-3l-7 1c0 1-1 1-1 2h-2l-1-1 1-2 1-4 1-2 1 1v1h0l1 1c1 0 1 1 2 1s5 0 6 1l6-2c1-1 2-2 4-3 0 1 0 1 1 1l1-2c0-1 0-2 1-3z" class="x"></path><path d="M148 210l1 1c1 0 1 1 2 1s5 0 6 1l-7 1-2-4z" class="G"></path><path d="M146 210h1v1c0 1 1 2 1 3v1h0c0 1-1 1-1 2h-2l-1-1 1-2 1-4z" class="n"></path><path d="M170 204v2c1 2 2 4 3 5-1 0-1 1-2 2 0 0-1 1-1 2-1 0-2 0-3 1-3-2-6-2-9-2h-3 10 2 1v-1s0-1-1-2h-4c1-1 2-2 4-3 0 1 0 1 1 1l1-2c0-1 0-2 1-3z" class="C"></path><path d="M170 204v2c1 2 2 4 3 5-1 0-1 1-2 2-1-2-1-4-2-6 0-1 0-2 1-3z" class="AQ"></path><path d="M221 366c2 3 6 4 8 7 1 2 2 4 2 6 1 1 1 3 2 4 1-1 0-2 0-4-1-2-1-6-3-8v-1-2h0c0 1 1 1 1 2h0v1 1c1 0 1 1 1 1 0 1 1 2 1 3v1 2l1 1v3c1 2 0 3 0 4-1 0-1 1-1 1l-1 1c-1 0-1-1-1-1 0-1 0-1-1-2v-2c0-1-1-1-1-2s-2-4-3-5v-1c0-1-1-1-2-2-1-3-4-4-6-6h0l-1-1v-1h-1v-2c-1 0-2 0-2-1h-1l-1-1-2-1h0-2l-1-1h-1-1 0l-1-1h-1-3 1 1c4 0 9 2 13 4l2 2c2 0 3 0 4 1z" class="w"></path><path d="M222 398l1 1h0l2-2h0c1 1 1 1 1 2s1 3 1 4l1 1h1c0 2 0 2-1 3v4h0c0 1 0 2-1 2l-2 2v1c-1 1-2 2-3 2s-1 0-2-1h0c0-2 0-3-1-4v-1l2-12h1v-2z" class="q"></path><path d="M219 412c1-1 1-1 2-1 1 1 1 1 1 2h-3v-1z" class="h"></path><path d="M222 398l1 1h0l2-2h0c1 1 1 1 1 2s1 3 1 4l1 1h1c0 2 0 2-1 3v4h0l-1-1v-2c0-1-1-2-1-4-1-1-1-3-1-4-1 0-1-1-1-1l-1 1v1s0-1-1-1v1-1-2z" class="AB"></path><path d="M226 433v-1s1-1 1-2c1-1 3-2 3-3v-1c1 0 2-1 2-1l2-2 1-1h2v1c0 1-3 3-3 4l-4 4v1l-1 1v1c-1 0-2 2-3 3v1 1l-1 1v1l2 1 1-1s2-4 2-5c1 0 1-1 1-2h1c0-1 0-2 1-2 0-1 0-1 1-2 0-1 2-2 2-4h1c1-2 3-3 5-5l3-2h0 1l-1 1-1 1-1 1h-1c0 1-1 1-1 2 0 0-1 0-1 1h0l-1 2c0 1-1 1-1 2h0c-1 2-2 4-3 5-1 2-2 4-3 5-1 2-2 3-1 5 0 1 1 2 2 2l1 2-1 1-3-4-1-2c-2 0-2 1-3 1h-1-1l-1-1v-2c1-2 1-4 2-5h1 0l3-5h0c-1 0-2 1-3 2z" class="t"></path><path d="M223 443v-2c1-2 1-4 2-5h1c-1 2-2 5-1 7 1 0 1 0 2-1 1 0 1 1 2 0v-2l2-1v1l1-1h0 0c-1 2-2 3-1 5 0 1 1 2 2 2l1 2-1 1-3-4-1-2c-2 0-2 1-3 1h-1-1l-1-1z" class="Y"></path><path d="M240 425l-1 2c0 1-1 1-1 2h0c-1 2-2 4-3 5-1 2-2 4-3 5h0 0l-1 1v-1l-2 1c2-5 6-11 11-15z" class="Q"></path><path d="M238 383l2 5h0c0 1 0 2 1 3s1 1 2 1l1-3h1v2c-1 4-1 8-2 12 1 2 1 3 1 4-1 1-2 1-3 1-2-1-3-1-4-2v-5-9-3h0c0-3 0-4 1-6z" class="x"></path><path d="M238 383l2 5v10c-1-3 0-7-1-9h-2 0c0-3 0-4 1-6z" class="M"></path><path d="M240 388h0c0 1 0 2 1 3s1 1 2 1l-2 8h-1v-2-10z" class="D"></path><path d="M244 389h1v2c-1 4-1 8-2 12v4h0-1c-1 0-2 0-3-1 1-2 2-4 2-6h0l2-8 1-3z" class="u"></path><path d="M120 205c1 1 0 2 0 3v2c1 0 2 1 3 1 1-1 1-2 2-3l2-2c1 1 1 2 2 4v2l1-1 1 1 2 1c1 1 2 1 2 2 0 0-1 0-2 1-3 1-6 3-8 6-1-1-1-2-1-2-2-2-3-4-4-6-3-3-6-3-10-4h0c2-1 4-1 5-2 2 0 3-1 4-1l1-2z" class="R"></path><path d="M120 205c1 1 0 2 0 3v2c-2-1-3-1-5-2 2 0 3-1 4-1l1-2z" class="p"></path><path d="M129 212l1-1 1 1 2 1c1 1 2 1 2 2 0 0-1 0-2 1-2-2-3-1-5-2h0c1-1 1-1 1-2z" class="n"></path><path d="M127 206c1 1 1 2 2 4v2c0 1 0 1-1 2v-2h-4c-1 0-1 0-1-1 1-1 1-2 2-3l2-2z" class="d"></path><path d="M274 325h1l-1 1c2 2 5 2 7 2-1 0-2 0-3 1v1h5c-3 1-7 2-8 4l-1 1c1 0 1 1 2 1v-1h1c-1 2-1 4-2 5v-1c-1 0-2 1-2 2v1s-9 2-10 2c1-1 1-1 1-2v-2l2-4c0-2 1-4 2-6l1-1h3l1-3 1-1z" class="H"></path><path d="M264 340l2-4v1c1 0 2-1 3-1v2c-1 1-3 2-4 2h-1z" class="C"></path><path d="M268 330l1-1h3c0 1-1 2-1 3l-1 1-1 3c-1 0-2 1-3 1v-1c0-2 1-4 2-6z" class="O"></path><path d="M269 330h2v2l-1 1c0-1-1-1-1-1v-2z" class="AO"></path><path d="M268 330l1-1h3c0 1-1 2-1 3v-2h-2-1z" class="P"></path><path d="M244 389v-2c2-7 2-16 3-24 0-9 2-19 4-28h0v1c0 1 0 3-1 4v1h0l2-2v-3c1-1 1-2 1-3v-1-3l1-2-1 10v4c-2 10-2 20-4 29v6h0c-1 2-1 4-1 6l-1-2v-1l-1 8-1 2h0-1z" class="C"></path><path d="M247 379l1-5v1h0v-1l1 2h0c-1 2-1 4-1 6l-1-2v-1z" class="H"></path><path d="M228 411c0 1 1 1 0 2v1c1-1 2-1 3-2l-1-1h1 0 0l1-1s0-1 1-1l1-1c1 1 2 1 3 2v4l-2 2h0v1l-1 1v1c-1 0-1 1-2 1v1l-4 4c-1 1-1 2-2 3v1 1 1l-1 1c0-2 1-4 2-6l-1-1c-1 1-2 1-2 2v-1-1h-3l-1-1h-1v-1h3l-3-1v-2-1c1-1 1-1 1-2 1 1 1 1 2 1s2-1 3-2v-1l2-2c1 0 1-1 1-2h0z" class="t"></path><path d="M231 413l2-4c1 2 1 3 2 5 0 1-1 2-1 4h-1v-1c-2 1-4 3-5 4h-1c1-1 1-2 2-2 1-1 1-1 1-2 1-1 1-1 1-2 0 0 1 0 1-1l-1-1z" class="q"></path><path d="M231 413l1 1c0 1-1 1-1 1 0 1 0 1-1 2 0 1 0 1-1 2v-1l-2-1c-1 2-3 4-4 5l-1 1-3-1v-2-1c1-1 1-1 1-2 1 1 1 1 2 1s2-1 3-2c2 0 4-1 6-3z" class="e"></path><path d="M219 420c1 0 2-1 4-2 1 0 3 0 4-1-1 2-3 4-4 5l-1 1-3-1v-2z" class="h"></path><path d="M229 419c-1 0-1 1-2 2h1c1-1 3-3 5-4v1c-1 1-2 3-4 5-1 1-2 2-2 3l-1-1c-1 1-2 1-2 2v-1-1h-3l-1-1h-1v-1h3l1-1c1-1 3-3 4-5l2 1v1z" class="Y"></path><path d="M222 423l1-1 1 1v1h0l-3 1-1-1h-1v-1h3zm2 2l3-3c1 0 1 0 2 1-1 1-2 2-2 3l-1-1c-1 1-2 1-2 2v-1-1z" class="g"></path><path d="M215 397c1-1 1-2 2-2h0l1-1 1-1h0v1 1c1-1 1-2 1-2h0v1c0 2-1 3-1 5h1l1-2h0v3l-2 12v1c1 1 1 2 1 4h0c0 1 0 1-1 2v1 2l3 1h-3v1h1-1v1c-1 1-1 0-1 1l-2 2c0-1 0-1-1-2h-1 0c-1 1-1 1-2 1v-1h-1v-1c-1-2 0-4 1-7l1-1h0v-1-2c1-1 1-2 2-3l2-5v-2c0-1 1-2 1-4v-3h0c0-1 0-1 1-1l-2-1-1 2h-1z" class="q"></path><path d="M215 422c-1 0-1-1-1-2h1c1 0 2 1 2 2h0-2z" class="S"></path><path d="M216 415h2l2 2h0c0 1 0 1-1 2v1h-1c-1-1-1-3-2-5z" class="X"></path><path d="M217 422h2l3 1h-3v1h1-1v1c-1 1-1 0-1 1l-2 2c0-1 0-1-1-2h-1 0c-1 1-1 1-2 1v-1c1-1 2-1 3-3v-1h2 0z" class="i"></path><path d="M215 426c1-1 1-2 3-2v2l-2 2c0-1 0-1-1-2z" class="f"></path><path d="M220 399l1-2h0v3l-2 12v1c1 1 1 2 1 4l-2-2h-2v-2c1-2 1-4 1-6l3-8zm28 127c0-2-1-3-2-4l-3-7h0 1l6 11c2 3 4 6 5 10 1 3 1 7 1 11h0c-1 2 0 4 0 5-1 0-2-1-2-2-1-1-3-3-4-5-3-4-7-8-10-13-1-2-2-5-3-7v-1-1l1 1-1-2c2 2 4 6 7 8h0c1 1 2 3 4 4 0 1 1 1 1 1 2 2 4 4 5 6v1h1v-3-2l-1-1c0-1 0-2-1-2-1-1-1-2-2-3h0c0-1-1-1-1-2l-2-3h0z" class="l"></path><defs><linearGradient id="As" x1="191.956" y1="311.928" x2="209.806" y2="298.644" xlink:href="#B"><stop offset="0" stop-color="#271f1c"></stop><stop offset="1" stop-color="#545657"></stop></linearGradient></defs><path fill="url(#As)" d="M185 263l33 79v1l1 1v1l-1 1h-1-1-1l1-1v-1h1c-1-1-1-2-1-3h0l-1-2c-1-3-3-6-4-9-2-4-3-8-5-12l-6-13c0-4-3-9-5-13l-5-12c-2-5-5-11-7-16 0 0 1 1 1 2h1v-3z"></path><path d="M234 490l-1-2v-2l-1-1v-1-2h0c0-1 0-2-1-2v-4c-1 0-1-2-1-4h0v-3h0c0-1 0-2 1-2v-5h1v-2c1-1 1-2 1-3v-2h0v-1-2c1 0 1-1 1-2h1 0 0c0 2 0 2-1 4v4c1-1 0-1 1-2 0-1 1-3 1-4v1 3 1c1-2 2-5 2-7h2c-1 2-3 10-2 12h0v1c1 1 1 2 1 3l-3 2v-1 1h0l-1 1c-1 0-1 0-1 1v9c0 1 1 3 1 4v3c1 1 1 2 1 3s0 2 1 3v3l-1 1h-1l-1-6z" class="Q"></path><path d="M238 463c1 1 1 2 1 3l-3 2v-1c1-1 1-3 2-4z" class="s"></path><path d="M234 490v-2-1l-1-1v-4c0-1-1-3-1-4 1-2 1-5 1-7l1-1v9c0 1 1 3 1 4v3c1 1 1 2 1 3s0 2 1 3v3l-1 1h-1l-1-6z" class="o"></path><path d="M220 424l1 1h3v1 1c0-1 1-1 2-2l1 1c-1 2-2 4-2 6l-2 4v1h-2c0 1 0 1-1 2v1 2c-1 1-1 2-1 2l-2-1-2 1-1-1 1-3v-1-2c-1 1-2 2-3 2h-1l-2 1-3 1-1-1c4-3 8-7 11-12l2-2c0-1 0 0 1-1v-1h1z" class="h"></path><path d="M215 437c0-1 0-2-1-2l4-5-1 5c-1 2-1 3-2 4h0v-2z" class="T"></path><path d="M220 427h1c0 2 0 3-1 5l-1-1c-1 1-1 3-2 4l1-5c0-1 2-3 2-3z" class="X"></path><path d="M224 427c0-1 1-1 2-2l1 1c-1 2-2 4-2 6l-2 4v1h-2c0 1 0 1-1 2v1-2c0-1 1-3 1-4h0-1v-2h0c1-2 1-3 1-5h-1l1-1c1 0 1 1 2 1l1-1v1z" class="AD"></path><path d="M220 427l1-1c1 0 1 1 2 1l1-1v1c-2 2-2 4-3 7h0-1v-2h0c1-2 1-3 1-5h-1z" class="i"></path><path d="M217 435c1-1 1-3 2-4l1 1h0v2h1 0c0 1-1 3-1 4v2 2c-1 1-1 2-1 2l-2-1-2 1-1-1 1-3v-1h0c1-1 1-2 2-4z" class="j"></path><path d="M218 441l1-1h0v1l-1 1h-1v-1h1z" class="X"></path><path d="M215 439h2s0 1 1 2h-1c-1 0-1 0-2-1v-1h0z" class="Ag"></path><path d="M220 432v2h1 0c0 1-1 3-1 4h-1c0-2 1-4 1-6z" class="K"></path><path d="M215 440c1 1 1 1 2 1v1h1 2c-1 1-1 2-1 2l-2-1-2 1-1-1 1-3z" class="Ai"></path><path d="M187 318h1 0l1 1v-1-1-1l-1-1v-1-1c-1 0-1-1-1-2-1-2-2-5-1-7h0c0-1 0-1-1-1l-3-3h1 0v-1l1-1v-1c1 0 1-1 1-2v-2c1 0 1-1 1-2v-1l1-1 1 1c0 1 0 3-1 4h0v2 1c1 1 1 3 2 5v2h0v1h1v1c0 1 1 2 1 3l5 9v1h-2l-2 1v2c0 2 0 3-1 4v2l-2 2v1h-1c-1-1-1-2-1-3l-2-2 1-1c1-1 0-2-1-3l1-1v-1-1l1-1z" class="F"></path><path d="M186 321v-1-1l1-1h0c1 1 2 1 3 1h-1c0 1 0 1-1 1s-1 0-2 1z" class="b"></path><path d="M186 321c1-1 1-1 2-1l3 3-1 1c-2 1-2 2-3 4l-2-2 1-1c1-1 0-2-1-3l1-1z" class="N"></path><path d="M211 305h0l-1 1v-1c0-1 0-1 1-3-1 0-2-1-2-2l-8-11h-1s-1 1-1 2l-1-1 2-4c0-3 1-8 3-9l2-2v1c-1 1-2 2-2 3s0 1 1 2h0c0 1 0 2 1 2v2c1 2 2 3 3 4v1c2 3 6 5 9 8 2 1 4 3 5 5v1c-3 0-6 0-9-1h-1c0 1 0 1 1 1s0 0 1 1c1 0 3 1 5 1h0c-1 0-2 1-3 0h0-1c-1 0-1 0-2-1h0-2z" class="F"></path><path d="M211 305h0v-1-1c0-1 0-2-1-2h1v-1c0 1 1 1 1 2 1 0 0 0 1 1h-1c0 1 0 1 1 1s0 0 1 1c1 0 3 1 5 1h0c-1 0-2 1-3 0h0-1c-1 0-1 0-2-1h0-2z" class="c"></path><path d="M234 470c0-1 0-1 1-1 0 1-1 1 0 2l1 1h1l1 5h1v2h1l1 2c0 1 1 2 1 3-1 1 0 2 0 3l1 9v6h1l-1 1 1 2c-1 1-1 1-1 2l-1-1-1 2-1 1v1c0 1-1 3-2 4 2-1 3-2 4-2 1 1 2 2 2 3h0-1 0l3 7c1 1 2 2 2 4-1-2-2-3-3-5h0v-1c-1-1-2-3-2-4l-1-1s0-1-1-2l-2 2v1h-1v-1h-1l1-1v-1h0v-2l1-1c1-1 0-3 0-5h0v-2c0-1 0-2-1-3v-1-1-1l-1-1c1 0 1-1 0-1v-3-1c0-1 0 0-1-1v-6-1-1l-1 4v-3c0-1-1-3-1-4v-9z" class="T"></path><path d="M236 472h1l1 5v6s-1-1-1-2l-1 1h0v-10z" class="S"></path><path d="M236 482l1-1c0 1 1 2 1 2 1 8 3 17 3 25l-1 1c0-2 0-6-1-8l-2-9c0-3 0-7-1-10z" class="j"></path><path d="M238 477h1v2h1l1 2c0 1 1 2 1 3-1 1 0 2 0 3l1 9v6h1l-1 1 1 2c-1 1-1 1-1 2l-1-1-1 2c0-8-2-17-3-25v-6z" class="h"></path><path d="M242 506l-1-2 2-1 1 2c-1 1-1 1-1 2l-1-1z" class="AA"></path><path d="M239 479h1l1 2c0 1 1 2 1 3-1 1 0 2 0 3v1h-1v2c1 0 0 0 0 1v-5c-1-2-1-4-2-5v-2z" class="L"></path><path d="M281 341h2 0l1 1h2c2-1 3-1 4-1l1-1h1c1-1 0-1 1-1h1c1-1 2-1 3-1l1 1c-1 1-2 2-2 3h-2 0-1v-1h0 0 3l1-2s0-1-1 0h-2v1l-2 1h-1l-1 1h-1l1 1h1l-3 3-2 2h0c-1 0 0 0-1 1h-2-2l-1 1-1-1c-1 2-3 4-4 7-1 1-3 2-4 3h0v-1c-2 0-3-1-4-1l-1-1c1-1 1-2 2-3h-1c-1-2-1-2-1-3v-2c1-2 3-2 5-3 0 0 1 0 2-1 0 1 0 1 1 1l1-1h5c0-1 1-1 2-1l1-1c-1 0-1-1-2-1z" class="H"></path><path d="M266 350v-2h1c1 0 1 0 2 1l-2 2c-1 0-1-1-1-1z" class="AO"></path><path d="M279 349c1-2 1-3 2-4l2-1c-1 2-1 3-2 5l-1 1-1-1z" class="F"></path><path d="M271 345v1l-2 3c-1-1-1-1-2-1h-1c1-2 3-2 5-3z" class="y"></path><path d="M283 344v-1h1c2 0 4-1 5-1l1 1h1l-3 3-2 2h0c-1 0 0 0-1 1h-2-2c1-2 1-3 2-5z" class="U"></path><defs><linearGradient id="At" x1="213.572" y1="470.272" x2="248.581" y2="493.426" xlink:href="#B"><stop offset="0" stop-color="#631815"></stop><stop offset="1" stop-color="#952522"></stop></linearGradient></defs><path fill="url(#At)" d="M229 446l1 1c0 1-1 4-2 6l-2 12-1 3c0 13 2 25 5 37l2 5 1 3 1 2-1 2h0c-2-3-4-7-5-10l-2-7c-4-14-7-31-2-45 1-3 3-6 5-9z"></path><path d="M225 468v3c-1-1 0-3-1-4 0-1 0-2 1-3h0l1 1-1 3z" class="AG"></path><path d="M165 264v-1c-1-1-1-1-1-2l-1-1h0c0-2 0-3 1-4h0c0-1 0-1 1-1 0-1 0-1 1-1l-2 2c0 1 1 1 1 2h1l1-2h1v1c0 2 0 3-1 4v1 3c1 2 3 4 5 6l6 10 2 1v-1c2 1 3 4 4 5h0c0 1 1 2 1 3-1 1-1 2-2 3v2 1 1c-1 1-1 2-1 3v1h-1l-1-1-1-1v-1l-1-1c-1-1-1-1-2-1h1l2 1 2 4c0-1 1-1 0-2h0 0c1-2 1-4 1-5s0-1 1-1c0-1-1-2-1-3h-1-1l-1-1c-2 0-1-2-3-4-1 0-2-1-3-2l-1-1-2-2c-1-2-1-2-2-2v-1h0c-1-2-4-4-6-5 0-1-1-2-1-2-1 0-3 2-4 2h-1c3-2 6-4 9-7z" class="I"></path><path d="M178 281l2 1v-1c2 1 3 4 4 5h0c0 2 0 2-1 3h0c-1-3-3-6-5-8z" class="B"></path><path d="M165 264v1l-2 4c2 1 3 2 5 3 1 0 1 1 2 2h0v1 1c1 2 4 4 6 6 1 1 1 3 3 4l3 3h-1-1l-1-1c-2 0-1-2-3-4-1 0-2-1-3-2l-1-1-2-2c-1-2-1-2-2-2v-1h0c-1-2-4-4-6-5 0-1-1-2-1-2-1 0-3 2-4 2h-1c3-2 6-4 9-7z" class="H"></path><defs><linearGradient id="Au" x1="119.639" y1="147.504" x2="140.643" y2="155.38" xlink:href="#B"><stop offset="0" stop-color="#5c5451"></stop><stop offset="1" stop-color="#7f7466"></stop></linearGradient></defs><path fill="url(#Au)" d="M159 106c0 1-1 3 0 4h1 0l-8 8c-4 4-8 8-11 12-11 14-21 30-29 45-2 5-5 11-7 17l-1-1c0 1 0 2-1 3h0l1-4h-1c2-5 4-10 7-15l3-6c2-3 4-6 5-9 2-3 3-6 4-8 6-9 12-17 18-25 6-7 15-13 19-21z"></path><path d="M248 382c0-2 0-4 1-6 0 2 0 2 1 3l1 1h3 1 0 1 2v-1h1v-1c2 0 3 0 5-1h0 3v1h1l-1 1h0 0v2h0l-9 6c-3 3-5 6-8 9-2 3-4 5-5 9 0 1-1 2-1 2 0-1 0-2-1-4 1-4 1-8 2-12v-2h0l1-2 1-8v1l1 2z" class="P"></path><path d="M250 390h-1c0-2 1-2 2-3h0l1 1-2 2z" class="D"></path><path d="M255 380h0l1 1h1c0 1 0 1-1 2-1 0-2-1-3-1v1h1l-2 2v-1h0c0-1 0-1 1-2v-1l2-1z" class="U"></path><path d="M264 377h0 3v1h1l-1 1h0 0c-3 1-5 3-7 4-3 2-4 4-7 6h0 0c1-2 1-4 1-6h-1v-1c1 0 2 1 3 1 1-1 1-1 1-2h-1l-1-1h1 2v-1h1v-1c2 0 3 0 5-1z" class="B"></path><path d="M264 377h0 3v1l-7 4c-1 0-2 1-3 1h-1c1-1 1-1 1-2h-1l-1-1h1 2v-1h1v-1c2 0 3 0 5-1z" class="P"></path><path d="M247 379v1l1 2c-1 3-1 6-1 8h0 1l1 1c0-1 0 0 1-1l2-2v1c0 2-3 3-1 5h-1v2c-2 3-4 5-5 9 0 1-1 2-1 2 0-1 0-2-1-4 1-4 1-8 2-12v-2h0l1-2 1-8z" class="O"></path><path d="M247 379v1l1 2c-1 3-1 6-1 8 0 3-1 5-2 7v-6-2h0l1-2 1-8zm-134-92v-1c2-3 3-10 7-11h2 0c1 1 2 1 3 1h1v1c2 1 4 2 6 4-1-1-1-1-2-1 0-1-1-1-1-1-1 0-1-1-1-1 0 1 0 1-1 2v2 2h-1v2c-1 1 0 3-1 4v2c-1 1-1 1-1 2v1h0c0 1-1 1-1 2s0 1-1 2h-1v1 1c-1 1-1 2-1 3v-1-2l1-2c-1 0-1-1-2-1h-3v1-1h-1v1l-1 1-3-3v-3h1c-1-1 0 0 0-1s-1-2-1-3v-1-3-2l1 6v-1c0-1 0-1 1-2z" class="B"></path><path d="M126 279h1c0 1-1 2-1 4l-1 6v-2h-1v1h0c-1-2 1-7 2-9z" class="E"></path><path d="M113 287c0 2 0 3 2 5h2c1 1 1 2 0 3v1c-2 0-2-1-3-2s-2-3-2-4v-1c0-1 0-1 1-2z" class="I"></path><path d="M206 362s1-1 1 0c1 0 2 0 3 1s2 1 2 1l3 3h1 0l2 2c1 1 2 2 2 3 1 0 1 0 1 1 1 0 1 1 2 1 0 1 0 2-1 2v1l-1 1h0c0 1 0 1-1 2h0l-3 7v1h-1c-1 2-1 3-2 4l-2 4c0-1-1-1-2-1v-1l2-5h-1l-2-1-1 1h0c-2-3-4-5-3-9l-1-1c0-2 5-10 5-12 1 0 1-1 1-2l-1-1-1-1h1 0c-1 0-1 0-1-1h-2z" class="t"></path><path d="M209 367c1 0 1-1 1-2l-1-1-1-1h1c1 1 2 1 3 3-1 0-1 1-1 2h-1v-1h-1zm4 13c1-2 1-3 2-4l1 1v4l3-4c-1 1-2 3-3 5l-2 3v-2c1-2 1-3 1-6l-2 3z" class="L"></path><path d="M209 367h1v1c-1 4-2 8-4 12h-1l-1-1c0-2 5-10 5-12zm10 10l2-3 1 1c0 2-2 4-3 6 0 1 0 3-1 4l-1-1c0-1 0-2-1-2 1-2 2-4 3-5z" class="T"></path><path d="M213 380l2-3c0 3 0 4-1 6v2 2l-2 2h-1l-2-1c0-2 3-6 4-8h0z" class="Q"></path><path d="M216 382c1 0 1 1 1 2l1 1-2 3c-1 2-1 3-2 4l-2 4c0-1-1-1-2-1v-1l2-5 2-2v-2l2-3z" class="Y"></path><path d="M214 387v2c-1 1-1 2 0 3l-2 4c0-1-1-1-2-1v-1l2-5 2-2z" class="AS"></path><path d="M201 200c2 1 3 3 6 3v1c0 1 0 1-1 1v1l-1 2c0 3 1 5 2 7l3 9c3 5 6 9 9 14 1 3 3 6 4 9l2-1c0 1 0 1 1 2v2l3 7s1 1 1 2c1 2 2 3 3 5l1 2 1 2c-1-1-3-5-4-6h-1c0-1-1-2-2-2-2-5-5-9-7-14l-9-15c-3-4-4-7-7-10-6-7-15-9-24-9l1-1v-1h-3v-1h0c3 0 6 1 8 0h1c1 0 3 0 5-1 2-2 2-3 2-5l2 1v-4l1 1v-1h0 3 0z" class="d"></path><path d="M225 246c0 1 0 1 1 2v2l-3-3 2-1z" class="G"></path><path d="M199 203l5 5h0v1h-3c-2-2-2-4-2-6z" class="AF"></path><path d="M198 200c2 2 4 4 7 5 1 0 0 0 1 1l-1 2h-1 0l-5-5-1-2v-1z" class="n"></path><path d="M201 200c2 1 3 3 6 3v1c0 1 0 1-1 1v1c-1-1 0-1-1-1-3-1-5-3-7-5h0 3 0z" class="AL"></path><path d="M195 203l2 1c-1 2-3 4-6 5-3 2-6 1-9 1h-3v-1h0c3 0 6 1 8 0h1c1 0 3 0 5-1 2-2 2-3 2-5z" class="H"></path><path d="M177 84c1 0 1-1 2-1l1 1v2l1 1v1l1 2 2-1c1 0 1 1 1 2-1 0-1 0-2 1-1 0-1 1-1 2l1 1-3 2h-1c-4 4-10 7-14 10l-5 3h-1c-1-1 0-3 0-4v-3c0-2-1-3-2-5l1-1h-1c0-1 0-2 1-3 1 0 1-1 2-1l8-4 5-3 4-2z" class="W"></path><path d="M177 84h0v1l1 2c-2 0-4 0-5 1v-2l4-2z" class="AM"></path><path d="M158 97h1 1l1 1v1c-1 1-1 2-1 3l-1 1c0-2-1-3-2-5l1-1z" class="E"></path><path d="M177 84c1 0 1-1 2-1l1 1v2l1 1-2 1-1-1-1-2v-1h0z" class="p"></path><path d="M177 84c1 0 1-1 2-1l1 1v2-1c-1 0-2 0-3-1h0z" class="m"></path><path d="M181 87v1 5c-1 1-1 1-1 2v2h-1-1v-1l-1 1h-1c1-1 3-2 3-4 1-1 1-3 0-5l2-1z" class="k"></path><path d="M181 88l1 2 2-1c1 0 1 1 1 2-1 0-1 0-2 1-1 0-1 1-1 2l1 1-3 2v-2c0-1 0-1 1-2v-5z" class="AK"></path><path d="M159 103l1-1 1 3c1 0 2 0 2-1 1 1 1 1 1 2 0 0 0 1 1 1l-5 3h-1c-1-1 0-3 0-4v-3z" class="u"></path><path d="M160 106h1v1c-1 0-1 1-1 0v-1z" class="V"></path><path d="M173 86v2c-5 2-9 6-14 9h-1-1c0-1 0-2 1-3 1 0 1-1 2-1l8-4 5-3z" class="AK"></path><path d="M176 97h1l1-1v1h1c-4 4-10 7-14 10-1 0-1-1-1-1 0-1 0-1-1-2h0l6-3 7-4z" class="AL"></path><path d="M242 506l1 1c1 1 1 2 2 3 0-1 0-2 1-2 2 1 3 1 4 1 2 0 4 0 6-1 1 1 1 1 2 1 2 0 3 0 4 1h1c1 0 2 1 3 2s2 1 4 1l1 1c1 0 1-1 2-1l-1 1c-2 0-4 0-6-1v1l4 2c1 0 1 1 2 1-1 0-4 0-5 1-1 0-1 1-2 2s-1 2-2 3h0-1v2 1l-1 1v5c0 1-1 4-1 4v8c0 1 0 2-1 3h0v2l-2 1v1c-1-1-1-3-1-4 0-4 0-8-1-11-1-4-3-7-5-10l-6-11h0c0-1-1-2-2-3-1 0-2 1-4 2 1-1 2-3 2-4v-1l1-1 1-2z" class="N"></path><path d="M264 515v-1h2 0l4 2c-2 0-4-1-6-1h0z" class="P"></path><path d="M258 547v-1c0-2 0-8 1-10h1v8c0 1 0 2-1 3h0-1z" class="R"></path><path d="M258 513h1 0l-3 6-1 2-1-1h0v-1-2c1-2 2-4 4-4z" class="J"></path><path d="M259 513h1v1h1c1 0 2 0 3 1h0c-2 1-3 2-5 4-1 1-3 1-4 2h0l1-2 3-6z" class="B"></path><defs><linearGradient id="Av" x1="244.15" y1="531.817" x2="266.971" y2="517.128" xlink:href="#B"><stop offset="0" stop-color="#160b0c"></stop><stop offset="1" stop-color="#2c2c2e"></stop></linearGradient></defs><path fill="url(#Av)" d="M242 506l1 1c1 1 1 2 2 3 0-1 0-2 1-2 2 1 3 1 4 1 2 0 4 0 6-1 1 1 1 1 2 1 2 0 3 0 4 1h1c1 0 2 1 3 2s2 1 4 1l1 1c1 0 1-1 2-1l-1 1c-2 0-4 0-6-1v1h0-2v1c-1-1-2-1-3-1h-1v-1h-1 0-1v-1c-2 0-2 0-3-1v1c-2 0-3 0-4 2l-3 3h1c0 1 1 3 2 4 0 1 0 1 1 2 1 3 3 6 4 9 0 2 1 5 1 7 0 3 0 6 1 8h1v2l-2 1v1c-1-1-1-3-1-4 0-4 0-8-1-11-1-4-3-7-5-10l-6-11h0c0-1-1-2-2-3-1 0-2 1-4 2 1-1 2-3 2-4v-1l1-1 1-2z"></path><path d="M255 511h3c1-1 1-1 2 0h0c-1 1-1 1-2 1-2 0-2 0-3-1z" class="B"></path><path d="M260 513c1 0 1-1 3-1 1 0 2 1 3 2h-2v1c-1-1-2-1-3-1h-1v-1zm-15-3c0-1 0-2 1-2 2 1 3 1 4 1h2c-1 1-4 3-5 4-1 0-1-1-2-2v-1z" class="D"></path><path d="M166 110h1c1-2 3-3 4-4 2-3 9-7 12-7l1 1v2c1 0 2-1 3-2l1 1v1c0 2 0 3 1 4-1 3-1 5-1 7 1 3 2 4 4 5l-1 1c-3-1-4 0-7 1-1 1-2 1-2 2-1 1-3 1-4 2l1-1h-1c-1 0-1 0-2-1v1c-2 2-3 4-5 6-4 2-7 6-10 10-4 6-9 12-13 19v-1h1l-1-1c0 1-1 1-1 1l-1 1c-5 7-9 15-13 23-1 3-3 6-4 10l-1-1 2-7-1 1c0 1 0 1-1 2h0c0 1 0 2-1 2l1-3c1-1 1-2 1-3v-1c1-4 4-9 7-13l2-3 1-2 2-4 1-2 5-9c2-3 4-5 5-7l12-14 1-3c2-1 4-4 5-5l-2-1c0-1-1-2-1-3l-2-1 1-1v-3z" class="w"></path><path d="M166 113c2 1 3 1 4 2h-3l-2-1 1-1z" class="H"></path><path d="M141 159c0 1 1 1 1 2l-3 4h-1l1-2 2-4z" class="D"></path><path d="M171 115c2-1 4-3 7-5-1 1-1 2-2 3s-3 2-4 3l-1-1z" class="C"></path><path d="M170 115h1l1 1c0 1-1 1-1 2-1 0-1 0-1 1l-2-1c0-1-1-2-1-3h3z" class="F"></path><path d="M183 109l1 1c-1 3-2 6-4 8l-2 2-1-1c3-3 4-7 6-10z" class="y"></path><path d="M147 148v1 1s1 0 1-1h1l-7 12h0c0-1-1-1-1-2l1-2 5-9z" class="b"></path><defs><linearGradient id="Aw" x1="165.005" y1="123.729" x2="175.389" y2="114.854" xlink:href="#B"><stop offset="0" stop-color="#333232"></stop><stop offset="1" stop-color="#4f4946"></stop></linearGradient></defs><path fill="url(#Aw)" d="M172 116c1-1 3-2 4-3-1 4-3 5-6 8-2 2-4 5-6 7v-1l1-3c2-1 4-4 5-5 0-1 0-1 1-1 0-1 1-1 1-2z"></path><path d="M136 168l2-3h1l-9 18-1 1c0 1 0 1-1 2h0c0 1 0 2-1 2l1-3c1-1 1-2 1-3v-1c1-4 4-9 7-13z" class="c"></path><path d="M147 148c2-3 4-5 5-7l12-14v1l-15 21h0-1c0 1-1 1-1 1v-1-1z" class="G"></path><path d="M177 119l1 1 2-2-1 3v1 1h-1c-1 0-1 0-2-1v1c-2 2-3 4-5 6-4 2-7 6-10 10-4 6-9 12-13 19v-1h1l-1-1c0 1-1 1-1 1l-1 1c2-4 5-7 7-10 4-6 9-13 14-18 2-3 5-6 8-9l2-2z" class="x"></path><path d="M177 119l1 1 2-2-1 3v1h-1c-1 0-2 0-3-1l2-2z" class="AF"></path><defs><linearGradient id="Ax" x1="186.056" y1="104.639" x2="182.446" y2="120.193" xlink:href="#B"><stop offset="0" stop-color="#373334"></stop><stop offset="1" stop-color="#54514f"></stop></linearGradient></defs><path fill="url(#Ax)" d="M184 102c1 0 2-1 3-2l1 1v1c0 2 0 3 1 4-1 3-1 5-1 7 1 3 2 4 4 5l-1 1c-3-1-4 0-7 1-1 1-2 1-2 2-1 1-3 1-4 2l1-1v-1-1l1-3c2-2 3-5 4-8l-1-1c0-2 1-5 1-7z"></path><path d="M184 102c1 0 2-1 3-2l1 1v1c-1 1-1 3-2 4v1c-1 1-1 2-2 3l-1-1c0-2 1-5 1-7z" class="M"></path><path d="M179 121h0c2 0 5-3 6-5h1c0-1 0-3 1-4h1v1c1 3 2 4 4 5l-1 1c-3-1-4 0-7 1-1 1-2 1-2 2-1 1-3 1-4 2l1-1v-1-1z" class="Z"></path><path d="M257 340l2-18 4-2h1v1c0 1 1 1 1 1 0 1-1 1-1 2l3-1h1c-2 2-3 3-3 5l1 1-1 1c-1 1-1 2-1 4s-1 3-2 4c0 1 0 1 1 2v1l1 1c0 1 0 1-1 2 1 0 10-2 10-2v-1c0-1 1-2 2-2v1c0 2-1 3-2 4s-2 1-2 1c-2 1-4 1-5 3v2c0 1 0 1 1 3h1c-1 1-1 2-2 3l1 1c1 0 2 1 4 1v1h0c1-1 3-2 4-3 1-3 3-5 4-7l1 1 1-1h2c-1 1-2 2-2 3l2 2c2 0 3-1 5-1l1-1c0 1 0 1 1 1v-1h1v6 3c1 0 1 2 1 3v1h-2c0-1 0-1-1-1v-1c0 1-1 1-2 1-2 3-6 4-10 6s-9 4-13 7h0c0-1-1-1-1-2-2 0-3 0-4-1s-1-3-2-4v-7-13-10z" class="a"></path><path d="M267 353h1c-1 1-1 2-2 3l-1 1-1-1 3-3z" class="O"></path><path d="M263 344c-2 1 0 0-2-1h-1v-1c1 0 2 0 3-1l1 1c0 1 0 1-1 2z" class="J"></path><path d="M263 362h2c-1 2-3 3-5 3l1-1h0c0-1 1-2 2-2z" class="D"></path><path d="M265 357l1-1 1 1c1 0 2 1 4 1v1l-3 2c-1 0-2 1-3 1h-2c1-2 2-3 2-4v-1z" class="U"></path><path d="M265 357l1-1 1 1h0l-1 2-1-1v-1z" class="R"></path><path d="M257 340l1 12v-2c0-1 0-1 1-2h0c0-1 1-2 1-2h1c1 0 1 0 2 1h-1c-2 3-2 6-3 9l-1 1v1c-1 2-1 8 0 11h2 1c1 0 1-1 3-1 0 0 0-1 1-2s2-3 3-5l3-2h0c1-1 3-2 4-3 1-3 3-5 4-7l1 1 1-1h2c-1 1-2 2-2 3l2 2c-2 0-3 0-5 2l-1 1c-1 0-3 2-4 3v2h-1-3v1c0 1 1 1 0 3s-5 4-7 5c-1 0-3 1-3 1-1 1-1 1 0 2-1-1-1-3-2-4v-7-13-10z" class="I"></path><path d="M281 349h2c-1 1-2 2-2 3l2 2c-2 0-3 0-5 2 0-3 1-4 2-6l1-1z" class="b"></path><path d="M259 356h-1v-1c0-2 1-7 2-8h2c-2 3-2 6-3 9z" class="AC"></path><defs><linearGradient id="Ay" x1="275.961" y1="371.983" x2="271.852" y2="359.025" xlink:href="#B"><stop offset="0" stop-color="#2a292a"></stop><stop offset="1" stop-color="#4b4746"></stop></linearGradient></defs><path fill="url(#Ay)" d="M290 353v-1h1v6 3c1 0 1 2 1 3v1h-2c0-1 0-1-1-1v-1c0 1-1 1-2 1-2 3-6 4-10 6s-9 4-13 7h0c0-1-1-1-1-2-2 0-3 0-4-1s-1-1 0-2c0 0 2-1 3-1 2-1 6-3 7-5s0-2 0-3v-1h3 1v-2c1-1 3-3 4-3l1-1c2-2 3-2 5-2s3-1 5-1l1-1c0 1 0 1 1 1z"></path><path d="M288 353l1-1c0 1 0 1 1 1 0 1 0 2-1 2-1 1-2 2-3 2 0-1 1-1 1-2h0v-1h0l1-1h0z" class="U"></path><path d="M291 361c1 0 1 2 1 3v1h-2c0-1 0-1-1-1v-1c0 1-1 1-2 1l2-2h-1c-1 1-2 1-3 1-1 1-4 3-5 2l6-3h2c1-1 1-1 2-1v2 1h1v-3z" class="a"></path><path d="M290 353v-1h1v6 3 3h-1v-1-2c-1-2-1-4-1-6 1 0 1-1 1-2z" class="I"></path><path d="M288 353h0l-1 1h0v1h0c0 1-1 1-1 2l-2-2c-3 1-7 3-9 6-1 0-1 1-2 1v-2c1-1 3-3 4-3l1-1c2-2 3-2 5-2s3-1 5-1z" class="H"></path><path d="M288 353h0l-1 1h0s-1 1-2 1h-1c0-1-3 1-4 1s-1 1-2 1h-1l1-1c2-2 3-2 5-2s3-1 5-1z" class="m"></path><path d="M226 433c1-1 2-2 3-2h0l-3 5h0-1c-1 1-1 3-2 5v2l1 1h1 1c1 0 1-1 3-1l1 2-1 1c-2 3-4 6-5 9-5 14-2 31 2 45l2 7c1 3 3 7 5 10l1 2 3 6c1 2 2 5 3 7 3 5 7 9 10 13 1 2 3 4 4 5 0 1 1 2 2 2 0-1-1-3 0-5h0c0 1 0 3 1 4v-1c0 3 1 5 3 7l7 7 21 15 1 1 1 1 1-2 2-2c2-2 4-2 6-1h0l12 2c1-1 2-1 3-2l4-1c0 3-1 5 0 8 0 2-1 5-1 7-4 0-6 2-9 3l-2 2h-2l-1 1-2 2c-2 1-3 3-5 3h0v-1c0-1 0-1-1-1-3-1-4-3-7-5l-1-1c-1-1-1-1-1-2l-2 1c-1 0-2-1-3-1-1-2-9-6-12-8-4-3-8-6-11-10l-1-1-2 1v-1c1-1 2-2 2-3l4-4 1-1-6-6-12-15c-5-7-10-15-14-23-2-3-3-6-4-9l-2 2c-4-8-5-16-6-24-1-3-1-6-1-9h0c0-2 1-3-1-5 0-1 1-4 1-6s-1-4-1-7h-1c1-4 1-10 3-13 0-1 1-1 1-2l-1-1c1-1 1-1 1-2 0 0 0-1 1-2v-2-1c1-1 1-1 1-2h2v-1h1c1-1 2-2 2-3z" class="AC"></path><path d="M300 590c2 0 6 2 8 3l-2 2c-2-2-5-3-7-5h1z" class="AB"></path><path d="M293 580c1 0 2 1 3 1l2 2v1c-2 0-5-1-7-3l2-1z" class="Q"></path><path d="M254 550c0 1 1 2 2 2 0-1-1-3 0-5h0c0 1 0 3 1 4v-1c0 3 1 5 3 7l7 7c-2-1-5-4-7-6 0-1-1-2-2-3-2 0-2-1-4-3v-2z" class="L"></path><path d="M297 577c2 0 4 0 6 2h0v1h-1c-2 0-3 0-5 1v1h1c2 0 4 2 6 3h-2l-3-1h0l3 1v1c-1-1-3-1-4-2v-1l-2-2c-1 0-2-1-3-1 1-1 3-2 4-3z" class="AB"></path><path d="M297 577c1 1 3 1 3 2-1 1-3 1-4 2-1 0-2-1-3-1 1-1 3-2 4-3z" class="T"></path><path d="M218 463l1 15c0 1 0 3-1 4v7c-1-3-1-6-1-9h0c0-2 1-3-1-5 0-1 1-4 1-6v-2c1-1 1-3 1-4z" class="Y"></path><path d="M218 482c1 4 1 7 2 11 1 6 4 12 6 18l-2 2c-4-8-5-16-6-24v-7z" class="Ae"></path><path d="M226 433c1-1 2-2 3-2h0l-3 5h0-1c-1 1-1 3-2 5v2l1 1h1 1c-2 2-5 5-6 7 0 2 0 3-1 4 0 2 0 5-1 8 0 1 0 3-1 4v2c0-2-1-4-1-7h-1c1-4 1-10 3-13 0-1 1-1 1-2l-1-1c1-1 1-1 1-2 0 0 0-1 1-2v-2-1c1-1 1-1 1-2h2v-1h1c1-1 2-2 2-3z" class="AS"></path><path d="M218 449h1c-1 1-1 3-1 4 0 3-1 5-1 7v7 2c0-2-1-4-1-7h-1c1-4 1-10 3-13z" class="n"></path><path d="M226 433c1-1 2-2 3-2h0l-3 5h0-1c-1 1-1 3-2 5v2c0 2 0 2-1 3h-1c0-2 0-4 1-5 0-1 0-3 1-4v-1h1c1-1 2-2 2-3z" class="T"></path><path d="M262 564l11 9 7 5 8 5c4 2 8 5 12 7h-1c2 2 5 3 7 5h-2l-1 1-2 2c-2 1-3 3-5 3h0v-1c0-1 0-1-1-1-3-1-4-3-7-5l-1-1c-1-1-1-1-1-2l-2 1c-1 0-2-1-3-1-1-2-9-6-12-8-4-3-8-6-11-10l-1-1-2 1v-1c1-1 2-2 2-3l4-4 1-1z" class="h"></path><path d="M284 592l2-2c1-1 1-1 3 0l-2 1v2c-1-1-1-1-1-2l-2 1z" class="q"></path><path d="M257 569c1 1 1 3 1 4l-1-1-2 1v-1c1-1 2-2 2-3z" class="T"></path><path d="M296 591c1-1 1-1 2-1h1c2 2 5 3 7 5h-2c-1-2-5-2-8-4z" class="J"></path><path d="M273 573l7 5-2 1-8-6h3z" class="AS"></path><path d="M280 578l8 5v1h-3l-7-5 2-1z" class="AD"></path><path d="M262 564l11 9h-3c-3-2-6-5-9-8l1-1z" class="AT"></path><path d="M288 583c4 2 8 5 12 7h-1-1c-1 0-1 0-2 1-1 0-1-1-2-1v-1c-1-1-3-1-4-2-2-1-4-2-5-3h3v-1z" class="K"></path><path d="M294 589c1 0 3 1 4 1-1 0-1 0-2 1-1 0-1-1-2-1v-1z" class="t"></path><path d="M289 590c2 0 3 1 5 2l9 4-2 2c-2 1-3 3-5 3h0v-1c0-1 0-1-1-1-3-1-4-3-7-5l-1-1v-2l2-1z" class="J"></path><path d="M288 594c3 0 4 1 7 2 2 1 3 1 5 2h1c-2 1-3 3-5 3h0v-1c0-1 0-1-1-1-3-1-4-3-7-5z" class="L"></path><path d="M213 191c2 2 3 4 5 6 4 3 7 7 11 11 2 2 5 5 8 6h1l-1 1v-1l-1 1v1c1 2 2 3 4 4h0c1 1 1 1 2 0 1 1 1 2 2 3h0 0l1 1 1 2c3 2 4 3 6 5 1 2 1 3 2 5h0 2l2-4 1-1h0c1-2 2-2 3-3 1 1 1 1 1 2-2 2-4 4-4 7h1l1-1 1 1-1 3h1c1 2 0 4 0 7v7 5 2c0 2 0 4-2 5h-1 0c-1 1-1 2-2 3v2c1 2 0 6 0 8l-2-1v-2h-1l-1-1c0-1-1-3-1-5v-12c0-2 1-5 0-7l-1-1v-3c-3-5-6-6-11-8l-1 1h1c0 1 0 2-1 3s-1 2-2 4c-1 0-1 0-2-1h-1-1-6l-1 2c-1-1-1-1-1-2l-2 1c-1-3-3-6-4-9-3-5-6-9-9-14l-3-9c-1-2-2-4-2-7l1-2v-1c1 0 1 0 1-1v-1h1c1-1 4-3 5-5 0-1-1-2-1-3v-3l-1-1c1 1 1 1 2 1v-1z" class="AU"></path><path d="M243 224h2l1 2v1 3c-1 1-1 2-1 4 0-4-1-7-2-10z" class="U"></path><path d="M226 208c1 1 2 2 3 2 1 1 6 5 6 7h0l-10-8 1-1z" class="m"></path><path d="M245 234c0-2 0-3 1-4 1 4 6 5 7 9-3-2-5-3-8-4h-1l1-1z" class="P"></path><path d="M216 202c3 0 7 4 10 6l-1 1c-2-1-3-2-5-3-3-1-6-1-9 0 0-1 0 0-1-1 1-1 4-3 6-3z" class="k"></path><path d="M229 208c2 2 5 5 8 6h1l-1 1v-1l-1 1v1c1 2 2 3 4 4h0c1 1 1 1 2 0 1 1 1 2 2 3h0 0l1 1h-2l-8-7h0c0-2-5-6-6-7l1 1 1-1c-1-1-2-1-2-2z" class="Z"></path><path d="M246 230v-3-1c3 2 4 3 6 5 1 2 1 3 2 5h0c0 2 1 4 1 6-1-1-2-2-2-3-1-4-6-5-7-9z" class="G"></path><path d="M218 197c4 3 7 7 11 11 0 1 1 1 2 2l-1 1-1-1c-1 0-2-1-3-2-3-2-7-6-10-6 1-1 1-1 1-2h1v-3h0z" class="AJ"></path><path d="M256 236v1h0l1-1v1c1 6 1 13 0 19 0 2 1 5 1 7 0 1 0 1-1 2h1 1v1h0c-1 1-1 2-2 3v2c1 2 0 6 0 8l-2-1v-2l1-27c0-2 0-5-1-7 0-2-1-4-1-6h2z" class="F"></path><path d="M256 236v1h0l1-1v1 13h0l-1-1c0-2 0-5-1-7 0-2-1-4-1-6h2z" class="u"></path><path d="M213 191c2 2 3 4 5 6h0v3h-1c0 1 0 1-1 2-2 0-5 2-6 3 1 1 1 0 1 1-3 3-4 5-4 9-1-2-2-4-2-7l1-2v-1c1 0 1 0 1-1v-1h1c1-1 4-3 5-5 0-1-1-2-1-3v-3l-1-1c1 1 1 1 2 1v-1z" class="P"></path><path d="M213 191c2 2 3 4 5 6h0v3h-1c-2-2-4-5-5-8l-1-1c1 1 1 1 2 1v-1z" class="p"></path><path d="M259 231c1-2 2-2 3-3 1 1 1 1 1 2-2 2-4 4-4 7h1l1-1 1 1-1 3h1c1 2 0 4 0 7v7 5 2c0 2 0 4-2 5h-1v-1h-1-1c1-1 1-1 1-2 0-2-1-5-1-7 1-6 1-13 0-19v-1l-1 1h0v-1l2-4 1-1h0z" class="I"></path><path d="M261 240h1c1 2 0 4 0 7v7 5 2c0 2 0 4-2 5h-1v-1c1-8 0-17 2-25z" class="C"></path><defs><linearGradient id="Az" x1="219.264" y1="209.44" x2="232.882" y2="242.372" xlink:href="#B"><stop offset="0" stop-color="#1b191b"></stop><stop offset="1" stop-color="#3e3b3c"></stop></linearGradient></defs><path fill="url(#Az)" d="M219 238h1c-1-1-2-3-3-4-2-6-7-12-6-19 1-2 2-2 3-3 1 0 2-1 3 0 5 2 9 5 13 8 5 3 8 5 11 10 0 1 1 2 0 3 0 1-1 1-1 2-2 0-2 1-3 2h-1v1h1c0 1 1 1 2 1h1 0l-1 1h1c0 1 0 2-1 3s-1 2-2 4c-1 0-1 0-2-1h-1-1-6l-1 2c-1-1-1-1-1-2l-2 1c-1-3-3-6-4-9z"></path><path d="M232 241l4-4v1h1c0 1 1 1 2 1h1 0l-1 1h1c0 1 0 2-1 3s-1 2-2 4c-1 0-1 0-2-1h-1-1-6l-1 2c-1-1-1-1-1-2l1-5h0c1 0 1 1 1 1v1c1 1 2 1 3 1h1 0v-3h1z" class="J"></path><path d="M231 244h0v-3h1v1c1 0 2 0 2 1s0 1-1 1-1 1-2 0z" class="N"></path><path d="M239 240h1c0 1 0 2-1 3s-1 2-2 4c-1 0-1 0-2-1h-1c1-1 2-2 3-4 1-1 2-1 2-2z" class="C"></path><path d="M266 514v-1c2 1 4 1 6 1 0 0 2 1 2 0 5 2 10 4 14 8 0 1 1 2 3 3 4 4 7 8 9 14 0 1 1 3 1 5v2l-1 2c0 2 0 3-1 5v1l-1-1c0 1 0 3-1 4 1 1 1 1 0 2v4h-1v3 1h0l-3 6s0 1 1 2l-2 2c-1 0-1 1-2 2h1l-1 2-1-1-1-1-21-15-7-7c-2-2-3-4-3-7l2-1v-2h0c1-1 1-2 1-3v-8s1-3 1-4v-5l1-1v-1-2h1 0c1-1 1-2 2-3s1-2 2-2c1-1 4-1 5-1-1 0-1-1-2-1l-4-2z" class="AA"></path><path d="M282 523c1 1 4 3 4 5s-1 4-2 6h0c-1 1-2 2-4 2h0c-2 1-4 1-5 1v-1h0c1-1 2 0 3 0 1-1 0-1 1-1 2-1 4-2 5-4h0v-1c1-1 1-2 1-3h-2l-1-1v-3z" class="X"></path><path d="M289 531c0-1 0-2-1-3v-1h0c2 0 3 3 4 4 2 3 4 6 5 9 1 2 2 5 4 6l-1 2c0 2 0 3-1 5v1l-1-1c-3-8-6-15-9-22z" class="q"></path><path d="M296 543c0-1 0-1 1-1l1 2-1 2c0-1-1-2-1-3z" class="AB"></path><path d="M295 539l2 3c-1 0-1 0-1 1-1 0-1-2-2-3l1-1z" class="L"></path><path d="M298 544l2 4c0 2 0 3-1 5l-1-1c0-2 0-4-1-6l1-2z" class="t"></path><path d="M292 531c2 3 4 6 5 9 1 2 2 5 4 6l-1 2-2-4-1-2-2-3-3-5c-1-1 0-2 0-3z" class="Q"></path><path d="M289 531c3 7 6 14 9 22 0 1 0 3-1 4l-18-13c-1-1-5-4-5-5 2 0 5 1 8 0 4-1 5-5 7-8zm-26-8h0c1-1 1-2 2-3s1-2 2-2c1-1 4-1 5-1 3 1 8 2 10 5v1 3l1 1h2c0 1 0 2-1 3v1h0c-1 2-3 3-5 4-1 0 0 0-1 1-1 0-2-1-3 0h0v1h-1l-2-1h-2c-2-2-5-6-6-8 0-2-1-4-1-5z" class="V"></path><path d="M283 527h2c0 1 0 2-1 3v1h0c-1 2-3 3-5 4-1 0 0 0-1 1-1 0-2-1-3 0h0v1h-1l-2-1-1-1-2-3c1 1 3 3 5 3s4 0 6-2c2-1 2-3 3-6z" class="K"></path><path d="M261 532v-5l1-1v-1-2h1c0 1 1 3 1 5 1 2 4 6 6 8h2l2 1h-1c0 1 0 1 1 2 0 1 4 4 5 5l18 13c1 1 1 1 0 2v4h-1v-1l-20-13c0 1 0 1-1 1 0 1-3 3-4 4s-1 1-2 1c-1-1-1-2-2-3l-3-2v-1c0-2-3-4-4-5v-8s1-3 1-4z" class="Y"></path><path d="M267 540h0c2 0 4 1 5 3 2 2 3 4 5 5 1 1 1 1 2 1 2 2 5 4 8 5 3 2 7 3 9 7v1l-20-13-9-9z" class="S"></path><path d="M278 545l1-1 18 13c1 1 1 1 0 2v4h-1v-1-1c-2-4-6-5-9-7l-9-9z" class="L"></path><path d="M261 532v-5l1-1v-1-2h1c0 1 1 3 1 5 1 2 4 6 6 8h2l2 1h-1c0 1 0 1 1 2 0 1 4 4 5 5l-1 1h0c-3-1-5-3-8-5-1-2-3-3-4-4-2-1-3-3-4-5h-1v1h0z" class="T"></path><defs><linearGradient id="BA" x1="267.989" y1="541.199" x2="262.736" y2="545.993" xlink:href="#B"><stop offset="0" stop-color="#2b0c0d"></stop><stop offset="1" stop-color="#531313"></stop></linearGradient></defs><path fill="url(#BA)" d="M261 532h0c1 2 4 7 6 8h0l9 9c0 1 0 1-1 1 0 1-3 3-4 4s-1 1-2 1c-1-1-1-2-2-3l-3-2v-1c0-2-3-4-4-5v-8s1-3 1-4z"></path><path d="M269 555c-1-1-1-2-2-3l-3-2v-1l6 5 5-4c0 1-3 3-4 4s-1 1-2 1z" class="j"></path><path d="M260 544c1 1 4 3 4 5v1l3 2c1 1 1 2 2 3 1 0 1 0 2-1s4-3 4-4c1 0 1 0 1-1l20 13v1 3 1h0l-3 6s0 1 1 2l-2 2c-1 0-1 1-2 2h1l-1 2-1-1-1-1-21-15-7-7c-2-2-3-4-3-7l2-1v-2h0c1-1 1-2 1-3z" class="l"></path><path d="M283 568c2 1 3 1 5 2h0l-1 1h1c1 1 2 2 2 3v1l-1-1c-2-2-3-3-6-5h0v-1z" class="i"></path><path d="M274 562c1 1 3 2 4 3 2 1 4 2 5 3v1h0l-9-5v-2z" class="Y"></path><path d="M264 555c3 3 7 5 10 7v2c-2-1-3-2-5-3-1 0-1-1-2-1h0l-4-4h1v-1z" class="e"></path><path d="M288 570l3 2 2 1h0s0 1 1 2l-2 2c-1 0-1 1-2 2h1l-1 2-1-1-1-1 1-1c1-1 1-2 1-3v-1c0-1-1-2-2-3h-1l1-1z" class="L"></path><path d="M293 573s0 1 1 2l-2 2c-1 0-1 1-2 2h1l-1 2-1-1v-1c1-1 2-2 2-3s1-2 2-3h0z" class="AA"></path><path d="M260 544c1 1 4 3 4 5v1l3 2c1 1 1 2 2 3 1 0 1 0 2-1 3 3 6 5 10 8l6 4v1l1 1s1 0 1 1h0c1 0 2 1 2 1v1h1 1c0 1 0 1-1 1h-1l-3-2h0c-2-1-3-1-5-2-1-1-3-2-5-3-1-1-3-2-4-3-3-2-7-4-10-7v1h-1l-5-6h-1l2-1v-2h0c1-1 1-2 1-3z" class="AG"></path><path d="M279 563c1-1 1-1 2-1l6 4v1l-8-4z" class="j"></path><path d="M259 547c1 2 2 3 3 5 0 1 1 2 2 3v1h-1l-5-6h-1l2-1v-2h0z" class="o"></path><path d="M269 555c1 0 1 0 2-1 3 3 6 5 10 8-1 0-1 0-2 1-4-2-6-6-10-8z" class="f"></path><defs><linearGradient id="BB" x1="287.031" y1="555.867" x2="282.039" y2="562.124" xlink:href="#B"><stop offset="0" stop-color="#27090b"></stop><stop offset="1" stop-color="#4d1313"></stop></linearGradient></defs><path fill="url(#BB)" d="M276 549l20 13v1 3 1h0l-3 6h0l-2-1h1c1 0 1 0 1-1h-1-1v-1s-1-1-2-1h0c0-1-1-1-1-1l-1-1v-1l-6-4c-4-3-7-5-10-8 1-1 4-3 4-4 1 0 1 0 1-1z"></path><path d="M287 566l6 3-1 1h-1s-1-1-2-1h0c0-1-1-1-1-1l-1-1v-1z" class="g"></path><path d="M293 569h1c0-1 1-1 1-2v-1l1 1h0l-3 6h0l-2-1h1c1 0 1 0 1-1h-1-1v-1h1l1-1z" class="X"></path><path d="M171 129h3c0 8-3 14-3 21 0 1 0 2 1 3v1l-1 6-1 23v8 5 1h-1v4c0 1 0 2 1 3-1 1-1 2-1 3l-1 2c-1 0-1 0-1-1-2 1-3 2-4 3l-6 2c-1-1-5-1-6-1s-1-1-2-1l-1-1h0v-1l-1-1-1 2-1 4c-3-1-5 1-8 0l1-1h-1l-4-1v1l-2-1-1-1-1 1v-2c-1-2-1-3-2-4l-2 2c-1 1-1 2-2 3-1 0-2-1-3-1v-2l2 1c1-1 3-9 4-10 1-3 2-6 3-8 1-4 3-7 4-10 4-8 8-16 13-23l1-1s1 0 1-1l1 1h-1v1c4-7 9-13 13-19 3-4 6-8 10-10z" class="J"></path><path d="M171 150c0 1 0 2 1 3v1l-1 6v-4c-1 1-1 2-1 4h-1c0-3 1-7 2-10z" class="O"></path><path d="M148 177v-1c0-1 0-1 1-1v-1-1c2-4 1-7 4-10h1s-1 0-1 1c-1 1-2 2-2 4 1-1 1 0 2-1 0 0 0-1 1-1h0 1l-5 13h-1v-1h-1v-1z" class="b"></path><path d="M150 179c-1 2-3 5-5 7-1 1-2 2-4 2-1 0-1 0-2-1h0v-1c2 0 3 0 4-2h1c0-1 1-2 2-3v-1l2-3v1h1v1h1z" class="B"></path><path d="M148 178h1v1c-1 2-2 4-3 5-1 2-2 3-3 3l-1-1c3-2 5-5 6-8z" class="U"></path><path d="M152 183c0-1 1-2 1-3h1l-3 10c0 2-1 4-1 6-1 4-2 9-2 14v-1l-1-1-1-2c1-3 2-7 2-11 1-4 2-8 4-12z" class="R"></path><path d="M152 183c0-2 0-3 1-4 0-1 1-2 1-3v-1l1-2c1-2 2-5 3-7v2h1v-3c1 2 0 4 0 6-1 3-2 5-3 6-1 3-2 5-3 7l-2 12h-1c0-2 1-4 1-6l3-10h-1c0 1-1 2-1 3z" class="b"></path><defs><linearGradient id="BC" x1="170.438" y1="172.182" x2="167.857" y2="172.248" xlink:href="#B"><stop offset="0" stop-color="#7c7267"></stop><stop offset="1" stop-color="#8e8881"></stop></linearGradient></defs><path fill="url(#BC)" d="M169 160h1c0-2 0-3 1-4v4l-1 23v8c-1-1-1-2-1-3l-1 1v-11c0-6 0-12 1-18z"></path><path d="M135 183h1c0 1-1 3-1 4-1 3-2 7-4 10v7 1l1 1c0 2 0 2 2 3v1h1s1 0 2 1h0 5c1-1 2-2 3-4v-1c1-2 1-4 2-5v-1-2c-1 2-1 3-2 4h0c0-1 2-7 3-8v1c0 4-1 8-2 11l1 2-1 2-1 4c-3-1-5 1-8 0l1-1h-1l-4-1v1l-2-1-1-1-1 1v-2c-1-2-1-3-2-4h0l2-2v1h0 1v-1h1c0-2-1-4 0-6 0-3 1-5 2-7l2-8z" class="G"></path><path d="M137 211c2 1 3 1 5 1l-1 1h-3-1v-2z" class="m"></path><path d="M133 210c1 0 2 1 4 1v2l-4-1v1l-2-1v-1h1l1-1z" class="AJ"></path><path d="M129 204v1h0 1v-1h1c0 1 0 4 1 5l1 1-1 1h-1v1l-1-1-1 1v-2c-1-2-1-3-2-4h0l2-2z" class="AK"></path><path d="M129 204v1h0v3l1 1-1 1c-1-2-1-3-2-4h0l2-2z" class="p"></path><path d="M146 206l1 2-1 2-1 4c-3-1-5 1-8 0l1-1h3l1-1v-1c2-1 3-3 4-5z" class="E"></path><path d="M166 197c0-3 0-6 1-8 1-4 0-8 1-11v11l1-1c0 1 0 2 1 3v5 1h-1v4c0 1 0 2 1 3-1 1-1 2-1 3l-1 2c-1 0-1 0-1-1-2 1-3 2-4 3l-6 2c-1-1-5-1-6-1s-1-1-2-1h2c3 0 5-1 7-1h0c3-2 5-3 7-6l1-7z" class="D"></path><path d="M151 211c3 0 5-1 7-1 1 0 1 0 1 1-2 1-6 1-7 0h-1z" class="m"></path><path d="M166 197c1 2 1 4 1 6 0 1 0 0-1 1 0-1 0 0-1 0l1-7z" class="Z"></path><path d="M168 189l1-1c0 1 0 2 1 3v5 1h-1v4c-1-4-1-8-1-12z" class="y"></path><defs><linearGradient id="BD" x1="163.863" y1="208.368" x2="160.08" y2="207.288" xlink:href="#B"><stop offset="0" stop-color="#6a615d"></stop><stop offset="1" stop-color="#787266"></stop></linearGradient></defs><path fill="url(#BD)" d="M165 204c1 0 1-1 1 0-1 3-1 5-4 6-1 1-2 1-3 1 0-1 0-1-1-1h0c3-2 5-3 7-6z"></path><path d="M146 158l1-1s1 0 1-1l1 1h-1v1l-12 22-1 3-2 8c-1 2-2 4-2 7-1 2 0 4 0 6h-1v1h-1 0v-1l-2 2h0l-2 2c-1 1-1 2-2 3-1 0-2-1-3-1v-2l2 1c1-1 3-9 4-10 1-3 2-6 3-8 1-4 3-7 4-10 4-8 8-16 13-23z" class="s"></path><path d="M127 202l1-1c1 1 1 2 1 3l-2 2h0l-2 2v-2c1-1 2-2 2-4z" class="u"></path><path d="M122 209h1v-1h1c0-1 1-1 1-2v2c-1 1-1 2-2 3-1 0-2-1-3-1v-2l2 1z" class="k"></path><path d="M127 202h-1c2-8 5-16 10-22l-1 3-2 8c-1 2-2 4-2 7-1 2 0 4 0 6h-1v1h-1 0v-1c0-1 0-2-1-3l-1 1z" class="B"></path><path d="M128 201c0-1 1-3 2-4v7 1h-1 0v-1c0-1 0-2-1-3z" class="r"></path><path d="M182 122c0-1 1-1 2-2 3-1 4-2 7-1l5 3h0c-1 2-1 4-2 6-1 8-2 17 0 25 0 1 1 3 1 3l1 3h3l1 2 3 8 5 10c-2 1-2 1-3 2h1l1 3 4 7 1 1v3c0 1 1 2 1 3-1 2-4 4-5 5h-1c-3 0-4-2-6-3h0-3 0v1l-1-1v4l-2-1c0 2 0 3-2 5-2 1-4 1-5 1h-1c-2 1-5 0-8 0h0v1h3v1l-1 1h-3-5v-1c-1-1-2-3-3-5v-2c-1-1-1-2-1-3v-4h1v-1-5-8l1-23 1-6v-1c-1-1-1-2-1-3 0-7 3-13 3-21h-3c2-2 3-4 5-6v-1c1 1 1 1 2 1h1l-1 1c1-1 3-1 4-2z" class="w"></path><path d="M188 167v-2h1c0 1 1 2 0 3-1 2 0 3 0 5-1-2-1-4-1-6z" class="AF"></path><path d="M187 161l1 1h0v-1h1v4h-1v2l-1-6z" class="x"></path><path d="M189 173l2 8c-1 0-1 0-1-1 0 1 0 2-1 2 0-3-1-6 0-9z" class="P"></path><path d="M181 139l1 1v3 1h-1l-1 1-1 1h0c0-3 1-5 2-7z" class="G"></path><path d="M180 182h1c1 0 1 0 2 1v4h0c1 0 2 0 3 1 0 1 0 1-1 2h-1l1-1c0-1 0-1-1-1l-1 1c1 0 1 1 1 1 0 1 1 2 1 3-1-1-1-3-2-4l-1-1v-1c-2 0-2-3-2-5h0z" class="I"></path><path d="M187 147v-4c1 1 1 4 1 5v8c1 2 1 4 1 5h-1v1h0l-1-1v-4-10z" class="Z"></path><defs><linearGradient id="BE" x1="197.793" y1="160.004" x2="200.606" y2="166.136" xlink:href="#B"><stop offset="0" stop-color="#78746b"></stop><stop offset="1" stop-color="#968f86"></stop></linearGradient></defs><path fill="url(#BE)" d="M196 159h3l1 2 3 8-2-1h0 0v2l-5-11z"></path><path d="M189 168c2 6 3 12 5 17v1c0 1-1 1-1 2-1-2-1-5-2-7l-2-8h0c0-2-1-3 0-5z" class="AK"></path><defs><linearGradient id="BF" x1="202.025" y1="167.091" x2="204.924" y2="179.534" xlink:href="#B"><stop offset="0" stop-color="#a39c92"></stop><stop offset="1" stop-color="#cac6bc"></stop></linearGradient></defs><path fill="url(#BF)" d="M201 170v-2h0 0l2 1 5 10c-2 1-2 1-3 2l-4-11z"></path><path d="M193 188c0-1 1-1 1-2 2 5 4 9 7 14h0-3 0v1l-1-1-1-5c-1-2-2-5-3-7z" class="AM"></path><path d="M187 147v-14-2h0c1-2 2-5 2-7l-1 24c0-1 0-4-1-5v4z" class="I"></path><path d="M206 181l1 3 4 7 1 1v3c-1-2-2-4-4-5h-4 0c0 1 0 2 1 2v5c-3-2-7-6-7-10l3-2c1 0 2 0 3 1h1l2 1c0-1 0-2-1-3v-3z" class="B"></path><path d="M205 192c-1 0-2-1-2-2l-1-1v-2-1c1 1 3 3 4 3s2 1 2 1h-4 0c0 1 0 2 1 2z" class="F"></path><path d="M176 123v-1c1 1 1 1 2 1h1l-1 1c1-1 3-1 4-2-2 2-3 4-4 5-4 9-5 18-6 27v-1c-1-1-1-2-1-3 0-7 3-13 3-21h-3c2-2 3-4 5-6z" class="C"></path><path d="M176 123v-1c1 1 1 1 2 1h1l-1 1-1 2h-1v-3z" class="P"></path><defs><linearGradient id="BG" x1="189.955" y1="193.15" x2="194.652" y2="193.12" xlink:href="#B"><stop offset="0" stop-color="#695f57"></stop><stop offset="1" stop-color="#7f766d"></stop></linearGradient></defs><path fill="url(#BG)" d="M189 182c1 0 1-1 1-2 0 1 0 1 1 1 1 2 1 5 2 7s2 5 3 7l1 5v4l-2-1c0 2 0 3-2 5-2 1-4 1-5 1l-1-1h1c2-1 4-1 5-3 1-1 1-2 1-4s-1-5-2-7l-3-12z"></path><path d="M195 203c0-2 1-4 0-6 0-1 0-1 1-2l1 5v4l-2-1z" class="Z"></path><path d="M170 183l3 17c1 3 2 5 4 7 2 1 7 1 10 1l1 1h-1c-2 1-5 0-8 0h0v1h3v1l-1 1h-3-5v-1c-1-1-2-3-3-5v-2c-1-1-1-2-1-3v-4h1v-1-5-8z" class="C"></path><path d="M174 208l1-1c1 0 1 0 1 1 1 0 3 1 3 1h0v1h-2c-1 0-2-1-3-2z" class="U"></path><path d="M169 201v-4h1c0 2 1 5 2 8-1 0-1 0-2 1v-2c-1-1-1-2-1-3z" class="r"></path><path d="M170 206c1-1 1-1 2-1 1 1 1 2 2 3s2 2 3 2h2 3v1l-1 1h-3-5v-1c-1-1-2-3-3-5z" class="n"></path><path d="M177 210h2 3v1l-1 1h-3-5c2 0 3 0 4-1v-1z" class="AJ"></path><defs><linearGradient id="BH" x1="356.296" y1="447.426" x2="261.551" y2="489.87" xlink:href="#B"><stop offset="0" stop-color="#010002"></stop><stop offset="1" stop-color="#3b3738"></stop></linearGradient></defs><path fill="url(#BH)" d="M316 328l1-1c1 3 1 5 2 8 0 1 0 2 1 3l2-2 2-3c1-1 1-2 2-2l2-2c0 1 1 3 1 3l-1 1h2c2 1 3 1 5 3h0c1 1 1 2 1 3 2 6 2 11 2 16 0 6 0 11-1 16 0 2-1 3-2 5v1c0 2-1 4-1 5v1c1 0 1 0 1 1s1 2 1 3c1 0 2 0 3-1l5 2 4 4-2 1v1h0v2c0 1 1 1 1 2h1l1 1c1-1 1-2 1-3l1 2c2 2 3 4 4 6h-2l2 4 1 7v2l1 3v6 4c-1 6-2 13-5 18-1 2-2 5-4 6v2 11c2-2 5-3 9-4-3 2-5 4-8 4h-1c-1 1-1 1 0 2h11c-3 0-10 0-12 1v1 30c0 5 0 11 1 16v1 1 2h1c1 1 2 2 4 3-1 1-2 1-3 1 0-1-1-1-2-2-1 2 0 3 0 4v22l-1 24c0 3 0 5 1 8-1 7-1 16 2 22 2 5 4 8 8 12l-2-1v2c-1-2-3-4-4-5v1h-1-1l-3-3c0-1-1-2-1-3l-1 1-2-1v-1c-1-1-1-3-1-4v-2c-1 0-2 1-3 1h0l-2 2-2 3c-1 0-1 0-1-1l-2 2v-2l-6 6c-1 1-2 1-3 2-1 0-2 1-2 2h0c-1 0-1 0-1-1-1 1-1 0-1 1-2 2-4 1-5 2l-1-1v-1h-2-1c0-1-1-1-1-1h-1l-2 1-1 1-9 6c-1 1-1 1-2 1l-1 1-14 4-13 3c-2 1-5 1-7 1 1-1 1-1 2-1 2-1 3-4 5-6s5-4 7-6h0 1c2-2 2 0 4 0l-1-1c1-1 2-2 3-2l5-4h0l1-1c1-1 2-1 3-2l3-3v-1l1-1c1 0 2-1 3-2 2 0 3-2 5-3l2-2 1-1h2l2-2c3-1 5-3 9-3 0-2 1-5 1-7-1-3 0-5 0-8l-4 1c-1 1-2 1-3 2l-12-2h0c-2-1-4-1-6 1l-2 2h-1c1-1 1-2 2-2l2-2c-1-1-1-2-1-2l3-6h0v-1-3h1v-4c1-1 1-1 0-2 1-1 1-3 1-4l1 1v-1c1-2 1-3 1-5l1-2v-2c0-2-1-4-1-5-2-6-5-10-9-14-2-1-3-2-3-3-4-4-9-6-14-8 0 1-2 0-2 0l1-1c-1 0-1 1-2 1l-1-1c-2 0-3 0-4-1s-2-2-3-2h-1c-1-1-2-1-4-1-1 0-1 0-2-1-2 1-4 1-6 1-1 0-2 0-4-1-1 0-1 1-1 2-1-1-1-2-2-3 0-1 0-1 1-2l-1-2 1-1h-1v-6l-1-9c0-1-1-2 0-3 0-1-1-2-1-3l-1-2h-1v-2h-1l-1-5h-1l-1-1c-1-1 0-1 0-2l1-1h0v-1 1l3-2c0-1 0-2-1-3v-1c1-1 1-2 2-2h1c0-1 0-2 1-2v-1-1c0-1 1-2 1-2v-1l1-1v-3h1c0-1 0-1-1-2 0 1 0 1-1 2 0 1 0 1-1 2v-1-1l1-1h0v-1l1-1h0v-1l1-1h0v-1l1-1c0-1 2-4 4-5v-1-1-1-1h0l-1-1v-1-1-1-4-3h1l-2-6-1-2v-1c-1 0-1-1-1-2h0l-1-1v-5c1-4 3-6 5-9 3-3 5-6 8-9l9-6h0v-2h0 0l1-1h-1v-1h-3c4-3 9-5 13-7s8-3 10-6c1 0 2 0 2-1v1c1 0 1 0 1 1h2 1c1-1 1-1 2-1v-1h1 1l2-1h2c1-1 2-1 4-2v1 1c1 0 2 0 3-1 2 0 4 0 6-1 1 1 2 1 3 1h0 1v1c0 2-1 9 0 11 1-4 1-8 1-12 0-12 0-22-3-33z"></path><path d="M305 539h3c-1 1-2 1-3 1h1l-1-1z" class="F"></path><path d="M304 518h2 0l-1 1-1 1h0v-2zm-1 30c1 1 2 1 2 2-1 0-1 0-2 1v-3h0z" class="B"></path><path d="M305 540c-1 1-2 1-3 1 0-1 0-1-1-1 1-1 3-1 4-1l1 1h-1z" class="c"></path><path d="M264 476l2 2-1 1-2-2 1-1z" class="m"></path><path d="M302 528h-6v-1h2 1 3v1zm4-70h1v1c1 1 1 2 0 3-1-1-1-1-1-2-1 0 0-1 0-2z" class="B"></path><path d="M306 422c2-1 5-2 7-2h0l-1 1c-2 1-3 2-5 2l-1-1z" class="G"></path><path d="M260 406l1-1 2 2-1 2h-1-1v-3z" class="J"></path><path d="M302 392c2-1 4-1 7-1h0c-2 1-5 1-7 2v-1z" class="q"></path><path d="M302 527h4c1 0 2 0 3 1h1c-2 1-6 0-7 0h-1v-1z" class="c"></path><path d="M302 527h4 0c-1 1-3 1-4 1v-1z" class="D"></path><path d="M306 422l1 1c-1 1-3 1-4 2-1 0-2 0-4 1v-1l7-3z" class="AA"></path><path d="M305 433c1 0 5 3 5 4s1 1 1 1h-1l-2-1c-1 0-1 0-2-1h0l-2-1c0-1 1-1 1-2z" class="D"></path><path d="M285 514l1-1h1v-1c1 1 2 1 3 1 0-1 1-1 1-2l2-1c1 0 2-1 3-1h1v-1l1 1c-3 1-6 2-8 4v2c-1-1-3-1-5-1z" class="a"></path><path d="M267 467l1 1c1 0 1 1 1 2 1 1 1 3 2 4h0v1h-1c-1-1-2-2-2-3-1-1-1-3-1-5z" class="C"></path><path d="M299 425v1c-2 1-5 2-8 3-1 0-1 1-2 2l-2-2c4-1 8-2 12-4zm15-65c1 1 2 1 3 1h-4l1 1v1h0v1l2-1s0-1 1-1v1h1c-2 1-5 2-7 2v-3c-1 0-2-1-3-1 2 0 4 0 6-1z" class="a"></path><path d="M270 440c1 1 1 1 2 0l1 1c0 1-1 2-1 3h-1c0-1-1-1-1-2-1 1-1 1-1 2v1c-1 2-3 6-5 8v-1c2-2 3-5 4-8 1-1 1-2 2-4z" class="B"></path><path d="M283 499l1 1 7-6h0c-1 1-4 3-4 5 1-1 2-1 3-1l-2 1c-1 1-2 2-2 3l-1 1h-1l-1-1c1 0 1-1 1-2l-1-1z" class="C"></path><path d="M284 500h1v1 2h-1l-1-1c1 0 1-1 1-2z" class="R"></path><path d="M297 467h-1l1-1c0-1 0-1-1-1-1-2-1-4 0-5v1c0 3 1 4 3 6 2 1 4 2 5 4v1c-2-1-3-2-4-3s-2-1-3-2z" class="C"></path><path d="M312 423c1 1 0 5-1 6 0 1-1 2-2 3v-1-3-3h0l2-2h1z" class="L"></path><path d="M309 425h0l2-2c0 2 0 3-1 4l-1 1v-3z" class="J"></path><path d="M285 514c2 0 4 0 5 1h3c1 0 2 0 3 1 1 0 3 1 4 1h0l1 1h-2c-3-1-5-1-8-2h-5l-1-1v-1z" class="R"></path><path d="M289 480c1 0 1 0 2 1l-1 1h-1c-1 0-1 1-2 2 0 2 3 4 4 6h-1c-1-1-2-1-3-3-1-1-2-3-4-4l6-3z" class="M"></path><path d="M294 480h1 3c0 1 1 1 1 1 1 0 1 0 2 1v1c2 0 4-2 7-2 1 0 1 0 2 1h-2 0l-4 2c0 1-1 1-2 1h-1l-1 1c-1-1-1-1-2-1l1-1h0c1 1 1 1 2 1-2-1-1-2-2-3-2-1-2-1-4-1l-1-1z" class="D"></path><path d="M270 440c0-2 1-4 3-6 0-1 1-1 2-1v1l1 1s1 0 2-1c1 1 2 1 2 3-1-1-2-2-3-2-1 1-3 2-4 3v1h-1v1c-1 1-1 1-2 0z" class="E"></path><path d="M301 544l1 1c0 1 0 2 1 3h0v3 1h-1-1c0 1 0 2-1 2 0 1 0 2-1 3v-3-1c1-2 1-3 1-5l1-2v-2z" class="H"></path><path d="M302 552v-3l1-1v3 1h-1z" class="E"></path><path d="M266 458c1-1 1-1 1-2v-2h0c1-1 0-1 1-1v-3h1v2-1-1c1-1 0-2 0-3h0v-2c0 1 1 1 1 2 0 0 0 1 1 2-1 1-1 2-1 3v1l1-1v1c-1 0-1 1-2 1v2c-1 1 0 2-1 3v3h0s0-1-1-2h0l-1-2z" class="F"></path><path d="M278 467h2v1c0 2-2 3-3 4l-2 2h-1c0 1 0 1-1 1h-1l-1-1 1-1c1-1 2-1 2-2l4-4z" class="C"></path><path d="M261 446c1-2 2-4 3-7 1-2 5-7 6-8h2c0 2-3 5-4 7-1 1-2 3-2 5-1 2-2 6-4 8h0 0c0-1 1-2 1-3l2-4c0-2 1-3 2-5h0l3-5v-1l2-2h0-1c-1 0-1 1-2 2v1c-1 0-1 1-1 1l-1 1v2l-2 2c-2 3-2 5-3 7l-1-1z" class="a"></path><path d="M267 478v-1c2 1 5 4 6 5v1h1c1 1 2 3 3 5l3 3h0v2h0v1l-1-2-3-3c-3-4-6-8-9-11z" class="N"></path><path d="M281 444h0l-4 5c-2 0-4 0-6-1v1c1 1 3 2 4 2h2 2c1 0 2-1 3-2l1 1v2h-2-2l-3 1 2 3v1h-1s-2-1-2-2 0-1-1-2h-1c-1-1-1-1-1-2s-1-1-1-2v-2h0c1 1 2 1 2 1 1 1 2 1 4 1 0-1 1-2 2-3 0-1 1-1 2-2z" class="E"></path><path d="M282 449l1 1v2h-2-2l-3 1h-1v-1c1 0 2 0 4-1 1 0 2-1 3-2z" class="x"></path><path d="M294 476c-2-1-4-3-5-4s-3-2-4-3-3-1-4-2-1-1-1-2 1-1 1-1l1 1c1 1 2 1 3 2 4 1 8 5 10 9h0-1z" class="R"></path><path d="M299 426c2-1 3-1 4-1l-3 3v1-1c-1 1-2 3-3 3-3 2-5 0-8 0 1-1 1-2 2-2l8-3z" class="G"></path><path d="M305 360v1 1c1 0 2 0 3-1 1 0 2 1 3 1v3h-2l-5 1v-2h-3-1l-2-1 3-1c1-1 2-1 4-2z" class="P"></path><path d="M301 364c3-1 4-1 7-1v1l1 1-5 1v-2h-3z" class="s"></path><path d="M266 478h1c3 3 6 7 9 11l3 3v1c0 1 0 2 1 3v1c0-1-1-1-1-1l-3-4c-2-4-7-9-11-13l1-1z" class="d"></path><path d="M312 421l1 1-1 1h-1l-2 2h0l-3 4-2 2c-1 1-3 0-4 0v-2-1l3-3c1-1 3-1 4-2 2 0 3-1 5-2z" class="W"></path><path d="M280 399c-4 2-8 3-11 5l22-8c0 1-1 1-2 2-1 0-2 0-3 1h-1c-1 0-1 1-2 1h0-1c-1 1-1 0-1 1-1 0-2 0-3 1h0 0c-2 0-4 1-5 2h-1-1l-1 1c-1 0 1 0 0 0h-1c-1 1-2 2-3 2v2l-3 3-1 1c-1 0-1 0-2-1l1-2 3-3 1-1v-1c1-1 2-1 3-2h1c0-1 0-1 1-1h1c1-1 2-1 2-1l1-1 3-1h1 2z" class="AA"></path><path d="M280 399c2 0 4-4 6-5 5-2 11-6 17-5h-1c-1 0-3 0-4 1h-1c-1 0-1 0-1 1h-1c-1 0-2 0-3 1l-1 1h-1c-1 0-1 1-2 1h0-1c0 1 0 1-1 1l-1 1v1c-1 0-1 0-2 1 0 0-1 0-2 1h0 2 0c2-1 4-2 5-2v-1h2 1l-1-1v-1c1-1 3-1 5-2 0 0 1 0 1-1h1 1c1 0 1-1 1-1h2 2c-3 1-7 1-9 4 2-1 5-2 8-2v1l-9 3h-2l-22 8c3-2 7-3 11-5z" class="Q"></path><path d="M278 416c-3 0-6 1-9 1-1 0-1 0-1 1h-4c-1 0-1-1-1-1 1-2 2-2 3-3l1 1c2 0 7 1 9-1l-1-1 1-1v-1h0 1 1l1-1s1 0 2-1h1 1c1-1 0-1 1-1s1 0 2-1h2l1-1c1-1 2-1 3-1h0l-9 4-6 3v2h1c0-1 1-1 1-1 1 0 2-1 3-1 2-1 4-2 6-2h0c-1 0-1 1-2 1s-2 0-2 1h-2l-1 1h2 1v-1h2 0c-2 1-5 2-6 3l1 1h-3z" class="L"></path><path d="M298 509c1-1 2-1 4-1 1-1 3-1 5-1 1 0 1 0 2 1h0-1c-1 0-3 1-4 1v1 1c-3 1-5 3-8 5-1-1-2-1-3-1h-3v-2c2-2 5-3 8-4z" class="J"></path><path d="M304 510v1c-3 1-5 3-8 5-1-1-2-1-3-1 4-3 7-4 11-5z" class="M"></path><path d="M307 571h3c1-1 2-1 3-1v1c-1 0-2 0-3 1h-5 0c-1 1-2 1-3 2h0 2c1 0 2 0 3-1 2 0 4 1 5 0 1 0 3 0 4-1h2v-34-12-6-1 40 11 6 7c-1-3 0-5 0-8l-4 1c-1 1-2 1-3 2l-12-2h0l1-1h0 1l-1-1 7-3z" class="G"></path><path d="M307 413c2-1 4-2 6-2h0c-6 4-12 5-18 8l-6 3h0l1 1c0 1 0 1-1 1-1 1-2 1-3 1l-1 1s-1 1-2 1c0-1-1-2-1-3v-2c1 0 1-1 2-2-1 0-1-1-1-2h6c2-2 5-3 8-4 1-1 3-1 5-2 1-1 1-1 3-1 1 1 1 1 2 1h0v1z" class="AY"></path><path d="M285 424c1 0 3-1 4-2l1 1c0 1 0 1-1 1-1 1-2 1-3 1l-1 1s-1 1-2 1c0-1-1-2-1-3l1-1 2 1z" class="w"></path><path d="M282 424l1-1 2 1h-1v1h2l-1 1s-1 1-2 1c0-1-1-2-1-3z" class="AC"></path><path d="M289 418h1 1c-2 2-4 2-6 3-1 0-1 0-1-1-1 0-1-1-1-2h6z" class="f"></path><path d="M305 411c1 1 1 1 2 1h0v1l-8 3c-2 1-5 1-6 3-1 0-2 1-3 1 1-2 3-3 4-4l4-2 7-3z" class="AU"></path><path d="M305 550c1 0 1 0 2 1h1v1l1 1c-2 0-2-2-4-1 0 1 1 1 1 2h0c1 2 2 2 3 3h1c1 0 1 1 2 1l1 1h1v1c-1 0 0 0-1-1h-1c-1 0-1 0-1-1-1 0-3 0-4-1l1 1h1c0 1 1 1 1 1l1 1c1 0 2 0 3 1h-2l-1-1h-2 0c0-1-2-2-3-2l-1-1c0 1 0 2 1 3l-2 2s-1 0-1-1l-2 2h0v1c-1 0-1 1-1 2 0 2-2 4-2 6h1 0c0-1 1-2 2-2 1-1 1-1 2-1 0 1 2 0 2 1 1 0 2 1 2 1l-7 3 1 1h-1 0l-1 1c-2-1-4-1-6 1l-2 2h-1c1-1 1-2 2-2l2-2c-1-1-1-2-1-2l3-6h0v-1-3h1v-4c1-1 1-1 0-2 1-1 1-3 1-4l1 1v3c1-1 1-2 1-3 1 0 1-1 1-2h1 1v-1c1-1 1-1 2-1z" class="W"></path><path d="M305 550c1 0 1 0 2 1h1v1l-3-1-1 1h1v4h0l-1 1h0c0 1 0 2 1 3v1h-1 0-2-1c-1 0-1 1-2 1-1-1-1-2-1-3 1 0 1-1 1-1v-1c1-1 1-2 1-3 1 0 1-1 1-2h1 1v-1c1-1 1-1 2-1z" class="a"></path><path d="M302 552h1v1c1 2 0 4 0 6l-1 2h-1c0-2 1-4 1-6v-3z" class="U"></path><path d="M302 552h1v1 2h-1v-3z" class="C"></path><path d="M299 557c1-1 1-2 1-3 1 0 1-1 1-2h1v3c0 2-1 4-1 6-1 0-1 1-2 1-1-1-1-2-1-3 1 0 1-1 1-1v-1z" class="N"></path><path d="M298 553l1 1v3 1s0 1-1 1c0 1 0 2 1 3 1 0 1-1 2-1h1c-2 2-2 4-3 6 0 3-1 5-3 7h-1l-1 1c-1-1-1-2-1-2l3-6h0v-1-3h1v-4c1-1 1-1 0-2 1-1 1-3 1-4z" class="F"></path><path d="M297 563h0 1c1 1 0 3-1 4h-1 0v-1-3h1z" class="I"></path><path d="M296 567h1c0 3-1 4-2 7l-1 1c-1-1-1-2-1-2l3-6z" class="G"></path><defs><linearGradient id="BI" x1="288.274" y1="375.994" x2="287.991" y2="364.596" xlink:href="#B"><stop offset="0" stop-color="#736a63"></stop><stop offset="1" stop-color="#a99a8b"></stop></linearGradient></defs><path fill="url(#BI)" d="M292 365h1c1-1 1-1 2-1v-1h1 1l2-1h2l-3 1 2 1h1 3v2c-5 1-10 2-15 4-8 3-15 7-22 11h0v-2h0 0l1-1h-1v-1h-3c4-3 9-5 13-7s8-3 10-6c1 0 2 0 2-1v1c1 0 1 0 1 1h2z"></path><path d="M298 363l2 1c-3 2-6 3-9 2l7-3z" class="AJ"></path><path d="M292 365h1c1-1 1-1 2-1v-1h1 1l2-1h2l-3 1-7 3c-5 2-10 4-14 7-3 2-6 5-10 6h0l1-1h-1v-1h-3c4-3 9-5 13-7s8-3 10-6c1 0 2 0 2-1v1c1 0 1 0 1 1h2z" class="H"></path><path d="M278 428c1 0 3 0 4 1 1 2 3 2 5 3l-1 1h0-5-1v-1h-1c-1 1-2 1-4 1-1 0-2 0-2 1-2 2-3 4-3 6-1 2-1 3-2 4-1 3-2 6-4 8v1c-1 1-2 1-3 1 0-1 1-2 1-3 2-2 3-6 4-8 0-2 1-4 2-5 1-2 4-5 4-7h-2c-1 1-5 6-6 8-1 3-2 5-3 7 0 1-1 2-1 3v1c-1 3-1 7-1 9 0 0 0-1-1-2 0-8 0-20 6-26 3-4 9-1 13-2l1-1z" class="I"></path><path d="M261 421l4 3c0 1 0 1-2 1 6 0 10 2 15 3l-1 1c-4 1-10-2-13 2-6 6-6 18-6 26 1 1 1 2 1 2 0 2 0 4 1 6 0 1 1 3 1 4h0v1h0c0-1-1-1-1-1 0-1-1-2-2-2-2-4-2-8-2-12-1-7 0-13 1-21l1-2c-1-1-1-1-1-2v-2l1-5h0c1 0 2 1 3 1v-2-1z" class="w"></path><path d="M258 423h0c0 1 0 1 1 2 1 0 1 0 2 1-2 1-2 4-3 6-1-1-1-1-1-2v-2l1-5z" class="Z"></path><path d="M261 421l4 3c0 1 0 1-2 1l-2 1c-1-1-1-1-2-1-1-1-1-1-1-2 1 0 2 1 3 1v-2-1z" class="AL"></path><path d="M284 447c1 0 3 1 4 2v2c1 0 1 1 1 2l-2 2h0l3 3v1h1l2 2v1c1 2 2 4 2 7l1 2h1c0-2 0-2-1-4h1c1 1 2 1 3 2s2 2 4 3v-1l1 2v2c2 0 3 0 4 1-1 0-1 0-2 1v1h1c-3 0-5 1-7 2-1 0-1 1-2 0h-1 0c-1-1 0-1-1-1h-1c-2-1-3-1-5-2h-1 1 3v-1h1 0c-2-4-6-8-10-9-1-1-2-1-3-2h1s1 1 2 1c-1-1-2-1-2-2l-1-1-1-2h0l-1-1c-1-1-1-2-2-3v-1l-2-3 3-1h2 2v-2l-1-1 2-2h0z" class="F"></path><path d="M304 471l1 2c0 1 0 2-1 4h0l-1-1 1-1v-2-1-1zm-7-4c1 1 2 1 3 2-1 0-2-1-3-1 1 1 1 2 2 2l1 1v2c-1 0-1 1-2 0-1 0-2-2-2-2h1c0-2 0-2-1-4h1zm-13-20c1 0 3 1 4 2v2h-2v1 2c0 1 1 4 2 5 2 3 3 5 3 8-1-1-3-3-4-3-2-1-5-5-6-7s-1-3-2-5h2 2v-2l-1-1 2-2h0z" class="H"></path><path d="M282 453c1-1 3-1 4-1v2h-1c-2 0-2-1-3-1z" class="O"></path><path d="M284 447c1 0 3 1 4 2v2h-2v1c-1 0-3 0-4 1l-1-1h2v-2l-1-1 2-2h0z" class="AJ"></path><path d="M284 447c1 0 3 1 4 2v2h-2s-1-1-1-2-1-1-1-2h0z" class="b"></path><path d="M283 427c1 0 2-1 2-1h0v1c0 1 1 1 2 2l2 2c3 0 5 2 8 0 1 0 2-2 3-3v1 2c1 0 3 1 4 0l1 2c0 1-1 1-1 2-1 2-4 3-5 6h-1c-3 2-9 4-10 8h0c-1-1-3-2-4-2h0l-2 2c-1 1-2 2-3 2h-2-2c-1 0-3-1-4-2v-1c2 1 4 1 6 1l4-5h0v-1l1-1c-1-1-1-2-1-4l-1-1h0c0-2-1-2-2-3-1 1-2 1-2 1l-1-1v-1c2 0 3 0 4-1h1v1h1 5 0l1-1h1c-1-1-3-3-4-3-1-1-2-1-2-1v-2l1 1z" class="U"></path><path d="M300 435l2-1c0 1 0 2-1 3l-1 1v-1-2z" class="F"></path><path d="M287 432h1l3 1v1c-2 0-4 0-5-1h0l1-1z" class="b"></path><path d="M283 427c1 0 2-1 2-1h0v1c0 1 1 1 2 2l2 2c3 0 5 2 8 0l1 1c-1 0-2 2-3 2s-3-1-4-1l-3-1c-1-1-3-3-4-3-1-1-2-1-2-1v-2l1 1z" class="M"></path><path d="M279 432h1v1h1v1l1 1c5 0 9 2 14 1 2-1 3-1 4-1v2c-2 1-3 2-5 2h-1c0-1-3 0-4 0-2 0-5-1-7-2h0c-1-1-3-2-3-4-1 0 0 0-1-1h0z" class="B"></path><path d="M279 432h0c1 1 0 1 1 1 0 2 2 3 3 4h0c2 1 5 2 7 2 1 0 4-1 4 0h1c-5 1-11 2-14 5h0v-1l1-1c-1-1-1-2-1-4l-1-1h0c0-2-1-2-2-3-1 1-2 1-2 1l-1-1v-1c2 0 3 0 4-1z" class="U"></path><path d="M300 437v1l-3 2 1 1c-3 2-9 4-10 8h0c-1-1-3-2-4-2h0l-2 2c-1 1-2 2-3 2h-2-2c-1 0-3-1-4-2v-1c2 1 4 1 6 1l4-5c3-3 9-4 14-5 2 0 3-1 5-2z" class="G"></path><path d="M297 440l1 1c-3 2-9 4-10 8h0c-1-1-3-2-4-2 1-3 7-4 9-5 1 0 3-1 4-2z" class="AQ"></path><defs><linearGradient id="BJ" x1="308.559" y1="451.207" x2="289.045" y2="453.048" xlink:href="#B"><stop offset="0" stop-color="#302e2f"></stop><stop offset="1" stop-color="#4f4b49"></stop></linearGradient></defs><path fill="url(#BJ)" d="M304 435l2 1h0-1c0 1 0 1 1 2v1h0v1 1l2 2c0 1 0 1 1 2h1 1l1 1c-1 0-2-1-3-1v3h1-1c0 1 1 2 0 3v1s-1 0-1 1c-1 2-2 3-3 5v1c-1 0-1 1-1 1v1h0l-1 1c0 1 0 1-1 2-1-1-2-2-2-3l-1-1h-1s-1 1-2 1v-1c-1 1-1 3 0 5 1 0 1 0 1 1l-1 1h1-1c1 2 1 2 1 4h-1l-1-2c0-3-1-5-2-7v-1l-2-2h-1v-1l-3-3h0l2-2c0-1 0-2-1-2v-2h0c1-4 7-6 10-8h1c1-3 4-4 5-6z"></path><path d="M292 457h1l1 1h-2v-1z" class="H"></path><path d="M295 469h1v-1-1h0c1 2 1 2 1 4h-1l-1-2z" class="U"></path><path d="M287 455h1c1 0 1-1 1-2h1c1 2 0 3 0 5l-3-3h0z" class="B"></path><path d="M298 460v-1l1-1c1-2 2-2 3-3h1v1l-4 4h-1z" class="D"></path><path d="M299 460l4-4v1l1 1c0 1-1 3-1 4s0 1-1 2c-1-1-2-2-2-3l-1-1z" class="U"></path><path d="M299 460l4-4v1l-3 4-1-1z" class="B"></path><path d="M299 441l1-1c1 1 1 0 2 1 0 2-1 3-1 5-2 2-6 3-8 4l-2 2h-1c-1 1 0 0 0 1h-1c0-1 0-2-1-2v-2h0c1-4 7-6 10-8h1z" class="D"></path><path d="M248 416h1v-2c-1-1 0-2-1-3v-1-1-1c0-1 1-1 1-2v-1h0v-1l1-1c1-2 2-4 3-5s1-1 1-2l1-1h1v2c-1 2 0 5 0 7v3c0 2 2 3 2 5 0 0 1 1 1 2v2l1 1c0 1 1 2 2 3 0 0 1 0 2-1l1 1h2l1-1h1 5l1-1c1 0 2-1 3-2h3c0 1 1 1 1 2h1c0 1 0 2 1 2-1 1-1 2-2 2v2c0 1 1 2 1 3l-1-1v2s1 0 2 1c1 0 3 2 4 3h-1c-2-1-4-1-5-3-1-1-3-1-4-1-5-1-9-3-15-3 2 0 2 0 2-1l-4-3v1 2c-1 0-2-1-3-1h0l-1 5v-1l-1-1h0v1l-1-1h0l-1-3-1-1s-1-1-2-1h0c0 1 1 3 0 4l-1-3-2-6z" class="I"></path><path d="M275 422h-2v-1h4 3l-2 1h0-3z" class="E"></path><path d="M277 419h4l1 1-1 1h-1-3v-2z" class="H"></path><path d="M251 403s0 1 1 1h1v1h-1c-1 0-1 1-1 1 0 1-1 1-1 2v1h-1c0-2 1-5 2-6z" class="Z"></path><path d="M250 409v-1c0-1 1-1 1-2 0 0 0-1 1-1h1c0 1 0 2-1 4v1c-1 0-2-1-2-1z" class="d"></path><path d="M278 416h3c0 1 1 1 1 2h1c0 1 0 2 1 2-1 1-1 2-2 2h-2l1-1 1-1-1-1h-4-3 0l1-1c1 0 2-1 3-2z" class="C"></path><path d="M282 418h1c0 1 0 2 1 2-1 1-1 2-2 2h-2l1-1 1-1-1-1h-2c1-1 2-1 3-1z" class="P"></path><path d="M252 401c1-2 2-3 3-4 0 3-1 8 0 11l-1 2h-1-1v-1c1-2 1-3 1-4v-1h-1c-1 0-1-1-1-1l1-2z" class="R"></path><path d="M252 401c1 0 1 1 2 2l-1 1h-1c-1 0-1-1-1-1l1-2z" class="M"></path><path d="M252 420c-2-4-3-7-3-11h1s1 1 2 1h1c1 1 1 4 1 5v1 3l-2 1h0z" class="P"></path><path d="M252 410h1c1 1 1 4 1 5v1l-2-3c-1-1-1-2 0-3z" class="N"></path><path d="M280 421h1l-1 1h2v2c0 1 1 2 1 3l-1-1v2s1 0 2 1c1 0 3 2 4 3h-1c-2-1-4-1-5-3-1-1-3-1-4-1-5-1-9-3-15-3 2 0 2 0 2-1l8 1v-1h-1c0-2 3 0 4-2h-1 3 0l2-1z" class="C"></path><path d="M275 422h3c1 1 2 1 3 2v1h-2 0l2 2c-2 0-6-2-8-2h0v-1h-1c0-2 3 0 4-2h-1z" class="B"></path><path d="M254 410l1-2c2 2 3 4 3 7l3 6v1 2c-1 0-2-1-3-1h0l-1 5v-1l-1-1h0v1l-1-1h0l-1-3-1-1s-1-1-2-1l1-1h0l2-1v-3-1c0-1 0-4-1-5h1z" class="F"></path><path d="M252 420h0l2-1 1 2c1 2 1 3 0 5l-1-3-1-1s-1-1-2-1l1-1z" class="O"></path><path d="M258 423c0-2-1-6 0-8l3 6v1 2c-1 0-2-1-3-1h0z" class="Aa"></path><path d="M258 423v-2l1-1 2 2v2c-1 0-2-1-3-1z" class="p"></path><path d="M253 410h1l1 2c1 1 1 6 1 8 0 0 0 1-1 1l-1-2v-3-1c0-1 0-4-1-5z" class="D"></path><path d="M251 421c1 0 2 1 2 1l1 1 1 3h0l1 1v-1h0l1 1v1 2c0 1 0 1 1 2l-1 2c-1 8-2 14-1 21 0 4 0 8 2 12 1 0 2 1 2 2 1 1 1 2 2 3l2 4-1 1 2 2c4 4 9 9 11 13l3 4s1 0 1 1v-1c-1-1-1-2-1-3v-1l1 2 3 5 1 1c0 1 0 2-1 2l1 1h1l1-1v1s1 0 1 1c1 0 1 0 2 1v1h-1l-1 1h-1c0 1-1 2-1 2v3h2c-1 1-2 0-3 0v-1h0v-2c-2 1-4 3-6 3-1 1-3 2-4 2 0 1-2 0-2 0l1-1c-1 0-1 1-2 1l-1-1c-2 0-3 0-4-1s-2-2-3-2h-1c-1-1-2-1-4-1-1 0-1 0-2-1-2 1-4 1-6 1-1 0-2 0-4-1-1 0-1 1-1 2-1-1-1-2-2-3 0-1 0-1 1-2l-1-2 1-1h-1v-6l-1-9c0-1-1-2 0-3 0-1-1-2-1-3l-1-2h-1v-2h-1l-1-5h-1l-1-1c-1-1 0-1 0-2l1-1h0v-1 1l3-2c0-1 0-2-1-3v-1c1-1 1-2 2-2h1c0-1 0-2 1-2v-1-1c0-1 1-2 1-2v-1l1-1v-3h1c0-1 0-1-1-2 0 1 0 1-1 2 0 1 0 1-1 2v-1-1l1-1h0v-1l1-1h0v-1l1-1h0v-1l1-1c0-1 2-4 4-5v-1-1-1-1h0l-1-1v-1-1-1-4-3h1l1 3c1-1 0-3 0-4h0z" class="J"></path><path d="M243 458c1-2 2-4 3-5 0 2 0 5-1 7h0l-2-2z" class="D"></path><path d="M262 493h3v1l1-1h0 1c1-1 2-1 3 0h0c1 0 1 0 2 1h0c1 1 2 1 2 2l-1-1-1-1c-2-1-3-1-5-1v1l1 1h0v1c-1 0-2 1-2 1-1 0-1-1-2 0l-2-1h5v-1c-1-1 0-1-1-1-2 0-3 0-4-1z" class="W"></path><path d="M268 495l-1-1v-1c2 0 3 0 5 1l1 1 1 1c1 1 2 1 2 2h0v1c-1 0-2-2-3-2-1-1-3-2-5-2z" class="I"></path><path d="M243 458l2 2c-2 3-4 4-6 6 0-1 0-2-1-3v-1c1-1 1-2 2-2v3h0c2-1 2-3 3-5z" class="R"></path><path d="M262 493c1 1 2 1 4 1 1 0 0 0 1 1v1h-5c-2 0-3-1-5-2h1l4-1z" class="I"></path><defs><linearGradient id="BK" x1="262.867" y1="474.762" x2="255.8" y2="469.919" xlink:href="#B"><stop offset="0" stop-color="#9d9383"></stop><stop offset="1" stop-color="#c0b6a9"></stop></linearGradient></defs><path fill="url(#BK)" d="M252 465h0l1 3h2v-1c2 2 4 4 7 5l2 4-1 1c-3-3-7-6-11-8v-4z"></path><path d="M245 460h0c0 2-5 6-5 7 1 0 0 1 0 1v1h1 0v-1c1 0 3-1 3-2h0c1-1 1-2 2-3h0c1 0 1-1 2-2v-1-1c1-1 1-2 1-3 0 2-1 6-2 8v2c0 1 0 2-1 3v-1h-1 0c-1 1-1 1-1 2l-3 2h0l-1 1c-1 0-2 0-3-1h-1l-1-1c-1-1 0-1 0-2l1-1h0v-1 1l3-2c2-2 4-3 6-6h0z" class="W"></path><path d="M236 468l1 1h2c1 0 1 1 2 2v1h0l-1 1c-1 0-2 0-3-1h-1l-1-1c-1-1 0-1 0-2l1-1z" class="G"></path><path d="M244 470c0-1 0-1 1-2h0 1v1 4c-1 1-1 2-1 4 0 3 3 7 3 10 0 1-1 4 0 5l1-1c-2 4-2 7-3 11h-1-1-1v-6l-1-9c0-1-1-2 0-3 0-1-1-2-1-3l-1-2h-1v-2h-1l-1-5c1 1 2 1 3 1l1-1h0l3-2z" class="a"></path><path d="M245 501l1-1c0-2 0-5 1-7 0-2 0-4 1-6 0 1-1 4 0 5l1-1c-2 4-2 7-3 11h-1v-1z" class="G"></path><path d="M243 496c0-2 0-3 1-5 0 3 1 7 1 10v1h-1-1v-6z" class="M"></path><path d="M242 480c1 1 1 2 1 2l2 4c1 1 1 3 2 5v1c-1-1-2-2-2-3 0-2-2-3-2-4 0 2 1 4 1 6-1 2-1 3-1 5l-1-9c0-1-1-2 0-3 0-1-1-2-1-3 1 0 1 0 1-1z" class="c"></path><path d="M244 470c0 2 0 3-1 5l-1 1v4c0 1 0 1-1 1l-1-2h-1v-2h-1l-1-5c1 1 2 1 3 1l1-1h0l3-2z" class="D"></path><path d="M237 472c1 1 2 1 3 1l1-1v1 1 2 1h0c0 1 0 1-1 2h-1v-2h-1l-1-5z" class="E"></path><path d="M237 472c1 1 2 1 3 1-1 1-1 3-1 4h-1l-1-5z" class="a"></path><path d="M251 421c1 0 2 1 2 1l1 1 1 3h0l1 1v-1h0l1 1v1 2c0 1 0 1 1 2l-1 2c-1 8-2 14-1 21 0 4 0 8 2 12 1 0 2 1 2 2 1 1 1 2 2 3-3-1-5-3-7-5v1h-2l-1-3h0v-18l-1-12-1-3v-3h-1v-4-3h1l1 3c1-1 0-3 0-4h0z" class="H"></path><path d="M256 431l1-1c0 1 0 1 1 2l-1 2h-1v-3z" class="C"></path><path d="M252 427v-4-1l1 2v2l-1 1z" class="R"></path><path d="M250 432l1-1h1v3h0l-1 1h0l-1-3z" class="AQ"></path><path d="M253 426l2 5c0 1 0 1-1 2l-1-1c0-1 0-3-1-5l1-1z" class="O"></path><path d="M251 435h0l1-1h0l1 10h0c-1 0-1 2-1 3l-1-12z" class="y"></path><path d="M253 424l1-1 1 3h0l1 1v-1h0l1 1v1 2l-1 1-1 1v-1l-2-5v-2z" class="N"></path><path d="M249 425v-3h1l1 3v1c0 1 1 3 1 5h-1l-1 1v-3h-1v-4z" class="x"></path><path d="M249 425v-3h1l1 3v1h-1v3h-1v-4z" class="O"></path><path d="M252 447c0-1 0-3 1-3h0l1 19c0 1 0 3 1 4v1h-2l-1-3h0v-18z" class="AM"></path><path d="M252 465c0-2 0-4 1-5 0 1 1 2 1 3s0 3 1 4v1h-2l-1-3z" class="n"></path><path d="M249 491c2-1 3-3 5-4l1 1c-1 0-1 0-2 1l-3 3v1h1v-1l1-1c1 0 1 0 2 1v1h5-2v1c2 1 3 2 5 2l2 1c1-1 1 0 2 0 0 0 1-1 2-1v-1h0c2 0 4 1 5 2 1 0 2 2 3 2 2 1 4 3 5 4h1c0-2-2-4-3-6-1-1-2-2-3-4v-1l3 4s1 0 1 1v-1c-1-1-1-2-1-3v-1l1 2 3 5 1 1c0 1 0 2-1 2l1 1h1l1-1v1s1 0 1 1c1 0 1 0 2 1v1h-1l-1 1h-1c0 1-1 2-1 2v3h2c-1 1-2 0-3 0v-1h0v-2c-2 1-4 3-6 3-1 1-3 2-4 2 0 1-2 0-2 0l1-1c-1 0-1 1-2 1l-1-1c-2 0-3 0-4-1s-2-2-3-2h-1c-1-1-2-1-4-1-1 0-1 0-2-1-2 1-4 1-6 1-1 0-2 0-4-1-1 0-1 1-1 2-1-1-1-2-2-3 0-1 0-1 1-2l-1-2 1-1h1 1c1-4 1-7 3-11z" class="B"></path><path d="M279 492l1 2 3 5 1 1c0 1 0 2-1 2l-4-6s1 0 1 1v-1c-1-1-1-2-1-3v-1z" class="O"></path><path d="M273 513c2-2 5-4 7-6 2-1 3-2 4-2h2v-1l2 1v1c-2 0-3 2-4 3-2 1-4 3-6 3-1 1-3 2-4 2 0 1-2 0-2 0l1-1z" class="W"></path><path d="M244 502h1 1l1 1c1 1 3 1 4 2 2 0 3 0 4-1 2-1 4-1 7 0l4 4c-1-1-1 0-2-1h0c-1 0-3-2-4-2l-1 1-3 2c-2 1-4 1-6 1-1 0-2 0-4-1-1 0-1 1-1 2-1-1-1-2-2-3 0-1 0-1 1-2l-1-2 1-1z" class="J"></path><path d="M247 503c1 1 3 1 4 2h-3c0 1 0 1-1 0v-2z" class="I"></path><path d="M243 507c0-1 0-1 1-2 1 1 1 2 2 3-1 0-1 1-1 2-1-1-1-2-2-3z" class="a"></path><path d="M316 328l1-1c1 3 1 5 2 8 0 1 0 2 1 3l2-2 2-3c1-1 1-2 2-2l2-2c0 1 1 3 1 3l-1 1h2c2 1 3 1 5 3h0c1 1 1 2 1 3 2 6 2 11 2 16 0 6 0 11-1 16 0 2-1 3-2 5v1c0 2-1 4-1 5v1c1 0 1 0 1 1s1 2 1 3c1 0 2 0 3-1l5 2 4 4-2 1v1h0v2c0 1 1 1 1 2h1l1 1c1-1 1-2 1-3l1 2c2 2 3 4 4 6h-2l2 4 1 7v2l1 3v6 4c-1 6-2 13-5 18-1 2-2 5-4 6v2 11c2-2 5-3 9-4-3 2-5 4-8 4h-1c-1 1-1 1 0 2h11c-3 0-10 0-12 1v1 30c0 5 0 11 1 16v1 1 2h1c1 1 2 2 4 3-1 1-2 1-3 1 0-1-1-1-2-2-1 2 0 3 0 4v22l-1 24c0 3 0 5 1 8-1 7-1 16 2 22 2 5 4 8 8 12l-2-1v2c-1-2-3-4-4-5v1h-1-1l-3-3c0-1-1-2-1-3l-1 1-2-1v-1c-1-1-1-3-1-4v-2c-1 0-2 1-3 1h0l-2 2-2 3c-1 0-1 0-1-1l-2 2v-2l-6 6c-1 1-2 1-3 2-1 0-2 1-2 2h0c-1 0-1 0-1-1-1 1-1 0-1 1-2 2-4 1-5 2l-1-1v-1h-2-1c0-1-1-1-1-1h-1l-2 1-1 1-9 6c-1 1-1 1-2 1l-1 1-14 4-13 3c-2 1-5 1-7 1 1-1 1-1 2-1 2-1 3-4 5-6s5-4 7-6h0 1c2-2 2 0 4 0l-1-1c1-1 2-2 3-2l5-4h0l1-1c1-1 2-1 3-2l3-3v-1l1-1c1 0 2-1 3-2 2 0 3-2 5-3l2-2 1-1h2l2-2c3-1 5-3 9-3 0-2 1-5 1-7v-7c1-2 0-5 0-8v-13-47-90-45c1-4 1-8 1-12 0-12 0-22-3-33z" class="AH"></path><path d="M316 606c0 2 0 2-2 4-1 0-2 1-3 2v1h2c1 1 1 0 0 1h-2-1c0-1-1-1-1-1h-1c3-3 5-4 8-7zm5-257c1 3 0 8 1 11 0 1 1 2 1 3h1c-1 0-1 1-1 1h-1c-1 2 0 3-1 4v-19z" class="AE"></path><path d="M321 437l2-2-1 7c0 1 0 1 1 1v1c0 1 0 2 1 2 0 2-1 3-1 4h0c-1 0-1 0-2-1v-4-8z" class="AI"></path><path d="M321 414c1 2 2 3 2 5v1-1c1 0 1 1 0 2h1 0c1 1 1 1 2 1-1 2-2 3-2 5h0l-1 1h-1 0l-1-1v-13z" class="AD"></path><path d="M324 421c1 1 1 1 2 1-1 2-2 3-2 5-1 0-1 0-1-1 0-2 0-3 1-5z" class="f"></path><path d="M323 435h1l2 2 1 3-3 6c-1 0-1-1-1-2v-1c-1 0-1 0-1-1l1-7z" class="j"></path><path d="M321 489h1c0 1 1 2 1 2h1v1c2 2 2 6 2 9 1 0 1 0 0 1v1l3 6 1 1-1 1c-2-2-3-5-4-7h-3c0 3 0 7-1 10v-25z" class="AP"></path><path d="M322 504v-5h0c1 1 1 1 1 2 1 1 1 2 2 3h-3z" class="j"></path><path d="M324 492c2 2 2 6 2 9 1 0 1 0 0 1v1c-1-1-1-3-2-4-1-2-1-3-1-4 0-2 0-2 1-3z" class="AD"></path><path d="M321 449c1 1 1 1 2 1h0 1v4 8l-1 3v11l1 7v4 4h-1s-1-1-1-2h-1v-39-1z" class="AE"></path><path d="M321 449c1 1 1 1 2 1h0 1v4h-2c-1-1-1-3-1-4v-1zm3 38c-1-1-1-2-2-3v-1-8c0-2-1-4 0-5h0v7h1v-8-4 11l1 7v4z" class="AS"></path><path d="M326 387h1v2c1 1 1 1 3 1-1 2-3 3-3 5l-1 1c0 3-1 5 0 8 1 2 1 3 2 5 0 1 0 1-1 2 0 1 0 2-1 3 0 1-1 2-2 4v1 2h-1c1-1 1-2 0-2v1-1c0-2-1-3-2-5v-15c1-2 1-5 1-6s0-2 1-2 1 0 1-1l1-2 1-1z" class="f"></path><g class="AN"><path d="M326 387h1v2c1 1 1 1 3 1-1 2-3 3-3 5l-1 1v-5-1-3z"></path><path d="M328 362c0-1 0-2 1-3v1l1 1-3 6v4 11l1 7h0c1 0 2 0 2-1h0l2-2-2 4c-2 0-2 0-3-1v-2h-1l-1 1-1 2c0 1 0 1-1 1s-1 1-1 2 0 4-1 6v-31c1-1 0-2 1-4h1v1h1 1 1c1-1 1-2 2-3z"></path></g><path d="M322 387c1 0 2 1 3 1l-1 2c-1 0-1-1-2-2v-1z" class="AD"></path><path d="M323 380h2c1 2 1 5 1 7l-1 1c-1 0-2-1-3-1l1-7z" class="g"></path><path d="M323 365h1l2 2c0 1 0 3-1 4 0 1-1 3-2 4-1-1-1-2-1-3 0-3 0-5 1-7z" class="S"></path><path d="M327 371v11l1 7h0c1 0 2 0 2-1h0l2-2-2 4c-2 0-2 0-3-1v-2h-1c0-2 0-5-1-7h-2c1-2 1-3 2-4 0-1 1-2 1-3 1 0 1-1 1-2z" class="AD"></path><path d="M325 376v4h-2c1-2 1-3 2-4z" class="S"></path><path d="M328 409l2 1 1-1c0 2 1 7 0 8v1h0c1 1 1 1 2 1 0 0 1 0 2-1h2c1 1 1 3 1 4v10h0c-1 1 0 1-1 2l-5 3-2 1 1 1h0v2l-2-2-1 1h-1l-1-3-2-2h-1l-2 2v-10l1 1h0 1l1-1h0c0-2 1-3 2-5-1 0-1 0-2-1h0v-2-1c1-2 2-3 2-4 1-1 1-2 1-3 1-1 1-1 1-2z" class="AE"></path><path d="M325 429h1l1 1c0 1-1 2-1 3l-1-1v-3z" class="j"></path><path d="M326 437h2c0 1 1 1 1 2l-1 1h-1l-1-3z" class="AS"></path><path d="M324 435l1-1c1 0 1 0 2 1l1 2h-2l-2-2z" class="Ab"></path><path d="M327 423v-3h1l-1 10-1-1c0-2 0-4 1-6z" class="f"></path><path d="M326 422l1 1c-1 2-1 4-1 6h-1v2h-1 1l-1-1v-3h0c0-2 1-3 2-5z" class="Ab"></path><path d="M328 409l2 1 1-1c0 2 1 7 0 8v-2c0-1-1-1-1-1h0c-2 2-1 4-2 6h-1v3l-1-1c-1 0-1 0-2-1h0v-2-1c1-2 2-3 2-4 1-1 1-2 1-3 1-1 1-1 1-2z" class="AN"></path><path d="M335 418h2c1 1 1 3 1 4v10h0c-1 1 0 1-1 2l-5 3-2 1h0c0-2-1-3-2-5 1-4 2-8 4-13 0 0 0-1 1-1 0 0 1 0 2-1z" class="X"></path><g class="S"><path d="M335 418h2c1 1 1 3 1 4v10h0l-1-1c0-2 0-4-1-5s-2-2-2-3-1-2-1-2l-1-1s0-1 1-1c0 0 1 0 2-1z"></path><path d="M334 383c1 0 1 0 1 1s1 2 1 3c1 0 2 0 3-1l5 2 4 4-2 1v1h0v2c0 1 1 1 1 2-2 0-4 0-6 1-1 2-1 3-1 5l-1 1h-1v13 4c0-1 0-3-1-4h-2c-1 1-2 1-2 1-1 0-1 0-2-1h0v-1c1-1 0-6 0-8l-1 1-2-1c-1-2-1-3-2-5-1-3 0-5 0-8l1-1c0-2 2-3 3-5l2-4c1-1 1-2 2-3z"></path></g><path d="M330 395l2 1v1h-1-1l-1-1 1-1z" class="j"></path><path d="M328 395c0-1 1-2 2-2v2h0l-1 1-2 1 1-2z" class="g"></path><path d="M330 393c1-2 3-4 5-6 0 1-1 2-1 2 1 2 1 1 3 1-3 1-5 2-7 5v-2z" class="K"></path><path d="M334 383c1 0 1 0 1 1s1 2 1 3h-1c-2 2-4 4-5 6-1 0-2 1-2 2h-1c0-2 2-3 3-5l2-4c1-1 1-2 2-3z" class="AV"></path><path d="M326 396l1-1h1l-1 2v6c1 2 2 3 4 4 0 0 2 0 2-1 2 0 3-2 4-3l1-4h0v6 13 4c0-1 0-3-1-4h-2l-2-1h5l-1-12h0c-2 2-4 3-6 4l-1 1-2-1c-1-2-1-3-2-5-1-3 0-5 0-8z" class="AP"></path><path d="M338 399c0-2-1-6 0-8l8 5c0 1 1 1 1 2-2 0-4 0-6 1-1 2-1 3-1 5l-1 1h-1v-6z" class="Ah"></path><path d="M336 387c1 0 2 0 3-1l5 2 4 4-2 1v1h0v2l-8-5-1-1c-2 0-2 1-3-1 0 0 1-1 1-2h1z" class="T"></path><path d="M331 409c2-1 4-2 6-4h0l1 12h-5l2 1c-1 1-2 1-2 1-1 0-1 0-2-1h0v-1c1-1 0-6 0-8z" class="S"></path><path d="M331 418c1-1 1-1 2-1l2 1c-1 1-2 1-2 1-1 0-1 0-2-1z" class="Ab"></path><defs><linearGradient id="BL" x1="338.664" y1="528.187" x2="320.563" y2="526.963" xlink:href="#B"><stop offset="0" stop-color="#751413"></stop><stop offset="1" stop-color="#9f1b1d"></stop></linearGradient></defs><path fill="url(#BL)" d="M325 504c1 2 2 5 4 7l1-1 4 6 1 4-1 1c1 2 2 5 3 7 0 4 1 7 1 10 1 3 1 5 1 7-1 1-1 2-1 3-2 2-3 4-5 4h-4c0-1 0-1-1-1-1-2-3-3-5-4v-1c0-1-1-1-1-2-1-3-1-6-1-8v-22c1-3 1-7 1-10h3z"></path><path d="M327 533c0-1 1-1 2-2v2h1l-2 2c-1-1-1-1-1-2z" class="AI"></path><path d="M328 535c-1 1-2 2-2 4h-1v-1c0-2 1-3 2-5 0 1 0 1 1 2z" class="AN"></path><path d="M329 511l1-1 4 6 1 4-1 1c-1-1-1-2-2-3-1-2-2-5-3-7z" class="AT"></path><path d="M329 531h-1c-1-1-2-2-2-3s0-1 1-2c1 0 2 0 3 1s1 1 1 2v1l-2 1z" class="X"></path><path d="M332 518c1 1 1 2 2 3 1 2 2 5 3 7h-1l-2 1-1-1c0-3-1-6-1-10z" class="AE"></path><path d="M333 528l1 1 2-1h1c0 4 1 7 1 10-1-1-1-2-2-4v-1c-2 0-3-1-5 0h-1-1v-2h0l2-1v-1c1 0 1 0 2-1z" class="AI"></path><path d="M331 530c2 0 3 1 5 2v1c-2 0-3-1-5 0h-1-1v-2h0l2-1z" class="AE"></path><path d="M331 533c2-1 3 0 5 0v1c1 2 1 3 2 4 1 3 1 5 1 7-1-1-1-1-2-1-2 0-3 1-4 2h-1c1-1 0-2 0-3v-6s1-1 1-2c-1-1-1-2-2-2z" class="f"></path><path d="M334 538h1v1l-1 1h-1c0-1 1-2 1-2z" class="j"></path><path d="M308 593c3-1 5-3 9-3-1 4-2 7-4 10 0 1-1 3-1 3-3 5-6 9-12 12-2 2-5 3-8 4-9 4-18 8-28 10-1 1-2 1-3 1 2-1 3-4 5-6s5-4 7-6h0 1c2-2 2 0 4 0l-1-1c1-1 2-2 3-2l5-4h0l1-1c1-1 2-1 3-2l3-3v-1l1-1c1 0 2-1 3-2 2 0 3-2 5-3l2-2 1-1h2l2-2z" class="G"></path><path d="M285 611h0l2 1c-1 1-2 1-3 2-1 2-4 3-6 4l-1-1c1-1 2-2 3-2l5-4z" class="D"></path><path d="M305 603v2l-11 4c-2 1-5 2-7 3l-2-1 1-1c1-1 2-1 3-2 1 1 2 0 4 1 1-1 2-1 4-2 0 0 1-1 2-1l6-3z" class="Z"></path><path d="M308 593c3-1 5-3 9-3-1 4-2 7-4 10 0 1-1 3-1 3-2-1-5 1-7 2v-2l-6 3c-1 0-2 1-2 1-2 1-3 1-4 2-2-1-3 0-4-1l3-3v-1l1-1c1 0 2-1 3-2 2 0 3-2 5-3l2-2 1-1h2l2-2z" class="W"></path><path d="M292 605h1v3c1 0 0 0 1-1h3c-2 1-3 1-4 2-2-1-3 0-4-1l3-3z" class="B"></path><path d="M305 603h0c1-1 7-3 8-3 0 1-1 3-1 3-2-1-5 1-7 2v-2z" class="H"></path><path d="M322 544c0 1 1 1 1 2v1c2 1 4 2 5 4 1 0 1 0 1 1h4c2 0 3-2 5-4v1l1 2c-1 2-1 4-1 7v3l-1 1v6l-2 5v3l1-1h0l-3 6-4 8-4 6-1 1-2 3-1 1c-1 1-2 2-2 3h-1 0c-1 1-2 2-3 2 0-2 2-3 3-5s1-6 1-8l2-7v-7-19c0-5-1-11 1-15z" class="S"></path><path d="M325 563c1 1 2 1 2 2s-1 2 0 3l-1 1c-1-2-1-3-1-5h0v-1zm5-1c3 2 4 4 5 6v1l-1-1c-1-1-1-2-2-2 0-1-1-1-1-2v-1l-1-1z" class="j"></path><path d="M326 569l1-1c1 1 1 1 2 3 0 1 0 2 1 3 0 2-1 3-1 5h-1c-1-1-1-4-1-5 0-2-1-4-1-5z" class="AD"></path><path d="M335 573v3l1-1h0l-3 6-4 8-4 6-1 1-2 3-1 1c-1 1-2 2-2 3h-1 0c-1 1-2 2-3 2 0-2 2-3 3-5s1-6 1-8l2-7v-7h1v-3 5c1 1 2 2 3 4 0 1 1 3 1 5h1c4-5 6-10 8-16z" class="AE"></path><path d="M324 590c0 2 0 3-1 4 0 1-1 2-1 3h-2v-2-2l1 1h1c1-1 2-2 2-4z" class="AN"></path><path d="M322 575v5c1 1 2 2 3 4 0 1 1 3 1 5l-1-1-1 2c0 2-1 3-2 4h-1l-1-1v2c-1-1-1-2-1-3l2-7v-7h1v-3z" class="j"></path><path d="M324 590v-4-3l1 1c0 1 1 3 1 5l-1-1-1 2zm-3-5c0 3 0 6-1 8v2c-1-1-1-2-1-3l2-7z" class="AI"></path><path d="M322 544c0 1 1 1 1 2v1c2 1 4 2 5 4 1 0 1 0 1 1h4c2 0 3-2 5-4v1l1 2c-1 2-1 4-1 7v3l-1 1v6h-1-1c-1-2-2-4-5-6l1 1v1c-2-1-4-3-6-2h0v1 1h0c-1 1-2 1-3 2-1 2 0 6 0 9v3h-1v-19c0-5-1-11 1-15z" class="AN"></path><path d="M327 554l-5-1v-6l1 1v-1c2 1 4 2 5 4 1 0 1 0 1 1h0 0v2h-2z" class="f"></path><path d="M330 562c-1-1-4-2-6-2h-1v-2c2-1 4 0 6 1 2 0 3 1 5 2h2c1 0 1 0 1 1h0v6h-1-1c-1-2-2-4-5-6z" class="X"></path><path d="M329 552h4c2 0 3-2 5-4v1l1 2c-1 2-1 4-1 7v3l-1 1h0c0-1 0-1-1-1h-2c-2-1-3-2-5-2l3-1c1-1 2-1 2-2h-4 0c-1-1-2-1-3-2h2v-2h0 0z" class="S"></path><path d="M329 552h4c2 0 3-2 5-4v1c-1 1-2 3-3 4-1 0-2 1-3 2h-2v1c-1-1-2-1-3-2h2v-2h0 0z" class="AD"></path><defs><linearGradient id="BM" x1="331.69" y1="333.518" x2="328.049" y2="382.614" xlink:href="#B"><stop offset="0" stop-color="#360e10"></stop><stop offset="1" stop-color="#901819"></stop></linearGradient></defs><path fill="url(#BM)" d="M326 331l2-2c0 1 1 3 1 3l-1 1h2c2 1 3 1 5 3h0c1 1 1 2 1 3 2 6 2 11 2 16 0 6 0 11-1 16 0 2-1 3-2 5v1c0 2-1 4-1 5v1c-1 1-1 2-2 3l-2 2h0c0 1-1 1-2 1h0l-1-7v-11-4l3-6-1-1v-1c-1 1-1 2-1 3-1 1-1 2-2 3h-1-1-1v-1s0-1 1-1h-1c0-1-1-2-1-3-1-3 0-8-1-11v-6c0-2 0-3 1-4v-3l2-3c1-1 1-2 2-2z"></path><path d="M331 381c0-1-1-1-1-2 1-1 2-1 3-2 0 2-1 3-2 4z" class="S"></path><path d="M330 361h0v3c0 2 0 4-1 6v-2l-2-1 3-6z" class="g"></path><path d="M329 346c0-1 1-5 1-5h1c1 2 0 3 1 5v3h-1 0c0 2 0 5-1 7v-10h-1z" class="z"></path><path d="M323 338v1h1l2 2c1 1 1 1 2 1h1c0-1 0-2 1-2s1 0 1 1h-1l-1 5c0 1 0 2-1 3h0c-2-3-3-5-4-7-1-1-1-3-1-4z" class="l"></path><path d="M326 331l2-2c0 1 1 3 1 3l-1 1c-1 1-4 3-5 5 0 1 0 3 1 4l-2 1h-1c0-2 0-3 1-4v-3l2-3c1-1 1-2 2-2z" class="AI"></path><path d="M327 371v-4l2 1v2c1 1 3 4 2 5 0 2-2 2-2 3s0 2-1 3v1h-1v-11z" class="S"></path><path d="M333 377l2-1v1c0 2-1 4-1 5v1c-1 1-1 2-2 3l-2 2h0c0 1-1 1-2 1h0l-1-7h1v2-1l1 1c0-1 1-2 2-3s2-2 2-4z" class="f"></path><path d="M324 342c1 2 2 4 4 7h0c1-1 1-2 1-3h1v10 2c1 2 4 3 4 5-1-1-2-2-4-2h0 0l-1-1v-1c-1 1-1 2-1 3v-3c-1 0-1 0-1-1h0l1 1v-1s1 0 1-1c1-4-2-8-5-11-1-1-1-2-2-3l2-1z" class="S"></path><defs><linearGradient id="BN" x1="325.229" y1="345.494" x2="325.495" y2="363.887" xlink:href="#B"><stop offset="0" stop-color="#681414"></stop><stop offset="1" stop-color="#951b1c"></stop></linearGradient></defs><path fill="url(#BN)" d="M321 343h1c1 1 1 2 2 3 3 3 6 7 5 11 0 1-1 1-1 1v1l-1-1h0c0 1 0 1 1 1v3c-1 1-1 2-2 3h-1-1-1v-1s0-1 1-1h-1c0-1-1-2-1-3-1-3 0-8-1-11v-6z"></path><path d="M324 363l1 2h-1-1v-1s0-1 1-1z" class="Ab"></path><path d="M334 521l1-1c3 5 6 8 11 11v-1c1 0 1 0 0 1h0 1v-4h1v22l-1 24c0 3 0 5 1 8-1 7-1 16 2 22 2 5 4 8 8 12l-2-1v2c-1-2-3-4-4-5v1h-1-1l-3-3c0-1-1-2-1-3l-1 1-2-1v-1c-1-1-1-3-1-4v-2c-1 0-2 1-3 1h0l-2 2-2 3c-1 0-1 0-1-1l-2 2v-2l-6 6c-1 1-2 1-3 2-1 0-2 1-2 2h0c-1 0-1 0-1-1-1 1-1 0-1 1-2 2-4 1-5 2l-1-1v-1c1-1 1 0 0-1h-2v-1c1-1 2-2 3-2 2-2 2-2 2-4l2-2v-1h1c0-1 1-2 2-3l1-1 2-3 1-1 4-6 4-8 3-6h0l-1 1v-3l2-5v-6l1-1v-3c0-3 0-5 1-7l-1-2v-1c0-1 0-2 1-3 0-2 0-4-1-7 0-3-1-6-1-10-1-2-2-5-3-7z" class="Ah"></path><path d="M332 602c2-1 2-3 4-4l-2 6-2 2v-2-2z" class="AR"></path><path d="M338 580c0 6-1 12-2 18-2 1-2 3-4 4l1-4c1-2 3-6 3-9l2-9z" class="AS"></path><defs><linearGradient id="BO" x1="337.473" y1="589.043" x2="329.145" y2="564.66" xlink:href="#B"><stop offset="0" stop-color="#96191c"></stop><stop offset="1" stop-color="#cf392d"></stop></linearGradient></defs><path fill="url(#BO)" d="M337 562l1-1v-3c0-3 0-5 1-7l-1 29-2 9v-3h-1c-1 1-2 5-3 5h-1c-1 1-1 2-2 2h0v-4h0l4-8 3-6h0l-1 1v-3l2-5v-6z"></path><path d="M347 527h1v22h-7v-1c-1-5 0-11 0-16h4l1-1v-1c1 0 1 0 0 1h0 1v-4z" class="AD"></path><path d="M332 591c1 0 2-4 3-5h1v3c0 3-2 7-3 9l-1 4v2l-6 6c-1 1-2 1-3 2-1 0-2 1-2 2h0c-1 0-1 0-1-1-1 1-1 0-1 1-2 2-4 1-5 2l-1-1v-1c1-1 1 0 0-1h-2v-1c1-1 2-2 3-2 2-2 2-2 2-4l2-2v-1h1c0-1 1-2 2-3l1-1 2-3 1-1 4-6h0v4h0c1 0 1-1 2-2h1z" class="AN"></path><path d="M332 591c1 0 2-4 3-5h1v3c0 3-2 7-3 9-2 1-3 3-4 4 0-1 1-2 1-3 1-3 1-5 2-7v-1z" class="S"></path><path d="M318 604c0 1 0 1 1 2 2 0 4-1 5 0 1-1 1-2 2-3 0-1 1-3 2-3h1l-1 2 1 1-1 1c-1 1-1 2-1 3l-2 1v1l1 1c-1 1-2 1-3 2-1 0-2 1-2 2h0c-1 0-1 0-1-1-1 1-1 0-1 1-2 2-4 1-5 2l-1-1v-1c1-1 1 0 0-1h-2v-1c1-1 2-2 3-2 2-2 2-2 2-4l2-2z" class="Ab"></path><path d="M325 608h0c-1 0-1 0-2-1l1-1c1 0 1 0 3 1l-2 1z" class="AP"></path><path d="M314 610l1-1c1-1 2-1 4-2h1l1 1h1c-2 2-6 5-9 5h-1l-1-1c1-1 2-2 3-2z" class="AI"></path><path d="M341 549h7l-1 24c0 3 0 5 1 8-1 7-1 16 2 22 2 5 4 8 8 12l-2-1v2c-1-2-3-4-4-5v1h-1-1l-3-3c0-1-1-2-1-3l-1 1-2-1v-1c-1-1-1-3-1-4v-2c-1 0-2 1-3 1h0l-2 2c1-3 2-6 3-8 0-5 0-10 1-15 0-10-1-20 0-30z" class="j"></path><path d="M344 574h1v1 1 2h-1-1c0-1 0-2 1-4z" class="AN"></path><path d="M349 605l3 6v1h-1-1l-3-3c1-1 2-2 2-4z" class="K"></path><path d="M346 606c-2-5-2-9 0-13l3 12c0 2-1 3-2 4 0-1-1-2-1-3z" class="Y"></path><path d="M350 396l1 2c2 2 3 4 4 6h-2l2 4 1 7v2l1 3v6 4c-1 6-2 13-5 18-1 2-2 5-4 6v2 11c2-2 5-3 9-4-3 2-5 4-8 4h-1c-1 1-1 1 0 2h11c-3 0-10 0-12 1v1 30c0 5 0 11 1 16v1 1 2h1c1 1 2 2 4 3-1 1-2 1-3 1 0-1-1-1-2-2-1 2 0 3 0 4h-1v4h-1 0c1-1 1-1 0-1v1c-5-3-8-6-11-11l-1-4-4-6-1-1-3-6v-1c1-1 1-1 0-1 0-3 0-7-2-9v-1-4-4l-1-7v-11l1-3v-8-4h-1c0-1 1-2 1-4l3-6h1l1-1 2 2v-2h0l-1-1 2-1 5-3c1-1 0-1 1-2h0v-10-4-13h1l1-1c0-2 0-3 1-5 2-1 4-1 6-1h1l1 1c1-1 1-2 1-3z" class="AN"></path><path d="M348 519v2h1c1 1 2 2 4 3-1 1-2 1-3 1 0-1-1-1-2-2-1 2 0 3 0 4h-1l1-8z" class="l"></path><path d="M347 400c1 4 1 9 1 13v5 2h2c1 1 3 1 4 2h0l2-1 1-1v6 4-3h-1c-3 0-5 1-7 2l-1 1-1 3v-33z" class="e"></path><path d="M348 430v-8l6 1c-2 1-5 4-5 6l-1 1z" class="Q"></path><path d="M350 396l1 2c2 2 3 4 4 6h-2l2 4 1 7v2l1 3-1 1-2 1h0c-1-1-3-1-4-2h-2v-2-5c0-4 0-9-1-13 0-1 0-1 1-2l1 1c1-1 1-2 1-3z" class="i"></path><path d="M350 396l1 2c2 2 3 4 4 6h-2c-1-2-2-3-4-5 1-1 1-2 1-3z" class="AE"></path><path d="M355 408l1 7-1-1-3 3c0-1 2-3 3-4v-1h-1c-1 1-2 2-3 2l3-3h-1c-1 0-1 0-2 1v-1c0-1 1-1 1-2 1 0 2 0 2-1h1z" class="Y"></path><path d="M352 417l3-3 1 1v2l1 3-1 1-2 1h0c-1-1-3-1-4-2h-1l3-3zm-3 12c2-1 4-2 7-2h1v3c-1 6-2 13-5 18-1 2-2 5-4 6l-1-21 1-3 1-1z" class="T"></path><path d="M338 405h1l1-1v76 28 10l-3-5c0-1-1-2-1-3 2-7 2-13 2-19v-9-5-13-24-8-10-4-13z" class="Af"></path><path d="M338 482v9c0 6 0 12-2 19 0 1 1 2 1 3l3 5v2c3 4 5 6 6 10v1c-5-3-8-6-11-11l-1-4-4-6-1-1-3-6v-1c1-1 1-1 0-1 0-3 0-7-2-9v-1-4-4c3 0 8 2 12 1l2-2z" class="S"></path><path d="M329 509h2 1l2 4h0v3l-4-6-1-1zm7-7c1 3 0 5 0 8 0 1 1 2 1 3l3 5v2c-1-1-2-1-2-3h-1c-1-2-1-3-2-5 0-1-1-2-2-4l2-1c1-1 1-3 1-5z" class="AD"></path><path d="M338 491c0 6 0 12-2 19 0-3 1-5 0-8h-1c-1 1-2 3-3 4h0v-2c1-1 2-4 3-6v-3c0-1 0-1 2-2 0-1 0-2 1-2z" class="AN"></path><path d="M334 513v-1h1c1 2 1 3 2 5h1c0 2 1 2 2 3 3 4 5 6 6 10v1c-5-3-8-6-11-11l-1-4v-3z" class="Y"></path><path d="M338 432h0v8 24 13 5l-2 2c-4 1-9-1-12-1l-1-7v-11l1-3v-8-4h-1c0-1 1-2 1-4l3-6h1l1-1 2 2v-2h0l-1-1 2-1 5-3c1-1 0-1 1-2z" class="S"></path><path d="M336 438l1 1c0 1-1 1-1 1-1 1-2 1-3 1v-1c1 0 2-1 3-2z" class="X"></path><path d="M325 448c0 1 0 3-1 4h2v-1 5c-1 1-2 1-2 2v4-8-4l1-2z" class="AN"></path><path d="M329 439l2 2h0c-1 0-3 0-4 2-1 1-2 3-2 5l-1 2h-1c0-1 1-2 1-4l3-6h1l1-1z" class="AP"></path><path d="M338 432h0v8h-1v-2-1l-1 1c-1 1-2 2-3 2v1h-2 0v-2h0l-1-1 2-1 5-3c1-1 0-1 1-2z" class="j"></path><path d="M332 437c1 1 1 2 1 3v1h-2 0v-2h0l-1-1 2-1z" class="AN"></path><path d="M323 476l1-3h0c2 0 4-3 6-1 2 1 1 1 1 2 1 0 2 1 2 1 1 0 1 0 1-1 0-3 0-6-1-8l-2-6c-1-2-1-4-2-6v-1c2-1 2-2 3-3 1-2 0-3 2-4h2l1 1v3 1c-2 1 0-1-1 0v1s1 0 1 1c1 1 0 4 0 5v12c0 3 0 5 1 7v5l-2 2c-4 1-9-1-12-1l-1-7z" class="X"></path></svg>