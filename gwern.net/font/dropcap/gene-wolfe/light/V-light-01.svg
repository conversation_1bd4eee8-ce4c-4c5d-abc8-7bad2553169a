<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="60 40 628 632"><!--oldViewBox="0 0 744 752"--><style>.B{fill:#f6f6f6}.C{fill:#e11f1d}.D{fill:#d20606}.E{fill:#fefeff}.F{fill:#5c5a5d}.G{fill:#cfced0}.H{fill:#3a3a3d}.I{fill:#dd0407}.J{fill:#aca9ab}.K{fill:#da282a}.L{fill:#0a0a0b}.M{fill:#6e6b6e}</style><path d="M338 170c2 1 4 3 6 4v3l1 1c-1-1-3-2-5-3-1-1-3-2-5-4h2l1-1zm19 98c0-4-2-8-2-12-1-8-1-15 0-22 0-2 1-4 1-6 0-1 0-2 1-2 0 3-1 7-1 10-1 11 0 20 3 30l-2-1v3z" class="K"></path><path d="M344 174c7 7 12 15 13 25 1 4 1 7 2 10s4 5 6 7c1 2 2 3 2 4 0 0-1 0-1-1-3-4-7-7-8-11v-1c-1-2-2-5-2-8-1-4-2-9-4-12s-4-5-6-7l-1-2-1-1v-3z" class="C"></path><path d="M267 455h2v2c0 3 3 7 4 9l4 8c4 7 8 14 13 19 1 1 1 0 1 1l-6-6c-5-6-8-12-12-18-3-5-5-10-6-15z" class="D"></path><path d="M385 288h1c2-1 4-4 5-6-1 3-1 5-3 7 0 1-1 2-2 4-1 3-1 8 0 11v1c0 2 1 4 1 5 1 1 1 2 1 2v2c-3-4-5-9-5-14s-1-9 2-12z" class="G"></path><path d="M273 425v1c-2 6-3 12-3 19-1 4-1 8 1 12 1 3 3 5 4 8h0l-6-8v-2h-2c0-1-1-2-1-2v-1c1-2 1-5 1-7 1-6 1-15 6-20z" class="C"></path><path d="M334 149l1 3c1 3 1 7 1 10 0 2 0 5 1 6 0 1 1 2 1 2l-1 1h-2c-4 0-5 1-8 3l-1 11c-1-1-2-1-2-1v-8c1-6 6-7 10-10 1-1 1-3 1-5 0-4-1-8-2-12h1z" class="I"></path><path d="M312 535l1 1 3 12c1 4 3 8 5 12 4 8 9 16 15 22a57.31 57.31 0 0 0 11 11h0c-4-2-8-5-12-9s-8-9-13-12v-1h2 1l-6-12c-3-8-5-15-7-24z" class="C"></path><path d="M266 453c-4-2-8-7-11-9-1-2-4-7-6-7l-2-1h2 0l-1-2c-2 0-5-1-8-1 0 1-1 1-1 0h-1c-1 0-3 0-4-1h0-1v-1c1 1 3 1 4 1h8c3 0 4-1 6-2s3-2 4-4l6-6 3-3c0 1-1 2-2 3-4 4-8 9-10 14 2 8 8 13 14 18h0v1z" class="I"></path><defs><linearGradient id="A" x1="462.715" y1="228.684" x2="452.567" y2="223.418" xlink:href="#B"><stop offset="0" stop-color="#b4b2b4"></stop><stop offset="1" stop-color="#d5d4d5"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M439 217c4 2 9 6 13 5 1 0 2 0 3 1 1 0 3 0 4 1h2 0l2 1h0 2c1 2 1 10 0 12v-3h0v-2-2c-2-2-7-1-10-2h-3l-5-2c-1 0-2 0-3-1l-1-1-5-3c1 0 2 0 2 1l2 1h1 1l1 1h1 0l1 1h1 0 1c1 1 1 1 2 1h0 1l-1-1h1 2v-1h-2c-1 0-1 0-1-1h-1-1-1l-1-1h-1c-1 0-1-1-2-1l-1-1h-1c-1-1-2-1-3-2v-1z"></path><path d="M418 141c1 1 2 1 2 1-2 3-1 13 0 16v1c0 2 1 4 2 5l-1 2-3-6c-14 10-17 27-26 40 0-1 3-5 3-6l5-12c2-5 5-10 8-14 2-4 6-8 8-12s2-10 2-15z" class="D"></path><path d="M331 145h1l1 4c1 4 2 8 2 12 0 2 0 4-1 5-4 3-9 4-10 10h-1c1-1 1-3 1-4 2-3 4-5 5-8v-1c-1-1-1 0-2 0l1-12c1 0 1-1 1-1 1-1 0-2 1-3 0-1 1-1 1-2z" class="E"></path><path d="M331 145h1c-1 2-1 6-1 8 0 3-1 7-2 11v-1c-1-1-1 0-2 0l1-12c1 0 1-1 1-1 1-1 0-2 1-3 0-1 1-1 1-2z" class="K"></path><path d="M351 138l2 1h0c-2 1-5 1-8 2v2c-3 4-8 5-10 9l-1-3h-1l-1-4h-1v-5c4-1 8-1 13-1 2-1 4-1 6 0h1v-1z" class="H"></path><path d="M334 149l-1-4 12-4v2c-3 4-8 5-10 9l-1-3z" class="B"></path><path d="M275 465c3 3 5 6 7 8 10 11 19 22 31 30 1 1 2 2 4 2 0 2 1 3 1 4-5 7-6 15-5 23v4l-1-1c-1-9-2-18 0-27h1c-5-6-11-11-16-16-5-6-10-11-15-17-2-3-5-6-7-10h0z" class="D"></path><path d="M441 107c1-5 4-10 7-13s7-5 10-8c-1 1-4 2-5 1-3-1-6-3-8-4-8-6-14-12-19-20-1-1-1-2-1-3 1 2 2 4 4 6 5 7 12 12 20 16 3 1 7 3 10 3 7 0 13-1 20 0v1c-3 0-7 0-10 1-9 1-17 6-22 12l-3 6c-1 1-2 1-2 2h-1 0zM269 85c5 0 11 0 16 1 5 0 9 1 14 0 3-1 6-2 9-4 4-3 7-6 10-10 2-2 3-4 4-6l3-6c-1 4-3 8-5 11-4 7-11 13-17 18 3 5 8 8 12 11 8 6 15 16 18 25h-1c-1-2-1-3-2-4-4-8-9-15-15-20l-9-6c-3-3-6-6-10-7 1 2 4 3 5 4 8 6 12 12 14 21h0-1v-1c-3-3-4-7-7-10-6-7-18-14-28-15-3-1-7-1-10 0v-2z" class="I"></path><path d="M315 113c-2-9-6-15-14-21-1-1-4-2-5-4 4 1 7 4 10 7l9 6c6 5 11 12 15 20 1 1 1 2 2 4l2 4c-2 0-14-4-15-3l-4-13z" class="E"></path><path d="M411 136l10-4c0-1 1-4 1-5 4-14 11-25 22-35-14 4-27 12-34 24h0c1-3 3-6 6-9 6-6 15-12 23-14 1-1 2-1 3-1 3 0 6-3 9-3 1 0 0 0 0 1l-6 3c-2 2-4 3-5 5-6 6-11 14-14 22-1 3-2 7-3 10 4-2 10-4 15-5h2l5-1 4-1c6-1 11-3 16-5h0c-2 2-7 3-7 4 0 2 2 5 3 7h4c2 0 12 0 13 1-3 0-19-1-19 0h-1c-10-2-23 0-33 3-4 1-8 3-12 4-1 0-1 0-2-1z" class="F"></path><path d="M375 172l1-1v124 8h0c0-1 0-1-1-1v1-1c-1-2-1-5-2-7s-4-4-5-6c-6-7-12-13-17-19-3-4-5-9-7-13-8-14-14-29-16-45h1c2 8 3 16 6 23 3 9 7 17 12 24 2 3 5 8 8 10h1l1-1v-3l2 1c3 10 8 15 15 22 1-6 1-12 1-18v-36-42-20z" class="D"></path><path d="M275 96l-3-9h7c10 1 22 8 28 15 3 3 4 7 7 10v1h1 0l4 13-2-1h0c-1 0-4 0-5-1-5 0-9 0-14-1-4-1-9-3-13-5-3-2-6-5-9-8l1-1c0-1 1-1 2-2h0 1c-1-1-2-1-2-2l-1-1v-3l-2-5z" class="E"></path><path d="M275 96c1 2 3 3 4 4s1 3 3 4c1 0 2 1 3 2h-2-1c-2-1-4-3-5-5l-2-5z" class="B"></path><defs><linearGradient id="C" x1="288.059" y1="123.491" x2="306.083" y2="108.8" xlink:href="#B"><stop offset="0" stop-color="#c4c3c4"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#C)" d="M277 101c1 2 3 4 5 5h1 2c1 0 1 0 2 1h3c1 0 3 0 3 1 3 0 6 0 9 1s5 1 7 2 3 2 5 1v1h1 0l4 13-2-1h0c-1 0-4 0-5-1-5 0-9 0-14-1-4-1-9-3-13-5-3-2-6-5-9-8l1-1c0-1 1-1 2-2h0 1c-1-1-2-1-2-2l-1-1v-3z"></path><path d="M285 106c1 0 1 0 2 1 1 0 1 1 2 2 1 0 1 0 1 1h-1c-4-2-7-1-12-1 2-1 4-1 6-2v-1h2z" class="K"></path><path d="M293 108c3 0 6 0 9 1s5 1 7 2 3 2 5 1v1h1 0l4 13-2-1c0-3-1-6-3-8-4-5-14-8-20-8l-1-1z" class="C"></path><path d="M469 87c3-1 7-1 10-1 2 7 1 15-3 22-2 4-7 7-11 10-5 2-10 4-16 5l-4 1-5 1h-2c0-3 1-6 2-9 0-3 0-6 1-9h0 1c0-1 1-1 2-2l3-6c5-6 13-11 22-12z" class="B"></path><path d="M464 107l2 1h-1c-2 0-6 0-8 1-5 0-9 2-12 5h-1-1 0-1 0l-1-1c1-1 4-2 5-3h0c5-3 12-3 18-3z" class="C"></path><path d="M441 107h0 1c0-1 1-1 2-2l-3 6c2 0 4-1 5-1h0c-1 1-4 2-5 3l1 1h0 1 0 1 1c-4 3-4 6-5 11h-2c0-3 1-6 2-9 0-3 0-6 1-9z" class="K"></path><defs><linearGradient id="D" x1="475.041" y1="108.887" x2="457.203" y2="101.979" xlink:href="#B"><stop offset="0" stop-color="#a8a5a8"></stop><stop offset="1" stop-color="#dfdede"></stop></linearGradient></defs><path fill="url(#D)" d="M469 87c3-1 7-1 10-1 2 7 1 15-3 22-2 4-7 7-11 10-5 2-10 4-16 5l-4 1-5 1c1-5 1-8 5-11 3-3 7-5 12-5 2-1 6-1 8-1h1l-2-1h4c5-6 6-13 7-20h-6z"></path><path d="M445 114c3-3 7-5 12-5h9c-6 4-12 7-17 12l1 1-1 1-4 1-5 1c1-5 1-8 5-11z" class="B"></path><path d="M449 121l1 1-1 1-4 1 1-1c1-1 2-2 3-2zM273 426l12 14c0 1 1 2 2 3 9 15 17 31 24 47l6 15c-2 0-3-1-4-2-12-8-21-19-31-30-2-2-4-5-7-8-1-3-3-5-4-8-2-4-2-8-1-12 0-7 1-13 3-19z" class="E"></path><path d="M287 443c-6 6-7 14-6 22 0 2 1 4 0 6v-2c-3-10-2-19 4-29 0 1 1 2 2 3z" class="C"></path><defs><linearGradient id="E" x1="453.691" y1="294.53" x2="436.709" y2="290.275" xlink:href="#B"><stop offset="0" stop-color="#b9b7b9"></stop><stop offset="1" stop-color="#d9d9d9"></stop></linearGradient></defs><path fill="url(#E)" d="M406 338l2 1s1 1 2 1h0c1 1 2 2 2 3 1 1 1 1 2 1 0 1 1 1 1 1l1 1 1 1h0l1 1h1v1l1 1c1 1 0-1 1 0h0 1 0l1-1v-1h1v-1c1-1 1-2 1-2h1l1-3v-1l1-1c0-1 0-1 1-1 0-2 1-4 2-5l1-3v-1l5-10v-2l1-1 1-3v-1c1-1 1-2 1-3 1-1 1-2 1-2 0-1 0-1 1-2l1-3v-2l1-3h0c1-1 1-1 1-2h0v-2c1-1 1-3 1-4l1-3c1-1 0-1 0-2 1-1 1-1 1-2v-1c1-1 1-1 1-2v-2-1c1-1 1-1 1-2v-1c1-1 0-2 0-3 1 0 1 0 1-1v-2c1-1 1-1 1-2v-2-3c1 0 1-1 1-2v-3-2c1-1 1-2 1-4v-4c1-1 0-7 0-9 2-1 0-5 1-8h0-1s0-1-1 0h-1c0-1 0-1-1-1h-2v-1h-2 0c-1 0-2-1-3-1s-1 0-1-1l-2-1c-1 0-2 0-3-1h0c-1-1-2-2-2-3h0 1c0 1 1 1 1 1l5 3 1 1c1 1 2 1 3 1l5 2h3c3 1 8 0 10 2v2 2h0v3c0 9-1 18-3 27-5 25-13 48-22 72l-6 15c-1 2-2 5-3 7-9-5-19-12-25-20z"></path><defs><linearGradient id="F" x1="449.197" y1="179.128" x2="424.954" y2="189.515" xlink:href="#B"><stop offset="0" stop-color="#c6c5c6"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#F)" d="M420 142l11 4 2 4h1c1 3 3 8 6 10 2 1 8 1 10 3l-9-1c1 2 3 7 5 8v1l-1 2c2 2 4 4 7 6 1 2 2 3 3 5 2 1 3 4 4 6 4 9 5 19 6 28l1 6h-1l-2 1-2-1h0-2c-1-1-3-1-4-1-1-1-2-1-3-1-4 1-9-3-13-5l-1-1c-4-3-7-7-9-12-1-4 0-9-1-14 0-6-2-12-4-18l-3-6 1-2c-1-1-2-3-2-5v-1c-1-3-2-13 0-16z"></path><path d="M420 142l11 4 2 4v3-1c-1 0-1-1-1-2-1-1-2-1-2-3l-1-1-1 1h-1l-1-1h1-2 1-1-1v-1h-1-3v1c-1 4 0 9 0 13v-1c-1-3-2-13 0-16z" class="G"></path><path d="M441 167l4 6c2 2 4 4 7 6 1 2 2 3 3 5-1 0-2-1-4-2h0-1l-1 1h-1c0-4-2-5-4-8-2-2-3-5-3-8z" class="J"></path><path d="M422 165c1-2 1-2 2-3h1c2 1 4 2 5 5h-1c1 0 1 1 1 1l-1 1c-1 1-1 0-2 1l-2 1-1 1-3-6 1-2v1z" class="E"></path><path d="M422 164v1c0 1 1 2 2 2 1 1 3 0 5 1v1c-1 1-1 0-2 1l-2 1-1 1-3-6 1-2z" class="C"></path><path fill="#ab100f" d="M434 150c1 3 3 8 6 10 2 1 8 1 10 3l-9-1c1 2 3 7 5 8v1l-1 2-4-6c-3-4-6-9-8-14v-3h1z"></path><path d="M427 170c2 1 7 2 9 4l-9-2 3 18c0 4 0 9 1 13 1 6 6 11 11 14 1 1 6 4 7 4s2 1 3 1c-4 1-9-3-13-5l-1-1c-4-3-7-7-9-12-1-4 0-9-1-14 0-6-2-12-4-18l1-1 2-1z" class="I"></path><defs><linearGradient id="G" x1="462.048" y1="201.406" x2="453.778" y2="202.964" xlink:href="#B"><stop offset="0" stop-color="#adaaad"></stop><stop offset="1" stop-color="#cbcaca"></stop></linearGradient></defs><path fill="url(#G)" d="M450 182h1 0c2 1 3 2 4 2 2 1 3 4 4 6 4 9 5 19 6 28l1 6h-1l-2 1-2-1h0-2c-1-1-3-1-4-1-1-1-2-1-3-1s-2-1-3-1h2 2l1 1h2c1 0 1 1 2 1l1-1v-6c-1-1 0-3-1-5v-4c0-2-1-4-1-5l-1-3c0-3-2-7-3-10-1-2-1-5-3-7z"></path><path d="M375 172c-1-2-1-4-1-5l-12-23c3-3 5-8 7-12l5-9c1-1 2-2 2-4v-8l1-18 1-67V11 2c2 12 1 24 1 36l1 47c1 11 0 23 1 34 0 3 2 5 3 6l6 10c2 2 3 5 4 7l2 2c-1 1-2 3-2 3l-3 6-5 9c-2 3-4 5-4 8-1 4-1 9-1 14v15 52l1 22c0 4 0 9 1 13 3-2 5-4 7-6 1-2 2-4 4-6l-3 6c-2 3-4 5-6 8-3 3-2 7-2 12s2 10 5 14c4 8 9 14 15 21 1 1 2 3 3 3 6 8 16 15 25 20 2 2 5 3 7 5h-1c-2-1-4-2-6-4l-1 1-3 8c-1-1-2-1-3-2h-1c-1 0-2-1-3-2 0 0-1 0-1-1-2 1-4 6-5 8l-8 12c-1 2-2 3-3 4-1 2-3 3-4 4l-3 3v1 1c-1 1-3 2-4 3l-1-1-7-14c-1-3-3-7-3-9-1-4-1-7 0-11l-1-7-1 1h0l-1 2h0-1c-1-3 0-7-1-10 1-1 1-3 1-4v-2c1-3 1-7 0-9v-6-13l-1-21V171l-1 1z" class="I"></path><path d="M377 335c0-2 1-3 1-5v18c0 3 0 7 1 10l-1 2h0-1c-1-3 0-7-1-10 1-1 1-3 1-4v-2c1-3 1-7 0-9z" class="D"></path><path d="M376 126v1h1l-9 19 3 5h-1l-3-6 9-19z" class="B"></path><path d="M376 126l2-5c2 5 5 9 7 14 2 3 3 6 5 9-1 2-3 5-4 7h0-1 0v-1h0l3-6c-2-4-4-8-7-11 0-2-2-4-2-5s-1-1-1-2l-1 1h-1v-1z" class="E"></path><path d="M380 343l2-27v-1l2 1v9l-1 21v1c0 3 0 12-1 13h0c-1 1-1 1-1 2v2l-1-7-1 1h0c-1-3-1-7-1-10l1 3h1c0-3-1-5 0-8z" class="J"></path><path fill="#a50104" d="M378 348l1 3h1c0-3-1-5 0-8v14l-1 1h0c-1-3-1-7-1-10z"></path><defs><linearGradient id="H" x1="389.285" y1="330.875" x2="395.132" y2="327.531" xlink:href="#B"><stop offset="0" stop-color="#c1bfc1"></stop><stop offset="1" stop-color="#f9f7f9"></stop></linearGradient></defs><path fill="url(#H)" d="M384 316c4 4 7 8 10 12l6 6 4 5-3 4-1 1h-1l-14-20-1 1v-9z"></path><path d="M400 344l-1-2c0-3-3-5-3-9 1 2 1 4 2 5h1c-1-2 1-2 1-4l4 5-3 4-1 1z" class="B"></path><defs><linearGradient id="I" x1="428.269" y1="364.341" x2="403.644" y2="341.965" xlink:href="#B"><stop offset="0" stop-color="#b8b5b7"></stop><stop offset="1" stop-color="#f5f5f6"></stop></linearGradient></defs><path fill="url(#I)" d="M401 343l5-3 25 19-1 1-3 8c-1-1-2-1-3-2h-1c-1 0-2-1-3-2 0 0-1 0-1-1l-4-3v-1h0l-1-1c-1 0-2-1-2-2-2-2-3-3-5-4l-6-7v-2z"></path><path d="M371 151l7 11h1 1c-1 2-2 3-2 5-1 15 0 31 0 47v68 26 17c0 1-1 4 0 5 0 2-1 3-1 5v-6-13l-1-21V171v-7c0-1-1-3-2-4l-4-9h1z" class="E"></path><path d="M377 127l1-1c0 1 1 1 1 2s2 3 2 5c3 3 5 7 7 11l-3 6h0v1h0 1 0l-6 11h-1-1l-7-11-3-5 9-19z" class="D"></path><path d="M385 150h0v1h0 1 0l-6 11h-1c-1-4 4-9 6-12zm-1 175l1-1 14 20h1l1-1h0v2l6 7c2 1 3 2 5 4 0 1 1 2 2 2l1 1h0v1l4 3c-2 1-4 6-5 8l-8 12c-1 2-2 3-3 4-1 2-3 3-4 4l-3 3v1 1c-1 1-3 2-4 3l-1-1-7-14c-1-3-3-7-3-9-1-4-1-7 0-11v-2c0-1 0-1 1-2h0c1-1 1-10 1-13v-1l1-21z" class="B"></path><path d="M400 344l1-1h0v2l6 7c2 1 3 2 5 4 0 1 1 2 2 2l1 1h0v1l4 3c-2 1-4 6-5 8l-8 12c-1 2-2 3-3 4-1 2-3 3-4 4l-3 3v1l-4-11c1 2 2 4 3 7 2-1 7-6 8-8l4-5 6-10 3-6-3-3c-1-2-2-3-4-4l-6-7c-1-1-3-2-4-3v-1h1z" class="G"></path><defs><linearGradient id="J" x1="390.786" y1="398.522" x2="383.588" y2="359.237" xlink:href="#B"><stop offset="0" stop-color="#807d7e"></stop><stop offset="1" stop-color="#aba8aa"></stop></linearGradient></defs><path fill="url(#J)" d="M381 364v-2c0-1 0-1 1-2h0c1-1 1-10 1-13v-1c0 6 0 11 1 17 1 7 4 14 8 21l4 11v1c-1 1-3 2-4 3l-1-1-7-14c-1-3-3-7-3-9-1-4-1-7 0-11z"></path><defs><linearGradient id="K" x1="574.075" y1="67.804" x2="570.341" y2="134.631" xlink:href="#B"><stop offset="0" stop-color="#000006"></stop><stop offset="1" stop-color="#4d4d52"></stop></linearGradient></defs><path fill="url(#K)" d="M479 85c-3-9-7-17-10-26l24 24c6 5 13 11 21 16 7 4 15 6 24 8 10 1 20 3 30 3 31 1 63-4 93-12l19-6c4-2 8-4 11-4l-17 12c0-1-1-1-2-1-6 1-12 5-19 7-3 2-7 4-11 6h-2l-2 1-3 1v1h-2l-1 1c-2 1-5 2-7 2l-4 2c-18 7-38 12-58 14l-11 1c-1 0-2 1-3 1-2-1-15 0-18 0l-14-2h-6 1l-1-1-3-1c-4-2-8-6-9-10-1-2-1-4-2-5-2 4-5 8-9 10h-2c-1 1-3 1-3 2l-1 1h-4c-1-1-11-1-13-1h-4c-1-2-3-5-3-7 0-1 5-2 7-4h0c4-3 9-6 11-10 4-7 5-15 3-22v-1z"></path><path d="M495 106c0-2 0-4-1-6 0-1-1-3-1-4 4 5 5 14 4 21-2 4-5 8-9 10h-2 0 0l1-1c4-5 6-12 7-19 0 0 1 0 1-1h0z" class="B"></path><defs><linearGradient id="L" x1="491.653" y1="111.867" x2="495.451" y2="115.422" xlink:href="#B"><stop offset="0" stop-color="#525153"></stop><stop offset="1" stop-color="#706e70"></stop></linearGradient></defs><path fill="url(#L)" d="M495 106c1 6 1 10-3 15-1 2-3 4-5 5 4-5 6-12 7-19 0 0 1 0 1-1h0z"></path><path d="M499 122v-1c0 1 0 1 1 2 0 0 3 1 4 1 1 1 2 1 3 1s3 1 5 1h5l19 3 9 1 10 2h1c2 0 3 1 5 1 1 0 2 0 2 1l-11 1c-1 0-2 1-3 1-2-1-15 0-18 0l-14-2h-6 1l-1-1-3-1c-4-2-8-6-9-10z" class="F"></path><defs><linearGradient id="M" x1="181.813" y1="65.52" x2="183.748" y2="131.831" xlink:href="#B"><stop offset="0" stop-color="#000004"></stop><stop offset="1" stop-color="#4d4c50"></stop></linearGradient></defs><path fill="url(#M)" d="M69 92c4 1 9 3 14 5l17 4c27 7 56 10 84 9 10 0 20-1 30-3 5-1 9-1 13-3 13-5 24-15 34-25l17-20c-3 9-7 17-9 26v2c3-1 7-1 10 0h-7l3 9 2 5v3l1 1c0 1 1 1 2 2h-1 0c-1 1-2 1-2 2l-1 1c3 3 6 6 9 8 1 2 2 2 4 2l3 2h0c-1 1-2 1-4 1l-3 1-25 6c1 2 3 2 4 4 1 1 2 1 3 2l-10-1h-21c-4 1-9 2-14 2-7 1-14 2-21 2l-16-1-8-1c-2 0-4-1-6 0-6-3-13-4-20-6-13-3-26-8-38-14-1 0-3-2-4-2l-6-3c-7-3-14-7-21-11l-9-6h-1v-1c-2 0-2 0-3-2z"></path><path d="M269 87c3-1 7-1 10 0h-7l3 9 2 5v3l1 1c0 1 1 1 2 2h-1 0c-1 1-2 1-2 2l-1 1-1-2c-4-6-7-15-6-21z" class="G"></path><defs><linearGradient id="N" x1="247.624" y1="109.937" x2="251.916" y2="116.419" xlink:href="#B"><stop offset="0" stop-color="#565456"></stop><stop offset="1" stop-color="#7b7c7d"></stop></linearGradient></defs><path fill="url(#N)" d="M254 103c1-2 1-3 2-4h1-1c-1 3-2 7-2 9-2 9-4 18-12 23-1 2-3 3-5 3h-1v-1c2-1 5-3 6-5l3-6 3-6c1-5 3-10 6-13z"></path><path d="M254 103c1-2 1-3 2-4h1-1c-1 3-2 7-2 9-2 9-4 18-12 23h-1l2-2c1-2 2-3 4-5 3-6 6-14 7-21z" class="G"></path><path d="M254 108h1c0 2 0 4-1 5 0 7 0 13 5 18 2 1 3 2 5 3 1 1 2 1 3 2l-10-1h-7-13v-1c2 0 4-1 5-3 8-5 10-14 12-23z" class="L"></path><path d="M113 117l1-1h1c2 1 4 2 5 3h2c2 1 3 1 5 2 0 0 1 0 1 1 2 0 2 0 3 1l20 4 5 1 5 1h4l5 1h3c3 1 7 0 10 0h20 5c1-1 2 0 3-1h6c2-1 4 0 6-1h4 1c3-1 5-1 7-2 3 0 7 0 8-2l2-2-3 6c-1 2-4 4-6 5v1h1v1h13 7-21c-4 1-9 2-14 2-7 1-14 2-21 2l-16-1-8-1c-2 0-4-1-6 0-6-3-13-4-20-6-13-3-26-8-38-14z" class="F"></path><path d="M187 273c1-1 2-3 2-5 1-4 1-8 1-12-1-11-6-23-11-33-19-40-49-74-82-103L71 99 56 87l13 5c1 2 1 2 3 2v1h1l9 6c7 4 14 8 21 11l6 3c1 0 3 2 4 2 12 6 25 11 38 14 7 2 14 3 20 6 5 3 8 6 11 12 7 10 12 21 17 31l18 40 34 79 35 83 67 160 13 32 14 37c1-9 2-18 4-26 4-15 8-30 13-45 12-34 26-67 42-100 16-34 33-66 47-101 4-10 8-21 11-32 6-20 11-41 15-63l4-30 1-27c1-12 1-26 7-36 6-9 15-13 25-15 1 0 2-1 3-1l11-1c20-2 40-7 58-14l4-2c2 0 5-1 7-2l1-1h2v-1l3-1 2-1h2c4-2 8-4 11-6 7-2 13-6 19-7 1 0 2 0 2 1-40 31-77 66-101 111-7 13-14 28-15 43l-1 5h0c0 7 1 14 7 19 7 7 19 9 28 13l15 4c-9 0-19 0-28 2-14 2-28 9-38 19-9 9-16 20-16 33v5c2 7 5 12 12 16 6 3 15 5 22 6l5 1h0l-17 4c-6 1-11 3-17 6-26 10-51 32-66 57-11 20-21 41-28 63 5-2 9-4 14-6 5-4 10-7 16-11-2 2-5 4-7 6-7 5-15 10-24 13-2 4-3 9-5 14l-6 16-20 53-19 52c-2 5-4 11-6 17l-38-96-13-33c-3-8-6-16-10-23 0-1-1-2-1-4l-6-15c-7-16-15-32-24-47-1-1-2-2-2-3l-12-14v-1c-3-3-6-6-9-8-16-17-38-29-60-35-7-2-14-3-22-5 2-1 6-1 8-1 8-2 18-4 25-10 2-1 3-3 5-5 1-4 2-7 3-11 0-12-7-23-15-31-11-12-27-20-43-22-9-1-17-1-26-2l16-4c6-2 13-4 20-7 2-1 4-2 6-4 3-2 4-4 6-7z" class="L"></path><path d="M502 314l17-64v3c0 2-1 3-1 5s-1 5-1 7c-1 2-1 4-2 6v4-1l1 1-2 5v2l-2 3v2c0 1-1 1-1 2l-1 3v1c-1 1-1 1 0 2h-1c0 1 0 2-1 3v1c0 2-1 3-1 4s0 0-1 1v1c0 1-1 2-1 2v2l-1 2h-1c0 2-1 2-1 3z" class="H"></path><defs><linearGradient id="O" x1="567.587" y1="176.048" x2="570.372" y2="178.933" xlink:href="#B"><stop offset="0" stop-color="#656465"></stop><stop offset="1" stop-color="#7d797d"></stop></linearGradient></defs><path fill="url(#O)" d="M563 182h-2 0 2v-1h1v-1h0v-1h2l-1-1h0l-1-1h2v-1h1 1c1 0 4-3 5-4s1-2 2-3c1 0 1-1 1-1 1-1 0-1 1-1h0v-1c1 0 1-1 2-2 0-1 0 0 0 0v1l-30 48h0v-2c2-3 3-8 5-10 1-2 3-4 4-6l-1-1c1-4 5-7 7-11l-1-1z"></path><defs><linearGradient id="P" x1="489.324" y1="354.374" x2="493.74" y2="355.767" xlink:href="#B"><stop offset="0" stop-color="#787577"></stop><stop offset="1" stop-color="#8f898d"></stop></linearGradient></defs><path fill="url(#P)" d="M495 344h2c2-5 3-9 5-14 2-4 4-7 5-10 1-2 2-4 3-5 0 1 0 2-1 3 0 2 0 2-1 3 0 0 1 0 0 1 0 1-1 1-1 2-1 2-1 3-1 5v1h-1 0c0-1 0-1-1-2v1h0v3 1c-1 2-2 3-2 5 0 0 1 0 1-1l-18 32h-1c0-1 1-2 1-3v-1l7-14c1-2 2-5 3-7z"></path><path d="M516 275h0v-1l-1-1h1v-2h0 1 1 0c0-1-1-1-1 0v-1h1v-1c1 0 1 0 1 1s-1 1-1 2v1l-1 2-1 4v1c0 1-1 2-1 3h0l-1 3-5 14v3c-1 0-1 0-1 1 0 2 0-1 0 1l-1 1c0 2-1 5-2 7l-4 10-4 10-1 5-4 10v3l-7 14v-1-1l1-1v-1c1-2 2-3 2-4s0-1 1-1v-1l1-1v-2h0 1l-1-1h1v-1-1h0v-1h0v-2l1-1v-1-1-1c1 0 1-1 1-2v-3l9-23c0-1 1-1 1-3h1l1-2v-2s1-1 1-2v-1c1-1 1 0 1-1s1-2 1-4v-1c1-1 1-2 1-3h1c-1-1-1-1 0-2v-1l1-3c0-1 1-1 1-2v-2l2-3v-2l2-5z" class="F"></path><defs><linearGradient id="Q" x1="469.225" y1="391.3" x2="474.168" y2="393.208" xlink:href="#B"><stop offset="0" stop-color="#3a3a3e"></stop><stop offset="1" stop-color="#656266"></stop></linearGradient></defs><path fill="url(#Q)" d="M493 337v3c0 1 0 2-1 2v1 1 1l-1 1v2h0v1h0v1 1h-1l1 1h-1 0v2l-1 1v1c-1 0-1 0-1 1s-1 2-2 4v1l-1 1v1 1 1c0 1-1 2-1 3h1l-1 3c-1 2-2 4-4 6v1l-3 6-12 26-14 30v-1h0v-1 1c-1 1-2 2-2 3v1h-1l45-107z"></path><path d="M480 378c1-1 1-1 1-2v-1c0-2 0-3 1-4l1 1h1c-1 2-2 4-4 6z" class="F"></path><defs><linearGradient id="R" x1="275.089" y1="381.835" x2="270.348" y2="383.35" xlink:href="#B"><stop offset="0" stop-color="#525252"></stop><stop offset="1" stop-color="#736e73"></stop></linearGradient></defs><path fill="url(#R)" d="M262 357l4 9c1 2 3 6 5 7 0 1 1 1 1 2h0c1 1 1 1 1 2v1h0c0 1 1 2 1 2v1s0 1 1 1h0v-1h0l1 1c0-1 1-1 1-1v-1h0l1-2h0l9 21c2 5 4 9 6 14 1 1 1 2 2 4h-1v1h1l-1 1s0 1 1 1v1l-1 1c0 1 0 1 1 2v2h0l1 1v1l5 12h-1l-3-6c0-1-1-2-1-3l-9-18-2-5-5-10-14-27h0v-3l-1-1c0-1-1-2-1-3-1-1-1-3-2-5v-2z"></path><path d="M275 382v-1h0l1 1h0v1 1c1 1 1 1 1 2v1c-1-1-1-2-1-3s-1-1-1-2zm5 16h1s1 1 1 2v-1c1 2 1 4 3 5l-1 1c1 0 2 1 1 2v1l-5-10zm7 15h1c1 0 1 1 1 1h0c1 0 1 0 1 1h0v1l1 1v1c0 1 0 1 1 2 0 1 1 3 1 4l1 2h0c1 2 2 2 2 4v1l-9-18z" class="H"></path><defs><linearGradient id="S" x1="186.35" y1="186.647" x2="174.865" y2="195.4" xlink:href="#B"><stop offset="0" stop-color="#3e3d3f"></stop><stop offset="1" stop-color="#888386"></stop></linearGradient></defs><path fill="url(#S)" d="M151 145h1c3 1 5 4 7 7 2 2 4 4 6 7l6 8 8 11c1 2 3 4 4 6 1 0 1 1 1 2h0 1c1 1 1 2 2 3 4 4 6 9 9 13v1 1l1-1c1 1 1 3 2 4l7 15-2-2c-2-3-4-5-6-7l8 16s-1 0-1 1c0 0 0 1 1 2 0 1 0 1 1 2 0 1 1 2 1 3v1 1l-1-2c-1-1-1-3-3-5-1 0-2-4-2-5l-12-22c-11-21-24-41-39-60z"></path><defs><linearGradient id="T" x1="234.805" y1="281.625" x2="213.134" y2="295.243" xlink:href="#B"><stop offset="0" stop-color="#7e7778"></stop><stop offset="1" stop-color="#bcb8ba"></stop></linearGradient></defs><path fill="url(#T)" d="M206 229l4 8c0 2 0 3 1 5l5 14c0 1 0 2 1 3l4 13h0c0 1 0 2 1 4s1 5 2 8v1l1 1v1c0 2 1 3 2 5 1 3 1 5 2 8 1 4 3 8 5 12 1 2 2 3 3 5 0 1 1 1 1 2-1 0-1 1-1 2s1 2 2 3c2 4 5 8 6 13h-1c-12-16-25-32-45-41l2-1 27 13c-1-5-2-11-3-15 0-2-1-5-2-6-4-15-9-29-14-43l-5-12c2 2 2 4 3 5l1 2v-1-1c0-1-1-2-1-3-1-1-1-1-1-2-1-1-1-2-1-2 0-1 1-1 1-1z"></path><path d="M155 291c6-2 13-4 20-7 2-1 4-2 6-4 3-2 4-4 6-7 0 3-3 6-5 7v1c-1 0-1 0-2 1h0c1 1 2 0 4 1 1 0 2 0 3 1 2 0 5-1 6 0h3c1 0 1 0 2 1h2 1l1 1 5 1c1 1 1 1 2 1l1 1h0c1 0 1 0 2 1l2 1s1 0 2 1l1 1 3 1v1c1 0 2 1 3 2l2 1h0v-1c0-1 0-2-1-3l1-1c1 4 2 10 3 15l-27-13-2 1c-1 0-2-1-3-2-3-1-6-2-9-2v-1h-6c-2-1-4-1-7-1-4 0-7 0-11 1-2 0-4 1-6 1-1 0-2-1-2-1zm-4-146l-12-14c5 2 10 4 15 7h0c-1 0-1-1-2-1s-1 0-1-1h-2c-1-1-1-1-2-1 2 1 3 2 4 3l3 3h0c1 1 1 2 2 2l2 2c2 2 4 3 5 5 0 1 2 2 2 2 1 0 1 0 1 1h-1l3 3 2 2 1 2 1 1s1 1 1 2l2 2 5 8v1l2 1 3 5 2 2 2 4 5 8c1 0 1 1 2 2l2 3c0 1 1 1 1 2 1 1 1 1 1 2l1 1 1 2c0 1 1 2 2 2v1h0v1s1 1 1 2l1 1 1 3 2 3c1 1 2 1 2 3v1l1 1s0 1 1 2v1c0 1 1 1 1 1v2l2 3v2l3 6c0 1 0 0 1 1 0 1 1 2 1 3 1 2 1 4 2 6 1 1 1 2 2 3 0 1 0 2 1 3v1l1 1v1c1 1 1 2 1 2l1 5 1 1c0 1 0 2 1 3h-1l-1-3c-1-1-1-1-1-2l-3-6c0-1 0-1-1-2h0c-2-2-3-6-4-8-1-1-1-2-2-2l-12-26-7-15c-1-1-1-3-2-4l-1 1v-1-1c-3-4-5-9-9-13-1-1-1-2-2-3h-1 0c0-1 0-2-1-2-1-2-3-4-4-6l-8-11-6-8c-2-3-4-5-6-7-2-3-4-6-7-7h-1z" class="H"></path><path d="M210 237l52 120v2c1 2 1 4 2 5 0 1 1 2 1 3l1 1v3h0l-5-9-4-5-7-12-6-8h1c-1-5-4-9-6-13-1-1-2-2-2-3s0-2 1-2c0-1-1-1-1-2-1-2-2-3-3-5-2-4-4-8-5-12-1-3-1-5-2-8-1-2-2-3-2-5v-1l-1-1v-1c-1-3-1-6-2-8s-1-3-1-4h0l-4-13c-1-1-1-2-1-3l-5-14c-1-2-1-3-1-5z" class="M"></path><path fill="#848082" d="M216 256c7 14 11 31 17 46 5 12 13 24 19 36 1 3 9 21 9 22-1 1 0 1 0 1v1l-4-5c1-4-2-7-3-10-2-5-4-11-7-16-5-9-11-18-15-28-2-4-3-8-4-11l-3-10c-1-3-2-7-4-10h0l-4-13c-1-1-1-2-1-3z"></path><path fill="#8c8788" d="M221 272c2 3 3 7 4 10l3 10c1 3 2 7 4 11 4 10 10 19 15 28 3 5 5 11 7 16 1 3 4 6 3 10l-7-12-6-8h1c-1-5-4-9-6-13-1-1-2-2-2-3s0-2 1-2c0-1-1-1-1-2-1-2-2-3-3-5-2-4-4-8-5-12-1-3-1-5-2-8-1-2-2-3-2-5v-1l-1-1v-1c-1-3-1-6-2-8s-1-3-1-4z"></path><path d="M238 319c3 6 7 12 10 19 1 2 3 5 2 7l-6-8h1c-1-5-4-9-6-13-1-1-2-2-2-3s0-2 1-2z" class="J"></path><path d="M563 182l1 1c-2 4-6 7-7 11-4 5-7 10-10 15-10 16-18 33-22 51-1 2-2 4-2 7h1c0 1 0 2-1 3 0 1 0 4-1 5 0 3-1 5-2 7v7c-2 4-3 9-5 14-1 3-2 5-3 8-1 1-2 3-2 4-1 1-2 3-3 5-1 3-3 6-5 10-2 5-3 9-5 14h-2c-1 2-2 5-3 7v-3l4-10 1-5 4-10 4-10c1-2 2-5 2-7l1-1c0-2 0 1 0-1 0-1 0-1 1-1v-3l5-14 1-3h0c0-1 1-2 1-3v-1c1-1 2-2 2-3h1c1-3 1-5 2-8l5-20c3-11 9-22 14-33 0-2 2-7 4-8h0v-1s1 0 1-1h1v-2h0 1v-1l1-1v-1-1l1-1v-1c1 0 2-1 2-2h0l1-1h0c1-1 1-1 1-2v-1h2c0-1 1-2 1-2l2-2v-1c1 0 1-1 3-1h0v-2h-1 0c1 0 2 0 3-1z" class="M"></path><path fill="#848082" d="M523 267h1c0 1 0 2-1 3 0 1 0 4-1 5 0 3-1 5-2 7v7c-2 4-3 9-5 14-1 3-2 5-3 8-1 1-2 3-2 4-1 1-2 3-3 5-1 3-3 6-5 10-2 5-3 9-5 14h-2l28-77z"></path><defs><linearGradient id="U" x1="550.053" y1="280.321" x2="553.399" y2="296.114" xlink:href="#B"><stop offset="0" stop-color="#292b32"></stop><stop offset="1" stop-color="#484647"></stop></linearGradient></defs><path fill="url(#U)" d="M521 292v2 1c-1 1-1 1-1 2h0 1v-1h0c2-1 3-3 5-4 1-1 1-2 2-3h1c1-1 1-1 2-1l1-1c1-1 2-2 4-2v-1l4-1 3-1c1-1 2-1 3-1h2c1-1 3 0 4 0 3 0 8 0 11-1h1 0 0l-3-4s-1 0-1-1l-1-1v-1l-1-1h0v-1-1h0l-1-1v-1-3c0-1-1-4 0-6h0c0 7 1 14 7 19 7 7 19 9 28 13h-6c-2-1-4-1-6-1h-9c-6 0-10 1-15 3l-9 3c-4-1-8 1-11 3-4 2-9 4-13 7-1 1-2 2-3 4-1 0-2 1-2 1 0-1-1-6 0-7l3-12z"></path><path d="M518 304h1 0 0c1 0 1 0 1-1 3-2 9-8 12-9 1-1 2-1 2-1 1-1 1 0 2-1h2c0-1 0-1 1-1v-1 1c1 0 1 0 2-1h2c1 0 0-1 1-1 1-1 3 0 4 0 0-1 0-1 1-1h10c2 0 3 0 4 1h1 4c1-1 1-1 2-1 0 1 0 1 1 2-6 0-10 1-15 3l-9 3c-4-1-8 1-11 3-4 2-9 4-13 7-1 1-2 2-3 4-1 0-2 1-2 1 0-1-1-6 0-7z" class="H"></path><defs><linearGradient id="V" x1="510.174" y1="306.945" x2="521.942" y2="312.299" xlink:href="#B"><stop offset="0" stop-color="#938f92"></stop><stop offset="1" stop-color="#c2b7b9"></stop></linearGradient></defs><path fill="url(#V)" d="M557 194l1 1c-1 2-3 4-4 6-2 2-3 7-5 10v2h0v1c-2 5-4 9-7 14-7 17-15 36-19 55-1 2-2 6-2 9l-3 12c-1 1 0 6 0 7 0 0 1-1 2-1 1-2 2-3 3-4 4-3 9-5 13-7 3-2 7-4 11-3-19 9-33 24-44 41 0 1-1 1-1 1 0-2 1-3 2-5v-1-3h0v-1c1 1 1 1 1 2h0 1v-1c0-2 0-3 1-5 0-1 1-1 1-2 1-1 0-1 0-1 1-1 1-1 1-3 1-1 1-2 1-3s1-3 2-4c1-3 2-5 3-8 2-5 3-10 5-14v-7c1-2 2-4 2-7 1-1 1-4 1-5 1-1 1-2 1-3h-1c0-3 1-5 2-7 4-18 12-35 22-51 3-5 6-10 10-15z"></path><path d="M509 318c1 1 1 1 1 2h0c-1 1-1 2-1 3h-1c0 1 1 1 0 2v1c-1 1-1 2-2 3 0-2 0-3 1-5 0-1 1-1 1-2 1-1 0-1 0-1 1-1 1-1 1-3z" class="J"></path><defs><linearGradient id="W" x1="505.598" y1="377.939" x2="520.984" y2="399.45" xlink:href="#B"><stop offset="0" stop-color="#2c2e33"></stop><stop offset="1" stop-color="#504e4f"></stop></linearGradient></defs><path fill="url(#W)" d="M525 354c2 7 5 12 12 16 6 3 15 5 22 6l-3 1h-8-9l-12 4c-9 3-17 8-24 13l-7 6-15 15v-5l1-1v-1-2c1 0 0 0 1-1h0v-1l1-2v-1l1-1h0l1-2s1-1 1-2c1-1 0-1 1-1l2-2v-1c1-2 3-4 5-5h0l2-2c0-1 1-1 1-1 2-1 2-2 3-2l1-1 2-2c1-1 2-1 2-1 1-1 1 0 1-1h1l1-1h1l3-2h1 0c1 0 1-1 2-1h1c0-1 1-1 2-1l1-1 5-1h2l1-1h2c1-1 1-1 2-1l-1-1c-1-1-2-2-3-4h0l-1-1v-1s0-1-1-1v-1s-1-1-1-2v-3z"></path><path d="M496 400c0-1 2-2 2-3h-2c1-1 2-3 3-4h0 0l1-1h0c0-1 2-3 3-4 2-1 4-2 5-3 4-3 10-5 14-5 3 0 5-1 7-2 3-1 5-1 8-2 1 0 1 0 2 1h0l-12 4c-9 3-17 8-24 13l-7 6z" class="F"></path><path d="M503 394h0v-1c2-3 5-6 8-7 3-2 7-3 10-5l3-1c1 0 1-1 2 0l1 1c-9 3-17 8-24 13z" class="M"></path><path d="M220 361c0 2-1 2-2 3-1 2-2 3-3 3v1c1 1 1 0 2 1h1 1 1l1 1h2l2 1h1l3 1h1 1 1c0 1 1 1 2 1l1 1h1s1 0 1 1h2c1 1 2 1 2 2h1l1 1h0c1 0 2 1 3 1 1 1 1 1 2 1v1h1c0 1 1 1 1 1l2 2c1 0 1 1 2 1l1 1c0 1 1 1 1 2 1 0 1 1 2 1v1l1 1 1 2s1 0 1 1c1 0 0 0 1 1 0 1 2 2 2 4l2 4c1 0 1 1 1 2 1 1 1 0 1 1s0 1 1 2v2 1 1l1 1h0v1 1c0 1 1 1 2 2l-1 1c-5-7-12-13-18-18-15-13-31-22-50-23l-6-1h-5-2c8-2 18-4 25-10 2-1 3-3 5-5z" class="H"></path><defs><linearGradient id="X" x1="247.308" y1="303.947" x2="240.605" y2="307.022" xlink:href="#B"><stop offset="0" stop-color="#27272a"></stop><stop offset="1" stop-color="#545253"></stop></linearGradient></defs><path fill="url(#X)" d="M206 229l-8-16c2 2 4 4 6 7l2 2 12 26c1 0 1 1 2 2 1 2 2 6 4 8h0c1 1 1 1 1 2l3 6c0 1 0 1 1 2l1 3h1c-1-1-1-2-1-3l-1-1-1-5s0-1-1-2v-1l-1-1v-1c-1-1-1-2-1-3-1-1-1-2-2-3-1-2-1-4-2-6 0-1-1-2-1-3-1-1-1 0-1-1l-3-6v-2l-2-3v-2s-1 0-1-1v-3h0 1l-1-1h1v2l1-1 1 1h-1 0l1 1h1l-1 1h1v1l1 1v1l1 1v3h0l1 1c0 1 0 1 1 1v2h1v1c0 1 1 1 1 2v2h1v2h1c1 1 0 2 1 4h1l-1 1h1v2 2c1 0 1 0 1 1v-1c1 1 1 1 1 2h1v2h0 0c1 1 1 1 1 3h0l1 1h0 0v2c1 0 1 1 1 1 0 1 1 1 1 2s0 1 1 2c0 1 0 1 1 2h0v1l3 9 12 31 20 48 7 18h0l-1 2h0v1s-1 0-1 1l-1-1h0v1h0c-1 0-1-1-1-1v-1s-1-1-1-2h0v-1c0-1 0-1-1-2h0c0-1-1-1-1-2-2-1-4-5-5-7l-4-9-52-120-4-8z"></path><path d="M206 229l-8-16c2 2 4 4 6 7l2 2 12 26 16 45c1 3 2 7 4 10l3 6c1 2 2 5 3 7 5 8 9 18 14 27 3 8 7 16 10 24l3 6c-2-1-4-5-5-7l-4-9-52-120-4-8z" class="F"></path><defs><linearGradient id="Y" x1="525.921" y1="180.187" x2="564.225" y2="208.324" xlink:href="#B"><stop offset="0" stop-color="#090b0d"></stop><stop offset="1" stop-color="#656265"></stop></linearGradient></defs><path fill="url(#Y)" d="M519 250l1-2 1-7c1-9 2-18 4-26 1-4 1-7 1-10l3-18c1-7 3-14 6-20l2-2 1-1c0-1 0-1 1-2 0-1 2-2 2-3 1 0 1-1 2-1l1-1c8-7 17-10 27-13l16-6 17-6-24 32-1 1v-1s0-1 0 0c-1 1-1 2-2 2v1h0c-1 0 0 0-1 1 0 0 0 1-1 1-1 1-1 2-2 3s-4 4-5 4h-1-1v1h-2l1 1h0l1 1h-2v1h0v1h-1v1h-2 0 2c-1 1-2 1-3 1h0 1v2h0c-2 0-2 1-3 1v1l-2 2s-1 1-1 2h-2v1c0 1 0 1-1 2h0l-1 1h0c0 1-1 2-2 2v1l-1 1v1 1l-1 1v1h-1 0v2h-1c0 1-1 1-1 1v1h0c-2 1-4 6-4 8-5 11-11 22-14 33l-5 20c-1 3-1 5-2 8h-1c0 1-1 2-2 3l1-4 1-2v-1c0-1 1-1 1-2s0-1-1-1v1h-1v1c0-1 1-1 1 0h0-1-1 0v2h-1l1 1v1h0l-1-1v1-4c1-2 1-4 2-6 0-2 1-5 1-7s1-3 1-5v-3z"></path><path d="M522 254h1c0 2-1 4-1 5v1c-1 1-2 6-2 7v-1l-1 1h-1v-1l1-1h0 1v-2h-1c1-1 0-4 1-6 0-2 1-2 2-3z" class="F"></path><path d="M497 117c1 1 1 3 2 5 1 4 5 8 9 10l3 1 1 1h-1 6l14 2c3 0 16-1 18 0-10 2-19 6-25 15-6 10-6 24-7 36l-1 27-4 30c-4 22-9 43-15 63-3 11-7 22-11 32-14 35-31 67-47 101-16 33-30 66-42 100-5 15-9 30-13 45-2 8-3 17-4 26l-14-37-13-32-67-160-35-83-34-79-18-40c-5-10-10-21-17-31-3-6-6-9-11-12 2-1 4 0 6 0l8 1 16 1c7 0 14-1 21-2 5 0 10-1 14-2h21l10 1c-1-1-2-1-3-2-1-2-3-2-4-4l25-6 3-1c2 0 3 0 4-1h0l-3-2c-2 0-3 0-4-2 4 2 9 4 13 5 5 1 9 1 14 1 1 1 4 1 5 1h0l2 1c1-1 13 3 15 3l-2-4h1c0 1 1 4 2 5 2 1 6 2 8 4l8 4v1h-1c-2-1-4-1-6 0-5 0-9 0-13 1v5c0 1-1 1-1 2-1 1 0 2-1 3 0 0 0 1-1 1l-1 12c1 0 1-1 2 0v1c-1 3-3 5-5 8 0 1 0 3-1 4h1v8s1 0 2 1c-1 2-1 4-2 7v2h0c0 1 1 1 0 2 0 1 0 3 1 4v1 1 2h1c0 1-1 1 0 2v2 1h1c1 1 1 2 0 4h1v-4l1-1v1 3h-1c2 16 8 31 16 45 2 4 4 9 7 13 5 6 11 12 17 19 1 2 4 4 5 6s1 5 2 7v1-1c1 0 1 0 1 1h0v-8l1 21v13 6c1 2 1 6 0 9v2c0 1 0 3-1 4 1 3 0 7 1 10h1 0l1-2h0l1-1 1 7c-1 4-1 7 0 11 0 2 2 6 3 9l7 14 1 1c1-1 3-2 4-3v-1-1l3-3c1-1 3-2 4-4 1-1 2-2 3-4l8-12c1-2 3-7 5-8 0 1 1 1 1 1 1 1 2 2 3 2h1c1 1 2 1 3 2l3-8 1-1c2 2 4 3 6 4h1c-2-2-5-3-7-5 1-2 2-5 3-7l6-15c9-24 17-47 22-72 2-9 3-18 3-27 1-2 1-10 0-12h-2 0l2-1h1l-1-6c-1-9-2-19-6-28-1-2-2-5-4-6-1-2-2-3-3-5-3-2-5-4-7-6l1-2v-1c-2-1-4-6-5-8l9 1c-2-2-8-2-10-3-3-2-5-7-6-10h-1l-2-4-11-4s-1 0-2-1h0l-5-1-5-2h0c0-1 2-1 3-2 1 1 1 1 2 1 4-1 8-3 12-4 10-3 23-5 33-3h1c0-1 16 0 19 0h4l1-1c0-1 2-1 3-2h2c4-2 7-6 9-10z" class="B"></path><path d="M404 494c-2-1-2-3-3-4v-1h-1-1-1 0c-1 1-4 0-5 0h-1s-1 0-2 1h0-2c-2 1-4 1-6 2h-1-2l21-6-2-6-5-11-4-11v-1l5 12 1 1v2l9 21v1zM255 138c6 0 13 1 18 3-1 4-2 8-3 13-1 3 0 7 0 11h0v-2c0-1 0-2-1-2l1-5h-1v1h0v-4c-1 0-1-1-1-1h-1l-5-3h0l-2-1c-1-1-3-2-5-3h2c1 1 1 1 2 1h0c1 1 3 2 5 3l3 2c1-1 1-2 2-2v-1c-1-1-1-3-1-4v-3h0v-1h-1-1c-1 0-1-1-2-1h-1-2c-1-1-4 0-5 0l-1-1z" class="G"></path><path d="M497 117c1 1 1 3 2 5 1 4 5 8 9 10l3 1 1 1h-1l-29-4 1-1c0-1 2-1 3-2h2c4-2 7-6 9-10z" class="L"></path><path d="M232 139h1 0c-4 1-7 3-11 5l-13 6c-6 3-11 5-16 8l-2-3c-1-4-3-8-4-12v-1c3-1 6-1 9-1h17l19-2zm40 32h0c1 2 1 4 2 5l2 7 11 26 3 8 10 24 9 21 4 10 3 7 4 10 15 37 5 9-10 8-1-1h1s1-1 2-1c0-1 0-1 1-1-1-1-2-2-3-2-1-1-2-1-3-2h-1 0-1-1l-1 1h0l-3 2v1l-1-1 16-11c-2-4-3-9-6-14h0l-1-2v-1h-1c-2 1-4 2-5 2l-4 2-5 3-14 6-3 2-1 1-3 2h-1s-1 0-1 1c-1 0-2 0-2 1-1 0-1 1-2 1h0l-2 1h0c-1 1-1 1-2 1 0 1-1 1-1 1-1 1-2 1-2 1l-1 1c-1 1-1 0-1 1l-1 1h-1 0c-1 1-2 1-3 2l1 2v1c-1-1-2-4-3-6l56-34-6-12h-1v-2h-1l1-1c0-1 0-2-1-3l-3-8-16-38-6-15c-1-3-3-6-4-9l-11-27c-2-5-5-11-6-18z" class="G"></path><defs><linearGradient id="Z" x1="379.763" y1="511.674" x2="378.238" y2="549.323" xlink:href="#B"><stop offset="0" stop-color="#aeacad"></stop><stop offset="1" stop-color="#d7d7d7"></stop></linearGradient></defs><path fill="url(#Z)" d="M404 494v-1c3 10 0 18-3 28l-5 13c-2 5-4 11-5 18v1 1h-1 0c0 1-1 2-1 2v1h0c0-2 1-5 1-7 1 0 1-1 1-1h-2c-1 1-1 1-2 1h0-1c-1-1-1-1-2-1h-1v-1c-1 0-1 1-2 0h-2l-1-1h0-2 0-1-3c-2 0-3-1-4 0h-3-3-1c-1 0-1 0-2 1v-1c-1 0-1 0-2-1v-1c0-1-1-1-1-1l-1-1v-1c-1 0-1 0-1 1s1 2 1 3c1 1 2 3 2 4l1 1v2c1 1 1 2 2 3v2h1v1 1s0 1 1 1v1 1l1 1v1h0 0c1 1 1 1 1 2h0v1c1 0 1 1 1 2 1 1 1 1 1 2v2l-13-32h1c1-2 4-4 6-5l16-9c9-5 19-11 27-17 1-1 1-3 1-4 1-5 1-8 0-13z"></path><defs><linearGradient id="a" x1="440.014" y1="130.165" x2="465.901" y2="487.975" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#27292e"></stop></linearGradient></defs><path fill="url(#a)" d="M411 136c1 1 1 1 2 1 4-1 8-3 12-4 10-3 23-5 33-3 15 5 27 15 35 29 13 25 14 56 11 84-2 21-6 41-12 60-6 22-13 43-22 64l-44 92-7 16c-3 6-5 14-9 19l-3-6-1-1 1-1c1-2 1-7 1-9v-25-7h0c-1-3-1-8 1-10 0-1 0-1 1-1v-1l27-57v1 1c-1 1-1 1-1 2h1c0-2 3-7 3-8-1 0-1 1-2 2h0c1-3 2-6 4-9 0-1 1-2 2-3v-2h0v-1c-1 2-2 3-3 5-1 0-2-1-3-1h0c-2-2-5-3-7-5 1-2 2-5 3-7l6-15c9-24 17-47 22-72 2-9 3-18 3-27 1-2 1-10 0-12h-2 0l2-1h1l-1-6c-1-9-2-19-6-28-1-2-2-5-4-6-1-2-2-3-3-5-3-2-5-4-7-6l1-2v-1c-2-1-4-6-5-8l9 1c-2-2-8-2-10-3-3-2-5-7-6-10h-1l-2-4-11-4s-1 0-2-1h0l-5-1-5-2h0c0-1 2-1 3-2z"></path><path d="M413 140c5-3 15-1 20 0 17 4 30 12 39 27 16 27 15 64 7 94l-1 2c-1-6 0-12 0-17v-14-2-9-3c-1-2-1-4-1-6-1-1 0-3-1-5l-1-7-1-2v-2l-2-5v-4l-2-5h-1c-1 0-1 1-2 0h-1-5 0c-3 0-7-1-9-3-3-2-5-4-7-6l1-2v-1c-2-1-4-6-5-8l9 1c-2-2-8-2-10-3-3-2-5-7-6-10h-1l-2-4-11-4s-1 0-2-1h0l-5-1z" class="H"></path><defs><linearGradient id="b" x1="443.689" y1="152.735" x2="439.048" y2="157.778" xlink:href="#B"><stop offset="0" stop-color="#696769"></stop><stop offset="1" stop-color="#8b888a"></stop></linearGradient></defs><path fill="url(#b)" d="M434 150l-1-3h0c7 2 14 6 19 10 2 1 4 2 5 4l-1 1h0c-1-1-2-1-2-1h-1c-1 0-1 1-2 2h-1c-2-2-8-2-10-3-3-2-5-7-6-10z"></path><defs><linearGradient id="c" x1="456.214" y1="169.434" x2="448.342" y2="173.842" xlink:href="#B"><stop offset="0" stop-color="#706d6f"></stop><stop offset="1" stop-color="#9c999a"></stop></linearGradient></defs><path fill="url(#c)" d="M446 170c-2-1-4-6-5-8l9 1h1c2 0 2 0 3 1h0 1 1l-2 1 2 2v1c1 1 1 1 1 2s1 1 1 2v1l3 7c1 0 4 0 5-1h2l1 1-1 1h0-2v1h-5 0c-3 0-7-1-9-3-3-2-5-4-7-6l1-2v-1z"></path><path d="M446 170h1c2 1 3 3 4 4 0 1 1 1 0 2-2-2-3-4-5-5v-1z" class="J"></path><path d="M446 171c2 1 3 3 5 5s7 4 10 5h5v1h-5 0c-3 0-7-1-9-3-3-2-5-4-7-6l1-2z" class="D"></path><defs><linearGradient id="d" x1="476.113" y1="274.766" x2="440.617" y2="271.156" xlink:href="#B"><stop offset="0" stop-color="#545255"></stop><stop offset="1" stop-color="#8b8789"></stop></linearGradient></defs><path fill="url(#d)" d="M452 179c2 2 6 3 9 3h0 5 1c1 1 1 0 2 0h1l2 5v4l2 5v2l1 2 1 7c1 2 0 4 1 5 0 2 0 4 1 6v3 9 2 14c0 5-1 11 0 17-3 14-8 29-13 43l-9 24-6 15-4 9c-1 2-2 3-2 5-1 2-2 3-3 5-1 0-2-1-3-1h0c-2-2-5-3-7-5 1-2 2-5 3-7l6-15c9-24 17-47 22-72 2-9 3-18 3-27 1-2 1-10 0-12h-2 0l2-1h1l-1-6c-1-9-2-19-6-28-1-2-2-5-4-6-1-2-2-3-3-5z"></path><path d="M285 118c4 2 9 4 13 5 5 1 9 1 14 1 1 1 4 1 5 1h0l2 1c1-1 13 3 15 3l-2-4h1c0 1 1 4 2 5 2 1 6 2 8 4l8 4v1h-1c-2-1-4-1-6 0-5 0-9 0-13 1v5c0 1-1 1-1 2-1 1 0 2-1 3 0 0 0 1-1 1l-1 12c1 0 1-1 2 0v1c-1 3-3 5-5 8 0 1 0 3-1 4h1v8s1 0 2 1c-1 2-1 4-2 7v2h0c0 1 1 1 0 2 0 1 0 3 1 4v1 1 2h1c0 1-1 1 0 2v2 1h1c1 1 1 2 0 4h1v-4l1-1v1 3h-1c2 16 8 31 16 45 2 4 4 9 7 13 5 6 11 12 17 19 1 2 4 4 5 6s1 5 2 7v1-1c1 0 1 0 1 1h0v-8l1 21v13 6c1 2 1 6 0 9v2c0 1 0 3-1 4 1 3 0 7 1 10h1 0l1-2h0l1-1 1 7c-1 4-1 7 0 11 0 2 2 6 3 9l7 14 1 1c1-1 3-2 4-3v-1-1l3-3c1-1 3-2 4-4 1-1 2-2 3-4l8-12c1-2 3-7 5-8 0 1 1 1 1 1 1 1 2 2 3 2h1c1 1 2 1 3 2l3-8 1-1c2 2 4 3 6 4h1 0c1 0 2 1 3 1 1-2 2-3 3-5v1h0v2c-1 1-2 2-2 3-2 3-3 6-4 9h0c1-1 1-2 2-2 0 1-3 6-3 8h-1c0-1 0-1 1-2v-1-1l-27 57v1c-1 0-1 0-1 1-2 2-2 7-1 10h0v7 25c0 2 0 7-1 9l-1 1-65-157-22-53-27-65-11-28c-2-4-4-9-6-14-2-6-3-13-1-20 3-7 9-12 16-15-2 0-4 0-6 1h-13-4c-1-1-2-1-3-2-1-2-3-2-4-4l25-6 3-1c2 0 3 0 4-1h0l-3-2c-2 0-3 0-4-2z" class="L"></path><path fill="#ab100f" d="M376 350c1 3 0 7 1 10h1 0l1-2h0v12c-1-2-2-5-3-7v-5c0-3-1-6 0-8z"></path><defs><linearGradient id="e" x1="405.757" y1="422.205" x2="391.941" y2="398.334" xlink:href="#B"><stop offset="0" stop-color="#4c4a4d"></stop><stop offset="1" stop-color="#7b787b"></stop></linearGradient></defs><path fill="url(#e)" d="M396 396l7 17 3 6c-1 2-2 5-4 7l-11-28 1 1c1-1 3-2 4-3z"></path><defs><linearGradient id="f" x1="427.448" y1="421.134" x2="410.929" y2="398.061" xlink:href="#B"><stop offset="0" stop-color="#262629"></stop><stop offset="1" stop-color="#615d61"></stop></linearGradient></defs><path fill="url(#f)" d="M411 407c1 0 2-2 3-3 2-2 5-4 6-6 2-1 3-3 4-5 3-3 5-6 7-9 0 1-1 4-2 6h0l-2 3v2l-20 43c-1-4-3-9-5-12 2-2 3-5 4-7l5-12z"></path><defs><linearGradient id="g" x1="428.372" y1="387.819" x2="417.168" y2="380.078" xlink:href="#B"><stop offset="0" stop-color="#575558"></stop><stop offset="1" stop-color="#868184"></stop></linearGradient></defs><path fill="url(#g)" d="M431 359c2 2 4 3 6 4h1 0c1 1 2 1 3 2-2 6-6 13-8 19-2 3-4 8-6 11v-2l2-3h0c1-2 2-5 2-6-2 3-4 6-7 9-1 2-2 4-4 5-1 2-4 4-6 6-1 1-2 3-3 3l15-38-3-3h1c1 1 2 1 3 2l3-8 1-1z"></path><defs><linearGradient id="h" x1="299.004" y1="121.696" x2="299.387" y2="134.565" xlink:href="#B"><stop offset="0" stop-color="#565456"></stop><stop offset="1" stop-color="#6e6c6e"></stop></linearGradient></defs><path fill="url(#h)" d="M285 118c4 2 9 4 13 5 5 1 9 1 14 1 1 1 4 1 5 1h0l2 1c1-1 13 3 15 3l-2-4h1c0 1 1 4 2 5 2 1 6 2 8 4h0c-4 0-9-1-13-2-9-1-17-1-26 0-3 0-7 1-10 2-1 0-4 1-4 1-2 0-4 0-6 1h-13-4c-1-1-2-1-3-2-1-2-3-2-4-4l25-6 3-1c2 0 3 0 4-1h0l-3-2c-2 0-3 0-4-2z"></path><defs><linearGradient id="i" x1="420.427" y1="396.226" x2="402.958" y2="383.458" xlink:href="#B"><stop offset="0" stop-color="#949194"></stop><stop offset="1" stop-color="#d2d2d2"></stop></linearGradient></defs><path fill="url(#i)" d="M419 363c0 1 1 1 1 1 1 1 2 2 3 2l3 3-15 38-5 12-3-6-7-17v-1-1l3-3c1-1 3-2 4-4 1-1 2-2 3-4l8-12c1-2 3-7 5-8z"></path><defs><linearGradient id="j" x1="310.025" y1="146.766" x2="316.539" y2="166.703" xlink:href="#B"><stop offset="0" stop-color="#242428"></stop><stop offset="1" stop-color="#444449"></stop></linearGradient></defs><path fill="url(#j)" d="M300 184l-2-6c-2-6-4-14-1-20 2-3 4-5 7-7 8-6 17-8 27-11v5c0 1-1 1-1 2-1 1 0 2-1 3 0 0 0 1-1 1l-1 12c1 0 1-1 2 0v1c-1 3-3 5-5 8 0 1 0 3-1 4h1v8s1 0 2 1c-1 2-1 4-2 7-3 3-5 6-8 9l-7 6-1-2-3-6c-2-5-4-10-5-15z"></path><path d="M313 161h0l-5-4c3 1 5 1 7 2 1 1 5 3 5 4v3l-7-5z" class="C"></path><path d="M313 161l7 5 4 3c-6 1-11 2-15 5l-1-1c1-4 2-10 5-12z" class="E"></path><path d="M315 159c2-3 5-5 8-8 1-1 3-2 4-3h1 0v3l-1 12c0 1-1 3-2 4h0l-5-4c0-1-4-3-5-4z" class="B"></path><defs><linearGradient id="k" x1="310.267" y1="184.082" x2="322.434" y2="184.173" xlink:href="#B"><stop offset="0" stop-color="#c8c6c7"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#k)" d="M327 163c1 0 1-1 2 0v1c-1 3-3 5-5 8 0 1 0 3-1 4h1v8s1 0 2 1c-1 2-1 4-2 7-3 3-5 6-8 9l-7 6-1-2-3-6c-2-5-4-10-5-15 0-1 0-3 1-5h1v-1h0c2-2 3-3 5-3 1-1 1-1 2-1 4-3 9-4 15-5l-4-3v-3l5 4h0c1-1 2-3 2-4z"></path><path d="M314 200c5-5 8-9 10-16 0 0 1 0 2 1-1 2-1 4-2 7-3 3-5 6-8 9l-7 6-1-2 2-2 4-3z" class="D"></path><path d="M327 163c1 0 1-1 2 0v1c-1 3-3 5-5 8 0 1 0 3-1 4v2h0l-1-7c-4 0-8 2-12 4l-2 1c-2 1-4 2-6 2h0c2-2 3-3 5-3 1-1 1-1 2-1 4-3 9-4 15-5l-4-3v-3l5 4h0c1-1 2-3 2-4z" class="I"></path><path d="M310 175c0 6 0 11 1 17 1 2 1 5 2 7l1 1-4 3-1-1c-1-1-1-4-1-5-1-5-1-10-1-14 0-2 0-5 1-7l2-1z" class="J"></path><defs><linearGradient id="l" x1="301.024" y1="190.742" x2="307.306" y2="190.482" xlink:href="#B"><stop offset="0" stop-color="#6a686a"></stop><stop offset="1" stop-color="#8d888a"></stop></linearGradient></defs><path fill="url(#l)" d="M302 178c2 0 4-1 6-2-1 2-1 5-1 7 0 4 0 9 1 14 0 1 0 4 1 5l1 1-2 2-3-6c-2-5-4-10-5-15 0-1 0-3 1-5h1v-1z"></path><defs><linearGradient id="m" x1="327.579" y1="297.103" x2="359.667" y2="272.646" xlink:href="#B"><stop offset="0" stop-color="#959497"></stop><stop offset="1" stop-color="#c0bdbd"></stop></linearGradient></defs><path fill="url(#m)" d="M316 201c3-3 5-6 8-9v2h0c0 1 1 1 0 2 0 1 0 3 1 4v1 1 2h1c0 1-1 1 0 2v2 1h1c1 1 1 2 0 4h1v-4l1-1v1 3h-1c2 16 8 31 16 45 2 4 4 9 7 13 5 6 11 12 17 19 1 2 4 4 5 6s1 5 2 7v1-1c1 0 1 0 1 1h0v-8l1 21v13 6c1 2 1 6 0 9v2c0 1 0 3-1 4-1 2 0 5 0 8v5c-2-3-3-6-4-9l-9-19-31-74-13-31-7-14-3-9 7-6z"></path><path fill="#814d4d" d="M377 316v13 6c1 2 1 6 0 9v2c0 1 0 3-1 4-1 2 0 5 0 8l-1-24v-9c1-3 1-6 2-9z"></path><path d="M352 289c0 1 0 1 1 1 0 0 0 1 1 1 1 2 1 3 2 4l12 12c2 2 3 4 4 6s1 4 1 5c0 5 0 10 1 15 0 2 0 5 1 7-1 2 0 3-1 5l-22-56z" class="B"></path><defs><linearGradient id="n" x1="333.43" y1="265.882" x2="363.565" y2="241.572" xlink:href="#B"><stop offset="0" stop-color="#cdcbcc"></stop><stop offset="1" stop-color="#f8f9fa"></stop></linearGradient></defs><path fill="url(#n)" d="M316 201c3-3 5-6 8-9v2h0c0 1 1 1 0 2 0 1 0 3 1 4v1 1 2h1c0 1-1 1 0 2v2 1h1c1 1 1 2 0 4h1v-4l1-1v1 3h-1c2 16 8 31 16 45 2 4 4 9 7 13 5 6 11 12 17 19 1 2 4 4 5 6s1 5 2 7v1-1c1 0 1 0 1 1h0v-8l1 21c-1 3-1 6-2 9v9c-1 2-1 4 0 6-1-2-1-5-1-7-1-5-1-10-1-15 0-1 0-3-1-5s-2-4-4-6l-12-12c-1-1-1-2-2-4-1 0-1-1-1-1-1 0-1 0-1-1h-1c-3-8-6-16-10-24-2-4-5-8-8-12h1c0 1 1 1 2 2v1c1 1 1 2 2 2-1-3-3-5-4-8l-6-11c-1-2-2-4-3-5h0l-6-16h0v-1c-1-1-1-1-1-2l-1-1c1 0 1-1 1-1 0-1-1-1-1-1l1-1c0-2-1-5-2-7v-3z"></path><path d="M376 295l1 21c-1 3-1 6-2 9v-23 1-1c1 0 1 0 1 1h0v-8z" class="K"></path></svg>