<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="152 75 744 848"><!--oldViewBox="0 0 1024 1024"--><path fill="#636262" d="M466 892c1 1 1 2 1 3h1c-3 1-7 0-11 0h-23-10l-2-1h0l8-1c1 0 3 0 4 1l6-1 19-1c2 0 5 1 7 0z"></path><path fill="#5e5d5d" d="M422 894l8-1c1 0 3 0 4 1h1 9 0c-3 0-8 0-10 1h-10l-2-1h0z"></path><path fill="#d8d7d7" d="M525 407l7-25 3 2-4 21c-1 2-2 2-3 4l-3-2z"></path><path fill="#636262" d="M562 892c2 1 4 1 6 1h12l35 1c1 0 2 0 3 1h-56v-3z"></path><defs><linearGradient id="A" x1="389.914" y1="900.623" x2="384.548" y2="886.779" xlink:href="#B"><stop offset="0" stop-color="#afa9ab"></stop><stop offset="1" stop-color="#d6d9d8"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M410 895c-4 1-9 0-13 0h-36-12c-2 0-5 1-7 0 2-3 22 1 26-2h-2c-1 1-14 1-16 1-2-1 0-1-2-1h-1v-1h32l11 1h1 34c3 0 8 1 11 0h4l-6 1c-1-1-3-1-4-1l-8 1h0l2 1h-14z"></path><path fill="#9f9e9e" d="M410 895v-1l-2 1v-1c4-1 10-1 14 0h0l2 1h-14z"></path><path d="M615 894c-2-1-2-2-4-2h0 55l24 1c-4 1-10 1-14 1v-1h-7v1l1 1c2 0 10 0 12-1h6 4c-7 1-14 1-21 1h-34-13-6c-1-1-2-1-3-1zm-41-546l8 6 3 3-3 15-2 7c-1 0-1 0-2-1-1 0-1-1-2 0-2-1-5-4-6-5 0 0 1 0 2 1h1v-1l1-1v-2c1-1 1-1 1-2v-3c1-1 1-2 1-3v-3c1-1 1-2 1-3v-3l1-1c-1-1-1-1-2-1-1-1-1-2-2-3z" fill="#c3c2c2"></path><defs><linearGradient id="C" x1="446.427" y1="897.587" x2="440.806" y2="883.016" xlink:href="#B"><stop offset="0" stop-color="#b2b0b2"></stop><stop offset="1" stop-color="#d3d3d2"></stop></linearGradient></defs><path fill="url(#C)" d="M465 889l1 3c-2 1-5 0-7 0l-19 1h-4c-3 1-8 0-11 0h-34-1l-11-1h5c1-1 1-1 3-1 0 1 2 1 3 1 1-1 3-1 5-1h0 3 1 3 0 1c2-2 17 1 21-1h-24c-4 0-8 0-11-1 1-1 4 0 5 0h15 43c4 0 10 1 13 0z"></path><path fill="#c3c2c2" d="M390 893l46-2c1 0 1 1 2 1h3-2c-1 0-2 0-3 1-3 1-8 0-11 0h-34-1z"></path><defs><linearGradient id="D" x1="601.288" y1="314.205" x2="588.739" y2="311.413" xlink:href="#B"><stop offset="0" stop-color="#aaa8a8"></stop><stop offset="1" stop-color="#d4d4d3"></stop></linearGradient></defs><path fill="url(#D)" d="M583 291l23 16c-1-1-2-1-3 0 0 1-1 2-1 4 0 5-2 11-4 16l-2 1c-5-3-11-7-15-11h0 1 1v1h1l1 2h1 1 0v-1c1-1 1-1 1-2v-2c1-1 1-1 1-2v-3c1-1 1-1 1-2v-3c1-1 1-1 1-2v-1c0-1 0-2 1-3v-1l-6-4c-2-1-2-1-3-2v-1z"></path><defs><linearGradient id="E" x1="593.322" y1="895.931" x2="595.639" y2="884.267" xlink:href="#B"><stop offset="0" stop-color="#b8b5b6"></stop><stop offset="1" stop-color="#dbdcdc"></stop></linearGradient></defs><path fill="url(#E)" d="M563 889h86 1 1v1l1-1 1 1c1 0 1-1 2 0 1 0 2 0 3 1h2c1 0 2 0 4 1h2-55 0c2 0 2 1 4 2l-35-1h-12c-2 0-4 0-6-1l1-3z"></path><defs><linearGradient id="F" x1="613.745" y1="322.949" x2="599.383" y2="319.086" xlink:href="#B"><stop offset="0" stop-color="#717171"></stop><stop offset="1" stop-color="#acabab"></stop></linearGradient></defs><path fill="url(#F)" d="M596 328l2-1c2-5 4-11 4-16 0-2 1-3 1-4 1-1 2-1 3 0 3 2 8 6 11 7l-6 24-15-10z"></path><defs><linearGradient id="G" x1="458.643" y1="414.353" x2="467.302" y2="411.492" xlink:href="#B"><stop offset="0" stop-color="#898988"></stop><stop offset="1" stop-color="#bab9b9"></stop></linearGradient></defs><path fill="url(#G)" d="M474 393v1l-3 3v1h-1v1l1 1-1 1 1 1h-1l-1-1h-1v1c1 2-1 1 1 4h0 1l-1 1c-1-1-1-1-3-2h0v1c0 1 1 1 0 2 1 1 1 2 1 3h1v-1-1-1-1l1 1c0 1-1 2 0 3 1 0 1 1 1 2 1 0 1 1 1 1h1 1 0l1 1 1-1 1 1h0v1 1c1 0 1-1 1-1h1v-1h1v2h1l-16 12-3 3-1-2c-2-8-4-15-7-22l3-2 18-13z"></path><path fill="#878586" d="M456 406c1 1 1 1 1 2-2 4 3 8 1 12 0 1 1 1 1 2l1 2c0 1 1 2 1 3 1 0 1 1 1 1 1 0 1 1 2 1l-3 3-1-2c-2-8-4-15-7-22l3-2z"></path><defs><linearGradient id="H" x1="599.053" y1="375.317" x2="581.289" y2="370.893" xlink:href="#B"><stop offset="0" stop-color="#727171"></stop><stop offset="1" stop-color="#b9b8b8"></stop></linearGradient></defs><path fill="url(#H)" d="M582 354l13 8c2 2 5 4 7 4 0 2-2 6-1 7-3 6-4 12-6 18l-19-13c1-1 1 0 2 0 1 1 1 1 2 1l2-7 3-15-3-3z"></path><defs><linearGradient id="I" x1="411.345" y1="257.742" x2="425.472" y2="253.155" xlink:href="#B"><stop offset="0" stop-color="#878686"></stop><stop offset="1" stop-color="#c8c7c7"></stop></linearGradient></defs><path fill="url(#I)" d="M411 247l19-14h0c0 1-1 2-2 2l-2 2h-1c0 1-1 2-2 2l-1 1c0 2 0 3 1 4v2l1 1h0l1 5c1 1 1 1 1 3h0c0 1 0 1 1 2 0 1-1 2 0 4v1h0 1s1-1 2-1l1-1h1c0-1 1-1 2-2l1-1v1c-3 2-6 5-9 7-4 3-8 6-13 9l-1 1-8-23 7-5z"></path><defs><linearGradient id="J" x1="408.119" y1="262.146" x2="412.615" y2="260.784" xlink:href="#B"><stop offset="0" stop-color="#727273"></stop><stop offset="1" stop-color="#898887"></stop></linearGradient></defs><path fill="url(#J)" d="M404 252l7-5c0 1 0 3-1 4-1 3 2 10 4 13 1 2 1 5 2 7l-2 1-1 2-1 1-8-23z"></path><defs><linearGradient id="K" x1="608.552" y1="341.304" x2="548.817" y2="325.427" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2b2b2b"></stop></linearGradient></defs><path fill="url(#K)" d="M574 348l-27-18 6-21c1-2 1-6 3-8l24 16h1c4 4 10 8 15 11l15 10-9 28c-2 0-5-2-7-4l-13-8-8-6z"></path><defs><linearGradient id="L" x1="623.529" y1="288.397" x2="564.093" y2="271.424" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2a2929"></stop></linearGradient></defs><path fill="url(#L)" d="M583 291c-2-2-5-3-7-5l-14-9c1-4 7-28 9-30l2 1c5 3 10 7 14 10 5 3 10 7 15 10 4 2 9 6 13 8l7 6c1 0 3 2 4 2l-9 30c-3-1-8-5-11-7l-23-16z"></path><defs><linearGradient id="M" x1="446.826" y1="384.231" x2="499.51" y2="367.821" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2d2d2d"></stop></linearGradient></defs><path fill="url(#M)" d="M485 321c1 1 1 2 1 3 0 4 2 8 3 12 1 3 2 6 4 9 1 3 2 7 3 10l6 18-20 14c-2 2-6 4-8 6l-18 13-3 2-8-28-8-24c2-1 4-2 5-4 1-1 4-2 5-3 5-4 10-7 14-10h0c3-3 6-5 9-7l15-11z"></path><path fill="#fdfdfd" d="M485 321c1 1 1 2 1 3 0 4 2 8 3 12 1 3 2 6 4 9l-15 10c-3 3-7 6-11 8-4 5-11 8-15 12-3 1-5 3-7 5l-8-24c2-1 4-2 5-4 1-1 4-2 5-3 5-4 10-7 14-10h0c3-3 6-5 9-7l15-11z"></path><defs><linearGradient id="N" x1="444.259" y1="360.984" x2="458.065" y2="356.921" xlink:href="#B"><stop offset="0" stop-color="#908f8e"></stop><stop offset="1" stop-color="#d5d4d4"></stop></linearGradient></defs><path fill="url(#N)" d="M442 352c1-1 4-2 5-3 5-4 10-7 14-10h0c-1 2-5 4-7 6 3 5 4 11 6 17v5c1-1 1-1 3-2h0l1-1h1l1-1h1c-4 5-11 8-15 12-3 1-5 3-7 5l-8-24c2-1 4-2 5-4z"></path><defs><linearGradient id="O" x1="441.018" y1="366.395" x2="445.89" y2="364.799" xlink:href="#B"><stop offset="0" stop-color="#777677"></stop><stop offset="1" stop-color="#91908f"></stop></linearGradient></defs><path fill="url(#O)" d="M437 356c2-1 4-2 5-4 0 2 1 2 1 3 2 2 1 7 2 9 1 3 2 6 4 9h0v-1l1 1v1l2 1c-3 1-5 3-7 5l-8-24z"></path><path fill="#141414" d="M480 417l17-12c5-2 9-5 13-9h0l10 26c1-1 1-3 2-5l3-10 3 2 1 1h-1l-1-1c-2 1-3 4-3 6h0c-1 2-2 4-2 6-1 2-1 5-1 7v11c1 1 2 1 2 2h1l-1 1-2-1c-1 1-1 8-1 11l-1 2-25 18-17 13-7-25-9-28 3-3 16-12z"></path><path fill="#fdfdfd" d="M488 447l30-21c0 9 1 19 1 28l-25 18-17 13-7-25c2-1 4-3 6-4l12-9z"></path><defs><linearGradient id="P" x1="471.008" y1="463.428" x2="475.376" y2="462.515" xlink:href="#B"><stop offset="0" stop-color="#777678"></stop><stop offset="1" stop-color="#8e8d8c"></stop></linearGradient></defs><path fill="url(#P)" d="M470 460c2-1 4-3 6-4l12-9c-1 2-2 3-2 5 0 1-1 2-1 2v1c1 1 1 2 1 3v1l1-1 3 3v3c0 1 1 1 1 2 1 1 0 2 0 3 0 2 1 3 0 5 1-1 2-2 3-2h0l-17 13-7-25z"></path><defs><linearGradient id="Q" x1="396.303" y1="199.105" x2="452.703" y2="205.677" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#282727"></stop></linearGradient></defs><path fill="url(#Q)" d="M436 146c5 0 10-1 15 0 1 0 2-1 3 0l-14 4-6 3 19 63-18 13s-4 3-5 4l-19 14-7 5c-4-12-8-24-9-36l-1-2c-1-1-3-3-3-5h0c-1 0-2-3-3-4h1c-1-1-1-2-1-2v-1-1-1-1h0v-1h1v-1-2c-1-3-1-6-1-9 1-4 2-9 4-13 2-3 3-5 5-8 1 0 1 0 1-1 0-2 0-1 1-2 0-1 1-2 2-3v-1c1 0 3-1 3-2 9-4 18-9 28-9l4-1z"></path><path fill="#c3c2c2" d="M436 146c5 0 10-1 15 0 1 0 2-1 3 0l-14 4-6 3c-9 3-18 10-24 18-3 2-4 5-6 8-4 7-7 14-8 22-1 5-2 10-1 15l-1-2c-1-1-3-3-3-5h0c-1 0-2-3-3-4h1c-1-1-1-2-1-2v-1-1-1-1h0v-1h1v-1-2c-1-3-1-6-1-9 1-4 2-9 4-13 2-3 3-5 5-8 1 0 1 0 1-1 0-2 0-1 1-2 0-1 1-2 2-3v-1c1 0 3-1 3-2 9-4 18-9 28-9l4-1z"></path><path fill="#9f9e9e" d="M436 146c5 0 10-1 15 0 1 0 2-1 3 0l-14 4-6 3c-9 3-18 10-24 18-3 2-4 5-6 8-4 7-7 14-8 22-2-9-1-19 3-27 9-15 21-23 37-28z"></path><path fill="#878586" d="M404 179c0-4 2-6 4-9h0c0-1 1-2 1-3l1-1c4-5 10-10 16-13 1-1 2-1 3-2 3 0 9-2 11-1l-6 3c-9 3-18 10-24 18-3 2-4 5-6 8z"></path><defs><linearGradient id="R" x1="413.933" y1="279.983" x2="466.829" y2="263.436" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2c2b2c"></stop></linearGradient></defs><path fill="url(#R)" d="M435 258l26-18 6 21c1 2 1 6 3 8 1 2 1 5 1 7 2 6 4 11 5 17l9 28-15 11c-3 2-6 4-9 7h0c-4 3-9 6-14 10-1 1-4 2-5 3-1 2-3 3-5 4l-9-28-7-24-9-29 1-1c5-3 9-6 13-9 3-2 6-5 9-7z"></path><path fill="#fdfdfd" d="M470 269c1 2 1 5 1 7 2 6 4 11 5 17l-24 17-13 10-9 6-2 2-7-24h0l26-19c0-1 3-2 4-3l19-13z"></path><defs><linearGradient id="S" x1="425.443" y1="310.848" x2="444.168" y2="305.118" xlink:href="#B"><stop offset="0" stop-color="#7d7c7c"></stop><stop offset="1" stop-color="#d7d6d6"></stop></linearGradient></defs><path fill="url(#S)" d="M447 285l-1 1c-1 2-6 4-6 5s2 5 2 6c1 5 3 11 4 16h1l1-2h1l1-1h1 0 1l-13 10-9 6-2 2-7-24h0l26-19z"></path><path fill="#767677" d="M421 304h0 2c1 2 1 4 2 6 0 2 1 4 2 6 1 3 1 6 3 9v1h0l-2 2-7-24z"></path><defs><linearGradient id="T" x1="430.572" y1="332.43" x2="483.014" y2="316.044" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2d2d2d"></stop></linearGradient></defs><path fill="url(#T)" d="M476 293l9 28-15 11c-3 2-6 4-9 7h0c-4 3-9 6-14 10-1 1-4 2-5 3-1 2-3 3-5 4l-9-28 2-2 9-6 13-10 24-17z"></path><path fill="#d8d7d7" d="M598 151c-1-1-3-1-5-2l-12-3c7-1 14 0 20 1 14 1 28 6 37 17 4 4 7 9 10 14l-1 1c0-1-1-2-1-3-1-1-1-1-2-1v-2l-1 1c1 1 1 2 2 3 1 0 1 1 1 2h0v1l1 2v1s1 1 1 2v1 2l1 1v3 1 1 4 1 1c1 0 0 0 0 1h0v1s-1 1-1 2h1l-1 1h0c1 1 0 1 0 2l1 1c0 1 0 1-1 2v1c1 1 1 2 1 3l-1 1v5c-1 1 0 1-1 2l-1-2v-1h0c-1 1-1 1-1 2s0 1-1 2v3c0 1 0 1-1 3v2c0 1 0 1-1 2v1 1l-1 1v2h0c-1 1-1 2-1 3 0 2 0 3-1 4 0 1 1 3 0 4 0 1 0 2-1 3v2l1-1c1 0 0 1 0 2s-1 2-2 2v2h1c-1 1-1 2-2 3 0 1 0 1-1 1v1 2l-1 1v2l-1 4h1c-1 1-1 2-2 3v2 1s0 1-1 1h1l-1 1h1l-1 1c-1 1-1 2-1 4 1 0 0 2 0 3h0c-1 3-1 4-2 6-3 2-2 8-4 11v2 1l-1 1h1l-1 1v1h0v1h0c-1 1-1 2-1 3h0v1 1 2c0 1-1 0-1 2 1 0 0 2 0 3h-1v3c0 1-1 2-1 2v1l-1 2v3s-1 1-1 2-1 1-2 2v2h0v1c-1 1-1 1 0 2l-1 1h1l-1 1c0 1-1 1-1 2h0c0 2-2 1-1 3v1 1c0 1-1 1-2 2v3l-2 3v1l-1 3v1l-1 3h1c-1 1-1 1-1 3h0c0 1-1 1-1 2h1l1 1-1 1c0 1 0 2-1 3 0 1 0 3-1 4v2l-1 2v3c0 1-1 2-1 2v2s-1 1-1 2l-1 3v2l-1 1v2l-1 1v2c0 1 0 2-1 3v2h-1v2l-1 2v1 2l-1 3-1 2v2l-1 2v3l-1 3-1 3-1 2v1l-1 3v2l-1 2v2c0 1 0 2-1 3v2l-1 3s-1 1-1 2h0l-1 2v1 2c0 1-1 2-1 2v1 1c0 1-1 2-1 2v1c0 1 0 2-1 2v2 2c0 1 0 0-1 1v3l-1 2v2l-1 1c0 3-1 5-1 7 0 1-1 1-1 2v2l-1 2v2l-3 13c0 1-1 1-1 2v2c0 1-1 2-1 4l-1 2-2 10-1 4c0 1-1 2-1 4 0 1-1 1-1 2v2c0 1-1 1-1 2v2c0 1-1 2-1 3l-3 14c-1 1-1 1-1 2v2l-1 2-1 4-3 15-16 65-5 19c-1 4-2 8-3 13l-3 17c-1 3-1 6-3 9v-6-9h0v-12c-1-6-1-13 0-19 1-8 3-17 2-26 0-2 1-5 2-8l4-15c4-11 6-22 10-33 0-2 1-5 2-8l6-22 10-36c1-6 3-13 5-19 0-1 1-3 1-4 2-5 3-10 4-15l3-10 9-30 2-5c0-2 0-4 1-5l4-14 8-29c2-6 3-12 6-18-1-1 1-5 1-7l9-28 6-24 9-30 7-24c7-19 14-40 10-60-3-7-5-14-10-21-7-10-16-18-26-24-3-1-6-3-9-4z"></path><path fill="#878586" d="M598 151c-1-1-3-1-5-2l-12-3c7-1 14 0 20 1l15 6c11 6 20 17 24 29 2 5 3 12 3 18-3-7-5-14-10-21-7-10-16-18-26-24-3-1-6-3-9-4z"></path><path fill="#c3c2c2" d="M595 391c2-6 3-12 6-18 0 2 0 3-1 5l-3 11c0 2-2 4-1 5h0c1 1 1 1 1 2l-1 1v1c-1 3-1 6-2 8v2l-1 1v1l-1 3h0l-1 2v3c0 1-1 2-1 2v1l-1 2c0 1 0 3-1 4v2c0 2-1 4-1 5l-1 2v3s-1 1-1 2v1c0 1-1 2-1 2v1l-1 2-1 4v2l-1 1v2l-1 2v2c0 1-1 3-1 4l-1 2c0 2 0 4-1 5v2l-1 2-1 6v1c0 1-1 3-1 4l-1 2v2l-1 2v2h0v2c0 1 0 0-1 1v2 1 1c0 1 0 1-1 2v2c0 1 0 0-1 1v2l-1 2v2l-1 2v1 1 1c0 1-1 2-1 2v2c0 1 0 0-1 1v2 2c-1 1-1 2-1 4 0 1-1 2-1 3v2l-1 1c0 2 0 3-1 5v2l-1 1-1 5v2c-1 1-1 2-1 3v2l-1 4c-1 2-1 4-2 6v2c0 2-1 3-1 4 0 2-1 3-1 4l-1 3-1 4-1 4v2c-1 1 0 3-1 4v2l-1 1v2c0 1-1 2-1 2v2c0 1-1 2-1 2v2c0 1-1 2-1 2v2c0 1-1 3-1 4l-4 15v2l-3 11v2c0 1-1 2-1 3s0 2-1 3v2l-9 43c0 2-1 3-2 5 0-4 2-7 2-11 3-22 9-43 14-64l6-27 5-20 9-40 11-49 3-14 5-21c1-2 2-5 1-7 0-1 0-2 1-3l1-5c0-2 0-4 1-5l4-14 8-29z"></path><path fill="#9f9e9e" d="M580 444l2-5-1 5c-1 1-1 2-1 3 1 2 0 5-1 7l-5 21-3 14-11 49-9 40-5 20-6 27c-5 21-11 42-14 64 0 4-2 7-2 11 0 2 0 5-1 7l-1 3v-9h0v-12c-1-6-1-13 0-19 1-8 3-17 2-26 0-2 1-5 2-8l4-15c4-11 6-22 10-33 0-2 1-5 2-8l6-22 10-36c1-6 3-13 5-19 0-1 1-3 1-4 2-5 3-10 4-15l3-10 9-30z"></path><path fill="#767677" d="M563 503v5c-2 5-2 10-4 15l-1 5-1 7-9 36-5 25-6 24-6 23-4 25c-1 9-1 20-5 30v3h0v-12c-1-6-1-13 0-19 1-8 3-17 2-26 0-2 1-5 2-8l4-15c4-11 6-22 10-33 0-2 1-5 2-8l6-22 10-36c1-6 3-13 5-19z"></path><path fill="#636262" d="M540 588c-1 2-1 4-1 6-2 5-3 11-4 17-3 13-7 26-9 39-1 6-1 13-2 19l-2 20c-1-6-1-13 0-19 1-8 3-17 2-26 0-2 1-5 2-8l4-15c4-11 6-22 10-33z"></path><defs><linearGradient id="U" x1="642.136" y1="202.245" x2="577.543" y2="209.465" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#232322"></stop></linearGradient></defs><path fill="url(#U)" d="M598 151c3 1 6 3 9 4 10 6 19 14 26 24 5 7 7 14 10 21 4 20-3 41-10 60l-7 24c-1 0-3-2-4-2l-7-6c-4-2-9-6-13-8-5-3-10-7-15-10-4-3-9-7-14-10-1-1-1-2-1-3 1-1 1-2 1-3v-1s1-1 1-2l1-3v-2c1-1 1-3 1-3 1-2 1-1 1-2v-3h1v-1l2-1-2-1v-1c0-2 1-4 2-6l5-18 13-47z"></path><defs><linearGradient id="V" x1="612.284" y1="251.695" x2="572.058" y2="242.149" xlink:href="#B"><stop offset="0" stop-color="#c4c4c3"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#V)" d="M573 248c-1-1-1-2-1-3 1-1 1-2 1-3v-1s1-1 1-2l1-3v-2c1-1 1-3 1-3 1-2 1-1 1-2v-3h1v-1l2-1 28 20h0c4 2 8 5 13 8l9 6c1 1 2 1 3 2l-7 24c-1 0-3-2-4-2l-7-6c-4-2-9-6-13-8-5-3-10-7-15-10-4-3-9-7-14-10z"></path><defs><linearGradient id="W" x1="623.667" y1="264.952" x2="609.102" y2="261.244" xlink:href="#B"><stop offset="0" stop-color="#8a8989"></stop><stop offset="1" stop-color="#bbbaba"></stop></linearGradient></defs><path fill="url(#W)" d="M608 244c4 2 8 5 13 8l9 6c1 1 2 1 3 2l-7 24c-1 0-3-2-4-2l-7-6c-4-2-9-6-13-8 2 0 3 1 5 2l3-9c1-2 1-4 2-6v-1-6c-1-1-2-2-3-2 0-1-1-2-1-2z"></path><defs><linearGradient id="X" x1="629.261" y1="270.739" x2="621.029" y2="267.862" xlink:href="#B"><stop offset="0" stop-color="#6e6d6e"></stop><stop offset="1" stop-color="#908f8f"></stop></linearGradient></defs><path fill="url(#X)" d="M615 276c1 0 2 1 3 2h1c4-5 4-9 5-15 1-2 1-5 2-6 1 1 2 1 3 1h1c1 1 2 1 3 2l-7 24c-1 0-3-2-4-2l-7-6z"></path><defs><linearGradient id="Y" x1="593.125" y1="395.516" x2="533.775" y2="377.454" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2b2a2a"></stop></linearGradient></defs><path fill="url(#Y)" d="M532 382l8-28 29 19h1c1 1 4 4 6 5l19 13-8 29-4 14c-1 1-1 3-1 5l-2 5-9 30-3 10c-1 5-2 10-4 15l-2-2-3-2-11-7-24-17-3-1c-1-1 0-3 0-5l-1-13c0-3 0-10 1-11l2 1 1-1h-1c0-1-1-1-2-2v-11c0-2 0-5 1-7 0-2 1-4 2-6h0c0-2 1-5 3-6l1 1h1l-1-1c1-2 2-2 3-4l4-21-3-2z"></path><path fill="#fdfdfd" d="M535 384l19 14c1 0 1 0 2 1l19 13 12 8-4 14c-1 1-1 3-1 5l-2 5c-5-3-11-8-16-11-1 0-2-2-3-2-3-2-7-4-9-7-6-2-11-7-16-10-2-1-4-4-7-4l-1-1c1-2 2-2 3-4l4-21z"></path><defs><linearGradient id="Z" x1="570.857" y1="420.168" x2="558.425" y2="417.5" xlink:href="#B"><stop offset="0" stop-color="#aeadad"></stop><stop offset="1" stop-color="#e0e0df"></stop></linearGradient></defs><path fill="url(#Z)" d="M556 399l19 13c-2 0-2 0-2 2-2 5-2 10-4 16-1 1-1 3-2 4l-1-1h-2c-1 0-2-2-3-2-3-2-7-4-9-7h1c1 1 2 2 4 2l4-22c-1-1-2-2-3-2-1-1-2-1-2-3h0z"></path><defs><linearGradient id="a" x1="583.615" y1="428.102" x2="569.487" y2="424.354" xlink:href="#B"><stop offset="0" stop-color="#717171"></stop><stop offset="1" stop-color="#afaeae"></stop></linearGradient></defs><path fill="url(#a)" d="M575 412l12 8-4 14c-1 1-1 3-1 5l-2 5c-5-3-11-8-16-11h2l1 1c1-1 1-3 2-4 2-6 2-11 4-16 0-2 0-2 2-2z"></path><defs><linearGradient id="b" x1="577.58" y1="448.085" x2="518.253" y2="432.371" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#272727"></stop></linearGradient></defs><path fill="url(#b)" d="M523 442l1-1h-1c0-1-1-1-2-2v-11c0-2 0-5 1-7 0-2 1-4 2-6h0c0-2 1-5 3-6l1 1h1c3 0 5 3 7 4 5 3 10 8 16 10 2 3 6 5 9 7 1 0 2 2 3 2 5 3 11 8 16 11l-9 30-3 10c-1 5-2 10-4 15l-2-2-3-2-11-7-24-17-3-1c-1-1 0-3 0-5l-1-13c0-3 0-10 1-11l2 1z"></path><defs><linearGradient id="c" x1="558.997" y1="471.022" x2="523.895" y2="465.689" xlink:href="#B"><stop offset="0" stop-color="#c2c1c1"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#c)" d="M520 452c0-3 0-10 1-11l2 1c2 1 3 2 5 3l26 18h0c6 4 11 8 17 11l-3 10c-1 5-2 10-4 15l-2-2-3-2-11-7-24-17-3-1c-1-1 0-3 0-5l-1-13z"></path><path fill="#d8d7d7" d="M520 452c0-3 0-10 1-11l2 1c2 1 3 2 5 3-2 1-2 3-2 5 0 3-1 6-1 9 0 2 0 4-1 6v6l-3-1c-1-1 0-3 0-5l-1-13z"></path><defs><linearGradient id="d" x1="568.257" y1="482.527" x2="556.863" y2="479.89" xlink:href="#B"><stop offset="0" stop-color="#767575"></stop><stop offset="1" stop-color="#b1b0af"></stop></linearGradient></defs><path fill="url(#d)" d="M554 463c6 4 11 8 17 11l-3 10c-1 5-2 10-4 15l-2-2-3-2-11-7 1-2h0 1 1v2l1 1v-1c1 0 0 1 1 2v-1h1v2l1-1 4-15v-2-5l-1-1-1-1-1-1c-2 0-2-1-2-2z"></path><path fill="#878586" d="M559 495c1 0 2-2 2-2 1-2 0-2 1-3h1v-1h1c-1 1-1 1-1 2v1c-1 1 0 1 0 3l-1 2-3-2z"></path><path fill="#767677" d="M563 495c2-3 3-8 4-11h1c-1 5-2 10-4 15l-2-2 1-2z"></path><defs><linearGradient id="e" x1="557.436" y1="562.18" x2="513.89" y2="558.605" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#303030"></stop></linearGradient></defs><path fill="url(#e)" d="M520 452l1 13c0 2-1 4 0 5l3 1 24 17 11 7 3 2 2 2c0 1-1 3-1 4-2 6-4 13-5 19l-10 36-6 22c-1 3-2 6-2 8-4 11-6 22-10 33l-4 15c-1 3-2 6-2 8l-2 7-2-13-9-37-12-44c-2-6-3-12-5-18l-17-54 17-13 25-18 1-2z"></path><path d="M520 452l1 13c0 2-1 4 0 5-1 4 0 8 0 11l-1 20v108 17c0 4 1 8 0 12l-9-37-12-44c-2-6-3-12-5-18l-17-54 17-13 25-18 1-2z"></path><path fill="#fdfdfd" d="M526 725l-4 14c-2-5-3-10-4-15l-11-39-35-117-69-222-16-53-9-28c-4-14-9-28-9-42-1-21 4-42 19-58 12-14 32-22 50-23h1c-1 1-2 1-2 1h-1-3c-1 0 0 0-2 1h-3c-1 0-1 0-2 1h-4c-1 1-2 1-3 1v1h-1-1l-1 1h-1c-1 0-1 0-1 1h-1-1l-1 1h1 0-1l-4 2c-2 1-3 2-4 3s-2 1-3 2h0l-2 1-1 2c-1 0-1 0-1 1l-2 1 1 1c1-2 2-2 3-3s0 0 2-1l1-1h0v1c-1 1-2 2-2 3-1 1-1 0-1 2 0 1 0 1-1 1-2 3-3 5-5 8-2 4-3 9-4 13 0 3 0 6 1 9v2 1h-1v1h0v1 1 1 1s0 1 1 2h-1c1 1 2 4 3 4h0c0 2 2 4 3 5l1 2c1 12 5 24 9 36l8 23 9 29 7 24 9 28 8 24 8 28c3 7 5 14 7 22l1 2 9 28 7 25 17 54c2 6 3 12 5 18l12 44 9 37 2 13 2-7c1 9-1 18-2 26-1 6-1 13 0 19v12h0v9 6c2-3 2-6 3-9l3-17c1-2 2-3 2-5 0 2 1 1 0 3v1c-1 2 0-1-1 2v2c-1 1-1 3-1 4-1 1-1 2-1 3v1l-2 8c0 2 0-1 0 2-1 1-1 2-1 3v3l-1 1v2c-1 1-1 2-1 3v1l-1 3h-1c0 2 0 3 1 5 0 1 0 1 1 2v2c0-2 0-2 1-3v-1c0-1 0-2 1-4v-2l1-1h0 1z"></path><path fill="#d8d7d7" d="M460 430l1 2 9 28 7 25 17 54c2 6 3 12 5 18l12 44 9 37 2 13 2-7c1 9-1 18-2 26-1 6-1 13 0 19v12h0v9 6c-1 3-1 7-1 10l-3-19c-1-2-1-4-1-5l-48-233v-1l1 1h1c0 1 0 1 1 2h0l-1-1v-2l-2-5c0-2 0-3-1-4l-1-5-1-3-1-3c-1-1-1-2-1-3-1-2-1-3-1-4-1-1-1-3-2-4 0-2 0-2-1-3v-4z"></path><path fill="#636262" d="M494 539c2 6 3 12 5 18l12 44 9 37 2 13 2-7c1 9-1 18-2 26-1 6-1 13 0 19v12h0v9 6c-1 3-1 7-1 10l-3-19c2-4-1-12-1-16l-14-104-6-35c-1-4-3-8-3-13z"></path><path fill="#5e5d5d" d="M511 601l9 37 2 13 2-7c1 9-1 18-2 26-1 6-1 13 0 19v12h-1c-2-2-1-7-1-10-1-1-1-5-1-6-1-2 0-4 0-6-1-2-1-6-1-7-1-2 0-7 0-9-1-2-1-7-1-9-1-1 0-3 0-5-1-1-1-3-1-4-1-2 0-4 0-6-1-1-1-4-1-6-1-1 0-3 0-4-1-2-1-4-1-5-1-2 0-4 0-5-1-1-1-2-1-3v-3c-1-1-1-1-1-2-1-2 0-3 0-4-1-1-1-4-1-6z"></path><path d="M465 889l-54-167-72-217-41-122c-10-32-20-64-33-96-11-26-22-52-36-77-7-11-14-23-23-33-11-12-26-21-42-24-4-1-8-1-12-1l1-18h301v8c-5-1-11-1-16 0-18 1-38 9-50 23-15 16-20 37-19 58 0 14 5 28 9 42l9 28 16 53 69 222 35 117 11 39c1 5 2 10 4 15l4-14c4-13 7-26 11-40l14-49 54-187 40-136 14-46c3-11 6-22 8-33 2-17 1-34-6-50-9-18-24-31-43-37-12-4-25-5-38-5v-8h235 41 11 3v18c-20 1-38 10-52 24-9 10-16 21-22 32-17 30-30 62-43 93-23 59-40 120-60 180L592 796l-29 93-1 3v3h-94-1c0-1 0-2-1-3l-1-3z"></path><path fill="#9f9e9e" d="M550 810c4 9 4 20-1 29-4 9-12 17-22 21-9 3-20 3-29-2-9-4-17-12-20-21l-1-5 6 6c7 9 15 13 26 15 9 1 17 0 24-6 13-9 15-22 17-37z"></path><path fill="#fdfdfd" d="M477 832c-1-7-1-14 1-20 4-10 11-19 20-23 9-5 20-5 29-2 11 4 18 12 23 23-2 15-4 28-17 37-7 6-15 7-24 6-11-2-19-6-26-15l-6-6z"></path></svg>