<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="114 108 819 848"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#a19f9f}.C{fill:#a9a7a8}.D{fill:#b6b5b5}.E{fill:#c5c3c3}.F{fill:#989595}.G{fill:#bfbebe}.H{fill:#d9d9d8}.I{fill:#8d8b8b}.J{fill:#878484}.K{fill:#40393a}.L{fill:#cfcecd}.M{fill:#787475}.N{fill:#6d696a}.O{fill:#52494a}.P{fill:#cac9c9}.Q{fill:#7f7b7b}.R{fill:#aeacac}.S{fill:#bab9b9}.T{fill:#e3e3e2}.U{fill:#5e595a}.V{fill:#524a4a}.W{fill:#939091}.X{fill:#2c2120}.Y{fill:#514849}.Z{fill:#686263}.a{fill:#362f2e}.b{fill:#332524}.c{fill:#ebeae9}.d{fill:#eeeeeb}.e{fill:#5a5354}.f{fill:#746f70}.g{fill:#9b9a9b}.h{fill:#3a2c2b}.i{fill:#828081}.j{fill:#b2b0b1}.k{fill:#f2f1f1}.l{fill:#625d5d}.m{fill:#292423}.n{fill:#d3d2d1}.o{fill:#4f4242}.p{fill:#dddddc}.q{fill:#463939}.r{fill:#433231}.s{fill:#422b2a}.t{fill:#2a1a19}.u{fill:#351817}.v{fill:#231716}.w{fill:#371e1d}.x{fill:#3c2222}.y{fill:#706765}.z{fill:#100b09}.AA{fill:#fff}</style><path d="M806 694c1 2 1 3 2 4l-1 1h-1v-5z" class="X"></path><path d="M646 153l1 3v1l-3-1c1-1 2-2 2-3z" class="q"></path><path d="M364 727l6 1-1 2-5-1v-2zm267-568h6c1 0 1 1 2 1-2 1-4 1-6 1l-1-1-1-1z" class="X"></path><path d="M866 311l4-1c1 0 3 0 4 1l-9 2-2-1c1 0 2 0 3-1z" class="Y"></path><path d="M637 159l6-2 2 1-1 2h-3-2c-1 0-1-1-2-1zm-195 14l-1-1c-1-2-1-4-1-6 1-1 3-1 4-1l2 1c-2 0-4 0-5 1l2 2c0 2 1 2 0 4h-1z" class="K"></path><path d="M806 699h1c0 5 1 8 3 13v1c-3-4-4-9-4-14z" class="t"></path><path d="M221 148h0l1 1 5 1c1 1 1 1 1 2h0-1c-4-1-8-1-12-1l6-3z" class="X"></path><path d="M822 678c5-1 10-1 15 1-2 1-3 1-5 1-2-1-4 0-6 0l-4-2z" class="t"></path><path d="M454 156l-1 4c-3 3-4 5-7 6l-2-1c4-2 5-4 7-7h1c0-1 1-1 2-2z" class="X"></path><path d="M451 158c1-5 1-8 0-12l2-2 2 1h-1-1v1c0 1 0 2 1 3v7c-1 1-2 1-2 2h-1z" class="z"></path><path d="M644 798c1 2 1 3 2 4h3c1 0 2-1 3-2l1 1c-3 2-6 4-9 3-2 0-2-1-3-2 1-2 2-3 3-4zm48-13c2 1 3 3 3 5 1 2 1 5 1 8l-1-1-1-3c0-2 0-3-1-5l-3-3c1 0 1-1 2-1z" class="K"></path><path d="M719 775l9 1v2c-5-1-11-1-16-1h0v-1l7-1z"></path><path d="M137 309h5l1 1c-6 1-10 1-15 5l-1-1c3-3 6-5 10-5z" class="a"></path><path d="M453 160c0 3 0 4-2 5l-3 3c-2 0-2 0-4 1l-1-1v1l-2-2c1-1 3-1 5-1 3-1 4-3 7-6z" class="G"></path><path d="M736 681c3 0 5 2 8 3h0l-1 1c-6-3-11-3-18-2l-1-1c3-2 9-1 12-1z" class="K"></path><path d="M303 731l17-2c0 1 1 1 1 2l-14 1-4-1z" class="X"></path><path d="M172 698c1 0 1 1 2 0l-1 7c1 4 2 7 3 10 0 1 0 3-1 4-3-7-4-13-3-21z" class="b"></path><path d="M442 173h1c-4 5-12 7-18 9l-4 2h-1c0-1 0-1-1-2 9-2 15-4 23-9z" class="v"></path><path d="M127 314l1 1c-2 3-3 6-2 10v2l1 1-1 2h-1c-2-5-3-8-2-13 1 1 1 1 2 1 1-1 1-2 1-3l1-1z" class="X"></path><path d="M206 679c6 3 10 7 12 14h0-2c-2-5-5-8-10-12v-2z" class="s"></path><path d="M265 652l2-4 1 1h0 2c-5 5-5 10-7 16l-1 1 1-13 2-1z" class="K"></path><path d="M312 692h1 1l8 5c3 2 6 5 9 5l4 2 1 1-1 1h0v1c-8-4-16-10-23-15z" class="X"></path><path d="M316 779h2c3 1 7 1 11 1 1 0 3-1 3 0 1 1 1 1 3 2-6 0-12 0-17-1h0v-1s-1 0-2-1h0zm0-88h1l13 9h0l1 2c-3 0-6-3-9-5l-8-5 2-1z" class="Y"></path><path d="M806 694c1-5 4-9 8-12l2 1c-3 3-5 5-6 9l-1 2-1 4h0c-1-1-1-2-2-4z" class="a"></path><path d="M612 160c1-2 0-3 2-4 2 0 4 2 6 2 4 1 7 1 11 1l1 1 1 1c-4 1-10 1-13-1-2-1-4-2-5-2l-1 1-1 1h-1z" class="w"></path><path d="M669 717c3-2 7-4 11-7 0 2-1 2 0 3l-17 10-1-1c3-2 5-3 7-5z" class="a"></path><path d="M216 693h2c0 5 1 9-1 13 0 2-2 4-3 6l-1-2 3-7h0v-10z" class="r"></path><path d="M216 703v4h0l1-2v1c0 2-2 4-3 6l-1-2 3-7z" class="a"></path><path d="M190 226l24 3-4 1v1l-21-4 1-1z" class="z"></path><path d="M816 756l8-5c1 0 2-1 2 0h1l-17 13-1-1h0l2-2c3-2 4-3 5-5z" class="a"></path><path d="M729 730c2 0 4-1 6-1 3-1 6-5 9-4h1v1c-2 3-3 6-2 9h1c-1 1-2 1-2 2-1-5-1-7 1-11l-6 5h-1-4c-2 0-2 0-3-1z" class="b"></path><path d="M214 229l25 2v1h-8l-21-1v-1l4-1z"></path><path d="M232 152c2 2 4 3 5 5 0 2 0 3 1 4 1 2 1 4 1 6v8l-1 2 2 2h-1l-2-2v-3c1-9 0-13-6-20l1-2z" class="s"></path><path d="M837 679c5 1 10 5 12 10v1 5l-2-2c-1-3-2-5-4-7-1-2-3-3-4-4-2-1-4-2-7-2 2 0 3 0 5-1z" class="r"></path><path d="M331 801v3c0 4 0 10 4 13 3 3 6 5 11 4h1l2 2c-4 1-7 0-10-1-4-2-7-4-8-8-2-4-2-9 0-13z" class="X"></path><path d="M194 346h1c1 0 2 1 3 2l2 6c-1 6-4 11-8 17 0 1-1 2-1 3l-1-1c0-3 5-9 7-13 1-5 0-9-3-14z" class="h"></path><path d="M842 280v-1c-1-4-3-6-6-9v-1c5 0 11 6 15 8 1 2 4 3 6 4l-2 1-1 1c-5-3-9-9-14-12l3 6v1c0 1 0 1-1 2h0z" class="X"></path><path d="M332 780c5 0 9 1 13 3l-2 1h-4c-2 1-4 1-5 3l-1 3c-1 3-1 6 1 9h-1l-1-1c-1-2-2-6-1-9 1-2 2-5 4-6 1 0 1 0 2-1h-2c-2-1-2-1-3-2z" class="b"></path><path d="M267 648s1 0 1-1c2-2 3-2 6-2 1 0 3 1 4 2 5 4 8 7 9 13v2c-2 4-4 7-6 11l-1-1c2-3 3-5 4-8 2-3 1-7 0-10-2-3-5-6-8-7-3 0-4 0-6 2h-2 0l-1-1z" class="h"></path><path d="M293 774c7 0 15 3 23 5h0c1 1 2 1 2 1v1h0c-8-1-16-3-24-5l1-1v1h4c-2-1-4-1-6-1v-1z" class="K"></path><path d="M642 125v2c-1 3-2 5-2 8 3 1 5 2 8 4l1 2c1 4 1 9 0 13l-2 2-1-3 2-4c1-3-1-8-4-10-1-1-4-1-5-3 0-4 1-7 3-11z" class="s"></path><path d="M843 278v-1l-3-6c5 3 9 9 14 12h-1c-3 1-7 0-11 0 1-2 1-4 1-5z" class="c"></path><path d="M280 730l-1-1c0-2 0-3-1-5v-1c3 0 6 3 8 5 5 3 12 3 17 3l4 1c-4 1-9 2-13 1-1 0-6-2-6-2h-1v1c1 1 2 3 2 5-1-2-1-3-2-4-1-3-4-5-7-7 0 1 1 2 1 3l-1 1z" class="v"></path><path d="M837 389v1c0 3 2 6 2 9 2 7 4 15 3 23v1l7 4-1 1c-5-3-10-4-15-5 1-1 1-2 2-2v1h5l1-1c1-4 0-10-1-14-1-5-4-11-4-16 0-2 0-2 1-2z" class="K"></path><path d="M849 427c3 2 8 7 13 7 2 0 3-1 4-2s0-1 1-1c4 4 5 11 5 17 0 2 0 4-1 7l-1-1v-2c1-4 0-9 0-13-1-2-1-5-3-6 0 1 0 1-1 2-2 1-5 1-7 0h-1 0l-10-7 1-1z" class="z"></path><path d="M782 635l1 1c-1 1-1 2-2 2 1 1 0 1 1 0-6 6-8 19-9 27l-1-1c1-4 1-7 0-11l1-3c1-4 3-8 6-12l3-3z" class="a"></path><path d="M868 459l2-5 1 1c-1 5-3 10-6 14-5 6-12 10-15 19h0l-1-1c1-5 3-8 7-12 5-4 10-9 12-16z" class="m"></path><path d="M735 778l12 2c2-1 3 0 6 0 0 0 1 1 2 1l3 1-1 1h10v1c-9 2-22-2-31-4l-1-2z" class="h"></path><path d="M747 780c2-1 3 0 6 0 0 0 1 1 2 1l3 1-1 1c-3-1-7-1-10-3z" class="I"></path><path d="M273 778h0c3-2 6-2 9-2 2 0 6-1 8-1h5l-1 1c-5 0-11 1-15 2-3 1-6 3-9 4-5 2-14 3-18 2 1-1 3-1 5-1h0c1-1 2-1 3-1h6c2-1 6-3 7-4z" class="r"></path><defs><linearGradient id="A" x1="257.043" y1="666.049" x2="245.457" y2="658.451" xlink:href="#B"><stop offset="0" stop-color="#22241f"></stop><stop offset="1" stop-color="#41353b"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M251 647v-1h1l1 24v9l-1-1-1 2c0-9 0-17-2-25 2-3 2-4 2-8z"></path><path d="M204 367v1c-2 2-7 4-9 6 1 0 1 0 2-1 1 0 2-1 3-2h1l1-1 1 1c-3 2-7 3-9 5-2 1-7 4-9 4-2-1-8-1-10 0l-1 1c0 1 0 2 1 3 2 2 7 2 10 4l-1 1c-3-2-7-2-10-4-1-1-2-2-3-4 1-2 1-2 2-3h10c3 1 6 0 8-2l1-1c2-2 6-4 9-6l3-2z" class="V"></path><path d="M320 729c14-2 30-3 44-2v2h-5c-12-1-25 0-38 2 0-1-1-1-1-2z" class="z"></path><path d="M770 654l2-1c1 4 1 7 0 11l1 1v12 8h-1l-1 2-1-26v-7z" class="X"></path><path d="M770 654l2-1c1 4 1 7 0 11v5c-1-1-1-6-1-7l-1-1v-7z" class="q"></path><path d="M843 278c0 1 0 3-1 5s-4 5-6 7l-2 1-9 4c-1-1-4-1-5-2h0c3-1 7-1 10-2-2-1-3-3-4-4s-1-2-2-2v-3c0-1 1-2 1-3h0 1c-1 2-1 3-1 5 1 2 2 4 4 4 3 1 5 0 7-2 2-1 4-4 5-6h1 0c1-1 1-1 1-2z" class="V"></path><path d="M279 735l1 1 2-1c-2 5-4 8-8 10l-9 4c-4 1-9 1-14 1l2-1c3-1 7-1 10-2 6-2 13-5 16-12z" class="a"></path><path d="M117 308l1 2-1 3-1 4c0 5 1 10 4 14s6 6 10 7l3 1c4 1 6-1 9-3l1 1c-1 1-2 2-3 2-3 2-7 2-11 1-5-2-9-6-12-11-3-7-3-14 0-21z" class="X"></path><path d="M189 678c3-2 7-2 11-1v1h2c-1 1 0 0-1 0l-3 1h-2-5c-4 2-8 4-11 8s-5 7-6 11c-1 1-1 0-2 0 0-2 1-5 2-7 3-7 8-11 15-13z" class="h"></path><path d="M189 678c3-2 7-2 11-1v1h2c-1 1 0 0-1 0l-3 1h-2-5c-1 0-3 0-4 1h0-1c-1 1 0 1-1 1l3-3h1z" class="X"></path><defs><linearGradient id="C" x1="172.338" y1="702.87" x2="181.145" y2="689.919" xlink:href="#B"><stop offset="0" stop-color="#a19e9f"></stop><stop offset="1" stop-color="#d0cfce"></stop></linearGradient></defs><path fill="url(#C)" d="M180 687c1 1 2 1 4 1v2c-1 1-1 1-2 3l1 1c-1 1-2 1-3 2-1 2-2 5-3 7-1 1-2 2-4 2l1-7c1-4 3-7 6-11z"></path><path d="M804 767c2-2 3-3 5-4l1 1-6 6c-1 2-5 8-7 8 0 1-2 1-3 1h-5c-7 2-15 5-22 5v-1c7-2 15-3 22-6 7-1 9-3 13-8l2-2z" class="b"></path><path d="M172 169c2 4 3 8 2 13 0 1-1 2-1 3l-3 3c-2 2-5 2-8 2-3-1-5-2-7-5-2-2-1-6 0-8v2l2 1v1c0 1-1 1-1 2 1 1 0 1 1 1v1c2 2 4 3 7 3 3-1 5-3 7-5l1-1c1-4 0-8-2-12l2-1z" class="z"></path><path d="M196 753l10 7c1 0 2 1 4 2 1 1 17 8 17 9v1c1 0 2 1 3 2h-1-2c-1 0-4-2-6-3-8-4-16-8-23-13l-4-4 2-1z" class="m"></path><path d="M336 705l19 11c3 2 6 4 9 5 1 0 2 1 2 1 2 2 2 2 4 3 1 0 2 1 3 1l-1 2c-2-1-5-3-7-4-10-5-21-11-30-17v-1h0l1-1z" class="b"></path><path d="M127 328c1 3 3 6 7 7 1 0 2-1 3-1 2-2 3-4 2-6 0-2 0-4-2-5s-3-2-4-4c0-2 0-3 1-4s2-1 3 0a10.85 10.85 0 0 1 8 8c2 4 1 9-1 12 0 1-1 2-1 2l-1-1c2-2 3-4 3-7s-1-7-3-10c-1-1-4-3-6-3-1 1-1 1-1 3 1 0 2 1 3 2 2 2 3 5 3 8 0 2-1 4-2 6-1 1-3 2-4 1-4 0-7-2-9-5v-1l1-2z"></path><path d="M697 699c4-1 8-7 12-7-9 7-19 16-29 21-1-1 0-1 0-3h0v-3l3-2h1l7-5 1 1c2-2 3-2 5-2z" class="X"></path><path d="M684 705h2c1-1 2-1 4-1-3 2-7 5-10 6v-3l3-2h1z" class="i"></path><path d="M691 700l1 1c2-2 3-2 5-2l-7 5c-2 0-3 0-4 1h-2l7-5z" class="M"></path><defs><linearGradient id="D" x1="292.444" y1="770.588" x2="316.769" y2="778.236" xlink:href="#B"><stop offset="0" stop-color="#a2a0a1"></stop><stop offset="1" stop-color="#c8c8c8"></stop></linearGradient></defs><path fill="url(#D)" d="M308 771s0 1 1 1l3 2c1 0 1 1 2 1 2 1 5 2 6 3h2v1h-4-2c-8-2-16-5-23-5l-4-1c1-1 3-1 5-2h0c4 0 10 1 14 0z"></path><path d="M308 771s0 1 1 1l3 2c-2 0-4-1-6-1l-11-1-1-1c4 0 10 1 14 0z" class="B"></path><path d="M858 435h1c2 1 5 1 7 0 1-1 1-1 1-2 2 1 2 4 3 6 0 4 1 9 0 13v2l-2 5v-2c-1-1-1-2-1-4-1-1 0-1 0-2 0-3 0-7-1-10l-2-2c0 1 0 1 1 2v4c-1-3-2-5-5-7h0c-1-1-1-1-2-3z" class="c"></path><path d="M867 451l1-1v-4h1c0 2 0 4 1 6v2l-2 5v-2c-1-1-1-2-1-4-1-1 0-1 0-2z" class="H"></path><path d="M802 148c2 0 2 0 3 2-2 1-5 0-8 1-5 2-11 9-12 14 0 3 0 6-1 9v3c0 1-2 3-3 4-3 4-9 12-6 17 2 2 9 6 9 6 0 2-2 4-2 5h-1v-1h-1 0c0-1 1-2 2-3v-1l-5-2c-2-2-4-4-4-7-1-6 7-13 9-18 1-2 1-5 1-7 1-6 2-11 5-15 4-3 8-6 13-6 1 0 0 0 1-1z" class="m"></path><defs><linearGradient id="E" x1="749.591" y1="748.521" x2="773.397" y2="734.285" xlink:href="#B"><stop offset="0" stop-color="#3e2421"></stop><stop offset="1" stop-color="#372e2d"></stop></linearGradient></defs><path fill="url(#E)" d="M744 735c7 8 18 11 28 12l7-1c1 1 2 1 3 2-3 0-5 1-8 1h-5c-4 0-8-1-12-2-5-2-13-5-15-10 0-1 1-1 2-2z"></path><path d="M454 149c2 3 4 7 4 10-1 3 0 9 1 12-1 1-1 1-2 1l-4-2h-2c-3-1-5 0-7-1 2-1 2-1 4-1l3-3c2-1 2-2 2-5l1-4v-7z" class="j"></path><path d="M117 308c2-6 5-11 10-15 11-10 25-12 40-11 1 1 1 1 3 1l-2 1c-7 0-13-1-19 1h-1c-11 3-21 8-27 18l-3 6v1l-1-2z" class="h"></path><path d="M169 429v1l-7 6c-2 0-4 0-6-1l-2-2c-3 3-3 10-3 14 0 8 2 16 8 22 2 2 4 3 7 5l1 1 1 2v1 2l-2-1c-5-6-11-10-14-18s-4-19 0-27c0-1 1-2 2-2l4 2c4 0 7-2 11-5z" class="a"></path><path d="M126 331c2 3 5 5 9 5 1 1 3 0 4-1 1-2 2-4 2-6 0-3-1-6-3-8-1-1-2-2-3-2 0-2 0-2 1-3 2 0 5 2 6 3 2 3 3 7 3 10s-1 5-3 7c-3 2-5 4-9 3l-3-1c-1-2-3-4-4-6v-1z" class="c"></path><path d="M280 672l1 1c-6 7-15 17-14 27 1 3 3 6 5 8 2 1 5 2 8 1h0c-3-1-6-2-8-6-2-3-1-8-1-10l3-6c4-3 8-5 12-6 1 0 2 1 2 1l-1 1c-4 0-9 2-12 6-2 4-3 7-2 12 0 5 5 5 9 8 0 1-1 1-2 1-4 1-7 0-10-2-2-2-4-5-4-9-1-10 8-19 14-27z" class="X"></path><path d="M168 516h1c-1 8-1 17 5 24 3 4 7 6 12 7h6c-7-2-11-4-15-11l-1-3c-2-5-2-9-2-13h2c0 4 0 7 1 11 3 9 9 12 17 16-3 1-5 1-8 1-5 0-9-2-12-5-7-8-7-16-6-27z" class="z"></path><path d="M836 717l2 1c-4 3-7 5-11 6-12 6-22 7-34 6-3-1-9-3-11-2v1l1 1 1 1v1c2 0 4 1 6 3v1c-3-2-7-2-9-5-1-1 0-2 0-4 1 0 1-1 2-1s4 1 6 1c2 1 5 1 7 2 12 1 21-2 31-6-3 0-6-1-9-3h0l2-1c2 0 4 1 6 1 2 1 4 1 6 1l4-4z" class="m"></path><path d="M237 602c0 1 2 11 2 11 0 7 1 16-2 22 0 1 0 2 1 3l1-1h1c3 1 4 3 6 5 0 2 1 3 2 5 0 1 0 2 2 3v-5l1 2c0 4 0 5-2 8l-1-3c-1-3-3-12-7-13-3-2-8 6-10 8h-2v-2c3-3 5-8 6-12 3-8 2-15 2-23h0l-1-8h1z" class="Y"></path><path d="M237 602c0 1 2 11 2 11 0 7 1 16-2 22l-3 7c1-4 2-7 3-11v-1c2-6 1-14 0-20l-1-8h1z" class="c"></path><path d="M849 690c5 8 4 17 2 26-4 14-12 27-24 35h-1c0-1-1 0-2 0 5-3 10-7 13-11 5-7 8-14 10-22 2-4 3-8 3-13 0-3 0-7-1-10v-5z" class="m"></path><defs><linearGradient id="F" x1="266.715" y1="753.383" x2="284.59" y2="730.346" xlink:href="#B"><stop offset="0" stop-color="#c1c0c0"></stop><stop offset="1" stop-color="#f4f2f2"></stop></linearGradient></defs><path fill="url(#F)" d="M281 729c0-1-1-2-1-3 3 2 6 4 7 7v1c-2 5-3 10-9 13-6 4-15 5-22 6h-4v-1c1 1 1 1 2 1h1 2c2-1 0-1 1-1h3l1-1h1v-1c1 0 1 0 2-1l9-4c4-2 6-5 8-10l-2 1-1-1 1-5 1-1z"></path><path d="M281 729c0 2 1 4 1 6l-2 1-1-1 1-5 1-1z" class="m"></path><path d="M695 797l1 1c-1 2-1 4 0 6 1 4 1 8 0 11-2 4-5 7-8 8l-2 1h-1c-6 1-13-2-18-5h0l2-1 6 3h1c3 1 7 1 10 1l-3-2 3-1v-1l-1-1 1-1-1-1c1 0 1 0 2-1 3-3 4-5 7-9v-1l-1-2c1-1 1-3 2-5z" class="X"></path><defs><linearGradient id="G" x1="694.76" y1="808.293" x2="690.426" y2="812.445" xlink:href="#B"><stop offset="0" stop-color="#706d6e"></stop><stop offset="1" stop-color="#858383"></stop></linearGradient></defs><path fill="url(#G)" d="M694 804c1 2 2 8 0 11l-1 2v-3c-1 0-2 0-3-1s-1 1-2 1h-1c3-3 4-5 7-9v-1z"></path><path d="M687 814h1c1 0 1-2 2-1s2 1 3 1v3c-2 2-4 4-7 5l-3-2 3-1v-1l-1-1 1-1-1-1c1 0 1 0 2-1z" class="W"></path><path d="M210 762c5-1 11 5 16 7 3 1 6 4 8 4 4 1 8 3 12 4 2 1 4 2 6 2 3 1 5 2 8 3-1 0-2 0-3 1h0c-2 0-4 0-5 1-9 0-17-6-25-10h2 1c-1-1-2-2-3-2v-1c0-1-16-8-17-9z" class="B"></path><path d="M234 773c4 1 8 3 12 4 2 1 4 2 6 2 3 1 5 2 8 3-1 0-2 0-3 1-4-1-8-1-11-2v-1l-9-5c-1 0-2-1-3-2z" class="g"></path><path d="M227 771c6 3 12 8 19 10 3 1 7 1 11 2h0c-2 0-4 0-5 1-9 0-17-6-25-10h2 1c-1-1-2-2-3-2v-1z" class="a"></path><path d="M860 438c3 2 4 4 5 7v-4c-1-1-1-1-1-2l2 2c1 3 1 7 1 10 0 1-1 1 0 2 0 2 0 3 1 4v2c-2 7-7 12-12 16l-1-1-1 1h0c-3 0-6 0-9-1-2 0-3-1-4-3 3 1 4 2 7 2s5-1 7-2c4-3 7-7 8-12v-1c2-7 1-14-3-20z" class="V"></path><path d="M865 445v-4c-1-1-1-1-1-2l2 2c1 3 1 7 1 10 0 1-1 1 0 2 0 2 0 3 1 4v2c-2 7-7 12-12 16l-1-1 4-4c3-4 6-11 6-17 1-2 0-5 0-8z" class="S"></path><path d="M166 474c-3-2-5-3-7-5-6-6-8-14-8-22 0-4 0-11 3-14l2 2c2 1 4 1 6 1-1 0-1 2-2 2-3 2-4 7-5 11 0 7 2 13 6 18 1 3 3 5 5 6v1z" class="E"></path><path d="M213 710l1 2c-4 6-10 9-17 11 8 3 15 6 24 6 2 0 10-2 12-1s5 5 5 7c1 1 0 2-1 4 1 2 3 5 5 6 3 2 7 3 11 4l-2 1c-5-1-9-3-13-7l-2-2v-4l1-1v-1l-1-1v1h-1s1-1 1-2c-1 0-3-3-3-3-1-1-2 0-2 0l-8 1c-10 1-20-3-29-7l-3-1c-1-1-3 0-5-1v-1h1c2 0 5 1 7 1l5-1 4-2 2-1a30.44 30.44 0 0 0 8-8z" class="m"></path><path d="M200 677c2 0 4 1 6 2v2c5 4 8 7 10 12v10c-3 0-3-2-5-3l-1-3c-1-2-1-3-2-5h0c0-2-1-3-2-4s-2-3-3-4h-1-1l-1-1c-1-1-1-1-3-1 0-1-1-1-2-1-1-1-1-1-2 0l-1-1c1 0 2 0 4-1h0 2l3-1c1 0 0 1 1 0h-2v-1z" class="k"></path><path d="M200 677c2 0 4 1 6 2v2c-2-1-5-2-8-2l3-1c1 0 0 1 1 0h-2v-1z" class="w"></path><path d="M849 487l1 1v12c2 10 7 20 4 31l-1 1c-1 6-4 11-10 14-3 2-8 3-12 2 0 0-1-1-2-1 1-2 5-3 7-4 8-6 11-16 9-26l-1-4h1v2l1 1v-3h0c2 5 2 9 2 14-2 5-4 8-6 12h0c-3 3-6 7-10 8h0 4c5 0 9-3 12-6 5-7 6-16 5-24l-3-13c-2-5-2-11-1-17z" class="z"></path><path d="M167 282c6-2 10-8 16-11 2-1 5-2 7-2-2 3-7 3-8 7-1 2 0 4 1 6s4 5 7 5c2 0 3 0 5-1 1-2 2-4 2-7l-1-1 4-1-1 2 3 1v1 1c-1 1-1 2-1 3l-1 1-4 3c-2 1-3 1-4 1l-3-1-1-1-1-1h-3 0c-3-3-3-5-4-9-1 1-2 4-5 5h-3-2 0c-2 0-2 0-3-1z" class="m"></path><path d="M170 283c2-1 3-2 5-4 2-1 5-6 7-6-1 1-1 2-2 4v1c-1 1-2 4-5 5h-3-2z" class="c"></path><path d="M196 278l4-1-1 2 3 1v1 1c-1 1-1 2-1 3l-1 1-4 3c-2 1-3 1-4 1l-3-1-1-1h4c1 0 3-1 4-2 2-2 2-4 2-6l-1-1-1-1z" class="B"></path><path d="M208 692c1 2 1 3 2 5l1 3c2 1 2 3 5 3h0l-3 7a30.44 30.44 0 0 1-8 8l-2 1-4 2c-1-1-2-2-3-2-2-1-2-2-3-4 2 0 4 0 7-1l2-2 4-4c4-6 3-10 2-16z" class="S"></path><path d="M209 706c0 1 0 2-1 3 0 2-1 3-2 3l1-2h-1c0-2 2-3 3-4z" class="P"></path><path d="M202 712h0c-1 2-3 3-5 4l1 1h1l1 1 3 1-4 2c-1-1-2-2-3-2-2-1-2-2-3-4 2 0 4 0 7-1l2-2z" class="C"></path><defs><linearGradient id="H" x1="199.735" y1="714.975" x2="214.072" y2="703.087" xlink:href="#B"><stop offset="0" stop-color="#c1bfbf"></stop><stop offset="1" stop-color="#eeedec"></stop></linearGradient></defs><path fill="url(#H)" d="M210 697l1 3c2 1 2 3 5 3h0l-3 7a30.44 30.44 0 0 1-8 8l-4-1 5-5c1 0 2-1 2-3 1-1 1-2 1-3 1-3 1-6 1-9z"></path><path d="M287 662v-4l1 1v1c0 1 0 1 1 2v2c-2 6-6 11-10 15-2 3-4 5-5 8l-3 6c0 2-1 7 1 10 2 4 5 5 8 6h0c-3 1-6 0-8-1-2-2-4-5-5-8-1-10 8-20 14-27 2-4 4-7 6-11z" class="T"></path><path d="M808 698l1-4c0 3 0 5 3 8h1c0 2 1 4 1 6v1c1 1 2 2 4 2 1 0 1 1 2 1 2 2 3 3 7 4 0 0 1 1 2 1v1h2 0l1 1h-1v1l1 1c-2 0-4 0-6-1-2 0-4-1-6-1l-2 1c-3-2-6-4-8-7v-1c-2-5-3-8-3-13l1-1h0z" class="C"></path><path d="M808 698h0c1 6 2 10 5 15l-3-1c-2-5-3-8-3-13l1-1z" class="y"></path><path d="M810 712l3 1c3 2 5 4 7 6l-2 1c-3-2-6-4-8-7v-1z" class="a"></path><path d="M814 709c1 1 2 2 4 2 1 0 1 1 2 1 2 2 3 3 7 4 0 0 1 1 2 1v1h2 0l1 1h-1v1l1 1c-2 0-4 0-6-1h1c-1-1 0-1-1-1s-2 0-2-1h-1c-3-1-5-3-7-5-2-1-2-3-2-4z" class="I"></path><path d="M820 712c2 2 3 3 7 4 0 0 1 1 2 1v1h2 0l1 1h-1v1l-9-3c-1-1-2-2-2-3v-2z" class="B"></path><defs><linearGradient id="I" x1="774.801" y1="774.154" x2="779.914" y2="782.386" xlink:href="#B"><stop offset="0" stop-color="#848182"></stop><stop offset="1" stop-color="#a0a0a1"></stop></linearGradient></defs><path fill="url(#I)" d="M804 765v2l-2 2c-4 5-6 7-13 8-7 3-15 4-22 6h-10l1-1-3-1h8l6-3h0c3-2 6-2 8-3h1c2 1 3 1 5 0 7-2 15-5 21-10z"></path><path d="M789 777h0c0-1 1-1 2-1h0c0-1 0 0-1-1h0c3-1 5-4 9-5l1-1 1-1 1 1c-4 5-6 7-13 8z" class="C"></path><path d="M124 307s1 0 2-1h0c1-3 4-5 7-5-3 2-6 4-7 7h1c1-2 3-3 5-4v-1c1-1 3 0 5 0-5 2-10 4-12 9-1 2-2 3-2 5-1 5 0 8 2 13h1v1 1c1 2 3 4 4 6-4-1-7-3-10-7s-4-9-4-14l1-4c1 1 1 1 2 0 1-2 3-4 5-6z" class="G"></path><path d="M117 313c1 1 1 1 2 0-1 2-1 3-1 5h-1l-1-1 1-4z" class="T"></path><path d="M120 331c1 0 1-1 1-1 0-1-2-2 0-3 0 1 1 1 1 2 1 2 2 2 4 3 1 2 3 4 4 6-4-1-7-3-10-7z" class="j"></path><defs><linearGradient id="J" x1="383.356" y1="803.611" x2="383.617" y2="789.822" xlink:href="#B"><stop offset="0" stop-color="#272727"></stop><stop offset="1" stop-color="#483d3e"></stop></linearGradient></defs><path fill="url(#J)" d="M388 786l2-2c2 1 3 1 4 1s0 0 2 1h3 2c0 1 0 1 1 2-2 0-3 1-5 1l-1 1c-4 2-8 5-11 8-2 2-3 5-5 6-1 1-3 1-3 0l-4-1h0-1l1-2h3c0-2 1-3 2-5l1-1c1-1 2-2 3-2 1-3 3-5 6-7z"></path><path d="M391 788v2c-4 3-10 11-15 11 0-2 1-3 2-5l1-1c1-1 2-2 3-2h0 0l1-1c1-1 3-2 4-2 2 0 3-1 4-2z" class="J"></path><path d="M388 786l2-2c2 1 3 1 4 1s0 0 2 1h3 2c0 1 0 1 1 2-2 0-3 1-5 1h-3v-2c-2 0-2 0-3 1s-2 2-4 2c-1 0-3 1-4 2l-1 1h0 0c1-3 3-5 6-7z" class="o"></path><path d="M583 845v-1h0c-4 0-6-2-8-5-4-5-5-12-4-18 1-8 3-14 6-21l2 2c-3 5-5 11-4 17 1 3 3 5 5 7 3 2 7 1 10 1v1c-1 1-3 2-5 2-4 1-7-1-9-3-1-1-2-2-3-2-1 2 0 6 1 8 2 6 6 8 11 11v1c-1 0-2 1-3 1l1-1z" class="X"></path><defs><linearGradient id="K" x1="830.392" y1="529.529" x2="854.781" y2="520.964" xlink:href="#B"><stop offset="0" stop-color="#c2c2c1"></stop><stop offset="1" stop-color="#ebeae9"></stop></linearGradient></defs><path fill="url(#K)" d="M848 504h2l3 13c1 8 0 17-5 24-3 3-7 6-12 6h-4 0c4-1 7-5 10-8h0c2-4 4-7 6-12l1-3c0-4 2-10 0-14 0-2-1-4-1-6z"></path><g class="L"><path d="M848 527l1-3c0 5 0 12-4 14-1 1-2 1-3 1 2-4 4-7 6-12z"></path><path d="M828 482h2l2 2 3 4v-1h1c-1-2-1-3-1-4v-1c3 4 5 8 8 13 0-3-2-5-3-8h2 1c1 2 2 4 2 6l3 11c0 2 1 4 1 6 2 4 0 10 0 14l-1 3c0-5 0-9-2-14 0-4-2-7-4-10 0 0-3-5-3-6-3-5-7-10-11-15z"></path></g><path d="M842 487h1c1 2 2 4 2 6l3 11c0 2 1 4 1 6h-1v-2c0-1-1-1-1-2-1-4-3-8-4-11 0-3-2-5-3-8h2z" class="J"></path><path d="M842 503c3 1 4 4 5 7v1c0-2 0-2-1-4 0 0 0-1 1-1 0 1 1 1 1 2v2h1c2 4 0 10 0 14l-1 3c0-5 0-9-2-14 0-4-2-7-4-10z" class="E"></path><path d="M771 687l1-2h1c0 4 1 9 2 13 2 9 5 19 5 28-3-4-5-9-8-14-2-7-5-14-7-22h1v-1c1 1 1 1 1 2 1 2 1 4 2 5 0-1-1-2 0-3v-2h0 3l-1-4z"></path><path d="M766 689c1 1 1 1 1 2 1 2 1 4 2 5 0-1-1-2 0-3v-2h0 3c1 10 4 21 7 31-6-10-10-21-13-32v-1z" class="D"></path><path d="M311 769c3 4 6 6 11 6l3 1c1 0 2-1 3-1h-2l1-1-1-2h1l11 1c0 1 1 1 2 1h1l7 2 2 2 1 1c-1 1-4 0-6 0l-3 1 5 1v1c-1 1-1 0-1 1h-1c-4-2-8-3-13-3 0-1-2 0-3 0-4 0-8 0-11-1h4v-1h-2c-1-1-4-2-6-3-1 0-1-1-2-1l-3-2c1-1 2-2 2-3z" class="n"></path><path d="M345 779c-1 0-2-1-3-1l1-1 7 1 1 1c-1 1-4 0-6 0z" class="p"></path><path d="M322 779h11c3-1 6-1 9 1l5 1v1c-1 1-1 0-1 1h-1c-4-2-8-3-13-3 0-1-2 0-3 0-4 0-8 0-11-1h4z" class="B"></path><path d="M176 715v1l1 1c2 4 6 9 8 13 1 2 3 4 5 5l2 1c0 1 2 2 2 2l1 2 6 5c2 2 4 3 5 4l-1 1c1 3 3 4 5 5-1 1-2 1-4 1l-7-5c0-1-1-2-2-3-2-1-3-2-6-3-2-5-6-8-8-12-4-5-6-9-8-14 1-1 1-3 1-4z" class="X"></path><path d="M190 735l2 1c0 1 2 2 2 2l1 2-2 1c-1-2-2-3-4-5l1-1z" class="N"></path><path d="M195 740l6 5c2 2 4 3 5 4l-1 1-2-1c-4-2-7-5-10-8l2-1z" class="l"></path><path d="M176 716l1 1c2 4 6 9 8 13 1 2 3 4 5 5l-1 1c-2 0-3-1-4-2-4-4-9-12-9-18z" class="U"></path><path d="M749 644h2c4 2 5 3 6 7 1 2 1 3 3 5 1 8 2 16 4 24 0 3 1 7 2 9v1h-1l-6-29c-1-7-3-11-9-15-3 1-7 3-9 6-4 4-4 12-1 17 2 2 4 5 6 7 5 6 10 14 11 21 0 4-1 8-4 10-2 2-6 4-8 4-2-1-2-1-3-2v-1c2-1 5-1 7-3 1-3 2-6 1-9 0-5-3-8-7-11l1-1c3 2 5 5 7 8 1 4 2 9 0 12-1 3-3 3-5 4l-1 1h1c2 0 4-1 6-3s3-5 3-8c1-13-12-20-17-31-1-3-2-6-2-9 1-5 3-7 7-11l6-3zm65-177l1-1c0 1 1 2 2 3s1 1 3 2l8 11c4 5 8 10 11 15 0 1 3 6 3 6 2 3 4 6 4 10h0v3l-1-1v-2h-1l-4-9c-2-4-6-10-10-13-1 0-2 0-3 1-3 1-6 4-7 7h0c-1 3-1 7-1 10-1 1-1 1-1 2-1-4-1-9 0-12l3-6c2-2 5-4 7-4h1l-2-3-5-6-1-2-1 2h-1v-1-1c-1-2-3-3-4-4l-2-3-1-1-1-3c1 1 2 2 4 2l-1-2z" class="b"></path><path d="M811 467c1 1 2 2 4 2l6 9-1 2h-1v-1-1c-1-2-3-3-4-4l-2-3-1-1-1-3z" class="g"></path><path d="M262 666l1-1c-2 18-6 37-13 54-1 3-2 7-5 9 0 1-1 1-1 1l-1-1c0-8 4-17 5-25 2-8 2-16 3-23l1-2 1 1-2 15h1l1 1h0v8l-1 1c0 1 0 1 1 2 4-12 8-27 9-40z" class="z"></path><path d="M251 694h1l1 1h0v8l-1 1c0 1 0 1 1 2-1 8-6 16-9 23l7-35z" class="S"></path><defs><linearGradient id="L" x1="757.203" y1="663.555" x2="734.008" y2="687.686" xlink:href="#B"><stop offset="0" stop-color="#cecdcf"></stop><stop offset="1" stop-color="#f9f9f6"></stop></linearGradient></defs><path fill="url(#L)" d="M740 646c2-2 6-6 8-7l1 2h0v3l-6 3c-4 4-6 6-7 11 0 3 1 6 2 9 5 11 18 18 17 31 0 3-1 6-3 8s-4 3-6 3h-1l1-1c2-1 4-1 5-4 2-3 1-8 0-12-2-3-4-6-7-8h0 2 0l-1-3h0l-2-2c-1-2-2-3-4-4-3-5-5-8-6-14h0c-1-1-2-2-3-2l2-4c1-4 4-7 7-10l1 1z"></path><path d="M732 655c0 1 0 1 1 2v4h0c-1-1-2-2-3-2l2-4z" class="l"></path><path d="M743 647l1-2c1-2 3-3 5-4v3l-6 3z" class="G"></path><path d="M739 645l1 1c-3 3-5 7-7 11-1-1-1-1-1-2 1-4 4-7 7-10z" class="e"></path><path d="M727 103l-1-1c-1-3 1-7 3-10 3-4 9-8 14-9l2 2c-1 1-3 2-5 3-2 2-5 5-6 8h-1c-1 3 0 6 0 9l1 1c2 6 8 9 8 17v4c-1 1-2 2-4 3l-3 12c-1 5 1 11 4 15 4 7 10 10 17 13 3 1 6 3 9 4 2 0 5 1 7-1 2-1 3-3 4-5 4-10 6-16 16-20h1c2-1 6-2 8-1l1 1h-7c-10 1-14 10-17 18 0 2-1 6-3 7-1 2-3 3-5 3-3 0-7-2-11-3-6-3-13-5-18-11-7-7-10-20-6-29 1-2 4-4 5-7 1-1 1-6 1-7-1-2-2-4-3-5-4-4-7-9-7-15 0-7 7-11 12-15-4 2-8 4-11 6-2 3-5 7-5 11v2h0z" class="U"></path><path d="M403 780c2 0 3 1 5 2l1-1h0 2c1 1 1 1 3 1h0l4 1 4 2 2-1c2 1 3 1 4 0 3 2 6 5 8 9h-1v1h-2c-10-2-21-5-31-6-1-1-1-1-1-2h-2-3c-2-1-1-1-2-1s-2 0-4-1c3-1 5-2 8-2h0c1-1 3-1 4-1l1-1z" class="t"></path><path d="M403 780c2 0 3 1 5 2h1-11c1-1 3-1 4-1l1-1z" class="o"></path><path d="M428 784c3 2 6 5 8 9h-1c-3-3-9-5-13-8l2-1c2 1 3 1 4 0z" class="U"></path><path d="M360 105c0-4-1-7 0-11 0-1 0-3 1-4h1c1 3 1 8 2 12h0c1 4 3 7 6 9 4 5 9 9 11 15 1 3 1 7 1 11-3 1-7 2-8 5-1 4 1 8 3 11 1 2 3 4 5 5 4 2 12 0 16-1l3-1c1 0 2 2 3 2 4 4 9 9 12 14 2 3 3 5 3 9v1c1 1 1 1 1 2l-1 1 1 1v3c1 1 1 3 1 4l-3 1-4 1h0l-2 1v-2c1-1 1-1 1-2-1 0-2 1-2 1l3-4c1-2 2-5 2-7 1-5-2-10-5-14a30.44 30.44 0 0 0-8-8c0-1-2-3-2-3-1 0-5 2-6 2-5 1-10 1-15 0l-3-4-2-4c-2-3-3-7-2-9 2-4 4-5 7-6h1c0-4-1-7-2-10-4-9-12-14-16-22-1-3-1-6-2-9 0 4 0 8 1 12-1 0-1-1-2-2z" class="X"></path><path d="M418 185h1l1 1v3c1 1 1 3 1 4l-3 1-4 1c0-2 0-2 1-4 1-1 1-1 1-2 1-1 1-2 2-4z" class="Y"></path><path d="M418 185h1l1 1v3c1 1 1 3 1 4l-3 1c1-3 1-6 0-9z" class="U"></path><path d="M585 787c0 2-1 3-2 4l1 1c-3 3-5 5-7 8-3 7-5 13-6 21-1 6 0 13 4 18 2 3 4 5 8 5h0v1c-3 0-5 0-8-1-5-2-10-7-12-13 0-1-1-3-1-4-1-1 3-7 3-9 5-7 6-16 10-23 1 1 1 1 2 1 1-2 3-3 4-5 1-1 3-2 4-4z" class="N"></path><path d="M180 696l1 1c-1 3-2 5-2 8 1 2 1 4 2 6 2 2 4 4 7 4l3 1 2-1c1 2 1 3 3 4 1 0 2 1 3 2l-5 1c-2 0-5-1-7-1h-1v1c2 1 4 0 5 1v1c-1-1-2-1-3-1l-1 1c2 2 4 4 7 6 1 1 2 2 4 3l-1 1 2 2h0c-1-1-2-1-3-2h-1l1 1 4 4-4-3h-4l-2-1c-2-1-4-3-5-5-2-4-6-9-8-13l-1-1v-1c-1-3-2-6-3-10 2 0 3-1 4-2 1-2 2-5 3-7z" class="V"></path><path d="M173 705c2 0 3-1 4-2-1 4 0 7 1 11l1 1h-1l-1 2-1-1v-1c-1-3-2-6-3-10z" class="W"></path><path d="M181 711c2 2 4 4 7 4l3 1 2-1c1 2 1 3 3 4 1 0 2 1 3 2l-5 1c-2 0-5-1-7-1h-1v1c2 1 4 0 5 1v1c-1-1-2-1-3-1l-1 1c0-1-1-1-1-2l-1-1-4-4 1-1c1 1 2 3 3 3v-1c-1-2-2-2-3-4-1-1-1-2-1-3z" class="l"></path><path d="M187 721s0-1 1-1h1v-1c-1-1-2-1-2-2h0 1l1 1h2l1 1c0 1 1 2 2 3-2 0-5-1-7-1z" class="f"></path><path d="M191 716l2-1c1 2 1 3 3 4 1 0 2 1 3 2l-5 1c-1-1-2-2-2-3l-1-1h-2l-1-1 3-1h0z" class="F"></path><path d="M177 717l1-2h1c1 2 4 6 5 7 4 5 8 9 13 12l2 2h0c-1-1-2-1-3-2h-1l1 1 4 4-4-3h-4l-2-1c-2-1-4-3-5-5-2-4-6-9-8-13z" class="B"></path><path d="M184 722c4 5 8 9 13 12l2 2h0c-1-1-2-1-3-2h-1l1 1 4 4-4-3c-2-2-4-3-6-5s-6-5-6-9z" class="G"></path><path d="M642 782v-1l2 1c3 3 7 6 10 10 0 1 2 2 2 3l1 1 1 2 3 3 6 9c1 1 2 3 4 4h3l9 6 3 2c-3 0-7 0-10-1h-1l-6-3-2 1c-6-4-10-12-14-18l-1-1c-2-4-3-7-6-11l-2-2c-1-1-3-2-3-2l-3-2 2-2 2 1z" class="b"></path><path d="M642 782v-1l2 1c3 3 7 6 10 10 0 1 2 2 2 3l1 1 1 2c1 3 2 7 1 9-1-2-2-3-3-5-1-1-2-3-3-5-3-6-6-11-11-15z" class="C"></path><path d="M657 796l1 2c1 3 2 7 1 9-1-2-2-3-3-5 1 0 1 1 2 1v1-1c-1-2-1-5-1-7z" class="B"></path><path d="M658 798l3 3 6 9c1 1 2 3 4 4h3l9 6 3 2c-3 0-7 0-10-1h-1l-6-3c-3-3-6-5-8-8 0-1-1-2-2-3 1-2 0-6-1-9z" class="i"></path><path d="M658 798l3 3c0 3 0 5 2 7v1h-1 0v1h-1c0-1-1-2-2-3 1-2 0-6-1-9z" class="F"></path><path d="M191 745c3 1 4 2 6 3 1 1 2 2 2 3l7 5c2 0 3 0 4-1 3 2 6 4 9 3 6 3 11 6 17 8l2 1h0c1 1 2 1 4 1 0 1 1 1 2 1h1v1c-2-1-3-1-5-1v2h0 0l1 2c2 1 3 2 5 4-4-1-8-3-12-4-2 0-5-3-8-4-5-2-11-8-16-7-2-1-3-2-4-2l-10-7-2 1c-2-3-2-6-3-9z" class="Q"></path><path d="M191 745c3 1 4 2 6 3 1 1 2 2 2 3-1-1-3-2-4-2 0 1 0 3 1 4l-2 1c-2-3-2-6-3-9z" class="r"></path><path d="M210 755c3 2 6 4 9 3 6 3 11 6 17 8l2 1h0c1 1 2 1 4 1 0 1 1 1 2 1h1v1c-2-1-3-1-5-1v2h0 0l1 2c-1-1-2-1-3-2-3-2-7-3-11-4-1-1-3-2-5-3-5-3-11-5-16-8 2 0 3 0 4-1z" class="o"></path><path d="M203 272c-8-8-19-15-29-22-6-3-12-7-18-11-8-6-15-13-21-21-7-10-11-22-9-34 1-7 5-15 12-20 6-4 13-6 20-4-1 0-1 1-3 1-6 0-13 2-18 6-6 6-10 14-10 23 0 13 7 25 16 35 14 14 33 23 49 35 3 3 8 6 11 9l1 3h-1 0z"></path><path d="M362 107c-1-4-1-8-1-12 1 3 1 6 2 9 4 8 12 13 16 22 1 3 2 6 2 10h-1c-3 1-5 2-7 6-1 2 0 6 2 9l2 4h-3v-1h-3c-1 1-4 1-6 1 3-2 4-5 5-8-3-4-1-8-1-13 1-2 2-3 2-6l-1-2v-4s-2-4-3-4c-2-4-3-7-5-11z" class="T"></path><path d="M370 122c3 3 7 7 7 12-1 3-4 5-6 7 0 2-1 4-1 5v1h0c-3-4-1-8-1-13 1-2 2-3 2-6l-1-2v-4z" class="C"></path><defs><linearGradient id="M" x1="453.347" y1="806.93" x2="417.215" y2="801.845" xlink:href="#B"><stop offset="0" stop-color="#2a1b1a"></stop><stop offset="1" stop-color="#533f3e"></stop></linearGradient></defs><path fill="url(#M)" d="M422 781c0-1 0-1-1-1v-1-1c2 1 2 1 4 1h1 0c3-1 5 1 8 3h1v-1c2 2 3 3 5 4 4 2 6 11 8 15 0 4 3 6 3 10 1 2 2 5 2 7-1 2-1 5-1 7-2 2-3 4-6 5-2 1-5 2-8 1-1 0-2-1-3-3h1l2-1c1 0 3 1 4 0 3-1 5-3 6-5 2-3 1-7 0-11-2-7-9-12-15-16h2v-1h1c-2-4-5-7-8-9l-6-3z"></path><defs><linearGradient id="N" x1="448.535" y1="796.423" x2="428.515" y2="780.615" xlink:href="#B"><stop offset="0" stop-color="#5d585d"></stop><stop offset="1" stop-color="#8e8e8d"></stop></linearGradient></defs><path fill="url(#N)" d="M426 779c3-1 5 1 8 3h1v-1c2 2 3 3 5 4 4 2 6 11 8 15 0 4 3 6 3 10-1-2-2-5-3-7-5-9-13-19-22-24h0z"></path><path d="M237 601h1v-4c-1-3-1-5-1-7v-1h1v2c3 4 5 13 5 18 1 0 1 2 1 2h1c3 11 6 23 7 35h-1v1l-1-2v5c-2-1-2-2-2-3-1-2-2-3-2-5-2-2-3-4-6-5h-1l-1 1c-1-1-1-2-1-3 3-6 2-15 2-22 0 0-2-10-2-11v-1z" class="i"></path><path d="M238 591c3 4 5 13 5 18v7c0-3-1-5-2-7-1-5-2-12-3-18z" class="N"></path><path d="M239 613c2 3 1 5 2 9h0c1 1 1 2 1 3 1 4 1 8 2 12l-1 1c-1-2-1-2-3-3l-1 1 1 1h-1l-1 1c-1-1-1-2-1-3 3-6 2-15 2-22z" class="M"></path><path d="M243 609c1 0 1 2 1 2h1c3 11 6 23 7 35h-1v1l-1-2v5c-2-1-2-2-2-3l1-1c0-2 0-5-1-8l-3-18c-1-1-1-3-2-4v-7z" class="U"></path><path d="M243 609c1 0 1 2 1 2 1 3 1 6 1 9-1-1-1-3-2-4v-7z" class="l"></path><defs><linearGradient id="O" x1="444.498" y1="818.725" x2="464.565" y2="822.805" xlink:href="#B"><stop offset="0" stop-color="#493b3c"></stop><stop offset="1" stop-color="#675b5b"></stop></linearGradient></defs><path fill="url(#O)" d="M448 800c2 1 3 2 4 4v-1-2l4 9c2 4 10 17 8 22-2 4-4 6-7 8-3 3-6 4-11 5l-1 1c-1 0-2-1-3-1 0-1 0-1 1-2 5-6 8-11 9-19 0-2 0-5 1-7 0-2-1-5-2-7 0-4-3-6-3-10z"></path><path d="M452 836c2-1 4-3 5-5 1 1 0 1 0 2-1 2-2 5-1 7h1 0c-3 3-6 4-11 5h-2v-1c4-2 6-5 8-8z" class="e"></path><defs><linearGradient id="P" x1="446.413" y1="821.891" x2="452.654" y2="837.779" xlink:href="#B"><stop offset="0" stop-color="#151412"></stop><stop offset="1" stop-color="#2f2727"></stop></linearGradient></defs><path fill="url(#P)" d="M453 817c1 4 3 11 1 15l-2 4c-2 3-4 6-8 8v1h2l-1 1c-1 0-2-1-3-1 0-1 0-1 1-2 5-6 8-11 9-19 0-2 0-5 1-7z"></path><defs><linearGradient id="Q" x1="829.619" y1="736.873" x2="789.623" y2="715.881" xlink:href="#B"><stop offset="0" stop-color="#c6c6c5"></stop><stop offset="1" stop-color="#f9f5f6"></stop></linearGradient></defs><path fill="url(#Q)" d="M839 717l1 1c0 2-2 3-3 5 0 0-1 1-1 2-1 0 0 1 0 1-4 3-7 5-10 7l-9 3-7 3c-2 0-3 0-5 1-3 1-5 2-8 3h-1l-2-1c1-1 1-2 2-3 0-2-1-2-2-4l-1-1c-3-2-6-4-10-4l-1-1v-1c2-1 8 1 11 2 12 1 22 0 34-6 4-1 7-3 11-6l1-1z"></path><path d="M793 734c3 1 4 3 6 4h0c-2 1-3 2-3 4h0 2l1-1h2l1-1h3l-8 3h-1l-2-1c1-1 1-2 2-3 0-2-1-2-2-4l-1-1z" class="L"></path><path d="M818 511c0-1 0-1 1-2 1 9 2 20 9 26h2v1c-8 2-14 2-18 9-5 7-4 17-2 25 1 6 5 14 10 17 2 1 5 2 6 4-1 1-2 1-3 1-3 1-6-1-8-2-7-2-15-3-21 3-6 5-9 14-9 23-1 11 3 21 9 31h0c-3-1-4-3-6-4-2-2-4-3-6-5-1 1 0 1-1 0 1 0 1-1 2-2l-1-1h2s0-1-1-2c0 0 0-1-1-1v-1c-1-6-1-11 0-17v-5c0-2 2-7 2-10 1-2 2-5 3-7l2-2h0c-1 4-4 6-3 10 0 0 0-1 1-2 0-1 0-1 1-2 1-2 1-2 3-3h0c7-7 15-8 23-4 4 1 7 2 10 2 0-1 0-1-1-1-1-1-2-1-4-2-7-5-10-14-12-23-1-7 0-16 5-22 3-5 8-7 14-8h0c-6-7-7-15-8-24z" class="m"></path><path d="M789 590h0c-1 4-4 6-3 10 0 0 0-1 1-2 0-1 0-1 1-2 1-2 1-2 3-3-5 8-8 16-8 26v-5h-1v-5c0-2 2-7 2-10 1-2 2-5 3-7l2-2z" class="H"></path><path d="M782 614h1v5c1 9 4 16 7 24-1-2-5-5-6-8 0 0 0-1-1-2 0 0 0-1-1-1v-1c-1-6-1-11 0-17z" class="T"></path><path d="M694 794l1 3c-1 2-1 4-2 5l1 2v1c-3 4-4 6-7 9-1 1-1 1-2 1l1 1-1 1 1 1v1l-3 1-9-6h-3c-2-1-3-3-4-4l1-1c-1-3-1-5-2-7l2 1c0-1 0-2-1-3 2 0 2 0 4 1l6-3 2 1c0 1 1 2 1 3 2 0 3-1 4-1l-1-2 1-3c2 0 5 0 7-2h2 1z" class="I"></path><path d="M693 802l1 2v1c-3 4-4 6-7 9-1 1-1 1-2 1h-3c-5-2-8-7-10-12 2 1 4 5 6 7v-1-1h1v1l2 1c2 1 3 0 5-1l3-2c2-1 3-3 4-5z" class="l"></path><path d="M678 810v-1-1h1v1l2 1c2 1 3 0 5-1l1 1h2 0c-2 3-4 3-7 4l-4-4z" class="N"></path><path d="M694 794l1 3c-1 2-1 4-2 5-1 2-2 4-4 5h-3l-3-2c-2-1-2-1-3-3 2 0 3-1 4-1l-1-2 1-3c2 0 5 0 7-2h2 1z" class="B"></path><path d="M684 801l2 2h1 2c-2 2-4 2-6 2-2-1-2-1-3-3 2 0 3-1 4-1z" class="D"></path><path d="M694 794l1 3c-1 2-1 4-2 5-1 2-2 4-4 5h-3c1 0 2-1 4-1v-3l1-1-1-1 3-3v-4h1z" class="F"></path><path d="M684 796c2 0 5 0 7-2h2v4l-3 3c-1 1-2 2-4 2l-2-2-1-2 1-3z" class="I"></path><defs><linearGradient id="R" x1="685.836" y1="810.789" x2="668.147" y2="808.872" xlink:href="#B"><stop offset="0" stop-color="#9d9b9d"></stop><stop offset="1" stop-color="#c5c4c3"></stop></linearGradient></defs><path fill="url(#R)" d="M667 800c2 0 2 0 4 1 0 1 0 1 1 2 2 5 5 10 10 12h3l1 1-1 1 1 1v1l-3 1-9-6h-3c-2-1-3-3-4-4l1-1c-1-3-1-5-2-7l2 1c0-1 0-2-1-3z"></path><path d="M666 802l2 1c2 4 3 7 6 11h-3c-2-1-3-3-4-4l1-1c-1-3-1-5-2-7z" class="l"></path><path d="M118 310v-1l3-6c6-10 16-15 27-18h1c6-2 12-1 19-1l3 2h0c1 2 6 4 8 5l3 2-1 1h-1-4l-8-3h0l-4-1c-2 0-4 0-6 1-4 0-8 0-11 1l-9 3c2 1 3 1 6 1l-3 1c-2 0-4 1-6 2l-1 1-1 1c-3 0-6 2-7 5h0c-1 1-2 1-2 1-2 2-4 4-5 6-1 1-1 1-2 0l1-3z" class="c"></path><path d="M124 307c2-5 9-9 14-12 2 1 3 1 6 1l-3 1c-2 0-4 1-6 2l-1 1-1 1c-3 0-6 2-7 5h0c-1 1-2 1-2 1z" class="D"></path><path d="M147 292c1-1 1-1 3-1 1-1 2-1 3-2-1 0-3 1-5 0 5-1 12-1 18 0 1 0 1 0 2 2l-4-1c-2 0-4 0-6 1-4 0-8 0-11 1z" class="p"></path><path d="M455 145c6 5 14 14 22 13 3 0 5-1 7-3v-5c-1-3-4-5-5-8 1 0 2 1 3 2 3 2 6 3 9 2 2 0 5-1 6-3s1-3 1-5c0-1 0-6 1-7h0c2 1 4 1 6 0 3-4 3-10 8-13 1 0 1 0 2 1 2 3 3 8 6 12v1 1c2 0 1-2 3-2l1 1c0 1-1 1-2 2l-1 1c0 2 3 5 5 7 2 1 4 3 7 3 2-1 4-2 7-3v1c0 2-1 3-2 4h-2l3-3h0c-3 1-5 2-7 2-14-5-12-13-18-24l-1-1-1 1v2h0c-2 5-4 9-9 11 0 1-1 2-2 2s-1 0-1-1c-1 1-1 1-1 2l2 2c0 2 0 3-1 5v1c0 2-1 3-2 5l-1 2h0l3-2-1 2c-1 0-2 1-3 2l-5 4c-1 0-1 1-2 1s-2 1-3 2c-2 0-2 0-2-2 0-1 1-2 1-3l-1-1c-3 2-6 3-10 4-9-2-14-9-21-15h1z" class="X"></path><path d="M504 135h-1c-1 0-2-1-3-1v-2l1 1h3c5 0 6-11 9-14l1 2-1 1v2h0c-2 5-4 9-9 11z" class="L"></path><path d="M500 138l2 2c0 2 0 3-1 5v1c0 2-1 3-2 5l-1 2h0l3-2-1 2c-1 0-2 1-3 2l-5 4c-1 0-1 1-2 1s-2 1-3 2c-2 0-2 0-2-2 0-1 1-2 1-3l-1-1 1-3c0-1 0-3-1-5l-1-1h2c2 1 5 1 7 0 4-2 5-5 7-9z" class="G"></path><path d="M892 309v-2c2 2 4 4 6 7h1c1 1 1 1 1 2v1c0 2 0 4 1 5v-1c0-2 1-4 0-6v-1h0v-2c-1-1-1-1-1-3 1 1 2 2 2 4h0l1 3h0c1 3 1 9 3 10 1-6 1-11 0-16l1-2c2 7 2 14 0 20-3 6-7 10-13 12-4 1-8 1-11-1-3-1-5-4-6-7 0-4 0-8 2-11s5-4 8-5c2 0 2 1 3 2-1 5-11 7-6 14 1 2 3 2 5 3 2 0 3-1 4-3 3-2 5-6 5-9s-1-4-3-7h1l2 2v-1c-1-4-4-6-6-8z" class="X"></path><path d="M901 314c1 4 1 10-1 14-2 3-4 6-7 9v2h-2v-1-1h-3 0c4-1 7-4 9-8 3-5 3-10 1-15h1c1 1 1 1 1 2v1c0 2 0 4 1 5v-1c0-2 1-4 0-6v-1h0z" class="p"></path><path d="M891 339c-2 1-5 0-7-1s-4-3-5-5c-1-3-1-7 1-10 1-3 3-5 6-5 1-1 1 0 2 0-1 3-4 3-6 6-1 2-1 5 0 7 0 2 2 4 4 5l1 1h1 0 3v1 1z" class="c"></path><path d="M901 314v-2c-1-1-1-1-1-3 1 1 2 2 2 4h0l1 3h0c1 3 1 9 3 10-3 5-6 10-11 12l-2 1v-2c3-3 5-6 7-9 2-4 2-10 1-14z" class="D"></path><path d="M184 470c1 1 1 2 2 3-1 1-3 2-4 3h-4c1 1 2 2 3 2l-3 8c0 2-1 3-1 4l1 1-4 12c-1 2-1 3-2 4v1 6c-1 2-1 7 0 9s2 4 2 6c0 1 1 2 2 4l1 3c4 7 8 9 15 11h-6c-5-1-9-3-12-7-6-7-6-16-5-24h-1l3-14c2-9 0-15-5-23l2 1v-2-1l-1-2-1-1v-1l1 1h3c1 0 1 0 2-1h3v-1c3-1 7 1 9-2z" class="p"></path><path d="M171 476c2 0 5 1 7 0 1 1 2 2 3 2l-3 8v-4-2c-1-1-2-1-4-1-1-1-2-2-3-2v-1z" class="E"></path><path d="M168 477c6 8 6 16 4 26l-3 13h-1l3-14c2-9 0-15-5-23l2 1v-2-1z" class="K"></path><defs><linearGradient id="S" x1="182.417" y1="475.691" x2="171.583" y2="472.809" xlink:href="#B"><stop offset="0" stop-color="#362e2c"></stop><stop offset="1" stop-color="#4e4647"></stop></linearGradient></defs><path fill="url(#S)" d="M184 470c1 1 1 2 2 3-1 1-3 2-4 3h-4c-2 1-5 0-7 0l-4-1-1-1v-1l1 1h3c1 0 1 0 2-1h3v-1c3-1 7 1 9-2z"></path><path d="M187 724l1-1c1 0 2 0 3 1v-1l3 1c9 4 19 8 29 7l8-1s1-1 2 0c0 0 2 3 3 3 0 1-1 2-1 2h1v-1l1 1v1l-1 1v4l2 2-2 1h1-1c-2 1-22-4-25-6-1 0-1 1-2 1-1-1-2-1-3-1v1c-2-1-4-2-7-3l-2-2 1-1c-2-1-3-2-4-3-3-2-5-4-7-6z" class="k"></path><path d="M187 724l1-1c1 0 2 0 3 1v-1l3 1-1 1c1 2 3 3 5 5h-4c-3-2-5-4-7-6z" class="C"></path><path d="M194 730h4c3 2 6 4 9 4l1 1v1l3 2c-1 0-1 1-2 1-1-1-2-1-3-1v1c-2-1-4-2-7-3l-2-2 1-1c-2-1-3-2-4-3z" class="F"></path><path d="M207 734c2 0 2 0 3 1h3c8-1 15 1 22 0h1v-1l1 1v1l-1 1v4l2 2-2 1h1-1c-2 1-22-4-25-6l-3-2v-1l-1-1z" class="T"></path><path d="M219 739c2-1 4 0 5 0l2 1h2c1 0 1 0 2 1h2v-1l3 1h1l2 2-2 1c-5-2-12-3-17-5z" class="p"></path><path d="M208 735c4 2 8 3 11 4 5 2 12 3 17 5h1-1c-2 1-22-4-25-6l-3-2v-1z" class="i"></path><path d="M194 490c5 2 7 5 9 10h0c1 2 1 5 1 7v5c-1 8-2 15-6 21l-1 1c0 1-1 1-1 1h0c6 1 10 3 14 7 1 1 2 3 3 5s2 4 2 7c2 7 0 13-1 20l1 2c1-3 2-8 3-10 0 4 0 9-2 14v2 1c1-1 0-1 1-2l1-1v1l1-1c1 0 2-1 3 0 1 0 2 0 2 1l1 3c4 1 7 3 9 6s2 7 3 11v1h-1l1 8h0c-2-7-4-14-10-19-5-4-11-4-17-1l-9 3c-1 0-2-1-3-2 0-1 4-3 5-3 6-4 9-11 10-18l1-1c0-4 1-8 0-12v-1c-1-6-2-10-6-14-5-5-11-5-18-5 3-2 6-3 8-5 2-3 2-7 3-10 2-7 4-19 0-25-3-4-6-4-9-5v-2h2z" class="m"></path><defs><linearGradient id="T" x1="229.446" y1="586.233" x2="212.621" y2="599.387" xlink:href="#B"><stop offset="0" stop-color="#c1bfc0"></stop><stop offset="1" stop-color="#e9eae9"></stop></linearGradient></defs><path fill="url(#T)" d="M215 576c1-3 2-8 3-10 0 4 0 9-2 14v2 1c1-1 0-1 1-2l1-1v1l1-1c1 0 2-1 3 0 1 0 2 0 2 1l1 3c4 1 7 3 9 6s2 7 3 11v1h-1c-2-4-5-9-8-12-5-3-11-4-16-3-2 1-5 3-7 4-3 0-5 0-7 1 1-1 3-2 4-3 6-2 10-10 12-15l1 2z"></path><path d="M219 580c1 0 2-1 3 0 1 0 2 0 2 1l1 3c-1 0-2 0-2-1h-2-5 0c1-1 0-1 1-2l1-1v1l1-1z" class="D"></path><path d="M215 576c1-3 2-8 3-10 0 4 0 9-2 14v2 1h0l-6 2c2-3 4-6 5-9z" class="C"></path><path d="M863 312l2 1c-7 3-15 7-21 12-6 6-13 16-16 23-2 5-2 10 0 15 3 6 8 10 11 16 3-1 8-3 11 0 1 0 1 1 1 2 0 2 0 2-2 3-3 3-8 4-12 6v-1c4-3 9-3 12-7 0-1 0-1-1-3h-5c-6 1-14-3-19-5-4-4-7-5-11-8l2-1 9 7c1 1 3 1 4 2-1-3-3-4-5-6-1-2-3-4-4-5 1-1 2-1 3-1l-2-2c0-1 0-3-1-4l1-1h1v-1-1l2 1v-2c0-3 1-5 2-7v-3h0l-1-1s1-1 1-2c1-1 1-2 1-4l8-7 2 1c5-5 11-9 17-13v1l10-5z" class="a"></path><path d="M825 345l3-3h0c-1 4-3 8-3 12-1 3 0 6 1 9-1-1-1-1-2-1-2-2-3-4-4-7h1v-1-1l2 1v-2c0-3 1-5 2-7z" class="G"></path><path d="M819 356l1-1c1 3 2 5 4 7 1 0 1 0 2 1 3 5 8 9 11 15l-9-4c-1-3-3-4-5-6-1-2-3-4-4-5 1-1 2-1 3-1l-2-2c0-1 0-3-1-4z" class="T"></path><path d="M853 316v1c-5 3-10 6-14 11-4 3-7 8-9 12 0 0-1 2-2 2h0l-3 3v-3h0l-1-1s1-1 1-2c1-1 1-2 1-4l8-7 2 1c5-5 11-9 17-13z" class="R"></path><path d="M834 328l2 1c-3 4-9 8-11 13l-1-1s1-1 1-2c1-1 1-2 1-4l8-7z" class="Z"></path><path d="M202 475v1 1c2 3 3 6 6 9l1-1v-2l2-2h1c-1 2-1 4-1 6l2-3v5l-2 11c-2 4-2 6-2 10h1l1-1v1h0v6c0 2-1 3-1 5-1 4-3 10-7 12-2 1-4 2-6 1l1-1c4-6 5-13 6-21v-5c0-2 0-5-1-7h0c-2-5-4-8-9-10h-2v-1l7-9c1-2 2-4 3-5z" class="j"></path><path d="M206 514l1 4v1c0 1 0 4-1 5 0 1 0 1-1 1h0c0 2-1 3-1 4h-1l3-15z" class="B"></path><path d="M204 512l2 1v1l-3 15c-1 1-2 3-4 4h-1c4-6 5-13 6-21z" class="d"></path><path d="M209 494l1 1c0 3 0 7-2 9 0 1 0 2-1 3l-1 6-2-1v-5c0-2 0-5-1-7l1-1c1 0 1 1 2 0h1c0 1 0 2 1 2 0-2 1-4 1-6v-1z" class="E"></path><path d="M202 475v1 1c2 3 3 6 6 9l1-1v-2l2-2h1c-1 2-1 4-1 6l-2 7v1c0 2-1 4-1 6-1 0-1-1-1-2h-1c-1 1-1 0-2 0l-1 1h0c-2-5-4-8-9-10h-2v-1l7-9c1-2 2-4 3-5z" class="B"></path><path d="M199 480h3l-3 3v1 1c1 1 2 1 2 3v3l2 4c-3-2-5-6-9-5h0-2v-1l7-9z" class="W"></path><path d="M202 475v1 1c2 3 3 6 6 9l1-1v-2l2-2h1c-1 2-1 4-1 6l-2 7v1c-3-1-3-7-4-10-1-2-2-3-3-5h-3c1-2 2-4 3-5z" class="l"></path><defs><linearGradient id="U" x1="285.213" y1="652.982" x2="290.033" y2="680.831" xlink:href="#B"><stop offset="0" stop-color="#5e5b5c"></stop><stop offset="1" stop-color="#b1b1b2"></stop></linearGradient></defs><path fill="url(#U)" d="M278 647l1-1 3 2h1c4 3 8 5 10 10l1-1h0c2 0 2 0 2 1l1-2-2-3 1-1c2-1 3-1 5-2 1 1 2 3 4 4v2l-1 1v3h0v1l1 3 1 1c-1 3 1 5 1 8h0v3c-1 0-2-1-3-2v-1h-1v1 2l-9-2-8 6h1 2l-3 1c-4 1-8 3-12 6 1-3 3-5 5-8 4-4 8-9 10-15v-2c-1-1-1-1-1-2v-1l-1-1v4-2c-1-6-4-9-9-13z"></path><path d="M294 671h2v1c-1 1-2 1-3 1h-1 0l2-2z" class="B"></path><path d="M278 647l1-1 3 2c4 3 7 7 7 12v4-2c-1-1-1-1-1-2v-1l-1-1v4-2c-1-6-4-9-9-13z" class="P"></path><path d="M301 650c1 1 2 3 4 4v2l-1 1v3h0v1l1 3 1 1c-1 3 1 5 1 8h0v3c-1 0-2-1-3-2v-1h0c0-3-2-3-4-5 0-1 1 0 0-1l-2-2-5-7 1-1h0c2 0 2 0 2 1l1-2-2-3 1-1c2-1 3-1 5-2z" class="C"></path><path d="M296 658l1-2c1 1 2 3 3 4 1 0 3 0 4 1l1 3 1 1c-1 3 1 5 1 8h0l-11-15z" class="p"></path><path d="M301 650c1 1 2 3 4 4v2l-1 1v3h0v1c-1-1-3-1-4-1-1-1-2-3-3-4l-2-3 1-1c2-1 3-1 5-2z" class="D"></path><path d="M301 650c1 1 2 3 4 4v2l-1 1v3-3c-1-2-3-3-5-4-1 0-1 0-2-1h-1c2-1 3-1 5-2z" class="F"></path><path d="M239 179h1l3 3c2 4 4 6 5 11 1-2 0-3 2-4v-2l-1-1h1l3 3c0 2 0 8-1 10-1 6-2 11 0 17 0 2 1 5 3 7 1 2 2 3 4 5h0c2 1 3 2 6 3h0v1h-1 0 3v1h-20c-5 0-10 0-16-1h8v-1l3-1h0c2-4 2-11 1-15v-1c-2-3-6-6-7-9 1-2 4-3 6-4 2-2 4-4 5-6 0-5-2-8-5-11-1-2-2-4-3-5z" class="Y"></path><path d="M250 216h2c0 2 1 5 3 7 1 2 2 3 4 5h0c2 1 3 2 6 3-3 0-9 0-12-1-3-2-5-7-6-10h2c0-1 1-1 1-2h1l-1-2z" class="D"></path><defs><linearGradient id="V" x1="256.905" y1="208.296" x2="240.916" y2="196.094" xlink:href="#B"><stop offset="0" stop-color="#bcbaba"></stop><stop offset="1" stop-color="#f1f1f0"></stop></linearGradient></defs><path fill="url(#V)" d="M248 193c1-2 0-3 2-4v-2l-1-1h1l3 3c0 2 0 8-1 10-1 6-2 11 0 17h-2l1 2h-1c0 1-1 1-1 2h-2c-2-7-5-10-10-15l5-2c3-3 5-5 6-10h0z"></path><defs><linearGradient id="W" x1="254.882" y1="212.642" x2="246.618" y2="201.858" xlink:href="#B"><stop offset="0" stop-color="#afaead"></stop><stop offset="1" stop-color="#dcdbdb"></stop></linearGradient></defs><path fill="url(#W)" d="M250 216c-1-2-3-5-4-7v-1-2c0-1 1-1 1-3 0-1 0-1 1-2l1-1v-1c1-1 2-2 3-2l-1 1 1 1c-1 6-2 11 0 17h-2z"></path><path d="M213 501l3-7c0 3 1 7 0 10h2v12 49 1c-1 2-2 7-3 10l-1-2c1-7 3-13 1-20 0-3-1-5-2-7s-2-4-3-5c-4-4-8-6-14-7h0s1 0 1-1c2 1 4 0 6-1 4-2 6-8 7-12 0-2 1-3 1-5v-6h0v-1l-1 1h-1c0-4 0-6 2-10v5-1l1-1 1-1v-1z" class="S"></path><g class="E"><path d="M216 504h2v12c-1-4-2-8-2-12z"></path><path d="M210 521c0-2 1-3 1-5v-6h0v-1l-1 1h-1c0-4 0-6 2-10v5-1l1-1 1-1v-1c0 5 1 9 1 14 1 1 2 5 1 6v1h0c1 1 1 1 1 2v1h0l-1-1c-2-3-2-2-5-3z"></path></g><path d="M210 521c3 1 3 0 5 3l1 1c1 5-1 8-3 12l-5 1c1 2 5 5 5 8v1c-1-2-2-4-3-5-4-4-8-6-14-7h0s1 0 1-1c2 1 4 0 6-1 4-2 6-8 7-12z" class="T"></path><defs><linearGradient id="X" x1="161.788" y1="329.723" x2="176.406" y2="317.909" xlink:href="#B"><stop offset="0" stop-color="#2b2526"></stop><stop offset="1" stop-color="#493c3c"></stop></linearGradient></defs><path fill="url(#X)" d="M137 303c3 0 6-1 9 0 12 1 24 10 34 17 1 3 2 3 4 4h0l8 7 4 3c0 1 0 2 1 3 1 2 3 3 4 6 1 2 3 6 3 9h-1c0-1 0-2-1-3l-2 1v1 3l-2-6c-1-1-2-2-3-2h-1s-11-14-12-16c-6-7-12-10-20-14-6-3-12-5-19-6l-1-1h-5c-4 0-7 2-10 5l-1 1c0 1 0 2-1 3-1 0-1 0-2-1 0-2 1-3 2-5 2-5 7-7 12-9z"></path><path d="M156 309c4 2 10 4 12 8h0c-4 0-9-5-13-6v-1l1-1z" class="V"></path><path d="M145 305c4 1 7 3 11 4l-1 1v1c-4 0-8-2-13-2h-5l1-1c1-1 1-1 2-1l-2-1c2-1 4-1 7-1z" class="e"></path><path d="M145 305c4 1 7 3 11 4l-1 1c-2-1-4-2-5-2-5-2-8-1-12 0 1-1 1-1 2-1l-2-1c2-1 4-1 7-1z" class="y"></path><defs><linearGradient id="Y" x1="197.902" y1="340.606" x2="199.018" y2="347.407" xlink:href="#B"><stop offset="0" stop-color="#767474"></stop><stop offset="1" stop-color="#979596"></stop></linearGradient></defs><path fill="url(#Y)" d="M193 339l1-1c2 2 5 3 7 5 1 2 3 6 3 9h-1c0-1 0-2-1-3l-2 1v1 3l-2-6c-1-3-3-6-5-9z"></path><defs><linearGradient id="Z" x1="190.954" y1="328.665" x2="193.046" y2="339.835" xlink:href="#B"><stop offset="0" stop-color="#5a4b4c"></stop><stop offset="1" stop-color="#6b6363"></stop></linearGradient></defs><path fill="url(#Z)" d="M193 339c-3-3-6-7-9-10h0 1v-1c1-1 5 3 6 3h1l4 3c0 1 0 2 1 3 1 2 3 3 4 6-2-2-5-3-7-5l-1 1z"></path><path d="M137 303c3 0 6-1 9 0h-3v1h-2 1c1 1 2 1 3 1-3 0-5 0-7 1l2 1c-1 0-1 0-2 1l-1 1c-4 0-7 2-10 5l-1 1c0 1 0 2-1 3-1 0-1 0-2-1 0-2 1-3 2-5 2-5 7-7 12-9z" class="K"></path><path d="M137 306v-1h0c1-1 4-2 6-2v1h-2 1c1 1 2 1 3 1-3 0-5 0-7 1h-1z" class="X"></path><path d="M137 306h1l2 1c-1 0-1 0-2 1l-1 1c-4 0-7 2-10 5l-1 1v-1c3-4 6-6 11-8z" class="M"></path><path d="M457 808l18 42 22 51 11 24c1 3 2 7 5 10 1 1 2 1 3 0 2-2 3-5 4-8l5-14 19-48 22-53c0 2 0 4-1 6 0 2-4 8-3 9 0 1 1 3 1 4 2 6 7 11 12 13 3 1 5 1 8 1l-1 1h0c-2 1-5 0-7-1-8-3-11-8-14-15l-31 78-8 19c-1 2-2 6-4 9-1 1-3 1-4 1-2 0-3-1-4-3-2-2-3-6-4-9l-9-20-32-72c-2 3-3 5-6 8-4 3-9 5-14 5l1-1c5-1 8-2 11-5 3-2 5-4 7-8 2-5-6-18-8-22l1-2z"></path><path d="M603 775l1 1c-1 1-2 1-3 1-1 1-1 1-1 2v1h-1l1 1v-1c2-1 4-1 6-2-1 2-2 3-4 3-1 1-2 3-3 4l5-2c4-1 9-3 13-3h1c1 1 2 1 2 1 6-1 12-1 18 2l3 2s2 1 3 2l2 2c3 4 4 7 6 11-1 1-2 2-3 2h-3c-1-1-1-2-2-4-1 1-2 2-3 4-2-6-4-11-9-14-8-4-23 1-30 3-4 1-9 2-12 3-5 2-8 5-11 8l-2-2c2-3 4-5 7-8l-1-1c1-1 2-2 2-4l1-1c5-5 10-9 17-11z" class="b"></path><path d="M603 775l1 1c-1 1-2 1-3 1-1 1-1 1-1 2v1h-1l1 1v-1c2-1 4-1 6-2-1 2-2 3-4 3-1 1-2 3-3 4-5 4-11 7-16 11h0c2-4 6-5 9-8h-1l-2 2c-1-1-1-1-2-1l-3 3-1-1c1-1 2-2 2-4l1-1c5-5 10-9 17-11z" class="M"></path><defs><linearGradient id="a" x1="613.818" y1="781.049" x2="639.452" y2="785.225" xlink:href="#B"><stop offset="0" stop-color="#453333"></stop><stop offset="1" stop-color="#757272"></stop></linearGradient></defs><path fill="url(#a)" d="M620 781c6-1 12-1 18 2l3 2s2 1 3 2l2 2c3 4 4 7 6 11-1 1-2 2-3 2h-3c-1-1-1-2-2-4-2-4-4-9-8-11-7-4-13-4-21-3-1 1-2 1-4 1 1-1 1 0 1-1h1v-1h-2c3-1 6-2 9-2z"></path><path d="M641 785s2 1 3 2l-3 2h0c-1-1-2-2-2-3l2-1z" class="J"></path><path d="M644 787l2 2c-1 1-1 1-1 3l1 4-5-7 3-2z" class="W"></path><path d="M646 789c3 4 4 7 6 11-1 1-2 2-3 2l-3-6-1-4c0-2 0-2 1-3z" class="C"></path><defs><linearGradient id="b" x1="197.74" y1="340.371" x2="204.444" y2="335.827" xlink:href="#B"><stop offset="0" stop-color="#a09e9e"></stop><stop offset="1" stop-color="#bbb"></stop></linearGradient></defs><path fill="url(#b)" d="M180 320c7 4 15 8 22 8v1c1 2 3 1 4 3 4 0 7 2 11 2h0 2v4l1-1v1h1l-4 7-1 2h1c1 4-3 6-3 9v2c-1 2-2 3-4 5-1 0-6 3-6 4l-3 2c-3 2-7 4-9 6l-1-1c0-1 1-2 1-3 4-6 7-11 8-17v-3-1l2-1c1 1 1 2 1 3h1c0-3-2-7-3-9-1-3-3-4-4-6-1-1-1-2-1-3l-4-3-8-7h0c-2-1-3-1-4-4z"></path><path d="M196 334c4 3 8 7 10 10 0 2 1 5 0 7l-2 4v-3c0-3-2-7-3-9-1-3-3-4-4-6-1-1-1-2-1-3z" class="l"></path><path d="M206 344l6 8c-2 3-4 6-6 8l-1-1h-2l1-4 2-4c1-2 0-5 0-7z" class="M"></path><path d="M204 355l2-4c0 3 1 5-1 8h-2l1-4z" class="e"></path><path d="M180 320c7 4 15 8 22 8v1c1 2 3 1 4 3 4 0 7 2 11 2h0 2v4l1-1v1h1l-4 7-1 2-1 1c0 1 0 2-1 2 0-4-4-7-7-10h0c-4-4-7-8-12-11-2-2-8-5-11-5h0c-2-1-3-1-4-4z" class="Z"></path><path d="M206 332c4 0 7 2 11 2h0l1 1-1 2 1 1-1 1c0-1-2-4-3-4-2-1-5-2-8-3z" class="V"></path><path d="M217 334h2v4l1-1v1h1l-4 7c-1 0-1 0-1-1 0-2 1-3 1-5l1-1-1-1 1-2-1-1z" class="Y"></path><path d="M195 329l9 3c1 1 3 2 5 3 2 0 3 2 5 3 0 2-1 8 1 10h0c0 1 0 2-1 2 0-4-4-7-7-10h0c-4-4-7-8-12-11z" class="M"></path><path d="M207 340c3 3 7 6 7 10 1 0 1-1 1-2l1-1h1c1 4-3 6-3 9v2c-1 2-2 3-4 5-1 0-6 3-6 4l-3 2c-3 2-7 4-9 6l-1-1c0-1 1-2 1-3 4-6 7-11 8-17v-3-1l2-1c1 1 1 2 1 3h1v3l-1 4h2l1 1c2-2 4-5 6-8 1 0 1 0 1-1 0-2-1-5-2-6-2-1-3-3-4-5h0z" class="j"></path><path d="M203 359h2l1 1c-3 3-5 5-8 7 2-3 4-5 5-8h0z" class="U"></path><path d="M192 371c2-1 3-1 4-2h1v1h1 1c0-1 1-1 2-2v1c-3 2-7 4-9 6l-1-1c0-1 1-2 1-3z" class="G"></path><path d="M847 693l2 2c1 3 1 7 1 10 0 5-1 9-3 13-2 8-5 15-10 22-3 4-8 8-13 11l-8 5c-1 2-2 3-5 5-1 0-2 0-3-1h0c-4-1-7 1-11 2-1 1-2 1-3 2s-3 1-5 1h-2-1-1c-2 1 0 1-2 1v-1c-1-1-2-1-3-1v-1c1 0 3-1 4-1 3-1 6-2 9-4l14-6c6-3 12-7 16-11l5-4c-1-1-2-1-3-2l2-1-1-1c3-2 6-4 10-7 0 0-1-1 0-1 0-1 1-2 1-2 1-2 3-3 3-5l-1-1 4-5 1-1v-1l1-4c1-2 2-4 2-6v-4-2-1z" class="B"></path><path d="M808 760l8-4c-1 2-2 3-5 5-1 0-2 0-3-1z" class="O"></path><path d="M847 700l1-3c1 3 1 7 1 10h-1v-1l-1 1c-1 1-2 2-3 4v-1l1-4c1-2 2-4 2-6z" class="Y"></path><path d="M847 707l1-1v1h1c-1 9-5 17-11 23l-2-2c-2 3-4 5-6 7l-2 2c-1-1-2-1-3-2l2-1-1-1c3-2 6-4 10-7 0 0-1-1 0-1 0-1 1-2 1-2 1-2 3-3 3-5l-1-1 4-5 1-1c1-2 2-3 3-4z" class="K"></path><path d="M844 711c1-2 2-3 3-4 0 3-1 5-2 8l-2-3 1-1z" class="M"></path><path d="M827 734c3-3 7-5 10-8h1 0l-2 2c-2 3-4 5-6 7l-2 2c-1-1-2-1-3-2l2-1z" class="C"></path><path d="M843 712l2 3c-2 5-5 8-9 11 0 0-1-1 0-1 0-1 1-2 1-2 1-2 3-3 3-5l-1-1 4-5z" class="I"></path><defs><linearGradient id="c" x1="809.782" y1="753.139" x2="805.576" y2="747.509" xlink:href="#B"><stop offset="0" stop-color="#363333"></stop><stop offset="1" stop-color="#524b4c"></stop></linearGradient></defs><path fill="url(#c)" d="M828 737l2-2c2-2 4-4 6-7l2 2c-10 11-21 20-34 27-4 2-7 4-11 5l-6 3h-1-1c-2 1 0 1-2 1v-1c-1-1-2-1-3-1v-1c1 0 3-1 4-1 3-1 6-2 9-4l14-6c6-3 12-7 16-11l5-4z"></path><defs><linearGradient id="d" x1="311.791" y1="768.857" x2="341.912" y2="758.755" xlink:href="#B"><stop offset="0" stop-color="#898788"></stop><stop offset="1" stop-color="#b7b5b5"></stop></linearGradient></defs><path fill="url(#d)" d="M352 758l11 1h8l3 1 3 3-12 5c-4 2-8 4-11 6h4l-3 3h-4l-3-1-7-2h-1c-1 0-2 0-2-1l-11-1h-1l1 2-1 1h2c-1 0-2 1-3 1l-3-1c-5 0-8-2-11-6v-2h0c1-2 1-1 3-2 1 0 2-2 3-2 11-3 21-4 32-4l3-1z"></path><path d="M318 771c1 0 3 0 4 1 0 1 1 2 2 3-3-1-4-2-6-4z" class="B"></path><path d="M343 764h3l1 1c2-1 4-1 7-1v1c0 2 0 2-2 4h0c-4 2-9-1-13 1h-5-7c-3 0-6 0-8-2 4-1 7-2 11-3 4 0 8 0 13-1z" class="d"></path><path d="M343 764h3l1 1c2-1 4-1 7-1v1c0 2 0 2-2 4h0v-2l1-1v-1c-3 1-5 1-7 1-1-1-2-1-3-2z" class="L"></path><path d="M318 771l-2-2 1-1h2c2 2 5 2 8 2h7 5 6c4 0 10 1 14-1h0c2 1 1 0 2-1h4c-4 2-8 4-11 6h4l-3 3h-4l-3-1-7-2h-1c-1 0-2 0-2-1l-11-1h-1l1 2-1 1h2c-1 0-2 1-3 1l-1-1c-1-1-2-2-2-3-1-1-3-1-4-1z" class="R"></path><path d="M322 772c1 0 2 0 2 1l2 2h2c-1 0-2 1-3 1l-1-1c-1-1-2-2-2-3z" class="C"></path><path d="M341 774v-1h1l2 1h9 1 4l-3 3h-4l-3-1-7-2z" class="O"></path><defs><linearGradient id="e" x1="367.201" y1="759.745" x2="353.878" y2="769.677" xlink:href="#B"><stop offset="0" stop-color="#9f9e9e"></stop><stop offset="1" stop-color="#c7c6c6"></stop></linearGradient></defs><path fill="url(#e)" d="M352 758l11 1h8l3 1 3 3-12 5h-4c-1 1 0 2-2 1h0c-4 2-10 1-14 1h-6c4-2 9 1 13-1h0c2-2 2-2 2-4v-1c-3 0-5 0-7 1l-1-1c1-1 5 0 6-1-1-2-2-3-3-4l3-1z"></path><path d="M191 679h5 0c-2 1-3 1-4 1l1 1c1-1 1-1 2 0 1 0 2 0 2 1 2 0 2 0 3 1l1 1h1 1c1 1 2 3 3 4s2 2 2 4h0c1 6 2 10-2 16l-4 4-2 2c-3 1-5 1-7 1l-2 1-3-1c-3 0-5-2-7-4-1-2-1-4-2-6 0-3 1-5 2-8l-1-1c1-1 2-1 3-2l-1-1c1-2 1-2 2-3v-2c-2 0-3 0-4-1 3-4 7-6 11-8z" class="d"></path><path d="M184 690l1 1h0 0c2 0 6 1 7 2h1l-2 1h-1c-2-2-5-1-7 0l-1-1c1-2 1-2 2-3z" class="H"></path><path d="M188 715v-1c1 0 1 0 2-1h-2v-1c2 0 3-1 4-2 0 1 0 2-1 2l2 2h1 1 5c-3 1-5 1-7 1l-2 1-3-1z" class="L"></path><path d="M193 693c1 1 3 3 3 4 1 2 1 5 1 7-1 2-3 4-5 6h0c-1 1-2 2-4 2v1h2c-1 1-1 1-2 1v1c-3 0-5-2-7-4-1-2-1-4-2-6l3 3c1 2 2 2 4 2l1-3c2-2 5-5 6-8l-1-2h1l-2-3 2-1z" class="S"></path><path d="M193 697c1 1 1 2 1 3-1 0-1 0-1-1l-1-2h1z" class="D"></path><path d="M187 707l2 4h-1c-1 0-2 0-2-1l1-3z" class="C"></path><path d="M183 694c2-1 5-2 7 0h1l2 3h-1l1 2c-1 3-4 6-6 8l-1 3c-2 0-3 0-4-2l-3-3c0-3 1-5 2-8l-1-1c1-1 2-1 3-2z" class="p"></path><path d="M183 696l1-1c1-1 3 0 5 0-3 1-5 2-6 4v-3z" class="P"></path><path d="M183 694c2-1 5-2 7 0h1l2 3h-1c-1-1-2-1-3-2-2 0-4-1-5 0l-1 1-2 1-1-1c1-1 2-1 3-2z" class="B"></path><path d="M183 696v3c-1 1-1 1-1 2v7l-3-3c0-3 1-5 2-8l2-1z" class="E"></path><path d="M850 440c4 6 6 12 4 19-1 5-4 9-7 13l1 1c-3 0-4-1-7-2 1 2 2 3 4 3 3 1 6 1 9 1h0l1-1 1 1c-4 4-6 7-7 12-1 6-1 12 1 17h-2l-3-11c0-2-1-4-2-6h-1-2c1 3 3 5 3 8-3-5-5-9-8-13 0-1-4-5-5-6-1-3-2-5-3-7v-4l2-3 2-3c0-1 1-2 2-3v-1c4-1 7-3 11-5l1-1c1 0 3-1 5-1l1-1h1 0c-1-2-2-2-3-3l-1-1c0-1 1-2 2-3z" class="P"></path><path d="M841 471h0l-1-1v-1h-2l-1-1c-1-1-2-3-2-4 0-4 2-6 4-8h2v1c-2 1-2 2-2 3v5 1c1 3 3 4 5 5 1 0 2 1 3 1l1 1c-3 0-4-1-7-2z" class="p"></path><path d="M834 468c2 1 3 1 4 3 1 1 2 1 3 2s2 2 3 2 4 0 4 2v1c-2 2-3 3-3 6v4 5c0-2-1-4-2-6s-1-6-2-8c-2-4-5-7-7-11h0z" class="R"></path><path d="M854 475l1-1 1 1c-4 4-6 7-7 12-1 6-1 12 1 17h-2l-3-11v-5l1-4c0-1 1-1 1-2l1-1c1-1 2-3 1-4l1-1 1 1c1-1 1-1 2-1l1-1z" class="T"></path><path d="M833 456v-1c4-1 7-3 11-5v2c-2-1-2 0-4 0-4 3-6 6-7 11 0 2 0 2 1 4h0v1h0c2 4 5 7 7 11 1 2 1 6 2 8h-1-2c1 3 3 5 3 8-3-5-5-9-8-13 0-1-4-5-5-6-1-3-2-5-3-7v-4l2-3 2-3c0-1 1-2 2-3z" class="Z"></path><path d="M829 462l2-3 1 1 1 1h-1c0 3 0 4 1 7 2 6 5 12 9 19h-2c-1-4-4-9-6-13l-1 1c-1 0-2-2-2-4-2-3-2-5-2-9z" class="W"></path><path d="M829 462l2-3 1 1c-2 5-1 9 1 13l1 1-1 1c-1 0-2-2-2-4-2-3-2-5-2-9z" class="U"></path><path d="M827 469v-4l2-3c0 4 0 6 2 9 0 2 1 4 2 4l1-1c2 4 5 9 6 13 1 3 3 5 3 8-3-5-5-9-8-13 0-1-4-5-5-6-1-3-2-5-3-7z" class="Z"></path><path d="M850 440c4 6 6 12 4 19-1 5-4 9-7 13-1 0-2-1-3-1-2-1-4-2-5-5v-1-5c0-1 0-2 2-3v-1l4 1v-1c0-1-1-1-3-2 0 0-1 0-1-1l-1-1c2 0 2-1 4 0v-2l1-1c1 0 3-1 5-1l1-1h1 0c-1-2-2-2-3-3l-1-1c0-1 1-2 2-3z" class="V"></path><path d="M852 447c1 2 1 3 1 5l-2-1-2-1 1-2 1-1h1z" class="Y"></path><path d="M850 448l1-1 1 3-1 1-2-1 1-2z" class="V"></path><path d="M852 453c1 5 1 8-2 13l-3 3c-1 0-2 0-3-1 1 0 1 0 1-1 2-1 2-2 2-3s0-2 1-3h1v-2c1-1 2-1 2-3 0-1 1-2 1-3z" class="B"></path><path d="M844 450l1-1c1 0 3-1 5-1l-1 2 2 1 2 1-1 1c0 1-1 2-1 3 0 2-1 2-2 3v2h-1v-4l-2-3c-1-1-1-1-1-2v-1l-1 1v-2z" class="e"></path><path d="M849 450l2 1 2 1-1 1c0 1-1 2-1 3 0 2-1 2-2 3-1-3 0-6 0-9z" class="Q"></path><path d="M844 452l1-1v1c0 1 0 1 1 2l2 3v4c-1 1-1 2-1 3s0 2-2 3c0 1 0 1-1 1 0-1-1-1-2-2h-1c0-1 0-1-1-2l-1 2v-1-5c0-1 0-2 2-3v-1l4 1v-1c0-1-1-1-3-2 0 0-1 0-1-1l-1-1c2 0 2-1 4 0z" class="S"></path><path d="M848 457v4c-1 1-1 2-1 3h-1v-5l2-2z" class="C"></path><path d="M846 464h1c0 1 0 2-2 3 0 1 0 1-1 1 0-1-1-1-2-2l1-1c1 0 2-1 3-1z" class="L"></path><path d="M839 466v-1-5c0-1 0-2 2-3v4h1c1 0 1 1 2 1-1 1-3 2-4 2l-1 2z" class="P"></path><defs><linearGradient id="f" x1="249.657" y1="743.99" x2="301.843" y2="780.01" xlink:href="#B"><stop offset="0" stop-color="#898784"></stop><stop offset="1" stop-color="#a4a3a6"></stop></linearGradient></defs><path fill="url(#f)" d="M318 760c5-1 11-2 17-3 5 2 11 0 17 1l-3 1c-11 0-21 1-32 4-1 0-2 2-3 2-2 1-2 0-3 2h0v2c0 1-1 2-2 3-1 0-1-1-1-1-4 1-10 0-14 0h0c-2 1-4 1-5 2l4 1v1c2 0 4 0 6 1h-4v-1h-5c-2 0-6 1-8 1-3 0-6 0-9 2h0c-1 1-5 3-7 4h-6c-3-1-5-2-8-3-2 0-4-1-6-2-2-2-3-3-5-4l-1-2h0 0v-2c2 0 3 0 5 1v-1h-1c-1 0-2 0-2-1-2 0-3 0-4-1h0l-2-1v-1h0 1 3 1l6 1c3 1 7 1 10 1 4 1 9 0 13 0 5-1 10-1 14-2l9-1c3 0 7-2 10-2l14-3h0l1 1z"></path><path d="M247 769h23c-2 1-3 1-5 1s-4 1-6 1h0v1c-2 0-6-1-8-1-1-1-2-2-4-2z" class="O"></path><path d="M293 766l1 1h2c1-1 1-1 2-1 2 0 4 0 5-1h2 0c2-1 3-1 4-1-2 2-5 2-7 3l-13 3c-5-1-12 0-17-1 7 0 14-2 21-3z" class="K"></path><path d="M309 764l8-1c-1 0-2 2-3 2-2 1-2 0-3 2h0v2c0 1-1 2-2 3-1 0-1-1-1-1-4 1-10 0-14 0h0c-2 0-3-1-5-1l13-3c2-1 5-1 7-3z" class="i"></path><path d="M308 771c0-2 0-3 2-4h1v2c0 1-1 2-2 3-1 0-1-1-1-1z" class="G"></path><defs><linearGradient id="g" x1="273.099" y1="766.33" x2="281.159" y2="776.855" xlink:href="#B"><stop offset="0" stop-color="#615e62"></stop><stop offset="1" stop-color="#858483"></stop></linearGradient></defs><path fill="url(#g)" d="M270 769h2c5 1 12 0 17 1 2 0 3 1 5 1-2 1-4 1-5 2h-7l-23-1v-1h0c2 0 4-1 6-1s3 0 5-1z"></path><path d="M318 760c5-1 11-2 17-3 5 2 11 0 17 1l-3 1c-11 0-21 1-32 4l-8 1c-1 0-2 0-4 1h0-2c-1 1-3 1-5 1-1 0-1 0-2 1h-2l-1-1 25-6z" class="a"></path><path d="M251 771c2 0 6 1 8 1l23 1h7l4 1v1c2 0 4 0 6 1h-4v-1h-5c-2 0-6 1-8 1-3 0-6 0-9 2h0-2c-2 0-4 0-6-1-1 0-2-1-4-1h-2c-2-1-3-1-5-1l-2-1c-1 0-1-1-2-2h0l1-1z" class="x"></path><path d="M238 767l9 2c2 0 3 1 4 2l-1 1h0c1 1 1 2 2 2l2 1c2 0 3 0 5 1h2c2 0 3 1 4 1 2 1 4 1 6 1h2c-1 1-5 3-7 4h-6l-8-3c-2 0-4-1-6-2-2-2-3-3-5-4l-1-2h0 0v-2c2 0 3 0 5 1v-1h-1c-1 0-2 0-2-1-2 0-3 0-4-1h0z" class="E"></path><path d="M254 775c2 0 3 0 5 1h2c2 0 3 1 4 1 2 1 4 1 6 1h0c-7 2-12 1-17-3z" class="s"></path><path d="M238 767l9 2c2 0 3 1 4 2l-1 1h0c1 1 1 2 2 2-3-1-5-2-8-2 1 2 6 6 8 7-2 0-4-1-6-2-2-2-3-3-5-4l-1-2h0 0v-2c2 0 3 0 5 1v-1h-1c-1 0-2 0-2-1-2 0-3 0-4-1h0z" class="q"></path><path d="M857 281l5 1h8 5c4 1 8 3 12 4 8 5 16 12 19 21l1 1-1 2c1 5 1 10 0 16-2-1-2-7-3-10h0l-1-3h0c0-2-1-3-2-4v-1c-1-4-5-7-8-9-3-3-6-4-10-5-1-1-3-2-4-3l-1 1c-1-1-1-1-2-1-3 0-6-1-9-1l-5 1 2 1-2 1h0l-2 1-9 2v4h0c0 1 0 1-1 2v1c-2 0-4 3-5 4l-1 1-1-1v-2l1-1-7 3-2-1h-1v-1l2-1 1-2-3 1v-2h-1v-1h-3c0-1 3-2 4-2-4-1-6 0-9 1l1-1h1l-2-1c2 0 5-2 7-3h0l3-3h0l2-1c2-2 5-5 6-7 4 0 8 1 11 0h1l1-1 2-1z" class="k"></path><path d="M857 281l5 1v1h1v1h-8v-2l2-1z" class="a"></path><path d="M867 287c4 1 6 1 9 2-1 0-1 1-2 0h-1l1 1v1c-1 0-2-1-2-1h-3c-1 0-1 0-2-1v-1-1z" class="H"></path><path d="M862 282h8 5l-1 2h-11v-1h-1v-1z" class="u"></path><path d="M862 282h8v1h-7-1v-1z" class="s"></path><path d="M875 282c4 1 8 3 12 4l-2 2-11-4 1-2z" class="b"></path><path d="M842 283c4 0 8 1 11 0h1l1-1v2h0c-3 1-5 1-7 2-4 1-8 4-12 4 2-2 5-5 6-7z" class="n"></path><path d="M887 286c8 5 16 12 19 21l1 1-1 2c-4-11-11-17-21-22l2-2z" class="a"></path><path d="M833 298c1-2 4-2 6-3 9-4 18-6 28-8v1 1h-1v1l-5 1 2 1-2 1h0l-2 1-9 2v4h0c0 1 0 1-1 2v1c-2 0-4 3-5 4l-1 1-1-1v-2l1-1-7 3-2-1h-1v-1l2-1 1-2-3 1v-2h-1v-1h-3c0-1 3-2 4-2z" class="G"></path><path d="M854 291c4-1 8-2 13-3v1h-1v1l-5 1-6 1-1-1z" class="d"></path><path d="M833 301c5-4 14-8 21-10l1 1-3 2-16 8-3 1v-2z" class="p"></path><path d="M855 292l6-1 2 1-2 1h0l-2 1-9 2v4h0c0 1 0 1-1 2v1c-2 0-4 3-5 4l-1 1-1-1v-2l1-1-7 3-2-1h-1v-1l2-1 1-2 16-8 3-2z" class="F"></path><path d="M855 292l6-1 2 1-2 1h0l-9 1 3-2zm-13 9c2-2 5-3 8-5v4h0l-8 1z" class="P"></path><path d="M842 301l8-1c0 1 0 1-1 2v1c-2 0-4 3-5 4l-1 1-1-1v-2l1-1-7 3-2-1h-1v-1l2-1 7-3z" class="X"></path><path d="M849 302v1c-2 0-4 3-5 4l-1 1-1-1v-2l1-1c1-1 5-2 6-2z" class="O"></path><path d="M454 149c-1-1-1-2-1-3v-1h1c7 6 12 13 21 15 4-1 7-2 10-4l1 1c0 1-1 2-1 3 0 2 0 2 2 2 1-1 2-2 3-2s1-1 2-1l5-4c1-1 2-2 3-2l1 2-1 1 1 1h1v1c-1 1-1 0-1 1v1c-2 3-4 5-6 7s-3 5-6 6c-6 3-14 5-20 10-1 2-2 3-4 5h0v1l1 1c-1 0-2 1-3 2s-2 2-3 4c-1 1-1 2-2 3h0l-1 2c2 2 4 4 6 5l-1 1c-1 0-2-2-4-2-4-2-10-7-14-11 1-1 3-2 4-3h4c1-3 2-4 5-5l2-2-2-1 2-1c1-2 1-3 2-5 0-2-2-3-4-5 1 0 1 0 2-1-1-3-2-9-1-12 0-3-2-7-4-10z" class="p"></path><path d="M485 160c0 2 0 2 2 2 1-1 2-2 3-2s1-1 2-1v1l-6 5h-1c0-1-1-2-2-3l2-2z" class="P"></path><path d="M489 173l1-1 1-1c0-3 2-4 3-6l1 1c2-2 4-4 6-7v1c-2 3-4 5-6 7s-3 5-6 6z" class="n"></path><path d="M462 189l-1 1c0 1-1 2-1 3 2-4 6-7 9-10h0c-1 2-2 3-4 5h0v1l1 1c-1 0-2 1-3 2s-2 2-3 4c-1 1-1 2-2 3-1-1-1-1-1-2 1-2 3-7 5-8z" class="L"></path><path d="M462 171c2 1 2 3 4 5l3 4c-1 1-3 2-4 3-1 2-2 5-3 6-1-1 0-2 0-3l2-3-1-1c1-1 1-2 2-3-1-2-2-4-3-7v-1z" class="G"></path><path d="M461 184c1-1 1-2 2-2l1 1-2 3c0 1-1 2 0 3-2 1-4 6-5 8 0 1 0 1 1 2h0l-1 2-1-1c-1-6 2-12 5-16z" class="D"></path><path d="M457 172c1 0 1 0 2-1-1-3-2-9-1-12 1 4 2 8 4 12v1c1 3 2 5 3 7-1 1-1 2-2 3-1 0-1 1-2 2h-2 0l-2-1 2-1c1-2 1-3 2-5 0-2-2-3-4-5z" class="F"></path><path d="M462 172c1 3 2 5 3 7-1 1-1 2-2 3-1 0-1 1-2 2h-2 0l-2-1 2-1h1c1-2 2-4 2-6-1-1-1-2 0-3v-1z" class="R"></path><path d="M459 184h2c-3 4-6 10-5 16l1 1c2 2 4 4 6 5l-1 1c-1 0-2-2-4-2-4-2-10-7-14-11 1-1 3-2 4-3h4c1-3 2-4 5-5l2-2h0z" class="M"></path><path d="M459 184h0l-5 13-1 1c-1-2-1-3-1-4 1-2 2-4 3-5s2-2 2-3l2-2z" class="D"></path><path d="M454 149c-1-1-1-2-1-3v-1h1c7 6 12 13 21 15h1-3-1c-2-1-5-2-7-4v1l-2 2h0l5 5c-1 0-5-4-6-5s0-1-1-2v1c0 2 5 6 7 8l3 3c0 1-1 2-1 3-1 2-2 2-4 4-2-2-2-4-4-5-2-4-3-8-4-12 0-3-2-7-4-10z" class="k"></path><path d="M787 765h2c2 0 4 0 5-1s2-1 3-2c4-1 7-3 11-2h0c1 1 2 1 3 1l-2 2h0c-2 1-3 2-5 4v-2c-6 5-14 8-21 10-2 1-3 1-5 0h-1c-2 1-5 1-8 3h0l-6 3h-8c-1 0-2-1-2-1-3 0-4-1-6 0l-12-2 1 2-5-1-3-1v-2l-9-1-7 1v1h0l-4 1h-4l-1 1c-4 1-8 2-12 4l1 2c-1 0-1 1-2 1l-2-1-2 1-1-1c-2 0-3 0-4 1l-1-1h-2l-3 1c1-1 2-2 3-2 1-1 2-1 3-1h-2-5 0c-1 0-3 0-4-1h0c-2 0-3-1-4-2l-6-4h0l-6-4c-2-1-3-1-4-3 9 3 19 7 28 7 1-1 3-1 5-1l9-4c1 0 3 0 4-1h2l3 1h5 3 0c1-2 0-3 0-4h0l1-1c2 2 4 3 8 4h0 1l12 2c3 1 6 2 9 2 1 1 3 0 5 1 6 1 13 1 19 0 1-1 1-1 2-1 2-3 5-3 7-4s4-2 6-4v-1l1-1c1 0 2 0 3 1v1c2 0 0 0 2-1h1 1z" class="P"></path><path d="M728 776l4 1-1 2-3-1v-2z" class="X"></path><path d="M732 777l3 1 1 2-5-1 1-2z" class="t"></path><path d="M709 771h0c1-2 0-3 0-4h0l1-1c2 2 4 3 8 4h0 1c-4 1-9 2-13 1h3z" class="g"></path><path d="M753 780c2-1 5-1 8-1 3-1 5-1 8-1h0l-6 3h-8c-1 0-2-1-2-1z" class="C"></path><path d="M780 764c1 0 2 0 3 1v1c2 0 0 0 2-1h1l-20 9c2-3 5-3 7-4s4-2 6-4v-1l1-1z" class="Z"></path><path d="M650 769c9 3 19 7 28 7-2 1-3 1-5 1l-3-1c-3 0-6-1-9-2l-1-1h-1-1c1 1 3 2 4 3h2l2 1c2 1 4 1 5 2l1-1h3c1 1 1 0 2 1h-3-4l-10-3h0l-6-4c-2-1-3-1-4-3z" class="k"></path><path d="M660 776l10 3c2 1 5 2 7 2 1 0 2-1 4-1 4-1 9-2 13-4v1c-4 2-9 4-13 5l-7 1c-1 0-3 0-4-1h0c-2 0-3-1-4-2l-6-4z" class="B"></path><defs><linearGradient id="h" x1="801.797" y1="758.288" x2="784.451" y2="777.931" xlink:href="#B"><stop offset="0" stop-color="#5c575a"></stop><stop offset="1" stop-color="#6e6e6d"></stop></linearGradient></defs><path fill="url(#h)" d="M808 760c1 1 2 1 3 1l-2 2h0c-2 1-3 2-5 4v-2c-6 5-14 8-21 10-2 1-3 1-5 0h-1l31-15h0z"></path><path d="M804 765v-1c1-1 3-1 4-2l1 1h0c-2 1-3 2-5 4v-2z" class="Z"></path><path d="M694 776c1 0 2-1 3-1 4-2 9-1 13 0 3 0 6-1 9 0l-7 1v1h0l-4 1h-4l-1 1c-4 1-8 2-12 4l1 2c-1 0-1 1-2 1l-2-1-2 1-1-1c-2 0-3 0-4 1l-1-1h-2l-3 1c1-1 2-2 3-2 1-1 2-1 3-1h-2-5 0l7-1c4-1 9-3 13-5v-1z" class="D"></path><path d="M701 777h2l1 1-1 1-2-2z" class="a"></path><path d="M703 777l4-1 1 2h-4l-1-1z" class="m"></path><path d="M707 776h5v1h0l-4 1-1-2z" class="v"></path><path d="M678 785c5-2 9-4 14-5-2 1-5 4-7 5-2 0-3 0-4 1l-1-1h-2z" class="N"></path><path d="M692 780l9-3 2 2c-4 1-8 2-12 4l1 2c-1 0-1 1-2 1l-2-1-2 1-1-1c2-1 5-4 7-5z" class="K"></path><path d="M691 783l1 2c-1 0-1 1-2 1l-2-1c-1 0-1 0 0-1s2-1 3-1z" class="b"></path><path d="M183 449h2l5 2c1 1 3 2 4 3 1 0 1-1 3 1h1c1-1 2-1 4-1 1 1 1 2 2 2l-6 11-3 6h0c0 2 0 2-1 3 1 0 1-1 2-1 2-1 3-4 6-5 1-1 8-11 9-13 1 2 1 2 0 4l-4 5c0 1 0 0 1 1l-6 8c-1 1-2 3-3 5l-7 9v1 2c-1 0-2 1-2 1-7 9-12 17-14 27h-2c0 4 0 8 2 13-1-2-2-3-2-4 0-2-1-4-2-6s-1-7 0-9v-6-1c1-1 1-2 2-4l4-12-1-1c0-1 1-2 1-4 1-2 2-5 3-8-1 0-2-1-3-2h4c1-1 3-2 4-3-1-1-1-2-2-3h-1l2-3v-1c-2 0-4 1-6 1l-2-1 1-2h0v-6h-1v-3l-2-1h-3l1-3-2-1h-1c3-1 6-1 8-1h4 1z" class="n"></path><path d="M191 453l-2 1-6-3c-1 0-2 0-3-1h1l1-1c3 1 6 2 9 4z" class="H"></path><path d="M173 451c4 1 9 2 14 4l-2 1-1-1c-2-1-5 0-7 0l-2-1h-3l1-3z" class="X"></path><path d="M189 456c2 2 5 5 5 8 1 2-1 5-2 7l-1-2h-1l-1-1 1-2h0v-2h0l1 1h1v-1c0-1 0-1-1-2 0-1 0-2-1-3s-1-2-1-3z" class="K"></path><path d="M195 473h0c0 2 0 2-1 3 1 0 1-1 2-1l-16 20c2-9 10-14 15-22h0z" class="AA"></path><path d="M183 449h2l5 2c1 1 3 2 4 3 1 0 1-1 3 1h1c1-1 2-1 4-1 1 1 1 2 2 2l-6 11v-1c1-2 1-2 1-4-1-3-3-5-5-7l-3-2c-3-2-6-3-9-4h0 1z" class="Q"></path><path d="M194 455c1 0 2 1 3 1h5l-3 6c-1-3-3-5-5-7z" class="f"></path><defs><linearGradient id="i" x1="185.769" y1="507.198" x2="178.231" y2="499.302" xlink:href="#B"><stop offset="0" stop-color="#252525"></stop><stop offset="1" stop-color="#524949"></stop></linearGradient></defs><path fill="url(#i)" d="M189 488c1 2 0 1 0 3 0 1 0 2 1 2-7 9-12 17-14 27h-2c0-12 8-24 15-32z"></path><path d="M202 470c1-1 8-11 9-13 1 2 1 2 0 4l-4 5c0 1 0 0 1 1l-6 8c-1 1-2 3-3 5l-7 9v1 2c-1 0-2 1-2 1-1 0-1-1-1-2 0-2 1-1 0-3l13-18zm-25-15c2 0 5-1 7 0l1 1 2-1 2 1c0 1 0 2 1 3s1 2 1 3c1 1 1 1 1 2v1h-1l-1-1h0v2h0-1c-1-1-1-1-2 0-1 0-1 0-2 1v-1c-2 0-4 1-6 1l-2-1 1-2h0v-6h-1v-3z" class="a"></path><path d="M178 458v-1c3 0 6 0 9 2 1 1 2 3 3 5h0 0c-1 0-1-1-2-1-2-2-3-3-6-3-2 1-3 2-4 4v-6z" class="f"></path><path d="M178 464c1-2 2-3 4-4 3 0 4 1 6 3 1 0 1 1 2 1v2h0-1c-1-1-1-1-2 0-1 0-1 0-2 1v-1c-2 0-4 1-6 1l-2-1 1-2h0z" class="W"></path><path d="M178 464c2 1 4 1 5 1 1-1 1-1 2-1v2c-2 0-4 1-6 1l-2-1 1-2z" class="I"></path><path d="M188 463c1 0 1 1 2 1v2h0-1c-1-1-1-1-2 0-1 0-1 0-2 1v-1-2l1-1h1 1z" class="J"></path><path d="M185 467c1-1 1-1 2-1 1-1 1-1 2 0h1l-1 2 1 1h1l1 2c-2 3-4 5-6 8l-12 25v-1l4-12-1-1c0-1 1-2 1-4 1-2 2-5 3-8-1 0-2-1-3-2h4c1-1 3-2 4-3-1-1-1-2-2-3h-1l2-3z" class="F"></path><path d="M189 468l1 1h1l1 2c-2 3-4 5-6 8h-1c-1-1 3-3 3-6 0-1 1-3 1-5z" class="U"></path><path d="M822 480l5 6 2 3h-1c-2 0-5 2-7 4l-3 6c-1 3-1 8 0 12 1 9 2 17 8 24h0c-6 1-11 3-14 8-5 6-6 15-5 22 2 9 5 18 12 23 2 1 3 1 4 2 1 0 1 0 1 1-3 0-6-1-10-2-8-4-16-3-23 4h0c-2 1-2 1-3 3-1 1-1 1-1 2-1 1-1 2-1 2-1-4 2-6 3-10h0l1-2c-1 0-2 1-3 2l-1-1h1c-1-2 0-4 0-5 1-1 1-1 1-2 1-1 0-3 1-4 1-3 1-5 3-8v-20c0-1 1-2 2-2 1-1 2 0 3 0h0l-2-4h3c2 1 2 1 2 3l2 4 1 1v-2h0c1-2 1-3 1-5h0v-1c1-2 1-2 3-2v-1-1h1c1 1 1 1 1 2l4-4c1-1 2-2 3-2l1-1h0c1-1 3-1 4-1-2-3-3-5-4-8-3-9-1-18-3-27 1-2 1-2 1-4s1-4 1-6l4-5v-1l2-2v-1z" class="d"></path><path d="M816 489h1l1 4c-1 1-2 3-3 5 0 1 1 3 1 4 0 3-1 6 0 9 0 5 0 9 1 13l1 1c0 2 1 4 2 6l3 3v1l-2-1c-2-3-3-5-4-8-3-9-1-18-3-27 1-2 1-2 1-4s1-4 1-6z" class="i"></path><path d="M822 480l5 6 2 3h-1c-2 0-5 2-7 4l-3 6h-1c-2 3-1 8-1 12-1-3 0-6 0-9 0-1-1-3-1-4 1-2 2-4 3-5l-1-4h-1l4-5v-1l2-2v-1z" class="F"></path><path d="M822 480l5 6h-4l-1-5v-1z" class="Q"></path><path d="M820 484l2 2-3 3c-1 0-2 1-2 0h-1l4-5z" class="R"></path><path d="M790 588c-1 0-2 1-3 2l-1-1h1c-1-2 0-4 0-5 1-1 1-1 1-2 1-1 0-3 1-4 1-3 1-5 3-8l1 9 1 2c2 0 9 0 11-1l-1-2c2 0 2 1 3 2 0 0 1 1 1 2l1 1 1-1c1 1 1 2 2 2l-1 1-1-1c-4-1-10-2-14 0-2 1-4 2-6 4z" class="Q"></path><path d="M793 579l1 2c2 0 9 0 11-1v2c-5 0-9 1-13 3h-1c1-2 1-3 2-5v-1z" class="C"></path><defs><linearGradient id="j" x1="802.002" y1="565.009" x2="806.12" y2="564.077" xlink:href="#B"><stop offset="0" stop-color="#848282"></stop><stop offset="1" stop-color="#9d9c9c"></stop></linearGradient></defs><path fill="url(#j)" d="M803 550h0c1-2 1-3 1-5h0v-1c1-2 1-2 3-2v-1-1h1c1 1 1 1 1 2-5 9-6 17-4 27l2 5c0 1 0 2 1 3 1 2 1 3 2 5l-1 1-1-1c0-1-1-2-1-2-1-1-1-2-3-2-2-5-4-12-4-18l-3-12h0l-2-4h3c2 1 2 1 2 3l2 4 1 1v-2z"></path><path d="M795 544h3c2 1 2 1 2 3l1 13h-1 0l-3-12h0l-2-4z" class="f"></path><path d="M792 550c0-1 1-2 2-2 1-1 2 0 3 0l3 12c0 6 2 13 4 18l1 2c-2 1-9 1-11 1l-1-2-1-9v-20z" class="S"></path><path d="M814 682c3-2 5-3 8-4l4 2c2 0 4-1 6 0 3 0 5 1 7 2 1 1 3 2 4 4 2 2 3 4 4 7v1 2 4c0 2-1 4-2 6l-1 4v1l-1 1-4 5-1 1-2-1-4 4-1-1v-1h1l-1-1h0-2v-1c-1 0-2-1-2-1-4-1-5-2-7-4-1 0-1-1-2-1-2 0-3-1-4-2v-1c0-2-1-4-1-6h-1c-3-3-3-5-3-8l1-2c1-4 3-6 6-9l-2-1z" class="k"></path><path d="M838 685c-4-1-7-1-11 0-1 1 0 0-1 0 1 0 2-1 4-1 2-1 4-1 6 0h2v1z" class="P"></path><path d="M839 682c1 1 3 2 4 4h-3l-2-1v-1h-2l3-2z" class="H"></path><path d="M840 686h3c2 2 3 4 4 7v1c-1 0-1-1-2-2l-5-6z" class="d"></path><path d="M814 682c3-2 5-3 8-4l4 2c-4 0-7 1-10 3l-2-1z" class="b"></path><path d="M816 694l1-1h0c1 1 1 1 0 2 0 2 0 3 1 5 0 3 1 8 4 10h0l1 2h1l3 4c-4-1-5-2-7-4-1 0-1-1-2-1-2 0-3-1-4-2v-1c0-2-1-4-1-6 1-3 1-6 3-8z" class="L"></path><path d="M816 703c0 3 1 5 2 8-2 0-3-1-4-2v-1l1-1 1-4z" class="M"></path><path d="M813 702c1-3 1-6 3-8v3c-1 2-1 4 0 6l-1 4-1 1c0-2-1-4-1-6z" class="f"></path><path d="M835 715c-1-1-1-1-2-1-2-3-3-4-3-7-1-3-1-5 1-7 2-3 5-5 8-6 3 0 5 1 8 2v4c0 2-1 4-2 6l-1 4v1l-1 1-4 5-1 1-2-1-4 4-1-1v-1h1l-1-1h0-2v-1c2 0 4-1 6-2z" class="G"></path><path d="M839 707c-1 0-1-1-2-1v-1l3-3c1 0 2-1 3 0 2 1 2 3 2 4l-1 4-2 1c-1 0-2-2-3-3v-1z" class="H"></path><path d="M835 715l3-3c0-2 0-3 1-5v1c1 1 2 3 3 3l2-1v1l-1 1-4 5-1 1-2-1-4 4-1-1v-1h1l-1-1h0-2v-1c2 0 4-1 6-2z" class="K"></path><path d="M835 715l3-3c0-2 0-3 1-5v1c0 1-1 3 0 4 0 2-1 3-2 5h-1l-4 4-1-1v-1h1l-1-1h0-2v-1c2 0 4-1 6-2z" class="W"></path><defs><linearGradient id="k" x1="384.794" y1="783.059" x2="378.693" y2="769.296" xlink:href="#B"><stop offset="0" stop-color="#aeadad"></stop><stop offset="1" stop-color="#edecec"></stop></linearGradient></defs><path fill="url(#k)" d="M371 759c5 1 12 1 17 2h3l-1 1v1h-1v1h-1v1c2-1 4-1 6-1h7c3 1 8 2 11 1l6 2c4 2 9 4 13 7 0 3 2 5 4 7v1h-1c-3-2-5-4-8-3h0-1c-2 0-2 0-4-1v1 1c1 0 1 0 1 1l6 3c-1 1-2 1-4 0l-2 1-4-2-4-1-1-1c-1-1-5-1-7-2-7-2-14-1-21 2v-2h-1l1-2-8 3c-1 0-2 1-2 1h-1l1-1-1-1h0c0-1 1-1 1-2v-1l-3 2-3 3c-1 2-3 3-5 5-3 0-6 1-9 1-1 0-2 0-2 1-1 1-1 2-1 2l-1 1-3-3h0l-3-2v-1l-2-1 2-1h1c0-1 0 0 1-1v-1l-5-1 3-1c2 0 5 1 6 0l-1-1-2-2 3 1h4l3-3h-4c3-2 7-4 11-6l12-5-3-3-3-1z"></path><path d="M369 781c-1 2-3 3-5 5-3 0-6 1-9 1l2-2c4-1 8-2 12-4z" class="Z"></path><path d="M409 771l7 2v1 1c1 0 0 0 1 1-4-2-8-2-12-3h-4c-1 1-2 1-3 1l-2-1c4 0 9-1 13-2z" class="C"></path><path d="M364 777c2 0 3-1 5-1-4 3-7 6-12 6-1 0-4 0-5-1 1-1 3-1 4-1 3 0 5-2 8-3z" class="Z"></path><path d="M345 783h1 0l5 2h1c2 1 3 0 5 0l-2 2c-1 0-2 0-2 1-1 1-1 2-1 2l-1 1-3-3h0l-3-2v-1l-2-1 2-1z" class="V"></path><path d="M345 783h1 0v2h-1l-2-1 2-1z" class="a"></path><path d="M352 785c2 1 3 0 5 0l-2 2c-1 0-2 0-2 1-1 1-1 2-1 2l-1 1-3-3h4c0-1 0-2-1-3h1z" class="U"></path><path d="M388 761h3l-1 1v1h-1v1h-1v1c-5 2-9 4-14 6h0c1-2 2-3 3-4h0c1-1 2-1 3-2 2-2 5-2 8-3v-1z" class="o"></path><path d="M348 776l3 1 4 1h2 0l-1 2c-1 0-3 0-4 1 1 1 4 1 5 1-3 1-5 1-7 1 0 1 1 1 2 2h-1l-5-2h0c0-1 0 0 1-1v-1l-5-1 3-1c2 0 5 1 6 0l-1-1-2-2z" class="E"></path><defs><linearGradient id="l" x1="434.857" y1="780.845" x2="411.767" y2="768.264" xlink:href="#B"><stop offset="0" stop-color="#8c8b8c"></stop><stop offset="1" stop-color="#b5b4b4"></stop></linearGradient></defs><path fill="url(#l)" d="M394 764h7c3 1 8 2 11 1l6 2c4 2 9 4 13 7 0 3 2 5 4 7v1h-1c-3-2-5-4-8-3l-9-3c-1-1 0-1-1-1v-1-1l-7-2h-1 2 2c1-1 1-3 1-4-2-2-5-1-8-1-3-1-7-1-10-2h-1z"></path><path d="M371 759c5 1 12 1 17 2v1c-3 1-6 1-8 3-1 1-2 1-3 2h0c-1 1-2 2-3 4h0l-2 2c-1 1-2 3-3 3-2 0-3 1-5 1-3 1-5 3-8 3l1-2h0-2l-4-1h4l3-3h-4c3-2 7-4 11-6l12-5-3-3-3-1z" class="V"></path><path d="M367 771l1 1h0-1l-1 1h-2l1 1h0c-3 1-6 2-10 3l3-3 3-1c2 0 3-1 5-1 0-1 0-1 1-1z" class="Z"></path><path d="M355 777c4-1 7-2 10-3 0 2 0 2-1 3-3 1-5 3-8 3l1-2h0-2l-4-1h4z" class="D"></path><path d="M367 771c1-1 2-1 3-2l6-3 1 1c-1 1-2 2-3 4h0l-2 2c-1 1-2 3-3 3-2 0-3 1-5 1 1-1 1-1 1-3h0l-1-1h2l1-1h1 0l-1-1z" class="l"></path><path d="M365 774c3 0 4-1 7-1-1 1-2 3-3 3-2 0-3 1-5 1 1-1 1-1 1-3h0z" class="C"></path><defs><linearGradient id="m" x1="406.166" y1="787.282" x2="393.722" y2="771.072" xlink:href="#B"><stop offset="0" stop-color="#7a7979"></stop><stop offset="1" stop-color="#bab9b9"></stop></linearGradient></defs><path fill="url(#m)" d="M396 773l2 1c1 0 2 0 3-1h4c4 1 8 1 12 3l9 3h0-1c-2 0-2 0-4-1v1 1c1 0 1 0 1 1l6 3c-1 1-2 1-4 0l-2 1-4-2-4-1-1-1c-1-1-5-1-7-2-7-2-14-1-21 2v-2h-1l1-2-8 3c1-1 2-1 3-2 1 0 2 0 3-1 1 0 2-1 3-1 1-1 3-1 5-2s3-1 5-1z"></path><path d="M420 780l2 1 6 3c-1 1-2 1-4 0l-2 1-4-2c0-1 0-2-1-3 0 0 1 0 2 1h1v-1z" class="N"></path><path d="M405 773c4 1 8 1 12 3l9 3h0-1c-2 0-2 0-4-1v1 1c1 0 1 0 1 1l-2-1c-5-3-9-5-14-6l-1-1z" class="O"></path><path d="M147 292c3-1 7-1 11-1 2-1 4-1 6-1l4 1h0l8 3h4 1l1-1 2 1c1 2 4 5 4 7-1 1 0 1-1 1l3 3c5 4 11 5 17 7h-1-3-1l1 1h1c1 0 1 1 2 1 1 1 3 0 5 1h0c1 0 2 1 2 1v3h1 0c0 2 0 3-1 4s-2 1-3 1h1c1 0 2 1 4 1l-6 1h4l-4 2c-2 0-5-1-7 0-7 0-15-4-22-8-10-7-22-16-34-17-3-1-6 0-9 0-2 0-4-1-5 0v1c-2 1-4 2-5 4h-1c1-3 4-5 7-7l1-1 1-1c2-1 4-2 6-2l3-1c-3 0-4 0-6-1l9-3z" class="q"></path><path d="M182 305c3 1 5 2 7 3-2 0-3 0-5 1 0 2-1 2-2 4-2-1-3-1-3-2h1 1v-1c1-1 1-1 0-2v-2l1-1z" class="N"></path><path d="M169 295l5 1 8 4c1 1 1 1 1 2-3-1-8-1-12-2h2v-1-1l-1-1c-1 0-2-1-3-2z" class="D"></path><path d="M182 313c1-2 2-2 2-4 2-1 3-1 5-1 2 1 4 2 5 3h0c-3 0-4-1-6-2-1 0-2 0-3 1h0c1 2 1 3 1 4v1c0 1 1 2 1 2-1 0-2-1-3-1 0-1-1-2-2-3z" class="f"></path><path d="M181 310c-1 0-2 0-3-1v-1h1c-1-2-5-3-7-4v-1c3 0 7 1 10 2l-1 1v2c1 1 1 1 0 2z" class="V"></path><path d="M162 294l4 1h3c1 1 2 2 3 2l1 1v1 1h-2c-5-1-11-2-16-2h-5 7c2-1 5-1 6-1l1-1-2-2z" class="E"></path><path d="M152 294h10l2 2-1 1c-1 0-4 0-6 1h-7-4-5v-1l3-1 8-2z" class="p"></path><path d="M168 291l8 3h4 1l1-1 2 1c1 2 4 5 4 7-1 1 0 1-1 1l-1-1-1 1 1 1v1c-1-1-2-1-3-2 0-1 0-1-1-2l-8-4c-2-2-3-3-6-4v-1z" class="Y"></path><path d="M182 293l2 1c1 2 4 5 4 7-1 1 0 1-1 1l-1-1c-2-2-7-5-10-7h4 1l1-1z" class="P"></path><path d="M147 292c3-1 7-1 11-1 2-1 4-1 6-1l4 1h0v1c3 1 4 2 6 4l-5-1h-3l-4-1h-10l-8 2c-3 0-4 0-6-1l9-3z" class="B"></path><path d="M152 294h-1v-1c4 0 8-1 12 0 1 1 1 1 3 2l-4-1h-10z" class="Q"></path><path d="M164 290l4 1h0v1c3 1 4 2 6 4l-5-1h-3c-2-1-2-1-3-2h4v-1l-3-1v-1z" class="f"></path><defs><linearGradient id="n" x1="201.263" y1="310.719" x2="190.283" y2="319.651" xlink:href="#B"><stop offset="0" stop-color="#645e60"></stop><stop offset="1" stop-color="#8d8c8f"></stop></linearGradient></defs><path fill="url(#n)" d="M187 317s-1-1-1-2v-1c0-1 0-2-1-4h0c1-1 2-1 3-1 2 1 3 2 6 2h0c2 1 4 2 6 2 4 2 8 2 10 6l1 1c-1 1-3 3-5 3h-4c-1 0-6-2-7-2-2-1-3-2-5-2l-3-2z"></path><path d="M190 319v-1l-1-2h1c3 1 5 3 9 4h0 1v1h-2l-2-1-1 1c-2-1-3-2-5-2z" class="W"></path><defs><linearGradient id="o" x1="168.312" y1="319.849" x2="172.166" y2="306.087" xlink:href="#B"><stop offset="0" stop-color="#c5c4c4"></stop><stop offset="1" stop-color="#ebeaea"></stop></linearGradient></defs><path fill="url(#o)" d="M134 300l1-1c2-1 4-2 6-2v1h5l1 1c7 1 13 3 20 7 6 3 11 7 17 10 1 0 2 1 3 1l3 2c2 0 3 1 5 2 1 0 6 2 7 2 2 1 5 2 7 2v1h4l-4 2c-2 0-5-1-7 0-7 0-15-4-22-8-10-7-22-16-34-17-3-1-6 0-9 0-2 0-4-1-5 0v1c-2 1-4 2-5 4h-1c1-3 4-5 7-7l1-1z"></path><path d="M134 300l1-1c2-1 4-2 6-2v1h5l1 1-13 1z" class="P"></path><defs><linearGradient id="p" x1="216.514" y1="345.93" x2="223.94" y2="353.607" xlink:href="#B"><stop offset="0" stop-color="#2b1614"></stop><stop offset="1" stop-color="#4e4545"></stop></linearGradient></defs><path fill="url(#p)" d="M240 315h2l1 1c1 1 2 2 3 4v1l-1 1-3 1 2 1 1-1c1 1 1 0 1 1l-1 6c0 2 1 6 0 7-1 2-2 3-2 5l-2 5v1l-1-1h0l-3 12c0 1-1 3-1 4-1 1-2 2-2 4-1 2-2 5-4 8l-5 4-1 1h-1 0l1-2c1-2 3-3 3-4-1-1-3 2-5 2h-1 0v-1l3-3h0-3c3-1 5-5 6-7l-1-1-2-2c-1 0-2 0-3 1l-3 3c-2 1-5 1-7 1-1 1-3 2-4 2-2 1-3 1-4 2l-1-1-1 1h-1c-1 1-2 2-3 2-1 1-1 1-2 1 2-2 7-4 9-6v-1c0-1 5-4 6-4 2-2 3-3 4-5v-2c0-3 4-5 3-9h-1l1-2 4-7h-1v-1l-1 1v-4h-2 0c-4 0-7-2-11-2-1-2-3-1-4-3v-1c2-1 5 0 7 0l4-2h-4l6-1c5-1 11-3 16-5 3-2 7-3 9-5z"></path><path d="M223 355c1-2 3-3 4-5l5-5c1-1 1-2 2-2v1 1c0 2-1 4-1 6l-1-1h-1 0-1v1c-2 1-4 3-7 4z" class="O"></path><path d="M223 355c3-1 5-3 7-4v-1h1 0 1l1 1-1 1c-1 1-2 0-3 1l-3 3c-1 0-1 0-2 1-1 0-2 1-3 1l-2-1c1-2 2-3 3-5v2h0l-1 1v1c1-1 2-1 2-1z" class="l"></path><path d="M219 357l2 1c1 0 2-1 3-1 1-1 1-1 2-1l3-3c1-1 2 0 3-1 0 2-1 4-1 6-2-1-6 1-9 2-2 1-4 1-7 2l4-5z" class="N"></path><path d="M215 362c3-1 5-1 7-2 3-1 7-3 9-2-1 2-2 5-4 7l-1-1-2-2c-1 0-2 0-3 1l-3 3c-2 1-5 1-7 1-1 1-3 2-4 2l8-7z" class="F"></path><path d="M241 331c2-2 3-4 4-7v1h0v5h0c0 2 1 6 0 7-1 2-2 3-2 5l-2 5v1l-1-1h0v-1h-3l2-6 2-6v-3z" class="r"></path><path d="M241 334c1 1 1 5 1 7h-1l-1-1h-1l2-6z" class="Z"></path><path d="M239 340h1l1 1-1 6h0v-1h-3l2-6z" class="M"></path><defs><linearGradient id="q" x1="223.204" y1="330.715" x2="223.838" y2="346.303" xlink:href="#B"><stop offset="0" stop-color="#656364"></stop><stop offset="1" stop-color="#918f90"></stop></linearGradient></defs><path fill="url(#q)" d="M230 329c0 1 0 1 1 1h1c-5 5-8 9-11 15-2 5-4 9-7 13v-2c0-3 4-5 3-9h-1l1-2 4-7c1-3 6-7 9-9z"></path><defs><linearGradient id="r" x1="239.003" y1="335.673" x2="230.29" y2="360.928" xlink:href="#B"><stop offset="0" stop-color="#281210"></stop><stop offset="1" stop-color="#433d3d"></stop></linearGradient></defs><path fill="url(#r)" d="M234 345c1 0 2-11 7-14v3l-2 6-2 6c-3 11-6 23-16 30v-1l3-3h0-3c3-1 5-5 6-7 2-2 3-5 4-7 0-2 1-4 1-6l1-1c0-2 1-4 1-6z"></path><defs><linearGradient id="s" x1="228.724" y1="350.785" x2="237.611" y2="370.022" xlink:href="#B"><stop offset="0" stop-color="#8a8889"></stop><stop offset="1" stop-color="#c2c1c2"></stop></linearGradient></defs><path fill="url(#s)" d="M237 346h3v1l-3 12c0 1-1 3-1 4-1 1-2 2-2 4-1 2-2 5-4 8l-5 4-1 1h-1 0l1-2c1-2 3-3 3-4-1-1-3 2-5 2h-1 0c10-7 13-19 16-30z"></path><defs><linearGradient id="t" x1="245.352" y1="322.477" x2="208.143" y2="335.277" xlink:href="#B"><stop offset="0" stop-color="#373333"></stop><stop offset="1" stop-color="#533e3e"></stop></linearGradient></defs><path fill="url(#t)" d="M240 315h2l1 1c1 1 2 2 3 4v1l-1 1-3 1c-2 1-5 3-8 5l-2 2h-1c-1 0-1 0-1-1-3 2-8 6-9 9h-1v-1l-1 1v-4h-2 0c-4 0-7-2-11-2-1-2-3-1-4-3v-1c2-1 5 0 7 0l4-2h-4l6-1c5-1 11-3 16-5 3-2 7-3 9-5z"></path><path d="M232 327c1 0 2 0 2 1l-2 2h-1c-1 0-1 0-1-1l2-2z" class="Z"></path><path d="M232 327c3-2 5-3 7-5 2 0 4-1 6 0l-3 1c-2 1-5 3-8 5 0-1-1-1-2-1z" class="U"></path><path d="M202 328c2-1 5 0 7 0h4v1 1h-1c-2 0-4 0-5 1h1 0c3 0 6 1 9 3-4 0-7-2-11-2-1-2-3-1-4-3v-1z" class="Y"></path><path d="M202 328c2-1 5 0 7 0h4v1 1c-4 0-7 0-11-1v-1z" class="B"></path><path d="M240 315h2l1 1c-2 4-11 7-15 9h-1l3-3h-1c1-1 2-1 2-2 3-2 7-3 9-5z" class="M"></path><defs><linearGradient id="u" x1="226.502" y1="326.681" x2="215.998" y2="323.319" xlink:href="#B"><stop offset="0" stop-color="#828084"></stop><stop offset="1" stop-color="#9a9a9a"></stop></linearGradient></defs><path fill="url(#u)" d="M215 325c5-1 11-3 16-5 0 1-1 1-2 2h1l-3 3c-4 2-9 5-14 4v-1h-4l4-2h-4l6-1z"></path><path d="M780 227c1 1 1 2 2 3 25 0 51-1 74-11 7-3 14-7 20-13 3-3 6-7 9-11 1-1 3-5 4-5 0 0 1 0 2-1 1 2 1 2 1 4 1 3 1 6 1 9s0 6-1 9c-1 6-7 12-10 16-12 12-26 22-40 30-9 4-19 9-27 16 3 2 6 4 10 4h1 1v1l-1 1h-1 0c0 1-1 2-1 3v3c1 0 1 1 2 2s2 3 4 4c-3 1-7 1-10 2h0c1 1 4 1 5 2l9-4h0l-3 3h0c-2 1-5 3-7 3l2 1h-1l-1 1c3-1 5-2 9-1-1 0-4 1-4 2-2 1-4 2-5 2-4 2-9 4-13 4l-16-2-5-1c-1 1-1 1-3 1v1l-1 1c-2 1-3 1-4 1l-2 1 1-1-2-1c7-8 14-17 22-24 6-6 12-12 19-16 9-5 18-9 27-14 14-9 27-19 37-32 3-2 5-5 6-9h0c2-7 1-15 1-21-4 6-7 11-12 16-9 9-19 14-32 18-25 8-52 8-79 8l1-1h2 8s0-1 1-1v-3h0z"></path><path d="M819 280c-3-2-6-5-8-7 4 1 7 3 10 5l-2 2z" class="T"></path><path d="M821 278l4 1h0c0 1-1 2-1 3v3h0l-1-1h0c-1-2-2-3-4-3v-1l2-2z" class="k"></path><path d="M786 301c1 2 2 2 4 2-1 1-1 1-3 1v1l-1 1c-2 1-3 1-4 1l-2 1 1-1c1-2 3-4 5-6z" class="N"></path><path d="M809 277l3-2c1 0 1 1 2 2 2 1 3 3 5 4h0c2 0 3 1 4 3h0l1 1h0c1 0 1 1 2 2s2 3 4 4c-3 1-7 1-10 2l-2-2v-1h-3l-5-1-8-6 2-1c2-2 3-4 5-5z" class="W"></path><path d="M809 277l3-2c1 0 1 1 2 2v4c2 2 5 3 6 5l2 1c-2 0-6-1-7-2s-1-2-2-2v-1c-1-1-1-2-2-3l-2-2z" class="B"></path><path d="M802 283l2-1c1 1 2 3 4 4 4 1 9 2 12 4h-2-3l-5-1-8-6z" class="e"></path><path d="M814 277c2 1 3 3 5 4h0c2 0 3 1 4 3h0l1 1h0c1 0 1 1 2 2s2 3 4 4c-3 1-7 1-10 2l-2-2v-1h2 4v-1l-2-2-2-1c-1-2-4-3-6-5v-4z" class="G"></path><path d="M798 288l4-5 8 6 5 1h3v1l2 2h0c1 1 4 1 5 2l9-4h0l-3 3h0c-2 1-5 3-7 3l2 1h-1l-1 1c3-1 5-2 9-1-1 0-4 1-4 2-2 1-4 2-5 2-4 2-9 4-13 4l-16-2-5-1c-2 0-3 0-4-2 0-1 1-1 1-2 1-1 2-2 3-2h0c0-2 1-3 3-4 1-1 2-2 3-2v-1l2-2z" class="L"></path><path d="M793 293l1 1 4 1c3 0 8 1 10 0 4-1 8-2 12-2 1 1 4 1 5 2-7 2-14 3-22 4-2 0-5 0-7-1-2 0-4 0-6-1h0c0-2 1-3 3-4z" class="U"></path><path d="M793 293l1 1 4 1-1 1-2-1-1 1c1 1 1 1 2 1v1c-2 0-4 0-6-1h0c0-2 1-3 3-4z" class="O"></path><path d="M787 299c1 0 2 1 3 1 2 0 4 1 6 2 6 1 12 1 18 0 3 0 8-2 11-2 1 1 2 0 4 0-2 1-4 2-5 2-4 2-9 4-13 4l-16-2-5-1c-2 0-3 0-4-2 0-1 1-1 1-2z" class="C"></path><path d="M798 288l4-5 8 6 5 1h3v1l2 2h0c-4 0-8 1-12 2-2 1-7 0-10 0l-4-1-1-1c1-1 2-2 3-2v-1l2-2z" class="i"></path><path d="M796 290c2 0 3 1 4 1-1 1-3 1-3 2l1 2-4-1-1-1c1-1 2-2 3-2v-1z" class="M"></path><path d="M815 290h3v1l2 2h0c-4 0-8 1-12 2-2-1-5 0-8-1 2-1 4 0 6 0s3 0 4-1v-1-1c1 0 3 0 4-1h1z" class="D"></path><path d="M815 290h3v1l-8 1v-1c1 0 3 0 4-1h1z" class="L"></path><path d="M798 288l4-5 8 6 5 1h-1c-1 1-3 1-4 1v1 1c-4 0-6-1-10-2-1 0-2-1-4-1l2-2z" class="E"></path><path d="M798 288l4-5 8 6c-2 0-3 0-4-1-1 0-3-1-4-1s-3 0-4 1z" class="Z"></path><defs><linearGradient id="v" x1="632.525" y1="792.298" x2="652.136" y2="763.311" xlink:href="#B"><stop offset="0" stop-color="#cfcece"></stop><stop offset="1" stop-color="#fcfcfb"></stop></linearGradient></defs><path fill="url(#v)" d="M612 764c7-1 14-2 21-1 5 1 9 3 13 5 1 0 4 1 4 1 1 2 2 2 4 3l6 4h0l6 4c1 1 2 2 4 2h0c1 1 3 1 4 1h0 5 2c-1 0-2 0-3 1-1 0-2 1-3 2l3-1h2l1 1c1-1 2-1 4-1l1 1 2-1 2 1 3 3c1 2 1 3 1 5h-1-2c-2 2-5 2-7 2l-1 3 1 2c-1 0-2 1-4 1 0-1-1-2-1-3l-2-1-6 3c-2-1-2-1-4-1 1 1 1 2 1 3l-2-1c1 2 1 4 2 7l-1 1-6-9-3-3-1-2-1-1c0-1-2-2-2-3-3-4-7-7-10-10l-2-1v1l-2-1-2 2c-6-3-12-3-18-2 0 0-1 0-2-1h-1c-4 0-9 2-13 3l-5 2c1-1 2-3 3-4 2 0 3-1 4-3-2 1-4 1-6 2v1l-1-1h1v-1c0-1 0-1 1-2 1 0 2 0 3-1l-1-1 2-1c-1-1-2-2-2-4 0-1 0-2 1-3 2-1 5-2 8-2v-1z"></path><path d="M640 777l6 4-2 1-2-1c-1-1-1-2-2-4z" class="W"></path><path d="M664 795c1 2 2 3 3 5 1 1 1 2 1 3l-2-1-1-3s0-1-1-2v-2z" class="N"></path><path d="M677 798c1-1 1-1 1-2l1 1c1 1 3 1 4 2l1 2c-1 0-2 1-4 1 0-1-1-2-1-3l-2-1z" class="H"></path><path d="M658 791l3 1 3 3v2c1 1 1 2 1 2l-5-4c-1-1-2-3-2-4z" class="F"></path><path d="M609 772c1 0 2-2 3-2 3-1 6-1 10 1l-13 1z" class="L"></path><path d="M650 783c2 0 3 1 5 3 2 1 4 4 6 6l-3-1c-1-3-6-6-8-8z" class="p"></path><defs><linearGradient id="w" x1="650.742" y1="789.974" x2="648.938" y2="781.383" xlink:href="#B"><stop offset="0" stop-color="#656364"></stop><stop offset="1" stop-color="#7c7979"></stop></linearGradient></defs><path fill="url(#w)" d="M644 782l2-1 4 2c2 2 7 5 8 8 0 1 1 3 2 4h0c-2-1-4-2-5-3 1 1 1 1 1 2v1c0-1-2-2-2-3-3-4-7-7-10-10z"></path><path d="M656 795v-1c0-1 0-1-1-2 1 1 3 2 5 3h0l5 4 1 3c1 2 1 4 2 7l-1 1-6-9-3-3-1-2-1-1z" class="O"></path><path d="M688 785l2 1 3 3c1 2 1 3 1 5h-1-2c-2 2-5 2-7 2l1-3c-1 1-2 2-2 3v2c-1-1-1-1-1-3l-1 1h-2v-6c2-2 4-3 7-4l2-1z" class="AA"></path><path d="M693 789c1 2 1 3 1 5h-1-2c-2 2-5 2-7 2l1-3 1-1c2-3 4-2 7-3z" class="B"></path><path d="M622 771h2c-2 1-3 0-5 1 3 1 5 0 8 1l3 1c-2 1-5 0-7 1l-12 3c-2 1-6 2-7 4h-1l1 1-5 2c1-1 2-3 3-4 2 0 3-1 4-3-2 1-4 1-6 2v1l-1-1h1v-1c0-1 0-1 1-2 1 0 2 0 3-1l-1-1 2-1 4-2 13-1z" class="W"></path><path d="M611 774c2-1 5-2 8-2 3 1 5 0 8 1-3 0-6 0-9 1-1 0-5 1-6 1s-1-1-1-1z" class="H"></path><path d="M622 771h2c-2 1-3 0-5 1-3 0-6 1-8 2-3 1-6 2-8 4 2 0 5-1 7-1h-1l2 1c-2 1-6 2-7 4h-1l1 1-5 2c1-1 2-3 3-4 2 0 3-1 4-3-2 1-4 1-6 2v1l-1-1h1v-1c0-1 0-1 1-2 1 0 2 0 3-1l-1-1 2-1 4-2 13-1z" class="Q"></path><defs><linearGradient id="x" x1="680.487" y1="788.965" x2="663.371" y2="784.494" xlink:href="#B"><stop offset="0" stop-color="#686465"></stop><stop offset="1" stop-color="#8a8786"></stop></linearGradient></defs><path fill="url(#x)" d="M662 785l-11-9c8 4 15 11 24 10l3-1h2l1 1c1-1 2-1 4-1l1 1c-3 1-5 2-7 4-1 2-2 3-4 3h-4-2c-2 0-6-5-7-8z"></path><path d="M674 788h1c1 2 1 1 0 2v3h-4c1-1 1-1 3-1v-1h-2v-1l2-1v-1z" class="Q"></path><path d="M666 787c2 1 5 1 8 1v1l-2 1v1c-1 0-1 0-2-1-2 0-3-2-4-3z" class="I"></path><path d="M662 785c1 1 1 1 2 1 1 1 1 1 2 1 1 1 2 3 4 3 1 1 1 1 2 1h2v1c-2 0-2 0-3 1h-2c-2 0-6-5-7-8z" class="F"></path><defs><linearGradient id="y" x1="620.89" y1="783.954" x2="622.077" y2="773.385" xlink:href="#B"><stop offset="0" stop-color="#8d8d8f"></stop><stop offset="1" stop-color="#c0bfbf"></stop></linearGradient></defs><path fill="url(#y)" d="M630 774c3 0 7 1 10 3 1 2 1 3 2 4v1l-2-1-2 2c-6-3-12-3-18-2 0 0-1 0-2-1h-1c-4 0-9 2-13 3l-1-1h1c1-2 5-3 7-4l12-3c2-1 5 0 7-1z"></path><path d="M617 780c8-2 16-2 23 1l-2 2c-6-3-12-3-18-2 0 0-1 0-2-1h-1z" class="X"></path><defs><linearGradient id="z" x1="358.883" y1="803.809" x2="364.271" y2="811.111" xlink:href="#B"><stop offset="0" stop-color="#8d8b8b"></stop><stop offset="1" stop-color="#aeacac"></stop></linearGradient></defs><path fill="url(#z)" d="M372 778l3-2v1c0 1-1 1-1 2h0l1 1-1 1h1s1-1 2-1l8-3-1 2h1v2c7-3 14-4 21-2 2 1 6 1 7 2l1 1h0c-2 0-2 0-3-1h-2 0l-1 1c-2-1-3-2-5-2l-1 1c-1 0-3 0-4 1h0c-3 0-5 1-8 2l-2 2c-3 2-5 4-6 7-1 0-2 1-3 2l-1 1c-1 2-2 3-2 5h-3l-1 2c-1 5-6 11-10 14s-9 6-13 6l-2-2h-1c-5 1-8-1-11-4-4-3-4-9-4-13v-3l2-2h1c-2-3-2-6-1-9l1-3c1-2 3-2 5-3h4l2 1v1l3 2h0l3 3 1-1s0-1 1-2c0-1 1-1 2-1 3 0 6-1 9-1 2-2 4-3 5-5l3-3z"></path><path d="M366 798l3-1c-2 5-6 9-9 13h0c-1-1-1-1-1-2s0 0 1-1c2-2 5-5 6-9z" class="i"></path><path d="M367 794l8-7 1 1c-2 3-6 6-7 9l-3 1c1-1 1-2 1-4z" class="U"></path><path d="M347 821c5-1 10-3 14-6v2h1c-4 3-9 6-13 6l-2-2z" class="a"></path><defs><linearGradient id="AA" x1="360.952" y1="797.584" x2="363.015" y2="805.291" xlink:href="#B"><stop offset="0" stop-color="#555050"></stop><stop offset="1" stop-color="#6b696a"></stop></linearGradient></defs><path fill="url(#AA)" d="M367 794c0 2 0 3-1 4-1 4-4 7-6 9-1 1-1 0-1 1-1 0-1 0-2-1 2-3 3-6 5-9l5-4z"></path><path d="M372 797h3 0l-2 4-1 2c-1 5-6 11-10 14h-1v-2c6-5 9-11 11-18z" class="m"></path><path d="M352 807v1c1-1 1-1 2-1l1 1h0l2-1c1 1 1 1 2 1 0 1 0 1 1 2h0c-3 2-4 4-7 4-2 2-5 3-7 4-1 1-2 2-4 2h0v-1l1-1h1c0-1 6-4 6-5s0-1 1-2l2-2-3 1c0-2 0-2 2-3z" class="Q"></path><path d="M359 808c0 1 0 1 1 2h0c-3 2-4 4-7 4l6-6z" class="I"></path><defs><linearGradient id="AB" x1="339.798" y1="802.533" x2="341.256" y2="817.524" xlink:href="#B"><stop offset="0" stop-color="#474141"></stop><stop offset="1" stop-color="#6e6b6d"></stop></linearGradient></defs><path fill="url(#AB)" d="M334 799l3 3c2 3 4 6 8 8h3 0 1 1l3-1-2 2c-1 1-1 1-1 2s-6 4-6 5h-1l-1 1v1l-2-1-3-2c-3-4-5-8-5-12v-1h-1v-3l2-2h1z"></path><path d="M334 799l3 3c2 3 4 6 8 8l-1 1c-2-1-4-4-6-6l-3-4h-1c0 5 1 9 4 14 1 1 2 2 2 4l-3-2c-3-4-5-8-5-12v-1h-1v-3l2-2h1z" class="K"></path><path d="M385 781c7-3 14-4 21-2 2 1 6 1 7 2l1 1h0c-2 0-2 0-3-1h-2 0l-1 1c-2-1-3-2-5-2l-1 1c-1 0-3 0-4 1h0c-3 0-5 1-8 2l-2 2c-3 2-5 4-6 7-1 0-2 1-3 2l-1 1c-1 2-2 3-2 5h-3l2-4h0-3c2-2 3-6 5-8s4-3 5-6l-6 5-1-1c3-3 7-5 10-6z" class="a"></path><path d="M396 780h7l-1 1c-1 0-3 0-4 1h0l-2-2z" class="e"></path><path d="M383 787c3-4 9-6 13-7l2 2c-3 0-5 1-8 2l-2 2c-2-1-3 0-5 1z" class="U"></path><path d="M383 787c2-1 3-2 5-1-3 2-5 4-6 7-1 0-2 1-3 2l-1 1c-1 2-2 3-2 5h-3l2-4c2-4 5-7 8-10z" class="B"></path><defs><linearGradient id="AC" x1="384.21" y1="776.427" x2="345.913" y2="795.816" xlink:href="#B"><stop offset="0" stop-color="#b9b7b7"></stop><stop offset="1" stop-color="#efeeee"></stop></linearGradient></defs><path fill="url(#AC)" d="M372 778l3-2v1c0 1-1 1-1 2h0l1 1-1 1h1s1-1 2-1l8-3-1 2h1v2c-3 1-7 3-10 6l-8 7-5 4c-2 3-3 6-5 9l-2 1h0l-1-1c-1 0-1 0-2 1v-1c-2 1-2 1-2 3h-1-1 0-3c-4-2-6-5-8-8l-3-3c-2-3-2-6-1-9l1-3c1-2 3-2 5-3h4l2 1v1l3 2h0l3 3 1-1s0-1 1-2c0-1 1-1 2-1 3 0 6-1 9-1 2-2 4-3 5-5l3-3z"></path><path d="M354 800l1 1c1 0 1 1 2 1s1 0 2-2c1-1 1-2 2-2h1c-2 3-3 6-5 9l-2 1h0l-1-1c-1 0-1 0-2 1v-1c1-2 2-3 2-6v-1z" class="p"></path><path d="M339 784h4l2 1v1l3 2c-2 0-3-1-4-1h-2c1 1 3 3 3 5 0 3 1 4 3 6 1 2 1 3 3 3l-1 1-1 1c-1 0-2-1-2-1l-1 1-1-1c-1-2 0-5 0-7l-2-3-1-2v-1l-1-2h-1l-1-2v-1z" class="T"></path><path d="M339 784h4l2 1v1h-3l-1 1h-1l-1-2v-1z" class="L"></path><path d="M345 795c1 3 2 5 5 7l-1 1c-1 0-2-1-2-1l-1 1-1-1c-1-2 0-5 0-7z" class="D"></path><path d="M372 778l1 1-2 2c-1 1 0 1-1 2 0 0-1 1-1 2-2 2-5 4-6 6s-2 3-3 3c0-1 1-2 0-2-1-1-2-1-2-1-2 0-4 0-5 1l-2-1 1-1s0-1 1-2c0-1 1-1 2-1 3 0 6-1 9-1 2-2 4-3 5-5l3-3z" class="j"></path><path d="M355 787c3 0 6-1 9-1-2 2-2 2-5 3-2 1-4 1-6 1h-1s0-1 1-2c0-1 1-1 2-1z" class="N"></path><path d="M343 792l2 3c0 2-1 5 0 7l1 1 1-1s1 1 2 1l1-1 1-1h1 1v-1h1v1c0 3-1 4-2 6-2 1-2 1-2 3h-1-1 0-3c-4-2-6-5-8-8 1-1 1 0 2 0l1-2 1 1c1-2 1-4 2-5v-4z" class="B"></path><path d="M351 801h1 1v-1h1v1l-3 6-1-1c-1-2-3-2-4-3l1-1s1 1 2 1l1-1 1-1z" class="E"></path><path d="M340 800l1 1c0 1-1 3 0 4l3 3h1c1 1 2 1 3 1 1-1 2-1 3-2l3-6c0 3-1 4-2 6-2 1-2 1-2 3h-1-1 0-3c-4-2-6-5-8-8 1-1 1 0 2 0l1-2z" class="Z"></path><defs><linearGradient id="AD" x1="335.613" y1="800.041" x2="336.869" y2="790.779" xlink:href="#B"><stop offset="0" stop-color="#757173"></stop><stop offset="1" stop-color="#8e8d8d"></stop></linearGradient></defs><path fill="url(#AD)" d="M334 787c1-2 3-2 5-3v1l1 2h1l1 2v1l1 2v4c-1 1-1 3-2 5l-1-1-1 2c-1 0-1-1-2 0l-3-3c-2-3-2-6-1-9l1-3z"></path><path d="M342 790l1 2v4l-1-2v-4z" class="M"></path><path d="M342 794l1 2c-1 1-1 3-2 5l-1-1c0-2 1-4 2-6z" class="N"></path><path d="M334 787c1-2 3-2 5-3v1l1 2h1l1 2-2 2c-3 0-5 1-7-1l1-3z" class="F"></path><path d="M334 787c1-2 3-2 5-3v1l1 2h0c-2 1-3 1-5 2 0-1-1-1-1-2z" class="D"></path><path d="M207 369c1 0 3-1 4-2 2 0 5 0 7-1l3-3c1-1 2-1 3-1l2 2 1 1c-1 2-3 6-6 7h3 0l-3 3v1h0 1c2 0 4-3 5-2 0 1-2 2-3 4l-1 2h0 1l-2 2-2 2h0l4-1c0 1-1 2-1 3l3-1 1 1-3 1h1c0 1 0 1 1 1-1 2-3 2-3 5h-1c0 1-2 2-2 2l-2 2c-1 1-3 1-4 3 0 1-1 1-1 3-1 1-3 3-5 3-1 0-2 0-2 1l-6 6-4 3 1 1c-1 1-1 2-2 3h0v1c-9 2-18 5-26 9v-1c3-2 7-4 10-5 0-3-1-7-1-10 0-5 2-9 4-14 1-2 2-4 3-7 0-2 0-2-1-4l1-1c-3-2-8-2-10-4-1-1-1-2-1-3l1-1c2-1 8-1 10 0 2 0 7-3 9-4 2-2 6-3 9-5 1-1 2-1 4-2z" class="k"></path><path d="M185 385c1-1 3-3 5-3s4 0 6 1l-2 1c-3 0-6 1-9 1z" class="H"></path><path d="M194 384h1c-1 1-1 3-2 3h-2-1-1v1c-1-1-2-2-4-2l-1-1h0 1c3 0 6-1 9-1z" class="U"></path><path d="M185 388c1 2 2 2 2 4s-2 4-2 6c-3 6-6 12-6 19 0 2 1 3 2 5 1 1 1 1 2 1s3-1 4-1v-1c-3-7-1-12 1-19 0 2 0 3-1 5 0 3-2 7 0 10 1 1 2 2 3 2l6-3 1 1c-1 1-1 2-2 3h0v1c-9 2-18 5-26 9v-1c3-2 7-4 10-5 0-3-1-7-1-10 0-5 2-9 4-14 1-2 2-4 3-7 0-2 0-2-1-4l1-1z" class="m"></path><path d="M207 369c1 0 3-1 4-2 2 0 5 0 7-1l3-3c1-1 2-1 3-1l2 2 1 1c-1 2-3 6-6 7 0-2 2-3 1-4l1-1v-1l-1 1c-1 1-4 3-7 3-4 1-9 5-14 6-1 0-2-2-3-1-2 1-3 1-4 1 2-2 6-3 9-5 1-1 2-1 4-2z" class="L"></path><path d="M196 383h1s-1 1-1 2v1 1c-1 1-2 2-1 4 0 3-1 7-1 10v3l1-3 2 3 3 3c1 0 3-1 4-1l4-2 1-1c2-1 4-2 5-3 0 1-1 1-1 3-1 1-3 3-5 3-1 0-2 0-2 1l-6 6-4 3-6 3c-1 0-2-1-3-2-2-3 0-7 0-10 1-2 1-3 1-5 0-1 2-3 2-5 1-4 1-6-1-9v-1h1 1 2c1 0 1-2 2-3h-1l2-1z" class="R"></path><path d="M195 401l2 3 3 3c-2 1-3 2-5 3-1 1-2 2-3 2h-1c0-3 1-5 3-8l1-3z" class="L"></path><path d="M197 404l3 3c-2 1-3 2-5 3h-2c1-1 1-2 1-2l3-3v-1z" class="H"></path><path d="M206 407l-6 6-4 3-6 3c-1 0-2-1-3-2-2-3 0-7 0-10 2 2 2 4 3 6v1l1 1c1-1 1-1 2-1 0-1 1-1 2-1l1-1c1 0 2 0 3-1 2-2 3-3 6-4h1z" class="F"></path><defs><linearGradient id="AE" x1="230.846" y1="381.193" x2="194.264" y2="398.367" xlink:href="#B"><stop offset="0" stop-color="#cbc9ca"></stop><stop offset="1" stop-color="#f0f0ef"></stop></linearGradient></defs><path fill="url(#AE)" d="M221 372h3 0l-3 3v1h0 1c2 0 4-3 5-2 0 1-2 2-3 4l-1 2h0 1l-2 2-2 2h0l4-1c0 1-1 2-1 3l3-1 1 1-3 1h1c0 1 0 1 1 1-1 2-3 2-3 5h-1c0 1-2 2-2 2l-2 2c-1 1-3 1-4 3-1 1-3 2-5 3l-1 1-4 2c-1 0-3 1-4 1l-3-3-2-3-1 3v-3c0-3 1-7 1-10-1-2 0-3 1-4v-1-1c0-1 1-2 1-2 1-1 5-2 6-2l12-6 6-3z"></path><path d="M223 380h1l-2 2-2 2h0l4-1c0 1-1 2-1 3-4 1-9 4-13 3l6-6c2-1 4-1 6-3h1z" class="i"></path><path d="M221 372h3 0l-3 3v1h0l-17 11v-1c2-2 5-3 7-4 1-1 1-1 2-1 1-1 2-3 3-4 0-1-1-1-1-2l6-3z" class="j"></path><path d="M197 383c1-1 5-2 6-2l12-6c0 1 1 1 1 2-1 1-2 3-3 4-1 0-1 0-2 1-2 1-5 2-7 4v1c-3 6-9 7-9 14l-1 3v-3c0-3 1-7 1-10-1-2 0-3 1-4v-1-1c0-1 1-2 1-2zm26 3l3-1 1 1-3 1h1c0 1 0 1 1 1-1 2-3 2-3 5h-1c0 1-2 2-2 2l-2 2c-1 1-3 1-4 3-1 1-3 2-5 3l-1 1c-1-1 0-1-1-2-1-2-1-3-1-6 1-2 3-3 5-4h-1-2c1-1 1-2 2-3 4 1 9-2 13-3z" class="E"></path><path d="M208 396c1-1 2-1 4-2l2 1-2 2h-1c-2 0-2 0-3-1z" class="g"></path><path d="M212 394c4-2 8-4 12-7h1c0 1 0 1 1 1-1 2-3 2-3 5h-1c0 1-2 2-2 2l-1-1c-2 0-3 1-5 1l-2-1z" class="I"></path><path d="M214 395c2 0 3-1 5-1l1 1-2 2c-1 1-3 1-4 3-1 1-3 2-5 3l-1 1c-1-1 0-1-1-2-1-2-1-3-1-6h2c1 1 1 1 3 1h1l2-2z" class="n"></path><path d="M206 396h2c1 1 1 1 3 1-2 2-3 2-4 4v1c-1-2-1-3-1-6z" class="C"></path><path d="M214 395c2 0 3-1 5-1l1 1-2 2c-1 1-3 1-4 3-1 1-3 2-5 3 1-1 4-4 4-6h-1l2-2z" class="G"></path><path d="M246 324c2 2 1 4 1 6s0 5 1 7c1 7 2 14 4 21 2 5 4 9 7 13l2 3c1 1 2 1 3 2v-3c2 3 3 5 5 7 1 1 1 1 1 2 2 3 5 6 9 8 0 1 1 1 2 2h-1l3 3v1l1 2 3 6c-1 1-1 1-2 1h-1c-3 0-5 0-8 1h-1c2-2 5-5 6-7h-4 0c-3-1-6-1-9-1l-4-1c-12 0-24 2-35 6l-6 3c-3 2-7 4-10 6s-6 4-10 6c-2 1-5 1-8 2h0c1-1 1-2 2-3l-1-1 4-3 6-6c0-1 1-1 2-1 2 0 4-2 5-3 0-2 1-2 1-3 1-2 3-2 4-3l2-2s2-1 2-2h1c0-3 2-3 3-5-1 0-1 0-1-1h-1l3-1-1-1-3 1c0-1 1-2 1-3l-4 1h0l2-2 2-2 1-1 5-4c2-3 3-6 4-8 0-2 1-3 2-4 0-1 1-3 1-4l3-12h0l1 1v-1l2-5c0-2 1-3 2-5 1-1 0-5 0-7l1-6z" class="c"></path><path d="M220 395s2-1 2-2c0 2-1 3 0 4-2 2-3 3-5 4-1-2 0-3 1-4l2-2z" class="M"></path><path d="M272 387l2 1h-1c-1 1-5 0-6 1 1 1 1 1 2 1h-5c-4 0-10 1-13-1 7-1 13-1 20-1l1-1z" class="C"></path><path d="M214 400c1-2 3-2 4-3-1 1-2 2-1 4-4 4-8 7-12 10-3 2-5 4-8 6l-1-1 4-3 6-6c0-1 1-1 2-1 2 0 4-2 5-3 0-2 1-2 1-3z" class="I"></path><path d="M218 406c2-1 4-2 7-3h4l-6 3c-3 2-7 4-10 6s-6 4-10 6c-2 1-5 1-8 2h0c1-1 1-2 2-3 3-2 5-4 8-6-1 3-3 5-5 6 1 0 3-1 4-2l2-1c4-2 8-5 12-8z" class="P"></path><defs><linearGradient id="AF" x1="262.738" y1="382.609" x2="265.262" y2="373.891" xlink:href="#B"><stop offset="0" stop-color="#281d19"></stop><stop offset="1" stop-color="#422a2b"></stop></linearGradient></defs><path fill="url(#AF)" d="M256 372c0-1 0-1 1-2l1 2 1-1 2 3c1 1 2 1 3 2v-3c2 3 3 5 5 7 1 1 1 1 1 2 2 3 5 6 9 8 0 1 1 1 2 2h-1l3 3v1c-3-3-6-5-9-8l-2-1c-3-2-11-9-15-8l-1-2h3 0c-1-2-2-3-3-5z"></path><path d="M264 373c2 3 3 5 5 7 1 1 1 1 1 2-4-2-6-5-9-8 1 1 2 1 3 2v-3z" class="f"></path><defs><linearGradient id="AG" x1="234.822" y1="380.513" x2="235.66" y2="394.693" xlink:href="#B"><stop offset="0" stop-color="#3c3535"></stop><stop offset="1" stop-color="#665f61"></stop></linearGradient></defs><path fill="url(#AG)" d="M236 363h1v1c0 2 1 4 2 6l2 2c1 1 2 2 4 3h4c2 0 3 0 4 1-1 1-2 2-3 2h2c1 0 2 0 3-1h1l1 2c-13 4-24 10-35 18-1-1 0-2 0-4h1c0-3 2-3 3-5-1 0-1 0-1-1h-1l3-1-1-1-3 1c0-1 1-2 1-3l-4 1h0l2-2 2-2 1-1 5-4c2-3 3-6 4-8 0-2 1-3 2-4z"></path><path d="M229 384l1 1c2-2 3-2 5-2l-6 5v-4z" class="N"></path><path d="M227 386v-1c1-1 1 0 1-1h1v4l-6 5c0-3 2-3 3-5-1 0-1 0-1-1h-1l3-1z" class="J"></path><defs><linearGradient id="AH" x1="238.726" y1="377.15" x2="229.781" y2="385.857" xlink:href="#B"><stop offset="0" stop-color="#625c5c"></stop><stop offset="1" stop-color="#857f7e"></stop></linearGradient></defs><path fill="url(#AH)" d="M230 375c2 0 3 0 5 1 1 1 2 1 3 1l6 3c-2 2-6 2-8 3h-1c-2 0-3 0-5 2l-1-1h-1c0 1 0 0-1 1v1l-1-1-3 1c0-1 1-2 1-3l-4 1h0l2-2 2-2 1-1 5-4z"></path><path d="M229 379c3 0 7-1 10 0-2 1-3 2-5 2-1-1-3-1-5-2z" class="Y"></path><path d="M225 379h4c2 1 4 1 5 2-3 1-7 2-10 2l-4 1h0l2-2 2-2 1-1z" class="o"></path><path d="M236 363h1v1c0 2 1 4 2 6l2 2c1 1 2 2 4 3h4c2 0 3 0 4 1-1 1-2 2-3 2-2 1-4 1-5 1l-1 1-6-3c-1 0-2 0-3-1-2-1-3-1-5-1 2-3 3-6 4-8 0-2 1-3 2-4z" class="C"></path><path d="M234 367c1 4 3 7 6 10 2 1 3 2 5 2l-1 1-6-3c-1 0-2 0-3-1-2-1-3-1-5-1 2-3 3-6 4-8z" class="K"></path><path d="M274 388c3 3 6 5 9 8l1 2 3 6c-1 1-1 1-2 1h-1c-3 0-5 0-8 1h-1c2-2 5-5 6-7h-4 0c-3-1-6-1-9-1l-4-1c-12 0-24 2-35 6h-4c-3 1-5 2-7 3 7-7 15-11 24-15 3-1 7-2 9-2 3 2 9 1 13 1h5c-1 0-1 0-2-1 1-1 5 0 6-1h1z" class="H"></path><path d="M274 388c3 3 6 5 9 8l1 2 3 6c-1 1-1 1-2 1h-1c-3 0-5 0-8 1h-1c2-2 5-5 6-7h-4 0c-3-1-6-1-9-1l-4-1h3c2-1 5-1 8-1v-1c-2-1-3-1-5-2-1 0-3 0-4-1h-3c-5-1-9-1-14 0-1 0-3 1-5 0 1 0 1 0 2-1h1c-2 0-3 1-4 0h-1c3-1 7-2 9-2 3 2 9 1 13 1h5c-1 0-1 0-2-1 1-1 5 0 6-1h1z" class="E"></path><path d="M267 397l10 1c2 0 5 1 7 0l3 6c-1 1-1 1-2 1h-1l1-1-1-3c-1-1-1-1-3-2h-4 0c-3-1-6-1-9-1l-4-1h3z" class="Y"></path><path d="M281 399c2 1 2 1 3 2l1 3-1 1c-3 0-5 0-8 1h-1c2-2 5-5 6-7z" class="O"></path><path d="M274 388c3 3 6 5 9 8l1 2c-2 1-5 0-7 0 0-2 1-4-1-5-2-2-4-3-7-3-1 0-1 0-2-1 1-1 5 0 6-1h1z" class="W"></path><path d="M246 324c2 2 1 4 1 6s0 5 1 7c1 7 2 14 4 21 2 5 4 9 7 13l-1 1-1-2c-1 1-1 1-1 2 1 2 2 3 3 5h0-3-1c-1 1-2 1-3 1h-2c1 0 2-1 3-2-1-1-2-1-4-1h-4c-2-1-3-2-4-3l-2-2c-1-2-2-4-2-6v-1h-1c0-1 1-3 1-4l3-12h0l1 1v-1l2-5c0-2 1-3 2-5 1-1 0-5 0-7l1-6z" class="N"></path><path d="M241 360l2-7c0-1 0-1 1-2l2 2-1 5v1c-2 0-3 0-4 1z" class="J"></path><path d="M241 360c1-1 2-1 4-1-2 3-3 7-5 10h-1v-4l2-5z" class="F"></path><path d="M245 359v-1c1 1 2 3 1 5s-2 4-3 5-1 2-1 2l-1 2-2-2 1-1c2-3 3-7 5-10z" class="O"></path><path d="M246 350l3 12c-1 1-1 2-1 3l-3 3c-1 0-1 1-1 2v1h-1l-1-1s0-1 1-2 2-3 3-5 0-4-1-5l1-5h0v-3z" class="q"></path><path d="M242 370l1 1h1v-1c0-1 0-2 1-2l3-3v1 8l1 1h-4c-2-1-3-2-4-3l1-2z" class="Y"></path><path d="M248 366v8l1 1h-4l1-1v-2c0-3 0-4 2-6z" class="e"></path><path d="M246 324c2 2 1 4 1 6s0 5 1 7c1 7 2 14 4 21 2 5 4 9 7 13l-1 1-1-2c-1 1-1 1-1 2 1 2 2 3 3 5h0-3-1c-1 1-2 1-3 1h-2c1 0 2-1 3-2-1-1-2-1-4-1l-1-1v-8-1c0-1 0-2 1-3l-3-12-1-13c1-1 0-5 0-7l1-6z" class="v"></path><path d="M252 358c2 5 4 9 7 13l-1 1-1-2c-1 1-1 1-1 2h-1c-1-2-1-2-1-4-2-2-3-5-3-7 0-1 0-2 1-3z" class="b"></path><path d="M248 365c0-1 0-2 1-3 2 5 3 10 6 15-1 1-2 1-3 1h-2c1 0 2-1 3-2-1-1-2-1-4-1l-1-1v-8-1z" class="Q"></path><path d="M783 435h1c1 1 2 1 2 2s0 1 1 3c3 0 4 1 6 3l1 3 5 2h0l2 2v1c1 1 3 3 4 5v3c2 3 4 5 6 8l1 3 1 1 2 3c1 1 3 2 4 4v1 1h1l1-2 1 2v1l-2 2v1l-4 5c0 2-1 4-1 6s0 2-1 4c2 9 0 18 3 27 1 3 2 5 4 8-1 0-3 0-4 1h0l-1 1c-1 0-2 1-3 2l-4 4c0-1 0-1-1-2h-1v1 1c-2 0-2 0-3 2v1h0c0 2 0 3-1 5h0v2l-1-1-2-4c0-2 0-2-2-3h-3l2 4h0c-1 0-2-1-3 0-1 0-2 1-2 2v-1c0-2 1-4 1-6h-1c-1-2 0-4 0-6l1-11c1-3 0-6 1-8v-17l-1-14c-1-3-1-6-1-8-1-2-2-3-3-5-1-3-1-6-3-9 0-1 0-2-1-4h0c1-1 1-3 1-5 0-1 1-1 1-2-1-3-2-8-3-11v-3s-1-2-2-2l1-3z" class="M"></path><path d="M796 534l-1-1c0-2 0-3 2-4l1 1h1c-1 2-2 3-3 4z" class="W"></path><path d="M793 543v-1c0-1 0-3 1-4h1c-1 1 0 4 0 6l2 4h0c-1 0-2-1-3 0-1 0-2 1-2 2v-1c0-2 1-4 1-6z" class="I"></path><path d="M795 538c3 1 3 3 5 5v-1c1 1 1 2 2 4h0c1 2 1 3 1 4v2l-1-1-2-4c0-2 0-2-2-3h-3c0-2-1-5 0-6z" class="B"></path><path d="M799 516c1 1 1 2 1 3l1 17c0 2 0 4-1 6-2-2-3-5-4-8 1-1 2-2 3-4 1-4 0-9 0-14z" class="C"></path><path d="M793 475l1 2c0 3 0 5 1 7-2 10 2 22-1 32v-11-4l-1-14c-1-3-1-6-1-8v-3h0l1-1z" class="V"></path><path d="M796 485c2 2 3 4 3 7v1l2 5v2c0 1-1 2 0 3 0 4 0 9-1 13v3c0-1 0-2-1-3v-1c-2-3-2-9-2-14l-1-16z" class="B"></path><path d="M799 515v-7c-1-5-1-10-1-15h1l2 5v2c0 1-1 2 0 3 0 4 0 9-1 13v3c0-1 0-2-1-3v-1z" class="E"></path><path d="M800 519v-3c1-4 1-9 1-13-1-1 0-2 0-3 1 3 3 8 4 9s1 1 0 2c-1 3 0 22 1 25 0 1 1 1 1 1 2-1 3-1 4-1s1 0 2-1h1 2 1 0l-1 1c-1 0-2 1-3 2l-4 4c0-1 0-1-1-2h-1v1 1c-2 0-2 0-3 2v1h0c0 2 0 3-1 5h0c0-1 0-2-1-4h0c-1-2-1-3-2-4h0c1-2 1-4 1-6l-1-17z" class="L"></path><path d="M795 468l2 5 1 6c1-3 2-4 1-7l1-1 1 1c0 2 0 2 1 4h0l2 8 2 1h1c0 1 1 2 2 3l3 5 2 6c2 9 0 18 3 27 1 3 2 5 4 8-1 0-3 0-4 1h-1-2-1c-1 1-1 1-2 1s-2 0-4 1c0 0-1 0-1-1-1-3-2-22-1-25 1-1 1-1 0-2s-3-6-4-9v-2l-2-5v-1c0-3-1-5-3-7l-1-1c-1-2-1-4-1-7v-7h0v-2h1z" class="c"></path><path d="M802 493l3 16c-1-1-3-6-4-9v-2c0-2 1-4 1-5z" class="B"></path><defs><linearGradient id="AI" x1="790.085" y1="476.166" x2="804.415" y2="487.334" xlink:href="#B"><stop offset="0" stop-color="#625f61"></stop><stop offset="1" stop-color="#929191"></stop></linearGradient></defs><path fill="url(#AI)" d="M795 468l2 5 1 6 4 14c0 1-1 3-1 5l-2-5v-1c0-3-1-5-3-7l-1-1c-1-2-1-4-1-7v-7h0v-2h1z"></path><path d="M804 484l2 1h1c0 1 1 2 2 3l3 5 2 6c2 9 0 18 3 27-1 0-1 0-1 1h-1l-2-4c0-1-1-3-1-4 0-2 0-4-1-6-1-6-2-11-4-17l-3-12z" class="E"></path><defs><linearGradient id="AJ" x1="797.984" y1="447.877" x2="792.19" y2="475.16" xlink:href="#B"><stop offset="0" stop-color="#b2b1b0"></stop><stop offset="1" stop-color="#ebeaec"></stop></linearGradient></defs><path fill="url(#AJ)" d="M783 435h1c1 1 2 1 2 2s0 1 1 3c3 0 4 1 6 3l1 3 5 2h0l2 2v1c1 1 3 3 4 5v3c2 3 4 5 6 8l1 3 1 1 2 3c1 1 3 2 4 4v1 1h1l1-2 1 2v1l-2 2v1l-4 5c0 2-1 4-1 6s0 2-1 4l-2-6-3-5c-1-1-2-2-2-3h-1l-2-1-2-8h0c-1-2-1-2-1-4l-1-1-1 1c1 3 0 4-1 7l-1-6-2-5h-1v2h0v7l-1-2-1 1h0v3c-1-2-2-3-3-5-1-3-1-6-3-9 0-1 0-2-1-4h0c1-1 1-3 1-5 0-1 1-1 1-2-1-3-2-8-3-11v-3s-1-2-2-2l1-3z"></path><path d="M792 450l2 9-1 3h0c-1-3-2-6-2-9h1v-3z" class="C"></path><path d="M794 459c2 4 3 9 3 14l-2-5-2-6 1-3z" class="P"></path><path d="M794 448l2 1 2 5 3 10 2 6-3-1c0-4-2-8-3-11h1c0-1-1-2-1-3-1-3-2-5-3-7z" class="D"></path><path d="M794 446l5 2h0l2 2v1h-1c-1 2 2 7 2 10l1 1-2 2-3-10-2-5-2-3z" class="O"></path><path d="M794 446l5 2h0l-1 1v2-1 4l-2-5-2-3z" class="V"></path><path d="M787 440c3 0 4 1 6 3l1 3 2 3-2-1-2-2-1 1c0 1 1 2 1 3v3h-1 0l-1 1h0v-1h-1l-2-7 1-3-1-3z" class="B"></path><path d="M788 443l3 10-1 1h0v-1h-1l-2-7 1-3z" class="a"></path><path d="M783 435h1c1 1 2 1 2 2s0 1 1 3l1 3-1 3 2 7h1v1h0l1-1h0c0 3 1 6 2 9v1h-2c0-2-1-4-1-5h-1 0l-2-4c-1-3-2-8-3-11v-3s-1-2-2-2l1-3z" class="K"></path><path d="M783 435h1c1 1 2 1 2 2s0 1 1 3l1 3-1 3c-1-1-1-2-2-3 0-1 0-2-1-3 0 0-1-2-2-2l1-3z" class="h"></path><path d="M787 454l2 4h0 1c0 1 1 3 1 5h2v-1h0l2 6h-1v2h0v7l-1-2-1 1h0v3c-1-2-2-3-3-5-1-3-1-6-3-9 0-1 0-2-1-4h0c1-1 1-3 1-5 0-1 1-1 1-2z" class="S"></path><path d="M789 458h0 1c0 1 1 3 1 5h2v-1h0l2 6h-1v2h0v7l-1-2-1 1h0l-3-18z" class="a"></path><path d="M793 462h0l2 6h-1v2h0v7l-1-2c-1-4 0-8-2-12h2v-1z" class="U"></path><defs><linearGradient id="AK" x1="800.725" y1="467.567" x2="813.281" y2="468.341" xlink:href="#B"><stop offset="0" stop-color="#7b7877"></stop><stop offset="1" stop-color="#a8a7a9"></stop></linearGradient></defs><path fill="url(#AK)" d="M803 462l-1-1c0-3-3-8-2-10h1c1 1 3 3 4 5v3c2 3 4 5 6 8l1 3 1 1 2 3c1 1 3 2 4 4v1 1h1l1-2 1 2v1l-2 2v1l-4 5c0 2-1 4-1 6s0 2-1 4l-2-6-3-5c-1-1-2-2-2-3h-1l-2-1-2-8c0-2-1-5-2-7l3 1-2-6 2-2z"></path><path d="M814 482v-4l-1 1c0-2-1-4 0-6l2 1c1 1 3 2 4 4v1c-1 1-3 3-5 3z" class="R"></path><path d="M821 478l1 2v1l-2 2v1l-4 5c0 2-1 4-1 6s0 2-1 4l-2-6v-1c1-1 1-3 2-5-1 0-1-1-1-2v-5l1 2c2 0 4-2 5-3v1h1l1-2z" class="N"></path><path d="M821 478l1 2v1l-2 2h-1l1-3 1-2z" class="Z"></path><path d="M814 482c2 0 4-2 5-3v1c-2 2-4 5-5 7-1 0-1-1-1-2v-5l1 2z" class="C"></path><path d="M803 462l1-1c1 1 2 2 2 4s1 3 2 6c1 4 3 9 5 14 0 1 0 2 1 2-1 2-1 4-2 5v1l-3-5c-1-1-2-2-2-3h-1l-2-1-2-8c0-2-1-5-2-7l3 1-2-6 2-2h0z" class="i"></path><path d="M806 471c0 3 4 14 3 17-2-5-4-10-5-14h1v1l1-4z" class="Z"></path><path d="M803 462h0l3 9-1 4v-1h-1c0-2-1-3-1-4l-2-6 2-2z" class="l"></path><path d="M800 469l3 1c0 1 1 2 1 4 1 4 3 9 5 14-1-1-2-2-2-3h-1l-2-1-2-8c0-2-1-5-2-7z" class="G"></path><defs><linearGradient id="AL" x1="701.245" y1="764.356" x2="701.968" y2="754.134" xlink:href="#B"><stop offset="0" stop-color="#959393"></stop><stop offset="1" stop-color="#c1c0c0"></stop></linearGradient></defs><path fill="url(#AL)" d="M656 749c12 0 22 2 34 4 2-1 4 0 7 1l13 2 13 2c14 2 27 4 41 3 2 1 2 1 4 1 1 0 1 0 2 1h0c3 0 8-1 11-3l5-2h7c-3 2-6 3-9 4-1 0-3 1-4 1v1l-1 1v1c-2 2-4 3-6 4s-5 1-7 4c-1 0-1 0-2 1-6 1-13 1-19 0-2-1-4 0-5-1-3 0-6-1-9-2l-12-2h-1 0c-4-1-6-2-8-4l-1 1h0c0 1 1 2 0 4h0-3-5l-3-1h-2c-1 1-3 1-4 1l-9 4c-2 0-4 0-5 1-9 0-19-4-28-7 0 0-3-1-4-1-4-2-8-4-13-5-7-1-14 0-21 1l-4 1v-1c-3 0-7 2-10 3l1-1v-1h-1l-1-1-4 1c3-2 6-3 9-3 2-1 4-1 6-1 3 0 6-1 8-3l3-1 7-2 7-2 1 1c2-1 3-1 5-1l6-1 1-1h3c2-1 3-1 5-1 1 0 1 0 2-1z"></path><path d="M639 758s-1-1-1-2h2 1 0 1c1 0 2 1 3 2h-6z" class="g"></path><path d="M648 758l13-2 2 1v1c-2 0-4 1-6 0h0c-3-1-6 1-9 0z" class="X"></path><path d="M710 756l13 2c-1 0-2 1-2 1-4 0-9-1-11-3z" class="G"></path><path d="M634 754c2-1 3-1 5-1h1l-2 1h3l-12 3h-1c2-2 4-2 6-3z" class="K"></path><path d="M645 752l1-1 8 1v1l-9 1h-4-3l2-1h-1l6-1z" class="V"></path><path d="M649 751c4 0 17-1 20 2h-11-4v-1l-8-1h3z" class="Z"></path><path d="M661 756l19 1-1 1h-16v-1l-2-1z" class="m"></path><path d="M786 758h7c-3 2-6 3-9 4-1 0-3 1-4 1l-8 2c1-1 1-1 3-1 1-1 1-1 2-1h1c-4 0-6 1-10 0h2c3 0 8-1 11-3l5-2z" class="B"></path><path d="M683 757l1-1h-4c2-1 3-1 6-1 1 1 2 0 3 0h1 1 2c1 0 0 0 2 1h3 2v1h2v1h0c-1 0-2 0-3 1l-16-2z" class="R"></path><path d="M772 765l8-2v1l-1 1c-9 2-17 3-26 3h0c1-1 0-1 1-1l-1-1c6 0 13 0 19-1z" class="Y"></path><path d="M656 749c12 0 22 2 34 4h-2c-6-1-13 1-19 0-3-3-16-2-20-2 2-1 3-1 5-1 1 0 1 0 2-1z" class="W"></path><path d="M633 753l1 1c-2 1-4 1-6 3h1c-4 2-9 4-13 4-3 0-6 0-9 1-3 0-6 3-8 4v-1h-1l-1-1-4 1c3-2 6-3 9-3 2-1 4-1 6-1 3 0 6-1 8-3l3-1 7-2 7-2z" class="Y"></path><path d="M680 757h3l16 2 36 6-2 1-3-1-11-1c-5-2-11-2-16-3-5 0-10-2-15-1l-9-2 1-1z" class="b"></path><path d="M645 758h3c3 1 6-1 9 0h0l-19 3c-1 0-2 1-4 1l-1 1c-7-1-14 0-21 1l-4 1v-1c10-3 21-4 31-6h6z" class="v"></path><defs><linearGradient id="AM" x1="734.212" y1="777.259" x2="743.64" y2="759.143" xlink:href="#B"><stop offset="0" stop-color="#7c7c7e"></stop><stop offset="1" stop-color="#a9a4a4"></stop></linearGradient></defs><path fill="url(#AM)" d="M688 760c5-1 10 1 15 1 5 1 11 1 16 3l11 1 3 1 2-1 18 1 1 1c-1 0 0 0-1 1h0c9 0 17-1 26-3v1c-2 2-4 3-6 4s-5 1-7 4c-1 0-1 0-2 1-6 1-13 1-19 0-2-1-4 0-5-1-3 0-6-1-9-2l-12-2h-1 0c-4-1-6-2-8-4l-1 1h0c0 1 1 2 0 4h0-3-5l-3-1 2-2c-1-2-2-3-3-4l-3-2c-2 0-4-2-6-2z"></path><path d="M712 766l-1-1v-1c3-1 5-1 8 0l11 1-5 2-12-2-1 1z" class="N"></path><path d="M735 765l18 1 1 1c-1 0 0 0-1 1h0-2c-7 0-14 1-21 0l-5-1 5-2 3 1 2-1z" class="Q"></path><path d="M730 765l3 1 5 1c-3 0-6 0-8 1l-5-1 5-2z" class="M"></path><path d="M735 765l18 1 1 1c-1 0 0 0-1 1l-15-1-5-1 2-1z" class="K"></path><defs><linearGradient id="AN" x1="774.05" y1="764.231" x2="770.263" y2="772.686" xlink:href="#B"><stop offset="0" stop-color="#676566"></stop><stop offset="1" stop-color="#8b898b"></stop></linearGradient></defs><path fill="url(#AN)" d="M753 768c9 0 17-1 26-3v1c-2 2-4 3-6 4s-5 1-7 4c-1 0-1 0-2 1l-1-1h1c-1-1-3-1-4-1h0l-1-1 6-1v-1c-3-2-9-2-13-1h0l-1-1h2z"></path><path d="M688 760c5-1 10 1 15 1 5 1 11 1 16 3-3-1-5-1-8 0v1l1 1c2 2 3 2 6 3h0c3 0 5 0 7 1 1 0 3 0 4 1l8 1c1 0 2 1 3 2-3 0-6-1-9-2l-12-2h-1 0c-4-1-6-2-8-4-1-1-3-2-4-2h0c-2-1-3-1-6-2h-6c-2 0-4-2-6-2z" class="U"></path><path d="M694 762h6c3 1 4 1 6 2h0c1 0 3 1 4 2l-1 1h0c0 1 1 2 0 4h0-3-5l-3-1 2-2c-1-2-2-3-3-4l-3-2z" class="N"></path><path d="M706 764c1 0 3 1 4 2l-1 1h0c0 1 1 2 0 4h0c-2-1-3-4-3-6v-1z" class="I"></path><path d="M694 762h6l4 2-1 1h0c-2 1-2 2-3 3-1-2-2-3-3-4l-3-2z" class="M"></path><path d="M663 758h16l9 2c2 0 4 2 6 2l3 2c1 1 2 2 3 4l-2 2h-2c-1 1-3 1-4 1l-9 4c-2 0-4 0-5 1-9 0-19-4-28-7 0 0-3-1-4-1-4-2-8-4-13-5l1-1c2 0 3-1 4-1l19-3c2 1 4 0 6 0z" class="W"></path><path d="M654 763c1 0 2 0 2-1 1-1 6-1 8-2l-3 1 1 1h0c1 0 2 0 3 1h-11z" class="B"></path><path d="M664 760h13 3l1 1v1c-2 0-4 0-5 1h-11c-1-1-2-1-3-1h0l-1-1 3-1z" class="C"></path><path d="M633 763l1-1c2 0 3-1 4-1 2 1 3 1 4 2h2c2 1 4 1 6 0v1h0c-1 0-1 0-2 1h0c2 1 2 1 3 2s1 1 3 2c3 2 7 3 11 4 2 1 4 1 6 2 3 1 7 0 10-1 4-1 7-3 11-3l-9 4c-2 0-4 0-5 1-9 0-19-4-28-7 0 0-3-1-4-1-4-2-8-4-13-5z" class="U"></path><path d="M697 764c1 1 2 2 3 4l-2 2h-2c-1 1-3 1-4 1-4 0-7 2-11 3-3-1-7-1-10-2l-14-3-1-1c-1 0-1 0-2-1 3 0 6 1 8 2 5 0 10 1 15 0 1 0 3 0 4-1 1 0 2 0 2-1h1c2 0 2 0 4-1 1-1 1-1 2-1 1 1 1 2 1 3h0l1-1v-1c1-2 3-2 5-2z" class="B"></path><path d="M697 764c1 1 2 2 3 4l-2 2h-2v-1c-1 1-1 1-2 1h-1-3l1-2 1-1v-1c1-2 3-2 5-2z" class="G"></path><path d="M692 767l1 1 1-1v1l-1 2h-3l1-2 1-1z" class="D"></path><path d="M680 760c1 1 3 1 5 2h3l1 1-1 1-4 3h-1c0 1-1 1-2 1-1 1-3 1-4 1-5 1-10 0-15 0-2-1-5-2-8-2h0c-1-1-1-2-1-3l1-1h11 11c1-1 3-1 5-1v-1l-1-1z" class="H"></path><path d="M680 760c1 1 3 1 5 2h3l1 1-1 1-1-1h-11c1-1 3-1 5-1v-1l-1-1z" class="B"></path><path d="M778 322l3 2v4l1 1h1c1 1 1 1 2 3h0v1 1l1 1c3 2 6 10 8 13l3 6h0c2 3 6 8 10 9h1c2 1 3 2 5 3 4 3 7 4 11 8 5 2 13 6 19 5h5c1 2 1 2 1 3-3 4-8 4-12 7-1 0-1 0-1 2 0 5 3 11 4 16 1 4 2 10 1 14l-1 1h-5v-1c-1 0-1 1-2 2l-12-4 1-1 4 1h0l-3-4a57.31 57.31 0 0 0-11-11l-8-6c-2 0-7-5-9-7l-2-3-4-4c-3-2-5-4-7-7l1-1c2 0 4 3 5 4l1-1c2 1 3 2 5 3l1-1-1-1h0v-1c-2-3-5-4-7-6-8-7-10-15-11-26h0l-1-7v-4l-1-5v-1-8h4z" class="c"></path><path d="M836 414l1 3c0 1 1 2 2 3 1 0 1 1 2 1l-1 1h-5v-1c1-2 1-5 1-7z" class="p"></path><path d="M831 383h1c1 1 3 1 4 1h0c-2 1-4 3-5 5 0 2 1 6 1 8h-1v-2l-2-5c0-2-1-3-1-5l1-1h0l2-1z" class="K"></path><path d="M831 395v2h1l4 17c0 2 0 5-1 7-1 0-1 1-2 2l-12-4 1-1 4 1h0l-3-4 1-1 4 4v-1-2c0-2 1-3 1-4v-3c1-2 1-4 1-6 0-1-1-3-1-5h0l2-2z" class="a"></path><path d="M833 405c1 2 1 11 0 14 0 1-1 1-1 2l-2-1 1-1v-3c0-3 1-7 2-10v-1z" class="e"></path><path d="M833 406c0 2 0 10-1 12l-1 1v-3c0-3 1-7 2-10z" class="l"></path><path d="M831 395v2h1l1 8v1c-1 3-2 7-2 10v3l-1 1-1-1-1-1v-1-2c0-2 1-3 1-4v-3c1-2 1-4 1-6 0-1-1-3-1-5h0l2-2z" class="Z"></path><path d="M831 395v2h1v4c0 1-1 2-1 3v1l-1-3c0-1-1-3-1-5h0l2-2z" class="f"></path><path d="M830 402l1 3v7l-1 4c0 1 0 2-1 3l-1-1v-1-2c0-2 1-3 1-4v-3c1-2 1-4 1-6z" class="J"></path><path d="M789 357c1-1 1-2 1-3l-1-4 2-1h1c1 2 2 4 4 5h1 0c2 3 6 8 10 9h1c2 1 3 2 5 3 4 3 7 4 11 8-2-1-3-1-5-2-3-1-7-2-10-4l-5-2c-2 0-3-1-5-1h-3l-1-1-1 1 2 2c-2 0-3 0-4-1h1l-4-9z" class="W"></path><path d="M794 364l-1-3c-1-2-2-4-2-6 2 2 4 4 6 5 3 0 6 2 8 4l10 5c1 1 3 2 4 3-3-1-7-2-10-4l-5-2c-2 0-3-1-5-1h-3l-1-1-1 1v-1z" class="j"></path><path d="M794 364h0c1-1 1 0 2-1l-1-1 1-1h0c5 3 9 3 13 7l-5-2c-2 0-3-1-5-1h-3l-1-1-1 1v-1z" class="G"></path><defs><linearGradient id="AO" x1="812.727" y1="390.513" x2="819.899" y2="379.904" xlink:href="#B"><stop offset="0" stop-color="#908d8d"></stop><stop offset="1" stop-color="#afacab"></stop></linearGradient></defs><path fill="url(#AO)" d="M802 374c2 1 5 2 7 2l17 6 1 1h4l-2 1h0l-1 1c0 2 1 3 1 5l2 5-2 2h0c0 2 1 4 1 5 0 2 0 4-1 6 0-3 0-7-2-9s-3-6-5-8l-1-1c-2-1-3-2-5-2l-2-1h-1c-2 0-8-1-9 0l-2 1v1h0c-2 1-3 1-4 1-2-1-4-2-5-2l-4-4c-3-2-5-4-7-7l1-1c2 0 4 3 5 4l1-1c2 1 3 2 5 3l1-1-1-1h0v-1h1l1-1h2 0v-2s0-1 1-1 1 0 2-1h1z"></path><path d="M828 390h-1 0c0-1-1-1-2-2-1 0-1-1-2-2l2-3 1-1 1 1-1 1 1 1c-1 1-2 0-3 1l4 3v1z" class="B"></path><path d="M802 374c2 1 5 2 7 2l-2 1v1 1c-3-1-7-3-9-3 0 0 0-1 1-1s1 0 2-1h1z" class="N"></path><path d="M827 383h4l-2 1h0l-1 1c0 2 1 3 1 5l2 5-2 2h0c-1-2-1-4-1-7v-1l-4-3c1-1 2 0 3-1l-1-1 1-1z" class="J"></path><path d="M782 377l1-1c2 0 4 3 5 4l1-1c2 1 3 2 5 3l1-1-1-1h0v-1h1l1-1h2c1 2 2 3 4 5 3 0 6 1 9 2 0 0 1 0 1 1 1 0 1 0 2-1l2 3-2-1h-1c-2 0-8-1-9 0l-2 1v1h0c-2 1-3 1-4 1-2-1-4-2-5-2l-4-4c-3-2-5-4-7-7z" class="C"></path><path d="M794 379h1l1-1h2c1 2 2 3 4 5-2 0-4-1-7-2l-1-1h0v-1z" class="P"></path><path d="M789 379c2 1 3 2 5 3s5 2 7 3c3 1 4 1 7 1v-1l1 1 5 1h-1c-2 0-8-1-9 0l-2 1v1h0c-3-2-5-3-8-4-2-2-4-3-6-5l1-1z" class="n"></path><path d="M782 377l1-1c2 0 4 3 5 4 2 2 4 3 6 5 3 1 5 2 8 4-2 1-3 1-4 1-2-1-4-2-5-2l-4-4c-3-2-5-4-7-7z" class="V"></path><path d="M802 389v-1l2-1c1-1 7 0 9 0h1l2 1c2 0 3 1 5 2l1 1c2 2 3 6 5 8s2 6 2 9v3c0 1-1 2-1 4v2 1l-4-4-1 1a57.31 57.31 0 0 0-11-11l-8-6c-2 0-7-5-9-7l-2-3c1 0 3 1 5 2 1 0 2 0 4-1h0z" class="W"></path><path d="M802 389c1 1 2 2 3 2l-3 2-4-3c1 0 2 0 4-1h0z" class="Z"></path><path d="M805 391h4c1 1 1 2 1 3 1 1 2 3 4 4 0 1 1 1 1 2l-13-7 3-2z" class="M"></path><path d="M804 398c4 0 6 4 9 6 1 0 2-1 3-1l2 2c1 1 2 2 2 3h-1c1 2 4 4 5 6l-1 1a57.31 57.31 0 0 0-11-11l-8-6z" class="Q"></path><path d="M827 399c2 2 2 6 2 9v3c-1 2-2 2-2 5 0-1-2-2-2-2 0-1 0-2-1-2-1-2-2-4-3-5l1-1-1-1h2c2-2 3-3 4-6z" class="B"></path><path d="M802 389v-1l2-1c1-1 7 0 9 0h1l2 1c2 0 3 1 5 2l1 1c2 2 3 6 5 8-1 3-2 4-4 6h-2c-1-1-3-2-4-4-2-1-3-3-3-6h0v-2l-1-2c0-1 1-1 1-2-1 0-2 1-3 1 0 1-1 1-2 1h-4c-1 0-2-1-3-2z" class="p"></path><defs><linearGradient id="AP" x1="792.282" y1="350.568" x2="780.115" y2="369.365" xlink:href="#B"><stop offset="0" stop-color="#8a8789"></stop><stop offset="1" stop-color="#c8c8c8"></stop></linearGradient></defs><path fill="url(#AP)" d="M778 322l3 2v4l1 1h1c1 1 1 1 2 3h0v1 1l1 1c3 2 6 10 8 13l3 6h-1c-2-1-3-3-4-5h-1l-2 1 1 4c0 1 0 2-1 3l4 9h-1c1 1 2 1 4 1s5 1 6 3l-1 1c0-1 0-2-1-2h-1c-2 1-2 1-4 1l1 1c1 0 2 1 3 1v1l3 1h-1c-1 1-1 1-2 1s-1 1-1 1v2h0-2l-1 1h-1c-2-3-5-4-7-6-8-7-10-15-11-26h0l-1-7v-4l-1-5v-1-8h4z"></path><path d="M775 339l2-2h1c1 1 1 3 1 4h0-1c-2 2-2 4-2 6h0l-1-7v-1z" class="M"></path><path d="M793 370l6 3 3 1h-1c-1 1-1 1-2 1s-1 1-1 1v2c-2-3-5-5-5-8z" class="K"></path><defs><linearGradient id="AQ" x1="776.379" y1="349.96" x2="795.712" y2="357.576" xlink:href="#B"><stop offset="0" stop-color="#7d7c7d"></stop><stop offset="1" stop-color="#bcbcbd"></stop></linearGradient></defs><path fill="url(#AQ)" d="M779 344l1-1c0-1 0-3 1-4h1 0c0 2 1 3 2 4 2 6 3 18 8 23 1 1 2 1 4 1s5 1 6 3l-1 1c0-1 0-2-1-2h-1c-2 1-2 1-4 1l1 1c1 0 2 1 3 1v1l-6-3h-1v-2l-1 1h-1l-2-1c-2-1-3-3-4-5-3-5-4-13-5-19z"></path><path d="M784 363l2 1v-1h0c1 2 3 4 4 5v1l-2-1c-2-1-3-3-4-5z" class="B"></path><path d="M778 322l3 2v4l1 1h1c1 1 1 1 2 3h0v1 1l1 1c3 2 6 10 8 13l3 6h-1c-2-1-3-3-4-5h-1l-2 1 1 4c0 1 0 2-1 3l4 9h-1c-5-5-6-17-8-23-1-1-2-2-2-4h0-1c-1 1-1 3-1 4l-1 1v-3h0c0-1 0-3-1-4h-1l-2 2v1-4l-1-5v-1-8h4z" class="s"></path><defs><linearGradient id="AR" x1="777.007" y1="330.703" x2="780.828" y2="337.221" xlink:href="#B"><stop offset="0" stop-color="#544a4c"></stop><stop offset="1" stop-color="#696666"></stop></linearGradient></defs><path fill="url(#AR)" d="M774 330c1-1 1-2 2-3 2 0 3 0 4 1l1 3c1 3 3 9 3 12-1-1-2-2-2-4h0-1c-1 1-1 3-1 4l-1 1v-3h0c0-1 0-3-1-4h-1l-2 2v1-4l-1-5v-1z"></path><path d="M774 330c1-1 1-2 2-3 2 0 3 0 4 1l1 3c-1-1-1-1-2-1h-2c-2 2-2 6-2 9h0v1-4l-1-5v-1z" class="O"></path><defs><linearGradient id="AS" x1="786.19" y1="333.104" x2="785.595" y2="348.445" xlink:href="#B"><stop offset="0" stop-color="#4f3f41"></stop><stop offset="1" stop-color="#717273"></stop></linearGradient></defs><path fill="url(#AS)" d="M787 351l-3-12c0-3-1-5-1-7v-1l2 2v1l1 1c3 2 6 10 8 13l3 6h-1c-2-1-3-3-4-5h-1l-2 1 1 4c0 1 0 2-1 3l-2-6z"></path><path d="M787 351c2-2 1-4 2-6h1c1 2 2 2 2 4h-1l-2 1 1 4c0 1 0 2-1 3l-2-6z" class="M"></path><path d="M221 148c1-1 2-1 3-1 4 0 7 0 11 2 7 3 10 10 13 18 1 2 1 4 3 5 2 2 5 2 8 1 5 0 9-3 14-5 4-3 10-6 13-11 4-6 3-16-1-23-2-4-4-5-5-10 0-10 10-16 8-26 0-3-3-6-5-8s-5-3-5-5l1-2c2 0 3 1 4 1 6 4 11 8 13 16v1c3-2 5-3 8-4 4-1 8-2 11 0 2 2 3 3 4 5l-1 1s-2 0-2-1c-3 0-5-1-8 0-1 1-2 2-2 4-2 5 0 13 3 18 2 5 5 8 11 9 8 3 19 0 26-4 6-4 11-10 13-16v-1c1-2 1-5 1-7 1 1 1 2 2 2 2 4 3 7 5 11 0 0-1 1-1 2 0 2 2 2 1 4l-3 1c-2 1-3 3-4 4v1 2h0c-2 2-1 7-1 9s-1 4-2 5c-2 3-4 3-7 4-3 0-7 0-9-1-1 0-2-1-3-1l-1-1c-2-2-6-1-9 0l-6-3-5-3v1c1 1 2 2 4 3l1 1c-1 1-3 2-3 4l-1-3c-1-1-3-2-4-4-1-1-2-2-4-2l-1-1c0-1-1-3-2-5l-2-4-4-9v1c-1-1-1-3-2-4h-2l-1-1-1 4c0 2 0 4-2 6l1 4c-1 1-1 2-3 1 3 5 3 9 3 14v1c-2 8-5 15-12 20v1l-14 6v1c0 1-1 1-2 1-2 1-5 1-7 2l1 3h0-2c-1 1-1 2-2 3s-3 2-3 4l-3-3h-1l1 1v2c-2 1-1 2-2 4-1-5-3-7-5-11l-3-3-2-2 1-2v-8c0-2 0-4-1-6-1-1-1-2-1-4-1-2-3-3-5-5l-1 2-3-2c0-1 0-1-1-2l-5-1-1-1h0z" class="e"></path><path d="M221 148c5-1 10 0 13 2l1 1h-1c-1 0-2-1-2-1h-1l1 1v1l-1 2-3-2c0-1 0-1-1-2l-5-1-1-1z" class="k"></path><path d="M227 150c2 0 4 1 5 2l-1 2-3-2c0-1 0-1-1-2z" class="a"></path><path d="M361 120l2-4c1 2 2 5 2 7l-1 2c-2 1-3 3-4 4v-2c1-1 1-2 2-3h-2-1l2-4z" class="G"></path><path d="M352 126c6-6 8-11 10-19 0 2 1 5 1 7 0 1-2 4-3 6l-1 1c-2 3-3 5-6 7 0 0 1-1 1-2h1c1-1 3-4 3-5-2 2-3 4-6 5z" class="d"></path><path d="M295 104c4-4 9-6 15-7 2 1 4 1 6 3v1h0-1v-1c-5-1-9 0-12 2-3 1-5 4-6 6-3 1-4 3-5 5-1 1-1 2-1 3-1 2-1 1-3 1-1 0-1 0-1-2 3-2 6-8 8-11z" class="c"></path><path d="M282 126c-4-9 10-17 8-27-1-7-7-11-12-15 3 1 6 2 9 5 5 4 7 8 8 15-2 3-5 9-8 11h-1c0 1 0 1-1 2s-1 2-2 4h0v5h-1z" class="T"></path><path d="M291 116c0-1 0-2 1-3 1-2 2-4 5-5l-1 2c1 0 1-1 2-1v2c-1 2-1 5-1 8l-1-1-1 4c0 2 0 4-2 6l1 4c-1 1-1 2-3 1l-2-4c0-3-1-3-2-5l-2 2h-2v-5h0c1-2 1-3 2-4s1-1 1-2h1c0 2 0 2 1 2 2 0 2 1 3-1z" class="L"></path><path d="M287 124c0-1 0 0-1-1h0v-1c2 0 3 1 4 2h1c0-2 0-2 1-4v2c0 2 0 3 1 5v1h0l1 4c-1 1-1 2-3 1l-2-4c0-3-1-3-2-5z" class="B"></path><path d="M291 116c0-1 0-2 1-3 1-2 2-4 5-5l-1 2c1 0 1-1 2-1v2c-1 2-1 5-1 8l-1-1-1 4c0 2 0 4-2 6h0v-1c-1-2-1-3-1-5v-2-3l-1-1z" class="n"></path><path d="M298 109v2c-1 2-1 5-1 8l-1-1c0-2 0-4 1-6v-2c-1 1-2 2-2 3l1-3c1 0 1-1 2-1z" class="B"></path><path d="M291 116c0-1 0-2 1-3 1-2 2-4 5-5l-1 2-1 3c-1 1-2 2-2 4 0 1 0 3-1 5v-2-3l-1-1z" class="H"></path><defs><linearGradient id="AT" x1="274.008" y1="120.402" x2="251.097" y2="176.54" xlink:href="#B"><stop offset="0" stop-color="#d7d6d6"></stop><stop offset="1" stop-color="#fdfcfc"></stop></linearGradient></defs><path fill="url(#AT)" d="M287 124c1 2 2 2 2 5l2 4c3 5 3 9 3 14v1c-2 8-5 15-12 20v1l-14 6v1c0 1-1 1-2 1-2 1-5 1-7 2l1 3h0-2c-1 1-1 2-2 3s-3 2-3 4l-3-3h-1l1 1v2c-2 1-1 2-2 4-1-5-3-7-5-11l-3-3-2-2 1-2v-8c0-2 0-4-1-6-1-1-1-2-1-4-1-2-3-3-5-5v-1l-1-1h1s1 1 2 1h1c6 2 9 9 11 15 1 3 2 6 5 8 4 2 9 1 12 0 4-1 8-3 11-5 6-3 11-6 14-12 3-7 3-17-1-23-1-2-4-4-5-7v-1h1 2l2-2z"></path><path d="M237 157c3 3 3 6 5 10v7c0-1 0 0-1-1l-2-6c0-2 0-4-1-6-1-1-1-2-1-4z" class="W"></path><path d="M242 167c2 4 4 8 9 10 5 3 12 0 17-2v1c0 1-1 1-2 1-2 1-5 1-7 2-5 1-9-1-13-3l-2 2-1-1-1-2v-1-7z" class="D"></path><path d="M239 167l2 6c1 1 1 0 1 1v1l1 2 1 1 2-2c4 2 8 4 13 3l1 3h0-2c-1 1-1 2-2 3s-3 2-3 4l-3-3h-1l1 1v2c-2 1-1 2-2 4-1-5-3-7-5-11l-3-3-2-2 1-2v-8z" class="H"></path><path d="M239 175c1 0 1 1 2 2s1 3 2 4v1l-3-3-2-2 1-2z" class="T"></path><path d="M247 181c2 0 6 1 7 2h1v1l-1 1c-1 0-1 0-2 1l-5-5z" class="d"></path><path d="M239 167l2 6c1 1 1 0 1 1v1l1 2 1 1c1 0 1 1 2 2l-5-3c-1-1-1-2-2-2v-8z" class="Q"></path><path d="M301 105l5-3v1c-3 6-2 13 0 19l2 4 4 4h0v1c3 2 6 3 10 4h0 2c10 2 20-3 28-9 3-1 4-3 6-5 0 1-2 4-3 5h-1c0 1-1 2-1 2 3-2 4-4 6-7l1-1h1l-2 4h1 2c-1 1-1 2-2 3v2 1 2h0c-2 2-1 7-1 9s-1 4-2 5c-2 3-4 3-7 4-3 0-7 0-9-1-1 0-2-1-3-1l-1-1c-2-2-6-1-9 0l-6-3-5-3v1c1 1 2 2 4 3l1 1c-1 1-3 2-3 4l-1-3c-1-1-3-2-4-4-1-1-2-2-4-2l-1-1c0-1-1-3-2-5l-2-4-4-9v1c-1-1-1-3-2-4h-2c0-3 0-6 1-8v-2c1-2 2-3 3-4z" class="k"></path><path d="M308 126l4 4-1 1c0 1 0 1-1 2-1-3-2-4-2-7z" class="T"></path><path d="M301 105c0 2-1 4-2 5 0 2 1 7 1 9l1 3v1c-1-1-1-3-2-4h-2c0-3 0-6 1-8v-2c1-2 2-3 3-4z" class="g"></path><path d="M360 120h1l-2 4c-3 4-6 7-10 9l-12 7v-1c-2 1-3 1-4 1h-1c-1 0-2 0-3-1 9-2 17-3 24-11 3-2 4-4 6-7l1-1z" class="B"></path><path d="M315 139l-5-7c6 6 11 7 19 7 1 1 2 1 3 1h1c1 0 2 0 4-1v1h5c3 1 6-1 8-2h0l-4 2c-2 1-3 2-3 3 2 4 3 5 7 7-3 0-7 0-9-1-1 0-2-1-3-1l-1-1c-2-2-6-1-9 0l-6-3-5-3-2-2z" class="C"></path><path d="M315 139c2 0 4 1 6 2 1 1 2 2 3 2h5v1h-6-1l-5-3-2-2z" class="D"></path><path d="M360 124h2c-1 1-1 2-2 3v2 1 2h0c-2 2-1 7-1 9s-1 4-2 5c-2 3-4 3-7 4-4-2-5-3-7-7 0-1 1-2 3-3l4-2h0c-2 1-5 3-8 2h-5l12-7c4-2 7-5 10-9h1z" class="d"></path><path d="M360 124h2c-1 1-1 2-2 3v2 1l-3 3v-2c1-3 2-4 4-6l-1-1z" class="H"></path><path d="M357 133l3-3v2h0c-2 2-1 7-1 9s-1 4-2 5v-1-2c0-1 1-8 0-9l-2 2-1-1 3-2z" class="R"></path><path d="M642 125c4-7 11-12 16-19 1-3 2-6 2-9 1-2 0-6 1-7 0-1 1-1 1-1v1c3 8 0 17 2 25 1 5 8 11 13 13 7 4 18 7 26 5 4-2 8-6 10-11 2-4 4-11 2-16-3-7-7-2-11-4 0-1 0-2 1-2 1-2 4-3 6-3 5-1 11 3 14 6h0c-5-2-10-7-16-5-2 1-3 1-4 3h0 2c3-1 6 0 9 1l2 2 1 3 2 22c0 1 0 3-1 5v1c0 3-1 5-2 7l-4 5-1 3h-1-1l-2 2c-2 2-6 5-9 5v-1l-10 3h-3l-9 1h0c-3 1-6 1-10 0-2 0-4 0-6 1l-4-1h-3v-1h2l-12-1-2-1 1-1 3 1v-1l2-2c1-4 1-9 0-13l-1-2c-3-2-5-3-8-4 0-3 1-5 2-8v-2z" class="K"></path><path d="M663 116c5 9 12 13 21 17h-1-1 0l1 2v1c-3-2-7-3-8-5h0-1l-2-1v1l-3-3-5-4c0-3 0-4-1-6h-1l1-2z" class="T"></path><path d="M716 129h1c1-1 2-3 2-4v-1h1c0 1 0 2-1 3 0 2-3 5-3 8 0 2-1 4-1 6-1 2-2 4-4 5-1-1-1-1-1-2h0l-2-1c-1 1-1 1-2 1l1-2h-1c-1 0-1 0-2-1l6-4c2-2 3-3 5-6 0-1 1-1 1-2z" class="j"></path><path d="M710 137l2 1 2-2c1 1 0 2 0 4h-1l-3 4-2-1c-1 1-1 1-2 1l1-2h-1c-1 0-1 0-2-1l6-4z" class="N"></path><path d="M707 142c1 0 1-1 2-2h1 1v-1h1l1 1-3 4-2-1c-1 1-1 1-2 1l1-2z" class="O"></path><defs><linearGradient id="AU" x1="637.893" y1="126.787" x2="665.241" y2="115.771" xlink:href="#B"><stop offset="0" stop-color="#d5d3d0"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#AU)" d="M642 127c6-10 17-16 20-28v-2 1l-1 10c1 3 1 5 2 8l-1 2h1c1 2 1 3 1 6l-2-2c-1 1-2 2-5 2-1 0-2 0-3 1-2 2-4 5-6 8v1c-1 2 0 4 0 5-3-2-5-3-8-4 0-3 1-5 2-8z"></path><path d="M659 114c0-2 1-4 2-6 1 3 1 5 2 8l-1 2h1c1 2 1 3 1 6l-2-2c-1 1-2 2-5 2-1 0-2 0-3 1 2-4 3-7 5-11z" class="C"></path><path d="M659 114c0-2 1-4 2-6 1 3 1 5 2 8l-1 2h1c1 2 1 3 1 6l-2-2h0c-2-3-2-5-3-8z" class="E"></path><path d="M684 133c7 2 15 3 22-1 5-2 9-8 10-13v-1c1-4 1-8 1-13 3 9 2 15-1 24 0 1-1 1-1 2-2 3-3 4-5 6l-6 4c1 1 1 1 2 1h1l-1 2c-2 0-3 1-5 2-1 0-2 1-3 2l-1-1h-4v-2l-8 2h-3c-3 1-7 1-11 1-1 0-3-1-4-2 0-2 0-3 1-4l3-4 1-2 1-5h-1v-1l2 1h1 0c1 2 5 3 8 5v-1l-1-2h0 1 1z" class="c"></path><path d="M704 141c1 1 1 1 2 1h1l-1 2c-2 0-3 1-5 2-1-2-2-1-4-2l7-3z" class="f"></path><path d="M693 145l4-1c2 1 3 0 4 2-1 0-2 1-3 2l-1-1h-4v-2z" class="F"></path><path d="M673 131c0 1 1 1 1 2s-1 4 0 5c0 2 3 4 5 4 1 1 1 2 4 2h0 3l-3 1c-3 0-4-2-6-3-2-2-4-3-6-4l1-2 1-5z" class="d"></path><path d="M686 144h0l6-2c-2-2-4-4-7-5h4c2 1 6 2 8 1h1 1 1c2-1 3-2 5-3l3-2v1c-1 1-2 1-3 2-3 2-6 4-10 5-1 1-2 1-3 1-1 1-2 2-3 2-1 1-3 1-4 3h-3v-1c-2 0-4-1-6-2l1-2c2 1 3 3 6 3l3-1z" class="L"></path><path d="M668 142l3-4c2 1 4 2 6 4l-1 2c2 1 4 2 6 2v1c-3 1-7 1-11 1-1 0-3-1-4-2 0-2 0-3 1-4z" class="k"></path><path d="M668 142l3-4c2 1 4 2 6 4l-1 2c-2-1-3-2-5-3-1 1-1 1-2 1h-1z" class="H"></path><path d="M654 125c1-1 2-1 3-1 3 0 4-1 5-2l2 2 5 4 3 3h1l-1 5-1 2-3 4c-1 1-1 2-1 4 1 1 3 2 4 2 4 0 8 0 11-1h3l8-2v2h4l1 1c1-1 2-2 3-2 2-1 3-2 5-2 1 0 1 0 2-1l2 1h0c0 1 0 1 1 2 2-1 3-3 4-5 1 2 1 2 0 4-1 1-1 2-1 2l-1 3h-1-1l-2 2c-2 2-6 5-9 5v-1l-10 3h-3l-9 1h0c-3 1-6 1-10 0-2 0-4 0-6 1l-4-1h-3v-1h2l-12-1-2-1 1-1 3 1v-1l2-2c1-4 1-9 0-13l-1-2c0-1-1-3 0-5v-1c2-3 4-6 6-8z" class="J"></path><path d="M656 149l1-1v1c2 2 5 4 7 6 2 0 4 0 7-1h2 3v1h-1l-6 1h-5c-3 0-5-1-7-4l-1-3z" class="O"></path><path d="M657 131c1-2 1-2 3-2 0 1 0 1 1 2v2l-2 5c1 4 1 8 3 12h1l-1 1c-1-2-3-3-4-5 0-2-1-4-1-6-1-2-2-3-2-4 0-2 1-4 2-5z" class="C"></path><path d="M657 131c1-2 1-2 3-2 0 1 0 1 1 2v2l-2 5-1-1h-1v-6z" class="j"></path><path d="M658 137l-1-1c1-2 2-2 4-3l-2 5-1-1z" class="D"></path><path d="M655 147l1 2 1 3c2 3 4 4 7 4h5c-1 1-2 2-4 2v1h1c-3 1-6 0-9 0l-12-1-2-1 1-1 3 1v-1l2-2 1 1c2 0 4 1 5 0 0-3-1-4 0-7v-1z" class="h"></path><path d="M664 156h5c-1 1-2 2-4 2v1l-9-1c3 0 5 0 8-2z" class="U"></path><path d="M657 152c2 3 4 4 7 4-3 2-5 2-8 2l-5-1h4c1-1 2-1 3-1 0-1-1-2-1-3v-1z" class="J"></path><path d="M655 147l1 2 1 3v1c0 1 1 2 1 3-1 0-2 0-3 1h-4-4v-1l2-2 1 1c2 0 4 1 5 0 0-3-1-4 0-7v-1z" class="F"></path><path d="M669 128l3 3h1l-1 5-1 2-3 4c-1 1-1 2-1 4h-1c-2-2-2-7-3-9 0 4 0 6 1 10l-2-4v-1c-1-1-1-5-1-7 1-1 2-2 2-3l-1-1h-1c-1-1-1-1-1-2 1 0 2 0 3 1 2 0 2 0 3-1 1 0 2 0 3-1z" class="I"></path><path d="M664 134v-3c2-1 3 0 5 1 0 0 1 1 2 1l1 1v-3h1l-1 5-8-2z" class="C"></path><path d="M664 134l8 2-1 2-3 4c-1 1-1 2-1 4h-1c-2-2-2-7-3-9l1-3z" class="j"></path><path d="M663 137c1 2 1 7 3 9h1c1 1 3 2 4 2 4 0 8 0 11-1h3l8-2v2h4l1 1c-6 3-13 6-20 5l-2 1h-3c-2-1-5-2-7-4 0-1 0-2-1-3h-1c-1-4-1-6-1-10z" class="R"></path><path d="M663 137c1 2 1 7 3 9h1c1 1 3 2 4 2h1c1 1 3 1 4 1-1 1-2 1-3 2 1 2 3 1 5 2l-2 1h-3c-2-1-5-2-7-4 0-1 0-2-1-3h-1c-1-4-1-6-1-10z" class="B"></path><defs><linearGradient id="AV" x1="666.677" y1="127.054" x2="651.11" y2="138.313" xlink:href="#B"><stop offset="0" stop-color="#7c7676"></stop><stop offset="1" stop-color="#928f90"></stop></linearGradient></defs><path fill="url(#AV)" d="M654 125c1-1 2-1 3-1 3 0 4-1 5-2l2 2 5 4c-1 1-2 1-3 1-1 1-1 1-3 1-1-1-2-1-3-1-2 0-2 0-3 2-1 1-2 3-2 5 0 1 1 2 2 4 0 2 1 4 1 6h0c-1 0-2-1-2-1-1 0-1 1-1 2h0v1c-1 3 0 4 0 7-1 1-3 0-5 0l-1-1c1-4 1-9 0-13l-1-2c0-1-1-3 0-5v-1c2-3 4-6 6-8z"></path><path d="M648 133h0c1 1 2 2 3 2 1 1 2 3 2 4v2c-2-2-4-4-5-7v-1z" class="B"></path><path d="M648 139c0-1-1-3 0-5 1 3 3 5 5 7 1 2 1 4 2 6h0v1c-1 3 0 4 0 7-1 1-3 0-5 0l-1-1c1-4 1-9 0-13l-1-2z" class="D"></path><path d="M711 146c2-1 3-3 4-5 1 2 1 2 0 4-1 1-1 2-1 2l-1 3h-1-1l-2 2c-2 2-6 5-9 5v-1l-10 3h-3l-9 1h0c-3 1-6 1-10 0-2 0-4 0-6 1l-4-1h-3v-1h2c3 0 6 1 9 0h-1v-1c2 0 3-1 4-2l6-1h1v-1l2-1c7 1 14-2 20-5 1-1 2-2 3-2 2-1 3-2 5-2 1 0 1 0 2-1l2 1h0c0 1 0 1 1 2z" class="J"></path><path d="M675 155l1 2c1-1 2-1 3-1h1l-9 3c7 0 13-1 19-2v2h-3l-9 1h0c-3 1-6 1-10 0-2 0-4 0-6 1l-4-1h-3v-1h2c3 0 6 1 9 0h-1v-1c2 0 3-1 4-2l6-1z" class="l"></path><path d="M675 155l1 2c-2 0-5 0-7 2h-3-1v-1c2 0 3-1 4-2l6-1z" class="y"></path><path d="M706 144c1 0 1 0 2-1l2 1h0c-2 2-3 2-5 3l-2 1-3 3-1-2c-4 2-11 6-16 5 0 1-1 2-1 2h-2-1c-1 0-2 0-3 1l-1-2h1v-1l2-1c7 1 14-2 20-5 1-1 2-2 3-2 2-1 3-2 5-2z" class="V"></path><path d="M699 149l2-1h2l-3 3-1-2z" class="Q"></path><path d="M676 155l7-1c0 1-1 2-1 2h-2-1c-1 0-2 0-3 1l-1-2h1z" class="M"></path><path d="M711 146c2-1 3-3 4-5 1 2 1 2 0 4-1 1-1 2-1 2l-1 3h-1-1l-2 2c-2 2-6 5-9 5v-1l-10 3v-2c3-1 5-1 8-3h-2v-1c1 0 3-1 4-2h0l3-3 2-1c2-1 3-1 5-3 0 1 0 1 1 2z" class="N"></path><path d="M700 156c4-3 9-6 12-10h1 0c0 2-1 3-2 4l-2 2c-2 2-6 5-9 5v-1zm10-12c0 1 0 1 1 2-2 2-8 7-10 7l4-4-7 5h-2v-1c1 0 3-1 4-2h0l3-3 2-1c2-1 3-1 5-3z" class="C"></path><path d="M296 118l1 1h2c1 1 1 3 2 4v-1l4 9 2 4c1 2 2 4 2 5l1 1c2 0 3 1 4 2 1 2 3 3 4 4l1 3c0-2 2-3 3-4l-1-1c-2-1-3-2-4-3v-1l5 3 6 3c3-1 7-2 9 0l1 1c1 1 2 2 2 3l-7 1h-1v1c-1 1-3 2-5 2v1l1 1h0c1-1 2-1 3 0l-23 15c-2 0-4 1-6 3-4 3-7 8-11 10l-7 7-6 10c-1 2-3 4-4 6v1l-2 2c-1 2-2 4-3 7-1 0-2-1-3-1v2c0 1 2 2 1 4h-1 0v3l-2 1c-1 1-4 0-5-1-2-1-2-3-4-3-2-2-3-5-3-7-2-6-1-11 0-17 1-2 1-8 1-10s2-3 3-4 1-2 2-3h2 0l-1-3c2-1 5-1 7-2 1 0 2 0 2-1v-1l14-6v-1c7-5 10-12 12-20v-1c0-5 0-9-3-14 2 1 2 0 3-1l-1-4c2-2 2-4 2-6l1-4z" class="c"></path><path d="M298 148c2 3 3 5 4 8 1 1 2 3 2 4v1c-1 1-1 1-1 2l-2 2c-1 0-1 1-2 1l-1 1v-1c1-2 2-5 3-7l-2-5v-1c0-1 0-3-1-5z" class="d"></path><path d="M317 141l5 3 6 3c-2 1-2 1-3 3 2 1 2 1 4 1 1 1 3 0 4 1h-1c-1 0-3 0-4 1l-3 1-2-4h-1 0c-1 3-3 4-6 5 0 0-1 0-1-1 1-1 3-2 3-2 1-1 1-1 1-2 0-2 2-3 3-4l-1-1c-2-1-3-2-4-3v-1z" class="H"></path><path d="M328 147c3-1 7-2 9 0l1 1c1 1 2 2 2 3l-7 1c-1-1-3 0-4-1-2 0-2 0-4-1 1-2 1-2 3-3z" class="d"></path><path d="M296 166h2v1c-2 2-4 5-6 7l-4 5c-1 0-2 0-3-1v-5c1-1 2-1 3-2h0c1 0 2-1 3-1 2-1 3-2 5-4z" class="U"></path><path d="M288 171l1 1h0l-1 2c1 0 1 1 2 0h2l-4 5c-1 0-2 0-3-1v-5c1-1 2-1 3-2z" class="M"></path><path d="M309 140l1 1c2 0 3 1 4 2 1 2 3 3 4 4l1 3c0 1 0 1-1 2 0 0-2 1-3 2 0 1 1 1 1 1-3 2-5 3-8 6-1 0-1 1-2 1h-1l8-6v-1c-1 0-1 0-2 1h-1l-3-1h-1c-1-2-3-4-3-6 1 1 2 2 4 3v-1l-1-2 1-1 1 1c1 1 2 1 3 0v-2-1c-2-2-2-3-2-6z" class="T"></path><path d="M309 140l1 1 3 3c2 2 3 2 5 4v2l-1-1c-1 1-1 0-2 1h0c-1-1-3-2-4-3v-1c-2-2-2-3-2-6z" class="j"></path><path d="M311 147c1 1 3 2 4 3h0c1-1 1 0 2-1l1 1-6 3c-2 0-3 0-5-1v-1l-1-2 1-1 1 1c1 1 2 1 3 0v-2z" class="J"></path><path d="M296 118l1 1h2c1 1 1 3 2 4h0c1 5 0 10-1 15v-4h-1l-1 1-1 1c-1 3-1 4 0 6v4l1 1v1c1 2 1 4 1 5-1-1-2-4-2-5h-3v-1c0-5 0-9-3-14 2 1 2 0 3-1l-1-4c2-2 2-4 2-6l1-4z" class="E"></path><path d="M294 147l2-3c0 1 1 3 1 4h-3v-1z" class="B"></path><g class="R"><path d="M294 132c1 2 1 4 1 6s1 4 1 6l-2 3c0-5 0-9-3-14 2 1 2 0 3-1z"></path><path d="M296 118l1 1h2c1 1 1 3 2 4h0l-1 1v2l-5 7 1-5c0-2 0-3-1-5v-1l1-4z"></path></g><path d="M297 119h2c0 3-1 6-2 9v-9z" class="W"></path><path d="M301 122l4 9 2 4c1 2 2 4 2 5 0 3 0 4 2 6v1 2c-1 1-2 1-3 0l-1-1-1 1 1 2v1c-2-1-3-2-4-3-2-4-3-7-3-11 1-5 2-10 1-15h0v-1z" class="f"></path><path d="M305 131l2 4h-1s-1 0-1-1c-2 3-2 9-1 11l3 3-1 1h-1l-2-2c-2-4-2-8-1-11l2-2 1-3z" class="W"></path><path d="M307 148l-3-3c-1-2-1-8 1-11 0 1 1 1 1 1h1c1 2 2 4 2 5 0 3 0 4 2 6v1 2c-1 1-2 1-3 0l-1-1z" class="C"></path><defs><linearGradient id="AW" x1="298.551" y1="150.615" x2="282.079" y2="170.48" xlink:href="#B"><stop offset="0" stop-color="#8a8787"></stop><stop offset="1" stop-color="#b6b3b3"></stop></linearGradient></defs><path fill="url(#AW)" d="M294 148h3c0 1 1 4 2 5v1l2 5c-1 2-2 5-3 7h-2c-2 2-3 3-5 4-1 0-2 1-3 1h-3-1c-2 0-2 0-4 1h0-1l3-3v-1c7-5 10-12 12-20z"></path><path d="M295 164l1 2c-2 2-3 3-5 4-1 0-2 1-3 1h-3c4-2 7-4 10-7z" class="a"></path><path d="M295 164c1-1 2-3 3-5s1-3 0-4l1-1 2 5c-1 2-2 5-3 7h-2l-1-2z" class="K"></path><defs><linearGradient id="AX" x1="310.749" y1="160.994" x2="315.308" y2="170.575" xlink:href="#B"><stop offset="0" stop-color="#afadac"></stop><stop offset="1" stop-color="#d0cfcf"></stop></linearGradient></defs><path fill="url(#AX)" d="M325 154l3-1c1-1 3-1 4-1v1c-1 1-3 2-5 2v1l1 1h0c1-1 2-1 3 0l-23 15c-2 0-4 1-6 3-4 3-7 8-11 10 1-2 3-5 5-7 3-5 6-10 11-13 5-5 12-8 18-11z"></path><defs><linearGradient id="AY" x1="253.918" y1="212.272" x2="278.311" y2="191.498" xlink:href="#B"><stop offset="0" stop-color="#b5b3b3"></stop><stop offset="1" stop-color="#dfdedd"></stop></linearGradient></defs><path fill="url(#AY)" d="M268 175l14-6-3 3h1 0c2-1 2-1 4-1h1 3 0c-1 1-2 1-3 2v5c1 1 2 1 3 1l-5 6h1l2-1v1c-2 3-3 5-4 8-1 1-2 2-2 4l1-1c0-2 2-2 3-4l-6 10c-1 2-3 4-4 6v1l-2 2c-1 2-2 4-3 7-1 0-2-1-3-1v2c0 1 2 2 1 4h-1 0v3l-2 1c-1 1-4 0-5-1-2-1-2-3-4-3-2-2-3-5-3-7-2-6-1-11 0-17 1-2 1-8 1-10s2-3 3-4 1-2 2-3h2 0l-1-3c2-1 5-1 7-2 1 0 2 0 2-1v-1z"></path><path d="M262 209l1-3c0-2 1-3 3-4l-2 5-2 2z" class="j"></path><path d="M266 202c1-3 2-5 3-7 1-4 2-8 4-11h1l-2 3v8c-2 3-3 5-3 8l-1 3v-1-3h-1v-1l-1 1z" class="F"></path><path d="M266 223c-2 1-2 2-4 2-4-2-5-5-6-9-3-6-2-13-2-20 0-3 0-6 1-8s7-5 9-6c1 0 2 0 2-1 3-1 6-1 9-3h1c-1 2-2 3-3 4v1l-1-1c-3 0-10 3-13 6 0 1 0 3-1 5s-2 5-3 7c-1 5 0 9 2 14 0 1 1 2 1 4 1 1 2 2 4 3h1 0v-1c-1-2 0-3 0-5-1-2-1-4-1-6l2-2 2-5 1-1v1h1v3 1l1-3 2 3c0 1 1 2 3 2h0v1l-2 2c-1 2-2 4-3 7-1 0-2-1-3-1v2c0 1 2 2 1 4h-1 0z" class="B"></path><path d="M266 202l1-1v1c-2 6-3 9-1 15h0v2c0 1 2 2 1 4h-1c0-1-1-1-1-2v-2c-2-5-1-7-1-12l2-5z" class="f"></path><path d="M267 202h1v3 1l1-3 2 3c0 1 1 2 3 2h0v1l-2 2c-1 2-2 4-3 7-1 0-2-1-3-1h0c-2-6-1-9 1-15z" class="D"></path><path d="M269 203l2 3c0 1 1 2 3 2h0v1l-2 2c-1 1-3 2-4 3-1-1-1-3-1-4 1-1 1-2 1-4l1-3z" class="P"></path><path d="M269 203l2 3c-1 2-2 3-4 4 1-1 1-2 1-4l1-3z" class="G"></path><path d="M268 175l14-6-3 3h1 0c2-1 2-1 4-1h1 3 0c-1 1-2 1-3 2l-5 5v2c-1 0-1 0-2-1-2 2-3 3-4 5h-1v-1-1c1-1 2-2 3-4h-1c-3 2-6 2-9 3 0 1-1 1-2 1-2 1-8 4-9 6s-1 5-1 8c0 7-1 14 2 20 1 4 2 7 6 9 2 0 2-1 4-2v3l-2 1c-1 1-4 0-5-1-2-1-2-3-4-3-2-2-3-5-3-7-2-6-1-11 0-17 1-2 1-8 1-10s2-3 3-4 1-2 2-3h2 0l-1-3c2-1 5-1 7-2 1 0 2 0 2-1v-1z" class="N"></path><path d="M268 175l14-6-3 3h1 0c2-1 2-1 4-1h1 3 0c-1 1-2 1-3 2l-5 5h-1l-2-2h-1c-7 1-14 5-20 9 1-1 1-2 2-3h2 0l-1-3c2-1 5-1 7-2 1 0 2 0 2-1v-1z" class="P"></path><path d="M284 171h1 3 0c-1 1-2 1-3 2l-5 5h-1l-2-2h-1l8-5z" class="V"></path><path d="M285 173v5c1 1 2 1 3 1l-5 6h1l2-1v1c-2 3-3 5-4 8-1 1-2 2-2 4l1-1c0-2 2-2 3-4l-6 10c-1 2-3 4-4 6h0c-2 0-3-1-3-2l-2-3c0-3 1-5 3-8v-8l2-3c1-2 2-3 4-5 1 1 1 1 2 1v-2l5-5z" class="p"></path><path d="M283 185h-1c1-3 1-6 3-7 1 1 2 1 3 1l-5 6z" class="J"></path><path d="M274 184c1-2 2-3 4-5 1 1 1 1 2 1-1 3-1 5-2 8h-1c-1 3-3 4-5 7v-8l2-3z" class="B"></path><defs><linearGradient id="AZ" x1="504.903" y1="128.316" x2="484.868" y2="188.762" xlink:href="#B"><stop offset="0" stop-color="#8d8c8c"></stop><stop offset="1" stop-color="#ccc9c9"></stop></linearGradient></defs><path fill="url(#AZ)" d="M504 135c5-2 7-6 9-11h0c0 1 1 4 1 5 1 8 1 16 0 24 0 1 1 6 0 7 0 1 0 3 1 4v1l1 3c-1 1-2 1-3 2l2 1c0 1-1 3-1 4h0v6c-1 0-3 0-4 1-2 2-3 1-5 2 1 1 1 2 2 4h-1l1 2v1l3 5 1 1 3 2h0v3l-3 2 1 1h-5v1 1h0l3 3c0 1-1 1-2 2l-1 2-4 2c0 1 0 2 1 2l-2 2v-1l-1 2-1-1h0c-1 3-2 9-4 11 0 1-1 1-2 1l-5-1c-2-1-4-1-5-2l-2-2h-1c-2-1-3-2-3-5-1-2-17-15-20-17 2 0 3 2 4 2l1-1c-2-1-4-3-6-5l1-2h0c1-1 1-2 2-3 1-2 2-3 3-4s2-2 3-2l-1-1v-1h0c2-2 3-3 4-5 6-5 14-7 20-10 3-1 4-4 6-6s4-4 6-7v-1c0-1 0 0 1-1v-1h-1l-1-1 1-1-1-2 1-2-3 2h0l1-2c1-2 2-3 2-5v-1c1-2 1-3 1-5l-2-2c0-1 0-1 1-2 0 1 0 1 1 1s2-1 2-2z"></path><path d="M507 145c1 2 0 4 0 6 0-1 1-1 2-2h1l-5 5h-2c0-1 1-1 2-2s1-1 1-2c0-2 1-4 1-5z" class="F"></path><path d="M504 167c-1 0 0 0-1-1l1-1v-1l-1 1-1-1 2-2h1c2 0 5-6 8-7h1v-2c0 1 1 6 0 7-3 1-6 5-10 7z" class="R"></path><path d="M514 160c0 1 0 3 1 4v1l1 3c-1 1-2 1-3 2s-3 2-5 3h-2l-1 1c-1-1-1-2-1-3-2 2-4 3-7 4h0-1-1 1l-1-1c1-1 8-6 9-7 4-2 7-6 10-7z" class="L"></path><path d="M504 171c3-1 8-6 11-7v1l1 3c-1 1-2 1-3 2s-3 2-5 3h-2l-1 1c-1-1-1-2-1-3z" class="f"></path><path d="M495 174l1 1h-1 1 1 0l-10 6c-9 5-17 9-24 16l-3 3h-1l-1-1h0c1-1 1-2 2-3 1-2 2-3 3-4s2-2 3-2c2-1 3-3 5-4 1-2 3-3 4-3 7-4 14-6 20-9z" class="k"></path><path d="M504 135c5-2 7-6 9-11h0c0 1 1 4 1 5l-3 4c-1 2-2 4-3 5 0 2 0 3-1 5v2c0 1-1 3-1 5 0 1 0 1-1 2s-2 1-2 2l-1 2h1l1 1-3 3v-1c0-1 0 0 1-1v-1h-1l-1-1 1-1-1-2 1-2-3 2h0l1-2c1-2 2-3 2-5v-1c1-2 1-3 1-5l-2-2c0-1 0-1 1-2 0 1 0 1 1 1s2-1 2-2z" class="j"></path><path d="M513 170l2 1c0 1-1 3-1 4h0v6c-1 0-3 0-4 1-2 2-3 1-5 2 1 1 1 2 2 4h-1-1v-2c-2-2-5-4-7-5h-4l-3 1c-2 0-3 0-4-1l10-6c3-1 5-2 7-4 0 1 0 2 1 3l1-1h2c2-1 4-2 5-3z" class="T"></path><path d="M504 184c0-1 0-1-1-2 1-3 3-4 5-5 0 1 0 2-1 3v1c-1 1-1 2-3 3z" class="S"></path><path d="M508 177h0c2-1 4-3 6-3v1 6c-1 0-3 0-4 1-2 2-3 1-5 2h-1c2-1 2-2 3-3v-1c1-1 1-2 1-3z" class="R"></path><defs><linearGradient id="Aa" x1="503.74" y1="174.748" x2="488.557" y2="179.62" xlink:href="#B"><stop offset="0" stop-color="#848182"></stop><stop offset="1" stop-color="#aaa7a6"></stop></linearGradient></defs><path fill="url(#Aa)" d="M504 171c0 1 0 2 1 3l1-1h2 0c-4 3-9 4-13 7 1 0 2 1 3 1h-4l-3 1c-2 0-3 0-4-1l10-6c3-1 5-2 7-4z"></path><path d="M498 181c2 1 5 3 7 5v2h1l1 2v1l3 5 1 1 3 2h0v3l-3 2 1 1h-5v1 1c-2-1-3-3-5-4l-1-1c-6-3-9-4-16-3-4 2-8 3-11 5l-8 6-4-3 1-1c-2-1-4-3-6-5l1-2 1 1h1l3-3c7-7 15-11 24-16 1 1 2 1 4 1l3-1h4z" class="k"></path><path d="M503 190h4v1l3 5 1 1 3 2h0v3l-3 2c-2-1-3-3-4-5l-3-6-1-3z" class="Q"></path><path d="M507 199l1-1c1 1 2 1 2 3l1 1h1c0-1 1-2 2-3v3l-3 2c-2-1-3-3-4-5z" class="I"></path><path d="M503 190h4v1l3 5-1 2c-2-3-2-4-5-5l-1-3z" class="f"></path><defs><linearGradient id="Ab" x1="498.083" y1="197.204" x2="476.133" y2="182.711" xlink:href="#B"><stop offset="0" stop-color="#797474"></stop><stop offset="1" stop-color="#b2b0af"></stop></linearGradient></defs><path fill="url(#Ab)" d="M498 181c2 1 5 3 7 5v2h1l1 2h-4c-3-3-6-5-10-5-3 0-4 0-7 1h0c-4 1-9 4-12 6-3 1-4 3-7 4-1 1-2 2-3 2l-1-1c7-7 15-11 24-16 1 1 2 1 4 1l3-1h4z"></path><path d="M498 181c2 1 5 3 7 5v2c-3-1-4-3-7-4-1-1-3-2-4-3h4z" class="F"></path><path d="M464 198l-3 4v1c2 0 4 1 5 0 6-2 10-7 16-8 3-1 4-2 8-2v1c1 0 2 1 3 2 2 1 4 2 5 4 1 1 1 0 2 1 1 0 1 0 1 1-6-3-9-4-16-3-4 2-8 3-11 5l-8 6-4-3 1-1c-2-1-4-3-6-5l1-2 1 1h1l3-3 1 1z" class="P"></path><path d="M463 206c2 0 3 0 4-1h1c3-1 4-3 7-4 3-2 5-4 8-5l2-1c2-1 3-1 5-1v3c-2 0-3 1-5 1v1c-4 2-8 3-11 5l-8 6-4-3 1-1h0z" class="D"></path><path d="M463 206c1 0 1 0 1 1 4 0 8-3 11-5h1c-1 1-2 1-2 2l-8 6-4-3 1-1h0z" class="R"></path><path d="M485 199c7-1 10 0 16 3l1 1c2 1 3 3 5 4h0l3 3c0 1-1 1-2 2l-1 2-4 2c0 1 0 2 1 2l-2 2v-1l-1 2-1-1h0c-1 3-2 9-4 11 0 1-1 1-2 1l-5-1c-2-1-4-1-5-2l-2-2h-1c-2-1-3-2-3-5-1-2-17-15-20-17 2 0 3 2 4 2l4 3 8-6c3-2 7-3 11-5z" class="s"></path><path d="M497 218l2-1v1h0c1 1 1 1 1 2-1 3-2 9-4 11 0 1-1 1-2 1l-5-1c-2-1-4-1-5-2l-2-2h-1c-2-1-3-2-3-5 1 2 4 3 6 4s6 1 9 0 3-5 4-8z" class="L"></path><path d="M502 203c2 1 3 3 5 4h0l3 3c0 1-1 1-2 2l-1 2-4 2c0 1 0 2 1 2l-2 2v-1l-1 2-1-1h0c0-1 0-1-1-2h0v-1l-2 1 2-9 3-6z" class="F"></path><path d="M499 209c2 3 2 7 2 10l-1 1h0c0-1 0-1-1-2h0v-1l-2 1 2-9z" class="D"></path><path d="M507 207l3 3c0 1-1 1-2 2l-1 2-4 2v-1c0-2 0-4 1-7h2l1-1zm-18 2c1 0 2 1 3 1 1 1 2 2 1 5 0 2-1 6-3 7-2 2-4 1-6 1l-3-2c-2-1-3-1-4-3 0 0 0-1 1-1 1-3 8-7 11-8z" class="f"></path><path d="M488 212c1 0 1 0 2 1s1 3 0 5c0 2-1 2-3 3l-1-1c0-2-2-3-4-4 3-1 4-2 6-4z" class="i"></path><path d="M513 124v-2l1-1 1 1c6 11 4 19 18 24 2 0 4-1 7-2h0l-3 3h2c-1 2-2 4-1 5 0 2 0 3 1 4 2 1 4 1 6 1 7 0 13-5 18-9 2-1 5-4 7-4 1 0 1 0 2 1v1c2 6-1 13 6 17l8 4-3 5c1 2 5 3 7 3 4 1 6 1 9-1 2-1 4-3 6-3 1 1 1 1 2 1 3-2 4-6 5-10v-2h1l1-1-1 7c2 0 4 1 6 1h-3c0 2 0 3-1 5l-2 2-3 6-1 1c0 3 0 4 2 7 2 1 3 3 5 3v1h0c-3-1-4-1-5-3-1-1-2-1-4-1l-1-1c-2 1-4 0-6 0h0c-3 0-5 1-8 2h0l-2-2h0c-3 0-4 0-5 2h-1c-2 1-4 1-5 2l-3 2c-2 0-4 0-6-2h-1l-1 1h-1v3h0c2 1 4 2 4 4h0c-1 2-2 3-4 3 0 1-1 1-2 1l1-1c0-1 0-1 1-3-3 0-4-2-6-4 0 1 0 2-1 3-6-5-14-11-22-12l-1-1c-2 0-5 0-8 1-4 1-6 6-8 9-1 3-4 8-7 10h-2l-1-1 3-2v-3h0l-3-2-1-1-3-5v-1l-1-2h1c-1-2-1-3-2-4 2-1 3 0 5-2 1-1 3-1 4-1v-6h0c0-1 1-3 1-4l-2-1c1-1 2-1 3-2l-1-3v-1c-1-1-1-3-1-4 1-1 0-6 0-7 1-8 1-16 0-24 0-1-1-4-1-5z" class="d"></path><path d="M515 165l4 2-1 1 1 1c2 1 4 3 5 5h0l-9-3-2-1c1-1 2-1 3-2l-1-3z" class="C"></path><path d="M556 185c-1-1-6-3-7-4v-1c0-1-2-2-2-2v-1l9 4c1 0 1-1 2-2v4c-1 0-2-1-3-1h1v3z" class="G"></path><path d="M519 167c5 4 12 7 17 11v1c-4 0-8-3-11-5h-1 0c-1-2-3-4-5-5l-1-1 1-1z" class="E"></path><path d="M562 172h1v1c-1 2-1 3-2 5v1c-1 2-1 3-1 5l1 2c1 2 1 3 1 4l-6-5v-3h-1c1 0 2 1 3 1v-4c1-2 2-5 4-7z" class="B"></path><path d="M525 184c0 2-4 3-4 5-1 1 0 5 0 6-1 3-4 8-7 10h-2l-1-1 3-2v-3h0l1-4s0 1 1 1l3-5h0c2-3 4-5 6-7z" class="E"></path><path d="M519 191c0 3-2 7-3 10-1 1-1 2-2 4h-2l-1-1 3-2v-3h0l1-4s0 1 1 1l3-5z" class="B"></path><path d="M561 179c1 4 2 6 4 9 1 1 3 2 4 3l-1 1h-1v3h0c2 1 4 2 4 4h0c-1 2-2 3-4 3 0 1-1 1-2 1l1-1c0-1 0-1 1-3 0-3-3-7-5-9 0-1 0-2-1-4l-1-2c0-2 0-3 1-5z" class="M"></path><path d="M567 195c2 1 4 2 4 4h0c-1 2-2 3-4 3 0-2 1-4 0-7z" class="n"></path><path d="M539 147c-1 2-2 4-1 5 0 2 0 3 1 4 2 1 4 1 6 1 7 0 13-5 18-9 2-1 5-4 7-4 1 0 1 0 2 1h-1c-1 0-1 0-2 1h0c-8 5-15 16-26 13-2 0-5-2-6-4s-1-4-1-6c1-1 1-2 1-2h2z" class="o"></path><defs><linearGradient id="Ac" x1="516.515" y1="194.649" x2="507.375" y2="182.928" xlink:href="#B"><stop offset="0" stop-color="#8a8483"></stop><stop offset="1" stop-color="#a3a0a0"></stop></linearGradient></defs><path fill="url(#Ac)" d="M505 184c2-1 3 0 5-2 1-1 3-1 4-1l1 14-1 4-3-2-1-1-3-5v-1l-1-2h1c-1-2-1-3-2-4z"></path><path d="M507 191h2c1 1 3 3 3 5l-1 1-1-1-3-5z" class="F"></path><path d="M525 184c2-1 3-2 5-3 3-1 11 1 14 3 6 2 12 6 17 11 0 1 0 2-1 3-6-5-14-11-22-12l-1-1c-2 0-5 0-8 1-4 1-6 6-8 9 0-1-1-5 0-6 0-2 4-3 4-5z" class="L"></path><path d="M569 146c1-1 1-1 2-1 0 6-1 13 4 18 2 2 6 3 9 4l-4 4h-1 0 0-2v-1h-4c-3 0-6 0-9 2l-1 1v-1h-1 0c1-2 3-3 5-3 1 0 2 0 3-1-1-2-3-3-3-5-1-3-2-8-1-10 1-3 3-4 4-6l-1-1z" class="P"></path><path d="M573 170v-1c2-1 6-2 8-1h0c-1 1-2 2-4 2h-4z" class="C"></path><path d="M573 170h4v1c1 3 1 4 3 6 1 1 3 2 3 4v1h0c-3 1-4 2-7 3l-1-1h-1-3c-1-1-1-1-2-1l-1 1c1 2 2 3 3 5h-1c-1-1-3-1-5-1-2-3-3-5-4-9v-1c1-2 1-3 2-5l1-1c3-2 6-2 9-2z" class="k"></path><path d="M567 181l1-2c2 2 4 4 7 5h-1-3c-1-1-1-1-2-1l-1 1-1-3z" class="E"></path><path d="M561 179v-1c2 3 3 6 5 8h1v-5l1 3c1 2 2 3 3 5h-1c-1-1-3-1-5-1-2-3-3-5-4-9z" class="d"></path><path d="M571 145h1v1c2 6-1 13 6 17l8 4-3 5c1 2 5 3 7 3 4 1 6 1 9-1 2-1 4-3 6-3 1 1 1 1 2 1 3-2 4-6 5-10v-2h1l1-1-1 7c2 0 4 1 6 1h-3c0 2 0 3-1 5l-2 2-3 6-1 1c0 3 0 4 2 7 2 1 3 3 5 3v1h0c-3-1-4-1-5-3-1-1-2-1-4-1l-1-1c-2 1-4 0-6 0h0c-3 0-5 1-8 2h0l-2-2h0c-3 0-4 0-5 2h-1c-2 1-4 1-5 2l-3 2c-2 0-4 0-6-2h-1c-1-1-3-2-4-3 2 0 4 0 5 1h1c-1-2-2-3-3-5l1-1c1 0 1 0 2 1h3 1l1 1c3-1 4-2 7-3h0v-1c0-2-2-3-3-4-2-2-2-3-3-6h2 0 0 1l4-4c-3-1-7-2-9-4-5-5-4-12-4-18z" class="K"></path><path d="M613 166c2 0 4 1 6 1h-3c0 2 0 3-1 5l-2 2-3 6-1 1h-1v2c-1-1-1-2-1-3-1-2 0-3 1-4 2-4 4-7 5-10z" class="M"></path><path d="M613 166c2 0 4 1 6 1h-3c-1 0-2 1-3 1-1 2-3 5-4 7v1h-1c2-4 4-7 5-10z" class="D"></path><path d="M577 171h2 0 0 1c2 4 9 6 13 7 2 0 4 0 5-1h1l-2 2c-3 1-6 1-8 1h-3-1l-2 2v-1c0-2-2-3-3-4-2-2-2-3-3-6z" class="c"></path><path d="M599 177c1-2 4-4 6-4l1 1h-1c-2 3-2 4-1 7l2 6c-2 1-4 0-6 0h0c-3 0-5 1-8 2h0l-2-2h0c-3 0-4 0-5 2h-1c-2 1-4 1-5 2l-3 2c-2 0-4 0-6-2h-1c-1-1-3-2-4-3 2 0 4 0 5 1h1c-1-2-2-3-3-5l1-1c1 0 1 0 2 1h3 1l1 1c3-1 4-2 7-3h0l2-2h1 3c2 0 5 0 8-1l2-2z" class="E"></path><path d="M579 191l-1-1c0-1 1-1 1-1l1-1c2 0 2 0 4 1-2 1-4 1-5 2z" class="G"></path><path d="M565 188c2 0 4 0 5 1h1 2c1 1 2 0 3 0v1c-1 0-2 1-3 1h-3-1c-1-1-3-2-4-3z" class="L"></path><path d="M598 180c2 0 4 0 6 1l2 6c-2 1-4 0-6 0h0l1-1c-2-1-3-2-5-3-1 0-2-1-3-1l1-1h4v-1z" class="S"></path><path d="M599 177c1-2 4-4 6-4l1 1h-1c-2 3-2 4-1 7-2-1-4-1-6-1h-12v1c-1 1-2 1-3 1h0l2-2h1 3c2 0 5 0 8-1l2-2z" class="L"></path><path d="M568 184l1-1c1 0 1 0 2 1h3 1l1 1c3 1 8 0 11-1 2-1 5-1 8-1v1c1 0 2 1 3 1l-1 1h-2c-1-1-3-1-4 0-3-1-4-1-6 0h-2c-1 0-2 0-2 1h-2c-1 0-2 1-3 1v1c-1 0-2 1-3 0h-2c-1-2-2-3-3-5z" class="D"></path><path d="M229 403c11-4 23-6 35-6l4 1c3 0 6 0 9 1h0 4c-1 2-4 5-6 7-2 0-5 1-7 2h-1l-4 1-7 4-20 16-13 14-12 14c-1 2-8 12-9 13-3 1-4 4-6 5-1 0-1 1-2 1 1-1 1-1 1-3h0l3-6 6-11c-1 0-1-1-2-2-2 0-3 0-4 1h-1c-2-2-2-1-3-1-1-1-3-2-4-3l-5-2h-2-1-4c-2 0-5 0-8 1h1l2 1-1 3h3l2 1v3h1v6h0l-1 2 2 1c2 0 4-1 6-1v1l-2 3h1c-2 3-6 1-9 2v1h-3c-1 1-1 1-2 1h-3l-1-1c-2-1-4-3-5-6-4-5-6-11-6-18 1-4 2-9 5-11 1 0 1-2 2-2l7-6c8-4 17-7 26-9v-1c3-1 6-1 8-2 4-2 7-4 10-6s7-4 10-6l6-3z" class="k"></path><path d="M251 409c3-1 7-2 10-3 0 1-1 1-2 2h0c-1 1-3 2-4 3l-1-1c-1 0-1-1-3-1z" class="B"></path><path d="M195 420c3-1 6-1 8-2 4-2 7-4 10-6l1 2c2 1 4-1 7 0l-26 7v-1z" class="K"></path><path d="M187 429c2-2 7-3 10-3 3-2 6-3 10-4v2c-2 0-2 0-3 1h-1c-1 0-1 0-1 1-2 0-5 2-7 2l-3 1c-2 0-3 0-5 1v-1z" class="U"></path><path d="M166 473c-2-1-4-3-5-6-4-5-6-11-6-18 1-4 2-9 5-11-3 8-4 16-1 23 2 6 6 10 11 12h2c-1 1-1 1-2 1h-3l-1-1z" class="K"></path><path d="M222 418l19-5c-1 2-1 2-3 3-1 1-2 1-4 1h0c-3 2-6 3-9 5h-1l-2 1h-2v-1h-3c1-1 3-2 4-2l1-1v-1z" class="h"></path><path d="M207 422c5 0 10-3 15-4v1l-1 1c-1 0-3 1-4 2h3v1h2l-2 2c-2 1-4 2-5 3h-1l1-1c-2 0-6 4-8 5v-2l2-1c2-2 5-4 7-6h-1c-2-1-6 1-8 1v-2z" class="e"></path><path d="M277 399h4c-1 2-4 5-6 7-2 0-5 1-7 2h-1l-4 1-2-1h-2 0c1-1 2-1 2-2 4-2 9-3 13-4-3-1-6-1-9-2 2 0 4 0 6-1h5 1z" class="C"></path><path d="M261 408c1-1 2-1 3-2h2l1 2-4 1-2-1z" class="S"></path><path d="M187 430c2-1 3-1 5-1l-4 2c-7 3-16 11-19 18l1 1h1l2 1-1 3h3l2 1v3h1v6h0l-1 2 2 1c2 0 4-1 6-1v1l-2 3h1c-2 3-6 1-9 2-3-2-6-5-7-10-3-7-3-12 0-19 4-8 11-12 19-14v1z" class="m"></path><path d="M179 467c2 0 4-1 6-1v1l-2 3h-1c-2 1-6-1-8-2l1-1h4z" class="D"></path><path d="M167 454h1c1 4 2 9 4 11l1 1v-2h1l3 2 2 1h-4l-1 1 1 1h-1c-5-4-6-9-7-15z" class="R"></path><path d="M187 430c2-1 3-1 5-1l-4 2c-7 3-16 11-19 18l1 1h1-2c-1 1-1 2-1 4h0-1c-1-2 0-5 1-7 4-9 11-14 19-17z" class="N"></path><path d="M168 454h0c0-2 0-3 1-4h2l2 1-1 3h3l2 1v3h1v6h0l-1 2-3-2h-1v2l-1-1c-2-2-3-7-4-11z" class="B"></path><path d="M168 454h0c0-2 0-3 1-4h2l2 1-1 3-2-1c-1 1-1 3 0 4 0 3 2 6 2 8-2-2-3-7-4-11z" class="h"></path><path d="M175 454l2 1v3h1v6h0l-1 2-3-2c2-2 1-7 1-10z" class="N"></path><path d="M229 403c11-4 23-6 35-6l4 1c3 0 6 0 9 1h0-1-5c-2 1-4 1-6 1l-3 2-20 5-21 7h0c-3-1-5 1-7 0l-1-2c3-2 7-4 10-6l6-3z" class="F"></path><path d="M223 406c5 1 7-1 11-2h1c2-1 3-1 4-1l-25 11-1-2c3-2 7-4 10-6z" class="q"></path><path d="M242 407c-5-1-9 4-13 2h1v-1l7-2c3-1 7-2 10-3 5-1 10-2 15-1l-20 5z" class="C"></path><path d="M229 403c11-4 23-6 35-6l4 1c-7 1-13 1-20 3-2 0-7 2-9 2-1 0-2 0-4 1h-1c-4 1-6 3-11 2l6-3z" class="b"></path><path d="M207 424c2 0 6-2 8-1h1c-2 2-5 4-7 6l-2 1v2c2-1 6-5 8-5l-1 1h1l-4 3-3 3c-2 0-4 2-5 4l-2 2c-2 3-5 6-5 10 0 1 0 2 1 4 1 0 2-1 3-1l2 1c-2 0-3 0-4 1h-1c-2-2-2-1-3-1-1-1-3-2-4-3l-5-2h-2-1-4c-2 0-5 0-8 1l-1-1c3-7 12-15 19-18l4-2 3-1c2 0 5-2 7-2 0-1 0-1 1-1h1c1-1 1-1 3-1z" class="o"></path><path d="M192 429l3-1c-2 2-3 4-5 5-3 1-6 5-8 6 1-4 4-5 6-8l4-2z" class="Y"></path><path d="M202 426v1l-3 3c-2 1-4 2-6 2 0 1-1 1-2 2l-1-1c2-1 3-3 5-5 2 0 5-2 7-2z" class="r"></path><path d="M197 432v1l2 1c-1 1-2 2-3 2-4 1-8 5-10 8-1 1-1 3-1 5h-2l-1-1v-1c0-2 2-3 2-4 4-5 8-8 13-11z" class="I"></path><path d="M170 450l-1-1c3-7 12-15 19-18-2 3-5 4-6 8-3 3-6 7-10 9h0 6v1c-2 0-5 0-8 1z" class="V"></path><path d="M215 423h1c-2 2-5 4-7 6l-2 1v2c2-1 6-5 8-5l-1 1h1l-4 3-3 3c-2 0-4 2-5 4l-2 2c-2 3-5 6-5 10 0 1 0 2 1 4 1 0 2-1 3-1l2 1c-2 0-3 0-4 1h-1c-2-2-2-1-3-1-1-1-3-2-4-3l-5-2c0-2 0-4 1-5 2-3 6-7 10-8 1 0 2-1 3-2l-2-1v-1c5-5 12-6 18-9z" class="S"></path><path d="M196 442c-2 2-2 3-4 5h0c0-2 0-3 1-4v-1l2-2c1-1 3-4 5-4-1 1-2 5-4 6z" class="P"></path><path d="M185 449c0-2 0-4 1-5 2-3 6-7 10-8-2 2-4 4-6 7h-1c-1 1-1 2-2 4l1 1c1 0 1 0 2-1h1v1 2l1 1h-2l-5-2z" class="R"></path><path d="M200 436h1 1 1l-1 1c-1 1-1 2-1 3-2 3-5 6-5 10 0 1 0 2 1 4 1 0 2-1 3-1l2 1c-2 0-3 0-4 1h-1c-2-2-2-1-3-1-1-1-3-2-4-3h2 1c1-1 2-2 2-3s1-3 1-4h1l-1-2c2-1 3-5 4-6z" class="C"></path><path d="M215 423h1c-2 2-5 4-7 6l-2 1v2c2-1 6-5 8-5l-1 1h1l-4 3-3 3c-2 0-4 2-5 4l-2 2c0-1 0-2 1-3l1-1h-1-1l7-7-6 3c-1 0-2 1-3 2l-2-1v-1c5-5 12-6 18-9z" class="J"></path><path d="M261 408l2 1-7 4-20 16-13 14-12 14c-1 2-8 12-9 13-3 1-4 4-6 5-1 0-1 1-2 1 1-1 1-1 1-3h0l3-6 6-11c-1 0-1-1-2-2l-2-1c-1 0-2 1-3 1-1-2-1-3-1-4 0-4 3-7 5-10l2-2c1-2 3-4 5-4l3-3 4-3c1-1 3-2 5-3l2-2 2-1h1c3-2 6-3 9-5h0c2 0 3 0 4-1 2-1 2-1 3-3h0l3-1 2-1 5-2c2 0 2 1 3 1l1 1c1-1 3-2 4-3h2z" class="c"></path><path d="M244 412l2-1 2 2-4 2v-3z" class="l"></path><path d="M212 452v1c-1 1-2 2-3 4-1 1-2 2-2 3l-1-1v-1c1-1 1-2 2-3h0v2c0-1 1-2 2-2 0-2 1-2 2-3z" class="k"></path><path d="M261 408l2 1-7 4c1-1 2-2 3-2h1l-1-1c-1 0-2 1-3 1h-1c1-1 3-2 4-3h2z" class="E"></path><path d="M251 409c2 0 2 1 3 1-1 1-4 3-6 3l-2-2 5-2z" class="Q"></path><path d="M241 413h0l3-1v3l-4 3-23 24c0-1 2-2 3-4 3-3 6-7 9-10l-1-1c1-1 2-3 3-4 0-3 3-3 3-6h0c2 0 3 0 4-1 2-1 2-1 3-3z" class="Z"></path><path d="M241 413h0l3-1v3l-4 3c-3 0-4 1-6 3l-3 2c0-3 3-3 3-6h0c2 0 3 0 4-1 2-1 2-1 3-3z" class="e"></path><path d="M234 417c0 3-3 3-3 6-1 1-2 3-3 4l1 1c-3 3-6 7-9 10-1 2-3 3-3 4-4 5-9 10-13 14-1 0-1-1-2-2l-2-1c-1 0-2 1-3 1-1-2-1-3-1-4 0-4 3-7 5-10l2-2c1-2 3-4 5-4l3-3 4-3c1-1 3-2 5-3l2-2 2-1h1c3-2 6-3 9-5z" class="D"></path><path d="M234 417c0 3-3 3-3 6-1 1-2 3-3 4l-13 14c2-2 3-4 4-6 3-3 8-7 9-11-1 0-1 1-2 1h-1l1-2-2-1h1c3-2 6-3 9-5z" class="U"></path><path d="M220 425c1 1 1 1 1 2v1c0 2-2 3-3 4-6 5-14 11-17 18l-1 1c-1 1-1 1-2 0-1 0-1-1-2-1h0c0-4 3-7 5-10l2-2c1-2 3-4 5-4l3-3 4-3c1-1 3-2 5-3z" class="d"></path><path d="M220 425c1 1 1 1 1 2v1c0 2-2 3-3 4v-4c-2 1-3 3-7 3l4-3c1-1 3-2 5-3zm-19 15l2-2c1-2 3-4 5-4h0c-3 5-7 9-10 14v2c1 1 2 0 3 0l-1 1c-1 1-1 1-2 0-1 0-1-1-2-1h0c0-4 3-7 5-10z" class="E"></path><defs><linearGradient id="Ad" x1="878.346" y1="349.561" x2="836.221" y2="315.699" xlink:href="#B"><stop offset="0" stop-color="#c6c3c2"></stop><stop offset="1" stop-color="#e8e7e7"></stop></linearGradient></defs><path fill="url(#Ad)" d="M866 290c3 0 6 1 9 1 1 0 1 0 2 1l1-1c1 1 3 2 4 3 4 1 7 2 10 5 3 2 7 5 8 9v1c0 2 0 2 1 3v2h0v1c1 2 0 4 0 6v1c-1-1-1-3-1-5v-1c0-1 0-1-1-2h-1c-2-3-4-5-6-7v2c2 2 5 4 6 8v1l-2-2h-1c-7-5-13-5-21-5-1-1-3-1-4-1l-4 1c-1 1-2 1-3 1l-10 5v-1c-6 4-12 8-17 13l-2-1-8 7c0 2 0 3-1 4 0 1-1 2-1 2l1 1h0v3c-1 2-2 4-2 7v2l-2-1v1 1h-1l-1 1c1 1 1 3 1 4l2 2c-1 0-2 0-3 1 1 1 3 3 4 5 2 2 4 3 5 6-1-1-3-1-4-2l-9-7-2 1c-2-1-3-2-5-3h-1c-4-1-8-6-10-9h0l-3-6c-2-3-5-11-8-13l-1-1v-1-1h0c-1-2-1-2-2-3h-1l-1-1v-4l-3-2-5-3h0l-2-1 8-12 2 1-1 1 2-1c1 0 2 0 4-1l1-1v-1c2 0 2 0 3-1l5 1 16 2c4 0 9-2 13-4 1 0 3-1 5-2h3v1h1v2l3-1-1 2-2 1v1h1l2 1 7-3-1 1v2l1 1 1-1c1-1 3-4 5-4v-1c1-1 1-1 1-2h0v-4l9-2 2-1h0l2-1-2-1 5-1z"></path><path d="M825 327c1-1 9-4 10-4l-10 8-5 1v-1l5-3v-1z" class="I"></path><path d="M884 298c3 0 5 0 7 1h1c3 2 7 5 8 9v1l-1-1c-1-1-3-2-4-3-2-1-3-1-4-2-2-2-4-4-7-5z" class="D"></path><path d="M843 315c-1 1-1 2-3 3l1 1c-1 1-2 1-3 2l-1 1c-1 0-1 1-2 1s-9 3-10 4l-3 1v-1c2-1 3-2 4-4 6-1 12-5 17-8z" class="H"></path><path d="M826 335c0 2 0 3-1 4 0 1-1 2-1 2l1 1h0v3c-1 2-2 4-2 7v2l-2-1v1 1h-1l-1 1c1 1 1 3 1 4 0-1-1-2-1-3l-1-2c-1-4-1-8 2-12 1-3 3-6 6-8z" class="F"></path><path d="M819 356c0-3-1-6 0-8 1-1 1-3 2-3v1 2c-1 1-1 3 1 4h1v2l-2-1v1 1h-1l-1 1z" class="D"></path><path d="M826 335c0 2 0 3-1 4 0 1-1 2-1 2l1 1h0l-2 3-1-1h0c1-1 1-2 1-2l-1-1c-1 1-1 2-2 2 1-3 3-6 6-8z" class="I"></path><path d="M820 332l5-1c-5 4-10 9-11 16 0 4 1 8 3 12-3-2-5-4-6-7v-1c-1-1-2-2-1-4h0v-3c0-1 1-1 1-2v-1h-2v-3c4 0 4-4 8-5 1 0 2-1 3-2v1z" class="D"></path><path d="M809 338c4 0 4-4 8-5 1 0 2-1 3-2v1c-2 3-8 6-9 9h-2v-3z" class="J"></path><defs><linearGradient id="Ae" x1="800.465" y1="331.974" x2="804.012" y2="341.855" xlink:href="#B"><stop offset="0" stop-color="#504a4b"></stop><stop offset="1" stop-color="#696363"></stop></linearGradient></defs><path fill="url(#Ae)" d="M822 328l3-1v1l-5 3c-1 1-2 2-3 2-4 1-4 5-8 5v3c-1 0-2 1-2 2 0 2 1 3 0 5l-2-3v-1h-1c-2-5-5-10-8-13 1-1 0-1 1-1h1c0 1 1 1 1 2h1c1 0 1 1 2 0 1 0 1 0 2 1 1 0 1 0 2-1h6l1-1v-1-1c2 1 4 0 6 0l3-1z"></path><path d="M805 337h1l1 1h2v3c-1 0-2 1-2 2 0 2 1 3 0 5l-2-3v-1l1-1c0-2 0-4-1-6z" class="M"></path><path d="M813 329c2 1 4 0 6 0 0 0 0 1-1 1-2 2-5 2-7 4h-1c-1 1-1 2-1 4h-2l-1-1h-1 0c-1-1-2-2-2-3h1c2 0 6 0 8-2l1-1v-1-1z" class="l"></path><path d="M822 328l3-1v1l-5 3c-1 1-2 2-3 2-4 1-4 5-8 5 0-2 0-3 1-4h1c2-2 5-2 7-4 1 0 1-1 1-1l3-1z" class="M"></path><path d="M834 328l11-9c9-7 19-15 30-16 6-1 12 0 17 4v2c-6-4-12-5-19-3-2 1-5 1-8 3l-6 3c-2 2-4 3-6 4-6 4-12 8-17 13l-2-1z" class="K"></path><path d="M809 341h2v1c0 1-1 1-1 2v3h0c-1 2 0 3 1 4v1c1 3 3 5 6 7 0 1 1 3 2 4s3 3 4 5c2 2 4 3 5 6-1-1-3-1-4-2l-9-7c-4-3-7-6-9-10v-3l1-1v-1-2c1-2 0-3 0-5 0-1 1-2 2-2z" class="F"></path><path d="M807 350c2 2 3 5 4 7s2 3 3 5 5 4 7 6c1 1 3 2 3 4l-9-7c-4-3-7-6-9-10v-3l1-1v-1z" class="P"></path><defs><linearGradient id="Af" x1="856.038" y1="298.364" x2="858.755" y2="305.902" xlink:href="#B"><stop offset="0" stop-color="#3b2828"></stop><stop offset="1" stop-color="#504645"></stop></linearGradient></defs><path fill="url(#Af)" d="M869 298h8c-8 2-16 4-23 8-2 2-5 4-7 6-1 0-2 2-3 2h-3-1c0-1 1-3 1-4v-1l2-1h0l1-1c1-1 3-4 5-4v-1c1-1 1-1 1-2h0l19-2z"></path><path d="M841 309l2-1v1c1 1 2 1 3 1l-3 3c-1 0-2 0-2 1h-1c0-1 1-3 1-4v-1z" class="O"></path><path d="M849 303h1c3-1 3-2 6-1-4 2-8 6-12 7v-2c1-1 3-4 5-4z" class="s"></path><path d="M853 316c2-1 4-2 6-4l6-3c3-2 6-2 8-3 7-2 13-1 19 3 2 2 5 4 6 8v1l-2-2h-1c-7-5-13-5-21-5-1-1-3-1-4-1l-4 1c-1 1-2 1-3 1l-10 5v-1z" class="Z"></path><defs><linearGradient id="Ag" x1="887.509" y1="310.064" x2="872.501" y2="309.916" xlink:href="#B"><stop offset="0" stop-color="#2b2a2a"></stop><stop offset="1" stop-color="#4f4849"></stop></linearGradient></defs><path fill="url(#Ag)" d="M866 311c1 0 2-1 3-1l1-1c3-1 6-1 8 0 3 0 6 0 8 1 2 0 5 2 7 4 1 0 2 1 3 2h-1c-7-5-13-5-21-5-1-1-3-1-4-1l-4 1z"></path><path d="M866 290c3 0 6 1 9 1 1 0 1 0 2 1l1-1c1 1 3 2 4 3 4 1 7 2 10 5h-1c-2-1-4-1-7-1-2-1-4-2-6-2l2 1c-3 0-8 0-11 1l-19 2v-4l9-2 2-1h0l2-1-2-1 5-1z" class="R"></path><path d="M866 290c3 0 6 1 9 1 1 0 1 0 2 1l1-1c1 1 3 2 4 3-6-2-12-3-19-2l-2-1 5-1z" class="n"></path><path d="M859 294c6 0 13 0 18 1l1 1h0l2 1c-3 0-8 0-11 1l-19 2v-4l9-2z" class="d"></path><defs><linearGradient id="Ah" x1="798.242" y1="341.391" x2="792.082" y2="345.733" xlink:href="#B"><stop offset="0" stop-color="#261412"></stop><stop offset="1" stop-color="#574546"></stop></linearGradient></defs><path fill="url(#Ah)" d="M781 324l2 1 6 4c2-1 2-1 4 0l3 2c3 3 6 8 8 13h1v1l2 3v2 1l-1 1v3c2 4 5 7 9 10l-2 1c-2-1-3-2-5-3h-1c-4-1-8-6-10-9h0l-3-6c-2-3-5-11-8-13l-1-1v-1-1h0c-1-2-1-2-2-3h-1l-1-1v-4z"></path><path d="M802 346c1-1 2-1 3-1l2 3v2 1l-1 1v3l-4-9z" class="G"></path><path d="M797 354v-1c1 0 3-1 3-1 2 2 3 4 4 6s3 3 4 5h-1c-4-1-8-6-10-9z" class="Y"></path><defs><linearGradient id="Ai" x1="797.204" y1="328.981" x2="795.275" y2="342.162" xlink:href="#B"><stop offset="0" stop-color="#79787a"></stop><stop offset="1" stop-color="#a8a4a2"></stop></linearGradient></defs><path fill="url(#Ai)" d="M789 329c2-1 2-1 4 0l3 2c3 3 6 8 8 13h1v1c-1 0-2 0-3 1l-1-2c-4-6-7-11-12-15z"></path><path d="M801 344h3 1v1c-1 0-2 0-3 1l-1-2z" class="C"></path><defs><linearGradient id="Aj" x1="787.639" y1="322.305" x2="815.202" y2="303.68" xlink:href="#B"><stop offset="0" stop-color="#2e1818"></stop><stop offset="1" stop-color="#584241"></stop></linearGradient></defs><path fill="url(#Aj)" d="M829 300h3v1h1v2l3-1-1 2-2 1v1h1l2 1 7-3-1 1v2l1 1h0l-2 1v1c0 1-1 3-1 4h1 3l-1 1c-5 3-11 7-17 8-1 2-2 3-4 4v1l-3 1c-2 0-4 1-6 0v1 1l-1 1h-6c-1 1-1 1-2 1-1-1-1-1-2-1-1 1-1 0-2 0h-1c0-1-1-1-1-2h-1c-1 0 0 0-1 1l-3-2c-2-1-2-1-4 0l-6-4-2-1-3-2-5-3h0l-2-1 8-12 2 1-1 1 2-1c1 0 2 0 4-1l1-1v-1c2 0 2 0 3-1l5 1 16 2c4 0 9-2 13-4 1 0 3-1 5-2z"></path><path d="M819 311l5-2v1 1h3l-4 2h-2c-1 1-3 1-5 0l-5 1c-2 0-8 1-10-1h4l-3-1c2-2 7-2 11-2 2 0 4 0 6 1z" class="h"></path><path d="M824 311h3l-4 2h-2c-1 1-3 1-5 0h1 0c3 0 5-1 7-2z" class="O"></path><path d="M813 310c2 0 4 0 6 1l-14 2-3-1c2-2 7-2 11-2z" class="F"></path><path d="M827 311l4-1-1 1c-2 2-5 4-8 5-2 1-5 1-6 4l1 1c0 1-1 1-1 2v2c-2 0-5 1-8 0h1c1-2 1-4 1-6 1-1 3-2 5-3l3-1c2 0 3 0 5-2l4-2z" class="e"></path><path d="M815 316l3-1c-1 2-2 3-4 4-1 1-1 5-2 6h0v-1c0-4 0-6 3-8z" class="Y"></path><path d="M809 325c1-2 1-4 1-6 1-1 3-2 5-3-3 2-3 4-3 8v1h-3z" class="o"></path><defs><linearGradient id="Ak" x1="781.977" y1="305.289" x2="807.884" y2="313.169" xlink:href="#B"><stop offset="0" stop-color="#7d7a7a"></stop><stop offset="1" stop-color="#9d9d9d"></stop></linearGradient></defs><path fill="url(#Ak)" d="M786 306l22 3c2 1 3 1 5 1-4 0-9 0-11 2-7 1-15-1-22-2l-1-1 1-1 2-1c1 0 2 0 4-1z"></path><defs><linearGradient id="Al" x1="779.72" y1="322.964" x2="807.711" y2="318.134" xlink:href="#B"><stop offset="0" stop-color="#898988"></stop><stop offset="1" stop-color="#acaaab"></stop></linearGradient></defs><path fill="url(#Al)" d="M777 313c1-1 1-2 2-1 7 1 13 6 19 9 1 1 4 2 5 3 2 0 3 1 5 1 3 1 6 0 8 0 3 0 7-1 10-2-1 2-2 3-4 4v1l-3 1c-2 0-4 1-6 0v1c-3 0-5 0-8-1-4-1-27-13-28-16z"></path><path d="M816 325c3 0 7-1 10-2-1 2-2 3-4 4v1l-3 1c-2 0-4 1-6 0h-2l1-1h-3c-1-1-2-2-3-2-2 0-2-1-3-2h0c2 0 3 1 5 1 3 1 6 0 8 0z" class="E"></path><path d="M777 313c1 3 24 15 28 16 3 1 5 1 8 1v1l-1 1h-6c-1 1-1 1-2 1-1-1-1-1-2-1-1 1-1 0-2 0h-1c0-1-1-1-1-2h-1c-1 0 0 0-1 1l-3-2c-2-1-2-1-4 0l-6-4-2-1-3-2-5-3h0c1-2 1-3 3-5 0 0 0-1 1-1z" class="Y"></path><path d="M783 325h5c2 1 4 2 5 4-2-1-2-1-4 0l-6-4z" class="M"></path><path d="M773 319c4-1 8 1 11 3 1 1 3 2 4 3h-5l-2-1-3-2-5-3z" class="e"></path><path d="M843 304l-1 1v2l1 1h0l-2 1v1c0 1-1 3-1 4h1 3l-1 1c-5 3-11 7-17 8-3 1-7 2-10 2v-2c0-1 1-1 1-2l-1-1c1-3 4-3 6-4 3-1 6-3 8-5l1-1 5-3 7-3z" class="Q"></path><defs><linearGradient id="Am" x1="835.808" y1="306.255" x2="832.692" y2="316.745" xlink:href="#B"><stop offset="0" stop-color="#878384"></stop><stop offset="1" stop-color="#9d9d9e"></stop></linearGradient></defs><path fill="url(#Am)" d="M841 310c-1 1-2 1-3 2-4 4-10 5-15 6l7-4c4-2 6-5 10-7l1 1v1 1z"></path><defs><linearGradient id="An" x1="807.479" y1="299.754" x2="820.021" y2="310.246" xlink:href="#B"><stop offset="0" stop-color="#6f6b6e"></stop><stop offset="1" stop-color="#908d8c"></stop></linearGradient></defs><path fill="url(#An)" d="M829 300h3v1h1v2l3-1-1 2-2 1v1h1l2 1-5 3-4 1h-3v-1-1l-5 2c-2-1-4-1-6-1s-3 0-5-1l-22-3 1-1v-1c2 0 2 0 3-1l5 1 16 2c4 0 9-2 13-4 1 0 3-1 5-2z"></path><path d="M790 303l5 1v1h-1c-2 1-1 0-2 1-2 0-3 0-5-1v-1c2 0 2 0 3-1z" class="M"></path><path d="M795 304l16 2c-6 2-13 1-19 0 1-1 0 0 2-1h1v-1z" class="J"></path><path d="M832 301h1v2l3-1-1 2-2 1v1h1l2 1-5 3-4 1h-3v-1-1l-5 2c-2-1-4-1-6-1s-3 0-5-1c6-1 10-2 16-4 3-1 5-3 8-4z" class="C"></path><path d="M832 301h1v2c-3 1-5 2-9 3v-1c3-1 5-3 8-4z" class="L"></path><path d="M824 309l9-4v1h1l2 1-5 3-4 1h-3v-1-1z" class="s"></path><path d="M659 727c5-1 9-1 14-1 2 0 5 0 7 1l9 1h3l7 1c3 0 6 0 8 1l12 1c3 0 7 0 10-1 1 1 1 1 3 1h4 1l6-5c-2 4-2 6-1 11 2 5 10 8 15 10 4 1 8 2 12 2h5c3 0 5-1 8-1-1-1-2-1-3-2 2 0 3 0 5-1s5-2 6-5c1-2 1-2 0-4v-1c-2-2-4-3-6-3v-1l-1-1c4 0 7 2 10 4l1 1c1 2 2 2 2 4-1 1-1 2-2 3l2 1h1c3-1 5-2 8-3 2-1 3-1 5-1l7-3 9-3 1 1-2 1c1 1 2 1 3 2l-5 4c-4 4-10 8-16 11l-14 6h-7l-5 2c-3 2-8 3-11 3h0c-1-1-1-1-2-1-2 0-2 0-4-1-14 1-27-1-41-3l-13-2-13-2c-3-1-5-2-7-1-12-2-22-4-34-4-1 1-1 1-2 1-2 0-3 0-5 1h-3l-1 1-6 1c-2 0-3 0-5 1l-1-1-7 2-7 2h0c-2 0-4-1-5-1v-1c3-1 5-3 7-5l-10 5h0l-2-1-4 2-3-1 22-14c1 0 13-7 16-8 1-1 4-1 5-2 4-1 8-2 11-2 0-1 0-2 1-2h2z" class="k"></path><path d="M689 728h3l-2 2-6-1h3 1l1-1z" class="t"></path><path d="M657 727h2l1 1h7l-11 1c0-1 0-2 1-2z" class="b"></path><path d="M713 754c4-1 7 1 11 2h0c2 0 4 1 5 1-4 1-11-2-16-3z" class="T"></path><path d="M692 728l7 1-2 1v1l-7-1 2-2z" class="u"></path><path d="M642 750c5-1 9-1 14-1-1 1-1 1-2 1-2 0-3 0-5 1h-3l-1 1-3-2z" class="C"></path><path d="M635 752l7-2 3 2-6 1c-2 0-3 0-5 1l-1-1 2-1z" class="B"></path><path d="M699 729c3 0 6 0 8 1l3 1h-1c-1 0-2 0-3 1-3 0-6-1-9-1v-1l2-1z" class="w"></path><path d="M707 730l12 1c-1 0-1 1-2 2l-11-1c1-1 2-1 3-1h1l-3-1z" class="u"></path><path d="M680 727l9 1-1 1h-1-3l-10-1h-1c1-1 2-1 3-1h4z" class="v"></path><path d="M729 730c1 1 1 1 3 1l-1 1c-1 0-1 0-2 1-1 0-1-1-2-1-3 1-7 1-10 1 1-1 1-2 2-2 3 0 7 0 10-1z" class="x"></path><path d="M659 727c5-1 9-1 14-1 2 0 5 0 7 1h-4c-1 0-2 0-3 1h1-7-7l-1-1z" class="t"></path><path d="M825 735c1 1 2 1 3 2l-5 4c-2 0-4 2-7 3 1-2 2-2 3-3 2-1 3-2 4-3-2 1-5 3-7 3h-1l10-6z" class="S"></path><path d="M817 736l9-3 1 1-2 1-10 6-7 3v-1l1-1c3-1 6-3 8-6z" class="M"></path><path d="M711 748c-6-1-12-1-16-3v-1h2v1c2 0 1 0 2 1h5c1 0 1 0 2 1 0-1 1-2 2-2 0-1 2-1 3-1 0 0 1 1 2 1l-1-1c-3 0-6 0-9-1s-6 0-8 0c8-1 16 1 25 1-4 2-7 2-11 2h-1 0 1c0 1 1 1 2 2z" class="P"></path><path d="M658 733h13 6c2 0 4 0 6 1-2 1-5 0-7 0-3-1-8-1-11 0h-1c-4 0-7 0-11 1-2 0-5 2-7 1h1l1-1h0c-2 0-2 1-3 0v-1h6c2-1 5-1 7-1z" class="T"></path><path d="M816 744c3-1 5-3 7-3-4 4-10 8-16 11-1-1-2-1-3 0-1 0 0 0-1-1l2-1 11-6z" class="W"></path><path d="M764 761c8-1 16-3 24-6l-1 1-7 4h1c-3 2-8 3-11 3h0c-1-1-1-1-2-1-2 0-2 0-4-1zm-120-21c2-1 6-2 8-1-2 1-5 1-7 2h-1 2 2c-1 1-2 1-4 2l-13 4h-2c0-3 6-4 9-5l-1-1c1-1 5-1 7-1z" class="R"></path><path d="M644 743h3l1 1c7-2 15-2 23-2h0c-5 0-9 1-14 1l-19 4-7 2v-2l13-4z" class="d"></path><path d="M629 747h2v2c-4 1-5 2-8 4 4 0 8-2 12-1l-2 1-7 2-7 2h0c-2 0-4-1-5-1v-1c3-1 5-3 7-5h0l7-3h1z" class="L"></path><path d="M621 750h0l7-3-1 1c-1 2-2 2-3 3-2 0-3 2-5 3v1h1 3 3l-7 2h0c-2 0-4-1-5-1v-1c3-1 5-3 7-5z" class="P"></path><path d="M788 755c2-2 6-3 9-4s5-1 8-1l-2 1c1 1 0 1 1 1 1-1 2-1 3 0l-14 6h-7l-5 2h-1l7-4 1-1z" class="G"></path><path d="M786 758l9-3c2-1 5-3 8-4 1 1 0 1 1 1 1-1 2-1 3 0l-14 6h-7z" class="C"></path><defs><linearGradient id="Ao" x1="603.397" y1="753.1" x2="623.421" y2="744.478" xlink:href="#B"><stop offset="0" stop-color="#a4a3a3"></stop><stop offset="1" stop-color="#cdcbcc"></stop></linearGradient></defs><path fill="url(#Ao)" d="M624 741c1 0 13-7 16-8v1c-2 1-3 2-5 3l-8 4c-1 0-3 1-4 2l1 2c1-1 2-1 3-1 0-1 1-1 2-1l1-1h2 1v-1h2c1 0 1 0 2-1h1 1c1 0 1 0 2-1l3 1c-2 0-6 0-7 1l1 1c-3 1-9 2-9 5h-1l-7 3h0l-10 5h0l-2-1-4 2-3-1 22-14z"></path><path d="M637 741l1 1c-3 1-9 2-9 5h-1l-7 3h0l-10 5h0l-2-1c2-2 5-3 8-5 7-3 13-5 20-8z" class="M"></path><path d="M783 730c4 0 7 2 10 4l1 1c1 2 2 2 2 4-1 1-1 2-2 3l2 1h1c3-1 5-2 8-3 2-1 3-1 5-1l7-3c-2 3-5 5-8 6l-1 1v1c-1 1-3 2-5 2-2 2-4 2-6 3-6 2-14 4-21 4h-6 1l-1-1c-2 0-5 0-7-2l1 1c2-1 3-1 5-2h5c3 0 5-1 8-1-1-1-2-1-3-2 2 0 3 0 5-1s5-2 6-5c1-2 1-2 0-4v-1c-2-2-4-3-6-3v-1l-1-1z" class="D"></path><path d="M796 743c0 1-1 2-2 2h-2v-1l2-2 2 1z" class="E"></path><path d="M764 751c4 1 7 0 11 0 1-1 6-2 7-1-2 1-4 1-6 3h-6 1l-1-1c-2 0-5 0-7-2l1 1z" class="C"></path><path d="M810 739l7-3c-2 3-5 5-8 6l-1 1v1c-1 1-3 2-5 2h0c0-1 0-1 1-1 1-1 2-1 3-2h-1l-5 2c3-3 6-4 9-6z" class="W"></path><path d="M783 730c4 0 7 2 10 4l1 1c0 3 0 5-3 8-2 3-6 4-9 5-1-1-2-1-3-2 2 0 3 0 5-1s5-2 6-5c1-2 1-2 0-4v-1c-2-2-4-3-6-3v-1l-1-1z" class="a"></path><defs><linearGradient id="Ap" x1="754.049" y1="766.19" x2="721.951" y2="732.31" xlink:href="#B"><stop offset="0" stop-color="#979697"></stop><stop offset="1" stop-color="#c4c4c3"></stop></linearGradient></defs><path fill="url(#Ap)" d="M737 731l6-5c-2 4-2 6-1 11 2 5 10 8 15 10 4 1 8 2 12 2-2 1-3 1-5 2l-1-1c2 2 5 2 7 2l1 1h-1c-9 1-18 1-28-1s-21-2-31-4c-1-1-2-1-2-2h-1 0 1c4 0 7 0 11-2 3 1 6 0 9-2 2-2 6-6 7-8v-3h1z"></path><path d="M737 731l6-5c-2 4-2 6-1 11 2 5 10 8 15 10 4 1 8 2 12 2-2 1-3 1-5 2l-1-1c-7-1-14-3-19-7-5-3-6-7-7-12z" class="c"></path><defs><linearGradient id="Aq" x1="256.918" y1="502.127" x2="228.045" y2="503.775" xlink:href="#B"><stop offset="0" stop-color="#716f71"></stop><stop offset="1" stop-color="#c8c7c7"></stop></linearGradient></defs><path fill="url(#Aq)" d="M223 443l13-14h3l2 1-2 2-2 8v6-1c1-1 2-2 3-2 1-2 2-4 4-5 1 0 1 0 2 1h1l1-1c2-1 4-3 5-4h1c2-2 4-4 6-5 2 1 4 1 7 1l1 1c0 7-2 16-5 23-1-2-1-3-2-5l-1-2c-1 2-2 2-3 4-3 0-3 0-4 2l-3 1-1 2c-1 2-1 3-2 5v1c-3 8-5 16-6 25l-1 1v1c0 1 0 2-1 3v3l-1 1c0 1 1 3 0 4 0 2 0 4-1 6h0l-1-2-1-1c-1 0-1-2-1-2v3c0 1 0 3 1 5 0 2 1 4 1 6s0 5 1 8l1 8c2 12 3 24 3 36-1 1-1 2-1 4-2 3-1 7-1 11v2c1 2 2 12 3 12l3 15h-1s0-2-1-2c0-5-2-14-5-18v-2h-1v1c0 2 0 4 1 7v4h-1c-1-4-1-8-3-11s-5-5-9-6l-1-3c0-1-1-1-2-1-1-1-2 0-3 0l-1 1v-1l-1 1c-1 1 0 1-1 2v-1-2c2-5 2-10 2-14v-1-49-12h-2c1-3 0-7 0-10l-3 7v1l-1 1-1 1v1-5l2-11v-5l-2 3c0-2 0-4 1-6h-1l-2 2v2l-1 1c-3-3-4-6-6-9v-1-1l6-8c-1-1-1 0-1-1l4-5c1-2 1-2 0-4l12-14z"></path><path d="M240 451h1l-1 2c1 1 1 2 1 3-1 1-1 2 0 4l-1-1v-2c-1-2 0-4 0-6zm0-8l-2 2c0 2 0 2-1 3-1 2-2 8-2 9 1-1 0-3 1-4 1 2-1 4-1 6l-3 13c-1 1-1 2-2 3l3-16c1-3 2-6 2-9 1-1 1-3 2-4v-1c1-1 2-2 3-2z" class="g"></path><path d="M230 475c1-1 1-2 2-3l-2 34v-8c-2 2-1 9-2 12 0 1 0 1 1 2 0 2 0 4-1 6h0v-11-8-5l2-19z" class="B"></path><path d="M230 542h1l5 26c1 4 2 9 3 14v2c1 2 2 12 3 12l3 15h-1s0-2-1-2c0-5-2-14-5-18v-2l-4-27-1-3-1-6c-1-3-1-7-2-11z" class="a"></path><path d="M234 562l4 27h-1v1c0 2 0 4 1 7v4h-1c-1-4-1-8-3-11s-5-5-9-6l-1-3 6 3 1 2c1-2 0-3 0-4 0-3 2-5 2-8v-6l1-6z" class="f"></path><defs><linearGradient id="Ar" x1="226.109" y1="460.136" x2="234.622" y2="465.308" xlink:href="#B"><stop offset="0" stop-color="#3f3d3e"></stop><stop offset="1" stop-color="#716665"></stop></linearGradient></defs><path fill="url(#Ar)" d="M233 449c1-3 2-6 4-9v6c-1 1-1 3-2 4 0 3-1 6-2 9l-3 16-2 19-1-2v-6c0-2 0-3-1-5 0-1 0-1-1-2-2 2-2 6-4 6 2-11 8-22 9-33l1-2h1l1-1z"></path><path d="M233 449c1-3 2-6 4-9v6c-1 1-1 3-2 4v-1-1h0v-2l-2 4v-1z" class="Y"></path><path d="M227 486l1-9c0-3 1-5 1-7 1-4 1-8 4-11l-3 16-2 19-1-2v-6z" class="m"></path><defs><linearGradient id="As" x1="238.202" y1="563.275" x2="218.578" y2="552.432" xlink:href="#B"><stop offset="0" stop-color="#929092"></stop><stop offset="1" stop-color="#b1b0b0"></stop></linearGradient></defs><path fill="url(#As)" d="M224 545c0-3 1-5 1-8h1l1 1h1c1 1 1 3 2 4 1 4 1 8 2 11l1 6 1 3-1 6v6c0 3-2 5-2 8 0 1 1 2 0 4l-1-2-6-3c0-1-1-1-2-1-1-1-2 0-3 0 0-1 1-2 1-4 1-2 1-5 2-8s2-8 2-12c-1-2 0-7 0-10v-1z"></path><path d="M225 558h1c1 2 1 4 2 7-1 1-1 1-1 3l1 1v1 1h0 0c-2-4-2-8-3-13z" class="B"></path><path d="M224 545c0-3 1-5 1-8h1l1 1h1c1 1 1 3 2 4 1 4 1 8 2 11l1 6 1 3-1 6-2 1v1l-3-28c-1 0-1-1-1-1 0-1 0-1-1-2l1 2-3 3v1z" class="F"></path><path d="M224 556l1 2c1 5 1 9 3 13h0l3 12-1 1-6-3c0-1-1-1-2-1-1-1-2 0-3 0 0-1 1-2 1-4 1-2 1-5 2-8s2-8 2-12z" class="G"></path><defs><linearGradient id="At" x1="275.269" y1="441.58" x2="228.364" y2="474.47" xlink:href="#B"><stop offset="0" stop-color="#979697"></stop><stop offset="1" stop-color="#b7b7b6"></stop></linearGradient></defs><path fill="url(#At)" d="M254 434c2-2 4-4 6-5 2 1 4 1 7 1l1 1c0 7-2 16-5 23-1-2-1-3-2-5l-1-2c-1 2-2 2-3 4-3 0-3 0-4 2l-3 1-1 2c-1 2-1 3-2 5v1c-3 8-5 16-6 25l-1 1v1c0 1 0 2-1 3v-2h-1l-1 3s-1 0-1 1v1h0l1 2-1 1-1-1v-2c1-2 0-7 0-9l1-3v-1c0-1 1-3 1-4-1-1-1-3 0-4h0v3h1c0-2 0-5 2-7 1-3 0-6 1-10 1-3 2-8 4-11 2-6 6-11 9-15z"></path><path d="M235 486l1-3v-1 5c1-1 1-2 2-4 0 3 0 6-1 8l-1 1v1c-1-2 0-5-1-7z" class="D"></path><path d="M236 482c0-1 1-3 1-4-1-1-1-3 0-4h0v3h1c0-2 0-5 2-7 0 1-1 2-1 3-1 3-1 7-1 10-1 2-1 3-2 4v-5z" class="R"></path><path d="M249 456l-3 3h0c3-7 9-13 12-20 1-2 1-3 2-5v-2h1v3h1 0l-2 5 1 2-1 2c1 1 2 3 1 5l-1-2c-1 2-2 2-3 4-3 0-3 0-4 2l-3 1-1 2z" class="W"></path><path d="M250 454c2-3 3-5 5-7 1-1 3-1 4-1l1 1c-1 2-2 2-3 4-3 0-3 0-4 2l-3 1z" class="B"></path><path d="M226 481c1 2 1 3 1 5v6l1 2v5 8 11h0l1 6c0 3 0 6 1 10 0 2 1 5 1 8h-1c-1-1-1-3-2-4h-1l-1-1h-1c0 3-1 5-1 8v1c0 3-1 8 0 10 0 4-1 9-2 12s-1 6-2 8c0 2-1 3-1 4l-1 1v-1l-1 1c-1 1 0 1-1 2v-1-2c2-5 2-10 2-14v-1-49-12c0-2 0-5 1-7 0-1 1-1 1-2 0-3 1-5 2-7v-2h0c1-2 3-4 4-5z" class="g"></path><path d="M226 481c1 2 1 3 1 5v6l1 2v5 8 1c-1 1-1 2-1 3v1h0v2h0c-1-1-1-1-2-1h0v-2c0-2 0-2-1-3 0 2-1 5 0 7l-1 12c-1 2 0 5-1 7-1-2-1-12-1-15 1-6 2-11 2-17 1-5 2-9 1-14 0-1 0-1 1-2h0l-3 2v-2h0c1-2 3-4 4-5z" class="i"></path><path d="M226 481c1 2 1 3 1 5v6l-1 3h0v-1-5c-1-1 0-3-1-4l-1-1-2 2h0c1-2 3-4 4-5z" class="Q"></path><path d="M226 495h0l1-3 1 2v5 8 1c-1 1-1 2-1 3v1h0v2h0c-1-1-1-1-2-1h0v-2c0-2 0-2-1-3 0-3 1-6 2-9v-4z" class="V"></path><path d="M226 495h0l1-3 1 2v5s-1 1-1 2h0v-3l-1 2v-1-4z" class="a"></path><defs><linearGradient id="Au" x1="231.255" y1="516.276" x2="216.655" y2="553.188" xlink:href="#B"><stop offset="0" stop-color="#514e4f"></stop><stop offset="1" stop-color="#949193"></stop></linearGradient></defs><path fill="url(#Au)" d="M224 508c1 1 1 1 1 3v2h0c1 0 1 0 2 1h0v-2h0v-1c0-1 0-2 1-3v-1 11h0l1 6c0 3 0 6 1 10 0 2 1 5 1 8h-1c-1-1-1-3-2-4h-1l-1-1h-1c0 3-1 5-1 8v1c0 3-1 8 0 10 0 4-1 9-2 12s-1 6-2 8c0 2-1 3-1 4l-1 1v-1l-1 1c-1 1 0 1-1 2v-1-2c2-5 2-10 2-14v-1c2-5 1-12 2-17l1-8 1-6c1-2 0-5 1-7l1-12c-1-2 0-5 0-7z"></path><path d="M228 518h0l1 6c-1-1-1-1-1-2-1-1-1-3 0-4z" class="e"></path><path d="M224 515v1c-1 6 0 12-1 17l-1 8-1-1 1-6c1-2 0-5 1-7l1-12z" class="V"></path><defs><linearGradient id="Av" x1="213.022" y1="558.805" x2="225.478" y2="566.695" xlink:href="#B"><stop offset="0" stop-color="#777578"></stop><stop offset="1" stop-color="#8c8b89"></stop></linearGradient></defs><path fill="url(#Av)" d="M218 565c2-5 1-12 2-17l1-8 1 1c0 3-1 9 0 12l1 3v-3c0-2 1-5 1-7 0 3-1 8 0 10 0 4-1 9-2 12s-1 6-2 8c0 2-1 3-1 4l-1 1v-1l-1 1c-1 1 0 1-1 2v-1-2c2-5 2-10 2-14v-1z"></path><path d="M222 553l1 3v-3c0-2 1-5 1-7 0 3-1 8 0 10 0 4-1 9-2 12v-15z" class="B"></path><defs><linearGradient id="Aw" x1="227.465" y1="476.528" x2="213.635" y2="474.926" xlink:href="#B"><stop offset="0" stop-color="#b7b7b7"></stop><stop offset="1" stop-color="#e2e1e0"></stop></linearGradient></defs><path fill="url(#Aw)" d="M223 443l13-14h3l2 1-2 2-2 8c-2 3-3 6-4 9l-1 1h-1l-1 2c-1 11-7 22-9 33 2 0 2-4 4-6 1 1 1 1 1 2-1 1-3 3-4 5h0v2c-1 2-2 4-2 7 0 1-1 1-1 2-1 2-1 5-1 7h-2c1-3 0-7 0-10l-3 7v1l-1 1-1 1v1-5l2-11v-5l-2 3c0-2 0-4 1-6h-1l-2 2v2l-1 1c-3-3-4-6-6-9v-1-1l6-8c-1-1-1 0-1-1l4-5c1-2 1-2 0-4l12-14z"></path><path d="M221 485c2 0 2-4 4-6 1 1 1 1 1 2-1 1-3 3-4 5h0v2c-1 2-2 4-2 7 0 1-1 1-1 2 0-4 0-9 2-12z" class="M"></path><path d="M217 473h1l2-2c-2 6-4 13-7 18v-5l-2 3c0-2 0-4 1-6l1-3 3-1c0-2 1-3 1-4z" class="E"></path><path d="M213 478l3-1-3 7-2 3c0-2 0-4 1-6l1-3z" class="N"></path><defs><linearGradient id="Ax" x1="225.128" y1="438.238" x2="233.687" y2="449.239" xlink:href="#B"><stop offset="0" stop-color="#342120"></stop><stop offset="1" stop-color="#433e3e"></stop></linearGradient></defs><path fill="url(#Ax)" d="M223 443l13-14h3l2 1-2 2-2 8c-2 3-3 6-4 9l-1 1h-1l-1 2c0-2 0-3 1-4v-1-1h0l-3 6c0 1-1 1-1 2-2 2-4 7-5 11h-2l1-3c-1-2 0-7 0-9 1-2 2-3 1-5 0-1 0-2 1-3v-2z"></path><path d="M235 436c0 2-1 5-2 7h0-1c-1 1-3 1-4 3 2-4 4-7 7-10z" class="Z"></path><path d="M235 436v-1c1-2 3-2 4-3l-2 8c-2 3-3 6-4 9l-1 1h-1l2-7h0c1-2 2-5 2-7z" class="h"></path><path d="M228 446c1-2 3-2 4-3h1l-2 7-1 2c0-2 0-3 1-4v-1-1h0l-3 6c0 1-1 1-1 2-2 2-4 7-5 11h-2l1-3 7-16z" class="C"></path><defs><linearGradient id="Ay" x1="220.56" y1="467.408" x2="201.749" y2="466.919" xlink:href="#B"><stop offset="0" stop-color="#6b676a"></stop><stop offset="1" stop-color="#878483"></stop></linearGradient></defs><path fill="url(#Ay)" d="M223 443v2c-1 1-1 2-1 3 1 2 0 3-1 5 0 2-1 7 0 9l-1 3h2l-1 2c0 1-1 3-1 4l-2 2h-1c0 1-1 2-1 4l-3 1-1 3h-1l-2 2v2l-1 1c-3-3-4-6-6-9v-1-1l6-8c-1-1-1 0-1-1l4-5c1-2 1-2 0-4l12-14z"></path><path d="M209 483c0-2 1-8 2-9l1-1v-2c0-1 1-2 1-3v-1l1-1c0-1 0-2 1-4h0v6l-4 13-2 2z" class="M"></path><path d="M223 443v2c-1 1-1 2-1 3-5 6-9 13-14 19-1-1-1 0-1-1l4-5c1-2 1-2 0-4l12-14z" class="m"></path><path d="M220 456l1-3c0 2-1 7 0 9l-1 3h2l-1 2c0 1-1 3-1 4l-2 2h-1c0 1-1 2-1 4l-3 1-1 3h-1l4-13c1-1 1-3 2-5 0-2 1-5 3-7z" class="O"></path><path d="M220 456l1-3c0 2-1 7 0 9l-1 3-1 1v-2h0c-1-2 1-6 1-8z" class="V"></path><path d="M213 478l3-6h0c1 0 1-1 1-1v2c0 1-1 2-1 4l-3 1z" class="l"></path><path d="M220 465h2l-1 2c0 1-1 3-1 4l-2 2h-1v-2c1-1 1-2 2-3v-2l1-1z" class="S"></path><path d="M220 465h2l-1 2h-1l-1 1v-2l1-1z" class="R"></path><path d="M780 377h2c2 3 4 5 7 7l4 4 2 3c2 2 7 7 9 7l8 6a57.31 57.31 0 0 1 11 11l3 4h0l-4-1-1 1 12 4c5 1 10 2 15 5l10 7h0c1 2 1 2 2 3h0c4 6 5 13 3 20v1c-1 5-4 9-8 12-2 1-4 2-7 2l-1-1c3-4 6-8 7-13 2-7 0-13-4-19-1 1-2 2-2 3l1 1c1 1 2 1 3 3h0-1l-1 1c-2 0-4 1-5 1l-1 1c-4 2-7 4-11 5v1c-1 1-2 2-2 3l-2 3-2 3v4c1 2 2 4 3 7 1 1 5 5 5 6v1c0 1 0 2 1 4h-1v1l-3-4-2-2h-2l-8-11c-2-1-2-1-3-2s-2-2-2-3l-1 1 1 2c-2 0-3-1-4-2-2-3-4-5-6-8v-3c-1-2-3-4-4-5v-1l-2-2h0l-5-2-1-3c-2-2-3-3-6-3-1-2-1-2-1-3s-1-1-2-2h-1l-2-4c-5-5-11-9-17-14l1-1-3-1-1-3-2-1c-1-3-3-4-5-6h-2l-8-2h-3c0-1-1-2-3-3 0 0 0-1-1-1h-1l-1-1v-1c4-1 10-3 14-5l-1-1c-1 0-1 0-1-1 1-2 2-4 4-6l4-4 1 1v1h0c2-1 3-2 4-3h1c4-1 8 0 12-1h2c-1 1 0 1-1 1l2 1h0c1 0 2 0 3 1l1-4z" class="k"></path><path d="M779 414c6-2 11 1 17 2 3 1 7 1 10 3 1 1 3 1 4 2l-15-4c-5-2-11-2-16-3z" class="c"></path><path d="M776 413l3 1c5 1 11 1 16 3 0 2 1 2 3 3l-18-3-4-4z" class="V"></path><path d="M795 417l15 4c4 1 8 2 12 4-2 0-3 0-5-1h0c2 1 3 2 4 3l-4-1c-3-1-5-2-8-3-2 0-3-1-4-1l-7-2c-2-1-3-1-3-3z" class="q"></path><path d="M821 427c-1-1-2-2-4-3h0c2 1 3 1 5 1 4 0 11 2 16 4-1 1-1 1 0 2h-2 3v2h0c-2-1-3-2-5-2l-11-3-2-1z" class="h"></path><path d="M754 405c4 1 10 2 14 4l5 2-1 1h0c-2-1-5-1-7-2v1h-1c2 2 4 3 5 5l-8-4-2-1c-1-3-3-4-5-6z" class="T"></path><path d="M773 406v-1c9 2 17 6 26 7 3 1 5 2 8 2h1v-1h0l1-1c-2-1-3-2-5-2l-1-3 10 6c3 2 5 4 9 5l-1 1-48-13z" class="V"></path><path d="M780 417l18 3 7 2c1 0 2 1 4 1l8 3h-4-6c-3 1-5 3-6 4-1 2-1 2-2 3-1-1-1-1-1-3l-2 1c-1-1-2-2-4-3l-2-2-10-9z" class="W"></path><path d="M809 423l8 3h-4-6c-3 1-5 3-6 4-1 2-1 2-2 3-1-1-1-1-1-3l1-1c1-2 6-4 9-5h0l1-1z" class="N"></path><path d="M780 417l18 3 7 2h0c-4 0-7-1-11-1-1 1-1 0-2 0 0 2 3 5 4 6s2 2 3 2l-1 1-2 1c-1-1-2-2-4-3l-2-2-10-9z" class="Q"></path><path d="M843 431h2c6 4 13 9 15 16 1 4 1 7 1 10l1 1h-1v4l1-1v-2h1c-1 5-4 9-8 12-2 1-4 2-7 2l-1-1c3-4 6-8 7-13 2-7 0-13-4-19l-4-6-1-1-2-2z" class="c"></path><path d="M860 447c1 4 1 7 1 10-1 1 0 4-1 5-1-2 0-5 0-7v-8z" class="P"></path><path d="M843 431h2c6 4 13 9 15 16v8c0-2 0-4-1-6-3-7-6-12-14-16l-2-2z" class="n"></path><defs><linearGradient id="Az" x1="780.812" y1="399.422" x2="779.447" y2="404.341" xlink:href="#B"><stop offset="0" stop-color="#493b3c"></stop><stop offset="1" stop-color="#645555"></stop></linearGradient></defs><path fill="url(#Az)" d="M768 398c9 1 19 1 27 5h0l3 1c2 1 3 2 5 2v1l1 3c2 0 3 1 5 2l-1 1h0v1h-1c-3 0-5-1-8-2-9-1-17-5-26-7v1c-5-1-12-4-16-5l-4-1c2-2 12-2 15-2z"></path><path d="M795 403l3 1c2 1 3 2 5 2v1l1 3-2-2-3-1c0-1 0-1-1-2-1 0-2-1-3-2z" class="e"></path><path d="M789 405c2 1 5 2 8 3 3 2 7 5 11 6h-1c-3 0-5-1-8-2-1-1-2-2-4-3-1 0-3 0-4-1s-2-2-2-3z" class="F"></path><path d="M757 401c3-1 5 0 8 0s7 0 10 1l14 3c0 1 1 2 2 3s3 1 4 1c2 1 3 2 4 3-9-1-17-5-26-7v1c-5-1-12-4-16-5z" class="B"></path><path d="M765 401c3 0 7 0 10 1l-2 1h1 0c-2 1-7-1-9-2z" class="W"></path><path d="M780 377h2c2 3 4 5 7 7l4 4 2 3c2 2 7 7 9 7l8 6a57.31 57.31 0 0 1 11 11l3 4h0l-4-1c-4-1-6-3-9-5l-10-6v-1c-2 0-3-1-5-2l-3-1h0c-8-4-18-4-27-5-3 0-13 0-15 2h-4c-1 1-2 1-3 1l-2 1v1h0-3c0-1-1-2-3-3 0 0 0-1-1-1h-1l-1-1v-1c4-1 10-3 14-5l-1-1c-1 0-1 0-1-1 1-2 2-4 4-6l4-4 1 1v1h0c2-1 3-2 4-3h1c4-1 8 0 12-1h2c-1 1 0 1-1 1l2 1h0c1 0 2 0 3 1l1-4z" class="k"></path><path d="M770 381h3c2 1 3 1 4 2v1c-4-1-9-1-13-1l6-2z" class="I"></path><path d="M755 380l1 1v1h0l-1 3c-2 1-5 2-7 5v1c-1 0-1 0-1-1 1-2 2-4 4-6l4-4z" class="K"></path><path d="M756 382c2-1 3-2 4-3h1l-2 2v1c3-1 6-2 9-1h2l-6 2-9 2 1-3z" class="o"></path><path d="M776 380c1 0 2 0 3 1h0c2 3 6 4 8 7h-1c-3-1-7-2-9-4v-1c-1-1-2-1-4-2h3v-1z" class="D"></path><path d="M773 378h2c-1 1 0 1-1 1l2 1h0v1h-3-3-2c-3-1-6 0-9 1v-1l2-2c4-1 8 0 12-1z" class="q"></path><path d="M750 391c6-2 15-2 22-1v1h-1c-1 1-1 0-2 1s-3 1-5 1h0l-1-1c-4-1-9 1-13-1z" class="Y"></path><path d="M812 404a57.31 57.31 0 0 1 11 11l3 4h0l-4-1c-4-1-6-3-9-5h0l1-1c1 1 3 2 4 3 1 0 1 1 3 1v-1c-1-2-3-4-5-5h0 2c-1 0-2-1-2-2-2-1-3-2-4-3v-1z" class="d"></path><path d="M780 377h2c2 3 4 5 7 7l4 4 2 3c-3 0-6-1-9-3h1c-2-3-6-4-8-7h0l1-4z" class="H"></path><path d="M780 377h2c2 3 4 5 7 7h-3c-2-2-3-3-6-4l-1 1h0l1-4z" class="L"></path><path d="M772 390c4 0 7 1 11 2 3 2 6 4 9 5l-1 1c-4-2-8-3-12-3-4-1-11 1-15-2 2 0 4 0 5-1s1 0 2-1h1v-1z" class="H"></path><path d="M749 392l1-1c4 2 9 0 13 1l1 1h0c4 3 11 1 15 2 4 0 8 1 12 3l1-1c2 1 4 2 5 3s2 3 4 4c1 0 1 1 2 2-2 0-3-1-5-2l-3-1h0c-8-4-18-4-27-5-3 0-13 0-15 2h-4c-1 1-2 1-3 1l-2 1v1h0-3c0-1-1-2-3-3 0 0 0-1-1-1h-1l-1-1v-1c4-1 10-3 14-5z" class="c"></path><path d="M768 398h5c1 0 0 0 1-1 4-1 11 1 15 2h1c1 0 1 0 2 1h1c1 1 2 1 3 1v1l1 1s1 0 1 1l-3-1h0c-8-4-18-4-27-5z" class="T"></path><defs><linearGradient id="BA" x1="745.293" y1="401.775" x2="751.207" y2="388.225" xlink:href="#B"><stop offset="0" stop-color="#0b1012"></stop><stop offset="1" stop-color="#4b3736"></stop></linearGradient></defs><path fill="url(#BA)" d="M749 392l1-1c4 2 9 0 13 1l1 1-27 6h-1l-1-1v-1c4-1 10-3 14-5z"></path><path d="M769 416c-1-2-3-3-5-5h1v-1c2 1 5 1 7 2h0l1-1c1 1 2 1 3 2l4 4 10 9 2 2c2 1 3 2 4 3l2-1c0 2 0 2 1 3 1-1 1-1 2-3 1-1 3-3 6-4h6 4l4 1 2 1 11 3c2 0 3 1 5 2h0v-2h-3 2c-1-1-1-1 0-2l5 2 2 2 1 1 4 6c-1 1-2 2-2 3l1 1c1 1 2 1 3 3h0-1l-1 1c-2 0-4 1-5 1l-1 1c-4 2-7 4-11 5v1c-1 1-2 2-2 3l-2 3-2 3v4c1 2 2 4 3 7 1 1 5 5 5 6v1c0 1 0 2 1 4h-1v1l-3-4-2-2h-2l-8-11c-2-1-2-1-3-2s-2-2-2-3l-1 1 1 2c-2 0-3-1-4-2-2-3-4-5-6-8v-3c-1-2-3-4-4-5v-1l-2-2h0l-5-2-1-3c-2-2-3-3-6-3-1-2-1-2-1-3s-1-1-2-2h-1l-2-4c-5-5-11-9-17-14l1-1-3-1-1-3 8 4z" class="c"></path><path d="M792 428c2 1 3 2 4 3l2-1c0 2 0 2 1 3 1-1 1-1 2-3l1 4 4 4c-1 0-2-1-3-1 2 5 8 8 9 13l-20-22z" class="M"></path><path d="M814 460c1 1 1 2 2 3 3 2 4 4 5 7 3 4 7 7 9 12h-2l-8-11c-2-4-4-7-7-10l1-1z" class="T"></path><path d="M789 432c9 8 17 18 25 28l-1 1-4-5-1-1-3-3h0c-2-1-3-2-4-2l-2-2h0c-1-2-3-4-4-6v-1l-1-1c-2-2-6-5-5-8z" class="o"></path><path d="M795 442v-1c3 3 7 6 10 10v1h0c-2-1-3-2-4-2l-2-2h0c-1-2-3-4-4-6z" class="m"></path><path d="M801 450c1 0 2 1 4 2h0l3 3 1 1 4 5c3 3 5 6 7 10-2-1-2-1-3-2s-2-2-2-3l-1 1 1 2c-2 0-3-1-4-2-2-3-4-5-6-8v-3c-1-2-3-4-4-5v-1z" class="a"></path><path d="M805 456c1 1 8 11 9 11l1 2c-2 0-3-1-4-2-2-3-4-5-6-8v-3z" class="i"></path><path d="M812 450c-1-5-7-8-9-13 1 0 2 1 3 1l1 1 3 3 1-1c3 3 7 6 9 10l1-1c1 1 2-1 3-2-1 3-2 6-4 8 1 1 1 3 1 3v2 2c-1-1-2-2-2-3l-7-10z" class="H"></path><path d="M812 450c-1-5-7-8-9-13 1 0 2 1 3 1l1 1 3 3c4 3 7 8 9 13h0l-6-7c2 4 5 7 6 12l-7-10z" class="C"></path><path d="M761 412l8 4 2 2c2 1 8 5 9 7 2 1 7 5 9 7-1 3 3 6 5 8l1 1v1c1 2 3 4 4 6l-5-2-1-3c-2-2-3-3-6-3-1-2-1-2-1-3s-1-1-2-2h-1l-2-4c-5-5-11-9-17-14l1-1-3-1-1-3z" class="b"></path><path d="M771 418c2 1 8 5 9 7h-1c-1-1-2-1-3-2h-1l-2-2c-1-1-1-1-1-2l-1-1z" class="X"></path><path d="M781 431c1 1 2 2 4 3s4 2 6 4h-3c-1 1-1 0-1 2-1-2-1-2-1-3s-1-1-2-2h-1l-2-4z" class="r"></path><path d="M787 440c0-2 0-1 1-2h3c1 2 2 3 4 4 1 2 3 4 4 6l-5-2-1-3c-2-2-3-3-6-3z" class="K"></path><path d="M824 439l1-1h1c1 1 1 1 3 1v1s0 1 1 1l1-1h0c1 0 2 0 3-1 1 1 2 1 2 3 0 1-1 3-1 4-1 1-2 2-3 4h0v1c-1 0-2 1-3 2-1 2-1 4-2 5 2-1 3-2 6-2-1 1-2 2-2 3l-2 3-2 3v4c1 2 2 4 3 7 1 1 5 5 5 6v1c0 1 0 2 1 4h-1v1l-3-4 1-1-11-17c0-1-1-2-1-3v-2-2s0-2-1-3c2-2 3-5 4-8-1 1-2 3-3 2 2-4 3-7 3-12v1z" class="T"></path><path d="M827 447c1-1 1-2 3-3h2l1 1-2 3h1v2 1c-1 0-2 1-3 2v-1c0-2 1-4 2-5v-2c-1 1-3 1-4 2z" class="H"></path><path d="M827 458c2-1 3-2 6-2-1 1-2 2-2 3l-2 3-2 3v4c-1-1-1-2-1-3v-1-3l1-4z" class="G"></path><path d="M824 439l1-1h1c1 1 1 1 3 1v1s0 1 1 1l1-1h0c1 0 2 0 3-1 1 1 2 1 2 3 0 1-1 3-1 4-1 1-2 2-3 4h0v-2h-1l2-3-1-1h-2c-2 1-2 2-3 3l-1 2v-1h0-2c-1 1-2 3-3 2 2-4 3-7 3-12v1z" class="E"></path><path d="M824 439l1-1h1c1 1 1 1 3 1v1s0 1 1 1c0 1-1 2-1 3h-1c-2-2-3-3-4-5z" class="D"></path><path d="M838 429l5 2 2 2 1 1 4 6c-1 1-2 2-2 3l1 1c1 1 2 1 3 3h0-1l-1 1c-2 0-4 1-5 1l-1 1c-4 2-7 4-11 5v1c-3 0-4 1-6 2 1-1 1-3 2-5 1-1 2-2 3-2v-1h0c1-2 2-3 3-4 0-1 1-3 1-4 0-2-1-2-2-3-1 1-2 1-3 1h0l1-2 1-3c0-1-1-2 0-3l1-1c2 0 3 1 5 2h0v-2h-3 2c-1-1-1-1 0-2z" class="Z"></path><path d="M833 432l4 4c2 1 4 3 4 6-1 3-3 3-6 4l4-4c0-1-1-2-1-3-1-2-3-3-5-4 0-1-1-2 0-3z" class="I"></path><path d="M833 435c2 1 4 2 5 4 0 1 1 2 1 3l-4 4c-1 1-1 3-3 4 1-2 2-3 3-4 0-1 1-3 1-4 0-2-1-2-2-3-1 1-2 1-3 1h0l1-2 1-3z" class="C"></path><path d="M838 429l5 2 2 2 1 1 4 6c-1 1-2 2-2 3l1 1-1 1-1-1v-3h0c0-1-1-2-2-3-3-1-5-3-6-5h0v-2h-3 2c-1-1-1-1 0-2z" class="Y"></path><path d="M838 429l5 2 2 2 1 1-2 2-5-3v-2h-3 2c-1-1-1-1 0-2z" class="X"></path><path d="M849 444c1 1 2 1 3 3h0-1l-1 1c-2 0-4 1-5 1l-1 1c-4 2-7 4-11 5v1c-3 0-4 1-6 2 1-1 1-3 2-5 1-1 2-2 3-2s4-2 5-3c4-1 7-3 11-3l1-1z" class="n"></path><path d="M817 426l4 1 2 1 11 3-1 1c-1 1 0 2 0 3l-1 3-1 2-1 1c-1 0-1-1-1-1v-1c-2 0-2 0-3-1h-1l-1 1v-1c0 5-1 8-3 12l-1 1c-2-4-6-7-9-10l-1 1-3-3-1-1-4-4-1-4c1-1 3-3 6-4h6 4z" class="i"></path><path d="M823 428l11 3-1 1c-1 1 0 2 0 3l-1 3c-1 0-1-1-1-1-1-3-1-4-3-5-1 1 0 1 0 2l-1-1c-1-2-2-3-4-5z" class="y"></path><path d="M807 426h6c5 2 12 5 13 10v1c2 1 2 1 3 2-2 0-2 0-3-1h-1l-1 1v-1c-1-3-2-4-5-5h-1c-2-1-3-1-5-1h-2c0-1 1-2 1-2l-1-1c-1-1-3-2-4-3z" class="C"></path><path d="M801 430c1-1 3-3 6-4 1 1 3 2 4 3l1 1s-1 1-1 2h2c2 0 3 0 5 1h1c-4 1-7 2-10 4-1 0-1 0-1 1-1 0-1 0-1 1l-1-1-4-4-1-4z" class="S"></path><path d="M819 433c3 1 4 2 5 5 0 5-1 8-3 12l-1 1c-2-4-6-7-9-10l-1 1-3-3c0-1 0-1 1-1 0-1 0-1 1-1 3-2 6-3 10-4z" class="k"></path><path d="M807 439c0-1 0-1 1-1 0-1 0-1 1-1 0 2 1 3 2 4l-1 1-3-3z" class="L"></path><defs><linearGradient id="BB" x1="365.409" y1="725.105" x2="371.128" y2="703.905" xlink:href="#B"><stop offset="0" stop-color="#929192"></stop><stop offset="1" stop-color="#c9c8c8"></stop></linearGradient></defs><path fill="url(#BB)" d="M384 657l2-1c1-2 1-2 3-3l2 4c1 1 2 3 2 5l2 3c0 1 1 2 1 4 1 2 2 4 2 7-2 3-4 5-8 6h0c0 2-2 2-1 4v1l-1 1c1 4 4 5 7 7l1 3c1 5 4 7 5 11 3 6 7 11 11 15l2 1h1c1 1 3 2 4 4l1 1h0c1 6 5 12 8 17 1 2 2 4 4 6 0 1 0 0 1 1s1 0 1 2l1 1v-1l2 5c-1-1-1-1-1-2h-2c1 0 1 1 1 1-1 0-2-1-3-1-1-1-3-3-5-4l-54-29c-1 0-2-1-3-1-2-1-2-1-4-3 0 0-1-1-2-1-3-1-6-3-9-5l-19-11-1-1-4-2-1-2h0l-13-9h-1l-2 1h-1-1c-8-4-14-9-24-10 0 0-1-1-2-1l3-1h-2-1l8-6 9 2v-2-1h1v1c1 1 2 2 3 2v-3h0c0-3-2-5-1-8l-1-1-1-3v-1h0v-3l1-1 11 6 2-1h1c2 1 4 3 6 3l22 10 3-3-6-3-1-1c2 1 4 1 6 1 2 1 5 1 7 2h0c3-2 8-2 11-2v-2l6-1c3-3 5-4 6-8l2 1c2 0 2 0 3-1z"></path><path d="M361 714c4-1 6 1 9 3h-3l-6-3z" class="B"></path><path d="M370 717c2 1 3 2 5 2 3 0 4 2 6 4l-5-1-9-5h3z" class="I"></path><path d="M374 710c3 0 5 2 7 3 9 5 16 10 24 15 3 3 7 5 11 8l3 3h0c1 1 1 1 2 1 0-2-2-4-2-6v-1c0-1 0-2 1-3h0c1 6 5 12 8 17h-1-1l-2-1-1-1h0l-1-1h0c0-1-1-2-2-3-2-2-4-4-7-5l-39-26z" class="O"></path><path d="M330 700l31 14 6 3 9 5h-1-1c-1 0-1-1-2-1h-2l1 2h-1-1c-1-1-2-1-3-1 0 0-1-1-2-1-3-1-6-3-9-5l-19-11-1-1-4-2-1-2z" class="N"></path><path d="M361 716c3 1 5 3 9 5l1 2h-1-1c-1-1-2-1-3-1 0 0-1-1-2-1 0-2-2-3-3-5z" class="f"></path><path d="M335 704h2 1l23 12c1 2 3 3 3 5-3-1-6-3-9-5l-19-11-1-1z" class="Q"></path><defs><linearGradient id="BC" x1="415.528" y1="730.08" x2="394.567" y2="714.907" xlink:href="#B"><stop offset="0" stop-color="#656164"></stop><stop offset="1" stop-color="#787678"></stop></linearGradient></defs><path fill="url(#BC)" d="M394 716v-1l-1-2h1c2 1 4 3 6 4 1 0 2 1 2 1 1 1 1 1 2 1l1-1c2 3 4 5 7 6l2 1h1c1 1 3 2 4 4l1 1c-1 1-1 2-1 3v1c0 2 2 4 2 6-1 0-1 0-2-1h0l-3-3c-4-3-8-5-11-8 1 0 2 2 4 2-1-1-2-2-3-4h0l-8-7-4-3z"></path><path d="M406 726c2 2 9 7 10 10-4-3-8-5-11-8 1 0 2 2 4 2-1-1-2-2-3-4h0z" class="g"></path><path d="M419 739v-2c-1-1-2-1-2-3 0 0-1-1-1-2-1-1-1-2-1-4h1c1 0 1 1 2 1h1l1 1c-1 1-1 2-1 3v1c0 2 2 4 2 6-1 0-1 0-2-1h0z" class="e"></path><defs><linearGradient id="BD" x1="341.682" y1="715.466" x2="344.308" y2="669.203" xlink:href="#B"><stop offset="0" stop-color="#bdbdbd"></stop><stop offset="1" stop-color="#f0eeee"></stop></linearGradient></defs><path fill="url(#BD)" d="M307 673c0-3-2-5-1-8 3 6 7 11 12 15 4 3 9 6 14 8 7 3 13 5 19 9 5 4 12 7 17 10l3 1 2 2c1 2 6 4 8 5v3c-4 0-6-1-9-4s-7-5-11-7c-8-4-15-8-23-12l-4-2c-4-3-9-5-13-7-3-2-6-5-8-7l-6-6z"></path><path d="M307 673h0l6 6c2 2 5 5 8 7 4 2 9 4 13 7v1c-1 0-1 0-2-1h-2 0c-1-1-1-1-2-1l-1 2h-4-1l-2-2c-1 0-2-1-3-1h-1l-2 1h-1-1c-8-4-14-9-24-10 0 0-1-1-2-1l3-1h-2-1l8-6 9 2v-2-1h1v1c1 1 2 2 3 2v-3z" class="H"></path><path d="M312 689l4 2-2 1h-1c-1-2-1-1-1-3z" class="o"></path><defs><linearGradient id="BE" x1="295.973" y1="687.768" x2="300.767" y2="681.413" xlink:href="#B"><stop offset="0" stop-color="#151514"></stop><stop offset="1" stop-color="#513936"></stop></linearGradient></defs><path fill="url(#BE)" d="M290 680c8 1 16 4 22 9h0c0 2 0 1 1 3h-1c-8-4-14-9-24-10 0 0-1-1-2-1l3-1h1z"></path><path d="M307 673h0l6 6c2 2 5 5 8 7 4 2 9 4 13 7v1c-1 0-1 0-2-1h-2 0c-1-1-1-1-2-1-3 0-5-2-7-4l-9-6c-2-1-4-3-7-3v-1c-4-1-8-1-12-2-2 1-3 2-4 3l1 1h-1-2-1l8-6 9 2v-2-1h1v1c1 1 2 2 3 2v-3z" class="L"></path><path d="M307 673h0l6 6c-3 1-5-1-7-2-1 0-2 0-3-1v-2-1h1v1c1 1 2 2 3 2v-3z" class="D"></path><defs><linearGradient id="BF" x1="400.349" y1="749.069" x2="405.592" y2="730.638" xlink:href="#B"><stop offset="0" stop-color="#3d2826"></stop><stop offset="1" stop-color="#676b6e"></stop></linearGradient></defs><path fill="url(#BF)" d="M370 721h2c1 0 1 1 2 1h1 1l5 1 4 2 13 7 12 6h2c1-1 1-2 1-2 3 1 5 3 7 5 1 1 2 2 2 3h0l1 1h0l1 1 2 1h1 1c1 2 2 4 4 6 0 1 0 0 1 1s1 0 1 2l1 1v-1l2 5c-1-1-1-1-1-2h-2c1 0 1 1 1 1-1 0-2-1-3-1-1-1-3-3-5-4l-54-29c-1 0-2-1-3-1-2-1-2-1-4-3 1 0 2 0 3 1h1 1l-1-2z"></path><path d="M413 736c3 1 5 3 7 5l-1 1c-3 1-7-2-9-4h2c1-1 1-2 1-2z" class="f"></path><path d="M385 725l13 7-1 1v1 1l-8-4 1-1h1c-2-1-4-2-6-5z" class="Z"></path><defs><linearGradient id="BG" x1="370.593" y1="724.549" x2="374.907" y2="691.951" xlink:href="#B"><stop offset="0" stop-color="#9e9e9d"></stop><stop offset="1" stop-color="#c9c8c9"></stop></linearGradient></defs><path fill="url(#BG)" d="M332 688l1-1c-1-3-7-6-10-8v-1c2 1 4 2 7 2h0 0l14 7c5 2 9 4 14 7 12 6 25 13 36 22l4 3 8 7h0c1 2 2 3 3 4-2 0-3-2-4-2-8-5-15-10-24-15-2-1-4-3-7-3h-1l-2-2-3-1c-5-3-12-6-17-10-6-4-12-6-19-9z"></path><path d="M332 688l1-1c-1-3-7-6-10-8v-1c2 1 4 2 7 2h0 0l14 7 3 3c-4-1-8-3-12-3l10 5c2 1 5 2 6 4v1c-6-4-12-6-19-9z" class="D"></path><path d="M344 687c5 2 9 4 14 7 12 6 25 13 36 22l4 3h-1c-2 0-3-2-5-3s-4-3-6-4-3-2-5-3c-2-2-5-2-7-4l-27-15-3-3z" class="C"></path><defs><linearGradient id="BH" x1="375.817" y1="684.311" x2="366.256" y2="700.335" xlink:href="#B"><stop offset="0" stop-color="#585154"></stop><stop offset="1" stop-color="#7a787b"></stop></linearGradient></defs><path fill="url(#BH)" d="M384 657l2-1c1-2 1-2 3-3l2 4c1 1 2 3 2 5l2 3c0 1 1 2 1 4 1 2 2 4 2 7-2 3-4 5-8 6h0c0 2-2 2-1 4v1l-1 1c1 4 4 5 7 7l1 3c1 5 4 7 5 11 3 6 7 11 11 15-3-1-5-3-7-6l-1 1c-1 0-1 0-2-1 0 0-1-1-2-1-2-1-4-3-6-4h-1l1 2v1c-11-9-24-16-36-22-5-3-9-5-14-7l-14-7h0 0c-3 0-5-1-7-2v1c3 2 9 5 10 8l-1 1c-5-2-10-5-14-8-5-4-9-9-12-15l-1-1-1-3v-1h0v-3l1-1 11 6 2-1h1c2 1 4 3 6 3l22 10 3-3-6-3-1-1c2 1 4 1 6 1 2 1 5 1 7 2h0c3-2 8-2 11-2v-2l6-1c3-3 5-4 6-8l2 1c2 0 2 0 3-1z"></path><path d="M393 697l2-2 1 3h-1l-2-1z" class="e"></path><path d="M381 687c1-1 1-1 2-1 1-1 2-1 5-2l1 2v1l-1 1c1 4 4 5 7 7l-2 2c-2-2-5-4-7-6-1-2-3-3-5-4z" class="O"></path><path d="M305 664c0-1 1-2 1-3 2 0 4 2 6 3l7 5c2 1 4 2 5 3 4 2 6 4 9 6h-2 1v1h-1l-1 1h0 0c-3 0-5-1-7-2v1c3 2 9 5 10 8l-1 1c-5-2-10-5-14-8-5-4-9-9-12-15l-1-1z" class="g"></path><path d="M305 664c0-1 1-2 1-3 2 0 4 2 6 3l7 5c2 1 4 2 5 3 4 2 6 4 9 6h-2 1v1h-1l-1 1h0c-4-4-9-7-14-10-2-2-6-6-9-6v1c1 5 7 10 11 13v2c-5-4-9-9-12-15l-1-1z" class="Q"></path><path d="M305 656l11 6 2-1h1c2 1 4 3 6 3l22 10c4 1 8 3 12 4-1 0-2 0-3 1 1 0 3 1 4 1s2 1 3 2c10 3 17 3 27 0 0 2-2 2-1 4l-1-2c-3 1-4 1-5 2-1 0-1 0-2 1-22-2-41-10-61-20l-8-5c-2-1-4-3-6-3l-2 1h0 0v-3l1-1z" class="t"></path><path d="M339 674c4 0 7 1 9 3 1 0 2 0 3 1 2 0 3 1 5 1 1 0 3 1 4 1s2 1 3 2c-8-1-17-4-24-8z" class="Q"></path><defs><linearGradient id="BI" x1="335.468" y1="672.184" x2="336.932" y2="667.16" xlink:href="#B"><stop offset="0" stop-color="#8a8989"></stop><stop offset="1" stop-color="#a7a6a7"></stop></linearGradient></defs><path fill="url(#BI)" d="M319 661c2 1 4 3 6 3l22 10c4 1 8 3 12 4-1 0-2 0-3 1-2 0-3-1-5-1-1-1-2-1-3-1-2-2-5-3-9-3-1 0-3-1-4-2-7-3-13-6-19-10l2-1h1z"></path><path d="M384 657l2-1c1-2 1-2 3-3l2 4c1 1 2 3 2 5l2 3c0 1 1 2 1 4 1 2 2 4 2 7-2 3-4 5-8 6h0c-10 3-17 3-27 0-1-1-2-2-3-2s-3-1-4-1c1-1 2-1 3-1-4-1-8-3-12-4l3-3-6-3-1-1c2 1 4 1 6 1 2 1 5 1 7 2h0c3-2 8-2 11-2v-2l6-1c3-3 5-4 6-8l2 1c2 0 2 0 3-1z" class="S"></path><path d="M365 676c-3 0-6-1-8-2h11 1l1 1-1 1h1-5z" class="B"></path><defs><linearGradient id="BJ" x1="380.525" y1="662.031" x2="381.312" y2="676.062" xlink:href="#B"><stop offset="0" stop-color="#817f80"></stop><stop offset="1" stop-color="#a4a3a4"></stop></linearGradient></defs><path fill="url(#BJ)" d="M385 663h0v-1l1-1c1-3 2-3 5-4 1 1 2 3 2 5-1 4-1 5-5 8-5 4-12 8-18 8h0c-2-2-3-2-5-2h5-1l1-1-1-1 7-2c4-3 8-4 11-7v-1l-2-1z"></path><path d="M369 674l7-2v1h2l-5 2h-3l-1-1z" class="F"></path><defs><linearGradient id="BK" x1="371.26" y1="656.489" x2="367.549" y2="671.696" xlink:href="#B"><stop offset="0" stop-color="#6d696d"></stop><stop offset="1" stop-color="#8f8f8f"></stop></linearGradient></defs><path fill="url(#BK)" d="M384 657l2-1c1-2 1-2 3-3l2 4c-3 1-4 1-5 4l-1 1v1h0c-5 5-11 7-17 8h-1c-3 0-8 0-11-1h0c3-2 8-2 11-2v-2l6-1c3-3 5-4 6-8l2 1c2 0 2 0 3-1z"></path><path d="M367 666l6-1c-1 1-3 3-5 3h-1v-2zm26-4l2 3c0 1 1 2 1 4 1 2 2 4 2 7-2 3-4 5-8 6h0c-10 3-17 3-27 0-1-1-2-2-3-2s-3-1-4-1c1-1 2-1 3-1h11 0c6 0 13-4 18-8 4-3 4-4 5-8z" class="I"></path><path d="M389 677l4-3c1 0 0 0 0 1l1 1c-1 1-2 3-4 4h-1-2c-1 1-2 1-3 1s-3 1-4 1-1 0-1-1c1 0 3-1 5-1v-1h2v-1c1 0 2 0 2-1h1z" class="F"></path><path d="M396 669c1 2 2 4 2 7-2 3-4 5-8 6h0v-2c2-1 3-3 4-4l-1-1c0-1 1-1 0-1l-4 3v-1l6-6h0l1-1z" class="O"></path><path d="M370 678l1 1c4 0 10-2 13-3l1 1c-2 1-3 1-5 2-5 2-10 3-16 1h-4c-1 0-3-1-4-1 1-1 2-1 3-1h11 0z" class="g"></path><path d="M393 662l2 3c0 1 1 2 1 4l-1 1h0l-1-1h0c-3 3-7 5-10 7-3 1-9 3-13 3l-1-1c6 0 13-4 18-8 4-3 4-4 5-8z" class="U"></path><path d="M372 728l1-2 54 29c2 1 4 3 5 4 1 0 2 1 3 1 0 0 0-1-1-1h2c0 1 0 1 1 2l4 9h-2c-2 0-4-2-6-3s-4-2-7-3c-1 0-2 0-3 1h-3c-1 0-2 1-2 2l-6-2c-3 1-8 0-11-1h-7c-2 0-4 0-6 1v-1h1v-1h1v-1l1-1h-3c-5-1-12-1-17-2h-8l-11-1c-6-1-12 1-17-1l-17 3-1-1h0l-14 3c-3 0-7 2-10 2l-9 1c-4 1-9 1-14 2-4 0-9 1-13 0-3 0-7 0-10-1l-6-1h-1-3-1 0v1c-6-2-11-5-17-8-3 1-6-1-9-3-2-1-4-2-5-5l1-1c-1-1-3-2-5-4l-6-5-1-2s-2-1-2-2h4l4 3-4-4-1-1h1c1 1 2 1 3 2h0c3 1 5 2 7 3v-1c1 0 2 0 3 1 1 0 1-1 2-1 3 2 23 7 25 6h1-1l2-1c4 4 8 6 13 7 5 0 10 0 14-1-1 1-1 1-2 1v1h-1l-1 1h-3c-1 0 1 0-1 1h-2-1c-1 0-1 0-2-1v1h4c7-1 16-2 22-6 6-3 7-8 9-13v-1c1 1 1 2 2 4 0-2-1-4-2-5v-1h1s5 2 6 2c4 1 9 0 13-1l14-1c13-2 26-3 38-2h5l5 1 1-2h1 1z" class="c"></path><path d="M243 752c3-1 5 1 9 1h4l-3 2h0l-2 1-15-3h3c2 1 5 1 7 1h0l-3-2z" class="Q"></path><path d="M313 746h2 0c-2 1-3 2-5 2 4-1 8 0 12-1h0v1c-5 1-11 2-17 3-2 0-5 1-7 1 0 0-2-1-3-1l18-5z" class="H"></path><path d="M219 744c4 1 8 2 12 4 3 1 5 1 8 2 1 1 3 2 4 2l3 2h0c-2 0-5 0-7-1h-3c-1 0-3-1-4-2-1 0-2 0-3-1-1 0-1-1-2-1l-3-2c-1 0-2 0-3-1h0c-1 0-1-1-2-2z" class="T"></path><defs><linearGradient id="BL" x1="388.83" y1="750.288" x2="363.527" y2="754.314" xlink:href="#B"><stop offset="0" stop-color="#4c4948"></stop><stop offset="1" stop-color="#848083"></stop></linearGradient></defs><path fill="url(#BL)" d="M360 749h6l12 2h4 3l2 1h2 1 2 1v-1c1 0 2 0 3 1l1 1h1l2 2-1 2-3-1v1l-1 1h-1c-1 0-1 0-2 1h-1l-5-1 1-2c-2 0-3-1-4-1-7-2-14-2-21-2h-1l-1-4z"></path><path d="M360 749h6l-2 1-1 2-1 1h-1l-1-4z" class="J"></path><path d="M383 755l9 1c-1 1-2 1-3 2h-1-2l1-2c-2 0-3-1-4-1z" class="Q"></path><path d="M392 756l4 1-1 1h-1c-1 0-1 0-2 1h-1l-5-1h2 1c1-1 2-1 3-2z" class="f"></path><path d="M393 751c1 0 2 0 3 1l1 1h1l2 2-1 2-3-1-3-1c-5-2-10-3-15-4h4 3l2 1h2 1 2 1v-1z" class="B"></path><path d="M398 753l2 2-1 2-3-1-3-1c2 0 3 0 5-1v-1z" class="I"></path><defs><linearGradient id="BM" x1="238.019" y1="750.653" x2="242.275" y2="741.298" xlink:href="#B"><stop offset="0" stop-color="#9a9797"></stop><stop offset="1" stop-color="#c2bfbe"></stop></linearGradient></defs><path fill="url(#BM)" d="M206 739v-1c1 0 2 0 3 1 1 0 1-1 2-1 3 2 23 7 25 6h1-1l2-1c4 4 8 6 13 7 5 0 10 0 14-1-1 1-1 1-2 1v1h-1l-1 1h-3c-1 0 1 0-1 1h-2-1c-1 0-1 0-2-1v1c-4 0-6-2-9-1-1 0-3-1-4-2-3-1-5-1-8-2-4-2-8-3-12-4s-9-3-13-5z"></path><defs><linearGradient id="BN" x1="350.985" y1="763.536" x2="320.474" y2="740.683" xlink:href="#B"><stop offset="0" stop-color="#8c898a"></stop><stop offset="1" stop-color="#bab9ba"></stop></linearGradient></defs><path fill="url(#BN)" d="M309 754c4-1 8-2 11-3 14-2 27-3 40-2l1 4c-12-1-23-1-35 2h-2v1h-2-2v1c-1 0-4 0-6-1v-1h-2c-1 0-2-1-3-1z"></path><path d="M287 734c0 3 0 4-1 7-1 2-2 3-3 5l1 1c3 1 5 1 8 1h2c4 0 9-1 13-2 2 0 4-1 6 0l-18 5-18 3c-8 1-18 3-26 2l2-1h0l3-2c7-1 16-2 22-6 6-3 7-8 9-13z" class="R"></path><path d="M283 746l1 1c3 1 5 1 8 1-4 1-10 1-14 2 1-2 3-3 5-4z" class="S"></path><path d="M287 733c1 1 1 2 2 4v1c-1 1-1 1-1 2l-2 4 37-6v1h-3 0c1 1 1 1 2 1l6-1c2 0 4 0 5 1h4l-2 1h-1l-19 5h-2c-2-1-4 0-6 0-4 1-9 2-13 2h-2c-3 0-5 0-8-1l-1-1c1-2 2-3 3-5 1-3 1-4 1-7v-1z" class="n"></path><path d="M328 739c2 0 4 0 5 1h4l-2 1h-1l-19 5h-2c-2-1-4 0-6 0-4 1-9 2-13 2 1-1 2-1 5-1l-3-1c3 0 6 0 8-1l14-3 4-2 6-1z" class="S"></path><path d="M328 739c2 0 4 0 5 1h4l-2 1h-1c-2-1-3 0-4 0-4 1-8 0-12 1l4-2 6-1z" class="E"></path><defs><linearGradient id="BO" x1="363.442" y1="751.957" x2="360.661" y2="739.021" xlink:href="#B"><stop offset="0" stop-color="#afacb0"></stop><stop offset="1" stop-color="#dededc"></stop></linearGradient></defs><path fill="url(#BO)" d="M373 740l2-1c1 0 3 0 4 1v1l8 3 3 2c1 1 2 1 3 2 2 1 3 3 4 5l-1-1c-1-1-2-1-3-1-5-1-10-3-15-4-2-1-4 0-6-1-14-2-28-2-41 0-2 0-4 1-6 2h-3v-1h0c-4 1-8 0-12 1 2 0 3-1 5-2h0l19-5h1c8-1 17-2 26 0 3 0 6 2 10 2 1-1 2-1 3-2l-1-1z"></path><path d="M373 740l2-1v1c1 1 2 1 2 1l2 3-8-1c1-1 2-1 3-2l-1-1z" class="C"></path><path d="M375 739c1 0 3 0 4 1v1l8 3 3 2-11-2-2-3s-1 0-2-1v-1z" class="F"></path><path d="M335 741c8-1 17-2 26 0-3 2-11 2-14 2-4 0-8 0-11 1l-14 3h0c-4 1-8 0-12 1 2 0 3-1 5-2h0l19-5h1z" class="k"></path><path d="M323 738h1c3-1 6-3 10-4 2-1 5-1 8-1 9 0 18-1 27 1h1 2c1 0 2 0 3 1h0c1 0 2 0 2 1l1-1c2 0 4 2 6 3h1-4c2 0 6 1 7 2 0 1 1 2 1 2l-10-2c-1-1-3-1-4-1l-2 1 1 1c-1 1-2 1-3 2-4 0-7-2-10-2-9-2-18-1-26 0l2-1h-4c-1-1-3-1-5-1l-6 1c-1 0-1 0-2-1h0 3v-1z" class="d"></path><path d="M328 739c5-1 10 0 15 1h-6-4c-1-1-3-1-5-1z" class="T"></path><path d="M371 737c2 0 3 0 4 1l-1 1s-2 0-2 1h1l1 1c-1 1-2 1-3 2-4 0-7-2-10-2-9-2-18-1-26 0l2-1h6c7-1 15 0 22 0 1 0 4 0 5-1 1 0 1-1 2-1h-1v-1z" class="S"></path><path d="M342 733c9 0 18-1 27 1h1 2c1 0 2 0 3 1h0c1 0 2 0 2 1l1-1c2 0 4 2 6 3h1-4c2 0 6 1 7 2 0 1 1 2 1 2l-10-2c-1-1-3-1-4-1l-2 1h-1c0-1 2-1 2-1l1-1c-1-1-2-1-4-1-3-1-7-2-10-2-6-1-13-1-19-1v-1z" class="j"></path><path d="M369 734h1 2c1 0 2 0 3 1h0c1 0 2 0 2 1l1-1c2 0 4 2 6 3h1-4c-1 0-3-1-4-2-3 0-5 0-8-2z" class="E"></path><path d="M321 731c13-2 26-3 38-2 2 1 3 1 6 1 2 0 4 1 7 1 2 1-1 0 1 1 3 0 4 1 5 3l-1 1c0-1-1-1-2-1h0c-1-1-2-1-3-1h-2-1c-9-2-18-1-27-1-3 0-6 0-8 1-4 1-7 3-10 4h-1l-37 6 2-4c0-1 0-1 1-2v-1c0-2-1-4-2-5v-1h1s5 2 6 2c4 1 9 0 13-1l14-1z" class="k"></path><defs><linearGradient id="BP" x1="344.937" y1="762.972" x2="343.33" y2="751.925" xlink:href="#B"><stop offset="0" stop-color="#868585"></stop><stop offset="1" stop-color="#b5b4b4"></stop></linearGradient></defs><path fill="url(#BP)" d="M314 756c2 1 5 1 6 1v-1h2 2v-1h2c12-3 23-3 35-2h1c7 0 14 0 21 2 1 0 2 1 4 1l-1 2 5 1c5 1 10 2 15 0 3 0 5-1 8 0h2l-2 1c-4 1-9 4-13 4h0-7c-2 0-4 0-6 1v-1h1v-1h1v-1l1-1h-3c-5-1-12-1-17-2h-8l-11-1c-6-1-12 1-17-1l-17 3-1-1h0l-14 3c-3 0-7 2-10 2 1 0 1-1 2-1-1-1 0-1-1-1l5-1c1-1 1-1 2-1 1-1 2-2 3-2h1 0-5c-1 1-2 1-3 1l1-1h0l-1-1 12-3c1 0 2 1 3 1h2v1z"></path><path d="M312 755h2v1l-5 1-8 3c1-1 2-2 3-2h1 0-5c2-1 4-1 7-2 1 0 3-1 5-1z" class="E"></path><path d="M297 757l12-3c1 0 2 1 3 1-2 0-4 1-5 1-3 1-5 1-7 2-1 1-2 1-3 1l1-1h0l-1-1z" class="D"></path><path d="M335 757l19-1h0c2 1 6 0 8 1 0 0 1 1 1 2l-11-1c-6-1-12 1-17-1z" class="X"></path><path d="M354 756l32 2 5 1c5 1 10 2 15 0 3 0 5-1 8 0h2l-2 1c-4 1-9 4-13 4h0-7c-2 0-4 0-6 1v-1h1v-1h1v-1l1-1h-3c-5-1-12-1-17-2h-8c0-1-1-2-1-2-2-1-6 0-8-1h0z" class="b"></path><path d="M391 761c1 1 3 1 3 1 1 0 1 1 1 1 1 1 5 0 6 1h0-7c-2 0-4 0-6 1v-1h1v-1h1v-1l1-1z" class="q"></path><path d="M196 736l4 3c8 6 17 11 26 15 7 3 12 6 19 8 12 2 24 1 36-1l16-4 1 1h0l-1 1c1 0 2 0 3-1h5 0-1c-1 0-2 1-3 2-1 0-1 0-2 1l-5 1c1 0 0 0 1 1-1 0-1 1-2 1l-9 1c-4 1-9 1-14 2-4 0-9 1-13 0-3 0-7 0-10-1l-6-1h-1-3-1 0v1c-6-2-11-5-17-8-3 1-6-1-9-3-2-1-4-2-5-5l1-1c-1-1-3-2-5-4l-6-5-1-2s-2-1-2-2h4z" class="C"></path><path d="M194 738c3 1 5 4 7 6l17 11c5 3 10 6 15 7 2 1 5 2 8 3h-1-3-1 0v1c-6-2-11-5-17-8-3 1-6-1-9-3-2-1-4-2-5-5l1-1c-1-1-3-2-5-4l-6-5-1-2z" class="I"></path><path d="M205 750l1-1c4 4 9 6 13 9-3 1-6-1-9-3-2-1-4-2-5-5z" class="Y"></path><path d="M372 728l1-2 54 29c2 1 4 3 5 4 1 0 2 1 3 1 0 0 0-1-1-1h2c0 1 0 1 1 2l4 9h-2c-2 0-4-2-6-3s-4-2-7-3c-1 0-2 0-3 1h-3c-1 0-2 1-2 2l-6-2c-3 1-8 0-11-1h0c4 0 9-3 13-4l2-1h-2c-3-1-5 0-8 0-5 2-10 1-15 0h1c1-1 1-1 2-1h1l1-1v-1l3 1 1-2-2-2h-1c-1-2-2-4-4-5-1-1-2-1-3-2l-3-2-8-3v-1l10 2s-1-1-1-2c-1-1-5-2-7-2h4-1c-2-1-4-3-6-3-1-2-2-3-5-3-2-1 1 0-1-1-3 0-5-1-7-1-3 0-4 0-6-1h5l5 1 1-2h1 1z" class="t"></path><path d="M370 728h1l4 2h0l-1 1-5-1 1-2z" class="v"></path><defs><linearGradient id="BQ" x1="418.051" y1="759.15" x2="408.735" y2="746.371" xlink:href="#B"><stop offset="0" stop-color="#767576"></stop><stop offset="1" stop-color="#8c8b8d"></stop></linearGradient></defs><path fill="url(#BQ)" d="M407 747l15 9 9 6h0-2c-3-2-7-3-10-5-5-2-9-4-13-7 1-1 1-2 1-3z"></path><path d="M419 757c3 2 7 3 10 5l-1 1c4 2 8 4 11 7-2 0-4-2-6-3s-4-2-7-3c-1 0-2 0-3 1h-3c-1 0-2 1-2 2l-6-2c-3 1-8 0-11-1h0c4 0 9-3 13-4l2-1 3-2z" class="B"></path><path d="M412 765v-1c5-1 10-1 14 0-1 0-2 0-3 1h-3c-1 0-2 1-2 2l-6-2z" class="o"></path><path d="M419 757c3 2 7 3 10 5l-1 1c-5-2-8-3-14-3l2-1 3-2z" class="h"></path><defs><linearGradient id="BR" x1="405.507" y1="740.07" x2="377.56" y2="741.926" xlink:href="#B"><stop offset="0" stop-color="#8e8e8f"></stop><stop offset="1" stop-color="#c8c7c8"></stop></linearGradient></defs><path fill="url(#BR)" d="M359 729h5l5 1 5 1c12 4 22 10 33 16 0 1 0 2-1 3l-17-8s-1-1-1-2c-1-1-5-2-7-2h4-1c-2-1-4-3-6-3-1-2-2-3-5-3-2-1 1 0-1-1-3 0-5-1-7-1-3 0-4 0-6-1z"></path><path d="M379 740l10 2 17 8c4 3 8 5 13 7l-3 2h-2c-3-1-5 0-8 0-5 2-10 1-15 0h1c1-1 1-1 2-1h1l1-1v-1l3 1 1-2-2-2h-1c-1-2-2-4-4-5-1-1-2-1-3-2l-3-2-8-3v-1z" class="l"></path><path d="M393 748c3 1 6 2 8 4l2 4v1h-4l1-2-2-2h-1c-1-2-2-4-4-5z" class="J"></path><path d="M401 752c2 1 2 1 3 3v2h1v1h-1 0c1 1 1 1 2 1-5 2-10 1-15 0h1c1-1 1-1 2-1h1l1-1v-1l3 1h4v-1l-2-4z" class="U"></path><path d="M379 740l10 2 17 8c4 3 8 5 13 7l-3 2h-2v-1c-2-2-5-2-7-4-3-1-5-3-7-5-4-3-9-5-13-5l-8-3v-1z" class="r"></path><path d="M727 103h0v-2c0-4 3-8 5-11 3-2 7-4 11-6-5 4-12 8-12 15 0 6 3 11 7 15 1 1 2 3 3 5 0 1 0 6-1 7-1 3-4 5-5 7-4 9-1 22 6 29 5 6 12 8 18 11 4 1 8 3 11 3 2 0 4-1 5-3 2-1 3-5 3-7 3-8 7-17 17-18h7c-1 1 0 1-1 1-5 0-9 3-13 6-3 4-4 9-5 15 0 2 0 5-1 7-2 5-10 12-9 18 0 3 2 5 4 7l5 2v1c-1 1-2 2-2 3h0 1v1h1c-2 6-2 12-2 18h0v3c-1 0-1 1-1 1h-8-2-2c-2 0-3 0-4-1h-5v1h-2l-1-1c-3 1-5 0-7 0l-1-1c-1 0-2-1-2-2s-1-2-2-3c0 1 0 2 1 3v1c-1-1-1 0-2-1-1-2-2-4-4-4l-1-1-1-2c-1-3-4-6-6-9v-1l-2-2-1-2c-1-1-2-2-2-3l-3 1c-1-2-2-3-3-4l-10-10c-1 0-3-2-4-3-2 0-6-4-8-5-3-2-6-4-10-5 0 0-2-1-3-2-3-1-5-1-8-2h-1c-2-1-4-1-6-2-1 0-2-1-2-1h-4c-3 0-7 0-9-2-3-2-12 0-15 0-2 0-6 0-9 1-3-1-6-1-10-1l-1-1c-2 0-4-1-6-1l1-7 1-1c1 0 3 1 5 2 3 2 9 2 13 1 2 0 4 0 6-1h2 3l1-2 12 1h-2v1h3l4 1c2-1 4-1 6-1 4 1 7 1 10 0h0l9-1h3l10-3v1c3 0 7-3 9-5l2-2h1 1l1-3 4-5c1-2 2-4 2-7v-1c1-2 1-4 1-5l-2-22-1-3-2-2c-3-1-6-2-9-1h-2 0c1-2 2-2 4-3 6-2 11 3 16 5h0 2z" class="c"></path><path d="M678 160l9-1 1 2c-3 0-6 0-10-1z" class="n"></path><path d="M708 161l7-1c1 1 2 1 4 1-2 1-3 1-4 1h0-4-3v-1z" class="R"></path><path d="M728 131l2 2c0 2 0 2-1 3l-1-1c-2 2-1 6-2 9v-8c1-2 2-4 2-5z" class="T"></path><path d="M750 175h1c0-1 0-1-1-2l1-1 4 3c0 1 1 2 1 3l-7-2 1-1z" class="E"></path><path d="M755 175l5 2 2 3h0l-6-2c0-1-1-2-1-3z" class="R"></path><path d="M709 152l2-2h1v2l1 1-1 1c2 0 4-1 5 0h1c-3 1-5 2-8 2 0-1-1-3-1-4z" class="f"></path><path d="M709 152c0 1 1 3 1 4l-11 3c0-1 1-1 1-2 3 0 7-3 9-5z" class="O"></path><path d="M715 160c4-1 7-3 11-6-1 2-2 3-1 5l-1 1c-2 1-3 1-5 1s-3 0-4-1z" class="F"></path><path d="M700 156v1c0 1-1 1-1 2-2 1-8 1-11 2l-1-2h3l10-3z" class="E"></path><path d="M764 179h6c1 1 1 1 2 1l2-1h1c1-1 0-1 1-2l1-1c1-1 1-2 1-3l1-1h0v-1l1-1-1-2h1v-1-1h1l-1-1 1-1h0c1 3 1 5 0 7l-1 1c0 1-1 1-1 2-1 4-6 5-7 9h-3l-5-4z" class="H"></path><path d="M729 111c1 1 2 2 2 4s-1 4 0 7l1-4c1 1 1 2 2 3-2 2-2 4-2 7 0 1-1 2-1 3l-1 2-2-2 1-8c1-5 2-8 0-12z" class="F"></path><path d="M732 118c1 1 1 2 2 3-2 2-2 4-2 7 0 1-1 2-1 3l-1-1v-1c1-2 1-5 1-7l1-4z" class="I"></path><path d="M732 118l1-5c2 3 6 6 5 11l-1 1c-1 1-3 2-5 3 0-3 0-5 2-7-1-1-1-2-2-3z" class="B"></path><path d="M734 121l1-1h1c1 1 1 3 1 4v1c-1 1-3 2-5 3 0-3 0-5 2-7z" class="j"></path><path d="M726 154c2-2 4-4 6-7v-1c1 3-2 14-4 17-1 0-1 1-2 1h-1l1-2c-1 0-1-1-2-2l1-1c-1-2 0-3 1-5z" class="J"></path><path d="M725 159v-1c2 0 3-2 5-3-1 3-1 6-4 7h0c-1 0-1-1-2-2l1-1z" class="R"></path><path d="M718 104h3v1c1 0 2 1 3 2 1 0 2 2 3 3 0 1 1 1 2 1h0c2 4 1 7 0 12-1 0-1 0-2-1-2-1-3-8-5-11 0-1-1-2-3-4l-1-3z" class="AA"></path><path d="M719 107c2 2 3 3 3 4 2 3 3 10 5 11 1 1 1 1 2 1l-1 8c0 1-1 3-2 5h-1l1-3c-2 0-4 0-6 1 1-2 1-4 1-5l-2-22z" class="n"></path><path d="M721 129c1-1 2-2 2-3 0-3-1-6-1-9v1c1 1 0 1 1 2h0c0 1 0 1 1 2 0 2 1 4 1 6 1 2 1 3 1 5-2 0-4 0-6 1 1-2 1-4 1-5z" class="d"></path><path d="M720 134c2-1 4-1 6-1l-1 3h1v8c0 3-1 5-4 7-1 1-3 3-4 3h-1c-1-1-3 0-5 0l1-1-1-1v-2h1l1-3 4-5c1-2 2-4 2-7v-1z" class="J"></path><path d="M724 139h1c0 2-1 3-1 5h-1c-1 4-5 6-8 8l-1-1-2 1v-2h1 1c1-1 2-1 3-1 2-1 5-5 6-7 0-1 1-2 1-3z" class="C"></path><path d="M718 142c0 1 0 1 2 2 1-1 2-3 4-5h0c0 1-1 2-1 3-1 2-4 6-6 7-1 0-2 0-3 1h-1l1-3 4-5z" class="L"></path><path d="M720 134c2-1 4-1 6-1l-1 3h0v3h-1 0c-2 2-3 4-4 5-2-1-2-1-2-2 1-2 2-4 2-7v-1z" class="H"></path><path d="M720 134c2-1 4-1 6-1l-1 3h0l-2 2c-2 0-2-2-3-3v-1z" class="c"></path><path d="M769 183h3c1-4 6-5 7-9 0-1 1-1 1-2l1-1c-1 2-1 3-2 5v1c-2 3-6 5-7 9-1 0-1 1-1 1-2 3-2 6-1 9l1 4c2 1 3 2 5 3l1-1 5 2v1c-1 1-2 2-2 3h0 1v1h1c-2 6-2 12-2 18h0v3c-1 0-1 1-1 1h-8-2-2c-2 0-3 0-4-1h-5v1h-2l-1-1-1-1c-1-1-2-3-2-4h0l-5-10v-2l3 1 1 1-2 2h3c2 0 4-2 5-3v-1h1c1-1 2-6 3-8h0c1-4 3-7 5-11 1-2 2-4 2-6 1-1 1-2 2-2 0-2 0-2-1-3z" class="S"></path><path d="M764 211l1 1c0 1 0 4-1 5 1 3 1 4 3 6v1l-2 2c0 2-1 3-2 4h-5l-1-1 1-2c1-1 2-1 3-3h0c2-4 2-9 3-13z" class="q"></path><path d="M761 224l1 1c0 1-1 2-2 3h1c2 0 2-1 3-3l1 1c0 2-1 3-2 4h-5l-1-1 1-2c1-1 2-1 3-3h0z" class="X"></path><path d="M750 214l1 1-2 2h3c2 0 4-2 5-3v-1h1v1c-1 2-4 4-6 6 0 1 1 2 1 3 1 0 1 1 2 1h1v-1c1 1 0 1 2 2h0l3-1c-1 2-2 2-3 3l-1 2 1 1v1h-2l-1-1-1-1c-1-1-2-3-2-4h0l-5-10v-2l3 1z" class="Y"></path><path d="M752 225l1-1 2 2v1 1l-1 1c-1-1-2-3-2-4z" class="K"></path><path d="M755 228l3-1-1 2 1 1v1h-2l-1-1-1-1 1-1z" class="h"></path><path d="M769 199c0-1 0-2 1-3l1 4c0 2 0 3 1 5-1 3-2 5-2 7l-1 6h-1c0 1 0 3-1 3v2c-2-2-2-3-3-6 1-1 1-4 1-5l-1-1c0-3 2-6 3-9l2-3z" class="V"></path><path d="M767 202v4c-1 2-1 2-1 4 1 3 0 7 1 10v1 2c-2-2-2-3-3-6 1-1 1-4 1-5l-1-1c0-3 2-6 3-9z" class="o"></path><path d="M769 199c0-1 0-2 1-3l1 4c0 2 0 3 1 5-1 3-2 5-2 7l-1 6h-1v-6c1-2 1-5 2-8 1-1-1-4-1-5z" class="y"></path><path d="M771 200c2 1 3 2 5 3l1-1 5 2v1c-1 1-2 2-2 3h0 1v1h1c-2 6-2 12-2 18h0v3c-1 0-1 1-1 1h-8-2-2c-2 0-3 0-4-1 1-1 2-2 2-4l2-2v-1-2c1 0 1-2 1-3h1l1-6c0-2 1-4 2-7-1-2-1-3-1-5z" class="H"></path><path d="M771 200c2 1 3 2 5 3h0v1c-3 3-3 8-4 12-1 3 0 7 0 10h-1c-2-3 0-11 1-15 0-2 1-4 0-6s-1-3-1-5z" class="f"></path><path d="M772 205c1 2 0 4 0 6-1 4-3 12-1 15 0 2 0 3-1 4l1 1h-2-2c-2 0-3 0-4-1 1-1 2-2 2-4l2-2v-1-2c1 0 1-2 1-3h1l1-6c0-2 1-4 2-7z" class="W"></path><path d="M769 224c0 1 0 4 1 6h0l1 1h-2-2v-2c0-2 1-4 2-5z" class="J"></path><path d="M768 218h1v6c-1 1-2 3-2 5v2c-2 0-3 0-4-1 1-1 2-2 2-4l2-2v-1-2c1 0 1-2 1-3z" class="i"></path><path d="M767 224v5 2c-2 0-3 0-4-1 1-1 2-2 2-4l2-2z" class="f"></path><path d="M780 208h0 1v1h1c-2 6-2 12-2 18h0v3c-1 0-1 1-1 1h-8l-1-1c1-1 1-2 1-4h1v2l6 3v-1c-2-7-2-15 2-22z" class="K"></path><path d="M726 166l5 4 1 1 1-1 2 1h0v-1c2 0 4 1 6 2l9 3-1 1 7 2 6 2h0l-2-3 4 2 5 4c1 1 1 1 1 3-1 0-1 1-2 2 0 2-1 4-2 6-2 4-4 7-5 11h0c-1 2-2 7-3 8h-1v1c-1 1-3 3-5 3h-3l2-2-1-1h0c0-1 0 0 1-1h0v-1l1-1-2-2c-1 0-2-1-3-1l-5-5 2-2v-7c-1-5-2-8-5-12l-5-7c-3-3-6-6-9-8l1-1z" class="I"></path><path d="M750 214l3 1c2-1 2-3 4-4s2-4 3-5l1-1c-1 2-2 7-3 8h-1v1c-1 1-3 3-5 3h-3l2-2-1-1h0z" class="l"></path><path d="M734 175l1-1 6 6c5 3 11 10 13 15 1 2 2 4 2 6s-1 4-1 6v3l-2 2h-1c2-2 2-2 3-5h-1v1c-1 1-2 2-2 3l-2-2h2c1-1 1-3 2-4 0-4 0-8-2-12h-2-1c-1-3-3-4-5-5l1 5-1 1c-1-5-2-8-5-12l-5-7z" class="Q"></path><path d="M739 182l1-1c1 0 1 1 2 1 4 3 8 8 10 11h-2-1c-1-3-3-4-5-5l1 5-1 1c-1-5-2-8-5-12z" class="B"></path><path d="M749 176l7 2 6 2h0l-2-3 4 2 5 4c1 1 1 1 1 3-1 0-1 1-2 2 0 2-1 4-2 6-2 4-4 7-5 11l-1-1 1-4c1-1 1-2 1-3s0-2 1-3v-1c-1-2 0-5-1-7 0-2-1-4-3-5v-1l-9-4h-1z" class="N"></path><path d="M766 183c2 2 2 3 2 5s-1 4-2 6c-1-1-1-3-1-4s1-2 1-4v-3z" class="l"></path><path d="M760 177l4 2 5 4c1 1 1 1 1 3-1 0-1 1-2 2 0-2 0-3-2-5-1-1-3-2-4-3h0l-2-3z" class="B"></path><path d="M745 193l-1-5c2 1 4 2 5 5h1 2c2 4 2 8 2 12-1 1-1 3-2 4h-2c-1 0-2-1-3-1l-5-5 2-2v-7l1-1z" class="E"></path><path d="M745 193v1c0 3-1 8 0 10 2 1 3 2 5 2h1v1h-2-1c1 0 2 1 4 1h0c0-1 1-2 2-3-1 1-1 3-2 4h-2c-1 0-2-1-3-1l-5-5 2-2v-7l1-1z" class="D"></path><defs><linearGradient id="BS" x1="678.096" y1="202.737" x2="696.477" y2="172.167" xlink:href="#B"><stop offset="0" stop-color="#d4d3d3"></stop><stop offset="1" stop-color="#fbfaf9"></stop></linearGradient></defs><path fill="url(#BS)" d="M614 159l1-1c1 0 3 1 5 2 3 2 9 2 13 1 2 0 4 0 6-1h2 3l1-2 12 1h-2v1h3l4 1c2-1 4-1 6-1 4 1 7 1 10 0l-1 1 2 3 6 1c2 0 5 0 8-1l4-2c2-1 4-1 5-1 2 1 4 0 6 0v1h3 4 0c1 0 2 0 4-1 2 0 3 0 5-1 1 1 1 2 2 2l-1 2h1v2l-1 1c3 2 6 5 9 8l5 7c3 4 4 7 5 12v7l-2 2 5 5c1 0 2 1 3 1l2 2-1 1v1h0c-1 1-1 0-1 1h0l-3-1v2l5 10h0c0 1 1 3 2 4l1 1c-3 1-5 0-7 0l-1-1c-1 0-2-1-2-2s-1-2-2-3c0 1 0 2 1 3v1c-1-1-1 0-2-1-1-2-2-4-4-4l-1-1-1-2c-1-3-4-6-6-9v-1l-2-2-1-2c-1-1-2-2-2-3l-3 1c-1-2-2-3-3-4l-10-10c-1 0-3-2-4-3-2 0-6-4-8-5-3-2-6-4-10-5 0 0-2-1-3-2-3-1-5-1-8-2h-1c-2-1-4-1-6-2-1 0-2-1-2-1h-4c-3 0-7 0-9-2-3-2-12 0-15 0-2 0-6 0-9 1-3-1-6-1-10-1l-1-1c-2 0-4-1-6-1l1-7z"></path><path d="M712 180c3 0 6 3 9 4l3 2 1 1-2 1-11-8z" class="a"></path><path d="M725 187c3 2 5 4 7 7 1 2 4 3 3 5l-12-11 2-1z" class="b"></path><path d="M645 158l12 1h-2v1h3l-1 1-16-1h3l1-2z" class="O"></path><path d="M639 168v-1c1 0 5 0 6-1 2 0 4 0 7 1h1 2c1 0 2 1 3 1h3 1l1 2c-3 0-7 0-9-2-3-2-12 0-15 0zm30 3c1-1 4 0 6 0s4 1 6 2c4 1 7 2 11 4h0 0-5s-2-1-3-2c-3-1-5-1-8-2h-1c-2-1-4-1-6-2z" class="H"></path><path d="M687 177h5 0c6 3 14 6 19 11-2 0-4-1-6-1s-6-4-8-5c-3-2-6-4-10-5z" class="P"></path><path d="M741 206l6 9 5 10h0c0 1 1 3 2 4l1 1c-3 1-5 0-7 0l-1-1h0c2 0 3-1 4 0h1c0-1 0-2-1-2v-1c-1-1-1-1-1-2l-1-1c0-3-2-6-3-8h-1c1 2 2 4 2 7 1 1 2 2 2 4h0c-2-3-3-7-4-10-1-2-4-5-4-7v-3z" class="T"></path><path d="M662 161c2-1 4-1 6-1 4 1 7 1 10 0l-1 1 2 3h-1s1 1 1 2l-22-5 1-1 4 1z" class="K"></path><path d="M662 161c2-1 4-1 6-1 4 1 7 1 10 0l-1 1 2 3h-1c-6-2-10-3-16-3z" class="L"></path><defs><linearGradient id="BT" x1="714.578" y1="198.567" x2="714.439" y2="190.432" xlink:href="#B"><stop offset="0" stop-color="#837b80"></stop><stop offset="1" stop-color="#a0a19d"></stop></linearGradient></defs><path fill="url(#BT)" d="M705 187c2 0 4 1 6 1 4 2 8 6 11 9 1 2 2 4 2 5l1 1-3 1c-1-2-2-3-3-4l-10-10c-1 0-3-2-4-3z"></path><path d="M705 187c2 0 4 1 6 1 4 2 8 6 11 9 1 2 2 4 2 5-3-3-5-6-9-9-2-2-3-3-6-3-1 0-3-2-4-3z" class="C"></path><path d="M722 197c7 6 16 15 19 23 1 2 2 3 2 4s0 2 1 3v1c-1-1-1 0-2-1-1-2-2-4-4-4l-1-1-1-2c-1-3-4-6-6-9v-1l-2-2-1-2c-1-1-2-2-2-3l-1-1c0-1-1-3-2-5z" class="D"></path><path d="M730 210c5 1 8 12 12 15 0-2-1-3-1-5 1 2 2 3 2 4s0 2 1 3v1c-1-1-1 0-2-1-1-2-2-4-4-4l-1-1-1-2c-1-3-4-6-6-9v-1z" class="C"></path><path d="M697 162c2-1 4-1 5-1 2 1 4 0 6 0v1h3c-1 0-1 1-2 1 2 1 3 2 4 3l-1 2h1c0 1 1 2 2 2v2 1h-1l1 1c1 1 1 1 3 1 2 2 6 5 6 9v2l-3-2c-3-1-6-4-9-4-11-6-21-10-33-14 0-1-1-2-1-2h1l6 1c2 0 5 0 8-1l4-2z" class="V"></path><defs><linearGradient id="BU" x1="707.877" y1="179.982" x2="716.834" y2="173.362" xlink:href="#B"><stop offset="0" stop-color="#9f9c9d"></stop><stop offset="1" stop-color="#cbc9c9"></stop></linearGradient></defs><path fill="url(#BU)" d="M701 172v-2c1 0 2-1 3 0h1c2 1 4 1 5 2l4 1 1 1c1 1 1 1 3 1 2 2 6 5 6 9v2l-3-2c-5-5-13-9-20-12z"></path><path d="M697 162c2-1 4-1 5-1 2 1 4 0 6 0v1h3c-1 0-1 1-2 1 2 1 3 2 4 3l-1 2h1c0 1 1 2 2 2v2 1h-1l-4-1c-1-1-3-1-5-2h-1c-1-1-2 0-3 0v2l-16-7c2 0 5 0 8-1l4-2z" class="W"></path><path d="M697 162c3 1 7 1 9 2 1 3 2 3 3 4l1 1c-1 0-5-3-7-4h0-2c-1-1-4-1-5 0h0c-1 0-2-1-3-1l4-2z" class="B"></path><path d="M697 162c2-1 4-1 5-1 2 1 4 0 6 0v1h3c-1 0-1 1-2 1 2 1 3 2 4 3l-1 2h1c0 1 1 2 2 2v2l-5-3-1-1c-1-1-2-1-3-4-2-1-6-1-9-2z" class="E"></path><path d="M706 164c2 2 4 3 6 4h1c0 1 1 2 2 2v2l-5-3-1-1c-1-1-2-1-3-4z" class="R"></path><path d="M724 160c1 1 1 2 2 2l-1 2h1v2l-1 1c3 2 6 5 9 8l5 7c3 4 4 7 5 12v7l-2 2 5 5c1 0 2 1 3 1l2 2-1 1v1h0c-1 1-1 0-1 1h0l-3-1v2l-6-9-6-7c1-2-2-3-3-5-2-3-4-5-7-7l-1-1v-2c0-4-4-7-6-9-2 0-2 0-3-1l-1-1h1v-1-2c-1 0-2-1-2-2h-1l1-2c-1-1-2-2-4-3 1 0 1-1 2-1h4 0c1 0 2 0 4-1 2 0 3 0 5-1z" class="c"></path><path d="M722 163l3 1h1v2l-1 1-2-2-1-2z" class="L"></path><path d="M716 167l2-1h0c-1-1-2-1-2-2h0l1 1 4 2c-2 1-3 0-5 0z" class="T"></path><path d="M724 160c1 1 1 2 2 2l-1 2-3-1h0c-2 0-5 0-7-1h0c1 0 2 0 4-1 2 0 3 0 5-1z" class="P"></path><path d="M709 163c2 0 4 1 6 3 0 0 1 0 1 1 2 3 5 4 7 8l-4-3 1 3c-2-1-3-2-5-2v-1-2c-1 0-2-1-2-2h-1l1-2c-1-1-2-2-4-3z" class="H"></path><path d="M713 168c2 1 4 3 6 4l1 3c-2-1-3-2-5-2v-1-2c-1 0-2-1-2-2z" class="j"></path><path d="M729 181c4 4 6 9 8 13 1 2 2 3 3 5 1 1 2 2 3 2h1l-2 2c-5-6-10-14-15-20l2-2z" class="E"></path><path d="M715 173c2 0 3 1 5 2l-1-3 4 3 6 6-2 2v-1c0 2 4 8 5 9v3c-2-3-4-5-7-7l-1-1v-2c0-4-4-7-6-9-2 0-2 0-3-1l-1-1h1z" class="B"></path><path d="M719 172l4 3 6 6-2 2v-1c-1-3-5-5-7-7l-1-3z" class="L"></path><path d="M727 182v1c5 6 10 14 15 20l5 5c1 0 2 1 3 1l2 2-1 1v1h0c-1 1-1 0-1 1h0l-3-1v2l-6-9-6-7c1-2-2-3-3-5v-3c-1-1-5-7-5-9z" class="K"></path><path d="M747 208c1 0 2 1 3 1l2 2-1 1v1h0c-1 1-1 0-1 1h0l-3-1c-1-1-2-3-2-4 1-1 1-1 2-1h0z" class="Z"></path><path d="M272 408c1-1 1-1 2-1l1 2h0v-2l1-1v1 1 2c1 1 0 3 1 4 0 5 0 9-1 14h1v6c-3 5-5 11-7 17-4 15-5 32-1 48l1 3 2 8c1 3 2 7 2 11 1 2 1 3 2 4l-3 4c-1 1-2 2-3 4 2 0 2 1 3 2 1 2 1 4 1 6-1 2-1 4-2 6l-1-1c-2 0-3 1-4 2v-2h-2v-1l-2 2c-1 2-2 4-2 7s-1 6 0 10l-1 1c-1 2 0 5 0 8 1 4 2 10 4 14 0 3 1 7 0 10 0 1 3 6 3 7v3c1 1 0 1 1 2-1 1-1 2-1 4 0 1 1 2 2 3 0 1 1 3 2 4h-4c-1 2-1 4-1 6l2 6c2 2 3 4 5 7 0 2 0 4 1 5v1c-3 0-4 0-6 2 0 1-1 1-1 1l-2 4-2 1-1 13c-1 13-5 28-9 40-1-1-1-1-1-2l1-1v-8h0l-1-1h-1l2-15v-9l-1-24c-1-12-4-24-7-35l-3-15c-1 0-2-10-3-12v-2c0-4-1-8 1-11 0-2 0-3 1-4 0-12-1-24-3-36l-1-8c-1-3-1-6-1-8s-1-4-1-6c-1-2-1-4-1-5v-3s0 2 1 2l1 1 1 2h0c1-2 1-4 1-6 1-1 0-3 0-4l1-1v-3c1-1 1-2 1-3v-1l1-1c1-9 3-17 6-25v-1c1-2 1-3 2-5l1-2 3-1c1-2 1-2 4-2 1-2 2-2 3-4l1 2c1 2 1 3 2 5 3-7 5-16 5-23l-1-1c-3 0-5 0-7-1-2 1-4 3-6 5h-1c-1 1-3 3-5 4l-1 1h-1c-1-1-1-1-2-1-2 1-3 3-4 5-1 0-2 1-3 2v1-6l2-8 2-2-2-1h-3l20-16 7-4 4-1h1 4z" class="d"></path><path d="M245 588l4 3 3-1v1c0 1-1 2-1 3-2-1-3-1-5-3l-1-3zm7 16c1 0 2 5 2 6h1v-3c1 3 1 7 1 10 1 1 1 2 0 3-1-1-1-3-1-4l-3-12z" class="T"></path><path d="M253 670c1-2 1-4 1-5v-3-1-1-5-1c1-1 0-4 0-5v-2-3c0-1-1-2-1-2 1-2 1-2 1-3l-1-1v-8h1l1 15c2 12 2 25 0 36 0 5-1 9-2 14h0l-1-1h-1l2-15v-9z" class="E"></path><path d="M252 694c2-8 3-18 3-27v-9c0-4 1-8 0-12v-1h0c2 12 2 25 0 36 0 5-1 9-2 14h0l-1-1z" class="W"></path><path d="M261 468v-4c2 2 2 4 3 6-1 3-1 7-1 10v5c0 1 1 1 1 2v8c0 2 1 6 1 7-1 2-2 4-2 6-3 7-4 13-5 20l-1 1h0 0l-1 1v-2-3c1-2 1-2 1-3 1-2 0-4 1-6v-1-3-1c1-1 1-2 1-3v-3c0 1 0 2-1 3v1h0c0-5 2-11 2-16 1-9 0-17 1-25z" class="H"></path><path d="M261 468v-4c2 2 2 4 3 6-1 3-1 7-1 10h0l-1-7c-1-2-1-3-1-5z" class="B"></path><path d="M263 480h0v5c0 1 1 1 1 2v8c0 2 1 6 1 7-1 2-2 4-2 6-3 7-4 13-5 20l-1 1h0c3-16 5-33 6-49z" class="O"></path><path d="M253 453c1-2 1-2 4-2l-10 33h0v-1c1-4 1-8 2-11v-2h0v-1-1h1v-2-2c-1 3-1 7-3 9l-1 4c0 2-1 4-2 6l-3 15c0 4 0 8-1 12 0 1 1 2 0 4l-1 4c2 2 2 3 2 5l1 5c-2 0-2 1-4 1v2l-1-8c-1-3-1-6-1-8s-1-4-1-6c-1-2-1-4-1-5v-3s0 2 1 2l1 1 1 2h0c1-2 1-4 1-6 1-1 0-3 0-4l1-1v-3c1-1 1-2 1-3v-1l1-1c1-9 3-17 6-25v-1c1-2 1-3 2-5l1-2 3-1z" class="H"></path><path d="M250 454l3-1-6 9v-1c1-2 1-3 2-5l1-2z" class="R"></path><path d="M239 518c-1-1-1-2-2-3 1-1 1-2 1-2-1-3 3-22 3-26l1 4v-2-1-3l2-4h0v-2c1-1 1-1 1-2 1-1 0-1 0-2s1-2 2-2l-1 4c0 2-1 4-2 6l-3 15c0 4 0 8-1 12 0 1 1 2 0 4l-1 4z" class="T"></path><defs><linearGradient id="BV" x1="241.946" y1="596.42" x2="252.554" y2="594.58" xlink:href="#B"><stop offset="0" stop-color="#c8c7c7"></stop><stop offset="1" stop-color="#f0efee"></stop></linearGradient></defs><path fill="url(#BV)" d="M238 531v-2c2 0 2-1 4-1l1 30v16c1 5 2 9 2 14l1 3c2 2 3 2 5 3-2 1-3 2-4 3-1 2 1 5 2 8s2 6 2 9c1 1 1 1 1 2 1 4 2 10 2 14h-1v8l1 1c0 1 0 1-1 3 0 0 1 1 1 2v3 2c0 1 1 4 0 5v1 5 1 1 3c0 1 0 3-1 5l-1-24c-1-12-4-24-7-35l-3-15c-1 0-2-10-3-12v-2c0-4-1-8 1-11 0-2 0-3 1-4 0-12-1-24-3-36z"></path><path d="M246 591c2 2 3 2 5 3-2 1-3 2-4 3v-3c-1-1-1-2-1-3z" class="H"></path><path d="M241 567c0 5-1 11 0 17 0 2 2 5 2 8h0l-1-1c0 1 0 2 1 4v1h-1c-1 0-2-10-3-12v-2c0-4-1-8 1-11 0-2 0-3 1-4z" class="E"></path><path d="M239 582c0-4-1-8 1-11 0 4 0 9-1 13v-2z" class="D"></path><path d="M270 527c2-2 3-4 4-6 1 2 1 3 2 4l-3 4c-1 1-2 2-3 4 2 0 2 1 3 2 1 2 1 4 1 6-1 2-1 4-2 6l-1-1c-2 0-3 1-4 2v-2h-2v-1l-2 2c-1 2-2 4-2 7s-1 6 0 10l-1 1c-1 2 0 5 0 8 1 4 2 10 4 14 0 3 1 7 0 10 0 1 3 6 3 7v3c1 1 0 1 1 2-1 1-1 2-1 4 0 1 1 2 2 3 0 1 1 3 2 4h-4c-1 2-1 4-1 6l2 6c2 2 3 4 5 7 0 2 0 4 1 5v1c-3 0-4 0-6 2 0 1-1 1-1 1l-2 4-2 1v-24c-1-1-1-3-1-4v-1-3-5c0-1-1-2-1-3v-1h0l-1 2c-1 3 0 6-1 9v-9c-1-2-1-5-1-7-1-3-1-6-2-8v-8c0-3-1-4 0-7h0c-1-2 0-4-1-5-1-3-1-6-1-9-1-1-1-2-1-3v-1h0c0-2-1-4-1-6v-18c1-1 1-3 1-4v-2h1v4c-1 3-1 9 0 12v-5-3-5c1-1 1-3 1-4v-1c0-1 0-3 1-4l1-1h0 0l1-1c1 0 2 0 3 1h-2c2 2 6 1 9 1l2-3z" class="L"></path><path d="M255 559v-3-13c2 1 2 1 4 1-2 4-2 10-2 15h-2z" class="f"></path><path d="M257 577l2 10c0-1 0-2 1-4l1 7-1 7c1 4 2 8 2 12l-2 2c0-5-1-11-2-15-2-5-1-14-1-19z" class="R"></path><path d="M259 587c0-1 0-2 1-4l1 7-1 7c0-3-1-6-1-10z" class="O"></path><defs><linearGradient id="BW" x1="257.226" y1="543.829" x2="260.987" y2="529.825" xlink:href="#B"><stop offset="0" stop-color="#6f6d6e"></stop><stop offset="1" stop-color="#9f9c9b"></stop></linearGradient></defs><path fill="url(#BW)" d="M257 529h0l1-1c1 0 2 0 3 1h-2c2 2 6 1 9 1h-1c0 1-1 2-2 3-2 2-3 4-4 7-1 1-2 2-2 4-2 0-2 0-4-1l2-14h0z"></path><path d="M260 597l1-7 2 7h1c0 1 3 6 3 7v3c1 1 0 1 1 2-1 1-1 2-1 4 0 1 1 2 2 3 0 1 1 3 2 4h-4c-1 2-1 4-1 6l2 6c2 2 3 4 5 7 0 2 0 4 1 5v1c-3 0-4 0-6 2 0 1-1 1-1 1l-2 4-2 1v-24l-1-20c0-4-1-8-2-12z" class="V"></path><defs><linearGradient id="BX" x1="261.82" y1="603.583" x2="268.488" y2="617.009" xlink:href="#B"><stop offset="0" stop-color="#777676"></stop><stop offset="1" stop-color="#918e8f"></stop></linearGradient></defs><path fill="url(#BX)" d="M263 597h1c0 1 3 6 3 7v3c1 1 0 1 1 2-1 1-1 2-1 4 0 1 1 2 2 3 0 1 1 3 2 4h-4c-1 2-1 4-1 6l-1-3-2-24v-2z"></path><path d="M265 623l1 3 2 6c2 2 3 4 5 7 0 2 0 4 1 5v1c-3 0-4 0-6 2 0 1-1 1-1 1l-2 4c-1-2-1-6-1-8v-15c0-2 0-4 1-6z" class="F"></path><path d="M265 623l1 3 2 6c0 3 1 9 3 10l1 1h-1c-1 1-2 1-2 2h-1v-1c1-3-1-7-2-10-1-2 0-3-1-4l-1-1c0-2 0-4 1-6z" class="Z"></path><path d="M270 527c2-2 3-4 4-6 1 2 1 3 2 4l-3 4c-1 1-2 2-3 4 2 0 2 1 3 2 1 2 1 4 1 6-1 2-1 4-2 6l-1-1c-2 0-3 1-4 2v-2h-2v-1l-2 2c-1 2-2 4-2 7s-1 6 0 10l-1 1c-1 2 0 5 0 8 1 4 2 10 4 14 0 3 1 7 0 10h-1l-2-7-1-7c-1 2-1 3-1 4l-2-10-2-18h2c0-5 0-11 2-15 0-2 1-3 2-4 1-3 2-5 4-7 1-1 2-2 2-3h1l2-3z" class="P"></path><path d="M265 545c1-2 1-3 3-4v3l-1 2h-2v-1z" class="k"></path><path d="M261 554v-1c-2-3 1-8 2-10h0v4c-1 2-2 4-2 7z" class="G"></path><path d="M268 541h2c1 1 1 2 2 3v1l-1 1c-2 0-3 1-4 2v-2l1-2v-3z" class="L"></path><path d="M268 541h2c1 1 1 2 2 3h-4v-3z" class="c"></path><path d="M255 559h2c0 4 0 9 1 13l1 9c1 0 1 2 1 2-1 2-1 3-1 4l-2-10-2-18z" class="U"></path><path d="M270 533c2 0 2 1 3 2 1 2 1 4 1 6-1 2-1 4-2 6l-1-1 1-1v-1c-1-1-1-2-2-3l-3-3c0-2 2-4 3-5z" class="I"></path><defs><linearGradient id="BY" x1="250.768" y1="421.39" x2="274.273" y2="458.554" xlink:href="#B"><stop offset="0" stop-color="#625d60"></stop><stop offset="1" stop-color="#959494"></stop></linearGradient></defs><path fill="url(#BY)" d="M272 408c1-1 1-1 2-1l1 2h0v-2l1-1v1 1 2c1 1 0 3 1 4 0 5 0 9-1 14h1v6c-3 5-5 11-7 17-4 15-5 32-1 48l1 3 2 8c1 3 2 7 2 11-1 2-2 4-4 6l-2 3c-3 0-7 1-9-1h2c-1-1-2-1-3-1 1-7 2-13 5-20 0-2 1-4 2-6 0-1-1-5-1-7v-8c0-1-1-1-1-2v-5c0-3 0-7 1-10-1-2-1-4-3-6l2-10c3-7 5-16 5-23l-1-1c-3 0-5 0-7-1-2 1-4 3-6 5h-1c-1 1-3 3-5 4l-1 1h-1c-1-1-1-1-2-1-2 1-3 3-4 5-1 0-2 1-3 2v1-6l2-8 2-2-2-1h-3l20-16 7-4 4-1h1 4z"></path><path d="M268 410c2 1 3 1 5 3 0 3 0 8 1 11 0 1-1 3-1 5-1 5-1 11-4 15h0c0-5 1-9 2-14 0-4 2-9 0-13v-1l-1-1c-1-1-3-2-5-1h-1 0c-2 1-2 1-4 1 3-2 6-3 8-5z" class="V"></path><path d="M267 408h1 4l1 1v1h-5c-2 2-5 3-8 5-7 4-13 10-19 15l-2-1h-3l20-16 7-4 4-1z" class="b"></path><defs><linearGradient id="BZ" x1="275.557" y1="464.7" x2="264.344" y2="464.234" xlink:href="#B"><stop offset="0" stop-color="#3b1a18"></stop><stop offset="1" stop-color="#322e2c"></stop></linearGradient></defs><path fill="url(#BZ)" d="M272 408c1-1 1-1 2-1l1 2h0v-2l1-1v1 1 2c1 1 0 3 1 4 0 5 0 9-1 14h1v6c-3 5-5 11-7 17-4 15-5 32-1 48l1 3 2 8c1 3 2 7 2 11-1 2-2 4-4 6l-2 3c-3 0-7 1-9-1h2c-1-1-2-1-3-1 1-7 2-13 5-20 0-2 1-4 2-6 0-1-1-5-1-7v-8c0-1-1-1-1-2v-5c0-3 0-7 1-10l5-26h0c3-4 3-10 4-15 0-2 1-4 1-5-1-3-1-8-1-11-2-2-3-2-5-3h5v-1l-1-1z"></path><path d="M269 499l1 3-2 3v-1c-1-3 0-3 1-5z" class="q"></path><path d="M270 520l1 1c0 2 0 3-1 5v1l-2 3c-3 0-7 1-9-1h2c1-1 2-1 3-2l1 1 1-1c1-1 1-2 2-3s1-2 2-4z" class="y"></path><defs><linearGradient id="Ba" x1="271.908" y1="505.874" x2="266.683" y2="516.932" xlink:href="#B"><stop offset="0" stop-color="#4d4343"></stop><stop offset="1" stop-color="#726868"></stop></linearGradient></defs><path fill="url(#Ba)" d="M270 502l2 8c1 3 2 7 2 11-1 2-2 4-4 6v-1c1-2 1-3 1-5l-1-1c-1-2-1-4-1-5 0-4 0-7-1-10l2-3z"></path><path d="M271 521c0-3 1-6 0-9 0-1 0-1 1-2 1 3 2 7 2 11-1 2-2 4-4 6v-1c1-2 1-3 1-5z" class="V"></path><defs><linearGradient id="Bb" x1="265.508" y1="506.285" x2="261.418" y2="527.956" xlink:href="#B"><stop offset="0" stop-color="#8d8c8d"></stop><stop offset="1" stop-color="#aaa9aa"></stop></linearGradient></defs><path fill="url(#Bb)" d="M258 528c1-7 2-13 5-20 0-2 1-4 2-6v1c1 9 5 17-1 24-1 1-2 1-3 2-1-1-2-1-3-1z"></path><path d="M747 418c0-3 0-6 1-8 6 2 11 4 16 7 6 5 12 9 17 14l2 4-1 3c1 0 2 2 2 2v3c1 3 2 8 3 11 0 1-1 1-1 2 0 2 0 4-1 5h0c1 2 1 3 1 4 2 3 2 6 3 9 1 2 2 3 3 5 0 2 0 5 1 8l1 14v17c-1 2 0 5-1 8l-1 11c0 2-1 4 0 6h1c0 2-1 4-1 6v1 20c-2 3-2 5-3 8-1 1 0 3-1 4 0 1 0 1-1 2 0 1-1 3 0 5h-1l1 1c1-1 2-2 3-2l-1 2-2 2c-1 2-2 5-3 7 0 3-2 8-2 10v5c-1 6-1 11 0 17v1c1 0 1 1 1 1 1 1 1 2 1 2h-2l-3 3c-3 4-5 8-6 12l-1 3-2 1v7l1 26 1 4h-3 0v2c-1 1 0 2 0 3-1-1-1-3-2-5 0-1 0-1-1-2-1-2-2-6-2-9-2-8-3-16-4-24-2-2-2-3-3-5l1-1 1 1c0-2 0-3 1-4h0v-5-10c1-6 1-13 1-19v-13-2-2l-1 2h0c-1-2 1-5 1-7l-1-1v-3l-1-2 4-26v-8c1-1 0-3 1-4l1 1v-3l-1-15-1-6-1-3-2-16-2-14-1-5c1-12-1-25-5-36 0-3-1-7-2-9v-2l-2-5c-1-4-1-9-1-14v-2z" class="d"></path><path d="M783 473c1-1 1-2 1-3v-1l1 2c0 2-1 4-1 6l-1-4z" class="H"></path><path d="M758 452c1-1 2-1 2-2h2c1 2 2 3 2 6-1-1-2-3-3-4 0 2 0 2 1 4l-3 3-1-7z" class="E"></path><path d="M771 555c0-1 0-2 1-3l-1-1c0-2 0-5 1-6v-8-6c-1 2-1 3-1 5h0v-15h1c1 10 1 23 0 34h-1z" class="T"></path><path d="M786 579c1 1 1 1 1 2-1 2-2 9-1 10l1 1c-1 2-2 5-3 7l-3 4v-1l5-23z" class="O"></path><path d="M770 661l1 26 1 4h-3v-7h0c0-5-2-9-2-14 2 2 1 3 2 5v2c1-1 1-2 0-4h0c1-4 0-8 1-12z" class="R"></path><path d="M786 465c2 3 2 6 3 9 1 2 2 3 3 5 0 2 0 5 1 8l1 14v17c-1 2 0 5-1 8v-6h-1c1-1 1-3 1-4v-3-1-1-2c0-4 1-8 0-11v1h-1c-1 1-1 4-1 6s-1 12-1 13v-13c1-9 1-18-1-28-1-3-1-5-3-8v-4z" class="P"></path><path d="M784 599c0 3-2 8-2 10-4 11-8 21-9 32-1 3-1 6-1 8l1 1-1 3-2 1c0-6 2-12 3-18 2-11 4-24 8-34v1l3-4z" class="l"></path><path d="M792 543h1c0 2-1 4-1 6v1 20c-2 3-2 5-3 8-1 1 0 3-1 4 0 1 0 1-1 2 0 1-1 3 0 5h-1l1 1c1-1 2-2 3-2l-1 2-2 2-1-1c-1-1 0-8 1-10 0-1 0-1-1-2 0-1 1-3 1-4l2-12 3-20z" class="N"></path><path d="M763 436l3 6c2 2 4 5 6 8 3 4 5 10 6 16v2l-2-7c-3-3-4-7-5-10-2 0-2 0-4 1 0 1 1 3 1 4 1 2 1 3 1 4v1c0 4 2 9 2 13h0l-4-15c-1-6-4-12-5-19l1-4z" class="B"></path><path d="M769 461c-1-2-1-3-2-4 0-3-1-6 0-8l2-1c3 2 6 9 7 13-3-3-4-7-5-10-2 0-2 0-4 1 0 1 1 3 1 4 1 2 1 3 1 4v1z" class="S"></path><path d="M766 593v-2c0-1 0-2 1-3v-2c0-1 0-5 1-6v-2c0-1 0-2 1-3v-3c0-1 0-2 1-4v-5l1-1-2-1c1-3 0-5 1-7 1 1 0 3 1 4v-3h1l-2 15-2 12c0 3 0 8-1 11 1-1 2-4 3-5 1 2 5 4 5 7 1 2 0 6 0 9-1 8-3 15-5 23h0v-1 2h-1v-3-1s-1-1-1-2l-1-4v-4l2-2c2 1 2 1 3 0 2-6 2-13 0-19l-1-1c-1 1-2 1-3 2s-1 0-2 1v-2z" class="H"></path><defs><linearGradient id="Bc" x1="772.27" y1="632.851" x2="781.179" y2="630.533" xlink:href="#B"><stop offset="0" stop-color="#9b9999"></stop><stop offset="1" stop-color="#bcb9b8"></stop></linearGradient></defs><path fill="url(#Bc)" d="M773 641c1-11 5-21 9-32v5c-1 6-1 11 0 17v1c1 0 1 1 1 1 1 1 1 2 1 2h-2l-3 3c-3 4-5 8-6 12l-1-1c0-2 0-5 1-8z"></path><path d="M780 634v-5-1c1 0 2 2 2 3v1c1 0 1 1 1 1 1 1 1 2 1 2h-2l-3 3c-1-1 0-2 1-4z" class="S"></path><path d="M773 641c2 0 4-3 5-6 1-1 1 0 2-1-1 2-2 3-1 4-3 4-5 8-6 12l-1-1c0-2 0-5 1-8z" class="W"></path><defs><linearGradient id="Bd" x1="758.306" y1="648.181" x2="774.694" y2="641.819" xlink:href="#B"><stop offset="0" stop-color="#888283"></stop><stop offset="1" stop-color="#b5b5b2"></stop></linearGradient></defs><path fill="url(#Bd)" d="M767 618l1 4c0 1 1 2 1 2v1 3h1v-2 1l-1 9c-2 11-3 23-2 34 0 5 2 9 2 14h0c-2-7-4-15-5-23-1-12 0-24 2-36h-1l2-7z"></path><path d="M767 618l1 4c0 1 1 2 1 2v1 3h1v-2 1l-1 9c0-1 0-2-1-4-1-3 1-7-1-10 0 1 0 2-1 2v1h-1l2-7z" class="E"></path><defs><linearGradient id="Be" x1="749.697" y1="424.82" x2="763.509" y2="448.922" xlink:href="#B"><stop offset="0" stop-color="#969495"></stop><stop offset="1" stop-color="#b9b9b9"></stop></linearGradient></defs><path fill="url(#Be)" d="M756 445l-3-17c-1-2-1-3-1-4h1v1h4l2 4 1 2v-2l1-1 1 1c-1 3 0 5 1 7h0l-1 4c1 7 4 13 5 19-1-1-1-2-2-3 0-2-1-4-2-6h-1-2c0 1-1 1-2 2l-2-7z"></path><path d="M759 429l1 2v-2l1-1 1 1c-1 3 0 5 1 7h0l-1 4-3-11z" class="F"></path><path d="M764 517c0 3 0 7 1 9v1h-1v1c1 4 1 9 2 14v2c1-3 1-5 0-7v-2-1c-1-1 0-3 0-4 0 1 1 1 1 2v3c1 4 0 10 0 14 1 6 2 12 1 18-1 1-1 3-1 4v6l1 1c-1 1-2 3-2 5-1 4 0 7-1 11v1c1-1 1-1 1-2v2c-2 4-3 9-3 14-1-2-1-3 0-5v-1h0c-1 3 0 6-2 10v-13-2-2l-1 2h0c-1-2 1-5 1-7l-1-1v-3l-1-2 4-26v-8c1-1 0-3 1-4l1 1v-3l-1-15-1-6-1-3c1-1 1-2 2-4z" class="E"></path><path d="M763 588l2-4c0 4-1 7-1 10-1 1-1 2-2 4l1-10z" class="S"></path><path d="M760 587l3-3v4l-1 10-1 2v-2-2l-1 2h0c-1-2 1-5 1-7l-1-1v-3z" class="V"></path><path d="M765 545c2 7 1 15 1 22 0 3 0 6-1 10v4-3l-1-3 1-12c-1-2-1-3-1-4h-1v-8c1-1 0-3 1-4l1 1v-3z" class="S"></path><path d="M763 551c1-1 0-3 1-4l1 1v15c-1-2-1-3-1-4h-1v-8z" class="U"></path><path d="M763 559h1c0 1 0 2 1 4l-1 12 1 3v3 3l-2 4v-4l-3 3-1-2 4-26z" class="K"></path><path d="M763 584l1-9 1 3v3 3l-2 4v-4z" class="j"></path><defs><linearGradient id="Bf" x1="735.085" y1="442.404" x2="774.415" y2="484.596" xlink:href="#B"><stop offset="0" stop-color="#898889"></stop><stop offset="1" stop-color="#c6c5c4"></stop></linearGradient></defs><path fill="url(#Bf)" d="M747 420h4v1l5 2 1 2h-4v-1h-1c0 1 0 2 1 4l3 17 2 7 1 7c1 3 2 7 2 11l2 23c0 4 1 9 3 13h-1l-1-4v-2l-1-1v-2-1c-1-1 0-2-1-3 0-2 1-4 0-6v-5l-2 3h0c1 3 1 5 1 8 1 3 0 6 1 10l1 6 1 7c0 2 1 4 1 6v4c-1-2-1-6-1-9-1 2-1 3-2 4l-2-16-2-14-1-5c1-12-1-25-5-36 0-3-1-7-2-9v-2l-2-5c-1-4-1-9-1-14z"></path><path d="M758 491h1l1 8v-5c-1-2-1-4 0-6h0l4 29c-1 2-1 3-2 4l-2-16-2-14z" class="S"></path><path d="M747 420h4v1l5 2 1 2h-4v-1h-1c0 1 0 2 1 4l3 17v1c-2-3-2-9-3-12-1-1-2-3-2-5h0-1v2 3h0v4 1l-2-5c-1-4-1-9-1-14z" class="I"></path><path d="M747 420h4v1c0 1 1 2 0 3l-1-1c0 1 0 2 1 4h-1v-2c-2 2-1 4-1 7l-1 2c-1-4-1-9-1-14z" class="i"></path><path d="M766 595c1-1 1 0 2-1s2-1 3-2l1 1c2 6 2 13 0 19-1 1-1 1-3 0l-2 2v4l-2 7h1c-2 12-3 24-2 36 1 8 3 16 5 23v7h0v2c-1 1 0 2 0 3-1-1-1-3-2-5 0-1 0-1-1-2-1-2-2-6-2-9-2-8-3-16-4-24-2-2-2-3-3-5l1-1 1 1c0-2 0-3 1-4h0v-5-10c1-6 1-13 1-19 2-4 1-7 2-10h0v1c-1 2-1 3 0 5 0-5 1-10 3-14z" class="c"></path><path d="M758 650l1 1c0-2 0-3 1-4h0v9c-2-2-2-3-3-5l1-1z" class="Y"></path><path d="M765 625v-1c-1-4 0-8 2-11l2-1-2 2v4l-2 7z" class="T"></path><defs><linearGradient id="Bg" x1="774.174" y1="430.667" x2="770.827" y2="491.806" xlink:href="#B"><stop offset="0" stop-color="#a1a1a2"></stop><stop offset="1" stop-color="#ceccca"></stop></linearGradient></defs><path fill="url(#Bg)" d="M747 418c0-3 0-6 1-8 6 2 11 4 16 7 6 5 12 9 17 14l2 4-1 3c1 0 2 2 2 2v3c1 3 2 8 3 11 0 1-1 1-1 2 0 2 0 4-1 5h0c1 2 1 3 1 4v4c2 3 2 5 3 8-1-1-2-3-3-4l-1-2-1-2v1c0 1 0 2-1 3l1 4c2 8 1 18 0 26h0l-3-25c-1-3-3-6-3-10v-2c-1-6-3-12-6-16-2-3-4-6-6-8l-3-6h0c-1-2-2-4-1-7l-1-1-1 1v2l-1-2-2-4-1-2-5-2v-1h-4v-2z"></path><defs><linearGradient id="Bh" x1="769.682" y1="431.39" x2="788.621" y2="468.1" xlink:href="#B"><stop offset="0" stop-color="#7e7d7e"></stop><stop offset="1" stop-color="#d3d3d2"></stop></linearGradient></defs><path fill="url(#Bh)" d="M770 435h0l-1-2v-1 1c3 2 4 2 6 5 2 0 2 0 3-1h3l3 6c1 3 2 8 3 11 0 1-1 1-1 2 0 2 0 4-1 5h0c1 2 1 3 1 4v4c2 3 2 5 3 8-1-1-2-3-3-4l-1-2-1-2v1c0 1 0 2-1 3l-1-6c-1-3-1-6-1-9l-1-1c0-1-1-1-1-2-1-1-1-1-1-2-1-2-2-5-2-7-2-4-4-7-6-11z"></path><defs><linearGradient id="Bi" x1="766.705" y1="419.158" x2="762.809" y2="426.814" xlink:href="#B"><stop offset="0" stop-color="#5b5457"></stop><stop offset="1" stop-color="#747174"></stop></linearGradient></defs><path fill="url(#Bi)" d="M747 418c0-3 0-6 1-8 6 2 11 4 16 7 6 5 12 9 17 14l2 4-1 3c1 0 2 2 2 2v3l-3-6h-3c-1 1-1 1-3 1-2-3-3-3-6-5v-1 1l1 2h0c-1 0-2-2-3-2-2-2-4-3-5-4l-1-1-1 1v2l-1-2-2-4-1-2-5-2v-1h-4v-2z"></path><path d="M747 418c2 0 2 0 4 2h-4v-2z" class="M"></path><path d="M756 423c3 1 6 2 8 4 3 1 4 3 7 4 2 1 3 2 5 3h2 0v-1c1 1 3 3 4 5 1 0 2 2 2 2v3l-3-6h-3c-1 1-1 1-3 1-2-3-3-3-6-5v-1 1l1 2h0c-1 0-2-2-3-2-2-2-4-3-5-4l-1-1-1 1v2l-1-2-2-4-1-2z" class="J"></path><path d="M367 118c1 0 3 4 3 4v4l1 2c0 3-1 4-2 6 0 5-2 9 1 13-1 3-2 6-5 8 2 0 5 0 6-1h3v1h3l3 4c5 1 10 1 15 0 1 0 5-2 6-2 0 0 2 2 2 3a30.44 30.44 0 0 1 8 8c3 4 6 9 5 14 0 2-1 5-2 7l-3 4s1-1 2-1c0 1 0 1-1 2v2l-2 2c-2 1-3 3-4 5 3-1 6-2 9-2-5 2-10 4-14 7l1 1 4 1-2 1 9-2h2c3-1 9-2 12-1 1 0 2 1 2 2l-6-1-1 1v1c1 1 4 0 6 0-4 0-7 0-10 2l-1 1-3 3v1l-1 1v1 2l-3 4-2 2c-1 1-1 1-1 2h-1c-1 0-1 1-2 1-6 2-13 1-19 1h-30c-2-1-4 0-5 0l-1-1h4c0-1 0-1-1-2-2 1-3 1-4 1h-1c-2 1-5 1-8 1v-1l2-2 1-1c0-1 1-2 2-3-1 1-2 1-3 2h-1c-2 2-5 4-8 5h-5c-3 0-4 0-6-2-1 1-3 2-4 2 1 1 6 0 7 1h-11 0-11l-1-1c-3-1-7 0-10 0v1l2 1h-26v-1h-3 0 1v-1h0c-3-1-4-2-6-3h0c-2-2-3-3-4-5 2 0 2 2 4 3 1 1 4 2 5 1l2-1v-3h0 1c1-2-1-3-1-4v-2c1 0 2 1 3 1 1-3 2-5 3-7l2-2v-1c1-2 3-4 4-6l6-10 7-7c4-2 7-7 11-10 2-2 4-3 6-3l23-15c-1-1-2-1-3 0h0l-1-1v-1c2 0 4-1 5-2v-1h1l7-1c0-1-1-2-2-3 1 0 2 1 3 1 2 1 6 1 9 1 3-1 5-1 7-4 1-1 2-3 2-5s-1-7 1-9h0v-2-1c1-1 2-3 4-4l3-1c1-2-1-2-1-4 0-1 1-2 1-2z" class="d"></path><path d="M357 185l1-1h1v-1c-1 0-2 1-4 1-1 1-3 2-4 2v-1c1 0 1 0 2-1h1c3-1 9-4 12-3h1 0 2c0-1 1-1 2-1h1l1-1c2-1 8 0 11 0l-7 1c-4 0-12 2-16 5l-2 1-2-1z" class="p"></path><path d="M377 180c1 0 2 1 3 1 2 0 2 1 4 2-4 0-8 0-12 1s-7 2-11 3v-2c4-3 12-5 16-5z" class="q"></path><path d="M384 179c6 2 12 4 15 10 1 2 2 5 1 7 0 1 0 2-1 3l-1 1c-1-1 0-4 0-5 0-2-2-3-3-4-3-4-7-6-11-8-2-1-2-2-4-2-1 0-2-1-3-1l7-1z" class="x"></path><path d="M307 194l4-3h1l-4 6 1 1 4-2h4c-7 5-16 12-21 20h0v-3l-1-1c-1 1-3 2-4 3l2-4 10-13 4-4z" class="B"></path><path d="M316 188c-1 3-4 5-5 7v1l1-1c2 0 1-1 3-1v-1c1 0 1-1 2-1 4-2 6-4 10-6 6-3 13-6 19-9 2 0 4-2 6-2 10-1 22-6 33-3v1c-5-1-10-1-15-1-1 0-3 1-4 1h-1-1c-1 0-1 0-2 1-1 0-2 0-4 1l-4 1-5 2-2 1h0c-1 1-2 1-2 1l-18 10-8 5s-1 1-2 1h-4l-4 2-1-1 4-6h0l4-3z" class="H"></path><path d="M313 196c4-3 9-6 14-6l-8 5s-1 1-2 1h-4z" class="E"></path><path d="M359 186l2-1v2l-4 2c-5 1-9 5-13 7h0l-5 4c-6 4-10 8-15 12-3 3-6 6-9 10-2 1-6 6-6 8v1s1 0 1 1l1-1c1 0 2 0 3 1h-1-11l-1-1c-3-1-7 0-10 0h-3c0-2 0-2 1-3l2-3h0l2-3c1 0 2 1 3 1 0-1 1-2 2-3l13-14h1v1l1 1c4-3 7-5 11-8 2 0 2 0 3 2l5-4 3-2 1-1c0-1 1-1 2-1 1-1 1-1 2-1h0c1-1 1-1 2-1s1 0 2-1c2 0 5-1 7-2l2-2h1c0-1 2-2 3-2l2 1z" class="G"></path><path d="M319 212c2-3 6-5 9-7-1 2-3 4-5 5v1h-1c-1 0-2 1-3 1z" class="r"></path><path d="M301 226c1 0 2-1 3-2 3-1 5-5 9-6l-7 8h-5z" class="D"></path><path d="M359 186l2-1v2l-4 2c-5 1-9 5-13 7-2 1-4 2-6 1 7-4 14-8 21-11z" class="X"></path><path d="M338 197c2 1 4 0 6-1h0l-5 4c-6 4-10 8-15 12l-1-2c2-1 4-3 5-5 3-3 6-6 10-8z" class="h"></path><defs><linearGradient id="Bj" x1="305.419" y1="219.351" x2="301.923" y2="211.284" xlink:href="#B"><stop offset="0" stop-color="#b8b7b8"></stop><stop offset="1" stop-color="#dcdbda"></stop></linearGradient></defs><path fill="url(#Bj)" d="M298 220l13-14h1v1l1 1-2 3h0l-6 6h1v1s0 1-1 1c-2 1-4 2-5 4l-5 2 1-1v-1h0c0-1 1-2 2-3z"></path><path d="M293 222c1 0 2 1 3 1h0v1l-1 1 5-2v1l-5 4 1 1c1 0 2-1 3-1l1-1s1 0 1-1h5c-1 2-3 4-5 5-3-1-7 0-10 0h-3c0-2 0-2 1-3l2-3h0l2-3z" class="B"></path><path d="M293 222c1 0 2 1 3 1h0v1l-1 1-5 4-1-1h0l2-3h0l2-3z" class="D"></path><defs><linearGradient id="Bk" x1="308.377" y1="232.975" x2="317.211" y2="208.925" xlink:href="#B"><stop offset="0" stop-color="#260d0c"></stop><stop offset="1" stop-color="#402f2d"></stop></linearGradient></defs><path fill="url(#Bk)" d="M319 212c1 0 2-1 3-1h1v-1l1 2c-3 3-6 6-9 10-2 1-6 6-6 8v1s1 0 1 1l1-1c1 0 2 0 3 1h-1-11l-1-1c2-1 4-3 5-5l7-8 6-6z"></path><path d="M377 155l3 4c5 1 10 1 15 0 1 0 5-2 6-2 0 0 2 2 2 3a30.44 30.44 0 0 1 8 8c3 4 6 9 5 14 0 2-1 5-2 7l-3 4-3 3c-2 2-3 4-5 6 0-2 1-3 1-5 1-2 1-6 0-8-1-1-3-6-4-7-3-6-9-8-15-9v-1c-11-3-23 2-33 3-2 0-4 2-6 2-6 3-13 6-19 9-4 2-6 4-10 6-1 0-1 1-2 1v1c-2 0-1 1-3 1l-1 1v-1c1-2 4-4 5-7 2-2 3-3 4-6 1 0 3-2 3-2v-1l3-3h-2c-2 1-4 2-7 2 6-5 13-9 20-13 3-1 6-3 9-4 2 0 3 0 5-1 3-1 6-1 10-1 4-1 10-2 13-4h3z" class="d"></path><path d="M377 155l3 4c-14 0-27 3-39 8h-1c5-4 11-5 17-7l4-1c4-1 10-2 13-4h3z" class="M"></path><defs><linearGradient id="Bl" x1="333.687" y1="161.296" x2="332.813" y2="177.204" xlink:href="#B"><stop offset="0" stop-color="#928d8b"></stop><stop offset="1" stop-color="#a9a8a9"></stop></linearGradient></defs><path fill="url(#Bl)" d="M346 161c2 0 3 0 5-1 3-1 6-1 10-1l-4 1c-6 2-12 3-17 7h1l-9 6-9 7v-1l3-3h-2c-2 1-4 2-7 2 6-5 13-9 20-13 3-1 6-3 9-4z"></path><path d="M326 176l1-1h-1l1-1c1-1 3-2 5-3v2l-9 7v-1l3-3z" class="C"></path><path d="M385 172c5 1 12 3 16 8h0c1 1 1 0 1 1 3 2 2 5 6 7-1-1-1-2-1-3v-1l-2-2v-1c-1-3-3-6-6-8l-7-4c6 2 14 6 17 13 1 2 1 5 2 7h3l-3 4-3 3c-2 2-3 4-5 6 0-2 1-3 1-5 1-2 1-6 0-8-1-1-3-6-4-7-3-6-9-8-15-9v-1z" class="P"></path><path d="M404 189h1c1 3 0 4 3 7-2 2-3 4-5 6 0-2 1-3 1-5 1-2 1-6 0-8z" class="F"></path><path d="M367 118c1 0 3 4 3 4v4l1 2c0 3-1 4-2 6 0 5-2 9 1 13-1 3-2 6-5 8 2 0 5 0 6-1h3v1c-3 2-9 3-13 4-4 0-7 0-10 1-2 1-3 1-5 1-3 1-6 3-9 4-7 4-14 8-20 13 3 0 5-1 7-2h2l-3 3v1s-2 2-3 2c-1 3-2 4-4 6l-4 3h0-1l-4 3-4 4-10 13-2 4c1-1 3-2 4-3l1 1v3h0v2l2 2c-1 1-2 2-2 3-1 0-2-1-3-1l-2 3h0l-2 3c-1 1-1 1-1 3h3v1l2 1h-26v-1h-3 0 1v-1h0c-3-1-4-2-6-3h0c-2-2-3-3-4-5 2 0 2 2 4 3 1 1 4 2 5 1l2-1v-3h0 1c1-2-1-3-1-4v-2c1 0 2 1 3 1 1-3 2-5 3-7l2-2v-1c1-2 3-4 4-6l6-10 7-7c4-2 7-7 11-10 2-2 4-3 6-3l23-15c-1-1-2-1-3 0h0l-1-1v-1c2 0 4-1 5-2v-1h1l7-1c0-1-1-2-2-3 1 0 2 1 3 1 2 1 6 1 9 1 3-1 5-1 7-4 1-1 2-3 2-5s-1-7 1-9h0v-2-1c1-1 2-3 4-4l3-1c1-2-1-2-1-4 0-1 1-2 1-2z" class="k"></path><path d="M332 160c3-2 7-2 11-3h5c0 1 2 0 2 0 1 0 1 1 1 1-1 1-2 1-3 1-1 1-1 2-2 2-3 1-6 3-9 4l-1-1h2c0-1 1-1 1-1l6-3h1-1l1-1h-1l-1-1c-4-1-7 3-11 2h-1z" class="p"></path><path d="M317 178c3 0 5-1 7-2h2l-3 3v1s-2 2-3 2c-1 3-2 4-4 6l-4 3h0-1l-4 3-4 4v-1c1-2 1-4 2-5l1-1-1-1c-1 1-2 2-3 4-3 2-7 7-10 9 2-4 16-18 20-20 1-2 3-3 5-5z" class="G"></path><path d="M312 191c0-1 1-3 2-4 1-3 4-4 6-5-1 3-2 4-4 6l-4 3z" class="P"></path><path d="M312 183h0c3-1 5-3 7-4-4 5-11 8-12 15l-4 4v-1c1-2 1-4 2-5l1-1-1-1c-1 1-2 2-3 4-3 2-7 7-10 9 2-4 16-18 20-20z" class="L"></path><defs><linearGradient id="Bm" x1="331.899" y1="163.801" x2="328.775" y2="158.225" xlink:href="#B"><stop offset="0" stop-color="#7c7778"></stop><stop offset="1" stop-color="#9f9c9c"></stop></linearGradient></defs><path fill="url(#Bm)" d="M352 152c2-1 4-1 7-3h0l1 1h0l1 2c0 1 0 2-1 4h0l5-1c2 0 5 0 6-1h3v1c-3 2-9 3-13 4-4 0-7 0-10 1-2 1-3 1-5 1 1 0 1-1 2-2 1 0 2 0 3-1 0 0 0-1-1-1 0 0-2 1-2 0h-5c-4 1-8 1-11 3-7 3-12 8-19 12l-1-1-7 5 3-4 23-15c-1-1-2-1-3 0h0l-1-1v-1c2 0 4-1 5-2h4 1 4l-1-1h1 11z"></path><path d="M332 153h4 1 4 1v1c-1 0-6-1-7 0s-2 2-4 2v1c-1-1-2-1-3 0h0l-1-1v-1c2 0 4-1 5-2z" class="S"></path><path d="M352 152c2-1 4-1 7-3h0l1 1h0c-3 2-6 5-9 5 0 0 1-1 2-1h0c-1-1-3-1-5-1l4-1z" class="f"></path><path d="M351 155c3 0 6-3 9-5l1 2c0 1 0 2-1 4h0l5-1c2 0 5 0 6-1h3v1c-3 2-9 3-13 4-4 0-7 0-10 1-2 1-3 1-5 1 1 0 1-1 2-2 1 0 2 0 3-1 0 0 0-1-1-1 0 0-2 1-2 0h-5l4-1c2 0 3-1 4-1z" class="P"></path><path d="M351 155c3 0 6-3 9-5l1 2c0 1 0 2-1 4h0-3c1-1 0-1 2-1v-1c-2 0-2 1-3 1-2 1-6 1-9 1 2 0 3-1 4-1z" class="B"></path><defs><linearGradient id="Bn" x1="361.507" y1="143.355" x2="341.072" y2="143.141" xlink:href="#B"><stop offset="0" stop-color="#7e7a7a"></stop><stop offset="1" stop-color="#a19e9d"></stop></linearGradient></defs><path fill="url(#Bn)" d="M367 118c1 0 3 4 3 4v4l1 2c0 3-1 4-2 6 0 5-2 9 1 13-1 3-2 6-5 8l-5 1h0c1-2 1-3 1-4l-1-2h0l-1-1h0c-3 2-5 2-7 3h-11-1l1 1h-4-1-4v-1h1l7-1c0-1-1-2-2-3 1 0 2 1 3 1 2 1 6 1 9 1 3-1 5-1 7-4 1-1 2-3 2-5s-1-7 1-9h0v-2-1c1-1 2-3 4-4l3-1c1-2-1-2-1-4 0-1 1-2 1-2z"></path><path d="M363 134c1 2 1 2 1 4v6h0v-2h1c0 3-1 5-2 7l-2 3-1-2h0c5-3 1-12 3-16z" class="R"></path><path d="M363 132c1-1 3-3 4-5h1l1-1 1 2c-1 1 0 2-1 3 0 2-2 3-3 5s-1 4-1 6h-1v2h0v-6c0-2 0-2-1-4h0v-2z" class="S"></path><path d="M367 118c1 0 3 4 3 4v4l1 2h-1l-1-2-1 1h-1c-1 2-3 4-4 5h-1c0 1-1 2-2 3v1c-1 1-1 2-1 4v1c0-2-1-7 1-9h0v-2-1c1-1 2-3 4-4l3-1c1-2-1-2-1-4 0-1 1-2 1-2z" class="W"></path><path d="M370 128h1c0 3-1 4-2 6 0 5-2 9 1 13-1 3-2 6-5 8l-5 1h0c1-2 1-3 1-4l2-3c1-2 2-4 2-7 0-2 0-4 1-6s3-3 3-5c1-1 0-2 1-3z" class="J"></path><path d="M370 128h1c0 3-1 4-2 6-3 5-2 10-4 15h-1-1c1-2 2-4 2-7 0-2 0-4 1-6s3-3 3-5c1-1 0-2 1-3z" class="M"></path><path d="M305 176l7-5 1 1-8 7h0l-6 6-2 2c-5 5-10 11-14 17-2 3-4 6-5 10-1 0-2 4-3 4-1 3-2 7-4 10l-1 3h-5 0c-3-1-4-2-6-3h0c-2-2-3-3-4-5 2 0 2 2 4 3 1 1 4 2 5 1l2-1v-3h0 1c1-2-1-3-1-4v-2c1 0 2 1 3 1 1-3 2-5 3-7l2-2v-1c1-2 3-4 4-6l6-10 7-7c4-2 7-7 11-10 2-2 4-3 6-3l-3 4z" class="e"></path><path d="M266 219c3 1 3 2 6 1l-3 3v1c-1 1-2 2-3 2v-3h0 1c1-2-1-3-1-4z" class="U"></path><path d="M274 209l2-1-1 4-3 7v1c-3 1-3 0-6-1v-2c1 0 2 1 3 1 1-3 2-5 3-7l2-2z" class="M"></path><path d="M263 229l1-1h1c2 0 4-2 5-4 0-1 0-1 1-1l3-3c0-1 1-1 1-2-1 3-2 7-4 10l-1 3h-5 0c-3-1-4-2-6-3 2 0 3 0 4 1h0z" class="O"></path><path d="M259 228c2 0 3 0 4 1h0c4 1 5 0 8-1l-1 3h-5 0c-3-1-4-2-6-3z" class="b"></path><path d="M305 176l7-5 1 1-8 7h0l-6 6-2 2c-4 1-6 5-10 6-3 3-5 7-8 11-1 1-2 3-3 4l-2 1v-1c1-2 3-4 4-6l6-10 7-7c4-2 7-7 11-10 2-2 4-3 6-3l-3 4z" class="D"></path><path d="M305 176l7-5 1 1-8 7h0c-4 2-6 4-10 5 3-3 7-6 10-8z" class="f"></path><path d="M295 184c4-1 6-3 10-5l-6 6-2 2c-4 1-6 5-10 6l8-9z" class="l"></path><path d="M302 194c1-2 2-3 3-4l1 1-1 1c-1 1-1 3-2 5v1l-10 13-2 4c1-1 3-2 4-3l1 1v3h0v2l2 2c-1 1-2 2-2 3-1 0-2-1-3-1l-2 3h0l-2 3c-1 1-1 1-1 3h3v1l2 1h-26v-1h-3 0 1v-1h5 4c2-2 3-5 4-7 3-5 5-10 9-14h0l1-3 4-4c3-2 7-7 10-9z" class="K"></path><path d="M280 225c0 1 1 1 0 3v3h-3l3-6z" class="S"></path><path d="M291 215c1-1 3-2 4-3l1 1v3h0v2l2 2c-1 1-2 2-2 3-1 0-2-1-3-1l-2 3h0-1l-3 3c0-2 1-2 2-4h-1c-3 2-4 6-7 7 2-3 3-6 5-9l5-7z" class="f"></path><path d="M289 224c1 0 5-10 7-11v3h0v2l2 2c-1 1-2 2-2 3-1 0-2-1-3-1l-2 3h0-1l-3 3c0-2 1-2 2-4z" class="F"></path><path d="M296 216h0v2l2 2c-1 1-2 2-2 3-1 0-2-1-3-1l3-6z" class="L"></path><defs><linearGradient id="Bo" x1="277.272" y1="221.449" x2="300.096" y2="203.257" xlink:href="#B"><stop offset="0" stop-color="#bebcbc"></stop><stop offset="1" stop-color="#e8e7e7"></stop></linearGradient></defs><path fill="url(#Bo)" d="M302 194c1-2 2-3 3-4l1 1-1 1c-1 1-1 3-2 5v1l-10 13-2 4-5 7c-2 3-3 6-5 9h-1v-3c1-2 0-2 0-3l7-15h0l1-3 4-4c3-2 7-7 10-9z"></path><path d="M302 194c1-2 2-3 3-4l1 1-1 1c-1 1-1 3-2 5v1l-10 13c-1-1-1-1 0-2l1-1c1-2 3-5 4-7 1-3 4-5 4-7z" class="H"></path><path d="M372 184c4-1 8-1 12-1 4 2 8 4 11 8 1 1 3 2 3 4 0 1-1 4 0 5l1-1v1l1 1c2 0 3-2 4-4 0 2-1 3-1 5 2-2 3-4 5-6l3-3s1-1 2-1c0 1 0 1-1 2v2l-2 2c-2 1-3 3-4 5 3-1 6-2 9-2-5 2-10 4-14 7l1 1 4 1-2 1 9-2h2c3-1 9-2 12-1 1 0 2 1 2 2l-6-1-1 1v1c1 1 4 0 6 0-4 0-7 0-10 2l-1 1-3 3v1l-1 1v1 2l-3 4-2 2c-1 1-1 1-1 2h-1c-1 0-1 1-2 1-6 2-13 1-19 1h-30c-2-1-4 0-5 0l-1-1h4c0-1 0-1-1-2-2 1-3 1-4 1h-1c-2 1-5 1-8 1v-1l2-2 1-1c0-1 1-2 2-3-1 1-2 1-3 2h-1c-2 2-5 4-8 5h-5c-3 0-4 0-6-2-1 1-3 2-4 2 1 1 6 0 7 1h-11 0 1c-1-1-2-1-3-1l-1 1c0-1-1-1-1-1v-1c0-2 4-7 6-8 3-4 6-7 9-10 5-4 9-8 15-12l5-4h0c4-2 8-6 13-7l4-2c4-1 7-2 11-3z" class="w"></path><path d="M390 221c1 1 2 1 2 2s0 0 1 1c-1 1-1 2-1 3h-1c0-2-1-4-1-6z" class="B"></path><path d="M393 229c0-1 1-2 1-3l2 2-1 2h-2v-1z" class="N"></path><path d="M383 211s1 1 2 1c1 1 1 2 1 3-2-1-4-1-7-1h2c1 0 1 0 2-1v-2z" class="s"></path><path d="M393 216v3c0 1-1 3 0 4v1c-1-1-1 0-1-1s-1-1-2-2c1-2 2-4 3-5z" class="R"></path><path d="M385 208c4 0 8 0 11-2-1 2-2 4-3 5-1-1-1-1-1-2h-3c-2 0-2 0-4-1z" class="r"></path><path d="M404 214c1-1 2-2 4-2 2-1 3-2 5-1-3 2-6 3-9 5v-2z" class="N"></path><path d="M361 208c1 0 4-1 6 0h4l-6 1-1 1h-2-4l3-2z" class="s"></path><path d="M361 208l4 1-1 1h-2-4l3-2z" class="O"></path><path d="M357 205c-1 2-1 3-3 4-3 1-4 4-7 4v-1l5-4c1-1 3-2 5-3z" class="o"></path><path d="M384 205h6c2 1 2 1 4 1h3-1c-3 2-7 2-11 2-1 0-2-1-3-1l2-2z" class="D"></path><path d="M404 214v2c-3 4-6 8-8 12l-2-2c2-2 3-4 5-6 1-2 3-4 5-6z" class="Z"></path><path d="M411 193s1-1 2-1c0 1 0 1-1 2v2l-2 2c-2 1-3 3-4 5l-3 2v-1l3-2-1-1-3 3-1-1 2-1c2-2 3-4 5-6l3-3z" class="v"></path><path d="M399 199v1l1 1c2 0 3-2 4-4 0 2-1 3-1 5l-2 1c-1 1-3 2-4 3h-3c-2 0-2 0-4-1 3-1 6-2 8-5l1-1z" class="P"></path><path d="M393 216c3-4 5-6 8-8l1 1 4 1-2 1c-4 4-8 7-11 12-1-1 0-3 0-4v-3z" class="G"></path><path d="M373 208c2 0 3 0 5 1s3 2 5 2v2c-1 1-1 1-2 1l-3-3c-4 1-7 1-11 3h-3l2-3h-1l-1-1 1-1 6-1h2z" class="O"></path><path d="M371 208h2l-1 1-6 2h-1l-1-1 1-1 6-1z" class="Z"></path><path d="M404 211l9-2h2c3-1 9-2 12-1 1 0 2 1 2 2l-6-1c-4 1-7 2-10 2-2-1-3 0-5 1-2 0-3 1-4 2-2 2-4 4-5 6-2 2-3 4-5 6 0 1-1 2-1 3l-2-2h1c0-1 0-2 1-3v-1c3-5 7-8 11-12z" class="Q"></path><defs><linearGradient id="Bp" x1="369.936" y1="230.419" x2="362.564" y2="220.581" xlink:href="#B"><stop offset="0" stop-color="#6e6d6f"></stop><stop offset="1" stop-color="#908f90"></stop></linearGradient></defs><path fill="url(#Bp)" d="M348 230c3-4 8-6 12-9l1 1c5-1 15 2 19 4v1h-1c-8 0-16 1-23 2h-4c-2 1-3 1-4 1z"></path><path d="M348 230c3-4 8-6 12-9l1 1c-3 2-7 4-10 7 1-1 3-1 4-2 5-2 13-1 19-1 1 0 3 0 4 1h1c-8 0-16 1-23 2h-4c-2 1-3 1-4 1z" class="V"></path><path d="M365 197h3c3 2 5 2 8 3l1-2h0c1 1 1 0 2 0 1 1 2 2 3 2l2 5-2 2c-6-3-9-4-15-4-4 1-7 1-10 2-2 1-4 2-5 3h-2c1-1 2-2 4-2v-1c2-3 5-4 8-6h0l-6 1c1-1 2-1 3-2h1l5-1z" class="R"></path><path d="M365 197h3c3 2 5 2 8 3l1-2h0c1 1 1 0 2 0 1 1 2 2 3 2l2 5-2 2c-6-3-9-4-15-4 2-1 6 0 8 0-3-3-6-3-11-3v-1c1 0 1 0 2-1l-1-1z" class="g"></path><path d="M376 200l1-2h0c1 1 1 0 2 0 1 1 2 2 3 2l2 5h-3l-1-1c-1-2-2-3-4-4z" class="N"></path><path d="M367 214c4-2 7-2 11-3l3 3h-2c-7 2-12 4-19 7-4 3-9 5-12 9h-1c-2 1-5 1-8 1v-1l2-2 7-7h3 1c0-1 1 0 2 0 3-2 5-3 7-5l6-2z" class="C"></path><path d="M348 221h3 1c0-1 1 0 2 0-3 2-5 3-8 4-2 1-3 3-5 5h4v-1c1 0 2 1 2 1-2 1-5 1-8 1v-1l2-2 7-7z" class="I"></path><path d="M358 210h4 2l1 1h1l-2 3h3l-6 2c-2 2-4 3-7 5-1 0-2-1-2 0h-1-3l-7 7 1-1c0-1 1-2 2-3-1 1-2 1-3 2h-1c-2 2-5 4-8 5h-5l12-12c3-3 5-5 8-7v1 2c3-2 5-3 8-4l3-1z" class="i"></path><path d="M358 210h4c-3 2-5 2-7 3s-3 2-5 2c2-1 4-3 5-4l3-1z" class="V"></path><path d="M340 226c4-4 9-7 14-10l-6 5-7 7 1-1c0-1 1-2 2-3-1 1-2 1-3 2h-1z" class="a"></path><path d="M361 216c-2 0-4 1-6 2 2-3 5-6 10-7h1l-2 3h3l-6 2z" class="N"></path><path d="M347 213v2c3-2 5-3 8-4-1 1-3 3-5 4-5 3-8 7-13 11-1 2-3 3-5 5h-5l12-12c3-3 5-5 8-7v1z" class="Y"></path><defs><linearGradient id="Bq" x1="340.615" y1="216.329" x2="336.93" y2="210.041" xlink:href="#B"><stop offset="0" stop-color="#acabac"></stop><stop offset="1" stop-color="#d3d2d2"></stop></linearGradient></defs><path fill="url(#Bq)" d="M356 200l6-1h0c-3 2-6 3-8 6v1c-2 0-3 1-4 2h2l-5 4c-3 2-5 4-8 7l-12 12c-3 0-4 0-6-2-1 1-3 2-4 2l-1-1c1-3 4-6 7-8 6-7 13-13 21-18h3l1-1c3 0 6-2 8-3z"></path><path d="M350 208h2l-5 4c-3 2-5 4-8 7-2 0-3 1-5 2h0l1-2c-1 1-2 1-3 1l-1 1c6-6 12-10 19-13z" class="Q"></path><path d="M331 221l1-1c1 0 2 0 3-1l-1 2h0c2-1 3-2 5-2l-12 12c-3 0-4 0-6-2h0c3-3 7-6 10-8z" class="Z"></path><path d="M357 193h0l4-2c5 1 13 0 17 3h-2l-2-1h-2-1l1 1h0c2 0-1 0 2 0h1l1 2c2 1 4 2 5 3 1 0 1 1 1 1-1 0-2-1-3-2-1 0-1 1-2 0h0l-1 2c-3-1-5-1-8-3h-3l-5 1h-1c-1 1-2 1-3 2-2 1-5 3-8 3l-1 1h-3c-8 5-15 11-21 18-3 2-6 5-7 8l1 1c1 1 6 0 7 1h-11 0 1c-1-1-2-1-3-1l-1 1c0-1-1-1-1-1v-1c0-2 4-7 6-8 3-4 6-7 9-10 5-4 9-8 15-12l5-4h2l11-3h1-1z" class="h"></path><path d="M346 196v1h0c-1 1-4 4-6 4-5 3-22 21-25 21 3-4 6-7 9-10 5-4 9-8 15-12l5-4h2z" class="Y"></path><defs><linearGradient id="Br" x1="377.347" y1="196.25" x2="369.213" y2="197.243" xlink:href="#B"><stop offset="0" stop-color="#6a6465"></stop><stop offset="1" stop-color="#828283"></stop></linearGradient></defs><path fill="url(#Br)" d="M344 204h0c7-4 15-9 23-9 3-1 6 0 9 1 2 1 4 2 5 3 1 0 1 1 1 1-1 0-2-1-3-2-1 0-1 1-2 0h0l-1 2c-3-1-5-1-8-3h-3l-5 1h-1c-1 1-2 1-3 2-2 1-5 3-8 3l-1 1h-3z"></path><defs><linearGradient id="Bs" x1="385.664" y1="202.179" x2="369.044" y2="181.451" xlink:href="#B"><stop offset="0" stop-color="#7b797b"></stop><stop offset="1" stop-color="#a7a6a6"></stop></linearGradient></defs><path fill="url(#Bs)" d="M372 184c4-1 8-1 12-1 4 2 8 4 11 8 1 1 3 2 3 4 0 1-1 4 0 5-2 3-5 4-8 5h-6l-2-5s0-1-1-1c-1-1-3-2-5-3l-1-2h-1-2 0l-1-1h1 2l2 1h2c-4-3-12-2-17-3l-4 2h0 1-1l-11 3h-2 0c4-2 8-6 13-7l4-2c4-1 7-2 11-3z"></path><path d="M372 184c4-1 8-1 12-1 4 2 8 4 11 8 0 3 1 6 0 8l-2 2c-1-1-1-1-1-2l1-1c1-2 0-4 0-6-1-2-5-4-7-5-5-2-9-3-14-3z" class="V"></path><path d="M358 190c8-3 20-3 28 1l1 1h-3-1c-2 0-3-1-4-1l1-1c-1 0-1 0-2-1h0c-4 0-8-1-11 1h0-1c-2 1-3 1-5 1l-4 2h0 1-1l-11 3h-2 0c4-2 8-6 13-7l1 1z" class="b"></path><path d="M344 196c4-2 8-6 13-7l1 1c-3 1-5 2-7 3l-4 2c4-1 7-2 10-2h1-1l-11 3h-2 0zm17-5c2 0 3 0 5-1h1 0c3-2 7-1 11-1h0c1 1 1 1 2 1l-1 1c1 0 2 1 4 1h1 3c2 2 3 5 5 7 0 1 0 1 1 2l2-2c1-2 0-5 0-8 1 1 3 2 3 4 0 1-1 4 0 5-2 3-5 4-8 5h-6l-2-5s0-1-1-1c-1-1-3-2-5-3l-1-2h-1-2 0l-1-1h1 2l2 1h2c-4-3-12-2-17-3z" class="s"></path><defs><linearGradient id="Bt" x1="319.758" y1="570.353" x2="288.714" y2="603.084" xlink:href="#B"><stop offset="0" stop-color="#595256"></stop><stop offset="1" stop-color="#818081"></stop></linearGradient></defs><path fill="url(#Bt)" d="M273 529l3-4c4 8 9 16 15 22l8 9c2 0 3 2 5 3v-1c-1-2-1-2-2-3h-1l1-2 1 1h2c2 2 5 5 7 5 1 1 2 1 3 1l-2-2c0-1-2-2-3-3l1-1 2 1v1-1l18 12v1l8 4 1 2v1c2 1 4 2 5 4h-3l8 7c-2 1-2 0-3 1h-1l-2-1c1 3 1 5 2 8l-1 1c2 2 5 5 6 7 4 6 8 10 11 16l2 2v3l7 5c4 2 7 5 10 8 1 1 2 2 3 2l3 6 3 8-1 1h0c-2 1-2 1-3 3l-2 1c-1 1-1 1-3 1l-2-1c-1 4-3 5-6 8l-6 1v2c-3 0-8 0-11 2h0c-2-1-5-1-7-2-2 0-4 0-6-1l1 1 6 3-3 3-22-10c-2 0-4-2-6-3h-1l-2 1-11-6v-2c-2-1-3-3-4-4-2 1-3 1-5 2l-1 1 2 3-1 2c0-1 0-1-2-1h0l-1 1c-2-5-6-7-10-10h-1l-3-2-1 1c-1-1-3-2-4-2v-1c-1-1-1-3-1-5-2-3-3-5-5-7l-2-6c0-2 0-4 1-6h4c-1-1-2-3-2-4-1-1-2-2-2-3 0-2 0-3 1-4-1-1 0-1-1-2v-3c0-1-3-6-3-7 1-3 0-7 0-10-2-4-3-10-4-14 0-3-1-6 0-8l1-1c-1-4 0-7 0-10s1-5 2-7l2-2v1h2v2c1-1 2-2 4-2l1 1c1-2 1-4 2-6 0-2 0-4-1-6-1-1-1-2-3-2 1-2 2-3 3-4z"></path><path d="M270 582c1 1 1 4 2 6h0v-2-1c-1-1-1-2-1-3v-1c0-3 0-5 1-7 0 4 0 8 1 12s3 9 4 13c1 2 4 5 4 7-5-6-9-13-11-21v-3z" class="K"></path><path d="M274 562c3-2 5-3 8-4 3 1 5 1 7 1 2 2 4 1 6 2l-1 1h2v1c-5-1-12-2-17 1-4 2-6 6-7 10-1 2-1 4-1 7v1c0 1 0 2 1 3v1 2h0c-1-2-1-5-2-6l-1-1v-8h0c1-5 3-8 5-11z" class="b"></path><path d="M282 558c17-2 44 14 57 24l5 4c1 3 1 5 2 8l-1 1v2l-3 1c0-1 0-3 1-4v-1l-2-2c-2-2-4-4-7-6 0 0-1-1-2-1-3 0-8-6-12-9-7-5-15-9-24-12v-1h-2l1-1c-2-1-4 0-6-2-2 0-4 0-7-1z" class="u"></path><defs><linearGradient id="Bu" x1="313.079" y1="576.565" x2="318.311" y2="556.994" xlink:href="#B"><stop offset="0" stop-color="#b7b6b6"></stop><stop offset="1" stop-color="#edecec"></stop></linearGradient></defs><path fill="url(#Bu)" d="M299 556c2 0 3 2 5 3v-1c-1-2-1-2-2-3h-1l1-2 1 1h2c2 2 5 5 7 5 1 1 2 1 3 1l-2-2c0-1-2-2-3-3l1-1 2 1v1-1l18 12v1l8 4 1 2v1c2 1 4 2 5 4h-3l8 7c-2 1-2 0-3 1h-1l-2-1-5-4c-13-10-40-26-57-24-3 1-5 2-8 4h0v-1c2-1 3-2 6-3h0l-3-1c-1 1-1 1-2 1 3-2 6-3 9-3s6-1 9 0c2 0 4 1 6 1z"></path><path d="M277 557c-1 1-1 1-2 1 3-2 6-3 9-3 2 1 2 1 4 1v1c-1 0-2 0-3-1-2 0-5 1-7 1h-1z" class="H"></path><path d="M331 568l8 4 1 2v1c2 1 4 2 5 4h-3l-9-7c0-1-2-3-2-4z" class="U"></path><path d="M313 558c0-1-2-2-3-3l1-1 2 1v1-1l18 12v1c0 1 2 3 2 4-2-1-4-2-5-3l-13-9-2-2z" class="N"></path><path d="M273 529l3-4c4 8 9 16 15 22l8 9c-2 0-4-1-6-1-3-1-6 0-9 0s-6 1-9 3c1 0 1 0 2-1l3 1h0c-3 1-4 2-6 3v1h0c-2 3-4 6-5 11h0v8h-1c-1-3-1-6-1-9 0-2 2-8 1-11l-1 1-1 4h0c-1-2-1-3 0-5v-2c-2-2-1-6-1-8l2-3c1-1 2-2 4-2l1 1c1-2 1-4 2-6 0-2 0-4-1-6-1-1-1-2-3-2 1-2 2-3 3-4z" class="E"></path><path d="M270 533c1-2 2-3 3-4 1 1 2 2 2 3 2 4 5 7 7 10h0c-2-1-4-2-5-3-1 1-1 0-1 2-1 1 1 4 0 6-1 1-1 3 0 4-1 2-2 4-4 4-1 1-1 1-2 1 0 1-1 1-2 2 0-1 1-2 2-3s3-3 4-5c1-3 1-6 0-9 0-2 0-4-1-6-1-1-1-2-3-2z" class="R"></path><path d="M270 533c1-2 2-3 3-4 1 1 2 2 2 3h-1c-1 1-1 2-1 3-1-1-1-2-3-2z" class="F"></path><path d="M272 547c1-2 1-4 2-6 1 3 1 6 0 9-1 2-3 4-4 5s-2 2-2 3c-1 1-1 2-2 3h0v-2c-2-2-1-6-1-8l2-3c1-1 2-2 4-2l1 1z" class="g"></path><path d="M271 546l1 1c-1 2-3 7-5 8-1-1-2-2-2-4l2-3c1-1 2-2 4-2z" class="K"></path><path d="M273 529l3-4c4 8 9 16 15 22v1h-1l-1-1c-1 0-2-1-3-1s-3-2-4-4c-2-3-5-6-7-10 0-1-1-2-2-3z" class="i"></path><defs><linearGradient id="Bv" x1="292.788" y1="573.585" x2="284.276" y2="617.62" xlink:href="#B"><stop offset="0" stop-color="#767476"></stop><stop offset="1" stop-color="#cac9c9"></stop></linearGradient></defs><path fill="url(#Bv)" d="M280 600h0c-2-4-2-15-1-19 2-3-1-6 1-9h2l1 1c3 0 5 3 7 3h1c1 1 1 0 1 1v1s3 1 3 2c2 1 3 2 5 3 1 1 3 1 4 2 0 2 1 3 0 5 0 1 0 1-1 2v3s0 1-1 2h1c0 1 1 0 2 0 1 1 1 1 2 3l-2-1c-1 0-1 0-2 1v1l-1-1c-1 1 0 1-1 2 0-2-1-2-2-3v1l-1-2h0l-1-1h0c-2-1-3-2-4-4-1 1-1 1-1 2-1 5-2 8-1 13v1c1 1 1 2 2 3l5 14v2c-2-3-6-7-8-10-1-2-3-4-4-5 0-3-2-4-3-6 0-3-1-5-3-7z"></path><path d="M280 600c4 3 5 7 7 10 1 1 1 2 1 3 1 1 1 2 2 3v2c-1-2-3-4-4-5 0-3-2-4-3-6 0-3-1-5-3-7z" class="R"></path><path d="M265 545v1h2v2l-2 3c0 2-1 6 1 8v2c-1 2-1 3 0 5h0l1-4 1-1c1 3-1 9-1 11 0 3 0 6 1 9h1l1 1v3c2 8 6 15 11 21 1 2 3 5 5 7 1 1 3 3 4 5 2 3 6 7 8 10v-2l-5-14c-1-2-1-4-1-6-1-2 0-5 0-8 1 1 1 1 1 2l1 1v2c0 2 0 3 1 5l1 1c0 2 1 4 1 6 1 3 1 6 2 8 3 6 8 11 13 14v1l12 9 10 7c2 2 4 3 6 6l-1 2c-5 0-10-2-14-4-1-2-7-5-9-6-4-3-9-6-12-9a57.31 57.31 0 0 1-11-11h0l-2-2-2-4c-1-1-2-3-3-4v-1c-1-1-1-2-2-4s-3-4-4-6c-1-4-4-6-6-9-2-2-4-5-5-8-1-1-2-2-2-4-1-1-1-2-2-3l-1-1v1c-2-4-3-10-4-14 0-3-1-6 0-8l1-1c-1-4 0-7 0-10s1-5 2-7l2-2z" class="c"></path><path d="M268 581h1l1 1v3h-2v-4zm-4 6c-2-4-3-10-4-14 0-3-1-6 0-8 0 2 0 4 1 6v4h0v-2h2c-1-2-1-2-1-3v-4l1 1h0c-1 2 0 5 0 7l1 12v1z" class="L"></path><path d="M265 545v1h2v2l-2 3c0 2-1 6 1 8v2c-1 2-1 3 0 5h0c0 1-1 5-1 6-1-2-1-6-1-8 1-1 1-1 1-2l-1-2v-1l-1 1s0 1-1 2v2h-1c-1-4 0-7 0-10s1-5 2-7l2-2z" class="p"></path><path d="M304 585c2 1 4 1 6 2v1c3 2 6 5 9 7 1 1 3 3 3 5h0l-3 1c-2 1-3 2-3 4v2 1l3 4c0 1 0 2 1 2h0c2-1 3-1 4-1h1-1l-4 3v1l-2-1-1 1h0c1 1 1 0 2 2h0c-1 1-1 3-1 4l1 1h0v1c0 6 1 10 5 14 0 3 2 5 4 7h1l-1 1h0c-2-1-3 0-4 0l-12-9v-1c-5-3-10-8-13-14-1-2-1-5-2-8 0-2-1-4-1-6l-1-1c-1-2-1-3-1-5v-2l-1-1c0-1 0-1-1-2 0 3-1 6 0 8 0 2 0 4 1 6-1-1-1-2-2-3v-1c-1-5 0-8 1-13 0-1 0-1 1-2 1 2 2 3 4 4h0l1 1h0l1 2v-1c1 1 2 1 2 3 1-1 0-1 1-2l1 1v-1c1-1 1-1 2-1l2 1c-1-2-1-2-2-3-1 0-2 1-2 0h-1c1-1 1-2 1-2v-3c1-1 1-1 1-2 1-2 0-3 0-5z" class="E"></path><path d="M301 602c1-1 0-1 1-2l1 1c0 2-1 4 0 6 0 2 1 3 1 4-2-1-2-3-3-5v-4z" class="B"></path><path d="M292 595c0-1 0-1 1-2 1 2 2 3 4 4h0l1 1h0l1 2v-1c1 1 2 1 2 3v4c-1-1-1-2-2-3 0-1 0-2-1-3h-1v-1c-1 0-1 0-2-1v-1l-1 1h0c-1-2-1-2-2-3z" class="D"></path><defs><linearGradient id="Bw" x1="322.616" y1="609.841" x2="314.611" y2="642.728" xlink:href="#B"><stop offset="0" stop-color="#b0afb0"></stop><stop offset="1" stop-color="#e2e3e1"></stop></linearGradient></defs><path fill="url(#Bw)" d="M312 620c1-3-1-5-2-8v-3-1h0 6l3 4c0 1 0 2 1 2h0c2-1 3-1 4-1h1-1l-4 3v1l-2-1-1 1h0c1 1 1 0 2 2h0c-1 1-1 3-1 4l1 1h0v1c0 6 1 10 5 14 0 3 2 5 4 7h-1s-1 0-1-1h-1-1c-4-2-7-8-9-12-1-3 0-6 0-9l-1 2v-1l-1-1v-1c0-1-1-2-1-3z"></path><defs><linearGradient id="Bx" x1="314.318" y1="591.128" x2="305.618" y2="605.079" xlink:href="#B"><stop offset="0" stop-color="#807e80"></stop><stop offset="1" stop-color="#ababaa"></stop></linearGradient></defs><path fill="url(#Bx)" d="M304 585c2 1 4 1 6 2v1c3 2 6 5 9 7 1 1 3 3 3 5h0l-3 1c-2 1-3 2-3 4v2 1h-6 0v1 3c1 3 3 5 2 8-2-6-3-13-5-20-1-2-1-2-2-3-1 0-2 1-2 0h-1c1-1 1-2 1-2v-3c1-1 1-1 1-2 1-2 0-3 0-5z"></path><path d="M319 595c1 1 3 3 3 5h0l-3 1 1-2c-1 0-1 0-1-1-1-1-1-2 0-3z" class="Q"></path><defs><linearGradient id="By" x1="310.914" y1="625.445" x2="292.665" y2="640.502" xlink:href="#B"><stop offset="0" stop-color="#c5c4c4"></stop><stop offset="1" stop-color="#efeeed"></stop></linearGradient></defs><path fill="url(#By)" d="M264 586l1 1c1 1 1 2 2 3 0 2 1 3 2 4 1 3 3 6 5 8 2 3 5 5 6 9 1 2 3 4 4 6s1 3 2 4v1c1 1 2 3 3 4l2 4 2 2h0a57.31 57.31 0 0 0 11 11c3 3 8 6 12 9 2 1 8 4 9 6 4 2 9 4 14 4l1-2 2 2c1 3 4 5 7 6-2 0-4 0-6-1l1 1 6 3-3 3-22-10c-2 0-4-2-6-3h-1l-2 1-11-6v-2c-2-1-3-3-4-4-2 1-3 1-5 2l-1 1 2 3-1 2c0-1 0-1-2-1h0l-1 1c-2-5-6-7-10-10h-1l-3-2-1 1c-1-1-3-2-4-2v-1c-1-1-1-3-1-5-2-3-3-5-5-7l-2-6c0-2 0-4 1-6h4c-1-1-2-3-2-4-1-1-2-2-2-3 0-2 0-3 1-4-1-1 0-1-1-2v-3c0-1-3-6-3-7 1-3 0-7 0-10v-1z"></path><path d="M318 660c3 0 5 1 6 3l1 1c-2 0-4-2-6-3l-1-1z" class="H"></path><path d="M305 650l13 10 1 1h-1c-4-2-7-4-11-7 0-1-1-3-2-4z" class="G"></path><path d="M305 654h2c4 3 7 5 11 7l-2 1-11-6v-2z" class="C"></path><path d="M325 658c4 2 9 4 14 4l1-2 2 2c1 3 4 5 7 6-2 0-4 0-6-1s-4-1-6-2c-4-3-9-4-12-7z" class="R"></path><path d="M287 635c1 1 2 1 3 2 4 5 10 9 15 13 1 1 2 3 2 4h-2c-2-1-3-3-4-4-2-2-4-4-7-5-2-3-5-5-7-8v-2z" class="j"></path><path d="M285 636l2 1c2 3 5 5 7 8 3 1 5 3 7 5-2 1-3 1-5 2l-1 1-1-1c-2-3-5-6-7-10 0-2-2-4-2-6z" class="Z"></path><path d="M294 645c3 1 5 3 7 5-2 1-3 1-5 2l-1 1-1-1 1-1-1-1v-1c1 1 1 1 2 0 0-1-1-3-2-4z" class="I"></path><path d="M267 604c1 2 2 4 4 4l2 1c1 1 1 1 2 3v1c1 1 0 1 0 2 1 2 3 4 4 7 2 3 3 6 6 9v-1c2 2 4 4 5 7-1-1-2-1-3-2v2l-2-1c0 2 2 4 2 6-5-6-10-12-13-19l-2-1-1-2c-1-1-2-3-2-4-1-1-2-2-2-3 0-2 0-3 1-4-1-1 0-1-1-2v-3z" class="n"></path><path d="M279 628c1-1 1-1 2 0 2 1 4 5 6 7v2l-2-1c-3-3-4-5-6-8z" class="W"></path><path d="M267 604c1 2 2 4 4 4l2 1-3 1c3 6 5 12 9 18 2 3 3 5 6 8 0 2 2 4 2 6-5-6-10-12-13-19l-2-1-1-2c-1-1-2-3-2-4-1-1-2-2-2-3 0-2 0-3 1-4-1-1 0-1-1-2v-3z" class="Y"></path><path d="M268 609c2 3 3 7 5 10 0 2 1 3 1 4l-2-1-1-2c-1-1-2-3-2-4-1-1-2-2-2-3 0-2 0-3 1-4z" class="i"></path><path d="M271 620l1 2 2 1c3 7 8 13 13 19 2 4 5 7 7 10l1 1 2 3-1 2c0-1 0-1-2-1h0l-1 1c-2-5-6-7-10-10h-1l-3-2-1 1c-1-1-3-2-4-2v-1c-1-1-1-3-1-5-2-3-3-5-5-7l-2-6c0-2 0-4 1-6h4z" class="E"></path><path d="M271 620l1 2c-2 1-2 1-4 0-1 1-1 1-1 2l1 1-1 1v1c1 0 2 2 3 3v3l3 3c1 1 1 3 2 4h0c3 1 5 3 7 5l1 2v1h-1l-3-2-1 1c-1-1-3-2-4-2v-1c-1-1-1-3-1-5-2-3-3-5-5-7l-2-6c0-2 0-4 1-6h4z" class="S"></path><path d="M273 639l3 3c1 2 4 4 7 5v1h-1l-3-2-1 1c-1-1-3-2-4-2v-1c-1-1-1-3-1-5z" class="B"></path><defs><linearGradient id="Bz" x1="377.247" y1="628.362" x2="352.587" y2="643.082" xlink:href="#B"><stop offset="0" stop-color="#2b1412"></stop><stop offset="1" stop-color="#503f3f"></stop></linearGradient></defs><path fill="url(#Bz)" d="M345 595c2 2 5 5 6 7 4 6 8 10 11 16l2 2v3l7 5c4 2 7 5 10 8 1 1 2 2 3 2l3 6 3 8-1 1h0c-2 1-2 1-3 3l-2 1c-1 1-1 1-3 1l-2-1c-1 4-3 5-6 8l-6 1v2c-3 0-8 0-11 2h0c-2-1-5-1-7-2-3-1-6-3-7-6l-2-2c-2-3-4-4-6-6l-10-7c1 0 2-1 4 0h0l1-1h-1c-2-2-4-4-4-7-4-4-5-8-5-14v-1h0l-1-1c0-1 0-3 1-4h0c-1-2-1-1-2-2h0l1-1 2 1v-1l4-3h1-1c-1 0-2 0-4 1h0c-1 0-1-1-1-2l-3-4v-1-2c0-2 1-3 3-4l3-1h2l1 1c5-1 12 0 17-3l3-1v-2z"></path><path d="M335 620c5 1 9 4 12 8 0 1 1 1 1 2l-4-1h-4l1-1h0v-2c-3-3-5-4-9-4 1-1 2-2 3-2z" class="U"></path><defs><linearGradient id="CA" x1="379.691" y1="641.82" x2="372.767" y2="648.255" xlink:href="#B"><stop offset="0" stop-color="#362626"></stop><stop offset="1" stop-color="#4e4c4c"></stop></linearGradient></defs><path fill="url(#CA)" d="M369 633c2 0 3 0 4 1 5 3 7 9 12 13h1 0c1 0 0 0 1-1v-2l3 8-1 1h0c-2 1-2 1-3 3l-2 1c-1 1-1 1-3 1l-2-1c-1-9-5-17-10-24z"></path><path d="M384 657v-1l-1-1c1-2 1-3 1-5h3c1 1 2 2 2 3h0c-2 1-2 1-3 3l-2 1z" class="K"></path><defs><linearGradient id="CB" x1="367.08" y1="631.061" x2="363.57" y2="664.243" xlink:href="#B"><stop offset="0" stop-color="#8f8f91"></stop><stop offset="1" stop-color="#b1b1b1"></stop></linearGradient></defs><path fill="url(#CB)" d="M356 637c0 1 1 3 2 4 0 1 0 3-1 5 0 3 0 6 1 8 2 3 4 6 7 7 2 1 4 1 6 0 1-1 1-3 2-4 0-5-2-11-4-16-2-3-4-7-3-11h1l2 3c5 7 9 15 10 24-1 4-3 5-6 8l-6 1c-3-1-5-2-8-4l-1-1c-5-7-4-12-3-20-1-2-1-3-1-5l1 1h1z"></path><defs><linearGradient id="CC" x1="350.871" y1="671.546" x2="342.226" y2="642.775" xlink:href="#B"><stop offset="0" stop-color="#949394"></stop><stop offset="1" stop-color="#c2c2c2"></stop></linearGradient></defs><path fill="url(#CC)" d="M332 622c4 0 6 1 9 4v2h0l-1 1h4l4 1c2 1 6 5 8 7h-1l-1-1c0 2 0 3 1 5-1 8-2 13 3 20l1 1c3 2 5 3 8 4v2c-3 0-8 0-11 2h0c-2-1-5-1-7-2-3-1-6-3-7-6l-2-2c-2-3-4-4-6-6l-10-7c1 0 2-1 4 0h0l1-1h-1c-2-2-4-4-4-7l3 2 3 4h1c0-1-1-2-1-3l-1-1c-1-4-1-7 0-12l1-2h-1l3-4v-1z"></path><path d="M331 645c3 2 5 3 8 4v1h-1c-1 0-2-1-3-1 1 2 2 2 4 3 0 0 1 0 1 1-1 0-2-1-3-1l-4-2c-2-1-2-2-3-5h1z" class="B"></path><path d="M344 629l4 1c2 1 6 5 8 7h-1l-1-1c0 2 0 3 1 5-1 8-2 13 3 20l1 1v1c-3 1-9-6-11-7-2-2-6-2-8-3 0-1-1-1-1-1-2-1-3-1-4-3 1 0 2 1 3 1h1c2 0 4-1 6-2 4-2 4-5 5-9-1-5-2-7-6-10z" class="I"></path><path d="M332 622c4 0 6 1 9 4v2h0l-1 1h4c4 3 5 5 6 10-1 4-1 7-5 9-2 1-4 2-6 2v-1c-3-1-5-2-8-4 0-1-1-2-1-3l-1-1c-1-4-1-7 0-12l1-2h-1l3-4v-1z" class="G"></path><path d="M336 636c1 3 3 6 6 7 0 1 1 1 2 1h0c-1 1-2 1-3 1s-2 0-3-1c0-1-1-2-2-3s0-3 0-5z" class="g"></path><path d="M330 627h3c1 0 2 1 3 2v3h-1v3h1v1c0 2-1 4 0 5s2 2 2 3c-1 0-2-2-3-3s-1-4-1-5v-1c0-2 0-4-1-6h-4l1-2z" class="Q"></path><path d="M332 622c4 0 6 1 9 4v2h0l-1 1c0 1 0 2-1 2-2 1-2 3-3 4h-1v-3h1v-3c-1-1-2-2-3-2h-3-1l3-4v-1z" class="N"></path><path d="M345 595c2 2 5 5 6 7 4 6 8 10 11 16l2 2v3c-5-2-10-3-14-4-4 1-6-1-10-1-1 0-3 0-4 1l-1 1c-1 0-2 1-3 2v1l-3 4h1l-1 2c-1 5-1 8 0 12l1 1c0 1 1 2 1 3h-1l-3-4-3-2c-4-4-5-8-5-14v-1h0l-1-1c0-1 0-3 1-4h0c-1-2-1-1-2-2h0l1-1 2 1v-1l4-3h1-1c-1 0-2 0-4 1h0c-1 0-1-1-1-2l-3-4v-1-2c0-2 1-3 3-4l3-1h2l1 1c5-1 12 0 17-3l3-1v-2z" class="o"></path><path d="M327 610c5-1 10-1 15 2h-1c-2 0-4 0-6-1h-2c-3 0-4 0-6-1z" class="I"></path><path d="M319 612c1 0 1-1 2-1 2 0 4 0 6-1 2 1 3 1 6 1h2c-4 0-7 1-10 2h-1c-1 0-2 0-4 1h0c-1 0-1-1-1-2z" class="g"></path><path d="M326 605c3-2 7-2 10-2v1c2 0 3 1 5 1 0 0 2 1 3 1l1 3v1c-7-3-12-5-19-5z" class="E"></path><path d="M342 598l3-1v4l-2 1c0 2-1 2-2 3-2 0-3-1-5-1v-1c-3 0-7 0-10 2-3 0-5 1-8 3h0-1v-2c2-3 5-4 8-5 5-1 12 0 17-3z" class="p"></path><path d="M336 603c3 0 5 0 7-1 0 2-1 2-2 3-2 0-3-1-5-1v-1z" class="B"></path><path d="M345 595c2 2 5 5 6 7 4 6 8 10 11 16l-4-2c0 1 1 1 0 2h-1c-1-1-1-1-2-1l-3-3-7-4v-1l-1-3c-1 0-3-1-3-1 1-1 2-1 2-3l2-1v-4-2z" class="e"></path><path d="M352 612l6 4c0 1 1 1 0 2h-1c-1-1-1-1-2-1l-3-3v-2z" class="C"></path><path d="M344 606l3 1 5 5v2l-7-4v-1l-1-3z" class="j"></path><path d="M345 595c2 2 5 5 6 7v2h-1v-2c-1 0-1 0-1 1l-2 2v2l-3-1c-1 0-3-1-3-1 1-1 2-1 2-3l2-1v-4-2z" class="g"></path><defs><linearGradient id="CD" x1="359.358" y1="611.474" x2="322.74" y2="625.474" xlink:href="#B"><stop offset="0" stop-color="#949394"></stop><stop offset="1" stop-color="silver"></stop></linearGradient></defs><path fill="url(#CD)" d="M325 613c3-1 6-2 10-2 2 1 4 1 6 1h1c3 1 5 2 8 3 2 0 3 2 5 2 1 0 1 0 2 1h1c1-1 0-1 0-2l4 2 2 2v3c-5-2-10-3-14-4-4 1-6-1-10-1-1 0-3 0-4 1l-1 1c-1 0-2 1-3 2v1l-3 4h1l-1 2c-1 5-1 8 0 12l1 1c0 1 1 2 1 3h-1l-3-4-3-2c-4-4-5-8-5-14v-1h0l-1-1c0-1 0-3 1-4h0c-1-2-1-1-2-2h0l1-1 2 1v-1l4-3h1z"></path><path d="M333 616c-1-1-1-1-1-2 2 0 2 1 4 2h-1-2z" class="C"></path><path d="M320 623v-1c-1-2-1-2 0-4h4c-2 1-3 3-4 5z" class="S"></path><path d="M336 616c5-1 9 1 14 3-4 1-6-1-10-1-1 0-3 0-4 1h-2l-1-1 2-2h1z" class="b"></path><path d="M333 616h2l-2 2 1 1h2l-1 1c-1 0-2 1-3 2v1l-1-1h-4v1h-4v1l-1 1c-1 1-1 1-1 2v1c-1 1-1 1-1 2h0c0-1 0-3-1-5l1-2c1-2 2-4 4-5h1c1-1 3-2 5-2h3z" class="r"></path><path d="M325 619h3v2h-1l-2 1-1-1 1-2z" class="u"></path><path d="M325 618c1-1 3-2 5-2l1 2-3 3v-2h-3v-1z" class="x"></path><path d="M333 616h2l-2 2 1 1h2l-1 1c-1 0-2 1-3 2v1l-1-1h-4v-1h1l3-3-1-2h3z" class="s"></path><path d="M327 622h4l1 1-3 4h1l-1 2c-1 5-1 8 0 12l1 1c0 1 1 2 1 3h-1l-3-4-3-2c-4-4-5-8-5-14h0c1 2 1 4 1 5h0c0-1 0-1 1-2v-1c0-1 0-1 1-2l1-1v-1h4v-1z" class="V"></path><path d="M327 622h4l1 1-3 4c0-2 1-3 0-4-1 1-2 2-3 4l-1 1h0c0-2 1-4 2-5v-1z" class="Y"></path><path d="M326 627h2v1c-1 4 0 9-1 13l-1-1-2-3c0-3 0-6 1-9l1-1z" class="U"></path><path d="M325 628l1-1c0 2 0 4-1 7 0 1 0 2 1 4v2l-2-3c0-3 0-6 1-9z" class="V"></path><path d="M323 624v-1h4c-1 1-2 3-2 5h0c-1 3-1 6-1 9l2 3 1 1h0l-3-2c-4-4-5-8-5-14h0c1 2 1 4 1 5h0c0-1 0-1 1-2v-1c0-1 0-1 1-2l1-1z" class="o"></path><path d="M323 624v-1h4c-1 1-2 3-2 5h-1-1v-2-1-1z" class="s"></path><path d="M443 169v-1l1 1c2 1 4 0 7 1h2l4 2c2 2 4 3 4 5-1 2-1 3-2 5l-2 1 2 1-2 2c-3 1-4 2-5 5h-4c-1 1-3 2-4 3 4 4 10 9 14 11 3 2 19 15 20 17 0 3 1 4 3 5h1l2 2c1 1 3 1 5 2 0 1 1 2 2 2l-1 1h-2-1v1c3 3 5 7 6 11 1 2 2 5 3 8l6 26 1 1v13 7h0c-1 1-2 3-3 5l2 1c0 2 1 4 3 5 0 1 2 1 3 1l3 3c2 2 1 5 2 8v6l-1 16c-1 5-1 11 0 16l-1 2 1 1v2h1l-1 1c-2 2-2 5-3 8-1 2-2 2-2 4 0 1-1 1-1 2-1 1-1 2-2 2-1-1-2-3-2-5h-1l-1-2c-1-1-2-2-2-4h-2c0-2 0-4-1-6h-1l-1-4v-1c0-3 0-7 1-10h-3l-3-2v1c-1-1-1-1-2-1l-4-6-1 1 1 1c0 1 1 2 1 4l-2-2c-1 0-2-2-3-3l-1-1h-1c0-1 0-1-1-2s-1-1-3-2c-1 0-2 0-3-1h-4v2c0 2-1 4-1 5h-1l-1-2c-1-2-1-5-1-7h-1v11l-1-2-5-9c-1-3-3-5-5-7l-2-1h0c-1-4-2-8-1-12h0c-1-2-3-4-5-5-1-1-1-2-3-2v1c-1 4 0 8 0 12h-1l-1 1c-1-1-3-1-4-1l-5-1c-1 2 0 5-1 8v2 1l-1 1v14 7h-3c0 1 0 2-1 2 0 3-2 5-3 7-1 0-1-1-1-2-3 1-5 2-7 3 0 0-1 1-2 1 0 1 0 1-1 2l-1-1-3 3c0 1 0 1 1 1-3 2-5 2-8 2h-2-1c-5-2-6-6-8-10-1-3-2-7-2-10l-1-2c-1-1-1-2-2-3v1l-3-6-1-4h-1l-3-10h1c0-1 0-1 1-1v-1c5-7 10-14 16-20 7-8 13-15 20-21-1-1-1-2-1-3 0-2 0-2 1-3 0-8 0-16-1-24-1-6-4-14-3-20h0c1-2 2-3 2-5h-4c1 0 1-1 2-1h1c0-1 0-1 1-2l2-2 3-4v-2-1l1-1v-1l3-3 1-1c3-2 6-2 10-2-2 0-5 1-6 0v-1l1-1 6 1c0-1-1-2-2-2-3-1-9 0-12 1h-2l-9 2 2-1-4-1-1-1c4-3 9-5 14-7-3 0-6 1-9 2 1-2 2-4 4-5l2-2 2-1h0l4-1 3-1c0-1 0-3-1-4v-3l-1-1 1-1h1l4-2c6-2 14-4 18-9 1-2 0-2 0-4z" class="k"></path><path d="M427 266h1l3 6v-1h1c1 4 5 8 8 11 2 3 5 4 7 7-9-4-15-12-19-20 0-1 0-2-1-3z" class="T"></path><path d="M434 262c3 1 5 4 7 7 2 2 4 5 6 6l9 5 1 1c-2 0-4-1-6-1h-1c-7-3-13-12-16-18z" class="H"></path><path d="M458 280l7 3 12 6c4 2 8 4 12 5l2 3c-3-1-5-2-7-3-5-2-10-3-15-5-7-2-13-5-18-9 2 0 4 1 6 1l-1-1h2z" class="G"></path><path d="M465 283l12 6h-2c-3 0-8-1-9-3 0-1 0-1-1-2v-1z" class="D"></path><path d="M427 280c1-3 2-5 1-8v-3c4 8 10 16 19 20 4 3 10 5 15 6h6l-1 1c2 2 5-1 8-1h0 7l2 1v1h1v1h-2c-4 0-5 4-8 4 0 2-1 3-1 4h-1l-1 1-1 3c-2 3-1 6 0 9l-2 2c-3-5-6-10-10-15-3-2-5-4-8-6h0l3-3-9-5c-2 0-3-1-5-2v-2c-1-1-3-2-4-2h-3s-1-1-2-1l1-2c-4-1-7-1-10 0-1 1-1 1-2 1 2-1 4-2 6-4h0 1z" class="J"></path><path d="M427 280c1 0 1 0 2-1v1c1 1 1 1 2 1v1c1 1 2 1 2 1l3 3h-3s-1-1-2-1l1-2c-4-1-7-1-10 0-1 1-1 1-2 1 2-1 4-2 6-4h0 1z" class="S"></path><path d="M445 292c4 0 10 3 14 5l6 2-2 3c-3-1-6-3-9-5l-9-5z" class="f"></path><path d="M454 297c3 2 6 4 9 5 0 2 0 3 1 4h1c1 1 1 1 1 2l-1 1-1-1h-1 0c-1-1-2-1-2-2h-2c-3-2-5-4-8-6h0l3-3z" class="E"></path><path d="M467 296c2 2 5-1 8-1h0 7l2 1v1h1v1h-2c-4 0-5 4-8 4 0 2-1 3-1 4h-1l-1 1-1 3c-2 3-1 6 0 9l-2 2c-3-5-6-10-10-15h2c0 1 1 1 2 2h0 1l1 1 1-1c0-1 0-1-1-2h-1c-1-1-1-2-1-4l2-3 2-3z" class="H"></path><path d="M468 303v3l1-1c1-1 3-1 4-2h1l1-1c0 2-1 3-1 4h-1l-1 1h-2-1l-1 1-1-1c-1-2 0-2 1-4z" class="L"></path><path d="M465 306l1-2c0-1 1-2 2-3l1 1-1 1c-1 2-2 2-1 4l1 1 1-1h1 2l-1 3c-2 3-1 6 0 9l-2 2c-3-5-6-10-10-15h2c0 1 1 1 2 2h0 1l1 1 1-1c0-1 0-1-1-2z" class="D"></path><path d="M423 239c1-3 3-6 4-9 1-2 1-4 2-5h1c-2 8-4 16-2 24h0c-2 5-2 12-1 17 1 1 1 2 1 3v3c1 3 0 5-1 8h-1 0c-2 2-4 3-6 4 1 0 1 0 2-1 3-1 6-1 10 0l-1 2c1 0 2 1 2 1h3c1 0 3 1 4 2v2c2 1 3 2 5 2l9 5-3 3-18-9c-2 0-3 0-4 1h-5l-1 2h-1l-7 1-6 3h-2l-1 1c-2 1-5 2-7 4-7 5-12 11-18 17-1 1-6 7-7 7 5-7 10-14 16-20 7-8 13-15 20-21l3-3c3-2 7-4 9-7 1-2 1-4 0-7 1-1 1-1 0-3-1-7-1-16 0-23 1-1 1-3 1-4z" class="c"></path><path d="M436 286c1 0 3 1 4 2v2c-2-1-4-2-7-3v-1h3z" class="W"></path><path d="M422 283c3-1 6-1 10 0l-1 2c1 0 2 1 2 1v1c-2 1-7 0-9 0 1-1 4-1 6-1v-1l-8-2z" class="p"></path><path d="M407 298c1-1 1-2 2-2 3-1 5-2 7-3 3-1 5-3 7-3 3 0 7 0 10 1-2 0-3 0-4 1h-5l-1 2h-1l-7 1-6 3h-2z" class="n"></path><path d="M415 295h1c2-2 5-2 8-3l-1 2h-1l-7 1z" class="D"></path><path d="M423 239c1-3 3-6 4-9 1-2 1-4 2-5h1c-2 8-4 16-2 24h0c-2 5-2 12-1 17 1 1 1 2 1 3v3c1 3 0 5-1 8h-1c0-4 1-7 1-10 0-2-1-4-1-6v-1c-1-5 0-9 0-13 0-3-1-5 0-8v-1-5c1-1 1-2 1-3v-1c-1 2-2 5-3 7h-1z" class="H"></path><defs><linearGradient id="CE" x1="409.927" y1="250.381" x2="417.988" y2="251.638" xlink:href="#B"><stop offset="0" stop-color="#453737"></stop><stop offset="1" stop-color="#675f60"></stop></linearGradient></defs><path fill="url(#CE)" d="M423 209l6 1c4 0 9 2 13 3l3 2v1l1 1v2c2 1 3 0 4 1l-1 1h-1c-1-1-1-1-2-1l-3-3s-1 1-2 1h-3-1-3v1c-1 1-1 1-1 2v1c-1 0-1 1-1 1l-1-1c0 1-1 2-1 3h-1c-1 1-1 3-2 5-1 3-3 6-4 9 0 1 0 3-1 4-1 7-1 16 0 23 1 2 1 2 0 3 1 3 1 5 0 7-2 3-6 5-9 7l-3 3c-1-1-1-2-1-3 0-2 0-2 1-3 0-8 0-16-1-24-1-6-4-14-3-20h0c1-2 2-3 2-5h-4c1 0 1-1 2-1h1c0-1 0-1 1-2l2-2 3-4v-2-1l1-1v-1l3-3 1-1c3-2 6-2 10-2-2 0-5 1-6 0v-1l1-1z"></path><path d="M409 283c3-1 3-1 4-3v3l-3 3c-1-1-1-2-1-3z" class="r"></path><path d="M413 232l3-2c-1 3-2 5-4 8v4l-1 1v-4c0-2-1-5 0-6h1l1-1z" class="V"></path><path d="M417 237c1-5 2-10 5-14 0-1 1-1 2-1v3c-2 2-3 5-5 8 0 1-1 2-2 4z" class="B"></path><path d="M416 254c0 3 0 8 2 10v5l-2 3h-1v-7-5c0-2 1-4 1-6z" class="f"></path><path d="M410 226c1 0 2 0 3-1s2-2 3-2l1-1h3l-2 1c0 3-1 5-2 7l-3 2-1 1h-1c-1 1 0 4 0 6h-1c-1-3 0-6-2-8h-4c1 0 1-1 2-1h1c0-1 0-1 1-2l2-2z" class="D"></path><path d="M418 223c0 3-1 5-2 7l-3 2v-1c0-3 3-6 5-8z" class="l"></path><path d="M404 231c1 0 1-1 2-1h1c0-1 0-1 1-2h4v1l-1 4c-1 1 0 4 0 6h-1c-1-3 0-6-2-8h-4z" class="b"></path><path d="M418 264v-4h0c1 1 2 3 2 5l2 4c1 3 1 5 0 7-2 3-6 5-9 7v-3l2-2c0-2 1-4 1-6l2-3v-5z" class="a"></path><path d="M418 264v-4h0c1 1 2 3 2 5-1 3-1 5-2 8 0 1 0 2-1 2 0 1-1 2-2 3 0-2 1-4 1-6l2-3v-5z" class="O"></path><path d="M408 231c2 2 1 5 2 8 1 11 6 25 2 37 0 1-1 3-1 4h-1 0c0-8 0-16-1-24-1-6-4-14-3-20h0c1-2 2-3 2-5z" class="S"></path><path d="M424 225c0-1 1-1 1-2s2-1 3-2v-1c-1 1-1 1-2 1 0-1 0-1-1-2h1 4 1 0l-1 1h0 1v2c0 1-1 2-1 3h-1c-1 1-1 3-2 5-1 3-3 6-4 9 0 1 0 3-1 4-1 7-1 16 0 23 1 2 1 2 0 3l-2-4c0-2-1-4-2-5h0v4c-2-2-2-7-2-10-1-6 0-11 1-17 1-2 2-3 2-4 2-3 3-6 5-8z" class="D"></path><path d="M422 266c0-1-1-1-1-2v-1c-2-4-2-12-1-16 1-2 1-4 2-5v1c-1 7-1 16 0 23z" class="B"></path><path d="M423 209l6 1c4 0 9 2 13 3l3 2v1l1 1v2c2 1 3 0 4 1l-1 1h-1c-1-1-1-1-2-1l-3-3s-1 1-2 1h-3-1-3v1c-1 1-1 1-1 2v1c-1 0-1 1-1 1l-1-1v-2h-1 0l1-1h0-1-4-1c1 1 1 1 1 2 1 0 1 0 2-1v1c-1 1-3 1-3 2s-1 1-1 2v-3l-1-1-3 1h0-3l-1 1c-1 0-2 1-3 2s-2 1-3 1l3-4v-2-1l1-1v-1l3-3 1-1c3-2 6-2 10-2-2 0-5 1-6 0v-1l1-1z" class="O"></path><path d="M423 209l6 1c4 0 9 2 13 3l3 2v1l1 1v2c2 1 3 0 4 1l-1 1h-1c-1-1-1-1-2-1l-3-3c-5-4-9-5-15-6-2 0-5 1-6 0v-1l1-1z" class="t"></path><path d="M413 222l2-2c6-5 15-3 23-2h-1-3v1c-1 1-1 1-1 2v1c-1 0-1 1-1 1l-1-1v-2h-1 0l1-1h0-1-4-1c1 1 1 1 1 2 1 0 1 0 2-1v1c-1 1-3 1-3 2s-1 1-1 2v-3l-1-1-3 1h0-3l-1 1c-1 0-2 1-3 2s-2 1-3 1l3-4z" class="C"></path><path d="M424 292h5c1-1 2-1 4-1l18 9h0c3 2 5 4 8 6 4 5 7 10 10 15l1 3 1 1c1 3 2 4 4 6 0 2 1 4 1 6l1 4c1 1 0 2 0 3h-1c0-1 0-1-1-2s-1-1-3-2c-1 0-2 0-3-1h-4v2c0 2-1 4-1 5h-1l-1-2c-1-2-1-5-1-7h-1v11l-1-2-5-9c-1-3-3-5-5-7l-2-1h0c-1-4-2-8-1-12h0c-1-2-3-4-5-5-1-1-1-2-3-2v1c-1 4 0 8 0 12h-1c-1-3-1-8-1-11 0-2 0-3-1-4l-8-2h0-3l-1 1v-3c0-2 0-4 1-6v-1l2-1-1-1h3l-6-1h1l1-2z" class="w"></path><path d="M455 318c4 3 6 8 7 14v12c-1-2-1-5-1-7 0-3-1-8-2-11-1-1-2-2-2-3s0-2-1-2c0-2-1-2-1-3z" class="M"></path><path d="M424 298c4-2 12 3 17 4 2 1 3 4 4 6 1 1 1 2 1 4v1l-1-1c-1-1-2-1-3-2-1 0-3-1-4-1s-1 2-2 3c0-2 0-3-1-4l-8-2h0-3l-1 1v-3c0-2 0-4 1-6z" class="Y"></path><path d="M424 298c4-2 12 3 17 4 2 1 3 4 4 6-6-6-12-7-20-8-2 2-1 3-2 4 0-2 0-4 1-6z" class="o"></path><path d="M424 306c1-2 1-3 2-5 0 1 1 2 1 2 2 0 4-1 6 0s2 0 3 1c4 2 7 3 9 7l1 1v1l-1-1c-1-1-2-1-3-2-1 0-3-1-4-1s-1 2-2 3c0-2 0-3-1-4l-8-2h0-3z" class="U"></path><path d="M461 318l2-1 1 2c2 1 4 4 6 5l1 1c1 3 2 4 4 6 0 2 1 4 1 6l1 4c1 1 0 2 0 3h-1c0-1 0-1-1-2s-1-1-3-2c-1 0-2 0-3-1h-4c0-7 0-14-4-21z" class="J"></path><path d="M473 339c0-3-1-4-3-6l-1-3v-1c2 1 5 6 7 8h0l1 4c1 1 0 2 0 3h-1c0-1 0-1-1-2 0-2 0-3-1-4l-1 1z" class="F"></path><defs><linearGradient id="CF" x1="466.765" y1="319.421" x2="464.667" y2="338.183" xlink:href="#B"><stop offset="0" stop-color="#737071"></stop><stop offset="1" stop-color="#898686"></stop></linearGradient></defs><path fill="url(#CF)" d="M461 318l2-1 1 2h0c1 2 4 6 4 8-1 0-1 0-1 1v4 1l1 1h0c3 1 3 3 5 5l1-1c1 1 1 2 1 4-1-1-1-1-3-2-1 0-2 0-3-1h-4c0-7 0-14-4-21z"></path><path d="M468 334c3 1 3 3 5 5l1-1c1 1 1 2 1 4-1-1-1-1-3-2 0-1-1-1-2-2h0c-1-2-1-3-2-4z" class="B"></path><defs><linearGradient id="CG" x1="456.846" y1="320.559" x2="446.709" y2="336.853" xlink:href="#B"><stop offset="0" stop-color="#7b787a"></stop><stop offset="1" stop-color="#949594"></stop></linearGradient></defs><path fill="url(#CG)" d="M436 312c1-1 1-3 2-3s3 1 4 1c1 1 2 1 3 2l1 1c1 1 2 1 4 2s4 1 5 2v1c0 1 1 1 1 3 1 0 1 1 1 2s1 2 2 3c1 3 2 8 2 11h-1v11l-1-2-5-9c-1-3-3-5-5-7l-2-1h0c-1-4-2-8-1-12h0c-1-2-3-4-5-5-1-1-1-2-3-2v1c-1 4 0 8 0 12h-1c-1-3-1-8-1-11z"></path><path d="M424 292h5c1-1 2-1 4-1l18 9h0c3 2 5 4 8 6 4 5 7 10 10 15l1 3c-2-1-4-4-6-5l-1-2-2 1c-1-1-2-3-3-4l-3-1c-2-1-4-1-6-3h0c1-2-1-5-2-6v-1c-2-1-3-2-5-3-2 0-5-1-7-2l-9-2-1-1h3l-6-1h1l1-2z" class="R"></path><path d="M423 294h0c1 0 2 0 3-1 2-1 5 0 7 1l-2 1h-3l-6-1h1z" class="I"></path><path d="M437 295c4 2 8 3 10 7h0v1c-2-1-3-2-5-3s-4-2-5-3v-2z" class="f"></path><path d="M433 294c2 0 3 1 4 1v2c1 1 3 2 5 3-2 0-5-1-7-2l-9-2-1-1h3 3l2-1z" class="t"></path><path d="M433 294c2 0 3 1 4 1v2l-6-2 2-1z" class="Q"></path><path d="M455 313c3-1 3 0 5 0 0-3-7-9-9-12v-1c3 2 5 4 8 6 4 5 7 10 10 15l1 3c-2-1-4-4-6-5l-1-2-2 1c-1-1-2-3-3-4l-3-1z" class="F"></path><path d="M458 314c1 0 3 0 4 1 0 1 1 2 1 2l-2 1c-1-1-2-3-3-4z" class="i"></path><defs><linearGradient id="CH" x1="388.667" y1="312.551" x2="403.03" y2="329.135" xlink:href="#B"><stop offset="0" stop-color="#341e1d"></stop><stop offset="1" stop-color="#584b4c"></stop></linearGradient></defs><path fill="url(#CH)" d="M422 294l6 1h-3l1 1-2 1v1c-1 2-1 4-1 6v3l1-1h3 0l8 2c1 1 1 2 1 4 0 3 0 8 1 11l-1 1c-1-1-3-1-4-1l-5-1c-1 2 0 5-1 8v2 1l-1 1v14 7h-3c0 1 0 2-1 2 0 3-2 5-3 7-1 0-1-1-1-2-3 1-5 2-7 3 0 0-1 1-2 1 0 1 0 1-1 2l-1-1-3 3c0 1 0 1 1 1-3 2-5 2-8 2h-2-1c-5-2-6-6-8-10-1-3-2-7-2-10l-1-2c-1-1-1-2-2-3v1l-3-6-1-4h-1l-3-10h1c0-1 0-1 1-1v-1c1 0 6-6 7-7 6-6 11-12 18-17 2-2 5-3 7-4l1-1h2l6-3 7-1z"></path><path d="M380 348v-1c0-1-1-1-1-3h1v-3h1c-1 2 0 5 0 7l1 1v2c-1-1-1-2-2-3z" class="q"></path><path d="M381 348l1-1c0-3 0-4 2-7v1l1-1c1-1 2-2 3-2l1-1h1v-1h3c-1 1-2 1-3 2h0c-3 1-5 3-7 6 0 2-1 3-1 5l-1-1z" class="O"></path><path d="M413 299c2-1 5-2 7-3l1 2c-2 2-2 7-2 10v-1c-2-4-4-6-6-8z" class="f"></path><path d="M396 329l2 2 3 4 1 3h0c-1 0-3-1-4-1h0c-1 0-3-1-3-2-2-1-2-2-2-3l2 1v-2c1 0 1-1 1-2z" class="h"></path><path d="M396 329l2 2 3 4c-3 0-4 0-6-2v-2c1 0 1-1 1-2z" class="q"></path><path d="M422 294l6 1h-3l1 1-2 1v1c-1 2-1 4-1 6v3c0 2 0 3-1 5l-1-1c1-4 1-9 0-13l-1-2c-2 1-5 2-7 3h-1v-1h2l1-1h1-1c-2 0-3 1-5 1h-1l6-3 7-1z" class="v"></path><path d="M388 318c2-1 4-3 6-4 0 4 0 10 2 14v1c0 1 0 2-1 2v2l-2-1-7-5c-2-1-3-1-4-3 1-1 2 0 3 0 0-3 1-4 3-7v1z" class="U"></path><path d="M388 318c2-1 4-3 6-4 0 4 0 10 2 14v1c0 1 0 2-1 2l-2-1v-1c-2-4-3-8-5-11z" class="b"></path><defs><linearGradient id="CI" x1="395.844" y1="312.192" x2="405.975" y2="319.037" xlink:href="#B"><stop offset="0" stop-color="#4e4141"></stop><stop offset="1" stop-color="#6e6d70"></stop></linearGradient></defs><path fill="url(#CI)" d="M396 309v-1l6-3c1-1 1-1 3-1l1 1c2 1 2 1 3 3v1c1 1 2 3 3 5l-1 1v-1c0 3 1 6 3 8l-2 3c0 2 1 4 2 5 1 2 0 2 0 3l-1 1v2c-1-3-3-6-5-9-1-2-2-3-5-3-1 1-2 1-3 3v1h-1-1v3l-2-2v-1c-2-4-2-10-2-14h0c0-2 1-4 2-5z"></path><path d="M396 315l1 1h0v10l1 2v3l-2-2v-1c0-5-1-9 0-13z" class="o"></path><path d="M405 314c3 0 4 1 5 3 1 1 1 3 1 4h-1c-1 1 0 1 0 2h-1v-1h0c0-1 0 0-1-1l-3-7z" class="g"></path><path d="M396 309h2c-1 2-2 4-2 6-1 4 0 8 0 13-2-4-2-10-2-14h0c0-2 1-4 2-5z" class="O"></path><path d="M403 308c-1-1-1-1 0-2s1-1 3-1c2 1 2 1 3 3v1c1 1 2 3 3 5l-1 1v-1c0 3 1 6 3 8l-2 3-1-4c0-1 0-3-1-4-1-2-2-3-5-3-1-2-1-4-2-6z" class="e"></path><path d="M403 308l1-2h2l1 3c2 2 2 5 3 8-1-2-2-3-5-3-1-2-1-4-2-6z" class="J"></path><path d="M399 328l1-3c1-2 2-4 4-5 2 1 3 1 5 2h0v1h1c0-1-1-1 0-2h1l1 4c0 2 1 4 2 5 1 2 0 2 0 3l-1 1v2c-1-3-3-6-5-9-1-2-2-3-5-3-1 1-2 1-3 3v1h-1z" class="S"></path><defs><linearGradient id="CJ" x1="405.978" y1="305.447" x2="422.645" y2="349.881" xlink:href="#B"><stop offset="0" stop-color="#a3a3a5"></stop><stop offset="1" stop-color="#cdcbca"></stop></linearGradient></defs><path fill="url(#CJ)" d="M421 298c1 4 1 9 0 13l1 1c1-2 1-3 1-5l1-1h3 0l8 2c1 1 1 2 1 4 0 3 0 8 1 11l-1 1c-1-1-3-1-4-1l-5-1c-1 2 0 5-1 8v2 1l-1 1v14c0-1-1-2-1-3v-2c-1 1-1 1-3 1-2 6-4 10-9 14h0v-1-1c-1 1-2 1-3 1h-1c-1 1-3 1-4 1 1-1 1-1 2-1v-2l2-1 3-3c3-5 3-10 2-15v-2l1-1c0-1 1-1 0-3-1-1-2-3-2-5l2-3v-2c1-1 1-1 1-2v-1c1 0 1-1 2-1 0-2 2-6 2-8 0-3 0-8 2-10z"></path><path d="M417 316c-1 4-3 10-3 14-1-1-2-3-2-5l2-3v-2c1-1 1-1 1-2v-1c1 0 1-1 2-1z" class="O"></path><path d="M421 311l1 1-3 15c0 4 1 8 1 11s0 5-3 7c1-2 1-4 0-6 0-2-1-5 0-7 0-7 3-14 4-21z" class="r"></path><path d="M420 338c0 2 1 4 1 6-2 6-4 10-9 14h0v-1-1c-1 1-2 1-3 1h-1c5-3 7-7 9-12 3-2 3-4 3-7z" class="V"></path><defs><linearGradient id="CK" x1="427.363" y1="313.452" x2="418.923" y2="343.271" xlink:href="#B"><stop offset="0" stop-color="#777677"></stop><stop offset="1" stop-color="#bdbbbb"></stop></linearGradient></defs><path fill="url(#CK)" d="M424 306h3c-1 1-2 2-2 4v3 7c1 4 0 8 1 12v1l-1 1v14c0-1-1-2-1-3v-2c-1 1-1 1-3 1 0-2-1-4-1-6 0-3-1-7-1-11l3-15c1-2 1-3 1-5l1-1z"></path><defs><linearGradient id="CL" x1="432.337" y1="307.994" x2="429.616" y2="320.861" xlink:href="#B"><stop offset="0" stop-color="#848384"></stop><stop offset="1" stop-color="#a2a1a2"></stop></linearGradient></defs><path fill="url(#CL)" d="M427 306h0l8 2c1 1 1 2 1 4 0 3 0 8 1 11l-1 1c-1-1-3-1-4-1l-5-1c-1 2 0 5-1 8v2c-1-4 0-8-1-12v-7-3c0-2 1-3 2-4z"></path><defs><linearGradient id="CM" x1="381.904" y1="352.406" x2="407.028" y2="357.5" xlink:href="#B"><stop offset="0" stop-color="#7c7b7c"></stop><stop offset="1" stop-color="#c7c6c6"></stop></linearGradient></defs><path fill="url(#CM)" d="M400 327c1-2 2-2 3-3 3 0 4 1 5 3 2 3 4 6 5 9 1 5 1 10-2 15l-3 3-2 1v2c-1 0-1 0-2 1 1 0 3 0 4-1h1c1 0 2 0 3-1v1 1h0c5-4 7-8 9-14 2 0 2 0 3-1v2c0 1 1 2 1 3v7h-3c0 1 0 2-1 2 0 3-2 5-3 7-1 0-1-1-1-2-3 1-5 2-7 3 0 0-1 1-2 1 0 1 0 1-1 2l-1-1-3 3c0 1 0 1 1 1-3 2-5 2-8 2h-2-1c-5-2-6-6-8-10-1-3-2-7-2-10l-1-2v-2c0-2 1-3 1-5 2-3 4-5 7-6h0c1-1 2-1 3-2 2 1 3 1 5 1h0c1 0 3 1 4 1h0l-1-3-3-4v-3h1 1v-1z"></path><path d="M394 353c2 3 3 7 7 9v-1l2 1v1l-1 1c-2-1-3-2-4-3l-1-1v1c-1-1-2-2-3-2v-6z" class="g"></path><path d="M394 353l1-4c0 1 1 1 1 3 1 3 3 5 5 8 1 0 1 0 1 1h-1v1c-4-2-5-6-7-9z" class="M"></path><path d="M396 346c1 0 2 1 4 2-1 0-1 1-2 2v1c1 3 2 5 4 7h1 1c1 0 3 0 4-1h1c1 0 2 0 3-1v1 1h0l-5 3c-2 1-3 1-5 0 0-1 0-1-1-1-2-3-4-5-5-8 0-2-1-2-1-3s1-2 1-3z" class="O"></path><path d="M408 357h1c1 0 2 0 3-1v1 1h0l-5 3c-1 0-3-1-4-2v-1h1c1 0 3 0 4-1z" class="o"></path><path d="M393 336c2 1 3 1 5 1h0c1 0 3 1 4 1h0l1 1 1 4-1 2h-1c0 1 0 3-1 4l-2 1v1h-1v-1c1-1 1-2 2-2-2-1-3-2-4-2l1-2c-2-1-2-2-4-2h0v-1c0-1-1-1-2-1 0-1-1-2-1-2h0c1-1 2-1 3-2z" class="I"></path><path d="M393 336c2 1 3 1 5 1h0c1 0 3 1 4 1h0l1 1 1 4-1 2h-1v-1c-1-2-1-2-3-3-3-1-5-3-9-3 1-1 2-1 3-2z" class="U"></path><path d="M398 337c1 0 3 1 4 1h0l1 1 1 4-1 2h-1v-1c0-3-2-5-4-7z" class="K"></path><defs><linearGradient id="CN" x1="405.808" y1="354.227" x2="424.847" y2="352.636" xlink:href="#B"><stop offset="0" stop-color="#a5a3a3"></stop><stop offset="1" stop-color="#c2c0c0"></stop></linearGradient></defs><path fill="url(#CN)" d="M421 344c2 0 2 0 3-1v2c0 1 1 2 1 3v7h-3c0 1 0 2-1 2 0 3-2 5-3 7-1 0-1-1-1-2-3 1-5 2-7 3 0 0-1 1-2 1l-6-2 1-1v-1l-2-1h1c2 1 3 1 5 0l5-3c5-4 7-8 9-14z"></path><path d="M400 327c1-2 2-2 3-3 3 0 4 1 5 3 2 3 4 6 5 9 1 5 1 10-2 15l-3 3-2 1v2c-1 0-1 0-2 1h-1-1c-2-2-3-4-4-7h1v-1l2-1c1-1 1-3 1-4h1l1-2-1-4-1-1-1-3-3-4v-3h1 1v-1z" class="I"></path><path d="M403 339h1c2 2 2 5 1 8l-1 1v-1-4l-1-4z" class="F"></path><path d="M404 343v4c-1 1-1 3-1 5l1 1c2 0 3-1 4-2l-1 3h1l-2 1v2c-1 0-1 0-2 1h-1-1c-2-2-3-4-4-7h1v-1l2-1c1-1 1-3 1-4h1l1-2z" class="O"></path><path d="M398 351h1c2 2 4 5 6 5l1-1v2c-1 0-1 0-2 1h-1-1c-2-2-3-4-4-7z" class="L"></path><path d="M400 327l1 2 3 1 1 1 3 4 2 3h-1l-1 1c0 2 0 3-1 5v1h-1v-6h-2-1l-1-1-1-3-3-4v-3h1 1v-1z" class="Q"></path><path d="M403 332l2-1 3 4c-3-1-3-1-5-3z" class="N"></path><path d="M400 327l1 2 3 1 1 1-2 1h-1v1h-1c0-1-1-3-1-5v-1z" class="l"></path><defs><linearGradient id="CO" x1="411.539" y1="329.947" x2="399.759" y2="346.708" xlink:href="#B"><stop offset="0" stop-color="#211613"></stop><stop offset="1" stop-color="#424244"></stop></linearGradient></defs><path fill="url(#CO)" d="M400 327c1-2 2-2 3-3 3 0 4 1 5 3 2 3 4 6 5 9 1 5 1 10-2 15l-3 3h-1l1-3c0-1 1-2 2-3 1-3 1-7 0-10l-2-3-3-4-1-1-3-1-1-2z"></path><path d="M445 215l7 4c2 1 4 3 6 4l10 9c7 7 12 14 17 22 2 4 3 7 5 11l2 7 1 2h0c1 1 1 1 1 2 1-1 0-4 0-5-1-1-1-2-1-4 1 3 2 5 2 8 1 3 0 7 3 10 0 3 1 8-1 11-1 1-1 1-2 1h-4l-2-3c-4-1-8-3-12-5l-12-6-7-3h-2l-9-5c-2-1-4-4-6-6-2-3-4-6-7-7l-6-13h0c-2-8 0-16 2-24 0-1 1-2 1-3l1 1s0-1 1-1v-1c0-1 0-1 1-2v-1h3 1 3c1 0 2-1 2-1l3 3c1 0 1 0 2 1h1l1-1c-1-1-2 0-4-1v-2l-1-1v-1z" class="b"></path><path d="M443 267c5 5 9 9 15 13h-2l-9-5c0-2-2-3-3-5h0c0-1-1-2-1-3z" class="R"></path><path d="M449 221l2 1c-4 7-8 12-11 20v1c-1-1-1 0-1-1 2-9 2-15 9-21h1zm44 46c1 3 2 5 2 8 1 3 0 7 3 10 0 3 1 8-1 11-1 1-1 1-2 1h-4l-2-3c1-1 2-1 3-2 3-4 2-13 1-18h0c1 1 1 1 1 2 1-1 0-4 0-5-1-1-1-2-1-4z" class="B"></path><path d="M443 217l3 3c1 0 1 0 2 1-7 6-7 12-9 21 0 1 0 0 1 1 0 1 0 2-1 3 1 2 1 5 0 6l-2 1v-4-1c-1-7 0-24 4-30 1 0 2-1 2-1z" class="r"></path><path d="M437 249v-6l1 3v-3l1 3h0c1 2 1 5 0 6l-2 1v-4z" class="s"></path><path d="M455 231c3 0 6 3 8 5h-3v-1c-1-1-4-1-5-1-2 1-3 1-4 3v1c-2 2-3 3-3 6h0l-1 3 1 2c-1 6 0 11 1 17l1 2v1c-1-1-1-1-2-1-3-4-6-10-6-15 1-9 6-17 13-22z" class="f"></path><path d="M447 247c0 2 0 4-1 7v4-1c-1-4-1-7 1-10z" class="i"></path><path d="M447 247h0l1 2c-1 6 0 11 1 17-2-2-2-5-3-8v-4c1-3 1-5 1-7z" class="J"></path><path d="M455 231l3-3c5 4 11 8 15 13 3 4 6 9 8 14 4 7 8 14 9 22 1 4 0 9-2 11 0 1-1 1-1 2-1 0-3-1-4-1 1-1 2-1 2-1l1-1h0c2-2 1-4 1-6v-1c-2-13-10-28-19-38l-5-6c-2-2-5-5-8-5z" class="o"></path><defs><linearGradient id="CP" x1="462.58" y1="236.442" x2="454.974" y2="248.506" xlink:href="#B"><stop offset="0" stop-color="#7b797a"></stop><stop offset="1" stop-color="#9b9a9a"></stop></linearGradient></defs><path fill="url(#CP)" d="M451 238v-1c1-2 2-2 4-3 1 0 4 0 5 1v1h3l5 6h-1c0 2 0 7 1 9v5l-2-1v-3c-1-1-1-1-1-2h0v3l-1 1-1-1v1c-1-1-2-1-2-2-1 0-1 0-2-1-2-1-4-3-7-3l-1 1-1 1c-1 0-1 0-2-1h0l-1-2 1-3h0c0-3 1-4 3-6z"></path><path d="M448 244h0c0-3 1-4 3-6 1 0 1 1 1 2v1h0c0 2 0 3-1 5v1h1-1s0 1-1 1l1 1-1 1c-1 0-1 0-2-1h0l-1-2 1-3z" class="I"></path><path d="M448 244c1 1 1 2 1 4h1l1 1-1 1c-1 0-1 0-2-1h0l-1-2 1-3z" class="g"></path><path d="M463 254v-1l1 1 1-1v-3h0c0 1 0 1 1 2v3l2 1h0v11 1c1 0 2 1 3 1h1c1 0 1 1 2 1h0v-1h1l1 2v-2h0l6 14c1 1 0 3 1 4v1c-12-4-24-10-33-19v-1h1c4 4 7 6 12 8h1l1 1v-1c0-1-1-1 0-2 0-3-1-7 0-10v-1l-2-9z" class="C"></path><path d="M463 254v-1l1 1 1-1v-3h0c0 1 0 1 1 2v3l2 1h0v11 1l-1 7c-2-4 0-7-1-10l-1 1v-3l-2-9z" class="g"></path><path d="M466 265v-4h1c0 2 0 4 1 6v1l-1 7c-2-4 0-7-1-10z" class="j"></path><path d="M468 268c1 0 2 1 3 1h1c1 0 1 1 2 1h0c2 2 3 4 4 7v1c1 2 2 5 2 7-4-1-10-3-13-7v-3l1-7z" class="G"></path><defs><linearGradient id="CQ" x1="444.72" y1="256.355" x2="468.712" y2="267.054" xlink:href="#B"><stop offset="0" stop-color="#aaa9a9"></stop><stop offset="1" stop-color="#c9c9c8"></stop></linearGradient></defs><path fill="url(#CQ)" d="M451 249l1-1c3 0 5 2 7 3 1 1 1 1 2 1 0 1 1 1 2 2l2 9v1c-1 3 0 7 0 10-1 1 0 1 0 2v1l-1-1h-1c-5-2-8-4-12-8h-1l-1-2c-1-6-2-11-1-17h0c1 1 1 1 2 1l1-1z"></path><defs><linearGradient id="CR" x1="478.911" y1="260.255" x2="468.26" y2="266.059" xlink:href="#B"><stop offset="0" stop-color="#838282"></stop><stop offset="1" stop-color="#b1b0b0"></stop></linearGradient></defs><path fill="url(#CR)" d="M468 256v-5c-1-2-1-7-1-9h1c9 10 17 25 19 38v1c0 2 1 4-1 6h0l-1 1s-1 0-2 1v-1-1c-1-1 0-3-1-4l-6-14h0v2l-1-2h-1v1h0c-1 0-1-1-2-1h-1c-1 0-2-1-3-1v-1-11h0z"></path><defs><linearGradient id="CS" x1="448.61" y1="239.824" x2="428.331" y2="249.413" xlink:href="#B"><stop offset="0" stop-color="#a8a8a9"></stop><stop offset="1" stop-color="#cbcac9"></stop></linearGradient></defs><path fill="url(#CS)" d="M438 218h3c-4 6-5 23-4 30v1 4c1 3 2 6 3 8l3 6c0 1 1 2 1 3h0c1 2 3 3 3 5-2-1-4-4-6-6-2-3-4-6-7-7l-6-13h0c-2-8 0-16 2-24 0-1 1-2 1-3l1 1s0-1 1-1v-1c0-1 0-1 1-2v-1h3 1z"></path><path d="M430 225c0-1 1-2 1-3l1 1-1 2v1l-2 6v5c-1 3 0 9-1 12-2-8 0-16 2-24z" class="B"></path><path d="M443 169v-1l1 1c2 1 4 0 7 1h2l4 2c2 2 4 3 4 5-1 2-1 3-2 5l-2 1 2 1-2 2c-3 1-4 2-5 5h-4c-1 1-3 2-4 3 4 4 10 9 14 11 3 2 19 15 20 17 0 3 1 4 3 5h1l2 2c1 1 3 1 5 2 0 1 1 2 2 2l-1 1h-2-1v1c3 3 5 7 6 11 1 2 2 5 3 8l6 26 1 1v13 7h0c-1 1-2 3-3 5l2 1c0 2 1 4 3 5 0 1 2 1 3 1l3 3c2 2 1 5 2 8v6l-1 16c-1 5-1 11 0 16l-1 2 1 1v2h1l-1 1c-2 2-2 5-3 8-1 2-2 2-2 4 0 1-1 1-1 2-1 1-1 2-2 2-1-1-2-3-2-5h-1l-1-2c-1-1-2-2-2-4h-2c0-2 0-4-1-6h-1l-1-4v-1c0-3 0-7 1-10h-3l-3-2v1c-1-1-1-1-2-1l-4-6-1 1 1 1c0 1 1 2 1 4l-2-2c-1 0-2-2-3-3l-1-1c0-1 1-2 0-3l-1-4c0-2-1-4-1-6-2-2-3-3-4-6l-1-1-1-3 2-2c-1-3-2-6 0-9l1-3 1-1h1c0-1 1-2 1-4 3 0 4-4 8-4h2v-1h-1v-1l-2-1 2-1c2 1 4 2 7 3h4c1 0 1 0 2-1 2-3 1-8 1-11-3-3-2-7-3-10 0-3-1-5-2-8 0 2 0 3 1 4 0 1 1 4 0 5 0-1 0-1-1-2h0l-1-2-2-7c-2-4-3-7-5-11-5-8-10-15-17-22l-10-9c-2-1-4-3-6-4l-7-4-3-2c-4-1-9-3-13-3 0-1-1-2-2-2-3-1-9 0-12 1h-2l-9 2 2-1-4-1-1-1c4-3 9-5 14-7-3 0-6 1-9 2 1-2 2-4 4-5l2-2 2-1h0l4-1 3-1c0-1 0-3-1-4v-3l-1-1 1-1h1l4-2c6-2 14-4 18-9 1-2 0-2 0-4z" class="c"></path><path d="M482 228l5 6v1c0 1 0 1-1 2l-2-3c-2-2-3-3-2-6z" class="p"></path><path d="M487 235c3 3 5 7 6 11-1-1-2-2-2-3l-2-4c-1 3 3 5 2 8l-5-10c1-1 1-1 1-2z" class="H"></path><path d="M482 227l2 2c1 1 3 1 5 2 0 1 1 2 2 2l-1 1h-2-1l-5-6v-1z" class="U"></path><path d="M491 303c1 0 1 0 2 1 2 3 4 5 6 8 1 1 1 2 2 4s4 3 4 6l-1-1c-2-3-4-5-7-8l-4-4c-1-2-2-4-2-6z" class="Z"></path><path d="M494 314c3 3 7 5 9 8 1 2 2 5 3 7s1 5 2 6v2 1c-2-2-2-2-4-2 0-3 0-6-1-8-1-4-2-8-6-10-2-2-2-2-3-4z" class="I"></path><path d="M421 184l4-2 1 1h4v1l-1 1c-1 1-2 2-2 3 2 2 5 2 7 3 3 2 7 3 10 5l-9-2c-1 0-2 0-3-1h-3-8c0-1 0-3-1-4v-3l-1-1 1-1h1z" class="W"></path><path d="M421 184l4-2 1 1h4v1l-1 1c-2 0-7 0-8-1z" class="E"></path><path d="M420 186l2-1c0 1 0 1-1 2h1c1 1 1 1 1 2h2l-1 2h2c2 0 4 1 6 1v1h-3-8c0-1 0-3-1-4v-3z" class="N"></path><path d="M491 311c1 1 3 2 3 3 1 2 1 2 3 4 4 2 5 6 6 10 1 2 1 5 1 8l-1 1h-1c-1-1-2-2-2-3l-3-7c-1-2-2-3-4-5 1-1 1-1 1-2-2-2-2-3-5-3 1-2 2-4 2-6z" class="P"></path><path d="M500 324h1v3c0 1 0 2 1 3v2c0 1-1 1-2 2l-3-7 2 1c-1-1-1-2-1-4h1l1 1v-1z" class="n"></path><path d="M499 328c-1-1-1-2-1-4h1l1 1 1 5h0c-1 0-2-1-2-2z" class="c"></path><path d="M491 311c1 1 3 2 3 3 1 2 1 2 3 4 1 2 2 3 3 6v1l-1-1h-1c0 2 0 3 1 4l-2-1c-1-2-2-3-4-5 1-1 1-1 1-2-2-2-2-3-5-3 1-2 2-4 2-6z" class="T"></path><path d="M444 194c-2-4-5-4-8-7-2-1-2-1-2-3 2-2 6-4 9-5l-3 3c1 1 1 2 3 3 3 1 6 0 9-1 2 0 4-1 5-1l2 1-2 2c-3 1-4 2-5 5h-4c-1 1-3 2-4 3z" class="N"></path><path d="M440 182c1 1 1 2 3 3 3 1 6 0 9-1 2 0 4-1 5-1 0 1 0 2-1 2-3 2-9 5-12 3-2-1-2-2-4-2h-1l1-2h-1v-1l1-1z" class="R"></path><path d="M493 267c-1-5-5-12-7-17-2-2-4-4-4-6-2-5-6-9-8-13h1l4 4v1l1-1c11 14 18 32 18 50-3-3-2-7-3-10 0-3-1-5-2-8z" class="S"></path><path d="M489 317c-1 1-2 3-4 4-1 1-3 1-4 0-3-1-4-3-5-6s-1-7 1-9c1-2 3-4 5-4 2-1 3 0 5 1 3 2 4 5 4 8 0 2-1 4-2 6z" class="u"></path><path d="M472 307l1-1c-1 5 0 10 3 15 1 2 3 3 6 4h3l3-1c-4 4-4 6-4 12-2 0-2 1-3 2 1 1 1 2 0 4 0 1 1 1 1 2l-1 1 1 1c0 1 1 2 1 4l-2-2c-1 0-2-2-3-3l-1-1c0-1 1-2 0-3l-1-4c0-2-1-4-1-6-2-2-3-3-4-6l-1-1-1-3 2-2c-1-3-2-6 0-9l1-3z" class="Y"></path><path d="M479 326c1 1 1 1 1 2l-1 1-1-1c0-1 1-1 1-2z" class="O"></path><path d="M477 330l3-1v3l1 1h0 1l-1 2c-1-1 0-1-1-2-1 0-2-1-3-1v-2z" class="U"></path><path d="M471 319l5 9c-2 0-4-1-5-3l-1-1-1-3 2-2z" class="B"></path><path d="M485 325l3-1c-4 4-4 6-4 12-2 0-2 1-3 2 1 1 1 2 0 4l-2-4 1-1-2-3h0l-1-2c1 0 2 1 3 1 1 1 0 1 1 2l1-2c0-2 1-3 2-5 0-1 0-2 1-3z" class="f"></path><path d="M477 332c1 0 2 1 3 1 1 1 0 1 1 2v2c-1 0-1 0-1-1l-2-2h0l-1-2z" class="y"></path><path d="M471 325c1 2 3 3 5 3 1 1 1 1 1 2v2l1 2h0l2 3-1 1 2 4c0 1 1 1 1 2l-1 1 1 1c0 1 1 2 1 4l-2-2c-1 0-2-2-3-3l-1-1c0-1 1-2 0-3l-1-4c0-2-1-4-1-6-2-2-3-3-4-6z" class="M"></path><path d="M477 337v-3h1 0l2 3-1 1-2-1z" class="Q"></path><path d="M477 337l2 1 2 4c0 1 1 1 1 2l-1 1 1 1c0 1 1 2 1 4l-2-2-1-1c0-1 0-2-1-3 0-1 0-1-1-2 0-2 0-3-1-4v-1z" class="I"></path><defs><linearGradient id="CT" x1="502.27" y1="262.42" x2="444.391" y2="227.175" xlink:href="#B"><stop offset="0" stop-color="#8c8a8b"></stop><stop offset="1" stop-color="#c5c5c3"></stop></linearGradient></defs><path fill="url(#CT)" d="M454 211c4 2 7 4 10 7 6 5 11 11 16 17l-1 1v-1l-4-4h-1c2 4 6 8 8 13 0 2 2 4 4 6 2 5 6 12 7 17 0 2 0 3 1 4 0 1 1 4 0 5 0-1 0-1-1-2h0l-1-2-2-7c-2-4-3-7-5-11-5-8-10-15-17-22l-10-9c-2-1-4-3-6-4l-2-3 3-1c2 1 5 2 7 4 1 1 1 1 2 1h1c-1-1-1-2-2-2h-1v-1l-3-3c-1-1-2-1-3-3h0z"></path><path d="M458 223c2 0 3 0 4 2 1 0 2 1 4 1l2 3v3l-10-9z" class="j"></path><path d="M491 303c-1-1-1-2-1-3h1c2 1 5 1 7 0s4-3 5-6v7h0c-1 1-2 3-3 5l2 1c0 2 1 4 3 5 0 1 2 1 3 1l3 3c2 2 1 5 2 8v6l-1 16c-1 5-1 11 0 16-3-4-1-8-2-11 0-2-1-2-2-3-1 1-1 2-1 2h-1l-4-5c0-1-1-3-1-4v-3-1h1 1l1-1c2 0 2 0 4 2v-1-2c0-5-1-9-3-13 0-3-3-4-4-6s-1-3-2-4c-2-3-4-5-6-8-1-1-1-1-2-1z" class="J"></path><path d="M501 337l1 1c1 1 2 1 3 1 2 2 2 4 2 7h0c-2-2-4-5-6-8v-1z" class="q"></path><path d="M501 338c2 3 4 6 6 8l1 2h0c-1 1-1 2-1 2h-1l-4-5c0-1-1-3-1-4v-3z" class="E"></path><path d="M506 318c2 1 2 3 4 6 3 6 1 15 2 22-1 5-1 11 0 16-3-4-1-8-2-11 0-2-1-2-2-3h0c1-1 1-1 1-2 1-2 1-4 1-6 0-6 0-12-2-17l-2-5z" class="B"></path><path d="M504 315h2l1-1 1-1 3 3c2 2 1 5 2 8v6l-1 16c-1-7 1-16-2-22-2-3-2-5-4-6l-2-2v-1z" class="R"></path><path d="M511 316c2 2 1 5 2 8v6c-1-5-3-9-2-14z" class="G"></path><path d="M491 303c-1-1-1-2-1-3h1c2 1 5 1 7 0s4-3 5-6v7h0c-1 1-2 3-3 5l2 1c0 2 1 4 3 5 0 1 2 1 3 1l-1 1-1 1h-2v1c-1-1-1-2-2-3-2-2-4-3-6-6v-1c-1-1-1-2-3-2-1-1-1-1-2-1z" class="F"></path><path d="M504 315l-6-9 1-1 1 1 2 1c0 2 1 4 3 5 0 1 2 1 3 1l-1 1-1 1h-2z" class="S"></path><path d="M493 322c2 2 3 3 4 5l3 7c0 1 1 2 2 3h-1v1 3l-1 3c0 1 0 3-1 4 0 1-1 1-1 1-1 2-2 3-3 3h-1-3l-3-2v1c-1-1-1-1-2-1l-4-6c0-1-1-1-1-2 1-2 1-3 0-4 1-1 1-2 3-2 0-6 0-8 4-12l5-2z" class="u"></path><path d="M481 342c1-2 1-3 0-4 1-1 1-2 3-2 1 4 2 7 6 10 2 1 4 2 7 1l3-3c0 1 0 3-1 4 0 1-1 1-1 1-1 2-2 3-3 3h-1-3l-3-2v1c-1-1-1-1-2-1l-4-6c0-1-1-1-1-2z" class="J"></path><path d="M488 350c-3-3-4-4-5-8 1 2 5 7 7 7 1 1 5 0 5 1v2h-1-3l-3-2z" class="g"></path><path d="M414 195l-1 2h-1v1h2 0c3 1 5-1 8 0 1 1 1 1 2 1 2 0 4 0 5 1h2c1 0 2 1 2 1h1c1 0 1 0 2 1h1c5 1 10 4 14 7h1l2 2h0c1 2 2 2 3 3l3 3v1h1c1 0 1 1 2 2h-1c-1 0-1 0-2-1-2-2-5-3-7-4l-3 1 2 3-7-4-3-2c-4-1-9-3-13-3 0-1-1-2-2-2-3-1-9 0-12 1h-2l-9 2 2-1-4-1-1-1c4-3 9-5 14-7-3 0-6 1-9 2 1-2 2-4 4-5l2-2 2-1z" class="H"></path><path d="M406 203c1-2 2-4 4-5 2 1 3 1 5 2h3c-1 1-1 1-3 1-3 0-6 1-9 2z" class="E"></path><path d="M426 206h0c-2-2-5-2-7-2-1 0-2 0-3-1 7-1 14 0 21 2v2l1 1h0c-4-1-8-2-12-2z" class="T"></path><path d="M426 206c4 0 8 1 12 2 5 2 10 4 15 7l-3 1 2 3-7-4-3-2v-2c-3-1-4-2-7-2-3-1-6-1-9-3z" class="P"></path><path d="M442 211c3 1 6 3 8 5l2 3-7-4-3-2v-2z" class="j"></path><path d="M406 210c6-5 13-4 20-4 3 2 6 2 9 3 3 0 4 1 7 2v2c-4-1-9-3-13-3 0-1-1-2-2-2-3-1-9 0-12 1h-2l-9 2 2-1z" class="g"></path><path d="M501 341c0 1 1 3 1 4l4 5h1s0-1 1-2c1 1 2 1 2 3 1 3-1 7 2 11l-1 2 1 1v2h1l-1 1c-2 2-2 5-3 8-1 2-2 2-2 4 0 1-1 1-1 2-1 1-1 2-2 2-1-1-2-3-2-5h-1l-1-2c-1-1-2-2-2-4h-2c0-2 0-4-1-6h-1l-1-4v-1c0-3 0-7 1-10h1c1 0 2-1 3-3 0 0 1 0 1-1 1-1 1-3 1-4l1-3z" class="u"></path><path d="M506 350h1l1 3h-3v-2l1-1z" class="x"></path><path d="M508 348c1 1 2 1 2 3 1 3-1 7 2 11l-1 2-3-11-1-3s0-1 1-2z" class="P"></path><path d="M502 361h0l1-2c0 2 0 7 1 8 2 3 0 10 0 14l2 1c-1 1-1 2-2 2-1-1-2-3-2-5h-1l-1-2c-1-5 0-10 2-16z" class="O"></path><path d="M502 378c1 1 1 2 1 3l1-1v1l2 1c-1 1-1 2-2 2-1-1-2-3-2-5v-1z" class="o"></path><path d="M502 361c1 6-1 12 0 17v1h-1l-1-2c-1-5 0-10 2-16z" class="L"></path><path d="M501 341c0 1 1 3 1 4l4 5-1 1-1-2c-1 2 0 1 0 2s-1 2-1 3h-1v2 1h1l1 1v1h-1l-1 2h0c-2 6-3 11-2 16-1-1-2-2-2-4h-2c0-2 0-4-1-6h-1l-1-4v-1c0-3 0-7 1-10h1c1 0 2-1 3-3 0 0 1 0 1-1 1-1 1-3 1-4l1-3z" class="w"></path><path d="M497 356l1 1 1 1c0 2-1 3-2 4v7c-1-4 0-9 0-13z" class="s"></path><path d="M501 341c0 1 1 3 1 4-2 4-4 7-5 11 0 4-1 9 0 13 0 1 0 3 1 4h-2c0-2 0-4-1-6h-1l-1-4v-1c0-3 0-7 1-10h1c1 0 2-1 3-3 0 0 1 0 1-1 1-1 1-3 1-4l1-3z" class="B"></path><path d="M495 352c1 0 2-1 3-3 0 0 1 0 1-1-3 7-4 12-4 19h-1l-1-4v-1c0-3 0-7 1-10h1z" class="e"></path><path d="M158 160c6 1 10 4 14 9l-2 1c2 4 3 8 2 12l-1 1c-2 2-4 4-7 5-3 0-5-1-7-3v-1c-1 0 0 0-1-1 0-1 1-1 1-2v-1l1-5c0-2-1-4-2-6-3-3-6-3-9-3-6 2-10 5-14 11-3 7-3 16-1 24 8 26 34 37 55 51 9 6 18 14 26 22 20 18 38 41 50 64 10 19 19 38 27 57l34 82 30 81 44 103 41 95 76 177v-1c1-5 4-9 5-14l10-25 31-79 26-65 44-106 51-130 27-70 5-14c1-2 3-5 3-8h0c-3 3-7 5-11 7-11 6-22 9-35 10l3-7c1 0 10-26 11-30 2-4 3-9 4-13 0-1 0-1-1-2v-1l-2 2-2-1c2-2 5-3 6-6v-1l1-5c-1 2-2 3-4 4l1-1h0v-1l1-1h-2v1l-1-1h-1v1h-4l2-1-2-1c-2 1-4 1-6 1-2 1-4 2-6 2h-7c-5-2-8-4-11-8l-3-4c-3-6-4-15-2-22 0-2 2-5 2-7v-1l1-1c0-2 1-2 1-4l8-11c2-1 3-1 4 0h1c2-3 2-5 2-8v-1l1-1c1 1 0 3 1 4h0c5-7 11-16 13-24 1-2 1-5 1-7s0-4-1-5v-2c1 1 2 1 3 2-2-4-6-7-10-9-8-4-18-5-26-2-5 2-9 5-12 10-4 9-1 19 2 27-1 0-2-3-3-4-4-5-7-11-10-16l-3-9c-2-7 0-13 2-19 4-9 7-17 16-21l-1-1c1-2 3-3 5-4-3 1-5 1-7 1h-1c-1 1-1 1-2 1s-2 0-3-1h0 2c1-1 2-2 2-4 0-3 0-5-1-8h3c-1-2-1-2-1-3 1-1 2-1 2-1 2-1 3-2 4-2l-1-2-1 1c-2-1-3-1-4-3h1c-1-1-4-2-5-2h0l-1-1-1-1h0l-2-1c-4-3-6-8-8-12l-3-3-1 1 1 2c-2 0-3-2-5-3-2-3-2-4-2-7l1-1 3-6 2-2c1-2 1-3 1-5h3l1 1c4 0 7 0 10 1 3-1 7-1 9-1 3 0 12-2 15 0 2 2 6 2 9 2h4s1 1 2 1c2 1 4 1 6 2h1c3 1 5 1 8 2 1 1 3 2 3 2 4 1 7 3 10 5 2 1 6 5 8 5 1 1 3 3 4 3l10 10c1 1 2 2 3 4l3-1c0 1 1 2 2 3l1 2 2 2v1c2 3 5 6 6 9l1 2 1 1c2 0 3 2 4 4 1 1 1 0 2 1v-1c-1-1-1-2-1-3 1 1 2 2 2 3s1 2 2 2l1 1c2 0 4 1 7 0l1 1h2v-1h5c1 1 2 1 4 1h2l-1 1c27 0 54 0 79-8 13-4 23-9 32-18 5-5 8-10 12-16 0 6 1 14-1 21h0c-1 4-3 7-6 9-10 13-23 23-37 32-9 5-18 9-27 14-7 4-13 10-19 16-8 7-15 16-22 24l-8 12 2 1h0l5 3h-4v8 1l1 5v4l1 7h0c1 11 3 19 11 26 2 2 5 3 7 6v1h0l1 1-1 1c-2-1-3-2-5-3l-1 1c-1-1-3-4-5-4l-1 1h-2l-1 4c-1-1-2-1-3-1h0l-2-1c1 0 0 0 1-1h-2c-4 1-8 0-12 1h-1c-1 1-2 2-4 3h0v-1l-1-1-4 4c-2 2-3 4-4 6 0 1 0 1 1 1l1 1c-4 2-10 4-14 5v1l1 1h1c1 0 1 1 1 1 2 1 3 2 3 3h3l8 2h2c2 2 4 3 5 6l2 1 1 3 3 1-1 1c-5-3-10-5-16-7-1 2-1 5-1 8v2c0 5 0 10 1 14l2 5v2c1 2 2 6 2 9 4 11 6 24 5 36l1 5 2 14 2 16 1 3 1 6 1 15v3l-1-1c-1 1 0 3-1 4v8l-4 26 1 2v3l1 1c0 2-2 5-1 7h0l1-2v2 2 13c0 6 0 13-1 19v10 5h0c-1 1-1 2-1 4l-1-1-1 1c-1-4-2-5-6-7h-2v-3h0l-1-2c-2 1-6 5-8 7l-1-1c-3 3-6 6-7 10l-2 4c1 0 2 1 3 2h0c1 6 3 9 6 14 2 1 3 2 4 4l2 2h0l1 3h0-2c-3-1-5-3-8-3s-9-1-12 1l1 1c-6 2-11 6-16 9-4 0-8 6-12 7-2 0-3 0-5 2l-1-1-7 5h-1l-3 2v3h0c-4 3-8 5-11 7-2 2-4 3-7 5l1 1c-2 1-5 2-6 4-1 0-1 1-1 2-3 0-7 1-11 2-1 1-4 1-5 2-3 1-15 8-16 8l-22 14 3 1 4-2 2 1h0l10-5c-2 2-4 4-7 5v1c1 0 3 1 5 1h0l-3 1c-2 2-5 3-8 3-2 0-4 0-6 1-3 0-6 1-9 3l4-1 1 1h1v1l-1 1c3-1 7-3 10-3v1l4-1v1c-3 0-6 1-8 2-1 1-1 2-1 3 0 2 1 3 2 4l-2 1c-7 2-12 6-17 11l-1 1c-1 2-3 3-4 4-1 2-3 3-4 5-1 0-1 0-2-1-4 7-5 16-10 23 1-2 1-4 1-6l-22 53-19 48-5 14c-1 3-2 6-4 8-1 1-2 1-3 0-3-3-4-7-5-10l-11-24-22-51-18-42-1 2-4-9v2 1c-1-2-2-3-4-4-2-4-4-13-8-15-2-1-3-2-5-4s-4-4-4-7c-4-3-9-5-13-7 0-1 1-2 2-2h3c1-1 2-1 3-1 3 1 5 2 7 3s4 3 6 3h2l-4-9-2-5v1l-1-1c0-2 0-1-1-2s-1 0-1-1c-2-2-3-4-4-6-3-5-7-11-8-17h0l-1-1c-1-2-3-3-4-4h-1l-2-1c-4-4-8-9-11-15-1-4-4-6-5-11l-1-3c-3-2-6-3-7-7l1-1v-1c-1-2 1-2 1-4h0c4-1 6-3 8-6 0-3-1-5-2-7 0-2-1-3-1-4l-2-3c0-2-1-4-2-5l-2-4h0l1-1-3-8-3-6c-1 0-2-1-3-2-3-3-6-6-10-8l-7-5v-3l-2-2c-3-6-7-10-11-16-1-2-4-5-6-7l1-1c-1-3-1-5-2-8l2 1h1c1-1 1 0 3-1l-8-7h3c-1-2-3-3-5-4v-1l-1-2-8-4v-1l-18-12v1-1l-2-1-1 1c1 1 3 2 3 3l2 2c-1 0-2 0-3-1-2 0-5-3-7-5h-2l-1-1-1 2h1c1 1 1 1 2 3v1c-2-1-3-3-5-3l-8-9c-6-6-11-14-15-22-1-1-1-2-2-4 0-4-1-8-2-11l-2-8-1-3c-4-16-3-33 1-48 2-6 4-12 7-17v-6h-1c1-5 1-9 1-14-1-1 0-3-1-4v-2-1-1l-1 1v2h0l-1-2c-1 0-1 0-2 1h-4c2-1 5-2 7-2h1c3-1 5-1 8-1h1c1 0 1 0 2-1l-3-6-1-2v-1l-3-3h1c-1-1-2-1-2-2-4-2-7-5-9-8 0-1 0-1-1-2-2-2-3-4-5-7v3c-1-1-2-1-3-2l-2-3c-3-4-5-8-7-13-2-7-3-14-4-21-1-2-1-5-1-7s1-4-1-6c0-1 0 0-1-1l-1 1-2-1 3-1 1-1v-1c-1-2-2-3-3-4l-1-1h-2c-2 2-6 3-9 5-5 2-11 4-16 5-2 0-3-1-4-1h-1c1 0 2 0 3-1s1-2 1-4h0-1v-3s-1-1-2-1h0c-2-1-4 0-5-1-1 0-1-1-2-1h-1l-1-1h1 3 1c-6-2-12-3-17-7l-3-3c1 0 0 0 1-1 0-2-3-5-4-7l-2-1-3-2c-2-1-7-3-8-5h0l-3-2 2-1h0 2 3c3-1 4-4 5-5 1 4 1 6 4 9h0 3l1 1 1 1 3 1c1 0 2 0 4-1l4-3 1-1c0-1 0-2 1-3v-1-1l-3-1 1-2-4 1h-1c0-2 7-2 9-4l-1-2h0 1l-1-3c-3-3-8-6-11-9-16-12-35-21-49-35-9-10-16-22-16-35 0-9 4-17 10-23 5-4 12-6 18-6 2 0 2-1 3-1z" class="d"></path><path d="M692 229l5 1 1 2h-9l2-1h1c-1 0-2 0-3-1h0c1 0 1 0 3-1z" class="r"></path><path d="M628 661v4c0 1-1 3-2 4l-1 4v1c-2 1-2 1-3 2l6-15z" class="t"></path><path d="M699 227l3 2 2 2c2 0 3 0 4 1h-10l-1-2h1l-1-2 2-1z" class="b"></path><path d="M771 318l2 1h0l5 3h-4v-2l-2 1c-3 2-4 4-6 7v-1c1-3 3-6 5-9zM596 737c0 3 0 6-1 9l-2 4-1 1-1-1c1-4 3-9 5-13z" class="X"></path><path d="M661 229l1 1v1c2 0 4 0 5 1h-20-1c0-1 2-1 3-1 3 0 9 0 12-2z" class="K"></path><path d="M226 292l5 6-1 1 1 1v1c-1-1-3-2-5-3h0l-2-1h2v-1c-1-1-2-3-3-4h3z" class="r"></path><path d="M758 230h5c1 1 2 1 4 1h2l-1 1h-28c3-2 12-1 16-1h2v-1z" class="m"></path><defs><linearGradient id="CU" x1="160.302" y1="184.489" x2="168.318" y2="182.042" xlink:href="#B"><stop offset="0" stop-color="#8a8589"></stop><stop offset="1" stop-color="#a09e9c"></stop></linearGradient></defs><path fill="url(#CU)" d="M165 182c1-1 3-1 4-3 1 2 0 3 0 4-1 2-3 2-4 3-3 0-5-1-7-3l-1-1 4 1h1c1 0 2 0 3-1z"></path><path d="M602 724c1 4-2 8-3 11 0 1 0 2-1 3s-1 4-1 6l-2 2c1-3 1-6 1-9 1-4 4-9 6-13z" class="m"></path><path d="M158 160c6 1 10 4 14 9l-2 1c-3-5-9-7-15-9 2 0 2-1 3-1z" class="v"></path><path d="M606 713c1 1 1 3 1 5l-3 8c-1 1-1 3-1 4l-1 2-3 3c1-3 4-7 3-11 0-3 3-8 4-11z" class="a"></path><path d="M669 558c1 2-2 8-2 12-1 2-1 3-2 5 0 1-1 3-1 4l-1 1c-1 0-1-1-2-1l8-21z" class="m"></path><path d="M374 615l10 23c-1 0-2-1-3-2 0-3-3-7-4-9 0-2 0-3-1-4s-1-1-1-2v-3c-1-1-1-1-1-3z" class="X"></path><path d="M734 389l1 1-1 4c0 2-1 2 0 3h1v1l1 1h-3c-1 1-2 2-3 4v2c-1 1-1 2-2 3l-1 3h-1c2-7 5-15 8-22z" class="m"></path><path d="M157 181h1c2-1 3-1 4-2 0-3 0-6-1-8s-3-3-5-4l-1-1 1-1c3 1 5 3 7 6v1c2 1 1 7 1 9l1 1c-1 1-2 1-3 1h-1l-4-1 1 1-1 1c-1 0 0 0-1-1 0-1 1-1 1-2z" class="S"></path><path d="M240 315h-1c1-1 1-1 2-1h1l-1-3c4 5 7 10 10 15l-1 2h0-2v9c-1-2-1-5-1-7s1-4-1-6c0-1 0 0-1-1l-1 1-2-1 3-1 1-1v-1c-1-2-2-3-3-4l-1-1h-2z" class="b"></path><path d="M247 330l1-6h0c1 2 2 3 2 4h-2v9c-1-2-1-5-1-7z" class="O"></path><path d="M335 520l12 31h-2l-5-15c-1-1-1-2-3-3l-1-1v-1h1v-4l-2-6v-1z" class="t"></path><path d="M656 227c2 1 4 1 5 2-3 2-9 2-12 2-1 0-3 0-3 1h1l-8 2-1-1c1-2 3-3 5-4h1c4-1 9 0 12-2z" class="U"></path><path d="M615 693h0c0 3-2 6-3 8l1 1v2h0l1-1 1-1v-1c2-2 2-5 5-6-2 5-6 9-9 13-1 3-2 7-4 10 0-2 0-4-1-5l9-20z" class="h"></path><path d="M704 222l3 2c1 2 3 4 5 5 1 1 2 0 3 0l1-1 4 3v1h-12c-1-1-2-1-4-1l-2-2v-1c-1-1-1-1-1-2 2 1 3 2 5 1l1-1-3-2v-2z" class="q"></path><path d="M701 226c2 1 3 2 5 1l1-1 3 4c-1 1-2 1-4 1h-2l-2-2v-1c-1-1-1-1-1-2z" class="J"></path><path d="M147 166c1-1 2-2 3-2 2 0 4 0 6 1l-1 1 1 1c2 1 4 2 5 4s1 5 1 8c-1 1-2 1-4 2h-1v-1l1-5c0-2-1-4-2-6-3-3-6-3-9-3z" class="n"></path><path d="M736 399h1c1 0 1 1 1 1 2 1 3 2 3 3h3l8 2v1l-5-1-1 1c-2 0-3 0-5-1h-3-8v-2c1-2 2-3 3-4h3z" class="R"></path><path d="M730 405v-2c1-2 2-3 3-4 1 2 2 3 3 4h1l1 2h3-3-8z" class="M"></path><path d="M420 730c3 2 5 7 7 8 2 3 3 7 5 10 1 3 3 6 3 8v1l-1-1c0-2 0-1-1-2s-1 0-1-1c-2-2-3-4-4-6-3-5-7-11-8-17z" class="X"></path><path d="M579 781h1l2-1-7 15c-4 7-5 16-10 23 1-2 1-4 1-6 1-4 3-8 5-12l8-19z" class="v"></path><path d="M735 221l1-1 1 2 1 1c2 0 3 2 4 4 1 1 1 0 2 1v-1c-1-1-1-2-1-3 1 1 2 2 2 3s1 2 2 2l1 1c2 0 4 1 7 0l1 1c-4 0-13-1-16 1h-9l-1-1h2l1-1c1 0 1 0 2-1 0-3-1-3-2-5l1-1h1l1-1-1-1z" class="K"></path><path d="M737 222l1 1c2 0 3 2 4 4 1 1 1 0 2 1v-1c-1-1-1-2-1-3 1 1 2 2 2 3v3h0 0-2c-3-1-5-5-6-8z" class="F"></path><path d="M736 222c1 2 1 5 2 7v1c-2 2-3 1-5 1h-1l1-1c1 0 1 0 2-1 0-3-1-3-2-5l1-1h1l1-1z" class="Y"></path><path d="M687 216c4 0 6 3 9 5 1 2 4 3 5 5 0 1 0 1 1 2v1l-3-2-2 1 1 2h-1l-5-1h-1c0-1 0-1 1-1v-1c-1-2-3-5-5-6l-3-2h-1l-1-1h2l1-1h0c1 1 2 1 3 1v1h2l-3-3z" class="s"></path><path d="M684 219l1-1c2 2 4 3 6 4 2 2 4 5 6 6l1 2h-1l-5-1h-1c0-1 0-1 1-1v-1c-1-2-3-5-5-6l-3-2z" class="t"></path><path d="M687 216c4 0 6 3 9 5 1 2 4 3 5 5 0 1 0 1 1 2v1l-3-2-9-8-3-3z" class="B"></path><path d="M203 269a249.64 249.64 0 0 1 23 23h-3l-2-1c-1 0-1 1-1 1-1 0-2-1-3 0h0c-2 0-6 0-8-1h0-3c1-1 2-2 2-3v-1l2-1c2-1 3-1 5-2-2-1-2-2-3-4v-1h0c-2-2-3-4-6-4-2 1-4 1-6 2l-4 1h-1c0-2 7-2 9-4l-1-2h0 1l-1-3z" class="a"></path><path d="M215 284c1 2 2 3 3 5-1 1-2 1-2 2h-4l3-2-1-2c-1-1-2-1-4-1 2-1 3-1 5-2z" class="e"></path><path d="M210 286c2 0 3 0 4 1l1 2-3 2h-3 0-3c1-1 2-2 2-3v-1l2-1z" class="i"></path><path d="M208 287l2 1c0 1-1 2-1 3h-3c1-1 2-2 2-3v-1z" class="I"></path><path d="M206 275c3 0 4 2 6 4h0v1c1 2 1 3 3 4-2 1-3 1-5 2l-2 1v1c0 1-1 2-2 3-2 0-7 1-9 0v-1l-1-1 4-3 1-1c0-1 0-2 1-3v-1-1l-3-1 1-2c2-1 4-1 6-2z" class="Y"></path><path d="M197 290l11-2c0 1-1 2-2 3-2 0-7 1-9 0v-1z" class="F"></path><path d="M206 275c3 0 4 2 6 4h0v1c-4 1-8 5-11 7l-1 1-1 1h-1l1-1c1-1 1-1 1-2l1-1c0-1 0-2 1-3v-1-1l-3-1 1-2c2-1 4-1 6-2z" class="K"></path><path d="M206 275c1 2 1 3 0 5l-5 5c0-1 0-2 1-3v-1-1l-3-1 1-2c2-1 4-1 6-2z" class="J"></path><path d="M184 287h3l1 1 1 1 3 1c1 0 2 0 4-1l1 1v1c2 1 7 0 9 0h3 0c2 1 6 1 8 1h0c1-1 2 0 3 0 0 0 0-1 1-1l2 1c1 1 2 3 3 4v1h-2l2 1h0c2 1 4 2 5 3v1l-6 1h-5v-1h0v-1l-1-2h-6-2c-9-1-20-6-27-12z" class="q"></path><path d="M211 299l15-1c2 1 4 2 5 3v1l-6 1h-5v-1h0v-1l-1-2h-6-2z" class="N"></path><path d="M219 299c2 0 3 0 5 1s3 1 5 1c-1 0-2 1-3 0l-1 2h-5v-1h0v-1l-1-2z" class="M"></path><path d="M192 290c1 0 2 0 4-1l1 1v1c2 1 7 0 9 0h3 0c2 1 6 1 8 1h0c1-1 2 0 3 0 0 0 0-1 1-1l2 1c1 1 2 3 3 4-1 1-3 0-4 0-7 0-15-1-22-3-3-1-5-1-8-3z" class="Z"></path><path d="M657 223c5-3 9-3 15-2 4 1 8 3 11 5 1 1 5 3 6 4h0c1 1 2 1 3 1h-1l-2 1h-22c-1-1-3-1-5-1v-1l-1-1c-1-1-3-1-5-2-1 0-1-1-2-1 1-1 1-2 3-3z" class="u"></path><path d="M766 327v1h0l-3 5h3l1 1c-1 1-1 3-1 4h-5v2l-2 10-2 8c-2 5-4 10-7 13-4 4-8 6-10 11-1 1-2 1-3 1 8-19 17-39 29-56z" class="b"></path><path d="M763 333h3l1 1c-1 1-1 3-1 4h-5c1-2 1-3 2-5z" class="Z"></path><path d="M660 209h5c7 2 12 5 17 9l1 1h1l3 2c2 1 4 4 5 6v1c-1 0-1 0-1 1h1c-2 1-2 1-3 1-1-1-5-3-6-4-3-2-7-4-11-5-6-1-10-1-15 2h-1l-4 2-1-2 2-1c0-2 0-3-1-4l3-3 1-4h1c1-1 2-2 3-2z" class="j"></path><path d="M680 218c1 0 1 1 3 1h1l3 2h-1c-1 0-2 0-3-1h-1c0 1 1 1 1 2l-8-3c2-1 3-1 5-1z" class="U"></path><path d="M662 215c3-1 6-1 8 0 1 0 2 0 3 1-2 0-3 0-4 1-3 0-5 0-7 1l-2 1c1-1 1-2 1-2 1-1 1-2 1-2z" class="J"></path><path d="M683 222c0-1-1-1-1-2h1c1 1 2 1 3 1h1c2 1 4 4 5 6v1c-1 0-1 0-1 1h1c-2 1-2 1-3 1-1-1-5-3-6-4h1 2c-1 0-1-1-2-1-1-1-1-1-1-3z" class="q"></path><path d="M683 222c2 2 5 5 8 7h1c-2 1-2 1-3 1-1-1-5-3-6-4h1 2c-1 0-1-1-2-1-1-1-1-1-1-3z" class="B"></path><path d="M660 209h5c7 2 12 5 17 9l1 1c-2 0-2-1-3-1-2 0-3 0-5 1l-6-2c1-1 2-1 4-1-1-1-2-1-3-1l1-1v-1l-10-2-5 4h-1l1-4h1c1-1 2-2 3-2z" class="F"></path><path d="M673 216c2 1 3 1 5 1l2 1c-2 0-3 0-5 1l-6-2c1-1 2-1 4-1z" class="M"></path><path d="M655 215h1l5-4 10 2v1l-1 1c-2-1-5-1-8 0 0 0 0 1-1 2 0 0 0 1-1 2l-4 4-4 2-1-2 2-1c0-2 0-3-1-4l3-3z" class="C"></path><path d="M662 215s0 1-1 2c0 0 0 1-1 2l-4 4-4 2-1-2 2-1c3-3 6-6 9-7z" class="l"></path><defs><linearGradient id="CV" x1="442.726" y1="774.698" x2="428.789" y2="783.089" xlink:href="#B"><stop offset="0" stop-color="#331a18"></stop><stop offset="1" stop-color="#463835"></stop></linearGradient></defs><path fill="url(#CV)" d="M418 767c0-1 1-2 2-2h3c1-1 2-1 3-1 3 1 5 2 7 3s4 3 6 3h2l12 27c1 4 4 8 4 11l-1 2-4-9v2 1c-1-2-2-3-4-4-2-4-4-13-8-15-2-1-3-2-5-4s-4-4-4-7c-4-3-9-5-13-7z"></path><defs><linearGradient id="CW" x1="440.656" y1="781.829" x2="431.777" y2="778.341" xlink:href="#B"><stop offset="0" stop-color="#706d70"></stop><stop offset="1" stop-color="#848485"></stop></linearGradient></defs><path fill="url(#CW)" d="M431 774c6 4 12 10 15 16 2 4 3 8 6 11v2 1c-1-2-2-3-4-4-2-4-4-13-8-15-2-1-3-2-5-4s-4-4-4-7z"></path><path d="M698 314v-1 1c1 1 1 2 1 3v6c1 3 1 6 1 8 0 5 0 9-1 13s-1 8-1 12c-1 1-1 2-1 3l-5 23c-1 3-2 6-2 9-1 5-4 11-6 16l-11 30h2 1c1-1 1-1 2-1h-2c0-2 0-2 1-4 0-1 1-1 0-2l1-1s0-1 1-1l1-2h-1c1-1 1-2 1-3 1-1 1-2 2-3v-1h0c1-2 2-5 3-7v-1-1c1-1 1-2 2-2l-1-1h1c0-2 1-3 1-4l2-4v-2h0v-1l1-1v-1h0v-1l1-1v-1l2-6v-1l1-3 1-2h0v-2c1-1 0 0 1-2v-1c0-1 1-2 1-4h1v-2-1c0-1 0-2 1-2h0v1c-1 6-3 12-5 19-5 17-11 34-19 50 14-1 29-7 40-16l1 2c-3 3-7 5-11 7-11 6-22 9-35 10l3-7c1 0 10-26 11-30 2-4 3-9 4-13 0-1 0-1-1-2v-1l-2 2-2-1c2-2 5-3 6-6v-1l1-5c-1 2-2 3-4 4l1-1h0v-1l1-1h-2v1l-1-1h-1v1h-4l2-1-2-1c6-4 10-7 13-13l2-2c1-1 1-3 1-4l1-12v-29z" class="c"></path><path d="M694 361l2-2c-1 10-3 20-7 29 0-1 0-1-1-2v-1l-2 2-2-1c2-2 5-3 6-6v-1l1-5c-1 2-2 3-4 4l1-1h0v-1l1-1h-2v1l-1-1h-1v1h-4l2-1-2-1c6-4 10-7 13-13z" class="v"></path><path d="M683 375c3-1 6-3 9-5l-1 4c-1 2-2 3-4 4l1-1h0v-1l1-1h-2v1l-1-1h-1v1h-4l2-1z" class="R"></path><defs><linearGradient id="CX" x1="602.2" y1="713.318" x2="621.711" y2="711.647" xlink:href="#B"><stop offset="0" stop-color="#4e4344"></stop><stop offset="1" stop-color="#79777a"></stop></linearGradient></defs><path fill="url(#CX)" d="M620 694l1-1h0c1 1 2 4 2 4 1 1 2 0 3 2l-1 1c0 1-1 2-1 3 1 2 1 5 2 6l1 1 1 2-3 5c-5 5-11 10-18 12l-1-1-3 3v-1c0-1 0-3 1-4l3-8c2-3 3-7 4-10 3-4 7-8 9-13v-1z"></path><path d="M620 694l1-1h0c1 1 2 4 2 4 1 1 2 0 3 2l-1 1c0 1-1 2-1 3v-2c-1 0-1 0-1 1h-1l-2 2h0c1-2 1-4 2-6-1-1-1-2-2-4z" class="q"></path><path d="M621 706v6c-1 2-1 2 0 4-4 3-7 7-11 9 0-1 1-1 1-2 2-2 3-4 5-6 2-3 3-7 5-11z" class="O"></path><path d="M622 702h1c0-1 0-1 1-1v2c1 2 1 5 2 6-2 3-3 5-5 7-1-2-1-2 0-4v-6l1-4z" class="s"></path><path d="M626 709l1 1 1 2-3 5c-5 5-11 10-18 12l-1-1c1-1 3-1 4-3 4-2 7-6 11-9 2-2 3-4 5-7z" class="E"></path><defs><linearGradient id="CY" x1="672.73" y1="555.937" x2="686.016" y2="556.214" xlink:href="#B"><stop offset="0" stop-color="#757172"></stop><stop offset="1" stop-color="#a1a1a2"></stop></linearGradient></defs><path fill="url(#CY)" d="M679 533l2-1h1c0 3 1 7 1 10 0 2-1 3 0 5s1 5 1 7h0c1 1 2 1 3 1l1 2 3-2v1l-3 3v1l3-2h2c-5 3-9 7-14 9l-10 6-4 2c1-2 1-3 2-5 0-4 3-10 2-12 1-3 3-6 4-9 2-5 3-11 6-16z"></path><path d="M691 555v1l-3 3v1c-2 2-5 3-8 4l2-2h1l-1-1 6-4 3-2z" class="J"></path><path d="M681 532h1c0 3 1 7 1 10 0 2-1 3 0 5s1 5 1 7l-2-1v-4c-1 0 0 0-1-1 1-3 1-5 0-8v-7-1z" class="g"></path><path d="M691 558h2c-5 3-9 7-14 9l-10 6-4 2c1-2 1-3 2-5 2-2 2-4 3-6v6c4-1 8-4 10-6 3-1 6-2 8-4l3-2z" class="U"></path><path d="M679 533l2-1v1c-2 2-2 6-3 9l-8 22c-1 2-1 4-3 6 0-4 3-10 2-12 1-3 3-6 4-9 2-5 3-11 6-16z" class="K"></path><path d="M656 594l1-3 1 4c-2 3-3 5-3 8l-3 9c-1 2-3 4-2 6 2-3 5-6 7-9 3-3 5-6 8-8l-6 10c-1 1-1 3-2 4-1 3-3 5-5 8h1l2-2h2c-1 3-2 4-4 5l-2 1c0 1 0 1 1 1l2-1h2c-1 1-1 2-1 2-1 2-3 4-4 5l-2 3-11 13c0 1-2 3-3 4l-2 3-5 8h0v-4l27-67h1z" class="X"></path><path d="M656 594l1-3 1 4c-2 3-3 5-3 8l-3 9c-1 2-3 4-2 6-1 2-3 3-4 4 2-10 7-18 10-28z" class="Y"></path><path d="M650 618c2-3 5-6 7-9 3-3 5-6 8-8l-6 10c-1 1-1 3-2 4-1 3-3 5-5 8h1l2-2h2c-1 3-2 4-4 5l-2 1c0 1 0 1 1 1l-7 5h0l-7 6 8-17c1-1 3-2 4-4z" class="N"></path><path d="M645 633v-1c0-2 2-7 4-9 1 0 1-1 2-2 2-3 5-7 8-10-1 1-1 3-2 4-1 3-3 5-5 8h1l2-2h2c-1 3-2 4-4 5l-2 1c0 1 0 1 1 1l-7 5z" class="S"></path><defs><linearGradient id="CZ" x1="751.936" y1="359.054" x2="760.875" y2="363.034" xlink:href="#B"><stop offset="0" stop-color="#8e8c8d"></stop><stop offset="1" stop-color="#b7b6b6"></stop></linearGradient></defs><path fill="url(#CZ)" d="M761 338h5c-1 6-2 12 0 18l-1 3c-1 3-2 7-4 9h-1c-2 0-2 1-3 2v1 1l-2 2c2-1 3-2 4-3v1l-1 1h1c0 2-2 3-3 5h1l-2 2-4 4c-2 2-3 4-4 6 0 1 0 1 1 1l1 1c-4 2-10 4-14 5h-1c-1-1 0-1 0-3l1-4-1-1 3-6c1 0 2 0 3-1 2-5 6-7 10-11 3-3 5-8 7-13l2-8 2-10v-2z"></path><path d="M759 350l1 1h0v1 2l-1 1v1l-2 2h0l2-8z" class="B"></path><path d="M760 368v-1c2-5 3-9 4-14 0 2 0 4 1 6-1 3-2 7-4 9h-1z" class="W"></path><path d="M758 373h1c0 2-2 3-3 5h1l-2 2-4 4c0-1 0-1 1-2v-1l-2 1-1-1 2-1-1-1c3-2 6-3 8-6z" class="M"></path><path d="M761 338h5c-1 6-2 12 0 18l-1 3c-1-2-1-4-1-6v-11c-1-1-2-1-3-2v-2z" class="J"></path><defs><linearGradient id="Ca" x1="735.126" y1="396.909" x2="748.041" y2="388.01" xlink:href="#B"><stop offset="0" stop-color="#4a3939"></stop><stop offset="1" stop-color="#5f5153"></stop></linearGradient></defs><path fill="url(#Ca)" d="M750 379l1 1-2 1 1 1 2-1v1c-1 1-1 1-1 2-2 2-3 4-4 6 0 1 0 1 1 1l1 1c-4 2-10 4-14 5h-1c-1-1 0-1 0-3l1-4-1-1 3-6c1 0 2 0 3-1 3 0 7-2 10-3z"></path><path d="M748 384c-1 4-10 8-14 10l1-4h1c5 0 9-4 12-6z" class="h"></path><path d="M750 379l1 1-2 1 1 1s-2 1-2 2c-3 2-7 6-12 6h-1l-1-1 3-6c1 0 2 0 3-1 3 0 7-2 10-3z" class="X"></path><defs><linearGradient id="Cb" x1="737.594" y1="387.074" x2="747.368" y2="384.294" xlink:href="#B"><stop offset="0" stop-color="#66595a"></stop><stop offset="1" stop-color="#77706f"></stop></linearGradient></defs><path fill="url(#Cb)" d="M749 381l1 1s-2 1-2 2c-3 2-7 6-12 6l2-3c1-2 8-5 11-6z"></path><path d="M692 564s0 1 1 2l-14 9h2l4-2c0 1 0 1-1 2-1 0-1 1-2 2l2-1v1c-1 2-2 3-4 4v2l5-4v3c-2 1-6 4-7 6-2 1-4 3-5 6l-1-1-7 8c-3 2-5 5-8 8-2 3-5 6-7 9-1-2 1-4 2-6l3-9c0-3 1-5 3-8l-1-4-1 3h-1l6-15c1 0 1 1 2 1l1-1c0-1 1-3 1-4l4-2 10-6c-1 2-1 2-1 4l1-1h0 2c1 0 2-1 2-1l9-5z" class="G"></path><path d="M682 577l2-1v1c-1 2-2 3-4 4v2c-2 2-4 4-7 6v-1l-3 1h-2c3-3 6-5 9-8l5-4z" class="P"></path><path d="M662 591c5-4 10-9 15-10-3 3-6 5-9 8-3 2-8 5-9 9 0 1 0 1 1 1l-5 4c0-3 1-5 3-8l-1-4 2-2v2h3 0z" class="C"></path><path d="M657 591l2-2v2h3 0l-4 4-1-4z" class="N"></path><path d="M660 599c4-2 8-7 13-9l-1 3-7 8c-3 2-5 5-8 8-2 3-5 6-7 9-1-2 1-4 2-6l3-9 5-4z" class="K"></path><defs><linearGradient id="Cc" x1="668.909" y1="591.811" x2="672.591" y2="572.689" xlink:href="#B"><stop offset="0" stop-color="#756f70"></stop><stop offset="1" stop-color="#9b9999"></stop></linearGradient></defs><path fill="url(#Cc)" d="M685 573c0 1 0 1-1 2-1 0-1 1-2 2l-5 4c-5 1-10 6-15 10h0-3v-2c1-2 3-5 5-7 2-1 5-2 8-3 2-2 4-3 7-4h2l4-2z"></path><defs><linearGradient id="Cd" x1="664.577" y1="571.323" x2="685.332" y2="579.641" xlink:href="#B"><stop offset="0" stop-color="#221b1c"></stop><stop offset="1" stop-color="#474647"></stop></linearGradient></defs><path fill="url(#Cd)" d="M692 564s0 1 1 2l-14 9c-3 1-5 2-7 4l-8 3c-2 2-4 5-5 7l-2 2-1 3h-1l6-15c1 0 1 1 2 1l1-1c0-1 1-3 1-4l4-2 10-6c-1 2-1 2-1 4l1-1h0 2c1 0 2-1 2-1l9-5z"></path><path d="M665 575l4-2c0 1 1 2 2 3l-7 3c0-1 1-3 1-4z" class="I"></path><path d="M669 573l10-6c-1 2-1 2-1 4l1-1h0 2c-3 2-7 4-10 6-1-1-2-2-2-3z" class="R"></path><path d="M389 653l1-1 24 55 8 19c2 4 4 8 5 12-2-1-4-6-7-8h0l-1-1c-1-2-3-3-4-4h-1l-2-1c-4-4-8-9-11-15-1-4-4-6-5-11l-1-3c-3-2-6-3-7-7l1-1v-1c-1-2 1-2 1-4h0c4-1 6-3 8-6 0-3-1-5-2-7 0-2-1-3-1-4l-2-3c0-2-1-4-2-5l-2-4h0z" class="u"></path><path d="M395 695c-3-2-6-3-7-7l1-1c1 3 2 4 4 5s5 2 7 4c2 4 1 8 2 12 2 6 8 12 13 17h-1l-2-1c-4-4-8-9-11-15-1-4-4-6-5-11l-1-3z" class="J"></path><defs><linearGradient id="Ce" x1="271.786" y1="365.294" x2="264.595" y2="369.241" xlink:href="#B"><stop offset="0" stop-color="#23100c"></stop><stop offset="1" stop-color="#5c5659"></stop></linearGradient></defs><path fill="url(#Ce)" d="M251 326l5 9c8 13 14 26 20 40 4 8 9 17 12 25-1-1-2-1-2-3-1 0 0 0-1-1 0-1 0 0-1-1l-1-4-2 1c-1-1-2-1-2-2-4-2-7-5-9-8 0-1 0-1-1-2-2-2-3-4-5-7v3c-1-1-2-1-3-2l-2-3c-3-4-5-8-7-13-2-7-3-14-4-21v-9h2 0l1-2z"></path><path d="M271 374c1 1 3 4 2 5 0-1 0-1-1-1h-1c-1-1 0-2 0-4h0z" class="O"></path><path d="M256 336l3 9c4 10 7 20 12 29h0l-2-3c-1-1-1-1-1-2l-1-1c0 1 0 0-1 1h0v3c-2-5-5-10-6-15-1-3-6-17-5-19 0-1 1-1 1-2z" class="U"></path><defs><linearGradient id="Cf" x1="249.034" y1="349.181" x2="266.966" y2="351.319" xlink:href="#B"><stop offset="0" stop-color="#2e1b19"></stop><stop offset="1" stop-color="#4d5355"></stop></linearGradient></defs><path fill="url(#Cf)" d="M251 326l5 9v1c0 1-1 1-1 2-1 2 4 16 5 19 1 5 4 10 6 15l1 3c1 2 2 3 2 5-2-2-3-4-5-7-4-6-8-15-10-23-2-7-2-14-4-22l1-2z"></path><defs><linearGradient id="Cg" x1="259.77" y1="349.708" x2="250.41" y2="354.462" xlink:href="#B"><stop offset="0" stop-color="#5c5859"></stop><stop offset="1" stop-color="#939293"></stop></linearGradient></defs><path fill="url(#Cg)" d="M248 337v-9h2 0c2 8 2 15 4 22 2 8 6 17 10 23v3c-1-1-2-1-3-2l-2-3c-3-4-5-8-7-13-2-7-3-14-4-21z"></path><path d="M340 574v-1c4 3 8 6 12 8 2-1 3-2 4-3l2 3 1-1 9 21 6 14c0 2 0 2 1 3v3c0 1 0 1 1 2s1 2 1 4c1 2 4 6 4 9-3-3-6-6-10-8l-7-5v-3l-2-2c-3-6-7-10-11-16-1-2-4-5-6-7l1-1c-1-3-1-5-2-8l2 1h1c1-1 1 0 3-1l-8-7h3c-1-2-3-3-5-4v-1z" class="h"></path><path d="M368 604c1 2 3 5 3 7l-1 1c0-1-1-1-1-1-1-1-1-2-2-3l1-4z" class="q"></path><path d="M368 615c1 0 2 1 3 3l6 9c1 2 4 6 4 9-3-3-6-6-10-8h1 1v-1h0v-1c-1-3-2-6-4-8-1-2-1-2-1-3z" class="M"></path><path d="M354 599c4 5 10 10 14 16 0 1 0 1 1 3 2 2 3 5 4 8v1h0l-3-3v-1c-2-6-6-9-10-14-1-1-4-4-5-6-1-1-1-2-1-4z" class="i"></path><path d="M344 586l2 1h0v1c2 4 6 8 8 11 0 2 0 3 1 4 1 2 4 5 5 6 4 5 8 8 10 14v1l3 3v1h-1-1l-7-5v-3l-2-2c-3-6-7-10-11-16-1-2-4-5-6-7l1-1c-1-3-1-5-2-8z" class="V"></path><path d="M364 620l8 8h-1l-7-5v-3z" class="I"></path><path d="M357 608l3 1c4 5 8 8 10 14v1l-13-16z" class="F"></path><path d="M344 586l2 1h0v1c2 4 6 8 8 11 0 2 0 3 1 4 1 2 4 5 5 6l-3-1c-1-1-2-3-3-4-3-4-5-7-8-10-1-3-1-5-2-8z" class="I"></path><defs><linearGradient id="Ch" x1="358.638" y1="582.754" x2="350.885" y2="587.43" xlink:href="#B"><stop offset="0" stop-color="#362424"></stop><stop offset="1" stop-color="#3e3837"></stop></linearGradient></defs><path fill="url(#Ch)" d="M340 574v-1c4 3 8 6 12 8 2-1 3-2 4-3l2 3 1-1 9 21h-1l1 3-1 4-2-3-5-4-1-1-13-13h0 1c1-1 1 0 3-1l-8-7h3c-1-2-3-3-5-4v-1z"></path><path d="M358 582c2 1 2 4 3 6l-5-5c1 0 1 0 2-1z" class="O"></path><path d="M356 578l2 3v1c-1 1-1 1-2 1l-4-2c2-1 3-2 4-3z" class="V"></path><path d="M342 579h3c4 4 8 7 12 10l3 3c1 3 2 5 0 9l-1-1c1-1 1-1 1-2v-1-1c-1-3-8-8-10-10l-8-7z" class="J"></path><path d="M360 592l2 2c2 1 2 2 4 4l1 3 1 3-1 4-2-3-5-4c2-4 1-6 0-9z" class="Y"></path><path d="M360 592l2 2c1 3 4 8 3 11l-5-4c2-4 1-6 0-9z" class="Q"></path><defs><linearGradient id="Ci" x1="360.613" y1="597.194" x2="347.071" y2="586.907" xlink:href="#B"><stop offset="0" stop-color="#908e8f"></stop><stop offset="1" stop-color="#b1b0b1"></stop></linearGradient></defs><path fill="url(#Ci)" d="M350 586c2 2 9 7 10 10v1 1c0 1 0 1-1 2l-13-13h0 1c1-1 1 0 3-1z"></path><defs><linearGradient id="Cj" x1="762.405" y1="352.296" x2="769.509" y2="355.592" xlink:href="#B"><stop offset="0" stop-color="#919090"></stop><stop offset="1" stop-color="#c4c3c3"></stop></linearGradient></defs><path fill="url(#Cj)" d="M772 321l2-1v2 8 1l1 5v4l1 7h0c1 11 3 19 11 26 2 2 5 3 7 6v1h0l1 1-1 1c-2-1-3-2-5-3l-1 1c-1-1-3-4-5-4l-1 1h-2l-1 4c-1-1-2-1-3-1h0l-2-1c1 0 0 0 1-1h-2c-4 1-8 0-12 1h-1c-1 1-2 2-4 3h0v-1l-1-1 2-2h-1c1-2 3-3 3-5h-1l1-1v-1c-1 1-2 2-4 3l2-2v-1-1c1-1 1-2 3-2h1c2-2 3-6 4-9l1-3c-2-6-1-12 0-18 0-1 0-3 1-4l-1-1h-3l3-5h0c2-3 3-5 6-7z"></path><path d="M762 372h1c1 0 1-1 1-1v1c-1 1-1 1-1 2v1c-1 1-1 2-3 4-1 1-2 2-4 3h0v-1l-1-1 2-2 5-6z" class="a"></path><path d="M768 361c1 1 2 1 2 2-1 4-4 8-7 11v1-1c0-1 0-1 1-2v-1s0 1-1 1h-1l6-11z" class="K"></path><defs><linearGradient id="Ck" x1="763.902" y1="338.597" x2="768.947" y2="350.545" xlink:href="#B"><stop offset="0" stop-color="#5d5453"></stop><stop offset="1" stop-color="#817b77"></stop></linearGradient></defs><path fill="url(#Ck)" d="M767 334v2l2-4v5l-3 19c-2-6-1-12 0-18 0-1 0-3 1-4h0z"></path><path d="M770 363c1 1 2 1 2 2 1 1 3 3 3 4-1 0-2 0-3 1v2l-1 1 1 1-1 1-1 1 1 1-1 1h3c-4 1-8 0-12 1h-1c2-2 2-3 3-4v-1c3-3 6-7 7-11z" class="M"></path><path d="M772 372h-2c1-2 0-3 1-6 0 0 1 0 1-1 1 1 3 3 3 4-1 0-2 0-3 1v2z" class="Q"></path><defs><linearGradient id="Cl" x1="769.75" y1="330.75" x2="772.351" y2="334.392" xlink:href="#B"><stop offset="0" stop-color="#787374"></stop><stop offset="1" stop-color="#8b8989"></stop></linearGradient></defs><path fill="url(#Cl)" d="M772 321l2-1v2 8 1c0 1-1 2-1 4 0 3 0 7-1 10v-6l-1 1v1l-1-1c1-1 1-2 1-3h-2v-5l-2 4v-2h0l-1-1h-3l3-5h0c2-3 3-5 6-7z"></path><path d="M772 321l2-1v2 8 1c0 1-1 2-1 4l-1-11v-3z" class="b"></path><path d="M766 328c2-3 3-5 6-7v3h-1c-1 3 0 6-2 8l-2 4v-2h0l-1-1h-3l3-5h0z" class="o"></path><path d="M766 328l1 1v5h0l-1-1h-3l3-5z" class="V"></path><defs><linearGradient id="Cm" x1="773.366" y1="349.362" x2="777.662" y2="368.74" xlink:href="#B"><stop offset="0" stop-color="#4e4445"></stop><stop offset="1" stop-color="#7b7777"></stop></linearGradient></defs><path fill="url(#Cm)" d="M773 335c0-2 1-3 1-4l1 5v4l1 7h0c1 11 3 19 11 26 2 2 5 3 7 6v1h0l1 1-1 1c-2-1-3-2-5-3l-1 1c-1-1-3-4-5-4l-1 1h-2l-1 4c-1-1-2-1-3-1h0l-2-1c1 0 0 0 1-1h-2-3l1-1-1-1 1-1 1-1-1-1 1-1v-2c1-1 2-1 3-1 0-1-2-3-3-4 0-1-1-1-2-2 0-1-1-1-2-2 2-5 4-11 4-16 1-3 1-7 1-10z"></path><defs><linearGradient id="Cn" x1="769.466" y1="346.599" x2="774.151" y2="348.099" xlink:href="#B"><stop offset="0" stop-color="#292324"></stop><stop offset="1" stop-color="#463c3b"></stop></linearGradient></defs><path fill="url(#Cn)" d="M773 335c0-2 1-3 1-4l1 5v4l1 7c-2 3-3 6-4 9v2 3c2 4 5 7 9 10h-2l-4-2c0-1-2-3-3-4 0-1-1-1-2-2 0-1-1-1-2-2 2-5 4-11 4-16 1-3 1-7 1-10z"></path><path d="M772 372v-2c1-1 2-1 3-1l4 2h2l2 2h0c0 1 1 2 2 2 0 1 1 1 2 2 1 0 2 1 2 2l-1 1c-1-1-3-4-5-4l-1 1h-2l-1 4c-1-1-2-1-3-1h0l-2-1c1 0 0 0 1-1h-2-3l1-1-1-1 1-1 1-1-1-1 1-1z" class="B"></path><path d="M780 377l-1 4c-1-1-2-1-3-1h0v-1c1 0 2-1 4-2z" class="P"></path><path d="M772 372v-2c1-1 2-1 3-1l4 2h2l2 2h0c-2 2-5 2-8 2h-4l1-1-1-1 1-1z" class="Q"></path><path d="M772 372v-2c1-1 2-1 3-1l4 2-7 1z" class="I"></path><defs><linearGradient id="Co" x1="227.247" y1="296.417" x2="194.562" y2="312.673" xlink:href="#B"><stop offset="0" stop-color="#514647"></stop><stop offset="1" stop-color="#9c9f9f"></stop></linearGradient></defs><path fill="url(#Co)" d="M180 278c1 4 1 6 4 9h0c7 6 18 11 27 12h2 6l1 2v1h0v1h5l6-1v-1-1l-1-1 1-1c2 3 6 6 7 9 1 2 3 3 3 4l1 3h-1c-1 0-1 0-2 1h1c-2 2-6 3-9 5-5 2-11 4-16 5-2 0-3-1-4-1h-1c1 0 2 0 3-1s1-2 1-4h0-1v-3s-1-1-2-1h0c-2-1-4 0-5-1-1 0-1-1-2-1h-1l-1-1h1 3 1c-6-2-12-3-17-7l-3-3c1 0 0 0 1-1 0-2-3-5-4-7l-2-1-3-2c-2-1-7-3-8-5h0l-3-2 2-1h0 2 3c3-1 4-4 5-5z"></path><path d="M213 304h3c0 1 1 1 2 2h0-1l-3 1v1c-1 0-2-1-2-1v-1h2v-1h-1v-1z" class="Z"></path><path d="M184 294h1c2 2 2 3 4 5l3 3-2 2v1l-3-3c1 0 0 0 1-1 0-2-3-5-4-7z" class="R"></path><defs><linearGradient id="Cp" x1="227.85" y1="299.758" x2="220.297" y2="308.261" xlink:href="#B"><stop offset="0" stop-color="#433232"></stop><stop offset="1" stop-color="#62595a"></stop></linearGradient></defs><path fill="url(#Cp)" d="M231 301v-1l-1-1 1-1c2 3 6 6 7 9v1 2l1 1c-2 1-4 1-6 1h0l1-1h1c-1 0-2 0-3-1 0 0-1-1-2-1h-3c-3 2-11 0-14 0h0c4-1 6 0 10 0-1-1-1-1-1-2l-1-1h-3c-1-1-2-1-2-2l3-1h1 5l6-1v-1z"></path><path d="M227 309c4-2 8 0 11 1h0l1 1c-2 1-4 1-6 1h0l1-1h1c-1 0-2 0-3-1 0 0-1-1-2-1h-3z" class="O"></path><path d="M238 310v-2-1c1 2 3 3 3 4l1 3h-1c-1 0-1 0-2 1h1c-2 2-6 3-9 5-5 2-11 4-16 5-2 0-3-1-4-1h-1c1 0 2 0 3-1s1-2 1-4h0-1v-3s-1-1-2-1h0c-2-1-4 0-5-1-1 0-1-1-2-1h-1l-1-1h1 3 1c8 2 18 2 26 0 2 0 4 0 6-1l-1-1z" class="x"></path><defs><linearGradient id="Cq" x1="199.249" y1="305.802" x2="191.568" y2="277.013" xlink:href="#B"><stop offset="0" stop-color="#ababaa"></stop><stop offset="1" stop-color="#dddbdb"></stop></linearGradient></defs><path fill="url(#Cq)" d="M180 278c1 4 1 6 4 9h0c7 6 18 11 27 12h2 6l1 2v1h0v1h-1l-3 1h-3v1h1v1h-2v1c-1 0-1 0-1 1l-3-2 1-1-1-2h-1c-1 1-3 0-5-1-6-2-11-5-16-9l-1 1h-1l-2-1-3-2c-2-1-7-3-8-5h0l-3-2 2-1h0 2 3c3-1 4-4 5-5z"></path><path d="M208 303h7 4l-3 1h-3-2v1h0-2l-1-2z" class="Q"></path><path d="M209 305h2 0v-1h2v1h1v1h-2v1c-1 0-1 0-1 1l-3-2 1-1z" class="f"></path><path d="M180 290c2 0 4 2 6 3l-1 1h-1l-2-1-3-2 1-1z" class="C"></path><path d="M213 299h6l1 2v1h0v1h-1-4c1-1 1-1 1-2l-3-2z" class="I"></path><path d="M170 283h0 2l5 2c1 0 1 0 2 1h1l1 2h-1v2l-1 1c-2-1-7-3-8-5h0l-3-2 2-1z" class="L"></path><defs><linearGradient id="Cr" x1="638.338" y1="203.444" x2="688.213" y2="211.715" xlink:href="#B"><stop offset="0" stop-color="#b8b7b7"></stop><stop offset="1" stop-color="#d8d8d7"></stop></linearGradient></defs><path fill="url(#Cr)" d="M652 198c4 1 11 1 15 3l8 3h4 3 2c2 1 4 3 6 4 5 4 12 9 14 14v2l3 2-1 1c-2 1-3 0-5-1-1-2-4-3-5-5-3-2-5-5-9-5l3 3h-2v-1c-1 0-2 0-3-1h0l-1 1h-2c-5-4-10-7-17-9h-5c-1 0-2 1-3 2h-1l-1 4-3 3c1 1 1 2 1 4l-2 1 1 2 4-2h1c-2 1-2 2-3 3 1 0 1 1 2 1-3 2-8 1-12 2h-1c-3 1-5 1-7 1h-1c-1 1-1 1-2 1s-2 0-3-1h0 2c1-1 2-2 2-4 0-3 0-5-1-8h3c-1-2-1-2-1-3 1-1 2-1 2-1 2-1 3-2 4-2l-1-2-1 1c-2-1-3-1-4-3h1c-1-1-4-2-5-2h0l-1-1-1-1h1l3 1c4 1 7 1 10-1v-1l1-1h-1v-3h3 1l5-1z"></path><path d="M684 208l6 3c1 4 6 7 6 10-3-2-5-5-9-5-2-1-4-3-6-4h3c1 0 2 0 3 1h1c0-1-1-1-1-2l-3-3z" class="L"></path><path d="M652 198c4 1 11 1 15 3h-1c-4 1-8 0-12 1-6 1-12 6-18 6-1-1-4-2-5-2h0l-1-1-1-1h1l3 1c4 1 7 1 10-1v-1l1-1h-1v-3h3 1l5-1z" class="Q"></path><path d="M646 199h1v1l-3 2h-1v-3h3z" class="W"></path><path d="M658 206c3-1 6 0 10 0 5 2 9 4 13 6 2 1 4 3 6 4l3 3h-2v-1c-1 0-2 0-3-1h0l-1 1h-2c-5-4-10-7-17-9 0 0-1-1-2-1h-1s-1-1-2-1h-1l-1-1z" class="r"></path><path d="M651 207l7-1 1 1h1c1 0 2 1 2 1h1c1 0 2 1 2 1h-5c-1 0-2 1-3 2h-1-3c-3 1-6 1-9 3-3 1-5 3-8 4-1-2-1-2-1-3 1-1 2-1 2-1 2-1 3-2 4-2l-1-2 11-3z" class="U"></path><path d="M640 210l11-3s1 1 1 2l-4 1h-3l-4 2-1-2z" class="V"></path><path d="M651 207l7-1 1 1h1c1 0 2 1 2 1h1c1 0 2 1 2 1h-5c-1-1-6 0-8 0 0-1-1-2-1-2z" class="o"></path><path d="M675 204h4 3 2c2 1 4 3 6 4 5 4 12 9 14 14v2l3 2-1 1c-2 1-3 0-5-1-1-2-4-3-5-5 0-3-5-6-6-10l-6-3-3-2-6-2z" class="Q"></path><path d="M675 204h4 3 2c2 1 4 3 6 4h0c-3-1-6-2-9-2l-6-2z" class="f"></path><path d="M690 211c5 2 11 9 14 13l3 2-1 1c-2 1-3 0-5-1-1-2-4-3-5-5 0-3-5-6-6-10z" class="C"></path><path d="M653 211h3l-1 4-3 3c1 1 1 2 1 4l-2 1 1 2 4-2h1c-2 1-2 2-3 3 1 0 1 1 2 1-3 2-8 1-12 2h-1c-3 1-5 1-7 1h-1c-1 1-1 1-2 1s-2 0-3-1h0 2c1-1 2-2 2-4 0-3 0-5-1-8h3c3-1 5-3 8-4 3-2 6-2 9-3z" class="w"></path><path d="M653 211h3l-1 4-3 3h0v-1-1h1c1-1 1-1 1-2h-3 1 1c1-1 1-2 1-3h-1z" class="h"></path><path d="M652 218h0c1 1 1 2 1 4l-2 1 1 2 4-2h1c-2 1-2 2-3 3 1 0 1 1 2 1-3 2-8 1-12 2h-1c-3 1-5 1-7 1h-1l13-8 2-1c0-1 1-2 2-3z" class="I"></path><path d="M650 221c0 1 0 2 1 2l1 2c-2 0-2 0-4-1v-2l2-1z" class="J"></path><path d="M652 218h0c1 1 1 2 1 4l-2 1c-1 0-1-1-1-2s1-2 2-3z" class="F"></path><defs><linearGradient id="Cs" x1="698.029" y1="308.66" x2="664.284" y2="308.108" xlink:href="#B"><stop offset="0" stop-color="#2d100f"></stop><stop offset="1" stop-color="#4a3838"></stop></linearGradient></defs><path fill="url(#Cs)" d="M681 282c0-2 0-4-1-5v-2c1 1 2 1 3 2 8 7 12 20 14 30 0 1 0 6 1 7v29l-1 12c0 1 0 3-1 4l-2 2c-3 6-7 9-13 13-2 1-4 1-6 1-2 1-4 2-6 2h-7c-5-2-8-4-11-8l-3-4c-3-6-4-15-2-22 0-2 2-5 2-7v-1l1-1c0-2 1-2 1-4l8-11c2-1 3-1 4 0h1c2-3 2-5 2-8v-1l1-1c1 1 0 3 1 4h0c5-7 11-16 13-24 1-2 1-5 1-7z"></path><path d="M685 314h3c0 2 0 2-1 3l-3-1h0l1-1h-1l1-1z" class="Y"></path><path d="M680 296v-1c0-2 1-4 2-6 1 2 1 2 1 4-2 1-2 2-3 3z" class="s"></path><path d="M679 308l1-2c2 0 3 2 4 3v1 1l-2-1c-1-1-2-2-3-2z" class="Y"></path><path d="M679 308c1 0 2 1 3 2l2 1-2 2v-1c-1 0-2 0-3-1 0-1-1-1-2-1l2-2z" class="U"></path><path d="M676 311c-2 4-4 8-5 12l-3 12h-1c0-4 0-7 1-11h0c-1 3-1 6 0 8h0v-1c0-2 0-3 1-4v-2l1-2v-2l1-2c1-2 1-3 2-5l3-3z" class="Y"></path><path d="M666 309c1 1 0 3 1 4h0c0 2-1 4-2 5 0 2-1 5-1 6l-1-1-2 6-1-7 1-1h0l1-2h1c2-3 2-5 2-8v-1l1-1z" class="J"></path><path d="M683 293c3 2 7 3 9 6 1 1 2 3 2 4l-7-3c-2-1-5-1-6-3l-1-1c1-1 1-2 3-3z" class="O"></path><path d="M679 334c2-2 4-5 5-7 0 5 4 11 8 14 2 1 4 2 5 3-1 1-2 1-4 0-5-1-7 0-11 4v-1c2-2 4-4 5-6h1l-5-3c-1-2-2-3-4-4z" class="U"></path><path d="M679 334c2 1 3 2 4 4l5 3h-1c-1 2-3 4-5 6v1l-1 2c0 1-1 2-1 3v3l-2-1c-3-1-5-3-8-5 0-1-1-2-2-3l2-3c3-4 5-7 9-10z" class="r"></path><path d="M683 338l5 3h-1c-1 2-3 4-5 6v1l-1 2c0 1-1 2-1 3-1 0-1-1-1-1-1-2-1-5-1-7l1-1 1-1c1-2 1-3 3-4v-1z" class="V"></path><path d="M679 344l1-1c1-2 1-3 3-4l2 1c-1 1-3 2-4 3s-1 1-2 1z" class="e"></path><path d="M680 353c-1 0-1-1-1-1-1-2-1-5-1-7l1 3h1v-1l1 1v2c0 1-1 2-1 3z" class="K"></path><defs><linearGradient id="Ct" x1="670.071" y1="314.432" x2="677.937" y2="328.505" xlink:href="#B"><stop offset="0" stop-color="#6f6c6e"></stop><stop offset="1" stop-color="#8d8c8d"></stop></linearGradient></defs><path fill="url(#Ct)" d="M684 310c1 1 1 3 1 4l-1 1h1l-1 1h0l3 1-1 2-2 8c-1 2-3 5-5 7-4 3-6 6-9 10 0-2 0-3-1-4l2-3c-2 0-3 1-4 2h-1l1-4h1l3-12c1-4 3-8 5-12l1-1c1 0 2 0 2 1 1 1 2 1 3 1v1l2-2v-1z"></path><defs><linearGradient id="Cu" x1="673.01" y1="317.953" x2="679.898" y2="331.845" xlink:href="#B"><stop offset="0" stop-color="#4f4645"></stop><stop offset="1" stop-color="#656568"></stop></linearGradient></defs><path fill="url(#Cu)" d="M684 310c1 1 1 3 1 4l-1 1h1l-1 1h0l-1 3c-2 3-3 6-5 9-1 3-3 5-5 7 0 0-1 2-2 2-2 0-3 1-4 2h-1l1-4h1 1c1 0 1-1 2-2 4-6 8-13 11-20l2-2v-1z"></path><defs><linearGradient id="Cv" x1="687.401" y1="325.238" x2="666.995" y2="335.557" xlink:href="#B"><stop offset="0" stop-color="#6c6b6d"></stop><stop offset="1" stop-color="#a2a0a2"></stop></linearGradient></defs><path fill="url(#Cv)" d="M684 316l3 1-1 2-2 8c-1 2-3 5-5 7-4 3-6 6-9 10 0-2 0-3-1-4l2-3c1 0 2-2 2-2 2-2 4-4 5-7 2-3 3-6 5-9l1-3z"></path><path d="M684 316l3 1-1 2h-3l1-3z" class="Z"></path><defs><linearGradient id="Cw" x1="679.636" y1="353.534" x2="645.384" y2="346.498" xlink:href="#B"><stop offset="0" stop-color="#b9b7b8"></stop><stop offset="1" stop-color="#e6e5e5"></stop></linearGradient></defs><path fill="url(#Cw)" d="M658 319c2-1 3-1 4 0l-1 2h0l-1 1 1 7 2-6 1 1c-1 5-1 10 0 15h2 1c1-1 2-2 4-2l-2 3c1 1 1 2 1 4l-2 3c1 1 2 2 2 3 3 2 5 4 8 5l2 1v-3c0-1 1-2 1-3l1-2c4-4 6-5 11-4 2 1 3 1 4 0l1-1-1 12c0 1 0 3-1 4l-2 2c-3 6-7 9-13 13-2 1-4 1-6 1-2 1-4 2-6 2h-7c-5-2-8-4-11-8l-3-4c-3-6-4-15-2-22 0-2 2-5 2-7v-1l1-1c0-2 1-2 1-4l8-11z"></path><path d="M669 373c-3 0-4 0-6-2h1c1-1 2-1 3 0s1 1 3 1l-1 1z" class="G"></path><path d="M664 367c-2 0-4 0-6-1 5 0 10-1 15-2 0 1 1 2 0 3s-1 1-2 0c-2-1-4 0-7 0z" class="F"></path><path d="M664 367c3 0 5-1 7 0 1 1 1 1 2 0l2 1c-1 1-1 0-1 1l1 1c-1 1-3 1-4 1-1 1-1 0-2 0v-1h0v-1h-2l-3-2z" class="B"></path><path d="M664 363h-3-1c-2 0-2 0-4-1h0c-2-1-3-2-4-4h-1c0-2-1-3-1-4v-1l-1-1c0-2 0-5 2-7-1 4-1 10 2 14 1 1 3 2 5 2 2 1 4 1 6 2z" class="n"></path><path d="M658 319c2-1 3-1 4 0l-1 2h0c-2 0-3 1-5 2v1c0 1-1 3-2 4-2 2-4 6-6 7l1-1c0-2 1-2 1-4l8-11z" class="G"></path><path d="M678 359h0 7l1 1-3 1-8 3c1-1 1-2 3-2h0c-1-1-2 0-3 0-3-1-6-1-9-1l-1-1c2 0 5-1 8-1h5z" class="B"></path><defs><linearGradient id="Cx" x1="680.114" y1="365.251" x2="670.253" y2="373.31" xlink:href="#B"><stop offset="0" stop-color="#807d7e"></stop><stop offset="1" stop-color="#b6b4b5"></stop></linearGradient></defs><path fill="url(#Cx)" d="M683 361h2c-1 1-3 2-5 3 0 2 1 2 1 3s0 2 1 3c-3 2-5 3-7 5-2 1-4 2-6 2v-1l2-2c-1 0-1 0-2-1h0l1-1c-2 0-2 0-3-1h2c1 0 1 1 2 0 1 0 3 0 4-1l-1-1c0-1 0 0 1-1l-2-1c1-1 0-2 0-3h2l8-3z"></path><defs><linearGradient id="Cy" x1="691.092" y1="363.41" x2="681.851" y2="359.783" xlink:href="#B"><stop offset="0" stop-color="#6c6668"></stop><stop offset="1" stop-color="#878687"></stop></linearGradient></defs><path fill="url(#Cy)" d="M697 344l1-1-1 12c0 1 0 3-1 4l-2 2c-3 6-7 9-13 13-2 1-4 1-6 1 2-2 4-3 7-5-1-1-1-2-1-3s-1-1-1-3c2-1 4-2 5-3h-2l3-1-1-1h-7 0-5v-1l-1-1-2-1c3 0 6 0 8-1l2 1v-3c0-1 1-2 1-3l1-2c4-4 6-5 11-4 2 1 3 1 4 0z"></path><path d="M678 355l2 1h10v1l-4 3 1 1 1-1h1c0 1-1 2-2 3h0v-1l-1-1h-1-2l3-1-1-1h-7 0-5v-1l-1-1-2-1c3 0 6 0 8-1z" class="Q"></path><path d="M672 357c2 0 4 0 5 1l1 1h-5v-1l-1-1z" class="M"></path><path d="M695 357l-1 4c-3 6-7 9-13 13-2 1-4 1-6 1 2-2 4-3 7-5 5-3 10-8 13-13z" class="q"></path><path d="M697 344l1-1-1 12c0 1 0 3-1 4l-2 2 1-4c1-1 1-2 0-3-2 0-4 1-5 2h-10v-3c0-1 1-2 1-3l1-2c4-4 6-5 11-4 2 1 3 1 4 0z" class="u"></path><path d="M660 322l1 7 2-6 1 1c-1 5-1 10 0 15h2 1c1-1 2-2 4-2l-2 3c1 1 1 2 1 4l-2 3c1 1 2 2 2 3 3 2 5 4 8 5-2 1-5 1-8 1l2 1 1 1v1c-3 0-6 1-8 1l1 1-2 2c-2-1-4-1-6-2-2 0-4-1-5-2-3-4-3-10-2-14l1-4c0-3 2-5 3-8 0-1 4-9 5-11z" class="Y"></path><path d="M658 361c2 0 4 0 7-1l1 1-2 2c-2-1-4-1-6-2z" class="P"></path><path d="M652 348c0-4 1-8 2-11v3l1-1v1l-2 5v2s0 1-1 1h0z" class="B"></path><path d="M658 355v-1h1 1c1 1 2 1 4 1l-2 2h-1v1c-1 1-1 1-1 2h-1v-3l-1-1v-1z" class="U"></path><path d="M652 354h1v-3l1 3c1 2 1 3 3 4 1-1 1-1 1-2l1 1v3c-2 0-3 0-5-2-1-1-2-2-2-4z" class="N"></path><path d="M664 349c2 0 3 0 4 1 1 0 1 1 2 0 3 2 5 4 8 5-2 1-5 1-8 1-2-2-4-5-6-7z" class="C"></path><path d="M660 341c1 4 3 8 3 12-1 0-2 1-4 1h-1v1c-1-2-1-8 0-10s2-1 2-4z" class="N"></path><defs><linearGradient id="Cz" x1="661.209" y1="326.275" x2="652.291" y2="338.225" xlink:href="#B"><stop offset="0" stop-color="#7b797a"></stop><stop offset="1" stop-color="#a7a5a5"></stop></linearGradient></defs><path fill="url(#Cz)" d="M660 322l1 7c0 2-1 5-1 7v-5l-1 2-1 1-3 6v-1l-1 1v-3c1-2 1-2 1-4 0-1 4-9 5-11z"></path><path d="M658 334l1-1c0 3 1 6 1 8 0 3-1 2-2 4s-1 8 0 10v1c0 1 0 1-1 2-2-1-2-2-3-4l-1-3v3h-1c0-2-1-4 0-6h0c1 0 1-1 1-1v-2l2-5 3-6z" class="Q"></path><path d="M653 345l1 1h1l2-3v1c-1 1 0 0 0 1v2l-3 4c0-2 0-3-1-4v-2z" class="J"></path><path d="M655 340l3-6-1 9-2 3h-1l-1-1 2-5z" class="W"></path><path d="M661 329l2-6 1 1c-1 5-1 10 0 15h2 1c1-1 2-2 4-2l-2 3c1 1 1 2 1 4l-2 3c1 1 2 2 2 3-1 1-1 0-2 0-1-1-2-1-4-1-2-4-3-8-4-13 0-2 1-5 1-7z" class="E"></path><path d="M666 339h1c1-1 2-2 4-2l-2 3c1 1 1 2 1 4l-2 3-2-3-2-5h2z" class="e"></path><path d="M669 340c1 1 1 2 1 4l-2 3-2-3c1-1 2-3 3-4z" class="B"></path><defs><linearGradient id="DA" x1="670.354" y1="207.817" x2="676.451" y2="180.864" xlink:href="#B"><stop offset="0" stop-color="#d2d1d1"></stop><stop offset="1" stop-color="#f7f6f5"></stop></linearGradient></defs><path fill="url(#DA)" d="M619 167l1 1c4 0 7 0 10 1 3-1 7-1 9-1 3 0 12-2 15 0 2 2 6 2 9 2h4s1 1 2 1c2 1 4 1 6 2h1c3 1 5 1 8 2 1 1 3 2 3 2 4 1 7 3 10 5 2 1 6 5 8 5 1 1 3 3 4 3l10 10c1 1 2 2 3 4l3-1c0 1 1 2 2 3l1 2 2 2v1c2 3 5 6 6 9l-1 1 1 1-1 1h-1l-1 1c1 2 2 2 2 5-1 1-1 1-2 1l-1 1h-2l1 1h-11v-1l-4-3-1 1c-1 0-2 1-3 0-2-1-4-3-5-5l-3-2c-2-5-9-10-14-14-2-1-4-3-6-4h-2-3-4l-8-3c-4-2-11-2-15-3l-5 1h-1-3v3h1l-1 1v1c-3 2-6 2-10 1l-3-1h-1 0l-2-1c-4-3-6-8-8-12l-3-3-1 1 1 2c-2 0-3-2-5-3-2-3-2-4-2-7l1-1 3-6 2-2c1-2 1-3 1-5h3z"></path><path d="M661 181l2 1v2l-2-1v-2z" class="R"></path><path d="M647 179l8 1-3 1-6-1h3l-2-1h0z" class="G"></path><path d="M663 182c1 0 6 1 6 2 0 0 1 1 0 2l-6-2v-2z" class="B"></path><path d="M634 176h-2 0c2-1 4-2 6-2 2 1 3 1 5 1h2c-3 1-7 0-11 1z" class="n"></path><path d="M655 180l6 1v2c-2-1-7-1-9-2l3-1z" class="D"></path><path d="M633 180l2-1c3-1 9-2 12 0h0l2 1h-3-7 0-1-5z" class="n"></path><path d="M697 191l7 5 6 5h0l-1 1-2-2h0c-1 0-2-1-2-1l-2-1-1-1c-2-1-4-4-5-6z" class="d"></path><path d="M669 184c8 2 16 6 23 10 2 2 5 3 7 5-4 0-9-4-12-5-5-4-12-6-18-8 1-1 0-2 0-2z" class="F"></path><defs><linearGradient id="DB" x1="696.091" y1="178.979" x2="673.078" y2="187.955" xlink:href="#B"><stop offset="0" stop-color="#8d8787"></stop><stop offset="1" stop-color="#b8b9b9"></stop></linearGradient></defs><path fill="url(#DB)" d="M673 177c7 2 14 5 21 8 3 1 6 2 8 4l-1 1v1 1c1 0 2 0 2 1l1 1v2l-7-5c-9-6-20-10-31-13v-1h1 6z"></path><defs><linearGradient id="DC" x1="721.649" y1="225.209" x2="726.793" y2="218.111" xlink:href="#B"><stop offset="0" stop-color="#9f9d9c"></stop><stop offset="1" stop-color="#c4c3c4"></stop></linearGradient></defs><path fill="url(#DC)" d="M704 204h1c4 3 7 6 11 9 2 3 4 5 7 7 0-1 0-2-1-3h0c-1-1 0 0-1-2l-1-1 1-1v-1c-1 0-2-1-2-2-2-1-2-2-2-3 4 4 8 9 11 15 1 2 3 4 3 7 0 1 0 1-1 1l-3 1c-3-2-7-9-10-12-2-3-5-7-8-9-2-2-4-3-5-6z"></path><path d="M705 211l1-1-4-3 1-1c1 1 3 3 5 4h1c3 2 6 6 8 9 3 3 7 10 10 12h3l1 1h-11v-1l-4-3-2-4c-1-1-2-3-3-4-1-3-4-6-6-9z" class="t"></path><defs><linearGradient id="DD" x1="733.129" y1="214.681" x2="718.694" y2="217.384" xlink:href="#B"><stop offset="0" stop-color="#544d4d"></stop><stop offset="1" stop-color="#7b7677"></stop></linearGradient></defs><path fill="url(#DD)" d="M702 189l9 6c2 2 4 5 8 5 1 1 2 2 3 4l3-1c0 1 1 2 2 3l1 2 2 2v1c2 3 5 6 6 9l-1 1 1 1-1 1h-1l-1 1c1 2 2 2 2 5-1 1-1 1-2 1l-1 1h-2-3l3-1c1 0 1 0 1-1 0-3-2-5-3-7-3-6-7-11-11-15l-1-1-6-5h0l-6-5v-2l-1-1c0-1-1-1-2-1v-1-1l1-1z"></path><path d="M726 209c0-2 0-2 1-3l1 2c-1 2 0 4 1 6l-1 1-1-2h0c0-2-1-3-1-4z" class="Z"></path><path d="M730 211c2 3 5 6 6 9l-1 1c-3-2-3-6-5-9h-1l1-1z" class="V"></path><path d="M702 189l9 6c2 2 4 5 8 5 1 1 2 2 3 4l3-1c0 1 1 2 2 3-1 1-1 1-1 3 0 1 1 2 1 4h0c-4-3-7-8-11-11l-1 1c1 1 1 1 1 3h0l-6-5h0l-6-5v-2l-1-1c0-1-1-1-2-1v-1-1l1-1z" class="J"></path><path d="M725 203c0 1 1 2 2 3-1 1-1 1-1 3-1-2-2-4-4-5l3-1z" class="N"></path><path d="M619 167l1 1c4 0 7 0 10 1 3-1 7-1 9-1 3 0 12-2 15 0 2 2 6 2 9 2h4s1 1 2 1c2 1 4 1 6 2h1c3 1 5 1 8 2 1 1 3 2 3 2 4 1 7 3 10 5 2 1 6 5 8 5 1 1 3 3 4 3l10 10c-4 0-6-3-8-5l-9-6c-2-2-5-3-8-4-7-3-14-6-21-8h-6-1v1c-1 0-5-1-7-1-1 0-3 0-4-1l-10-1h-2c-2 0-3 0-5-1-2 0-4 1-6 2h0 2c-2 2-5 2-8 3l-3 3c0 1 0 1-1 2s-1 2-2 3l1 1c1 0 1 0 1-1 2-4 7-7 11-7h0 5 1 0c-4 1-10 2-13 5-2 2-2 3-3 5v2h-1c-1-1-2-2-2-4v-4c-1-1-1-2-1-4-2 5-1 7 0 11l-3-3-1 1 1 2c-2 0-3-2-5-3-2-3-2-4-2-7l1-1 3-6 2-2c1-2 1-3 1-5h3z" class="p"></path><path d="M621 177c2-1 3-2 5-3 5-3 13-5 19-5h-1c-1 1-2 1-3 1l-5 1c-2 1-3 1-5 2-1 1-2 2-3 2h-1c-1 1-2 1-3 1l-3 3h1c-1 3-2 6-2 9v-4c-1-1-1-2-1-4 1-1 2-2 2-3z" class="H"></path><path d="M638 174c5-1 10-1 16 0 3 0 6-1 9 0h2c2 1 5 1 8 3h-6-1v1c-1 0-5-1-7-1-1 0-3 0-4-1l-10-1h-2c-2 0-3 0-5-1z" class="L"></path><path d="M659 177l2-1h6v1h-1v1c-1 0-5-1-7-1z" class="E"></path><path d="M619 167l1 1c4 0 7 0 10 1 3-1 7-1 9-1 3 0 12-2 15 0-3 0-7 0-9 1-6 0-14 2-19 5-2 1-3 2-5 3 0 1-1 2-2 3-2 5-1 7 0 11l-3-3-1 1 1 2c-2 0-3-2-5-3-2-3-2-4-2-7l1-1 3-6 2-2c1-2 1-3 1-5h3z" class="C"></path><path d="M622 171h2c-2 1-3 2-5 3h-3c2-2 4-2 6-3z" class="E"></path><path d="M616 174h3c-2 3-5 5-7 8v1c0-1-1-2 0-3 0-2 2-5 4-6z" class="j"></path><path d="M610 180v1l2 6c1-2 2-5 4-7-1 2-1 4-1 5s-1 1-1 1c0 1 0 2 1 3l1 2c-2 0-3-2-5-3-2-3-2-4-2-7l1-1z" class="I"></path><path d="M616 180c1-1 3-2 4-3h1c0 1-1 2-2 3-2 5-1 7 0 11l-3-3-1 1c-1-1-1-2-1-3 0 0 1 0 1-1s0-3 1-5z" class="M"></path><path d="M619 167l1 1c4 0 7 0 10 1-1 0-5 2-6 2h-2c-1-1-2-1-3 0-2 1-4 2-5 3h-1l2-2c1-2 1-3 1-5h3z" class="D"></path><path d="M619 167l1 1c-1 1-3 2-5 4 1-2 1-3 1-5h3z" class="B"></path><path d="M639 180h7l6 1c2 1 7 1 9 2l2 1 6 2c6 2 13 4 18 8 3 1 8 5 12 5l5 5c1 3 3 4 5 6h-1c-2-1-4-3-5-4l-1 1 4 3-1 1c2 3 5 6 6 9 1 1 2 3 3 4l2 4-1 1c-1 0-2 1-3 0-2-1-4-3-5-5l-3-2c-2-5-9-10-14-14-2-1-4-3-6-4h-2-3-4l-8-3c-4-2-11-2-15-3l-5 1h-1-3v3h1l-1 1v1c-3 2-6 2-10 1l-3-1h-1 0l-2-1c-4-3-6-8-8-12-1-4-2-6 0-11 0 2 0 3 1 4v4c0 2 1 3 2 4h1v-2c1-2 1-3 3-5 3-3 9-4 13-5z" class="F"></path><path d="M642 190c2-2 5-2 7-3 1 0 2 0 3 1l-1 1c-3 1-6 1-9 1z" class="M"></path><path d="M637 195h2l2-2c2-2 6-2 9-2l-1 1v1l2 1c-1 0-2 1-3 1l-9 2h-3l1-2z" class="j"></path><path d="M656 185l4 1 8 2c-6 1-12 1-17 1l1-1c-1-1-2-1-3-1 2-2 4-2 7-2z" class="Q"></path><path d="M656 185l4 1c-1 0-1 0-2 1-2 1-4 1-6 1-1-1-2-1-3-1 2-2 4-2 7-2z" class="N"></path><path d="M635 194c2-2 4-3 7-4-2 2-4 3-5 5l-1 2h3l9-2h4v1h3v1h-1c-2 0-6 0-8 2h-3c-1 0-3 0-4-1-1 0-1 1-2 0h-1-1-1l1-1-1-1 1-2z" class="Q"></path><path d="M687 194c3 1 8 5 12 5l5 5c1 3 3 4 5 6h-1c-2-1-4-3-5-4l-1 1 4 3-1 1-5-5c-5-5-11-8-17-12 1 0 2 0 3 1l1-1z" class="X"></path><path d="M650 191h11c5 1 10 2 14 4s6 5 9 7l-1 1 1 1h-2-3-4l-8-3c-4-2-11-2-15-3l-5 1h-1c2-2 6-2 8-2h1v-1h-3v-1h-4c1 0 2-1 3-1l-2-1v-1l1-1z" class="S"></path><path d="M651 193h6l7 1-2 1h1c1 0 0 0 1 1 2-1 3 0 4 1l-10-2h-6-4c1 0 2-1 3-1v-1z" class="D"></path><path d="M651 193c3 0 4 0 6 1l1 1h-6-4c1 0 2-1 3-1v-1z" class="C"></path><path d="M658 195l10 2 9 3 6 3 1 1h-2-3-4l-8-3c-4-2-11-2-15-3l-5 1h-1c2-2 6-2 8-2h1v-1h-3v-1h6z" class="U"></path><path d="M658 195l10 2 9 3c-2 1-11-2-13-2-3-1-5-1-7-1-2 1-4 1-5 1l-5 1h-1c2-2 6-2 8-2h1v-1h-3v-1h6z" class="q"></path><defs><linearGradient id="DE" x1="697.339" y1="219.28" x2="693.661" y2="196.22" xlink:href="#B"><stop offset="0" stop-color="#868686"></stop><stop offset="1" stop-color="#aca9ab"></stop></linearGradient></defs><path fill="url(#DE)" d="M675 195c4 0 7 2 10 3 4 2 7 3 10 6h2c1 1 2 1 3 2l5 5c2 3 5 6 6 9 1 1 2 3 3 4l2 4-1 1c-1 0-2 1-3 0-2-1-4-3-5-5l-3-2c-2-5-9-10-14-14-2-1-4-3-6-4l-1-1 1-1c-3-2-5-5-9-7z"></path><defs><linearGradient id="DF" x1="699.778" y1="223.498" x2="697.29" y2="205.493" xlink:href="#B"><stop offset="0" stop-color="#8e8786"></stop><stop offset="1" stop-color="#b2b5b7"></stop></linearGradient></defs><path fill="url(#DF)" d="M684 202c5 3 9 5 14 9 0 0 2 2 3 2 2 0 5 3 6 5h0l3 2h1c1 1 2 3 3 4l2 4-1 1c-1 0-2 1-3 0-2-1-4-3-5-5l-3-2c-2-5-9-10-14-14-2-1-4-3-6-4l-1-1 1-1z"></path><path d="M701 213c2 0 5 3 6 5h0l3 2h1c1 1 2 3 3 4l2 4-1 1c-1 0-2 1-3 0-2-1-4-3-5-5 0 0 1-1 1-2-2-3-4-6-7-9z" class="V"></path><path d="M708 222c1 2 4 5 4 7-2-1-4-3-5-5 0 0 1-1 1-2z" class="i"></path><path d="M707 218l3 2h1c1 1 2 3 3 4l-1 1c-3-2-4-4-6-7z" class="f"></path><defs><linearGradient id="DG" x1="654.084" y1="179.89" x2="655.245" y2="196.191" xlink:href="#B"><stop offset="0" stop-color="#321b1a"></stop><stop offset="1" stop-color="#352d2b"></stop></linearGradient></defs><path fill="url(#DG)" d="M639 180h7l6 1c2 1 7 1 9 2l2 1 6 2c6 2 13 4 18 8l-1 1c-1-1-2-1-3-1s-4-2-5-2c-3-2-7-3-10-4l-8-2-4-1c-3 0-5 0-7 2-2 1-5 1-7 3h0c-3 1-5 2-7 4l-1 2 1 1-1 1h1 1 1c1 1 1 0 2 0 1 1 3 1 4 1v3h1l-1 1v1c-3 2-6 2-10 1l-3-1h-1 0l-2-1c-4-3-6-8-8-12-1-4-2-6 0-11 0 2 0 3 1 4v4c0 2 1 3 2 4h1v-2c1-2 1-3 3-5 3-3 9-4 13-5z"></path><path d="M639 184c1-1 3 0 5 0l-2 2v-1c-1 0-1-1-3-1h0zm-6 17v-2-1c1 1 1 1 2 1l4 3-6-1z" class="V"></path><path d="M636 184h3 0c2 0 2 1 3 1l-2 2h0-1c-1-1-2-1-3-1l-2 2c0-2 0-2 2-4z" class="N"></path><path d="M639 184c2 0 2 1 3 1l-2 2h0l-1-3z" class="Z"></path><path d="M635 198h1 1c1 1 1 0 2 0 1 1 3 1 4 1v3h-4l-4-3v-1z" class="C"></path><path d="M627 192c1 3 2 4 3 6l1-1c0-1-1-1-1-2l2-1c-1-2-1-1 0-3v1 1c1 0 1 1 2 1h1l-1 2 1 1-1 1h1v1c-1 0-1 0-2-1v1 2h-1v-1c-2 0-4-1-5-4-1-1-1-2 0-4z" class="e"></path><path d="M627 192c0-1 1-2 1-4 2-2 5-3 8-4-2 2-2 2-2 4-1 2-1 3-2 5v-1-1c-1 2-1 1 0 3l-2 1c0 1 1 1 1 2l-1 1c-1-2-2-3-3-6z" class="Z"></path><path d="M644 184h2c3 0 6 0 10 1-3 0-5 0-7 2-2 1-5 1-7 3h0c-3 1-5 2-7 4h-1c-1 0-1-1-2-1 1-2 1-3 2-5l2-2c1 0 2 0 3 1h1 0l2-2v1l2-2z" class="V"></path><path d="M644 184h2v2l-4 2c-1 0-1-1-2-1l2-2v1l2-2z" class="O"></path><path d="M634 188l2-2c1 0 2 0 3 1l-4 5c-1 0-1 1-1 2-1 0-1-1-2-1 1-2 1-3 2-5z" class="W"></path><path d="M619 180c0 2 0 3 1 4v4c0 2 1 3 2 4h1v-2c1 4 1 7 5 9 3 3 10 5 15 4v1c-3 2-6 2-10 1l-3-1h-1 0l-2-1c-4-3-6-8-8-12-1-4-2-6 0-11z" class="P"></path><defs><linearGradient id="DH" x1="308.398" y1="480.485" x2="291.465" y2="480.549" xlink:href="#B"><stop offset="0" stop-color="#5b5659"></stop><stop offset="1" stop-color="#777477"></stop></linearGradient></defs><path fill="url(#DH)" d="M281 392l2-1 1 4c1 1 1 0 1 1 1 1 0 1 1 1 0 2 1 2 2 3l5 12 4 8c0 1 1 2 1 3l8 21 7 16 14 39c2 4 4 9 6 14 0 2 2 5 2 7v1l2 6v4h-1v1l1 1c2 1 2 2 3 3l5 15h2l12 29-1 1-2-3c-1 1-2 2-4 3-4-2-8-5-12-8v1l-1-2-8-4v-1l-18-12v1-1l-2-1-1 1c1 1 3 2 3 3l2 2c-1 0-2 0-3-1-2 0-5-3-7-5h-2l-1-1-1 2h1c1 1 1 1 2 3v1c-2-1-3-3-5-3l-8-9c-6-6-11-14-15-22-1-1-1-2-2-4 0-4-1-8-2-11l-2-8-1-3c-4-16-3-33 1-48 2-6 4-12 7-17v-6h-1c1-5 1-9 1-14-1-1 0-3-1-4v-2-1-1l-1 1v2h0l-1-2c-1 0-1 0-2 1h-4c2-1 5-2 7-2h1c3-1 5-1 8-1h1c1 0 1 0 2-1l-3-6-1-2v-1l-3-3h1z"></path><path d="M306 444l7 16h-1c0 1 1 2 1 3 0 3-3 6-4 8 0 1-1 3-1 4h0c-1 3-3 6-2 9v2h0v-1s-1 0-1-1v-1-1 6h-1 0c-1 0-1 0-1-1-2-1-4-4-4-6l-1-1c-1-5 1-10 2-15s2-9 4-13l1-2c1-2 1-5 1-6z" class="x"></path><path d="M304 488c1-2 1-9 1-12 1-6 4-11 6-16h1c0 1 1 2 1 3 0 3-3 6-4 8 0 1-1 3-1 4h0c-1 3-3 6-2 9v2h0v-1s-1 0-1-1v-1-1 6h-1 0z" class="e"></path><defs><linearGradient id="DI" x1="324.555" y1="502.702" x2="282.386" y2="507.251" xlink:href="#B"><stop offset="0" stop-color="#b3b2b0"></stop><stop offset="1" stop-color="#dcdbdc"></stop></linearGradient></defs><path fill="url(#DI)" d="M287 473l1 1v3c0 2-1 4 0 6 1-1 1-1 2-3l2-1c-1 3-2 6-1 9 0 4 3 11 5 15 2 2 3 4 5 5 1 1 5 3 5 4l4 3 3 2-1 2c5 4 10 8 17 11-1 1-1 2-2 3-4-2-8-3-11-5h-1c-3-1-6-3-10-5l-10-8-2-2c-1-1-3-1-4-1h-1c0-2-1-3 0-5l-2-2 1-1 1 1c0-2-1-4-2-6v-1l-1-2c-1-1-1-2-2-3v-2l-2-3c-1-3-2-6-2-10v-1l1-1h0c1 0 1-1 2-2 0 1 1 3 2 4 2-1 2-3 3-5z"></path><path d="M305 514c0-1 1-1 1-2l4 3 3 2-1 2c-3-1-5-4-7-5z" class="h"></path><path d="M296 503c2 2 3 4 5 5 1 1 5 3 5 4s-1 1-1 2c-4-3-7-5-10-9l1-2z" class="b"></path><path d="M286 499c1 1 2 2 2 4 2 4 5 7 9 10l-2 2-2-2c-1-1-3-1-4-1h-1c0-2-1-3 0-5l-2-2 1-1 1 1c0-2-1-4-2-6z" class="V"></path><path d="M288 507l5 6c-1-1-3-1-4-1h-1c0-2-1-3 0-5z" class="J"></path><path d="M297 513c6 5 12 11 19 15h-1c-3-1-6-3-10-5l-10-8 2-2z" class="O"></path><defs><linearGradient id="DJ" x1="284.105" y1="489.092" x2="290.955" y2="475.204" xlink:href="#B"><stop offset="0" stop-color="#332c2e"></stop><stop offset="1" stop-color="#534240"></stop></linearGradient></defs><path fill="url(#DJ)" d="M287 473l1 1v3c0 2-1 4 0 6 1-1 1-1 2-3l2-1c-1 3-2 6-1 9 0 4 3 11 5 15l-1 2c-2-3-2-6-4-9-4-7-9-13-11-20 1 0 1-1 2-2 0 1 1 3 2 4 2-1 2-3 3-5z"></path><path d="M313 460l14 39c2 4 4 9 6 14 0 2 2 5 2 7v1l2 6v4h-1v1l-7-2c-7-3-12-7-17-11l1-2-3-2v-1c-1-3 0-6 0-9l-1-17c-2-2-1-10-1-13-1 4-2 6-2 9-1-3 1-6 2-9h0c0-1 1-3 1-4 1-2 4-5 4-8 0-1-1-2-1-3h1z" class="o"></path><path d="M312 519l1-2c3 3 7 6 11 8s9 3 12 6v1l-7-2c-7-3-12-7-17-11z" class="v"></path><path d="M313 460l14 39h0l-1-2-1-1v-1c-1-1-1-1-1-2v-1l-4-11c-1-1-1-2-2-3-1-4-2-8-4-10-2 1-2 4-2 5-1 7-2 14-2 21h0l-1-12v-5s-1-1-1-2h0c0-1 1-3 1-4 1-2 4-5 4-8 0-1-1-2-1-3h1z" class="h"></path><defs><linearGradient id="DK" x1="269.956" y1="421.963" x2="297.461" y2="431.183" xlink:href="#B"><stop offset="0" stop-color="#1b0000"></stop><stop offset="1" stop-color="#5f5c5f"></stop></linearGradient></defs><path fill="url(#DK)" d="M281 392l2-1 1 4c1 1 1 0 1 1 1 1 0 1 1 1 0 2 1 2 2 3l5 12 4 8c0 1 1 2 1 3l8 21c0 1 0 4-1 6l-1 2c-1-2-2-3-4-5l-7 27c0 1-1 4-1 5l-2 1c-1 2-1 2-2 3-1-2 0-4 0-6v-3l-1-1c-1 2-1 4-3 5-1-1-2-3-2-4-1 1-1 2-2 2h0l-3-6c-1-4-1-7-2-11v-7h0l-1-1c0-1-1-2-1-3 1-1 1-3 1-4-1 2-1 4-3 6l-1 1c2-6 4-12 7-17v-6h-1c1-5 1-9 1-14-1-1 0-3-1-4v-2-1-1l-1 1v2h0l-1-2c-1 0-1 0-2 1h-4c2-1 5-2 7-2h1c3-1 5-1 8-1h1c1 0 1 0 2-1l-3-6-1-2v-1l-3-3h1z"></path><path d="M289 429h0c0 2-1 4-1 6-1 1 0 2-1 3v1 1c-1 1-1 0-1 1v1l-1 1v2l-1 3h0v-3c0-2 1-5 1-8 1-2 3-5 4-8z" class="o"></path><path d="M281 459h0l1-2v3 1h1l1-1-1-1 1-1v-3-1c1-1 1-1 1-2l-1-1h1c1 0 1-1 1-2 1-1 1-3 2-4 1-2 3-4 5-5v9l-2 14c0 2-1 4-1 6-1 1-1 1-1 2v2l-1 4v-3l-1-1c-1 2-1 4-3 5-1-1-2-3-2-4-2-5-2-10-1-15z" class="Z"></path><path d="M291 463c0 2-1 4-1 6-1 1-1 1-1 2v2l-1 4v-3l-1-1 4-10z" class="o"></path><defs><linearGradient id="DL" x1="285.634" y1="450.277" x2="300.863" y2="457.426" xlink:href="#B"><stop offset="0" stop-color="#27120f"></stop><stop offset="1" stop-color="#443030"></stop></linearGradient></defs><path fill="url(#DL)" d="M293 412l4 8c0 1 1 2 1 3l8 21c0 1 0 4-1 6l-1 2c-1-2-2-3-4-5l-7 27c0 1-1 4-1 5l-2 1c-1 2-1 2-2 3-1-2 0-4 0-6l1-4v-2c0-1 0-1 1-2 0-2 1-4 1-6l2-14c2-5 3-12 3-17 0-3 0-6-1-8 0-2-1-4-1-5l-1-2 1-1c-1-1-1-3-1-4z"></path><path d="M300 447c1-1 1-3 2-5v2h1c0-1 0-1 1 0 0 2 1 3 1 6l-1 2c-1-2-2-3-4-5z" class="o"></path><path d="M293 412l4 8c0 1 1 2 1 3v4c-1 3 0 6 0 9-1 8-2 16-4 23-1 5-2 10-5 14v-2c0-1 0-1 1-2 0-2 1-4 1-6l2-14c2-5 3-12 3-17 0-3 0-6-1-8 0-2-1-4-1-5l-1-2 1-1c-1-1-1-3-1-4z" class="q"></path><path d="M284 405h1c1 0 1 0 2 1v2-1c2 3 1 8 0 12l-1 4c0 2-1 3-1 4h0c0 2 0-1-1 2h0v1l-2 5c-1 5-1 10-2 15 0 3-2 7 0 10h1v-2 1c-1 5-1 10 1 15-1 1-1 2-2 2h0l-3-6c-1-4-1-7-2-11v-7h0l-1-1c0-1-1-2-1-3 1-1 1-3 1-4-1 2-1 4-3 6l-1 1c2-6 4-12 7-17v-6h-1c1-5 1-9 1-14-1-1 0-3-1-4v-2-1-1l-1 1v2h0l-1-2c-1 0-1 0-2 1h-4c2-1 5-2 7-2h1c3-1 5-1 8-1z" class="w"></path><path d="M277 414c0 1 0 1 1 2v-2h1c-1 4-1 9-2 14h-1c1-5 1-9 1-14z" class="u"></path><path d="M277 434l-2 25v-7h0l-1-1c0-1-1-2-1-3 1-1 1-3 1-4-1 2-1 4-3 6l-1 1c2-6 4-12 7-17z" class="P"></path><path d="M270 451l1-1c2-2 2-4 3-6 0 1 0 3-1 4 0 1 1 2 1 3l1 1h0v7c1 4 1 7 2 11l3 6-1 1v1c0 4 1 7 2 10l2 3v2c1 1 1 2 2 3l1 2v1c1 2 2 4 2 6l-1-1-1 1 2 2c-1 2 0 3 0 5h1c1 0 3 0 4 1l2 2 10 8c4 2 7 4 10 5h1c3 2 7 3 11 5 1-1 1-2 2-3l7 2 1 1c2 1 2 2 3 3l5 15h2l12 29-1 1-2-3c-1 1-2 2-4 3-4-2-8-5-12-8v1l-1-2-8-4v-1l-18-12v1-1l-2-1-1 1c1 1 3 2 3 3l2 2c-1 0-2 0-3-1-2 0-5-3-7-5h-2l-1-1-1 2h1c1 1 1 1 2 3v1c-2-1-3-3-5-3l-8-9c-6-6-11-14-15-22-1-1-1-2-2-4 0-4-1-8-2-11l-2-8-1-3c-4-16-3-33 1-48z" class="c"></path><path d="M273 493c0 4 2 7 3 10 1 2 2 4 2 5 0 4 3 8 4 12l-4-4-5-23z" class="I"></path><path d="M278 516l4 4 3 7 7 10 13 14c3 2 5 5 8 7l2 2c-1 0-2 0-3-1-2 0-5-3-7-5-1 0-12-11-14-13-5-6-12-16-13-25z" class="W"></path><path d="M285 527l7 10 13 14-1 1c-3-2-4-4-6-6-6-5-11-11-14-18l1-1z" class="B"></path><defs><linearGradient id="DM" x1="311.075" y1="524.125" x2="294.089" y2="534.695" xlink:href="#B"><stop offset="0" stop-color="#c2c2c2"></stop><stop offset="1" stop-color="#f4f3f2"></stop></linearGradient></defs><path fill="url(#DM)" d="M273 479v-3c2 6 4 12 7 18 2 3 5 7 7 10l-1 1 2 2c-1 2 0 3 0 5h1c0 1 1 3 1 4 0 3 0 4 2 7l1 2v1c5 7 11 13 18 19 1 2 3 4 5 5 4 4 9 7 14 10l7 5v2c1 1 2 1 3 3 0 1 0 1-1 2l-8-4v-1l-18-12v1-1l-2-1-1 1c1 1 3 2 3 3-3-2-5-5-8-7l-13-14-7-10-3-7c-1-4-4-8-4-12 0-1-1-3-2-5-1-3-3-6-3-10l-1-1c-1-2-1-2-1-4l1-1v-3c0 1 0 1 1 2v1h0v-2l-1-1c0-2 0-3 1-5z"></path><path d="M330 560l7 5v2h-2c-1-1-4-3-5-4v-3z" class="G"></path><path d="M285 511l3 1h1c0 1 1 3 1 4 0 3 0 4 2 7l1 2v1c-4-5-6-10-8-15z" class="M"></path><path d="M278 508c5 9 9 17 15 25-1 1-1 3-1 4l-7-10-3-7c-1-4-4-8-4-12z" class="N"></path><path d="M293 533c4 5 8 10 13 15 2 2 6 5 7 7v1-1l-2-1-1 1c1 1 3 2 3 3-3-2-5-5-8-7l-13-14c0-1 0-3 1-4z" class="U"></path><path d="M273 479v-3c2 6 4 12 7 18 2 3 5 7 7 10l-1 1 2 2c-1 2 0 3 0 5l-3-1c-5-10-10-21-12-32z" class="N"></path><defs><linearGradient id="DN" x1="349.903" y1="555.149" x2="327.816" y2="558.552" xlink:href="#B"><stop offset="0" stop-color="#474041"></stop><stop offset="1" stop-color="#89898b"></stop></linearGradient></defs><path fill="url(#DN)" d="M289 512c1 0 3 0 4 1l2 2 10 8c4 2 7 4 10 5h1c3 2 7 3 11 5 1-1 1-2 2-3l7 2 1 1c2 1 2 2 3 3l5 15h2l12 29-1 1-2-3c-1 1-2 2-4 3-4-2-8-5-12-8v1l-1-2c1-1 1-1 1-2-1-2-2-2-3-3v-2l-7-5c-5-3-10-6-14-10-2-1-4-3-5-5-7-6-13-12-18-19v-1l-1-2c-2-3-2-4-2-7 0-1-1-3-1-4z"></path><path d="M331 551l-2-2v-2-2c2 2 3 4 3 7v2l-1 1c-1-2 0-3 0-4z" class="J"></path><path d="M316 528c3 2 7 3 11 5l3 1h0v1h0l4 2h-4c-1-1-2-2-3-2-2-1-4-2-6-2l-6-5h1z" class="U"></path><path d="M329 530l7 2 1 1c2 1 2 2 3 3-2 1-4 1-6 1l-4-2h0v-1h0l-3-1c1-1 1-2 2-3z" class="C"></path><path d="M337 533c2 1 2 2 3 3-2 1-4 1-6 1l-4-2h0v-1h7v-1z" class="e"></path><path d="M316 550c3 0 5 3 8 5l31 21-1-3c-1-5-3-8-5-13-1-3-3-6-4-9h2l12 29-1 1-2-3c-1 1-2 2-4 3-4-2-8-5-12-8v1l-1-2c1-1 1-1 1-2-1-2-2-2-3-3v-2l-7-5c-5-3-10-6-14-10z" class="X"></path><defs><linearGradient id="DO" x1="350.802" y1="577.236" x2="346.423" y2="573.081" xlink:href="#B"><stop offset="0" stop-color="#817f81"></stop><stop offset="1" stop-color="#9d9b9c"></stop></linearGradient></defs><path fill="url(#DO)" d="M337 565c2 1 5 3 6 4 4 3 9 6 13 9-1 1-2 2-4 3-4-2-8-5-12-8v1l-1-2c1-1 1-1 1-2-1-2-2-2-3-3v-2z"></path><path d="M337 565c2 1 5 3 6 4h0c0 2 0 3 1 4l-1 1c-1 0-1-1-3-1v1l-1-2c1-1 1-1 1-2-1-2-2-2-3-3v-2z" class="D"></path><path d="M289 512c1 0 3 0 4 1l2 2 10 8c4 2 7 4 10 5l6 5c1 1 2 2 4 2 2 2 4 3 6 4h1c2-1 4-1 7 0v1l-6 3c-2 0-3 1-4 2v2 2l2 2-3 5c-2 0-3-1-4-1-3-2-5-5-8-5-2-1-4-3-5-5-7-6-13-12-18-19v-1l-1-2c-2-3-2-4-2-7 0-1-1-3-1-4z" class="W"></path><path d="M293 521l1-1 3 2c2 2 4 3 5 4l-3-1h-1c1 1 1 2 2 2 3 2 5 6 8 8-2-1-3-1-4-1-3-2-7-6-9-9l-2-4z" class="D"></path><path d="M304 534c1 0 2 0 4 1 3 2 6 5 9 6l1 1h1 1 1c1 0 3 0 5-1h2c-1 2-2 2-3 4 1 0 1 0 1 1-3 0-5-2-8-2 0 0-1 0-1-1-6-2-9-5-13-9z" class="R"></path><path d="M289 512c1 0 3 0 4 1l2 2 10 8c4 7 12 8 18 13-5-1-9-4-14-5l-7-5c-1-1-3-2-5-4l-3-2-1 1c0-1-1-2-2-3l-1-2c0-1-1-3-1-4z" class="B"></path><path d="M291 518l1-3h0c1 1 2 2 2 4l3 3-3-2-1 1c0-1-1-2-2-3z" class="C"></path><path d="M308 535c-3-2-5-6-8-8-1 0-1-1-2-2h1l3 1 7 5c5 1 9 4 14 5v1c2 1 3 2 5 2 1 1 1 1 2 1-1 1-1 1-2 1h-2c-2 1-4 1-5 1h-1-1-1l-1-1c-3-1-6-4-9-6z" class="E"></path><path d="M309 531c5 1 9 4 14 5v1c2 1 3 2 5 2 1 1 1 1 2 1-1 1-1 1-2 1h-2c-1 0-1-1-1-2-2-1-5-2-7-3-3-1-6-3-9-5z" class="D"></path><path d="M730 405h8 3c2 1 3 1 5 1l1-1 5 1v-1h2c2 2 4 3 5 6l2 1 1 3 3 1-1 1c-5-3-10-5-16-7-1 2-1 5-1 8v2c0 5 0 10 1 14l2 5v2c1 2 2 6 2 9 4 11 6 24 5 36l1 5 2 14 2 16 1 3 1 6 1 15v3l-1-1c-1 1 0 3-1 4v8l-4 26-9 26-2 5h0l-3 6 2 1-2 6h-3c-1 1 0 2-2 2h0c-1-2-1-2-1-4l1-1-1-1h0c-2 0-4 0-6-1v-3-1h-1c-1-1-2 0-3 0 0-2 0-2 1-4h-1-1c0 1-2 4-3 5 0 0-1 1-1 2l-1-1c2-4 4-9 5-14 1-2 1-4 0-6 0-1-1-2-2-2h-3c-3 4 0 11-4 14l1-7c1-2 1-4 0-6v-1c0-2-1-3-2-5 0-2-1-4-3-6-3-2-5 0-8 2h-1l-9 7c2 1 4 2 5 3l-7-2-5-1-1 1c-3 0-6 0-9-1-2-2-3-4-3-7 0-1 1-2 1-3 1-2 5-5 7-6v-3l-5 4v-2c2-1 3-2 4-4v-1l-2 1c1-1 1-2 2-2 1-1 1-1 1-2l-4 2h-2l14-9c-1-1-1-2-1-2l-9 5s-1 1-2 1h-2 0l-1 1c0-2 0-2 1-4 5-2 9-6 14-9h-2l-3 2v-1l3-3v-1l-3 2-1-2c-1 0-2 0-3-1h0c0-2 0-5-1-7s0-3 0-5c0-3-1-7-1-10h-1l-2 1 10-26h0l17-45 7-17 4-10c3-8 5-16 9-24h1l1-3c1-1 1-2 2-3z" class="k"></path><path d="M735 538l5-6c-1 1-1 3-2 4l-1 3v-1h0-2z" class="B"></path><path d="M732 554c1-2 1-2 3-3 1 0 3 0 4 1v1l2 1c-3-1-6-1-9 0z" class="n"></path><path d="M685 573c1-1 3-2 4-2l1 1-6 4-2 1c1-1 1-2 2-2 1-1 1-1 1-2z" class="B"></path><path d="M748 491c1 2 1 3 1 5v1c-1 3-2 6-4 9 0-1-1-2 0-2 0-2 0-3 1-4l2-9z" class="p"></path><path d="M749 558c1 1 2 2 3 4l3 4c0 1 0 2 1 3l1-3 1-1c0 2-1 12-2 13h0l-1-1 1-1c0-1-1-1-1-2v-4l-1-1c0-2-1-4-2-6v-1l-1-1c-1-1-2-2-2-3z" class="P"></path><path d="M751 503c1 2-2 5-1 7l-2 5c0 2 0 4 1 6 0 4 1 5 2 8l-1-1c-3-2-3-6-4-9l5-16z" class="K"></path><path d="M699 561c4-1 7-4 10-6-1 1-1 2-3 3l-2 2c1 0 1 0 3-1h0c-1 1-6 6-7 6-3 0-4 2-7 2v-1l6-5z" class="L"></path><path d="M693 566v1c3 0 4-2 7-2-3 2-6 5-10 7l-1-1c-1 0-3 1-4 2l-4 2h-2l14-9z" class="j"></path><path d="M743 559c1 0 2-1 3 0h1c2 4 5 6 6 10v1c1 1 1 1 1 2l1 1v3c0 1-1 1-1 2-1 2-1 5-2 8-1 2-1 4-2 6 0 1-1 3-2 4v-2h1l-1-1v-1l-1 1h0l2-7c1-1 2-6 3-8 0-3 0-7-1-10-1-5-4-7-8-9z" class="P"></path><path d="M718 543c3-3 6-5 9-7 3-3 5-8 8-11 0-1 2-3 3-4 0-1 1-2 2-3-1 4-3 7-5 10-7 10-14 18-24 25 1-4 5-6 7-10z" class="I"></path><defs><linearGradient id="DP" x1="741.619" y1="533.41" x2="728.52" y2="551.466" xlink:href="#B"><stop offset="0" stop-color="#949090"></stop><stop offset="1" stop-color="#bebebe"></stop></linearGradient></defs><path fill="url(#DP)" d="M738 536l2-2v1h1c1 0 3 0 4 1h0l1 1h0l1 3c-1-1-2-2-3-2-3 1-4 3-6 4-4 4-9 10-15 11-2 2-5 5-8 5 1-2 3-2 4-3l16-16v-1h2 0v1l1-3z"></path><defs><linearGradient id="DQ" x1="752.451" y1="550.204" x2="726.86" y2="545.727" xlink:href="#B"><stop offset="0" stop-color="#bebdbd"></stop><stop offset="1" stop-color="#ececeb"></stop></linearGradient></defs><path fill="url(#DQ)" d="M723 553c6-1 11-7 15-11 2-1 3-3 6-4 1 0 2 1 3 2v1 2c0 2 0 6 1 7 0 1 1 1 1 2 1 1 2 3 3 4h0v6c-1-2-2-3-3-4l-2-2-6-2-2-1v-1c-1-1-3-1-4-1-2 1-2 1-3 3h0l-8 1-1-2z"></path><path d="M739 552c2 0 4 0 6 1v1c1 0 2 1 3 2h-1l-6-2-2-1v-1z" class="E"></path><path d="M748 463l2 3v1c1 3 1 8 0 11v2c-1 4-1 8-2 11l-2 9c-1 1-1 2-1 4-1 0 0 1 0 2-1 3-1 6-3 9l-2 3c-1 1-2 2-2 3-1 1-3 3-3 4-3 3-5 8-8 11-3 2-6 4-9 7l-5 5h-1l10-10c9-11 16-23 22-35h-1v-1c0-1 1-1 1-2v-1c1-1 0-1 0-2l-1 2h-1c2-3 2-5 2-8 1-1 1-2 1-4l-1-1 3-10 1-1v-2-10z" class="Q"></path><path d="M748 463l2 3v1c1 3 1 8 0 11v2c-1 2-2 4-2 6l-1 6v1c-1 3-2 7-3 10h-1v-1c0-1 1-1 1-2v-1c1-1 0-1 0-2l-1 2h-1c2-3 2-5 2-8 1-1 1-2 1-4l-1-1 3-10 1-1v-2-10z" class="H"></path><defs><linearGradient id="DR" x1="708.567" y1="566.568" x2="715.941" y2="585.477" xlink:href="#B"><stop offset="0" stop-color="#331817"></stop><stop offset="1" stop-color="#4f3c3c"></stop></linearGradient></defs><path fill="url(#DR)" d="M693 576l2-1c13-9 32-20 48-16 4 2 7 4 8 9 1 3 1 7 1 10-1-2-1-4-2-6 0-1-1-3-2-4v1c-1-1-1-1-2-1-3-4-6-5-10-6-10-2-24 5-31 11l-6 4v2c-2 3-6 5-8 8-1 2-3 4-4 6l-1 2-1 2c1 1 3 1 5 1l-1 1c-3 0-6 0-9-1-2-2-3-4-3-7 0-1 1-2 1-3 1-2 5-5 7-6v-3c2-2 4-3 8-3z"></path><path d="M685 579c2-2 4-3 8-3l-8 6v-3z" class="P"></path><path d="M688 588c-2 2-3 3-6 4l1-1c2-4 5-7 9-8-2 1-3 3-4 5z" class="s"></path><path d="M692 583c2-3 5-5 7-6v2c-2 3-6 5-8 8-1 2-3 4-4 6l-1-1h1c1-2-1 0 0-2 1-1 1-1 1-2 1-2 2-4 4-5z" class="Y"></path><defs><linearGradient id="DS" x1="708.824" y1="532.114" x2="718.177" y2="540.057" xlink:href="#B"><stop offset="0" stop-color="#c8c7c7"></stop><stop offset="1" stop-color="#eceaea"></stop></linearGradient></defs><path fill="url(#DS)" d="M744 491c0 3 0 5-2 8h1l1-2c0 1 1 1 0 2v1c0 1-1 1-1 2v1h1c-6 12-13 24-22 35l-10 10h1l5-5c-2 4-6 6-7 10l-2 2c-3 2-6 5-10 6l-6 5c-1-1-1-2-1-2l-9 5s-1 1-2 1h-2 0l-1 1c0-2 0-2 1-4 5-2 9-6 14-9h-2l-3 2v-1l3-3v-1c0-1 3-2 4-3l3-1c2-1 4-3 6-5h-1c1-2 3-4 4-6v-1-1c1-2 4-5 6-6l3-3h0c1 0 2-1 2-2l7-10 2-2v-1l3-3c0-1 2-3 3-3-1 1-1 1-1 2s0 2 1 3c1-1 1-2 2-2 0-2 1-3 2-4h0c3-5 5-11 7-16z"></path><path d="M692 564c0-1 2-2 3-3l10-6c-1 3-4 3-6 6l-6 5c-1-1-1-2-1-2z" class="U"></path><path d="M718 543c-2 4-6 6-7 10l-2 2c-3 2-6 5-10 6 2-3 5-3 6-6v-1l7-6h1l5-5z" class="i"></path><path d="M727 514l3-3c0-1 2-3 3-3-1 1-1 1-1 2s0 2 1 3c1-1 1-2 2-2 0-2 1-3 2-4h0c-4 10-10 22-18 30l-1-1c1-2 4-6 6-7 0-1 1-2 1-2 1-3 2-4 2-6l1-2c-1-1-2-2-3-2l2-2v-1z" class="Q"></path><path d="M727 515c1-1 2-1 3-2 0 1 0 3-1 4 0 0-1 1-1 2-1-1-2-2-3-2l2-2z" class="I"></path><path d="M725 517c1 0 2 1 3 2l-1 2c0 2-1 3-2 6 0 0-1 1-1 2-2 1-5 5-6 7l1 1c-8 8-17 15-26 21h-2l-3 2v-1l3-3v-1c0-1 3-2 4-3l3-1c2-1 4-3 6-5h-1c1-2 3-4 4-6v-1-1c1-2 4-5 6-6l3-3h0c1 0 2-1 2-2l7-10z" class="i"></path><path d="M695 552l3-1c-2 3-4 5-7 7l-3 2v-1l3-3v-1c0-1 3-2 4-3z" class="F"></path><path d="M725 517c1 0 2 1 3 2l-1 2c-2 2-4 5-6 8l-9 9c-1 2-3 5-5 5l-3 3h-1c1-2 3-4 4-6v-1-1c1-2 4-5 6-6l3-3h0c1 0 2-1 2-2l7-10z" class="j"></path><path d="M707 539v-1c1-2 4-5 6-6l-6 11-3 3h-1c1-2 3-4 4-6v-1z" class="P"></path><path d="M743 526c0-2 1-4 2-5 1 0 1-1 1-2 1 3 1 7 4 9l1 1c1 1 3 2 5 2h1c1 0 2-1 4-2v-1c1-1 1-3 0-4v-1l2 1 1 6 1 15v3l-1-1c-1 1 0 3-1 4v8l-4 26-9 26v-1h-1l-1 1c3-7 5-14 6-22 1 0 1-6 2-7-1-1 0-3 0-4 1-1 2-11 2-13l-1 1-1 3c-1-1-1-2-1-3l-3-4v-6h0c-1-1-2-3-3-4 0-1-1-1-1-2-1-1-1-5-1-7v-2-1l-1-3h0l-1-1h0c-1-1-3-1-4-1h-1v-1l-2 2c1-1 1-3 2-4 0-1 3-5 3-6z" class="S"></path><path d="M761 523l2 1 1 6c-1 1-1 1-1 2 0 2 0 3-1 5l-1-8v-1c1-1 1-3 0-4v-1z" class="N"></path><path d="M762 537c1-2 1-3 1-5 0-1 0-1 1-2l1 15v3l-1-1c-1 1 0 3-1 4l-1-14z" class="l"></path><path d="M758 565c0-4 0-8 1-13v-3c2 7 0 15-1 21 0 4 0 9-2 12h0 0c-1-1 0-3 0-4 1-1 2-11 2-13z" class="B"></path><path d="M751 551c-1-1-1-3-1-5h1l4 2v1l1 2v2c2 1 1 10 1 13l-1 3c-1-1-1-2-1-3l-3-4v-6h0c0-2 0-3-1-5z" class="Y"></path><path d="M751 551c-1-1-1-3-1-5h1l4 2v1l1 2c-2-1-3-2-4-3-1 1-1 2-1 3z" class="e"></path><path d="M752 556l3 6v4l-3-4v-6z" class="C"></path><path d="M756 553c2 1 1 10 1 13l-1 3c-1-1-1-2-1-3v-4l1-1c1-1 0-6 0-8z" class="B"></path><path d="M743 526c0-2 1-4 2-5 1 0 1-1 1-2 1 3 1 7 4 9l1 1c1 1 3 2 5 2l-3 1c1 0 2 0 2 1v1c0 1 1 2 1 4 1 3 1 6 1 9l-2 2v-1l-4-2s0-1-1-1v-2l2-1h0l2-2v-2c-1-2-3-3-4-4h-1c-3-3-5-5-6-8z" class="L"></path><path d="M752 542l1 1v1h-1l3 2v2l-4-2s0-1-1-1v-2l2-1z" class="H"></path><path d="M743 526c1 3 3 5 6 8h1c1 1 3 2 4 4v2l-2 2h0l-2 1v2c1 0 1 1 1 1h-1c0 2 0 4 1 5 1 2 1 3 1 5-1-1-2-3-3-4 0-1-1-1-1-2-1-1-1-5-1-7v-2-1l-1-3h0l-1-1h0c-1-1-3-1-4-1h-1v-1l-2 2c1-1 1-3 2-4 0-1 3-5 3-6z" class="W"></path><path d="M750 534c1 1 3 2 4 4v2l-2 2h0l-2 1v2c1 0 1 1 1 1h-1c0 2 0 4 1 5 1 2 1 3 1 5-1-1-2-3-3-4v-10c0-3 0-5 1-8z" class="J"></path><path d="M752 405h2c2 2 4 3 5 6l2 1 1 3 3 1-1 1c-5-3-10-5-16-7-1 2-1 5-1 8v2c0 5 0 10 1 14l2 5v2c1 2 2 6 2 9 4 11 6 24 5 36l1 5 2 14 2 16 1 3-2-1v1c1 1 1 3 0 4v1c-2 1-3 2-4 2h-1c-2 0-4-1-5-2-1-3-2-4-2-8-1-2-1-4-1-6l2-5c-1-2 2-5 1-7 0-2 1-5 2-7 0-3 1-6 1-10 2-18-4-36-12-52l3 21c0 8-2 16-5 24-1 3-2 7-5 11h0c-1-2 1-2 1-3v-1l1-1v-1l1-1v-2l1-1c0-1 1-2 1-3v-1l1-3 1-2v-1-1c1-1 1-2 1-3h-1v1-1c1-6 0-14-2-20-2-9-5-18-6-27v-13c2 0 3 0 4-1h3c2 1 3 1 5 1l1-1 5 1v-1z" class="b"></path><defs><linearGradient id="DT" x1="758.929" y1="491.31" x2="754.825" y2="501.087" xlink:href="#B"><stop offset="0" stop-color="#322e2c"></stop><stop offset="1" stop-color="#5d4e50"></stop></linearGradient></defs><path fill="url(#DT)" d="M756 503h0v-12c1-2 1-3 1-5l1 5 2 14-1-1-1-1c-1 1-1 0-1 1s-1 1-1 1v-2z"></path><path d="M752 405h2c2 2 4 3 5 6l2 1 1 3 3 1-1 1c-5-3-10-5-16-7-1 2-1 5-1 8v2c0 5 0 10 1 14l2 5v2h-1c-1-2-2-4-2-6-1-3-3-7-4-10 0-4 1-9 2-12 0-3 0-5 1-7h0l1-1 5 1v-1z" class="w"></path><path d="M752 405h2c2 2 4 3 5 6-4-2-8-4-13-5h0l1-1 5 1v-1z" class="n"></path><path d="M756 505s1 0 1-1 0 0 1-1l1 1 1 1 2 16 1 3-2-1v1c1 1 1 3 0 4v1c-2 1-3 2-4 2h-1c-2 0-4-1-5-2-1-3-2-4-2-8-1-2-1-4-1-6l2-5h0c0 2-1 3 0 4l1-1 1-1h1c1-1 1-2 1-3h-1l1-2c1-1 1-2 2-4v2z" class="B"></path><path d="M756 505s1 0 1-1 0 0 1-1l1 1 1 1 2 16 1 3-2-1c-1-6-1-12-5-16v-2z" class="l"></path><path d="M754 525v-4l1 1v-5-2l1-1 1-1h0l3 11h0-2l1 2h-2c-2 0-2 0-3-1z" class="C"></path><path d="M756 503v2 2c-2 5-3 10-3 15 0 1 1 2 1 3 1 1 1 1 3 1h2l1-1 1-1c1 1 1 3 0 4v1c-2 1-3 2-4 2h-1c-2 0-4-1-5-2-1-3-2-4-2-8-1-2-1-4-1-6l2-5h0c0 2-1 3 0 4l1-1 1-1h1c1-1 1-2 1-3h-1l1-2c1-1 1-2 2-4z" class="y"></path><path d="M756 503v2 2c-2 5-3 10-3 15h-1c0-3 0-7 1-10-2 3-3 6-4 9-1-2-1-4-1-6l2-5h0c0 2-1 3 0 4l1-1 1-1h1c1-1 1-2 1-3h-1l1-2c1-1 1-2 2-4z" class="V"></path><defs><linearGradient id="DU" x1="677.33" y1="521.616" x2="756.311" y2="471.87" xlink:href="#B"><stop offset="0" stop-color="#c7c7c7"></stop><stop offset="1" stop-color="#f0efee"></stop></linearGradient></defs><path fill="url(#DU)" d="M745 455c1 0 1 0 2 1v2l1 5v10 2l-1 1-3 10 1 1c0 2 0 3-1 4-2 5-4 11-7 16h0c-1 1-2 2-2 4-1 0-1 1-2 2-1-1-1-2-1-3s0-1 1-2c-1 0-3 2-3 3l-3 3v1l-2 2-7 10c0 1-1 2-2 2h0l-3 3c-2 1-5 4-6 6v1 1c-1 2-3 4-4 6h1c-2 2-4 4-6 5l-3 1c-1 1-4 2-4 3l-3 2-1-2c-1 0-2 0-3-1h0c0-2 0-5-1-7s0-3 0-5c0-3-1-7-1-10h1l5-1c-1-1-2-1-4-1v-1c3 0 7-3 10-4 6-3 11-6 16-10l6-6c1 0 2-1 3-2h0 2c2-1 3-3 5-5 4-4 7-8 9-12 3-4 4-8 5-11 3-8 5-16 5-24z"></path><path d="M747 458l1 5v10 2l-1 1h-1v-2c2-5 1-11 1-16z" class="L"></path><defs><linearGradient id="DV" x1="721.732" y1="514.311" x2="720.327" y2="519.955" xlink:href="#B"><stop offset="0" stop-color="#6a6464"></stop><stop offset="1" stop-color="#7c7979"></stop></linearGradient></defs><path fill="url(#DV)" d="M713 522c5-4 9-9 14-14v1l-3 3v1h1l1 1h1v1l-2 2-7 10 2-4-1-1-1 1c-2 0-2 0-4-1h1-2z"></path><path d="M694 534l19-12h2-1c2 1 2 1 4 1l1-1 1 1-2 4c0 1-1 2-2 2h0v-1c1-1 2-1 2-3-1 0-2 1-3 1-2 0-1 0-2 1s-3 1-5 1c-5 2-9 7-14 7v-1z" class="B"></path><path d="M694 525c6-3 11-6 16-10l6-6c1 0 2-1 3-2h0 2c-7 7-15 14-24 19-3 2-6 4-9 5-1-1-2-1-4-1v-1c3 0 7-3 10-4z" class="m"></path><path d="M694 534v1c5 0 9-5 14-7 2 0 4 0 5-1s0-1 2-1c1 0 2-1 3-1 0 2-1 2-2 3-1 0-1-1-3 0-1 0-2 1-3 2l-9 6c-2 2-5 6-8 5 0-1-1-1-1-2v-2c0-2 0-2 2-3z" class="S"></path><defs><linearGradient id="DW" x1="730.259" y1="502.225" x2="736.3" y2="506.093" xlink:href="#B"><stop offset="0" stop-color="#4a4040"></stop><stop offset="1" stop-color="#7a7575"></stop></linearGradient></defs><path fill="url(#DW)" d="M746 476h1l-3 10 1 1c0 2 0 3-1 4-2 5-4 11-7 16h0c-1 1-2 2-2 4-1 0-1 1-2 2-1-1-1-2-1-3s0-1 1-2c-1 0-3 2-3 3l-3 3h-1l-1-1h-1v-1l3-3v-1l2-2c2-2 5-5 6-7 5-7 9-15 11-23z"></path><defs><linearGradient id="DX" x1="684.558" y1="548.016" x2="716.771" y2="533.56" xlink:href="#B"><stop offset="0" stop-color="#c0bfbf"></stop><stop offset="1" stop-color="#e5e5e5"></stop></linearGradient></defs><path fill="url(#DX)" d="M693 541c3 1 6-3 8-5l9-6c1-1 2-2 3-2 2-1 2 0 3 0v1l-3 3c-2 1-5 4-6 6v1 1c-1 2-3 4-4 6h1c-2 2-4 4-6 5l-3 1c-1 1-4 2-4 3l-3 2-1-2c-1 0-2 0-3-1h0c0-2 0-5-1-7s0-3 0-5c0-3-1-7-1-10h1l2 3c1 1 2 1 4 2 1 0 2 1 3 2 0 1 1 1 1 2z"></path><path d="M685 535c1 1 2 1 4 2 1 0 2 1 3 2 0 1 1 1 1 2h-2c-3-1-5-3-7-6h1z" class="F"></path><path d="M707 539v1c-1 2-3 4-4 6h1c-2 2-4 4-6 5l-3 1h-1c5-5 9-8 13-13z" class="S"></path><path d="M682 532h1l2 3h-1-1c0 2 2 4 2 6 2 1 3 2 5 3h0-4v1h-1v-2h-1v3h1 0 1c-1 1-2 1-1 2 0 3 2 4 3 6 3-1 4-1 6-2h1c-1 1-4 2-4 3l-3 2-1-2c-1 0-2 0-3-1h0c0-2 0-5-1-7s0-3 0-5c0-3-1-7-1-10z" class="R"></path><defs><linearGradient id="DY" x1="721.609" y1="577.671" x2="716.864" y2="614.25" xlink:href="#B"><stop offset="0" stop-color="#797879"></stop><stop offset="1" stop-color="#b6b6b6"></stop></linearGradient></defs><path fill="url(#DY)" d="M699 577l6-4c7-6 21-13 31-11 4 1 7 2 10 6 1 0 1 0 2 1v-1c1 1 2 3 2 4 1 2 1 4 2 6-1 2-2 7-3 8l-2 7h0l1-1v1l1 1h-1v2c1-1 2-3 2-4l3-3h1c-1 8-3 15-6 22l1-1h1v1l-2 5h0l-3 6 2 1-2 6h-3c-1 1 0 2-2 2h0c-1-2-1-2-1-4l1-1-1-1h0c-2 0-4 0-6-1v-3-1h-1c-1-1-2 0-3 0 0-2 0-2 1-4h-1-1c0 1-2 4-3 5 0 0-1 1-1 2l-1-1c2-4 4-9 5-14 1-2 1-4 0-6 0-1-1-2-2-2h-3c-3 4 0 11-4 14l1-7c1-2 1-4 0-6v-1c0-2-1-3-2-5 0-2-1-4-3-6-3-2-5 0-8 2h-1l-9 7c2 1 4 2 5 3l-7-2-5-1c-2 0-4 0-5-1l1-2 1-2c1-2 3-4 4-6 2-3 6-5 8-8v-2z"></path><path d="M730 616v-1c0-2 0-5 2-6h1-1v2 1 1c1 1 0 3-1 4-1 0-1 0-1-1z" class="C"></path><path d="M733 609l1-6c1 1 0 4 0 6h0v-1c1-1 1-1 1-2h1v-3h1 0c0 2 0 3-1 5v1l1-1c1-2 1-2 2-2v2c-1 3-3 5-4 8l-1 2-1 2h-1c-1-1-2 0-3 0 0-2 0-2 1-4h0c0 1 0 1 1 1 1-1 2-3 1-4v-1-1-2h1z" class="B"></path><path d="M732 613l1-1 1-1 1 1c-2 2-2 3-1 6l-1 2h-1c-1-1-2 0-3 0 0-2 0-2 1-4h0c0 1 0 1 1 1 1-1 2-3 1-4z" class="R"></path><path d="M736 562c4 1 7 2 10 6 1 0 1 0 2 1v-1c1 1 2 3 2 4 1 2 1 4 2 6-1 2-2 7-3 8l-2 7h0-2l-3 3c0-3 1-6 2-10v-10c0-2 0-5-1-6l-2-4h0c0-1 0-1-1-2s-2-1-4-2h0z" class="U"></path><path d="M747 586v3l1-1 1-2-2 7h0-2l2-7z" class="h"></path><path d="M746 568c1 0 1 0 2 1v-1c1 1 2 3 2 4 1 2 1 4 2 6-1 2-2 7-3 8l-1 2-1 1v-3-2c1-3 2-6 2-10l-3-6z" class="a"></path><path d="M746 568c1 0 1 0 2 1v-1c1 1 2 3 2 4 0 2 0 4-1 6v-4l-3-6z" class="b"></path><path d="M750 592l3-3h1c-1 8-3 15-6 22l1-1h1v1l-2 5h0l-3 6 2 1-2 6h-3c-1 1 0 2-2 2h0c-1-2-1-2-1-4l1-1-1-1h0c-2 0-4 0-6-1v-3-1l1-2 1-2c1-3 3-5 4-8v-2c2-2 3-7 3-10l3-3h2 0 0l1-1v1l1 1h-1v2c1-1 2-3 2-4z" class="AA"></path><path d="M748 611l1-1h1v1l-2 5h0c-2 1-3 2-4 4 0 0-1 1-1 2-1 1-2 1-3 2l8-13z" class="R"></path><path d="M740 624c1-1 2-1 3-2 0-1 1-2 1-2 1-2 2-3 4-4l-3 6 2 1-2 6h-3c-1 1 0 2-2 2h0c-1-2-1-2-1-4l1-1-1-1 1-1z" class="C"></path><path d="M740 631l5-9 2 1-2 6h-3c-1 1 0 2-2 2z" class="Y"></path><path d="M747 593h0l1-1v1l1 1h-1v2c-2 5-5 11-9 16-1 1-1 3-3 3l-1 1c1-3 3-5 4-8v-2c2-2 3-7 3-10l3-3h2 0z" class="G"></path><path d="M745 593h2c-2 5-5 10-8 15v-2c2-2 3-7 3-10l3-3z" class="K"></path><defs><linearGradient id="DZ" x1="709.935" y1="568.828" x2="718.628" y2="593.063" xlink:href="#B"><stop offset="0" stop-color="#635f63"></stop><stop offset="1" stop-color="#8a898a"></stop></linearGradient></defs><path fill="url(#DZ)" d="M699 577l6-4c7-6 21-13 31-11h0c2 1 3 1 4 2s1 1 1 2h0l2 4v4l-1-3-1 1 1 3c1 1 0 2 1 4v4 1 1c-5-3-9-6-14-6 0 0-1 0-2-1-4 1-7 3-11 4-2 1-3 2-5 3-5 2-12 6-14 11h1c2-1 4-3 6-4l1-1h1l-9 7c2 1 4 2 5 3l-7-2-5-1c-2 0-4 0-5-1l1-2 1-2c1-2 3-4 4-6 2-3 6-5 8-8v-2z"></path><path d="M686 595c2 1 4 2 7 3 1 0 1 1 2 1l-5-1c-2 0-4 0-5-1l1-2z" class="M"></path><defs><linearGradient id="Da" x1="744.504" y1="410.047" x2="702.571" y2="529.246" xlink:href="#B"><stop offset="0" stop-color="#382522"></stop><stop offset="1" stop-color="#727378"></stop></linearGradient></defs><path fill="url(#Da)" d="M730 405h8c-1 1-2 1-4 1v13c1 9 4 18 6 27 2 6 3 14 2 20v1-1h1c0 1 0 2-1 3v1 1l-1 2-1 3v1c0 1-1 2-1 3l-1 1v2l-1 1v1l-1 1v1c0 1-2 1-1 3h0c-2 4-5 8-9 12-2 2-3 4-5 5h-2 0c-1 1-2 2-3 2l-6 6c-5 4-10 7-16 10-3 1-7 4-10 4v1c2 0 3 0 4 1l-5 1h-1-1l-2 1 10-26h0l17-45 7-17 4-10c3-8 5-16 9-24h1l1-3c1-1 1-2 2-3z"></path><path d="M720 476l2 2-1 2h-1l-1-2 1-2z" class="r"></path><path d="M716 472l1-2c2 1 2 1 2 2l1 4-1 2c-1-1-1-2-2-2l-1 1v-5z" class="o"></path><path d="M721 463c2 3 2 9 1 13v2l-2-2-1-4 2-1c1-2 0-6 0-8z" class="X"></path><path d="M708 500c0 3-2 6-4 9h0v-1l1-1v-1h0v-1l-1 2h-1l1-3h1v-2h0c1-1 1-1 1-2l-1 1v-1l3-8v8h0z" class="e"></path><path d="M717 435c0 1 1 1 1 2v7c0 3 1 6 1 9h0l-2-4c0-2-1-4-1-6-2 1-2 1-3 2l4-10zm-2 32h1v5 5c-2 9-5 15-8 23h0v-8c1-1 2-3 2-5l3-9c0-4 1-7 2-11z" class="h"></path><path d="M717 459l1-4h1c0 1 1 7 2 8 0 2 1 6 0 8l-2 1c0-1 0-1-2-2l-1 2v-5h-1c-2 0-3 1-5 2l-1 1h0l-2 1c0-1 1-3 1-5 2-1 4-2 7-3h2v-4z" class="I"></path><path d="M716 467c3 1 4 2 5 4h0l-2 1c0-1 0-1-2-2l-1 2v-5z" class="r"></path><path d="M717 463v2h0c-1 0-1 0-1-1-2 1-6 3-6 5l-1 1h0l-2 1c0-1 1-3 1-5 2-1 4-2 7-3h2z" class="N"></path><path d="M740 446l-1 1c-1-1-2-2-2-3l-2-3c-1-2 0 0-1-1v-2-1l-1-2h0l-1-1c0-2 0-3-1-5v-6l-1-2c0-3 1-5 2-8l1 1 1 5c1 9 4 18 6 27z" class="Y"></path><defs><linearGradient id="Db" x1="708.641" y1="451.388" x2="717.008" y2="457.073" xlink:href="#B"><stop offset="0" stop-color="#342929"></stop><stop offset="1" stop-color="#5d4e4e"></stop></linearGradient></defs><path fill="url(#Db)" d="M716 443c0 2 1 4 1 6l2 4v2h-1l-1 4v4h-2c-3 1-5 2-7 3v-2-2c1-1 1-3 1-4-1 1-1 2-2 3l-1 1 7-17c1-1 1-1 3-2z"></path><path d="M717 459h0v4h-2c0-1 1-2 2-4z" class="V"></path><path d="M716 443c0 2 1 4 1 6-1 2-1 3-1 5-1-4-1-7 0-11z" class="N"></path><path d="M717 449l2 4v2h-1l-1 4h0c0-1-1-3-1-5s0-3 1-5z" class="M"></path><defs><linearGradient id="Dc" x1="684.566" y1="483.362" x2="707.59" y2="504.671" xlink:href="#B"><stop offset="0" stop-color="#33201e"></stop><stop offset="1" stop-color="#5d5151"></stop></linearGradient></defs><path fill="url(#Dc)" d="M706 462l1-1c1-1 1-2 2-3 0 1 0 3-1 4v2 2c0 2-1 4-1 5l2-1c0 2 0 2-2 3-2 3-3 7-4 11l-6 24c-2 6-4 11-6 17h0 0 3c-3 1-7 4-10 4v1c2 0 3 0 4 1l-5 1h-1-1l-2 1 10-26h0l17-45z"></path><path d="M689 507c0 2 0 2-1 4-1 3-2 7-4 11-1 3-2 5-3 8l1 1 2-1c2 0 3 0 4 1l-5 1h-1-1l-2 1 10-26z" class="X"></path><path d="M759 585l1 2v3l1 1c0 2-2 5-1 7h0l1-2v2 2 13c0 6 0 13-1 19v10 5h0c-1 1-1 2-1 4l-1-1-1 1c-1-4-2-5-6-7h-2v-3h0l-1-2c-2 1-6 5-8 7l-1-1c-3 3-6 6-7 10l-2 4c1 0 2 1 3 2h0c1 6 3 9 6 14 2 1 3 2 4 4l2 2h0l1 3h0-2c-3-1-5-3-8-3s-9-1-12 1l1 1c-6 2-11 6-16 9-4 0-8 6-12 7-2 0-3 0-5 2l-1-1-7 5h-1l-3 2v3h0c-4 3-8 5-11 7-2 2-4 3-7 5l1 1c-2 1-5 2-6 4-1 0-1 1-1 2-3 0-7 1-11 2-1 1-4 1-5 2-3 1-15 8-16 8l-22 14 3 1 4-2 2 1h0l10-5c-2 2-4 4-7 5v1c1 0 3 1 5 1h0l-3 1c-2 2-5 3-8 3-2 0-4 0-6 1-3 0-6 1-9 3l4-1 1 1h1v1l-1 1c3-1 7-3 10-3v1l4-1v1c-3 0-6 1-8 2-1 1-1 2-1 3 0 2 1 3 2 4l-2 1c-7 2-12 6-17 11l-1 1c-1 2-3 3-4 4-1 2-3 3-4 5-1 0-1 0-2-1l7-15-2 1h-1l12-31 1 1 1-1 2-4 2-2c0-2 0-5 1-6s1-2 1-3l3-3 1-2v1l3-3 1 1c7-2 13-7 18-12l3-5-1-2-1-1c-1-1-1-4-2-6 0-1 1-2 1-3l1-1c-1-2-2-1-3-2 0 0-1-3-2-4h0l-1 1v1c-3 1-3 4-5 6v1l-1 1-1 1h0v-2l-1-1c1-2 3-5 3-8h0c0-1 1-3 1-4l6-13c1-1 1-1 3-2v-1l1-4c1-1 2-3 2-4h0l5-8 2-3c1-1 3-3 3-4l11-13 2-3c1-1 3-3 4-5 0 0 0-1 1-2h-2l-2 1c-1 0-1 0-1-1l2-1c2-1 3-2 4-5h-2l-2 2h-1c2-3 4-5 5-8 1-1 1-3 2-4l6-10 7-8 1 1c1-3 3-5 5-6 0 1-1 2-1 3 0 3 1 5 3 7 3 1 6 1 9 1l1-1 5 1 7 2c-1-1-3-2-5-3l9-7h1c3-2 5-4 8-2 2 2 3 4 3 6 1 2 2 3 2 5v1c1 2 1 4 0 6l-1 7c4-3 1-10 4-14h3c1 0 2 1 2 2 1 2 1 4 0 6-1 5-3 10-5 14l1 1c0-1 1-2 1-2 1-1 3-4 3-5h1 1c-1 2-1 2-1 4 1 0 2-1 3 0h1v1 3c2 1 4 1 6 1h0l1 1-1 1c0 2 0 2 1 4h0c2 0 1-1 2-2h3l2-6-2-1 3-6h0l2-5 9-26z" class="p"></path><path d="M654 704l1 2c-1 0-2 2-2 2-1-1-2-1-3-1 1-1 3-2 4-3z" class="P"></path><path d="M645 710c1-2 3-3 5-3 1 0 2 0 3 1-2 1-3 2-4 3-2-1-3-1-4-1z" class="E"></path><path d="M654 704c2-1 10-6 12-6h1l-12 8-1-2z" class="n"></path><path d="M685 685l5-3h3c1 1 1 2 1 3h0c0 1-1 2-1 3-1 1-3 2-4 2 0-2 2-1 2-2 1-1 1-3 1-4h-1c-1 0-2 0-3 1h-3 0z" class="L"></path><defs><linearGradient id="Dd" x1="724.324" y1="668.274" x2="718.4" y2="677.865" xlink:href="#B"><stop offset="0" stop-color="#969394"></stop><stop offset="1" stop-color="#b0afb0"></stop></linearGradient></defs><path fill="url(#Dd)" d="M714 678c4-3 8-7 12-11l1 1h0c0 3-3 4-4 6s-4 4-6 5h-1v-1s-1 0-1 1l-1-1z"></path><path d="M606 739c1 0 3-2 4-2 2-1 3-1 5-2-2 2-5 3-5 5h1 0 1l1-1h0c1-1 1-1 2-1h0c1-1 2-1 2-2h1 2l-15 8c0-2 0-3 1-5z" class="C"></path><path d="M721 654l1-1 1 1 2-1 1 1c0 1-1 2-3 3 1 3 0 4-2 7-1 2-3 4-5 4l4-7c1-1 1-3 1-5v-2z" class="S"></path><path d="M680 702s0 1-1 1c-3 2-5 4-7 6l-3 2-5 4c-3 2-5 5-8 6 1-1 1-2 2-3h1l-1-1c7-6 14-11 22-15z" class="f"></path><path d="M680 702c2-2 6-4 9-6l-1 1-3 3c-2 1-3 3-4 5-1 0-2 1-2 2v-1h2v-1h2l-3 2-7 4c-1 1-2 2-3 2h-2l1-2 3-2c2-2 4-4 7-6 1 0 1-1 1-1z" class="C"></path><path d="M670 713c1 0 2-1 3-2l7-4v3h0c-4 3-8 5-11 7-3 1-6 2-8 3l-6 3v-1l1-1c3-1 5-4 8-6l5-4-1 2h2z" class="W"></path><path d="M664 715l5-4-1 2h2l-4 3c-1-1-1 0-2-1z" class="B"></path><path d="M730 659c1 0 2 1 3 2h0c1 6 3 9 6 14-2 0-6-4-7-5-4 0-5 1-8 4h-1c1-2 4-3 4-6h0l-1-1c2-2 3-3 4-6v-2z" class="M"></path><path d="M726 667c2-2 3-3 4-6 0 2 1 3 0 5l-1 2h0c1 0 2 1 3 2-4 0-5 1-8 4h-1c1-2 4-3 4-6h0l-1-1z" class="W"></path><path d="M749 619h0c3-2 4-5 6-7v17c-1 5-2 8-6 12l-1-2c-2 1-6 5-8 7l-1-1c3-4 9-5 12-10 2-4 2-9 1-13h-1c-1 1-2 2-3 4 0 2-2 3-2 5v1l-1-2c-1 3-2 5-4 7h-1c2-3 4-5 5-8l2-6 2-4z" class="G"></path><path d="M737 630c0-1 2-2 2-3 0 2 0 2 1 4h0c2 0 1-1 2-2h3c-1 3-3 5-5 8l-4 5c-3 3-5 6-7 10l-2 2h-1l-1-1-2 1-1-1-1 1v-1l1-1c1-3 3-4 5-6s4-5 5-8c2-2 5-5 5-8z" class="D"></path><path d="M734 641l2 1c-3 3-5 6-7 10h-3v1c-1 0-2-1-3-1 4-2 8-7 10-11h1z" class="Z"></path><path d="M745 629c-1 3-3 5-5 8l-4 5-2-1 6-10h0c2 0 1-1 2-2h3z" class="l"></path><defs><linearGradient id="De" x1="714.738" y1="656.834" x2="696.024" y2="685.012" xlink:href="#B"><stop offset="0" stop-color="#9c9a9a"></stop><stop offset="1" stop-color="#bfbebe"></stop></linearGradient></defs><path fill="url(#De)" d="M721 656c0 2 0 4-1 5l-4 7c-6 7-15 15-23 20 0-1 1-2 1-3h0c0-1 0-2-1-3h-3c3-2 6-3 9-4l6-6c3-2 4-4 6-7 1-1 2-2 2-3h0c3-2 5-4 8-6z"></path><path d="M645 710c1 0 2 0 4 1-3 1-7 5-10 6-1 1-9 5-9 6h-2l-1-1-10 5c-3 3-8 5-11 5h-2l-1-1 3-3 1 1c7-2 13-7 18-12l8-5 1 1h1l1 1c3-1 6-3 9-4z" class="D"></path><path d="M634 713h1l1 1c-2 2-3 3-6 3l-1-1 5-3z" class="P"></path><path d="M627 722h0c2-2 4-3 6-4l5-2 1 1c-1 1-9 5-9 6h-2l-1-1z" class="J"></path><path d="M625 717l8-5 1 1-5 3c-2 2-4 3-6 5-3 2-6 3-8 6h2c-3 3-8 5-11 5h-2l-1-1 3-3 1 1c7-2 13-7 18-12z" class="W"></path><path d="M628 732c2-1 4-2 6-2 3-1 5-3 7-4 3-1 7-3 10-5 2-1 4-3 7-4l1 1h-1c-1 1-1 2-2 3l-1 1v1l6-3c2-1 5-2 8-3-2 2-4 3-7 5l1 1c-2 1-5 2-6 4-1 0-1 1-1 2-3 0-7 1-11 2-1 1-4 1-5 2-3 1-15 8-16 8l-2-1h0l7-4v-1h1l3-2c3-1 7-2 9-4-1 0-2 0-3 1h-2c-2 1-5 2-8 2h-1z" class="Z"></path><path d="M662 722l1 1c-2 1-5 2-6 4-1 0-1 1-1 2-3 0-7 1-11 2l1-2 16-7z" class="h"></path><path d="M629 736l17-7-1 2c-1 1-4 1-5 2-3 1-15 8-16 8l-2-1h0l7-4z" class="X"></path><defs><linearGradient id="Df" x1="603.12" y1="737.953" x2="614.322" y2="733.886" xlink:href="#B"><stop offset="0" stop-color="#878586"></stop><stop offset="1" stop-color="#aaa9aa"></stop></linearGradient></defs><path fill="url(#Df)" d="M617 727l10-5 1 1h2c-1 3-3 4-5 5-1 1-2 1-3 1s-2 2-2 2v1c-1 1-5 3-5 3-2 1-3 1-5 2-1 0-3 2-4 2-1 2-1 3-1 5-1 1-2 2-3 2l-4 2c-2 1-3 2-5 2l2-4 2-2c0-2 0-5 1-6s1-2 1-3l3-3 1-2v1l1 1h2c3 0 8-2 11-5z"></path><path d="M597 744l3-4c0 1-1 3-2 4 0 1 0 1-1 2 0 1 0 2 1 2-2 1-3 2-5 2l2-4 2-2z" class="N"></path><path d="M604 741l2-2c-1 2-1 3-1 5-1 1-2 2-3 2l-4 2c-1 0-1-1-1-2 2-1 3-3 5-4 0-1 1-1 2-1z" class="i"></path><path d="M604 741l2-2c-1 2-1 3-1 5-1 1-2 2-3 2 0-2 1-3 2-5z" class="g"></path><path d="M599 735l3-3 1 2c2 0 4 0 6-1h2c-1 1-6 4-7 4l-4 3-3 4c0-2 0-5 1-6s1-2 1-3z" class="U"></path><path d="M598 738h2c1-1 2-1 4-1l-4 3-3 4c0-2 0-5 1-6z" class="K"></path><path d="M617 727l10-5 1 1-8 5c-3 2-6 4-9 5h-2c-2 1-4 1-6 1l-1-2 1-2v1l1 1h2c3 0 8-2 11-5z" class="Q"></path><path d="M711 663l2-1c0 1-1 2-2 3-2 3-3 5-6 7l-6 6c-3 1-6 2-9 4l-5 3c-14 7-27 13-40 20-3 3-7 5-10 8h-1l-1-1-8 5 3-5 2-2v1l13-10 6-3h2l1-1c5-2 10-3 15-5s11-6 15-9c1-2 3-4 5-5l4-1c2-2 3-2 4-4 5-4 11-6 16-10z" class="g"></path><path d="M711 663l2-1c0 1-1 2-2 3-2 3-3 5-6 7-3 1-6 3-9 4-2 1-4 2-5 3l-6 3h-1l-2 1c1-2 3-4 5-5l4-1c2-2 3-2 4-4 5-4 11-6 16-10z" class="J"></path><path d="M759 585l1 2v3l1 1c0 2-2 5-1 7h0l1-2v2 2 13c0 6 0 13-1 19v10 5h0c-1 1-1 2-1 4l-1-1-1 1c-1-4-2-5-6-7h-2v-3h0c4-4 5-7 6-12v-17c-2 2-3 5-6 7h0l-2 4-2-1 3-6h0l2-5 9-26z" class="W"></path><path d="M759 636l-1-2c-2-5-1-12 0-17 0 1 0 6 1 7v12z" class="Q"></path><path d="M759 624c1 2 1 6 1 8v10 5h0c-1 1-1 2-1 4l-1-1 1-14v-12z" class="V"></path><path d="M755 629c1 2 1 5 1 7v1c-1 2-1 5-1 7v1c-2-1-2-1-4-1h-2v-3h0c4-4 5-7 6-12z" class="M"></path><defs><linearGradient id="Dg" x1="765.027" y1="598.212" x2="754.973" y2="618.788" xlink:href="#B"><stop offset="0" stop-color="#3c3a39"></stop><stop offset="1" stop-color="#5d5859"></stop></linearGradient></defs><path fill="url(#Dg)" d="M760 598h0l1-2v2 2 13c0 6 0 13-1 19 0-2 0-6-1-8-1-1-1-6-1-7l2-19z"></path><path d="M759 585l1 2v3l-6 20c-2 3-4 6-5 9l-2 4-2-1 3-6h0l2-5 9-26z" class="a"></path><path d="M732 670c1 1 5 5 7 5 2 1 3 2 4 4l2 2h0l1 3h0-2c-3-1-5-3-8-3s-9-1-12 1l1 1c-6 2-11 6-16 9-4 0-8 6-12 7-2 0-3 0-5 2l-1-1-7 5h-1-2v1h-2v1c0-1 1-2 2-2 1-2 2-4 4-5l3-3 1-1h0l25-18 1 1c0-1 1-1 1-1v1h1c2-1 5-3 6-5h1c3-3 4-4 8-4z" class="E"></path><path d="M715 684c1-1 3-2 4-2 6-3 11-4 17-1-3 0-9-1-12 1-3 1-5 3-9 2z" class="O"></path><path d="M732 670c1 1 5 5 7 5 2 1 3 2 4 4-2 0-3-1-4-2-5-3-8-5-15-3 3-3 4-4 8-4z" class="C"></path><defs><linearGradient id="Dh" x1="715.95" y1="690.823" x2="697.046" y2="690.673" xlink:href="#B"><stop offset="0" stop-color="#3f3a3d"></stop><stop offset="1" stop-color="#666462"></stop></linearGradient></defs><path fill="url(#Dh)" d="M724 682l1 1c-6 2-11 6-16 9-4 0-8 6-12 7-2 0-3 0-5 2l-1-1 24-16c4 1 6-1 9-2z"></path><defs><linearGradient id="Di" x1="651.074" y1="684.349" x2="656.12" y2="699.774" xlink:href="#B"><stop offset="0" stop-color="#6e6b6d"></stop><stop offset="1" stop-color="#939393"></stop></linearGradient></defs><path fill="url(#Di)" d="M676 681l6-3c4-1 9-4 13-5-1 2-2 2-4 4l-4 1c-2 1-4 3-5 5-4 3-10 7-15 9s-10 3-15 5l-1 1h-2l-6 3-13 10v-1l-2 2-1-2-1-1c-1-1-1-4-2-6 0-1 1-2 1-3l1-1c5-1 11-8 14-11 1 0 3-2 3-2h3c3 0 6-1 8-1 7 0 15-3 22-4z"></path><path d="M625 700c2 0 2 1 3 3v1c1 2 1 3 1 5h1v1l-2 2-1-2-1-1c-1-1-1-4-2-6 0-1 1-2 1-3z" class="D"></path><path d="M628 732h1c3 0 6-1 8-2h2c1-1 2-1 3-1-2 2-6 3-9 4l-3 2h-1v1l-7 4h0l2 1-22 14 3 1 4-2 2 1h0l10-5c-2 2-4 4-7 5v1c1 0 3 1 5 1h0l-3 1c-2 2-5 3-8 3-2 0-4 0-6 1-3 0-6 1-9 3l4-1 1 1h1v1l-1 1c3-1 7-3 10-3v1l4-1v1c-3 0-6 1-8 2-1 1-1 2-1 3 0 2 1 3 2 4l-2 1c-7 2-12 6-17 11l-1 1c-1 2-3 3-4 4-1 2-3 3-4 5-1 0-1 0-2-1l7-15-2 1h-1l12-31 1 1 1-1c2 0 3-1 5-2l4-2c1 0 2-1 3-2l15-8c3-1 6-3 8-4z" class="t"></path><path d="M602 755l3 1c-2 1-3 2-5 2l-2-1 4-2z" class="F"></path><path d="M600 758c-2 2-7 4-11 5 1-2 7-5 9-6l2 1z" class="J"></path><path d="M599 766l-1 1c3-1 7-3 10-3v1c-2 0-5 1-7 2s-4 2-5 2-3 1-4 1c1-1 1-1 1-2h-4c-1 1-3 1-4 2h0-1c1-1 1-2 2-3h1c2 0 4-2 6-2l4-1 1 1h1v1z" class="q"></path><path d="M611 755h0l10-5c-2 2-4 4-7 5v1c1 0 3 1 5 1h0l-3 1c-2 2-5 3-8 3-2 0-4 0-6 1-3 0-6 1-9 3-2 0-4 2-6 2 3-3 8-5 12-7l12-5z" class="C"></path><path d="M616 758c-1 0-3 0-5 1h-2v-1h0c0-1 2-2 3-3h2v1c1 0 3 1 5 1h0l-3 1z" class="D"></path><path d="M628 732h1c3 0 6-1 8-2h2c1-1 2-1 3-1-2 2-6 3-9 4l-3 2h-1v1l-7 4h0l-33 19 3-8 1-1c2 0 3-1 5-2l4-2c1 0 2-1 3-2l15-8c3-1 6-3 8-4z" class="l"></path><defs><linearGradient id="Dj" x1="582.489" y1="780.506" x2="603.893" y2="772.672" xlink:href="#B"><stop offset="0" stop-color="#abaaaa"></stop><stop offset="1" stop-color="#c9c9c9"></stop></linearGradient></defs><path fill="url(#Dj)" d="M608 765l4-1v1c-3 0-6 1-8 2-1 1-1 2-1 3 0 2 1 3 2 4l-2 1c-7 2-12 6-17 11l-1 1c-1 2-3 3-4 4-1 2-3 3-4 5-1 0-1 0-2-1l7-15c1-2 3-3 4-5 1 0 2-1 2-1l8-5c1 0 3-1 5-2s5-2 7-2z"></path><defs><linearGradient id="Dk" x1="575.904" y1="792.824" x2="581.364" y2="791.329" xlink:href="#B"><stop offset="0" stop-color="#787274"></stop><stop offset="1" stop-color="#8e8b8c"></stop></linearGradient></defs><path fill="url(#Dk)" d="M586 775l-1 3v3l2-1c0 1 0 1-1 2s-2 3-1 4h1l-1 1c-1 2-3 3-4 4-1 2-3 3-4 5-1 0-1 0-2-1l7-15c1-2 3-3 4-5z"></path><path d="M719 614c4-3 1-10 4-14h3c1 0 2 1 2 2 1 2 1 4 0 6-1 5-3 10-5 14l1 1c0-1 1-2 1-2 1-1 3-4 3-5h1 1c-1 2-1 2-1 4 1 0 2-1 3 0h1v1 3c2 1 4 1 6 1h0l1 1-1 1c0 1-2 2-2 3 0 3-3 6-5 8-1 3-3 6-5 8s-4 3-5 6l-1 1v1 2c-3 2-5 4-8 6h0l-2 1c-5 4-11 6-16 10-4 1-9 4-13 5l-6 3c-7 1-15 4-22 4-2 0-5 1-8 1h-3s-2 2-3 2c-3 3-9 10-14 11-1-2-2-1-3-2 0 0-1-3-2-4h0l-1 1v1c-3 1-3 4-5 6v1l-1 1-1 1h0v-2l-1-1c1-2 3-5 3-8h0c0-1 1-3 1-4l6-13c1-1 1-1 3-2v-1l1-4c1-1 2-3 2-4h0l5-8 3 1c0 3 1 4 3 6h0c1 1 2 2 2 3h2c0 1 1 2 2 2v-1h-1l1-1h1c2 1 3 2 5 2s3 0 4 1h1c0-1 1-1 1-1 2 0 4 0 6-1-1 1-2 2-4 2h-1c1 1 3 0 4 1l1-1c1 0 2 0 4-1 1 0 1 0 2-1h2c10-5 21-12 29-20 3-5 6-9 9-13 2-2 4-6 6-8l4-13z" class="E"></path><path d="M652 684c8-1 16-3 24-5h1c0 1-1 1-1 2-7 1-15 4-22 4h-2-1-2c-2 0-3 0-5-1h7 1z" class="X"></path><path d="M709 662c1 1 1 1 2 1-5 4-11 6-16 10-4 1-9 4-13 5l-6 3c0-1 1-1 1-2h-1c0-1 5-2 5-2 8-3 17-7 23-12l5-3z" class="V"></path><path d="M628 665h0c1 2 0 2 2 4s7 4 10 5l2 1c1 2 2 2 4 3h1v1h-2c-3 0-6 0-9-1-2 0-4-1-6-1-1 0-1 0-2 1-1-1-2-3-3-5l1-4c1-1 2-3 2-4z" class="R"></path><path d="M640 674l2 1c1 2 2 2 4 3h1v1h-2c-3-1-6-1-9-3l4-2z" class="Q"></path><path d="M628 665h0c1 2 0 2 2 4s7 4 10 5l-4 2c-2-1-3-2-5-3-2-2-3-3-5-4 1-1 2-3 2-4z" class="Z"></path><defs><linearGradient id="Dl" x1="630.519" y1="663.507" x2="642.109" y2="666.284" xlink:href="#B"><stop offset="0" stop-color="#a9a8a8"></stop><stop offset="1" stop-color="#cdcccc"></stop></linearGradient></defs><path fill="url(#Dl)" d="M633 657l3 1c0 3 1 4 3 6h0c1 1 2 2 2 3-1 1 0 3 0 5 0 0 1 1 1 2l1 1h-1l-2-1c-3-1-8-3-10-5s-1-2-2-4l5-8z"></path><path d="M622 676c1-1 1-1 3-2v-1c1 2 2 4 3 5 7 6 16 6 24 6h-1-7c2 1 3 1 5 1h2 1 2c-2 0-5 1-8 1h-3s-2 2-3 2c-3 3-9 10-14 11-1-2-2-1-3-2 0 0-1-3-2-4h0l-1 1v1c-3 1-3 4-5 6v1l-1 1-1 1h0v-2l-1-1c1-2 3-5 3-8h0c0-1 1-3 1-4l6-13z" class="x"></path><defs><linearGradient id="Dm" x1="676.896" y1="638.925" x2="697.532" y2="665.21" xlink:href="#B"><stop offset="0" stop-color="#cbcac9"></stop><stop offset="1" stop-color="#f8f8f7"></stop></linearGradient></defs><path fill="url(#Dm)" d="M721 643c1-2 3-3 4-5 2-2 4-3 5-6 1 0 1-1 2-2v1h2c1 0 2 0 3-1 0 3-3 6-5 8-1 3-3 6-5 8s-4 3-5 6l-1 1v1 2c-3 2-5 4-8 6h0l-2 1c-1 0-1 0-2-1l-5 3v-1c1-1 3-2 5-3v-1-1h-1c-3 2-6 4-9 5-4 2-8 4-12 5-5 2-12 4-17 7h0 0c-3 2-8 3-12 4h-1c-3 0-8 0-10-1v-1h-1c-2-1-3-1-4-3h1l-1-1c0-1-1-2-1-2 0-2-1-4 0-5h2c0 1 1 2 2 2v-1h-1l1-1h1c2 1 3 2 5 2s3 0 4 1c4 1 12 2 15 0h1 1c3-1 6-3 9-4 14-7 27-15 40-23z"></path><path d="M699 664c2-2 4-3 6-4l10-8c3-2 4-4 7-5-1 2-5 7-7 8l-1 1-2 1c-1 0 0 0-1 1-1 0-2 0-2 1h-1c-3 2-6 4-9 5z" class="T"></path><path d="M643 675c9 2 17 3 27 1h0c-3 2-8 3-12 4h-1c-3 0-8 0-10-1v-1h-1c-2-1-3-1-4-3h1z" class="F"></path><path d="M722 647c4-3 6-6 10-9-1 3-3 6-5 8s-4 3-5 6l-1 1v1 2c-3 2-5 4-8 6h0l-2 1c-1 0-1 0-2-1l-5 3v-1c1-1 3-2 5-3v-1-1c0-1 1-1 2-1 1-1 0-1 1-1l2-1 1-1c2-1 6-6 7-8z" class="p"></path><path d="M721 653v1 2c-3 2-5 4-8 6h0l-2 1c-1 0-1 0-2-1l12-9z" class="K"></path><path d="M719 614c4-3 1-10 4-14h3c1 0 2 1 2 2 1 2 1 4 0 6-1 5-3 10-5 14l1 1c0-1 1-2 1-2 1-1 3-4 3-5h1 1c-1 2-1 2-1 4 1 0 2-1 3 0h1v1 3c2 1 4 1 6 1h0l1 1-1 1c0 1-2 2-2 3-1 1-2 1-3 1h-2v-1c-1 1-1 2-2 2-1 3-3 4-5 6-1 2-3 3-4 5-13 8-26 16-40 23-3 1-6 3-9 4h-1-1c-3 2-11 1-15 0h1c0-1 1-1 1-1 2 0 4 0 6-1-1 1-2 2-4 2h-1c1 1 3 0 4 1l1-1c1 0 2 0 4-1 1 0 1 0 2-1h2c10-5 21-12 29-20 3-5 6-9 9-13 2-2 4-6 6-8l4-13z" class="H"></path><path d="M719 614c4-3 1-10 4-14h3c1 0 2 1 2 2 1 2 1 4 0 6 0-2 0-2-1-4v-2h-4c-1 2 0 4-1 6 0 2 0 5-1 7v1h1l-3 6-3 4h0l-1 1 4-13z" class="E"></path><path d="M728 616h1 1c-1 2-1 2-1 4 1 0 2-1 3 0h1v1 3c2 1 4 1 6 1h0l1 1-1 1c0 1-2 2-2 3-1 1-2 1-3 1h-2v-1c-1 1-1 2-2 2-1 3-3 4-5 6-1 2-3 3-4 5l-1-1h-1c-1 0-1 0-2-1v-1c-2 0-2-1-3-2 2-5 5-9 6-13l1-2 1 1v-1c0 1 0 0 1 1l1-1c0-1 1-2 1-2 1-1 3-4 3-5z" class="p"></path><path d="M739 625h0l1 1-1 1c0 1-2 2-2 3-1 1-2 1-3 1v-1c1-2 3-4 5-5z" class="F"></path><path d="M727 626c2-2 3-4 6-5v3c-1 2-3 4-5 6l-1-1c1 0 1-1 2-1v-2c-1 0-1 1-2 1v-1z" class="P"></path><path d="M728 616h1 1c-1 2-1 2-1 4 1 0 2-1 3 0h1v1c-3 1-4 3-6 5v-1l-1-1 1-1c1-1 1-2 0-3 1-1 0-1 1-2l1-1-1-1z" class="G"></path><path d="M706 591h1c3-2 5-4 8-2 2 2 3 4 3 6 1 2 2 3 2 5v1c1 2 1 4 0 6l-1 7-4 13c-2 2-4 6-6 8-3 4-6 8-9 13-8 8-19 15-29 20h-2c-1 1-1 1-2 1-2 1-3 1-4 1l-1 1c-1-1-3 0-4-1h1c2 0 3-1 4-2-2 1-4 1-6 1 0 0-1 0-1 1h-1c-1-1-2-1-4-1s-3-1-5-2h-1l-1 1h1v1c-1 0-2-1-2-2h-2c0-1-1-2-2-3h0c-2-2-3-3-3-6l-3-1 2-3c1-1 3-3 3-4l11-13 2-3c1-1 3-3 4-5 0 0 0-1 1-2h-2l-2 1c-1 0-1 0-1-1l2-1c2-1 3-2 4-5h-2l-2 2h-1c2-3 4-5 5-8 1-1 1-3 2-4l6-10 7-8 1 1c1-3 3-5 5-6 0 1-1 2-1 3 0 3 1 5 3 7 3 1 6 1 9 1l1-1 5 1 7 2c-1-1-3-2-5-3l9-7z" class="T"></path><path d="M682 649h0c1-1 1 0 2-1h1l1-1v2c-1 2-2 3-2 4-1 2-2 3-4 5l3-8-1-1z" class="H"></path><path d="M706 607v-1c2-5 8-8 12-11 1 2 2 3 2 5v1c1 2 1 4 0 6v-1c-1-2 0-4-1-6l-1-3c-1 0-2 0-3 1-3 2-7 5-8 9 0 1 0 3 1 4v-1 4c0 2 0 2-1 4h-1v-2c-3 0-4 1-5 0-2-2-5-4-8-4l-4-1c3 0 6 0 9 2h1c2-1 4-1 6-2h1v-4z" class="L"></path><path d="M669 614c6-3 12-3 20-3l4 1c3 0 6 2 8 4l-1 1-1-1c-1-1-3-2-4-2h-3 2c0 1 2 1 2 1l2 2-1 1c-1-1-3-2-5-2h0c-2-1-4-1-6-1-4-2-8-1-12 0-2 1-6 3-9 2l4-3z" class="c"></path><path d="M686 615c2 0 4 0 6 1h0c2 0 4 1 5 2 4 3 6 6 6 11s-2 10-5 13c-2 2-5 3-7 5 0-2 3-3 4-4 1-2 2-3 2-5l1-3v-5c0-2-1-4-2-6l-2-2-4-3 2-1c-3-1-7-2-11-2l5-1z" class="Y"></path><path d="M692 618c2 1 5 3 6 5h1c0 2 1 3 1 4v1c1 2 0 3 0 5 0 1 0 2-1 3h0l-1-1v-5c0-2-1-4-2-6l-2-2-4-3 2-1z" class="a"></path><path d="M680 606h3l1 1c3-1 8-2 11-1h2l4 2s1 0 2 1h1 1l1-1v-1 4h-1c-2 1-4 1-6 2h-1c-3-2-6-2-9-2-8 0-14 0-20 3 0-1 0-1 1-2h0l1-3 1 1 7-4h1z" class="F"></path><path d="M680 606h3l1 1h-1c-3 0-5 2-8 3-2 0-3 1-5 2h0l1-3 1 1 7-4h1z" class="P"></path><path d="M706 608v-1 4h-1c-3-1-10-1-13-2l2-1h2c2 0 3 1 5 0 0 0 1 0 2 1h1 1l1-1z" class="Y"></path><path d="M684 607c3-1 8-2 11-1h2l4 2c-2 1-3 0-5 0h-2l-2 1c-3 0-6 0-9-1v-1h1z" class="e"></path><path d="M695 606h2l4 2c-2 1-3 0-5 0h-2c-1 0-2-1-2-1h-1 0l4-1z" class="K"></path><defs><linearGradient id="Dn" x1="705.814" y1="591.552" x2="710.674" y2="601.464" xlink:href="#B"><stop offset="0" stop-color="#9a9999"></stop><stop offset="1" stop-color="#b6b6b5"></stop></linearGradient></defs><path fill="url(#Dn)" d="M706 591h1c3-2 5-4 8-2 2 2 3 4 3 6-4 3-10 6-12 11v1 1c-1-3-2-5-4-7-1-1-3-2-5-3l9-7z"></path><path d="M665 617c3 1 7-1 9-2 4-1 8-2 12 0l-5 1c4 0 8 1 11 2l-2 1c-7-2-14-1-21 3-2 0-3 2-5 3-1 0-2 3-3 3-2 2-2 3-4 4l2-5c-2 0-3 2-4 2 0 0 0-1 1-2h-2l-2 1c-1 0-1 0-1-1l2-1c2-1 3-2 4-5l1-1c2 0 4-2 7-3z" class="m"></path><path d="M665 617c3 1 7-1 9-2 4-1 8-2 12 0l-5 1c-9 1-20 6-27 11l-2 1c-1 0-1 0-1-1l2-1c2-1 3-2 4-5l1-1c2 0 4-2 7-3z" class="P"></path><path d="M687 620c1 0 3 1 5 1l2 1 2 2c1 2 2 4 2 6v5l-1 3c0 2-1 3-2 5-1 1-4 2-4 4h0 0-1v-1l-1-1-1-1-1-1c0-2 2-5 3-7l-2 1v-4-6c-1 0-1 0-1 1h-1c0-1 0-1-1-1 0-2 1-3 1-4s0-2 1-3z" class="k"></path><path d="M692 626v-2-1c1 1 2 1 3 1h1c1 2 2 4 2 6l-1 1h-1c0-2-1-4-2-5 0-1-1 0-2 0z" class="H"></path><path d="M687 620c1 0 3 1 5 1l2 1 2 2h-1c-1 0-2 0-3-1v1 2 1c1 3 1 7 0 10 0 2-1 4-2 6h0c0-3 1-6 1-9-1 0-1 1-1 2l-2 1v-4-6c-1 0-1 0-1 1h-1c0-1 0-1-1-1 0-2 1-3 1-4s0-2 1-3z" class="L"></path><path d="M687 620c1 0 3 1 5 1l2 1 2 2h-1c-1 0-2 0-3-1v1 2 1 5c0-1-1-1-1-2 0-2-1-4-3-6h0v3c-1 0-1 0-1 1h-1c0-1 0-1-1-1 0-2 1-3 1-4s0-2 1-3z" class="F"></path><path d="M676 592c0-1 0-1 1-1 0 3 1 5 3 7 3 1 6 1 9 1l1-1 5 1 7 2c2 2 3 4 4 7l-1 1h-1-1c-1-1-2-1-2-1l-4-2h-2c-3-1-8 0-11 1l-1-1h-3-1v-1h-2-1v-1h2v-1h-4v-3c1-1 1-2 1-3l1-5z" class="k"></path><path d="M696 604h0c2 1 4 1 6 2l1-1c1 2 1 2 0 4-1-1-2-1-2-1l-4-2-1-2z" class="D"></path><path d="M690 598l5 1 7 2c2 2 3 4 4 7l-1 1h-1-1c1-2 1-2 0-4v-1c-2-3-10-4-14-5l1-1z" class="K"></path><path d="M679 606v-1h-2-1v-1h2v-1h-4v-3c1-1 1-2 1-3l1 5c1 1 14 2 17 2h3l1 2h-2c-3-1-8 0-11 1l-1-1h-3-1z" class="B"></path><path d="M693 604h3l1 2h-2c-3-1-8 0-11 1l-1-1h-3l2-2h3l1 1c2 0 5 0 7-1z" class="R"></path><path d="M678 588c0 1-1 2-1 3-1 0-1 0-1 1l-1 5c0 1 0 2-1 3v3h4v1h-2v1h1 2v1l-7 4-1-1-1 3h0c-1 1-1 1-1 2l-4 3c-3 1-5 3-7 3l-1 1h-2l-2 2h-1c2-3 4-5 5-8 1-1 1-3 2-4l6-10 7-8 1 1c1-3 3-5 5-6z" class="F"></path><path d="M660 618l2-2c4-2 5-5 9-7l-1 3h0c-1 1-1 1-1 2l-4 3c-3 1-5 3-7 3 0-1 1-1 2-2z" class="B"></path><path d="M678 588c0 1-1 2-1 3-1 0-1 0-1 1-1 1-1 1-2 3-1 6-4 10-7 15-2 4-5 5-7 8-1 1-2 1-2 2l-1 1h-2c3-2 5-6 7-9 4-6 7-12 11-18 1-3 3-5 5-6z" class="i"></path><path d="M672 593l1 1c-4 6-7 12-11 18-2 3-4 7-7 9l-2 2h-1c2-3 4-5 5-8 1-1 1-3 2-4l6-10 7-8z" class="n"></path><defs><linearGradient id="Do" x1="668.587" y1="622.657" x2="675.143" y2="633.881" xlink:href="#B"><stop offset="0" stop-color="#665e5e"></stop><stop offset="1" stop-color="#878484"></stop></linearGradient></defs><path fill="url(#Do)" d="M661 628c1 0 2-3 3-3 2-1 3-3 5-3 7-4 14-5 21-3l4 3-2-1c-2 0-4-1-5-1-1 1-1 2-1 3s-1 2-1 4c1 0 1 0 1 1h1c0-1 0-1 1-1v6l-3-4c0 1-1 1-1 2l-1 1-1-2c-1-1-1-1-2-1-3-2-5-1-7 0-3 2-6 4-8 6 0 2 0 4 1 6h-4 0l-2-2c0-2 1-3 2-5 0 0 1-1 1-2l-1 1c-1 1-2 2-3 4h-1v-1c1-3 2-5 3-8z"></path><path d="M665 635c0 2 0 4 1 6h-4 0c0-3 1-4 3-6z" class="L"></path><defs><linearGradient id="Dp" x1="680.556" y1="622.342" x2="685.155" y2="626.225" xlink:href="#B"><stop offset="0" stop-color="#7d7879"></stop><stop offset="1" stop-color="#939090"></stop></linearGradient></defs><path fill="url(#Dp)" d="M685 629l-3-3-3-3c1-1 2-1 3-1h0c1-1 3-2 5-2-1 1-1 2-1 3s-1 2-1 4c1 0 1 0 1 1h1c0-1 0-1 1-1v6l-3-4z"></path><defs><linearGradient id="Dq" x1="667.551" y1="635.783" x2="682.465" y2="639.282" xlink:href="#B"><stop offset="0" stop-color="#c8c7c7"></stop><stop offset="1" stop-color="#fcfbfb"></stop></linearGradient></defs><path fill="url(#Dq)" d="M673 629c2-1 4-2 7 0 1 0 1 0 2 1l1 2 1-1c0-1 1-1 1-2l3 4v4c-1 1-1 3-2 4-3 4-5 5-10 6h0c-2 0-3 0-4-1-1 0-1 0-2 1-1-1-1-1-2 0 0-1-1-2-1-3s-1-2-1-3c-1-2-1-4-1-6 2-2 5-4 8-6z"></path><path d="M673 629c2-1 4-2 7 0 1 0 1 0 2 1-2 0-3 0-5 2v1h0v1c-1 0-1 0-1 1l-1-1c0-1-1-1-2-1v-2l1-1s-1 0-1-1z" class="T"></path><path d="M665 635c2-2 5-4 8-6 0 1 1 1 1 1l-1 1c-2 1-5 3-6 5 0 2 0 5 1 6v1c1 2 4 2 6 3h1c1 1 1 0 1 1h0c-2 0-3 0-4-1-1 0-1 0-2 1-1-1-1-1-2 0 0-1-1-2-1-3s-1-2-1-3c-1-2-1-4-1-6z" class="R"></path><path d="M685 629l3 4v4c-1 1-1 3-2 4-3 4-5 5-10 6 0-1 0 0-1-1 4-1 6-3 8-6 2-2 2-5 1-8h-1l1-1c0-1 1-1 1-2z" class="l"></path><path d="M685 629l3 4v4c-1 1-1 3-2 4l-1-2c1-1 1-2 1-3 0-2-1-3-2-4h-1l1-1c0-1 1-1 1-2z" class="I"></path><path d="M655 629c1 0 2-2 4-2l-2 5c2-1 2-2 4-4l-3 8v1h1c1-2 2-3 3-4l1-1c0 1-1 2-1 2-1 2-2 3-2 5l2 2h0 4c0 1 1 2 1 3s1 2 1 3c1-1 1-1 2 0l2 1c3 2 6 2 10 1l1 1c-1 3-2 5-3 8-1 0-6 3-7 3l-5 4h1l-6 3c-2 1-4 1-6 1 0 0-1 0-1 1h-1c-1-1-2-1-4-1s-3-1-5-2h-1l-1 1h1v1c-1 0-2-1-2-2h-2c0-1-1-2-2-3h0c-2-2-3-3-3-6l-3-1 2-3c1-1 3-3 3-4l11-13 2-3c1-1 3-3 4-5z" class="M"></path><path d="M658 636v1l-1 3c-1 3 0 6 0 9h0v-1l-1-2v3c-1 3-4 6-5 9h-1v-1c0-2 0-1-1-2v-1c1 0 1-1 2-1h1c1-1 2-2 2-4 1-2 1-5 2-7 0-2 1-4 2-6z" class="N"></path><path d="M666 641c0 1 1 2 1 3s1 2 1 3c1 3 1 5 0 9h0l-1-1c-2 2-3 4-5 4l-2 1c0-2 0-2 1-3 2-3 2-7 1-10v-1l2-2-2-3h4z" class="G"></path><path d="M661 657h1c1-1 2-2 2-3s1-1 1-2c0 3-2 5-3 7l-2 1c0-2 0-2 1-3z" class="J"></path><path d="M664 644c0 2 2 5 1 8 0 1-1 1-1 2s-1 2-2 3h-1c2-3 2-7 1-10v-1l2-2z" class="Q"></path><defs><linearGradient id="Dr" x1="658.667" y1="654.092" x2="677.503" y2="661.474" xlink:href="#B"><stop offset="0" stop-color="#8e8c8c"></stop><stop offset="1" stop-color="#c3c2c2"></stop></linearGradient></defs><path fill="url(#Dr)" d="M668 647c1-1 1-1 2 0l2 1c3 2 6 2 10 1l1 1c-1 3-2 5-3 8-1 0-6 3-7 3s-2 0-3 1h-2c-3 2-8 7-12 6-1 0-2-1-3-1v-2c-1-1-3-2-4-4l1-1c1 1 2 2 4 3 3 0 4-1 6-3l2-1c2 0 3-2 5-4l1 1h0c1-4 1-6 0-9z"></path><path d="M668 647c1-1 1-1 2 0l2 1c-1 1-1 2-2 3 1 0 2 0 3 1h0c-1 2-1 3-3 4h-2c1-4 1-6 0-9z" class="B"></path><path d="M662 659c2 0 3-2 5-4l1 1c-1 4-5 7-9 8-2 1-3 1-6 1-1-1-3-2-4-4l1-1c1 1 2 2 4 3 3 0 4-1 6-3l2-1z" class="n"></path><path d="M655 629c1 0 2-2 4-2l-2 5c2-1 2-2 4-4l-3 8c-1 2-2 4-2 6-1 2-1 5-2 7 0 2-1 3-2 4h-1c-1 0-1 1-2 1v1c0 1 0 2-1 3h0c-1 2-1 3 0 5 0 0 1-1 1-2 1 2 3 3 4 4v2c1 0 2 1 3 1 4 1 9-4 12-6h2c1-1 2-1 3-1l-5 4h1l-6 3c-2 1-4 1-6 1 0 0-1 0-1 1h-1c-1-1-2-1-4-1s-3-1-5-2h-1l-1 1h1v1c-1 0-2-1-2-2h-2c0-1-1-2-2-3h0c-2-2-3-3-3-6l-3-1 2-3c1-1 3-3 3-4l11-13 2-3c1-1 3-3 4-5z" class="E"></path><path d="M649 661c1 2 3 3 4 4v2c-1-1-1-1-2-1h0c-2-1-2-2-3-3 0 0 1-1 1-2z" class="I"></path><path d="M657 632c2-1 2-2 4-4l-3 8c-1 2-2 4-2 6-1 2-1 5-2 7 0 2-1 3-2 4h-1c-1 0-1 1-2 1v1c0 1 0 2-1 3h0c0-3 1-6 2-9 2-6 4-11 7-17z" class="O"></path><path d="M649 637l2-3c0 3 0 4-2 6 0 2-1 3-1 4-1 3-3 5-4 8s-2 5-2 8c1 2 1 4 2 5-1 0-2-1-2 0l1 2h0-2c0-1-1-2-2-3h0c-2-2-3-3-3-6l-3-1 2-3c1-1 3-3 3-4l11-13z" class="B"></path><path d="M642 660c-1-2-1-3-1-5l3-3c-1 3-2 5-2 8z" class="R"></path><path d="M636 657l1 2c1-1 1-2 1-3 1-1 1-1 1-2s1-2 1-3h0c1-2 2-3 3-4l-4 9c-1 3-1 5 0 8h0c-2-2-3-3-3-6v-1z" class="F"></path><path d="M635 654c1-1 3-3 3-4l11-13c-1 5-3 7-6 10-1 1-2 2-3 4h0c0 1-1 2-1 3s0 1-1 2c0 1 0 2-1 3l-1-2v1l-3-1 2-3z" class="I"></path><path d="M635 654c2 1 1 2 1 3v1l-3-1 2-3z" class="g"></path><path d="M700 365v-1c2-3 2-7 2-10 4-23 3-51-8-71-5-10-14-18-25-21-9-3-19-2-28 2-5 3-8 8-10 14-1 2-1 5-1 8-4-9-8-17-4-27 2-6 6-18 12-21 4-2 10-2 15-2h21 74c31 1 63 3 93-6 10-2 20-7 29-13 7-5 12-10 17-17 0 3 1 6 0 9-2 7-9 13-13 18-10 10-21 18-33 24-9 5-18 9-27 15-12 8-23 19-32 30-19 22-33 47-44 72l-8 17c-1 4-3 9-5 12-5 8-12 13-18 18 5-1 13-7 17-11-2 5-3 12-8 15-11 9-26 15-40 16 8-16 14-33 19-50 2-7 4-13 5-19v-1zM147 166c3 0 6 0 9 3 1 2 2 4 2 6l-1 5-2-1v-2c0-2 0-3-1-4-2-1-4-2-6-2-3 1-5 2-6 4-3 4-4 10-3 15 2 10 12 19 20 24 10 6 20 9 31 12l-1 1 21 4 21 1c6 1 11 1 16 1h20 26l-2-1v-1c3 0 7-1 10 0l1 1h11 0 11c-1-1-6 0-7-1 1 0 3-1 4-2 2 2 3 2 6 2h5c3-1 6-3 8-5h1c1-1 2-1 3-2-1 1-2 2-2 3l-1 1-2 2v1c3 0 6 0 8-1h1c1 0 2 0 4-1 1 1 1 1 1 2h-4l1 1c1 0 3-1 5 0h30c6 0 13 1 19-1h4c0 2-1 3-2 5h0c-1 6 2 14 3 20 1 8 1 16 1 24-1 1-1 1-1 3 0 1 0 2 1 3-7 6-13 13-20 21-6 6-11 13-16 20v1c-1 0-1 0-1 1h-1l3 10h1l1 4 3 6v-1c1 1 1 2 2 3l1 2c0 3 1 7 2 10 2 4 3 8 8 10h1 2c3 0 5 0 8-2-1 0-1 0-1-1l3-3 1 1c1-1 1-1 1-2 1 0 2-1 2-1 2-1 4-2 7-3 0 1 0 2 1 2 1-2 3-4 3-7 1 0 1-1 1-2h3v-7-14l1-1v-1-2c1-3 0-6 1-8l5 1c1 0 3 0 4 1l1-1h1c0-4-1-8 0-12v-1c2 0 2 1 3 2 2 1 4 3 5 5h0c-1 4 0 8 1 12h0l2 1c2 2 4 4 5 7l5 9 1 2v-11h1c0 2 0 5 1 7l1 2h1c0-1 1-3 1-5v-2h4c1 1 2 1 3 1 2 1 2 1 3 2s1 1 1 2h1l1 1c1 1 2 3 3 3l2 2c0-2-1-3-1-4l-1-1 1-1 4 6c1 0 1 0 2 1v-1l3 2h3c-1 3-1 7-1 10v1l1 4h1c1 2 1 4 1 6h2c0 2 1 3 2 4l1 2h1c0 2 1 4 2 5 1 0 1-1 2-2 0-1 1-1 1-2 0-2 1-2 2-4 1-3 1-6 3-8l1-1h-1v-2l-1-1 1-2c-1-5-1-11 0-16l1-16v-6c-1-3 0-6-2-8l-3-3c-1 0-3 0-3-1-2-1-3-3-3-5l-2-1c1-2 2-4 3-5h0v-7-13l-1-1-6-26c-1-3-2-6-3-8-1-4-3-8-6-11v-1h1 2l1-1c-1 0-2-1-2-2l5 1c1 0 2 0 2-1 2-2 3-8 4-11h0l1 1 1-2v1l2-2c-1 0-1-1-1-2l4-2 1-2c1-1 2-1 2-2l-3-3h0v-1-1h5 2c3-2 6-7 7-10 2-3 4-8 8-9 3-1 6-1 8-1l1 1c8 1 16 7 22 12 1-1 1-2 1-3 2 2 3 4 6 4-1 2-1 2-1 3l-1 1c1 0 2 0 2-1 2 0 3-1 4-3h0c0-2-2-3-4-4h0v-3h1l1-1h1c2 2 4 2 6 2l3-2c1-1 3-1 5-2h1c1-2 2-2 5-2h0l2 2h0c3-1 5-2 8-2h0c2 0 4 1 6 0l1 1c2 0 3 0 4 1 1 2 2 2 5 3h0v-1l-1-2 1-1 3 3c2 4 4 9 8 12l2 1h0l1 1 1 1h0c1 0 4 1 5 2h-1c1 2 2 2 4 3l1-1 1 2c-1 0-2 1-4 2 0 0-1 0-2 1 0 1 0 1 1 3h-3c1 3 1 5 1 8 0 2-1 3-2 4h-2 0c1 1 2 1 3 1s1 0 2-1h1c2 0 4 0 7-1-2 1-4 2-5 4l1 1c-9 4-12 12-16 21-2 6-4 12-2 19l3 9c3 5 6 11 10 16 1 1 2 4 3 4-3-8-6-18-2-27 3-5 7-8 12-10 8-3 18-2 26 2 4 2 8 5 10 9-1-1-2-1-3-2v2c1 1 1 3 1 5s0 5-1 7c-2 8-8 17-13 24h0c-1-1 0-3-1-4l-1 1v1c0 3 0 5-2 8h-1c-1-1-2-1-4 0l-8 11c0 2-1 2-1 4l-1 1v1c0 2-2 5-2 7-2 7-1 16 2 22l3 4c3 4 6 6 11 8h7c2 0 4-1 6-2 2 0 4 0 6-1l2 1-2 1h4v-1h1l1 1v-1h2l-1 1v1h0l-1 1c2-1 3-2 4-4l-1 5v1c-1 3-4 4-6 6l2 1 2-2v1c1 1 1 1 1 2-1 4-2 9-4 13-1 4-10 30-11 30l-3 7c13-1 24-4 35-10 4-2 8-4 11-7h0c0 3-2 6-3 8l-5 14-27 70-51 130-44 106-26 65-31 79-10 25c-1 5-4 9-5 14v1l-76-177-41-95-44-103-30-81-34-82c-8-19-17-38-27-57-12-23-30-46-50-64-8-8-17-16-26-22-21-14-47-25-55-51-2-8-2-17 1-24 4-6 8-9 14-11z"></path><path d="M669 442l4 2c1 1 0 3 0 4l-10 25-20 50-54 137-42 110c-5 12-9 25-11 37-1-4-2-9-3-14l-6-22-12-39-22-65-81-217-47-124c-5-15-8-30-7-46 2-14 7-23 20-30 4-2 10-4 14-7 3-2 7-5 10-7-2-1-16 0-19 0h-70l-75 1c-13 0-26-1-38-3-16-3-34-8-47-19-4-3-6-6-9-10h0c11 12 29 18 45 22l21 4 21 1c6 1 11 1 16 1h20 26l-2-1v-1c3 0 7-1 10 0l1 1h11 0 11c-1-1-6 0-7-1 1 0 3-1 4-2 2 2 3 2 6 2h5c3-1 6-3 8-5h1c1-1 2-1 3-2-1 1-2 2-2 3l-1 1-2 2v1c3 0 6 0 8-1h1c1 0 2 0 4-1 1 1 1 1 1 2h-4l1 1c1 0 3-1 5 0h30c6 0 13 1 19-1h4c0 2-1 3-2 5h0c-1 6 2 14 3 20 1 8 1 16 1 24-1 1-1 1-1 3 0 1 0 2 1 3-7 6-13 13-20 21-6 6-11 13-16 20v1c-1 0-1 0-1 1h-1l3 10h1l1 4 3 6v-1c1 1 1 2 2 3l1 2c0 3 1 7 2 10 2 4 3 8 8 10h1 2c3 0 5 0 8-2-1 0-1 0-1-1l3-3 1 1c1-1 1-1 1-2 1 0 2-1 2-1 2-1 4-2 7-3 0 1 0 2 1 2 1-2 3-4 3-7 1 0 1-1 1-2h3v-7-14l1-1v-1-2c1-3 0-6 1-8l5 1c1 0 3 0 4 1l1-1h1c0-4-1-8 0-12v-1c2 0 2 1 3 2 2 1 4 3 5 5h0c-1 4 0 8 1 12h0l2 1c2 2 4 4 5 7l5 9 1 2v-11h1c0 2 0 5 1 7l1 2h1c0-1 1-3 1-5v-2h4c1 1 2 1 3 1 2 1 2 1 3 2s1 1 1 2h1l1 1c1 1 2 3 3 3l2 2c0-2-1-3-1-4l-1-1 1-1 4 6c1 0 1 0 2 1v-1l3 2h3c-1 3-1 7-1 10v1l1 4h1c1 2 1 4 1 6h2c0 2 1 3 2 4l1 2h1c0 2 1 4 2 5l1 1v5 6l1 4c1 2 0 4 1 6v2c-1 4-1 9-1 14 0 2 1 4 0 6 1 2 1 5 1 7l4 27c-1 2-1 4-2 6l1 5c-1 0-1 0-2 1-1 6 0 12-1 18v-3h0l-1 1v27 21l-1 13c0 1 0 4 1 5v1c0 1 0 2 1 3l1-1c2-3 4-5 7-7l6-3c1 0 2-1 4 0 0 1 0 3-1 4v3l-1 1c-5 10-1 20-3 30v1h1v1c2 0 3 0 4-1h6l-2-2-1-1c-1 0-1-1-1-2 1-1 1-2 2-3 3-3 5-4 9-4 1 0 1-1 2-1 1-1 2-1 2-2 3-2 6-5 9-5 1-1 3-2 4-1l2 2v2l1 3v-3c0-1 0-1 1-2v4c0 4 0 9 2 12l1 2v3l-1 1c1 1 2 1 3 2 1 0 2 1 3 1l3-1c1 1 1 1 2 1 1 1 6 1 8 0 1-1 1-1 1-2l1-3v1l1-2c0 2 0 2 1 3l3-1v-3h1v1l1 1c2-2 8-4 10-7h0c3-1 6-4 7-7l3-4 1-1c1-2 1-4 1-6h1c1-2 0-5 0-8 1-3 1-5 1-8 0 0-1 0-1-1 0-2 1-3 2-5l2-2 1-1c0-1 0-1-1-2l1-1c5-6 6-13 9-20v-1c1-3 4-5 6-7l6-6-1-1h1l-1-1v-2l1-1c-1-3 3-7 4-11 1-3 1-7 1-11h-1l-2-11c0-2 1-2 2-4l3-3c1 0 2 0 2 1h3c1 1 1 1 2 0l3 1c2 1 4 0 6-1l3-2 1-3v-1-1l-3-1h-1l-2-1v-1c-1-1-1-1-1-2 1-2 4-3 6-4l1-1h1c1 1 2 1 3 1h0z" class="k"></path><path d="M515 718c1-1 1-1 2-1l1 3-2 1-1-3z" class="b"></path><path d="M516 721l2-1c0 1 1 4 1 5l-1 2-2-6zM386 251l5-3 1 2-4 1-2 1v-1z" class="m"></path><path d="M379 254c2-1 4-2 7-3v1h0c-1 1-3 2-3 3l-1 1s-1 0-1-1v-1l-1 1-1-1z" class="X"></path><path d="M380 353h3c0 3 1 7 2 10l-1 1-4-11z" class="a"></path><path d="M375 339h1l1 4 3 6v-1c1 1 1 2 2 3l1 2h-3l-5-14z" class="K"></path><path d="M291 231c3 0 7-1 10 0l1 1h11 0l-20 1-2-1v-1z" class="b"></path><path d="M665 441h1c1 1 2 1 3 1h0c-4 1-6 2-8 5-1 1-1 1-2 1-1-1-1-1-1-2 1-2 4-3 6-4l1-1zM539 777l2 1c-1 5-3 10-5 15l-2-12c1 1 1 1 2 1 0 1 0 1 1 1l1-1 1-5z" class="X"></path><path d="M340 226h1c1-1 2-1 3-2-1 1-2 2-2 3l-1 1-2 2v1c3 0 6 0 8-1h1c1 0 2 0 4-1 1 1 1 1 1 2h-4l1 1c1 0 3-1 5 0h-31c-1-1-6 0-7-1 1 0 3-1 4-2 2 2 3 2 6 2h5c3-1 6-3 8-5z" class="m"></path><path d="M643 508l1-1c1-2 1-3 3-4l-19 47v-1c1-5 3-9 4-14h-1c-1-1-3-2-4-3l2-2h0c1 1 3 1 4 2l9-23 1-1z"></path><path d="M494 652l19 51 4 14c-1 0-1 0-2 1l-22-65 1-1z" class="a"></path><path d="M523 723l2 5v3 3c0 3 0 7 1 10v1c1 8 5 16 8 24 0 2 2 11 3 13h1l-1 1c-1 0-1 0-1-1-1 0-1 0-2-1l-16-54 1-2 2 6h1v1-1-1c0-2 1-5 1-7z" class="t"></path><path d="M523 723l2 5v3 3c0 3 0 7 1 10v1c-2-4-4-9-5-14h1v1-1-1c0-2 1-5 1-7z" class="N"></path><defs><linearGradient id="Ds" x1="511.568" y1="719.347" x2="524.825" y2="708.417" xlink:href="#B"><stop offset="0" stop-color="#807d7f"></stop><stop offset="1" stop-color="#a1a0a1"></stop></linearGradient></defs><path fill="url(#Ds)" d="M510 686c1 1 1 2 2 3 1 2 3 4 2 6v1c0 1 0 1 1 1l1-2 1 1v4h0v1l1 1v3h1l3-3v6c0 5 0 10 1 15 0 2-1 5-1 7v1 1-1h-1l-2-6c0-1-1-4-1-5l-1-3-4-14c0-2 1-5 0-7l-1-4v-2c-1-1-2-2-2-4z"></path><path d="M518 705h1l3-3v6h0l-1-2v-1l-2 1h-1v-1z" class="B"></path><path d="M526 744l1 2h1c1-1 0-1 1 0 2 5 5 10 8 15l1-1 2 6c1 1 1 2 2 3l2-5c0 1 1 1 1 1l-4 13-2-1-1 5h-1c-1-2-3-11-3-13-3-8-7-16-8-24v-1z" class="Y"></path><defs><linearGradient id="Dt" x1="542.35" y1="776.017" x2="536.65" y2="766.983" xlink:href="#B"><stop offset="0" stop-color="#1a1311"></stop><stop offset="1" stop-color="#362424"></stop></linearGradient></defs><path fill="url(#Dt)" d="M538 760l2 6c1 1 1 2 2 3l2-5c0 1 1 1 1 1l-4 13-2-1c1-5 0-11-2-16l1-1z"></path><path d="M622 563l1-1h1l-17 44-2-2v-1l-2 2h-1l5-6-1-1h-6l-8 5c-2 0-3 0-4 1l-2-2c4-1 8-3 12-5l5-4 1-4h0c3-1 5-3 7-6 1-2 2-5 4-7l-1-1c0-1 1-2 2-3 2-2 4-5 5-8 0-1 0-1 1-1z" class="m"></path><path d="M600 598c5-2 8-5 11-8-1 3-2 7-4 9l-1-1h-6z" class="e"></path><path d="M614 575c0-1 1-2 2-3 2-2 4-5 5-8 0-1 0-1 1-1-4 11-8 23-19 30l1-4h0c3-1 5-3 7-6 1-2 2-5 4-7l-1-1z" class="B"></path><path d="M640 501c1 0 2 0 3 2v5l-1 1-9 23c-1-1-3-1-4-2h0c-1-2-1-3-2-4v-5h0c2-4 5-7 5-11-1 2-4 5-6 6v-1c1-3 4-5 6-7l6-6 2-1z" class="J"></path><path d="M640 501c1 0 2 0 3 2v5l-1 1v-5h-3c-2 2-2 4-3 6l-3 8c-1 3-1 7-2 10-1 0-1 1-2 1v1h0c-1-2-1-3-2-4v-5h0c2-4 5-7 5-11-1 2-4 5-6 6v-1c1-3 4-5 6-7l6-6 2-1z" class="N"></path><path d="M630 518h2l-3 11v1h0c-1-2-1-3-2-4 1-2 1-4 2-5s1-2 1-3z" class="C"></path><path d="M632 510l1-1h0c0 2 1 4 0 6l-1 3h0-2c0 1 0 2-1 3s-1 3-2 5v-5h0c2-4 5-7 5-11z" class="F"></path><path d="M630 518v-1c1-1 1-2 3-2l-1 3h0-2z" class="B"></path><path d="M661 447c1 1 3 0 4 0l2-1h2c-2 9-6 18-10 27-2 4-3 8-5 12h-1l1-1c0-1 0-2 1-3h0v-1c1-1 1-1 1-2l-3 1v-1c-2 0-2 0-3-1l1-1-3-4h0c-1-2-2-4-4-5l-1-1h0c-1-2-1-4-1-6l3-3c1 0 2 0 2 1h3c1 1 1 1 2 0l3 1c2 1 4 0 6-1l3-2 1-3v-1-1l-3-1h-1l-2-1v-1c1 0 1 0 2-1z" class="m"></path><path d="M664 456l-3 7c0-1-2-1-3-1 2-1 3-2 3-4l3-2z" class="O"></path><path d="M658 470c-1 4-3 5-5 8-2 0-2 0-3-1l1-1s1-1 1-2v-1l1-1h1c1 0 3-1 4-2z" class="N"></path><path d="M655 459c2 1 4 0 6-1 0 2-1 3-3 4 1 0 3 0 3 1s-1 5-2 6l-1 1c-1 1-3 2-4 2h-1v-1l1-1v-4c-1-1-2-1-3-2v-3c1-1 2-1 4-2z" class="D"></path><path d="M655 459c2 1 4 0 6-1 0 2-1 3-3 4h0l-1-1c-2 0-3 0-5 2l-1-2c1-1 2-1 4-2z" class="W"></path><path d="M658 462h0c1 0 3 0 3 1s-1 5-2 6h-1-2c1-1 2-3 2-4l-1-1-1 1-1-1 3-2z" class="f"></path><path d="M645 457c1 0 2 0 2 1h3c1 1 1 1 2 0l3 1c-2 1-3 1-4 2v3c1 1 2 1 3 2v4l-1 1v1l-1 1v1c0 1-1 2-1 2l-3-4h0c-1-2-2-4-4-5l-1-1h0c-1-2-1-4-1-6l3-3z" class="D"></path><path d="M649 464c-1 0-1 0-2-1-1 0-1-1-1-2-1-1 0-1 1-2h3l1 1-1 1c-1 0-1 0-2-1 0 2 1 3 1 4zm0 2l4 5v1l-1 1v1c0 1-1 2-1 2l-3-4h0l1-1c0-1 0-2-1-3v-1l1-1z" class="C"></path><path d="M650 458c1 1 1 1 2 0l3 1c-2 1-3 1-4 2v3c1 1 2 1 3 2v4l-1 1-4-5v-2c0-1-1-2-1-4 1 1 1 1 2 1l1-1-1-1v-1z" class="E"></path><path d="M638 497c-1-3 3-7 4-11 1-3 1-7 1-11h-1l-2-11c0-2 1-2 2-4 0 2 0 4 1 6h0l1 1c2 1 3 3 4 5h0l3 4-1 1c1 1 1 1 3 1v1l3-1c0 1 0 1-1 2v1h0c-1 1-1 2-1 3l-1 1h1l-7 18c-2 1-2 2-3 4l-1 1v-5c-1-2-2-2-3-2l-2 1-1-1h1l-1-1v-2l1-1z" class="G"></path><path d="M644 467c2 1 3 3 4 5h0c-1 1-1 2-1 4v1 4c0-5-2-9-3-14z" class="X"></path><path d="M648 472l3 4-1 1v4c-2 5-3 10-6 14-1 2-4 4-4 6l-2 1-1-1h1l-1-1v-2l1-1c5-4 7-10 9-16v-4-1c0-2 0-3 1-4z" class="t"></path><path d="M650 477c1 1 1 1 3 1v1l3-1c0 1 0 1-1 2v1h0c-1 1-1 2-1 3l-1 1h1l-7 18c-2 1-2 2-3 4l-1 1v-5c-1-2-2-2-3-2 0-2 3-4 4-6 3-4 4-9 6-14v-4z" class="r"></path><path d="M650 477c1 1 1 1 3 1v1c-1 2-1 5-2 7 0-2 0-4-1-5v-4z" class="s"></path><path d="M632 510c0 4-3 7-5 11h0v5c1 1 1 2 2 4l-2 2c1 1 3 2 4 3h1c-1 5-3 9-4 14v1l-3 10-1 2h-1l-1 1c-1 0-1 0-1 1-1 3-3 6-5 8-1 1-2 2-2 3l1 1c-2 2-3 5-4 7-2 3-4 5-7 6h0l-1 4-5 4c-4 2-8 4-12 5l2 2c-4 1-8 1-12 2h-5l-8 1h-1l-1 3c-2-1-5-1-7-2 1 0 1 0 2-2l-2-1c-2 0-4-1-6-2-6-1-11-4-16-6h-3l-3-3c-1-2-3-3-5-5 2 0 3 0 4-1h6l-2-2-1-1c-1 0-1-1-1-2 1-1 1-2 2-3 3-3 5-4 9-4 1 0 1-1 2-1 1-1 2-1 2-2 3-2 6-5 9-5 1-1 3-2 4-1l2 2v2l1 3v-3c0-1 0-1 1-2v4c0 4 0 9 2 12l1 2v3l-1 1c1 1 2 1 3 2 1 0 2 1 3 1l3-1c1 1 1 1 2 1 1 1 6 1 8 0 1-1 1-1 1-2l1-3v1l1-2c0 2 0 2 1 3l3-1v-3h1v1l1 1c2-2 8-4 10-7h0c3-1 6-4 7-7l3-4 1-1c1-2 1-4 1-6h1c1-2 0-5 0-8 1-3 1-5 1-8 0 0-1 0-1-1 0-2 1-3 2-5l2-2 1-1c0-1 0-1-1-2l1-1c5-6 6-13 9-20 2-1 5-4 6-6z" class="I"></path><path d="M631 535h1c-1 5-3 9-4 14l-3 1 1-2c1-5 3-8 5-13h0z" class="U"></path><path d="M557 571l1 3c-2 2-4 5-3 8 0 2 1 4 2 5-1 0-2 0-2-1h-1c0-2-1-3-1-4v-2-2c1-3 2-4 4-7zm24 21l1-3v1l1-2c0 2 0 2 1 3l3-1-1 3c-5 3-12 3-18 1h-1l3-1c1 1 1 1 2 1 1 1 6 1 8 0 1-1 1-1 1-2z" class="B"></path><path d="M609 571v3c-2 4-9 10-12 13-3 2-7 5-11 6l1-3v-3h1v1l1 1c2-2 8-4 10-7h0c3-1 6-4 7-7l3-4z" class="F"></path><path d="M531 584c1-2 2-3 3-4s2-1 3-1c2 1 3 2 4 3v4h-1c-1 0-2 1-3 2h-1s0-1-1-1l-1 1h-1 0c-2-2-2-2-2-4z" class="c"></path><path d="M531 584c1-2 2-3 3-4s2-1 3-1c-1 1-2 1-2 2-1 1-1 2-2 3v4c-2-2-2-2-2-4z" class="T"></path><path d="M603 587c0-2 0-2 1-3l1-1c1-2 0 0 1-1s0-1 1-2h-1l1-1c1-3 3-5 5-8 1-2 3-4 4-6v-1c1-3 3-4 6-6-2 4-4 9-7 12 0 1-1 2-2 3l-1 3c-1 2-3 3-4 6l-5 5z" class="Q"></path><path d="M625 550l3-1v1l-3 10-1 2h-1l-1 1c-1 0-1 0-1 1-1 3-3 6-5 8-1 1-2 2-2 3-2 2-4 5-6 7 1-3 3-4 4-6l1-3c1-1 2-2 2-3 3-3 5-8 7-12l3-8z" class="K"></path><path d="M559 569v4c0 4 0 9 2 12l1 2v3l-1 1c-2-1-3-2-4-4-1-1-2-3-2-5-1-3 1-6 3-8v-3c0-1 0-1 1-2z" class="E"></path><path d="M542 573c3-2 6-5 9-5l-10 10h-2c-3 0-4 0-6 1-1 1-1 1-1 2l-1 1c0 1-1 1 0 2 0 2 0 2 2 4h0-2l-2-2-1-1c-1 0-1-1-1-2 1-1 1-2 2-3 3-3 5-4 9-4 1 0 1-1 2-1 1-1 2-1 2-2z" class="C"></path><path d="M537 588c1-1 2-2 3-2h1v2h0c-2 4-5 7-9 9h-3l-3-3c-1-2-3-3-5-5 2 0 3 0 4-1h6 2 1l1-1c1 0 1 1 1 1h1z" class="T"></path><path d="M533 588h1l1-1c1 0 1 1 1 1h1c0 2-1 2-2 3-1 0-1 0-2-1-2-1-5-1-7-2h-1 6 2z" class="H"></path><path d="M525 588h1c2 2 5 2 7 4l-1 1c-2 1-4 1-6 1-1-2-3-3-5-5 2 0 3 0 4-1z" class="c"></path><path d="M541 588h0c-2 4-5 7-9 9h-3l-3-3c2 0 4 0 6-1 4-1 6-2 9-5z" class="Q"></path><defs><linearGradient id="Du" x1="611.122" y1="533.127" x2="627.512" y2="551.882" xlink:href="#B"><stop offset="0" stop-color="#8d8b8b"></stop><stop offset="1" stop-color="#b7b7b7"></stop></linearGradient></defs><path fill="url(#Du)" d="M632 510c0 4-3 7-5 11h0v5c1 1 1 2 2 4l-2 2h-1 0c-1 1-2 2-2 3v1c-1 1-1 2-2 3-1 2-1 4-2 6 0 2 0 4-1 6-1 3-2 8-3 11-1 1-2 3-3 4h-1v-2c1-2 0-5 0-8 1-3 1-5 1-8 0 0-1 0-1-1 0-2 1-3 2-5l2-2 1-1c0-1 0-1-1-2l1-1c5-6 6-13 9-20 2-1 5-4 6-6z"></path><path d="M617 539h3v-1 1c-1 3-4 6-7 9 0 0-1 0-1-1 0-2 1-3 2-5l2-2 1-1z" class="i"></path><defs><linearGradient id="Dv" x1="599.503" y1="585.774" x2="547.996" y2="585.742" xlink:href="#B"><stop offset="0" stop-color="#c8c6c6"></stop><stop offset="1" stop-color="#f8f7f7"></stop></linearGradient></defs><path fill="url(#Dv)" d="M550 570l3-3c2 1 2 1 3 2v2c-3 2-5 6-5 9 0 4 3 8 6 11s7 5 11 7h1c7 2 15 0 22-3l5-3 7-5h0l5-5c2-2 4-5 6-7l1 1c-2 2-3 5-4 7-2 3-4 5-7 6h0l-1 4-5 4c-4 2-8 4-12 5l2 2c-4 1-8 1-12 2h-5l-8 1h-1l-1 3c-2-1-5-1-7-2 1 0 1 0 2-2l-2-1c-2 0-4-1-6-2-6-1-11-4-16-6 4-2 7-5 9-9 1-2 2-4 2-6 2-5 4-9 7-12z"></path><path d="M598 597v-3c2-1 4-4 6-5l-1 4-5 4z" class="G"></path><path d="M573 605c5-1 9-2 13-3l2 2c-4 1-8 1-12 2h-5l2-1z" class="b"></path><defs><linearGradient id="Dw" x1="565.452" y1="601.576" x2="558.235" y2="604.839" xlink:href="#B"><stop offset="0" stop-color="#575456"></stop><stop offset="1" stop-color="#706a69"></stop></linearGradient></defs><path fill="url(#Dw)" d="M554 605c-1-1-3-2-3-4h0l-1-1v-3-1-2-3l2 4c4 5 9 8 15 9l6 1-2 1-8 1h-1l-1 3c-2-1-5-1-7-2 1 0 1 0 2-2l-2-1z"></path><path d="M554 605c-1-1-3-2-3-4h0l-1-1v-3-1-2-3l2 4c0 2-1 5 1 7l3 3h1c1 1 3 2 5 2l-1 3c-2-1-5-1-7-2 1 0 1 0 2-2l-2-1z" class="D"></path><path d="M541 588c1-2 2-4 2-6 2-5 4-9 7-12v2c-1 1-1 2-2 3v2h0c-1 6 1 9 2 14v3 2 1 3l1 1h0c0 2 2 3 3 4-2 0-4-1-6-2-6-1-11-4-16-6 4-2 7-5 9-9z" class="L"></path><path d="M548 603v-2c-1-1 0-3 0-5h-1c-1-3 1-7 0-10l-1-1c0-2 0-6 2-8-1 6 1 9 2 14v3 2 1 3l1 1h0c0 2 2 3 3 4-2 0-4-1-6-2z" class="G"></path><path d="M418 364c1-2 3-4 3-7 1 0 1-1 1-2h3v15 16l-1 31c0 3-1 8 1 11h0c3 0 5 1 6 3 1 1 1 2 1 3l-1 3c-1 0-2 1-3 2l-1-1c-1-1-2-2-2-3v-1h0l-2 1c0 7 0 14 2 21 1 3 3 5 4 8 3 6 5 14 6 21 0 4 1 6 0 9 2 3 8 21 9 22l6 16c1 3 2 6 4 10 0 1 1 3 2 5l6 17 32 88-1 1-109-289 1-1c2 4 3 8 8 10h1 2c3 0 5 0 8-2-1 0-1 0-1-1l3-3 1 1c1-1 1-1 1-2 1 0 2-1 2-1 2-1 4-2 7-3 0 1 0 2 1 2z" class="b"></path><path d="M427 430c2 1 2 1 3 3v2c-1 0-2 0-2 1-1 0-1 0-1-1v-3-1-1z" class="S"></path><path d="M427 431h0c-6 2-9 5-12 10l-2-7 4-2c3-1 7-3 10-2v1z" class="T"></path><path d="M428 464h1c3 6 5 14 6 21 0 4 1 6 0 9l-10-27c1-2 2-1 3-3z" class="J"></path><path d="M423 435c0 7 0 14 2 21 1 3 3 5 4 8h-1c-3-1-5-4-6-7-2-4-5-10-5-14s3-6 6-8z" class="F"></path><path d="M408 366c1 0 2-1 2-1 2-1 4-2 7-3 0 3 1 12-1 14v1l-1 1-4-4c-4 0-9 5-12 7-2 2-3 4-5 6-1-5-3-10-5-14 3 1 5 3 8 3v-1l-2-1-1-1h2c3 0 5 0 8-2-1 0-1 0-1-1l3-3 1 1c1-1 1-1 1-2z" class="H"></path><path d="M404 371c3 0 5-1 8 1 2 1 3 2 4 4v1l-1 1-4-4h-1c-4-1-9 3-13 2v-1l-2-1-1-1h2c3 0 5 0 8-2z" class="K"></path><path d="M408 366c1 0 2-1 2-1 2-1 4-2 7-3 0 3 1 12-1 14-1-2-2-3-4-4-3-2-5-1-8-1-1 0-1 0-1-1l3-3 1 1c1-1 1-1 1-2z" class="L"></path><defs><linearGradient id="Dx" x1="399.393" y1="385.736" x2="417.547" y2="395.079" xlink:href="#B"><stop offset="0" stop-color="#8c8b8c"></stop><stop offset="1" stop-color="#aaa9a9"></stop></linearGradient></defs><path fill="url(#Dx)" d="M404 410l-8-19c2-5 8-13 14-14 1-1 6 4 7 5h1l2 3c1 2 1 3-1 6 0 1-2 3-3 4-1 3 0 9-1 13l-1-4v-9c1-1 1-2-1-3 0-1-1-2-2-2-2 0-3 0-4 2-1 1-1 2-2 4-1 1-1 3-2 5 0 3 1 6 1 9z"></path><path d="M404 410c0-3-1-6-1-9 1-2 1-4 2-5 1-2 1-3 2-4 1-2 2-2 4-2 1 0 2 1 2 2 2 1 2 2 1 3v9l1 4c1 6-1 16 2 22-1 1-3 1-5 2l-1-3-7-19z" class="P"></path><path d="M414 404l1 4c1 6-1 16 2 22-1 1-3 1-5 2l-1-3h2c1-4 1-7 1-10v-15z" class="C"></path><path d="M418 364c1-2 3-4 3-7 1 0 1-1 1-2h3v15 16l-1 31c0 3-1 8 1 11-3 0-5 1-8 2-3-6-1-16-2-22 1-4 0-10 1-13 1-1 3-3 3-4 2-3 2-4 1-6l-2-3v-3l-2-2v-1c2-2 1-11 1-14 0 1 0 2 1 2z" class="H"></path><path d="M418 377c2 3 2 5 2 8l-2-3v-3-2z" class="n"></path><path d="M417 362c0 1 0 2 1 2 1 4 1 9 0 13v2l-2-2v-1c2-2 1-11 1-14z" class="S"></path><defs><linearGradient id="Dy" x1="363.303" y1="285.29" x2="409.967" y2="281.942" xlink:href="#B"><stop offset="0" stop-color="#2e0e0c"></stop><stop offset="1" stop-color="#403130"></stop></linearGradient></defs><path fill="url(#Dy)" d="M406 236c-1 6 2 14 3 20 1 8 1 16 1 24-1 1-1 1-1 3 0 1 0 2 1 3-7 6-13 13-20 21-6 6-11 13-16 20v1c-1 0-1 0-1 1h-1c-7-19-13-42-6-61 2-6 7-11 13-14l1 1 1-1v1c0 1 1 1 1 1l1-1c0-1 2-2 3-3h0l2-1 4-1-1-2c6-4 11-7 15-12z"></path><path d="M403 278c1 2 3 4 2 7h-1c-1-1-3-3-3-4l1-1c1 0 1-1 1-2z" class="M"></path><path d="M398 287c1 2 1 2 1 4-1 3-5 7-7 10-1 1-3 3-4 5v-1h0l-1-1h0c-1 3-2 4-4 5 3-5 6-11 9-15 2-3 4-5 6-7z" class="O"></path><path d="M392 250l11 28c0 1 0 2-1 2l-1 1c-5-7-9-15-12-23l-1-7 4-1z" class="F"></path><path d="M386 252l2-1 1 7c0 2 1 3 1 5 0 1 0 0 1 1v3l4 15 1 2h-2c-6-1-9-8-13-12h-1l4-8c-1 0-1-1-1-1 0-1 0-2-1-3h0v-4l1-1c0-1 2-2 3-3h0z" class="q"></path><path d="M386 252c1 2 1 2 1 4l-2 6-1 2c-1 0-1-1-1-1 0-1 0-2-1-3h0v-4l1-1c0-1 2-2 3-3z" class="Q"></path><path d="M383 255l1 1c0 2-1 3 1 6l-1 2c-1 0-1-1-1-1 0-1 0-2-1-3h0v-4l1-1z" class="g"></path><defs><linearGradient id="Dz" x1="394.383" y1="289.636" x2="371.954" y2="275.878" xlink:href="#B"><stop offset="0" stop-color="#797879"></stop><stop offset="1" stop-color="#a1a0a2"></stop></linearGradient></defs><path fill="url(#Dz)" d="M382 260h0c1 1 1 2 1 3 0 0 0 1 1 1l-4 8h1c4 4 7 11 13 12h2c1 1 1 2 2 3-2 2-4 4-6 7-3 4-6 10-9 15l-7 8c4-8 9-13 9-22 1-7-1-14-6-19-3 2-6 6-8 9l11-25z"></path><path d="M603 605l2-2v1l2 2-14 34c-1 4-3 8-4 12-1 2-1 4-2 5l-11 27c-2 5-4 12-7 16-2 7-5 13-7 20-1 2-2 5-3 7l-14 38s-1 0-1-1l-2 5c-1-1-1-2-2-3l-2-6-1 1c-3-5-6-10-8-15-1-1 0-1-1 0h-1l-1-2c-1-3-1-7-1-10v-3-3l-2-5c-1-5-1-10-1-15v-6l1-7v-5c1-7 2-10 6-15l1-2c1-2 3-4 4-6l5-12 1-4 1 2 4-19 1-2v-2c0-2 1-3 3-4l1 1-1 4h1l1-5c0-3-1-5-2-6 2-2 6-1 9 0v-1h-1c3 0 5 0 7-1h2l11-1 1-1c8 1 17-6 24-11h1z" class="w"></path><path d="M571 652c0-1 0-3 1-4s1-1 1-3v-1c1 0 1 0 1-1h0 1c-1 1-1 2-1 4 0-1 0-1 1-1v1c-2 3-3 6-4 10h-1v-4l1-1z" class="h"></path><path d="M546 678l1-1h0c1-3 0 0 1-2v-1l1 1-6 18c-1-2-1-3-1-4h0c1-4 2-8 4-11z" class="P"></path><path d="M544 701l-1-3c1 0 1 0 1 1 3 2 4 3 8 3h1c1 1 1 1 1 3h-1-3c-1-1-3-1-4-3l-2-1z" class="C"></path><path d="M540 738c0 1 1 1 1 1 1 1 1 3 2 4 1 3 2 7 3 10 0 2 0 5-1 7 0-5-2-9-4-14 1-2-1-6-1-7v-1z" class="h"></path><path d="M558 666c4-7 6-14 12-20v1c-1 1-2 2-2 3s-1 2-1 3c-2 2-3 5-3 8-1 1-2 3-2 4h-1l-3 1z" class="u"></path><path d="M563 684c5-8 10-13 18-17-7 8-15 12-15 24l-1 13c-1-1-1-2-2-3 1-1 1-4 1-5h0v-7-2c0-1 1-2 0-3h-1z" class="I"></path><path d="M563 684h1c1 1 0 2 0 3v2 2c-2 5-6 11-10 14 0-2 0-2-1-3h-1 0c4-2 5-7 7-11l4-7z" class="B"></path><path d="M543 705c1 1 2 0 2 2-1 1-1 4-1 6-1 4-1 9 0 12l2 7 2-4 4-7h1v1c-2 2-5 6-6 9v6c0 2 0 2 2 3 1 0 1-1 2-1l-2 2-1-1c-1 2 1 3 1 5-1-2-2-3-3-5 0-2-1-4-2-6h0l-3-6c0-3 1-6 1-9s0-7 1-10v-4z" class="r"></path><path d="M564 689v7h0c0 1 0 4-1 5 0 1-3 6-3 7 0 0-1 2-2 2h-4-3l-4-2-2-1c0-2-1-1-2-2l1-3v-1l2 1c1 2 3 2 4 3h3 1c4-3 8-9 10-14v-2z" class="a"></path><path d="M544 701l2 1c1 2 3 2 4 3h3l1 1h3l-1 1c-2 1-6 1-8 0s-3-3-4-5v-1z" class="I"></path><path d="M554 705c4-3 8-9 10-14-1 6-2 11-6 15h-1-3l-1-1h1z" class="i"></path><defs><linearGradient id="EA" x1="561.978" y1="659.977" x2="570.022" y2="671.023" xlink:href="#B"><stop offset="0" stop-color="#5b5858"></stop><stop offset="1" stop-color="#817e80"></stop></linearGradient></defs><path fill="url(#EA)" d="M567 653v1h1c1-1 2-2 3-2l-1 1v4h1c1 3 1 8-1 10l1 1-1 1c-2 2-5 7-7 9-1 0-1 0-1-1 1-2 1-4 1-7v-1h0l-1-1c-1-1 0-1-2-1l1-2h1c0-1 1-3 2-4 0-3 1-6 3-8z"></path><path d="M570 667l-1 1c0-2 0-9 1-11h1c1 3 1 8-1 10zm-3-14v1h1c1-1 2-2 3-2l-1 1-3 3c0 1-1 2-1 2-2 3-2 8-3 11h0l-1-1c-1-1 0-1-2-1l1-2h1c0-1 1-3 2-4 0-3 1-6 3-8z" class="Y"></path><path d="M558 666l3-1-1 2c2 0 1 0 2 1l1 1h0v1c0 3 0 5-1 7 0 1 0 1 1 1l-10 16-1-1c-1-4 1-9 2-13v-1c0-5 2-9 4-13z" class="l"></path><path d="M560 667c2 0 1 0 2 1l1 1h0v1c-1 3-2 4-4 6l2-7c0-1 0 0-1-1v-1z" class="e"></path><path d="M558 666l3-1-1 2v1c-2 4-3 8-6 11 0-5 2-9 4-13z" class="s"></path><path d="M545 707l2 1 4 2h3 4c1 0 2-2 2-2 1 1 0 2-1 3l-2 3c-1 2-3 4-4 7h-1l-4 7-2 4-2-7c-1-3-1-8 0-12 0-2 0-5 1-6z" class="Z"></path><path d="M547 712l2 2-1 1 1 1s-1 0-1 1v4l1 1 2-1h1l-4 7c-1 0-2-2-2-2 0-2 1-4 1-5v-9z" class="Q"></path><path d="M560 708c1 1 0 2-1 3l-2 3c-1 2-3 4-4 7h-1-1l-2 1-1-1v-4c0-1 1-1 1-1l-1-1 1-1-2-2v-4l4 2h3 4c1 0 2-2 2-2z" class="J"></path><path d="M560 708c1 1 0 2-1 3l-2 3c0-1-1-1-1-1-1 0-1 1-2 1h-3 0c-1 0-2-1-2-1 1-1 1-1 1-2l-1-1h1 1 3 4c1 0 2-2 2-2z" class="W"></path><defs><linearGradient id="EB" x1="562.863" y1="724.965" x2="550.462" y2="726.096" xlink:href="#B"><stop offset="0" stop-color="#362e2e"></stop><stop offset="1" stop-color="#564847"></stop></linearGradient></defs><path fill="url(#EB)" d="M565 704l1-13v10 4l1-1v-1l1-1c0-1 0-1 1-2h0c-2 7-5 13-7 20-1 2-2 5-3 7l-1-1c-2 3-3 7-4 11-1 1-1 2-2 3 0-1 0-1 1-2l-2 1c-1 0-1 1-2 1-2-1-2-1-2-3v-6c1-3 4-7 6-9v-1c1-3 3-5 4-7l2-3c1-1 2-2 1-3 0-1 3-6 3-7 1 1 1 2 2 3z"></path><path d="M553 722c1-1 1-2 2-3 0-1 1-1 2-2l1 1-5 7c-2 2-3 4-2 7 0 3-2 3-2 6-1 0-1-1-2-1v-6c1-3 4-7 6-9z" class="U"></path><path d="M563 701c1 1 1 2 2 3s0 3-1 4v2l-1 1c-1 2-3 4-5 7l-1-1c-1 1-2 1-2 2-1 1-1 2-2 3v-1c1-3 3-5 4-7l2-3c1-1 2-2 1-3 0-1 3-6 3-7z" class="e"></path><path d="M603 605l2-2v1c-13 16-30 27-42 44v-1c1-4 2-7 5-10 2-2 4-4 4-6l3-3v-1s-1-1-1-2l1-1-1-1 2-1c1-1 2-1 2-2h-1v-1h-3c1-1 3-1 3-2l1-1c8 1 17-6 24-11h1z" class="R"></path><path d="M575 624l1-1c2-2 3-2 6-2l-7 7v-1s-1-1-1-2l1-1z" class="O"></path><path d="M578 616c8 1 17-6 24-11h1c-6 6-14 10-21 16-3 0-4 0-6 2l-1 1-1-1 2-1c1-1 2-1 2-2h-1v-1h-3c1-1 3-1 3-2l1-1z" class="V"></path><path d="M579 639l4-4h2l2-2c1 1 1 0 1 1l2 2c1 2 0 3 0 5h0c1 0 1 0 1-1v-1h1v2l1-1h0c-1 4-3 8-4 12-1 2-1 4-2 5-2 1-4 2-5 3-3 3-8 9-12 9l1-1-1-1c2-2 2-7 1-10 1-4 2-7 4-10v-1l1-3 3-4z" class="r"></path><path d="M590 641c1 0 1 0 1-1v-1h1v2l1-1h0c-1 4-3 8-4 12-1 2-1 4-2 5-2 1-4 2-5 3 2-4 4-7 6-11 1-2 1-2 1-4h1v-4z" class="h"></path><path d="M582 640l-1 10h1c1-3 1-11 4-13 1 0 1 1 2 2 0 4-2 8-3 12 0 2-3 6-5 7v-1c0-1 0-3 1-5l-1-1 1-10 1-1z" class="V"></path><defs><linearGradient id="EC" x1="576.025" y1="646.598" x2="572.365" y2="666.162" xlink:href="#B"><stop offset="0" stop-color="#5c5758"></stop><stop offset="1" stop-color="#777677"></stop></linearGradient></defs><path fill="url(#EC)" d="M579 639h1c1 0 2 0 3-1v1l-1 1-1 1-1 10 1 1c-1 2-1 4-1 5v1l-9 10-1-1c2-2 2-7 1-10 1-4 2-7 4-10v-1l1-3 3-4z"></path><path d="M580 651l1 1c-1 2-1 4-1 5-2 1-4 5-6 6 0-2 3-3 3-5v-3h2l1-4z" class="Z"></path><defs><linearGradient id="ED" x1="582.275" y1="640.269" x2="576.8" y2="647.927" xlink:href="#B"><stop offset="0" stop-color="#433737"></stop><stop offset="1" stop-color="#514d4e"></stop></linearGradient></defs><path fill="url(#ED)" d="M579 639h1c1 0 2 0 3-1v1l-1 1-1 1-1 10-1 4h-2c1-2 1-5 2-8h-4v-1l1-3 3-4z"></path><path d="M576 643l2 2 1 2h-4v-1l1-3z" class="O"></path><path d="M564 638c2-2 6-6 8-7 0 2-2 4-4 6-3 3-4 6-5 10v1l-8 15c-2 4-4 9-6 12l-1-1v1c-1 2 0-1-1 2h0l-1 1c-2 3-3 7-4 11h0c0 1 0 2 1 4-2 8-4 16-5 25l1 15-1 1c-1-1-1 0-1-1l-1 1h0c-1-4-1-8-1-12-1-9-1-19 1-28h1c0-2 1-5 1-7l4-15 2-3 3-7 4-9 4-6c2 0 3 0 5-2 0-1 1-1 2-2h0 1c1-2 1-3 1-5z" class="T"></path><path d="M542 672l2-3c0 5-2 14-6 18l4-15z" class="n"></path><path d="M536 694h1l-1 10c-1 5-1 10 0 15 0 2 0 4 1 6v1 1 3h1c0-4-1-9 0-12l1 15-1 1c-1-1-1 0-1-1l-1 1h0c-1-4-1-8-1-12-1-9-1-19 1-28zm28-56c2-2 6-6 8-7 0 2-2 4-4 6-3 3-4 6-5 10v1l-8 15c-2 4-4 9-6 12l-1-1v1c-1 2 0-1-1 2h0l-1 1c0-1 1-2 1-3l6-14c2-6 6-12 9-18h1c1-2 1-3 1-5z" class="B"></path><path d="M566 618l11-1c0 1-2 1-3 2h3v1h1c0 1-1 1-2 2l-2 1 1 1-1 1c0 1 1 2 1 2v1l-3 3c-2 1-6 5-8 7 0 2 0 3-1 5h-1 0c-1 1-2 1-2 2-2 2-3 2-5 2l-4 6v-1c0-1 1-2 1-2v-1l-1 1h0-1 0c-1 2-2 3-3 4 1-3 2-6 3-10h0v-6-7l1-5c0-3-1-5-2-6 2-2 6-1 9 0v-1h-1c3 0 5 0 7-1h2z" class="g"></path><defs><linearGradient id="EE" x1="573.674" y1="626.216" x2="562.128" y2="635.54" xlink:href="#B"><stop offset="0" stop-color="#555152"></stop><stop offset="1" stop-color="#767475"></stop></linearGradient></defs><path fill="url(#EE)" d="M574 623l1 1-1 1c0 1 1 2 1 2v1l-3 3c-2 1-6 5-8 7 0 2 0 3-1 5h-1 0c-1 1-2 1-2 2-2 2-3 2-5 2v-1c1-3 4-6 6-9 4-5 7-9 12-13h0l1-1z"></path><path d="M564 638c0 2 0 3-1 5h-1 0c-1 1-2 1-2 2-2 2-3 2-5 2 1-1 4-5 6-6 0 0 1 0 1-1 1 0 2-1 2-2z" class="G"></path><defs><linearGradient id="EF" x1="564.349" y1="624.045" x2="548.147" y2="629.452" xlink:href="#B"><stop offset="0" stop-color="#4e4344"></stop><stop offset="1" stop-color="#675a5a"></stop></linearGradient></defs><path fill="url(#EF)" d="M566 618l11-1c0 1-2 1-3 2h3v1h1c0 1-1 1-2 2l-2 1-1 1h0c-3-1-7-1-10 0h0c-3 4-6 7-9 11-2 3-2 6-4 9v-6-7l1-5c0-3-1-5-2-6 2-2 6-1 9 0v-1h-1c3 0 5 0 7-1h2z"></path><path d="M566 618l11-1c0 1-2 1-3 2-1 0-3 1-4 1h-5c-3 1-5 0-7 0v-1h-1c3 0 5 0 7-1h2z" class="q"></path><path d="M574 619h3v1h1c0 1-1 1-2 2l-2 1-1 1h0c-3-1-7-1-10 0 2-3 5-3 7-4 1 0 3-1 4-1z" class="e"></path><defs><linearGradient id="EG" x1="562.441" y1="695.329" x2="520.146" y2="664.369" xlink:href="#B"><stop offset="0" stop-color="#c4bec2"></stop><stop offset="1" stop-color="#dadcda"></stop></linearGradient></defs><path fill="url(#EG)" d="M545 634l1-2v-2c0-2 1-3 3-4l1 1-1 4h1v7 6h0c-1 4-2 7-3 10 1-1 2-2 3-4h0 1 0l1-1v1s-1 1-1 2v1l-4 9-3 7-2 3-4 15c0 2-1 5-1 7h-1c-2 9-2 19-1 28 0 4 0 8 1 12h-1l-1-2c-1-2-1-4-2-6 0-2-1-4-2-6-1-7 0-15 0-22l2-10 9-35 4-19z"></path><path d="M550 631v7 6h0l-3 3h0l2-16h1z" class="O"></path><path d="M532 688c2 5 0 10 0 16 0 7 0 17 2 24v4c-1-2-1-4-2-6 0-2-1-4-2-6-1-7 0-15 0-22l2-10z" class="G"></path><path d="M547 647h0l3-3c-1 4-2 7-3 10 1-1 2-2 3-4h0 1 0l1-1v1s-1 1-1 2v1l-4 9-3 7-2 3-4 15c0 2-1 5-1 7h-1l11-47z" class="e"></path><path d="M544 661l2-2 1 3-3 7-2 3 2-11z" class="G"></path><path d="M547 654c1-1 2-2 3-4h0 1 0l1-1v1s-1 1-1 2v1l-4 9-1-3-2 2 3-7z" class="C"></path><path d="M540 651l1 2-9 35-2 10c0 7-1 15 0 22 1 2 2 4 2 6 1 2 1 4 2 6l1 2h1 0l1-1c0 1 0 0 1 1l1-1 1 5v1c0 1 2 5 1 7 2 5 4 9 4 14l-1 4-2 5c-1-1-1-2-2-3l-2-6-1 1c-3-5-6-10-8-15-1-1 0-1-1 0h-1l-1-2c-1-3-1-7-1-10v-3-3l-2-5c-1-5-1-10-1-15v-6l1-7v-5c1-7 2-10 6-15l1-2c1-2 3-4 4-6l5-12 1-4z" class="r"></path><path d="M530 720c1 2 2 4 2 6v7c-1-4-1-9-2-13z" class="B"></path><path d="M525 734c2 4 3 8 4 12-1-1 0-1-1 0h-1l-1-2c-1-3-1-7-1-10z" class="O"></path><path d="M525 728c0-4-1-8-1-11 0-6 1-11 1-16l1 1v-3 6c-1 8 1 16 2 23h-1-1v-5h-1v8-3z" class="o"></path><path d="M540 651l1 2-9 35-2 10v-1-4-1c1-1 1-1 1-2v-2c0-2 1-4 1-5v-1l1-2v-3h0c1-1 1-2 1-3h0v-2l-4 14-2 10h-1l2-11c-1-2 1-5 0-7v-3l1-2c1-2 3-4 4-6l5-12 1-4z" class="b"></path><path d="M530 673c1-2 3-4 4-6l-5 18c-1-2 1-5 0-7v-3l1-2z" class="Q"></path><path d="M523 690c1-7 2-10 6-15v3c1 2-1 5 0 7l-2 11-1 3v3l-1-1c0 5-1 10-1 16 0 3 1 7 1 11l-2-5c-1-5-1-10-1-15v-6l1-7v-5z" class="l"></path><path d="M523 690c1-7 2-10 6-15v3c-2 5-3 12-6 17v-5z" class="J"></path><defs><linearGradient id="EH" x1="539.876" y1="742.005" x2="528.494" y2="737.637" xlink:href="#B"><stop offset="0" stop-color="#898687"></stop><stop offset="1" stop-color="#a2a3a3"></stop></linearGradient></defs><path fill="url(#EH)" d="M532 726c1 2 1 4 2 6l1 2h1 0l1-1c0 1 0 0 1 1l1-1 1 5v1c0 1 2 5 1 7 2 5 4 9 4 14l-1 4-2 5c-1-1-1-2-2-3l-2-6-2-5c-2-7-4-15-4-22v-7z"></path><path d="M534 732l1 2h1 0l1 6c0 2 0 4 1 6-2-4-4-9-4-14z" class="S"></path><path d="M539 733l1 5v1l-1 1-1-1-1 1-1-6 1-1c0 1 0 0 1 1l1-1z" class="g"></path><path d="M537 740l1-1 1 1 1-1c0 1 2 5 1 7 0 1 0 2 1 3-1 1 0 1-1 1l-1 1 1 4h0l-2-3c-1-2-2-4-1-6-1-2-1-4-1-6z" class="I"></path><defs><linearGradient id="EI" x1="545.601" y1="758.85" x2="539.124" y2="754.791" xlink:href="#B"><stop offset="0" stop-color="#5f5a5b"></stop><stop offset="1" stop-color="#787679"></stop></linearGradient></defs><path fill="url(#EI)" d="M541 746c2 5 4 9 4 14l-1 4-2 5c-1-1-1-2-2-3l-2-6-2-5c1 0 1 0 1-1h0 1v-3l1 1 2 3h0l-1-4 1-1c1 0 0 0 1-1-1-1-1-2-1-3z"></path><path d="M536 755c1 0 1 0 1-1h0 1v-3l1 1 2 3h0c1 3 1 7 0 10l-1 1-2-6-2-5z" class="Q"></path><path d="M477 538v-2l1-1 1-1v-1h2c1 0 1 0 2 1 0 2 0 3 1 5v1-3-1l1 6 1-6v58 14 8h1v-2 1h2c0-1 0-2 1-2l2-3-2-2h0c2-3 6-4 8-6l4-1c3-2 6-3 10-3 0-3-3-6-4-9l-4-9h-1c-2-5 0-10 1-14l2-4 1-2 1-1c2-3 4-5 7-7l6-3c1 0 2-1 4 0 0 1 0 3-1 4v3l-1 1c-5 10-1 20-3 30v1h1v1c2 2 4 3 5 5l3 3h3c5 2 10 5 16 6 2 1 4 2 6 2l2 1c-1 2-1 2-2 2 2 1 5 1 7 2l1-3h1l8-1h5c4-1 8-1 12-2 1-1 2-1 4-1l8-5h6l1 1-5 6c-7 5-16 12-24 11l-1 1-11 1h-2c-2 1-4 1-7 1h1v1c-3-1-7-2-9 0 1 1 2 3 2 6l-1 5h-1l1-4-1-1c-2 1-3 2-3 4v2l-1 2-4 19-1-2-1 4-5 12c-1 2-3 4-4 6l-1 2c-4 5-5 8-6 15v5l-1 7-3 3h-1v-3l-1-1v-1h0v-4l-1-1-1 2c-1 0-1 0-1-1v-1c1-2-1-4-2-6-1-1-1-2-2-3 0 2 1 3 2 4v2l1 4c1 2 0 5 0 7l-19-51-32-88 1-1h1v-7-20h1v12 4c1 8 1 15 2 22 1 4 2 6 4 9 0 1 0 2 1 3 0 1 1 2 1 4 1 1 2 3 3 4 0 2 0 4 1 5v-61z" class="g"></path><path d="M490 613c3-3 6-4 9-6l1 1-2 1h1v1c-4 2-7 4-10 6h-1l-1-1h2c0-1 0-2 1-2z" class="b"></path><path d="M504 645c1 3 1 7 1 11 2-3 0-9 2-12h0v24-9c-1 1-1 2-2 3 0-3 0-5-1-8v5l-1-1 1-13z" class="C"></path><path d="M515 609c3 0 6 1 9 3-1 0-2 0-2-1h-1c-2 3-1 9-1 12 0 2 0 3-1 4h-1c-1-1-2-1-2-1-1-2 1-13-1-17h0z" class="J"></path><path d="M486 608v8h1v-2 1l1 1h1c1 1 2 2 3 2v2c1 2 0 5 0 7v12c-2-5-5-11-6-17-1-4 0-9 0-14z" class="R"></path><path d="M503 623c2 0 4 1 6 0v1l-1 1c0 6 1 13-1 19h0c-2 3 0 9-2 12 0-4 0-8-1-11-1-1 0-5-1-7 0-2 1-4 1-6-1-3-1-6-1-9z" class="D"></path><path d="M531 615h1c-1-1-2-1-2-2h1l-1-1 1-1c1 1 2 1 2 2l1 1s1 1 2 1c4 1 11 4 13 7 0 2 1 3 1 5h0l-1-1c-2 1-3 2-3 4v2l-1 2-4 19-1-2c3-6 4-13 3-20s-6-12-12-16z" class="a"></path><path d="M536 615c4 1 11 4 13 7 0 2 1 3 1 5h0l-1-1c-2 1-3 2-3 4v2l-1 2v-4c0-7-4-11-9-15zm-33 43c-1-4-5-8-6-13-3-8-3-16-1-24l7 2c0 3 0 6 1 9 0 2-1 4-1 6 1 2 0 6 1 7l-1 13z" class="G"></path><defs><linearGradient id="EJ" x1="493.253" y1="609.733" x2="469.694" y2="535.806" xlink:href="#B"><stop offset="0" stop-color="#aeadad"></stop><stop offset="1" stop-color="#edecec"></stop></linearGradient></defs><path fill="url(#EJ)" d="M477 538v-2l1-1 1-1v-1h2c1 0 1 0 2 1 0 2 0 3 1 5v1-3-1l1 6-1 50c0 6 1 13 0 19v3h-1l-1-1h0-1c-1-2-1-3-1-5h0-1v-4h0c0-1-1-2-1-3l-1-2h0v-61z"></path><path d="M509 624c2 0 4 1 6 2l1 2c2 0 2 0 4 1v-2c4 0 7 3 11 5 1 1 2 3 4 5h0v2c1 1 1 2 1 3v3 6l1-4v5l1 2c1 1 0 1 1 1l-5 12c-1 2-3 4-4 6l-1 2c-4 5-5 8-6 15v5l-1 7-3 3h-1v-3l-1-1v-1h0v-4l-1-1-1 2c-1 0-1 0-1-1v-1c1-2-1-4-2-6-1-1-1-2-2-3-2-2-2-5-2-8l-1-10v-24c2-6 1-13 1-19l1-1z" class="C"></path><path d="M521 631v-2c1-1 2-1 3 0 4 1 9 5 10 9 1 1 1 3 1 4-1-2-3-4-5-6l-1-1c-2-2-5-4-8-4z" class="S"></path><path d="M518 693c0-2 1-4 0-6v-12c-1-1-1-1-1-2 1-1 1-2 2-3h0c1 0 0 7 1 9 0-3 0-6 1-8 0 6 0 12-1 18 0 1 0 6-1 7s-1 2-2 3v-1l1-5z" class="g"></path><defs><linearGradient id="EK" x1="525.209" y1="680.763" x2="499.878" y2="628.961" xlink:href="#B"><stop offset="0" stop-color="#bfbfbe"></stop><stop offset="1" stop-color="#e9e8e8"></stop></linearGradient></defs><path fill="url(#EK)" d="M509 624c2 0 4 1 6 2v42c0 6 1 14 0 20-2-2-4-4-5-7l-2-3-1-10v-24c2-6 1-13 1-19l1-1z"></path><defs><linearGradient id="EL" x1="501.365" y1="681.57" x2="541.498" y2="651.807" xlink:href="#B"><stop offset="0" stop-color="#999899"></stop><stop offset="1" stop-color="#cfcece"></stop></linearGradient></defs><path fill="url(#EL)" d="M521 631c3 0 6 2 8 4l1 1c2 2 4 4 5 6l1 3v6l1-4v5l1 2c1 1 0 1 1 1l-5 12c-1 2-3 4-4 6l-1 2c-4 5-5 8-6 15v5l-1 7-3 3h-1v-3l-1-1v-1h0v-4c0-1 1-2 1-3l-1 5v1c1-1 1-2 2-3s1-6 1-7c1-6 1-12 1-18v-5-16-19z"></path><path d="M536 651l1-4v5l1 2c1 1 0 1 1 1l-5 12c-1 2-3 4-4 6l-1 2c-4 5-5 8-6 15v-9h0c2-5 4-9 6-14 1 0 1-1 2-1v-1c1-2 0-2 1-2 0-1 1-2 1-2v-1c0-2 2-6 3-9h0z" class="B"></path><path d="M536 651l1-4v5l1 2c1 1 0 1 1 1l-5 12c-1 2-3 4-4 6 1-6 5-11 6-16v-6h0z" class="W"></path><path d="M521 549c1 0 2-1 4 0 0 1 0 3-1 4v3l-1 1c-5 10-1 20-3 30v1h1v1c2 2 4 3 5 5l3 3h3c5 2 10 5 16 6 2 1 4 2 6 2l2 1c-1 2-1 2-2 2 2 1 5 1 7 2l1-3h1l8-1h5c4-1 8-1 12-2 1-1 2-1 4-1l8-5h6l1 1-5 6c-7 5-16 12-24 11l-1 1-11 1h-2c-2 1-4 1-7 1h1v1c-3-1-7-2-9 0 1 1 2 3 2 6l-1 5h-1l1-4h0c0-2-1-3-1-5-2-3-9-6-13-7-1 0-2-1-2-1l-1-1c0-1-1-1-2-2l-1 1 1 1h-1c0 1 1 1 2 2h-1c-2-1-4-2-7-3-3-2-6-3-9-3-5-1-10-2-14 0h-1l-1 1v-1h-1l2-1-1-1c-3 2-6 3-9 6l2-3-2-2h0c2-3 6-4 8-6l4-1c3-2 6-3 10-3 0-3-3-6-4-9l-4-9h-1c-2-5 0-10 1-14l2-4 1-2 1-1c2-3 4-5 7-7l6-3z" class="k"></path><path d="M551 617c5 1 9 1 13 1-2 1-4 1-7 1h1v1c-3-1-7-2-9 0l-1-1h0v-1h1c1 0 1 0 2-1h0z" class="O"></path><path d="M502 601l2 1c-5 2-8 4-12 8l-2-2h0c2-3 6-4 8-6l4-1z" class="m"></path><path d="M506 576c2 5 5 9 6 13 0 1 1 2 1 3v2l-4-6-4-9 1-3z" class="L"></path><path d="M509 588l4 6 3 6h-5c-3 1-5 1-7 2l-2-1c3-2 6-3 10-3 0-3-3-6-4-9l1-1z" class="O"></path><path d="M538 613l13 4h0c-1 1-1 1-2 1h-1v1h0l1 1c1 1 2 3 2 6l-1 5h-1l1-4h0c0-2-1-3-1-5-2-3-9-6-13-7-1 0-2-1-2-1l-1-1h3v1l2-1z" class="s"></path><path d="M499 607c9-4 20-1 28 2l11 4-2 1v-1h-3c0-1-1-1-2-2l-1 1 1 1h-1c0 1 1 1 2 2h-1c-2-1-4-2-7-3-3-2-6-3-9-3-5-1-10-2-14 0h-1l-1 1v-1h-1l2-1-1-1z" class="x"></path><path d="M521 549c1 0 2-1 4 0 0 1 0 3-1 4v3l-1 1c-5 10-1 20-3 30v1s0 1-1 1c-3-6-5-16-3-24h0c2-6 6-11 9-16l-3 1c-8 3-13 9-16 16v5 5l-1 3 4 9-1 1-4-9h-1c-2-5 0-10 1-14l2-4 1-2 1-1c2-3 4-5 7-7l6-3z" class="t"></path><path d="M506 566v5 5l-1 3c-2-5-1-8 1-13z" class="G"></path><path d="M524 553v3l-1 1c-5 10-1 20-3 30-1-1-2-3-2-5v-1c-1-5-2-12 0-16v-1c1-4 4-7 6-11z" class="C"></path><path d="M520 588h1v1c2 2 4 3 5 5l3 3h3c5 2 10 5 16 6 2 1 4 2 6 2l2 1c-1 2-1 2-2 2 2 1 5 1 7 2l1-3h1l8-1h5c4-1 8-1 12-2 1-1 2-1 4-1l8-5h6l1 1-5 6c-7 5-16 12-24 11l-1 1-11 1c2-1 3-1 5-1 1-1 0-1 2-1 0 0 0-1 1-1l-1-1v-1l-3 1h-4-1c-8 0-15-1-22-5-9-3-20-10-24-20 1 0 1-1 1-1z" class="U"></path><path d="M530 598l3 3 1 2c-3-1-4-2-4-5z" class="l"></path><path d="M533 601l8 3c1 2 1 2 3 3 1 1 2 1 3 2-1 0-2 0-3-1-4-1-7-3-10-5l-1-2z" class="M"></path><path d="M571 606h5c1 0 2 1 3 2-1 1-3 1-4 1s0 0-1 1h-6-6-1l1-3h1l8-1z" class="Z"></path><path d="M562 607h1c2 0 4 1 6 2l-1 1h-6-1l1-3z" class="B"></path><defs><linearGradient id="EM" x1="560.614" y1="609.397" x2="544.874" y2="608.28" xlink:href="#B"><stop offset="0" stop-color="#787373"></stop><stop offset="1" stop-color="#938f90"></stop></linearGradient></defs><path fill="url(#EM)" d="M541 604l13 4c2 1 5 1 7 2h1c1 1 2 1 3 2-6 0-13 0-18-3-1-1-2-1-3-2-2-1-2-1-3-3z"></path><path d="M529 597h3c5 2 10 5 16 6 2 1 4 2 6 2l2 1c-1 2-1 2-2 2l-13-4-8-3-3-3-1-1z" class="T"></path><path d="M600 598h6l1 1-5 6c-7 5-16 12-24 11l-1 1-11 1c2-1 3-1 5-1 1-1 0-1 2-1 0 0 0-1 1-1l-1-1v-1l-3 1h-4-1c3-1 7-2 10-2 4-2 8-3 11-5l6-4 8-5z" class="C"></path><path d="M586 607h1c1 0 2 0 4-1 0 1 0 1-1 2l-12 6c-1 0-2 1-3 2h3 0l-1 1-11 1c2-1 3-1 5-1 1-1 0-1 2-1 0 0 0-1 1-1l-1-1v-1l-3 1h-4-1c3-1 7-2 10-2 4-2 8-3 11-5z" class="G"></path><defs><linearGradient id="EN" x1="442.83" y1="556.226" x2="469.724" y2="341.343" xlink:href="#B"><stop offset="0" stop-color="#c9c8c8"></stop><stop offset="1" stop-color="#f3f3ef"></stop></linearGradient></defs><path fill="url(#EN)" d="M437 323h1c0-4-1-8 0-12v-1c2 0 2 1 3 2 2 1 4 3 5 5h0c-1 4 0 8 1 12h0l2 1c2 2 4 4 5 7l5 9 1 2v-11h1c0 2 0 5 1 7l1 2h1c0-1 1-3 1-5v-2h4c1 1 2 1 3 1 2 1 2 1 3 2s1 1 1 2h1l1 1c1 1 2 3 3 3l2 2c0-2-1-3-1-4l-1-1 1-1 4 6c1 0 1 0 2 1v-1l3 2h3c-1 3-1 7-1 10v1l1 4h1c1 2 1 4 1 6h2c0 2 1 3 2 4l1 2h1c0 2 1 4 2 5l1 1v5 6l1 4c1 2 0 4 1 6v2c-1 4-1 9-1 14 0 2 1 4 0 6 1 2 1 5 1 7l4 27c-1 2-1 4-2 6l1 5c-1 0-1 0-2 1-1 6 0 12-1 18v-3h0l-1 1v27 21l-1 13c0 1 0 4 1 5v1c0 1 0 2 1 3l-1 2-2 4c-1 4-3 9-1 14h1l4 9c1 3 4 6 4 9-4 0-7 1-10 3l-4 1c-2 2-6 3-8 6h0l2 2-2 3c-1 0-1 1-1 2h-2v-1 2h-1v-8-14-58l-1 6-1-6v1 3-1c-1-2-1-3-1-5-1-1-1-1-2-1h-2v1l-1 1-1 1v2 61c-1-1-1-3-1-5-1-1-2-3-3-4 0-2-1-3-1-4-1-1-1-2-1-3-2-3-3-5-4-9-1-7-1-14-2-22v-4-12h-1v20 7h-1l-1 1-6-17c-1-2-2-4-2-5-2-4-3-7-4-10l-6-16c-1-1-7-19-9-22 1-3 0-5 0-9-1-7-3-15-6-21-1-3-3-5-4-8-2-7-2-14-2-21l2-1h0v1c0 1 1 2 2 3l1 1c1-1 2-2 3-2l1-3c0-1 0-2-1-3-1-2-3-3-6-3h0c-2-3-1-8-1-11l1-31v-16-15-7-14l1-1v-1-2c1-3 0-6 1-8l5 1c1 0 3 0 4 1l1-1z"></path><path d="M464 518c0 1 1 1 1 2v3c0 4 1 9 0 12v1h-1v20 7h-1l-1 1-6-17h0l4 9c0 1 1 2 1 3l2-1v-4-2c1-3 1-29 1-34z" class="j"></path><path d="M467 363c2 2 4 5 5 8l-4-4c-1-1-1 0 0-1l-1-1v5c-1 6 0 12-1 18-1 8 0 17 0 25 0 3 0 7-1 10v-36c0-7-1-14 0-21h1v3h0v-1-1-4h1z" class="E"></path><path d="M454 337l5 9c1 5 4 9 5 13l1 1 2-1v2c-1 1-1 0 0 2h-1v4 1 1h0v-3h-1c0-1 0-3-1-4-3-4-4-8-7-11 0-3-2-6-3-8v-6z" class="C"></path><defs><linearGradient id="EO" x1="441.046" y1="428.231" x2="451.424" y2="427.873" xlink:href="#B"><stop offset="0" stop-color="#a2a1a3"></stop><stop offset="1" stop-color="#d7d6d4"></stop></linearGradient></defs><path fill="url(#EO)" d="M447 337v3l1-1c0-1 0-2-1-3v-6c1 1 1 2 1 3l1-3c2 2 4 4 5 7v6c1 2 3 5 3 8-2-2-5-8-6-11v-1l-1 2c0 4 0 7 1 11 0 2-1 4-1 5v32l-1 93v25c0 6 0 12 1 18v1c0 1 0 1 1 3v2c1 3 3 7 3 11-2-4-3-7-4-10l-6-16h1l-1-2h2v-1-3c1-2 0-6 0-8v-18l1-95-1-48 1-4z"></path><path d="M449 330c2 2 4 4 5 7v6c1 2 3 5 3 8-2-2-5-8-6-11v-1l-1 2c-1 2 0 4-1 6v12c-1-3 0-8 0-12v-3-4c-1-3-1-5-1-7l1-3z" class="P"></path><path d="M449 330c2 2 4 4 5 7v6c0-2-1-3-2-5l-1-2h-1c-2 2-1 6-1 8v-4c-1-3-1-5-1-7l1-3z" class="S"></path><defs><linearGradient id="EP" x1="487.87" y1="459.562" x2="396.507" y2="388.453" xlink:href="#B"><stop offset="0" stop-color="#d5d7d6"></stop><stop offset="1" stop-color="#fffdfb"></stop></linearGradient></defs><path fill="url(#EP)" d="M437 323h1c0-4-1-8 0-12v-1c2 0 2 1 3 2 2 1 4 3 5 5h0c-1 4 0 8 1 12h0l2 1-1 3c0-1 0-2-1-3v6c1 1 1 2 1 3l-1 1v-3l-1 4 1 48-1 95v18c0 2 1 6 0 8v3 1h-2c0-2-1-3-1-4l-1-1c1-1 0-1 1-2h0c0-2-1-5-3-6h-1c0-2 0-2 1-4l1 1 1-1c-1-2-2-4-2-6h0l-2-27c1-3 0-7 0-10v-60c0-12 1-23 0-35-2-3-1-12-1-16l-1-19 1-1z"></path><path d="M438 333h1 2l2 2v-3h0 0c1 2 2 2 4 3v2l-1 4-3-3h0 0l-2-2c-1-1-1-1-2-1 0 1-1 3-1 4v-6z" class="L"></path><defs><linearGradient id="EQ" x1="443.648" y1="311.211" x2="439.915" y2="325.545" xlink:href="#B"><stop offset="0" stop-color="#827f81"></stop><stop offset="1" stop-color="#989797"></stop></linearGradient></defs><path fill="url(#EQ)" d="M437 323h1c0-4-1-8 0-12v-1c2 0 2 1 3 2 2 1 4 3 5 5h0c-1 4 0 8 1 12h0l2 1-1 3c0-1 0-2-1-3v6c1 1 1 2 1 3l-1 1v-3-2c-2-1-3-1-4-3h0 0v3l-2-2h-2-1v6 20c-2-3-1-12-1-16l-1-19 1-1z"></path><path d="M438 333c-1-2 0-6 0-9 3 1 6 2 8 5l1 6c-2-1-3-1-4-3h0 0v3l-2-2h-2-1z" class="E"></path><path d="M426 332v-2c1-3 0-6 1-8l5 1c1 0 3 0 4 1l1 19c0 4-1 13 1 16 1 12 0 23 0 35v60c0 3 1 7 0 10l2 27h0c0 2 1 4 2 6l-1 1-1-1c-1 2-1 2-1 4h1c2 1 3 4 3 6h0c-1 1 0 1-1 2l1 1c0 1 1 2 1 4l1 2h-1c-1-1-7-19-9-22 1-3 0-5 0-9-1-7-3-15-6-21-1-3-3-5-4-8-2-7-2-14-2-21l2-1h0v1c0 1 1 2 2 3l1 1c1-1 2-2 3-2l1-3c0-1 0-2-1-3-1-2-3-3-6-3h0c-2-3-1-8-1-11l1-31v-16-15-7-14l1-1v-1z" class="H"></path><path d="M425 348v-14l1-1v32c0 2 0 3-1 5h0v-15-7z" class="g"></path><path d="M426 365l-1 63h0c-2-3-1-8-1-11l1-31v-16h0c1-2 1-3 1-5z" class="C"></path><path d="M423 435l2-1h0v1c0 1 1 2 2 3l1 1c1-1 2-2 3-2-1 2-1 2-4 3 0-1-1-1-1-2l-1-1c-2 2-2 5-1 8v2 1l1 5c2 8 7 14 9 22l2 7c1 2 1 5 2 8 0 1 1 3 1 4v-7c-1-1 0-3 0-5l-1-1v-7-1-9l2 27h0c0 2 1 4 2 6l-1 1-1-1c-1 2-1 2-1 4h1c2 1 3 4 3 6h0c-1 1 0 1-1 2l1 1c0 1 1 2 1 4l1 2h-1c-1-1-7-19-9-22 1-3 0-5 0-9-1-7-3-15-6-21-1-3-3-5-4-8-2-7-2-14-2-21z" class="G"></path><path d="M466 388c1-6 0-12 1-18v-5l1 1c-1 1-1 0 0 1l4 4 2 8c3 1 3 2 6 4l1 3 1 7c1 6 0 13 3 18 0 1 1 1 2 2-1 1-1 4-1 6v16 8 70 17 6l-1 6-1-6v1 3-1c-1-2-1-3-1-5-1-1-1-1-2-1h-2v1l-1 1-1 1v2 61c-1-1-1-3-1-5-1-1-2-3-3-4 0-2-1-3-1-4-1-1-1-2-1-3-2-3-3-5-4-9-1-7-1-14-2-22v-4-12-1c1-3 0-8 0-12v-3c0-1-1-1-1-2v-6-21l1-68c1-3 1-7 1-10 0-8-1-17 0-25z" class="c"></path><path d="M485 411c0 1 1 1 2 2-1 1-1 4-1 6v16 8c0 1-1 3-1 4v-36z" class="E"></path><path d="M485 447c0-1 1-3 1-4v70 17 6l-1 6-1-6 1-89z" class="S"></path><path d="M474 379c3 1 3 2 6 4l1 3 1 7c-1-2-2-2-2-4-1 0-1 1-2 2v3c-1 4-1 9-1 13v25 67 11c0 1 0 3-1 4-2 5 0 12 0 18-1-5-1-10-1-15l1-25v-21-36-25c0-8 2-19-1-27l-1-4z" class="E"></path><path d="M466 388c1-6 0-12 1-18v-5l1 1c-1 1-1 0 0 1l4 4 2 8 1 4c3 8 1 19 1 27v25 36 21l-1-2c-1-2 0-3-1-5v-9c0-2-1-4-2-6 0-2-1-4-1-5l-3-4-1 1h-1v-74z" class="T"></path><defs><linearGradient id="ER" x1="454.385" y1="528.155" x2="486.582" y2="518.556" xlink:href="#B"><stop offset="0" stop-color="#bfbebe"></stop><stop offset="1" stop-color="#e9eae8"></stop></linearGradient></defs><path fill="url(#ER)" d="M465 423c1-3 1-7 1-10 0-8-1-17 0-25v74h1l1-1 3 4c0 1 1 3 1 5 1 2 2 4 2 6v9c1 2 0 3 1 5l1 2-1 25c0 5 0 10 1 15 0-6-2-13 0-18l1 24v61c-1-1-1-3-1-5-1-1-2-3-3-4 0-2-1-3-1-4-1-1-1-2-1-3-2-3-3-5-4-9-1-7-1-14-2-22v-4-12-1c1-3 0-8 0-12v-3c0-1-1-1-1-2v-6-21l1-68z"></path><path d="M476 532c0-6-2-13 0-18l1 24v61c-1-1-1-3-1-5-1-1-2-3-3-4 0-2-1-3-1-4-1-1-1-2-1-3 1 1 2 3 3 4l2-55z" class="B"></path><path d="M465 423c1-3 1-7 1-10 0-8-1-17 0-25v74 58l1 22c0 4 1 8 0 12h0c0-2 0-6-1-8 0-1 0-1 1-2-1-1 0-2-1-3v-7-13c-1 1 0 8 0 10v13c0 2-1 5 0 7v1h-1v-4-12-1c1-3 0-8 0-12v-3c0-1-1-1-1-2v-6-21l1-68z" class="G"></path><path d="M460 348v-11h1c0 2 0 5 1 7l1 2h1c0-1 1-3 1-5v-2h4c1 1 2 1 3 1 2 1 2 1 3 2s1 1 1 2h1l1 1c1 1 2 3 3 3l2 2c0-2-1-3-1-4l-1-1 1-1 4 6c1 0 1 0 2 1v-1l3 2h3c-1 3-1 7-1 10v1l1 4h1c1 2 1 4 1 6h2c0 2 1 3 2 4l1 2h1c0 2 1 4 2 5l1 1v5 6l1 4c1 2 0 4 1 6v2c-1 4-1 9-1 14 0 2 1 4 0 6 1 2 1 5 1 7l4 27c-1 2-1 4-2 6l1 5c-1 0-1 0-2 1-1 6 0 12-1 18v-3h0l-1 1v27 21l-1 13c0 1 0 4 1 5v1c0 1 0 2 1 3l-1 2-2 4c-1 4-3 9-1 14h1l4 9c1 3 4 6 4 9-4 0-7 1-10 3l-4 1c-2 2-6 3-8 6h0l2 2-2 3c-1 0-1 1-1 2h-2v-1 2h-1v-8-14-58-6-17-70-8-16c0-2 0-5 1-6-1-1-2-1-2-2-3-5-2-12-3-18l-1-7-1-3c-3-2-3-3-6-4l-2-8c-1-3-3-6-5-8-1-2-1-1 0-2v-2l-2 1-1-1c-1-4-4-8-5-13l1 2z" class="d"></path><path d="M492 437h2l2 3-1-1c-1 1-1 1-1 2v49c-1-4-1-7-1-10v-17c0-2 1-6 0-8-1-1-1-4-1-5v-13z" class="L"></path><path d="M494 545v1 17c0 2 0 5 1 7v6c0-1-1-3 0-4 0-1 2-3 3-4-1 3-1 6-1 9l-1-3h0v4c-1 2-1 3-1 5v-3c-1 0-1 1-2 1v-5 2h0c-1-2 0-6 0-8v-19l1-6z" class="E"></path><path d="M506 517v21l-1 13c0 1 0 4 1 5-1 2-1 3-2 4v1c-1 1-2 1-3 1l1-1c3-10 2-22 2-32 1-4 1-8 2-12z" class="p"></path><path d="M493 455c1 2 0 6 0 8v17c0 3 0 6 1 10v41 14l-1 6v-96z" class="G"></path><path d="M506 556v1c0 1 0 2 1 3l-1 2-2 4c-5 4-4 9-6 15v4c0 5 2 12 0 17-2 2-6 3-8 6h0l-1 1c0-3 1-4 3-6 1-4 1-6 0-10 0-4 1-10 1-15h0v-2 5c1 0 1-1 2-1v3c0-2 0-3 1-5v-4h0l1 3c0-3 0-6 1-9l3-6c1 0 2 0 3-1v-1c1-1 1-2 2-4z" class="j"></path><path d="M498 585c-1-3-1-8 0-12h0c1-3 2-5 3-7 2-2 2-3 5-4l-2 4c-5 4-4 9-6 15v4z" class="g"></path><path d="M493 578h0v-2 5c1 0 1-1 2-1v3c1 5 1 9 1 13 1 2 1 4 0 6-1 1-2 1-3 2 0 1-1 1-1 1v-2c1-4 1-6 0-10 0-4 1-10 1-15z" class="S"></path><path d="M498 585v-4c2-6 1-11 6-15-1 4-3 9-1 14h1l4 9c1 3 4 6 4 9-4 0-7 1-10 3l-4 1c2-5 0-12 0-17z" class="W"></path><defs><linearGradient id="ES" x1="507.413" y1="426.923" x2="493.13" y2="473.938" xlink:href="#B"><stop offset="0" stop-color="#938f8e"></stop><stop offset="1" stop-color="#cacac9"></stop></linearGradient></defs><path fill="url(#ES)" d="M494 418h1c0 2 0 3 1 5v1c2 1 4 1 5 3 1 1 2 4 3 6v-4c1 1 1 2 2 3 0-2 0-3-1-5l1 1c1 2 1 5 1 7l4 27c-1 2-1 4-2 6l1 5c-1 0-1 0-2 1-1 6 0 12-1 18v-3h0l-1 1c0-8 0-15-2-22 0-4 0-7-1-11-2-6-4-11-7-17l-2-3v-2c0-1-1-1 0-2 1-3 0-6 0-9l1-1c-1-2-1-3-1-5z"></path><path d="M505 437l3 22-3-3c-1-4-1-8-2-13v-3l2-3z" class="B"></path><path d="M505 427l1 1c1 2 1 5 1 7l4 27c-1 2-1 4-2 6l-1-9-3-22c0-1-1-3-1-4v-4c1 1 1 2 2 3 0-2 0-3-1-5z" class="X"></path><path d="M487 413l4 5v1l1 18v13c0 1 0 4 1 5v96 19c0 2-1 6 0 8 0 5-1 11-1 15 1 4 1 6 0 10-2 2-3 3-3 6l1-1 2 2-2 3c-1 0-1 1-1 2h-2v-1 2h-1v-8-14-58-6-17-70-8-16c0-2 0-5 1-6z" class="d"></path><path d="M486 594v6c0 3 0 6 1 8 1-1 3-4 3-5l1-1v-3l1-1v-4-1c1 4 1 6 0 10-2 2-3 3-3 6l1-1 2 2-2 3c-1 0-1 1-1 2h-2v-1 2h-1v-8-14z" class="G"></path><path d="M490 608l2 2-2 3c-1 0-1 1-1 2h-2v-1l2-5 1-1z" class="X"></path><path d="M460 348v-11h1c0 2 0 5 1 7l1 2h1c0-1 1-3 1-5v-2h4c1 1 2 1 3 1 2 1 2 1 3 2s1 1 1 2h1l1 1c1 1 2 3 3 3l2 2c0-2-1-3-1-4l-1-1 1-1 4 6c1 0 1 0 2 1v-1l3 2h3c-1 3-1 7-1 10v1l1 4h1c1 2 1 4 1 6h2c0 2 1 3 2 4l1 2h1c0 2 1 4 2 5l1 1v5 6l1 4c1 2 0 4 1 6v2c-1 4-1 9-1 14 0 2 1 4 0 6l-1-1c1 2 1 3 1 5-1-1-1-2-2-3v4c-1-2-2-5-3-6-1-2-3-2-5-3v-1c-1-2-1-3-1-5h-1c0 2 0 3 1 5l-1 1c0 3 1 6 0 9-1 1 0 1 0 2v2h-2l-1-18v-1l-4-5c-1-1-2-1-2-2-3-5-2-12-3-18l-1-7-1-3c-3-2-3-3-6-4l-2-8c-1-3-3-6-5-8-1-2-1-1 0-2v-2l-2 1-1-1c-1-4-4-8-5-13l1 2z" class="I"></path><path d="M480 361v6c0 1-1 2-2 3v-8l2-1z" class="K"></path><path d="M465 339h4c1 1 2 1 3 1 2 1 2 1 3 2s1 1 1 2l1 3-1 1c2 2 3 5 3 8l1 1v4l-2 1h0c0-1-1-2-1-3l-1-1v-1c-1-1-1-2-2-3h-1v-1c-1 0-2-1-2-1l-1-1c-1 0-1 1-2 1h-2c-1-2-3-3-3-5v-1h1c0-1 1-3 1-5v-2z" class="Q"></path><path d="M473 350c3 1 4 2 5 5v1h1 0l1 1v4l-2 1h0v-1c0-5-2-8-5-11z" class="o"></path><path d="M464 346h0 2c2-1 5-2 7-1l3 3c2 2 3 5 3 8h0-1v-1c-1-3-2-4-5-5h-2c-1 0-3-1-5-1-1 0-2-1-3-2v-1h1z" class="r"></path><path d="M465 339h4c1 1 2 1 3 1 2 1 2 1 3 2s1 1 1 2l1 3-1 1-3-3c-2-1-5 0-7 1h-2 0c0-1 1-3 1-5v-2z" class="C"></path><path d="M465 339h4l-2 3-2-1v-2z" class="I"></path><path d="M459 346l1 2h1c1 2 2 3 3 5s2 4 4 6v1c0 1 1 1 1 1 0 1 1 1 1 2 1 1 2 2 2 3l3 5h0 0c1 1 1 3 1 5 1 0 0 0 1-1 1 2 2 3 3 4l2 2h1v-1c-1-1 0-1 0-2h0c1 1 1 3 1 4s0 1 1 2c0 1 1 3 2 4 0 1 0 2-1 3 0 4 0 8 3 11l1 1c0 1 1 3 1 4s1 1 1 2c1 1 1 1 1 3h0c0 2 0 3 1 5v1c0 2 0 3 1 5l-1 1c0 3 1 6 0 9-1 1 0 1 0 2v2h-2l-1-18v-1l-4-5c-1-1-2-1-2-2-3-5-2-12-3-18l-1-7-1-3c-3-2-3-3-6-4l-2-8c-1-3-3-6-5-8-1-2-1-1 0-2v-2l-2 1-1-1c-1-4-4-8-5-13z" class="g"></path><path d="M481 386c4 6 2 14 4 20v1c1 3 2 5 4 7 1 2 2 2 2 4l-4-5c-1-1-2-1-2-2-3-5-2-12-3-18l-1-7z" class="C"></path><path d="M476 344h1l1 1c1 1 2 3 3 3l2 2c0-2-1-3-1-4l-1-1 1-1 4 6c1 0 1 0 2 1v-1l3 2h3c-1 3-1 7-1 10v1l-2-1v3c1 2 2 4 3 7 2 3 3 7 4 11-1 0-2 0-2 1v1l-1 1-1-1h0c-1 3-1 5 0 7-1 1-2 1-3 3l-1-1v1l-1 1h0c-1-1-1-4-1-6 1-4-1-10-2-14-3-1-5-1-8-2h-1c0-1 1-3 1-4 1-1 2-2 2-3v-6-4l-1-1c0-3-1-6-3-8l1-1-1-3z" class="B"></path><path d="M479 351l2-1 1 1 1-1c0 1 0 2 1 4h-1c-1 0-1-1-1-1-1-1-2-1-2-2h-1z" class="I"></path><path d="M478 345c1 1 2 3 3 3l2 2h0l-1 1-1-1-2 1c-1-2-1-4-1-6z" class="i"></path><path d="M491 391c1-1 1-2 1-3 1-1 1-5 1-7l1 1v3h0c-1 3-1 5 0 7-1 1-2 1-3 3l-1-1 1-3z" class="D"></path><path d="M488 350l3 2h3c-1 3-1 7-1 10v1l-2-1c0-1-1-3-1-4-1-3-1-5-2-7v-1z" class="f"></path><path d="M491 352h3c-1 3-1 7-1 10l-1-2c-1-3-1-5-1-8z" class="U"></path><path d="M480 357v-1h1 0c0 2 0 2 1 3v-2h1c1 2 3 7 3 9l-3-1c-2 1-2 1-3 2v-6-4z" class="G"></path><path d="M480 357v-1h1 0c0 2 0 2 1 3v5 1h1c-2 1-2 1-3 2v-6-4z" class="j"></path><path d="M496 384c-3-5-4-9-5-13-1-2-2-3-3-5-1-3-2-5-2-8h1c0 3 2 5 4 7 1 2 2 4 3 7 2 3 3 7 4 11-1 0-2 0-2 1z" class="P"></path><path d="M483 365l3 1c4 9 5 16 5 25l-1 3v1l-1 1h0c-1-1-1-4-1-6 1-4-1-10-2-14-3-1-5-1-8-2h-1c0-1 1-3 1-4 1-1 2-2 2-3 1-1 1-1 3-2z" class="X"></path><path d="M478 374c2-1 3-1 5-2 1 1 2 3 3 4-3-1-5-1-8-2z" class="Q"></path><path d="M491 362l2 1 1 4h1c1 2 1 4 1 6h2c0 2 1 3 2 4l1 2h1c0 2 1 4 2 5l1 1v5 6l1 4c1 2 0 4 1 6v2c-1 4-1 9-1 14 0 2 1 4 0 6l-1-1c1 2 1 3 1 5-1-1-1-2-2-3v4c-1-2-2-5-3-6-1-2-3-2-5-3v-1c-1-2-1-3-1-5 0-3 0-9-1-12-1-1-2-3-3-5h-1c-1-1-2-2-2-3v-8c0 2 0 5 1 6h0l1-1v-1l1 1c1-2 2-2 3-3-1-2-1-4 0-7h0l1 1 1-1v-1c0-1 1-1 2-1-1-4-2-8-4-11-1-3-2-5-3-7v-3z" class="J"></path><path d="M504 401l-1-2c-1-2-1-3-1-5 1-2 1-3 3-4v6c-1 2-1 3-1 5z" class="i"></path><path d="M498 383c1 1 1 4 2 6v2l-1 1c-1-1-1 0-1-1l-2 1v-1l-1-5 1-1v-1c0-1 1-1 2-1z" class="D"></path><path d="M496 385c0 2 1 4 3 5l1-1v2l-1 1c-1-1-1 0-1-1l-2 1v-1l-1-5 1-1z" class="W"></path><path d="M496 373h2c0 2 1 3 2 4l1 2h1c0 2 1 4 2 5l1 1-1 4-1 1s-1 0-1-1l-1-4c-1 0-3-3-3-4-1-1-1-1-1-2v-1c0-1 0-3-1-5h0z" class="R"></path><path d="M501 385c1 0 1 1 2 2v3s-1 0-1-1l-1-4z" class="D"></path><path d="M500 410c0-1 0-1 1-1v-4-11c0 2 1 3 1 5 1 2 1 3 2 4v1-3c0-2 0-3 1-5l1 4c1 2 0 4 1 6v2c-1 4-1 9-1 14 0 2 1 4 0 6l-1-1c1 2 1 3 1 5-1-1-1-2-2-3 0-1-1-2-1-3-1-2-2-3-5-4h0v-1h1v-2c1-3 1-6 1-9z" class="F"></path><path d="M505 396l1 4c1 2 0 4 1 6v2c-1 4-1 9-1 14 0 2 1 4 0 6l-1-1v-6-12c-1-2-1-4-1-5v-3c0-2 0-3 1-5z" class="N"></path><path d="M505 396l1 4c-1 3 0 6-1 9-1-2-1-4-1-5v-3c0-2 0-3 1-5z" class="f"></path><path d="M494 385l1 1 1 5v1l4 18c0 3 0 6-1 9v2h-1v1h0c3 1 4 2 5 4 0 1 1 2 1 3v4c-1-2-2-5-3-6-1-2-3-2-5-3v-1c-1-2-1-3-1-5 0-3 0-9-1-12-1-1-2-3-3-5h-1c-1-1-2-2-2-3v-8c0 2 0 5 1 6h0l1-1v-1l1 1c1-2 2-2 3-3-1-2-1-4 0-7h0z" class="Q"></path><path d="M496 408l2-2v3c-1 1-1 2-1 4l-1-5z" class="b"></path><path d="M489 396h0l1-1v-1l1 1h1 1l1 2-1 1c-2 0-2 0-4-2z" class="O"></path><path d="M496 396c1 3 2 7 2 10l-2 2c-1-3-2-6-3-10l1-1 2-1z" class="a"></path><path d="M497 413c0-2 0-3 1-4 1 4 1 7 0 12v1h0c3 1 4 2 5 4 0 1 1 2 1 3v4c-1-2-2-5-3-6-1-2-3-2-5-3v-1c1-3 1-6 1-10z" class="m"></path><path d="M494 385l1 1 1 5v1l4 18c0 3 0 6-1 9v2h-1c1-5 1-8 0-12v-3c0-3-1-7-2-10l-2 1-1-2h-1-1c1-2 2-2 3-3-1-2-1-4 0-7h0z" class="G"></path><path d="M494 392c1 1 2 2 2 4l-2 1-1-2h-1-1c1-2 2-2 3-3z" class="q"></path><path d="M521 195c2-3 4-8 8-9 3-1 6-1 8-1l1 1c8 1 16 7 22 12 1-1 1-2 1-3 2 2 3 4 6 4-1 2-1 2-1 3l-1 1c1 0 2 0 2-1 2 0 3-1 4-3h0c0-2-2-3-4-4h0v-3h1l1-1h1c2 2 4 2 6 2l3-2c1-1 3-1 5-2h1c1-2 2-2 5-2h0l2 2h0c3-1 5-2 8-2h0c2 0 4 1 6 0l1 1c2 0 3 0 4 1 1 2 2 2 5 3h0v-1l-1-2 1-1 3 3c2 4 4 9 8 12l2 1h0l1 1 1 1h0c1 0 4 1 5 2h-1c1 2 2 2 4 3l1-1 1 2c-1 0-2 1-4 2 0 0-1 0-2 1 0 1 0 1 1 3h-3c1 3 1 5 1 8 0 2-1 3-2 4h-2 0c1 1 2 1 3 1s1 0 2-1h1c2 0 4 0 7-1-2 1-4 2-5 4l1 1c-9 4-12 12-16 21-2 6-4 12-2 19l3 9c3 5 6 11 10 16 1 1 2 4 3 4-3-8-6-18-2-27 3-5 7-8 12-10 8-3 18-2 26 2 4 2 8 5 10 9-1-1-2-1-3-2v2c1 1 1 3 1 5s0 5-1 7c-2 8-8 17-13 24h0c-1-1 0-3-1-4l-1 1v1c0 3 0 5-2 8h-1c-1-1-2-1-4 0l-8 11c0 2-1 2-1 4l-1 1v1c0 2-2 5-2 7-2 7-1 16 2 22l3 4c3 4 6 6 11 8h7c2 0 4-1 6-2 2 0 4 0 6-1l2 1-2 1h4v-1h1l1 1v-1h2l-1 1v1h0l-1 1c2-1 3-2 4-4l-1 5v1c-1 3-4 4-6 6l2 1 2-2v1c1 1 1 1 1 2-1 4-2 9-4 13-1 4-10 30-11 30h-2c-1 4-2 7-6 10h-1l-1 1c-2 1-5 2-6 4 0 1 0 1 1 2v1l2 1h1l3 1v1 1l-1 3-3 2c-2 1-4 2-6 1l-3-1c-1 1-1 1-2 0h-3c0-1-1-1-2-1l-3 3c-1 2-2 2-2 4l2 11h1c0 4 0 8-1 11-1 4-5 8-4 11l-1 1v2l1 1h-1l1 1-6 6c-2 2-5 4-6 7v1c-3 7-4 14-9 20l-1 1c1 1 1 1 1 2l-1 1-2 2c-1 2-2 3-2 5 0 1 1 1 1 1 0 3 0 5-1 8 0 3 1 6 0 8h-1c0 2 0 4-1 6l-1 1-3 4c-1 3-4 6-7 7h0c-2 3-8 5-10 7l-1-1v-1h-1v3l-3 1c-1-1-1-1-1-3l-1 2v-1l-1 3c0 1 0 1-1 2-2 1-7 1-8 0-1 0-1 0-2-1l-3 1c-1 0-2-1-3-1-1-1-2-1-3-2l1-1v-3l-1-2c-2-3-2-8-2-12v-4c-1 1-1 1-1 2v3l-1-3v-2l-2-2c-1-1-3 0-4 1-3 0-6 3-9 5 0 1-1 1-2 2-1 0-1 1-2 1-4 0-6 1-9 4-1 1-1 2-2 3 0 1 0 2 1 2l1 1 2 2h-6c-1 1-2 1-4 1v-1h-1v-1c2-10-2-20 3-30l1-1v-3c1-1 1-3 1-4-2-1-3 0-4 0l-6 3c-3 2-5 4-7 7l-1 1c-1-1-1-2-1-3v-1c-1-1-1-4-1-5l1-13v-21-27l1-1h0v3c1-6 0-12 1-18 1-1 1-1 2-1l-1-5c1-2 1-4 2-6l-4-27c0-2 0-5-1-7 1-2 0-4 0-6 0-5 0-10 1-14v-2c-1-2 0-4-1-6l-1-4v-6-5l-1-1c1 0 1-1 2-2 0-1 1-1 1-2 0-2 1-2 2-4 1-3 1-6 3-8l1-1h-1v-2l-1-1 1-2c-1-5-1-11 0-16l1-16v-6c-1-3 0-6-2-8l-3-3c-1 0-3 0-3-1-2-1-3-3-3-5l-2-1c1-2 2-4 3-5h0v-7-13l-1-1-6-26c-1-3-2-6-3-8-1-4-3-8-6-11v-1h1 2l1-1c-1 0-2-1-2-2l5 1c1 0 2 0 2-1 2-2 3-8 4-11h0l1 1 1-2v1l2-2c-1 0-1-1-1-2l4-2 1-2c1-1 2-1 2-2l-3-3h0v-1-1h5 2c3-2 6-7 7-10z" class="d"></path><path d="M574 319h0c5 0 7-4 12-5 0 2 0 4 1 6 0 3 0 6-1 9-1-3-1-5-1-8-1-1-1-2-2-2-5 2-7 4-11 7v-2c0-2 1-3 2-5z" class="E"></path><path d="M582 545l1-61v1c0 1 0 1 1 2v57c0 2 1 4 1 5v19c0 4 0 7-1 11v-8-17c0-2 0-6-1-8v-3-2-12-6-4-8-5c0 4 0 11-1 16-1 7 1 15 0 23h0z" class="p"></path><path d="M586 408c1 2 1 3 1 5v2l1 137-1 26v9 3l-3 1c-1-1-1-1-1-3l-1 2v-1l-1 3c-3 1-6 1-9 1-1-2-1-5-1-7v-5c1 3 1 7 1 10h8c0-3 1-7 0-10h0c1-5 1-10 1-14l1-22h0c1-8-1-16 0-23 1-5 1-12 1-16v5 8 4 6 12 2 3c1 2 1 6 1 8v17 8c1 3 1 7 0 11h0 0l2-1h0c1-2 1-5 0-7 0-5 1-10 0-15v-1c1-2 1-6 1-8v-22-85l-1-43z" class="n"></path><path d="M565 340c2-2 1-5 2-8 0-3 2-6 4-8h1v2c-2 6-1 13-1 19l-1 46v125l1 48v17 5c0 2 0 5 1 7 3 0 6 0 9-1 0 1 0 1-1 2-2 1-7 1-8 0-1 0-1 0-2-1l-3 1c-1 0-2-1-3-1-1-1-2-1-3-2l1-1v-3-15l1-30V400v-41-13c0-1 1-2 1-2l1-1-1-2 1-1z" class="d"></path><path d="M563 346c0-1 1-2 1-2v5c-2 3-1 9-1 13v36l1 185c0 2-1 5 0 7l1 1 4 1v-6-10c2 2 1 6 2 10 0 2 0 5 1 7 3 0 6 0 9-1 0 1 0 1-1 2-2 1-7 1-8 0-1 0-1 0-2-1l-3 1c-1 0-2-1-3-1-1-1-2-1-3-2l1-1v-3-15l1-30V400v-41-13z" class="R"></path><path d="M565 340c2-2 1-5 2-8 0-3 2-6 4-8h1v2c-2 6-1 13-1 19l-1 46v125l1 48v17 5c-1-4 0-8-2-10l1-240c-1 1-1 2-1 3l-4 4-1-2 1-1z" class="G"></path><path d="M587 359c2 0 3 1 4 1v1c2 1 7 1 9 3 0 0 1 1 1 2 1 0 2 1 3 2l1 7c1 2 1 3 0 5 0 3-2 9-1 12 1 1 5 2 5 3 1 1 1 3 1 4 2 7 2 14 2 22v5 1l2 1h1l-3 2c-2-1-3-1-5-2-2 1-3 1-4 2l1 1 5 1 1 2c2 3 3 6 4 10l2 9c0 1 0 3 1 4-2 2-4 5-5 7v1h-1c0 2 0 1 1 3 2 1 1 8 1 10h0c-1 6 0 14-1 20l-3 1h-3-2-1l-1 2c-2 1-4 2-5 4l-1 1v1c1 2 1 2 3 3l2-1c3 0 4 0 7 1 1 0 2 0 3 1h2c0 1 1 2 1 2h1 0l1 1v-3l1-3h4 0c2-1 3-1 5-1l11-9v2l1 1h-1l1 1-6 6c-2 2-5 4-6 7v1c-3 7-4 14-9 20l-1 1c1 1 1 1 1 2l-1 1-2 2c-1 2-2 3-2 5 0 1 1 1 1 1 0 3 0 5-1 8 0 3 1 6 0 8h-1c0 2 0 4-1 6l-1 1-3 4c-1 3-4 6-7 7h0c-2 3-8 5-10 7l-1-1v-1h-1v-9l1-26-1-137v-2c0-2 0-3-1-5h0v-17c0-10-1-19 0-29 1-1 1-2 1-3h0z" class="d"></path><path d="M601 428c2-2 4-2 7-1l-1 1-4 1-2-1z" class="X"></path><path d="M608 427h4l2 1h1l-3 2c-2-1-3-1-5-2h0l1-1z" class="m"></path><path d="M614 444l2 9-2 1-1-10h1z" class="a"></path><path d="M616 453c0 1 0 3 1 4-2 2-4 5-5 7v1h-1l-1-2 1-1c2-2 3-5 3-8l2-1z" class="K"></path><path d="M599 437c-1-1-1 0-1-1-1-1-1-3-1-4 1-2 2-3 4-4l2 1c-2 1-3 2-3 3-1 1-1 1 0 2v3h-1z" class="h"></path><path d="M592 510l3-15v7l1-1c0 1 0 0 1 2v1l1-2c1 0 1-1 2-1l-1-1v-1h4l-1 2c-2 1-4 2-5 4l-1 1v1l-4 3z" class="T"></path><path d="M587 359h0v40 11c0 1 1 3 0 5v-2c0-2 0-3-1-5h0v-17c0-10-1-19 0-29 1-1 1-2 1-3z" class="p"></path><path d="M607 428h0c-2 1-3 1-4 2l1 1 5 1 1 2c2 3 3 6 4 10h-1l-1-1c-3-2-4-5-7-7v1c-1 0-2 1-2 1-2 0-3 0-4-1h1v-3c-1-1-1-1 0-2 0-1 1-2 3-3l4-1z" class="H"></path><path d="M600 434c1 1 1 1 3 2 1 0 1 1 2 1-1 0-2 1-2 1-2 0-3 0-4-1h1v-3z" class="a"></path><path d="M604 431l5 1 1 2c2 3 3 6 4 10h-1l-1-1c0-2-1-4-3-6-1-2-3-4-5-4h-2l-1-1v-1h3z" class="t"></path><defs><linearGradient id="ET" x1="607.118" y1="515.613" x2="592.47" y2="512.476" xlink:href="#B"><stop offset="0" stop-color="#acaaab"></stop><stop offset="1" stop-color="#d1d0d0"></stop></linearGradient></defs><path fill="url(#ET)" d="M596 507c1 2 1 2 3 3l2-1 3 2c2 1 3 3 4 5 0 1-1 2-1 4v1c-2-2-3-2-5-2h-4c-3-2-5-6-6-9l4-3z"></path><path d="M616 537c1 1 1 1 1 2l-1 1-2 2c-1 2-2 3-2 5 0 1 1 1 1 1 0 3 0 5-1 8 0 3 1 6 0 8h-1c0 2 0 4-1 6l-1 1-3 4c-1 3-4 6-7 7h0c-2 3-8 5-10 7l-1-1v-1h-1v-9l1-26 1 33 7-3 3-3c2-1 5-4 6-5 1-2-1 0 1-2l2-2c0-1 0-1 1-2v-1c1-2 1-4 1-5 1-2 0-8 0-9 1-1 1-1 1-2-1 0-1 0-1 1v6h-1c0-2 1-3 0-5-1-1-2-1-3-2-3-2-1-4-1-7v-2-1h2c2 1 4-1 6-1h0l3-3z" class="R"></path><path d="M616 537c1 1 1 1 1 2l-1 1-2 2c-1 2-2 3-2 5 0 1 1 1 1 1 0 3 0 5-1 8 0 3 1 6 0 8h-1v-5c1-3 1-8 0-10-1 0-1 0-2 1l-1-1c1-4 5-5 5-9h0l3-3z" class="B"></path><path d="M604 499l1-13c0-6 0-11 1-17l1-4c1-2 2-2 3-2l1 2c0 2 0 1 1 3 2 1 1 8 1 10h0c-1 6 0 14-1 20l-3 1h-3-2z" class="T"></path><path d="M604 499l1-13c0-6 0-11 1-17l1-4c1-2 2-2 3-2l1 2c0 2 0 1 1 3 2 1 1 8 1 10h0-1v-1c1-2 0-6 0-8v-1h0l-1 1h0c1 3 0 8 0 11v1c0-5 0-10-1-15-1 4-2 8-2 12 0 3 0 7-1 11v2l-1-3v11h-2z" class="H"></path><path d="M626 507l11-9v2l1 1h-1l1 1-6 6c-2 2-5 4-6 7v1c-3 7-4 14-9 20l-1 1-3 3h0c-2 0-4 2-6 1h-2c-1 0-1 0-1-1-2-1-1-2-2-4 1-2 2-2 3-3 0-1 1-2 1-3 2-4 2-6 1-9v-1c0-2 1-3 1-4-1-2-2-4-4-5l-3-2c3 0 4 0 7 1 1 0 2 0 3 1h2c0 1 1 2 1 2h1 0l1 1v-3l1-3h4 0c2-1 3-1 5-1z" class="c"></path><path d="M617 508h4c-2 3-1 6-1 8l1 1-2 3v-6h-1c-3 2-3 2-3 5l-1-6h1 0l1 1v-3l1-3z" class="H"></path><path d="M608 510c1 0 2 0 3 1h2c0 1 1 2 1 2l1 6c-2 6-3 12-9 16-1 1-2 1-2 3h0l1 1 3 1h-1v1h-2c-1 0-1 0-1-1-2-1-1-2-2-4 1-2 2-2 3-3 1 0 2 0 3-1 4-4 4-10 5-16-2-2-3-4-5-6z" class="a"></path><path d="M601 509c3 0 4 0 7 1 2 2 3 4 5 6-1 6-1 12-5 16-1 1-2 1-3 1 0-1 1-2 1-3 2-4 2-6 1-9v-1c0-2 1-3 1-4-1-2-2-4-4-5l-3-2z" class="B"></path><path d="M626 507l11-9v2l1 1h-1l1 1-6 6c-2 2-5 4-6 7v1c-3 7-4 14-9 20l-1 1-3 3h0c-2 0-4 2-6 1v-1h1l-3-1c2 0 3-1 4-2s3-1 4-2c3-4 6-10 6-15l2-3-1-1c0-2-1-5 1-8h0c2-1 3-1 5-1z" class="y"></path><path d="M621 508h0c2-1 3-1 5-1-3 3-4 5-5 10l-1-1c0-2-1-5 1-8z" class="P"></path><path d="M637 500l1 1h-1l1 1-6 6-2-2c3-2 5-4 7-6z" class="v"></path><path d="M630 506l2 2c-2 2-5 4-6 7-1 0-1-1-1-2 1-3 2-5 5-7z" class="t"></path><path d="M608 540h0c3-1 7-4 8-6 2-4 4-8 6-13 0-2 1-5 2-7l1-1c0 1 0 2 1 2v1c-3 7-4 14-9 20l-1 1-3 3h0c-2 0-4 2-6 1v-1h1z" class="b"></path><path d="M639 336h1c1-1 1-1 2-3v-2c-1-1 0-2 0-3v2c0 2 1 4 1 5l-1 2h0 1 1v-1l6-6c0 2-1 2-1 4l-1 1v1c0 2-2 5-2 7-2 7-1 16 2 22l3 4c3 4 6 6 11 8h7c2 0 4-1 6-2 2 0 4 0 6-1l2 1-2 1h4v-1h1l1 1v-1h2l-1 1v1h0l-1 1c2-1 3-2 4-4l-1 5v1c-1 3-4 4-6 6l2 1 2-2v1c1 1 1 1 1 2-1 4-2 9-4 13-1 4-10 30-11 30h-2c-1 4-2 7-6 10h-1l-1 1c-2 1-5 2-6 4 0 1 0 1 1 2v1l2 1h1l3 1v1 1l-1 3-3 2c-2 1-4 2-6 1l-3-1c-1 1-1 1-2 0h-3c0-1-1-1-2-1l-3 3c-1 2-2 2-2 4l2 11h1c0 4 0 8-1 11-1 4-5 8-4 11l-1 1-11 9c-2 0-3 0-5 1h0-4l-1 3v3l-1-1h0-1s-1-1-1-2h-2c-1-1-2-1-3-1-3-1-4-1-7-1l-2 1c-2-1-2-1-3-3v-1l1-1c1-2 3-3 5-4l1-2h1 2 3l3-1c1-6 0-14 1-20h0c0-2 1-9-1-10-1-2-1-1-1-3h1v-1c1-2 3-5 5-7-1-1-1-3-1-4l-2-9c-1-4-2-7-4-10l-1-2-5-1-1-1c1-1 2-1 4-2 2 1 3 1 5 2l3-2h-1l-2-1v-1-5c0-8 0-15-2-22 0-1 0-3-1-4 0-1-4-2-5-3-1-3 1-9 1-12l3-2c1-1 2-2 2-4 3-1 4-2 7-3s7-1 10-3c2-3 2-3 2-6l2-6c0-2 0-3 1-5l1 1c1-2 2-4 2-5h1c1 0 2-3 2-4h0c1-2 2-4 2-6l-1-1z" class="k"></path><path d="M601 507l2-1c2-1 4 0 5 0l-1 2-6-1zm20-45l-1-1v-1c-1-1-1-4 0-5 1 1 3 8 3 10-1-1-2-1-4-1l2-2z" class="H"></path><path d="M617 457l1 2c-1 1-2 2-2 3h5l-2 2h-7c1-2 3-5 5-7z" class="T"></path><path d="M608 506l3 1 6 1-1 3v3l-1-1h0-1s-1-1-1-2c-2-2-4-2-6-3l1-2z" class="S"></path><path d="M611 507l6 1-1 3c-2-1-3-3-5-4z" class="P"></path><path d="M645 399l2 3 4 5h0c1 1 2 1 3 2-2 0-4 1-6 1l-3-11z" class="J"></path><path d="M631 482l6-12v11s-1 1-2 1h-1c-1 1-1 2-2 3s0 3-1 4v-7z" class="a"></path><path d="M635 482c1 0 2-1 2-1l-1 6c-2 6-4 11-10 14l-1 1-1-1c1-1 1-1 2-1 2-1 6-5 6-7 1-2 0-2-1-4 1-1 0-3 1-4s1-2 2-3h1z" class="b"></path><path d="M635 482c1 0 2-1 2-1l-1 6c-1 0-1-1-2-2l1-3z" class="K"></path><path d="M630 470c-1-5 0-9 1-14 1-3 3-6 6-7 0-1 1 0 1 0l1 2c-5 5-8 12-9 19z" class="D"></path><path d="M619 464c2 0 3 0 4 1l6 7 2 3-1 1h-1 0c-1-1-2-1-2-2l-4-4c-1-2-3-3-5-3-2-2-4 0-6-2v-1h7z" class="O"></path><path d="M639 451c0 8-5 17-8 24l-2-3c1-1 1-1 1-2 1-7 4-14 9-19z" class="F"></path><path d="M604 499h2 3v1 1h-2c5 2 12 2 17 0l1 1c-3 1-7 2-10 2-6-1-14-4-17 3v1c2 0 2 0 3-1h0l6 1c2 1 4 1 6 3h-2c-1-1-2-1-3-1-3-1-4-1-7-1l-2 1c-2-1-2-1-3-3v-1l1-1c1-2 3-3 5-4l1-2h1z" class="m"></path><path d="M604 499h2 3v1 1h-2-5l1-2h1zm5-67c3 1 6 3 10 4 4 2 9 1 13 0v2c-2 3-4 5-6 8-3-1-5-5-8-6-2-3-4-6-8-6l-1-2z" class="B"></path><path d="M632 436c4-2 7-5 10-8 0 1-1 2-2 3-2 3-6 6-8 10-4 4-6 11-6 17v4c-2-8-4-15-8-22 3 1 5 5 8 6 2-3 4-5 6-8v-2z" class="D"></path><path d="M629 476h1c0 2 0 4 1 6v7c1 2 2 2 1 4 0 2-4 6-6 7-1 0-1 0-2 1-5 2-12 2-17 0h2v-1c2 0 5 1 7 1 2-1 5-1 7-2l1-2h2c1-2 1-2 1-4h1c0-3 0-4 1-6v-11z" class="I"></path><path d="M629 476h1c0 2 0 4 1 6v7c1 2 2 2 1 4 0 2-4 6-6 7-1 0-1 0-2 1 2-4 7-5 7-9h-1l-1 1h0v-6-11z" class="i"></path><path d="M623 470l4 4c0 1 1 1 2 2h0v11c-1 2-1 3-1 6h-1c0 2 0 2-1 4h-2l-1 2v-3-5-21z" class="g"></path><defs><linearGradient id="EU" x1="623.119" y1="410.243" x2="632.261" y2="426.748" xlink:href="#B"><stop offset="0" stop-color="#251310"></stop><stop offset="1" stop-color="#443837"></stop></linearGradient></defs><path fill="url(#EU)" d="M638 406v-4c0 1 1 1 1 1l1 3h1l1-2c1 8 0 14-5 20-4 4-9 7-15 8-4 0-6-1-10-2l3-2 4 1c2 0 4 0 6-1h1c2-1 3-2 4-3 2-1 3-2 4-3 4-5 4-10 4-16z"></path><path d="M611 465h1c2 2 4 0 6 2 2 0 4 1 5 3v21 5 3c-2 1-5 1-7 2-2 0-5-1-7-1v-1l3-1c1-6 0-14 1-20h0c0-2 1-9-1-10-1-2-1-1-1-3z" class="G"></path><path d="M611 465h1c2 2 4 0 6 2-1 0-3-1-4 0-1 3 0 8 0 11-1 6-1 14-1 20l1 1c1-1 3-1 3-1l1-1h2c1 0 1-1 2-1l1-5h0v5 3c-2 1-5 1-7 2-2 0-5-1-7-1v-1l3-1c1-6 0-14 1-20h0c0-2 1-9-1-10-1-2-1-1-1-3z" class="j"></path><path d="M623 496v3c-2 1-5 1-7 2 1-1 1-2 2-2 2-1 3-1 5-3z" class="C"></path><path d="M614 478c0-3-1-8 0-11 1-1 3 0 4 0 0 3 1 6 0 10s0 9-1 14c0 2 1 4 0 6h-3v-6-13z" class="L"></path><path d="M613 377h0c3-2 6-1 9-1l5 2 4 2 4 5c1 2 2 4 3 5l1 3c1 4 3 7 3 11l-1 2h-1l-1-3s-1 0-1-1v4c0 6 0 11-4 16-1 1-2 2-4 3-1 1-2 2-4 3h-1c-2 1-4 1-6 1l-4-1h-1l-2-1v-1-5c0-8 0-15-2-22 0-1 0-3-1-4 0-1-4-2-5-3-1-3 1-9 1-12l3-2v1l1 1c2 0 2 0 4-2v-1z" class="H"></path><path d="M605 380l3-2v1c-1 1-1 1-1 3l2 2v4c1 1 3 1 4 1 2-1 4 0 6 0 1 2 1 8 0 11 0-3 0-7-1-10-2-1-2-1-4-1l-1 1h1v5 15l-1 12c0 2 0 4 1 6l-2-1v-1-5c0-8 0-15-2-22 0-1 0-3-1-4 0-1-4-2-5-3-1-3 1-9 1-12z" class="L"></path><path d="M607 382l2 2v4c1 1 3 1 4 1h-6v-7z" class="G"></path><defs><linearGradient id="EV" x1="630.011" y1="396.499" x2="610.506" y2="406.917" xlink:href="#B"><stop offset="0" stop-color="#959494"></stop><stop offset="1" stop-color="#bebdbd"></stop></linearGradient></defs><path fill="url(#EV)" d="M615 377c3 1 4 1 6 2 2 2 3 3 3 6v7l1 36c-2 1-4 1-6 1v-29c1-3 1-9 0-11-2 0-4-1-6 0-1 0-3 0-4-1v-4l-2-2c0-2 0-2 1-3l1 1c2 0 2 0 4-2l2-1z"></path><path d="M613 377h0c3-2 6-1 9-1l5 2 4 2 4 5c1 2 2 4 3 5l1 3c1 4 3 7 3 11l-1 2h-1l-1-3s-1 0-1-1v4c0 6 0 11-4 16-1 1-2 2-4 3-1 1-2 2-4 3h-1l-1-36v-7c0-3-1-4-3-6-2-1-3-1-6-2l-2 1v-1z" class="M"></path><path d="M635 395c2 4 2 7 3 11 0 6 0 11-4 16-1 1-2 2-4 3 2-3 3-5 4-8v-6l1-16z" class="e"></path><path d="M613 377h0c3-2 6-1 9-1l5 2 4 2 4 5c1 2 2 4 3 5l1 3c1 4 3 7 3 11l-1 2h-1l-1-3s-1 0-1-1v4c-1-4-1-7-3-11-1-5-3-9-7-12s-7-6-13-6l-2 1v-1z" class="t"></path><path d="M638 390l1 3c1 4 3 7 3 11l-1 2h-1l-1-3c0-2-1-4-1-6s-1-3-1-4 1-2 1-3z" class="s"></path><path d="M624 392c2 2 4 3 5 5 3 7 5 12 5 20-1 3-2 5-4 8-1 1-2 2-4 3h-1l-1-36z" class="j"></path><path d="M624 392c2 2 4 3 5 5l-3-2v33h-1l-1-36z" class="I"></path><path d="M686 387l2-2v1c1 1 1 1 1 2-1 4-2 9-4 13-1 4-10 30-11 30h-2c-1 4-2 7-6 10h-1l-1 1c-2 1-5 2-6 4 0 1 0 1 1 2v1l2 1h1l3 1v1 1l-1 3-3 2c-2 1-4 2-6 1l-3-1c-1 1-1 1-2 0h-3c0-1-1-1-2-1v-3c-2-1-3-1-4-2 0-2 0-4 1-6 0-7 11-21 16-27h0v-1c0-3 5-8 7-10v-2l1-1c1-2 2-3 3-4-4-1-6 4-9 4h-1l8-6v-2l2-3c2-2 6-4 9-6 1 0 4-2 6-2l2 1z" class="s"></path><path d="M651 442c2-2 3-3 4-5-1 3-3 7-3 10l-2 4s1 0 2 1c1 0 2 0 3 1-2 0-4 0-6-1h-2c0-3 3-7 4-10h0zm14-34c1-1 1-2 3-3l8-6c0 1 0 2-1 3-4 4-8 7-11 11l-6 6h0v-1c0-3 5-8 7-10z" class="N"></path><path d="M651 442c0-3 2-5 3-7 2-4 5-8 8-11h1c1 0 1-1 2-2s0-1 1-1l4-4h1 0v2h0l-3 3c-4 0-9 8-11 11l-2 4c-1 2-2 3-4 5z" class="r"></path><path d="M684 393c2-3 3-4 4-7 1 1 1 1 1 2-1 4-2 9-4 13h-1c0-1 1-1 1-2v-1l1-1v-2-1c-1 2-5 6-5 7l-1 1-1-1v1h-3-1c1-1 1-2 1-3l8-6z" class="b"></path><path d="M676 399l8-6c0 3-2 5-5 7l-3 2h-1c1-1 1-2 1-3z" class="e"></path><path d="M642 446v3 3c2 1 1 0 3 0 1 0 2 2 3 1h1v-1c2 1 4 1 6 1h0 1l2 1c2 0 4-1 5-2l-1-2 3 1v1 1l-1 3-3 2c-2 1-4 2-6 1l-3-1c-1 1-1 1-2 0h-3c0-1-1-1-2-1v-3c-2-1-3-1-4-2 0-2 0-4 1-6z" class="n"></path><path d="M642 446v3 3c2 1 1 0 3 0 1 0 2 2 3 1h1v-1c2 1 4 1 6 1h0 1l2 1c2 0 4-1 5-2l-1-2 3 1v1 1c-3 0-6 3-9 3s-7-2-11-2h0c-2-1-3-1-4-2 0-2 0-4 1-6z" class="K"></path><defs><linearGradient id="EW" x1="680.838" y1="391.25" x2="672.197" y2="399.287" xlink:href="#B"><stop offset="0" stop-color="#b2b0ae"></stop><stop offset="1" stop-color="#d1d0d0"></stop></linearGradient></defs><path fill="url(#EW)" d="M686 387l2-2v1c-1 3-2 4-4 7l-8 6-8 6c-2 1-2 2-3 3v-2l1-1c1-2 2-3 3-4-4-1-6 4-9 4h-1l8-6v-2l2-3c2-2 6-4 9-6 1 0 4-2 6-2l2 1z"></path><defs><linearGradient id="EX" x1="680.735" y1="388.372" x2="667.148" y2="396.941" xlink:href="#B"><stop offset="0" stop-color="#656364"></stop><stop offset="1" stop-color="#8c8787"></stop></linearGradient></defs><path fill="url(#EX)" d="M678 388c1 0 4-2 6-2l2 1c-6 5-12 9-19 12v-2l2-3c2-2 6-4 9-6z"></path><path d="M671 419l4-4c0 3 1 5 0 7 0 3-2 6-3 9-1 4-2 7-6 10h-1l-1 1c-2 1-5 2-6 4 0 1 0 1 1 2v1l2 1h1l1 2c-1 1-3 2-5 2l-2-1h-1 0c-1-1-2-1-3-1-1-1-2-1-2-1l2-4c0-3 2-7 3-10l2-4c2-3 7-11 11-11l3-3h0z" class="J"></path><g class="M"><path d="M655 453c-1-1-2-1-3-1-1-1-2-1-2-1l2-4 1 2c1 1 1 1 3 1v1h5v-1h1l1 2c-1 1-3 2-5 2l-2-1h-1 0z"></path><path d="M655 437l2-4c2-3 7-11 11-11v1l-9 12c-2 4-4 8-7 12 0-3 2-7 3-10z"></path></g><path d="M665 430h1c0 4-4 7-3 10 0 0 1 1 1 2-2 1-5 2-6 4 0 1 0 1 1 2v1l-1 1-2-1v-6c1-2 3-4 4-6 2-2 4-5 5-7z" class="g"></path><defs><linearGradient id="EY" x1="672.501" y1="428.304" x2="666.142" y2="438.783" xlink:href="#B"><stop offset="0" stop-color="#676566"></stop><stop offset="1" stop-color="#7e7d7e"></stop></linearGradient></defs><path fill="url(#EY)" d="M671 419l4-4c0 3 1 5 0 7 0 3-2 6-3 9-1 4-2 7-6 10h-1l-1 1c0-1-1-2-1-2-1-3 3-6 3-10h-1 0l2-2h1c1-2 0-3 1-5v-1l-1 1v-1l3-3h0z"></path><defs><linearGradient id="EZ" x1="671.306" y1="431.521" x2="661.251" y2="435.139" xlink:href="#B"><stop offset="0" stop-color="#827c83"></stop><stop offset="1" stop-color="#939593"></stop></linearGradient></defs><path fill="url(#EZ)" d="M671 419l1 1v4c0 2-1 4-2 5-2 4-3 7-5 11v1l-1 1c0-1-1-2-1-2-1-3 3-6 3-10h-1 0l2-2h1c1-2 0-3 1-5v-1l-1 1v-1l3-3z"></path><defs><linearGradient id="Ea" x1="681.246" y1="354.942" x2="638.335" y2="370.859" xlink:href="#B"><stop offset="0" stop-color="#cbc9c9"></stop><stop offset="1" stop-color="#fafafa"></stop></linearGradient></defs><path fill="url(#Ea)" d="M639 336h1c1-1 1-1 2-3v-2c-1-1 0-2 0-3v2c0 2 1 4 1 5l-1 2h0 1 1v-1l6-6c0 2-1 2-1 4l-1 1v1c0 2-2 5-2 7-2 7-1 16 2 22l3 4c3 4 6 6 11 8h7c2 0 4-1 6-2 2 0 4 0 6-1l2 1-2 1h4v-1h1l1 1v-1h2l-1 1v1h0l-1 1c2-1 3-2 4-4l-1 5v1c-1 3-4 4-6 6-2 0-5 2-6 2-3 2-7 4-9 6l-2 3v2l-8 6-5 4c-1-1-2-1-3-2h0l-4-5-2-3h-1c-1-2-1-4-3-6h-2l-1-3c-1-1-2-3-3-5l-4-5-4-2-5-2c-3 0-6-1-9 1h0v1c-2 2-2 2-4 2l-1-1v-1c1-1 2-2 2-4 3-1 4-2 7-3s7-1 10-3c2-3 2-3 2-6l2-6c0-2 0-3 1-5l1 1c1-2 2-4 2-5h1c1 0 2-3 2-4h0c1-2 2-4 2-6l-1-1z"></path><path d="M640 361h1c1 3 1 6 2 9l1 1-1-1c-1-3-3-5-3-9z" class="d"></path><path d="M652 382l9 3h-1 0 0c2 0 3 0 4 1h-1c-5 0-8-1-11-4z" class="T"></path><path d="M648 369h3c3 4 6 6 11 8h-1c-1 0-2 0-3-1h-1 0l2 2c1 0 1 1 2 1-6-1-9-5-13-10z" class="b"></path><path d="M648 335v1c0 2-2 5-2 7-2 7-1 16 2 22l3 4h-3c-2-4-3-7-4-12-2-8 0-15 4-22zm27 40c2 0 4 0 6-1l2 1-2 1c-6 2-14 5-20 3-1 0-1-1-2-1l-2-2h0 1c1 1 2 1 3 1h1 7c2 0 4-1 6-2z" class="m"></path><path d="M633 366c1-1 1-1 3-2v-4-1c1 11 3 21 11 29 2 2 4 3 6 4l-1 1-2-1-1-1c-7-4-10-9-13-15l-1-3-2-6v-1z" class="K"></path><defs><linearGradient id="Eb" x1="635.34" y1="376.414" x2="637.859" y2="386.409" xlink:href="#B"><stop offset="0" stop-color="#757071"></stop><stop offset="1" stop-color="#8c898a"></stop></linearGradient></defs><path fill="url(#Eb)" d="M634 377l2-1c3 6 6 11 13 15l1 1h-2l-1 1h0l-3-1-1 1 1 2v4c-1-2-1-4-3-6h-2l-1-3c-1-1-2-3-3-5 1-1 1-1 1-2l-2-6z"></path><path d="M639 336h1c1-1 1-1 2-3v-2c-1-1 0-2 0-3v2c0 2 1 4 1 5l-1 2h0 1 1v-1c-4 7-6 15-8 23v1 4c-2 1-2 1-3 2l-1-1s-1 1-1 2h-2l-2 1c2-3 2-3 2-6l2-6c0-2 0-3 1-5l1 1c1-2 2-4 2-5h1c1 0 2-3 2-4h0c1-2 2-4 2-6l-1-1z" class="h"></path><path d="M632 351l1 1c1-2 2-4 2-5h1c-1 5-2 9-3 15 0 0-1 2-1 3 0 0-1 1-1 2h-2l-2 1c2-3 2-3 2-6l2-6c0-2 0-3 1-5z" class="N"></path><path d="M629 362l2-6c0 4 0 7-2 11l-2 1c2-3 2-3 2-6z" class="Q"></path><path d="M678 388c-2-1-5 1-8 1-4 1-11 2-15 0-7-3-9-11-11-17h0c2 4 5 7 8 10s6 4 11 4h1c8 0 15-4 22-7l1-1c2-1 3-2 4-4l-1 5v1c-1 3-4 4-6 6-2 0-5 2-6 2z" class="F"></path><path d="M632 365l1 1v1l2 6 1 3-2 1 2 6c0 1 0 1-1 2l-4-5-4-2-5-2c-3 0-6-1-9 1h0v1c-2 2-2 2-4 2l-1-1v-1c1-1 2-2 2-4 3-1 4-2 7-3s7-1 10-3l2-1h2c0-1 1-2 1-2z" class="m"></path><path d="M632 365l1 1v1l-1 2-3 2h-1v-1c1-1 2-1 3-3 0-1 1-2 1-2z" class="v"></path><path d="M613 377l-1-1h1c2-3 5-3 9-3v1l1 2h-1c-3 0-6-1-9 1h0z" class="G"></path><path d="M629 371c1 1 1 1 1 2 0 2 2 5 1 7l-4-2-5-2h1l-1-2v-1c2 0 4 0 6-2h1z" class="F"></path><path d="M622 374c3-1 3 0 6 2v2h-1l-5-2h1l-1-2z" class="C"></path><path d="M633 367l2 6 1 3-2 1 2 6c0 1 0 1-1 2l-4-5c1-2-1-5-1-7 0-1 0-1-1-2l3-2 1-2z" class="Q"></path><path d="M633 367l2 6 1 3-2 1 2 6h-1c-4-4-1-10-3-14l1-2z" class="Z"></path><path d="M635 373l1 3-2 1c0-1 0-2 1-4z" class="f"></path><path d="M644 399v-4l-1-2 1-1 3 1h0l1-1h2l2 1 1-1c1 0 2 1 4 1h2l1 1c1 0 2 0 3-1h2v1l1-1c1 0 1 0 2 1h1l-2 3v2l-8 6-5 4c-1-1-2-1-3-2h0l-4-5-2-3h-1z" class="n"></path><path d="M653 398s-1 0-1-1c-1 0-1-1-2-2h1l5 1-3 1h0v1z" class="p"></path><path d="M663 393h2v1l1-1c1 0 1 0 2 1-1 1-2 2-3 2-1 1-2 1-4 2h-2c-2 1-3 1-6 0v-1h0l3-1c3 0 5-1 7-3z" class="c"></path><path d="M668 394h1l-2 3v2l-8 6-5 4c-1-1-2-1-3-2h3 0c2-1 4-3 6-5s4-3 5-6c1 0 2-1 3-2z" class="I"></path><path d="M563 346v13 41 142l-1 30v15l-1-2c-2-3-2-8-2-12v-4c-1 1-1 1-1 2v3l-1-3v-2l-2-2c-1-1-3 0-4 1-3 0-6 3-9 5 0 1-1 1-2 2-1 0-1 1-2 1-4 0-6 1-9 4-1 1-1 2-2 3 0 1 0 2 1 2l1 1 2 2h-6c-1 1-2 1-4 1v-1h-1v-1c2-10-2-20 3-30l1-1v-3c1-1 1-3 1-4-2-1-3 0-4 0l-6 3c-3 2-5 4-7 7l-1 1c-1-1-1-2-1-3v-1c-1-1-1-4-1-5l1-13v-21-27l1-1h0v3c1-6 0-12 1-18 1-1 1-1 2-1l-1-5c1-2 1-4 2-6l1 8v-10c0 3 1 6 2 9l4-25v-1l1 1c1-1 2-8 2-10-1-1-1-3-1-5 0-1 1-3 1-4 1-2 1-4 2-6 0 1 0 3 1 4 2-2 4-3 6-4l-1-1v-1c1-1 1-2 1-3v-1c0-3 1-6 0-8 0-2 0-3-1-5v-1l1-1 3 1 2-1 1-1c-2-5-3-9-2-14 0-1 1-2 2-2 1-1 3-1 4-1 0-1-1-2-1-3 0-2 0-4 1-6 2-3 5-2 8-2v-1l1-3-1-2 1-6 1-5c2 0 4 0 5 1l6-6 2-1z" class="d"></path><path d="M543 460c1-6 1-13 1-20v-44l-1-10c0-2 0-4 1-6h0v-1h1v-1l1 2-2 93c0-1 0-1-1-2 0 0 1-2 1-3l-1-8z" class="L"></path><defs><linearGradient id="Ec" x1="568.774" y1="533.802" x2="526.452" y2="517.914" xlink:href="#B"><stop offset="0" stop-color="#bcbab9"></stop><stop offset="1" stop-color="#ddddd9"></stop></linearGradient></defs><path fill="url(#Ec)" d="M543 460l1 8c0 1-1 3-1 3 1 1 1 1 1 2l1 24-1 43c0 8 0 18 1 25l4-1c2-1 5 0 7 1s2 2 3 4c-1 1-1 1-1 2v3l-1-3v-2l-2-2c-1-1-3 0-4 1-3 0-6 3-9 5 0 1-1 1-2 2-1 0-1 1-2 1-4 0-6 1-9 4-1 1-1 2-2 3 0 1 0 2 1 2l1 1 2 2h-6c-1 1-2 1-4 1v-1h-1v-1c2-10-2-20 3-30l1-1v5c0 3 1 7 2 10h-1c0-1-1-2-1-3-1 2 0 4 0 6v5 1c1 0 2 0 3-1l5-4 3-2c3-1 4-3 5-5s1-5 1-7l1-12 1-71v-18z"></path><path d="M520 587c2-10-2-20 3-30v9c-1 3-2 13 0 16h0 1 2c3-3 5-6 10-8l2-1c1 0 1 0 2-1h1v1h1c0 1-1 1-2 2-1 0-1 1-2 1-4 0-6 1-9 4-1 1-1 2-2 3 0 1 0 2 1 2l1 1 2 2h-6c-1 1-2 1-4 1v-1h-1v-1z" class="G"></path><path d="M521 588h0c2-1 3-2 6-1h0l2-1 2 2h-6c-1 1-2 1-4 1v-1z" class="L"></path><defs><linearGradient id="Ed" x1="549.65" y1="464.41" x2="533.411" y2="464.004" xlink:href="#B"><stop offset="0" stop-color="#d3d3d1"></stop><stop offset="1" stop-color="#fafbf7"></stop></linearGradient></defs><path fill="url(#Ed)" d="M550 352c2 0 4 0 5 1l-1 1h0v2l-1 11v5l-2 2-5 6-1-2v1h-1v1h0c-1 2-1 4-1 6l1 10v44c0 7 0 14-1 20v18l-1 71-1 12c0 2 0 5-1 7s-2 4-5 5l-3 2-5 4c0-1 0-2 1-3h0l1-1c0-1 1-1 1-2l1-1v-1-23c1-4 1-8 2-11v-16c1-12 2-96-1-102h-1-1l-1-1v-1c1-1 1-2 1-3v-1c0-3 1-6 0-8 0-2 0-3-1-5v-1l1-1 3 1 2-1 1-1c-2-5-3-9-2-14 0-1 1-2 2-2 1-1 3-1 4-1 0-1-1-2-1-3 0-2 0-4 1-6 2-3 5-2 8-2v-1l1-3-1-2 1-6 1-5z"></path><path d="M549 357c2-1 4-1 5-3v2l-2 2-1 1v-1h-1c-1 1 0 0 0 1-1 2-1 3-2 4l1-6z" class="E"></path><path d="M550 352c2 0 4 0 5 1l-1 1h0c-1 2-3 2-5 3l1-5z" class="S"></path><path d="M549 365c0 1 1 2 0 3-1 2-2 2-4 2h-3c-1 2-2 2-2 4s0 3 1 4l2-1v1h0c-1 1-2 2-2 4v1c-1 1-2 2-3 2-1 4-2 8-2 12h0c-2-5-3-9-2-14 0-1 1-2 2-2 1-1 3-1 4-1 0-1-1-2-1-3 0-2 0-4 1-6 2-3 5-2 8-2v-1l1-3z" class="P"></path><path d="M535 398c-1 5-1 11 0 16 4 17 2 37 1 54l-1 41v20c0 3 1 7 1 10 0 6 1 12 0 18v4 10c1-1 3-3 4-3-1 2-2 4-5 5l-3 2-5 4c0-1 0-2 1-3h0l1-1c0-1 1-1 1-2l1-1v-1-23c1-4 1-8 2-11v-16c1-12 2-96-1-102h-1-1l-1-1v-1c1-1 1-2 1-3v-1c0-3 1-6 0-8 0-2 0-3-1-5v-1l1-1 3 1 2-1z" class="T"></path><path d="M535 398c-1 5-1 11 0 16h-1c-1-3-2-12-1-15l2-1z" class="L"></path><path d="M530 419h1 1c3 6 2 90 1 102v16c-1 3-1 7-2 11v23 1l-1 1c0 1-1 1-1 2l-1 1h0c-1 1-1 2-1 3-1 1-2 1-3 1v-1-5c0-2-1-4 0-6 0 1 1 2 1 3h1c-1-3-2-7-2-10v-5-3c1-1 1-3 1-4-2-1-3 0-4 0l-6 3c-3 2-5 4-7 7l-1 1c-1-1-1-2-1-3v-1c-1-1-1-4-1-5l1-13v-21-27l1-1h0v3c1-6 0-12 1-18 1-1 1-1 2-1l-1-5c1-2 1-4 2-6l1 8v-10c0 3 1 6 2 9l4-25v-1l1 1c1-1 2-8 2-10-1-1-1-3-1-5 0-1 1-3 1-4 1-2 1-4 2-6 0 1 0 3 1 4 2-2 4-3 6-4z" class="d"></path><path d="M527 422c-1 3-2 7-2 10s-1 9-3 11c0-7 2-13 3-19l2-2z" class="c"></path><path d="M530 419h1c-1 1-2 2-4 3l-2 2c-1 6-3 12-3 19l-2 28c-1 1 0 12 0 14-1-7 1-15-1-21v-2h1l1-18c-2 5-3 11-3 16 0 2-1 3-1 5h-1c0-2 1-4 1-6l2-15c1-1 2-8 2-10-1-1-1-3-1-5 0-1 1-3 1-4 1-2 1-4 2-6 0 1 0 3 1 4 2-2 4-3 6-4z" class="H"></path><path d="M530 419h1c-1 1-2 2-4 3l-2 2c-3 3-3 6-4 10-1-1-1-3-1-5 0-1 1-3 1-4 1-2 1-4 2-6 0 1 0 3 1 4 2-2 4-3 6-4z" class="r"></path><path d="M519 464c2 6 0 14 1 21 0-2-1-13 0-14l1 78-6 3h0l1-11 1 2 1 1v5h0v-1l1-1v-6c-1-1-1-2-1-3 1-5 1-9 1-14v-23-21-10c-1-1-1-2-1-3 1-1 1-2 1-3z" class="p"></path><path d="M518 444v-1l1 1-2 15c0 2-1 4-1 6h1c0-2 1-3 1-5 0-5 1-11 3-16l-1 18h-1v2c0 1 0 2-1 3 0 1 0 2 1 3v10 21 23c0 5 0 9-1 14 0 1 0 2 1 3v6l-1 1v1h0v-5l-1-1-1-2-1 11h0c-3 2-5 4-7 7l-1 1c-1-1-1-2-1-3v-1c-1-1-1-4-1-5l1-13v-21-27l1-1h0v3c1-6 0-12 1-18 1-1 1-1 2-1l-1-5c1-2 1-4 2-6l1 8v-10c0 3 1 6 2 9l4-25z" class="d"></path><path d="M506 490l1-1h0v3c0 9-1 64 1 67l-1 1c-1-1-1-2-1-3v-1c-1-1-1-4-1-5l1-13v-21-27z" class="n"></path><path d="M518 444v-1l1 1-2 15c0 2-1 4-1 6l-4 27-2-19-1-5c1-2 1-4 2-6l1 8v-10c0 3 1 6 2 9l4-25z" class="t"></path><path d="M512 460c0 3 1 6 2 9l-1 10c-1-3-1-6-1-9v-10z" class="d"></path><path d="M521 195c2-3 4-8 8-9 3-1 6-1 8-1l1 1c8 1 16 7 22 12 1-1 1-2 1-3 2 2 3 4 6 4-1 2-1 2-1 3l-1 1c1 0 2 0 2-1 2 0 3-1 4-3h0c0-2-2-3-4-4h0v-3h1l1-1h1c2 2 4 2 6 2l3-2c1-1 3-1 5-2h1c1-2 2-2 5-2h0l2 2h0c3-1 5-2 8-2h0c2 0 4 1 6 0l1 1c2 0 3 0 4 1 1 2 2 2 5 3h0v-1l-1-2 1-1 3 3c2 4 4 9 8 12l2 1h0l1 1 1 1h0c1 0 4 1 5 2h-1c1 2 2 2 4 3l1-1 1 2c-1 0-2 1-4 2 0 0-1 0-2 1 0 1 0 1 1 3h-3c1 3 1 5 1 8 0 2-1 3-2 4h-2 0c1 1 2 1 3 1s1 0 2-1h1c2 0 4 0 7-1-2 1-4 2-5 4l1 1c-9 4-12 12-16 21-2 6-4 12-2 19l3 9c3 5 6 11 10 16 1 1 2 4 3 4-3-8-6-18-2-27 3-5 7-8 12-10 8-3 18-2 26 2 4 2 8 5 10 9-1-1-2-1-3-2v2c1 1 1 3 1 5s0 5-1 7c-2 8-8 17-13 24h0c-1-1 0-3-1-4l-1 1v1c0 3 0 5-2 8h-1c-1-1-2-1-4 0l-8 11-6 6v1h-1-1 0l1-2c0-1-1-3-1-5v-2c0 1-1 2 0 3v2c-1 2-1 2-2 3h-1l1 1c0 2-1 4-2 6h0c0 1-1 4-2 4h-1c0 1-1 3-2 5l-1-1c-1 2-1 3-1 5l-2 6c0 3 0 3-2 6-3 2-7 2-10 3s-4 2-7 3c0 2-1 3-2 4l-3 2c1-2 1-3 0-5l-1-7c-1-1-2-2-3-2 0-1-1-2-1-2-2-2-7-2-9-3v-1c-1 0-2-1-4-1h0c0 1 0 2-1 3v-8c-1-2 0-5 0-7 0-6-1-12 0-18 1-3 1-6 1-9-1-2-1-4-1-6-5 1-7 5-12 5h0c-1 2-2 3-2 5h-1c-2 2-4 5-4 8-1 3 0 6-2 8l-1 1 1 2-1 1s-1 1-1 2l-2 1-6 6c-1-1-3-1-5-1l-1 5-1 6 1 2-1 3v1c-3 0-6-1-8 2-1 2-1 4-1 6 0 1 1 2 1 3-1 0-3 0-4 1-1 0-2 1-2 2-1 5 0 9 2 14l-1 1-2 1-3-1-1 1v1c1 2 1 3 1 5 1 2 0 5 0 8v1c0 1 0 2-1 3v1l1 1c-2 1-4 2-6 4-1-1-1-3-1-4-1 2-1 4-2 6 0 1-1 3-1 4 0 2 0 4 1 5 0 2-1 9-2 10l-1-1v1l-4 25c-1-3-2-6-2-9v10l-1-8-4-27c0-2 0-5-1-7 1-2 0-4 0-6 0-5 0-10 1-14v-2c-1-2 0-4-1-6l-1-4v-6-5l-1-1c1 0 1-1 2-2 0-1 1-1 1-2 0-2 1-2 2-4 1-3 1-6 3-8l1-1h-1v-2l-1-1 1-2c-1-5-1-11 0-16l1-16v-6c-1-3 0-6-2-8l-3-3c-1 0-3 0-3-1-2-1-3-3-3-5l-2-1c1-2 2-4 3-5h0v-7-13l-1-1-6-26c-1-3-2-6-3-8-1-4-3-8-6-11v-1h1 2l1-1c-1 0-2-1-2-2l5 1c1 0 2 0 2-1 2-2 3-8 4-11h0l1 1 1-2v1l2-2c-1 0-1-1-1-2l4-2 1-2c1-1 2-1 2-2l-3-3h0v-1-1h5 2c3-2 6-7 7-10z" class="AA"></path><path d="M534 296h1c-1 2-2 3-3 4v-1c0-1 1-2 2-3z" class="d"></path><path d="M533 272h2l-1 4-3 3 2-7z" class="G"></path><path d="M560 198c1-1 1-2 1-3 2 2 3 4 6 4-1 2-1 2-1 3-1 0-1 0-2-1s-2-2-4-3z" class="E"></path><path d="M578 309s0-1-1-1v-1c0-1-1-3 0-4l1 1 1-4h-1l3-3v3c-1 1-2 3-2 5s0 3-1 4zm-15-12c4 2 6 0 9-1-2 1-3 2-5 3 0 1 0 1 1 1h-1l-4 4v-1c0-1 0-1 1-1l1-2h-2v-3z" class="T"></path><path d="M514 249l3-18c3 6 0 12-1 17h-1l-1 1z" class="k"></path><path d="M551 287l2-1 2 2c-1 0-2 1-3 1l-1 1c-3 1-6 1-9 0l1-1 8-2z" class="d"></path><path d="M602 195h4 2 0c0 1-1 1-1 2h-1c-3 0-6 0-9 1-3 0-7 2-10 2 5-2 9-4 15-5z" class="M"></path><path d="M606 195h2 0c0 1-1 1-1 2h-1-3c0-1 2-2 3-2z" class="U"></path><path d="M567 202c2 0 3-1 4-3l1 2-1 1h0l-12 8-1 1v-2h1l-1-1c1-1 3-2 5-3l2-2c1 0 2 0 2-1z" class="Z"></path><path d="M533 242h0c1-2 3-5 4-8h1l1-1h1c-2 3-3 5-4 8l-6 13h0l3-12z" class="G"></path><path d="M531 279l3-3c-1 4-1 8 0 11 1 1 2 2 4 2h2 2 1l-1 1c-3 1-5 2-8 1-2-4-2-8-3-12z" class="D"></path><path d="M585 189c1-2 2-2 5-2h0l2 2c-3 0-3 1-5 3v1l-4 3c-1 1-2 1-4 2 1-2 2-3 3-4 0-1 2-4 3-5z" class="T"></path><path d="M544 228c3-3 5-8 9-10 0 3-3 4-4 6h-1l-1 1c0 1 0 1-1 2 0 1-1 2-2 3 0 1-1 2-2 4l-3 5v-2c-1 1-1 2-2 3l-1 1c1-3 2-5 4-8l4-5z" class="d"></path><path d="M540 252h2c-3 7-5 13-7 20h-2c1-7 4-14 7-20z" class="E"></path><path d="M519 207l1 1s0 1 1 2v1c3 6 3 14 10 17 2 1 4 1 5 1l-2 2v-1c-11-2-10-13-16-19l-2-2c1 0 2-1 3-2z" class="H"></path><path d="M579 305v5l1 1c-1 1-2 1-3 1-4 1-8 3-9 7l-2 3v1l-1-2c1-3 2-5 3-7 3-3 6-4 10-5 1-1 1-2 1-4z" class="O"></path><path d="M558 211l1-1c-1 3-4 5-6 8-4 2-6 7-9 10v-3c-2 1-1 3-4 3l2-2v-1-1h1c2-1 3-3 5-4l10-9z" class="S"></path><path d="M583 267v2c-1 3-6 7-8 9-4 4-10 6-15 8l-2-2 11-5c6-3 10-8 14-12z" class="E"></path><path d="M553 331c3-3 5-6 7-9v3l-1 1c0 1 0 1-1 2v1h1c0-1 1-2 2-3s2-1 3-2v1l-1 1c-1 1-1 1-1 3-1 3-4 5-6 8-1 0-2-1-4-1 0-1 0-2 1-4l-1 1 1-2z" class="p"></path><path d="M581 297c4-2 7-3 11-4l1 1c-1 0-3 0-4 1h1 2c3-1 7 0 9 0l-10 1 2 2 1 1-3-1c-4 1-6 1-8 4-1 2-2 4-2 6v1h0l-1 2-1-1v-5c0-2 1-4 2-5v-3z" class="n"></path><path d="M589 296h2l2 2 1 1-3-1c-2 0-4 0-5-1l3-1z" class="h"></path><path d="M589 296h2l2 2c-2-1-3-1-4-2z" class="s"></path><path d="M581 300c2-1 3-2 5-3 1 1 3 1 5 1-4 1-6 1-8 4-1 2-2 4-2 6v1h0l-1 2-1-1v-5c0-2 1-4 2-5z" class="q"></path><defs><linearGradient id="Ee" x1="599.866" y1="295.114" x2="596.608" y2="307.443" xlink:href="#B"><stop offset="0" stop-color="#311c1b"></stop><stop offset="1" stop-color="#564141"></stop></linearGradient></defs><path fill="url(#Ee)" d="M591 296l10-1c2 1 4 1 5 1v1h1c-1 0-3 0-4 1h-2v1 1c0 1 1 1 2 2 1 3 1 6 1 10-2-2-2-2-3-5l-2-2c-1-3-3-4-5-6l-1-1-2-2z"></path><path d="M584 189h1c-1 1-3 4-3 5-1 1-2 2-3 4l-8 4h0l1-1-1-2h0c0-2-2-3-4-4h0v-3h1l1-1h1c2 2 4 2 6 2l3-2c1-1 3-1 5-2z" class="U"></path><path d="M567 195h0v-3h1c2 2 3 2 3 4 1 1 1 2 0 3 0-2-2-3-4-4z" class="E"></path><path d="M584 189h1c-1 1-3 4-3 5-2 1-5 3-8 2l-3-3h5l3-2c1-1 3-1 5-2z" class="K"></path><path d="M591 298l3 1c2 2 4 3 5 6l2 2v5l-4-6c-1 0-2 0-3-1-1 0-2-1-2-1-2 0-4 0-5 1s-1 1-2 1h-3l-1 3v-1c0-2 1-4 2-6 2-3 4-3 8-4z" class="Z"></path><path d="M591 298l3 1c2 2 4 3 5 6 0 0-1-1-2-1-1-1 0-1-1-1v1l-1-1c-4-2-8-1-12-1 2-3 4-3 8-4zm15-111l1 1 2 1v2l1 1v1l1 1c-1 0-1 1-3 1h-2-4l1-1-1-1c-1-1-2-1-3-1s-2 0-3-1l-9 2v-1c2-2 2-3 5-3h0c3-1 5-2 8-2h0c2 0 4 1 6 0z" class="e"></path><path d="M606 187l1 1 2 1v2c-5-2-12-2-17-2 3-1 5-2 8-2h0c2 0 4 1 6 0z" class="C"></path><path d="M596 191c5 0 9 0 14 2l1 1c-1 0-1 1-3 1h-2-4l1-1-1-1c-1-1-2-1-3-1s-2 0-3-1z" class="n"></path><path d="M512 259l1-1v66c-1-3 0-6-2-8l-3-3c-1 0-3 0-3-1-2-1-3-3-3-5l3 2 3 2c1 0 3 0 4 1v-1c1-1 0-16 0-17 0-5-1-9-2-13 0-5 1-9 1-14v-2c1-2 1-4 1-6h0z" class="P"></path><defs><linearGradient id="Ef" x1="531.954" y1="232.536" x2="537.728" y2="240.999" xlink:href="#B"><stop offset="0" stop-color="#adaaa9"></stop><stop offset="1" stop-color="#dfe0dd"></stop></linearGradient></defs><path fill="url(#Ef)" d="M540 228c3 0 2-2 4-3v3l-4 5h-1l-1 1h-1c-1 3-3 6-4 8h0-1l-1 1c-1 2-3 4-3 6-1 3-3 4-4 6 0-8 5-17 10-23v-1l2-2 2 1 2-1v-1z"></path><path d="M536 229l2 1c-1 1-1 1-2 1s-1 1-2 1v-1l2-2z" class="R"></path><path d="M516 346v-1c1-1 1-1 1-2l-1-1v-11c0-2 0-5 1-7 2-3 5-7 7-9 2-3 4-5 6-7 1-1 2-2 3-2 1-1 2-1 3-1 0 1 0 2-1 3l-1 1-7 7c-4 5-10 15-10 22v2l1-1c0 2-1 3 0 5 0 1-1 1-2 2z" class="E"></path><path d="M541 325c3 0 5-1 8-3 2-1 4-5 4-8 1-5 0-9-4-13-2-3-3-3-6-4h-1 6c2 1 4 2 5 5 3 4 2 12 0 17h0c0 2 0 3-1 4v4 3l1 1-1 2-1-1c-3 0-4-2-6-3 1 1 1 1 1 2l-7-5-2-2c1 0 3 0 4 1h0z" class="R"></path><path d="M537 324c1 0 3 0 4 1h0-1c1 1 2 1 4 1v1c1 0 1 0 2-1h3l-2 1-2 1v1c1 1 1 1 1 2l-7-5-2-2z" class="H"></path><path d="M549 326l4-7h0c0 2 0 3-1 4v4 3l1 1-1 2-1-1c-3 0-4-2-6-3v-1l2-1 2-1z" class="d"></path><path d="M547 327l3 3 1 2c-3 0-4-2-6-3v-1l2-1z" class="T"></path><path d="M589 283c3 0 9 2 11 0l6 4c-5-1-9-1-14 0-7 1-14 5-20 9h0c-3 1-5 3-9 1l-2-2 1-1c2 1 3 0 5 0 1 0 1 0 2-1l12-6c3-1 6-2 8-4z" class="C"></path><path d="M569 293l12-6c-2 4-4 4-8 6h-4z" class="j"></path><path d="M581 309h5v3 2c-5 1-7 5-12 5h0c-1 2-2 3-2 5h-1c-2 2-4 5-4 8-1 3 0 6-2 8v-1c0-5 0-11 1-16v-1l2-3c1-4 5-6 9-7 1 0 2 0 3-1l1-2z" class="g"></path><path d="M574 319l1-1c3-4 5-6 11-6v2c-5 1-7 5-12 5h0z" class="D"></path><path d="M522 203l5-5c1-2 4-3 6-3 2-1 4-1 6 0h1c4 1 8 3 12 5 3 1 6 4 9 4l1 1h1c-2 1-4 2-5 3-8-4-15-11-26-9-3 1-7 2-9 6-1 1 0 1 0 3h0c-1 0-1-1-2-1 1-2 1-2 1-4z" class="T"></path><path d="M519 207c1-1 2-2 3-4 0 2 0 2-1 4 1 0 1 1 2 1h0l1-1c1 0 2-1 3-1 2-1 4 0 6 1-2 0-4 0-6 1-1 2 0 3 0 5l1 2 2 3c1 3 1 3 3 4l4 2 1-1c2 1 3 1 4 1v1 1l-2 2v1l-2 1-2-1c-1 0-3 0-5-1-7-3-7-11-10-17v-1c-1-1-1-2-1-2l-1-1z" class="k"></path><path d="M531 224c1-1 4-1 6 0l1-1c2 1 3 1 4 1v1 1l-2 2v1c-2-1-4-3-6-3l-3-2z" class="h"></path><path d="M542 225v1l-2 2v1c-2-1-4-3-6-3 3 0 5 0 8-1z" class="d"></path><path d="M523 208l1-1c1 0 2-1 3-1 2-1 4 0 6 1-2 0-4 0-6 1-1 2 0 3 0 5l1 2 2 3c1 3 1 3 3 4l4 2c-2-1-5-1-6 0-5-4-4-11-8-16h0z" class="o"></path><path d="M533 207c5 2 9 4 12 8l1 1v3s0 1-1 1v1l2-2 1 1c-2 1-3 3-5 4h-1c-1 0-2 0-4-1l-1 1-4-2c-2-1-2-1-3-4l-2-3-1-2c0-2-1-3 0-5 2-1 4-1 6-1z" class="B"></path><path d="M534 219h0l2 2c-2 0-3 0-4-1l2-1z" class="W"></path><path d="M534 219c-2-2-3-3-4-5 1-1 2-1 3-2 3 0 6 1 9 3v2c-1 1-2 2-3 2h-5 0z" class="S"></path><path d="M533 207c5 2 9 4 12 8l1 1v3s0 1-1 1v1l2-2 1 1c-2 1-3 3-5 4h-1c-1 0-2 0-4-1l-1 1-4-2c-2-1-2-1-3-4l2 2c1 1 2 1 4 1h3c2-1 3-2 5-4l-1-1c-2-3-5-5-8-5-2 0-5-1-7 0v4l-1-2c0-2-1-3 0-5 2-1 4-1 6-1z" class="J"></path><path d="M545 215l1 1v3s0 1-1 1v1l2-2 1 1c-2 1-3 3-5 4h-1c-1 0-2 0-4-1 2-1 6-3 7-5v-3z" class="r"></path><path d="M536 305c2-2 3-4 6-4 2 0 4 1 6 3s3 6 2 9c0 2-1 5-3 6-1 1-3 2-5 2-2-1-4-2-5-4-2-3-2-6-2-9 1-1 1-2 1-3z" class="u"></path><defs><linearGradient id="Eg" x1="625.192" y1="305.548" x2="617" y2="315.891" xlink:href="#B"><stop offset="0" stop-color="#321c1a"></stop><stop offset="1" stop-color="#453738"></stop></linearGradient></defs><path fill="url(#Eg)" d="M606 296c2 0 6 1 8 3 9 3 16 9 22 15 2 2 4 5 6 6v1c-2 0-2 0-3-1-1 1-2 3-3 4v1l2 2-1 2h1 0l-1 1h-2c0 1 0 2 1 3l-2-1v-3c0-1 0-2-1-3 1-1 0-1 1-2h0c-1 1-2 0-2 2l-1 1-1-1h1l-1-1v1l-4 1c2-3 5-7 5-10 0-1 0-2-1-3s-2-2-4-3c-1-1-1-2-2-2l-5-1-1 1v1c-1-1-1-1-1-2-1-2-3-3-5-4l2-1h-3l-1-1h0l-1-2c-2 0-4 1-6 2-1-1-2-1-2-2v-1-1h2c1-1 3-1 4-1h-1v-1z"></path><path d="M610 302c2-1 2-2 4-1l1 1-1 1h-3l-1-1h0z" class="v"></path><path d="M606 296c2 0 6 1 8 3h-1c-1-1-2-1-3-1h-7c1-1 3-1 4-1h-1v-1z" class="X"></path><path d="M614 303c1 0 1-1 2 1 1 1 2 2 2 3 2 0 2-1 4 0 1 0 1 1 2 2l-5-1-1 1v1c-1-1-1-1-1-2-1-2-3-3-5-4l2-1z" class="r"></path><path d="M523 208c0-2-1-2 0-3 2-4 6-5 9-6 11-2 18 5 26 9l1 1h-1v2l-10 9-1-1-2 2v-1c1 0 1-1 1-1v-3l-1-1c-3-4-7-6-12-8-2-1-4-2-6-1-1 0-2 1-3 1l-1 1z" class="u"></path><path d="M547 219c2-2 3-3 4-5l2-3c2 0 3-1 5-2v2l-10 9-1-1zm-38-6c2-1 2-2 4-1 3 2 4 7 4 11v8l-3 18c0 3-1 6-1 9l-1 1c0-4-1-8-2-12-2-5-3-10-4-16 1-1 0-3 0-4 0-5 0-9 3-14z" class="s"></path><defs><linearGradient id="Eh" x1="617.093" y1="307.425" x2="607.172" y2="323.362" xlink:href="#B"><stop offset="0" stop-color="#504041"></stop><stop offset="1" stop-color="#6e6c6d"></stop></linearGradient></defs><path fill="url(#Eh)" d="M603 302c2-1 4-2 6-2l1 2h0l1 1h3l-2 1c2 1 4 2 5 4 0 1 0 1 1 2v-1l1-1 5 1c1 0 1 1 2 2l-1 2h0 0c-1 2-2 3-3 5h0c-1 1-2 3-3 5v1h-3-2c-1 0-3 1-4 2l-6 3c0-2 1-3 3-5v-4-2c-1 1-1 1-2 1h0l1-5h-2v-2c0-4 0-7-1-10z"></path><path d="M612 313c1-1 1-1 4-2l-1 3c-1 0-2 0-3-1z" class="l"></path><path d="M612 313c1 1 2 1 3 1-1 2-2 3-2 5h0-1l-1-2 1-4z" class="N"></path><defs><linearGradient id="Ei" x1="613.826" y1="311.452" x2="616.23" y2="318.206" xlink:href="#B"><stop offset="0" stop-color="#4b3d3e"></stop><stop offset="1" stop-color="#585556"></stop></linearGradient></defs><path fill="url(#Ei)" d="M617 308c0 1 0 1 1 2 0 1 0 2-1 4v2l-2 4h-1 0l-1 1v-2c0-2 1-3 2-5l1-3 1-3z"></path><defs><linearGradient id="Ej" x1="622.619" y1="311.706" x2="616.127" y2="318.911" xlink:href="#B"><stop offset="0" stop-color="#574e50"></stop><stop offset="1" stop-color="#696668"></stop></linearGradient></defs><path fill="url(#Ej)" d="M618 309l1-1 5 1c1 0 1 1 2 2l-1 2h0 0c-1 2-2 3-3 5h0c-1 1-2 3-3 5v1h-3-2l1-1v-3l2-4v-2c1-2 1-3 1-4v-1z"></path><path d="M618 309l1-1 5 1c1 0 1 1 2 2l-1 2h0v-1c-2-1-4-2-7-3z" class="O"></path><path d="M617 316s1-1 1-2l1 1v1c0 2-2 5 0 7v1h-3-2l1-1v-3l2-4z" class="N"></path><defs><linearGradient id="Ek" x1="604.706" y1="308.393" x2="610.094" y2="314.335" xlink:href="#B"><stop offset="0" stop-color="#292123"></stop><stop offset="1" stop-color="#47403d"></stop></linearGradient></defs><path fill="url(#Ek)" d="M603 302c2-1 4-2 6-2l1 2h0l1 1h3l-2 1c-2 4-4 10-4 15 0 2 0 4-1 5v-4-2c-1 1-1 1-2 1h0l1-5h-2v-2c0-4 0-7-1-10z"></path><path d="M603 302c2-1 4-2 6-2l1 2h0c-3 3-3 8-4 12h-2v-2c0-4 0-7-1-10z" class="C"></path><path d="M529 322c3 0 5 1 8 2h0l2 2 7 5v2c-1 1-2 2-2 3h0c-3 2-5 6-7 8s-4 3-6 4l-3-1c-3-3-4-8-5-12-1 3-3 6-5 9-1-2 0-3 0-5h0c1-1 1-2 1-3l2-3 2-4 1-2c1-1-1 1 0-1s3-3 5-4z" class="u"></path><path d="M518 339c1-1 1-2 1-3l2-3 2-4 1-2c1-1-1 1 0-1s3-3 5-4c0 4-1 10-4 12-1 1-1 0-1 0-2 1-3 5-6 5z" class="c"></path><path d="M539 326l7 5v2c-1 1-2 2-2 3h0c-3 2-5 6-7 8s-4 3-6 4l-3-1 6-3c2-2 5-5 6-8s0-7-1-10z" class="R"></path><path d="M626 311c2 1 3 2 4 3s1 2 1 3c0 3-3 7-5 10l4-1v-1l1 1h-1l1 1 1-1c0-2 1-1 2-2h0c-1 1 0 1-1 2 0 1-2 1-3 2 2 1 3 2 3 3v7c1 2 1 4 1 6v2l1-1 2-2h1c0 1-1 4-2 4h-1c0 1-1 3-2 5l-1-1v-1c1-3 2-9 1-11-2 0-2 0-2 1l-3 1c-1 0-5 2-6 2l-2-1c-1 0-3 2-4 3l-1 2-2 2v-2l-3-3c0-5 4-10 7-13l1-2-1-1 1-1-2-3h3v-1c1-2 2-4 3-5h0c1-2 2-3 3-5h0 0l1-2z" class="e"></path><path d="M618 327c1 2 1 5 0 8-1 2-2 3-2 5-1 1-2 2-2 3-1 1-1 3-1 4l-3-3c0-5 4-10 7-13l1-2-1-1 1-1z" class="g"></path><path d="M626 311c2 1 3 2 4 3s1 2 1 3c0 3-3 7-5 10l4-1v-1l1 1h-1l1 1 1-1c0-2 1-1 2-2h0c-1 1 0 1-1 2 0 1-2 1-3 2-5 3-9 8-13 13 0-2 2-4 2-6 1-1 2-3 3-4 0-1 0-3-1-4h0c0-3 1-4 2-6l-1-3h0c1-2 2-3 3-5h0 0l1-2z" class="r"></path><path d="M626 311c2 1 3 2 4 3l-2 2-1 1v-1-2l-2-1 1-2z" class="h"></path><path d="M622 318h0c1-2 2-3 3-5l1 2c-1 2-2 5-3 6l-1-3z" class="o"></path><path d="M630 314c1 1 1 2 1 3 0 3-3 7-5 10 0 1-1 2-2 2h-1l2-5 2-7 1-1 2-2z" class="l"></path><path d="M628 316c1 0 1 1 1 2 0 2-2 5-3 7h-1v-1l2-7 1-1z" class="N"></path><defs><linearGradient id="El" x1="617.118" y1="329.321" x2="601.934" y2="340.757" xlink:href="#B"><stop offset="0" stop-color="#2d2020"></stop><stop offset="1" stop-color="#4a3c3c"></stop></linearGradient></defs><path fill="url(#El)" d="M614 324h2l2 3-1 1 1 1-1 2c-3 3-7 8-7 13l3 3v2c0 3 0 4-1 7l-1-1c-3 3-4 4-7 4v-1c0-1 0-1-1-2h-1 1l-4-2c-3-1-4-4-5-6v-1c-1-7 5-13 10-18l6-3c1-1 3-2 4-2z"></path><path d="M612 330l1 1-1 1c-3 3-5 8-5 12h0c1 0 1 0 2 1-1 0-2 1-3 1h-1v1c0 1 0 2-1 3h0c1 2 1 3 1 4h-1c-2-3-1-9-1-12 0 1 1 2 1 2h2c-1-3 0-3 1-6v-1c2-3 3-5 5-7z" class="o"></path><path d="M612 330h2 0c1 0 2 0 3 1-3 3-7 8-7 13l3 3v2c0 3 0 4-1 7l-1-1c0-1 1-3 1-5-1-2-2-3-3-5h0c-1-1-1-1-2-1h0c0-4 2-9 5-12l1-1-1-1z" class="h"></path><path d="M609 345h0c1 2 2 3 3 5 0 2-1 4-1 5-3 3-4 4-7 4v-1c0-1 0-1-1-2h-1 1l2-2c0-1 0-2-1-4h0c1-1 1-2 1-3v-1h1c1 0 2-1 3-1z" class="L"></path><path d="M609 345h0l-1 1c0 3 0 7-2 9-1 1-1 1-3 1h-1 1l2-2c0-1 0-2-1-4h0c1-1 1-2 1-3v-1h1c1 0 2-1 3-1z" class="e"></path><path d="M609 345h0l-1 1v1l-1 1h-1v-2c1 0 2-1 3-1z" class="O"></path><path d="M614 324h2l2 3-1 1c-1-1-2-2-3-2-4 1-7 5-8 8-2 3-2 6-3 8 0 3-1 9 1 12h1l-2 2-4-2c-3-1-4-4-5-6v-1c-1-7 5-13 10-18l6-3c1-1 3-2 4-2z" class="S"></path><path d="M604 329l6-3c0 1-1 2-2 3h-1-1c0 1-1 2-2 3-3 2-7 7-8 11v2 2h-1l-1 1v-1c-1-7 5-13 10-18z" class="D"></path><path d="M631 340c0-1 0-1 2-1 1 2 0 8-1 11v1c-1 2-1 3-1 5l-2 6c0 3 0 3-2 6-3 2-7 2-10 3s-4 2-7 3c0 2-1 3-2 4l-3 2c1-2 1-3 0-5l-1-7c-1-1-2-2-3-2 1-1 2-1 3-2-2-1-3-2-5-3-1 0 0-1-1-2l-1-1h0c2 1 4 1 7 1s4-1 7-4l1 1c1-3 1-4 1-7l2-2 1-2c1-1 3-3 4-3l2 1c1 0 5-2 6-2l3-1z" class="F"></path><path d="M613 349l2-2 1-2c1-1 3-3 4-3l2 1c1 0 5-2 6-2 0 1 0 2-1 3v1h-2l-1 1h0-1v-3c-1 1-2 1-2 1h-1l-1 1-1 1c-2 1-3 5-4 7 0 2-1 4-1 6-3 2-6 4-9 5-2-1-3-2-5-3-1 0 0-1-1-2l-1-1h0c2 1 4 1 7 1s4-1 7-4l1 1c1-3 1-4 1-7z" class="J"></path><path d="M604 359c3 0 4-1 7-4l1 1v2c-1 1-2 0-3 1-1 0-2 2-3 2h-5v-1h-2l-1-1-1-1h0c2 1 4 1 7 1z" class="f"></path><defs><linearGradient id="Em" x1="635.511" y1="345.175" x2="620.28" y2="361.631" xlink:href="#B"><stop offset="0" stop-color="#8a8889"></stop><stop offset="1" stop-color="#a3a2a2"></stop></linearGradient></defs><path fill="url(#Em)" d="M631 340c0-1 0-1 2-1 1 2 0 8-1 11v1c-1 2-1 3-1 5l-2 6-2 2v2c-1 1-1 1-2 1h-1c1-1 1-3 1-4v-1h0c0-6 1-12 2-17v-1c1-1 1-2 1-3l3-1z"></path><path d="M631 340l-2 5-2-1c1-1 1-2 1-3l3-1z" class="M"></path><path d="M627 344l2 1-4 17h0c0-6 1-12 2-17v-1z" class="i"></path><defs><linearGradient id="En" x1="623.96" y1="356.27" x2="608.54" y2="361.73" xlink:href="#B"><stop offset="0" stop-color="#adacac"></stop><stop offset="1" stop-color="#d3d2d3"></stop></linearGradient></defs><path fill="url(#En)" d="M618 349l2-3h1c1 1 0 3 0 4 1 4 0 8 0 12 0 2 0 4-1 6h0l-1 1-2 2c-3 1-4 2-7 3 0 2-1 3-2 4l-3 2c1-2 1-3 0-5l-1-7c-1-1-2-2-3-2 1-1 2-1 3-2 3-1 6-3 9-5 0-2 1-4 1-6l4-4z"></path><path d="M611 371c2 1 2 0 4 0 0-1 1-2 2-3 1 1 1 0 2 1l-2 2c-3 1-4 2-7 3l1-3z" class="E"></path><path d="M618 349c0 1-1 3-1 4v1c-1 2-2 7-3 9l-1-2v-2c0-2 1-4 1-6l4-4z" class="j"></path><path d="M613 359v2l1 2c0 3 1 4-1 6h-1c0 1 0 2-1 2l-1 3c0 2-1 3-2 4l-3 2c1-2 1-3 0-5l-1-7c-1-1-2-2-3-2 1-1 2-1 3-2 3-1 6-3 9-5z" class="T"></path><path d="M612 369l1-8 1 2c0 3 1 4-1 6h-1z" class="G"></path><defs><linearGradient id="Eo" x1="582.468" y1="327.766" x2="591.538" y2="330.631" xlink:href="#B"><stop offset="0" stop-color="#a5a3a3"></stop><stop offset="1" stop-color="#e0dfde"></stop></linearGradient></defs><path fill="url(#Eo)" d="M587 305c1-1 3-1 5-1 0 0 1 1 2 1 1 1 2 1 3 1l4 6v-5c1 3 1 3 3 5v2h2l-1 5h0c1 0 1 0 2-1v2 4c-2 2-3 3-3 5-5 5-11 11-10 18v1c1 2 2 5 5 6l4 2h-1 1c1 1 1 1 1 2v1c-3 0-5 0-7-1h0l1 1c1 1 0 2 1 2 2 1 3 2 5 3-1 1-2 1-3 2 0-1-1-2-1-2-2-2-7-2-9-3v-1c-1 0-2-1-4-1h0c0 1 0 2-1 3v-8c-1-2 0-5 0-7 0-6-1-12 0-18 1-3 1-6 1-9-1-2-1-4-1-6v-2-3h-5 0l1-3h3c1 0 1 0 2-1z"></path><defs><linearGradient id="Ep" x1="586.448" y1="306.912" x2="587.902" y2="314.323" xlink:href="#B"><stop offset="0" stop-color="#898787"></stop><stop offset="1" stop-color="#a5a2a1"></stop></linearGradient></defs><path fill="url(#Ep)" d="M581 309l1-3h3c1 0 1 0 2-1l2 1h-1l2 1h0v1c0 1 1 2 2 3l1 2c-2 1-3 0-4 2 0 2 1 4 0 6h0v-2-2l-1-2h-1c-1 2 0 3 0 5-1-2-1-4-1-6v-2-3h-5 0z"></path><path d="M589 315h0c0-2 0-5 1-7 0 1 1 2 2 3l1 2c-2 1-3 0-4 2z" class="C"></path><path d="M586 354c0-2 0-6 1-8h0l2-5c0-4 1-6 3-10v1c-1 5-4 10-4 16l1 1c1 1 1 1 1 2 1 2 4 5 6 6h1v1h0l1 1c1 1 0 2 1 2 2 1 3 2 5 3-1 1-2 1-3 2 0-1-1-2-1-2-2-2-7-2-9-3v-1c-1 0-2-1-4-1h0c0 1 0 2-1 3v-8z" class="L"></path><path d="M587 359v-4h1 0l3 5c-1 0-2-1-4-1h0z" class="T"></path><path d="M587 305c1-1 3-1 5-1 0 0 1 1 2 1 1 1 2 1 3 1l4 6c0 4 0 9-3 12l-1 3v1 1c-2 2-4 4-5 7l-2 4c-1 3-1 6-1 9l-1-1c0-6 3-11 4-16v-1c0-1 0-3 1-4 0-4 1-10 0-14l-1-2c-1-1-2-2-2-3v-1h0l-2-1h1l-2-1z" class="g"></path><path d="M587 305c1-1 3-1 5-1 0 0 1 1 2 1 1 1 2 1 3 1l4 6c0 4 0 9-3 12 1-2 1-4 2-7-1-2-1-5-3-7-2-1-4 0-5 0v1c-1-1-2-2-2-3v-1h0l-2-1h1l-2-1z" class="J"></path><defs><linearGradient id="Eq" x1="593.296" y1="320.953" x2="603.902" y2="357.594" xlink:href="#B"><stop offset="0" stop-color="#b8b7b7"></stop><stop offset="1" stop-color="#e9e8e8"></stop></linearGradient></defs><path fill="url(#Eq)" d="M601 307c1 3 1 3 3 5v2h2l-1 5h0c1 0 1 0 2-1v2 4c-2 2-3 3-3 5-5 5-11 11-10 18v1c1 2 2 5 5 6l4 2h-1 1c1 1 1 1 1 2v1c-3 0-5 0-7-1v-1h-1c-2-1-5-4-6-6 0-1 0-1-1-2 0-3 0-6 1-9l2-4c1-3 3-5 5-7v-1-1l1-3c3-3 3-8 3-12v-5z"></path><path d="M601 307c1 3 1 3 3 5v2h2l-1 5-1 1v-1c-1 0-1 0-2 1l-5 8v-1l1-3c3-3 3-8 3-12v-5z" class="U"></path><path d="M604 314h2l-1 5-1 1v-1c-1 0-1 0-2 1l2-6z" class="P"></path><path d="M605 319h0c1 0 1 0 2-1v2 4c-2 2-3 3-3 5-5 5-11 11-10 18v1c1 2 2 5 5 6l4 2h-1c-2 0-4-1-6-2-2-2-3-4-3-7-1-7 4-14 9-20v-1c1-2 2-4 2-6l1-1z" class="Y"></path><path d="M605 319h0c1 0 1 0 2-1v2c-1 1-2 5-3 6s-2 1-2 1v-1c1-2 2-4 2-6l1-1z" class="K"></path><path d="M507 214l1-2 1 1c-3 5-3 9-3 14 0 1 1 3 0 4 1 6 2 11 4 16 1 4 2 8 2 12h0c0 2 0 4-1 6v2c0 5-1 9-1 14 1 4 2 8 2 13 0 1 1 16 0 17v1c-1-1-3-1-4-1l-3-2-3-2-2-1c1-2 2-4 3-5h0v-7-13l-1-1-6-26c-1-3-2-6-3-8-1-4-3-8-6-11v-1h1 2l1-1c-1 0-2-1-2-2l5 1c1 0 2 0 2-1 2-2 3-8 4-11h0l1 1 1-2v1l2-2c-1 0-1-1-1-2l4-2z" class="C"></path><path d="M507 214c-1 2-2 3-2 5 0 3-2 6-2 10h0c-1-1-1-1-1-3v-2c1-2 2-4 2-6-1 0-1-1-1-2l4-2z" class="B"></path><path d="M511 265c-1-12-7-22-7-35 1-1 1-3 2-4v4 1c1 6 2 11 4 16 1 4 2 8 2 12h0c0 2 0 4-1 6z" class="c"></path><path d="M508 306l1-1h1v-7c0-8-1-16-3-24l2 3h1v-6h0c0-2 1-3 1-4 0 5-1 9-1 14 1 4 2 8 2 13 0 1 1 16 0 17v1c-1-1-3-1-4-1v-1l-1-1 1-1v-2h0z" class="j"></path><defs><linearGradient id="Er" x1="494.147" y1="263.612" x2="503.481" y2="261.77" xlink:href="#B"><stop offset="0" stop-color="#767071"></stop><stop offset="1" stop-color="#a9a7a7"></stop></linearGradient></defs><path fill="url(#Er)" d="M489 231l5 1 2 1c1 1 1 3 2 4 0 1 0 1 1 2h0-2c0 3 3 6 4 9v2h0c-1-1-1-2-1-3l-1-1c0-2-1-2-2-3 1 3 3 7 4 10 1 1 2 3 2 5v1c1 1 1 2 1 3l2 11c1 2 1 5 1 8 1 8 2 16 1 25h0v2l-1 1 1 1v1l-3-2-3-2-2-1c1-2 2-4 3-5h0v-7-13l-1-1-6-26c-1-3-2-6-3-8-1-4-3-8-6-11v-1h1 2l1-1c-1 0-2-1-2-2z"></path><path d="M503 281c2 3 0 11 1 15l-1 5v-7-13z" class="W"></path><path d="M489 231l5 1 2 1-2 2c-1 1-1 3 0 4 0 1 0 1 1 2h-1 0c0-1-1-1-1-2-1-1-1-1-1-2s-1-2-1-4c-1 0-2-1-2-2z" class="J"></path><path d="M496 254c3 4 3 8 5 12h0v-3c1 3 2 8 2 11l-1 6-6-26z" class="C"></path><path d="M504 296l1-3h1c1 3 0 7-1 11h1l-1 5-3-2-2-1c1-2 2-4 3-5h0l1-5z" class="f"></path><path d="M503 301v5h1c1 0 1-1 1-2h1l-1 5-3-2-2-1c1-2 2-4 3-5z" class="J"></path><path d="M506 273c1 2 1 5 1 8 1 8 2 16 1 25h0v2l-1 1 1 1v1l-3-2 1-5c2-4 2-10 1-15v-4-4l-1-3c0-1 0-2-1-2v-1c1-1 1-1 1-2z" class="B"></path><path d="M615 189l1-1 3 3c2 4 4 9 8 12l2 1h0l1 1 1 1h0c1 0 4 1 5 2h-1c1 2 2 2 4 3l1-1 1 2c-1 0-2 1-4 2 0 0-1 0-2 1 0 1 0 1 1 3h-3c1 3 1 5 1 8 0 2-1 3-2 4h-2 0l-14-7-1 1c-1-1-1-2-2-3l-1-1c-1 0-1-1-2-1l-1-1v2c-1-3-2-4-3-6-4-1-6-2-10-2-6 1-11 2-16 5l-1 2v1h-3c0-1-1-1-2-1l-1-1-11 8c-8 7-15 17-20 26h-2c6-16 18-30 33-38 2-1 3-3 5-4 5-3 9-4 15-5 2-1 6-2 9-3h7c1-1-1 0 1-1v1l-1 1h-8c4 0 10-1 13 2h3c-1 0 0 0-1-1-2-1-4-3-6-3h-1c-1-1-4-1-5-1 0-1-1-1-1-2h-6c3-1 6-1 9-1h1c0-1 1-1 1-2h0c2 0 2-1 3-1l-1-1v-1l-1-1v-2l-2-1c2 0 3 0 4 1 1 2 2 2 5 3h0v-1l-1-2z" class="H"></path><path d="M608 195l4 1c-1 1-1 2-1 2l-4-1c0-1 1-1 1-2z" class="e"></path><path d="M593 210c4-1 7-2 11-2v1l-2 1h-3-3-3z" class="I"></path><path d="M612 196c2 1 4 2 7 3l-2 1-1 1-5-3s0-1 1-2z" class="K"></path><path d="M621 211c5 3 7 7 10 11 1 2 2 4 3 4 0 2-1 3-2 4h-2l-1-1c0-1 0-3-1-5v-2c-1-4-5-6-7-10v-1z" class="D"></path><path d="M593 210h3v2c-6 1-11 2-16 5l-1 2v1h-3c0-1-1-1-2-1l-1-1c6-4 13-6 20-8z" class="t"></path><path d="M604 208c5-1 11 2 15 3 0 0 1 1 2 0v1c2 4 6 6 7 10v2c1 2 1 4 1 5-2-1-5-6-7-9l-2-2-1-1 1-1c-3-3-7-5-12-6l-4-1v-1z" class="B"></path><path d="M620 216c2 2 5 4 6 6v1c-2-1-2-3-4-3l-2-2-1-1 1-1z" class="J"></path><path d="M604 209l4 1c5 1 9 3 12 6l-1 1 1 1 2 2c2 3 5 8 7 9l1 1h0l-14-7-1 1c-1-1-1-2-2-3l-1-1c-1 0-1-1-2-1l-1-1v2c-1-3-2-4-3-6-4-1-6-2-10-2v-2h3 3l2-1z" class="u"></path><path d="M604 209l4 1-2 1c-2 0-5-1-7-1h3l2-1z" class="J"></path><path d="M608 210c5 1 9 3 12 6l-1 1c-5-2-8-4-13-6l2-1z" class="Q"></path><path d="M606 214h0c3 2 6 4 8 6 2 1 3 2 5 3l1-1c0-1-1-2 0-4l2 2c2 3 5 8 7 9l1 1h0l-14-7-1 1c-1-1-1-2-2-3l-1-1c-1 0-1-1-2-1l-1-1v2c-1-3-2-4-3-6z" class="x"></path><path d="M615 189l1-1 3 3c2 4 4 9 8 12l2 1h0l1 1 1 1h0c1 0 4 1 5 2h-1c1 2 2 2 4 3l1-1 1 2c-1 0-2 1-4 2 0 0-1 0-2 1 0 1 0 1 1 3h-3l-1-1c-4-7-9-11-16-16l1-1 2-1c-3-1-5-2-7-3l-4-1h0c2 0 2-1 3-1l-1-1v-1l-1-1v-2l-2-1c2 0 3 0 4 1 1 2 2 2 5 3h0v-1l-1-2z" class="b"></path><path d="M627 203l2 1h0l1 1 1 1h0c1 0 4 1 5 2h-1c1 2 2 2 4 3-3 0-4-1-7-2-2-2-5-4-5-6z" class="W"></path><path d="M609 189c3 3 7 6 10 9v1c-3-1-5-2-7-3l-4-1h0c2 0 2-1 3-1l-1-1v-1l-1-1v-2z" class="B"></path><path d="M632 209c3 1 4 2 7 2l1-1 1 2c-1 0-2 1-4 2 0 0-1 0-2 1 0 1 0 1 1 3h-3l-1-1c1-3 1-6 0-8z" class="q"></path><defs><linearGradient id="Es" x1="575.569" y1="252.63" x2="608.222" y2="262.723" xlink:href="#B"><stop offset="0" stop-color="#d0d0cf"></stop><stop offset="1" stop-color="#f6f6f5"></stop></linearGradient></defs><path fill="url(#Es)" d="M596 212c4 0 6 1 10 2 1 2 2 3 3 6v-2l1 1c1 0 1 1 2 1l1 1c0 5 3 9 3 13l-2 4h0c0 1 0 3-1 5l-1-1c0 2-1 5 0 7-1 3-2 12-1 15l1 6v11l1 3h1 0c1 1 1 2 1 4l-9-9h-1-1l-2 2h-1l-1 2h0c-2 2-8 0-11 0-2 2-5 3-8 4l-12 6c-1 1-1 1-2 1-2 0-3 1-5 0l-1 1c-1 0-2-1-2-2v-1c5 1 9 0 14-3h0l-1-1-1 1c-4 2-7 3-11 1 3 0 6 0 8-2 4-2 8-4 11-6 6-5 8-11 10-18 0-2 1-3 1-4l-1-2c1 0 1-1 1-1v-1-1l-1 3h-1l-2 6c-1 1-2 1-3 2l2-4c1-2 1-5 2-6v-2c1-11-1-21-5-31h0l-1-4h-2l1-2c5-3 10-4 16-5z"></path><path d="M590 251v-3c2 2 0 8 1 10l-1 2-1-2c1 0 1-1 1-1v-1-1l-1 3h-1c0-2 1-5 2-7z" class="T"></path><path d="M589 242c1 3 1 6 1 9-1 2-2 5-2 7l-2 6c-1 1-2 1-3 2l2-4c1-2 1-5 2-6 0-1 1-1 1-2 1-1 0-6 0-7 1-1 1-4 1-5z" class="E"></path><path d="M593 261c1-9 2-17 1-25v-1h0c1 6 2 11 2 17-1 2 0 4-1 5l-1 1h0c0 1 0 2-1 3z" class="i"></path><path d="M591 258v1 2l-1 2v2 2l-1 4c-1 1-1 2-1 3-2 3-4 7-7 8h-2c6-5 8-11 10-18 0-2 1-3 1-4l1-2z" class="H"></path><path d="M582 223l1-2v-1-1h2c-1 4 1 7 1 10 1 4 2 8 3 13 0 1 0 4-1 5 0 1 1 6 0 7 0 1-1 1-1 2v-2c1-11-1-21-5-31z" class="S"></path><path d="M593 261c1-1 1-2 1-3h0l1-1c1-1 0-3 1-5v21l1 4 3 6h0c-2 2-8 0-11 0l1-2c2-7 3-14 3-20z" class="M"></path><path d="M596 281l-1-1v-1-4c0-1 0-1 1-2l1 4h0l-1-1v5z" class="J"></path><path d="M596 281v-5l1 1h0l3 6h0c-2 2-8 0-11 0l1-2 2 1h1c1 0 1 0 2-1h1z" class="I"></path><path d="M596 212c4 0 6 1 10 2 1 2 2 3 3 6v-2l1 1c1 0 1 1 2 1l1 1c0 5 3 9 3 13l-2 4h0c0 1 0 3-1 5l-1-1c0 2-1 5 0 7-1 3-2 12-1 15l1 6v11l1 3h1 0c1 1 1 2 1 4l-9-9c-2-3-3-5-4-9-1-1-1-3-1-4-1-2 0-4 0-6v-13c0-5-1-9-2-13v-2l-1-5c-1 0-1-1-1-2l-1-1-2-6c-3 0-6 1-9 1h-2v1 1l-1 2h0l-1-4h-2l1-2c5-3 10-4 16-5z" class="B"></path><path d="M610 267c0 1 1 2 2 3v11l-2-2v-1-11z" class="e"></path><path d="M610 267c0-6-1-12 0-18h2c-1 3-2 12-1 15l1 6c-1-1-2-2-2-3z" class="V"></path><path d="M602 270v-1l8 10 2 2 1 3h1 0c1 1 1 2 1 4l-9-9c-2-3-3-5-4-9z" class="X"></path><path d="M596 212c4 0 6 1 10 2 1 2 2 3 3 6h0c-2-1-4-3-7-3h0c-3-1-8-2-11-1-3 0-6 0-8 1-2 0-2 1-2 2h-2l1-2c5-3 10-4 16-5z" class="F"></path><path d="M609 220v-2l1 1c1 0 1 1 2 1l1 1c0 5 3 9 3 13l-2 4h0c0 1 0 3-1 5l-1-1c0 2-1 5 0 7h-2c1-4 1-9 1-14s-1-10-2-15h0z" class="X"></path><path d="M612 242c0-1 0-3 1-5l1 1c0 1 0 3-1 5l-1-1z" class="s"></path><defs><linearGradient id="Et" x1="609.195" y1="242.429" x2="598.505" y2="243.5" xlink:href="#B"><stop offset="0" stop-color="#b5b4b4"></stop><stop offset="1" stop-color="#d6d5d5"></stop></linearGradient></defs><path fill="url(#Et)" d="M594 218c4 0 7 1 10 2 2 0 2 1 3 2 1 3 2 6 2 9 1 6 0 11 0 17s1 13-1 18c0 1 0 1-1 2-2 0-2 0-2-1h-2c-1-1-1-1-2-1h0c-1-2 0-4 0-6v-13c0-5-1-9-2-13v-2l-1-5c-1 0-1-1-1-2l-1-1-2-6z"></path><path d="M573 218l1 1c1 0 2 0 2 1h3v-1h2l1 4h0c4 10 6 20 5 31v2c-1 1-1 4-2 6l-2 4v1c-4 4-8 9-14 12l-11 5 2 2-5 2-2-2-2 1-8 2h-1-2-2c-2 0-3-1-4-2-1-3-1-7 0-11l1-4c2-7 4-13 7-20 5-9 12-19 20-26 4-3 7-5 11-8z" class="d"></path><path d="M546 253c5-4 8-10 12-13h1v2c-3 1-5 5-7 7-6 8-11 20-11 30 1 1 2 1 4 1l-4 1-2-1c-2-4 0-7 1-11 0-6 3-11 6-16z" class="P"></path><path d="M546 253l1-1c1-3 15-19 18-20l2 2v10s0-1-1-2v-1c-2 0-4 1-6 2-1 7 0 14-1 21 0 3 0 8 1 11l-2 1 1-34v-2h-1c-4 3-7 9-12 13z" class="G"></path><path d="M560 243l1-4c0-1 0-3 2-4 1-1 2-1 4-1v10s0-1-1-2v-1c-2 0-4 1-6 2z" class="H"></path><path d="M560 243c2-1 4-2 6-2v1c1 1 1 2 1 2v3l-1 20v4l-1 1-5 3c-1-3-1-8-1-11 1-7 0-14 1-21z" class="c"></path><path d="M542 252c5-9 12-19 20-26 1 1 2 1 3 2h1l1 2 7 7h-1s-1 0-1-1l-1 1c0-1-1-1-1-1l-1-1v2h1c1 1 2 3 3 4-1 0-2-1-3-2h-1v1c-1 2-1 4-1 7h-1v-3-10l-2-2c-3 1-17 17-18 20l-1 1c-3 5-6 10-6 16-1 4-3 7-1 11l2 1 4-1c2-1 5-1 8-2 2 0 4-2 5-2l2-1 5-3h1l4-2c1 0 1-1 2-1 1-1 1-1 2-1-1 1-2 3-4 4l-1 3h1l1 1c-1 1-1 2-2 3l-11 5 2 2-5 2-2-2-2 1-8 2h-1-2-2c-2 0-3-1-4-2-1-3-1-7 0-11l1-4c2-7 4-13 7-20z" class="R"></path><path d="M537 280c2 1 4 2 5 4 1 1 1 1 2 1l1 1h-2-1 0l-3-3c-1-1-2-2-2-3z" class="B"></path><path d="M546 250c0-2 1-3 2-5l1-2 2-1c3-5 10-13 15-14l1 2 7 7h-1s-1 0-1-1l-1 1c0-1-1-1-1-1l-1-1c-1-2-2-3-4-4-2 0-4 2-5 3-6 5-9 11-14 16z" class="I"></path><path d="M542 252c5-9 12-19 20-26 1 1 2 1 3 2h1c-5 1-12 9-15 14l-2 1-1 2c-1 2-2 3-2 5-5 8-8 16-10 25 0 1 0 3 1 5 0 1 1 2 2 3l3 3h0 1c2 1 6 1 8 1l-8 2h-1-2-2c-2 0-3-1-4-2-1-3-1-7 0-11l1-4c2-7 4-13 7-20z" class="q"></path><path d="M539 283l3 3h0c-2 0-3 0-5-1 0-1 1-1 2-2z" class="I"></path><path d="M537 285l-1-1c-2-3-1-6 0-9 0 1 0 3 1 5 0 1 1 2 2 3-1 1-2 1-2 2z" class="J"></path><path d="M574 268c-1 1-2 3-4 4l-1 3h1l1 1c-1 1-1 2-2 3l-11 5 2 2-5 2-2-2-2 1c-2 0-6 0-8-1h2l8-3v-2c-2 1-6 3-8 2h-2 0l-2-2 4-1c2-1 5-1 8-2 2 0 4-2 5-2l2-1 5-3h1l4-2c1 0 1-1 2-1 1-1 1-1 2-1z" class="K"></path><path d="M553 286l5-2 2 2-5 2-2-2z" class="L"></path><path d="M567 273l3-1-1 3c-2 2-5 4-8 5 1-3 4-5 6-7z" class="W"></path><path d="M553 281c5-2 9-4 12-7 1-1 1-1 2-1-2 2-5 4-6 7l-8 3v-2z" class="B"></path><defs><linearGradient id="Eu" x1="552.839" y1="272.779" x2="552.642" y2="283.192" xlink:href="#B"><stop offset="0" stop-color="#a5a1a1"></stop><stop offset="1" stop-color="#c1c0c1"></stop></linearGradient></defs><path fill="url(#Eu)" d="M574 268c-1 1-2 3-4 4l-3 1c-1 0-1 0-2 1-3 3-7 5-12 7-2 1-6 3-8 2h-2 0l-2-2 4-1c2-1 5-1 8-2 2 0 4-2 5-2l2-1 5-3h1l4-2c1 0 1-1 2-1 1-1 1-1 2-1z"></path><path d="M573 218l1 1c1 0 2 0 2 1h3v-1h2l1 4h0c4 10 6 20 5 31v2c-1 1-1 4-2 6l-2 4v1c-4 4-8 9-14 12 1-1 1-2 2-3l-1-1h-1l1-3c2-1 3-3 4-4-1 0-1 0-2 1-1 0-1 1-2 1l-4 2h-1l1-1v-4l1-20h1c0-3 0-5 1-7v-1h1c1 1 2 2 3 2-1-1-2-3-3-4h-1v-2l1 1s1 0 1 1l1-1c0 1 1 1 1 1h1l-7-7-1-2h-1c-1-1-2-1-3-2 4-3 7-5 11-8z" class="w"></path><path d="M567 230c2 0 5 1 5 2 1 2 2 3 2 5l-7-7z" class="s"></path><path d="M571 276c3-2 5-5 8-7 2-2 3-5 6-7l-2 4v1c-4 4-8 9-14 12 1-1 1-2 2-3z" class="V"></path><path d="M574 268c1-3 3-5 4-7 1-4-1-7 2-11 1 6 1 12-3 17-2 3-4 6-7 8h-1l1-3c2-1 3-3 4-4z" class="J"></path><path d="M569 235l1 1s1 0 1 1l1-1c0 1 1 1 1 1 4 4 6 8 7 13-3 4-1 7-2 11-1 2-3 4-4 7-1 0-1 0-2 1-1 0-1 1-2 1 1-2 3-3 4-6 1-2 2-4 2-6 0-5 1-8-1-12 0-2-1-3-2-5-1-1-2-3-3-4h-1v-2z" class="B"></path><path d="M574 219c1 0 2 0 2 1 2 3 4 6 5 9 2 3 3 10 3 14-2-4-4-9-7-13-1-2-6-6-6-8l2-2 1-1z" class="R"></path><path d="M574 219c1 0 2 0 2 1 2 3 4 6 5 9l-1 1c-2-1-5-5-6-6 0-1 0-2-1-3v-1l1-1z" class="S"></path><path d="M569 240v-1h1c1 1 2 2 3 2 1 2 2 3 2 5 2 4 1 7 1 12 0 2-1 4-2 6-1 3-3 4-4 6l-4 2h-1l1-1v-4l1-20h1c0-3 0-5 1-7z" class="T"></path><path d="M567 247h1l-1 20h-1l1-20z" class="L"></path><path d="M569 240v-1h1c1 1 2 2 3 2 1 2 2 3 2 5h-1c0 2 1 4 0 6-1-5-2-8-4-12h-1z" class="D"></path><path d="M574 252c1-2 0-4 0-6h1c2 4 1 7 1 12l-1-1v1l-1-1v2-7z" class="G"></path><path d="M574 259v-2l1 1v-1l1 1c0 2-1 4-2 6-1 3-3 4-4 6l-4 2h-1l1-1c4-4 6-6 8-12z" class="D"></path><path d="M565 321l1 2c-1 5-1 11-1 16v1l-1 1 1 2-1 1s-1 1-1 2l-2 1-6 6c-1-1-3-1-5-1l-1 5-1 6 1 2-1 3v1c-3 0-6-1-8 2-1 2-1 4-1 6 0 1 1 2 1 3-1 0-3 0-4 1-1 0-2 1-2 2-1 5 0 9 2 14l-1 1-2 1-3-1-1 1v1c1 2 1 3 1 5 1 2 0 5 0 8v1c0 1 0 2-1 3v1l1 1c-2 1-4 2-6 4-1-1-1-3-1-4-1 2-1 4-2 6 0 1-1 3-1 4 0 2 0 4 1 5 0 2-1 9-2 10l-1-1v1l-4 25c-1-3-2-6-2-9v10l-1-8-4-27c0-2 0-5-1-7 1-2 0-4 0-6 0-5 0-10 1-14v-2c-1-2 0-4-1-6l-1-4v-6-5l-1-1c1 0 1-1 2-2 0-1 1-1 1-2 0-2 1-2 2-4 1-3 1-6 3-8l1-1h-1v-2 1h1l2-7c-1-2-1-5 0-8 0-1 0-3 1-5 1-1 2-1 2-2 2-3 4-6 5-9 1 4 2 9 5 12l3 1c2-1 4-2 6-4s4-6 7-8h0c0-1 1-2 2-3v-2c0-1 0-1-1-2 2 1 3 3 6 3l1 1 1-1c-1 2-1 3-1 4 2 0 3 1 4 1 2-3 5-5 6-8 0-2 0-2 1-3l1-1v-1l1-3z" class="c"></path><path d="M528 391h-1v-14c0-4 1-10 3-13h1v1c-2 5-3 12-3 18 0 3 1 5 0 8z" class="H"></path><path d="M536 349h0l-3 9c0 2-1 4-2 6h-1c0-4 1-8 0-12h0l6-3z" class="B"></path><path d="M523 374c0 3 1 5 2 8 0 4-1 10-2 15v-1c-1-2-1-3-2-5-1 0 0-4 0-5 0-2 0-3 1-4l1-8z" class="P"></path><path d="M519 369c1-1 2-1 3-2v4l1 3-1 8c-1 1-1 2-1 4l-2-2-1-3-1-1c2-2 2-4 2-6v-1-4z" class="Y"></path><path d="M519 369c1-1 2-1 3-2v4c0 1-1 2-1 3-1-1 0-1-1-1h-1v-4zm-2 11c2-2 2-4 2-6l3 8c-1 1-1 2-1 4l-2-2-1-3-1-1z" class="O"></path><path d="M515 359c2-4 3-7 5-11 0 2 0 4 1 5v1c-1 2-1 5-1 7l1 1 1 5c-1 1-2 1-3 2l-1-2c0-2 0-3-1-4h-2v2h0-1 0l-1 1 2-7z" class="b"></path><path d="M521 362l1 5c-1 1-2 1-3 2l-1-2 1-2 1-1h0l1-2z" class="a"></path><path d="M515 365h0v-2h2c1 1 1 2 1 4l1 2v4 1c0 2 0 4-2 6l-1-4c-2 1-2 2-2 4l-1-1h0c-1-1-1-4-1-6 1-1 0-4 0-5l1-1h-1v-2 1h1l1-1h0 1z" class="v"></path><path d="M513 366l1-1h0 1l-1 3h0c1 3 2 5 2 8-2 1-2 2-2 4l-1-1h0c-1-1-1-4-1-6 1-1 0-4 0-5l1-1h-1v-2 1h1z" class="Z"></path><path d="M514 368c1 3 2 5 2 8-2 1-2 2-2 4l-1-1 1-11z" class="H"></path><defs><linearGradient id="Ev" x1="517.31" y1="352.338" x2="529.558" y2="360.079" xlink:href="#B"><stop offset="0" stop-color="#2b100f"></stop><stop offset="1" stop-color="#452b2a"></stop></linearGradient></defs><path fill="url(#Ev)" d="M520 348l2-4 4 7c3 4 1 14 0 19v1l-3-10c-1-1-1-2-2-3l-1 3c0-2 0-5 1-7v-1c-1-1-1-3-1-5z"></path><path d="M521 354l2 2-1 1c1 1 1 2 1 3v1h0c-1-1-1-2-2-3l-1 3c0-2 0-5 1-7z" class="u"></path><path d="M533 381c2-1 2-2 4-2 0 1 0 1-1 2-1 0-2 1-2 2-1 5 0 9 2 14l-1 1-2 1-3-1-1 1v1c1 2 1 3 1 5 1 2 0 5 0 8v1c0 1 0 2-1 3v1c-1 0-1 1-2 1-2-5-1-15 0-20 1-1 1-3 3-4v1c2 1 2 1 4 0h0l-1-3c-1-4-1-8 0-12z" class="X"></path><path d="M529 417c-1 0-1-1-1-3-1-4-1-11 1-15v1c1 2 1 3 1 5 1 2 0 5 0 8v1c0 1 0 2-1 3z" class="P"></path><path d="M565 321l1 2c-1 5-1 11-1 16v1l-1 1 1 2-1 1s-1 1-1 2l-2 1-6 6c-1-1-3-1-5-1l-1 5-1 6 1 2-1 3v1c-3 0-6-1-8 2-1 2-1 4-1 6 0 1 1 2 1 3-1 0-3 0-4 1 1-1 1-1 1-2-2 0-2 1-4 2-1 4-1 8 0 12l-1 1-2-1-2-2c1-3 0-5 0-8 0-6 1-13 3-18v-1c1-2 2-4 2-6l3-9h0l5-5c1-2 3-4 4-5-3 1-5 4-8 5h0c2-2 4-6 7-8h0c0-1 1-2 2-3v-2c0-1 0-1-1-2 2 1 3 3 6 3l1 1 1-1c-1 2-1 3-1 4 2 0 3 1 4 1 2-3 5-5 6-8 0-2 0-2 1-3l1-1v-1l1-3z" class="AA"></path><path d="M535 369v4c1 0 0 0 1-1l1-1v-2l1-1-1-1c1-1 1-1 3-2-2 3-2 6-3 9-1 2-1 2-1 3h-3c0-3 1-6 2-8z" class="T"></path><path d="M533 381h0v-1l-2 1v-2c1-3 2-13 4-15v5c-1 2-2 5-2 8h3c0-1 0-1 1-3 0 2 1 4 0 5-2 0-2 1-4 2z" class="H"></path><path d="M536 349c3-1 4-3 6-5-1 3-3 5-5 7-2 5-2 10-5 14h-1v-1c1-2 2-4 2-6l3-9z" class="c"></path><path d="M540 365l2-1c2 0 3 1 4 2l1 1 1 1v1c-3 0-6-1-8 2-1 2-1 4-1 6 0 1 1 2 1 3-1 0-3 0-4 1 1-1 1-1 1-2 1-1 0-3 0-5 1-3 1-6 3-9z" class="r"></path><path d="M565 321l1 2c-1 5-1 11-1 16v1l-1 1 1 2-1 1s-1 1-1 2l-2 1-6 6c-1-1-3-1-5-1l-1 5-1 6 1 2-1 3-1-1h1c-1-5-1-10 1-15 1-4 4-8 9-8 1-1 2 0 3-1 2-1 2-3 2-5 1-1 0-3 0-3l1-10v-1l1-3z" class="K"></path><path d="M552 348c2-2 5-2 8-3l4-4 1 2-1 1s-1 1-1 2l-2 1h-4l-5 1z" class="F"></path><path d="M552 348l5-1h4l-6 6c-1-1-3-1-5-1 1-1 1-3 2-4z" class="B"></path><path d="M552 333l1-1c-1 2-1 3-1 4 2 0 3 1 4 1 2-3 5-5 6-8 0-2 0-2 1-3l1-1-1 10s1 2 0 3v-1-5l-2 2c-3 4-8 5-12 7l-2-1-5 4c-2 2-3 4-6 5h0l5-5c1-2 3-4 4-5-3 1-5 4-8 5h0c2-2 4-6 7-8h0c0-1 1-2 2-3v-2c0-1 0-1-1-2 2 1 3 3 6 3l1 1z" class="P"></path><path d="M546 334h2c0 2 0 2-1 3h-1v-3z" class="D"></path><path d="M512 368c0 1 1 4 0 5 0 2 0 5 1 6h0l1 1c0-2 0-3 2-4l1 4 1 1 1 3 2 2c0 1-1 5 0 5 1 2 1 3 2 5v1c1 0 1 1 0 2l-1-1-1 1c0 2 0 4 1 6v9c0 2 0 3 1 4v1c-1 2-1 4-2 6 0 1-1 3-1 4 0 2 0 4 1 5 0 2-1 9-2 10l-1-1v1l-4 25c-1-3-2-6-2-9v10l-1-8-4-27c0-2 0-5-1-7 1-2 0-4 0-6 0-5 0-10 1-14v-2c-1-2 0-4-1-6l-1-4v-6-5l-1-1c1 0 1-1 2-2 0-1 1-1 1-2 0-2 1-2 2-4 1-3 1-6 3-8z" class="F"></path><path d="M511 393l1 2c0 4 1 10 0 15-1-4 0-7-1-10l-1-1c0-1 0-4 1-6z" class="J"></path><path d="M512 368c0 1 1 4 0 5-1 2-1 3-1 5 0 1 0 1 1 2-1 3-3 10-1 13-1 2-1 5-1 6l-1 5-1-1-1-4v7c-1-2 0-4-1-6l-1-4v-6-5l-1-1c1 0 1-1 2-2 0-1 1-1 1-2 0-2 1-2 2-4 1-3 1-6 3-8z" class="G"></path><defs><linearGradient id="Ew" x1="509.976" y1="387.714" x2="503.524" y2="389.786" xlink:href="#B"><stop offset="0" stop-color="#473e3b"></stop><stop offset="1" stop-color="#545355"></stop></linearGradient></defs><path fill="url(#Ew)" d="M506 382c0-1 1-1 1-2 0-2 1-2 2-4l-2 23v7c-1-2 0-4-1-6l-1-4v-6-5l-1-1c1 0 1-1 2-2z"></path><path d="M510 399l1 1c1 3 0 6 1 10-1 2-1 4-1 6 0 1 0 4-1 5v6l2 2 2-1c2 1 2 1 4 3v1c-2-1-2-2-4-2s-3 2-4 3 0 0 0 1l-1 1c0 1 0 1 1 2 0 1-1 3 0 4v4c1 1 1 1 1 2l1 3v10 10l-1-8-4-27c0-2 0-5-1-7 1-2 0-4 0-6 0-5 0-10 1-14v-2-7l1 4 1 1 1-5z" class="Q"></path><path d="M507 406v-7l1 4 1 1-1 3c0 4 1 9 0 13v12c0 2-1 3 0 5 0 1 0 2 1 4 0 2 0 2 1 4 1 1 1 1 1 2l1 3v10 10l-1-8-4-27c0-2 0-5-1-7 1-2 0-4 0-6 0-5 0-10 1-14v-2z" class="F"></path><path d="M507 406v-7l1 4 1 1-1 3v4h-1v-3-2z" class="D"></path><path d="M519 431l1-2c0 2 0 4 1 5 0 2-1 9-2 10l-1-1v1l-4 25c-1-3-2-6-2-9v-10l-1-3c0-1 0-1-1-2v-4c-1-1 0-3 0-4-1-1-1-1-1-2l1-1c0-1-1 0 0-1s2-3 4-3 2 1 4 2l1-1z" class="k"></path><path d="M519 431l1-2c0 2 0 4 1 5 0 2-1 9-2 10l-1-1v1c0-4 1-8 0-12l1-1z" class="q"></path><path d="M514 380c0-2 0-3 2-4l1 4 1 1 1 3 2 2c0 1-1 5 0 5 1 2 1 3 2 5v1c1 0 1 1 0 2l-1-1-1 1c0 2 0 4 1 6v9c0 2 0 3 1 4v1c-1 2-1 4-2 6 0 1-1 3-1 4l-1 2-1 1v-1c-2-2-2-2-4-3v-46-2z" class="AA"></path><path d="M514 380c0-2 0-3 2-4l1 4 1 1-2-1-2 2v-2z" class="d"></path><path d="M519 385l2 21v9 3s-1 1-1 2c-1-4 0-8 0-12 0-3-1-6-1-9-1-4 0-9 0-14z" class="k"></path><path d="M519 384l2 2c0 1-1 5 0 5 1 2 1 3 2 5v1c1 0 1 1 0 2l-1-1-1 1c0 2 0 4 1 6l-1 1-2-21v-1z" class="S"></path><path d="M521 391c1 2 1 3 2 5v1c1 0 1 1 0 2l-1-1-1 1v-8z" class="d"></path><path d="M522 405v9c0 2 0 3 1 4v1c-1 2-1 4-2 6 0 1-1 3-1 4l-1 2c-1-3 0-7 1-11 0-1 1-2 1-2v-3-9l1-1z" class="H"></path><path d="M613 221c1 1 1 2 2 3l1-1 14 7c1 1 2 1 3 1s1 0 2-1h1c2 0 4 0 7-1-2 1-4 2-5 4l1 1c-9 4-12 12-16 21-2 6-4 12-2 19l3 9c3 5 6 11 10 16 1 1 2 4 3 4-3-8-6-18-2-27 3-5 7-8 12-10 8-3 18-2 26 2 4 2 8 5 10 9-1-1-2-1-3-2v2c1 1 1 3 1 5s0 5-1 7c-2 8-8 17-13 24h0c-1-1 0-3-1-4l-1 1v1c0 3 0 5-2 8h-1c-1-1-2-1-4 0l-8 11-6 6v1h-1-1 0l1-2c0-1-1-3-1-5v-2c0 1-1 2 0 3v2c-1 2-1 2-2 3h-1l1 1c0 2-1 4-2 6h0-1l-2 2-1 1v-2c0-2 0-4-1-6v-7c0-1-1-2-3-3 1-1 3-1 3-2 1 1 1 2 1 3v3l2 1c-1-1-1-2-1-3h2l1-1h0-1l1-2-2-2v-1c1-1 2-3 3-4 1 1 1 1 3 1v-1c-1-2-4-5-5-8-7-8-14-17-22-24 0-2 0-3-1-4h0-1l-1-3v-11l-1-6c-1-3 0-12 1-15-1-2 0-5 0-7l1 1c1-2 1-4 1-5h0l2-4c0-4-3-8-3-13z" class="u"></path><path d="M664 272l1-5h1c1 2 0 7-1 9l-1-4z" class="V"></path><path d="M647 306h2c-2 3-4 5-7 7 1-2 3-5 5-7z" class="N"></path><path d="M635 345v-1c1-3 0-6 2-9v1h2l1 1c0 2-1 4-2 6h0-1l-2 2z" class="Y"></path><path d="M633 326c1 1 1 2 1 3v3l2 1v2 4 1h-1c-1-2-1-3 0-5v-1h0c-1 3-1 7-1 10 0-2 0-4-1-6v-7c0-1-1-2-3-3 1-1 3-1 3-2z" class="q"></path><path d="M635 230h1c2 0 4 0 7-1-2 1-4 2-5 4-2 1-5 2-7 2s-3 0-5-1h2 1v-1c2 0 3 0 5-1l-1-1c1 0 1 0 2-1z" class="Z"></path><path d="M611 264l2 2 1 18h0-1l-1-3v-11l-1-6z" class="I"></path><defs><linearGradient id="Ex" x1="663.647" y1="310.264" x2="661.96" y2="316.35" xlink:href="#B"><stop offset="0" stop-color="#929192"></stop><stop offset="1" stop-color="#ababac"></stop></linearGradient></defs><path fill="url(#Ex)" d="M666 306v3l-1 1v1c0 3 0 5-2 8h-1c-1-1-2-1-4 0l8-13z"></path><path d="M615 224l1-1 14 7c1 1 2 1 3 1l1 1c-2 1-3 1-5 1-5-3-10-5-14-9z" class="C"></path><path d="M613 221c1 1 1 2 2 3h0c1 2 3 4 4 6 2 6-1 14-3 20v-6-10c0-4-3-8-3-13z" class="W"></path><defs><linearGradient id="Ey" x1="666.739" y1="287.583" x2="645.761" y2="290.917" xlink:href="#B"><stop offset="0" stop-color="#4e4a4a"></stop><stop offset="1" stop-color="#65676a"></stop></linearGradient></defs><path fill="url(#Ey)" d="M664 272l1 4c-2 11-9 21-16 30h0-2c2-4 5-7 7-11 5-7 8-15 10-23z"></path><path d="M616 234v10 6c1 2 1 2 0 3v3c0 3-1 5-1 7l-1 1c0-1 0-5-1-6h0v8l-2-2c-1-3 0-12 1-15-1-2 0-5 0-7l1 1c1-2 1-4 1-5h0l2-4z" class="b"></path><path d="M612 242l1 1c0 5-1 11 0 15v8l-2-2c-1-3 0-12 1-15-1-2 0-5 0-7z" class="l"></path><defs><linearGradient id="Ez" x1="669.243" y1="284.111" x2="676.757" y2="308.889" xlink:href="#B"><stop offset="0" stop-color="#504849"></stop><stop offset="1" stop-color="#7a7b7d"></stop></linearGradient></defs><path fill="url(#Ez)" d="M675 279l1-7h1c1 2 2 2 2 4l2 6c0 2 0 5-1 7-2 8-8 17-13 24h0c-1-1 0-3-1-4v-3c0-1 2-4 2-5l5-12 1-4s1-5 1-6z"></path><defs><linearGradient id="FA" x1="680.321" y1="282.724" x2="669.539" y2="285.878" xlink:href="#B"><stop offset="0" stop-color="#3b3738"></stop><stop offset="1" stop-color="#5d5050"></stop></linearGradient></defs><path fill="url(#FA)" d="M675 279l1-7h1c1 2 2 2 2 4v3c0 2 0 4-1 6h0c-1 5-3 11-7 15 1-3 1-5 3-8v-1l-1-2 1-4s1-5 1-6z"></path><path d="M674 285c1 1 1 2 1 3l-1 3-1-2 1-4z" class="U"></path><path d="M675 279l1 1c0 2 0 5-1 8 0-1 0-2-1-3l1-6z" class="O"></path></svg>