<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="150 122 759 804"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#d2d1d0}.C{fill:#cbcac9}.D{fill:#aaa9a8}.E{fill:#dcdbdb}.F{fill:#b2b1b1}.G{fill:#1b1b1a}.H{fill:#bdbcbc}.I{fill:#c6c5c4}.J{fill:#797878}.K{fill:#9a9998}.L{fill:#e1e1e0}.M{fill:#a1a09f}.N{fill:#2b2b2a}.O{fill:#c2c1c0}.P{fill:#b8b7b7}.Q{fill:#ebeae9}.R{fill:#6d6d6c}.S{fill:#323131}.T{fill:#535353}.U{fill:#4a4949}.V{fill:#1e1d1d}.W{fill:#d7d6d6}.X{fill:#8a8989}.Y{fill:#828181}.Z{fill:#252424}.a{fill:#040404}.b{fill:#90908f}.c{fill:#414040}.d{fill:#f0efef}.e{fill:#636262}.f{fill:#131312}.g{fill:#5e5d5d}</style><path d="M680 545c1 0 1 1 2 1l-1 1-2-2h1z" class="B"></path><path d="M743 387c1-1 1-1 3-1l1 4h-3v-1l-1-2z" class="O"></path><path d="M674 544c4 2 6 8 7 11-2-2-7-7-7-11z" class="C"></path><path d="M743 387l1 2c-2 1-3 2-5 3-1-1-3-1-5-1 3-1 6-3 9-4z" class="D"></path><path d="M668 556h1v1c1 2 2 5 4 7l4 4c-2-1-4-2-7-3-1-1-2-2-2-4 1-2 1-2 1-4l-1-1z" class="N"></path><path d="M861 270h1 1s1-1 1-2 1-2 2-2c-1 5-3 8-6 12 0-1 1-2 0-4h-1-1l3-4z" class="S"></path><path d="M725 394h1c3 0 6-2 8-3 2 0 4 0 5 1-2 1-4 3-7 3h-4c-2 0-3 0-5-1h2z" class="F"></path><path d="M212 316h4l2 1c3 1 6 1 9 3 1 1 1 1 1 2h0c-5-2-11-2-16-6z" class="G"></path><path d="M167 252l1-1h2v1l4 1h-4c0 1 0 1 1 2l1 1v1l2 6h-1c-1-1-1-2-2-3l-1 1-3-9z" class="T"></path><path d="M860 220c4 3 9 9 11 14v4c-1-1-1-1-1-2l-2-1-2-3c1-1 0-1 1-2-1-1-2-3-3-4h1c-2-2-5-4-5-6z" class="c"></path><path d="M681 539l12 2h0c2 2 5 4 6 6-3-1-6-1-8-3l-2-2c-2 0-3 0-4-1h-1c-1-1-2-1-3-2z" class="C"></path><path d="M170 261l1-1c1 1 1 2 2 3h1l4 10-1 1v2c-1-1-2-4-3-4l-4-11z" class="U"></path><path d="M269 401c1-1 2-3 3-5 2-3 5-8 7-10l2 2h0c-1 2-4 8-6 8-3 2-4 4-6 5z" class="H"></path><path d="M174 272c1 0 2 3 3 4v-2l1-1c1 1 1 2 3 3 3 1 5 1 8 2 1 0 1 0 2 1-3 0-8-2-10-1 0 0-1 1-2 1l3 6h-1c-3-4-6-9-7-13z" class="c"></path><path d="M676 516l3-3 4 5c-1 3-3 4-4 6 0 1 0 1-1 1v-1h-1c0-1 1-3 1-4v-2l-2-2z" class="G"></path><path d="M207 308l6 3c2 2 4 4 7 5h-1c0 1 0 0-1 1l-2-1h-4c-2-1-5-4-6-5l1-2v-1z" class="Y"></path><path d="M207 309c3 3 6 5 9 7h-4c-2-1-5-4-6-5l1-2z" class="Z"></path><path d="M167 236h1v3h0c1-1 1-1 2-1 0 1 0 2-1 3-1 3 0 5 0 8h1l1 2h-1-2l-1 1c-2-6-3-10 0-16z" class="R"></path><path d="M773 417c2 3 4 5 4 8l1 3c1 3 2 9 1 12-2-2-2-6-3-8 0-5-2-10-5-14l2-1z" class="B"></path><path d="M691 569l-4 13c-4-4-8-9-14-12-1-1-5-3-5-4 4 0 13 7 17 10l2 2 3-7v-1l1-1z" class="N"></path><path d="M386 575l4 8c-2-1-2-2-3-3h-1v2h0v1c0 1 1 3 2 5v5c1 2 2 2 2 5-2-2-3-5-4-8-1-2-2-5-3-8v-4c1-2 2-2 3-3z" class="F"></path><path d="M386 590c1 3 2 6 4 8l7 19c0 1 1 2 0 3v-2l-1-1v-1c-1-2-1-3-2-4 0-2-1-4-2-6 0-2-1-4-2-5h-1c-1 1-1 2-1 4 0 0 1 0 1 1v1h0l1 1v1h0c0 1 1 1 1 2v1h0l1 1h0v1c1 1 1 2 1 3l1 1-1 1-8-21c1-3 1-5 1-8z" class="M"></path><path d="M368 566c2 0 3 1 5 1l5 1h5 0c-2 1-3 2-3 4-1 2-1 3-1 4-1 1-1 2-2 3l-3-8-1-3c-2-1-3-1-5-2z" class="G"></path><path d="M368 566c2 0 3 1 5 1l5 1h2c-1 1-2 3-3 4l-3-1-1-3c-2-1-3-1-5-2z" class="V"></path><path d="M182 285c6 6 12 12 19 18 2 2 3 4 6 5v1l-1 2c-2-1-4-3-5-5-7-6-14-13-20-21h1z" class="N"></path><path d="M544 814l12-32v6l-1 1v1l-1-1v1c0 1-1 2 0 4l-1 1v1c-1 4-4 8-5 12v2c-1 0-1 1-1 1l-1 4c-1 2-1 2-2 3-1-1-1-3 0-4z" class="C"></path><path d="M744 390h3c3 5 7 10 11 14 5 5 10 8 15 13l-2 1c-2-2-4-4-7-6l-9-8-6-7c-2-2-3-5-5-7z" class="P"></path><defs><linearGradient id="A" x1="646.374" y1="582.745" x2="639.971" y2="598.488" xlink:href="#B"><stop offset="0" stop-color="#80807e"></stop><stop offset="1" stop-color="#a8a7a6"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M645 583h1c1-1 2-1 3-2l-15 37v-2h1v-1h0c1-2 1-4 2-5v-1-1c1 0 1-1 1-2s1-1 1-1c1-1 1-3 2-4h0v-1-1h-2v1 1l-1 1-1 3c-1 1-1 2-1 3v-1c0-2 7-21 9-24z"></path><path d="M772 424h0c2 2 3 5 4 8h0 0c1 2 1 6 3 8-1 3-1 7-2 10-1 2-1 4-2 6-1-1-2-2-3-4l1-2c1 1 0 1 1 0l1-2v-8c-1-3-1-6-2-10 0-2-1-4-1-6z" class="M"></path><path d="M690 561h1c1-1 3-3 3-6s-1-3-3-5v-1c1 0 2 1 3 2h1c1 1 2 1 4 0-1 2-2 4-2 7 0 2-4 9-6 11l-1 1v1l-3 7-2-2-1-2 2 2c2-4 3-9 4-14v-1z" class="F"></path><defs><linearGradient id="C" x1="323.039" y1="453.875" x2="330.555" y2="462.129" xlink:href="#B"><stop offset="0" stop-color="#3b3b3a"></stop><stop offset="1" stop-color="#5e5d5d"></stop></linearGradient></defs><path fill="url(#C)" d="M327 444l2 4h1v2c-1 2-1 4-1 6l3 7-1 1v-1l-2 1v1l1 3c-3-5-5-10-7-15h0 1c0-2 1-3 1-5l-1-1c1-2 2-2 3-3z"></path><path d="M329 448h1v2c-1 2-1 4-1 6-1-1-2-3-2-4 0-2 0-3 1-4h0 1z" class="R"></path><path d="M685 550h0c2 1 4 3 5 5s0 5 0 6v1c-1 5-2 10-4 14l-2-2h0c-1-2-4-3-6-5l5 2c1-1 1 0 1-1 0-2 0-3 1-4 3-5 1-10 0-16z" class="G"></path><path d="M685 550c3 3 3 7 3 11-1 3-2 7-3 10h-2c1-1 1 0 1-1 0-2 0-3 1-4 3-5 1-10 0-16z" class="B"></path><path d="M176 227c1-1 2-1 3-1s2-2 4-3h3l-4 3-1 1c-3 0-5 2-6 4-4 6-3 13-5 18h-1c0-3-1-5 0-8 1-1 1-2 1-3-1 0-1 0-2 1h0v-3h-1c1-3 2-6 4-9l2 1 2-2 1 1z" class="J"></path><path d="M171 227l2 1 2-2 1 1c-2 2-6 5-6 8l-2 1h-1c1-3 2-6 4-9z" class="T"></path><path d="M269 401c2-1 3-3 6-5l-3 5h3l-5 5-7 6-3 3c-1 0-2 1-3 1h0c-2 1-3 2-4 4v-3c5-6 10-10 16-16z" class="D"></path><path d="M272 401h3l-5 5-7 6-3 3c-1 0-2 1-3 1h0c0-1 2-2 2-2l6-5 7-8z" class="c"></path><path d="M558 224c-1-3 1-4 2-7 1-1 1-1 1-2 1-2 2-3 2-5 1-1 1-1 1-2l1-2c1-1 1-2 1-2v-2c1-1 1-1 1-2h1l2-5v-1l5-13c1-1 1-2 2-3 0-2 1-5 2-7v-2c1-1 0 0 1-2h0 0v4c-1 1-1 2-1 3h1c0 1-1 3-2 5-1 3-3 7-4 11-2 5-5 11-7 16-1 3-3 9-5 11 0 1 0 1-1 2v-1c-2 2-2 4-3 6z" class="Q"></path><path d="M683 534v-1h0c1-1 2-1 3-1l3 2 5 4h0-2v1l1 2h0l-12-2-17-3v-1c3 0 9 1 12 1l1-1-1-1h7z" class="S"></path><path d="M676 536l1-1-1-1h7c2 1 6 2 7 3v2c-5-2-9-3-14-3z" class="C"></path><path d="M683 534v-1h0c1-1 2-1 3-1l3 2 5 4h0-2v1l1 2h0c-1-1-2-1-3-2v-2c-1-1-5-2-7-3z" class="W"></path><path d="M383 568l2 3 1 4c-1 1-2 1-3 3v4l3 8c0 3 0 5-1 8l-4-10-4-9c1-1 1-2 2-3 0-1 0-2 1-4 0-2 1-3 3-4z" class="b"></path><path d="M383 568l2 3c-3 2-3 3-4 5 0 4 3 9 0 12l-4-9c1-1 1-2 2-3 0-1 0-2 1-4 0-2 1-3 3-4z" class="e"></path><path d="M275 396c2 0 5-6 6-8 6 5 12 9 20 9l2 1c-2 1-5 0-8-1l-6-1c-1 0-2 0-3 1l1 1c-1 1-2 2-4 2-1 0-3-1-4 0-1 0-2 1-4 1h0-3l3-5z" class="S"></path><path d="M278 399c0-1 2-2 2-4h0c1-1 2-2 3-2l1 1 5 2c-1 0-2 0-3 1l1 1c-1 1-2 2-4 2-1 0-3-1-4 0-1 0-2 1-4 1l3-2z" class="R"></path><path d="M278 399c0-1 2-2 2-4h0c1-1 2-2 3-2l1 1h0c0 2 0 3-1 4-2 0-4 1-5 1z" class="T"></path><path d="M433 234l1-1h10c-6 5-11 9-16 15-3 3-5 7-8 11 0-3 1-5 2-7 3-7 8-12 11-18z" class="B"></path><path d="M847 293c2-3 5-6 8-9 1-2 3-4 5-6-1 3-4 6-6 8-7 9-15 16-23 23-3 3-7 7-10 9-5 2-9 2-14 2-2 0-4 0-6 1l-4 4-1-1v-2h1-2l-1-1h-1l2-1h1v-1h7c4 0 10 0 13-2h1c5-3 8-7 13-9 6-4 12-9 17-15z" class="U"></path><path d="M795 320l6 1-4 4-1-1v-2h1-2l-1-1h-1l2-1z" class="K"></path><path d="M643 568c3 0 6 0 8-1s5-1 7-2c-3 2-4 4-5 7l-4 9h0c-1 1-2 1-3 2h-1l1-3c0-1-1-2-1-3h-1c-2 1-2 2-4 2l-1 1c1-3 3-6 4-9l1-2-1-1z" class="R"></path><path d="M643 568c3 0 6 0 8-1s5-1 7-2c-3 2-4 4-5 7-2 1-2 2-3 3l-1 1h0v-4c-2-1-4-1-6-1l1-2-1-1z" class="V"></path><path d="M319 363c1 0 6 2 6 3h1c2 0 3 0 5-1h3l-2 4c-1 1-2 2-2 3l1 2-1 2 1 1c-1 2-1 2-2 3s-2 1-3 3-1 5-2 6l-2 1v1l-2 2-1-1c2-3 3-6 5-9 1-4 1-8 0-12 0-3-2-5-3-6l-2-2z" class="Y"></path><path d="M322 390l1-1c1-3 2-7 4-10 1-1 1-2 3-3l1 1c-1 2-1 2-2 3s-2 1-3 3-1 5-2 6l-2 1z" class="K"></path><path d="M319 363c1 0 6 2 6 3 1 1 2 2 2 3l2 2c0 1-1 2-1 3-2-1-3-2-4-3 0-3-2-5-3-6l-2-2z" class="R"></path><path d="M478 807c2 4 3 9 5 13l11 29 4 12c1 1 2 3 2 4-1 3 0 4 1 7l4 11c0 1 2 4 1 5 0 0-1-1-1-2-1-2-1-3-2-4v-2-1l-1-1h0v-1l-1-1v-2-1c-1-1-1-2-2-3l-1-3v-1c-2-3-2-5-3-8v-1l-12-32-4-12c-1-1-2-3-2-4s1-1 1-2z" class="P"></path><path d="M745 396c1 1 2 1 3 2l1-1 6 7 9 8c3 2 5 4 7 6 3 4 5 9 5 14h0 0c-1-3-2-6-4-8h0c0 2 1 4 1 6 0 0-1-2-1-3-1-2-3-3-4-5-4-5-9-12-14-15l-1-1-5-4-2-3-1-3z" class="T"></path><path d="M745 396c1 1 2 1 3 2l1-1 6 7c-2 0-3-2-4-2v1l2 1v2l-5-4-2-3-1-3z" class="c"></path><path d="M764 412c3 2 5 4 7 6 3 4 5 9 5 14h0 0c-1-3-2-6-4-8h0-1c0-3-1-4-3-7-1-1-3-2-3-3-1 0-1-2-1-2z" class="R"></path><path d="M500 865l11 31 1-1 8-21v5c-2 4-4 9-4 14 0 1-1 3-1 4l-4 11c-2-7-5-14-7-21-1-2-2-3-2-5l1 1v2c1 1 1 1 1 2l1 1h0v2l1 1v1l1 1v1c0 1 0 1 1 2v1 1c1 1 1 1 1 2h0l1 1v3l1 1h0c1-1 1-2 1-3l1-2 1-1h-2v1l-1 1-1-1v-3c-2 0-2-1-2-2v-1c-1-2-1-4-2-6 1-1-1-4-1-5l-4-11c-1-3-2-4-1-7z" class="M"></path><path d="M544 814c-1 1-1 3 0 4l-12 30-7 18c0 2-1 4-1 6l-1 1s0 1-1 2l-1 3v2s0-1 1-1c0 1-1 3-2 4-1 3-2 7-4 10 0-5 2-10 4-14v-5l24-60z" class="F"></path><path d="M135 167l-1-1h50c-1 1-2 2-3 2 0 1-1 0-2 1h-1c-4 1-10 0-15 0-3 1-6 2-8 2-2-1-3-1-5 0l-4-1-11-3z" class="Y"></path><path d="M178 169h51 0v1h-18c3 1 7 1 9 1h24s1-1 1 0h-21-17-37c-6 0-13 1-20 0 2-1 3-1 5 0 2 0 5-1 8-2 5 0 11 1 15 0z" class="M"></path><path d="M639 580l1-1c2 0 2-1 4-2h1c0 1 1 2 1 3l-1 3c-2 3-9 22-9 24v1 2l-1 1v1c0 1-1 1-1 2v1l-1 2c0 1-1 1-1 3v1c-1 0-1 1-1 2l-1 1c0 1-1 2-1 4-1 0-1 1-1 2l-1 3-4 7h0v-2l2-4c0-2 1-5 3-7v-1l1-1v-2c1-1 1-1 1-2s1-2 1-3l1-2c0-2 1-3 1-4h0c1-1 1 0 1-1v-2c1-1 1-2 1-4-1 1-1 1-1 2-1 2-1 4-2 6h-1l3-10c1-2 3-5 3-8h0c-2 2-3 6-4 9h0v-1c0-2 1-3 1-4s1-2 1-2l1-2v-1c1-1 1-3 2-4 0-1 1-2 1-3s0 0 1-1h0c0 1-1 3-1 4l-1 1v3-1l1-1 1-2c0-1 0 0 1-1h0 0l1-1c-1-1-1-1-1-2h1v-1-1-1h1v-3h0v-1l-2 3v1c-1 1-1 1-3 1 0 2-1 3-1 4-1 2-2 5-3 6l-1 2v1c-1 1-1 2-2 3h0c0-1 1-3 1-4l7-16z" class="H"></path><path d="M174 253c4 3 7 7 8 12l1 3 3 6c1 1 2 2 3 4-3-1-5-1-8-2-2-1-2-2-3-3l-4-10-2-6v-1l-1-1c-1-1-1-1-1-2h4z" class="P"></path><g class="K"><path d="M172 257c2 2 3 4 4 7 2 4 3 8 5 12-2-1-2-2-3-3l-4-10-2-6z"></path><path d="M174 253c4 3 7 7 8 12l1 3-2-2c0-1 0-1-1-2v1c0 2 2 3 3 6l-1 1c-1-1-1-2-2-3l-3-6-1 1c-1-3-2-5-4-7v-1l-1-1c-1-1-1-1-1-2h4z"></path></g><path d="M172 256v-2h3c1 3 1 6 2 9l-1 1c-1-3-2-5-4-7v-1z" class="D"></path><path d="M315 540l1 2c2 2 6 5 6 7v1c2 1 3 3 5 4l9 21 1-1v-3l1-1v-3c1 1 2 2 2 3 1 0 2 1 2 1 3-1 5-3 8-3h1c3-2 7-4 10-5v1c-3 3-8 5-12 7-4 3-8 7-12 10-3 0-2-6-3-9-1-2-1-4-2-6-2-4-4-9-7-12-4-5-8-8-12-12l2-2z" class="Y"></path><path d="M337 571l1-1v-3c1 1 2 2 2 3 1 0 2 1 2 1 3-1 5-3 8-3-2 2-5 4-7 5-2 2-3 3-5 4-1-1-1-1 0-2l-1-1v-3z" class="C"></path><path d="M337 571l1-1v-3c1 1 2 2 2 3 1 0 2 1 2 1l-4 4-1-1v-3z" class="c"></path><defs><linearGradient id="D" x1="308.876" y1="426.841" x2="327.85" y2="448.729" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#292828"></stop></linearGradient></defs><path fill="url(#D)" d="M306 425c2-1 4-1 7-1l4 2 1 1c0 1 1 2 2 2 2 5 5 10 7 15-1 1-2 1-3 3l1 1c0 2-1 3-1 5h-1 0l-13-27c-1-1-2-1-4-1h0z"></path><defs><linearGradient id="E" x1="268.159" y1="342.88" x2="292.989" y2="348.818" xlink:href="#B"><stop offset="0" stop-color="#040505"></stop><stop offset="1" stop-color="#605e5e"></stop></linearGradient></defs><path fill="url(#E)" d="M242 338h2c3 1 7 1 10 0h1c9-2 14-6 21-13 2 3 4 8 5 12 0 0-1 1-1 2 0 2 0 0 1 2v1h-1l4 10h0c1 2 2 3 3 5l2 5h0l2 4c-1 0-3 0-4-1v1c1 1 2 3 1 4h0s-1-2-1-3l-4-7-12-28c-4 3-8 5-13 7-6 1-11 1-16-1z"></path><path d="M289 362h2c1 1 2 3 3 4l1 1 1 1c2 4 4 10 7 14 2 5 2 8 4 12l4 1c-4 1-9 0-12-1l-11-24h0c1-1 0-3-1-4v-1c1 1 3 1 4 1l-2-4z" class="e"></path><path d="M289 362h2c1 1 2 3 3 4l1 1 1 1c2 4 4 10 7 14 0 3 1 5-1 8h-1c-2-2-3-5-4-8l-6-16-2-4z" class="K"></path><path d="M310 397c3-1 6-3 9-5l1 1v1 1l1 1v1h1l2 1c-3 1-5 1-8 1l-1 1c-2 2-4 3-7 3-3-2-9 1-12 0h-6v-1c-4 1-8 1-11 2l-1 1-1 1h-4-1-2l5-5h0c2 0 3-1 4-1 1-1 3 0 4 0 2 0 3-1 4-2l-1-1c1-1 2-1 3-1l6 1c3 1 6 2 8 1l-2-1c3 1 6 1 9 0z" class="X"></path><path d="M310 397c3-1 6-3 9-5l1 1v1 1l1 1h-2l-2 1h-1c-3 1-3 1-6 0zm-35 7c1 0 2-1 3-1 1-1 5-1 7-1 1 0 2 0 4-1 0-1 0 0 1-1h2-1c-1 1-1 1-1 2h0c-4 1-8 1-11 2l-1 1-1 1h-4l2-2z" class="K"></path><path d="M275 401c2 0 3-1 4-1 1-1 3 0 4 0 2 0 3-1 4-2l-1-1c1-1 2-1 3-1l6 1c-2 1-4 1-5 2-2 0-3 1-4 1l-2 1c-3-1-4 0-7 1l-2 2-2 2h-1-2l5-5h0z" class="J"></path><path d="M322 397l2 1c-3 1-5 1-8 1l-1 1c-2 2-4 3-7 3-3-2-9 1-12 0h-6v-1h0c0-1 0-1 1-2h1 5c1 0 2-1 3-1l12 1 10-3z" class="C"></path><path d="M292 400h5v1c-2 0-4 1-7 1 0-1 0-1 1-2h1z" class="M"></path><path d="M297 400c1 0 2-1 3-1l12 1c-5 0-10 1-15 1v-1z" class="K"></path><path d="M257 416h0c1 0 2-1 3-1v1 2c0 1-1 2-2 4l-3 5h1l-1 2c0 2 0 4-1 5v3h1v1l1 1c-1 1-1 1-2 1-1 1-1 1-2 1l-1 1 1 2c-1 1-2 2-2 4h2 2 0l-5 8-1-3c-2-5-3-9-3-14 0-3 0-5 1-7 0-2 1-4 2-5 1-4 3-7 5-10v3c1-2 2-3 4-4z" class="D"></path><path d="M255 427c0 1-1 3-1 4v1l-1-1c0-1 0-4 1-5 1-2 2-4 4-4l-3 5z" class="P"></path><path d="M251 442v-3c1-1 2-4 3-5v3h1v1l1 1c-1 1-1 1-2 1-1 1-1 1-2 1l-1 1z" class="K"></path><path d="M257 416h0c1 0 2-1 3-1v1c-2 3-5 5-7 8-3 5-3 12-3 19 0-1-1-1-1-1 1-8 0-15 4-22 1-2 2-3 4-4z" class="U"></path><path d="M253 417v3c-4 7-3 14-4 22 0 1 0 4-1 5h-1v-2-3c0-1-1-2-2-3 0-3 0-5 1-7 0-2 1-4 2-5 1-4 3-7 5-10z" class="I"></path><path d="M170 249c2-5 1-12 5-18 1-2 3-4 6-4l-1 1c-1 1-2 2-3 4 0 1-1 3 0 5 0 5 3 11 4 16 1 2 1 2 3 4h1v-1c2 8 3 14 8 22h-2l1 1h-1c-1-1-1-1-2-1-1-2-2-3-3-4l-3-6-1-3c-1-5-4-9-8-12l-4-1v-1h1l-1-2z" class="B"></path><path d="M171 251c4 0 6 1 9 4l1 1c3 6 4 12 5 18l-3-6-1-3c-1-5-4-9-8-12l-4-1v-1h1z" class="Z"></path><path d="M170 249c2-5 1-12 5-18 1-2 3-4 6-4l-1 1c-1 1-2 2-3 4-3 3-2 7-3 11 0 2 1 4 1 6 3 2 5 2 5 6-3-3-5-4-9-4l-1-2z" class="O"></path><path d="M744 389v1c2 2 3 5 5 7l-1 1c-1-1-2-1-3-2l1 3 2 3-5-1-5-2h-3v1c-5-1-10 1-14 1h-8l2 2v1c-2-1-4-2-6-2-1-1-2-1-3 0l-1-1c-4-1-7 0-11-1l6-2h1l2-1 1-1c0-1 1-1 2-1s1 0 2-1h0 1c3 1 6 1 9 1 2-1 3 0 5-1 2 1 3 1 5 1h4c3 0 5-2 7-3s3-2 5-3z" class="S"></path><path d="M709 394c3 1 6 1 9 1 2-1 3 0 5-1 2 1 3 1 5 1h4c-7 3-13 4-20 2h0c-1-1-2-2-4-3h1z" class="B"></path><path d="M741 393l1-1 1 1c0 1 1 2 2 3l1 3 2 3-5-1-5-2h-3-1c-3 0-4 0-6-1 4-1 9-3 13-5z" class="g"></path><path d="M741 393l1-1 1 1c0 1 1 2 2 3l1 3h-1l-1-1-1-1-2-2 1-2h-1z" class="T"></path><path d="M738 399h7 1l2 3-5-1-5-2z" class="J"></path><defs><linearGradient id="F" x1="715.12" y1="393.986" x2="706.94" y2="404.933" xlink:href="#B"><stop offset="0" stop-color="#abadac"></stop><stop offset="1" stop-color="#d6d2d2"></stop></linearGradient></defs><path fill="url(#F)" d="M708 394h0c2 1 3 2 4 3h0v2c6 0 11 0 16-1 2 1 3 1 6 1h1v1c-5-1-10 1-14 1h-8l2 2v1c-2-1-4-2-6-2-1-1-2-1-3 0l-1-1c-4-1-7 0-11-1l6-2h1l2-1 1-1c0-1 1-1 2-1s1 0 2-1z"></path><path d="M708 394h0c2 1 3 2 4 3h0v2c-3 0-8-1-12-1h1l2-1 1-1c0-1 1-1 2-1s1 0 2-1z" class="J"></path><defs><linearGradient id="G" x1="640.595" y1="552.365" x2="666.147" y2="557.228" xlink:href="#B"><stop offset="0" stop-color="#b5b3b3"></stop><stop offset="1" stop-color="#dddcdc"></stop></linearGradient></defs><path fill="url(#G)" d="M658 541l2 3c0-1 1-2 2-3l3 3c0 1 2 3 2 5 1 2 2 4 2 7h-1l1 1c-2 2-4 4-6 5l-1 2h-1 0c-1 1-2 1-3 1-2 1-5 1-7 2s-5 1-8 1h-2c-3-1-6-1-9-2 2-1 5-1 7-1v-1h9l2-1v-2c4-4 4-8 6-14v-3l1-1v-1l1-1z"></path><path d="M667 549c1 2 2 4 2 7h-1v-1c0-1 0-1-1-1v3l-1-1c0-2 0-5 1-7z" class="C"></path><path d="M639 565h8-1c-2 1-4 1-6 1h-1 0 0l2 2c-3-1-6-1-9-2 2-1 5-1 7-1z" class="D"></path><path d="M658 541l2 3c2 4 3 8 1 12-1 4-4 6-8 7l-6 2h-8v-1h9l2-1v-2c4-4 4-8 6-14v-3l1-1v-1l1-1z" class="S"></path><path d="M657 543c1 1 1 3 2 5l-3-1v-3l1-1z" class="L"></path><path d="M656 547l3 1v2l-1 5c-1 2-1 5-3 7l-5 1v-2c4-4 4-8 6-14z" class="Q"></path><path d="M658 555c0-1-1-2-1-3s1-1 2-2l-1 5z" class="L"></path><defs><linearGradient id="H" x1="877.308" y1="144.68" x2="827.692" y2="188.32" xlink:href="#B"><stop offset="0" stop-color="#79737a"></stop><stop offset="1" stop-color="#a3a69f"></stop></linearGradient></defs><path fill="url(#H)" d="M830 170c-5 1-10 0-14 0h-1c-2 0-4-1-5 0h-1-1-2c-2-1-3 0-4-1h1c2-1 6-1 8-1-2 0-2 0-3-1h-20-1-16c-3 0-8 1-11 0l139-1h1 0c-3 2-8 4-11 4-4 1-8 1-12 1h0 3v-1c-2 0-5 1-7 0-5-1-11-1-16-1h-27v1z"></path><path d="M857 169h21c3 0 8-1 10 1h1 0c-4 1-8 1-12 1h0 3v-1c-2 0-5 1-7 0-5-1-11-1-16-1z" class="X"></path><path d="M683 518c4 5 7 9 13 12 2 1 4 3 7 3 2 1 5 1 8 1 1 0 3-1 4 0 0 2-1 4-3 6-4 7-11 10-15 18 0-3 1-5 2-7-2 1-3 1-4 0l1-1c2-1 3-3 6-4h1c0-2-1-3-2-4 0-1-1-1-2-1-1-1-3-2-4-2l-1-1-5-4-3-2 2-1c-5-2-8-2-14-2 2-1 3-2 4-4 1 0 1 0 1-1 1-2 3-3 4-6z" class="g"></path><path d="M702 546l1 1c2-2 3-3 5-3-2 3-5 6-9 7-2 1-3 1-4 0l1-1c2-1 3-3 6-4z" class="I"></path><path d="M711 534c1 0 3-1 4 0 0 2-1 4-3 6h-2 0-1c-1-1-1-2-1-4-1 0-2-1-3-2h6z" class="Z"></path><path d="M709 540v-4c2-1 2-1 4-1l1 1c-1 1-1 0-1 1-2 0-2 1-3 3h0-1z" class="V"></path><path d="M688 531c5 2 9 4 13 5 2 0 4 2 5 3l-1 3c0 2-1 3-2 4 0-2-1-3-2-4 0-1-1-1-2-1-1-1-3-2-4-2l-1-1-5-4-3-2 2-1z" class="B"></path><path d="M339 521h0c2 1 3 1 4 1l5 5c1 1 2 1 3 2l5 1c-4 1-8 1-11 1h-5c0 1-1 2-2 2s-2 0-3 1c2 1 8 0 10 0-2 2-9 1-12 2h-1c-3 1-7 4-10 5v1c1 1 1 0 2 0 0 1 0 1-1 2s-1 1-1 3c2 2 4 3 7 5 1-1 2-1 3-1-1 1-1 2-2 2v1 1l-1 1-1-2h-1c-2-1-3-3-5-4v-1c0-2-4-5-6-7l-1-2-2 2c-1-3-3-6-4-9h12c5-1 8-3 12-6l6-6z" class="U"></path><path d="M327 554c-2-1-3-3-5-4v-1c2 1 5 4 7 3h0c1-1 2-1 3-1-1 1-1 2-2 2v1 1l-1 1-1-2h-1z" class="P"></path><path d="M315 540c-1-1-2-3-3-4v-1-1h1c0 1 0 1 1 1 1 1 3 0 4 0-1 1-1 1-3 2v1h1c0 1 1 2 1 3l-1 1-1-2z" class="f"></path><path d="M333 527c0 2 0 2-2 4h-2l-1 2c-1 1-3 1-5 1l-2-1c5-1 8-3 12-6z" class="T"></path><path d="M322 547l-3-5c0-3 0-3 2-5 3 0 8-3 12-4 1 0 3-3 5-4 1-1 4-1 6-1s4 1 6 1h1l5 1c-4 1-8 1-11 1h-5c0 1-1 2-2 2s-2 0-3 1c2 1 8 0 10 0-2 2-9 1-12 2h-1c-3 1-7 4-10 5v1c1 1 1 0 2 0 0 1 0 1-1 2s-1 1-1 3z" class="P"></path><defs><linearGradient id="I" x1="221.404" y1="144.189" x2="249.048" y2="190.73" xlink:href="#B"><stop offset="0" stop-color="#837c84"></stop><stop offset="1" stop-color="#c0c5bc"></stop></linearGradient></defs><path fill="url(#I)" d="M184 166l117 1c-1 1-2 1-3 1h2v1c-5 0-12-1-17 0-2 0-4 0-7 1h-11c-2 0-3-1-4 0h-22c-3 0-7 1-10 0v-1h0-51 1c1-1 2 0 2-1 1 0 2-1 3-2z"></path><defs><linearGradient id="J" x1="275.215" y1="448.603" x2="276.785" y2="424.397" xlink:href="#B"><stop offset="0" stop-color="#bbbabe"></stop><stop offset="1" stop-color="#e7e6e4"></stop></linearGradient></defs><path fill="url(#J)" d="M285 416h1l-1 2 1 1v1 1 1c-1 0-2 0-3 1v1l1 1 1 1 1 1c6 0 14-2 20-4v1 1h0c-2 1-2 1-3 2h-1-1c-1 1-3 2-4 2h-1s-1 1-2 1h0-2l-2 1h-2-1-1c-1 0-2 1-3 1h-2c-1 0 0 0-2 1h-1 0l-1 1c-1 0-3 1-5 1-1 1-1 1-2 1-3 2-6 3-9 6-3 2-5 4-7 6h0-2-2c0-2 1-3 2-4l-1-2 1-1c1 0 1 0 2-1 1 0 1 0 2-1l4-4c3-3 7-5 10-7 4-3 7-5 10-9l4-3h0 1z"></path><path d="M285 416h1l-1 2 1 1v1 1 1c-1 0-2 0-3 1v1l1 1 1 1 1 1c6 0 14-2 20-4v1c-4 1-9 3-13 4-4 0-7 0-10 1-8 0-16 4-22 8-4 2-6 5-9 7l-1-2 1-1c1 0 1 0 2-1 1 0 1 0 2-1l4-4c3-3 7-5 10-7 4-3 7-5 10-9l4-3h0 1z" class="X"></path><path d="M277 424v4h0l-2 1-3 1c-1 1-1 1-2 1l-1-1 8-6z" class="D"></path><path d="M285 416h1l-1 2 1 1v1 1 1c-1 0-2 0-3 1h-4 0l5-7h1z" class="H"></path><path d="M279 423h0 4v1l1 1v1 1h-1l-6 1h0v-4l2-1z" class="P"></path><path d="M279 423h4v1l-1 1-2 1h-1v-3z" class="C"></path><path d="M260 415l3-3 7-6h2 1 4c-1 0-3 2-4 2v1c2 0 2-1 4-1v1 1l1 1h0 0c-1 1-1 1-1 2h-1l-2 1v1c2 0 2-1 4-1 1-1 1-1 2-1l-1 1v1l-1 2h2v2c-3 4-6 6-10 9-3 2-7 4-10 7l-4 4-1-1v-1h-1v-3c1-1 1-3 1-5l1-2h-1l3-5c1-2 2-3 2-4v-2-1z" class="B"></path><path d="M255 429h1l1 1v2c0 1-1 2-1 3l1 1c1 0 0 0 2-1h1l-4 4-1-1v-1h-1v-3c1-1 1-3 1-5z" class="C"></path><path d="M274 415c2 0 2-1 4-1 1-1 1-1 2-1l-1 1v1l-1 2c-1 0-2 2-4 2v-1l1-1h-1c-1 0-1 0-1 1-1 0-2 0-2-1l-1 1h-2 0c2-2 4-3 6-3z" class="F"></path><path d="M273 409c2 0 2-1 4-1v1 1l1 1h0 0c-1 1-1 1-1 2h-1l-2 1v1c-2 0-4 1-6 3h0v1h-2c1-1 1-1 1-2h0v1l-2 1v-2c0-1 1-1 2-2v-1c0-1 0 0 1-1l5-4z" class="O"></path><path d="M260 415l3-3 7-6h2 1 4c-1 0-3 2-4 2v1l-5 4c-1 1-1 0-1 1-1 1-3 2-4 3-3 3-5 6-7 10h-1l3-5c1-2 2-3 2-4v-2-1z" class="M"></path><path d="M263 417c1-3 3-5 6-7 1-1 2-3 4-2v1l-5 4c-1 1-1 0-1 1-1 1-3 2-4 3z" class="D"></path><path d="M861 228c2 1 3 2 5 4l2 3 2 1c0 1 0 1 1 2v-4c1 3 2 5 2 8-1 2-2 5-2 7l-5 17c-1 0-2 1-2 2s-1 2-1 2h-1-1l-3 4h1l-2 1-8 1h0 0c1-1 2-2 3-2v-1l-2 1v-1l5-3-2-1c-1 1 0 1-1 1 1-5 2-10 5-14l-1-2c3-2 3-6 4-10h0l3-4 2 1v-3c0-2-1-3-2-4 0-1 0-2-1-2-1-2-1-3-1-4z" class="U"></path><path d="M855 266c-1-1 0-2 0-2 1-5 4-9 9-10 2-1 2 0 4-1h0c0 2-1 3-1 5l-1 2h-1v-3l-1 1h-1c-1 1-2 2-4 3v1l-3 6-1-1v-1z" class="Y"></path><path d="M859 261c0-2 1-3 2-4l2 1c-1 1-2 2-4 3z" class="b"></path><path d="M861 228c2 1 3 2 5 4l2 3c2 5 4 9 2 14-1 1-1 2-2 3h-2l2-2h0c0-1 1-1 1-2 0-2 0-4-1-5 0-1-2-1-2-1v-1c0-1-1-2-1-3 0-2-1-3-2-4 0-1 0-2-1-2-1-2-1-3-1-4z" class="Y"></path><path d="M865 238c0 1 1 2 1 3v1s2 0 2 1c1 1 1 3 1 5 0 1-1 1-1 2h0l-2 2-6 1-3 3-1-2c3-2 3-6 4-10h0l3-4 2 1v-3z" class="C"></path><path d="M860 253l2-1c2-2 3-2 6-2l-2 2-6 1z" class="H"></path><path d="M865 238c0 1 1 2 1 3v1s2 0 2 1c1 1 1 3 1 5 0 1-1 1-1 2l-1-3c0-1-1-1-2-2 0 1-1 2-2 2h-1l1-2c1-1 1-2 2-4h0v-3z" class="F"></path><path d="M864 258l1-1v3h1c-1 3-2 5-4 7l-1 3-3 4h1l-2 1-8 1h0 0c1-1 2-2 3-2v-1l-2 1v-1l5-3-2-1 2-3v1l1 1 3-6v-1c2-1 3-2 4-3h1z" class="M"></path><path d="M864 258l1-1v3h1c-1 3-2 5-4 7l2-9z" class="J"></path><path d="M859 262h1c0 3-1 4-3 6-1 1-1 2-2 2l-2-1 2-3v1l1 1 3-6z" class="D"></path><defs><linearGradient id="K" x1="637.836" y1="518.435" x2="646.906" y2="521.299" xlink:href="#B"><stop offset="0" stop-color="#696969"></stop><stop offset="1" stop-color="#8a8989"></stop></linearGradient></defs><path fill="url(#K)" d="M635 516l3 1c7 2 13 2 20 3l5-1-1 2c5-1 9-2 14-5l2 2v2c0 1-1 3-1 4h1v1c-1 2-2 3-4 4 6 0 9 0 14 2l-2 1c-1 0-2 0-3 1h0v1h-7l1 1-1 1c-3 0-9-1-12-1h-2-1-1c-1 0-1-1-2-1-3 0-5-1-8-3l-1-3-3-3-3-1v-2c-3 0-5-1-7-2h-1c0-1 0-1-1-2 0-1-1-1-1-2h2z"></path><path d="M658 520l5-1-1 2h-6c-1-1-2 0-3-1h5 0z" class="D"></path><path d="M635 516l3 1-1 1h2l-1 1c-1 0-1 0-2 1h-1c0-1 0-1-1-2 0-1-1-1-1-2h2z" class="g"></path><path d="M676 516l2 2v2l-1-1c-2 0-3 1-4 2-5 2-11 3-17 3-3-1-6 0-9-2v-1c3-1 6 0 9 0h6c5-1 9-2 14-5z" class="J"></path><path d="M656 524c6 0 12-1 17-3 1-1 2-2 4-2l1 1c0 1-1 3-1 4-5 3-11 5-17 4-3-1-6-2-8-3h0c1-1 3-1 4-1z" class="E"></path><path d="M643 522c3 1 6 3 9 3 2 1 5 2 8 3 6 1 12-1 17-4h1v1c-1 2-2 3-4 4 6 0 9 0 14 2l-2 1c-1 0-2 0-3 1h0v1h-7l1 1-1 1c-3 0-9-1-12-1h-2-1-1c-1 0-1-1-2-1-3 0-5-1-8-3l-1-3-3-3-3-1v-2z" class="Q"></path><defs><linearGradient id="L" x1="675.278" y1="521.056" x2="660.694" y2="532.068" xlink:href="#B"><stop offset="0" stop-color="#161517"></stop><stop offset="1" stop-color="#333331"></stop></linearGradient></defs><path fill="url(#L)" d="M643 522c3 1 6 3 9 3 2 1 5 2 8 3 6 1 12-1 17-4h1v1c-1 2-2 3-4 4-4 1-10 2-14 1-5-1-9-3-14-5l-3-1v-2z"></path><path d="M135 167l11 3c2 2 3 3 5 4l1 1h1l21 25c1 3 4 4 5 7 1 2 2 4 3 5l-5 5h-1c-10-10-19-21-28-32l-10-13c-1-1-3-3-3-5z" class="d"></path><defs><linearGradient id="M" x1="704.453" y1="480.081" x2="667.237" y2="497.331" xlink:href="#B"><stop offset="0" stop-color="gray"></stop><stop offset="1" stop-color="#b2b1b0"></stop></linearGradient></defs><path fill="url(#M)" d="M698 445c1 0 2-1 3-1l1 2 1-2 1 1-1 1v4h1c0-1 1-1 2-2l-11 23c-3 9-7 17-10 25l-5 11-3 6c-1 2-2 2-4 3-1 1-3 2-4 2s0 0-1-1h-1c0-3 1-6 3-9l19-42 3-7 4-9v-1l1-2 1-2z"></path><path d="M692 459h0c1 0 2-1 3-2 1 1 1 2 1 3 0 2-3 6-4 8 0-1 0-2 1-3v-3h-1c-1 1-2 2-2 3l-1 1 3-7z" class="M"></path><defs><linearGradient id="N" x1="703.295" y1="451.003" x2="695.848" y2="462.771" xlink:href="#B"><stop offset="0" stop-color="#3c3c3c"></stop><stop offset="1" stop-color="#6e6d6c"></stop></linearGradient></defs><path fill="url(#N)" d="M698 445c1 0 2-1 3-1l1 2 1-2 1 1-1 1v4h1c0-1 1-1 2-2l-11 23c0-1 0-2 1-3h0l1-1v-1-1h0v-1l-1 2-1-1 1-1 3-10c0-2 1-3 0-4h-3v-1l1-2 1-2z"></path><path d="M689 466l1-1c0-1 1-2 2-3h1v3c-1 1-1 2-1 3l-18 42c0 2-1 3-3 4h-1v-2-4l19-42zM561 218v1c-1 1-1 2-1 3l-1 3c1 1 2 1 3 2 1-1 2-2 2-3 2-2 3-5 5-8l1-2c0-2 1-3 2-4 0-1 1-1 2-2-2 5-5 10-7 15h0l7-7h26l1 1c-1 3 0 6-4 7l-1 3h1v2l-1 1h-1l1 1h-1l1 1c6 3 14 13 15 19l1 3-8-10-7-7c-1-1-4-4-5-4-2-1-6 0-8 0-10 0-20-1-31 0 2-3 3-5 4-8l1-1c1-2 1-4 3-6z" class="I"></path><path d="M574 216h26c-1 3-1 6-3 8-3 0-28 1-29 0l-1-1 7-7zM345 534c5 0 10 1 15 1h0 1l-2 1c-1 3-2 3-5 5l1 1-1 2c-2 2-4 6-5 8v10c1 0 1 0 1-1 1-1 1-2 1-3l1-2 1-1v-1c0-1 0-2 1-3 1-2 0 0 1-1v-2l1-1v-1-1c1-2 3-3 5-4 0 0 1 0 2 1 0-2 1-3 3-4 0-1 1 0 3 0l-2 2c-3 3-5 7-6 12l-1-3c0 3-1 6-1 9v4l2 1c-3 1-7 3-10 5h-1c-3 0-5 2-8 3 0 0-1-1-2-1 0-1-1-2-2-3v3l-1 1v3l-1 1-9-21h1l1 2 1-1v-1-1c1 0 1-1 2-2-1 0-2 0-3 1-3-2-5-3-7-5 0-2 0-2 1-3s1-1 1-2c-1 0-1 1-2 0v-1c3-1 7-4 10-5h1c3-1 10 0 12-2z" class="E"></path><path d="M330 543l6-1c-1 1-2 3-4 3h-3l-1-1c1 0 1-1 2-1z" class="B"></path><path d="M327 554h1l1 2 1-1v-1-1c0 2 0 2 1 4h0v1h0l-2-1c2 3 3 6 4 8v-2c0-1-1-4 0-5v-2c0 2 0 4 1 6 0 2 1 4 2 6l1 3v3l-1 1-9-21z" class="H"></path><defs><linearGradient id="O" x1="343.783" y1="535.622" x2="343.217" y2="541.878" xlink:href="#B"><stop offset="0" stop-color="#2e2c2c"></stop><stop offset="1" stop-color="#4b4b4b"></stop></linearGradient></defs><path fill="url(#O)" d="M360 535h1l-2 1c-7 2-16 2-23 6l-6 1c2-1 3-3 5-4 5-3 13-3 19-3 2 0 4 0 6-1z"></path><path d="M363 542c0-2 1-3 3-4 0-1 1 0 3 0l-2 2c-3 3-5 7-6 12l-1-3 1-3-1-1c-4 5-3 12-8 16v2l-1 1-1-1c1-1 1-1 1-2v-1c1-1 1-2 2-3l2-6c1-2 1-3 2-5s2-3 4-4h1 1z" class="B"></path><path d="M351 564l1-1v-2c5-4 4-11 8-16l1 1-1 3c0 3-1 6-1 9v4l2 1c-3 1-7 3-10 5l-1-1c-1 0-1-1-1-1v-2h1 1z" class="D"></path><path d="M349 566h2c3-3 6-4 8-8v4l2 1c-3 1-7 3-10 5l-1-1c-1 0-1-1-1-1z" class="C"></path><path d="M338 551v-1l1-1c3-2 5-4 8-6v1h0c-5 4-9 6-9 12v3-1c1 3 3 8 2 12 0-1-1-2-2-3v3l-1 1-1-3c-1-2-2-4-2-6-1-2-1-4-1-6 1-2 3-4 5-5z" class="B"></path><path d="M338 551l-2 9c-1 0-1 0-1 2h0-1c-1-2-1-4-1-6 1-2 3-4 5-5z" class="Z"></path><path d="M334 562h1 0c0-2 0-2 1-2h1c0 2 1 5 1 7v3l-1 1-1-3c-1-2-2-4-2-6z" class="G"></path><path d="M334 562h1 0c0-2 0-2 1-2h1l-1 8c-1-2-2-4-2-6z" class="S"></path><defs><linearGradient id="P" x1="210.521" y1="308.951" x2="229.436" y2="282.5" xlink:href="#B"><stop offset="0" stop-color="#c3c2c1"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#P)" d="M191 279h1l-1-1h2c4 4 7 9 12 13 3 3 8 5 12 7 9 4 18 6 28 7 6-1 12-1 18-3l9-3c-5 4-10 11-15 15-6 4-14 6-20 6h-2-1c-1 1-1 1-3 2l-2-3-2 1c-3-2-6-2-9-3 1-1 1 0 1-1h1c-3-1-5-3-7-5l-6-3c-3-1-4-3-6-5-7-6-13-12-19-18l-3-6c1 0 2-1 2-1 2-1 7 1 10 1z"></path><path d="M263 302l9-3c-5 4-10 11-15 15-6 4-14 6-20 6h-2-1c-1 1-1 1-3 2l-2-3-2 1c-3-2-6-2-9-3 1-1 1 0 1-1h1c-3-1-5-3-7-5 2 0 3 0 4 1 3 0 7 1 10 3h0c1 1 1 2 2 3h5c2 0 3-1 4-1 1-1 1 0 1-1h-1l-1-1c1-2 2-3 3-4v-1l2-2h-3l9-1h1 1l2-1h1 2c1-1 1-1 2-1s2 0 2-1l-2 1h-5-7c6-1 12-1 18-3z" class="O"></path><path d="M213 311c2 0 3 0 4 1s3 2 4 3c2 1 5 2 8 4l-2 1c-3-2-6-2-9-3 1-1 1 0 1-1h1c-3-1-5-3-7-5z" class="M"></path><path d="M252 307c3 0 6-2 9-2l-5 4-1 1c-1 1-3 2-5 3h-2c1 0 2-1 2-1-2-1-6-1-9-1l4-3h0c1-1 2-1 3-1h1 3z" class="D"></path><path d="M252 307c3 0 6-2 9-2l-5 4h-3c-3 1-6 1-9 1h2c1-1 1-2 3-2 0 0 2-1 3-1z" class="K"></path><defs><linearGradient id="Q" x1="815.381" y1="287.515" x2="821.261" y2="311.067" xlink:href="#B"><stop offset="0" stop-color="#adabab"></stop><stop offset="1" stop-color="#d5d5d4"></stop></linearGradient></defs><path fill="url(#Q)" d="M859 274h1c1 2 0 3 0 4-2 2-4 4-5 6-3 3-6 6-8 9-5 6-11 11-17 15-5 2-8 6-13 9h-1c-3 2-9 2-13 2h-7c-6 0-11-1-16-5-2-1-4-3-6-5l1-1c-1-1-1-2-2-3l1-1 1-1 5 1c3 1 7 1 10 1 16-2 31-6 44-17 3-3 7-7 11-10 1 0 3-1 4-2l8-1 2-1z"></path><path d="M774 304c1 0 3 1 3 1l1 1c2 1 3 1 5 3 3 1 6 4 8 6-5-2-12-2-16-7-1-1-1-2-2-3l1-1z" class="K"></path><path d="M780 314c1 1 2 1 3 1 1 1 2 2 4 2h1 2v-1l1 1c2 1 5 0 7 0 7 1 11-3 16-6l2-2 4-2h4c-8 4-12 10-21 12h-7c-6 0-11-1-16-5z" class="C"></path><path d="M847 293c-5 6-11 11-17 15-5 2-8 6-13 9h-1c-3 2-9 2-13 2 9-2 13-8 21-12 2-1 5-3 7-5h0l-2 3 2-1c1 0 1-1 2-1 1-2-1 0 1-1 1-1 1-2 3-3 3-1 5-5 10-6z" class="M"></path><path d="M857 275v1l-3 2c-6 3-11 7-16 12-3 2-6 5-10 7l-10 5c-5 2-9 4-14 5-4 1-10 1-15 0-2 0-5 0-7-1l-5-1s-2-1-3-1l1-1 5 1c3 1 7 1 10 1 16-2 31-6 44-17 3-3 7-7 11-10 1 0 3-1 4-2l8-1z" class="d"></path><path d="M775 303l5 1c3 1 7 1 10 1h-1c-1 1-3 1-5 1h1 1c1 1 1 0 2 0h1v1c-2 0-5 0-7-1l-5-1s-2-1-3-1l1-1z" class="L"></path><defs><linearGradient id="R" x1="304.832" y1="402.224" x2="344.146" y2="430.806" xlink:href="#B"><stop offset="0" stop-color="#d7d6d6"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#R)" d="M320 393l2-2 1 1c5 4 12 7 18 9l14 5 4 1v1c1 0 2 1 3 1h1l-16 6c-6 2-13 3-19 6-4 1-7 3-10 6l-1-1-4-2c-3 0-5 0-7 1v-1-1c-6 2-14 4-20 4l-1-1-1-1-1-1v-1c1-1 2-1 3-1v-1-1-1l-1-1 1-2h-1-1 0l-4 3v-2h-2l1-2v-1l1-1c-1 0-1 0-2 1-2 0-2 1-4 1v-1l2-1h1c0-1 0-1 1-2h0 0l-1-1v-1-1c-2 0-2 1-4 1v-1c1 0 3-2 4-2l1-1 1-1c3-1 7-1 11-2v1h6c3 1 9-2 12 0 3 0 5-1 7-3l1-1c3 0 5 0 8-1l-2-1h-1v-1l-1-1v-1-1z"></path><path d="M355 406l4 1v1c-4 0-7 0-11-1 1 0 2-1 3 0h3l1-1h0z" class="Z"></path><path d="M306 423c2-1 5-2 7-3 1 0 1 0 3-1h2 1 1c-2 1-3 2-5 4l1 1s1 1 1 2l-4-2c-3 0-5 0-7 1v-1-1z" class="C"></path><defs><linearGradient id="S" x1="338.473" y1="394.214" x2="337.595" y2="405.409" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#252423"></stop></linearGradient></defs><path fill="url(#S)" d="M320 393l2-2 1 1c5 4 12 7 18 9l14 5h0l-1 1h-3c-1-1-2 0-3 0s-4-2-6-2c-5-2-10-4-14-6l-4-1-2-1h-1v-1l-1-1v-1-1z"></path><path d="M324 398l4 1c-4 1-8 1-12 2v1l1 1h0c2-1 4-1 6 0s3 0 4 0l1 1h-2c-7 1-16 3-21 9-5 5-7 9-16 10l-1-1-1 1h0c1 1 2 1 3 1l-1 1h1-1c-2 0-3 0-4 1l-1-1-1-1v-1c1-1 2-1 3-1v-1l3-3c2-3 6-6 9-8 2-1 3-1 5-2v-1c1 0 1 0 2-1l-1-1c-1 1-2 2-3 2l1-1 1-1c1-1 3-1 5-2 3 0 5-1 7-3l1-1c3 0 5 0 8-1z" class="E"></path><path d="M289 423c9-4 12-14 22-17 4-1 12-3 15-2-7 1-16 3-21 9-5 5-7 9-16 10z" class="Z"></path><path d="M278 405l1-1c3-1 7-1 11-2v1h6c3 1 9-2 12 0-2 1-4 1-5 2l-1 1-1 1c1 0 2-1 3-2l1 1c-1 1-1 1-2 1v1c-2 1-3 1-5 2-3 2-7 5-9 8l-3 3v-1-1l-1-1 1-2h-1-1 0l-4 3v-2h-2l1-2v-1l1-1c-1 0-1 0-2 1-2 0-2 1-4 1v-1l2-1h1c0-1 0-1 1-2h0 0l-1-1v-1-1c-2 0-2 1-4 1v-1c1 0 3-2 4-2l1-1z" class="D"></path><path d="M278 405l1 2c2 0 2-2 4-2 1-1 5 0 6 0-2 1-4 1-5 2-3 1-4 2-7 2v-1c-2 0-2 1-4 1v-1c1 0 3-2 4-2l1-1z" class="H"></path><path d="M277 413c1-1 3-2 5-3l1-1c1 0 3-1 4-1 1-1 2-1 2-1h1v1h2 1c-2 2-3 3-5 4-1 1-2 3-3 4h-1 0l-4 3v-2h-2l1-2v-1l1-1c-1 0-1 0-2 1-2 0-2 1-4 1v-1l2-1h1z" class="M"></path><path d="M279 415c2 0 3-1 4-1 1 1 1 1 1 2l-4 3v-2h-2l1-2z" class="H"></path><defs><linearGradient id="T" x1="247.158" y1="305.176" x2="291.058" y2="328.924" xlink:href="#B"><stop offset="0" stop-color="#a8a7a6"></stop><stop offset="1" stop-color="#ededec"></stop></linearGradient></defs><path fill="url(#T)" d="M290 279c1 0 2 1 3 2v1c1 2 5 7 7 8h0l2 3 1 1 2 2c1 0 2 0 3 1-4 0-8 1-12 3v1l5-1h4 4l3 6v1c1 1 0 1 2 1l-1 2c3 2 11 9 12 13l-1 2h-1c-1-4-5-7-8-9-4-2-7-2-11-2h-4c-9 0-16 2-23 9l-1 2c-7 7-12 11-21 13h-1c-3 1-7 1-10 0h-2l-1-1h-1c-5-2-11-10-12-15h0c0-1 0-1-1-2l2-1 2 3c2-1 2-1 3-2h1 2c6 0 14-2 20-6 5-4 10-11 15-15l-9 3-1-2c8-2 16-5 25-6h3 9c-2-3-8-6-9-9h-1l1-1-2-1v-1c1-1 1-2 1-3h1z"></path><path d="M227 320l2-1 2 3 2 1 9 9h-1c1 1 3 1 4 2h1 1c-3 1-5 0-8-1-2-2-4-3-6-4 1 2 3 4 5 6 1 0 1 1 2 1l1 1h-1c-5-2-11-10-12-15h0c0-1 0-1-1-2z" class="b"></path><path d="M290 311c2-2 4-1 6-1 1-1 2-1 3-1 3 0 7 0 10 1l1 1c-1 2-4 1-6 3h-4c-9 0-16 2-23 9v-1c0-1 1-2 2-3h1l2-1 1-1h-1c0-3 2-3 4-4h-3c-1 1-1 1-2 1 1-3 6-2 8-3h1z" class="L"></path><path d="M290 311c2-2 4-1 6-1 1-1 2-1 3-1 3 0 7 0 10 1l1 1c-1 2-4 1-6 3h-4l1-1v-1c1 0 2 0 3-1h2 0 2c-1 0-3 0-4-1-3-1-10 0-14 1z" class="C"></path><path d="M301 300h4 4l3 6v1c1 1 0 1 2 1l-1 2c3 2 11 9 12 13l-1 2h-1c-1-4-5-7-8-9-4-2-7-2-11-2 2-2 5-1 6-3l-1-1c-3-1-7-1-10-1 0-1 1-2 1-3 2-1 3-1 4-1h0v-1-1h0c-2 1-6 4-9 3h-1l5-1c1-1 3-2 3-3l-1-2z" class="H"></path><path d="M305 300h4l3 6v1c1 1 0 1 2 1l-1 2c3 2 11 9 12 13l-1 2h-1c-1-4-5-7-8-9v-1c0-1 0-2-1-3-2-3-3-2-5-3-1-1-1-2-3-2v-1h3v-1h-2l1-1c-1-2-1-2-2-2-1-1-1-1-1-2z" class="J"></path><path d="M262 318c3-5 10-13 15-15l-4 4h1c2 2 3 1 5 1-1 3-6 7-9 9-1 1-2 3-4 5h-1l-1 1c-1 1-4 3-6 4h-1l-1-1c-2 1-9 3-10 5 2 0 3 1 5 2h0-1l1 1h-1-2v-1h1c-1-1-2-1-2-2h-2c-1-1-1-1-2-1h-1 0c0 1 1 1 1 1l3 2c-1 0-2-1-4-1l-9-9h3c1 1 3 1 4 2 6 0 12 0 17-3 2-1 4-3 5-4z" class="D"></path><path d="M262 318c3-5 10-13 15-15l-4 4-4 4c-2 3-3 6-6 9-1 2-5 5-7 6s-9 3-10 5v-1c-2 0-3 0-4-1v-1h2c5 1 9-1 13-4l2-1 4-4-1-1z" class="F"></path><path d="M273 307h1c2 2 3 1 5 1-1 3-6 7-9 9-1 1-2 3-4 5h-1l-1 1c-1 1-4 3-6 4h-1l-1-1c2-1 6-4 7-6 3-3 4-6 6-9l4-4z" class="C"></path><path d="M269 311c0 2-1 3-2 5v1h0c-2 4-6 7-10 10l-1-1c2-1 6-4 7-6 3-3 4-6 6-9z" class="H"></path><path d="M286 296c6-2 13-1 19 0 1 0 2 0 3 1-4 0-8 1-12 3v1l5-1 1 2c0 1-2 2-3 3l-5 1c-3 0-7 2-10 3h-1l-1 1c-4 1-6 6-10 8v-1h0c-3 1-6 7-9 10-1 1-2 2-4 2l1-1c3-2 5-4 6-6 2-2 3-4 4-5 3-2 8-6 9-9-2 0-3 1-5-1h-1l4-4h0c2 0 3-1 4-2s2-2 3-2l3-2c1 0 2 0 2-1h-2-1z" class="Q"></path><path d="M286 296c6-2 13-1 19 0 1 0 2 0 3 1-4 0-8 1-12 3v1s-1 0-2 1c-1 0 0 0-1-1 1 0 0 0 1-1s2-1 2-2h-4-1-2l-3 2c-2 0-3 1-5 1 1-1 2-2 3-2l3-2c1 0 2 0 2-1h-2-1z" class="C"></path><defs><linearGradient id="U" x1="238.472" y1="314.579" x2="281.434" y2="303.494" xlink:href="#B"><stop offset="0" stop-color="#020202"></stop><stop offset="1" stop-color="#242322"></stop></linearGradient></defs><path fill="url(#U)" d="M290 279c1 0 2 1 3 2v1c1 2 5 7 7 8h0l2 3 1 1 2 2c-6-1-13-2-19 0h1 2c0 1-1 1-2 1l-3 2c-1 0-2 1-3 2s-2 2-4 2h0c-5 2-12 10-15 15-1 1-3 3-5 4-5 3-11 3-17 3-1-1-3-1-4-2h-3l-2-1c2-1 2-1 3-2h1 2c6 0 14-2 20-6 5-4 10-11 15-15l-9 3-1-2c8-2 16-5 25-6h3 9c-2-3-8-6-9-9h-1l1-1-2-1v-1c1-1 1-2 1-3h1z"></path><defs><linearGradient id="V" x1="260.197" y1="305.372" x2="266.322" y2="313.846" xlink:href="#B"><stop offset="0" stop-color="#c7c4c5"></stop><stop offset="1" stop-color="#efeeee"></stop></linearGradient></defs><path fill="url(#V)" d="M235 320v1c2 1 5 1 8 1 5 0 10-1 14-4 4-2 7-6 9-9 5-6 12-12 20-13h1 2c0 1-1 1-2 1l-3 2c-1 0-2 1-3 2s-2 2-4 2h0c-5 2-12 10-15 15-1 1-3 3-5 4-5 3-11 3-17 3-1-1-3-1-4-2h-3l-2-1c2-1 2-1 3-2h1z"></path><path d="M687 435l2 2 3 3c1 0 4 2 4 3 1 1 0 3 1 4l-1 2v1l-4 9-3 7-19 42c-2 3-3 6-3 9h1c1 1 0 1 1 1l-6 1-5 1c-7-1-13-1-20-3l-3-1v-1l3-6 6-14c9-18 19-36 30-52l1-2c2-1 3-1 5-1 1 0 2 0 3-1l4 2c0-1-1-2-2-3 1-1 1-2 2-3z" class="a"></path><path d="M687 435l2 2 3 3c1 0 4 2 4 3 1 1 0 3 1 4l-1 2c-2-3-6-5-9-8 0-1-1-2-2-3 1-1 1-2 2-3z" class="X"></path><defs><linearGradient id="W" x1="312.576" y1="221.93" x2="315.066" y2="163.495" xlink:href="#B"><stop offset="0" stop-color="#d0cfce"></stop><stop offset="1" stop-color="#f9f9f8"></stop></linearGradient></defs><path fill="url(#W)" d="M224 171l1 1 232-3v1l2 6s1 1 1 2 0 0 1 1v2 1l6 14c1 4 3 8 5 12h0c0-2-2-7-3-10h1c1 4 2 8 4 12 1 1 0 2 1 3v1l1 1v1c1 1 0 1 1 2 0 2 2 6 3 8s3 5 3 7h-32-7-10c1-2 0-1 0-3-1-1 0-7 0-9l1-4c1-1 2 0 3 0h25 1c2 3 4 5 7 7h0c-2-3-3-6-4-9l-7-16-10-22-12 1-228-1c-4-1-9 0-14 0h-27c-5 0-11 0-16-2h-1l-1-1c-2-1-3-2-5-4l4 1c7 1 14 0 20 0h37 17z"></path><path d="M435 217c1-1 2 0 3 0h25 1c2 3 4 5 7 7h0c2 2 4 5 8 6 1 0 1-1 1-2l2 4c-10 1-21 0-31 1h-7-10c1-2 0-1 0-3-1-1 0-7 0-9l1-4z" class="O"></path><path d="M438 217h25 1c2 3 4 5 7 7l-1 1-29-1c0-1-1-2-2-3s-1-3-1-4z" class="E"></path><defs><linearGradient id="X" x1="687.095" y1="405.06" x2="666.777" y2="434.748" xlink:href="#B"><stop offset="0" stop-color="#0c0a09"></stop><stop offset="1" stop-color="#353535"></stop></linearGradient></defs><path fill="url(#X)" d="M735 399h3l5 2 5 1 5 4 1 1c5 3 10 10 14 15 1 2 3 3 4 5 0 1 1 3 1 3 1 4 1 7 2 10v8l-1 2c-1 1 0 1-1 0l-1 2-2-3c-6-7-14-12-23-14-5-2-10-3-15-4-5-2-10-5-15-5-3 2-4 7-6 10l-5 12c-1 1-2 1-2 2h-1v-4l1-1-1-1-1 2-1-2c-1 0-2 1-3 1l-1 2c-1-1 0-3-1-4 0-1-3-3-4-3l-3-3-2-2h0 3 0c0-3 0-4-1-6v-1c-2-1-3-2-3-4l-5-3c-2 0-5-1-7-3-1 1-2 0-2 0-2-1-3-1-4-2v-1c-1 0-2-1-3-2-8-2-16-1-25 0h0-1-2l-1 1-1-1c2-1 4-2 6-2l23-6h1 0l1 1v1h1c2 1 6 0 9-1l18-6c4 1 7 0 11 1l1 1c1-1 2-1 3 0 2 0 4 1 6 2v-1l-2-2h8c4 0 9-2 14-1v-1z"></path><path d="M703 430l1 1c-1 5-4 10-6 14l-1 2c-1-1 0-3-1-4 0-1-3-3-4-3 3 0 4 0 6-2 1 0 1-1 2-2v-2c2-1 3-2 3-4z" class="M"></path><path d="M665 413c11 3 21 6 31 11l1 3h-1l-1-1h-1l-14-6c-2-1-4-2-6-2-1 1-2 0-2 0-2-1-3-1-4-2v-1c-1 0-2-1-3-2z" class="Q"></path><path d="M674 418c2 0 4 1 6 2l14 6h1l1 1h1l-1-3c3 2 5 4 7 6h0c0 2-1 3-3 4v2c-1 1-1 2-2 2-2 2-3 2-6 2l-3-3-2-2h0 3 0c0-3 0-4-1-6v-1c-2-1-3-2-3-4l-5-3c-2 0-5-1-7-3z" class="F"></path><path d="M696 424c3 2 5 4 7 6-1 0-2 1-2 1-3-1-5-3-7-5h1l1 1h1l-1-3zm-10 0c1 0 3 1 5 2 2 0 5 3 7 5-1 1-2 2-3 2l-1 1v-1l-2-2c-1-2-1-2-3-2v-1c-2-1-3-2-3-4z" class="B"></path><path d="M689 429c2 0 2 0 3 2l2 2v1l1-1c1 0 2-1 3-2 1 2 1 3 2 5-1 1-1 2-2 2-2 2-3 2-6 2l-3-3-2-2h0 3 0c0-3 0-4-1-6z" class="C"></path><path d="M689 437h3 2c1 1 2 2 4 1-2 2-3 2-6 2l-3-3z" class="H"></path><defs><linearGradient id="Y" x1="744.473" y1="443.662" x2="747.081" y2="428.013" xlink:href="#B"><stop offset="0" stop-color="#bab9b9"></stop><stop offset="1" stop-color="#e8e8e7"></stop></linearGradient></defs><path fill="url(#Y)" d="M717 424c2-1 2-1 4 0 2 0 4 2 7 2 2 1 5 2 8 2 4 1 8 1 11 2h1c6 1 13 5 17 8 3 2 5 5 8 8l2 2-1 2c-1 1 0 1-1 0-8-8-17-14-29-17-3-1-7-1-10-2-6-2-11-4-17-7z"></path><path d="M735 418l3 4h1 0c-1-3-2-4-4-6l1-1 4 4s1 1 2 1v-1c2 3 4 5 7 7 5 3 11 7 16 11 2 0 3 0 5-1v-6c0-3-3-4-2-7v-1c1 2 3 3 4 5 0 1 1 3 1 3 1 4 1 7 2 10v8l-2-2c-3-3-5-6-8-8-4-3-11-7-17-8h-1c-3-1-7-1-11-2-3 0-6-1-8-2s-3-2-5-2l1-1 13 4c0-1 0-1-1-2 0-1-1-2-2-3 0-2 1-3 1-4z" class="J"></path><path d="M768 422c1 2 3 3 4 5 0 1 1 3 1 3 1 4 1 7 2 10v8l-2-2c0-5 0-9-1-13v6 2 1c-2-1-5-4-7-5 2 0 3 0 5-1v-6c0-3-3-4-2-7v-1z" class="F"></path><path d="M735 418l3 4h1 0c-1-3-2-4-4-6l1-1 4 4s1 1 2 1h0c3 4 6 6 9 9l-8-4 3 3c-3 0-6 0-9-1 0-1 0-1-1-2 0-1-1-2-2-3 0-2 1-3 1-4z" class="C"></path><defs><linearGradient id="Z" x1="740.722" y1="402.121" x2="733.7" y2="413.725" xlink:href="#B"><stop offset="0" stop-color="#9e9d9d"></stop><stop offset="1" stop-color="#cdcdcc"></stop></linearGradient></defs><path fill="url(#Z)" d="M735 399h3l5 2 5 1 5 4 1 1c5 3 10 10 14 15v1c-1 3 2 4 2 7v6c-2 1-3 1-5 1-5-4-11-8-16-11-3-2-5-4-7-7v1c-1 0-2-1-2-1l-4-4-1 1c2 2 3 3 4 6h0-1l-3-4c-2-2-4-5-6-6l-4-3c-1-1-2-1-3-1l-3-3c-1 0-3-1-4-1v-1l-2-2h8c4 0 9-2 14-1v-1z"></path><path d="M735 399h3l5 2v2h-1c-1-1-1-1-2-1l-5-2v-1zm9 5l8 6 2 2c-2 0-3-1-4-2-1 0-2 1-3 0-2-1-5-4-7-4h-1c1-1 1 0 3-1l1 1 1-1h-1l1-1z" class="K"></path><path d="M715 404v-1c3 0 7 1 10 2l3 1c3 1 5 2 7 4 1 0 2 0 3 1h1l3 2h0c1 1 2 1 3 2 0 0 1 0 1 1-2 0-2 0-3 1l-1 1v1 1c-1 0-2-1-2-1l-4-4-1 1c2 2 3 3 4 6h0-1l-3-4c-2-2-4-5-6-6l-4-3c-1-1-2-1-3-1l-3-3c-1 0-3-1-4-1z" class="F"></path><path d="M735 410c1 0 2 0 3 1h1l3 2h0c1 1 2 1 3 2h-2-1v1l-4-2c-3-1-5-2-7-4h0c1 0 3 1 4 1v-1z" class="M"></path><defs><linearGradient id="a" x1="760.735" y1="408.424" x2="752.646" y2="430.401" xlink:href="#B"><stop offset="0" stop-color="#bab9b9"></stop><stop offset="1" stop-color="#efefee"></stop></linearGradient></defs><path fill="url(#a)" d="M743 401l5 1 5 4 1 1c5 3 10 10 14 15v1c-1 3 2 4 2 7v6c-2 1-3 1-5 1-5-4-11-8-16-11-3-2-5-4-7-7v-1l1-1c1-1 1-1 3-1 1 1 2 1 3 2 1 0 0 0 1 1 1 0 2 1 3 1-1-1-3-3-5-4 3 1 8 5 10 5 0-3-3-4-4-7 1 1 2 1 3 1l-3-3-2-2-8-6-1-1v-2z"></path><path d="M742 419v-1l1-1c1-1 1-1 3-1 1 1 2 1 3 2 1 0 0 0 1 1h-5v1c1 2 2 3 3 4l1 2c-3-2-5-4-7-7z" class="C"></path><path d="M743 401l5 1 5 4 1 1h-1l-2-2c-1-1-2-1-3-1l-4-1 5 4h1c0 1 1 2 2 3l-8-6-1-1v-2z" class="D"></path><path d="M666 406v1h1c2 1 6 0 9-1l18-6c4 1 7 0 11 1l1 1c1-1 2-1 3 0 2 0 4 1 6 2 1 0 3 1 4 1l3 3c1 0 2 0 3 1l4 3c2 1 4 4 6 6 0 1-1 2-1 4 1 1 2 2 2 3 1 1 1 1 1 2l-13-4-1 1c2 0 3 1 5 2-3 0-5-2-7-2-2-1-2-1-4 0-3 0-5 0-8 2l-2 1h0c-5-3-10-6-16-8-5-3-10-4-15-5-6-2-12-4-18-5 2-1 5-2 7-3v-1l1 1z" class="L"></path><path d="M728 417c2 2 4 4 6 5 1 1 2 2 2 3-3-2-6-4-10-6l2-2z" class="G"></path><path d="M697 404c9 0 20 2 26 8l5 5-2 2c-6-4-9-11-17-13-3-1-7-1-11-2h-1z" class="N"></path><path d="M706 402c1-1 2-1 3 0 2 0 4 1 6 2 1 0 3 1 4 1l3 3c1 0 2 0 3 1l4 3c2 1 4 4 6 6 0 1-1 2-1 4-2-1-4-3-6-5l-5-5c-6-6-17-8-26-8l1-1c3 0 7 0 9 1h1 2c2 1 0 0 1 1h1 1 1c1 1 1 1 3 1v-1l-10-3h-1z" class="B"></path><path d="M666 406v1h1c2 1 6 0 9-1l18-6c4 1 7 0 11 1l1 1h1l10 3v1c-2 0-2 0-3-1h-1-1-1c-1-1 1 0-1-1h-2-1c-2-1-6-1-9-1l-1 1h-4v1c-1 1-3 1-5 2 2 1 3 1 5 1 1 1 2 1 3 2 3 1 4 2 6 4 2 1 4 3 6 4v1h-4c0 2 4 5 4 6l-1 1v1c-5-3-10-6-16-8-5-3-10-4-15-5-6-2-12-4-18-5 2-1 5-2 7-3v-1l1 1z" class="E"></path><path d="M300 314h4c4 0 7 0 11 2 3 2 7 5 8 9h1l1-2 2 2h1c2 1 3 2 4 4 0 1 1 2 2 3 2 2 4 6 5 9l-1 1h0c0 2 2 4 2 6 1 3 0 8 1 10v5c0-1 0-2-1-4-1 3-1 6-1 9l-2-1c-1 3-3 5-5 7 0 1-1 2-1 3l-1-1 1-2-1-2c0-1 1-2 2-3l2-4h-3c-2 1-3 1-5 1h-1c0-1-5-3-6-3l2 2h-1c3 5 4 10 3 15s-3 10-7 13c-2 1-4 1-5 2l-4-1c-2-4-2-7-4-12-3-4-5-10-7-14l-1-1-1-1c-1-1-2-3-3-4h-2 0l-2-5c-1-2-2-3-3-5h0l-4-10h1v-1c-1-2-1 0-1-2 0-1 1-2 1-2-1-4-3-9-5-12l1-2c7-7 14-9 23-9z" class="a"></path><path d="M320 365c-2-2-3-3-5-4l-2-1h0c2-1 4-1 6 0l-1 1s1 1 1 2l2 2h-1z" class="C"></path><path d="M319 360c3 0 9 4 12 2l2-2 1 1c-1 1-2 3-3 4-2 1-3 1-5 1h-1c0-1-5-3-6-3 0-1-1-2-1-2l1-1z" class="B"></path><path d="M284 352h2c1 1 2 3 4 3 1 2 2 3 2 5l4 8-1-1-1-1c-1-1-2-3-3-4h-2 0l-2-5c-1-2-2-3-3-5z" class="Y"></path><path d="M284 352h2c1 1 2 3 4 3 1 2 2 3 2 5-2-1-2-2-3-3l-3-3c0 1 1 2 1 3-1-2-2-3-3-5z" class="J"></path><defs><linearGradient id="b" x1="280.496" y1="343.209" x2="290.779" y2="352.54" xlink:href="#B"><stop offset="0" stop-color="#3b3a3a"></stop><stop offset="1" stop-color="#706f6f"></stop></linearGradient></defs><path fill="url(#b)" d="M281 337c4 5 7 12 9 18-2 0-3-2-4-3h-2 0l-4-10h1v-1c-1-2-1 0-1-2 0-1 1-2 1-2z"></path><path d="M333 360c2-3 3-7 2-11 0-2-2-4-4-6-5-4-15-2-21-2 3-3 5-5 9-7l8 3c3 1 5 2 7 4l1 2c0 2 1 4 1 5 1 5 0 9-2 13h0l-1-1z" class="O"></path><path d="M325 323l2 2h1c2 1 3 2 4 4 0 1 1 2 2 3 2 2 4 6 5 9l-1 1-2-4c0 2 0 3 1 5h-2l-1-2c-2-2-4-3-7-4l-8-3c2-1 3-2 4-4l1-1-1-4h1l1-2z" class="Y"></path><path d="M323 325h1l3 5-3-1-1-4z" class="T"></path><path d="M324 329l3 1c3 3 7 6 8 10h0c-1-1-2-3-4-4-1-1-1-1-2-1-2-2-3-3-5-4l-1-1 1-1z" class="g"></path><path d="M327 325h1c2 1 3 2 4 4 0 1 1 2 2 3 2 2 4 6 5 9l-1 1-2-4-9-13z" class="T"></path><path d="M336 338l2 4h0c0 2 2 4 2 6 1 3 0 8 1 10v5c0-1 0-2-1-4-1 3-1 6-1 9l-2-1c-1 3-3 5-5 7 0 1-1 2-1 3l-1-1 1-2-1-2c0-1 1-2 2-3l2-4h-3c1-1 2-3 3-4h0c2-4 3-8 2-13 0-1-1-3-1-5h2c-1-2-1-3-1-5z" class="X"></path><path d="M338 353c1 2 2 4 2 6-1 3-1 6-1 9l-2-1v-1c2-4 1-10 1-13z" class="M"></path><path d="M334 365l2-3h0c0 2-1 5-2 7-1 1-2 2-2 3v2c0 1-1 2-1 3l-1-1 1-2-1-2c0-1 1-2 2-3l2-4z" class="J"></path><path d="M336 338l2 4h0c0 2 2 4 2 6 1 3 0 8 1 10v5c0-1 0-2-1-4 0-2-1-4-2-6 0-4-1-7-1-10-1-2-1-3-1-5z" class="F"></path><path d="M889 170c3 0 8-2 11-4 1 3-23 33-27 37-5 5-9 10-13 15-1-1-2-2-3-2l-1 1-7-3h-1c-2-1-3-1-5-1l32-36H672h-55l-16 1c-3 0-7 0-10-1h-3l-14 31c-1 1-2 1-2 2-1 1-2 2-2 4l-1 2c-2 3-3 6-5 8 0 1-1 2-2 3-1-1-2-1-3-2l1-3c0-1 0-2 1-3v-1 1c1-1 1-1 1-2 2-2 4-8 5-11 2-5 5-11 7-16 1-4 3-8 4-11 1-2 2-4 2-5 0-2 1-4 2-6h12c2 0 5 0 7 1h1c1 0 2-1 3 0 6 1 13 0 19 0h56 35l18 2h26 21c4 0 8 1 12 0 3-1 9 0 13 0h29c2 0 6 0 9-1h-12-1v-1h27c5 0 11 0 16 1 2 1 5 0 7 0v1h-3 0c4 0 8 0 12-1z" class="d"></path><path d="M830 170v-1h27c5 0 11 0 16 1 2 1 5 0 7 0v1h-3 0c-9 1-18 0-28 0h-57c3-1 9 0 13 0h29c2 0 6 0 9-1h-12-1z" class="K"></path><path d="M875 177c2 0 4-1 6-1-10 12-21 23-30 36 1 2 4 3 6 4l-1 1-7-3h-1c-2-1-3-1-5-1l32-36z" class="D"></path><defs><linearGradient id="c" x1="347.488" y1="369.695" x2="398.671" y2="454.667" xlink:href="#B"><stop offset="0" stop-color="#d4d4d3"></stop><stop offset="1" stop-color="#fbfafa"></stop></linearGradient></defs><path fill="url(#c)" d="M341 358l1-2c1 1 1 5 1 7 1 3 2 7 3 10l3 6c2 3 5 7 8 9l2 2c3 3 5 6 7 9 2 1 4 2 6 2l7 2 4 1c3 0 6 1 9 2h0c2 0 2 0 3 1 1-1 2-2 3-4h-1l1-1c11 8 27 17 30 32 1 1 2 5 2 5v3 4h0c-3 5-6 8-8 12s-3 7-4 11l-2 8v-1-5-2l1-1v-1-2-1c1-2 1-7 1-10 0-2 0-5-1-7h0l-4-4c0-1-1-2-1-2l-2-2c-1-2-2-2-3-3l-2-2c-2-1-3-3-5-3h-1l-8-4c-1-1-2-1-3-1v1c0 1-1 1-1 1l-4-2-7-1c-1 0-2 1-3 2l-7-1h-6l-6 1c-2 1-3 2-5 3l-2 2-1 2v1 4l-8 6-7 5h-1v-2h-1l-2-4c-2-5-5-10-7-15-1 0-2-1-2-2 3-3 6-5 10-6 6-3 13-4 19-6l16-6h-1c-1 0-2-1-3-1v-1l-4-1-14-5c-6-2-13-5-18-9l-1-1v-1l2-1c1-1 1-4 2-6s2-2 3-3 1-1 2-3c0-1 1-2 1-3 2-2 4-4 5-7l2 1c0-3 0-6 1-9 1 2 1 3 1 4v-5z"></path><path d="M371 407v-1c1 0 1 0 2 1h1v-1h1c2 1 4 1 6 2 1 0 2 0 3 1h-1 1l1 1h0c1 0 2 1 3 2-6-2-12-3-17-5z" class="E"></path><path d="M339 390v-3c4 4 8 11 13 15-6-2-9-7-13-12z" class="S"></path><path d="M338 384c0-1-1-2-2-2h-1v-2c0-2 0-2 1-3l2 2v-1-3l2-1h0v2c0 1 1 1 1 2v-6h1l1-1h0l1 5-1-1c-1-1 0-1-1-1 0 3 0 7 2 10 1 2 2 4 2 6-2-2-3-5-5-7l-3-3v3 1z" class="O"></path><path d="M351 391c2-1 3 0 5 1 1 2 3 4 6 5 1 1 2 2 4 2 2 1 4 2 6 2l7 2 4 1c3 0 6 1 9 2h0l3 2h0c-15-5-33-3-44-17z" class="c"></path><path d="M360 404l11 3c5 2 11 3 17 5l19 8c-4-1-7-2-11-3-10-3-19-6-30-5 2-2 5-1 8-1v-1c-1 0-1 0-2-1h-1v-1l-7-1c-1-1-3-2-4-3z" class="S"></path><path d="M340 359c1 2 1 3 1 4l2 8-1 1h-1v6c0-1-1-1-1-2v-2h0l-2 1v3 1l-2-2c-1 1-1 1-1 3v2h1c1 0 2 1 2 2 1 1 1 2 1 3v3c-1-1-1-2-2-3v-1h0-2v1l-3-4c1-2 1-4 2-6 1-3 3-6 5-9h0c0-3 0-6 1-9z" class="P"></path><path d="M335 386h2 0v1c1 1 1 2 2 3 4 5 7 10 13 12h2c1 1 2 1 2 1 2 0 3 0 4 1h0c1 1 3 2 4 3-1 1-3 1-5 0l-4-1-14-5v-4c0-1-1-1-1-2-1-1-1 0-1-1 0-2 0-2-1-3-1-2-2-3-3-5z" class="W"></path><path d="M341 358l1-2c1 1 1 5 1 7 1 3 2 7 3 10l3 6c2 3 5 7 8 9l2 2c3 3 5 6 7 9-2 0-3-1-4-2-3-1-5-3-6-5-2-1-3-2-5-1-2-2-3-5-4-7-2-3-2-6-3-8l-1-5h0l-2-8v-5z" class="R"></path><path d="M343 371l1 1h0c1 6 5 10 8 14 2 3 4 5 7 8l3 3c-3-1-5-3-6-5-2-1-3-2-5-1-2-2-3-5-4-7-2-3-2-6-3-8l-1-5z" class="g"></path><path d="M347 384c3 1 6 5 9 8-2-1-3-2-5-1-2-2-3-5-4-7z" class="T"></path><path d="M364 407l7 1v1h1c1 1 1 1 2 1v1c-3 0-6-1-8 1-7 1-12 3-18 5-8 3-16 5-22 9-2 1-3 2-4 3h-2c-1 0-2-1-2-2 3-3 6-5 10-6 6-3 13-4 19-6l16-6h-1c-1 0-2-1-3-1v-1c2 1 4 1 5 0z" class="f"></path><path d="M364 407l7 1v1h-8-1c-1 0-2-1-3-1v-1c2 1 4 1 5 0z" class="I"></path><path d="M337 367l2 1h0c-2 3-4 6-5 9-1 2-1 4-2 6l3 4v-1c1 2 2 3 3 5 1 1 1 1 1 3 0 1 0 0 1 1 0 1 1 1 1 2v4c-6-2-13-5-18-9l-1-1v-1l2-1c1-1 1-4 2-6s2-2 3-3 1-1 2-3c0-1 1-2 1-3 2-2 4-4 5-7z" class="D"></path><path d="M330 388v-1c0-1-1-1-1-2 1-1 1-2 2-3v1c0 2 3 5 4 7h1l-1-3v-1c1 2 2 3 3 5 1 1 1 1 1 3 0 1 0 0 1 1 0 1 1 1 1 2-4-3-8-6-11-9z" class="O"></path><path d="M324 389h2c0-1 1-1 1-2v-1h1l2 2c3 3 7 6 11 9v4c-6-2-13-5-18-9l-1-1v-1l2-1z" class="L"></path><path d="M324 389h2c0-1 1-1 1-2v-1h1v2l4 5c-2 0-4-1-5-2-2 0-2 0-4 1l-1-1v-1l2-1z" class="F"></path><path d="M345 422h1c6-2 12-5 18-5l1-1c5 0 9 0 14 1 2 0 4 1 6 1h0c3 0 6 2 8 2 1 0 2 1 3 1h3v1c1 0 2 0 2 1h2c1 0 2 0 2 1h3 2l1 1c2 0 3 1 4 2 2 0 5 2 6 3 2 2 5 7 7 7v-3c1 1 2 5 2 5v3 4h0c-3 5-6 8-8 12s-3 7-4 11l-2 8v-1-5-2l1-1v-1-2-1c1-2 1-7 1-10 0-2 0-5-1-7h0l-4-4c0-1-1-2-1-2l-2-2c-1-2-2-2-3-3l-2-2c-2-1-3-3-5-3h-1l-8-4c-1-1-2-1-3-1v1c0 1-1 1-1 1l-4-2-7-1c-1 0-2 1-3 2l-7-1h-6l-6 1c-2 1-3 2-5 3l-2 2-1 2v1 4l-8 6-7 5h-1v-2h-1l-2-4c-2-5-5-10-7-15h2c1-1 2-2 4-3 2 1 6-2 9-2l3-1v1l2-1h0 1-1l5-1h0z" class="Q"></path><path d="M387 425c5 1 9 3 12 6h1-1l-8-4c-1-1-2-1-3-1v1c0 1-1 1-1 1l-4-2 1-1h3z" class="X"></path><path d="M366 421c5-1 8-2 13-1 3 0 6 0 9 2-2 0-5-1-6-1-1-1-3-1-4 0h1c1 0 1 0 2 1l1-1v1c1 0 2 0 3 1h1v1c-2 0-5-2-7-2-4-1-9-1-13-1z" class="L"></path><path d="M415 438l-7-7h1c3 2 5 5 8 7l2 4c1 0 2 1 2 1 1 1 1 2 1 4l-1 2v1c0 2-1 3-3 4h0c0-2 0-5-1-7 1 1 1 2 2 3 1-1 1-2 1-4 0-3-3-6-5-8z" class="H"></path><path d="M340 428c0-1 1-2 2-3-1 4-11 11-9 14l3 1-4 2c-1-1-1-1-2-3l-2-4c0-1 0-2 1-3 1 1 1 1 3 2h0l1-1c1 0 2-1 3-1 1-2 2-3 4-4z" class="C"></path><path d="M415 438c2 2 5 5 5 8 0 2 0 3-1 4-1-1-1-2-2-3h0l-4-4c0-1-1-2-1-2l-2-2c1-1 2-1 3-1h2z" class="E"></path><path d="M340 423l5-1h0c0 1-1 1-2 1-2 1-3 2-4 3l1 2c-2 1-3 2-4 4-1 0-2 1-3 1l-1 1h0c-2-1-2-1-3-2l4-3-1-1c1-2 7-4 8-5z" class="B"></path><path d="M339 426l1 2c-2 1-3 2-4 4-1 0-2 1-3 1l-1 1v-1c0-1 1-2 2-3 1-2 3-3 5-4z" class="L"></path><path d="M335 424l3-1v1l2-1h0 1-1c-1 1-7 3-8 5l1 1-4 3c-1 1-1 2-1 3l2 4c1 2 1 2 2 3l-2 1-1 1 1 2v2h-1l-2-4c-2-5-5-10-7-15h2c1-1 2-2 4-3 2 1 6-2 9-2z" class="F"></path><path d="M335 424l3-1v1l2-1h0 1-1c-1 1-7 3-8 5l-1 1c-1 0-2-1-2-1 0-2 4-3 6-4z" class="d"></path><path d="M320 429h2l2 1h1c1 0 1 0 2 1h0c-1 3 0 5 1 8 1 1 1 3 2 4l-1 1 1 2v2h-1l-2-4c-2-5-5-10-7-15z" class="X"></path><path d="M366 421c4 0 9 0 13 1 2 0 5 2 7 2l1 1h-3l-1 1-7-1c-1 0-2 1-3 2l-7-1h-6l-6 1c-2 1-3 2-5 3l-2 2-1 2v1 4l-8 6-7 5h-1v-2-2l-1-2 1-1 2-1 4-2c2-2 4-3 6-5s4-5 6-7c6-5 11-6 18-7z" class="J"></path><path d="M366 426c3-2 7-2 10-1-1 0-2 1-3 2l-7-1z" class="K"></path><path d="M354 427c2-2 4-2 7-3 1 0 4-1 5 0-1 1-4 0-6 2h0l-6 1z" class="e"></path><path d="M330 446c3-1 4-4 7-5l1 4-7 5h-1v-2-2z" class="K"></path><path d="M337 441c3-2 7-8 10-9l-1 2v1 4l-8 6-1-4z" class="H"></path><defs><linearGradient id="d" x1="416.766" y1="443.185" x2="418.926" y2="427.987" xlink:href="#B"><stop offset="0" stop-color="#acacac"></stop><stop offset="1" stop-color="#d6d5d3"></stop></linearGradient></defs><path fill="url(#d)" d="M410 424l1 1c2 0 3 1 4 2 2 0 5 2 6 3 2 2 5 7 7 7v-3c1 1 2 5 2 5v3 4h0c-3 5-6 8-8 12s-3 7-4 11l-2 8v-1-5-2l1-1v-1-2-1c1-2 1-7 1-10h0c2-1 3-2 3-4v-1l1-2c0-2 0-3-1-4 0 0-1-1-2-1l-2-4h0l-4-6h0c-2-2-3-3-4-5 1-1 1-1 1-2v-1z"></path><path d="M410 424l1 1v1c1 1 1 1 1 2s3 3 4 4h-3 0c-2-2-3-3-4-5 1-1 1-1 1-2v-1z" class="E"></path><path d="M416 432c2 1 3 3 3 5 1 1 0 1 0 2s2 3 2 4c0 0-1-1-2-1l-2-4h0l-4-6h3z" class="C"></path><path d="M430 446h-2c-1-1-3-1-4-1-1-1 0-7 0-8 2 1 4 4 6 5h0v4z" class="D"></path><path d="M640 413c9-1 17-2 25 0 1 1 2 2 3 2v1c1 1 2 1 4 2 0 0 1 1 2 0 2 2 5 3 7 3l5 3c0 2 1 3 3 4v1c1 2 1 3 1 6h0-3 0c-1 1-1 2-2 3 1 1 2 2 2 3l-4-2c-1 1-2 1-3 1-2 0-3 0-5 1l-1 2c-11 16-21 34-30 52l-6 14-3 6v1h-2c-1-1-3-1-4-1l-1-1c-2 1-3 1-3 2v1c1 0 1 1 2 1h-1c-1 0-1 0-2 1-4 0-8 0-12 1-1 1-1 1-2 1h-2l-1 1h-1c2-1 3-2 4-3v-1c3-2 7-4 11-5l-3-3c-1-2 0-3 0-5h1c-2-3 0-7-2-9 0 1-1 3-2 4 0-2 1-3 1-4 1-6 0-8-2-13l-1-6c-1-2-1-3-1-5h0l-1-1v-2-1c-1-1-1-1-1-2 0-2 1-5 2-7 0-2 2-3 2-4 0-2 0-2 1-2l2-2c1-3 4-9 7-11 2-3 4-5 6-7l3-2-2-3c-1 1-3 2-5 3-5 3-10 7-15 11 1-2 2-3 4-4h-1c-2 0-4 3-5 4v1c0-1 1-4 1-5 2-6 7-8 11-11l1-1c1 0 3-1 4-2 3 0 5-1 7-3h0-2c1-2 1-2 2-2h0l-1-1c1-1 2-1 3-2 0 0 0-1 1-2l4-1z" class="G"></path><path d="M621 472l2 6c0 1 0 1 1 2v1c1 1 1 2 2 3v1h1l1-1h4l-2 2h-1l-1 4c-1 0-1 1 0 2-1 1-2 1-3 1h-1c2-6-3-10-4-15h1l3 7h0v-1-1c0-1-1-2-1-3h0l-1-2h0c-1-3-1-4-1-6z" class="Z"></path><path d="M628 490l-1-1c0-2-1-2 0-3l2-1v1l-1 4z" class="f"></path><path d="M671 426c3 2 6 4 9 4l1 1c2 2 4 3 6 4h0c-1 1-1 2-2 3 1 1 2 2 2 3l-4-2c-1-1-3-2-4-3-2-4-5-7-9-9l1-1z" class="D"></path><path d="M621 472v-6c-1 0-1-1-1-2l1-1v-2c0-1 1-1 1-2 1-2 1-4 3-5h1l-2 4c1 0 2-1 3-1v1h0c-2 1-3 4-3 6h0v2c-1 3-1 11 1 13v1c1 1 1 2 1 4-1-1-1-2-2-3v-1c-1-1-1-1-1-2l-2-6z" class="S"></path><path d="M625 454v-2l1-2c3-6 9-11 14-15 1-1 3-2 4-3 7-2 15-4 22-1l1 1c4 1 6 2 9 5h1c1 0 0 0 1-1h1c1 1 3 2 4 3-1 1-2 1-3 1-2 0-3 0-5 1h-1l-1-2c-2-2-5-5-8-6h-1c-2-1-2-1-4-1h-3c-4 0-9 1-12 3-2 1-3 2-4 3-4 1-8 7-11 9v1c-1 1-1 2-1 3v1c-1 1-2 2-3 2h-1z" class="U"></path><path d="M624 440c4-2 6-6 10-9h1c1-1 2-1 4-2h1l1 1c-10 6-19 17-22 29-2 5-1 13 1 19 1 5 6 9 4 15-1 2-2 4-2 5l-3 7c-2-3 0-7-2-9 0 1-1 3-2 4 0-2 1-3 1-4 1-6 0-8-2-13l-1-6c-1-2-1-3-1-5h0l-1-1v-2-1c-1-1-1-1-1-2 0-2 1-5 2-7 0-2 2-3 2-4 0-2 0-2 1-2l2-2c1-3 4-9 7-11z" class="B"></path><path d="M612 472c1 1 1 1 1 3l1 2h0c1 1 1 2 2 2 1 1 1 3 2 5 2 4 3 6 3 11v3h1l-3 7c-2-3 0-7-2-9 0 1-1 3-2 4 0-2 1-3 1-4 1-6 0-8-2-13l-1-6c-1-2-1-3-1-5z" class="F"></path><path d="M617 496v-2c1-3 0-5 1-8 1 4 2 8 2 11v1l-1 1h1l1-1h1l-3 7c-2-3 0-7-2-9z" class="C"></path><path d="M657 432h3c2 0 2 0 4 1h1c3 1 6 4 8 6l1 2h1l-1 2c-2 0-3 0-5 1h0l-28 7c-4 1-7 2-10 4-1 0-3 1-4 2-1 0-2 1-3 1l2-4c1 0 2-1 3-2v-1c0-1 0-2 1-3v-1c3-2 7-8 11-9 1-1 2-2 4-3 3-2 8-3 12-3z" class="D"></path><path d="M657 432h3v2l1 1 1-1c1 1 1 1 2 3l-1 1h2 2l1 1c-3 0-5 0-8-1 0-1 0-1-1-2s-2-2-2-4z" class="M"></path><path d="M638 446l-1-1h-1-1c1-1 1-1 1-2h1c1-1 2-2 4-2 1 0 3-1 4-2s1-2 3-2h1l-1 3-4 2-1 1c-1 2-3 2-5 3z" class="F"></path><path d="M660 432c2 0 2 0 4 1h1c3 1 6 4 8 6l1 2h-2c-1-1-2-2-4-2l-1-1h-2-2l1-1c-1-2-1-2-2-3l-1 1-1-1v-2z" class="K"></path><path d="M643 443l14-3c3 0 5-1 8 0 1 0 2 1 4 1h0-1v1l-27 6-1-1h-2-1l1-1c2-1 4-1 5-3z" class="I"></path><path d="M674 441h1l-1 2c-2 0-3 0-5 1h0l-28 7c-4 1-7 2-10 4-1 0-3 1-4 2-1 0-2 1-3 1l2-4c1 0 2-1 3-2l12-4 27-6c1 0 3-1 4-1h2z" class="V"></path><path d="M640 413c9-1 17-2 25 0 1 1 2 2 3 2v1c1 1 2 1 4 2 0 0 1 1 2 0 2 2 5 3 7 3l5 3c0 2 1 3 3 4v1c1 2 1 3 1 6h0-3c-2-1-4-2-6-4l-1-1c-3 0-6-2-9-4l-1 1c-5-2-11-2-17-1-4 1-8 2-12 4l-1-1h-1c-2 1-3 1-4 2h-1c-4 3-6 7-10 9 2-3 4-5 6-7l3-2-2-3c-1 1-3 2-5 3-5 3-10 7-15 11 1-2 2-3 4-4h-1c-2 0-4 3-5 4v1c0-1 1-4 1-5 2-6 7-8 11-11l1-1c1 0 3-1 4-2 3 0 5-1 7-3h0-2c1-2 1-2 2-2h0l-1-1c1-1 2-1 3-2 0 0 0-1 1-2l4-1z" class="d"></path><path d="M627 429c1-1 2-2 3-2l3-2h1l2-1c1-1 1-1 2-1h1l3-1-2-1c2 0 4-1 6-1 0 0 1 1 2 1-8 2-14 5-21 8z" class="Q"></path><path d="M681 421l5 3c0 2 1 3 3 4v1c1 2 1 3 1 6h0-3c-2-1-4-2-6-4l3 1c1-1 1-1 1-2-1-2-3-3-5-4v-1l-1-1c-1 0-2-1-3-1l1-1 3 2h2l3 3-1-2 1-1c-1 0-2-1-3-2l-1-1z" class="L"></path><path d="M648 421c10-1 21 0 29 7l3 2c-3 0-6-2-9-4l-1 1c-5-2-11-2-17-1-4 1-8 2-12 4l-1-1h-1c-2 1-3 1-4 2h-1c-4 3-6 7-10 9 2-3 4-5 6-7l3-2-2-3c-1 1-3 2-5 3-5 3-10 7-15 11 1-2 2-3 4-4l12-9c7-3 13-6 21-8z" class="e"></path><path d="M640 424c3 0 6-1 8-1 8-1 16 2 23 3l-1 1c-5-2-11-2-17-1-1-1-3-2-5-2-3 1-5 1-8 0z" class="X"></path><path d="M640 424c3 1 5 1 8 0 2 0 4 1 5 2-4 1-8 2-12 4l-1-1h-1c-2 1-3 1-4 2h-1c-4 3-6 7-10 9 2-3 4-5 6-7l3-2-2-3c2-1 3-1 5-2l4-2z" class="M"></path><path d="M631 428c2-1 3-1 5-2l2 2-1 1c-1 0-2 1-4 2l-2-3z" class="H"></path><defs><linearGradient id="e" x1="653.358" y1="478.438" x2="649.378" y2="476.342" xlink:href="#B"><stop offset="0" stop-color="#aaa9a9"></stop><stop offset="1" stop-color="#d8d7d7"></stop></linearGradient></defs><path fill="url(#e)" d="M669 444h0c2-1 3-1 5-1-11 16-21 34-30 52l-6 14-3 6v1h-2c-1-1-3-1-4-1l-1-1c-2 1-3 1-3 2v1c1 0 1 1 2 1h-1c-1 0-1 0-2 1-4 0-8 0-12 1-1 1-1 1-2 1h-2l-1 1h-1c2-1 3-2 4-3v-1c3-2 7-4 11-5l-3-3c-1-2 0-3 0-5h1l3-7c0-1 1-3 2-5h1c1 0 2 0 3-1-1-1-1-2 0-2l1-4h1l2-2h-4l-1 1h-1v-1c0-2 0-3-1-4v-1c-2-2-2-10-1-13v-2h0c0-2 1-5 3-6h0v-1c1-1 3-2 4-2 3-2 6-3 10-4l28-7z"></path><path d="M633 507c3-5 5-12 9-16l-9 20h-1c0-2 0-2 1-3v-1z" class="R"></path><path d="M657 456c2-2 4-5 5-7 2 0 3-1 4-2v-1h1c0 1-1 2-1 3-1 1-1 2-2 3l-1 2-1 1s0 1-1 2c0 0-1 1-1 2l-2 2v-1l2-4 1-1h0c-3 0-6 3-7 5l-2 1h0c1-2 3-4 5-5z" class="F"></path><path d="M650 470c2-1 2-2 4-2-1 2-2 3-3 5 0 2 0 2-2 4 0 1 0 1-1 3h0l-4 7v2l-2 2c-4 4-6 11-9 16v-7c0-2 1-3 1-4 1-1 0-2 1-2 0-1 1-3 2-3l-3 1-3-1c-1 0-2 0-3 1-1-1-1-2 0-2l1-4h1l2-2h-4l1-1 2-1 4-3 15-9z" class="C"></path><path d="M630 486v4h1c4-1 9-4 13-6v1c-2 3-4 4-7 6l-3 1-3-1c-1 0-2 0-3 1-1-1-1-2 0-2l1-4h1z" class="c"></path><path d="M651 473c0 2 0 2-2 4v-1l-2 2c0 1-1 2-2 2l-2 2c-3 2-5 3-8 4h-1v-1c1-1 2-2 3-4 4-3 9-6 14-8z" class="B"></path><path d="M650 470c2-1 2-2 4-2-1 2-2 3-3 5-5 2-10 5-14 8l-5 3h-4l1-1 2-1 4-3 15-9z" class="V"></path><path d="M634 492l3-1c-1 0-2 2-2 3-1 0 0 1-1 2 0 1-1 2-1 4v7 1c-1 1-1 1-1 3h1v3l2 1v1h-2c-1-1-3-1-4-1l-1-1c-2 1-3 1-3 2v1c1 0 1 1 2 1h-1c-1 0-1 0-2 1-4 0-8 0-12 1-1 1-1 1-2 1h-2l-1 1h-1c2-1 3-2 4-3v-1c3-2 7-4 11-5l-3-3c-1-2 0-3 0-5h1l3-7c0-1 1-3 2-5h1c1 0 2 0 3-1s2-1 3-1l3 1z" class="f"></path><path d="M625 516c-1 0-1 0-1 1h-1-1c1-1 1-2 2-3s2-2 2-3h1c0 1 0 2 1 2v1c-2 1-3 1-3 2z" class="V"></path><path d="M627 496c1 2 1 2 3 3 0 1-1 1-1 2-1 0-1 0-2 1 0 1-1 2-2 3h0v1l-1-1c0-3 1-6 3-9zm5 11h0l1 1c-1 1-1 1-1 3h1v3l2 1v1h-2c-1-1-3-1-4-1l-1-1v-1c-1 0-1-1-1-2h-1l2-2c2 0 3-1 4-2z" class="X"></path><path d="M626 511l2-2 1 3c1 1 3 2 4 2l2 1v1h-2c-1-1-3-1-4-1l-1-1v-1c-1 0-1-1-1-2h-1z" class="N"></path><path d="M634 492l3-1c-1 0-2 2-2 3-1 0 0 1-1 2 0 1-1 2-1 4v7 1l-1-1h0c-1-2-3-2-4-3 0-1 0-1-1-2 1-1 1-1 2-1 0-1 1-1 1-2-2-1-2-1-3-3 2-2 4-3 7-4z" class="P"></path><path d="M630 499h1v1c0 1 0 2 1 3 0 1 0 1 1 2l-1 2h0c-1-2-3-2-4-3 0-1 0-1-1-2 1-1 1-1 2-1 0-1 1-1 1-2z" class="Y"></path><path d="M669 444l-1 2h-1-1v1c-1 1-2 2-4 2-1 2-3 5-5 7-2 1-4 3-5 5h0l2-1c1-2 4-5 7-5h0l-1 1-2 4v1h0l-1 2-2 4c0 1 0 1-1 1-2 0-2 1-4 2l-15 9-4 3-2 1-1 1-1 1h-1v-1c0-2 0-3-1-4v-1c-2-2-2-10-1-13v-2h0c0-2 1-5 3-6h0v-1c1-1 3-2 4-2 3-2 6-3 10-4l28-7z" class="P"></path><g class="B"><path d="M642 460c0 2-1 2-3 3-1 2-4 3-6 4 1-2 3-3 4-5l1-1 4-1zm11-3h1s1-1 3-1c-2 1-4 3-5 5l-5 2c-4 3-8 8-13 11h-2v1l-1-1h0c2-5 5-7 8-9 2-2 4-4 6-5h1c2-2 5-3 7-3z"></path><path d="M654 460c1-2 4-5 7-5h0l-1 1-2 4v1h0l-1 2-2 4c0 1 0 1-1 1-2 0-2 1-4 2l-15 9c-2 0-2 0-3-1v-1c1-2 7-5 9-6 1-1 2-1 3-2v-1h1c3-2 5-5 7-7l2-1z"></path></g><path d="M654 460c1-2 4-5 7-5h0l-1 1-2 4v1h0l-1 2-2 4c0 1 0 1-1 1-2 0-2 1-4 2 1-4 3-6 5-9l3-4-3 3h-1z" class="O"></path><path d="M669 444l-1 2h-1-1v1c-1 1-2 2-4 2-1 2-3 5-5 7-2 0-3 1-3 1h-1v-1c-1 0-1 0-1-1l-9 3c-1 0-1 1-1 2l-4 1c-2 1-4 2-5 3-1 0-2 0-3 1l-1 2v2c1 1 1 0 1 1l-1 1c0 2-1 3-1 6l1 1v3l2 1-2 1-1 1-1 1h-1v-1c0-2 0-3-1-4v-1c-2-2-2-10-1-13v-2h0c0-2 1-5 3-6h0v-1c1-1 3-2 4-2 3-2 6-3 10-4l28-7z" class="J"></path><path d="M629 483c-1-2-2-6-2-8v-2h0l-1-1h0c0-3 0-5 2-7 0 2-1 4 0 6h1c0 2-1 3-1 6l1 1v3l2 1-2 1zm2-28c3-2 6-3 10-4-2 2-4 2-6 3v1c-2 1-4 2-6 5v2l-2 1h0c1-2 0-3 1-5 0-1 2-3 3-3z" class="M"></path><path d="M643 458c-1 0-1 1-1 2l-4 1c-2 1-4 2-5 3-1 0-2 0-3 1l-1 2v2c1 1 1 0 1 1l-1 1h-1c-1-2 0-4 0-6 4-4 9-5 15-7z" class="D"></path><path d="M669 444l-1 2h-1-1v1c-1 1-2 2-4 2-1 2-3 5-5 7-2 0-3 1-3 1h-1v-1c-1 0-1 0-1-1l1-1c-2-1-4 0-5 1l-11 3c-3 1-5 2-8 4v-2c2-3 4-4 6-5v-1c2-1 4-1 6-3l28-7z" class="O"></path><path d="M635 455c2 0 3-1 4-1h1v-1h1 1 1 0c-2 1-5 2-7 3 0 1 1 2 1 2-3 1-5 2-8 4v-2c2-3 4-4 6-5z" class="F"></path><path d="M200 219c4 0 8 2 12 3l1 2h1c1 0 2 1 3 1 1 1 3 1 3 3l-2-1-9 3c-4 2-8 4-9 8v3c0 1 0 1 1 2 2 0 6-2 7-1 1 0 6 6 7 7h0c1 1 5 3 5 4 3 1 5 1 7 1 1 0 3-1 4 0l3-1c2 0 3 1 5 1 2-1 7-8 8-10l1-1s-1-1-2-1l1-2c1-1 2-2 3-4h0l1-1c1-1 2-1 3-2h1l-1 4 1 1 1 2-1 7c0 3 0 8 1 11l1 1v1l1 2 2 2 2 2 5 3 3 1 4 1 4 1c3 1 7 2 10 4h1l1 3h-1c0 1 0 2-1 3v1l2 1-1 1h1c1 3 7 6 9 9h-9-3c-9 1-17 4-25 6l1 2c-6 2-12 2-18 3-10-1-19-3-28-7-4-2-9-4-12-7-5-4-8-9-12-13-5-8-6-14-8-22v1h-1c-2-2-2-2-3-4-1-5-4-11-4-16-1-2 0-4 0-5 1-2 2-3 3-4l1-1 1-1 4-3c1-1 3-2 5-3 1 1 1 0 2 0l2 1c1-1 2-1 3-1l2-1z" class="L"></path><path d="M196 238v1l-1 1c-1-1-1-3-2-4l3-3c-1 2-1 3 0 5zm10 12l5 2c-1 0-1 1-2 1-2 0-4 0-5-1 0-2 0-2 2-2z" class="B"></path><path d="M224 269h1l1 4-1 2c0 1 0 1 1 2l3 3v1c-1-2-4-3-4-5l-1-1h0c-1-2 0-4 0-6z" class="C"></path><path d="M241 296h3 2c4 1 8 1 12 1h2l-1 1c-5 0-14 0-18-2z" class="E"></path><path d="M244 287c3 0 7 2 10 2 4 0 11-2 14 0-5 1-12 1-18 0-2-1-4-1-6-2z" class="C"></path><path d="M196 233c1-1 2-2 4-3h1v1h1c-3 2-5 4-6 7-1-2-1-3 0-5z" class="P"></path><path d="M206 250l-1-1c-1 0-1 0-2-1l3-3c2 1 2 3 4 4h0l2 2-1 1-5-2z" class="Q"></path><path d="M215 249c1 1 5 3 5 4 1 2 2 2 4 3v2h1c1 4 2 7 4 10h-3l-1 1h-1l-1-5 1-3v-1c0-2-1-3-3-4-1-1 0-1-1-2-2-1-4-2-5-5z" class="B"></path><path d="M210 249c1 1 2 1 3 2s1 1 2 1l1 1c3 0 4 2 6 4 1 2 1 3 1 6l1-2-1 3 1 5c0 2-1 4 0 6h0l1 1c-1-1-2-1-2-2s-1-2-1-3c-1-2-1-3 0-5v-2c0-4-1-5-3-9h-1-1c-2-1-3-2-5-4l-2-2z" class="I"></path><path d="M218 281c3 4 6 6 10 9s9 4 13 6c-2 0-4-1-6-2-8-2-15-4-19-12 1 0 2 1 2 2l1 1c1 1 3 3 4 3-2-2-4-3-5-7z" class="Z"></path><path d="M182 226c2 0 3 0 5-1 2 0 4 1 6 0 2 2 2 1 4 1h7l1 1-2 1v-1c-2 0-4 0-6 1h-4c-3 1-6 3-9 4l5-4-1-1c-2 0-5 1-8 1l1-1 1-1z" class="B"></path><path d="M214 224c1 0 2 1 3 1 1 1 3 1 3 3l-2-1-9 3c-2-1-3-1-4 0-1 0-2 1-3 1h-1v-1h-1l3-2 2-1h0l3-1c2-1 4-1 6-2z" class="D"></path><path d="M214 224c1 0 2 1 3 1 1 1 3 1 3 3l-2-1s0-1-1-1h-3c-2 1-6 2-9 1l3-1c2-1 4-1 6-2z" class="b"></path><path d="M218 257v1c0 4-1 8-1 13-1 3 0 6 1 10s3 5 5 7c-1 0-3-2-4-3l-1-1c0-1-1-2-2-2v-1c-5-9-2-16 2-24z" class="G"></path><path d="M205 269s1 1 2 1l1-1 1 1v2l1 4c0 1 0 1 1 2l2 5v1l1 2h0c1 2 2 2 3 3l-1 1-1-1c-3 0-5-2-6-4-3-2-6-6-6-8h1 0v-1c1-2 1-5 1-7z" class="H"></path><path d="M225 269l1-1h3l1 2c1 2 1 3 2 4l2 1c1 2 7 6 9 7l6 3 3 2h-2c-1 0-2-1-3-1h-1-1-1c-1 0-2 0-3-1-4-2-8-5-12-8h-1c-1-1-1-1-2 0-1-1-1-1-1-2l1-2-1-4z" class="Q"></path><path d="M226 273l3 4h-1c-1-1-1-1-2 0-1-1-1-1-1-2l1-2z" class="B"></path><path d="M200 219c4 0 8 2 12 3l1 2h1c-2 1-4 1-6 2l-3 1h0l-1-1h-7c-2 0-2 1-4-1-2 1-4 0-6 0-2 1-3 1-5 1l4-3c1-1 3-2 5-3 1 1 1 0 2 0l2 1c1-1 2-1 3-1l2-1z" class="H"></path><path d="M193 225h14l1 1-3 1h0l-1-1h-7c-2 0-2 1-4-1z" class="C"></path><path d="M195 221c1-1 2-1 3-1 0 1 0 1-1 2h5c3 0 6 0 9 1h-19l3-2z" class="F"></path><path d="M200 219c4 0 8 2 12 3l1 2-2-1c-3-1-6-1-9-1h-5c1-1 1-1 1-2l2-1zm-7 1l2 1-3 2c-2 1-3 1-5 2s-3 1-5 1l4-3c1-1 3-2 5-3 1 1 1 0 2 0z" class="M"></path><defs><linearGradient id="f" x1="216.201" y1="269.986" x2="226.724" y2="272.795" xlink:href="#B"><stop offset="0" stop-color="#949393"></stop><stop offset="1" stop-color="#c7c6c5"></stop></linearGradient></defs><path fill="url(#f)" d="M217 255h1 1c2 4 3 5 3 9v2c-1 2-1 3 0 5-1 3-1 4 1 6h0l4 4h0l-3-2h0c1 2 3 4 3 5v1c0 1 1 1 0 2h0c-1-2-1-2-2-3h-2-1c2 2 4 3 6 6-4-3-7-5-10-9-1-4-2-7-1-10 0-5 1-9 1-13v-1c0-1 0-1-1-2z"></path><path d="M185 256v-6c1 1 0 1 0 2s1 1 1 2v1c3 14 11 28 23 36l2 1h0c6 3 12 5 19 7 8 3 17 5 26 3l6-2 1 2c-6 2-12 2-18 3-10-1-19-3-28-7-4-2-9-4-12-7-5-4-8-9-12-13-5-8-6-14-8-22z" class="a"></path><defs><linearGradient id="g" x1="270.164" y1="270.843" x2="249.337" y2="289.657" xlink:href="#B"><stop offset="0" stop-color="#b0afaf"></stop><stop offset="1" stop-color="#e2e2e1"></stop></linearGradient></defs><path fill="url(#g)" d="M240 274c2 2 3 3 6 3h1 3c1 0 2-1 2-2l1-1v1h1v3c2 3 5 4 8 5 5 1 12 1 17 0h2c1-1 3-1 5-1h2v1l2 1-1 1h1c1 3 7 6 9 9h-9-3v-1c-2-3-7-3-9-4h-1c-2-1-7 0-9 0-3-2-10 0-14 0-3 0-7-2-10-2v-1h1 1 1c1 0 2 1 3 1h2l-3-2-6-3c-2-1-8-5-9-7 2 0 4 0 6-1z"></path><path d="M243 282c3-1 5 1 8 2-1 1-1 1-2 1l-6-3z" class="W"></path><path d="M279 283h2c1-1 3-1 5-1h2v1l2 1-1 1s-1 0-2 1v1h-6l-1-1c2 0 3 0 3-2h-4v-1z" class="D"></path><path d="M279 284h-4c-4 1-9 2-13 1-4 0-7-1-9-4-1-1 0-2 0-3h1c2 3 5 4 8 5 5 1 12 1 17 0v1z" class="V"></path><defs><linearGradient id="h" x1="270.629" y1="274.567" x2="266.067" y2="284.116" xlink:href="#B"><stop offset="0" stop-color="#c0bebe"></stop><stop offset="1" stop-color="#e0e0df"></stop></linearGradient></defs><path fill="url(#h)" d="M258 262l2 2 2 2 5 3 3 1 4 1 4 1c3 1 7 2 10 4h1l1 3h-1c0 1 0 2-1 3h-2c-2 0-4 0-5 1h-2c-5 1-12 1-17 0-3-1-6-2-8-5v-3h-1v-1l-1 1c0 1-1 2-2 2h-3 1c1-1 2-1 3-1 0-1 1-2 2-3l1 1c1 0 1-1 2-2-1-2 0-4 0-6v-1c1-2 1-2 2-3z"></path><path d="M273 276h3c4 1 8 1 12 3-1 0-1 0-1 1h0-2-2l-1-1c-4 0-6-1-9-2-1 0-1 1-1 0l1-1z" class="P"></path><path d="M256 272c2 2 3 3 6 4 1 0 3 1 4 1 0 1 0 1-1 1-1 1-5-1-7-1l-2-2h-1-1-1v-1l-1 1c0 1-1 2-2 2h-3 1c1-1 2-1 3-1 0-1 1-2 2-3l1 1c1 0 1-1 2-2z" class="I"></path><path d="M278 272c3 1 7 2 10 4h1l1 3h-1-1c-4-2-8-2-12-3h-3c-1-1-1 0-1-1h5 0c-1 0-3-1-4-1 0-2 3 0 4-1 0 0 1 0 1-1z" class="T"></path><path d="M288 276h1l1 3h-1-1c-4-2-8-2-12-3h11 1z" class="V"></path><path d="M258 262l2 2 2 2 5 3 3 1 4 1 4 1c0 1-1 1-1 1-1 1-4-1-4 1 1 0 3 1 4 1h0-5c-2-1-2-1-4-1v1l2 1h0l-4 1c-1 0-3-1-4-1-3-1-4-2-6-4-1-2 0-4 0-6v-1c1-2 1-2 2-3z" class="W"></path><path d="M258 267c1 1 2 3 4 3l1 1h-1c-2 0-3 0-5-1v-1h1l1 1h0c-1-1-1-2-1-3z" class="L"></path><path d="M267 269l3 1 4 1 4 1c0 1-1 1-1 1-1 1-4-1-4 1-2 0-4-1-6-2-1 0-2-1-3-1 1-1 2-1 3-2z" class="K"></path><path d="M267 269l3 1-1 2h0-2 0c-1 0-2-1-3-1 1-1 2-1 3-2z" class="D"></path><path d="M258 262l2 2 2 2 5 3c-1 1-2 1-3 2h-1l-1-1c-2 0-3-2-4-3l-2-1v-1c1-2 1-2 2-3z" class="B"></path><path d="M262 270c-1-2-3-3-3-5l2 1h1l5 3c-1 1-2 1-3 2h-1l-1-1z" class="F"></path><path d="M254 233h1l-1 4 1 1 1 2-1 7c0 3 0 8 1 11l1 1v1l1 2c-1 1-1 1-2 3v1c0 2-1 4 0 6-1 1-1 2-2 2l-1-1c-1 1-2 2-2 3-1 0-2 0-3 1h-1-1c-3 0-4-1-6-3-2 1-4 1-6 1l-2-1c-1-1-1-2-2-4l-1-2c-2-3-3-6-4-10h-1v-2c-2-1-3-1-4-3 3 1 5 1 7 1 1 0 3-1 4 0l3-1c2 0 3 1 5 1 2-1 7-8 8-10l1-1s-1-1-2-1l1-2c1-1 2-2 3-4h0l1-1c1-1 2-1 3-2z" class="a"></path><path d="M256 258l1 1v1l1 2c-1 1-1 1-2 3v1c0 2-1 4 0 6-1 1-1 2-2 2l-1-1c-1 1-2 2-2 3-1 0-2 0-3 1 2-4 4-9 6-13 2-2 2-3 2-6z" class="P"></path><path d="M237 262l2 8 1 4c-2 1-4 1-6 1l-2-1c-1-1-1-2-2-4 1 0 0 0 1-1s2-1 3-2c2-1 2-3 2-5h1z" class="F"></path><path d="M232 274c1-1 1-2 2-2l3 1h0l2-3 1 4c-2 1-4 1-6 1l-2-1z" class="C"></path><path d="M231 257h4c1-1 2-1 3-1-1 2-1 4-1 5v1h-1c0 2 0 4-2 5-1 1-2 1-3 2s0 1-1 1l-1-2c-2-3-3-6-4-10h-1l1-1h5 1z" class="R"></path><path d="M231 257h4c1-1 2-1 3-1-1 2-1 4-1 5l-1-3h0c-1 0-2 0-2-1l-1 1h-1v2h0 1l1-1v3c-1 1-2 1-3 2l-1-1 1-1h1c-1-2-2-3-2-5h1z" class="T"></path><path d="M224 258l1-1h5c0 2 1 3 2 5h-1l-1 1h0l-1-1-3-3-1-1h-1z" class="U"></path><path d="M254 233h1l-1 4c-3 6-7 12-12 15-1 2-2 3-4 4-1 0-2 0-3 1h-4-1-5l-1 1v-2c-2-1-3-1-4-3 3 1 5 1 7 1 1 0 3-1 4 0l3-1c2 0 3 1 5 1 2-1 7-8 8-10l1-1s-1-1-2-1l1-2c1-1 2-2 3-4h0l1-1c1-1 2-1 3-2z" class="R"></path><path d="M250 236l1-1c1-1 2-1 3-2-1 3-4 4-5 7v2l-1 1s-1-1-2-1l1-2c1-1 2-2 3-4h0z" class="J"></path><path d="M235 254c1 1 2 1 3 1 1-1 2-1 3-3h1 0c-1 2-2 3-4 4-1 0-2 0-3 1h-4l-1-1h0c1-1 3-1 5-2z" class="F"></path><path d="M220 253c3 1 5 1 7 1 1 0 3-1 4 0h4c-2 1-4 1-5 2h0l1 1h-1-5l-1 1v-2c-2-1-3-1-4-3z" class="M"></path><path d="M742 293l8 1c6 1 11 2 16 3 6 2 11 5 18 6l-1 1h-3l-5-1-1 1-1 1c1 1 1 2 2 3l-1 1c2 2 4 4 6 5 5 4 10 5 16 5v1h-1l-2 1h1l1 1h2-1v2l1 1c-2 4-6 10-10 11-1 0-1 1-2 1-7 2-15 2-21-1l-9-6c-1 1-6 12-7 12 0 2-2 8-4 9l-11 23c-1 3-2 6-4 9l-4 11h-2c-2 1-3 0-5 1-3 0-6 0-9-1h-1 0c-1 1-1 1-2 1s-2 0-2 1l-1 1-2 1 1-3s1-1 1-2c0-2 0-2-2-3l-1 1h-2-1c0-2-1-4-2-5 0-1 0-1-1-1-1 1-1 2-2 4h-1c0-1 1-2 1-3v-3h1c-2-2-3-9-4-11v-1c-1 1-1 0-2 1h0c-1 0-2-1-2-2l-1-5v1c-1-1-1-1-2-1v-5-5-3c0-3 2-5 3-7l-2-1v-1c1-1 2-3 2-4l2-3c1 0 1 0 1-1 2-3 4-5 7-8l7-7 17-15c1-1 4-5 6-5 0-1 2-1 3-1s2 0 3-1l-2-1c1-1 1-1 1-2 4-1 8 0 11-1l1-1z" class="a"></path><path d="M691 358c1 2 2 3 5 4 3 0 6-2 9-2-1 1-2 2-2 3-2 1-4 3-7 3-1 0-1-1-2-1l-1-1-2-4-1-1 1-1z" class="C"></path><path d="M740 342c1-2 2-4 4-7-1 2-2 4-2 6l1 1c0-1 1-2 1-3l1-1h0c0 2-2 7-2 9 1 0 1-1 2-2s1-2 3-3h0c0 2-2 8-4 9v-1-2c0 2-1 2-2 3h-1l1-4c0-2-1-3-2-5z" class="S"></path><path d="M708 336c2 1 4 2 5 5h0-6c-6-1-12 1-16 5l-1-1c1-3 3-6 6-7 4-1 7-2 12-2z" class="I"></path><path d="M701 331l3 3 4 2c-5 0-8 1-12 2-3 1-5 4-6 7l1 1c-2 3-2 5-1 8 0 1 1 4 1 4l-1 1 1 1 2 4c-2-1-3-2-4-4l-2-3c-2-4-1-7 0-11 1-2 2-3 3-4h0c1-1 1-2 2-3s3-1 4-2 2-1 3-2l1-1c0-1 0-2 1-3z" class="K"></path><path d="M701 331l3 3c-1 1-3 1-5 1l1-1c0-1 0-2 1-3z" class="J"></path><path d="M705 360c1-1 4-1 5 0h1c-5 3-9 5-11 12-2 5 0 11 3 15 2 3 3 5 6 7h-1 0c-1 1-1 1-2 1s-2 0-2 1l-1 1-2 1 1-3s1-1 1-2c0-2 0-2-2-3h0v-1c-1-1-2-2-2-3-1-2-1-3 0-5l-1-3v-6c-2-1-3-2-4-4v-3c1 0 1 1 2 1 3 0 5-2 7-3 0-1 1-2 2-3z" class="P"></path><path d="M703 363c-2 3-4 6-5 9-2-1-3-2-4-4v-3c1 0 1 1 2 1 3 0 5-2 7-3z" class="J"></path><path d="M699 381c2 5 4 9 8 13h0 1c-1 1-1 1-2 1s-2 0-2 1l-1 1-2 1 1-3s1-1 1-2c0-2 0-2-2-3h0v-1c-1-1-2-2-2-3-1-2-1-3 0-5z" class="T"></path><path d="M695 327v1l2-1c1 0 1-1 3-1v4l1 1c-1 1-1 2-1 3l-1 1c-1 1-2 1-3 2s-3 1-4 2-1 2-2 3h0c-1 1-2 2-3 4l-2-2v1l-2-1v-1c1-1 2-3 2-4l2-3c1 0 1 0 1-1 2-3 4-5 7-8z" class="U"></path><path d="M690 338l2-3c1 0 1 0 2-1l3-2c1-1 0-2 1-3l2 1 1 1c-1 1-1 2-1 3l-3-2c0 1-1 2-2 2-1 1-1 2-2 3-1 0-2 1-3 1z" class="T"></path><path d="M690 338c1 0 2-1 3-1 1-1 1-2 2-3 1 0 2-1 2-2l3 2-1 1c-1 1-2 1-3 2s-3 1-4 2-1 2-2 3h0c-1 1-2 2-3 4l-2-2c0-2 4-4 5-6z" class="R"></path><path d="M685 344l2 2c-1 4-2 7 0 11l2 3c1 2 2 3 4 4l1 1v3c1 2 2 3 4 4v6l1 3c-1 2-1 3 0 5 0 1 1 2 2 3v1h0l-1 1h-2-1c0-2-1-4-2-5 0-1 0-1-1-1-1 1-1 2-2 4h-1c0-1 1-2 1-3v-3h1c-2-2-3-9-4-11v-1c-1 1-1 0-2 1h0c-1 0-2-1-2-2l-1-5v1c-1-1-1-1-2-1v-5-5-3c0-3 2-5 3-7v-1z" class="X"></path><path d="M682 352l1-1h2v4h-2c-1 2-1 3-1 5v-5-3z" class="M"></path><path d="M695 379c0-2 0-4-1-5 0-2-1-3-1-4 2 2 3 5 5 8l1 3c-1 2-1 3 0 5 0 1 1 2 2 3v1l-1-1c-1 0-2-1-2-2-2-3-2-5-3-8z" class="R"></path><path d="M682 360c0-2 0-3 1-5h2c0 4 0 11 2 14h0l1-3h0l2 5-1 1v-1c-1 1-1 0-2 1h0c-1 0-2-1-2-2l-1-5v1c-1-1-1-1-2-1v-5z" class="F"></path><path d="M690 371c2 1 2 2 3 4 1 1 1 3 2 4 1 3 1 5 3 8 0 1 1 2 2 2l1 1h0l-1 1h-2-1c0-2-1-4-2-5 0-1 0-1-1-1-1 1-1 2-2 4h-1c0-1 1-2 1-3v-3h1c-2-2-3-9-4-11l1-1z" class="D"></path><defs><linearGradient id="i" x1="746.758" y1="350.068" x2="725.641" y2="378.231" xlink:href="#B"><stop offset="0" stop-color="#3c3c3c"></stop><stop offset="1" stop-color="#6f6e6e"></stop></linearGradient></defs><path fill="url(#i)" d="M740 342c1 2 2 3 2 5l-1 4h1c1-1 2-1 2-3v2 1l-11 23c-1 3-2 6-4 9l-4 11h-2c-2 1-3 0-5 1 0-3 0-4 1-6l1-2 17-39 3-6z"></path><path d="M719 389c1 1 1 2 2 4 2-2 4-2 5-5 1-2 1-4 3-5l-4 11h-2c-2 1-3 0-5 1 0-3 0-4 1-6z" class="T"></path><defs><linearGradient id="j" x1="727.094" y1="352.119" x2="729.443" y2="385.913" xlink:href="#B"><stop offset="0" stop-color="#7e7d7d"></stop><stop offset="1" stop-color="#a1a09f"></stop></linearGradient></defs><path fill="url(#j)" d="M737 348c1 2 1 5 1 7l-1 2-6 13c-2 5-4 14-9 17h-2l17-39z"></path><defs><linearGradient id="k" x1="794.273" y1="326.885" x2="725.604" y2="310.074" xlink:href="#B"><stop offset="0" stop-color="#bbbab9"></stop><stop offset="1" stop-color="#fbfbfa"></stop></linearGradient></defs><path fill="url(#k)" d="M742 293l8 1c6 1 11 2 16 3 6 2 11 5 18 6l-1 1h-3l-5-1-1 1-1 1c1 1 1 2 2 3l-1 1c2 2 4 4 6 5 5 4 10 5 16 5v1h-1l-2 1h1l1 1h2-1v2l1 1c-2 4-6 10-10 11-1 0-1 1-2 1-6 0-11 1-16 0h0c-4-2-9-4-12-6-3-3-5-6-8-9-6-5-14-8-22-9-3 0-7 0-11 1-4 2-8 3-11 6-2 2-4 5-5 6-2 0-2 1-3 1l-2 1v-1l7-7 17-15c1-1 4-5 6-5 0-1 2-1 3-1s2 0 3-1l-2-1c1-1 1-1 1-2 4-1 8 0 11-1l1-1z"></path><path d="M739 309c1 0 3 1 4 1v1c-1 0-2 1-3 1-2 0-6-2-7-2h2c1-1 1-1 2-1h2z" class="B"></path><path d="M736 299c1-1 3-1 5-1 1 0 1 1 2 1 2 1 6 1 9 2-3 0-7-1-9 0h0c2 0 3 1 4 1v1h-3c-1 0-1-1-2-1v-1c-2-1-4-2-6-2z" class="W"></path><path d="M794 321l1 1c-2 1-3 4-5 6-2 1-3 2-5 2 0-1 1-1 1-2l3-1 1-2-1-1h-3c-1 1-2 1-3 0h4v-1h3c2-1 3-1 4-2z" class="M"></path><path d="M744 303h3c1 1 4 2 5 3 5 4 8 7 12 12-5-1-8-8-12-10-3-2-5-4-8-5z" class="B"></path><path d="M741 294l6 1-4 2v1l5 1c2 0 4 1 6 2v1l-2-1c-3-1-7-1-9-2-1 0-1-1-2-1-2 0-4 0-5 1l-5-1-2-1c1-1 1-1 1-2 4-1 8 0 11-1z" class="C"></path><path d="M795 322h2-1v2l1 1c-2 4-6 10-10 11v-1c0-1 2-2 4-3 0 0 2-1 2-2l1-1v-2l-2 2c-4 5-9 5-14 5 2-2 4-3 6-4h1c2 0 3-1 5-2 2-2 3-5 5-6z" class="M"></path><path d="M790 328v1l-1 1c-2 2-3 2-5 3h-1l1-3h1c2 0 3-1 5-2z" class="b"></path><path d="M795 322h2-1v2c-2 3-4 5-7 6l1-1v-1c2-2 3-5 5-6z" class="X"></path><path d="M716 314v-1c2-2 4-2 6-4 2-1 4-1 6-2-2-1-3 0-4-1l6-6c3 0 6 0 8 1-1 0-2 0-2 1-2 0-1 0-2 1h-1c-1 0-1 0-2 1h0v1c-1 0-1 1-2 1 3 1 5 1 7 2l3 1h-2c-1 0-1 0-2 1h-2c-3 0-5 0-7 1h0l1 2c-3 0-7 0-11 1z" class="I"></path><path d="M731 298l5 1c2 0 4 1 6 2v1l-4-1c-2-1-5-1-8-1l-6 6c1 1 2 0 4 1-2 1-4 1-6 2-2 2-4 2-6 4v1c-4 2-8 3-11 6-2 2-4 5-5 6-2 0-2 1-3 1l-2 1v-1l7-7 17-15c1-1 4-5 6-5 0-1 2-1 3-1s2 0 3-1z" class="g"></path><path d="M747 295c5 1 11 3 15 6 5 4 9 9 13 14 2 2 5 4 8 6 3 1 7 0 10 0h1c-1 1-2 1-4 2h-3c-4 0-7 0-11-2l-4-4c-4-4-7-8-12-12l-2-2h-1c-1-1-2-1-3-2-2-1-4-2-6-2l-5-1v-1l4-2z" class="Q"></path><path d="M742 293l8 1c6 1 11 2 16 3 6 2 11 5 18 6l-1 1h-3l-5-1-1 1-1 1c1 1 1 2 2 3l-1 1c2 2 4 4 6 5 5 4 10 5 16 5v1h-1l-2 1c-3 0-7 1-10 0-3-2-6-4-8-6-4-5-8-10-13-14-4-3-10-5-15-6l-6-1 1-1z" class="a"></path><path d="M774 309l-8-9 9 3-1 1-1 1c1 1 1 2 2 3l-1 1z" class="I"></path><path d="M759 227c2 1 4 3 5 4v15c0 3 0 6-1 9l2 3h1c1-1 1-2 2-3v-1c1-1 2-2 2-3h1v-3l5-4h-1l2-2c1-2 2-3 2-6 0-1 1-1 1-2l1 1 4 4c2 1 3 1 4 3l2 3h0l2 3 1 1c0 1 1 1 1 2 1 1 2 2 3 2s3 1 4 0c3 0 4 0 5-2h5 1l2 2 1-1c3-2 5-4 8-7 1-1 1-2 3-2v1l2 1c1 0 2 1 3 1 2 0 4 0 6-1 2-2 2-4 2-6l1-1v-2c-1-2-3-3-4-5h1c1 1 0 0 1 0 1 1 1 2 3 2-1-1-1-2-2-2 2 0 3 2 4 2-1-2-3-3-4-4h2c3 1 5 0 8 0 3 1 6 4 8 6v1h1c1 3 1 5 1 8h0c-1 4-1 8-4 10l1 2c-3 4-4 9-5 14 1 0 0 0 1-1l2 1-5 3v1l2-1v1c-1 0-2 1-3 2h0 0c-1 1-3 2-4 2-4 3-8 7-11 10-13 11-28 15-44 17-3 0-7 0-10-1h3l1-1c-7-1-12-4-18-6l-16-3-8-1-1 1c-3 1-7 0-11 1 0 1 0 1-1 2l2 1c-1 1-2 1-3 1s-3 0-3 1c-2 0-5 4-6 5l-17 15-7 7c-3 3-5 5-7 8 0 1 0 1-1 1h-1l-3 1h-1v1c-1 1-3 1-4 2l-25 25c-1 1-3 2-5 3-1-1-1-1-3 0-1 0-2 1-3 1l-1-1c0-1 1-3 2-3l2 1 1-1c1-1 4-3 5-5 2-2 5-4 6-6h0c2-1 4-3 5-5l47-49-1-1c2-1 4-3 4-5 1-2 3-2 4-4 2-2 3-3 4-5l3-3 3-3v1h2l3-3 14-13c3-3 7-6 9-9 0-1 1-1 2-2v-2c1-2 1-3 1-5l-1-1 2-2 1 1c0-3 1-9-1-12h0c0-2 1-3 1-5z" class="L"></path><path d="M854 249c0-2 0-5 1-7v6l-1 1h0zm-50 27c-1 1-3 2-5 3v-1c1-2 2-3 3-6h0c0 1 1 3 2 4zm-15-1l2 3c-2 2-3 3-5 4h-2c1-1 1-3 1-4l1 1h0 1l2-4z" class="B"></path><path d="M788 284c0 2-1 2-3 3-1 1-3 1-4 2-3 0-5 1-7 0h0c5-1 10-3 14-5z" class="C"></path><path d="M828 268l-1-3c-1-3-2-8-1-10v1h0c1 1 1 2 2 3 0 1 2 2 2 3v8h0l-2-2z" class="W"></path><path d="M828 274c1 1 1 1 1 2h1v2l-2 4c-1 3-4 5-6 7h-1c3-4 5-10 7-15z" class="U"></path><path d="M854 249h0v2c-2 8-4 16-7 23v-2h0v-1l1-1h0v-2h1v-1-2c-1 2-2 5-4 7h0c1-3 3-6 4-9 2-5 3-9 5-14z" class="S"></path><path d="M841 238c1 2 1 2 0 4 0 2-3 4-5 5-1 1-2 1-4 0h-1c-1 1-1 0-2 0s0 0-1 1v1h0l-4-4c1-1 1-2 3-2v1l2 1c1 0 2 1 3 1 2 0 4 0 6-1 2-2 2-4 2-6l1-1z" class="C"></path><path d="M788 284h1l5-3c-1 1-2 2-3 4h0c1 0 2 0 2-1 2-1 2-1 4-1 1 1 1 1 1 2-1 1-4 2-6 2l-2 1c-1 0-1 1-2 1h-7c1-1 3-1 4-2 2-1 3-1 3-3z" class="W"></path><path d="M821 289h1c2-2 5-4 6-7l2-4v3 1c-1 1-1 2-2 3h0v2c1-1 2-1 3-2h1c-4 4-10 8-15 10 1-2 6-4 8-6h-1c-1 0-1 1-2 1l-1 1c-1 1-2 1-4 0l2-1s1-1 2-1z" class="C"></path><path d="M814 260h3v1c1 0 1 0 1 1-1 3-1 6-2 10h-1l-4 4c1-1 1-2 1-3h0l1-2c1-1 1-1 1-2-1-1-2 0-3 0l1-1 1-3c0-1 1-3 1-5z" class="I"></path><path d="M812 268h1l3-3h1c-1 1-1 2-1 3v4h-1l-4 4c1-1 1-2 1-3h0l1-2c1-1 1-1 1-2-1-1-2 0-3 0l1-1z" class="E"></path><path d="M811 276l4-4v1c-1 4-3 7-6 9-1 1-1 2-2 3l1 1v1c1 0 2 0 3 1-1 1-3 2-4 2v-1h-1c-1 0-2 0-2-1-2-4 3-4 4-7h-2l1-1 4-4z" class="C"></path><path d="M801 266c1 1 3 1 4 2l8-3-1 3-1 1-4 5-3 2c-1-1-2-3-2-4v-4l-1-2z" class="P"></path><path d="M802 268c1 3 2 4 5 6l-3 2c-1-1-2-3-2-4v-4z" class="I"></path><path d="M824 245l4 4c-2 0-3 1-4 2h1 1 0c-1 2-5 2-6 4-1 0 0 1-1 1h-1v2 4c0-1 0-1-1-1v-1h-3c0-2 1-2 1-4h1c-1-2 0-3 0-4 3-2 5-4 8-7z" class="H"></path><path d="M847 274c3-7 5-15 7-23v4h1l1-1 1 2c-3 4-4 9-5 14 1 0 0 0 1-1l2 1-5 3v1l2-1v1c-1 0-2 1-3 2h0 0c-1 1-3 2-4 2 1-1 1-2 2-4zm-17-4h0 2 2c1-2 2-4 2-6 1-3 1-5 2-8v8c-1 4-2 8-4 11v1l2-1h1c-2 2-4 4-5 7h-1l-3 3c1-1 1-2 2-3v-1-3-2h-1c0-1 0-1-1-2-1-2-1-4 0-6l2 2z" class="B"></path><path d="M818 258c2 9 3 16-2 24-1 2-3 4-5 6-1-1-2-1-3-1v-1l-1-1c1-1 1-2 2-3 3-2 5-5 6-9v-1h1c1-4 1-7 2-10v-4z" class="F"></path><defs><linearGradient id="l" x1="825.171" y1="267.169" x2="800.87" y2="282.677" xlink:href="#B"><stop offset="0" stop-color="#070605"></stop><stop offset="1" stop-color="#323332"></stop></linearGradient></defs><path fill="url(#l)" d="M818 258v-2h1c4 6 6 13 4 20-2 6-6 12-11 15s-11 4-16 5c4-2 8-3 11-6 1 0 3-1 4-2 2-2 4-4 5-6 5-8 4-15 2-24z"></path><path d="M807 251h5 1l2 2 1-1c0 1-1 2 0 4h-1c0 2-1 2-1 4s-1 4-1 5l-8 3c-1-1-3-1-4-2v-1c0-2 0-3-1-5h0c-1-2-1-3-3-5v-1h1v-1c1 0 3 1 4 0 3 0 4 0 5-2z" class="R"></path><path d="M813 256h1l-3 3v1c0 1-1 2-2 2h0c-1 0-1-1-1-2-1 1-1 2-1 3h-1c-1-2-1-3-1-5l1-1h1 1c1-1 3-1 5-1z" class="T"></path><path d="M797 255h6c1 0 2 1 4 1v1h-1l-1 1c0 2 0 3 1 5h1v2h-3-3c0-2 0-3-1-5h0c-1-2-1-3-3-5z" class="D"></path><path d="M800 260c0-1 0-2-1-3h0l1-1c1 1 2 1 4 1l-1 3c-1 1-1 1-3 0z" class="T"></path><path d="M804 257h2l-1 1c0 2 0 3 1 5h1v2h-3-3c0-2 0-3-1-5 2 1 2 1 3 0l1-3z" class="e"></path><path d="M807 251h5 1l2 2 1-1c0 1-1 2 0 4h-1v-1l-2 1c-2 0-4 0-5 1h-1v-1c-2 0-3-1-4-1h-6v-1h1v-1c1 0 3 1 4 0 3 0 4 0 5-2z" class="S"></path><path d="M815 253l1-1c0 1-1 2 0 4h-1v-1l-2 1c-2 0-4 0-5 1h-1v-1c-2 0-3-1-4-1 4-1 8-1 12-2z" class="K"></path><path d="M845 272h0c2-2 3-5 4-7v2 1h-1v2h0l-1 1v1h0v2c-1 2-1 3-2 4-4 3-8 7-11 10-13 11-28 15-44 17-3 0-7 0-10-1h3l1-1c11-1 23-3 33-8 5-2 11-6 15-10 5-2 10-8 13-13z" class="V"></path><defs><linearGradient id="m" x1="782.616" y1="283.946" x2="769.504" y2="280.584" xlink:href="#B"><stop offset="0" stop-color="#1c1d1c"></stop><stop offset="1" stop-color="#373636"></stop></linearGradient></defs><path fill="url(#m)" d="M782 270c0 1 1 3 1 4 1 1 1 1 1 2s0 1-1 2h2c0 1 0 3-1 4-4 2-7 4-12 4-2 1-5-1-6 0-1 0-1 1-1 1-1 1-2 1-3 1-2 0-4 1-6 2-1 1-2 2-4 2h0l-2 2-8-1h-4-3c0-1 1-1 2-2 0 0 1 0 1-1 2-1 4-3 6-3 1 0 1-1 2-2h1l2-1h0v-1c0-1 1-2 1-3l2-1-1-1c2 0 5 0 7 1 1 1 0 0 0 1 2 0 2-1 4 0l7-2h3 0l2-2v-1l1-1 1 1c3-1 4-3 6-5z"></path><path d="M751 278c2 0 5 0 7 1 1 1 0 0 0 1l-1 1c1 0 3 2 5 2h5c-6 2-12 0-18 0 0-1 1-2 1-3l2-1-1-1z" class="D"></path><path d="M738 293c1 0 2-1 2-1l1-1h1l3-3 1-1c3 0 4-1 6-2s5 0 6 0l14 1c-2 1-5-1-6 0-1 0-1 1-1 1-1 1-2 1-3 1-2 0-4 1-6 2-1 1-2 2-4 2h0l-2 2-8-1h-4z" class="F"></path><path d="M782 270c0 1 1 3 1 4 1 1 1 1 1 2s0 1-1 2c-2 3-6 5-10 5-2 1-4 1-6 0h0-5c-2 0-4-2-5-2l1-1c2 0 2-1 4 0l7-2h3 0l2-2v-1l1-1 1 1c3-1 4-3 6-5z" class="E"></path><path d="M758 280c2 0 2-1 4 0l-1 1c3 1 7-1 10 0-2 2-2 0-4 1h-2s1 1 2 0c2 0 4 1 6 1-2 1-4 1-6 0h0-5c-2 0-4-2-5-2l1-1z" class="I"></path><path d="M777 242c1-2 2-3 2-6 0-1 1-1 1-2l1 1 4 4c2 1 3 1 4 3l2 3h0l2 3 1 1c0 1 1 1 1 2 1 1 2 2 3 2v1h-1v1c2 2 2 3 3 5 0 5 0 11-4 15-1 2-3 2-5 3l-2-3-2 4h-1 0l-1-1h-2c1-1 1-1 1-2s0-1-1-2c0-1-1-3-1-4s0-2 1-3c0-1 0-1 1-2l-1-2v-5-2-2h-2-2v-3-4l-1-3h-2 0-1l2-2z" class="a"></path><path d="M782 246l1 8h-2-2v-3-4c1 1 1 2 2 2 0-1 0-1 1-3z" class="K"></path><path d="M779 247c1 1 1 2 2 2v1c0 1 0 2-1 3l1 1h-2v-3-4z" class="b"></path><path d="M785 239c2 1 3 1 4 3l2 3h0l2 3 1 1c0 1 1 1 1 2-4-3-7-8-10-12z" class="Y"></path><defs><linearGradient id="n" x1="780.224" y1="247.068" x2="779.303" y2="236.517" xlink:href="#B"><stop offset="0" stop-color="#999898"></stop><stop offset="1" stop-color="#b7b7b6"></stop></linearGradient></defs><path fill="url(#n)" d="M777 242c1-2 2-3 2-6 0-1 1-1 1-2l1 1c0 3 1 8 1 11-1 2-1 2-1 3-1 0-1-1-2-2l-1-3h-2 0-1l2-2z"></path><path d="M782 270c0-1 0-2 1-3 0-1 0-1 1-2 1 4 3 7 5 10l-2 4h-1 0l-1-1h-2c1-1 1-1 1-2s0-1-1-2c0-1-1-3-1-4z" class="O"></path><path d="M759 227c2 1 4 3 5 4v15c0 3 0 6-1 9l2 3h1c1-1 1-2 2-3v-1c1-1 2-2 2-3h1v-3l5-4h0 2l1 3v4 3h2 2v2 2 5l1 2c-1 1-1 1-1 2-1 1-1 2-1 3-2 2-3 4-6 5l-1-1-1 1v1l-2 2h0-3l-7 2c-2-1-2 0-4 0 0-1 1 0 0-1-2-1-5-1-7-1l1 1-2 1c0 1-1 2-1 3v1h0l-2 1h-1c-1 1-1 2-2 2-2 0-4 2-6 3 0 1-1 1-1 1-1 1-2 1-2 2h3 4l-1 1c-3 1-7 0-11 1 0 1 0 1-1 2l2 1c-1 1-2 1-3 1s-3 0-3 1c-2 0-5 4-6 5l-17 15-7 7c-3 3-5 5-7 8 0 1 0 1-1 1h-1l-3 1h-1v1c-1 1-3 1-4 2l-25 25c-1 1-3 2-5 3-1-1-1-1-3 0-1 0-2 1-3 1l-1-1c0-1 1-3 2-3l2 1 1-1c1-1 4-3 5-5 2-2 5-4 6-6h0c2-1 4-3 5-5l47-49-1-1c2-1 4-3 4-5 1-2 3-2 4-4 2-2 3-3 4-5l3-3 3-3v1h2l3-3 14-13c3-3 7-6 9-9 0-1 1-1 2-2v-2c1-2 1-3 1-5l-1-1 2-2 1 1c0-3 1-9-1-12h0c0-2 1-3 1-5z" class="L"></path><path d="M716 290c2-2 3-3 4-5 1 0 1 1 2 1v1l-9 9-4 4-1-1c2-1 4-3 4-5 1-2 3-2 4-4z" class="U"></path><path d="M678 340c3-4 6-7 10-10 4-5 9-10 13-14h0c-2 4-5 7-8 10v1c3-2 5-6 8-7h1l-7 7c-3 3-5 5-7 8 0 1 0 1-1 1h-1l-3 1h-1v1c-1 1-3 1-4 2z" class="S"></path><path d="M758 243l1 1c0 2 1 6 0 8 0 1-2 2-2 3l-35 32v-1c-1 0-1-1-2-1l3-3 3-3v1h2l3-3 14-13c3-3 7-6 9-9 0-1 1-1 2-2v-2c1-2 1-3 1-5l-1-1 2-2z" class="G"></path><path d="M723 282l3-3v1h2c-2 2-3 4-6 5v1c-1 0-1-1-2-1l3-3z" class="e"></path><path d="M776 244h0 2l1 3v4 3h2 2v2 2 5l1 2c-1 1-1 1-1 2-1 1-1 2-1 3-2 2-3 4-6 5l-1-1-1 1v1l-2 2h0-3l-7 2c-2-1-2 0-4 0 0-1 1 0 0-1-2-1-5-1-7-1l1 1-2 1c0 1-1 2-1 3v1h0l-2 1h-1c-1 1-1 2-2 2-2 0-4 2-6 3 0 1-1 1-1 1-1 1-2 1-2 2h3 4l-1 1c-3 1-7 0-11 1 0 1 0 1-1 2l2 1c-1 1-2 1-3 1s-3 0-3 1c-2 0-5 4-6 5l-17 15h-1c-3 1-5 5-8 7v-1c3-3 6-6 8-10h0 0l57-56 5-5 2 3h1c1-1 1-2 2-3v-1c1-1 2-2 2-3h1v-3l5-4z" class="G"></path><path d="M754 272l1-1c1-2 4-2 6-3 1 1 1 1 2 1v1l-8 3-1-1z" class="S"></path><path d="M763 255l2 3h1c1-1 1-2 2-3 1 0 2 0 2 1-1 3-4 6-7 9 1-2 1-3 3-5 1 0 2-1 2-2v-1c-1 1-2 2-3 2-1 1 1 0-1 1l-1 1-1-2-4 1 5-5z" class="N"></path><path d="M748 275c1 0 2-1 4-1 0 0 0 1 1 2v1h0l-4 1c1 1 1 1 2 0l1 1-2 1c0 1-1 2-1 3v1h0l-2 1h-1c-1 1-1 2-2 2-2 0-4 2-6 3 0 1-1 1-1 1-1 1-2 1-2 2-2 0-2 0-3-1 2-1 3-2 5-4h1v-2c1 0 3-1 3-2v-1l1-1c2-2 4-3 5-5 0-1 1-1 1-2z" class="S"></path><path d="M748 275c1 0 2-1 4-1 0 0 0 1 1 2-2 0-3 0-5-1z" class="U"></path><path d="M742 282l2 1h0v1l-1 1c-1 0-1 1-2 1-1 1-2 2-3 2v-2c1 0 3-1 3-2v-1l1-1z" class="V"></path><defs><linearGradient id="o" x1="756.761" y1="275.892" x2="778.325" y2="272.689" xlink:href="#B"><stop offset="0" stop-color="#b8b7b7"></stop><stop offset="1" stop-color="#e5e5e3"></stop></linearGradient></defs><path fill="url(#o)" d="M776 244h0 2l1 3v4 3h2 2v2 2 5l1 2c-1 1-1 1-1 2-1 1-1 2-1 3-2 2-3 4-6 5l-1-1-1 1v1l-2 2h0-3l-7 2c-2-1-2 0-4 0 0-1 1 0 0-1-2-1-5-1-7-1-1 1-1 1-2 0l4-1h0v-1c-1-1-1-2-1-2l2-2 1 1 8-3v-1c-1 0-1 0-2-1h1l1-3c3-3 6-6 7-9 0-1-1-1-2-1v-1c1-1 2-2 2-3h1v-3l5-4z"></path><path d="M770 272l1 1c-6 2-12 4-18 4h0v-1c-1-1-1-2-1-2l2-2 1 1h1l1 1c4 1 10-1 13-2z" class="T"></path><path d="M781 259l2-1v5l1 2c-1 1-1 1-1 2-1 1-1 2-1 3-2 2-3 4-6 5l-1-1c1-1 4-2 5-3 0-1 1-3 1-5h1l-1-1c-3 3-6 6-10 7v1l-1-1c2-1 5-3 7-4 0-1 1-2 2-3s1-2 1-3v-1l1-2z" class="F"></path><path d="M781 259l2-1v5h-1-1 0v-2h-1l1-2z" class="M"></path><defs><linearGradient id="p" x1="765.164" y1="265.069" x2="772.771" y2="271.286" xlink:href="#B"><stop offset="0" stop-color="#969696"></stop><stop offset="1" stop-color="#c5c3c3"></stop></linearGradient></defs><path fill="url(#p)" d="M779 261h1v1c0 1 0 2-1 3s-2 2-2 3c-2 1-5 3-7 4-3 1-9 3-13 2l-1-1h-1l8-3v-1c3-2 8-3 11-6 1-1 3-1 5-2z"></path><path d="M779 261h1c-5 5-10 8-17 9v-1c3-2 8-3 11-6 1-1 3-1 5-2z" class="G"></path><defs><linearGradient id="q" x1="764.934" y1="260.385" x2="777.156" y2="250.729" xlink:href="#B"><stop offset="0" stop-color="#403e3d"></stop><stop offset="1" stop-color="#636262"></stop></linearGradient></defs><path fill="url(#q)" d="M776 244h0 2l1 3v4 3h2 2v2 2l-2 1-1 2v1-1h-1c-2 1-4 1-5 2-3 3-8 4-11 6-1 0-1 0-2-1h1l1-3c3-3 6-6 7-9 0-1-1-1-2-1v-1c1-1 2-2 2-3h1v-3l5-4z"></path><path d="M776 244h0 2l1 3v4c-1 1-2 1-2 3h-1c0-2 1-3 1-4-1-1-1-2-2-3-1 2-2 3-4 3v1-3l5-4z" class="J"></path><path d="M762 268c6-2 11-8 14-14h1v1c-1 2-3 4-4 6v1c1 1 1 0 1 1-3 3-8 4-11 6-1 0-1 0-2-1h1z" class="R"></path><path d="M777 254c0-2 1-2 2-3v3h2 2v2 2l-2 1-1 2v1-1h-1c-2 1-4 1-5 2 0-1 0 0-1-1v-1c1-2 3-4 4-6v-1z" class="J"></path><path d="M778 254h1 2 2v2 2l-2 1c-1-1-1-2-2-3l-1-2z" class="b"></path><path d="M778 254h1 2 2v2h-4l-1-2z" class="X"></path><path d="M777 254c0-2 1-2 2-3v3h-1c-1 2-2 4-2 6 1 0 1 1 3 1-2 1-4 1-5 2 0-1 0 0-1-1v-1c1-2 3-4 4-6v-1z" class="g"></path><path d="M153 175c5 2 11 2 16 2h27c5 0 10-1 14 0l228 1 12-1 10 22 7 16c1 3 2 6 4 9h0c-3-2-5-4-7-7h-1-25c-1 0-2-1-3 0v-1-5c0-7-3-12-7-16-12-11-25-14-41-13-4 0-9 0-13 2-4 1-6 4-8 6-1 0-2 1-2 1-1 0-3 0-3-1-3 0-7-1-10-1-4-1-8-1-12-1l-21 1c-2 3-5 6-8 9l-14 12h-1c1-2 1-3 0-5-6 4-17 9-20 16l-1-1v-1c-2 1-2 2-3 4h-3c0-1 0-2-1-2-1 1-2 2-2 3h-1c0 1-1 3-1 3l-6 6v-1h-1-1v1h-1c-1 1-2 1-3 2l-1 1h0c-1 2-2 3-3 4l-1 2c1 0 2 1 2 1l-1 1c-1 2-6 9-8 10-2 0-3-1-5-1l-3 1c-1-1-3 0-4 0-2 0-4 0-7-1 0-1-4-3-5-4h0c-1-1-6-7-7-7-1-1-5 1-7 1-1-1-1-1-1-2v-3c1-4 5-6 9-8l9-3 2 1c0-2-2-2-3-3-1 0-2-1-3-1h-1l-1-2c-4-1-8-3-12-3l-2 1c-1 0-2 0-3 1l-2-1c-1 0-1 1-2 0-2 1-4 2-5 3h-3c-2 1-3 3-4 3s-2 0-3 1l-1-1-2 2-2-1c4-5 8-10 14-13 0-1 0-2-1-3v-1c-1-1-1-2-2-3-2-3-5-5-8-7l-21-25z" class="a"></path><path d="M210 206c5 1 8 7 12 10 1 1 0 1 1 1 1 1 2 2 3 2h1l11 11c-5-1-7-5-12-7l-16-17z" class="H"></path><defs><linearGradient id="r" x1="249.53" y1="227.996" x2="243.054" y2="236.554" xlink:href="#B"><stop offset="0" stop-color="#7b7b79"></stop><stop offset="1" stop-color="#979696"></stop></linearGradient></defs><path fill="url(#r)" d="M226 223c5 2 7 6 12 7 2 1 4 4 6 5h3c1 0 3-2 4-3l3-3c1 1 1 2 1 3v1h-1c-1 1-2 1-3 2l-1 1h-1l-3 3-4-2c-5-3-11-9-16-14z"></path><path d="M242 237l4 2 3-3h1 0c-1 2-2 3-3 4l-1 2c1 0 2 1 2 1l-1 1c-1 2-6 9-8 10-2 0-3-1-5-1-2-1-6-1-8-3l1-1c1-2 3-3 4-4 4-3 7-6 11-8z" class="X"></path><path d="M227 249c7 0 13-5 20-5-1 2-6 9-8 10-2 0-3-1-5-1-2-1-6-1-8-3l1-1z" class="I"></path><path d="M438 178l12-1 10 22 7 16c1 3 2 6 4 9h0c-3-2-5-4-7-7h-1-25c2-1 6-1 8-1h17l-17-38h-8z" class="R"></path><path d="M153 175c5 2 11 2 16 2h27c5 0 10-1 14 0h-50l32 35c-3 0-5 1-7 2 0-1 0-2-1-3v-1c-1-1-1-2-2-3-2-3-5-5-8-7l-21-25z" class="F"></path><defs><linearGradient id="s" x1="187.219" y1="213.554" x2="188.041" y2="225.462" xlink:href="#B"><stop offset="0" stop-color="#403f3f"></stop><stop offset="1" stop-color="#747373"></stop></linearGradient></defs><path fill="url(#s)" d="M192 212c2 2 6 1 9 2 6 2 12 6 16 11-1 0-2-1-3-1h-1l-1-2c-4-1-8-3-12-3l-2 1c-1 0-2 0-3 1l-2-1c-1 0-1 1-2 0-2 1-4 2-5 3h-3c-2 1-3 3-4 3s-2 0-3 1l-1-1-2 2-2-1c4-5 8-10 14-13 2-1 4-2 7-2z"></path><path d="M179 223c0-2 3-4 5-5h2c2-2 4-2 7-2 7-1 14 2 19 6-4-1-8-3-12-3l-2 1c-1 0-2 0-3 1l-2-1c-1 0-1 1-2 0-2 1-4 2-5 3h-3c-2 1-3 3-4 3s-2 0-3 1l-1-1 4-3z" class="J"></path><path d="M187 218h3 1c2 0 4-1 7-1 1 1 1 2 2 2l-2 1c-1 0-2 0-3 1l-2-1c-1 0-1 1-2 0h0v-1c-1 0-3 0-4-1z" class="R"></path><path d="M198 217c1 1 1 2 2 2l-2 1c-1 0-2 0-3 1l-2-1c2-1 3-2 5-3z" class="Y"></path><path d="M187 218c1 1 3 1 4 1v1h0c-2 1-4 2-5 3h-3c-2 1-3 3-4 3s-2 0-3 1l-1-1 4-3c2-2 6-3 8-5z" class="N"></path><path d="M210 206c-1 0-12-12-14-13h67 17c3 0 7-1 10 0 2 2 3 4 4 7l1 5c-6 4-17 9-20 16l-1-1v-1c-2 1-2 2-3 4h-3c0-1 0-2-1-2-1 1-2 2-2 3h-1c0 1-1 3-1 3l-6 6v-1h-1-1c0-1 0-2-1-3l-3 3c-1 1-3 3-4 3h-3c-2-1-4-4-6-5l-11-11h-1c-1 0-2-1-3-2-1 0 0 0-1-1-4-3-7-9-12-10z" class="Q"></path><path d="M238 207h2v1 1h0-3l1-2z" class="d"></path><path d="M250 210v2l1 1h0c-1 1-2 1-2 2v1c0 1 0 1-1 2l1 1h-1-8c2-2 4-5 6-6l1-1 3-2z" class="B"></path><path d="M260 201l2-1 1-1 4 1c-1 1-2 1-3 1v1h2l-1 1h-1c0 1 0 1 1 1v1h-3l-1-1c-2 1-5 4-6 6l-2 2c-1 2-3 3-4 4v-1c0-1 1-1 2-2h0l-1-1v-2h1c0-2 1-3 2-3 1-1 1-2 2-3h1c0-1 0-1-1-2l1-1v1l1 1h2c1 0 1-1 1-2z" class="E"></path><path d="M227 219c-7-8-15-15-23-23l15 1 9-1-2 2-1-1c0 1-1 1-1 2l-3 3h0c1-2 2-3 3-3l-1-2c-1 1-1 1-2 1h-2l-2 2v-1-1h-3c-1 1-1 1-1 2l1 1 1-2h1l-1 2v1l-1 1 1 1c0-1 1-1 1-2l1-1c1 0 2-1 3-1 0 1-2 2-3 3v1h0c2-1 2-1 3-1l-1 2h0c1 0 2-1 3-1 0 1-1 2-1 3h1 1 0v2c0 1-1 1-2 2 2 0 3-1 4-2h1c-1 1-1 1-1 3l-1 1v1c2-1 2-1 3-2l1-2v1h2c0 1 0 1 1 2l1-1 5 5c1 1 2 1 3 2h8c-2 2-2 3-4 4h-1c-1 1-1 1-2 1l-1 1h-1l-1 2 2 2c1 0 1 1 3 2h1 0l2 2c-1 0-1 1-2 2-2-1-4-4-6-5l-11-11z" class="N"></path><path d="M228 211h2c0 1 0 1 1 2l1-1 5 5c1 1 2 1 3 2h8c-2 2-2 3-4 4h-1c-1 1-1 1-2 1l-1 1h-1c1-1 0-1 1-2h0 0l-2 1h-1l1-1c-1-1-2 0-3 0l1-2h0-2c0-1 1-1 1-2-1 1-2 2-3 2h-1l1-1c1-1 1-2 0-3-1 1-1 2-2 2h-1c1-1 2-1 2-2h-2 0c-1-1 0-1 0-2-1 0-2 0-3 1v-1c1-1 1-2 2-3v-1z" class="W"></path><path d="M266 199h3c1 0 1 0 2-1 1 2 2 1 4 2l-12 12c-2 2-5 5-6 8-1 2-1 4-1 7l1 5h-1-1c0-1 0-2-1-3l-3 3c-1 1-3 3-4 3h-3c1-1 1-2 2-2l-2-2h0-1c-2-1-2-2-3-2l-2-2 1-2h1l1-1c1 0 1 0 2-1h1c2-1 2-2 4-4h1l-1-1c1-1 1-1 1-2 1-1 3-2 4-4l2-2c1-2 4-5 6-6l1 1h3v-1c-1 0-1 0-1-1h1l1-1h-2v-1c1 0 2 0 3-1l-4-1h2 1z" class="G"></path><path d="M251 231c1-1 1-2 1-3h0l1-2 1 1v2l-3 3v-1z" class="V"></path><path d="M247 232c2-2 2-1 4-1v1c-1 1-3 3-4 3h-3c1-1 1-2 2-2l1-1z" class="N"></path><path d="M247 232c0 1-1 2 0 3h-3c1-1 1-2 2-2l1-1z" class="V"></path><path d="M255 210c1-2 4-5 6-6l1 1-1 1 1 2c-1 1-2 1-4 1v1h1l-2 2c0-1-1-2-2-2z" class="C"></path><path d="M255 210c1 0 2 1 2 2h-1l1 1c-1 1-1 1-1 2-1 1-2 2-3 4 0 0-1 1-1 2v2 1c-1-1-1 0-1-1l-2 2v1l-1 1v-1c-2 0-3 1-4 2l-2-1c-1-1-1-2-2-2l1-1c1 0 1 0 2-1h1c2-1 2-2 4-4h1l-1-1c1-1 1-1 1-2 1-1 3-2 4-4l2-2z" class="H"></path><defs><linearGradient id="t" x1="247.338" y1="227.539" x2="244.366" y2="189.88" xlink:href="#B"><stop offset="0" stop-color="#b4b3b3"></stop><stop offset="1" stop-color="#f3f2f3"></stop></linearGradient></defs><path fill="url(#t)" d="M210 206c-1 0-12-12-14-13h67 17c3 0 7-1 10 0 2 2 3 4 4 7l1 5c-6 4-17 9-20 16l-1-1v-1c-2 1-2 2-3 4h-3c0-1 0-2-1-2-1 1-2 2-2 3h-1c0 1-1 3-1 3l-6 6v-1l-1-5c0-3 0-5 1-7 1-3 4-6 6-8l12-12c-2-1-3 0-4-2-1 1-1 1-2 1h-3-1-2l-1 1-2 1c0-1 0-1-1-1v-1l-1-1v-1h1c-2-2-5 0-7-1h-2-2-1l-1 1h-1l-1 1c-1-1-2-1-3-2h-5v1h-1 0c-2-1-5-1-7-1l-9 1-15-1c8 8 16 15 23 23h-1c-1 0-2-1-3-2-1 0 0 0-1-1-4-3-7-9-12-10z"></path><path d="M283 198c2-1 2-2 4-3v3c-1 3-5 6-8 7l1-1v-1c1-2 1-2 1-4l2-1z" class="I"></path><path d="M266 199s1-1 2-1l-1-1-1 1-1-1c3-1 10-1 13-1v1l-3 3c-2-1-3 0-4-2-1 1-1 1-2 1h-3z" class="Z"></path><path d="M256 227l2-1c1-1 1-2 1-3 3-6 8-11 13-16l4-4c2-1 3-3 5-4 0 2 0 2-1 4v1l-1 1-10 8c-4 3-7 8-9 13l1 1h0l1-1 1-1 1-1c0 1-1 3-1 3l-6 6v-1l-1-5z" class="F"></path><path d="M275 200l3-3c2 1 3 1 5 1l-2 1c-2 1-3 3-5 4l-4 4c-5 5-10 10-13 16 0 1 0 2-1 3l-2 1c0-3 0-5 1-7 1-3 4-6 6-8l12-12z" class="E"></path><path d="M267 221c1-5 7-6 10-9 4-3 7-6 10-9 2-3 4-3 7-3l1 5c-6 4-17 9-20 16l-1-1v-1c-2 1-2 2-3 4h-3c0-1 0-2-1-2z" class="Y"></path><path d="M574 208l14-31h3c3 1 7 1 10 1l16-1h55 203l-32 36c2 0 3 0 5 1h1l7 3c1 1 3 2 4 3 0 2 3 4 5 6h-1c1 1 2 3 3 4-1 1 0 1-1 2-2-2-3-3-5-4 0 1 0 2 1 4 1 0 1 1 1 2 1 1 2 2 2 4v3l-2-1-3 4c0-3 0-5-1-8h-1v-1c-2-2-5-5-8-6-3 0-5 1-8 0h-2c1 1 3 2 4 4-1 0-2-2-4-2 1 0 1 1 2 2-2 0-2-1-3-2-1 0 0 1-1 0h-1c1 2 3 3 4 5v2l-1 1c0 2 0 4-2 6-2 1-4 1-6 1-1 0-2-1-3-1l-2-1v-1c-2 0-2 1-3 2-3 3-5 5-8 7l-1 1-2-2h-1-5c-1 2-2 2-5 2-1 1-3 0-4 0s-2-1-3-2c0-1-1-1-1-2l-1-1-2-3h0l-2-3c-1-2-2-2-4-3l-4-4-1-1c0 1-1 1-1 2 0 3-1 4-2 6l-2 2h1l-5 4v3h-1c0 1-1 2-2 3v1c-1 1-1 2-2 3h-1l-2-3c1-3 1-6 1-9v-15c-1-1-3-3-5-4l-32-31c-3-2-5-4-8-6-4-3-25-2-30-1s-10 2-14 2-6-5-10-7c-7-4-28-1-37 1s-18 7-22 15c-3 4-4 8-3 13v3l-1 1h-1l-1-1h-26l-7 7h0c2-5 5-10 7-15z" class="a"></path><path d="M748 213c2 1 5 4 7 4-1-1-1-1-1-2h2l3 3 4 3h1c0-1 0-2 1-3v1l1 2c-1 1-1 4-1 5l-1 1-1-1c-1-2-1-2-2-3s-1-2-1-3c-1-1-3-3-4-3l-1 2h0c-3-2-6-3-7-6z" class="S"></path><path d="M793 238l2-2c6 5 13 10 18 15h-1-5c0-2-2-3-3-5 0-1-1-2-2-3-1 0-1 0-2-1v-1c-2-2-3-1-5-2-1 0-1-1-2-1z" class="J"></path><path d="M833 232c2 1 6 4 7 6v1c0 2 0 4-2 6-2 1-4 1-6 1-1 0-2-1-3-1l-2-1v-1c3 0 6 0 9-1 1-1 1-1 1-3 1-3-2-5-4-7z" class="E"></path><path d="M742 205c3 0 6 0 8 2 1 1 2 3 3 4 2 2 5 4 6 7l-3-3h-2c0 1 0 1 1 2-2 0-5-3-7-4l-4-4c-1-2-2-3-2-4z" class="G"></path><defs><linearGradient id="u" x1="822.961" y1="223.661" x2="827.149" y2="231.546" xlink:href="#B"><stop offset="0" stop-color="#838182"></stop><stop offset="1" stop-color="#999a98"></stop></linearGradient></defs><path fill="url(#u)" d="M822 223l1 1c3-1 5-2 8-2l-7 4 6 1c2 1 6 1 8 3v1l-1-1h-2c0 2 3 3 3 4 1 1 2 2 3 2v2l-1 1v-1c-1-2-5-5-7-6h0c-3-1-7-3-10-4h-7l6-5z"></path><defs><linearGradient id="v" x1="594.407" y1="194.591" x2="564.37" y2="202.569" xlink:href="#B"><stop offset="0" stop-color="#686769"></stop><stop offset="1" stop-color="#838381"></stop></linearGradient></defs><path fill="url(#v)" d="M574 208l14-31h3c-1 4-3 8-5 12l-12 27-7 7h0c2-5 5-10 7-15z"></path><path d="M800 228c2 1 7-3 9-4-4 4-8 9-14 12l-2 2c1 0 1 1 2 1 2 1 3 0 5 2v1c1 1 1 1 2 1 1 1 2 2 2 3 1 2 3 3 3 5-1 2-2 2-5 2-1 1-3 0-4 0s-2-1-3-2c0-1-1-1-1-2l-1-1-2-3h0l-2-3c-1-2-2-2-4-3l-4-4-1-1v-1l1-5 1-1v2l5 5 1 1h3c2-1 3-2 5-4 1-1 2-2 4-3z" class="b"></path><path d="M782 229l5 5v3h-1c0-1-1-2-2-3-1-2-2-3-2-5zm11 13c-2-1-3-3-3-4v-1l2 2 1-1c1 0 1 1 2 1 2 1 3 0 5 2-2 0-4 0-5 1l-1 1-1-1z" class="Y"></path><path d="M793 242l1 1 1-1c1-1 3-1 5-1v1c1 1 1 1 2 1 1 1 2 2 2 3 1 2 3 3 3 5-1 2-2 2-5 2-1 1-3 0-4 0s-2-1-3-2c0-1-1-1-1-2l-1-1-2-3v-2-1h2z" class="C"></path><path d="M793 242l1 1 1-1c1-1 3-1 5-1v1c1 1 1 1 2 1 1 1 2 2 2 3-2-1-5-3-7-3-2 2-2 3-2 5 1 2 2 3 3 5-1 0-2-1-3-2 0-1-1-1-1-2l-1-1-2-3v-2-1h2z" class="K"></path><path d="M843 213c2 0 3 0 5 1h1l7 3c1 1 3 2 4 3 0 2 3 4 5 6h-1c1 1 2 3 3 4-1 1 0 1-1 2-2-2-3-3-5-4 0 1 0 2 1 4 1 0 1 1 1 2 1 1 2 2 2 4v3l-2-1-3 4c0-3 0-5-1-8h-1v-1c-2-2-5-5-8-6-3 0-5 1-8 0h-2c1 1 3 2 4 4-1 0-2-2-4-2 1 0 1 1 2 2-2 0-2-1-3-2-1 0 0 1-1 0h-1c1 2 3 3 4 5-1 0-2-1-3-2 0-1-3-2-3-4h2l1 1v-1c-2-2-6-2-8-3l-6-1 7-4c-3 0-5 1-8 2l-1-1 1-1c3-2 6-4 9-5s5-2 8-2c1 0 2-1 3-2z" class="I"></path><path d="M863 234c1 1 2 2 2 4v3l-2-1-3 4c0-3 0-5-1-8h0c1 1 1 2 2 3l1 1h1v-3-1-2z" class="P"></path><path d="M830 227l-1-1-3-1h4l1 1h5c1-1 2-1 4-1h1c1 0 0 0 2-1h1 0 4c3 1 6 2 8 4 2 1 2 2 3 3l2 2c-1 1-1 2 0 4l1 1-1 1c-1-1-1-2-2-3h0-1v-1c-2-2-5-5-8-6-3 0-5 1-8 0h-2c1 1 3 2 4 4-1 0-2-2-4-2 1 0 1 1 2 2-2 0-2-1-3-2-1 0 0 1-1 0h-1c1 2 3 3 4 5-1 0-2-1-3-2 0-1-3-2-3-4h2l1 1v-1c-2-2-6-2-8-3z" class="W"></path><path d="M843 213c2 0 3 0 5 1h1l7 3c1 1 3 2 4 3 0 2 3 4 5 6h-1c1 1 2 3 3 4-1 1 0 1-1 2-2-2-3-3-5-4-3-3-6-4-9-6-1 1-6-1-7-2-2 0-5-1-8 0l-6 2c-3 0-5 1-8 2l-1-1 1-1c3-2 6-4 9-5s5-2 8-2c1 0 2-1 3-2z" class="R"></path><path d="M832 217h7l-12 5h-2-2c3-2 6-4 9-5z" class="T"></path><path d="M849 217l-3-1h0 3 0c4 1 7 4 10 6l5 4c1 1 2 3 3 4-1 1 0 1-1 2-2-2-3-3-5-4-3-3-6-4-9-6 4-1 8 4 11 5h0c-4-4-9-8-14-10z" class="N"></path><path d="M843 213c2 0 3 0 5 1h1l7 3c1 1 3 2 4 3 0 2 3 4 5 6h-1l-5-4c-3-2-6-5-10-6h0-3 0l3 1h-10-7c3-1 5-2 8-2 1 0 2-1 3-2z" class="U"></path><defs><linearGradient id="w" x1="801.916" y1="223.505" x2="794.242" y2="185.231" xlink:href="#B"><stop offset="0" stop-color="#c8c7c6"></stop><stop offset="1" stop-color="#fafafa"></stop></linearGradient></defs><path fill="url(#w)" d="M746 194l1-1c3-1 7 0 11 0h60 16 8l-15 14c-4 5-9 9-13 13-1 1-3 3-5 4s-7 5-9 4c-2 1-3 2-4 3-2 2-3 3-5 4h-3l-1-1-5-5v-2l-1 1-1 5v1c0 1-1 1-1 2 0 3-1 4-2 6l-2 2h1l-5 4v3h-1c0 1-1 2-2 3v1c-1 1-1 2-2 3h-1l-2-3c1-3 1-6 1-9v-15h2l1-4v-3l-1-3-1-2v-1c-1 1-1 2-1 3h-1l-4-3c-1-3-4-5-6-7-1-1-2-3-3-4-2-2-5-2-8-2l1-3c1-3 1-6 3-8z"></path><path d="M765 203l-8-7 12 1c0 1 0 2-1 3h-2v1c1 0 1 0 0 1l-1 1z" class="G"></path><path d="M769 197l1-1-1 3v2h1l1-1v1c-1 1 0 1-1 2 1 0 1 0 2-1 0 2 0 2 1 3s0 1 1 1v1c0 1 0 1 1 2v1h-3l-7-7 1-1c1-1 1-1 0-1v-1h2c1-1 1-2 1-3z" class="S"></path><path d="M814 215c-1 3-5 4-5 7 0-1 0-1 2-1 1 0 0 0 1-1h1c2-1 5-6 8-8 2-2 4-4 6-5h0c-4 5-9 9-13 13-1 1-3 3-5 4s-7 5-9 4l14-13z" class="H"></path><path d="M770 196h9 3l1 1v3s-1 1-2 1l-2 1v3h1v1c-1 1-2 1-2 3l-3-3c1 0 1-2 2-3-2 1-2 2-3 3-1 0 0 0-1-1s-1-1-1-3c-1 1-1 1-2 1 1-1 0-1 1-2v-1l-1 1h-1v-2l1-3z" class="C"></path><path d="M824 196c4 1 7 1 10 0l-20 19-14 13c-2 1-3 2-4 3h-2l-1-1h0c0-1 1-2 3-3 1 0 0 0 1-1h1l1-1 1-1c1-1 1 0 1-1l9-7 15-14c1-1 4-3 4-4-1 0-1 0-2-1h0l-2 1-1-2z" class="S"></path><defs><linearGradient id="x" x1="771.866" y1="212.662" x2="761.617" y2="216.65" xlink:href="#B"><stop offset="0" stop-color="#bebdbc"></stop><stop offset="1" stop-color="#e7e6e6"></stop></linearGradient></defs><path fill="url(#x)" d="M760 203c7 4 14 13 16 20 1 1 1 3 2 5-2-1-4-2-4-3-3-3-5-6-8-8l-3-3c-1-2-2-3-3-5s-2-4-3-5c2 0 2 0 3-1z"></path><path d="M746 194l1-1c3-1 7 0 11 0h-5c0 5 4 8 7 10-1 1-1 1-3 1 1 1 2 3 3 5s2 3 3 5l-2-1h-1c-1-1-1-1-2-1s-2-1-3-2c-2-2-5-6-7-7l-1-1h-4c1-3 1-6 3-8z" class="I"></path><path d="M746 194l1-1c3-1 7 0 11 0h-5c0 5 4 8 7 10-1 1-1 1-3 1 1 1 2 3 3 5-1-1-2-3-4-4-1-1-2-1-3-2l-3-9h0-3-1z" class="B"></path><path d="M813 196h4v1l-1 1 1 1 1-1v-1l1-1c1 1 0 2 0 3 1-1 0-2 2-3v1c0 2 0 2-1 4h1l1-1v-3c1-1 1-1 2-1h0l1 2 2-1h0c1 1 1 1 2 1 0 1-3 3-4 4l-15 14-9 7c0 1 0 0-1 1l-1 1-1 1h-1c-1 1 0 1-1 1-2 1-3 2-3 3l-1-2v-1-2l1-1v1l1 1c1-1 0-1 0-3 1 0 1 1 3 2v-1h-1l1-2 1 1v-1l-1-1h2s1 0 1 1v-1c0-1 1-1 1-2h0l1-1 4-4c1 0 1-1 1-1h2v-3c2 0 3-3 5-4v-1h0-2l1-1c1 0 2 0 3 1l1-1c-1 0-1-1-2-1l-1-1c1 0 1-1 1-2h-1v-1l1-1 1 1h0c0-1-2-2-3-3z" class="K"></path><path d="M814 205l1 2c0 1 0 1-1 1-2 2-3 4-5 6-2 1-3 3-4 4h-3l4-4c1 0 1-1 1-1h2v-3c2 0 3-3 5-4v-1z" class="H"></path><path d="M743 202h4l1 1c2 1 5 5 7 7 1 1 2 2 3 2s1 0 2 1h1l2 1 3 3c3 2 5 5 8 8 0 1 2 2 4 3l1 2 2-2-1 5v1c0 1-1 1-1 2 0 3-1 4-2 6l-2 2h1l-5 4-1-1c0-3-1-5 1-8l1-1c1-2 3-4 5-6h1v-1c-1 0-2-1-3-1-1-3-3-5-5-8h0c-2-1-3-2-5-3v-1c-1 1-1 2-1 3h-1l-4-3c-1-3-4-5-6-7-1-1-2-3-3-4-2-2-5-2-8-2l1-3z" class="F"></path><path d="M771 239v1c1 2 1 2 0 4l1 1c1-1 1-2 2-3l1 2h1l-5 4-1-1c0-3-1-5 1-8z" class="D"></path><path d="M777 242c-1 0-2 0-3-1h0c0-4 1-4 3-6 1-1 1-2 3-2h0v1c0 1-1 1-1 2 0 3-1 4-2 6z" class="H"></path><path d="M765 219c2 1 3 2 5 3h0c2 3 4 5 5 8 1 0 2 1 3 1v1h-1c-2 2-4 4-5 6l-1 1c-2 3-1 5-1 8l1 1v3h-1c0 1-1 2-2 3v1c-1 1-1 2-2 3h-1l-2-3c1-3 1-6 1-9v-15h2l1-4v-3l-1-3-1-2z" class="b"></path><path d="M772 226c1 2 2 3 3 6l-4 3-3 6h0c0 3-1 8 2 10h0c0 1-1 2-2 3l-1-2v-3-8-1h0c0-2 1-3 1-4 1-1 3-3 3-4 1-2 1-4 1-6z" class="R"></path><path d="M767 224c2 0 2 1 4 0 0 1 0 1 1 2 0 2 0 4-1 6 0 1-2 3-3 4 0 1-1 2-1 4h0v-4c-1-2-1-4 0-6 1-1 0-1 0-3v-3z" class="U"></path><path d="M767 227c0 2 1 2 0 3-1 2-1 4 0 6v4 1 8 3l1 2v1c-1 1-1 2-2 3h-1l-2-3c1-3 1-6 1-9v-15h2l1-4z" class="c"></path><path d="M766 258l-1-2c-1-1 1-2 1-3 1-5-2-8 1-12v8 3l1 2v1c-1 1-1 2-2 3z" class="T"></path><path d="M774 206c1-1 1-2 3-3-1 1-1 3-2 3l3 3 4 4 1 1c1-1 1 0 1-1 0-3 0-4 2-5h1l-1 1 1 1c1-1 1-1 3-1h1v1h1v1l1 1 1 1-1 1h2c-1 1-3 2-4 3v1c1 0 1 0 1-1h3l1 1-1 1 2 2 1 1v1l-1-1-1 2h1v1c-2-1-2-2-3-2 0 2 1 2 0 3l-1-1v-1l-1 1v2 1l1 2h0l1 1h2c-2 2-3 3-5 4h-3l-1-1-5-5v-2c-1-4-3-8-5-12-1 0-4-4-5-5h3v-1c-1-1-1-1-1-2v-1z" class="C"></path><path d="M790 209h1v1h1v1c-1 1-1 2-1 2h-1c-1-1-1-1-2-1 1-1 1-2 2-3h0z" class="E"></path><path d="M774 206c1-1 1-2 3-3-1 1-1 3-2 3l3 3 4 4 1 1v2h1v-1l1 1v1l3 3-2 2-1-2-1-1h-1-2v-1c-1-2-3-3-4-3s-4-4-5-5h3v-1c-1-1-1-1-1-2v-1z" class="G"></path><path d="M777 215c1 0 3 1 4 3v1h2 1l1 1 1 2c1 0 1 1 1 2h1c1-1 2-1 3-1v1h-1 0-1c0 1-1 1-1 2h0 0c1 1 1 2 2 3 1 0 1 0 2-1l1 2h0l1 1h2c-2 2-3 3-5 4h-3l-1-1-5-5v-2c-1-4-3-8-5-12z" class="S"></path><path d="M788 226h0c1 1 1 2 2 3 1 0 1 0 2-1l1 2h0l1 1h2c-2 2-3 3-5 4h-3c1-2 3-3 3-4l-2-2h-1v-2h-1l1-1z" class="Z"></path><path d="M782 196c3 0 8 1 11 0 1 1 1 1 1 2h1v-1c1-1 0-1 1-1h2 5v1l1 1h1v-1l1-1h1 3 3c1 1 3 2 3 3h0l-1-1-1 1v1h1c0 1 0 2-1 2l1 1c1 0 1 1 2 1l-1 1c-1-1-2-1-3-1l-1 1h2 0v1c-2 1-3 4-5 4v3h-2s0 1-1 1l-4 4-1 1h0c0 1-1 1-1 2v1c0-1-1-1-1-1h-2l-2-2 1-1-1-1h-3c0 1 0 1-1 1v-1c1-1 3-2 4-3h-2l1-1-1-1-1-1v-1h-1v-1h-1c-2 0-2 0-3 1l-1-1 1-1h-1c-2 1-2 2-2 5 0 1 0 0-1 1l-1-1-4-4c0-2 1-2 2-3v-1h-1v-3l2-1c1 0 2-1 2-1v-3l-1-1z" class="L"></path><path d="M793 212c1-1 0-3 2-4 1 0 1 1 2 2 0 1 0 1-1 2v2h-1 0-2l1-1-1-1z" class="E"></path><path d="M792 217l2-1h1 2 2c1 1 3 0 4 0 0-1 1-1 2-1l1-1-4 4-1 1h0c0 1-1 1-1 2v1c0-1-1-1-1-1h-2l-2-2 1-1-1-1h-3z" class="W"></path><path d="M631 428l2 3-3 2c-2 2-4 4-6 7-3 2-6 8-7 11l-2 2c-1 0-1 0-1 2 0 1-2 2-2 4-1 2-2 5-2 7 0 1 0 1 1 2v1 2l1 1h0c0 2 0 3 1 5l1 6c2 5 3 7 2 13 0 1-1 2-1 4 1-1 2-3 2-4 2 2 0 6 2 9h-1c0 2-1 3 0 5l3 3c-4 1-8 3-11 5v1c-1 1-2 2-4 3h1l1-1h2c1 0 1 0 2-1 4-1 8-1 12-1 1-1 1-1 2-1h1c-1 0-1-1-2-1v-1c0-1 1-1 3-2l1 1c1 0 3 0 4 1 0 1 1 1 1 2 1 1 1 1 1 2h1c2 1 4 2 7 2v2l3 1 3 3 1 3c3 2 5 3 8 3 1 0 1 1 2 1h1 1 2v1 2 2l2 2h-1l-1-1h-1v1h1v1l1 1h0l-3-3c-1 1-2 2-2 3l-2-3-1 1v1l-1 1v3c-2 6-2 10-6 14v2l-2 1h-9v1c-2 0-5 0-7 1h-1-1l-1-1c0-2 1-6 2-8 1-5 1-10 0-15-2 0-2-2-4-2l-1 2c0 1-1 1-1 2-1 2-2 5-3 6l-2-2-1 1c-1 2-1 4-3 5h-1c0 2 0 2-1 3v2h-1l-1 3-3 6 1 1-1 1s0 1-1 2h-1c-1 1-1 2-1 3h-1v2c-1 1-2 3-2 4v2c-1 3-3 5-5 7-6 4-12 8-17 12l-2 2 4 2-1 1h-2v4c0 2-2 7-2 9 0 0 1 1 1 2 0 0-2 2-2 3l-1-1-3 3c6 3 12 10 14 16s2 13-1 18c-2 3-4 5-7 6-5 1-10 0-14-1l-6 4-1-1c-3-2-5-3-7-5-3-1-6-4-8-6v-1c-1-2-4-5-5-6l-2-1h-4c-1 1-1 1-2 1h-1c2-1 6-3 6-5h-1v-1-2s0-1-1-1h-1v-1l1-1 2-2c-1-4-2-8-3-11-1-8-2-17-1-24 1-4 2-7 3-10 2-9 4-16 8-24 2-4 6-9 7-13l-1-1c-1 0-2 0-3 1-2-1-2-2-3-3h1v-1l-1-1c0 1-1 2-1 2l-1 1 2-6 4-6 26-62c3-9 7-17 9-26 1-1 2-1 3-2 2-2 4-4 7-5v2c0 1 0 3 1 4 1 0 1 0 2-1-1-2 0-3 0-5h0l-1-1c0-1 1-2 1-3 0 3 0 3 2 5 3 2 5 5 7 7l3 3-1-4-1-1c-1-2-1-4 0-6l1-5 1-1h1v-1l2-2h1c0 1-1 4-1 5v-1c1-1 3-4 5-4h1c-2 1-3 2-4 4 5-4 10-8 15-11 2-1 4-2 5-3z" class="d"></path><path d="M656 544c-1-2-1-4 0-6h1l1 3-1 1v1l-1 1h0z" class="N"></path><path d="M579 604l4 2-1 1h-2l-1 4-1-3-1-1 1-1c0-1 0-1 1-2z" class="K"></path><path d="M548 582h2c-1 4-2 7-2 10 0-1 0-3-1-4 0-2 1-4 1-6z" class="G"></path><path d="M657 538h0c1-1 1-1 1-2h1l4 3c0 1-1 1-1 2-1 1-2 2-2 3l-2-3-1-3z" class="E"></path><path d="M547 588c1 1 1 3 1 4 0 6 0 13 4 18l-2 1c-4-8-4-15-3-23z" class="a"></path><path d="M579 611l1-4v4c0 2-2 7-2 9 0 0 1 1 1 2 0 0-2 2-2 3l-1-1-3 3c-1 0-2 1-3 2h0c5-6 7-10 9-18z" class="G"></path><path d="M550 611l2-1 1 1c4 5 11 5 17 5-2 0-8 2-10 1-4-2-7-2-10-6z" class="N"></path><path d="M656 544h0v3c-2 6-2 10-6 14v2l-2 1c-2-2-5-2-7-3h0l2-2 1 1h3c4-2 6-7 7-10 1-1 1-2 1-3v-2l1-1z" class="B"></path><path d="M628 514l1 1c1 0 3 0 4 1 0 1 1 1 1 2 1 1 1 1 1 2h1c2 1 4 2 7 2v2c-6-2-12-4-19-5 1-1 1-1 2-1h1c-1 0-1-1-2-1v-1c0-1 1-1 3-2z" class="G"></path><path d="M629 515c1 0 3 0 4 1 0 1 1 1 1 2 1 1 1 1 1 2h-1c-3-1-4-3-5-5z" class="U"></path><path d="M552 636h1c6-1 8-5 12-10 1-2 3-4 5-6 1-1 1-2 1-3l1-1 1-1v1c-3 9-12 14-15 23-2 0-4-1-6-3z" class="f"></path><path d="M556 563c0-2 0-3 1-4 1-2 2-3 5-3l-1 1v3c0 1-1 0-1 2v1c-2 2-4 4-5 7-2 4-4 8-5 12h-2l2-8 1-2c1-3 3-7 5-9z" class="c"></path><path d="M556 563c0-2 0-3 1-4 1-2 2-3 5-3l-1 1c-2 2-3 4-4 7-1 2-3 5-4 8h-1c1-3 4-6 4-9z" class="B"></path><path d="M641 530c-2-1-4-3-6-5 5 2 10 4 13 10 2 5 2 12 0 17-1 3-2 5-5 7 2-4 3-9 3-14v-3c1-5-1-8-5-12z" class="N"></path><path d="M561 573v-2c3-8 10-14 15-21 1-2 1-4 3-6l4-4c1 0 1-1 2-1l2-2h1l1-1c1 0 0 0 1-1l2-1 1-1h1 1v-1h2 1c1 0 0 0 1-1 1 0 2 0 3-1-1 1-1 2-2 3h0c-1 1-1 1-2 1v1c-2 1-2 1-3 2v1c-11 8-21 16-29 28-1 2-4 4-4 7h-1z" class="B"></path><path d="M538 626c0-3-1-6-1-10-1-5 0-11 2-16v-1c0-1 1-2 1-4-1 8-3 15 2 22 2 4 5 6 8 9v1h-1 1v1h-1c1 0 2 0 3 1-2 1-4 0-6 0l-1-1-2 1c1 1 2 3 2 4 1 3 2 5 3 6s1 1 1 2l-1 1s1 2 2 2c1 1 1 1 2 1-1 0-1 0-2 1 4 4 8 7 13 9l-4-8c-2-2-3-6-5-7l-4-3v-1h2c2 2 4 3 6 3 0 6 4 12 8 16l3 2-1 1c-2-1-4-2-7-2l-4-2c-4-2-7-5-9-9-4-6-7-12-10-19z" class="N"></path><path d="M539 624c1 0 2 0 3 1 0 1 0 2 1 4 1 1 2 3 2 4 1 3 2 5 3 6s1 1 1 2l-1 1s1 2 2 2c1 1 1 1 2 1-1 0-1 0-2 1-6-6-8-15-11-22z" class="E"></path><path d="M548 639c-1 0-2 0-3-1-1-2 0-3 0-5 1 3 2 5 3 6z" class="B"></path><path d="M539 624c-1-3-1-6-1-9h0 1v1 1c2 3 6 8 10 9h1v1h-1 1v1h-1c1 0 2 0 3 1-2 1-4 0-6 0l-1-1-2 1c-1-2-1-3-1-4-1-1-2-1-3-1z" class="Q"></path><path d="M602 530l6-1c6-2 14-1 19 2l1 1c4 2 8 6 10 11v1c1 2 0 5 1 8 1 1 1 2 1 3h0c0-1 0-2 1-3l-1-1c0-2 0-2 1-3v-1-3c1-1 1-3 1-4v-1c-1-2-1-3-2-5h0c1-1 1-3 1-4 4 4 6 7 5 12v3c0 5-1 10-3 14h0l-2 2h0c2 1 5 1 7 3h-9v1c-2 0-5 0-7 1h-1-1l-1-1c0-2 1-6 2-8 1-5 1-10 0-15-1-2-2-3-3-4l-3-3-3-2c-5-3-10-2-15-1l-12 6v-1c1-1 1-1 3-2v-1c1 0 1 0 2-1h0c1-1 1-2 2-3h0z" class="B"></path><path d="M602 530l2 1v-1h5v-1h1c4 0 9 0 13 1l1 1c2 0 4 2 5 3-1 1-2 1-4 1l-3-2c-5-3-10-2-15-1l-12 6v-1c1-1 1-1 3-2v-1c1 0 1 0 2-1h0c1-1 1-2 2-3h0z" class="L"></path><path d="M636 547l1 2h1v4l1 3 1 1 1-1v-2c1-2 2-5 2-8v-1c1-2 0-4 2-5h0c0 2 0 4 1 5h0c0 5-1 10-3 14h0l-2 2h0c2 1 5 1 7 3h-9l-1-1v-1c0-1 0-1 1-1 0-1 0-2-1-2-1-1-1-3-1-5s-1-5-1-7z" class="H"></path><defs><linearGradient id="y" x1="633.82" y1="550.401" x2="630.854" y2="546.337" xlink:href="#B"><stop offset="0" stop-color="#737371"></stop><stop offset="1" stop-color="#888787"></stop></linearGradient></defs><path fill="url(#y)" d="M628 538c2 0 3 0 4 2v1c3 1 3 2 4 5v1c0 2 1 5 1 7s0 4 1 5c1 0 1 1 1 2-1 0-1 0-1 1v1l1 1v1c-2 0-5 0-7 1h-1-1l-1-1c0-2 1-6 2-8 1-5 1-10 0-15-1-2-2-3-3-4z"></path><path d="M634 550v3 6l1 2s0 1-1 1c0 1 2 1 3 2h2v1c-2 0-5 0-7 1h-1c0-1 0-1 1-2 0-1 1-1 1-3l1-11z" class="J"></path><path d="M634 553c1 1 1 2 1 3l1 1h1v-3c0 2 0 4 1 5 1 0 1 1 1 2-1 0-1 0-1 1v1l1 1h-2c-1-1-3-1-3-2 1 0 1-1 1-1l-1-2v-6z" class="D"></path><path d="M628 538c2 0 3 0 4 2v1c3 1 3 2 4 5v1c0 2 1 5 1 7v3h-1l-1-1c0-1 0-2-1-3v-3c0-2 0-5-1-6-1-2-1-2-2-2-1-2-2-3-3-4z" class="F"></path><defs><linearGradient id="z" x1="621.682" y1="506.03" x2="564.415" y2="534.168" xlink:href="#B"><stop offset="0" stop-color="#d6d5d4"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#z)" d="M617 496c2 2 0 6 2 9h-1c0 2-1 3 0 5l3 3c-4 1-8 3-11 5v1c-1 1-2 2-4 3h1l1-1h2c-8 5-19 10-27 16l-11 11-1-1h-1l-1-1-1-2c1-1 1-2 1-3h-1l2-2c2-1 3-3 5-5 2 0 1 1 2 0l3-4 2-1c1 0 2-1 2-2 4-1 7-6 10-7 4-1 8-4 11-6 5-4 9-9 10-14 1-1 2-3 2-4z"></path><path d="M569 546c2 0 4-2 5-4 3-2 5-4 7-5 10-7 19-13 29-19v1c-1 1-2 2-4 3h1l1-1h2c-8 5-19 10-27 16l-11 11-1-1h-1l-1-1z" class="N"></path><defs><linearGradient id="AA" x1="630.325" y1="548.15" x2="548.844" y2="587.062" xlink:href="#B"><stop offset="0" stop-color="#070706"></stop><stop offset="1" stop-color="#292828"></stop></linearGradient></defs><path fill="url(#AA)" d="M595 538l12-6c5-1 10-2 15 1l3 2 3 3c1 1 2 2 3 4-2 0-2-2-4-2l-1 2c0 1-1 1-1 2-1 2-2 5-3 6l-2-2-1 1c-1 2-1 4-3 5h-1c0 2 0 2-1 3v2h-1l-1 3-3 6 1 1-1 1s0 1-1 2h-1c-1 1-1 2-1 3h-1v2c-1 1-2 3-2 4v2c-1 3-3 5-5 7-6 4-12 8-17 12l-2 2c-1 1-1 1-1 2l-1 1c-5 1-9 2-14 1-3-1-6-2-8-6-2-5-1-11 1-16 1-5 3-9 5-13h1c0-3 3-5 4-7 8-12 18-20 29-28z"></path><path d="M561 573h1l-4 11h-1l-1 2c1-5 3-9 5-13z" class="N"></path><path d="M579 562h1c1 1 2 0 4 0h2 2l-16 9-1-1 2-1c1-2 2-4 4-6l2-1z" class="H"></path><path d="M579 562c0 3-2 5-5 6l-1 1c1-2 2-4 4-6l2-1z" class="D"></path><path d="M622 533l3 2 3 3c1 1 2 2 3 4-2 0-2-2-4-2l-1 2v-2l-1-1c-2-1-3-3-6-3l-6-1c-2-1-6 1-8 2-1 1-2 1-4 2 2-3 5-3 7-4 3-2 7-1 9-1h1c2 1 4 1 5 2s2 1 2 2c0-1 0-1-1-2h0c-1-1-1-2-2-3z" class="G"></path><path d="M558 590l1 1 1-2c0 1 0 1-1 2h0l1 1v1c0 1-1 2 0 3 0 2 1 1 1 3h1l1-1 2-2v1h0c-1 1-2 2-2 3 1 3 2 4 4 6h1 1c-2 1-2 1-3 1-1-1-1-1-2-1l-2-2c-2 0-3-1-4-2s-1-3-1-5 0-4 1-7z" class="g"></path><path d="M557 597c1 2 1 3 3 4 0 0 1 0 2-1v2l1 1c0 1 0 1 1 2h1c1 1 2 1 3 1h1c-2 1-2 1-3 1-1-1-1-1-2-1l-2-2c-2 0-3-1-4-2s-1-3-1-5z" class="U"></path><defs><linearGradient id="AB" x1="587.687" y1="581.278" x2="597.96" y2="549.77" xlink:href="#B"><stop offset="0" stop-color="#b8b7b9"></stop><stop offset="1" stop-color="#dbdad8"></stop></linearGradient></defs><path fill="url(#AB)" d="M568 576l24-13c7-4 15-8 23-9 0 2 0 2-1 3v2h-1l-1 3-3 6 1 1-1 1s0 1-1 2h-1c0-1 0-2 1-3 0-1 0-1 1-2 0-1 1-3 1-4l-2 3h-1c1-1 1-2 1-2 1-1 1-1 1-2l-1-1h-1c-2 1-2 2-3 2-3 1-4 3-6 4h0c-3 0-5 1-7 2-6 2-11 5-17 8-3 1-5 2-7 3h0l1-2v-1-1z"></path><path d="M605 537c2-1 6-3 8-2l6 1c3 0 4 2 6 3l1 1v2c0 1-1 1-1 2-1 2-2 5-3 6l-2-2-1 1h-1l-4 1-14 6c-1 1-4 2-5 2-2 1-6 4-7 4h-2-2c-2 0-3 1-4 0h-1l-2 1c-2 2-3 4-4 6l-2 1 1 1-6 3c8-14 21-28 35-35 2-1 3-1 4-2z" class="J"></path><path d="M618 540h8v2c0 1-1 1-1 2h-2c-2-1-2-2-3-3-1 0-1 0-2-1z" class="S"></path><path d="M605 537c2-1 6-3 8-2l6 1c3 0 4 2 6 3l1 1h-8l1-1c-1 0-1-1-2-1-1-1-1-1-2-1-2-2-4 0-7 1-1 0-2 0-3-1z" class="N"></path><path d="M577 563c4-6 9-12 16-17v1l3-1-4 3c0 1-1 2-1 2-2 3-4 6-7 7l-4 4h-1l-2 1z" class="K"></path><path d="M593 547l3-1-4 3c0 1-1 2-1 2-2 3-4 6-7 7l1-1c0-3 6-8 8-10z" class="F"></path><path d="M593 546c1-1 3-2 4-3 2-1 5-2 7-2 1-1 2-1 4-1 0 0 1-1 2-1h1 1c2 0 4 1 5 2h0c0 1 1 1 2 2 0 1 0 1 1 2 0 2 0 2-2 4 0-3 0-5-2-7-2 0-5-1-7 0h-2 0c-3 0-8 2-11 4l-3 1v-1z" class="D"></path><path d="M596 546c3-2 8-4 11-4h0 2c2-1 5 0 7 0 2 2 2 4 2 7h0l-4 1-14 6c-1 1-4 2-5 2-2 1-6 4-7 4h-2-2c-2 0-3 1-4 0l4-4c3-1 5-4 7-7 0 0 1-1 1-2l4-3z" class="B"></path><path d="M591 551c2-1 5-4 7-3-1 1-3 2-3 3v1c-1 1-3 2-5 3-1 1-2 1-4 3l-4 3h3l1-1 1 1h1 0l2-1c0-1 0 0 1-1 1 0 2 0 4-1-2 1-6 4-7 4h-2-2c-2 0-3 1-4 0l4-4c3-1 5-4 7-7z" class="I"></path><path d="M596 546c3-2 8-4 11-4h0 2c2-1 5 0 7 0 2 2 2 4 2 7h0l-4 1c1-2 2-2 2-4-1-1-1-2-2-2-5 0-8 3-12 4-2 0-5 2-7 3 0-1 2-2 3-3-2-1-5 2-7 3 0 0 1-1 1-2l4-3z" class="H"></path><path d="M598 567h0c2-1 3-3 6-4 1 0 1-1 3-2h1l1 1c0 1 0 1-1 2 0 0 0 1-1 2h1l2-3c0 1-1 3-1 4-1 1-1 1-1 2-1 1-1 2-1 3-1 1-1 2-1 3h-1v2c-1 1-2 3-2 4v2c-1 3-3 5-5 7-6 4-12 8-17 12l-2 2c-1 1-1 1-1 2l-1 1c-5 1-9 2-14 1 1 0 2-1 3-1s1 0 3-1h-1-1c-2-2-3-3-4-6 0-1 1-2 2-3h0v-1l-2 2-1 1h-1c0-2-1-1-1-3-1-1 0-2 0-3v-1l-1-1h0c1-1 1-1 1-2l-1 2-1-1c2-6 6-11 10-14v1 1l-1 2h0c2-1 4-2 7-3 6-3 11-6 17-8 2-1 4-2 7-2z" class="E"></path><path d="M580 583c2-2 4-5 7-5v1c-2 2-4 3-7 5v-1z" class="B"></path><defs><linearGradient id="AC" x1="581.215" y1="588.952" x2="584.46" y2="604.378" xlink:href="#B"><stop offset="0" stop-color="#010000"></stop><stop offset="1" stop-color="#2b2c2b"></stop></linearGradient></defs><path fill="url(#AC)" d="M603 583c-1 3-3 5-5 7-6 4-12 8-17 12l-2 2c-1 1-1 1-1 2l-1 1c-5 1-9 2-14 1 1 0 2-1 3-1s1 0 3-1c4-1 7-3 11-6l12-10c3-2 7-5 11-7z"></path><path d="M568 576v1 1l-1 2h0c2-1 4-2 7-3 6-3 11-6 17-8 2-1 4-2 7-2l-6 3c-3 3-8 5-12 7-2 1-3 3-5 4-3 2-7 5-9 8v4c1-1 3-2 4-3 1 0 1-1 2-1l8-6v1c-5 3-10 7-13 11l-1 2c1 0 1 0 2-1 0 1 0 1-1 2h1c1 0 2 1 2 1 0 1 0 2-1 3h1c1 0 1 0 2 1v-1-1h0c0 1 0 1 1 1 2-1 4-2 7-2-4 3-7 5-11 6h-1-1c-2-2-3-3-4-6 0-1 1-2 2-3h0v-1l-2 2-1 1h-1c0-2-1-1-1-3-1-1 0-2 0-3v-1l-1-1h0c1-1 1-1 1-2l-1 2-1-1c2-6 6-11 10-14z" class="I"></path><path d="M568 576v1 1l-1 2h0c2-1 4-2 7-3 6-3 11-6 17-8 2-1 4-2 7-2l-6 3c-6 2-11 4-17 7-3 2-6 3-9 6-1 0-2 2-3 3v3l-1 1c-1 1 0 6 0 7l1 1-1 1h-1c0-2-1-1-1-3-1-1 0-2 0-3v-1l-1-1h0c1-1 1-1 1-2l-1 2-1-1c2-6 6-11 10-14z" class="e"></path><path d="M631 428l2 3-3 2c-2 2-4 4-6 7-3 2-6 8-7 11l-2 2c-1 0-1 0-1 2 0 1-2 2-2 4-1 2-2 5-2 7 0 1 0 1 1 2v1 2l1 1h0c0 2 0 3 1 5l1 6c2 5 3 7 2 13 0 1-1 2-1 4-1 5-5 10-10 14-3 2-7 5-11 6-3 1-6 6-10 7 0 1-1 2-2 2l-2 1-3 4c-1 1 0 0-2 0-2 2-3 4-5 5l-2 2h1c0 1 0 2-1 3l1 2 1 1h1l1 1-12 14c0-2 1-1 1-2v-3l1-1c-3 0-4 1-5 3-1 1-1 2-1 4-2 2-4 6-5 9l-1 2-2 1c-3 4-5 10-6 15l-1 2c0 1 0 3-1 3 0 2-1 3-1 4v1c-2 5-3 11-2 16 0 4 1 7 1 10 3 7 6 13 10 19 2 4 5 7 9 9l4 2c3 0 5 1 7 2l1-1c1 1 2 1 3 1 2 1 1 1 2 0-2-4-6-6-8-10h0c-2-2-3-5-4-7 0-5 5-9 8-12h0c1-1 2-2 3-2 6 3 12 10 14 16s2 13-1 18c-2 3-4 5-7 6-5 1-10 0-14-1l-6 4-1-1c-3-2-5-3-7-5-3-1-6-4-8-6v-1c-1-2-4-5-5-6l-2-1h-4c-1 1-1 1-2 1h-1c2-1 6-3 6-5h-1v-1-2s0-1-1-1h-1v-1l1-1 2-2c-1-4-2-8-3-11-1-8-2-17-1-24 1-4 2-7 3-10 2-9 4-16 8-24 2-4 6-9 7-13l-1-1c-1 0-2 0-3 1-2-1-2-2-3-3h1v-1l-1-1c0 1-1 2-1 2l-1 1 2-6 4-6 26-62c3-9 7-17 9-26 1-1 2-1 3-2 2-2 4-4 7-5v2c0 1 0 3 1 4 1 0 1 0 2-1-1-2 0-3 0-5h0l-1-1c0-1 1-2 1-3 0 3 0 3 2 5 3 2 5 5 7 7l3 3-1-4-1-1c-1-2-1-4 0-6l1-5 1-1h1v-1l2-2h1c0 1-1 4-1 5v-1c1-1 3-4 5-4h1c-2 1-3 2-4 4 5-4 10-8 15-11 2-1 4-2 5-3z" class="Q"></path><path d="M560 549c-1-2-1-2-1-3l1-1 2 3-2 1z" class="E"></path><path d="M596 499c0 1 1 2 1 2-1 2-1 5-3 7v-1c1-2 1-5 2-8z" class="B"></path><path d="M562 548c1 1 2 2 3 2h1c0 1 0 1-1 2-1 0-4-1-5-2v-1l2-1zm37-38h0c0-1 0-2 1-2v-1-3-2c0-1 0-2 1-3h0l1 4s0-1 1-1h0c-1 3-2 6-4 8h0z" class="L"></path><path d="M546 542l1-1c-1 4-3 7-4 11 2 1 3 2 5 3-1 0-2 0-3 1-2-1-2-2-3-3h1v-1l-1-1c0 1-1 2-1 2l-1 1 2-6 4-6z" class="N"></path><path d="M597 467c0 1 3 8 3 8 2 4 0 9 2 13h-1 0c0-1 0-2-1-3-1-3-1-5-2-8h-2 0v-3h1 0v-2-5z" class="B"></path><path d="M597 472l1 5h-2 0v-3h1 0v-2z" class="F"></path><path d="M586 459l3-1 4 4 3 12v3c0-1-1-1-1-2v-1-3l-2-4-1 1c1 3 2 7 1 10-2-7-2-13-7-19z" class="I"></path><path d="M593 446c0-1 1-2 1-3 0 3 0 3 2 5 3 2 5 5 7 7l3 3c0 2 1 5 1 7l2-5c0-1 0-2 1-3h0l-2 10-1 3h0c-2-6-4-13-8-18h0c-2-2-3-4-5-5l-1-1z" class="G"></path><defs><linearGradient id="AD" x1="590.616" y1="462.837" x2="598.384" y2="467.663" xlink:href="#B"><stop offset="0" stop-color="#929191"></stop><stop offset="1" stop-color="#acacaa"></stop></linearGradient></defs><path fill="url(#AD)" d="M584 452c2-2 4-4 7-5v2c0 1 0 3 1 4l5 14v5 2h0-1l-3-12c-1-4-3-7-3-12h0-2c-2 1-3 1-4 2z"></path><path d="M584 452c1-1 2-1 4-2h2 0c0 5 2 8 3 12l-4-4-3 1-5-5c1-1 2-1 3-2z" class="D"></path><path d="M581 649c2 2 2 4 2 6-1 2-2 5-4 6-3 2-9 1-12 0-2-1-2-1-4-3l-1-1s0-1-1-1c3 0 5 1 7 2 3 2 5 2 9 1l2-2c2-1 2-5 2-8z" class="B"></path><path d="M568 544l1 2 1 1h1l1 1-12 14c0-2 1-1 1-2v-3l1-1v-2l-5-3-1 1h0l-1-2h5c1 1 4 2 5 2 1-1 1-1 1-2 1-2-2-4-3-6l4 3h1v-3z" class="V"></path><defs><linearGradient id="AE" x1="591.888" y1="483.823" x2="602.112" y2="493.677" xlink:href="#B"><stop offset="0" stop-color="#b3b2b3"></stop><stop offset="1" stop-color="#d4d3d2"></stop></linearGradient></defs><path fill="url(#AE)" d="M596 477h2c1 3 1 5 2 8 1 1 1 2 1 3h0c0 2-1 4-1 6-1 1-1 1-1 2-1 2-1 4-2 5 0 0-1-1-1-2 0-7 1-15 0-22z"></path><path d="M594 447c2 1 3 3 5 5h0-1l-3-3v4c1 1 2 3 3 5 2 5 4 11 6 17h-1c-1-1-1-2-1-2l-1 1h0l-1 1s-3-7-3-8l-5-14c1 0 1 0 2-1-1-2 0-3 0-5h0z" class="E"></path><path d="M535 638c7 12 16 23 30 28l-6 4-1-1c-3-2-5-3-7-5-3-1-6-4-8-6v-1c-1-2-4-5-5-6l-2-1h-4c-1 1-1 1-2 1h-1c2-1 6-3 6-5h-1v-1-2s0-1-1-1h-1v-1l1-1 2-2z" class="Z"></path><path d="M570 629h0l-1 2 4 4c4 4 6 8 8 14 0 3 0 7-2 8l-2 2c-4 1-6 1-9-1l1-1c1 1 2 1 3 1 2 1 1 1 2 0-2-4-6-6-8-10h0c-2-2-3-5-4-7 0-5 5-9 8-12z" class="J"></path><path d="M568 644h1c1 1 2 2 2 3s-1 1-1 2c1 1 2 1 4 2 1 1 1 2 1 4l-1 1c0-1-2-2-3-3-1-2-2-3-3-4v-5z" class="Y"></path><path d="M570 629h0l-1 2 4 4c4 4 6 8 8 14 0 3 0 7-2 8l-2 2c-1-1-1-1-1-2s1-1 1-2 0-2-1-4l1-1c-1-4-4-9-7-11-2-1-4-1-6-1-2 4 2 6 2 10-2-2-3-5-4-7 0-5 5-9 8-12z" class="a"></path><path d="M538 626h0l-2-5v-4h0-1v2s0 2 1 3h0v2h0c0 1 1 2 1 2v2h0l-1-2c0-1-1-2-1-4v-2h-1v-4c1-1 0-1 1-2h0l1-1v-2-2-1-3c1-1 1-2 1-3l-1-1c0-3 1-6 2-8 2-10 4-18 9-27 0-1 1-1 1-2v-2h1l1-4 1-1v-1-1c1-2-1-3-2-5 0-1 0-1 1-2h0c1 0 1 0 1 1l1 1 1 1c0-1 0-1 1-2l1 1 1 2h0l1-1 5 3v2c-3 0-4 1-5 3-1 1-1 2-1 4-2 2-4 6-5 9l-1 2-2 1c-3 4-5 10-6 15l-1 2c0 1 0 3-1 3 0 2-1 3-1 4v1c-2 5-3 11-2 16 0 4 1 7 1 10z" class="L"></path><path d="M631 428l2 3-3 2c-2 2-4 4-6 7-3 2-6 8-7 11l-2 2c-1 0-1 0-1 2 0 1-2 2-2 4-1 2-2 5-2 7 0 1 0 1 1 2v1 2l1 1h0c0 2 0 3 1 5l1 6c2 5 3 7 2 13 0 1-1 2-1 4-1 5-5 10-10 14-3 2-7 5-11 6-3 1-6 6-10 7 1-1 3-2 5-4 4-3 8-8 10-13h0c2-2 3-5 4-8h0c0-1 0-2 1-3 0-1 1-3 1-4v-2s0-1 1-2h0c-1-1 0-1-1-1l-1-1c0-2 0-3 1-5h0c-2-3 0-8 0-11 1-1 1-1 2-3h0 0l1-3 2-10h0c-1 1-1 2-1 3l-2 5c0-2-1-5-1-7l-1-4-1-1c-1-2-1-4 0-6l1-5 1-1h1v-1l2-2h1c0 1-1 4-1 5v-1c1-1 3-4 5-4h1c-2 1-3 2-4 4 5-4 10-8 15-11 2-1 4-2 5-3z" class="Q"></path><path d="M631 428l2 3-3 2c0-1 0-2-1-3l-3 1c2-1 4-2 5-3z" class="B"></path><path d="M610 438c0 1-1 4-1 5v-1c1-1 3-4 5-4h1c-2 1-3 2-4 4l-1 1h0c-3 6-4 7-3 14v2 2c0-1 0-1 1-2v-2-1c1-1 2-1 2-1h1l-1 2h0c-1 1-1 2-1 3l-2 5c0-2-1-5-1-7l-1-4-1-1c-1-2-1-4 0-6l1-5 1-1h1v-1l2-2h1z" class="C"></path><path d="M610 438c0 1-1 4-1 5v-1c1-1 3-4 5-4h1c-2 1-3 2-4 4l-1 1h0c-2 3-4 5-5 8 0-2 0-4 1-6s2-5 3-7h1z" class="L"></path><path d="M608 467c0 3 0 5 1 7v-2c1 2 1 3 1 5s1 6 0 9c-1 2-1 5-1 8h-1c-1 7-6 15-10 21-1 1-3 3-4 5 5-4 11-8 16-13 1-2 3-6 5-7-1 5-5 10-10 14-3 2-7 5-11 6-3 1-6 6-10 7 1-1 3-2 5-4 4-3 8-8 10-13h0c2-2 3-5 4-8h0c0-1 0-2 1-3 0-1 1-3 1-4v-2s0-1 1-2h0c-1-1 0-1-1-1l-1-1c0-2 0-3 1-5h0c-2-3 0-8 0-11 1-1 1-1 2-3h0 0l1-3z" class="G"></path><path d="M608 467c0 3 0 5 1 7v-2c1 2 1 3 1 5s1 6 0 9l-3-12c0 9 1 20-4 28h0c0-1 0-2 1-3 0-1 1-3 1-4v-2s0-1 1-2h0c-1-1 0-1-1-1l-1-1c0-2 0-3 1-5h0c-2-3 0-8 0-11 1-1 1-1 2-3h0 0l1-3z" class="I"></path><path d="M581 454l5 5c5 6 5 12 7 19 0 14 0 29-10 39-6 6-14 9-21 14-3 2-6 6-10 8-2 1-3 1-5 2l-1 1 26-62c3-9 7-17 9-26z" class="G"></path><path d="M373 427c1-1 2-2 3-2l7 1 4 2s1 0 1-1v-1c1 0 2 0 3 1l8 4h1c2 0 3 2 5 3l2 2c1 1 2 1 3 3l2 2s1 1 1 2l4 4h0c1 2 1 5 1 7 0 3 0 8-1 10v1 2 1l-1 1v2 5 1l2-8c1-4 2-7 4-11s5-7 8-12h1 2v1 2c2 2 5 2 7 5-1 0-3 1-4 2 2 1 1 2 3 3-8 9-9 20-9 32 2 11 5 22 15 30 4 3 9 5 14 8 3 2 6 5 10 7 6 2 9 12 11 17l-4 3c10 14 13 29 17 45 1 10 1 20-2 30 1 2 2 3 3 5h0v1h-1l6 5h1l2 1-2 2c0 1 1 2 1 3l-10-6v1l-2 2c0 2 2 3 3 5l1 1c-1-1-2-1-4-2v1c0 1 0 1-1 1l-3 3c-3 3-7 8-11 11-3 2-6 3-9 5-2-1-2-1-3-2l1-1c1-1 3-2 4-3h1c-3 1-7 2-9 4-1 0-3 1-5 0h-1l-1-1c-3 0-6 0-9-1-3-2-6-6-7-9-1-6 1-12 4-17 2-4 8-12 12-13h1 0 1c-3-2-5-5-7-8v-1l-3-8c0-1 0-2 1-3h4c-1-2-3-3-5-4l-3-3-13-9v-1h-1c-2-2-3-4-4-6l1-1h0l1-1c1-1-2-3-3-4l-8-15c0-1 0-2-1-3l-3-4 1-1h-1c-2-1-3-3-4-5v-1l-3-3c-1-1-1-2-2-3-1 0-1-2-2-2h0v-2l-2-1h0l-2-1c-1 0-2 2-3 3s-3 2-3 4v1c0 1 0 3-1 4h-1v2h0l3 4c0 1 1 2 2 3v1c1 1 2 2 2 3h1c1 1 1 2 0 3l-2 1h-1l-9 2h-5l-5-1c-2 0-3-1-5-1h-1c1-1 0-1 1-1h1v-1c-1-1-3-1-4-2-3-3-3-7-4-10 1-5 3-9 6-12l2-2c-2 0-3-1-3 0-2 1-3 2-3 4-1-1-2-1-2-1-2 1-4 2-5 4v1 1l-1 1v2c-1 1 0-1-1 1-1 1-1 2-1 3v1l-1 1-1 2c0 1 0 2-1 3 0 1 0 1-1 1v-10c1-2 3-6 5-8l1-2-1-1c3-2 4-2 5-5l2-1h-1 0c-5 0-10-1-15-1-2 0-8 1-10 0 1-1 2-1 3-1s2-1 2-2h5c3 0 7 0 11-1l-5-1c-1-1-2-1-3-2l-5-5c-1 0-2 0-4-1h0c1-2 2-4 4-6 1-1 0-1 2-1l1 1s0-1 1-1h2c-3-6-5-12-8-18l-11-28-1-3v-1l2-1v1l1-1-3-7c0-2 0-4 1-6h1l7-5 8-6v-4-1l1-2 2-2c2-1 3-2 5-3l6-1h6l7 1z" class="Q"></path><path d="M369 538l1 1c0 2 0 3-1 5h-1v-3l-1-1 2-2z" class="S"></path><path d="M491 631c1 2 2 3 3 5h0v1h-1-4l2-6z" class="N"></path><path d="M470 551c0-1 1-1 2-2v-3h1l1 2c0 1 0 1-1 3l-1 1c-1-1-1 0-1-1h-1zm2 95h0v-1c1-2 3-4 4-6h1 0 1c0-2 1-2 2-3-1 4-4 7-7 10h-1zM356 530l1 1c-1 1-2 1-3 1h-2c-2-1-6-1-8 0v1h-6c1 0 2-1 2-2h5c3 0 7 0 11-1z" class="L"></path><path d="M476 603h2c-1 3-2 7-4 9h-1c1-1 1-1 1-2l-2 2-1-1c3-2 4-5 5-8z" class="N"></path><path d="M471 661v1c-1 0-3 1-3 2-3 1-7 2-9 4-1 0-3 1-5 0h-1l-1-1c8-1 12-3 19-6z" class="V"></path><path d="M377 533v-1c2-4 9-6 13-7l-3 2c-3 2-5 4-8 7 0-1 1-2 1-3-2 1-1 0-2 1 0 1-1 2-1 2v-1z" class="N"></path><path d="M448 640c2-3 5-6 7-9 0-1 0-1 1-2l2 3c-1 1-1 2-3 3l-1 1v1c-3 0-3 3-6 3z" class="a"></path><path d="M478 642h0c2 0 2-1 3-2h1 0c-1 2-3 3-4 6-2 3-4 8-7 10 0-2 2-3 3-5 0-1 0-1 1-2v-1-1h0c0-2 1-3 3-5z" class="P"></path><path d="M466 640c1 0 2 0 3 1l-3 6-4 6c-1 1-1 1-3 1 4-5 6-8 7-14z" class="N"></path><path d="M486 627c2 4-1 8-3 11l-1 2h0-1c-1 1-1 2-3 2h0c3-4 6-10 8-15z" class="I"></path><path d="M472 646h1c-1 3-4 5-6 6l-2 1-1 1h-1l-1-1 4-6v1c3 0 4 0 6-2z" class="E"></path><path d="M459 654c2 0 2 0 3-1l1 1c-1 0-2 0-3 1 1 1 1 1 2 1h-2v1c-1 1-1 1-2 1l-2 2h-2l-2-4c1-1 2-1 2-2h1v3c1 0 3-1 4-3z" class="X"></path><path d="M471 611l1 1 2-2c0 1 0 1-1 2h1c-1 1-4 3-6 4-5 2-12 1-17-1 8 1 13 1 20-4z" class="G"></path><path d="M458 632l3 4c1 2 1 3 2 5l-3 6v-3h0l1-1v-1c-1-1-1-2-1-3-2-1-3-2-6-3l1-1c2-1 2-2 3-3z" class="Z"></path><path d="M458 632l3 4h-2c-1-1-2-1-4-1 2-1 2-2 3-3z" class="V"></path><path d="M459 550c2-2 4-2 5-3l2 2h0l1-1c1 1 1 2 2 2-1 2-1 2-2 3h-2c0 1-1 1-1 1l-5-4z" class="W"></path><path d="M411 503c3 5 8 8 12 12 2 1 5 3 6 5l-10-5c-3-3-7-7-8-12z" class="S"></path><path d="M469 550l1 1v3c1 1 1 1 2 3l1 1-1 1c-1-1-1-1-3-1l-1 2-4-6s1 0 1-1h2c1-1 1-1 2-3z" class="L"></path><path d="M473 569c2 4 4 8 4 12 2 6 2 13 1 19v3h-2c1-6 1-13 0-19 0-4-2-11-4-14l1-1z" class="a"></path><path d="M470 571v-1c0-2-1-2-1-4l-4-6c4 2 6 6 7 10 2 3 4 10 4 14v4c-2-1-5-12-5-15-1 0-1-1-1-2z" class="E"></path><path d="M400 493c1 1 2 1 2 2l1 1 2 2c1 3 3 5 5 6 0 0 1-1 1-2v1c1 5 5 9 8 12 0 0-1-1-2-1l-1-1c-1 1 0 1-1 1-1-2-3-4-4-5s-2-2-4-3v1h-1c-2-5-5-9-6-14z" class="C"></path><path d="M436 456c2 1 1 2 3 3-8 9-9 20-9 32v-5-1-1-3h0c0-2 0-3 1-5v-1c0-1 0-2 1-3v-1-1h0c0-1 1-2 1-3v-1h1v-1-1l1-2c0-1 1-2 1-2v-1l-1 1c-2 3-4 7-5 11-1 3-1 6-1 9-1 3-1 6-1 9-1 2 0 4 0 6l-1-1c0-3-1-5-1-8h1c1-10 3-22 9-30z" class="I"></path><path d="M377 533v1s1-1 1-2c1-1 0 0 2-1 0 1-1 2-1 3-2 6-2 15 1 22h0v3c-2-2-4-4-5-7-1-5-1-15 2-19z" class="G"></path><path d="M385 521l1 1-12 6-9 3h-8l-1-1-5-1c-1-1-2-1-3-2 2 0 5 1 7 0 8 2 12 1 19-2 4 0 9-2 11-4z" class="S"></path><path d="M475 647v1 1c-1 1-1 1-1 2-1 2-3 3-3 5-2 2-3 2-5 3v1c-4 3-10 3-14 4-1-1-1-1-1-2h2 2l1-1h0 4c2-1 4-1 6-2v-1c-1-1-1-1-3-1l-1 1c-1 1-2 1-3 1l-1 1h-2l2-2c1 0 1 0 2-1v-1h2l6-3h1l6-6z" class="L"></path><path d="M468 653h1 0c0 1 0 1 1 2v1c-1 0-1 0-2 1l-1-1 1-3z" class="d"></path><path d="M466 640v-1c-1-3-2-5-3-7-3-4-14-12-13-18l1 1h0c1 4 9 11 12 14 1 2 3 4 5 5s6 0 8 1c-2 2-3 3-6 5 0 0-1 0-1 1-1-1-2-1-3-1z" class="Z"></path><defs><linearGradient id="AF" x1="394.985" y1="518.603" x2="386.725" y2="516.581" xlink:href="#B"><stop offset="0" stop-color="#3b3a3b"></stop><stop offset="1" stop-color="#5a5858"></stop></linearGradient></defs><path fill="url(#AF)" d="M390 514h2v1c1 0 2 0 2-1 2-2 2-3 4-3-1 1-2 2-1 4l1 1v2c0 1 0 1 1 1l-13 3-1-1c1-1 1-1 1-2h-3c-1 1-1 1-2 1v-2l4-2c2 0 3-1 4-3l1 1z"></path><path d="M416 477v4h1c1 4 0 8 1 12 1 3 2 9 4 12h2c1 2 2 3 2 6h0c2 5 7 9 11 14h0c-3-2-6-4-8-6-2-3-4-7-7-10-4-8-7-15-7-24 0-3 0-6 1-9v1z" class="S"></path><path d="M422 505h2c1 2 2 3 2 6h0c-2-2-3-4-4-6z" class="E"></path><defs><linearGradient id="AG" x1="376.82" y1="543.462" x2="387.412" y2="540.321" xlink:href="#B"><stop offset="0" stop-color="#b1b0b0"></stop><stop offset="1" stop-color="#e3e3e1"></stop></linearGradient></defs><path fill="url(#AG)" d="M387 527c-1 2-1 3-1 6-2 3-5 5-5 9-1 3-1 7 0 10v1 1c0 1-1 1-1 2h0c-3-7-3-16-1-22 3-3 5-5 8-7z"></path><path d="M381 553v-1c-1-3-1-7 0-10 1 2 1 3 0 5v3c1-1 1-3 2-4 1 1 1 2 1 3v1h0 0 1c0-1 0-2 1-3h1 0l2-2c0 1 0 3-1 4h-1v2h0l2 5-2 2c1 2 2 4 2 6h-5l1-1v-1c0-1 0-4-1-5-1 0-1 0-2-1 0-1 0-1-1-2v-1z" class="H"></path><path d="M381 553v-1c-1-3-1-7 0-10 1 2 1 3 0 5v3c1-1 1-3 2-4 1 1 1 2 1 3v1h0 0 1v1 3c1 2 2 4 2 5-1 0-1 0-1-1l-3-8c-1 1-1 2-2 3z" class="C"></path><path d="M448 640c3 0 3-3 6-3l-3 3-3 6v5 2l1 1h0c1 1 2 2 3 2l2 4h-2c-3 1-5 0-6-1-2-2-3-3-3-5 0-5 2-11 5-14z" class="G"></path><path d="M446 659v-1l1-1c-1-4 0-7 1-11v5 2l1 1h0c1 1 2 2 3 2l2 4h-2c-3 1-5 0-6-1z" class="e"></path><path d="M449 654h0c1 1 2 2 3 2l2 4h-2c0-1 0 0-1-1h-3l1-1c0-1-1-1-2-2l2-2z" class="J"></path><path d="M384 550c0-2 0-5 1-7l2-2c1-2 2-3 3-5 1-1 3-3 5-3 2-2 5-3 8-4 5-1 10-2 14-1h1c1 0 2 1 3 1v1l-2-1c-4 0-9-1-13 0h-1c-1 1-1 1-2 1 0 1-1 1-2 1v1h2 3l-5 2-4 4-2-1c-1 0-2 2-3 3s-3 2-3 4v1l-2 2h0-1c-1 1-1 2-1 3h-1z" class="W"></path><path d="M395 537v-1c2-2 3-2 6-2l-4 4-2-1z" class="H"></path><path d="M368 541v3c-1 1-1 4-1 7s2 6 5 8l1 1c3 0 5 1 8-1h-1v-3c0-1 1-1 1-2 1 1 1 1 1 2 1 1 1 1 2 1 1 1 1 4 1 5v1l-1 1h-8-2c-3-1-6-2-8-5s-2-9-1-13h0c0-2 1-3 3-5z" class="B"></path><path d="M381 559c1 1 1 1 1 2-1 1-4 1-6 1-1-1-2-1-3-2 3 0 5 1 8-1z" class="E"></path><path d="M454 636c3 1 4 2 6 3 0 1 0 2 1 3v1l-1 1h0v3c-1 2-3 4-4 6 0 1-1 1-1 1h-1c0 1-1 1-2 2-1 0-2-1-3-2h0l-1-1v-2-5l3-6 3-3v-1z" class="J"></path><path d="M451 640v1c0 1 0 2-1 4 1 1 1 1 2 1l-2 1v1c-1 1-1 3-1 4l1 1-1 1h0l-1-1v-2-5l3-6z" class="R"></path><path d="M452 646l3-2h0c0-2 1-1 2-2v1l-1 1 1 1 3-2v1 3c-1 2-3 4-4 6v-1c0-1 1-2 0-4-1-1-2 0-2 0-2-1-2 0-4-1l2-1z" class="Y"></path><path d="M450 653l-1-1c0-1 0-3 1-4v-1c2 1 2 0 4 1 0 0 1-1 2 0 1 2 0 3 0 4v1c0 1-1 1-1 1h-1c0 1-1 1-2 2-1 0-2-1-3-2l1-1z" class="b"></path><path d="M450 653l-1-1c0-1 0-3 1-4v-1c2 1 2 0 4 1-1 1-3 1-3 3v1l-1 1z" class="J"></path><path d="M361 552c1-5 3-9 6-12l1 1c-2 2-3 3-3 5h0c-1 4-1 10 1 13s5 4 8 5h2 8 5c0-2-1-4-2-6l2-2-2-5 3 4c0 1 1 2 2 3v1c1 1 2 2 2 3h1c1 1 1 2 0 3l-2 1h-1l-9 2h-5l-5-1c-2 0-3-1-5-1h-1c1-1 0-1 1-1h1v-1c-1-1-3-1-4-2-3-3-3-7-4-10z" class="N"></path><path d="M369 564l6 1-2 2c-2 0-3-1-5-1h-1c1-1 0-1 1-1h1v-1z" class="B"></path><path d="M375 565c2 0 6 0 8 1 2 0 4 0 7-1 1 0 1 0 2 1h0l-9 2h-5l-5-1 2-2z" class="I"></path><path d="M387 551l3 4c0 1 1 2 2 3v1c1 1 2 2 2 3h1c1 1 1 2 0 3l-2 1h-1 0c-1-1-1-1-2-1-3 1-5 1-7 1l3-1c-3 0-7 0-10-1h8 5c0-2-1-4-2-6l2-2-2-5z" class="K"></path><path d="M387 551l3 4c0 1 1 2 2 3v1c1 1 2 2 2 3h1c1 1 1 2 0 3l-2 1v-1c-1-3-3-6-4-9l-2-5z" class="G"></path><path d="M477 624l-2 1h0v-1c5-2 10-7 11-13 2-6-1-14-3-20h1c4 12 7 23 2 36-2 5-5 11-8 15-2 2-3 3-3 5h0l-6 6h-1l-6 3c-1 0-1 0-2-1 1-1 2-1 3-1h1l1-1 2-1c2-1 5-3 6-6 3-3 6-6 7-10 1-1 1-2 2-3v-2c0-2 1-3 1-4h0c-1 2-1 3-3 4h-1l-1 2v-1-1-3l2-1c1-1 1-2 1-3h-4z" class="S"></path><path d="M477 624c4-3 7-6 10-9 0 2-1 4-1 7v2c-2 3-2 6-4 9v-2c0-2 1-3 1-4h0c-1 2-1 3-3 4h-1l-1 2v-1-1-3l2-1c1-1 1-2 1-3h-4z" class="C"></path><path d="M484 622v-2l2-2c-1 1 0 3 0 4v2l-2-2z" class="B"></path><path d="M484 622l2 2c-2 3-2 6-4 9v-2c0-2 1-3 1-4h0c-1 2-1 3-3 4h-1c2-3 4-5 5-9z" class="E"></path><path d="M489 637h4l6 5h1l2 1-2 2c0 1 1 2 1 3l-10-6v1l-2 2c0 2 2 3 3 5l1 1c-1-1-2-1-4-2v1c0 1 0 1-1 1l-3 3c-3 3-7 8-11 11-3 2-6 3-9 5-2-1-2-1-3-2l1-1c1-1 3-2 4-3h1c0-1 2-2 3-2v-1c8-8 13-14 18-24z" class="Z"></path><path d="M491 642l-1-1c-1-1 0-2 0-3 1 0 2 0 3 1l2 2h1c1 1 1 1 3 1h0 1l2 1-2 2c0 1 1 2 1 3l-10-6z" class="V"></path><path d="M349 514c1 1 2 1 3 2 7 4 16 4 24 3h2c1-1 2-1 3-1v2c1 0 1 0 2-1h3c0 1 0 1-1 2-2 2-7 4-11 4-7 3-11 4-19 2-2 1-5 0-7 0l-5-5c-1 0-2 0-4-1h0c1-2 2-4 4-6 1-1 0-1 2-1l1 1s0-1 1-1h2z" class="Q"></path><path d="M347 520h0v-1h1c2 0 2 1 4 1h1c1 0 0 0 2 1h1l3 1c2 1 3 1 5 1 3 1 5 1 8 2h-5c-4 0-7-1-11-2-3-1-5-2-9-3z" class="X"></path><path d="M343 515c1-1 0-1 2-1l1 1c1 1 2 2 4 3l-2 1h-1v1h0v2c2 3 5 4 8 5-2 1-5 0-7 0l-5-5c-1 0-2 0-4-1h0c1-2 2-4 4-6z" class="Z"></path><path d="M343 515c1-1 0-1 2-1l1 1c1 1 2 2 4 3l-2 1h-1v1h0v2h-1c-2 0-3-1-4-2 0-2 1-3 1-4v-1z" class="f"></path><defs><linearGradient id="AH" x1="377.194" y1="515.751" x2="371.905" y2="526.057" xlink:href="#B"><stop offset="0" stop-color="#6e6d6b"></stop><stop offset="1" stop-color="#828082"></stop></linearGradient></defs><path fill="url(#AH)" d="M349 514c1 1 2 1 3 2 7 4 16 4 24 3h2c1-1 2-1 3-1v2c1 0 1 0 2-1h3c0 1 0 1-1 2-2 2-7 4-11 4h0-2c-3-1-5-1-8-2-2 0-3 0-5-1l-3-1h-1c-2-1-1-1-2-1h-1c-2 0-2-1-4-1l2-1c-2-1-3-2-4-3 0 0 0-1 1-1h2z"></path><path d="M349 514c1 1 2 1 3 2 7 4 16 4 24 3h2v1c-5 2-15 2-20 0-3 0-5-2-8-2h0c-2-1-3-2-4-3 0 0 0-1 1-1h2z" class="C"></path><path d="M430 446h1 2v1 2c2 2 5 2 7 5-1 0-3 1-4 2-6 8-8 20-9 30h-1c0 3 1 5 1 8l1 1c0 2 0 5 1 7l2 8c-2-3-3-6-3-9v1l-1 1c-1 0-1-1-2-1l-1 1-1-1h0l1 3h-2c-2-3-3-9-4-12-1-4 0-8-1-12h-1v-4l2-8c1-4 2-7 4-11s5-7 8-12z" class="B"></path><path d="M417 481v-4h1 1 1 0c0 2 0 6 1 8s-1 9-1 11c-1-2 1-6 0-8h0l-1-1h0c0 1 0 1-1 2 1 2 1 2 0 4-1-4 0-8-1-12z" class="C"></path><path d="M429 457c2-3 3-5 4-8 2 2 5 2 7 5-1 0-3 1-4 2-6 8-8 20-9 30h-1c0-4 0-7 1-11 0-3 2-5 1-8v-2c1-2 2-5 3-8 1-2 2-3 2-4h0c-1 1-2 2-3 4h-1z" class="M"></path><path d="M430 446h1 2v1 2c-1 3-2 5-4 8-2 2-3 8-4 11-1 1-2 3-2 5-1 1-1 3-1 5h-1v-3c-1 1-1 1-2 1 0-1 0-2-1-3v-4c1-4 2-7 4-11s5-7 8-12z" class="d"></path><path d="M422 458c-1 5-3 13-2 17h1 0c-1 1-1 1-2 1 0-1 0-2-1-3v-4c1-4 2-7 4-11z" class="L"></path><path d="M406 532c3-1 6-1 10 0 8 1 17 8 24 13 6 5 12 11 17 17 3 3 5 6 7 10v1l1 3h-1c-1-1-2-3-3-3s-5-2-6-3c-3-1-7-2-9-4l-31-14-9-3v1-1l-3-3c-1-1-1-2-2-3-1 0-1-2-2-2h0v-2l-2-1h0l4-4 5-2z" class="V"></path><path d="M399 541c1 0 2 1 3 1 0-1 1-2 2-3 0 0 1 0 1-1s1-2 2-2c1-1 4 0 6 0-2 2-4 1-6 2 0 1 0 1 1 2l-1 1v-1c-1 1-3 2-3 3l-1 1 1 1-1 1c-1-1-1-2-2-3-1 0-1-2-2-2z" class="G"></path><path d="M413 536h1 2c2 0 4 1 6 2 1 1 1 1 2 1 4 1 7 4 11 6 2 2 5 4 7 6 5 5 9 10 14 15 2 2 3 5 5 7-1 0-5-2-6-3h0l-1-1c-1-1-2-2-3-4h0l-6-6c-2-1-3-3-4-5l-1-1-4-4c-4-3-10-8-16-9-1-1-3-1-5-2v1c-2-1-3-1-5-1l-1 1-1 1c-1-1-1-1-1-2 2-1 4 0 6-2z" class="T"></path><path d="M408 540l1-1 1-1c2 0 3 0 5 1v-1c2 1 4 1 5 2 6 1 12 6 16 9l4 4 1 1c1 2 2 4 4 5l6 6h0c1 2 2 3 3 4l1 1h0c-3-1-7-2-9-4l-31-14-9-3v1-1l-3-3 1-1-1-1 1-1c0-1 2-2 3-3v1l1-1z" class="B"></path><path d="M417 547c3-1 4 0 6 0l1 1v-2l2 2c-3 1-5 0-9-1z" class="C"></path><path d="M417 543l2 1c2 0 4 1 5 2v2l-1-1c-2 0-3-1-6 0v-1l1-1c-1-1-1-1-1-2z" class="I"></path><path d="M408 540l1-1 1-1c2 0 3 0 5 1v-1c2 1 4 1 5 2v1c-1 1-2 0-3 1 1 0 2 1 2 2l-2-1c0 1 0 1 1 2l-1 1-4-1h-1c1 2 2 3 2 5l1 2-9-3v1-1l-3-3 1-1-1-1 1-1c0-1 2-2 3-3v1l1-1z" class="D"></path><path d="M411 543l-1-2h0 2c2 1 3 2 5 2h0c0 1 0 1 1 2l-1 1-4-1-2-2z" class="H"></path><path d="M406 549l1-1c1-2 0-3 1-5l-1-1h2c1 1 1 1 2 1l2 2h-1c1 2 2 3 2 5l1 2-9-3z" class="I"></path><path d="M420 540c6 1 12 6 16 9l4 4 1 1c1 2 2 4 4 5l6 6h0c1 2 2 3 3 4l1 1h0c-3-1-7-2-9-4v-1l-1-1c-1 0-2-1-3-2h1l4 1-14-11c-2 0-5-2-7-4l-2-2c-1-1-3-2-5-2 0-1-1-2-2-2 1-1 2 0 3-1v-1z" class="X"></path><path d="M419 544c0-1-1-2-2-2 1-1 2 0 3-1 2 1 3 1 4 2h0c2 0 2 1 3 2l10 8h0c-1 0-2-1-4-1s-5-2-7-4l-2-2c-1-1-3-2-5-2z" class="F"></path><path d="M406 549l9 3 31 14c2 2 6 3 9 4 1 1 5 3 6 3s2 2 3 3h1l-1-3v-1c4 5 7 19 7 26-1 3-2 6-5 8-4 3-7 3-12 2l-5-1c-1-2-3-3-5-4l-3-3-13-9v-1h-1c-2-2-3-4-4-6l1-1h0l1-1c1-1-2-3-3-4l-8-15c0-1 0-2-1-3l-3-4 1-1h-1c-2-1-3-3-4-5v-1z" class="W"></path><path d="M428 591v-1h-1c-2-2-3-4-4-6l1-1c2 2 18 14 18 15h0c2 3 5 4 7 6h-2c-2 0-3-4-6-4l-13-9z" class="G"></path><path d="M444 574l3 1c1 0 1 0 2 1l7 3c1 1 2 1 3 2s2 3 3 5v1c0 1 0 1 1 2 0 1-1 3 1 5h-1l-2-1c-2-1-3-3-5-4l-5-5h1c2 0 5 3 7 4 0-1 0 0-1-1v-2c-2-1-2-2-4-3s-4-3-6-4h0l6 3 1-1-8-4c-1-1-2-1-3-2z" class="C"></path><defs><linearGradient id="AI" x1="441.066" y1="559.97" x2="423.434" y2="570.03" xlink:href="#B"><stop offset="0" stop-color="#b4b1b3"></stop><stop offset="1" stop-color="#dcdedb"></stop></linearGradient></defs><path fill="url(#AI)" d="M416 556c10 2 19 7 28 12 5 2 9 5 14 8v2c1 1 2 1 2 2 0 0-1 0-1 1-1-1-2-1-3-2l-7-3c-1-1-1-1-2-1l-3-1-3-2-2-1c-2 0-3-1-4-1v-1h-2l-7-3v-1l-2-1-2-2c-1 0-1-1-2-1 0-1-1-1-2-1l-2-2v-2z"></path><defs><linearGradient id="AJ" x1="424.225" y1="541.561" x2="460.582" y2="607.556" xlink:href="#B"><stop offset="0" stop-color="#0d0d0c"></stop><stop offset="1" stop-color="#2d2c2c"></stop></linearGradient></defs><path fill="url(#AJ)" d="M406 549l9 3 31 14c2 2 6 3 9 4 1 1 5 3 6 3s2 2 3 3h1l-1-3v-1c4 5 7 19 7 26-1 3-2 6-5 8-4 3-7 3-12 2l-5-1c-1-2-3-3-5-4l-3-3c3 0 4 4 6 4h2c-2-2-5-3-7-6h0c2 1 4 2 5 4 3 1 6 4 9 3v-1h1 1l-1-1 1-1c0 1 1 1 2 1l1-1c0-2 1-4 2-6l-2-3 2 1h1c-2-2-1-4-1-5-1-1-1-1-1-2v-1c-1-2-2-4-3-5 0-1 1-1 1-1 0-1-1-1-2-2v-2c-5-3-9-6-14-8-9-5-18-10-28-12-1-1-4-1-5-1h-1c-2-1-3-3-4-5v-1z"></path><path d="M373 427c1-1 2-2 3-2l7 1 4 2s1 0 1-1v-1c1 0 2 0 3 1l8 4h1c2 0 3 2 5 3l2 2c1 1 2 1 3 3l2 2s1 1 1 2l4 4h0c1 2 1 5 1 7 0 3 0 8-1 10v1 2 1l-1 1v2 5c-1 3-1 6-1 9 0 9 3 16 7 24l-3-2c-1-1-5-9-5-11v-1c-2-1-3-2-3-4l-1-3c-1 0-1 1-2 1v4c1 1 1 3 1 4 1 1 1 2 1 2 0 1 1 2 1 2v1c0 1-1 2-1 2-2-1-4-3-5-6l-2-2-1-1c0-1-1-1-2-2 1 5 4 9 6 14h1 0c0 3-1 4-2 6 6 3 11 6 16 10 10 5 20 11 29 18l9 9 5 4 4 6c2 3 4 6 5 9l-1 1c-1-4-3-8-7-10l4 6c0 2 1 2 1 4v1c-3-9-9-15-15-21l-6-6c-6-6-14-10-22-15-5-3-11-7-17-9-4-1-7-1-11-1-1 0-1 0-1-1v-2l-1-1c-1-2 0-3 1-4-2 0-2 1-4 3 0 1-1 1-2 1v-1h-2l-1-1c-1 2-2 3-4 3l-4 2c-1 0-2 0-3 1h-2c-8 1-17 1-24-3-1-1-2-1-3-2-3-6-5-12-8-18l-11-28-1-3v-1l2-1v1l1-1-3-7c0-2 0-4 1-6h1l7-5 8-6v-4-1l1-2 2-2c2-1 3-2 5-3l6-1h6l7 1z" class="V"></path><path d="M385 480c5 3 9 5 11 10l-8-3c1 0 2 0 3-1-1-1-2-2-3-2h-1 2v-1c-2-1-3-1-3-3h-1z" class="H"></path><path d="M400 448c4 1 4 4 7 6l2 5-1 1-2-2c0 1 1 2 1 4 1 2 0 5 0 7 0-3-1-6-1-9-1-5-4-9-6-12z" class="I"></path><path d="M378 449c5 1 15 4 20 8h0c1 3 2 4 2 7l-11-8h0c0-1-3-3-4-4h-1l-6-2v-1z" class="D"></path><path d="M356 430l3-1c5 0 11 1 16 2 1 1 3 1 5 2s4 3 6 4c6 4 10 11 14 17-2 0-4-1-5-2v-1c-1-3-6-8-9-10-2-1-3-2-5-4-2 0-4-1-5-2l-1-1h-3c-3-2-6-2-9-2h-1l-2-2h-4z" class="U"></path><path d="M381 487h0c4 3 11 4 15 7 1 2 2 6 3 8v2l-1 1c-2-1-3-1-4-3l-1 2c1 1 1 0 2 1v1h-2v1c0 1 0 2-1 3-3-2-5-13-8-16-2-3-3-4-3-7z" class="H"></path><path d="M407 454l2 1v-1-2c1 0 1 1 2 1v1c1 2 1 3 1 5h0-1c-1 2-1 5-1 7v3c-1 6-2 12 0 18v1c-1 0-1 1-2 1v4c1 1 1 3 1 4 1 1 1 2 1 2 0 1 1 2 1 2v1c0 1-1 2-1 2-2-1-4-3-5-6l-2-2-1-1c0-1-1-1-2-2-1-5 3-11 5-15 1-3 1-6 2-9 0-2 1-5 0-7 0-2-1-3-1-4l2 2 1-1-2-5z" class="W"></path><path d="M411 453v1c1 2 1 3 1 5h0-1c-1 2-1 5-1 7 0-1-1-1-1-2h0-1 0c0-1 0-1 1-1 1-1 1-3 0-4v-1c0-1 1-3 2-5z" class="I"></path><path d="M356 430h4l2 2h1c3 0 6 0 9 2h3l1 1c1 1 3 2 5 2 2 2 3 3 5 4 3 2 8 7 9 10v1l-3-1c-12-5-23-8-36-10h-3c0 1 0 1 1 2-1 1-1 1-2 1-2-2-3-4-6-5v-4-1l1-2 2-2c1 1 1 2 3 2 1-1 2-2 4-2z" class="P"></path><path d="M372 434h3l1 1c1 1 3 2 5 2 2 2 3 3 5 4 3 2 8 7 9 10v1l-3-1v-1l-2-2v-1l-1-1c-2-1-1 0-2-1l-2-2c-1 0-1 0-2-1s-2-1-4-1c-1-1-3-2-4-3h-2l-1-1c-1 1-2 1-4 1v1h3c1 0 1 1 2 1s1 0 2 1c-2 0-2-1-3-1h-5c-1-1-2-1-3-1h-1-1c1-1 1-1 0-2h-2-3v-1c1-2 1-2 3-2v1h1 4l1-1c2 1 4 0 6 0z" class="D"></path><path d="M356 430h4l2 2h1c3 0 6 0 9 2-2 0-4 1-6 0l-1 1h-4-1v-1c-2 0-2 0-3 2v1h3 2c1 1 1 1 0 2-1 0-3 0-4-1l-1 1 1 1-2 1h-3c0 1 0 1 1 2-1 1-1 1-2 1-2-2-3-4-6-5v-4-1l1-2 2-2c1 1 1 2 3 2 1-1 2-2 4-2z" class="T"></path><path d="M349 430c1 1 1 2 3 2-2 1-3 4-5 4-1-1-1-1-1-2l1-2 2-2z" class="N"></path><path d="M355 436v-3c2-2 5-2 7-1h1c3 0 6 0 9 2-2 0-4 1-6 0l-1 1h-4l1-2h-2-1c-1 1-2 1-3 1v1l-1 1z" class="X"></path><path d="M355 436l1-1v-1c1 0 2 0 3-1h1 2l-1 2h-1v-1c-2 0-2 0-3 2v1h3 2c1 1 1 1 0 2-1 0-3 0-4-1l-1 1 1 1-2 1h-3c-1 0-2-1-3-1v-2h-1v-1l1-1h2l1 1c0-1 0-1 1-2v1 1h1v-1z" class="K"></path><path d="M373 427c1-1 2-2 3-2l7 1 4 2s1 0 1-1v-1c1 0 2 0 3 1l8 4h1c2 0 3 2 5 3l2 2c1 1 2 1 3 3l2 2s1 1 1 2l4 4h0c1 2 1 5 1 7 0 3 0 8-1 10v1 2 1l-1 1v2 5c-1 3-1 6-1 9 0 9 3 16 7 24l-3-2c-1-1-5-9-5-11v-1c-2-1-3-2-3-4l-1-3v-1c-2-6-1-12 0-18v-3c0-2 0-5 1-7h1 0c0-2 0-3-1-5v-1c-1 0-1-1-2-1v2 1l-2-1c-3-2-3-5-7-6l-8-11c-6-5-11-8-19-10z" class="L"></path><path d="M410 466c0-2 0-5 1-7h1c0 7 0 14-1 20v-6-1l-1-3v-3z" class="F"></path><path d="M410 469l1 3v1 6 12l-1-3v-1c-2-6-1-12 0-18z" class="D"></path><path d="M392 428l6 6c0 1-1 1 0 2 1 2 1 4 2 6-2-3-5-7-7-8l-1-1 1-1-1-4z" class="P"></path><path d="M416 471c-1-2-1-4-1-6 0-1 0-2-1-3h0c0-1-1-1-1-2v-1h-1c0-1 0-2 1-4h0c-1-1-1-2 0-3h1c0 1 1 2 1 2 1 1 2 1 3 0 0 3 0 8-1 10v1 2 1l-1 1v2z" class="C"></path><defs><linearGradient id="AK" x1="375.225" y1="424.678" x2="384.56" y2="429.801" xlink:href="#B"><stop offset="0" stop-color="#a09f9f"></stop><stop offset="1" stop-color="#c3c2c1"></stop></linearGradient></defs><path fill="url(#AK)" d="M373 427c1-1 2-2 3-2l7 1 4 2s1 0 1-1v-1c1 0 2 0 3 1l1 1 1 4-1 1 1 1h-1l-3-3c-1 0-1 0-2-1h-1l-1 1h0c2 0 3 1 4 2s3 3 3 4c-6-5-11-8-19-10z"></path><path d="M387 428s1 0 1-1v-1c1 0 2 0 3 1l1 1 1 4-1 1c-1-2-4-3-5-5z" class="D"></path><path d="M354 443c4 1 8 3 13 3 3 1 7 2 11 3v1l6 2h1c1 1 4 3 4 4h0l11 8h0v3c0 2-1 4-1 6 0 4-1 7-1 10-1 1-1 1-1 2l-10-7-17-10c1 1 2 3 3 4 3 3 9 5 12 8h1c0 2 1 2 3 3v1h-2 1c1 0 2 1 3 2-1 1-2 1-3 1h-2c-1-1-4-2-6-2l1 2c0 3 1 4 3 7 3 3 5 14 8 16 1-1 1-2 1-3l2 1v1c1 0 2 0 4 1l-1 1h0c-2 0-2 1-4 3 0 1-1 1-2 1v-1h-2l-1-1c-2-5-5-9-7-14-9-19-18-38-30-55 1 0 1 0 2-1z" class="I"></path><path d="M368 464c1 0 2 1 3 1 0-1-1-2-1-3h2 0l4 4c-1 0-2 1-2 1-1 0-2 0-3-1h0-1c-1 0-1-1-2-2z" class="P"></path><path d="M365 454l2 1 5 7h0-2c0 1 1 2 1 3-1 0-2-1-3-1 0-1-1-2-1-3v-1c-1-2-1-4-2-6z" class="M"></path><path d="M367 461c-3-4-6-8-8-12-1-1-2-3-2-4h1l7 9c1 2 1 4 2 6v1z" class="R"></path><path d="M384 494c3 3 5 14 8 16 1-1 1-2 1-3l2 1v1c1 0 2 0 4 1l-1 1h0c-2 0-2 1-4 3 0 1-1 1-2 1v-1h-2l2-2c-2-1-3-5-3-7l-5-11z" class="U"></path><path d="M367 455c1 0 1 0 3 1s5 3 7 4c2 2 5 5 7 6l-1 1h0c-1 0-6-5-8-6v1c1 1 4 3 4 4l-1 1v-1h-2l-4-4-5-7z" class="B"></path><path d="M379 459l-2-2v-1l-1-1h1 2l5 2h1c1 1 2 1 4 1v-2l11 8h0v3c0 2-1 4-1 6 0 4-1 7-1 10-1 1-1 1-1 2l-10-7h4 1 0c0-1-1-2-1-2l-1-2 1-1c-1-2-2-3-3-5v-1h0c-3-3-7-5-10-8h1z" class="P"></path><path d="M379 459c2 0 4 1 6 2 2 0 3 2 4 4 1 1 3 4 3 6h0 0c-2-1-3-3-4-4h0c-3-3-7-5-10-8h1z" class="B"></path><path d="M346 439c3 1 4 3 6 5 12 17 21 36 30 55 2 5 5 9 7 14-1 2-2 3-4 3l-4 2c-1 0-2 0-3 1h-2c-8 1-17 1-24-3-1-1-2-1-3-2-3-6-5-12-8-18l-11-28-1-3v-1l2-1v1l1-1-3-7c0-2 0-4 1-6h1l7-5 8-6z" class="a"></path><defs><linearGradient id="AL" x1="354.772" y1="472.695" x2="336.283" y2="502.218" xlink:href="#B"><stop offset="0" stop-color="#afaeaf"></stop><stop offset="1" stop-color="#d4d3d1"></stop></linearGradient></defs><path fill="url(#AL)" d="M330 450h1l4 9c1 1 2 2 2 3l15 34c2 7 6 13 7 20 0 0-1 1-2 1s-3-1-4-1v-1l-1 1c-1-1-2-1-3-2-3-6-5-12-8-18l-11-28-1-3v-1l2-1v1l1-1-3-7c0-2 0-4 1-6z"></path><defs><linearGradient id="AM" x1="328.825" y1="453.249" x2="334.691" y2="458.765" xlink:href="#B"><stop offset="0" stop-color="#7e7d7d"></stop><stop offset="1" stop-color="#9d9d9d"></stop></linearGradient></defs><path fill="url(#AM)" d="M330 450h1l4 9c1 1 2 2 2 3l-1 1-1-2-1-1v3l1 1v5 1l-3-7-3-7c0-2 0-4 1-6z"></path><defs><linearGradient id="AN" x1="321.759" y1="478.866" x2="358.23" y2="496.629" xlink:href="#B"><stop offset="0" stop-color="#616263"></stop><stop offset="1" stop-color="#b2afad"></stop></linearGradient></defs><path fill="url(#AN)" d="M332 463l3 7c3 10 8 19 12 29 1 4 2 9 4 13 1 1 3 2 4 3h1c1 0 2 1 3 1 0 0-1 1-2 1s-3-1-4-1v-1l-1 1c-1-1-2-1-3-2-3-6-5-12-8-18l-11-28-1-3v-1l2-1v1l1-1z"></path><path d="M603 213c-1-5 0-9 3-13 4-8 13-13 22-15s30-5 37-1c4 2 6 7 10 7s9-1 14-2 26-2 30 1c3 2 5 4 8 6l32 31c0 2-1 3-1 5h0c2 3 1 9 1 12l-1-1-2 2 1 1c0 2 0 3-1 5v2c-1 1-2 1-2 2-2 3-6 6-9 9l-14 13-3 3h-2v-1l-3 3-3 3c-1 2-2 3-4 5-1 2-3 2-4 4 0 2-2 4-4 5l1 1-47 49c-1 2-3 4-5 5h0c-1 2-4 4-6 6-1 2-4 4-5 5l-1 1-2-1c-1 0-2 2-2 3l1 1c1 0 2-1 3-1 2-1 2-1 3 0 2-1 4-2 5-3l25-25c1-1 3-1 4-2v-1h1l3-1h1l-2 3c0 1-1 3-2 4v1l2 1c-1 2-3 4-3 7v3 5 5c1 0 1 0 2 1v-1l1 5c0 1 1 2 2 2h0c1-1 1 0 2-1v1c1 2 2 9 4 11h-1v3c0 1-1 2-1 3h1c1-2 1-3 2-4 1 0 1 0 1 1 1 1 2 3 2 5h1 2l1-1c2 1 2 1 2 3 0 1-1 2-1 2l-1 3h-1l-6 2-18 6c-3 1-7 2-9 1h-1v-1l-1-1h0-1l-23 6c-2 0-4 1-6 2l1 1 1-1h2 1 0l-4 1c-1 1-1 2-1 2-1 1-2 1-3 2l1 1h0c-1 0-1 0-2 2h2 0c-2 2-4 3-7 3-1 1-3 2-4 2l-1 1c-4 3-9 5-11 11h-1l-2 2v1h-1l-1 1-1 5c-1 2-1 4 0 6l1 1 1 4-3-3c-2-2-4-5-7-7-2-2-2-2-2-5 0 1-1 2-1 3-3 0-4 0-6 1l-3 2v-1c5-8 8-16 12-25l25-59 20-46c3-7 6-14 8-21 2-6 2-13 3-19 0-3 0-5-1-8 0-6-2-12-5-17-1-1-2-1-2-2-1 0-1 0-2-1l-2-2-1 1c1 0 1 0 1 1v1l-2-1h-1c-1 0-2 0-2 1v1h1l-1 2c-3-6-7-11-13-15-1-1-3-2-5-3-1-1-2-2-4-2-2-1-5-2-7-2l-2-3c-1-1-1-2-1-4 1-3 0-5-1-8h0l1-1v-3z" class="d"></path><path d="M610 212v-1l-2 2v-1c0-1 1-3 2-4l1-1v-1c1-1 2-1 4-2-2 3-3 6-5 9v-1z" class="E"></path><path d="M610 212v1c1 4 1 6 4 8v1c2 1 5 3 8 3 0-1 0-1 1-1v-1h1v2h0c1-1 1-2 1-3-1-1-1-2-2-2l1-1c1 0 2 1 2 2 0 2 0 4-2 6-1 2-5 2-7 2h-2-1c-2 0-3 0-4-1l-1 1c1 0 1 1 2 2h1c1 1 2 1 3 2l-2 1c-2-1-5-2-7-2l-2-3c-1-1-1-2-1-4 1-3 0-5-1-8h0l1-1v-3c1 3 1 6 3 8v1c2 2 5 5 7 5l1 1h2l3 1c1-1 1-1 1-2h1c0-1 0-1 1-1-2-1-4-1-5-1l-1-1h-1c-3 0-4 0-6-2v-2-4c0-2 0-2 1-4z" class="H"></path><path d="M603 216c2 6 4 10 10 13h2-1c-2 0-3 0-4-1l-1 1c1 0 1 1 2 2h1c1 1 2 1 3 2l-2 1c-2-1-5-2-7-2l-2-3c-1-1-1-2-1-4 1-3 0-5-1-8h0l1-1z" class="Z"></path><path d="M622 200l1-1h0v3c0 1 0 3-1 4-1 2-3 5-3 6 0 2 1 3 1 5 0 1 1 2 1 3l1 2c-3 0-5 0-8-2v1c-3-2-3-4-4-8 2-3 3-6 5-9 1-2 3-3 4-5l3 1z" class="X"></path><path d="M617 207c0 4 0 10 3 13h1l1 2c-3 0-5 0-8-2v-1-6c0-2 2-4 3-6z" class="Y"></path><path d="M619 199l3 1-5 7c-1 2-3 4-3 6v6 1 1c-3-2-3-4-4-8 2-3 3-6 5-9 1-2 3-3 4-5z" class="T"></path><path d="M732 234h0c-1-1 0-1 0-2v-2l1-1v-2h0v-4h0c1-1 0-2 0-3h1v-4c-2-1-3-2-4-3-1 0-2-1-3-2 0-1-1-2-2-3 0 3 1 6 2 9 1 1 1 1 1 2-2-2-3-4-4-6 0-2-1-3-1-4v-1c-1-1-2-2-3-2h-1 0c0-1 0-1-1-1-2-1-3-3-5-4-1-2-2-3-4-4v-1h0 1c-2 0-5 0-7-1 6 0 12-1 17 2l6 6c4 3 8 7 11 10 0 1 0 2-1 2v1l-1 6v2c0 1-1 2-1 2v2c0 1-1 2-1 2 0 2 0 3-1 4h0z" class="H"></path><path d="M681 200c8 0 15 0 21 6s8 15 8 23c0 5-2 10-4 14-2 3-4 6-5 9v-1c2-6 1-12 1-19h0c-2-12-7-21-16-28-2-1-3-1-5-2v-2z" class="a"></path><path d="M737 213l1-1-1-4-4-4c-2-2-5-5-6-8l32 31c0 2-1 3-1 5h0c2 3 1 9 1 12l-1-1-2 2 1 1c0 2 0 3-1 5v2c-1 1-2 1-2 2-2 3-6 6-9 9l-14 13-3 3h-2v-1l-3 3-3 3c-1 2-2 3-4 5-1 2-3 2-4 4h-1c-1 2-3 3-4 5h-1l-1 1-1 1-4 4-1 1c-2 0-3 0-5 2 0 1 0 1-1 2h0l-2-2v-2l2 1v-1l-2-2c-1 1-1 1-2 1v1c-1-1-1-2-1-3s1-2 1-3c1-1 1-2 2-4 1-1 2-2 3-4s3-3 4-4l15-19c1-1 1-2 2-3 0-1 1-1 1-2 1-1 1-1 1-2h1l2-4c1-1 1-2 1-2l3-5 1-2c0-1 0-1 1-1v-2l1-2 3-6h0l2-4h0c1-1 1-2 1-4 0 0 1-1 1-2v-2s1-1 1-2v-2l1-6v-1c1 0 1-1 1-2z" class="Z"></path><path d="M740 229c0-1 1-2 1-3v-1c1-2 1-2 2-3h1l-1 1h1l2 2h1v1c1 1 1 1 3 2h0c0 2 1 3 2 3 1 1 1 2 2 2h-3v-1c-2 0-4 2-6 2v-1h-2v-2l-3-2z" class="Q"></path><path d="M717 279l1 1h-2v1c2 0 5 0 7 1l-3 3c-1 2-2 3-4 5-1 2-3 2-4 4h-1v-6h1c-1-1-2-2-2-4h1v-1-1l1-1v1-1-1l1-1v1l1-1h2 1z" class="E"></path><path d="M717 279l1 1h-2v1c2 0 5 0 7 1l-3 3c-1 2-2 3-4 5-1-1 0-1-1-1v-1-3h-1c1-1 3 0 4 0v-2l-1-1h-1-1c-1 0-2-1-3-1v-1l1-1v1l1-1h2 1z" class="O"></path><path d="M728 260l2 2c1 0 1 0 2-1v1l-1 2c0 1-1 2-1 3l1 1-1 2h-1c0 1 0 2 1 2l-1 1-2 2h0c0 2-1 3-1 4l-3 3c-2-1-5-1-7-1v-1h2l-1-1c-1 0-2-1-2-1v-1h1 1c0-1-1-2 0-3h2c0-1 0-1-1-2l1-1 1 1 1-1-1-1 1-1h1 0c0-1 0-1-1-2h2 0v-2c1 1 1 1 2 1l-1-2c1-1 1-1 1-2l1 1h1v-1l1-2z" class="W"></path><path d="M734 250h3l3-3h1c0 1-1 1-1 2h2c-1 1-1 2-2 2l3 1v-1l1 2v2c-2 0-2 1-3 2l-1 2 1 1v1c-2 2-4 5-6 5-1-1-1-2-1-3v1c0 1 0 1-1 2l1 1s-1 1-1 2l-3 3c-1 0-1-1-1-2h1l1-2-1-1c0-1 1-2 1-3l1-2v-1c-1 1-1 1-2 1l-2-2-1-1c1 0 2-1 3-2l-1-1h0c1 0 2 0 2-1h1c-1-1-1-1-1-2h1l-1-2 1-1h2z" class="L"></path><path d="M734 250h3l3-3h1c0 1-1 1-1 2h2c-1 1-1 2-2 2v1 1c-2 0-1 0-2 1v1l-1 1 1 1v2h-2-1c0-1 0-1-1-1l1-2h0l-1-1h-2c-1-1-1-1-1-2h1l-1-2 1-1h2z" class="Q"></path><path d="M740 229l3 2v2h2v1c2 0 4-2 6-2v1h3c0 1 0 2 1 2v2h-1c-1 0-1 0-2-1v1l1 1h1v1c-1 1-2 1-3 1l-1 1h-1l-1 1 1 1h0 2l2 2c-2 0-3 0-4 1h2c-1 2-2 4-4 5l-1 1-2 1-1-2v1l-3-1c1 0 1-1 2-2h-2c0-1 1-1 1-2h-1l-3 3h-3c0-1-1-1-1-2 1 0 1-1 2 0h0l1-1-2-1 1-1h2l-1-1v-1l2-1c-1-1-1-1-1-2 1 0 2-1 2-1v-1h-1c0-1 0-2 1-3h2l-2-1c0-2 1-3 1-5z" class="W"></path><path d="M746 247c1-2 1-2 1-3l1 1h0l1 1-1 2h-1c-1-1-1 0-1-1z" class="L"></path><path d="M748 242l-1 1-1-1 1-3c1 0 1 0 2-1h1 0l1 2-1 1h-1l-1 1z" class="Q"></path><path d="M755 235l1 1v1l1 2s-1 0-1 1c1 1 1 1 2 3l-2 2 1 1c0 2 0 3-1 5v2c-1 1-2 1-2 2-2 3-6 6-9 9l-14 13-3 3h-2v-1c0-1 1-2 1-4h0l2-2 1-1 3-3c0-1 1-2 1-2l-1-1c1-1 1-1 1-2v-1c0 1 0 2 1 3 2 0 4-3 6-5v-1l-1-1 1-2c1-1 1-2 3-2v-2l2-1 1-1c2-1 3-3 4-5h-2c1-1 2-1 4-1l-2-2h-2 0l-1-1 1-1h1l1-1c1 0 2 0 3-1v-1h-1l-1-1v-1c1 1 1 1 2 1h1v-2z" class="b"></path><path d="M733 269h0c-1 1-2 2-2 3l-3 3 1 1 1-1c1 1 0 1 1 2l-3 3h-2v-1c0-1 1-2 1-4h0l2-2 1-1 3-3z" class="Y"></path><path d="M749 246c1-1 2-1 4-1h1c0 1 0 0-1 2 0 0-1 1-1 2l1 1-4 2-1 1h2a30.44 30.44 0 0 1-8 8h-1 0v-1l-1-1 1-2c1-1 1-2 3-2v-2l2-1 1-1c2-1 3-3 4-5h-2z" class="B"></path><defs><linearGradient id="AO" x1="723.018" y1="294.218" x2="737.259" y2="207.96" xlink:href="#B"><stop offset="0" stop-color="#c4c4c3"></stop><stop offset="1" stop-color="#f1f0f0"></stop></linearGradient></defs><path fill="url(#AO)" d="M737 213l1-1-1-4-4-4c-2-2-5-5-6-8l32 31c0 2-1 3-1 5h0 0c-6-4-11-10-17-15-1 18-11 36-21 51l-8 11c-3 2-5 5-6 8 1 0 2 1 2 1v-1-2c0-1 0 0 1-1h1c-1 1-1 0-1 1l1-1c0 2 1 3 2 4h-1v6c-1 2-3 3-4 5h-1l-1 1-1 1-4 4-1 1c-2 0-3 0-5 2 0 1 0 1-1 2h0l-2-2v-2l2 1v-1l-2-2c-1 1-1 1-2 1v1c-1-1-1-2-1-3s1-2 1-3c1-1 1-2 2-4 1-1 2-2 3-4s3-3 4-4l15-19c1-1 1-2 2-3 0-1 1-1 1-2 1-1 1-1 1-2h1l2-4c1-1 1-2 1-2l3-5 1-2c0-1 0-1 1-1v-2l1-2 3-6h0l2-4h0c1-1 1-2 1-4 0 0 1-1 1-2v-2s1-1 1-2v-2l1-6v-1c1 0 1-1 1-2z"></path><path d="M699 290l2 1c0 1-1 2-1 3h-1l-1-1 1-3z" class="B"></path><path d="M698 288l15-19c1-1 1-2 2-3 0 1 0 2-1 3l-3 5c0 1-1 1-2 2l-1 1-1 2c-1 1-1 2-2 2l-6 9-1 3c-2 1-3 2-4 3l4-8z" class="O"></path><path d="M706 287c1 0 2 1 2 1v-1-2c0-1 0 0 1-1h1c-1 1-1 0-1 1l1-1c0 2 1 3 2 4h-1v6c-1 2-3 3-4 5h-1l-1 1-1 1v-3-1c-1-3-2-2-4-3h1 1 1l-1-1h0c0-1 0-1 1-1v-1h1l1 1v-1h0l1-2v-2z" class="B"></path><path d="M694 292c1-2 3-3 4-4l-4 8c1-1 2-2 4-3l1 1h1 0c2 1 3 0 4 3v1 3l-4 4-1 1c-2 0-3 0-5 2 0 1 0 1-1 2h0l-2-2v-2l2 1v-1l-2-2c-1 1-1 1-2 1v1c-1-1-1-2-1-3s1-2 1-3c1-1 1-2 2-4 1-1 2-2 3-4z" class="C"></path><path d="M700 294h0c2 1 3 0 4 3v1 3l-4 4v-4c-1-2-2-1-4-2 0-2 2-4 3-5h1z" class="O"></path><path d="M619 199c10-7 23-10 35-9 2 1 5 2 7 3 2 2 4 5 7 6h1c3 2 8 1 12 1v2c2 1 3 1 5 2 9 7 14 16 16 28h0c0 7 1 13-1 19v1 1c-1 4-3 8-4 12s-1 7-2 11c-1 5-4 10-6 15l1 1h2 2c-1 2-2 3-3 4-1 2-1 3-2 4 0 1-1 2-1 3s0 2 1 3v-1c1 0 1 0 2-1l2 2v1l-2-1v2l2 2h0c1-1 1-1 1-2 2-2 3-2 5-2l1-1 4-4 1-1 1-1h1c1-2 3-3 4-5h1c0 2-2 4-4 5l1 1-47 49c-1 2-3 4-5 5h0c-1 2-4 4-6 6-1 2-4 4-5 5-1-1-1-1-1-2l-1-1-1 1h-1c2-5 6-9 9-14l21-35c2-4 5-7 7-11s2-7 3-11v-6c1-3 1-8 1-11l-1-1v-6c0-1-1-3 0-5h0c-1-1-1-2-1-3l-2-2c0-1 0-2-1-3v-1c-2-3-3-6-5-8-1 1 0 2-1 4h0v-4l-2-5c-1-3-2-6-6-7-2-5-5-8-9-12-6-5-13-11-22-10-4 0-7 2-9 5-2 2-2 3-2 5h0l-1-2c0-1-1-2-1-3 0-2-1-3-1-5 0-1 2-4 3-6 1-1 1-3 1-4v-3h0l-1 1-3-1z" class="d"></path><path d="M684 244c-1-2-2-5-2-7l-1-2h0v-1c1 1 1 2 2 3 1 3 2 4 3 7h-2zm-30-42c1 0 2 0 3 1h2l1-1c1 1 2 1 2 2h1l3 2 3 2 2 2h0c-6-3-11-6-17-8z" class="E"></path><path d="M632 194c-2 2-3 5-4 7-2 1-3 2-4 3l-2 2c1-1 1-3 1-4v-3h0c3-2 6-3 9-5z" class="O"></path><path d="M685 233l-1-2c-6-15-21-24-35-30l5 1c6 2 11 5 17 8 7 7 11 13 14 22v1z" class="U"></path><path d="M622 206l2-2h3c-1 4-5 5-4 10v1l1-1c4-3 8-5 13-4 7 1 14 5 18 11v1c-6-5-13-11-22-10-4 0-7 2-9 5-2 2-2 3-2 5h0l-1-2c0-1-1-2-1-3 0-2-1-3-1-5 0-1 2-4 3-6z" class="C"></path><path d="M655 195c4 1 5 5 9 7 3 1 6 1 9 2l6 3c3 0 5 1 7 3l2 2v1c1 2 2 4 4 5h-1v1h-1c2 2 3 4 3 6l-3-4-4-4c-5-6-9-9-16-12-3-1-6-2-8-3v-1c-2 0-2 0-3-1l-1-2h-1c-2 0-2-2-2-3z" class="I"></path><path d="M679 207c3 0 5 1 7 3l2 2v1c1 2 2 4 4 5h-1v1h-1c-1-2-3-4-4-6-2-2-5-4-7-6z" class="K"></path><path d="M619 199c10-7 23-10 35-9 2 1 5 2 7 3 2 2 4 5 7 6h1c3 2 8 1 12 1v2c2 1 3 1 5 2l-1 1c1 0 2 1 3 2v4 1l-2-2c-2-2-4-3-7-3l-6-3c-3-1-6-1-9-2-4-2-5-6-9-7l-4-1h0c-2-1-3-1-4-3-6 0-10 1-15 3-3 2-6 3-9 5l-1 1-3-1z" class="S"></path><path d="M647 191c4 0 6 1 10 2-2 1-4 0-6 1h0c-2-1-3-1-4-3z" class="E"></path><path d="M657 193h1c3 2 4 6 8 8s10 1 15 4c0 0 2 0 3-1l-3-1v-1c2 1 3 1 5 2l-1 1c1 0 2 1 3 2v4 1l-2-2c-2-2-4-3-7-3l-6-3c-3-1-6-1-9-2-4-2-5-6-9-7l-4-1c2-1 4 0 6-1z" class="J"></path><defs><linearGradient id="AP" x1="641.911" y1="301.641" x2="703.613" y2="298.88" xlink:href="#B"><stop offset="0" stop-color="#b1b0af"></stop><stop offset="1" stop-color="#eae9e8"></stop></linearGradient></defs><path fill="url(#AP)" d="M688 207c-1-1-2-2-3-2l1-1c9 7 14 16 16 28h0c0 7 1 13-1 19v1 1c-1 4-3 8-4 12s-1 7-2 11c-1 5-4 10-6 15l1 1h2 2c-1 2-2 3-3 4-1 2-1 3-2 4 0 1-1 2-1 3s0 2 1 3v-1c1 0 1 0 2-1l2 2v1l-2-1v2l2 2h0c1-1 1-1 1-2 2-2 3-2 5-2l1-1 4-4 1-1 1-1h1c1-2 3-3 4-5h1c0 2-2 4-4 5l1 1-47 49c-1 2-3 4-5 5h0c-1 2-4 4-6 6-1 2-4 4-5 5-1-1-1-1-1-2l-1-1-1 1h-1c2-5 6-9 9-14l21-35c2-4 5-7 7-11s2-7 3-11v-6c1-3 1-8 1-11l-1-1v-6c0-1-1-3 0-5h0 0l1 1v2c1 1 0 3 0 5 2 2 0 14 1 18h0c1-1 1-1 1-2v-1c1-1 1-1 1-2 1-3 0-11 0-14-1-1 0-3 0-4l-1-1v-3-3l-1-2v-1s-1-1-1-2v-1c-1-2-2-4-2-6-1-1-1-1-1-2l-1-2v-1l1 1h1c1 0 1 1 2 1h1 2v-2c0 1 0 1 1 1 0-2-1-4-1-7l-1-1v-2-1c2 3 2 7 3 10l3 17 1-2v-1c1-3 0-6 0-9 1-5 0-10 0-15 0-2 0-3-1-5 0-1 0-2-1-3v-3l3 4c0-2-1-4-3-6h1v-1h1c-2-1-3-3-4-5v-1-1-4z"></path><path d="M699 243c1-3 1-8 3-11 0 7 1 13-1 19l-1-2 1-1v-5c0-1 1-1 0-2 0 2-1 5-2 7v2h0l-1-1c1-2 1-4 1-6z" class="O"></path><path d="M690 221l3 4c1 3 3 6 3 9 1 4 1 6 1 10 0 2-1 3-1 5v6 4c-3-3-2-11-2-16 1-3 0-10-1-13-1-1-1-1-1-2s-1-3-2-4v-3z" class="W"></path><defs><linearGradient id="AQ" x1="700.653" y1="219.511" x2="684.847" y2="243.489" xlink:href="#B"><stop offset="0" stop-color="#8c8b8c"></stop><stop offset="1" stop-color="#c3c2c0"></stop></linearGradient></defs><path fill="url(#AQ)" d="M688 207c-1-1-2-2-3-2l1-1c9 7 14 16 16 28h0c-2 3-2 8-3 11 0 2 0 4-1 6l1 1c0 3-1 4-1 7h-1v-4c-1 1-1 1-1 2v-6c0-2 1-3 1-5 0-4 0-6-1-10 0-3-2-6-3-9 0-2-1-4-3-6h1v-1h1c-2-1-3-3-4-5v-1-1-4z"></path><path d="M688 207c-1-1-2-2-3-2l1-1c9 7 14 16 16 28h0c-2 3-2 8-3 11 0-4 1-8 0-12l-3-9-3-6c-1-2-2-3-3-5 0-1-1-2-2-4z" class="P"></path><path d="M711 294h1c0 2-2 4-4 5l1 1-47 49c0-1 0-1-1-2h-1c0-1 1-1 2-2 1 1 1 0 2 1v-1l-1-1c0-1 0-1 1-2 0 1 0 1 1 1v-2l-1-1c1-1 1-1 1-2h0l1-1c1 1 1 0 2 0h2v-1l-1-3h1v2h1v-2-1c2 1 1 1 2 2h1l-1-2 1-1 1 1v-2c1-1 2-2 2-3v-1-1-1l-1-1-1-1 3-3 1-1v-1l2-2v-1c0-1 2-2 3-3 0-2-1-1 0-2s0-1 1-2c0-1 2-3 3-4 0 1 0 2 1 3v-1c1 0 1 0 2-1l2 2v1l-2-1v2l2 2h0c1-1 1-1 1-2 2-2 3-2 5-2l1-1 4-4 1-1 1-1h1c1-2 3-3 4-5z" class="T"></path><path d="M665 341l-1-1c1-1 1-1 1-2h0l1-1c1 1 1 0 2 0h0c1 1 0 1 1 2h0-1l-1 1v1 1c-1 0-1-1-2-1z" class="K"></path><path d="M688 303c0 1 0 2 1 3v-1c1 0 1 0 2-1l2 2v1l-2-1v2l2 2h0c1-1 1-1 1-2 2-2 3-2 5-2l-16 16h-1v1c-1 1-1 1-1 2v1c-1 0 0 0-1-1l-3 2v-1-1-1l-1-1-1-1 3-3 1-1v-1l2-2v-1c0-1 2-2 3-3 0-2-1-1 0-2s0-1 1-2c0-1 2-3 3-4z" class="H"></path><path d="M622 222h0c0-2 0-3 2-5 2-3 5-5 9-5 9-1 16 5 22 10 4 4 7 7 9 12 4 1 5 4 6 7l2 5v4h0c1-2 0-3 1-4 2 2 3 5 5 8v1c1 1 1 2 1 3l2 2c0 1 0 2 1 3h0c-1 2 0 4 0 5v6l1 1c0 3 0 8-1 11v6c-1 4-1 7-3 11s-5 7-7 11l-21 35c-3 5-7 9-9 14h1l1-1 1 1c0 1 0 1 1 2l-1 1-2-1c-1 0-2 2-2 3l1 1c1 0 2-1 3-1 2-1 2-1 3 0 2-1 4-2 5-3l25-25c1-1 3-1 4-2v-1h1l3-1h1l-2 3c0 1-1 3-2 4v1l2 1c-1 2-3 4-3 7v3 5 5c1 0 1 0 2 1v-1l1 5c0 1 1 2 2 2h0c1-1 1 0 2-1v1c1 2 2 9 4 11h-1v3c0 1-1 2-1 3h1c1-2 1-3 2-4 1 0 1 0 1 1 1 1 2 3 2 5h1 2l1-1c2 1 2 1 2 3 0 1-1 2-1 2l-1 3h-1l-6 2-18 6c-3 1-7 2-9 1h-1v-1l-1-1h0-1l-23 6c-2 0-4 1-6 2l1 1 1-1h2 1 0l-4 1c-1 1-1 2-1 2-1 1-2 1-3 2l1 1h0c-1 0-1 0-2 2h2 0c-2 2-4 3-7 3-1 1-3 2-4 2l-1 1c-4 3-9 5-11 11h-1l-2 2v1h-1l-1 1-1 5c-1 2-1 4 0 6l1 1 1 4-3-3c-2-2-4-5-7-7-2-2-2-2-2-5 0 1-1 2-1 3-3 0-4 0-6 1l-3 2v-1c5-8 8-16 12-25l25-59 20-46c3-7 6-14 8-21 2-6 2-13 3-19 0-3 0-5-1-8 0-6-2-12-5-17-1-1-2-1-2-2-1 0-1 0-2-1l-2-2-1 1c1 0 1 0 1 1v1l-2-1h-1c-1 0-2 0-2 1v1h1l-1 2c-3-6-7-11-13-15-1-1-3-2-5-3-1-1-2-2-4-2l2-1c-1-1-2-1-3-2h-1c-1-1-1-2-2-2l1-1c1 1 2 1 4 1h1 2c2 0 6 0 7-2 2-2 2-4 2-6 0-1-1-2-2-2l-1 1c1 0 1 1 2 2 0 1 0 2-1 3h0v-2h-1v1c-1 0-1 0-1 1-3 0-6-2-8-3v-1-1c3 2 5 2 8 2z" class="a"></path><path d="M672 246v4 3l-2-6 2-1z" class="Q"></path><path d="M664 234c4 1 5 4 6 7l2 5-2 1c-1-4-3-9-6-13z" class="E"></path><path d="M649 377c1 1 1 2 1 4l-1 2v3c-1 1-1 2-2 3l-1-1 1-1v-1c0-2 0-1-1-2l-1 1h-1 0l5-8z" class="F"></path><defs><linearGradient id="AR" x1="621.748" y1="233.442" x2="621.192" y2="242.554" xlink:href="#B"><stop offset="0" stop-color="#1c1c1c"></stop><stop offset="1" stop-color="#444443"></stop></linearGradient></defs><path fill="url(#AR)" d="M611 231c2 0 3 0 5 1 1 1 1 1 2 1 5 2 10 6 15 9 0-1 0-2 1-2h0 2c2 2 4 3 5 5 2 3 4 5 5 8-1-1-2-1-2-2-1 0-1 0-2-1l-2-2-1 1c1 0 1 0 1 1v1l-2-1h-1c-1 0-2 0-2 1v1h1l-1 2c-3-6-7-11-13-15-1-1-3-2-5-3-1-1-2-2-4-2l2-1c-1-1-2-1-3-2h-1z"></path><defs><linearGradient id="AS" x1="621.611" y1="315.462" x2="688.673" y2="301.235" xlink:href="#B"><stop offset="0" stop-color="#d3d0d1"></stop><stop offset="1" stop-color="#f7f8f6"></stop></linearGradient></defs><path fill="url(#AS)" d="M672 250h0c1-2 0-3 1-4 2 2 3 5 5 8v1c1 1 1 2 1 3l2 2c0 1 0 2 1 3h0c-1 2 0 4 0 5v6l1 1c0 3 0 8-1 11v6c-1 4-1 7-3 11s-5 7-7 11l-21 35c-3 5-7 9-9 14h1l1-1 1 1c0 1 0 1 1 2l-1 1-2-1c-1 0-2 2-2 3l1 1c1 0 2-1 3-1 2-1 2-1 3 0-6 4-14 7-21 10l5-6c4-5 7-11 11-16 9-15 17-30 23-45 3-8 5-16 7-24 1-11 0-23-1-34v-3z"></path><defs><linearGradient id="AT" x1="634.249" y1="387.247" x2="644.777" y2="420.267" xlink:href="#B"><stop offset="0" stop-color="#d4d3d3"></stop><stop offset="1" stop-color="#fefdfd"></stop></linearGradient></defs><path fill="url(#AT)" d="M678 340c1-1 3-1 4-2v-1h1l3-1h1l-2 3c0 1-1 3-2 4v1l2 1c-1 2-3 4-3 7v3 5 5c1 0 1 0 2 1v-1l1 5c0 1 1 2 2 2h0c1-1 1 0 2-1v1c1 2 2 9 4 11h-1v3c0 1-1 2-1 3h1c1-2 1-3 2-4 1 0 1 0 1 1 1 1 2 3 2 5h1 2l1-1c2 1 2 1 2 3 0 1-1 2-1 2l-1 3h-1l-6 2-18 6c-3 1-7 2-9 1h-1v-1l-1-1h0-1l-23 6c-2 0-4 1-6 2l1 1 1-1h2 1 0l-4 1c-1 1-1 2-1 2-1 1-2 1-3 2l1 1h0c-1 0-1 0-2 2h2 0c-2 2-4 3-7 3-1 1-3 2-4 2l-1 1c-4 3-9 5-11 11h-1l-2 2v1h-1l-1 1-1 5c-1 2-1 4 0 6l1 1 1 4-3-3c-2-2-4-5-7-7-2-2-2-2-2-5v-1l3-9c0-2 2-4 3-6 6-13 21-21 32-30 5-4 9-8 12-12h1l1-1c1 1 1 0 1 2v1l-1 1 1 1c1-1 1-2 2-3v-3l1-2c0-2 0-3-1-4 0 0 1-2 2-2l4-7v-2l-2-1 25-25z"></path><path d="M677 393c-1 1-2 3-2 5l-3 3v-3l5-5z" class="L"></path><path d="M630 407v-2c1-1 3-2 4-3 1 1 1 1 2 3-2 0-2 0-3 1h-1c-1 0-1 1-2 1z" class="E"></path><path d="M680 375h1l-2 6v2c-1 2-2 7-4 7h-1l-2 3c-1 0-1 1-1 0 4-5 7-11 9-18h0z" class="Q"></path><path d="M642 405c3-1 6-2 9-2 7-2 14-4 19-9 0 2-1 5-4 6-1 1-3 1-4 1-2 0-5 2-8 2-1 0-2 1-4 1l-3 1h-5z" class="L"></path><path d="M641 411l2-2h1c1-1 1-1 2-1h0-2c6-2 14-5 20-3l-23 6z" class="W"></path><path d="M649 386c1-2 2-5 4-6 0 2 0 2-1 4v1c-1 1-1 0-1 1v1c0 1-3 3-4 4-3 1-7 6-9 6v-1c2-2 6-4 6-6v-1l2-2c0-1 0-1 1-1v1l-1 1 1 1c1-1 1-2 2-3z" class="H"></path><defs><linearGradient id="AU" x1="673.724" y1="387.657" x2="670.182" y2="384.62" xlink:href="#B"><stop offset="0" stop-color="#3b3a3a"></stop><stop offset="1" stop-color="#555454"></stop></linearGradient></defs><path fill="url(#AU)" d="M677 376l2-1h1c-2 7-5 13-9 18l-1 1c-5 5-12 7-19 9-3 0-6 1-9 2l-7 1-3 1-1 1-1-1c1 0 1-1 2-1h1c1-1 1-1 3-1-1-2-1-2-2-3 1 0 3-1 4-2s3-1 4-2h0c2 0 4-2 6-3h3l-1 1h0v1h0v1 1l3-1 2-1-1 4h1c2-1 7-2 8-4 3-4 7-8 10-13l3-6 1-2z"></path><path d="M653 398l2-1-1 4c-2 0-3 1-4 1l-4 1 1-3c1 0 2-1 3-1l3-1z" class="F"></path><path d="M653 398l2-1-1 4c-2 0-3 1-4 1l3-4z" class="K"></path><path d="M648 395h3l-1 1h0v1h0v1 1c-1 0-2 1-3 1l-1 3h-5c-2 1-4 1-5 2-1-2-1-2-2-3 1 0 3-1 4-2s3-1 4-2h0c2 0 4-2 6-3z" class="B"></path><path d="M641 403c2-2 4-2 6-3l-1 3h-5z" class="H"></path><path d="M682 365c1 0 1 0 2 1v-1l1 5c0 1 1 2 2 2h0c1-1 1 0 2-1v1c1 2 2 9 4 11h-1v3c0 1-1 2-1 3-1 0-2 1-2 2l-5 4v2 1h-6c-2 1-4 3-7 4h0l1-1 3-3c0-2 1-4 2-5 1-2 2-3 2-4 0-2 0-3 1-4l3-6c0-3 1-5 0-7h-2 0c0-1 1-3 1-5v-2z" class="B"></path><path d="M682 365c1 0 1 0 2 1v-1l1 5c0 1 1 2 2 2h0c1-1 1 0 2-1v1c1 2 2 9 4 11h-1v3c0 1-1 2-1 3-1 0-2 1-2 2-1-1-1-2-1-3 1-3 2-8 0-11 0-1 0-2-1-3l-2-3c0 1 0 2-1 2 0-1 0-3-1-4v-1-1h-1v-2z" class="O"></path><path d="M680 385h1c1-2 3-4 4-6l1 1v2c0 1 1 2 0 4l-1 1c-1 5-4 8-7 11-2 1-4 3-7 4h0l1-1 3-3c0-2 1-4 2-5 1-2 2-3 2-4 0-2 0-3 1-4z" class="E"></path><path d="M686 382c0 1 1 2 0 4l-1 1c-1 0-1 1-2 1v-2c0-1 2-3 3-4z" class="O"></path><path d="M683 388c1 0 1-1 2-1-1 5-4 8-7 11-2 1-4 3-7 4h0l1-1 3-3 8-10z" class="N"></path><path d="M691 389h1c1-2 1-3 2-4 1 0 1 0 1 1 1 1 2 3 2 5h1 2l1-1c2 1 2 1 2 3 0 1-1 2-1 2l-1 3h-1l-6 2-18 6c-3 1-7 2-9 1h-1v-1l-1-1h0l2-2h2c0-1 1-1 2-1h0c3-1 5-3 7-4h6v-1-2l5-4c0-1 1-2 2-2z" class="Q"></path><path d="M684 398v-1-2c2 1 3 1 4 1-1 1-2 2-4 2z" class="O"></path><path d="M691 389h1c1-2 1-3 2-4 1 0 1 0 1 1 1 1 2 3 2 5h1 2v1c-2-1-3 0-5 0h0c-1 2-4 3-7 4-1 0-2 0-4-1l5-4c0-1 1-2 2-2z" class="P"></path><path d="M701 390c2 1 2 1 2 3 0 1-1 2-1 2l-1 3h-1l-6 2-18 6c-3 1-7 2-9 1h-1v-1c3-1 7-2 11-3 8-2 17-4 23-11v-1l1-1z" class="V"></path><defs><linearGradient id="AV" x1="610.053" y1="442.091" x2="608.009" y2="427.36" xlink:href="#B"><stop offset="0" stop-color="#b6b5b4"></stop><stop offset="1" stop-color="#dfdfde"></stop></linearGradient></defs><path fill="url(#AV)" d="M635 413l1 1 1-1h2 1 0l-4 1c-1 1-1 2-1 2-1 1-2 1-3 2l1 1h0c-1 0-1 0-2 2h2 0c-2 2-4 3-7 3-1 1-3 2-4 2l-1 1c-4 3-9 5-11 11h-1l-2 2v1h-1l-1 1-1 5c-1 2-1 4 0 6l1 1 1 4-3-3c-2-2-4-5-7-7-2-2-2-2-2-5v-1l3-9v2 1l1-1v-1c2-1 4-4 6-5 3-2 5-4 8-5 1 0 1-1 1-1 2 0 3 0 4 1 1-1 0 0 2 0 1 0 4-2 6-2l1-1v-1h2v-1h0l-1-1h1c-4 0-6 1-9 1v-1c5-2 10-4 16-5z"></path><path d="M594 442h1c3-2 4-5 7-5 1 1 1 2 2 3l-1 3v-3l-1-1c-1 1-1 2-2 2-2 0-2 1-3 2v3c0 1 0 1-1 2-2-2-2-2-2-5v-1z" class="P"></path><path d="M597 446v-3c1-1 1-2 3-2 1 0 1-1 2-2l1 1v3l1 4c-1 2-1 4 0 6l1 1 1 4-3-3c-2-2-4-5-7-7 1-1 1-1 1-2z" class="D"></path><path d="M597 446l3-3 1 1h0c-1 4 0 7 2 11-2-2-4-5-7-7 1-1 1-1 1-2z" class="R"></path><path d="M635 413l1 1 1-1h2 1 0l-4 1c-1 1-1 2-1 2-1 1-2 1-3 2l1 1h0c-1 0-1 0-2 2h2 0c-2 2-4 3-7 3-1 1-3 2-4 2-2 1-4 1-5 2l-8 6c2-3 6-6 9-8l7-4h0l1-1v-1h2v-1h0l-1-1h1c-4 0-6 1-9 1v-1c5-2 10-4 16-5z" class="Q"></path><path d="M635 413l1 1 1-1h2 1 0l-4 1-11 3c-1 1-4 2-6 1 5-2 10-4 16-5z" class="G"></path><path d="M678 340c1-1 3-1 4-2v-1h1l3-1h1l-2 3c0 1-1 3-2 4v1l2 1c-1 2-3 4-3 7v3 5 5 2c0 2-1 4-1 5h0l-1 3h0-1l-2 1-1 2-3 6c-3 5-7 9-10 13-1 2-6 3-8 4h-1l1-4-2 1-3 1v-1-1h0v-1h0l1-1h-3c-2 1-4 3-6 3h0c1-1 4-3 5-3 3-3 6-5 9-8 0-1 1-2 1-3v-1c-1 1-2 1-4 2h0l-2 1c0-1 0 0 1-1v-1c1-2 1-2 1-4-2 1-3 4-4 6v-3l1-2c0-2 0-3-1-4 0 0 1-2 2-2l4-7v-2l-2-1 25-25z" class="X"></path><path d="M666 373h0c0 4-3 6-3 9l2-2 1-1v1h1c-1 3-3 5-5 7l-2 2-1-1-1-1c1-2 3-4 4-6 2-3 3-6 4-8z" class="F"></path><path d="M665 369l1-1c0 3-1 5-2 7-1 4-5 10-8 12 0-1 1-2 1-3v-1c-1 1-2 1-4 2h0l6-9h-1l2-3c1-2 3-3 5-4z" class="P"></path><path d="M660 373c1-2 3-3 5-4-1 3-2 5-5 7h-1-1l2-3z" class="M"></path><path d="M683 344l2 1c-1 2-3 4-3 7v3 5 5 2c0 2-1 4-1 5h0l-1 3h0-1l-2 1v-1l1-5v-1l1-4c0-3 1-7-1-10-2 0-3 0-5 1v2h-1c-1 0-2 1-2 1h-1c0-1 1-2 2-3 1 0 1-1 2-1 1-2 3-4 5-5l2-1c2-2 2-3 3-5z" class="J"></path><path d="M682 355v5 5 2c0 2-1 4-1 5h0l-1 3h0-1l-2 1v-1l1-5v-1l1-4c2-3 1-5 1-9l2-1z" class="R"></path><path d="M677 375c1-2 2-3 3-5l1 2h0l-1 3h0-1l-2 1v-1z" class="e"></path><path d="M666 373c0-1 1-3 1-4l1-5c2-2 5-5 8-5 1 1 1 2 1 3 0 2 0 4-1 5s-1 3-1 4c0 2 0 1-1 2v2l-1-1h-1c-1 1-3 5-5 6h-1v-1l-1 1-2 2c0-3 3-5 3-9h0z" class="D"></path><defs><linearGradient id="AW" x1="667.374" y1="387.704" x2="656.532" y2="382.492" xlink:href="#B"><stop offset="0" stop-color="#939192"></stop><stop offset="1" stop-color="#c9c9c7"></stop></linearGradient></defs><path fill="url(#AW)" d="M677 362c0 3 1 5 1 7v1l-1 5v1l-1 2-3 6c-3 5-7 9-10 13-1 2-6 3-8 4h-1l1-4-2 1-3 1v-1-1h0v-1h0l1-1h-3c3-3 7-5 10-8l1 1 1 1 2-2c2-2 4-4 5-7 2-1 4-5 5-6h1l1 1v-2c1-1 1 0 1-2 0-1 0-3 1-4s1-3 1-5z"></path><path d="M650 398v-1h0v-1h0 1 2l-1 1s-1 1-2 1z" class="C"></path><path d="M662 390c2-2 4-3 6-5l-2 4c-2 2-3 3-5 4 0-1 1-2 1-3z" class="X"></path><path d="M671 381h1c2-1 3-2 4-3l-3 6-7 5 2-4c1-1 2-2 3-4z" class="Y"></path><path d="M662 390c0 1-1 2-1 3l-1 1h1c0 2-2 2-3 3s-2 2-3 4h-1l1-4c2-2 4-5 7-7z" class="b"></path><path d="M677 362c0 3 1 5 1 7v1l-1 5v1l-1 2c-1 1-2 2-4 3h-1l2-2-1-1 1-1v-3l1 1v-2c1-1 1 0 1-2 0-1 0-3 1-4s1-3 1-5z" class="K"></path><path d="M673 379c2-3 3-6 5-9l-1 5v1l-1 2c-1 1-2 2-4 3h-1l2-2z" class="X"></path><path d="M666 389l7-5c-3 5-7 9-10 13-1 2-6 3-8 4 1-2 2-3 3-4s3-1 3-3h-1l1-1c2-1 3-2 5-4z" class="J"></path><path d="M678 340c1-1 3-1 4-2v-1h1l3-1h1l-2 3c0 1-1 3-2 4v1c-1 2-1 3-3 5l-2 1c-2 1-4 3-5 5-1 0-1 1-2 1-1 1-2 2-2 3-1 1-1 3-1 4-1 0-2-1-4-2-1 4-3 8-4 12l-2 3h1l-6 9-2 1c0-1 0 0 1-1v-1c1-2 1-2 1-4-2 1-3 4-4 6v-3l1-2c0-2 0-3-1-4 0 0 1-2 2-2l4-7v-2l-2-1 25-25z" class="f"></path><path d="M655 368v1c0 1-1 2-1 4v2c1-1 2-3 3-4 0-1 1-3 2-4h1c0 2-3 8-2 9h1l-6 9-2 1c0-1 0 0 1-1v-1c1-2 1-2 1-4-2 1-3 4-4 6v-3l1-2c0-2 0-3-1-4 0 0 1-2 2-2l4-7z" class="X"></path><path d="M664 361h1c0-3 2-4 4-6 3-4 7-7 10-11 2-2 3-4 6-5 0 1-1 3-2 4v1c-1 2-1 3-3 5l-2 1c-2 1-4 3-5 5-1 0-1 1-2 1-1 1-2 2-2 3-1 1-1 3-1 4-1 0-2-1-4-2z" class="G"></path><path d="M296 210l14-12c3-3 6-6 8-9l21-1c4 0 8 0 12 1 3 0 7 1 10 1 0 1 2 1 3 1 0 0 1-1 2-1 2-2 4-5 8-6 4-2 9-2 13-2 16-1 29 2 41 13 4 4 7 9 7 16v5c-1 2-1 4-2 5-2 3-5 6-8 7l1 1-3 3h3c-9 4-16 9-23 17l-4 6c0-1 0-2-1-2l-4 1-2 3-1 5-2-1c-2 3-3 4-4 8h0c0 2-1 6-1 8-2 2-1 4-2 7 1 2 0 4 1 6 0 4 1 7 2 10 4 13 10 25 15 37l6 16c1 4 3 8 5 12 2 8 7 16 9 24 0 2 0 3 1 4 0 1 0 0 1 2 0 0 0 1 1 1l-1 1 2 5c1 1 1 2 2 3l5 12v1c0 1 1 2 1 3 1 1 1 3 2 5l5 12v1l1 1v1c1 2 0-1 0 1 1 3 3 6 4 9-3-2-6-3-9-5l-1-1c-1 0-2-1-3-1v-1c0-2 0-2-1-4 0 0-1-4-2-5-3-15-19-24-30-32l-1 1h1c-1 2-2 3-3 4-1-1-1-1-3-1h0c-3-1-6-2-9-2l-4-1-7-2c-2 0-4-1-6-2-2-3-4-6-7-9l-2-2c-3-2-6-6-8-9l-3-6c-1-3-2-7-3-10 0-2 0-6-1-7l-1 2c-1-2 0-7-1-10 0-2-2-4-2-6h0l1-1c-1-3-3-7-5-9-1-1-2-2-2-3-1-2-2-3-4-4h-1l-2-2c-1-4-9-11-12-13l1-2c-2 0-1 0-2-1v-1l-3-6h-4-4l-5 1v-1c4-2 8-3 12-3-1-1-2-1-3-1l-2-2-1-1-2-3h0c-2-1-6-6-7-8v-1c-1-1-2-2-3-2l-1-3h-1c-3-2-7-3-10-4l-4-1-4-1-3-1-5-3-2-2-2-2-1-2v-1l-1-1c-1-3-1-8-1-11l1-7-1-2-1-1 1-4v-1h1 1v1l6-6s1-2 1-3h1c0-1 1-2 2-3 1 0 1 1 1 2h3c1-2 1-3 3-4v1l1 1c3-7 14-12 20-16 1 2 1 3 0 5h1z" class="d"></path><path d="M342 195c2-1 19 0 21 1-1 1-2 1-3 1-6-1-11-1-17-1l-1-1z" class="L"></path><path d="M347 282h2c0 2 0 3 1 5l1 3v1h-1s-1-1-1-2v-1h0-1c-2-2-1-4-1-6z" class="W"></path><path d="M349 263l1 1-1 2c0 1 0 3 1 4l-1 12h-2c0-7 1-12 2-19z" class="O"></path><path d="M347 227v1c3-2 3-6 5-9h0c1 2 0 3-1 5-1 3-3 7-4 10 0 2 0 5-1 7 0-1-1-2-1-4 1-3 0-7 2-10z" class="L"></path><path d="M344 238l1-1c0 2 1 3 1 4 1 2 1 2 1 4l1 1c0 2 0 4-1 6v2c0 1-1 2-1 3v2l-1-1h-1l1-6c0-2-1-4 0-5v-3c0-2 0-4-1-6z" class="E"></path><path d="M345 252v-4h1c0 2 0 4 1 6 0 1-1 2-1 3v2l-1-1h-1l1-6z" class="Q"></path><path d="M344 299c1 1 2 2 3 4l11 17c2 2 3 5 5 7h0-3c-1-3-3-5-5-6v-1c-1-1-2-2-2-3v-3l-4-6-3-5c-1-1-1-2-2-4z" class="F"></path><path d="M340 260h0 0l-1-2c-1-1-1-3-2-4-1-2-1-5-3-6-1-2-3-5-4-7 0-1-1-3-1-4v-1c-1-1-1 0-1-1v-2c-1-2 0-4 0-6-1-1-1-1-1-2l1-1c0-2-2-2-2-4 1 0 2-1 2-2l1-1h0c0-1 0-1 1-2 0-1 1-2 1-3h0c1-1 0-1 1-1v-1h1c-4 8-6 18-4 27 1 2 3 5 4 7s2 4 3 5l4 10v1z" class="E"></path><path d="M354 248l7-12-4 11c-1 1-2 3-2 4-3 6-3 11-4 17 0 4-1 7-1 11v8c-1-2-1-3-1-5l1-12c1-7 1-15 4-22z" class="c"></path><path d="M344 238c1 2 1 4 1 6v3c-1 1 0 3 0 5l-1 6-1 10-1-4c-1-1-1-3-2-4v-1c1-3 0-6 1-9 1-4 2-7 3-12z" class="B"></path><path d="M352 233l1-1c1-5 3-9 5-13 7-10 18-15 30-17-4 1-7 3-11 5-8 5-15 10-20 18-2 3-3 6-5 8z" class="U"></path><path d="M355 251c0 3-1 6-2 9h0c0 1-1 3 0 3h0 1v3c1-1 1 0 1-1 1 1 1 2 1 3l-1 1v2c-1 1 0 3 0 4-1 2-1 5-1 7v3c-1-1-1-4-1-6h-1c0 2 1 6 0 7-2-2 0-5-2-7 0-4 1-7 1-11 1-6 1-11 4-17z" class="W"></path><path d="M366 220h1c1-1 1-1 2-1l3-2h0l-1 1c-1 1-2 2-3 4s-8 10-10 11h0-1c0 2-1 3-2 5l-1 4c-1 1-1 2-1 3v2l1 1c-3 7-3 15-4 22-1-1-1-3-1-4l1-2-1-1c-1-1-1 0 0-1 0-4 0-8 1-11 2-4 2-9 4-12v-1c2-5 4-10 8-14 0-1 1-1 1-2 1 0 2-1 3-2z" class="B"></path><path d="M361 206s3-1 3-2l1 1h-1v1 1l-5 4-2-1c-5 5-8 10-10 17-2 3-1 7-2 10l-1 1c-1 5-2 8-3 12-1-4-1-10 1-14v-4c1-2 2-4 2-6 0 0 0-1 1-2v-1c1-3 3-4 4-7 1-2 5-6 7-7 2 0 4-2 5-3z" class="I"></path><path d="M377 207c-1 1-3 3-3 4h0c-3 2-4 4-7 5v1l-1 1c-2 1-5 4-6 6l4-4h2c-1 1-2 2-3 2 0 1-1 1-1 2-4 4-6 9-8 14v1c-1 0-1 1-2 2v2c-1 1-1 1-1 2v1h0c-1-2 0-3 0-4v-2-1l-1-1c0-1 1-1 1-2s1-2 1-3c2-2 3-5 5-8 5-8 12-13 20-18z" class="L"></path><path d="M355 335c1-1 2-1 3-1l5 4c1 1 0 1 1 2 1 2 3 2 5 1 4 5 7 10 10 16l3 6 2 2h-1c-2-1-4-2-5-4-2-4-6-7-8-10l-15-16z" class="N"></path><path d="M374 197h1l8-4c0 2-1 4-2 5 0 0-2 1-3 2-4 2-9 4-14 6v-1h1l-1-1c0 1-3 2-3 2-3 0-4 1-7 3-1 1-1 1-2 1-2 1-4 4-6 5 2-2 4-4 4-7 1-1 4-3 5-5 1-1 2-1 3-1l1-2 6-1h3c2 0 4-1 6-2z" class="J"></path><path d="M365 199l4 1c-1 2-6 2-8 3-1-1-1-1-3-1l1-2 6-1z" class="V"></path><defs><linearGradient id="AX" x1="335.882" y1="232.024" x2="348.052" y2="237.957" xlink:href="#B"><stop offset="0" stop-color="#979696"></stop><stop offset="1" stop-color="#b6b5b5"></stop></linearGradient></defs><path fill="url(#AX)" d="M350 208c0 3-2 5-4 7 2-1 4-4 6-5 1 0 1 0 2-1 3-2 4-3 7-3-1 1-3 3-5 3-2 1-6 5-7 7-1 3-3 4-4 7v1c-1 1-1 2-1 2 0 2-1 4-2 6v4c-2 4-2 10-1 14-1 3 0 6-1 9l-4-10h1l1 3h0l1-1c-2-3-1-9-1-12 1-12 3-23 12-31z"></path><path d="M301 214c3-4 8-8 12-12 2-1 4-4 6-5l1-1c3-2 8-1 12-1l-1 1h-3 1 1c-2 2-4 3-6 4-1 1-2 3-4 4-1 1-2 2-3 4-1 0 0 0-1 1v-1c-2 0-3 1-4 1-3 2-3 4-3 6v1c0 1 0 1-1 1-1 2-1 5-1 8 1 0 0 1 0 2v1 1c0 1-1 3 0 4v1l1 1v1c0 1 1 3 1 4l-1-1c0-2-2-5-3-7l-1-6s-1-1-1-2l-1-7c-1-1-1-2-1-3z" class="H"></path><path d="M355 321c2 1 4 3 5 6h-1c1 0 1 1 2 1 1 1 2 3 3 4s1 2 2 2l2 4c3 3 5 7 7 11l3 3h0c2 1 3 2 4 4 0-1-1-1-1-2l-1-1c-3-7-9-13-11-20l-3-6c-1-1-1-1-1-2v-1c-1-1-1 0-1-1h1l6 12 2 4h1 0c-1-1 0 0-1-2 0-2-2-4-3-6-1-3-4-7-3-9 0 1 1 1 1 2l4 8c1 1 0 1 1 2s2 3 3 5l4 8c2 3 5 6 6 10l1 4h0c-1-1-1-2-2-3 0 2 0 3 1 4 0 1 1 2 3 3l-1 1 2 2 1 1-2 2h1c2 1 3 3 5 3l-5-11-4-7-2-4c-1-1-1-2-1-2-1-2-2-3-3-5l-2-4c-1-1-1-2-1-3s-1-3-2-4c0-1-1-2-1-4h-1v-3l24 50-14-9c2 1 5 1 7 1-1-1-3-2-4-2-1-1-1-1-2-1l-1-1h1l-2-2-3-6c-3-6-6-11-10-16 0-1-1-1-1-2l-4-5h-2v-4l-1 1v1h-1v-1c-1-1-1-1-1-2l-2-4-1 1h-1v-1-2-2h0z" class="I"></path><path d="M401 192c4 2 8 3 12 6 4 2 6 4 9 8l-3 3 1 2h-1-1c0 1-1 1-1 2s-1 3-1 4h-2 0c-3-3-5-5-8-5-11 1-23 10-30 17v-1h0c1-3 3-4 4-6 3-5 11-9 16-12 1-1 2-2 3-2h1c1-1 0-1 2-1 1-1 4 0 6 0h0 2c1 1 1 1 3 1 0-1 1-1 1-2s-1-2-1-3c-2-4-4-6-8-8-1 0-3-1-3-2l-1-1z" class="C"></path><path d="M413 198c4 2 6 4 9 8l-3 3 1 2h-1-1c0 1-1 1-1 2s-1 3-1 4h-2 0c0-4 2-7 3-11 0-3-3-6-4-8z" class="M"></path><path d="M374 197c2-3 5-5 9-7 3-1 9 0 12 0 7 0 13 3 19 6 3 2 6 3 8 6l3 3c3 2 6 3 7 6s0 7-2 10c-1 1-3 2-4 3-2 1-4 3-5 5 1-1 2-1 3-2h0c3 0 4-2 5-3l1-1 2-2h1c-2 3-5 6-8 7s-6 2-9 1c-1-1-3-2-4-4h0c0-1-1-1-1-2 0-2 1-3 2-4l1-2h2c0-1 1-3 1-4s1-1 1-2h1 1l-1-2 3-3c-3-4-5-6-9-8-4-3-8-4-12-6-6-1-11-2-18 1l-8 4h-1z" class="G"></path><defs><linearGradient id="AY" x1="421.442" y1="216.991" x2="417.931" y2="213.001" xlink:href="#B"><stop offset="0" stop-color="#7b7a7a"></stop><stop offset="1" stop-color="#929291"></stop></linearGradient></defs><path fill="url(#AY)" d="M422 206v1c1 1 1 2 2 3s1 0 1 1v1c1 1 1 3 1 4l-1 1v1c0 1-1 1-2 2h-1c-1 1-2 1-3 1l2-2h1v-1c-2 0-3-1-5 0h-1l-2-1h2c0-1 1-3 1-4s1-1 1-2h1 1l-1-2 3-3z"></path><path d="M425 205c3 2 6 3 7 6s0 7-2 10c-1 1-3 2-4 3-2 1-4 3-5 5 1-1 2-1 3-2h0c3 0 4-2 5-3l1-1 2-2h1c-2 3-5 6-8 7s-6 2-9 1c-1-1-3-2-4-4h0c0-1-1-1-1-2 0-2 1-3 2-4l1-2 2 1h1c2-1 3 0 5 0v1h-1l-2 2-1 1c-1-1-2-1-3-2v3l1 1c1 0 3 0 4-1 3-1 6-3 7-6 2-4-1-8-2-12z" class="O"></path><path d="M414 217l2 1h1c2-1 3 0 5 0v1h-1l-2 2-1 1c-1-1-2-1-3-2v3l1 1h-1c-1 1 0 1-1 3-1-1-1-2-1-3-1-2 0-3 1-5h-1l1-2z" class="J"></path><path d="M333 210l1-1c4-4 8-8 15-9h10l-1 2c-1 0-2 0-3 1-1 2-4 4-5 5-9 8-11 19-12 31 0 3-1 9 1 12l-1 1h0l-1-3h-1c-1-1-2-3-3-5s-3-5-4-7c-2-9 0-19 4-27z" class="a"></path><path d="M355 203c-1 2-4 4-5 5-9 8-11 19-12 31 0 3-1 9 1 12l-1 1h0l-1-3c-2-7-1-16 0-22 3-11 9-18 18-24z" class="P"></path><defs><linearGradient id="AZ" x1="333.448" y1="265.517" x2="315.555" y2="272.407" xlink:href="#B"><stop offset="0" stop-color="#c3c2c3"></stop><stop offset="1" stop-color="#eeedec"></stop></linearGradient></defs><path fill="url(#AZ)" d="M297 218h0c1-1 2-1 3-1l1-1v-2c0 1 0 2 1 3l1 7c0 1 1 2 1 2l1 6c1 2 3 5 3 7l1 1 2 4 2 4 3 7c1 1 1 2 2 3l1 2 1 1c2 4 4 8 7 12h0c1 1 2 2 3 4l3 4c0 1 0 1 1 2 0 1 1 1 1 2 1 1 2 3 3 4l2 4c1 1 1 0 1 1 1 1 1 2 2 3 0 0 1 1 1 2h0c1 2 1 3 2 4l3 5 4 6v3c0 1 1 2 2 3v1h0l-2-1-2-3h-2 0-2c1-1 2-1 3-1 0-2-2-4-3-6l-8-11-10-15c-7-11-15-22-22-34-3-5-6-11-7-18l-1-3v-1c-1-4-2-7-2-10z"></path><path d="M274 220l1 1c3-7 14-12 20-16 1 2 1 3 0 5h1l-22 21v17c0 2-1 5 0 7 0 0 3 3 3 4 3 2 20 21 20 22v1l1 2h0c2 2 4 5 6 7v1l2 2h1c0 1 1 2 2 3h-1c-1-1-2-1-3-1l-2-2-1-1-2-3h0c-2-1-6-6-7-8v-1c-1-1-2-2-3-2l-1-3h-1c-3-2-7-3-10-4l-4-1-4-1-3-1-5-3-2-2-2-2-1-2v-1l-1-1c-1-3-1-8-1-11l1-7-1-2-1-1 1-4v-1h1 1v1l6-6s1-2 1-3h1c0-1 1-2 2-3 1 0 1 1 1 2h3c1-2 1-3 3-4v1z" class="f"></path><path d="M274 220l1 1c3-7 14-12 20-16 0 3 0 3-2 5l-11 8c-2 2-6 6-8 6v-4zm-3 16c2 4 1 11 0 15v6c0 1 2 3 3 4 2 5 8 8 12 12-3-1-9-2-11-4 0-1-3-2-4-3-1-2-3-3-4-4-3-1-4-4-5-6 0-1-1-3-1-5l1-1h1 1l1 1c1 1 3 2 4 3 1-6 2-12 2-18z" class="G"></path><defs><linearGradient id="Aa" x1="264.638" y1="260.768" x2="267.263" y2="252.541" xlink:href="#B"><stop offset="0" stop-color="#393939"></stop><stop offset="1" stop-color="#5f5e5d"></stop></linearGradient></defs><path fill="url(#Aa)" d="M261 251l1-1h1 1l1 1c1 2 3 4 4 6l2 1c0 1 0 1-1 1 0 2 0 2 1 4-2-1-3-1-4-1-3-1-4-4-5-6 0-1-1-3-1-5z"></path><path d="M269 257l2 1c0 1 0 1-1 1 0 0-1 0-2-1l1-1z" class="U"></path><path d="M255 247h1 0c1-1 1-3 1-4h0c0 1 1 1 2 3 1-2 1-2 3-3v1 1l2 2h0v2 1h-1-1l-1 1c0 2 1 4 1 5 1 2 2 5 5 6 1 1 3 2 4 4 1 1 4 2 4 3h-1v2l-4-1-3-1-5-3-2-2-2-2-1-2v-1l-1-1c-1-3-1-8-1-11z" class="J"></path><path d="M264 247h0v2 1h-1-1l-1 1c0 2 1 4 1 5-2 0-2-3-4-4h-1v-1h0v-1h1c2-2 3-2 5-2l1-1z" class="R"></path><path d="M264 247h0v2 1h-1-1l-1 1v-2h2v-1l1-1z" class="e"></path><path d="M255 247h1 0c1-1 1-3 1-4h0c0 1 1 1 2 3 1-2 1-2 3-3v1 1h-1v1l-5 4c0 3 1 6 1 9l-1-1c-1-3-1-8-1-11z" class="Y"></path><path d="M257 252h1c2 1 2 4 4 4 1 2 2 5 5 6 1 1 3 2 4 4-3-1-5-2-7-4-3-2-5-4-6-7l-1-1v-2z" class="e"></path><path d="M257 260c1-1 1-1 1 0 1 0 2 0 2 1l9 6c1 1 4 1 5 2v2l-4-1-3-1-5-3-2-2-2-2-1-2z" class="V"></path><path d="M257 260c1-1 1-1 1 0 1 0 2 0 2 1 1 1 2 2 2 3h-2 0l-2-2-1-2z" class="N"></path><path d="M267 221c1 0 1 1 1 2h3l-1 2v3l1 8c0 6-1 12-2 18-1-1-3-2-4-3l-1-1v-1-2h0l-2-2v-1-1c-2 1-2 1-3 3-1-2-2-2-2-3h0c0 1 0 3-1 4h0-1l1-7-1-2-1-1 1-4v-1h1 1v1l6-6s1-2 1-3h1c0-1 1-2 2-3z" class="R"></path><path d="M262 234h1l3 6 1 3c-1 1-1 4-3 4h0l-2-2v-1-1c-1-2-2-4-2-6 0-1 1-2 2-3z" class="M"></path><path d="M260 237h1c1 1 2 3 2 5h0c0 1-1 2-1 2v-1c-1-2-2-4-2-6z" class="P"></path><path d="M257 232v1l6-6c0 3-1 4-3 6 0 1 1 1 2 1-1 1-2 2-2 3 0 2 1 4 2 6-2 1-2 1-3 3-1-2-2-2-2-3h0c0 1 0 3-1 4h0-1l1-7-1-2-1-1 1-4v-1h1 1z" class="b"></path><path d="M255 232h1v8l-1-2-1-1 1-4v-1z" class="f"></path><path d="M265 230v-1-1c0-2 1-3 2-5h0l1 3h0v1l2-2v3l1 8c0 6-1 12-2 18-1-1-3-2-4-3l-1-1v-1c1 0 1 1 2 1 1-1 2-1 3-3 1-4 0-8-3-12v-1c1-2 0-3-1-4z" class="U"></path><path d="M265 230v-1-1c0-2 1-3 2-5h0l1 3h0v1l2-2v3 6h-1c-1-3-2-4-2-7-1 1-1 1-2 3z" class="g"></path><path d="M297 218c0 3 1 6 2 10v1l1 3c1 7 4 13 7 18 7 12 15 23 22 34l10 15 8 11c1 2 3 4 3 6-1 0-2 0-3 1h2 0 2l2 3 2 1v2 2 1h1l1-1 2 4c0 1 0 1 1 2v1h1v-1l1-1v4h2l4 5c0 1 1 1 1 2-2 1-4 1-5-1-1-1 0-1-1-2l-5-4c-1 0-2 0-3 1l-39-43-27-29c-2-2-6-8-10-9v-1c-1-2-1-4-1-6v-13c2-2 6-5 9-7 3-3 6-7 10-9z" class="S"></path><path d="M286 252c1 0 1 0 2-1v-1h0 1v1h0v2l-1 2h1s0-1 1-1v-1l1 1c1-1 1 0 2-1h0 3 2v2c1 0 2 1 2 1 0 1 0 1-1 1 0 1 0 1-1 2l1 1v2c1 1 2 1 3 1h0c0 1 1 1 1 1 0 1-1 1-1 3l1 1-1 1h0c1 1 0 0 0 1v1h-2c0-1-1-2-2-3l-2-1v-1c-1-1-1-1-2-1-1-1-1-1-2-1 2-2 1-3 1-5v2c-1 0 0 0-1 1-3-2-5-5-8-8 1 0 1 0 1-1v-1h1z" class="O"></path><path d="M325 284v1h2v1c1 0 1-1 2-2l10 15c-1 0-1 0-2 1h0-2v1l-2 2-1-2v-1-1h-1c-1 1-1 0-1 1 1 1 1 2 2 3l-1 1c-1-2-2-4-4-5s-4-3-6-5h0v-1l-3-3h-1v-2c-2-1-3-2-4-3 1 0 2 1 3 1s1 0 2-1h2c1 1 2 1 3 0l1-1h1z" class="d"></path><path d="M327 288v2l-1-1v-1c-1 1-2 0-2 2h-1v1l-1 1-1-1-1-1c-1-1-1 0-1-1l1-1 1-1h1 2 0c1 0 2 1 3 1z" class="Q"></path><path d="M329 284l10 15c-1 0-1 0-2 1l-2-1v-1h0l-1-2c-1 0-2 1-3 2l-1-1c1-1 2-1 3-1v-1c0-1-1-2-2-3h0c-1-1-1-1-1-2v-1c-1 0-1-1-2-2h-1v1c-1 0-2-1-3-1l3-1c1 0 1-1 2-2z" class="U"></path><path d="M331 304l1-1c-1-1-1-2-2-3 0-1 0 0 1-1h1v1 1l1 2 2-2v-1h2 0c1-1 1-1 2-1l8 11c1 2 3 4 3 6-1 0-2 0-3 1h2 0 2l2 3 2 1v2 2 1h1l1-1 2 4c0 1 0 1 1 2v1h1v-1l1-1v4h2l4 5c0 1 1 1 1 2-2 1-4 1-5-1-1-1 0-1-1-2l-5-4c-3-4-6-7-9-10-1-1-2-3-4-4l-1-1c0-2-3-3-5-5-1-3-5-5-6-8-1-1-1-2-2-2z" class="D"></path><path d="M337 300c1-1 1-1 2-1l8 11c1 2 3 4 3 6-1 0-2 0-3 1h2 0 2l2 3c-1 0-1-1-2-1-1-1-1-1-2-1l-1 1v2h0 0c0-1-1-2-1-3v-1h0c-1-1-2-1-3-1h0l1-1c2-1 2 0 3 0h1l-1-1c-1-1-1-1-1-2-1-1-1 0-2-1h-1c1-1 0-2 0-3l-2 1-1-1h2v-1l-2-1h-2-1v-1h1c-1-1-1-1-2 0h-1l-2-1h0l-1-1 2-2v-1h2 0z" class="c"></path><path d="M335 301h0 2l1 1v1h2v1l-1 1c-1-1-1-1-2 0h-1l-2-1h0l-1-1 2-2zm-32-50l2-2-1-1v-1c-2 0-2-1-4-1 0-3 1-2 2-4l-1-1-2 1-1-1c1-1 2 0 3-1 0-2-1-4-1-5h-3l3-3c1 7 4 13 7 18 7 12 15 23 22 34-1 1-1 2-2 2v-1h-2v-1h-1l-1 1c-1 1-2 1-3 0h-2c-1 1-1 1-2 1s-2-1-3-1l-2-3h0v-2-2h-1v3 1h-1c0-2-2-3-3-5h0v-1-1l-1 1-1-1v-1l-1-2c-2 1 0 1-2 2v-1c0-1-1-2-1-2-1 0-1 0-1-1l1 1h2v-1c0-1 1 0 0-1h0l1-1-1-1c0-2 1-2 1-3 0 0-1 0-1-1h0c-1 0-2 0-3-1v-2l-1-1c1-1 1-1 1-2 1 0 1 0 1-1 0 0-1-1-2-1v-2h1c1 0 1 1 2 1 0 0 1-1 1-2h0l1-1z" class="L"></path><path d="M302 263l-1-1v-1h1v-2s1 0 1-1h1s0 1 1 1v1c1-1 1-1 2-1h3v1l1 1c0 1-1 1-2 2l-1-1c-2 1-3 2-3 4v1h0-1c0-1 0-2-1-3 0 0-1 0-1-1z" class="E"></path><path d="M303 251l2-2-1-1v-1c-2 0-2-1-4-1 0-3 1-2 2-4l-1-1-2 1-1-1c1-1 2 0 3-1 0-2-1-4-1-5h-3l3-3c1 7 4 13 7 18 7 12 15 23 22 34-1 1-1 2-2 2v-1h-2v-1l1-1-1-1h-1-1v-2c1-1-1-1-2-2l1-1v-1h-5c0 1 0 0-1 0s-2 0-2-1v-1l1-1h0c0-1 1-1 2-2l-1-1v-1h-3-1c2-1 2 0 2-2h-2-1c1-1 2-1 2-2l-1-1v-1h-1l-1 1-1-1c1-1 2-1 2-2l-1-1v-1h-3c-1 0-1 0-2 1v-1c-1 0-1-1-1-1h-1c0 1-1 1-1 1v2h-1v1l1 1h0c-1 0-2 0-3-1v-2l-1-1c1-1 1-1 1-2 1 0 1 0 1-1 0 0-1-1-2-1v-2h1c1 0 1 1 2 1 0 0 1-1 1-2h0l1-1z" class="c"></path><path d="M315 273c2 0 2-1 4 0v1c0 1-1 1-2 2 0 1 0 0-1 0s-2 0-2-1v-1l1-1zm-17-20h1c1 0 1 1 2 1 0 0 1-1 1-2h0l1-1h3 0v1c-1 0-1 0-2 1h2l1 1c-1 0-1 0-2 1h3c0 1 0 1 1 1-1 1-1 2-2 3-1 0-1 0-2 1v-1c-1 0-1-1-1-1h-1c0 1-1 1-1 1v2h-1v1l1 1h0c-1 0-2 0-3-1v-2l-1-1c1-1 1-1 1-2 1 0 1 0 1-1 0 0-1-1-2-1v-2z" class="Q"></path><path d="M297 218c0 3 1 6 2 10v1l1 3-3 3h3c0 1 1 3 1 5-1 1-2 0-3 1l1 1 2-1 1 1c-1 2-2 1-2 4 2 0 2 1 4 1v1l1 1-2 2-1 1h0c0 1-1 2-1 2-1 0-1-1-2-1h-1-2-3 0c-1 1-1 0-2 1l-1-1v1c-1 0-1 1-1 1h-1l1-2v-2h0v-1h-1 0v1c-1 1-1 1-2 1h-1v1c0 1 0 1-1 1l-2-2-3 2v-1c-1-2-1-4-1-6v-13c2-2 6-5 9-7 3-3 6-7 10-9z" class="L"></path><path d="M297 218c0 3 1 6 2 10v1h-3l2-2-1-1c-1 0-1 1-2 1 0-1 0-1-1-2l1-1c1 0 1 0 1 1h1l-1-2h-3l-1 1 1 1h0-2c-1 1-1 1 0 2l-1 1-1-1v1c0 1 1 1 2 2h0-1l-2-1c-1 1-1 3-1 4l-1-1-1 1 1 1-1 1h0c-1 1-1 1-3 0 0 1-1 2-1 3h0c2 2-2-1 1 1l2 1h0v1l-2-1-1 1c1 0 2 1 2 2v1l-2-1h0c1 2 2 2 3 3h0l-1 1c-1-1-1-2-2-1s0 3 1 4h0v-2c1 0 1 0 1 1l2-1c0 1-2 2-3 4l-3 2v-1c-1-2-1-4-1-6v-13c2-2 6-5 9-7 3-3 6-7 10-9z" class="N"></path><path d="M285 235h0l1-1-1-1 1-1 1 1c0-1 0-3 1-4l2 1h1 0c-1-1-2-1-2-2v-1l1 1 1-1c-1-1-1-1 0-2h2 0l-1-1 1-1h3l1 2h-1c0-1 0-1-1-1l-1 1c1 1 1 1 1 2 1 0 1-1 2-1l1 1-2 2h3l1 3-3 3h3c0 1 1 3 1 5-1 1-2 0-3 1l1 1 2-1 1 1c-1 2-2 1-2 4 2 0 2 1 4 1v1l1 1-2 2-1 1h0c0 1-1 2-1 2-1 0-1-1-2-1h-1-2-3 0c-1 1-1 0-2 1l-1-1v1c-1 0-1 1-1 1h-1l1-2v-2h0v-1h-1 0v1c-1 1-1 1-2 1v-2c1-2 2-3 3-5h3v1l1 1 2-2h0l-1-1v-2c0-1 0-1 1-2h0l1-1-2-2-2-1-1 1v1c-1 0-2-1-2-1-1-1-2-2-4-2z" class="d"></path><path d="M376 229c7-7 19-16 30-17 3 0 5 2 8 5h0l-1 2c-1 1-2 2-2 4 0 1 1 1 1 2h0c1 2 3 3 4 4 3 1 6 0 9-1l1 1-3 3h3c-9 4-16 9-23 17l-4 6c0-1 0-2-1-2l-4 1-2 3-1 5-2-1c-2 3-3 4-4 8h0c0 2-1 6-1 8-2 2-1 4-2 7 1 2 0 4 1 6 0 4 1 7 2 10 4 13 10 25 15 37l6 16c1 4 3 8 5 12 2 8 7 16 9 24 0 2 0 3 1 4 0 1 0 0 1 2 0 0 0 1 1 1l-1 1 2 5c1 1 1 2 2 3l5 12v1c0 1 1 2 1 3 1 1 1 3 2 5l5 12v1l1 1v1c1 2 0-1 0 1 1 3 3 6 4 9-3-2-6-3-9-5l-1-1c-1 0-2-1-3-1v-1c0-2 0-2-1-4 0 0-1-4-2-5-3-15-19-24-30-32l-1 1h1c-1 2-2 3-3 4-1-1-1-1-3-1h0c-3-1-6-2-9-2l-4-1-7-2c-2 0-4-1-6-2-2-3-4-6-7-9l-2-2c-3-2-6-6-8-9l-3-6c-1-3-2-7-3-10 0-2 0-6-1-7l-1 2c-1-2 0-7-1-10 0-2-2-4-2-6h0l1-1c-1-3-3-7-5-9-1-1-2-2-2-3-1-2-2-3-4-4h-1l-2-2c-1-4-9-11-12-13l1-2c-2 0-1 0-2-1v-1l-3-6h-4-4l-5 1v-1c4-2 8-3 12-3h1c-1-1-2-2-2-3h-1l-2-2v-1c-2-2-4-5-6-7h0l-1-2v-1c10 9 19 20 28 30l27 29c7 8 13 16 21 23 2 2 6 4 10 5l14 9-24-50c-3-10-8-19-9-29h-1v-4c-3-23-1-46 13-65z" class="a"></path><path d="M385 261c2-1 5-3 6-5 0-2 1-3 2-4h1c-1 2-2 3-2 5l-1 5-2-1c-2 3-3 4-4 8h0c0 2-1 6-1 8-2 2-1 4-2 7-1-7 1-16 3-23z" class="K"></path><defs><linearGradient id="Ab" x1="414.049" y1="232.862" x2="414.523" y2="242.61" xlink:href="#B"><stop offset="0" stop-color="#181817"></stop><stop offset="1" stop-color="#3c3c3d"></stop></linearGradient></defs><path fill="url(#Ab)" d="M385 261c1-3 3-7 5-10 1-2 3-4 5-6 1 0 0 0 1-1l1-1c1-1 2-1 3 0h0c-1 1-1 2-1 3 2-3 5-5 8-8 2-1 15-7 16-6h3c-9 4-16 9-23 17l-4 6c0-1 0-2-1-2l-4 1-2 3c0-2 1-3 2-5h-1c-1 1-2 2-2 4-1 2-4 4-6 5z"></path><path d="M394 254l3-5c1-2 2-2 4-2l2 2-4 6c0-1 0-2-1-2l-4 1z" class="b"></path><path d="M308 297h1c2 4 7 7 10 11 2 1 3 3 4 4 3 4 6 7 9 11l14 15 1 1c2 1 5 5 7 7l8 9c1 1 1 2 3 3 0 0 1 1 1 2l1 1v1l-4-4c-6-6-11-14-18-19l-2-3c0 1-1 2 0 3v2-1l-6-7-2-2-1 1c-1-1-2-2-2-3-1-2-2-3-4-4h-1l-2-2c-1-4-9-11-12-13l1-2c-2 0-1 0-2-1v-1l-3-6h-4-4l-5 1v-1c4-2 8-3 12-3z" class="V"></path><path d="M334 326c2 2 9 8 9 10 0 1-1 2 0 3v2-1l-6-7c-1-2-2-4-3-7z" class="T"></path><path d="M309 300c7 4 13 12 18 18-5-1-9-8-13-10-2 0-1 0-2-1v-1l-3-6z" class="U"></path><path d="M314 308c4 2 8 9 13 10l7 8c1 3 2 5 3 7l-2-2-1 1c-1-1-2-2-2-3-1-2-2-3-4-4h-1l-2-2c-1-4-9-11-12-13l1-2z" class="g"></path><path d="M334 332l1-1 2 2 6 7v1-2c-1-1 0-2 0-3l2 3c7 5 12 13 18 19l4 4h1c0 1 2 3 2 4l5 7c-1 0-1 0-2-1v1c-1 1-1 1-1 3l1 2c-1 0-1-1-2-1l-1 1 1 4h-1c-1-1-1-1-1-2l-1-1-3-6c0-1-1-2-2-3l-1-1c0-1-1-3-2-4-2-6-9-13-14-17l-2-3h-1c-2-1-4-2-5-3h0l1-1c-1-3-3-7-5-9z" class="R"></path><path d="M370 366l5 7c-1 0-1 0-2-1v1c-1 1-1 1-1 3l1 2c-1 0-1-1-2-1l-1 1 1 4h-1c-1-1-1-1-1-2 1-4-4-8-3-12l4 7c0-3-1-6 0-9z" class="M"></path><path d="M343 341v-2c-1-1 0-2 0-3l2 3c7 5 12 13 18 19l4 4h1c-2 2-1 2-2 4h0c-1-1-2-3-3-5l1 5c1 1 1 2 1 3-1-1-1 0-1-1l-5-10-3-3c-4-5-9-9-13-14z" class="G"></path><path d="M343 341v-2c-1-1 0-2 0-3l2 3c2 4 6 8 9 12 3 2 5 4 5 7l-3-3c-4-5-9-9-13-14z" class="c"></path><defs><linearGradient id="Ac" x1="346.917" y1="377.064" x2="400.454" y2="386.87" xlink:href="#B"><stop offset="0" stop-color="#adacab"></stop><stop offset="1" stop-color="#e6e6e6"></stop></linearGradient></defs><path fill="url(#Ac)" d="M338 342c1 1 3 2 5 3h1l2 3c5 4 12 11 14 17 1 1 2 3 2 4l1 1c1 1 2 2 2 3l3 6 1 1c0 1 0 1 1 2h1l-1-4 1-1c1 0 1 1 2 1l-1-2c0-2 0-2 1-3v-1c1 1 1 1 2 1 4 7 8 13 13 19 3 3 6 7 10 10l-1 1h1c-1 2-2 3-3 4-1-1-1-1-3-1h0c-3-1-6-2-9-2l-4-1-7-2c-2 0-4-1-6-2-2-3-4-6-7-9l-2-2c-3-2-6-6-8-9l-3-6c-1-3-2-7-3-10 0-2 0-6-1-7l-1 2c-1-2 0-7-1-10 0-2-2-4-2-6z"></path><path d="M379 403c1-1 1-1 1-2l1 1h1l1 2-4-1z" class="B"></path><path d="M362 386c1 0 2 1 3 2l5 6c-2-1-4-1-6-3h0c0-3-1-3-2-5h0z" class="D"></path><path d="M354 376c1 1 1 2 2 3 1 2 4 5 6 7h0c1 2 2 2 2 5h0 0c-3-3-7-7-9-11h0c-1-2-1-3-1-4zm5 14c2 1 3 1 4 1v1c4 3 10 4 15 5v1h0c-3-1-6-1-8-1h-1l1 1c0 1 1 2 2 3-2 0-4-1-6-2-2-3-4-6-7-9z" class="K"></path><path d="M373 373h0c0 2 0 3 1 3 1 1 1 1 1 2h0c-1 1-2 1-4 1 1 2 1 3 1 5-1 0-1 0-2-1 0 1 1 2 1 2l-1 1 1 1h1l2 4c-3-1-6-2-8-5-1-1 0 0 0-1l-1-1c0-1-1-1-1-2l-2-2 1-1 4 3c1 1 1 2 2 2l-1-4h-1l1-1 1 1c0 1 0 1 1 2h1l-1-4 1-1c1 0 1 1 2 1l-1-2c0-2 0-2 1-3z" class="P"></path><path d="M338 342c1 1 3 2 5 3h1l2 3c5 4 12 11 14 17 1 1 2 3 2 4l1 1c1 1 2 2 2 3l3 6-1 1h1l1 4c-1 0-1-1-2-2l-4-3-1 1 2 2c0 1 1 1 1 2h-1l-3-3v-1c-1-1-1-1-1-2h-1c0-1 0-2-1-3l-3-9c0-2 0-3-1-4l-2-2-1-3h0c0 4 3 10 1 13-2 0-2-1-3-2 0 1-1 2 0 3 1 0 1 1 1 1h0l1 1v1l1 1 1-1v-1c0 1 1 2 1 3s0 2 1 4h0c2 4 6 8 9 11h-1c-1 0-2 0-4-1l-2-2c-3-2-6-6-8-9l-3-6c-1-3-2-7-3-10 0-2 0-6-1-7l-1 2c-1-2 0-7-1-10 0-2-2-4-2-6z" class="D"></path><path d="M357 362l3 3c1 1 2 3 2 4l1 1c1 1 2 2 2 3l3 6-1 1h1l1 4c-1 0-1-1-2-2l-1-1c-2-4-4-7-6-10 0-1-1-1-1-2v-2-1c-1-1-1-2-2-4z" class="M"></path><path d="M349 368c0-1-1-3-1-4-1-3-2-7-1-10l4 3h0c0 4 3 10 1 13-2 0-2-1-3-2z" class="O"></path><path d="M338 342c1 1 3 2 5 3h1l2 3c5 4 12 11 14 17l-3-3h-1c-1-1-1-3-3-3 0-1-1-1-1-2-1-3-4-5-5-7h-1c-2 3-1 11 0 14v1c0 3 2 5 2 8l1 2v1c2 3 4 5 6 8l1 1c0 1 1 1 1 3-3-2-6-6-8-9l-3-6c-1-3-2-7-3-10 0-2 0-6-1-7l-1 2c-1-2 0-7-1-10 0-2-2-4-2-6z" class="K"></path><path d="M338 342c1 1 3 2 5 3h1l2 3-1 1h-1-1v2c1 1 0 2 1 3v1 4c0 3 1 6 1 9l1 2v3c-1-3-2-7-3-10 0-2 0-6-1-7l-1 2c-1-2 0-7-1-10 0-2-2-4-2-6z" class="b"></path><path d="M433 221c1-1 1-3 2-5v1l-1 4c0 2-1 8 0 9 0 2 1 1 0 3l-1 1c-3 6-8 11-11 18-1 2-2 4-2 7-2 4-4 8-5 12-1 3-1 6-1 9v4c1 8 4 14 6 21 5 11 10 22 13 33 2 4 4 8 5 12l10 24 30 75 17 42c2 5 5 10 7 16l10 26 2 3c1-1 1-1 1-3l33-74c0-1 1-4 2-6l8-18 30-70 11-26c2-4 3-9 5-12 5-14 14-28 16-43 0-7 0-12-2-19-2-4-4-7-6-11l-1-3c-1-6-9-16-15-19l-1-1h1l-1-1h1l1-1v-2h-1l1-3c4-1 3-4 4-7h1 0c1 3 2 5 1 8 0 2 0 3 1 4l2 3c2 0 5 1 7 2 2 0 3 1 4 2 2 1 4 2 5 3 6 4 10 9 13 15l1-2h-1v-1c0-1 1-1 2-1h1l2 1v-1c0-1 0-1-1-1l1-1 2 2c1 1 1 1 2 1 0 1 1 1 2 2 3 5 5 11 5 17 1 3 1 5 1 8-1 6-1 13-3 19-2 7-5 14-8 21l-20 46-25 59c-4 9-7 17-12 25v1l3-2c2-1 3-1 6-1l1 1h0c0 2-1 3 0 5-1 1-1 1-2 1-1-1-1-3-1-4v-2c-3 1-5 3-7 5-1 1-2 1-3 2-2 9-6 17-9 26l-26 62-4 6-2 6 1-1s1-1 1-2l1 1v1h-1c1 1 1 2 3 3 1-1 2-1 3-1l1 1c-1 4-5 9-7 13-4 8-6 15-8 24-1 3-2 6-3 10-1 7 0 16 1 24 1 3 2 7 3 11l-2 2-1 1v1c-4 2-9 6-13 8-3 1-5 0-7 1h-3c0-1 1-1 1-2l-2-3-6-3-2-1h-1l-6-5h1v-1h0c-1-2-2-3-3-5 3-10 3-20 2-30-4-16-7-31-17-45l4-3c-2-5-5-15-11-17-4-2-7-5-10-7-5-3-10-5-14-8-10-8-13-19-15-30 0-12 1-23 9-32-2-1-1-2-3-3 1-1 3-2 4-2-2-3-5-3-7-5v-2-1h-2-1 0v-4-3c1 2 1 2 1 4v1c1 0 2 1 3 1l1 1c3 2 6 3 9 5-1-3-3-6-4-9 0-2 1 1 0-1v-1l-1-1v-1l-5-12c-1-2-1-4-2-5 0-1-1-2-1-3v-1l-5-12c-1-1-1-2-2-3l-2-5 1-1c-1 0-1-1-1-1-1-2-1-1-1-2-1-1-1-2-1-4-2-8-7-16-9-24-2-4-4-8-5-12l-6-16c-5-12-11-24-15-37-1-3-2-6-2-10-1-2 0-4-1-6 1-3 0-5 2-7 0-2 1-6 1-8h0c1-4 2-5 4-8l2 1 1-5 2-3 4-1c1 0 1 1 1 2l4-6c7-8 14-13 23-17h-3l3-3-1-1c3-1 6-4 8-7z" class="G"></path><path d="M513 553h0c1-1 1-2 2-3h0c0-2 0-3 1-4 0-1 0 0 1-1h0 1l-4 9c-1-1-1 0-1-1z" class="J"></path><defs><linearGradient id="Ad" x1="399.793" y1="254.572" x2="389.75" y2="266.028" xlink:href="#B"><stop offset="0" stop-color="#9b999a"></stop><stop offset="1" stop-color="#d2d1d0"></stop></linearGradient></defs><path fill="url(#Ad)" d="M394 254l4-1c1 0 1 1 1 2-4 8-6 16-6 25 0-2-1-3 0-5v-3h0c1-1 0-2 0-3h-1c0-1-1-2-2-2l-1 5v-3c0-2 1-5 2-7l1-5 2-3z"></path><defs><linearGradient id="Ae" x1="537.645" y1="544.083" x2="515.712" y2="472.68" xlink:href="#B"><stop offset="0" stop-color="#717170"></stop><stop offset="1" stop-color="#bebdbc"></stop></linearGradient></defs><path fill="url(#Ae)" d="M548 459v1c0 1-2 5-2 6 0 2 2 3 2 5h1v-1l1-2h0c1-1 1-2 2-2l-34 79h-1 0c-1 1-1 0-1 1-1 1-1 2-1 4h0c-1 1-1 2-2 3h0c-1-2-1-4-2-6l-4-11-20-48v-1-3l-1-1 1-1c2 1 1 0 2 1v1c1 1 1 1 1 2 1 3 3 5 4 7v1c1 1 1 2 2 4 2 4 3 9 5 13h0c0 1 1 1 1 2s1 2 1 4v-6l-1-2c0-1 0-1-1-2h1l10 26 2 3c1-1 1-1 1-3l33-74z"></path><path d="M512 538c0-2-1-4 0-5l2 3c1-1 1-1 1-3 1 2 1 3 1 5 0 1-1 3-2 5l-2-5z" class="G"></path><path d="M503 517v-6l-1-2c0-1 0-1-1-2h1l10 26c-1 1 0 3 0 5l-1-2c-4-5-6-14-8-19z" class="J"></path><defs><linearGradient id="Af" x1="656.832" y1="313.684" x2="508.196" y2="408.798" xlink:href="#B"><stop offset="0" stop-color="#959494"></stop><stop offset="1" stop-color="#c3c3c2"></stop></linearGradient></defs><path fill="url(#Af)" d="M602 217h0c1 3 2 5 1 8 0 2 0 3 1 4l2 3c-2-1-4-1-6-1 13 11 25 26 26 43 1 6 1 12 0 18h-1c-1 6-4 12-6 18l-11 24-47 110-7 17c-1 1-2 4-2 5-1 0-1 1-2 2h0l-1 2v1h-1c0-2-2-3-2-5 0-1 2-5 2-6v-1c0-1 1-4 2-6l8-18 30-70 11-26c2-4 3-9 5-12 5-14 14-28 16-43 0-7 0-12-2-19-2-4-4-7-6-11l-1-3c-1-6-9-16-15-19l-1-1h1l-1-1h1l1-1v-2h-1l1-3c4-1 3-4 4-7h1z"></path><path d="M602 217h0c-1 1-1 2-1 4s-1 3-2 4c0 2 0 3 1 5l-2 1c3 2 6 4 8 6l3 6c2 3 4 5 5 9l3 6c0 1 1 3 1 5v2c-2-4-4-7-6-11l-1-3c-1-6-9-16-15-19l-1-1h1l-1-1h1l1-1v-2h-1l1-3c4-1 3-4 4-7h1z" class="F"></path><path d="M620 284c0-1 0-2 1-3v2h1c0 4 0 8-1 13h0c-2 5-4 10-7 15-1 2-2 5-3 8 0-2 0-1 1-2v-2-1-1c-1 2-2 4-3 5s-1 2-2 3c0 2-1 5-2 6h-1c5-14 14-28 16-43z" class="Y"></path><defs><linearGradient id="Ag" x1="482.682" y1="371.503" x2="439.851" y2="392.982" xlink:href="#B"><stop offset="0" stop-color="#8d8c8c"></stop><stop offset="1" stop-color="#bfbfbe"></stop></linearGradient></defs><path fill="url(#Ag)" d="M487 488l-17-41-31-77-19-48c-4-10-9-19-11-29-2-7-2-16 0-23 2-11 8-19 15-27 2-4 5-7 9-9-3 6-8 11-11 18-1 2-2 4-2 7-2 4-4 8-5 12-1 3-1 6-1 9v4c1 8 4 14 6 21 5 11 10 22 13 33 2 4 4 8 5 12l10 24 30 75 17 42c2 5 5 10 7 16h-1c1 1 1 1 1 2l1 2v6c0-2-1-3-1-4s-1-1-1-2h0c-2-4-3-9-5-13-1-2-1-3-2-4v-1c-1-2-3-4-4-7 0-1 0-1-1-2v-1c-1-1 0 0-2-1l-1 1 1 1v3 1z"></path><defs><linearGradient id="Ah" x1="428.617" y1="305.018" x2="416.822" y2="323.579" xlink:href="#B"><stop offset="0" stop-color="#7e797c"></stop><stop offset="1" stop-color="#909390"></stop></linearGradient></defs><path fill="url(#Ah)" d="M414 284c1 8 4 14 6 21 5 11 10 22 13 33l-1-1v-1l-1 1v3c-4-8-7-17-10-26-3-7-7-15-8-23 0-2 0-4 1-7z"></path><path d="M636 252h-1v-1c0-1 1-1 2-1h1l2 1v-1c0-1 0-1-1-1l1-1 2 2c1 1 1 1 2 1 0 1 1 1 2 2 3 5 5 11 5 17 1 3 1 5 1 8-1 6-1 13-3 19-2 7-5 14-8 21l-20 46-25 59c-4 9-7 17-12 25v1l3-2c2-1 3-1 6-1l1 1h0c0 2-1 3 0 5-1 1-1 1-2 1-1-1-1-3-1-4v-2c-3 1-5 3-7 5-1 1-2 1-3 2-2 9-6 17-9 26l-26 62-4 6-2 6 1-1s1-1 1-2l1 1v1h-1c1 1 1 2 3 3 1-1 2-1 3-1l1 1c-1 4-5 9-7 13-4 8-6 15-8 24-1 3-2 6-3 10-1 7 0 16 1 24 1 3 2 7 3 11l-2 2-1 1v1c-4 2-9 6-13 8-3 1-5 0-7 1h-3c0-1 1-1 1-2l-2-3-6-3-2-1h-1l-6-5h1v-1h0c-1-2-2-3-3-5 3-10 3-20 2-30-4-16-7-31-17-45l4-3c-2-5-5-15-11-17-4-2-7-5-10-7-5-3-10-5-14-8-10-8-13-19-15-30 0-12 1-23 9-32-2-1-1-2-3-3 1-1 3-2 4-2-2-3-5-3-7-5v-2-1h-2-1 0v-4-3c1 2 1 2 1 4v1c1 0 2 1 3 1l1 1c3 2 6 3 9 5-1-3-3-6-4-9 0-2 1 1 0-1v-1l-1-1v-1l-5-12c-1-2-1-4-2-5 0-1-1-2-1-3v-1l-5-12c-1-1-1-2-2-3l-2-5 1-1c-1 0-1-1-1-1-1-2-1-1-1-2-1-1-1-2-1-4-2-8-7-16-9-24-2-4-4-8-5-12l-6-16c-5-12-11-24-15-37-1-3-2-6-2-10-1-2 0-4-1-6 1-3 0-5 2-7 0-2 1-6 1-8h0c1-4 2-5 4-8l2 1c-1 2-2 5-2 7v3l1-5c1 0 2 1 2 2h1c0 1 1 2 0 3h0v3c-1 2 0 3 0 5 0 4 0 8 1 11 4 15 11 30 17 44l32 80 42 105 16 39 12 30 80-186 30-70 10-25c2-4 4-8 5-13 3-8 3-16 3-25-2-6-3-11-6-16l1-2z" class="Q"></path><defs><linearGradient id="Ai" x1="650.612" y1="266.128" x2="643.524" y2="284.03" xlink:href="#B"><stop offset="0" stop-color="#afadaf"></stop><stop offset="1" stop-color="#d8d8d6"></stop></linearGradient></defs><path fill="url(#Ai)" d="M645 265c1-1 2-1 3-2 0 2 1 4 1 6 1 3 1 6 3 9-1 6-1 13-3 19l-2-1v-1c1-2 1-2 1-3h0v-3h0c1-1 1-1 1-2l-3-6h0c-1 1 0 5-1 7 0 2 0 3-1 4v-1c1-4 1-7 1-10 0-4 1-9 0-14v-2z"></path><path d="M636 252h-1v-1c0-1 1-1 2-1h1l2 1v-1c0-1 0-1-1-1l1-1 2 2c1 1 1 1 2 1 0 1 1 1 2 2 3 5 5 11 5 17 1 3 1 5 1 8-2-3-2-6-3-9 0-2-1-4-1-6-1 1-2 1-3 2v2c-1-1-1-1-1-2v-1c-1-1-1-1-1-2l-3 1c0 1 0 2 1 2v5c-2-6-3-11-6-16l1-2z" class="R"></path><path d="M642 256l1-2-1-1h1s1 0 1 1c1 2 2 4 2 5-1 0-2 1-3 1l-1-4z" class="Y"></path><path d="M636 252h4l2 4 1 4c1 0 2-1 3-1l2 4c-1 1-2 1-3 2v2c-1-1-1-1-1-2v-1c-1-1-1-1-1-2l-3 1c0 1 0 2 1 2v5c-2-6-3-11-6-16l1-2z" class="I"></path><path d="M643 260c1 0 2-1 3-1l2 4c-1 1-2 1-3 2l-2-5z" class="K"></path><path d="M584 448v1l3-2c2-1 3-1 6-1l1 1h0c0 2-1 3 0 5-1 1-1 1-2 1-1-1-1-3-1-4v-2c-3 1-5 3-7 5-1 1-2 1-3 2-2 9-6 17-9 26l-26 62-4 6c0-4 1-6 3-10 0-1 0-2 1-4l16-37 9-21 2-5 2-5c1-3 2-8 4-12 2-2 3-4 5-6z" class="O"></path><path d="M391 262c-1 2-2 5-2 7v3c0 2 1 4 0 6s0 4 0 5c1 5 1 11 3 16l36 90 7 16 1 5c1 3 3 5 4 8v1 1c1 4 3 7 5 11 1 5 4 10 6 15 0 1 1 3 1 4l4 11 1 1 7 18 6 14c1 1 1 3 2 4l5 13c0 1 1 3 2 5 0 1 1 3 2 5 3 8 8 16 10 25v1s1 1 1 2h0l1 5c1 2 3 5 4 7l3 6c1 4 3 9 5 13 1 1 1 3 2 4 1 3 2 5 3 7v1c1 1 1 3 1 4l-1 1c0 1 0 2 1 3-1 1-1 2 0 3v2c-2-2-3-5-4-8l-7-16-1-1c0-1-1-2-1-2 0-1-1-1-2-2l-33-81c-1-1-1-3-2-5l-6-16-23-55-7-18c-1-4-3-8-5-12-2-8-7-16-9-24-2-4-4-8-5-12l-6-16c-5-12-11-24-15-37-1-3-2-6-2-10-1-2 0-4-1-6 1-3 0-5 2-7 0-2 1-6 1-8h0c1-4 2-5 4-8l2 1z" class="E"></path><path d="M391 262c-1 2-2 5-2 7 0 1-1 2-1 3v-2-1-1h1-1l-1 1h-2 0c1-4 2-5 4-8l2 1z" class="D"></path><path d="M463 495c2 1 3 1 3 3v1l1 2 1 1c0 1 1 3 2 5v2l4 8v2c1 1 1 1 1 2 2 4 4 9 6 13 2 3 4 8 5 11 0 1-1 2 0 3 1 2 1 3 3 3l7 19c1 3 3 7 4 11l-1-1c0-1-1-2-1-2 0-1-1-1-2-2l-33-81z" class="B"></path><path d="M385 269h2c-1 2-1 3-1 5l-1 1v4 4 12c1 3 2 7 4 10l2 7c1 1 1 1 1 2l2 4v2l2 5 1 3 1 2 3 7v1h1c0 2 1 3 1 4h0c1 2 2 4 2 6h1c1 2 1 3 1 5l2 3 1 3v3l1 1v2c-2-4-4-8-5-12l-6-16c-5-12-11-24-15-37-1-3-2-6-2-10-1-2 0-4-1-6 1-3 0-5 2-7 0-2 1-6 1-8z" class="O"></path><path d="M382 284c1-3 0-5 2-7 0 3 0 6-1 9v3 1c-1-2 0-4-1-6z" class="P"></path><path d="M420 389c2 4 4 8 5 12l7 18 23 55 6 16c1 2 1 4 2 5l33 81c1 1 2 1 2 2 0 0 1 1 1 2l1 1 7 16c1 3 2 6 4 8 0 1 1 2 1 2l1-1c1-1 1-1 1-2h-1c0-1 6-10 7-12s2-4 3-7c1-2 2-5 3-7l1-1c0-1 0-2 1-2l1-2v-1l2-1c1-2 2-3 3-6h-1c0-1 0-1 1-2 0-1 0-3 1-4l1-1v-2c1-1 1-1 1-2l1-1v-1c1-3 2-5 3-7v-1c1-1 1-1 1-2l1-1v-1c0-2 1-3 3-6-1 2-1 3-1 4-2 4-3 6-3 10l-2 6 1-1s1-1 1-2l1 1v1h-1c1 1 1 2 3 3 1-1 2-1 3-1l1 1c-1 4-5 9-7 13-4 8-6 15-8 24-1 3-2 6-3 10-1 7 0 16 1 24 1 3 2 7 3 11l-2 2-1 1v1c-4 2-9 6-13 8-3 1-5 0-7 1h-3c0-1 1-1 1-2l-2-3-6-3-2-1h-1l-6-5h1v-1h0c-1-2-2-3-3-5 3-10 3-20 2-30-4-16-7-31-17-45l4-3c-2-5-5-15-11-17-4-2-7-5-10-7-5-3-10-5-14-8-10-8-13-19-15-30 0-12 1-23 9-32-2-1-1-2-3-3 1-1 3-2 4-2-2-3-5-3-7-5v-2-1h-2-1 0v-4-3c1 2 1 2 1 4v1c1 0 2 1 3 1l1 1c3 2 6 3 9 5-1-3-3-6-4-9 0-2 1 1 0-1v-1l-1-1v-1l-5-12c-1-2-1-4-2-5 0-1-1-2-1-3v-1l-5-12c-1-1-1-2-2-3l-2-5 1-1c-1 0-1-1-1-1-1-2-1-1-1-2-1-1-1-2-1-4z" class="f"></path><path d="M442 454h1l-4 5c-2-1-1-2-3-3 1-1 3-2 4-2h2z" class="O"></path><path d="M433 446c4 2 9 4 12 7-1 1-1 1-2 1h-1c-3-4-5-5-9-7v-1z" class="E"></path><path d="M433 447c4 2 6 3 9 7h-2c-2-3-5-3-7-5v-2z" class="T"></path><path d="M525 615c1 2 1 4 1 6l-3 8-5 10c-1 1-2 2-4 3l11-27z" class="I"></path><path d="M525 615l6-12c-1 5-2 10-2 15l1 10c0 2 2 7 1 9l-3 3h-1c-3 2-7 3-9 5l-1-1 7-15h-1c1-2 2-5 3-8 0-2 0-4-1-6z" class="K"></path><path d="M524 629c1-1 1-2 2-4h1c1 3 0 4 0 7 0 1 1 4 0 6v1 1c-3 2-7 3-9 5l-1-1 7-15z" class="B"></path><path d="M546 534c-1 2-1 3-1 4-2 4-3 6-3 10l-2 6-20 45c-2 5-4 11-7 16-4-8-7-16-11-25-2-4-4-9-6-14 1 1 2 1 2 2 0 0 1 1 1 2l1 1 7 16c1 3 2 6 4 8 0 1 1 2 1 2l1-1c1-1 1-1 1-2h-1c0-1 6-10 7-12s2-4 3-7c1-2 2-5 3-7l1-1c0-1 0-2 1-2l1-2v-1l2-1c1-2 2-3 3-6h-1c0-1 0-1 1-2 0-1 0-3 1-4l1-1v-2c1-1 1-1 1-2l1-1v-1c1-3 2-5 3-7v-1c1-1 1-1 1-2l1-1v-1c0-2 1-3 3-6zm-53 67c1 1 2 2 2 4l14 32c1 3 3 6 3 9 1-1 2-2 2-4 2-1 3-2 4-3l5-10h1l-7 15 1 1-2 3c1 1 1 2 3 2-3 1-5 0-7 1h-3c0-1 1-1 1-2l-2-3-6-3-2-1h-1l-6-5h1v-1h0c-1-2-2-3-3-5 3-10 3-20 2-30z" class="C"></path><path d="M500 642v-1l-2-1v-3c1-2 2-7 1-9h0v-1h1l1 1v1l1 1c1 5 3 11 6 15v1l-6-3-2-1z" class="W"></path><path d="M514 642c2-1 3-2 4-3l5-10h1l-7 15 1 1-2 3c1 1 1 2 3 2-3 1-5 0-7 1h-3c0-1 1-1 1-2l-2-3v-1c-3-4-5-10-6-15 1 1 1 1 2 3v1c1 1 1 2 1 2 1 1 1 1 1 2 1 3 3 6 5 8 1 1 0 1 1 0s2-2 2-4z" class="D"></path><path d="M493 601c1 1 2 2 2 4h0c0 2 0 3 1 4v2l4 14-4-8c0 3 1 6 1 8s-1 3-1 4v4l-1 1c0 1 0 1-1 2h0c-1-2-2-3-3-5 3-10 3-20 2-30z" class="c"></path><path d="M395 537l2 1h0l2 1v2h0c1 0 1 2 2 2 1 1 1 2 2 3l3 3v1c1 2 2 4 4 5h1l-1 1 3 4c1 1 1 2 1 3l8 15c1 1 4 3 3 4l-1 1h0l-1 1c1 2 2 4 4 6h1v1l13 9 3 3c2 1 4 2 5 4h-4c-1 1-1 2-1 3l3 8v1c2 3 4 6 7 8h-1 0-1c-4 1-10 9-12 13-3 5-5 11-4 17 1 3 4 7 7 9 3 1 6 1 9 1l1 1h1c2 1 4 0 5 0 2-2 6-3 9-4h-1c-1 1-3 2-4 3l-1 1c1 1 1 1 3 2 3-2 6-3 9-5 4-3 8-8 11-11l3-3c1 0 1 0 1-1v-1c2 1 3 1 4 2l-1-1c-1-2-3-3-3-5l2-2v-1l10 6c0-1-1-2-1-3l2-2 6 3 2 3c0 1-1 1-1 2h3c2-1 4 0 7-1 4-2 9-6 13-8h1c1 0 1 1 1 1v2 1h1c0 2-4 4-6 5h1c1 0 1 0 2-1h4l2 1c1 1 4 4 5 6v1c2 2 5 5 8 6 2 2 4 3 7 5l1 1 6-4c4 1 9 2 14 1 3-1 5-3 7-6 3-5 3-12 1-18s-8-13-14-16l3-3 1 1c0-1 2-3 2-3 0-1-1-2-1-2 0-2 2-7 2-9v-4h2l1-1-4-2 2-2c5-4 11-8 17-12 2-2 4-4 5-7v-2c0-1 1-3 2-4v-2h1c0-1 0-2 1-3h1c1-1 1-2 1-2l1-1-1-1 3-6 1-3h1v-2c1-1 1-1 1-3h1c2-1 2-3 3-5l1-1 2 2c1-1 2-4 3-6 0-1 1-1 1-2l1-2c2 0 2 2 4 2 1 5 1 10 0 15-1 2-2 6-2 8l1 1h1 1c3 1 6 1 9 2h2l1 1-1 2c-1 3-3 6-4 9l-7 16c0 1-1 3-1 4l-21 50-37 89-14 35c-1 2-3 6-3 8l-12 32-24 60-8 21-1 1-11-31c0-1-1-3-2-4l-4-12-11-29c-2-4-3-9-5-13 0-1-1-3-1-4l-5-12-82-208-4-8-1-4-2-3h0l9-2h1l2-1c1-1 1-2 0-3h-1c0-1-1-2-2-3v-1c-1-1-2-2-2-3l-3-4h0v-2h1c1-1 1-3 1-4v-1c0-2 2-3 3-4s2-3 3-3z" class="f"></path><path d="M391 544l3-2c0 2-1 3-1 5l-2 1v-4z" class="K"></path><path d="M395 537l2 1h0c-1 1-2 2-3 4l-3 2 1-4c1-1 2-3 3-3z" class="M"></path><path d="M389 545v-1c0-2 2-3 3-4l-1 4v4l2-1c0 3 0 6 1 8 0 3 1 5 1 7h-1c0-1-1-2-2-3v-1c-1-1-2-2-2-3l-3-4h0v-2h1c1-1 1-3 1-4z" class="J"></path><path d="M392 558v-1-3l1 2 1-1c0 3 1 5 1 7h-1c0-1-1-2-2-3v-1z" class="U"></path><path d="M389 545v-1c0-2 2-3 3-4l-1 4v4c0 1 0 3-1 4 1 1 1 1 1 3h-1l-3-4h0v-2h1c1-1 1-3 1-4z" class="D"></path><path d="M619 549l1-1 2 2-35 77-2-1c-1-2 0-3 1-5 1-3 2-7 4-10s4-7 4-10c1-4 4-7 4-11 2-2 4-4 5-7v-2c0-1 1-3 2-4v-2h1c0-1 0-2 1-3h1c1-1 1-2 1-2l1-1-1-1 3-6 1-3h1v-2c1-1 1-1 1-3h1c2-1 2-3 3-5z" class="C"></path><path d="M581 602c5-4 11-8 17-12 0 4-3 7-4 11 0 3-2 7-4 10s-3 7-4 10c-1 2-2 3-1 5l2 1c-1 1-2 3-3 5h-1c-1 0-2-2-3-3s-1-2-3-4h0c0-1 2-3 2-3 0-1-1-2-1-2 0-2 2-7 2-9v-4h2l1-1-4-2 2-2z" class="D"></path><path d="M584 614c0 2 0 4 1 6-1 2-1 3-2 5v-1c0-3 0-6 1-10z" class="O"></path><path d="M580 607h2c0 2 0 3 1 5 0 1 0 1-1 3 0-2-1-3-2-4v-4z" class="b"></path><path d="M579 604l2-2v1c1 1 2 1 4 1 1 2 0 3 1 4v7l2-2-3 7c-1-2-1-4-1-6v-1c0-2 1-3 0-4 0-2 0-2-1-3l-4-2z" class="C"></path><path d="M580 611c1 1 2 2 2 4-1 2-1 3-1 5s0 4 1 6h0l1 1c-1 2-1 2 0 4l1 1h-1c-1 0-2-2-3-3s-1-2-3-4h0c0-1 2-3 2-3 0-1-1-2-1-2 0-2 2-7 2-9z" class="R"></path><path d="M581 602c5-4 11-8 17-12 0 4-3 7-4 11l-6 12-2 2v-7c-1-1 0-2-1-4-2 0-3 0-4-1v-1z" class="E"></path><path d="M401 543c1 1 1 2 2 3l3 3v1c1 2 2 4 4 5h1l-1 1 3 4c1 1 1 2 1 3l8 15c1 1 4 3 3 4l-1 1h0l-1 1c1 2 2 4 4 6h1v1l13 9 3 3c2 1 4 2 5 4h-4c-1 1-1 2-1 3l3 8v1c-1 2 0 4 0 6-1 2-1 4-3 5v1l-1 1-1-1-3-4-22-49c-5-11-12-23-16-35z" class="I"></path><path d="M428 591l13 9 3 3c-1 1-2 3-3 4h-1c0 2 0 2-1 4v2c-1 0-1 0-1-1-1-1-1-2-2-4 0-1 0-2-1-3h0l-4-7-2-3v-1c-1-1-1-2-1-3z" class="E"></path><path d="M444 603c2 1 4 2 5 4h-4c-1 1-1 2-1 3l3 8v1c-1 2 0 4 0 6-1 2-1 4-3 5v1l-1 1-1-1-3-4 1-1c1-5-3-13-5-17l1-1c1 2 1 3 2 4 0 1 0 1 1 1v-2c1-2 1-2 1-4h1c1-1 2-3 3-4z" class="R"></path><path d="M444 610l3 8v1c-1 2 0 4 0 6-1 2-1 4-3 5v1l-1 1-1-1h0v-5c1-1 2-2 2-4 1-3-4-7-2-11 1 0 1 0 2-1z" class="c"></path><path d="M532 642h1c1 0 1 1 1 1v2 1h1c0 2-4 4-6 5h1c1 0 1 0 2-1h4l2 1c1 1 4 4 5 6v1c2 2 5 5 8 6 2 2 4 3 7 5l1 1-6 5v3h0l1-1h2c2-1 5-4 7-4 1 2 0 3 0 5 1-2 2-2 4-3v1c-4 11-9 21-13 32l-27 64-9 20c-1 4-3 11-6 14l-34-83-14-33c-2-6-4-11-6-16l1 1 1-1c2-1 3-1 5 0l3-1-3-3c3-2 6-3 9-5 4-3 8-8 11-11l3-3c1 0 1 0 1-1v-1c2 1 3 1 4 2l-1-1c-1-2-3-3-3-5l2-2v-1l10 6c0-1-1-2-1-3l2-2 6 3 2 3c0 1-1 1-1 2h3c2-1 4 0 7-1 4-2 9-6 13-8z" class="L"></path><path d="M506 742l-8-10v-1c3 3 6 7 10 10v1h-2z" class="I"></path><path d="M519 764h1c-1 3-2 6-4 7-1 1-3 1-4 1-2-1-2-2-3-3l-1-3v-1l2 2c1 2 3 2 4 1 2-1 4-2 5-4zm-47-88l11 9c2 1 5 3 6 5 0 0-1 0-2-1l-2-1h0c3 3 6 5 9 8l-1 1c-1 0-4-4-6-5l-16-15 1-1z" class="N"></path><path d="M501 748l11 9c3-2 6-3 8-5l2-2-1 2c-1 2-3 4-5 6-1 1-2 2-3 2-5-2-9-8-12-12z" class="G"></path><path d="M472 696c0-2 0-3-1-5v-1h0l1 2c2-4-6-10-6-15h1v1c1 3 3 5 5 8h-1l3 6 5 21h-1c-1-1-1-1-1-2-1-2-2-3-2-5 0-1-1-3-1-4-1-1-2-3-2-4l-1-1 1-1z" class="B"></path><path d="M468 673h1l3 3-1 1-2-1-2 1h-1c0 5 8 11 6 15l-1-2h0v1c1 2 1 3 1 5l-2-3-8-16h0c-1-1-2-1-2-2h-1l1-1c2-1 3-1 5 0l3-1z" class="C"></path><path d="M468 673h1l3 3-1 1-2-1c-1-1-3-1-4-2l3-1z" class="V"></path><defs><linearGradient id="Aj" x1="541.277" y1="698.547" x2="523.545" y2="711.714" xlink:href="#B"><stop offset="0" stop-color="#323231"></stop><stop offset="1" stop-color="#5c5b5a"></stop></linearGradient></defs><path fill="url(#Aj)" d="M527 708l22-18c-2 2-4 5-6 7-5 5-10 9-14 14l-12 12c-1 1-2 2-3 2-3 0-5-1-6-2l-4-4c3 1 5 3 6 4 3-1 5-2 7-4-1 0-2 0-2-1h2c4-3 7-7 10-10z"></path><path d="M517 718c2 0 2-1 4-2h0c-2 3-4 7-7 8-1 0-3 0-4-1 3-1 5-2 7-4-1 0-2 0-2-1h2z" class="F"></path><path d="M504 719l-1-2-2-2c-3-1-6-5-8-7v-3c-2 0-3-1-4-2-2-3-5-5-7-8v-1l1 2c1 0 2 1 3 2 1 0 2 1 2 2 2 0 2 1 3 2 2 1 4 2 4 4l5 5c2 1 0-1 2 1 1 1 3 3 5 4h1l1 1c1 0 3 1 4 1v-2l-1-1 1-1h2 2c1-1 2-2 3-2 2-1 2-2 3-2l1-1c1 0 2-1 3-1-3 3-6 7-10 10h-2c0 1 1 1 2 1-2 2-4 3-7 4-1-1-3-3-6-4z" class="O"></path><path d="M508 741l1-1c-2-2-4-4-6-7l-7-8h1l1-1c1 0 1 1 2 1l1 2 1 1c2 0 2 1 3 2s2 1 3 2l1 1c1 0 1 1 2 1 2 0 3 0 5-1l2-1c1-1 2-1 2-2h1l2-2 3-2v1c0 1 0 1-1 2l-2 2c-1 2-5 6-8 6h-1l-2 1c-2 0-2-1-4-2h-1v1c2 2 4 4 7 5 2-1 3-2 5-3v-1l7-7 1 1c-1 1-2 2-3 4s-4 3-5 5c2-1 3-2 5-2-1 2-3 7-5 8h-1 0c-2 1-3 2-4 2h-1 0c-2 0-1 0-2-1h-1c-1-1-2-1-3-2l-1-1c-1 0-1-1-2-1l1-1h1v-1h2v-1z" class="C"></path><path d="M553 675v3h0l1-1h2c-7 5-12 10-18 15-8 7-14 16-24 18-9-2-15-7-21-13l1-1c-3-3-6-5-9-8h0l2 1c1 1 2 1 2 1 2 1 3 2 4 3 6 5 13 12 20 13 2 0 5-1 7-3 5-3 10-8 15-12l18-16z" class="Z"></path><path d="M532 642h1c1 0 1 1 1 1v2 1h1c0 2-4 4-6 5h1c1 0 1 0 2-1h4l2 1c1 1 4 4 5 6v1c2 2 5 5 8 6 2 2 4 3 7 5l1 1-6 5-18 16c-5 4-10 9-15 12-2 2-5 3-7 3-7-1-14-8-20-13-1-1-2-2-4-3-1-2-4-4-6-5l-11-9-3-3h-1l-3-3c3-2 6-3 9-5 4-3 8-8 11-11l3-3c1 0 1 0 1-1v-1c2 1 3 1 4 2l-1-1c-1-2-3-3-3-5l2-2v-1l10 6c0-1-1-2-1-3l2-2 6 3 2 3c0 1-1 1-1 2h3c2-1 4 0 7-1 4-2 9-6 13-8z" class="E"></path><path d="M506 685c-1-1-1-1-1-2h1l-1-1c0-1 0-2 1-3 1 2 2 4 3 5v2h-1s-1 0-1-1h-1z" class="C"></path><path d="M504 690c2 2 4 4 7 5l1 1-4 1-3-3-2-2 1-2z" class="N"></path><path d="M517 683v1c0 1-1 2-1 3l-2 3-1 1-2 1c-2-2-3-5-5-7h1c0 1 1 1 1 1h1c1 0 3 2 4 3l4-6z" class="H"></path><path d="M511 695h3c9-2 9-12 14-18v-1l-2 10c-2 5-3 8-9 11-3 1-6 1-9 0l4-1-1-1z" class="G"></path><path d="M517 669c1 0 1 0 3-1l1-1h1l2-1c0 1 0 1-1 3-1 1-1 4-2 6l-1 2-3 6-4 6c-1-1-3-3-4-3v-2c-1-1-2-3-3-5l-1-2-1-2v-1l-1-2c0-1 0-1-1-2v-3l1 1h3 0l1 1c1-1 2 0 4 1h0c2 0 3 0 5-1h1z" class="Q"></path><path d="M532 642h1c1 0 1 1 1 1v2 1h1c0 2-4 4-6 5h1c1 0 1 0 2-1h4l2 1c1 1 4 4 5 6v1c2 2 5 5 8 6 2 2 4 3 7 5h-3l-2-2h-1c-2-1-2 0-2-1-2 0-3-2-4-3l-3-3h-3l-1-1 1-1c-2-2-2-2-4-2-1-1-1-1-1-2-2 1-4 3-6 4l-1 1-1 1c-2 1-3 4-6 5-1 1-2 2-4 3v1h-1c-2 1-3 1-5 1v-1l-1-1c-1-1-3-6-2-8h2c-1 0-3 0-4-1-1 0 0 0-1 1l2 2-1 1-4-1v1l-1 1-3-4c-2-1-1 0-2-1l-4-3c0-1 0-1-1-1v-1l-1-1c-2 0-4 1-5 1l3-3c1 0 1 0 1-1v-1c2 1 3 1 4 2l-1-1c-1-2-3-3-3-5l2-2v-1l10 6c0-1-1-2-1-3l2-2 6 3 2 3c0 1-1 1-1 2h3c2-1 4 0 7-1 4-2 9-6 13-8z" class="O"></path><path d="M529 651h1c1 0 1 0 2-1h4l2 1c1 1 4 4 5 6v1h-1c-1-1-2-3-4-4-1-1-3-1-4-1-4 0-8 6-12 6h-1l1-2v-1c-2 1-2 1-4 1 4-2 7-4 11-6z" class="D"></path><path d="M518 658h2c0 3 0 4-1 6-1 1-1 1-1 2l-2 2v1c-2 1-3 1-5 1v-1l-1-1c-1-1-3-6-2-8h2c3-1 6-1 8-2z" class="Q"></path><path d="M518 658h2c0 3 0 4-1 6-1 1-1 1-1 2l-2 2v1c-2 1-3 1-5 1v-1c2 0 3 0 4-1 0-4 3-6 3-10z" class="B"></path><defs><linearGradient id="Ak" x1="511.366" y1="644.3" x2="512.729" y2="657.424" xlink:href="#B"><stop offset="0" stop-color="#bcbcbb"></stop><stop offset="1" stop-color="#e5e5e4"></stop></linearGradient></defs><path fill="url(#Ak)" d="M532 642h1c1 0 1 1 1 1v2 1h1c0 2-4 4-6 5-4 2-7 4-11 6h0c-1 0-3 1-4 1-5 1-9 0-13-2-1-1-3-2-4-3h-1l-1-1c-1-1-1-1-2-1l-1-1c-1-2-3-3-3-5l2-2v-1l10 6c0-1-1-2-1-3l2-2 6 3 2 3c0 1-1 1-1 2h3c2-1 4 0 7-1 4-2 9-6 13-8z"></path><path d="M501 648c0-1-1-2-1-3l2-2 6 3 2 3c0 1-1 1-1 2-3 0-5-1-8-3z" class="a"></path><path d="M485 654c1 0 3-1 5-1l1 1v1c1 0 1 0 1 1h0l-2-1c0 1-1 1-1 2-1 0-1 1-2 1-1 1-1 1-1 2 1 2 2 3 3 4s0 0 0 1c1 1 3 2 4 3 2 1 6 5 7 8 1 1 1 2 1 3v1 3c1 2 2 5 3 7l-1 2 2 2 3 3c3 1 6 1 9 0 6-3 7-6 9-11l2-10c0-1 1-2 2-3v-1c1-2 2-3 3-4 1-3 4-7 6-9l1 1h3l3 3c1 1 2 3 4 3 0 1 0 0 2 1h1l2 2h3l1 1-6 5-18 16c-5 4-10 9-15 12-2 2-5 3-7 3-7-1-14-8-20-13-1-1-2-2-4-3-1-2-4-4-6-5l-11-9-3-3h-1l-3-3c3-2 6-3 9-5 4-3 8-8 11-11z" class="L"></path><path d="M533 668c1-3 4-7 6-9l1 1h3l3 3c1 1 2 3 4 3 0 1 0 0 2 1h1l2 2h3l1 1-6 5h0c0-1 1-2 1-3-1-1-2-1-3-1h-3c0-1 0-2-1-2-2-1-2-2-4-2h-1l1-2c-1-1-1-1-3-1-3 1-4 4-6 7-2 1-3 3-4 5-1 1-1 2-1 3 1 0 1-1 1-2 1-1 1-2 2-3h0c0 3-3 6-4 9 0 2-1 2-2 3l2-10c0-1 1-2 2-3v-1c1-2 2-3 3-4z" class="B"></path><path d="M533 668v1c0 2-3 6-5 7 0-1 1-2 2-3v-1c1-2 2-3 3-4z" class="C"></path><path d="M485 654c1 0 3-1 5-1l1 1v1c1 0 1 0 1 1h0l-2-1c0 1-1 1-1 2-1 0-1 1-2 1-1 1-1 1-1 2 1 2 2 3 3 4s0 0 0 1c1 1 3 2 4 3 2 1 6 5 7 8 1 1 1 2 1 3v1 3c1 2 2 5 3 7l-1 2 2 2-2 1h-2c-4-3-8-9-9-14-4-4-8-9-12-12-1 1 0 2 0 3-1-1-3-2-4-2-2 0-5 2-7 3h-1l-3-3c3-2 6-3 9-5 4-3 8-8 11-11z" class="W"></path><path d="M496 674c2 3 4 6 5 9 1 2 2 5 3 7l-1 2c-4-6-5-12-7-18z" class="Z"></path><path d="M485 654c1 0 3-1 5-1l1 1v1c1 0 1 0 1 1h0l-2-1c0 1-1 1-1 2-1 0-1 1-2 1-1 1-1 1-1 2 1 2 2 3 3 4s0 0 0 1c1 1 3 2 4 3 2 1 6 5 7 8 1 1 1 2 1 3v1 3c-1-3-3-6-5-9h-1c0-1-1-1-1-2-3-4-6-7-9-10-1 0-1 0-2 1v1c0 1 0 1 1 1 0 1 1 2 1 3h0l-2-1-1 1 10 13h0c-4-4-8-9-12-12-1 1 0 2 0 3-1-1-3-2-4-2-2 0-5 2-7 3h-1l-3-3c3-2 6-3 9-5 4-3 8-8 11-11z" class="C"></path></svg>