<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="144 60 759 888"><!--oldViewBox="0 0 1023 1024"--><style>.B{fill:#262523}.C{fill:#52514e}.D{fill:#1f1e1b}.E{fill:#191714}.F{fill:#3a3936}.G{fill:#131211}.H{fill:#010101}.I{fill:#6e6c6a}.J{fill:#32312f}.K{fill:#43413e}.L{fill:#2d2c2a}.M{fill:#4b4946}.N{fill:#595855}.O{fill:#3f3f3c}.P{fill:#666563}.Q{fill:#787775}.R{fill:#8f8e8c}.S{fill:#5f5e5a}.T{fill:#373534}.U{fill:#838280}.V{fill:#c9c8c7}.W{fill:#b41f16}.X{fill:#9a9998}.Y{fill:#a6a5a4}.Z{fill:#7e7d7b}.a{fill:#b2b0b0}.b{fill:#b4231d}.c{fill:#bd3229}</style><path d="M408 188c1 1 1 2 2 3l-1 3-4-1 3-5z" class="I"></path><path d="M725 453c2 2 2 5 3 8h-2c-1-3-2-5-1-8z" class="N"></path><path d="M785 646l1-2 1 11c-1 1-1 1-2 1-1-3 0-8 0-10z" class="W"></path><path d="M693 221l9 11c-1 0-2-1-3-1-2-1-3-4-5-4-1-2-2-4-1-6z" class="N"></path><path d="M679 437l2 2 1 2 2 3h2c-1 0-2 0-3 2v2l-2 1c0-5-1-8-2-12z" class="F"></path><path d="M738 359c1 1 0 3 1 5l-1 3v2c0 2 0 3-1 4-1-4-1-8-1-12l2-2z" class="C"></path><path d="M594 271h2l4 3h2l-3 6c-1-3-3-6-5-9z" class="P"></path><path d="M737 373c1-1 1-2 1-4v-2h1c0 1-1 5 0 6l1 1c-1 1-2 1-2 3v1c1 1 1 1 1 2v4h0l-1 2h0l-1-13z" class="M"></path><path d="M726 461h2v14l-4-12c1 0 1-1 2-2z" class="C"></path><path d="M415 176c2-3 3-5 6-8l2-2-3 9c-1 0-1 2-1 2l-1-1c-1 1-1 1-2 3l-1-3z" class="N"></path><path d="M680 645l-4-12c2 1 6 4 6 6s-1 4-1 6h-1z" class="W"></path><path d="M611 192c0-5-2-11-2-16 1 1 2 3 4 4l-1 3-1 1 1 1c1 1 1 3 0 5 0 1 0 1-1 2z" class="C"></path><path d="M748 449l2 1c-1 1-1 1-1 2l-2 2c1 1 2 1 3 1l-10 2c2-2 6-5 8-8h0z" class="I"></path><path d="M378 726l2-1 12 8c-4-1-9-2-13-4v-1l-1-2z" class="B"></path><path d="M739 364h3c-1 2-2 3-2 5 1 1 1 2 3 3h1l-1 1v1 2c-1 0-2-1-3-2l-1-1c-1-1 0-5 0-6h-1l1-3z" class="T"></path><path d="M681 439l2-1h1 2l4 2v2l-4 2h-2l-2-3-1-2z" class="E"></path><path d="M681 439l2-1h1l2 2-1 1c-1 1-1 0-3 0l-1-2z" class="G"></path><path d="M855 527c3 2 10 6 11 10h-1l-6-2h0c-1-3-2-3-3-5-1 0-1-2-1-3z" class="L"></path><path d="M408 188l7-12 1 3c-2 4-4 7-5 12h-1c-1-1-1-2-2-3z" class="Q"></path><path d="M861 265c2 1 5 1 7 1 3 1 6 3 9 2 4-1 6-2 9-4-3 3-6 6-10 7-2 1-6-2-7-3-3-1-5-1-8-2v-1z" class="W"></path><path d="M364 713c5 4 11 8 16 12l-2 1c-4-2-8-4-11-7v-1c-1 2 0 1-1 2h-1l1-1c-1-1-1-3-2-4v-2z" class="P"></path><path d="M698 488c4-3 9-5 14-5h1v1h0c-4 0-7 2-9 4l-2 3-1-1-2 2h0c-1-2-1-3-1-4z" class="B"></path><defs><linearGradient id="A" x1="738.367" y1="344.131" x2="733.325" y2="348.112" xlink:href="#B"><stop offset="0" stop-color="#343131"></stop><stop offset="1" stop-color="#4c4c4b"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M735 336c0 1 1 1 1 2v1l1 2c1 4 1 10 1 14v4l-2 2-1-25z"></path><path d="M175 267c-4 0-10 3-14 5-4-2-7-5-10-8 3 1 5 2 8 3 5 2 12 0 16-2h1l-1 2z" class="c"></path><path d="M497 238h1l3 3c1 0 2 1 3 1 2 3 4 7 6 11v2 3c-2-4-4-8-7-12-1-2-2-3-4-3 1 0 1 0 1-1l-3-4z" class="C"></path><path d="M615 494l5 3c-1 1-7 2-9 3-1 0-2 0-3-1-1 0-2-1-3-2l1-1c3-1 6-1 9-2z" class="b"></path><path d="M215 395v2l-3 3c-1 0-2 1-2 1l-2 1v1l-1-1-1 2h-2l-1 1c-2 1-7 1-9 1 2-2 4-3 6-5 0 1 0 1 1 1s3 0 4-1h1c2-1 4-3 6-3l3-3z" class="N"></path><path d="M722 447c0 2 1 4 3 6-1 3 0 5 1 8-1 1-1 2-2 2-1-3-3-6-6-8v-2l1-2h1 2 0 0v-3-1z" class="L"></path><path fill="#db8e8d" d="M360 455l6 3c4 3 9 10 10 15 1 0 1 1 1 2h-1c-3-5-7-9-10-13-2-2-5-4-6-7z"></path><path d="M846 455c7 8 14 14 19 24l-9-9h0v-1l-6-6-1-1-1-1c-2-2-2-3-2-6z" class="F"></path><path d="M405 497c3 1 10 3 11 7v1c-1 0-1 0-2-1-2-1-6-2-9-2-1 7-2 17-8 22h-1v-1c4-3 5-8 6-13l2-8-2-1h4c0-2 0-2-1-4z" class="D"></path><path d="M690 392c-5-4-8-6-15-6-3 0-5 0-8-1h0c0-1 1-1 1-1h7c7 1 12 2 19 6l-1 2c-1 1-1 1-3 0z" class="c"></path><path d="M192 462v1h-1c-2 1-3 1-5 3l1 1c1-1 3-1 5 0h2 0c1 1 1 1 2 1h-1c-2 0-7-1-9 0h-1c-5 3-9 8-12 12 2-6 5-10 10-15 1 0 2-1 3-1 2-1 4-2 6-2z" class="L"></path><path d="M606 534c2-2 4-4 5-6l3-6c1-4 4-6 6-9 0-1 1-2 2-4l1 1-2 2c-2 3-6 10-6 13 0 5 1 10 1 14-1-3-3-6-4-10l-6 6v-1z" class="W"></path><path d="M435 259h1 0c0 6-3 7-4 12-1 0 0 2 1 2 1-1 2-2 4-3-1 4-3 7-4 11-2-4-3-10-7-12h-1c2-1 5-1 7-3 2-1 2-4 3-7z" class="S"></path><path d="M319 203c5 3 8 5 9 11 0 2 0 5-1 7-3 3-7 5-11 6h0c4-2 7-4 9-7 1-4 0-6-2-9l-3-3s0-1 1-1c-2-2-3-3-5-3l3-1z" class="F"></path><path d="M183 465c7-9 14-16 18-27 0 2 0 4-1 6v2c-1 3-3 4-4 7-1 1-3 3-3 5l-2 2c1 1 1 0 2 1 1 0 0 0 1 1h-2c-2 0-4 1-6 2-1 0-2 1-3 1z" class="M"></path><path d="M862 281c0-4 1-9-1-12-1-3-3-3-6-4h3 0c1-1-1-1-1-3 2 1 3 2 4 3v1c4 6 6 13 5 20 0 1-1 3-1 4-1-2-1-3-2-5 0-2 0-2-1-4z" class="T"></path><path d="M422 549c1-3 4-9 3-12l-1-2c-1-2-1-6-1-9 1-1 1-3 1-4-1-3-6-8-5-11 1 0 4 5 4 6 2 3 3 5 3 8 1 2 2 3 3 5h0l-2-2h-1c1 8 1 14-3 21h-1z" class="c"></path><path d="M405 193l4 1c-1 2-1 3 0 5-1 0-2-1-3-2l-2 2-1 1c-5 4-9 6-14 9-3 2-5 4-9 4h0c9-6 19-11 25-20z" class="C"></path><path d="M421 461h1c0 1-1 1-1 2 2 1 3 3 3 5 1 1 1 3 2 3 5 4 13 4 18 4 3 1 6 2 9 2 2 0 9-1 11 0l-2 1-23-2c-2 1-3 1-4 2-3-1-8-3-10-5s-2-5-4-7c0-1-2-2-2-3l2-2z" class="W"></path><path d="M433 536c4 4 13 9 14 15 1 1 2 2 3 4 2 3 3 13 3 17h-1v-1c0-5-2-11-4-15l-6-6-9-14zm173-2v1c-1 3-2 5-4 8-6 9-13 17-12 29v1h0c-1-4-1-12 0-16 2-3 3-7 5-10 3-5 8-9 11-13z" class="c"></path><path d="M344 390c9-6 20-12 31-12l1 1c-2 2-10 3-13 4-5 2-10 5-14 9h-2l-2-1-1-1z" class="W"></path><path d="M680 645h1 0c5 4 6 10 11 13 2 2 5 2 8 2 1 1 3 2 4 3 5 2 8 5 12 8 0 1 0 2-1 2l-2-3c-3-3-12-9-15-9h-3c-5-1-10-9-12-12-1 2-2 5-3 7s-3 5-3 7c0 1 1 3 1 4 0 2 1 3 1 5-1 2-1 3-1 5v1c-3-3-1-6-1-9 0-2 0-4-1-6 0-2 3-9 4-11s1-4 0-6v-1z" class="b"></path><defs><linearGradient id="C" x1="681.154" y1="580.229" x2="688" y2="590.501" xlink:href="#B"><stop offset="0" stop-color="#595658"></stop><stop offset="1" stop-color="#757670"></stop></linearGradient></defs><path fill="url(#C)" d="M692 572v1c-1 1-1 2-1 2l1-1 2-2c-3 5-7 8-7 14-2 3-4 8-4 12-1 1-1 4-2 6h0v7c-3-8 3-27 5-35l2 1c2-1 3-3 4-5z"></path><path d="M721 274h1l2 2h1l2 2h2 1l1 1h1l1-1c0-1 0-1 1-1l3 1c1 1 1 1 2 0 0 3 2 6 2 9 3-1 5-3 7-3l-1 2h0c-4 3-7 6-12 7 2-4 3-5 2-10-1-1-2-1-3-2h-1c-5-1-8-2-12-7z" class="X"></path><path d="M417 478h0c0 2-3 4-4 5l2 1c0 4-2 6-3 9l6 5c0-1 1-1 1-2 1 0 3-1 4-1 2-1 8 1 10 2v1l-2 1c-2 1-4 2-6 1-2 0-5-1-6-1-1-1-1-1-2 0h-1c-2-1-5-3-7-5 1-3 3-5 4-7l-4-3 8-6z" class="b"></path><path d="M819 384l1 1s1 1 1 2v1l1 2 1 1h0c1 2 2 3 3 5v-1h1c-1-2-2-3-3-4v-1-1-1c1 2 3 5 5 7 4 4 9 8 14 11-3 0-7-1-11-1h4l1-1-9-4c-2-1-3-1-5 0h-1v-3c0-3 0-5-2-7l-2-5 1-1z" class="J"></path><defs><linearGradient id="D" x1="699.864" y1="278.216" x2="694.13" y2="286.769" xlink:href="#B"><stop offset="0" stop-color="#302f2c"></stop><stop offset="1" stop-color="#474747"></stop></linearGradient></defs><path fill="url(#D)" d="M688 278l2-2c2 1 2 0 3 2-1 1-1 1 0 2 1 0 1-1 2-1h1 0c1 1 1 1 1 2h1c1-1 2-2 3-2 2 1 1 1 1 2 1 1 6 0 7 1 1 0 3 2 4 3h-23v-3c-2-1-3-3-4-3 0-1 1 0 2-1z"></path><path d="M620 447h0c3 3 5 7 5 11v1c3 2 5 3 8 6-4 5-9 9-14 13 0 3 0 6 2 9 1 2 3 5 5 7l-5 4c0-1 1-3 2-4l1-1c0-1-3-3-3-4h-3v-12l5-4c2-2 6-5 6-8-2-2-4-4-5-6-1 0-2-2-2-2-1 0-4 1-4 1-3 0-7 1-10 1v-1l12-1c2-1 3-1 4-2-1-3-3-5-4-7v-1z" class="W"></path><path d="M839 462c2-1 4-1 7-1 1 2 3 2 4 2l6 6v1c-4-2-7-3-12-1-2 1-3 2-4 4v1l-2-1c-1-2-1-8 1-11z" class="H"></path><path d="M749 413c3 2 4 6 5 9v2c1 1 1 1 1 2h3v-1c1 6 1 11-2 17-2 3-4 6-6 8l-2-1c1-4 3-7 4-11 4-8 0-16-3-25z" class="B"></path><defs><linearGradient id="E" x1="655.788" y1="429.955" x2="670.212" y2="406.545" xlink:href="#B"><stop offset="0" stop-color="#51524e"></stop><stop offset="1" stop-color="#736e6f"></stop></linearGradient></defs><path fill="url(#E)" d="M667 417h1c6 1 10-3 15-5 1 0 1 0 2 1-1 1-3 1-4 3l-2 1c-2 1-4 2-6 2-1 1-3 2-4 3h-1c-6 1-12 1-18 2h-6l-1-1 24-6z"></path><path d="M699 394c4 3 10 6 14 10 2 2 3 6 4 9-1-1-1-1-1 0l-2 1v-1h-1c0-1 0-1-1-1-1-1-2-1-3-2h0l-2-1h-1l-1 1-1 1-3-11-2-5v-1z" class="B"></path><path d="M704 401c0 1 2 3 3 3l1 1c1 0 3 3 3 5h-2 0v-1c-4-3-4-3-5-8z" class="F"></path><path d="M701 400c1 0 2 1 3 1 1 5 1 5 5 8v1l-2-1h-1l-1 1-1 1-3-11z" class="I"></path><defs><linearGradient id="F" x1="725.812" y1="677.459" x2="713.252" y2="671.898" xlink:href="#B"><stop offset="0" stop-color="#686767"></stop><stop offset="1" stop-color="#878684"></stop></linearGradient></defs><path fill="url(#F)" d="M722 667h1c1-1 0-1 1-1s2 0 3 1c2 0 3 1 4 1l-11 9h-2c-2 2-3 4-6 5h-1c-3 2-5 5-8 5l12-13v-1c1 0 1-1 1-2 2 0 5-3 6-4z"></path><path d="M193 305l-5-4c-2-1-6-4-7-6 0-1 0 0-1-1-2-2-3-4-4-7 0 0-1-1-1-2h-1l-1-1 1-1c1 0 1 1 2 1h1l-1-1c0-2 0-2-1-2l1-1 1 3c2 5 9 11 14 14 7 3 12 3 19 3-2 1-3 1-5 1s-4 1-6 1l-2 1c-1-1-1-2-2-2s-1 0-2 2h0v2z" class="T"></path><defs><linearGradient id="G" x1="752.828" y1="246.565" x2="718.779" y2="234.143" xlink:href="#B"><stop offset="0" stop-color="#2c2b2a"></stop><stop offset="1" stop-color="#4c4b47"></stop></linearGradient></defs><path fill="url(#G)" d="M711 241c4 2 8 5 12 6 7 3 16 3 22 0s9-9 12-15c0 2 1 5 0 7h-1c-1 2-2 6-4 7l-1-1c-1 2-3 4-5 6-1 0-2 1-3 1h-3c-2 1-3 1-5 1-2-1-5-1-7-1-4-2-7-3-10-4-1-1-2-1-2-2l-5-3h1l-1-2z"></path><path d="M427 693c1-1 1-2 1-3-1-2-1-2-1-3v-2c1-1 1-1 3-2 0 6 3 11 4 17 1 2 1 5 2 8l3 6 1 1c0 1 0 3-1 4-1 6-4 11-6 17h0c2-7 4-12 5-18-2-2-4-4-5-6s-1-4-2-6c0-2-1-3-2-5 0-3 0-5-2-8z" class="b"></path><path d="M427 693c-2-3-3-6-4-9-1-4-2-9-1-12 1-2 3-4 4-5l5-5c3-7 8-12 12-18-1 5-6 10-9 14-3 5-7 14-5 19l1 6c-2 1-2 1-3 2v2c0 1 0 1 1 3 0 1 0 2-1 3z" class="W"></path><defs><linearGradient id="H" x1="412.941" y1="198.065" x2="423.966" y2="183.149" xlink:href="#B"><stop offset="0" stop-color="#0a0908"></stop><stop offset="1" stop-color="#333230"></stop></linearGradient></defs><path fill="url(#H)" d="M416 179c1-2 1-2 2-3l1 1c-1 4-4 14-3 17 1 1 2 1 3 1 6 1 13-3 18-5l1 1c-2 1-12 4-12 5l-2 2c1 0 2-1 3-1h1c1-1 2-1 3-2h1l1-1h1c1 0 1 0 2-1h2 0c-8 4-15 8-23 8-2-1-3-1-4-3s0-5 0-7c1-5 3-8 5-12z"></path><path d="M380 213h0c4 0 6-2 9-4 5-3 9-5 14-9l1-1 2-2c1 1 2 2 3 2v1c-10 9-22 12-32 20h-1c-1 1-2 1-2 1-2 0-3 2-3 3h-1v1l-1 3-1-1c1-1 0-1 0-2l-1 1-1-1h-1l-1 1c-1-1-1-2-2-2l18-11z" class="K"></path><defs><linearGradient id="I" x1="672.902" y1="221.991" x2="665.725" y2="226.922" xlink:href="#B"><stop offset="0" stop-color="#696767"></stop><stop offset="1" stop-color="#807f79"></stop></linearGradient></defs><path fill="url(#I)" d="M630 205l16 8s1 0 1-1l24 11c3 1 7 4 10 5h5c-2 1-2 1-3 0-2 0-3 1-4 2h-1v1l1 1c-3 1-6-1-9-2l-1-1-1-1c-2-1-3-2-3-4-1-1-5-2-6-3l-18-8-12-7 1-1z"></path><path d="M665 224c2 1 5 2 7 3s4 4 6 4l1 1c-3 1-6-1-9-2l-1-1-1-1c-2-1-3-2-3-4z" class="N"></path><defs><linearGradient id="J" x1="632.9" y1="430.938" x2="632.185" y2="419.433" xlink:href="#B"><stop offset="0" stop-color="#252421"></stop><stop offset="1" stop-color="#656161"></stop></linearGradient></defs><path fill="url(#J)" d="M656 431c-8 1-18 1-25-1-6-2-15-4-18-10-1-2-1-3 0-4h1c0 1 1 2 1 3 1 1 4 2 5 2 8 2 15 2 23 2l1 1h6c-1 0-2 0-3 1l-1 1c-2 0-6 0-8 1h0 4c2 1 5 1 8 1v2c2 0 4 0 6 1z"></path><path d="M701 494h0c3-1 5-3 7-4 5-2 13 2 17 3 7 2 15-3 21-6 3-2 8-5 12-4l-1 1c-5 1-9 3-13 6-2 3-4 6-7 7h-1c-2 0-8 2-10 1l-1-2c0 1-1 1-1 2-2 0-4 0-6-1s-5-2-8-2c-3-1-7 1-10 2 0-1 0-2 1-3z" class="b"></path><path d="M732 303v-5c5-2 9-5 14-8l-1 3c1 1 1 1 3 2h-1l-3 4c2 1 3 1 4 1 2 0 2 0 4 1l-4 1-3 2c-1 1-2 3-3 4 0 1-1 3-1 4l-2 2c-2-1-4-1-6-1-1-3-1-6-1-10z" class="Q"></path><path d="M743 300v-2c0-1 1-1 1-2l-1-1c0-1 1-1 2-2 1 1 1 1 3 2h-1l-3 4-1 1z" class="N"></path><path d="M745 304h-1-3c-1 1-2 1-3 1v-1c1-1 0-1 1-1 0-1 1-2 3-3l-1 2 2 1c1 0 2-1 3-1h2l-3 2z" class="P"></path><path d="M743 300l1-1c2 1 3 1 4 1 2 0 2 0 4 1l-4 1h-2c-1 0-2 1-3 1l-2-1 1-2h1z" class="C"></path><path d="M732 303l1 1c1 1 1 1 1 2l2 1c-1 0-1 1-1 1 2 1 4 1 6 0h1c0 1-1 3-1 4l-2 2c-2-1-4-1-6-1-1-3-1-6-1-10z" class="a"></path><defs><linearGradient id="K" x1="679.635" y1="401.539" x2="694.292" y2="410.08" xlink:href="#B"><stop offset="0" stop-color="#474442"></stop><stop offset="1" stop-color="#5d5e5b"></stop></linearGradient></defs><path fill="url(#K)" d="M694 390c1 1 4 2 5 4v1c0 3 1 5 1 7-2 2-2 3-5 3h0l-1 7c-1 1-2 1-3 2l1-3h-1c-2 0-4 1-6 2-1-1-1-1-2-1-5 2-9 6-15 5h-1c8-4 20-9 23-18 1-2 1-5 0-7 2 1 2 1 3 0l1-2z"></path><path d="M338 500l-2-2c-2-2-4-3-7-3h0c-6-1-9 3-14 4-2 1-4 0-6 0-3-1-6-1-9-1-3-1-5-1-7-2-6-4-8-10-15-11l-5-1h0 8v1c4 1 6 4 10 6 2 2 5 3 7 3 7 2 14 0 21-2l6-3c3 0 7 2 10 3h1c1 1 1 2 1 3l1 2v3z" class="b"></path><defs><linearGradient id="L" x1="696.568" y1="544.025" x2="685.24" y2="574.664" xlink:href="#B"><stop offset="0" stop-color="#262421"></stop><stop offset="1" stop-color="#413f3d"></stop></linearGradient></defs><path fill="url(#L)" d="M689 537c1 1 1 2 2 4 1 1 2 2 4 3 1 0 1-1 2-1 0 1 1 2 2 2-1 2-1 4-1 6-1 1-2 2-3 4h0c-1 1-1 3 0 4 0 1 1 2 2 2l-2 4c-1 2-3 5-3 7-1 2-2 4-4 5l-2-1 2-16 1-23z"></path><path d="M695 548s0 1 1 1v3h-2c0-2 0-2 1-4zm-6-11c1 1 1 2 2 4v9c-1 2-1 3-1 5 1 1 0 1 0 2v3 1-1-1c-1-2 0-2 0-3v-1c-1-1-1-2-1-3-1 3 1 5-1 8l1-23z" class="T"></path><defs><linearGradient id="M" x1="395.171" y1="405.029" x2="381.888" y2="427.207" xlink:href="#B"><stop offset="0" stop-color="#433f3a"></stop><stop offset="1" stop-color="#575654"></stop></linearGradient></defs><path fill="url(#M)" d="M356 409c14 9 32 15 49 14 3 0 7 0 11-1 2-1 5-2 7-4 1-2 0-3 0-5 1 1 1 0 2 1v4c-1 3-5 5-8 7-4 2-9 3-14 4l-1-1v-1c-2-1-3 0-5 0h-12c1 0 4 0 6-1-1 0-1 0-2-1h-1c-1-1-2-1-4-1-6-1-12-3-18-6-2-1-4-2-5-3-1 0-1-1-2-1s-2-1-3-1v-4z"></path><defs><linearGradient id="N" x1="691.051" y1="411.919" x2="702.32" y2="420.131" xlink:href="#B"><stop offset="0" stop-color="#676663"></stop><stop offset="1" stop-color="#81807d"></stop></linearGradient></defs><path fill="url(#N)" d="M699 395l2 5 3 11v11c0 2 0 4-1 5v1c-2-1-5-2-7-2h-1l-10-1 2-1h0l3-2c0-2 2-5 3-6s1-3 1-4l1-7h0c3 0 3-1 5-3 0-2-1-4-1-7z"></path><path d="M699 395l2 5 3 11v11c0 2 0 4-1 5l-1-1c0-1 0-2-1-3h0v-1c1-1 1-2 1-3-1-3 0-5 0-7 0-3-1-6-2-10 0-2-1-4-1-7z" class="Y"></path><defs><linearGradient id="O" x1="621.927" y1="198.391" x2="629.052" y2="193.626" xlink:href="#B"><stop offset="0" stop-color="#53524f"></stop><stop offset="1" stop-color="#7f7d7c"></stop></linearGradient></defs><path fill="url(#O)" d="M613 180h0c4 4 6 9 9 13 7 9 16 14 25 19 0 1-1 1-1 1l-16-8-1 1c-4-2-7-5-12-5l-3-1h1 1c-2 0-5-1-7-1-2-1-4-2-5-3v-2l4 1c2 0 2-1 3-3 1-1 1-1 1-2 1-2 1-4 0-5l-1-1 1-1 1-3z"></path><path d="M617 193c2 1 2 1 2 3 3 5 7 6 11 9l-1 1c-4-2-7-5-12-5l-3-1h1 1c1-1 2-1 2-2 1-1 0-3-1-5z" class="U"></path><path d="M612 183c1 1 3 3 3 5 1 1 2 4 2 5 1 2 2 4 1 5 0 1-1 1-2 2-2 0-5-1-7-1-2-1-4-2-5-3v-2l4 1c2 0 2-1 3-3 1-1 1-1 1-2 1-2 1-4 0-5l-1-1 1-1z" class="B"></path><defs><linearGradient id="P" x1="757.939" y1="406.804" x2="738.058" y2="405.749" xlink:href="#B"><stop offset="0" stop-color="#0d0b09"></stop><stop offset="1" stop-color="#2d2d2d"></stop></linearGradient></defs><path fill="url(#P)" d="M738 386l1-2c5 7 10 14 14 21 2 5 5 12 8 17l-1 1s-1 1 0 1l1 11-1 1c0 1-1 3 0 4v1l-1 1c-1 0-1 0-1 1l-2-1c3-6 3-11 2-17v1h-3c0-1 0-1-1-2v-2c-1-3-2-7-5-9l-6-12c-1-3-3-5-4-8-1-2-1-5-1-7h0z"></path><path d="M738 386l1-2c5 7 10 14 14 21 2 5 5 12 8 17l-1 1s-1 1 0 1l1 11-1 1c0 1-1 3 0 4v1l-1 1c-1 0-1 0-1 1l-2-1c3-6 3-11 2-17-2-14-11-28-20-39z" class="I"></path><path d="M344 390l1 1 2 1h2c-1 2-2 5-1 8 0 1 1 2 1 3l1 5h-1c-1 0-2 0-3-1v-1-1c-3 0-4 1-6 2v1h-1l-2 5v2l-1 3c0 2-1 2-1 4v1l-1 2v-13-5s1 0 0-1c0-1-1 0-1-2h-2c-1 1-2 2-4 3h0c1-4 3-9 7-11 3-3 7-4 10-6z" class="M"></path><path d="M337 404c1 1 1 2 2 3l1 1h-1l-2 5v2l-1 3h-1c-1-2 0-7 1-9l1-5z" class="D"></path><path d="M337 404c1 1 1 2 2 3l1 1h-1c-1 0-1 0-2 1v1l-1-1 1-5z" class="E"></path><path d="M345 391l2 1c0 2 0 2-1 4l-3 6c-2 2-3 3-4 5-1-1-1-2-2-3l8-13z" class="D"></path><path d="M347 392h2c-1 2-2 5-1 8 0 1 1 2 1 3l1 5h-1c-1 0-2 0-3-1v-1-1c-3 0-4 1-6 2v1l-1-1c1-2 2-3 4-5l3-6c1-2 1-2 1-4z" class="J"></path><path d="M343 402h1l2-1v1c1 2 3 3 3 5-1 0-2 0-3-1v-1c-3 0-4 1-6 2v1l-1-1c1-2 2-3 4-5z" class="B"></path><path d="M545 153c13 16 29 28 49 37 1 1 10 4 10 4v2c1 1 3 2 5 3v1h-3l-8-3-12-6c-11-4-19-11-28-18-3-3-4-6-7-8-1-3-5-6-6-9v-3z" class="Q"></path><defs><linearGradient id="Q" x1="844.122" y1="408.928" x2="824.457" y2="448.88" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2a2923"></stop></linearGradient></defs><path fill="url(#Q)" d="M823 400c2-1 3-1 5 0l9 4-1 1h-4c-1 2-3 3-3 5s1 5 1 7c3 14 8 26 16 38 0 3 0 4 2 6l1 1 1 1c-1 0-3 0-4-2 0 0 0-1-1-1l-1-1h-1c-2-2-4-4-5-7l-1-1-5-14-9-30v-2-5z"></path><path d="M422 549h1c-2 6-6 11-8 17v1c-1 4-2 8-1 12 1 3 0 5 1 8 0 5 2 10 2 15-1 6 1 11-3 16h-1c-2-2-3-3-4-6 1-7 1-15 1-22 0-6-1-11-1-16 0-1-1-3 0-4 0-2 3-5 4-6l9-15h0zm179 97h0l11 14c1 2 2 4 4 6 1 1 4 3 5 5 1 4-2 14-4 17-1 4-3 5-4 9-2 5-2 10-4 15-1 2-3 4-4 6-2 3 0 8 1 11l1 3-1 1v-1c-2-4-3-9-4-13 0-1-1-3 0-4 0-2 3-4 3-7 1-3 1-7 2-9 1-3 3-6 4-9 1-5 3-11 3-15 0-7-3-13-6-19-3-3-5-6-7-9v-1z" class="b"></path><path d="M619 546h1c2 2 3 5 4 8 2 2 6 6 7 9v9c0 6-1 13-1 19l1 13c0 3 1 6 1 8-1 1-2 4-3 5l-2 1v-1c-4-3-2-10-3-14-1-7 1-12 2-19v-5-5-11c-1-5-5-11-7-16v-1z" class="W"></path><defs><linearGradient id="R" x1="211.502" y1="403.129" x2="225.884" y2="335.56" xlink:href="#B"><stop offset="0" stop-color="#30302e"></stop><stop offset="1" stop-color="#53504e"></stop></linearGradient></defs><path fill="url(#R)" d="M231 332c2 1 3 1 4 3l1 1c0-1 3-3 3-4 1-2 2-4 4-5v1h0c-2 1-3 5-3 7l-3 8-1 2-1 5c-1 1-1 1-1 2h-1l-2 5c0 2-1 4-2 6 0 1-1 1-1 3-1 0-1 1-1 2s-1 2-1 4l-3 7-1 3c-1 1-1 2-1 3-2 3-2 5-4 7s-2 2-2 3l-3 3c-2 0-4 2-6 3h-1c-1 1-3 1-4 1s-1 0-1-1c5-3 10-7 13-12 4-4 6-11 8-17 5-13 13-25 10-40z"></path><path d="M327 407h0c2-1 3-2 4-3h2c0 2 1 1 1 2 1 1 0 1 0 1v5 13l1 1 1-1v-2c0-1 1-1 2-1l1 1-1 1 2 1-5 3c-1 1-3 2-5 4-4 2-8 6-11 10v-2c1-12 4-22 8-33z" class="E"></path><path d="M330 430v-1l2-2h-1l1-2v-2c0-2-1-3-1-5 0-3 1-4 3-6v13l1 1c-1 1-4 3-5 4z" class="G"></path><path d="M335 426l1-1v-2c0-1 1-1 2-1l1 1-1 1 2 1-5 3c-1 1-3 2-5 4-4 2-8 6-11 10v-2l11-10c1-1 4-3 5-4z" class="I"></path><path d="M705 410l1-1h1l2 1h0c1 1 2 1 3 2 1 0 1 0 1 1h1v1l2-1c0-1 0-1 1 0 2 6 4 14 4 21 1 4 1 9 1 13-3-4-5-8-9-12l-7-6c-1-1-2-1-3-1v-1c1-1 1-3 1-5v-11l1-1z" class="B"></path><path d="M707 409l2 1h0c1 1 2 1 3 2 1 0 1 0 1 1s-1 3-2 3c-1-3-2-2-4-4-1-1-1-1 0-3z" class="E"></path><path d="M709 422c0-1 1-1 2-2v1l2 2c2 1 3 2 4 3h1c1 1 1 2 1 4h-1-1c0-1-1-2-2-3-2-2-5-3-6-5z" class="F"></path><path d="M705 410h0c1 2 0 4-1 5 2 3 3 5 5 7 1 2 4 3 6 5 1 1 2 2 2 3v1c-1-1-3-2-4-3s-2-1-3-2l-1-1h-1c-2-1-3-2-4-3v-11l1-1z" class="M"></path><defs><linearGradient id="S" x1="709.932" y1="424.556" x2="708.875" y2="432.982" xlink:href="#B"><stop offset="0" stop-color="#585757"></stop><stop offset="1" stop-color="#72726d"></stop></linearGradient></defs><path fill="url(#S)" d="M703 427c1-1 1-3 1-5 1 1 2 2 4 3h1l1 1c1 1 2 1 3 2s3 2 4 3c0 2 1 3 1 5-2 0 0-1-1-2-1 0-1 0-2-1l-2 2-7-6c-1-1-2-1-3-1v-1z"></path><defs><linearGradient id="T" x1="710.455" y1="418.618" x2="724.785" y2="428.698" xlink:href="#B"><stop offset="0" stop-color="#2a2a21"></stop><stop offset="1" stop-color="#555256"></stop></linearGradient></defs><path fill="url(#T)" d="M713 413h1v1l2-1c0-1 0-1 1 0 2 6 4 14 4 21 1 4 1 9 1 13-3-4-5-8-9-12l2-2c1 1 1 1 2 1 1 1-1 2 1 2 0-2-1-3-1-5v-1h1 1c0-2 0-3-1-4l-2-7c0-2 0-2-2-3-1 0-1 0-1 2h-3l1-2c1 0 2-2 2-3z"></path><path d="M713 435l2-2c1 1 1 1 2 1 1 1-1 2 1 2l3 7v-9c1 4 1 9 1 13-3-4-5-8-9-12z" class="Q"></path><defs><linearGradient id="U" x1="318.367" y1="251.858" x2="317.748" y2="217.518" xlink:href="#B"><stop offset="0" stop-color="#302e2d"></stop><stop offset="1" stop-color="#615f5c"></stop></linearGradient></defs><path fill="url(#U)" d="M290 244c3 2 6 3 9 4 16 4 27-8 37-19 4-6 8-12 14-15l-3 7c-1 3-1 6-1 9-2-1-3-1-5-1l-3 6v-1l-1 1-2 1v1 2c-1 1-3 1-3 2-5 5-10 10-16 13h-3c-3 0-4 1-7 1h-3c-4-2-8-3-11-6-1 0-3-2-4-3v-1c1 0 1 0 2-1h0z"></path><path d="M341 229c1-3 2-6 5-8h1c-1 3-1 6-1 9-2-1-3-1-5-1z" class="D"></path><path d="M292 249c1 0 2 1 3 1 3 2 5 3 8 3 7 1 14-1 19-5 5-3 8-8 13-11v2c-1 1-3 1-3 2-5 5-10 10-16 13h-3c-3 0-4 1-7 1h-3c-4-2-8-3-11-6z" class="C"></path><path d="M724 206v-2c7-2 15-3 22 0 3 1 5 3 7 5 1 0 2 2 3 2 5 6 9 12 11 20v7c-3 1-6 4-9 6h0c-2 1-3 2-3 3h-3v-1c2-1 3-5 4-7h1c1-2 0-5 0-7s1-4 0-7c0-5-4-9-8-12-8-6-16-9-25-7z" class="H"></path><path d="M767 231v7c-3 1-6 4-9 6h0c-2 1-3 2-3 3h-3v-1c2-1 3-5 4-7v3l2-1 5-5c1 0 1-1 2-2 0-1 1-2 2-3z" class="D"></path><path d="M731 668c1-1 4-3 5-4h1c-8 12-20 19-31 28l1 2c-4 3-8 5-13 8-8 6-16 12-25 17-4 2-8 5-12 6 2-3 5-5 7-6l13-10-3-8 4 7 25-21c3 0 5-3 8-5h1c3-1 4-3 6-5h2l11-9z" class="Q"></path><defs><linearGradient id="V" x1="716.642" y1="684.513" x2="713.358" y2="679.487" xlink:href="#B"><stop offset="0" stop-color="#3c3b3b"></stop><stop offset="1" stop-color="#5d5c5b"></stop></linearGradient></defs><path fill="url(#V)" d="M731 668c1-1 4-3 5-4h1c-8 12-20 19-31 28l-10 7-6 3 6-6c5-5 11-9 16-14 3-1 4-3 6-5h2l11-9z"></path><defs><linearGradient id="W" x1="278.97" y1="413.755" x2="298.405" y2="415.975" xlink:href="#B"><stop offset="0" stop-color="#030301"></stop><stop offset="1" stop-color="#353331"></stop></linearGradient></defs><path fill="url(#W)" d="M277 438c-1-10 2-22 7-31 1-2 3-4 4-6 4-7 8-13 12-19 1-1 1-2 3-3v5 3c-1 3-3 5-5 7-3 5-6 11-8 17-3 7-5 13-6 20s0 13 4 19l3 3h-1-1c-3-1-4-3-6-5-1-2-2-2-3-4l-3-6z"></path><path d="M685 413c2-1 4-2 6-2h1l-1 3c1-1 2-1 3-2 0 1 0 3-1 4s-3 4-3 6l-3 2h0l-2 1h-3l3 2c0 2 0 2-1 3-4 3-1 5 2 8h-2-1l-2 1-2-2c-1-1-2-3-3-4-5-4-14-3-20-2-2-1-4-1-6-1v-2c-3 0-6 0-8-1h-4 0c2-1 6-1 8-1l1-1c1-1 2-1 3-1 6-1 12-1 18-2h1c1-1 3-2 4-3 2 0 4-1 6-2l2-1c1-2 3-2 4-3z" class="D"></path><path d="M667 423l14-1h1 4l1 1v1l-2 1h-3c-8-1-16 1-24 2-5 0-11 1-16 0h-4 0c2-1 6-1 8-1l1-1c1-1 2-1 3-1 6-1 12-1 18-2l-1 1z" class="I"></path><path d="M668 422l-1 1c-7 2-14 3-21 3l1-1c1-1 2-1 3-1 6-1 12-1 18-2z" class="B"></path><path d="M685 413c2-1 4-2 6-2h1l-1 3c1-1 2-1 3-2 0 1 0 3-1 4s-3 4-3 6l-3 2h0v-1l-1-1h-4-1l-14 1 1-1h1c1-1 3-2 4-3 2 0 4-1 6-2l2-1c1-2 3-2 4-3z" class="K"></path><path d="M679 417l2-1 1 2c0 1 0 2-2 3h0l-1-1c1-2 0-2 0-3z" class="N"></path><path d="M679 417c0 1 1 1 0 3h-1s-1 0-1 1l-8 1c1-1 3-2 4-3 2 0 4-1 6-2z" class="C"></path><path d="M685 413c2-1 4-2 6-2h1l-1 3-1 2h-1c0-1 0-2-1-3-2 1-3 2-4 4v1h-2l-1-2c1-2 3-2 4-3z" class="L"></path><path d="M694 412c0 1 0 3-1 4s-3 4-3 6l-3 2h0v-1l-1-1h-4-1l2-2 1-1h0c1 1 1 1 1 2 1 0 1 0 2-1s2-2 3-4l1-2c1-1 2-1 3-2z" class="P"></path><defs><linearGradient id="X" x1="192.57" y1="426.89" x2="210.252" y2="439.231" xlink:href="#B"><stop offset="0" stop-color="#24211f"></stop><stop offset="1" stop-color="#464644"></stop></linearGradient></defs><path fill="url(#X)" d="M206 404l1-2 1 1 3 3 2 4c1 0 2-1 3 0v2c1 1 1 1 2 1l-1 2v2l-1 1c-1 1 0 3 0 5-1 1-1 1-1 2l1 1c0 1 0 1-1 2h0v4 1c-1 1-1 2-1 4l-1 5v1l-2 3 1 1c-2 2-4 3-5 5l-1 1 3 2h-1-1c-1-1-2-1-3-1-3 2-5 5-6 7l-4 1c-1-1 0-1-1-1-1-1-1 0-2-1l2-2c0-2 2-4 3-5 1-3 3-4 4-7v-2c1-2 1-4 1-6 3-7 5-14 7-22v-6c-1-3-3-4-5-5l1-1h2z"></path><path d="M199 457s-1-1-2-1c2-2 4-3 5-4 0 2-1 4-3 5z" class="M"></path><path d="M206 404l1-2 1 1 3 3 2 4c1 8-4 16-5 24l-2 8 1-1c1 0 2 0 3-1 1 0 2 1 2 2 1 0 1 1 1 1l-2 3 1 1c-2 2-4 3-5 5l-1 1 3 2h-1-1c-1-1-2-1-3-1-3 2-5 5-6 7l-4 1c-1-1 0-1-1-1h1c3 0 4-2 5-4 2-1 3-3 3-5 2-4 2-8 3-12l6-22c0-3 1-7 0-9-1-3-3-4-5-5z" class="Q"></path><path d="M206 442l1-1c1 0 2 0 3-1 1 0 2 1 2 2 1 0 1 1 1 1l-2 3c-2 2-3 3-6 4 0-3 1-6 1-8z" class="B"></path><path d="M213 410c1 0 2-1 3 0v2c1 1 1 1 2 1l-1 2v2l-1 1c-1 1 0 3 0 5-1 1-1 1-1 2l1 1c0 1 0 1-1 2h0v4 1c-1 1-1 2-1 4l-1 5v1s0-1-1-1c0-1-1-2-2-2-1 1-2 1-3 1l-1 1 2-8c1-8 6-16 5-24z" class="K"></path><path d="M208 434c1 2 3 3 4 6h1v2 1s0-1-1-1c0-1-1-2-2-2-1 1-2 1-3 1l-1 1 2-8z" class="J"></path><defs><linearGradient id="Y" x1="593.942" y1="172.91" x2="592.556" y2="212.659" xlink:href="#B"><stop offset="0" stop-color="#484544"></stop><stop offset="1" stop-color="#6c6d68"></stop></linearGradient></defs><path fill="url(#Y)" d="M552 170c1 1 1 1 2 1 2 1 2 2 4 2 9 7 17 14 28 18l12 6 8 3h3v-1c2 0 5 1 7 1h-1-1l3 1c5 0 8 3 12 5l12 7v1c1 1 3 1 5 2-2 0-3-1-5 0 1 0 2 1 3 1l1 1h0l1 1s1 1 2 1h0c-2 0-4-1-6-2h-3c-1-1 0-1-1-1-2-1-3-2-6-2 0 0-1 0-2-1-1 0-2 0-3-1-2-2-6-2-8-3-2 0-4-1-6-2-1 0-1-1-2 0v1l1 1-2 1v-1h-1c-1-1-2-1-3-1 0-1-1-1-1-2h-1v-1-1h0l-1 1c-2-2-3-4-6-5l-3-1h-2c-8-4-17-8-25-13l-6-3 1-1c-1-2-3-4-4-5-3-3-4-5-6-8z"></path><path d="M606 206c3-1 4 1 7 1v1c-1 0-1-1-2 0v1c-2-1-3-2-5-3z" class="I"></path><path d="M604 206h2c2 1 3 2 5 3l1 1-2 1v-1h-1c-1-1-2-1-3-1 0-1-1-1-1-2h-1v-1z" class="P"></path><path d="M617 201c5 0 8 3 12 5l12 7v1c1 1 3 1 5 2-2 0-3-1-5 0-3-1-6-3-10-5-1 0-3-1-4-1-2-1-3-3-5-4-1-1-3-1-5-2h-2-1l-1-1 1-1c1-1 2-1 3-1h0z" class="N"></path><defs><linearGradient id="Z" x1="657.053" y1="474.413" x2="662.59" y2="482.398" xlink:href="#B"><stop offset="0" stop-color="#494540"></stop><stop offset="1" stop-color="#60605e"></stop></linearGradient></defs><path fill="url(#Z)" d="M683 448h3c1-1 2-1 3-1 0 2-2 3-3 5l-1-1c-2 1-3 4-5 6v1c1 1 1 0 1 1v2l-1 2 1 1v1c-1 1-2 2-3 2l-3 3c-1 1-2 1-2 2v3h-1l-2 2v1l1-1h2c-3 2-7 4-9 7-1 1-1 3-1 4-1 1 0 3 0 4l-2 1c-2 1-6 2-8 3-10 3-19 6-29 8h-2 0c0-3 1-3 3-4l15-8c5-3 9-6 13-8 3-3 7-6 9-9s4-6 5-9c1-2 2-4 3-5s1-1 2 0v1c3-4 7-8 9-13l2-1z"></path><path d="M683 448h3c1-1 2-1 3-1 0 2-2 3-3 5l-1-1c-2 1-3 4-5 6l-3 4c-2 3-5 5-7 7h-1c0-2 2-4 3-6 3-4 7-8 9-13l2-1z" class="T"></path><path d="M624 504c1-2 3-3 5-3 12-5 22-9 33-17 0 3 0 6-1 9-2 1-6 2-8 3-10 3-19 6-29 8z" class="G"></path><defs><linearGradient id="a" x1="824.518" y1="278.548" x2="838.438" y2="305.846" xlink:href="#B"><stop offset="0" stop-color="#2d2b2b"></stop><stop offset="1" stop-color="#52514f"></stop></linearGradient></defs><path fill="url(#a)" d="M795 275v1c1 0 0 1 1 2v2c1 1 1 0 1 1 0 2 2 4 3 6h0c0-2-2-4-2-6l-2-4v-3h0c-1-1 0-2 0-2h0c1 6 3 12 7 18l1 1c1-1 1-2 1-3v-1c-1-1-2-2-2-4 4 7 9 13 17 15 4 1 7 1 11 1 8 0 13-1 20-4 5-4 9-8 11-14 1 2 1 2 1 4-1 2-1 4-2 6h-1c-5 10-14 15-25 18-2 0-5 1-7 1-3-2-20 1-25 2l-1-2c3-3 6-3 9-4-3 0-5 0-8-1h2c2-1 4-1 6-2h0l-2-1c1 0 1 0 2-1-1 0-2-1-2-1l-1-1-1-1h-1c-1-1-3-3-3-4v-1l-3-3-4-8v-1c-1-2-1-1-1-2-1-2 0-3 0-4z"></path><path d="M831 299c8 0 13-1 20-4l-2 2c-2 2-8 3-11 3h-2-5v-1z" class="P"></path><defs><linearGradient id="b" x1="812.479" y1="299.997" x2="821.52" y2="313.511" xlink:href="#B"><stop offset="0" stop-color="#575653"></stop><stop offset="1" stop-color="#737271"></stop></linearGradient></defs><path fill="url(#b)" d="M803 294h1l1 1 2 2c5 3 9 4 14 5 3 1 5 2 8 3 2 0 4 0 6 1h3c7-1 16-7 20-13l1-3 1 1c-5 10-14 15-25 18-2 0-5 1-7 1-3-2-20 1-25 2l-1-2c3-3 6-3 9-4-3 0-5 0-8-1h2c2-1 4-1 6-2h0l-2-1c1 0 1 0 2-1-1 0-2-1-2-1l-1-1-1-1h-1c-1-1-3-3-3-4z"></path><defs><linearGradient id="c" x1="527.096" y1="863.305" x2="578.301" y2="876.255" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#3a3934"></stop></linearGradient></defs><path fill="url(#c)" d="M582 838c4-5 7-9 11-13 2-4 4-8 6-11 1 4-12 23-15 27-7 10-16 17-25 26-7 8-14 17-19 27l-8 16v-1l-1 1c0 1-1 1-1 2v1l-1 1c0-2 1-4 1-5v-3h1c2-8 7-16 11-23 2-3 4-6 7-9l-1-1-9 13c0-1-1-3-1-3-1 0-3-1-5-2l-1 1c-1 0-2 0-3 1h-1c-1 0-2-1-3-1s-1 0-2-1v-1c1-2 3-2 4-3l5-2 14-5c1 0 3-1 4-1v-1c3 0 6-4 8-5s7-4 7-6h1c1-2 4-3 5-5l-1-1c-7 6-15 12-24 15l-2-3c1 0 2-1 3-2l-1-1h1c2-1 4-2 7-3l15-9 13-10z"></path><path d="M582 838c-1 1-1 2-2 3l-1 1c-2 2-1 2-2 3h-2c-2 1-3 3-6 3l13-10z" class="F"></path><path d="M287 655v-1l1-1c1 2 2 2 4 3 2 0 2 0 3 1 0 0 1 1 2 1 5 3 9 7 14 12 3 0 3 0 6 2v-1h1c2 4 8 10 11 13l33 27h0l1-1 6-20c1-3 2-6 0-10l-2-2v-1c1 0 2 0 2 1 2 2 3 4 3 6 0 4-3 7-4 11 0 3 0 6-1 8s-4 7-4 9c0 0 0 1 1 1v2c1 1 1 3 2 4l-1 1-1-1c-3-3-7-4-10-6-9-4-17-10-25-17-4-3-9-7-13-11v-1-1c0-1-2-2-2-2-2-2-5-6-7-7s-3-3-5-4-4-4-6-6l-9-9z" class="I"></path><path d="M354 713c-1-2-5-4-6-6h0c2 0 3 1 5 2s3 3 6 4c1 0 3 1 3 2l1 1v1l1 2c-3-3-7-4-10-6z" class="N"></path><defs><linearGradient id="d" x1="322.285" y1="673.607" x2="320.69" y2="680.503" xlink:href="#B"><stop offset="0" stop-color="#2d2b2d"></stop><stop offset="1" stop-color="#4b4946"></stop></linearGradient></defs><path fill="url(#d)" d="M311 670c3 0 3 0 6 2v-1h1c2 4 8 10 11 13-1 0-1-1-2-2l-1 1 1 1c-1 1-1 2-2 2 0-1-1-1-1-2v-1c-1-1-3-2-4-4l-9-9z"></path><defs><linearGradient id="e" x1="614.058" y1="237.349" x2="660.743" y2="213.757" xlink:href="#B"><stop offset="0" stop-color="#67676a"></stop><stop offset="1" stop-color="#9e9c95"></stop></linearGradient></defs><path fill="url(#e)" d="M611 209v-1c1-1 1 0 2 0 2 1 4 2 6 2 2 1 6 1 8 3 1 1 2 1 3 1 1 1 2 1 2 1 3 0 4 1 6 2 1 0 0 0 1 1h3c2 1 4 2 6 2h0c-1 0-2-1-2-1l-1-1h0l-1-1c-1 0-2-1-3-1 2-1 3 0 5 0-2-1-4-1-5-2v-1l18 8c1 1 5 2 6 3 0 2 1 3 3 4l1 1 1 1c1 1 2 1 4 2l-1 1 5 3v2h-1 1c0 1 0 1-1 2l-2-1c-5 0-10-2-14 0l-49-19c0-1-1-2-2-3-2-2-3-3-5-2-1-1-2-1-3-2l-2-1h0 1c1-1 1-1 2-1l2-1h0 5v1l2-1-1-1z"></path><path d="M618 215l3-3c2 1 4 1 6 2l-1 2-1 1h1 1c1 1 1 1 3 1l-2 1v1h1c1 1 2 1 3 2-1 0-2 0-2-1-3 0-4-1-6-2-2-2-4-3-6-4z" class="U"></path><path d="M626 216c2 0 4 1 6 2s4 2 5 3c1 0 1 1 2 1 3 0 5 2 8 3h1c3 2 7 1 11 2 2 1 5 2 7 3s5 2 7 3l5 3v2h-1 1c0 1 0 1-1 2l-2-1h-1c-2 0-3-1-4-2h1l-3-3h-1-2l-1-1h-3c-3-1-6-2-9-2-7-1-14-5-20-9-1-1-2-1-3-2h-1v-1l2-1c-2 0-2 0-3-1h-1-1l1-1z" class="X"></path><defs><linearGradient id="f" x1="655.07" y1="219.922" x2="653.637" y2="223.632" xlink:href="#B"><stop offset="0" stop-color="#676665"></stop><stop offset="1" stop-color="#7c7b77"></stop></linearGradient></defs><path fill="url(#f)" d="M611 209v-1c1-1 1 0 2 0 2 1 4 2 6 2 2 1 6 1 8 3 1 1 2 1 3 1 1 1 2 1 2 1 3 0 4 1 6 2 1 0 0 0 1 1h3c2 1 4 2 6 2h0c-1 0-2-1-2-1l-1-1h0l-1-1c-1 0-2-1-3-1 2-1 3 0 5 0-2-1-4-1-5-2v-1l18 8c1 1 5 2 6 3 0 2 1 3 3 4l1 1 1 1c1 1 2 1 4 2l-1 1c-2-1-5-2-7-3s-5-2-7-3c-4-1-8 0-11-2h-1c-3-1-5-3-8-3-1 0-1-1-2-1-1-1-3-2-5-3s-4-2-6-2l1-2c-2-1-4-1-6-2l-3 3h0c-2 0-3-1-5-1-1-1-1-1-2-1l-2-2c-2 0-5 0-7 2l-2-1h0 1c1-1 1-1 2-1l2-1h0 5v1l2-1-1-1z"></path><path d="M611 209v-1c1-1 1 0 2 0 2 1 4 2 6 2 2 1 6 1 8 3 1 1 2 1 3 1 1 1 2 1 2 1 2 1 3 1 4 2l2 1c1 0 1 0 3 1h1s1 1 2 1h0 2c1 0 1 0 2 1 1 0 1 0 2 2h-3c-1-1-2-1-2-2-1 0-2 0-3-1h0-1-2c-1 0-1-1-2-1v-1h-3c-1-1-1-1-2-1-2-1-4-2-5-3-2-1-4-1-6-2l-3 3h0c-2 0-3-1-5-1-1-1-1-1-2-1l-2-2c-2 0-5 0-7 2l-2-1h0 1c1-1 1-1 2-1l2-1h0 5v1l2-1-1-1z" class="Q"></path><path d="M336 492c-2-3-4-7-7-7-2-1-3-1-4-1h0c1-2 2-2 4-2 5 1 11 6 14 10s4 9 5 15c2 17-1 35 1 53 0 6 1 12 3 18 3 11 7 24 5 35-1 3-2 5-3 7-1-1-1 0 0-1l1-4c2-8 0-18-4-25-2-3-5-6-7-9-5-4-10-7-15-10l-3-3v-1l2-1 1-2h-1v1l-9-3c-1 0-1 0-2-1l6-4 6-4 4-3c5-5 10-12 12-19 1-5 1-10 1-15l-1-1v6c-1-3-1-6-1-8v-1-2l-1-3-1-3c-1-1-1-1-1-2-1-1-1-2-1-3l-1 2c1 2 1 3 1 5-1-2-2-4-2-6v-3l-1-2c0-1 0-2-1-3z" class="B"></path><path d="M339 557c1 0 1 0 1 1 1 2 1 5 1 7h0l2 1c0 1 1 1 1 3v-1c0-1 0-1 1-1 2-4-2-10 1-13v-4h0v5c-1 4-1 10 0 14h0l-1-1v1l-1 1c-1-2-1-3-2-4-1 0-1 0-1 1-1 2-2 3-4 4-1-1-2-1-3-1h-1l-1-1 2-2v1 1h2c1-1 2-1 3-3 1-3 1-6 0-9z" class="E"></path><path d="M323 557l6-4c1 0 2 1 2 1 3 0 4 1 5 2 1 0 2 1 3 1 1 3 1 6 0 9-1 2-2 2-3 3h-2v-1-1l-2 2c-1-1-2-1-2-2v-2c1-2 3-3 4-3l1-1-1-1c-1 0-1 0-2 1l-1-1c2-1 1-3 2-4h-3c-1 0-1 1-2 1l-2-1c-1 1-1 1-3 1z" class="F"></path><path d="M333 556c2 0 3 1 5 2 0 2 1 5 0 6v1-1c-1-1-2-2-4-2l1-1-1-1c-1 0-1 0-2 1l-1-1c2-1 1-3 2-4z" class="D"></path><path d="M332 569c-1-1-2-1-2-2v-2c1-2 3-3 4-3 2 0 3 1 4 2v1c-1 1-1 2-2 2l-2 1v-1l-2 2z" class="H"></path><path d="M328 557c1 0 1-1 2-1h3c-1 1 0 3-2 4l1 1c1-1 1-1 2-1l1 1-1 1c-1 0-3 1-4 3v2c0 1 1 1 2 2l1 1h1c1 0 2 0 3 1h0c1 1 3 1 4 1 1 1 2 5 3 7-1 1 0 1 0 2-5-4-10-7-15-10l-3-3v-1l2-1 1-2h-1v1l-9-3c-1 0-1 0-2-1l6-4c2 0 2 0 3-1l2 1z" class="T"></path><path d="M328 557c1 0 1-1 2-1h3c-1 1 0 3-2 4l1 1h0c-1 1-2 2-3 2h-1v-1h0c-2-1-4 0-5-1l-1-1c1-1 1-1 2-1l1-1 3-1z" class="L"></path><path d="M328 557c1 0 1-1 2-1h3c-1 1 0 3-2 4l-5-1-1-1 3-1z" class="G"></path><defs><linearGradient id="g" x1="284.103" y1="208.273" x2="311.187" y2="247.649" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#292826"></stop></linearGradient></defs><path fill="url(#g)" d="M302 198c6 0 12 2 17 5l-3 1c2 0 3 1 5 3-1 0-1 1-1 1-6-2-13-2-19-1l-1 1c-7 2-13 6-16 13-2 4-2 7-1 12 2 4 4 8 7 11h0c-1 1-1 1-2 1v1c1 1 3 3 4 3 3 3 7 4 11 6l-3 1v2l-3 2c-2 1-3 1-4 3l-1 1v1c-1 0-1 1-1 1l-1 1c-1 0-1 0-2 1v1 1l-3-1c0-1-1-1-1-2-1 0-1 1-2 1h-1c-1-2-1-2 0-4l-3-1-6-18v-1h-1c-1-1-1-2-1-3 0-2 0-4-1-6l1-8c1-9 7-15 13-20s12-6 18-7l1-2z"></path><path d="M273 244c-2-4-2-13-1-18 1 2 1 4 1 6 1 1 2 1 2 2v4c0 1 1 2 1 3 1 2 1 7 0 9h0v-2l-2-2c0-1 0-1-1-2z" class="E"></path><path d="M283 207l1 1c-5 4-11 10-12 17v1c-1 5-1 14 1 18l-1 1v-1h-1c-1-1-1-2-1-3 0-2 0-4-1-6l1-8c1-9 7-15 13-20z" class="C"></path><path d="M302 198c6 0 12 2 17 5l-3 1c-9-3-20-3-29 2l-3 2-1-1c6-5 12-6 18-7l1-2z" class="M"></path><path d="M273 244c1 1 1 1 1 2l2 2v2h0c2 1 2 1 3 3 1-1 2-1 2-2l4 3h6l-2 1c1 1 2 1 3 1l1 2 1-1h1 1 2c0-1 1-1 2-1v2l-3 2c-2 1-3 1-4 3l-1 1v1c-1 0-1 1-1 1l-1 1c-1 0-1 0-2 1v1 1l-3-1c0-1-1-1-1-2-1 0-1 1-2 1h-1c-1-2-1-2 0-4l-3-1-6-18 1-1z" class="L"></path><defs><linearGradient id="h" x1="217.595" y1="335.187" x2="224.599" y2="273.033" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#393837"></stop></linearGradient></defs><path fill="url(#h)" d="M176 265c2-2 4-4 6-5-1 2-3 4-3 5l-1 1c-3 3-2 10-2 14h0l-1 1c1 0 1 0 1 2l1 1h-1c-1 0-1-1-2-1l-1 1 1 1h1c0 1 1 2 1 2 1 3 2 5 4 7 1 1 1 0 1 1 1 2 5 5 7 6l5 4c2 0 3 1 5 2 3 1 10 3 13 2 19 3 39 3 56 15 2 1 5 3 6 5h1c-3 1-6 3-9 4l-4 2c-4 1-7 2-10 4h-2l-2 1h-2l-1-1h-1l-3-4c0-2 1-6 3-7h0v-1c-2 1-3 3-4 5 0 1-3 3-3 4l-1-1c-1-2-2-2-4-3v-1c-3-7-11-9-19-11-14-5-31-13-38-28-4-9-2-16 1-25l1-2z"></path><path d="M243 339v-1h1c1-1 1-1 2-1l1 2c1 0 1 0 2-1v-2c1-1 1-3 2-4 0-3 1-6 3-7l1 1c-1 2-1 4-1 6s-2 2-2 4h2c1 0 1-1 2-1h1c1-1 2 0 4 0-4 1-7 2-10 4h-2l-2 1h-2l-1-1h-1z" class="G"></path><defs><linearGradient id="i" x1="789.316" y1="230.255" x2="792.885" y2="205.877" xlink:href="#B"><stop offset="0" stop-color="#1b1b19"></stop><stop offset="1" stop-color="#464443"></stop></linearGradient></defs><path fill="url(#i)" d="M724 206c-5 2-8 3-11 8 0 2-1 4 0 6s5 5 7 6c-1 1-3-1-4-1-3-2-5-5-6-8 0-3 2-7 3-9 5-7 14-9 22-10 11-1 24 4 35 7 7 2 14 3 21 4l32 3c10 1 21 1 31 3 7 2 14 5 20 10l1 1c-1 1-5-1-7-2l-1 1c-8-3-17-4-27-4-5-2-10-1-16-1h-10c-5 0-8 1-13 2-2 0-5 0-7-1l-10-4-14-2c0 5 1 9 3 14-1 0-1 1-2 2-1-3-2-3-4-4-1-4-4-8-6-12-1-1-3-3-5-4-1 0-2-2-3-2-2-2-4-4-7-5-7-3-15-2-22 0v2z"></path><path d="M784 217h1c1 0 2 0 3 1s3 1 4 2c1-1 1-1 2-1l2 1 1-1-1-1-3-1h-1c-1-1-1 0-2-1h-2l1-1c3 1 6 2 9 2 2 1 4 0 6 2-1 1-1 2-3 3-2 0-5 0-7-1l-10-4z" class="G"></path><path d="M824 220c14-2 31-1 44 4l-1 1c-8-3-17-4-27-4-5-2-10-1-16-1z" class="Y"></path><defs><linearGradient id="j" x1="371.451" y1="462.485" x2="335.543" y2="495.967" xlink:href="#B"><stop offset="0" stop-color="#070000"></stop><stop offset="1" stop-color="#242829"></stop></linearGradient></defs><path fill="url(#j)" d="M334 431h1c0 1 0 3-1 4v3h0c0 2 0 4 1 6 1 1 1 2 1 4 1 2 3 5 5 6l1 1h1c1 1 1 1 2 3l2 1 1-1 6 6c1 0 2 0 4 1 4 2 14 12 18 10h1c2 3 6 5 9 8l18 13 1 1h0c1 2 1 2 1 4h-4c-2 0-6-2-8-2-19-7-36-18-51-30-5-5-11-12-17-14-2-1-5-2-7 0-2 0-3 2-4 4-3 4-4 8-6 14-2-5-1-11 1-16 0-1 1-4 2-5s5-2 6-3l1-7c3-4 7-8 11-10 2 0 2-1 4-1z"></path><path d="M348 458l6 6c1 0 2 0 4 1 4 2 14 12 18 10h1c2 3 6 5 9 8l18 13 1 1c-6-2-12-5-17-8-15-9-29-18-41-30l1-1z" class="K"></path><path d="M354 464c1 0 2 0 4 1 4 2 14 12 18 10h1c2 3 6 5 9 8-1 0-1 0-1 1-3 0-8-4-11-5l-7-5-3-2s-1-1-2-1l-5-4-3-3z" class="J"></path><path d="M682 425h3l10 1h1c2 0 5 1 7 2 1 0 2 0 3 1l7 6c4 4 6 8 9 12h0v1 3h0 0-2-1l-1 2v2c-3-1-6-1-9 1-1 1-3 2-4 3-4 4-8 7-12 11-9 8-19 16-30 22 0-1-1-3 0-4 0-1 0-3 1-4 2-3 6-5 9-7h-2l-1 1v-1l2-2h1v-3c0-1 1-1 2-2l3-3c1 0 2-1 3-2v-1l-1-1 1-2v-2c0-1 0 0-1-1v-1c2-2 3-5 5-6l1 1c1-2 3-3 3-5-1 0-2 0-3 1h-3v-2c1-2 2-2 3-2l4-2v-2l-4-2c-3-3-6-5-2-8 1-1 1-1 1-3l-3-2z" class="H"></path><path d="M703 428c1 0 2 0 3 1 0 1 0 2-1 3l5 5-1 1c-2 0-3-1-5-1l-1 1v1l1 1h-1c-2 2-2 3-2 5l-1 2-1 1c-1 3-2 6-5 8 3-6 6-12 7-18 1-2 1-4 1-6l1-4z" class="D"></path><path d="M703 428c1 0 2 0 3 1 0 1 0 2-1 3l-2 1-1-1 1-4z" class="G"></path><path d="M706 429l7 6c4 4 6 8 9 12h0v1 3h0 0-2-1l-1 2v2c-3-1-6-1-9 1-1 1-3 2-4 3 0-4 3-6 6-9 0-1 1-2 1-3s1-2 2-3c-1-2-2-4-5-6l1-1-5-5c1-1 1-2 1-3z" class="E"></path><path d="M698 441c1 0 1-1 1-2 1-1 1-2 1-3l1 1v1c-1 6-4 12-7 18-6 8-13 15-21 21h-2l-1 1v-1l2-2h1v-3c0-1 1-1 2-2l3-3c1 0 2-1 3-2v-1l-1-1 1-2c2-1 2-2 2-3h3s3-2 4-2c0-3 4-6 5-8l1-1c0-1 1-2 1-3s0-1 1-2v-1z" class="P"></path><path d="M681 461c2-1 2-2 2-3h3s3-2 4-2c-2 3-5 5-8 7 0 1 0 0-1 1l-1-1 1-2z" class="C"></path><path d="M682 425h3l10 1h1c2 0 5 1 7 2l-1 4c0 2 0 4-1 6v-1l-1-1c0 1 0 2-1 3 0 1 0 2-1 2v1c-1 1-1 1-1 2s-1 2-1 3l-1 1c-1 2-5 5-5 8-1 0-4 2-4 2h-3c0 1 0 2-2 3v-2c0-1 0 0-1-1v-1c2-2 3-5 5-6l1 1c1-2 3-3 3-5-1 0-2 0-3 1h-3v-2c1-2 2-2 3-2l4-2v-2l-4-2c-3-3-6-5-2-8 1-1 1-1 1-3l-3-2z" class="K"></path><path d="M696 427h1l1 2 2 1h0-1c-1 1-1 2-2 2l-3-3 2-2z" class="F"></path><path d="M696 426c2 0 5 1 7 2l-1 4c0 2 0 4-1 6v-1l-1-1c0 1 0 2-1 3 0 1 0 2-1 2v-1c1-3 1-5 1-7 0-1 0-1 1-1 1-1 1-1 1-2l-1-1v1l-2-1-1-2h-1v-1z" class="N"></path><path d="M697 436v5c-1 3-3 7-5 9l-4 4c0 1-1 2-2 3h-3c0-2 2-3 2-4h1 0l3-3c1-1 2-3 4-4 0-1 1-2 2-3v-1c1-2 1-4 2-6z" class="T"></path><path d="M682 425h3l10 1c-2 1-6 0-7 1v1h1a30.44 30.44 0 0 1 8 8c-1 2-1 4-2 6v1c-1 1-2 2-2 3-2 1-3 3-4 4l-3 3h0v-1c1-2 3-3 3-5-1 0-2 0-3 1h-3v-2c1-2 2-2 3-2l4-2v-2l-4-2c-3-3-6-5-2-8 1-1 1-1 1-3l-3-2z" class="D"></path><path d="M690 442l1-1 1 1c-1 1-2 3-3 5-1 0-2 0-3 1h-3v-2c1-2 2-2 3-2l4-2z" class="M"></path><path d="M682 425h3l10 1c-2 1-6 0-7 1v1h1l-3 1v1 1h-2l-1 2c1 1 3 3 4 3 1 1 3 2 4 3l-1 1-4-2c-3-3-6-5-2-8 1-1 1-1 1-3l-3-2z" class="O"></path><path d="M224 212c13-1 26-3 38-6 13-3 26-8 40-8l-1 2c-6 1-12 2-18 7s-12 11-13 20l-1 8c1 2 1 4 1 6s0 3-1 5l-2 1h0 0c0 2 1 3 1 5h-1l1 5-7-10c-2-1-3-1-4-1-1 1-1 1-2 1l-3-3h0l-2-1-7-7-1-2c-1 0-1-1-2-2v-1c-1-1-2-1-2-3h-1l-2-1c-1-1-3-1-4-3-1-1-2-3-3-4-1-3-3-5-4-8z" class="B"></path><path d="M264 229c0 2 0 3-1 4 1 1 2 2 3 2v1c-1 0-1-1-3-1l-1 2v2l-2-2v-3c1-2 2-3 4-5z" class="T"></path><path d="M262 237c1 0 2 1 2 1h2v2h0c2 0 2 0 3-1 0 0 1-1 0-2v-2h0c1 2 1 4 1 6s0 3-1 5c-2-1-2-1-3-2 1-2-3-3-4-5v-2zm-34-17h0 1c3-1 5-1 7-1s4-1 6-2v1c-2 0-3 1-4 1 3 1 3 0 5 0l2-1h0 1 2c2-1 3-1 5-2h4l4-1h1c2 0 2 0 3 1l-1 1h-2c-6 1-11 2-16 4l-2 2 2 2c-2 1-3 1-4 1l-2-1c-1 1-1 1-2 1s-2 1-3 1c-1-1-3-1-4-3-1-1-2-3-3-4z" class="O"></path><path d="M246 221l-2 2 2 2c-2 1-3 1-4 1l-2-1c-1 0-1-1-2-2l8-2z" class="D"></path><path d="M231 224c0-1 0-2 1-3h4v1c-2 0-1 0-2 1v1c2 0 3 0 4-1 1 1 1 2 2 2-1 1-1 1-2 1s-2 1-3 1c-1-1-3-1-4-3z" class="B"></path><path d="M262 217h2v4c2 4 0 5 0 8-2 2-3 3-4 5v3l2 2c1 2 5 3 4 5 1 1 1 1 3 2l-2 1h0 0c0 2 1 3 1 5h-1l1 5-7-10c-2-1-3-1-4-1-1 1-1 1-2 1l-3-3h0l-2-1-7-7-1-2c-1 0-1-1-2-2v-1c-1-1-2-1-2-3h-1l-2-1c1 0 2-1 3-1s1 0 2-1l2 1c1 0 2 0 4-1l-2-2 2-2c5-2 10-3 16-4z" class="S"></path><path d="M252 240v-1c0-1-1-1-2-2v-1c2 0 2 0 3 2 1 0 1 1 2 2h1v1 2h-2c-1-1-2-2-2-3z" class="P"></path><path d="M254 227l1 3 1 1h1c0-2 0-3 2-4h1 3c-1 2-3 5-6 5-1 0-2 0-4-1 0-2 0-2 1-4z" class="E"></path><path d="M260 234v3l2 2c1 2 5 3 4 5 1 1 1 1 3 2l-2 1h0c-1-1-1-2-2-2l-1-1c-1 0-2-2-3-3-1-2-4-3-4-5 1-2 1-2 3-2z" class="M"></path><path d="M238 228c2 1 4 2 6 4l1 2 2 3c1 2 2 2 5 3 0 1 1 2 2 3l-2 1-2-1-7-7-1-2c-1 0-1-1-2-2v-1c-1-1-2-1-2-3z" class="Q"></path><path d="M256 240c4 4 8 7 11 12l1 5-7-10c-2-1-3-1-4-1-1 1-1 1-2 1l-3-3h0l2-1h2v-2-1z" class="I"></path><path d="M254 243l2 1c2 1 4 2 5 3-2-1-3-1-4-1-1 1-1 1-2 1l-3-3h0l2-1z" class="U"></path><path d="M246 225h1v1l2-2c1-1 2-1 3-1l1 1-2 1c0 1 1 2 1 3l-1 1v2c-1 0-1 1-2 0s-2-1-3-1-2-1-3-1c-1-2-4-1-6-1l-2-1c1 0 2-1 3-1s1 0 2-1l2 1c1 0 2 0 4-1z" class="L"></path><path d="M262 217h2v4l-1 6h-3-1c-2 1-2 2-2 4h-1l-1-1-1-3c0-1-1-2-1-3l-1-1c-1 0-2 0-3 1l-2 2v-1h-1l-2-2 2-2c5-2 10-3 16-4z" class="H"></path><defs><linearGradient id="k" x1="339.355" y1="439.906" x2="396.05" y2="452.512" xlink:href="#B"><stop offset="0" stop-color="#080200"></stop><stop offset="1" stop-color="#362b22"></stop></linearGradient></defs><path fill="url(#k)" d="M349 403c2 2 4 4 7 6v4c1 0 2 1 3 1s1 1 2 1c1 1 3 2 5 3 6 3 12 5 18 6 2 0 3 0 4 1h1c1 1 1 1 2 1-2 1-5 1-6 1h12c2 0 3-1 5 0v1l1 1c-4 1-8 2-12 2h-17c-2 0-5 0-8 1-2 0-5 3-6 5-3 4-3 10-2 15l2 3c1 3 4 5 6 7 3 4 7 8 10 13-4 2-14-8-18-10-2-1-3-1-4-1l-6-6-1 1-2-1c-1-2-1-2-2-3h-1l-1-1c-2-1-4-4-5-6 0-2 0-3-1-4-1-2-1-4-1-6h0v-3c1-1 1-3 1-4h-1c-2 0-2 1-4 1 2-2 4-3 5-4l5-3-2-1 1-1-1-1c-1 0-2 0-2 1v2l-1 1-1-1 1-2v-1c0-2 1-2 1-4l1-3v-2l2-5h1v-1c2-1 3-2 6-2v1 1c1 1 2 1 3 1h1l-1-5z"></path><path d="M344 430c1 1 1 2 2 3h0c0 1-1 1-2 1s-1-1-2-1l2-3zm10 16h1v1c-1 2-1 2-3 3h0l-1-1 3-3z" class="H"></path><path d="M346 433l3-1c1 1 1 1 2 1l1 1v1h-2c-2-1-3 0-4 1-1 0-2 1-2 1h-1l1-3c1 0 2 0 2-1z" class="G"></path><path d="M342 433c1 0 1 1 2 1l-1 3h1v1l-1 1s0 1 1 1c0 1 0 2 1 4l-2-2v-1 1l-2 1v-1h1c0-2 0-2-1-4l1-2v-3z" class="E"></path><path d="M344 426c2 0 4 0 6 1 2-1 6-1 9-1h-1l-3 1h1 2c1 0 2 1 2 1h-4c-3-1-9 0-12 2l-2 3v3l-1 2c1 2 1 2 1 4h-1c-1-1-2-2-2-3 0-2 0-6 1-8 1-1 1-1 1-2h-2l1-1h1 0c1-1 2-1 3-2z" class="G"></path><path d="M350 423l30 3h0c-2 1-6 0-8 1-1 1-4 0-6 0h-2c-1 0-3 1-4 1 0 0-1-1-2-1h-2-1l3-1h1c-3 0-7 0-9 1-2-1-4-1-6-1h5c0-1 0-2 1-3z" class="D"></path><path d="M344 437s1-1 2-1c1-1 2-2 4-1l2 1 1-1h1c0 2 0 3 1 4l-2 2c-1 1-2 2-2 3l-1 1c0 1-1 1-1 1-2-1-3-1-4-2-1-2-1-3-1-4-1 0-1-1-1-1l1-1v-1z" class="G"></path><path d="M344 437s1-1 2-1c1-1 2-2 4-1l2 1v1c-1 1-1 1-1 2-1 1-3 2-5 3-1-1-1-2-2-4v-1z" class="H"></path><path d="M354 420l1 1h1v-1c0-1 1-2 2-3l1 1h1v3c-1 0-1 0-2 1l31 3c1 1 1 1 2 1-2 1-5 1-6 1l-5-1h0l-30-3c-1 1-1 2-1 3h-5c-1 1-2 1-3 2l-1-1c-1 0-2 1-2 2l-3-1 5-3-2-1 1-1c1 0 2-1 4-1h4v-1c1 1 1 1 2 1s2-1 3-1l2-1z" class="S"></path><path d="M340 425c3-1 6-1 10-2-1 1-1 2-1 3h-5c-1 1-2 1-3 2l-1-1c-1 0-2 1-2 2l-3-1 5-3z" class="B"></path><path d="M335 428l3 1c0-1 1-2 2-2l1 1h0-1l-1 1h2c0 1 0 1-1 2-1 2-1 6-1 8 0 1 1 2 2 3v1l1 2v-1 1h-1c-1-1-1-2-1-3h-1c1 3 2 6 4 9 1 1 1 1 1 2 1 2 3 4 4 5l-1 1-2-1c-1-2-1-2-2-3h-1l-1-1c-2-1-4-4-5-6 0-2 0-3-1-4-1-2-1-4-1-6h0v-3c1-1 1-3 1-4h-1c-2 0-2 1-4 1 2-2 4-3 5-4z" class="G"></path><path d="M335 428l3 1c0-1 1-2 2-2l1 1h0-1l-1 1c-2 3-2 6-2 9l-1 1c-1-1-1-2-1-3 0-2 1-4 0-6l-1 1c-2 0-2 1-4 1 2-2 4-3 5-4z" class="D"></path><path d="M336 439l1-1c1 4 2 7 4 10 1 2 2 4 3 5h0c1 2 3 4 4 5l-1 1-2-1c-1-2-1-2-2-3-3-6-6-10-7-16z" class="B"></path><path d="M339 429h2c0 1 0 1-1 2-1 2-1 6-1 8 0 1 1 2 2 3v1l1 2v-1 1h-1c-1-1-1-2-1-3h-1c1 3 2 6 4 9 1 1 1 1 1 2h0c-1-1-2-3-3-5-2-3-3-6-4-10 0-3 0-6 2-9z" class="H"></path><path d="M349 403c2 2 4 4 7 6v4c1 0 2 1 3 1s1 1 2 1c1 1 3 2 5 3 6 3 12 5 18 6 2 0 3 0 4 1h1l-31-3c1-1 1-1 2-1v-3h-1l-1-1c-1 1-2 2-2 3v1h-1l-1-1v-2c1-1 1-3 1-4v-1c-3-3-8-4-12-5l-1 1 1 1 4 1c-2 0-2 0-3 2-1-1-2-2-3-2l-2 1s-1 1-2 1l2-5h1v-1c2-1 3-2 6-2v1 1c1 1 2 1 3 1h1l-1-5z" class="F"></path><path d="M355 414l4 1c2 1 3 2 4 3h0v1h-1v1l-2 1v-3h-1l-1-1c-1 1-2 2-2 3v1h-1l-1-1v-2c1-1 1-3 1-4z" class="I"></path><path d="M347 411l-4-1-1-1 1-1c4 1 9 2 12 5v1c0 1 0 3-1 4v2l-2 1c-1 0-2 1-3 1s-1 0-2-1v1h-4c-2 0-3 1-4 1l-1-1c-1 0-2 0-2 1v2l-1 1-1-1 1-2v-1c0-2 1-2 1-4l1-3v-2c1 0 2-1 2-1l2-1c1 0 2 1 3 2 1-2 1-2 3-2z" class="K"></path><path d="M352 421h-1l-1-1 1-7h1l2 2v3 2l-2 1z" class="B"></path><path d="M344 413c1-2 1-2 3-2l2 1v1c0 2-1 4-1 6-1 1-2 0-3 0-1-2-2-3-2-5l1-1z" class="D"></path><path d="M337 415h2c2 2 2 2 2 4v1h0l2 2c-2 0-3 1-4 1l-1-1c-1 0-2 0-2 1v2l-1 1-1-1 1-2v-1c0-2 1-2 1-4l1-3z" class="N"></path><path d="M337 417h1c1 2 2 3 2 4-2 0-2 0-4-1l1-3z" class="P"></path><path d="M684 216c-1-1-1-2-1-3 3 0 7 5 9 7l1 1c-1 2 0 4 1 6 2 0 3 3 5 4 1 0 2 1 3 1 1 1 9 8 9 9l1 2h-1l5 3c0 1 1 1 2 2 3 1 6 2 10 4 2 0 5 0 7 1 2 0 3 0 5-1h3c1 0 2-1 3-1 2-2 4-4 5-6l1 1v1h3c0-1 1-2 3-3h0c3-2 6-5 9-6-1 6-2 11-4 16-1 4-2 6-3 10l-3 5c-2 5-5 10-9 15-2 0-4 2-7 3 0-3-2-6-2-9-1 1-1 1-2 0l-3-1c-1 0-1 0-1 1l-1 1h-1l-1-1h-1-2l-2-2h-1l-2-2h-1l-4-3c-1-2-3-4-4-5-2-1-4-2-5-3-10-8-22-13-34-18l-13-6c4-2 9 0 14 0l2 1c1-1 1-1 1-2h-1 1v-2l-5-3 1-1c-2-1-3-1-4-2 3 1 6 3 9 2l-1-1v-1h1c1-1 2-2 4-2 1 1 1 1 3 0l1-1c1-3-2-8-3-11z" class="I"></path><path d="M720 263c1 1 2 1 3 1s2 1 3 2h-5l-1-3zm14-1v1c1 2 3 3 4 5-1-1-2-1-4-1v2c0 1 1 2 2 3h-2v-1h0c0-1-1-2-1-2-3 1-4 0-7 0v-1l1-1h2l1-1h0 2c1-1 1-1 1-3l1-1zm-41-30c2 1 4 2 7 3 3 2 6 4 9 7v1c-1 0-2-1-2-2-1 0-2 0-3 1l-1-1h-2c-1 0-2-1-3-2-2-3-5-3-7-4l1-1h-1l2-2z" class="P"></path><path d="M694 227c2 0 3 3 5 4 1 0 2 1 3 1 1 1 9 8 9 9l1 2h-1l-2-1c-3-3-6-5-9-7-3-1-5-2-7-3l1-2v-3z" class="M"></path><path d="M684 216l1-1 1 1c0 2 2 3 3 5 2 2 3 5 3 8 0 1-1 3-3 4-2 0-5-1-7-2l-3-1c1-1 2-2 4-2 1 1 1 1 3 0l1-1c1-3-2-8-3-11z" class="L"></path><path d="M712 253l1-1c2 2 4 3 6 4 1 0 2 1 4 1l1 1v2l1-1h2l-3 2h0 1c3-1 5-2 8-2 0 1 0 1 1 2v1l-1 1c0 2 0 2-1 3h-2 0c-2 0-3-1-4 0-1-1-2-2-3-2s-2 0-3-1-2-1-4-2v-3c-1 1-1 1-2 0l2-1h2v-1l-6-3z" class="S"></path><path d="M725 261c3-1 5-2 8-2 0 1 0 1 1 2h-3v2c-2 2-1 0-4 1 0-1 0-2-1-2l-1-1z" class="L"></path><defs><linearGradient id="l" x1="739.385" y1="259.06" x2="702" y2="245.825" xlink:href="#B"><stop offset="0" stop-color="#484744"></stop><stop offset="1" stop-color="#676562"></stop></linearGradient></defs><path fill="url(#l)" d="M704 242c1-1 2-1 3-1 0 1 1 2 2 2v-1l2 1 5 3c0 1 1 1 2 2 3 1 6 2 10 4 2 0 5 0 7 1 2 0 3 0 5-1h3v1h-1v2l-3 1c-1 0-3 0-4 1h-2c-2 0-2 0-3 1-1 0-2 0-3 1h-2l-1 1v-2l-1-1c-2 0-3-1-4-1-2-1-4-2-6-4l-1 1-5-2v-2c-1-1-2-3-3-4l-1-1 1-2z"></path><path d="M673 233l1-1c3 2 7 5 11 5 2 0 5 4 7 5v1c-2-1-3-2-4-3l-2 2 1 1c2 1 2 1 3 3v1c1 1 2 1 3 2 2 1 3 2 5 3 1 0 2 1 2 2l2 2c4 2 7 5 11 7 3 2 6 3 9 6l1 1c2 0 3 2 5 1 2 1 2 1 4 1h1l1-1v1c1 1 1 2 0 3v2c-1 0-1 0-1 1l-1 1h-1l-1-1h-1-2l-2-2h-1l-2-2h-1l-4-3c-1-2-3-4-4-5-2-1-4-2-5-3-10-8-22-13-34-18l-13-6c4-2 9 0 14 0l2 1c1-1 1-1 1-2h-1 1v-2l-5-3z" class="U"></path><path d="M678 236c1 1 1 2 2 3 1 0 2 1 2 2v1c-2-2-2-2-5-2 1-1 1-1 1-2h-1 1v-2zm30 27c5 2 10 5 14 8l1 1h2v1h2c3 1 4 2 7 2v2c-1 0-1 0-1 1l-1 1h-1l-1-1h-1-2l-2-2h-1l-2-2h-1l-4-3c-1-2-3-4-4-5-2-1-4-2-5-3z" class="R"></path><defs><linearGradient id="m" x1="747.224" y1="255.727" x2="749.227" y2="275.778" xlink:href="#B"><stop offset="0" stop-color="#585754"></stop><stop offset="1" stop-color="#7b7a77"></stop></linearGradient></defs><path fill="url(#m)" d="M767 238c-1 6-2 11-4 16-1 4-2 6-3 10l-3 5c-2 5-5 10-9 15-2 0-4 2-7 3 0-3-2-6-2-9-1 1-1 1-2 0l-3-1v-2c1-1 1-2 0-3h2c-1-1-2-2-2-3v-2c2 0 3 0 4 1-1-2-3-3-4-5v-1-1c-1-1-1-1-1-2-3 0-5 1-8 2h-1 0l3-2c1-1 2-1 3-1 1-1 1-1 3-1h2c1-1 3-1 4-1l3-1v-2h1v-1c1 0 2-1 3-1 2-2 4-4 5-6l1 1v1h3c0-1 1-2 3-3h0c3-2 6-5 9-6z"></path><path d="M755 255h1c0 1 0 2-1 3h-1l1 1v1l-1 1-1-1h0c0-2 0-3 2-5z" class="P"></path><path d="M754 272v-4h0v-1l1 1c2-1 1-2 2-3s2-1 3-1l-3 5h-2c-1 1-1 2-1 3z" class="Q"></path><path d="M752 246v1h3c0-1 1-2 3-3h0c0 1 1 2 0 3s-4 5-4 6c-1 1-2 2-3 2h-1v-1c1-1 4-3 5-5l-1-1c-1 1-2 3-3 3-1 1-1 0-2 1-1 0-2 1-2 2-2 1-3 1-5 1v-2h1v-1c1 0 2-1 3-1 2-2 4-4 5-6l1 1z" class="K"></path><path d="M754 272c0-1 0-2 1-3h2c-2 5-5 10-9 15-2 0-4 2-7 3 0-3-2-6-2-9-1 1-1 1-2 0 0 0 0-1 1-1v-1l2 1c0-1 1-1 2-1l5-1h1v1h0 0c2-2 4-2 6-4z" class="U"></path><defs><linearGradient id="n" x1="764.255" y1="240.107" x2="756.498" y2="258.627" xlink:href="#B"><stop offset="0" stop-color="#3f3e3b"></stop><stop offset="1" stop-color="#605f5b"></stop></linearGradient></defs><path fill="url(#n)" d="M767 238c-1 6-2 11-4 16l-2 2h0l-3 3-1-1h-2 0c1-1 1-2 1-3h-1l-1-2c0-1 3-5 4-6s0-2 0-3c3-2 6-5 9-6z"></path><defs><linearGradient id="o" x1="746.507" y1="257.488" x2="735.618" y2="267.967" xlink:href="#B"><stop offset="0" stop-color="#565551"></stop><stop offset="1" stop-color="#6d6a6a"></stop></linearGradient></defs><path fill="url(#o)" d="M747 254l1 1h1v1h-1v1c0 1 0 1 1 2l-1 1-1 1-2-1h0l1 1-1 1 2 1s-1 0-1 1v3c0 1-1 4-2 5s-3 1-5 1v-1l-1 1v1l-1 1h-1v-1l1-1v-1c0-1-1-1-1-2-1-1-1-1-1-2h1c1 0 1 0 2 1h3c-1-1-2 0-3-1-1-2-3-3-4-5v-1-1c-1-1-1-1-1-2-3 0-5 1-8 2h-1 0l3-2c1-1 2-1 3-1 1-1 1-1 3-1h2c1-1 3-1 4-1l3-1c2 0 3 0 5-1z"></path><path d="M747 254l1 1h1v1h-1 0-1v1l-1-1-1 1h-3l-3-1 3-1c2 0 3 0 5-1z" class="C"></path><path d="M735 259c2-1 3-1 5 0 2 0 3 2 4 4 0 2 0 4-1 6l-3 3h-1l-1 1v1l-1 1h-1v-1l1-1v-1c0-1-1-1-1-2-1-1-1-1-1-2h1c1 0 1 0 2 1h3c1-1 1-2 1-2 1-2 1-3 0-4 0-2-2-3-4-3l-3-1z" class="U"></path><path d="M733 259h2l3 1c2 0 4 1 4 3 1 1 1 2 0 4 0 0 0 1-1 2-1-1-2 0-3-1-1-2-3-3-4-5v-1-1c-1-1-1-1-1-2z" class="J"></path><path d="M733 259h2l3 1c1 1 2 2 2 3-2 1-4 1-6 0v-1-1c-1-1-1-1-1-2z" class="E"></path><defs><linearGradient id="p" x1="214.487" y1="222.801" x2="187.443" y2="300.681" xlink:href="#B"><stop offset="0" stop-color="#030302"></stop><stop offset="1" stop-color="#353432"></stop></linearGradient></defs><path fill="url(#p)" d="M223 212h1c1 3 3 5 4 8 1 1 2 3 3 4 1 2 3 2 4 3l2 1h1c0 2 1 2 2 3v1c1 1 1 2 2 2l1 2 7 7 2 5c1 4 2 9 2 14v7c-1 7-3 12-7 17 1 1 1 2 0 3h0v1c1 3 1 5 2 7l2 2c-1 0-1 0-2 1v1l-2 1 1 1-1 1 1 1c-3 0-4 0-6 2-2-1-4-1-7-1l-1 1h0-1v-1h-1-1l1 1c-1 1-1 2-2 2v1l-12-1c-2 0-6-1-7 0-3 1-10-1-13-2-2-1-3-2-5-2v-2h0c1-2 1-2 2-2s1 1 2 2l2-1c2 0 4-1 6-1s3 0 5-1h0l8-2c7-3 12-7 18-13 3-3 5-7 6-11 1-11 0-26-7-35-6-8-17-11-26-12-8-1-16-1-24-1-12 0-24 1-34 9-4 3-6 6-7 11v2l-1-1c0-2 0-4 1-7s2-5 4-7c4-5 8-8 13-11 20-11 40-8 62-10z"></path><path d="M244 283h2c1 1 0 2 0 3h-1l-1-1v-2z" class="E"></path><path d="M247 286c1 1 1 2 0 3h0l-12 8c-3 1-7 2-10 4h-3c3-1 6-3 9-4 6-3 12-7 16-11z" class="Z"></path><path d="M236 285c0 2 0 3-1 4-3 4-10 9-15 9 0 1-1 0-1 0h-1c7-3 12-7 18-13z" class="T"></path><path d="M243 245h1c4 4 4 12 4 17 0 1-1 1-1 2v1h-1v-1-2s-1 0-1-1 1-2 0-3c0-2-1-7 0-9 0-1-2-2-2-4z" class="B"></path><path d="M223 212h1c1 3 3 5 4 8 1 1 2 3 3 4 1 2 3 2 4 3l2 1h1c0 2 1 2 2 3v1c1 1 1 2 2 2l1 2c-3-2-6-4-10-6-1 0-3-1-4-2l-3-3c-4-2-9-3-14-3-13-2-28-2-41 1-9 2-16 6-22 12v-2c4-4 9-7 15-9 16-6 48-7 64 0v1l-5-13z" class="R"></path><defs><linearGradient id="q" x1="230.165" y1="295.382" x2="228.011" y2="309.644" xlink:href="#B"><stop offset="0" stop-color="#2f2e2c"></stop><stop offset="1" stop-color="#4b4a47"></stop></linearGradient></defs><path fill="url(#q)" d="M247 289v1c1 3 1 5 2 7l2 2c-1 0-1 0-2 1v1l-2 1 1 1-1 1 1 1c-3 0-4 0-6 2-2-1-4-1-7-1l-1 1h0-1v-1h-1-1l1 1c-1 1-1 2-2 2v1l-12-1c-2 0-6-1-7 0-3 1-10-1-13-2v-1l4 1h1c4 1 7-1 11-3 1 0 2-1 3-2 1 0 1 0 2-1h3 3c3-2 7-3 10-4l12-8z"></path><path d="M247 289v1c1 3 1 5 2 7h-1-3l-1 1h-1l-1 1-2-1c-2 1-3 3-5 4-1 0-7 1-9 1h-1-1-1 0l2-2c3-2 7-3 10-4l12-8z" class="B"></path><path d="M247 290c1 3 1 5 2 7h-1-3l-1 1h-1l-1 1-2-1c1-1 2-3 4-3 1 0 0 0 1-1 2-1 2-2 2-4zm-103-50c1-3 2-5 4-7 4-5 8-8 13-11 20-11 40-8 62-10l5 13v-1c-16-7-48-6-64 0-6 2-11 5-15 9v2c-1 1-1 2-2 2 0 1 0 1-1 1 0 1-1 2-2 2z" class="L"></path><defs><linearGradient id="r" x1="227.975" y1="384.007" x2="254.587" y2="388.634" xlink:href="#B"><stop offset="0" stop-color="#777672"></stop><stop offset="1" stop-color="#8c8a8b"></stop></linearGradient></defs><path fill="url(#r)" d="M266 334c2-1 4-3 7-3v1l-2 3-7 11c-4 5-8 10-11 15-6 12-11 25-16 37-3 7-6 14-7 20v5c-3 11-4 20-3 31-1 4-1 7-2 11v4l-1 11v3 1c-1 1-1 2-1 4v-6l-1 3v-16c-2-7-1-14-2-20h-1v3c-2-1-2-2-3-4l1-1v-2c-2 1-4 3-6 4l-2 2c-1 1-1 1-2 1 1-2 3-3 5-5l-1-1 2-3v-1l1-5c0-2 0-3 1-4v-1-4h0c1-1 1-1 1-2l-1-1c0-1 0-1 1-2 0-2-1-4 0-5l1-1v-2l1-2c-1 0-1 0-2-1v-2c-1-1-2 0-3 0l-2-4-3-3v-1l2-1s1-1 2-1l3-3v-2c0-1 0-1 2-3s2-4 4-7c0-1 0-2 1-3l1-3 3-7c0-2 1-3 1-4s0-2 1-2c0-2 1-2 1-3 1-2 2-4 2-6l2-5h1c0-1 0-1 1-2l1-5 1-2 3-8 3 4h1l1 1h2l2-1h2c3-2 6-3 10-4l4-2 1 1z"></path><path d="M230 386l1-1c0 3-1 5 0 8l-2 2c0 1 1 1 1 2-1 2-2 4-4 5 0-2 1-4 1-5 1-3 2-6 2-9l1-2z" class="I"></path><path d="M239 362v4l-3 8c-2 3-3 8-5 11l-1 1v-1c0-2 0-5 2-6l1-1-1-2v-1h2c1-1 1-3 1-4 1-1 1-2 1-4 1-2 2-3 3-5z" class="C"></path><path d="M239 362c1-1 2-3 3-4s1-2 2-3c2-6 7-10 13-13l-2 2c-1 1-3 2-3 3-2 3-4 6-6 8-2 1-2 4-3 5s-1 2-2 2c-1 1-1 2-1 3l-1 1v-4zm-21 51c1-2 2-3 2-4 1-5 3-10 5-14l1-2c1-2 2-3 3-5 0 3-1 6-2 9 0 1-1 3-1 5v1 4l-1 1v1c-2 2-2 4-3 7v1c-1 1-1 2-1 3v1c0 1 0 1 1 3 1 0 1 0 1-1 2-2 3-8 4-10 0-2 0-4 1-6h1l1 1v1c-1 1-1 2-1 3-1 1-2 3-2 5-1 3-1 8-3 11 0 2 0 3 1 4v1l5-15v5c-3 11-4 20-3 31-1 4-1 7-2 11v4l-1 11v3 1c-1 1-1 2-1 4v-6l-1 3v-16c-2-7-1-14-2-20h-1v3c-2-1-2-2-3-4l1-1v-2c-2 1-4 3-6 4l-2 2c-1 1-1 1-2 1 1-2 3-3 5-5l-1-1 2-3v-1l1-5c0-2 0-3 1-4v-1-4h0c1-1 1-1 1-2l-1-1c0-1 0-1 1-2 0-2-1-4 0-5l1-1v-2l1-2z" class="I"></path><path d="M222 424h0l1 1c-1 1-1 1-1 2v1h-1c-1-1-1-2-2-3l1-1 1 1 1-1z" class="Q"></path><path d="M218 413c1-2 2-3 2-4 1-5 3-10 5-14l1-2c1-2 2-3 3-5 0 3-1 6-2 9 0 1-1 3-1 5v1l-5 6c-1 1-1 2-1 3l-3 10c0 2 0 3 1 4v4 2h-1v-1h-1c0 1 0 1 1 2-1 2-2 3-3 4 0-2 0-3 1-4v-1-4h0c1-1 1-1 1-2l-1-1c0-1 0-1 1-2 0-2-1-4 0-5l1-1v-2l1-2z" class="S"></path><path d="M212 447h0c2-3 4-5 5-8 0-1 1-2 2-3v1l-1 2h1c1 0 2-1 3-2v9c1 3 0 8 0 11s0 6 1 9l-1 3c-2-7-1-14-2-20h-1v3c-2-1-2-2-3-4l1-1v-2c-2 1-4 3-6 4l-2 2c-1 1-1 1-2 1 1-2 3-3 5-5z" class="C"></path><defs><linearGradient id="s" x1="223.039" y1="458.491" x2="226.873" y2="458.509" xlink:href="#B"><stop offset="0" stop-color="#848483"></stop><stop offset="1" stop-color="#b8b6b6"></stop></linearGradient></defs><path fill="url(#s)" d="M230 418v5c-3 11-4 20-3 31-1 4-1 7-2 11v4l-1 11v3 1c-1 1-1 2-1 4v-6l-1 3v-16l1-3c1-7-1-14 0-21l2-12 5-15z"></path><path d="M266 334c2-1 4-3 7-3v1l-2 3-7 11h-2c-1 0-2-1-3 0h-2l-2-2 2-2c-6 3-11 7-13 13-1 1-1 2-2 3s-2 3-3 4c-1 2-2 3-3 5 0 2 0 3-1 4 0 1 0 3-1 4h-2v1l1 2-1 1c-2 1-2 4-2 6v1l-1 2c-1 2-2 3-3 5l-1 2c-2 4-4 9-5 14 0 1-1 2-2 4-1 0-1 0-2-1v-2c-1-1-2 0-3 0l-2-4-3-3v-1l2-1s1-1 2-1l3-3v-2c0-1 0-1 2-3s2-4 4-7c0-1 0-2 1-3l1-3 3-7c0-2 1-3 1-4s0-2 1-2c0-2 1-2 1-3 1-2 2-4 2-6l2-5h1c0-1 0-1 1-2l1-5 1-2 3-8 3 4h1l1 1h2l2-1h2c3-2 6-3 10-4l4-2 1 1z" class="C"></path><path d="M234 365c0 1 0 1 2 2 0 2 0 3-1 4 0 1 0 3-1 4h-2v1c-1 1-2 1-2 2 0-1 0-3 1-4 1-2 1-5 2-7 0-1 0-2 1-2z" class="O"></path><path d="M236 345l1 1c0 1 1 1 1 1 1 0 1 0 2-1s3-1 4-1l-3 3-1 2s-1 1-2 3l-1 1c-1 0-2 0-3-1v-1c0-1 0-1 1-2l1-5z" class="K"></path><path d="M240 352v1h1 2v1l-1 1c-1 2-3 4-3 7-1 2-2 3-3 5-2-1-2-1-2-2l1-4 3-6 2-3z" class="F"></path><path d="M240 335l3 4h1l1 1h2l2-1h2l-4 4c-1 0-2 1-3 2-1 0-3 0-4 1s-1 1-2 1c0 0-1 0-1-1l-1-1 1-2 3-8z" class="J"></path><path d="M266 334c2-1 4-3 7-3v1l-2 3-7 11h-2c-1 0-2-1-3 0h-2l-2-2 2-2c-6 3-11 7-13 13-1 1-1 2-2 3s-2 3-3 4c0-3 2-5 3-7l1-1v-1h-2-1v-1c0-1 1-1 1-2 3-4 6-6 10-9 1 0 1 0 2-1h1l4-2 4-2 4-2z" class="M"></path><path d="M257 342c2-2 5-4 8-5 2 0 4-1 6-2l-7 11h-2c-1 0-2-1-3 0h-2l-2-2 2-2z" class="Q"></path><path d="M232 376l1 2-1 1c-2 1-2 4-2 6v1l-1 2c-1 2-2 3-3 5l-1 2c-2 4-4 9-5 14 0 1-1 2-2 4-1 0-1 0-2-1v-2c-1-1-2 0-3 0l-2-4-3-3v-1l2-1s1-1 2-1l3-3v-2c0-1 0-1 2-3v2l-1 1h1l1-1c2-1 3-2 4-3 3-2 6-7 7-10v-2l1-1c0-1 1-1 2-2z" class="F"></path><path d="M214 401h1c2 0 2 1 3 1h1 1v1l-3 7h-1c-1-1-2 0-3 0l-2-4 4 2c1 0 1-2 2-3h0c-1-2-2-2-3-4z" class="B"></path><path d="M222 391v3c-1 0-2 1-2 2l-5 5h-1-4s1-1 2-1l3-3v-2c0-1 0-1 2-3v2l-1 1h1l1-1c2-1 3-2 4-3z" class="K"></path><path d="M210 401h4c1 2 2 2 3 4h0c-1 1-1 3-2 3l-4-2-3-3v-1l2-1z" class="H"></path><path d="M771 273v1h2l-1-1c0-2 0-2 1-3s2-1 3-2v-1c2-1 1 0 3 1h2c-1 3-6 7-9 9h0v3l-1 1h2c1 1 2 1 3 1l1 1h1 2c2 0 2 1 4 1h1l1 1v-1-1c1-1 1-2 2-4l3-9 1-1v5l1 4c0 1 1 1 1 2v1 2h1l1-1 4 8 3 3v1c0 1 2 3 3 4h1l1 1 1 1s1 1 2 1c-1 1-1 1-2 1l2 1h0c-2 1-4 1-6 2h-2c3 1 5 1 8 1-3 1-6 1-9 4l1 2-12 3c-10 3-19 8-28 12-2 1-3 1-5 2-4 0-8-5-11-6s-5-1-7-3h-1l-2 1v-1c-1-1-3-1-4-1v-3-3c2 0 4 0 6 1l2-2c0-1 1-3 1-4 1-1 2-3 3-4l3-2 4-1c-2-1-2-1-4-1-1 0-2 0-4-1l3-4h1c-2-1-2-1-3-2l1-3 1-1c2-1 4-3 5-4l9-10c2 0 3-1 5-3 1 1 2 1 4 1h1z" class="C"></path><path d="M771 292c3 1 6-2 8 2-1 1-1 2-2 2v-1l-2-2c-2 1-3 2-5 2-2 1-3 1-4 1h-1c2-1 3-1 4-4h2zm13 8c1 0 2-2 3-3l-1 3c-1 3-3 4-5 6h-1c-1 0-2 0-2 1h-1c-2 0-5 0-6-1 2 0 4 1 6-1 0-1 1-1 2-2h2c1-1 2-2 2-3h1z" class="O"></path><path d="M773 312h-2c-2-1-5 0-7-1s-1 0-2-1h0c0-1 2-2 3-3l1 1 1 1h4c2-1 4 0 5 0l1-1h1 2c0 1 1 2 1 3-2 1-5 2-8 1z" class="K"></path><path d="M772 297c1 0 2 0 3 1v1h2l1 1s1-1 1-2h2c1 0 2-2 4-1 0 1-1 2-1 3h-1c0 1-1 2-2 3h-2c-1 1-2 1-2 2-2 2-4 1-6 1s-3-1-4-2c-1-2-1-3 0-4v-1c1-2 3-2 5-2z" class="E"></path><path d="M767 300h1l1-1c1-1 1-1 3-1 1 0 2 0 2 1v2h-1c-1 0-1 1-2 1l-1 1-3 1c-1-2-1-3 0-4z" class="G"></path><path d="M774 301h0c1 0 1 1 1 1 1 0 3 1 3 0 3-1 2-3 5-2 0 1-1 2-2 3h-2c-1 1-2 1-2 2-2 2-4 1-6 1s-3-1-4-2l3-1 1-1c1 0 1-1 2-1h1z" class="J"></path><path d="M753 310h0c1 0 1 1 2 1 1 1 3 1 4 1 3 0 5 0 7 1 1-1 1-1 3-1l1 1c1 0 2 0 3-1 3 1 6 0 8-1l3-1c-1 1-1 2-2 2-1 1-1 1-2 1-1 1-1 1-2 1l-8 3-3-1-1-1-2 2 1 2-3 1-1-1 1-2-1-1h-3-5c-2-1-3-2-4-2-1-1-2-1-3-2l-1-1h1c2 0 4 0 7-1z" class="U"></path><path d="M746 312l11 2c3 1 7 0 10 0l-1 1-2 2 1 2-3 1-1-1 1-2-1-1h-3-5c-2-1-3-2-4-2-1-1-2-1-3-2z" class="X"></path><path d="M774 289h-1v-1c4-2 6-1 10-1 1 1 2 1 4 2l1 3v2l-1 2v1c-1 1-2 3-3 3 0-1 1-2 1-3-2-1-3 1-4 1h-2c0 1-1 2-1 2l-1-1h-2v-1c-1-1-2-1-3-1l5-1c1 0 1-1 2-2-2-4-5-1-8-2v-2h0 1c2 0 1 0 2-1z" class="H"></path><path d="M783 287c1 1 2 1 4 2l1 3v2l-1 2c-1 0-2 0-3-1h0c-2 0-1 1-2 2l-1-1c0-1-1-2-1-3 1 0 1 1 2 1l1-1c0-1 1-1 2-2l1-1-3-3z" class="E"></path><path d="M774 289c2 0 4 0 5 1h1l1 1h0-1c-1 1-1 1 0 2 0 1 1 2 1 3l1 1c1-1 0-2 2-2h0c1 1 2 1 3 1v1c-1 1-2 3-3 3 0-1 1-2 1-3-2-1-3 1-4 1h-2c0 1-1 2-1 2l-1-1h-2v-1c-1-1-2-1-3-1l5-1c1 0 1-1 2-2-2-4-5-1-8-2v-2h0 1c2 0 1 0 2-1z" class="L"></path><defs><linearGradient id="t" x1="751.878" y1="315.179" x2="743.615" y2="324.19" xlink:href="#B"><stop offset="0" stop-color="#b3b2b1"></stop><stop offset="1" stop-color="#e0dfdf"></stop></linearGradient></defs><path fill="url(#t)" d="M752 301c1 0 2 0 3 1s2 2 3 4l-5 4c-3 1-5 1-7 1h-1l1 1c1 1 2 1 3 2 1 0 2 1 4 2h5 3l1 1-1 2 1 1c-1 1-2 1-3 2-1 0-1-1-2 0h1c1 1 2 1 3 1l-1 1c-1 0-1 0-2 1h0 0c1 1 1 1 2 1s2 1 3 1c-2 1-3 1-5 2-4 0-8-5-11-6s-5-1-7-3h-1l-2 1v-1c-1-1-3-1-4-1v-3-3c2 0 4 0 6 1l2-2c0-1 1-3 1-4 1-1 2-3 3-4l3-2 4-1z"></path><path d="M761 316l1 1-1 2 1 1c-1 1-2 1-3 2-1 0-1-1-2 0l-2-1v-1c1 0 2-1 3-2v-1h2l1-1z" class="Y"></path><path d="M733 313c2 0 4 0 6 1l2-2v2c0 1-1 1-1 1-1 2-2 4-3 5-1-1-3-1-4-1v-3-3z" class="V"></path><path d="M733 316c2 0 4 1 6 0l1-1c-1 2-2 4-3 5-1-1-3-1-4-1v-3z" class="a"></path><path d="M745 311l-1-1v-2c2-4 7-5 11-6 1 1 2 2 3 4l-5 4c-3 1-5 1-7 1h-1z" class="B"></path><path d="M752 304c1-1 1 0 2 0l1 1-1 1h-2l-1-1 1-1z" class="G"></path><defs><linearGradient id="u" x1="781.215" y1="324.509" x2="764.555" y2="314.736" xlink:href="#B"><stop offset="0" stop-color="#81807f"></stop><stop offset="1" stop-color="#b4b2b1"></stop></linearGradient></defs><path fill="url(#u)" d="M798 300c1 0 2 0 3 1l3 1h2c1 1 1 0 3 0l2 1h0c-2 1-4 1-6 2h-2c3 1 5 1 8 1-3 1-6 1-9 4l1 2-12 3c-10 3-19 8-28 12-1 0-2-1-3-1s-1 0-2-1h0 0c1-1 1-1 2-1l1-1c-1 0-2 0-3-1h-1c1-1 1 0 2 0 1-1 2-1 3-2l3-1-1-2 2-2 1 1 3 1 8-3c1 0 1 0 2-1 1 0 1 0 2-1 1 0 1-1 2-2l5-2h1l1-1c2-2 3-3 5-4h1v-2l1-1z"></path><path d="M765 319l-1-2 2-2 1 1 3 1-5 2z" class="R"></path><path d="M798 300c1 0 2 0 3 1l3 1h2c1 1 1 0 3 0l2 1h0c-2 1-4 1-6 2h-2 0l-1-1h-2c-4 2-7 3-11 4h1l1-1c2-2 3-3 5-4h1v-2l1-1z" class="I"></path><defs><linearGradient id="v" x1="806.152" y1="308.773" x2="786.273" y2="310.099" xlink:href="#B"><stop offset="0" stop-color="#4e4d4c"></stop><stop offset="1" stop-color="#787774"></stop></linearGradient></defs><path fill="url(#v)" d="M803 305h0c3 1 5 1 8 1-3 1-6 1-9 4-1 0-1 1-2 1h-1c-1 0-2 1-3 1s-2 0-3 1c-3 0-4 1-6 1l-2-1c2-3 8-5 11-6h1 1c2-1 3-1 5-2z"></path><path d="M791 270l1-1v5l1 4c0 1 1 1 1 2v1 2h1l1-1 4 8 3 3v1c0 1 2 3 3 4h1l1 1 1 1s1 1 2 1c-1 1-1 1-2 1-2 0-2 1-3 0h-2l-3-1c-1-1-2-1-3-1l-1 1v2h-1c-2 1-3 2-5 4l-1 1h-1l-5 2-3 1c0-1-1-2-1-3l2-1c2-1 3-3 4-4h0c1-1 1-2 2-2v-1c1-2 1-5 2-7-1-2-1-3-2-4v-1c0-1-1-2-2-3v-1-1c1-1 1-2 2-4l3-9z" class="G"></path><path d="M786 284h2c1 1 1 2 2 3h0c1 2 1 3 2 5 0 2 0 4-1 6 0-2-1-3-1-5-1-2-1-3-2-4v-1c0-1-1-2-2-3v-1z" class="E"></path><path d="M790 293c0 2 1 3 1 5-1 2-1 4-3 5 0 1-1 2-1 3l1-1c1-1 1-1 2-1 3-1 4-3 6-6-1 2-1 4-2 5h-1v1l-2 1-6 3h-1c1-2 2-3 2-5h0c1-1 1-2 2-2v-1c1-2 1-5 2-7z" class="L"></path><path d="M791 270l1-1v5l1 4c0 1 1 1 1 2v1 2 2l-1-2v1l1 2-1 1h-3 0c-1-1-1-2-2-3h-2v-1c1-1 1-2 2-4l3-9z" class="H"></path><path d="M795 283l1-1 4 8 3 3v1c0 1 2 3 3 4h1l1 1 1 1s1 1 2 1c-1 1-1 1-2 1-2 0-2 1-3 0h-2l-3-1c-1-1-2-1-3-1l-1 1v2h-1c-2 1-3 2-5 4l-1 1h-1l-5 2-3 1c0-1-1-2-1-3l2-1c2-1 3-3 4-4 0 2-1 3-2 5h1l6-3 2-1v-1h1c1-1 1-3 2-5h0v-6-2c-1-2-1-3 0-5-1-1-1-2-1-2z" class="T"></path><path d="M796 285c1 2 1 3 1 5s2 3 1 5l-2 3v-6-2c-1-2-1-3 0-5z" class="L"></path><path d="M798 300c0-1 1-2 2-2h1 0c1 0 1 0 2-1-1-1-2-2-2-3s-1-2-2-3l1-1 3 3v1c0 1 2 3 3 4h1l1 1 1 1s1 1 2 1c-1 1-1 1-2 1-2 0-2 1-3 0h-2l-3-1c-1-1-2-1-3-1z" class="K"></path><path d="M771 273v1h2l-1-1c0-2 0-2 1-3s2-1 3-2v-1c2-1 1 0 3 1h2c-1 3-6 7-9 9h0v3l-1 1-1 1v1c2 0 3 0 4 1h1l3 1h3l3 1 3 3c-2-1-3-1-4-2-4 0-6-1-10 1v1h1c-1 1 0 1-2 1h-1 0v2h-2c-1 3-2 3-4 4l-2 2c0 2-1 3-2 4h-2v3c0 1 0 1-1 1-1-2-2-3-3-4s-2-1-3-1c-2-1-2-1-4-1-1 0-2 0-4-1l3-4h1c-2-1-2-1-3-2l1-3 1-1c2-1 4-3 5-4l9-10c2 0 3-1 5-3 1 1 2 1 4 1h1z" class="D"></path><path d="M770 273h1c0 1 0 2-1 3h-2l-1 1-1-1v-1-1c1 0 2 0 3-1h1zm0 10c-1 0-1 0-3 1h-1c0-2 0-2 1-4h1c1-1 2-2 4-3v3l-1 1-1 1v1z" class="B"></path><path d="M752 285c0 2-1 3-3 4 2 1 3 0 4 1l1 1 1-1c0-1 0-1 1-1h1c0 1 1 1 2 1 0 1 1 1 0 2l-2 2-1-1c-1 1-1 1-2 1h-1c-1 1-1 1-1 2 2 1 2 1 3 1 1-1 1-2 2-2h2c1-1 1-1 1-2h2c1 1 1 1 2 1 0-1 1-2 2-2 1-1 1 0 2-1h2l1-1v2h-2c-1 3-2 3-4 4l-2 2c0 2-1 3-2 4h-2v3c0 1 0 1-1 1-1-2-2-3-3-4s-2-1-3-1c-2-1-2-1-4-1-1 0-2 0-4-1l3-4h1c-2-1-2-1-3-2l1-3 1-1c2-1 4-3 5-4z" class="J"></path><g class="K"><path d="M752 285c0 2-1 3-3 4l-1 1c1 1 1 1 3 1-1 2-2 3-3 4-2-1-2-1-3-2l1-3 1-1c2-1 4-3 5-4z"></path><path d="M744 299l3-4c0 2 0 2-1 3 1 1 2 1 3 1l1-1 1 1c1 0 4-1 6 0 0 0 0 1 1 1h2l2-2h1c0 2-1 3-2 4h-2v3c0 1 0 1-1 1-1-2-2-3-3-4s-2-1-3-1c-2-1-2-1-4-1-1 0-2 0-4-1z"></path></g><defs><linearGradient id="w" x1="817.574" y1="282.249" x2="807.136" y2="393.781" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2e2c29"></stop></linearGradient></defs><path fill="url(#w)" d="M863 285c1 2 1 3 2 5-2 5-5 9-8 13-9 9-22 13-34 18-5 2-11 4-14 8-4 6-1 20 1 27 3 9 7 17 11 25 1 2 3 5 3 7v1 1 1c1 1 2 2 3 4h-1v1c-1-2-2-3-3-5h0l-1-1-1-2v-1c0-1-1-2-1-2l-1-1-1 1-4-6-4-6v-1c-1 0-1 0-2 1l-1-2-1 2c2 2 3 4 4 8 1 2 2 4 2 7l-5-6v1l-6-7c-3-4-4-7-7-11a30.44 30.44 0 0 0-8-8l-6-6c-2-3-7-5-10-6-1-1-1-1-2-1-2-1-6-7-8-8-1-1-2-3-4-3-5-4-11-6-16-9-2 0-4-2-5-2h-1-1v-3c1 0 3 0 4 1v1l2-1h1c2 2 4 2 7 3s7 6 11 6c2-1 3-1 5-2 9-4 18-9 28-12l12-3c5-1 22-4 25-2 2 0 5-1 7-1 11-3 20-8 25-18h1c1-2 1-4 2-6z"></path><path d="M779 337c2 0 4 0 5 1l1-1-1-1 1-1 2 1h1v-1c0-1 0-1 1-2l1 1-2 3c0 1 1 2 1 3l-1 1h2v1c-2 0-2-1-4-1-2-1-4-3-7-4z" class="G"></path><path d="M804 356l-5-23c-1-2-1-4 0-6l1 5c1 3 2 7 3 11v1c2 9 4 19 9 28 2 4 5 8 7 12l-1 1-4-6-4-6v-1c-1 0-1 0-2 1l-1-2-3-6c-1-1-1-1-1-3 0-1 0-2-1-3h-1c-1-4-4-7-6-10 3 1 3 1 4 4 1 1 2 2 2 4h2l1-1z" class="O"></path><path d="M795 349c3 1 3 1 4 4 1 1 2 2 2 4h2l1-1c1 3 2 7 3 10h-1v-1l-3-3c0-1 0-2-1-3h-1c-1-4-4-7-6-10z" class="L"></path><path d="M803 312c5-1 22-4 25-2-7 1-15 1-22 3-8 2-16 5-23 8-6 2-12 4-18 8-2 1-3 2-5 4l1 1-1 2c-1-1-2-3-4-3-5-4-11-6-16-9-2 0-4-2-5-2h-1-1v-3c1 0 3 0 4 1v1l2-1h1c2 2 4 2 7 3s7 6 11 6c2-1 3-1 5-2 9-4 18-9 28-12l12-3z" class="V"></path><path d="M761 334l-1-1c2-2 3-3 5-4 1 1 2 2 3 2l1 1 6 3h0l4 2c3 1 5 3 7 4 4 3 6 5 9 8 2 3 5 6 6 10h1c1 1 1 2 1 3 0 2 0 2 1 3l3 6-1 2c2 2 3 4 4 8 1 2 2 4 2 7l-5-6v1l-6-7c-3-4-4-7-7-11a30.44 30.44 0 0 0-8-8l-6-6c-2-3-7-5-10-6-1-1-1-1-2-1-2-1-6-7-8-8l1-2z" class="S"></path><path d="M800 366c3 1 3 2 4 4l2 3c2 2 3 4 4 8 1 2 2 4 2 7l-5-6-3-8-4-8z" class="O"></path><path d="M761 334l-1-1c2-2 3-3 5-4 1 1 2 2 3 2l1 1 6 3h0v1 1 3h3 1l-1 1v1c-3 0-5-1-7-2-2-2-5-3-8-3 0-2-1-2-2-3z" class="M"></path><path d="M769 332l6 3h0v1 1 3c-2-1-3-2-4-4 0-1-1-2-2-3h0v-1z" class="C"></path><path d="M761 334c1 1 2 1 2 3 1 1 3 3 4 3 4 3 9 3 12 6l1 1h1c1 0 2 1 3 2s2 2 3 2c2 1 2 2 3 4s3 4 5 6c2 3 3 7 4 10h1c1 2 1 3 1 5-3-4-4-7-7-11a30.44 30.44 0 0 0-8-8l-6-6c-2-3-7-5-10-6-1-1-1-1-2-1-2-1-6-7-8-8l1-2z" class="U"></path><defs><linearGradient id="x" x1="806.148" y1="371.401" x2="793.357" y2="352.434" xlink:href="#B"><stop offset="0" stop-color="#31312f"></stop><stop offset="1" stop-color="#575653"></stop></linearGradient></defs><path fill="url(#x)" d="M775 337v-1-1l4 2c3 1 5 3 7 4 4 3 6 5 9 8 2 3 5 6 6 10h1c1 1 1 2 1 3 0 2 0 2 1 3l3 6-1 2-2-3c-1-2-1-3-4-4l-2-3c-1-3-4-5-6-7-1-2 1 0 0-1l-1-1c-1-1-1-1-1-2-2-1-2-1-3-3l-1-1c-1-2-2-3-4-4-1 0-1 0-2-1v-1l2-1s-1-1-2-1l-1-1-4-2z"></path><path d="M782 341c3 2 5 3 6 5l4 5c1 1 2 1 2 3h-1c-2-2-3-4-6-5l-1-1c-1-2-2-3-4-4-1 0-1 0-2-1v-1l2-1z" class="C"></path><defs><linearGradient id="y" x1="704.481" y1="574.99" x2="740.695" y2="631.538" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#32312e"></stop></linearGradient></defs><path fill="url(#y)" d="M698 551c0-2 0-4 1-6 1 1 2 2 2 4h1l1-1v1c14 11 31 15 46 24h0c6 5 12 10 16 16l1 2 2 5v2l-1 2 1 6c-3 0-4 1-5 3v1c-3 1-5 1-6 3l-2 4h-1v1c-2 2-3 4-4 6 0 2-1 3-2 4h-1l-1-1h0c0-3 1-5 0-8 1-8 1-16-3-22-5-6-14-9-22-10-6-1-18 2-23 6-5 3-8 9-9 14 0 6 1 13 5 18 5 4 10 4 16 4v1c-1 1-3 2-5 2-4 1-9 0-13-3-6-4-10-10-11-18v-7h0c1-2 1-5 2-6 0-4 2-9 4-12 0-6 4-9 7-14l-2 2-1 1s0-1 1-2v-1c0-2 2-5 3-7l2-4c-1 0-2-1-2-2-1-1-1-3 0-4h0c1-2 2-3 3-4z"></path><path d="M765 592l1 4v2l-1-1c-1 1-2 3-3 3h0c0-1 0-2 1-2v-1c0-2 1-3 2-5z" class="L"></path><path d="M756 590h2c1 1 1 2 2 4l-2 2h-1c-1-1-2-2-2-3s1-2 1-3z" class="H"></path><path d="M681 604c1-2 1-5 2-6 0-4 2-9 4-12l1 2c-4 7-6 18-4 26l2 7c-4-6-4-11-5-17z" class="R"></path><path d="M766 596l1 4 1 6c-3 0-4 1-5 3v1c-3 1-5 1-6 3l-2 4h-1c0-1 0-2 1-3h0c1-2 1-5 3-7 1-1 1-1 2-1l2-1h0l1-1c0-1 0-2 1-2 0-2 1-3 2-4v-2z" class="K"></path><path d="M698 551c0-2 0-4 1-6 1 1 2 2 2 4h1l1-1v1c14 11 31 15 46 24h0c6 5 12 10 16 16l1 2 2 5v2l-1 2-1-4-1-4c-3-4-5-7-8-10-7-7-16-13-25-13-12 0-26 3-36 11-2 2-5 4-7 7l-1 1-1-2c0-6 4-9 7-14l-2 2-1 1s0-1 1-2v-1c0-2 2-5 3-7l2-4c-1 0-2-1-2-2-1-1-1-3 0-4h0c1-2 2-3 3-4z" class="Y"></path><path d="M727 564l6 3c-8 1-15 1-22 3 1-1 2-2 3-2 1-1 2-1 3-2h1 1c1 0 3-1 5-1 1 0 2 0 3-1z" class="P"></path><path d="M706 553l10 5 2 2 9 4c-1 1-2 1-3 1h-4-1c-1 0-2 0-3-1h-1l2-3-2-1h-2v-1h-2l-2-2h-1c0-1-1-1-2-2v-2z" class="K"></path><path d="M705 563l4 2 2-2h0c2 0 3 0 4 1h1c1 1 2 1 3 1h1 4c-2 0-4 1-5 1h-1-1c-1 1-2 1-3 2-1 0-2 1-3 2l-1 1h-7v-1c1-2 2-3 4-4 1-2 0 0-1-1s-1-1-1-2z" class="C"></path><path d="M698 551c0-2 0-4 1-6 1 1 2 2 2 4h1l1-1v1c1 2 2 3 3 4v2c1 1 2 1 2 2h1l2 2h2v1h2l2 1-2 3c-1-1-2-1-4-1h0l-2 2-4-2c0-1 0-2-1-2l-2-2-2-3-2-1h-1c0 1 1 1 1 2h0l-1 1v1 2c-1 0-2-1-2-2-1-1-1-3 0-4h0c1-2 2-3 3-4z" class="T"></path><path d="M698 551c0-2 0-4 1-6 1 1 2 2 2 4h1v3h-1c-1-1-2-1-2-1h-1zm2 5c3 0 4 0 7 1l2 2c1 1 1 1 2 1h1v2l-1 1h0l-2 2-4-2c0-1 0-2-1-2l-2-2-2-3z" class="J"></path><path d="M704 561c2 0 4 0 6 1l1 1-2 2-4-2c0-1 0-2-1-2z" class="F"></path><defs><linearGradient id="z" x1="701.563" y1="568.553" x2="689.211" y2="583.104" xlink:href="#B"><stop offset="0" stop-color="#747271"></stop><stop offset="1" stop-color="#8e8d8d"></stop></linearGradient></defs><path fill="url(#z)" d="M697 561v-2-1l1-1h0c0-1-1-1-1-2h1l2 1 2 3 2 2c1 0 1 1 1 2s0 1 1 2 2-1 1 1c-2 1-3 2-4 4v1h7c-4 1-7 2-9 4-5 3-9 5-12 10l-1 1 1 1-1 1-1-2c0-6 4-9 7-14l-2 2-1 1s0-1 1-2v-1c0-2 2-5 3-7l2-4z"></path><path d="M697 561v-2-1l1-1h0c0-1-1-1-1-2h1l2 1 2 3h-1l2 3 1 2c-1 2-4 3-6 4l-4 4-2 2-1 1s0-1 1-2v-1c0-2 2-5 3-7l2-4z" class="C"></path><path d="M698 564c0-2 1-3 2-5h1l2 3c-1 1-2 2-5 2h0z" class="D"></path><path d="M703 562l1 2c-1 2-4 3-6 4-1-1-1-2 0-4h0c3 0 4-1 5-2z" class="J"></path><defs><linearGradient id="AA" x1="351.915" y1="230.663" x2="366.301" y2="247.806" xlink:href="#B"><stop offset="0" stop-color="#3d3c38"></stop><stop offset="1" stop-color="#8c8b8b"></stop></linearGradient></defs><path fill="url(#AA)" d="M433 202l5-2c1-1 2-1 2-2h1l12-10c1 0 2-1 3-1l2 1-3 2c-5 3-9 7-13 10-8 4-17 7-25 10-2 1-8 3-9 3-6 4-9 7-12 12-1 2-1 2-1 3 2 0 3-1 4-1v1l1 1-6 3c0 1 0 1-1 2v-2c-4 0-8 3-11 4-14 7-28 14-41 24-10 7-19 15-28 23-1-1-1-1-2-1s-2 0-3-1c-3 0-4-1-7 0l-4 4-1 1-2-1v2c-2 1-3 1-4 0l-1-1-2-2c0-1 0-1 1-2 0-2-1-1-2-2 0 0-1-2-2-2l-6-15 3 1c-1 2-1 2 0 4h1c1 0 1-1 2-1 0 1 1 1 1 2l3 1v-1-1c1-1 1-1 2-1l1-1s0-1 1-1v-1l1-1c1-2 2-2 4-3l3-2v-2l3-1h3c3 0 4-1 7-1h3c6-3 11-8 16-13 0-1 2-1 3-2v-2-1l2-1 1-1v1l3-6c2 0 3 0 5 1 6 0 11-4 16-6 1 0 1 1 2 2l1-1h1l1 1 1-1c0 1 1 1 0 2l1 1 1-3v-1h1c0-1 1-3 3-3 0 0 1 0 2-1h1c10-8 22-11 32-20v-1c-1-2-1-3 0-5l1-3h1c0 2-1 5 0 7s2 2 4 3h0c2 1 3 1 5 1v1 1c6-1 11-5 17-6-1 1-3 3-4 3l-1 1h1z"></path><path d="M393 222c3-3 4-6 9-6-4 3-6 6-8 10h-1l1-2c-1 1-1 1-2 1-1 1-2 2-3 2h0-1l1-1c1 0 2-1 3-2v-1l1-1z" class="Z"></path><path d="M362 235l3-1c2-1 3-2 6-2h-1c-1 2-2 2-3 4 0 1-2 2-3 3h-1v-1c-3 2-5 3-8 3 1-2 5-4 7-6z" class="I"></path><path d="M335 239v1l-1 1v2h2 3c-1 1-2 1-3 2 0 2 0 3-1 4s-2 1-3 1c-2 1-3 1-4 2v-1c1-1 2-2 3-4v-1c1-2 2-2 2-4l-1-1c0-1 2-1 3-2z" class="O"></path><path d="M393 226h1c-2 3-4 4-6 5-4 2-7 3-10 5-1 1-8 5-9 5l-1-1c1-1 1 0 2-1h1l1-1h1c1-2 2-3 4-4 2 0 3-2 5-3s4-1 6-2h1v-1c2 0 3-1 4-2z" class="R"></path><path d="M322 259l3-1h2c1 0 3-1 4-2 0-1 1-1 1-2 1-1 1-1 2-1 0 0 1-1 2-1l1 1v-1-1c1 1 2 1 2 1-1 1-3 1-4 2 0 1-2 2-3 2h-1v1c-1 2-4 2-6 3 0 1 1 1 1 2h0c-2 1-4 2-5 3l-1 1c-1 0 0 0-1 1-2 1-4 4-6 4v-1c2-1 3-3 4-5l-1-1h0c2-1 4-3 6-5z" class="C"></path><path d="M402 216c2-2 4-3 6-3-6 4-9 7-12 12-1 2-1 2-1 3 2 0 3-1 4-1v1l1 1-6 3c0 1 0 1-1 2v-2c-4 0-8 3-11 4l-1-1c-1 1-2 1-3 1 3-2 6-3 10-5 2-1 4-2 6-5 2-4 4-7 8-10z" class="X"></path><path d="M365 225c0 1 0 2-1 3-4 6-11 10-18 12l-1 1-1 1h-1-4c1 1 1 1 1 3l-1-2h-3-2v-2l1-1v-1-2-1l2-1 1-1v1 2c1 1 1 1 3 2 2 0 7-1 9-3 1-1 2-2 4-3 0-1 1-1 1-1 1-1 2-1 3-2 2-1 4-2 6-4l1-1z" class="R"></path><path d="M335 237v-1l2-1v4c2 2 3 2 5 2h3l-1 1h-1-4c1 1 1 1 1 3l-1-2h-3-2v-2l1-1v-1-2z" class="M"></path><path d="M319 267c2 0 4-2 7-3v2c-2 2-4 3-6 6-1 1-2 2-4 3-3 1-4 4-7 6 0-2 0-3 1-5h-2v-1-1l-1-1c-1 0-1 0-3-1 1-1 2-1 3-1 1-1 3-1 4-2l1-1 4-4 1 1c-1 2-2 4-4 5v1c2 0 4-3 6-4z" class="S"></path><defs><linearGradient id="AB" x1="342.51" y1="226.542" x2="358.732" y2="233.627" xlink:href="#B"><stop offset="0" stop-color="#090908"></stop><stop offset="1" stop-color="#2b2929"></stop></linearGradient></defs><path fill="url(#AB)" d="M346 230c6 0 11-4 16-6 1 0 1 1 2 2-2 2-4 3-6 4-1 1-2 1-3 2 0 0-1 0-1 1-2 1-3 2-4 3-2 2-7 3-9 3-2-1-2-1-3-2v-2l3-6c2 0 3 0 5 1z"></path><path d="M378 236c1 0 2 0 3-1l1 1c-14 7-28 14-41 24-10 7-19 15-28 23-1-1-1-1-2-1s-2 0-3-1h1c3-2 4-5 7-6 2-1 3-2 4-3h1c2-2 5-3 7-5 3-2 5-4 8-6 10-8 21-14 33-20 1 0 8-4 9-5z" class="a"></path><path d="M332 241l1 1c0 2-1 2-2 4v1c-1 2-2 3-3 4v1c-1 1-2 2-3 4 0 1-2 2-3 3-2 2-4 4-6 5h0l-4 4-1 1c-2 0-4 0-5-2l1-2h0l3-3h2l1-3 1-2h0c-1-1-1-2-1-3h3c6-3 11-8 16-13z" class="B"></path><path d="M313 254h3l1 1c0 1-1 1-1 2v1c0 1-2 2-2 2-1 1-2 1-2 2l1-3 1-2h0c-1-1-1-2-1-3z" class="J"></path><path d="M315 264c1-1 1-2 2-3v1c1 0 1-1 2-1l1-1c0-2 1-3 3-4 1-1 2-1 2-3 1-1 2-2 3-2v1c-1 1-2 2-3 4 0 1-2 2-3 3-2 2-4 4-6 5h-1z" class="L"></path><path d="M307 265h0l3-3 1 3 1 1 3-2h1 0l-4 4-1 1c-2 0-4 0-5-2l1-2z" class="F"></path><path d="M303 255h3c3 0 4-1 7-1 0 1 0 2 1 3h0l-1 2-1 3h-2l-3 3h0l-1 2c1 2 3 2 5 2-1 1-3 1-4 2-1 0-2 0-3 1h0c-1-1-2-1-3-2l-2 2-2-1-1-1c-1-1-2-2-2-3 0-2 2-3 4-5l-1-1v-1l3-2v-2l3-1z" class="H"></path><path d="M309 259h4l-1 3h-2l-3 3h0-3l5-5h0v-1z" class="O"></path><path d="M304 265h3l-1 2c1 2 3 2 5 2-1 1-3 1-4 2-1 0-2 0-3 1h0c-1-1-2-1-3-2l-2 2-2-1c3-1 5-4 7-6z" class="N"></path><path d="M303 255h3c3 0 4-1 7-1 0 1 0 2 1 3h0l-1 2h-4c-4-1-7 1-10 3h-1l-1-1v-1l3-2v-2l3-1z" class="I"></path><path d="M303 255h3c3 0 4-1 7-1 0 1 0 2 1 3-6-1-9-1-14 1v-2l3-1z" class="F"></path><path d="M288 270v-1-1c1-1 1-1 2-1l1-1s0-1 1-1v-1l1-1c1-2 2-2 4-3v1l1 1c-2 2-4 3-4 5 0 1 1 2 2 3l1 1 2 1 2-2c1 1 2 1 3 2h0c2 1 2 1 3 1l1 1v1 1h2c-1 2-1 3-1 5h-1c-3 0-4-1-7 0l-4 4-1 1-2-1v2c-2 1-3 1-4 0l-1-1-2-2c0-1 0-1 1-2 0-2-1-1-2-2 0 0-1-2-2-2l-6-15 3 1c-1 2-1 2 0 4h1c1 0 1-1 2-1 0 1 1 1 1 2l3 1z" class="S"></path><path d="M292 270l2 2h1l2 1v2c1 0 1 1 2 1-1 0-1 0-1 1l-2-2h0c-3-2-3-3-4-5z" class="I"></path><path d="M281 268h1c1 0 1-1 2-1 0 1 1 1 1 2l3 1v5h-1c-1 0-1-1-2-2-1 0-1 0-2-1l1-3c-1 0-2 0-2-1h-1z" class="K"></path><path d="M288 270v-1-1c1-1 1-1 2-1l1-1s0-1 1-1v-1l1-1c1-2 2-2 4-3v1c-2 2-4 3-5 5s-1 4-2 5l1 2c-1 1-2 1-3 2v-5z" class="M"></path><defs><linearGradient id="AC" x1="307.566" y1="269.749" x2="293.919" y2="273.971" xlink:href="#B"><stop offset="0" stop-color="#5b5a56"></stop><stop offset="1" stop-color="#777673"></stop></linearGradient></defs><path fill="url(#AC)" d="M292 266c1-2 3-3 5-5l1 1c-2 2-4 3-4 5 0 1 1 2 2 3l1 1 2 1 2-2c1 1 2 1 3 2h0c2 1 2 1 3 1l1 1v1 1h2c-1 2-1 3-1 5h-1c-3 0-4-1-7 0l-4 4-1 1-2-1v2c-2 1-3 1-4 0l-1-1-2-2h4v-2h5c2 0 1 0 2-1v-1c1-1 1-2 2-3l-1-1c-1 0-1-1-2-1v-2l-2-1h-1l-2-2v-4z"></path><defs><linearGradient id="AD" x1="392.039" y1="213.511" x2="395.112" y2="219.272" xlink:href="#B"><stop offset="0" stop-color="#595855"></stop><stop offset="1" stop-color="#71706d"></stop></linearGradient></defs><path fill="url(#AD)" d="M433 202l5-2c1-1 2-1 2-2h1l12-10c1 0 2-1 3-1l2 1-3 2c-5 3-9 7-13 10-8 4-17 7-25 10-2 1-8 3-9 3-2 0-4 1-6 3-5 0-6 3-9 6l1-2c-1 0-2 0-3 1-2 1-5 2-7 3-3 1-6 5-8 7-1 1-3 4-5 4h0l2-3h0-2c-3 0-4 1-6 2l-3 1 8-6h-1l-1 1 1-2 1-3v-1h1c0-1 1-3 3-3 0 0 1 0 2-1h1c10-8 22-11 32-20v-1c-1-2-1-3 0-5l1-3h1c0 2-1 5 0 7s2 2 4 3h0c2 1 3 1 5 1v1 1c6-1 11-5 17-6-1 1-3 3-4 3l-1 1h1z"></path><path d="M420 204c6-1 11-5 17-6-1 1-3 3-4 3l-1 1h1v1h-1v1c-1 0-2 0-3 1h0c-1 0-3 0-3 1l-2 1h-1 0-1c1-1 2-1 3-2 1 0 3-1 4-2h0c-3 1-6 2-9 2h0v-1z" class="Q"></path><path d="M410 191h1c0 2-1 5 0 7s2 2 4 3h0c2 1 3 1 5 1v1h-1c-3 0-5 0-7-1h-1l1 1c-1 1-1 1-2 1-2 1-4 3-6 4-1 1-3 1-5 2l-13 6c-3 2-5 3-9 4 10-8 22-11 32-20v-1c-1-2-1-3 0-5l1-3z" class="U"></path><path d="M756 211c2 1 4 3 5 4 2 4 5 8 6 12 2 1 3 1 4 4 1-1 1-2 2-2-2-5-3-9-3-14l14 2 10 4c2 1 5 1 7 1 5-1 8-2 13-2h10c6 0 11-1 16 1 10 0 19 1 27 4l1-1c2 1 6 3 7 2 5 3 9 9 10 14v2c1 2 1 4 1 7h0c-4-2-6-8-8-12-10-13-33-12-48-10-9 2-17 5-23 13-7 10-8 24-6 35l2 8c0 2 1 3 2 4v1c0 1 0 2-1 3l-1-1c-4-6-6-12-7-18h0s-1 1 0 2h0v3l2 4c0 2 2 4 2 6h0c-1-2-3-4-3-6 0-1 0 0-1-1v-2c-1-1 0-2-1-2v-1c0 1-1 2 0 4 0 1 0 0 1 2v1l-1 1h-1v-2-1c0-1-1-1-1-2l-1-4v-5l-1 1-3 9c-1 2-1 3-2 4v1 1l-1-1h-1c-2 0-2-1-4-1h-2-1l-1-1c-1 0-2 0-3-1h-2l1-1v-3h0c3-2 8-6 9-9h-2c-2-1-1-2-3-1v1c-1 1-2 1-3 2s-1 1-1 3l1 1h-2v-1h-1c-2 0-3 0-4-1-2 2-3 3-5 3l-9 10c-1 1-3 3-5 4v-3h0l1-2c4-5 7-10 9-15l3-5c1-4 2-6 3-10 2-5 3-10 4-16v-7c-2-8-6-14-11-20z" class="G"></path><path d="M784 252c2 1 1 1 2 0l1-1v2 1c0 3-1 4-3 6l1 2c-1 2-1 2-2 3 0-4 1-8 1-13z" class="E"></path><path d="M768 267l2-3v6s-1 1-2 1-1 0-2 1c-2 2-3 3-5 3 2-3 5-5 7-8z" class="B"></path><path d="M788 265c0-1-1-1-2-3 2-2 2-5 2-7l1-4c0-2 0-3 1-4h1v-2c0 1 1 2 0 3-2 4-2 7-2 11v1c1 1 1 1 1 3h1l-2 1-1 1z" class="E"></path><path d="M868 224c2 1 6 3 7 2 5 3 9 9 10 14v2c-1-1-2-4-3-5-4-6-8-9-15-12l1-1z" class="I"></path><path d="M781 268v-1-6l-1-1c-1-1 0-3 0-4h1v-1l3-3c0 5-1 9-1 13l-1 3c0 3-5 8-8 10h-1l-1 2v-3h0c3-2 8-6 9-9zm10-5h0v-1-1-2l1-1c0-1 0-1 1-2l-1-1v-1-1c0-1 1-1 2-2v-1h-1v-1h3v1l-2 9c0 3-1 12 0 14v2h1c0 1-1 2 0 4 0 1 0 0 1 2v1l-1 1h-1v-2-1c0-1-1-1-1-2l-1-4v-5l-1 1-2-2v-4l2-1z" class="L"></path><path d="M788 265l1-1v4l2 2-3 9c-1 2-1 3-2 4v1 1l-1-1h-1c-2 0-2-1-4-1h-2-1l-1-1c-1 0-2 0-3-1h1l1-1h3c2-1 4 0 6-2v-1l-1-1h-1v-1c1-1 2-1 3-2l-1-1c1-2 2-3 3-4v-1l1-2z" class="E"></path><path d="M788 265l1-1v4l-1 6c-1-3-1-5-1-7l1-2z" class="D"></path><path d="M789 268l2 2-3 9c-1 2-1 3-2 4v1 1l-1-1h-1c-2 0-2-1-4-1 2-1 4-1 5-3 2-1 3-4 3-6l1-6z" class="O"></path><defs><linearGradient id="AE" x1="794.058" y1="243.625" x2="789.083" y2="238.332" xlink:href="#B"><stop offset="0" stop-color="#adabab"></stop><stop offset="1" stop-color="#dad8d8"></stop></linearGradient></defs><path fill="url(#AE)" d="M814 220h10c6 0 11-1 16 1-5 0-10 0-15 1-4 0-7 2-10 3-4 2-8 3-12 5-9 6-17 15-24 23-3 3-5 7-9 10v1l-2 3-1-2 1-4c1-1 1-2 2-3s2-3 4-4c0-1 1-2 2-2 0-3 1-5 2-7 2-3 4-5 6-7 2-1 3-3 4-5 2-2 4-4 7-6 2 0 4-2 7-3 1-1 3 0 5-1s4-2 7-3z"></path><path d="M768 261c1-1 1-2 2-3s2-3 4-4c-1 2-2 3-3 5-1 1-1 2-1 4h0v1l-2 3-1-2 1-4zm46-41h10c6 0 11-1 16 1-5 0-10 0-15 1-4 0-7 2-10 3 0-1 0-1 1-2-1-1-2 0-4 0h0c-2 1-5 2-7 3-2 0-5 3-7 5-5 4-11 8-15 13-3 2-5 5-7 8 0-3 1-5 2-7 2-3 4-5 6-7 2-1 3-3 4-5 2-2 4-4 7-6 2 0 4-2 7-3 1-1 3 0 5-1s4-2 7-3z" class="a"></path><path d="M756 211c2 1 4 3 5 4 2 4 5 8 6 12 2 1 3 1 4 4 0 3 0 7 1 11 0 2 0 3-1 5v1h1l3-2 10-12 3-1c-1 2-2 4-4 5-2 2-4 4-6 7-1 2-2 4-2 7-1 0-2 1-2 2-2 1-3 3-4 4s-1 2-2 3l-1 4 1 2c-2 3-5 5-7 8l-9 10c-1 1-3 3-5 4v-3h0l1-2c4-5 7-10 9-15l3-5c1-4 2-6 3-10 2-5 3-10 4-16v-7c-2-8-6-14-11-20z" class="Y"></path><path d="M761 265h1c1 1 3-1 4-2-2 3-3 5-5 7s-3 4-5 6l5-11h0z" class="U"></path><path d="M767 227c2 1 3 1 4 4 0 3 0 7 1 11 0 2 0 3-1 5h-1v-4c-1 0 0 0-1 1-1 2-1 4-1 7l-2-1c2-8 3-15 1-23z" class="N"></path><path d="M785 234l3-1c-1 2-2 4-4 5-2 2-4 4-6 7-1 2-2 4-2 7-1 0-2 1-2 2-2 1-3 3-4 4s-1 2-2 3l-2 2c-1 1-3 3-4 2h-1l2-3 3-12 2 1c0-3 0-5 1-7 1-1 0-1 1-1v4h1v1h1l3-2 10-12z" class="Z"></path><path d="M771 247v1h1l3-2c0 2-2 4-3 6-2 2-3 4-4 6l-2-1 1-2 1-1v-2c1-2 2-3 2-5h0 1z" class="M"></path><path d="M766 250l2 1c0-3 0-5 1-7 1-1 0-1 1-1v4h0c0 2-1 3-2 5v2l-1 1-1 2 2 1c-1 1-1 3-3 3-1 1-1 1-2 1l3-12zm7-21c-2-5-3-9-3-14l14 2 10 4c2 1 5 1 7 1 5-1 8-2 13-2-3 1-5 2-7 3s-4 0-5 1c-3 1-5 3-7 3-3 2-5 4-7 6l-3 1-10 12-3 2h-1v-1c1-2 1-3 1-5-1-4-1-8-1-11 1-1 1-2 2-2z" class="P"></path><path d="M782 228l-1 2c-1 1-1 1-3 2l-2-2c-3-3-4-7-4-12l1-1c3 0 6 1 9 2h0c0 2 0 3-1 5 0 2 0 3 1 4z" class="H"></path><path d="M782 219c4 1 6 2 9 5-1 2-2 4-3 5-1 2-2 3-3 5l-10 12-3 2h-1v-1c1-2 1-3 1-5-1-4-1-8-1-11 1-1 1-2 2-2l3 3c1 1 3 2 4 1 2-1 4-3 5-6 0-1-1-2-2-4h-1c1 2 1 3 0 5-1-1-1-2-1-4 1-2 1-3 1-5h0z" class="G"></path><defs><linearGradient id="AF" x1="532.216" y1="851.192" x2="467.161" y2="879.629" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#252523"></stop></linearGradient></defs><path fill="url(#AF)" d="M511 926c-3-8-6-15-10-22-5-11-12-22-20-31-5-6-11-11-16-16-8-8-14-17-20-27-3-6-6-11-8-17 3 4 6 9 9 13 9 13 25 27 40 33h1l1 1h1 6 9 1l1-1c3 0 5-1 8-1l2-2c1 0 2 0 3-1l1 2 21 5v1c1 1 2 0 3 0l2 3c9-3 17-9 24-15l1 1c-1 2-4 3-5 5h-1c0 2-5 5-7 6s-5 5-8 5v1c-1 0-3 1-4 1l-14 5-5 2c-1 1-3 1-4 3v1c1 1 1 1 2 1s2 1 3 1h1c1-1 2-1 3-1l1-1c2 1 4 2 5 2 0 0 1 2 1 3l9-13 1 1c-3 3-5 6-7 9-4 7-9 15-11 23h-1v3c0 1-1 3-1 5l1-1v-1c0-1 1-1 1-2l1-1v1c-2 6-5 12-7 18-3 10-5 19-7 29l-7-31z"></path><path d="M504 890h1c0 1 0 1 1 2 0 1 0 2-1 3h0c-1-1-1-1-2-3 0-1 0-1 1-2z" class="D"></path><path d="M546 866c9-3 17-9 24-15l1 1c-1 2-4 3-5 5h-1c-6 4-11 8-19 10h-1c-6 2-14 2-20 2h-9c-5 0-10 1-15 0-13-1-25-8-34-16h1c1 0 3 2 4 3 4 2 7 4 11 6 4 3 8 4 13 5h1c6 1 14 1 21 0 6 0 12 1 18 0l10-1z" class="I"></path><path d="M519 855l1 2 21 5v1c1 1 2 0 3 0l2 3-10 1c-6 1-12 0-18 0-7 1-15 1-21 0h-1c-5-1-9-2-13-5h2v-1l1-1h1v-1l1 1h1 6 9 1l1-1c3 0 5-1 8-1l2-2c1 0 2 0 3-1z" class="H"></path><path d="M512 864c2 0 2-1 3-2 1 0 2-1 3-1 0 1 0 1 1 2 0-1 1-1 2-1 2 0 5 1 7 1 3 1 7 1 10 1-1 1-7 0-9 1l-12 1-1 1c-1 0-3-1-4 0-2 0-4-1-7-1 3-1 5-1 7-2z" class="E"></path><path d="M504 860h1l1-1c3 0 5-1 8-1l-4 4c-1 0-1 0-2-1v1l-3 1v1h2l1-1c1 0 1 2 2 1l1-1h0l1 1c-2 1-4 1-7 2 0-1-2-1-3-1h-8l-9-3v-1l1-1h1v-1l1 1h1 6 9z" class="N"></path><path d="M487 859l1 1h1 6 9c-2 1-3 1-5 2-3 0-4 0-5 2v1l-9-3v-1l1-1h1v-1z" class="K"></path><path d="M519 855l1 2 21 5v1l-3 1c-3 0-7 0-10-1-2 0-5-1-7-1-1 0-2 0-2 1-1-1-1-1-1-2-1 0-2 1-3 1-1 1-1 2-3 2l-1-1h0l-1 1c-1 1-1-1-2-1l-1 1h-2v-1l3-1v-1c1 1 1 1 2 1l4-4 2-2c1 0 2 0 3-1z" class="Q"></path><path d="M516 869h9c6 0 14 0 20-2h1c8-2 13-6 19-10 0 2-5 5-7 6s-5 5-8 5v1c-1 0-3 1-4 1l-14 5-5 2c-1 1-3 1-4 3v1c1 1 1 1 2 1s2 1 3 1h1c1-1 2-1 3-1l1-1c2 1 4 2 5 2 0 0 1 2 1 3l9-13 1 1c-3 3-5 6-7 9-4 7-9 15-11 23h-1v3c0 1-1 3-1 5l1-1v-1c0-1 1-1 1-2l1-1v1c-2 6-5 12-7 18-3 10-5 19-7 29l-7-31h1 1c0-1 0-2-1-3h0v-3-1c-1-1-1-2-2-3v-1-1l-1-1c-1-2 0 0 0-1-2-6-6-12-9-17l-1-3v-1c2 2 4 5 5 7l1 3 1 1c0 1 0 1 1 2 0 1 1 1 2 2l4 10c0 2 1 3 1 5v-4c-1-1 0-3 0-5h-1v-3c-1-1-1-2-1-2v-1l-1-1c0-1-1-3-1-4s-1-1-1-3l-1-1c1 0 0 0 1-1h0l-1-1c1-1 1-1 1-2 0 0-1 0-1-1 0-2 1-4 1-6l-2-1c1-2 1 0 2-1 1 0 1-1 2-1l-2-4h0c3 2 5 2 8 2l1-1-1-2c0-1 0-1 1-2 1 1 1 1 1 2h2l2-1c1-1 1-1 2-1h1l3-2h1l3-1 1-1s1-1 2-1 0 0 1-1l3-1h0c-3 1-7 1-10 1-4 0-9 0-13-1h-1z" class="G"></path><path d="M518 880c1 3 1 7 1 11l-1 16c0-2 0-4-1-6v-18-2l1-1z" class="X"></path><path d="M534 884h3 0c-2 1-3 2-4 5 1 0 1 0 1 1s-2 2-3 2l-2 1c-1-3-1-5-1-8l1-1c1 1 1 2 2 2l1-2h0 2z" class="D"></path><path d="M517 901c1 2 1 4 1 6v28 7c-2-6-1-14-1-20v-21z" class="Y"></path><defs><linearGradient id="AG" x1="522.735" y1="894.283" x2="508.503" y2="902.777" xlink:href="#B"><stop offset="0" stop-color="#4c4d4c"></stop><stop offset="1" stop-color="#696661"></stop></linearGradient></defs><path fill="url(#AG)" d="M511 883l-2-4h0c3 2 5 2 8 2v2 18 21c-1-2-2-4-2-5l-1-11c-1-1-1-3-1-4-1-3-2-5-2-7-1-4-1-9 0-12z"></path><path d="M513 902v-7l1-1 1 1v10l-1 1c-1-1-1-3-1-4z" class="I"></path><path d="M511 883l-2-4h0c3 2 5 2 8 2v2h-3c-1 1-3 3-3 4v8c-1-4-1-9 0-12z" class="K"></path><path d="M539 886l9-13 1 1c-3 3-5 6-7 9-4 7-9 15-11 23h-1v3c0 1-1 3-1 5l1-1v-1c0-1 1-1 1-2l1-1v1c-2 6-5 12-7 18-3 10-5 19-7 29l-7-31h1 1c0-1 0-2-1-3h0v-3-1c-1-1-1-2-2-3v-1-1l-1-1c-1-2 0 0 0-1-2-6-6-12-9-17l-1-3v-1c2 2 4 5 5 7l1 3 1 1c0 1 0 1 1 2 0 1 1 1 2 2l4 10c0 2 1 3 1 5v-4c-1-1 0-3 0-5h-1v-3c-1-1-1-2-1-2v-1l-1-1c0-1-1-3-1-4s-1-1-1-3l-1-1c1 0 0 0 1-1h0l-1-1c1-1 1-1 1-2 0 0-1 0-1-1 0-2 1-4 1-6l-2-1c1-2 1 0 2-1 1 0 1-1 2-1-1 3-1 8 0 12 0 2 1 4 2 7 0 1 0 3 1 4l1 11c0 1 1 3 2 5 0 6-1 14 1 20v-7l1 3v2c-1 3 0 5-2 8v2c1 1 1 2 1 3 0-2 1-3 1-4 1-1 0-3 1-4v-2h0l1-3v-1c0-1 0-3 1-3v-1c0-4 1-7 1-11 3-13 9-27 16-38z" class="J"></path><path d="M795 569h1 0 1 1v-1l2-2-2 5c0 1 1 1 1 1h0c-2 8-7 16-11 23-1 3-5 8-5 11-1 2 0 6 0 8 2 9 4 19 3 28l1 1-1 1h0l-1 2c-15 17-32 30-50 43-8 5-14 11-22 15-14 8-29 15-44 20l-12 5c-3 1-7 2-10 3 2-2 5-4 8-6l2-1c4-1 8-4 12-6 9-5 17-11 25-17 5-3 9-5 13-8l-1-2c11-9 23-16 31-28h-1c-1 1-4 3-5 4-1 0-2-1-4-1-1-1-2-1-3-1s0 0-1 1h-1c12-14 21-30 24-48 1 3 0 5 0 8h0l1 1h1c1-1 2-2 2-4 1-2 2-4 4-6v-1h1l2-4c1-2 3-2 6-3v-1c1-2 2-3 5-3l-1-6 1-2v-2l-2-5-1-2c-4-6-10-11-16-16h1 0l1-1 1 1c2 0-1-1 2 0l1 1 1-1h0-1c1-1 1-1 1-2l-1-1 1-1c1 1 1 1 2 1h1 2l2 2c2 0 2-2 3-3 0 1 1 2 2 2 2 1 3 2 6 2h3l5-2c-1 2-3 4-3 5 0 2 1 2 2 3 3 0 3 0 5-2 1 0 1 0 1-1s0-1 1-2l3-2 3-5v2h1z" class="H"></path><path d="M737 664c1 0 1-1 2-1 4-3 7-8 10-11h2c-5 6-11 13-17 19-8 9-18 16-27 23l-1-2c11-9 23-16 31-28z" class="Z"></path><defs><linearGradient id="AH" x1="784.178" y1="586.663" x2="792.322" y2="596.837" xlink:href="#B"><stop offset="0" stop-color="#7c797a"></stop><stop offset="1" stop-color="#8b8d88"></stop></linearGradient></defs><path fill="url(#AH)" d="M784 596c2-3 3-5 4-7 2-4 4-8 6-11 1-2 2-5 4-7 0 1 1 1 1 1h0c-2 8-7 16-11 23-1 3-5 8-5 11-1 2 0 6 0 8 2 9 4 19 3 28l1 1-1 1v-1l-4-34-2 3c-6 15-18 28-29 40h-2l15-17 1-5h0c0-1-1-1-1-1l-3-1c0-1 1-3 2-4h1c0 1 0 1 1 2l2-2c2-3 3-7 3-10l2-2 1-2h0l1 1c1-2 2-3 3-4 1-2 3-4 5-6v-1c1-2 2-3 2-4z"></path><path d="M773 610h0l1 1c1-2 2-3 3-4 1-2 3-4 5-6-1 3-2 7-4 11-2 5-5 10-9 15-1 3-3 5-5 8l1-5h0c0-1-1-1-1-1l-3-1c0-1 1-3 2-4h1c0 1 0 1 1 2l2-2c2-3 3-7 3-10l2-2 1-2z" class="M"></path><path d="M763 624h1c0 1 0 1 1 2l2-2c-1 1-1 2-2 4l-1 1-3-1c0-1 1-3 2-4z" class="N"></path><path d="M795 569h1 0 1 1v-1l2-2-2 5c-2 2-3 5-4 7-2 3-4 7-6 11-1 2-2 4-4 7 0 1-1 2-2 4v1c-2 2-4 4-5 6-1 1-2 2-3 4l-1-1h0l-1 2-2 2v-4c1-1 1-3 1-4-1-1-1-2-1-3l-1-2-1-3v-2l-2-5-1-2c-4-6-10-11-16-16h1 0l1-1 1 1c2 0-1-1 2 0l1 1 1-1h0-1c1-1 1-1 1-2l-1-1 1-1c1 1 1 1 2 1h1 2l2 2c2 0 2-2 3-3 0 1 1 2 2 2 2 1 3 2 6 2h3l5-2c-1 2-3 4-3 5 0 2 1 2 2 3 3 0 3 0 5-2 1 0 1 0 1-1s0-1 1-2l3-2 3-5v2h1z" class="K"></path><path d="M774 573h3l1 3h-1-1c-2 1-2 3-2 5 0-2-1-3-2-4h0c0-2 0-3 2-4z" class="M"></path><path d="M755 574l1-1 5 5c4 4 8 8 10 13 0 0 1 1 1 2s0 1-1 1c0 1-1 1-2 1v-1l1-1-1-1c-1-1-1-1-3-1l-1-2h2v1l1-1c-1-2-3-4-4-6-3-3-5-6-9-9z" class="S"></path><path d="M766 591c2 0 2 0 3 1l1 1-1 1v1c1 0 2 0 2-1 1 0 1 0 1-1v1c0 1 0 1 1 2v1c-1 3-2 9-1 13v2l-2 2v-4c1-1 1-3 1-4-1-1-1-2-1-3l-1-2-1-3v-2l-2-5z" class="C"></path><path d="M768 596c2 0 3 0 3 2 0 1-1 1-2 1v2l-1-3v-2z" class="N"></path><path d="M751 572l1 1c2 0-1-1 2 0l1 1c4 3 6 6 9 9 1 2 3 4 4 6l-1 1v-1h-2c-4-6-10-11-16-16h1 0l1-1z" class="M"></path><path d="M766 569c0 1 1 2 2 2 2 1 3 2 6 2-2 1-2 2-2 4h0c1 1 2 2 2 4l2 1h1l1 1-1 1c-2 1-3-1-5-1l-2-2c0-1 0-1-1-2h0v-1c-2 0-3-2-4-3s-3-1-4-3c0-1-1-1-2-2h2l2 2c2 0 2-2 3-3z" class="C"></path><path d="M780 592c1 1 3 2 3 4 0 1 0 0-1 2v2 1c-2 2-4 4-5 6-1 1-2 2-3 4l-1-1h0c1-1 2-2 1-3v-3c0-2-1-4 1-6l1-1v-2c2-1 3-1 4-3h0z" class="F"></path><path d="M795 569h1 0 1 1v-1l2-2-2 5c-2 2-3 5-4 7-2 3-4 7-6 11-1 2-2 4-4 7 0 1-1 2-2 4v-2c1-2 1-1 1-2 0-2-2-3-3-4h0l-1-1 1-1c1 1 1 1 2 1v-1l1-2v-1h-2v3c-1-1-1-1-1-2h-2 0l-2-2 1-1c1 0 2 1 3 1l2-2v-2l3-1 1-1v-1l1-1c1 0 1 0 2-1v-1l2-4 3-5v2h1z" class="C"></path><path d="M780 586l2-2v-2l3-1 1 2c-1 2-1 3-3 3h-3zm3 2l1 1h0c1 1 1 2 1 4h0l-1 3c0 1-1 2-2 4v-2c1-2 1-1 1-2 0-2-2-3-3-4h0l-1-1 1-1c1 1 1 1 2 1v-1l1-2z" class="M"></path><defs><linearGradient id="AI" x1="766.306" y1="631.262" x2="720.127" y2="657.925" xlink:href="#B"><stop offset="0" stop-color="#41403f"></stop><stop offset="1" stop-color="#74736f"></stop></linearGradient></defs><path fill="url(#AI)" d="M768 598l1 3 1 2c0 1 0 2 1 3 0 1 0 3-1 4v4c0 3-1 7-3 10l-2 2c-1-1-1-1-1-2h-1c-1 1-2 3-2 4l3 1s1 0 1 1h0l-1 5-15 17c-3 3-6 8-10 11-1 0-1 1-2 1h-1c-1 1-4 3-5 4-1 0-2-1-4-1-1-1-2-1-3-1s0 0-1 1h-1c12-14 21-30 24-48 1 3 0 5 0 8h0l1 1h1c1-1 2-2 2-4 1-2 2-4 4-6v-1h1l2-4c1-2 3-2 6-3v-1c1-2 2-3 5-3l-1-6 1-2z"></path><path d="M738 648l2-1 1 1v2l1 2-3 2c-1 1-1 1-2 1l-1-1c0-2 2-2 3-4l-1-1v-1z" class="C"></path><path d="M753 636l5-7c1-2 2-3 4-5 0-1 0-2 1-2v1 1c-1 1-2 3-2 4-3 6-9 11-12 17 0 1-1 2-3 3h0l-1 2c-1-1-1-2-1-3s0-1 1-2l-1-1c1-2 2-4 4-5s3-2 5-3z" class="Q"></path><path d="M748 639c2-1 3-2 5-3-2 1-3 3-4 4 0 2-1 2-2 3s-1 0-1 1v1c0 1-1 1-1 2v1h1l-1 2c-1-1-1-2-1-3s0-1 1-2l-1-1c1-2 2-4 4-5z" class="I"></path><defs><linearGradient id="AJ" x1="752.625" y1="620.788" x2="760.272" y2="625.154" xlink:href="#B"><stop offset="0" stop-color="#53524e"></stop><stop offset="1" stop-color="#6c6b69"></stop></linearGradient></defs><path fill="url(#AJ)" d="M768 598l1 3 1 2c0 1 0 2 1 3 0 1 0 3-1 4v4c0 3-1 7-3 10l-2 2c-1-1-1-1-1-2h-1v-1-1c-1 0-1 1-1 2-2 2-3 3-4 5l-5 7c-2 1-3 2-5 3 1-1 2-2 2-3h-1c-1 1-1 2-3 3h-1l2-2c1-1 0-1 1-2s1-1 2-1h1l-2-1c0-4 4-9 5-13v-3h1l2-4c1-2 3-2 6-3v-1c1-2 2-3 5-3l-1-6 1-2z"></path><path d="M755 617l1-1v2l-2 2v-3h1z" class="C"></path><path d="M763 610v-1c1-2 2-3 5-3-1 4-1 9-3 13l-1-1c0-1 0-2-1-3v-2l-2 2c0-2 1-3 2-5z" class="S"></path><path d="M768 598l1 3 1 2c0 1 0 2 1 3 0 1 0 3-1 4v4c0 3-1 7-3 10l-2 2c-1-1-1-1-1-2h-1v-1h0l1-1c1 1 0 1 1 1l1-1-1-1v-2c2-4 2-9 3-13l-1-6 1-2z" class="Q"></path><path d="M770 603c0 1 0 2 1 3 0 1 0 3-1 4v4c0 3-1 7-3 10l-2 2c-1-1-1-1-1-2h-1v-1h0l1-1c1 1 0 1 1 1l1-1c1-2 2-5 2-8 1 0 1 0 2-1-1-1-1-2-1-4 1-2 1-4 1-6z" class="S"></path><path d="M437 190c1 0 4-1 5-2 4-2 8-3 12-6 10-6 20-13 28-21 5-5 9-10 13-16 12-19 17-42 21-64l1-12c0-2 0-5 1-7 0 17 3 34 8 50 3 10 6 21 11 30l8 11v3c1 3 5 6 6 9 3 2 4 5 7 8-2 0-2-1-4-2-1 0-1 0-2-1 2 3 3 5 6 8 1 1 3 3 4 5l-1 1 6 3c8 5 17 9 25 13-3 1-10-1-14-1l-3-1v1c1 0 2 1 3 1l3 3h0l-1-1c-2 0-3-1-5-1-1-1-1 0-2-1h-1-2l1-1c0-1 0-1-1-1h0-1-2-2c-5-2-11-1-17-1h-6c-2 0-5-1-6 0l2 1c1 0 2 1 3 1h0c-8-1-15-3-23-2l-14 2v2c-7 1-15 2-22 4l-20 5c-1 0-4 2-5 1l-57 18-1-1v-1c-1 0-2 1-4 1 0-1 0-1 1-3 3-5 6-8 12-12 1 0 7-2 9-3 8-3 17-6 25-10 4-3 8-7 13-10l3-2-2-1c-1 0-2 1-3 1l-12 10h-1c0 1-1 1-2 2l-5 2h-1l1-1c1 0 3-2 4-3-6 1-11 5-17 6v-1-1c-2 0-3 0-5-1h0c8 0 15-4 23-8h0-2c-1 1-1 1-2 1h-1l-1 1h-1c-1 1-2 1-3 2h-1c-1 0-2 1-3 1l2-2c0-1 10-4 12-5l-1-1z" class="K"></path><path d="M507 143c2 3 1 6 0 8 0 1 0 2-1 3v1c-1 1-2 3-3 4v-2h1l-1-1-1 1c0-1 1-2 1-3s2-5 3-7c0 0 1-3 1-4z" class="D"></path><path d="M508 133c0-1 0-2 1-3 0-1 1-2 1-3 0-2 0 0 1-1v-2l1-4v-2h1v-1c1 2-3 11-3 13-1 2-1 3-1 4v3c0 1 0 1-1 2l-1 4h0c0 1-1 4-1 4h-1s0 1-1 1v-2c1-1 1-3 1-4v-1l3-8z" class="M"></path><path d="M511 142c1-1 1-1 1-2h1v6l-2 7-4 9c-1 3-1 7-4 9h0c-3 2-5 5-7 8v-1c0-1 1-2 1-3 0-3 3-5 5-8 1-1 1-3 2-4 3-4 4-9 5-14 1-3 1-5 2-7z" class="I"></path><path d="M503 154c0 1-1 2-1 3l1-1 1 1h-1v2c-2 3-6 7-8 10v1c-1 2-1 3-2 5 0 2 0 3 1 5l1 1c1-1 1-1 1-2 2-3 4-6 7-8l1 3c-1 4-2 6-5 9-2 1-3 1-4 1-1 1-2 1-3 0-3-3-3-9-3-13 1-4 4-6 7-9 3-2 5-5 7-8z" class="E"></path><path d="M516 107c0-5 0-10 1-15 1 5 1 11 1 16v26 15 23c-1-3 0-8-1-12v-6h0c0 6 1 12-1 18v-16-8-4c-1-2 0-5 0-7l-2 1h0c0 2 0 5-1 7v1-6h-1c0 1 0 1-1 2 2-8 2-17 4-25h0v-5l1-1v-4z" class="Y"></path><defs><linearGradient id="AK" x1="510.435" y1="124.809" x2="518.746" y2="130.822" xlink:href="#B"><stop offset="0" stop-color="#504e4e"></stop><stop offset="1" stop-color="#6d6d68"></stop></linearGradient></defs><path fill="url(#AK)" d="M511 142c2-8 2-17 4-25h0v-5l1-1v-4 49-8-4c-1-2 0-5 0-7l-2 1h0c0 2 0 5-1 7v1-6h-1c0 1 0 1-1 2z"></path><path d="M438 193l8-3c3-1 6-4 10-5 1 0 1 0 2-1l12-8c6-4 11-9 16-15a30.44 30.44 0 0 0 8-8c5-7 10-15 13-23v-1c0 3-1 5-2 8-3 5-5 9-8 14l-4 6h0c1-2 3-3 4-4l1-1c0-1 1-1 1-2l1-1c1-2 2-5 3-6 1-2 2-3 2-4 1-1 1-2 1-3 1-1 1-2 2-3l-3 8v1c0 1 0 3-1 4v2c1 0 1-1 1-1h1c-1 2-3 6-3 7-2 3-4 6-7 8l-1-1c-3 2-5 5-7 7l-5 6c-5 2-6 4-10 8h0c-1 0-2 1-3 2l-6 3c-2 1-3 3-4 3-2 1-3 1-4 1l-1-1 3-2-2-1c-1 0-2 1-3 1l-12 10h-1c0 1-1 1-2 2l-5 2h-1l1-1c1 0 3-2 4-3-6 1-11 5-17 6v-1-1c-2 0-3 0-5-1h0c8 0 15-4 23-8z" class="P"></path><path d="M490 162c0-1 2-3 2-4 3-2 5-4 8-7 1-3 3-6 5-9v-1 1c0 1 0 3-1 4v2c1 0 1-1 1-1h1c-1 2-3 6-3 7-2 3-4 6-7 8l-1-1c-3 2-5 5-7 7 0-2 0-4 2-5v-1h0z" class="N"></path><path d="M490 162h0v1c-2 1-2 3-2 5l-5 6c-5 2-6 4-10 8h0c-1 0-2 1-3 2l-6 3c-2 1-3 3-4 3-2 1-3 1-4 1l-1-1 3-2c3-2 6-5 9-8 4-3 7-5 11-8 4-4 7-7 12-10z" class="C"></path><path d="M518 149c1-7 1-15 1-22 0-3-1-6 0-8v4c1-3 2-5 3-8v1l1-1c1 2 2 5 2 7l5 15c6 13 14 22 22 33 2 3 3 5 6 8 1 1 3 3 4 5l-1 1 6 3c8 5 17 9 25 13-3 1-10-1-14-1l-3-1v1c1 0 2 1 3 1l3 3h0l-1-1c-2 0-3-1-5-1-1-1-1 0-2-1h-1-2l1-1c0-1 0-1-1-1h0-1-2-2c-5-2-11-1-17-1h-6c-2 0-5-1-6 0l2 1c1 0 2 1 3 1h0c-8-1-15-3-23-2 0-6-1-11 0-16v-9-23z" class="H"></path><path d="M524 135c2 2 2 5 4 8 1 2 1 4 3 6 1 1 1 2 2 4 0 1 2 2 3 4h-1v1c-1 0-1 0-1 1l-1-1-4-9c-1-3-6-12-5-14z" class="D"></path><path d="M547 181l-1-1c0-6-4-9-7-13-3-2-6-6-8-9s-3-6-4-9l-4-13-1-2c0-1 1-1 1-2 1 1 1 1 1 3-1 2 4 11 5 14s3 7 4 9l1 1c2 4 7 8 10 13 1 1 4 3 4 5-1 2-1 2-1 4z" class="L"></path><path d="M522 116l1-1c1 2 2 5 2 7l5 15c6 13 14 22 22 33 2 3 3 5 6 8 1 1 3 3 4 5l-1 1-3-3-5-6c-4-7-10-13-15-20-8-11-12-26-16-39z" class="P"></path><defs><linearGradient id="AL" x1="584.644" y1="194.284" x2="550.658" y2="186.226" xlink:href="#B"><stop offset="0" stop-color="#101010"></stop><stop offset="1" stop-color="#3f3e3a"></stop></linearGradient></defs><path fill="url(#AL)" d="M553 175l5 6 3 3 6 3c8 5 17 9 25 13-3 1-10-1-14-1l-3-1c-5-2-12-3-17-4l-12-2c-2 0-4 0-6-1 5-1 8-1 12-4l2-4c0-2-1-5-1-8z"></path><path d="M554 183h2l1 2c-1 0-3 2-5 2l2-4z" class="L"></path><path d="M553 175l5 6c1 2 1 3 1 4h-2l-1-2h-2c0-2-1-5-1-8z" class="B"></path><path d="M546 192l2-2c2 0 7 1 9 1l1 3-12-2z" class="O"></path><defs><linearGradient id="AM" x1="543.181" y1="186.424" x2="545.004" y2="201.053" xlink:href="#B"><stop offset="0" stop-color="#666362"></stop><stop offset="1" stop-color="#90908e"></stop></linearGradient></defs><path fill="url(#AM)" d="M547 181c0-2 0-2 1-4 1 2 1 4 0 6-2 5-7 5-12 7l1 1h3c2 1 4 1 6 1l12 2c5 1 12 2 17 4v1c1 0 2 1 3 1l3 3h0l-1-1c-2 0-3-1-5-1-1-1-1 0-2-1h-1-2l1-1c0-1 0-1-1-1h0-1-2-2c-5-2-11-1-17-1h-6c-2 0-5-1-6 0l2 1c1 0 2 1 3 1h0c-8-1-15-3-23-2 0-6-1-11 0-16v8c6 0 14 1 19-1h1 1c3-1 6-4 8-7z"></path><defs><linearGradient id="AN" x1="455.093" y1="174.92" x2="493.608" y2="216.94" xlink:href="#B"><stop offset="0" stop-color="#81807e"></stop><stop offset="1" stop-color="#aeadac"></stop></linearGradient></defs><path fill="url(#AN)" d="M513 146v-1c1-2 1-5 1-7h0l2-1c0 2-1 5 0 7v4 8 16c2-6 1-12 1-18h0v6c1 4 0 9 1 12v9c-1 5 0 10 0 16l-14 2v2c-7 1-15 2-22 4l-20 5c-1 0-4 2-5 1l-57 18-1-1v-1c-1 0-2 1-4 1 0-1 0-1 1-3 3-5 6-8 12-12 1 0 7-2 9-3 8-3 17-6 25-10 4-3 8-7 13-10l1 1c1 0 2 0 4-1 1 0 2-2 4-3l6-3c1-1 2-2 3-2h0c4-4 5-6 10-8l5-6c2-2 4-5 7-7l1 1c-3 3-6 5-7 9 0 4 0 10 3 13 1 1 2 1 3 0 1 0 2 0 4-1 3-3 4-5 5-9l-1-3h0c3-2 3-6 4-9l4-9 2-7z"></path><path d="M504 199v2c-7 1-15 2-22 4-1 0-1 0-2-1l24-5z" class="V"></path><path d="M511 153c0 3 1 6-1 8-2 4-2 10-6 13l-1-3h0c3-2 3-6 4-9l4-9z" class="R"></path><path d="M455 210c5-2 10-2 16-3 3-1 6-3 9-3 1 1 1 1 2 1l-20 5c-1 0-4 2-5 1-1 0-2 0-2-1z" class="a"></path><path d="M417 210l-1 1v1c-2 0-3 1-4 3-2 1-6 3-8 5-2 1-6 7-8 5 3-5 6-8 12-12 1 0 7-2 9-3z" class="C"></path><path d="M455 210c0 1 1 1 2 1l-57 18-1-1v-1l56-17z" class="V"></path><path d="M488 168c2-2 4-5 7-7l1 1c-3 3-6 5-7 9 0 4 0 10 3 13 1 1 2 1 3 0 1 0 2 0 4-1v1h4v1c-2 2-4 3-6 4-3 2-8 3-12 5-1 0-2 1-3 2h-1c-3 0-8 3-12 4 1-2 2-2 4-3 0-1-1-1-1-1l-27 11c-2 1-3 2-6 1 1 0 2-1 2-1 1-1 1-1 1-2h-2c-1 1-3 1-4 1l-24 9c1-2 2-3 4-3v-1l1-1c8-3 17-6 25-10 4-3 8-7 13-10l1 1c1 0 2 0 4-1 1 0 2-2 4-3l6-3c1-1 2-2 3-2h0c4-4 5-6 10-8l5-6z" class="R"></path><path d="M490 185c0 1 1 1 2 1h6c-1 1-3 2-4 2l-4 2c-1 0-1 1-2 1-2 1-7 4-9 4v-1l3-2c1-1 2-1 3-1s1-1 2-1c-1-1-2-1-3-1l-1-1c1-1 2-2 3-2h1c-1 1-1 1 0 2l3-3z" class="Z"></path><path d="M486 178c1 2 2 5 4 7l-3 3c-1-1-1-1 0-2h-1c-1 0-2 1-3 2-2 3-5 4-8 6 1-3 4-3 5-5 2-4 3-6 5-9l1-2z" class="C"></path><defs><linearGradient id="AO" x1="456.858" y1="194.508" x2="451.366" y2="207.303" xlink:href="#B"><stop offset="0" stop-color="#52504d"></stop><stop offset="1" stop-color="#6f6e6e"></stop></linearGradient></defs><path fill="url(#AO)" d="M475 187l2-4c1-3 5-6 8-7l1 2-1 2c-2 3-3 5-5 9-1 2-4 2-5 5l-3 2-27 11v-2c2-2 5-4 7-5 4-2 7-4 11-5l8-4c2-1 3-3 4-4z"></path><path d="M463 195l8-4c0 1 0 2 1 3-3 1-5 2-7 2-1 0-2 0-2-1z" class="J"></path><path d="M475 187l2-4c1-3 5-6 8-7l1 2-1 2c-1 1-2 1-2 2h-1c-2 2-3 4-4 6-1 3-4 4-6 6-1-1-1-2-1-3 2-1 3-3 4-4z" class="E"></path><path d="M473 182c4-4 5-6 10-8-1 2-3 3-5 5s-3 4-5 5v1l2 2c-1 1-2 3-4 4l-8 4c-4 1-7 3-11 5-2 1-5 3-7 5v2c-2 1-3 2-6 1 1 0 2-1 2-1 1-1 1-1 1-2h-2c-1 1-3 1-4 1l-24 9c1-2 2-3 4-3v-1l1-1c8-3 17-6 25-10 4-3 8-7 13-10l1 1c1 0 2 0 4-1 1 0 2-2 4-3l6-3c1-1 2-2 3-2h0z" class="P"></path><path d="M473 182c4-4 5-6 10-8-1 2-3 3-5 5s-3 4-5 5v1l2 2c-1 1-2 3-4 4l-8 4c-4 1-7 3-11 5-2 1-5 3-7 5v2c-2 1-3 2-6 1 1 0 2-1 2-1 1-1 1-1 1-2h-2c-1 1-3 1-4 1 3-2 6-2 9-4l9-5c3-1 6-2 9-4 5-3 9-6 10-11z" class="Z"></path><defs><linearGradient id="AP" x1="837.798" y1="629.94" x2="766.856" y2="408.439" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#363431"></stop></linearGradient></defs><path fill="url(#AP)" d="M807 371l1 2c1-1 1-1 2-1v1l4 6 4 6 2 5c2 2 2 4 2 7v3h1v5 2l9 30 5 14 1 1c1 3 3 5 5 7h1l1 1c1 0 1 1 1 1-3 0-5 0-7 1-2 3-2 9-1 11l2 1-1 13c1 12 1 22 7 33 3 4 5 5 9 7 0 1 0 3 1 3 1 2 2 2 3 5-5 0-9 2-12 6-5 6-8 15-9 23-4 14-7 29-9 44 0 10-3 24-8 33v-18c-1-11-1-23-5-33-7 9-11 20-16 30-4 8-8 16-13 23l-1-1c1-9-1-19-3-28 0-2-1-6 0-8 0-3 4-8 5-11 4-7 9-15 11-23h0s-1 0-1-1l2-5-2 2v1h-1-1 0-1-1v-2-1c1-2 1-3 2-4h0c-1-2 0-5 0-7 1-2 1-7 0-9l-2-1v-1c2-1 4-3 6-5 2-4 1-5 0-9h0v-1c-2-2-3-2-5-2h-1v-1c2-2 3-5 4-7 2-4 4-6 4-11v-1c2-2 2-3 3-5l2-1c0-1-1-1-1-2l1-1c1-1 1-3 2-4 1-4 2-9 2-13l-1-1 1-1-1-1c0-2 1-4 2-7h1c0-4-1-9-2-13l1-1v-5h1v4l1-1-3-29-2-6h0l-1-5 1-1h1 1l3 1c0-2 0-2-1-4v-1c0-4-1-8-2-11-1-6-2-11-4-16v-1l5 6c0-3-1-5-2-7-1-4-2-6-4-8l1-2z"></path><path d="M824 499h1c0 2 0 5-1 7v1l2 1-1 2c0 2 0 3 1 4h-2v-2c0-1 0-3-1-4v-1c1-1 0-2 1-3v-5z" class="G"></path><path d="M812 457v-5h1v4c1 6 2 13 2 19-1-2-1-3-2-4 0-4-1-9-2-13l1-1z" class="R"></path><path d="M839 487c1 12 1 22 7 33h-2c-2-2-3-4-4-7l-1-3c-1-2-2-9-1-12l1 1v-1l-1-1c1-3 0-7 1-10z" class="J"></path><path d="M834 494c1 2 0 7 1 9h0v-2c0-2 0-3 1-4h0c-1 8-1 18 2 27 1 4 4 5 7 7l2 1c-3 1-6-3-9-4-1 0-2 1-3 2l-1-1 3-3c-4-10-3-21-3-32z" class="F"></path><path d="M832 437l5 14 1 1c1 3 3 5 5 7h1l1 1c1 0 1 1 1 1-3 0-5 0-7 1h-2c-1-1-2-2-2-3s0-2 1-2c0 1 0 0 1 1l1-1-2-3c-1-2-1-4-2-6 0-1-1-2-1-2v-2l-1-2c0-1-1-2 0-3v-2z" class="L"></path><path d="M818 410l4 11s0 1 1 2c1 4 2 8 3 13 1 3 2 7 4 10l-2 1v1h2v4l-1 1-1-1v-2h2l-1-1h0v1l-2-1v1c0 1 0 3-1 4v-2-1-1l-1-1 2-2 2-1-1-1c0-3-1-4-1-6v2h-1v-1l-2-2h0c0-2-2-2-1-4l-1-3c0-7-3-14-4-21z" class="F"></path><path d="M821 623h1v12l1-1h0v-1-1c1-1 1 0 1-1v-2l1-3c1-1 1-1 1-2v-1-1c0-1 1-2 1-4l1-3v-1c0-1 0-2-1-3v5 1c-2-1-1-4-1-6h-1c0 5 1 10-1 14l-1 1v-2c1-1 1-1 1-2 1-2 0-3 0-5 1-14 2-28 0-42-1-2-1-6-2-8 0-1-1-1-3-2h0v-1h2c1 1 2 2 3 4 1 3 2 7 2 11l1 21c0 2-1 5 0 8l1 2h0l1-2h0c0 10-3 24-8 33v-18z" class="O"></path><defs><linearGradient id="AQ" x1="808.845" y1="434.906" x2="817.155" y2="421.594" xlink:href="#B"><stop offset="0" stop-color="#4a4948"></stop><stop offset="1" stop-color="#73726e"></stop></linearGradient></defs><path fill="url(#AQ)" d="M808 415l1-1h1 1l3 1 3 10c-1 1-2 1-3 1v1c1 1 2 3 2 4 0 2 0 4 1 6 0 1-1 2-1 3l-1-2-1 1v2h2v1c1 1 1 1 3 2 1 0 1 1 2 1v1l1 2c1-1 0-2 0-3l1-1c0 1 1 3 1 4s0 1-1 1h0 0-1c-1 0-1 0-1-1l-1 4c1 1 1 1 2 1 0-1-1-1-1-2 1 0 1-1 1 0 1 1 1 1 1 2h1v-1h1v2h-1l-1 1v2 1c-1-2-1-3-3-5l-1-2 1-2-1-2h-1l-1 1c1 1 1 1 1 2l1 2v6l-1 1c0-2 1-5 0-7-1 0 0-2-1-2-1-2-1-1 0-3 1 0 1-1 2-1l-1-1-3-3c-3 3 1 10-1 13l-3-29-2-6h0l-1-5z"></path><path d="M808 415l1-1h1 1l2 4-1 1-1-1h-1l-1 2h0l-1-5z" class="Z"></path><path d="M810 478c0-2 1-4 2-7h1c1 1 1 2 2 4 1 13 0 27-2 41-2 19-5 38-14 56 0 0-1 0-1-1l2-5h0c6-13 9-30 11-44 0-5 1-9 1-13-1-1-1-4 0-5v-2h0v-6h0 0l-1 8h-1l1-1v-9c-1 2-3 4-3 7-1 1-1 2-2 3v1l1-4c0-1-1-1-1-2l1-1c1-1 1-3 2-4 1-4 2-9 2-13l-1-1 1-1-1-1z" class="a"></path><path d="M810 478c0-2 1-4 2-7h1c0 13 1 26-1 38-1-1-1-4 0-5v-2h0v-6h0 0l-1 8h-1l1-1v-9c-1 2-3 4-3 7-1 1-1 2-2 3v1l1-4c0-1-1-1-1-2l1-1c1-1 1-3 2-4 1-4 2-9 2-13l-1-1 1-1-1-1z" class="I"></path><defs><linearGradient id="AR" x1="786.696" y1="537.06" x2="820.304" y2="527.94" xlink:href="#B"><stop offset="0" stop-color="#4a4b43"></stop><stop offset="1" stop-color="#807e7f"></stop></linearGradient></defs><path fill="url(#AR)" d="M810 504h1l1-8h0 0v6h0v2c-1 1-1 4 0 5 0 4-1 8-1 13-2 14-5 31-11 44h0l-2 2v1h-1-1 0-1-1v-2-1c1-2 1-3 2-4h0c-1-2 0-5 0-7 1-2 1-7 0-9l-2-1v-1c2-1 4-3 6-5 0 2 0 4-1 6 0 1-1 3 0 4l1-1c1-1 3-4 3-6 2-3 2-6 3-9l1-7h0c1-2 1-2 1-4v-2c0-2 1-4 2-7v-6-3z"></path><path d="M799 545c0 1-1 3 0 4l1-1c1-1 3-4 3-6 2-3 2-6 3-9 1 5-2 12-4 16-1 1-2 3-2 4v2l-2-1c0-3 0-6 1-9z" class="I"></path><path d="M794 544c2-1 4-3 6-5 0 2 0 4-1 6-1 3-1 6-1 9l2 1c-1 2-1 2-1 4v1c-1 2-1 4-3 6 0 1 0 2-1 3h-1v-2-1c1-2 1-3 2-4h0c-1-2 0-5 0-7 1-2 1-7 0-9l-2-1v-1z" class="P"></path><defs><linearGradient id="AS" x1="808.118" y1="529.915" x2="798.53" y2="525.391" xlink:href="#B"><stop offset="0" stop-color="#7b7979"></stop><stop offset="1" stop-color="#91908e"></stop></linearGradient></defs><path fill="url(#AS)" d="M806 505v-1c1-1 1-2 2-3 0-3 2-5 3-7v9l-1 1v3 6c-1 3-2 5-2 7v2c0 2 0 2-1 4h0l-1 7c-1 3-1 6-3 9 0 2-2 5-3 6l-1 1c-1-1 0-3 0-4 1-2 1-4 1-6 2-4 1-5 0-9h0v-1c-2-2-3-2-5-2h-1v-1c2-2 3-5 4-7 2-4 4-6 4-11v-1c2-2 2-3 3-5l2-1-1 4z"></path><path d="M809 507h1v6c-1 3-2 5-2 7v2c-1-1-1-3-1-4 1-1 1-2 1-2-1-1-1-2-1-2 0-1 2-3 2-4v-3z" class="U"></path><path d="M806 505v-1c1-1 1-2 2-3 0-3 2-5 3-7v9l-1 1v3h-1c-2 0-3 1-3 3-2 4-2 8-5 12-1 1-1 2-1 4-1 0-2 1-3 1h-2-1v-1c2-2 3-5 4-7 2-4 4-6 4-11v-1c2-2 2-3 3-5l2-1-1 4z" class="Q"></path><path d="M798 519c2-4 4-6 4-11v-1c2-2 2-3 3-5l2-1-1 4s0 1-1 2c0 1-1 0-1 1-1 4-2 8-4 11 0 1-1 1-1 2s0 4-1 5c0 0-1 0-1 1h-2-1v-1c2-2 3-5 4-7z" class="S"></path><path d="M807 371l1 2c1-1 1-1 2-1v1l4 6 4 6 2 5c2 2 2 4 2 7v3h1v5 2l9 30v2l-6-18c-4-11-8-22-14-32 0 4 1 8 2 12 2 3 2 7 4 9 1 7 4 14 4 21l1 3c-1 2 1 2 1 4l-1 1 2 2v1h1v2l-1 1v-1h1l-2-2c-1 0 0 0-1-1v1c-1 1-2 1-3 1-1-1-1-1-1-3l-2 1-1-1c0-1 1-2 1-3-1-2-1-4-1-6 0-1-1-3-2-4v-1c1 0 2 0 3-1l-3-10c0-2 0-2-1-4v-1c0-4-1-8-2-11-1-6-2-11-4-16v-1l5 6c0-3-1-5-2-7-1-4-2-6-4-8l1-2z" class="C"></path><path d="M816 431l2 2h1 0c1 1 1 1 1 3l1 1c0 2 0 2-1 3-1-1-2-1-1-2v-2c-1 0-1 0-2 1-1-2-1-4-1-6z" class="S"></path><path d="M817 425c1 3 1 5 2 8h0-1l-2-2c0-1-1-3-2-4v-1c1 0 2 0 3-1z" class="P"></path><path d="M814 401c2 3 2 7 4 9 1 7 4 14 4 21l-2-2c0-5-3-9-3-14 0-4-2-10-3-14z" class="M"></path><defs><linearGradient id="AT" x1="821.923" y1="387.697" x2="807.093" y2="387.259" xlink:href="#B"><stop offset="0" stop-color="#141211"></stop><stop offset="1" stop-color="#2c2d2c"></stop></linearGradient></defs><path fill="url(#AT)" d="M807 371l1 2c1-1 1-1 2-1v1l4 6 4 6 2 5c2 2 2 4 2 7v3h1v5 2c-3-7-6-14-11-19 0-3-1-5-2-7-1-4-2-6-4-8l1-2z"></path><defs><linearGradient id="AU" x1="244.528" y1="654.749" x2="330.729" y2="519.273" xlink:href="#B"><stop offset="0" stop-color="#040403"></stop><stop offset="1" stop-color="#3b3a37"></stop></linearGradient></defs><path fill="url(#AU)" d="M211 449c2-1 4-3 6-4v2l-1 1c1 2 1 3 3 4v-3h1c1 6 0 13 2 20v16l1-3v6c0-2 0-3 1-4v-1l1 16c0 17 1 36 6 53 3 14 9 27 15 41 6 15 13 29 24 42h0l17 20 9 9c2 2 4 5 6 6s3 3 5 4 5 5 7 7c0 0 2 1 2 2v1 1c4 4 9 8 13 11 8 7 16 13 25 17 3 2 7 3 10 6l1 1h1c1-1 0 0 1-2v1c3 3 7 5 11 7l1 2v1l-17-6c-6-1-12-3-18-6l-41-24c-7-3-13-8-19-13-11-8-22-17-31-28-8-10-15-22-21-34l-9-18c-2-4-4-9-6-13l-1 1c-2 3-3 7-4 11-2 9-2 20-1 30 1 4 2 8 2 12-3-7-4-15-5-23l-4-32-5-32c-1-6-2-13-8-17s-15-1-22 0c4-3 10-6 14-9 4-2 8-4 10-7 7-10 7-26 6-37 0-4 0-8-1-11 0-3-1-3-3-5h1c-1 0-1 0-2-1h0-2c-2-1-4-1-5 0l-1-1c2-2 3-2 5-3h1v-1h2l4-1c1-2 3-5 6-7 1 0 2 0 3 1h1 1l-3-2 1-1c1 0 1 0 2-1l2-2z"></path><path d="M209 451l3 1c1 1 0 2-1 4h0l-2-1-3-2 1-1c1 0 1 0 2-1z" class="L"></path><path d="M204 521h1 1c0 2 0 2-1 3v1c0 1-2 1-3 2v1l-1 1c-1-1-1-1-2-1-1-1 0-1-1-1v-1h1c1 0 2-1 2-2 1-2 2-2 3-3zm23 48l1 2 1 1v1 2c2 2 2 5 4 7v3c-2-1-3-4-3-6l-4-9 1-1z" class="D"></path><path d="M220 520h0l1 4v3l-2 1c1 1 2 1 3 2-1 2-1 2-2 3h-2v-1c0-1 0-2-1-3h0l1-1-2-2h1 0c1 1 2 0 3 0v-2c-1-1 0-3 0-4z" class="F"></path><path d="M344 717c4-1 10 1 14 3 2 1 3 2 4 3-6-1-12-3-18-6z" class="B"></path><path d="M211 449c2-1 4-3 6-4v2l-1 1c1 2 1 3 3 4v-3h1c0 2 0 4-1 6l-1-1h0c-1 1-1 1-2 1l-1-2h-1 1c-1-1 0-1-2-1-1-1-1-2-2-3z" class="M"></path><path d="M226 570l-1-1c-1-2-2-3-4-4h-5c-2 2-4 4-4 8-1 4-1 8-1 13-1 2 0 6-2 8v-5-1c1-6 0-12 2-18l1-4c0-3-1-8-2-12h1c0 1 0 3 1 4 0 2 0 2 2 4v-2l-1-1c-1-1-1-1-1-2l1-1h1v2h0v-1c0-1 0-1 1-2 0-1 0-1-1-2v-3h0v-1-2c-1-2 0-5-1-6 0-2 1-3 0-4h0 1c0 1 0 2 1 3v1c1 4 0 9 1 13 1 3-1 3 1 6l1 1 1-1c-1-1-1-1-1-2h0c0-2-1-4 0-6h0c1 1 2 2 3 4h1v-2h1v2c-1 0-1 1-2 1 0-1-1-2-2-2 0 2 1 2 1 4 0 1-1 2 0 3l2 1c2 1 4 4 5 6l-1 1z" class="F"></path><path d="M222 485l1-3v6c0-2 0-3 1-4v-1l1 16c0 17 1 36 6 53 3 14 9 27 15 41 6 15 13 29 24 42h0l17 20 9 9c2 2 4 5 6 6s3 3 5 4 5 5 7 7c0 0 2 1 2 2v1 1l-23-21c-11-10-21-22-29-34-7-11-13-23-19-35-4-9-9-19-12-29-3-7-4-15-6-23-3-20-4-38-5-58z" class="Y"></path><defs><linearGradient id="AV" x1="326.654" y1="490.206" x2="238.906" y2="618.38" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#252422"></stop></linearGradient></defs><path fill="url(#AV)" d="M225 469l1 1c2 6 3 14 6 20 8 23 24 43 46 54 4 2 11 6 16 6l1-1c1 0 1 0 2 1 2 0 7 0 9-1 1 0 1-1 2 0h1c1-1 1-1 1-2l1-1h2c-1-1-1 0-1-1l1-2h0c-1 0-3 0-4-1-1 0-2 0-3-1 10 0 19-6 26-13s8-14 8-22c0-2 0-3-1-5l1-2c0 1 0 2 1 3 0 1 0 1 1 2l1 3 1 3v2 1c0 2 0 5 1 8v-6l1 1c0 5 0 10-1 15-2 7-7 14-12 19l-4 3-6 4-6 4c1 1 1 1 2 1l9 3v-1h1l-1 2-2 1v1l3 3c5 3 10 6 15 10 2 3 5 6 7 9 4 7 6 17 4 25l-1 4c-1 1-1 0 0 1-1 1-2 2-2 3-5 5-11 9-18 10-3 0-5-1-7-3-1 0-1-1-2-1l1-1h0c5 2 10 1 15-2 3-2 6-5 7-9 1-6 0-14-3-19-4-7-12-10-19-11-10-1-20 1-28 7-5 4-6 9-7 14-3 20 8 39 19 54l6 7c2-1 5-2 7-3 2-2 3-3 5-3h0c-2 1-4 3-5 4-2 1-3 3-5 4h-1v1c-3-2-3-2-6-2-5-5-9-9-14-12-1 0-2-1-2-1-1-1-1-1-3-1-2-1-3-1-4-3l-1 1v1l-17-20h0c-11-13-18-27-24-42-6-14-12-27-15-41-5-17-6-36-6-53l-1-16v-3l1-11z"></path><path d="M268 581l1-2h-1c4-1 7-4 10-7v-1c1 1 1 0 2 1h0l1 1c-2 2-4 4-6 5l-1 1c-2 0-3 0-4 1l-2 1z" class="D"></path><path d="M242 530l7 8c1 1 2 3 4 5 1 1 4 1 5 2-1 1-2 1-4 1h-1l-1 1-1-1c-1-6-4-10-9-14l-1-1 1-1z" class="O"></path><path d="M245 545l-1 1c-2-2-4-3-5-5 0-1-1-3-1-4-1-2 0-1-1-3s-1-3 0-4v1c1-1 0-1 1-1v-1-1h1l1-1 2 2v1l-1 1 1 1c-2 0-2 0-3 1 0 5 2 9 6 12z" class="D"></path><path d="M351 590c4 7 6 17 4 25l-1 4c-1 1-1 0 0 1-1 1-2 2-2 3-1-3 1-5 1-7 2-9 0-17-3-25l1-1z" class="I"></path><path d="M270 580c1-1 2-1 4-1 0 1 0 2-1 3-1 0-2 1-3 1 0 2 0 3-2 4l-2 1-1 2-2 2h-2c2-5 4-8 7-11l2-1z" class="O"></path><path d="M270 580c1-1 2-1 4-1 0 1 0 2-1 3-1 0-2 1-3 1 0 2 0 3-2 4l-2 1-1 2c0-2 1-2 0-4 3-2 5-4 8-6h-3z" class="F"></path><path d="M281 571v-3l6-6c1-1 2-2 2-3v-1h2l5-3c1 0 2 0 4-1h1c1-1 2-1 3-1v1l-6 3c-4 2-7 3-10 6l-3 3c0 1 0 1-1 2l-3 3z" class="E"></path><path d="M259 603h1v1h1l1-1v1l-1 1v2h0v-1c1-1 1 0 1-1 1 0 1-1 1-1v-1l1-1v1c0 2 0 2-1 3h1c1 2 2 3 2 4 1 4 2 8 4 10 1 1 1 2 1 2 0 1 0 1-1 2 0-2 0-3-1-4l-1 1c0 1 1 2 1 3l-8-13c0-2-1-4-1-6l-1-1v-1z" class="F"></path><defs><linearGradient id="AW" x1="281.725" y1="644.281" x2="284.68" y2="653.426" xlink:href="#B"><stop offset="0" stop-color="#454442"></stop><stop offset="1" stop-color="#5e5d5e"></stop></linearGradient></defs><path fill="url(#AW)" d="M270 635c2 2 3 4 5 6l4 5 1-1h2l-4-5c-1-1-1-2-2-3l-1-1 1-1v-1-1h1v3c1 3 5 6 7 9 1 4 5 8 8 11-2-1-3-1-4-3l-1 1v1l-17-20z"></path><path d="M225 499c2 5 0 10 1 14v2h1c0 1 1 2 1 2 0 1-1 2-1 3l2 2h-1c0 2 1 3 1 5 0 1 0 2-1 4v4c0 1 0 0 1 1 1 2 2 7 2 10v6c-5-17-6-36-6-53z" class="B"></path><path d="M245 545c-4-3-6-7-6-12 1-1 1-1 3-1 5 4 8 8 9 14l-3 9c0 4 2 9 3 12l1 3v1h0l-1-1c-2-3-3-4-3-8v-1c-1-1-1-5 0-7v-1c0-1 0-2 1-3l1-2c0-2 0-4-1-6h0l-2-3v-1c-1-1-4-4-5-4 0-1-1 0-2 0v1c1 3 1 6 3 8 2 1 2 2 3 4-1 2-2 4-1 6v2l-2 1 1 1 1 2v5c1 0 1 1 1 2v3c1 1 1 1 2 3h-1c0-3-2-4-3-7v-1h0v-1-5c-1-1-1-2-2-3 1 0 1 0 2-1-1-1-1-1-2-1-1-1-1-2-2-4 2-1 3-1 5-2v-2z" class="H"></path><path d="M310 547h4c0-1 1-1 2-2 4-1 7-4 9-7l1-1 1-1 3-3 1 1h1c1 1 1 1 2 3h0l1-1c1-3 3-5 3-7h0c1-2 2-5 2-7 0-1 0-2 1-2 1 1 0 4 0 6-1 4-4 9-7 13h-1c-1-2-1-3-2-4h-1c-3 3-6 6-8 9h0c-1 2-3 3-4 4h-2c-2 3-2 3-5 3-2 0-3 1-5 2 0 0-1 0-2 1v-1c1 0 1-1 2-1h1c1-1 1-2 2-3s1-1 1-2z" class="D"></path><path d="M251 546l1 1 1-1h1v2 1c-2 2-3 4-4 7s1 6 2 10c2 4 5 7 10 8 2 1 4 1 6 0l1-1c1 0 2-1 3-2 2-3 2-7 2-10-1-1-1-3-2-4h1c3 3 3 5 2 9 0 4-3 7-6 9-4 1-6 2-10 0a30.44 30.44 0 0 1-8-8c-1-3-3-8-3-12l3-9z" class="F"></path><path d="M297 569c-9 5-20 13-24 22-4 7-3 16 0 23 0 2 2 4 2 6-1-1-2-2-3-4-3-7-5-15-2-23 4-10 16-21 26-25l1 1z" class="R"></path><path d="M274 561c0 3 0 7-2 10-1 1-2 2-3 2l-1 1c-2 1-4 1-6 0-5-1-8-4-10-8 2 1 3 2 5 3 1 1 1 1 2 1 1-1 1-2 2-2 0-1 1-1 1-2s0-1 1-1c1 1 2 1 3 1 0 0 1-1 2-1l1 3h1c2-2 3-4 4-7z" class="E"></path><path d="M304 554c1-1 2-1 2-1 2-1 3-2 5-2 3 0 3 0 5-3h2c1-1 3-2 4-4l-1 3v1c2 0 2 2 2 3h1c-2 2-2 2-4 2-1 0-2 1-3 1v-1c1 0 2 0 3-1 1 0 1 0 2-1 0-1 0-2-1-2h0v1c-1 1-2 1-3 1s-3 1-4 2h0c1 1 2 1 3 2-1 2-1 2-2 2h-1c-2 0-7 3-8 2-2 2-5 2-7 3s-3 3-5 4c-4 2-8 3-11 7h-1 0v-1l-1 1-1-1 1-1 3-3c1-1 1-1 1-2l3-3c3-3 6-4 10-6l6-3z" class="J"></path><path d="M306 559l-7 2v-1h0c2-2 4-3 7-4l8-3c1 1 2 1 3 2-1 2-1 2-2 2h-1c-2 0-7 3-8 2z" class="K"></path><path d="M345 521v-6l1 1c0 5 0 10-1 15-2 7-7 14-12 19l-4 3-6 4-6 4c1 1 1 1 2 1l9 3v-1h1l-1 2-2 1v1l3 3c5 3 10 6 15 10 2 3 5 6 7 9l-1 1c-3-6-10-12-16-15-10-6-26-10-37-7l-1-1c12-6 26-10 35-19 9-8 13-17 14-28z" class="V"></path><path d="M317 561c1 1 1 1 2 1l9 3v-1h1l-1 2-2 1v1l3 3-22-6 7-3 3-1z" class="C"></path><path d="M317 561c1 1 1 1 2 1l9 3c-3 0-6-1-9-1h-4l-1-2 3-1z" class="M"></path><path d="M258 545c6 3 11 7 15 12h-1c1 1 1 3 2 4-1 3-2 5-4 7h-1l-1-3c-1 0-2 1-2 1-1 0-2 0-3-1-1 0-1 0-1 1s-1 1-1 2c-1 0-1 1-2 2-1 0-1 0-2-1-2-1-3-2-5-3-1-4-3-7-2-10s2-5 4-7v-1-2c2 0 3 0 4-1z" class="H"></path><path d="M260 559c2 0 2 0 3 2v1c-1 1-1 2-2 2l-2-1h0c-2-1-2-1-2-3l1-1c0 1 1 1 2 1v-1z" class="G"></path><path d="M258 545c6 3 11 7 15 12h-1c-2-3-5-5-9-7-3-2-5-2-9-1v-1-2c2 0 3 0 4-1z" class="M"></path><path d="M270 241c0 1 0 2 1 3h1v1l6 18 6 15c1 0 2 2 2 2 1 1 2 0 2 2-1 1-1 1-1 2l2 2 1 1c1 1 2 1 4 0v-2l2 1 1-1c1 4 1 6 4 8l1 1c-1 1-1 1-2 1l2 1v1c1 1 3 1 3 3h1v6 6c0 5 0 10-1 14l-1 37c0 7 0 14-1 21v-5c-2 1-2 2-3 3-4 6-8 12-12 19-1 2-3 4-4 6-5 9-8 21-7 31l3 6c1 2 2 2 3 4 2 2 3 4 6 5h1 1c1 1 3 2 4 4-2-1-10-2-11-3s-2-1-3-1c-4 2-6 9-7 12-2 9-2 19-2 28 0 12 1 25 7 35 3 6 9 10 16 11l11 2c1 1 2 1 3 1 1 1 3 1 4 1h0l-1 2c0 1 0 0 1 1h-2l-1 1c0 1 0 1-1 2h-1c-1-1-1 0-2 0-2 1-7 1-9 1-1-1-1-1-2-1l-1 1c-5 0-12-4-16-6-22-11-38-31-46-54-3-6-4-14-6-20l-1-1v-4c1-4 1-7 2-11-1-11 0-20 3-31v-5c1-6 4-13 7-20 5-12 10-25 16-37 3-5 7-10 11-15l7-11 2-3v-1c-3 0-5 2-7 3l-1-1c3-1 6-3 9-4h-1c-1-2-4-4-6-5-17-12-37-12-56-15 1-1 5 0 7 0l12 1v-1c1 0 1-1 2-2l-1-1h1 1v1h1 0l1-1c3 0 5 0 7 1 2-2 3-2 6-2l-1-1 1-1-1-1 2-1v-1c1-1 1-1 2-1l-2-2c-1-2-1-4-2-7v-1h0c1-1 1-2 0-3 4-5 6-10 7-17v-7c0-5-1-10-2-14l-2-5 2 1h0l3 3c1 0 1 0 2-1 1 0 2 0 4 1l7 10-1-5h1c0-2-1-3-1-5h0 0l2-1c1-2 1-3 1-5z" class="H"></path><path d="M254 479c1 1 1 2 1 3v1h-1l-2 1c0-1 1-1 1-3l-1-1c1 0 1 0 2-1zm-26-12c2 3 3 7 3 11l-2-4-1-7z" class="G"></path><path d="M252 484l2-1h1c-1 2-2 3-3 4s-1 1-3 1l-1-1c1-2 2-2 4-3zm0-4h-7c0 1-1 0-2 0v-1l6-3 5 3c-1 1-1 1-2 1z" class="E"></path><path d="M260 504c1-1 1-2 2-2l5 17c-3-5-5-10-7-15z" class="B"></path><path d="M247 418l3 3c1 1 2 0 3 2 1 1 1 3 4 3l2 1c-1 0-2 0-2 1h-1c-3-2-4-4-7-5 3 3 5 5 8 7v1c-2-1-4-2-6-4l-1-2c-2 0-3 2-5 2 0-1 1-1 2-1l2-2-2-3v-1-2zm-2 24h1c-2 1-3 3-4 5h0c-2 2-3 7-2 9l1 2c1 1 1 2 1 2h-1c0 2 0 3 1 4l-2-1c0-1 0-2-1-2v-2c0-1-1-4-1-5 1-1 1-1 0-2 0-2 1-5 0-7v-1h1v2c1 0 1 1 2 1l1-1v-1l1-1v-1l2-1zm-2 18h0c1 1 1 1 3 1h0l-1-1s1 0 2-1c0-1 0-1-1-3 0-2 1-1 2-3 0-1 0-1 1-2l1 2c3-1 4-1 6-4 0 2-1 3-2 5-1 1-3 1-4 3 0 1 1 2-1 3l-1-1c-1 1-1 3-3 4-1-1-2-2-2-3z" class="E"></path><path d="M260 504l-1-2-1-1c-2-1-3-4-3-6h1c0 1 1 2 1 3 1 0 2 0 3-1l-1-6v-1c2 0 1 0 2-1h0c1 5 1 8 1 13-1 0-1 1-2 2z" class="D"></path><path d="M257 430c4 1 9 2 12 0h2c1 0 3-1 4-2v3h-2l-3 3h0l2 2v2c-2 0-3 0-5-1h-1c0-1-1-1-3-1 1-2 1-2 2-3h1 1l1-1c-3 0-7 0-10-1h-1v-1z" class="J"></path><path d="M267 437c-1-1-1-2-2-2h-1c2-1 2-1 3-1s1-1 2-2c1 0 2-1 3-1h1l-3 3h0l2 2v2c-2 0-3 0-5-1z" class="O"></path><path d="M289 377v-1c-2 0-4-1-6-3 0-1-1-2-3-3l1-1 3 2c3 2 5 3 9 2l1-1h1c0 1 0 1 1 1 1-1 3-2 4-2 0 1-1 2-2 3-4 1-8 4-10 6h-1c-2 0-2 0-3 1h0l2-2h2 0c1-1 1-1 1-2z" class="E"></path><path d="M284 371c3 2 5 3 9 2l1-1h1c0 1 0 1 1 1h0c-2 1-4 2-6 2-3 0-4-1-6-2v-2z" class="B"></path><path d="M254 440v-2c5 0 11-1 15 1l1 1c4 2 9 5 13 8 2 2 3 4 6 5h1 1c1 1 3 2 4 4-2-1-10-2-11-3v-1c0-1 0-1-1-2-4-4-12-10-17-11h-12z" class="C"></path><path d="M273 361c3-5 5-10 9-14 1 1 3 2 6 3 2 1 3 2 6 2v1l-1 1c-3-1-9-5-12-5-2 3-3 5-3 8-1 1-1 5-1 6 1 1 2 3 3 4l1 2-1 1c2 1 3 2 3 3 2 2 4 3 6 3v1h-4c-1-1-1-1-2-1s-2-1-2-2h0l-1-1c0-1-1-1-1-1-2-1-2-2-3-4-1-1-1-1-1-2s0-1-1-2c0-1 0-2-1-3z" class="G"></path><path d="M273 431h2v4h0c1 1 1 2 1 2l1 1 3 6c1 2 2 2 3 4-4-3-9-6-13-8l-1-1c-4-2-10-1-15-1v2c-3 0-6 1-8 2h-1-2l1-1c2-2 4-3 7-4 1 1 5 1 7 0h1c1-1 1-1 3 0v-1l-1-1-1-1h1c1 0 1 1 2 2v1h1 2 0 1c2 1 3 1 5 1v-2l-2-2h0l3-3z" class="F"></path><path d="M273 431h2v4h0c1 1 1 2 1 2l2 6c-2-2-5-4-6-7l-2-2h0l3-3z" class="L"></path><path d="M256 449h1 1c0 8-2 16-8 22-2 2-5 4-8 5-2 0-3 0-4-2-1 0-1 0-1-1h1l2 1v-1c1-1 1-1 2-1 2-1 4-3 5-4l-2-1-1-1c0-1-1-1-2-2s-1-2-1-4h1 1c0 1 1 2 2 3 2-1 2-3 3-4l1 1c2-1 1-2 1-3 1-2 3-2 4-3 1-2 2-3 2-5z" class="B"></path><path d="M249 464l1-1 1 1c-1 2-2 3-4 4l-2-1-1-1 2 1 3-3z" class="D"></path><path d="M242 464c-1-1-1-2-1-4h1 1c0 1 1 2 2 3 2 0 3 0 4 1l-3 3-2-1c0-1-1-1-2-2z" class="J"></path><path d="M272 445h2c-3 3-5 7-6 11v1c-1 1-1 3-1 5-1 4 0 9-1 14 0 9 1 17 2 26 1 7 2 14 4 20-1-1-1-1-2-3-4-7-5-17-6-25v-2-1l-1-1v-4c-1-1-1-3-1-4 1-1 1 0 1-1 0 1 1 2 0 3v1l1 1v-3-1c0-1 1-2 0-3l-1-1c0-1-1-2-1-2l-1-1h1c1 1 1 1 2 1v-3-4-8c1-1 1-3 1-4v-3c-1 2-1 4-1 6-1 4-2 6-1 10h0l-1 1c0-1-1-2-1-3v-1-1l1-1c0-2 0-4 1-5 0-3 1-6 3-9l6-6z" class="B"></path><path d="M227 454c0 4 0 9 1 13l1 7 2 4c2 5 3 11 6 17 2 5 5 9 6 15 10 16 24 28 43 36 2 1 5 2 8 3h1l-1 1c-5 0-12-4-16-6-22-11-38-31-46-54-3-6-4-14-6-20l-1-1v-4c1-4 1-7 2-11z" class="N"></path><path d="M229 474l2 4c2 5 3 11 6 17 2 5 5 9 6 15-7-11-13-23-14-36z" class="D"></path><path d="M283 282l3 3 3 3v-2l1 1c1 1 2 1 4 0v-2l2 1 1-1c1 4 1 6 4 8l1 1c-1 1-1 1-2 1l2 1v1c1 1 3 1 3 3h1v6 6c0 5 0 10-1 14v-8c-2 0-7 4-9 5l-12 7c-4 1-8 3-10 6-4 4-8 9-11 14-12 17-20 37-28 57-2 5-3 11-5 16v-5c1-6 4-13 7-20 5-12 10-25 16-37 3-5 7-10 11-15l7-11 2-3v-1c-3 0-5 2-7 3l-1-1c3-1 6-3 9-4h-1c-1-2-4-4-6-5-17-12-37-12-56-15 1-1 5 0 7 0l12 1v-1c1 0 1-1 2-2l-1-1h1 1v1h1 0l1-1c3 0 5 0 7 1 2 0 3 1 5 1h0c1 1 1 2 3 2h2c2 0 5-1 7 0h2 1 5l1-1h0c2 0 5 1 6 0 1 1 2 2 3 4v1h3l1 2v1h2l2-4 1-2c-1 0-2 1-4 1v-2l-1-1c2 0 3 0 5-1v-4-1l-1-8c0-5-1-8-2-13z" class="V"></path><path d="M230 310v-1c1 0 1-1 2-2l-1-1h1 1v1h1 0v2h1c2 0 6 1 7 2v1l-12-2z" class="K"></path><defs><linearGradient id="AX" x1="240.73" y1="303.722" x2="246.647" y2="314.063" xlink:href="#B"><stop offset="0" stop-color="#504f4b"></stop><stop offset="1" stop-color="#6b6a6a"></stop></linearGradient></defs><path fill="url(#AX)" d="M234 307l1-1c3 0 5 0 7 1 2 0 3 1 5 1h0c1 1 1 2 3 2h2c1 0 1 1 1 1 1 1 3 1 5 1 1 0 2 1 3 2h-7-1-2l-9-2v-1c-1-1-5-2-7-2h-1v-2z"></path><defs><linearGradient id="AY" x1="292.956" y1="314.345" x2="286.303" y2="324.945" xlink:href="#B"><stop offset="0" stop-color="#817e7c"></stop><stop offset="1" stop-color="#9d9d9c"></stop></linearGradient></defs><path fill="url(#AY)" d="M286 311l1-2v1c0 2-2 8-1 9v1h2c1-1 3-2 4-3s1-2 0-4l1-1 4 4h1c0-2 1-3 2-4h1 0v-1c2 2 2 4 3 6l-3 1c-3 3-8 4-12 5-4 2-7 3-11 4h-1c-3-1-6-3-8-4-2-2-3-2-4-3h0c2 0 3 1 4 1 3 2 7 2 10 3 1-1 2-1 2-3l1-1 1-3 2-4 1-2z"></path><path d="M301 311c2 2 2 4 3 6l-3 1c-2 0-2 0-4 1l-1-1 1-2h1c0-2 1-3 2-4h1 0v-1z" class="T"></path><path d="M283 282l3 3c0 4 0 7 1 11 1 1 1 1 2 3s2 4 4 5c1 0 1 1 1 1v1h0c1 1 2 3 3 4 0 1-1 1-1 2h2c1-1 1-1 3 0h-1c-1 1-2 2-2 4h-1l-4-4-1 1c1 2 1 3 0 4s-3 2-4 3h-2v-1c-1-1 1-7 1-9v-1l-1 2c-1 0-2 1-4 1v-2l-1-1c2 0 3 0 5-1v-4-1l-1-8c0-5-1-8-2-13z" class="F"></path><path d="M288 320c0-1 0-1-1-1v-3c1 0 2-1 3-2-1 0-1 0-2-1l1-1c0-1 0-1 1-2l2 3c1 2 1 3 0 4s-3 2-4 3z" class="B"></path><path d="M287 296c1 1 1 1 2 3s2 4 4 5c1 0 1 1 1 1v1h0c1 1 2 3 3 4 0 1-1 1-1 2h2c1-1 1-1 3 0h-1c-1 1-2 2-2 4h-1l-4-4 1-2-1-1h-1c-1-1-2-1-2-2-1-1-1-1-1-2-3-3-2-6-2-9z" class="D"></path><path d="M268 309c2 0 5 1 6 0 1 1 2 2 3 4v1h3l1 2v1h2l-1 3-1 1c0 2-1 2-2 3-3-1-7-1-10-3-1 0-2-1-4-1h0l-2-1-12-5h2 1 7c-1-1-2-2-3-2-2 0-4 0-5-1 0 0 0-1-1-1 2 0 5-1 7 0h2 1 5l1-1h0z" class="P"></path><path d="M258 312l1-1 2 1c1 0 1-1 2-1h2v2h3l1-1h0c1 1 2 1 2 1 3 1 4 5 7 4 1 0 1 0 2-1h0 1v1h2l-1 3h-2v-2c-1 0-3 1-4 0h-3v-1h-1c-1-1-2-1-3-2-1 0-1 1-2 1-1 1-1 1-2 1-1-1-1-2-1-3l-1-1-2 1h0c-1-1-2-2-3-2z" class="I"></path><path d="M261 314h0l2-1 1 1c0 1 0 2 1 3 1 0 1 0 2-1 1 0 1-1 2-1 1 1 2 1 3 2h1v1h3c1 1 3 0 4 0v2h2l-1 1c0 2-1 2-2 3-3-1-7-1-10-3-1 0-2-1-4-1h0l-2-1-12-5h2 1 7z" class="U"></path><path d="M263 319c1 0 4 0 5-1 1 0 2 0 4 1 3 1 6 2 9 2 0 2-1 2-2 3-3-1-7-1-10-3-1 0-2-1-4-1h0l-2-1z" class="R"></path><path d="M289 286l1 1c1 1 2 1 4 0v-2l2 1 1-1c1 4 1 6 4 8l1 1c-1 1-1 1-2 1l2 1v1c1 1 3 1 3 3h1v6 6c-1 3-1 3-2 5-1-2-1-4-3-6v1h0c-2-1-2-1-3 0h-2c0-1 1-1 1-2-1-1-2-3-3-4h0v-1s0-1-1-1c-2-1-3-3-4-5s-1-2-2-3c-1-4-1-7-1-11l3 3v-2z" class="E"></path><path d="M298 305c-2-2-3-4-5-6 2 0 3 2 5 2 1 0 2 0 3 1 1 2 0 4 1 5v1c-1-1-2-3-4-3z" class="F"></path><path d="M289 288c4 0 8 5 12 5l1 1c-1 1-1 1-2 1l2 1v1c-5-1-10-6-13-9z" class="V"></path><path d="M289 286l1 1c1 1 2 1 4 0v-2l2 1 1-1c1 4 1 6 4 8-4 0-8-5-12-5h0v-2z" class="X"></path><path d="M301 302c2 0 3-1 5-2v6 6c-1 3-1 3-2 5-1-2-1-4-3-6v1h0c-2-1-2-1-3 0h-2c0-1 1-1 1-2-1-1-2-3-3-4h0v-1l3 1 1-1c2 0 3 2 4 3v-1c-1-1 0-3-1-5z" class="L"></path><path d="M301 311c1-2 3-4 5-5v6c-1 3-1 3-2 5-1-2-1-4-3-6z" class="C"></path><path d="M270 241c0 1 0 2 1 3h1v1l6 18 6 15c1 0 2 2 2 2 1 1 2 0 2 2-1 1-1 1-1 2l2 2v2l-3-3-3-3c1 5 2 8 2 13l1 8v1 4c-2 1-3 1-5 1l1 1v2c2 0 3-1 4-1l-1 2-2 4h-2v-1l-1-2h-3v-1c-1-2-2-3-3-4-1 1-4 0-6 0h0l-1 1h-5-1-2c-2-1-5 0-7 0h-2c-2 0-2-1-3-2h0c-2 0-3-1-5-1 2-2 3-2 6-2l-1-1 1-1-1-1 2-1v-1c1-1 1-1 2-1l-2-2c-1-2-1-4-2-7v-1h0c1-1 1-2 0-3 4-5 6-10 7-17v-7c0-5-1-10-2-14l-2-5 2 1h0l3 3c1 0 1 0 2-1 1 0 2 0 4 1l7 10-1-5h1c0-2-1-3-1-5h0 0l2-1c1-2 1-3 1-5z" class="H"></path><path d="M285 295l1 8v1l-2 1-1-1v-2h0c-1-2-2-2-3-3h-1s0-1 1-1l-1-1v-1l1 1v2h3 0v-2l2-1v-1z" class="E"></path><path d="M256 285v-2h1c2 1 4 2 6 2l4-2-1-4v-1h1c1 2 1 4 1 6l-1 2c-2 1-3 2-4 2l-3 1c-2-1-2-1-3-3l-1-1z" class="K"></path><path d="M257 286h3 1c1 1 1 2 2 2l-3 1c-2-1-2-1-3-3z" class="C"></path><path d="M256 285l1 1c1 2 1 2 3 3l-1 1v3l-1 2h2s1 1 1 2h1v-2h1l1 1v2l1 1c1-1 1-2 1-3h1v1l1 1c-1 1-2 1-3 1h-2c-1 1-2 1-4 1h-3l1-1-2-3c0-1 0-2 1-3h1l-1-1c-1-2-1-5 0-7z" class="E"></path><path d="M259 300c-1-1-2-2-2-3l1-1h1l1 1c1 1 2 1 3 2-1 1-2 1-4 1z" class="B"></path><path d="M272 292c-1 0-1-1-1-2l1-1h0c2 4 2 6 2 10l2-2c1 0 2 1 3 2-1 1-2 2-2 3 1 2 2 3 4 4l1-1v1l-1 1v1c2 0 3 1 4-1h0v-2h-1l2-1v4c-2 1-3 1-5 1h0l-2-2h-2l-4-3h-1-2-3-1l1-1 3-3c2-2 3-4 3-6 0-1 0-1-1-2z" class="L"></path><path d="M270 300c0 1 1 1 1 2l1 1c1 0 1 0 1 1h-1-2-3-1l1-1 3-3z" class="F"></path><path d="M250 243l2 1h0l3 3 2 2-2 2 1 2c0 2 0 4 1 5v11h0v2 2h2 0l-4 3v-6l-1-1v-7c0-5-1-10-2-14l-2-5z" class="P"></path><path d="M252 244l3 3 2 2-2 2 1 2c0 2 0 4 1 5v11h0v2c-1-2-1-6-1-8 0-4-1-7-2-11-1-3-2-5-2-8z" class="E"></path><path d="M268 284c1 2 3 3 4 5l-1 1c0 1 0 2 1 2l-1 4-1-1c-1 1-2 1-3 1h-1c0 1 0 2-1 3l-1-1v-2l-1-1h-1v2h-1c0-1-1-2-1-2h-2l1-2v-3l1-1 3-1c1 0 2-1 4-2l1-2z" class="H"></path><path d="M268 284c1 2 3 3 4 5l-1 1c0 1 0 2 1 2l-1 4-1-1v-2l-2-1c0-2 2-2 1-4l-2-2 1-2z" class="E"></path><path d="M255 247c1 0 1 0 2-1 1 0 2 0 4 1l7 10c3 5 6 10 11 15h1v1c1 1 2 4 4 4v1c1 0 2 2 2 2 1 1 2 0 2 2-1 1-1 1-1 2l2 2v2l-3-3-3-3c-2-3-6-7-9-11-5-7-11-15-17-22l-2-2z" class="R"></path><path d="M254 269l1 1v6c-3 6-5 13-3 20v1l3 1c0 1 1 1 0 1l1 1h3c2 0 3 0 4-1h2c1 0 2 0 3-1l-1-1v-1c1 0 2 0 3-1l1 1 1-4c1 1 1 1 1 2 0 2-1 4-3 6l-3 3-5 1c-4 1-9-3-11-5l-2-2c-1-2-1-4-2-7v-1h0c1-1 1-2 0-3 4-5 6-10 7-17z" class="Q"></path><path d="M270 295l1 1c-2 3-4 5-8 6-2 1-5 0-7-1s-3-2-4-4l3 1c0 1 1 1 0 1l1 1h3c2 0 3 0 4-1h2c1 0 2 0 3-1l-1-1v-1c1 0 2 0 3-1z" class="D"></path><defs><linearGradient id="AZ" x1="276.41" y1="253.461" x2="269.235" y2="259.625" xlink:href="#B"><stop offset="0" stop-color="#4a4a47"></stop><stop offset="1" stop-color="#666561"></stop></linearGradient></defs><path fill="url(#AZ)" d="M270 241c0 1 0 2 1 3h1v1l6 18 6 15v-1c-2 0-3-3-4-4v-1h-1c-5-5-8-10-11-15l-1-5h1c0-2-1-3-1-5h0 0l2-1c1-2 1-3 1-5z"></path><path d="M271 244h1v1l6 18 6 15v-1c-2 0-3-3-4-4v-1c-5-9-8-19-9-28z" class="X"></path><path d="M248 303l-1-1 2-1v-1c1-1 1-1 2-1 2 2 7 6 11 5l5-1-1 1h1 3 2 1l4 3h2l2 2h0l1 1v2c2 0 3-1 4-1l-1 2-2 4h-2v-1l-1-2h-3v-1c-1-2-2-3-3-4-1 1-4 0-6 0h0l-1 1h-5-1-2c-2-1-5 0-7 0h-2c-2 0-2-1-3-2h0c-2 0-3-1-5-1 2-2 3-2 6-2l-1-1 1-1z" class="M"></path><path d="M272 304h1l4 3h2l2 2c-1 0-1 0-2-1h0l-1 2h-2s-1-1-1-2h0c-1-1-1-2-3-2h0l2-1-2-1z" class="O"></path><path d="M277 313h1 2 1 2 2l-2 4h-2v-1l-1-2h-3v-1z" class="N"></path><path d="M247 308c4-1 8 1 13 1h2l1-1c1 0 1 1 2 1s2-1 4 0h-1 0l-1 1h-5-1-2c-2-1-5 0-7 0h-2c-2 0-2-1-3-2z" class="S"></path><path d="M248 303l-1-1 2-1v-1c1-1 1-1 2-1 2 2 7 6 11 5l5-1-1 1h1 3c-2 0-4 1-5 0v1l-3 1 4 1v1c-1 0-2 0-3-1h-1v1l-2-1h0c-3 1-7 0-9-1l-2 1c-1-1-1-1-1-2h0l-1-1 1-1z" class="K"></path><path d="M248 303c1 0 2 0 3 2-1 0-1 0-2 1l-1-1h0l-1-1 1-1z" class="F"></path><defs><linearGradient id="Aa" x1="252.663" y1="391.3" x2="287.213" y2="401.344" xlink:href="#B"><stop offset="0" stop-color="#060605"></stop><stop offset="1" stop-color="#272624"></stop></linearGradient></defs><path fill="url(#Aa)" d="M273 361c1 1 1 2 1 3 1 1 1 1 1 2s0 1 1 2c1 2 1 3 3 4 0 0 1 0 1 1l1 1h0c0 1 1 2 2 2s1 0 2 1h4c0 1 0 1-1 2h0-2l-2 2h0c1-1 1-1 3-1h1c2-2 6-5 10-6 1-1 2-2 2-3-1 0-3 1-4 2-1 0-1 0-1-1 2-1 4-3 5-5l4-4c0 7 0 14-1 21v-5c-2 1-2 2-3 3-4 6-8 12-12 19-1 2-3 4-4 6-5 9-8 21-7 31l-1-1s0-1-1-2h0v-4-3c-1 1-3 2-4 2h-2c-3 2-8 1-12 0-3-2-5-4-8-7 3 1 4 3 7 5h1c0-1 1-1 2-1l-2-1c-3 0-3-2-4-3-1-2-2-1-3-2l-3-3v-1c-1-2-1-3-1-5 1-1 2-2 2-3l-1-2c2-3 4-4 6-6h1v-1h0l1-1-1-1c1-1 3-5 4-7l4-9c1-2 0-4 2-6 0 0 0-1 1-1 0-2 1-3 2-4 1-2 1-3 3-4v-1c1-2 2-3 3-5z"></path><path d="M277 396c0-1-1-3 0-4 1-3 3-4 5-5l-5 9z" class="B"></path><path d="M268 429c1-1 1-3 1-4 1 0 2 1 3 1l1-2c1 0 1-1 1-2v4c-1 1-3 2-6 3z" class="D"></path><path d="M254 410c1-2 1-3 3-3h4v2 1c1 0 1 0 1 1l-1 2v-1l-2-1-2 2-3-2v-1z" class="E"></path><path d="M254 410c2-1 4-1 6-1 0 1 0 1-1 2l-2 2-3-2v-1z" class="D"></path><path d="M293 381h3l-11 20h-1v-1c1-1 1-3 2-4l1-1v-4h-1c3-3 5-6 7-10z" class="F"></path><path d="M253 401c4-1 8-1 11 1 2 1 4 3 4 5v3h0c-2-1-1-2-2-3l-1-2c-3-3-6-3-10-2-3 0-6 4-7 6l-1-2c2-3 4-4 6-6z" class="D"></path><path d="M288 380c2-2 6-5 10-6 1 0 2-1 3 0 0 1-1 2-2 2-1 1-1 2-2 4-1 0 0 0-1 1h-3c1-2 2-3 3-5-3 2-8 6-10 10v1c-1 2-4 5-6 7 0 1 0 1-1 2s-1 2-2 3c-1 2-2 4-3 7h0c-1-2 0-2 0-4s2-4 3-6l5-9c1-2 4-4 6-7z" class="L"></path><path d="M304 363c0 7 0 14-1 21v-5c-2 1-2 2-3 3-4 6-8 12-12 19-1 2-3 4-4 6-5 9-8 21-7 31l-1-1s0-1-1-2h0v-4-3c-1 1-3 2-4 2h-2c-3 2-8 1-12 0-3-2-5-4-8-7 3 1 4 3 7 5h1c3 1 8 3 11 1 3-1 5-2 6-3 3-3 4-6 4-9 2-6 4-11 6-16h1l11-20c1-1 0-1 1-1 1-2 1-3 2-4 1 0 2-1 2-2-1-1-2 0-3 0 1-1 2-2 2-3-1 0-3 1-4 2-1 0-1 0-1-1 2-1 4-3 5-5l4-4z" class="O"></path><path d="M734 322h1c1 0 3 2 5 2 5 3 11 5 16 9 2 0 3 2 4 3 2 1 6 7 8 8 1 0 1 0 2 1 3 1 8 3 10 6l6 6a30.44 30.44 0 0 1 8 8c3 4 4 7 7 11l6 7c2 5 3 10 4 16 1 3 2 7 2 11v1c1 2 1 2 1 4l-3-1h-1-1l-1 1 1 5h0l2 6 3 29-1 1v-4h-1v5l-1 1c1 4 2 9 2 13h-1c-1 3-2 5-2 7l1 1-1 1 1 1c0 4-1 9-2 13-1 1-1 3-2 4l-1 1c0 1 1 1 1 2l-2 1c-1 2-1 3-3 5v1c0 5-2 7-4 11-1 2-2 5-4 7v1h1c2 0 3 0 5 2v1h0c1 4 2 5 0 9-2 2-4 4-6 5v1l2 1c1 2 1 7 0 9 0 2-1 5 0 7h0c-1 1-1 2-2 4v1l-3 5-3 2c-1 1-1 1-1 2s0 1-1 1c-2 2-2 2-5 2-1-1-2-1-2-3 0-1 2-3 3-5l-5 2h-3c-3 0-4-1-6-2-1 0-2-1-2-2-1 1-1 3-3 3l-2-2h-2-1c-1 0-1 0-2-1l-1 1 1 1c0 1 0 1-1 2h1 0l-1 1-1-1c-3-1 0 0-2 0l-1-1-1 1h0-1 0c-15-9-32-13-46-24v-1l-1 1h-1c0-2-1-3-2-4-1 0-2-1-2-2-1 0-1 1-2 1-2-1-3-2-4-3-1-2-1-3-2-4l-1-11v-9c1-11 1-22 10-29 0 1 0 2 1 4h0l2-2 1 1-1 3c-1 1-1 2-1 3-1 3-1 7-1 9 0 9 3 18 9 25 6 6 15 10 24 10 8 0 15-2 21-8 3-3 5-8 7-12 4-12 5-24 5-36 0-7-1-14-3-20-1-3-3-9-6-11-2 0-4 1-6 1-1 0-2 0-3-1l2-2c0-1 0-1 1-2 2-2 4-5 6-8l2 1c0-1 0-1 1-1l1-1v-1c-1-1 0-3 0-4l1-1-1-11c-1 0 0-1 0-1l1-1c-3-5-6-12-8-17-4-7-9-14-14-21h0v-4c0-1 0-1-1-2v-1c0-2 1-2 2-3 1 1 2 2 3 2v-2-1l1-1h-1c-2-1-2-2-3-3 0-2 1-3 2-5h-3c-1-2 0-4-1-5v-4c0-4 0-10-1-14l-1-2v-1c0-1-1-1-1-2l-1-4v-10z" class="G"></path><path d="M734 332c3 2 2 3 3 6v3l-1-2v-1c0-1-1-1-1-2l-1-4z" class="L"></path><path d="M806 415h1 1l1 5-1 2-2-7h0z" class="R"></path><path d="M809 420h0l2 6c0 1 0 2-1 3h-1l-1-7 1-2z" class="U"></path><path d="M780 486l-2-3c1-1 2-2 2-3v-2c0-1-1-1-1-2l3-2c0 2 0 4-1 6v1 4s0 1-1 1z" class="E"></path><path d="M756 442l2 1 1 1v1h0l-10 7c0-1 0-1 1-2 2-2 4-5 6-8z" class="Z"></path><path d="M787 405c1 0 2 1 3 2 2 3 3 4 4 7 0 1 1 1 1 2 2 2 0 6 2 8 0 1 0 3-1 4-1-2 0-5-1-6-2-3-2-5-3-8 0-1-1-3-2-4-1-2-2-4-3-5zm-12 63h3c1 4-1 7-2 10 0 3-1 5-1 8v3s0-2-1-3c0-1-1-1-1-2h0c2-1 2-2 2-3h-1 0v-1l-1-1 2-4v-7z" class="D"></path><path d="M811 426l3 29-1 1v-4h-1v5c0-10-1-19-3-28h1c1-1 1-2 1-3z" class="I"></path><path d="M790 479c1 1 1 1 1 2 1 1 0 2 0 2 0 1-1 2-1 3-3 0-6 1-8 3-1 1-2 3-3 4 0-3 0-4 1-7 1 0 1-1 1-1 1 0 1-1 2-1l3-1c0-1 1-2 2-2l2-2z" class="D"></path><path d="M790 479c1 1 1 1 1 2 1 1 0 2 0 2h-4v1l-1-1c0-1 1-2 2-2l2-2z" class="B"></path><path d="M750 548c1-1 2-3 4-4 2 0 4-1 5-1l1-1 1-1 2-1h1c0-1 0-1-1-2h0c1 0 1-1 2-1 2-2 4-2 6-3 0-1 0-1 1-1 2-1 2-2 4-3s3-4 6-4c-9 10-21 17-32 22z" class="J"></path><path d="M779 493c1-1 2-3 3-4 2-2 5-3 8-3-1 2-2 3-5 4-1 0-2 0-3 2l-3 9c-1 7-3 13-7 19h0l3-9c2-4 3-9 4-14 0-1 1-3 0-4z" class="C"></path><path d="M784 461l3 3c0 1 1 2 1 3 2 1 6 0 8 0l4-3h1v1h-1l-1 1 1 1c-1 1-3 1-4 2h1c1 2 1 2 0 3l-1 1c-1 0-1 1-2 2h-1-1v-2l2-1h-1c-2-1-4-2-6-4s-3-4-3-7z" class="J"></path><path d="M787 468c2 0 3 1 5 1 1 1 1 1 3 2h1l1 1c-1 0-2 0-3 1v-1h-1c-2-1-4-2-6-4z" class="F"></path><path d="M751 374l2-1-3 3c1 1 1 1 1 2h0c1 1 2 2 2 3l2 1c0 1 0 1 1 2 1 0 1 1 1 2h1s1 0 2 1h1c1 0 0 0 1 1 1 0 1 1 2 1l1-1 2 1c-1 1-1 1-2 1h0 0-1c-1-1-2-1-2-1-1-1-1-1-2-1s-1 0-1-1h-1c-1 0-2-1-2-1v-1l-2-2-2 1c-1-1-1 0-1-1h0c-2-3-3-3-5-4l-3-3v-2h8z" class="B"></path><path d="M751 374l2-1-3 3c1 1 1 1 1 2h-3c-1-1-2-1-2-2v-1c2 0 3-1 5-1z" class="E"></path><path d="M786 440l1 1c-2 2-3 4-4 6-1 5 1 10 1 14 0 3 1 5 3 7s4 3 6 4h1l-2 1v2c-3 0-5-2-7-4 0-1 0-3-1-4-1-2-1-3-2-5-2-7-1-12 1-19l1-1 2-2z" class="I"></path><path d="M703 548c3 0 7 4 10 6 2 1 6 3 8 5 2 1 5 1 7 2 5 2 10 5 15 6 2 0 3 3 6 3h-1l-1-2c2 1 4 2 4 4h0l-1 1h0-1 0c-15-9-32-13-46-24v-1h0z" class="J"></path><path d="M782 462c1 2 1 3 2 5 1 1 1 3 1 4 2 2 4 4 7 4h1v3h-1l-2 1-2 2c-1 0-2 1-2 2l-3 1c-1 0-1 1-2 1v-4-1c1-2 1-4 1-6v-12z" class="C"></path><path d="M784 475h0c1 2 2 4 4 6-1 0-2 1-2 2l-3 1c0-3 1-6 1-9z" class="G"></path><path d="M785 471c2 2 4 4 7 4h1v3h-1l-2 1-2 2c-2-2-3-4-4-6h0c0-1 0-3 1-4z" class="H"></path><path d="M785 471c2 2 4 4 7 4h1v3h-1c-2-1-5-4-7-4l-1 1h0c0-1 0-3 1-4zm-48-133l3 3c-1 1-1 1-2 1v1h1c2 1 3 4 4 6l1 1 1-2c1 0 2 1 4 2l2-1v-1c-3-1-4-1-5-2l-1-1c1-1 1-2 2-2l2-1c1 1 4 4 4 5 0 2 1 3 1 5 0 1 1 1 2 2 0 1 0 1 1 2v1c1 1 1 2 1 3l-1 1c-1-1-1-3-2-4h0c-1-1-1-2-2-3-3-2-4-3-7-2h-5l-2 4-1-1c0-4 0-10-1-14v-3z" class="D"></path><path d="M757 361l1-1c1 2 2 4 1 6-2 3-3 5-6 7h0l-2 1h-8v-1l1-1h-1c-2-1-2-2-3-3 0-2 1-3 2-5 0 0 0-1 1-2h4 1 1c1 1 1 1 2 3v1h2 1l1-1 1 1v-2l1-2v-1z" class="B"></path><path d="M757 361l1-1c1 2 2 4 1 6-2 3-3 5-6 7h0l-2 1h-8v-1l1-1h0c1 0 2 1 4 0 3 0 6-1 8-4 1-2 2-4 1-6v-1z" class="O"></path><path d="M746 379c2 1 3 1 5 4h0c0 1 0 0 1 1l2-1 2 2v1s1 1 2 1h1c0 1 0 1 1 1s1 0 2 1c0 0 1 0 2 1h1 0l3 3h1c0 1 1 1 1 2s-1 2-2 3c0 0 1 0 1 1 1 0 2 1 3 1h1 1v-1l1 1-1 1h-1c-2 1-3 2-4 3s-1 3-1 3c-2-1-2-1-4-1l-2-2h1v-3-1c0-2 0-3-1-4-3-2-4-6-7-7l-2-2v-1h-2 0l-1-1c-2-2-3-3-4-6z" class="J"></path><path d="M763 400c1-1 1-1 3-1 0 1 1 1 1 1l-2 2 1 1 3 1c-1 1-1 3-1 3-2-1-2-1-4-1l-2-2h1v-3-1z" class="F"></path><path d="M739 356l2-4h5c3-1 4 0 7 2 1 1 1 2 2 3h0c1 1 1 3 2 4v1l-1 2v2l-1-1-1 1h-1-2v-1c-1-2-1-2-2-3h-1-1-4c-1 1-1 2-1 2h-3c-1-2 0-4-1-5v-4l1 1z" class="H"></path><path d="M747 362v-3h3c0 2 1 3 1 4v2c-1-2-1-2-2-3h-1-1z" class="E"></path><path d="M755 357c1 1 1 3 2 4v1l-1 2h-4 0 3c1-1 1-1 1-2l-2-1c0-2 1-2 1-4z" class="G"></path><path d="M738 359v-4l1 1c1 3 2 4 4 6-1 1-1 2-1 2h-3c-1-2 0-4-1-5zm49 82c1-1 1-1 3-1 1 1 2 1 3 2h1c1 2 1 4 2 5v14h0c-2-1-2-1-3-2h1v-1h1v-1l1-1-2-2v-1l2 1c0-1-1-3 0-4-1-1-1 0-2 0l1 1-1 1-2 1c-2-1-1-2-3 0-1 1-1 2-1 3v5l-1 2c-1-1 0-2-1-3v2l1 2-3-3c0-4-2-9-1-14 1-2 2-4 4-6z" class="L"></path><path d="M787 441c1-1 1-1 3-1 1 1 2 1 3 2l1 2-1 1h-2v2c0 1-2 3-3 4h0 0c0 1 0 2-1 4v4l-2-5c-2-2-1-4-2-7 1-2 2-4 4-6z" class="G"></path><path d="M740 374c1 1 2 2 3 2l3 3c1 3 2 4 4 6l1 1h0 2v1l2 2c3 1 4 5 7 7 1 1 1 2 1 4v1 3h-1l-1-1c-1 0-2-1-3-1-2-1-3-2-5-4h0l-1-1c-1-1 0-1-1-2v-1-1l-3-3c-2-2-4-4-6-5v-1s0-1 1-1h0l-3-3h-1c0-1 0-1-1-2v-1c0-2 1-2 2-3z" class="O"></path><path d="M740 380v-1c0-1-1-1-1-2v-1h1l2 2c1 3 4 6 6 8l1 1 1 1c1 2 2 3 3 5 1 1 2 1 3 2l1 1c0 1 1 2 2 3v1c1 1 2 2 2 3-1 0-2-1-3-1-2-1-3-2-5-4h0l-1-1c-1-1 0-1-1-2v-1-1l-3-3c-2-2-4-4-6-5v-1s0-1 1-1h0l-3-3z" class="J"></path><path d="M782 526c13-13 23-33 27-51 1-6 1-11 2-17 1 4 2 9 2 13h-1c-1 3-2 5-2 7-4 14-9 27-18 39-4 5-8 9-13 14-3 3-7 6-11 9-11 7-22 13-35 13h1c6-1 11-3 16-5 11-5 23-12 32-22z" class="U"></path><defs><linearGradient id="Ab" x1="776.755" y1="461.547" x2="769.248" y2="461.372" xlink:href="#B"><stop offset="0" stop-color="#272624"></stop><stop offset="1" stop-color="#403f3d"></stop></linearGradient></defs><path fill="url(#Ab)" d="M766 446c1-2 0-3 1-5v1 1c1 0 1 1 2 2 3 1 7 0 10 2v5c-1 1-2 0-1 2v3h0v2c-1 1 0 2 0 3v2 4h-3v7l-2 4c0-1-1-2-1-3h0v-2c0-2-1-1-1-3-1 0 0-3 0-4-1-8-1-14-5-21z"></path><path d="M775 468c0-2 1-3 2-4h0 1v4h-3z" class="B"></path><path d="M768 344c1 0 1 0 2 1 3 1 8 3 10 6l6 6c-1 2-1 3 1 5s4 5 5 7v3l-1 1h-1c2 4 5 9 7 14l4 8c0 1 1 3 2 4l-2 2c-5-14-12-28-20-40l-13-17z" class="V"></path><path d="M785 365l2-3c2 2 4 5 5 7v3l-1 1h-1c-2-3-3-6-5-8z" class="a"></path><defs><linearGradient id="Ac" x1="778.208" y1="349.861" x2="785.121" y2="362.936" xlink:href="#B"><stop offset="0" stop-color="#979594"></stop><stop offset="1" stop-color="#b2b0b0"></stop></linearGradient></defs><path fill="url(#Ac)" d="M770 345c3 1 8 3 10 6l6 6c-1 2-1 3 1 5l-2 3-1-3c-4-7-9-12-14-17z"></path><defs><linearGradient id="Ad" x1="699.306" y1="503.548" x2="686.355" y2="506.23" xlink:href="#B"><stop offset="0" stop-color="#292924"></stop><stop offset="1" stop-color="#414041"></stop></linearGradient></defs><path fill="url(#Ad)" d="M698 488c0 1 0 2 1 4-2 2-3 5-4 8 0 1-1 2-1 4l-2 7c-2 11 1 23 6 32 1 2 3 4 5 5h0l-1 1h-1c0-2-1-3-2-4-1 0-2-1-2-2-1 0-1 1-2 1-2-1-3-2-4-3-1-2-1-3-2-4l-1-11v-9c1-11 1-22 10-29z"></path><defs><linearGradient id="Ae" x1="687.392" y1="535.45" x2="695.691" y2="529.129" xlink:href="#B"><stop offset="0" stop-color="#181415"></stop><stop offset="1" stop-color="#2c2c29"></stop></linearGradient></defs><path fill="url(#Ae)" d="M688 526l1-6h1c2 3 1 8 2 12s3 7 5 11c-1 0-1 1-2 1-2-1-3-2-4-3-1-2-1-3-2-4l-1-11z"></path><defs><linearGradient id="Af" x1="775.177" y1="508.716" x2="809.823" y2="508.29" xlink:href="#B"><stop offset="0" stop-color="#302e2c"></stop><stop offset="1" stop-color="#4e4e4b"></stop></linearGradient></defs><path fill="url(#Af)" d="M810 478l1 1-1 1 1 1c0 4-1 9-2 13-1 1-1 3-2 4l-1 1c0 1 1 1 1 2l-2 1c-1 2-1 3-3 5v1c0 5-2 7-4 11-1 2-2 5-4 7v1c-1 0-1 1-2 2-2 0-3 2-5 4-4 2-8 6-13 9 0 1-2 2-3 3l-6 3c0-2 0-2 2-3l2-2c1-2 2-4 4-4v-1c1-1 2-2 3-2-3 0-5 3-8 4 4-3 8-6 11-9 5-5 9-9 13-14 9-12 14-25 18-39z"></path><defs><linearGradient id="Ag" x1="775.36" y1="528.165" x2="790.205" y2="533.019" xlink:href="#B"><stop offset="0" stop-color="#302e2a"></stop><stop offset="1" stop-color="#4d4d4c"></stop></linearGradient></defs><path fill="url(#Ag)" d="M773 539c3-3 7-5 9-7 5-4 7-10 12-13l1 1 1 1 2-2c-1 2-2 5-4 7v1c-1 0-1 1-2 2-2 0-3 2-5 4-4 2-8 6-13 9 0 1-2 2-3 3l-6 3c0-2 0-2 2-3l2-2c1-2 2-4 4-4z"></path><path d="M795 520l1 1v1c-1 1-1 1-3 2v-1c0-1 1-2 2-3z" class="K"></path><defs><linearGradient id="Ah" x1="808.253" y1="390.841" x2="793.5" y2="392.756" xlink:href="#B"><stop offset="0" stop-color="#81807e"></stop><stop offset="1" stop-color="#afadae"></stop></linearGradient></defs><path fill="url(#Ah)" d="M786 357a30.44 30.44 0 0 1 8 8c3 4 4 7 7 11l6 7c2 5 3 10 4 16 1 3 2 7 2 11v1c1 2 1 2 1 4l-3-1h-1-1l-1 1h-1-1 0l-5-14 2-2c-1-1-2-3-2-4l-4-8c-2-5-5-10-7-14h1l1-1v-3c-1-2-3-5-5-7s-2-3-1-5z"></path><path d="M801 401l2-2c0 1 0 2 1 4 2 3 3 7 3 11l-1 1h0l-5-14z" class="X"></path><defs><linearGradient id="Ai" x1="802.82" y1="371.746" x2="788.315" y2="373.735" xlink:href="#B"><stop offset="0" stop-color="#838180"></stop><stop offset="1" stop-color="#a7a6a5"></stop></linearGradient></defs><path fill="url(#Ai)" d="M786 357a30.44 30.44 0 0 1 8 8c3 4 4 7 7 11l6 7c2 5 3 10 4 16l-1-2c-2-5-5-11-9-15-4-3-6-9-9-13-1-2-3-5-5-7s-2-3-1-5z"></path><defs><linearGradient id="Aj" x1="750.511" y1="542.691" x2="762.221" y2="564.516" xlink:href="#B"><stop offset="0" stop-color="#11100f"></stop><stop offset="1" stop-color="#43423e"></stop></linearGradient></defs><path fill="url(#Aj)" d="M768 540c3-1 5-4 8-4-1 0-2 1-3 2v1c-2 0-3 2-4 4l-2 2c-2 1-2 1-2 3l6-3c-2 4-8 7-9 10l1 2c-1 2-2 4-1 6l2 3h-1l1 2s1 1 2 1c-1 1-1 3-3 3l-2-2h-2-1c-1 0-1 0-2-1l-1 1 1 1c0 1 0 1-1 2h1 0l-1 1-1-1c-3-1 0 0-2 0l-1-1h0c0-2-2-3-4-4-4-3-9-6-13-8h-1l-2-2c-1 0-2 0-3-1l1-1v-1-1h3l1-1c13 0 24-6 35-13z"></path><path d="M765 548l6-3c-2 4-8 7-9 10l1 2c-1 2-2 4-1 6l2 3h-1l1 2s1 1 2 1c-1 1-1 3-3 3l-2-2h-2-1c-1 0-1 0-2-1l-1 1 1 1c0 1 0 1-1 2l-1-2h0l-3-3 1-1c1 1 1 1 2 1h1l1-1v-1l1 1h0c1-1 1-2 3-2v1h1c0-1-1-3-2-3h-1c-1-1-2-2-4-2v-1-1l2-2c2-3 6-5 9-8v-1z" class="O"></path><path d="M790 410c1 1 2 3 2 4 1 3 1 5 3 8 1 1 0 4 1 6v4 1 3c1 4 1 8 0 11-1-1-1-3-2-5h-1c-1-1-2-1-3-2-2 0-2 0-3 1l-1-1-2 2-1 1-1-1c-1 1-2 2-2 4l-1 1c-3-2-7-1-10-2-1-1-1-2-2-2v-1-1c-1 2 0 3-1 5l-3-3-4 2h0v-1l-1-1c0-1 0-1 1-1l1-1v-1c-1-1 0-3 0-4l1-1-1-11c-1 0 0-1 0-1l1-1c2 3 4 6 8 7 2 1 4 1 5 1 6-1 11-3 15-7v-1h0c2-4 1-7 0-11l1-1z" class="C"></path><path d="M768 439v-1c1-1 1-2 2-2 1-1 1 0 2-1-1-1-1-1-2-1 1-1 1 0 2 0h1c0 2 1 2 3 3-3 0-5 1-8 2z" class="N"></path><path d="M786 440c1-1 1-2 3-3h2c2 2 3 2 4 4l-1 1h0-1c-1-1-2-1-3-2-2 0-2 0-3 1l-1-1z" class="Q"></path><path d="M783 430c2 0 2 0 4-1v2c-1 0-1 1-2 2 0 0 0 2 1 3h-1c-2 1-4 0-6 0 2-2 3-4 3-6h1 0z" class="K"></path><path d="M792 414c1 3 1 5 3 8 1 1 0 4 1 6v4 1 3h-1l-2-2v-2c-1-1-2-2-2-3l1-1c1 0 1 1 3 0-1-1-1-2-1-3s0-1-1-2c-2-3-1-5-1-9z" class="M"></path><path d="M761 422c2 3 4 6 8 7 2 1 4 1 5 1 6-1 11-3 15-7v1c-1 2-3 4-6 6h0-2c-3 1-7 3-10 2-5-2-9-5-11-9l1-1z" class="R"></path><path d="M776 437h0 3l1 1h3v1 1c-5-1-12-1-16 1-1 2 0 3-1 5l-3-3-4 2h0v-1l-1-1h1c3-2 6-3 9-4s5-2 8-2z" class="Q"></path><path d="M767 441c4-2 11-2 16-1l-1 2c-1 1-2 2-2 4l-1 1c-3-2-7-1-10-2-1-1-1-2-2-2v-1-1z" class="H"></path><path d="M740 380l3 3h0c-1 0-1 1-1 1v1c2 1 4 3 6 5l3 3v1 1c1 1 0 1 1 2l1 1h0c2 2 3 3 5 4 1 0 2 1 3 1l1 1 2 2c2 0 2 0 4 1 0 0 0-2 1-3s2-2 4-3h1l1-1c4 0 7 0 10 3l2 2c1 1 2 3 3 5l-1 1c1 4 2 7 0 11h0v1c-4 4-9 6-15 7-1 0-3 0-5-1-4-1-6-4-8-7-3-5-6-12-8-17-4-7-9-14-14-21h0v-4h1z" class="L"></path><path d="M783 419h3v2h-2c-1 1-1 1-1 0l-1-1 1-1zm-6 5l1 2-3 2h0c-1-1-2-1-3-1 2-2 3-1 5-3z" class="F"></path><path d="M768 420l2 1 2 2h2c1 0 2 0 3 1-2 2-3 1-5 3h-1c0-1-1-2-1-3h-1c0-2-1-2-1-4z" class="O"></path><path d="M775 410c2 0 3-1 5 0v1c1 2 3 3 4 6l-1 1c-1 0-1 1-2 1l-1 1v2h1l-2 4h-1l-1-2c-1-1-2-1-3-1h-2l-2-2c1 0 2 0 3 1h0c1 0 1-1 2-1s1 0 1-1h-2l-1-1 1-1-2-1c1-1 2-1 2-2 0-2 0-3-1-5h0 2z" class="T"></path><path d="M772 417c1-1 2-1 2-2 0-2 0-3-1-5h0 2v2c1 1 2 1 2 2 0 2 0 2 1 4v2l2 1-1 1c-3-1-3 0-5 1h-2l-2-2c1 0 2 0 3 1h0c1 0 1-1 2-1s1 0 1-1h-2l-1-1 1-1-2-1z" class="B"></path><path d="M775 400c4 0 7 0 10 3l2 2c1 1 2 3 3 5l-1 1c1 4 2 7 0 11h0c-4-2-2-4-4-7v-1c-1-2-2-4-5-4-2-1-3 0-5 0h-2 0c1 2 1 3 1 5 0 1-1 1-2 2l2 1-1 1-2-2-2-3c-2-2-2-4-1-7 0 0 0-2 1-3s2-2 4-3h1l1-1z" class="H"></path><path d="M775 400c4 0 7 0 10 3l2 2c1 1 2 3 3 5l-1 1c-2-4-4-8-9-9-2 0-5 0-8 1-1 1-2 2-3 4v5l1 1c0 1 1 3 2 4l2 1-1 1-2-2-2-3c-2-2-2-4-1-7 0 0 0-2 1-3s2-2 4-3h1l1-1z" class="N"></path><defs><linearGradient id="Ak" x1="766.506" y1="396.827" x2="748.763" y2="404.479" xlink:href="#B"><stop offset="0" stop-color="#1c1b18"></stop><stop offset="1" stop-color="#3e3d3b"></stop></linearGradient></defs><path fill="url(#Ak)" d="M740 380l3 3h0c-1 0-1 1-1 1v1c2 1 4 3 6 5l3 3v1 1c1 1 0 1 1 2l1 1h0c2 2 3 3 5 4 1 0 2 1 3 1l1 1 2 2c2 0 2 0 4 1-1 3-1 5 1 7l2 3 2 2 1 1h2c0 1 0 1-1 1s-1 1-2 1h0c-1-1-2-1-3-1l-2-1-3-3-2-3-1 6c2 2 2 4 4 5s2 2 3 4h0c-4-1-6-4-8-7-3-5-6-12-8-17-4-7-9-14-14-21h0v-4h1z"></path><path d="M764 406c2 0 2 0 4 1-1 3-1 5 1 7l2 3h-3c-3-3-3-6-4-10v-1z" class="J"></path><path d="M794 527h1c2 0 3 0 5 2v1h0c1 4 2 5 0 9-2 2-4 4-6 5v1l2 1c1 2 1 7 0 9 0 2-1 5 0 7h0c-1 1-1 2-2 4v1l-3 5-3 2c-1 1-1 1-1 2s0 1-1 1c-2 2-2 2-5 2-1-1-2-1-2-3 0-1 2-3 3-5l-5 2h-3c-3 0-4-1-6-2-1 0-2-1-2-2-1 0-2-1-2-1l-1-2h1l-2-3c-1-2 0-4 1-6l-1-2c1-3 7-6 9-10 1-1 3-2 3-3 5-3 9-7 13-9 2-2 3-4 5-4 1-1 1-2 2-2z" class="R"></path><path d="M785 537c1 2 1 3 2 4l1-3v5c0 1 1 9 2 10 0 3-1 5-1 8-1-3-1-7-2-9s-2-4-3-5v-1h-1l-1-1-1-1h0-4 1c2-2 3-3 6-3v-3l1-1z" class="Q"></path><path d="M785 537c1 2 1 3 2 4v2h-1l-2-2v-3l1-1z" class="Z"></path><path d="M771 545c1-1 3-2 3-3 5-3 9-7 13-9 0 2 0 3-2 4l-1 1v3c-3 0-4 1-6 3h-1c-1 0-1 1-2 1-2 0-2 1-4 2l-1 1 1 1c-3 2-6 4-8 8l-1-2c1-3 7-6 9-10z" class="P"></path><path d="M794 527h1c2 0 3 0 5 2v1h0c1 4 2 5 0 9-2 2-4 4-6 5h-1l-2 2-3-3v-5l-1 3c-1-1-1-2-2-4 2-1 2-2 2-4 2-2 3-4 5-4 1-1 1-2 2-2z" class="G"></path><path d="M794 527h1c2 0 3 0 5 2v1c-3-1-4-1-7 0-1 0-1 0-1-1 1-1 1-2 2-2z" class="X"></path><path d="M792 529c0 1 0 1 1 1-2 3-4 5-5 8l-1 3c-1-1-1-2-2-4 2-1 2-2 2-4 2-2 3-4 5-4z" class="R"></path><path d="M788 543l3 3 2-2h1v1l2 1c1 2 1 7 0 9 0 2-1 5 0 7h0c-1 1-1 2-2 4v1l-3 5-3 2c-1 1-1 1-1 2s0 1-1 1c-2 2-2 2-5 2-1-1-2-1-2-3 0-1 2-3 3-5 3-3 5-6 7-10h0c0-3 1-5 1-8-1-1-2-9-2-10z" class="B"></path><path d="M794 545l2 1c1 2 1 7 0 9 0 2-1 5 0 7h0c-1 1-1 2-2 4v1l-3 5-3 2c1-2 2-5 3-7 1-1 1-2 2-3 1-3 0-5 2-7h0v-2c0-2 1-6 0-8l-1-2zm-5 16c0 5-2 7-4 10l1 1v1c-1 1-3 2-4 3h-1l1 2c1-1 1-1 2-1h0c1 0 1 0 2-1h1c0 1 0 1-1 1-2 2-2 2-5 2-1-1-2-1-2-3 0-1 2-3 3-5 3-3 5-6 7-10z" class="F"></path><path d="M788 543l3 3 2-2h1v1l1 2c0 1 0 3-1 4l-1 1-1 1c1 1 1 2 2 3 0 0-1 1-1 2-1 1-2 3-3 4 0-3 1-6 0-9-1-1-2-9-2-10z" class="D"></path><path d="M793 552l-1-1c-1-1-2-1-2-3v-1l1 1c2 0 2 1 3 3l-1 1z" class="G"></path><path d="M771 549c3-2 6-3 9-2s4 2 5 5l1 2c0 3 0 7-3 9l-3 4c-3 2-8 2-11 2l-1 2c-1 0-2-1-2-2-1 0-2-1-2-1l-1-2h1l-2-3c-1-2 0-4 1-6 2-4 5-6 8-8z" class="K"></path><path d="M762 563c2 0 4-3 6-5h0l-3 6c2 1 2 1 2 3l-1 1-3-2h1l-2-3z" class="B"></path><path d="M777 554l4 1v1l-2 1c-1 0-2 0-2-1h-1l-1 2h-1l-3 3-1-1c-1-1 0-1 0-2 3-1 5-3 7-4z" class="C"></path><path d="M786 554c0 3 0 7-3 9l-3 4c-3 2-8 2-11 2l-1 2c-1 0-2-1-2-2-1 0-2-1-2-1l-1-2 3 2h1l2 1c1-1 1-3 2-4v-1c1-1 1-2 1-2l1-1v-1l3-1h0 6 0c2-1 2-3 4-5h0z" class="N"></path><path d="M773 561v-1l3-1h0c2 1 4 2 4 4v1h-1c-2-1-4 0-5 0l-1-1 1-2h-1z" class="C"></path><path d="M771 549c3-2 6-3 9-2s4 2 5 5c-1 1-1 2-2 2-2 0-4-2-6-2v1h-1c-3 0-6 3-8 5s-4 5-6 5c-1-2 0-4 1-6 2-4 5-6 8-8zM518 197c8-1 15 1 23 2h0c-1 0-2-1-3-1l-2-1c1-1 4 0 6 0h6c6 0 12-1 17 1h2 2 1 0c1 0 1 0 1 1l-1 1h2 1c1 1 1 0 2 1 2 0 3 1 5 1l1 1h0l-3-3c-1 0-2-1-3-1v-1l3 1c4 0 11 2 14 1h2l3 1c3 1 4 3 6 5l1-1h0v1 1h1c0 1 1 1 1 2 1 0 2 0 3 1h1-5 0l-2 1c-1 0-1 0-2 1h-1 0l2 1c1 1 2 1 3 2 2-1 3 0 5 2 1 1 2 2 2 3l49 19 13 6c12 5 24 10 34 18 1 1 3 2 5 3 1 1 3 3 4 5l4 3c4 5 7 6 12 7h1c-3 0-6 1-9 0v1 1c-2 1-4 1-7 1l-5 1c-1-1-3-3-4-3-1-1-6 0-7-1 0-1 1-1-1-2-1 0-2 1-3 2h-1c0-1 0-1-1-2h0-1c-1 0-1 1-2 1-1-1-1-1 0-2-1-2-1-1-3-2l-2 2c-1 1-2 0-2 1 1 0 2 2 4 3v3h-32c-4 0-10 1-14 0-4-2-7-3-11-5l-14-6c-4-2-7-4-11-5-2 0-3 1-4 2l-2 3h-2l-4-3h-2l-1-3-3-4c-8-12-26-17-41-20l-11-1c-12 11-15 25-19 40 0 1-1 1-1 2l-1-1-7-26v-3-2c-2-4-4-8-6-11-1 0-2-1-3-1l-3-3h-1l3 4c0 1 0 1-1 1-10 1-20 3-29 6-10 3-22 7-28 15l-5 6c-2 1-3 2-4 3-1 0-2-2-1-2 1-5 4-6 4-12h0-1c-1 3-1 6-3 7-2 2-5 2-7 3-5 1-9 3-14 5l-10 5c-3 2-7 4-11 5s-9 0-14 0c-5 1-9 1-14 1h-6-13l-10-1c-6 1-13 0-20-1 9-8 18-16 28-23 13-10 27-17 41-24 3-1 7-4 11-4v2c1-1 1-1 1-2l6-3 57-18c1 1 4-1 5-1l20-5c7-2 15-3 22-4v-2l14-2z" class="H"></path><path d="M596 262c0-2 0-5 1-7 1 2 0 5 0 7l-1 1v-1z" class="D"></path><path d="M617 248h1c1 0 2-1 3-1s2 0 4 2h0c-2-1-3 0-5 1l-3-2z" class="E"></path><path d="M545 211c2 1 5 1 7 3v1h-4c-1-2-2-3-3-4zm-134 34c1-1 2-1 4-2 0 0 1 0 2 1 0 2 0 2-2 3h-3l-1-2z" class="G"></path><path d="M365 265c2-3 5-4 9-5h1l-3 3c-2 0-3 0-5 1 0 0-1 1-2 1z" class="E"></path><path d="M563 221c-4 0-9-1-13-2 5-1 12-1 17 0-1 0-3 0-5 1 1 0 0 0 1 1z" class="N"></path><path d="M455 221l10-1 2 1-4 2-9-1v-1h1z" class="K"></path><path d="M454 222h-12-7-2c2-1 4-1 6-1h16-1v1z" class="J"></path><path d="M465 220l16-2v2c-5 0-9 1-13 1h-1l-2-1z" class="C"></path><path d="M374 268c3-3 6-6 10-8-1 2-4 6-4 8v1l-3 1c0-1 1-1 1-2v-1l-4 1z" class="B"></path><path d="M642 257c1 0 1-1 2-1v-1c1 0 1 0 3 1 0 0 1 0 2 1 1 0 2 0 3 1h1l1 1c-1 0-1 1-2 1v1c-3-1-5-1-7-3l-1-1h-2z" class="G"></path><path d="M567 219l13 1v1l-1 1c-5-1-11-1-16-1-1-1 0-1-1-1 2-1 4-1 5-1z" class="S"></path><path d="M625 249h1 1c1-1 2-1 3-1 1 1 1 1 2 1l1 1c-1 0-2 0-2 1-1 0-1 1-2 1l2 2c1 0 3 2 4 3-2-1-4-3-6-3l-1-1v-2l-2-1h-2v2c-1 0-1-1-2-1l-1-1h-1c2-1 3-2 5-1zm71 29c2 1 2 1 4 1l2-1c0-1-1-1 0-2 0 1 1 1 2 1h0c2 0 3 2 5 3h1c1 1 2 3 3 3h1c-1-1-1-2-2-2v-1l4 3c1 0 2 1 2 1l-5 1c-1-1-3-3-4-3-1-1-6 0-7-1 0-1 1-1-1-2-1 0-2 1-3 2h-1c0-1 0-1-1-2h0v-1z" class="D"></path><path d="M547 233h0c-1 0-1 1-2 1h-1c-1 0-2 0-3 1h0l-1-1c1-1 3-1 5-2h1c1 0 3-3 4-4 0-2-1-4 0-6l1 1v1c1 2 1 3 1 5h1l1-1c2-2 3-4 6-5 2 1 3 1 5 2-5 1-9 3-13 6h-1c-1 1-1 0-2 1 0 0-1 1-2 1z" class="G"></path><path d="M590 264h3l3-2v1l1-1 1 7 6 2-2 3h-2l-4-3h-2l-1-3-3-4z" class="B"></path><path d="M590 264h3l3-2v1c0 2 1 4 0 5h-3l-3-4z" class="G"></path><path d="M710 269l1-1c3 0 4 2 6 3l4 3c4 5 7 6 12 7h1c-3 0-6 1-9 0v1c-3-1-5-5-7-7l-8-6z" class="V"></path><path d="M333 284c1 0 2-1 2-1 2 0 3 1 4 0v-2l2-2 1-1h1l1-1h0c1 0 1 0 2-1h2l4-1c0-1 0-1 1-1h1c1-1 1-2 2-3h0c1-2 3-2 4-2l-2 2c-3 2-4 5-6 7s-3 2-4 4c-2 0-1-1-3 0l-2 3-10-1z" class="T"></path><path d="M380 269c7-3 13-6 16-12l1-2v1c1 1 0 2-1 4s-4 5-7 6c-2 1-3 2-5 3-5 1-8 4-12 5l-4 2c-2 1-4 3-6 4l-1 1v-1c1-2 2-3 3-4 2-1 3-2 5-3v-1l2-2c1 0 2-2 3-2l4-1v1c0 1-1 1-1 2l3-1z" class="O"></path><path d="M374 268l4-1v1c0 1-1 1-1 2-3 2-5 2-8 2l2-2c1 0 2-2 3-2z" class="F"></path><path d="M384 269c-3 2-6 3-9 5v1h1 2v-1h1 1 2v-1h1c3-1 5-2 8-3 2-2 3-2 5-2-2 3-6 7-10 7-1 0-1 0-2 1h-2c-1 0-2 0-3 1h-1-1c-1 1-2 1-3 1s-2 0-2 1c-3 1-6 0-8 2l-2-1c2-1 4-3 6-4l4-2c4-1 7-4 12-5z" class="J"></path><path d="M611 262l-15-8c-3-2-7-3-10-4l-18-6c-3 0-6-1-9-2-2 0-4 0-5-1 2-1 4 0 6 0 4 1 8 1 11 2l6 2c2 0 4 0 6 1h0c1 1 2 1 4 2h0c1 0 1 0 2 1 4 1 8 3 11 5l6 3 2 2h0c2 0 2 1 3 2v1z" class="B"></path><path d="M467 221h1l1 2s1 0 2-1c2-1 6 0 8 0 2 2 4 3 6 4 1 1 2 1 3 2 1 0 2 1 4 2l3 1-1 1c4 3 8 5 10 10-1 0-2-1-3-1l-3-3h-1s-2-1-2-2c-2-1-4-3-6-4-8-5-16-7-26-9l4-2z" class="F"></path><path d="M471 222c2-1 6 0 8 0 2 2 4 3 6 4 1 1 2 1 3 2 1 0 2 1 4 2l3 1-1 1c-1 0-3-1-4-1l-8-5c-4-1-7-3-11-4zm43-10c1-1 3-1 5-2 8-2 18 0 26 1 1 1 2 2 3 4-3 0-5 0-8-1-6-1-14-1-19 1v1c-1 0-2 1-2 1 0-1 0-3-1-4v2h-1v3h-1c0-1 0-2-1-3v-2l-1-1z" class="E"></path><path d="M497 214c5-1 12-2 17-2l-1 2c-5 0-11 1-15 3-3 2-7 1-9 4-1 2-1 3 0 5l2 2 1 2c-2-1-3-2-4-2-1-1-2-1-3-2-2-1-4-2-6-4-2 0-6-1-8 0-1 1-2 1-2 1l-1-2c4 0 8-1 13-1v-2l7-2 9-2z" class="J"></path><path d="M481 218l7-2 1 2c-3 1-5 1-8 2v-2z" class="M"></path><path d="M488 216l9-2v2h0l-2 1c-1 0-5 1-6 1l-1-2z" class="O"></path><defs><linearGradient id="Al" x1="584.392" y1="226.521" x2="599.108" y2="217.479" xlink:href="#B"><stop offset="0" stop-color="#565751"></stop><stop offset="1" stop-color="#767171"></stop></linearGradient></defs><path fill="url(#Al)" d="M580 220c8 0 16 2 24 3 4 0 8 1 11 0 2 1 4 1 5 2-19-1-40-3-59 3-1 1-2 1-3 1-3 1-6 3-9 5l-2 1s0-1-1-1c1 0 1-1 1-1 1 0 2-1 2-1 1-1 1 0 2-1h1c4-3 8-5 13-6s11-1 15-3h-1l1-1v-1z"></path><defs><linearGradient id="Am" x1="365.619" y1="275.909" x2="346.382" y2="272.591" xlink:href="#B"><stop offset="0" stop-color="#323130"></stop><stop offset="1" stop-color="#504f4b"></stop></linearGradient></defs><path fill="url(#Am)" d="M365 265c1 0 2-1 2-1 2-1 3-1 5-1l-1 1c-4 4-10 6-12 12l-1 3c3-3 9-9 13-9l-2 2v1c-2 1-3 2-5 3-1 1-2 2-3 4v1l1-1 2 1-3 3h-5v1h-13l2-3c2-1 1 0 3 0 1-2 2-2 4-4s3-5 6-7l2-2c1-1 4-3 5-4z"></path><path d="M412 247c-2 1-4 5-4 7v1l2 1c3-2 6-4 10-6l26-11c-9 9-25 9-33 18 2 0 3 0 5-1 4-1 8-3 12-5-1 4-6 6-9 7l-15 9 1-1 1-1c1-1 2-1 2-1l1-1c1 0 2-1 3-2 0 0 1 0 2-1-1 0-2 0-3 1l-6 4h-1c1-2 3-3 4-4v-1h-1c-1 2-3 3-5 4-1 1 0 1-1 1-2 1-4 3-7 3 2-1 4-2 6-4 1-2 3-3 4-5v-2c0-2 0-3 1-4 1-3 2-5 4-8l1 2z" class="J"></path><path d="M402 264c1-2 3-3 4-5v-2c0-2 0-3 1-4h0c0 3 1 4 2 6 0 1-4 4-6 5h-1zm253 10c2 1 6 2 8 1h2v-1c2-1 3-2 4-2l1 1h5c-1 0-2-1-2-1l-1-1c1 0 3 1 4 2v1c2 0 3-1 5-2l1 1h1 0c0-1 0-2 1-2s1 0 2-1l1 1c1 0 1 0 1 1h1l8 2h0-3-1l2 2c0 1-1 2-1 2h0 2v1h-1c-1 0-1 1-2 1-1-1-1-1 0-2-1-2-1-1-3-2l-2 2c-1 1-2 0-2 1l-3 1v-2l-2-1c-2 0-5 1-6 2-1 0-1 0-2 1l-1-2-1-1-1 1c-2 1-3 0-5 0-1 1 0 1-2 1h-2-1l1-1h-4c-2 0-5-3-7-3l-2-2c2 0 2 0 4 1 1 0 3 1 4 2s2 1 3 1h1l-1-1c-2-1-3-1-4-2z" class="B"></path><path d="M661 278c1-1 3 0 5-1 1-1 2-2 4-2h0 3c1 1 0 1 1 1l1-1c-1 1-1 2-2 3h-1l-1-1-1 1c-2 1-3 0-5 0-1 1 0 1-2 1h-2-1l1-1z" class="J"></path><path d="M675 275c2 0 3 1 4 0h5 0c2 0 1 0 2 1h1l1 2c-1 1-2 0-2 1l-3 1v-2l-2-1c-2 0-5 1-6 2-1 0-1 0-2 1l-1-2h1c1-1 1-2 2-3z" class="O"></path><path d="M396 268c3 0 5-2 7-3 1 0 0 0 1-1 2-1 4-2 5-4h1v1c-1 1-3 2-4 4h1l6-4c1-1 2-1 3-1-1 1-2 1-2 1-1 1-2 2-3 2l-1 1s-1 0-2 1l-1 1-1 1-30 17c-5 1-9 1-14 1h-6v-1h5l3-3c2-2 5-1 8-2 0-1 1-1 2-1s2 0 3-1h1 1c1-1 2-1 3-1h2c1-1 1-1 2-1 4 0 8-4 10-7z" class="K"></path><path d="M624 250h2l2 1v2l1 1c2 0 4 2 6 3 1-1 1-1 2-1h1 2c1 0 1 1 2 1h0 2l1 1c2 2 4 2 7 3v-1c1 0 1-1 2-1 3 1 7 3 8 6l1 1c-1 0-2 1-3 1-1 1-2 1-3 2-1 0-2 0-3 1-2-1-4-2-7-3-5-2-9-4-13-7-5-3-7-5-10-10z" class="B"></path><path d="M635 257c1-1 1-1 2-1h1 2c1 0 1 1 2 1h0 2l1 1c2 2 4 2 7 3v-1c1 0 1-1 2-1 3 1 7 3 8 6l-1-1h-3c-1 0-2 0-2 1l-2-2h-4c-2 0-3-1-3-2-1-1-1-1-3-1l1-1-1-1c-1 0-1 1-2 1 0 0-1 0-2-1v-1h-3l-1 1-1-1z" class="D"></path><path d="M624 250h2l2 1v2l1 1c1 1 3 3 5 4s4 0 6 3h2c1 2 3 2 5 3 1 0 2 1 4 2 1 0 2 0 3 1h5l1-1h0v1c-1 1-2 1-3 2-1 0-2 0-3 1-2-1-4-2-7-3-5-2-9-4-13-7-5-3-7-5-10-10z" class="J"></path><path d="M606 218l6 2 49 19 13 6c12 5 24 10 34 18 1 1 3 2 5 3 1 1 3 3 4 5-2-1-3-3-6-3l-1 1c-6-4-11-8-16-12-23-14-48-24-74-32-1-1-3-1-5-2l-10-4s0-1 1-1z" class="Y"></path><path d="M514 212l1 1v2c1 1 1 2 1 3h1v-3h1v-2c1 1 1 3 1 4l-1 21v44l1 1c0 1-1 1-1 2l-1-1-7-26v-3l2 5 1 1c1-6 0-13 1-18-1-1-1-3-1-5 0-8 2-16 0-24l1-2z" class="M"></path><path d="M517 237v19h0c-1 2 0 8-1 10-1-2-1-6-1-8v-2c-2 2-1 3-2 5 1-6 0-13 1-18h1 1l1-6z" class="K"></path><path d="M518 215v-2c1 1 1 3 1 4l-1 21c-1 11 0 23 0 34-1-1-1-3-1-4v-12-19l1-22z" class="R"></path><path d="M514 212l1 1v2c1 1 1 2 1 3h1v-3h1l-1 22-1 6h-1-1c-1-1-1-3-1-5 0-8 2-16 0-24l1-2z" class="N"></path><path d="M518 215l-1 22-1 6h-1c1-3 1-7 1-10 0-6 0-12-1-18 1 1 1 2 1 3h1v-3h1z" class="B"></path><path d="M491 228l-2-2c-1-2-1-3 0-5 2-3 6-2 9-4 4-2 10-3 15-3 2 8 0 16 0 24 0 2 0 4 1 5-1 5 0 12-1 18l-1-1-2-5v-2c-2-4-4-8-6-11-2-5-6-7-10-10l1-1-3-1-1-2z" class="H"></path><path d="M491 228h1c2 1 3 1 5 0s3-2 3-3c1-2 0-4 0-5v-1l3 4c-1 3-3 6-6 7l-2 1-3-1-1-2z" class="N"></path><path d="M430 251c9-3 17-5 26-7 2 0 7-2 9-1-2 1-4 1-7 2l-10 3c-4 1-9 3-13 5 0 2 0 4 1 6h-1c-1 3-1 6-3 7-2 2-5 2-7 3-5 1-9 3-14 5l-10 5c-3 2-7 4-11 5s-9 0-14 0l30-17 15-9c3-1 8-3 9-7z" class="H"></path><defs><linearGradient id="An" x1="448.062" y1="242.629" x2="432.126" y2="256.514" xlink:href="#B"><stop offset="0" stop-color="#1e1d1a"></stop><stop offset="1" stop-color="#393936"></stop></linearGradient></defs><path fill="url(#An)" d="M430 251c9-3 17-5 26-7 2 0 7-2 9-1-2 1-4 1-7 2l-10 3c-4 1-9 3-13 5 0 2 0 4 1 6h-1l-1-6c-14 7-27 15-40 23-4 2-9 5-13 7l9 1c-4 1-9 0-14 0l30-17 15-9c3-1 8-3 9-7z"></path><defs><linearGradient id="Ao" x1="605.424" y1="228.765" x2="511.378" y2="184.414" xlink:href="#B"><stop offset="0" stop-color="#767675"></stop><stop offset="1" stop-color="#bbb9b9"></stop></linearGradient></defs><path fill="url(#Ao)" d="M518 197c8-1 15 1 23 2h0c-1 0-2-1-3-1l-2-1c1-1 4 0 6 0h6c6 0 12-1 17 1h2 2 1 0c1 0 1 0 1 1l-1 1h2 1c1 1 1 0 2 1 2 0 3 1 5 1l1 1h0l-3-3c-1 0-2-1-3-1v-1l3 1c4 0 11 2 14 1h2l3 1c3 1 4 3 6 5l1-1h0v1 1h1c0 1 1 1 1 2 1 0 2 0 3 1h1-5 0l-2 1c-1 0-1 0-2 1h-1 0l2 1c1 1 2 1 3 2 2-1 3 0 5 2 1 1 2 2 2 3l-6-2c-1 0-1 1-1 1l-25-8c-3 0-7-1-10-2-14-4-30-9-45-10-7 0-14 1-21 2v-2l14-2z"></path><path d="M570 209l1-1v-1c3 1 7 2 10 3l-1 1c-3 0-7-1-10-2z" class="a"></path><path d="M581 210l25 8c-1 0-1 1-1 1l-25-8 1-1z" class="V"></path><path d="M578 199c4 0 11 2 14 1h2l3 1c3 1 4 3 6 5l1-1h0v1 1h1c0 1 1 1 1 2 1 0 2 0 3 1h1-5c-8-2-15-7-24-10l-3-1z" class="I"></path><path d="M578 199c4 0 11 2 14 1h2l3 1c3 1 4 3 6 5-2 0-4-1-5-2-3-1-6-1-8-2s-1-1-2-1h-1-2c-1-1-2-1-4-1l-3-1z" class="S"></path><defs><linearGradient id="Ap" x1="595.449" y1="230.5" x2="676.575" y2="298.274" xlink:href="#B"><stop offset="0" stop-color="#0e0d09"></stop><stop offset="1" stop-color="#4b4a4a"></stop></linearGradient></defs><path fill="url(#Ap)" d="M571 243c2-1 5-1 7-1l1 1h1 1 1c1 1 1 1 2 1 2 0 3 0 5 1h2l3 1s1 1 2 1h0c2 1 4 1 5 1h1l1 1h1c0-2 0-2 1-4-2 0-2 1-3 2l-1-1c1-1 2-1 3-2 1 0 3 1 4 1h2c2 1 5 2 7 3l3 2h1l1 1c1 0 1 1 2 1v-2c3 5 5 7 10 10 4 3 8 5 13 7 3 1 5 2 7 3-1 0-2 1-3 2v-1l-1-1v2h1c1 0 2 1 4 2h0c1 1 2 1 4 2l1 1h-1c-1 0-2 0-3-1s-3-2-4-2c-2-1-2-1-4-1l2 2c2 0 5 3 7 3h4l-1 1h1 2c2 0 1 0 2-1 2 0 3 1 5 0l1-1 1 1 1 2c1-1 1-1 2-1 1-1 4-2 6-2l2 1v2l3-1c1 0 2 2 4 3v3h-32c-4 0-10 1-14 0l4-1h0l-37-22v-1c-1-1-1-2-3-2h0l-2-2-6-3c-3-2-7-4-11-5-1-1-1-1-2-1h0c-2-1-3-1-4-2h0c-2-1-4-1-6-1l-6-2z"></path><path d="M488 860c12-15 17-35 22-53h-50-51l-24 1c-4 0-11 1-15 0l-1-1c-1 0-1-1-1-1 2-2 11-2 15-3 8-1 17-2 25-4 11-4 21-9 29-16 21-20 27-50 28-77V350l-51-1c-9 0-20-1-29 1-15 4-31 13-41 24-14 14-24 32-28 51-1 5-1 9-2 14 0 2 0 6-1 8 0 1-1 1-2 1s-2-1-3-2c-2-4-2-9-2-13l1-27 2-62c1-8 3-44 1-49-2-3-6-4-8-7v-2c2-1 3 0 5 0 5 2 8 3 14 3 20 2 40 1 60 1h113 140 78c6-1 13-1 18-3l2-1c1 0 1 0 2 1-1 4-5 5-6 8-1 1-1 3-1 4 0 6 1 13 2 19l5 90v20c0 5 0 10-1 15v1c0 1-1 3-2 4-2 1-2 1-3 1l-1-1c-1-1 0-3 0-3l-1-8c0-5 0-10-1-15-6-28-20-54-47-66-6-3-13-6-18-6h-81l1 236v61c0 23-1 47 4 70 4 20 14 39 28 54 18 17 41 25 65 30l12 3c2 0 4 1 6 2 1 0 1 1 1 2-2 1-27-1-31-1H526c3 12 6 26 12 37 2 6 6 10 9 16h-1l1 1c-1 1-2 2-3 2s-2 1-3 0v-1l-21-5-1-2c-1 1-2 1-3 1l-2 2c-3 0-5 1-8 1l-1 1h-1-9-6-1z" class="H"></path><path d="M495 860c4-1 8-3 13-4 2 0 6 0 8-1h4-1c-1 1-2 1-3 1l-2 2c-3 0-5 1-8 1l-1 1h-1-9z" class="F"></path><path d="M520 855c3 0 7 1 11 2 5 1 10 3 15 3l1 1c-1 1-2 2-3 2s-2 1-3 0v-1l-21-5-1-2h1z" class="O"></path></svg>