<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="64 16 604 692"><!--oldViewBox="0 0 728 752"--><style>.B{fill:#575757}.C{fill:#353435}.D{fill:#272627}.E{fill:#3e3e3e}.F{fill:#848484}.G{fill:#676666}.H{fill:#989798}.I{fill:#7d7c7d}.J{fill:#151515}.K{fill:#a4a3a3}.L{fill:#8c8b8b}.M{fill:#2c2b2c}.N{fill:#444344}.O{fill:#605f60}.P{fill:#b6b5b6}.Q{fill:#c8c7c7}.R{fill:#484748}.S{fill:#1f1e1f}.T{fill:#303030}.U{fill:#c1c0c0}.V{fill:#4d4c4d}.W{fill:#cfcecf}.X{fill:#9e9d9e}.Y{fill:#515051}.Z{fill:#3a393a}.a{fill:#939293}.b{fill:#727172}.c{fill:#777}.d{fill:#aaa8a9}.e{fill:#1b1b1b}.f{fill:#aeadad}.g{fill:#050404}.h{fill:#6d6c6d}.i{fill:#bcbbbb}.j{fill:#0d0d0d}.k{fill:#232223}.l{fill:#d6d5d6}.m{fill:#b1b0b0}.n{fill:#fff}.o{fill:#8f8e8f}.p{fill:#e0dfdf}.q{fill:#dad9d9}.r{fill:#e6e6e6}.s{fill:#eeeded}</style><path d="M458 605l2 4h-2l-1-3 1-1z" class="C"></path><path d="M530 188l3 3v1l-3-1v-3z" class="B"></path><path d="M583 241c1 0 2-1 3-2h1v1h2l1 1h-7z" class="G"></path><path d="M305 626c2-2 3-4 4-6l1 1h0l-1 1c-1 1 0 1 0 2h1c-2 1-3 2-5 2z" class="W"></path><path d="M367 675l2-2c1 2 0 3 0 5h0c-1 0-1 0-2-1v-1-1z" class="R"></path><path d="M412 82c1-2 2-2 4-2v3l-3 1-1-2z" class="X"></path><path d="M142 238h2c1 0 2 0 4-1h0c-1 2-3 4-5 4h0c-1-1-1-2-1-3h0z" class="P"></path><path d="M140 237c1 0 1 1 2 1h0c0 1 0 2 1 3h0-3v-2l-1-1h0l1-1z" class="I"></path><path d="M198 130c-1-2-1-4 1-6h0c0 1 0 2 1 3h0c0 1-1 2-1 3h-1z" class="G"></path><path d="M423 82c2-1 3-1 5 0 1 1 2 1 2 2l-2 1c-1-1-2-1-2-2h-4l1-1z" class="M"></path><path d="M584 336c1-1 1-2 2-3l1 1c-1 1-1 1-1 2 1 1 2 3 3 4h-1c-2-1-3-3-4-4z" class="R"></path><path d="M332 78l1-2c1-1 3-2 4-4 0 2-1 4-2 5s-1 2-2 2-1 0-2-1h1z" class="M"></path><path d="M325 79c1-1 1-2 2-3 2 0 4 1 5 2h-1c-1 1-4 1-6 1z" class="q"></path><path d="M126 235h1c1 0 0 0 1 1l1 1h-1l-1 2c0 1 0 1 1 2-1-1-2-1-3-2 0-2 0-2 1-4z" class="V"></path><path d="M198 130h1c1 1 1 2 2 3v3h-1c-1-1-1-1-2-3v-3z" class="E"></path><path d="M240 594l4-2-2 5h0l-2 1-2 2 2-6z" class="H"></path><path d="M453 599c2 2 4 4 5 6l-1 1c-2-2-3-4-6-5l1-1 1-1z" class="M"></path><path d="M244 588h4l-4 4-4 2c1-2 2-4 4-6z" class="P"></path><path d="M387 73l5 5v2c-1 0-3 0-4-1v-2c0-1-1-2-1-3v-1z" class="C"></path><path d="M144 223c2 0 3 2 4 3s3 2 4 3c-1 1-1 1-2 1-3-2-4-4-6-7z" class="B"></path><path d="M178 163c1-5 2-9 6-12h0c-3 4-3 6-3 11l-1-1v1c-1 0-1 1-2 1z" class="c"></path><path d="M547 186h1c-2 2-6 6-5 8v1h-1 0v-1c-1 0-1 1-2 2-1-1-1-1-2-1v-1h0c2 0 8-6 9-8z" class="H"></path><path d="M416 80c1-1 2-1 3-2v-3h0c1 1 2 2 2 4 0 1 0 1-1 2s-2 2-3 2h-1v-3z" class="B"></path><path d="M369 58l1 2h1c0 1 0 1 1 1 0 3 2 5 3 7h-2l-5-9 1-1z" class="S"></path><path d="M381 644c0 2 1 2 3 3l2 1-4 4c-1 0-1 0-1-1h-1l1-1c0-1-1-2-1-3 1-1 1-1 1-3z" class="N"></path><path d="M381 644c0 2 1 2 3 3-2 1-2 1-2 2v2h-1-1l1-1c0-1-1-2-1-3 1-1 1-1 1-3z" class="Z"></path><path d="M564 173c3 2 6 4 8 7v2l-2 1c-1-4-3-6-6-9v-1z" class="H"></path><path d="M373 68h2c2 2 3 5 5 7-1 1 0 1-1 2l-5-6-1-3z" class="h"></path><path d="M166 351l1-1 2 2c1 0 0 0 1 1 1 2 2 5 1 7 0 1-1 1-1 2-1 0-1-1-1-1v-6l-1-1c0-1 0-2-1-3h-1z" class="a"></path><path d="M184 463c1 1 1 1 2 1l-1 3c-4 0-8 1-12 0h1c3 0 6-1 8-3l2-1z" class="C"></path><path d="M591 229c0 2 0 4-1 6h0c-1 2-3 3-4 4s-2 2-3 2c-2 0-4-1-6-2h1c2 0 4 0 6-1h0c3-1 5-3 6-6l1-3z" class="B"></path><path d="M200 127c2 3 4 4 7 5-1 1-2 1-3 2-1 0-2 0-3-1s-1-2-2-3c0-1 1-2 1-3z" class="F"></path><path d="M135 226h1c0-1 1-2 1-3h0c0 5 1 9 3 14l-1 1c-2-2-4-5-4-7 0-1 0-1 1-2 0-1-1-2-1-3z" class="O"></path><path d="M158 432c4 1 7 1 11 1v1h1v1h-6c-2 0-5-1-8-1v-1-1h2z" class="D"></path><path d="M169 434h1v1h-6 0l1-1h4z" class="j"></path><path d="M440 596c5 0 9 1 13 3l-1 1-1 1c-3-3-7-3-11-3-1-1-1-1-2-1h-1 0c1 0 2 0 2-1h1z" class="k"></path><path d="M266 619c-2-3-2-7-2-11l1 1h1c0 3 0 5 1 8l3 2h-2l-1-1-1 1z" class="G"></path><path d="M238 600l2-2 2-1-1 3-2 8c0 1 0 2-1 3v6c-1-3-1-9 0-12-1-2 0-4 0-5z" class="F"></path><path d="M238 600l2-2 2-1-1 3-3 3v2c-1-2 0-4 0-5z" class="o"></path><path d="M306 605l2 2c2 2 3 5 3 8v3l-1 3-1-1h0c1-4-1-8-3-11 1-1 1-2 0-4z" class="G"></path><path d="M127 239l1-2h1 1c1 0 3 1 4 1l1 1v2h-7c-1-1-1-1-1-2z" class="C"></path><path d="M134 238l1 1v2h-7c-1-1-1-1-1-2h2c2 0 2 1 3 2 1 0 1-1 2-1v-1-1z" class="E"></path><path d="M522 128h1c1-1 1-2 1-4v-1c1 1 1 1 1 2 2 2 0 7 0 10l-1 2h0-1v-1c0-3 0-5-1-8z" class="G"></path><path d="M173 354h2c1 1 1 3 3 3h0l1-1 3 5h-3c-1 0-1 1-2 2h-1c1-3-1-6-3-9z" class="Z"></path><path d="M198 133c1 2 1 2 2 3v2 2c-1 1-3 3-3 5v1h0c-5 1-9 1-13 5h0c3-3 6-5 10-6h2c0-4 2-8 2-12z" class="I"></path><path d="M543 462c5 2 9 2 14 1 1-1 2-2 2-3h0c-1 4-3 6-7 7v-1c-1 0-3-1-4-1-2 0-3-1-5-1l-1-1 1-1zM266 619l1-1 1 1h2 1v1c3 0 4 0 6-1 0 1-1 1-2 2h0c-2 1-6-1-7 2-1 1-1 3 0 4 1 2 2 3 3 5-1 0-3-3-4-4v-2c-1-2-1-4-1-7z" class="Y"></path><path d="M266 619l1-1 1 1h2 1l-3 1s-1 0-1 1v5c-1-2-1-4-1-7z" class="O"></path><path d="M409 623c0-1 1-3 2-5v2c1-1 1-1 2-1v1s1 1 1 2h1 0l1 1c1 2 3 3 4 4h-4v-1h0c-2-2-4-3-7-3z" class="K"></path><path d="M449 571v2h1 0c-4 3-12 9-18 8l-4-1h3c1 0 2-1 4 0h2c2-1 3-3 5-4l6-3s1-1 1-2z" class="C"></path><path d="M329 625c2 1 3 4 5 6 0 1 1 0 1 1 2 2 3 4 4 7l1 2v2h0l-11-18z" class="R"></path><path d="M525 135c0 4 1 7 3 10h2c4 1 7 3 9 6h0c-4-4-9-4-14-6h1c0-1-1-2-2-3v-1c-1-2-2-3-4-3l-1-1c2-1 2 0 4 0h1 0l1-2z" class="I"></path><path d="M360 43l1 1c0 5-2 9-4 13-1 0-1 0-1 1l-1-1c1-5 3-10 5-14z" class="E"></path><path d="M533 191l1 1c1 1 2 1 4 2v1c1 0 1 0 2 1l1 1-1 1c-2 1-2 0-3 0h-1 0-1l-3-3c0-1-1-2-2-4l3 1v-1z" class="G"></path><path d="M534 192c1 1 2 1 4 2v1h-3v1c-1 0-1-1-2-2 0-1 0-1 1-2z" class="L"></path><path d="M538 195c1 0 1 0 2 1l1 1-1 1c-2 1-2 0-3 0h-1l-1-2v-1h3z" class="U"></path><path d="M589 245c3 0 11-1 14 1l19 17v1l-11-10c-2-2-5-5-8-7l-5 3c0-1-1-2 0-3h1c0-1 1-1 1-2h-11z" class="F"></path><path d="M116 250h0l6-5c5 0 11 0 16 1h-4l1 1h-9c-1 0-3-1-4-1l-1 1-4 5-1-2zm480-14h2c0 1-1 3-2 4s-4 1-6 1l-1-1h-2v-1h1c1-1 2-2 3-2s3 0 5-1z" class="Y"></path><path d="M596 236v1c-2 2-4 3-6 3-1-1-1-1-2-1 1-1 2-2 3-2s3 0 5-1z" class="M"></path><path d="M187 428l1 1c-5 4-11 6-18 6v-1h-1v-1c3 0 6 0 9-1 2 0 3-1 5-2 1 0 2-1 4-2z" class="S"></path><path d="M131 231l2-1h0 0c1 3 2 5 3 7l3 1 1 1v2h-3-2v-2l-1-1c-1 0-3-1-4-1l4-1v-1c-1-1-2-3-3-4z" class="F"></path><path d="M136 237l3 1 1 1v2h-3c1 0 1-1 2-1-2-1-3-2-3-3z" class="c"></path><path d="M572 182c1 2 1 3 1 5 0 1-1 4-1 5 1 2 4 6 6 8 0 0-1 0-1 1-1-2-3-3-5-5h1c0-1-2-4-2-5-1-2 0-4 0-6l-1-2 2-1z" class="P"></path><path d="M144 210c0-1 1-1 2-1-1 2 0 4 0 7-1 1-1 3 0 4v1c1 2 2 3 3 4h0l-1 1c-1-1-2-3-4-3h0c-1-4-2-9 0-13z" class="f"></path><path d="M195 405c1 1 1 3 2 4h-1c-1 1 0 2 0 4-1 1-1 3-2 4-1 5-3 8-6 12l-1-1c0-1 1-3 3-3 1-1 1-2 1-3 1-2 2-4 2-5 1-2 0-5 1-7 1-1 1-2 0-4 0-1 1-1 1-1z" class="Y"></path><path d="M528 187l2 1v3c1 2 2 3 2 4l3 3h-1l1 1c0 1-1 2-1 3 0-1-1-1-2-2 1 1 2 2 1 3l-1-1v2h-1v-1-2s-1-1-1-2c0-4-1-8-2-12z" class="d"></path><path d="M532 195l3 3h-1l1 1c0 1-1 2-1 3 0-1-1-1-2-2v-5z" class="a"></path><path d="M128 354h0v1 1c1 1 1 1 1 2l1-1v-3h-1c1-1 2-2 3-2 2 0 2 0 3 1v3c-1-1-1-2-3-2-1 2 0 6-1 8l-2-4c-3-1-6-4-10-4h-1 0-1 0c2-1 4-1 6 0 1 0 3 2 4 1l1-1z" class="H"></path><path d="M420 597c2 0 3-2 5-3h0c0 1 0 1 1 1v1l-15 12h0c1-2 1-2 3-3 0-1 1-1 1-1l5-4c-1 0-1 1-2 1h-1-1c-2 2-4 5-6 5h0c3-3 7-6 10-9z" class="R"></path><path d="M137 384c-3-4-5-7-6-11h0c4 6 7 10 13 12h1v1l-1 1-1 1h-1s-1-1-2-1l-1 1-2-4z" class="O"></path><path d="M137 384c2 0 5 3 7 2v-1h1v1l-1 1-1 1h-1s-1-1-2-1l-1 1-2-4z" class="F"></path><path d="M364 50c1 3 2 6 4 9l5 9 1 3-2-1-2-3c-1-1-2-3-3-4-1-3-3-5-3-8v-5z" class="K"></path><path d="M409 623c3 0 5 1 7 3h0c-2 0-4 0-7 1l-7 6h-1l1-2 7-8z" class="D"></path><defs><linearGradient id="A" x1="550.765" y1="359.531" x2="544.356" y2="365.297" xlink:href="#B"><stop offset="0" stop-color="#605d5e"></stop><stop offset="1" stop-color="#7c7d7d"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M545 359c0-1 0-1 1-1 0-1 2-4 3-4 1-1 2-1 2-1-2 3-4 8-3 12 0 4 2 7 5 10h1l-1 1-3-3c-2-2-3-4-4-6v-8h-1z"></path><path d="M587 230h0c0-1 0-2 1-3v1h3v1l-1 3c-1 3-3 5-6 6l-1-1c0-2 2-4 2-6 1 0 2 0 2-1z" class="i"></path><path d="M587 230h0c0-1 0-2 1-3v1h3v1l-1 3v-1h-1v-1h-1-1z" class="Q"></path><path d="M588 230v1c0 2-3 4-5 6h0c0-2 2-4 2-6 1 0 2 0 2-1h1z" class="B"></path><path d="M264 608c1-2 1-3 3-5 5-5 10-7 17-7h0c1 0 2 1 3 1h0-2c-1 0-2 0-3 1-6 1-11 2-14 7-1 1-2 3-2 4h-1l-1-1z" class="T"></path><path d="M139 388l1-1c1 0 2 1 2 1v1c0 1 0 3 1 4h0l-2 4-2 4h-1l-2-2c1-3 3-7 3-11z" class="b"></path><path d="M129 348c2-4 5-8 8-11l-1-5c1 1 2 3 3 5l3-3c1 1 2 3 3 3l-2 2-7 1c-2 3-4 5-6 8v1c-1 0-1 0-1-1z" class="L"></path><defs><linearGradient id="C" x1="585.869" y1="384.429" x2="579.89" y2="381.617" xlink:href="#B"><stop offset="0" stop-color="#6a6a6b"></stop><stop offset="1" stop-color="#868483"></stop></linearGradient></defs><path fill="url(#C)" d="M576 385c6-1 11-4 14-8 1-2 3-5 4-7v-3h1l-1 3c-1 5-5 12-9 16h-1c-1 0-2 1-3 2h-1c-1-1-2-1-3-1 0-1-1-1-1-2z"></path><path d="M360 43l2-8c0 5 2 8 3 12 2 4 3 8 4 11l-1 1c-2-3-3-6-4-9-1-1-1-5-3-6l-1-1z" class="R"></path><path d="M125 228v-1c1 0 2 0 2 1v1h0c1 1 2 2 4 2 1 1 2 3 3 4v1l-4 1h-1l-1-1c-1-1 0-1-1-1h-1l-1-1c-1-2 0-4 0-6z" class="a"></path><path d="M127 229c1 1 2 2 4 2 1 1 2 3 3 4l-1 1c-1-1-2-1-3-2 0 0-1-1-2-1v-1c-1-1-1-1-1-3z" class="P"></path><path d="M173 467h-1c-4 0-7-4-9-7l3 2c2 2 8 2 11 1h1v1l3-1h1c1 0 1-1 2-1v1c-1 0-1 1-2 1-2 2-5 3-8 3h-1z" class="Y"></path><path d="M181 463h1c1 0 1-1 2-1v1c-1 0-1 1-2 1-2 2-5 3-8 3-2-1-3-1-4-2h0c3 0 6 0 8-1h0l3-1z" class="U"></path><path d="M590 411l1 1c3 1 5 2 8 1 5-1 9-5 13-8v1c-3 5-11 10-17 11-3 0-7 0-9-1 0-1 1-1 2-2 4 2 6 2 10 1h-3c-2-1-3-2-5-2v-2z" class="F"></path><path d="M590 235v1c1 0 1 0 1-1 1-2 3-3 4-5 0-1 1-3 2-4h1c0 2 1 4 1 6-1 1-1 2-1 4h-2c-2 1-4 1-5 1s-2 1-3 2h-1-1c1-1 3-2 4-4z" class="a"></path><path d="M591 237h0c2-1 4-4 5-6h0l1-1 2 2c-1 1-1 2-1 4h-2c-2 1-4 1-5 1z" class="b"></path><path d="M402 631l-1 2h1l-15 13v-1h0l-1 1v-1-1l13-11c2-1 2-1 3-2z" class="C"></path><defs><linearGradient id="D" x1="480.787" y1="603.073" x2="484.006" y2="592.857" xlink:href="#B"><stop offset="0" stop-color="#8a8b8f"></stop><stop offset="1" stop-color="#b5b3b2"></stop></linearGradient></defs><path fill="url(#D)" d="M477 590c2 1 3 1 5 2 4 6 6 15 4 22 0-2-1-4-1-6l-1-2h-1l-1-2 1-1v-3h0c-1-4-3-7-6-10z"></path><path d="M483 600c0 2 1 4 1 6h-1l-1-2 1-1v-3h0z" class="C"></path><path d="M574 242h0c1 1 2 2 4 3s5 0 8 0l-3 1 1 2c-1 1-2 1-3 1h-1l-2 1c-1 1-2 1-3 3h0l-2 1-1-1c2-2 2-4 3-7 0-1-1-3-1-4z" class="l"></path><path d="M575 246v2h2 0c1 1 1 1 2 1h2-1l-2 1c-1 1-2 1-3 3h0l-2 1-1-1c2-2 2-4 3-7z" class="i"></path><defs><linearGradient id="E" x1="545.707" y1="436.071" x2="540.755" y2="426.739" xlink:href="#B"><stop offset="0" stop-color="#171618"></stop><stop offset="1" stop-color="#393834"></stop></linearGradient></defs><path fill="url(#E)" d="M533 426h1 0c1 1 2 1 3 2 2 1 6 4 9 4l1-1c1 0 1 0 2-1 1 1 1 1 2 1l-1 2h3v1h2l-1 1h0 0-4c-7-1-13-3-17-9z"></path><path d="M550 435v-1h3 2l-1 1h0 0-4z" class="j"></path><path d="M549 430c1 1 1 1 2 1l-1 2-4-1 1-1c1 0 1 0 2-1z" class="m"></path><defs><linearGradient id="F" x1="566.409" y1="374.789" x2="566.795" y2="369.387" xlink:href="#B"><stop offset="0" stop-color="#5d5b5c"></stop><stop offset="1" stop-color="#7a797a"></stop></linearGradient></defs><path fill="url(#F)" d="M577 375h-2c-3 1-6 1-9 1-6-2-10-5-13-10 4 2 8 5 13 7 1 1 3 1 5 1l2-1h4v1h0v1h0z"></path><path d="M510 129c2 1 4 3 6 3 3 0 5-2 6-4 1 3 1 5 1 8v1c-2 0-2-1-4 0-1 0-1-1-2-1h-1l-3-3-1-1-1-1c0-1-1-2-1-2z" class="I"></path><path d="M517 136c2-1 4-1 5-1l1 1v1c-2 0-2-1-4 0-1 0-1-1-2-1z" class="K"></path><path d="M460 609c0 4 0 6-1 10-1 1-1 2-2 3 0 2 1 3 0 5 0 1-2 2-2 3l-2 2h0c2-3 3-5 3-8 0-1 0-1-1-2-2-1-6-1-8-1 1-1 1-1 2-1 3 0 5-1 7-3s2-4 2-7v-1h2z" class="B"></path><defs><linearGradient id="G" x1="295.793" y1="595.518" x2="299.77" y2="609.933" xlink:href="#B"><stop offset="0" stop-color="#3a3937"></stop><stop offset="1" stop-color="#645e61"></stop></linearGradient></defs><path fill="url(#G)" d="M284 596c9 0 16 4 22 9 1 2 1 3 0 4l-2-2c-6-7-14-9-22-9 1-1 2-1 3-1h2 0c-1 0-2-1-3-1h0z"></path><path d="M136 403v-4l2 2h1c-2 5-3 9-8 12 0 0-1 0-1 1h-2c-1-1-1-1-2-1v1c-1 0-2 0-3 1h-1c-2-1-4-3-6-4-1-1-2-2-4-3l-2-2v-1c2 2 3 3 5 4s4 3 7 4 6 0 8-1c4-2 5-6 6-9z" class="o"></path><path d="M136 403v-4l2 2-1 1c0 1 0 1-1 0v1z" class="H"></path><path d="M547 186l1-2c0-1 1-3 1-5h-1 1c1 2 0 8 2 9l3-1v1l2 1h-1c-3 1-6 3-9 5-1 0-2 1-3 1v-1c-1-2 3-6 5-8h-1z" class="l"></path><path d="M551 188l3-1v1c-1 1-2 1-4 1v-1h1z" class="Q"></path><defs><linearGradient id="H" x1="156.228" y1="377.636" x2="162.066" y2="366.286" xlink:href="#B"><stop offset="0" stop-color="#656566"></stop><stop offset="1" stop-color="#807c7d"></stop></linearGradient></defs><path fill="url(#H)" d="M160 371c3-1 6-2 8-4 1-1 2-3 3-4-2 5-4 9-9 11-1 1-2 1-3 1l-1 1h-11c1 0 2-1 3 0h0v-1h-1c-1-1-1-1-2-1h0l1-1h1v1c4 1 8 0 11-3z"></path><path d="M125 228c0-2 0-3 1-5 1-6 4-12 9-16 2-2 5-3 8-5v2l-6 3-4 4c-1 1-1 2-2 3v1c-1 1-1 2-1 3h0c0 1 0 1-1 2 0 0 1 0 0 1s-1 4-1 5l-1 3v-1c0-1-1-1-2-1v1z" class="P"></path><path d="M399 633c2-4 5-7 7-10 1-4 2-8 5-11 1-2 3-3 5-4l-5 10c-1 2-2 4-2 5l-7 8c-1 1-1 1-3 2z" class="i"></path><path d="M244 588h0-2-3-1-1l-1-1c-1 0-1 0-2-1l1-1c0 1 1 1 1 1h1c1 1 3 1 4 1l1-1c3 0 7-4 9-7v-1-1h1c1 2 2 3 3 4h0 0l-7 7h-4z" class="l"></path><path d="M354 670c0 2 1 2 2 3v1c3 3 3 6 5 9h0 1c-1 1-1 1-1 2v1 9 1 1c-3-8-5-18-9-26l2-1z" class="N"></path><path d="M545 359h1v8c1 2 2 4 4 6l-1 1s1 1 1 2h-1 0c-1-2-2-3-4-4-1-2-3-2-4-3l-2-2v-1l1-2 2-2h1c1-1 2-2 2-3z" class="U"></path><path d="M543 364c0 1-1 2 0 3 0 2 2 2 2 5-1-2-3-2-4-3l-2-2v-1l3-1c0-1 0-1 1-1z" class="d"></path><path d="M545 359h1v8c-1-2-1-3-2-4l-1 1c-1 0-1 0-1 1l-3 1 1-2 2-2h1c1-1 2-2 2-3z" class="H"></path><path d="M590 356l1 1 2 4c-2 5-5 10-10 12-2 1-4 2-6 2h0v-1h0v-1c1 0 2 0 3-1 2-1 3-2 5-3 2-4 5-8 5-13z" class="B"></path><path d="M549 177c0 1 0 1-1 1-1-1-1-2-2-3v-1c0 1 1 1 1 2 1-2 3-3 5-3 3-1 9-1 12 0v1c3 3 5 5 6 9l1 2-1-2-3-3-3 4v-2c0-1 1-2 1-3-1-1-2-3-4-4h0c-2-1-4-1-6-1-1 0-6 2-7 3h1z" class="Q"></path><path d="M152 385h0v1c-1 2-1 3-3 4-2 3-4 7-7 8l-1-1 2-4h0c-1-1-1-3-1-4v-1h1l1-1 1-1v-1h7z" class="W"></path><path d="M143 393h0c-1-1-1-3-1-4v-1h1l2 2c0 1-1 2-2 3z" class="R"></path><path d="M152 385h0v1c-3 1-5 1-8 1l1-1v-1h7z" class="L"></path><path d="M270 80l1 1c1 0 2 1 3 1 1 1 2 2 4 2 0-1 0-1-1-2l1-1c2 1 3 3 5 3h0 2c0-1-1-2-1-2l1-1c0 1 1 1 1 1 1 1 2 1 2 1v1h-1c0 1 0 1 1 1 1 1 2 1 3 2l-1 1c-1-1-2-2-4-2v1h1l1 1h1c1 1 2 3 3 4 0 1 1 1 1 2h-1l-1-1c0-1-1-1-1-2l-3-3h-1c-1-1-1-1-2-1v2h0l-1-1-5-2c-1-1-2-1-2-1l-3-2h-2l-1-1c-1 0-1 0-2-1h1l1-1z" class="m"></path><defs><linearGradient id="I" x1="310.274" y1="609.562" x2="312.766" y2="602.047" xlink:href="#B"><stop offset="0" stop-color="#29292e"></stop><stop offset="1" stop-color="#55514f"></stop></linearGradient></defs><path fill="url(#I)" d="M298 594h3l11 10c2 1 4 5 6 5l1 1c0 1 0 2 1 3h1c-1 1-1 1-2 1l3 2h0c-2 0-4-1-5-3-2-1-3-4-5-5-2-3-5-5-8-8l1-1c-1 0-2-2-4-3-1 0-2-1-3-2z"></path><defs><linearGradient id="J" x1="360.527" y1="686.197" x2="365.086" y2="687.74" xlink:href="#B"><stop offset="0" stop-color="#212021"></stop><stop offset="1" stop-color="#444445"></stop></linearGradient></defs><path fill="url(#J)" d="M367 677c1 1 1 1 2 1l-7 24c0-2-1-3-1-5v-1-1-9-1c0-1 0-1 1-2 2-1 3-3 5-6z"></path><path d="M545 169v1c1 0 2-2 4-2-4 3-8 7-13 10-2 1-6 2-10 2-1-1-2-1-2-1h0l-2-2h1 2c3 0 6 0 8-2 1 0 2 0 3-1h0c2-1 2-1 3-3l1-1c1-1 2-1 3-2h1l1 1z" class="Z"></path><path d="M449 571h0c0 1-1 2-1 2l-6 3c-2 1-3 3-5 4h-2c-2-1-3 0-4 0h-3c-1 0-1 1-2 1h0 0v-1c1 0 1 0 2-1v-2h0-1c0-1-2-2-3-3h4c1 1 1 1 2 1l3 1c2 1 6-1 8-3 2 0 3-1 4-2h4z" class="D"></path><path d="M228 520c3 2 5 5 9 6l1-1-1-1s1 0 1-1c2 1 6 2 8 1h1l1 1v2c-2 1-5 2-8 2-6 0-12-4-15-8h0 0c0-1 1-1 2-1h0 1z" class="B"></path><path d="M238 523c2 1 6 2 8 1h1l1 1c-3 2-7 2-10 1h-1 0l1-1-1-1s1 0 1-1z" class="K"></path><path d="M468 579h0 2l2-2h1-1c0 2 0 2 1 4 2 2 6 5 9 6 1 0 3 0 4-1h1s1 0 1-1h1l4-4v1h0c-1 0-1 1-1 1l-3 3c-2 1-5 3-8 3 0 1 0 1 1 2v1c-2-1-3-1-5-2l-1-1-7-7s-1-2-1-3z" class="l"></path><path d="M476 589c2 0 3 0 4 1l2 1v1c-2-1-3-1-5-2l-1-1z" class="i"></path><path d="M586 205c6 4 9 10 11 17 1 1 1 3 1 4h-1c-1 1-2 3-2 4-1 2-3 3-4 5 0 1 0 1-1 1v-1h0c1-2 1-4 1-6v-1l1 1v2 1l1-2h0c1-2 1-5 1-8 0-1-1-3-2-5-2-4-4-7-7-11l1-1z" class="U"></path><defs><linearGradient id="K" x1="436" y1="607.119" x2="413.471" y2="606.807" xlink:href="#B"><stop offset="0" stop-color="#2d2c28"></stop><stop offset="1" stop-color="#4e4e50"></stop></linearGradient></defs><path fill="url(#K)" d="M416 608l3-4c5-5 13-8 21-8h-1c0 1-1 1-2 1h0 1c1 0 1 0 2 1-9 1-17 4-22 11-3 3-4 7-4 11l1 2h-1c0-1-1-2-1-2v-1c-1 0-1 0-2 1v-2l5-10z"></path><path d="M129 348c0 1 0 1 1 1v-1c2-3 4-5 6-8l7-1c-3 4-5 8-7 12l-1 2c-1-1-1-1-3-1-1 0-2 1-3 2h1v3l-1 1c0-1 0-1-1-2v-1-1h0c0-2 0-4 1-6z" class="d"></path><path d="M478 524v1c2-1 4 0 5-1 3 0 5-2 6-4 1 1 2 1 3 1 1 1 1 1 1 2l-1 1h3v1c-4 3-10 5-14 4-2 0-4-1-5-2 0 0 0-1 1-2l1-1z" class="O"></path><defs><linearGradient id="L" x1="486.8" y1="519.227" x2="483.58" y2="527.42" xlink:href="#B"><stop offset="0" stop-color="#a3a2a2"></stop><stop offset="1" stop-color="#c7c7c8"></stop></linearGradient></defs><path fill="url(#L)" d="M478 524v1c2-1 4 0 5-1 3 0 5-2 6-4 1 1 2 1 3 1 1 1 1 1 1 2l-1 1-2 1c-4 2-8 2-13 1v-1l1-1z"></path><path d="M131 362c1-2 0-6 1-8 2 0 2 1 3 2 0 5 1 10 5 14 2 2 4 3 6 3l1 1h0c1 0 1 0 2 1h1v1h0c-1-1-2 0-3 0-2 0-4-1-6-2-5-2-8-6-10-12z" class="O"></path><path d="M315 80c1-1 3 0 4-1 0-1-1-1-3-2h0 1c2 0 4 2 5 3-1 1-2 0-2 1v1c1 1 3 4 3 6 1 1 0 2 0 3l-1 2h-3-1l-1-5c0-2-3-4-5-5 0 0 0-1-2-2h3c1 0 1-1 2-1h0z" class="f"></path><path d="M317 81l1 1 1 1 1 1h0l-1 1v-1c-1 0-1 0-2-1h-1c1-1 1-1 1-2z" class="m"></path><path d="M310 81h3c1 0 1-1 2-1h0l1 1h1c0 1 0 1-1 2-1-1-1-1-2-1l-1 1h-1s0-1-2-2zm13 7c1 1 0 2 0 3l-1 2h-3v-5 3h1c2-1 1-1 2-3l1 1v-1z" class="U"></path><path d="M313 83c2 1 5 1 6 3v2 5h-1l-1-5c0-2-3-4-5-5h1z" class="W"></path><path d="M308 607c2 2 5 4 6 6 3 4 4 9 7 13 2 3 5 6 6 8-4-1-9-8-12-12l-4 2h-1-1c0-1-1-1 0-2l1-1h0l1-3v-3c0-3-1-6-3-8z" class="i"></path><path d="M311 615c1 1 1 1 2 3v1l-1 1c0-1-1-1-1-2v-3z" class="Q"></path><path d="M311 618c0 1 1 1 1 2l-1 4h-1-1c0-1-1-1 0-2l1-1h0l1-3z" class="l"></path><path d="M402 79c2-1 3-2 5-2h-1c0 1-1 1-1 2s2 1 3 2l2-1 1 1-1 1 1 1c0-1 1-1 1-1l1 2c-2 0-4 2-6 3-1 1-2 2-2 3s1 1 2 2h0l1 1h-4c-2 0-2-1-3-2v-1l-1-2-1-2c1-1 2-3 3-4v-1h0v-1-1z" class="G"></path><path d="M402 80v-1c2 1 2 1 3 2-2 3-4 5-5 7l-1-2c1-1 2-3 3-4v-1h0v-1zm6 1l2-1 1 1-1 1 1 1c0-1 1-1 1-1l1 2c-2 0-4 2-6 3-1 1-2 2-2 3s1 1 2 2h0l1 1h-4c-2 0-2-1-3-2v-1c1-1 3-5 4-6 2-1 4-2 5-3h-1-1z" class="f"></path><path d="M467 581v2c0 2 1 3 1 4h-1c-12-5-22-3-33 1l-9 6c-2 1-3 3-5 3 1-2 3-3 4-5 8-6 19-10 29-10 3 0 7 0 10 1 1 0 3 0 4-1v-1z" class="m"></path><defs><linearGradient id="M" x1="583.891" y1="246.933" x2="596.204" y2="251.361" xlink:href="#B"><stop offset="0" stop-color="#383738"></stop><stop offset="1" stop-color="#717171"></stop></linearGradient></defs><path fill="url(#M)" d="M589 245h11c0 1-1 1-1 2h-1c-1 1 0 2 0 3-3 2-7 4-9 6h-2c0-1-1-3-1-4 0-2-1-3-2-4l-1-2 3-1h3z"></path><path d="M200 449l1 1v1c0 1-1 4 0 6h1c-4 5-10 8-17 10l1-3c-1 0-1 0-2-1l5-3 5-5h1c0-1 1-1 2-1l3-5z" class="N"></path><path d="M184 463l5-3h2c-1 1-3 3-5 4-1 0-1 0-2-1z" class="S"></path><path d="M194 455h1c0-1 1-1 2-1l-6 6h-2l5-5z" class="j"></path><defs><linearGradient id="N" x1="533.279" y1="456.854" x2="531.437" y2="464.51" xlink:href="#B"><stop offset="0" stop-color="#444344"></stop><stop offset="1" stop-color="#5c5b5c"></stop></linearGradient></defs><path fill="url(#N)" d="M520 457c1-1 1-2 2-3h1 3c2 3 5 8 10 9h0 2c1 0 2 1 3 2 1-1 2-1 2-1 2 0 3 1 5 1 1 0 3 1 4 1v1h0c-2 1-5 1-7 0-7 0-12-1-18-5-2-1-4-4-6-5h-1z"></path><path d="M543 464c2 0 3 1 5 1 1 0 3 1 4 1-4 1-7 0-11-1 1-1 2-1 2-1z" class="U"></path><defs><linearGradient id="O" x1="564.223" y1="424.101" x2="586.986" y2="415.705" xlink:href="#B"><stop offset="0" stop-color="#3c3a3c"></stop><stop offset="1" stop-color="#696868"></stop></linearGradient></defs><path fill="url(#O)" d="M580 408c1 0 2 0 3 1 2 2 3 4 5 5-1 1-2 1-2 2-2 2-3 3-4 5-4 6-8 9-14 12l-1-2c1-1 3-2 5-3 4-3 7-8 7-14 1-2 1-4 1-6z"></path><path d="M361 44c2 1 2 5 3 6v5c0 3 2 5 3 8 1 1 2 3 3 4l-1 1v1c0-1-1-1-2-1 0 1 1 1 1 2l1 2h-1l-1-1v-1c-1-1-1-1-1-2h-1v-2c-1-1-2-2-3-4 0-1 1 0 0-1l-1 1v1 2l-2 2v-2s0-1 1-1v-1c0-1 0-2-1-3v1h-1l-1 1v-2-1h1v-1h0v-1h0-1c2-4 4-8 4-13z" class="f"></path><path d="M367 68l-1-2c-1-1-1-1-1-2l-2-3v-1c0-1-1-2-1-3l-1-5c0-1 0-2 1-3 0 1 0 1 1 1 0 2 0 3 1 5 0 3 2 5 3 8 1 1 2 3 3 4l-1 1v1c0-1-1-1-2-1z" class="W"></path><path d="M295 85l-2-2h0c1-1 2-1 3-1 1-1 2-1 4 0 1 1 3 1 4 0l-1-1v-4-1c1 0 1 0 1-1h1v2c-1 0 0 1 0 2h1c0 1 0 1 1 1l3 1c2 1 2 2 2 2 2 1 5 3 5 5l1 5h-3c-2 0-2 1-4 3v-1c0-1 0-1 1-2h0c-1-1-2-3-3-5 0-1-1-2-2-2v-2h-1c-3 1-6-2-8-1-1 0-2 1-2 1l-1 1z" class="K"></path><path d="M307 84c2 0 3-1 4-1 2 1 3 2 4 4s2 4 1 6h-1c-2 0-2 1-4 3v-1c0-1 0-1 1-2h0c-1-1-2-3-3-5 0-1-1-2-2-2v-2z" class="p"></path><path d="M312 88c2 1 3 3 4 5h-1c-2 0-2 1-4 3v-1c0-1 0-1 1-2h0l1-1v-4h-1z" class="i"></path><path d="M311 83c2 1 3 2 4 4s2 4 1 6c-1-2-2-4-4-5 0-1 0-1-1-1-1-1-1-2-2-3 1 0 1 0 2-1z" class="r"></path><path d="M380 74v-2h0c1 0 2 1 3 2h0l2 2h0c1 0 1-1 1-1v-2h1v1c0 1 1 2 1 3v2c0 1 0 2 1 3h1c1 1 1 6 1 7-1 2-1 2-2 3h0v1h-1l-1-1h-1l-1-2-2-2 1-2-2-5-1-1-2-3c1-1 0-1 1-2v-1z" class="Q"></path><path d="M386 90l2-1 1 3v1h-1l-1-1-1-2z" class="H"></path><path d="M381 80c1 0 2 1 3 2s1 2 2 2c1 2 1 4 2 5l-2 1 1 2h-1l-1-2-2-2 1-2-2-5-1-1z" class="X"></path><path d="M384 86c1 1 2 3 2 4l1 2h-1l-1-2-2-2 1-2z" class="l"></path><path d="M380 74v-2h0c1 0 2 1 3 2h0l2 2 1 8c-1 0-1-1-2-2s-2-2-3-2l-2-3c1-1 0-1 1-2v-1z" class="C"></path><path d="M380 74l3 6 1 2c-1-1-2-2-3-2l-2-3c1-1 0-1 1-2v-1z" class="I"></path><path d="M383 74l2 2 1 8c-1 0-1-1-2-2l-1-2c1-2 0-4 0-6z" class="R"></path><defs><linearGradient id="P" x1="436.489" y1="582.906" x2="456.805" y2="601.575" xlink:href="#B"><stop offset="0" stop-color="#424142"></stop><stop offset="1" stop-color="#646463"></stop></linearGradient></defs><path fill="url(#P)" d="M425 594l9-6c11-4 21-6 33-1 1 1 4 4 4 5v1c-9-6-18-7-27-5-7 2-13 5-18 8v-1c-1 0-1 0-1-1h0z"></path><defs><linearGradient id="Q" x1="152.895" y1="419.502" x2="137.79" y2="422.895" xlink:href="#B"><stop offset="0" stop-color="#3c3b3a"></stop><stop offset="1" stop-color="#6f6e6f"></stop></linearGradient></defs><path fill="url(#Q)" d="M142 409c2 2 1 6 2 9 1 5 5 9 10 12l4 2h-2v1 1c-6-2-11-7-15-13 0-1-1-4-2-5h-1c0-1-3 0-4 1-3 0-7 0-9-1l-3-1h1c1-1 2-1 3-1v-1c1 0 1 0 2 1 0 0-1 0-1 1h-1 0c2 1 5 1 7 0 3-1 5-4 6-6h3z"></path><path d="M133 211c0 1-1 2-1 2-1 3-2 6-1 8 1 1 1 1 2 1v-2c1-3 1-5 3-7h0c0-2 2-3 3-3v1c1 0 1 0 2 1-2 3-3 6-4 11h0c0 1-1 2-1 3h-1c0 1 1 2 1 3-1 1-1 1-1 2 0 2 2 5 4 7h0l-3-1c-1-2-2-4-3-7h0 0l-2 1c-2 0-3-1-4-2h0l1-3c0-1 0-4 1-5s0-1 0-1c1-1 1-1 1-2h0c0-1 0-2 1-3v-1c1-1 1-2 2-3z" class="Q"></path><path d="M139 211c1 0 1 0 2 1-2 3-3 6-4 11h0c0 1-1 2-1 3h-1v-3h-2v-1h2c0-5 2-8 4-11z" class="V"></path><path d="M576 413v-1c0-2 0-5-1-7v-1l4 5v5c0 6-3 11-7 14-2 1-4 2-5 3l1 2c-1 0-3 1-4 1-3 1-7 2-10 1h0 0l1-1h-2v-1h-3l1-2 2 1h3c3 0 7-1 9-2s3-4 5-5h1c3-3 4-8 5-12z" class="l"></path><path d="M551 431l2 1c1 1 2 1 3 1h2 1-6-3l1-2z" class="U"></path><path d="M563 432l4-1 1 2c-1 0-3 1-4 1 0-1 0-1-1-2z" class="T"></path><path d="M559 433c1 0 3 0 4-1 1 1 1 1 1 2-3 1-7 2-10 1h0 0l1-1h-2v-1h6z" class="k"></path><path d="M467 581v-1l1-1c0 1 1 3 1 3l7 7 1 1c3 3 5 6 6 10h0v3l-1 1 1 2h1l1 2-1 1h-1c0 1-1 1-1 2-2-8-5-13-11-18v-1c0-1-3-4-4-5h1c0-1-1-2-1-4v-2z" class="F"></path><path d="M483 606h1l1 2-1 1h-1v-3z" class="E"></path><path d="M469 582l7 7 1 1c3 3 5 6 6 10h0v3l-1 1c-1-4-3-9-6-13-2-2-5-4-7-7v-2z" class="M"></path><path d="M584 248c1 1 2 2 2 4 0 1 1 3 1 4h2l-1 2c-1-1-1 0-3-1 1 2 1 3 1 5l1 3h0v2c-1 0-5-5-6-7-1-1-3-1-5-1-1-1-1-1-2 0-1 0-1-1-1-1l-3 2 1-2c-1-1-1-1 0-2h-1c0-1 0-3 1-3h1l1 1 2-1h0c1-2 2-2 3-3l2-1h1c1 0 2 0 3-1z" class="I"></path><path d="M580 249c2 1 3 2 4 4v1 1c-2-2-4-3-6-5l2-1z" class="N"></path><path d="M584 248c1 1 2 2 2 4 0 1 1 3 1 4h2l-1 2c-1-1-1 0-3-1 0-1 0-2-1-3v-1c-1-2-2-3-4-4h1c1 0 2 0 3-1z" class="Q"></path><path d="M571 253h1l1 1 2-1v2c2-1 4-1 6 1s3 4 5 6l1 3h0v2c-1 0-5-5-6-7-1-1-3-1-5-1-1-1-1-1-2 0-1 0-1-1-1-1l-3 2 1-2c-1-1-1-1 0-2h-1c0-1 0-3 1-3z" class="D"></path><path d="M573 258c1-1 2-2 3-2 2 1 4 3 5 4-1-1-3-1-5-1-1-1-1-1-2 0-1 0-1-1-1-1z" class="H"></path><path d="M571 253h1l1 1 2-1v2c-2 1-3 2-4 3-1-1-1-1 0-2h-1c0-1 0-3 1-3z" class="X"></path><path d="M296 84s1-1 2-1c2-1 5 2 8 1h1v2c1 0 2 1 2 2 1 2 2 4 3 5h0c-1 1-1 1-1 2v1c0 1-2 3-3 4l1 6-2-2c-1 0-1-1-1-2-1-1-1-2-2-3v-1c-1-3-2-5-3-7-1-1-2-4-3-5-1 0-2-1-2-2z" class="W"></path><path d="M304 99l2-1v-2c0-2 0-5-2-6h-1c-1 0-2-1-2-2l-3-3v-1l2 2 2 2h2 0 2s1 0 1 1h0-2c1 2 1 3 1 5v1h1v-2c1 0 0-1 1-1 1 1 1 3 1 5l-1 2v1l1 6-2-2c-1 0-1-1-1-2-1-1-1-2-2-3z" class="P"></path><defs><linearGradient id="R" x1="577.159" y1="262.24" x2="587.128" y2="271.238" xlink:href="#B"><stop offset="0" stop-color="#a9a8a8"></stop><stop offset="1" stop-color="#dfdede"></stop></linearGradient></defs><path fill="url(#R)" d="M576 259c2 0 4 0 5 1 1 2 5 7 6 7v1c0 1 1 2 1 3l1 1h0c-1 0-1 1-1 1v4l-1 2h0-7-1-1-1c0-2 1-4 1-6v-1l2-5-1-1h0v-1h-1v-1-3h0 1v-1l-3-1z"></path><path d="M580 279l-1-1 1-1c1-2 3-5 5-6 1 1 2 1 3 2v4l-1 2h0-7z" class="s"></path><path d="M153 195h2 1v1c1 1 2 1 3 0h2 1v2h1v1l1 1c-3 0-7 0-9 2-6 2-11 4-16 8-1 0-3 1-3 3h0c-2 2-2 4-3 7v2c-1 0-1 0-2-1-1-2 0-5 1-8 0 0 1-1 1-2l4-4 6-3v-2l2-1c3-1 5-4 8-6z" class="l"></path><path d="M153 195h2 1v1c1 1 2 1 3 0h2 1v2c-6 0-12 2-18 5l-1 1v-2l2-1c3-1 5-4 8-6z" class="h"></path><path d="M473 525h0c1-2 1-3 2-5v1 4h-1c0 1-1 1-1 2l-1 3h1v-1c1 0 1 0 1-1l2-1c-4 8-9 17-14 24l-7 8c-1 1-2 3-3 5s-1 2 0 4c0 1 0 2 1 3-1 0-2 1-3 2h0-1v-2h0v-1h0l-1-1v-5c1-3 4-6 6-9l9-11c2-3 4-7 6-10s2-6 4-9z" class="E"></path><path d="M244 603v-1h0c1-5 8-11 12-13 10-6 18-5 28-3h1c1 0 2 0 3 1h1l1 1v-1h-1v-1c5 2 8 5 12 8h-3c1 1 2 2 3 2 2 1 3 3 4 3l-1 1-11-7c-9-4-17-7-26-6l-1 1c-8 1-14 4-19 10l-3 5z" class="N"></path><path d="M292 590c2 1 5 2 6 4 1 1 2 2 3 2 2 1 3 3 4 3l-1 1-11-7h1l5 3-3-3c-1-1-3-1-4-3h0z" class="Y"></path><path d="M284 586h1c1 0 2 0 3 1h1l1 1v-1h-1v-1c5 2 8 5 12 8h-3c-1-2-4-3-6-4l-8-4z" class="K"></path><path d="M457 82c14-9 26-24 34-39 2-4 3-8 5-11 1-2 1-2 3-3 1 0 2 1 3 1 1 1 2 3 2 5 3 7 5 14 5 21v-1l-1-1v-1l-3 1c1 0 1 0 1-1v-3c-1-4-2-10-5-14h-1c0-1 0-1 1-2l-1-1 1-1c-1-1-1-1-2-1-4 2-6 10-8 14-4 8-10 16-16 23-7 9-18 16-28 21l-1-1 1-1c3-2 7-3 10-5z" class="C"></path><path d="M501 32c1 1 2 2 2 4 2 3 3 7 4 10 0 2 0 5 1 7l-3 1c1 0 1 0 1-1v-3c-1-4-2-10-5-14h-1c0-1 0-1 1-2l-1-1 1-1z" class="d"></path><defs><linearGradient id="S" x1="238.795" y1="604.796" x2="285.421" y2="579.132" xlink:href="#B"><stop offset="0" stop-color="#7a797a"></stop><stop offset="1" stop-color="#b5b4b4"></stop></linearGradient></defs><path fill="url(#S)" d="M255 581l1-2h1v3l-1 1h0c8-1 17-1 25 0 3 1 6 2 8 3h0v1h1v1l-1-1h-1c-1-1-2-1-3-1h-1c-10-2-18-3-28 3-4 2-11 8-12 13h0v1c-2 5-3 9-2 15-1-2-1-4-1-6-1-2 0-5 0-8 1-3 2-7 4-9 1-3 4-6 7-8 1-1 3-3 4-5l-1-1h0z"></path><defs><linearGradient id="T" x1="466.621" y1="627.823" x2="479.549" y2="633.608" xlink:href="#B"><stop offset="0" stop-color="#3e3d3e"></stop><stop offset="1" stop-color="#5e5e5e"></stop></linearGradient></defs><path fill="url(#T)" d="M485 608c0 2 1 4 1 6s0 4-1 6c-1 12-11 22-21 28-4 2-8 2-11 5-2 2-3 4-5 6 0 2-1 4-3 5 2-3 3-7 5-10 4-4 10-7 15-10 3-3 7-6 10-9 6-7 8-15 7-24 0-1 1-1 1-2h1l1-1z"></path><defs><linearGradient id="U" x1="561.751" y1="249.795" x2="564.206" y2="227.261" xlink:href="#B"><stop offset="0" stop-color="#bab8b8"></stop><stop offset="1" stop-color="#dbdadb"></stop></linearGradient></defs><path fill="url(#U)" d="M553 227v-1l1-1c1 1 2 1 4 2h2l4 1v1h0 3 0-1v2l4 6 2 2s1 2 2 3c0 1 1 3 1 4-1 3-1 5-3 7h-1c-1 0-1 2-1 3h-1l-1-1c1-2 2-3 3-6 0-3 0-5-2-7l-1-2c0-1 0-1-1-2-2 1-5 1-6 3h-1v-1c0-2-1-4-3-7l2-1c-2-1-4-3-6-5z"></path><path d="M570 239l-1-1h1v-1l2 2h-2zm-12-12h2l4 1v1h0 3 0-1v2l-1 1c-2-1-6-3-7-5z" class="U"></path><defs><linearGradient id="V" x1="572.928" y1="252.54" x2="572.051" y2="238.963" xlink:href="#B"><stop offset="0" stop-color="#9d9b9c"></stop><stop offset="1" stop-color="#c1c0c0"></stop></linearGradient></defs><path fill="url(#V)" d="M572 239s1 2 2 3c0 1 1 3 1 4-1 3-1 5-3 7h-1l1-3h0c1-2 1-4 1-5l-3-6h2z"></path><path d="M559 232c3 2 5 4 8 6-2 1-5 1-6 3h-1v-1c0-2-1-4-3-7l2-1z" class="g"></path><path d="M392 78c1 1 1 1 2 0l2-2c1 1 1 2 2 2s1 1 2 1l2 1v1c-2 0-5 1-6 2 0 2-1 4-1 5s1 3 1 4l1 2v1l1 1c-1 1-1 1-1 3 1 1 1 1 1 2v1h1 0l2 1-1 2-2-1c0 1-1 1-2 1l-1-2-2 2h0c-1 1-1 1-1 2h-1l-1-2 2-3-1-1h0l-2-8v-1h0c1-1 1-1 2-3 0-1 0-6-1-7h-1c-1-1-1-2-1-3 1 1 3 1 4 1v-2z" class="P"></path><path d="M399 102l2 1-1 2-2-1v-1l1-1z" class="Q"></path><path d="M396 99c-1-2-1-5-1-7h1l1 2v1h-1v4z" class="H"></path><path d="M396 99v-4h1l1 1c-1 1-1 1-1 3 1 1 1 1 1 2v1c-2-1-2-2-2-3z" class="o"></path><path d="M400 79l2 1v1c-2 0-5 1-6 2h-1c1-1 1-2 0-3h-1 0v-1h5 1z" class="B"></path><path d="M391 101l1-17c0-1 1-2 1-3h1 1c0 2-1 4-1 6-1 5-1 11 1 16l-2 2h0c-1 1-1 1-1 2h-1l-1-2 2-3-1-1h0z" class="W"></path><path d="M391 101c1 1 1 3 2 4h0c-1 1-1 1-1 2h-1l-1-2 2-3-1-1z" class="U"></path><path d="M288 83h1c2 1 4 2 6 2h0l1-1c0 1 1 2 2 2 1 1 2 4 3 5 1 2 2 4 3 7v1c1 1 1 2 2 3h-2 0v2l2 3v3 1c-1 0-2-1-3-2h-1c-1-1 0-1-1-1 0 1 0 1 1 2h0-1c-1-1-1-3-2-4h1l1 1 1-1-1-1-4-5-2-4h0l-2-2c0-1-1-1-1-2-1-1-2-3-3-4h-1l-1-1h-1v-1c2 0 3 1 4 2l1-1c-1-1-2-1-3-2-1 0-1 0-1-1h1v-1z" class="W"></path><path d="M289 83c2 1 4 2 6 2h0l1-1c0 1 1 2 2 2 1 1 2 4 3 5h-1-1v2h-1v-1c-1 0-1 0-1-1h0v-1h0c-1-1-1-2-2-2l-1-1c-2-1-3-1-5-2v-2z" class="P"></path><path d="M300 91h1c1 2 2 4 3 7v1c1 1 1 2 2 3h-2 0v2l-1-2-2-2-3-7h1v-2h1z" class="X"></path><path d="M303 99c0 1 0 1 1 2l-1 1-2-2c1 0 2 0 2-1z" class="K"></path><path d="M299 93v-2h1c1 2 2 4 2 6h0l-3-3v-1z" class="U"></path><path d="M298 93h1v1l3 3h0l1 2c0 1-1 1-2 1l-3-7z" class="f"></path><path d="M416 83h1c1 0 2-1 3-2l1 1h0 2l-1 1h4c-1 1-1 1-3 2h0l-1 3c-3 2-2 5-3 9v2h0c-1 0-3 1-3 2-1 1 0-1 0 1-1 0-1 1-1 2h0v-3c-1-2-1-4-2-5l-2-2-3-1h0l-1-1h0c-1-1-2-1-2-2s1-2 2-3c2-1 4-3 6-3l3-1z" class="Q"></path><path d="M421 82h2l-1 1h0l-1 1c-3 1-5 3-6 6l-1 1v3c-1 0-1 1-1 2l-2-2c0-1 0-1 1-2v-1l3-3v-2c1-1 3-3 4-3l2-1z" class="K"></path><path d="M411 94c0-1 0-1 1-2 0 1 0 1 2 2-1 0-1 1-1 2l-2-2z" class="f"></path><path d="M415 101v-2c0-3 0-6 2-9 1-2 4-5 6-5l-1 3c-3 2-2 5-3 9v2h0c-1 0-3 1-3 2-1 1 0-1 0 1-1 0-1 1-1 2h0v-3z" class="i"></path><path d="M416 83h1c1 0 2-1 3-2l1 1h0l-2 1c-1 0-3 2-4 3v2l-3 3v1c-1 1-1 1-1 2l-3-1h0l-1-1h0c-1-1-2-1-2-2s1-2 2-3c2-1 4-3 6-3l3-1z" class="p"></path><path d="M407 87c0 1-1 2-1 4h1c1-4 3-2 6-4 1 0 1 0 2-1v2l-3 3v1c-1 1-1 1-1 2l-3-1h0l-1-1h0c-1-1-2-1-2-2s1-2 2-3z" class="W"></path><path d="M407 92c0-1 0-1 1-2h0l1 2v1h-1 0l-1-1z" class="K"></path><path d="M410 91h2v1c-1 1-1 1-1 2l-3-1h1v-1l1-1z" class="U"></path><path d="M410 91v-1c1-1 3-2 5-2l-3 3h-2z" class="Q"></path><path d="M567 238c1 1 1 1 1 2l1 2c2 2 2 4 2 7-1 3-2 4-3 6h0l-1 1c-1 1-1 2-2 3 0 1 0 1-1 1h-1c0-2 0-3-1-5l-2 2c0 1 0 1-1 1l-3-2v-1l1-1v-1c-1-1 0-2 0-4l1-3h0l1-1 1-2v-2h1c1-2 4-2 6-3z" class="S"></path><path d="M557 254h1c1 1 1 2 2 3 0 1 0 1-1 1l-3-2v-1l1-1z" class="j"></path><path d="M568 240l1 2h-1v1c0 1 0 2-1 3s-1 1-3 1l2-2h0c1-2 2-3 2-5z" class="T"></path><path d="M566 250v-1c2-1 3 0 5 0-1 3-2 4-3 6h0l-1 1c-1 1-1 2-2 3 0 1 0 1-1 1h-1c0-2 0-3-1-5h-1l-1-1c3-1 4-3 6-4z" class="d"></path><path d="M566 250v-1c2-1 3 0 5 0-1 3-2 4-3 6h0l-1 1c1-2 1-3 1-4v-1h-1l-1-1z" class="R"></path><path d="M567 238c1 1 1 1 1 2 0 2-1 3-2 5h0l-2 2-4 4c0 1-1 2-2 3h-1v-1c-1-1 0-2 0-4l1-3h0l1-1 1-2v-2h1c1-2 4-2 6-3z" class="i"></path><path d="M562 242l2-2h0c1 1 1 2 1 3l-3 3h0c-1 0-1 0-1-1l-1-1c1-1 2-1 2-2h0z" class="q"></path><path d="M560 243c1 0 1-1 2-1h0c0 1-1 1-2 2l1 1c0 1 0 1 1 1h0c2 0 2 0 4-1l-2 2-4 4c0 1-1 2-2 3h-1v-1c-1-1 0-2 0-4l1-3h0l1-1 1-2z" class="m"></path><path d="M560 243c1 0 1-1 2-1h0c0 1-1 1-2 2l1 1c0 1 0 1 1 1-1 1-1 3-3 4h0v-2-3l1-2z" class="Q"></path><path d="M176 363h1c1-1 1-2 2-2h3 0c1 2 3 4 2 6v2h0 1c2 2 3 5 3 8h0-2-1 0l-7-1-9 6-1-1h-2l-3 2-1-1s1-1 2-1v-2c1-1 2-1 3-2 3-2 6-5 7-8 1-2 1-4 2-6z" class="H"></path><path d="M180 367c0-1 0-1 1-1l-1 4-1-1 1-2z" class="f"></path><path d="M180 367c0-2 0-2 1-3h1c0 1 0 2-1 2s-1 0-1 1zm-1 2l1 1h-1v2c0 1-2 3-3 3v-1h-2v-2-2c1 1 1 2 1 3h0 0c2-1 3-2 4-4z" class="d"></path><path d="M184 369h1c2 2 3 5 3 8h0-2v-1c-2-1-5-1-7-1 2-2 4-4 5-6h0z" class="n"></path><defs><linearGradient id="W" x1="167.563" y1="373.253" x2="169.65" y2="379.903" xlink:href="#B"><stop offset="0" stop-color="#afadae"></stop><stop offset="1" stop-color="#d1d0d1"></stop></linearGradient></defs><path fill="url(#W)" d="M174 369v1 2 2h2v1l-4 3-2 1-2 2h-2l-3 2-1-1s1-1 2-1v-2c1-1 2-1 3-2 3-2 6-5 7-8z"></path><path d="M216 70c-3-11-2-28 5-38 0-1 1-2 3-3 1 0 2 0 2 1 1 1 2 3 2 4l4 8c6 13 15 25 26 34l9 6 6 3c2 2 5 4 8 6h-1c-4-3-11-5-16-9-12-9-23-21-30-34l-8-16-1 1h0v3s-1-1-1-2v-1c-1 1-1 1-1 2-2 2-3 4-4 7l-1 6v4c0 5 1 9 3 14h-1c0-1 0-1-1-2v-1h-1c-1 1-2 0-3 0v3c1 1 1 1 1 2v1 1z" class="T"></path><path d="M218 41l1 1h0l-1 6h-1l-1-1 2-6z" class="X"></path><path d="M218 52l-1 6c-1-4-1-7-1-11l1 1h1v4z" class="F"></path><path d="M218 41l2-6c1-2 2-3 4-4 1 0 1 0 2 1l-1 1h0v3s-1-1-1-2v-1c-1 1-1 1-1 2-2 2-3 4-4 7h0l-1-1z" class="W"></path><defs><linearGradient id="X" x1="256.244" y1="627.477" x2="243.288" y2="632.698" xlink:href="#B"><stop offset="0" stop-color="#404040"></stop><stop offset="1" stop-color="#696768"></stop></linearGradient></defs><path fill="url(#X)" d="M255 581l1 1c-1 2-3 4-4 5-3 2-6 5-7 8-2 2-3 6-4 9 0 3-1 6 0 8 0 2 0 4 1 6 1 12 10 21 20 28 4 2 8 4 10 7s4 6 5 9h0c-2-3-3-6-6-8-3-4-8-5-12-7-2-1-5-3-7-4-7-7-13-16-14-26v-6c1-1 1-2 1-3l2-8 1-3h0l2-5 4-4 7-7z"></path><defs><linearGradient id="Y" x1="557.722" y1="182.92" x2="556.999" y2="194.038" xlink:href="#B"><stop offset="0" stop-color="#b7b5b5"></stop><stop offset="1" stop-color="#dbdadb"></stop></linearGradient></defs><path fill="url(#Y)" d="M549 177h-1c1-1 6-3 7-3 2 0 4 0 6 1h0c2 1 3 3 4 4 0 1-1 2-1 3v2l3-4 3 3 1 2c0 2-1 4 0 6 0 1 2 4 2 5h-1 0l-2-3-2 1 1 1h-1-3c0 1 0 1-1 1l-5-2h-2l-2-1c-2 1-4 1-6 2h-2 0l-1-1c3-2 6-4 9-5h1l-2-1v-1c3-1 7-2 8-5 1-1 1-2 1-3-1-2-2-3-4-3-3-1-7 0-10 1z"></path><path d="M555 189l1 1h0 1c-3 2-8 2-9 5h1-2 0l-1-1c3-2 6-4 9-5z" class="i"></path><path d="M560 192c1 0 2-1 4-1h2 0v-2h1c1 2 2 3 3 4l-2 1 1 1h-1-3c0 1 0 1-1 1l-5-2h-2l-2-1 5-1z" class="K"></path><path d="M560 192c1 0 2-1 4-1h2 0v-2h1v1c-1 1-1 2-1 2 0 1-1 1-2 1l-4-1z" class="Q"></path><path d="M564 184l3-4 3 3 1 2c0 2-1 4 0 6 0 1 2 4 2 5h-1 0l-2-3c-1-1-2-2-3-4h-1v-1c-2-1-3 0-4 0h-1l3-3v-1z" class="r"></path><path d="M610 297c3 1 27 1 29 0 3-2 10-4 13-3h3c1 1 2 1 3 3l4 3h-70v1c-1-1-2-2-4-3h-5-3c-2-1-5 0-7-1h-1l1-1h3 2l15 1c5 0 11 1 16 0h1z" class="j"></path><path d="M655 294c1 1 2 1 3 3h-6-13c4-2 9-2 13-3h3z" class="Q"></path><path d="M402 81h0v1c-1 1-2 3-3 4l1 2 1 2v1c1 1 1 2 3 2h4 0l3 1 2 2c1 1 1 3 2 5v3c-1 2-1 3-1 5l-3 2c-1 0-2 0-2-1v-1h0-1c-1-1-1-2-2-2 0 0-1 1-2 1 0 1 0 1-1 1v-1c0-1 1-1 1-2-1 0-2-1-3-1h-1l1-2-2-1h0-1v-1c0-1 0-1-1-2 0-2 0-2 1-3l-1-1v-1l-1-2c0-1-1-3-1-4s1-3 1-5c1-1 4-2 6-2z" class="p"></path><path d="M400 100c0 1 1 2 2 2l1 1h-2l-2-1h0c0-1 0-1 1-2z" class="X"></path><path d="M399 86l1 2 1 2v1l-1 1-1-1-1-3c0-1 1-1 1-2z" class="Q"></path><path d="M397 94h1c0 1 1 1 2 1 0 1 0 1-1 1 0 2 0 3 1 4-1 1-1 1-1 2h-1v-1c0-1 0-1-1-2 0-2 0-2 1-3l-1-1v-1z" class="d"></path><path d="M411 99c1 3 0 7-1 10h-1 0-1c-1-1-1-2-2-2l1-1 2-2h1 1v-3-2z" class="W"></path><path d="M407 106c1 0 1 0 2 1v2h-1c-1-1-1-2-2-2l1-1z" class="l"></path><path d="M404 95h4l2 2c1 1 1 2 1 2v2 3h-1-1l-2 2-1 1s-1 1-2 1c0 1 0 1-1 1v-1c0-1 1-1 1-2-1 0-2-1-3-1h-1l1-2h2l-1-1h2c0-1 1-2 2-2-1-1-2-2-2-3l-1-1h0 0v-1h1z" class="P"></path><path d="M409 104h-1c1-1 1-2 2-2h0v2h-1z" class="m"></path><path d="M410 97c1 1 1 2 1 2v2h0c-1 0-1-1-1-1l-2-1h0c1 0 2-1 2-2z" class="K"></path><path d="M404 95h4l-1 2c-1 0-2-1-3-1v-1z" class="h"></path><path d="M401 105c1 0 1-1 2-1l1 1h1c0 1-1 1-1 2v1c0 1 0 1-1 1v-1c0-1 1-1 1-2-1 0-2-1-3-1z" class="f"></path><path d="M408 95l2 2c0 1-1 2-2 2h0c-1 0-1-1-1-2l1-2z" class="F"></path><path d="M583 298h5c2 1 3 2 4 3v-1c2 2 5 4 7 6l3 3c3 2 5 5 8 7 1 2 3 3 4 5-2 0-5-1-7-2-5-1-12-2-17-5-1-1-1-2-2-3 0-1 0-1-1-2l-1-1-3-3v-1-3-3z" class="W"></path><path d="M322 93c1 0 1 0 2 1v1c-1 2-1 6-2 7v1h1c1 0 2 0 2 1l1 1h-2c0 1-1 1-2 2h-4c0 1 1 1 2 2h1v1l1 1h0c-1 1-1 1-1 2-1 1 0 3-1 3h-1c0-1-1-1-1-1l-1 1h-1 0c-3-2-6-4-8-7l-2-2-2-3v-2h0 2c0 1 0 2 1 2l2 2-1-6c1-1 3-3 3-4 2-2 2-3 4-3h3 1 3z" class="r"></path><path d="M313 105v-1-3h1l1 1v2c1 1 2 3 3 3 0 1 1 1 2 2h1v1l-1 1-2-2v1c-1 0-1 1-1 2h0c-1 0-1 0-1 1l-2-1c0-1 0-2-1-2l1-1v-1l1 1 1 1 1 1v-2h1c-2-2-3-4-4-6 0 2 1 2 0 4-1-1-1-1-1-2z" class="l"></path><path d="M314 112c0-1 0-2-1-2l1-1 3 3c-1 0-1 0-1 1l-2-1z" class="P"></path><path d="M317 112h0c0-1 0-2 1-2v-1l2 2 1-1 1 1h0c-1 1-1 1-1 2-1 1 0 3-1 3h-1c0-1-1-1-1-1l-1 1h-1l-2-3v-1l2 1c0-1 0-1 1-1z" class="U"></path><path d="M318 102l1-1v1h3 0v1h1c1 0 2 0 2 1l1 1h-2c0 1-1 1-2 2h-4c-1 0-2-2-3-3l2-1 1-1z" class="Q"></path><path d="M318 102l1-1v1h3l-2 1c-1 1-1 1-3 1v-1l1-1z" class="W"></path><path d="M317 103v1l1 1h3 3c0 1-1 1-2 2h-4c-1 0-2-2-3-3l2-1z" class="f"></path><path d="M304 104v-2h0 2c0 1 0 2 1 2l2 2 1 2c1 0 2 1 3 1h0l1-1v1l-1 1c1 0 1 1 1 2v1l2 3h0c-3-2-6-4-8-7l-2-2-2-3z" class="a"></path><path d="M310 108c1 0 2 1 3 1h0l1-1v1l-1 1c1 0 1 1 1 2v1c-2-2-3-3-4-5z" class="Q"></path><path d="M304 104v-2h0 2c0 1 0 2 1 2 0 2 1 4 1 5l-2-2-2-3z" class="L"></path><path d="M313 105c0-1-1-2-1-2 0-2 0-5 1-6s2-2 4-2c1 0 2 0 3 1v2c-1 1-1 2-1 3h0l-1 1-1 1-2 1v-2l-1-1h-1v3 1z" class="P"></path><path d="M315 102v-3c1 1 2 1 2 2l1 1-1 1-2 1v-2z" class="X"></path><path d="M342 603c3 4 5 8 7 13 3 5 5 11 4 17 0 2-1 5-2 7 0 0-2 2-2 3s3 4 3 5c-1 0-2 0-3-1h-2l-1-1v2l1 1h-1c-1 0-1 1-2 1h0c0 1-1 1-1 1l-1 1c-2-3-6-6-8-9l-12-9-5-5c-1-1-2-2-4-3-2 0-6 1-9 1l1-1c2 0 3-1 5-2h1l4-2c3 4 8 11 12 12 2 2 11 10 12 10l1-1h0v-2l-1-2c1 0 2 1 2 2v1c1-1 1-1 2-1v-1l1 1c1 0 2-2 3-2 2-2 5-6 5-9 0-1 0-1-1-1l-1 1c0-8-6-18-10-24v-1l2-2z" class="Z"></path><path d="M344 650h-1v-4c1-1 2-2 3-2v1c-1 1-1 2-1 3s0 1-1 2h0z" class="k"></path><path d="M267 82v-1h0c1-1 2-1 3-1l-1 1h-1c1 1 1 1 2 1l1 1h2l3 2s1 0 2 1l5 2 1 1h0v-2c1 0 1 0 2 1h1l3 3c0 1 1 1 1 2l1 1h1l2 2h0l2 4 4 5 1 1-1 1-1-1h-1c1 1 1 3 2 4h1c2 3 6 6 7 9v2c-1-2-3-4-4-5l-1 1c0 1 1 1 2 2 1 2 2 3 3 4l2 2s0 1 1 1l1 3c0 1 1 2 1 2v2c-1-2-3-4-4-6-2-2-3-5-5-7l-1 1-1-1h-1l-2-4-2-3v1h-1c-1-2-2-3-4-3-1-1-1-3-2-5-2-2-4-5-6-7h1c0-2-2-3-3-5-1-1-2-1-2-2l-1-1h1c-3-2-6-4-8-6l-6-3z" class="Q"></path><path d="M300 116c1 0 2 0 3 1v3h-1l-2-4zm-8-19h1v-1h1v1h0c1 2 1 2 1 3h-1 0c-1-1-1-2-2-2v-1z" class="U"></path><path d="M267 82v-1h0c1-1 2-1 3-1l-1 1h-1c1 1 1 1 2 1l1 1h2l3 2s1 0 2 1l5 2 1 1h0c1 0 4 2 4 3l1 1c0 1 1 1 1 2h1c0 1 0 1 1 2h0v1c-1 0-1 0-1-1l-1 1v1c1 1 1 2 1 4l7 10v1h-1c-1-2-2-3-4-3-1-1-1-3-2-5-2-2-4-5-6-7h1c0-2-2-3-3-5-1-1-2-1-2-2l-1-1h1c-3-2-6-4-8-6l-6-3z" class="i"></path><path d="M281 91s1 1 2 1c2 2 4 5 5 8l3 3 7 10v1h-1c-1-2-2-3-4-3-1-1-1-3-2-5-2-2-4-5-6-7h1c0-2-2-3-3-5-1-1-2-1-2-2l-1-1h1z" class="f"></path><path d="M245 519c1 2 2 3 3 4v1c0 1 1 1 1 1l1 1c1 0 1 0 2 1-1-1-1-1-1-2 1 0 1 0 1 1 1 5 6 11 9 16 1 2 2 4 3 5h1s1 1 1 2h1c0-1-1 0-1-1v-3h1 0c0-1 0-1-1-2 0-1-1-2-1-3-1-1-1-3-2-4h0 0c3 4 4 8 7 12 1 1 2 3 3 4l6 8c0 1 1 2 1 2 1 1 3 5 5 5 1 0 1-1 1-1v1 2c0 1-1 1-2 2h-5-2l-2 1v2l-5-4c1-1 2-3 3-4-2-4-5-8-8-12l-7-9c-3-6-7-12-10-18v-2l-1-1c-1-1-2-3-2-5z" class="R"></path><defs><linearGradient id="Z" x1="266.922" y1="553.392" x2="278.219" y2="553.314" xlink:href="#B"><stop offset="0" stop-color="#706f6f"></stop><stop offset="1" stop-color="#878687"></stop></linearGradient></defs><path fill="url(#Z)" d="M263 536c3 4 4 8 7 12 1 1 2 3 3 4l6 8c0 1 1 2 1 2 1 1 3 5 5 5 1 0 1-1 1-1v1 2c0 1-1 1-2 2h-5-2-1c-1-1 1-4 2-5 0-1-1-3-1-4-2-1-3-3-5-5-2-3-5-6-7-10 0 0 1 1 1 2h1c0-1-1 0-1-1v-3h1 0c0-1 0-1-1-2 0-1-1-2-1-3-1-1-1-3-2-4h0 0z"></path><path d="M277 562h0c2 1 4 6 6 6-1-2-4-4-3-6 1 1 3 5 5 5 1 0 1-1 1-1v1 2c0 1-1 1-2 2h-5-2-1c-1-1 1-4 2-5 0-1-1-3-1-4z" class="G"></path><path d="M370 67l2 3 2 1 5 6 2 3 1 1 2 5-1 2 2 2 1 2h1l1 1h1l2 8h0l1 1-2 3 1 2h1c0-1 0-1 1-2 1 1 2 2 2 3 1 2 3 2 4 3-1 1-1 1-1 2s-1 2-2 3c-1 0-2 1-3 1l-2 2h-1c-1 0-1 0-2-1l-1-1c0 1-1 1-2 1l-1 1-1-1 1-3h0v-2c1-1 1-1 1-2h-2v-1-1l1-2h0c1-2 1-4 1-6v-1c-1-4-2-7-4-11-3-3-5-7-8-10l-4-7-1-2c0-1-1-1-1-2 1 0 2 0 2 1v-1l1-1z" class="q"></path><path d="M372 70l2 1 5 6 2 3 1 1h-2c-1-2-2-3-3-5-2-2-4-4-5-6zm-5-2c1 0 2 0 2 1l9 12c2 2 4 4 5 7l2 2h-1c-1 0-2-1-2-2-1 0-1 0-1-1l-3-3c1 2 2 3 3 5-3-3-5-7-8-10l-4-7-1-2c0-1-1-1-1-2z" class="Q"></path><path d="M381 89c-1-2-2-3-3-5l3 3c0 1 0 1 1 1 0 1 1 2 2 2h1l1 2h1l1 1h1l2 8h0l1 1-2 3-1 2v2l-2 2h0l-1-1h-1-1l-1-1 1-2h0c1-2 1-4 1-6v-1c-1-4-2-7-4-11z" class="q"></path><path d="M390 103l2-1-2 3-1 2v2l-2 2h0l-1-1c1-2 2-3 3-5 1-1 1-1 1-2z" class="H"></path><path d="M384 90h1l1 2h1l1 1h1l2 8h0l1 1-2 1h0c-1-1-1-1-2-1 0-2 0-4-1-6h0l-3-6z" class="f"></path><path d="M388 93h1l2 8h0l1 1-2 1h0c-1-4-1-7-2-10z" class="c"></path><path d="M389 107l1-2 1 2h1c0-1 0-1 1-2 1 1 2 2 2 3 1 2 3 2 4 3-1 1-1 1-1 2s-1 2-2 3c-1 0-2 1-3 1l-2 2h-1c-1 0-1 0-2-1l-1-1c0 1-1 1-2 1l-1 1-1-1 1-3h0v-2c1-1 1-1 1-2h-2v-1-1l1 1h1 1l1 1h0l2-2v-2z" class="f"></path><path d="M389 107l1-2 1 2h1c0 1 0 1 1 2h0v1c-1-1-1 0-1-1h-1c0-1-1-1-2-2z" class="m"></path><path d="M389 107c1 1 2 1 2 2h1c0 1 0 0 1 1l-2 4c-1 0-1-1-1-1 0-2 1-3 0-4h0-1v-2z" class="U"></path><path d="M388 118c2-1 3-2 4-3 0-1 2-2 2-2h1c0 1 1 2 1 3-1 0-2 1-3 1l-2 2h-1c-1 0-1 0-2-1z" class="Q"></path><path d="M389 109h1 0c1 1 0 2 0 4 0 0 0 1 1 1l-4 3c0 1-1 1-2 1l-1 1-1-1 1-3h0v-2c1-1 1-1 1-2h-2v-1-1l1 1h1 1l1 1h0l2-2z" class="W"></path><path d="M384 113h1v4 1l-1 1-1-1 1-3h0v-2z" class="l"></path><path d="M577 331l2 2 4 2c0 1 0 1 1 1 1 1 2 3 4 4h1c3 3 5 7 6 12 0 0 1 5 2 5l-2 1-2 3-2-4-1-1c0 5-3 9-5 13-2 1-3 2-5 3l1-1c0-1 1-2 1-2 2-3 4-5 4-8 1-3 1-8-1-11-3-5-10-7-16-8-2-1-4-1-6-1 2 0 3-1 3-2v-1l-1-1h0l2-2 2-3c2 1 6 0 8-1z" class="K"></path><path d="M590 356c-1 0-1 0-1-1s0-1-1-2h1 0c1 1 2 1 2 1l2 2v2h1 1l-2 3-2-4-1-1z" class="X"></path><path d="M572 338v-1c2 0 5 0 6 1 3 2 5 4 7 7l2 3c-3-2-6-5-8-6l-7-4z" class="q"></path><path d="M567 335h2c-1 1-2 1-2 2h1 3l1 1h0l7 4c-2 1-4-2-6-1-1 0-3 0-4 1-2-1-4-1-6-1 2 0 3-1 3-2v-1l-1-1h0l2-2z" class="Z"></path><path d="M568 337h3l1 1h-1c-1 1-2 1-3 1l-1-1 1-1z" class="D"></path><path d="M577 331l2 2 4 2c0 1 0 1 1 1 1 1 2 3 4 4h1c3 3 5 7 6 12 0 0 1 5 2 5l-2 1h-1c1-1 0-2 0-3v-3c-1-3-4-9-7-11h0c-1 0-1-1-2-1s-1-1-2-1h0-2c-1-1-2-1-3-1-1-1-4-1-6-1v1h0l-1-1h-3-1c0-1 1-1 2-2h-2l2-3c2 1 6 0 8-1z" class="I"></path><path d="M577 331l2 2 4 2c-2 1-4 0-6 0-1 0-3 1-5 1 0 1-1 1-1 1h-3-1c0-1 1-1 2-2h-2l2-3c2 1 6 0 8-1z" class="V"></path><path d="M577 331l2 2c-2 2-7 1-10 2h-2l2-3c2 1 6 0 8-1z" class="T"></path><defs><linearGradient id="a" x1="196.319" y1="473.692" x2="235.658" y2="508.956" xlink:href="#B"><stop offset="0" stop-color="#0f0e0e"></stop><stop offset="1" stop-color="#444"></stop></linearGradient></defs><path fill="url(#a)" d="M204 467h1 0v-3c0-1 0-1 1-2 1 2 2 4 4 5h5 0c0 1-1 1-1 1 0 5 0 10 1 14v5h2c0 5 1 9 3 14v1l-1 1h0c2 6 5 12 9 17h-1 0c-1 0-2 0-2 1h0 0-1c-15-13-19-35-20-54z"></path><path d="M215 487h2c0 5 1 9 3 14v1l-1 1h0c-1-1-1-2-1-3-1-4-2-9-3-13z" class="L"></path><path d="M152 385h0c4-2 8-3 12-6v2c-1 0-2 1-2 1l1 1 3-2h2l1 1c-2 2-5 4-7 6 1 3 2 4 1 7l1 1 1-1-1 4c-2 4-2 8-1 11v1h-1 0 0c-2-2-2-4-4-6v-1c0-1 0-2 1-4v-1c0-2 1-5 2-7-6 5-13 10-14 19v5c0-1-1-2-1-3v-1-1-2h1v-1c-1 1-2 3-2 5-1 1-1 2-1 5-1-3 0-7-2-9h-3c-1 2-3 5-6 6-2 1-5 1-7 0h0 1c0-1 1-1 1-1h2c0-1 1-1 1-1 5-3 6-7 8-12l2-4 1 1c3-1 5-5 7-8 2-1 2-2 3-4v-1h0z" class="i"></path><path d="M139 409c0-1 1-3 2-4 0 2 0 3 1 4h-3z" class="b"></path><path d="M162 382l1 1c-4 3-7 6-11 9 0-1 4-4 4-5 1-1 0 0 2-1l1-1c1 0 2-1 2-1v-1c-2 1-3 2-4 3-2 1-4 4-6 5h0c1-2 3-3 4-5 2-1 5-3 7-4z" class="l"></path><path d="M152 385h0c4-2 8-3 12-6v2c-1 0-2 1-2 1-2 1-5 3-7 4-1 2-3 3-4 5l-2-1c2-1 2-2 3-4v-1h0z" class="P"></path><path d="M169 382l9-6 7 1c1 1 3 1 4 2l1 7c2 4 2 8 2 12v1h2c-1 0-2 0-2 1-2 0-4-2-5-3 0 1 0 2 1 3v1h-1v-1c-2-2-5-4-8-5s-6 0-9 1h-2c-2 1-3 2-4 3l1-4-1 1-1-1c1-3 0-4-1-7 2-2 5-4 7-6z" class="n"></path><path d="M186 393c1-1 0-4 0-6 1 1 2 4 3 4h1l-1-2c0-3-4-5-7-6l1-1c3 1 4 4 7 5v-1c2 4 2 8 2 12v1c-1-1-2-3-4-4-1 0 0 1 0 0-1-1-2-1-3-2h1z" class="U"></path><path d="M173 391c4-1 8-2 12 1 1 0 1 0 1 1h-1c1 1 2 1 3 2 0 1-1 0 0 0 2 1 3 3 4 4h2c-1 0-2 0-2 1-2 0-4-2-5-3 0 1 0 2 1 3v1h-1v-1c-2-2-5-4-8-5s-6 0-9 1h-2c-2 1-3 2-4 3l1-4c2-2 5-3 8-4z" class="b"></path><path d="M173 391h5c-2 0-4 1-6 2-1 1-3 2-4 3-2 1-3 2-4 3l1-4c2-2 5-3 8-4z" class="I"></path><path d="M173 391c4-1 8-2 12 1 1 0 1 0 1 1h-1c1 1 2 1 3 2 0 1-1 0 0 0 2 1 3 3 4 4h2c-1 0-2 0-2 1-2 0-4-2-5-3-3-3-5-5-9-6h-5z" class="Q"></path><defs><linearGradient id="b" x1="398.74" y1="607.984" x2="411.626" y2="614.779" xlink:href="#B"><stop offset="0" stop-color="#151515"></stop><stop offset="1" stop-color="#3b393b"></stop></linearGradient></defs><path fill="url(#b)" d="M427 577h1 0v2c-1 1-1 1-2 1v1h0 0c0 2-1 2-2 3l-5 7-7 10c-2 3-4 5-5 8l3-3h0 0c2 0 4-3 6-5h1 1c1 0 1-1 2-1l-5 4s-1 0-1 1c-2 1-2 1-3 3h0c-3 4-8 8-12 12-3 3-5 8-8 12-2 4-5 8-6 12h0 1v1 1l1-1h0v1l-1 2-2-1c-2-1-3-1-3-3v-2c6-14 15-25 23-38h-1v-1h-1 0v-1c1-1 2-2 4-3h0 2 1l-1-1h-1 0c0-3 3-7 4-9l3-4 1 1c3-3 5-7 9-8l3-1z"></path><path d="M406 599h0 2l-3 3-1 2h-1v-1h-1 0v-1c1-1 2-2 4-3z" class="b"></path><path d="M402 603c1-1 2-1 3-1l-1 2h-1v-1h-1 0z" class="Y"></path><path d="M335 77l1 1 3-3c0 2 0 4-1 6 0 2 0 2 1 2-1 2-1 4-1 6l-1 4v1l-1 1h0l-2 2c0 2 0 4-1 6l1 3v1h-1l1 1c-1 1-2 1-2 2v1c1 1 1 3 2 4l-1 1c1 1 2 2 2 3v1c-1 0-1 0-2 1l2 2-1 1-1-1c-1-1-3-4-5-5l-3-3c-1-1-1-2-2-3 2-1 4-2 5-3l1-1 1-2v-3h0c-1 0-1 0-1 1-1 1-1 2-2 3-2 1-3 2-5 4l-1-1v-1h-1c-1-1-2-1-2-2h4c1-1 2-1 2-2h2l-1-1c0-1-1-1-2-1h-1v-1c1-1 1-5 2-7v-1c-1-1-1-1-2-1l1-2c0-1 1-2 0-3 0-2-2-5-3-6v-1c0-1 1 0 2-1h1c1 0 1-1 2-1 2 0 5 0 6-1 1 1 1 1 2 1s1-1 2-2z" class="m"></path><path d="M328 93h1l-1 5h-1c0-2 0-3 1-5z" class="H"></path><path d="M326 92v-1c2-1 3-2 3-4 0 1 0 3-1 5h-2z" class="Q"></path><path d="M326 93v2 1 1c0 1-1 4-2 5h-2c1-1 1-5 2-7v-1l2-1z" class="W"></path><path d="M326 93v2 1c-1 1-1 2-2 2v-3-1l2-1z" class="X"></path><path d="M334 92c1-4 0-9 2-12h1l1 1h0c0 2 0 2 1 2-1 2-1 4-1 6l-1 4v1l-1 1h0l-2 2v-5z" class="i"></path><path d="M338 81h0c0 2 0 2 1 2-1 2-1 4-1 6l-1 4v1l-1-1c0-3 0-5 1-8v-1c1-1 1-1 1-3z" class="d"></path><path d="M322 80h1c1 1 2 1 3 1v1c1 0 2 1 2 1l1 4c0 2-1 3-3 4v1 1l-2 1c-1-1-1-1-2-1l1-2c0-1 1-2 0-3 0-2-2-5-3-6v-1c0-1 1 0 2-1z" class="l"></path><path d="M326 106h1c2-2 2-4 2-6 2-6 1-10 1-16l2-2h1c1 2 0 5 0 7 0 5-2 10-1 15-1 2-2 3-4 5l1-1 1-2v-3h0c-1 0-1 0-1 1-1 1-1 2-2 3-2 1-3 2-5 4l-1-1v-1h-1c-1-1-2-1-2-2h4c1-1 2-1 2-2l2 1z" class="W"></path><path d="M324 105l2 1c-2 2-3 2-6 3-1-1-2-1-2-2h4c1-1 2-1 2-2z" class="L"></path><path d="M333 89l1-1v4 5c0 2 0 4-1 6l1 3v1h-1l1 1c-1 1-2 1-2 2v1c1 1 1 3 2 4l-1 1c1 1 2 2 2 3v1c-1 0-1 0-2 1l2 2-1 1-1-1c-1-1-3-4-5-5l-3-3c-1-1-1-2-2-3 2-1 4-2 5-3 2-2 3-3 4-5-1-5 1-10 1-15z" class="a"></path><path d="M330 109c0-1 1-2 2-3h0l1-1v2l1 1c-1 1-2 1-2 2v1l-1-1-1-1z" class="P"></path><path d="M330 109l1 1 1 1c1 1 1 3 2 4l-1 1c1 1 2 2 2 3v1c-1 0-1 0-2 1-2-3-5-5-8-7v-1c2-1 2 0 4 0h0c-1-1-1-1-1-2l2-2z" class="W"></path><path d="M331 110l1 1c1 1 1 3 2 4l-1 1c1 1 2 2 2 3h-1c-1-2-2-3-4-4 0-2 0-3 1-5z" class="H"></path><defs><linearGradient id="c" x1="584.449" y1="390.107" x2="559.499" y2="399.346" xlink:href="#B"><stop offset="0" stop-color="#cccbcc"></stop><stop offset="1" stop-color="#f0efef"></stop></linearGradient></defs><path fill="url(#c)" d="M550 373l3 3 1-1c5 4 11 8 17 9 1 1 3 0 4 1h1c0 1 1 1 1 2 1 0 2 0 3 1h1c1-1 2-2 3-2h1l-1 2c0 4 2 7 2 11 1 3 2 7 3 10 0 1 1 1 1 2v2c2 0 3 1 5 2h3c-4 1-6 1-10-1-2-1-3-3-5-5-1-1-2-1-3-1 0 2 0 4-1 6v-5l-4-5v1c1 2 1 5 1 7v1-1c0-5-1-9-6-13-1-1-4-3-5-5-1 0-2-1-3-2h1c1-1 1-1 1-2l-2-2-1-1c-2-2-5-4-8-6-1-1-2-2-2-3l-2-2h1c0-1-1-2-1-2l1-1z"></path><path d="M574 387h3c1 0 2 0 3 1l-3 2c0-1-2-2-3-3z" class="W"></path><path d="M581 388h1c-1 2-2 3-3 3v1l-2-2 3-2h1z" class="B"></path><path d="M575 385h1c0 1 1 1 1 2h-3l-3-2h4z" class="H"></path><path d="M581 388c1-1 2-2 3-2h1l-1 2c-2 1-2 3-4 4v1l-1-1v-1c1 0 2-1 3-3h-1z" class="G"></path><path d="M554 375c5 4 11 8 17 9 1 1 3 0 4 1h-4 0-4c-1-1-2-2-3-2-4-2-7-5-11-7l1-1z" class="W"></path><path d="M584 401h0c1-1 2-1 2-2 1 3 2 7 3 10 0 1 1 1 1 2v2c-2-2-3-4-4-7-1-1-2-3-2-5z" class="d"></path><path d="M580 393v-1c2-1 2-3 4-4 0 4 2 7 2 11 0 1-1 1-2 2h0c-1-3-2-5-4-8z" class="h"></path><defs><linearGradient id="d" x1="562.926" y1="392.864" x2="567.13" y2="389.388" xlink:href="#B"><stop offset="0" stop-color="#7f7e7f"></stop><stop offset="1" stop-color="#bbbaba"></stop></linearGradient></defs><path fill="url(#d)" d="M549 376h1c4 4 10 6 14 10l6 6c1 1 3 4 4 5 1 0 2 1 2 2 2 2 3 4 4 6v-1c1 0 1 0 2-1v1c1 0 1 2 1 4v1c-1-1-2-1-3-1 0 2 0 4-1 6v-5l-4-5v1c1 2 1 5 1 7v1-1c0-5-1-9-6-13-1-1-4-3-5-5-1 0-2-1-3-2h1c1-1 1-1 1-2l-2-2-1-1c-2-2-5-4-8-6-1-1-2-2-2-3l-2-2z"></path><path d="M582 404c1 0 1 2 1 4v1c-1-1-2-1-3-1l-1-1 1-1 2-2z" class="K"></path><path d="M562 388c4 2 8 6 10 9 2 2 3 5 4 7 1 1 3 3 3 4v1l-4-5v1c1 2 1 5 1 7v1-1c0-5-1-9-6-13-1-1-4-3-5-5-1 0-2-1-3-2h1c1-1 1-1 1-2l-2-2z" class="Q"></path><defs><linearGradient id="e" x1="628.439" y1="279.589" x2="631.467" y2="297.261" xlink:href="#B"><stop offset="0" stop-color="#8a8989"></stop><stop offset="1" stop-color="#b1b1b1"></stop></linearGradient></defs><path fill="url(#e)" d="M622 263l26 25c3 2 5 3 7 6h-3c-3-1-10 1-13 3-2 1-26 1-29 0h0c1-1 1-1 1-2l1-1 2-2v-1l1-1h0c2-3 4-6 5-10l19 1-17-17v-1z"></path><path d="M355 57l1 1c0-1 0-1 1-1h1 0v1h0v1h-1v1 2l1-1h1v-1c1 1 1 2 1 3v1c-1 0-1 1-1 1v2c-1 0-1 0-1 1l-1 1c-1 1-3 3-4 5v1h1c-2 3-4 7-6 10l-1 1c-1 2-2 4-3 7v3l-1-1h0v2c0 1 1 2 1 3 1 3 4 6 3 8v3c-1 0-1 1-1 2s0 1-1 2l-1-1-1-1v1h-1v-1l-1 1h0l-1-1-3-4-3-3-1-3c1-2 1-4 1-6l2-2h0l1-1v-1l1-4c0-2 0-4 1-6-1 0-1 0-1-2 1-2 1-4 1-6l3-3 1-1h3c2-4 5-6 6-10v-2l1 2 1-1 1-3z" class="l"></path><path d="M339 102h0c-1-3 0-6 0-9 1 1 1 2 1 4l2 4-1 2-1-1-1-3h0v3z" class="P"></path><path d="M339 102v-3h0l1 3 2 8c1 1 1 2 2 2h0l2 1c0 1 0 1-1 2l-1-1-1-1v1h-1v-1c-1-3-3-5-3-8 1-1 0-2 0-3z" class="o"></path><path d="M337 94v-1c0 3-2 7-1 10l1 6-3-3-1-3c1-2 1-4 1-6l2-2h0l1-1z" class="Q"></path><path d="M342 97v3c0-2 0-2 1-3 0 1 1 2 1 3 1 3 4 6 3 8v3c-1 0-1 1-1 2l-2-1h0c-1 0-1-1-2-2l-2-8 1 1 1-2-2-4h2z" class="f"></path><path d="M342 97v3h0l1 1c1 1 1 3 2 4v1c-1-1-1-1-1-2-1-1-2-1-2-2v-1l-2-4h2z" class="a"></path><path d="M344 112c0-2-2-5-1-7l1 1c1 0 2 0 3 1v1 3c-1 0-1 1-1 2l-2-1z" class="b"></path><path d="M344 106c1 0 2 0 3 1v1 3l-1-1c-1-1-2-2-2-4z" class="W"></path><path d="M359 61v-1c1 1 1 2 1 3v1c-1 0-1 1-1 1v2c-1 0-1 0-1 1l-1 1c-1 1-3 3-4 5v1h1c-2 3-4 7-6 10l-1 1c-1 2-2 4-3 7v3l-1-1h0v2c-1 1-1 1-1 3v-3h-2c0-2 0-3-1-4l1-2c2-1 3-4 4-6l7-11c1-2 1-4 2-6s3-4 4-6l1-1h1z" class="K"></path><path d="M340 91c1 1 2 2 1 3v1l1 2h-2c0-2 0-3-1-4l1-2z" class="I"></path><path d="M359 61v-1c1 1 1 2 1 3v1c-1 0-1 1-1 1v2c-1 0-1 0-1 1l-1 1c-1 1-3 3-4 5h0c0-1 1-2 1-3 1-4 3-7 5-10zm-4-4l1 1c0-1 0-1 1-1h1c-1 3-3 6-5 8-1 2-2 3-2 5-1 1-1 2-1 2-2 3-4 6-6 8l-3 3v1l-1 1-1 2-1 2c0-2 0-4 1-6-1 0-1 0-1-2 1-2 1-4 1-6l3-3 1-1h3c2-4 5-6 6-10v-2l1 2 1-1 1-3z" class="Q"></path><path d="M342 72h1l-1 2 1 1h1c-1 3-4 6-5 8-1 0-1 0-1-2 1-2 1-4 1-6l3-3z" class="E"></path><path d="M355 57l1 1c0-1 0-1 1-1-3 6-8 14-13 18h-1l-1-1 1-2h-1l1-1h3c2-4 5-6 6-10v-2l1 2 1-1 1-3z" class="R"></path><path d="M342 72l1-1h3l-3 4-1-1 1-2h-1z" class="s"></path><path d="M588 311c1 1 1 2 2 3 5 3 12 4 17 5 2 1 5 2 7 2 1 1 2 2 2 3l1 2v3h0l-25 1h-8c-2 0-3 1-5 0 0 1-1 1-2 1-2 1-6 2-8 1l-2 3-2 2h0c-2 1-6 2-7 2v-1c0-2 2-3 3-4l-1-2h-2 2c0-2-2-3-3-4v-4c2 0 3 1 5 1 1-1 2-7 2-8h0l2 5c0 2 1 4 4 5 2 1 5 1 8 0 2 0 5-2 6-4 2-3 4-6 4-10v-2h0z" class="j"></path><path d="M576 330h3c0 1-1 1-2 1-2 1-6 2-8 1-1-1-1-1-3-1 2-1 8-1 10-1z" class="Y"></path><path d="M566 331c2 0 2 0 3 1l-2 3-2 2h0c-2 1-6 2-7 2v-1c0-2 2-3 3-4h0c1-1 2-2 3-2 0 0 1 0 2-1h0z" class="M"></path><path d="M558 338c2-1 4-1 6-2v-1c1 1 1 1 1 2h0c-2 1-6 2-7 2v-1z" class="V"></path><path d="M566 331c2 0 2 0 3 1l-2 3-2 2c0-1 0-1-1-2l2-4h0z" class="o"></path><path d="M564 317h0l2 5c-1 1-2 1-1 3v5c-1 0-1 1-1 1-2 1-3 1-4 1 0-2-2-3-3-4v-4c2 0 3 1 5 1 1-1 2-7 2-8z" class="D"></path><defs><linearGradient id="f" x1="598.023" y1="324.606" x2="600.09" y2="332.703" xlink:href="#B"><stop offset="0" stop-color="#323433"></stop><stop offset="1" stop-color="#504c4f"></stop></linearGradient></defs><path fill="url(#f)" d="M616 326h1v3h0l-25 1h-8c-2 0-3 1-5 0h-3c2 0 3 0 4-1 2-2 31-2 35-2l1-1z"></path><path d="M590 314c5 3 12 4 17 5 2 1 5 2 7 2 1 1 2 2 2 3l1 2h-1c-2-1-6-1-8-1l-13 1h-9-1c2-2 6-8 5-11v-1z" class="V"></path><path d="M545 206s0-1-1-2v-1l1-1 5 6 3 2v-1c1 1 2 1 2 1l2 1c1 0 2 1 2 1 1-1 1-1 1-2h2l-1-1v-3h2c1 0 2 1 3 1l1-1 1-1 6 3h0l1-1 1 1c1 0 2 1 3 2 1 4 2 8 0 12-1 2-2 4-4 6-1 1-1 2-2 3-1 0-1 0-2-1l1-1h0-1c-1 1-2 0-4 0h0 0-3 0v-1l-4-1h-2c-2-1-3-1-4-2l-1 1v1 1c-1 4-2 7-3 11-1 1-2 1-3 2l-1 1 2-4c1-4 2-7 2-10 1-2 0-4 1-7v-1h-1c1 0 1 0 2-1v-1c0-1 0-1-1-2 1-1 1-1 2-1h0c-1-3-6-6-8-9z" class="F"></path><path d="M563 224l3-2v2c0 1-1 2-1 3-1-2-1-2-2-3z" class="N"></path><path d="M561 226l2-2c1 1 1 1 2 3l-1 1-4-1 1-1z" class="C"></path><path d="M574 208l1-1 1 1 1 2c2 3 2 5 1 8-1 4-4 7-8 9-1 1-1 1-3 1v-1c1-3 9-5 8-9h-1 0c1-1 1-2 1-2 1-3 0-5-1-6l1-1-1-1z" class="W"></path><path d="M574 208l1-1 1 1 1 2v1h-1l-1-1v-1l-1-1z" class="q"></path><path d="M568 205l6 3h0l1 1-1 1c1 1 2 3 1 6 0 0 0 1-1 2h0c-2 3-4 6-8 8l2-7h0c0-2 0-3 1-5-2-2-2-2-4-3-1-1-2-1-3-1l-1-1v-3h2c1 0 2 1 3 1l1-1 1-1z" class="n"></path><path d="M568 205l6 3h0l1 1-1 1c-2-2-5-3-8-3l1-1 1-1z" class="Y"></path><defs><linearGradient id="g" x1="548.384" y1="214.365" x2="555.161" y2="207.562" xlink:href="#B"><stop offset="0" stop-color="#bcbaba"></stop><stop offset="1" stop-color="#e4e3e4"></stop></linearGradient></defs><path fill="url(#g)" d="M545 206s0-1-1-2v-1l1-1 5 6 3 2v-1c1 1 2 1 2 1l2 1c1 0 2 1 2 1 1-1 1-1 1-2h2c1 0 2 0 3 1 2 1 2 1 4 3-1 2-1 3-1 5h0-1c-1 1-1 2-1 3h0l-3 2-2 2-1 1h-2c-2-1-3-1-4-2l-1 1v1 1c-1 4-2 7-3 11-1 1-2 1-3 2l-1 1 2-4c1-4 2-7 2-10 1-2 0-4 1-7v-1h-1c1 0 1 0 2-1v-1c0-1 0-1-1-2 1-1 1-1 2-1h0c-1-3-6-6-8-9z"></path><path d="M551 216c1-1 1-1 2-1 1 1 1 2 0 3l-1 1v-1c0-1 0-1-1-2z" class="F"></path><path d="M562 217l2-1h0c0 2-2 4-4 6h0l-1-1c0 1-2 1-3 2 0 0-2 0-2-1-1-1 0-1-1-2h1s0 1 1 1l6-1c0-1 1-2 1-3z" class="m"></path><path d="M562 210c1 0 2 0 3 1v5h-1 0l-2 1h0c0-1 0-1 1-2-1 0-1 0-2-1h0c0-1-1-1-2-2 1-1 1-1 1-2h2z" class="l"></path><path d="M562 210v4h-1c0-1-1-1-2-2 1-1 1-1 1-2h2z" class="O"></path><path d="M565 211c2 1 2 1 4 3-1 2-1 3-1 5h0-1c-1 1-1 2-1 3h-2c-2 0-3 2-4 1v-1c2-2 4-4 4-6h1v-5z" class="p"></path><path d="M553 220c1 1 0 1 1 2 0 1 2 1 2 1 1-1 3-1 3-2l1 1h0v1c1 1 2-1 4-1h2 0l-3 2-2 2-1 1h-2c-2-1-3-1-4-2l-1 1v1 1c-1 4-2 7-3 11-1 1-2 1-3 2l-1 1 2-4c1-4 2-7 2-10 1-2 0-4 1-7 1 0 1-1 2-1z" class="c"></path><path d="M560 222v1c1 1 2-1 4-1h2 0l-3 2-2 2-7-1c2-1 4-1 6-3h0 0z" class="i"></path><path d="M553 220c1 1 0 1 1 2 0 1 2 1 2 1h-3c-1 1-1 3-1 5-1 3-2 7-4 10 1-4 2-7 2-10 1-2 0-4 1-7 1 0 1-1 2-1z" class="K"></path><defs><linearGradient id="h" x1="573.62" y1="326.479" x2="568.979" y2="307.809" xlink:href="#B"><stop offset="0" stop-color="#787778"></stop><stop offset="1" stop-color="#adabac"></stop></linearGradient></defs><path fill="url(#h)" d="M573 297c2 1 5 0 7 1h3v3 3 1l3 3 1 1c1 1 1 1 1 2h0v2c0 4-2 7-4 10-1 2-4 4-6 4-3 1-6 1-8 0-3-1-4-3-4-5l-2-5h0c-2 0-2-1-3-3h-2l-2-2c-1 0-1-1-1-1l2-4 5-1c2-1 5-1 7-3 2-1 2-3 2-5l1-1z"></path><path d="M567 319v-3h1v1h0c1-1 1-2 2-3l1-1h0c1-1 2-2 4-2v-1h4c1 1 1 1 2 1 2 2 2 3 2 5l-1 1v-2c-1 0-1 0-1-1-1-1-2-1-3-2-1 1-2 1-2 1-1 0-2 1-3 2h-1 0c-1 2-1 2-1 4v2c-1-1-1-2-2-3h-1l-1 1z" class="f"></path><path d="M563 306c2-1 5-1 7-3 2-1 2-3 2-5l1-1c1 2 1 4 0 5 0 1-1 2-2 3h0c-2 2-5 6-6 8h-1 0c-2-1-4-1-5-2 0-1 0-2 1-3l3-2z" class="q"></path><path d="M573 297c2 1 5 0 7 1h3v3 3 1h0-5l-1-1c-2 1-4 2-6 1 1-1 2-2 2-3 1-1 1-3 0-5z" class="B"></path><path d="M580 298h3v3 3 1h0-5l-1-1h2l-1-1c0-2 0-4 1-5h1z" class="K"></path><path d="M578 312c1 1 2 1 3 2 0 1 0 1 1 1v2l1-1c0-1 0-2 1-3 0 3 0 5-1 7-2 1-3 3-5 3-2 1-4 1-5 0s-1-1-1-2h-1v-2c0-2 0-2 1-4h0 1c1-1 2-2 3-2 0 0 1 0 2-1z" class="D"></path><path d="M578 312c1 1 2 1 3 2 0 1 0 1 1 1-1 2-1 3-1 4h-1-2-1c-2 1-3 0-4-1s-1-1-1-3h1c1-1 2-2 3-2 0 0 1 0 2-1z" class="Q"></path><path d="M578 312c1 1 2 1 3 2 0 1 0 1 1 1-1 2-1 3-1 4h-1-2c1-2 2-2 2-4 0-1-1-2-2-3z" class="S"></path><path d="M576 313c1 1 1 2 2 3 0 1 0 1-1 2h-2-2c-1-1-1-1-1-3h1c1-1 2-2 3-2z" class="l"></path><path d="M578 305h5 0l3 3 1 1c1 1 1 1 1 2h0v2c0 4-2 7-4 10-1 2-4 4-6 4l-5-1h0l-2-1c-3-2-3-4-4-6l1-1h1c1 1 1 2 2 3h1c0 1 0 1 1 2s3 1 5 0c2 0 3-2 5-3 1-2 1-4 1-7-1-1-1-1-2-1-1-2-2-3-3-3h-2v-1c0-2 0-2 1-3z" class="Q"></path><path d="M573 326c3 0 5 0 8-2 2-3 4-6 5-9 1 0 1-2 1-2v-4h0c1 1 1 1 1 2h0v2c0 4-2 7-4 10-1 2-4 4-6 4l-5-1z" class="X"></path><path d="M570 193l2 3h0c2 2 4 3 5 5 0-1 1-1 1-1l8 5-1 1c3 4 5 7 7 11 1 2 2 4 2 5 0 3 0 6-1 8h0l-1 2v-1-2l-1-1h-3v-1c-1 1-1 2-1 3h0c0 1-1 1-2 1 1-2 1-5 1-7 0-6-2-11-6-15l-1 1c-1-1-2-2-3-2l-1-1-1 1h0l-6-3-1 1-1 1c-1 0-2-1-3-1h-2v3l1 1h-2c0 1 0 1-1 2 0 0-1-1-2-1l-2-1s-1 0-2-1v1l-3-2-5-6s-2-1-2-2l-2-2v-1l-1-1c1-1 1-2 2-2v1h0 1c1 0 2-1 3-1l1 1h0 2c2-1 4-1 6-2l2 1h2l5 2c1 0 1 0 1-1h3 1l-1-1 2-1z" class="l"></path><path d="M589 218c0 1 0 2 1 3 0 0 1 2 0 3 0 1 0 1-1 2h-1 0c0-1 1-2 1-3v-5z" class="i"></path><path d="M577 201c0-1 1-1 1-1l8 5-1 1c-3-2-6-3-9-5h1z" class="a"></path><path d="M570 193l2 3h0c2 2 4 3 5 5h-1c-4-2-9-3-13-3h-2l1-1h-2c-1 0-1-1-1-2v-1l5 2c1 0 1 0 1-1h3 1l-1-1 2-1z" class="c"></path><path d="M570 193l2 3c-2 1-3 0-4-1h1l-1-1 2-1z" class="a"></path><path d="M559 194l5 2h2v1 1l-4-1h-2c-1 0-1-1-1-2v-1zm4 8h5c7 2 16 5 19 12l2 4v5c0 1-1 2-1 3v1c-1 1-1 2-1 3h0c0 1-1 1-2 1 1-2 1-5 1-7 0-6-2-11-6-15l-1 1c-1-1-2-2-3-2l-1-1-1 1h0l-6-3h0v-1h3c-1 0-3-1-4-1h-4v-1z" class="O"></path><path d="M568 205v-1h3c3 1 6 2 9 5l-1 1c-1-1-2-2-3-2l-1-1-1 1h0l-6-3h0z" class="W"></path><path d="M561 200c3 0 5 1 7 2h-5v1h4c1 0 3 1 4 1h-3v1h0l-1 1-1 1c-1 0-2-1-3-1h-2v3l1 1h-2c0 1 0 1-1 2 0 0-1-1-2-1l-2-1s-1 0-2-1v1l-3-2c1-1 2-1 2-2v-1l-2 1-1-1 1-1c2-2 8-3 11-4z" class="a"></path><path d="M559 205l1 2c-1 2-1 3-3 4h0l-2-1s-1 0-2-1c2-1 4-2 6-4z" class="p"></path><path d="M561 200c3 0 5 1 7 2h-5c-4 0-8 2-11 3-1 0-1 0-2-1 2-2 8-3 11-4z" class="d"></path><path d="M567 203c1 0 3 1 4 1h-3v1h0l-1 1-1 1c-1 0-2-1-3-1h-2v3l1 1h-2c0 1 0 1-1 2 0 0-1-1-2-1h0c2-1 2-2 3-4l-1-2h0c2-2 5-2 8-2z" class="i"></path><path d="M562 205h6 0l-1 1-1 1c-1 0-2-1-3-1l-1-1z" class="B"></path><path d="M560 207v-1c1-1 1-1 2-1l1 1h-2v3l1 1h-2c0 1 0 1-1 2 0 0-1-1-2-1h0c2-1 2-2 3-4z" class="c"></path><path d="M549 195c2-1 4-1 6-2l2 1h2v1c0 1 0 2 1 2h2l-1 1h2l4 1c-1 0-1 0-2 1h-4c-3 1-9 2-11 4l-1 1 1 1 2-1v1c0 1-1 1-2 2l-5-6s-2-1-2-2l-2-2v-1l-1-1c1-1 1-2 2-2v1h0 1c1 0 2-1 3-1l1 1h0 2z" class="n"></path><path d="M557 194h2v1c0 1 0 2 1 2h2l-1 1c-2 0-4-1-6-2l2-2z" class="b"></path><path d="M549 195c2-1 4-1 6-2l2 1-2 2c-3 0-6 1-8 3 0-1 1-2 0-4h2z" class="a"></path><path d="M540 196c1-1 1-2 2-2v1h0 1c1 0 2-1 3-1l1 1h0c1 2 0 3 0 4l-2 2 4 4 1 1 2-1v1c0 1-1 1-2 2l-5-6s-2-1-2-2l-2-2v-1l-1-1z" class="F"></path><path d="M541 198v-1l1-1v1h3v1c0 1-1 1-2 2l-2-2z" class="b"></path><path d="M540 196c1-1 1-2 2-2v1h0 1c1 0 2-1 3-1l1 1c-1 1-2 1-3 1h0c-1 1-1 1-2 1v-1l-1 1v1-1l-1-1z" class="L"></path><path d="M374 641c2 1 2 2 3 3v-1l3 4c0 1 1 2 1 3l-1 1-5 11c-1 4-3 8-6 11l-2 2v1 1c-2 3-3 5-5 6h-1 0c-2-3-2-6-5-9v-1c-1-1-2-1-2-3l-2 1v-1c-2-5-4-10-7-15 0-1-1-3-1-3s-1 0-1-1c0 0 1 0 1-1h0c1 0 1-1 2-1h1l-1-1v-2l1 1h2c1 1 2 1 3 1l6 10c2 2 3 4 4 6h1v-1c1-2 2-3 3-5l6-10c0-1 2-3 3-5 0 0-1-1-1-2z" class="V"></path><path d="M354 665h0c1-2 1-3 1-4 1 1 2 2 2 4h0c-1 0-2 0-2 1l-1 2v2l-2 1v-1c1 0 1-1 1-2l1-3z" class="B"></path><path d="M366 675h1v1 1c-2 3-3 5-5 6h-1v-1c1-2 3-5 5-7z" class="W"></path><path d="M354 668c2 3 8 12 7 14v1h0c-2-3-2-6-5-9v-1c-1-1-2-1-2-3v-2z" class="D"></path><path d="M377 643l3 4c0 1 1 2 1 3h-1-1c-1 1-2 1-2 2-1 1-1 1-2 0 1-2 3-5 2-8v-1z" class="g"></path><path d="M347 649l-1-1v-2l1 1c3 5 8 12 7 18l-1 3v-1-1c1-4-1-7-3-11l-3-6z" class="D"></path><path d="M372 648h0c1-1 2-1 2-3l1-2c1 1 2 2 2 3s0 0-1 1v1c-1 0-1 0-2 1s-1 3-2 4l-5 8c-1 2-1 4-3 5-1-1-1-2-1-3 1-2 2-3 3-5l6-10z" class="B"></path><path d="M344 650c1 0 1-1 2-1h1l3 6c2 4 4 7 3 11v1 1c0 1 0 2-1 2-2-5-4-10-7-15 0-1-1-3-1-3s-1 0-1-1c0 0 1 0 1-1h0z" class="N"></path><path d="M377 652c0-1 1-1 2-2h1 1l-1 1-5 11c-1 4-3 8-6 11l-2 2h-1v-1-3c2-3 3-6 5-10 0-1 1-3 2-4 0-2 1-3 2-5 1 1 1 1 2 0z" class="D"></path><path d="M375 652c1 1 1 1 2 0l-3 5h-1c0-2 1-3 2-5z" class="j"></path><path d="M373 657h1l-3 6v-1-1c0-1 1-3 2-4z" class="e"></path><defs><linearGradient id="i" x1="592.999" y1="279.828" x2="593.57" y2="296.211" xlink:href="#B"><stop offset="0" stop-color="#807f7f"></stop><stop offset="1" stop-color="#b0afaf"></stop></linearGradient></defs><path fill="url(#i)" d="M610 277h0c1 1 1 2 1 3h9c-1 4-3 7-5 10h0l-1 1v1l-2 2-1 1c0 1 0 1-1 2h0-1c-5 1-11 0-16 0l-15-1h-2c0-1 0-1-1-2 2-2 1-3 2-5l3-9-1-1h1 7c3 1 21 2 22 0l1-2z"></path><path d="M580 280h2c0 1-1 3-1 4-2 3-3 6-3 9l-1 2 1 1h-2c0-1 0-1-1-2 2-2 1-3 2-5l3-9z" class="B"></path><defs><linearGradient id="j" x1="615.057" y1="279.437" x2="604.645" y2="296.831" xlink:href="#B"><stop offset="0" stop-color="#838282"></stop><stop offset="1" stop-color="#a7a7a7"></stop></linearGradient></defs><path fill="url(#j)" d="M611 280h9c-1 4-3 7-5 10h0l-1 1v1l-2 2-1 1c0 1 0 1-1 2h0-1c-5 1-11 0-16 0 3-1 6-1 9 0v-4-1s0-1 1-2h0 1c0-1 1-1 2-2s3-3 3-4c1-1 1-1 1-2 1 0 1-1 1-2z"></path><defs><linearGradient id="k" x1="418.246" y1="139.372" x2="396.758" y2="102.27" xlink:href="#B"><stop offset="0" stop-color="#b9b9b9"></stop><stop offset="1" stop-color="#e8e6e7"></stop></linearGradient></defs><path fill="url(#k)" d="M434 83c2-1 3-2 5-2v1l-1 2c3 0 4-2 7-2l1 1h1l-2 1h-1l-3 3c-2 1-4 3-6 5l-1 2c0 1 0 1-1 1 0 1-1 2-1 3s-1 1-1 2c-2 2-3 6-5 8h-1l1 1h1c-1 1-1 2-2 3h0l-1-1-5 6v-1l-2 2-1 1-1-1c-3 0-5 3-7 6-2 2-3 5-5 7-1 3-3 6-4 8-1 4 0 9 1 12v4c0 1 1 1 1 1v2c-1-1-1-3-2-4v-3c-1 0-2-2-2-3-1-1-2-4-2-6 0-1 1-3 2-4 1-2 3-4 4-7h0c1-1 1-2 0-3v-2-2c-1-2 0-3 0-5l1-2c0-2 0-3-1-5l-2-1c-1-1-3-1-4-3 0-1-1-2-2-3h0l2-2 1 2c1 0 2 0 2-1l2 1h1c1 0 2 1 3 1 0 1-1 1-1 2v1c1 0 1 0 1-1 1 0 2-1 2-1 1 0 1 1 2 2h1 0v1c0 1 1 1 2 1l3-2c0-2 0-3 1-5h0c0-1 0-2 1-2 0-2-1 0 0-1 0-1 2-2 3-2h0v-2c1-4 0-7 3-9l1-3h0c2-1 2-1 3-2 0 1 1 1 2 2l2-1 1 1c1-1 2-2 3-2z"></path><path d="M405 110v1h2l-3 6v-1h0v-1h0v-3l1-2z" class="m"></path><path d="M404 108c1 0 2-1 2-1 1 0 1 1 2 2h1 0v1h-1l-1 1h-2v-1l-1 2-2-3h1c1 0 1 0 1-1z" class="U"></path><path d="M405 110h2c0-1 0-1 1-1v1l-1 1h-2v-1z" class="X"></path><path d="M398 104l2 1h1c1 0 2 1 3 1 0 1-1 1-1 2v1h-1c-2 0-4-2-6-4 1 0 2 0 2-1z" class="a"></path><path d="M421 110h1l1-1c1 0 1-1 3-1h-1l1 1h1c-1 1-1 2-2 3h0l-1-1-5 6v-1l-2 2-2-2-1 1h0c0-1 2-2 3-3 1-2 2-3 4-4h0z" class="Q"></path><path d="M421 110v2c-1 1-2 1-3 2h-1c1-2 2-3 4-4z" class="U"></path><path d="M419 116c1-1 3-3 4-5 0-1 2-2 2-3l1 1h1c-1 1-1 2-2 3h0l-1-1-5 6v-1z" class="i"></path><path d="M415 104h0c0-1 0-2 1-2 0-2-1 0 0-1 0-1 2-2 3-2 0 1-1 2-1 3h1c0 1-1 2-1 3v1s1 0 1 1c-3 4-12 14-17 16 3-5 6-8 9-12l3-2c0-2 0-3 1-5z" class="L"></path><g class="d"><path d="M415 104h0c0-1 0-2 1-2 0-2-1 0 0-1 0-1 2-2 3-2 0 1-1 2-1 3-1 2-2 5-4 7 0-2 0-3 1-5z"></path><path d="M426 83c0 1 1 1 2 2l2-1 1 1c1-1 2-2 3-2 0 1-1 2-2 3h0l-1 2-2 2c-3 3-5 5-6 9-1 2-2 5-4 8 0-1-1-1-1-1v-1c0-1 1-2 1-3h-1c0-1 1-2 1-3h0v-2c1-4 0-7 3-9l1-3h0c2-1 2-1 3-2z"></path></g><path d="M426 83c0 1 1 1 2 2-2 1-4 3-6 3l1-3h0c2-1 2-1 3-2z" class="G"></path><path d="M434 83c0 1-1 2-2 3h0l-1 2-2 2h-1l2-2v-1h-2v-1l2-1h1c1-1 2-2 3-2z" class="a"></path><path d="M420 99c1-3 3-6 4-8 1-1 1-2 2-3 1 0 3-1 4 0l-2 2h1c-3 3-5 5-6 9-1 2-2 5-4 8 0-1-1-1-1-1v-1c0-1 1-2 1-3h-1c0-1 1-2 1-3h0v-2c1 1 1 1 1 2z" class="W"></path><path d="M428 90h1c-3 3-5 5-6 9-1 2-2 5-4 8 0-1-1-1-1-1v-1c0-1 1-2 1-3h-1c0-1 1-2 1-3h0v-2c1 1 1 1 1 2 0 2-1 3-1 4h1c1-2 2-6 4-8 1-2 3-4 4-5z" class="K"></path><path d="M434 83c2-1 3-2 5-2v1l-1 2c3 0 4-2 7-2l1 1h1l-2 1h-1l-3 3c-2 1-4 3-6 5l-1 2c0 1 0 1-1 1 0 1-1 2-1 3s-1 1-1 2c-2 2-3 6-5 8-2 0-2 1-3 1l-1 1h-1 0-1v-1c0-1 1-2 2-2l1-3c0-6 4-10 8-15v-1l1-2h0c1-1 2-2 2-3z" class="U"></path><path d="M438 84c3 0 4-2 7-2l1 1-4 2h-1c-3 1-5 4-7 6-4 5-6 11-10 15-1 0-1 1-2 1l1-3c3-4 5-8 8-12 2-2 5-4 7-7h-1-2v-1l2-1 1 1z" class="I"></path><path d="M434 83c2-1 3-2 5-2v1l-1 2-1-1-2 1v1h2 1c-2 3-5 5-7 7-3 4-5 8-8 12 0-6 4-10 8-15v-1l1-2h0c1-1 2-2 2-3z" class="f"></path><path d="M434 83c2-1 3-2 5-2v1l-1 2-1-1-2 1v1h2l-1 1-3 2c0-1 0-1 1-2h0-2 0c1-1 2-2 2-3z" class="L"></path><defs><linearGradient id="l" x1="416.988" y1="146.513" x2="400.993" y2="129.67" xlink:href="#B"><stop offset="0" stop-color="#b0aeb0"></stop><stop offset="1" stop-color="#e5e4e4"></stop></linearGradient></defs><path fill="url(#l)" d="M419 117l5-6 1 1c0 1 0 2-1 3 0 1-1 2-1 3-1 1-1 2-2 4v2 2h0l1 1c0 1 0 1-1 2v2 3c0 1 0 2-1 3v3c0 1 0 1-1 2v1c0 4-3 9-5 12-1 0-1 1-1 2l-2 2h0l-3 4-6 10-1-1v-2h0c-1-2-2-3-3-5l1-1h1c3-2 0-3 1-5v-1-2s-1 0-1-1v-4c-1-3-2-8-1-12 1-2 3-5 4-8 2-2 3-5 5-7 2-3 4-6 7-6l1 1 1-1 2-2v1z"></path><path d="M416 119c0 1-1 2-2 3-1 2-2 3-3 5s-2 5-3 7c-1 1-2 3-2 3-1 2-2 5-2 7 0-1 0-2-1-3 1-6 5-10 8-15 1-2 3-5 5-7z" class="O"></path><path d="M403 141c1 1 1 2 1 3-1 5 1 8 4 13 0 1 1 2 1 2 0 1 0 2-1 2v2l-6 10-1-1v-2h0c-1-2-2-3-3-5l1-1h1c3-2 0-3 1-5l2 2 3 2c-1-4-4-9-4-14 0-3 0-6 1-8z" class="Z"></path><path d="M401 159l2 2h0v3c-2 2-2 4-2 6-1-2-2-3-3-5l1-1h1c3-2 0-3 1-5z" class="N"></path><path d="M411 127h1c-2 8-6 15-2 23l1 1 1 4c0 1 0 1 1 2l-2 2h0l-3 4v-2c1 0 1-1 1-2 0 0-1-1-1-2-3-5-5-8-4-13 0-2 1-5 2-7 0 0 1-2 2-3 1-2 2-5 3-7z" class="l"></path><path d="M404 144c0-2 1-5 2-7 0 6 0 11 3 16 0 2 2 4 2 6h0l-3 4v-2c1 0 1-1 1-2 0 0-1-1-1-2-3-5-5-8-4-13z" class="I"></path><defs><linearGradient id="m" x1="424.783" y1="136.221" x2="412.595" y2="128.266" xlink:href="#B"><stop offset="0" stop-color="#a8a6a9"></stop><stop offset="1" stop-color="#d1d1ce"></stop></linearGradient></defs><path fill="url(#m)" d="M419 117l5-6 1 1c0 1 0 2-1 3 0 1-1 2-1 3-1 1-1 2-2 4v2 2h0l1 1c0 1 0 1-1 2v2 3c0 1 0 2-1 3v3c0 1 0 1-1 2v1c0 4-3 9-5 12-1 0-1 1-1 2-1-1-1-1-1-2l-1-4-1-1c-4-8 0-15 2-23h-1c1-2 2-3 3-5 1-1 2-2 2-3h0l1-1 2-2v1z"></path><path d="M416 128c1-1 2-6 3-7l-3 13v-1c-1-1 0-3 0-5z" class="F"></path><defs><linearGradient id="n" x1="408.894" y1="141.174" x2="417.127" y2="138.255" xlink:href="#B"><stop offset="0" stop-color="#71716f"></stop><stop offset="1" stop-color="#a09da1"></stop></linearGradient></defs><path fill="url(#n)" d="M411 140c2-4 3-8 5-12 0 2-1 4 0 5v1c0 5 0 10-1 15l-2 4v2h-1l-1-4v-7-4z"></path><path d="M414 145c0 1 0 1 1 3v1l-2 4h-1c0-2 0-4 1-6v-1l1-1z" class="O"></path><path d="M416 133v1c0 5 0 10-1 15v-1c-1-2-1-2-1-3h0l2-12z" class="b"></path><path d="M419 116v1 1h0c0 1 0 1 1 2v1h-1c-1 1-2 6-3 7-2 4-3 8-5 12v4 7l-1-1c-4-8 0-15 2-23h-1c1-2 2-3 3-5 1-1 2-2 2-3h0l1-1 2-2z" class="f"></path><path d="M410 140l1 1v-1 4l-1-1v-3z" class="i"></path><path d="M419 118h0c0 1 0 1 1 2v1h-1c-1 1-2 6-3 7-2 4-3 8-5 12v1l-1-1c1-5 3-10 5-15 1-2 3-4 3-6 1 0 1-1 1-1z" class="Q"></path><path d="M155 202c2-2 6-2 9-2l11 4c-1 1-2 1-3 2v1 1l3-3 2 1-5 5-3 4-1 2c1 0 1 1 1 1l5 2h0c-1 1-1 1-1 2l-1 3h0v2c-1 1-1 3-1 4v-4c-2 2-4 4-7 6-2 1-6 3-8 5-1 1-2 3-3 4h0v-2c0-2 0-2 1-4h-1l3-6-1-1v-1h0c-2-2-4-2-6-3h0c-1-1-2-2-3-4v-1c-1-1-1-3 0-4 0-3-1-5 0-7-1 0-2 0-2 1l-1-1-2 3c-1-1-1-1-2-1v-1c5-4 10-6 16-8z" class="P"></path><path d="M165 216h2v1 1h-1c0 1-1 1-1 3l2 1c-1 0-1 0-2-1-1-2 0-3 0-5z" class="f"></path><path d="M168 214c0-1 3-3 4-3l-3 4-1 2c1 0 1 1 1 1v2l-2-2v-1-1h-2l-2 2c-1 0-2 0-3-1v-1h0c1 0 1 0 1 1l3-2c1 0 2-1 4-1z" class="U"></path><path d="M169 218l5 2h0c-1 1-1 1-1 2l-1 3h0c0-1-1-1-2-1h-3l-2-1h2 2l-2-1-2-1c0-2 1-2 1-3h1l2 2v-2z" class="X"></path><path d="M169 218l5 2h0c-1 1-1 1-1 2-2-1-3-1-4-2v-2z" class="P"></path><path d="M159 214c0 1 0 5 1 6l1 1 1 1c1 0 2 0 3 1l2 1-2 2h-1-1c-1 1-2 1-3 2v-1-1c-1-2-3-2-4-5v-2h0l1 2c2-2 2-4 2-6v-1z" class="U"></path><path d="M162 222c1 0 2 0 3 1l2 1-2 2c-1-2-2-3-3-4z" class="a"></path><path d="M156 221v-2h0l1 2c1 3 3 3 6 5-1 1-2 1-3 2v-1-1c-1-2-3-2-4-5z" class="M"></path><path d="M151 221h3 1c1-1 0-3 0-4v-1c1-1 1-2 2-3 1 0 2 0 3-1l-1-1h0l4-1c-1 1-1 2 0 3h1l3-1 1 1v1c-2 0-3 1-4 1l-3 2c0-1 0-1-1-1 1-1 1-2 0-3h0l-1 1v1c0 2 0 4-2 6l-1-2h0v2 2c-2 0-3-1-5-2z" class="p"></path><path d="M149 220c-1-2-2-4-2-6s0-4 2-5h0c-1 2-1 4-1 7 1 2 2 4 3 5 2 1 3 2 5 2v-2c1 3 3 3 4 5v1 1c1-1 2-1 3-2h1l-1 1 1 1-3 2h-3c-1 0-1-1-2-1s-1 0-1-1h0c-2-2-4-2-6-3h0c-1-1-2-2-3-4v-1c-1-1-1-3 0-4 0 1 1 3 1 4h2z" class="L"></path><path d="M156 226h4v1 1h-3-1v-2z" class="C"></path><path d="M149 220l3 3c1 0 4 2 4 3h-1 0c-4-1-5-2-8-6h2z" class="W"></path><path d="M149 220c-1-2-2-4-2-6s0-4 2-5h0c-1 2-1 4-1 7 1 2 2 4 3 5 2 1 3 2 5 2v-2c1 3 3 3 4 5h-4-1 1c0-1-3-3-4-3l-3-3z" class="K"></path><path d="M167 224h3c1 0 2 0 2 1v2c-1 1-1 3-1 4v-4c-2 2-4 4-7 6-2 1-6 3-8 5-1 1-2 3-3 4h0v-2c0-2 0-2 1-4h-1l3-6-1-1v-1c0 1 0 1 1 1s1 1 2 1h3l3-2-1-1 1-1h1l2-2z" class="l"></path><path d="M167 224h3c1 0 2 0 2 1v2c-1 1-1 3-1 4v-4-1c-1-1-2 0-3 0s-1 1-2 1l-2 1-1-1 1-1h1l2-2z" class="I"></path><path d="M153 236l3-6-1-1v-1c0 1 0 1 1 1s1 1 2 1h3l-4 3-3 3h-1z" class="f"></path><path d="M149 209c2-3 5-4 8-4 4-1 5 0 7 2l1 2c0 1 1 2 0 3l-1 1h-1c-1-1-1-2 0-3l-4 1h0l1 1c-1 1-2 1-3 1-1 1-1 2-2 3v1c0 1 1 3 0 4h-1-3c-1-1-2-3-3-5 0-3 0-5 1-7h0z" class="n"></path><path d="M149 209c2-3 5-4 8-4 4-1 5 0 7 2l1 2c0 1 1 2 0 3l-1 1h-1c-1-1-1-2 0-3v-2c-1-1-1-2-2-2-4-1-9 1-12 3h0z" class="O"></path><defs><linearGradient id="o" x1="163.144" y1="203.068" x2="144.812" y2="211.096" xlink:href="#B"><stop offset="0" stop-color="#b7b5b4"></stop><stop offset="1" stop-color="#dedcde"></stop></linearGradient></defs><path fill="url(#o)" d="M155 202c2-2 6-2 9-2l11 4c-1 1-2 1-3 2v1 1l3-3 2 1-5 5c-1 0-4 2-4 3v-1l-1-1-3 1 1-1c1-1 0-2 0-3l-1-2c-2-2-3-3-7-2-3 0-6 1-8 4-2 1-2 3-2 5s1 4 2 6h-2c0-1-1-3-1-4 0-3-1-5 0-7-1 0-2 0-2 1l-1-1-2 3c-1-1-1-1-2-1v-1c5-4 10-6 16-8z"></path><path d="M155 202c5 0 8 1 12 3h-1c-5-2-8-3-13-1h-1c-1 0-3 1-4 2s-1 2-2 3h0c-1 0-2 0-2 1l-1-1-2 3c-1-1-1-1-2-1v-1c5-4 10-6 16-8z" class="O"></path><path d="M143 209c1-2 3-2 5-3-1 1-1 2-2 3h0c-1 0-2 0-2 1l-1-1z" class="Q"></path><path d="M167 205c1 0 1 0 2 1h1c1 0 1 1 2 1v1l3-3 2 1-5 5c-1 0-4 2-4 3v-1l-1-1-3 1 1-1c1-1 0-2 0-3l-1-2h1 4c-1-1-2-1-3-2h1z" class="l"></path><path d="M165 209l3 2c-1 0-1 1-1 1l-3 1 1-1c1-1 0-2 0-3z" class="U"></path><path d="M167 205c1 0 1 0 2 1h1c1 0 1 1 2 1v1c-1 0-1 1-2 2-1-1-1-2-1-3-1-1-2-1-3-2h1z" class="I"></path><path d="M155 202c2-2 6-2 9-2l11 4c-1 1-2 1-3 2v1c-1 0-1-1-2-1h-1c-1-1-1-1-2-1-4-2-7-3-12-3z" class="i"></path><path d="M563 341c2 0 4 0 6 1 6 1 13 3 16 8 2 3 2 8 1 11 0 3-2 5-4 8 0 0-1 1-1 2l-1 1c-1 1-2 1-3 1h-4l-2 1c-2 0-4 0-5-1v-1l-2-2c-4-3-5-5-6-10l-1-2h0c-1 1-2 5-4 6 0-1-1-2 0-3-1-3 0-6 1-8v-2h0c2-2 4-4 7-5 0-1 2-1 2-2l-2-1h-2 0l4-2z" class="n"></path><path d="M573 373h3c2-2 4-3 6-4 0 0-1 1-1 2l-1 1c-1 1-2 1-3 1h-4 0z" class="f"></path><path d="M565 370c3 2 5 3 8 3h0l-2 1c-2 0-4 0-5-1v-1l-2-2h1z" class="H"></path><path d="M571 349h3c2 3 4 5 4 9 0 3-1 4-3 6 1-3 3-5 2-9-1-2-3-3-5-3l1-1h0l-2-2z" class="O"></path><path d="M561 343h3c2 1 3 2 4 2 2 0 4 3 6 4h-3 0c-2-1-4-1-6-1 0 0 0-1-1-1h1l1-1c-1-1-4 1-5 0 0-1 2-1 2-2l-2-1z" class="K"></path><path d="M565 348c2 0 4 0 6 1h0l2 2h0l-1 1c-5-1-7 0-10 3-2 3-3 6-1 10 1 2 2 4 4 5h-1c-4-3-5-5-6-10v-6c2-3 4-5 7-6z" class="R"></path><path d="M554 351h0c2-2 4-4 7-5 1 1 4-1 5 0l-1 1h-1c1 0 1 1 1 1-3 1-5 3-7 6v6l-1-2h0c-1 1-2 5-4 6 0-1-1-2 0-3-1-3 0-6 1-8v-2z" class="i"></path><defs><linearGradient id="p" x1="555.831" y1="349.359" x2="558.047" y2="351.121" xlink:href="#B"><stop offset="0" stop-color="#5e5e5f"></stop><stop offset="1" stop-color="#767475"></stop></linearGradient></defs><path fill="url(#p)" d="M554 351h0c2-2 4-4 7-5 1 1 4-1 5 0l-1 1h-1c-5 2-8 4-10 9 0 1 0 3-1 5-1-3 0-6 1-8v-2z"></path><defs><linearGradient id="q" x1="400.921" y1="138.697" x2="422.357" y2="165.946" xlink:href="#B"><stop offset="0" stop-color="#bcbdbc"></stop><stop offset="1" stop-color="#f4f1f4"></stop></linearGradient></defs><path fill="url(#q)" d="M428 124v-2c1-1 1-1 1-2v-1c1-3 3-6 5-7v1h1c-1 1-2 2-3 4h2 1 0l1-1 1 1-1 1-1 1h0c1 3-1 5-1 8v6c1 0 1 0 1-1 1 3 0 8 2 11h0l-1 1v2c0 1 1 2 1 2-1 1-1 2-2 3v1h-1v-3h0l-1 1c0 1 0 2-1 3h-1v-3h-1c0 3-2 5-3 8 1 1 0 2 0 3l-1 1 1 2c-3 0-6 1-9 3-3 3-5 6-7 10-1 0-2 1-3 2v1c-1 0-1 2-2 2-1 1-3 1-4 1v-2l-1-1c0 2-1 3-2 4h-1v-2l3-6v-2l1-1 6-10 3-4h0l2-2c0-1 0-2 1-2 2-3 5-8 5-12v-1c1-1 1-1 1-2v-3c1-1 1-2 1-3v-3-2c1-1 1-1 1-2l-1-1h0v-2-2h2c0-1 0-1 1-2l1-1 1 1c-1 2-1 4-2 7v6l1-2v-2c1 0 1 0 1-1l1-4h1z"></path><path d="M425 119l1 1c-1 2-1 4-2 7v-2-2c1-1 1-1 1-2h-1c-1 1-1 5-2 6 0-2 0-4 1-5 0-1 0-1 1-2l1-1z" class="Q"></path><path d="M401 180c0-1 1-1 1-2l1 1v2 1-1c1-1 1-1 1-2 1 0 1-1 1-1 2 1 2 0 3 2-1 0-1 2-2 2-1 1-3 1-4 1v-2l-1-1z" class="p"></path><path d="M408 163l3-4v3c-1 0-1 1-1 1-1 3-3 6-4 8-1 0-2 1-2 1-1 1-2 3-3 4v-2l1-1 6-10z" class="q"></path><path d="M409 171l9-15v3h1v1h0-1v2c-1 2-3 3-4 5 0 1-1 2-2 3s-2 1-3 1z" class="I"></path><defs><linearGradient id="r" x1="410.738" y1="169.673" x2="413.448" y2="175.483" xlink:href="#B"><stop offset="0" stop-color="#b1b1af"></stop><stop offset="1" stop-color="#ceccd0"></stop></linearGradient></defs><path fill="url(#r)" d="M414 167h1c1 0 1 0 2-1h0l1 1h0c-3 3-5 6-7 10-1 0-2 1-3 2v1c-1-2-1-1-3-2l1-2c1-2 2-4 3-5 1 0 2 0 3-1s2-2 2-3z"></path><path d="M409 171c1 0 2 0 3-1-1 3-3 5-5 7h0l-1-1c1-2 2-4 3-5z" class="X"></path><path d="M423 150l1 1v2h1c0 2-2 3-2 5h1 0c1 0 1 0 2 1l1-1c1 1 0 2 0 3l-1 1 1 2c-3 0-6 1-9 3h0l-1-1h0c-1 1-1 1-2 1h-1c1-2 3-3 4-5v-2h1 0v-1l1-1 3-8z" class="I"></path><path d="M420 158l1-1h1c0 2-2 4-1 5l1-1h1c0 1-1 1-1 2h0c-1 1-1 1-2 1v-2-1c-1-1-1-1-2-1h1 0v-1l1-1z" class="X"></path><path d="M424 158c1 0 1 0 2 1l1-1c1 1 0 2 0 3l-1 1c-1 1-3 1-4 1h0c0-1 1-1 1-2h-1c0-1 1-2 2-3h0z" class="N"></path><path d="M424 158c1 0 1 0 2 1-1 1-2 2-3 2h-1c0-1 1-2 2-3h0z" class="m"></path><path d="M418 160c1 0 1 0 2 1v1 2c1 0 1 0 2-1 1 0 3 0 4-1l1 2c-3 0-6 1-9 3h0l-1-1h0c-1 1-1 1-2 1h-1c1-2 3-3 4-5v-2z" class="f"></path><path d="M424 133l1-2v-2c1 0 1 0 1-1l1-4h1v4c-1 2-1 4-1 5v1c0 4 1 9 0 13l-2 3c0 1 0 2-1 3v-2l-1-1-3 8-1 1h-1v-3c1-1 1-3 2-4 2-6 3-12 4-19z" class="N"></path><path d="M426 143v3c0 1-1 3-1 4s0 2-1 3v-2l-1-1s1-3 1-4c1-1 1-2 2-3z" class="G"></path><path d="M426 143v-7-5l1-1v4c0 4 1 9 0 13l-2 3c0-1 1-3 1-4v-3z" class="E"></path><path d="M428 124v-2c1-1 1-1 1-2v-1c1-3 3-6 5-7v1h1c-1 1-2 2-3 4h2 1 0c-4 7-5 12-5 20v1c-2 2-1 5-1 7l-1 3h0 1 0l1 1v1c0 3-2 5-3 8l-1 1c-1-1-1-1-2-1h0-1c0-2 2-3 2-5h-1c1-1 1-2 1-3l2-3c1-4 0-9 0-13v-1c0-1 0-3 1-5v-4z" class="B"></path><path d="M428 128l1 1v6h-1s0-1-1-2c0-1 0-3 1-5z" class="h"></path><path d="M427 147h1c-1 3-1 4-3 6h-1c1-1 1-2 1-3l2-3z" class="b"></path><defs><linearGradient id="s" x1="428.25" y1="149.246" x2="426.482" y2="157.372" xlink:href="#B"><stop offset="0" stop-color="#878585"></stop><stop offset="1" stop-color="#a1a0a1"></stop></linearGradient></defs><path fill="url(#s)" d="M429 148l1 1v1c0 3-2 5-3 8l-1 1c-1-1-1-1-2-1l5-10z"></path><defs><linearGradient id="t" x1="433.327" y1="122.644" x2="428.305" y2="117.496" xlink:href="#B"><stop offset="0" stop-color="#807f80"></stop><stop offset="1" stop-color="#999899"></stop></linearGradient></defs><path fill="url(#t)" d="M428 124v-2c1-1 1-1 1-2v-1c1-3 3-6 5-7v1h1c-1 1-2 2-3 4h2c-3 4-4 8-5 12l-1-1v-4z"></path><path d="M435 117l1-1 1 1-1 1-1 1h0c1 3-1 5-1 8v6c1 0 1 0 1-1 1 3 0 8 2 11h0l-1 1v2c0 1 1 2 1 2-1 1-1 2-2 3v1h-1v-3h0l-1 1c0 1 0 2-1 3h-1v-3h-1v-1l-1-1h0-1 0l1-3c0-2-1-5 1-7v-1c0-8 1-13 5-20z" class="Z"></path><path d="M429 148c1-1 1-2 2-3v-1c1 2 0 4 0 6h-1v-1l-1-1h0z" class="I"></path><path d="M435 151h0v-2c-1-1-1-2-1-3h0c-1-1 0-1 0-2h2 0v2c0 1 1 2 1 2-1 1-1 2-2 3z" class="S"></path><path d="M430 137v1h1v-6-1c0 3 0 6 1 9 0 1 1 2 1 3l-2 1c-1-1-1-4-1-6v-1z" class="b"></path><path d="M434 133c1 0 1 0 1-1 1 3 0 8 2 11h0l-1 1h0l-1-1c-2-3-1-7-1-10z" class="R"></path><path d="M435 117l1-1 1 1-1 1-1 1h0c-2 4-3 8-4 12v1 6h-1v-1c0-8 1-13 5-20z" class="L"></path><path d="M161 335h1c2 1 3 2 4 4h0l-1 1c0 2 1 2 2 3v1h0l-1 1h-6l5 2v3h-2c2 3 3 6 2 10 0 4-2 6-4 9l-2 2h1c-3 3-7 4-11 3v-1h-1l-1 1-1-1c-2 0-4-1-6-3-4-4-5-9-5-14v-3l1-2c2-4 4-8 7-12l2-2c2-1 3-1 5-1l3 1h0 0c2 0 4 0 6 1 1-1 1-2 1-3h1z" class="n"></path><path d="M149 373c4 0 7-2 11-4h1l-2 2h1c-3 3-7 4-11 3v-1z" class="P"></path><path d="M150 348c2 0 3-1 5-1l-2 2c-1 0-2 1-3 2h1c-2 1-4 3-5 5-1 1-1 4 0 5 0 1 1 2 1 2-1 0-2-2-2-3-1-2-1-4 0-6h1c1-2 2-4 4-6z" class="G"></path><defs><linearGradient id="u" x1="143.876" y1="342.748" x2="138.953" y2="352.419" xlink:href="#B"><stop offset="0" stop-color="#7f7d7e"></stop><stop offset="1" stop-color="#adadad"></stop></linearGradient></defs><path fill="url(#u)" d="M153 340c2 2 4 1 6 1-6 0-11 2-16 6-2 2-3 4-4 6-1-1-1 0-1-1l-1 1v-1c2-4 5-9 9-11h1 2c1 0 2-1 4-1z"></path><path d="M135 353l1-2c0 1 0 1 1 1v1l1-1c0 1 0 0 1 1-2 4-2 8 0 12 1 3 4 5 7 7 0 1 1 1 2 1l-1 1-1-1c-2 0-4-1-6-3-4-4-5-9-5-14v-3z" class="m"></path><defs><linearGradient id="v" x1="164.515" y1="346.561" x2="152.346" y2="346.271" xlink:href="#B"><stop offset="0" stop-color="#b0afaf"></stop><stop offset="1" stop-color="#d6d3d4"></stop></linearGradient></defs><path fill="url(#v)" d="M163 343h1l3 1-1 1h-6l5 2v3h-2l-8-3c-2 0-3 1-5 1 0 0 0-1 1-2s2-2 4-2h1 7v-1z"></path><path d="M150 336l3 1h0c-3 1-5 2-7 4-4 2-7 7-9 11-1 0-1 0-1-1 2-4 4-8 7-12l2-2c2-1 3-1 5-1z" class="p"></path><path d="M155 347l8 3c2 3 3 6 2 10 0 4-2 6-4 9h-1c2-2 3-5 3-8 0-2-1-6-2-7-3-3-6-3-10-3h-1c1-1 2-2 3-2l2-2z" class="B"></path><defs><linearGradient id="w" x1="160.618" y1="335.938" x2="154.557" y2="342.832" xlink:href="#B"><stop offset="0" stop-color="#181818"></stop><stop offset="1" stop-color="#403d40"></stop></linearGradient></defs><path fill="url(#w)" d="M161 335h1c2 1 3 2 4 4h0l-1 1c0 2 1 2 2 3v1h0l-3-1h-1l-4-2c-2 0-4 1-6-1-2 0-3 1-4 1h-2-1c2-2 4-3 7-4h0c2 0 4 0 6 1 1-1 1-2 1-3h1z"></path><path d="M161 335h1c2 1 3 2 4 4h0c-2-1-5-1-7-1 1-1 1-2 1-3h1z" class="E"></path><path d="M153 340c1-1 3-1 4-1 2 0 4 1 6 2 0 0 0 1 1 1v1h-1l-4-2c-2 0-4 1-6-1z" class="N"></path><defs><linearGradient id="x" x1="506.665" y1="408.558" x2="544.555" y2="436.849" xlink:href="#B"><stop offset="0" stop-color="#090909"></stop><stop offset="1" stop-color="#3b3b3b"></stop></linearGradient></defs><path fill="url(#x)" d="M515 392h0c0 1 0 4 1 6h0v2l1 1v-1l1-1h0l1-1h1 0c2 1 2 2 4 2h1 0 0v-2h1v2 1l1-3v5 1s0 1 1 1v1c-1 1-1 3-2 5 1 1 1 1 1 2 0 8 1 16 2 24 2 8 3 15 10 22l4 3-1 1 1 1s-1 0-2 1c-1-1-2-2-3-2h-2 0c-5-1-8-6-10-9h-3-1c-1 1-1 2-2 3h0v-1h-1c0-2 1-4 1-7v-7l-3-6-1-1h-1c-1-1-1-1-2-1l-4-4h0c1 2 3 3 4 5-3-3-6-5-9-8h2c-2-3-5-5-8-6l1-1h3c-3-1-5-2-8-3v-1h4 2c1 1 4 1 5 1h2 4l5-3h0c-2-2-1-19-1-22z"></path><path d="M512 430l-3-3h0 2c1 0 2 1 2 3h-1z" class="H"></path><path d="M517 415c0 1 1 1 0 2-2 1-4 2-6 2 0 0 1-1 2-1 1-2 3-1 4-3z" class="F"></path><path d="M527 404s0 1 1 1v1c-1 1-1 3-2 5-1-2-1-3-1-4 0-2 1-2 2-3z" class="p"></path><path d="M518 433l-2-5v-7c1 3 3 6 3 9v1h0l-1 2h0z" class="C"></path><path d="M506 427l7 6 1-1-2-2h1l3 5h-1c-1-1-1-1-2-1l-4-4h0c1 2 3 3 4 5-3-3-6-5-9-8h2z" class="I"></path><path d="M516 414l1 1c-1 2-3 1-4 3-1 0-2 1-2 1-3 1-6 1-9 1l-8-3v-1h4 2c1 1 4 1 5 1h2 4l5-3z" class="K"></path><defs><linearGradient id="y" x1="526.406" y1="434.08" x2="535.999" y2="465.654" xlink:href="#B"><stop offset="0" stop-color="#767477"></stop><stop offset="1" stop-color="#b9b9b8"></stop></linearGradient></defs><path fill="url(#y)" d="M518 433l1-2h0c4 10 10 19 17 27 2 1 4 3 6 5l1 1s-1 0-2 1c-1-1-2-2-3-2h-2 0c-5-1-8-6-10-9h-3-1c-1 1-1 2-2 3h0v-1h-1c0-2 1-4 1-7v-7l-3-6h1v-1-2h0z"></path><path d="M518 433h0l3 8s-1 0-1-1c0 0 0-1-1-1 1 1 1 2 1 3l-3-6h1v-1-2z" class="D"></path><defs><linearGradient id="z" x1="515.917" y1="445.625" x2="539.314" y2="455.997" xlink:href="#B"><stop offset="0" stop-color="#151515"></stop><stop offset="1" stop-color="#4c4a4b"></stop></linearGradient></defs><path fill="url(#z)" d="M520 442c0-1 0-2-1-3 1 0 1 1 1 1 0 1 1 1 1 1 4 8 10 16 17 22h-2 0c-5-1-8-6-10-9h-3-1c-1 1-1 2-2 3h0v-1h-1c0-2 1-4 1-7v-7z"></path><defs><linearGradient id="AA" x1="338.695" y1="138.009" x2="296.802" y2="115.003" xlink:href="#B"><stop offset="0" stop-color="#c7c7c7"></stop><stop offset="1" stop-color="#ecebeb"></stop></linearGradient></defs><path fill="url(#AA)" d="M322 111c2-2 3-3 5-4 1-1 1-2 2-3 0-1 0-1 1-1h0v3l-1 2-1 1c-1 1-3 2-5 3 1 1 1 2 2 3-2 2 0 3-2 4v2c0 2-1 3 1 5l3 3h-2c0-1-1-2-2-2v1l2 1h-1l3 4v1l-1-1v1c0 1 1 4 2 5l1-1c1 1 2 3 4 4 1 2 2 4 2 6 0 4 1 7 0 11 0 0-1 1-1 2v2l-1 1h-1v1c-1 1-2 1-2 2h0c-2 1-3 2-5 3l-3 3h1-2l-7-11-2-2c0-1 0-3 1-4s1-3 2-4c1-4 1-9 1-13-1-2-1-4-2-6v-2s-1-1-1-2l-1-3c-1 0-1-1-1-1l-2-2c-1-1-2-2-3-4-1-1-2-1-2-2l1-1c1 1 3 3 4 5v-2c-1-3-5-6-7-9h0c-1-1-1-1-1-2 1 0 0 0 1 1h1c1 1 2 2 3 2v-1-3l2 2c2 3 5 5 8 7h0 1l1-1s1 0 1 1h1c1 0 0-2 1-3 0-1 0-1 1-2h0z"></path><path d="M323 119c-1-2-1-5 0-7 1 1 1 2 2 3-2 2 0 3-2 4z" class="f"></path><path d="M329 144c-1-2-1-4-2-5-2-4-5-7-5-11h1l1 1 3 4v1l-1-1v1c0 1 1 4 2 5s1 3 2 5h-1z" class="K"></path><path d="M306 107l2 2c2 3 5 5 8 7h0 1c2 2 3 4 4 7l-15-12v-1-3z" class="P"></path><path d="M322 159v-1c3-5 3-9 3-14 2 5 3 9 1 15-1 1-2 2-2 4h1l-2 1v1h-2l2 2h-1 0c-1 0-2-1-2-2 0 0 1-1 1-2s0-1-1-2v-1l2-1z" class="H"></path><path d="M322 159c1 1 1 1 1 3-1 1-1 1-1 2s0 0 1 1h-2l2 2h-1 0c-1 0-2-1-2-2 0 0 1-1 1-2s0-1-1-2v-1l2-1z" class="O"></path><path d="M328 139l1-1c1 1 2 3 4 4 1 2 2 4 2 6 0 4 1 7 0 11 0 0-1 1-1 2v2l-1 1h-1v1c-1 1-2 1-2 2h0c-2 1-3 2-5 3l-3 3v-2h1l-1-2 1-1-1-1h1l-2-2h2v-1l2-1c1-1 2-1 3-3 3-5 2-11 1-16h1c-1-2-1-4-2-5z" class="Q"></path><path d="M324 167v1l1 1c2 0 3-2 5-2-2 1-3 2-5 3l-3 3v-2h1l-1-2 1-1v-1h1z" class="I"></path><path d="M330 144c1 5 2 14-1 19-1 2-3 4-5 4h-1v1l-1-1h1l-2-2h2v-1l2-1c1-1 2-1 3-3 3-5 2-11 1-16h1z" class="b"></path><path d="M323 165v-1l1 1v1c0 1 0 1-1 1l-2-2h2zm-10-36l-1-3c-1 0-1-1-1-1l-2-2c-1-1-2-2-3-4-1-1-2-1-2-2l1-1c1 1 3 3 4 5 2 2 7 9 8 11 3 6 6 13 4 20 0 2-1 4-2 6 0 1 1 2 1 2v1c1 1 1 1 1 2s-1 2-1 2c0 1 1 2 2 2h0l1 1-1 1 1 2h-1v2h1-2l-7-11-2-2c0-1 0-3 1-4s1-3 2-4c1-4 1-9 1-13-1-2-1-4-2-6v-2s-1-1-1-2z" class="B"></path><path d="M314 162c0-1 0-2-1-3v-1l1-1c0-1 1-2 2-3l-1 2c0 1 0 3-1 4h1v2l1 1c2-1 2-4 3-5 0 1 1 2 1 2v1c1 1 1 1 1 2s-1 2-1 2c0 1 1 2 2 2h0l1 1-1 1 1 2h-1v2h1-2l-7-11z" class="a"></path><path d="M320 165c0 1 1 2 2 2h0l1 1-1 1 1 2h-1l-3-4h1v-2z" class="V"></path><path d="M320 161c1 1 1 1 1 2s-1 2-1 2v2h-1c-1-1-2-2-2-3 1-2 2-2 3-3z" class="E"></path><path d="M313 129l-1-3c-1 0-1-1-1-1l-2-2c-1-1-2-2-3-4-1-1-2-1-2-2l1-1c1 1 3 3 4 5 2 2 7 9 8 11l-1 1c0 1 2 4 2 5 1 3 1 6 1 9-1 3-2 5-3 7-1 1-2 2-2 3l-1 1v1c1 1 1 2 1 3l-2-2c0-1 0-3 1-4s1-3 2-4c1-4 1-9 1-13-1-2-1-4-2-6v-2s-1-1-1-2z" class="f"></path><path d="M313 129l2 2v1s1 1 0 2h0c0 1 1 3 1 5-1-2-1-4-2-6v-2s-1-1-1-2z" class="U"></path><path d="M598 250l5-3c3 2 6 5 8 7l11 10 17 17-19-1h-9c0-1 0-2-1-3h0l-1 2c-1 2-19 1-22 0h0l1-2v-4s0-1 1-1h0l-1-1c0-1-1-2-1-3v-1-2h0l-1-3c0-2 0-3-1-5 2 1 2 0 3 1l1-2c2-2 6-4 9-6z" class="r"></path><defs><linearGradient id="AB" x1="596.402" y1="260.541" x2="597.797" y2="279.471" xlink:href="#B"><stop offset="0" stop-color="#c7c7c7"></stop><stop offset="1" stop-color="#f3f2f2"></stop></linearGradient></defs><path fill="url(#AB)" d="M585 257c2 1 2 0 3 1l1 1c2 1 4 3 6 5l12 10v1c1 0 2 2 3 2l-1 2c-1 2-19 1-22 0h0l1-2v-4s0-1 1-1h0l-1-1c0-1-1-2-1-3v-1-2h0l-1-3c0-2 0-3-1-5z"></path><path d="M587 268h2v4l-1-1c0-1-1-2-1-3z" class="r"></path><path d="M589 272c0 1 1 1 1 2s0 2-1 3h-1v-4s0-1 1-1z" class="W"></path><path d="M589 259c2 1 4 3 6 5h-1c-2 0-3-1-4-2v-1l-1 1v1 1c0-2-1-4 0-5z" class="U"></path><path d="M585 257c2 1 2 0 3 1l1 1c-1 1 0 3 0 5-1 0-2 0-2 1h0l-1-3c0-2 0-3-1-5z" class="P"></path><path d="M284 571c1 0 2 1 2 2l3 3c2 0 3-1 5-1l2 2c2 2 5 2 7 4 1 1 2 2 3 4 1 1 3 1 3 3 1 0 2 1 2 2 1 3 3 5 4 8h1 1v-3c0 1 1 2 1 3-1 0-2 0-2 1 2 1 3 2 4 2 1 1 3 2 4 3h-1v1c1-1 1-1 2-1l-1-1h1c0-2-3-6-3-7 2 2 3 5 4 8l1-2v-1l2-3c1 1 2 2 3 2v3c0 1 0 0 1 1v1c1-1 2-2 2-3l1-1h1l3 5c4 6 10 16 10 24l1-1c1 0 1 0 1 1 0 3-3 7-5 9-1 0-2 2-3 2l-1-1v1c-1 0-1 0-2 1v-1c0-1-1-2-2-2-1-3-2-5-4-7 0-1-1 0-1-1-2-2-3-5-5-6 0 0-6-8-7-9h0l-3-2c1 0 1 0 2-1h-1c-1-1-1-2-1-3l-1-1c-6-9-13-17-19-26-1 0-2-3-3-3 0 0-3 1-4 1-7 1-12-4-17-7v-2l2-1h2 5z" class="M"></path><path d="M324 611c2 1 2 2 3 3v1c-2-1-2-2-3-3v-1z" class="O"></path><path d="M319 610v-1h1c1 1 1 2 2 3 0 0 0 1-1 1h-1c-1-1-1-2-1-3z" class="j"></path><path d="M316 599c2 1 3 2 4 2 1 1 3 2 4 3h-1c-2-1-4-2-6-2-1-1-2-1-2-2l1-1z" class="L"></path><path d="M284 571c1 0 2 1 2 2l3 3c-2 0-3 0-4-1-1 0-3-1-4-2h3v-1c-2-1-3-1-5-1h5z" class="Y"></path><path d="M322 596c2 2 3 5 4 8 3 4 6 9 9 13 5 7 8 13 11 20 2-2 4-4 4-7l1-1c1 0 1 0 1 1 0 3-3 7-5 9-1 0-2 2-3 2l-1-1v-1c-5-8-10-17-16-24v-1c-1-1-1-2-3-3h2v-1c-1-2-3-3-4-4l-1-1h0 2c1-1 1-1 2-1l-1-1h1c0-2-3-6-3-7z" class="B"></path><path d="M329 598c1 1 2 2 3 2v3c0 1 0 0 1 1v1c1-1 2-2 2-3l1-1h1l3 5c4 6 10 16 10 24 0 3-2 5-4 7-3-7-6-13-11-20-3-4-6-9-9-13l1-2v-1l2-3z" class="g"></path><path d="M509 135h3l4 1h1c1 0 1 1 2 1l1 1c2 0 3 1 4 3v1c1 1 2 2 2 3h-1c5 2 10 2 14 6h0l1 1 2 2h0c3 4 4 9 3 14v1l-1-1h-1c-1 1-2 1-3 2l-1 1c-1 2-1 2-3 3h0c-1 1-2 1-3 1-2 2-5 2-8 2h-2-1l2 2h0v1c-2-1-3-2-4-3v1l-1-1-1 1-2-2-4-2v-1h1l-2-1c0-1-1-1-1-2h1c-1-1-1-1-1-2l-2-2v-1h1 0l-3-3v-1l2 1v-5l1 1h1v-2h1c-1-1 0-2 0-3-1-1-1-3-1-3l1-1h1v-1l-2-4c-1-1-1-1-3-1l1-1v-1-4h0l-1-1v-1h2z" class="n"></path><path d="M533 175c1-1 1-3 2-4l1 1v2c-1 1-2 1-3 1z" class="G"></path><path d="M538 154c1 1 1 2 2 3l1 2v1h-1c-1-2-2-3-4-4l2-1h0l-1-1h1z" class="T"></path><path d="M518 173c3 2 4 3 7 4h-2-1l2 2h0v1c-2-1-3-2-4-3v-1h-1c-1-1-1-2-1-3z" class="h"></path><path d="M539 151l1 1 2 2h0c-1 1 0 2 0 3v2h-1l-1-2c-1-1-1-2-2-3l-1-1h1l1-1v-1h0z" class="q"></path><path d="M540 157l1-1c0-1 0-2-1-3h0v-1l2 2h0c-1 1 0 2 0 3v2h-1l-1-2z" class="Q"></path><path d="M542 157c1 4 1 6 0 9l-2 4-1 1c0-2 0-5 1-7v-4h1v-1h1v-2z" class="U"></path><path d="M541 160c1 2 0 4-1 6v-2-4h1z" class="C"></path><path d="M542 154c3 4 4 9 3 14v1l-1-1h-1c-1 1-2 1-3 2l2-4c1-3 1-5 0-9 0-1-1-2 0-3z" class="Y"></path><path d="M509 165l4 2c1 0 1 0 2 1 0 1 1 2 1 2l2 3c0 1 0 2 1 3h1v1 1l-1-1-1 1-2-2-4-2v-1h1l-2-1c0-1-1-1-1-2h1c-1-1-1-1-1-2l-2-2v-1h1 0z" class="G"></path><path d="M512 174v-1h1c2 1 4 3 6 4l-1 1-2-2-4-2z" class="H"></path><path d="M509 165l4 2c1 0 1 0 2 1 0 1 1 2 1 2v1c-3-1-5-3-7-5h-1v-1h1 0z" class="e"></path><path d="M515 154h1c1-1 3-2 5-3l1 1-1 1h0l2 1h0c-2 2-4 4-5 6 0 2 1 4 2 6-1 1-1 1-2 1h-2l-1 1c-1-1-1-1-2-1l-4-2-3-3v-1l2 1v-5l1 1h1v-2h1c-1-1 0-2 0-3 0 1 0 2 1 2s2 0 2-1h1 0z" class="Z"></path><path d="M506 161l2 1 1 1 1 1c1 1 3 0 4 2-1 0-1 0-1 1l-4-2-3-3v-1z" class="V"></path><path d="M510 156h1c1 1 1 3 1 4v1l-2-1v1 1c0 1 0 1-1 1l-1-1v-5l1 1h1v-2z" class="M"></path><path d="M515 154h1c1-1 3-2 5-3l1 1-1 1h0c-3 2-4 4-6 7 0 1 1 3 2 4v1l-1 1c-1-1-1-2-2-3s-1-2-2-3c0-1 0-3-1-4s0-2 0-3c0 1 0 2 1 2s2 0 2-1h1 0z" class="G"></path><path d="M509 135h3l4 1h1c1 0 1 1 2 1l1 1c2 0 3 1 4 3v1c1 1 2 2 2 3h-1c5 2 10 2 14 6v1l-1 1h-1l1 1h-1l1 1h0l-2 1c-3-3-5-4-9-4h-3-2 0l-1-1c-2 1-4 2-5 3h-1 0-1c0 1-1 1-2 1s-1-1-1-2c-1-1-1-3-1-3l1-1h1v-1l-2-4c-1-1-1-1-3-1l1-1v-1-4h0l-1-1v-1h2z" class="n"></path><path d="M512 149l3 2h2 0l-2 3h0-1c-1-1-2-3-3-5h1z" class="B"></path><path d="M510 150l1-1c1 2 2 4 3 5 0 1-1 1-2 1s-1-1-1-2c-1-1-1-3-1-3z" class="b"></path><path d="M517 151h0c2-2 5-2 8-2h4c-2 0-3 0-5 2v1h3-3-2 0l-1-1c-2 1-4 2-5 3h-1l2-3h0z" class="L"></path><path d="M529 149c3 1 5 2 8 4l1 1h-1l1 1h0l-2 1c-3-3-5-4-9-4h-3v-1c2-2 3-2 5-2z" class="E"></path><path d="M509 135h3l4 1h1c1 0 1 1 2 1l1 1c2 0 3 1 4 3v1c1 1 2 2 2 3h-1c-3-1-6-1-10-1-2 1-3 1-5 0-1-1-1-1-3-1l1-1v-1-4h0l-1-1v-1h2z" class="g"></path><path d="M508 137v-1l1 1c0 1 0 2 1 2h3c0 1 1 1 0 2v1c-1 0-1 1-2 2h0 4c-2 1-3 1-5 0-1-1-1-1-3-1l1-1v-1-4z" class="M"></path><path d="M513 139h0c2 0 5-1 7-1s3 1 4 3v1c-1-1-3-2-4-2-2 0-5 1-7 2v-1c1-1 0-1 0-2z" class="Z"></path><defs><linearGradient id="AC" x1="510.587" y1="138.458" x2="514.804" y2="134.639" xlink:href="#B"><stop offset="0" stop-color="#494949"></stop><stop offset="1" stop-color="#676567"></stop></linearGradient></defs><path fill="url(#AC)" d="M509 135h3l4 1h1c1 0 1 1 2 1l1 1c-2 0-5 1-7 1h0-3c-1 0-1-1-1-2l-1-1v1h0l-1-1v-1h2z"></path><path d="M158 173h0c4-3 7-5 12-6 3-1 5 0 8 2h0l1 1c3 5 7 8 13 10 3 0 5 0 7-1l1 1-2 2c-1 2-5 1-6 3l1 1 3-1h0c0 1 0 2-1 2l-1 2h0v1c-2 2-2 4-2 7-1-1-2-1-3-2-2 2-12 11-12 11l-2-1-3 3v-1-1c1-1 2-1 3-2l-11-4-1-1v-1h-1v-2h-1-2c-1 1-2 1-3 0v-1h-1-2c-3 2-5 5-8 6 0-3 5-5 6-8 2-3 0-7 1-10v-1c1-4 3-6 6-9z" class="l"></path><path d="M157 190h6 3c-3 0-4 0-6 2h0c0 1-1 2-1 2s-1 0-1-1l-1-3z" class="P"></path><path d="M160 182c1 1 1 1 1 2 2 1 3 2 6 3h-1-3c-2-1-3-3-4-5 0 0 0 1 1 0z" class="a"></path><path d="M155 192l1-2h1l1 3-1 1 1 1h1l2 1h0-2c-1 1-2 1-3 0v-1h-1-2l2-3z" class="o"></path><path d="M155 192l1-2h1l1 3-1 1v-1h-1l-1-1z" class="K"></path><path d="M158 173l1 1h0c-2 3-5 8-6 11 0 2 0 4-1 5v-7-1c1-4 3-6 6-9z" class="H"></path><path d="M160 192h0c2-2 3-2 6-2 4 1 9 1 12 4h0l1 2 1 1-1 1h-2v2l1 1c0 1-2 2-3 3l-11-4-1-1v-1h-1v-2h-1 0l-2-1h-1l-1-1 1-1c0 1 1 1 1 1s1-1 1-2z" class="X"></path><path d="M176 195c1 0 2 1 3 1h0l1 1-1 1h-2l-2-2c0-1 1-1 1-1z" class="I"></path><path d="M162 192c3-1 8-2 11 0 1 0 2 1 3 1v1h-1-3c-2-1-6-1-9-1l-1-1z" class="l"></path><path d="M160 192v1l2-1 1 1c3 0 7 0 9 1h3 1v1s-1 0-1 1h0c-1 0-2 0-3 1h-1l-4-1c-1 1-2 1-4 2h-1v-2h-1 0l-2-1h-1l-1-1 1-1c0 1 1 1 1 1s1-1 1-2z" class="K"></path><path d="M160 192v1l2-1 1 1c-1 1-3 1-4 2h-1l-1-1 1-1c0 1 1 1 1 1s1-1 1-2z" class="f"></path><path d="M172 194h3-2 0l-2 1h-4-3c1-1 3-1 4-1h0 4z" class="H"></path><path d="M164 195h3v1c-1 1-2 1-4 2h-1v-2h-1 0l3-1z" class="I"></path><path d="M175 194h1v1s-1 0-1 1h0c-1 0-2 0-3 1h-1l-4-1v-1h4l2-1h0 2z" class="a"></path><path d="M175 194h1v1s-1 0-1 1h0-2v-2h2z" class="F"></path><path d="M171 197h1c1-1 2-1 3-1h0l2 2v2l1 1c0 1-2 2-3 3l-11-4-1-1v-1c2-1 3-1 4-2l4 1z" class="n"></path><path d="M171 197h1c1-1 2-1 3-1h0l2 2v2c-2-1-4-3-6-3z" class="H"></path><path d="M158 173h0c4-3 7-5 12-6 3-1 5 0 8 2h0l1 1c-2 2-4 4-5 6h0c-1 2-1 5 0 7h-1l1 3v1c-2 1-5 0-7 0-3-1-4-2-6-3 0-1 0-1-1-2-1 1-1 0-1 0h-1c0-3 1-5 2-6l-1-2h0l-1-1z" class="n"></path><path d="M160 176l1-1h1c-2 2-2 4-2 7-1 1-1 0-1 0h-1c0-3 1-5 2-6z" class="L"></path><path d="M158 173h0c4-3 7-5 12-6 3-1 5 0 8 2h0l1 1c-2 2-4 4-5 6h0c-1 2-1 5 0 7h-1v-1c-1-5 1-7 3-11 1 0 1-1 1-2-1 0-3-1-4-1-4-1-8 4-11 7h-1l-1 1-1-2h0l-1-1z" class="d"></path><path d="M174 183c-1-2-1-5 0-7h0c1-2 3-4 5-6 3 5 7 8 13 10 3 0 5 0 7-1l1 1-2 2c-1 2-5 1-6 3l1 1 3-1h0c0 1 0 2-1 2l-1 2h0v1c-2 2-2 4-2 7-1-1-2-1-3-2-2 2-12 11-12 11l-2-1-3 3v-1-1c1-1 2-1 3-2s3-2 3-3l-1-1v-2h2l1-1-1-1-1-2h0 1l2 1h1v-1c0-1-2-2-3-3l-5-5-1-3h1z" class="n"></path><path d="M194 189h0v1c-2 2-2 4-2 7-1-1-2-1-3-2 1-1 2-1 2-2-2 0-3 1-5 1l8-5z" class="B"></path><path d="M174 183c1 1 2 3 3 4 3 3 6 5 8 8-1 1-2 2-2 3h-1c-1 0-1 0-2-1l-1-1-1-2h0 1l2 1h1v-1c0-1-2-2-3-3l-5-5-1-3h1z" class="X"></path><path d="M179 196l-1-2h0 1l4 4h-1c-1 0-1 0-2-1l-1-1z" class="b"></path><path d="M186 194c2 0 3-1 5-1 0 1-1 1-2 2-2 2-12 11-12 11l-2-1-3 3v-1-1c1-1 2-1 3-2s3-2 3-3l-1-1v-2h2l1-1c1 1 1 1 2 1h1c0-1 1-2 2-3l1-1z" class="U"></path><path d="M180 197c1 1 1 1 2 1-1 1-1 2-2 3s-3 3-5 4l-3 3v-1-1c1-1 2-1 3-2s3-2 3-3l-1-1v-2h2l1-1z" class="F"></path><defs><linearGradient id="AD" x1="450.193" y1="118.283" x2="480.163" y2="25.061" xlink:href="#B"><stop offset="0" stop-color="#b4b3b3"></stop><stop offset="1" stop-color="#f8f6f6"></stop></linearGradient></defs><path fill="url(#AD)" d="M447 89c10-5 21-12 28-21 6-7 12-15 16-23 2-4 4-12 8-14 1 0 1 0 2 1l-1 1c-2 3-3 8-4 11-2 6-4 12-7 18-1 1-1 3-2 4l-1-1v1l-1 2v1 1c0 1 0 1-1 2h0c1 1 1 2 0 2l-2 4c-1 1-1 1-1 2-1-1-1 0-2-1 0-1 0-2 1-3 0-2 1-4 3-6 0-1 1-3 1-3v-1c-1 2-2 4-3 5-1 3-2 5-3 7s-2 4-3 7c-1 0-1 2-2 2l-1-1c0 1-1 1-1 2h-1v1h-1c0 1 0 1-1 1l-1 1v1c-2 1-1 1-3 2-1 0-1 1-2 1h0l-1 1h-1c-1 1-3 2-5 3-2 2 1 0-1 1l-1 1h-1-2l4-3-4 2h-1c0 1-1 1-2 2l-1 1h0c-2 1-3 3-4 3l-1 1h-2l4-3v-1c1 0 2-1 2-2-1 0-2 0-3 1l-5 5c0 2-2 3-3 5-2 1-4 4-5 7v1c0 1 0 1-1 2v2h-1l-1 4c0 1 0 1-1 1v2l-1 2v-6c1-3 1-5 2-7l-1-1-1 1c-1 1-1 1-1 2h-2c1-2 1-3 2-4 0-1 1-2 1-3 1-1 1-2 1-3h0c1-1 1-2 2-3h-1l-1-1h1c2-2 3-6 5-8 0-1 1-1 1-2s1-2 1-3c1 0 1 0 1-1l1-2c2-2 4-4 6-5l3-3h1l2-1c1 0 1 0 2-1 0 0 1-1 2-1s1-1 2-1c2 0 2 1 4 2-3 2-7 3-10 5l-1 1 1 1z"></path><path d="M435 109c1-2 2-3 3-4l4-4s1 0 2-1h0c2-1 5-4 7-4v1h1 0 0c-2 2-3 3-5 3l-3 3h-1c1 0 2-1 2-2-1 0-2 0-3 1l-5 5-2 2z" class="f"></path><path d="M437 107c0 2-2 3-3 5-2 1-4 4-5 7v1c0 1 0 1-1 2v2h-1l-1 4c0 1 0 1-1 1v2l-1 2v-6c1-3 1-5 2-7 2-3 3-5 5-7 1-1 3-2 4-4h0l2-2z" class="F"></path><path d="M447 83c1 0 1 0 2-1 0 0 1-1 2-1s1-1 2-1c2 0 2 1 4 2-3 2-7 3-10 5l-1 1 1 1c-1 1-3 2-4 2v1c-1 1-3 1-4 2h0 2l-3 3h1l4-3c-1 2-2 5-3 7l-7 8c-2 2-3 3-4 5l-1 1c-1 1-1 2-2 3h-1v1l-1 1c-1 1-1 1-1 2h-2c1-2 1-3 2-4 0-1 1-2 1-3 1-1 1-2 1-3h0c1-1 1-2 2-3h-1l-1-1h1c2-2 3-6 5-8 0-1 1-1 1-2s1-2 1-3c1 0 1 0 1-1l1-2c2-2 4-4 6-5l3-3h1l2-1z" class="m"></path><path d="M424 120v-2c1-2 1-3 2-4h0l1 1-1 3h-1v1l-1 1z" class="i"></path><path d="M443 91h-3v-1c2-2 4-3 7-3l-1 1 1 1c-1 1-3 2-4 2z" class="I"></path><path d="M427 115c1-1 2-3 3-5s3-3 4-5c0 0 0-1 1-2s2-2 2-3h1c1 0 1 0 2 1l-7 8c-2 2-3 3-4 5l-1 1c-1 1-1 2-2 3l1-3z" class="H"></path><path d="M147 416v-5c1-9 8-14 14-19-1 2-2 5-2 7v1c-1 2-1 3-1 4v1c2 2 2 4 4 6h0 0 1v-1c-1-3-1-7 1-11 1-1 2-2 4-3h2c3-1 6-2 9-1s6 3 8 5v1h1v-1l1 1c1 0 2-1 3 0h1c0 1 1 1 1 1l1 3s-1 0-1 1c1 2 1 3 0 4-1 2 0 5-1 7 0 1-1 3-2 5 0 1 0 2-1 3-2 0-3 2-3 3-2 1-3 2-4 2-2 1-3 2-5 2-3 1-6 1-9 1-4 0-7 0-11-1l-4-2c-5-3-9-7-10-12 0-3 0-4 1-5 0-2 1-4 2-5v1h-1v2 1 1c0 1 1 2 1 3z" class="n"></path><path d="M188 423c2-3 3-5 3-8l2 2c0 1-1 3-2 5-1 1-2 1-3 1z" class="c"></path><path d="M188 423c1 0 2 0 3-1 0 1 0 2-1 3-2 0-3 2-3 3-2 1-3 2-4 2v-3h1c2-1 3-2 4-4z" class="O"></path><path d="M173 431c0-1 0-3-1-4v-1l4 3c2 1 6-1 7-2v3c-2 1-3 2-5 2-2-1-4 0-5-1z" class="F"></path><defs><linearGradient id="AE" x1="174.322" y1="419.629" x2="165.484" y2="412.305" xlink:href="#B"><stop offset="0" stop-color="#2c2b2a"></stop><stop offset="1" stop-color="#565455"></stop></linearGradient></defs><path fill="url(#AE)" d="M163 410l1 2h1v-1c3 3 5 5 10 6h4l1 1c-2 1-5 2-8 1-5-1-8-4-10-8h0 1v-1z"></path><path d="M189 401c1 0 2-1 3 0h1c0 1 1 1 1 1l1 3s-1 0-1 1c1 2 1 3 0 4-1 2 0 5-1 7l-2-2v-8-1l-1 3c0-3 0-5-1-8z" class="G"></path><path d="M144 418c0-3 0-4 1-5 0-2 1-4 2-5v1h-1v2 1 1c0 1 1 2 1 3 1 4 2 7 5 9h1v1c1 0 2 2 2 2 2 2 4 3 6 3 2 1 4 1 7 1 1 0 4-1 5-1 1 1 3 0 5 1-3 1-6 1-9 1-4 0-7 0-11-1l-4-2c-5-3-9-7-10-12z" class="W"></path><defs><linearGradient id="AF" x1="148.68" y1="406.119" x2="155.583" y2="408.695" xlink:href="#B"><stop offset="0" stop-color="#636263"></stop><stop offset="1" stop-color="#7e7d7c"></stop></linearGradient></defs><path fill="url(#AF)" d="M147 416v-5c1-9 8-14 14-19-1 2-2 5-2 7v1c-1 2-1 3-1 4v1 9-2c-1-4-1-8-1-12l-4 4c-2 3-2 8-2 11-1 4 0 7 2 10h-1c-3-2-4-5-5-9z"></path><path d="M170 396c3-1 6-2 9-1s6 3 8 5v1h1v-1l1 1c1 3 1 5 1 8s-2 5-4 7c-2 1-4 2-6 2l-1-1h-4c-5-1-7-3-10-6v1h-1l-1-2c-1-3-1-7 1-11 1-1 2-2 4-3h2z" class="n"></path><defs><linearGradient id="AG" x1="162.037" y1="402.737" x2="167.588" y2="400.543" xlink:href="#B"><stop offset="0" stop-color="#343234"></stop><stop offset="1" stop-color="#4e4f4d"></stop></linearGradient></defs><path fill="url(#AG)" d="M168 396h2c-3 3-5 5-6 9 0 2 0 4 1 5v1 1h-1l-1-2c-1-3-1-7 1-11 1-1 2-2 4-3z"></path><path d="M187 401h1v-1l1 1c1 3 1 5 1 8s-2 5-4 7c-2 1-4 2-6 2l-1-1c2-1 4-1 6-2s3-4 4-6c0-2 0-4-1-5s-3-1-4-2h-1c1 0 3-1 4-1z" class="D"></path><path d="M116 250l1 2-31 28h1 23c1 0 1 0 1 1h1 0l1 1v-2l30-1c0 6 1 11 1 17h5 1v1h-7-2c-2 1-3 1-5 2-8 5-14 14-22 20v-1l18-18h-9-19-43l55-50z" class="J"></path><defs><linearGradient id="AH" x1="79.991" y1="285.044" x2="83.753" y2="299.481" xlink:href="#B"><stop offset="0" stop-color="#7d7c7c"></stop><stop offset="1" stop-color="#9d9c9d"></stop></linearGradient></defs><path fill="url(#AH)" d="M84 281s1-1 2-1h1l-1 1 1 1h0 1v1l1 1v1s0 1 1 1l-1 1c-1-1 0 0-1 0v1 2h1 1 1c1 1 1 2 2 2h1v1c2 1 4 3 6 3v1H67h-1l9-8c2-2 3-4 6-5l3-3z"></path><path d="M87 290c0-1 0-1 1-2v2h1c1 1 1 2 2 3-1 1-2 1-3 0h-1 0v-3z" class="F"></path><path d="M87 290c0-1 0-1 1-2v2c0 1 0 2-1 3h0v-3z" class="b"></path><path d="M84 281c1 2 0 4 1 5v4l-1 1c-1-2-1-3-1-4v-1l-2-2 3-3z" class="K"></path><path d="M84 281s1-1 2-1h1l-1 1 1 1h0 1v1l1 1v1s0 1 1 1l-1 1c-1-1 0 0-1 0v1c-1 1-1 1-1 2-1-1-1-2-2-4-1-1 0-3-1-5z" class="I"></path><defs><linearGradient id="AI" x1="99.149" y1="278.679" x2="103.012" y2="297.902" xlink:href="#B"><stop offset="0" stop-color="#7c7b7b"></stop><stop offset="1" stop-color="#9e9d9d"></stop></linearGradient></defs><path fill="url(#AI)" d="M87 280h23c1 0 1 0 1 1h1 0l1 1s-1 1 0 1v1c0 1 0 3 1 4h0v1h0c0 3 0 6 1 8h-9-5v-1l-1 1v-1c-2 0-4-2-6-3v-1h-1c-1 0-1-1-2-2h-1-1-1v-2-1c1 0 0-1 1 0l1-1c-1 0-1-1-1-1v-1l-1-1v-1h-1 0l-1-1 1-1z"></path><path d="M88 288v-1c1 0 0-1 1 0l2 2v1h-1-1-1v-2z" class="L"></path><defs><linearGradient id="AJ" x1="126.143" y1="277.618" x2="130.191" y2="297.822" xlink:href="#B"><stop offset="0" stop-color="#7d7c7c"></stop><stop offset="1" stop-color="#b3b3b3"></stop></linearGradient></defs><path fill="url(#AJ)" d="M113 280l30-1c0 6 1 11 1 17-10 1-19 1-29 1-1-2-1-5-1-8h0v-1h0c-1-1-1-3-1-4v-1c-1 0 0-1 0-1v-2z"></path><path d="M568 255h0l1 1h1 1c-1 1-1 1 0 2l-1 2 3-2s0 1 1 1c1-1 1-1 2 0l3 1v1h-1 0v3 1h1v1h0l1 1-2 5v1c0 2-1 4-1 6h1 1l1 1-3 9c-1 2 0 3-2 5 1 1 1 1 1 2h-3l-1 1h1l-1 1c0 2 0 4-2 5-2 2-5 2-7 3l-5 1-2 4c0 1-1 1 0 3l-16-1c-2 1-4 0-7 0 0-6 2-13 3-19 0-3 1-6 2-9v-1-2l1-1v-3l2-1c2-1 4-2 5-3s3-2 3-4l1-2h0l4-3c1-1 2-1 3-2v-1c0-1 0-2 1-3l1-1c1 0 1 0 1-1l2-2c1 2 1 3 1 5h1c1 0 1 0 1-1 1-1 1-2 2-3l1-1z" class="g"></path><path d="M541 277c2-1 4-2 5-3l1 1c0 2-2 6-3 7-2 0-3 1-5 1l-1-1 1-1v-3l2-1z" class="Q"></path><defs><linearGradient id="AK" x1="565.122" y1="305.627" x2="570.884" y2="297.43" xlink:href="#B"><stop offset="0" stop-color="#545154"></stop><stop offset="1" stop-color="#6d6c6c"></stop></linearGradient></defs><path fill="url(#AK)" d="M572 291c1 1 1 3 1 4s-1 1-1 1v1h1l-1 1c0 2 0 4-2 5-2 2-5 2-7 3l-5 1c1-1 2-2 3-2v-5h0l2 2h1c2-1 4-2 5-4l1-2c0-1 1-3 2-5z"></path><path d="M569 298l1 1c-1 2-3 4-5 4-1 1-1 1-2 0h-1c1-1 1-1 2-1 2-1 4-2 5-4z" class="F"></path><defs><linearGradient id="AL" x1="544.609" y1="290.007" x2="555.81" y2="288.549" xlink:href="#B"><stop offset="0" stop-color="#929192"></stop><stop offset="1" stop-color="#b9b7b8"></stop></linearGradient></defs><path fill="url(#AL)" d="M550 275h0v3c1 1 3 1 5 1h1v1c-2 3-8 20-10 22l-1-1 3-13c1-2 2-5 1-7 0-2 1-4 1-6z"></path><defs><linearGradient id="AM" x1="565.477" y1="286.79" x2="575.725" y2="289.379" xlink:href="#B"><stop offset="0" stop-color="#181818"></stop><stop offset="1" stop-color="#494849"></stop></linearGradient></defs><path fill="url(#AM)" d="M571 281h1 1v-2h-1v-1h1 2c1-1 0-1 0-2s1-1 1-1c1-1 2-2 2-3v1c0 2-1 4-1 6h1 1l1 1-3 9c-1 2 0 3-2 5 1 1 1 1 1 2h-3l-1 1v-1s1 0 1-1 0-3-1-4c-1 2-2 4-2 5 0 0-1 0-1-1l-2 1c-1 0-3 1-5 1v-1c1-1 1-1 1-2v-1h0c1-1 2-2 2-4l1-1v-2-1c1 0 1-1 1-1 1-2 2-3 4-3z"></path><path d="M572 289c1 0 2-1 3-1v5h0v2h-2c0-1 0-3-1-4v-2z" class="T"></path><path d="M578 279h1l1 1-3 9c-1 2 0 3-2 5 1 1 1 1 1 2h-3l-1 1v-1s1 0 1-1h2v-2s0-2 1-2c0-3 1-8 2-10l1-1-1-1z" class="Y"></path><path d="M573 286h1l1 2c-1 0-2 1-3 1v2c-1 2-2 4-2 5 0 0-1 0-1-1l-2 1 2-2 1-5 3-3z" class="S"></path><path d="M573 286h1l1 2c-1 0-2 1-3 1 0-1 0-1 1-3h0z" class="C"></path><defs><linearGradient id="AN" x1="531.051" y1="300.206" x2="546.228" y2="296.358" xlink:href="#B"><stop offset="0" stop-color="#686667"></stop><stop offset="1" stop-color="#969596"></stop></linearGradient></defs><path fill="url(#AN)" d="M533 313c0-6 2-13 3-19 0-3 1-6 2-9v-1-2l1 1c2 0 3-1 5-1-1 1-1 2 0 3l1 1-5 27c-2 1-4 0-7 0z"></path><path d="M540 289l-1 8h0v-1-3h-1c1-1 1-3 2-4z" class="I"></path><defs><linearGradient id="AO" x1="564.905" y1="298.306" x2="555.547" y2="276.71" xlink:href="#B"><stop offset="0" stop-color="#6c6b6c"></stop><stop offset="1" stop-color="#9a9998"></stop></linearGradient></defs><path fill="url(#AO)" d="M578 266h1 0l1 1-2 5c0 1-1 2-2 3 0 0-1 0-1 1s1 1 0 2h-2-1v1h1v2h-1-1 1l1-1h-1c-1-1-1-1-1-2-1 2-3 5-5 5l-13 16c-3 4-6 7-9 10h-1c0-3 1-6 2-8l1 1c2-2 8-19 10-22v-1c4-3 10-6 15-8 1-1 4-3 5-4l-1-1h0 3z"></path><path d="M579 266l1 1-2 5c0 1-1 2-2 3 0 0-1 0-1 1s1 1 0 2h-2-1v1h1v2h-1-1 1l1-1h-1c-1-1-1-1-1-2-1 2-3 5-5 5l13-17z" class="D"></path><path d="M556 279c4-3 10-6 15-8-1 0-1 1-1 2l-2 1c-2 1-4 3-6 4-3 3-5 6-8 10 1-2 3-6 2-8h0v-1z" class="P"></path><path d="M568 255h0l1 1h1 1c-1 1-1 1 0 2l-1 2 3-2s0 1 1 1c1-1 1-1 2 0l3 1v1h-1 0v3 1h1v1h-1-3 0l1 1c-1 1-4 3-5 4-5 2-11 5-15 8h-1c-2 0-4 0-5-1v-3h0c0-1-1-2-1-2l-2 2-1-1c1-1 3-2 3-4l1-2h0l4-3c1-1 2-1 3-2v-1c0-1 0-2 1-3l1-1c1 0 1 0 1-1l2-2c1 2 1 3 1 5h1c1 0 1 0 1-1 1-1 1-2 2-3l1-1z" class="n"></path><path d="M561 266c1 1 2 2 4 2h1c-3 1-15 4-16 6v1c0-1-1-2-1-2l12-7z" class="N"></path><path d="M554 267l5-3h0l2 1v1l-12 7-2 2-1-1c1-1 3-2 3-4l1-2h0l4-3v2z" class="P"></path><path d="M554 265v2l-2 2c-1 0-1 0-2-1h0l4-3z" class="U"></path><path d="M562 255c1 2 1 3 1 5h1c1 0 1 0 1-1l1 2c1 2 2 3 3 5 2 0 2 1 4 1l-7 1h-1c-2 0-3-1-4-2v-1l-2-1h0l-5 3v-2c1-1 2-1 3-2v-1c0-1 0-2 1-3l1-1c1 0 1 0 1-1l2-2z" class="W"></path><path d="M561 265c3 0 6 2 8 1 2 0 2 1 4 1l-7 1h-1c-2 0-3-1-4-2v-1z" class="C"></path><path d="M562 255c1 2 1 3 1 5h1c1 0 1 0 1-1l1 2h-6c-1 1-2 1-3 2v-1c0-1 0-2 1-3l1-1c1 0 1 0 1-1l2-2z" class="L"></path><path d="M562 255c1 2 1 3 1 5v1c-1 0-1 0-2-1v-1l-1-1-1 1v1l-1-1 1-1c1 0 1 0 1-1l2-2z" class="e"></path><defs><linearGradient id="AP" x1="570.518" y1="260.648" x2="578.083" y2="262.441" xlink:href="#B"><stop offset="0" stop-color="#939192"></stop><stop offset="1" stop-color="#b0afaf"></stop></linearGradient></defs><path fill="url(#AP)" d="M568 255h0l1 1h1 1c-1 1-1 1 0 2l-1 2 3-2s0 1 1 1c1-1 1-1 2 0l3 1v1h-1 0v3 1h1v1h-1-3 0l-2 1c-2 0-2-1-4-1-1-2-2-3-3-5l-1-2c1-1 1-2 2-3l1-1z"></path><path d="M568 255h0l1 1h1 1c-1 1-1 1 0 2l-1 2c0 1-1 1-1 2 1 1 1 2 3 3l6 1h-3 0l-2 1c-2 0-2-1-4-1-1-2-2-3-3-5l-1-2c1-1 1-2 2-3l1-1z" class="M"></path><path d="M568 255h0l1 1h1 1c-1 1-1 1 0 2l-1 2c0 1-1 1-1 2l-1-1v-1c-1-1-1-3 0-5z" class="L"></path><path d="M151 279c0 1 0 1 1 2l1 1c2 1 3 2 4 4l1 1c1 1 1 2 3 4h0l1 1 1 11v2h-1l1 1 1 1c1 1 1 2 3 2v1c0 1 1 1 1 1v1l-2 1h0v1c0 1 2 3 2 4 1 1 2 3 2 5v1h0c0 1 0 1-2 2h0v1c-2 1-3 1-4 3v1h0s-1 0-1 1c0 0 0 1-1 1v1 1h-1-1c0 1 0 2-1 3-2-1-4-1-6-1h0 0l-3-1c-2 0-3 0-5 1-1 0-2-2-3-3 2-1 3-2 4-2v-1c-3 0-6 0-9-1-3 0-28 0-29-1v-3-1c0-3 4-5 6-7v1c8-6 14-15 22-20 2-1 3-1 5-2h2 7v-1h-1-5c0-6-1-11-1-17h2 6z" class="S"></path><path d="M110 325l24 1c-3 1-8 0-11 0 2 1 5 0 7 0 1 0 2 0 3 1h-9-10c-2 0-3 0-4-1v-1z" class="g"></path><path d="M141 329c2 0 4 0 6-1h0c1 1 2 1 3 1l1-1s2 1 2 0c1 0 3 0 4-1 2 2 4 4 4 5v3h-1 0c-1-1-2-3-3-4-3-3-8-1-11-1h-2c-1-1-2-1-3-1z" class="T"></path><path d="M146 330c3 0 8-2 11 1 1 1 2 3 3 4l-1 1c-1 0-2-1-2-2-4-2-6-2-11-2v-1c-3 0-6 0-9-1h9z" class="L"></path><path d="M142 334c2-1 3-2 4-2 5 0 7 0 11 2 0 1 1 2 2 2l1-1h0c0 1 0 2-1 3-2-1-4-1-6-1h0 0l-3-1c-2 0-3 0-5 1-1 0-2-2-3-3z" class="R"></path><path d="M150 336h7 1l1 1c-2 1-4 0-6 0h0 0l-3-1z" class="G"></path><path d="M142 334c2-1 3-2 4-2h0v2 1c2 0 4-1 6-1s4 1 5 2h-7c-2 0-3 0-5 1-1 0-2-2-3-3z" class="B"></path><path d="M108 326c1-1 1-1 2-1v1c1 1 2 1 4 1h10c6 1 12 0 17 2 1 0 2 0 3 1h2-9c-3 0-28 0-29-1v-3z" class="C"></path><defs><linearGradient id="AQ" x1="165.002" y1="324.884" x2="161.76" y2="314.418" xlink:href="#B"><stop offset="0" stop-color="#616061"></stop><stop offset="1" stop-color="#7c7b7b"></stop></linearGradient></defs><path fill="url(#AQ)" d="M159 314h0c1 1 4 0 4 0 1-1 2-1 3-1h0 0v1c0 1 2 3 2 4 1 1 2 3 2 5v1h0c0 1 0 1-2 2h0c-1-1-3 0-5 0h-4c1-2 2-7 0-10v3c0-2-1-3-1-5h1z"></path><path d="M114 319l-3 3 23-8h0c0 4 1 8 2 10 1 1 1 1 1 2h-3l-24-1c-1 0-1 0-2 1v-1c0-3 4-5 6-7v1z" class="R"></path><path d="M143 297l1 1c0 1 0 4-1 6h-2c0 1 0 1 1 1s2 0 4 1h0l2 2v1l2 1c0 1 1 1 2 1l3 3c1 2 1 2 1 4 0 1-1 3-2 4h0l1 1h0c-1 1-1 2-2 3s-2 1-3 1c-2 1-6 0-8-1-4-3-6-8-7-11v-2l-1 1-23 8 3-3c8-6 14-15 22-20 2-1 3-1 5-2h2z" class="P"></path><path d="M143 297l1 1c0 1 0 4-1 6h-2c-1 0-1 1-2 1l-1-1c1-1 2-3 2-4l1-3h2z" class="Q"></path><path d="M142 305c1 0 2 0 4 1h0l2 2v1l2 1c0 1 1 1 2 1l3 3c1 2 1 2 1 4 0 1-1 3-2 4h0c-1 1-2 3-5 3l-1-1h-2-1l-3-3c-1 0-2-1-3-2s-1-3-1-4c0 1 0 1-1 2 0-1 0-3-1-4h0c0-1-1-2 0-3 0-2 4-4 6-5z" class="k"></path><path d="M150 318l1-1v-2c1 1 1 2 2 2 0 1 0 3-1 3-1 2-3 3-5 3l-2 1-3-3v-1c2 0 3 2 5 3 2-1 3-2 5-4v-1c-1 0-1 1-2 1h-1l1-1z" class="M"></path><path d="M148 313c1 1 2 1 2 2 1 1 1 2 0 3l-1 1-2 1c-1 0-2-2-3-3v-1c0-2 0-2 1-3h3z" class="W"></path><path d="M146 315c2 0 2 0 3 1v1h-1-1c-1 0-1 0-1-1v-1z" class="r"></path><path d="M150 310c0 1 1 1 2 1l3 3c1 2 1 2 1 4 0 1-1 3-2 4h0c-1 1-2 3-5 3l-1-1h-2-1l2-1c2 0 4-1 5-3 1 0 1-2 1-3-1 0-1-1-2-2v2l-1 1c1-1 1-2 0-3 0-1-1-1-2-2l1-1c0 1 1 1 2 2h0 1c-1-2-2-2-3-3l1-1z" class="a"></path><path d="M142 305c1 0 2 0 4 1h0l2 2v1l2 1-1 1c1 1 2 1 3 3h-1 0c-1-1-2-1-2-2l-1 1h-3c-1 1-1 1-1 3v1l-2-1h-1c0 1 0 3 1 4v1c-1 0-2-1-3-2s-1-3-1-4c0 1 0 1-1 2 0-1 0-3-1-4h0c0-1-1-2 0-3 0-2 4-4 6-5z" class="l"></path><path d="M144 309h4l2 1-1 1-3-1c-1-1-1-1-2-1z" class="F"></path><path d="M140 311c1-1 3-2 4-2s1 0 2 1c-2 1-4 2-5 4 0-2 0-2-1-3z" class="I"></path><path d="M146 310l3 1c1 1 2 1 3 3h-1 0c-1-1-2-1-2-2l-1 1h-3v-1c-1 1-3 2-4 3v-1h0c1-2 3-3 5-4z" class="d"></path><path d="M140 311c1 1 1 1 1 3h0v1c1-1 3-2 4-3v1c-1 1-1 1-1 3v1l-2-1h-1c0 1 0 3 1 4v1c-1 0-2-1-3-2s-1-3-1-4l2-4z" class="c"></path><path d="M140 311c1 1 1 1 1 3h0c-1 1-1 1-1 2s0 2-1 3c-1-1-1-3-1-4l2-4z" class="h"></path><defs><linearGradient id="AR" x1="154.506" y1="283.979" x2="142.546" y2="289.204" xlink:href="#B"><stop offset="0" stop-color="#353536"></stop><stop offset="1" stop-color="#686767"></stop></linearGradient></defs><path fill="url(#AR)" d="M151 279c0 1 0 1 1 2l1 1c2 1 3 2 4 4l1 1c1 1 1 2 3 4h0l1 1 1 11v2h-1l1 1 1 1c1 1 1 2 3 2v1c0 1 1 1 1 1v1l-2 1h0c-1 0-2 0-3 1 0 0-3 1-4 0h0-1c0 2 1 3 1 5-2 3-3 5-6 7 1-1 1-2 2-3h0l-1-1h0c1-1 2-3 2-4 0-2 0-2-1-4l-3-3c-1 0-2 0-2-1l-2-1v-1l-2-2h0c-2-1-3-1-4-1s-1 0-1-1h2c1-2 1-5 1-6l-1-1h7v-1h-1-5c0-6-1-11-1-17h2 6z"></path><path d="M156 285v1c0 1-2 1-3 2l-1-1c1 0 2 0 3-1h0l1-1z" class="C"></path><path d="M152 287l1 1c1 1 2 3 1 5 0 0 0 2-1 3l-1-1h0v-2h-1c0-2 0-4 1-6z" class="j"></path><path d="M153 282c2 1 3 2 4 4l1 1c1 1 1 2 3 4h0l1 1 1 11v2h-1l1 1c-2 0-3-1-5-2h-1s-2-1-3-1h0l-1-1c-1-2-1-4-1-6v-1l1 1c1-1 1-3 1-3 1-2 0-4-1-5 1-1 3-1 3-2v-1l-3-3z" class="M"></path><path d="M153 298l1-1h0c1 0 1-1 1-1h2 3 0 1c0 1 1 3 1 4v3c-2-1-5-2-6-3s-2-2-3-2z" class="g"></path><defs><linearGradient id="AS" x1="159.721" y1="304.54" x2="155.66" y2="298.617" xlink:href="#B"><stop offset="0" stop-color="#6f6f70"></stop><stop offset="1" stop-color="#878585"></stop></linearGradient></defs><path fill="url(#AS)" d="M152 296c1 1 0 2 1 2h0c1 0 2 1 3 2s4 2 6 3h1v2h-1l1 1c-2 0-3-1-5-2h-1s-2-1-3-1h0l-1-1c-1-2-1-4-1-6z"></path><path d="M150 297c2 0 1-2 2-3v1h0v1c0 2 0 4 1 6l1 1h0c1 0 3 1 3 1h1c2 1 3 2 5 2l1 1c1 1 1 2 3 2v1c0 1 1 1 1 1v1l-2 1h0c-1 0-2 0-3 1 0 0-3 1-4 0h0-1c0 2 1 3 1 5-2 3-3 5-6 7 1-1 1-2 2-3h0l-1-1h0c1-1 2-3 2-4 0-2 0-2-1-4l-3-3c-1 0-2 0-2-1l-2-1v-1l-2-2h0c-2-1-3-1-4-1s-1 0-1-1h2c1-2 1-5 1-6 2 0 4-1 6-1z" class="K"></path><path d="M150 297c2 0 1-2 2-3v1h0v1c0 2 0 4 1 6l1 1h0c1 0 3 1 3 1h-2-1l1 1v1h-3c0 1 0 1 1 1 2 2 5 4 5 7h1-1c-1-1-2-3-3-4s-3-2-4-3l-3-2c-1-1-2-1-4-1v-2-3c3 0 5 1 6 3v-1-4z" class="a"></path><path d="M144 302h3l1 1v2h0c-1-1-2-1-4-1v-2z" class="O"></path><path d="M150 297c2 0 1-2 2-3v1h0v1c0 2 0 4 1 6v2c-1 1-2 1-2 0-1-1-1-1-1-2v-1-4z" class="q"></path><path d="M159 314h-1c0-3-3-5-5-7-1 0-1 0-1-1h3v-1l-1-1h1 2 1c2 1 3 2 5 2l1 1c1 1 1 2 3 2v1c0 1 1 1 1 1v1l-2 1h0c-1 0-2 0-3 1 0 0-3 1-4 0h0z" class="f"></path><path d="M396 116c1-1 2-2 2-3s0-1 1-2l2 1c1 2 1 3 1 5l-1 2c0 2-1 3 0 5v2 2c1 1 1 2 0 3h0c-1 3-3 5-4 7-1 1-2 3-2 4 0 2 1 5 2 6 0 1 1 3 2 3v3c1 1 1 3 2 4v1c-1 2 2 3-1 5h-1l-1 1c1 2 2 3 3 5h0v2l1 1-1 1v2l-3 6h-1c0 1-1 2-1 2l-2-1-2-2h-2-1l2 1h0c1 1 2 3 3 4 0 2 0 5 1 7l-3 3c-2 1-3 0-5 0h-1 0v-1h-1c0-1 0-1-1-2v-1c0 2 0 3-1 4v1h-1-1-1-1c-1-1-2-2-2-3s0-1-1-1v-3h0l1-1-1-2h1v-2-1l2-1-1-1c-1 0-2 0-4-1l-4-3 2-1h-1c-1-1-1-2-1-3h-2l-1-1h0-1c-1-2-2-3-2-5-1-1 0-2-1-3 0-1 0-1 1-2h0v-2l-1-1h0c3-6 8-11 13-17 0 0 0-1 1-1v-1c1-1 1-2 0-3 1-1 2-3 2-5 1-1 3-4 4-4h1c0-1 1-2 1-3 1 1 1 1 1 3 1-1 1-2 1-3 1-1 1-2 1-2v-1l3-4 2-2c1 0 2-1 3-1z" class="W"></path><path d="M378 141c0 1 1 3 0 4-1 3 0 7-2 9h-1c0-2 2-5 1-7l-1-1 1-2c1-1 2-1 2-3z" class="Q"></path><path d="M392 181c-2-2-8-2-10-5l1-1c1 1 4 3 6 3 1 0 1 0 1-1 2 1 3 2 4 4h1v1l-1 1-2-2z" class="f"></path><defs><linearGradient id="AT" x1="366.76" y1="171.863" x2="371.624" y2="165.293" xlink:href="#B"><stop offset="0" stop-color="#9f9d9e"></stop><stop offset="1" stop-color="#bebcbc"></stop></linearGradient></defs><path fill="url(#AT)" d="M367 173v-1l-1-1c-1-4 1-10 3-14 0 3-2 6-1 8 2-1 2-2 2-3l1 1h0c0 2 1 4 0 6v1-2h-1c0 2 0 3 1 5v1h-1-2l-1-1z"></path><defs><linearGradient id="AU" x1="376.949" y1="154.274" x2="381.975" y2="154.196" xlink:href="#B"><stop offset="0" stop-color="#7a7979"></stop><stop offset="1" stop-color="#939292"></stop></linearGradient></defs><path fill="url(#AU)" d="M381 144l2-1c0 1-1 2 0 4h0c-4 7-4 15-2 23-1-2-2-5-4-6-1-7 1-12 3-18l1-2z"></path><path d="M376 143v1l-1 2c-2 2-3 5-5 8 0 1-1 2-1 3-2 4-4 10-3 14l1 1v1h0-1c-1-2-2-3-2-5-1-1 0-2-1-3 0-1 0-1 1-2h0v-2l-1-1h0c3-6 8-11 13-17z" class="N"></path><path d="M385 126c1 1 1 1 1 3l-3 9v2 1h0v2l-2 1-1 2-2 2c1-1 0-2 0-3 1-1 0-3 0-4 0 2-1 2-2 3v-1s0-1 1-1v-1c1-1 1-2 0-3 1-1 2-3 2-5 1-1 3-4 4-4h1c0-1 1-2 1-3z" class="p"></path><path d="M383 141h0v2l-2 1 2-3z" class="a"></path><path d="M379 133c1-1 3-4 4-4l-5 12c0 2-1 2-2 3v-1s0-1 1-1v-1c1-1 1-2 0-3 1-1 2-3 2-5z" class="P"></path><path d="M396 116c1-1 2-2 2-3s0-1 1-2l2 1c1 2 1 3 1 5l-1 2c0 2-1 3 0 5v2 2c-1 0-3 2-4 4l-1 1v1 1l-1 1c-2 2-3 3-4 5l-1 2c-1 1-2 2-2 3-1-1-1-1-2-1l1-2h-1l-2 1c0 1-1 2-1 3-1-2 0-3 0-4v-2h0v-1-2l3-9c1-1 1-2 1-3 1-1 1-2 1-2v-1l3-4 2-2c1 0 2-1 3-1z" class="U"></path><path d="M388 123c1 1 1 1 2 1 0 0 0-1 1-1 0-1 1-1 1-1v1c-1 0-1 1-2 2s-2 4-3 6-1 7-4 7l3-9c1-1 1-2 1-3 1-1 1-2 1-2v-1zm-5 17c3 0 6-5 8-8l7-9c1 0 1 0 1 1s-1 2-2 2c-1 2 0 0-1 1 0 1-2 2-2 3l-9 10-1 1v3c0 1-1 2-1 3-1-2 0-3 0-4v-2h0v-1z" class="K"></path><path d="M396 116c1-1 2-2 2-3s0-1 1-2l2 1c0 1-1 1-1 1-1 1-2 2-2 3l-6 6s-1 0-1 1c-1 0-1 1-1 1-1 0-1 0-2-1l3-4 2-2c1 0 2-1 3-1z" class="H"></path><defs><linearGradient id="AV" x1="393.934" y1="124.295" x2="395.008" y2="136.408" xlink:href="#B"><stop offset="0" stop-color="#a1a09e"></stop><stop offset="1" stop-color="#bebdbf"></stop></linearGradient></defs><path fill="url(#AV)" d="M398 123c1-2 2-3 3-4 0 2-1 3 0 5v2 2c-1 0-3 2-4 4l-1 1v1 1l-1 1c-2 2-3 3-4 5l-1 2c-1 1-2 2-2 3-1-1-1-1-2-1l1-2h-1l-2 1v-3l1-1 9-10c0-1 2-2 2-3 1-1 0 1 1-1 1 0 2-1 2-2s0-1-1-1z"></path><path d="M401 126v2c-1 0-3 2-4 4l-1 1v1l-1-1c0-2 4-5 6-7z" class="U"></path><path d="M396 134v-1 1 1l-1 1c-2 2-3 3-4 5l-1 2c-1 1-2 2-2 3-1-1-1-1-2-1l1-2h-1l-2 1v-3l1-1h2c1-1 5-5 7-6h2z" class="d"></path><path d="M387 143c2-2 5-7 9-8l-1 1c-2 2-3 3-4 5l-1 2c-1 1-2 2-2 3-1-1-1-1-2-1l1-2z" class="I"></path><path d="M371 169l1 2v-1-2l-1-1c1-1 1-2 2-3h0v1l1 3c0 1 1 2 1 3 1 1 1 2 2 3l1 1c3 3 8 5 13 7 1 1 2 3 3 4 0 2 0 5 1 7l-3 3c-2 1-3 0-5 0h-1 0v-1h-1c0-1 0-1-1-2v-1c0 2 0 3-1 4v1h-1-1-1-1c-1-1-2-2-2-3s0-1-1-1v-3h0l1-1-1-2h1v-2-1l2-1-1-1c-1 0-2 0-4-1l-4-3 2-1h-1c-1-1-1-2-1-3h1v-1c-1-2-1-3-1-5h1v2-1z" class="i"></path><path d="M371 169l1 2v-1-2l-1-1c1-1 1-2 2-3h0v1c0 4 1 8 3 11l1 1 1 2c-1-1-2-1-3-1h0c-2-3-3-5-4-8v-1z" class="m"></path><defs><linearGradient id="AW" x1="375.018" y1="181.314" x2="371.783" y2="173.21" xlink:href="#B"><stop offset="0" stop-color="#6d6d6d"></stop><stop offset="1" stop-color="#858384"></stop></linearGradient></defs><path fill="url(#AW)" d="M371 173c-1-2-1-3-1-5h1v2c1 3 2 5 4 8h0c1 0 2 0 3 1 2 1 3 1 4 2l2 3h-1c-1-1-3-1-4-1l-1-1c-1 0-2 0-4-1l-4-3 2-1h-1c-1-1-1-2-1-3h1v-1z"></path><path d="M371 173c0 1 1 3 1 4h-1c-1-1-1-2-1-3h1v-1z" class="X"></path><path d="M375 178c1 0 2 0 3 1 2 1 3 1 4 2l2 3h-1c-1-1-3-1-4-1l-1-1c0-1 0-2-1-2-1-1-1-2-2-2z" class="F"></path><path d="M382 181c2 0 3 1 4 2h1c2 0 3 1 5 2 1 3 2 6 0 8 0 1 0 1-1 2h0v1h1c-2 1-3 0-5 0h-1 0v-1c1-2 2-3 1-5 0-3-1-5-3-6l-2-3z" class="l"></path><path d="M382 181c2 0 3 1 4 2 2 3 3 6 3 10 0 1-1 2-2 2l-1 1v-1c1-2 2-3 1-5 0-3-1-5-3-6l-2-3z" class="a"></path><path d="M379 183c1 0 3 0 4 1h1c2 1 3 3 3 6 1 2 0 3-1 5h-1c0-1 0-1-1-2v-1c0 2 0 3-1 4v1h-1-1-1-1c-1-1-2-2-2-3s0-1-1-1v-3h0l1-1-1-2h1v-2-1l2-1z" class="V"></path><path d="M382 184c1 1 2 1 3 2-1 0-2 0-3 1l-1 1s-1 0-1-1h0c0-1 1-2 2-3z" class="F"></path><path d="M379 183c1 0 3 0 4 1h-1c-1 1-2 2-2 3v1h-2l-1-3v-1l2-1z" class="Z"></path><path d="M383 184h1c2 1 3 3 3 6 1 2 0 3-1 5h-1c0-1 0-1-1-2v-1h0l-1-1-1-1v-1h3l1-1c-1-1-1-1-1-2-1-1-2-1-3-2h1z" class="R"></path><path d="M377 185l1 3v1l1 1h3l1 1 1 1h0c0 2 0 3-1 4v1h-1-1-1-1c-1-1-2-2-2-3s0-1-1-1v-3h0l1-1-1-2h1v-2z" class="I"></path><path d="M377 185l1 3v1 3h-1v-3l-1-2h1v-2z" class="X"></path><path d="M383 191l1 1h0c0 2 0 3-1 4v1l-1-2h-1v1h-2 0v-1c0-1 0-2 1-3v-1h3z" class="d"></path><path d="M383 191l1 1-1 1c-1 0-2 1-3 0v-1-1h3z" class="Q"></path><path d="M401 128c1 1 1 2 0 3h0c-1 3-3 5-4 7-1 1-2 3-2 4 0 2 1 5 2 6 0 1 1 3 2 3v3c1 1 1 3 2 4v1c-1 2 2 3-1 5h-1l-1 1c1 2 2 3 3 5h0v2l1 1-1 1v2l-3 6h-1c0 1-1 2-1 2l-2-1 1-1v-1h-1c-1-2-2-3-4-4-2-2-4-4-5-7-4-9-2-17 1-25 1 0 1 0 2 1 0-1 1-2 2-3l1-2c1-2 2-3 4-5l1-1v-1-1l1-1c1-2 3-4 4-4z" class="d"></path><path d="M395 151v-5l1 5h-1z" class="H"></path><path d="M390 156c0 1 0 3 1 5s1 4 3 6l-1 1h0c1 1 0 2 0 3-1-1-1-3-2-4l-2-7 1 1v-5z" class="W"></path><path d="M401 128c1 1 1 2 0 3-1 0-2 2-3 3l-2 3h0v-1h-1l1-1v-1-1l1-1c1-2 3-4 4-4z" class="i"></path><path d="M394 143c0 3-1 7-2 10h0c0 1 0 1-1 2-1 2 0 4 0 6-1-2-1-4-1-5 0-4 0-7 2-10l2-3z" class="U"></path><defs><linearGradient id="AX" x1="397.306" y1="162.769" x2="392.148" y2="153.938" xlink:href="#B"><stop offset="0" stop-color="#817f81"></stop><stop offset="1" stop-color="#9a9a99"></stop></linearGradient></defs><path fill="url(#AX)" d="M393 158c0-3 0-6 1-8v5c1 2 1 4 2 6l3 3h1-1l-1 1h0c-2-1-4-3-4-5l-1-2z"></path><path d="M393 158l1 2c0 2 2 4 4 5h0c1 2 2 3 3 5h0v2l1 1-1 1-1 1-1-2s1 0 1-1v-1c-1-2-2-3-3-4 0-1 0-1-1-1 0 0 0-1-1-1-2-2-2-4-2-7z" class="H"></path><path d="M394 167l1 1 2-1c1 1 2 2 3 4-1 3-2 1-3 1l-1 1c-1 0-2-1-3-2 0-1 1-2 0-3h0l1-1z" class="s"></path><path d="M395 136h1v1l-2 6-2 3c-2 3-2 6-2 10v5l-1-1c-1-2-1-9-1-12 1-2 2-4 2-5l1-2c1-2 2-3 4-5z" class="q"></path><path d="M395 151h1c1 1 1 2 3 3h0c1 1 1 3 2 4v1c-1 2 2 3-1 5h-1l-3-3c-1-2-1-4-2-6l1-1v-3z" class="L"></path><path d="M398 160l2 2-1 2-3-3 2-1z" class="U"></path><path d="M394 155l1-1c1 2 1 4 2 5l1 1-2 1c-1-2-1-4-2-6z" class="Q"></path><path d="M388 146c0-1 1-2 2-3 0 1-1 3-2 5 0 3 0 10 1 12l2 7c1 1 1 3 2 4s2 2 3 2l1-1c1 0 2 2 3-1v1c0 1-1 1-1 1l1 2 1-1v2l-3 6h-1c0 1-1 2-1 2l-2-1 1-1v-1h-1c-1-2-2-3-4-4-2-2-4-4-5-7-4-9-2-17 1-25 1 0 1 0 2 1z" class="G"></path><path d="M397 173c0 2 0 3-2 4-1 0-1 0-2-1 1 0 1-1 2-1v-1l2-1z" class="H"></path><path d="M391 167c1 1 1 3 2 4s2 2 3 2l1-1c1 0 2 2 3-1v1c0 1-1 1-1 1h-2 0l-2 1v1c-1 0-1 1-2 1l-3-3c1 0 1 0 2-1l1-1c-1 0-1-1-1-2 0 0 0-1-1-1v-1z" class="U"></path><path d="M401 174v2l-3 6h-1c0 1-1 2-1 2l-2-1 1-1v-1-1c2-2 4-3 5-5l1-1z" class="i"></path><defs><linearGradient id="AY" x1="390.523" y1="159.45" x2="385.627" y2="160.793" xlink:href="#B"><stop offset="0" stop-color="#abaaa9"></stop><stop offset="1" stop-color="#dbdbdc"></stop></linearGradient></defs><path fill="url(#AY)" d="M388 146c0-1 1-2 2-3 0 1-1 3-2 5 0 3 0 10 1 12l2 7v1c1 0 1 1 1 1 0 1 0 2 1 2l-1 1c-1 1-1 1-2 1l-2-3s0-1-1-1c-3-7-2-16 1-23z"></path><path d="M388 170l1-1 1 1h0v-1c-1-1-2-3-2-5h0v1l1 1 3 6h0c-1 1-1 1-2 1l-2-3z" class="p"></path><path d="M553 227c2 2 4 4 6 5l-2 1c2 3 3 5 3 7v1 2l-1 2-1 1h0l-1 3c0 2-1 3 0 4v1l-1 1v1l3 2-1 1c-1 1-1 2-1 3v1c-1 1-2 1-3 2l-4 3h0l-1 2c0 2-2 3-3 4s-3 2-5 3l-2 1v3l-1 1v2 1c-1 3-2 6-2 9-1 6-3 13-3 19-1 0-2 0-2 1-1-1-1-1-1-2l-1 1h0l-2-4s-1-1-2-1l-1-5c-1-4 0-8 0-12h-1c1-1 1-2 1-3-1-2-2-6-2-7l-2-7c1 0 1 0 2-1 0-1-1-1-1-2l-1-1h1l-1-2c-1 1-2 1-2 2l-1-2-3-6-1-2c-1-1-1-2-1-3l-2-5 1-1h1v-5h0c1-1 1-2 1-2l3-4c1-1 2-2 4-3l4 1 1-1-2-1-4-3 1-1c2 1 4 3 5 5h1 1s1 1 2 1c2 1 4 3 6 4h1l3 4c1-1 1-1 1-2 2 2 3 3 4 5h0v-2c0-2 2-4 2-5l1-1c1-1 2-1 3-2 1-4 2-7 3-11v-1z" class="g"></path><path d="M532 281c1 1 2 3 2 6h-1c0 2 0 4-1 6v-12z" class="J"></path><path d="M545 258l1-1v1c0 1 0 2-1 3l1 2h1c0 1 0 3-1 5v1l-2 2c-1 0-1 0-2-1-1 1 0 2 0 3h0l-1-1c-1 0-1 2-1 3v1l1 1-2 1v3-15c2 0 3 0 4-1 0 0 0-1 1-2 0-1 0-3 1-5z" class="E"></path><path d="M544 271v-1c0-2 0-2 1-3l1 1v1l-2 2z" class="M"></path><path d="M545 267v-6l1 2h1c0 1 0 3-1 5l-1-1z" class="D"></path><defs><linearGradient id="AZ" x1="541.415" y1="265.996" x2="552.991" y2="263.205" xlink:href="#B"><stop offset="0" stop-color="#838181"></stop><stop offset="1" stop-color="#aaa8aa"></stop></linearGradient></defs><path fill="url(#AZ)" d="M553 252l1-1c0-1 2-1 3-2 0 2-1 3 0 4v1l-1 1v1l3 2-1 1c-1 1-1 2-1 3v1c-1 1-2 1-3 2l-4 3h0l-1 2c0 2-2 3-3 4s-3 2-5 3l-1-1v-1c0-1 0-3 1-3l1 1h0c0-1-1-2 0-3 1 1 1 1 2 1l2-2v-1c1-2 1-4 1-5 1-2 1-6 2-7 0-1 1-1 2-2l2-2z"></path><path d="M542 273l1 1c2-1 4-2 5-4h1 0c0 2-2 3-3 4s-3 2-5 3l-1-1v-1c0-1 0-3 1-3l1 1h0z" class="C"></path><path d="M547 263c1-2 1-6 2-7 0-1 1-1 2-2v3l-2 6c-1 1-1 3-1 5l-1-1c0 1 0 1-1 2v-1c1-2 1-4 1-5z" class="L"></path><path d="M553 252l1-1c0-1 2-1 3-2 0 2-1 3 0 4v1l-1 1v1l3 2-1 1c-1 1-1 2-1 3v1c-1 1-2 1-3 2l-4 3c0-1 0-1 1-1l1-9v-1h-1v-3l2-2z" class="S"></path><path d="M553 252c0 2 0 4-1 6v-1h-1v-3l2-2z" class="H"></path><path d="M554 265c0-3 1-6 2-9l3 2-1 1c-1 1-1 2-1 3v1c-1 1-2 1-3 2z" class="X"></path><path d="M525 268c6-3 11-8 16-11 2-2 4-2 6-3 4-2 8-6 10-9l1 1-1 3c-1 1-3 1-3 2l-1 1-2 2c-1 1-2 1-2 2-1 1-1 5-2 7h-1l-1-2c1-1 1-2 1-3v-1l-1 1h-1c-2 0-2 1-4 2-1 1-2 3-4 4l-2 1c0 1 0 0-1 1 0 0-1 0-1 1h-1c0 1-1 2-1 3h0l1 2-1 2-1 1h0-1v2c-1-2-1-2-2-2 0 0-1 1-1 2 0 5 0 9-1 14h0-1c1-1 1-2 1-3-1-2-2-6-2-7l-2-7c1 0 1 0 2-1 0-1 0-2 1-2h0c0-1 1-2 2-3z" class="J"></path><path d="M528 272l2-2 1 2-1 2-1 1h0-1v2c-1-2-1-2-2-2l2-3z" class="X"></path><path d="M528 272l1 3h0-1v2c-1-2-1-2-2-2l2-3z" class="h"></path><path d="M522 273c0-1 0-2 1-2v1c1 2 1 1 1 3h0-1c-1 2-1 4-1 6l-2-7c1 0 1 0 2-1z" class="a"></path><path d="M531 272l1 9v12l-2 19-1 1h0l-2-4s-1-1-2-1l-1-5c-1-4 0-8 0-12h0c1-5 1-9 1-14 0-1 1-2 1-2 1 0 1 0 2 2v-2h1 0l1-1 1-2z" class="L"></path><path d="M527 297v2 10s-1-1-2-1l-1-5h0l1 1c1-2 1-5 2-7z" class="V"></path><path d="M528 277v-2h1c2 8 0 16-2 24v-2c0-1 0-2 1-3v-17z" class="B"></path><defs><linearGradient id="Aa" x1="522.642" y1="288.074" x2="527.756" y2="289.27" xlink:href="#B"><stop offset="0" stop-color="#6d6d6c"></stop><stop offset="1" stop-color="#898789"></stop></linearGradient></defs><path fill="url(#Aa)" d="M526 275c1 0 1 0 2 2v17c-1 1-1 2-1 3-1 2-1 5-2 7l-1-1h0c-1-4 0-8 0-12h0c1-5 1-9 1-14 0-1 1-2 1-2z"></path><path d="M553 227c2 2 4 4 6 5l-2 1c2 3 3 5 3 7v1 2l-1 2-1 1h0l-1-1c-2 3-6 7-10 9-2 1-4 1-6 3-5 3-10 8-16 11-1 1-2 2-2 3h0c-1 0-1 1-1 2 0-1-1-1-1-2v-4c1 0 1-1 2-2h0c2-3 5-5 8-7 2-2 4-3 6-4v1l1-1 1-1c1 0 2 0 3-1h0l1-1v-1l1-1h0v-2c0-2 2-4 2-5l1-1c1-1 2-1 3-2 1-4 2-7 3-11v-1z" class="X"></path><defs><linearGradient id="Ab" x1="547.864" y1="244.078" x2="540.495" y2="257.785" xlink:href="#B"><stop offset="0" stop-color="#acacab"></stop><stop offset="1" stop-color="#cfccd0"></stop></linearGradient></defs><path fill="url(#Ab)" d="M549 247l5-2h1c0 1-1 1-1 2l-6 4c-1 0-2 1-3 1-1 1-2 2-3 2l-1 1h-1-3v1-1l1-1 1-1c1 0 2 0 3-1h0l1-1v-1h1c2-1 3-2 5-3h0z"></path><defs><linearGradient id="Ac" x1="522.948" y1="270.489" x2="533.901" y2="254.157" xlink:href="#B"><stop offset="0" stop-color="#979597"></stop><stop offset="1" stop-color="#c7c6c6"></stop></linearGradient></defs><path fill="url(#Ac)" d="M537 254v1 1-1h3l-4 3-3 3h-1c-1 0-1 1-2 2-1 0-1 1-2 1-1 1-3 3-3 4-1 1-2 2-2 3h0c-1 0-1 1-1 2 0-1-1-1-1-2v-4c1 0 1-1 2-2h0c2-3 5-5 8-7 2-2 4-3 6-4z"></path><path d="M553 227c2 2 4 4 6 5l-2 1c2 3 3 5 3 7l-1 1c0 1 0 2-1 3h-1l-1-1-2 2-5 2h0c-2 1-3 2-5 3h-1l1-1h0v-2c0-2 2-4 2-5l1-1c1-1 2-1 3-2 1-4 2-7 3-11v-1z" class="e"></path><path d="M550 239c1-4 2-7 3-11 0 2 0 4-1 6v2h0c0 2 0 4-1 5 0 1-2 4-2 4v2h0c-1 0-1 0-2-1 1-3 2-5 3-7z" class="M"></path><path d="M546 242l1-1c1-1 2-1 3-2-1 2-2 4-3 7 1 1 1 1 2 1-2 1-3 2-5 3h-1l1-1h0v-2c0-2 2-4 2-5z" class="G"></path><path d="M556 243l-2-7c0-2 0-3 1-4 1 0 2 1 2 1 2 3 3 5 3 7l-1 1c0 1 0 2-1 3h-1l-1-1z" class="m"></path><path d="M556 236h1v2c-1 0-1 0-2-1l1-1z" class="W"></path><path d="M520 232c2 1 4 3 5 5h1 1s1 1 2 1c2 1 4 3 6 4h1l3 4c1-1 1-1 1-2 2 2 3 3 4 5l-1 1v1l-1 1h0c-1 1-2 1-3 1l-1 1-1 1v-1c-2 1-4 2-6 4-3 2-6 4-8 7h0c-1 1-1 2-2 2v4l-1-1h1l-1-2c-1 1-2 1-2 2l-1-2-3-6-1-2c-1-1-1-2-1-3l-2-5 1-1h1v-5h0c1-1 1-2 1-2l3-4c1-1 2-2 4-3l4 1 1-1-2-1-4-3 1-1z" class="L"></path><path d="M524 238c3 0 5 1 7 3l1 1c-1-1-3-2-5-2h-2c0-1 0-1-1-2z" class="d"></path><path d="M518 246c1 1-1 5-1 7-1 1-1 2 0 4v5c0 2 1 4 0 6l-3-6v-2l1-1c0-2 0-4 1-7l2-5v-1z" class="h"></path><path d="M535 242h1l3 4c1-1 1-1 1-2 2 2 3 3 4 5l-1 1v1l-1 1h0c-1 1-2 1-3 1v-1c0-1 0-2 1-3-2-2-3-5-5-7z" class="Q"></path><path d="M540 249l2 3c-1 1-2 1-3 1v-1c0-1 0-2 1-3z" class="K"></path><path d="M540 244c2 2 3 3 4 5l-1 1v1l-4-5c1-1 1-1 1-2z" class="V"></path><path d="M520 237l4 1h0c1 1 1 1 1 2-2 1-5 3-7 5v1 1h-1 0c-1-1-1-2-2-3 0 1-1 1-1 1l-1-1 3-4c1-1 2-2 4-3z" class="U"></path><defs><linearGradient id="Ad" x1="510.843" y1="256.264" x2="515.993" y2="249.353" xlink:href="#B"><stop offset="0" stop-color="#727073"></stop><stop offset="1" stop-color="#898887"></stop></linearGradient></defs><path fill="url(#Ad)" d="M513 244l1 1s1 0 1-1c1 1 1 2 2 3h0 1l-2 5c-1 3-1 5-1 7l-1 1v2l-1-2c-1-1-1-2-1-3l-2-5 1-1h1v-5h0c1-1 1-2 1-2z"></path><path d="M513 244l1 1s1 0 1-1c1 1 1 2 2 3h0 1l-2 5h-1v-1c0-2 0-4-3-5 1-1 1-2 1-2z" class="X"></path><path d="M511 251h1c1 3 1 7 2 9v2l-1-2c-1-1-1-2-1-3l-2-5 1-1z" class="R"></path><path d="M520 268c-2-6-3-11 0-17 1-3 2-5 5-6 2-1 6 0 8 1 1 0 2 1 2 1-1 1-2 0-3 2-2-1-3-1-5-1l-3 2 1 1-2 3c-1 2-2 8 0 10v1h0c-1 1-1 2-2 2v4l-1-1h1l-1-2z" class="G"></path><path d="M521 254c1-2 2-3 3-4l1 1-2 3s0-1-1-1v1h-1z" class="F"></path><path d="M527 248v-1c1-1 2 0 3-1h3c1 0 2 1 2 1-1 1-2 0-3 2-2-1-3-1-5-1z" class="B"></path><path d="M521 254h1v-1c1 0 1 1 1 1-1 2-2 8 0 10v1h0c-1 1-1 2-2 2v4l-1-1h1c2-5-3-12 0-16z" class="c"></path><path d="M532 249c1-2 2-1 3-2l4 5v1l-1 1-1 1v-1c-2 1-4 2-6 4-3 2-6 4-8 7v-1c-2-2-1-8 0-10l2-3-1-1 3-2c2 0 3 0 5 1z" class="g"></path><path d="M532 249c1-2 2-1 3-2l4 5v1l-1 1-1 1v-1h0c-1-2-4-4-6-4-2-1-4 0-6 1l-1-1 3-2c2 0 3 0 5 1z" class="X"></path><path d="M532 249c1-2 2-1 3-2l4 5v1l-1 1c-2-2-3-4-6-5z" class="T"></path><defs><linearGradient id="Ae" x1="313.936" y1="136.448" x2="347.577" y2="166.762" xlink:href="#B"><stop offset="0" stop-color="#bcbbbb"></stop><stop offset="1" stop-color="#e2e2e3"></stop></linearGradient></defs><path fill="url(#Ae)" d="M334 106l3 3 3 4 1 1h0l1-1v1h1l1 3 1 2c1 1 3 4 3 6l1-1c1 1 1 2 2 2h0 2c0 2 1 2 2 3h0c1 1 2 1 3 2l1 1c-1 1-2 2-2 4-1 0-1 1-2 1l1 1c1 0 1 0 1 1l-1 3h0c2-1 3-3 3-4 1-1 1-1 1-2s1-2 1-3c1 1 2 1 2 0h1c0 2 1 4 2 5l2 3h1 0l1-1 1 5c2-2 4-4 6-7 1 1 1 2 0 3v1c-1 0-1 1-1 1-5 6-10 11-13 17l-2 3c0-1-1-2-1-3v-1l-1 1v2 1c-1 1-1 1-2 0 0 3 0 6-1 8-1 1-1 2-1 3-2 1-4 1-4 4h1l-1 1c-1-1-1-1-2-1s-1 1-2 2c0-1-1-1-2-1h0l-2 1-1-1v1l-1 1c-2 0-2 1-3 2-1 0-2 1-2 2v-1l-1-1c1-2 2-3 4-4h0c-2 0-5 3-6 3l-1-1h1v-2c-1 0-1 1-1 2-2 0-3 1-5 0h0 0c-3-2-5-5-6-8h2-1l3-3c2-1 3-2 5-3h0c0-1 1-1 2-2v-1h1l1-1v-2c0-1 1-2 1-2 1-4 0-7 0-11 0-2-1-4-2-6-2-1-3-3-4-4l-1 1c-1-1-2-4-2-5v-1l1 1v-1l-3-4h1l-2-1v-1c1 0 2 1 2 2h2l-3-3c-2-2-1-3-1-5v-2c2-1 0-2 2-4l3 3c2 1 4 4 5 5l1 1 1-1-2-2c1-1 1-1 2-1v-1c0-1-1-2-2-3l1-1c-1-1-1-3-2-4v-1c0-1 1-1 2-2l-1-1h1v-1z"></path><path d="M330 136h2 1c1 1 2 1 2 2 1 2 1 3 2 5v2l-7-9z" class="f"></path><path d="M327 133l3 3 7 9c1 1 1 2 1 3h0v5c-1-2-2-3-3-5 0-2-1-4-2-6-2-1-3-3-4-4l-1 1c-1-1-2-4-2-5v-1l1 1v-1z" class="L"></path><path d="M328 139c-1-1-2-4-2-5v-1l1 1c2 2 5 5 6 8-2-1-3-3-4-4l-1 1z" class="U"></path><path d="M337 139c2 1 3 3 5 4h0l3 6 1 6c1 6 0 11-4 16 0-3 1-6 1-8 1-5 0-10-1-15-2-3-4-6-5-9z" class="c"></path><path d="M325 115l3 3c2 1 4 4 5 5l1 1 1-1c1 1 1 3 2 4 0 2 1 4 1 6 1 2 1 3 1 5 2 2 3 3 3 5h0c-2-1-3-3-5-4h-1v-1c0-3-6-7-8-9h-1l-3-3c-2-2-1-3-1-5v-2c2-1 0-2 2-4z" class="U"></path><path d="M334 134c1-1 2-1 3-1 0 1 0 2 1 3l-1 1-3-3zm3-7c0 2 1 4 1 6l-1-1c-2-1-2-3-2-4l2-1z" class="Q"></path><path d="M325 124c1 1 2 2 4 3 1 1 3 2 4 4-2-1-3-1-4-2-2-2-3-3-4-5z" class="P"></path><path d="M323 121c1 1 1 2 2 3 1 2 2 3 4 5h-1-1l-3-3c-2-2-1-3-1-5z" class="H"></path><path d="M329 129c1 1 2 1 4 2 0 1 0 2 1 3l3 3c1 0 2 1 2 1 2 2 3 3 3 5h0c-2-1-3-3-5-4h-1v-1c0-3-6-7-8-9h1z" class="d"></path><defs><linearGradient id="Af" x1="342.871" y1="161.262" x2="322.284" y2="169.811" xlink:href="#B"><stop offset="0" stop-color="#6a6969"></stop><stop offset="1" stop-color="#888787"></stop></linearGradient></defs><path fill="url(#Af)" d="M338 153v-5h0c3 8 3 16-1 24l-1 2c0 1-1 2-1 4l1 1-3 2v-2c-1 0-1 1-1 2-2 0-3 1-5 0h0 0c-3-2-5-5-6-8h2-1l3-3c2-1 3-2 5-3h0c0-1 1-1 2-2v-1h1l1-1v-2c0-1 1-2 1-2 1-4 0-7 0-11 1 2 2 3 3 5z"></path><path d="M336 174c0 1-1 2-1 4l1 1-3 2v-2c0-2 1-3 3-5z" class="l"></path><path d="M321 173h2c2 3 3 5 6 7h1 1l1 1c-2 0-3 1-5 0h0 0c-3-2-5-5-6-8z" class="K"></path><path d="M335 148c1 2 2 3 3 5v8c-1 4-2 10-6 13-2 1-2 1-4 1h0c-1-1-1-2-2-3l-1-2c2-1 3-2 5-3h0c0-1 1-1 2-2v-1h1l1-1v-2c0-1 1-2 1-2 1-4 0-7 0-11z" class="f"></path><path d="M330 167h0c0-1 1-1 2-2v-1h1c-1 2-3 7-5 8h-2l-1-2c2-1 3-2 5-3z" class="l"></path><path d="M328 175v-2c1-1 3-2 4-4 2-2 3-6 4-9 0-1 1-2 1-2h1v3c-1 4-2 10-6 13-2 1-2 1-4 1z" class="q"></path><path d="M347 146c5 4 9 11 10 17 0 3 0 6-1 8-1 1-1 2-1 3-2 1-4 1-4 4h1l-1 1c-1-1-1-1-2-1s-1 1-2 2c0-1-1-1-2-1h0l-2 1-1-1v1l-1 1c-2 0-2 1-3 2-1 0-2 1-2 2v-1l-1-1c1-2 2-3 4-4h0c-2 0-5 3-6 3l-1-1h1l3-2 6-5c6-5 7-12 8-19l-2-6c-1-1-1-2-1-3z" class="P"></path><defs><linearGradient id="Ag" x1="339.301" y1="167.463" x2="349.159" y2="168.953" xlink:href="#B"><stop offset="0" stop-color="#b5b2b4"></stop><stop offset="1" stop-color="#d4d4d4"></stop></linearGradient></defs><path fill="url(#Ag)" d="M350 155c1 5 2 12-2 17-2 3-5 4-8 6 0 1-1 1-1 1h0c-2 0-5 3-6 3l-1-1h1l3-2 6-5c6-5 7-12 8-19z"></path><path d="M334 106l3 3 3 4 1 1h0l1-1v1h1l1 3 1 2c1 1 3 4 3 6l1-1c1 1 1 2 2 2h0 2c0 2 1 2 2 3h0c1 1 2 1 3 2l1 1c-1 1-2 2-2 4-1 0-1 1-2 1l1 1c1 0 1 0 1 1l-1 3h0c2-1 3-3 3-4 1-1 1-1 1-2s1-2 1-3c1 1 2 1 2 0h1c0 2 1 4 2 5l2 3h1 0l1-1 1 5c2-2 4-4 6-7 1 1 1 2 0 3v1c-1 0-1 1-1 1-5 6-10 11-13 17l-2 3c0-1-1-2-1-3v-1l-1 1v2 1c-1 1-1 1-2 0-1-6-5-13-10-17v-1h-1c0-1-1-1-1-2l-1-1c0 1-1 2 0 3 0 1 1 2 1 3v1l-3-6c0-2-1-3-3-5 0-2 0-3-1-5 0-2-1-4-1-6-1-1-1-3-2-4l-2-2c1-1 1-1 2-1v-1c0-1-1-2-2-3l1-1c-1-1-1-3-2-4v-1c0-1 1-1 2-2l-1-1h1v-1z" class="r"></path><path d="M345 137v-4l3 3-1 1h-2z" class="B"></path><path d="M358 151c1 1 2 2 4 3 0 0-1 1-1 2l-4-4 1-1z" class="X"></path><path d="M337 115h1c1 1 1 5 1 6v2c-1-2-2-6-2-8z" class="F"></path><path d="M366 150h1c-2 2-4 6-6 6h0c0-1 1-2 1-2l3-4h1z" class="I"></path><path d="M342 134l-3-10h1c0 1 0 1 1 2 0 2 1 4 2 6v2h-1 0z" class="L"></path><path d="M343 132l2 1v4c0 1 1 1 1 2h-1 0c-2-1-2-3-3-5h0 1v-2z" class="X"></path><path d="M348 136l4 2c-1 1-1 1-3 1h-1v2l-2-2c0-1-1-1-1-2h2l1-1z" class="b"></path><path d="M338 113h1 1l1 1h0l1-1v1c0 1-1 1-1 2l-1 1h0v1 1c-1 0-1 1-1 2 0-1 0-5-1-6h-1v-1h0 1v-1z" class="Q"></path><path d="M334 106l3 3 3 4h-1-1c-1-1-1-1-2-1h-1c-1-1-1-2-1-4l-1-1h1v-1z" class="f"></path><path d="M352 138c1 1 2 2 3 2-1 1-2 2-2 3 0 2 1 3 2 5l3 3-1 1c-3-4-6-8-9-11v-2h1c2 0 2 0 3-1z" class="a"></path><path d="M352 138c1 1 2 2 3 2-1 1-2 2-2 3h-1c-1-1-1-2-2-4h-1c2 0 2 0 3-1z" class="I"></path><path d="M347 145c-1-1-2-3-2-4l15 18-1 1v2 1c-1 1-1 1-2 0-1-6-5-13-10-17v-1z" class="E"></path><path d="M359 138h1 1-1c0 1-1 2-1 3s0 1 1 1v2c1 1 1 2 2 3 1 0 2 1 2 2h0l1 1-3 4c-2-1-3-2-4-3l-3-3c-1-2-2-3-2-5 0-1 1-2 2-3 0 1 0 1 1 2h0c2-1 3-3 3-4z" class="P"></path><path d="M362 147c1 0 2 1 2 2h-2v-2z" class="d"></path><path d="M364 133c0 2 1 4 2 5l2 3h1 0l1-1 1 5c-1 1-3 4-4 5h-1-1l-1-1h0c0-1-1-2-2-2-1-1-1-2-2-3v-2c-1 0-1 0-1-1s1-2 1-3h1-1-1c1-1 1-1 1-2s1-2 1-3c1 1 2 1 2 0h1z" class="X"></path><path d="M364 149c2-1 2-2 3-4h1 0c0 2-1 3-2 5h-1l-1-1z" class="d"></path><path d="M368 141h-2 0c0 1-2 5-3 5h-1l3-6c0-1-2-1-3-1v-1c1-1 1-1 2-1 0 0 1 1 1 2l1-1 2 3z" class="m"></path><path d="M342 114h1l1 3 1 2c1 1 3 4 3 6l1-1c1 1 1 2 2 2h0 2c0 2 1 2 2 3h0c1 1 2 1 3 2l1 1c-1 1-2 2-2 4-1 0-1 1-2 1l-4-3v-1c-1-1-5-4-5-6-1-1-2-1-2-2l-3-5h0l-1-2v-1h0l1-1c0-1 1-1 1-2z" class="K"></path><path d="M351 133v-1-2h1 0 0c1 1 1 2 2 3h0l-1 1-1-1-1 1v-1z" class="P"></path><path d="M349 124c1 1 1 2 2 2h0 2c0 2 1 2 2 3h-3v1h0c-2-2-3-3-4-5l1-1z" class="o"></path><path d="M351 126h2c0 2 1 2 2 3h-3c-1-1-1-1-1-3z" class="H"></path><path d="M355 129h0c1 1 2 1 3 2h-1v1s-1 0-1 1l-1-1-1 1c-1-1-1-2-2-3h0v-1h3z" class="F"></path><path d="M352 130h1c1 0 1 0 2 1v1l-1 1c-1-1-1-2-2-3z" class="c"></path><path d="M356 133c0-1 1-1 1-1v-1h1l1 1c-1 1-2 2-2 4-1 0-1 1-2 1l-4-3 1-1 1 1 1-1h0l1-1 1 1z" class="I"></path><path d="M354 133l1-1 1 1v1h-1l-1-1h0z" class="h"></path><path d="M342 114h1l1 3 1 2 1 2-1 1c-1-1-2-1-3-2h-1 0l-1-2v-1h0l1-1c0-1 1-1 1-2z" class="P"></path><path d="M360 159v1c0 1 1 2 1 3l2-3h0l1 1v2h0c-1 1-1 1-1 2 1 1 0 2 1 3 0 2 1 3 2 5h1 0l1 1h2c0 1 0 2 1 3h1l-2 1 4 3c2 1 3 1 4 1l1 1-2 1v1 2h-1l1 2-1 1h0v3c1 0 1 0 1 1s1 2 2 3l-2 6h1c-1 2-2 3-2 5-1 1-3 3-3 5-2 0-2 0-3 1h0v2c0 1-2 1-3 2-1 2-1 5-2 7v2 3 1l-1-1c-2 1-2 3-3 5 0 1 0 2-1 2h-1 0c-1 1-1 2-2 3h-2c-1 0-2 2-3 3h-1c0 1-1 1-1 2h-1 0c-1 1-2 1-3 1v-1l-1-1h-1l1-1c0-1-1-1-1-2-1 0-1-1-2-1h-1l-1-1h0c-1-2 0-7 1-8l1 1c1-2 1-3 2-5l2-3h0l2-2v-3s-1-1-1-2c-1-2-1-4-3-6l4 1 1 2h2l-1-3h-1l-2-1v-1c0-1 0-2-1-3v-1l-2-2-3 2-1-2h-2c0-1 0-1-1-1-2-1-3-2-4-4v-1-1c-1-4 0-7 2-11h1c0-1 1-2 2-2 1-1 1-2 3-2l1-1v-1l1 1 2-1h0c1 0 2 0 2 1 1-1 1-2 2-2s1 0 2 1l1-1h-1c0-3 2-3 4-4 0-1 0-2 1-3 1-2 1-5 1-8 1 1 1 1 2 0v-1-2l1-1z" class="W"></path><path d="M364 173h1c1 1 1 2 1 3-1 0-2-1-3-1v-1l1-1z" class="G"></path><path d="M367 173l1 1h2c0 1 0 2 1 3h1l-2 1-1-2h-2-1c0-1 0-2-1-3h1 1 0z" class="L"></path><path d="M347 180c1-1 1-2 2-2s1 0 2 1c-2 2-3 3-5 3l-1 1c-1 0-1-1-1-2 1 0 2 0 3-1z" class="B"></path><path d="M349 184c4-3 8-5 12-6-2 2-5 3-7 5-1 1-2 1-3 2-1 2-1 3-2 5v-1h-1v1-1h-1c1-1 1-2 1-3l1-2z" class="F"></path><path d="M368 182v-1c-2-1-3-1-5-1l-1-1h-1 2c4-1 7 1 10 3 1 1 1 1 2 0 0 0 0-1-1-1 2 1 3 1 4 1l1 1-2 1v1 2h-1l1 2-1 1c-2-4-4-6-8-8z" class="b"></path><path d="M374 181c2 1 3 1 4 1l1 1-2 1v1 2h-1c-1-2-2-3-3-5 1 1 1 1 2 0 0 0 0-1-1-1z" class="m"></path><path d="M338 184l6-3c0 1 0 2 1 2l1 1c1 0 1 1 2 0h1l-1 2c0 1 0 2-1 3v3l-1 2v-1c0-1-1-1-1-2l-1 1v-2s1-1 1-2h0c-1 0-2-1-3-1h0c-1-1-2-1-3-1 0-1-1-1-1-2z" class="N"></path><path d="M346 184c1 0 1 1 2 0h1l-1 2c0 1 0 2-1 3v3l-1 2v-1c0-1-1-1-1-2l-1 1v-2s1-1 1-2v-2h-1l-1-1 1-1c0 1 0 1 1 1h1v-1z" class="a"></path><path d="M346 184c1 0 1 1 2 0h1l-1 2-2 2h0v-3-1z" class="P"></path><path d="M354 183c1 0 2 1 3 1 2-1 3-2 4-3 0 0 1 0 1 1 1 1 4 1 5 2l1 2h-1-1c-2-1-5-1-6 0h0l-3 2-1-1h-1c0 1 0 2-1 2h-3v-4c1-1 2-1 3-2z" class="s"></path><path d="M368 182c4 2 6 4 8 8h0c0 1 0 2-1 3h-1c-2 0-3 1-5 1-1-1-1-3-1-4h-1s-1-1-1-2l-1 1c0-1-1-2-2-3h-1l-3 3v-1c0-1 0-1 1-2h0c1-1 4-1 6 0h1 1l-1-2h0 2l-1-2z" class="q"></path><path d="M366 188v-1h1c1 1 1 2 1 3h0-1s-1-1-1-2z" class="K"></path><path d="M368 182c4 2 6 4 8 8h0c0 1 0 2-1 3h-1c-2 0-3 1-5 1-1-1-1-3-1-4h0 0c2 1 4 0 5-1v-1c-1-2-2-3-4-4l-1-2z" class="U"></path><path d="M360 159v1c0 1 1 2 1 3l2-3h0l1 1v2h0c-1 1-1 1-1 2 1 1 0 2 1 3 0 2 1 3 2 5h-1-1 0v-1c-1 0-2 0-4-1 0 1 0 1-1 2-1 2-2 3-4 4-1 0-2 1-3 1h-1c0-3 2-3 4-4 0-1 0-2 1-3 1-2 1-5 1-8 1 1 1 1 2 0v-1-2l1-1z" class="Y"></path><path d="M363 160h0l1 1v2h0c-1 1-1 1-1 2 1 1 0 2 1 3 0 2 1 3 2 5h-1-1 0v-1c-1 0-2 0-4-1h1c0-1-1-2-1-2-1 0-1 1-2 1v-1c2-2 1-6 2-9 0 1 1 2 1 3l2-3z" class="a"></path><path d="M360 169h0c0-1 1-1 2-1 0-2-1-2-1-4 1 0 1 0 2 1s0 2 1 3c0 2 1 3 2 5h-1-1 0v-1c-1 0-2 0-4-1h1c0-1-1-2-1-2z" class="f"></path><path d="M349 190c1-2 1-3 2-5v4h3c1 0 1-1 1-2h1l1 1 3-2c-1 1-1 1-1 2v1l3-3h1c1 1 2 2 2 3l1-1c0 1 1 2 1 2l-2 2h0l2 2h1c-1 3-3 3-6 5h-2 0c-1 0-2-1-3-1s-2 0-3 1h0v2c-1 0-2 0-2 2h-1l-4-6v1c-1-2-1-4 0-6v-3h1v1-1h1v1z" class="F"></path><path d="M347 189h1v1-1h1v1 1c-2 2-2 4-1 6h-1v1c-1-2-1-4 0-6v-3z" class="c"></path><path d="M349 190c1-2 1-3 2-5v4h3c1 0 1-1 1-2h1l1 1h0v1c-1 1-1 3-1 4v1c-2 0-5 0-6-1s-1-2-1-2v-1z" class="q"></path><path d="M357 198c-1 0-2-1-2-2 1 0 4 1 5 1h0c-1-1-2-1-2-2-1-1-1-2-1-3h0l1 1 1 1c1 1 2 1 3 1 2 0 2-2 3-3l2 2h1c-1 3-3 3-6 5h-2 0c-1 0-2-1-3-1z" class="V"></path><path d="M367 194h1c-1 3-3 3-6 5h-2 0v-1h1l1-1c2 0 3-1 5-3z" class="E"></path><path d="M362 186h1c1 1 2 2 2 3l1-1c0 1 1 2 1 2l-2 2h0c-1 1-1 3-3 3-1 0-2 0-3-1l-1-1-1-1c0-1 1-2 1-3l-1-1h0l3-2c-1 1-1 1-1 2v1l3-3z" class="d"></path><path d="M366 188c0 1 1 2 1 2l-2 2v-3l1-1z" class="P"></path><path d="M360 186c-1 1-1 1-1 2v1l3-3c1 2 2 2 2 3h-1-1c-1 1 0 1-1 2h-1v-1h-1c-1 1 1 2-1 3l-1-1c0-1 1-2 1-3l-1-1h0l3-2z" class="Q"></path><path d="M336 185c0-1 1-2 2-2 1-1 1-2 3-2l1-1v-1l1 1 2-1h0c1 0 2 0 2 1-1 1-2 1-3 1l-6 3c0 1 1 1 1 2 1 0 2 0 3 1h0c1 0 2 1 3 1h0c0 1-1 2-1 2v2l1-1c0 1 1 1 1 2v1l1-2c-1 2-1 4 0 6l5 8v2h-1-3 0l-1 1c0-1 0-2-1-3v-1l-2-2-3 2-1-2h-2c0-1 0-1-1-1-2-1-3-2-4-4v-1-1c-1-4 0-7 2-11h1z" class="d"></path><path d="M333 197l1-1h0c1 0 1 0 1 1 1 1 1 1 1 2l1 1 1 1h0l2 2h-2c0-1 0-1-1-1-2-1-3-2-4-4v-1z" class="H"></path><path d="M339 200c-2-1-4-4-4-7s1-6 3-9c0 1 1 1 1 2l-2 2v2c0 2-1 3 0 5l1 1c0 1 1 2 3 3l1 1-1 1-2-1z" class="B"></path><path d="M338 196v-1h0 1c1 1 1 2 2 2s2 0 2-1l1 1c0 1 1 2 0 4h1c0 1 1 2 1 3v1l-2-2-3 2-1-2-2-2 1-1 2 1 1-1-1-1c-2-1-3-2-3-3z" class="C"></path><path d="M338 201l1-1 2 1 3 2-3 2-1-2-2-2z" class="l"></path><path d="M344 192l1-1c0 1 1 1 1 2v1l1-2c-1 2-1 4 0 6l5 8v2h-1-3 0l-1 1c0-1 0-2-1-3v-1-1c0-1-1-2-1-3h-1c1-2 0-3 0-4l-1-1c1 0 1 0 1-1v-1h-1l1-2z" class="F"></path><path d="M346 206h1v-1-1c1 0 3 1 4 2v1 1h-3 0l-1 1c0-1 0-2-1-3z" class="a"></path><path d="M339 186c1 0 2 0 3 1h0c1 0 2 1 3 1h0c0 1-1 2-1 2v2l-1 2h1v1c0 1 0 1-1 1 0 1-1 1-2 1s-1-1-2-2h-1 0v1l-1-1c-1-2 0-3 0-5v-2l2-2z" class="E"></path><path d="M339 186c1 0 2 0 3 1h0c-1 1-3 2-5 3v-2l2-2z" class="Q"></path><path d="M339 195v-2c1-2 3-2 5-3v2l-1 2h1v1c0 1 0 1-1 1 0 1-1 1-2 1s-1-1-2-2z" class="i"></path><path d="M367 190h1c0 1 0 3 1 4 2 0 3-1 5-1h1c1-1 1-2 1-3v3c1 0 1 0 1 1s1 2 2 3l-2 6h1c-1 2-2 3-2 5-1 1-3 3-3 5-2 0-2 0-3 1h0c-1 0-1-1-2-1l-1-1-3 1v-1h0l3-1-1-1v-1c-3 1-6 1-10 0-1-1-2-2-4-3l-5-8v-1l4 6h1c0-2 1-2 2-2v-2h0c1-1 2-1 3-1s2 1 3 1h0 2c3-2 5-2 6-5h-1l-2-2h0l2-2z" class="a"></path><path d="M363 200h2l2 2h-1-1c-1 0-2-1-2-2z" class="d"></path><path d="M376 193c1 0 1 0 1 1s0 4-1 5h-1c1-2 1-4 1-6z" class="G"></path><path d="M367 194l1-1v3l-1 1c-1 1-1 1-1 2l-1 1h-2l-1-1h0c3-2 5-2 6-5h-1z" class="F"></path><defs><linearGradient id="Ah" x1="358.802" y1="211.044" x2="370.423" y2="201.542" xlink:href="#B"><stop offset="0" stop-color="#121012"></stop><stop offset="1" stop-color="#383835"></stop></linearGradient></defs><path fill="url(#Ah)" d="M347 198v-1l4 6c2 2 3 3 5 4 1 1 5 0 7 1l3-1c5-2 7-5 9-8h1c-2 5-4 8-9 10h-1c-3 1-6 1-10 0-1-1-2-2-4-3-2-3-3-5-5-8z"></path><defs><linearGradient id="Ai" x1="379.461" y1="197.683" x2="365.838" y2="209.393" xlink:href="#B"><stop offset="0" stop-color="#6e6d6d"></stop><stop offset="1" stop-color="#919191"></stop></linearGradient></defs><path fill="url(#Ai)" d="M377 194c0 1 1 2 2 3l-2 6h1c-1 2-2 3-2 5-1 1-3 3-3 5-2 0-2 0-3 1h0c-1 0-1-1-2-1l-1-1-3 1v-1h0l3-1-1-1v-1h1c5-2 7-5 9-10 1-1 1-4 1-5z"></path><path d="M367 211l3-1c3-1 5-5 7-7h1c-1 2-2 3-2 5-1 1-3 3-3 5-2 0-2 0-3 1h0c-1 0-1-1-2-1l-1-1-3 1v-1h0l3-1z" class="G"></path><defs><linearGradient id="Aj" x1="355.847" y1="206.044" x2="358.758" y2="199.98" xlink:href="#B"><stop offset="0" stop-color="#8b8a8a"></stop><stop offset="1" stop-color="#a9a7a7"></stop></linearGradient></defs><path fill="url(#Aj)" d="M354 199h0c1-1 2-1 3-1s2 1 3 1h0 2 0c-1 2-1 3 0 4l1 3h0v2c-2-1-6 0-7-1-2-1-3-2-5-4h1c0-2 1-2 2-2v-2z"></path><path d="M354 199h0c1-1 2-1 3-1s2 1 3 1h0v4h0c0-1 0-2-1-3h-3c0-1-1-1-2-1z" class="L"></path><path d="M360 199h2 0c-1 2-1 3 0 4l1 3h0l-3-3v-4z" class="O"></path><path d="M352 206c2 1 3 2 4 3 4 1 7 1 10 0v1l1 1-3 1h0v1l3-1 1 1c1 0 1 1 2 1v2c0 1-2 1-3 2-1 2-1 5-2 7v2 3 1l-1-1c-2 1-2 3-3 5 0 1 0 2-1 2h-1 0c-1 1-1 2-2 3h-2c-1 0-2 2-3 3h-1c0 1-1 1-1 2h-1 0c-1 1-2 1-3 1v-1l-1-1h-1l1-1c0-1-1-1-1-2-1 0-1-1-2-1h-1l-1-1h0c-1-2 0-7 1-8l1 1c1-2 1-3 2-5l2-3h0l2-2v-3s-1-1-1-2c-1-2-1-4-3-6l4 1 1 2h2l-1-3h-1l-2-1v-1l1-1h0 3 1v-2z" class="q"></path><path d="M351 232v-1c1-1 2-2 2-3h0v4 1c-1 1-2 1-3 2l1-3z" class="K"></path><path d="M351 214c1 2 2 4 4 5 1 1 1 2 2 3 0 2-1 3-1 5v1h0c-1 0-1 0-1 1h-1c0-2 0-5-1-8-1-2-3-4-4-7h2z" class="s"></path><path d="M348 219l1 4h0l1-1c1 1 1 2 1 3v2l-2 3v1c0 1 0 2-2 3h1c1 0 2-2 3-2l-1 3h-1v1h2-1c-1 0-2 0-4-1-1-1-1 0-2 0h-1l-1 2c1 2 2 2 3 3l-1 1c-1 0-1-1-2-1h-1l-1-1h0c-1-2 0-7 1-8l1 1c1-2 1-3 2-5l2-3h0l2-2v-3z" class="U"></path><path d="M349 230c-1 2-2 3-3 4h-1c1-1 1-2 2-2v-1c2-2 2-5 3-7h0l1 3-2 3z" class="d"></path><path d="M346 224h0l2-2c0 3-1 5-3 7-1 2-3 5-3 7v1c1 2 2 2 3 3l-1 1c-1 0-1-1-2-1h-1l-1-1h0c-1-2 0-7 1-8l1 1c1-2 1-3 2-5l2-3z" class="H"></path><path d="M340 239v-2h1c0 1 1 1 1 3h-1l-1-1z" class="P"></path><defs><linearGradient id="Ak" x1="357.697" y1="227.449" x2="346.199" y2="238.114" xlink:href="#B"><stop offset="0" stop-color="#c9c8c9"></stop><stop offset="1" stop-color="#f3f2f3"></stop></linearGradient></defs><path fill="url(#Ak)" d="M356 228l1-1h1v-2h1c0 1 1 2 0 3h0v2h0c1 1 1 2 1 2l1 1v2c0 1 0 2-1 2h-1 0c-1 1-1 2-2 3h-2c-1 0-2 2-3 3h-1c0 1-1 1-1 2h-1 0c-1 1-2 1-3 1v-1l-1-1h-1l1-1c0-1-1-1-1-2l1-1c-1-1-2-1-3-3l1-2h1c1 0 1-1 2 0 2 1 3 1 4 1h1-2v-1h1c1-1 2-1 3-2l2-3s0-1 1-2h0z"></path><path d="M355 230c1 1 1 1 0 2-1 2-3 3-4 4h-2v-1h1c1-1 2-1 3-2l2-3z" class="H"></path><path d="M359 230h0c1 1 1 2 1 2l1 1v2c0 1 0 2-1 2h-1 0c-1 1-1 2-2 3h-2c1-1 2-1 2-2-1-1-1-1-2-1 2-3 3-4 4-7z" class="C"></path><path d="M360 232l1 1v2c0 1 0 2-1 2h-1l1-5z" class="d"></path><path d="M342 237l1-2h1v1 1h1c1 1 3 2 5 2l5-2c1 0 1 0 2 1 0 1-1 1-2 2-1 0-2 2-3 3h-1c0 1-1 1-1 2h-1 0c-1 1-2 1-3 1v-1l-1-1h-1l1-1c0-1-1-1-1-2l1-1c-1-1-2-1-3-3z" class="O"></path><path d="M342 237l1-2h1v1 1h1v1c0 1 0 1 1 2l-1 1v-1c-1-1-2-1-3-3z" class="p"></path><path d="M349 245h-1c-1-2-2-4-2-7l2 2c0 1 1 1 1 2h2l1 1h-1c0 1-1 1-1 2h-1z" class="E"></path><path d="M350 239l5-2c1 0 1 0 2 1 0 1-1 1-2 2-1 0-2 2-3 3l-1-1-2-2v-1h1z" class="D"></path><path d="M352 206c2 1 3 2 4 3 4 1 7 1 10 0v1l1 1-3 1h0v1l3-1 1 1c1 0 1 1 2 1v2c0 1-2 1-3 2-1 2-1 5-2 7v2 3 1l-1-1c-2 1-2 3-3 5v-2l-1-1s0-1-1-2h0v-2h0c1-1 0-2 0-3h-1v2h-1l-1 1v-1c0-2 1-3 1-5-1-1-1-2-2-3-2-1-3-3-4-5l-1-3h-1l-2-1v-1l1-1h0 3 1v-2z" class="p"></path><path d="M356 227c1 0 1-1 1-2 1-1 0-2 0-3 1 1 1 2 2 3h-1v2h-1l-1 1v-1z" class="q"></path><path d="M358 213v1l1 1v2l-2 2c-1-2-3-3-4-4 1-2 2-2 5-2z" class="D"></path><path d="M352 206c2 1 3 2 4 3l-2 2v1c1 0 1 0 1 1h3c-3 0-4 0-5 2-1-2-2-3-3-4h-1l-2-1v-1l1-1h0 3 1v-2z" class="I"></path><path d="M348 208c1 0 2 1 3 2-1 0-1 1-2 1l-2-1v-1l1-1z" class="R"></path><path d="M351 210c1 0 2 0 3 1v1c1 0 1 0 1 1h3c-3 0-4 0-5 2-1-2-2-3-3-4h-1c1 0 1-1 2-1z" class="B"></path><path d="M356 209c4 1 7 1 10 0v1l1 1-3 1h0v1h0l-2 2h-2v-1h-1v1l-1-1v-1h-3c0-1 0-1-1-1v-1l2-2z" class="c"></path><path d="M358 214c1-1 4-1 6-1l-2 2h-2v-1h-1v1l-1-1z" class="o"></path><path d="M359 217h1c0 1 2 3 2 3 1 2 2 3 3 4v1 2 3 1l-1-1c-2 1-2 3-3 5v-2l-1-1s0-1-1-2h0v-2h0l1 1h1c-1-3-1-6-3-9 0 0-1 0-1-1h0l2-2z" class="m"></path><path d="M363 227l1-2h0c0 2 0 4 1 5v1l-1-1c0-1-1-2-1-3z" class="L"></path><path d="M363 227c0 1 1 2 1 3-2 1-2 3-3 5v-2c0-3 0-4 2-6z" class="H"></path><path d="M364 213l3-1 1 1c1 0 1 1 2 1v2c0 1-2 1-3 2-1 2-1 5-2 7v-1c-1-1-2-2-3-4 0 0-2-2-2-3h-1v-2-1h1v1h2l2-2h0z" class="F"></path><path d="M359 215v-1h1v1h1c0 1 0 1 1 2l3 3h-1-1-1s-2-2-2-3h-1v-2z" class="a"></path><path d="M368 213c1 0 1 1 2 1v2c0 1-2 1-3 2h-1c-1-1-1-2-1-3 1-1 2-1 3-2z" class="D"></path><path d="M539 367l2 2c1 1 3 1 4 3 2 1 3 2 4 4h0l2 2c0 1 1 2 2 3 3 2 6 4 8 6l1 1 2 2c0 1 0 1-1 2h-1c1 1 2 2 3 2 1 2 4 4 5 5 5 4 6 8 6 13v1c-1 4-2 9-5 12h-1c-2 1-3 4-5 5s-6 2-9 2h-3l-2-1c-1 0-1 0-2-1-1 1-1 1-2 1l-1 1c-3 0-7-3-9-4-1-1-2-1-3-2h0-1 0c-3-3-4-6-4-10-1-1-1-2-1-3h-1c0-1 0-1-1-2 1-2 1-4 2-5v-1c-1 0-1-1-1-1v-1-5c0-1-1 0 0-1 1 1 2 1 3 1 1-1 1-3 1-5l3-14 3-1v-1h-1l-1-1 3-7 1-2z" class="n"></path><path d="M565 397h1v3 1c0 5 0 11-2 16h0v1l1-7h0v-10-4z" class="F"></path><path d="M565 394c1 2 4 4 5 5 1 2 1 3 0 4-1-1-2-2-4-2v-1-3h-1c0-1-1-1 0-3z" class="V"></path><path d="M566 397h1c0 1 1 2 1 3h-2v-3z" class="N"></path><path d="M535 424c3 3 7 5 11 6 2 0 4-3 6-5-1 2-2 4-3 5s-1 1-2 1l-1 1c-3 0-7-3-9-4l-2-4zm30-17c0-2 0-3-1-5 0-2-3-6-4-8 0-1 0-2 1-2h1c1 1 2 2 3 2-1 2 0 2 0 3v4 10h0c-1-1 0-3 0-4z" class="d"></path><path d="M537 414c3 2 5 2 8 2 2 0 3 1 6 0 1 0 2 0 3 1 1-1 2-1 3-2l1 1c-3 2-5 3-8 4-5 0-11-3-14-6h1z" class="N"></path><path d="M570 399c5 4 6 8 6 13v1c-1 4-2 9-5 12h-1c1-1 1-3 2-4 2-6 1-13-2-18 1-1 1-2 0-4z" class="I"></path><path d="M538 389c1 0 2 0 2 1 1 0 2 1 3 1h4l-4 2v1h-1c1 0 1 1 2 0h2c-4 2-7 3-10 6h-1v1c0 1 0 1-1 2h0c-1 1-2 1-2 3-1 4-1 11 1 15 0 1 1 2 2 3l2 4c-1-1-2-1-3-2h0-1 0c-3-3-4-6-4-10-1-1-1-2-1-3h-1c0-1 0-1-1-2 1-2 1-4 2-5v-1c-1 0-1-1-1-1v-1-5c0-1-1 0 0-1 1 1 2 1 3 1 1-1 1-3 1-5h2l5-4z" class="O"></path><path d="M528 406c1-3 1-4 4-5h0c0 2-1 3-2 4-1 4-1 7-1 11-1-1-1-2-1-3h-1c0-1 0-1-1-2 1-2 1-4 2-5z" class="I"></path><defs><linearGradient id="Al" x1="541.246" y1="390.814" x2="527.072" y2="401.291" xlink:href="#B"><stop offset="0" stop-color="#a7a7a7"></stop><stop offset="1" stop-color="#d4d2d4"></stop></linearGradient></defs><path fill="url(#Al)" d="M538 389c1 0 2 0 2 1 1 0 2 1 3 1h4l-4 2c-4 2-7 5-11 8-3 1-3 2-4 5v-1c-1 0-1-1-1-1v-1-5c0-1-1 0 0-1 1 1 2 1 3 1 1-1 1-3 1-5h2l5-4z"></path><path d="M538 389c1 0 2 0 2 1 1 0 2 1 3 1-3 1-6 2-10 4 2-2 5-4 5-6z" class="s"></path><path d="M539 367l2 2c1 1 3 1 4 3 2 1 3 2 4 4h0l2 2c0 1 1 2 2 3 3 2 6 4 8 6l1 1 2 2c0 1 0 1-1 2h-1-1c-1 0-1 1-1 2 1 2 4 6 4 8 1 2 1 3 1 5l-2-1c-1 1-1 2-1 3 0-3 0-7-1-10-1-2-3-3-4-5-3-2-6-3-10-3h-4c-1 0-2-1-3-1 0-1-1-1-2-1l-5 4h-2l3-14 3-1v-1h-1l-1-1 3-7 1-2z" class="n"></path><path d="M546 375c1 0 2 1 3 1h0l2 2c0 1 1 2 2 3-3-1-5-3-7-5h0v-1z" class="F"></path><path d="M561 387l1 1 2 2c0 1 0 1-1 2h-1-1c-1 0-1 1-1 2 1 2 4 6 4 8 1 2 1 3 1 5l-2-1c-1 1-1 2-1 3 0-3 0-7-1-10-1-2-3-3-4-5l1 1c4 2 4 7 5 10h0c0-1 1-3 0-4 0-1-1-2-1-2-1-2-2-3-3-4s-1-2-1-3 1-1 2-2l1 1c1 0 1 0 2-1l-2-2v-1z" class="p"></path><path d="M539 367l2 2c1 1 3 1 4 3 2 1 3 2 4 4-1 0-2-1-3-1v1h0c-4 1-6 1-9 2v-1h-1l-1-1 3-7 1-2z" class="B"></path><path d="M541 369c1 1 3 1 4 3 2 1 3 2 4 4-1 0-2-1-3-1-2-2-4-4-5-6z" class="L"></path><path d="M538 369c1 0 1 1 2 1 1 2 2 3 4 5-3 0-6 0-8 2l-1-1 3-7zm9 22c4 0 7 1 10 3 1 2 3 3 4 5 1 3 1 7 1 10-1 3-2 6-4 7l-1-1c-1 1-2 1-3 2-1-1-2-1-3-1-3 1-4 0-6 0-3 0-5 0-8-2h-1c-1 0-1 0-1-1-2-3-2-6-1-10h0c1-1 1-1 1-2v-1h1c3-3 6-4 10-6h-2c-1 1-1 0-2 0h1v-1l4-2z" class="n"></path><path d="M534 403h0c1-1 1-1 1-2v-1h1c1 1 3 2 3 3-1 0-2-1-3 0-2 2-2 5-1 7 0 2 1 3 2 4h-1c-1 0-1 0-1-1-2-3-2-6-1-10z" class="Z"></path><path d="M547 391c4 0 7 1 10 3 1 2 3 3 4 5 1 3 1 7 1 10-1 3-2 6-4 7l-1-1c-1 1-2 1-3 2-1-1-2-1-3-1h0c2 0 3-1 4-2 2-2 4-6 4-9 0-2-1-6-3-8-3-2-7-3-10-3h-2c-1 1-1 0-2 0h1v-1l4-2z" class="I"></path><path d="M428 163l1 1h1v1l2-1v1c-1 1-2 1-2 2 0 2-1 2-2 3l-3 3c0 1 0 2 1 3h1l-1 2-2 1c0 2-2 4-2 6h1c0 1-1 2-1 2l-1 3c1 0 1 0 1 1 1 0 0 1 1 1h1c2 0 3-1 4-2l3-1h1c1 1 2 1 3 1h0v1c3-2 5-2 8-3 2-2 4-2 7-3h2c-1 1-1 1-2 1l1 1-1 1v1c1 0 2-1 2 0l1 1c0 2-1 1-2 2v3c2 0 2-1 4-1 1 1 2 2 3 2v1 4c1 0 1 0 2 1-1 3-4 4-7 6h-1l-3 1-2 1-7 1h-3c-1-1-1-1-2-1v-1c-2 1-6 2-8 3h-1c1 0 2-1 3-2h-2l-10 6s-1 1-2 1-4 4-5 5h0c0 1-2 4-3 5l-2 1h-1s-1-1-2-1c-1 1-1 2-2 0-1 0-1-1-1-1h-1c0 1-1 1-1 2 0 0-1 0-1-1v-2l-1-1h0v-1h0c0-1 0-2-1-3 0 0 0 1-1 1h-1v-1c-1-3-1-5-1-8 0-1 1-2 1-3l-1-1v-1h-1v-1h-1 0c1-1 1-2 2-4h0c2 0 3-1 4-2h0l-1-1v1c-1 0-2 0-3-1h0c-1 0-1 1-2 1h-1l-2-2 1-2c2 0 3 1 5 0l3-3c-1-2-1-5-1-7-1-1-2-3-3-4h0l-2-1h1 2l2 2 2 1s1-1 1-2h1v2h1c1-1 2-2 2-4l1 1v2c1 0 3 0 4-1 1 0 1-2 2-2v-1c1-1 2-2 3-2 2-4 4-7 7-10 3-2 6-3 9-3l1-1z" class="r"></path><path d="M404 204c1-2 3-4 4-5 1-2 2-5 4-7h2c-1 3-2 4-3 6 0 1-1 1-1 2l-1 1s-1 1-2 1l-1 1s-1 1-2 1z" class="L"></path><path d="M425 173c0 1 0 2 1 3h1l-1 2-2 1c0 2-2 4-2 6l-7 9c-1 1-1 3-2 4l-1-1-1 1c1-2 2-3 3-6h-2l2-3h-2l-4 4h0l6-9 11-11z" class="I"></path><path d="M419 184c1-2 3-4 4-6 1 0 2-1 2-1h1l-2 2h0c0 2-2 4-2 6l-7 9c-1 1-1 3-2 4l-1-1-1 1c1-2 2-3 3-6h-2l2-3h0c1-1 1-2 2-2v-1c1-1 2-2 2-3l1 1z" class="H"></path><path d="M414 189h0c1-1 1-2 2-2v-1c1-1 2-2 2-3l1 1-5 8h-2l2-3z" class="b"></path><defs><linearGradient id="Am" x1="407.169" y1="202.244" x2="396.384" y2="199.943" xlink:href="#B"><stop offset="0" stop-color="#cac8cb"></stop><stop offset="1" stop-color="#fcfcfb"></stop></linearGradient></defs><path fill="url(#Am)" d="M406 190c2-1 4-3 5-5h0l-1 2h1 0c1-2 2-2 2-3h1 0l-6 9h0c0 1-1 1-1 2s-1 1-1 2c-2 2-3 4-6 5-1 1-1 2-1 3-1 2-2 5-3 8 0 1-1 2-1 2h1v5c-1 1-1 2-1 3 0-1 0-2-1-3 0 0 0 1-1 1h-1v-1c-1-3-1-5-1-8 0-1 1-2 1-3l-1-1v-1h-1v-1h-1 0c1-1 1-2 2-4h0c2 0 3-1 4-2l1 1 1-2 1-2c1-1 3-4 4-5l5-6c0 2 0 3-1 4z"></path><path d="M400 202c1-1 4-6 6-7h1c0 1-1 1-1 2-2 2-3 4-6 5z" class="l"></path><path d="M402 192l5-6c0 2 0 3-1 4-3 1-5 6-7 9h-2l1-2c1-1 3-4 4-5z" class="K"></path><path d="M397 199h2c-1 1-2 3-3 4-2 2-3 3-4 6l-1-1v-1h-1v-1h-1 0c1-1 1-2 2-4h0c2 0 3-1 4-2l1 1 1-2z" class="d"></path><path d="M395 200l1 1c-2 2-4 4-6 5h-1 0c1-1 1-2 2-4h0c2 0 3-1 4-2z" class="G"></path><path d="M392 209c1-3 2-4 4-6-1 3-3 7-2 10v4l1-2h1v5c-1 1-1 2-1 3 0-1 0-2-1-3 0 0 0 1-1 1h-1v-1c-1-3-1-5-1-8 0-1 1-2 1-3z" class="p"></path><path d="M411 198l1-1 1 1c1-1 1-3 2-4l7-9h1c0 1-1 2-1 2l-1 3c-1 1-2 3-3 5l-2 3h1 1l-2 3c-2 1-2 3-3 4-2 4-3 8-5 12-1 2-4 4-4 6v1 2h0c1 0 2-1 2-1 0-1 1-1 1-2l1 1c1-1 2-1 2-2 0 1-2 4-3 5l-2 1h-1s-1-1-2-1c-1 1-1 2-2 0-1 0-1-1-1-1h-1c0 1-1 1-1 2 0 0-1 0-1-1v-2-2l1-1v3-1c0-1 1-1 1-2 0-4 2-8 4-12h0l-1-1c1-2 2-4 3-5 1 0 2-1 2-1l1-1c1 0 2-1 2-1l1-1c0-1 1-1 1-2z" class="n"></path><path d="M397 225v1l1-1c1-1 1-2 2-3v1h0c1 1 1 1 1 2h1l-1-1v-3 2c1 0 1 1 2 2l1-1v2h0c1 0 2-1 2-1 0-1 1-1 1-2l1 1c1-1 2-1 2-2 0 1-2 4-3 5l-2 1h-1s-1-1-2-1c-1 1-1 2-2 0-1 0-1-1-1-1h-1c0 1-1 1-1 2 0 0-1 0-1-1v-2-2l1-1v3z" class="q"></path><defs><linearGradient id="An" x1="423.035" y1="187.604" x2="409.961" y2="199.044" xlink:href="#B"><stop offset="0" stop-color="#6b6a6a"></stop><stop offset="1" stop-color="#a19fa0"></stop></linearGradient></defs><path fill="url(#An)" d="M411 198l1-1 1 1c1-1 1-3 2-4l7-9h1c0 1-1 2-1 2l-1 3c-1 1-2 3-3 5l-2 3-9 11c0-3 2-5 3-8-2 2-3 4-5 5-1 1-2 2-3 4l-1-1c1-2 2-4 3-5 1 0 2-1 2-1l1-1c1 0 2-1 2-1l1-1c0-1 1-1 1-2z"></path><path d="M428 163l1 1h1v1l2-1v1c-1 1-2 1-2 2 0 2-1 2-2 3l-3 3-11 11h0-1c0 1-1 1-2 3h0-1l1-2h0c-1 2-3 4-5 5 1-1 1-2 1-4l-5 6c-1 1-3 4-4 5l-1 2-1 2-1-1h0l-1-1v1c-1 0-2 0-3-1h0c-1 0-1 1-2 1h-1l-2-2 1-2c2 0 3 1 5 0l3-3c-1-2-1-5-1-7-1-1-2-3-3-4h0l-2-1h1 2l2 2 2 1s1-1 1-2h1v2h1c1-1 2-2 2-4l1 1v2c1 0 3 0 4-1 1 0 1-2 2-2v-1c1-1 2-2 3-2 2-4 4-7 7-10 3-2 6-3 9-3l1-1z" class="Q"></path><path d="M418 177h0 1 0l-3 2h0 0c-1 0 0 0 0-1l2-1z" class="W"></path><path d="M404 185h0c3-1 5-2 7-4-1 2-3 4-4 5l-5 6-2-2 1-1c0-1 2-3 3-4z" class="N"></path><path d="M397 195l1-1c0-1 1-3 1-4s0-2 1-3c1-2 2-2 4-2-1 1-3 3-3 4l-1 1 2 2c-1 1-3 4-4 5l-1 2-1 2-1-1h0l-1-1v1c-1 0-2 0-3-1 3-1 5-3 6-4z" class="B"></path><path d="M394 199c1 0 2-2 4-2l-1 2-1 2-1-1h0l-1-1z" class="b"></path><path d="M428 163l1 1c-4 4-9 7-13 10-2 1-3 2-5 3 2-4 4-7 7-10 3-2 6-3 9-3l1-1z" class="M"></path><defs><linearGradient id="Ao" x1="396.874" y1="184.719" x2="383.622" y2="194.352" xlink:href="#B"><stop offset="0" stop-color="#c7c6c7"></stop><stop offset="1" stop-color="#faf8f8"></stop></linearGradient></defs><path fill="url(#Ao)" d="M391 182h0l-2-1h1 2l2 2 2 1s1-1 1-2h1v2h1l-1 1h-1 0v4 6c-1 1-3 3-6 4h0c-1 0-1 1-2 1h-1l-2-2 1-2c2 0 3 1 5 0l3-3c-1-2-1-5-1-7-1-1-2-3-3-4z"></path><path d="M443 188c2-2 4-2 7-3h2c-1 1-1 1-2 1l1 1-1 1v1c1 0 2-1 2 0l1 1c0 2-1 1-2 2v3c2 0 2-1 4-1 1 1 2 2 3 2v1 4c1 0 1 0 2 1-1 3-4 4-7 6h-1l-3 1-2 1-7 1h-3c-1-1-1-1-2-1v-1c-2 1-6 2-8 3h-1c1 0 2-1 3-2h-2l-10 6s-1 1-2 1-4 4-5 5h0c0 1-1 1-2 2l-1-1c0 1-1 1-1 2 0 0-1 1-2 1h0v-2-1c0-2 3-4 4-6 2-4 3-8 5-12 1-1 1-3 3-4l2-3h-1-1l2-3c1-2 2-4 3-5 1 0 1 0 1 1 1 0 0 1 1 1h1c2 0 3-1 4-2l3-1h1c1 1 2 1 3 1h0v1c3-2 5-2 8-3z" class="s"></path><path d="M429 195h1c1 0 2-2 4-1h0l-2 2c-1 1-3 2-4 2 0 0-1 0-1-1-2 1-4 2-6 4v-1c1 0 2-1 2-1l2-1v-1h1c0-1 1-1 1-1 1 0 2-1 2-1z" class="l"></path><path d="M439 193c1 0 2 0 3 1-1 0-1 1-1 2h0c1 0 2-1 3 0v1l-1 1-3 2h-1v-1c-2-1-4 0-6 0-1 1-1 1-2 1-1 1-1 1-2 1-2 1-3 3-6 4v-1h0c1 0 1-1 2-1h0 2l-2-1c1 0 1-1 1-1h0l-3 2h0l5-5c1 0 3-1 4-2l1 1 1-1c1 0 2 0 3-1l2-2z" class="W"></path><path d="M439 193c1 0 2 0 3 1-1 0-1 1-1 2h0l-1 1h-2c-1 0-1 0-1-1-2 1-3 2-5 2l-2 2c-1 0-2 1-4 1h0l-3 2h0l5-5c1 0 3-1 4-2l1 1 1-1c1 0 2 0 3-1l2-2z" class="U"></path><defs><linearGradient id="Ap" x1="445.513" y1="205.637" x2="429.644" y2="203.033" xlink:href="#B"><stop offset="0" stop-color="#afaab0"></stop><stop offset="1" stop-color="#d4d5d2"></stop></linearGradient></defs><path fill="url(#Ap)" d="M445 197c1 1 2 1 3 1l2 2h-2l-8 4c-4 2-7 4-11 6h-2c1-3 5-6 7-7l5-3h1l3-2 1-1h1z"></path><path d="M439 200h1v1c-1 1-3 2-5 2h-1l5-3z" class="f"></path><path d="M445 197c1 1 2 1 3 1l2 2h-2v-1c-3 0-5 1-8 2v-1l3-2 1-1h1z" class="H"></path><path d="M445 197c1 1 2 1 3 1-1 1-3 1-5 1v-1l1-1h1z" class="L"></path><path d="M435 191c3-2 5-2 8-3l-4 4h1l-1 1-2 2c-1 1-2 1-3 1l-1 1-1-1 2-2h0c-2-1-3 1-4 1h-1v-1c-3 1-6 2-7 4-1 1-5 3-6 3h0l2-3h-1-1l2-3c1-2 2-4 3-5 1 0 1 0 1 1 1 0 0 1 1 1h1c2 0 3-1 4-2l3-1h1c1 1 2 1 3 1h0v1z" class="m"></path><path d="M435 191c3-2 5-2 8-3l-4 4h1l-1 1-2 2c-1 0-1 0-2-1l3-2h-1-2l-1-1h1z" class="b"></path><path d="M429 193c1-1 3-2 4-2h0v1c-1 0-3 2-4 2-3 1-6 2-7 4-1 1-5 3-6 3h0l2-3h-1-1l2-3c2-1 3-2 4-2 1-1 2 0 2 0 1 0 3-1 4-1l1 1z" class="W"></path><path d="M424 193c1 0 3-1 4-1l1 1c-2 1-5 2-7 4-1 1-3 2-4 2 1-2 4-4 5-5l1-1z" class="d"></path><path d="M443 188c2-2 4-2 7-3h2c-1 1-1 1-2 1l1 1-1 1v1c1 0 2-1 2 0l1 1c0 2-1 1-2 2v3c2 0 2-1 4-1 1 1 2 2 3 2v1 4c1 0 1 0 2 1-1 3-4 4-7 6h-1l-3 1-2 1-7 1h-3c-1-1-1-1-2-1v-1c-2 1-6 2-8 3h-1c1 0 2-1 3-2 4-2 7-4 11-6l8-4h2l-2-2c-1 0-2 0-3-1h-1v-1c-1-1-2 0-3 0h0c0-1 0-2 1-2-1-1-2-1-3-1l1-1h-1l4-4z" class="G"></path><path d="M449 194l1-1-1 2h-1v1h2 1 1l-1 1v1 1l-1 1-2-2c-1 0-2 0-3-1 2-1 3-2 4-3z" class="I"></path><path d="M453 200l2-2h1c1 0 1-1 2-1v4h0c-1 1-2 1-4 1l-2-2h1z" class="o"></path><path d="M453 200h4l1 1c-1 1-2 1-4 1l-2-2h1z" class="K"></path><path d="M448 188h1c0 1 0 1 1 1 0 1 2 1 1 3l-1 1-1 1c-2-2-4-2-6-3 1-1 4-2 5-3z" class="L"></path><path d="M458 201c1 0 1 0 2 1-1 3-4 4-7 6 0-1-1-1-1-1-1-3 3-3 4-5h-2c2 0 3 0 4-1h0z" class="F"></path><defs><linearGradient id="Aq" x1="449.196" y1="187.538" x2="441.439" y2="188.462" xlink:href="#B"><stop offset="0" stop-color="#3a383b"></stop><stop offset="1" stop-color="#52514f"></stop></linearGradient></defs><path fill="url(#Aq)" d="M443 188c2-2 4-2 7-3h2c-1 1-1 1-2 1l-2 2c-1 1-4 2-5 3l-3 1h-1l4-4z"></path><path d="M443 191c2 1 4 1 6 3-1 1-2 2-4 3h-1v-1c-1-1-2 0-3 0h0c0-1 0-2 1-2-1-1-2-1-3-1l1-1 3-1z" class="d"></path><path d="M440 204c2 0 2 1 4 0 0 0 1-1 2-1v2 1h0c2-1 4-2 6-2h0l-2 2c-1 0-2 0-2 1h1c1 0 1 0 2 1h1l-3 1-2 1-7 1h-3c-1-1-1-1-2-1v-1c-2 1-6 2-8 3h-1c1 0 2-1 3-2 4-2 7-4 11-6z" class="O"></path><path d="M435 209c1 0 7-2 8-2l1 1c2 0 3 0 5 1l-2 1-7 1h-3c-1-1-1-1-2-1v-1z" class="k"></path><path d="M171 227v4l3 6c0 1 1 2 1 3 0 2 1 4 1 6h1l1 1h1c1 0 1 0 1 1v2c-1 0-1 0-1 1v1 1l1 1-1 1c-1-1-2-1-2-1h-1l-2 2c0-1-2-3-3-4v7 1c1 2 1 4 2 6l-1 1-1-2c-3 0-6-2-7-4h-2 0-1-3c0 1-2 3-3 3h-1c-1 1-2 1-4 1l-4 1-2 1-2 1 1 8v3l-30 1v2l-1-1h0-1c0-1 0-1-1-1H87h-1l31-28 4-5 1-1c1 0 3 1 4 1h9l-1-1h4 3c1-1 3 0 5 0 3-3 5-7 7-10h1c-1 2-1 2-1 4v2h0c1-1 2-3 3-4 2-2 6-4 8-5 3-2 5-4 7-6z" class="r"></path><path d="M112 281c0-1 0-2 1-4v1 1 1 2l-1-1zm9-34l1-1c1 0 3 1 4 1h9c1 3 0 7 0 10l-2-1-12-9z" class="I"></path><path d="M126 247h9c1 3 0 7 0 10l-2-1-1-3c-1-2-1-4-1-6h-4-1z" class="G"></path><defs><linearGradient id="Ar" x1="152.034" y1="259.444" x2="142.281" y2="244.749" xlink:href="#B"><stop offset="0" stop-color="#989697"></stop><stop offset="1" stop-color="#c8c7c7"></stop></linearGradient></defs><path fill="url(#Ar)" d="M153 236h1c-1 2-1 2-1 4v2h0v3 6h0c0 1 0 1 1 2h0c0 1 0 2 1 3v1l-1 3h0l-1-1h-1l1 2c0 1-2 3-2 4h-1l-4 1-2 1-2 1h0v-3h-1c-1 1-2 2-4 2h0-1 0c-1-1-1-2 0-3-1-1-1-3-1-4h-1v-1s1 0 1-1v-1c0-3 1-7 0-10l-1-1h4 3c1-1 3 0 5 0 3-3 5-7 7-10z"></path><path d="M139 254c1-2 2-4 4-5l4 2c-3 0-6 2-8 4v-1z" class="C"></path><path d="M147 251c2 1 4 2 5 4v2c-2-1-4-3-6-2-4 0-5 2-7 5l-1-2c0-1 0-3 1-4v1c2-2 5-4 8-4z" class="L"></path><path d="M138 246h3c-1 1-2 2-3 4-1 1-1 3-2 5 0 1 0 2 1 3v-1l1 1h0l1 2c-1 2-2 5-3 7-1-1-1-2 0-3-1-1-1-3-1-4h-1v-1s1 0 1-1v-1c0-3 1-7 0-10l-1-1h4z" class="T"></path><path d="M136 255c0 1 0 2 1 3v-1l1 1h0l1 2c-1 2-2 5-3 7-1-1-1-2 0-3v-5-4z" class="P"></path><defs><linearGradient id="As" x1="149.157" y1="258.158" x2="143.215" y2="266.5" xlink:href="#B"><stop offset="0" stop-color="#9f9e9e"></stop><stop offset="1" stop-color="#c2c1c1"></stop></linearGradient></defs><path fill="url(#As)" d="M139 260c2-3 3-5 7-5 2-1 4 1 6 2l1 2h-1l1 2c0 1-2 3-2 4h-1l-4 1-2 1-2 1h0v-3h-1c-1 1-2 2-4 2h0-1 0c1-2 2-5 3-7z"></path><path d="M139 260c2-3 3-5 7-5 2-1 4 1 6 2l1 2h-1c-2-1-3-2-5-3-4 3-7 7-10 11h-1 0c1-2 2-5 3-7z" class="J"></path><path d="M171 227v4l3 6c0 1 1 2 1 3 0 2 1 4 1 6h1l1 1h1c1 0 1 0 1 1v2c-1 0-1 0-1 1v1 1l1 1-1 1c-1-1-2-1-2-1h-1l-2 2c0-1-2-3-3-4v7 1c1 2 1 4 2 6l-1 1-1-2c-3 0-6-2-7-4h-2 0-1-3c0 1-2 3-3 3h-1c-1 1-2 1-4 1h1c0-1 2-3 2-4l-1-2h1l1 1h0l1-3v-1c-1-1-1-2-1-3h0c-1-1-1-1-1-2h0v-6-3c1-1 2-3 3-4 2-2 6-4 8-5 3-2 5-4 7-6z" class="j"></path><path d="M161 255h0c1 2 3 4 4 6h-1-2l1-1c-1-2-1-3-3-4l1-1z" class="I"></path><path d="M165 261c1 0 3 1 4 2 1 0 2 2 2 2-3 0-6-2-7-4h1z" class="H"></path><path d="M157 244h1c2 1 4 3 6 5 0-1 0-1 1-1h1 0c1 3 2 5 2 8-1-1-2-2-2-3-2 0-3 0-4 1v-1c0-1 0-2-1-3l-1-1-1-3c-1-1-1-2-2-2z" class="P"></path><path d="M159 246l7 7c-2 0-3 0-4 1v-1c0-1 0-2-1-3l-1-1-1-3z" class="E"></path><path d="M156 241v-1c1-1 2-1 3-1 2 1 3 2 4 4 0 2 1 3 2 5-1 0-1 0-1 1-2-2-4-4-6-5h-1l-1-3z" class="p"></path><path d="M153 242c1-1 2-3 3-4 1 1 1 1 3 1h0c-1 0-2 0-3 1v1l1 3c1 0 1 1 2 2l1 3 1 1c1 1 1 2 1 3v1l-1 1h0l-3-2-1-1h-1l-1 1v-2h-2 0v-6-3z" class="J"></path><path d="M155 251l1-2c1 0 5 3 6 4v1l-1 1h0l-3-2-1-1h-1l-1 1v-2z" class="K"></path><path d="M153 242c1-1 2-3 3-4 1 1 1 1 3 1h0c-1 0-2 0-3 1v1c-1 2-1 3-1 4s-1 1-1 2l-1 4v-6-3z" class="Y"></path><path d="M153 251h2v2l1-1h1l1 1 3 2-1 1c2 1 2 2 3 4l-1 1h0-1-3c0 1-2 3-3 3h-1c-1 1-2 1-4 1h1c0-1 2-3 2-4l-1-2h1l1 1h0l1-3v-1c-1-1-1-2-1-3h0c-1-1-1-1-1-2z" class="R"></path><path d="M160 256c2 1 2 2 3 4l-1 1h0-1l-1-5z" class="T"></path><path d="M153 251h2v2c1 2 3 3 4 4l-1 1-1 1c-1-2-1-3-2-4v1c-1-1-1-2-1-3h0c-1-1-1-1-1-2z" class="H"></path><path d="M155 257c1 2 0 5-1 7-1 1-2 1-4 1h1c0-1 2-3 2-4l-1-2h1l1 1h0l1-3z" class="L"></path><path d="M155 253l1-1h1l1 1 3 2-1 1c0 1 0 2-1 2v-1c-1-1-3-2-4-4z" class="m"></path><path d="M171 227v4l3 6c0 1 1 2 1 3 0 2 1 4 1 6h1l1 1h1c1 0 1 0 1 1v2c-1 0-1 0-1 1v1 1l1 1-1 1c-1-1-2-1-2-1h-1l-2 2c0-1-2-3-3-4v-2l-4-3s-1 0-1 1h0-1c-1-2-2-3-2-5-1-2-2-3-4-4h0c-2 0-2 0-3-1 2-2 6-4 8-5 3-2 5-4 7-6z" class="M"></path><path d="M166 234c1-1 2-1 3-2v1c1 2 1 3 0 5h0c-1 1-2 1-3 2 0 1 0 0-1 1 0-1-1-1-1-2 1-1 1-3 2-5z" class="P"></path><path d="M164 239c0 1 1 1 1 2 1-1 1 0 1-1 1-1 2-1 3-2h0l-2 6c2 2 4 2 6 2-1 0-2 0-3 1h-3s-1 0-1 1h0-1c-1-2-2-3-2-5l1-1v-3z" class="K"></path><path d="M163 243l1-1c0 2 0 2 1 4h1l1-2c2 2 4 2 6 2-1 0-2 0-3 1h-3s-1 0-1 1h0-1c-1-2-2-3-2-5z" class="F"></path><path d="M164 233c1 0 2 0 2 1-1 2-1 4-2 5v3l-1 1c-1-2-2-3-4-4h0c-2 0-2 0-3-1 2-2 6-4 8-5z" class="J"></path><path d="M176 246h1l1 1h1c1 0 1 0 1 1v2c-1 0-1 0-1 1v1 1l1 1-1 1c-1-1-2-1-2-1h-1l-2 2c0-1-2-3-3-4v-2l-4-3h3c1-1 2-1 3-1l3 1v-1z" class="c"></path><path d="M173 246l3 1h-1v1c0 1 1 2 1 3-2-1-4-3-6-4 1-1 2-1 3-1z" class="X"></path><path d="M171 250c2 1 4 2 5 4l-2 2c0-1-2-3-3-4v-2z" class="D"></path><path d="M176 246h1l1 1h1c1 0 1 0 1 1v2c-1 0-1 0-1 1v1 1c-1 0-2-1-3-2 0-1-1-2-1-3v-1h1v-1z" class="H"></path><path d="M176 251c0-1-1-2-1-3v-1c2 1 3 2 4 4v1 1c-1 0-2-1-3-2z" class="P"></path><path d="M361 65v-2-1l1-1c1 1 0 0 0 1 1 2 2 3 3 4v2h1c0 1 0 1 1 2v1l1 1h1l4 7c3 3 5 7 8 10 2 4 3 7 4 11v1c0 2 0 4-1 6h0l-1 2v1 1h2c0 1 0 1-1 2v2h0l-1 3 1 1 1-1c1 0 2 0 2-1l1 1c1 1 1 1 2 1h1l-3 4v1s0 1-1 2c0 1 0 2-1 3 0-2 0-2-1-3 0 1-1 2-1 3h-1c-1 0-3 3-4 4 0 2-1 4-2 5-2 3-4 5-6 7l-1-5-1 1h0-1l-2-3c-1-1-2-3-2-5h-1c0 1-1 1-2 0 0 1-1 2-1 3s0 1-1 2c0 1-1 3-3 4h0l1-3c0-1 0-1-1-1l-1-1c1 0 1-1 2-1 0-2 1-3 2-4l-1-1c-1-1-2-1-3-2h0c-1-1-2-1-2-3h-2 0c-1 0-1-1-2-2l-1 1c0-2-2-5-3-6l-1-2-1-3v-1l1 1 1 1c1-1 1-1 1-2s0-2 1-2v-3c1-2-2-5-3-8 0-1-1-2-1-3v-2h0l1 1v-3c1-3 2-5 3-7l1-1c2-3 4-7 6-10h-1v-1c1-2 3-4 4-5l1-1c0-1 0-1 1-1l2-2z" class="H"></path><path d="M376 94c1-1 1-2 1-2l1-1 1 1v1l-1 5v1c0 1-1 1-1 2-1-1-1-2-1-3 1-1 1-3 0-4z" class="P"></path><path d="M379 93c1 0 2 1 2 2l2 2c-1 1-1 1-1 2v1 2h0v-1h-1-1v-1c-1-1-1-1-2-1v-1l1-5z" class="I"></path><path d="M381 95l2 2c-1 1-1 1-1 2v1 2h0v-1h-1c0-2 0-2-1-3 0-2 0-2 1-3z" class="o"></path><path d="M369 80l1 1c2 1 1 0 2 2h0v1c1 2 1 5 2 7l1 5c-2-2-3-3-4-5-1-4-2-8-2-11z" class="P"></path><path d="M382 102v-2-1c0-1 0-1 1-2l2 3v1c0 2 0 4-1 6h0l-1 2v1l-1 1c0-1-1-1-2-1h0l-1-1-1-1h0c0-2-1-2-1-4 2-1 2 1 3 2 2-1 0-3 1-4h1 0z" class="m"></path><path d="M380 110l2-4h2v1l-1 2v1l-1 1c0-1-1-1-2-1z" class="b"></path><path d="M382 102v-2-1c0-1 0-1 1-2l2 3v1h0c-1 1-1 2-1 3v1l-2-2v-1z" class="F"></path><path d="M361 65v-2-1l1-1c1 1 0 0 0 1 1 2 2 3 3 4v2h1c0 1 0 1 1 2v1l1 1h1l4 7c3 3 5 7 8 10 2 4 3 7 4 11l-2-3-2-2c0-1-1-2-2-2v-1l-1-1-1 1s0 1-1 2v-1c0-2-1-3-1-5s-2-3-3-5h0c-1-2 0-1-2-2l-1-1c-1-3-3-5-3-7-1-1-1-3-1-4 0 0-1-1-2-1l-1 1c-1 0-3 0-4-1 0-1 0-1 1-1l2-2z" class="U"></path><path d="M361 65c1 0 2 1 3 2v1h-1l-1 1c-1 0-3 0-4-1 0-1 0-1 1-1l2-2z" class="p"></path><path d="M368 72h1l4 7c3 3 5 7 8 10 2 4 3 7 4 11l-2-3-2-2c0-1-1-2-2-2v-1l-1-1-1 1s0 1-1 2v-1c0-2-1-3-1-5l1 1c0-1 0-2 1-2-1-4-4-7-6-10-2-2-3-3-3-5z" class="H"></path><path d="M376 89c0-1 0-2 1-2l2 2c0 1 1 1 0 2v1l-1-1-1 1s0 1-1 2v-1c0-2-1-3-1-5l1 1z" class="m"></path><path d="M376 89c0-1 0-2 1-2l2 2h-1c-1 1-1 0-2 0z" class="W"></path><path d="M357 69l1-1c1 1 3 1 4 1l1-1c1 0 2 1 2 1l-1 1v1c0 2 0 4 1 6 0 2 2 5 2 7 0 3 2 6 3 9 1 2 5 6 5 8 0 1-1 2-2 3v-1l-4-6c-1-3-4-6-5-9-1 0-1 0-1-1-1-1-1-2-2-3-1 1-1 2 0 3l1 1-2 1h-1l-2 2v-2l2-9 1-1v-3h-1v-1c0-1 1-1 1-2 1 0 1-1 1-1v-2h0l-3 3h-1-1c0 1 0 1-1 2v-2c1-1 1-1 2-1l1-1c0-1 0-1-1-2z" class="f"></path><path d="M363 68c1 0 2 1 2 1l-1 1v1-1h-1c0 2 0 5 1 7 0 0-1 1 0 1v3 1c0 1 0 1-1 2v-2c-1-2 0-4-1-6 1-2 0-5 0-7l1-1z" class="K"></path><path d="M361 84h-1c0-3 1-6 1-9h1v1c1 2 0 4 1 6v2l2 3-1 1h0 0c-1 0-1 0-1-1-1-1-1-2-2-3z" class="Q"></path><path d="M357 69l1-1c1 1 3 1 4 1 0 2 1 5 0 7v-1h-1c0 3-1 6-1 9h1c-1 1-1 2 0 3l1 1-2 1h-1l-2 2v-2l2-9 1-1v-3h-1v-1c0-1 1-1 1-2 1 0 1-1 1-1v-2h0l-3 3h-1-1c0 1 0 1-1 2v-2c1-1 1-1 2-1l1-1c0-1 0-1-1-2zm8 18v1h1c0-2 0-3-1-4v-4c-1-1-1-2-1-3l2 3c-1 1 0 1 0 2v1 1h1c0 3 2 6 3 9 1 2 5 6 5 8 0 1-1 2-2 3v-1l-4-6c-1-3-4-6-5-9h0 0l1-1z" class="i"></path><path d="M369 97l1-1c2 1 3 3 3 5v2l-4-6z" class="d"></path><path d="M353 74c1-2 3-4 4-5 1 1 1 1 1 2l-1 1c-1 0-1 0-2 1v2c1-1 1-1 1-2h1 1l3-3h0v2s0 1-1 1c0 1-1 1-1 2v1h1v3l-1 1-2 9v2 1l-5 8c-1 2-2 5-3 6l-1-1c-1-1-2-2-2-3s0-2-1-3-1-1-1-3v-3c1-3 2-5 3-7l1-1c2-3 4-7 6-10h-1v-1z" class="i"></path><path d="M353 74c1-2 3-4 4-5 1 1 1 1 1 2l-1 1c-1 0-1 0-2 1v2c1 2-1 5-2 7s-1 5-2 6c0 1 0 2-1 2 0 2-1 3-2 4l3-8c1-2 1-4 2-6s1-3 1-5h-1v-1z" class="H"></path><path d="M358 73l3-3h0v2s0 1-1 1c0 1-1 1-1 2v1h1v3l-1 1-2 9v2 1l-5 8c-1 2-2 5-3 6l-1-1v-1h0c-1-2 0-3 1-5s3-3 3-5v-2l1-1c0-1 1-1 0-3 0-1 0-3 1-5l-1-1c1-2 3-5 2-7 1-1 1-1 1-2h1 1z" class="Q"></path><path d="M358 73l3-3h0v2s0 1-1 1c0 1-1 1-1 2v1l-3 13c0 2-1 3-2 4l1-5c1-4 2-8 3-11h-1s0 1-1 2c-1 3-1 6-3 9 0-1 0-3 1-5l-1-1c1-2 3-5 2-7 1-1 1-1 1-2h1 1z" class="K"></path><path d="M355 75c1-1 1-1 1-2h1 1l-2 4c-1 2-1 4-2 6l-1-1c1-2 3-5 2-7z" class="U"></path><path d="M360 89l2-1c1 2 3 3 4 5l-2 1c-1 3-3 5-4 8l-3 5-2 2-4 6 1 2h0c0 1 0 2 1 3 0 1 0 3 1 4v1l2 2-1 2h0c-1-1-2-1-2-3h-2 0c-1 0-1-1-2-2l-1 1c0-2-2-5-3-6l-1-2-1-3v-1l1 1 1 1c1-1 1-1 1-2s0-2 1-2v-3c1-2-2-5-3-8 0-1-1-2-1-3v-2h0l1 1c0 2 0 2 1 3s1 2 1 3 1 2 2 3l1 1c1-1 2-4 3-6s3-5 5-8v-1l2-2h1z" class="i"></path><path d="M353 104v1c0 2-1 4-1 5-1 1-1 2-1 3-1 1-1 0-1 1 0-2-1-3 0-4s2-5 3-6z" class="H"></path><path d="M344 117c1-1 2-1 3-1 0 3 1 6 2 8l-1 1c0-2-2-5-3-6l-1-2z" class="c"></path><path d="M357 92c0 2 0 2-1 3-2 2-4 5-5 8-2 4-4 8-4 13-1 0-2 0-3 1l-1-3v-1l1 1 1 1c1-1 1-1 1-2s0-2 1-2v-3c1-2-2-5-3-8 0-1-1-2-1-3v-2h0l1 1c0 2 0 2 1 3s1 2 1 3 1 2 2 3l1 1c1-1 2-4 3-6s3-5 5-8z" class="G"></path><path d="M353 105l1-1 2 2 1 1-2 2-4 6 1 2h0c0 1 0 2 1 3 0 1 0 3 1 4v1l2 2-1 2h0c-1-1-2-1-2-3h-2 0c0-2-1-4-2-6l-1-1c0-3-1-5 0-8h1v-1 1h0v2 2l1-1 1-1c0-1 0-2 1-3 0-1 1-3 1-5z" class="Q"></path><path d="M353 126v-2c-2-3-2-5-2-9l1 2h0c0 1 0 2 1 3 0 1 0 3 1 4v1l2 2-1 2h0c-1-1-2-1-2-3zm7-37l2-1c1 2 3 3 4 5l-2 1c-1 3-3 5-4 8l-3 5-1-1-2-2-1 1v-1c0-2 0-4 1-5s2-1 2-2l1-1-1-1c1-1 1-1 1-3v-1l2-2h1z" class="K"></path><path d="M361 90l1 1c0 1 0 1-1 2l-1-1v-1l1-1z" class="H"></path><path d="M360 89l2-1c1 2 3 3 4 5l-2 1c-2 0-3 0-4 1-2 2-3 5-4 7 0-2 1-4 2-5 0-1 1-1 1-2 1-1 1-2 2-2 1-1 1-1 1-2l-1-1h0l-1-1z" class="Q"></path><path d="M356 102c1-2 2-5 4-7 1-1 2-1 4-1-1 3-3 5-4 8l-3 5-1-1-2-2 2-2z" class="q"></path><path d="M361 84c1 1 1 2 2 3 0 1 0 1 1 1 1 3 4 6 5 9l4 6v1l2 2h0c1 1 1 1 2 1v1 2c1 2 1 4 1 6h1c0-2 1-4 1-6h0c1 0 2 0 2 1l1-1v1h2c0 1 0 1-1 2v2h0l-1 3 1 1 1-1c1 0 2 0 2-1l1 1c1 1 1 1 2 1h1l-3 4v1s0 1-1 2c0 1 0 2-1 3 0-2 0-2-1-3 0 1-1 2-1 3h-1c-1 0-3 3-4 4 0 2-1 4-2 5-2 3-4 5-6 7l-1-5-1 1h0-1l-2-3c-1-1-2-3-2-5h-1c0 1-1 1-2 0 0 1-1 2-1 3s0 1-1 2c0 1-1 3-3 4h0l1-3c0-1 0-1-1-1l-1-1c1 0 1-1 2-1 0-2 1-3 2-4l-1-1c-1-1-2-1-3-2l1-2-2-2v-1c-1-1-1-3-1-4-1-1-1-2-1-3h0l-1-2 4-6 2-2 3-5c1-3 3-5 4-8l2-1c-1-2-3-3-4-5l-1-1c-1-1-1-2 0-3z" class="c"></path><path d="M374 128v2l-1 1-1-1 2-2z" class="h"></path><path d="M380 121l-3 5c0-2 0-4 1-6l1-3c0 2 0 2 1 4z" class="X"></path><path d="M382 111l1-1v1h2c0 1 0 1-1 2v2h0l-1-1c-1 2-2 5-3 7-1-2-1-2-1-4h0v-1c0-2 1-4 1-6h0c1 0 2 0 2 1z" class="U"></path><path d="M380 110h0c1 0 2 0 2 1l-3 6v-1c0-2 1-4 1-6z" class="G"></path><defs><linearGradient id="At" x1="363.98" y1="132.561" x2="377.535" y2="109.283" xlink:href="#B"><stop offset="0" stop-color="#868586"></stop><stop offset="1" stop-color="#b6b5b5"></stop></linearGradient></defs><path fill="url(#At)" d="M373 105c2 2 3 5 3 8 1 4 1 8-1 12v1l-1 2-2 2h0c0 1-1 1-1 1-2 1-3 3-4 5 0-1-1-2-1-4v-1h1c1-1 2-1 3-2s2-3 2-5c2-3 2-11 1-14 0-2-1-3 0-5z"></path><defs><linearGradient id="Au" x1="375.537" y1="120.941" x2="379.718" y2="136.301" xlink:href="#B"><stop offset="0" stop-color="#c3c2c2"></stop><stop offset="1" stop-color="#e8e7e8"></stop></linearGradient></defs><path fill="url(#Au)" d="M385 118c1 0 2 0 2-1l1 1c1 1 1 1 2 1h1l-3 4v1s0 1-1 2c0 1 0 2-1 3 0-2 0-2-1-3 0 1-1 2-1 3h-1c-1 0-3 3-4 4 0 2-1 4-2 5-2 3-4 5-6 7l-1-5-1 1h0-1l-2-3c-1-1-2-3-2-5 0-1-1-1-1-2v-1 1c1 1 1 1 2 0v-3l1-1s1 1 1 2v2h-1v1c0 2 1 3 1 4l1 1 8-6c2-4 5-7 6-11l1-2 1 1 1-1z"></path><path d="M366 127s1 1 1 2v2h-1v1c0-1 0-3-1-4l1-1z" class="H"></path><path d="M385 126c1-2 1-4 2-5 1 0 1 1 1 3 0 0 0 1-1 2 0 1 0 2-1 3 0-2 0-2-1-3zm-3-6c1 1 1 1 1 2 0 2-1 4-2 6l-1-1 2-2h0c-2 0-3 1-3 3l-3 3h0c2-4 5-7 6-11z" class="q"></path><path d="M379 133c0 2-1 4-2 5-2 3-4 5-6 7l-1-5v-1h1c0-1 1-1 2-1 1-1 1 0 1-1 2-1 3-2 5-4z" class="c"></path><path d="M371 139c0-1 1-1 2-1 0 2 0 3-1 4v-1c-1 0-1-1-1-2h0z" class="K"></path><path d="M366 93l4 7v1l2 2 1 2c-1 2 0 3 0 5 1 3 1 11-1 14 0 2-1 4-2 5s-2 1-3 2v-2c0-1-1-2-1-2 0-1 1-2 1-3v-1l-1-1c0-1 0-1 1-1v-1c1 0 1 1 1 1l1-1-1-1h1 1 0v-2h0c-1-4-3-7-5-10-2-1-3-2-4-2h-1c0-1 0-1 1-2l-1-1c1-3 3-5 4-8l2-1z" class="U"></path><path d="M370 101l2 2-1 1h-1v-3z" class="m"></path><path d="M361 105c1 0 2 0 2-1 1 0 1-1 1-1 1 1 1 2 1 3v1c-2-1-3-2-4-2z" class="W"></path><path d="M370 117v1l1 7v2-1c0-1 1-1 1-2 0 2-1 4-2 5s-2 1-3 2v-2c0-1-1-2-1-2 0-1 1-2 1-3v-1l-1-1c0-1 0-1 1-1v-1c1 0 1 1 1 1l1-1-1-1h1 1 0v-2z" class="f"></path><path d="M369 125h1 0v4c-1 1-2 1-3 2v-2c1-1 2-3 2-4z" class="K"></path><path d="M369 120h0c1 1 1 2 1 3s0 1-1 2c0 1-1 3-2 4 0-1-1-2-1-2 0-1 1-2 1-3v-1l-1-1c0-1 0-1 1-1v-1c1 0 1 1 1 1l1-1z" class="P"></path><path d="M367 120c1 0 1 1 1 1l-1 2-1-1c0-1 0-1 1-1v-1z" class="o"></path><path d="M366 93l4 7h-3 0c0 1 0 1 1 2 1 2 2 3 2 5 1 2 2 3 2 5h-1c-2-2-3-5-5-7v-1c0-1-1-3-2-5v4s0 1-1 1c0 1-1 1-2 1h-1c0-1 0-1 1-2l-1-1c1-3 3-5 4-8l2-1z" class="f"></path><path d="M361 103c1-1 1-4 2-5h1v1 4s0 1-1 1c0 1-1 1-2 1h-1c0-1 0-1 1-2z" class="r"></path><path d="M360 102l1 1c-1 1-1 1-1 2h1c1 0 2 1 4 2 2 3 4 6 5 10h0v2h0-1-1l1 1-1 1s0-1-1-1v1c-1 0-1 0-1 1l1 1v1c0 1-1 2-1 3l-1 1v3c-1 1-1 1-2 0v-1 1c0 1 1 1 1 2h-1c0 1-1 1-2 0 0 1-1 2-1 3s0 1-1 2c0 1-1 3-3 4h0l1-3c0-1 0-1-1-1l-1-1c1 0 1-1 2-1 0-2 1-3 2-4l-1-1c-1-1-2-1-3-2l1-2-2-2v-1c-1-1-1-3-1-4-1-1-1-2-1-3h0l-1-2 4-6 2-2 3-5z" class="I"></path><path d="M356 110c1-1 0-1 2-1v1 1h-1l-1-1z" class="m"></path><path d="M355 111h1l1 2c-1 1-2 2-2 3h-1l-1-1c1-2 1-3 2-4z" class="K"></path><path d="M352 117h2v2c-1 2 1 4 1 5h-1v1-1c-1-1-1-3-1-4-1-1-1-2-1-3z" class="d"></path><path d="M360 102l1 1c-1 1-1 1-1 2s-1 3-1 4 0 1-1 1v-1c-2 0-1 0-2 1l-1-1 2-2 3-5z" class="a"></path><path d="M362 110c-1-1-1-1-1-2h1c1 1 2 1 2 2 2 2 3 3 4 5 0 1 1 1 1 2h1 0v2h0-1-1l1 1-1 1s0-1-1-1v-1c0-1 0-2-1-2v-1c-1-2-3-4-4-6h0z" class="K"></path><path d="M362 110h0c1 2 3 4 4 6v1c1 0 1 1 1 2v1 1l-1-1c-1-2-3-4-5-6l-4 4c0 1-1 2-1 2h-1v-1c0-4 4-7 7-9z" class="W"></path><path d="M355 120h1s1-1 1-2l4-4c2 2 4 4 5 6l1 1c-1 0-1 0-1 1h-2c-1 0-1 1-2 1h-2l-1 2h-1v-1l-2-2-1-2z" class="P"></path><path d="M361 117h1l1 1c1 1 1 1 1 2h-1-2-1c0-1 1-2 1-3z" class="q"></path><path d="M356 122l2-2h1c0 1 0 2 1 3l-1 2h-1v-1l-2-2z" class="Q"></path><path d="M360 123h2c1 0 1-1 2-1h2l1 1v1c0 1-1 2-1 3l-1 1v3c-1 1-1 1-2 0v-1 1c0 1 1 1 1 2h-1c0 1-1 1-2 0 0 1-1 2-1 3s0 1-1 2c0 1-1 3-3 4h0l1-3c0-1 0-1-1-1l-1-1c1 0 1-1 2-1 0-2 1-3 2-4l-1-1c-1-1-2-1-3-2l1-2-2-2v-1h1c2 1 1 1 3 1h1l1-2z" class="l"></path><path d="M360 127h1c0 2-2 3-2 5l-1-1c-1-1-2-1-3-2l1-2h1 3z" class="P"></path><path d="M357 127h3l-1 2h-1l-1-2z" class="U"></path><path d="M357 139c2-3 3-8 5-10l1 1v1c0 1 1 1 1 2h-1c0 1-1 1-2 0 0 1-1 2-1 3s0 1-1 2c0 1-1 3-3 4h0l1-3z" class="a"></path><path d="M360 123h2c1 0 1-1 2-1h2l1 1v1c-2 1-4 3-6 3-1 0-3-1-4 0l-1-1c1 0 2 0 3-1h0l1-2z" class="p"></path><path d="M452 143c2 1 4 2 6 2 1 1 1 1 1 2h4 1l4 1v1h4l4-1h3c2-1 4-1 6-2v1h4 0l1-2c0 1 1 1 2 1 1 1 3 1 4 2h2 0c1-1 2-2 3-2h0v-2h2l3-1h1c2 0 2 0 3 1l2 4v1h-1l-1 1s0 2 1 3c0 1-1 2 0 3h-1v2h-1l-1-1v5l-2-1v1l3 3h0-1v1l2 2c0 1 0 1 1 2h-1c0 1 1 1 1 2l2 1h-1v1l4 2c0 3 2 2 2 4-1 0-1 0-2-1h0l-1 1 1 1-1 1c4 2 6 4 9 9h-1c-1-1-2-1-2-1h-2 0c-1-1-2-2-2-4 0 2 0 4-1 5-1 0-1-1-2-3l-2-2v-2c-3-2-8-4-11-6l2 2h-3l-3-2-1 1c-1 0-1-1-2 0l-3-2c-1 0-2-2-3-3l-10-3c-2 0-4 0-6-1h-3c-2 1-4 1-6 1h-1c-1 1-1 1-2 1l-1 2c-1 1-2 1-3 1l1 1-1 1h0l-1 1c-1 0-2 0-3 2-1 0-3 0-5 1l4 2h0l1 1h-1l-1 1h0c-3 1-5 1-7 3-3 1-5 1-8 3v-1h0c-1 0-2 0-3-1h-1l-3 1c-1 1-2 2-4 2h-1c-1 0 0-1-1-1 0-1 0-1-1-1l1-3s1-1 1-2h-1c0-2 2-4 2-6l2-1 1-2h-1c-1-1-1-2-1-3l3-3c1-1 2-1 2-3 0-1 1-1 2-2v-1l-2 1v-1h-1l-1-1-1 1-1-2 1-1c0-1 1-2 0-3 1-3 3-5 3-8h1v3h1c1-1 1-2 1-3l1-1h0v3h1v-1c1-1 1-2 2-3h0c1-1 1-2 2-2h1v1h1 0l1 1c1-1 2-1 3-2h4v-1h0 1l-1-1v-1c1 1 1 1 2 1l1-1z" class="J"></path><path d="M457 152c2-1 5-1 7-1l1 1-1 1h-2l-1-1h-4z" class="M"></path><path d="M491 158v-1h1c2 1 2 2 3 4v1h0-1v1h-1c0-2-1-3-2-5z" class="R"></path><path d="M490 154l5 1 5 3 3 3s0 1-1 1l-1-1-1-1c-4-2-7-4-11-6h1z" class="O"></path><path d="M472 149l4-1c2 2 5 2 7 3h1 1c1 1 1 1 2 1h1c0 1 1 1 2 2h-1c-2 0-6-2-8-2l-9-3z" class="T"></path><path d="M500 160l1 1c0 2 1 3 0 4h1v1l-1 1v1s0 1-1 2l-1-2c-2-2-3-4-4-6v-1c2 1 3 1 5 2v1l1-1c-1-1-1-2-1-3z" class="S"></path><path d="M499 168h1c0-2-1-3-1-4h0l2 1h1v1l-1 1v1s0 1-1 2l-1-2z" class="N"></path><defs><linearGradient id="Av" x1="457.981" y1="144.972" x2="455.415" y2="151.715" xlink:href="#B"><stop offset="0" stop-color="#63635f"></stop><stop offset="1" stop-color="#78767d"></stop></linearGradient></defs><path fill="url(#Av)" d="M447 148c2 0 5-1 7-1h9 1l4 1v1c-7 0-14-1-20 1-1 0-1-1-1-2z"></path><defs><linearGradient id="Aw" x1="492.967" y1="165.262" x2="504.422" y2="171.232" xlink:href="#B"><stop offset="0" stop-color="#535352"></stop><stop offset="1" stop-color="#807e82"></stop></linearGradient></defs><path fill="url(#Aw)" d="M494 163v-1h1 0c1 2 2 4 4 6l1 2 2 2 3 3c-1 0-1 0-1 1-1 0-6-4-7-5 0-2-1-3-2-4 0-2-1-3-1-4z"></path><path d="M452 143c2 1 4 2 6 2 1 1 1 1 1 2h4-9c-2 0-5 1-7 1l-6 2h-1l2-2c1-1 2-1 3-2h4v-1h0 1l-1-1v-1c1 1 1 1 2 1l1-1z" class="T"></path><path d="M503 161c0 1 0 0 1 1v-1l2 1 3 3h0-1v1l2 2c0 1 0 1 1 2h-1c-1 0-1 0-2 1l-4-4h-1c0 1-1 1 0 2v1c0 1 0 1-1 2l-2-2c1-1 1-2 1-2v-1l1-1v-1h-1c1-1 0-2 0-4l1 1c1 0 1-1 1-1z" class="V"></path><path d="M503 161c0 1 0 0 1 1v2 1h-2-1c1-1 0-2 0-4l1 1c1 0 1-1 1-1z" class="C"></path><path d="M503 161c0 1 0 0 1 1v2l-2-2c1 0 1-1 1-1z" class="B"></path><path d="M504 162v-1l2 1 3 3h0-1v1l2 2-1 1c-1 0-3-3-4-4h0v-1c0-1 0-2-1-2z" class="J"></path><path d="M485 146v1h4 0l1-2c0 1 1 1 2 1l-2 1v1h0c0 1 1 3 3 3 0 1 1 1 1 2h0l1 2-5-1c-1-1-2-1-2-2h-1c-1 0-1 0-2-1h-1-1c-2-1-5-1-7-3h3c2-1 4-1 6-2z" class="e"></path><path d="M486 150c0-1 1-2 2-2 1 1 1 1 2 3h-1c-1 0-2 0-3-1z" class="g"></path><path d="M486 150c1 1 2 1 3 1h1l3 3 1-1 1 2-5-1c-1-1-2-1-2-2h-1c-1 0-1 0-2-1h-1l2-1z" class="j"></path><defs><linearGradient id="Ax" x1="505.119" y1="166.379" x2="513.728" y2="179.454" xlink:href="#B"><stop offset="0" stop-color="#737373"></stop><stop offset="1" stop-color="#898789"></stop></linearGradient></defs><path fill="url(#Ax)" d="M503 170v-1c-1-1 0-1 0-2h1l4 4c1-1 1-1 2-1 0 1 1 1 1 2l2 1h-1v1l4 2c0 3 2 2 2 4-1 0-1 0-2-1h0l-1 1c-3-1-7-4-10-5l-3-3c1-1 1-1 1-2z"></path><path d="M511 172l2 1h-1v1l-2-1v-1h1z" class="F"></path><defs><linearGradient id="Ay" x1="507.979" y1="170.523" x2="508.905" y2="179.044" xlink:href="#B"><stop offset="0" stop-color="#59595a"></stop><stop offset="1" stop-color="#797676"></stop></linearGradient></defs><path fill="url(#Ay)" d="M503 170c1 1 2 3 4 3 3 2 6 3 9 6l-1 1c-3-1-7-4-10-5l-3-3c1-1 1-1 1-2z"></path><defs><linearGradient id="Az" x1="485.378" y1="162.406" x2="492.603" y2="161.022" xlink:href="#B"><stop offset="0" stop-color="#141415"></stop><stop offset="1" stop-color="#2f2e2e"></stop></linearGradient></defs><path fill="url(#Az)" d="M483 156c2 0 3 0 4 1s2 3 3 5h2c0-1-1-2-1-4 1 2 2 3 2 5h1c0 1 1 2 1 4 1 1 2 2 2 4l-2-1h-2c1 1 1 2 2 3h-1c-1 0-3-1-4-2l-1-1c0-2-1-3 0-5l-1-2-3-4c0-1-1-2-2-3z"></path><path d="M490 162h-1c-1-2-2-3-3-5h1c1 1 2 3 3 5z" class="D"></path><path d="M491 167c1 0 1 0 2 1 1 0 1 1 2 2h0-2s-2-2-2-3z" class="e"></path><path d="M489 165s1 2 2 2c0 1 2 3 2 3 1 1 1 2 2 3h-1c-1 0-3-1-4-2l-1-1c0-2-1-3 0-5z" class="Y"></path><path d="M472 154l1-1c1 0 1 0 2 1l1 1c1 1 1 1 2 1l1-1 1-1h-1 0c2 0 3 0 4 1v1h0c1 1 2 2 2 3l3 4 1 2c-1 2 0 3 0 5l-1-1h-1c-1-5-6-7-10-9l-3-3h-1c0-1-1-2-1-3h0z" class="S"></path><path d="M480 154h-1 0c2 0 3 0 4 1v1h0c1 1 2 2 2 3l-1 2-4-7z" class="C"></path><path d="M474 157h-1c0-1-1-2-1-3h0c2 2 4 3 6 5 2 1 5 2 7 4h1c-1-1-1-2-2-2l1-2 3 4 1 2c-1 2 0 3 0 5l-1-1h-1c-1-5-6-7-10-9l-3-3z" class="Z"></path><path d="M488 169v-2c-1-1 0 0 0-1 0 0-1-1-1-2l1-1 1 2c-1 2 0 3 0 5l-1-1z" class="N"></path><path d="M446 154c3-1 7-2 11-2h4l1 1h2 2 1l4-1c2 0 3 0 4 1h0c2 1 3 1 4 2l-1 1c-1 0-1 0-2-1l-1-1c-1-1-1-1-2-1l-1 1h0c0 1 1 2 1 3h1c-1 0-1 1-2 2 0-1 0-1-1-1l-5-2h-1c0-1 0-1-1-1l-1 1 1 1c-1 0-2-1-4-1 1-1 2-1 2-2v-1l-2 1v-1l-2 2h-1c-1 0-3 0-4 1h-1v-1h-2l-5 2v-1-1h-1l2-1z" class="T"></path><path d="M466 153h1c1 0 2 0 3 1h-7v-1h3z" class="Z"></path><path d="M450 155h1c2-1 6-2 9-2l-2 2h-1c-1 0-3 0-4 1h-1v-1h-2z" class="B"></path><path d="M495 170l2 1c1 1 6 5 7 5 0-1 0-1 1-1 3 1 7 4 10 5l1 1-1 1c4 2 6 4 9 9h-1c-1-1-2-1-2-1h-2 0c-1-1-2-2-2-4 0 2 0 4-1 5-1 0-1-1-2-3l-2-2v-2c-3-2-8-4-11-6-2-2-4-4-6-5-1-1-1-2-2-3h2z" class="j"></path><path d="M514 188h0c1-1 1-2 0-3l-1-1 1-1c2 1 5 5 7 6v1h-2 0c-1-1-2-2-2-4 0 2 0 4-1 5-1 0-1-1-2-3z" class="Z"></path><path d="M504 176c0-1 0-1 1-1 3 1 7 4 10 5l1 1-1 1c-4-2-8-4-11-6z" class="a"></path><path d="M437 148h0c1-1 1-2 2-2h1v1h1 0l1 1-2 2h1l6-2c0 1 0 2 1 2-2 1-6 2-7 3h0c-1 3-4 2-6 5h0 1 1c0-1 1-1 2-2 2-2 4-2 7-2l-2 1h1v1 1c-1 0-2 0-3 1h0 0l-3 3c-2 1-4 2-6 4l-3 2c0-1 1-1 2-2v-1l-2 1v-1h-1l-1-1-1 1-1-2 1-1c0-1 1-2 0-3 1-3 3-5 3-8h1v3h1c1-1 1-2 1-3l1-1h0v3h1v-1c1-1 1-2 2-3z" class="D"></path><path d="M441 147h0l1 1-2 2c0-1 0 0-1-1l2-2z" class="Y"></path><path d="M444 155h1v1 1c-1 0-2 0-3 1h0c-2 0-3 1-5 1 3-2 4-3 7-4z" class="H"></path><path d="M441 150l6-2c0 1 0 2 1 2-2 1-6 2-7 3s-3 2-4 3c1-3 2-4 4-6z" class="I"></path><path d="M442 158h0l-3 3c-2 1-4 2-6 4v-1-1c0-2 1-2 2-3 1 0 1-1 2-1 2 0 3-1 5-1z" class="l"></path><path d="M437 148h0c1-1 1-2 2-2-1 4-4 7-5 12 0 1 0 2 1 2-1 1-2 1-2 3v1 1l-3 2c0-1 1-1 2-2v-1l-2 1v-1h-1l-1-1-1 1-1-2 1-1c0-1 1-2 0-3 1-3 3-5 3-8h1v3h1c1-1 1-2 1-3l1-1h0v3h1v-1c1-1 1-2 2-3z" class="F"></path><path d="M431 156h1c0 1 0 3-1 4 0 1-2 2-3 3l-1 1-1-2 1-1h1c1-1 3-4 3-5z" class="P"></path><path d="M430 164c2-2 3-4 3-7v-1h1v2c0 1 0 2 1 2-1 1-2 1-2 3v1 1l-3 2c0-1 1-1 2-2v-1l-2 1v-1z" class="W"></path><path d="M430 150h1v3h1 0c0 1-1 2-1 3s-2 4-3 5h-1c0-1 1-2 0-3 1-3 3-5 3-8z" class="R"></path><path d="M428 161c0-2 1-3 1-5 1-1 1-2 3-3 0 1-1 2-1 3s-2 4-3 5z" class="B"></path><path d="M507 143c2 0 2 0 3 1l2 4v1h-1l-1 1s0 2 1 3c0 1-1 2 0 3h-1v2h-1l-1-1v5l-2-1v1l-2-1v1c-1-1-1 0-1-1l-3-3-5-3-1-2h0c0-1-1-1-1-2-2 0-3-2-3-3h0v-1l2-1c1 1 3 1 4 2h2 0c1-1 2-2 3-2h0v-2h2l3-1h1z" class="j"></path><path d="M510 150s0 2 1 3c0 1-1 2 0 3h-1-4l1-1c0-1 1-1 1-1 2-1 2-2 2-4h0z" class="E"></path><path d="M512 148v1h-1l-1 1h0-1c-2 1-4 2-6 4h0l-1-1 2-2 5-3c1 0 1 0 1 1 1 0 1-1 2-1z" class="C"></path><path d="M500 154v-1h1 1l1 1c0 1-1 2 0 3l1 1c1 1 1 2 2 3h0v1l-2-1v1c-1-1-1 0-1-1l-3-3h0c1-2 1-2 0-4z" class="R"></path><path d="M504 158c1-1 2-1 2-3h1l-1 1h4v2h-1l-1-1v5l-2-1h0c-1-1-1-2-2-3z" class="S"></path><path d="M506 161l1-1h0c-1-1-1-2 0-3h1v5l-2-1h0z" class="Z"></path><path d="M507 143c2 0 2 0 3 1l2 4c-1 0-1 1-2 1 0-1 0-1-1-1l-5 3h-2c-1 0-2 0-3-1l1-1c-1-1-1-1-2-1 1-1 2-2 3-2h0v-2h2l3-1h1z" class="g"></path><path d="M501 144h2l-1 1c1 0 2 1 3 1h1v1h-1c-2 0-4 1-5 2-1-1-1-1-2-1 1-1 2-2 3-2h0v-2zm6-1c2 0 2 0 3 1l2 4c-1 0-1 1-2 1 0-1 0-1-1-1 0-1-1-1-1-2h-1c0-1 0-2-1-3h1z" class="J"></path><path d="M492 146c1 1 3 1 4 2h2 0c1 0 1 0 2 1l-1 1c1 1 2 1 3 1h2l-2 2h-1-1v1c1 2 1 2 0 4h0l-5-3-1-2h0c0-1-1-1-1-2-2 0-3-2-3-3h0v-1l2-1z" class="D"></path><path d="M501 153l-2-2c-1 0-1 0-1-1h1c1 1 2 1 3 1h2l-2 2h-1z" class="J"></path><path d="M494 153h0c0-1-1-1-1-2-2 0-3-2-3-3h0c2 1 3 1 4 2s1 1 2 1c2 1 3 3 4 3 1 2 1 2 0 4h0l-5-3-1-2z" class="g"></path><path d="M464 157l-1-1 1-1c1 0 1 0 1 1h1l5 2c1 0 1 0 1 1 1-1 1-2 2-2l3 3c4 2 9 4 10 9h1l1 1 1 1c1 1 3 2 4 2h1c2 1 4 3 6 5l2 2h-3l-3-2-1 1c-1 0-1-1-2 0l-3-2c-1 0-2-2-3-3l-10-3c-2 0-4 0-6-1h-3c-2 1-4 1-6 1h-1c1-1 1-2 2-4h-1l-2-2c-1 1-3 1-4 2s-2 1-3 2c-1-1 0-1-1-1s-2 1-3 1l-1-1h-1c-1 0-1 0-1-1l2-2h1l5-4c2-2 4-3 5-5 2 0 3 1 4 1z" class="D"></path><path d="M477 166c1 0 1 0 2 1-1 1-1 2-2 2s-2 0-3-1h0v-1c1 0 2 0 3-1z" class="Z"></path><path d="M488 174c3 1 6 3 9 4l-1 1c-1 0-1-1-2 0l-3-2c-1 0-2-2-3-3z" class="E"></path><path d="M474 161c-1 0-2-1-2-1v-1l2 1 9 5v1c-2 0-3-1-5-2h-1l-3-3z" class="B"></path><path d="M490 171v1c-3-1-6-2-8-4v-1h0c2 0 4 1 5 2h1l1 1 1 1z" class="J"></path><path d="M474 161l3 3h1l-2 1 1 1c-1 1-2 1-3 1v1h0l-2 1v-1l-1-1-1-1h0c2 0 3 0 4-2l-2-1h-1c1-1 2-1 3-2z" class="T"></path><path d="M474 164l2 1-1 1h0-2c0 1 0 1 1 1v1h0l-2 1v-1l-1-1-1-1h0c2 0 3 0 4-2z" class="N"></path><path d="M469 164c1-1 2-1 3-1l2 1c-1 2-2 2-4 2h0l1 1 1 1v1 1h-3c-2 1-4 1-6 1h-1c1-1 1-2 2-4 1-1 2-2 3-2v-1h2z" class="T"></path><path d="M470 166l1 1 1 1v1 1h-3v-2h-1-1 2l1-2z" class="E"></path><path d="M470 166l1 1 1 1h-2-1l1-2z" class="T"></path><path d="M469 164c1-1 2-1 3-1l2 1c-1 2-2 2-4 2h0l-1 2h-2v-1-2-1h2z" class="V"></path><path d="M467 164h2c0 1-1 2-2 3v-2-1z" class="E"></path><path d="M464 157l-1-1 1-1c1 0 1 0 1 1h1l5 2c1 0 1 0 1 1 1 0 2 1 2 1l-2-1v1s1 1 2 1c-1 1-2 1-3 2h1c-1 0-2 0-3 1h-2v1c-1 0-2 1-3 2h-1l-2-2c-1 1-3 1-4 2s-2 1-3 2c-1-1 0-1-1-1s-2 1-3 1l-1-1h-1c-1 0-1 0-1-1l2-2h1l5-4c2-2 4-3 5-5 2 0 3 1 4 1z" class="Z"></path><path d="M467 164h-1v-1s1-1 2-1h1l1 1-1 1h-2zm-5-5c2 0 4-1 6 0h0c2 0 3 0 4 1l-1 1c-2 0-5-1-7-1l-2-1z" class="D"></path><path d="M458 163h1l-3 3c2-1 6-3 9-2h0c-2 1-3 1-4 1-1 1-3 1-4 2s-2 1-3 2c-1-1 0-1-1-1s-2 1-3 1l-1-1h-1l10-5z" class="F"></path><defs><linearGradient id="BA" x1="454.267" y1="157.527" x2="455.411" y2="166.421" xlink:href="#B"><stop offset="0" stop-color="#1e1d1d"></stop><stop offset="1" stop-color="#444344"></stop></linearGradient></defs><path fill="url(#BA)" d="M460 156c2 0 3 1 4 1v1h-1c-1 0-1 0-2 1h1l2 1-6 3-10 5c-1 0-1 0-1-1l2-2h1l5-4c2-2 4-3 5-5z"></path><path d="M460 153v1l2-1v1c0 1-1 1-2 2-1 2-3 3-5 5l-5 4h-1l-2 2c0 1 0 1 1 1h1l1 1c1 0 2-1 3-1s0 0 1 1c1-1 2-1 3-2s3-1 4-2l2 2h1c-1 2-1 3-2 4s-1 1-2 1l-1 2c-1 1-2 1-3 1l1 1-1 1h0l-1 1c-1 0-2 0-3 2-1 0-3 0-5 1l4 2h0l1 1h-1l-1 1h0c-3 1-5 1-7 3-3 1-5 1-8 3v-1h0c-1 0-2 0-3-1h-1l-3 1c-1 1-2 2-4 2h-1c-1 0 0-1-1-1 0-1 0-1-1-1l1-3s1-1 1-2h-1c0-2 2-4 2-6l2-1 1-2h-1c-1-1-1-2-1-3l3-3c1-1 2-1 2-3l3-2c2-2 4-3 6-4l3-3h0 0c1-1 2-1 3-1l5-2h2v1h1c1-1 3-1 4-1h1l2-2z" class="G"></path><path d="M435 175c1-3 1-5 4-7v1l-1 3c0 1-1 3-2 4l-1-1z" class="U"></path><path d="M438 172l1 1c-1 1-2 3-2 5h2c-1 0-1 1-1 1l-1-1h0c-1 0-1 1-1 1h-1 0c0 1-1 1-2 1s0 0-1 1l-1-1v-1l3-3c1 0 1 0 1-1l1 1c1-1 2-3 2-4z" class="b"></path><defs><linearGradient id="BB" x1="447.633" y1="158.363" x2="445.44" y2="170.245" xlink:href="#B"><stop offset="0" stop-color="#1c181a"></stop><stop offset="1" stop-color="#2d302f"></stop></linearGradient></defs><path fill="url(#BB)" d="M451 160c1 0 2-1 2-1l1 1h-1v1c-2 1-4 2-6 4-2 3-6 5-8 8l-1-1 1-3 3-3c2-2 5-3 7-5 1 0 2-1 2-1z"></path><path d="M430 178h1c-1 2-3 4-4 5 2-1 3-2 4-3l1 1-6 6h0l-1-1h0c-1 1-2 1-3 1 0 0 1-1 1-2h-1c0-2 2-4 2-6l2-1 2 1 2-1z" class="I"></path><path d="M424 179l2-1 2 1c-2 2-4 4-5 6h-1c0-2 2-4 2-6z" class="B"></path><path d="M447 165h2l-2 2c0 1 0 1 1 1h1c-2 1-4 2-5 3v3l-1 1c-1 1-2 3-3 4l-1-1h-2c0-2 1-4 2-5 2-3 6-5 8-8z" class="L"></path><path d="M442 173h0c0-2 3-5 5-6 0 1 0 1 1 1h1c-2 1-4 2-5 3l-2 2z" class="R"></path><path d="M442 173l2-2v3l-1 1c-1 1-2 3-3 4l-1-1v-1c1-1 2-2 3-4z" class="K"></path><path d="M460 153v1l2-1v1c0 1-1 1-2 2-1 2-3 3-5 5l-5 4h-1-2c2-2 4-3 6-4v-1h1l-1-1s-1 1-2 1c0 0-1 1-2 1v-1l5-3-1-1c1-1 3-1 4-1h1l2-2z" class="E"></path><path d="M458 155h1c-1 2-5 5-6 6v-1h1l-1-1s-1 1-2 1c2-1 3-2 4-3l2-2h0 1z" class="k"></path><path d="M453 156c1-1 3-1 4-1h0l-2 2c-1 1-2 2-4 3 0 0-1 1-2 1v-1l5-3-1-1z" class="C"></path><path d="M438 179s0-1 1-1l1 1-1 1 1 1v2l-2 2-5 3-2 1-3 1c-1 1-2 2-4 2h-1c-1 0 0-1-1-1 0-1 0-1-1-1l1-3c1 0 2 0 3-1h0l1 1h0l6-6c1-1 0-1 1-1s2 0 2-1h0 1s0-1 1-1h0l1 1z" class="B"></path><path d="M438 179s0-1 1-1l1 1-1 1-1 2-1-1s0-1 1-2z" class="I"></path><path d="M439 180l1 1v2l-2 2h-1c0-1 0-2 1-2v-1h0l1-2z" class="Y"></path><defs><linearGradient id="BC" x1="431.976" y1="185.99" x2="426.478" y2="183.818" xlink:href="#B"><stop offset="0" stop-color="#616062"></stop><stop offset="1" stop-color="#797878"></stop></linearGradient></defs><path fill="url(#BC)" d="M432 181c1-1 0-1 1-1s2 0 2-1c0 2-1 4-2 5-2 2-7 4-7 6h1 1c-1 1-2 2-4 2h-1c-1 0 0-1-1-1 0-1 0-1-1-1l1-3c1 0 2 0 3-1h0l1 1h0l6-6z"></path><path d="M422 187c1 0 2 0 3-1h0l1 1c-2 1-2 2-3 4 0 1 0 1 1 1h-1c-1 0 0-1-1-1 0-1 0-1-1-1l1-3z" class="X"></path><path d="M450 155h2v1h1l1 1-5 3-7 4c-1 1-3 2-4 3-2 1-4 3-5 4 0 1-1 2-1 2 0 2-1 3-2 5h0l-2 1-2-1 1-2h-1c-1-1-1-2-1-3l3-3c1-1 2-1 2-3l3-2c2-2 4-3 6-4l3-3h0 0c1-1 2-1 3-1l5-2z" class="M"></path><path d="M428 170c2-1 4-2 6-4-1 2-3 4-4 6l-1 1c-1 1-1 2-2 3h0-1c-1-1-1-2-1-3l3-3z" class="h"></path><path d="M439 161h2l-7 5c-2 2-4 3-6 4 1-1 2-1 2-3l3-2c2-2 4-3 6-4z" class="d"></path><path d="M450 155h2v1c-4 2-7 3-11 5h-2l3-3h0 0c1-1 2-1 3-1l5-2z" class="P"></path><path d="M430 172c1-1 1-1 3-1 0 1-1 2-1 2 0 2-1 3-2 5h0l-2 1-2-1 1-2h0c1-1 1-2 2-3l1-1z" class="R"></path><path d="M427 176c1-1 1-2 2-3l1 2c-1 0-1 1-2 1h-1z" class="T"></path><path d="M430 172c1-1 1-1 3-1 0 1-1 2-1 2-1 1-2 1-2 2l-1-2 1-1z" class="D"></path><path d="M454 169c1-1 2-1 3-2s3-1 4-2l2 2h1c-1 2-1 3-2 4s-1 1-2 1l-1 2c-1 1-2 1-3 1l1 1-1 1h0l-1 1c-1 0-2 0-3 2-1 0-3 0-5 1l4 2h0l1 1h-1l-1 1h0c-3 1-5 1-7 3-3 1-5 1-8 3v-1h0c-1 0-2 0-3-1h-1l2-1 5-3 2-2v-2l-1-1 1-1c1-1 2-3 3-4l1-1v-3c1-1 3-2 5-3l1 1c1 0 2-1 3-1s0 0 1 1z" class="k"></path><path d="M449 168l1 1c1 0 2-1 3-1s0 0 1 1c-4 1-7 2-10 5v-3c1-1 3-2 5-3z" class="P"></path><defs><linearGradient id="BD" x1="453.471" y1="171.216" x2="454.748" y2="175.385" xlink:href="#B"><stop offset="0" stop-color="#727171"></stop><stop offset="1" stop-color="#8b898a"></stop></linearGradient></defs><path fill="url(#BD)" d="M463 167h1c-1 2-1 3-2 4s-1 1-2 1l-1 2c-1 1-2 1-3 1-3 2-6 2-10 3-1 0-2 1-4 1l1-1 1-1c2-1 5-2 7-3 2 0 4-1 5-2 3-1 5-3 7-5z"></path><path d="M443 175c0 1 0 2 1 2l-1 1-1 1c2 0 3-1 4-1 4-1 7-1 10-3l1 1-1 1h0l-1 1c-1 0-2 0-3 2-1 0-3 0-5 1h-2l-1 1c-1-1-1 0-2 1l-1 1c1 1 1 1 1 2h-1c-1-1-1-2-1-3v-2l-1-1 1-1c1-1 2-3 3-4z" class="G"></path><path d="M444 182l1-1h2l4 2h0l1 1h-1l-1 1h0c-3 1-5 1-7 3-3 1-5 1-8 3v-1h0c-1 0-2 0-3-1h-1l2-1 5-3 2-2c0 1 0 2 1 3h1c0-1 0-1-1-2l1-1c1-1 1-2 2-1z" class="d"></path><path d="M442 186c0-1 0-1-1-2l1-1c1-1 1-2 2-1 1 0 2 1 3 2-1 1-3 2-4 2h0-1z" class="C"></path><path d="M440 183c0 1 0 2 1 3h1 1c-1 1-2 1-2 1-3 0-4 2-8 1h0l5-3 2-2z" class="h"></path><defs><linearGradient id="BE" x1="245.49" y1="89.354" x2="264.818" y2="54.168" xlink:href="#B"><stop offset="0" stop-color="#b9b7b8"></stop><stop offset="1" stop-color="#f9f9f9"></stop></linearGradient></defs><path fill="url(#BE)" d="M225 36v-3h0l1-1 8 16c7 13 18 25 30 34 5 4 12 6 16 9l1 1c0 1 1 1 2 2 1 2 3 3 3 5h-1c2 2 4 5 6 7 1 2 1 4 2 5 2 0 3 1 4 3h1v-1l2 3 2 4h1l1 1 1-1c2 2 3 5 5 7 1 2 3 4 4 6s1 4 2 6c0 4 0 9-1 13-1 1-1 3-2 4s-1 3-1 4l2 2 7 11c1 3 3 6 6 8h0 0l1 1h4l-6 6c-1-1-1-2-1-4 0 0-1-1-1-2l-3-4c-1-2-2-3-4-4-1 0-1-1-2-2v3l1 1h-1l-3-3-4-7-1-2-1-1h-2-1-2c-1 0-2-1-4-1h-1-1-1c0 1-1 1-1 1h0c-1 0-1 0-2-1v-4h0v-1c0-1 0-2-1-2l-2-2v1c-1-1-2-3-3-4h0 0c1 1 1 2 2 3h-1c-1-1-2-3-3-4h0c-1-2-2-3-3-4v-1h-2-1s-2-1-3-1-2 1-2 1h0v-1h0c0-1 3-2 3-2 2 0 4-1 6-1-1-2-2-4-2-6v-1-1h-1c-1-1-1-2-1-2v-1-2l1-1h0 1c0-2-2-3-2-5l1-1-1-1c2 0 3 0 4-1-2-2-5-4-8-7l-2-1-2-2c-2-1-3-2-5-4-3-3-6-6-9-10-2-1-3-3-4-5-2-3-5-7-7-11-1-3-2-6-3-8-1-1-3-3-4-3h0-1v1h-1c-2-4-5-10-6-15l-3-6 1-1v-2l-2-8z"></path><path d="M227 44c1 1 3 5 3 6h0c-1-1-1-1-1-2v4 1l-3-6 1-1v-2z" class="P"></path><path d="M289 107c0-2-2-3-3-5a30.44 30.44 0 0 0-8-8l1-1 1 1c2 1 4 3 5 5 2 2 4 5 6 7 1 2 1 4 2 5s3 3 4 5c2 4 2 9 3 13h0c-1-1-1-2-1-3-1-2-2-4-2-7-2-4-5-8-8-12z" class="H"></path><path d="M284 104c-1-1-7-8-7-9 3 2 6 5 9 9 0 1 1 2 2 3v1c1 1 4 6 4 7s0 2-1 3l-2-4-3-3v-2l-3-3 1-2z" class="f"></path><path d="M284 104c1 2 2 3 2 5l-3-3 1-2z" class="U"></path><path d="M265 99h0c1 1 2 2 3 2h4v-1c2 2 4 2 6 3 3 2 3 4 6 4l-3-3 2 2 3 3v2l3 3-1 1h-1c-1-2-3-4-4-5h0c-1 0-2-1-3-1v1c-2-1-4-2-5-4l-4-2c-2-2-5-3-6-5z" class="K"></path><path d="M275 106c1 1 1 1 2 1h0c2-1 3 0 4 0 2 1 3 3 5 4l3 3-1 1h-1c-1-2-3-4-4-5h0c-1 0-2-1-3-1v1c-2-1-4-2-5-4z" class="o"></path><path d="M289 107c3 4 6 8 8 12 0 3 1 5 2 7 0 1 0 2 1 3h0c1 1 1 3 1 4 0 3 0 5 1 8 1 4 3 9 4 13 0 2 1 4 1 5-2-2-3-5-4-8l-5-16c1-1 0-3 0-4-1 0-2-1-2-1l-1-1h0l-1 1-1-2h0v-3c0-2 0-3-1-4l-1-3c1-1 1-2 1-3s-3-6-4-7l1-1z" class="U"></path><path d="M296 123l2 8c-1 0-2-1-2-1l-1-1h0l-1 1-1-2h0v-3c1 0 1 0 1 1h0 1c0-1 0-1 1-2v-1z" class="F"></path><path d="M291 118c1-1 1-2 1-3 1 2 4 6 4 8v1c-1 1-1 1-1 2h-1 0c0-1 0-1-1-1 0-2 0-3-1-4l-1-3z" class="H"></path><defs><linearGradient id="BF" x1="265.434" y1="108.184" x2="266.53" y2="96.638" xlink:href="#B"><stop offset="0" stop-color="#7f7e80"></stop><stop offset="1" stop-color="#a9a8a7"></stop></linearGradient></defs><path fill="url(#BF)" d="M255 94c1 0 1 1 2 2h0c2 0 3 1 4 0l-2-1v-1c2 1 3 3 5 4 0 0 0 1 1 1h0c1 2 4 3 6 5l4 2c1 2 3 3 5 4v-1c1 0 2 1 3 1h0c1 1 3 3 4 5h1l1-1 2 4 1 3-1 1-1-1v1c-1 0 0 0-1-1l-1 1c-1-4-5-9-8-11-2-1-3-1-5-1 0 0-1 0-1-1l-1 2h0l-2-1-2-2c-2-1-3-2-5-4-3-3-6-6-9-10z"></path><path d="M269 108l2-1c1 1 2 1 3 2h0l-1 2h0l-2-1-2-2z" class="c"></path><path d="M280 110v-1c1 0 2 1 3 1h0c1 1 3 3 4 5h1l1-1 2 4 1 3-1 1-1-1v1c-1 0 0 0-1-1l-1-2c-1-4-5-7-8-9z" class="H"></path><path d="M288 115l1-1 2 4 1 3-1 1-1-1c-1-2-1-4-2-6z" class="B"></path><defs><linearGradient id="BG" x1="290.69" y1="140.818" x2="310.454" y2="152.891" xlink:href="#B"><stop offset="0" stop-color="#636363"></stop><stop offset="1" stop-color="#888687"></stop></linearGradient></defs><path fill="url(#BG)" d="M294 130l1-1h0l1 1s1 1 2 1c0 1 1 3 0 4l5 16c1 3 2 6 4 8v1c0 1 1 2 1 3v1 2l-1-2-1-1h-2-1-2c-1 0-2-1-4-1h-1-1-1c0 1-1 1-1 1h0c-1 0-1 0-2-1v-4h0v-1c0-1 0-2-1-2 1 0 1-1 2-1h0c-1-2-1-2-1-4l-1-2h0v-3l1-1c1-2 1-4 1-6h2v-4-4z"></path><path d="M294 130l1-1h0l1 1v8h0c-1-1-1-3-1-4h-1v-4z" class="R"></path><path d="M293 146l1-1c1 1 1 2 3 3v1l-2 2h0s0 1-1 1c0-2-1-4-1-6z" class="I"></path><path d="M297 158c1-1 1-2 2-3 1 3 2 6 5 8h-1-2c-1-2-3-3-4-5z" class="d"></path><path d="M294 152c1 0 1-1 1-1h0l2-2 2 6c-1 1-1 2-2 3l-3-6z" class="H"></path><defs><linearGradient id="BH" x1="301.269" y1="156.497" x2="308.885" y2="161.12" xlink:href="#B"><stop offset="0" stop-color="#505152"></stop><stop offset="1" stop-color="#6d6b6a"></stop></linearGradient></defs><path fill="url(#BH)" d="M302 154h1v-1c-1-1-1-1 0-2h0c1 3 2 6 4 8v1c0 1 1 2 1 3v1 2l-1-2-1-1c-1-3-3-6-4-9z"></path><path d="M298 135l5 16h0c-1 1-1 1 0 2v1h-1c-1-3-3-5-4-8v-7-2-2z" class="V"></path><path d="M294 134h1c0 1 0 3 1 4 0 1 0 3-1 3 0 2-3 3-2 5 0 2 1 4 1 6l3 6c1 2 3 3 4 5-1 0-2-1-4-1h-1-1-1c0 1-1 1-1 1h0c-1 0-1 0-2-1v-4h0v-1c0-1 0-2-1-2 1 0 1-1 2-1h0c-1-2-1-2-1-4l-1-2h0v-3l1-1c1-2 1-4 1-6h2v-4z" class="N"></path><path d="M292 138h2v1c-1 1-1 3-2 4 0 1-1 2-1 3l1 1c0 2 1 4 2 6h0-1v2l-1-1h0c-1-2-1-2-1-4l-1-2h0v-3l1-1c1-2 1-4 1-6z" class="I"></path><path d="M293 155v-2h1l2 6s1 2 1 3h-1-1-1c0 1-1 1-1 1h0c-1 0-1 0-2-1v-4h0v-1c0-1 0-2-1-2 1 0 1-1 2-1l1 1z" class="L"></path><path d="M292 154l1 1v2l-1 1c0-1 0-1-1-1 0-1 0-2-1-2 1 0 1-1 2-1z" class="b"></path><path d="M294 162h-1v-3h1l2 3h-1-1z" class="i"></path><defs><linearGradient id="BI" x1="273.567" y1="147.301" x2="287.469" y2="116.54" xlink:href="#B"><stop offset="0" stop-color="#121212"></stop><stop offset="1" stop-color="#4e4d4c"></stop></linearGradient></defs><path fill="url(#BI)" d="M273 111h0l1-2c0 1 1 1 1 1 2 0 3 0 5 1 3 2 7 7 8 11l1-1c1 1 0 1 1 1v-1l1 1 1-1c1 1 1 2 1 4v3h0l1 2v4 4h-2c0 2 0 4-1 6l-1 1v3h0l1 2c0 2 0 2 1 4h0c-1 0-1 1-2 1l-2-2v1c-1-1-2-3-3-4h0 0c1 1 1 2 2 3h-1c-1-1-2-3-3-4h0c-1-2-2-3-3-4v-1h-2-1s-2-1-3-1-2 1-2 1h0v-1h0c0-1 3-2 3-2 2 0 4-1 6-1-1-2-2-4-2-6v-1-1h-1c-1-1-1-2-1-2v-1-2l1-1h0 1c0-2-2-3-2-5l1-1-1-1c2 0 3 0 4-1-2-2-5-4-8-7z"></path><path d="M284 147c-1-1-1-4-2-5 2 2 3 3 5 4 1 1 1 2 1 3v1c-1-1-2-3-3-4l-1 1z" class="B"></path><path d="M284 147l1-1c1 1 2 3 3 4v1c1 1 1 2 2 3v1l-2-2v1c-1-1-2-3-3-4l-1-3z" class="c"></path><path d="M288 149c1 0 1 0 2-1l1 2c0 2 0 2 1 4h0c-1 0-1 1-2 1v-1c-1-1-1-2-2-3v-1-1z" class="B"></path><path d="M288 149c1 0 1 0 2-1l1 2-3 1v-1-1z" class="C"></path><path d="M286 124l3 9c0 2-1 5 1 7 0 1 0 3-1 4l1 1v3h0c-1 1-1 1-2 1 0-1 0-2-1-3v-1c0-1-1-1-1-2-1-1 0-6 1-9 0-2 0-5-1-7v-3z" class="V"></path><path d="M287 145c2 1 2 2 3 3h0c-1 1-1 1-2 1 0-1 0-2-1-3v-1z" class="Z"></path><path d="M289 133c0 2-1 5 1 7 0 1 0 3-1 4h-1c0-1 0-4 1-5v-6z" class="G"></path><defs><linearGradient id="BJ" x1="281.402" y1="126.913" x2="277.219" y2="121.035" xlink:href="#B"><stop offset="0" stop-color="#5c5b5b"></stop><stop offset="1" stop-color="#767677"></stop></linearGradient></defs><path fill="url(#BJ)" d="M281 118c1 1 1 2 2 3 0 1 1 3 1 4l1 3v7 3h-1c-1-2 1-5 0-8l-1 1c0 3-1 6-2 8v1c-1-2-2-4-2-6v-1-1h-1c-1-1-1-2-1-2v-1-2l1-1h0 1c0-2-2-3-2-5l1-1-1-1c2 0 3 0 4-1z"></path><path d="M279 129l1-1v1h0c1 1 0 3 0 5 0-1 0-2-1-3v-2z" class="B"></path><path d="M278 126c0 1 1 2 1 3v2 1h-1c-1-1-1-2-1-2v-1-2l1-1z" class="E"></path><path d="M281 118c1 1 1 2 2 3 0 1-1 3-2 3 0-2-1-3-3-4l-1-1c2 0 3 0 4-1z" class="a"></path><defs><linearGradient id="BK" x1="283.301" y1="136.708" x2="278.761" y2="130.238" xlink:href="#B"><stop offset="0" stop-color="#373739"></stop><stop offset="1" stop-color="#4f504e"></stop></linearGradient></defs><path fill="url(#BK)" d="M279 134l1 1c2-2 0-4 1-6h1v2h1c0 3-1 6-2 8v1c-1-2-2-4-2-6z"></path><path d="M283 121c0 1 1 3 1 4l1 3v3l-1 1v-3l-1 1h-1v-3c0-1 0-2-1-3 1 0 2-2 2-3z" class="G"></path><path d="M283 121c0 1 1 3 1 4l-1 2h-1c0-1 0-2-1-3 1 0 2-2 2-3z" class="I"></path><defs><linearGradient id="BL" x1="281.099" y1="124.517" x2="287.148" y2="119.092" xlink:href="#B"><stop offset="0" stop-color="#828181"></stop><stop offset="1" stop-color="#abaaab"></stop></linearGradient></defs><path fill="url(#BL)" d="M275 110c2 0 3 0 5 1 3 2 7 7 8 11l1-1c1 1 0 1 1 1v-1l1 1 1-1c1 1 1 2 1 4v3h0l1 2v4 4h-2c0 2 0 4-1 6l-1 1-1-1c1-1 1-3 1-4-2-2-1-5-1-7l-3-9v-1c-1-5-6-10-11-13z"></path><path d="M289 126l1-1c1 1 1 2 1 3 1 3 1 6 1 9v1c0 2 0 4-1 6l-1 1-1-1c1-1 1-3 1-4v-2c0-4 0-8-1-12z" class="R"></path><path d="M290 138c0 2 0 4 1 6h0l-1 1-1-1c1-1 1-3 1-4v-2z" class="N"></path><path d="M290 121l1 1 1-1c1 1 1 2 1 4v3h0l1 2v4 4h-2v-1c0-3 0-6-1-9 0-1 0-2-1-3l-1 1h0l-1-4 1-1c1 1 0 1 1 1v-1z" class="I"></path><path d="M291 122l1-1c1 1 1 2 1 4v3c-1-2-2-4-2-6z" class="N"></path><path d="M293 128l1 2v4 4h-2v-1c1-3 1-6 1-9z" class="K"></path><defs><linearGradient id="BM" x1="317.246" y1="145.087" x2="303.56" y2="154.167" xlink:href="#B"><stop offset="0" stop-color="#cccbcc"></stop><stop offset="1" stop-color="#fcfafb"></stop></linearGradient></defs><path fill="url(#BM)" d="M293 111c2 0 3 1 4 3h1v-1l2 3 2 4h1l1 1 1-1c2 2 3 5 5 7 1 2 3 4 4 6s1 4 2 6c0 4 0 9-1 13-1 1-1 3-2 4s-1 3-1 4l2 2 7 11c1 3 3 6 6 8h0 0l1 1h4l-6 6c-1-1-1-2-1-4 0 0-1-1-1-2l-3-4c-1-2-2-3-4-4-1 0-1-1-2-2v3l1 1h-1l-3-3-4-7v-2-1c0-1-1-2-1-3v-1c0-1-1-3-1-5-1-4-3-9-4-13-1-3-1-5-1-8 0-1 0-3-1-4-1-4-1-9-3-13-1-2-3-4-4-5z"></path><path d="M307 160c3 3 5 8 8 12v3l1 1h-1l-3-3-4-7v-2-1c0-1-1-2-1-3z" class="I"></path><path d="M298 113l2 3 2 4c1 2 1 3 1 5l1 2 2 14c0 1 1 2 0 3h-1c-1-3-1-5-2-8-1-6 0-11-2-16l-3-6v-1z" class="H"></path><path d="M302 120h1l1 1 1-1c2 2 3 5 5 7 1 2 3 4 4 6s1 4 2 6c0 4 0 9-1 13-1 1-1 3-2 4s-1 3-1 4c-2-4-4-7-5-11l-2-5h1c1-1 0-2 0-3l-2-14-1-2c0-2 0-3-1-5z" class="p"></path><path d="M302 120h1l1 1 4 7-1 2h-1 1c0 1-1 2-1 2 0-1 0-4-1-6l-1-1v2l-1-2c0-2 0-3-1-5z" class="d"></path><path d="M310 132c1 2 2 5 3 8 0 1 0 4 1 4-1 4-2 7-2 10h-1v-1c-1-1-1 0-1-1v-1h2c0-3 0-4-1-7h1c0-3-1-7-2-10-1-1-1-1 0-2z" class="X"></path><path d="M314 144v8h1c-1 1-1 3-2 4s-1 3-1 4c-2-4-4-7-5-11 0 0 1 1 2 1 0 1 1 1 1 2s0 0 1 1v1h1c0-3 1-6 2-10z" class="W"></path><path d="M308 128c1 1 1 2 2 4-1 1-1 1 0 2 1 3 2 7 2 10h-1c-1 0-2-1-3-1-1-1-1-5-1-6 0-2 0-3-1-5 0 0 1-1 1-2h-1 1l1-2z" class="P"></path><path d="M306 132s1-1 1-2h-1 1l2 7h-2c0-2 0-3-1-5z" class="H"></path><path d="M307 137h2l2 7c-1 0-2-1-3-1-1-1-1-5-1-6z" class="F"></path><path d="M304 127v-2l1 1c1 2 1 5 1 6 1 2 1 3 1 5 0 1 0 5 1 6 1 0 2 1 3 1 1 3 1 4 1 7h-2v1c0-1-1-1-1-2-1 0-2-1-2-1l-2-5h1c1-1 0-2 0-3l-2-14z" class="P"></path><path d="M308 143c1 0 2 1 3 1 1 3 1 4 1 7h-2l-2-8z" class="c"></path><defs><linearGradient id="BN" x1="216.739" y1="408.732" x2="253.005" y2="496.974" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#414141"></stop></linearGradient></defs><path fill="url(#BN)" d="M246 401v-3h1c0 1 1 2 1 3 1 3 2 7 2 11 1 3 1 10 0 13-1 2-3 3-5 4-1 2-3 3-5 3v1l2 1c2 1 2 0 3 1l-3 3h0c-1 1-1 1-2 1l-2-1c0 1 1 2 0 3h0 0l3 3-1 3-1 1h1l-2 5h0c-1 1-2 4-2 7 0 5 1 10 4 14 2 3 6 6 6 10h1v13c0 9 2 19 4 28 0 1 0 1 1 2-1-1-1-1-2-1l-1-1s-1 0-1-1v-1c-1-1-2-2-3-4 0 2 1 4 2 5h-1c-2 1-6 0-8-1 0 1-1 1-1 1l1 1-1 1c-4-1-6-4-9-6-4-5-7-11-9-17h0l1-1v-1c-2-5-3-9-3-14h-2v-5c-1-4-1-9-1-14 0 0 1 0 1-1h0-5c-2-1-3-3-4-5-1 1-1 1-1 2v3h0-1c1-1 0-2 0-3 0-2 0-6-1-8l-1 1h-1c-1-2 0-5 0-6v-1l-1-1 1-1c2-1 2-2 2-3l1-2c0-2 1-4 2-6 0-2 1-3 2-5l2-2c1-2 3-4 5-5 1 0 2-1 2-1h2l-2 2v1h0s1 0 1-1l2-1c1-1 2-1 4-1 0-1 2-2 3-2 5-4 12-8 16-13h0c2-2 2-5 3-8z"></path><path d="M215 482h1v-1 1h0l1 5h-2v-5z" class="I"></path><path d="M238 441c0-1 0-1-1-1-2-3-1-5-1-8v4h1c1 0 1 1 1 2s1 2 0 3z" class="V"></path><path d="M229 426l1 1c-2 2-3 4-4 6l-1-1-2 1c2-3 4-5 6-7z" class="B"></path><path d="M229 426c2-2 4-4 7-6v1h1c-2 1-3 2-4 3l-3 3-1-1z" class="J"></path><path d="M242 427c1-1 3-3 4-5 2-4 0-10 1-14 0 2 1 3 1 5 0 3 0 6-1 9h1c0 1-2 4-4 5h-2z" class="N"></path><path d="M250 412c1 3 1 10 0 13-1 2-3 3-5 4h0l1-1c1-2 3-4 3-6h-1-1c1-3 1-6 1-9 1 2 1 4 1 6h0v-2c1 0 0-1 0-2 1-1 1-2 1-3z" class="E"></path><path d="M215 467l1-1v16h0v-1 1h-1c-1-4-1-9-1-14 0 0 1 0 1-1h0z" class="h"></path><path d="M248 422h1c0 2-2 4-3 6l-1 1h0c-1 2-3 3-5 3v1l2 1c2 1 2 0 3 1l-3 3h0c-1 1-1 1-2 1l-2-1c0-1 0-2-1-2h-1v-4c0-2 1-3 2-4h3l1-1h2c2-1 4-4 4-5z" class="G"></path><path d="M242 438c-1 0-3-1-3-2-1-1-1-3 0-4 0 0 1 0 1 1l2 1c2 1 2 0 3 1l-3 3h0z" class="j"></path><defs><linearGradient id="BO" x1="222.685" y1="446.559" x2="217.316" y2="445.678" xlink:href="#B"><stop offset="0" stop-color="#636365"></stop><stop offset="1" stop-color="#7c7b7c"></stop></linearGradient></defs><path fill="url(#BO)" d="M223 433l2-1 1 1c-5 9-8 19-10 28v5l-1-2v-3c-1-1 0-3 0-5 1-8 3-16 8-23z"></path><path d="M217 426v1h0s1 0 1-1l2-1c1-1 2-1 4-1-3 1-5 2-7 4-6 5-10 11-11 19-1 5 0 10 3 14 1 2 3 3 5 3h1l1 2h0l-1 1h-5c-2-1-3-3-4-5-1 1-1 1-1 2v3h0-1c1-1 0-2 0-3 0-2 0-6-1-8l-1 1h-1c-1-2 0-5 0-6v-1l-1-1 1-1c2-1 2-2 2-3l1-2c0-2 1-4 2-6 0-2 1-3 2-5l2-2c1-2 3-4 5-5 1 0 2-1 2-1h2l-2 2z" class="L"></path><path d="M201 448c2-1 2-2 2-3l1 10h-1c-2-1-1-5-2-7z" class="J"></path><path d="M201 448c1 2 0 6 2 7h1l2 7c-1 1-1 1-1 2v3h0-1c1-1 0-2 0-3 0-2 0-6-1-8l-1 1h-1c-1-2 0-5 0-6v-1l-1-1 1-1z" class="D"></path><path d="M208 432l2-2c1-2 3-4 5-5 1 0 2-1 2-1h2l-2 2c-2 2-4 3-6 6-2 1-3 3-5 5 0-2 1-3 2-5z" class="R"></path><path d="M236 446h1v2h1l1 1v-1h1l-2 5h0c-1 1-2 4-2 7 0 5 1 10 4 14 2 3 6 6 6 10h1v13c0 9 2 19 4 28 0 1 0 1 1 2-1-1-1-1-2-1l-1-1s-1 0-1-1v-1c-1-1-2-2-3-4 0 2 1 4 2 5h-1c-2 1-6 0-8-1 0 1-1 1-1 1l1 1-1 1c-4-1-6-4-9-6-4-5-7-11-9-17h0l1-1v-1h0c1 3 2 5 4 8 1 0 2-1 2-1v-2c-1-1-1 0-1-1h0v-1c-1-1-1-2-1-3s-1-1-1-2v-1c1-1 0-2 1-3v-2c-1-1 0-2 0-3-1-1-1-2 0-3v-5c1-4 1-7 2-11 1-7 4-14 7-20 1-2 2-4 3-5z" class="J"></path><path d="M240 484c0-1 0-3 1-4 1 3 1 4 1 8v-3c-1-1-1-1-2-1z" class="V"></path><path d="M242 488l1 1v1c0 2-1 4-2 6v-1-1h1v-2l-1 2h-1c1-2 2-4 2-6z" class="j"></path><path d="M241 509c0 1 1 1 1 2s1 2 2 4c0 1 0 1 1 2l1 2h0v-3l-1-1h0v-2c2 3 3 5 3 7 0 1 0 1 1 1 0 2 1 3 1 5l-1-1s-1 0-1-1v-1c-1-1-2-2-3-4l-4-10zm2-19l1-2h0c1 2 1 9 1 11-1 1-2 3-3 3h-1c-1-1-1-1-1-2l1-4c1-2 2-4 2-6z" class="C"></path><defs><linearGradient id="BP" x1="241.965" y1="485.66" x2="237.568" y2="492.446" xlink:href="#B"><stop offset="0" stop-color="#515051"></stop><stop offset="1" stop-color="#6f6f6f"></stop></linearGradient></defs><path fill="url(#BP)" d="M240 484c1 0 1 0 2 1v3h0c0 2-1 4-2 6s-2 4-4 5l-2 2h-5v1h-1l-1-1v-1h5c2-1 3-3 4-5 3-3 3-7 4-11z"></path><path d="M236 446v1c0 3-1 5-1 7-2 5-2 10-3 15 0 6 0 12 1 18 0 2 0 5 1 7v1h2c-1 2-2 4-4 5h-5v1l-2-1h0l-2-2c1-1 0-2 1-3v-2c-1-1 0-2 0-3-1-1-1-2 0-3v-5c1-4 1-7 2-11 1-7 4-14 7-20 1-2 2-4 3-5z" class="n"></path><path d="M223 498c1-1 0-2 1-3v-2c-1-1 0-2 0-3-1-1-1-2 0-3 0 2 0 6 2 8 1 1 2 2 3 2 2 0 3-1 5-2v-1 1h2c-1 2-2 4-4 5h-5v1l-2-1h0l-2-2z" class="E"></path><path d="M223 498l2 2h0l2 1 1 1h1v-1h5l2-2c1 1 1 2 2 3h0l3 7 4 10c0 2 1 4 2 5h-1c-2 1-6 0-8-1 0 1-1 1-1 1l1 1-1 1c-4-1-6-4-9-6-4-5-7-11-9-17h0l1-1v-1h0c1 3 2 5 4 8 1 0 2-1 2-1v-2c-1-1-1 0-1-1h0v-1c-1-1-1-2-1-3s-1-1-1-2v-1z" class="n"></path><path d="M234 520c1 1 3 2 4 3 0 1-1 1-1 1l-4-1 1-3z" class="F"></path><path d="M236 499c1 1 1 2 2 3h0c-4 1-6 1-10 0h1v-1h5l2-2z" class="C"></path><path d="M236 499c1 1 1 2 2 3-2 0-3 0-4-1l2-2z" class="M"></path><defs><linearGradient id="BQ" x1="224.298" y1="508.932" x2="233.299" y2="520.709" xlink:href="#B"><stop offset="0" stop-color="#565656"></stop><stop offset="1" stop-color="#757475"></stop></linearGradient></defs><path fill="url(#BQ)" d="M223 498l2 2h0c0 2 1 4 1 6 2 5 5 9 8 14l-1 3 4 1 1 1-1 1c-4-1-6-4-9-6-4-5-7-11-9-17h0l1-1v-1h0c1 3 2 5 4 8 1 0 2-1 2-1v-2c-1-1-1 0-1-1h0v-1c-1-1-1-2-1-3s-1-1-1-2v-1z"></path><path d="M219 503h0l1-1 3 8c3 5 5 10 10 13l4 1 1 1-1 1c-4-1-6-4-9-6-4-5-7-11-9-17z" class="m"></path><defs><linearGradient id="BR" x1="471.772" y1="403.027" x2="505.162" y2="513.637" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#242424"></stop></linearGradient></defs><path fill="url(#BR)" d="M475 392l1 1c0 2-1 6-1 8 0 0 1-1 1-2 1 3 2 9 4 11v-1l1 1h1c1 1 3 2 5 4l2 1 1 1v-1l4 2c3 1 5 2 8 3h-3l-1 1c3 1 6 3 8 6h-2c3 3 6 5 9 8-1-2-3-3-4-5h0l4 4c1 0 1 0 2 1h1l1 1 3 6v7c0 3-1 5-1 7h1v1c0 2 0 4-1 7h0c0 2-2 5-3 7h0c2-2 3-3 4-6 0 4-1 8-1 11-2 16-7 32-18 44-2 2-4 4-6 5v-1h-3l1-1c0-1 0-1-1-2-1 0-2 0-3-1-1 2-3 4-6 4-1 1-3 0-5 1v-1l-1 1c-1 1-1 2-1 2h0l-2 1c0 1 0 1-1 1v1h-1l1-3c0-1 1-1 1-2h1v-4h0l1-2v-2l1-5c0 1-1 1-1 2h0v-5-6l1-10v-7l1-2v-1c0 1-1 1-1 2v-5h0c0-3 4-8 6-11 0-1 1-3 1-4h-1v-1c2-2 2-4 2-7 0-5 0-9-3-14v-1h0c0-1-1-1-2-1l3-1c0-1 0-1-1-1v-1c-2 0-3-1-4-3h0c-1-1-1-2-1-3v-1l-4-4h0l-1-1c-1-2-1-4-2-6v-9c0-2 1-3 0-5h0l4-10c1 0 0 0 1-1v-2-1z"></path><path d="M486 415c0-1 0-1 1-1l2 1c-1 0-1 1-1 1l-2-1z" class="X"></path><path d="M473 411c1-2 1-5 2-7v2c0 1-1 2 0 4 0-2 0-2 1-3h0v8l-1-3v-1c0 1-1 1-1 1l-1-1z" class="e"></path><path d="M480 410v-1l1 1h1c1 1 3 2 5 4-1 0-1 0-1 1-2-1-4-3-6-5z" class="a"></path><path d="M483 440c2-1 3-2 5-5 0 2-1 4-2 5s-2 2-4 2c0-1-1-1-2-1l3-1z" class="N"></path><path d="M488 416s0-1 1-1l1 1 5 3-1 2-6-5z" class="K"></path><path d="M497 421h1c3 1 6 3 8 6h-2l-8-5 1-1z" class="o"></path><path d="M485 431v-2l2 2 1 4c-2 3-3 4-5 5 0-1 0-1-1-1 1-1 2-2 3-2 1-2 0-4 0-6z" class="h"></path><path d="M490 416v-1l4 2 8 3h-3l-1 1h-1l-1 1-2-1 1-2-5-3z" class="G"></path><path d="M495 419l2 2-1 1-2-1 1-2z" class="X"></path><path d="M485 431h0c0 2 1 4 0 6-1 0-2 1-3 2v-1c-2 0-3-1-4-3h1c1 0 2 0 3-1l1-1c1 0 1-1 1-1l1-1z" class="g"></path><defs><linearGradient id="BS" x1="481.262" y1="484.358" x2="484.999" y2="491.626" xlink:href="#B"><stop offset="0" stop-color="#363637"></stop><stop offset="1" stop-color="#545454"></stop></linearGradient></defs><path fill="url(#BS)" d="M487 500h0c-1-1-1-2-2-3 0-2-2-3-2-5-2-4-1-7 0-11 1 3 1 6 2 9 0 1 1 3 1 4v5l1 1h0z"></path><path d="M476 519h0v4-1c1-1 1-2 3-2v-1l2-6h1l1-3c0-1 1-2 1-2l-6 16-1 1c-1 1-1 2-1 2h0l-2 1c0 1 0 1-1 1v1h-1l1-3c0-1 1-1 1-2h1v-4h0l1-2z" class="C"></path><defs><linearGradient id="BT" x1="505.533" y1="442.968" x2="517.632" y2="450.032" xlink:href="#B"><stop offset="0" stop-color="#191816"></stop><stop offset="1" stop-color="#343435"></stop></linearGradient></defs><path fill="url(#BT)" d="M507 435c2 1 3 3 4 5 4 5 5 11 4 17l-1 1c-1-1 0-2 0-3 0-3-1-7-2-11-2-3-4-6-5-9z"></path><path d="M473 416v-5l1 1s1 0 1-1v1l1 3c0 5 0 7 4 11 2 2 6 1 7 5l-2-2v2h0c-1-2-6-4-8-6-1-1-2-3-3-6l-1 1v-4z" class="G"></path><path d="M473 416v-5l1 1s1 0 1-1v1c-1 2-1 6-1 7l-1 1v-4z" class="k"></path><path d="M478 484c0-1 1-2 1-3s1-1 1-2v1c0 1-1 2-1 3s0 3 1 5c0 1 1 2 1 4 1 2 2 3 2 6h0v-1c-1-2-2-6-4-8 0 2 2 15 1 16-1 2-1 5-3 7 0 1-1 1-1 2h0v-5-6l1-10v-7l1-2z" class="J"></path><defs><linearGradient id="BU" x1="501.7" y1="447.629" x2="505.141" y2="446.363" xlink:href="#B"><stop offset="0" stop-color="#6e6d6e"></stop><stop offset="1" stop-color="#878687"></stop></linearGradient></defs><path fill="url(#BU)" d="M495 427l6 6c2 4 3 8 4 12 2 5 4 11 4 16 0 1 0 2-1 3v1l-1 1c0-3-1-6-1-9-1-5-3-10-5-15-1-5-3-9-6-13v-1-1z"></path><path d="M513 435c-1-2-3-3-4-5h0l4 4c1 0 1 0 2 1h1l1 1 3 6v7c0 3-1 5-1 7h1v1c0 2 0 4-1 7h0c0 2-2 5-3 7-1 0-1 0-1 1-2-2-4-3-5-5h0-1 0v-1l1 1c1-2 3-3 4-5v-1c1-2 3-5 4-8 1-7-2-13-5-18z" class="L"></path><path d="M519 456h1v1c0 2 0 4-1 7h0c0 2-2 5-3 7-1 0-1 0-1 1-2-2-4-3-5-5 2 0 3 0 4-1 3-2 4-6 5-10z" class="g"></path><defs><linearGradient id="BV" x1="470.236" y1="414.851" x2="483.919" y2="416.42" xlink:href="#B"><stop offset="0" stop-color="#737373"></stop><stop offset="1" stop-color="#989697"></stop></linearGradient></defs><path fill="url(#BV)" d="M475 392l1 1c0 2-1 6-1 8v3c-1 2-1 5-2 7v5 4l1-1c1 3 2 5 3 6 2 2 7 4 8 6l-1 1s0 1-1 1l-1 1c-1 1-2 1-3 1h-1 0c-1-1-1-2-1-3v-1l-4-4h0l-1-1c-1-2-1-4-2-6v-9c0-2 1-3 0-5h0l4-10c1 0 0 0 1-1v-2-1z"></path><path d="M477 432c2 1 3 1 5 2h0c-1 1-2 1-3 1h-1 0c-1-1-1-2-1-3z" class="G"></path><path d="M483 433v-2c-3-1-6-3-8-5-2-3-3-6-2-10v4l1-1c1 3 2 5 3 6 2 2 7 4 8 6l-1 1s0 1-1 1z" class="E"></path><path d="M499 500c1 3-3 10-4 13l1 1s1 0 1 1h0l-3 5c-1 1-1 1-2 1s-2 0-3-1c-1 2-3 4-6 4-1 1-3 0-5 1v-1l6-16 2-5h4c2 0 6-1 7-2l2-1z" class="n"></path><path d="M495 513l1 1s1 0 1 1h0l-3 5c-1 1-1 1-2 1s-2 0-3-1l2-2c1-1 2-3 4-5z" class="F"></path><path d="M491 518v1l3 1c-1 1-1 1-2 1s-2 0-3-1l2-2z" class="H"></path><path d="M490 495c1-11 3-21 1-32-1-6-2-11-3-16 6 9 8 18 11 29v6h1c1 0 2-1 2-2l1-2v4c-1 2 0 4 0 7l-2 8-1 2h-1v1l-2 1c-1 1-5 2-7 2h0v-1c-2 0-2-1-3-2h0l-1-1v-5l1 2h1c1 0 1-1 2-1z" class="n"></path><path d="M503 478v4c-1 2 0 4 0 7l-2 8-1 2h-1v1l-2 1c-1 1-5 2-7 2h0v-1c-2 0-2-1-3-2h0l-1-1v-5l1 2h1c1 0 1-1 2-1v1h3c3 1 4-1 6-2 1-4 1-8 0-12h1c1 0 2-1 2-2l1-2z" class="B"></path><path d="M499 499v-1c0-1 1-1 1-2v-1h1v2l-1 2h-1z" class="C"></path><path d="M486 494l1 2c1 1 2 3 4 4h5l1-1h1s0 1-1 1v1c-1 1-5 2-7 2h0v-1c-2 0-2-1-3-2h0l-1-1v-5z" class="i"></path><defs><linearGradient id="BW" x1="488.786" y1="412.786" x2="502.363" y2="488.923" xlink:href="#B"><stop offset="0" stop-color="#0f0e0e"></stop><stop offset="1" stop-color="#373738"></stop></linearGradient></defs><path fill="url(#BW)" d="M499 476h0c1 1 1 2 1 3v1c0-1 1-1 1-1v-2-3h0c1-3 0-7-1-10-1-5-4-10-6-16l-3-12c0-3-1-6-2-8-2-3-5-5-7-8-1-2-2-5-2-7h0l1 2h0c0-1 0-1 1 0 1 0 1 2 2 3 2 1 3 2 5 3-1-1-1-1 0-1 1 3 4 4 6 7v1 1c3 4 5 8 6 13 2 5 4 10 5 15 0 3 1 6 1 9 1 5 1 11 0 17 0 3-1 6-1 8h-1v-1c0-3 0-7-1-9-1-1 0-2-1-3l-1 2c0 1-1 2-2 2h-1v-6z"></path><path d="M502 480v-2c1-1 1-1 2-1 1 2 1 4 2 6v6-2-1-1c0-1 0-2 1-2 0 3-1 6-1 8h-1v-1c0-3 0-7-1-9-1-1 0-2-1-3l-1 2z" class="V"></path><defs><linearGradient id="BX" x1="525.348" y1="485.769" x2="485.07" y2="504.974" xlink:href="#B"><stop offset="0" stop-color="#1c1c1c"></stop><stop offset="1" stop-color="#555455"></stop></linearGradient></defs><path fill="url(#BX)" d="M514 461v1c-1 2-3 3-4 5l-1-1v1h0 1 0c1 2 3 3 5 5 0-1 0-1 1-1h0c2-2 3-3 4-6 0 4-1 8-1 11-2 16-7 32-18 44-2 2-4 4-6 5v-1h-3l1-1c0-1 0-1-1-2 1 0 1 0 2-1l3-5h0c0-1-1-1-1-1l-1-1c1-3 5-10 4-13v-1h1l1-2 2-8c0-3-1-5 0-7v-4c1 1 0 2 1 3 1 2 1 6 1 9v1h1c0-2 1-5 1-8 1-6 1-12 0-17l1-1c3-1 4-2 6-4z"></path><path d="M505 495v1 2 1l1-1v-1h0c-2 7-4 14-8 20-1 2-3 4-5 6 0-1 0-1-1-2 1 0 1 0 2-1l3-5c3-5 6-11 7-17l1-3z" class="d"></path><path d="M514 461v1c-1 2-3 3-4 5l-1-1v1h0c1 8 0 15-1 23 0 2 0 5-2 7h0v1l-1 1v-1-2-1l1-4c0-2 1-5 1-8 1-6 1-12 0-17l1-1c3-1 4-2 6-4z" class="X"></path><defs><linearGradient id="BY" x1="492.175" y1="486.338" x2="507.51" y2="505.352" xlink:href="#B"><stop offset="0" stop-color="#5b5a5b"></stop><stop offset="1" stop-color="#7d7d7d"></stop></linearGradient></defs><path fill="url(#BY)" d="M503 478c1 1 0 2 1 3 1 2 1 6 1 9v1h1l-1 4-1 3c-1 6-4 12-7 17h0c0-1-1-1-1-1l-1-1c1-3 5-10 4-13v-1h1l1-2 2-8c0-3-1-5 0-7v-4z"></path><path d="M505 490v1h1l-1 4-1 3c-1-2 0-5 1-8z" class="B"></path><path d="M223 35c0-1 0-1 1-2v1c0 1 1 2 1 2l2 8v2l-1 1 3 6c1 5 4 11 6 15h1v-1h1 0c1 0 3 2 4 3 1 2 2 5 3 8 2 4 5 8 7 11 1 2 2 4 4 5 3 4 6 7 9 10 2 2 3 3 5 4l2 2 2 1c3 3 6 5 8 7-1 1-2 1-4 1l1 1-1 1c0 2 2 3 2 5h-1 0l-1 1v2 1s0 1 1 2h1v1 1c0 2 1 4 2 6-2 0-4 1-6 1 0 0-3 1-3 2-3 1-8 2-10 4h0l-1 1h-8-3c-2 1-3 1-4 1h-3c-2 0-4 0-6 1-2 0-3 1-4 1-1-1-1-1-1-2l-1 1v1c-1 0-1 1-3 2l-1-1h-1l-2 3-2-1c-2-1-4-3-6-4l-1-1-2 2v-1l-1 1c0-1 0-2-1-3h0l-2 3h-2c-1-1-3-1-5-2h-3v-1h-3l-1-1h1l1-1h0v-1c0-2 2-4 3-5v-2-2h1v-3c1 1 2 1 3 1 1-1 2-1 3-2 4-2 6-3 9-6l1-1v-1c1-2 2-4 3-7 1-5 1-13 0-18-2-6-3-10-4-16V70v-1-1c0-1 0-1-1-2v-3c1 0 2 1 3 0h1v1c1 1 1 1 1 2h1c-2-5-3-9-3-14v-4l1-6c1-3 2-5 4-7z" class="g"></path><path d="M246 129h1c1 1 1 2 1 3 1 0 1 1 1 1l-1 1h0s0 1-1 1c-1-2 0-3 0-5l-1-1z" class="k"></path><path d="M249 137l5 2 4 1h1l-2 1c-2-1-7-1-8-3v-1z" class="e"></path><path d="M238 118c1 0 2 0 3 1h0l1 1 5 3h0c-1 0-2 0-2 1l-1-1c-1-1-3-2-4-3v-1l-2-1z" class="j"></path><path d="M246 129l1 1c0 2-1 3 0 5l2 2v1c-3-1-4-3-5-6 0-1 1-2 2-3z" class="N"></path><path d="M243 83c1-1 1-1 1-2 2 2 3 7 4 9v3l-5-10z" class="F"></path><path d="M223 35c0-1 0-1 1-2v1c0 1 1 2 1 2l2 8v2l-1 1c-1-4-2-9-3-12z" class="m"></path><path d="M261 134l3-3c1 0 2-2 3-2s1-1 1-1l1-1h1v3c-2 2-5 4-8 4h-1z" class="R"></path><path d="M227 95h0 1l1 3c2 2 3 5 5 7 2 3 6 5 9 7-2 0-3 0-4-1-6-3-10-10-12-16z" class="J"></path><path d="M271 123c0 1 1 2 2 3 1 0 3 3 4 4 0 0 0 1 1 2h1v1h-1c-2 2-3 4-5 5l-1 1c-1 1-3 0-5 1 0-1-1-1-1-1l3-1c2 0 4-2 5-4s1-2 1-4v-1h0l-1-1v1c0-1-2-2-2-3-1-1-2-1-3-2-1 1-2 3-3 4-2 1-3 2-5 3v-1h0c2-2 4-3 6-6h0v1c2-2 2-2 4-2z" class="e"></path><path d="M223 96c-2-5-4-10-4-15-1-2 0-5-1-7h1c1 1 2 2 2 4v6c0 2 1 5 2 7 3 7 6 13 11 18l9 8h0c-5 0-7-2-10-4v-1c0-1-1-1-2-2s-3-3-4-5-2-6-4-9z" class="D"></path><path d="M270 130c0 1 0 1 1 2h1c1-1 2-1 2-1l1-1c0 2 0 2-1 4s-3 4-5 4l-3 1h-3l-6-1h-2 0c-2-1-3-2-5-3v-1h1c1 0 2 1 3 1h0v-2h0v-1l3 2h4 1c3 0 6-2 8-4z" class="S"></path><path d="M268 135c1 0 1 0 1-1 1 0 2-1 2-1l2 1-2 1h-3z" class="C"></path><path d="M255 138h0c-2-1-3-2-5-3v-1h1c1 0 2 1 3 1 0 1 1 1 2 1h0 3 2 4c-3 1-7 0-10 2z" class="D"></path><path d="M275 130c0 2 0 2-1 4s-3 4-5 4l-3 1h-3l-6-1h-2c3-2 7-1 10-2l3-1h3l2-1c0-1 1-2 1-3l1-1zm-27-40c1 1 1 2 1 4h1l1 1c1 1 2 2 2 4 0 0 0 1 1 2h0c3 3 6 7 9 9 1 1 2 1 2 1 1 1 2 2 3 2l9 6 1 1-1 1c0 2 2 3 2 5h-1 0l-1 1v2 1c-1-1-3-4-4-4-1-1-2-2-2-3-2 0-2 0-4 2v-1-2c0-1-3-3-4-5-3-3-7-7-8-10-2-3-4-4-5-6s-2-5-2-8v-3z" class="E"></path><path d="M248 90c1 1 1 2 1 4h1l1 1c1 1 2 2 2 4 0 0 0 1 1 2v1c2 3 5 7 8 10h0c1 1 2 3 3 4h0c-3-2-6-9-10-9-2-3-4-4-5-6s-2-5-2-8v-3z" class="Q"></path><path d="M250 94l1 1c1 1 2 2 2 4 0 0 0 1 1 2v1c-1-1-3-2-3-3-1-1-1-3-1-5z" class="D"></path><path d="M254 101h0c3 3 6 7 9 9 1 1 2 1 2 1 1 1 2 2 3 2l9 6 1 1-1 1c0 2 2 3 2 5h-1 0l-1 1v2 1c-1-1-3-4-4-4-1-1-2-2-2-3l-1-1v-1l2 1h0 1c-1-1-1-2-2-3s-2-3-3-4c-2-1-3-3-6-3h0c-3-3-6-7-8-10v-1z" class="F"></path><path d="M271 119l3 2c2 1 3 3 4 5h0l-1 1v2 1c-1-1-3-4-4-4-1-1-2-2-2-3l-1-1v-1l2 1h0 1c-1-1-1-2-2-3z" class="N"></path><path d="M236 68v-1h1 0c1 0 3 2 4 3 1 2 2 5 3 8 2 4 5 8 7 11 1 2 2 4 4 5 3 4 6 7 9 10 2 2 3 3 5 4l2 2 2 1c3 3 6 5 8 7-1 1-2 1-4 1l-9-6c-1 0-2-1-3-2 0 0-1 0-2-1-3-2-6-6-9-9h0c-1-1-1-2-1-2 0-2-1-3-2-4l-1-1h-1c0-2 0-3-1-4-1-2-2-7-4-9 0 1 0 1-1 2-2-4-6-9-7-13h1l-1-2z" class="s"></path><path d="M253 99c4 2 7 5 10 8 2 2 3 3 5 4 2 2 4 4 7 5l1 1-1-1v-1c-1-1-3-3-4-5l2 1c3 3 6 5 8 7-1 1-2 1-4 1l-9-6c-1 0-2-1-3-2 0 0-1 0-2-1-3-2-6-6-9-9h0c-1-1-1-2-1-2z" class="P"></path><path d="M279 133v1c0 2 1 4 2 6-2 0-4 1-6 1 0 0-3 1-3 2-3 1-8 2-10 4h0l-1 1h-8-3c-2 1-3 1-4 1-2-2-5-2-7-3-1-1-1-1-2-3h1l2 2c2-1 5 0 7 0h6-1c-3-2-5-3-8-5-1 0-3-1-4-2h0c0-1-1-1-1-2l1-1c1 1 1 1 2 0 2 2 4 4 6 5h2c1 1 2 0 3 1h1 1c1 1 2 1 3 1v-1h-1l2-1h-1l-4-1c1 0 2 0 3-1l6 1h3s1 0 1 1c2-1 4 0 5-1l1-1c2-1 3-3 5-5h1z" class="j"></path><path d="M259 145c2-1 8-1 10-3v-1c0 2 0 2-2 2l1 1 1-1h1c2-1 3-2 5-3v1s-3 1-3 2c-3 1-8 2-10 4h0-8c2-1 3-1 5-2z" class="g"></path><path d="M246 149c-2-2-5-2-7-3-1-1-1-1-2-3h1l2 2c2 1 5 2 7 2 1 0 1 0 2-1 2-1 5-1 7-1h1 1 1c-2 1-3 1-5 2h8l-1 1h-8-3c-2 1-3 1-4 1z" class="k"></path><path d="M231 131c2 0 2-1 4 0h0c2 2 5 2 7 4-1 1-1 1-2 0l-1 1c0 1 1 1 1 2h0c1 1 3 2 4 2l8 5h1-6c-2 0-5-1-7 0l-2-2h-1c1 2 1 2 2 3 2 1 5 1 7 3h-3c-2 0-4 0-6 1-2 0-3 1-4 1-1-1-1-1-1-2l-1 1v1c-1 0-1 1-3 2l-1-1h-1l-2 3-2-1c-2-1-4-3-6-4l-1-1-2 2v-1l-1 1c0-1 0-2-1-3h0c1-1 2-2 2-3l2-2h1l1-1v-1l2 1c2 0 3-1 5-1h0c2-2 4-3 5-5h0l2-5z" class="g"></path><path d="M232 133h1l-1 1h0c1 1 1 2 1 3l-2-1 1-3z" class="J"></path><path d="M236 136v-1c2 0 2 0 3 1 0 1 1 1 1 2l-3 1c-1-1-1-1-1-3z" class="D"></path><path d="M232 133l1-1c3 1 5 2 7 3h0l-1 1c-1-1-1-1-3-1v1c-1-1-2-1-3-3h-1z" class="S"></path><path d="M231 136l2 1v4h0c0 1 0 1-1 1 0-1 0-1-1-2v-1-1h-2l1-1 1-1z" class="e"></path><path d="M213 149l2-2c3 1 5 7 9 5h1 1l-2 3-2-1c-2-1-4-3-6-4l-1-1-2 2v-1-1z" class="C"></path><path d="M231 131c2 0 2-1 4 0h0c2 2 5 2 7 4-1 1-1 1-2 0h0c-2-1-4-2-7-3l-1 1-1 3-1 1-1-1h0l2-5z" class="D"></path><path d="M225 152c0-1 3-3 4-4h1l1 1c0-1 1-2 2-3h1l-1 4v1c-1-1-1-1-1-2l-1 1v1c-1 0-1 1-3 2l-1-1h-1-1z" class="e"></path><path d="M213 145l2-2h1c1 0 3 0 4 1h0 4 0l-1 2h2l-1 1h0c-2 0-4 0-6-1l-3-1-2 3v1 1l-1 1c0-1 0-2-1-3h0c1-1 2-2 2-3z" class="S"></path><path d="M229 136l1 1-1 1h2v1 1c1 1 1 1 1 2 1 0 1 0 1-1l2-1v1c0 1-2 2-3 3l-1 1s0 1-1 1h-1-4-2l1-2h0-4 0c-1-1-3-1-4-1l1-1v-1l2 1c2 0 3-1 5-1h0c2-2 4-3 5-5z" class="D"></path><path d="M231 139v1 2h-2c-1 0-1 1-2 1-1 1-2 0-3 0l3-1c1 0 2-1 3-2l1-1zm2 2l2-1v1c0 1-2 2-3 3l-1 1h-3-1l1-1h1c1-1 2-1 3-2 1 0 1 0 1-1z" class="T"></path><path d="M219 142c2 0 3-1 5-1h3v1l-3 1-4 1c-1-1-3-1-4-1l1-1v-1l2 1z" class="B"></path><path d="M229 136l1 1-1 1h2v1l-1 1c-1 1-2 2-3 2v-1h-3 0c2-2 4-3 5-5z" class="E"></path><path d="M224 141c2 0 3-1 4-1h2c-1 1-2 2-3 2v-1h-3 0z" class="R"></path><path d="M216 83c3 2 2 5 4 8 0 2 1 4 2 5h1c2 3 3 7 4 9s3 4 4 5 2 1 2 2v1c0 1 1 1 2 2l3 3 2 1v1c1 1 3 2 4 3l1 1-2 4c0 1-2 2-4 3-1 0-2-1-4 0h0c-2-1-2 0-4 0l-2 5h0c-1 2-3 3-5 5h0c-2 0-3 1-5 1l-2-1v1l-1 1h-1l-2 2c0 1-1 2-2 3l-2 3h-2c-1-1-3-1-5-2h-3v-1h-3l-1-1h1l1-1h0v-1c0-2 2-4 3-5v-2-2h1v-3c1 1 2 1 3 1 1-1 2-1 3-2 4-2 6-3 9-6l1-1v-1c1-2 2-4 3-7 1-5 1-13 0-18-2-6-3-10-4-16z" class="S"></path><path d="M220 122v-1l1-2h1c0 2 1 3 0 4l-2-1z" class="E"></path><path d="M236 129h0v-1l-2 2v-1c0-2 1-3 1-5h0l1-1v2 2c1 1 1 1 2 1l-2 1z" class="j"></path><path d="M220 122l2 1v3l-3 4c0-2 1-3 1-5l-1-2 1-1zm15-4l-2-1c-2-2-5-6-7-8v-1c3 3 5 5 8 7h1l3 3 2 1v1c-1 0-4-2-5-2z" class="J"></path><g class="g"><path d="M224 127h0v-2c-1-2 0-4 0-6 1-3 1-7 0-10-1-1-1-1-1-2 1 1 1 1 1 2v-1-3c0 2 1 7 2 9h1c0 1 0 3-1 5v3c-1 2-1 4-2 5zm11-9c1 0 4 2 5 2 1 1 3 2 4 3-1 3-3 6-6 7 0 0-1 0-1 1l-1-2 2-1v-1c0-1-1-4-1-5h-1v-1c1 1 2 1 2 2h2c0 1 0 0 1 1v-1l-6-5z"></path><path d="M227 114v-1 1h1c0 1 1 3 2 4v-1c2 3 3 5 3 8 0 2-1 4-2 6l-2 5h0c-1 2-3 3-5 5h0c-2 0-3 1-5 1l-2-1s-1 0-1-1 1-2 2-3 2-2 2-3l2-2c1 0 2-2 2-3h-1c0-1 1-1 1-2 1-1 1-3 2-5v-3c1-2 1-4 1-5z"></path></g><path d="M220 134l1 1h3 1l-4 4-1-2h-2c1-1 2-2 2-3z" class="C"></path><path d="M218 137h2l1 2c-1 1-2 2-2 3l-2-1s-1 0-1-1 1-2 2-3z" class="J"></path><path d="M227 114v-1 1h1c0 1 1 3 2 4 1 5 0 10-3 15-1 1-2 1-2 2h-1-3l-1-1 2-2c1 0 2-2 2-3h-1c0-1 1-1 1-2 1-1 1-3 2-5v-3c1-2 1-4 1-5z" class="T"></path><path d="M226 122l1 1c0 2-1 4-3 6h0-1c0-1 1-1 1-2 1-1 1-3 2-5z" class="C"></path><path d="M217 125v-1c1 1 2 1 3 1 0 2-1 3-1 5l3-4 1 1-1 3s1 0 1-1h1c0 1-1 3-2 3l-2 2c0 1-1 2-2 3s-2 2-2 3 1 1 1 1v1l-1 1h-1l-2 2c0 1-1 2-2 3l-2 3h-2c-1-1-3-1-5-2h-3v-1h-3l-1-1h1l1-1h0v-1c0-2 2-4 3-5v-2-2h1v-3c1 1 2 1 3 1 1-1 2-1 3-2 4-2 6-3 9-6l1-1z" class="R"></path><path d="M205 139c1 0 2-1 3-1l1 1c0 1-1 1 0 2-2 0-3-1-4-2z" class="E"></path><path d="M213 138v-1l3-3 1 1v1l-1 1c-1 0-2 1-3 1z" class="B"></path><path d="M217 125v-1c1 1 2 1 3 1 0 2-1 3-1 5h0c-1 0-1 1-3 1v-3c1-1 1-2 1-3z" class="T"></path><path d="M217 136c1-2 3-3 4-4h1l-2 2c0 1-1 2-2 3s-2 2-2 3 1 1 1 1v1l-1 1h-1l-2 2-1-1c2-2 3-4 4-7l1-1z" class="h"></path><path d="M207 132c4-2 6-3 9-6 0 2-1 3-1 5-1 3-4 4-6 5s-4 1-6 0l-1 1v-1h-1v1c0 1 0 2-1 3h0v-2-2h1v-3c1 1 2 1 3 1 1-1 2-1 3-2z" class="G"></path><path d="M213 138c1 0 2-1 3-1-1 3-2 5-4 7-2 1-4 0-6 0-1 0-2-1-3-1s-4 2-6 3v-1c0-2 2-4 3-5h0c2-1 3-1 5-1 1 1 2 2 4 2 0 1 0 1 1 1 1-1 2-2 3-4z" class="j"></path><path d="M197 146c2-1 5-3 6-3s2 1 3 1c2 0 4 1 6 0l1 1c0 1-1 2-2 3l-2 3h-2c-1-1-3-1-5-2h-3v-1h-3l-1-1h1l1-1h0zm92 15h0v-2l2-1h0v4c1 1 1 1 2 1h0s1 0 1-1h1 1 1c2 0 3 1 4 1h2 1 2l1 1 1 2 4 7 3 3h1l-1-1v-3c1 1 1 2 2 2 2 1 3 2 4 4l3 4c0 1 1 2 1 2 0 2 0 3 1 4l6-6h-4l-1-1c2 1 3 0 5 0 0-1 0-2 1-2v2h-1l1 1c1 0 4-3 6-3h0c-2 1-3 2-4 4l1 1v1h-1c-2 4-3 7-2 11v1 1c1 2 2 3 4 4 1 0 1 0 1 1h2l1 2 3-2 2 2v1c1 1 1 2 1 3v1l2 1h1l1 3h-2l-1-2-4-1c2 2 2 4 3 6 0 1 1 2 1 2v3l-2 2h0l-2 3c-1 2-1 3-2 5l-1-1v-2l-1 1-1 1c-1 0-1 0-2-1l1-3h-1c-1 1-1 1-1 2h1v7c-1 2-1 3-2 4l-1 2-1 1-1 1v-1c-1-1-1-1-2-1h-1l-3-3v1 2l1 1h0-2l2 2c1 2 1 3 1 4 0 0 0 1-1 1l-4-3v1c1 0 1 1 2 1h0-4-1c0-1-1-1-2-2l-4-2c0-1 0-1-1-2-2 0-3-2-4-2-2-1-2-1-4-1h0c-3-1-4-3-7-2l-1-1c-2-2-4-3-5-5l-2-1-2-2-1-1v-1c-1 1-1 0-1 1h-1l-1-1c0-1-1-1-2-2-3-4-5-6-10-8h0l-1-2h-4 6l-2-1-3-1c0-1-1-1-2-1-2-1-5-2-7-3h-1c-1 0-1 0-2-1l1-1h0c-2 0-3-1-4-2-1 0-1 0-1-1l1-1c1-1 2-2 2-3h1c1-2 3-4 4-6v-1c1-1 1-1 2-1 0-1 0-2-1-4-1 2-2 4-3 5-1-1-1-1-2-1l1-1c1-2 4-5 4-8h0l1-2h6c2 1 4 2 6 1l-2-1 1-1h2c3 3 7 5 10 6 0 1 0 1 1 2l3 2c1 1 2 1 4 2h1v-1h0l2 1h1c0-4-2-5-4-8 0-1 0-1 1-2-1-2-3-4-5-6 1 0 2 0 4-1v-1h0v-1l1-1 1 1v-2l-1-1c-2-1-3-3-5-4 1-1 1-2 1-4l-1-1z" class="n"></path><path d="M288 196c2 0 2 1 4 1l5 5h0c-1 0-2-1-2-1l-2-1c-1-2-5-2-7-2l-2 2v-1l-2-1v-2h0c1 0 2 1 3 1 0 0 0-1 1-1h2z" class="W"></path><path d="M322 225c0-2 0-4 1-5 1 1 1 3 1 4l-1 1c0 1 0 2 1 2l-1 1-2-1c-1 0-3-2-5-3 1 1 2 3 2 4h0-1l-4-5h0c2 1 3 3 5 5 0-1-1-1-1-2l-1-1h0c-1-1-1-2-1-3v-1h1 0c1 1 2 2 3 4v-1h0l-1-1h1l3 3h1-1v-1z" class="l"></path><path d="M284 199v1c4 1 9 4 11 7l-6-3c-1 0-1 0-1-1h-3v1c1 0 2 1 3 1v1h0c-2 1-3 1-5-1h0l-3-1-1-1h2c0-1 1-1 1-2h1s1 0 1-1h0v-1z" class="U"></path><path d="M279 203h2c3 1 5 2 7 3h0c-2 1-3 1-5-1h0l-3-1-1-1z" class="F"></path><path d="M293 184c0-1 0-1 1-2l4 5c0 1 2 3 2 4l2 2 1-2 2 1h1c0 2 2 3 2 5h-2v1l-2-2c-1-1 0-1-1-1v1c1 0 1 1 1 1l3 8c-2-2-6-7-8-7l-3-3-4-3h1 0 1v-1h0l2 1h1c0-4-2-5-4-8z" class="Q"></path><path d="M303 191l2 1h1c0 2 2 3 2 5h-2l-4-4 1-2z" class="o"></path><path d="M293 184c0-1 0-1 1-2l4 5c0 1 2 3 2 4v1c0 1 2 3 2 4-1 0-1 1-1 2-1-1-2-1-2-2-2-1-4-3-6-4h0 1v-1h0l2 1h1c0-4-2-5-4-8z" class="H"></path><path d="M298 187c0 1 2 3 2 4v1c0 1 2 3 2 4-1 0-1 1-1 2-1-1-2-1-2-2h1l-2-2v-2c0-2-1-3-1-4l1-1z" class="d"></path><path d="M316 204h1 0l3 3 1 1 1 1 1-1c1 1 4 7 5 9h0v2l1 2h0c-1 1-1 2-1 3v1 2 1c-1 0-2 0-2-1l-1-2-1-1c0-1 0-3-1-4-1 1-1 3-1 5v-3-1c-1-2-1-4-2-6 0-1-1-2-1-2-1-1-2-2-2-4h0l-4-5h3z" class="r"></path><path d="M313 204h3c0 1 1 1 1 2h1s1 1 1 2h0l-2-2h0v2 1h0l-4-5z" class="Q"></path><path d="M325 225h0c1 1 1 2 2 2v-1-1s-1-1 0-2v-1c0-1 0-4 1-5v2l1 2h0c-1 1-1 2-1 3v1 2 1c-1 0-2 0-2-1l-1-2z" class="q"></path><path d="M331 208c1 0 1 0 2 1h1 0c2 2 3 4 3 6v2l1-1 1 2c1 1 1 2 1 3h0l-1 3v-1c-1 1-2 2-2 3h-1 0c-1 0-1 0-1 1h-1 0l-1 1h-1-1l-1 1 1 2-2 1-2-2-1-2-1 1 1 2h-1-1c-2 0-2-1-3-2h-1v2c-2-1-2-2-3-3h1 0c0-1-1-3-2-4 2 1 4 3 5 3l2 1 1-1c-1 0-1-1-1-2l1-1 1 1 1 2c0 1 1 1 2 1v-1-2-1c0-1 0-2 1-3h0l1 2v1h1l1-3v-5c1-2 1-3 1-4v-1c-1-1-2-2-2-3z" class="U"></path><path d="M334 216l1 1c0 1 0 2-1 3 0 1-1 2-1 3l1 1 1 3h-1 0l-1 1h-1-1l-1 1 1 2-2 1-2-2c1 0 1-1 2-2v-4l1 1c1 0 1-1 1-1l1-1c0-1 1-2 1-3 0 0 1-1 1-2h0v-2z" class="f"></path><path d="M333 223l1 1 1 3h-1 0l-1 1v-5z" class="L"></path><path d="M331 208c1 0 1 0 2 1h1 0c2 2 3 4 3 6v2l1-1 1 2c1 1 1 2 1 3h0l-1 3v-1c-1 1-2 2-2 3h-1 0c-1 0-1 0-1 1l-1-3-1-1c0-1 1-2 1-3 1-1 1-2 1-3l-1-1c0-1-1-2-1-4v-1c-1-1-2-2-2-3z" class="X"></path><path d="M339 218c1 1 1 2 1 3h0l-1 3v-1c-1 1-2 2-2 3h-1 0c1-2 2-6 3-8z" class="P"></path><path d="M331 208c1 0 1 0 2 1h1 0c2 2 3 4 3 6v2c0 2 0 4-1 7h-1c-1-2 1-5 1-8l-1 1-1-1c0-1-1-2-1-4v-1c-1-1-2-2-2-3z" class="i"></path><path d="M331 208c1 0 1 0 2 1 0 1 1 2 1 3 1 2 1 3 2 4l-1 1-1-1c0-1-1-2-1-4v-1c-1-1-2-2-2-3z" class="K"></path><path d="M335 207c1 0 4 1 4 1 2 1 3 1 5 3h0c2 2 2 4 3 6 0 1 1 2 1 2v3l-2 2h0l-2 3c-1 2-1 3-2 5l-1-1v-2l-1 1-1 1c-1 0-1 0-2-1l1-3h-1c-1 1-1 1-1 2s0 2-1 2h-1v-4h1c0-1 0-1 1-1h0 1c0-1 1-2 2-3v1l1-3h0c0-1 0-2-1-3l-1-2-1 1v-2c0-2-1-4-3-6 1-1 1-1 1-2z" class="U"></path><path d="M347 217c0 1 1 2 1 2v3l-2 2h0v-6-1l1 1v-1z" class="F"></path><path d="M338 227c1 1 1 1 3 1v-1s0-1 1-1h0v3l2-2c-1 2-1 3-2 5l-1-1v-2l-1 1h-2v-3z" class="m"></path><path d="M336 226h0 1c0-1 1-2 2-3v1l-1 3v3h2l-1 1c-1 0-1 0-2-1l1-3h-1c-1 1-1 1-1 2s0 2-1 2h-1v-4h1c0-1 0-1 1-1z" class="K"></path><path d="M339 208c2 1 3 1 5 3h0c2 2 2 4 3 6v1l-1-1v1c-1-3-3-6-6-8l-2-1 1-1z" class="c"></path><defs><linearGradient id="BZ" x1="336.117" y1="215.676" x2="343.38" y2="216.645" xlink:href="#B"><stop offset="0" stop-color="#c9c9c8"></stop><stop offset="1" stop-color="#f2f0f2"></stop></linearGradient></defs><path fill="url(#BZ)" d="M335 207c1 0 4 1 4 1l-1 1 2 1h0c0 2 1 3 2 4 2 2 2 4 2 6-1 2-1 4-3 5l-1-1v-3h0c0-1 0-2-1-3l-1-2-1 1v-2c0-2-1-4-3-6 1-1 1-1 1-2z"></path><path d="M338 211l3 6c0 1 0 3-1 4 0-1 0-2-1-3l-1-2v-5z" class="U"></path><path d="M335 207c1 0 4 1 4 1l-1 1-1-1-1 1 2 2v5l-1 1v-2c0-2-1-4-3-6 1-1 1-1 1-2z" class="K"></path><path d="M301 215h3c3 2 6 5 9 8l4 5c1 1 1 2 3 3v-2h1c1 1 1 2 3 2h1 1l-1-2 1-1 1 2 2 2 2-1-1-2 1-1h1 1l1-1h0v4h1c1 0 1-1 1-2h1v7c-1 2-1 3-2 4l-1 2-1 1-1 1v-1c-1-1-1-1-2-1h-1l-3-3v1 2l1 1h0-2c-1 0-3-3-3-4h0c-1-1-2-1-3-2-2-3-4-6-6-10-1 0-2-2-3-3-2-3-3-5-6-7l-3-2z" class="F"></path><path d="M332 235c0-1 0-2 1-3l1 1v3l2-2v1l1 1c-1 2-1 3-2 4l-1 2-1 1-1 1v-1c-1-1-1-1-2-1h-1l-3-3v-1s1 1 2 1c1-1 2-1 3-1h0v-1c1-1 1-1 1-2z" class="R"></path><path d="M330 242l2-1v-2h1c1 0 1 1 2 1l-1 2-1 1-1 1v-1c-1-1-1-1-2-1z" class="D"></path><path d="M313 227c2 2 3 4 5 5 1 0 2 0 3 1 1 0 1 0 2-1 0 0 0 1 1 1 1 2 1 3 3 4h0 3v-1c1 0 1-1 2-1 0 1 0 1-1 2v1h0c-1 0-2 0-3 1-1 0-2-1-2-1v1 1 2l1 1h0-2c-1 0-3-3-3-4h0c-1-1-2-1-3-2-2-3-4-6-6-10z" class="Y"></path><path d="M319 237c2-1 2-2 4-2h1c0 1 1 2 2 3v1 1 2l1 1h0-2c-1 0-3-3-3-4h0c-1-1-2-1-3-2z" class="J"></path><path d="M301 215h3c3 2 6 5 9 8l4 5c1 1 1 2 3 3v-2h1c1 1 1 2 3 2h1 1l-1-2 1-1 1 2 2 2 2-1v2l-1 1-1-1c-1 1-1 1-1 2l-1-1c-1 0-2 0-3-1-1 0-1-1-1-1-1 1-1 1-2 1-1-1-2-1-3-1-2-1-3-3-5-5-1 0-2-2-3-3-2-3-3-5-6-7l-3-2z" class="L"></path><path d="M295 171l8 8 3 3c1 1 1 2 2 4h0c3 5 5 10 9 15 2 2 4 5 6 7l-1 1-1-1-1-1-3-3h0-1-3-1l-6-6v-1h2c0-2-2-3-2-5h-1l-2-1-1 2-2-2c0-1-2-3-2-4l-4-5c-1-2-3-4-5-6 1 0 2 0 4-1v-1h0v-1l1-1 1 1v-2z" class="P"></path><path d="M305 192l1-1v-1-1c1 2 1 4 3 5 1 3 4 5 5 8-2-1-4-3-6-5 0-2-2-3-2-5h-1z" class="L"></path><path d="M295 171l8 8 3 3c1 1 1 2 2 4h0c3 5 5 10 9 15v1h0c-3-3-6-7-8-11s-3-8-6-11l-1 1v-1 1 1c-2-2-3-4-5-6h0c-1-1-1-2-2-3v-2z" class="h"></path><path d="M297 176l1-1 5 5-1 1v-1 1 1c-2-2-3-4-5-6h0z" class="i"></path><defs><linearGradient id="Ba" x1="297.907" y1="184.755" x2="304.631" y2="183.889" xlink:href="#B"><stop offset="0" stop-color="#656667"></stop><stop offset="1" stop-color="#908e8e"></stop></linearGradient></defs><path fill="url(#Ba)" d="M293 173l1-1 1 1c1 1 1 2 2 3h0c2 2 3 4 5 6 0 1 4 6 4 7v1 1l-1 1h1-1l-2-1-1 2-2-2c0-1-2-3-2-4l-4-5c-1-2-3-4-5-6 1 0 2 0 4-1v-1h0v-1z"></path><path d="M301 188c1 0 1 0 1-1 1 2 2 3 3 5h1-1l-2-1-2-3z" class="K"></path><path d="M295 177h1c1 3 5 6 6 10 0 1 0 1-1 1-2-3-5-6-6-10v-1z" class="H"></path><defs><linearGradient id="Bb" x1="289.229" y1="177.833" x2="303.626" y2="188.415" xlink:href="#B"><stop offset="0" stop-color="#565657"></stop><stop offset="1" stop-color="#7c7b7b"></stop></linearGradient></defs><path fill="url(#Bb)" d="M293 173l1-1 1 1c1 1 1 2 2 3h0l-1 1h-1v1c1 4 4 7 6 10l2 3-1 2-2-2c0-1-2-3-2-4l-4-5c-1-2-3-4-5-6 1 0 2 0 4-1v-1h0v-1z"></path><path d="M293 173l1-1 1 1c1 1 1 2 2 3h0l-1 1h-1v-1c-1-1-1-2-2-3z" class="N"></path><path d="M272 181l1-1h2c3 3 7 5 10 6 0 1 0 1 1 2l3 2c1 1 2 1 4 2h0-1l-3-1h0v1c1 1 2 1 3 2h-1 0c-1-1-2-1-3-1v1l4 3c-2 0-2-1-4-1h-2c-1 0-1 1-1 1-1 0-2-1-3-1h0v2l2 1v1h0c0 1-1 1-1 1h-1c0 1-1 1-1 2h-2c-1 0-1-1-2-1-2-1-3-1-5-2h-2v1h-1 1v1c-1 0-1 0-1 1h0-2c0-1-1-2-1-2l-2 1h0-1v-2s-1 0-1-1l4-4v-2-1c-1 1-2 1-2 1-2 0-2 1-4 1v-1c1-1 1-1 2-1 0-1 0-2-1-4-1 2-2 4-3 5-1-1-1-1-2-1l1-1c1-2 4-5 4-8h0l1-2h6c2 1 4 2 6 1l-2-1z" class="I"></path><path d="M277 184c2 1 3 1 5 2v1c-2 0-3 0-5-1-1 0-2 0-3-1 1-1 2-1 3-1z" class="B"></path><path d="M272 186l4 1c2 1 3 2 5 3 0 1 0 1-1 1l-1-1c-2 0-3-1-5-2l-3-1v-1h1z" class="N"></path><path d="M286 188l3 2c1 1 2 1 4 2h0-1l-3-1h0v1c1 1 2 1 3 2h-1 0c-1-1-2-1-3-1v1c-2-1-5-3-7-4 2 0 4 1 5 1l1-1c-1 0-2-1-3-2h2z" class="P"></path><path d="M262 181h6c2 1 4 2 6 1 1 0 1 1 2 1 0 1 1 1 1 1-1 0-2 0-3 1 1 1 2 1 3 1l-1 1-4-1h-3 0 0v-2c-2-1-2-1-3-1-2 0-2 2-3 3l-2 2c-1 2-2 4-3 5-1-1-1-1-2-1l1-1c1-2 4-5 4-8h0l1-2z" class="h"></path><path d="M266 183c1 0 1 0 3 1v2h0 0 3-1v1 1h0c1 1 1 3 1 4 0 0-1 1 0 2h0-1c0 1-1 1-2 1l-1-1-2 1v-2-1c-1 1-2 1-2 1-2 0-2 1-4 1v-1c1-1 1-1 2-1 0-1 0-2-1-4l2-2c1-1 1-3 3-3z" class="V"></path><path d="M266 192c1-1 2-1 4-1h1l-1 2-2 1-2 1v-2-1z" class="N"></path><path d="M266 193l2-1 1 1h1l-2 1-2 1v-2z" class="V"></path><path d="M263 186s2 0 2 1c1 0 1 0 1 1 1 0 2 0 3 1-1 0-5 3-7 3h0c0-1 0-2-1-4l2-2z" class="S"></path><path d="M266 183c1 0 1 0 3 1v2h0 0 3-1v1 1h0c1 1 1 3 1 4 0 0-1 1 0 2h0-1c0 1-1 1-2 1l-1-1 2-1 1-2c0-1-1-2-1-3l-1 1c-1-1-2-1-3-1 0-1 0-1-1-1 0-1-2-1-2-1 1-1 1-3 3-3z" class="B"></path><path d="M268 186l2 1v1l-1 1c-1-1-2-1-3-1l2-2z" class="E"></path><path d="M266 183c0 1 1 2 2 3l-2 2c0-1 0-1-1-1 0-1-2-1-2-1 1-1 1-3 3-3z" class="M"></path><path d="M272 192c0-1 0-3-1-4h0v-1l3 1c2 1 3 2 5 2l1 1c3 2 6 3 8 5h-2c-1 0-1 1-1 1-1 0-2-1-3-1h0v2l2 1v1h0c0 1-1 1-1 1h-1c0 1-1 1-1 2h-2c-1 0-1-1-2-1-2-1-3-1-5-2h-2v1h-1 1v1c-1 0-1 0-1 1h0-2c0-1-1-2-1-2l-2 1h0-1v-2s-1 0-1-1l4-4 2-1 1 1c1 0 2 0 2-1h1 0c-1-1 0-2 0-2z" class="H"></path><path d="M282 199v-1l2 1v1h0c0 1-1 1-1 1h-1c0 1-1 1-1 2h-2c-1 0-1-1-2-1v-1c-1 0-1 0-1-1h5 0l1-1z" class="d"></path><defs><linearGradient id="Bc" x1="276.523" y1="191.053" x2="284.455" y2="196.811" xlink:href="#B"><stop offset="0" stop-color="#989696"></stop><stop offset="1" stop-color="#cac9ca"></stop></linearGradient></defs><path fill="url(#Bc)" d="M282 199c-1-1-3-1-4-2s-1-2 0-3h0c-1-1-1-1-2-1v-1l1-1c1 0 1-1 2-1l1 1c3 2 6 3 8 5h-2c-1 0-1 1-1 1-1 0-2-1-3-1h0v2 1z"></path><path d="M272 192c0-1 0-3-1-4h0v-1l3 1-1 1v1l1 1c0 2-1 2 0 4h0l1 2c2 0 2 0 3 1v1h-1-5v1h-2v1h-1 1v1c-1 0-1 0-1 1h0-2c0-1-1-2-1-2l-2 1h0-1v-2s-1 0-1-1l4-4 2-1 1 1c1 0 2 0 2-1h1 0c-1-1 0-2 0-2z" class="b"></path><path d="M272 192c1 0 1 1 1 2h-1 0c-1-1 0-2 0-2z" class="Y"></path><path d="M267 197v2l-1 1v1l-2 1v-2c1-2 1-2 3-3z" class="H"></path><path d="M267 197c1-1 1-1 2-1v5h1v1c-1 0-1 0-1 1h0-2c0-1-1-2-1-2v-1l1-1v-2z" class="F"></path><path d="M269 196h3v1h-2v1c1 1 1 1 2 0l1-1h2 0c2 0 2 0 3 1v1h-1-5v1h-2v1h-1v-5z" class="c"></path><path d="M289 161h0v-2l2-1h0v4c1 1 1 1 2 1h0s1 0 1-1h1 1 1c2 0 3 1 4 1h2 1 2l1 1 1 2 4 7 3 3h1l-1-1v-3c1 1 1 2 2 2 2 1 3 2 4 4l3 4c0 1 1 2 1 2 0 2 0 3 1 4l6-6h-4l-1-1c2 1 3 0 5 0 0-1 0-2 1-2v2h-1l1 1c1 0 4-3 6-3h0c-2 1-3 2-4 4l1 1v1h-1c-2 4-3 7-2 11v1 1c1 2 2 3 4 4 1 0 1 0 1 1h2l1 2 3-2 2 2v1c1 1 1 2 1 3v1l2 1h1l1 3h-2l-1-2-4-1h0c-2-2-3-2-5-3 0 0-3-1-4-1 0 1 0 1-1 2h0-1c-1-1-1-1-2-1 0 1 1 2 2 3v1c0 1 0 2-1 4v5l-1 3h-1v-1l-1-2-1-2v-2h0c-1-2-4-8-5-9-2-2-4-5-6-7-4-5-6-10-9-15h0c-1-2-1-3-2-4l-3-3-8-8-1-1c-2-1-3-3-5-4 1-1 1-2 1-4l-1-1z" class="p"></path><path d="M323 190v3c0 1 0 1-1 2v2h0l-2-2h0l1-1v-1c1-1 1-2 2-3z" class="Q"></path><path d="M330 205l5 2c0 1 0 1-1 2h0-1c-1-1-1-1-2-1-1-1-1-1-1-3z" class="f"></path><path d="M309 172l2 2c0 1 1 2 1 3l-7-4h3l1-1z" class="h"></path><path d="M312 184c-2-2-3-5-6-7v-1c1 1 2 2 3 2l5 5c-1 0-1 0-2 1z" class="m"></path><path d="M299 168c3 1 4 1 6 2l3 3h-3l-3-3-3-2z" class="B"></path><path d="M306 182c5 3 8 7 11 11v1l-3-3c-2-2-3-4-5-6l-1 1h0c-1-2-1-3-2-4z" class="I"></path><path d="M325 192s1 0 1 1c1 3 1 6 3 8 0 1 2 2 2 2v1c-2-1-3-2-5-3-2-3-2-6-1-9z" class="H"></path><path d="M317 185h3l1-1v-1c1 0 2 2 3 3 0 1-1 1-1 2v1 1c-1 1-1 2-2 3v1l-1 1-1-2v-1c1 0 1 0 2-1v-1l1-1c-1-2-2-3-4-3l-1-1z" class="P"></path><path d="M330 223v-1c1-1 1-6 1-7v-1-2c-1-1-4-6-4-7h3c0 2 0 2 1 3 0 1 1 2 2 3v1c0 1 0 2-1 4v5l-1 3h-1v-1z" class="Q"></path><path d="M312 184c1-1 1-1 2-1 1 1 2 2 3 2l1 1c2 0 3 1 4 3l-1 1v1c-1 1-1 1-2 1v1l-7-9z" class="d"></path><path d="M318 186c2 0 3 1 4 3l-1 1h-2c0-1-2-2-2-4h1z" class="B"></path><path d="M289 161h0v-2l2-1h0v4c1 1 1 1 2 1h0l6 5 3 2c-1 0-2 0-4-1 0 0-2-1-3-1l-1 1v1c-2-1-3-3-5-4 1-1 1-2 1-4l-1-1z" class="U"></path><path d="M341 205l3-2 2 2v1c1 1 1 2 1 3v1l2 1h1l1 3h-2l-1-2-4-1h0c-1-1-1-2-1-3-1 0-2-1-3-1h-1-1-1l-1-1h-3c-1-1-2-1-3-2h0c1 1 2 1 3 1h5 3z" class="r"></path><path d="M315 172c1 1 1 2 2 2 2 1 3 2 4 4l3 4v4c-1-1-2-3-3-3v1l-1 1h-3c-1 0-2-1-3-2l-5-5 1 1c1 0 2 0 3 1h0c2 1 3 3 6 4h0l1-1c-1-1-2-5-3-6-1 0-2 1-2 1-2 0-2 0-3-1 0-1-1-2-1-3s0-1 1-1l3 3h1l-1-1v-3z" class="W"></path><path d="M317 174c2 1 3 2 4 4v3h-1c-1-2-2-4-3-7z" class="p"></path><path d="M297 162c2 0 3 1 4 1h2 1 2l1 1 1 2 4 7c-1 0-1 0-1 1l-2-2-1 1-3-3c-2-1-3-1-6-2l-6-5s1 0 1-1h1 1 1z" class="D"></path><path d="M297 162c2 0 3 1 4 1h2 1 2l1 1c-2 0-2 0-3 1v1l-9-4h1 1z" class="X"></path><path d="M307 164l1 2 4 7c-1 0-1 0-1 1l-2-2-1 1-3-3 1-2-2-2v-1c1-1 1-1 3-1z" class="P"></path><path d="M306 168c1 1 2 2 3 4l-1 1-3-3 1-2z" class="N"></path><path d="M308 186l1-1c2 2 3 4 5 6l3 3v2c3 2 4 5 7 7 1 1 1 2 1 3 1 3 4 5 5 7v4c0 1 0 2-1 3h0l-1-1v-2h0c-1-2-4-8-5-9-2-2-4-5-6-7-4-5-6-10-9-15z" class="s"></path><path d="M333 179v2h-1l1 1c1 0 4-3 6-3h0c-2 1-3 2-4 4l1 1v1h-1c-2 4-3 7-2 11v1 1c1 2 2 3 4 4 1 0 1 0 1 1h2l1 2h-3l-4-1h-3v-1s-2-1-2-2c-2-2-2-5-3-8 0-1-1-1-1-1 0-2 0-3 1-4l6-6h-4l-1-1c2 1 3 0 5 0 0-1 0-2 1-2z" class="Q"></path><path d="M338 203h2l1 2h-3l-4-1 1-1h3z" class="i"></path><path d="M333 198v1c1 2 3 3 4 4h-2 0-1c0-1 0-2-1-3l-2-2c-1-3-2-5-1-8 1-2 3-5 5-6v1c-2 4-3 7-2 11v1 1z" class="l"></path><path d="M266 192v1 2l-4 4c0 1 1 1 1 1v2h1 0l2-1s1 1 1 2h2 0c0-1 0-1 1-1v-1h-1 1v-1h2c2 1 3 1 5 2 1 0 1 1 2 1l1 1 3 1h0c2 2 3 2 5 1 3 2 5 3 7 5l6 4 3 2c3 2 4 4 6 7 1 1 2 3 3 3 2 4 4 7 6 10 1 1 2 1 3 2h0c0 1 2 4 3 4l2 2c1 2 1 3 1 4 0 0 0 1-1 1l-4-3v1c1 0 1 1 2 1h0-4-1c0-1-1-1-2-2l-4-2c0-1 0-1-1-2-2 0-3-2-4-2-2-1-2-1-4-1h0c-3-1-4-3-7-2l-1-1c-2-2-4-3-5-5l-2-1-2-2-1-1v-1c-1 1-1 0-1 1h-1l-1-1c0-1-1-1-2-2-3-4-5-6-10-8h0l-1-2h-4 6l-2-1-3-1c0-1-1-1-2-1-2-1-5-2-7-3h-1c-1 0-1 0-2-1l1-1h0c-2 0-3-1-4-2-1 0-1 0-1-1l1-1c1-1 2-2 2-3h1c1-2 3-4 4-6 2 0 2-1 4-1 0 0 1 0 2-1z" class="g"></path><path d="M298 220l1-1c2 0 2 1 3 2h0v1 1l-2-1c-1 0-1-1-2-2z" class="s"></path><path d="M306 237c3 1 6 1 9 4v1 1c-1 0-1 1-1 2 0-1 0-1-1-2l-3-3h-1l-3-3z" class="J"></path><path d="M279 215h3c0 1 2 1 2 1 1 0 2 1 3 2 2 2 3 6 3 9-1-1-2-3-4-5l-1-1c0-2-1-3-3-4h0l-3-2z" class="j"></path><path d="M302 223v-1c0 1 0 2 1 3 1 0 1-2 3-1h0c1 0 1 1 1 1 2 0 3 1 3 2 1 1 1 2 1 2l-2 2c0-4-3-2-6-4-2-1-2-2-3-4h2z" class="M"></path><path d="M309 231h-2c-3 0-5-1-7-3-3-2-3-4-3-7l1-1c1 1 1 2 2 2l2 1h-2c1 2 1 3 3 4 3 2 6 0 6 4z" class="O"></path><path d="M278 210h5 3 5c1 1 3 1 4 1l6 4 3 2c-4 0-8-2-12-4-5-2-10-2-14-3z" class="J"></path><path d="M291 229l3 3c1 1 2 2 4 2 2 1 3 2 5 2l3 1 3 3h1l3 3c-2 0-3-2-4-2-2-1-2-1-4-1h0c-3-1-4-3-7-2l-1-1c-2-2-4-3-5-5 0-1 0-2-1-2v-1z" class="e"></path><path d="M283 205c2 2 3 2 5 1 3 2 5 3 7 5-1 0-3 0-4-1h-5-3-5-5 0c-2 0-3 0-4-1h5v-1c1-1 4-1 6-1h1 1s0-1 1-2z" class="M"></path><path d="M274 209h3 0-1c-1 0-2 1-3 1-2 0-3 0-4-1h5z" class="T"></path><path d="M274 208c1-1 4-1 6-1h1l1 1c-1 0-3 0-5 1h-3v-1z" class="Y"></path><path d="M283 205c2 2 3 2 5 1 3 2 5 3 7 5-1 0-3 0-4-1-1 0-3-1-4-1-2 0-3-1-5-1l-1-1h1s0-1 1-2z" class="B"></path><defs><linearGradient id="Bd" x1="276.21" y1="203.761" x2="270.049" y2="204.4" xlink:href="#B"><stop offset="0" stop-color="#5f5e5f"></stop><stop offset="1" stop-color="#7b7a7b"></stop></linearGradient></defs><path fill="url(#Bd)" d="M270 201v-1h2c2 1 3 1 5 2 1 0 1 1 2 1l1 1 3 1h0c-1 1-1 2-1 2h-1-1c-2 0-5 0-6 1l-4-1c-1 0-2-1-3-1v-3h2 0c0-1 0-1 1-1v-1h-1 1z"></path><path d="M267 203h2c1 1 1 2 1 3v1c-1 0-2-1-3-1v-3z" class="L"></path><path d="M280 204l3 1h0c-1 1-1 2-1 2h-1-1c-2 0-2-1-4-2 2 0 2 0 4-1z" class="G"></path><path d="M270 201v-1h2c2 1 3 1 5 2 1 0 1 1 2 1l1 1c-2 1-2 1-4 1-1 0-3-2-4-3h0l-2-1z" class="c"></path><path d="M270 201v-1h2c2 1 3 1 5 2 0 1 0 0-1 1-1 0-2-1-4-1l-2-1z" class="G"></path><path d="M271 214l8 1 3 2h0c2 1 3 2 3 4l1 1c2 2 3 4 4 5l1 2v1c1 0 1 1 1 2l-2-1-2-2-1-1v-1c-1 1-1 0-1 1h-1l-1-1c0-1-1-1-2-2-3-4-5-6-10-8h0l-1-2h-4 6l-2-1z" class="V"></path><path d="M285 224c0-1-1-2 0-3l1 1c2 2 3 4 4 5l1 2v1c0-1-1-1-1-1-1-1-1-2-2-2 0 0-1-1-2-1h0c-1-1-2-1-3-2l1 1 1-1z" class="C"></path><path d="M271 214l8 1 3 2h0c2 1 3 2 3 4l1 1-1-1c-1 1 0 2 0 3l-1 1-1-1c0-1-1-2-2-3-1 0-1-1-2-1-2-2-4-4-6-5l-2-1z" class="D"></path><path d="M285 224c-1 0-1-1-1-1v-2c-1 0-1 0-1-1l2 1 1 1-1-1c-1 1 0 2 0 3z" class="T"></path><path d="M266 192v1 2l-4 4c0 1 1 1 1 1v2h1 0l2-1s1 1 1 2v3c1 0 2 1 3 1l4 1v1h-5c1 1 2 1 4 1h0-1-2c-1 1-3 0-4 0l-3-1-1-1c-2-1-3-1-5-1h0c-2 0-3-1-4-2-1 0-1 0-1-1l1-1c1-1 2-2 2-3h1c1-2 3-4 4-6 2 0 2-1 4-1 0 0 1 0 2-1z" class="J"></path><path d="M258 200l1 2v1h0l-1 1v1c2 0 3 1 4 2v1c-2-1-3-1-5-1h0c-2 0-3-1-4-2-1 0-1 0-1-1l1-1c1-1 2-2 2-3h1l1 2 1-2z" class="N"></path><path d="M258 200l1 2v1h0l-1 1v1h-2c1-1 1-1 1-2v-1l1-2z" class="E"></path><path d="M253 203c2 0 3 1 4 3v1c-2 0-3-1-4-2-1 0-1 0-1-1l1-1z" class="B"></path><path d="M263 200v2h1 0l2-1s1 1 1 2v3c1 0 2 1 3 1l4 1v1h-5l-2-1c-2-1-5-4-6-6h0l1-2h1z" class="Z"></path><path d="M263 200v2 1c-1 0-1-1-2-1l1-2h1z" class="k"></path><path d="M266 201s1 1 1 2v3c-1-1-2-2-3-4h0l2-1z" class="f"></path><path d="M266 192v1 2l-4 4c0 1 1 1 1 1h-1l-1 2h0v-2h-1c-1 0-1 1-1 2l-1-2-1 2-1-2c1-2 3-4 4-6 2 0 2-1 4-1 0 0 1 0 2-1z" class="D"></path><path d="M260 194c2 0 2-1 4-1-2 2-4 5-6 7l-1 2-1-2c1-2 3-4 4-6z" class="c"></path><path d="M469 170h3c2 1 4 1 6 1l10 3c1 1 2 3 3 3l3 2c1-1 1 0 2 0l1-1 3 2h3l-2-2c3 2 8 4 11 6v2l2 2c1 2 1 3 2 3 1-1 1-3 1-5 0 2 1 3 2 4h0 2s1 0 2 1h1c-3-5-5-7-9-9l1-1-1-1 1-1h0c1 1 1 1 2 1 0-2-2-1-2-4l2 2 1-1 1 1v-1c1 1 2 2 4 3 0 1 2 3 3 3s3 1 4 2v1l-4-1c0 1 1 1 1 2 1 4 2 8 2 12 0 1 1 2 1 2v2 1h1v-2l1 1c1-1 0-2-1-3 1 1 2 1 2 2 0-1 1-2 1-3l-1-1h1 1 0 1c1 0 1 1 3 0l1-1v1l2 2c0 1 2 2 2 2l-1 1v1c1 1 1 2 1 2 2 3 7 6 8 9h0c-1 0-1 0-2 1 1 1 1 1 1 2v1c-1 1-1 1-2 1h1v1c-1 3 0 5-1 7 0 3-1 6-2 10l-2 4c0 1-2 3-2 5v2h0c-1-2-2-3-4-5 0 1 0 1-1 2l-3-4h-1c-2-1-4-3-6-4-1 0-2-1-2-1h-1-1c-1-2-3-4-5-5l-1 1 4 3 2 1-1 1-4-1c-2 1-3 2-4 3l-1-1c-1-2-3-3-4-4-2-2-4-2-6-3 0-1-1-1-1-1-2-2-4-2-6-2-1 0-2-1-4-1-1 0-2-1-3-1-2-1-4-2-7-3-2-1-5-2-7-2l-1-1c0-2-2-2-4-3s-3-2-5-3h-1s0-1-1-1c-2-2-3-2-6-2h-1c1 0 1 0 1-1l-2-1c-2 1-6 1-8 0h-2l2-1 3-1h1c3-2 6-3 7-6-1-1-1-1-2-1v-4-1c-1 0-2-1-3-2-2 0-2 1-4 1v-3c1-1 2 0 2-2l-1-1c0-1-1 0-2 0v-1l1-1-1-1c1 0 1 0 2-1h-2 0l1-1h1l-1-1h0l-4-2c2-1 4-1 5-1 1-2 2-2 3-2l1-1h0l1-1-1-1c1 0 2 0 3-1l1-2c1 0 1 0 2-1h1c2 0 4 0 6-1z" class="K"></path><defs><linearGradient id="Be" x1="530.887" y1="228.896" x2="520.178" y2="228.273" xlink:href="#B"><stop offset="0" stop-color="#0a0907"></stop><stop offset="1" stop-color="#323336"></stop></linearGradient></defs><path fill="url(#Be)" d="M522 222l1-1c2 1 3 2 5 3h0l2 5h-1c1 3 2 5 2 7v2c-1-1-2-1-2-2-1-1-2-3-2-5-2-3-4-6-7-9h1 0 1z"></path><path d="M522 222l1-1c2 1 3 2 5 3h0l2 5h-1c-1-3-4-5-7-7z" class="B"></path><path d="M528 224c1 0 1 1 2 1 2 0 4 1 5 1l1 1-1 1c0 4 3 7 3 11v1h0l2 4c0 1 0 1-1 2l-3-4c-2-2-3-3-5-4v-2c0-2-1-4-2-7h1l-2-5z" class="d"></path><path d="M528 224c1 0 1 1 2 1 3 3 4 9 6 13h-1 0c-3-2-4-7-5-9l-2-5z" class="D"></path><path d="M530 229c1 2 2 7 5 9h0 1l2 2 2 4c0 1 0 1-1 2l-3-4c-2-2-3-3-5-4v-2c0-2-1-4-2-7h1z" class="N"></path><path d="M481 198c2 2 14 8 16 8h0c-1-1-1-1-1-2 6 4 13 8 19 13l2 1 1 2c-6-2-12-7-18-10-2 0-3 0-5-1l-6-3-6-3h1c-1-1-1-2-1-2l-4-3h2z" class="h"></path><path d="M483 201l17 9c-2 0-3 0-5-1l-6-3-6-3h1c-1-1-1-2-1-2z" class="a"></path><defs><linearGradient id="Bf" x1="494.696" y1="202.414" x2="521.306" y2="213.686" xlink:href="#B"><stop offset="0" stop-color="#5a595a"></stop><stop offset="1" stop-color="#858585"></stop></linearGradient></defs><path fill="url(#Bf)" d="M496 197l1 1c0 1 0 1 1 2 2 1 6 2 8 3s3 2 4 3l1 1h0 2c1 0 3 2 5 1h0c2 0 2 0 3 1-1 1 0 1-1 2v1h0c0 1 1 1 1 1v2h0v4 3h0-1s-2-1-2-2l-1-2-2-1c-6-5-13-9-19-13h-1l1-1c-1-1-1-1-1-2l-3-2h2l-1-1h1l1-1v1c1 0 1-1 1-1z"></path><path d="M517 216l2 1-2 1-2-1 2-1z" class="C"></path><path d="M495 197v1l1 3h-1l-3-2h2l-1-1h1l1-1z" class="V"></path><path d="M519 217l2 2v3h0-1s-2-1-2-2l-1-2 2-1z" class="E"></path><path d="M513 207c1 0 3 2 5 1h0c2 0 2 0 3 1-1 1 0 1-1 2v1h0c0 1 1 1 1 1v2h0c-1 0-2-1-3-2-2-1-7-4-7-6h2z" class="X"></path><path d="M495 201h1c1 3 3 3 5 5 3 1 6 3 9 4 2 2 4 4 7 6l-2 1c-6-5-13-9-19-13h-1l1-1c-1-1-1-1-1-2z" class="D"></path><path d="M521 215h0l3 2 2 1c5 2 10 5 16 5 1 0 2 0 3-1h1l1-1c1-1 0-1 1-2l2 1h1v1c-1 3 0 5-1 7 0 3-1 6-2 10l-2 4c0 1-2 3-2 5v2h0c-1-2-2-3-4-5l-2-4h0v-1c0-4-3-7-3-11l1-1-1-1c-1 0-3-1-5-1-1 0-1-1-2-1h0c-2-1-3-2-5-3l-1 1h-1v-3-4z" class="Q"></path><defs><linearGradient id="Bg" x1="540.973" y1="224.559" x2="545.245" y2="237.684" xlink:href="#B"><stop offset="0" stop-color="#0b0a0b"></stop><stop offset="1" stop-color="#2e2e2e"></stop></linearGradient></defs><path fill="url(#Bg)" d="M536 225h0c3 1 7 1 10 0 1 0 2-2 2-3l-1 7c-2 4-3 10-3 14-2-5-4-11-8-15v-1l-1-1 1-1z"></path><defs><linearGradient id="Bh" x1="533.257" y1="214.403" x2="534.729" y2="225.741" xlink:href="#B"><stop offset="0" stop-color="#a1a0a0"></stop><stop offset="1" stop-color="#c1bfc1"></stop></linearGradient></defs><path fill="url(#Bh)" d="M521 215h0l3 2 2 1c5 2 10 5 16 5 1 0 2 0 3-1h1l1-1c1-1 0-1 1-2l2 1h1v1c-1 3 0 5-1 7v-3-4-1l-1 1v1 2 2h0c0 1-1 2-2 3l1-7c0 1-1 3-2 3-3 1-7 1-10 0h0l-1 1c-1 0-3-1-5-1-1 0-1-1-2-1h0c-2-1-3-2-5-3l-1 1h-1v-3-4z"></path><path d="M521 219l15 6-1 1c-1 0-3-1-5-1-1 0-1-1-2-1h0c-2-1-3-2-5-3l-1 1h-1v-3z" class="R"></path><path d="M541 197v1l2 2c0 1 2 2 2 2l-1 1v1c1 1 1 2 1 2 2 3 7 6 8 9h0c-1 0-1 0-2 1 1 1 1 1 1 2v1c-1 1-1 1-2 1l-2-1c-1 1 0 1-1 2l-1 1h-1c-1 1-2 1-3 1-6 0-11-3-16-5 1 0 2 0 3-1l1-2h0v-11h-1l1-1v-4c0 1 1 2 1 2v2 1h1v-2l1 1c1-1 0-2-1-3 1 1 2 1 2 2 0-1 1-2 1-3l-1-1h1 1 0 1c1 0 1 1 3 0l1-1z" class="T"></path><path d="M532 200c1 1 2 1 2 2l4 2v1l2 1-1 1c-1 0-1 0-2-1 0 1 3 3 3 4h-1c-1-1-4-4-6-5h-1c0 1 0 2 1 3l-1 1v-1c-1-2-1-4-1-7v2 1h1v-2l1 1c1-1 0-2-1-3z" class="X"></path><path d="M537 206v-1l1-1v1l2 1-1 1c-1 0-1 0-2-1z" class="i"></path><path d="M539 210h1c1 1 2 1 4 2l5 5 2-2v1c1 1 1 1 1 2v1c-1 1-1 1-2 1l-2-1c-1 1 0 1-1 2v-1c-2-1-3-3-4-5-2-1-3-3-4-5z" class="H"></path><path d="M551 215v1c1 1 1 1 1 2l-1 1-2-2 2-2z" class="i"></path><path d="M541 197v1l2 2c0 1 2 2 2 2l-1 1v1c1 1 1 2 1 2 2 3 7 6 8 9h0c-1 0-1 0-2 1v-1l-2 2-5-5c-2-1-3-1-4-2 0-1-3-3-3-4 1 1 1 1 2 1l1-1-2-1v-1l-4-2c0-1 1-2 1-3l-1-1h1 1 0 1c1 0 1 1 3 0l1-1z" class="X"></path><path d="M534 202c0-1 1-2 1-3l4 5-1 1v-1l-4-2z" class="I"></path><path d="M541 197v1l2 2c0 1 2 2 2 2l-1 1v1c1 1 1 2 1 2-1 0-2-1-2-2-1-1 0-1-1-2-2-2-4-3-5-4 1 0 1 1 3 0l1-1z" class="P"></path><path d="M539 207l1-1c4 2 8 5 11 9l-2 2-5-5c-2-1-3-1-4-2 0-1-3-3-3-4 1 1 1 1 2 1z" class="W"></path><path d="M537 206c1 1 1 1 2 1 2 1 3 3 5 5-2-1-3-1-4-2 0-1-3-3-3-4z" class="K"></path><path d="M530 199c0 1 1 2 1 2 0 3 0 5 1 7v1l1-1 11 13 2 1h-1c-1 1-2 1-3 1-6 0-11-3-16-5 1 0 2 0 3-1l1-2h0v-11h-1l1-1v-4z" class="G"></path><path d="M533 208l11 13h0c-2 0-3 0-4-1-4-2-7-7-8-11l1-1z" class="P"></path><path d="M516 176l2 2 1-1 1 1v-1c1 1 2 2 4 3 0 1 2 3 3 3s3 1 4 2v1l-4-1c0 1 1 1 1 2 1 4 2 8 2 12v4l-1 1h1v11h0l-1 2c-1 1-2 1-3 1l-2-1-3-2v-2s-1 0-1-1h0v-1c1-1 0-1 1-2-1-1-1-1-3-1h0c-2 1-4-1-5-1l-1-1c0-2 0-2 1-4h1v-1l2-2 1-2c0-2 0-4-1-6 1-1 1-3 1-5 0 2 1 3 2 4h0 2s1 0 2 1h1c-3-5-5-7-9-9l1-1-1-1 1-1h0c1 1 1 1 2 1 0-2-2-1-2-4z" class="J"></path><path d="M514 201l1 1h1l1-1h1c0 1 0 2-1 3-2 0-2 0-4-2h1v-1z" class="D"></path><path d="M521 194c1 1 1 3 2 4v2c0 1 0 1 1 2l-1 2-1-1-1 1v-10z" class="G"></path><path d="M513 207l-1-1c0-2 0-2 1-4 2 2 2 2 4 2 0 1 0 2 1 3l3-3c-1 2-2 3-3 4h0c-2 1-4-1-5-1z" class="S"></path><path d="M519 190h2s1 0 2 1v1l1 1 1 4c0 1 2 3 3 4v3l-1 1v-1 1l-1-1h0v2c0-2 0-4-1-6-1 1-1 2-1 3v-1c-1-1-1-1-1-2v-2c-1-1-1-3-2-4l-2-4z" class="L"></path><path d="M523 192l1 1h-1v5c1 1 1 2 2 2-1 1-1 2-1 3v-1c-1-1-1-1-1-2v-2c0-2-1-4 0-6z" class="F"></path><path d="M519 190h2s1 0 2 1v1c-1 2 0 4 0 6-1-1-1-3-2-4l-2-4zm6 10c1 2 1 4 1 6 0 3-1 5-2 8v2 1l-3-2v-2s-1 0-1-1h0v-1c1-1 0-1 1-2-1-1-1-1-3-1 1-1 2-2 3-4h0l1-1 1 1 1-2v1c0-1 0-2 1-3z" class="c"></path><path d="M524 202v1c0 1 0 3-1 4v1l-1-1c0-1 0-2 1-3l1-2z" class="L"></path><path d="M523 208v-1c0 2 0 5 1 7v2 1l-3-2v-2c0-2 1-3 2-5z" class="b"></path><path d="M525 200c1 2 1 4 1 6 0 3-1 5-2 8-1-2-1-5-1-7 1-1 1-3 1-4s0-2 1-3z" class="Y"></path><defs><linearGradient id="Bi" x1="528.296" y1="189.104" x2="523.121" y2="191.851" xlink:href="#B"><stop offset="0" stop-color="#2b2a29"></stop><stop offset="1" stop-color="#47484a"></stop></linearGradient></defs><path fill="url(#Bi)" d="M516 176l2 2 1-1 1 1v-1c1 1 2 2 4 3 0 1 2 3 3 3s3 1 4 2v1l-4-1c0 1 1 1 1 2 1 4 2 8 2 12v4l-1 1h1v11h0l-1 2c-1 1-2 1-3 1l-2-1v-1-2c1-3 2-5 2-8v-2h0l1 1v-1 1l1-1v-3c-1-1-3-3-3-4l-1-4-1-1v-1h1c-3-5-5-7-9-9l1-1-1-1 1-1h0c1 1 1 1 2 1 0-2-2-1-2-4z"></path><path d="M528 204h1c1 2 1 8 1 11l-1 2c-1 1-2 1-3 1l-2-1v-1-2c1-3 2-5 2-8v-2h0l1 1v-1 1l1-1z" class="D"></path><path d="M524 216l3-3v2c1 1 1 0 2 1v1c-1 1-2 1-3 1l-2-1v-1z" class="S"></path><path d="M526 206v-2h0l1 1v-1 1c0 2-1 6 0 8h0l-3 3v-2c1-3 2-5 2-8z" class="a"></path><path d="M516 176l2 2 1-1 1 1c3 6 6 11 7 17 0 2 1 4 1 6-1-1-3-3-3-4l-1-4-1-1v-1h1c-3-5-5-7-9-9l1-1-1-1 1-1h0c1 1 1 1 2 1 0-2-2-1-2-4z" class="I"></path><path d="M524 191h0c1 1 2 2 2 3v3h-1l-1-4-1-1v-1h1z" class="G"></path><path d="M453 186h3c1-1 1-1 1-2v-1h1l1 1c1 1 2 3 2 4 1-1 1-2 2-2 2 2 4 6 7 6 0 1 1 1 2 1l7 5 4 3s0 1 1 2h-1l6 3c-2 0-6-1-8-3-1-1-3-3-5-4h-1v1l1 1c0 1 0 1 1 2 3 3 7 5 11 6 4 2 28 11 29 14l5 5c2 2 4 6 5 9h-1-1c-1-2-3-4-5-5l-1 1 4 3 2 1-1 1-4-1c-2 1-3 2-4 3l-1-1c-1-2-3-3-4-4-2-2-4-2-6-3 0-1-1-1-1-1-2-2-4-2-6-2-1 0-2-1-4-1-1 0-2-1-3-1-2-1-4-2-7-3-2-1-5-2-7-2l-1-1c0-2-2-2-4-3s-3-2-5-3h-1s0-1-1-1c-2-2-3-2-6-2h-1c1 0 1 0 1-1l-2-1c-2 1-6 1-8 0h-2l2-1 3-1h1c3-2 6-3 7-6-1-1-1-1-2-1v-4-1c-1 0-2-1-3-2-2 0-2 1-4 1v-3c1-1 2 0 2-2l-1-1c0-1-1 0-2 0v-1l1-1-1-1c1 0 1 0 2-1h-2 0 2 0l1 1z" class="j"></path><path d="M477 209c2 0 5 1 7 3-1 0-1 0-2-1h-1c-1 1-2 0-3 1-1 0-2-2-2-2h-2v-1h3z" class="Z"></path><path d="M487 215h2c1 0 1 1 2 1 2 1 5 3 7 3l1 1c-2 0-3 0-5-1s-5-2-7-4z" class="h"></path><path d="M478 212c1-1 2 0 3-1h1c1 1 1 1 2 1l7 4c-1 0-1-1-2-1h-2l-2-1c-2-1-4-2-6-1l-1-1z" class="V"></path><path d="M459 184c1 1 2 3 2 4l5 5c0 1 1 2 1 2v1l-1-1c0-1-1-2-3-3 1 2 3 4 3 5-2-1-3-4-4-6l-1-1-3-3c0-1 0-1 1-3z" class="M"></path><defs><linearGradient id="Bj" x1="480.789" y1="213.623" x2="492.159" y2="219.128" xlink:href="#B"><stop offset="0" stop-color="#585757"></stop><stop offset="1" stop-color="#8b898a"></stop></linearGradient></defs><path fill="url(#Bj)" d="M479 213c2-1 4 0 6 1l2 1c2 2 5 3 7 4l-1 1h0v1h0s-1 0-1-1h-1 0c-1-1-2-1-3-1l-9-6z"></path><path d="M498 219c3 0 4 1 6 1 4 1 7 4 10 5h1c1 1 2 1 3 2l3 1c-1-1-3-3-4-5l5 5h-1-3c0-1-1-1-1-1-2-1-4-2-5-3h-4l-3-1-6-3-1-1z" class="S"></path><path d="M508 224h4c1 1 3 2 5 3 0 0 1 0 1 1h3 1c2 2 4 6 5 9h-1-1c-1-2-3-4-5-5-3-3-8-5-12-8z" class="T"></path><path d="M470 205h1c0 1-1 1-2 1l-2 1h1 0c1 0 1 0 2-1h2c1 0 1 1 2 1l1 1 2 1h-3-6v-1l-1 1 1 1 2 3 1 1h-4s-1 0-1 1c0 0 0-1-1-1-2-2-3-2-6-2h-1c1 0 1 0 1-1l-2-1 2-2c1 0 3 0 4-1 0 0 1 0 1-1h3l3-1z" class="J"></path><path d="M468 208h0c2-2 5-1 7 0l2 1h-3-6v-1z" class="B"></path><path d="M467 209l1 1 2 3 1 1h-4s-1 0-1 1c0 0 0-1-1-1-2-2-3-2-6-2 0 0 1 0 2-1h0v-1c1 0 1-1 2 0h0c1 0 3 0 4-1z" class="k"></path><path d="M467 209l1 1 2 3h-1c-2-1-4-1-6-1-1-1-1-1-1-2h1 0c1 0 3 0 4-1z" class="T"></path><defs><linearGradient id="Bk" x1="494.881" y1="217.041" x2="507.179" y2="232.539" xlink:href="#B"><stop offset="0" stop-color="#8d8b8f"></stop><stop offset="1" stop-color="#bdbbb8"></stop></linearGradient></defs><path fill="url(#Bk)" d="M494 219c2 1 3 1 5 1l6 3 3 1c4 3 9 5 12 8l-1 1c-2-2-5-3-8-5l-22-5c1 0 1-1 1-1 1-1 1-1 3-1h0v-1h0l1-1z"></path><path d="M494 219c2 1 3 1 5 1l6 3c-3 0-9 0-12-2v-1h0l1-1z" class="o"></path><defs><linearGradient id="Bl" x1="471.011" y1="185.59" x2="474.854" y2="204.114" xlink:href="#B"><stop offset="0" stop-color="#676566"></stop><stop offset="1" stop-color="#979797"></stop></linearGradient></defs><path fill="url(#Bl)" d="M461 188c1-1 1-2 2-2 2 2 4 6 7 6 0 1 1 1 2 1l7 5 4 3s0 1 1 2h-1l6 3c-2 0-6-1-8-3-1-1-3-3-5-4h-1v1l1 1c0 1 0 1 1 2-4-2-7-5-10-8 0 0-1-1-1-2l-5-5z"></path><path d="M460 199v-1c1 0 1 0 2 1v1h3c1 1 2 2 4 2h0c1 0 2 1 2 3h-1l-3 1h-3c0 1-1 1-1 1-1 1-3 1-4 1l-2 2c-2 1-6 1-8 0h-2l2-1 3-1h1c3-2 6-3 7-6v-3z" class="e"></path><path d="M459 208h0v-1c1 0 2-1 3-1s1 0 1-1l1 1c0 1-1 1-1 1-1 1-3 1-4 1z" class="D"></path><path d="M465 200c1 1 2 2 4 2h0c1 0 2 1 2 3h-1l-3 1h-3l-1-1 2-2-3-3h3z" class="C"></path><path d="M465 203h2c1 1 2 1 3 1v1l-3 1h-3l-1-1 2-2z" class="O"></path><path d="M465 203h2l-2 2v1h2-3l-1-1 2-2z" class="E"></path><defs><linearGradient id="Bm" x1="470.173" y1="217.226" x2="486.153" y2="213.388" xlink:href="#B"><stop offset="0" stop-color="#414141"></stop><stop offset="1" stop-color="#767476"></stop></linearGradient></defs><path fill="url(#Bm)" d="M468 208v1h6v1h2s1 2 2 2l1 1 9 6c1 0 2 0 3 1h0 1c0 1 1 1 1 1-2 0-2 0-3 1 0 0 0 1-1 1l-2-1c-3-1-6-2-9-4-2-1-4-2-6-4h-1l-1-1-2-3-1-1 1-1z"></path><path d="M468 210c2 1 3 1 4 3v1h-1l-1-1-2-3z" class="E"></path><path d="M491 220h1c0 1 1 1 1 1-2 0-2 0-3 1 0 0 0 1-1 1l-2-1 1-1c0-1 2 0 3-1z" class="F"></path><path d="M488 219h-2c-2-1-12-8-13-9h1 2s1 2 2 2l1 1 9 6z" class="M"></path><path d="M453 186h3c1-1 1-1 1-2v-1h1l1 1c-1 2-1 2-1 3l3 3 1 1c1 2 2 5 4 6l3 5h0c-2 0-3-1-4-2h-3v-1c-1-1-1-1-2-1v1 3c-1-1-1-1-2-1v-4-1c-1 0-2-1-3-2-2 0-2 1-4 1v-3c1-1 2 0 2-2l-1-1c0-1-1 0-2 0v-1l1-1-1-1c1 0 1 0 2-1h-2 0 2 0l1 1z" class="B"></path><path d="M456 192h1c1 0 2 1 2 2v1c-2 0-2-1-3-1v-2z" class="T"></path><path d="M457 190l2 1c0 1 1 1 2 1 0 0-1-1 0-2l1 1c-1 1-1 1-1 2 1 1 1 2 1 3l-3-2c0-1-1-2-2-2h-1v-1c1 0 1 0 1-1z" class="V"></path><path d="M459 194l3 2 3 4h-3v-1c-1-1-1-1-2-1v1h-1v-1l1-1h0l-1-2v-1z" class="D"></path><path d="M462 191c1 2 2 5 4 6l3 5h0c-2 0-3-1-4-2l-3-4c0-1 0-2-1-3 0-1 0-1 1-2z" class="h"></path><path d="M453 186h3c1-1 1-1 1-2v-1h1l1 1c-1 2-1 2-1 3l3 3c-1 1 0 2 0 2-1 0-2 0-2-1l-2-1h0-1c-1-1-3-1-3-3v-1z" class="Z"></path><path d="M466 215c0-1 1-1 1-1h4 1c2 2 4 3 6 4 3 2 6 3 9 4l2 1 22 5c3 2 6 3 8 5l4 3 2 1-1 1-4-1c-2 1-3 2-4 3l-1-1c-1-2-3-3-4-4-2-2-4-2-6-3 0-1-1-1-1-1-2-2-4-2-6-2-1 0-2-1-4-1-1 0-2-1-3-1-2-1-4-2-7-3-2-1-5-2-7-2l-1-1c0-2-2-2-4-3s-3-2-5-3h-1z" class="j"></path><path d="M511 235h4c1 1 2 2 3 2v-1h0l2 1h0c-2 1-3 2-4 3l-1-1c-1-2-3-3-4-4z" class="e"></path><path d="M511 228c3 2 6 3 8 5l4 3 2 1-1 1-4-1h0l-2-1h0c-2-2-4-3-6-4l1-1 2 2h0 1v-1c-1-1-2-2-3-2l-2-2z" class="Z"></path><path d="M518 236h5l2 1-1 1-4-1h0l-2-1z" class="C"></path><path d="M469 170h3c2 1 4 1 6 1l10 3c1 1 2 3 3 3l3 2c1-1 1 0 2 0l1-1 3 2h3l-2-2c3 2 8 4 11 6v2l2 2c1 2 1 3 2 3 1 2 1 4 1 6l-1 2-2 2v1h-1c-1 2-1 2-1 4l1 1h-2 0l-1-1c-1-1-2-2-4-3s-6-2-8-3c-1-1-1-1-1-2l-1-1s0 1-1 1v-1l-1 1h-1l1 1h-2l3 2c0 1 0 1 1 2l-1 1h1c0 1 0 1 1 2h0c-2 0-14-6-16-8h-2l-7-5c-1 0-2 0-2-1-3 0-5-4-7-6-1 0-1 1-2 2 0-1-1-3-2-4l-1-1h-1v1c0 1 0 1-1 2h-3l-1-1h0-2l1-1h1l-1-1h0l-4-2c2-1 4-1 5-1 1-2 2-2 3-2l1-1h0l1-1-1-1c1 0 2 0 3-1l1-2c1 0 1 0 2-1h1c2 0 4 0 6-1z" class="k"></path><path d="M472 187c2-1 2-1 4-1v1l-2 1-2-1z" class="D"></path><path d="M483 194c2-1 3 0 5 0l4 2c1 1 1 1 1 2l1 1h-2c-3-1-6-3-9-5z" class="B"></path><path d="M482 191c1 0 3 1 4 0v-1c-1-1-1-1-2-1l1-1v-2l1 1h0c2 3 6 4 5 8 1 0 1 1 1 1l-4-2c-2 0-3-1-5 0-1-1-4-2-5-4 1 0 2 0 3 1h1z" class="Z"></path><path d="M483 194c-1-1-4-2-5-4 1 0 2 0 3 1h1c1 1 4 1 5 2 0 0 1 0 1 1 2 0 2 1 3 1s1 1 1 1l-4-2c-2 0-3-1-5 0z" class="N"></path><path d="M487 183l2 2c1 0 1-1 2-1l2 4h1c1 0 2 1 3 2l-1 1v1c0 1 1 3 1 4v2l-1-1s0 1-1 1v-1l-1 1h-1c0-1 0-1-1-2 0 0 0-1-1-1 1-4-3-5-5-8h0v-1h1l-1-3h1z" class="o"></path><path d="M493 188h1c1 0 2 1 3 2l-1 1v1l-1-1-1 1-1-4z" class="Y"></path><path d="M494 192l1-1 1 1c0 1 1 3 1 4v2l-1-1c-1-2-1-3-2-5z" class="Z"></path><defs><linearGradient id="Bn" x1="486.199" y1="185.902" x2="494.505" y2="195.497" xlink:href="#B"><stop offset="0" stop-color="#292829"></stop><stop offset="1" stop-color="#444342"></stop></linearGradient></defs><path fill="url(#Bn)" d="M487 183l2 2 6 12-1 1h-1c0-1 0-1-1-2 0 0 0-1-1-1 1-4-3-5-5-8h0v-1h1l-1-3h1z"></path><path d="M469 187v-1h3v1l2 1 2 3c6 5 12 10 19 13h1c0 1 0 1 1 2h0c-2 0-14-6-16-8h-2l-7-5-1-1 1-1c-1-1-2-3-3-4z" class="F"></path><path d="M469 187v-1h3v1l2 1 2 3h-2-1c2 3 5 5 8 7h-2l-7-5-1-1 1-1c-1-1-2-3-3-4z" class="O"></path><path d="M463 181c3 0 6 1 9 0h2v2c1 0 2-1 3-1 3 1 6 2 9 4v1l-1-1v2l-1 1c1 0 1 0 2 1v1c-1 1-3 0-4 0h-1c1-1 2-1 3-1v1l1-1s0-1-1-1v-1-1c-2-1-5-2-7-2v1h-5 0-3v1c1 1 2 3 3 4l-1 1 1 1c-1 0-2 0-2-1-3 0-5-4-7-6 0-1-1-2-1-3l1-1v-1z" class="C"></path><path d="M469 187c-1-1-1-2-2-4h1l1 1 3 2h0-3v1z" class="R"></path><path d="M469 184l1-1h1l6 2v1h-5l-3-2z" class="e"></path><defs><linearGradient id="Bo" x1="461.349" y1="185.49" x2="471.353" y2="188.744" xlink:href="#B"><stop offset="0" stop-color="#4f4e4f"></stop><stop offset="1" stop-color="#696868"></stop></linearGradient></defs><path fill="url(#Bo)" d="M463 182c3 3 5 6 8 10l1 1c-1 0-2 0-2-1-3 0-5-4-7-6 0-1-1-2-1-3l1-1z"></path><path d="M455 178c1 0 1 0 1 1h1c9-1 20 1 29 4l1 3h-1c-3-2-6-3-9-4-1 0-2 1-3 1v-2h-2c-3 1-6 0-9 0v1l-1 1c0 1 1 2 1 3-1 0-1 1-2 2 0-1-1-3-2-4l-1-1h-1v1c0 1 0 1-1 2h-3l-1-1h0-2l1-1h1l-1-1h0l-4-2c2-1 4-1 5-1 1-2 2-2 3-2z" class="S"></path><path d="M455 178c1 0 1 0 1 1h1c-2 1-3 1-5 1 1-2 2-2 3-2z" class="V"></path><defs><linearGradient id="Bp" x1="462.757" y1="182.956" x2="455.421" y2="185.545" xlink:href="#B"><stop offset="0" stop-color="#5a595a"></stop><stop offset="1" stop-color="#727272"></stop></linearGradient></defs><path fill="url(#Bp)" d="M451 183c2 0 3-1 5-2h7v1l-1 1c0 1 1 2 1 3-1 0-1 1-2 2 0-1-1-3-2-4l-1-1h-1v1c0 1 0 1-1 2h-3l-1-1h0-2l1-1h1l-1-1h0z"></path><path d="M451 183c2 0 2 0 3 1v1h-2 0-2l1-1h1l-1-1z" class="H"></path><path d="M469 170h3c2 1 4 1 6 1l10 3c1 1 2 3 3 3l3 2c1-1 1 0 2 0l1-1 3 2c2 1 4 2 5 3l-1 1c-1-1-2-2-3-2l-1 1c1 2 1 3 1 5-2-2-6-4-9-6l3 3c-1 1-1 2-1 3h-1l-2-4c-1 0-1 1-2 1l-2-2h-1c-9-3-20-5-29-4h-1c0-1 0-1-1-1l1-1h0l1-1-1-1c1 0 2 0 3-1l1-2c1 0 1 0 2-1h1c2 0 4 0 6-1z" class="B"></path><path d="M477 177c-1 0-3-1-5-1h-3 3 2 0 0c-2 0-3 0-5-1 3 0 6 0 9 1h0l-1 1z" class="C"></path><path d="M469 170h3c2 1 4 1 6 1-1 0-1 1-2 1h-1l-1 1h1c0 1-1 2-1 2h-7c-4 1-7 2-11 2h0l1-1-1-1c1 0 2 0 3-1l1-2c1 0 1 0 2-1h1c2 0 4 0 6-1z" class="h"></path><path d="M459 174l1-2 1 1h1 1l3-1c2 0 3 0 4 1-4 1-8 2-13 3l-1-1c1 0 2 0 3-1z" class="V"></path><path d="M459 174l1-2 1 1h1 1c-2 1-3 1-4 1z" class="h"></path><defs><linearGradient id="Bq" x1="472.153" y1="168.256" x2="465.473" y2="175.799" xlink:href="#B"><stop offset="0" stop-color="#4d4f4d"></stop><stop offset="1" stop-color="#686468"></stop></linearGradient></defs><path fill="url(#Bq)" d="M469 170h3c2 1 4 1 6 1-1 0-1 1-2 1h-1l-1 1h-4c-1-1-2-1-4-1l-3 1h-1-1l-1-1c1 0 1 0 2-1h1c2 0 4 0 6-1z"></path><path d="M478 176c1 0 3 0 4-1 3 0 5 1 7 2h0c1 0 2 1 3 1l-1-1 3 2c1-1 1 0 2 0l1-1 3 2c2 1 4 2 5 3l-1 1c-1-1-2-2-3-2l-1 1c1 2 1 3 1 5-2-2-6-4-9-6l3 3c-1 1-1 2-1 3h-1l-2-4c-1 0-1 1-2 1l-2-2c-1-1-1-2-2-3h1c-2-2-6-3-9-3l1-1h0z" class="E"></path><path d="M486 180l1-1c2 1 3 2 5 3l-1 2c-1-2-3-3-5-4z" class="C"></path><path d="M486 180h0c2 1 4 2 5 4h0c-1 0-1 1-2 1l-2-2c-1-1-1-2-2-3h1z" class="I"></path><path d="M489 177h0c1 0 2 1 3 1l-1-1 3 2c1-1 1 0 2 0l1-1 3 2c2 1 4 2 5 3l-1 1c-1-1-2-2-3-2l-1 1h-1-1-1c-1 0-1 0-2-1-2-2-4-3-6-5z" class="V"></path><path d="M498 183c-1 0-1 0-2-1l1-1c1 1 1 2 2 2h-1z" class="B"></path><path d="M494 179c1-1 1 0 2 0 1 1 3 2 5 3l-1 1h-1c-1 0-1-1-2-2l-3-2z" class="M"></path><path d="M497 178l3 2c2 1 4 2 5 3l-1 1c-1-1-2-2-3-2-2-1-4-2-5-3l1-1z" class="Z"></path><path d="M501 178c3 2 8 4 11 6v2l2 2c1 2 1 3 2 3 1 2 1 4 1 6l-1 2-2 2v1h-1c-1 2-1 2-1 4l1 1h-2 0l-1-1c-1-1-2-2-4-3s-6-2-8-3c-1-1-1-1-1-2v-2c0-1-1-3-1-4v-1l1-1c-1-1-2-2-3-2 0-1 0-2 1-3l-3-3c3 2 7 4 9 6 0-2 0-3-1-5l1-1c1 0 2 1 3 2l1-1c-1-1-3-2-5-3h3l-2-2z" class="D"></path><path d="M505 193c0-3-2-6-3-9 2 2 3 3 4 5 1 1 2 3 3 4v1c1 2 1 4 1 6-1-1-1-2-2-2l-2-3c0-1 0-2-1-2z" class="c"></path><path d="M505 183h0l3 4c1 1 2 3 2 5l3 2v2h1c0 2 0 2-1 3v1s0 1-1 1-1 0-2-1h0c0-2 0-4-1-6v-1c-1-4-3-6-5-9l1-1z" class="F"></path><path d="M510 192l3 2v2h1c0 2 0 2-1 3-1-3-2-5-3-7z" class="G"></path><defs><linearGradient id="Br" x1="503.48" y1="178.622" x2="511.705" y2="191.233" xlink:href="#B"><stop offset="0" stop-color="#252324"></stop><stop offset="1" stop-color="#4d4e4e"></stop></linearGradient></defs><path fill="url(#Br)" d="M501 178c3 2 8 4 11 6 0 1-1 2-1 3h0v1c1 0 1 1 1 1l1 5-3-2c0-2-1-4-2-5l-3-4h0c-1-1-3-2-5-3h3l-2-2z"></path><path d="M505 183l1-1c1 1 2 1 2 2v3l-3-4z" class="M"></path><path d="M513 194l-1-5s0-1-1-1v-1h0c0-1 1-2 1-3v2l2 2c1 2 1 3 2 3 1 2 1 4 1 6l-1 2-2 2v1l-2-1c1 0 1-1 1-1v-1c1-1 1-1 1-3h-1v-2z" class="G"></path><path d="M514 196h2c1 0 1 0 1 1l-1 2c-1-1-2-1-2-3z" class="F"></path><path d="M514 196c0 2 1 2 2 3l-2 2v1l-2-1c1 0 1-1 1-1v-1c1-1 1-1 1-3h0z" class="b"></path><path d="M513 194l-1-5s0-1-1-1v-1h0c0-1 1-2 1-3v2c0 2 1 5 2 7v3h0-1v-2z" class="E"></path><path d="M495 185l-3-3c3 2 7 4 9 6l1 1v1l3 3h0c1 0 1 1 1 2l2 3c1 0 1 1 2 2h0c1 1 1 1 2 1l2 1h-1c-1 2-1 2-1 4l1 1h-2 0l-1-1c-1-1-2-2-4-3s-6-2-8-3c-1-1-1-1-1-2v-2c0-1-1-3-1-4v-1l1-1c-1-1-2-2-3-2 0-1 0-2 1-3z" class="I"></path><path d="M495 185c2 2 3 3 4 5-1 1-1 1-2 1v-1c-1-1-2-2-3-2 0-1 0-2 1-3z" class="C"></path><path d="M502 189v1l3 3h0c1 0 1 1 1 2l2 3-2-1c0-1-1-2-2-2s-1 0-2-1v-5z" class="h"></path><path d="M499 190l3 8 1 1h-3l-3-3c0-1-1-3-1-4v-1l1-1v1c1 0 1 0 2-1z" class="R"></path><path d="M496 192v-1l1-1v1c1 3 3 5 4 8h-1l-3-3c0-1-1-3-1-4z" class="G"></path><path d="M504 195c1 0 2 1 2 2v2 4c-2-1-6-2-8-3-1-1-1-1-1-2v-2l3 3h3l-1-1h2v-3z" class="D"></path><path d="M504 195c1 0 2 1 2 2v2c0 1 0 2-1 3-1-1-1-2-2-3l-1-1h2v-3z" class="c"></path><path d="M506 197l2 1c1 0 1 1 2 2h0c1 1 1 1 2 1l2 1h-1c-1 2-1 2-1 4l1 1h-2 0l-1-1c-1-1-2-2-4-3v-4-2z" class="J"></path><path d="M510 200c1 1 1 1 2 1l2 1h-1c-1 2-1 2-1 4l1 1h-2 0l-1-1v-6z" class="h"></path><path d="M500 33l1 1c-1 1-1 1-1 2h1c3 4 4 10 5 14v3c0 1 0 1-1 1l3-1v1l1 1v1c0 5-1 10-1 15-1 5 0 9-1 13 0 4-2 8-3 11-2 8-2 16 0 24h0 1v1c0 2 0 2 1 4l2 3 2 2s1 1 1 2l1 1 1 1 3 3-4-1h-3-2v1l1 1h0v4 1l-1 1h-1l-3 1h-2v2h0c-1 0-2 1-3 2h0-2c-1-1-3-1-4-2-1 0-2 0-2-1l-1 2h0-4v-1c-2 1-4 1-6 2h-3l-4 1h-4v-1l-4-1h-1-4c0-1 0-1-1-2-2 0-4-1-6-2l-1 1c-1 0-1 0-2-1v1l1 1h-1 0v1h-4c-1 1-2 1-3 2l-1-1h0-1v-1h-1c-1 0-1 1-2 2h0s-1-1-1-2v-2l1-1h0c-2-3-1-8-2-11 0 1 0 1-1 1v-6c0-3 2-5 1-8h0l1-1 1-1-1-1-1 1h0-1-2c1-2 2-3 3-4h-1v-1c1-2 3-3 3-5l5-5c1-1 2-1 3-1 0 1-1 2-2 2v1l-4 3h2l1-1c1 0 2-2 4-3h0l1-1c1-1 2-1 2-2h1l4-2-4 3h2 1l1-1c2-1-1 1 1-1 2-1 4-2 5-3h1l1-1h0c1 0 1-1 2-1 2-1 1-1 3-2v-1l1-1c1 0 1 0 1-1h1v-1h1c0-1 1-1 1-2l1 1c1 0 1-2 2-2 1-3 2-5 3-7s2-4 3-7c1-1 2-3 3-5v1s-1 2-1 3c-2 2-3 4-3 6-1 1-1 2-1 3 1 1 1 0 2 1 0-1 0-1 1-2l2-4c1 0 1-1 0-2h0c1-1 1-1 1-2v-1-1l1-2v-1l1 1c1-1 1-3 2-4 3-6 5-12 7-18 1-3 2-8 4-11z" class="g"></path><path d="M456 138h2 1c0 1-1 1-1 2h-1l-5-2h4z" class="D"></path><path d="M479 137v1l2-1 1 1c-1 1-2 2-4 3l-1-1h1c1-1 1-1 1-2h-1l1-1z" class="J"></path><path d="M459 138c1 1 2 2 3 2h5l-2 1c-1 0-3-1-4-1-1 1 0 1-2 1-1 0-1 0-2-1h1c0-1 1-1 1-2z" class="S"></path><path d="M485 117h0c-1 1-3 2-4 2 0-1 1-1 1-2v-1-1c1-1 2-1 3-2v3 1z" class="e"></path><path d="M500 73l-1-1-3 3c1-3 4-6 6-9 0 3 0 6-2 7z" class="J"></path><path d="M475 133c0-1 1-3 2-4l2 2c0 2-2 3-3 5l-1-3z" class="Y"></path><path d="M452 123l1 1-2 2c-1 0-1 1-2 1v2 1-1h-1v3 2l-1-1v-2h0v-3l-1 1v-2l3-3c1 0 2 0 3-1z" class="J"></path><path d="M446 127l3-3v1 2c-1 0-1 1-2 1l-1 1v-2z" class="C"></path><path d="M505 56v1h0v4 4h1 0c-1 2-1 4-2 6-1-2-1-6-1-8 1-1 1-3 1-4s0-2 1-3z" class="N"></path><path d="M448 134v-2-3h1v1h0c0 1 1 2 1 2 0 1-1 1 0 2s4 3 6 4h-4c-2 0-3-2-4-4z" class="C"></path><path d="M456 122c0 1 1 2 2 3l-1 1v1c2 1 4 2 7 4-2-1-5-2-7-2l-1 1c0-1-1-1-2-1 1-1 0-1 1-1h1 0c-1-1-2-1-2-2l-1-1h1c0-1 1-2 2-3z" class="J"></path><path d="M505 54l3-1v1 2c-1 3-1 6-2 9h0-1v-4-4h0v-1h-1v-1l1-1z" class="G"></path><path d="M505 54l3-1v1 2h-4v-1l1-1z" class="q"></path><path d="M502 66c0-1 1-2 1-3 0 2 0 6 1 8-1 3-5 7-8 9l4-7c2-1 2-4 2-7z" class="k"></path><path d="M470 145h1c1 0 2-1 4 0l1 2h2l1 1h-3l-4 1h-4v-1l-4-1h7 0c-1 0-2-1-3-1l1-1h1z" class="M"></path><path d="M476 147h2l1 1h-3l-4 1h-4v-1c1-1 3 0 4 0s3-1 4-1z" class="N"></path><path d="M470 133h0c-1 3-4 4-7 5v1h-1v1c-1 0-2-1-3-2h-1 1v-1-2h1c0-1 1-1 2-1h0 3 1v1h1l3-2z" class="V"></path><path d="M459 138h4v1h-1v1c-1 0-2-1-3-2z" class="D"></path><path d="M462 134h0 3 1v1h1c-2 1-3 1-5 2h-3v-2h1c0-1 1-1 2-1z" class="k"></path><path d="M457 116c1-1 1 0 2-1h1c1 0 1 1 1 1-1 1-2 2-3 4l-2 2c-1 1-2 2-2 3h-1l-4 4v-2c1 0 1-1 2-1l2-2-1-1s-1 0-1-1h-1l3-3c2-1 3-2 4-3z" class="C"></path><path d="M457 116c1-1 1 0 2-1h1c1 0 1 1 1 1-1 1-2 2-3 4-1 0-2 1-2 0h-1l2-3v-1z" class="R"></path><path d="M470 133l1-1c0 1 1 1 1 2l1 1h1l1-2 1 3c-2 2-6 3-9 4h-5v-1h1v-1c3-1 6-2 7-5h0z" class="T"></path><path d="M472 134l1 1h1 0 1c-1 2-3 2-5 3h0v-1h0 0 0c1-1 2-2 2-3z" class="j"></path><path d="M470 133l1-1c0 1 1 1 1 2s-1 2-2 3h0 0 0v1h0c-2 1-4 1-7 1v-1c3-1 6-2 7-5h0z" class="J"></path><path d="M468 146s-1-1-2-1c-2-1-5-1-7-2-2 0-3 0-4-1 1-1 3-1 5-1 0 1 1 1 1 1 1-1 2-1 3-1l1 1h2c4 0 7-1 10-2l1 1c-1 2-1 2-3 3v1c-2-1-3 0-4 0h-1-1l-1 1z" class="S"></path><path d="M470 145c1-1 1-1 2-1 1-1 2-1 3 0v1c-2-1-3 0-4 0h-1z" class="J"></path><defs><linearGradient id="Bs" x1="492.851" y1="62.459" x2="484.2" y2="66.715" xlink:href="#B"><stop offset="0" stop-color="#807e81"></stop><stop offset="1" stop-color="#969493"></stop></linearGradient></defs><path fill="url(#Bs)" d="M500 33l1 1c-1 1-1 1-1 2h1c-2 2-3 7-4 10-1 4-3 9-4 13-2 4-3 7-5 11 0 0-1 2-2 2 0-1-1-1-1-1 0-2 1-3 2-5 1-1 1-3 2-4 3-6 5-12 7-18 1-3 2-8 4-11z"></path><path d="M453 125l1 1c0 1 1 1 2 2h0-1c-1 0 0 0-1 1 1 0 2 0 2 1l3 2c2 0 2 1 3 2-1 0-2 0-2 1h-1v2 1h-1-2c-2-1-5-3-6-4s0-1 0-2c0 0-1-1-1-2h0v-1l4-4z" class="S"></path><path d="M454 129c1 0 2 0 2 1l3 2-1 1s0 1-1 1c-1-1-1-2-3-2l-1-1 1-2h0z" class="Z"></path><path d="M450 132l1 1c1 1 2 2 3 2h3 0c1-1 2 0 3 0h-1v2 1h-1-2c-2-1-5-3-6-4s0-1 0-2z" class="B"></path><path d="M454 135h3 0c1-1 2 0 3 0h-1-1v1 1c-2 0-2-1-4-2z" class="C"></path><path d="M453 125l1 1c0 1 1 1 2 2h0-1c-1 0 0 0-1 1h0l-1 2c-1 1-1 2-2 2l-1-1s-1-1-1-2h0v-1l4-4z" class="g"></path><path d="M454 129l-1 2c-1 1-1 2-2 2l-1-1s-1-1-1-2l1 1c2 0 3-1 4-2z" class="j"></path><path d="M441 139h0l1-1c0-1 0-2 1-3v-1 3 1c1 2 3 2 5 2 1 1 3 2 4 3l-1 1c-1 0-1 0-2-1v1l1 1h-1 0v1h-4c-1 1-2 1-3 2l-1-1h0-1v-1h-1c-1 0-1 1-2 2h0s-1-1-1-2v-2l1-1h0v-1c1-1 2-2 2-3h1 1z" class="J"></path><path d="M437 143h2c1-1 0-2 2-1 0 1 0 1-1 2v1 1h-1c-1 0-1 1-2 2h0s-1-1-1-2v-2l1-1z" class="T"></path><path d="M448 140c1 1 3 2 4 3l-1 1c-1 0-1 0-2-1v1h-2c-1 0-1-1-2-2h0 1l2-2z" class="k"></path><path d="M441 147h0c1-1 1-2 1-3v-1c2 2 5 2 7 2v1h-4c-1 1-2 1-3 2l-1-1z" class="N"></path><path d="M492 106s-1 0-2 1v-1l2-3 1-1c2-3 4-6 5-10 1-3 5-10 3-13v-1c0-2 2-3 3-4 1 2 0 8 0 10-1 3-2 7-2 10 0 1-1 2-1 3-2 2-2 5-3 7s-3 4-4 5c-1 2-7 6-7 6l-2 2v-1-3c1-1 3-2 4-3l-1-1c2 0 3-1 4-2v-1z" class="D"></path><path d="M492 106v-1c1 0 2-2 3-2h1 0c0 1-2 3-3 3l-1 1v-1z" class="J"></path><path d="M479 137c2-1 4-3 6-4 1 0 1 0 2-1h0c-1-1-2-1-3-2-3-1-5-4-5-6v-1l1 1c1 2 2 4 4 5 1 1 3 1 4 2h2v1 1s0 1 1 1c1 1 1 1 2 3 0 1 0 2 1 3 1-1 1-1 1 0h2c2 1 3 1 5 2h-3l-1 1h0l3 1v2h0c-1 0-2 1-3 2h0-2c-1-1-3-1-4-2-1 0-2 0-2-1l-1 2h0-4v-1c-2 1-4 1-6 2l-1-1h-2l-1-2v-1c2-1 2-1 3-3 2-1 3-2 4-3l-1-1-2 1v-1z" class="S"></path><path d="M494 140c1-1 1-1 1 0h2c2 1 3 1 5 2h-3l-1 1h0c-2-1-3-2-4-3z" class="N"></path><path d="M488 141c1 0 1-1 2 0 2 0 4 1 6 2 0 1 0 1-1 2h-1c-2-1-4-2-6-4z" class="M"></path><path d="M493 137h-2l-2 1c0 1-1 1-2 0-1 0-2 0-2-1 0-3 3-3 5-5v1s0 1 1 1c1 1 1 1 2 3zm-11 1h2 0c1 1 3 3 4 3 2 2 4 3 6 4h1c1 1 1 1 2 1 1 1 1 1 1 2h-2c-1-1-3-1-4-2-1 0-2 0-2-1l-1 2h0-4v-1c-2 1-4 1-6 2l-1-1h-2l-1-2v-1c2-1 2-1 3-3 2-1 3-2 4-3z" class="g"></path><path d="M478 147l7-3v1 1c-2 1-4 1-6 2l-1-1z" class="C"></path><path d="M473 87c1 0 1-2 2-2 1-3 2-5 3-7s2-4 3-7c1-1 2-3 3-5v1s-1 2-1 3c-2 2-3 4-3 6-1 1-1 2-1 3 1 1 1 0 2 1 0-1 0-1 1-2l2-4c1 0 1-1 0-2h0c1-1 1-1 1-2v-1-1l1-2v-1l1 1c-1 2-2 3-2 5 0 0 1 0 1 1-2 3-4 7-6 10-1 2-2 4-3 5-2 3-2 7-3 11-1 3-3 6-5 8l-1 2-2 2-5 6s0-1-1-1h-1c-1 1-1 0-2 1s-2 2-4 3l-3 3h1c0 1 1 1 1 1-1 1-2 1-3 1l-3 3-1-1c1-1 2-2 2-3l-2 1v-2h-1v-1c-1 0-2-1-3-1l-1-1 3-3 8-6 8-7 2-2c2-1 8-8 9-10 2-1 2-3 3-4z" class="p"></path><path d="M477 87v-1h0c-1-2 1-4 2-6h0c0 1 0 1 1 2h0c-1 2-2 4-3 5z" class="W"></path><path d="M469 100c1-1 2-5 3-6l1 1c-1 3-1 4-3 6h0l-1-1z" class="T"></path><path d="M473 95v2c0 1 1 1 1 1-1 3-3 6-5 8l-1 2-1-1v-2c-1-1 0-2 0-3l2-2 1 1h0c2-2 2-3 3-6z" class="P"></path><path d="M467 102l2-2 1 1c-1 2-2 3-2 4l1 1-1 2-1-1v-2c-1-1 0-2 0-3z" class="H"></path><path d="M467 102c0 1-1 2 0 3v2l1 1-2 2c-1 0-2 0-3 1s-2 2-4 3l-1-1s0-1 1-1l-1-1s1-2 2-2c0-1 1-1 1-2 2-1 4-3 6-5z" class="f"></path><path d="M467 105v2l1 1-2 2c-1 0-2 0-3 1 1-2 2-3 3-5l1-1z" class="F"></path><path d="M467 102c0 1-1 2 0 3l-1 1c-2 0-4 2-5 3v1l-2 2-1-1s1-2 2-2c0-1 1-1 1-2 2-1 4-3 6-5z" class="K"></path><path d="M460 109c-1 0-2 2-2 2l1 1c-1 0-1 1-1 1l1 1c2-1 3-2 4-3s2-1 3-1l-5 6s0-1-1-1h-1c-1 1-1 0-2 1s-2 2-4 3l-3 3h1c0 1 1 1 1 1-1 1-2 1-3 1l-3 3-1-1c1-1 2-2 2-3l-2 1v-2h-1l1-1-1-2c3-3 10-8 13-9 1 0 2-1 3-1z" class="B"></path><path d="M460 109c-1 0-2 2-2 2-1 2-3 2-4 3l-9 7c2 0 3 1 4 1v2l-2-1-2 1v-2h-1l1-1-1-2c3-3 10-8 13-9 1 0 2-1 3-1z" class="f"></path><path d="M445 122c1 0 1 0 2 1l-2 1v-2z" class="G"></path><path d="M443 103v1l-4 3h2l1-1c1 0 2-2 4-3h0l1-1c1-1 2-1 2-2h1l4-2-4 3h2 1l1-1c2-1-1 1 1-1 2-1 4-2 5-3h1l1-1h0c1 0 1-1 2-1 2-1 1-1 3-2v-1l1-1c1 0 1 0 1-1h1v-1h1c0-1 1-1 1-2l1 1c-1 1-1 3-3 4-1 2-7 9-9 10l-2 2-8 7-8 6-3 3 1 1c1 0 2 1 3 1v1h1v2l2-1c0 1-1 2-2 3l1 1v2l-1 1-1 2v1h-1v1 1c-1 1-1 2-1 3l-1 1h0-1-1c0 1-1 2-2 3v1c-2-3-1-8-2-11 0 1 0 1-1 1v-6c0-3 2-5 1-8h0l1-1 1-1-1-1-1 1h0-1-2c1-2 2-3 3-4h-1v-1c1-2 3-3 3-5l5-5c1-1 2-1 3-1 0 1-1 2-2 2z" class="K"></path><path d="M456 101h1l1-1h1c1 0 1-1 1-1h1c3-2 6-8 9-8-1 2-7 9-9 10v-1c-1 1-1 1-2 1 0 1-2 2-3 1h0v-1z" class="o"></path><path d="M456 101v1h0c1 1 3 0 3-1 1 0 1 0 2-1v1l-2 2h-1c-1 0-3 1-4 2-1 0-2 0-3 1-3 1-6 3-9 5h0c-2 1-3 3-5 4 0 0-1 0-1 1-1 0-1 0-1 1h-1-2c1-2 2-3 3-4 2-3 4-4 7-6 1 0 2-2 3-2h1l1-1c2 0 4-2 6-2 1 0 2-1 3-1z" class="L"></path><path d="M437 115l-1-1 3-3h3c-2 1-3 3-5 4z" class="a"></path><path d="M454 105c1-1 3-2 4-2h1l-8 7-8 6h-1l-1 1 1-2-2 1c0 1-1 2-2 4 0 1-1 3-1 4-1 3-1 6-2 8 0 1 0 1-1 1v-6c0-3 2-5 1-8h0l1-1 1-1-1-1-1 1h0c0-1 0-1 1-1 0-1 1-1 1-1 2-1 3-3 5-4h0c3-2 6-4 9-5 1-1 2-1 3-1z" class="I"></path><path d="M438 120l-1-1v-1c1-2 2-2 3-3v1c0 1-1 2-2 4z" class="Q"></path><path d="M454 105c1-1 3-2 4-2h1l-8 7c0-1-1-1-2-1l-3 2c2-3 6-5 8-6z" class="G"></path><path d="M446 111l3-2c1 0 2 0 2 1l-8 6h-1l-1 1 1-2c1-2 2-3 4-4z" class="C"></path><defs><linearGradient id="Bt" x1="442.913" y1="134.371" x2="433.14" y2="125.326" xlink:href="#B"><stop offset="0" stop-color="#1a1a1b"></stop><stop offset="1" stop-color="#494849"></stop></linearGradient></defs><path fill="url(#Bt)" d="M442 115l-1 2 1-1h1l-3 3 1 1c1 0 2 1 3 1v1h1v2l2-1c0 1-1 2-2 3l1 1v2l-1 1-1 2v1h-1v1 1c-1 1-1 2-1 3l-1 1h0-1-1c0 1-1 2-2 3v1c-2-3-1-8-2-11 1-2 1-5 2-8 0-1 1-3 1-4 1-2 2-3 2-4l2-1z"></path><path d="M439 124v4h0 1l-2 6c0-3 0-6 1-10z" class="G"></path><path d="M444 121v1h1v2c-1 2-2 4-2 6h-1v-3c0-1 1-2 2-4l-1-1s0-1 1-1z" class="O"></path><path d="M440 119l1 1c1 0 2 1 3 1-1 0-1 1-1 1l-2 2-1 4h-1 0v-4c0-2 1-3 1-5z" class="c"></path><path d="M441 120c1 0 2 1 3 1-1 0-1 1-1 1l-2 2v-1c-1-1 0-2 0-3z" class="X"></path><path d="M447 123c0 1-1 2-2 3l1 1v2l-1 1-1 2v1h-1v1 1c-1 1-1 2-1 3l-1 1h0v-7l1-2h1c0-2 1-4 2-6l2-1z" class="Z"></path><path d="M443 130c-1 1-1 2-1 3l-1-1 1-2h1z" class="R"></path><path d="M445 130l-1-1c0-1 1-2 1-3l1 1v2l-1 1z" class="E"></path><path d="M487 115s6-4 7-6c1-1 3-3 4-5s1-5 3-7c0-1 1-2 1-3 0-3 1-7 2-10v3c0 2-1 6 0 8-2 8-2 16 0 24h0 1v1c0 2 0 2 1 4l2 3 2 2s1 1 1 2l1 1 1 1 3 3-4-1h-3-2v1l1 1h0v4 1l-1 1h-1l-3 1h-2l-3-1h0l1-1h3c-2-1-3-1-5-2h-2c0-1 0-1-1 0-1-1-1-2-1-3-1-2-1-2-2-3-1 0-1-1-1-1v-1-1h-2c-1-1-3-1-4-2-2-1-3-3-4-5h1-1v-1c1 0 2-1 3-1h-3 0 0c3-2 5-4 8-6l-1-1z" class="J"></path><path d="M498 109v1l1-1h0v7c0 2 1 4 0 5h-1-1-2l3-12z" class="g"></path><path d="M499 133c0-2-1-3-1-4-1-2-2-4-2-6h3c1 2 0 5 2 7h0l2 4s0 1-1 2l-3-3z" class="j"></path><path d="M506 124l2 3 2 2s1 1 1 2l1 1 1 1 3 3-4-1h-3 0c-2 0-3-1-5 0l-1-1-2-4v-1h1c1 0 1 0 2 1 1 0 1 0 2-1h0c0-1-1-2-1-3s0-2 1-2z" class="O"></path><path d="M508 127l2 2s1 1 1 2l1 1 1 1 3 3-4-1c-2 0-3-2-5-3l1-5z" class="b"></path><path d="M483 122c0-1 0-2 1-2 1 1 1 1 1 2 1 0 1 1 1 2v1h1c1 1 1 2 1 3h0v-2h0l1 2 1-1-1-1 1-2h1c1 0 1-1 2-1 0 4 1 7 4 10h0 0l2 2h1l-2-2h1l3 3c1-1 1-2 1-2l1 1c2-1 3 0 5 0h0-2v1l1 1h0v4 1l-1 1h-1l-3 1h-2l-3-1h0l1-1h3c-2-1-3-1-5-2h-2c0-1 0-1-1 0-1-1-1-2-1-3-1-2-1-2-2-3-1 0-1-1-1-1v-1-1h-2c-1-1-3-1-4-2-2-1-3-3-4-5h1-1v-1c1 0 2-1 3-1z" class="j"></path><path d="M486 125h1c1 1 1 2 1 3h0v1h-1l-1-1c-1-2 0-2 0-3z" class="S"></path><path d="M493 137l1-1 3 3v1h-2c0-1 0-1-1 0-1-1-1-2-1-3z" class="C"></path><path d="M502 138h4c0 1 0 1 1 1l-3 3-2-1v1c-2-1-3-1-5-2v-1c1 0 1-1 2-1 1 1 0 2 1 3h2c1-1 0-2 0-3z" class="J"></path><path d="M497 133h0l2 2h1l-2-2h1l3 3c1-1 1-2 1-2l1 1c1 1 2 2 3 4h0c-1 0-1 0-1-1h-4l-5-5z" class="M"></path><path d="M504 135c2-1 3 0 5 0h0-2v1l1 1h0v4 1l-1 1h-1l-3 1h-2l-3-1h0l1-1h3v-1l2 1 3-3h0c-1-2-2-3-3-4z" class="Y"></path><path d="M507 139v2l-1 1h-2l3-3h0z" class="N"></path><path d="M475 437c1-2 1-3 1-5l1-1v1c0 1 0 2 1 3h0c1 2 2 3 4 3v1c1 0 1 0 1 1l-3 1c1 0 2 0 2 1h0v1c3 5 3 9 3 14 0 3 0 5-2 7v1h1c0 1-1 3-1 4-2 3-6 8-6 11h0v5c0-1 1-1 1-2v1l-1 2v7l-1 10v6 5h0c0-1 1-1 1-2l-1 5v2l-1 2h0v-1c-1 2-1 3-2 5h0c-2 3-2 6-4 9s-4 7-6 10l-9 11c-2 3-5 6-6 9v5l1 1h0v1h-4c-1 1-2 2-4 2-2 2-6 4-8 3l-3-1c-1 0-1 0-2-1h-4c1 1 3 2 3 3l-3 1c-4 1-6 5-9 8l-1-1-3 4c-1 2-4 6-4 9h0 1l1 1h-1-2 0c-2 1-3 2-4 3v1h0 1v1h1c-8 13-17 24-23 38v2c0 2 0 2-1 3l-3-4c0-1 0-1-1-2v-2c0-1-1-2-1-3h1c-1-2-1-4-1-6 0-7 3-15 7-20 1-2 3-4 4-6 1 0 1-3 1-4l-1-1v-1c2-2 4-3 6-4h1l1-2 1 2v-2c2-3 5-5 9-6 0-1-1-2 0-4 1 0 2-1 3-1l2-1c2-2 7-7 9-10 1-1 1-2 1-3s1-3 2-4c0-4 1-8 0-11h1l-1-1c0-1-1-2-2-3v-3l-1-1c0-2 0-3-1-4v-1l7 1h0c0-1-1-1-1-2-1 0-3-1-4-1l-3-1h1v-4l-1-1c0-1 1-2 1-2 0-1-1-3-1-4h1l1-1c-1-1-2-2-4-3h0l1-1c2-1 4-3 5-4l-1-1h1l3-2h0 1v-3-2c0-1 0-2 1-3v-2-1h0c1-1 1-2 1-3 3-4 6-7 10-10l1-1v-1l2-3 1-1c-1-1-2-2-3-2l-3-3c1-1 1-1 1-2v-1c1-1 2-1 2-2h1c0-1 0 0 1-1-3-1-4-1-6-3l-1-2c-1 0-1-1-1-2-1-1-2-1-3-2h-1 0l-1-2-4-4v-1l8-9h0v1c1 0 2-2 4-2-1 3-1 4 1 5l2 2v-1-2h3 1l8-1 2 1h1c2 1 4 2 5 2 4 0 8 0 10-3h1c4-3 7-7 7-12z" class="j"></path><path d="M398 595c1 1 1 2 0 3h0c-1 0-2 0-3-1h1c1 0 2-1 2-2z" class="N"></path><path d="M482 458v3c-1 1-1 2-2 3h0c-1 0-1 0-1-1 1-1 2-3 3-5z" class="c"></path><path d="M418 575c1 0 1-1 2 0l1 1c0 1-1 2-2 3 0-2 0-2-1-4z" class="B"></path><path d="M395 594v-1l1 1c1 0 2 0 2 1s-1 2-2 2h-1 0v-3z" class="Y"></path><path d="M437 491c2 1 3 2 4 4h-1c-1-1-4-2-4-3l1-1z" class="O"></path><path d="M440 483l1 1c-1 2 0 3-3 4h-1v-1l2-3 1-1z" class="M"></path><path d="M444 514l1-1c1 1 1 2 1 3s0 1-1 1h-2v-1c0-1 0-2 1-2z" class="E"></path><path d="M432 501h0c0-1 0-2-1-2v-3l1-1h0c1 2 1 6 1 8v-2h-1z" class="D"></path><path d="M453 454c2 1 2 2 3 5 1 1 1 3 1 5-1-1-1-2-1-2-1-3-1-6-3-8z" class="C"></path><path d="M478 435h0c1 2 2 3 4 3v1c1 0 1 0 1 1l-3 1c-1-2-2-3-2-6z" class="O"></path><path d="M424 510c0 1 0 6 1 7 2 1 2 2 3 4v1 2l-1-2-1-2c0-1-1-2-2-3v-4-3z" class="J"></path><path d="M457 514c1-1 1-2 2-3 0 3-1 5-1 7l-1-1c-1 1-1 2-2 2v-1l-1 1c0-1 2-4 3-5z" class="D"></path><path d="M395 594v-2c2-3 5-5 9-6v2c-1 0-3 1-4 1-2 1-3 3-4 4v1l-1-1v1h0z" class="Z"></path><path d="M446 530h1l1-2v-1c1 1 1 2 1 3 0 2-1 2-1 3-1 1-1 1-1 2h0c1 2 0 3 0 4-1-1-1-3-1-4 1-2 0-4 0-5z" class="S"></path><path d="M437 491c2-1 2-1 4-1l2 1c0 1-1 1 0 2v1c0 1 1 2 1 4h-1c0-1-1-2-2-3h0c-1-2-2-3-4-4zm6 41l1-1v-1-1h1l1 1c0 1 1 3 0 5l-4 3c0-1 0-3 1-4v-2z" class="D"></path><path d="M478 444v1c2 4 1 10-1 13l-1 1c0-3 0-6 1-9 0-2 0-4 1-6z" class="O"></path><path d="M446 523c3-1 5-3 7-4-2 3-5 6-9 7-2 1-4 0-6-1h3v1c1-1 2-1 2-2 2 0 2 0 3-1z" class="Z"></path><path d="M443 494h1l1 1c1 4 1 9-1 14 0-3 1-8-1-10h0 0c-1-1-2-2-3-4h1 0c1 1 2 2 2 3h1c0-2-1-3-1-4z" class="R"></path><path d="M448 548c4-4 6-10 9-14 0 4-3 9-5 13l-3 3-1 1v-3z" class="b"></path><path d="M443 499h0c2 2 1 7 1 10-1 2-3 6-6 8h0c0-1 0-1 1-2 0 0 0-1 1-2h-1c0-1 1-2 2-3 2-3 2-7 2-11z" class="G"></path><path d="M437 497c1 0 2 2 2 2 2 4 1 9-1 12-1 0-1 1-1 1v-8c0-1-1-5 0-6v-1z" class="B"></path><path d="M482 443c3 5 3 9 3 14-1 4-2 8-5 10 0-1 1-2 1-3h1v-1-2-3c0-1 1-3 1-4 0-3-2-7-1-11z" class="h"></path><path d="M448 548v3c-4 4-7 8-13 10h-4 1c1-1 1-2 2-2 6-2 11-7 14-11z" class="Y"></path><path d="M425 542l1-1 1 1c0 1 0 1 1 2-2 3-4 7-5 11-1 2-1 5-2 8 0-4 1-8 0-11h1l-1-1h1v-3l3-3h0c1-1 1-1 1-3h-1zm18-51c4 3 8 6 9 12v4h0c-1 3-2 6-4 8h0v-2c2-4 2-11 0-15 0-2-1-3-3-4h0v1l-1-1h-1v-1c-1-1 0-1 0-2z" class="M"></path><path d="M432 501h1v2c0 3 0 6-1 9-1 1-2 2-2 3 1 1 2 1 3 1h-4v1-1c-2-4 1-11 3-15z" class="T"></path><path d="M460 464v-1c2 1 3 0 5 1h-3v2l2 6v2c1 4 1 8 1 12v2l-4-18-1-5v-1z" class="Y"></path><path d="M460 465l1-1c0 1 1 3 0 4v2l-1-5z" class="B"></path><path d="M424 574c0-1-1-2-1-3-1-3 1-8 2-11l9-1c-1 0-1 1-2 2h-1c-3 1-4 1-6 4l1 1v1l-1 3h0 0c0 2 1 3 3 4h-4z" class="T"></path><path d="M425 565l1 1v1l-1 3h0 0c-1 0-1 0-1-1s0-2 1-4z" class="G"></path><path d="M392 594h1l1-2 1 2h0v3h-1v1c0 2-1 4-3 6h-1 0c-1-2-2-3-2-5h-1v1l-1-1v-1c2-2 4-3 6-4z" class="M"></path><path d="M388 599v-1c1-1 2 0 3 0 1 1 1 2 1 3s-1 2-1 3h-1 0c-1-2-2-3-2-5z" class="E"></path><path d="M417 539l7 1-1 1v1h2 1c0 2 0 2-1 3h0l-3 3v3h-1c0-1-1-2-2-3v-3l-1-1c0-2 0-3-1-4v-1z" class="Y"></path><path d="M425 542h1c0 2 0 2-1 3h0l-3 3v3h-1c0-1-1-2-2-3v-3c0 2 1 3 2 4v-5h0l2 2v-1c1-1 1-2 0-3h2z" class="E"></path><path d="M448 551l1-1 3-3c-1 1-1 2-2 3-1 4-4 7-6 10h0l-1-1c-1 0-2 1-3 2h0c-1 1-2 1-3 1h0c-2 1-5 1-7 1-2 1-3 2-4 3l-1-1c2-3 3-3 6-4h4c6-2 9-6 13-10z" class="a"></path><path d="M431 561h4 0c-2 1-3 1-5 1v1c1-1 4 0 4-1h1 1 1 0c-2 1-5 1-7 1-2 1-3 2-4 3l-1-1c2-3 3-3 6-4z" class="F"></path><path d="M439 513h1c-1 1-1 2-1 2-1 1-1 1-1 2h-1l-4 3c1 0 3 0 4 1l-1 1h0c2 2 5 2 7 2l3-1c-1 1-1 1-3 1 0 1-1 1-2 2v-1h-3c-1 0-3-1-4-1-3-2-4-4-5-7v-1h4l6-3z" class="E"></path><path d="M439 513h1c-1 1-1 2-1 2-1 1-1 1-1 2h-1l-4 1c-1 0-2-1-4-2h4l6-3z" class="c"></path><defs><linearGradient id="Bu" x1="458.423" y1="508.579" x2="438.739" y2="524.455" xlink:href="#B"><stop offset="0" stop-color="#474647"></stop><stop offset="1" stop-color="#676667"></stop></linearGradient></defs><path fill="url(#Bu)" d="M457 504v10c-1 1-3 4-3 5h-1c-2 1-4 3-7 4l-3 1c-2 0-5 0-7-2h0l1-1c1 1 2 1 3 1 3 0 6-1 8-3 5-4 7-8 8-14l1-1z"></path><path d="M455 519c1 0 1-1 2-2l1 1-4 13c-1 6-2 10-6 14 1-2 2-4 2-7 1-6 1-13 5-19z" class="C"></path><defs><linearGradient id="Bv" x1="480.651" y1="460.43" x2="468.172" y2="471.967" xlink:href="#B"><stop offset="0" stop-color="#747374"></stop><stop offset="1" stop-color="#8c8b8b"></stop></linearGradient></defs><path fill="url(#Bv)" d="M479 463c0 1 0 1 1 1h0c1-1 1-2 2-3v2 1h-1c0 1-1 2-1 3-4 4-8 5-13 6h0l-3 1v-2l-2-6v-2h3l1 1c2 1 4 2 7 2l1-1c2 0 3-1 5-3z"></path><path d="M462 466v-2h3l1 1c2 1 4 2 7 2l-8 1v-1c0-1-2-2-3-2v1z" class="b"></path><path d="M462 466v-1c1 0 3 1 3 2v1c1 0 1 1 2 2v3h0l-3 1v-2l-2-6z" class="K"></path><path d="M464 472v-2h3v3h0l-3 1v-2z" class="f"></path><path d="M471 452l4-4h0c-1 6-4 10-7 15 0 1-1 1-1 1l-1 1-1-1c-2-1-3 0-5-1v1c-1-1-1-3-1-5v-1c1-1 2-1 3-2 2 0 4-1 5-2h0c2-1 3-2 4-2z" class="D"></path><path d="M471 452c-2 4-5 7-7 11h-1v-1c1-2 4-6 4-8h0c2-1 3-2 4-2z" class="J"></path><path d="M467 454c0 2-3 6-4 8h-1l-2-1s0-1-1-2v-1c1-1 2-1 3-2 2 0 4-1 5-2z" class="M"></path><path d="M460 461l1-1h1c0 1 1 1 1 2h-1l-2-1z" class="N"></path><path d="M418 575c1 2 1 2 1 4l-5 6-3 4c-1 2-4 6-4 9h0 1l1 1h-1-2 0c-2 1-3 2-4 3v1h0l-3 2v-4l4-5c3-8 9-15 15-21z" class="I"></path><path d="M403 596c-1 1-1 2-2 3h1c1-1 1-1 2-1-1 0-1 1-1 2l3-1c-2 1-3 2-4 3v1h0l-3 2v-4l4-5z" class="O"></path><path d="M404 598v-1c2-3 5-7 7-8-1 2-4 6-4 9h0 1l1 1h-1-2 0l-3 1c0-1 0-2 1-2z" class="B"></path><path d="M437 452c6 1 10 1 16 2 2 2 2 5 3 8 0 0 0 1 1 2l-1 2c-1 0-2 0-4 1h-4 0l-3-1h0v-1c1 0 2 0 2-1v-1l-1-6h0c-3-1-5-3-9-4v-1z" class="N"></path><path d="M446 457h1c2 2 3 5 3 8l2 2h-4 0l-3-1h0v-1c1 0 2 0 2-1v-1l-1-6z" class="O"></path><path d="M448 467c-1-1 0-2 0-3 1 0 1 0 2 1l2 2h-4 0z" class="L"></path><path d="M452 467c2-1 3-1 4-1 0 6 2 12 2 18l1 1h-1 0c-1 0-3-1-5-1h0c-1 0-2-1-2-2l-3-1v-1l1-1h2v-1c-1 0-3-1-4-1-1-4-2-7-2-11l3 1h0 4z" class="C"></path><path d="M451 478l1-1c1 1 3 0 3 1-1 2-1 1-3 2l-1 2-3-1v-1l1-1h2v-1z" class="V"></path><path d="M445 466l3 1h0c1 0 1 0 1 1 1 1 1 2 1 3 1 2 2 4 2 6l-1 1c-1 0-3-1-4-1-1-4-2-7-2-11z" class="g"></path><path d="M399 601v4l3-2h1v1h1c-8 13-17 24-23 38v2c0 2 0 2-1 3l-3-4c0-1 0-1-1-2v-2c0-1-1-2-1-3h1 1 1l4-6c5-10 11-19 17-29z" class="B"></path><path d="M402 603h1v1c-1 1-2 2-3 2l-1-1h0l3-2zm-25 33h1l4-6c0 3-3 9-1 12v2c0 2 0 2-1 3l-3-4c0-1 0-1-1-2v-2c0-1-1-2-1-3h1 1z" class="R"></path><path d="M376 636h1c1 1 1 1 0 3h-1c0-1-1-2-1-3h1z" class="Q"></path><defs><linearGradient id="Bw" x1="468.583" y1="524.228" x2="442.543" y2="525.157" xlink:href="#B"><stop offset="0" stop-color="#989697"></stop><stop offset="1" stop-color="#c4c2c3"></stop></linearGradient></defs><path fill="url(#Bw)" d="M468 486c0 11 0 21-1 31-1 3-2 7-3 10 0 1-1 3-1 4l-6 12c-3 7-8 14-13 20v-3c2-3 5-6 6-10 1-1 1-2 2-3 2-4 5-9 5-13 3-8 6-15 7-23 1-5 1-11 1-17 1-2 0-5 1-7 1 0 1 0 2-1z"></path><defs><linearGradient id="Bx" x1="447.062" y1="502.525" x2="459.87" y2="487.076" xlink:href="#B"><stop offset="0" stop-color="#302f30"></stop><stop offset="1" stop-color="#5d5c5c"></stop></linearGradient></defs><path fill="url(#Bx)" d="M439 472l2 2c1 1 2 2 3 2l3 1c1 0 3 1 4 1v1h-2l-1 1v1l3 1c0 1 1 2 2 2h0c2 0 4 1 5 1h0v4c1 3 3 8 3 11v4c-1 3-2 5-2 7-1 1-1 2-2 3v-10l-1 1c-2-10-8-15-15-21l-1-1c-1-1-2-2-3-2l-3-3c1-1 1-1 1-2v-1c1-1 2-1 2-2h1c0-1 0 0 1-1z"></path><path d="M461 504h-2v-4h0l1 2h1v-2 4z" class="R"></path><path d="M439 472l2 2c1 1 2 2 3 2l3 1c1 0 3 1 4 1v1h-2l-1 1v1h0-1l-1 1h0c0 1-1 1-1 2v1c6 5 11 10 12 19l-1 1c-2-10-8-15-15-21l-1-1c-1-1-2-2-3-2l-3-3c1-1 1-1 1-2v-1c1-1 2-1 2-2h1c0-1 0 0 1-1z" class="I"></path><path d="M439 472l2 2v4l-1 1c-1 0-2-1-3-2 0-2 0-3 1-4 0-1 0 0 1-1z" class="p"></path><path d="M441 478c1 0 2 0 3 1h0l2 2h1l-1 1h0c0 1-1 1-1 2v1l-5-3v-3l1-1z" class="h"></path><path d="M441 474c1 1 2 2 3 2l3 1c1 0 3 1 4 1v1h-2l-1 1v1h0-1-1l-2-2h0c-1-1-2-1-3-1v-4z" class="F"></path><path d="M444 476l3 1c1 0 3 1 4 1v1h-2l-1 1c-1-1-3-2-4-4z" class="B"></path><path d="M430 447h0v1c1 0 2-2 4-2-1 3-1 4 1 5l2 2c4 1 6 3 9 4h0l1 6v1c0 1-1 1-2 1v1h0c0 4 1 7 2 11l-3-1c-1 0-2-1-3-2l-2-2c-3-1-4-1-6-3l-1-2c-1 0-1-1-1-2-1-1-2-1-3-2h-1 0l-1-2-4-4v-1l8-9z" class="j"></path><path d="M430 447h0v1c1 0 2-2 4-2-1 3-1 4 1 5-4 3-6 7-9 10l-4-4v-1l8-9z" class="D"></path><path d="M428 463c2 0 6-3 8-4 1-1 2-1 3-2 1 1 2 2 3 2 0 1 0 1 1 2 1 0 2 0 2 1l1 1h1v1c0 1-1 1-2 1v1c-4-1-9-1-13 0v1c-1 0-1-1-1-2-1-1-2-1-3-2z" class="M"></path><defs><linearGradient id="By" x1="433.481" y1="464.612" x2="447.46" y2="472.867" xlink:href="#B"><stop offset="0" stop-color="#0e0d0e"></stop><stop offset="1" stop-color="#313031"></stop></linearGradient></defs><path fill="url(#By)" d="M432 467v-1c4-1 9-1 13 0h0c0 4 1 7 2 11l-3-1c-1 0-2-1-3-2l-2-2c-3-1-4-1-6-3l-1-2z"></path><path d="M444 563c5-6 10-13 13-20l6-12v3c2-1 2-2 4-1 1 0 1 0 2 1-2 3-4 7-6 10l-9 11c-2 3-5 6-6 9v5l1 1h0v1h-4c-1 1-2 2-4 2-2 2-6 4-8 3l-3-1c-1 0-1 0-2-1-2-1-3-2-3-4h0 0l1-3v-1c1-1 2-2 4-3 2 0 5 0 7-1h0c1 0 2 0 3-1h0c1-1 2-2 3-2l1 1h0v3z" class="O"></path><path d="M439 572l1-2c2 1 3 1 5 1-1 1-2 2-4 2h-1s0-1-1-1h0z" class="E"></path><path d="M430 575h3c2 0 4-1 6-3h0c1 0 1 1 1 1h1c-2 2-6 4-8 3l-3-1z" class="B"></path><path d="M439 569c0-2 0-4 1-6v2 1 1c1-1 2-1 3-2 1 1 1 1 1 2l-1 1c-1 0-1 1-2 2h-1l-1-1h0z" class="b"></path><path d="M440 561h0c1-1 2-2 3-2l1 1h0v3l-1 2c-1 1-2 1-3 2v-1-1-2-2z" class="U"></path><path d="M440 561h0c1-1 2-2 3-2l1 1-3 2c0 1-1 2-1 3v-2-2z" class="I"></path><defs><linearGradient id="Bz" x1="431.509" y1="572.863" x2="431.155" y2="563.599" xlink:href="#B"><stop offset="0" stop-color="#242324"></stop><stop offset="1" stop-color="#454444"></stop></linearGradient></defs><path fill="url(#Bz)" d="M440 561v2c-1 2-1 4-1 6h0c0 1-1 1-2 2s-2 2-4 3c-2 0-4 0-5-2-2-1-2-2-2-5v-1c1-1 2-2 4-3 2 0 5 0 7-1h0c1 0 2 0 3-1z"></path><path d="M437 567l2 2h0c0 1-1 1-2 2v-4z" class="Y"></path><path d="M437 562l1 1c1 1 0 1 0 2h-2c0-1 0-1-1-2h-5c2 0 5 0 7-1z" class="B"></path><path d="M440 561v2c-1 2-1 4-1 6l-2-2-1-2h2c0-1 1-1 0-2l-1-1h0c1 0 2 0 3-1z" class="G"></path><path d="M430 565h2c1 1 1 2 1 3s0 2-2 2c-1 1-1 1-1 0-1-1-2-1-2-3l2-2z" class="i"></path><path d="M423 513h0 1v4c1 1 2 2 2 3l1 2 1 2 1 1c1 0 3 1 4 1 2 3 0 7 4 8l1-1v-1l1-1c2-1 3 0 4 1v2c-1 1-1 3-1 4l4-3c0 1 0 3 1 4l-1 3c-2 3-3 6-5 8h-1c-1 2-4 4-6 5s-4 3-6 3c0-1 0-2 1-2l1-2c1 0 0 0 1-1h0l2-2h0v-1-1l1-2h0c1-1 1-4 0-5v-1h0l-1-1c-1 0-3-1-4 0v1l-1 3c-1-1-1-1-1-2l-1-1-1 1h-2v-1l1-1h0c0-1-1-1-1-2-1 0-3-1-4-1l-3-1h1v-4l-1-1c0-1 1-2 1-2 0-1-1-3-1-4h1l1-1c-1-1-2-2-4-3h0l1-1c2-1 4-3 5-4l-1-1h1l3-2z" class="D"></path><path d="M436 551h0c-1 1-1 2-2 2h-1l2-3 1 1h0z" class="E"></path><path d="M433 526c2 3 0 7 4 8l1-1 1 2h-1s-1 0-1-1l-2 2-3 3h0l-1-1c-3-1-4-7-5-10h-1 7s0-1 1-2z" class="S"></path><path d="M438 533v-1l1-1c2-1 3 0 4 1v2c-1 1-1 3-1 4l4-3c0 1 0 3 1 4l-1 3c-3 3-6 7-9 8l-1 1h0l-1-1 1-1v-2c0-2 1-6 0-7v-1h-4l3-3 2-2c0 1 1 1 1 1h1l-1-2z" class="Z"></path><path d="M441 538h2c0 1 0 0-1 1s-2 4-3 5v-3c0-1-1-1-1-1 0-1 0-2 1-2h2z" class="i"></path><path d="M435 536h2 1c1 0 2 1 3 2h-2c-1 0-1 1-1 2 0 0 1 0 1 1l-1-1c-1 0-1 0-2-1h-4l3-3z" class="L"></path><path d="M436 539c1 1 1 1 2 1l1 1v3c0 2 0 2-1 3 0 1-1 3-1 3l-1 1h0l-1-1 1-1v-2c0-2 1-6 0-7v-1z" class="R"></path><path d="M423 513h0 1v4c1 1 2 2 2 3l1 2 1 2 1 1c-1 1-2 2-4 2v1 1 1c0 3 1 4 1 7h-1c0 1 0 2-1 3 0-1-1-1-1-2-1 0-3-1-4-1l-3-1h1v-4l-1-1c0-1 1-2 1-2 0-1-1-3-1-4h1l1-1c-1-1-2-2-4-3h0l1-1c2-1 4-3 5-4l-1-1h1l3-2z" class="B"></path><path d="M417 532v-3h1c1 2 1 5 1 8l-3-1h1v-4z" class="h"></path><path d="M423 513h0 1v4c1 1 2 2 2 3l1 2 1 2 1 1c-1 1-2 2-4 2v1 1 1c0 3 1 4 1 7h-1c0 1 0 2-1 3 0-1-1-1-1-2v-2l-1-5c-2-3-2-7-2-10v-2c1-1 1-1 2-1l1-1h0v-3h0v-1z" class="E"></path><path d="M424 517c1 1 2 2 2 3-1 1-2 1-2 2-1-2-1-3 0-5z" class="D"></path><path d="M424 522c0-1 1-1 2-2l1 2-3 3c-1-1-1-2 0-3zm-4-1c1 1 1 2 1 4 1 2 1 4 1 6-2-3-2-7-2-10z" class="N"></path><path d="M427 522l1 2 1 1c-1 1-2 2-4 2h-1v-2l3-3z" class="W"></path><path d="M425 537c-1-1-1-2-2-3v-6h2v1 1c0 3 1 4 1 7h-1z" class="e"></path><defs><linearGradient id="CA" x1="459.308" y1="478.641" x2="486.932" y2="521.134" xlink:href="#B"><stop offset="0" stop-color="#272627"></stop><stop offset="1" stop-color="#525251"></stop></linearGradient></defs><path fill="url(#CA)" d="M485 457c0 3 0 5-2 7v1h1c0 1-1 3-1 4-2 3-6 8-6 11h0v5c0-1 1-1 1-2v1l-1 2v7l-1 10v6 5h0c0-1 1-1 1-2l-1 5v2l-1 2h0v-1c-1 2-1 3-2 5h0c-2 3-2 6-4 9-1-1-1-1-2-1-2-1-2 0-4 1v-3c0-1 1-3 1-4 1-3 2-7 3-10 1-10 1-20 1-31-1 1-1 1-2 1-1 2 0 5-1 7v-6-2c0-4 0-8-1-12l3-1h0c5-1 9-2 13-6 3-2 4-6 5-10z"></path><path d="M473 525l3-16v5h0c0-1 1-1 1-2l-1 5v2l-1 2h0v-1c-1 2-1 3-2 5h0z" class="e"></path><path d="M464 474l3-1v4c0 2 0 4 1 5h0 2c0 1-1 3-2 4s-1 1-2 1c-1 2 0 5-1 7v-6-2c0-4 0-8-1-12z" class="F"></path><path d="M465 483c0-2 0-4 1-6h1c0 2 0 4 1 5h0c-1 1-1 1-2 1h-1z" class="a"></path><path d="M468 482h2c0 1-1 3-2 4s-1 1-2 1l-1-4h1c1 0 1 0 2-1z" class="W"></path><path d="M272 143h0v1h0s1-1 2-1 3 1 3 1h1 2v1c1 1 2 2 3 4h0c1 1 2 3 3 4h1c-1-1-1-2-2-3h0 0c1 1 2 3 3 4v-1l2 2c1 0 1 1 1 2v1l-2 1v2h0l1 1c0 2 0 3-1 4 2 1 3 3 5 4l1 1v2l-1-1-1 1v1h0v1c-2 1-3 1-4 1 2 2 4 4 5 6-1 1-1 1-1 2 2 3 4 4 4 8h-1l-2-1h0v1h-1c-2-1-3-1-4-2l-3-2c-1-1-1-1-1-2-3-1-7-3-10-6h-2l-1 1 2 1c-2 1-4 0-6-1h-6l-1 2h0c0 3-3 6-4 8l-1 1c1 0 1 0 2 1 1-1 2-3 3-5 1 2 1 3 1 4-1 0-1 0-2 1v1c-1 2-3 4-4 6h-1c0 1-1 2-2 3l-1 1c0 1 0 1 1 1l-1 3c-1-1-2-1-3-1h0v2l-15 9h0c-3 1-4 1-7 1-5 3-10 5-15 7-2 1-4 2-5 4-2 1-4 2-7 4 0 0-1 0-1 1h-1-1v1h1v1h-2v-1l-1 1h-2c-1 0-2 1-3 2v1l-4 3h-2l-4 5c0-1 0-1-1-1h-1l-1-1h-1c0-2-1-4-1-6 0-1-1-2-1-3l-3-6c0-1 0-3 1-4v-2h0l1-3c0-1 0-1 1-2h0l-5-2s0-1-1-1l1-2 3-4 5-5 12-11c1 1 2 1 3 2 0-3 0-5 2-7v-1h0l1-2c1 0 1-1 1-2h0l-3 1-1-1c1-2 5-1 6-3l2-2-1-1c-2 1-4 1-7 1-6-2-10-5-13-10l-1-1h0v-6c1 0 1-1 2-1v-1l1 1c0-5 0-7 3-11 4-4 8-4 13-5l-1 1h-1l1 1h3v1h3c2 1 4 1 5 2h2l2-3h0c1 1 1 2 1 3l1-1v1l2-2 1 1c2 1 4 3 6 4l2 1 2-3h1l1 1c2-1 2-2 3-2v-1l1-1c0 1 0 1 1 2 1 0 2-1 4-1 2-1 4-1 6-1h3c1 0 2 0 4-1h3 8l1-1h0c2-2 7-3 10-4z" class="S"></path><path d="M240 163h1l-2 2-2 1-1-1h0c2-1 3-1 4-2z" class="C"></path><path d="M224 169c1 0 2-1 3 0h1c-2 2-4 3-6 5v-2h-1l1-1 2-2h0z" class="b"></path><path d="M202 187c1-2 2-3 4-4v2c-1 2-3 2-4 4l1 1-1 2-1 2c-1-1-1-2-1-3l1-3 1-1z" class="M"></path><path d="M202 187c0 2-1 3 0 5l-1 2c-1-1-1-2-1-3l1-3 1-1z" class="C"></path><path d="M232 169c0-3 1-4 3-6s4-5 6-6l1-1c1 0 3-1 4-1h1l-1 1c-4 1-7 5-10 8v1l1 1v1l-1-1c-2 0-3 2-4 3z" class="E"></path><path d="M208 189l5-6c-1 3-4 5-5 9l1 1v4c-1 1-1 2-1 4h0c0-1-1-1-1-2-2-4-1-7 1-10z" class="b"></path><path d="M203 190l3-2h0 0 1 0l1 1c-2 3-3 6-1 10 0 1 1 1 1 2 2 2 4 1 4 4h0c-2 2-3 3-5 4-1 0-2-1-3-1h-1v-1c-1 0-1 0-1-1s-1-1-2-1h0c-1-3 0-8 1-11l1-2 1-2z" class="S"></path><path d="M203 190l3-2h0 0c-1 1-2 2-2 4-2 4-3 8-1 12l1 4h-1v-1c-1 0-1 0-1-1s-1-1-2-1h0c-1-3 0-8 1-11l1-2 1-2z" class="G"></path><path d="M206 188h1 0l1 1c-2 3-3 6-1 10 0 1 1 1 1 2 2 2 4 1 4 4h0-4c-1 0-2-1-2-2-1-2-2-4-2-6v-5c0-2 1-3 2-4z" class="j"></path><path d="M217 182c1-1 2-1 4-1l-1 1h2c-3 3-5 6-6 10v1 5h-1l2 1v2h0l1 2v1c-1 0-2 1-3 1l-2-2-1 2c0-3-2-2-4-4h0c0-2 0-3 1-4v-4c1-3 3-6 5-8 0-1 1-2 2-2 0-1 0-1 1-1z" class="c"></path><path d="M217 182c1-1 2-1 4-1l-1 1-3 4-1-1h0 0v-2h0c0-1 0-1 1-1z" class="D"></path><path d="M214 185c0-1 1-2 2-2h0v2h0 0l1 1-1 2c-1 0-2 1-4 1 1-2 2-3 2-4z" class="M"></path><path d="M212 189c2 0 3-1 4-1l-2 4c-1 1-2 2-2 3l-1-1c0-2 0-4 1-5z" class="E"></path><path d="M214 192l1 1h1v5h-1s0-1-1-1l-1 1v1h-1v-4c0-1 1-2 2-3z" class="h"></path><path d="M214 185c0 1-1 2-2 4-1 1-1 3-1 5v1 5c-1-1-2-2-2-3v-4c1-3 3-6 5-8z" class="F"></path><path d="M211 194l1 1v4h1v-1l1-1c1 0 1 1 1 1l2 1v2h0l1 2v1c-1 0-2 1-3 1l-2-2-1 2c0-3-2-2-4-4h0c0-2 0-3 1-4 0 1 1 2 2 3v-5-1z" class="Y"></path><path d="M213 198l1-1c1 0 1 1 1 1l2 1v2h0l1 2v1c-1 0-2 1-3 1l-2-2s-1-1 0-1c0-1 1-1 1-1 0-1-1-2-1-3z" class="S"></path><path d="M214 201c1 2 1 2 3 2v-2l1 2v1c-1 0-2 1-3 1l-2-2s-1-1 0-1c0-1 1-1 1-1z" class="D"></path><path d="M223 161v2c0 1 1 1 1 2-1 1-1 2-1 3l1 1-2 2-1 1h1v2h0l-5 3-7 4c-1 0-3 2-4 2-2 1-3 2-4 4l-1 1-1-1s1-1 1-2v-1h-1c1-1 1-2 2-3 0-1 1-2 1-3l3-2 1-3 5-3 3-2c3-2 5-4 8-7z" class="I"></path><path d="M215 170c1 0 1 0 1-1h3c-1 2-2 3-4 4v-1l1-1-1-1z" class="h"></path><path d="M216 176c1-1 3-3 5-3l1 1-5 3h0v-1h-1z" class="H"></path><path d="M202 181l4-2h1l-6 5h-1c1-1 1-2 2-3z" class="a"></path><path d="M206 176l7-4h0l-3 3c-1 1-2 3-3 4h-1l-4 2c0-1 1-2 1-3l3-2z" class="F"></path><path d="M216 176h1v1h0l-7 4c-1 0-3 2-4 2-2 1-3 2-4 4l-1 1-1-1s1-1 1-2c5-4 10-6 15-9z" class="X"></path><path d="M223 161v2c0 1 1 1 1 2-1 1-1 2-1 3l1 1-2 2-1 1h-2l3-3h-1-2-3c0 1 0 1-1 1l-2 2-7 4 1-3 5-3 3-2c3-2 5-4 8-7z" class="O"></path><path d="M223 161v2c0 1 1 1 1 2-1 1-1 2-1 3l1 1-2 2c-1-2 0-2 0-3-1-1-1 0-2 0h-2-3c3-2 5-4 8-7z" class="B"></path><path d="M203 175c0-1 1-2 2-2h2l-1 3-3 2c0 1-1 2-1 3-1 1-1 2-2 3h1v1c0 1-1 2-1 2l1 1-1 3c0 1 0 2 1 3-1 3-2 8-1 11h0v1l-1-1v1 1 2c0 2 1 3 2 4l-2 2-2-5-2-1 1 1c-1 1-1 2-2 2h-1v-1l-1-1h-2c1-1 1-2 1-3v-2-3-3l1-2c0-3 0-5 2-7v-1h0l1-2c1 0 1-1 1-2h0l-3 1-1-1c1-2 5-1 6-3l2-2-1-1 1-2v-1c1 0 2-1 3-1z" class="M"></path><path d="M200 176c1 0 2-1 3-1 0 0-1 1 0 2l-3 3-1-1 1-2v-1z" class="B"></path><path d="M200 184h1v1c0 1-1 2-1 2-1 1-2 1-2 1 0-1 1-3 2-4z" class="L"></path><path d="M196 199c0 4 1 7 1 11l-2-1v-1c-1-1 0-1 0-2-1-2 1-5 1-7z" class="E"></path><path d="M203 175c0-1 1-2 2-2h2l-1 3-3 2v-1c-1-1 0-2 0-2z" class="Y"></path><path d="M194 189l1-2c0 3-1 5-1 7-1 2-1 4-1 5-1 3-1 7 0 10v2l-1-1h-2c1-1 1-2 1-3v-2-3-3l1-2c0-3 0-5 2-7v-1h0z" class="X"></path><path d="M198 188s1 0 2-1l1 1-1 3c0 1 0 2 1 3-1 3-2 8-1 11h0v1l-1-1v1 1 2c0 2 1 3 2 4l-2 2-2-5c0-4-1-7-1-11 1-1 1-4 1-6 0-1 0-3 1-5z" class="a"></path><path d="M200 191c0 1 0 2 1 3-1 3-2 8-1 11h0v1l-1-1v1 1 2c-1-6-1-12 1-18z" class="R"></path><path d="M253 148h8-2 1 1c1 1 3 1 5 1 4 0 7 0 10 1l1 2h0v1h-2-2l-6-1c-2-1-8 0-10 0-4 0-8-1-11 0-2 1-5 2-8 3-1 1-4 1-4 3h0c0 3-3 6-4 8-1 1-2 2-2 3h-1c-1-1-2 0-3 0h0l-1-1c0-1 0-2 1-3 0-1-1-1-1-2v-2-1-1s1-1 2-1l-1-1-1 1-1-1v-1l2-1 2-3h1l1 1c2-1 2-2 3-2v-1l1-1c0 1 0 1 1 2 1 0 2-1 4-1 2-1 4-1 6-1h3c1 0 2 0 4-1h3z" class="j"></path><path d="M250 148h3v1h3l-6 1c-1-1-1-1 0-2z" class="E"></path><path d="M253 148h8-2 1 1c1 1 3 1 5 1h-10-3v-1z" class="B"></path><path d="M227 165c1 0 2 1 3 1-1 1-2 2-2 3h-1c-1-1-2 0-3 0l3-4z" class="G"></path><path d="M225 158l2-1c0 2-1 2-2 4h0l-1 1c1 1 1 1 0 3 0-1-1-1-1-2v-2-1-1s1-1 2-1z" class="k"></path><path d="M227 165c0-2 2-6 4-7h0v3l3-3c0 3-3 6-4 8-1 0-2-1-3-1z" class="V"></path><path d="M246 149c1 0 2 0 4-1-1 1-1 1 0 2-4 0-8 1-11 2-4 2-8 4-12 5l-2 1-1-1-1 1-1-1v-1l2-1 2-3h1l1 1c2-1 2-2 3-2v-1l1-1c0 1 0 1 1 2 1 0 2-1 4-1 2-1 4-1 6-1h3z" class="C"></path><path d="M233 151c1 0 2-1 4-1 2-1 4-1 6-1-3 2-7 4-11 4v-1l-1-1v-1l1-1c0 1 0 1 1 2z" class="k"></path><path d="M226 152h1l1 1c2-1 2-2 3-2l1 1v1c-2 1-7 3-8 4l-1 1-1-1v-1l2-1 2-3z" class="j"></path><path d="M272 143h0v1h0s1-1 2-1 3 1 3 1h1 2v1c1 1 2 2 3 4h0c1 1 2 3 3 4h1c-1-1-1-2-2-3h0 0c1 1 2 3 3 4v-1l2 2c1 0 1 1 1 2v1l-2 1v2h0l1 1c0 2 0 3-1 4 2 1 3 3 5 4l1 1v2l-1-1-1 1v1h0v1c-2 1-3 1-4 1l-1-2h-1l-3-4h0l-2-1c-1-2-4-4-6-5l-10-7h-2 0-2l-2-1-1 1-2 1v-1-1h1c0-1 0 0-1-1h0l1-1h3c0-1 1-1 2-1h4 1 0l-1-1h0 0l6 1h2 2v-1h0l-1-2c-3-1-6-1-10-1-2 0-4 0-5-1h-1-1 2l1-1h0c2-2 7-3 10-4z" class="M"></path><path d="M288 154v-1l2 2c1 0 1 1 1 2v1l-2 1v2h0-1c1-1 1-1 1-2-1-1-1-3-1-5z" class="X"></path><path d="M260 156c-1 0-1-1-2-1v-1c1 0 2 1 3 1l1-1c2 0 4-1 6 0v2h-1l-1 1h-2 0-2l-2-1z" class="E"></path><path d="M268 154c2 1 6 2 8 2l1-1c4 0 7 1 10 4v1c-3-1-6-3-10-4h-4v1s-4-1-5-1v-2z" class="h"></path><path d="M272 143h0v1h0s1-1 2-1 3 1 3 1h1 2v1c1 1 2 2 3 4h0l1 3c2 1 2 3 3 4-1 0-3-2-4-3-2-1-4-2-7-3s-6-1-10-1c-2 0-4 0-5-1h-1-1 2l1-1h0c2-2 7-3 10-4z" class="T"></path><path d="M280 145c1 1 2 2 3 4h0l1 3-3-2v-1c0-1-1-1-1-2-1-1 0-1 0-2z" class="N"></path><path d="M262 147c7 0 12 1 19 3l3 2c2 1 2 3 3 4-1 0-3-2-4-3-2-1-4-2-7-3s-6-1-10-1c-2 0-4 0-5-1h-1-1 2l1-1h0z" class="b"></path><path d="M268 156c1 0 5 1 5 1v-1h4c4 1 7 3 10 4l1 1h1l1 1c0 2 0 3-1 4 2 1 3 3 5 4l1 1v2l-1-1-1 1v1h0v1c-2 1-3 1-4 1l-1-2h-1l-3-4h0l-2-1c-1-2-4-4-6-5l-10-7 1-1h1z" class="M"></path><path d="M284 170c0-2-2-4-3-6l1 1 2 2 2 2c1 1 2 4 2 5h-1l-3-4z" class="K"></path><path d="M288 167h0c1 2 2 4 2 6h1l2 1v1c-2 1-3 1-4 1l-1-2c0-1-1-4-2-5l-2-2 2 1h2v-1z" class="N"></path><path d="M287 165l2 1c2 1 3 3 5 4l1 1v2l-1-1-1 1v1h0l-2-1h-1c0-2-1-4-2-6h0l-1-2z" class="O"></path><path d="M291 170c1 1 2 1 3 2l-1 1v1h0l-2-1v-3z" class="B"></path><path d="M288 167c2 0 2 0 3 1v2 3h-1c0-2-1-4-2-6z" class="i"></path><path d="M273 157v-1h4c4 1 7 3 10 4l1 1h1l1 1c0 2 0 3-1 4l-2-1h0c-4-4-9-6-14-8z" class="l"></path><path d="M177 206l12-11c1 1 2 1 3 2l-1 2v3 3 2c0 1 0 2-1 3h2l1 1v1h1c1 0 1-1 2-2l-1-1 2 1 2 5 2-2 1 1c-1 2-3 3-4 4h3 0l-1 1v1c-1 2-2 2-4 4l1 1c-1 1-3 3-3 5 0 1 0 2-1 3h0v4c-1 0-2 1-3 2v1l-4 3h-2l-4 5c0-1 0-1-1-1h-1l-1-1h-1c0-2-1-4-1-6 0-1-1-2-1-3l-3-6c0-1 0-3 1-4v-2h0l1-3c0-1 0-1 1-2h0l-5-2s0-1-1-1l1-2 3-4 5-5z" class="P"></path><path d="M175 240l4 7h-1l-1-1h-1c0-2-1-4-1-6z" class="N"></path><path d="M174 220h1l1 1v1l1-1 1 1h-1v2c-1 1 0 2 0 3l3 13 1 1h0c-1 1-1 2-2 3 0-2-1-4-1-5h-1-1c0-2-1-3-2-5-1-3-1-6-2-9l1-3c0-1 0-1 1-2z" class="I"></path><path d="M174 220h1l1 1v1 3c-1 0-1 1-1 1-1 1 2 11 3 13h-1-1c0-2-1-3-2-5-1-3-1-6-2-9l1-3c0-1 0-1 1-2z" class="Q"></path><defs><linearGradient id="CB" x1="197.964" y1="229.326" x2="182.189" y2="238.421" xlink:href="#B"><stop offset="0" stop-color="#0c0b0e"></stop><stop offset="1" stop-color="#464644"></stop></linearGradient></defs><path fill="url(#CB)" d="M200 220c-1 2-2 2-4 4l1 1c-1 1-3 3-3 5 0 1 0 2-1 3h0v4c-1 0-2 1-3 2v1l-4 3h-2l5-8v-2l1-3c1-1 1 0 1-1v-1-2-1l-2 1 1-1-1-1h0l11-4z"></path><path d="M190 239v-1c0-2 2-4 3-5v4c-1 0-2 1-3 2z" class="E"></path><path d="M200 220c-1 2-2 2-4 4-3 2-4 5-6 8v1c0 1-1 1-1 2v-2l1-3c1-1 1 0 1-1v-1-2-1l-2 1 1-1-1-1h0l11-4z" class="R"></path><path d="M177 224c2 2 5 2 8 1 1 0 3 0 4-1l1 1-1 1c-4 4-6 9-8 15l-1-1-3-13c0-1-1-2 0-3z" class="J"></path><defs><linearGradient id="CC" x1="188.583" y1="216.622" x2="191.903" y2="221.274" xlink:href="#B"><stop offset="0" stop-color="#414041"></stop><stop offset="1" stop-color="#585858"></stop></linearGradient></defs><path fill="url(#CC)" d="M195 209l2 1 2 5 2-2 1 1c-1 2-3 3-4 4h3 0l-1 1v1l-11 4h0c-1 1-3 1-4 1-3 1-6 1-8-1v-2h1l-1-1c2-2 3-4 5-6l4-4c1 1 1 1 2 1l2-2h2l1 1v1h1c1 0 1-1 2-2l-1-1z"></path><path d="M195 209l2 1 2 5c-1 1-3 2-4 2h-1-2c-1 0-3 1-4 2v-1h-1l6-6h1c1 0 1-1 2-2l-1-1z" class="T"></path><path d="M192 217c1-1 2-3 3-3v2l-1 1h-2z" class="B"></path><path d="M198 218h3 0l-1 1v1l-11 4h0c-1 1-3 1-4 1-3 1-6 1-8-1v-2h1l2 1c5 1 13-2 18-5z" class="m"></path><path d="M188 212l2-2h2l1 1v1l-6 6-2 1c-1 1-2 2-4 3-1 0-1 0-1 1l-2-1-1-1c2-2 3-4 5-6l4-4c1 1 1 1 2 1z" class="a"></path><path d="M185 219c1-3 3-3 4-5s1-3 3-4l1 1v1l-6 6-2 1z" class="h"></path><path d="M182 215l4-4c1 1 1 1 2 1-2 2-7 6-8 9l1 1c-1 0-1 0-1 1l-2-1-1-1c2-2 3-4 5-6z" class="E"></path><path d="M177 206l12-11c1 1 2 1 3 2l-1 2v3 3 2c0 1 0 2-1 3l-2 2c-1 0-1 0-2-1l-4 4c-2 2-3 4-5 6l-1 1v-1l-1-1h-1 0l-5-2s0-1-1-1l1-2 3-4 5-5z" class="L"></path><path d="M186 211c1-1 2-3 4-5l1 1c0 1 0 2-1 3l-2 2c-1 0-1 0-2-1z" class="C"></path><path d="M169 215h1v2c1 1 2 1 3 1h4c-2 1-2 2-3 2l-5-2s0-1-1-1l1-2z" class="B"></path><path d="M190 200l1-1v3c0 1-1 2-1 2 0 1-3 2-4 3h-1v-2c0-1 1-1 0-3h0c2-1 3-2 5-2z" class="F"></path><path d="M190 200l1-1v3c0 1-1 2-1 2 0 1-3 2-4 3 0-1 2-3 2-5 1-1 1-2 2-2z" class="O"></path><path d="M177 218s1-1 1-2c3-3 5-6 8-8 0 2-3 5-4 7h0c-2 2-3 4-5 6l-1 1v-1l-1-1h-1 0c1 0 1-1 3-2z" class="K"></path><path d="M185 202c1 2 0 2 0 3v2c-2 0-8 7-11 9 0 0 0 1-1 1h-1l-1-2c1-1 3-3 5-4l4-4c1-1 1-2 2-2l1-1c0-1 1-1 2-2z" class="i"></path><path d="M177 206l12-11c1 1 2 1 3 2l-1 2-1 1c-2 0-3 1-5 2h0c-1 1-2 1-2 2l-1 1c-1 0-1 1-2 2l-4 4c-2 1-4 3-5 4h-1-1l3-4 5-5z" class="h"></path><path d="M184 151c4-4 8-4 13-5l-1 1h-1l1 1h3v1h3c2 1 4 1 5 2h2l2-3h0c1 1 1 2 1 3l1-1v1l2-2 1 1c2 1 4 3 6 4l2 1-2 1v1l1 1 1-1 1 1c-1 0-2 1-2 1v1 1c-3 3-5 5-8 7l-3 2-5 3h-2c-1 0-2 1-2 2-1 0-2 1-3 1v1l-1 2c-2 1-4 1-7 1-6-2-10-5-13-10l-1-1h0v-6c1 0 1-1 2-1v-1l1 1c0-5 0-7 3-11z" class="n"></path><path d="M208 167c0 1 1 1 1 2v1l1-1 2 1-5 3h-2l3-6z" class="C"></path><path d="M184 156c2 0 2 0 3-1v1c-2 2-4 5-3 9v2h-1c0-1-1-2-1-3 0-3 0-6 2-8z" class="k"></path><path d="M194 149h0c2 1 3 0 5 1h0-1c-1 1-1 1-2 1-1 1-3 1-4 2-2 1-4 2-5 3v-1c-1 1-1 1-3 1v-1c3-3 6-5 10-6zm14 18h-5v-1c1-2 2-4 3-5-2-4-5-7-8-9h-1c2 0 4 1 6 2h1c0 1 1 1 2 2l1-1c1 1 1 4 1 5 0 3 0 4 1 6l-1 1z" class="D"></path><path d="M207 151h2l2-3h0c1 1 1 2 1 3l1-1v1 1c-1 1-1 2-1 2l-1 2v2l-1 4-2-2c0-1 0-4-1-5l-1 1c-1-1-2-1-2-2h-1l1-1c-3-2-5-2-8-2 1 0 1 0 2-1h1 0c-2-1-3 0-5-1h0 5 3c2 1 4 1 5 2z" class="O"></path><path d="M211 156h-2v-1l1-1h2l-1 2z" class="N"></path><path d="M202 149c2 1 4 1 5 2v1 1 1c-2 0-2-2-3-2-1-1-1-2-2-3z" class="b"></path><defs><linearGradient id="CD" x1="179.235" y1="159.411" x2="195.295" y2="147.592" xlink:href="#B"><stop offset="0" stop-color="#c5c4c6"></stop><stop offset="1" stop-color="#f6f4f7"></stop></linearGradient></defs><path fill="url(#CD)" d="M184 151c4-4 8-4 13-5l-1 1h-1l1 1h3v1h-5c-4 1-7 3-10 6v1c-2 2-2 5-2 8 0 1 1 2 1 3h1c2 2 4 5 5 7h-2c-4-3-6-8-6-12 0-5 0-7 3-11z"></path><defs><linearGradient id="CE" x1="187.591" y1="177.585" x2="190.553" y2="167.537" xlink:href="#B"><stop offset="0" stop-color="#424242"></stop><stop offset="1" stop-color="#5d5b5d"></stop></linearGradient></defs><path fill="url(#CE)" d="M180 162v-1l1 1c0 4 2 9 6 12h2c2 2 4 3 6 3s4 0 5-1v1l-1 2c-2 1-4 1-7 1-6-2-10-5-13-10l-1-1h0v-6c1 0 1-1 2-1z"></path><path d="M180 162c0 1 0 3-1 5-1 1-1 1-1 2h0v-6c1 0 1-1 2-1z" class="G"></path><path d="M213 151l2-2 1 1c2 1 4 3 6 4l2 1-2 1v1l1 1 1-1 1 1c-1 0-2 1-2 1v1 1c-3 3-5 5-8 7l-3 2-2-1-1 1v-1c0-1-1-1-1-2h0l1-1c-1-2-1-3-1-6l2 2 1-4v-2l1-2s0-1 1-2v-1z" class="T"></path><path d="M218 157v-1h1v2h-1v-1z" class="B"></path><path d="M224 157l1 1c-1 0-2 1-2 1-5 3-9 6-13 10l-1 1v-1c2-2 3-4 5-5l2-2v-1c2 0 4-1 5-2s1-1 2-1l1-1z" class="O"></path><path d="M218 157v1h1c-2 1-4 3-5 4s-2 3-4 3l-1 1h0c-1-2-1-3-1-6l2 2s0 1-1 2h0l1 1c1-2 2-4 4-5l2-2 2-1z" class="Y"></path><path d="M223 159v1 1c-3 3-5 5-8 7l-3 2-2-1c4-4 8-7 13-10z" class="g"></path><path d="M213 151l2-2 1 1c2 1 4 3 6 4l-2 2c-1 0-2-1-2-1-1 0-1 0-2 1v2l-2 2c-2 1-3 3-4 5l-1-1h0c1-1 1-2 1-2l1-4v-2l1-2s0-1 1-2v-1z" class="D"></path><path d="M213 152c0 1 1 1 1 1 0 2-2 3-3 5v-2l1-2s0-1 1-2z" class="V"></path><path d="M214 160c-1-2-1-2-1-4h2c0-1 1-1 1-2l-1-1v-3h1c2 1 4 3 6 4l-2 2c-1 0-2-1-2-1-1 0-1 0-2 1v2l-2 2z" class="g"></path><path d="M250 156h1c0 1 1 1 1 2h1 0c1-1 2-1 4-1v1l2-1 1-1 2 1h2 0 2l10 7c2 1 5 3 6 5l2 1h0l3 4h1l1 2c2 2 4 4 5 6-1 1-1 1-1 2 2 3 4 4 4 8h-1l-2-1h0v1h-1c-2-1-3-1-4-2l-3-2c-1-1-1-1-1-2-3-1-7-3-10-6h-2l-1 1 2 1c-2 1-4 0-6-1h-6l-1 2h0c0 3-3 6-4 8l-1 1c1 0 1 0 2 1 1-1 2-3 3-5 1 2 1 3 1 4-1 0-1 0-2 1v1c-1 2-3 4-4 6h-1c0 1-1 2-2 3l-1 1c0 1 0 1 1 1l-1 3c-1-1-2-1-3-1h0v2l-15 9h0c-3 1-4 1-7 1-5 3-10 5-15 7-2 1-4 2-5 4-2 1-4 2-7 4 0 0-1 0-1 1h-1-1v1h1v1h-2v-1l-1 1h-2v-4h0c1-1 1-2 1-3 0-2 2-4 3-5l-1-1c2-2 3-2 4-4v-1l1-1h0-3c1-1 3-2 4-4l-1-1c-1-1-2-2-2-4v-2-1-1l1 1v-1c1 0 2 0 2 1s0 1 1 1v1h1c1 0 2 1 3 1 2-1 3-2 5-4h0l1-2 2 2c1 0 2-1 3-1v-1l-1-2h0v-2l-2-1h1v-5-1c1-4 3-7 6-10h-2l1-1c-2 0-3 0-4 1 0-2 2-3 3-4 2-1 3-2 4-2s1 0 2-1c-1 0 0 0 0 0l2-2 4-4c1-1 2-3 4-3l1 1v-1l2-1 2-2h-1c2-2 6-3 8-5 0-1 1-1 2-2z" class="M"></path><path d="M249 164c1-1 2-1 3 0h1l-4 1v-1z" class="D"></path><path d="M252 166l2 1c-1 0-1 1-1 1h-2 0l-2 1v-1l3-2z" class="E"></path><path d="M261 171l3 2-3 1c-2 0-4-1-6-1h4v-1c-1 0-1-1-2-1 1-1 2 0 3 0h1z" class="V"></path><path d="M252 166c2 0 3-1 4 0 1 0 1-1 2-1v1c-1 1-1 3-3 4l-2 1h-2v-1h0c1-1 2-1 2-2h0s0-1 1-1l-2-1z" class="Z"></path><path d="M245 168h4v1l2-1v1h1-1c-2 1-3 2-5 3h-4l-1 1c-1 0-2 0-4 1h0c-1-1-1-1-2-1l8-3v-1l2-1z" class="S"></path><path d="M245 168h4v1c-2 1-4 1-6 1v-1l2-1z" class="C"></path><defs><linearGradient id="CF" x1="244.298" y1="179.026" x2="261.244" y2="171.679" xlink:href="#B"><stop offset="0" stop-color="#555253"></stop><stop offset="1" stop-color="#7a7a7b"></stop></linearGradient></defs><path fill="url(#CF)" d="M243 174c4-1 8-1 12-1 2 0 4 1 6 1 1 1 3 1 3 3h-2c-2 0-4-1-6-1-3-1-6-1-9-1h-4c-2 0-3 2-6 1l6-2z"></path><path d="M261 171c0-1-1-2-1-3 1 0 2 1 3 1 3 1 7 3 10 5l6 3v1c-2-1-3-1-5-1h-1c0 1-3 0-3 0-2-1-4-3-6-4l-3-2z" class="F"></path><path d="M268 174h5l6 3v1c-2-1-3-1-5-1-1-1-2-1-3-1s-2-1-3-2z" class="B"></path><path d="M263 169c3 1 7 3 10 5h-5c-2-1-4-3-5-5z" class="Y"></path><path d="M249 164v1c-1 0-2 0-3 1l1 1h2-2c-1 0-1 1-2 1l-2 1c-4 1-9 4-13 4h0-2l4-4c1-1 2-3 4-3l1 1v-1l2-1c2 1 7 0 10-1z" class="e"></path><path d="M232 169c1-1 2-3 4-3l1 1-4 3-3 3h-2l4-4z" class="G"></path><path d="M250 156h1c0 1 1 1 1 2h1 0c1-1 2-1 4-1v1l2-1 1-1 2 1h2 0 2l10 7c-1 0-2 1-3 1l-1-1h-1l1 1h-1 0-5-2-1v1c-2-1-5-2-7-3h0c-2-1-3-2-5-3-1-2-1-2-1-4z" class="Z"></path><path d="M262 163l-2-2c-1 0 0 0-1-1h1l2 1c1 1 2 1 3 3h-1c-1 0-1-1-2-1z" class="T"></path><path d="M259 157l-1 2c-1 0-2 1-2 2h-2c0-1-1-1-1-2 1-1 3-1 4-1l2-1z" class="e"></path><path d="M260 156l2 1h2v1l1 2s0 1-1 1l-1-1h0c-2-1-3-1-5-1l1-2 1-1z" class="D"></path><path d="M263 160l1 1 1 1c2 2 3 2 5 2h1v1h0-5-2-1v1c-2-1-5-2-7-3 1-1 1-1 3-1h1c1 1 1 1 2 1s1 1 2 1h1c-1-2-2-2-3-3l1-1z" class="V"></path><path d="M264 157h0 2l10 7c-1 0-2 1-3 1l-1-1h-1l1 1h-1 0 0v-1h-1c-2 0-3 0-5-2l-1-1c1 0 1-1 1-1l-1-2v-1z" class="B"></path><path d="M264 158c2 1 3 2 4 3l-3 1-1-1c1 0 1-1 1-1l-1-2z" class="T"></path><path d="M265 162l3-1 3 3h-1c-2 0-3 0-5-2z" class="M"></path><path d="M230 173h0c4 0 9-3 13-4v1l-8 3c1 0 1 0 2 1h0c2-1 3-1 4-1l1-1h4c-1 0-2 1-3 2l-6 2-6 3c-4 3-9 6-12 10h0c0 1-1 2-1 3h-2c1-4 3-7 6-10h-2l1-1c-2 0-3 0-4 1 0-2 2-3 3-4 2-1 3-2 4-2s1 0 2-1c-1 0 0 0 0 0l2-2h2z" class="D"></path><path d="M230 173h0c4 0 9-3 13-4v1l-8 3c-3 1-6 3-9 5l-4 4h-2l1-1c-2 0-3 0-4 1 0-2 2-3 3-4 2-1 3-2 4-2s1 0 2-1c-1 0 0 0 0 0l2-2h2z" class="T"></path><path d="M226 175l3-1h0c-2 3-6 5-8 7-2 0-3 0-4 1 0-2 2-3 3-4 2-1 3-2 4-2s1 0 2-1z" class="j"></path><defs><linearGradient id="CG" x1="288.194" y1="171.258" x2="281.613" y2="183.813" xlink:href="#B"><stop offset="0" stop-color="#5d5c5d"></stop><stop offset="1" stop-color="#7b7a7a"></stop></linearGradient></defs><path fill="url(#CG)" d="M271 165h0 1l-1-1h1l1 1c1 0 2-1 3-1 2 1 5 3 6 5l2 1h0l3 4h1l1 2c2 2 4 4 5 6-1 1-1 1-1 2 2 3 4 4 4 8h-1l-2-1h0v1h-1c-2-1-3-1-4-2l-3-2c-1-1-1-1-1-2-3-1-7-3-10-6h-2l-1 1c-2-2-4-2-7-2v-1c2-1 3 0 5-1 0 0 3 1 3 0h1c2 0 3 0 5 1v-1l1-1c-1 0-1-1-1-2-3-3-8-5-11-7-2 0-3-1-5-2h1 2 5z"></path><path d="M289 190v-1-1h0 1c1 1 3 2 4 3h0v1h-1c-2-1-3-1-4-2z" class="a"></path><path d="M284 170l3 4 1 5c-1-1-1-3-2-4-2-1-3-3-4-4v-2l2 1h0z" class="b"></path><path d="M288 174l1 2c2 2 4 4 5 6-1 1-1 1-1 2l-5-5-1-5h1z" class="F"></path><path d="M268 167v-1c3 0 8 3 11 5 2 1 3 3 4 5l-1 1-3-3c-3-3-8-5-11-7z" class="i"></path><path d="M279 174l3 3c0 2 7 8 9 10l-1 1h-1 0v1 1l-3-2c-1-1-1-1-1-2-3-1-7-3-10-6h-2l-1 1c-2-2-4-2-7-2v-1c2-1 3 0 5-1 0 0 3 1 3 0h1c2 0 3 0 5 1v-1l1-1c-1 0-1-1-1-2z" class="c"></path><path d="M273 180c0-1-1-1-2-2 3 0 5 0 7 2h-3-2z" class="Y"></path><path d="M278 180l6 4 1 1v1c-3-1-7-3-10-6h3z" class="T"></path><path d="M279 174l3 3c0 2 7 8 9 10l-1 1-11-10v-1l1-1c-1 0-1-1-1-2z" class="Y"></path><path d="M264 173c2 1 4 3 6 4-2 1-3 0-5 1v1c3 0 5 0 7 2l2 1c-2 1-4 0-6-1h-6c-3 0-4 1-5 2l-4 4c0 1-2 3-2 4-1 0-4 3-4 4-1 0-3 2-4 3h-2v-2c2-1 4-3 6-4h-2c0-1-1-1-1-1l-5 2c-1 0-3 1-4 1l-4 3c-1 1-2 1-3 2s-1 1-2 1h0c-2 1-4 2-6 2 1 1 2 0 3 1-1 0-1 1-1 1-2 1-5 2-7 2v-1c1 0 2-1 3-1v-1l-1-2h0v-2l-2-1h1v-5-1h2c0-1 1-2 1-3h0c3-4 8-7 12-10l6-3c3 1 4-1 6-1h4c3 0 6 0 9 1 2 0 4 1 6 1h2c0-2-2-2-3-3l3-1z" class="D"></path><path d="M249 189c-1 1-1 2-2 3h0-2c0-1-1-1-1-1 1-1 3-2 5-2z" class="N"></path><path d="M231 186c1 0 2-1 3-1l1 1-3 4h-1v-3-1h0z" class="o"></path><path d="M232 193c3-2 5-3 9-5-1 2-4 3-5 4 1 1 2 1 3 1-1 0-3 1-4 1l-4 3c-1 1-2 1-3 2 0-1 0-2 1-3l1-3h2z" class="R"></path><path d="M229 196l1-3h2c-1 2-1 3-1 4-1 1-2 1-3 2 0-1 0-2 1-3z" class="E"></path><defs><linearGradient id="CH" x1="247.171" y1="187.211" x2="244.222" y2="197.817" xlink:href="#B"><stop offset="0" stop-color="#4d4c4d"></stop><stop offset="1" stop-color="#717171"></stop></linearGradient></defs><path fill="url(#CH)" d="M249 189c1-1 2-2 2-3 1-1 1-2 1-2 1-1 2-1 3-1 0 1-1 2-2 3v1c0 1-2 3-2 4-1 0-4 3-4 4-1 0-3 2-4 3h-2v-2c2-1 4-3 6-4h0c1-1 1-2 2-3z"></path><defs><linearGradient id="CI" x1="240.514" y1="177.014" x2="235.309" y2="185.188" xlink:href="#B"><stop offset="0" stop-color="#444442"></stop><stop offset="1" stop-color="#5a585b"></stop></linearGradient></defs><path fill="url(#CI)" d="M233 181h2c3-2 5-2 9-2l2 1c-2 1-4 1-5 2-1 0-3 1-3 1l-3 3-1-1c-1 0-2 1-3 1h0v1h-1c0-1 0-1-1-2l1-1-1-1 4-2z"></path><path d="M230 184h1v2 1h-1c0-1 0-1-1-2l1-1z" class="R"></path><path d="M231 186l1-1c1-2 1-2 3-2 1-1 2 0 3 0l-3 3-1-1c-1 0-2 1-3 1z" class="F"></path><path d="M264 173c2 1 4 3 6 4-2 1-3 0-5 1v1c-5 1-10 0-15 0-1 1-3 1-4 1l-2-1c-4 0-6 0-9 2h-2l7-4c1 0 2-1 4-1h0c2 0 4 1 6 1h1c1-1 3-1 5-1s4 1 6 1h2c0-2-2-2-3-3l3-1z" class="O"></path><path d="M244 179c2-1 3-1 5 0h1c-1 1-3 1-4 1l-2-1z" class="B"></path><path d="M251 177c1-1 3-1 5-1s4 1 6 1l-1 1c-4 0-7 0-10-1z" class="T"></path><path d="M237 176c3 1 4-1 6-1h4c3 0 6 0 9 1-2 0-4 0-5 1h-1c-2 0-4-1-6-1h0c-2 0-3 1-4 1l-7 4-4 2c-5 4-8 7-10 13v1l-1 6v1-1l-1-2h0v-2l-2-1h1v-5-1h2c0-1 1-2 1-3h0c3-4 8-7 12-10l6-3z" class="V"></path><path d="M219 189c1 0 1 0 1 1-1 2-2 4-2 5l1 1v1h-2 0c-1-1 0-4 1-5 0-1 1-2 1-3z" class="G"></path><path d="M218 192c-1 1-2 4-1 5h0 2l-1 6v1-1l-1-2h0v-2l-2-1h1v-5-1h2z" class="R"></path><path d="M229 183l1 1-1 1c1 1 1 1 1 2h1v3h1l-2 3-1 3c-1 1-1 2-1 3-1 1-1 1-2 1h0c-2 1-4 2-6 2 1 1 2 0 3 1-1 0-1 1-1 1-2 1-5 2-7 2v-1c1 0 2-1 3-1v-1l1-6v-1c2-6 5-9 10-13z" class="I"></path><path d="M220 199v-2h2c0-4 1-6 3-9l1 2c-1 1-1 0-1 1-1 1-1 3-1 4l-1 1h0l-3 3z" class="B"></path><path d="M223 196l1 1c1 1 1 2 2 3-2 1-4 2-6 2v-3l3-3z" class="D"></path><path d="M227 186l2-1c1 1 1 1 1 2v1l-3 5-2 3-1 1-1-1h0l1-1c0-1 0-3 1-4 0-1 0 0 1-1l-1-2c1-1 2-1 2-2z" class="E"></path><path d="M225 188c1-1 2-1 2-2 0 2 0 4-1 6-1 1-2 2-2 3 0-1 0-3 1-4 0-1 0 0 1-1l-1-2z" class="Y"></path><path d="M230 187h1v3h1l-2 3-1 3c-1 1-1 2-1 3-1 1-1 1-2 1h0c-1-1-1-2-2-3l1-1 2-3 3-5v-1z" class="O"></path><path d="M227 193v3c-1 0-1 1-2 0h0l2-3z" class="G"></path><path d="M230 187h1v3h1l-2 3-1 3h-1c1-3 2-6 2-8v-1z" class="F"></path><path d="M253 187l4-4c1-1 2-2 5-2l-1 2h0c0 3-3 6-4 8l-1 1c1 0 1 0 2 1 1-1 2-3 3-5 1 2 1 3 1 4-1 0-1 0-2 1v1c-1 2-3 4-4 6h-1c0 1-1 2-2 3l-1 1c0 1 0 1 1 1l-1 3c-1-1-2-1-3-1h0v2l-15 9h0c-3 1-4 1-7 1-5 3-10 5-15 7-2 1-4 2-5 4-2 1-4 2-7 4 0 0-1 0-1 1h-1-1v1h1v1h-2v-1l-1 1h-2v-4h0c1-1 1-2 1-3 0-2 2-4 3-5l-1-1c2-2 3-2 4-4v-1l1-1h0-3c1-1 3-2 4-4l-1-1c-1-1-2-2-2-4v-2-1-1l1 1v-1c1 0 2 0 2 1s0 1 1 1v1h1c1 0 2 1 3 1 2-1 3-2 5-4h0l1-2 2 2v1c2 0 5-1 7-2 0 0 0-1 1-1-1-1-2 0-3-1 2 0 4-1 6-2h0c1 0 1 0 2-1s2-1 3-2l4-3c1 0 3-1 4-1l5-2s1 0 1 1h2c-2 1-4 3-6 4v2h2c1-1 3-3 4-3 0-1 3-4 4-4 0-1 2-3 2-4z" class="f"></path><path d="M199 227l1-1v1h0c-1 2-2 4-2 6v-1l1-1-1 4h-1v1h1v1h-2v-1c0-3 2-7 3-9z" class="d"></path><path d="M230 207c2-1 3-1 5-1-3 2-5 4-8 5-1 0-3 1-5 1l8-5z" class="H"></path><path d="M194 230h0c1-1 1-2 2-3s2-1 3-1v1c-1 2-3 6-3 9l-1 1h-2v-4h0c1-1 1-2 1-3z" class="j"></path><path d="M251 191h0c-1 1-2 3-2 4h1c-1 1-1 2-1 3-4 3-10 5-14 8-2 0-3 0-5 1 1-2 2-3 4-3l9-6c1-1 3-3 4-3 0-1 3-4 4-4z" class="I"></path><path d="M247 195l1 1c-3 4-8 6-13 8h-1l9-6c1-1 3-3 4-3z" class="o"></path><defs><linearGradient id="CJ" x1="258.39" y1="181.959" x2="251.311" y2="192.54" xlink:href="#B"><stop offset="0" stop-color="#444343"></stop><stop offset="1" stop-color="#6d6c6d"></stop></linearGradient></defs><path fill="url(#CJ)" d="M253 187l4-4c1-1 2-2 5-2l-1 2h0c0 3-3 6-4 8l-1 1c1 0 1 0 2 1l-2 2-1 1h0c-1 0-1-1-2-1s-3 2-4 3c0-1 0-2 1-3h-1c0-1 1-3 2-4h0c0-1 2-3 2-4z"></path><path d="M261 183h0c0 3-3 6-4 8l-1-1c-1 0-2 1-3 2 2-3 5-7 8-9z" class="I"></path><path d="M253 192c1-1 2-2 3-2l1 1-1 1c1 0 1 0 2 1l-2 2-1 1h0c-1 0-1-1-2-1s-3 2-4 3c0-1 0-2 1-3l3-3z" class="L"></path><path d="M256 195l-1-1c0-1 0-1 1-2 1 0 1 0 2 1l-2 2z" class="I"></path><defs><linearGradient id="CK" x1="226.56" y1="212.363" x2="219.49" y2="205.834" xlink:href="#B"><stop offset="0" stop-color="#737073"></stop><stop offset="1" stop-color="#888887"></stop></linearGradient></defs><path fill="url(#CK)" d="M241 196v2h2l-9 6c-2 0-3 1-4 3-2 1-5 3-8 5-2 1-5 2-8 4-2 1-4 3-5 3-1 1-3 1-4 1 9-9 21-14 32-21l4-3z"></path><path d="M241 196v2l-3 2h0l-1-1 4-3z" class="c"></path><path d="M239 193l5-2s1 0 1 1h2c-2 1-4 3-6 4l-4 3c-11 7-23 12-32 21-2 2-4 4-5 6l-1 1v-1c-1 0-2 0-3 1s-1 2-2 3h0c0-2 2-4 3-5l-1-1c2-2 3-2 4-4v-1l1-1h0-3c1-1 3-2 4-4l-1-1c-1-1-2-2-2-4v-2-1-1l1 1v-1c1 0 2 0 2 1s0 1 1 1v1h1c1 0 2 1 3 1 2-1 3-2 5-4h0l1-2 2 2v1c2 0 5-1 7-2 0 0 0-1 1-1-1-1-2 0-3-1 2 0 4-1 6-2h0c1 0 1 0 2-1s2-1 3-2l4-3c1 0 3-1 4-1z" class="C"></path><path d="M239 193l5-2s1 0 1 1l-8 4-1-1-1-1c1 0 3-1 4-1z" class="B"></path><path d="M235 194l1 1 1 1c-2 2-5 3-8 4-1 1-3 2-4 3-1 0-2 1-3 1 0 0 0-1 1-1-1-1-2 0-3-1 2 0 4-1 6-2h0c1 0 1 0 2-1s2-1 3-2l4-3z" class="G"></path><defs><linearGradient id="CL" x1="218.438" y1="206.971" x2="199.675" y2="212.114" xlink:href="#B"><stop offset="0" stop-color="#666566"></stop><stop offset="1" stop-color="#939292"></stop></linearGradient></defs><path fill="url(#CL)" d="M222 204c1 0 2-1 3-1-3 3-7 4-10 6-5 3-9 8-15 10l1-1h0-3c1-1 3-2 4-4l-1-1c-1-1-2-2-2-4v-2-1-1l1 1v-1c1 0 2 0 2 1s0 1 1 1v1h1c1 0 2 1 3 1 2-1 3-2 5-4h0l1-2 2 2v1c2 0 5-1 7-2z"></path><path d="M199 209v-2-1-1l1 1c0 1 0 2 1 3 0 2 1 3 1 5l-1-1c-1-1-2-2-2-4z" class="E"></path><path d="M213 203l2 2v1c-2 1-4 2-5 3s-2 1-3 1v-1c2-1 3-2 5-4h0l1-2z" class="L"></path><path d="M258 193c1-1 2-3 3-5 1 2 1 3 1 4-1 0-1 0-2 1v1c-1 2-3 4-4 6h-1c0 1-1 2-2 3l-1 1c0 1 0 1 1 1l-1 3c-1-1-2-1-3-1h0v2l-15 9h0c-3 1-4 1-7 1-5 3-10 5-15 7-2 1-4 2-5 4-2 1-4 2-7 4 0 0-1 0-1 1h-1l1-4 2-2c1-2 3-3 4-4 3-3 7-5 11-7 8-3 15-6 23-10 5-2 10-6 14-10l2-2h0l1-1 2-2z" class="g"></path><path d="M258 193c1-1 2-3 3-5 1 2 1 3 1 4-1 0-1 0-2 1v1c-1 2-3 4-4 6h-1 1 0c0-1 0-2 1-3h0c1-1 1-1 1-2h0v-1l-1 1-3 3h-1l2-2h0l1-1 2-2z" class="M"></path><defs><linearGradient id="CM" x1="243.852" y1="207.33" x2="240.952" y2="214.435" xlink:href="#B"><stop offset="0" stop-color="#242625"></stop><stop offset="1" stop-color="#3d3a3c"></stop></linearGradient></defs><path fill="url(#CM)" d="M230 217c2-1 4-2 5-3 3-1 9-6 11-6h1c1 0 1-1 2-1h0 0v2l-15 9h0c-3 1-4 1-7 1l3-2z"></path><path d="M201 229c0 1 0 1-1 2v1h1l1-1v-1c3-1 4-2 6-4 4-3 9-5 14-6 3-1 6-3 8-3l-3 2c-5 3-10 5-15 7-2 1-4 2-5 4-2 1-4 2-7 4 0 0-1 0-1 1h-1l1-4 2-2z" class="k"></path><path d="M384 192v1c1 1 1 1 1 2h1v1h0 1l-1 2 2 2h1c1 0 1-1 2-1h0c1 1 2 1 3 1v-1l1 1h0c-1 1-2 2-4 2h0c-1 2-1 3-2 4h0 1v1h1v1l1 1c0 1-1 2-1 3 0 3 0 5 1 8v1h1c1 0 1-1 1-1 1 1 1 2 1 3h0v1h0l1 1v2c0 1 1 1 1 1 0-1 1-1 1-2h1s0 1 1 1c1 2 1 1 2 0 1 0 2 1 2 1h1l2-1c1-1 3-4 3-5h0c1-1 4-5 5-5s2-1 2-1l10-6h2c-1 1-2 2-3 2h1c2-1 6-2 8-3v1c1 0 1 0 2 1h3l7-1h2c2 1 6 1 8 0l2 1c0 1 0 1-1 1h1c3 0 4 0 6 2 1 0 1 1 1 1h1c2 1 3 2 5 3s4 1 4 3l1 1c2 0 5 1 7 2 3 1 5 2 7 3 1 0 2 1 3 1 2 0 3 1 4 1 2 0 4 0 6 2 0 0 1 0 1 1 2 1 4 1 6 3 1 1 3 2 4 4l1 1-3 4s0 1-1 2h0v5h-1l-1 1 2 5c-2 0-3 0-4 1l-1 1 1 5 2 2h-1l2 2h0-1s-1-1-2-1v1h1c1 0 1 1 2 1l-6-2-6-3c-1 1-1 0-1 1l-5-4-2-2-1 1 9 47v7c-1-1-2-2-2-3h0c-1-3-2-5-3-7-2-8-6-16-11-22-3-3-7-5-10-7-2-2-3-2-5-3h0c-2 0-4-1-5-1h-2s0-1-1-1c-1 1-2 1-3 0h0-4-3c0-1 4-3 5-3l16-6c2-1 3-1 4-2 2-1 4-1 5-2 1 2 1 3 1 4v1l1 1v-2c-1-2-1-4-1-5h0c-2 1-3 2-5 2-4 1-9 0-14 0l-19 1h-75v21c0 2 0 6-1 9h0c-1-1-3-2-4-4v-1c-2-3-3-7-3-12-3-2-6-3-9-5v-1h-1c-2 1-7 3-9 4l1 2c-1 0-1 0-1-1h-3c-2-1-5-1-8-1l-15 1h-23c-5 0-11-1-16 0-6 1-13 2-19 5-8 4-13 10-19 17l-9 18-2 3h-1v-4l11-53s1-7 1-8h1c2-2 4-3 6-5h-2l3-4v-2c0-2 1-4 2-5h1 0c2 0 3-1 5-1s5 1 7 1c3 1 7 3 10 4 0 1 1 1 2 1v-1h2v-1c-1 0-3-1-4-2s-1-1-1-2c2-1 3 1 4 1l1-1-2-1-1-1v-1h2v-2h1l3 1h1 2 0c0-2 0-2-1-3h2c1 1 2 1 2 2l1 1h1c0-1 0 0 1-1v1l1 1 2 2 2 1c1 2 3 3 5 5l1 1c3-1 4 1 7 2h0c2 0 2 0 4 1 1 0 2 2 4 2 1 1 1 1 1 2l4 2c1 1 2 1 2 2h1 4 0c-1 0-1-1-2-1v-1l4 3c1 0 1-1 1-1 0-1 0-2-1-4l-2-2h2 0l-1-1v-2-1l3 3h1c1 0 1 0 2 1v1l1-1 1-1 1-2c1-1 1-2 2-4v-7h-1c0-1 0-1 1-2h1l-1 3c1 1 1 1 2 1l1-1 1-1v2c-1 1-2 6-1 8h0l1 1h1c1 0 1 1 2 1 0 1 1 1 1 2l-1 1h1l1 1v1c1 0 2 0 3-1h0 1c0-1 1-1 1-2h1c1-1 2-3 3-3h2c1-1 1-2 2-3h0 1c1 0 1-1 1-2 1-2 1-4 3-5l1 1v-1-3-2c1-2 1-5 2-7 1-1 3-1 3-2v-2h0c1-1 1-1 3-1 0-2 2-4 3-5 0-2 1-3 2-5h-1l2-6h1 1 1 1v-1c1-1 1-2 1-4z" class="n"></path><path d="M323 255c2-1 4-1 6 0 1 1 2 2 3 2v1h-1l-1-1c0-1-2-2-2-2-3 0-6 1-8 0h0 3z" class="s"></path><path d="M346 246c1 0 2 0 3-1v2c0 2-1 2-2 3h0-9 5 3v-2-2z" class="f"></path><path d="M350 245h1c0 2 0 3 1 5h4-9 0c1-1 2-1 2-3v-2h0 1z" class="a"></path><path d="M371 248c0-1 1-1 2-2s2-1 3-3v1l2 1-1 1-1 1v3h2 13c-2 1-5 0-7 0h-16c1 0 2-1 3-2z" class="K"></path><path d="M376 250h-7l7-3v3z" class="F"></path><path d="M351 266c2 1 5 2 7 4 2 1 3 2 4 3 2 1 3 2 5 3-2-1-4-1-6-2 1 6 2 11 6 15h0c-1-1-3-2-4-4v-1c-2-3-3-7-3-12-3-2-6-3-9-5v-1h0z" class="W"></path><path d="M381 248h0l4-1c1 0 1-1 2-1l1-2c0-1 0-3-1-4l1-1h0l1 2 1 1-1 1c0 2 0 4-1 6v1c1 0 1 0 2-1h0c0 1 1 0 1 1h1 7c4 0 9-1 13 0h-21-13 1 1l1-2z" class="Z"></path><path d="M355 240h2c0 1-1 2-1 3s1 2 1 2c1 1 2 3 4 4l-1 1h5-9-4c-1-2-1-3-1-5h-1c0-1 1-1 1-2h1c1-1 2-3 3-3z" class="Y"></path><path d="M360 250h-3c0-2-1-4 0-5 1 1 2 3 4 4l-1 1z" class="E"></path><path d="M355 240h2c0 1-1 2-1 3-1 1-1 1-1 2h-1c-1 0-1-1-2-1l-1-1h1c1-1 2-3 3-3z" class="O"></path><path d="M341 259h22l-7 4c-2 1-4 1-5 3h0-1 0c0-1 0-1-1-2h-1l-3-2-1-1c-1 0-2-1-3-2z" class="m"></path><path d="M293 245c1 0 2 1 3 1v1 2 1h4-34c3-1 7 0 11-1l-5-2 1-1h1c3-1 4 2 7 2h0 7 0c2-2 2-2 4-2 1 0 1-1 1-1z" class="b"></path><path d="M274 246c3-1 4 2 7 2h0c1 0 1 1 2 2h0-4c-1-1-4-4-5-4z" class="B"></path><path d="M388 226v2h0c1 2 2 5 0 7h0c0 1 1 4 0 4h0l-1 1c1 1 1 3 1 4l-1 2c-1 0-1 1-2 1l-4 1h0l-1 2h-1-1-2v-3l1-1 1-1 2-2s1-2 2-3l-1-3-1-2c1-1 1-2 1-3h0l1-1h2 0c2 0 1-2 2-3h1v-1l1-1z" class="k"></path><path d="M376 247l1-1c1 1 1 2 2 4h-1-2v-3z" class="h"></path><path d="M388 226v2h0c1 2 2 5 0 7l-1-2h-1v3l-1 1c-1-2-1-2-2-3h0c-1 1-1 1-1 2v4l-1-3-1-2c1-1 1-2 1-3h0l1-1h2 0c2 0 1-2 2-3h1v-1l1-1z" class="c"></path><defs><linearGradient id="CN" x1="388.365" y1="232.728" x2="387.209" y2="227.391" xlink:href="#B"><stop offset="0" stop-color="#7c7b7c"></stop><stop offset="1" stop-color="#959494"></stop></linearGradient></defs><path fill="url(#CN)" d="M388 226v2h0c1 2 2 5 0 7l-1-2v-1c0-1 0-2-1-3v1 3h-1l-1-2h0c2 0 1-2 2-3h1v-1l1-1z"></path><path d="M385 237l1-1v-3h1l1 2h0c0 1 1 4 0 4h0l-1 1c1 1 1 3 1 4l-1 2c-1 0-1 1-2 1l-4 1h0l1-2c0-1 2-3 3-5v-4z" class="B"></path><path d="M385 237l1-1v-3h1l1 2h0-1c-1 3 0 5-2 8v1c0 1-2 2-3 3v-1c0-1 2-3 3-5v-4z" class="O"></path><path d="M248 249h-2 0c1-3 7-6 9-7 3-1 7-2 10-1 1 1 2 1 3 3l2 2 2 1 5 2c-4 1-8 0-11 1h-12l-6-1z" class="T"></path><defs><linearGradient id="CO" x1="256.032" y1="244.499" x2="273.28" y2="252.002" xlink:href="#B"><stop offset="0" stop-color="#2d2d2e"></stop><stop offset="1" stop-color="#4c4c4c"></stop></linearGradient></defs><path fill="url(#CO)" d="M265 241c1 1 2 1 3 3l2 2 2 1 5 2c-4 1-8 0-11 1h-12l1-1v-1h0-2l1-1h2 1c3-1 6-2 10-2h-1c-1-1-3-1-4-1h0 5v-1c-1-1-1-1-2-1v-1z"></path><path d="M336 229c0-1 0-1 1-2h1l-1 3c1 1 1 1 2 1l1-1 1-1v2c-1 1-2 6-1 8h0l1 1h1c1 0 1 1 2 1 0 1 1 1 1 2l-1 1h1l1 1v1 2 2h-3-5-27 2c1 0 3-1 4-1h2l-1-1v-1c1 1 2 1 2 2h1 4 0c-1 0-1-1-2-1v-1l4 3c1 0 1-1 1-1 0-1 0-2-1-4l-2-2h2 0l-1-1v-2-1l3 3h1c1 0 1 0 2 1v1l1-1 1-1 1-2c1-1 1-2 2-4v-7h-1z" class="V"></path><path d="M338 239c0 1 1 2 1 3-1 0-2 0-3-1 0-1 1-2 2-2z" class="O"></path><path d="M332 244l1-1h0c1 2 1 3 3 4 0 1 0 1 1 1 0 1 0 1-1 2h-5c0-2 0-4 1-6h0z" class="Z"></path><path d="M326 239l3 3h1c1 0 1 0 2 1v1h0c-1 2-1 4-1 6h-2-16c1 0 3-1 4-1h2l-1-1v-1c1 1 2 1 2 2h1 4 0c-1 0-1-1-2-1v-1l4 3c1 0 1-1 1-1 0-1 0-2-1-4l-2-2h2 0l-1-1v-2-1z" class="e"></path><path d="M329 250c1-2 1-2 1-4 0-1-1-1-1-1 0-1 2-1 3-1-1 2-1 4-1 6h-2z" class="M"></path><defs><linearGradient id="CP" x1="339.797" y1="246.064" x2="344.1" y2="235.076" xlink:href="#B"><stop offset="0" stop-color="#171818"></stop><stop offset="1" stop-color="#484645"></stop></linearGradient></defs><path fill="url(#CP)" d="M336 229c0-1 0-1 1-2h1l-1 3c1 1 1 1 2 1l1-1 1-1v2c-1 1-2 6-1 8h0l1 1h1c1 0 1 1 2 1 0 1 1 1 1 2l-1 1h1l1 1v1 2 2h-3c-1-2-3-4-4-6l1-1-1-1c0-1-1-2-1-3-1 0-2 1-2 2l-1 1h-1l1-2c1-1 1-2 2-4v-7h-1z"></path><path d="M341 240h1c1 0 1 1 2 1 0 1 1 1 1 2l-1 1c-1-1-2-2-3-4z" class="i"></path><path d="M340 243l5 6c1 0 1 0 1-1v2h-3c-1-2-3-4-4-6l1-1z" class="O"></path><path d="M336 229c0-1 0-1 1-2h1l-1 3c1 1 1 1 2 1-1 2-1 5-1 8-1 0-2 1-2 2l-1 1h-1l1-2c1-1 1-2 2-4v-7h-1z" class="H"></path><defs><linearGradient id="CQ" x1="252.222" y1="238.029" x2="257.389" y2="247.713" xlink:href="#B"><stop offset="0" stop-color="#343333"></stop><stop offset="1" stop-color="#4f4e4f"></stop></linearGradient></defs><path fill="url(#CQ)" d="M249 233h0c2 0 3-1 5-1s5 1 7 1c3 1 7 3 10 4 0 1 1 1 2 1 1 1 2 1 4 1v1h-1 0-3c0 1 0 1 1 2l2 1v1l5 4c-3 0-4-3-7-2h-1l-1 1-2-1-2-2c-1-2-2-2-3-3-3-1-7 0-10 1-2 1-8 4-9 7h0 2-9c2-2 4-3 6-5h-2l3-4v-2c0-2 1-4 2-5h1z"></path><path d="M246 240l2-1 1 1c-1 2-3 3-4 4h-2l3-4z" class="j"></path><path d="M270 246l-1-3c-1 0-1-1-2-2h0c1 0 2 0 3 1l1-1 3 1 2 1v1l5 4c-3 0-4-3-7-2h-1l-1 1-2-1z" class="G"></path><path d="M270 242l1-1 3 1 2 1-1 1c-2 0-3-1-5-2z" class="V"></path><defs><linearGradient id="CR" x1="250.83" y1="230.908" x2="271.36" y2="241.719" xlink:href="#B"><stop offset="0" stop-color="#3c3b3c"></stop><stop offset="1" stop-color="#818080"></stop></linearGradient></defs><path fill="url(#CR)" d="M249 233h0c2 0 3-1 5-1s5 1 7 1c3 1 7 3 10 4 0 1 1 1 2 1 1 1 2 1 4 1v1h-1 0-3c0 1 0 1 1 2l-3-1c-2-2-4-3-6-4-4 0-7 0-10 1-3 0-5 1-7 1h0l-2 1v-2c0-2 1-4 2-5h1z"></path><path d="M248 233h1c0 1 0 1 1 2h8l1-1c1 1 2 1 3 1v2h3c-4 0-7 0-10 1-3 0-5 1-7 1h0l-2 1v-2c0-2 1-4 2-5z" class="Y"></path><path d="M248 233h1c0 1 0 1 1 2h8c-2 2-10 1-10 4h0 0l-2 1v-2c0-2 1-4 2-5z" class="D"></path><defs><linearGradient id="CS" x1="479.034" y1="253.723" x2="452.695" y2="239.384" xlink:href="#B"><stop offset="0" stop-color="#212021"></stop><stop offset="1" stop-color="#3c3b3c"></stop></linearGradient></defs><path fill="url(#CS)" d="M467 236c2 0 4 0 6-1l2-1h0l1 1v3l1 3 2 2h-1c2 2 5 4 6 6l-18 1h-11-14c2 0 2 0 3-1l-1-1c1-2 3-2 4-3l-1-1 6-4c1 0 2-1 3-1 2-2 3-2 5-3l1 1c1-1 4-1 5-1h1z"></path><path d="M478 243h0c-1-1-3-1-4-2v-1h1l2 1 2 2h-1z" class="J"></path><path d="M467 236c2 0 4 0 6-1l2-1h0l1 1v3l1 3-2-1c1 0 1 0 1-1-2-2-6-2-9-3h0z" class="S"></path><path d="M444 249l8-6c1 0 2-1 4-1l-1 1c-2 2-5 3-7 5 0 1 0 1 1 2 2 0 4-1 6 0h-14c2 0 2 0 3-1z" class="O"></path><path d="M460 236l1 1h-1-1c0 1-1 1-1 2h0l1-1h0v1c-1 0-3 2-3 3h0c-2 0-3 1-4 1l-8 6-1-1c1-2 3-2 4-3l-1-1 6-4c1 0 2-1 3-1 2-2 3-2 5-3z" class="V"></path><path d="M282 225c1 1 2 1 2 2l1 1h1c0-1 0 0 1-1v1l1 1 2 2 2 1c1 2 3 3 5 5l1 1c3-1 4 1 7 2h0c2 0 2 0 4 1 1 0 2 2 4 2 1 1 1 1 1 2l4 2v1l1 1h-2c-1 0-3 1-4 1h-2-3-8-4v-1-2-1c-1 0-2-1-3-1 0 0 0 1-1 1-2 0-2 0-4 2h0-7 0l-5-4v-1l-2-1c-1-1-1-1-1-2h3 0 1v-1c-2 0-3 0-4-1v-1h2v-1c-1 0-3-1-4-2s-1-1-1-2c2-1 3 1 4 1l1-1-2-1-1-1v-1h2v-2h1l3 1h1 2 0c0-2 0-2-1-3h2z" class="d"></path><path d="M288 237s0-1 1-1l3 3c-1 1-1 1-1 3h1v1h-1s0-1-1-2c1-1-1-3-2-4z" class="V"></path><path d="M279 244h6c1 1 2 1 4 1l1-1c0-1 0-1-1-1l1-2c1 1 1 2 1 2l1 2c-2 1-3 0-5 0-1 0-3 0-4 1-2-1-3-1-4-2z" class="H"></path><path d="M283 232c0-1 0-2-1-2v-2h0c1 0 2 1 2 2l5 6c-1 0-1 1-1 1l-5-5z" class="h"></path><path d="M291 243h1l1 2s0 1-1 1c-2 0-2 0-4 2h0-7 0l-5-4h3c1 1 2 1 4 2 1-1 3-1 4-1 2 0 3 1 5 0l-1-2z" class="L"></path><path d="M271 234c-1-1-1-1-1-2 2-1 3 1 4 1l1-1h1 0c1-1 2 0 3 0 3 1 6 2 8 5l1 3h-1c-1 1-2-1-4-1l-5-2-1 1v1c-2 0-3 0-4-1v-1h2v-1c-1 0-3-1-4-2z" class="U"></path><path d="M279 234c1 1 2 1 2 2h-2 0-2l1 1-2-1h0v-1c1 0 0 0 1-1 1 1 1 1 2 0z" class="f"></path><path d="M271 234c-1-1-1-1-1-2 2-1 3 1 4 1l1-1h1c1 1 2 2 3 2-1 1-1 1-2 0-1 1 0 1-1 1v1h0l2 1-1 1v1c-2 0-3 0-4-1v-1h2v-1c-1 0-3-1-4-2z" class="K"></path><path d="M282 225c1 1 2 1 2 2l1 1h1c0-1 0 0 1-1v1l1 1 2 2 2 1c1 2 3 3 5 5l1 1h1c0 1 1 2 2 2s1 1 1 1c-1 2-3 2-4 3l-3-2-3-3-3-3-5-6c0-1-1-2-2-2h0v2c1 0 1 1 1 2-2-2-3-3-5-4h1 2 0c0-2 0-2-1-3h2z" class="m"></path><path d="M293 236c1 0 2 1 4 1l1 1h1c0 1 1 2 2 2s1 1 1 1c-1 2-3 2-4 3l-3-2h3v-1c-1-2-4-4-5-5z" class="X"></path><path d="M282 225c1 1 2 1 2 2l1 1h1c0-1 0 0 1-1v1l1 1 2 2 2 1c1 2 3 3 5 5-2 0-3-1-4-1l-1-1c-2-1-3-2-4-4-1-1-1-1-3-2l-1 1c0-1-1-2-2-2h0v2c1 0 1 1 1 2-2-2-3-3-5-4h1 2 0c0-2 0-2-1-3h2z" class="o"></path><defs><linearGradient id="CT" x1="305.503" y1="250.375" x2="304.245" y2="238.723" xlink:href="#B"><stop offset="0" stop-color="#121212"></stop><stop offset="1" stop-color="#3c3b3c"></stop></linearGradient></defs><path fill="url(#CT)" d="M298 238c3-1 4 1 7 2h0c2 0 2 0 4 1 1 0 2 2 4 2 1 1 1 1 1 2l4 2v1l1 1h-2c-1 0-3 1-4 1h-2-3-8-4v-1-2-1c-1 0-2-1-3-1l-1-2v-1h-1c0-2 0-2 1-3l3 3 3 2c1-1 3-1 4-3 0 0 0-1-1-1s-2-1-2-2h-1z"></path><path d="M298 238c3-1 4 1 7 2h0c1 1 2 1 2 2 0 2 0 2-1 3-3 0-5 0-8-1 1-1 3-1 4-3 0 0 0-1-1-1s-2-1-2-2h-1z" class="a"></path><path d="M384 192v1c1 1 1 1 1 2h1v1h0 1l-1 2 2 2h1c1 0 1-1 2-1h0c1 1 2 1 3 1v-1l1 1h0c-1 1-2 2-4 2h0c-1 2-1 3-2 4h0 1v1h1v1l1 1c0 1-1 2-1 3 0 3 0 5 1 8v1h1c1 0 1-1 1-1 1 1 1 2 1 3h0v1h0l1 1v2c0 1 1 1 1 1v1l-1 1v1h-1v2l-1 1-1-1v-1l-1-1c-1 0-1-2-1-3h0c-1-1-1 0-3 0h0v-2l-1 1v1h-1c-1 1 0 3-2 3h0-2l-1 1h0c0 1 0 2-1 3l1 2 1 3c-1 1-2 3-2 3l-2 2-2-1v-1c-1 2-2 2-3 3s-2 1-2 2c-1 1-2 2-3 2h-3-5l1-1c-2-1-3-3-4-4 0 0-1-1-1-2s1-2 1-3c1-1 1-2 2-3h0 1c1 0 1-1 1-2 1-2 1-4 3-5l1 1v-1-3-2c1-2 1-5 2-7 1-1 3-1 3-2v-2h0c1-1 1-1 3-1 0-2 2-4 3-5 0-2 1-3 2-5h-1l2-6h1 1 1 1v-1c1-1 1-2 1-4z" class="f"></path><path d="M391 222c1 0 2 1 3 2v1h1c0 1 1 1 1 2h0c0 1 1 1 1 1v1l-1 1-1-1s0-1-1-1h0c-1-1 0-1-1-2 0-1-1-2-2-2v-2z" class="i"></path><path d="M391 222v-10c0 3 0 5 1 8v1h1c1 0 1-1 1-1 1 1 1 2 1 3h0v1h0l1 1v2h0c0-1-1-1-1-2h-1v-1c-1-1-2-2-3-2z" class="l"></path><path d="M389 209h1 0v4c-1 1-1 2-1 4v1l-2 1h0v2l-1 1h0l-1-2c0-3 0-7 2-9 1-1 1-1 2-1v-1z" class="U"></path><path d="M389 217c-1-1-1-2-1-3s0-2 1-3l1 1v1c-1 1-1 2-1 4z" class="W"></path><path d="M387 219c1 1 3 4 3 6l1 1 3 3 1 1c0 1-1 1 0 1v2l-1 1-1-1v-1l-1-1c-1 0-1-2-1-3h0c-1-1-1 0-3 0h0v-2l-1 1h0c-1-1-1-5-2-7l1 2h0l1-1v-2h0z" class="K"></path><g class="a"><path d="M391 228v1h2c1 1 1 2 1 4h1l-1 1-1-1v-1l-1-1c-1 0-1-2-1-3z"></path><path d="M388 226l-1-4h1c1 1 2 4 3 6-1-1-1 0-3 0h0v-2zm1-23l2-1h0c-1 2-1 3-2 4h0 1v1l-2 2h1v1c-1 0-1 0-2 1-2 2-2 6-2 9 1 2 1 6 2 7h0v1h-1c-1 1 0 3-2 3h0v-1l-1-2v-3l-1-3-1-2-1-2h-2c1-1 2-3 3-4 0-1 0-2-1-3 2-2 6-6 9-8h0 0z"></path></g><path d="M383 225c1 1 1 1 2 1h0v3l-1 1-1-2v-3z" class="X"></path><path d="M389 203l2-1h0c-1 2-1 3-2 4h0 1v1l-2 2-2 2h0c-1-3 2-4 2-6 0-1 0-1 1-2h0 0z" class="c"></path><path d="M380 218l3-1h0v2l2 7h0c-1 0-1 0-2-1l-1-3-1-2-1-2z" class="Q"></path><path d="M381 214h0v-2c1-1 1 0 2 0 1 1 0 5 0 7v-2h0l-3 1h-2c1-1 2-3 3-4z" class="P"></path><path d="M384 192v1c1 1 1 1 1 2h1v1h0 1l-1 2 2 2h1c1 0 1-1 2-1h0c1 1 2 1 3 1v-1l1 1h0c-1 1-2 2-4 2l-2 1h0 0c-3 2-7 6-9 8 1 1 1 2 1 3-1 1-2 3-3 4h0-1v-1-1c-1 0-1-1-2-2v-1c-1 0-1 1-2 1 0 1 0 1-1 2 0 1-2 2-2 4h-1v-1l-2-1c1-1 3-1 3-2v-2h0c1-1 1-1 3-1 0-2 2-4 3-5 0-2 1-3 2-5h-1l2-6h1 1 1 1v-1c1-1 1-2 1-4z" class="p"></path><path d="M380 211c1 1 1 2 1 3-1 1-2 3-3 4h0-1v-1-1c1-2 2-3 3-5z" class="c"></path><path d="M391 199h0c1 1 2 1 3 1v-1l1 1h0c-1 1-2 2-4 2l-2 1h0v-2h0c-3 0-6 4-8 6-1 1-1 2-3 1 3-2 8-8 11-8 1 0 1-1 2-1z" class="l"></path><path d="M391 199h0c1 1 2 1 3 1v-1l1 1h0c-1 1-2 2-4 2l-2 1 1-2h1v-2z" class="W"></path><path d="M384 192v1c1 1 1 1 1 2h1v1h0 1l-1 2 2 2h1c-3 0-8 6-11 8l-1 2v-1-1h-1c0-2 1-3 2-5h-1l2-6h1 1 1 1v-1c1-1 1-2 1-4z" class="s"></path><path d="M380 202v2c0 1-2 3-3 4h-1c0-2 1-3 2-5l2-1z" class="l"></path><path d="M384 192v1c1 1 1 1 1 2h1v1h0c-1 2-2 3-3 4l-3 2-2 1h-1l2-6h1 1 1 1v-1c1-1 1-2 1-4z" class="T"></path><path d="M384 192v1c1 1 1 1 1 2h1v1h0c-1 2-2 3-3 4 0-1 0-1-1-2l-1-1h-1 1 1 1v-1c1-1 1-2 1-4z" class="O"></path><path d="M370 220c0-2 2-3 2-4 1-1 1-1 1-2 1 0 1-1 2-1v1c1 1 1 2 2 2v1 1h1 0 2l1 2 1 2 1 3v3l1 2v1h-2l-1 1h0c0 1 0 2-1 3l1 2 1 3c-1 1-2 3-2 3l-2 2-2-1v-1c0-1 0-2-1-3v-1h1c0-2-2-2-3-3-1-2-2-3-3-4v1l-1-1h-1c-1-4 1-9 2-12z" class="i"></path><path d="M380 243l-2-1c0-2 0-3 1-5h2l1 3c-1 1-2 3-2 3zm-5-29c1 1 1 2 2 2v1c-1 2-1 4-2 5l-1 1c0 1 0 2-1 3v-1-2-2c0-2 1-4 2-7z" class="Q"></path><path d="M370 220c0-2 2-3 2-4 1-1 1-1 1-2 1 0 1-1 2-1v1c-1 3-2 5-2 7l-3 8v3 1l-1-1h-1c-1-4 1-9 2-12z" class="r"></path><path d="M374 223l1-1c1-1 1-3 2-5v1h1 0 2l1 2 1 2 1 3v3l1 2v1h-2l-1 1h0c0 1 0 2-1 3 0 0-1-2-2-2h-1c1 1 1 1 1 2h-1l-3-3v-1s-1-2-1-3 1-1 1-2v-3z" class="X"></path><path d="M377 228h-1c-1-1-1-3-1-4s0-1 1-2l1 1v2c0 1 1 1 1 2v1h-1z" class="L"></path><path d="M377 228h1 0c1 1 2 4 3 4h0c0 1 0 2-1 3 0 0-1-2-2-2h0c-1-2-1-3-1-5z" class="I"></path><path d="M378 218h2l1 2 1 2c-1 0-2-1-3-1 0 1-1 3-2 4v-2c0-2 1-3 1-5h0z" class="U"></path><path d="M379 221c1-1 1-1 2-1l1 2c-1 0-2-1-3-1z" class="P"></path><defs><linearGradient id="CU" x1="380.774" y1="230.906" x2="379.664" y2="222.141" xlink:href="#B"><stop offset="0" stop-color="#8e8c8e"></stop><stop offset="1" stop-color="#abaaaa"></stop></linearGradient></defs><path fill="url(#CU)" d="M379 221c1 0 2 1 3 1l1 3v3l1 2v1h-2l-1 1h0 0c-1 0-2-3-3-4h0v-1c0-1-1-1-1-2 1-1 2-3 2-4z"></path><path d="M381 232c1-2 1-3 2-4l1 2v1h-2l-1 1z" class="I"></path><path d="M367 218l2 1v1h1c-1 3-3 8-2 12h1l1 1v-1c1 1 2 2 3 4 1 1 3 1 3 3h-1v1c1 1 1 2 1 3-1 2-2 2-3 3s-2 1-2 2c-1 1-2 2-3 2h-3-5l1-1c-2-1-3-3-4-4 0 0-1-1-1-2s1-2 1-3c1-1 1-2 2-3h0 1c1 0 1-1 1-2 1-2 1-4 3-5l1 1v-1-3-2c1-2 1-5 2-7z" class="R"></path><path d="M373 238l2 1v1c1 1 1 2 1 3-1 2-2 2-3 3s-2 1-2 2l-1-1c0-2 2-3 2-6v1c1-2 1-2 1-4z" class="E"></path><path d="M373 238l2 1v1s0 1-1 1c0 1 0 2-1 3h-1v-2c1-2 1-2 1-4z" class="R"></path><path d="M359 237c1 2 2 1 4 2h0l2 1v1h1c1 1 1 2 1 3s-2 3-3 4h-2v1h-1 0c-2-1-3-3-4-4 0 0-1-1-1-2s1-2 1-3c1-1 1-2 2-3z" class="b"></path><path d="M364 245v1l-2 2v1c-1-1-2-2-2-3h0 1 0c1 0 2 0 3-1z" class="E"></path><path d="M363 239l2 1c-1 0-2 0-3 1h-2l-1 1-1-1h1c1-2 2-2 4-2z" class="B"></path><path d="M360 241h1c0 2 1 2 0 4v1h-1c-1-2-1-2-1-4l1-1z" class="m"></path><path d="M365 240v1h1c1 1 1 2 1 3s-2 3-3 4h-2l2-2v-1c-1 1-2 1-3 1h0v-1c1-2 0-2 0-4h-1 2c1-1 2-1 3-1z" class="G"></path><path d="M362 242l1-1c1 0 1 0 2 1 0 1 0 2-1 2l-2-2z" class="H"></path><path d="M361 241h0l1 1h0l2 2v1c-1 1-2 1-3 1h0v-1c1-2 0-2 0-4z" class="a"></path><path d="M367 218l2 1v1h1c-1 3-3 8-2 12h1l1 1v-1c1 1 2 2 3 4 1 1 3 1 3 3h-1l-2-1c0 2 0 2-1 4v-1c-1-1 0-1-1-2h-3s-3-1-3-2l1 4h-1v-1l-2-1h0c-2-1-3 0-4-2h0 1c1 0 1-1 1-2 1-2 1-4 3-5l1 1v-1-3-2c1-2 1-5 2-7z" class="l"></path><path d="M370 232c1 1 2 2 3 4h-2c-1-1-3-2-3-4h1l1 1v-1z" class="W"></path><path d="M367 218l2 1v1c-1 1-1 1-1 3-1 1-1 3-3 3v1-2c1-2 1-5 2-7z" class="r"></path><path d="M365 237c-1-2-1-4-1-5l1-1c0 2 0 4 2 5s4 2 6 2c0 2 0 2-1 4v-1c-1-1 0-1-1-2h-3s-3-1-3-2z" class="C"></path><path d="M364 230l1 1h0l-1 1c0 1 0 3 1 5l1 4h-1v-1l-2-1h0c-2-1-3 0-4-2h0 1c1 0 1-1 1-2 1-2 1-4 3-5z" class="F"></path><path d="M477 222c2 0 5 1 7 2 3 1 5 2 7 3 1 0 2 1 3 1 2 0 3 1 4 1 2 0 4 0 6 2 0 0 1 0 1 1 2 1 4 1 6 3 1 1 3 2 4 4l1 1-3 4s0 1-1 2h0v5h-1l-1 1 2 5c-2 0-3 0-4 1l-1 1 1 5 2 2h-1l2 2h0-1s-1-1-2-1v1h1c1 0 1 1 2 1l-6-2-6-3c-1 1-1 0-1 1l-5-4-2-2-1 1-2-11h-4c-1-2-4-4-6-6h1l-2-2-1-3v-3l-1-1h0l-2 1c-2 1-4 1-6 1h-1c-1 0-4 0-5 1l-1-1c-2 1-3 1-5 3-1 0-2 1-3 1 0-2-1 0-2-1-1 0-1-1-2-1l4-2h1 1l1-1v-1l3-1h0c3-1 3-3 5-5l1-1 3-1 4-1h1c0-2 0-2-1-3h3 3z" class="e"></path><defs><linearGradient id="CV" x1="482.557" y1="224.924" x2="472.094" y2="222.62" xlink:href="#B"><stop offset="0" stop-color="#222223"></stop><stop offset="1" stop-color="#4b4a4b"></stop></linearGradient></defs><path fill="url(#CV)" d="M477 222c2 0 5 1 7 2 0 0 0 1-1 1 0 0-3-1-4-1-3 0-6 1-8 1h1c0-2 0-2-1-3h3 3z"></path><path d="M502 244c2 1 3 4 4 6 1 3 0 7 1 9l1 5h-1c0-1-1-4-2-5-1 0-2-2-3-4v-1c1 0 1 0 1 1h0l1-1v-1-2-1c0-2-1-4-2-6z" class="D"></path><path d="M504 254c0 1 0 1 1 2v3c-1 0-2-2-3-4v-1c1 0 1 0 1 1h0l1-1z" class="e"></path><path d="M498 229c2 0 4 0 6 2 0 0 1 0 1 1 2 1 4 1 6 3 1 1 3 2 4 4l1 1-3 4s0 1-1 2h0v5h-1l-1 1c-1-3-3-6-4-8l-6-12v-1h0l-2-2z" class="J"></path><path d="M500 231c6 5 8 13 11 20l-1 1c-1-3-3-6-4-8l-6-12v-1z" class="F"></path><path d="M505 232c2 1 4 1 6 3 1 1 3 2 4 4l1 1-3 4s0 1-1 2h0v-2c-1-3-3-6-5-9 0-1-2-2-2-3z" class="Y"></path><path d="M507 235c2 0 3 2 4 3 1 0 2 2 2 2v3l-1 1c-1-3-3-6-5-9z" class="G"></path><defs><linearGradient id="CW" x1="474.752" y1="226.994" x2="473.673" y2="237.058" xlink:href="#B"><stop offset="0" stop-color="#464546"></stop><stop offset="1" stop-color="#5e5d5d"></stop></linearGradient></defs><path fill="url(#CW)" d="M464 227l3-1 1 1c4 1 8 0 13 0 4 1 11 4 14 7l-1 1c-3 0-4-1-6-2-1 0-2-1-3 0-1 0-3-1-4-1s-2 1-3 1c0 1 0 1 1 2h-1c-1 0-1 1-1 1l-1-1-1-1h0l-2 1c-2 1-4 1-6 1h-1c-1 0-4 0-5 1l-1-1c-2 1-3 1-5 3-1 0-2 1-3 1 0-2-1 0-2-1-1 0-1-1-2-1l4-2h1 1l1-1v-1l3-1h0c3-1 3-3 5-5l1-1z"></path><path d="M465 231l5-1-2 1h0 1c2 1 6 0 8-1-1 2-2 1-4 2-1 0-8 0-9-1h0 1z" class="E"></path><path d="M470 230c2 0 6-1 8-1v1h-1c-2 1-6 2-8 1h-1 0l2-1z" class="C"></path><path d="M463 228l1-1v2s1 1 1 2h-1c-1 0-2 0-2 1-2 1-5 2-7 3v-1l3-1h0c3-1 3-3 5-5z" class="G"></path><path d="M466 236c1-1 2-1 3-2s3-1 4-1c1-1 1-1 2-1 0 1 1 1 1 2h1 0l1-1c0 1 0 1 1 2h-1c-1 0-1 1-1 1l-1-1-1-1h0l-2 1c-2 1-4 1-6 1h-1z" class="E"></path><defs><linearGradient id="CX" x1="457.297" y1="233.263" x2="451.326" y2="240.041" xlink:href="#B"><stop offset="0" stop-color="#6d6e6c"></stop><stop offset="1" stop-color="#888588"></stop></linearGradient></defs><path fill="url(#CX)" d="M454 236h1l2-2c1 0 1 1 2 0h0c0 1 0 0 1 1v1h0c-2 1-3 1-5 3-1 0-2 1-3 1 0-2-1 0-2-1-1 0-1-1-2-1l4-2h1 1z"></path><path d="M478 233c1 0 2-1 3-1s3 1 4 1c1-1 2 0 3 0 2 1 3 2 6 2l1-1c3 3 5 6 7 10h0c1 2 2 4 2 6v1 2 1l-1 1h0c0-1 0-1-1-1v1c1 2 2 4 3 4 1 1 2 4 2 5h1l2 2h-1l2 2h0-1s-1-1-2-1v1h1c1 0 1 1 2 1l-6-2-6-3c-1 1-1 0-1 1l-5-4-2-2-1 1-2-11h-4c-1-2-4-4-6-6h1l-2-2-1-3v-3l1 1s0-1 1-1h1c-1-1-1-1-1-2z" class="g"></path><path d="M497 249h0c2 1 3 4 5 5v1c-2-1-4-3-6-4 1-1 1-1 1-2z" class="Y"></path><path d="M493 239c4 1 6 3 9 6v-1h0c1 2 2 4 2 6v1 2c-4-5-6-10-11-14z" class="I"></path><defs><linearGradient id="CY" x1="487.916" y1="237.466" x2="500.743" y2="238.74" xlink:href="#B"><stop offset="0" stop-color="#727070"></stop><stop offset="1" stop-color="#949495"></stop></linearGradient></defs><path fill="url(#CY)" d="M485 233c1-1 2 0 3 0 2 1 3 2 6 2l1-1c3 3 5 6 7 10v1c-3-3-5-5-9-6-1-1-3-2-4-3-1-2-2-2-4-3z"></path><path d="M478 233c1 0 2-1 3-1s3 1 4 1c2 1 3 1 4 3-2 0-5-1-7-2-1 0-2 0-2 1h-1c1 1 1 3 2 3 1 1 3 1 4 1 4 1 7 2 10 5 0 1 1 2 2 3v1 1h0c-4-3-9-5-14-6h-1l1 2c-2-1-3-2-4-2l-2-2-1-3v-3l1 1s0-1 1-1h1c-1-1-1-1-1-2z" class="C"></path><path d="M478 238c1 0 2 2 3 3l-1 1c-1-1-2-3-2-4z" class="J"></path><path d="M476 238l1-1 1 1c0 1 1 3 2 4 1 0 2 1 3 1h-1l1 2c-2-1-3-2-4-2l-2-2-1-3z" class="E"></path><path d="M481 241c4 1 8 1 12 3h2c0 1 1 2 2 3v1 1h0c-4-3-9-5-14-6-1 0-2-1-3-1l1-1z" class="S"></path><path d="M479 243c1 0 2 1 4 2l-1-2h1c5 1 10 3 14 6 0 1 0 1-1 2 2 1 4 3 6 4 1 2 2 4 3 4 1 1 2 4 2 5h1l2 2h-1l2 2h0-1s-1-1-2-1v1h1c1 0 1 1 2 1l-6-2-6-3c-1 1-1 0-1 1l-5-4-2-2-1 1-2-11h-4c-1-2-4-4-6-6h1z" class="j"></path><path d="M483 243c5 1 10 3 14 6 0 1 0 1-1 2-4-3-8-4-13-6l-1-2h1z" class="O"></path><defs><linearGradient id="CZ" x1="499.137" y1="266.103" x2="501.257" y2="253.528" xlink:href="#B"><stop offset="0" stop-color="#2d2c2c"></stop><stop offset="1" stop-color="#505050"></stop></linearGradient></defs><path fill="url(#CZ)" d="M488 249l4 4c3 2 6 4 8 6s5 4 7 5h1l2 2h-1l2 2h0-1s-1-1-2-1v1h1c1 0 1 1 2 1l-6-2-6-3c-1 1-1 0-1 1l-5-4-2-2-1 1-2-11z"></path><defs><linearGradient id="Ca" x1="290.796" y1="255.183" x2="294.322" y2="282.654" xlink:href="#B"><stop offset="0" stop-color="#979696"></stop><stop offset="1" stop-color="#b4b3b4"></stop></linearGradient></defs><path fill="url(#Ca)" d="M238 293c0-4 3-9 4-13l5-20c9 1 18 0 26 0l50-1h6 3v-1c3 1 7 1 9 1 1 1 2 2 3 2l1 1 3 2h1c1 1 1 1 1 2h0c-2 1-7 3-9 4l1 2c-1 0-1 0-1-1h-3c-2-1-5-1-8-1l-15 1h-23c-5 0-11-1-16 0-6 1-13 2-19 5-8 4-13 10-19 17z"></path><path d="M427 212c2-1 6-2 8-3v1c1 0 1 0 2 1h3l7-1h2c2 1 6 1 8 0l2 1c0 1 0 1-1 1h1c3 0 4 0 6 2 1 0 1 1 1 1h1c2 1 3 2 5 3s4 1 4 3l1 1h-3-3c1 1 1 1 1 3h-1l-4 1-3 1-1 1c-2 2-2 4-5 5h0l-3 1v1l-1 1h-1-1l-4 2c1 0 1 1 2 1 1 1 2-1 2 1l-6 4 1 1c-1 1-3 1-4 3l1 1c-1 1-1 1-3 1h-17-12c-4-1-9 0-13 0h-7-1c0-1-1 0-1-1h0c-1 1-1 1-2 1v-1c1-2 1-4 1-6l1-1-1-1-1-2c1 0 0-3 0-4h0c2-2 1-5 0-7 2 0 2-1 3 0h0c0 1 0 3 1 3l1 1v1l1 1 1-1v-2h1v-1l1-1v-1c0-1 1-1 1-2h1s0 1 1 1c1 2 1 1 2 0 1 0 2 1 2 1h1l2-1c1-1 3-4 3-5h0c1-1 4-5 5-5s2-1 2-1l10-6h2c-1 1-2 2-3 2h1z" class="g"></path><path d="M415 238l1-1v2h0-1v-1z" class="k"></path><path d="M420 229c2-1 4-3 5-4 0 2 0 2-2 4-1 1-2 1-3 1v-1z" class="V"></path><path d="M427 212c2-1 6-2 8-3v1c1 0 1 0 2 1h3-4c-1 1-2 1-3 1h-2-4z" class="j"></path><path d="M421 222c0-1 0-1 1-2 2-1 2-1 3 0h1v4l-1-1h0-2-1-1v-1z" class="r"></path><path d="M425 220h1v4l-1-1h0-2-1-1l2-1c1-1 2-1 2-2z" class="G"></path><path d="M421 222v1h1v1c0 1 0 1-1 1s-1 1-1 1c-1 1-2 1-2 2-1 0-1-1-1-1h-1c-1-1-2 0-3 0 0 0 0-1 1-1 0-1 1-2 2-2l1 1v1h2c2-2 2-2 2-4z" class="D"></path><path d="M398 239c2-2 3-3 5-4l2 1-3 3-1-2-4 4v1h-1-2 0c-1-1-1-1-1-2l1 1h0v-2h1c1-1 2-1 3 0z" class="j"></path><path d="M395 239c1-1 2-1 3 0-1 0-1 0-1 1s-1 1-1 2h-2 0c-1-1-1-1-1-2l1 1h0v-2h1z" class="k"></path><path d="M416 239h1c2 0 1-1 2-2 2-1 3-1 5-2h0c-1 1-3 2-5 2v1h1c1 0 1 0 2-1h2l2-1v-1c1 0 2-1 3-1-2 2-4 3-6 5-3 0-5 1-7 2h-3l1-1-1-1 2-1v1h1 0z" class="S"></path><path d="M400 248c1-1 3-2 4-3 0-1 1-1 2-2s2-2 3-2c1-1 2-2 4-2l1 1-1 1-3 3h-1c-1 1-2 1-3 1-1 1-4 2-6 3z" class="j"></path><path d="M431 232c0-1 0-2 1-3s1-1 1-2c1-2 2-3 3-5 1-3 3-6 7-7v1l-2 2c-1 0-1 0 0 1-1 0-2 1-2 2s-1 2-1 3c-1 1-1 2-2 3 0 0-1 0-1 1l-2 2-1 1-1 1z" class="e"></path><path d="M397 234c2-1 5-1 7-1 2-2 3-3 5-4-2 2-3 5-4 7l-2-1c-2 1-3 2-5 4-1-1-2-1-3 0v-1c1-2 2-2 2-4z" class="D"></path><path d="M422 223h1 2 0l1 1-1 1c-1 1-3 3-5 4v1c-2 1-4 1-6 0-1 0-1 0-1-1v-2c1 0 2-1 3 0h1s0 1 1 1c0-1 1-1 2-2 0 0 0-1 1-1s1 0 1-1v-1z" class="O"></path><path d="M413 227c1 0 2-1 3 0h1-1v1c-1 0-1 1-2 2h0c-1 0-1 0-1-1v-2z" class="C"></path><path d="M423 223h2 0l1 1-1 1c-1 1-3 3-5 4h-2l1-1c1 0 3-2 3-2l1-3z" class="c"></path><path d="M397 229h0c1 1 1 1 1 2h0c1 0 1-1 2-1 1 1 2 1 3 1 2 0 3-1 5-3 1 0 0-1 2-1l-1 2c-2 1-3 2-5 4-2 0-5 0-7 1 0 2-1 2-2 4h-1l-2-2c0-1 1-2 1-3h0l1 1 1-1v-2h1v-1l1-1z" class="H"></path><path d="M396 231h1c0 1 0 2-1 2l1 1c0 2-1 2-2 4h-1l-2-2c0-1 1-2 1-3h0l1 1 1-1v-2h1z" class="L"></path><path d="M441 219l2-2 1 1v-1h2l1 1v1 1l-3 3-3 3h0c-1 0-1 0-2 1h0l-2 1h-2c0-1 1-1 1-1 1-1 1-2 2-3 0-1 1-2 1-3s1-2 2-2z" class="M"></path><path d="M446 217l1 1v1 1h-3c1-1 2-1 2-3z" class="C"></path><path d="M444 220h3l-3 3-3 3h0c-1 0-1 0-2 1h0l-2 1h-2c0-1 1-1 1-1 1-1 1-2 2-3l1 1c1 0 1 0 1-2 1 0 3-2 4-3z" class="O"></path><path d="M388 228c2 0 2-1 3 0h0c0 1 0 3 1 3l1 1v1h0c0 1-1 2-1 3l2 2h1v1h-1v2h0l-1-1h-1l1 2c1 1 1 1 1 2l-1-1c-1 1-2 1-3 1h0c-1 1-1 1 0 2v3h-2c1-2 1-4 1-6l1-1-1-1-1-2c1 0 0-3 0-4h0c2-2 1-5 0-7z" class="M"></path><path d="M389 238l1-1v1h1v1 4h-1v-1l-1-1 1-1c0-1-1-1-1-2z" class="R"></path><path d="M391 238v-2h1l2 2h1v1h-1-3v-1z" class="N"></path><defs><linearGradient id="Cb" x1="386.825" y1="233.426" x2="391.103" y2="231.433" xlink:href="#B"><stop offset="0" stop-color="#666566"></stop><stop offset="1" stop-color="#7c7b7b"></stop></linearGradient></defs><path fill="url(#Cb)" d="M388 228c2 0 2-1 3 0h0c0 1 0 3 1 3l1 1v1h0c0 1-1 2-1 3h-1v2h-1v-1l-1 1c0 1 1 1 1 2l-1 1-1-2c1 0 0-3 0-4h0c2-2 1-5 0-7z"></path><path d="M389 238v-1l1-1v-1c1-1 1-2 2-2h1c0 1-1 2-1 3h-1v2h-1v-1l-1 1z" class="B"></path><path d="M427 210h2c-1 1-2 2-3 2-4 3-9 5-12 9-2 2-3 4-4 6-2 0-1 1-2 1-2 2-3 3-5 3-1 0-2 0-3-1-1 0-1 1-2 1h0c0-1 0-1-1-2h0v-1c0-1 1-1 1-2h1s0 1 1 1c1 2 1 1 2 0 1 0 2 1 2 1h1l2-1c1-1 3-4 3-5h0c1-1 4-5 5-5s2-1 2-1l10-6z" class="d"></path><path d="M431 232l1-1 1 1v1 1c0 1-1 2-1 4-1 0-1 0-1 1l-3 3-1 1c-2 1-5 1-7 1s-3 0-4-1v-2c2-1 4-2 7-2 2-2 4-3 6-5l2-2z" class="a"></path><path d="M433 232v1 1c0 1-1 2-1 4-1 0-1 0-1 1l-3 3c-2 0-4 0-6-1h0l2-1c4-2 6-5 9-8z" class="P"></path><path d="M433 234c0 1-1 2-1 4-1 0-1 0-1 1l-3 3c-2 0-4 0-6-1h0l2-1v1h2c3-1 5-5 7-7z" class="K"></path><defs><linearGradient id="Cc" x1="416.502" y1="249.18" x2="416.158" y2="240.019" xlink:href="#B"><stop offset="0" stop-color="#080807"></stop><stop offset="1" stop-color="#3b3a3c"></stop></linearGradient></defs><path fill="url(#Cc)" d="M432 239h2l-3 4c-3 3-4 5-7 7h-12c-4-1-9 0-13 0l1-2c2-1 5-2 6-3 1 0 2 0 3-1h1l3-3h3v2c1 1 2 1 4 1s5 0 7-1l1-1 3-3h1z"></path><path d="M410 245c1 0 1 1 2 1 1 1 3 0 4 0h1l-1 1c-2 0-3 1-5 0h-2c0-1 0-1 1-2z" class="e"></path><defs><linearGradient id="Cd" x1="461.212" y1="225.716" x2="435.987" y2="241.644" xlink:href="#B"><stop offset="0" stop-color="#848383"></stop><stop offset="1" stop-color="#acaaab"></stop></linearGradient></defs><path fill="url(#Cd)" d="M459 212c3 0 4 0 6 2 1 0 1 1 1 1h1c2 1 3 2 5 3s4 1 4 3l1 1h-3-3c1 1 1 1 1 3h-1l-4 1-3 1-1 1c-2 2-2 4-5 5h0l-3 1v1l-1 1h-1-1l-4 2c1 0 1 1 2 1 1 1 2-1 2 1l-6 4 1 1c-1 1-3 1-4 3l1 1c-1 1-1 1-3 1h-17c3-2 4-4 7-7l3-4h-2-1c0-1 0-1 1-1 0-2 1-3 1-4v-1-1l-1-1 1-1 2-2h2l2-1h0c1-1 1-1 2-1h0l3-3 3-3v-1-1l-1-1h-2v1l-1-1c4-2 7-3 11-4l4-1h1z"></path><path d="M452 236c1-1 2-1 3-2v1l-1 1h-1-1z" class="c"></path><path d="M435 235l3-1v1h0c-1 2-3 3-4 4h0-2c1-2 2-3 3-4z" class="N"></path><path d="M458 233v-1c-4 0-6 4-10 4h0l10-7h1 1 1c0-1 1-1 2-1h0c-2 2-2 4-5 5h0z" class="c"></path><path d="M435 241h1 1c0-1 1-1 1-1h1c1-1 2-2 4-3l1 1 4-1-3 3c-2 1-4 2-7 2-2 1-2 1-4 1l-1-1c1 0 2-1 2-1z" class="d"></path><path d="M439 227v2h2l-1 1h1 0l1 1-4 4v-1l-3 1c-1 1-2 2-3 4h-1c0-1 0-1 1-1 0-2 1-3 1-4v-1-1l-1-1 1-1 2-2h2l2-1z" class="H"></path><path d="M435 228h2v3h-1-1c0 1-1 2-2 2v-1l-1-1 1-1 2-2z" class="F"></path><path d="M433 230h2v1c0 1-1 2-2 2v-1l-1-1 1-1z" class="K"></path><path d="M439 227v2h2l-1 1h1 0l1 1-4 4v-1l-3 1c1 0 1-1 1-1 0-1 1-1 1-1s1-1 1-2h-1v-3l2-1z" class="b"></path><path d="M439 227v2l-1 2h0-1v-3l2-1z" class="L"></path><defs><linearGradient id="Ce" x1="454.771" y1="230.684" x2="435.856" y2="233.39" xlink:href="#B"><stop offset="0" stop-color="#9e9d9f"></stop><stop offset="1" stop-color="#bbb9b8"></stop></linearGradient></defs><path fill="url(#Ce)" d="M454 226h1 0c0 1-2 3-3 3-3 1-6 5-9 6-1 1-2 2-4 2-1 1-2 1-2 1 0-1 1-2 1-3h0l4-4c1-1 3-2 4-3 3 1 5-1 8-2z"></path><path d="M445 240c1-1 2-1 3-2 1 0 1 1 2 1 1 1 2-1 2 1l-6 4 1 1c-1 1-3 1-4 3l1 1c-1 1-1 1-3 1h-17c3-2 4-4 7-7l3-4h0l1 2s-1 1-2 1l1 1c2 0 2 0 4-1 3 0 5-1 7-2z" class="o"></path><path d="M434 239l1 2s-1 1-2 1l-1 1 1 1h0c0 1 0 2-1 2h2c4-1 8-1 12-2l1 1c-1 1-3 1-4 3-1 1-6-1-8-1h-4 0-1c1-1 2-1 2-2l-1-2 3-4h0z" class="c"></path><defs><linearGradient id="Cf" x1="440.302" y1="252.513" x2="429.578" y2="245.144" xlink:href="#B"><stop offset="0" stop-color="#51534f"></stop><stop offset="1" stop-color="#6b686b"></stop></linearGradient></defs><path fill="url(#Cf)" d="M424 250c3-2 4-4 7-7l1 2c0 1-1 1-2 2h1 0 4c2 0 7 2 8 1l1 1c-1 1-1 1-3 1h-17z"></path><path d="M459 212c3 0 4 0 6 2 1 0 1 1 1 1h1c2 1 3 2 5 3s4 1 4 3l1 1h-3-3c1 1 1 1 1 3h-1l-4 1-3 1-1 1h0l-1-1c-2 1-3 1-4 1-1 1-4 2-5 2l-1-1c1 0 3-2 3-3h0-1c-3 1-5 3-8 2-1 1-3 2-4 3l-1-1h0-1l1-1h-2v-2h0c1-1 1-1 2-1h0l3-3 3-3v-1-1l-1-1h-2v1l-1-1c4-2 7-3 11-4l4-1h1z" class="I"></path><path d="M458 224s0 1 1 1l-2 2v-3s-1 0-1 1h-3l1-1h4z" class="c"></path><path d="M459 225h0l1 1h1l-2 1c-1 1-1 1-2 1h-1l1-1 2-2z" class="b"></path><path d="M446 228c2-1 3-1 4-2h4c-3 1-5 3-8 2z" class="a"></path><path d="M462 219h1l1 1c-1 0-2 0-3 1l-3 3h-4l-1 1c-2 0-3 1-4 0h0l2-1c1-1 2-1 4-2 1-1 3-2 5-3h2z" class="B"></path><path d="M454 224c1-1 2-1 3-1 0-1 1-1 1-1 1-1 2-1 3-1l-3 3h-4z" class="O"></path><path d="M444 223c2-1 4-2 6-2v1h-1c-1 0-3 2-4 4v1h0l-2 1c0 1-1 2-2 2h0-1l1-1h-2v-2h0c1-1 1-1 2-1h0l3-3z" class="L"></path><path d="M443 228h0v-2c0-1 1-1 1-1h1v2l-2 1z" class="X"></path><path d="M439 227h0c1-1 1-1 2-1h0v3h-2v-2z" class="h"></path><path d="M447 219c4-2 8-3 12-3h0c2 1 3 1 5 2h0c0 1-1 1-2 1h-2c-2 1-4 2-5 3-2 1-3 1-4 2l-2-1v-1h1v-1c-2 0-4 1-6 2l3-3v-1z" class="c"></path><path d="M450 222h0c1-1 1-2 2-2h1c2-1 4-1 7-1-2 1-4 2-5 3-2 1-3 1-4 2l-2-1v-1h1z" class="G"></path><path d="M459 212c3 0 4 0 6 2 1 0 1 1 1 1h1c2 1 3 2 5 3s4 1 4 3l1 1h-3-3c1 1 1 1 1 3h-1l-4 1-3 1-1 1h0l-1-1h-3l2-1h-1l-1-1h0c-1 0-1-1-1-1l3-3c1-1 2-1 3-1l-1-1h-1c1 0 2 0 2-1h0c-2-1-3-1-5-2h0c-4 0-8 1-12 3v-1l-1-1h-2v1l-1-1c4-2 7-3 11-4l4-1h1z" class="E"></path><path d="M472 218c2 1 4 1 4 3l1 1h-3-3 0c1-1 1-3 1-4zm-13-6c3 0 4 0 6 2 1 0 1 1 1 1h1c-2 0-7 0-8-1v1c-1 0-2 0-3-1h-1l-1-1 4-1h1z" class="C"></path><defs><linearGradient id="Cg" x1="467.218" y1="221.445" x2="464.505" y2="227.921" xlink:href="#B"><stop offset="0" stop-color="#5b5a5a"></stop><stop offset="1" stop-color="#717071"></stop></linearGradient></defs><path fill="url(#Cg)" d="M462 224c3-1 6-1 9-2 1 1 1 1 1 3h-1l-4 1-3 1-1 1h0l-1-1h-3l2-1 1-2z"></path><defs><linearGradient id="Ch" x1="468.982" y1="222.362" x2="459.005" y2="220.029" xlink:href="#B"><stop offset="0" stop-color="#575658"></stop><stop offset="1" stop-color="#6e6c6c"></stop></linearGradient></defs><path fill="url(#Ch)" d="M459 216c2 0 5-1 7 0 0 1 0 1 1 1s2 1 3 2v1c0 1 0 1-1 1-3 1-5 1-7 3h0l-1 2h-1l-1-1h0c-1 0-1-1-1-1l3-3c1-1 2-1 3-1l-1-1h-1c1 0 2 0 2-1h0c-2-1-3-1-5-2h0z"></path><defs><linearGradient id="Ci" x1="286.619" y1="563.614" x2="378.296" y2="494.16" xlink:href="#B"><stop offset="0" stop-color="#080807"></stop><stop offset="1" stop-color="#292829"></stop></linearGradient></defs><path fill="url(#Ci)" d="M246 438h2c1 5 4 10 9 12 4 2 9 3 13 1 4-1 6-2 9-1h6l1 1v1c-2 1-5 1-7 1h-5c-2 1-4 1-5 3s-1 3-1 5c0 1 1 1 2 1 0 1 0 1-1 2l-3 14 3 1 1 1v1h0c0 3 0 5-1 8v2l4-5c1-2 3-2 4-3 2-1 5-4 7-4v1l-4 3 1 1 2-1 1-1 1 1c1 1 2 2 2 3l2 2v1l1 1 1 2c1 2 2 3 3 4s1 3 2 3c0 1 1 1 2 1 3 3 5 6 7 10v1l1 2 1-1h2v1h0c2 0 2 0 3 1v2s-1 1-2 1l-1 1c1 1 2 1 3 1l1-1c3 0 4-1 6-4 1-1 1-1 2-3 0 0 0-1 1-2h0c-1 5-1 10-6 14-2 1-4 3-6 4l2 1-1 1v1c-1 2-1 4-1 6h-2c-1 1-3 1-4 1-2 1-2 1-4 1 0 0 0 1-1 1v2c2-1 3-1 5-1h4 8 2l86-1h13v1c1 1 1 2 1 4l1 1v3c1 1 2 2 2 3l1 1h-1c1 3 0 7 0 11-1 1-2 3-2 4s0 2-1 3c-2 3-7 8-9 10l-2 1c-1 0-2 1-3 1-1 2 0 3 0 4-4 1-7 3-9 6v2l-1-2-1 2h-1c-2 1-4 2-6 4v1l1 1c0 1 0 4-1 4-1 2-3 4-4 6-4 5-7 13-7 20 0 2 0 4 1 6h-1c0 1 1 2 1 3v2c1 1 1 1 1 2v1c-1-1-1-2-3-3 0 1 1 2 1 2-1 2-3 4-3 5l-6 10c-1 2-2 3-3 5v1h-1c-1-2-2-4-4-6l-6-10c0-1-3-4-3-5s2-3 2-3c1-2 2-5 2-7 1-6-1-12-4-17-2-5-4-9-7-13l-2 2v1l-3-5h-1l-1 1c0 1-1 2-2 3v-1c-1-1-1 0-1-1v-3c-1 0-2-1-3-2l-2 3v1l-1 2c-1-3-2-6-4-8 0 1 3 5 3 7h-1l1 1c-1 0-1 0-2 1v-1h1c-1-1-3-2-4-3-1 0-2-1-4-2 0-1 1-1 2-1 0-1-1-2-1-3v3h-1-1c-1-3-3-5-4-8 0-1-1-2-2-2 0-2-2-2-3-3-1-2-2-3-3-4-2-2-5-2-7-4l-2-2c-2 0-3 1-5 1l-3-3c0-1-1-2-2-2 1-1 2-1 2-2v-2-1s0 1-1 1c-2 0-4-4-5-5 0 0-1-1-1-2l-6-8c-1-1-2-3-3-4-3-4-4-8-7-12h0 0c1 1 1 3 2 4 0 1 1 2 1 3 1 1 1 1 1 2h0-1v3c0 1 1 0 1 1h-1c0-1-1-2-1-2h-1c-1-1-2-3-3-5-3-5-8-11-9-16 0-1 0-1-1-1-2-9-4-19-4-28v-13h-1c0-4-4-7-6-10-3-4-4-9-4-14 0-3 1-6 2-7h0l2-5h-1l1-1 1-3-3-3h0 0c1-1 0-2 0-3l2 1c2 1 4-1 6-1h0z"></path><path d="M320 587c-1-1-1-1-1-2h0c0-1-1-2-1-2v-1l3 2h-1v3z" class="e"></path><path d="M290 501l1-1v1c1 2 2 5 1 8v-1c-2-2-2-5-2-7z" class="B"></path><path d="M294 553c2 2 4 4 5 6l-6-3 1-1v-2z" class="e"></path><path d="M285 499c1-1 1-2 2-3 1 2 1 6 0 8h0l-1-1v-1h1v-1l-2-2z" class="R"></path><path d="M285 499l2 2v1h-1v1l1 1c-1 2 0 3 0 5h-1v-1c-1-2-2-7-1-9z" class="b"></path><path d="M286 514c2 1 4 2 7 2v1l-1 1h-2l-6-3 2-1z" class="c"></path><path d="M289 535c1-1 3-2 5-3h0c0 1 0 3-1 4v1c-1 0 0 1 0 1h-2v-2s-1 0-1-1h0-1z" class="N"></path><path d="M309 513h0c2 0 2 0 3 1v2s-1 1-2 1h0c-2 0-3 0-4-1h0c2-1 2-2 3-3z" class="S"></path><path d="M290 549l4 4v2l-1 1c-3-1-5-2-7-5l1 1c1 0 1 1 2 1h2c0-2-1-3-1-4z" class="M"></path><path d="M299 520c2-3 2-5 2-9h0c1 1 1 1 1 2v2s1 0 1 1c1 1 0 3 0 4h0-1-3z" class="R"></path><path d="M280 507v-3h1c1 4 2 7 5 10l-2 1c-2-3-3-5-4-8z" class="F"></path><path d="M339 598l2 3 1 2-2 2v1l-3-5c0-1 1-2 2-3z" class="B"></path><path d="M341 601l1 2-2 2v-2c0-1 0-1 1-2z" class="h"></path><path d="M240 439c2 1 4-1 6-1-1 2-2 3-4 4v1c-2 0-3-1-4-2h0c1-1 0-2 0-3l2 1z" class="N"></path><path d="M325 588c2 1 2 2 3 4h0c0 1-1 2-1 3l1 1c-2-1-3-2-5-3 1-1 2 0 3-1h0-2l-1-1c1-1 1-2 1-2h1v-1z" class="S"></path><path d="M325 588c2 1 2 2 3 4h0c0 1-1 2-1 3v-3c-1-1-1-2-2-3v-1z" class="Z"></path><path d="M264 527l2 2 4 9c0 1 1 2 1 3h0-1c-1-1-1-2-2-2-2-4-3-8-4-12z" class="B"></path><path d="M295 547c1 0 1-1 1-1h1c2 1 3 1 4 3v1l1 6c-1-1-1-2-2-3-2-1-3-4-5-6z" class="k"></path><path d="M419 567l-1 2h-1l1-1c0-1 1-2 1-3 0 0 1 0 1-1v-1-2h0l-1 2h-1l-1 1v-1c0-2 2-4 2-6h0c1-1 0-1 0-1 1-1 1-2 1-3s1-1 1-1c1 3 0 7 0 11-1 1-2 3-2 4z" class="Z"></path><path d="M392 594l1-2c1-1 2-2 2-3l3-3c0-1 1-1 1-2h1l1-1c1-1 2-1 3-1-1 2 0 3 0 4-4 1-7 3-9 6v2l-1-2-1 2h-1z" class="S"></path><path d="M372 626c0 2 0 3 1 4 0 1 1 6 2 6 0 1 1 2 1 3v2c1 1 1 1 1 2v1c-1-1-1-2-3-3h0c-3-4-3-10-2-15z" class="E"></path><path d="M254 452c3 3 8 7 10 11v1h-1v-1h-2c-2 1-2 1-3 2 0-2-1-3-2-5h0c1-1 3 3 4 3l1-1-3-3c-2-2-3-4-4-6v-1z" class="C"></path><path d="M387 589h2l1-1h1c-1 2-3 3-4 4-3 4-7 8-9 12-1 0-1 0-2-1 1-2 3-4 4-6l7-8z" class="G"></path><path d="M246 444h1c1 3 1 5 0 8 0 3 0 6 1 8v1h-1c-1-1-1-2-2-3-2-5 0-10 1-14z" class="R"></path><defs><linearGradient id="Cj" x1="296.4" y1="546.26" x2="302.017" y2="541.134" xlink:href="#B"><stop offset="0" stop-color="#313131"></stop><stop offset="1" stop-color="#504f4f"></stop></linearGradient></defs><path fill="url(#Cj)" d="M299 539v2c2-1 3-1 5-1-1 1-1 1-1 2l-1-1h-1c0 3 1 6 0 9v-1c-1-2-2-2-4-3h-1s0 1-1 1c0-1 0-2-1-2 1-2 2-3 2-4l1-1 2-1z"></path><path d="M299 539v2s0 1-2 1c0-1 0-1-1-1l1-1 2-1z" class="Y"></path><path d="M278 540c-1 0-1-1-1-2-1-2-2-7-2-9h1c1 0 1 1 2 2h1l-1 1c0-1-2-2-2-2v2c0 2 0 4 2 7v-4c1-1 2-2 3-4h0 2 2l1 1-1 1-1 1 1 1 1 1c-1 1-2 2-4 2h0l-1 1-2-1-1 2z" class="M"></path><path d="M279 538v-3c1 0 2 2 3 3l-1 1-2-1z" class="X"></path><path d="M284 534v1h-1c-1-1-2-2-2-3h1c2 0 2 0 3 1l-1 1z" class="E"></path><path d="M250 451l1-1 1 1c1 0 1 0 2 1h0v1c1 2 2 4 4 6l3 3-1 1c-1 0-3-4-4-3h0c-1 0-2-1-2-1h-2l-1-1c-1-3-2-5-1-7z" class="D"></path><path d="M250 451l1-1 1 1c1 0 1 0 2 1h0v1c1 2 2 4 4 6 0 0 0 1 1 2l-1-1c-3-2-5-5-6-8-1-1-1-1-1-2l-1 1z" class="e"></path><path d="M293 516h0c1 0 2 0 3 1 0 1 0 1-1 2-1 3-5 5-8 6h-2c-5 1-8 0-12-2l-2-2c0-1-1-3-2-4h1c2 1 3 3 5 4 2 2 4 3 6 3l1 1c2 0 4-1 6-2 2-2 3-3 4-5l1-1v-1z" class="Z"></path><path d="M279 538l2 1c1 1 2 1 3 1h1l-2 1h-1l1 2v1l3 3c1 0 1 0 1-1h1 0c1 1 1 2 2 3 0 1 1 2 1 4h-2c-1 0-1-1-2-1l-1-1c-4-3-6-6-8-11l1-2zm-18-75h2v1h1l-3 14c0 3-1 7-2 10v-4h-1c-1-1-1-2-2-2 1-3 1-6 2-9h1l1-3c1-2 1-5 1-7z" class="E"></path><path d="M258 473h1c-1 4-1 7-1 11-1-1-1-2-2-2 1-3 1-6 2-9z" class="I"></path><defs><linearGradient id="Ck" x1="260.602" y1="499.832" x2="271.27" y2="483.808" xlink:href="#B"><stop offset="0" stop-color="#696a6b"></stop><stop offset="1" stop-color="#858283"></stop></linearGradient></defs><path fill="url(#Ck)" d="M266 478l3 1 1 1v1h0c0 3 0 5-1 8v2c-3 4-4 9-4 13l-3-3c1-4 2-7 4-11 1-3 1-6 1-9l-1-3z"></path><path d="M266 478l3 1 1 1v1h0-3l-1-3z" class="l"></path><path d="M289 535h1 0c0 1 1 1 1 1v2h2c1 0 2-1 4 0h-1c-1 1-7 2-8 3v5h0-1c0 1 0 1-1 1l-3-3v-1l-1-2h1l2-1h-1c-1 0-2 0-3-1l1-1h0c2 0 3-1 4-2l2-1h1z" class="Y"></path><path d="M283 543l2-1 1 2h0-3v-1z" class="R"></path><path d="M286 536l2-1 1 1c0 1 0 2-1 3l-4 1c-1 0-2 0-3-1l1-1h0c2 0 3-1 4-2z" class="G"></path><path d="M320 587v-3h1l2 1 2 2c1 1 2 2 4 3l1 1-1-2h0l5 5c2 1 4 3 5 4-1 1-2 2-2 3h-1l-1 1c0 1-1 2-2 3v-1c-1-1-1 0-1-1v-3c-1 0-2-1-3-2h0l-1-2-1-1c0-1 1-2 1-3h0c-1-2-1-3-3-4v1h-1l-1-1h0c-2 0-2 0-3-1z" class="k"></path><path d="M323 588c-1-1-1-1-1-2 1 0 2 1 3 2v1h-1l-1-1z" class="T"></path><path d="M327 595c0-1 1-2 1-3 1 1 2 2 2 4 0 1 0 1-1 2l-1-2-1-1z" class="b"></path><path d="M333 604c0-3 0-6 1-8 0 2 1 4 1 6 0 1-1 2-2 3v-1z" class="E"></path><path d="M386 599l1 1c0 1 0 4-1 4-1 2-3 4-4 6-4 5-7 13-7 20 0 2 0 4 1 6h-1c-1 0-2-5-2-6-1-1-1-2-1-4 2-10 8-20 14-27z" class="R"></path><path d="M373 630c0-1 0-3 1-4 0 1 1 3 1 4 0 2 0 4 1 6h-1c-1 0-2-5-2-6z" class="b"></path><path d="M270 538h1v1l3 6h0l5 7c1 0 1 0 2 1s3 2 5 2c4 2 11 2 12 7 1 1 1 2 1 3l-1-1c-1 0-1 0-2-1 0 1 0 1-1 2l-2-1-2-1-5-1-1-2v-1l-5-4c-3-5-6-9-9-14 0-1-1-2-1-3z" class="J"></path><path d="M280 555c3 0 5 3 8 3 3 1 5 2 6 4l2 1c0 1 0 1-1 2l-2-1-2-1-5-1-1-2v-1l-5-4z" class="Z"></path><path d="M292 562h2l2 1c0 1 0 1-1 2l-2-1-2-1h1v-1h0z" class="F"></path><path d="M285 559l7 3h0v1h-1l-5-1-1-2v-1z" class="X"></path><defs><linearGradient id="Cl" x1="399.236" y1="567.815" x2="405.731" y2="579.716" xlink:href="#B"><stop offset="0" stop-color="#3d3b3c"></stop><stop offset="1" stop-color="#6e6d6e"></stop></linearGradient></defs><path fill="url(#Cl)" d="M418 552c1 2-2 5-1 7l1-1c0 1-2 5-2 6-1 2-2 3-3 4-4 6-9 9-15 14l-7 6h-1l-1 1h-2c2-3 6-7 9-9 2-1 4-3 6-5 1-1 2-2 2-3 7-5 12-12 14-20z"></path><path d="M352 600l1 3 1 2c0 1 0 1 1 3h1l-1 1h0l2 6c1 1 1 2 1 3l1 2 1 1 1-2 1 2-2 2v1c-1 1-1 2-3 2h-1 0l-1-2v-3c-2-5-4-10-7-15l1-1c0-2-1-3-2-5 2 1 3 3 5 5-1-2-1-3-2-5h1 1z" class="N"></path><path d="M352 600l1 3 1 2c0 1 0 1 1 3h1l-1 1h0l2 6c1 1 1 2 1 3-1-1-2-3-2-5-2-3-3-6-4-8s-1-3-2-5h1 1z" class="D"></path><path d="M361 619l1 2-2 2v1c-1 1-1 2-3 2h-1 0l-1-2v-3c1-1 1-1 2-1s1 1 2 1h1l1-2z" class="n"></path><path d="M361 619l1 2-2 2v-1c-1-1-1 0-2 0s-2 1-3 2v-3c1-1 1-1 2-1s1 1 2 1h1l1-2z" class="Y"></path><path d="M262 501l3 3c1 2 1 4 2 7 0 2 1 4 2 6 1 1 2 3 2 4v1c2 5 2 10 3 14 0 3 1 5 1 8-3-4-4-9-6-14 0-3-2-5-2-8l-3-10c-1-4-2-7-2-11z" class="M"></path><defs><linearGradient id="Cm" x1="279.231" y1="553.295" x2="273.679" y2="555.763" xlink:href="#B"><stop offset="0" stop-color="#9d9b9d"></stop><stop offset="1" stop-color="#c4c2c2"></stop></linearGradient></defs><path fill="url(#Cm)" d="M268 539c1 0 1 1 2 2h1 0c3 5 6 9 9 14l5 4v1l1 2v1h-1l1 1v2s0 1-1 1c-2 0-4-4-5-5 0 0-1-1-1-2l-6-8c-1-1-2-3-3-4 0-2 0-3-1-4h0c0-1 0-1 1-1l-2-4z"></path><path d="M268 539c1 0 1 1 2 2h1 0c3 5 6 9 9 14l5 4v1l1 2v1h-1l1 1v2c0-1 0 0-1-1-1-3-4-6-6-9-4-4-7-8-9-13l-2-4z" class="O"></path><defs><linearGradient id="Cn" x1="312.036" y1="592.678" x2="317.577" y2="587.097" xlink:href="#B"><stop offset="0" stop-color="#585657"></stop><stop offset="1" stop-color="#6e6e6e"></stop></linearGradient></defs><path fill="url(#Cn)" d="M317 595c-4-7-9-13-14-19l2-2c7 6 12 13 17 22 0 1 3 5 3 7h-1l1 1c-1 0-1 0-2 1v-1h1c-1-1-3-2-4-3-1 0-2-1-4-2 0-1 1-1 2-1 0-1-1-2-1-3z"></path><path d="M286 562l5 1 2 1 2 1c1-1 1-1 1-2 1 1 1 1 2 1l1 1c1 2 2 3 1 4 0 2-2 3-3 4s-2 1-3 2c-2 0-3 1-5 1l-3-3c0-1-1-2-2-2 1-1 2-1 2-2v-2-1-2l-1-1h1v-1z" class="J"></path><path d="M286 569c1 1 1 2 2 4h-2c0-1-1-2-2-2 1-1 2-1 2-2z" class="R"></path><path d="M293 564l2 1 1 1c-1 0-1 1 0 2h-1v-1c-1-1-1-1-3-2h-1l-2 2v-2c1-1 2-1 4-1z" class="E"></path><path d="M292 565c2 1 2 1 3 2v1c0 1 0 1-1 2-2-1-2-2-3-4h0 0l1-1z" class="d"></path><path d="M289 567l2-2h1l-1 1h0 0c1 2 1 3 3 4-1 0-1 1-2 1-1-1-1-1-2-1 0-1-1-2-1-3z" class="o"></path><path d="M286 562l5 1 2 1c-2 0-3 0-4 1-1 0-2 1-3 2v-1-2l-1-1h1v-1z" class="V"></path><path d="M296 563c1 1 1 1 2 1l1 1c1 2 2 3 1 4 0 2-2 3-3 4s-2 1-3 2c-2 0-3 1-5 1l-3-3h2 1c2 1 3 1 5 0s3-3 4-5c-1-1-1-2-2-2l-1-1c1-1 1-1 1-2z" class="O"></path><path d="M296 563c1 1 1 1 2 1v4h0c-1-1-1-2-2-2l-1-1c1-1 1-1 1-2z" class="G"></path><defs><linearGradient id="Co" x1="239.403" y1="460.587" x2="257.408" y2="458.453" xlink:href="#B"><stop offset="0" stop-color="#838383"></stop><stop offset="1" stop-color="#a5a4a6"></stop></linearGradient></defs><path fill="url(#Co)" d="M240 448c1-1 2-3 3-4h0c-1 6-4 12 0 18 2 2 4 4 7 4 2 1 4 1 6 0l2-1c1-1 1-1 3-2 0 2 0 5-1 7l-1 3h-1c-3-1-8 0-11-2l-3-3c-5-4-6-9-6-15h0l2-5z"></path><path d="M261 463c0 2 0 5-1 7-1 0-1 0-2-1h0c1-1 1-1 1-2s-1 0-2 0-1-1-1-1l2-1c1-1 1-1 3-2z" class="m"></path><defs><linearGradient id="Cp" x1="253.634" y1="514.369" x2="271.627" y2="516.317" xlink:href="#B"><stop offset="0" stop-color="#8a8a8a"></stop><stop offset="1" stop-color="#b9b6b8"></stop></linearGradient></defs><path fill="url(#Cp)" d="M259 484v4c-1 9 0 20 2 30l3 9c1 4 2 8 4 12l2 4c-1 0-1 0-1 1h0c1 1 1 2 1 4-3-4-4-8-7-12-3-10-6-19-7-29-1-7 0-14 0-20h2l1-3z"></path><path d="M371 606h1l5-8c1-1 2-1 3-2v1c-1 2-3 4-4 6 1 1 1 1 2 1-4 5-7 10-8 16-2 5-3 11-3 16-1 3 0 7-1 9v-4h-1l-2 2v-7-4c0-5 2-10 3-15l-4 4-1-2c2-1 4-2 5-4 1-3 3-7 5-9z" class="E"></path><path d="M362 621l4-4c-1 5-3 10-3 15v4 7l2-2h1v4c-1 3-2 5-3 8l-2 2h0c-2-5-3-11-4-17 0-4 0-8-1-12h0 1c2 0 2-1 3-2v-1l2-2z" class="J"></path><path d="M363 643l2-2h1v4l-3 8h-1l1-10z" class="b"></path><path d="M360 624c0 3-1 7 0 10 1 0 2 0 2 1v1c-1 0-1 0-1-1l-1 1c1 3 1 15 2 17h1l-2 2h0c-2-5-3-11-4-17 0-4 0-8-1-12h0 1c2 0 2-1 3-2z" class="C"></path><path d="M360 624c0 3-1 7 0 10h0-1c-1-2-2-4-2-6 0-1 0-2-1-2h1c2 0 2-1 3-2z" class="V"></path><defs><linearGradient id="Cq" x1="305.132" y1="574.589" x2="328.725" y2="572.097" xlink:href="#B"><stop offset="0" stop-color="#202021"></stop><stop offset="1" stop-color="#494847"></stop></linearGradient></defs><path fill="url(#Cq)" d="M301 550c1-3 0-6 0-9h1l1 1c0 3 0 7 1 11 0 3 1 6 2 8l2 1c5 10 15 15 23 23l1 2c-3-1-5-3-8-5 0 0-1 0-2-1v1c-1 0-3-1-4-2l-4-3-5-4c-4-2-6-8-6-12-1-2-1-3-1-5l-1-6z"></path><path d="M314 575l-1-2h0c2 1 4 3 6 5 2 1 4 3 5 4 0 0-1 0-2-1v1c-1 0-3-1-4-2l-4-3h1l-1-2z" class="e"></path><path d="M314 575c1 1 3 2 4 4v1l-4-3h1l-1-2z" class="D"></path><defs><linearGradient id="Cr" x1="301.493" y1="565.641" x2="310.66" y2="545.476" xlink:href="#B"><stop offset="0" stop-color="#313231"></stop><stop offset="1" stop-color="#555255"></stop></linearGradient></defs><path fill="url(#Cr)" d="M301 550c1-3 0-6 0-9h1l1 1c0 3 0 7 1 11 0 3 1 6 2 8 1 3 3 5 4 8v1c-1-1-2-1-3-1 0-2-2-3-2-4v-1c-1-1-1-2-2-3-1-2-1-3-1-5l-1-6z"></path><path d="M313 518c3 0 4-1 6-4 1-1 1-1 2-3 0 0 0-1 1-2h0c-1 5-1 10-6 14-2 1-4 3-6 4l2 1-1 1v1c-1 2-1 4-1 6h-2c-1 1-3 1-4 1-2 1-2 1-4 1 0 0 0 1-1 1l-2 1 1-1c1-1 2-2 2-3v-2-1c1-2 0-4-1-5h-2c-2-1-2 0-4 0l-1-1h0c0-1 2-2 3-2l2-2 1-1 1-2h3 1 0c0-1 1-3 0-4 0-1-1-1-1-1v-2l1 1s2 0 2 1l1 1c1 1 2 1 4 1h0l-1 1c1 1 2 1 3 1l1-1z" class="N"></path><path d="M297 523c1 1 2 2 2 3v1h-1c-1 0-2-1-3-2l2-2z" class="U"></path><path d="M306 519c-1-1-1-2-2-3l1-1 1 1c1 1 2 1 4 1h0l-1 1c1 1 2 1 3 1h-1c-2 1-3 1-5 0z" class="F"></path><path d="M302 513l1 1s2 0 2 1l-1 1c1 1 1 2 2 3-1 1-1 2-2 2h-1 0-1l1-1h0c0-1 1-3 0-4 0-1-1-1-1-1v-2z" class="C"></path><path d="M299 520h3 1l-1 1v1c-1 2-1 4-3 5v-1c0-1-1-2-2-3l1-1 1-2z" class="b"></path><path d="M299 520h3 1l-1 1v1h-4l1-2z" class="B"></path><path d="M301 538v-3-1c0-2 1-6 2-7 1 0 1 0 2-1h0c-1 4-2 7-3 11-1 0-1 0-1 1z" class="O"></path><path d="M307 534c1-2 2-5 3-7l2 1-1 1v1c-1 2-1 4-1 6h-2-1v-2z" class="j"></path><path d="M305 526l2-2v1l-1 2v5h1v2 2h1c-1 1-3 1-4 1-2 1-2 1-4 1h1c0-1 0-1 1-1 1-4 2-7 3-11z" class="X"></path><path d="M304 537h-1c2-2 3-7 3-10v5h1v2 2h1c-1 1-3 1-4 1z" class="B"></path><path d="M269 491l4-5c1-2 3-2 4-3 2-1 5-4 7-4v1l-4 3 1 1 2-1 1-1 1 1c1 1 2 2 2 3l2 2v1l1 1c-1 0-1 2-2 3s-4 1-5 2h0c-2 3-2 5-2 9h-1v3c1 3 2 5 4 8l6 3h2c-1 2-2 3-4 5-2 1-4 2-6 2l-1-1c-2 0-4-1-6-3-2-1-3-3-5-4h-1c-1-2-2-4-2-6-1-3-1-5-2-7 0-4 1-9 4-13z" class="g"></path><path d="M281 484l2-1v3l-1 1c-1 0-2-1-3-1 0-1 2-2 2-2z" class="Y"></path><path d="M276 513c2 0 4 3 5 4h-2c-2-1-3-2-4-3l1-1z" class="O"></path><path d="M280 492v1l-2 2c-1 2-2 5-3 7v1c-1 2-1 6 0 8 0 1 1 1 1 2l-1 1v-1c-2-2-3-5-3-8 1-6 4-9 8-13z" class="Z"></path><path d="M289 489l1 1c-1 0-1 2-2 3s-4 1-5 2h0c-2 3-2 5-2 9h-1v3l-1-2c-1-2-1-7-1-10l2-2v-1c2-1 6-2 8-3h1z" class="C"></path><path d="M279 505v-6h1v-2c1-2 2-2 3-2-2 3-2 5-2 9h-1v3l-1-2z" class="G"></path><path d="M269 491l4-5c1-2 3-2 4-3 2-1 5-4 7-4v1l-4 3 1 1s-2 1-2 2c-3 3-6 5-8 9-1 3-2 5-3 8 0 3 0 6 1 9 2 3 5 7 9 8 3 2 6 2 9 1 1-1 2-1 3-3h0 2c-1 2-2 3-4 5-2 1-4 2-6 2l-1-1c-2 0-4-1-6-3-2-1-3-3-5-4h-1c-1-2-2-4-2-6-1-3-1-5-2-7 0-4 1-9 4-13z" class="V"></path><path d="M268 503s0 1-1 1v-1c0-3 1-6 3-8h1l-3 8z" class="b"></path><path d="M280 483l1 1s-2 1-2 2c-3 3-6 5-8 9h-1c2-5 6-8 10-12z" class="H"></path><path d="M308 540h8c-1 3-1 6-1 9l2 7c0 3 2 6 3 9 0 0 1 1 1 2 1 1 1 2 2 2v2s0 1 1 1c1-1 1-1 2-1v1l1 1c1-1 2-1 3-3h2 1c1 1 1 2 2 3l1 1h1l-2 3c0 3 0 4 2 5v1c6 5 10 11 13 17 1 2 1 3 2 5-2-2-3-4-5-5 1 2 2 3 2 5l-1 1c-4-5-8-11-12-15l-4-4-1-2c-8-8-18-13-23-23l-2-1c-1-2-2-5-2-8-1-4-1-8-1-11 0-1 0-1 1-2h4z" class="C"></path><path d="M324 572c1-1 1-1 2-1v1 1h-2v-1z" class="S"></path><path d="M330 570h2 1c1 1 1 2 2 3l1 1h1l-2 3-1 4h0l-7-8c1-1 2-1 3-3z" class="Y"></path><path d="M336 574c-1 0-2 0-2 1-1 0-2 0-3-1h0c0-2 1-3 2-4 1 1 1 2 2 3l1 1z" class="J"></path><defs><linearGradient id="Cs" x1="305.487" y1="578.453" x2="348.722" y2="580.391" xlink:href="#B"><stop offset="0" stop-color="#444445"></stop><stop offset="1" stop-color="#6b6969"></stop></linearGradient></defs><path fill="url(#Cs)" d="M308 562c0-3-1-5-1-7 0 2 1 3 2 4h0c2 4 5 7 7 10 1 1 2 2 4 3 3 4 9 7 12 10 6 5 10 12 15 18 1 2 2 3 2 5l-1 1c-4-5-8-11-12-15l-4-4-1-2c-8-8-18-13-23-23h0z"></path><path d="M331 585c2 1 4 3 4 5l1 1-4-4-1-2z" class="V"></path><path d="M308 540h8c-1 3-1 6-1 9l2 7c0 3 2 6 3 9 0 0 1 1 1 2 1 1 1 2 2 2v2h-2 0l-1-1h-1c0 1 0 1 1 2h0c-2-1-3-2-4-3-2-3-5-6-7-10h0c-1-1-2-2-2-4 0 2 1 4 1 7h0l-2-1c-1-2-2-5-2-8-1-4-1-8-1-11 0-1 0-1 1-2h4z" class="k"></path><path d="M321 571v-1c-1-4-3-7-5-10-1-2-3-9-2-11h1l2 7c0 3 2 6 3 9 0 0 1 1 1 2 1 1 1 2 2 2v2h-2z" class="B"></path><path d="M311 548v1h-2v3c1 2 3 4 4 6l1 1 2 3s0 1 1 2h0l-1-1h1c1 2 2 3 3 6h-4c-2-3-5-6-7-10-2-3-2-6-3-10h4l1-1z" class="j"></path><defs><linearGradient id="Ct" x1="302.778" y1="549.708" x2="307.818" y2="548.039" xlink:href="#B"><stop offset="0" stop-color="#262627"></stop><stop offset="1" stop-color="#4b4b4a"></stop></linearGradient></defs><path fill="url(#Ct)" d="M304 540h4 0 2c1 3 1 5 1 8l-1 1h-4c1 4 1 7 3 10h0c-1-1-2-2-2-4 0 2 1 4 1 7h0l-2-1c-1-2-2-5-2-8-1-4-1-8-1-11 0-1 0-1 1-2z"></path><path d="M308 562c-3-3-2-10-3-14 0 1 0 1 1 1h0c1-1-1-5 0-6v6c1 4 1 7 3 10h0c-1-1-2-2-2-4 0 2 1 4 1 7z" class="Z"></path><path d="M308 540h2c1 3 1 5 1 8l-1 1h-4v-6c0-1 1-2 1-3h1z" class="g"></path><defs><linearGradient id="Cu" x1="220.581" y1="481.778" x2="283.476" y2="523.215" xlink:href="#B"><stop offset="0" stop-color="#2c2c2c"></stop><stop offset="1" stop-color="#626161"></stop></linearGradient></defs><path fill="url(#Cu)" d="M251 525c-2-9-4-19-4-28v-13h-1c0-4-4-7-6-10-3-4-4-9-4-14 0-3 1-6 2-7 0 6 1 11 6 15l3 3c3 2 8 1 11 2-1 3-1 6-2 9 1 0 1 1 2 2h1l-1 3h-2c0 6-1 13 0 20 1 10 4 19 7 29h0 0c1 1 1 3 2 4 0 1 1 2 1 3 1 1 1 1 1 2h0-1v3c0 1 1 0 1 1h-1c0-1-1-2-1-2h-1c-1-1-2-3-3-5-3-5-8-11-9-16 0-1 0-1-1-1z"></path><path d="M256 487l-3-9c1 1 2 3 3 4 1 0 1 1 2 2h1l-1 3h-2z" class="U"></path><path d="M318 540l86-1h13v1c1 1 1 2 1 4v8c-2 8-7 15-14 20 0 1-1 2-2 3-2 2-4 4-6 5-3 2-7 6-9 9l-7 8v-1c-1 1-2 1-3 2l-5 8h-1c-2 2-4 6-5 9-1 2-3 3-5 4l-1 2-1-1-1-2c0-1 0-2-1-3l-2-6h0l1-1h-1c-1-2-1-2-1-3l-1-2-1-3h-1-1c-3-6-7-12-13-17v-1c-2-1-2-2-2-5l2-3h-1l-1-1c-1-1-1-2-2-3h-1-2c-1 2-2 2-3 3l-1-1v-1c-1 0-1 0-2 1-1 0-1-1-1-1v-2c-1 0-1-1-2-2 0-1-1-2-1-2-1-3-3-6-3-9l-2-7c0-3 0-6 1-9h2z" class="g"></path><path d="M367 603v-2c1-1 2-1 2-1l1 2-3 6v-5z" class="I"></path><path d="M375 586c2-3 4-5 6-8h0v1h0c-1 3-3 5-4 8 0-1-1-1 0-2h0c-1 0-1 1-2 1z" class="k"></path><path d="M348 562c1 1 2 3 3 4v3l-1 1h-1c-1-2-1-6-1-8z" class="E"></path><path d="M370 557c1 0 2-1 3-1h2 0c0 1-3 4-4 5v-1c-1-1 0-1-1-1v-2z" class="k"></path><path d="M356 560c1 1 3 2 3 4h-1v3c1 1 0 2 0 3-1-3-3-5-5-8 0 0 0-1-1-1 1 0 1 0 2 1 0 0 0 1 1 2l1 1v1l1-1v-2-1l-1-1v-1z" class="M"></path><path d="M359 560v-1h-2v-1h3l1 1 1-1c1 0 1 1 3 1 1 0 1-1 2-2h1l-1 2h0v1h1v-1c1-1 1-1 2-1v-1 2c-1 0-2 1-2 1l-2 3v-1-1l1-1-1-1c0 1-1 1-1 2l-2 1c0-1 0-1-1-1v-2h-1l-1 1h-1z" class="J"></path><path d="M334 566c-1-1-2-3-3-5h0l3 3v1-3c1-1 1-1 1-2 0 0 0-1 1-2l-1-1c0 1 0 1-1 2h0v-2h1v-1l2 2 3-6h0l-1 8s-1-1-2-1l-1-1c-1 2-1 4-1 6l-1 2z" class="S"></path><path d="M370 559c1 0 0 0 1 1v1l-2 3-3 5v-4h1l-1-1v-1l2-3s1-1 2-1z" class="E"></path><path d="M370 559c1 0 0 0 1 1v1l-2 3v-3l-1-1s1-1 2-1z" class="C"></path><path d="M375 586c1 0 1-1 2-1h0c-1 1 0 1 0 2l-3 9v-3l-1-1c0-1 0-1-1-2 1-1 2-3 3-4z" class="D"></path><path d="M381 578h1c1-1 2-3 3-3 1 1 1 2 1 3l1 4h-4c-1-1-1-2-2-3h0v-1z" class="C"></path><path d="M366 564l1 1h-1v4l-3 7-2 1h0c0-3 0-5 1-8 1-2 3-4 4-5z" class="F"></path><path d="M358 570c0-1 1-2 0-3v-3h1c1 1 2 4 3 5-1 3-1 5-1 8h0c0 1 0 1-1 1 0-2-1-5-2-8z" class="Z"></path><path d="M337 582s0-2 1-3l2-5 3 3v2 1c-2 2-3 3-6 3v-1z" class="M"></path><path d="M389 581c0-2 0-3-1-5v-2c1 0 2 1 3 1 2-1 1-4 2-5s1 0 3 0l2 2v1l-1-1h-2c0 1 0 1-1 2-1 2-3 5-5 8v-1z" class="G"></path><path d="M404 539h13v1h-2c0 1 0 2-1 3s-1 1-2 1-2-1-3-2l-1-1c-1 1-1 1-1 2h-1 0c-1 0-3 0-4-1 1-1 2-1 2-2v-1z" class="D"></path><defs><linearGradient id="Cv" x1="372.761" y1="592.338" x2="367.966" y2="597.546" xlink:href="#B"><stop offset="0" stop-color="#3b393b"></stop><stop offset="1" stop-color="#626261"></stop></linearGradient></defs><path fill="url(#Cv)" d="M372 590c1 1 1 1 1 2l1 1v3c0 1-1 2-2 3l-2 3-1-2s-1 0-2 1v2h0c-1-5 2-9 5-13z"></path><path d="M369 600c-1-1 0-1-1-1l1-1c2 0 2 0 3 1l-2 3-1-2z" class="G"></path><path d="M356 560c-1 0-1 0-1-1-1 0-1 0-1-1h0l2 1 2 2h1v-1h1l1-1h1v2c1 0 1 0 1 1l2-1c0-1 1-1 1-2l1 1-1 1v1 1 1c-1 1-3 3-4 5-1-1-2-4-3-5 0-2-2-3-3-4z" class="D"></path><path d="M365 561v1l-2 3h-1 0v-1c0-1 0-2 1-2l2-1z" class="N"></path><path d="M360 560l1-1h1v2c1 0 1 0 1 1-1 0-1 1-1 2v1c-1-2-2-3-2-5z" class="Z"></path><path d="M343 577c4 5 7 9 10 15v4h1v-1-1c1 0 1-1 1-2l1 2-1 1-1 2h-1l-1-1-1 1c-2-4-3-7-4-11-1-2-2-3-3-5v-1h-1v-1-2z" class="D"></path><path d="M396 570c1-2 5-9 7-10l-2 3 2 3h1v1c1-2 2-3 3-3h1l-1 1-3 7h0c0 1-1 2-2 3h0v-1l-1-2h-1-2l-2-2z" class="C"></path><path d="M400 572c2-1 3-2 4-4 0 0 1-1 1-2 1 0 1 0 2-1l-3 7h0c0 1-1 2-2 3h0v-1l-1-2h-1z" class="E"></path><path d="M343 580h1v1c1 2 2 3 3 5 1 4 2 7 4 11l1 3h-1-1c-3-6-7-12-13-17 3 0 4-1 6-3z" class="g"></path><path d="M377 564h0c1 0 1 0 1-1 0 0 1-1 1-2 1-1 1-1 2-1h1c1-1 1-4 1-5v-1 2c1 2 1 4 1 5l-1 2-10 11-5 8c-2 1-2 3-4 4h0l-1-1h1v-3l-1-4h-1l-2 2v-2c1 0 1 0 1-1l2-1v1c1 1 1 3 1 4v-3l1-1h0c3-3 5-6 7-9 1-2 3-4 4-6l1 2z" class="J"></path><path d="M383 556c1 2 1 4 1 5l-1 2v-1h0v-6zm-18 21h0c3-3 5-6 7-9 1-2 3-4 4-6l1 2c-1 7-7 12-11 18h-1c-1-1-1-1 0-3v-2z" class="C"></path><path d="M398 572h2 1l1 2v1h0c-2 2-4 4-6 5-3 2-7 6-9 9l-7 8v-1c-1 1-2 1-3 2l-5 8h-1c4-8 9-14 14-21 1-1 2-3 4-4v1c2-3 4-6 5-8 1-1 1-1 1-2h2l1 1v-1z" class="V"></path><path d="M398 572h2 1v1l-2 1c-1 0-1-1-1-1v-1z" class="b"></path><defs><linearGradient id="Cw" x1="326.888" y1="544.061" x2="317.554" y2="567.983" xlink:href="#B"><stop offset="0" stop-color="#100e0e"></stop><stop offset="1" stop-color="#363737"></stop></linearGradient></defs><path fill="url(#Cw)" d="M316 540h2c0 3 1 6 1 8 3 8 7 14 10 21l1 1c-1 2-2 2-3 3l-1-1v-1c-1 0-1 0-2 1-1 0-1-1-1-1v-2c-1 0-1-1-2-2 0-1-1-2-1-2-1-3-3-6-3-9l-2-7c0-3 0-6 1-9z"></path><path d="M324 567l3 3-1 1c-1 0-1 0-2 1-1 0-1-1-1-1v-2h0l1 1v-3z" class="Y"></path><path d="M317 556l6 9 1 2v3l-1-1h0c-1 0-1-1-2-2 0-1-1-2-1-2-1-3-3-6-3-9z" class="C"></path><path d="M321 567l1-1 2 2-1 1h0c-1 0-1-1-2-2z" class="T"></path><path d="M335 564c0-2 0-4 1-6l1 1c1 0 2 1 2 1h0c3 4 6 7 9 11 2 2 3 4 5 6 0 2 2 3 2 4v6l-1 2c-1-2-3-4-4-6-3-4-6-7-9-11-2-1-4-2-5-4l-2-2 1-2z" class="E"></path><path d="M335 564l1 1c0 1 1 2 0 3l-2-2 1-2z" class="V"></path><path d="M335 564c0-2 0-4 1-6l1 1c0 2-1 4-1 6l-1-1z" class="B"></path><path d="M384 561c1-1 3-3 5-4v1c2 0 5 0 6 1-3 5-7 8-10 12l-18 21c-4 5-9 10-11 16h-1c-1-2-1-2-1-3l-1-2-1-3-1-3 1-1 1 1h1l1-2 1-1-1-2c-1-4 3-8 5-12l2-2h1l1 4v3h-1l1 1h0c2-1 2-3 4-4l5-8 10-11 1-2z" class="R"></path><path d="M359 588h0l1 1v1 2h-1-1c0-2 0-3 1-4z" class="h"></path><path d="M354 601h3c-1 2-1 2-3 4l-1-2c0-1 0-1 1-2z" class="W"></path><path d="M360 586l1-1c1-1 1-1 2-1v1 1c-1 1-2 1-3 1v-1z" class="c"></path><path d="M355 595h1c1 2 2 3 1 4v2h-3c1-2 1-3 1-6z" class="Q"></path><path d="M351 597l1-1 1 1h1l1-2h0c0 3 0 4-1 6-1 1-1 1-1 2l-1-3-1-3z" class="E"></path><path d="M367 289c1-3 1-7 1-9v-21h75l19-1c5 0 10 1 14 0 2 0 3-1 5-2h0c0 1 0 3 1 5v2l-1-1v-1c0-1 0-2-1-4-1 1-3 1-5 2-1 1-2 1-4 2l-16 6c-1 0-5 2-5 3h3 4 0c1 1 2 1 3 0 1 0 1 1 1 1h2c1 0 3 1 5 1h0c2 1 3 1 5 3 3 2 7 4 10 7 5 6 9 14 11 22 1 2 2 4 3 7h0c0 1 1 2 2 3v-7l-9-47 1-1 2 2 5 4c0-1 0 0 1-1l6 3 6 2c-1 0-1-1-2-1h-1v-1c1 0 2 1 2 1h1 0l-2-2h1l-2-2-1-5 1-1c1-1 2-1 4-1 0 1 0 2 1 3l1 2 3 6 1 2c0-1 1-1 2-2l1 2h-1l1 1c0 1 1 1 1 2-1 1-1 1-2 1l2 7c0 1 1 5 2 7 0 1 0 2-1 3h1c0 4-1 8 0 12l1 5c1 0 2 1 2 1l2 4h0l1-1c0 1 0 1 1 2 0-1 1-1 2-1 3 0 5 1 7 0l16 1c-1-2 0-2 0-3 0 0 0 1 1 1l2 2h2c1 2 1 3 3 3 0 1-1 7-2 8-2 0-3-1-5-1v4c1 1 3 2 3 4h-2 2l1 2c-1 1-3 2-3 4v1c1 0 5-1 7-2l1 1v1c0 1-1 2-3 2l-4 2h0 2l2 1c0 1-2 1-2 2-3 1-5 3-7 5h0l-3 2s-1 0-2 1c-1 0-3 3-3 4-1 0-1 0-1 1s-1 2-2 3h-1l-2 2-1 2v1l-1 2-3 7 1 1h1v1l-3 1-3 14c0 2 0 4-1 5-1 0-2 0-3-1-1 1 0 0 0 1l-1 3v-1-2h-1v2h0 0-1c-2 0-2-1-4-2h0-1l-1 1h0l-1 1v1l-1-1v-2h0c-1-2-1-5-1-6h0c0 3-1 20 1 22h0l-5 3h-4-2c-1 0-4 0-5-1h-2-4v1l-4-2v1l-1-1-2-1c-2-2-4-3-5-4h-1l-1-1v1c-2-2-3-8-4-11 0 1-1 2-1 2 0-2 1-6 1-8l-1-1v1 2c-1 1 0 1-1 1l-4 10h0c1 2 0 3 0 5v9c1 2 1 4 2 6l1 1h0l4 4-1 1c0 2 0 3-1 5 0 5-3 9-7 12h-1c-2 3-6 3-10 3-1 0-3-1-5-2h-1l-2-1-8 1h-1-3v2 1l-2-2c-2-1-2-2-1-5-2 0-3 2-4 2v-1h0l-8 9v1l4 4 1 2h0 1c1 1 2 1 3 2 0 1 0 2 1 2l1 2c2 2 3 2 6 3-1 1-1 0-1 1h-1c0 1-1 1-2 2v1c0 1 0 1-1 2l3 3c1 0 2 1 3 2l-1 1-2 3v1l-1 1c-4 3-7 6-10 10 0 1 0 2-1 3h0v1 2c-1 1-1 2-1 3v2 3h-1 0l-3 2h-1l1 1c-1 1-3 3-5 4l-1 1h0c2 1 3 2 4 3l-1 1h-1c0 1 1 3 1 4 0 0-1 1-1 2l1 1v4h-1-1l-13-3-4-2c-2 0-3-1-4-1l-1-1h-2c-9-3-16-8-23-13-1-2-1-5-1-7v-11l1-27v-33-13-7-1-6-10-39-25-14-7-14-11c0-1-1-1-1-2h0z" class="g"></path><path d="M480 361l1 1-1 2h0v-1h-2l2-2z" class="c"></path><path d="M536 345c1 0 2 0 3-1 1 1 0 2 1 3-2 0-3-1-4-2z" class="J"></path><path d="M535 376l1 1h1v1l-3 1c0-1 0-2 1-3z" class="Y"></path><path d="M508 334h2v1l-2 2s0-1-1-1l1-2z" class="H"></path><path d="M396 494h1 0c0 2 0 2-1 3h-1 0c0-1 0-2 1-3z" class="S"></path><path d="M424 391c0-2 1-4 3-5 0 1-1 5-1 6l-1-1h-1z" class="T"></path><path d="M450 395c2-1 4-2 6-2l-4 4v-1-1l-2 1v-1z" class="O"></path><path d="M519 379h0l3-1c1 0 1 0 2 1l-4 2c0-1-1-1-1-2z" class="m"></path><path d="M477 376c0-2 1-3 2-5 0 0 0 1 1 2-1 1-2 3-2 4l-1-1z" class="H"></path><path d="M422 413l-1-1c-1 0-2 0-2-1 0 0 0-1 1-1h4l-1 1-1 2z" class="S"></path><path d="M485 324l1 2c-2 1-3 2-4 3-1-1-1-1-2-1 1-1 3-3 5-4z" class="G"></path><path d="M445 394c1 0 1-1 3-1 0 0 1 0 1 1l1 1v1l-2 1v-1s0-1-1-1l-2-1z" class="D"></path><path d="M482 358l2-2v2l-3 4-1-1c0-1 2-3 2-3z" class="G"></path><path d="M536 346v1l-5 4-1-1c1-1 1-1 1-2 1-1 2-1 3-1 0 0 1 0 1-1h1z" class="J"></path><path d="M477 376l1 1v3 7c-1-1-1-2-1-3-1-2-1-5 0-8z" class="V"></path><path d="M507 336c1 0 1 1 1 1l-4 6v-2-1l3-4z" class="K"></path><path d="M473 392l1-4 1-1 1 2v4l-1-1h0-2z" class="b"></path><path d="M518 270c0-1 1-1 2-2l1 2h-1l1 1c0 1 1 1 1 2-1 1-1 1-2 1l-1-1-1-3z" class="K"></path><path d="M518 270c0-1 1-1 2-2l1 2h-1l-1 1v1c1 0 1 0 0 1l-1-3z" class="H"></path><path d="M426 285c1 0 1-1 1-1 1-1 1 0 1-1h0 0 1l-1 1v1l-1 1h1c0-2 1-2 2-3l1-1c-1 3-4 5-5 7h-2l2-4z" class="S"></path><path d="M422 286c0-1 0-1 1-2 1-2 1-2 3-2h0c-1 0-1 0-2 1v1c0 1 1 1 1 2h0c1 0 1 0 1-1l-2 4h0 0l-1-1c0-1 0-1-1-2h0 0z" class="T"></path><path d="M537 350c1-2 1-2 2-3 1 3 1 6 0 8v-1h-1v1l-1-5z" class="C"></path><path d="M490 314l4-4c1 0 1 1 1 2v1l-1 2-1-1h-3z" class="M"></path><path d="M511 269c-1 0-1-1-2-1h-1v-1c1 0 2 1 2 1h1 0l-2-2h1c3 2 4 5 6 9-2 0-4-4-5-6z" class="C"></path><path d="M482 358c2-3 4-7 6-9h1 0c-1 3-3 7-5 9v-2l-2 2z" class="B"></path><path d="M506 393c1 2 2 4 2 5v5l1 3c-2-3-3-6-4-9l1-4z" class="E"></path><path d="M426 373h1l2 1c1 2 1 3 2 4-1 1 0 2-2 2h-1 0c-1 0 0 0-1-1h2 0c1 0 1-1 1-1h-1c-1-2-2-3-3-4v-1z" class="e"></path><path d="M519 301l1-5h1c0 4 0 9-1 12 0-2 0-4-1-6v-1z" class="c"></path><path d="M430 384h1 1c3 1 5 1 8 1l2 2h-1c-4 0-7 0-11-3zm100-12c0 1-1 1-1 2h1c-2 2-4 4-6 5-1-1-1-1-2-1l4-3 4-3z" class="d"></path><path d="M424 410c2 0 4-1 6-1h0 0 1v1l-3 4h0l-1-1c-1-2-2-2-4-2l1-1z" class="D"></path><path d="M437 410s-1 0-1-1c1-1 3 0 4-1 2-1 4-1 6-1 0 1 0 1 1 2-1 1-2 1-3 1-2-1-4-1-7-1v1z" class="O"></path><path d="M494 377h0v-2c0-2 1-4 3-5h0c0 2-1 4-2 7 0 2 1 5 0 6l-1-1c1-1 0-3 0-5z" class="c"></path><path d="M504 386h0l1 3 1 4-1 4v-2l-2-5h0 0l1-4z" class="M"></path><path d="M505 389l1 4-1 4v-2-1-5z" class="C"></path><path d="M393 442c1 1 1 1 1 0h3 0c2 0 4-2 5-3h1l-4 4h0v-1c-2 1-3 2-5 2s-4 2-5 3c1-2 2-4 4-5z" class="j"></path><path d="M444 402h1c4-1 7-3 11-5-2 3-7 6-11 6l-3 1-1-1c1 0 2 0 3-1z" class="I"></path><path d="M486 374c2-2 5-5 7-6h3c-1 1-3 2-3 4l-2 1c-1 1-2 0-3 1h-2z" class="M"></path><path d="M496 368h0l4-3-3 5h0c-2 1-3 3-3 5v2h0c0-1 0-2-1-2v-3c0-2 2-3 3-4z" class="Y"></path><path d="M423 411c2 0 3 0 4 2l1 1c-1 1-3 5-4 5h-1l-1 1c0-1 1-2 1-2l1-1v-3l-1-1-1 1v-1l1-2z" class="Z"></path><path d="M429 391c0-1 1-2 2-2s2-1 3 0c0 1-2 4-3 5-1 0-1 1-3 1h0c1-1 1-3 1-4z" class="T"></path><path d="M407 426h0-1c-2-2-3-3-4-6v-1-1h0v1c0 1 2 3 3 4 2 1 5 1 8 1h0c0 1-1 2-3 2h-3zm116-75h3c1-1 2-2 3-2l1 1 1 1-2 2-2 1h-1-1c-1-1-2-2-4-3h1 1z" class="C"></path><path d="M526 354h-1c-1-1-2-2-4-3h1 1 1c1 1 3 0 4 1l-2 2z" class="R"></path><defs><linearGradient id="Cx" x1="434.704" y1="400.983" x2="439.075" y2="404.588" xlink:href="#B"><stop offset="0" stop-color="#646160"></stop><stop offset="1" stop-color="#757677"></stop></linearGradient></defs><path fill="url(#Cx)" d="M426 399c1 1 2 1 3 2 1 0 2 1 4 1h3 4 1v1l1 1c-3 0-7 0-10-1h-1c-2-1-3-1-4-2 0-1-1-1-1-2h0z"></path><path d="M440 378c1-1 3-2 4-3l2-2v-1l2-2v1h0l-1 1-1 1c0 1 0 1-1 1-1 2-2 3-2 4h0 0l1-1 1-1h1c2-1 3-5 5-7-1 2-2 4-3 5l-2 3c-3 2-6 3-9 4l3-3z" class="C"></path><path d="M450 396l2-1v1 1c-3 2-5 4-8 5h0c-1 1-2 1-3 1v-1h-1c1-1 3-2 4-2 2-1 3-2 4-3l2-1z" class="G"></path><path d="M431 399c2-3 4-7 7-9h1c0 3-5 9-6 11-1 0-1-1-2-2z" class="D"></path><path d="M396 486c1 1 1 2 1 2h1 1c1 1 3 0 4 0h1 2c-1 0-1 1-2 1-1-1-3 0-4 0h-1l2 2c1 2 1 6 0 9v1 1c-1 0-1-2-1-3h1v-7l-2-2h-1v-1h-1v1l-1 1-1 1-1-1c0-2 1-3 2-5z" class="e"></path><path d="M464 396c0-1 2-2 3-2v6c2-4 3-7 4-12h0 1c0 2-1 5-2 7v2c0 1-1 2-1 3h-1c-1 0-1 1-2 1v2l-1-1c0-1 0-2 1-3v-2c-1-1-1-1-2-1z" class="G"></path><path d="M443 373v-1l7-8v1l-2 5-2 2v1l-2 2c-1 1-3 2-4 3 1-2 2-3 3-5z" class="E"></path><path d="M530 372c2-3 4-6 6-10v-1c0-1 1-1 1-2l1 2c-2 5-4 9-8 13h-1c0-1 1-1 1-2h0z" class="H"></path><path d="M443 373c-1 2-2 3-3 5l-3 3c3-1 6-2 9-4-1 1-1 2-1 2l-2 1-1 1h-1l-1 1h-2l-1-1-1-1h-1l8-7z" class="h"></path><path d="M445 361l7-10h0l-2 4-1 2h1 1c0-1 1-1 2-2l1-3h1c-1 2-2 3-3 5h1 1l1 1h-1-1c-3 1-4 2-7 5 0-1 0-1-1-2z" class="C"></path><path d="M429 391c0 1 0 3-1 4h0c2 0 2-1 3-1v2h-1s0 1-1 2l1 1h1c1 1 1 2 2 2v1c-2 0-3-1-4-1-1-1-2-1-3-2-1-2-1-4-1-6 1 1 1 2 1 3 0 0 0 1 1 1v1-3h0c0-2 1-2 2-4z" class="S"></path><path d="M428 396h1v2h-1l-1-1 1-1z" class="T"></path><path d="M400 508h0v6c-1 3-3 5-5 7h0c-1-5 3-10 5-13z" class="e"></path><path d="M517 367v1h0c1 0 2 1 2 1 0-2 0-2-1-3v-1s0-1-1-1h0l-1-2h0c3 2 4 4 4 8h0c0 2-2 3-3 4s-2 2-4 2l1-1v-1l1-2c1-1 1-4 2-5z" class="M"></path><path d="M525 308c1 0 2 1 2 1l2 4h0l1-1c0 1 0 1 1 2l-1 1c-1 1-2 3-3 4-2-3-2-7-2-11z" class="k"></path><path d="M468 373c1-1 1-3 2-4h1v6c0 3-1 6 0 10-1 0-1 0-1-1-1-1-2-2-2-4v-7z" class="L"></path><path d="M445 394l2 1c1 0 1 1 1 1v1c-1 1-2 2-4 3-1 0-3 1-4 2h-4l1-1c1-1 3-3 4-3 2-1 3-2 4-4zm74-100c-1-4 0-8 1-12 0 3 1 6 1 8s1 4 0 6h-1l-1 5v1 2-10z" class="C"></path><path d="M519 301v-5c1-2 1-4 1-6h1c0 2 1 4 0 6h-1l-1 5z" class="B"></path><path d="M504 341v2c-2 3-4 6-9 6l-2 1-3-1h0v-1h3c4-1 8-4 11-7z" class="P"></path><path d="M413 407h0c2-1 2-1 3-3s2-4 3-5h1c1 1 1 1 1 2l-1 1v2l-2 2h3c2 0 3-1 5 0v1c-1 1-1 1-2 1l-2 1c1-1 1-1 1-2h-6-4z" class="e"></path><path d="M520 398l-2-3v-4c0-1 1-1 1-2 1 0 2 0 3 1 0 2 0 5 2 7 0 1 0 2 1 3h-1c-2 0-2-1-4-2z" class="H"></path><path d="M455 377h0c1 1 1 1 2 1-4 4-9 7-15 9l-2-2c4-1 7-2 10-5 1 0 4-1 5-3z" class="U"></path><path d="M388 350c0-2 1-2 3-3l3-2 1 1c0 2 0 5-1 7v1h-1c-3-1-5-2-8-2v-2h3z" class="Z"></path><path d="M388 350h1c1 1 1 1 2 1l1-1c1 0 1 0 1 1s1 1 1 1v1 1h-1c-3-1-5-2-8-2v-2h3zm57 11c1 1 1 1 1 2 3-3 4-4 7-5h1c-1 1-3 2-4 2-1 2-2 3-3 4l-9 9c-1 1-2 3-3 3h-1 0l11-15z" class="B"></path><path d="M410 413l4-1c0 2 1 4 0 6-2 0-2 1-3 2h-3-2l-1-1v-1h1 1c1 0 1-1 1-2h-1c1-2 2-3 3-3z" class="J"></path><path d="M447 409v1c2 0 3 2 5 3 1 0 3-1 4-1v1l-3 1v1l-6-3h-1c-1 0-2 1-4 1l-1 1-4-3v-1-1c3 0 5 0 7 1 1 0 2 0 3-1z" class="c"></path><path d="M437 411c3 0 7 0 9 1-1 0-2 1-4 1l-1 1-4-3z" class="B"></path><path d="M436 291l2-2v-1h0c1 0 1-1 1-1 0 1 1 0 0 1 0 1-1 3-2 5l-1 1h0l-1 2h0l-1 1c-2 2-3 4-4 5h-2v1c0 1-1 2-1 2l-1 2c0-3 2-6 4-8 1-1 1-2 2-4h0 0l-1 1h0c0-1 1-2 1-3v-1-1l2-2c1 1 1 2 2 2z" class="e"></path><path d="M432 292v-1l2-2c1 1 1 2 2 2-1 3-3 6-6 8 1-1 1-2 2-4h0 0l-1 1h0c0-1 1-2 1-3v-1z" class="C"></path><defs><linearGradient id="Cy" x1="477.747" y1="373.774" x2="473.943" y2="373.275" xlink:href="#B"><stop offset="0" stop-color="#807f80"></stop><stop offset="1" stop-color="#9d9c9e"></stop></linearGradient></defs><path fill="url(#Cy)" d="M478 363h2v1l-3 5c-2 5-3 12-1 16v2h-1c-1-1-2-2-2-4-1-4 0-10 2-14v-3l3-3z"></path><defs><linearGradient id="Cz" x1="485.009" y1="373.931" x2="485.635" y2="385.703" xlink:href="#B"><stop offset="0" stop-color="#3e3d3d"></stop><stop offset="1" stop-color="#5a5a5a"></stop></linearGradient></defs><path fill="url(#Cz)" d="M486 374h2c1-1 2 0 3-1h0l-3 5c-2 2-6 7-6 10v1c-1-1-1-1-1-2 0-5 2-9 5-13z"></path><defs><linearGradient id="DA" x1="431.072" y1="285.927" x2="428.957" y2="291.518" xlink:href="#B"><stop offset="0" stop-color="#2d2c2c"></stop><stop offset="1" stop-color="#4d4d4d"></stop></linearGradient></defs><path fill="url(#DA)" d="M431 282h0c0-1 1-1 1-1h0 1l-1 1c-1 1-1 2-2 3h0v1c2 0 3 0 4 1h0 2l-1 1h0l-1 1-2 2v1c-1-1-1-1-2-1s-2 2-3 3h-1c1-1 1-2 1-3h-1c-1 1-1 2-2 2 0-1 1-2 2-4s4-4 5-7z"></path><defs><linearGradient id="DB" x1="493.73" y1="353.01" x2="482.002" y2="361.222" xlink:href="#B"><stop offset="0" stop-color="#2d2b2d"></stop><stop offset="1" stop-color="#434443"></stop></linearGradient></defs><path fill="url(#DB)" d="M490 349l3 1c-2 4-3 8-5 12v1c-1 1-2 1-3 2-1 0-2-1-2-1 2-3 3-6 5-9 1-2 2-4 2-6z"></path><path d="M551 341l1-1v1c-2 5-7 10-11 14h0c-1-1 0-3 1-5 1-4 5-7 9-9z" class="e"></path><path d="M424 391h1l1 1-1 1c0 2 0 4 1 6h0c0 1 1 1 1 2 1 1 2 1 4 2h1l-2 2v2h0 0c-2 0-5-3-6-5h0c-1-1-2-3-2-4 0-2 1-6 2-7z" class="J"></path><path d="M427 401c1 1 2 1 4 2h1l-2 2c-1-1-1-1-2-1-1-1-1-2-1-3z" class="D"></path><path d="M424 391h1l1 1-1 1c0 2 0 4 1 6h0c-2-2-2-5-2-8z" class="R"></path><defs><linearGradient id="DC" x1="405.405" y1="446.413" x2="403.725" y2="435.688" xlink:href="#B"><stop offset="0" stop-color="#2e2e2e"></stop><stop offset="1" stop-color="#494848"></stop></linearGradient></defs><path fill="url(#DC)" d="M403 439l1 1c3-1 4-6 7-7h1c0-1 1-1 2-1l-10 12c-2 3-5 7-9 10h0l2-2h0l2-2h0l1-2h0c-1-1-1-2-1-3h0l-1 1-1 1v1c0-2 2-4 2-5l4-4h0z"></path><path d="M496 336l1-1c0-1 1-2 2-3l11-19c0 1-1 3-2 4h2v1h-1c0 1 0 3-1 4v-1c-1 1-1 1-1 2s-1 3-1 4-1 2-2 3c0-1 0-2-1-3l-2 4c-1 3-3 4-5 5z" class="J"></path><path d="M506 323c0-2 2-4 3-5 0 1 0 3-1 4v-1l-2 2z" class="M"></path><path d="M508 321c-1 1-1 1-1 2s-1 3-1 4-1 2-2 3c0-1 0-2-1-3l3-4 2-2z" class="E"></path><path d="M405 467c1 1 0 4-1 5s-1 2-2 3l-6 8h0l-2 3c-1 2-4 4-6 4v-1c1 0 2-1 3-2l6-9 8-11z" class="D"></path><path d="M519 302c1 2 1 4 1 6-1 8-4 15-8 23 0 1-1 3-2 4v-1h-2c6-10 9-19 11-30v-2z" class="L"></path><defs><linearGradient id="DD" x1="494.726" y1="333.475" x2="500.051" y2="342.036" xlink:href="#B"><stop offset="0" stop-color="#454646"></stop><stop offset="1" stop-color="#797677"></stop></linearGradient></defs><path fill="url(#DD)" d="M503 327c1 1 1 2 1 3-3 6-6 12-12 16l1 1h-2l2 1h-3v1l-1-1c2-4 5-8 7-12 2-1 4-2 5-5l2-4z"></path><defs><linearGradient id="DE" x1="508.29" y1="329.271" x2="493.385" y2="346.236" xlink:href="#B"><stop offset="0" stop-color="#313031"></stop><stop offset="1" stop-color="#6c6c6d"></stop></linearGradient></defs><path fill="url(#DE)" d="M506 327l1 2v-1l1-1 1-3 1 1c-2 4-6 8-6 14v1 1c-3 3-7 6-11 7l-2-1h2l-1-1c6-4 9-10 12-16 1-1 2-2 2-3z"></path><path d="M407 360c-2 5-6 8-9 12h-1l3-3c-2 0-2 2-4 2v1h0c-1 0-1-1-2-1h0c-1 0-2 0-3-1s1-2 1-3c3-3 4-7 6-11-1 2-1 3-1 5 0 1 0 2-1 3h0 2 0c1 0-1 2-1 3h2c1-1 2-3 3-4 2-1 4-2 5-3z" class="D"></path><defs><linearGradient id="DF" x1="403.963" y1="397.631" x2="416.687" y2="412.13" xlink:href="#B"><stop offset="0" stop-color="#141313"></stop><stop offset="1" stop-color="#303030"></stop></linearGradient></defs><path fill="url(#DF)" d="M422 409c-6 1-14 2-19-1-1 0-2-2-2-2l-2-7h0l4 4c1 1 3 4 5 4 1-1 1-1 1-2l1-2v1l-1 1v1h1v-1h0l1-1c-1 1-1 2-1 2 1 1 2 1 3 1h4 6c0 1 0 1-1 2z"></path><path d="M565 337l1 1v1c0 1-1 2-3 2l-4 2h0 2l2 1c0 1-2 1-2 2-3 1-5 3-7 5h0l-3 2s-1 0-2 1c-1 0-3 3-3 4-1 0-1 0-1 1s-1 2-2 3h-1l-2 2c1-4 2-6 4-10v1s-1 1-1 2h0c-1 1-1 1-1 2h1l3-3h0l2-3c1-1 2-3 4-4 1 0 1-1 2-1h0v1h0 0c1 0 2-1 3-2s1-1 2-1c0-1 1-1 1-1v-1h-4l1-2v-3h1c1 0 5-1 7-2z" class="S"></path><path d="M413 424v-1l2-2c2-2 2-4 2-6v-1h0l2 2 1 2c-2 3-4 5-6 7s-3 5-5 7l-6 7h0-1c-1 1-3 3-5 3h0-3c0 1 0 1-1 0l9-6c3-3 5-6 8-8v-1h0l-3-1h3c2 0 3-1 3-2z" class="D"></path><path d="M503 390h0c-2-3-4-8-4-11-1-2 2-12 3-13h1c-1-1-1-2-1-3l2-1c1 0 2 0 3 1 1 0 2-1 3 0h2c-3 1-6 0-9 1v2c1 2 1 5 1 7v1c-1 1-1 2-2 2 0 4 1 7 2 10l-1 4h0 0z" class="S"></path><path d="M434 376h0 1c1 0 2-2 3-3l9-9c1-1 2-2 3-4 1 0 3-1 4-2-1 3-2 5-4 7v-1l-7 8v1l-8 7h0l-2 1h-2-2-1v-1h0 1c2 0 1-1 2-2l3-2z" class="C"></path><path d="M431 381v-1c1-1 2-2 3-2 0 1 1 1 1 2l-2 1h-2z" class="J"></path><path d="M463 292c2 0 3 1 3 2l3 3c2 2 3 5 5 8 3 8 0 15-4 22l-1 2v-1c1-3 2-6 2-9 2-7 0-14-3-21-1-1-1-2-2-3s-2-2-2-3h-1z" class="e"></path><defs><linearGradient id="DG" x1="432.179" y1="383.069" x2="450.868" y2="379.88" xlink:href="#B"><stop offset="0" stop-color="#575556"></stop><stop offset="1" stop-color="#747374"></stop></linearGradient></defs><path fill="url(#DG)" d="M448 374l1 1v2h1 0l1 1-1 2c-3 3-6 4-10 5-3 0-5 0-8-1h-1-1 0c-1-1-1-2-2-3h1 2 2l2-1h0 1l1 1 1 1h2l1-1h1l1-1 2-1s0-1 1-2l2-3z"></path><path d="M435 380h0 1l1 1c-2 3-3 1-6 1h-1l-1-1h2 2l2-1z" class="N"></path><path d="M401 503h0v2l3 1h1v1c1 1 1 2 2 3l1 3 2 3v1l3 3h-3-3-1v1l-1-1h-1l-2-1v-4c1-1 0-4-1-6v-6z" class="B"></path><path d="M404 516c1 0 2 0 3 1l1 1c-1 1-1 2-2 2v1l-1-1h1l-2-4z" class="G"></path><path d="M402 515c1 1 2 1 2 1l2 4h-1-1l-2-1v-4z" class="H"></path><path d="M422 414l1-1 1 1v3l-1 1s-1 1-1 2l1-1h1c-2 3-4 5-6 8-1 1-3 4-4 5-1 0-2 0-2 1h-1c-3 1-4 6-7 7l-1-1 6-7c2-2 3-5 5-7s4-4 6-7l2-3v-1z" class="V"></path><path d="M422 414l1-1 1 1v3l-1 1s-1 1-1 2l1-1h1c-2 3-4 5-6 8v-2c1 0 1 0 1-1 1-3 4-5 4-9v-1l-1 1v-1z" class="N"></path><path d="M387 310c-1-3-1-5-1-7 1-1 1-3 1-4v-2c-1-1 0-5 0-6l-1-1h1v-2l1-4 6-3c1 0 1-1 2-2l1 1h2c2-1 4 1 6 1 1 0 2-1 3-1v1h1 1c1-1 1-1 3-1v1c1 0 1 0 1-1h1 0v1h1 3l-2 2h2l1-2h1c0 1 1 1 1 2v1l-1-1h-2v1h-1c-1-1-3-1-4-1h0-4c-1 1-2 1-3 1v-2h-1c-2 1-3 0-5 0s-3 0-4 1l-1-1c-2 0-2 1-4 2-1 1-2 3-3 5h0c-2 3-2 9-1 12l-1 3v6z" class="S"></path><path d="M426 373c-1 0-1-1-2-1 1-2 2-3 2-5v-1h0v-2c2-3 1-7 3-10v-1c1-1 1-2 2-2 0-1-1-3 0-4l2 7-1 1c0 1 1 2 0 3h0l1-1c1 1 0 1 1 1l1 1 2 2h0c-1-1-2-1-2-1l-2-2v1c0 1 2 3 4 3 0 1 1 1 2 2h2v1h0c-4-1-7-4-9-7l-3 16-2-1h-1z" class="J"></path><defs><linearGradient id="DH" x1="462.957" y1="398.976" x2="468.976" y2="407.573" xlink:href="#B"><stop offset="0" stop-color="#7a797a"></stop><stop offset="1" stop-color="#a8a7a7"></stop></linearGradient></defs><path fill="url(#DH)" d="M473 392h2 0v1 2c-1 1 0 1-1 1l-4 10h0c-2 4-6 7-10 8-3 1-5 1-7 1v-1l3-1v-1c5-1 8-4 10-7 0-1 1-2 1-2h-1 0v-2c1 0 1-1 2-1h1c0-1 1-2 1-3v-2l1 1c1-2 1-3 2-4z"></path><path d="M469 400c0-1 1-2 1-3v-2l1 1c0 4-3 11-7 14-2 1-5 2-8 3v-1c5-1 8-4 10-7 0-1 1-2 1-2h-1 0v-2c1 0 1-1 2-1h1z" class="O"></path><path d="M466 403v-2c1 0 1-1 2-1h1c-1 2-2 3-3 5 0-1 1-2 1-2h-1 0z" class="F"></path><path d="M431 409c3 2 8 5 10 7v1h1c1 0 3 0 4 1 0 1 0 2 1 2 1 1 1 1 3 1h1 1v1h1c-4 2-11 3-16 1-2 0-3-1-4-2l-1 1-1-1v-3h1v2c1 0 1 1 2 1 3 0 3-1 5-3-1-1-2-1-3-1-1-2-3-3-3-5-1-1-2-1-2-2v-1z" class="V"></path><path d="M433 412c3 1 5 3 7 5l-1 1c-1-1-2-1-3-1-1-2-3-3-3-5z" class="J"></path><path d="M406 448h1c-1 10-8 16-14 24-1 1-2 2-2 3l-1 1v-1l-1-1h0v-1l3-6 6-8c2-4 5-7 8-11h0z" class="e"></path><defs><linearGradient id="DI" x1="483.845" y1="312.127" x2="475.441" y2="339.998" xlink:href="#B"><stop offset="0" stop-color="#3b3a3a"></stop><stop offset="1" stop-color="#646465"></stop></linearGradient></defs><path fill="url(#DI)" d="M490 314h3l1 1-8 8c-2 1-5 3-6 5h0c-2 2-5 4-7 6 0 0-1 0-1 1-2 2-5 4-8 5 7-11 17-18 26-26z"></path><path d="M535 355h0v-2-2l1-1h1 0l1 5v-1h1v1c0 2-1 4-1 6l-1-2c0 1-1 1-1 2v1c-2 4-4 7-6 10h0l-4 3 1-2h-1l-1-2c1-2 2-5 4-7 1-3 2-7 5-9h1z" class="G"></path><path d="M529 364c0 2-2 5-3 7v1c1 0 2 0 2-1h2 0 0v1h0l-4 3 1-2h-1l-1-2c1-2 2-5 4-7z" class="O"></path><path d="M535 355h0v-2-2l1-1h1 0l1 5-4 8 1-8z" class="J"></path><path d="M412 450v-1c0-2 1-3 3-5v1c0 4 2 6 4 9l9-12v2 1c-1 0-1 1-1 1l-2 2h0c-1 1-1 2-2 3s-2 3-2 4c0 0 0 1 1 1h0v1l4 4 1 2h-1v1c-1 0-1 1-2 0s-2-1-3-2l-1 1-1-1c0-1-1-2-2-2l-4-5c0-2-1-3-1-5z" class="Z"></path><path d="M412 450h0c2 0 7 10 9 12l-1 1-1-1c0-1-1-2-2-2l-4-5c0-2-1-3-1-5z" class="B"></path><defs><linearGradient id="DJ" x1="510.185" y1="362.919" x2="508.96" y2="373.315" xlink:href="#B"><stop offset="0" stop-color="#1f1e1f"></stop><stop offset="1" stop-color="#585758"></stop></linearGradient></defs><path fill="url(#DJ)" d="M504 362c1 0 4-1 5-1s2 1 3 1l1 1h0c2 0 3 2 3 3l1 1c-1 1-1 4-2 5l-1 2v1c-2 0-3 0-4-1l-3-1v-1h-1l-2 2v-1c0-2 0-5-1-7v-2c3-1 6 0 9-1h-2c-1-1-2 0-3 0-1-1-2-1-3-1z"></path><path d="M510 374h1v-1c0-1 1-2 2-2v3h1v1c-2 0-3 0-4-1z" class="C"></path><path d="M504 373l1-3h1l1 1 1 1h-1-1l-2 2v-1z" class="B"></path><path d="M513 371c0-1 0-1 1-2 0 1 1 1 1 2v1l-1 2h-1v-3z" class="H"></path><path d="M424 435c0-3-1-6 0-9 1-1 0-2 0-3 1-2 1-4 3-5h1 1v1c0 1 0 2 1 3s1 2 2 3c0 1 1 2 1 2-1 1-1 2-1 3v1c-1 0-1 1-1 1l-1 1s0 1-1 1c-1 2-1 3-2 4h0c-1 1-1 3-3 4h-1v-7h1z" class="J"></path><path d="M428 420l3 6h1v2h0c-1 1-1 4-2 5l-1-1v-1c1-3 0-5-1-8 1 0 1 0 1-1-1-1-1-1-1-2z" class="S"></path><path d="M424 435c0-1 0-1 1-1l1 1h0c0-4-1-8-2-12l4-4v-1 2c0 1 0 1 1 2 0 1 0 1-1 1 1 3 2 5 1 8v1l1 1c-2 2-4 5-5 8h-1v-6z" class="D"></path><path d="M409 484v5h0v4c-1 1-2 3-2 4v4h1 1l1 3-2 2c-1 2-1 3-1 4-1-1-1-2-2-3v-1h-1l-3-1v-2h0v-1-1-1c1-3 1-7 0-9l-2-2h1c1 0 3-1 4 0 1 0 1-1 2-1 0-1 2-4 3-4z" class="i"></path><path d="M406 494c0-1 0-2 1-3h0l2-2v4c-1 1-2 3-2 4-1 3-2 6-2 10v-1c-1-4 0-9 1-12z" class="N"></path><defs><linearGradient id="DK" x1="408.02" y1="484.188" x2="406.395" y2="491.378" xlink:href="#B"><stop offset="0" stop-color="#302f30"></stop><stop offset="1" stop-color="#4d4c4d"></stop></linearGradient></defs><path fill="url(#DK)" d="M409 484v5h0l-2 2h0c-1 1-1 2-1 3 0-2-1-3-3-5-1 1-1 0-2 1v1l-2-2h1c1 0 3-1 4 0 1 0 1-1 2-1 0-1 2-4 3-4z"></path><path d="M407 497v4h1 1l1 3-2 2c-1 2-1 3-1 4-1-1-1-2-2-3 0-4 1-7 2-10z" class="j"></path><path d="M408 501h1l1 3-2 2h0c-1-2 0-4 0-5z" class="N"></path><path d="M451 322h1c0 1 0 2 1 3 1 0 2 0 3 1s1 1 1 3c-1 0-2-1-3-1-3 1-7 3-9 6-1 1-1 3-1 5h0c1 5 6 9 4 14-1 1-1 2-2 2l-1-1c-1-2-2-4-2-5-2-5-3-10-2-15 1-3 7-5 10-7h-4l-1 1v-1l3-2 2-2v-1z" class="j"></path><path d="M467 387c0 1-1 2-2 2h0-1v1c-1 0-2 0-3 1h-1c-1 1-2 1-3 1v1c-1 0-1 0-2-1h0-4l-1-1 1-1v-1c1-2 4-3 6-4 2-2 4-4 6-5 1-1 2-1 3-1h0l2-6v7c0 2 1 3 2 4-1 1-2 1-3 1 1 1 1 1 0 2z" class="e"></path><path d="M467 387h-2 0v-1h0c0-1 0-1 1-2 0-1 1-2 1-3h0v-3h1v2c0 2 1 3 2 4-1 1-2 1-3 1 1 1 1 1 0 2z" class="J"></path><path d="M536 345c1 1 2 2 4 2h-1c-1 1-1 1-2 3h0-1l-1 1v2 2h0-1c-3 2-4 6-5 9-2 2-3 5-4 7-2 2-4 4-8 5 0-1 1-1 1-1l-1-1c1-1 3-2 3-4 4-5 5-10 7-15v-1l2-1 2-2 5-4v-1-1z" class="k"></path><path d="M536 347v2c-2 2-5 3-6 6l-1-2 2-2 5-4z" class="O"></path><defs><linearGradient id="DL" x1="521.548" y1="360.365" x2="526.11" y2="367.602" xlink:href="#B"><stop offset="0" stop-color="#8e8b8c"></stop><stop offset="1" stop-color="#a5a5a6"></stop></linearGradient></defs><path fill="url(#DL)" d="M527 354l2-1 1 2-1 3c-2 6-5 13-11 17l-1-1c1-1 3-2 3-4 4-5 5-10 7-15v-1z"></path><path d="M527 354l2-1 1 2-1 3-1-1c0-1-1-2-1-2v-1z" class="h"></path><defs><linearGradient id="DM" x1="447.085" y1="359.195" x2="470.044" y2="356.65" xlink:href="#B"><stop offset="0" stop-color="#717071"></stop><stop offset="1" stop-color="#979697"></stop></linearGradient></defs><path fill="url(#DM)" d="M472 335l1 1c-7 7-10 12-13 21-1 2-2 5-2 7v1c0 1-1 2-1 3-1 1-1 2-1 2-1 3-3 6-5 8l-1-1h0-1v-2l-1-1c1-1 2-3 3-5 3-6 5-12 8-19 2-3 4-7 5-10 3-1 6-3 8-5z"></path><defs><linearGradient id="DN" x1="524.983" y1="378.612" x2="508.758" y2="373.244" xlink:href="#B"><stop offset="0" stop-color="#515152"></stop><stop offset="1" stop-color="#6c6b6b"></stop></linearGradient></defs><path fill="url(#DN)" d="M518 375s-1 0-1 1c4-1 6-3 8-5l1 2h1l-1 2-4 3-3 1h0c0 1 1 1 1 2-4 2-9 3-13 1h0c-1 0-2-1-2-1s-2-1-2-2c-1 0-1-2-1-3 1 0 1-1 2-2l2-2h1v1l3 1c1 1 2 1 4 1l-1 1c2 0 3-1 4-2l1 1z"></path><path d="M507 373l3 1c1 1 2 1 4 1l-1 1c-2 1-4 1-7 0v-2l1-1z" class="e"></path><path d="M504 374l2-2h1v1l-1 1c-1 2 0 3 1 4 0 1 1 2 3 3 3 0 6-1 9-2 0 1 1 1 1 2-4 2-9 3-13 1h0c-1 0-2-1-2-1s-2-1-2-2c-1 0-1-2-1-3 1 0 1-1 2-2z" class="W"></path><defs><linearGradient id="DO" x1="513.765" y1="320.049" x2="508.685" y2="318.496" xlink:href="#B"><stop offset="0" stop-color="#101012"></stop><stop offset="1" stop-color="#323131"></stop></linearGradient></defs><path fill="url(#DO)" d="M519 294v10c-2 11-5 20-11 30l-1 2-3 4v-1c0-6 4-10 6-14l-1-1-1 3-1 1v1l-1-2c0-1 1-3 1-4s0-1 1-2v1c1-1 1-3 1-4h1v-1l1-1c2-3 2-6 4-9 1-4 3-8 4-13z"></path><defs><linearGradient id="DP" x1="466.575" y1="403.594" x2="446.894" y2="409.238" xlink:href="#B"><stop offset="0" stop-color="#868586"></stop><stop offset="1" stop-color="#a2a0a1"></stop></linearGradient></defs><path fill="url(#DP)" d="M464 396c1 0 1 0 2 1v2c-1 1-1 2-1 3l1 1h0 1s-1 1-1 2c-2 3-5 6-10 7-1 0-3 1-4 1-2-1-3-3-5-3v-1c-1-1-1-1-1-2 4-2 7-3 11-6 0 0 1-1 2-1 1-1 3-3 5-4z"></path><path d="M464 396c1 0 1 0 2 1v2c-1 1-1 2-1 3l1 1h0-1c-1 2-3 4-5 5v-1l1-1 1-1c0-1 0-1 1-2v-1c1-1 0-1 0-2 0 0-1 0-1 1h-1c-2 1-3 1-4 0 0 0 1-1 2-1 1-1 3-3 5-4z" class="I"></path><defs><linearGradient id="DQ" x1="482.357" y1="394.119" x2="477.488" y2="407.065" xlink:href="#B"><stop offset="0" stop-color="#535251"></stop><stop offset="1" stop-color="#6a6b6b"></stop></linearGradient></defs><path fill="url(#DQ)" d="M476 399l1-7 2-1h1c1 2 1 4 2 6 1 3 3 7 5 10 2 0 2 1 3 2 2 1 2 2 3 3h0c-1-2-2-4-3-5l-1-1v-1h-1v-3-1c2 4 4 8 7 11l5 4h-2-4v1l-4-2v1l-1-1-2-1c-2-2-4-3-5-4h-1l-1-1v1c-2-2-3-8-4-11z"></path><path d="M480 403c-1-1-1-1-1-3h1c0 1 1 2 1 3h-1z" class="h"></path><path d="M481 403c1 2 4 5 6 7 1 1 3 2 4 3h-1v1 1 1l-1-1-2-1c-2-2-4-3-5-4h-1c1 0 1 0 1-1h1c0-1-2-4-3-5v-1h1z" class="F"></path><path d="M488 401c2 4 4 8 7 11l5 4h-2-4v1l-4-2v-1-1h1c-1-1-3-2-4-3h0 1c1 1 3 2 5 3-1-2-4-4-5-6h-1c2 0 2 1 3 2 2 1 2 2 3 3h0c-1-2-2-4-3-5l-1-1v-1h-1v-3-1z" class="k"></path><path d="M490 415v-1-1h1c2 1 4 2 7 3h-4v1l-4-2z" class="a"></path><defs><linearGradient id="DR" x1="396.665" y1="475.417" x2="417.605" y2="474.428" xlink:href="#B"><stop offset="0" stop-color="#353433"></stop><stop offset="1" stop-color="#636364"></stop></linearGradient></defs><path fill="url(#DR)" d="M413 455l4 5c1 0 2 1 2 2l-1 1-19 25h-1-1s0-1-1-2h1v-1-1l-1-1h0l6-8c1-1 1-2 2-3s2-4 1-5c1 0 1-1 2-1 2-2 6-8 6-11z"></path><path d="M402 475v2c1 0 1-1 2-2v1l-5 7c-1 1-2 2-2 3v-1-1l-1-1h0l6-8z" class="j"></path><path d="M413 455l4 5c1 0 2 1 2 2l-1 1c-1-1-1-1-2-1-2 0-5 4-6 6-2 3-4 5-6 8v-1c-1 1-1 2-2 2v-2c1-1 1-2 2-3s2-4 1-5c1 0 1-1 2-1 2-2 6-8 6-11z" class="S"></path><path d="M504 386c-1-3-2-6-2-10 0 1 0 3 1 3 0 1 2 2 2 2s1 1 2 1l-2 1c1 1 2 2 3 2 1 1 6 0 6 2 1 2 1 3 1 5 0 3-1 20 1 22h0l-5 3v-1-1l-2-9h0l-1-3v-5c0-1-1-3-2-5l-1-4-1-3h0z" class="V"></path><path d="M504 386l1-1c1 1 1 2 2 2h0v1h6c1 0 0 0 1 1 0 6-1 13-2 20-1-2-3-5-3-7l-1-4c0-1-1-3-2-5l-1-4-1-3z" class="L"></path><path d="M504 386l1-1c1 1 1 2 2 2h0c1 3 2 4 2 7 0 0 1 0 1 1 1 2 1 4 0 6v1 1-1h-1l-1-4c0-1-1-3-2-5l-1-4-1-3z" class="X"></path><path d="M414 320l2 2v1c0 2-1 4-1 7v3c0 1-1 3-1 5 0 0 1 1 0 1 0 2 0 3-1 5-1 4-2 7-3 11-1 1-3 4-3 5-1 1-3 2-5 3-1 1-2 3-3 4h-2c0-1 2-3 1-3h0-2 0c1-1 1-2 1-3 0-2 0-3 1-5v-9l1 1v1c0 2 1 3 1 5h0l2-2 1 1-2 3c0 1-1 2-1 3l6-8c5-9 6-21 8-31z" class="H"></path><path d="M410 347l3-6c0-2 0-2 1-3 0 0 1 1 0 1 0 2 0 3-1 5l-2 2-1 1z" class="F"></path><path d="M410 347l1-1 2-2c-1 4-2 7-3 11-1 1-3 4-3 5-1 1-3 2-5 3-1 1-2 3-3 4h-2c0-1 2-3 1-3h0c0-1 0-2 1-2l3-4c0 1-1 2-1 3h0v1h1l2-2c0-1 2-2 2-4 2-3 4-6 4-9z" class="I"></path><path d="M427 294c1-1 2-3 3-3s1 0 2 1v1c0 1-1 2-1 3h0l1-1h0 0c-1 2-1 3-2 4-2 2-4 5-4 8-4 9-4 18-11 26h0v-3c0-3 1-5 1-7v-1l-2-2c0-2 0-4 1-7v-2l1 1c1 0 1 0 1-1 1 0 1-1 1-1 0-1 1-2 1-2 0-2 1-3 1-4 1-4 2-8 4-11 1 0 1-1 2-2h1c0 1 0 2-1 3h1z" class="E"></path><defs><linearGradient id="DS" x1="430.004" y1="291.129" x2="426.723" y2="298.945" xlink:href="#B"><stop offset="0" stop-color="#595858"></stop><stop offset="1" stop-color="#757373"></stop></linearGradient></defs><path fill="url(#DS)" d="M427 294c1-1 2-3 3-3s1 0 2 1v1c-1 1-2 2-2 3-1 1-2 2-3 4l-1-1c1-2 0-3 1-5z"></path><path d="M424 293c1 0 1-1 2-2h1c0 1 0 2-1 3h1c-1 2 0 3-1 5l1 1c-1 2-3 4-4 6l-1 1v-1l-1-1h0s-1 1-1 2v1h-1c0-2 1-3 1-4 1-4 2-8 4-11z" class="O"></path><path d="M424 293c1 0 1-1 2-2h1c0 1 0 2-1 3l-2 5c0 1-1 2-1 3-1 0-1 1-2 2h-1c1-4 2-8 4-11z" class="h"></path><path d="M419 308h1v-1c0-1 1-2 1-2h0l1 1v1h1l1 1c-1 7-4 16-8 22h-1c0-3 1-5 1-7v-1l-2-2c0-2 0-4 1-7v-2l1 1c1 0 1 0 1-1 1 0 1-1 1-1 0-1 1-2 1-2z" class="g"></path><path d="M419 308h1v-1c0-1 1-2 1-2h0l1 1v1h1c-1 1-5 10-5 10 0 1-1 3-1 4l-1 2v-1l-2-2c0-2 0-4 1-7v-2l1 1c1 0 1 0 1-1 1 0 1-1 1-1 0-1 1-2 1-2z" class="Y"></path><path d="M415 318c1 0 2-2 3-2v1c0 1-1 3-1 4l-2-1v-2z" class="F"></path><path d="M415 313v5h0v2l2 1-1 2v-1l-2-2c0-2 0-4 1-7z" class="L"></path><path d="M402 519l2 1h1l1 1v-1h1 3 3l1 1h0c2 1 3 2 4 3l-1 1h-1c0 1 1 3 1 4 0 0-1 1-1 2l1 1v4h-1-1l-13-3-4-2c-2 0-3-1-4-1v-1c1-2 3-4 5-7 1 0 1-2 2-2l1-1z" class="g"></path><path d="M407 531l3-6c1 3 3 5 4 8h0c-3-3-4-2-7-2z" class="S"></path><path d="M407 531c3 0 4-1 7 2h0l1 3-13-3h2c1-1 2-2 3-2z" class="C"></path><path d="M413 520l1 1h0c2 1 3 2 4 3l-1 1h-1c0 1 1 3 1 4 0 0-1 1-1 2-2-4-5-7-9-10h-1v-1h1 3 3z" class="E"></path><defs><linearGradient id="DT" x1="397.387" y1="523.152" x2="401.076" y2="528.175" xlink:href="#B"><stop offset="0" stop-color="#252425"></stop><stop offset="1" stop-color="#3e3d3d"></stop></linearGradient></defs><path fill="url(#DT)" d="M402 519l2 1h1l1 1h1c-1 1-1 2-2 2-1 3-3 6-6 8h-1 0c-2 0-3-1-4-1v-1c1-2 3-4 5-7 1 0 1-2 2-2l1-1z"></path><path d="M404 520h1l1 1h1c-1 1-1 2-2 2 0 0-1-2-2-2l1-1z" class="J"></path><defs><linearGradient id="DU" x1="491.053" y1="376.087" x2="486.847" y2="404.076" xlink:href="#B"><stop offset="0" stop-color="#949394"></stop><stop offset="1" stop-color="#c1c0c0"></stop></linearGradient></defs><path fill="url(#DU)" d="M493 372v3c1 0 1 1 1 2 0 2 1 4 0 5h0v1 1 1c1 5 2 10 5 15l5 11c1 2 3 4 3 6h-2c-1 0-4 0-5-1l-5-4c-3-3-5-7-7-11l-2-5-1-4c0-2-1-2-3-3v-1c0-3 4-8 6-10l3-5h0l2-1z"></path><path d="M491 373l1 1c0 3 0 5-1 8v-4-1h0l-1-1-2 2 3-5z" class="L"></path><path d="M486 396c3 2 5 6 7 9 0 1 0 1 1 2l1-1c1 2 3 4 3 6h-1l-2-2v2c-3-3-5-7-7-11l-2-5z" class="K"></path><defs><linearGradient id="DV" x1="499.379" y1="409.169" x2="496.063" y2="411.37" xlink:href="#B"><stop offset="0" stop-color="#848384"></stop><stop offset="1" stop-color="#9b9b9b"></stop></linearGradient></defs><path fill="url(#DV)" d="M493 398l1 3c1 1 2 3 3 4l8 12c-1 0-4 0-5-1l-5-4v-2l2 2h1c0-2-2-4-3-6-2-2-2-5-2-8z"></path><path d="M493 372v3c1 0 1 1 1 2 0 2 1 4 0 5h0v1 1c0 3-1 7 0 10 0 4 2 7 3 11-1-1-2-3-3-4l-1-3c-1 0-1-2-1-3-1-5-1-9-1-13 1-3 1-5 1-8l-1-1h0l2-1z" class="h"></path><path d="M493 375c1 0 1 1 1 2 0 2 1 4 0 5h0v1h-1c0-3-1-6 0-8zm1 9v1c1 5 2 10 5 15l5 11c1 2 3 4 3 6h-2l-8-12c-1-4-3-7-3-11-1-3 0-7 0-10z" class="V"></path><path d="M388 301c-1-3-1-9 1-12h0c1-2 2-4 3-5 2-1 2-2 4-2l1 1c1-1 2-1 4-1s3 1 5 0h1v2c1 0 2 0 3-1h4 0c1 0 3 0 4 1h1v-1l1 2c1 0 1 0 2 1h0 0c1 1 1 1 1 2l1 1h0 0 2c-1 2-2 3-2 4-2 3-3 7-4 11 0 1-1 2-1 4 0 0-1 1-1 2 0 0 0 1-1 1 0 1 0 1-1 1l-1-1 3-9c-1-1-1-1-1-2-1 0-2 1-2 1l-2 5c-1-2 0-4 0-5l-1-1v-1-3c-1-1-1-3 0-5-1 0-2-1-3-1-1 1-1 1-1 2l1 1c0 1-1 2-1 3l-1-1c-1 1-1 2-1 4h0 0c-1 0-1-1-1-2h-1c-1 0-1-1-1-2-1 0-1 0-1 1h-1 0-1l1-3v-1h-1v-2c0-1 0-1-1-2-1 0-2 2-3 3v-3h-1l1-1c-1 0-2 0-3 1v1c-1 1-2 2-2 3s-1 2-1 3v2c-1 1-1 2-2 3v1z" class="M"></path><path d="M396 285l2-2h3l1 1v1c2 0 2-1 4-1 0 1 0 1 1 2h1c0-1 1-1 1-2 1 0 2 1 2 1 1 0 1-1 2-1h0c0 1 1 1 1 2h2v1h2c-1 1-1 2-1 2-1 0-1 0-2 1h0v-3c-1 0-2-1-3-1s-1 0-2 1l-1-1-1 1c-2-1-2-1-3-2l-2 1h-1s-1 0-1-1c-2-1-3-1-5 0z" class="N"></path><defs><linearGradient id="DW" x1="422.544" y1="286.994" x2="411.505" y2="296.095" xlink:href="#B"><stop offset="0" stop-color="#313132"></stop><stop offset="1" stop-color="#595858"></stop></linearGradient></defs><path fill="url(#DW)" d="M413 284h0c2 0 4 0 6 1h1c1 0 1 0 2 1h0 0s-1 1-1 2l-2 3v2c0 1-1 1-2 2-1 2-2 4-2 5h-1c0-2 0-3 1-4l2-7s0-1 1-2h-2v-1h-2c0-1-1-1-1-2h0z"></path><path d="M413 284h0c2 0 4 0 6 1l-2 1h-1v-1c-1 0-2 0-3-1h0z" class="T"></path><path d="M396 285c2-1 3-1 5 0 0 1 1 1 1 1h1l2-1c1 1 1 1 3 2l1-1 1 1h0l-1 2-1 1h0v-1h-1l-2 2c0-1 0-1-1-2-2 1-2 2-3 3h-1v-2c0-1 0-1-1-2-1 0-2 2-3 3v-3h-1l1-1c-1 0-2 0-3 1v1c-1 1-2 2-2 3s-1 2-1 3v2c-1 1-1 2-2 3 0-4 2-10 4-12 1-1 1-2 2-3 1 0 1 1 2 1v-1z" class="Y"></path><path d="M396 287c1-1 1-1 2-1h2c1 1 1 1 1 2v1c2-1 2-2 3-2h1c0 1 1 1 1 1 1 0 1 0 2-1l1 1v1l-1 1h0v-1h-1l-2 2c0-1 0-1-1-2-2 1-2 2-3 3h-1v-2c0-1 0-1-1-2-1 0-2 2-3 3v-3h-1l1-1z" class="G"></path><path d="M422 286c1 1 1 1 1 2l1 1h0 0 2c-1 2-2 3-2 4-2 3-3 7-4 11 0 1-1 2-1 4 0 0-1 1-1 2 0 0 0 1-1 1 0 1 0 1-1 1l-1-1 3-9c-1-1-1-1-1-2-1 0-2 1-2 1v-1c0-1 1-3 2-5 1-1 2-1 2-2v-2l2-3c0-1 1-2 1-2z" class="M"></path><path d="M422 286c1 1 1 1 1 2l1 1h0l-2 3c-1 1-1 3-2 4v-3h-1v-2l2-3c0-1 1-2 1-2z" class="V"></path><path d="M422 286c1 1 1 1 1 2l1 1h0l-2 3c0-2 0-3-1-4 0-1 1-2 1-2z" class="E"></path><path d="M419 293h1v3l-2 5v1c-1-1-1-1-1-2-1 0-2 1-2 1v-1c0-1 1-3 2-5 1-1 2-1 2-2z" class="F"></path><path d="M410 287c1-1 1-1 2-1s2 1 3 1v3h0c1-1 1-1 2-1l-2 7c-1 1-1 2-1 4h1v1l-2 5c-1-2 0-4 0-5l-1-1v-1-3c-1-1-1-3 0-5-1 0-2-1-3-1-1 1-1 1-1 2l1 1c0 1-1 2-1 3l-1-1c-1 1-1 2-1 4h0 0c-1 0-1-1-1-2h-1c-1 0-1-1-1-2-1 0-1 0-1 1h-1 0-1l1-3v-1c1-1 1-2 3-3 1 1 1 1 1 2l2-2h1v1h0l1-1 1-2h0z" class="L"></path><path d="M401 293l3-1v1h1c0-1 0-1 1-1v2h1l1-2 1 1c0 1-1 2-1 3l-1-1c-1 1-1 2-1 4h0 0c-1 0-1-1-1-2h-1c-1 0-1-1-1-2-1 0-1 0-1 1h-1 0-1l1-3z" class="P"></path><path d="M410 287c1-1 1-1 2-1s2 1 3 1v3h0c1-1 1-1 2-1l-2 7c-1 1-1 2-1 4h1v1l-2 5c-1-2 0-4 0-5l-1-1v-1c1-3 1-6 2-8-1-2-1-3-2-4h-2 0z" class="B"></path><path d="M408 292c0-1 0-1 1-2 1 0 2 1 3 1-1 2-1 4 0 5v3 1l1 1c0 1-1 3 0 5l2-5s1-1 2-1c0 1 0 1 1 2l-3 9v2c-1 3-1 5-1 7-2 10-3 22-8 31l-6 8c0-1 1-2 1-3l2-3-1-1-2 2h0c0-2-1-3-1-5v-1l-1-1h0v-4h0v-1c-1-1-1-1-1-2l1-6c1-2 1-4 0-5 0 0 2-3 2-4 1-4 3-9 4-13l1-3c0-3 1-6 1-9h0v-1h0c0-2 0-3 1-4l1 1c0-1 1-2 1-3l-1-1z" class="W"></path><path d="M408 292c0-1 0-1 1-2 1 0 2 1 3 1-1 2-1 4 0 5v3 1l1 1c0 1-1 3 0 5 0 1-1 3-2 4v1-1-2c0-1 0-1-1-2 0-1 1-3 0-4l-3 3c0 1 0 2-1 2v2l-1 1v1c0 1 0 1-1 2h0 0c1-2 1-2 1-4h0c0-3 1-6 1-9h0v-1h0c0-2 0-3 1-4l1 1c0-1 1-2 1-3l-1-1z" class="i"></path><path d="M408 292c0-1 0-1 1-2 1 0 2 1 3 1-1 2-1 4 0 5v3 1l1 1c0 1-1 3 0 5 0 1-1 3-2 4v1-1-2h0v-1-3c-1-1 1-4 0-5 0-2 0-3-1-4l-2 2v-1c0-1 1-2 1-3l-1-1z" class="K"></path><defs><linearGradient id="DX" x1="397.018" y1="337.615" x2="412.436" y2="333.797" xlink:href="#B"><stop offset="0" stop-color="#c7c9c5"></stop><stop offset="1" stop-color="#e7e4e9"></stop></linearGradient></defs><path fill="url(#DX)" d="M401 342l4-10c1-2 2-4 2-6 1-2 1-4 2-6h0c0 1 1 1 1 2 0 2-1 3-1 5-1 3-1 7-2 10-1 2-3 6-3 8 1 2 0 5-1 7v1l-1-1-2 2h0c0-2-1-3-1-5v-1l-1-1h0v-4c1 2 1 3 1 4l2-5z"></path><path d="M404 345c1 2 0 5-1 7v1l-1-1-2 2h0c0-2-1-3-1-5v-1l1 1h1v1h1l2-5z" class="P"></path><path d="M404 312v3c1-2 2-4 3-5 0-1 0-3 1-4v-1l1-1 1-1c0 2 0 4-1 5 1 2 1 3 1 5s0 3-1 5v2h0c-1 2-1 4-2 6 0 2-1 4-2 6l-4 10-2 5c0-1 0-2-1-4h0v-1c-1-1-1-1-1-2l1-6c1-2 1-4 0-5 0 0 2-3 2-4 1-4 3-9 4-13z" class="r"></path><path d="M401 342c0 1-1 2-1 2v1l-1-1c-1-3 4-11 5-14 0-3 1-5 2-7s1-4 3-6v1 2h0c-1 2-1 4-2 6 0 2-1 4-2 6l-4 10z" class="s"></path><path d="M546 321c4 0 8 2 11 3v4c1 1 3 2 3 4h-2l-14 6h0c-6 3-13 6-20 9l-26 13c-7 4-13 7-18 13-1-1-1-2-1-2l1-3c1-1 2-3 3-4 0 0 1 1 2 1 1-1 2-1 3-2l12-11h1 3c1 0 2-1 3-1 2 0 30-23 34-27 2-1 3-2 5-3z" class="K"></path><path d="M483 364s1 1 2 1c-1 2-3 3-5 3 1-1 2-3 3-4z" class="B"></path><path d="M546 321c4 0 8 2 11 3v4c1 1 3 2 3 4h-2l-14 6v-1s1 0 2-1h0 1c1-1 1-4 1-5s0-2 1-4h0c-1 0-1-1-2-1h-2l1-1-5-1c2-1 3-2 5-3z" class="U"></path><path d="M546 321c4 0 8 2 11 3v4l-11-3-5-1c2-1 3-2 5-3z" class="B"></path><defs><linearGradient id="DY" x1="397.022" y1="316.052" x2="401.732" y2="317.425" xlink:href="#B"><stop offset="0" stop-color="#cbcaca"></stop><stop offset="1" stop-color="#f4f3f4"></stop></linearGradient></defs><path fill="url(#DY)" d="M396 287l-1 1h1v3c1-1 2-3 3-3 1 1 1 1 1 2v2h1v1l-1 3h1 0 1c0-1 0-1 1-1 0 1 0 2 1 2h1c0 1 0 2 1 2h0 0 0v1h0c0 3-1 6-1 9l-1 3c-1 4-3 9-4 13 0 1-2 4-2 4 1 1 1 3 0 5l-1 6c0 1 0 1 1 2v1h0v4l-1-4c-1-1-2-2-2-3l-4-4-1-2-1-5-1-2c-2-4-1-11 0-15 0-3-1-5-1-8l1-3v-1c1-1 1-2 2-3v-2c0-1 1-2 1-3s1-2 2-3v-1c1-1 2-1 3-1z"></path><path d="M396 334v-3-3h1l-1 6 2-5c1 1 1 3 0 5l-1 6c0-2 0-4-1-6z" class="W"></path><path d="M400 299h0c1 0 1 0 2-1v4c0 2-1 5-2 7l-2 8c-1 2-1 3-2 4s-1 1-1 2v2h-1v1c-1-4 0-7 0-10 2-2 2-4 3-6 0-3 1-5 2-7 0-1 0-2 1-4z" class="r"></path><path d="M393 305c1-2 3-12 4-13h2c0 2 0 2-1 3v2 2l1 1 1-1h0c-1 2-1 3-1 4-1 2-2 4-2 7-1 2-1 4-3 6 0 3-1 6 0 10v-1h1l-1 1c1 2 1 2 1 4s-1 3-1 5v1 1c1-1 1-1 0-2h1v-1h1c1 2 1 4 1 6 0 1 0 1 1 2v1h0v4l-1-4c-1-1-2-2-2-3l-4-4c0-3 1-7 0-10v-9c0-4 1-8 2-12z" class="i"></path><path d="M394 326c0 1-1 2-1 2h-1v-3c0-3 0-7 1-10 1-2 1-4 2-5-1 2-1 4-1 6 0 3-1 6 0 10v-1h1l-1 1z" class="W"></path><defs><linearGradient id="DZ" x1="393.249" y1="300.686" x2="399.374" y2="304.579" xlink:href="#B"><stop offset="0" stop-color="#acaaaa"></stop><stop offset="1" stop-color="#dcdcdc"></stop></linearGradient></defs><path fill="url(#DZ)" d="M393 305c1-2 3-12 4-13h2c0 2 0 2-1 3v2 2l1 1 1-1h0c-1 2-1 3-1 4-1 2-2 4-2 7-1 2-1 4-3 6 0-2 0-4 1-6v-4c1-1 1-1 1-2l-1-1 1 1c0 1-1 1-1 2h-1l-1-1z"></path><path d="M399 303h-1c-1-1-1-2-1-3h0c0-2 0-2 1-3v2l1 1 1-1h0c-1 2-1 3-1 4z" class="P"></path><path d="M396 287l-1 1h1v3c1-1 2-3 3-3 1 1 1 1 1 2v2h1v1l-1 3c0 1 0 1-1 2 0-1 0-2-1-3 1-1 1-1 1-3h-2c-1 1-3 11-4 13-1 4-2 8-2 12v9c1 3 0 7 0 10l-1-2-1-5-1-2c-2-4-1-11 0-15 0-3-1-5-1-8l1-3v-1c1-1 1-2 2-3v-2c0-1 1-2 1-3s1-2 2-3v-1c1-1 2-1 3-1z" class="F"></path><path d="M394 292h1c0 3-1 6-2 9 0 1 0 2-1 3h-1v-5c1-2 2-5 3-7z" class="a"></path><path d="M396 287l-1 1h1l-1 1c0 1-1 2-1 3-1 2-2 5-3 7v5 1c0 1 1 1 1 1 0 1-1 3-1 3h0c-1 5-3 10-3 15 0 1 1 1 1 2v3l-1-2c-2-4-1-11 0-15 0-3-1-5-1-8l1-3v-1c1-1 1-2 2-3v-2c0-1 1-2 1-3s1-2 2-3v-1c1-1 2-1 3-1z" class="B"></path><path d="M395 289c0 1-1 2-1 3-1 2-2 5-3 7v5 1c0 1 1 1 1 1 0 1-1 3-1 3h0l-1-1c0 1-1 2-1 2 0-3 0-6 1-9 1-1 1-3 1-4 1-3 2-5 4-8z" class="c"></path><path d="M556 314c-1-2 0-2 0-3 0 0 0 1 1 1l2 2h2c1 2 1 3 3 3 0 1-1 7-2 8-2 0-3-1-5-1-3-1-7-3-11-3-2 1-3 2-5 3-4 4-32 27-34 27-1 0-2 1-3 1h-3-1l27-33c1-1 2-3 3-4l1-1c0-1 1-1 2-1 3 0 5 1 7 0l16 1z" class="W"></path><path d="M533 313c3 0 5 1 7 0l16 1h1c-1 1-3 2-4 2h-1c-6 1-14 2-20 0-1-1-1 0-2-1l1-1c0-1 1-1 2-1z" class="n"></path><path d="M556 314c-1-2 0-2 0-3 0 0 0 1 1 1l2 2h2c1 2 1 3 3 3 0 1-1 7-2 8-2 0-3-1-5-1-3-1-7-3-11-3 2-1 3-2 4-3 1 0 2-1 2-2h1c1 0 3-1 4-2h-1z" class="L"></path><path d="M421 462c1 1 2 1 3 2s1 0 2 0v-1h1 0 1c1 1 2 1 3 2 0 1 0 2 1 2l1 2c2 2 3 2 6 3-1 1-1 0-1 1h-1c0 1-1 1-2 2v1c0 1 0 1-1 2l3 3c1 0 2 1 3 2l-1 1-2 3v1l-1 1c-4 3-7 6-10 10 0 1 0 2-1 3h0v1 2c-1 1-1 2-1 3v2 3h-1 0l-3 2h-1l1 1c-1 1-3 3-5 4l-1 1-1-1-3-3v-1l-2-3-1-3c0-1 0-2 1-4l2-2-1-3h-1-1v-4c0-1 1-3 2-4v-4h0v-5c-1 0-3 3-3 4h-2-1c-1 0-3 1-4 0l19-25 1-1 1 1 1-1z" class="g"></path><path d="M424 473v-1h1c1 1 0 4 0 5l-1 1h0c-1-1 0-4 0-5z" class="D"></path><path d="M413 479h0c0 2 2 3 2 5 1 1 1 2 2 3h0l-2-1h0l2 2h-1c-1-1-1-2-2-3l-1-1c-1-1 0-4 0-5z" class="e"></path><path d="M422 470h0v7c1-1 1-2 1-4h1c0 1-1 4 0 5h0l1-1v2l1 1s1 0 1 1h1c-1 0-2 1-3 0-1 1-2 1-2 2l-1-1v-8c-1-2-1-3 0-4z" class="k"></path><path d="M424 478l1-1v2l1 1s1 0 1 1h-3v-1-2z" class="e"></path><path d="M428 481c1 0 2-1 3-2l3 3h-4c-1 1-2 1-2 1h-1l1 2v1h-1l-1-1h-2c-1-1-1-1-1-2s1-1 2-2c1 1 2 0 3 0z" class="R"></path><path d="M423 483c0-1 1-1 2-2 1 2 1 3 1 4h-2c-1-1-1-1-1-2z" class="d"></path><path d="M437 481c1 0 2 1 3 2l-1 1-2 3c-1 0-2-1-3-2s-5 0-6 0l-1-2h1s1 0 2-1h4c1 0 2-1 3-1z" class="B"></path><path d="M434 485c1-1 2-1 3-2 1 0 1 0 2 1l-2 3c-1 0-2-1-3-2z" class="p"></path><path d="M409 484c2-4 2-7 3-11l1 6c0 1-1 4 0 5 0 4-2 6-4 9v-4h0v-5z" class="D"></path><path d="M421 462c1 1 2 1 3 2s1 0 2 0v-1h1 0 1c1 1 2 1 3 2 0 1 0 2 1 2l1 2c2 2 3 2 6 3-1 1-1 0-1 1h-1c0 1-1 1-2 2v1c0 1 0 1-1 2l-14-15 1-1z" class="R"></path><path d="M427 463h1c1 1 2 1 3 2-1 2-1 3-2 4-1-2-2-3-2-5v-1z" class="W"></path><path d="M431 465c0 1 0 2 1 2l1 2-1 2h-1l-2-2h0c1-1 1-2 2-4z" class="Y"></path><defs><linearGradient id="Da" x1="409.639" y1="484.406" x2="422.114" y2="485.52" xlink:href="#B"><stop offset="0" stop-color="#282728"></stop><stop offset="1" stop-color="#414141"></stop></linearGradient></defs><path fill="url(#Da)" d="M407 501c1-3 4-5 6-6l4-4s1-2 1-3c1-5 1-10 1-16 0-1 0-4 1-5 1 1 1 1 2 3-1 1-1 2 0 4v8l1 1c0 1 0 1 1 2l-1 1h0c-2 4-4 7-6 10s-6 5-7 8l-1-3h-1-1z"></path><defs><linearGradient id="Db" x1="413.93" y1="494.675" x2="427.742" y2="501.43" xlink:href="#B"><stop offset="0" stop-color="#020202"></stop><stop offset="1" stop-color="#353433"></stop></linearGradient></defs><path fill="url(#Db)" d="M428 485c1 0 5-1 6 0s2 2 3 2v1l-1 1c-4 3-7 6-10 10 0 1 0 2-1 3h0v1 2c-1 1-1 2-1 3v2 3h-1 0l-3 2h-1l1 1c-1 1-3 3-5 4l-1 1-1-1-3-3v-1l-2-3-1-3c0-1 0-2 1-4l2-2c1-3 5-5 7-8s4-6 6-10h0l1-1h2l1 1h1v-1z"></path><path d="M428 490l3-3 3 2s1 0 1-1l1 1c-4 3-7 6-10 10-3 3-5 6-8 9v-1c0-1-1-3-1-3-1 0-1 0-2-1l3-3 3-4c2-2 5-4 7-6z" class="I"></path><path d="M415 503l3-3c1 3 1 4 0 7 0-1-1-3-1-3-1 0-1 0-2-1z" class="G"></path><path d="M428 490h3c0 1-2 3-3 4l-6 3-1-1c2-2 5-4 7-6z" class="i"></path><path d="M418 508c3-3 5-6 8-9 0 1 0 2-1 3h0v1 2c-1 1-1 2-1 3v2 3h-1 0l-3 2h-1l1 1c-1 1-3 3-5 4l-1 1-1-1-3-3v-1l-2-3 4-6c1-1 2-2 3-4 1 1 1 1 2 1 0 0 1 2 1 3v1z" class="D"></path><path d="M420 515v-2-3c1 0 1-1 2-1 0-1 0-1 1-1h1v2 3h-1 0l-3 2z" class="M"></path><path d="M419 515l1 1c-1 1-3 3-5 4l-1 1-1-1-3-3v-1c1 1 3 2 4 2s3-3 5-3z" class="Q"></path><path d="M412 507c1-1 2-2 3-4 1 1 1 1 2 1 0 0 1 2 1 3v1 1c-1 2-3 3-5 5 0-1 1-2 1-3l1-1c1 0 1-1 2-1h0c0-2 0-2-1-3-1 0-1 1-2 1 0 1 0 1 1 1h0c-1 0-2 0-3-1z" class="E"></path><path d="M470 406c1 2 0 3 0 5v9c1 2 1 4 2 6l1 1h0l4 4-1 1c0 2 0 3-1 5 0 5-3 9-7 12h-1c-2 3-6 3-10 3-1 0-3-1-5-2h-1l-2-1-8 1h-1-3v2 1l-2-2c-2-1-2-2-1-5-2 0-3 2-4 2v-1h0l-8 9h0c-1 0-1-1-1-1 0-1 1-3 2-4s1-2 2-3h0l2-2s0-1 1-1v-1-2c2-3 5-6 7-10 0-1 1-3 3-4 1 0 2 0 3-1-4-1-6-3-9-5l1-1c1 1 2 2 4 2 5 2 12 1 16-1h-1v-1h-1-1c-2 0-2 0-3-1-1 0-1-1-1-2-1-1-3-1-4-1h-1v-1h2v-1l-2-1 1-1c2 0 3-1 4-1h1l6 3c2 0 4 0 7-1 4-1 8-4 10-8z" class="J"></path><path d="M436 445l1-1v2h0c0-1 1-1 2-2-1 2-1 2-1 4 0 0 0 1-1 1h-1v-4z" class="T"></path><path d="M461 418l5-3c-2 4-5 6-10 8 1 0 1-1 2-2l2-1h0l-2 1c-1 0-2 1-3 1l2-1 4-3z" class="X"></path><path d="M439 444c2-2 4-4 7-4l-8 8c0-2 0-2 1-4z" class="C"></path><path d="M435 444v-1 4h1v-2 4h1c1 1 2 1 3 1h-3v2 1l-2-2c-2-1-2-2-1-5l1-2z" class="e"></path><path d="M435 444v-1 4h1v-2 4h1c1 1 2 1 3 1h-3c-1 1 0 1-1 1h0-1v-7z" class="C"></path><path d="M459 435c1 0 2 0 3 1v-1c-1 0-1 0-1-1 2 1 1 1 2 3v-2-2h1v-1-2l-2 2h0-1l3-3h1 1v1c-1 2-2 3-2 5v3c0 1-1 2-1 3v-1l-1-1v-1c0-1 0-1-1-2l-2-1z" class="T"></path><path d="M446 413c1 2 1 3 2 4 2 1 4 2 6 2s5-2 7-1l-4 3c-3-1-5-1-8-1-1 0-1-1-2-1-1-1-1-5-1-6z" class="M"></path><path d="M435 443v-1l1-1h1c2-1 6-5 7-6v-1l2-2 1 1-1 1c1 0 3-1 4-2-2 2-3 3-5 4-2 2-4 4-6 5-1 1-2 2-2 3l-1 1v2h-1v-4zm11-31h1l-1 1c0 1 0 5 1 6 1 0 1 1 2 1 3 0 5 0 8 1l-2 1h-2-1v-1h-1-1c-2 0-2 0-3-1-1 0-1-1-1-2-1-1-3-1-4-1h-1v-1h2v-1l-2-1 1-1c2 0 3-1 4-1z" class="E"></path><path d="M438 428c2 1 4 1 6 1l1 1-4 4v1c-1 0-1 1-2 1 0 1 1 0 0 1 0 1-2 1-2 2-1 1 0 1-1 2l-1-1 2-5 3-5v-1h-1c-2 1-3 2-4 3 0-1 1-3 3-4z" class="D"></path><path d="M455 422c1 0 2-1 3-1l2-1h0l-2 1c-1 1-1 2-2 2-5 3-10 4-15 4-4-1-6-3-9-5l1-1c1 1 2 2 4 2 5 2 12 1 16-1h2z" class="f"></path><defs><linearGradient id="Dc" x1="419.275" y1="450.913" x2="438.319" y2="439.884" xlink:href="#B"><stop offset="0" stop-color="#484847"></stop><stop offset="1" stop-color="#727072"></stop></linearGradient></defs><path fill="url(#Dc)" d="M435 432c1-1 2-2 4-3h1v1l-3 5-2 5-5 7-8 9h0c-1 0-1-1-1-1 0-1 1-3 2-4s1-2 2-3h0l2-2s0-1 1-1v-1-2c2-3 5-6 7-10z"></path><path d="M435 432c1-1 2-2 4-3h1v1l-3 5s0-1-1-1c-1 1-2 3-3 5s-2 4-4 5c-1 2-1 3-4 4l2-2s0-1 1-1v-1-2c2-3 5-6 7-10z" class="B"></path><path d="M436 434c1-1 2-3 3-4h1l-3 5s0-1-1-1z" class="G"></path><path d="M470 420c1 2 1 4 2 6l1 1h0l4 4-1 1c0 2 0 3-1 5 0 5-3 9-7 12h-1c-2 3-6 3-10 3-1 0-3-1-5-2h-1l-2-1-8 1h-1c-1 0-2 0-3-1 1 0 1-1 1-1l8-8c2-2 5-5 8-6h0c2 0 3 0 5 1l2 1c1 1 1 1 1 2v1l1 1v1c0-1 1-2 1-3v-3c0-2 1-3 2-5l2 2c2-3 2-8 2-12z" class="V"></path><path d="M449 447h2l1 1c0 1 0 1-1 2l-2-1v-2z" class="B"></path><path d="M441 450l-1-1h0c2-1 7-2 9-2v2l-8 1z" class="b"></path><path d="M456 438h1c1 0 1 1 2 2 0 1-1 1-1 2h-1c-2 0-2-1-2-2s0-1 1-2z" class="D"></path><path d="M466 442c-1 1-2 2-4 3s-4 0-7 0v-1h5c1-2 2-3 2-4v-1l1 1v1 1h1c-1 1-2 2-4 2-1 0 0 0-1 1 2 0 3 0 4-1s1-2 3-2h0z" class="Z"></path><path d="M472 429l1-2 4 4-1 1c0 2 0 3-1 5 0 5-3 9-7 12h-1 0c0-1 0-1 1-1 2-2 3-3 4-5-1-1 0-1-1-1v-1h0c2-4 2-8 1-12z" class="G"></path><defs><linearGradient id="Dd" x1="474.865" y1="430.482" x2="471.204" y2="435.346" xlink:href="#B"><stop offset="0" stop-color="#1f1d1c"></stop><stop offset="1" stop-color="#343738"></stop></linearGradient></defs><path fill="url(#Dd)" d="M472 429l1-2 4 4-1 1c0 2 0 3-1 5 0-2 1-4 0-5h-1c0 4-1 7-2 11-1-1 0-1-1-1v-1h0c2-4 2-8 1-12z"></path><path d="M470 420c1 2 1 4 2 6l1 1h0l-1 2v-2h-1c-1 2-1 3-1 4 0 2-1 3-1 5-1 2-2 5-3 6h0 0c-2 0-2 1-3 2s-2 1-4 1c1-1 0-1 1-1 2 0 3-1 4-2h-1v-1c0-1 1-2 1-3v-3c0-2 1-3 2-5l2 2c2-3 2-8 2-12z" class="E"></path><path d="M490 260l1-1 2 2 5 4c0-1 0 0 1-1l6 3 6 2c1 2 3 6 5 6 1 2 1 10 1 13-2 9-7 17-11 25-1 2-3 4-4 6l-7 10c-2 3-3 5-4 8h-1c-1 0-1 0-2 1v1c-10 14-18 28-31 39-1 0-1 0-2-1h0c-1 2-4 3-5 3l1-2c2-2 4-5 5-8 0 0 0-1 1-2 0-1 1-2 1-3v-1c0-2 1-5 2-7 3-9 6-14 13-21l-1-1c0-1 1-1 1-1 2-2 5-4 7-6h0c1 0 1 0 2 1 1-1 2-2 4-3l-1-2s2-2 3-2l8-8h0 1v-3c0 1 1 2 2 3v-7l-9-47z" class="j"></path><path d="M480 328h0c1 0 1 0 2 1-3 2-6 5-9 7l-1-1c0-1 1-1 1-1 2-2 5-4 7-6z" class="h"></path><path d="M496 314l1 1c0 1-2 2-3 3l-8 8-1-2s2-2 3-2l8-8z" class="B"></path><path d="M475 341c0-1 1-2 2-3h1l-2 2v1 1c1 0 1 0 1-1l2-1v-1l1-1c0-1 1-1 2-2l-3 5-3 3-7 10c0-2 2-5 3-7 1 0 1 0 1-1h0l1-1s2-2 2-4h-1z" class="D"></path><path d="M475 341h1c0 2-2 4-2 4l-1 1h0c0 1 0 1-1 1h-1l-2 2c0 1-1 1-2 1 0 1-1 1-2 2 0-1 1-1 1-2 0-2 4-5 5-5 2-1 3-3 4-4z" class="C"></path><path d="M493 327c1-3 5-7 7-9h0l-5 6c0 1 0 1 1 2l-2 3 1-1 3-4v-1l1-1 1-1v-1h0c1-1 1-1 2-1l-7 10c-2 3-3 5-4 8h-1c-1 0-1 0-2 1l3-5-1-1h-1l4-5z" class="D"></path><path d="M493 327l1 1c-1 1-2 4-3 5l-1-1h-1l4-5z" class="Y"></path><path d="M482 336c1-2 4-5 5-6l-3 6c0 1-1 3-1 4 1-1 2-3 3-5v1l-12 17c0-1 4-8 5-9v-1l-3 1 3-3 3-5z" class="E"></path><path d="M479 341l2-2h1c-1 2-2 3-3 5v-1l-3 1 3-3z" class="O"></path><defs><linearGradient id="De" x1="474.211" y1="342.634" x2="471.491" y2="359.062" xlink:href="#B"><stop offset="0" stop-color="#6d6c6b"></stop><stop offset="1" stop-color="#989799"></stop></linearGradient></defs><path fill="url(#De)" d="M476 344l3-1v1c-1 1-5 8-5 9l-3 6c-1-1-1-1-1-2l-2 1h-1l1-1c0-1 1-2 1-3l7-10z"></path><path d="M465 352c1-1 2-1 2-2 1 0 2 0 2-1l2-2h1c-1 2-3 5-3 7 0 1-1 2-1 3l-1-1c-1 1-1 3-2 4v-2c-1 1-1 2-2 3-2 3-4 7-7 9 0 0 0-1 1-2 0-1 1-2 1-3 3-4 5-9 7-13z" class="F"></path><path d="M463 361c1-3 2-5 4-7h1l-1 2c-1 1-1 3-2 4v-2c-1 1-1 2-2 3z" class="X"></path><path d="M465 360c1-1 1-3 2-4l1 1-1 1c0 1-1 1-1 1 0 1-1 3-2 4-1 2-1 4-2 7-2 3-5 4-7 7h0c-1 2-4 3-5 3l1-2c2-2 4-5 5-8 3-2 5-6 7-9 1-1 1-2 2-3v2z" class="d"></path><path d="M465 360c1-1 1-3 2-4l1 1-1 1c0 1-1 1-1 1 0 1-1 3-2 4-1 2-1 4-2 7-2 3-5 4-7 7h0v-1c5-5 7-10 10-16z" class="L"></path><defs><linearGradient id="Df" x1="469.69" y1="353.787" x2="475.154" y2="359.149" xlink:href="#B"><stop offset="0" stop-color="#858483"></stop><stop offset="1" stop-color="#c2c1c3"></stop></linearGradient></defs><path fill="url(#Df)" d="M489 332h1l1 1-3 5v1c-10 14-18 28-31 39-1 0-1 0-2-1 2-3 5-4 7-7 1-3 1-5 2-7 1-1 2-3 2-4 0 0 1 0 1-1h1l2-1c0 1 0 1 1 2l3-6 12-17v-1l3-3z"></path><path d="M489 332h1l1 1-3 5v1-2c0-1 1-1 1-2-1 1-1 1-3 1v-1l3-3z" class="O"></path><path d="M467 358h1l2-1c0 1 0 1 1 2l-3 5-6 6c1-3 1-5 2-7 1-1 2-3 2-4 0 0 1 0 1-1z" class="H"></path><path d="M504 272c0-1-1-2-2-3l1-1h0 1v-1c1 0 2 1 3 2 1 0 4 1 4 2 1 1 2 3 2 5 1 1 1 4 1 5 0 7-1 13-4 19-2 4-5 7-8 11-1 1-2 2-3 2v1-7c1-1 3-3 4-5 2-2 4-5 5-8v-3c1-7 0-14-4-19z" class="M"></path><path d="M490 260l1-1 2 2 5 4 1 1c2 2 4 4 5 6 4 5 5 12 4 19v3c-1 3-3 6-5 8-1 2-3 4-4 5l-9-47z" class="j"></path><path d="M506 293c-1 1-2 2-4 2-1-1-1-4-1-5-1-3-2-6-2-9v-1c1 2 2 5 3 7v-1h1c2 0 2 0 3 1v1c0 1 1 1 1 2l-1 3z" class="Z"></path><path d="M493 261l5 4 1 1c2 2 4 4 5 6 4 5 5 12 4 19v3l-1-2v1h-1l1-3c0-1-1-1-1-2v-1h0c1-2-1-4-2-6-3-6-7-13-11-19v-1z" class="g"></path><defs><linearGradient id="Dg" x1="352.869" y1="397.015" x2="490.851" y2="379.398" xlink:href="#B"><stop offset="0" stop-color="#a6a5a5"></stop><stop offset="1" stop-color="#dad9d9"></stop></linearGradient></defs><path fill="url(#Dg)" d="M367 289c1-3 1-7 1-9v-21h75l19-1c5 0 10 1 14 0 2 0 3-1 5-2h0c0 1 0 3 1 5v2l-1-1v-1c0-1 0-2-1-4-1 1-3 1-5 2-1 1-2 1-4 2l-16 6c-1 0-5 2-5 3h3 4 0c1 1 2 1 3 0 1 0 1 1 1 1h2c1 0 3 1 5 1h0c2 1 3 1 5 3-5-2-9-3-13-4-9-1-17-1-26-1h-49v11c1 5 0 32 1 33 1-1 1-3 1-4h0v-6c0 3 1 5 1 8-1 4-2 11 0 15l1 2 1 5 1 2 4 4c0 1 1 2 2 3l-1 1-1 1v1l-1-1-3 2c-2 1-3 1-3 3h-3v2 108l1 37c0 7 0 15 1 22 1 4 3 7 5 10h1-2c-9-3-16-8-23-13-1-2-1-5-1-7v-11l1-27v-33-13-7-1-6-10-39-25-14-7-14-11c0-1-1-1-1-2h0z"></path><path d="M374 408l1 1v1c1 3-4 10-6 12h0l1-1h-2c1 0 0 0 1-1s3-3 4-5 2-4 1-7z" class="a"></path><path d="M373 466v4c1 5 1 15 5 19h1l2 2h1-1c-1 0-2 0-3-1-1 1 1 4 0 5l-1-2c-1-2-1-3-1-3-2 0-3 2-4 3h0-1 0v-1c1-1 2-2 3-4h0c1-1 1-2 0-2 0-2 0-3-1-5v-1c0-1 0-2-1-2v-2-1-5-2h-1c1-1 1-2 2-2z" class="H"></path><path d="M385 281c1 5 0 32 1 33 1-1 1-3 1-4h0v-6c0 3 1 5 1 8-1 4-2 11 0 15l1 2 1 5c-2 0-3-1-5-2v-51z" class="T"></path><path d="M385 332c2 1 3 2 5 2l1 2 4 4c0 1 1 2 2 3l-1 1-1 1v1l-1-1-3 2c-2 1-3 1-3 3h-3v-2c1-5 0-11 0-16z" class="g"></path><path d="M395 340c0 1 1 2 2 3l-1 1-1 1c0-1 0-2-1-3l-1 1c0 1 0 1-1 2h-1l-1-1c-2 1-2 3-3 5h0v-3c0-2 2-2 3-3 0-1 1 0 1 0l1 1v-2c1-1 1-1 2-1l1-1z" class="J"></path><path d="M387 349h0c1-2 1-4 3-5l1 1h1c1-1 1-1 1-2l1-1c1 1 1 2 1 3v1l-1-1-3 2c-2 1-3 1-3 3h-3v-2l2 1z" class="D"></path><path d="M253 205c1 1 2 2 4 2h0l-1 1c1 1 1 1 2 1h1c2 1 5 2 7 3 1 0 2 0 2 1l3 1 2 1h-6 4l1 2h0c5 2 7 4 10 8h-2c1 1 1 1 1 3h0-2-1l-3-1h-1v2h-2v1l1 1 2 1-1 1c-1 0-2-2-4-1 0 1 0 1 1 2s3 2 4 2v1h-2v1c-1 0-2 0-2-1-3-1-7-3-10-4-2 0-5-1-7-1s-3 1-5 1h0-1c-1 1-2 3-2 5v2l-3 4h2c-2 2-4 3-6 5h-1c0 1-1 8-1 8l-11 53v4h1l2-3 9-18c6-7 11-13 19-17 6-3 13-4 19-5 5-1 11 0 16 0h23l15-1c3 0 6 0 8 1h3c0 1 0 1 1 1l-1-2c2-1 7-3 9-4h1v1c3 2 6 3 9 5 0 5 1 9 3 12v1c1 2 3 3 4 4 0 1 1 1 1 2v11 14 7 14 25 39 10 6 1 7 13 33l-1 27v11c0 2 0 5 1 7 7 5 14 10 23 13h2l1 1c1 0 2 1 4 1l4 2 13 3h1l3 1c1 0 3 1 4 1 0 1 1 1 1 2h0l-7-1h-13l-86 1h-2-8-4c-2 0-3 0-5 1v-2c1 0 1-1 1-1 2 0 2 0 4-1 1 0 3 0 4-1h2c0-2 0-4 1-6v-1l1-1-2-1c2-1 4-3 6-4 5-4 5-9 6-14h0c-1 1-1 2-1 2-1 2-1 2-2 3-2 3-3 4-6 4l-1 1c-1 0-2 0-3-1l1-1c1 0 2-1 2-1v-2c-1-1-1-1-3-1h0v-1h-2l-1 1-1-2v-1c-2-4-4-7-7-10-1 0-2 0-2-1-1 0-1-2-2-3s-2-2-3-4l-1-2-1-1v-1l-2-2c0-1-1-2-2-3l-1-1-1 1-2 1-1-1 4-3v-1c-2 0-5 3-7 4-1 1-3 1-4 3l-4 5v-2c1-3 1-5 1-8h0v-1l-1-1-3-1 3-14c1-1 1-1 1-2-1 0-2 0-2-1 0-2 0-3 1-5s3-2 5-3h5c2 0 5 0 7-1v-1l-1-1h-6c-3-1-5 0-9 1-4 2-9 1-13-1-5-2-8-7-9-12h-2 0c-2 0-4 2-6 1 1 0 1 0 2-1h0l3-3c-1-1-1 0-3-1l-2-1v-1c2 0 4-1 5-3 2-1 4-2 5-4 1-3 1-10 0-13 0-4-1-8-2-11 0-1-1-2-1-3h-1v3c-1 3-1 6-3 8h0c-4 5-11 9-16 13-1 0-3 1-3 2-2 0-3 0-4 1l-2 1c0 1-1 1-1 1h0v-1l2-2h-2s-1 1-2 1c-2 1-4 3-5 5l-2 2c-1 2-2 3-2 5-1 2-2 4-2 6l-1 2c0 1 0 2-2 3l-1 1-3 5c-1 0-2 0-2 1h-1l-5 5-5 3-2 1c1 0 1-1 2-1v-1c-1 0-1 1-2 1h-1l-3 1v-1h1c8-5 11-12 14-20 2-6 3-13 3-19v-11c0-2-1-3 0-4h1c-1-1-1-3-2-4l-1-3s-1 0-1-1h-1c-1-1-2 0-3 0l-1-1c-1-1-1-2-1-3 1 1 3 3 5 3 0-1 1-1 2-1h-2v-1c0-4 0-8-2-12l-1-7c-1-1-3-1-4-2h0 1 2 0c0-3-1-6-3-8h-1 0v-2c1-2-1-4-2-6h0l-3-5-1 1h0c-2 0-2-2-3-3h-2c-1 0-3-3-3-3l-1 1-2-2-1 1-1-1v-3l-5-2h6l1-1h0v-1c-1-1-2-1-2-3l1-1h0c-1-2-2-3-4-4v-1-1c1 0 1-1 1-1 0-1 1-1 1-1h0v-1c1-2 2-2 4-3v-1h0c2-1 2-1 2-2h0v-1c0-2-1-4-2-5 0-1-2-3-2-4v-1h0l2-1v-1s-1 0-1-1v-1c-2 0-2-1-3-2l-1-1-1-1h1v-2l-1-11-1-1h0c-2-2-2-3-3-4l-1-1c-1-2-2-3-4-4l-1-1c-1-1-1-1-1-2h-6-2v-3l-1-8 2-1 2-1 4-1c2 0 3 0 4-1h1c1 0 3-2 3-3h3 1 0 2c1 2 4 4 7 4l1 2 1-1c-1-2-1-4-2-6v-1-7c1 1 3 3 3 4l2-2h1s1 0 2 1l1-1-1-1v-1-1c0-1 0-1 1-1v-2l4-5h2l4-3v-1c1-1 2-2 3-2h2l1-1v1h2v-1h-1v-1h1 1c0-1 1-1 1-1 3-2 5-3 7-4 1-2 3-3 5-4 5-2 10-4 15-7 3 0 4 0 7-1h0l15-9v-2h0c1 0 2 0 3 1l1-3z" class="j"></path><path d="M198 351h3v1l-3 2v-3z" class="N"></path><path d="M252 391v-6c2 2 1 4 2 7l-1-2-1 1z" class="e"></path><path d="M293 406c0-1 1-2 2-2h0l-1 2c-1 1-2 1-3 1 0-1 1-1 2-1z" class="Z"></path><path d="M210 237c0 1 0 1 1 2l-2 3c-1 0-1-1-2-2h1c0-1 1-2 2-3z" class="Y"></path><path d="M300 438h0c1 2 3 3 3 5h-1c-1-1-2-3-3-4h1v-1z" class="S"></path><path d="M203 275l1-2c0 2 0 3-1 5 0 1-1 1-2 1 0-1 1-3 2-4z" class="J"></path><path d="M185 346h1v3l-1 2h-1v1c0-2 1-4 1-6z" class="B"></path><path d="M293 400c2 0 3-1 4-2l1 1-3 3c-1 1-2 1-3 1 0-1 0-1 1-2v-1z" class="E"></path><path d="M328 487l2 3h0l-1 1c-1 2 0 2 1 4-1 0-2-1-3-2l-1-1-2-2c1 0 2 0 2 1h1l1-1h1c0-1-1-2-1-3z" class="C"></path><path d="M326 349l2-1v-2l1 2v3l1 3-2-1c-1-1-1-2-1-3l-1-1z" class="D"></path><path d="M216 233c0 2-4 4-5 6-1-1-1-1-1-2 2-2 4-3 6-4z" class="V"></path><path d="M194 399h0c1-2 2-3 3-4 0 1 0 4-1 5 0 1-1 1-1 2-1-1-2-1-3-2h0c0-1 1-1 2-1z" class="W"></path><path d="M198 359h1 3s0 1-1 2h0c-1 2-1 3-1 4-1-2-1-4-2-6z" class="S"></path><path d="M294 369h0c1 2 2 5 2 7 0 0-1 0-1 1v-1l-4 2v-1c1 0 3-2 4-2l-1-6z" class="T"></path><path d="M298 388c1 1 2 4 1 6 0 2-1 3-1 5l-1-1c1-3 1-6 1-10z" class="b"></path><path d="M279 361h0c2-1 5-3 7-4v1c-1 2-3 3-5 4l-1 1-1-2z" class="J"></path><path d="M197 424c1-1 2-2 4-3 0 2 0 3-2 5l-2 1v-3z" class="r"></path><path d="M207 415h0v1c2 1 4 1 6 2 0 1 1 1 2 1s1 0 2 1h-1c-3 0-8 0-10-3 0-1 0-1 1-2z" class="G"></path><path d="M201 395h1c1 0 1 1 2 2-2 2-3 3-5 4h0v-1c1-2 1-3 2-5z" class="b"></path><path d="M247 389c1 0 1-1 2-1h1l2 7h-1v-2c-1 0-1 0-2-1h0c-1-1-1-1-2-1v-2z" class="N"></path><path d="M292 381l2-2h1c-1 3-4 6-7 7h0v-1h1v-1c-1 1-2 1-3 0h-1c3 0 5-1 7-3z" class="X"></path><path d="M222 382c1 0 1 1 1 1-1 3-2 6-4 8-1-2 0-3 0-4l1-1c1-1 2-3 2-4z" class="P"></path><path d="M281 364l1-1c2 0 2-2 3-2 1-1 1-1 2-1l3-3c-1 4-5 6-8 9l-1-2z" class="S"></path><path d="M319 405l3-3h1c0 1 0 1-1 2h0v2h0l-3 3c-1 0-2 0-3 1 0-1 0-1 1-2 1 0 2-1 3-2l-1-1z" class="k"></path><path d="M279 416l1 1c2-1 4-3 6-4l1-1h0c-2 2-3 3-3 7-2-1-4-1-5-2v-1zm27-6c-2 0-4-1-5-2v-1l2-1h3v4z" class="T"></path><path d="M198 238c3-1 5 0 7-2v1 1c0 1 1 1 1 1l1 1v2 1c-2-2-3-3-5-4-1-1-2-1-4-1z" class="X"></path><path d="M312 487c0-1 1-2 1-3h1c0 1 1 2 1 3 1 2 1 4 0 6l-3-6z" class="Z"></path><path d="M320 421c1 0 1 0 1 1-1 1-3 3-5 4-2 0-4-2-6-3h1c1 0 2 1 3 1 2 0 4-2 6-3h0z" class="E"></path><path d="M329 348h0 2l2 1h0c0 1 0 1-1 2 1 0 2 0 3 1-2 1-4 1-5 2l-1-3v-3z" class="P"></path><path d="M329 348h0 2c0 1 0 2-1 3h-1v-3z" class="U"></path><path d="M312 487v-1h0v-1h0l1-1-1-1v-3l-1-5h1 1c0 1 1 3 1 4v5h-1c0 1-1 2-1 3z" class="J"></path><path d="M315 487c2 2 2 2 5 3-2 2-2 4-2 7-2-1-2-2-3-4 1-2 1-4 0-6zm-22-186c0-2-1-3-2-4s-2-1-3-2h1 0v-2c-1 0-1 0-1-1v-1l-1-1v-3h0v1c1 0 1 1 2 2v2 1c2 3 4 5 6 7l1 2c-1-1-2-1-3-1z" class="C"></path><path d="M185 346l1-1h0c1 1 0 3 1 4h-1v2-1c1 0 1-1 2-2h1c1 1 2 1 3 0h0c2 0 2 0 4 1 0 1 1 2 1 2h0c-2-1-3-1-5-1-1 0-3-1-4 0l-1 1v1h-1v-3-3h-1z" class="S"></path><path d="M302 480v-8c1-1 2-3 3-3h0c1 4 1 7 1 10l-1-1v-7c-1 2-2 3-2 5l-1 4z" class="B"></path><path d="M205 429v-1c0-1 1-5 2-6s1-1 2-1c0 1-1 5-2 7l-1 2v1c0-1 0-2-1-2z" class="M"></path><path d="M289 402h2v-1c-3-4-5-8-7-12 1 2 3 3 4 5s2 6 5 7c-1 1-1 1-1 2h-1c-1-1-2-1-2-1z" class="k"></path><path d="M326 440c-2-1-5-3-6-5v-1l2 2c4 4 11 6 14 11-2-1-3-3-5-4l-5-3z" class="M"></path><path d="M250 309c0 1 1 1 1 2-1 2 0 4 0 7l3 11c-2-3-3-6-4-9s-2-6-1-8v-1-2 7c0 1 1 2 1 3h1v-2c0-1-1-1-1-2v-1-3-2z" class="C"></path><path d="M258 383h1c0 1 1 1 1 1h1c0 1 1 1 1 1 1 1 0 1 1 1h1 0c-1 0-2-2-3-2l-1-1h0 0l-2-3v-1c2 1 5 4 6 5h0l-1 1c1 1 2 2 4 3h1l-1 1c-1 0-1 0-2-1h0c-1 0-1 0-2-1 0 0-1-1-2-1h0l-1-1c-1 0-1-1-2-2z" class="e"></path><path d="M216 233l1-1h0v2h0-1c0 1-1 2-1 3h1c-3 2-5 4-6 8l-1-2v-1l2-3c1-2 5-4 5-6z" class="C"></path><path d="M187 397c1 1 3 3 5 3h0c1 1 2 1 3 2 0-1 1-1 1-2 0 3 3 7 1 9-1-1-1-3-2-4l-1-3s-1 0-1-1h-1c-1-1-2 0-3 0l-1-1c-1-1-1-2-1-3z" class="f"></path><path d="M198 235h1c0-1 1-1 1-1 3-2 5-3 7-4-1 0-1 1-2 2h1c0-1 1-1 2-1-3 2-7 6-10 6v-1h-1v-1h1z" class="F"></path><path d="M300 386l2 2s0 1 1 1c0-1-1-1-1-2v-1l-1-2v-1h0c1 3 3 6 4 9l5 9h0c-3-2-4-5-6-8-1-2-2-4-4-7zm-104 38h1v3 2 5c-1 2-1 5-2 8h0l-1-1v1s0 1-1 1c2-6 3-13 3-19z" class="k"></path><path d="M252 391l1-1 1 2c2 2 6 4 7 6-3 0-4 0-5-2-1 0-1 0-1-1h-1v4-2c-1 0-1 0-1-1l-1-5z" class="M"></path><path d="M237 227c2 0 5 0 7 1h-1c-3 0-5 0-7 2l-7 4-2 2h0v-1c1-3 5-5 7-6 1-1 2-1 3-2z" class="S"></path><path d="M245 225h4c1 0 2 1 3 1h0v1c-2 1-6 1-8 1-2-1-5-1-7-1l4-1h0 1c1 0 2-1 3-1z" class="J"></path><path d="M214 324h1c3 6 5 11 9 16l-1 1c-3 0-5-4-7-5-1-3-3-5-3-7v1h1v1h0l1 1h0c0 1 1 2 1 2 1 1 2 3 3 4h1 1v-1h-1v-1l-1-1v-1s-1 0-1-1v-1h-1v-2l-1-1v-1h0c-1-2-1-3-2-4z" class="C"></path><path d="M270 432l-1-2h0c1-1 1 1 2 0h2 0c3 2 6 2 8 4v2c-1 0-1 1-1 1-2-2-5-4-8-5h-1-1zm-70-44v-5c2 1 5 2 6 4s0 6 0 7c-1-2-1-4-3-6-1 0-1-1-2-1l-1 1z" class="S"></path><path d="M310 401l1-1s0-1 1-2l1 1c1 1 1 1 2 3 0 0 0 1 1 2h0 0l-2 2h0v1h1v-1l1 1v-1h1 1c-1 1-3 1-5 1v-1c-1-1-3-3-3-5h0z" class="e"></path><defs><linearGradient id="Dh" x1="250.354" y1="372.14" x2="256.272" y2="379.493" xlink:href="#B"><stop offset="0" stop-color="#4e4d4f"></stop><stop offset="1" stop-color="#6d6d6d"></stop></linearGradient></defs><path fill="url(#Dh)" d="M253 368h0c1 1 1 2 1 3 1 3 2 9 0 12l-1 1c-1-2 0-4 0-6v-10z"></path><path d="M203 300v-1c1 1 1 1 1 2 1 4 2 8 2 13 0 2 1 5 0 6-2-6-3-14-3-20z" class="c"></path><path d="M305 426c-2-3-10-11-10-15l1-1h1c1 1 1 2 2 2l1 1h0-2c1 5 7 8 7 13z" class="T"></path><path d="M319 405l1 1c-1 1-2 2-3 2-1 1-1 1-1 2-3 0-7-1-10 0v-4c1 1 1 1 2 1s1 0 1 1c2 0 3 0 4-1 2 0 4 0 5-1l1-1z" class="D"></path><path d="M222 330c1 0 2 1 2 1l2 2 1 1c0 1 0 1 1 1l5 7c1 1 1 2 1 3-3-3-7-6-9-9 0-1 0-1-1-2 0-1-1-3-2-4z" class="S"></path><path d="M200 388l1-1c1 0 1 1 2 1 2 2 2 4 3 6-1 1-2 3-2 3-1-1-1-2-2-2h-1l-1-7z" class="Y"></path><path d="M204 283c1 2 1 5 1 7v7c0 1 0 1-1 1v3c0-1 0-1-1-2v1c-1-2 0-5 0-7-1-3 1-7 1-10z" class="Z"></path><path d="M203 293v-2c2 2 2 5 1 7v3c0-1 0-1-1-2v1c-1-2 0-5 0-7z" class="O"></path><path d="M242 396h0l1 1c0 1-2 4-2 5l-3 6c-1 0-1 1-2 2s-2 3-4 4l-1 1c-1-1-1-1-2-1 7-4 10-11 13-18z" class="I"></path><path d="M218 342h1c3 2 5 3 8 4 1 1 6 0 7 1 0 1 0 1-1 1-1 1-3 1-4 1-5 0-8-3-11-7z" class="X"></path><path d="M294 406h1c1 0 1-1 2-1l4-7c0 1 0 3-1 4-1 3-10 11-10 12 1 1 1 1 1 3 1 1 1 3 0 4l-1-1h0c1-1-1-5-2-6h0c2-3 5-5 6-7v-1z" class="Z"></path><path d="M261 386c1 0 2 1 2 1 1 1 1 1 2 1h0c1 1 1 1 2 1l1-1h-1c-2-1-3-2-4-3l1-1c3 2 7 5 11 7h-2c-1 0-3 1-5 0h-1c-1-1-2-1-2-1l-1-1c-1 0-1 0-2-1s-1-1-1-2z" class="C"></path><path d="M272 396l-2-2h1c1 0 1 0 2 1h1c1-1 3-1 4 0 3 3 5 4 8 5 1 1 2 1 2 2-6 0-10-3-15-7l-1 1z" class="M"></path><path d="M187 351l1-1c1-1 3 0 4 0 2 0 3 0 5 1h0 1v3 3l-1-2-2-1h-1 0c-1 0-1-1-2-2l-4 1h-1v-2z" class="N"></path><path d="M192 350c2 0 3 0 5 1h0 1v3 3l-1-2h0c0-2-3-4-5-5z" class="M"></path><path d="M300 413c1 0 1 0 1 1 2 2 4 4 6 7 0 0 0 1 1 1 1 1 1 2 2 3l1 1 1 1c1 1 1 1 1 2l3 3v1c1 0 3 2 3 2v-2h0l1 1c1-1 1-2 3-2 0 1 0 2-1 3v1l-2-2v1c1 2 4 4 6 5h-1-1l-1-1v1c1 0 1 1 1 1 0 1 1 1 1 1v1h1v1l-26-31h0z" class="J"></path><path d="M216 336c2 1 4 5 7 5l1-1 5 6h0-2c-3-1-5-2-8-4l-1-2h0c-1-2-2-3-2-4z" class="V"></path><path d="M273 391c0 1 0 1-1 1h-1l-2 1c-3-1-6-1-8-3-2-1-4-3-5-5h1c1 1 1 2 3 2h0c-1-2-2-3-4-4l1-1h0l1 1c1 1 1 2 2 2l1 1h0c0 1 0 1 1 2s1 1 2 1l1 1s1 0 2 1h1c2 1 4 0 5 0z" class="M"></path><path d="M204 283c1-1 1-1 1-2 1 2 1 3 1 5v8c0 7 1 14 2 22-1-2-1-2-2-2 0-5-1-9-2-13v-3c1 0 1 0 1-1v-7c0-2 0-5-1-7z" class="C"></path><path d="M259 437v-3h0c0-2-1-2-2-4l-1 1v-1h1v-2c1 1 1 1 1 2l1 1c1 2 1 2 1 5l1-1h1c0-1 0-1 1-1h1v-1c-1-2 0-2 0-4-1 0-1-1-1-1v-2h0v1c1 2 1 4 2 7 1 0 3-1 4 0h3c1 1 1 1 2 1l-2-2c-1 0-1 0-1-1h-1 1c2 2 5 3 7 5l4 4c1 1 2 2 3 4-3-2-5-5-8-6l-4-4c-3-1-6-1-9-1h0c-2 1-3 2-5 3z" class="J"></path><path d="M293 301c1 0 2 0 3 1l1 1c-2 4 2 16 4 20l2 5c0 2 1 5 2 7h-1v-2c-3-7-7-14-8-23 0-3-1-6-3-9z" class="M"></path><path d="M312 413c1-1 2-1 3 0 1 2 2 3 2 6l-2 1c-3-1-4-1-6-2 1-1 1-1 1-2l1-2v-1h1z" class="J"></path><path d="M280 408h2c1 0 1 0 2 1h4v1h-1c-1 1-4 3-5 3l-1-1c-1 0-3 1-4 2v1l-2 1v-1-2l-1-1c2-1 6-3 6-4z" class="R"></path><path d="M280 408h2c1 0 1 0 2 1h4v1h-1c-1-1-1 0-2 0-1-1-4-1-5 0-1 0-2 2-3 2 0 1-1 2-2 3h0v-2l-1-1c2-1 6-3 6-4z" class="e"></path><path d="M277 415v-1c1-1 3-2 4-2l1 1-3 3v1c1 1 3 1 5 2 1 0 2 1 3 2h-2 0c-2 0-4 1-6 0-1-1-3-2-4-3 0-1 1-2 2-3z" class="G"></path><path d="M218 387h1c0 1-1 2 0 4 1 3-1 7-2 10s-2 7-4 11c0-1-1-2 0-3l3-14c1-2 2-4 1-6v-1h0l1-1z" class="E"></path><path d="M219 235c1-1 2-2 4-3l3-2v1c-6 6-10 15-12 23-1-1-1-2-1-3 1-5 3-11 6-16z" class="B"></path><path d="M294 369l-1-1c-1-3-1-7-1-10-1-2-1-5-1-7 1-1 1-3 1-4v2c1 3 1 10 3 13v1 1 5 1c1 3 2 4 1 7h1c0 2 0 3 1 5l1 1v1h0l1 1h0v1c-1-1-3-4-3-6v-1-1h-1l-1 1c1-1 1-2 1-3 0-2-1-5-2-7h0z" class="D"></path><path d="M297 303c1 2 2 4 2 6 1 5 3 11 4 16v3l-2-5c-2-4-6-16-4-20z" class="g"></path><path d="M223 416h1c2-1 4-1 5-2 1 0 1 0 2 1-2 1-7 5-9 4h0-1c-1 1-2 1-4 1h0c-1-1-1-1-2-1s-2 0-2-1c-2-1-4-1-6-2v-1h2 1c1 0 1 1 1 1 2 1 3 1 5 1h2 3 1l1-1z" class="X"></path><path d="M222 417h1v1h-3 1l-1-1h1 1z" class="f"></path><path d="M294 390h1 1v1 1 1 1 1l1 1v-1c1-2 0-5 1-7h0c0 4 0 7-1 10-1 1-2 2-4 2 1-2-1-4-2-7 0-1-2-3-2-4h1c1 1 1 1 2 1l1-1s1 0 1 1z" class="D"></path><path d="M291 393c0-1-2-3-2-4h1c1 1 1 1 2 1l1-1s1 0 1 1c1 1 2 2 1 3v1c0 1 1 2 0 2s-1 0-1-1c-1-1-2-1-3-2z" class="Z"></path><path d="M199 270l1 1h2 0c0 2 0 2 1 4-1 1-2 3-2 4h0c0 1-1 2-1 2 0 2-1 5 0 6 1 2 0 3 1 4v-1 5c-1 2 0 4-1 6v-9c-1-5 0-9-1-14h0l-1-1c0-1-1-2-2-4h0 1c1-1 1-1 1-2h1v-1z" class="k"></path><path d="M199 270l1 1h2 0c0 2 0 2 1 4-1 1-2 3-2 4h0c0-3-1-6-2-8v-1z" class="W"></path><path d="M216 237c0-1 1-1 1-1 0-1 0-1 1 0h1v-1c-3 5-5 11-6 16l-1 1v1c0-2 0-3-1-5 0-1 0-2-1-3 1-4 3-6 6-8z" class="J"></path><path d="M272 396l1-1c5 4 9 7 15 7h1s1 0 2 1l-8 1c-4 0-10-2-13-5-1 0-2-2-2-3 1 0 2 2 3 3 3 2 6 3 9 4-2-3-5-5-8-7z" class="B"></path><defs><linearGradient id="Di" x1="247.681" y1="220.366" x2="249.53" y2="225.478" xlink:href="#B"><stop offset="0" stop-color="#1c1c1c"></stop><stop offset="1" stop-color="#383738"></stop></linearGradient></defs><path fill="url(#Di)" d="M245 225h-7c3-2 5-4 9-4h1 3c4 0 7 1 10 3-1 1-6-1-8-1h-3l-1 1 3 2c-1 0-2-1-3-1h-4z"></path><path d="M249 225h-1c-1 0-2 0-3-1h0c2-1 3-1 5-1l-1 1 3 2c-1 0-2-1-3-1z" class="V"></path><defs><linearGradient id="Dj" x1="215.788" y1="255.818" x2="226.688" y2="237.306" xlink:href="#B"><stop offset="0" stop-color="#5b5a5b"></stop><stop offset="1" stop-color="#7a787a"></stop></linearGradient></defs><path fill="url(#Dj)" d="M227 236h1l-1 1h1l-2 2 1 1h-1c0 1-1 2-2 4h1c-2 3-4 6-5 9-1 1-2 3-3 4h-1v-1c2-7 6-14 11-20h0z"></path><path d="M167 344h0c2 2 4 3 5 5v1h0c2 2 4 4 5 6h1 0v-1h0v-1l-1-1v-1c1 1 2 3 2 4l-1 1h0c-2 0-2-2-3-3h-2c-1 0-3-3-3-3l-1 1-2-2-1 1-1-1v-3l-5-2h6l1-1z" class="M"></path><path d="M165 347c2 1 3 2 5 4l-1 1-2-2-1 1-1-1v-3z" class="K"></path><path d="M276 357c1 1 2 3 3 4l1 2 1 1 1 2 8 10-1 1c-3-1-7-6-9-9v1c-3-3-2-5-3-9h0c-1-1-1-2-1-3z" class="B"></path><path d="M214 324c-1-3-3-6-4-9v-2-1 1c0 1 1 2 1 3h1v-1h0s1 1 1 2v1c1 0 1-1 2-1h1-1v-1c0-1-1-2 0-3v1l7 16c1 1 2 3 2 4 1 1 1 1 1 2-4-4-5-10-9-13h-1v1h-1z" class="E"></path><path d="M304 492l1-1h0c0-2 1-4 1-6 0 3 0 4 2 6v1c3 2 5 4 6 7l1 1c0 1 1 3 0 4-1 0-2-1-2-2-2-1-3-3-5-5-1-1-3-3-4-5z" class="D"></path><path d="M314 499v2 1 1c-1-2-2-3-3-5l-4-5 1-1c3 2 5 4 6 7z" class="C"></path><path d="M289 290c0-1 0-2 1-2s1 0 2 1v-2s1 0 1-1c0 1 1 1 2 2v1h1c0-1-1-1-1-2s-1-2-1-2v-1c1 1 2 3 3 3v1h0l1 2 2 2 2 1c0 2 0 3 1 4v3h-1v-1c0-2-1-4-2-5l-1-1-1-1c-2-1-2-1-3-3l-1 1c0 1 1 2 1 3h-1l-2-2-1 1c0 1 1 2 2 3v1c1 1 2 2 2 4-2-2-4-4-6-7v-1-2z" class="Z"></path><path d="M294 290c-1 0-1-1-1-1v-1c1 0 1 1 2 1l-1 1z" class="N"></path><defs><linearGradient id="Dk" x1="277.868" y1="408.216" x2="266.344" y2="413.243" xlink:href="#B"><stop offset="0" stop-color="#303030"></stop><stop offset="1" stop-color="#605e60"></stop></linearGradient></defs><path fill="url(#Dk)" d="M275 407l5 1c0 1-4 3-6 4-3 2-7 3-10 2-1 0-2-1-3-1 0-1 0-1 1-2l3 2 1-1c-1-1-1-2-2-2h2c2 0 4 0 7-2h3l-1-1z"></path><path d="M262 411l3 2h3v1h-4c-1 0-2-1-3-1 0-1 0-1 1-2z" class="L"></path><path d="M280 369v-1c2 3 6 8 9 9l1-1 1 1v1l4-2v1c0-1 1-1 1-1 0 1 0 2-1 3h0-1l-2 2c-1 0-2 0-2-1h-1l-1-1h-1-1c-1 1-2 1-3 0l-2-3h0v-1h2l-3-6z" class="Z"></path><path d="M281 376v-1h2c1 2 2 3 4 4h-1c-1 1-2 1-3 0l-2-3h0z" class="H"></path><path d="M302 480l1-4c0-2 1-3 2-5v7l1 1v1c1 0 2 1 3 2h-1c-1 1-1 2-2 3 0 2-1 4-1 6h0l-1 1h0v-1c-3-3-1-7-2-11h0z" class="h"></path><path d="M306 480v1l-2 2v-1c-1-2-1-3-1-5 1 0 1 0 2 1l1 1v1z" class="o"></path><path d="M174 327h1 0v3c0 2 0 3 1 4-1 0-1 1-1 2l-11-5h0v-1c1-2 2-2 4-3h2 4z" class="P"></path><path d="M168 327h2c-2 1-4 3-6 3v1-1c1-2 2-2 4-3z" class="a"></path><path d="M206 314c1 0 1 0 2 2 1 4 3 8 5 13 0 2 2 4 3 7 0 1 1 2 2 4h0l1 2h-1c-2-1-3-4-4-6-3-5-7-10-8-16 1-1 0-4 0-6z" class="B"></path><path d="M211 348c2 2 4 3 6 4 1 1 2 2 3 2 1 1 1 2 1 2 1 0 2 1 3 1v1h1l1 1c2 0 3 1 4 2l2 1-8-7c0-1 0-1-1-2l7 6v1c6 5 14 5 16 14v1c-3-7-14-12-20-15l-14-8 1-1 4 3c-2-2-4-3-5-5l-1-1z" class="K"></path><defs><linearGradient id="Dl" x1="196.073" y1="316.906" x2="195.599" y2="302.233" xlink:href="#B"><stop offset="0" stop-color="#0b0b0c"></stop><stop offset="1" stop-color="#2e2d2d"></stop></linearGradient></defs><path fill="url(#Dl)" d="M200 292v9c0 2 0 4-1 5 0 2-1 4-1 6h0c-1 3 0 5-1 7l-4-5h-1v-1c1 0 1 0 2-1l1-2v-1-6c0-1 0-3 1-4l1 1v1h1c2-2 2-6 2-9z"></path><path d="M200 292v9c0 2 0 4-1 5h-1v-1c0-1-1-3-1-4-1 3-1 5-2 8v-6c0-1 0-3 1-4l1 1v1h1c2-2 2-6 2-9z" class="E"></path><defs><linearGradient id="Dm" x1="256.642" y1="398.484" x2="262.266" y2="412.855" xlink:href="#B"><stop offset="0" stop-color="#393a3b"></stop><stop offset="1" stop-color="#565555"></stop></linearGradient></defs><path fill="url(#Dm)" d="M252 395s0 1 1 1v4h1v-1h0v-4h1c0 1 0 1 1 1l-1 1c0 4 3 9 6 12 1 0 2 1 3 1s1 1 2 2l-1 1-3-2c-1 1-1 1-1 2-3-2-6-4-8-7-1-1-1-3-1-4s0-1 1-2l-2-5h1z"></path><path d="M253 406c-1-1-1-3-1-4s0-1 1-2c2 5 4 8 9 11-1 1-1 1-1 2-3-2-6-4-8-7z" class="H"></path><defs><linearGradient id="Dn" x1="275.842" y1="418.802" x2="277.82" y2="426.62" xlink:href="#B"><stop offset="0" stop-color="#81817f"></stop><stop offset="1" stop-color="#a7a5a7"></stop></linearGradient></defs><path fill="url(#Dn)" d="M291 421c-2 2-3 3-5 4-4 2-10 2-14 1-6-2-14-6-17-13l4 4c5 4 14 7 21 7 3 0 6-1 8-3l2-1 1 1z"></path><defs><linearGradient id="Do" x1="245.396" y1="402.356" x2="239.246" y2="402.953" xlink:href="#B"><stop offset="0" stop-color="#434242"></stop><stop offset="1" stop-color="#6b6a6b"></stop></linearGradient></defs><path fill="url(#Do)" d="M244 390h1s1 0 1 1l1-2v2l1 10c0-1-1-2-1-3h-1v3c-1 3-1 6-3 8h0c0-1 0 0-1-1-1 0-1 1-1 1-1 0-1 0-2-1 1-1 2-2 2-3s0-1 1-2h0l-1-1c0-1 2-4 2-5l-1-1h0c0-1 1-4 2-6z"></path><path d="M244 390h1s1 0 1 1l1-2v2l1 10c0-1-1-2-1-3h-1v3-9s0 1-1 2c0 1 0 3-2 4 0-2 2-6 1-8z" class="e"></path><path d="M220 375h1c1 1 1 3 1 5v2c0 1-1 3-2 4l-1 1h-1l-1 1h0-1-3c-1-1-2-1-3 0s-1 3-1 5v-7l1-1h1-1v-1h0 2l-2-1c3 0 5 0 7-2l1-1 1-1 1-3v-1z" class="J"></path><path d="M220 376c1 2 1 3 0 5 0 1-1 2-1 2v1c0 1 1 1 1 2l-1 1h-1v-1l-1-1v-1c1-1 1-1 1-2 1 0 1-1 1-1 1-1 1-1 1-2-1 0-1 0-2 1l1-1 1-3z" class="k"></path><path d="M220 375h1c1 1 1 3 1 5v2c0 1-1 3-2 4 0-1-1-1-1-2v-1s1-1 1-2c1-2 1-3 0-5v-1z" class="E"></path><path d="M230 380v-1h-1l-1-3c-1-3-3-6-3-9l2 2h0c1 0 2 1 3 1l1 1 1 2c1 0 1 1 1 1 1 1 1 0 1 1l1 1v-1h1v1c0 1 1 2 2 3l2 3h0c0 1 0 1 1 1 0 1 0 1 1 2v1h1c0-1 0-2-1-3v-1-1h0c-1-2-2-3-2-4 2 3 4 7 3 10 0 1-1 1-1 2v-1c-2-1-3-3-4-5l-4-6v-1l-1 1v7c-1-2-1-4-2-5l-1 1z" class="S"></path><path d="M231 379c0-3-1-5-1-7 2 1 3 3 4 4l-1 1v7c-1-2-1-4-2-5z" class="c"></path><path d="M234 377c3 0 4 3 5 5s3 4 3 6c-2-1-3-3-4-5l-4-6z" class="C"></path><path d="M211 248c1 2 1 3 1 5v-1l1-1c0 1 0 2 1 3-2 5-5 10-8 15-1 1-1 3-2 4h0l-1 2c-1-2-1-2-1-4h0-2l-1-1 3-3h0v-3-2c1 0 1-1 1-2s0-3 1-4v8h1 0c1 0 1-1 2 0 0-1 0-3 1-4 2-2 2-4 3-6s0-4 0-6z" class="R"></path><defs><linearGradient id="Dp" x1="205.14" y1="259.935" x2="201.958" y2="264.641" xlink:href="#B"><stop offset="0" stop-color="#8c888d"></stop><stop offset="1" stop-color="#9fa09f"></stop></linearGradient></defs><path fill="url(#Dp)" d="M204 256v8h1 0c1 0 1-1 2 0-2 2-3 4-3 7v2h0l-1 2c-1-2-1-2-1-4h0-2l-1-1 3-3h0v-3-2c1 0 1-1 1-2s0-3 1-4z"></path><path d="M202 267h2c0 2-1 3-2 4h0-2l-1-1 3-3h0z" class="p"></path><path d="M294 290l1-1c1 2 1 2 3 3l1 1 1 1c1 1 2 3 2 5v1h1v3c0 2 1 4 2 6h-1c-3-2-4-6-7-8-1-2-2-4-4-5v-1c-1-1-2-2-2-3l1-1 2 2h1c0-1-1-2-1-3z" class="O"></path><path d="M296 292l1 1h2l1 1v3c-1-2-4-3-4-5z" class="B"></path><path d="M300 294c1 1 2 3 2 5v1c-1-1-2-2-2-3v-3z" class="Y"></path><path d="M294 290l1-1c1 2 1 2 3 3l1 1h-2l-1-1h-1 1v1c0 1 1 3 1 4 0-1-3-3-3-4h1c0-1-1-2-1-3z" class="V"></path><path d="M320 411c0-1 1-1 2-1 0 0 1 2 1 3 2 6 6 11 8 17 1 1 1 3 1 4v2h0 0l-1-1c-4-4-6-9-9-15-1 1-1 1-1 2 0-1 0-1-1-1l1-3c-1-2-2-4-2-7h1z" class="D"></path><path d="M320 411h1c0 2 1 4 1 6l-1 1c-1-2-2-4-2-7h1z" class="C"></path><path d="M215 324v-1h1c4 3 5 9 9 13 2 3 6 6 9 9l1 2h-1c-1-1-6 0-7-1h2 0l-5-6c-4-5-6-10-9-16z" class="G"></path><defs><linearGradient id="Dq" x1="266.108" y1="413.766" x2="281.636" y2="426.359" xlink:href="#B"><stop offset="0" stop-color="#313233"></stop><stop offset="1" stop-color="#4c4a4a"></stop></linearGradient></defs><path fill="url(#Dq)" d="M275 416l2-1c-1 1-2 2-2 3 1 1 3 2 4 3 2 1 4 0 6 0h0 2 1c-2 2-5 3-8 3-7 0-16-3-21-7 1-1 2 0 3 0 3 1 6 1 8 1s3-1 5-2z"></path><path d="M275 416l2-1c-1 1-2 2-2 3 1 1 3 2 4 3 2 1 4 0 6 0h0c-3 2-10 0-13-1-1 0-1 0-2-2 2 0 3-1 5-2z" class="B"></path><path d="M299 439c-2-2-4-4-5-7-1-1-3-5-3-7 0 0 2-2 2-3 1-1 2-3 2-5 2 1 3 3 4 5-1 6-1 10 1 16v1h-1z" class="D"></path><path d="M299 480h1c1 0 1 1 1 2 1 0 0 1 0 2h0-1-2c-1 0-3 0-3 1l-1 1c2 2 3 3 4 5-1-1-3-3-6-2h-1v3l-1-2-1-1v-1l-2-2c0-1-1-2-2-3l-1-1c0-1 3-1 3-2 3 1 5 1 7 1h4l1-1z" class="Y"></path><path d="M291 486l2-1v1c0 1-1 1-2 1h-1l1-1z" class="O"></path><path d="M287 486c2 0 3-1 4 0l-1 1h-1v1l-2-2z" class="E"></path><path d="M299 480h1c1 0 1 1 1 2 1 0 0 1 0 2h0-4v-1c0-1 1-2 2-3z" class="b"></path><path d="M314 354c2 1 3 2 4 4 1 1 1 0 2 0l1 2c1 1 1 1 2 1h3l1-1 2 4c1 0 2 2 2 3v2c-1 1-1 1-2 1h1c-1 0-2-1-3-1v1c1 0 1 1 2 1h0c-2 0-3-2-5-2l-1-1c-1 0-2-2-3-2-3-4-5-8-6-12z" class="Z"></path><path d="M318 358c1 1 1 0 2 0l1 2c1 1 1 1 2 1h3l1-1 2 4c-2 1-5 0-8 0l-3-6z" class="I"></path><path d="M253 205c1 1 2 2 4 2h0l-1 1c1 1 1 1 2 1h1c2 1 5 2 7 3 1 0 2 0 2 1l3 1 2 1h-6-3 0c-3 1-5 1-8 2-1 1-3 2-4 2s-1 0-1 1v1h-3-1c1-1 3-2 4-2 0-1 0-1 1-2s4-2 6-3v-1h-3c-1 0-3 1-4 1l1-2h0c2-1 1-2 3-3h1v-1h-4l1-3z" class="C"></path><path d="M259 209c2 1 5 2 7 3 1 0 2 0 2 1h-6c-1 0-3 1-4 1v-1h-3c2-1 4 0 6-1-1-2-1-2-2-3z" class="D"></path><path d="M253 205c1 1 2 2 4 2h0l-1 1c1 1 1 1 2 1h1c1 1 1 1 2 3-2 1-4 0-6 1-1 0-3 1-4 1l1-2h0c2-1 1-2 3-3h1v-1h-4l1-3z" class="T"></path><path d="M240 293h1v2c1 1 1 2 1 3h0v3c1 1 0 1 1 2 0 2 0 4 1 6 1 4 2 9 4 13v1l-1-1h-1c-1-2-3-3-4-5-2-3-3-9-4-13 0-4 0-8 2-11z" class="e"></path><defs><linearGradient id="Dr" x1="228.326" y1="416.303" x2="230.038" y2="419.134" xlink:href="#B"><stop offset="0" stop-color="#7b7a7a"></stop><stop offset="1" stop-color="#9e9d9f"></stop></linearGradient></defs><path fill="url(#Dr)" d="M241 402l1 1h0c-1 1-1 1-1 2s-1 2-2 3c1 1 1 1 2 1 0 0 0-1 1-1 1 1 1 0 1 1-4 5-11 9-16 13-1 0-3 1-3 2-2 0-3 0-4 1l-2 1c0 1-1 1-1 1h0v-1l2-2 5-3c-1-1-5-1-7-1 2 0 3 0 4-1h1 0c2 1 7-3 9-4l1-1c2-1 3-3 4-4s1-2 2-2l3-6z"></path><path d="M231 415l1-1c2-1 3-3 4-4s1-2 2-2h1l-1 1c0 1-1 1-1 2-3 3-5 5-8 7-2 1-3 2-5 3-1-1-5-1-7-1 2 0 3 0 4-1h1 0c2 1 7-3 9-4z" class="O"></path><path d="M264 410c-1 0-2-1-3-1-3-3-6-8-6-12l1-1c1 2 2 2 5 2 1 2 3 5 6 6l6 3h2l1 1h-3c-3 2-5 2-7 2h-2z" class="b"></path><path d="M273 407h0c-1 1-1 1-2 1-6-1-9-3-12-7 1 0 2 1 4 2 1 1 2 1 4 1l6 3z" class="f"></path><path d="M216 371c-3 1-4 4-7 3v-1s1-1 2-1v-1c1 0 1 0 1-1l1-1c0-1 1-1 1-2s1-1 1-1v-1h0 0c2-1 4-2 6-2v3c2 5 4 9 3 14l-1 3s0-1-1-1v-2c0-2 0-4-1-5h-1v1l-1 3-1-1v-3l-1-1-1-3z" class="E"></path><path d="M216 371s1-1 2-1c2 0 3 1 4 2 2 2 2 4 2 6v2l-1 3s0-1-1-1v-2c0-2 0-4-1-5h-1v1l-1 3-1-1v-3l-1-1-1-3z" class="L"></path><path d="M217 374c1-1 2-1 3 0h0v1 1l-1 3-1-1v-3l-1-1z" class="d"></path><path d="M321 489h0c1 0 2 0 3-1h0c0-3-14-20-17-23l3-3c1 1 3 4 4 5h0c1 1 2 3 3 5 4 5 8 10 11 15 0 1 1 2 1 3h-1l-1 1h-1c0-1-1-1-2-1h0v-1h-3z" class="R"></path><path d="M261 340c1 1 4 8 5 8h0l5 8 3 3v-1 1c1 0 1 1 2 1h1c1 4 0 6 3 9l3 6h-2v1h0-2c-3-2-6-8-7-11l-3-6c0-2-2-5-3-8h0c-1-1-1-2-1-2v-1h0l-1-1v-1c-1-1-1-2-2-3h0v-1c-1 0-1-1-1-2z" class="T"></path><path d="M266 348h0l5 8 3 3c0 1 0 2-1 4-3-5-5-9-7-15z" class="L"></path><path d="M274 359v-1 1c1 0 1 1 2 1h1c1 4 0 6 3 9l3 6h-2v1c-2-2-3-5-5-8-1-2-2-4-3-5 1-2 1-3 1-4z" class="G"></path><path d="M247 391c1 0 1 0 2 1h0c1 1 1 1 2 1v2l2 5c-1 1-1 1-1 2s0 3 1 4h-1c1 4 1 8 1 12-1 3 0 7-2 9-3 3-6 5-9 7l-2-1v-1c2 0 4-1 5-3 2-1 4-2 5-4 1-3 1-10 0-13 0-4-1-8-2-11l-1-10z" class="o"></path><path d="M249 392h0c1 1 1 1 2 1v2l2 5c-1 1-1 1-1 2s0 3 1 4h-1c-1-2-1-4-2-6 0-2-1-4-1-7h0v-1z" class="F"></path><path d="M254 337c1 1 2 2 3 2h1c4 5 6 12 8 18 4 9 5 17 14 22v1l-5-2h0l-2-2c-2-3-5-6-7-9v-2c-2-4-3-9-5-14-1-3-2-5-4-7v-1-1c0-1 0-1-1-1v-1h0 0c0-1-1-2-2-3z" class="c"></path><defs><linearGradient id="Ds" x1="272.364" y1="339.708" x2="264.031" y2="351.239" xlink:href="#B"><stop offset="0" stop-color="#3a3b3b"></stop><stop offset="1" stop-color="#6b6a6b"></stop></linearGradient></defs><path fill="url(#Ds)" d="M261 339l-3-6h0c0-2-1-3 0-5v1 1 1 1h0 2v-1c1 1 0 1 1 1h0 1v-1h0v-1c-1-1 0-2-1-3v-1-3 3c1 0 1 0 1 1v1l3 9 8 15c1 1 2 3 3 5 0 1 0 2 1 3h0-1c-1 0-1-1-2-1v-1 1l-3-3-5-8h0c-1 0-4-7-5-8v-1z"></path><path d="M261 339h1c2 1 3 4 4 6v3h0 0c-1 0-4-7-5-8v-1z" class="O"></path><path d="M257 370c2 0 2 1 3 2s4 5 6 6l1-1h3l2 2h2 0 1v-1h0l5 2v-1h3c1 1 2 1 3 0h1 1l1 1h1c0 1 1 1 2 1-2 2-4 3-7 3h1c1 1 2 1 3 0v1h-1v1c-2 1-4 1-7 0-8-1-19-9-24-16z" class="m"></path><path d="M275 379l2 1v2c-2-1-3-2-5-3h2 0 1z" class="G"></path><defs><linearGradient id="Dt" x1="289.905" y1="382.917" x2="277.145" y2="379.418" xlink:href="#B"><stop offset="0" stop-color="#424141"></stop><stop offset="1" stop-color="#5a595a"></stop></linearGradient></defs><path fill="url(#Dt)" d="M275 378h0l5 2v-1h3c1 1 2 1 3 0h1 1l1 1h1c0 1 1 1 2 1-2 2-4 3-7 3-2 0-4 0-6-1l-2-1v-2l-2-1v-1z"></path><path d="M277 380c2 1 2 1 2 3l-2-1v-2zm6-1c1 1 2 1 3 0h1 1c0 2-1 1-2 2-2 0-4 0-6-1h0v-1h3z" class="O"></path><path d="M321 489h3v1c-1 0-1 1-1 2l-1 1v10c0 2 1 4 0 6h0c-1 1-1 2-1 2-1 2-1 2-2 3-2 3-3 4-6 4l-1 1c-1 0-2 0-3-1l1-1c1 0 2-1 2-1v-1c2-1 3-2 4-4 2-4 2-8 2-13v-1c0-3 0-5 2-7l1-1z" class="H"></path><path d="M321 489h3v1c-1 0-1 1-1 2-1 0-2 1-2 2-1 2 0 5-2 6l-1-2v-1c0-3 0-5 2-7l1-1z" class="i"></path><path d="M322 493v10c0 2 1 4 0 6h0c-1 1-1 2-1 2-1 2-1 2-2 3-2 3-3 4-6 4 5-4 8-13 9-19v-6h0z" class="B"></path><defs><linearGradient id="Du" x1="217.953" y1="399.196" x2="230.878" y2="400.282" xlink:href="#B"><stop offset="0" stop-color="#202120"></stop><stop offset="1" stop-color="#3b3a3b"></stop></linearGradient></defs><path fill="url(#Du)" d="M231 379c1 1 1 3 2 5 0 5 0 10-2 14v1c-2 6-7 14-13 17v1h-2c0-4 5-8 7-11 5-9 6-17 7-26l1-1z"></path><path d="M208 276c1 3 1 7 1 10v1l2 7 1 2c3 7 7 12 11 17h0c-1 0-2-1-3-2 1 1 2 3 2 4 3 4 6 9 9 13l3 3v1c-1 0-1 1-1 1v3l-17-25c-4-7-8-15-8-24 0-1-1-2 0-4s0-5 0-7z" class="N"></path><path d="M212 296c3 7 7 12 11 17h0c-1 0-2-1-3-2 1 1 2 3 2 4h0c-4-6-7-10-10-16v-3z" class="j"></path><defs><linearGradient id="Dv" x1="339.777" y1="338.512" x2="326.367" y2="347.341" xlink:href="#B"><stop offset="0" stop-color="#0b0a0a"></stop><stop offset="1" stop-color="#2a2a2a"></stop></linearGradient></defs><path fill="url(#Dv)" d="M335 334c1 0 2-1 3 0v15 2c-1 1-3 1-3 1-1-1-2-1-3-1 1-1 1-1 1-2h0l-2-1h-2 0l-1-2v2l-2 1c-1 1-1 2-1 4v-6s0-1 1-2c1-2 2-4 3-5l4-4 2-2z"></path><path d="M330 344c0-1 1-2 2-3 1 2 3 4 4 6l2 2v2c-1 1-3 1-3 1-1-1-2-1-3-1 1-1 1-1 1-2h0l-2-1h-2 0l-1-2 2-2z" class="N"></path><path d="M330 344c1 0 2 2 3 3v2l-2-1h-2 0l-1-2 2-2z" class="K"></path><path d="M209 393c0-2 0-4 1-5s2-1 3 0h3 1v1c1 2 0 4-1 6l-3 14c-1 1 0 2 0 3l-2 4s0-1-1-1h-1-2 0c1-3 2-5 2-7v-13-2z" class="B"></path><path d="M209 395v9c1 3 2 8 1 11h-1-2 0c1-3 2-5 2-7v-13z" class="k"></path><path d="M293 296c2 1 3 3 4 5 3 2 4 6 7 8h1 0c1 2 1 4 3 5v-1c1 1 1 3 1 4h0c-1 2-1 4-2 6l2 7v2 4c1 1 1 3 1 4-1-2-1-5-2-7s-3-5-4-7l-1-1c-1-5-3-11-4-16 0-2-1-4-2-6l-1-1-1-2c0-2-1-3-2-4z" class="Y"></path><path d="M309 330h-1v1-1c-1-1-2-2-3-4-2-5-4-11-4-17 2 3 4 7 5 11l1 3 2 7z" class="g"></path><path d="M230 359h1v-1l1-1h2c3 1 5 4 7 5 1 1 2 3 3 4l-1 1s1 0 1 1l1 1c0 1 1 1 1 2h0c1 1 1 1 1 2h0v2h1v1 5l-1 1v3h0c1 0 1 0 1-1v-1c1-2 0-7 0-9h-1v-2l-1-2c0-1 0-2-1-2v-1l-1-1h0l-1-1c-1-1 0 0 0-1l-1-1v-1c-1 0-1 0-1-1-1-2-2-4-4-6-1-1-3-4-3-6h1c2 2 10 13 10 14 1 2 3 5 4 8 2 4 1 10 1 15l-2 1-1 1h0c-1-2 0-5-1-7v-6-1c-2-9-10-9-16-14v-1z" class="D"></path><path d="M248 387h-1v-1c1 0 2-1 2-2 1-6-1-14-5-20l1-1c1 2 3 5 4 8 2 4 1 10 1 15l-2 1z" class="h"></path><defs><linearGradient id="Dw" x1="208.097" y1="438.034" x2="180.009" y2="456" xlink:href="#B"><stop offset="0" stop-color="#696667"></stop><stop offset="1" stop-color="#b7bcbc"></stop></linearGradient></defs><path fill="url(#Dw)" d="M206 431v-1l1-2v4h1c-1 2-2 3-2 5-1 2-2 4-2 6l-1 2c0 1 0 2-2 3l-1 1-3 5c-1 0-2 0-2 1h-1l-5 5-5 3-2 1c1 0 1-1 2-1v-1c-1 0-1 1-2 1h-1c12-9 18-21 24-34 1 0 1 1 1 2z"></path><path d="M206 431v-1l1-2v4h1c-1 2-2 3-2 5-1 2-2 4-2 6 0-2 0-2-1-3 1-2 2-5 2-7 0-1 0-1 1-2h0z" class="k"></path><path d="M203 440c1 1 1 1 1 3l-1 2c0 1 0 2-2 3l-1 1-3 5c-1 0-2 0-2 1h-1l9-15z" class="g"></path><path d="M303 296c0-1-1-2-2-4v-1h0l-1-1c0-1 1 0 0-1s-1-2-2-3h1 0l1-1c0 1 1 1 1 2l1-1h0c1 0 3-1 4-2h-1 0c-1-1 0-1 0-1l-1-1c3 0 7-1 10 0h0 2 0 2 0c1-1 1 0 2-1h1l1-1c1 0 1 0 2 1 1 0 2-1 3-1l1 1h3 1l1-1h0c2 1 2 3 3 4v1h0 0c-1 0-1-1-1-1v-1c-1 1-2 1-2 2-1 1-1 1-2 1l-1-1-2-2c0 1-1 1-1 2h-1c0-1-1-1-2-1s-2 1-4 1l-1 2c-1 0-1-1-1-1h-2l-1-1h-2c-2 1-4 1-5 3 0 1 0 2 1 3h-1l-1-1h-1c1 2 2 4 3 7h-1c0-1-1-5-2-5-1-1-4-2-5-2l3 6h-1z" class="E"></path><path d="M193 270c1 0 2 1 2 2l1 1h0c1 2 2 3 2 4l1 1h0c1 5 0 9 1 14 0 3 0 7-2 9h-1v-1l-1-1h0l-2-3-1-2c-3-4-1-6-2-11h0c0-4 1-9 2-13z" class="O"></path><defs><linearGradient id="Dx" x1="196.592" y1="292.884" x2="192.401" y2="290.869" xlink:href="#B"><stop offset="0" stop-color="#949194"></stop><stop offset="1" stop-color="#b1b0b0"></stop></linearGradient></defs><path fill="url(#Dx)" d="M193 288c1-1 2-1 3-1v4l1 7-1 1-2-3-1-2v-6z"></path><path d="M199 278c1 5 0 9 1 14 0 3 0 7-2 9h-1v-1c1 0 1-1 1-2 1-3 1-8-1-11h0c0-2 0-6 1-7 0-1 1-2 1-2z" class="V"></path><path d="M194 284v-12h1l1 1h0c0 2 0 2 1 3 0 3-1 7-1 10v5-4c-1 0-2 0-3 1l1-4z" class="F"></path><path d="M194 284h1c1 1 1 2 1 3-1 0-2 0-3 1l1-4z" class="X"></path><path d="M193 270c1 0 2 1 2 2h-1v12l-1 4v6c-3-4-1-6-2-11h0c0-4 1-9 2-13z" class="c"></path><path d="M304 296l-3-6c1 0 4 1 5 2 1 0 2 4 2 5h1c-1-3-2-5-3-7h1l1 1h1c-1-1-1-2-1-3 1-2 3-2 5-3h2l1 1h2s0 1 1 1l1-2c2 0 3-1 4-1s2 0 2 1h1c0-1 1-1 1-2l2 2v1h-2v1 1h-1v-1l-1-1h-2-1c-1 1-2 1-2 2s0 1-1 2l1 2h-1-1l-2-2h-1c0 1-1 0-1 1-1 1 0 3 0 4l1 1c-1 1-1 1-2 1l1 4h0c-1-2-1-2-1-4l-1 1v4h0v2h0l-1 1c0-1-1-1-1-1-2-1-2-3-3-4-1 0-2 1-2 1l-2-5h0z" class="h"></path><path d="M311 294h0c-1-1-1-2-1-4l1-1h1l1-1c1 0 1-1 2-1h1v1c-1 0-2 1-2 2v2h0c-2 0-1-1-2-2-1 2-1 3-1 4zm-7 2l1-1v1c1 0 1 1 2 1 2 2 2 4 5 6l-1-4v-3h1v5c0 1 0 2 1 3h0l-1 1c0-1-1-1-1-1-2-1-2-3-3-4-1 0-2 1-2 1l-2-5z" class="F"></path><path d="M311 294c0-1 0-2 1-4 1 1 0 2 2 2h0v-2c0-1 1-2 2-2l1 1c1 0 1 0 2-1 0 1 1 1 1 2l1 2h-1-1l-2-2h-1c0 1-1 0-1 1-1 1 0 3 0 4l1 1c-1 1-1 1-2 1l1 4h0c-1-2-1-2-1-4l-1 1v4h0v2c-1-1-1-2-1-3v-5h-1v-2z" class="H"></path><path d="M312 296v-3c1 0 1 1 2 2h1l1 1c-1 1-1 1-2 1l1 4h0c-1-2-1-2-1-4l-1 1v4h0v2c-1-1-1-2-1-3v-5z" class="P"></path><path d="M315 295l1 1c-1 1-1 1-2 1v-2h1z" class="X"></path><path d="M291 492v-3h1c3-1 5 1 6 2l3 3 11 13c1 2 2 4 4 4-1 2-2 3-4 4v1-2c-1-1-1-1-3-1h0v-1h-2l-1 1-1-2v-1c-2-4-4-7-7-10-1 0-2 0-2-1-1 0-1-2-2-3s-2-2-3-4z" class="F"></path><path d="M293 490c2 0 3 1 4 2s2 2 2 4l-3 3h0l-1-1c-1-2 1-2 1-4l-3-3v-1z" class="r"></path><path d="M301 494l11 13-1 1-2-2-1 1h0c0 1-1 1-1 2-1 0-1 1-1 2h-1v-1h1v-4c0-1-1-2-2-3-2-3-3-5-3-9z" class="G"></path><path d="M305 511h1c0-1 0-2 1-2 0-1 1-1 1-2h0l1-1 2 2 1-1c1 2 2 4 4 4-1 2-2 3-4 4v1-2c-1-1-1-1-3-1h0v-1h-2l-1 1-1-2z" class="T"></path><path d="M309 513h1c1 0 1-1 2-1v1 2 1-2c-1-1-1-1-3-1z" class="M"></path><defs><linearGradient id="Dy" x1="334.521" y1="437.253" x2="299.176" y2="437.755" xlink:href="#B"><stop offset="0" stop-color="#1b1a1b"></stop><stop offset="1" stop-color="#555455"></stop></linearGradient></defs><path fill="url(#Dy)" d="M305 426c0-5-6-8-7-13h2l26 31 6 7c0 1 0 2 1 3l1 1h0c0 1 1 1 1 2v1c1 1 0 3 0 4l-30-36z"></path><path d="M265 337c1-2 2-5 2-6s-1-1-2-2h0c0-1 0-2 1-2s2 1 3 1c3 3 10 2 10 7s-3 11-5 15c0 1-1 1-1 2l-8-15z" class="g"></path><path d="M233 384v-7l1-1v1l4 6c1 2 2 4 4 5v1c-2 1-3 1-4 2v1c-1 4-2 8-4 12s-7 8-11 11v1l-1 1h-1-3v-1c6-3 11-11 13-17v-1c2-4 2-9 2-14z" class="L"></path><defs><linearGradient id="Dz" x1="245.849" y1="235.09" x2="257.455" y2="224.609" xlink:href="#B"><stop offset="0" stop-color="#3b3b3c"></stop><stop offset="1" stop-color="#6a6868"></stop></linearGradient></defs><path fill="url(#Dz)" d="M250 223h3c2 0 7 2 8 1 1 0 1 0 2 1s2 1 4 2c1 1 2 1 3 2 1 0 1 1 2 1l1 1 2 1-1 1c-1 0-2-2-4-1 0 1 0 1 1 2s3 2 4 2v1h-2v1c-1 0-2 0-2-1-3-1-7-3-10-4-2 0-5-1-7-1s-3 1-5 1h0l-2-2h-1c-1 0-1-1-2-1h0c-3 0-6 1-8 0 2-2 4-2 7-2h1c2 0 6 0 8-1v-1h0l-3-2 1-1z"></path><path d="M252 226c2 0 4 0 5 1-1 1-3 1-4 1h-2-8 1c2 0 6 0 8-1v-1z" class="E"></path><defs><linearGradient id="EA" x1="261.877" y1="234.671" x2="271.549" y2="234.227" xlink:href="#B"><stop offset="0" stop-color="#787476"></stop><stop offset="1" stop-color="#90908e"></stop></linearGradient></defs><path fill="url(#EA)" d="M261 233c0-1 0-1 1-2 4 1 7 4 11 6v1c-1 0-2 0-2-1-3-1-7-3-10-4z"></path><defs><linearGradient id="EB" x1="257.425" y1="222.921" x2="257.701" y2="229.745" xlink:href="#B"><stop offset="0" stop-color="#5c5b5c"></stop><stop offset="1" stop-color="#848383"></stop></linearGradient></defs><path fill="url(#EB)" d="M250 223h3c2 0 7 2 8 1 1 0 1 0 2 1s2 1 4 2c1 1 2 1 3 2 1 0 1 1 2 1l1 1 2 1-1 1c-1 0-2-2-4-1 0 1 0 1 1 2-1-1-3-1-5-2-1-2-3-2-5-3-1 0-2-1-4-2-1-1-3-1-5-1h0l-3-2 1-1z"></path><path d="M266 232v-1c-1-1-1-1-1-2 1-2 5 2 6 2h2l2 1-1 1c-1 0-2-2-4-1 0 1 0 1 1 2-1-1-3-1-5-2z" class="L"></path><path d="M264 215h0 3 4l1 2h0c5 2 7 4 10 8h-2c1 1 1 1 1 3h0-2-1l-3-1h-1v2h-2v1c-1 0-1-1-2-1-1-1-2-1-3-2-2-1-3-1-4-2s-1-1-2-1c-3-2-6-3-10-3v-1c0-1 0-1 1-1s3-1 4-2c3-1 5-1 8-2z" class="h"></path><path d="M264 215h0 3 4l1 2c-1 0-2 0-3-1-2-1-3 0-5 0v-1z" class="B"></path><path d="M263 225c1-1 1-1 2 0h2 1 3l3 1h-3 0-3l-1 1c-2-1-3-1-4-2z" class="F"></path><path d="M271 225c0-1-2-1-3-1-2 0-2 0-3-1s-2-2-3-2c1 0 5 2 6 2 0 0 0-1 1-1 0 0 1 1 2 1h1l3 3h-1 0l-3-1z" class="B"></path><path d="M274 226h0l1 1h-1v2h-2v1c-1 0-1-1-2-1-1-1-2-1-3-2l1-1h3 0 3z" class="o"></path><path d="M272 217c5 2 7 4 10 8h-2c1 1 1 1 1 3h0-2-1l-3-1-1-1h1l-3-3v-1l1-1c-2-1-5-2-7-3 2 0 4-1 6-1z" class="I"></path><path d="M279 228l-1-1c-1-1-2-2-2-3s-1-1-1-2l-1-1h1c2 0 2 1 4 2l1 2c1 1 1 1 1 3h0-2z" class="H"></path><defs><linearGradient id="EC" x1="248.703" y1="223.359" x2="205.417" y2="222.407" xlink:href="#B"><stop offset="0" stop-color="#898a8a"></stop><stop offset="1" stop-color="#adaaab"></stop></linearGradient></defs><path fill="url(#EC)" d="M249 207c1 0 2 0 3 1h4v1h-1c-2 1-1 2-3 3h0l-1 2c-6 5-12 8-20 10s-16 3-23 7c-1 0-2 0-2 1h-1c1-1 1-2 2-2 1-2 3-3 5-4 5-2 10-4 15-7 3 0 4 0 7-1h0l15-9v-2h0z"></path><path d="M235 219c1 0 2-1 3-1 3-1 5-3 8-5 3-1 5-4 9-4-2 1-1 2-3 3h0c-4 0-8 4-11 6-4 2-8 3-11 4 1-1 3-2 5-3z" class="R"></path><defs><linearGradient id="ED" x1="252.286" y1="206.807" x2="234.149" y2="220.232" xlink:href="#B"><stop offset="0" stop-color="#716f70"></stop><stop offset="1" stop-color="#878688"></stop></linearGradient></defs><path fill="url(#ED)" d="M249 207c1 0 2 0 3 1h4v1h-1c-4 0-6 3-9 4-3 2-5 4-8 5-1 0-2 1-3 1h-2l1-1h0 0l15-9v-2h0z"></path><path d="M290 442c-4-5-7-9-11-12 1 0 3 1 4 0h2 0c0-1 1-1 2-1 5 6 9 14 14 20 1 2 2 4 3 5 1 0 2 0 3-1-3 4-6 8-9 10v-1l-7-7s-2-1-2-2c0 0 1-1 1-2l-1-1c0-2 0-4-1-6h0c0-2-2-4-3-5h1c1 0 2 1 2 1v1c1 0 1 1 2 1z" class="N"></path><path d="M285 439h1c1 0 2 1 2 1v1c1 0 1 1 2 1h0l12 15c1 0 2-2 2-2v-1c1 0 2 0 3-1-3 4-6 8-9 10v-1l-7-7s-2-1-2-2c0 0 1-1 1-2l-1-1c0-2 0-4-1-6h0c0-2-2-4-3-5z" class="e"></path><defs><linearGradient id="EE" x1="214.092" y1="336.79" x2="176.047" y2="341.86" xlink:href="#B"><stop offset="0" stop-color="#727171"></stop><stop offset="1" stop-color="#a09fa0"></stop></linearGradient></defs><path fill="url(#EE)" d="M183 324l28 24 1 1c1 2 3 3 5 5l-4-3-1 1c-1 0-3-1-3-2l-8-3-26-11c0-1 0-2 1-2-1-1-1-2-1-4v-3h0-1l4-2c2 0 3 0 5-1z"></path><path d="M307 453c1-1 3-4 3-6l-1-1v-1c2 1 4 3 5 5 0 4-7 11-9 14l-9 9c-3 3-6 5-9 7 0 1-3 1-3 2l-1 1-2 1-1-1 4-3v-1c-2 0-5 3-7 4-1 1-3 1-4 3l-4 5v-2c1-3 1-5 1-8h0v-1l-1-1v-1h2c1 1 1 0 2 0h2l6-2h0l4-2c-1-1 0-4 0-5h0c0-1 0-2 1-2h3 2c2-1 4-2 5-3l-1-1h2l1-1v1c3-2 6-6 9-10z" class="O"></path><path d="M281 476h1c-1 2-4 3-5 3v-1h-2l6-2z" class="L"></path><path d="M269 478h2c1 1 1 0 2 0h2 2v1l-7 2v-1l-1-1v-1z" class="a"></path><path d="M298 462v1c-4 4-8 8-13 11-1-1 0-4 0-5h0c0-1 0-2 1-2h3 2c2-1 4-2 5-3l-1-1h2l1-1z" class="J"></path><path d="M309 307l1 5v1c1 3 2 5 3 7 0 2 1 3 1 4l5 12c1 4 1 7 3 10 0 2 0 3 1 4v-3-2-4 1h0v-2h1v1 2 1s1 1 2 1c-1 1-1 2-1 2v6c0 2 1 4 2 7l-1 1h-3c-1 0-1 0-2-1l-1-2c-1 0-1 1-2 0-1-2-2-3-4-4 0-1-1-3-1-4-1-3-2-6-3-10 0-1 0-3-1-4v-4-2l-2-7c1-2 1-4 2-6h0c0-1 0-3-1-4v-1l1-5z" class="W"></path><path d="M320 353c1 2 3 4 4 5-1-3-2-7-3-10 1 1 2 4 3 5 0-2 0-4 1-6h0v6c0 2 1 4 2 7l-1 1h-3c-1 0-1 0-2-1l-1-2-3-5 1-1v1c1-1 1 0 0-1h0c1 0 1 1 2 2v-1z" class="a"></path><path d="M317 353l1-1v1c1-1 1 0 0-1h0c1 0 1 1 2 2s2 3 2 5l-1 1-1-2-3-5z" class="U"></path><path d="M309 307l1 5v1c0 3 1 6 1 8v1l1 1h0c0 2 0 2-1 4l1 1v1 2c1 0 1 0 1 1 0 2 1 3 1 5h-1c0-1-1-2-2-3 0-1-1-2-2-2v-2l-2-7c1-2 1-4 2-6h0c0-1 0-3-1-4v-1l1-5z" class="i"></path><path d="M309 332c1 0 2 1 2 2 1 1 2 2 2 3h1c1 4 2 7 3 11 1 2 3 3 3 5v1c-1-1-1-2-2-2h0c1 1 1 0 0 1v-1l-1 1 3 5c-1 0-1 1-2 0-1-2-2-3-4-4 0-1-1-3-1-4-1-3-2-6-3-10 0-1 0-3-1-4v-4z" class="P"></path><path d="M309 336c4 5 4 12 8 17l3 5c-1 0-1 1-2 0-1-2-2-3-4-4 0-1-1-3-1-4-1-3-2-6-3-10 0-1 0-3-1-4z" class="o"></path><path d="M185 351l1-2v3h1v-1 2h1l4-1c1 1 1 2 2 2h0 1l2 1 1 2v2c1 2 1 4 2 6 2 4 5 9 10 11h4l1-1c0 1 0 1 1 1h1l1-1v3l1 1-1 1-1 1c-2 2-4 2-7 2l-5-2c-3-1-5-2-7-3h0c-3-2-8-5-9-9-3-5-4-11-5-17v-1h1z" class="T"></path><path d="M214 376h1c0 1 0 1-1 2-2 0-3 0-5-1l1-1h4z" class="I"></path><path d="M199 377l9 3h-3v1c-3-1-5-2-7-3l1-1z" class="f"></path><path d="M193 364l-1-4 1-1c1 1 2 4 2 4h0c1 3 2 5 2 7-2-1-3-4-4-6z" class="V"></path><path d="M194 354l2 8-1 1s-1-3-2-4l-1 1 1 4-1-2v-1-1c-1-1-1-1-1-2 1-1 1-2 2-2l1-2h0z" class="Z"></path><path d="M188 353l4-1c1 1 1 2 2 2l-1 2c-1 0-1 1-2 2 0-1 0-2-1-2s-1 1-2 1v-4z" class="J"></path><path d="M218 375v3l1 1-1 1-1 1c-1-1-2-1-3 0h-4l-2-1h3l4-1c1-1 2-2 2-3l1-1z" class="X"></path><path d="M208 380h0l2 1h4c1-1 2-1 3 0-2 2-4 2-7 2l-5-2v-1h3z" class="U"></path><path d="M194 354h1l2 1 1 2v2c1 2 1 4 2 6 2 4 5 9 10 11l-1 1h-1c-7-3-9-9-12-15l-2-8z" class="a"></path><path d="M184 352v-1h1c1 4 1 8 2 11 1 2 2 3 2 4h0c1 0 1 0 1-1-1-1-1-2-1-4h1l3 6 4 7c0 1 2 3 2 3l-1 1h0c-3-2-8-5-9-9-3-5-4-11-5-17z" class="G"></path><defs><linearGradient id="EF" x1="252.962" y1="351.432" x2="246.145" y2="358.511" xlink:href="#B"><stop offset="0" stop-color="#6e696b"></stop><stop offset="1" stop-color="#babcbb"></stop></linearGradient></defs><path fill="url(#EF)" d="M235 334h1 1l-1-1v-1l1-1 1 2c1 0 1 1 2 1v1l1 1c1 0 1 0 2 1h1l5 5c0 1 1 1 2 2 1 2 4 7 7 8 1 1 5 10 6 12l2 3c2 3 5 6 7 9l2 2v1h-1 0-2l-2-2h-3l-1 1c-2-1-5-5-6-6s-1-2-3-2c0 0-1-1-1-2l-4-6-19-26v-3s0-1 1-1l1 2z"></path><path d="M260 367v1h1c0 1 1 2 2 2 0 1 0 1 1 1h0v-1c1 1 2 3 3 3 1 1 2 3 3 4h-3v-1c-1 0-3-1-4-3-1-1-1-2-2-2 0-1 0-1-1-1v-1c0-1-1-1-1-1l1-1z" class="K"></path><path d="M252 356l1-2c4 5 7 11 11 16v1h0c-1 0-1 0-1-1-1 0-2-1-2-2h-1v-1l-3-3-2-3-3-5z" class="d"></path><path d="M235 334h1 1l-1-1v-1l1-1 1 2c1 0 1 1 2 1v1l1 1c0 2 2 3 3 4-1 1-1 2-1 2 0 2 1 4 2 6 1 0 1 1 1 1l-9-12-2-3z" class="C"></path><path d="M235 334h1 1l-1-1v-1l1-1 1 2-1 1c1 1 1 1 1 3h-1l-2-3z" class="M"></path><path d="M240 335l1 1c0 2 2 3 3 4-1 1-1 2-1 2h0c-2-2-3-4-4-6l1-1z" class="b"></path><defs><linearGradient id="EG" x1="262.053" y1="351.512" x2="253.667" y2="366.861" xlink:href="#B"><stop offset="0" stop-color="#787574"></stop><stop offset="1" stop-color="#9a9b9c"></stop></linearGradient></defs><path fill="url(#EG)" d="M241 336c1 0 1 0 2 1h1l5 5c0 1 1 1 2 2 1 2 4 7 7 8 1 1 5 10 6 12l2 3c2 3 5 6 7 9l2 2v1h-1 0-2l-2-2c-1-1-2-3-3-4-1 0-2-2-3-3-4-5-7-11-11-16l-1 2c-2-3-3-5-5-7h-1 0s0-1-1-1c-1-2-2-4-2-6 0 0 0-1 1-2-1-1-3-2-3-4z"></path><path d="M247 349v-1l6 6-1 2c-2-3-3-5-5-7z" class="K"></path><path d="M267 373h1c2 1 3 4 5 4v-1l2 2v1h-1 0-2l-2-2c-1-1-2-3-3-4z" class="F"></path><path d="M241 336c1 0 1 0 2 1h1l5 5c0 1 1 1 2 2h-1c0 2 6 8 5 9l-11-13c-1-1-3-2-3-4z" class="C"></path><path d="M255 353c1-1-5-7-5-9h1c1 2 4 7 7 8 1 1 5 10 6 12-1-1-2-1-3-2-1-2-3-4-4-6l-1-1s-1 0-1-1h0v-1z" class="c"></path><path d="M324 490l2 2 1 1c0 2 0 3 1 4 2 3 3 6 4 9 2 2 3 3 5 3-1 1 0 3-1 3-1 5-2 11-6 15h0l-2 1c-1 1-4 3-6 3l-4 2h-1c-1-1 0-1-1-1s-1 0-1-1l-4-1v-1l1-1-2-1c2-1 4-3 6-4 5-4 5-9 6-14 1-2 0-4 0-6v-10l1-1c0-1 0-2 1-2h0z" class="g"></path><path d="M312 528l3 3-4-1v-1l1-1z" class="J"></path><path d="M325 499l1-1h1c0 2-1 4-1 6v1h-1v-6z" class="E"></path><path d="M326 492l1 1c0 2 0 3 1 4 2 3 3 6 4 9v1l-1-2c-1-1-2-2-4-2l-1 1c0-2 1-4 1-6h-1l-1 1 1-7z" class="D"></path><path d="M322 531c-1-2-3-4-5-6 2-1 3-3 4-4l7 7h0c-1 1-4 3-6 3zm9-26l1 2v-1c2 2 3 3 5 3-1 1 0 3-1 3-1 5-2 11-6 15h0c-2-3-5-5-5-9h0v-1c-1-2 1-4 1-6 1-1 1-3 2-4l2-2h1z" class="S"></path><path d="M332 506c2 2 3 3 5 3-1 1 0 3-1 3h-1l-2 7c-1-2-3-5-3-7-1-2 0-4 2-5v-1z" class="N"></path><path d="M289 450l1 1c0 1-1 2-1 2 0 1 2 2 2 2l7 7-1 1h-2l1 1c-1 1-3 2-5 3h-2-3c-1 0-1 1-1 2h0c0 1-1 4 0 5l-4 2h0l-6 2h-2c-1 0-1 1-2 0h-2v1l-3-1 3-14c1-1 1-1 1-2-1 0-2 0-2-1 0-2 0-3 1-5s3-2 5-3l2 1v1h2c1-1 2-1 4-1h3c2-1 3-2 4-4z" class="Y"></path><path d="M266 478l3-14c1 2 0 3 1 5v1c-1 2 1 5-1 8v1l-3-1z" class="E"></path><path d="M276 455h2c1-1 2-1 4-1-1 1-2 1-3 2-1 2-1 4-2 6v3c-1 0-1 0-2 1-1 0-1 0-2 1h0-1c1-4 1-9 4-12z" class="C"></path><defs><linearGradient id="EH" x1="273.138" y1="468.898" x2="283.816" y2="473.686" xlink:href="#B"><stop offset="0" stop-color="#101010"></stop><stop offset="1" stop-color="#2c2c2c"></stop></linearGradient></defs><path fill="url(#EH)" d="M290 464l1 1c-1 1-4-1-5 1v1c-1 0-1 1-1 2h0c0 1-1 4 0 5l-4 2h0l-6 2h-2-1c-2-3 0-8 1-11 6-1 11-2 17-2v-1z"></path><path d="M289 450l1 1c0 1-1 2-1 2 0 1 2 2 2 2l7 7-1 1h-2l1 1c-1 1-3 2-5 3h-2-3v-1c1-2 4 0 5-1l-1-1c-3-2-5-1-9 0-1 0-3 0-4 1v-3c1-2 1-4 2-6 1-1 2-1 3-2h3c2-1 3-2 4-4z" class="g"></path><path d="M277 462l1 1h1v-1c2 0 3-3 5-4h0c1-1 2-1 3-1 3 0 6 4 8 6l1 1c-1 1-3 2-5 3 1-1 0-1 1-1 1-1 2-1 3-3-1 0-2-1-3-2-2 0-3-1-5-2-2 1-4 3-6 5-1 0-3 0-4 1v-3z" class="e"></path><path d="M281 464c2-2 4-4 6-5 2 1 3 2 5 2 1 1 2 2 3 2-1 2-2 2-3 3-1 0 0 0-1 1h-2-3v-1c1-2 4 0 5-1l-1-1c-3-2-5-1-9 0z" class="D"></path><defs><linearGradient id="EI" x1="247.229" y1="326.335" x2="236.269" y2="345.618" xlink:href="#B"><stop offset="0" stop-color="#010100"></stop><stop offset="1" stop-color="#262626"></stop></linearGradient></defs><path fill="url(#EI)" d="M229 311l1 1c1 0 4 2 4 3 10 7 18 14 24 24h-1c-1 0-2-1-3-2 1 1 2 2 2 3h0 0v1c1 0 1 0 1 1v1 1c2 2 3 4 4 7 2 5 3 10 5 14v2l-2-3c-1-2-5-11-6-12-3-1-6-6-7-8-1-1-2-1-2-2l-5-5h-1c-1-1-1-1-2-1l-1-1v-1c-1 0-1-1-2-1l-1-2-1 1v1l1 1h-1-1l-1-2v-1l-3-3c-3-4-6-9-9-13 0-1-1-3-2-4 1 1 2 2 3 2h0l3 3 1-2 2-3z"></path><path d="M240 334h2c1 1 2 2 2 3h-1c-1-1-1-1-2-1l-1-1v-1z" class="D"></path><path d="M231 328c1 1 1 0 1 1h1 0c0-1-1-2-1-3h0v-1h0l2 2-1 1 1 2v1l-3-3z" class="J"></path><path d="M251 336c1-1 1 0 2 0l1 1c1 1 2 2 2 3h0 0v1c1 0 1 0 1 1v1l-6-7z" class="L"></path><path d="M234 330l-1-2 1-1 3 4-1 1v1l1 1h-1-1l-1-2v-1-1z" class="D"></path><path d="M234 330c1 0 1 1 1 1v1h-1v-1-1z" class="e"></path><path d="M243 329h2c3 2 6 4 8 7-1 0-1-1-2 0h0l-8-7z" class="I"></path><path d="M227 316h2c1 1 3 3 4 3h1c2 0 3 2 4 3v4l-11-10z" class="Z"></path><path d="M229 311l1 1c1 0 4 2 4 3-1 1-1 2-1 3l1 1h-1c-1 0-3-2-4-3h-2-1l1-2 2-3z" class="D"></path><path d="M233 318c-1-1-3-3-4-5l1-1c1 0 4 2 4 3-1 1-1 2-1 3z" class="E"></path><defs><linearGradient id="EJ" x1="235.436" y1="328.702" x2="254.368" y2="325.376" xlink:href="#B"><stop offset="0" stop-color="#3f3d3f"></stop><stop offset="1" stop-color="#737373"></stop></linearGradient></defs><path fill="url(#EJ)" d="M233 318c0-1 0-2 1-3 10 7 18 14 24 24h-1c-1 0-2-1-3-2l-1-1c-2-3-5-5-8-7h-2c-1 0-4-3-5-3v-4c-1-1-2-3-4-3l-1-1z"></path><path d="M236 230c2 1 5 0 8 0h0c1 0 1 1 2 1h1l2 2h-1c-1 1-2 3-2 5v2l-3 4h2c-2 2-4 3-6 5h-1c0 1-1 8-1 8h-1l-1 1c-1 0-2 0-4 1 0 0-2 1-3 1h-1c-3 0-5 2-8 2l-2 2-1 1c-1-3 0-6 0-9v1h1c1-1 2-3 3-4 1-3 3-6 5-9h-1c1-2 2-3 2-4h1l-1-1 2-2h-1l1-1h-1l2-2 7-4z" class="g"></path><path d="M231 236l1 1h1c-3 2-6 5-8 7h-1c1-2 2-3 2-4h1c1-2 2-3 4-4z" class="a"></path><path d="M234 242l1 1c-1 1-3 2-3 3h0l-4 3-2-1c2-2 5-4 8-6z" class="Y"></path><defs><linearGradient id="EK" x1="242.765" y1="246.137" x2="228.985" y2="245.16" xlink:href="#B"><stop offset="0" stop-color="#6e6c6f"></stop><stop offset="1" stop-color="#8b8b8a"></stop></linearGradient></defs><path fill="url(#EK)" d="M243 238v1h2l1-1v2l-3 4c-2 2-5 2-8 3l-6 3h-1v-1l4-3h0c0-1 2-2 3-3l-1-1c1-2 7-3 9-4z"></path><path d="M235 243c3-1 6-3 10-4-3 4-8 5-13 7h0c0-1 2-2 3-3z" class="J"></path><path d="M243 238v1h2 0c-4 1-7 3-10 4l-1-1c1-2 7-3 9-4z" class="E"></path><path d="M236 230c2 1 5 0 8 0h0c1 0 1 1 2 1h1l2 2h-1c-1 1-2 3-2 5l-1 1h-2v-1l2-2c0-1 0-1-1-2-2 0-5 1-8 2-1 0-2 1-3 1h-1l-1-1c-2 1-3 2-4 4l-1-1 2-2h-1l1-1h-1l2-2 7-4z" class="G"></path><path d="M231 236c1-1 3-1 4-1l1 1c-1 0-2 1-3 1h-1l-1-1h0z" class="F"></path><path d="M229 234c2 0 3-1 4-1 0 1-1 1-1 1 0 1-1 1-1 2h0c-2 1-3 2-4 4l-1-1 2-2h-1l1-1h-1l2-2z" class="b"></path><path d="M229 250l6-3c3-1 6-1 8-3h2c-2 2-4 3-6 5h-1c0 1-1 8-1 8h-1l-1 1c-1 0-2 0-4 1 0 0-2 1-3 1h-1c-3 0-5 2-8 2l-2 2-1 1c-1-3 0-6 0-9v1h1c1-1 2-3 3-4v1l6-6 2 1v1h1z" class="g"></path><path d="M227 256l2-2v-1c2-2 5-3 7-3l-3 3-6 3z" class="S"></path><path d="M216 256v1h1c1-1 2-3 3-4v1 1c0 1 1 1 2 1-2 2-5 5-5 8h0l-1 1c-1-3 0-6 0-9z" class="N"></path><path d="M226 248l2 1v1h1l-7 6c-1 0-2 0-2-1v-1l6-6z" class="G"></path><defs><linearGradient id="EL" x1="234.482" y1="257.784" x2="224.293" y2="254.878" xlink:href="#B"><stop offset="0" stop-color="#626162"></stop><stop offset="1" stop-color="#858383"></stop></linearGradient></defs><path fill="url(#EL)" d="M236 250s1-1 2-1c0 1-1 8-1 8h-1l-1 1c-1 0-2 0-4 1 0 0-2 1-3 1h-1c-3 0-5 2-8 2 3-2 5-4 8-6l6-3 3-3z"></path><path d="M236 250s1-1 2-1c0 1-1 8-1 8h-1v-5h0l-3 1 3-3z" class="Y"></path><path d="M251 427c2-2 1-6 2-9 0 2 0 4-1 5 0 1 0 2 1 3 0 1 1 3 2 4v1h0l3 6s0 1 1 1v-1c2-1 3-2 5-3h0c3 0 6 0 9 1l4 4c3 1 5 4 8 6-1-2-2-3-3-4l-4-4c-2-2-5-3-7-5h1c3 1 6 3 8 5 0 0 0-1 1-1v-2c1 2 3 3 4 5 1 1 3 3 3 5h0c1 2 1 4 1 6-1 2-2 3-4 4h-3c-2 0-3 0-4 1h-2v-1l-2-1h5c2 0 5 0 7-1v-1l-1-1h-6c-3-1-5 0-9 1-4 2-9 1-13-1-5-2-8-7-9-12h-2 0c-2 0-4 2-6 1 1 0 1 0 2-1h0l3-3c-1-1-1 0-3-1 3-2 6-4 9-7z" class="N"></path><path d="M263 438h1v3c1 0 1 0 2 1l2-2-1-2c1 0 1 1 2 2 0 1-1 2-1 2-1 1-2 1-3 1s-2-1-2-2c-1-1-1-2 0-3z" class="k"></path><path d="M251 429c0 2 0 3 1 5 0 4 2 7 4 10-4-3-5-5-6-10h-1 0c0-3 0-3 2-5z" class="b"></path><path d="M251 427c2-2 1-6 2-9 0 2 0 4-1 5 0 1 0 2 1 3 0 1 1 3 2 4v1h0l3 6c-1 0-1-1-1-1l-2-3c0-1-1-2-1-3-1 0-1-1-2-2v6c-1-2-1-3-1-5v-2z" class="C"></path><path d="M242 434c3-2 6-4 9-7v2c-2 2-2 2-2 5h0c0 1 0 2-1 2v2h-2 0c-2 0-4 2-6 1 1 0 1 0 2-1h0l3-3c-1-1-1 0-3-1z" class="e"></path><path d="M245 435c0-1 0-1 1-1h1c0 1 0 2-1 4h0c-2 0-4 2-6 1 1 0 1 0 2-1h0l3-3z" class="B"></path><path d="M259 437c2-1 3-2 5-3h0c3 0 6 0 9 1l4 4h-2s-1 0-1-1h-1-3c-2-1-3-2-5-2h-1l1-1h-2c-1 1-3 2-3 3l-1 1h-1v-2s0 1 1 1v-1zm22-3c1 2 3 3 4 5 1 1 3 3 3 5h0c1 2 1 4 1 6-1 2-2 3-4 4h-3c-2 0-3 0-4 1h-2v-1l-2-1h5c2 0 5 0 7-1v-1l-1-1h0c1 0 2-1 2-2l-2-2 1-2c-1-2-4-5-6-7 0 0 0-1 1-1v-2z" class="E"></path><path d="M281 436c2 2 7 7 7 11 0 0 0 1-1 1l-2-2 1-2c-1-2-4-5-6-7 0 0 0-1 1-1z" class="J"></path><defs><linearGradient id="EM" x1="236.428" y1="347.554" x2="176.296" y2="325.658" xlink:href="#B"><stop offset="0" stop-color="#b8b6b9"></stop><stop offset="1" stop-color="#d7d7d6"></stop></linearGradient></defs><path fill="url(#EM)" d="M184 313c2 0 3 0 5 1h3 1l4 5 26 34c1 1 1 1 1 2l8 7-2-1c-1-1-2-2-4-2l-1-1h-1v-1c-1 0-2-1-3-1 0 0 0-1-1-2-1 0-2-1-3-2-2-1-4-2-6-4l-28-24c-2 1-3 1-5 1l-4 2h-4-2v-1h0c2-1 2-1 2-2h0v-1c0-2-1-4-2-5 0-1-2-3-2-4v-1c6 0 12 1 18 0z"></path><path d="M184 313c2 0 3 0 5 1h3 1v1 3h-1s-1 0-1 1c-4 1-7 2-10 1-2-1-3-1-5-1l7 5c-2 1-3 1-5 1l-4 2h-4-2v-1h0c2-1 2-1 2-2h0v-1c0-2-1-4-2-5 0-1-2-3-2-4v-1c6 0 12 1 18 0z" class="n"></path><path d="M184 313c2 0 3 0 5 1h-12c-3 0-6-1-9 0h0l2 2h-1v1l-1 1c0-1-2-3-2-4v-1c6 0 12 1 18 0z" class="a"></path><path d="M170 316c1-1 4 0 6 0 4 1 13 2 17-1v3h-1s-1 0-1 1c-4 1-7 2-10 1-2-1-3-1-5-1s-4-2-6-3z" class="m"></path><defs><linearGradient id="EN" x1="174.819" y1="325.388" x2="176.612" y2="319.428" xlink:href="#B"><stop offset="0" stop-color="#5d5b5d"></stop><stop offset="1" stop-color="#7e7d7d"></stop></linearGradient></defs><path fill="url(#EN)" d="M170 316c2 1 4 3 6 3l7 5c-2 1-3 1-5 1l-4 2h-4-2v-1h0c2-1 2-1 2-2h0v-1c0-2-1-4-2-5l1-1v-1h1z"></path><path d="M168 326h0c2-1 2-1 2-2l1 1c2 1 5 0 7 0l-4 2h-4-2v-1z" class="F"></path><path d="M169 317c2 1 3 2 3 5l-1 3-1-1h0v-1c0-2-1-4-2-5l1-1z" class="d"></path><defs><linearGradient id="EO" x1="195.007" y1="257.014" x2="203.53" y2="243.297" xlink:href="#B"><stop offset="0" stop-color="#5c5c5d"></stop><stop offset="1" stop-color="#757374"></stop></linearGradient></defs><path fill="url(#EO)" d="M196 236v1h2v1h0c2 0 3 0 4 1 2 1 3 2 5 4v-1-2h0c1 1 1 2 2 2v1l1 2c1 1 1 2 1 3 0 2 1 4 0 6s-1 4-3 6c-1 1-1 3-1 4-1-1-1 0-2 0h0-1v-8c-1 1-1 3-1 4s0 2-1 2v2 3h0l-3 3v1h-1c0 1 0 1-1 2h-1l-1-1c0-1-1-2-2-2v-1c-1-1-1-2-2-3 0-1-3-3-3-3-3-1-5-3-7-5 1-1 0-2 0-3v-1h0-1l-1-1v-1-1c0-1 0-1 1-1v-2l4-5h2l4-3v-1c1-1 2-2 3-2h2l1-1z"></path><path d="M193 241l1 1c0 1-3 2-4 3l-1-1 3-2 1-1z" class="B"></path><path d="M198 238h0c2 0 3 0 4 1l-2 2-4-1c0-1 1-2 2-2z" class="f"></path><path d="M197 242h1c1 0 1 0 2 1s2 4 2 6l-5-6v-1z" class="F"></path><path d="M200 241l2-2c2 1 3 2 5 4v3l-1 1c-1-3-3-5-6-6z" class="Q"></path><path d="M186 250c1 0 2-1 2-1 2-1 3-3 4-3h3l1 1h-2c-1 1-3 2-4 3s-4 4-5 4l-1-1 2-3z" class="Z"></path><path d="M196 236v1h2v1c-1 0-2 1-2 2l-3 1-1 1h-1c0-1 0 0-1-1v-1h0v-1c1-1 2-2 3-2h2l1-1z" class="F"></path><path d="M194 247c2 1 3 1 5 3h0c-2-1-3-1-5 0-3 0-5 2-6 4v1c1 1 2 3 2 5h0c-3-1-4-2-5-5-1 0-1 0-1-1v-1l1 1c1 0 4-3 5-4s3-2 4-3z" class="o"></path><path d="M190 240h0v1c1 1 1 0 1 1h1l-3 2-3 6-2 3v1h0c-1-1-1-2-1-3h0l-1-1 1 2h0-4v-1c0-1 0-1 1-1v-2l4-5h2l4-3z" class="H"></path><path d="M184 243h2c-1 2-4 5-4 7l1 2h0-4v-1c0-1 0-1 1-1v-2l4-5z" class="B"></path><path d="M182 250l1 1h0c0 1 0 2 1 3h0c0 1 0 1 1 1 1 3 2 4 5 5h0c1 0 2 2 3 2v1c1 1 1 2 1 3h0l1 1h0l-1 1c-1-1-2-1-3-2 0-1-3-3-3-3-3-1-5-3-7-5 1-1 0-2 0-3v-1h0-1l-1-1v-1h4 0l-1-2z" class="G"></path><path d="M181 254c2 3 5 6 7 8h0c2 1 3 2 4 2l1-1c1 1 1 2 1 3h0l1 1h0l-1 1c-1-1-2-1-3-2 0-1-3-3-3-3-3-1-5-3-7-5 1-1 0-2 0-3v-1z" class="T"></path><defs><linearGradient id="EP" x1="209.574" y1="260.187" x2="203.804" y2="251.305" xlink:href="#B"><stop offset="0" stop-color="#757472"></stop><stop offset="1" stop-color="#8b898d"></stop></linearGradient></defs><path fill="url(#EP)" d="M207 240h0c1 1 1 2 2 2v1l1 2c1 1 1 2 1 3 0 2 1 4 0 6s-1 4-3 6c-1 1-1 3-1 4-1-1-1 0-2 0 1-3 0-7 1-10 1-2 0-5 0-7l1-1v-3-1-2z"></path><path d="M209 243l1 2c1 1 1 2 1 3 0 2 1 4 0 6s-1 4-3 6l1-17z" class="O"></path><path d="M188 255c2 0 3 1 5 3 2 0 7 4 8 6h1 0v3h0l-3 3v1h-1c0 1 0 1-1 2h-1l-1-1c0-1-1-2-2-2v-1c-1-1-1-2-2-3 1 1 2 1 3 2l1-1h0l-1-1h0c0-1 0-2-1-3v-1c-1 0-2-2-3-2 0-2-1-4-2-5z" class="B"></path><path d="M193 258c2 0 7 4 8 6-1 0-2 0-2-1-1 0-2 0-2-1-1-1-1-2-2-3-1 0-1-1-2-1z" class="R"></path><path d="M193 262v-2h0l2 2v1c1 1 1 1 2 1h0c1 0 2 1 3 1v1c1 1 1 1 2 1l-3 3v1h-1l-5-9z" class="G"></path><path d="M193 262l5 9c0 1 0 1-1 2h-1l-1-1c0-1-1-2-2-2v-1c-1-1-1-2-2-3 1 1 2 1 3 2l1-1h0l-1-1h0c0-1 0-2-1-3v-1h0z" class="S"></path><path d="M188 255v-1c1-2 3-4 6-4 2-1 3-1 5 0 0 1 1 3 2 4v-1h1v1c1 0 1 1 2 2-1 1-1 3-1 4s0 2-1 2v2h0-1c-1-2-6-6-8-6-2-2-3-3-5-3z" class="g"></path><path d="M201 254v-1h1v1c1 0 1 1 2 2-1 1-1 3-1 4s0 2-1 2c0-3 0-5-1-8z" class="L"></path><defs><linearGradient id="EQ" x1="234.736" y1="283.441" x2="221.281" y2="282.918" xlink:href="#B"><stop offset="0" stop-color="#050505"></stop><stop offset="1" stop-color="#414041"></stop></linearGradient></defs><path fill="url(#EQ)" d="M236 257h1l-11 53v4h1l-1 2-3-3c-4-5-8-10-11-17l-1-2-2-7v-1c0-3 0-7-1-10 1-2 1-3 2-5s3-4 6-6l1-1 2-2c3 0 5-2 8-2h1c1 0 3-1 3-1 2-1 3-1 4-1l1-1z"></path><path d="M227 280c0-3 1-5 2-6 0 2-1 4-2 7v-1z" class="T"></path><path d="M227 280v1c0 1-1 3-1 4-1 4 0 9-3 11l-1-1c-2 0-3-1-4-2 0-1 1-2 2-2h1l-1-1h1c1-1 1-2 1-3l1-1c0 1 1 1 1 1 1-1 1-2 1-3l2-4z" class="N"></path><path d="M222 287l1 1v7h-1v-4h-1l-1-1h1c1-1 1-2 1-3z" class="B"></path><path d="M221 291h1v4h0c-2 0-3-1-4-2 0-1 1-2 2-2h1z" class="L"></path><path d="M221 291h1c0 1 0 2-1 3-1-1-1-1-1-2v-1h1z" class="H"></path><path d="M228 260c1 0 3-1 3-1 2-1 3-1 4-1 0 0-3 2-3 3v1l-2 2c-5 4-7 11-9 17h1 0 0c0 3-1 6-2 9l1 1h-1c-1 0-2 1-2 2 1 1 2 2 4 2l1 1 1 1c1 2 1 8 0 10-3-3-6-6-7-10-1-2-2-5-2-7-1-3-1-6 0-9 0-6 5-11 10-15 0-1 0-1 1-2 0-1 1-2 1-3l1-1z" class="g"></path><path d="M228 260c1 0 3-1 3-1 2-1 3-1 4-1 0 0-3 2-3 3l-7 5c0-1 0-1 1-2 0-1 1-2 1-3l1-1z" class="Y"></path><defs><linearGradient id="ER" x1="219.412" y1="282.343" x2="219.302" y2="289.724" xlink:href="#B"><stop offset="0" stop-color="#555455"></stop><stop offset="1" stop-color="#747474"></stop></linearGradient></defs><path fill="url(#ER)" d="M221 281h1 0 0c0 3-1 6-2 9l1 1h-1c-1 0-2 1-2 2l-1-1c-1-3 2-8 4-11z"></path><path d="M219 262c3 0 5-2 8-2h1l-1 1c0 1-1 2-1 3-1 1-1 1-1 2-5 4-10 9-10 15-1 3-1 6 0 9 0 2 1 5 2 7 1 4 4 7 7 10 1 1 1 3 2 3v4h1l-1 2-3-3c-4-5-8-10-11-17l-1-2-2-7v-1c0-3 0-7-1-10 1-2 1-3 2-5s3-4 6-6l1-1 2-2z" class="b"></path><path d="M215 269c0 2-2 4-3 7s0 7-1 10l-1-1c0 1 0 1-1 2v-1c0-3 0-7-1-10 1-2 1-3 2-5l1 3h0c1-2 3-4 4-5z" class="B"></path><path d="M210 271l1 3h0c-1 4-1 7-1 11 0 1 0 1-1 2v-1c0-3 0-7-1-10 1-2 1-3 2-5z" class="I"></path><path d="M219 262c3 0 5-2 8-2h1l-1 1-2 2c-3 2-7 3-10 6h0c-1 1-3 3-4 5h0l-1-3c1-2 3-4 6-6l1-1 2-2z" class="X"></path><path d="M225 263l2-2c0 1-1 2-1 3-1 1-1 1-1 2-5 4-10 9-10 15-1 3-1 6 0 9-1-1-2-2-2-4v-1h0c-1-6 1-11 4-15 2-1 4-2 5-4l3-3z" class="O"></path><path d="M330 286l2 4 3 12c1 3 1 6 2 9v15c0 1-1 1-1 2-1 1-1 4-1 6l-2 2-4 4c-1 1-2 3-3 5-1 0-2-1-2-1v-1-2-1h-1v2h0v-1 4 2 3c-1-1-1-2-1-4-2-3-2-6-3-10l-5-12c0-1-1-2-1-4-1-2-2-4-3-7v-1l-1-5-1 5v1 1c-2-1-2-3-3-5h0c-1-2-2-4-2-6v-3-3-1h1 0l2 5s1-1 2-1c1 1 1 3 3 4 0 0 1 0 1 1l1-1h0v-2h0v-4l1-1c0 2 0 2 1 4h0l-1-4c1 0 1 0 2-1l-1-1c0-1-1-3 0-4 0-1 1 0 1-1h1l2 2h1 1l-1-2c1-1 1-1 1-2s1-1 2-2h1 2l1 1v1h1v-1-1h2z" class="r"></path><path d="M324 297h1s0 1 1 2l-1 6c0-2-1-4-2-6 1-1 1-1 1-2z" class="Q"></path><path d="M324 297v-2h1c2 0 3 2 4 3 1 3 1 5 2 8l3 9c0 6 0 13-1 20v1l-4 4c0-1 0-2 1-3v-1c1-2 1-6 1-8s1-5 1-7c0-3 0-5-1-7-1-5-2-9-3-13 0-1-1-2-2-3h0c0-1 0-1-1-2v1h-1z" class="i"></path><path d="M304 296h0l2 5s1-1 2-1c1 1 1 3 3 4 0 0 1 0 1 1l1-1h0v-2h0v-4l1-1-1 2v1c2 4 1 8 2 12h0-1v2c1 0 1 1 1 2l1 4c0-1 0-2-1-2v-1s0-1-1-2c0 1 0 2 1 4h0c-1-3-3-6-4-8h-1v1l-1-5-1 5v1 1c-2-1-2-3-3-5h0c-1-2-2-4-2-6v-3-3-1h1z" class="Q"></path><path d="M306 301s1-1 2-1c1 1 1 3 3 4 0 0 1 0 1 1l1-1c1 2 1 4 1 6-2-3-4-5-6-8 0 2 1 3 1 5l-1 5-2-11z" class="K"></path><path d="M304 296h0l2 5 2 11v1 1c-2-1-2-3-3-5h0c-1-2-2-4-2-6v-3-3-1h1z" class="J"></path><path d="M315 295c0-1-1-3 0-4 0-1 1 0 1-1h1l2 2h1 1v1l1 3 1 2c-1 0-1-1-1-1h-1 0l-1 2v-2c0 1-1 2 0 3s1 2 1 4c2 4 3 9 4 13 1 2 1 3 2 5v7c0 2 1 4 1 6l-3-7c0-1-1-3-1-4-1-1-1 0-1-1l-1-2v-1c0-1-1-3-1-4 0-2 0-3-1-5h-1c-1-1-3-8-4-10l-1-4c1 0 1 0 2-1l-1-1z" class="s"></path><defs><linearGradient id="ES" x1="313.951" y1="296.798" x2="320.236" y2="299.483" xlink:href="#B"><stop offset="0" stop-color="#bab8b8"></stop><stop offset="1" stop-color="#dcdddc"></stop></linearGradient></defs><path fill="url(#ES)" d="M315 295c0-1-1-3 0-4 0-1 1 0 1-1h1l2 2h1 1v1l1 3 1 2c-1 0-1-1-1-1h-1 0l-1 2v-2c0-1-1-2-1-2 0 1 0 2 1 3v5 3c-1 1 0 2 0 3v1 1h-1c-1-1-3-8-4-10l-1-4c1 0 1 0 2-1l-1-1z"></path><path d="M316 296l2 9c0 2 1 4 2 5v1h-1c-1-1-3-8-4-10l-1-4c1 0 1 0 2-1z" class="K"></path><defs><linearGradient id="ET" x1="328.796" y1="313.425" x2="332.95" y2="289.857" xlink:href="#B"><stop offset="0" stop-color="#858684"></stop><stop offset="1" stop-color="#a2a1a2"></stop></linearGradient></defs><path fill="url(#ET)" d="M330 286l2 4 3 12c1 3 1 6 2 9v15c0 1-1 1-1 2-1 1-1 4-1 6l-2 2v-1c1-7 1-14 1-20l-3-9c-1-3-1-5-2-8-1-1-2-3-4-3h-1v2c0 1 0 1-1 2v-1l-1-2-1-3v-1l-1-2c1-1 1-1 1-2s1-1 2-2h1 2l1 1v1h1v-1-1h2z"></path><path d="M322 296h1v-2c1-1 2-1 3-2 2 2 2 4 3 6-1-1-2-3-4-3h-1v2c0 1 0 1-1 2v-1l-1-2z" class="P"></path><path d="M330 286l2 4h-2c-1 1 0 2 0 3-3-1-1-5-6-3-1 0-2 1-2 1v2h-1v-1l-1-2c1-1 1-1 1-2s1-1 2-2h1 2l1 1v1h1v-1-1h2z" class="F"></path><path d="M171 252c1 1 3 3 3 4l2-2h1s1 0 2 1l1-1h1 0v1c0 1 1 2 0 3 2 2 4 4 7 5 0 0 3 2 3 3 1 1 1 2 2 3v1c-1 4-2 9-2 13h0c1 5-1 7 2 11l1 2 2 3h0c-1 1-1 3-1 4v6 1l-1 2c-1 1-1 1-2 1v1h-3c-2-1-3-1-5-1-6 1-12 0-18 0h0l2-1v-1s-1 0-1-1v-1c-2 0-2-1-3-2l-1-1-1-1h1v-2l-1-11-1-1h0c-2-2-2-3-3-4l-1-1c-1-2-2-3-4-4l-1-1c-1-1-1-1-1-2h-6-2v-3l-1-8 2-1 2-1 4-1c2 0 3 0 4-1h1c1 0 3-2 3-3h3 1 0 2c1 2 4 4 7 4l1 2 1-1c-1-2-1-4-2-6v-1-7z" class="g"></path><path d="M180 305l2 7c-1 0-2-1-3-3v-3l1-1z" class="R"></path><path d="M164 307v-1c1 0 1-1 2-1h1c1 2 1 4 2 6 0 1 0 0-1 1v-1s-1 0-1-1v-1c-2 0-2-1-3-2z" class="F"></path><path d="M176 275l1-1c2 2 5 3 8 4v4c-1 1-2 1-3 2l-1-1c-2-2-3-6-5-8z" class="Q"></path><defs><linearGradient id="EU" x1="191.512" y1="298.372" x2="178.411" y2="296.441" xlink:href="#B"><stop offset="0" stop-color="#4d4d4c"></stop><stop offset="1" stop-color="#7e7d7f"></stop></linearGradient></defs><path fill="url(#EU)" d="M185 278h0l1 1c-1 2-1 3 0 5 0 5 2 10 3 15l1 9c0 2 0 4 1 5h1v1h-3c-2-1-3-1-5-1-1-7-3-14-4-21-1-2-1-4-1-5s1-3 2-4l1 1c1-1 2-1 3-2v-4z"></path><path d="M171 252c1 1 3 3 3 4l2-2h1s1 0 2 1l1-1h1 0v1c0 1 1 2 0 3 2 2 4 4 7 5 0 0 3 2 3 3 1 1 1 2 2 3v1c-1 4-2 9-2 13h0c1 5-1 7 2 11l1 2 2 3h0c-1 1-1 3-1 4v6 1l-1 2c-1 1-1 1-2 1h-1c-1-1-1-3-1-5l-1-9c-1-5-3-10-3-15-1-2-1-3 0-5l-1-1h0c-3-1-6-2-8-4l-1 1h0v-2h-1l-2-2c1 0 0 0 1-1-1-1-1-2-2-3l1-1c-1-2-1-4-2-6v-1-7z" class="g"></path><path d="M183 268h2c0 1 1 1 1 2v3 6h0l-1-1v-6s0-1-1-1h-1 0v-3z" class="Z"></path><path d="M180 261c1 2 3 3 3 6v1h0v3h0c0 1-1 2-1 2-1 0-2-1-3-2h1l1-1c0-3-1-6-1-9z" class="k"></path><path d="M183 271h1c1 0 1 1 1 1v6h0c-3-1-6-2-8-4l-1 1h0v-2h0l5 1c1 0 1 0 1-1 0 0 1-1 1-2z" class="B"></path><defs><linearGradient id="EV" x1="195.989" y1="302.882" x2="189.304" y2="301.092" xlink:href="#B"><stop offset="0" stop-color="#4d4c4e"></stop><stop offset="1" stop-color="#656565"></stop></linearGradient></defs><path fill="url(#EV)" d="M194 312c-2-10-4-19-3-29h0c1 5-1 7 2 11l1 2 2 3h0c-1 1-1 3-1 4v6 1l-1 2z"></path><path d="M194 296l2 3h0c-1 1-1 3-1 4v6 1c0-2 0-3-1-4v-6c0-1 0-1-1-2 0-1 0-1 1-2z" class="V"></path><path d="M180 254h1 0v1c0 1 1 2 0 3l-1-1h-1l1 4c0 3 1 6 1 9l-1 1h-1c-2-1-3-8-4-11 0-1 0-2-1-4l2-2h1s1 0 2 1l1-1z" class="S"></path><path d="M180 254h1 0v1c0 1 1 2 0 3l-1-1h-1l1 4c0 3 1 6 1 9l-1 1c-1-3-3-16-3-17 0 0 1 0 2 1l1-1z" class="B"></path><path d="M171 252c1 1 3 3 3 4 1 2 1 3 1 4 1 3 2 10 4 11 1 1 2 2 3 2 0 1 0 1-1 1l-5-1h0-1l-2-2c1 0 0 0 1-1-1-1-1-2-2-3l1-1c-1-2-1-4-2-6v-1-7z" class="a"></path><path d="M171 259l1-2s1 0 2 1h0l-2 2h-1v-1z" class="d"></path><defs><linearGradient id="EW" x1="180.607" y1="271.302" x2="174.246" y2="268.293" xlink:href="#B"><stop offset="0" stop-color="#727272"></stop><stop offset="1" stop-color="#8a898a"></stop></linearGradient></defs><path fill="url(#EW)" d="M175 260c1 3 2 10 4 11 1 1 2 2 3 2 0 1 0 1-1 1l-5-1h0-1l-2-2c1 0 0 0 1-1-1-1-1-2-2-3l1-1v1c1 0 1 1 1 1h1v-2h0v-1h1c-1-1-1-1-1-2v-2-1z"></path><defs><linearGradient id="EX" x1="168.269" y1="308.561" x2="164.342" y2="276.002" xlink:href="#B"><stop offset="0" stop-color="#434342"></stop><stop offset="1" stop-color="#737172"></stop></linearGradient></defs><path fill="url(#EX)" d="M162 261h2c1 2 4 4 7 4l1 2c1 1 1 2 2 3-1 1 0 1-1 1l2 2c-1 0-1 0-1 1v1c1 10 3 20 6 30l-1 1v3l-17-18v1l-1-1h0c-2-2-2-3-3-4l-1-1c-1-2-2-3-4-4l-1-1c-1-1-1-1-1-2h-6-2v-3l-1-8 2-1 2-1 4-1c2 0 3 0 4-1h1c1 0 3-2 3-3h3 1 0z"></path><defs><linearGradient id="EY" x1="161.249" y1="284.518" x2="157.193" y2="276.118" xlink:href="#B"><stop offset="0" stop-color="#6b696b"></stop><stop offset="1" stop-color="#8a8988"></stop></linearGradient></defs><path fill="url(#EY)" d="M154 276c2 0 3 1 5 0l3 2v2c-1-1-2-2-3-2v1l4 4c0 1-1 1-1 2h0-1l-1-2c-2-3-4-5-6-7z"></path><path d="M151 279c-1-2-2-4-3-5-1-2-4-4-4-6h1c1 2 3 4 4 6l13 17v1l-1-1h0c-2-2-2-3-3-4l-1-1c-1-2-2-3-4-4l-1-1c-1-1-1-1-1-2z" class="S"></path><path d="M144 267l2-1v1 1h0l13 8c-2 1-3 0-5 0-2-2-2-2-5-2-1-2-3-4-4-6h-1c0 2 3 4 4 6 1 1 2 3 3 5h-6-2v-3l-1-8 2-1z" class="a"></path><path d="M142 268l2-1v6c1 2 1 4 1 6h0-2v-3l-1-8z" class="K"></path><defs><linearGradient id="EZ" x1="180.004" y1="291.393" x2="160.316" y2="288.064" xlink:href="#B"><stop offset="0" stop-color="#777"></stop><stop offset="1" stop-color="#aaa9a9"></stop></linearGradient></defs><path fill="url(#EZ)" d="M162 278c2 1 5 2 8 3h0c2-2 3-4 4-6 1 10 3 20 6 30l-1 1c-1-2-3-5-4-7-2-3-4-7-6-10s-5-6-7-9v-2z"></path><path d="M162 261h2c1 2 4 4 7 4l1 2c1 1 1 2 2 3-1 1 0 1-1 1l2 2c-1 0-1 0-1 1v1c-1 2-2 4-4 6h0l-8-3-3-2-13-8h0v-1-1l4-1c2 0 3 0 4-1h1c1 0 3-2 3-3h3 1 0z" class="n"></path><path d="M154 264h1l-1 2h2l1 1h-11v-1l4-1c2 0 3 0 4-1z" class="E"></path><path d="M156 266l5-2 12 7 2 2c-1 0-1 0-1 1 0 0 0-1-1-1-5-2-11-5-16-6l-1-1z" class="T"></path><defs><linearGradient id="Ea" x1="168.418" y1="272.802" x2="159.807" y2="261.018" xlink:href="#B"><stop offset="0" stop-color="#b7b5b6"></stop><stop offset="1" stop-color="#dfdedf"></stop></linearGradient></defs><path fill="url(#Ea)" d="M162 261h2c1 2 4 4 7 4l1 2c1 1 1 2 2 3-1 1 0 1-1 1l-12-7-5 2h-2l1-2c1 0 3-2 3-3h3 1 0z"></path><path d="M350 266h1v1c3 2 6 3 9 5 0 5 1 9 3 12v1c1 2 3 3 4 4 0 1 1 1 1 2v11 14 7 14 25 39 10 6 1 7 13 33l-1 27v11c0 2 0 5 1 7 7 5 14 10 23 13h2l1 1c1 0 2 1 4 1l4 2 13 3h1l3 1c1 0 3 1 4 1 0 1 1 1 1 2h0l-7-1h-13l-86 1h-2-8-4c-2 0-3 0-5 1v-2c1 0 1-1 1-1 2 0 2 0 4-1 1 0 3 0 4-1h2c0-2 0-4 1-6l4 1c0 1 0 1 1 1s0 0 1 1h1l4-2c2 0 5-2 6-3l2-1h0c4-4 5-10 6-15 1 0 0-2 1-3 1-8 1-16 1-24v-38-9-14-48c0-8 1-16 0-25v-2-15c-1-1-2 0-3 0 0-2 0-5 1-6 0-1 1-1 1-2v-15c-1-3-1-6-2-9l-3-12-2-4v-1l1 1c1 0 1 0 2-1 0-1 1-1 2-2v1s0 1 1 1h0 0c1 2 0 3 2 4v-2-2c0-3-1-6 0-9v-5h3c0 1 0 1 1 1l-1-2c2-1 7-3 9-4z" class="n"></path><path d="M342 281c2-1 4 0 5 1v5h-1c0-2 0-3-1-4 0-1-1-1-2-1h-1v-1z" class="p"></path><path d="M311 530l4 1c0 1 0 1 1 1s0 0 1 1h1l-8 3c0-2 0-4 1-6z" class="T"></path><path d="M342 293c3 1 3 8 5 10 1 1 4 2 6 3l-1 1-4-2c-3-1-4 3-7 3h0c1-2 0-3 1-5 1 0 2 0 2-1 0-3-1-6-2-8v-1z" class="r"></path><path d="M365 451h0c0 1-1 2-1 4-1 2-2 4-1 6 0 2 1 4 2 7 1 2 1 6 0 8v-7l-3-6c0 2 0 4-1 5v1c-1 2-3 4-5 6-1 1-2 3-3 4 0 0-2 1-2 2-1 1-1 2-1 3h0v-2c0-1 1-2 2-3 2-1 3-2 4-4s3-4 4-6c1-4 1-7 1-10 1-3 2-6 4-8z" class="U"></path><path d="M368 417c-1-8-1-17-1-25v-49-13c0-1 1-4 0-5-2 0-3 1-5 1-1 1-2 1-4 2h0c-1 1-2 1-2 2-1 2-2 3-3 5 0-1 0-2 1-3 2-3 1-10 1-14-1-1-1-3-1-5l1-5v-1l4-2s-2 2-2 3c-1 1-1 2-1 3l2 2h1 0-3v-1l-1 1 1 6v9h1c3-2 7-3 11-5v14 25 39 10 6z" class="r"></path><path d="M336 285c1 2 0 3 2 4v-2-2c0-3-1-6 0-9v6 165-9-14-48c0-8 1-16 0-25v-2-15c-1-1-2 0-3 0 0-2 0-5 1-6 0-1 1-1 1-2v-15c-1-3-1-6-2-9l-3-12-2-4v-1l1 1c1 0 1 0 2-1 0-1 1-1 2-2v1s0 1 1 1h0 0z" class="B"></path><defs><linearGradient id="Eb" x1="336.648" y1="284.772" x2="332.504" y2="291.145" xlink:href="#B"><stop offset="0" stop-color="#424142"></stop><stop offset="1" stop-color="#5e5e5d"></stop></linearGradient></defs><path fill="url(#Eb)" d="M336 285c1 2 0 3 2 4v-2-2c0-3-1-6 0-9v6 9h-1c0-1-1-1-1-1l-1 1c1 3 1 7 0 11l-3-12-2-4v-1l1 1c1 0 1 0 2-1 0-1 1-1 2-2v1s0 1 1 1h0 0z"></path></svg>