<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="90 42 862 924"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#8b1214}.C{fill:#120a0a}.D{fill:#9b1718}.E{fill:#620b0a}.F{fill:#720d0d}.G{fill:#9f1315}.H{fill:#580a0a}.I{fill:#450908}.J{fill:#670a0b}.K{fill:#5d0b0b}.L{fill:#3c0605}.M{fill:#b81e20}.N{fill:#2a282a}.O{fill:#4c0807}.P{fill:#302c2a}.Q{fill:#480908}.R{fill:#2f0706}.S{fill:#360707}.T{fill:#400706}.U{fill:#171515}.V{fill:#550a0b}.W{fill:#370605}.X{fill:#1c1110}.Y{fill:#272526}.Z{fill:#151413}.a{fill:#941214}.b{fill:#343131}.c{fill:#280404}.d{fill:#232022}.e{fill:#891212}.f{fill:#d22f32}.g{fill:#4e4a47}.h{fill:#831111}.i{fill:#6d0d0d}.j{fill:#4f0a0a}.k{fill:#8a1414}.l{fill:#403e3d}.m{fill:#8f1517}.n{fill:#1f0303}.o{fill:#2e0706}.p{fill:#ad1b1d}.q{fill:#555251}.r{fill:#6a6561}.s{fill:#d22a2d}.t{fill:#615c59}.u{fill:#bf1b1e}.v{fill:#bdbab1}.w{fill:#4f4740}.x{fill:#d9d6ce}.y{fill:#d9282c}.z{fill:#df4146}.AA{fill:#d12427}.AB{fill:#e8595f}.AC{fill:#260a07}.AD{fill:#a6a29a}.AE{fill:#e8454a}.AF{fill:#ec656d}.AG{fill:#b31e21}.AH{fill:#dfddda}.AI{fill:#ee545a}.AJ{fill:#070302}.AK{fill:#726e6b}.AL{fill:#908c87}.AM{fill:#868078}.AN{fill:#d4d0c9}.AO{fill:#c0bdb6}.AP{fill:#7a7672}.AQ{fill:#ec8790}.AR{fill:#e76670}.AS{fill:#938f89}.AT{fill:#aeaba3}.AU{fill:#51473f}.AV{fill:#dddace}.AW{fill:#9a958b}.AX{fill:#e4e2d8}.AY{fill:#a9a39b}.AZ{fill:#e8cbcd}.Aa{fill:#bcb9ad}.Ab{fill:#bbb7ab}.Ac{fill:#eacacc}.Ad{fill:#e7828b}.Ae{fill:#ec98a2}.Af{fill:#ba555b}</style><path d="M620 75c2 1 3 2 5 2l3-2c0 2-3 4-5 5h-2c0-1-1-2-1-3v-2z" class="y"></path><path d="M613 111v-2h1v1l1 1 2-1s1 2 2 2c2 2 3 3 3 6-3-3-5-5-9-7z" class="P"></path><path d="M444 102c3 4 4 10 5 15 0-1-1-1-2-1 0-1-1-3-2-5l-1-3 1-1c-1-2-1-3-1-5z" class="D"></path><path d="M908 524c1 2 0 2 2 3h0l1 1c0 3-1 7-3 9h-1c0-3 1-6 1-9v-1-3z" class="Q"></path><path d="M603 154l-1-2c2 1 3 2 5 3s5 2 8 1l1 1v2h-5l-2-2-1 1h-1c-1-2-2-3-4-4z" class="d"></path><path d="M447 116c1 0 2 0 2 1 2 5 2 11 2 16l-1-2-3-15z" class="h"></path><path d="M419 154c2-1 4-2 6-2-3 3-6 5-8 9v1c-1-1-2-1-3-1h-2-1l3-3c2-2 5-2 8-4l-1-1-1 1h-1z" class="Z"></path><path d="M268 575l-6-11c-2-3-5-6-5-10 1 1 2 1 2 3 1 1 1 2 2 3l3 3c1 2 2 4 3 7 0 1 1 2 1 3v2z" class="F"></path><path d="M860 371c4 6 8 12 13 18 2 3 5 5 7 7h0c-2 0-3-1-5-1-1-1-4-3-5-5s-1-4-2-5c-2-4-5-8-8-12v-2z" class="I"></path><path d="M263 755c-7 1-13-1-19-3 5 0 10 1 15 0h1 3 2c1 2 2 1 4 2-2 0-4 0-6 1z" class="J"></path><path d="M168 193c2 0 3 0 4 1l1 3 2 7v1 9c-2-7-4-14-7-21z" class="G"></path><path d="M582 105c1 3-3 9-1 12h0l-1 5c0 3-1 5-1 7-1 3-1 6-2 8-1-11 1-22 5-32z" class="u"></path><path d="M741 128c5 1 9 3 14 5 3 1 7 2 10 3h-2 0 0c2 1 2 1 3 2l-22-6h0l-3-1v-1c-1 0-1 0-2-1l2-1z" class="e"></path><path d="M440 121c3 2 3 3 4 5h1c1 2 3 4 5 5l1 2c0 5 0 9-1 14-2-10-5-17-10-26z" class="AC"></path><path d="M127 156h0c1-1 1-1 2-1 10 2 16 5 24 11l5 6-1 3c-4-5-9-11-15-14-4-2-10-4-15-5z" class="u"></path><path d="M288 117l-4-4c6 2 15 5 22 3 5-1 10-3 14-5-1 2-3 3-6 4-2 1-4 4-6 4-3 1-9-2-12 0-2-1-5-2-8-2z" class="M"></path><path d="M269 754c2-1 5-1 7-2 8-2 14-6 20-9v-1l2 2c-10 7-21 12-35 11 2-1 4-1 6-1z" class="F"></path><path d="M718 118h1c1 1 2 1 3 2 1 0 2 1 4 1h0c3 0 5-2 7-3s5-1 7-1c0 1-1 1-2 3 0 0-2-1-3 0-2 1-2 3-4 4-1 0-2 0-2-1l-4 2c-2 0-2 0-4-1h-1l-1-1 1-1c-1-1-2-2-2-3v-1z" class="D"></path><path d="M720 122c2 1 4 1 5 2v1c-2 0-2 0-4-1h-1l-1-1 1-1z" class="M"></path><path d="M378 67c6 2 19 11 25 9 2 0 3-2 4-3 0 2-1 5-3 7-1 0-1 0-1 1l-1-1h-3c-1 0-1 0-2-1-1 0-2-1-3-1-3-1-5-3-7-5-3-2-7-3-9-6z" class="z"></path><path d="M121 487l2-2-4 54c-3-5-4-10-4-17l1-2h1 1c0-11 1-22 3-33z" class="Q"></path><path d="M173 197l5 1c1 1 2 2 2 4h1v1c-1 1-2 3-3 4 1 2 1 2 3 3-1 2-1 3-2 5v1l1 14c-3-5-5-10-5-16v-9-1l-2-7z" class="p"></path><path d="M177 213c0-2 0-4 1-6 1 2 1 2 3 3-1 2-1 3-2 5v1 4c-2-2-2-5-2-7z" class="R"></path><path d="M173 197l5 1c1 1 2 2 2 4h1v1c-1 1-2 3-3 4-1 2-1 4-1 6 0-3-1-5-2-8v-1l-2-7z" class="B"></path><path d="M173 197l5 1c1 1 2 2 2 4h1l-4-1c-1 1-1 2-2 3l-2-7z" class="K"></path><path d="M203 282c1-1 2-1 2-2v-1l1 2v-2l1-2v2c0 2-1 4-2 6v1c-3 1-4 2-5 5v1c0 1-1 2-1 3v2 2 1c1 1 1 14 0 15v1l-1 3-3 6c0 2-1 3-1 4-1 1-2 1-3 2 3-6 6-12 6-18 1-4 1-8 1-12-1-4-6-13-5-16h2l2 2c1 0 2 0 4-1 1-1 1-2 2-3v-1z" class="W"></path><path d="M86 154v-1c3-2 6-3 10-5 18-9 38-7 58-5h0l-4 1-5-1c-7 0-14-1-21 0-1 0-2 1-3 2h0v1c-10 0-17 1-26 5l-9 3z" class="e"></path><path d="M415 79c11 3 22 11 28 21 0 1 1 1 1 2 0 2 0 3 1 5l-1 1c-3-5-6-9-10-13-1-1-3-2-4-3h-1v-1l-9-5c0-1-1-2-1-3-1 0-1 0-1-1l-3-3z" class="M"></path><path d="M407 72l1-2c2 2 4 8 7 9l3 3c0 1 0 1 1 1 0 1 1 2 1 3l-13-4c-4 2-8 3-12 3l-2-2-2-2c2 0 2 0 4-1l-1-2c1 0 2 1 3 1 1 1 1 1 2 1h3l1 1c0-1 0-1 1-1 2-2 3-5 3-7v-1z" class="y"></path><path d="M407 72c1 1 1 3 1 4 0 2 0 3-1 4-2 1-5 2-7 2h-1c-1-1-3-2-4-2l-1-2c1 0 2 1 3 1 1 1 1 1 2 1h3l1 1c0-1 0-1 1-1 2-2 3-5 3-7v-1z" class="Ac"></path><path d="M283 616h1l3 3 1-1c1 2 2 4 2 6h1l1 2v1l5 18c1 2 1 6 3 9h-1c-1-1-3-1-5-2l-8-28-3-8z" class="F"></path><path d="M283 616h1l3 3 1-1c1 2 2 4 2 6h1l1 2v1 1h-1c1 1 1 1 1 2 1 1 0 1 1 2v2l1 3v3c-2-2-2-4-2-7l-3-6v-1c0-1-1-1-3-2l-3-8z" class="J"></path><path d="M875 395c2 0 3 1 5 1h0c3 3 7 6 9 10l3 6c2 3 5 4 6 7l10 26c3 8 7 17 7 26h-2v-3c-1-4-1-7-2-11l-2-5v-2l-1-2c0-1-1-2-1-3-2-6-5-12-7-17l-1-3-1-1c-1-1-1-3-1-4v-1l-1-1c-3-3-5-6-8-10-1 0-2 0-2 1-2 0-2 1-3 3-1-1-1-3-1-4 0-2-1-2-1-3l1-3h0l1-2c-1-1-2-2-4-3-1 0-2 0-3-1l-1-1z" class="i"></path><path d="M116 453h1v4c0 2-1 5 0 7-1 5-2 10-2 15-1 11-1 21 0 32 0 3 0 6 1 9l-1 2c-2-12-3-24-3-35 0-12 0-23 4-34z" class="D"></path><path d="M765 136c9 2 18 3 26 4l16 2c-2 1-5 1-8 1-2-1-4-1-7-1h-1c-2 0-3-1-5-1l-1 1 2 1v1c2 1 5 1 7 1l1 1c-1 0-3 0-4 1v1c-2 0-6 1-8 0-1-1-3-1-5-2l-5-1-1-1 1-2-1-1-3-1-3-2c-1-1-1-1-3-2h0 0 2z" class="B"></path><path d="M781 142v-2c2 0 3 0 5 1l-1 1 2 1v1l-6-2z" class="D"></path><path d="M772 141c3 0 6 0 9 1l6 2c2 1 5 1 7 1l1 1c-1 0-3 0-4 1v1c-2 0-6 1-8 0-1-1-3-1-5-2l-5-1-1-1 1-2-1-1z" class="s"></path><path d="M773 142c2 1 3 1 5 2 1 1 5 2 5 4-1-1-3-1-5-2l-5-1-1-1 1-2z" class="B"></path><path d="M374 75h0l1-1c1-2 1-6 2-8l1 1c2 3 6 4 9 6 2 2 4 4 7 5l1 2c-2 1-2 1-4 1h0-4l-1-1c-1 1-1 1-2 1-1-1-1-2-3-3-3 0-6 1-9 3v1h-1l-2 1c-4 1-8 4-12 5 2-2 5-4 7-5 4-2 7-5 10-8z" class="f"></path><path d="M372 81c2-2 5-5 8-5 2 0 2 2 4 2h3 1l3 3h-4l-1-1c-1 1-1 1-2 1-1-1-1-2-3-3-3 0-6 1-9 3z" class="s"></path><path d="M374 75h0l1-1c1-2 1-6 2-8l1 1c2 3 6 4 9 6l-1 1c-2 0-5-2-6-2-2 0-4 3-6 3z" class="e"></path><path d="M123 464c1 1 1 3 1 5l-1 1v3l-1 1v2h0c-1 2 0 4-1 5v2 1 3c-2 11-3 22-3 33h-1-1c-1-3-1-6-1-9-1-11-1-21 0-32v2c0 1 1 1 0 3v1l1-1v-2c0-1 1-2 2-3-1 2-1 4-1 6h1c2-7 2-14 5-21z" class="C"></path><path d="M679 723c2-2 2-2 5-2l1 1c7 1 14 3 21 7l1 1 7 3c1 1 3 2 4 3l9 6c1 1 3 1 4 2s2 1 3 2c0 0 1 0 2 1 6 4 13 6 21 7h8l3-3 12 1-6 2c-3 2-7 2-11 2-7 0-15-1-22-4-9-4-17-10-26-15-11-7-23-13-36-14z" class="V"></path><path d="M648 67l2-1c1 1 1 2 1 3 0 3 1 4 3 7h0c-1 0-1 0-2-1-3 0-3 0-6 2 0 1-1 1-2 2l-3-1-2 1h-2l-2-3-3 6v2c-1 0-3 0-3 1h-1l-1-1c-2 0-3 0-5 1 0-1-1-2-1-3 1 0 1-1 2-1l-2-1h2c2-1 5-3 5-5l6-3c5-2 9-4 14-5z" class="z"></path><path d="M627 84l1-1c2-3 6-8 9-9l-2 2-3 6v2c-1 0-3 0-3 1h-1l-1-1z" class="AZ"></path><path d="M648 67l2-1c1 1 1 2 1 3 0 3 1 4 3 7h0c-1 0-1 0-2-1-3 0-3 0-6 2 0 1-1 1-2 2l-3-1-2 1h-2l-2-3 2-2 2-1c1-1 3-2 5-4h1c2-1 2-1 3-2z" class="K"></path><path d="M639 73c1-1 3-2 5-4 0 1-1 2 0 3 1-1 3-1 5 0h0c-1 0-2 0-3 1h-2c-1 1-2 2-3 2s-2-1-2-2z" class="B"></path><path d="M649 72l3 3c-3 0-3 0-6 2 0 1-1 1-2 2l-3-1 2-2c2-1 2-2 3-3s2-1 3-1z" class="y"></path><path d="M639 73c0 1 1 2 2 2s2-1 3-2h2c-1 1-1 2-3 3l-2 2-2 1h-2l-2-3 2-2 2-1z" class="e"></path><path d="M686 99l20 12c1 1 6 3 7 5l3 2c1-1 1-1 2 0v1c0 1 1 2 2 3l-1 1 1 1h1v4c-1 0-4-2-5-2-1-1-3-2-5-2v-2c-2-1-3-1-4-1l-8-5-3-2c-1-1-3-2-3-3l-3-4h0l4 2h1c0-1 0-1-1-1-2-2-4-5-6-7-1 0-1-1-2-2h0z" class="s"></path><path d="M699 116h0c-1-1-2-3-3-3h0 2c5 1 9 5 14 6 1-1 0-1 1-2v-1l3 2c1-1 1-1 2 0v1c0 1 1 2 2 3l-1 1 1 1h1v4c-1 0-4-2-5-2-1-1-3-2-5-2v-2c-2-1-3-1-4-1l-8-5z" class="f"></path><path d="M883 412c1-2 1-3 3-3 0-1 1-1 2-1 3 4 5 7 8 10l1 1v1c0 1 0 3 1 4l1 1 1 3c2 5 5 11 7 17 0 1 1 2 1 3l1 2v2l2 5v2l-2 1c-4-15-12-29-21-41l-5-7z" class="B"></path><path d="M161 143c1-1 4-2 6-2 0 0 2 1 3 2 1 0 3 1 5 1h1c-2 1-3 2-4 2l-2-1c-1 0-1 0-2 1v1h0v4c-1 1-1 0-2 1h-1l2 1v2 1c-2-1-3-2-4-4l-1-1v1c-5-3-11-4-17-4l-24-2v-1h0c1-1 2-2 3-2 7-1 14 0 21 0l5 1 4-1h0 7z" class="z"></path><path d="M145 143l5 1h0l-1 1c-2 1-4 1-6 0v-1l2-1z" class="AB"></path><path d="M165 152c0-1-1-2-2-2v-2c-1-1 0-1 0-2l3-2 2 2v1h0v4c-1 1-1 0-2 1h-1z" class="AQ"></path><path d="M161 143c1-1 4-2 6-2 0 0 2 1 3 2 1 0 3 1 5 1h1c-2 1-3 2-4 2l-2-1c-1 0-1 0-2 1l-2-2v-1h0c-3 1-4 0-6 1h0c-1 0-2 0-3-1h4z" class="p"></path><path d="M828 305l1 1c1 5 1 10 3 14 2 6 6 11 9 16 7 11 12 24 19 35v2c3 4 6 8 8 12 1 1 1 3 2 5s4 4 5 5l1 1c1 1 2 1 3 1 2 1 3 2 4 3l-1 2h0-1c0 1-1 1-1 1-1-1-1-1-3-1h-1c0-1 0-2-1-3-3-1-4-2-6-4s-2-5-4-7c0-2-1-3-2-5-2-4-5-8-8-12v-1c-3-5-6-10-8-15-1-2-2-4-2-6v-1h1c-1-2-2-3-3-4-1-7-7-13-10-18s-6-15-5-21z" class="i"></path><path d="M855 371h1l2 2 2 3 2 4c1 2 2 3 3 5l2 3c1 1 1 2 2 3 0-2-1-3-1-5l-2-2v-1l-2-2c-1-1 0-1-1-2 0-1-1-1-2-2v-1h-1v-1l-1-1c-1-1-1-2-1-3h0l2 2c3 4 6 8 8 12 1 1 1 3 2 5s4 4 5 5l1 1c1 1 2 1 3 1 2 1 3 2 4 3l-1 2h0-1c0 1-1 1-1 1-1-1-1-1-3-1h-1c0-1 0-2-1-3-3-1-4-2-6-4s-2-5-4-7c0-2-1-3-2-5-2-4-5-8-8-12z" class="D"></path><path d="M875 399c2 0 2 0 4 1l-2 2h-1c0-1 0-2-1-3z" class="V"></path><path d="M475 204c1 1 4 2 6 2 6 2 13 1 18-2 10-5 13-17 15-27h1v1l-1 1-2 10c0 4-1 7-2 10h0c-1 1-2 1-3 2s-1 1-2 1h-1l-6 6h-1c-1 0-3 1-4 1l-2 1h-1-1c-1 1-2 2-2 4h-2-4l-1-1-1 1h-4c0 1-1 2-2 3-3-1-7-3-10-3-2-1-4-1-5-2 0-1 0-2 1-3 0 1 0 1 1 2v-1c1 0 2 0 2 1l3-4 3-7v1c1-3 3-5 4-6 0 2-1 3 0 5s1 3 3 4z" class="q"></path><path d="M512 189c0 4-1 7-2 10h0c-1 1-2 1-3 2s-1 1-2 1c2-4 5-8 7-13z" class="AD"></path><path d="M468 201c1-3 3-5 4-6 0 2-1 3 0 5s1 3 3 4h-2c-1 1-1 2-2 3-1-2-2-1-3-2l1-3-1-1z" class="b"></path><path d="M462 211l3-4 3 3h3c2 0 3-1 5-2h1c0 1 1 1 2 1 1 1 1 0 2 1h5 3c-1 1-2 2-2 4h-2-4l-1-1-1 1h-4c0 1-1 2-2 3-3-1-7-3-10-3-2-1-4-1-5-2 0-1 0-2 1-3 0 1 0 1 1 2v-1c1 0 2 0 2 1z" class="AO"></path><path d="M458 212c0-1 0-2 1-3 0 1 0 1 1 2v-1c1 0 2 0 2 1 2 0 3 0 5 1 1 0 4 0 5 1 1 0 2 1 3 1 0 1-1 2-2 3-3-1-7-3-10-3-2-1-4-1-5-2z" class="AN"></path><path d="M615 74c1-1 2-3 4-3 0 1 0 1 1 2v2 2c0 1 1 2 1 3l2 1c-1 0-1 1-2 1 0 1 1 2 1 3h-3l-5 1c-3 1-12 4-14 7h0l-1 1c-6 5-10 8-14 15-1 3-2 5-4 8h0c-2-3 2-9 1-12 4-9 10-16 18-21 5-3 11-6 15-10z" class="f"></path><path d="M615 74c1-1 2-3 4-3 0 1 0 1 1 2v2 2c0 1 1 2 1 3l2 1c-1 0-1 1-2 1 0 1 1 2 1 3h-3l-5 1c1-1 1-1 3-1 0-1 0-1 2-1 0-1-2-4-2-5v-1-1c-1-1-1-2-2-3z" class="AE"></path><path d="M621 82h-2c-1-1 0-3 0-4l1-1c0 1 1 2 1 3l2 1c-1 0-1 1-2 1z" class="AR"></path><path d="M744 598h1c2 0 4 0 6-1-4 5-7 9-9 15-1 8-5 16-7 23-3 14-8 29-7 44h-2c0-2 1-5 0-7 0-2-1-2-1-4 0-5 1-10 1-15l1-1c0-6 0-12 1-17s1-10 3-15c1-3 1-5 2-8l2 1v-3l1 1 1 1c1-1 4-4 5-6s4-4 5-6l-1-1c-1 0-1 0-2-1z" class="J"></path><path d="M731 620c1-3 1-5 2-8l2 1-1 2c-1 3-1 6-2 8s0 3 0 5c0 3-2 6-3 8l-1-1c1-5 1-10 3-15z" class="H"></path><path d="M735 635c-1-1-1-2-1-3l1-1h-1 0c1-6 3-13 6-19h0l-1-1 3-3v1h0v3h0c-1 8-5 16-7 23z" class="K"></path><path d="M726 679h2c0 8-1 18 1 26 2 7 5 13 8 20 3 6 6 11 11 16s10 7 16 10h-1c-3 1-11-3-15-4l-5-5c-2-1-5-5-7-7l-4-5c-5-8-9-16-13-24l1-1h1l-1-1 1-1h1l1-1c1 1 1 2 2 4h0c2-2 2-4 2-7v-4-2c-1-5-1-9-1-14z" class="V"></path><path d="M727 699c0 2 0 3 1 4 1 6 3 11 5 16l3 6h-1c-1-1-2-1-3-2-1 0 0 0-1-1l-3-6-2 1-2-4v-1c-1-2-1-3-1-5h0l2-1h0c2-2 2-4 2-7z" class="k"></path><path d="M731 722v-4l1 2h0l1-1 3 6h-1c-1-1-2-1-3-2-1 0 0 0-1-1zm-8-15c1 1 2 2 2 3s0 2 1 2c1 2 1 3 2 4l-2 1-2-4v-1c-1-2-1-3-1-5z" class="AG"></path><path d="M723 702c1 1 1 2 2 4l-2 1h0c0 2 0 3 1 5v1l2 4 2-1 3 6c1 1 0 1 1 1 1 1 2 1 3 2h1l1 4c0 1 1 3 2 4v1h-1l-2 1-4-5c-5-8-9-16-13-24l1-1h1l-1-1 1-1h1l1-1z" class="Af"></path><path d="M723 702c1 1 1 2 2 4l-2 1-1-1-1 1v-1h1v-3l1-1z" class="AG"></path><path fill="#a4404b" d="M726 717l2-1 3 6c1 1 0 1 1 1 1 1 2 1 3 2h1l1 4c-2-1-3-3-4-4-2 0-3-1-4-2l-3-6z"></path><defs><linearGradient id="A" x1="119.571" y1="429.742" x2="129.429" y2="439.758" xlink:href="#B"><stop offset="0" stop-color="#65100d"></stop><stop offset="1" stop-color="#991f20"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M140 405h4v1c0 1-1 2-2 4h2c0-1 1-2 2-3h1l1 2 1-1 1 1h3c-1 2-3 3-3 5v1c-4 1-7 4-10 7-8 8-14 19-19 30l-4 12c-1-2 0-5 0-7v-4h-1l8-20c3-8 5-14 11-20 0-1 1-2 1-3 2-2 3-3 4-5z"></path><path d="M140 405h4v1c0 1-1 2-2 4-2 1-4 4-6 5v1c-1 0-2 0-3 1h0c0-1 1-2 2-4h0c0-1 1-2 1-3 2-2 3-3 4-5z" class="V"></path><path d="M142 410h2c0-1 1-2 2-3h1l1 2-8 8v-2l-1 2c-2 1-4 3-6 3 0-1 2-3 3-5 2-1 4-4 6-5z" class="k"></path><defs><linearGradient id="C" x1="133.563" y1="424.229" x2="138.327" y2="427.507" xlink:href="#B"><stop offset="0" stop-color="#c22729"></stop><stop offset="1" stop-color="#ea3940"></stop></linearGradient></defs><path fill="url(#C)" d="M149 408l1 1h3c-1 2-3 3-3 5v1c-4 1-7 4-10 7-8 8-14 19-19 30h-1v-2c0-1 2-3 2-5 3-7 5-13 10-19 2-3 6-6 8-9l8-8 1-1z"></path><defs><linearGradient id="D" x1="242.775" y1="134.215" x2="246.654" y2="146.199" xlink:href="#B"><stop offset="0" stop-color="#c31d1f"></stop><stop offset="1" stop-color="#f04048"></stop></linearGradient></defs><path fill="url(#D)" d="M257 135l23-8 4 1c-1 2-2 3-3 4s-1 1-2 3h1l3 1-8 1-14 3c-1 1-4 1-6 2h0-2v1h2c-2 0-3 0-4 1h-1l-2 1c-2 1-4 2-7 4-1 0-1 1-2 1h-4c-4-1-7-1-11-2l-2-1c-3 0-5-1-7-1h-1c-1-1-2-1-2-1h0l2-1c1-1 5-1 6-1l37-8z"></path><path d="M276 131c2 0 2 0 3 1-1 1-3 2-4 3l-1 1 1 1-14 3v-2l11-3c2-1 3-2 5-3l-1-1z" class="s"></path><path d="M220 143l37-8h1c-6 2-32 11-37 8h-1z" class="M"></path><path d="M257 135l23-8 4 1c-1 2-2 3-3 4s-1 1-2 3h1l3 1-8 1-1-1 1-1c1-1 3-2 4-3-1-1-1-1-3-1-3 0-5 0-8 1l-10 3h-1z" class="u"></path><path d="M603 154c2 1 3 2 4 4 1 3 2 5 2 7s-1 4-2 6h1v2h1c1-1 1 0 2 1h3v-2c1 0 2 1 3 0 3-3 6-5 9-9l1 1c1-1 2-2 2-3l1 1c-1 2-4 5-5 7l-7 10c-1 1-6 5-7 7l-4 3-7 5c-4 0-7-3-10-6l-1-2c-1-2-1-4-2-6v4-6l1-1 1 1 2 1c1 1 2 0 4 0 4-2 9-7 11-11 2-5-1-10-3-14z" class="v"></path><path d="M598 190c1 0 2 1 4 1 1 0 3-2 4-3-2 0-4 2-7 2v-1c3 0 6-3 9-3 0 1 0 0-1 1s0 1 0 2l-7 5c-4 0-7-3-10-6l1-1v1c2 1 4 2 6 2h1z" class="AH"></path><path d="M626 163l1 1c-1 2-3 4-4 6l-1 1-3 4h-1c-1 1 0 1-1 2 0 0-1 0-2 1 0 1-1 2-2 2l-2-1c0 1 0 1-1 1s-2-1-3-1v1c-1-1-1-1-1-2h-2c0-1 0-1 1-2l-1-1 1-1c1-1 1-2 2-3h0 1v2h1c1-1 1 0 2 1h3v-2c1 0 2 1 3 0 3-3 6-5 9-9z" class="r"></path><path d="M605 176h1l3-1 1 1c-1 1-2 1-3 2v1 1c-1-1-1-1-1-2h-2c0-1 0-1 1-2z" class="AP"></path><path d="M607 171h1v2h1c1-1 1 0 2 1 0 0-1 0-2 1l-3 1h-1l-1-1 1-1c1-1 1-2 2-3h0z" class="g"></path><path d="M603 154c2 1 3 2 4 4 1 3 2 5 2 7s-1 4-2 6h0c-1 1-1 2-2 3l-1 1 1 1c-1 1-1 1-1 2h2c0 1 0 1 1 2v-1c1 0 2 1 3 1s1 0 1-1l2 1c-2 2-2 3-4 3-2-1-1-1-3-1 0 1 0 2-1 2-2 2-6 3-9 4h-1l1 1c1 0 1 0 2 1h-1c-2 0-4-1-6-2v-1l-1 1-1-2c-1-2-1-4-2-6v4-6l1-1 1 1 2 1c1 1 2 0 4 0 4-2 9-7 11-11 2-5-1-10-3-14z" class="r"></path><path d="M604 178h2c0 1 0 1 1 2v-1c1 0 2 1 3 1s1 0 1-1l2 1c-2 2-2 3-4 3-2-1-1-1-3-1v-1c-3 2-5 0-7 1h-1-1c1-1 0-1 1-2h0c1 0 1-1 2-2l1 1-2 2c2 0 2-2 3-3h2z" class="AS"></path><path d="M587 184v-6l1-1 1 1-1 1h1c0 1 0 3 1 4l1-1v1 1h1l1-1h0c1-2 1-1 2-1s2-2 3-2h0c-1 1 0 1-1 2h1 1c2-1 4 1 7-1v1c0 1 0 2-1 2-2 2-6 3-9 4h-1l1 1c1 0 1 0 2 1h-1c-2 0-4-1-6-2v-1l-1 1-1-2c-1-2-1-4-2-6v4z" class="AD"></path><path d="M731 124c2-1 2-3 4-4 1-1 3 0 3 0-1 1-2 2-2 5 1 1 3 2 5 3l-2 1c1 1 1 1 2 1v1l3 1h0l22 6 3 2 3 1 1 1-1 2 1 1h-4l-2 3h-2c-2-1-4-1-7-2l-14-3 2-1c-1 0-2 0-3-1l-1-1c-3 0-6-1-9-2 0-1-1-1-1-2-2-1-4-1-5-2l-2-3-2-1-2-1v-1-4c2 1 2 1 4 1l4-2c0 1 1 1 2 1z" class="a"></path><path d="M725 125l4-2c-1 2-2 3-2 4l-1-1c-2 1-3 1-3 2v2l-2-1v-1-4c2 1 2 1 4 1z" class="AG"></path><path d="M744 133l19 7h-1c-1 1-4 0-6-1h-2c-1-1-2-1-3-2l-8-3 1-1z" class="B"></path><path d="M731 124c2-1 2-3 4-4 1-1 3 0 3 0-1 1-2 2-2 5 1 1 3 2 5 3l-2 1h-2v1l3 1c1 1 2 1 2 2h0c-2-1-4-1-6-2-1-2-2-2-3-3s-2-1-3-2c0-1 0-1 1-2z" class="V"></path><path d="M744 132h0l22 6 3 2 3 1 1 1-1 2c-2-1-4-2-5-2l-4-2h0l-19-7v-1z" class="H"></path><path d="M763 140h3c2 0-1 0 1 0h1 1l3 1 1 1-1 2c-2-1-4-2-5-2l-4-2z" class="E"></path><path d="M725 131c4 1 7 3 11 4l4 1h0c1 1 2 1 2 2 1 0 3 0 4 1 3 0 6 2 9 2l3 3-16-4c-3 0-6-1-9-2 0-1-1-1-1-2-2-1-4-1-5-2l-2-3z" class="D"></path><path d="M742 140l16 4-3-3c2 0 5 0 7 1h5c1 0 3 1 5 2l1 1h-4l-2 3h-2c-2-1-4-1-7-2l-14-3 2-1c-1 0-2 0-3-1l-1-1z" class="AR"></path><path d="M755 141c2 0 5 0 7 1l3 3-7-1-3-3z" class="G"></path><path d="M762 142h5c1 0 3 1 5 2l1 1h-4-4l-3-3z" class="m"></path><defs><linearGradient id="E" x1="716.815" y1="135.99" x2="701.205" y2="114.139" xlink:href="#B"><stop offset="0" stop-color="#d92a2c"></stop><stop offset="1" stop-color="#f04f59"></stop></linearGradient></defs><path fill="url(#E)" d="M684 113h0c0-1 0 0 1-1 1 0 0 0 1 1h4c2 0 3 1 4 1h2l3 2 8 5c1 0 2 0 4 1v2c2 0 4 1 5 2 1 0 4 2 5 2v1l2 1 2 1 2 3c1 1 3 1 5 2 0 1 1 1 1 2h-1c-4-2-9-3-13-4 0 0-1 1-1 2l3 2h-1c-2-1-4-1-6-2-1 0-2 0-3 1h0l-1 1v3l-1 1-2 2-3-2h0l-1 1h0l-2-1v-4l-2-4c-2-4-5-9-8-11-2-1-6-5-7-7v-3z"></path><path d="M712 127c-1-1-3-1-4-2l1-1h2c1 1 1 1 1 3z" class="AE"></path><path d="M721 129l2 1 2 1 2 3c-3-1-4-1-6-3v-2z" class="s"></path><path d="M711 124c2 0 4 1 5 2l3 3h0-2c-1 0-1 0-1-1-1 0-4-1-4-1 0-2 0-2-1-3z" class="z"></path><path d="M684 113h0c0-1 0 0 1-1 1 0 0 0 1 1h4c2 0 3 1 4 1h2l3 2 8 5h-3c-1 0-4-1-5-2s-1-1-3-2c1 3 3 4 3 6h0v2h-1v1h-1c-1-1-4-4-6-5v2c-2-1-6-5-7-7v-3z" class="AE"></path><path d="M691 121c-1-1-3-2-4-3s-1-1 0-3l1 1c3-1 6 0 8 1 1 3 3 4 3 6h0v2h-1v1h-1c-1-1-4-4-6-5z" class="AZ"></path><path d="M688 116c3-1 6 0 8 1 1 3 3 4 3 6-4-2-8-5-11-7z" class="AI"></path><path d="M691 121c2 1 5 4 6 5h1v-1h1v-2c5 3 9 6 13 8l7 3s-1 1-1 2l3 2h-1c-2-1-4-1-6-2-1 0-2 0-3 1h0l-1 1v3l-1 1-2 2-3-2h0l-1 1h0l-2-1v-4l-2-4c-2-4-5-9-8-11v-2z" class="AB"></path><path d="M712 131l7 3s-1 1-1 2c-2-1-3-1-5-1-5-1-2 0-5 2 0-2-1-3-2-5 2 1 3 1 4 1l2-2z" class="Ae"></path><path d="M699 123c5 3 9 6 13 8l-2 2c-1 0-2 0-4-1-1 0-2-1-3 0-2-1-2-2-3-3l-3-3h1v-1h1v-2z" class="Ac"></path><path d="M150 415h2 3l1 1c2 0 3 2 4 3l-1 1v1 1l1 1c3 1 6 2 9 4l3 2v1c-8-3-14-4-22-1-11 3-18 10-23 20l-1 2 1 1-4 11v1c-3 7-3 14-5 21h-1c0-2 0-4 1-6-1 1-2 2-2 3v2l-1 1v-1c1-2 0-2 0-3v-2c0-5 1-10 2-15l4-12c5-11 11-22 19-30 3-3 6-6 10-7z" class="AJ"></path><path d="M152 415h3l1 1c2 0 3 2 4 3l-1 1v1 1c-3-2-4-5-7-7z" class="F"></path><path d="M126 451l1 1-4 11v1c-3 7-3 14-5 21h-1c0-2 0-4 1-6 0-1 0-4 1-5 1-7 3-17 7-23z" class="Q"></path><path d="M264 563c2 1 4 2 5 4l2 2c2 2 4 3 4 6 2 0 2 0 3 1 3 4 5 8 7 11 1 3 3 5 4 8 2 3 5 8 5 11l3 11c-2 1-3 3-3 4-2 3-2 3-2 5l-1-2h-1c0-2-1-4-2-6l-1 1-3-3h-1l-15-41v-2c0-1-1-2-1-3-1-3-2-5-3-7z" class="O"></path><path d="M293 619c0-2 1-4 0-6 0-2 0-5 1-7l3 11c-2 1-3 3-3 4-1 0-1-1-1-2z" class="H"></path><path d="M279 585c0-2-2-4-2-5h1c1 3 3 5 5 8 1 2 1 5 2 7s2 4 2 7l6 17c0 1 0 2 1 2-2 3-2 3-2 5l-1-2h-1c0-2-1-4-2-6l-4-12 2-2-5-15c0-2-1-3-2-4z" class="S"></path><path d="M284 606l2-2 5 20h-1c0-2-1-4-2-6l-4-12z" class="h"></path><path d="M268 573h1c0 2 1 3 1 5h0 1c0 4 4 6 5 10h0c0-2-1-3-1-4h1c0-1 0-1-1-1 2 0 2 2 3 3l1-1h0c1 1 2 2 2 4l5 15-2 2 4 12-1 1-3-3h-1l-15-41v-2z" class="H"></path><path d="M284 606c0-2-1-4-2-5-1-4-2-8-2-11l1-1 5 15-2 2z" class="B"></path><path d="M635 131c2 0 3-1 5 0l1 1v5h0v4c-2 5-4 12-8 16-1 1-1 3-3 5l-1-1c0 1-1 2-2 3l-1-1c-3 4-6 6-9 9-1 1-2 0-3 0v2h-3c-1-1-1-2-2-1h-1v-2h-1c1-2 2-4 2-6s-1-4-2-7h1l1-1 2 2h5v-2l-1-1c6-3 8-10 10-15 1-3 1-6 2-9v1h1c0 2 1 2 2 3 2-2 4-3 5-5z" class="AU"></path><path d="M627 159c1 1 2 1 3 1l1-2c1-1 1-1 2-1-1 1-1 3-3 5l-1-1c0 1-1 2-2 3l-1-1h0c0-1 1-2 1-4z" class="t"></path><path d="M635 145h2l-1 1v3s-1 2-1 3c-1 0-2 0-2 1-1 2-1 2-2 3-1-1-1-1-1-2 1-1 1-1 1-2v-1c2-1 1-2 2-4 1 0 1-1 2-2z" class="w"></path><path d="M620 165c1-2 4-3 5-5-1-1 0-1-1-1l1-2c1 0 1 0 2-1l1-1v1 1l-1 2h0c0 2-1 3-1 4h0c-3 4-6 6-9 9-1 1-2 0-3 0 2-1 3-2 4-4 0-1 0-1-1-2 2 0 2 0 3-1z" class="g"></path><path d="M635 131c2 0 3-1 5 0l1 1v5h0-1c0 1 0 2-1 3v-1-3l-1 1c0 1 0 0-1 1v1c-1 1-2 1-3 2 0-1 1-2 1-3-2 1-3 2-3 4v1 2h-1c0-2-1-2-1-3s2-4 2-5v-1c-2 0-3 3-4 5h-2-1c1-3 1-6 2-9v1h1c0 2 1 2 2 3 2-2 4-3 5-5z" class="P"></path><path d="M635 131c2 0 3-1 5 0l1 1v5h0-1c0 1 0 2-1 3v-1-3h0v-2l-1-1-1 1-1-1-1-2z" class="d"></path><path d="M608 158c1 1 2 2 4 3l1 1c1-1 1 0 1-1l2 1c2 0 3 2 4 3-1 1-1 1-3 1 1 1 1 1 1 2-1 2-2 3-4 4v2h-3c-1-1-1-2-2-1h-1v-2h-1c1-2 2-4 2-6s-1-4-2-7h1z" class="P"></path><g class="w"><path d="M616 162c2 0 3 2 4 3-1 1-1 1-3 1v-1c-2-1-2-1-3-1l-1-1c1 0 2 0 2-1h1z"></path><path d="M609 165c0 2 0 4 1 5h2c1-1 1-1 3-1h1v-1l-2-1v-1h3c1 1 1 1 1 2-1 2-2 3-4 4v2h-3c-1-1-1-2-2-1h-1v-2h-1c1-2 2-4 2-6z"></path></g><path d="M196 145l18-1-2 1h0s1 0 2 1h1c2 0 4 1 7 1l2 1c4 1 7 1 11 2h4c2 0 3 1 5 2l1 1-3 1h-6l-10 1h-8l-1 2c-1 0-2-1-2-1-1-1-2-1-2-1-5 1-9 1-14 1-11-1-21-2-32-4v1l-2-1h1c1-1 1 0 2-1v-4h0v-1c1-1 1-1 2-1l2 1c1 0 2-1 4-2l20 1z" class="AR"></path><path d="M168 147c1 0 2 1 3 1v3l-1 1h-1-2v1l-2-1h1c1-1 1 0 2-1v-4h0z" class="Ad"></path><path d="M173 148l10 2c4 0 8 0 11 1h4c-3 1-6 1-8 1s-4-1-5 0h-2c-4-1-6-1-10-4z" class="AB"></path><path d="M176 144l20 1h-2v2l-2 1h2l4 1 6 2-1 1h-1l-4-1h-4c-3-1-7-1-11-1l-10-2h-2c-1 0-2-1-3-1v-1c1-1 1-1 2-1l2 1c1 0 2-1 4-2z" class="D"></path><path d="M196 145l18-1-2 1h0s1 0 2 1h1c2 0 4 1 7 1l2 1c4 1 7 1 11 2h4c2 0 3 1 5 2l1 1-3 1h-6l-10 1c1-1 1-1 2-1h0l1-2h-16c-2 1 0 0-2 0h-1l-6-1-6-2-4-1h-2l2-1v-2h2z" class="B"></path><path d="M235 152h6v1c-2 0-3 0-5 1l-10 1c1-1 1-1 2-1h0l1-2h6z" class="AI"></path><path d="M204 148h1c2 1 3 1 5 1 1 0 0 0 1 1l1-1c1 0 1 0 1 1l-3 2-6-1-6-2 6-1z" class="h"></path><path d="M213 150c2 0 3 0 4 1l1-1h0 0c4 1 8-1 12 1 1 1 2 1 3 1h2-6-16c-2 1 0 0-2 0h-1l3-2z" class="M"></path><path d="M196 145l18-1-2 1h0s1 0 2 1h1c2 0 4 1 7 1l2 1-2 1h0c-1 0-1 0-2-1h-1v1h-3c-2-1-8-2-11-1h0-1l-6 1-4-1h-2l2-1v-2h2z" class="a"></path><path d="M194 148c0-1 1-1 2-2 3 0 5 1 8 2l-6 1-4-1z" class="e"></path><defs><linearGradient id="F" x1="192.563" y1="317.157" x2="201.941" y2="320.028" xlink:href="#B"><stop offset="0" stop-color="#130201"></stop><stop offset="1" stop-color="#290202"></stop></linearGradient></defs><path fill="url(#F)" d="M191 331c1-1 2-1 3-2 0-1 1-2 1-4l3-6 1-3v-1c1-1 1-14 0-15v-1-2-2c0-1 1-2 1-3v-1c1-3 2-4 5-5-1 4 0 7 0 10 0 4-1 9-2 13 2 3 1 6 4 8 1 0 2 0 2 1 1 1 2 3 2 4l2-2v4c0 1 0 2-1 3v1 1c0 1 0 3-1 4l1 1-4 4c-1 1-2 2-2 3l-1-1h-1v-2c-2 0-1 1-3 2 0 1-1 2-2 3l-2-2c-1 1-1 1-2 1h0v3l-1 3c-1-2-2-2-3-3-2 0-4 2-4 3v1h-1-1v1c-1 1-1 0-1 1h0c-1 2-2 5-3 7h0c-1 1-1 2-2 2 0-3 2-5 3-7 1-4 2-8 3-11 1-4 4-8 6-11z"></path><path d="M200 331l-1 3-3 3-3 2v-2c1-2 1-3 3-4 1 0 2-1 3-1l1-1z" class="L"></path><path d="M193 339l3-2 1 2c-1 1-2 2-2 3v3l-1 3c-1-2-2-2-3-3s1-4 2-6z" class="H"></path><path d="M203 309c2 3 1 6 4 8-1 1-1 2-2 3s0 2-1 3c0 0-1 0-1 1 0 0 0 1-1 2s-1 2-2 3v-1c1-7 2-13 3-19z" class="L"></path><path d="M207 317c1 0 2 0 2 1 1 1 2 3 2 4l2-2v4c0 1 0 2-1 3v1 1c0 1 0 3-1 4l1 1-4 4c-1 1-2 2-2 3l-1-1h-1v-2c-2 0-1 1-3 2 0 1-1 2-2 3l-2-2c-1 1-1 1-2 1h0c0-1 1-2 2-3l-1-2 3-3 1-3v-2c1-1 1-2 2-3s1-2 1-2c0-1 1-1 1-1 1-1 0-2 1-3s1-2 2-3z" class="h"></path><path d="M201 340l1-1c1-2 2-4 4-5l1 1 1 3c-1 1-2 2-2 3l-1-1h-1v-2c-2 0-1 1-3 2z" class="G"></path><path d="M199 334l2-1c0-2 1-3 3-5 0 1 0 1 1 2v-4h1 1 1-1v1c0 1 1 1 1 2h1v1l-1 2h-1c0-1 0-2-1-3-1 1-1 1-1 2s0 2-1 3v1h-1-1c-1 1-1 2-1 3l-1 1h-1c-1-1-1-1-2 0l-1-2 3-3z" class="F"></path><path d="M207 317c1 0 2 0 2 1 1 1 2 3 2 4l2-2v4c0 1 0 2-1 3v1l-2 1-1 1v-1h-1c0-1-1-1-1-2v-1h1-1-1-1v4c-1-1-1-1-1-2-2 2-3 3-3 5l-2 1 1-3v-2c1-1 1-2 2-3s1-2 1-2c0-1 1-1 1-1 1-1 0-2 1-3s1-2 2-3z" class="H"></path><path d="M213 320v4c0 1 0 2-1 3v1l-2 1-1 1v-1c0-1 0-2 1-3s1-2 1-4l2-2z" class="B"></path><path d="M259 557l1-2h1 1 1l4 3c2 2 4 4 6 5l7 11c2 2 4 4 6 7 2 4 5 8 7 12l2 2 1 2 4 3c2 0 3 2 5 4l2 1h0l3 4v2h0l1 3c0 3 1 6 1 10 0 2 1 7 0 9h-1v2l-1-1h0c-1-1-2-1-3 0l1 7v1c-1 2 0 5-1 7-2-3-1-5-2-8-1-2-2-5-2-8l-2-7h0v-3c-1-2-1-3-2-4v-1l-1-1h-1l-3-11c0-3-3-8-5-11-1-3-3-5-4-8-2-3-4-7-7-11-1-1-1-1-3-1 0-3-2-4-4-6l-2-2c-1-2-3-3-5-4l-3-3c-1-1-1-2-2-3z" class="R"></path><path d="M293 593l2 2 1 2c2 4 3 10 6 12l6 16 3 8v2l-1-1h0c-1-1-2-1-3 0-1-7-4-13-6-20l-8-21z" class="AA"></path><path d="M296 597l4 3c2 0 3 2 5 4l2 1h0l3 4v2h0l1 3c0 3 1 6 1 10 0 2 1 7 0 9h-1l-3-8-6-16c-3-2-4-8-6-12z" class="S"></path><path d="M310 611l1 3c0 3 1 6 1 10 0 2 1 7 0 9h-1l-3-8 2-1c0-2-1-6 0-8v-5z" class="H"></path><path d="M296 597l4 3c2 0 3 2 5 4l2 1h0l3 4v2l-7-7-1 1v4c-3-2-4-8-6-12z" class="AB"></path><defs><linearGradient id="G" x1="674.947" y1="94.564" x2="664.13" y2="105.878" xlink:href="#B"><stop offset="0" stop-color="#e23338"></stop><stop offset="1" stop-color="#f25b61"></stop></linearGradient></defs><path fill="url(#G)" d="M652 75c1 1 1 1 2 1l2 2c2 3 6 5 9 7l16 10c1 1 4 2 5 4h0c1 1 1 2 2 2 2 2 4 5 6 7 1 0 1 0 1 1h-1l-4-2h0l3 4c0 1 2 2 3 3h-2c-1 0-2-1-4-1h-4c-1-1 0-1-1-1-1 1-1 0-1 1h0c-7-5-13-10-21-14l-16-9-4-1 1-2v-1l-2-2v-2l2-2v-1c1-1 2-1 2-2 3-2 3-2 6-2z"></path><path d="M644 87c1 0 2 1 3 2v1l-4-1 1-2z" class="AI"></path><path d="M661 91l4 1 6 2 15 9v1c-3 0-7-3-9-4-4-1-8-2-11-4-2-1-4-2-6-4l1-1z" class="y"></path><path d="M658 84l5 3 2-2 16 10c1 1 4 2 5 4h0c1 1 1 2 2 2 2 2 4 5 6 7 1 0 1 0 1 1h-1l-4-2c-1-2-3-3-4-4l-15-9-6-2c0-3-5-5-7-8z" class="AE"></path><path d="M658 84l5 3 9 6c0 1 0 1-1 1l-6-2c0-3-5-5-7-8z" class="AF"></path><path d="M652 75c1 1 1 1 2 1l2 2c2 3 6 5 9 7l-2 2-5-3c2 3 7 5 7 8l-4-1-6-3h-1-1-2c-1 0-1 0-2-1-2-1-3-1-5-1l-2-2v-2l2-2v-1c1-1 2-1 2-2 3-2 3-2 6-2z" class="z"></path><path d="M652 75c1 1 1 1 2 1l2 2-4 1c-1 0-3-1-4-1l-2-1c3-2 3-2 6-2z" class="f"></path><path d="M655 88c-1-2-2-4-2-5l1-1c2 0 3 1 4 2 2 3 7 5 7 8l-4-1-6-3z" class="AB"></path><path d="M646 77l2 1 1 2c2 2 2 2 3 4l1 3 1 1h-1-2c-1 0-1 0-2-1-2-1-3-1-5-1l-2-2v-2l2-2v-1c1-1 2-1 2-2z" class="AF"></path><path d="M646 77l2 1 1 2h-5v-1c1-1 2-1 2-2z" class="AI"></path><path d="M642 84c2-2 3-2 5-2 2 2 2 4 6 5l1 1h-1-2c-1 0-1 0-2-1-2-1-3-1-5-1l-2-2z" class="AQ"></path><path d="M722 635c0-1 1-1 1-2 1-2 1-5 2-6 0-1 2-2 2-2 0-1 0-1 1-2v-2c1 0 1-1 3-1-2 5-2 10-3 15s-1 11-1 17l-1 1c0 5-1 10-1 15 0 2 1 2 1 4 1 2 0 5 0 7 0 5 0 9 1 14v2 4c0 3 0 5-2 7h0c-1-2-1-3-2-4l-1 1c1-2 0-2 0-4v-1l-1-4-1-3c-1 1-2 1-3 2l-2-17h0c-1-2-1-4-1-6v-4-5h0l-1-15 2-1c0-3 0-4 2-6l-1 6c1 2 1 4 1 6h0c1-2 2-3 3-4l1-5c0-2 1-5 1-7z" class="m"></path><path d="M726 653c-1 0-1 0-2-1 0-1 0-1 1-2l2 2-1 1z" class="B"></path><path d="M714 661c2 2 2 3 3 5v1l-1 1v-1c-1-1-1-1-2-1v-5z" class="i"></path><path d="M720 647l1-5v9 7c0 2-2 4-3 5-1-1 0-1-1-2v-1c1 0 1-1 1-1l1-2v-4c1-2 1-3 1-6z" class="e"></path><path d="M717 666l1-1c1 2 1 4 1 6s0 4-1 6c-1 0-2 0-3-1h0c-1-2-1-4-1-6v-4c1 0 1 0 2 1v1l1-1v-1z" class="M"></path><path d="M717 666l1-1c1 2 1 4 1 6l-3-2v1l-1-1v7c-1-2-1-4-1-6v-4c1 0 1 0 2 1v1l1-1v-1zm4 28h1v-5h0l1 5v-5c1 1 1 0 1 1 0 2 0 3 1 5h0 1v-1l1 1v4c0 3 0 5-2 7h0c-1-2-1-3-2-4l-1 1c1-2 0-2 0-4v-1l-1-4z" class="p"></path><path fill="#a4404b" d="M722 698h1c1 0 1 1 1 2v1c-1 0-1 0-1 1l-1 1c1-2 0-2 0-4v-1z"></path><path d="M715 676c1 1 2 1 3 1v3l1-1v-1l1 1h0c0 1 0 2 1 3v1h-1v-1l-1 1c0 2 0 4 1 7v1c-1 1-2 1-3 2l-2-17z" class="B"></path><path d="M713 646l2-1c0-3 0-4 2-6l-1 6c1 2 1 4 1 6h0c1-2 2-3 3-4 0 3 0 4-1 6v4l-1 2-1-1c-1 0-2 1-3 2v1l-1-15z" class="M"></path><path d="M717 658c-2-4-1-9-1-13 1 2 1 4 1 6h0c1-2 2-3 3-4 0 3 0 4-1 6v4l-1 2-1-1z" class="j"></path><path d="M514 179l1-1c3 9 4 18 11 25v1l2 2c1 0 2 1 2 1-1 1-1 1-2 1h-1l-1-1-1 2h0v1c2 0 4 1 5 2-2 0-5 0-7-1l-2 2 1 1-1 2h0l-3 3-3-2c0-1-1-2-1-3-1-1-1-2-2-3-1 2-3 4-4 5-2 2-4 2-7 3s-7 1-10 2c-1-1-3 0-4 0-3-1-7-2-11-3 0-1-2-1-3-1 1-1 2-2 2-3h4l1-1 1 1h4 2c0-2 1-3 2-4h1 1l2-1c1 0 3-1 4-1h1l6-6h1c1 0 1 0 2-1s2-1 3-2h0c1-3 2-6 2-10l2-10z" class="AH"></path><path d="M489 210h1 1v1 2l2 1v1h-1 0c-2 0-2 0-2 1l-2 1-2-1-1-2h2c0-2 1-3 2-4z" class="AN"></path><path d="M510 199h1 0c0-1 1-2 2-3h0v4c-1 2 0 4 0 6s-2 3-3 4-2 1-3 1v1l-1-1 1-1h-2 0c0-1 1-1 1-2v-1c1-1 2 1 2-2l-1-1-1 1-2 2c-1-1 0 0 0-2-2 1-2 1-3 2l1 1h1v1h0-1l-2-2c-1 1-2 1-3 1h1l6-6h1c1 0 1 0 2-1s2-1 3-2h0z" class="AO"></path><path d="M510 199h1 0c0-1 1-2 2-3h0v4c-1 2 0 4 0 6-1 0-1 1-2 1h-1l1-2v-2l-1-1v1l-1 1v-2l-1 1-1-1h-3 1c1 0 1 0 2-1s2-1 3-2h0z" class="v"></path><path d="M488 217l1 1c2 1 7-1 9-2h1c2-1 4-1 5-2s1-1 2-1c1-1 1 0 2 0v2 1c-2 2-4 2-7 3s-7 1-10 2c-1-1-3 0-4 0-3-1-7-2-11-3 0-1-2-1-3-1 1-1 2-2 2-3h4l1-1 1 1h4l1 2 2 1z" class="x"></path><path d="M475 214h4l1-1 1 1h4l1 2c-2 0-4 0-6-1v1s0 1 1 1 5-1 5 0c1 0 1 1 1 2v2c-3-1-7-2-11-3 0-1-2-1-3-1 1-1 2-2 2-3z" class="AH"></path><path d="M514 179l1-1c3 9 4 18 11 25v1l2 2c1 0 2 1 2 1-1 1-1 1-2 1h-1l-1-1-1 2h0v1c2 0 4 1 5 2-2 0-5 0-7-1l-2 2 1 1-1 2h0l-3 3-3-2c0-1-1-2-1-3-1-1-1-2-2-3l2-3h0v-6c0-2 1-4 1-6 0-5 1-13-1-17z" class="AP"></path><path d="M520 203c1 1 3 3 3 5l1 1h-2l-3-2c0-2 0-3 1-4z" class="AK"></path><path d="M515 196h2 1v1c0 1 1 2 1 3l1 3c-1 1-1 2-1 4l3 2c-2 1-2 1-2 3l1 1 1 1-1 2h0l-3 3-3-2c0-1-1-2-1-3-1-1-1-2-2-3l2-3h0v-6c0-2 1-4 1-6z" class="t"></path><path d="M519 200l1 3c-1 1-1 2-1 4v3c-1 0-3 0-3-1-2-1-1-4-1-5l1 1 1 1h1v-1-2c1-2 1-2 1-3z" class="w"></path><path d="M514 208l1 3c0 1 0 1 1 2h0c0 1 1 2 1 2h1 1 1v-1c-1-1-1-1-3-1 1-1 2-1 3-1l1 1 1 1-1 2h0l-3 3-3-2c0-1-1-2-1-3-1-1-1-2-2-3l2-3z" class="r"></path><path d="M515 196h2 1v1c0 1 1 2 1 3s0 1-1 3v2 1h-1l-1-1-1-1-1-2c0-2 1-4 1-6z" class="q"></path><path d="M121 146l24 2c6 0 12 1 17 4v-1l1 1c1 2 2 3 4 4v-1l1 1v1 1l3 4-17-6c-1 0 0 0-1 1 1 1 2 2 4 3l-1 1h0c0 1 1 2 2 3s4 1 4 3 2 4 2 6c1 1 1 2 2 2v2h0l-1-1c0-1-1-2-2-3-1-2-1-3-3-4h-1v1 1l-3-3c-1-1-2-2-3-2-8-6-14-9-24-11-1 0-1 0-2 1h0c-14-2-27 0-40 2l-9 3c3-3 5-5 8-7l9-3c9-4 16-5 26-5z" class="C"></path><path d="M87 158c1-2 2-2 4-3 13-2 25-3 38 0-1 0-1 0-2 1h0c-14-2-27 0-40 2z" class="p"></path><path d="M154 156l-22-6c6-1 14 1 20 2 2 1 5 1 8 1 1 0 1 0 2-1v-1l1 1c1 2 2 3 4 4v-1l1 1v1 1l3 4-17-6z" class="E"></path><path d="M157 160c-2-1-3-2-4-3 1-1 0-1 1-1l17 6 7 6c1 1 2 3 2 5h-1 0c0 1 1 1 1 1 3 3 4 4 6 7v1l1 2-1 1h2v3l3 1h0l5 5 1 6-4-3v3l-3-2v1c0 1 1 2 2 3v1h-1v1c0 1 1 2 2 3-1 2-1 2 0 4 1 0 1 0 1 1h0v1c1 2 2 3 2 5-1-1-3-2-4-4 0-2-1-2-3-4-1-3-3-5-6-7h-2v-1h-1c0-2-1-3-2-4l-5-1-1-3c-1-1-2-1-4-1-3-6-7-12-11-18l1-3-5-6c1 0 2 1 3 2l3 3v-1-1h1c2 1 2 2 3 4 1 1 2 2 2 3l1 1h0v-2c-1 0-1-1-2-2 0-2-2-4-2-6s-3-2-4-3-2-2-2-3h0l1-1z" class="F"></path><path d="M180 197c1 1 2 3 3 6h-2v-1h-1c0-2-1-3-2-4l2-1z" class="O"></path><path d="M176 190l1-1c2 0 2 0 3 2 2 2 6 5 6 7v1 2h-1c-1-1-1-1-1-2-1-2-2-4-4-5h-2l-2-4z" class="D"></path><path d="M191 203l-2-4c-2-4-6-7-8-10v-1c-2 0-3-2-4-3-1-2-2-3-2-4l1-1c1 3 2 5 5 6h2c1-1 2-2 3-4l1 2-1 1h2v3l3 1h0l5 5 1 6-4-3v3l-3-2v1c0 1 1 2 2 3v1h-1z" class="G"></path><path d="M186 185h2v3l3 1h0l5 5 1 6-4-3-5-6c-2-1-3-2-4-4l2-2z" class="R"></path><path d="M157 160c-2-1-3-2-4-3 1-1 0-1 1-1l17 6 7 6c1 1 2 3 2 5h-1 0c0 1 1 1 1 1 3 3 4 4 6 7v1c-1 2-2 3-3 4h-2c-3-1-4-3-5-6l-1-3 1-2h0-3c-3-4-6-7-9-10-3-2-5-4-7-5z" class="AJ"></path><path d="M157 160c2 1 4 3 7 5l-1 1c2 3 5 4 5 7 1 2 1 3 2 5 2 3 4 7 6 11v1l2 4 2 3-2 1-5-1-1-3c-1-1-2-1-4-1-3-6-7-12-11-18l1-3-5-6c1 0 2 1 3 2l3 3v-1-1h1c2 1 2 2 3 4 1 1 2 2 2 3l1 1h0v-2c-1 0-1-1-2-2 0-2-2-4-2-6s-3-2-4-3-2-2-2-3h0l1-1z" class="c"></path><path d="M176 189v1l2 4 2 3-2 1-5-1-1-3h2l2-1v-3-1z" class="L"></path><path d="M158 172c3 3 5 8 8 11l-3-6-1-2-1-2 1-1c4 6 8 15 10 22-1-1-2-1-4-1-3-6-7-12-11-18l1-3z" class="k"></path><path d="M191 345c1 1 2 1 3 3 1 1 2 1 2 2l1 1v3c0 2 0 3-1 5v3l-3 4c-4 6-9 12-15 17l2 1c1 0 2-1 4-2v2c-4 0-8 4-11 7l-14 12c-1 2-4 5-6 6h-3l-1-1-1 1-1-2h-1c-1 1-2 2-2 3h-2c1-2 2-3 2-4v-1h-4c2-4 7-7 10-9 4-5 8-10 13-14 2-2 4-4 5-7 2-2 4-5 6-7 1-1 3-3 5-4v-4c1 0 1-1 2-2h0c1-2 2-5 3-7h0c0-1 0 0 1-1v-1h1 1v-1c0-1 2-3 4-3z" class="i"></path><path d="M147 407c8-3 11-12 17-18 1 0 2-2 4-2 1 0 2-1 3-1l2 1-24 21-1 1-1-2z" class="B"></path><path d="M163 382c2-2 4-4 5-7 2-2 4-5 6-7 1-1 3-3 5-4l-1 1c0 2-1 3-3 4l-1 5 1 1s1 0 1-1c2 0 2-2 4 0 1 1 1 1 3 1h0l-3 3h-1l1-2v-1l-2 2c-1 1-3 1-4 2-1 0-2 2-3 3-2 2-5 5-8 7 0-2 1-3 2-5l1-1c3-3 6-6 8-10l-1-1c-4 3-5 9-10 10z" class="O"></path><path d="M178 383l2 1c1 0 2-1 4-2v2c-4 0-8 4-11 7l-14 12c-1 2-4 5-6 6h-3l-1-1 24-21c2-2 3-3 5-4z" class="AE"></path><path d="M191 345c1 1 2 1 3 3 1 1 2 1 2 2l1 1v3c0 2 0 3-1 5-1 1-1 1-1 3l-4 4-3 4c-1 2-3 4-5 5h0c-2 0-2 0-3-1-2-2-2 0-4 0 0 1-1 1-1 1l-1-1 1-5c2-1 3-2 3-4l1-1v-4c1 0 1-1 2-2h0c1-2 2-5 3-7h0c0-1 0 0 1-1v-1h1 1v-1c0-1 2-3 4-3z" class="AJ"></path><path d="M197 351v3c0 2 0 3-1 5-1 1-1 1-1 3l-4 4-3 4v-4l-2-3c1-1 1-2 2-2h2c1-1 2-3 3-4h1c2-1 2-4 3-6z" class="n"></path><path d="M191 366l-1-1c0-2 0-3 1-4 3-1 4-4 6-7 0 2 0 3-1 5-1 1-1 1-1 3l-4 4z" class="I"></path><defs><linearGradient id="H" x1="183.585" y1="243.496" x2="204.219" y2="240.903" xlink:href="#B"><stop offset="0" stop-color="#110304"></stop><stop offset="1" stop-color="#480705"></stop></linearGradient></defs><path fill="url(#H)" d="M192 202c2 0 4 3 5 4l2 2c1 2 2 3 3 3l1 1 1 1-1 1h-1v1c1 1 1 2 2 4s2 7 2 10l1 1v-1l1 1v2l1 2c0-1 1-2 1-3l1 1v1 4c0 2 1 2 2 2l-1 4c0 2 0 3 1 4l-1 1h1c1 3 1 5 3 8h-1l2 9c-1 1-1 2-1 3l-1 4-1-2v-1h-4-2l2 3-1 2-1 4h0l-1 1v-2l-1 2v2l-1-2v1c0 1-1 1-2 2 1-3 1-6 3-9-2-2-4-4-5-6v-7c-1-4-2-8-3-11-1-7-5-13-7-19-1-3-1-6-1-8-1-3-3-7-4-9-2-2-2-3-5-3-2-1-2-1-3-3 1-1 2-3 3-4h2c3 2 5 4 6 7 2 2 3 2 3 4 1 2 3 3 4 4 0-2-1-3-2-5v-1h0c0-1 0-1-1-1-1-2-1-2 0-4-1-1-2-2-2-3v-1h1v-1z"></path><path d="M205 237l1 1v3h0c-1 2-2 3-2 6v-4c-1 0-1-1-2-1l1-3h1l1-2z" class="O"></path><path d="M189 210c2 2 3 2 3 4 1 2 3 3 4 4l1 4-1-1h-1l-1-1-5-10z" class="K"></path><path d="M206 241c0 3-1 6 1 9v1 1l2 1c-2 2-2 4-2 6v3-2c0-1 0-2-1-3 0-1 0-3-1-4-1-2-1-4-1-6 0-3 1-4 2-6z" class="F"></path><path d="M194 220l1 1h1l1 1h1c1 1 2 1 3 0 0 2 0 3-1 5l1 1c0 1-1 3 0 4 0 0 1 1 1 2v3c-1-1-2-2-2-3-1-2-1-3-3-5-1-1-1-3-1-4-1-2-1-3-2-5z" class="V"></path><path d="M198 222c1 1 2 1 3 0 0 2 0 3-1 5-1-2-2-3-2-5zm3 6l1-1c1 0 1 2 3 2h1l1 1v-1l1 1v2l-1-1c0 2-1 4-2 6l-1 2h-1l-1-2v-3c0-1-1-2-1-2-1-1 0-3 0-4z" class="F"></path><path d="M205 237c1-2 2-4 2-6l1 1 1 2c0-1 1-2 1-3l1 1v1 4c0 2 1 2 2 2l-1 4h0c-1 3-1 6-3 8-1 1-1 1-2 0v-1c-2-3-1-6-1-9h0v-3l-1-1z" class="D"></path><path d="M209 234c0-1 1-2 1-3l1 1v1 4c-1 1-1 1-1 2l-1 1v-2l-1-3 1-1z" class="e"></path><path d="M205 237c1-2 2-4 2-6l1 1 1 2-1 1c0 2-1 4-2 6v-3l-1-1z" class="V"></path><path d="M209 251h-1 0c0-2 1-4 1-5l-1-1c1-1 1-2 2-2h2c-1 3-1 6-3 8z" class="k"></path><path d="M192 202c2 0 4 3 5 4l2 2c1 2 2 3 3 3l1 1 1 1-1 1h-1v1c1 1 1 2 2 4s2 7 2 10h-1c-2 0-2-2-3-2l-1 1-1-1c1-2 1-3 1-5-1 1-2 1-3 0h-1l-1-4c0-2-1-3-2-5v-1h0c0-1 0-1-1-1-1-2-1-2 0-4-1-1-2-2-2-3v-1h1v-1z" class="J"></path><path d="M191 203h1c3 2 6 10 7 14 0 1 1 1 2 2v3c-1 1-2 1-3 0h-1l-1-4c0-2-1-3-2-5v-1h0c0-1 0-1-1-1-1-2-1-2 0-4-1-1-2-2-2-3v-1z" class="m"></path><path d="M212 243h0c0 2 0 3 1 4l-1 1h1c1 3 1 5 3 8h-1l2 9c-1 1-1 2-1 3l-1 4-1-2v-1h-4-2c-1-2-1-4-1-7v-3c0-2 0-4 2-6l-2-1v-1c1 1 1 1 2 0 2-2 2-5 3-8z" class="B"></path><path d="M207 259c1-1 2-1 2-3h1v3c-1 1-1 1-1 3 1 2 0 5 1 7h-2c-1-2-1-4-1-7v-3z" class="D"></path><path d="M214 269l-1-1v-2c0-3 0-3 2-5l-1-1v-1c0-1 0-1 1-3l2 9c-1 1-1 2-1 3l-1 4-1-2v-1z" class="G"></path><path d="M821 223l1 2c1-1 2-2 3-2v1c0 1-1 2-1 3-1 1-1 3-1 5 1 1 1 2 2 2 3 0 5-2 6-4 1-1 2-1 4-2v2c0 3 1 5 3 8-8 5-12 11-14 20-1 2-1 4 0 6 0 2 2 3 3 3-1 4-3 8-3 12 0 5 0 9 1 14l-1-1c-2-2-1-5-1-8-1-2 0-7 0-9l2-7c-1-1-2-3-3-4l-1 1c0 2-1 3-2 5h0c0 3 0 6-1 9-2 7-1 14-3 21-1 1-1 2-2 2-1-1-1-2-2-3 0-2 0-3-1-5 0-1-1-1-1-2v-1c0-2 0-5-1-7-1-1 0-2 0-3-2 0-2 0-3-1v-3h0c2-2 1-7 1-10v-6l-1-3h0c-1-1 0-3 0-4v-2l-1-1c1-1 1-2 2-4 1-1 2-1 4-2l1 1 2-2v-1c1-3 3-7 4-9 2-2 2-3 2-6v-2c1-1 2-1 2-3z" class="O"></path><path d="M821 223l1 2c1-1 2-2 3-2v1c0 1-1 2-1 3-1 1-1 3-1 5h-3c-1 1-1 2-1 4h0v3 1c-1-1-1 0-1-1h-1c-1 1-1 1-2 1l-1 3h-1c1-3 3-7 4-9 2-2 2-3 2-6v-2c1-1 2-1 2-3z" class="H"></path><path d="M810 245l1 1 2-2c-1 1-1 2-1 2-2 2-2 4-2 6 0 1 0 1 1 1l-1 1h1v-2h1c1 0 1-1 2-2 1 1 1 1 1 2l-1 3v2l-1-1-1 1h-2v1h-1 0-1v-1l-1-1-1-1v2 4l-1-3h0c-1-1 0-3 0-4v-2l-1-1c1-1 1-2 2-4 1-1 2-1 4-2z" class="J"></path><path d="M815 252c1 2 0 3 0 5l1 2v-2c1-1 1-2 1-2v-2h-1l1-4h0c0 1 0 3 1 5h-1c-1 4-1 10 0 14v1l-1 1 1 2c-1 3-1 7-4 9l-1-1v-1h-1l-1 1c-1-1-1-3-1-5l1-1v-1h1c0-1 1-2 1-3s0-1 1-2v-3-1c-2-2-2-4-3-6v-1h2l1-1 1 1v-2l1-3z" class="K"></path><path d="M811 273c0-1 1-2 1-3s0-1 1-2c1 1 1 2 1 3-1 1-2 2-3 2z" class="O"></path><path d="M810 258v-1h2l1-1 1 1c1 0 1 2 1 2 0 2-1 2-1 4h0c-1 1-1 2-1 2h0v-1c-2-2-2-4-3-6z" class="F"></path><path d="M806 261v-4-2l1 1 1 1v1h1 0 1c1 2 1 4 3 6v1 3c-1 1-1 1-1 2s-1 2-1 3h-1v1l-1 1c0 2 0 4 1 5l1-1h1v1l1 1c3-2 3-6 4-9l2-2c0 3 0 6-1 9-2 7-1 14-3 21-1 1-1 2-2 2-1-1-1-2-2-3 0-2 0-3-1-5 0-1-1-1-1-2v-1c0-2 0-5-1-7-1-1 0-2 0-3-2 0-2 0-3-1v-3h0c2-2 1-7 1-10v-6z" class="Q"></path><path d="M811 299c0-2 0-4 2-5h0c1 1 0 2 0 3s1 3 1 3h1c-1 1-1 2-2 2-1-1-1-2-2-3z" class="W"></path><path d="M807 256l1 1v1h1 0 1c1 2 1 4 3 6v1 3c-1 1-1 1-1 2s-1 2-1 3h-1v-3c1-2 0-3-1-4 0-1 1-2 1-2l-1-1c-1-1-2-2-2-4v-3z" class="H"></path><path d="M762 571c1 1 1 2 0 3 0 3-3 11-5 13h-1c0-2 1-6 0-7-2 1-4 2-4 4-2 2-3 4-4 6-2 3-4 5-4 8 1 1 1 1 2 1l1 1c-1 2-4 4-5 6s-4 5-5 6l-1-1-1-1v3l-2-1c-1 3-1 5-2 8-2 0-2 1-3 1v2c-1 1-1 1-1 2 0 0-2 1-2 2-1 1-1 4-2 6 0 1-1 1-1 2 0 2-1 5-1 7l-1 5c-1 1-2 2-3 4h0c0-2 0-4-1-6l1-6c-2 2-2 3-2 6l-2 1v-1c-1-2-1-2-3-4h0-2c5-6 6-15 7-23 0-2 1-4 1-6 0-1 0-2 1-4h2v-2c2-1 4-3 6-3v-1l2-1c1-1-1-1-1-2 0 0 1 0 1-1 2-1 4-3 4-5 1-1 1-1 1-2 0-2 3-4 4-6h1l3-3c3 0 5-2 7-4s4-3 6-5c1-1 4-2 6-2 1 1 1 0 3 0z" class="X"></path><path d="M730 608c3-7 7-13 12-18-1 2-2 5-3 8-2 4-2 9-5 12 0-1 0-2-1-3v-1 2h-3z" class="H"></path><path d="M730 608h3v-2 1c1 1 1 2 1 3l-1 2c-1 3-1 5-2 8-2 0-2 1-3 1v2c-1 1-1 1-1 2 0 0-2 1-2 2-1 1-1 4-2 6 0 1-1 1-1 2l1-6c1-8 5-14 7-21z" class="e"></path><path d="M740 582c3 0 5-2 7-4 0 1-1 4-3 5a30.44 30.44 0 0 1-8 8c-4 5-7 10-10 15h-2c1-1 2-2 1-3v-1l2-1c1-1-1-1-1-2 0 0 1 0 1-1 2-1 4-3 4-5 1-1 1-1 1-2 0-2 3-4 4-6h1l3-3z" class="M"></path><path d="M737 585l3 1c-3 2-5 4-8 5 0-2 3-4 4-6h1z" class="V"></path><path d="M740 582c3 0 5-2 7-4 0 1-1 4-3 5-2 0-3 2-4 3l-3-1 3-3z" class="j"></path><path d="M725 603c1 1 0 2-1 3h2l-7 21-2 8v2 2h0c-2 2-2 3-2 6l-2 1v-1c-1-2-1-2-3-4h0-2c5-6 6-15 7-23 0-2 1-4 1-6 0-1 0-2 1-4h2v-2c2-1 4-3 6-3z" class="S"></path><g class="B"><path d="M716 625l3 2-2 8h-4c2-3 2-6 3-10z"></path><path d="M713 635h4v2 2h0c-2 2-2 3-2 6l-2 1v-1c-1-2-1-2-3-4 0-1 1-2 2-3l1-3z"></path></g><path d="M713 635h4v2l-3 3-1 2c-1-1 0 0 0-1s-1-2-1-3l1-3z" class="M"></path><path d="M725 603c1 1 0 2-1 3h2l-7 21-3-2c2-4 4-9 5-14-2-2-2-2-4-3h2v-2c2-1 4-3 6-3z" class="p"></path><path d="M725 603c1 1 0 2-1 3 0 2-1 2-2 2s-2-1-3-2c2-1 4-3 6-3z" class="I"></path><path d="M611 95c3-2 7-2 11-2 1 1 1 0 3 1 1 0 2 2 3 3 1 2 1 4 1 6 2 2 3 5 4 7v2l1 2c1-1 1-2 3-2s2 1 3 2c2 3 2 8 2 12l-1 2v1h4l-4 1v2l-1-1c-2-1-3 0-5 0-1 2-3 3-5 5-1-1-2-1-2-3h-1v-1-2c-2-4-2-8-5-12 0-3-1-4-3-6-1 0-2-2-2-2l-2 1-1-1v-1h-1v2c-7-1-13 1-18 5-8 6-13 15-16 24-1 2-1 5-2 8v-11c1-2 1-5 2-8 0-2 1-4 1-7l1-5c2-3 3-5 4-8l1 1 2-2h1c4-5 9-9 16-11 2-1 4-1 6-2z" class="AJ"></path><path d="M617 110h1v-1h1 1l1-1v1c1 0 1 0 2 1 1 0 1 1 1 2v1h1v2c-2-1-3-3-6-3-1 0-2-2-2-2z" class="U"></path><path d="M585 109l1 1 2-2h1l-3 6v1c-2 3-3 5-6 7l1-5c2-3 3-5 4-8z" class="j"></path><path d="M619 112c3 0 4 2 6 3 1 1 1 3 1 4l1 2v3c0-2 0-2-1-3v4c1 2 1 4 1 5-2-4-2-8-5-12 0-3-1-4-3-6z" class="d"></path><path d="M626 100l3 3c2 2 3 5 4 7v2l1 2-3 6-1-7c-1-6-4-9-8-12 1-1 3-1 4-1z" class="G"></path><path d="M611 95c6 2 10 1 15 5-1 0-3 0-4 1l-1-1c-6-2-12-1-18 1v-2c1-1 2-1 2-2 2-1 4-1 6-2z" class="a"></path><path d="M611 95c3-2 7-2 11-2 1 1 1 0 3 1 1 0 2 2 3 3 1 2 1 4 1 6l-3-3c-5-4-9-3-15-5z" class="J"></path><path d="M589 108c4-5 9-9 16-11 0 1-1 1-2 2v2c-7 3-12 8-17 14v-1l3-6z" class="G"></path><path d="M587 184v-4c1 2 1 4 2 6l1 2c3 3 6 6 10 6l-1 1c-1 1-2 2-2 3l-2 1c-1 0-2 1-2 1l-2 1c-4 3-7 5-12 7 1 1 1 1 1 2h-1c-1 0-2 1-3 1-2 1-4 1-6 2h-1c-1 1-4 3-5 3-1-1-1-1-2-1h-2c-1 0-2 0-3-1l-3 1h-2 0c1 1 1 1 2 3v1c-5 1-10 2-15 2h-5c-5 0-9-2-12-5h-1l1-2-1-1 2-2c2 1 5 1 7 1-1-1-3-2-5-2v-1h0l1-2 1 1h1c1 0 1 0 2-1 0 0-1-1-2-1l-2-2v-1c3 1 6 4 10 4 5 0 11-1 15-4 4-5 3-10 3-16h1l1 3c2 2 4 4 6 5 3 2 7 1 10 0 6-2 12-5 15-11z" class="AK"></path><path d="M535 212c1-1 1-2 4-3v1l1 1c0-1 0-1 1-1l2 1c-2 2-6 1-8 1z" class="AP"></path><path d="M584 199c1-1 3-2 4-3h2l1-1c1 0 0 0 1 1h1l1-1 1 1 1 1-1 2c-1 0-2 1-2 1h-5l1-1-1-1-2 1h-2z" class="AL"></path><path d="M584 199h2l2-1 1 1-1 1h5l-2 1c-2 0-2-1-4 1l-3 1c-2 0-4 2-6 2 0-1 0-2 1-3h-1v-1c1-1 2-2 3-2s1 0 2-1l1 1z" class="AS"></path><path d="M579 202l3-2h1v1c0 1 1 1 1 2-2 0-4 2-6 2 0-1 0-2 1-3z" class="AT"></path><path d="M566 206c1-1 1-2 1-3h-2v-1h3 0 3 1c0-1-1-1 0-3h1v1c2 2 2 2 5 2h1c-1 1-1 2-1 3-3 1-8 3-10 2l-1-1h-1z" class="AD"></path><path d="M584 203l3-1c2-2 2-1 4-1-4 3-7 5-12 7l-5 2c-4 1-6 0-10-2l-1-1-2-2c2-1 3-1 4-1l1 1-1 1h1 1l1 1c2 1 7-1 10-2 2 0 4-2 6-2z" class="x"></path><path d="M587 184v-4c1 2 1 4 2 6-1 1-2 2-2 3-2 2-4 4-6 5s-2 2-4 2h-2c-2 1-4 1-6 2-2 0-2 2-4 2-1-1-2-1-3-2h-1-1c1 1 2 0 3 1 0 1 1 1 2 2h1-3 0-2-1c0-1-1-1-1-2l-2-7-1-1v-1c2 2 4 4 6 5 3 2 7 1 10 0 6-2 12-5 15-11z" class="g"></path><path d="M556 206h0c2-1 3-1 5-1l2 2 1 1c4 2 6 3 10 2l5-2c1 1 1 1 1 2h-1c-1 0-2 1-3 1-2 1-4 1-6 2h-1c-1 1-4 3-5 3-1-1-1-1-2-1h-2l-1-2-3-4 1-1c-1-1-1-1-2-1l1-1z" class="q"></path><path d="M563 207l1 1v2c-1 1-1 1-3 1v-2l2-2z" class="g"></path><path d="M552 207c1-1 2-1 4-1l-1 1c1 0 1 0 2 1l-1 1 3 4 1 2c-1 0-2 0-3-1l-3 1h-2 0c1 1 1 1 2 3v1c-5 1-10 2-15 2h-5c-5 0-9-2-12-5h-1l1-2-1-1 2-2c2 1 5 1 7 1h3l1 1 1-1h0c2 0 6 1 8-1 1 0 1 0 2-1h2c1-2 1-1 3-1 0-1 1-2 2-2z" class="AO"></path><path d="M552 207c1-1 2-1 4-1l-1 1c1 0 1 0 2 1l-1 1 3 4 1 2c-1 0-2 0-3-1-1 0-2-1-3-1l-1-1v-2h-1v1l1 2v1c-1 0-1 0-2-1l-1 1h-2v1h-2c-1 1-1 1-3 1v-1c1-2 1-2 3-3 0 1 0 1 1 1v-1l1-1c1-1 2-1 3-2h1v-2z" class="AD"></path><path d="M555 207c1 0 1 0 2 1l-1 1 3 4h-1c-1-1-2-2-3-2h-1v-2l1-2z" class="AY"></path><path d="M543 211c1 0 1 0 2-1h2c1-2 1-1 3-1 0-1 1-2 2-2v2h-1c-1 1-2 1-3 2l-1 1v1c-1 0-1 0-1-1-2 1-2 1-3 3h-3l-3 1h-3c-2 0-1-1-2-2-2 0-2 0-4-1l-1 1c-1 0-2-1-2-2-2 1-2 2-3 2l-1-1 2-2c2 1 5 1 7 1h3l1 1 1-1h0c2 0 6 1 8-1z" class="AS"></path><path d="M522 216l1-1h2c1 1 1 1 3 1v-1c1 0 1 0 2 1l-1 1c2 2 6 2 9 2h1 3 0c2-1 3-1 4-2 2-1 3-1 4-1l2-1c1 1 1 1 2 3v1c-5 1-10 2-15 2h-5c-5 0-9-2-12-5z" class="x"></path><path d="M542 219c2-1 3-1 4-2 2-1 3-1 4-1l-3 4c-1 0-3-1-5-1z" class="AH"></path><path d="M405 163h0l2-1v-1h-1v-1h3l-1 2v1c0-1 1-1 2-2h1 1 2c1 0 2 0 3 1 1 3 1 5 2 7l1 1c2 3 6 7 10 8 2 1 4 0 6-1l2-1v3c-1 3-1 5 0 8 1 4 8 7 13 9s10 3 16 0c1-1 4-3 5-3v2c-1 1-3 3-4 6v-1l-3 7-3 4c0-1-1-1-2-1v1c-1-1-1-1-1-2-1 1-1 2-1 3 1 1 3 1 5 2h0l1 1v1c1 1 0 1 1 1-2 0-3 0-5-1-1 0-3-1-4-1-3 0-6-2-9-3-4-1-8-4-11-6s-6-3-7-6v-1c-1-1-1-2-2-3-1-2-5-4-7-6h-1l-3-3v-1c-3-2-5-4-7-6v-1l1-1c-1 0-2-1-2-2l-2-2v-2c0-1-1-2-1-3h0l-2-1v-1h2s0-1 1 0h1l1-2c-2 0-2 0-3-1v-1z" class="AK"></path><path d="M434 191h2l2 2-1 1c0 1 0 1-1 1l-1-1c-1-1-1-1-1-3z" class="r"></path><path d="M430 186c1 0 1 1 2 1v2l-2-1-1 2 1 1h-1l-1-1v-3l2-1z" class="AP"></path><path d="M420 190c1 0 2 0 3 1 0 1 1 1 2 1 0 1 0 1 1 1l2 2 1-1v-1h1v3c2 1 2 0 4 1h0v2c1 2 3 2 4 3 1 0 1 0 1 1h-1c-4-2-7-4-11-7-1-2-5-4-7-6z" class="AN"></path><path d="M439 203c1-1 1 0 2-1l-1-1 1-1 1 1 1-1-2-3 2-1 9 4h0-3v1c0 1 1 2 3 2 1 1 3 1 4 2h-2c1 2 2 1 2 2v1c-4-1-7-2-10-3-1-1-2-1-3-1-1-1-3-1-4-1z" class="AO"></path><path d="M410 178c1 1 1 1 1 3h1c1 1 2 1 2 3l6 5h1c-1-1-1-1-1-2s0-1 1-2h0l2 1v-4h1l1 2h1l2-1 2 1v2l-2 1v3l1 1c0 1-1 1 0 2v1l-1 1-2-2c-1 0-1 0-1-1-1 0-2 0-2-1-1-1-2-1-3-1h-1l-3-3v-1c-3-2-5-4-7-6v-1l1-1z" class="r"></path><path d="M452 200c4 1 6 2 10 1h1c2-1 4-1 5-1l-3 7-3 4c0-1-1-1-2-1 1-1 3-2 4-3-1-1-2 0-3 0-2 1-3 1-5 1h0v-1c0-1-1 0-2-2h2c-1-1-3-1-4-2-2 0-3-1-3-2v-1h3z" class="AN"></path><path d="M463 201c2-1 4-1 5-1l-3 7-3 4c0-1-1-1-2-1 1-1 3-2 4-3-1-1-2 0-3 0l2-3v-1h-1-1c1-1 1-1 1-2h1z" class="AH"></path><path d="M436 177l2-1v3c-1 3-1 5 0 8 1 4 8 7 13 9s10 3 16 0c1-1 4-3 5-3v2c-1 1-3 3-4 6v-1c-1 0-3 0-5 1h-1c-4 1-6 0-10-1h0c0-1-1-2-1-3-2-1-5-2-7-3s-4-3-6-4h-1c-2-1-2-2-3-4-1 0-1 0-2-1-1 0-2-1-2-1l-2-1-2 1h-1l-1-2h-1v4l-2-1-1-2-2-2-1-1h1c1 0 1 0 2 1l2-2v-3c2 3 4 5 6 6s3 1 4 1c3-1 4-3 5-5v-1h-1z" class="w"></path><path d="M417 180h1c1 0 1 0 2 1l2-2c0 1 1 2 0 3l-2 1-2-2-1-1z" class="Y"></path><path d="M465 198c2-1 5-3 7-5v2c-1 1-3 3-4 6v-1c-1 0-3 0-5 1 1-1 1-2 2-3z" class="AN"></path><path d="M451 197c5 2 8 2 14 1-1 1-1 2-2 3h-1c-4 1-6 0-10-1h0c0-1-1-2-1-3z" class="AD"></path><defs><linearGradient id="I" x1="439.53" y1="213.024" x2="453.291" y2="202.118" xlink:href="#B"><stop offset="0" stop-color="#3f3e3e"></stop><stop offset="1" stop-color="#5d5959"></stop></linearGradient></defs><path fill="url(#I)" d="M427 196c4 3 7 5 11 7h1c1 0 3 0 4 1 1 0 2 0 3 1 3 1 6 2 10 3h0c2 0 3 0 5-1 1 0 2-1 3 0-1 1-3 2-4 3v1c-1-1-1-1-1-2-1 1-1 2-1 3 1 1 3 1 5 2h0l1 1v1c1 1 0 1 1 1-2 0-3 0-5-1-1 0-3-1-4-1-3 0-6-2-9-3-4-1-8-4-11-6s-6-3-7-6v-1c-1-1-1-2-2-3z"></path><path d="M439 203c1 0 3 0 4 1 1 0 2 0 3 1 3 1 6 2 10 3h0c2 0 3 0 5-1 1 0 2-1 3 0-1 1-3 2-4 3v1c-1-1-1-1-1-2-1 1-1 2-1 3-7-2-14-5-20-9h1z" class="x"></path><path d="M405 163h0l2-1v-1h-1v-1h3l-1 2v1c0-1 1-1 2-2h1 1 2c1 0 2 0 3 1 1 3 1 5 2 7l1 1c2 3 6 7 10 8 2 1 4 0 6-1h1v1c-1 2-2 4-5 5-1 0-2 0-4-1s-4-3-6-6v3l-2 2c-1-1-1-1-2-1h-1l1 1 2 2 1 2h0c-1 1-1 1-1 2s0 1 1 2h-1l-6-5c0-2-1-2-2-3h-1c0-2 0-2-1-3-1 0-2-1-2-2l-2-2v-2c0-1-1-2-1-3h0l-2-1v-1h2s0-1 1 0h1l1-2c-2 0-2 0-3-1v-1z" class="q"></path><path d="M418 181h-1l-1-1v-1c-1-1-2-1-2-2l-1 1-1-1v-1c-1-1-1-1-1-2-1 0-1 0-2-1h0l1-1c-1-1-1-2-1-2v-1-3l1-1c1 1 0 1 0 2l1 1c0-2 0-2 1-3h1l1 2c1 0 1 1 2 2-1 2-1 2-3 3l1 1c1-1 2-2 4-2v1h-1c-1 0-2 1-3 2v1h0l2-1c1 0 1 1 1 2s-1 1-1 2l1 1 1-1v1h-1v1l1 1z" class="r"></path><path d="M414 161c1 0 2 0 3 1 1 3 1 5 2 7l1 1c2 3 6 7 10 8 2 1 4 0 6-1h1v1c-1 2-2 4-5 5-1 0-2 0-4-1s-4-3-6-6v3l-2 2c-1-1-1-1-2-1h-1v-1h1v-1l-1 1-1-1c0-1 1-1 1-2s0-2-1-2l-2 1h0v-1c1-1 2-2 3-2h1v-1c-2 0-3 1-4 2l-1-1c2-1 2-1 3-3-1-1-1-2-2-2l1-2c-1-2-1-3-1-4z" class="g"></path><path d="M422 176l-2-1 1-1c3 2 7 7 11 6 2 0 3-1 5-3v1c-1 2-2 4-5 5-1 0-2 0-4-1s-4-3-6-6z" class="AN"></path><path d="M819 270h0c1-2 2-3 2-5l1-1c1 1 2 3 3 4l-2 7c0 2-1 7 0 9 0 3-1 6 1 8l1 1 3 12c-1 6 2 16 5 21s9 11 10 18c1 1 2 2 3 4h-1v1c0 2 1 4 2 6 2 5 5 10 8 15v1c3 4 6 8 8 12 1 2 2 3 2 5 2 2 2 5 4 7l-1 1c-1 1-2-2-4-2-2-2-2-2-4-3-1-1-2-3-3-4l-1-1c-1-4-6-8-8-12l-14-21h1l-3-5v-2 1l1-1c-1-3-3-6-4-8l-2-2h-1c-1 0-11-18-12-20 0 0-1-3-1-4-2-3-3-6-4-10 2 2 3 6 5 8 0 0 1 0 1-1l-2-7c1 0 1-1 2-2 2-7 1-14 3-21 1-3 1-6 1-9z" class="n"></path><path d="M814 316s-1-3-1-4c-2-3-3-6-4-10 2 2 3 6 5 8 0 0 1 0 1-1 3 6 5 11 9 16 1 3 4 5 6 8 3 4 4 10 5 15-1 0-1-1-2-2-1-3-3-6-4-8l-2-2h-1c-1 0-11-18-12-20z" class="E"></path><path d="M814 316s-1-3-1-4c-2-3-3-6-4-10 2 2 3 6 5 8l15 28-2-2h-1c-1 0-11-18-12-20z" class="n"></path><path d="M832 347c1 1 2 2 4 3 2-4 0-8-1-12 0-1-1-3 0-4 4 7 2 18 9 25l3 3v-4c-1-5-3-9-4-14 1 1 2 2 3 4h-1v1c0 2 1 4 2 6 2 5 5 10 8 15v1c3 4 6 8 8 12 1 2 2 3 2 5 2 2 2 5 4 7l-1 1c-1 1-2-2-4-2-2-2-2-2-4-3-1-1-2-3-3-4l-1-1c-1-4-6-8-8-12l-14-21h1l-3-5v-2 1z" class="I"></path><path d="M863 383c1 2 2 3 2 5 2 2 2 5 4 7l-1 1c-1 1-2-2-4-2-2-2-2-2-4-3-1-1-2-3-3-4 2 1 3 3 5 4-1-2-2-3-2-5h0c1 2 2 3 4 4 0-1 0-2-1-3v-4z" class="j"></path><defs><linearGradient id="J" x1="294.826" y1="676.864" x2="311.891" y2="677.187" xlink:href="#B"><stop offset="0" stop-color="#941313"></stop><stop offset="1" stop-color="#b92125"></stop></linearGradient></defs><path fill="url(#J)" d="M297 617h1l1 1v1c1 1 1 2 2 4v3h0l2 7c0 3 1 6 2 8 1 3 0 5 2 8 1-2 0-5 1-7v-1l-1-7c1-1 2-1 3 0h0l1 1v-2h1c1-2 0-7 0-9l5 12-1 1c-1-2-2-3-2-5 0-1 0-1-1-1v3c0 1 1 1 1 2v1l-1 1c0 1 0 2 1 3h0c-1 3-1 6-1 9-1 8 0 16 0 25-1 8-3 16-4 24-1 3-1 7-2 9h-2c-1 3-2 6-4 9v-1 1c-1 0-1-1-2-1l-8 11v-1l-1-1c-1 1-2 2-3 2 6-12 11-23 12-37 0-13-2-25-5-38 2 1 4 1 5 2h1c-2-3-2-7-3-9l-5-18v-1c0-2 0-2 2-5 0-1 1-3 3-4z"></path><path d="M299 716l3-6c1-2 2-3 4-3l-1 1c-1 3-2 6-4 9v-1 1c-1 0-1-1-2-1z" class="a"></path><path d="M299 690v-2c1 12-2 27-9 37-1 1-2 2-3 2 6-12 11-23 12-37z" class="E"></path><path d="M308 641c1 4 1 9 1 13 0 7 0 15-2 22-1 2-1 6-1 9h0l-1-8-3-16 1 1v-1c1-1 0-4 0-5s1-3 1-5v-6-2h1v5c1 1 2 2 3 2v-8-1z" class="J"></path><path d="M302 661l1 1v-1c1-1 0-4 0-5s1-3 1-5v12c0 1 1 2 1 3 0 2-1 4 0 6 0 1 0 2 1 3 0 1 0 2-1 2l-3-16z" class="E"></path><path d="M297 617h1l1 1v1c1 1 1 2 2 4v3h0l2 7c0 3 1 6 2 8 1 3 0 5 2 8 1-2 0-5 1-7v8c-1 0-2-1-3-2v-5h-1v2 6c0 2-1 4-1 5s1 4 0 5v1l-1-1-3-7h1c-2-3-2-7-3-9l-5-18v-1c0-2 0-2 2-5 0-1 1-3 3-4z" class="L"></path><path d="M297 617h1l1 1v1c1 1 1 2 2 4v3h0l-3-9c-1 2-3 5-3 7l1 2h0c0 3 1 6 2 9 2 6 3 12 3 18v3-1l-1-1c-2-3-2-7-3-9l-5-18v-1c0-2 0-2 2-5 0-1 1-3 3-4z" class="AC"></path><path d="M211 233h1c0 1 1 2 2 2l1 1h1c0 3-1 3-1 5 0 1 1 1 2 2v-3c1 1 2 2 2 3l1 1c1 1 1 2 1 2 0 1 0 2 1 2l1 8v2c1 0 1 1 2 1v1l1-3 3 3 3 3 2 6 3 3 2-1c-1 3-1 5-3 7l-1 3 2 1v2c1 1 1 0 2 0h1l-3 5c-1 0-2 1-2 1-2 2-4 6-5 8 0 1-2 3-2 5l-1 6c1 1 2 1 2 1-1 2-3 6-3 9l2 1c-2 3-4 10-8 13 0-1-1-1-2-1-2 3-5 5-7 8l-4 5c-1-2-1-2-1-4 0-1 1-2 2-3l4-4-1-1c1-1 1-3 1-4v-1-1c1-1 1-2 1-3v-4l-2 2c0-1-1-3-2-4 0-1-1-1-2-1-3-2-2-5-4-8 1-4 2-9 2-13 0-3-1-6 0-10v-1c1-2 2-4 2-6l1-1h0l1-4 1-2-2-3h2 4v1l1 2 1-4c0-1 0-2 1-3l-2-9h1c-2-3-2-5-3-8h-1l1-1c-1-1-1-2-1-4l1-4c-1 0-2 0-2-2v-4z" class="I"></path><path d="M223 285v1c1 2 1 5 2 7l1 1c0 1 1 2 1 3l-1 6c0 1 0 2-1 3l-1-8-1-13z" class="a"></path><path d="M211 233h1c0 1 1 2 2 2l1 1h1c0 3-1 3-1 5 0 1 1 1 2 2v5 1c1 2 1 5 2 8-1-1-1-2-2-3v-1-1c-1-1-1-2-2-2 0 2 1 3 1 6-2-3-2-5-3-8h-1l1-1c-1-1-1-2-1-4l1-4c-1 0-2 0-2-2v-4z" class="E"></path><path d="M211 233h1c0 1 1 2 2 2l1 1h1c0 3-1 3-1 5-1-1-1-2-1-4h0-1v2c-1 0-2 0-2-2v-4z" class="B"></path><path d="M221 268l1-1c1 0 1 0 2-1h0c0 1 0 2 1 3 0 2 0 5-1 7 1 2 3 4 3 7h-1l-1 1c0 1 0 1 1 1l1 1v1c0 1 0 1-1 2v5l-1-1c-1-2-1-5-2-7v-1l-2-17z" class="M"></path><path d="M215 272l1-4c0-1 0-2 1-3l3 18c0 2 0 6 1 8l1 13h-1v-1c0-1 0-1-1-2s-1-2-1-3v-4h-1l-3-22z" class="u"></path><path d="M217 240c1 1 2 2 2 3l1 1c1 1 1 2 1 2 0 1 0 2 1 2l1 8v2c1 0 1 1 2 1v1 1h0v1c0 2 0 3-1 4h0c-1 1-1 1-2 1l-1 1-2-11c-1-3-1-6-2-8v-1-5-3z" class="e"></path><path d="M223 258c0 1 0 2 1 3h-1c-1-1-1-1-1-3 0 0 0-1-1-2-1-3 0-6-1-9v-1h1c0 1 0 2 1 2l1 8v2z" class="B"></path><path d="M226 257l3 3 3 3 2 6 3 3 2-1c-1 3-1 5-3 7l-1 3 2 1v2c1 1 1 0 2 0h1l-3 5c-1 0-2 1-2 1-2 2-4 6-5 8 0 1-2 3-2 5h-2l1-6c0-1-1-2-1-3v-5c1-1 1-1 1-2v-1l-1-1c-1 0-1 0-1-1l1-1h1c0-3-2-5-3-7 1-2 1-5 1-7-1-1-1-2-1-3 1-1 1-2 1-4v-1h0v-1l1-3z" class="y"></path><path d="M234 269l3 3 2-1c-1 3-1 5-3 7l-2-9z" class="n"></path><path d="M230 285v-6c-1-3-1-5-1-8h1 1v3 1c-1 1-1 1-1 2h2v1c0 1 0 2-1 3v1l2 2-2 4c0-1 0-2-1-3z" class="M"></path><path d="M226 257l3 3c-1 1-1 3-1 4 0 2 1 4 2 5-1 1-1 1-2 1h-1c-1 0-1-1-2-1-1-1-1-2-1-3 1-1 1-2 1-4v-1h0v-1l1-3z" class="u"></path><path d="M226 257l3 3c-1 1-1 3-1 4h-1v-1h0l-1-1-1-1v-1l1-3zm4 12h1v2h-1-1c0 3 0 5 1 8v6 2l-1-1h-1v1l-1-1-1-1c-1 0-1 0-1-1l1-1h1c0-3-2-5-3-7 1-2 1-5 1-7 1 0 1 1 2 1h1c1 0 1 0 2-1z" class="s"></path><path d="M233 284l2-3 2 1v2c1 1 1 0 2 0h1l-3 5c-1 0-2 1-2 1-2 2-4 6-5 8 0 1-2 3-2 5h-2l1-6c0-1-1-2-1-3v-5c1-1 1-1 1-2v-1l1 1v-1h1l1 1v-2c1 1 1 2 1 3l2-4z" class="I"></path><path d="M227 286l1 1v-1h1l1 1v-2c1 1 1 2 1 3l-4 9c0-1-1-2-1-3v-5c1-1 1-1 1-2v-1z" class="f"></path><path d="M221 291l1 1v1-1h0 0c1 2 1 4 2 6l1 8c1-1 1-2 1-3h2l-1 6c1 1 2 1 2 1-1 2-3 6-3 9l2 1c-2 3-4 10-8 13 0-1-1-1-2-1-2 3-5 5-7 8l-4 5c-1-2-1-2-1-4 0-1 1-2 2-3l4-4-1-1c1-1 1-3 1-4v-1-1c1-1 1-2 1-3v-4l-1-2c1-1 1-1 2 0h0c0-1 0-2 1-3 1 2 1 2 1 4 1 0 2-1 2-2 1-2 1-3 1-5v-2h-1c-1-1-1-2 0-3v-1-1-5-6h1v4c0 1 0 2 1 3s1 1 1 2v1h1l-1-13z" class="E"></path><path d="M226 319l2 1c-2 3-4 10-8 13 0-1-1-1-2-1 3-4 6-8 8-13z" class="AB"></path><path d="M221 291l1 1v1-1h0 0c1 2 1 4 2 6l1 8h0v3c1 3 0 6 0 9 0 2-1 4-2 5l-1-1v-1-1c1-4 1-11 0-16l-1-13z" class="L"></path><path d="M218 294h1v4c0 1 0 2 1 3s1 1 1 2v1h1c1 5 1 12 0 16v1c-3 5-7 9-10 13l-1-1c1-1 1-3 1-4v-1-1c1-1 1-2 1-3v-4l-1-2c1-1 1-1 2 0h0c0-1 0-2 1-3 1 2 1 2 1 4 1 0 2-1 2-2 1-2 1-3 1-5v-2h-1c-1-1-1-2 0-3v-1-1-5-6z" class="h"></path><path d="M218 294h1v4c0 1 0 2 1 3s1 1 1 2v1h1c1 5 1 12 0 16-1-1-1-2-1-3 1-2 0-6 0-8l-1-1c0-2-1-3-1-6 0-1-1-1-1-2v-6z" class="M"></path><path d="M212 327l1-1c1-2 1-2 3-4l2-2c1 0 1 1 2 0 0-1 0-1 1-2v-1c0 1 0 2 1 3v1c-3 5-7 9-10 13l-1-1c1-1 1-3 1-4v-1-1z" class="G"></path><defs><linearGradient id="K" x1="207.488" y1="282.875" x2="212.217" y2="320.41" xlink:href="#B"><stop offset="0" stop-color="#3d0100"></stop><stop offset="1" stop-color="#6d0d0e"></stop></linearGradient></defs><path fill="url(#K)" d="M208 269h2 4v1l1 2 3 22v6 5 1 1c-1 1-1 2 0 3h1v2c0 2 0 3-1 5 0 1-1 2-2 2 0-2 0-2-1-4-1 1-1 2-1 3h0c-1-1-1-1-2 0l1 2-2 2c0-1-1-3-2-4 0-1-1-1-2-1-3-2-2-5-4-8 1-4 2-9 2-13 0-3-1-6 0-10v-1c1-2 2-4 2-6l1-1h0l1-4 1-2-2-3z"></path><path d="M208 269h2 4v1l1 2 3 22v6 5 1 1c-1 1-1 2 0 3h1v2c-2-2-4-4-4-7-1-1-2-3-3-5l-3-12c0-1 0-1-1-2h0c-1-1-1-2-1-3l-1 3-1-1c1-2 2-4 2-6l1-1h0l1-4 1-2-2-3z" class="i"></path><path d="M208 269h2 4v1l-2 1v3l-1 1c0-1-1-2-1-3l-2-3z" class="h"></path><path d="M209 274c0 3 0 5 2 8 0 2 1 5 2 8 1 2 0 5 0 7l-1 3-3-12c0-1 0-1-1-2h0c-1-1-1-2-1-3l-1 3-1-1c1-2 2-4 2-6l1-1h0l1-4z" class="E"></path><path d="M207 279l1-1h0l1 10c0-1 0-1-1-2h0c-1-1-1-2-1-3l-1 3-1-1c1-2 2-4 2-6z" class="I"></path><path d="M372 81c3-2 6-3 9-3 2 1 2 2 3 3 1 0 1 0 2-1l1 1h4 0l2 2h-3-1v1 1c1 2 1 3 1 4l-15 6c-2 3-6 5-9 7l-12 8c-2 2-5 4-7 6-4 3-8 7-11 10v2c-2 2-2 3-3 6l-2 1c-5 4-9 9-13 14-3 3-5 6-6 9-6 8-11 17-15 26l-4 6c0 1 0 2-1 3-3 2-5 7-6 11l-2 2-1 1h0c1-4 3-7 4-11 2-3 3-7 5-10l11-21 12-17c4-5 8-10 10-15v-3h1v2l2 1c1-1 1-2 2-3l-2-1-1-1v-1h0c0-1-1-1-2-1h-2l1-2h-1c0 1-1 1-1 1h-1l-2 1-2 1c-3 0-6 1-10 1-2 0-5 1-8 2-2 1-4 2-7 2l-4 2-5 2-3-1h-1c1-2 1-2 2-3s2-2 3-4l-4-1c4-1 7-2 10-3h1c0-3-2-5-3-7 3 0 6 1 8 2 3-2 9 1 12 0 2 0 4-3 6-4 3-1 5-2 6-4 1-1 4-3 5-3l10-7c7-5 14-10 22-13 4-1 8-4 12-5l2-1h1v-1z" class="f"></path><path d="M356 95l-1 1-1 1h2 0 1l-15 7c-1 0-2 1-4 1v1c1 0 2 1 3 1v1l-5 2c-5 1-9 3-13 5l-2 1h0c-1 1-1 1-3 1h0c3-2 5-4 7-6 3-2 7-4 10-6 2-1 6-2 8-4 4-3 8-4 13-6z" class="y"></path><path d="M353 101c2-1 4-2 6-2 1-1 1-1 2-1l-26 21-2 1h-3c-2 1-3 2-6 2v1c-1 0-2-1-3-1h0c-2-1-5 0-7-1l2-1c1 0 3 0 4-1s1-2 1-3h0l2-1c4-2 8-4 13-5l5-2v-1c-1 0-2-1-3-1v-1c2 0 3-1 4-1v1c4 0 7-5 11-4z" class="AI"></path><path d="M342 104v1c4 0 7-5 11-4-3 3-8 5-12 7h0v-1c-1 0-2-1-3-1v-1c2 0 3-1 4-1z" class="z"></path><path d="M325 120c1-2 2-2 4-3 3-1 4-2 6-5l1 1c0 2 0 4-1 6l-2 1h-3c-2-1-3-1-5 0z" class="AE"></path><path d="M321 116h0l2-1c4-2 8-4 13-5-2 2-4 3-6 4-1 1-2 1-3 2-2 0-3 2-4 2v2l1 1 1-1h0c2-1 3-1 5 0-2 1-3 2-6 2v1c-1 0-2-1-3-1h0c-2-1-5 0-7-1l2-1c1 0 3 0 4-1s1-2 1-3z" class="z"></path><path d="M288 117c3 0 6 1 8 2l4 1 8 1c3 1 7 1 10 2l2 1-9 3c-1 0-2 0-4 1-2 0-5 1-8 2-2 1-4 2-7 2l-4 2-5 2-3-1h-1c1-2 1-2 2-3s2-2 3-4l-4-1c4-1 7-2 10-3h1c0-3-2-5-3-7z" class="e"></path><path d="M283 136l-3-1h-1c1-2 1-2 2-3 2 1 1 0 3 0 1 0 3 1 3 1l1 1-5 2z" class="G"></path><path d="M291 124l1-1-1-1c1-1 2-1 3-1 1 2 2 3 2 4s-1 2-1 2c-2 1-2 1-3 1l-2-4h1z" class="O"></path><path d="M288 117c3 0 6 1 8 2l4 1c-1 1-2 1-4 1h-1-1c-1 0-2 0-3 1l1 1-1 1c0-3-2-5-3-7z" class="E"></path><path d="M308 121c3 1 7 1 10 2l2 1-9 3c-1 0-2 0-4 1-2 0-5 1-8 2-2 1-4 2-7 2 2-2 4-3 5-4 0-1 0-2-1-2l1-2c0-1-1 0 1-1 1 1 3 1 5 1h6 0 0l-1-3z" class="B"></path><path d="M372 81c3-2 6-3 9-3 2 1 2 2 3 3 1 0 1 0 2-1l1 1h4 0l2 2h-3-1v1 1l-16 7-12 6c-1 0-1 0-2 1-2 0-4 1-6 2-4-1-7 4-11 4v-1l15-7h-1 0-2l1-1 1-1c-5 2-9 3-13 6l-1-1v-1c-2 1-5 3-7 2 7-5 14-10 22-13 4-1 8-4 12-5l2-1h1v-1z" class="AE"></path><path d="M372 82c1-1 3-2 5-2h4v2h1 3v1c-2 2-4 2-6 3v1c-2 0-4 0-5 1-3 1-6 3-9 3h-1-1c0-1 2-2 2-2v-1c-2 1-3 2-5 2v-1l9-6 2-1h1z" class="AF"></path><path d="M372 82c1-1 3-2 5-2h4v2h1 3v1c-2 2-4 2-6 3v1c-2 0-4 0-5 1-3 1-6 3-9 3 1-1 3-1 5-2 1-1 3-2 4-2h2v-1c1 0 2 0 2-1 1 0 1-1 1-1v-1l-1 1c-2 0-5 2-7 2l1-1c2-1 3-2 4-3h0c-1-1-3 0-4 0h-1 1z" class="AI"></path><path d="M357 88c4-1 8-4 12-5l-9 6v1c2 0 3-1 5-2v1s-2 1-2 2h1c-2 3-6 3-8 4-5 2-9 3-13 6l-1-1v-1c-2 1-5 3-7 2 7-5 14-10 22-13z" class="AA"></path><path d="M360 89v1c2 0 3-1 5-2v1s-2 1-2 2h1c-2 3-6 3-8 4-5 2-9 3-13 6l-1-1v-1c6-2 12-6 18-10z" class="AI"></path><path d="M361 98l12-6 16-7c1 2 1 3 1 4l-15 6c-2 3-6 5-9 7l-12 8c-2 2-5 4-7 6-4 3-8 7-11 10v2c-2 2-2 3-3 6l-2 1c-5 4-9 9-13 14-3 3-5 6-6 9-6 8-11 17-15 26l-4 6c0 1 0 2-1 3-3 2-5 7-6 11l-2 2-1 1h0c1-4 3-7 4-11 2-3 3-7 5-10l11-21 12-17c4-5 8-10 10-15v-3h1v2l2 1c1-1 1-2 2-3l-2-1-1-1v-1h0c0-1-1-1-2-1h-2l1-2v-1-1c3 0 4-1 6-2h3l2-1 26-21z" class="AQ"></path><defs><linearGradient id="L" x1="355.866" y1="102.722" x2="359.937" y2="109.114" xlink:href="#B"><stop offset="0" stop-color="#eaccce"></stop><stop offset="1" stop-color="#f1f8fa"></stop></linearGradient></defs><path fill="url(#L)" d="M361 98l12-6 16-7c1 2 1 3 1 4l-15 6c-3 1-5 2-8 4l-5 3-12 8-1 1c-3 2-6 5-9 7-3 4-7 7-10 12l-2-1-1-1v-1h0c0-1-1-1-2-1h-2l1-2v-1-1c3 0 4-1 6-2h3l2-1 26-21z"></path><path d="M330 120h3c-2 2-3 3-6 4l-2 2h-2l1-2v-1-1c3 0 4-1 6-2z" class="AI"></path><path d="M228 319l2-1 1 1h3c1-1 2-1 3-1l1 1h-1l1 1-2 1v1c3 1 5 2 6 5h4l1-1c2 5 1 13 0 18l-1 15h0c-1 2-1 3-1 5v1 1l-2 1-1 1h0c-1 1-2 1-3 2s-1 1-1 2c-2 2-4 3-7 4h-1c-1-1-1-2-1-3l-1-1c-1 0-5-1-7-1-1 1-1 1-3 1h-1-3-1l-3-1-1 4c-1-1-3-1-4-1v2h-1-2-2c-2 0-3-1-5-2l1-5c-1-1-1-1 0-2h0l-1-1h-1-1l3-4v-3c1-2 1-3 1-5v-3l-1-1c0-1-1-1-2-2l1-3v-3h0c1 0 1 0 2-1l2 2c1-1 2-2 2-3 2-1 1-2 3-2v2h1l1 1c0 2 0 2 1 4l4-5c2-3 5-5 7-8 1 0 2 0 2 1 4-3 6-10 8-13v-1z" class="e"></path><path d="M218 348c1 1 1 1 1 3h1l2-1v1c0 1 0 2-1 2s-2-1-3-1v-4z" class="G"></path><path d="M224 344h2c1 2 2 6 5 6v1h-2c-1 1-1 1-2 0-1 0-2-2-2-3-1 0-1-3-1-4z" class="M"></path><path d="M224 344v-3l1-1 2 1c3 3 5 7 6 10l-1 1-1-1v-1c-3 0-4-4-5-6h-2z" class="G"></path><path d="M228 372c0-1 0-1 1-2 0-1 0-1-1-1v-1h1v-2c1-2 0-4 1-5h1 1c0-1 0-1 1-2 0 1 1 2 1 3l-2 2c1 0 1 1 2 1-1 2-2 5-3 7 0 0-1 1-1 2l1 1v1h-1c-1-1-1-2-1-3l-1-1z" class="M"></path><path d="M224 336l2 2c-2 1-3 1-5 2v5l1 1v1l-1-1h0l-2-1c0 1-1 2-1 3v4c-1 2-2 3-2 5-1 0-1-1-1-1-1 1-1 1-1 2s0 0-1 1c0 1 1 1 2 2h1c-1 1-1 1 0 2 0 1 0 2 1 4l2 2c-1 1-1 2-2 2h-4v1l-3-1-1 4c-1-1-3-1-4-1l-2-2c1-4 3-8 4-13 1-1 1-3 2-4 1-3 3-4 5-5 1-2 2-3 3-4 2-4 4-7 7-10z" class="D"></path><path d="M207 359c1-1 1-3 2-4 1-3 3-4 5-5l-1 3c-1 3-2 6 0 10h0c-1 1 0 2-1 3l-3-1c-1-2-1-3-1-5l-1-1z" class="H"></path><path d="M207 359l1 1c0 2 0 3 1 5l3 1c1-1 0-2 1-3 1 1 3 2 4 4l2 2c-1 1-1 2-2 2h-4v1l-3-1-1 4c-1-1-3-1-4-1l-2-2c1-4 3-8 4-13z" class="M"></path><path d="M203 372h1l1-1c2-1 3 0 5 0l-1 4c-1-1-3-1-4-1l-2-2z" class="z"></path><path d="M213 363c1 1 3 2 4 4l2 2c-1 1-1 2-2 2h-4c-1-1-2-1-4-1h-1v-3c1-1 1-1 1-2l3 1c1-1 0-2 1-3z" class="B"></path><defs><linearGradient id="M" x1="207.314" y1="342.969" x2="222.437" y2="348.782" xlink:href="#B"><stop offset="0" stop-color="#1f0604"></stop><stop offset="1" stop-color="#360707"></stop></linearGradient></defs><path fill="url(#M)" d="M228 319l2-1 1 1h3c1-1 2-1 3-1l1 1h-1c-3 2-6 4-7 8v3c0 2 0 3 1 4v2 1c-1 1-1 2-1 3h-3l-1-2-2-2c-3 3-5 6-7 10-1 1-2 2-3 4-2 1-4 2-5 5-1 1-1 3-2 4-1 5-3 9-4 13l2 2v2h-1-2-2c-2 0-3-1-5-2l1-5c-1-1-1-1 0-2h0l-1-1h-1-1l3-4v-3c1-2 1-3 1-5v-3l-1-1c0-1-1-1-2-2l1-3v-3h0c1 0 1 0 2-1l2 2c1-1 2-2 2-3 2-1 1-2 3-2v2h1l1 1c0 2 0 2 1 4l4-5c2-3 5-5 7-8 1 0 2 0 2 1 4-3 6-10 8-13v-1z"></path><path d="M224 336c0-1 0-1 1-2h1c0-1 0-2 1-3h0c0 2 0 3 1 5 1-1 1-1 1-2s0-2 1-3v-1c0 2 0 3 1 4v2 1c-1 1-1 2-1 3h-3l-1-2-2-2z" class="Q"></path><path d="M218 332c1 0 2 0 2 1l-7 8c-5 8-15 17-17 27v1h0c-1-1-1-1 0-2h0l-1-1h-1-1l3-4 11-17 4-5c2-3 5-5 7-8z" class="AB"></path><path d="M218 332c1 0 2 0 2 1l-7 8 1-2c1-1 2-2 2-3-1 1-2 3-4 4h-1c2-3 5-5 7-8z" class="AI"></path><path d="M201 340c2-1 1-2 3-2v2h1l1 1c0 2 0 2 1 4l-11 17v-3c1-2 1-3 1-5v-3l-1-1c0-1-1-1-2-2l1-3v-3h0c1 0 1 0 2-1l2 2c1-1 2-2 2-3z" class="M"></path><path d="M203 344h1c-1 1-1 1-1 2l-1 3c0 1-1 3-3 4l-1-1c0-4 3-6 5-8z" class="s"></path><path d="M195 342h0c1 0 1 0 2-1l2 2c-1 2-1 5-3 7 0-1-1-1-2-2l1-3v-3z" class="F"></path><path d="M230 330v-3c1-4 4-6 7-8l1 1-2 1v1c3 1 5 2 6 5h4l1-1c2 5 1 13 0 18l-1 15h0c-1 2-1 3-1 5v1 1l-2 1-1 1h0c-1 1-2 1-3 2s-1 1-1 2c-2 2-4 3-7 4v-1l-1-1c0-1 1-2 1-2 1-2 2-5 3-7v-1c2-4 0-8-1-12v-1c-1-3-3-7-6-10v-1h3c0-1 0-2 1-3v-1-2c-1-1-1-2-1-4z" class="h"></path><path d="M230 330v-3c1-4 4-6 7-8l1 1-2 1v1c3 1 5 2 6 5 1 1 0 2 0 4s1 4 0 5c-2 4-2 11-4 15-1-1-1-1-1-2l-1 3c0 1 0 1-1 1s-1-1-2-1v-1c-1-3-3-7-6-10v-1h3c0-1 0-2 1-3v-1-2c-1-1-1-2-1-4z" class="G"></path><path d="M236 322c3 1 5 2 6 5 1 1 0 2 0 4-1-1-2-1-2-2v-2l-1-1c0-1-1-1-1-1v-2h-1l-2 1v-2h1zm0 7l2-1v1 1 1c1 2 0 5 0 7l1 2-1 1c-1-1-1-2-2-3 0-1-1-2-2-3-1 0-1 0-2 1h-1 0v-2h1 1 1l1-1v1l1-1v-4z" class="m"></path><path d="M230 330v-3c1-4 4-6 7-8l1 1-2 1v1h-1v2c-1 1-1 2-1 3l2 2v4l-1 1v-1l-1 1h-1-1-1c-1-1-1-2-1-4z" class="B"></path><path d="M231 336h0l6 13-1 3c0 1 0 1-1 1s-1-1-2-1v-1c-1-3-3-7-6-10v-1h3c0-1 0-2 1-3v-1z" class="J"></path><defs><linearGradient id="N" x1="179.355" y1="431.426" x2="181.223" y2="499.247" xlink:href="#B"><stop offset="0" stop-color="#010101"></stop><stop offset="1" stop-color="#460302"></stop></linearGradient></defs><path fill="url(#N)" d="M172 418c2 1 2 1 3 3h0c2 0 2 0 3-2 1 1 1 2 2 3l1 1c2 1 3 3 5 4h3c1 2 2 3 4 3h0l2 2h0l2-9v-1h1v14l-1 2v1c1 1 2 1 4 1 6 4 13 10 18 15 1 0 2 2 3 2l7 9c2 3 4 5 6 8 1 1 3 2 4 4-1 0-2 0-2 2 1 2 1 4 1 6l-3 3-4 6c-3 3-4 5-6 9h-1v-1c0-3 1-6 1-9 0-4-2-8-4-12-4-7-9-14-15-19-12-9-24-20-39-23h0c-8-1-15 1-23 3-12 9-17 18-20 32l-1 10-2 2v-3-1-2c1-1 0-3 1-5h0v-2l1-1v-3l1-1c0-2 0-4-1-5v-1l4-11-1-1 1-2c5-10 12-17 23-20 8-3 14-2 22 1v-1l-3-2-1-2c-2-2-3-4-4-6 2 1 3 2 4 3l1 1v-1-1h3v-3z"></path><path d="M231 495l-1-1c0-2-1-4-1-5l1-2c1 2 1 2 3 2h2l-4 6z" class="K"></path><path d="M198 447h4c2 2 5 4 8 5l6 5h-1-1c-2-1-4-3-7-4s-6-4-9-6z" class="L"></path><path d="M230 487h0c0-2-1-4-2-5-1-3-3-6-4-9 3 3 9 10 9 15v1c-2 0-2 0-3-2z" class="E"></path><path d="M224 465c2 0 3 1 5 1 2 3 4 5 6 8 1 1 3 2 4 4-1 0-2 0-2 2h0l-13-15z" class="B"></path><path d="M206 463v-1l-1-2h1c5 5 11 10 14 16 0 2 1 3 1 4v2c-4-7-9-14-15-19z" class="a"></path><path d="M173 433l6 1 3 1 10 5 10 7h-4c-3-2-7-3-11-5-1 0-4-1-5-2l1-1h1c-1-2-4-2-5-3-2-1-5-2-6-3z" class="I"></path><path d="M150 429c8-3 14-2 22 1v-1c3 1 5 3 7 5l-6-1-3-1-1-1c-6-1-14 1-19 2l-3-1c0-1 1-1 1-1l3-1-1-1z" class="V"></path><path d="M126 451l1-2c5-10 12-17 23-20l1 1-3 1s-1 0-1 1l3 1c-9 4-18 11-23 19l-1-1z" class="K"></path><path d="M144 443c2-2 4-3 7-3v-1h8c2 0 5-1 7 0 9 1 18 6 26 10 5 3 10 7 14 11h-1l1 2v1c-12-9-24-20-39-23h0c-8-1-15 1-23 3z" class="e"></path><path d="M179 430h1c2 2 4 3 7 4 2 0 5 2 7 3 1 0 2 1 3 2s2 1 4 1c6 4 13 10 18 15 1 0 2 2 3 2l7 9c-2 0-3-1-5-1l-8-8-6-5c-3-1-6-3-8-5l-10-7-10-5c-1-3-3-3-4-4l1-1z" class="D"></path><path d="M192 440l2-1c1 1 1 1 1 2h1 3c1 1 1 2 1 3 1 0 3 2 4 3v-1l1 1c2 1 4 1 4 4l1 1c-3-1-6-3-8-5l-10-7z" class="B"></path><path d="M172 418c2 1 2 1 3 3h0c2 0 2 0 3-2 1 1 1 2 2 3l1 1c2 1 3 3 5 4h3c1 2 2 3 4 3h0l2 2h0l2-9v-1h1v14l-1 2v1c-1-1-2-2-3-2-2-1-5-3-7-3-3-1-5-2-7-4h-1l-1 1c1 1 3 1 4 4l-3-1c-2-2-4-4-7-5l-3-2-1-2c-2-2-3-4-4-6 2 1 3 2 4 3l1 1v-1-1h3v-3z" class="F"></path><path d="M178 419c1 1 1 2 2 3 0 1 0 2-1 3l-4-4c2 0 2 0 3-2z" class="R"></path><path d="M180 422l1 1c2 1 3 3 5 4 1 2 4 5 6 7l-8-5c-2-1-3-2-5-4 1-1 1-2 1-3z" class="Q"></path><path d="M172 418c2 1 2 1 3 3h-1c1 4 5 6 7 9h-1-1c-1 0-2-1-3-2-2-2-4-5-7-6v-1h3v-3z" class="o"></path><path d="M164 419c2 1 3 2 4 3l1 1v-1c3 1 5 4 7 6 1 1 2 2 3 2l-1 1c1 1 3 1 4 4l-3-1c-2-2-4-4-7-5l-3-2-1-2c-2-2-3-4-4-6z" class="G"></path><defs><linearGradient id="O" x1="252.53" y1="517.659" x2="265.075" y2="516.33" xlink:href="#B"><stop offset="0" stop-color="#2a0202"></stop><stop offset="1" stop-color="#570705"></stop></linearGradient></defs><path fill="url(#O)" d="M238 486l1 2c1-1 2-1 3-2h1c1 1 2 1 3 2s2 2 4 3l2 3 3 2h0v-1c1-1 2-1 3-1 1 1 2 5 3 7v-6l3 8h0l1-1c0 1 0 1 1 2v2c1 1 0 1 1 2v1l1 2c0 1 1 2 1 3l1-1c0 1 1 2 1 3 1 2 2 4 2 6 0 1 1 2 2 3h1c1 1 1 2 1 3v1 1c1 2 0-1 1 1v1c0 1 1 2 2 4l5 13-1 2 1 1c0 1 0 1 1 2 0 1 0 2 1 3 1 2 1 2 1 3v2h-1c3 9 8 16 13 24 1 2 3 5 3 7l-1 1c1 2 5 6 5 8v1l1 1-1 1h0l-2-1c-2-2-3-4-5-4l-4-3-1-2-2-2c-2-4-5-8-7-12-2-3-4-5-6-7l-7-11c-2-1-4-3-6-5l-4-3h-1-1-1l-1 2c0-2-1-2-2-3 1-3 1-6 0-9-5-10-12-25-21-31-3-2-6-3-9-4h-3-1-1c1-3 1-5 2-7v1h1c2-4 3-6 6-9l4-6 3-3z"></path><path d="M257 521v-1l1-1c2 3 3 6 4 9l-1-1v3l-4-9h0z" class="J"></path><path d="M246 496c2 1 3 2 4 4 1 1 2 2 2 4h-2 0c-1-2-4-5-4-8z" class="Q"></path><path d="M261 530v-3l1 1c2 3 4 5 4 8l-1 1v1h-1l-3-8z" class="i"></path><path d="M246 488c1 1 2 2 4 3l2 3v2c-2-1-4-3-6-4-1-1-2-1-3-2 1-1 2-2 3-2z" class="B"></path><path d="M257 521l4 9 3 8v2c-3-3-4-7-6-10 1 0 2 1 2 2v-2l-1-3-1-1c-1-2-1-3-1-5z" class="T"></path><path d="M265 538v-1l1-1c3 5 5 11 6 17-1-1-2-2-2-3l-1 1h0l-4-13z" class="h"></path><path d="M250 504h2c0 1 0 3 1 4 1 2 6 10 5 11l-1 1v1c-1-1-2-3-2-4-2-4-3-7-4-10l-1-3z" class="H"></path><path d="M243 510l1-2s1 0 1-1l1-1 1-1v-2h1v2c0 1 1 1 3 2 1 3 2 6 4 10 0 1 1 3 2 4h0c0 2 0 3 1 5l1 1 1 3v2c0-1-1-2-2-2l-8-15-2-2c0 2 0 4 1 6-1-1-1-1-1-2l-3-3v1h0c-2-2-2-3-2-5z" class="o"></path><path d="M245 514v-1c0-1 0-2-1-3v-1c3 0 5 3 6 5v1l-2-2c0 2 0 4 1 6-1-1-1-1-1-2l-3-3z" class="O"></path><defs><linearGradient id="P" x1="237.855" y1="503.581" x2="247.632" y2="508.322" xlink:href="#B"><stop offset="0" stop-color="#130000"></stop><stop offset="1" stop-color="#300202"></stop></linearGradient></defs><path fill="url(#P)" d="M246 496h0c0 3 3 6 4 8h0l1 3c-2-1-3-1-3-2v-2h-1v2l-1 1-1 1c0 1-1 1-1 1l-1 2h0l-3 3c-3-1-5-1-7-2v-1c1-1 2-1 3-2 4-3 7-8 10-12z"></path><path d="M249 519c-1-2-1-4-1-6l2 2 8 15c2 3 3 7 6 10v-2h1l4 13h0l4 12c-2-1-4-3-6-5l-4-3 2-1c-1-4-2-8-4-12-3-9-8-16-12-23z" class="K"></path><path d="M263 555l2-1c1 0 1 2 2 2 1 1 1 0 2 1l-2 1h0l-4-3z" class="V"></path><path d="M252 494l3 2h0v-1c1-1 2-1 3-1 1 1 2 5 3 7 1 3 1 6 3 9v5c4 11 7 21 9 32h-1c-2-5-3-10-5-15-1-5-4-11-6-17-3-7-5-13-9-19v-2z" class="D"></path><path d="M255 495c1-1 2-1 3-1 1 1 2 5 3 7 1 3 1 6 3 9v5c-2-4-3-9-6-14 0-2-1-4-3-5v-1z" class="i"></path><path d="M233 511c2 1 4 1 7 2l3-3h0c0 2 0 3 2 5h0v-1l3 3c0 1 0 1 1 2 4 7 9 14 12 23 2 4 3 8 4 12l-2 1h-1-1-1l-1 2c0-2-1-2-2-3 1-3 1-6 0-9-5-10-12-25-21-31-3-2-6-3-9-4 2 0 3 0 6 1z" class="L"></path><path d="M238 486l1 2c1-1 2-1 3-2h1c1 1 2 1 3 2-1 0-2 1-3 2-1 0-2 1-3 2 1 0 3-1 4 0l4 2c-1 1-1 2-2 2-3 4-6 9-10 12-1 1-2 1-3 2v1c-3-1-4-1-6-1h-3-1-1c1-3 1-5 2-7v1h1c2-4 3-6 6-9l4-6 3-3z" class="AJ"></path><path d="M238 486l1 2c1-1 2-1 3-2h1c1 1 2 1 3 2-1 0-2 1-3 2-1 0-2 1-3 2l-2 1c-4 4-13 11-14 16v1h-1-1c1-3 1-5 2-7v1h1c2-4 3-6 6-9l4-6 3-3z" class="B"></path><path d="M243 486c1 1 2 1 3 2-1 0-2 1-3 2-1 0-2 1-3 2l-2 1c1-2 2-3 3-5l2-2z" class="a"></path><defs><linearGradient id="Q" x1="274.13" y1="551.049" x2="283.975" y2="547.433" xlink:href="#B"><stop offset="0" stop-color="#0a0000"></stop><stop offset="1" stop-color="#630d0d"></stop></linearGradient></defs><path fill="url(#Q)" d="M261 495l3 8h0l1-1c0 1 0 1 1 2v2c1 1 0 1 1 2v1l1 2c0 1 1 2 1 3l1-1c0 1 1 2 1 3 1 2 2 4 2 6 0 1 1 2 2 3h1c1 1 1 2 1 3v1 1c1 2 0-1 1 1v1c0 1 1 2 2 4l5 13-1 2 1 1c0 1 0 1 1 2 0 1 0 2 1 3 1 2 1 2 1 3v2h-1c3 9 8 16 13 24 1 2 3 5 3 7l-1 1c1 2 5 6 5 8v1l1 1-1 1h0l-2-1c-2-2-3-4-5-4l-4-3-1-2-2-2c-2-4-5-8-7-12-2-3-4-5-6-7l-7-11-4-12 1-1c0 1 1 2 2 3 0 2 2 8 4 10h0l-4-16h1c-2-11-5-21-9-32v-5c-2-3-2-6-3-9v-6z"></path><path d="M295 595c0-2-1-4-2-6v-1c4 5 8 11 14 15l1 1-1 1h0l-2-1c-2-2-3-4-5-4l-4-3-1-2z" class="AF"></path><defs><linearGradient id="R" x1="279.239" y1="577.608" x2="287.703" y2="575.194" xlink:href="#B"><stop offset="0" stop-color="#b61c25"></stop><stop offset="1" stop-color="#de4645"></stop></linearGradient></defs><path fill="url(#R)" d="M272 547h1c1 7 3 14 6 20 2 4 5 7 8 10 2 3 3 5 4 7 1 1 2 3 2 4v1c1 2 2 4 2 6l-2-2c-2-4-5-8-7-12-2-3-4-5-6-7l-7-11-4-12 1-1c0 1 1 2 2 3 0 2 2 8 4 10h0l-4-16z"></path><path d="M269 514l1-1c0 1 1 2 1 3 1 2 2 4 2 6 0 1 1 2 2 3h1c1 1 1 2 1 3v1 1c1 2 0-1 1 1v1c0 1 1 2 2 4l5 13-1 2 1 1c0 1 0 1 1 2 0 1 0 2 1 3 1 2 1 2 1 3v2h-1c3 9 8 16 13 24 1 2 3 5 3 7l-1 1-1-1c-9-10-13-22-19-34-1-4-3-8-4-12-2-4-3-9-4-13v-4c-1-2-1-4-2-7 0-3-2-6-3-9z" class="F"></path><defs><linearGradient id="S" x1="177.048" y1="388.478" x2="209.144" y2="412.95" xlink:href="#B"><stop offset="0" stop-color="#050201"></stop><stop offset="1" stop-color="#3d0807"></stop></linearGradient></defs><path fill="url(#S)" d="M193 366h1 1l1 1h0c-1 1-1 1 0 2l-1 5c2 1 3 2 5 2h2 2 1v-2c1 0 3 0 4 1l1-4 3 1h1 3 1c2 0 2 0 3-1 2 0 6 1 7 1l1 1c0 1 0 2 1 3h1c3-1 5-2 7-4 1 2 0 3 0 5 1 0 2-1 3-1v2s1 0 0 1v2c-2 3-3 5-6 5h-2v2l-1 1-1-1v-2c-2 1-3 0-5 0h-1l-1-1c-1-1-2-1-4-2 1 1 1 2 3 2 0 1 0 1 1 2 0 0 1 0 1 1 1 2 2 3 1 5l1 1v1c0 1 1 1 1 2-2 0-2 1-3 3h-2c0 1-1 2-1 3v1l-2 1v1 1l1-1h0c-1 2-3 4-4 6s-2 4-4 6l2 2c0 1 1 1 2 2-2 0-4 1-6 0-2 0-4 0-5-1s-1 0-1-1c-2 0-3-1-4-2-2-3-3-6-3-9-1 1-1 1-1 2v1 10 1l-2 9h0l-2-2h0c-2 0-3-1-4-3h-3c-2-1-3-3-5-4l-1-1c-1-1-1-2-2-3-1 2-1 2-3 2h0c-1-2-1-2-3-3v3h-3v1 1l-1-1c-1-1-2-2-4-3 1 2 2 4 4 6l1 2c-3-2-6-3-9-4l-1-1v-1-1l1-1c-1-1-2-3-4-3l-1-1h-3-2v-1c0-2 2-3 3-5 2-1 5-4 6-6l14-12c3-3 7-7 11-7v-2c-2 1-3 2-4 2l-2-1c6-5 11-11 15-17z"></path><path d="M164 419l-1-3c-1-2 0-4 0-6h1c0 5 2 8 5 11v1 1l-1-1c-1-1-2-2-4-3z" class="AG"></path><path d="M182 392l6-3c3 0 5-1 8-3l-1 4c-2 2-3 1-4 4l-1 1-2-1c-1 1-1 2-2 2h-2l-2 2c-1-1-2-1-3-2l-1 1c-1-1-1-1-2-1 2-2 4-3 6-4z" class="O"></path><path d="M183 393l8-3c-1 1-2 2-4 3-1 0 0 0-1 1l-6 1v-1l3-1z" class="o"></path><path d="M176 396c2-2 4-3 6-4l1 1-3 1v1l6-1-2 2-2 2c-1-1-2-1-3-2l-1 1c-1-1-1-1-2-1z" class="S"></path><path d="M193 366h1 1l1 1h0c-1 1-1 1 0 2l-1 5v-2l-1 1c0 1 1 2 2 3 0 1 2 2 3 3h-5 1c-1-2-3-4-4-5-2 2-4 4-5 6v3l1 1-1 1s-1 0-2-1v-2c-2 1-3 2-4 2l-2-1c6-5 11-11 15-17z" class="AF"></path><path d="M159 403l14-12c-2 4-3 7-7 11 0 1-1 3-2 4v4h-1c0 2-1 4 0 6l1 3c1 2 2 4 4 6l1 2c-3-2-6-3-9-4l-1-1v-1-1l1-1c-1-1-2-3-4-3l-1-1h-3-2v-1c0-2 2-3 3-5 2-1 5-4 6-6z" class="L"></path><path d="M159 403v1c-1 2-3 4-4 7l5 4c0 1 1 2 1 4h-1c-1-1-2-3-4-3l-1-1h-3-2v-1c0-2 2-3 3-5 2-1 5-4 6-6z" class="k"></path><path d="M160 415c0-2 0-4 2-6 1-2 1-6 4-7 0 1-1 3-2 4v4h-1c0 2-1 4 0 6l1 3c1 2 2 4 4 6l1 2c-3-2-6-3-9-4l-1-1v-1-1l1-1h1c0-2-1-3-1-4z" class="J"></path><path d="M210 371l3 1h1 3 1c2 0 2 0 3-1 2 0 6 1 7 1l1 1c0 1 0 2 1 3h1c3-1 5-2 7-4 1 2 0 3 0 5 1 0 2-1 3-1v2s1 0 0 1v2c-2 3-3 5-6 5h-2v2l-1 1-1-1v-2c-2 1-3 0-5 0h-1l-1-1c-1-1-2-1-4-2-1-1-3-1-5-1-6-1-11-2-16-3-1-1-3-2-3-3-1-1-2-2-2-3l1-1v2c2 1 3 2 5 2h2 2 1v-2c1 0 3 0 4 1l1-4z" class="f"></path><path d="M210 371l3 1h1l1 2 1-1c1 0 3 0 5 1l1 1h0-4v-1l-1 1c-1 0-2 0-2 1-1-1-2-1-3-1h-3l1-4z" class="y"></path><path d="M205 374c1 0 3 0 4 1h3c1 0 2 0 3 1 0-1 1-1 2-1l2 1v1l-8 1c-3 0-8-1-11-2h2 2 1v-2z" class="M"></path><path d="M234 378c-1 0 0 0-1-1-1 0 0 0-1 1h-3c-1 0-1-1-2 0h0c-2-1-3-1-4-1h-1c0-1 1-1 2-1 0-1 1-1 2-2h1l1-1h1c0 1 0 2 1 3h1c3-1 5-2 7-4 1 2 0 3 0 5-2 0-3 0-4 1z" class="y"></path><path d="M199 379c-1-1-3-2-3-3-1-1-2-2-2-3l1-1v2c2 1 3 2 5 2 3 1 8 2 11 2 4 1 9 2 13 3 1 1 2 1 3 2v1l-3 1c-1-1-2-1-4-2-1-1-3-1-5-1-6-1-11-2-16-3z" class="Ad"></path><path d="M238 377c1 0 2-1 3-1v2s1 0 0 1v2c-2 3-3 5-6 5h-2v2l-1 1-1-1v-2c-2 1-3 0-5 0h-1l-1-1 3-1v-1c-1-1-2-1-3-2 1 0 3 0 4 1l4-1v-1l2-2c1-1 2-1 4-1z" class="AF"></path><path d="M224 381c1 0 3 0 4 1h0 4v1 2c-2 0-5 0-7 1h0l-1-1 3-1v-1c-1-1-2-1-3-2z" class="AR"></path><path d="M238 377c1 0 2-1 3-1v2c-1 0-2 1-2 2-2 1-4 1-7 1v-1l2-2c1-1 2-1 4-1z" class="u"></path><path d="M241 378s1 0 0 1v2c-2 3-3 5-6 5h-2v2l-1 1-1-1v-2c-2 1-3 0-5 0h-1 0c2-1 5-1 7-1s4 0 5-2h2 0v-3c0-1 1-2 2-2z" class="y"></path><path d="M190 395l1-1 1 2-1 5 1 2h0v1c2 0 2 0 2 2l2-1v4l1 2v1 10 1l-2 9h0l-2-2h0c-2 0-3-1-4-3h-3c-2-1-3-3-5-4l-1-1c-1-1-1-2-2-3-1 2-1 2-3 2h0c-1-2-1-2-3-3l-1-1c0-5-1-9 1-15 1-2 2-4 4-6 1 0 1 0 2 1l1-1c1 1 2 1 3 2l2-2h2c1 0 1-1 2-2l2 1z" class="H"></path><path d="M186 396c1 0 1-1 2-2l2 1c-1 1-3 2-4 3v-2z" class="Q"></path><path d="M187 402c0-1 0-1-1-1h0v-1h1c1-1 1-1 1-2h1c1 0 2 1 2 3l1 2c-2 0-3-1-5-1z" class="I"></path><path d="M187 402c2 0 3 1 5 1h0v1l-2 3c-1 1-1 1-2 1h0c0-1-1-2-1-3h-1l1-3z" class="j"></path><path d="M189 424v-1c-1-2-1-2-2-3-2-3-1-6-1-9 1 1 1 2 3 3l-1 2c1 1 2 0 2 2v2h1c0 1 1 2 2 3l-1 1h-3z" class="O"></path><path d="M184 396h2v2 1c-2 1-1 2-2 3s-3 2-3 3l-1-1-2 1v-1h0c1-2 2-4 4-6l2-2z" class="E"></path><path d="M172 402c1-2 2-4 4-6 1 0 1 0 2 1l1-1c1 1 2 1 3 2-2 2-3 4-4 6h0l-2 2v1l-1-1c0-1-2-3-3-4z" class="c"></path><path d="M176 406c1 1 2 2 2 3 1 2 4 3 5 5 1 1 1 4 1 5-1 1-2 1-4 1v2c-1-1-1-2-2-3s-1-3-1-4c-1 0-1 0-2-1-1-3 0-5 1-7v-1z" class="T"></path><path d="M172 418l-1-1c0-5-1-9 1-15 1 1 3 3 3 4l1 1c-1 2-2 4-1 7 1 1 1 1 2 1 0 1 0 3 1 4-1 2-1 2-3 2h0c-1-2-1-2-3-3z" class="n"></path><path d="M194 406l2-1v4l1 2v1 10 1l-2 9h0l-2-2h0c-2 0-3-1-4-3h-3c-2-1-3-3-5-4 1-1 2-1 4-1l4 2h3l1-1v-7c-1-2-1-4-1-6v-4h2z" class="G"></path><path d="M192 410c0-1 1-2 1-3l1 1v2h0c0 1 0 2-1 4v2c-1-2-1-4-1-6z" class="a"></path><path d="M194 406l2-1v4l1 2v1c-1 0-2-1-3-2h0v-2l-1-1c0 1-1 2-1 3v-4h2z" class="m"></path><path d="M181 423c1-1 2-1 4-1l4 2h3l-2 2c-1 0-2 0-3-1 0 1 0 1-1 2-2-1-3-3-5-4z" class="B"></path><path d="M196 386c3-1 7-1 11-1 7 1 11 3 16 7 1 1 2 1 3 1l1 1v1c0 1 1 1 1 2-2 0-2 1-3 3h-2c0 1-1 2-1 3v1l-2 1v1 1l1-1h0c-1 2-3 4-4 6s-2 4-4 6l2 2c0 1 1 1 2 2-2 0-4 1-6 0-2 0-4 0-5-1s-1 0-1-1c-2 0-3-1-4-2-2-3-3-6-3-9-1 1-1 1-1 2l-1-2v-4l-2 1c0-2 0-2-2-2v-1h0l-1-2 1-5-1-2c1-3 2-2 4-4l1-4z" class="G"></path><path d="M199 404l1 1c1 1 1 1 1 3l-1 1h-2v-4l1-1z" class="AG"></path><path d="M217 395c2 1 4 2 5 4l-1 1c-2 0-3-2-5-3 0-1 0-1 1-2z" class="M"></path><path d="M200 399c1 1 2 1 3 2h0c1 1 1 2 2 2l-1 3h-1l-2-3-1-4z" class="D"></path><path d="M213 418l2 2c0 1 1 1 2 2-2 0-4 1-6 0-1-1-1-1-2-1h-1c1-1 2 0 3-1l2-2zm-16-21c1 0 1 0 1 1 1 0 1 1 2 1l1 4v1l-1-1-1 1-1 1c0-1 0-2-1-3v-1c-1-1 0-3 0-4z" class="p"></path><path d="M203 401h0c1 0 1 0 2-1 1 0 0 0 1-1h1c0 1 0 2 1 3v2c1 0 1 0 2 1 1 0 2-1 2 1l-1 1 1 1c-1 1-2 0-3 0-1-1-2-2-2-3l-1-2h-1c-1 0-1-1-2-2zm17 4l-2 2-1 1-1 2c0 1-1 1-1 2l-2-1-1 1h-1v-1l1-2 1-1c0-1 0-2 1-3h2c1 0 2-2 3-2l3 1-2 1z" class="M"></path><path d="M205 403h1l1 2c0 1 1 2 2 3v1l-1 1-1-1h-3v1c1 1 1 2 2 4h1l-1 1c-1 0-2 0-3-1 0-1 0-2-1-3v-1c0-1 1-3 1-4h1l1-3z" class="a"></path><path d="M197 391c1-1 2-1 4-1h-1l2 1v3l-1 1c-1 0-2 0-3 1l-1 1c0 1-1 3 0 4v1c1 1 1 2 1 3v4h0c-1 1-1 1-1 2l-1-2v-4l-2 1c0-2 0-2-2-2v-1h0l-1-2 1-5 2-1c1-1 3-2 3-4z" class="h"></path><path d="M202 394h-2l-2 1v-1c0-1 1-2 2-4l2 1v3z" class="J"></path><path d="M194 395c0 1 1 1 0 2v5s-1 1-2 1h0l-1-2 1-5 2-1z" class="B"></path><path d="M196 386c3-1 7-1 11-1 7 1 11 3 16 7 1 1 2 1 3 1l1 1v1c0 1 1 1 1 2-2 0-2 1-3 3h-2c1-2 2-3 3-5-1 0-2 0-3 1 0 1 0 2-1 3-1-2-3-3-5-4h0 0c-1-1-2-2-3-2-2 1-3 1-4 2 1 0 2 1 2 1 0 1 0 1-1 2l-3-3h-1c-2-1-3-1-5-2v1-3l-2-1h1c-2 0-3 0-4 1 0 2-2 3-3 4l-2 1-1-2c1-3 2-2 4-4l1-4z" class="D"></path><path d="M197 391c1-2 2-2 3-3 1 1 2 1 3 2h0c1 1 3 2 4 2v1c-1 0-2 0-2-1-1 1-2 1-3 1v1-3l-2-1h1c-2 0-3 0-4 1z" class="i"></path><path d="M196 386c3-1 7-1 11-1l-3 2-1 3c-1-1-2-1-3-2-1 1-2 1-3 3 0 2-2 3-3 4l-2 1-1-2c1-3 2-2 4-4l1-4z" class="E"></path><path d="M208 392v-1l-2-1 1-1 5-1 2 3c1-1 1-1 2-1h3 0c1 1 2 2 4 2-1 1-2 2-3 2v1h1 1l1 1c0 1 0 2-1 3-1-2-3-3-5-4h0 0c-1-1-2-2-3-2-2 1-3 1-4 2-1-1-2-2-2-3z" class="m"></path><path d="M208 392c2-1 2-1 4 0l2 1c-2 1-3 1-4 2-1-1-2-2-2-3z" class="e"></path><defs><linearGradient id="T" x1="218.038" y1="225.779" x2="248.971" y2="216.004" xlink:href="#B"><stop offset="0" stop-color="#1c0503"></stop><stop offset="1" stop-color="#631011"></stop></linearGradient></defs><path fill="url(#T)" d="M195 184h4c2 0 3 1 5 1l1-1c1 1 1 3 2 4 1-2 1-2 3-2l2 2c2 2 4 4 4 7l1-1c0 2 0 3 1 4l1-1c1-1 2 0 3 1l2 1 10 8 9 6c2 2 7 5 8 8l2 2v1l-5-3v1l7 9 1 3c1 2 3 4 3 6 0 1-1 2-2 3v3l1 1c0 1 0 3-1 4v3 2 1 5c-1 1 0 2 0 3l2 1-1 4v6c0 4-1 7-2 11l-2 9c0-1-1-2-1-4 1-1 1-2 1-4l-2 1c0-2-1-6-2-9l-1 1-2-2c-1 0-1-1-2-1-1-1-1-1-2-1-2 2-2 2-2 4l-1 3h-1c-1 0-1 1-2 0v-2l-2-1 1-3c2-2 2-4 3-7l-2 1-3-3-2-6-3-3-3-3-1 3v-1c-1 0-1-1-2-1v-2l-1-8c-1 0-1-1-1-2 0 0 0-1-1-2l-1-1c0-1-1-2-2-3v3c-1-1-2-1-2-2 0-2 1-2 1-5h-1l-1-1c-1 0-2-1-2-2h-1v-1l-1-1c0 1-1 2-1 3l-1-2v-2l-1-1v1l-1-1c0-3-1-8-2-10s-1-3-2-4v-1h1l1-1-1-1-1-1c-1 0-2-1-3-3l-2-2c-1-1-3-4-5-4-1-1-2-2-2-3v-1l3 2v-3l4 3-1-6-5-5h0v-2l1-2h1c1 0 1 0 2-1z"></path><path d="M219 212c0-1 0-5-1-7 2 2 3 3 5 4h1c-1 0-2 0-2 1v1h-2l-1 1z" class="B"></path><path d="M232 233l1 1 2 2c1 2 2 3 3 5l-1 1-6-5-1-1c1-2 1-2 2-3z" class="AA"></path><path d="M232 233l1 1 2 2-4 1-1-1c1-2 1-2 2-3z" class="y"></path><path d="M211 205c1-1-1-1 0-3v-1c0-1-2-2-1-4h1l2 2c2 2 2 5 1 7v5l-2-1-1-1 1-1-1-2v-1z" class="D"></path><path d="M214 211v1 2c2 0 2 0 3 2v4c3 3 6 5 9 8 2 2 5 2 7 6l-1-1c-4-3-8-5-12-8-3-3-5-6-8-7-3 0-6-2-8-3l-1-1 1-1c1 0 2 1 3 1l2 1h4v-2l1-2z" class="W"></path><path d="M212 218c3 1 5 4 8 7 4 3 8 5 12 8-1 1-1 1-2 3-2-2-5-4-8-6-2-2-4-3-6-5-1-1-2-3-4-4l-1 1h-1v-2l2-2z" class="AA"></path><path d="M219 212l1-1h2v-1c0-1 1-1 2-1 5 3 12 7 16 12-1-1-3-1-4-2v-1h-2l-1 1c-1-1-2-1-3-2-2 0-2-1-3-2-2 0-2 1-4 2l-1 1h0c-2-2-3-4-3-6z" class="a"></path><path d="M223 217l-1-1v-3l1-1c2 1 3 2 4 3-2 0-2 1-4 2z" class="B"></path><path d="M218 198l1-1c1-1 2 0 3 1l2 1 10 8 9 6c2 2 7 5 8 8l2 2v1l-5-3v1h-1c-2-3-5-5-8-8l-14-10c-3-2-6-3-7-6z" class="AB"></path><path d="M222 230c3 2 6 4 8 6l1 1 6 5-1 2c-1 1-3 3-3 5h0c0 2 0 3-1 5-2-1-3 0-5 1-1 1-1 1-1 2l-1 3v-1c-1 0-1-1-2-1v-2l-1-8c-1 0-1-1-1-2 0 0 0-1-1-2 1 0 2-1 3-1h0 1c0-2 0-4-1-5-1-3-1-5-1-8h0z" class="G"></path><path d="M231 244l1-1h0l1 2h1l1-2h0l1 1c-1 1-3 3-3 5h0-2 0l-1-1c1-1 1-2 1-3v-1z" class="p"></path><path d="M231 244v1c0 1 0 2-1 3l1 1h0 2c0 2 0 3-1 5-2-1-3 0-5 1-1 1-1 1-1 2l-1 3v-1c-1 0-1-1-2-1v-2l-1-8h0l4 4 3-4c0-1 0-2 1-3l1-1z" class="M"></path><path d="M206 229c0-3-1-8-2-10s-1-3-2-4v-1h1l1 1c2 1 5 3 8 3l-2 2v2h1l1-1c2 1 3 3 4 4 2 2 4 3 6 5h0c0 3 0 5 1 8 1 1 1 3 1 5h-1 0c-1 0-2 1-3 1l-1-1c0-1-1-2-2-3v3c-1-1-2-1-2-2 0-2 1-2 1-5h-1l-1-1c-1 0-2-1-2-2h-1v-1l-1-1c0 1-1 2-1 3l-1-2v-2l-1-1v1l-1-1z" class="D"></path><path d="M204 215c2 1 5 3 8 3l-2 2v2h1c0 1 0 1-1 2-2-1-2-1-3-2 0-1-1-2-1-3-1-1-1-2-2-3h0v-1z" class="a"></path><path d="M215 236v-2-3c-1-1-1-1-1-2l-1-1c1-1 2-2 3-2l1 1v1c-1 2 0 5 0 7v5 3c-1-1-2-1-2-2 0-2 1-2 1-5h-1z" class="F"></path><path d="M223 217c2-1 2-2 4-2 1 1 1 2 3 2 1 1 2 1 3 2l1-1h2v1c1 1 3 1 4 2 5 4 9 9 13 14 1 0 2 0 3-1 1 2 3 4 3 6 0 1-1 2-2 3v3-1-1l-1 1c-1-1-1-1-2-1 0 1 0 0-1 1-1 0-2 0-3-1 0-1 0-1-1-2s-2-2-2-4h0c-2 0-2-1-3-2v4l-1 1-1-1h-1v4c0-2 0-3-1-5-1-3-3-7-5-8-6-3-10-7-13-13h0l1-1z" class="f"></path><path d="M226 218l1 1v2l1 1h-3l1-4z" class="u"></path><path d="M241 240v-1-1h1l-1-1c1-1 1-2 2-2l1 1v4l-1 1-1-1h-1z" class="y"></path><path d="M256 234c1 2 3 4 3 6 0 1-1 2-2 3-1-3-3-6-4-8 1 0 2 0 3-1z" class="F"></path><path d="M195 184h4c2 0 3 1 5 1l1-1c1 1 1 3 2 4 1-2 1-2 3-2l2 2c2 2 4 4 4 7l-2 1c-1 1-1 1-1 3l-2-2h-1c-1 2 1 3 1 4v1c-1 2 1 2 0 3v1l1 2-1 1 1 1 2 1-1 2v2h-4l-2-1c-1 0-2-1-3-1l-1-1-1-1c-1 0-2-1-3-3l-2-2c-1-1-3-4-5-4-1-1-2-2-2-3v-1l3 2v-3l4 3-1-6-5-5h0v-2l1-2h1c1 0 1 0 2-1z" class="k"></path><path d="M204 200c-1-1-1-1-1-2h2c2 2 4 5 6 7v1l1 2-1 1 1 1 2 1-1 2v2h-4l-2-1c-1 0-2-1-3-1l-1-1c1-1 1-1 2-1 0 1 1 1 1 2 1-1 1 0 1-1l-1-2 1-1c-2-2-3-5-5-7-1-1-1-1-1-2h3z" class="F"></path><path d="M213 213c0 1-1 1-1 1-2-2-2-2-2-4h2l2 1-1 2z" class="a"></path><path d="M204 200c-1-1-1-1-1-2h2c2 2 4 5 6 7v1l1 2-1 1h-1c-3-2-4-6-6-9z" class="e"></path><path d="M192 185h1c1 1 2 2 2 3 1 0 2 1 2 1h1c0-1 1-1 1-2 1 1 2 2 4 3 2 2 5 4 8 7h0-1c-1 2 1 3 1 4v1c-1 2 1 2 0 3-2-2-4-5-6-7v-1c-1-1-2-3-3-4l-1 2 1 3h-1c-1-2-2-3-3-4h-2l-5-5h0v-2l1-2z" class="J"></path><path d="M192 185h1c1 1 2 2 2 3 1 0 2 1 2 1h-3-3 0v-2l1-2z" class="O"></path><path d="M195 184h4c2 0 3 1 5 1l1-1c1 1 1 3 2 4 1-2 1-2 3-2l2 2c2 2 4 4 4 7l-2 1c-1 1-1 1-1 3l-2-2h0c-3-3-6-5-8-7-2-1-3-2-4-3 0 1-1 1-1 2h-1s-1-1-2-1c0-1-1-2-2-3 1 0 1 0 2-1z" class="L"></path><path d="M195 184l2 2v1l-2 1c0-1-1-2-2-3 1 0 1 0 2-1z" class="S"></path><path d="M207 188c1-2 1-2 3-2l2 2c2 2 4 4 4 7l-2 1c-2-3-4-6-7-8z" class="AF"></path><path d="M244 236c1 1 1 2 3 2h0c0 2 1 3 2 4s1 1 1 2c1 1 2 1 3 1 1-1 1 0 1-1 1 0 1 0 2 1l1-1v1 1l1 1c0 1 0 3-1 4v3 2 1 5c-1 1 0 2 0 3l2 1-1 4v6c0 4-1 7-2 11l-2 9c0-1-1-2-1-4 1-1 1-2 1-4l-2 1c0-2-1-6-2-9l-1 1-2-2c-1 0-1-1-2-1-1-1-1-1-2-1-2 2-2 2-2 4l-1 3h-1c-1 0-1 1-2 0v-2l-2-1 1-3c2-2 2-4 3-7l-2 1-3-3-2-6-3-3-3-3c0-1 0-1 1-2 2-1 3-2 5-1l2 2 3-6h4l-2-1v-2c1-1 1-2 2-3v-4h1l1 1 1-1v-4z" class="AA"></path><path d="M255 255c-2-2-2-3-2-6h1 1v5 1z" class="u"></path><path d="M255 254h2v2 1l-2 3c0-1 0-2-1-3l1-2v-1z" class="M"></path><path d="M246 259l1-1h0l1-1c0 2 1 6 0 7v1 7l-1-8c0-2 0-2-1-4v-1z" class="s"></path><path d="M256 271c1 0 1 0 2-1v6c-1 1-2 1-2 2 0 2 0 3-1 4h-1 0c1-2 1-4 1-5 0-2 0-4 1-6z" class="p"></path><path d="M245 249l2-1c1 0 2 2 3 3 0 2 0 4-1 6-1-1-2-2-2-4-1-1-2-2-2-4z" class="E"></path><path d="M254 282h0 1c1-1 1-2 1-4 0-1 1-1 2-2 0 4-1 7-2 11-1 0-1-1-2-1h-1v-1c0-1 1-2 1-3h0z" class="f"></path><path d="M257 257v5c-1 1 0 2 0 3l2 1-1 4c-1 1-1 1-2 1-1-2-1-3-1-5v-1-5l2-3z" class="G"></path><path d="M255 266l-2-2c0-2 0-2-1-3h-1v2h-1c0-2 0-3 1-5 0-1 1-2 2-3 0 1 1 1 1 2 1 1 1 2 1 3v5 1z" class="u"></path><path d="M239 247h1c1 0 2 0 4-1v1c-1 1-1 1-1 3l1 1c1-1 1-1 1-2h0c0 2 1 3 2 4 0 2 1 3 2 4h-1l-1 1h0l-1 1c-2-3-3-7-5-9l-2-1v-2z" class="D"></path><path d="M246 260c1 2 1 2 1 4l1 8c0 1 0 1 1 2 0 2 0 4 1 6l-1 1-2-2c-1 0-1-1-2-1-1-1-1-1-2-1-2 2-2 2-2 4l-1 3h-1c-1 0-1 1-2 0v-2l-2-1 1-3c2-2 2-4 3-7v-1l2-2c1 0 1-1 2-1 1-2 1-2 2-3s1-3 1-4z" class="J"></path><path d="M246 260c1 2 1 2 1 4l-1 4s-1 1-1 2v2c-2 1-2 1-3 0-1 0-1-1-1-2h-2l2-2c1 0 1-1 2-1 1-2 1-2 2-3s1-3 1-4z" class="Q"></path><path d="M240 275l1-1v1l1-1h1l1 2h3l2-2c0 2 0 4 1 6l-1 1-2-2c-1 0-1-1-2-1-1-1-1-1-2-1-2 2-2 2-2 4l-1-6z" class="m"></path><path d="M239 271v-1c0 2 0 4 1 5l1 6-1 3h-1c-1 0-1 1-2 0v-2l-2-1 1-3c2-2 2-4 3-7z" class="S"></path><path d="M234 256l3-6h4c2 2 3 6 5 9v1c0 1 0 3-1 4s-1 1-2 3c-1 0-1 1-2 1l-2 2v1l-2 1-3-3-2-6-3-3-3-3c0-1 0-1 1-2 2-1 3-2 5-1l2 2z" class="AJ"></path><path d="M226 257c0-1 0-1 1-2 2-1 3-2 5-1l2 2c-2 1-3 1-4 2 0 1 1 2 1 2 1 1 1 2 1 3l-3-3-3-3z" class="X"></path><path d="M635 76l2 3h2l2-1 3 1v1l-2 2v2l2 2v1l-1 2 4 1 16 9c8 4 14 9 21 14v3c1 2 5 6 7 7 3 2 6 7 8 11l2 4c-1 5-2 9-3 15l-1 2-5 15h-2c-2 0-3-2-5-3v1c-2-1-4-4-5-6l-3-5-2-3c0 1-2 1-2 2-1 1 0 3 0 4-2 0-2 0-3 1l1 1c-1 0-1 0-2-1 0-2 1-3 1-5 0-6-5-11-9-15l1-1-9-6c-2-1-2-2-5-3l-1-1-2-1h-4v-1l1-2c0-4 0-9-2-12-1-1-1-2-3-2s-2 1-3 2l-1-2v-2c-1-2-2-5-4-7 0-2 0-4-1-6-1-1-2-3-3-3-2-1-2 0-3-1-4 0-8 0-11 2-2 1-4 1-6 2-7 2-12 6-16 11h-1l-2 2-1-1c4-7 8-10 14-15l1-1h0c2-3 11-6 14-7l5-1h3c2-1 3-1 5-1l1 1h1c0-1 2-1 3-1v-2l3-6z" class="I"></path><path d="M622 93c2 0 5 1 8 1l-2 3c-1-1-2-3-3-3-2-1-2 0-3-1z" class="K"></path><path d="M657 107c5 2 10 3 14 7-1-1-3-1-4-1h0l2 4s-1 0-1 1l-6-6s-3-2-3-3l-2-2z" class="F"></path><path d="M614 86l5-1v2c2 2 7 0 8 1-2 1-5 1-7 0-7 0-14 2-20 5h0c2-3 11-6 14-7z" class="Ae"></path><path d="M699 134l2 4c-1 5-2 9-3 15-1-1-2-1-3-1v-4l1-1c0-2 0-2-1-3v-3h1c0 2 1 3 1 5h0l1-1c0-4 1-7 1-11z" class="a"></path><path d="M663 99c8 4 14 9 21 14v3c-8-6-16-11-25-16l1-1 1 1c1 0 2-1 2-1z" class="AZ"></path><path d="M635 76l2 3h2l2-1 3 1v1l-2 2v2l2 2v1l-1 2 4 1 16 9s-1 1-2 1l-1-1-1 1c-10-6-20-10-32-12-1-1-6 1-8-1v-2h3c2-1 3-1 5-1l1 1h1c0-1 2-1 3-1v-2l3-6z" class="Ac"></path><path d="M635 76l2 3h2l2-1 3 1v1l-2 2v2l2 2v1l-1 2-4-1-7-4v-2l3-6z" class="f"></path><path d="M639 88c1-1 1-2 1-3 1-1 1-1 0-1v-1l2-1v2l2 2v1l-1 2-4-1z" class="AE"></path><path d="M635 76l2 3h2l-3 5-4-2 3-6z" class="p"></path><path d="M659 109c0 1 3 3 3 3l6 6c0-1 1-1 1-1l-2-4h0c1 0 3 0 4 1 3 3 8 4 10 8 1 3 3 3 4 5 4 4 9 9 10 14v3c1 1 1 1 1 3l-1 1v4c1 0 2 0 3 1l-1 2-5 15h-2c-2 0-3-2-5-3l1-1v-3-5l2-1-1-1c-1-1-2-2-2-4l-1-1-1 1-1-3c-1-1-1-1-1-2-1-3-3-6-6-9-1-4-3-6-5-9l-2-2c-1-4-4-7-6-10l1 1v-1c0-1 0-1-1-2v-1l-3-3v-2z" class="AA"></path><path d="M685 152h1v-2c0-1-1-3-1-5l-3-8c-2-3-4-7-6-10v-2l-5-7h1c1 0 2 0 3 1v1h1c1 1 2 1 2 2l2 2h0c1 0 1 0 2 1l1 1s1 1 2 1h0c4 4 9 9 10 14v3c1 1 1 1 1 3l-1 1v4c1 0 2 0 3 1l-1 2-1 1h-1-1v-3c1-3 0-7-1-10-1 0-1-1-1-1-1-3-3-6-5-7-2-2-4-5-6-7l-1 1h1v2c1 3 3 4 3 6v2c0 1 1 0 1 2 0 0 0 1 1 1v4l2 2v-1l1 1-1 1 1 1c1 2 0 6 0 8h0l-1-1-1-1c-1-1-2-2-2-4z" class="M"></path><path d="M659 109c0 1 3 3 3 3l6 6c0-1 1-1 1-1l-2-4h0c1 0 3 0 4 1 3 3 8 4 10 8 1 3 3 3 4 5h0c-1 0-2-1-2-1l-1-1c-1-1-1-1-2-1h0l-2-2c0-1-1-1-2-2h-1v-1c-1-1-2-1-3-1h-1l5 7v2c2 3 4 7 6 10l3 8c0 2 1 4 1 5v2h-1l-1-1-1 1-1-3c-1-1-1-1-1-2-1-3-3-6-6-9-1-4-3-6-5-9l-2-2c-1-4-4-7-6-10l1 1v-1c0-1 0-1-1-2v-1l-3-3v-2z" class="m"></path><path d="M682 149c0-2-1-3-1-4l1-2c1 3 3 6 3 9l-1-1-1 1-1-3z" class="M"></path><path d="M659 109c0 1 3 3 3 3l6 6v1c4 5 7 9 9 15l1 2c2 2 3 5 4 7l-1 2c0 1 1 2 1 4-1-1-1-1-1-2-1-3-3-6-6-9-1-4-3-6-5-9l-2-2c-1-4-4-7-6-10l1 1v-1c0-1 0-1-1-2v-1l-3-3v-2z" class="G"></path><path d="M662 112l6 6v1c4 5 7 9 9 15l1 2v1c-2-1-4-5-5-7-1-1-1-1-2-3h0c-1-1-1-2-1-3-1-2-3-3-4-5-2-2-2-4-4-5v-1-1z" class="p"></path><path d="M630 94c2 1 3 2 5 2l1 1h2s1 1 2 1l7 2 4 2c2 1 4 3 6 5l2 2v2l3 3v1c1 1 1 1 1 2v1l-1-1c2 3 5 6 6 10l2 2c2 3 4 5 5 9 3 3 5 6 6 9 0 1 0 1 1 2l1 3 1-1 1 1c0 2 1 3 2 4l1 1-2 1v5 3l-1 1v1c-2-1-4-4-5-6l-3-5-2-3c0 1-2 1-2 2-1 1 0 3 0 4-2 0-2 0-3 1l1 1c-1 0-1 0-2-1 0-2 1-3 1-5 0-6-5-11-9-15l1-1-9-6c-2-1-2-2-5-3l-1-1-2-1h-4v-1l1-2c0-4 0-9-2-12-1-1-1-2-3-2s-2 1-3 2l-1-2v-2c-1-2-2-5-4-7 0-2 0-4-1-6l2-3z" class="f"></path><path d="M653 122c3 1 5 3 7 5l4 3-2 2-9-6h3 1l-3-3-1-1z" class="S"></path><path d="M643 112c1-1 1-1 2 0 2 4 4 7 8 10l1 1 3 3h-1-3l-5-3h0l-2-2-3-9z" class="n"></path><path d="M645 112c1-1 2-2 3-1 1 0 2 1 3 2 2 2 4 4 7 6 1 1 2 3 3 3l1 1c-1 2-1 3-2 4-2-2-4-4-7-5-4-3-6-6-8-10z" class="e"></path><path d="M675 138c3 3 5 6 6 9 0 1 0 1 1 2l1 3 1-1 1 1c0 2 1 3 2 4l1 1-2 1v5 3l-1 1c-4-4-5-10-8-16h1l-1-2 2-2c-1-1-1-3-2-4-1-2-1-3-2-5z" class="E"></path><path d="M683 152l1-1 1 1c0 2 1 3 2 4l1 1-2 1v5l-3-11z" class="f"></path><path d="M661 122l2 1h0c2 2 3 3 5 4l2 2c2 3 4 5 5 9 1 2 1 3 2 5 1 1 1 3 2 4l-2 2 1 2h-1c-4-7-9-13-15-19l2-2-4-3c1-1 1-2 2-4l-1-1z" class="H"></path><path d="M661 122l2 1h0c2 2 3 3 5 4l2 2v1c0 3 1 3 2 5l-1 1-2-3h-1c-1 0-3-2-4-3l-4-3c1-1 1-2 2-4l-1-1z" class="O"></path><path d="M662 123l6 10c-1 0-3-2-4-3l-4-3c1-1 1-2 2-4z" class="D"></path><path d="M640 98l7 2 4 2c2 1 4 3 6 5l2 2v2l3 3v1c1 1 1 1 1 2v1l-1-1c2 3 5 6 6 10-2-1-3-2-5-4h0l-2-1c-1 0-2-2-3-3-3-2-5-4-7-6-1-1-2-2-3-2-1-1-2 0-3 1-1-1-1-1-2 0l-1-5c0-1-1-3-1-4 1 0 2 0 3 1 0-1-1-2-1-3h-2l-1-1v-2z" class="L"></path><path d="M653 110c3 1 6 4 8 7h1c2 3 5 6 6 10-2-1-3-2-5-4v-1-1l-1-1-1-1c-2-3-7-6-8-9z" class="H"></path><path d="M640 98l7 2 4 2c2 1 4 3 6 5l2 2v2l3 3v1c1 1 1 1 1 2v1l-1-1h-1c-2-3-5-6-8-7v-1l-9-5c0-1-1-2-1-3h-2l-1-1v-2z" class="k"></path><path d="M640 98l7 2-3 1c1 1 2 1 3 2l2 2h-1l1 1c2 1 2 2 4 3l-9-5c0-1-1-2-1-3h-2l-1-1v-2z" class="F"></path><path d="M630 94c2 1 3 2 5 2l1 1h2s1 1 2 1v2l1 1h2c0 1 1 2 1 3-1-1-2-1-3-1 0 1 1 3 1 4l1 5 3 9 2 2h0v2c2 1 3 2 5 2 2 1 2 2 3 3s2 1 3 1c0 1 0 2 1 2 1 2 3 4 5 6 5 4 7 9 10 15 0 1-2 1-2 2-1 1 0 3 0 4-2 0-2 0-3 1l1 1c-1 0-1 0-2-1 0-2 1-3 1-5 0-6-5-11-9-15l1-1-9-6c-2-1-2-2-5-3l-1-1-2-1h-4v-1l1-2c0-4 0-9-2-12-1-1-1-2-3-2s-2 1-3 2l-1-2v-2c-1-2-2-5-4-7 0-2 0-4-1-6l2-3z" class="k"></path><path d="M642 126c2 1 3 1 5 1 2 1 3 2 4 3 1 0 2 0 3 1h2l1 2h0-1c-3-1-4-2-6-3h-3l-2-1h-4v-1l1-2z" class="B"></path><path d="M647 130h3c2 1 3 2 6 3h1 0c2 2 4 3 5 6v1l-9-6c-2-1-2-2-5-3l-1-1z" class="K"></path><path d="M630 94c2 1 3 2 5 2l1 1h2s1 1 2 1v2l1 1h2c0 1 1 2 1 3-1-1-2-1-3-1 0 1 1 3 1 4s-1 1-1 2 1 2 0 3c-1-1-2-2-2-4l-1-1v1l1 1c-1 1-2 1-3 1l-3 2v-2c-1-2-2-5-4-7 0-2 0-4-1-6l2-3z" class="i"></path><path d="M633 100l-1-2 1-1h5s1 1 2 1v2l1 1h2c0 1 1 2 1 3-1-1-2-1-3-1-3-3-2-1-5-1l-2-2h-1z" class="K"></path><path d="M630 94c2 1 3 2 5 2l1 1h2-5l-1 1 1 2c0 2 1 3 3 5 0 1 1 2 1 4l-1 1-3 2v-2c-1-2-2-5-4-7 0-2 0-4-1-6l2-3z" class="H"></path><path d="M636 105c0 1 1 2 1 4l-1 1-3 2v-2c0-1 0-2 1-3v1c1-1 1-2 2-3z" class="Q"></path><path d="M307 128c4 0 7-1 10-1l2-1 2-1h1s1 0 1-1h1l-1 2h2c1 0 2 0 2 1h0v1l1 1 2 1c-1 1-1 2-2 3l-2-1v-2h-1v3c-2 5-6 10-10 15l-12 17-11 21c-2 3-3 7-5 10-1 4-3 7-4 11-3 4-4 9-7 14 0-1 0-1-1-2h-2v-2l1-1-1-4 1-3h0c0-2 1-3 2-4h1v-2c0-1-1-2-2-3v-1c1 0 1 0 1-1-1-1-1-2-2-3-1 0-2 0-2 1-3-2-7-3-11-5-1-1-3-2-5-3h-1l-2-2v-3c1-2 2-3 1-5v-3h-1v-5h-1v-2l-1-1-1-3c0-1-1-2-1-4l-2-2 3-3h-1l-3-1h-2-2 0l3-1-1-1c-2-1-3-2-5-2 1 0 1-1 2-1 3-2 5-3 7-4l2-1h1c1-1 2-1 4-1h-2v-1h2 0c2-1 5-1 6-2l14-3 8-1 5-2 4-2c3 0 5-1 7-2 3-1 6-2 8-2z" class="y"></path><path d="M253 170v-1l2-2c2 1 4 1 6 1-1 1-2 1-3 2-3 1-3 4-4 5h-1v-5z" class="D"></path><path d="M254 178l1-3h2 0c2-1 3-3 4-4l3-3c3 0 3 0 5 2h-1l-4 2c-2 1-3 1-4 3v1h-1l-1 1-1 2h-2v3l-2 1c1-2 2-3 1-5z" class="s"></path><path d="M321 132h3s1 0 1 1c-2 5-6 10-10 15-1 1-3 1-4 2-1 0-3 2-3 2h-1l-1-1c-2 1-4 1-6 3l-3 3c-3-1-5 1-7 2-1 0-2 1-3 2h-1l-1-1c-1 1-1 1 0 2l-1 1v-1c-3-1-3-1-5 0-3 1-3 1-6 1l4-1 2-2h3v-1c1-2 2-2 4-3 1-1 3-2 4-2 2-1 3-1 4-2l11-4 2 1c1-1 0-1 1-1s2-1 3-2 2-2 4-2h1c1-2 3-4 4-6 0-1 1-2 2-3v-2l-1-1z" class="f"></path><path d="M294 152l11-4 2 1c-2 2-5 2-7 4h-2c-2 1-1 3-4 1-2 1-3 1-4 3h0c-1 0-1 0-2 1-2 0-2 0-4 1v3c-3-1-3-1-5 0-3 1-3 1-6 1l4-1 2-2h3v-1c1-2 2-2 4-3 1-1 3-2 4-2 2-1 3-1 4-2z" class="M"></path><path d="M321 132l1 1v2c-1 1-2 2-2 3-1 2-3 4-4 6h-1c-2 0-3 1-4 2s-2 2-3 2 0 0-1 1l-2-1-11 4v-3h-5v-1l1-1c-1 0-1-1-2-2l33-13z" class="E"></path><path d="M290 147c2 0 5-1 7-1l2 1-1 1c-3 1-2-2-4 1h-5v-1l1-1z" class="D"></path><path d="M299 147c1-1 3-1 5-2h0l2 1 4-2c3-1 3-2 6-4 1 0 3-1 4-2-1 2-3 4-4 6h-1c-2 0-3 1-4 2s-2 2-3 2 0 0-1 1l-2-1-11 4v-3c2-3 1 0 4-1l1-1z" class="m"></path><defs><linearGradient id="U" x1="263.926" y1="147.651" x2="267.761" y2="157.11" xlink:href="#B"><stop offset="0" stop-color="#4a0404"></stop><stop offset="1" stop-color="#7b1110"></stop></linearGradient></defs><path fill="url(#U)" d="M285 146c1-1 3-1 3-1 1 1 1 2 2 2l-1 1v1h5v3c-1 1-2 1-4 2-1 0-3 1-4 2-2 1-3 1-4 3v1h-3l-2 2-4 1c-2 1-5 2-7 3-2 0-4 0-5 2-2 0-4 0-6-1l-2 2v1h-1v-2l-1-1-1-3c0-1-1-2-1-4l-2-2 3-3h-1l-3-1h-2-2 0l3-1 40-7z"></path><path d="M285 146c1-1 3-1 3-1 1 1 1 2 2 2l-1 1-5 1v-1l2-2h-1z" class="i"></path><path d="M271 157c1-1 2-1 2-2v-1-1c1 0 2-1 2-2h1c3-1 5-1 8-1-2 1-5 2-6 4 0 1 0 1 1 2 1 0 1 0 2 1-2 1-2 1-4 1h-1v-1h-2-1-2z" class="B"></path><path d="M289 149h5v3c-1 1-2 1-4 2-1 0-3 1-4 2-2 1-3 1-4 3l-1-2c-1-1-1-1-2-1-1-1-1-1-1-2 1-2 4-3 6-4h0l5-1z" class="G"></path><path d="M281 157l1 2v1h-3l-2 2-4 1c-2 1-5 2-7 3-2 0-4 0-5 2-2 0-4 0-6-1l-2 2v1h-1v-2l-1-1c1-1 1-2 2-3v-1l2-2c1 0 3 1 3 1 2-1 3-2 5-3 1 0 2 0 4-1h2l2-1h2 1 2v1h1c2 0 2 0 4-1z" class="h"></path><path d="M252 168c1-1 3-3 4-3l2 1c1 0 2-3 3-3h2c1 0 3-1 4-1s2 0 3-1h1 2v-1c2 1 4 1 6 0l-2 2-4 1c-2 1-5 2-7 3-2 0-4 0-5 2-2 0-4 0-6-1l-2 2v1h-1v-2z" class="G"></path><defs><linearGradient id="V" x1="302.392" y1="140.255" x2="301.617" y2="134.309" xlink:href="#B"><stop offset="0" stop-color="#de2727"></stop><stop offset="1" stop-color="#ea4e51"></stop></linearGradient></defs><path fill="url(#V)" d="M307 128c4 0 7-1 10-1l2-1 2-1h1s1 0 1-1h1l-1 2h2c1 0 2 0 2 1h0v1l1 1 2 1c-1 1-1 2-2 3l-2-1v-2h-1v3c0-1-1-1-1-1h-3l-33 13s-2 0-3 1l-40 7-1-1c-2-1-3-2-5-2 1 0 1-1 2-1 3-2 5-3 7-4l2-1h1c1-1 2-1 4-1h-2v-1h2 0c2-1 5-1 6-2l14-3 8-1 5-2 4-2c3 0 5-1 7-2 3-1 6-2 8-2z"></path><path d="M244 148c2 0 5 1 6 0 3-1 5-1 8-1l1 1c-4 2-10 3-15 4-2-1-3-2-5-2 1 0 1-1 2-1l3-1z" class="AF"></path><path d="M323 126h2c1 0 2 0 2 1h0v1l1 1 2 1c-1 1-1 2-2 3l-2-1v-2h-1c-5 0-8 1-13 3-12 5-25 9-38 12l-15 3-1-1 15-3 36-12 10-4c1-1 4-2 4-2z" class="AZ"></path><path d="M323 126h2c1 0 2 0 2 1h0v1l1 1 2 1c-1 1-1 2-2 3l-2-1v-2h-1c-5 0-8 1-13 3h-2l1-1c3-1 6-2 8-4 1-1 4-2 4-2z" class="AH"></path><path d="M307 128c4 0 7-1 10-1l2-1 2-1h1s1 0 1-1h1l-1 2s-3 1-4 2l-10 4-36 12-15 3c-3 0-5 0-8 1-1 1-4 0-6 0l-3 1c3-2 5-3 7-4l2-1h1c1-1 2-1 4-1h-2v-1h2 0c2-1 5-1 6-2l14-3 8-1 5-2 4-2c3 0 5-1 7-2 3-1 6-2 8-2z" class="AB"></path><path d="M244 148c5-2 10-2 16-4h5c3-1 4-2 8 0l-15 3c-3 0-5 0-8 1-1 1-4 0-6 0z" class="AI"></path><path d="M307 128c4 0 7-1 10-1l2-1 2-1h1s1 0 1-1h1l-1 2s-3 1-4 2l-10 4c-4 0-6 0-10 1-2 1-4 1-6 1-1 1-1 1-2 1s-2 0-2 1c-3 1-5 2-7 2l-18 4c-2 1-5 1-7 2h-1-1l-7 1 2-1h1c1-1 2-1 4-1h-2v-1h2 0c2-1 5-1 6-2l14-3 8-1 5-2 4-2c3 0 5-1 7-2 3-1 6-2 8-2zm-17 33c1-1 4-1 6-1h1v2c0 1 0 1 1 2s1 3 1 4c2 0 3-1 4-3l-11 21c-2 3-3 7-5 10-1 4-3 7-4 11-3 4-4 9-7 14 0-1 0-1-1-2h-2v-2l1-1-1-4 1-3h0c0-2 1-3 2-4h1v-2c0-1-1-2-2-3v-1c1 0 1 0 1-1-1-1-1-2-2-3-1 0-2 0-2 1-3-2-7-3-11-5-1-1-3-2-5-3h-1l-2-2v-3l2-1v-3h2l1-2 1-1h1v-1c1-2 2-2 4-3l4-2h1c3-1 4-3 7-5h1 2c1-1 3-1 4-1h2l1 2h0l2-3h0v-1l2-1z" class="z"></path><path d="M281 205h1l1-1c0-1 1-1 1-2 0-2 2-4 2-6 1 0 1-1 1-1v1c-1 4-3 7-4 11-3 4-4 9-7 14 0-1 0-1-1-2h-2v-2l1-1 1-1h0l3-3v-1c2-2 3-4 3-6z" class="s"></path><path d="M285 176c1-1 1-2 2-3l3 3c0 1 0 1-1 2v1c0 2-1 3-2 4l1 1c-2 2-6 4-6 7h-1c0-3 2-6 2-8v-2c1-2 1-3 2-5z" class="AR"></path><path d="M285 176l2 2v1l1 1-1 1h-1-3c1-2 1-3 2-5z" class="AB"></path><path d="M275 194l1 1c0 2 1 3 2 4v1-2l2-1 1 1v3h-1-1v1h2 0 1c0 1 0 2-1 3 0 2-1 4-3 6v1l-3 3h0l-1 1-1-4 1-3h0c0-2 1-3 2-4h1v-2c0-1-1-2-2-3v-1c1 0 1 0 1-1-1-1-1-2-2-3l1-1z" class="y"></path><path d="M290 161c2 0 4-1 5 1 1 0 1 1 1 1 0 2 1 3 0 4-1 3-2 6-4 9l2 2-1 1v1l-1-1-1 2-2 1v-3-1c1-1 1-1 1-2l-3-3c-1 1-1 2-2 3-1 2-1 3-2 5v2c-2-2-2-4-4-6v2h-2v-3h-1l1-2c-1-2-3-4-5-4l-1 1c-1-1-1-1-3-1h1c3-1 4-3 7-5h1 2c1-1 3-1 4-1h2l1 2h0l2-3h0v-1l2-1z" class="AE"></path><path d="M282 168l-1 5c-1 1-2 2-2 3-1-1-1-5-1-7 1 0 2 0 4-1z" class="AI"></path><path d="M277 165h2c1-1 3-1 4-1 0 2 0 3-1 4-2 1-3 1-4 1l-1-4z" class="AF"></path><path d="M288 162c1 0 2 1 3 1 1-1 2-1 2 0 2 1 2 1 2 3h0l-1 1c0 1-1 2-1 3h-1c-1-1-1-3-2-4l-2-3h0v-1z" class="AB"></path><path d="M271 171l1-1c2 0 4 2 5 4l-1 2h1v3 3c0 1-1 3-2 5 1 2 0 2-1 4h-1l-1 2c2 0 2 0 3 1l-1 1c-1 0-2 0-2 1-3-2-7-3-11-5-1-1-3-2-5-3h-1l-2-2v-3l2-1v-3h2l1-2 1-1h1v-1c1-2 2-2 4-3l4-2c2 0 2 0 3 1z" class="AI"></path><path d="M277 182h-2l-1-4 1-1 2 2v3z" class="AF"></path><path d="M275 187c1 2 0 2-1 4v-1h-3c1-1 1-1 2-1l-1-1 1-1h2z" class="AE"></path><path d="M264 187h1c1-1 3 0 5 1l-2 1h-2c-2 0-1 0-2 1-1-1-1-1-2-1l2-2z" class="AR"></path><path d="M261 191c2-1 3-1 4 0 1 0 3 1 4 1s2-1 4-1l-1 2c2 0 2 0 3 1l-1 1c-1 0-2 0-2 1-3-2-7-3-11-5z" class="f"></path><path d="M253 183l2-1v-3h2l1-2 1-1v6c-1 1-1 1-2 1-1 2-1 3-1 5h-1l-2-2v-3z" class="z"></path><path d="M268 170c2 0 2 0 3 1l1 3-1 2c1 1 1 1 1 3l-2 1c-1 0-3 0-4 1 0 1 0 1-1 2-1-2-5-4-5-7v-1c1-2 2-2 4-3l4-2z" class="AF"></path><path d="M264 172h0c0 3 1 3 2 5l1 1h-2c-2 1-3-1-5-3 1-2 2-2 4-3z" class="Ad"></path><path d="M268 170c2 0 2 0 3 1l1 3-1 2c-1 1-3 2-4 2l-1-1c-1-2-2-2-2-5h0l4-2z" class="AB"></path><path d="M268 170c2 0 2 0 3 1l1 3c-1-1-2-1-3-1l-1 1c-1 0-3-1-4-2h0l4-2z" class="AQ"></path><defs><linearGradient id="W" x1="208.409" y1="154.65" x2="207.266" y2="176.949" xlink:href="#B"><stop offset="0" stop-color="#200504"></stop><stop offset="1" stop-color="#660b0b"></stop></linearGradient></defs><path fill="url(#W)" d="M167 152c11 2 21 3 32 4 5 0 9 0 14-1 0 0 1 0 2 1 0 0 1 1 2 1l1-2h8l10-1h6 0 2 2l3 1h1l-3 3 2 2c0 2 1 3 1 4l1 3 1 1v2h1v5h1v3c1 2 0 3-1 5v3l2 2h1c2 1 4 2 5 3 4 2 8 3 11 5 0-1 1-1 2-1 1 1 1 2 2 3 0 1 0 1-1 1v1c1 1 2 2 2 3v2h-1c-1 1-2 2-2 4h0l-1 3 1 4-1 1v2h2c1 1 1 1 1 2l-2 2 1 1h0l2-2 1 1-1 2s0 1 1 2l1-3c0 1 0 3-1 5 1 1 2 3 2 5-2-2-5-4-7-5-1-1-2-2-2-3h0c-1 2-2 3-2 5-1 1-1 1-2 1l-8-5c-3-3-5-5-6-9v5l-2-2c-1-3-6-6-8-8l-9-6-10-8-2-1c-1-1-2-2-3-1l-1 1c-1-1-1-2-1-4l-1 1c0-3-2-5-4-7l-2-2c-2 0-2 0-3 2-1-1-1-3-2-4l-1 1c-2 0-3-1-5-1h-4c-1 1-1 1-2 1h-1l-1 2v2l-3-1v-3h-2l1-1-1-2v-1c-2-3-3-4-6-7 0 0-1 0-1-1h0 1c0-2-1-4-2-5l-7-6-3-4v-1-1l-1-1v-2-1z"></path><path d="M226 161c2-1 4-1 7-1h1c2-2 6 0 9-2 2 0 2 0 2 2v1c-1 0-2 0-3 1h-1c-3-1-5-1-8-1s-5 1-7 0z" class="W"></path><path d="M226 161c2 1 4 0 7 0s5 0 8 1l-1 1h-1-5c-1 0-2 1-3 1s-1 0-2 1c-3 0-5 1-7 2h-2v-1c-1 0-1-1-1-2 1 0 1-1 2-2 1 0 3-1 5-1z" class="O"></path><path d="M220 166c0-1 1-1 1-2h3c3-1 8-3 12-2 1 1 2 1 4 1h-1-5c-1 0-2 1-3 1s-1 0-2 1c-3 0-5 1-7 2h-2v-1z" class="V"></path><path d="M236 154h6 0 2 2l3 1h1l-3 3-2-2c-1 0-1 1-1 1h-2c-1 0-2 0-3 1-2 0-6 1-8 0-4-1-10-1-14-1l1-2h8l10-1z" class="W"></path><path d="M183 163c2 0 8 1 9 2l6 3c1 1 1 1 2 1h3c0 1 1 2 1 3s1 2 2 3l-1 2 3 2c1 0 2 1 3 1l4 2 1 1h0l1 1c1 1 1 2 1 2l1 2-1 1h-1v4 1l-1 1c0-3-2-5-4-7l-2-2c-1-2-6-6-8-7 0 0-1-1-1-2-2-1-2-1-2-2s-1-2-2-2-1 0-2-1-1 0-2 0c0-1-2-2-2-2-2-2-5-2-6-5-1-1-1-1-2-1v-1z" class="H"></path><path d="M203 176c1 0 2 1 2 1l3 2v1h-1l1 1-1 1-1-1c-1 0-1-1-2-1 0-2-1-2-2-2v-1h-1l2-1z" class="j"></path><path d="M192 165l6 3c1 1 1 1 2 1h3c-2 1-3 2-5 1v1c-2-2-6-4-6-6z" class="J"></path><path d="M203 169c0 1 1 2 1 3s1 2 2 3l-1 2s-1-1-2-1c-1-2-4-4-5-5v-1c2 1 3 0 5-1z" class="F"></path><path d="M212 188h0l1 1h3c0-2-5-6-7-7l5 1 1-1 1 1h0l1 1c1 1 1 2 1 2l1 2-1 1h-1v4 1l-1 1c0-3-2-5-4-7z" class="D"></path><path d="M183 163v-1c-2 0-3 0-4-1h0 1c2 1 4 1 5 0l1-1 3 3c1-1 1-1 1-2 2-1 4-2 6-2l1 2c1 1 2 0 3 0s2-1 3-1v2c2 0 4 1 6 1l1-1c1-1 2-1 3-1 0 1 1 1 1 1h2c1 1 1 0 2 1v1h1c0 1 0 2 1 2v1h-1l-1 1 1 1-1-1c-1 0-5 1-6 2s-1 1-2 1-2 0-2 1l-2-1-1 1h-1c0-1-1-2-1-3h-3c-1 0-1 0-2-1l-6-3c-1-1-7-2-9-2z" class="T"></path><path d="M210 162c1-1 2-1 3-1 0 1 1 1 1 1h2c-2 1-3 2-5 2l-1-2z" class="W"></path><path d="M218 164h1c0 1 0 2 1 2v1h-1l-1 1 1 1-1-1c-1 0-5 1-6 2s-1 1-2 1-2 0-2 1l-2-1-1 1h-1c0-1-1-2-1-3h-3c-1 0-1 0-2-1l1-1 1-1c1 0 1 0 2-1h1c0 1 0 1 1 1h2l2 1 1-2h4c2 0 3 0 5-1z" class="K"></path><path d="M249 160c0 2 1 3 1 4l1 3 1 1v2h1v5h-1l-2 2c-1-3-1-4-3-5-1-1-1-1-1-2-1-2-1-2-1-4l-1-1-1 1-1-2c-1 0-2 1-2 1-2 0-2 0-2-1-2 0-3 0-4 1 0-1-1-1-1 0h-1c-1 0-3 1-4 1v2c-1 0-1-1-1-1h-1-1v2l1 1h-2l-1-2v1h-1v1h-2v3h-2l-2-2c-2 2-2 4-3 6l-1 2-1 1c-1 0-2-1-3-1l-3-2 1-2c-1-1-2-2-2-3h1l1-1 2 1c0-1 1-1 2-1s1 0 2-1 5-2 6-2l1 1-1-1 1-1h1 2c2-1 4-2 7-2 1-1 1-1 2-1s2-1 3-1h5 1l1-1h1c1-1 2-1 3-1 0 1 1 2 1 3 2-2 2-2 3-4z" class="J"></path><path d="M206 175l2 1 1-2c1 0 1 0 2 1l-1 1v1l2 2-1 1c-1 0-2-1-3-1l-3-2 1-2z" class="B"></path><path d="M249 160c0 2 1 3 1 4l1 3 1 1v2c-1 0-1 1-2 1-1-3-4-5-7-7 0-2 0-1-1-2 1-1 2-1 3-1 0 1 1 2 1 3 2-2 2-2 3-4z" class="O"></path><path d="M249 160c0 2 1 3 1 4-1 1-1 1-2 1l-2-1c2-2 2-2 3-4z" class="W"></path><path d="M168 156c4 4 9 7 14 11 6 4 13 10 20 12 2 1 7 5 8 7-2 0-2 0-3 2-1-1-1-3-2-4l-1 1c-2 0-3-1-5-1h-4c-1 1-1 1-2 1h-1l-1 2v2l-3-1v-3h-2l1-1-1-2v-1c-2-3-3-4-6-7 0 0-1 0-1-1h0 1c0-2-1-4-2-5l-7-6-3-4v-1-1z" class="M"></path><path d="M198 181c1 0 3-1 4 0 2 1 2 2 3 3l-1 1c-2 0-3-1-5-1-1-1-1-2-1-3z" class="n"></path><path d="M178 168c6 4 13 11 20 13 0 1 0 2 1 3h-4c-1 1-1 1-2 1h-1l-1 2v2l-3-1v-3h-2l1-1-1-2v-1c-2-3-3-4-6-7 0 0-1 0-1-1h0 1c0-2-1-4-2-5z" class="c"></path><path d="M180 174c2 0 6 4 7 5l3 3 2 3-1 2v2l-3-1v-3h-2l1-1-1-2v-1c-2-3-3-4-6-7z" class="E"></path><path d="M190 182l2 3-1 2-2-1c0-2 0-3 1-4z" class="K"></path><path d="M213 177c1-2 1-4 3-6l2 2h2v-3h2v-1h1v-1l1 2h2l-1-1v-2h1 1s0 1 1 1v-2c1 0 3-1 4-1h1c0-1 1-1 1 0 1-1 2-1 4-1 0 1 0 1 2 1 0 0 1-1 2-1l1 2 1-1 1 1c0 2 0 2 1 4 0 1 0 1 1 2 2 1 2 2 3 5 0 2-1 2 0 4h-3 0c-2 1-4 1-6 1v-1c-2 0-4 2-5 3-1 0-2 2-2 3 1 2 1 4 2 6-1 1-1 1-2 0v-1c-2 0-2-1-3-2v-1l-1-1-1 1-3-1h0l-6-3-2-1h-1l-1-1h0l-1-1-4-2 1-1 1-2z" class="h"></path><path d="M228 174l3-3c2-1 4-1 6-1h1 1c1 0 3 0 3 1h0c0 1 1 2 1 3h-3c-1 0-1-1-1-1-2-1-2 0-4 1h0c-2 1-3 2-4 4-1 0-1 0-1-1h-1-3v-1l2-2z" class="s"></path><path d="M235 174h0c2-1 2-2 4-1 0 0 0 1 1 1h3c2 1 4 3 5 5l-1 2h0c-2 1-4 1-6 1v-1c-2 0-4 2-5 3l-1-1c-1 1 0 1-1 1-2 0-2 0-3-1-1-2-1-3 0-5s2-3 4-4z" class="f"></path><path d="M231 178c1-2 2-3 4-4l4 6-4 3c-1 1 0 1-1 1-2 0-2 0-3-1-1-2-1-3 0-5z" class="o"></path><path d="M221 177c2-2 1 0 3-1 0 0 1-2 2-2h2l-2 2v1h3 1c0 1 0 1 1 1-1 2-1 3 0 5 1 1 1 1 3 1 1 0 0 0 1-1l1 1c-1 0-2 2-2 3 1 2 1 4 2 6-1 1-1 1-2 0v-1c-2 0-2-1-3-2v-1l-1-1-1 1-3-1h0l-6-3-2-1h-1l-1-1h0l-1-1-4-2 1-1 1-2c3 1 4 0 6-1 1 0 1 1 2 1z" class="f"></path><path d="M213 177c3 1 4 0 6-1 1 0 1 1 2 1-2 1-4 2-5 3v2c1 0 2 1 3 1l1 1h0-2-1l-1-1h0l-1-1-4-2 1-1 1-2z" class="G"></path><path d="M220 184s1 1 2 1c2 0 2 0 3-1h1c2 0 2 0 3-1h2c1 1 1 1 3 1 1 0 0 0 1-1l1 1c-1 0-2 2-2 3 1 2 1 4 2 6-1 1-1 1-2 0v-1c-2 0-2-1-3-2v-1l-1-1-1 1-3-1h0l-6-3-2-1h2z" class="AE"></path><path d="M226 188c1-2 1-2 2-2s2 0 2 1v1l-1 1-3-1z" class="AR"></path><path d="M250 177l2-2h1 1v3c1 2 0 3-1 5v3l2 2h1c2 1 4 2 5 3 4 2 8 3 11 5 0-1 1-1 2-1 1 1 1 2 2 3 0 1 0 1-1 1v1c1 1 2 2 2 3v2h-1c-1 1-2 2-2 4h0l-1 3 1 4-1 1v2h2c1 1 1 1 1 2l-2 2 1 1h0l2-2 1 1-1 2s0 1 1 2l1-3c0 1 0 3-1 5 1 1 2 3 2 5-2-2-5-4-7-5-1-1-2-2-2-3h0c-1 2-2 3-2 5-1 1-1 1-2 1l-8-5c-3-3-5-5-6-9v5l-2-2c-1-3-6-6-8-8l-9-6-10-8-2-1c-1-1-2-2-3-1l-1 1c-1-1-1-2-1-4v-1-4h1l1-1-1-2s0-1-1-2h1l2 1 6 3h0l3 1 1-1 1 1v1c1 1 1 2 3 2v1c1 1 1 1 2 0-1-2-1-4-2-6 0-1 1-3 2-3 1-1 3-3 5-3v1c2 0 4 0 6-1h0 3c-1-2 0-2 0-4z" class="f"></path><path d="M272 196c0-1 1-1 2-1 1 1 1 2 2 3 0 1 0 1-1 1l-2-1c-2-1-3-1-5-2h4z" class="AG"></path><path d="M255 206h8c0 1 0 2-1 3-2 0-5 0-7-1v-2z" class="a"></path><path d="M255 188h1c2 1 4 2 5 3 4 2 8 3 11 5h-4c-4-2-10-4-14-7 1 0 1 0 1-1z" class="D"></path><path d="M248 189l3 2c1 1 2 3 3 4v2l2 1c0 1 0 2-2 3-1 1-2 2-3 2-2 1-3 1-4 1h-1l2-1 1-1c0-3-1-3-2-4 1-1 2-1 3-2v-4l-2-2v-1z" class="Q"></path><path d="M250 177l2-2h1 1v3c1 2 0 3-1 5v3l2 2c0 1 0 1-1 1l-3-1-5-3c-1 0-1-1-2-1h-2l-1-2c2 0 4 0 6-1h0 3c-1-2 0-2 0-4z" class="F"></path><path d="M247 181l4 4 1 1c0 1-1 1-1 2l-5-3c-1 0-1-1-2-1h-2l-1-2c2 0 4 0 6-1z" class="o"></path><path d="M236 184c1-1 3-3 5-3v1l1 2h2l4 5v1l2 2v4c-1 1-2 1-3 2-1 0-4 1-5 1-3 0-3-4-6-5v-1c-1-2-1-4-2-6 0-1 1-3 2-3z" class="AJ"></path><path d="M218 184l2 1 6 3h0l3 1 1-1 1 1v1c1 1 1 2 3 2v1c1 1 1 1 2 0v1c3 1 3 5 6 5 1 0 4-1 5-1 1 1 2 1 2 4l-1 1-2 1h1c1 0 2 0 4-1l2 2-1 1h3v2l-2 1v2h-2-2v1l1 1v2l-1 1c-2-1-3-3-4-4 1 3 0 0 1 2 1 1 0 1 0 1h0c-1-1-2-1-3-2l-9-6-10-8-2-1c-1-1-2-2-3-1l-1 1c-1-1-1-2-1-4v-1-4h1l1-1-1-2s0-1-1-2h1z" class="y"></path><path d="M238 205h1c1 0 2 0 2 1v1l-1 1c-1-1-2-2-2-3z" class="f"></path><path d="M236 194c3 1 3 5 6 5 1 0 4-1 5-1 1 1 2 1 2 4l-1 1-2 1c-1-1-1-1-2-1-3-2-7-5-8-9z" class="c"></path><path d="M218 184l2 1 6 3c0 2 0 3 1 4s2 1 2 2c2 2 4 4 6 5l1 1v2h-4c-1-1-1-1-1-2-2-2-7-4-9-5 1 1 2 3 2 4l-2-1c-1-1-2-2-3-1l-1 1c-1-1-1-2-1-4v-1-4h1l1-1-1-2s0-1-1-2h1z" class="u"></path><path d="M218 184l2 1 6 3c0 2 0 3 1 4h-5l1 1c-2 1-4 0-6 0v-4h1l1-1-1-2s0-1-1-2h1z" class="a"></path><path d="M220 185l6 3c0 2 0 3 1 4h-5l-1-1c1-1 0-1 1-1v-1l-2-4zm43 21c4 2 7 2 10 6l1 4-1 1v2h2c1 1 1 1 1 2l-2 2 1 1h0l2-2 1 1-1 2s0 1 1 2l1-3c0 1 0 3-1 5 1 1 2 3 2 5-2-2-5-4-7-5-1-1-2-2-2-3h0c-1 2-2 3-2 5-1 1-1 1-2 1l-8-5c-3-3-5-5-6-9v5l-2-2c-1-3-6-6-8-8 1 1 2 1 3 2h0s1 0 0-1c-1-2 0 1-1-2 1 1 2 3 4 4l1-1v-2l-1-1v-1h2 2v-2l2-1c2 1 5 1 7 1 1-1 1-2 1-3z" class="f"></path><path d="M275 224l2-2 1 1-1 2s0 1 1 2l-1 1c-1 1-1 1-2 0-1-2-1-2 0-4z" class="AQ"></path><path d="M263 206c4 2 7 2 10 6l1 4-1 1c0-1-1-2-1-3-2-2-5-5-8-6v2l-2-1c1-1 1-2 1-3z" class="p"></path><path d="M253 218c1-2 2-5 4-5 3-1 7 1 9 3s5 6 5 9v1h0c-1 2-2 3-2 5-1 1-1 1-2 1l-8-5c-3-3-5-5-6-9z" class="AJ"></path><path d="M241 281c0-2 0-2 2-4 1 0 1 0 2 1 1 0 1 1 2 1l2 2 1-1c1 3 2 7 2 9l2-1c0 2 0 3-1 4 0 2 1 3 1 4v2c-1 2-1 4-2 5 0 2 0 3 1 4h1c0-1 0-2 1-3 1 0 1-1 2-2-5 25-6 50-7 75 0 6-1 13 0 19v5l2 44v33c0 4 1 9 2 13 0 1 1 2 1 4v1h0l-3-2-2-3c-2-1-3-2-4-3s-2-1-3-2h-1c-1 1-2 1-3 2l-1-2c0-2 0-4-1-6 0-2 1-2 2-2-1-2-3-3-4-4-2-3-4-5-6-8l-7-9c-1 0-2-2-3-2-5-5-12-11-18-15-2 0-3 0-4-1v-1l1-2v-14h-1v-10-1c0-1 0-1 1-2 0 3 1 6 3 9 1 1 2 2 4 2 0 1 0 0 1 1s3 1 5 1c2 1 4 0 6 0-1-1-2-1-2-2l-2-2c2-2 3-4 4-6s3-4 4-6h0l-1 1v-1-1l2-1v-1c0-1 1-2 1-3h2c1-2 1-3 3-3 0-1-1-1-1-2v-1l-1-1c1-2 0-3-1-5 0-1-1-1-1-1-1-1-1-1-1-2-2 0-2-1-3-2 2 1 3 1 4 2l1 1h1c2 0 3 1 5 0v2l1 1 1-1v-2h2c3 0 4-2 6-5v-2c1-1 0-1 0-1v-2c-1 0-2 1-3 1 0-2 1-3 0-5 0-1 0-1 1-2s2-1 3-2h0l1-1 2-1v-1-1c0-2 0-3 1-5h0l1-15c1-5 2-13 0-18l-1 1h-4c-1-3-3-4-6-5v-1l2-1-1-1h1l-1-1c-1 0-2 0-3 1h-3l-1-1-2 1v1l-2-1c0-3 2-7 3-9 0 0-1 0-2-1l1-6c0-2 2-4 2-5 1-2 3-6 5-8 0 0 1-1 2-1l3-5 1-3z" class="AA"></path><path d="M240 320h1c3 2 4 3 6 6l-1 1h-4c-1-3-3-4-6-5v-1l2-1h2z" class="Q"></path><path d="M246 359c1 6-2 11-5 17h0c-1 0-2 1-3 1 0-2 1-3 0-5 0-1 0-1 1-2s2-1 3-2h0l1-1 2-1v-1-1c0-2 0-3 1-5h0z" class="G"></path><path d="M239 370c2 2 2 3 2 6h0c-1 0-2 1-3 1 0-2 1-3 0-5 0-1 0-1 1-2z" class="D"></path><path d="M245 460v3c0 1 1 1 1 2 1 0 0 1 0 1 1 1 1 1 1 2l1 1v-5c1 2 1 5 0 8l1-1c1 2 1 3 0 5 0 2 0 4 1 6l1 1v-1c0-2 1-3 1-4 0 4 1 9 2 13 0 1 1 2 1 4v1h0l-3-2-2-3c-2-1-3-2-4-3s-2-1-3-2h-1c-1 1-2 1-3 2l-1-2c0-2 0-4-1-6 0-2 1-2 2-2l1 1c1 0 1-1 2-1 0-2 0-4 1-5v-1h0l1-1v-1h-1l1-5v-2c0-1 0-2 1-3z" class="u"></path><path d="M243 486c1-1 3-3 4-5h0v1l3 9c-2-1-3-2-4-3s-2-1-3-2z" class="p"></path><path d="M245 460v3c0 1 1 1 1 2 1 0 0 1 0 1 1 1 1 1 1 2l1 1c-1 4-2 9-5 11l-1 1c-1 0-2-1-2-2h0c1 0 1-1 2-1 0-2 0-4 1-5v-1h0l1-1v-1h-1l1-5v-2c0-1 0-2 1-3z" class="H"></path><path d="M242 392c2 2 3 4 4 7h0c2 6 1 15 1 21 1 15 2 29 1 44v5l-1-1c0-1 0-1-1-2 0 0 1-1 0-1 0-1-1-1-1-2v-3l1-3c-1-2-1-3 0-6-2-4-1-7-2-10l-1-5c2-2 0-8 0-11v-1h0c1 1 1 3 1 4v-3h0v-1-2c0-5-1-8-1-12l-1-7c0-1 1-2 1-3h0l-1-5v-3z" class="B"></path><path d="M246 451h1c2 3 1 6 1 9s-1 5-1 8c0-1 0-1-1-2 0 0 1-1 0-1 0-1-1-1-1-2v-3l1-3c-1-2-1-3 0-6z" class="K"></path><path d="M243 400c1 4 0 7 1 11v2c0 2 1 5 1 7l1 17c0 2 1 4 1 6v8h-1c-2-4-1-7-2-10l-1-5c2-2 0-8 0-11v-1h0c1 1 1 3 1 4v-3h0v-1-2c0-5-1-8-1-12l-1-7c0-1 1-2 1-3z" class="J"></path><path d="M220 383c2 1 3 1 4 2l1 1h1c2 0 3 1 5 0v2l1 1 1-1c2-1 3-1 5-1 2 2 3 3 4 5v3l1 5h0c0 1-1 2-1 3l1 7c0 4 1 7 1 12v2 1h0v3c0-1 0-3-1-4h0v1c0 3 2 9 0 11l1 5c1 3 0 6 2 10-1 3-1 4 0 6l-1 3c-1 1-1 2-1 3v2l-1 5h1v1l-1 1h0c-2-3 0-5 0-8v-6c0-1 1-4 1-5-1-2-1-3-1-5l-1-1v-2-4l-4-12-3-10c0-2-1-4-1-5l-4-13c-1 0-2-3-2-4s-1-1-1-2v-1l-1-1c1-2 0-3-1-5 0-1-1-1-1-1-1-1-1-1-1-2-2 0-2-1-3-2z" class="Q"></path><path d="M242 395l1 5h0c0 1-1 2-1 3l-1-1c0-1-1-5 0-6h1v-1z" class="H"></path><path d="M232 393l1-3h2c2 1 3 4 4 5 0 1-1 2-1 3h-2c-1-2-1-2-3-4l-1-1z" class="c"></path><path d="M230 393c1-1 1-2 1-4 1 1 1 2 1 4l1 1c4 8 6 15 8 23v4 1h-1l-2-5c-1-5-2-10-4-15-1-3-3-6-4-9z" class="e"></path><path d="M220 383c2 1 3 1 4 2l1 1h1c2 0 3 1 5 0v2l1 1h-1c0 2 0 3-1 4 1 3 3 6 4 9 2 5 3 10 4 15h0-1v-1c-1-1-1-2-1-3 0-2-1-3-2-5 0-1-1-3-1-4h-1c0-2-1-2-2-3-1 0-2-3-2-4s-1-1-1-2v-1l-1-1c1-2 0-3-1-5 0-1-1-1-1-1-1-1-1-1-1-2-2 0-2-1-3-2z" class="I"></path><path d="M226 386c2 0 3 1 5 0v2l1 1h-1c0 2 0 3-1 4-1-1-2-3-2-4v-1l-2-2z" class="M"></path><path d="M230 401c1 1 2 1 2 3h1c0 1 1 3 1 4 1 2 2 3 2 5 0 1 0 2 1 3v1h1 0l2 5h1v-1-4l2 7v1c0 3 2 9 0 11l1 5c1 3 0 6 2 10-1 3-1 4 0 6l-1 3c-1 1-1 2-1 3v2l-1 5h1v1l-1 1h0c-2-3 0-5 0-8v-6c0-1 1-4 1-5-1-2-1-3-1-5l-1-1v-2-4l-4-12-3-10c0-2-1-4-1-5l-4-13z" class="j"></path><path d="M241 417l2 7v1c0 3 2 9 0 11l-3-14h1v-1-4z" class="F"></path><path d="M223 400h2c1-2 1-3 3-3 0 1 1 4 2 4l4 13c0 1 1 3 1 5l3 10 4 12v4 2l-1 1v4l-1 1v-1c-1 0-2-1-3-1v-1c0-1-1-1-1-2h-2 0l-2-1c-1-1-2-2-2-4 0-1 0-2-1-2-1-2-1-4-2-6l-3-4-1-1-3-5-3-3c-1-1-2-1-2-2l-2-2c2-2 3-4 4-6s3-4 4-6h0l-1 1v-1-1l2-1v-1c0-1 1-2 1-3z" class="h"></path><path d="M239 441v2c1 0 2-1 3-2v4c-1 1-2 1-2 1l-1 1h-1v-1l1-2-1-1c0-1 0-1 1-2z" class="D"></path><path d="M238 441h-1c-2-1-3-2-5-3 2-2 1-2 2-4 1 2 1 2 3 4l1 3z" class="F"></path><path d="M227 435h1v-2c1 1 1 0 1 1 1 4 4 8 5 13v1h0l-2-1c-1-1-2-2-2-4 0-1 0-2-1-2-1-2-1-4-2-6z" class="H"></path><path d="M234 429h-1c-2 1-2 2-4 2h-2 0c-1-1-1-2-2-3l5-5c0 2-1 3 0 4h0s1 0 2-1c1 1 2 1 3 2l-1 1z" class="G"></path><path d="M235 428l2 2 1-1 4 12c-1 1-2 2-3 2v-2h-1l-1-3c-1-4-2-6-3-9l1-1z" class="p"></path><path d="M215 420c1 0 2 0 3-1 1 0 2-2 3-2s2 0 3-1c2 1 3 1 4 3l1 2-2-1h-1c-1-1-2-1-2-2h-1c-1 1-1 3 0 5 1 3 2 4 1 8l-1-1-3-5-3-3c-1-1-2-1-2-2z" class="G"></path><path d="M223 400h2c1-2 1-3 3-3 0 1 1 4 2 4l4 13c0 1 1 3 1 5l3 10-1 1-2-2c-1-1-2-1-3-2-1 1-2 1-2 1h0c-1-1 0-2 0-4l-1-2-1-2c-1-2-2-2-4-3-1 1-2 1-3 1s-2 2-3 2c-1 1-2 1-3 1l-2-2c2-2 3-4 4-6s3-4 4-6h0l-1 1v-1-1l2-1v-1c0-1 1-2 1-3z" class="M"></path><path d="M224 407l1 1 1 2-1 1-1-1-2 1-1-1 3-3z" class="f"></path><path d="M223 400h2c1-2 1-3 3-3 0 1 1 4 2 4l4 13c0 1 1 3 1 5-3-3-5-8-7-11l-3-3v-2-1c-1 2-3 3-4 4h0l-1 1v-1-1l2-1v-1c0-1 1-2 1-3z" class="e"></path><path d="M241 281c0-2 0-2 2-4 1 0 1 0 2 1 1 0 1 1 2 1l2 2 1-1c1 3 2 7 2 9l2-1c0 2 0 3-1 4 0 2 1 3 1 4v2c-1 2-1 4-2 5h0c0 2-1 3-1 5v2l-2 6v7 2c-2-1-4-4-5-6 0-1-1-2-2-2h0c0 1 0 0 1 1v1h0l-2-1v2h-1-2l-1-1h1l-1-1c-1 0-2 0-3 1h-3l-1-1-2 1v1l-2-1c0-3 2-7 3-9 0 0-1 0-2-1l1-6c0-2 2-4 2-5 1-2 3-6 5-8 0 0 1-1 2-1l3-5 1-3z" class="B"></path><path d="M237 304l-1-1v-1l1-1c0 1 0 1 1 2l2-4c0 1 1 3 2 5v1h-1l-1-1v1l-3-1z" class="D"></path><path d="M237 304l3 1-2 2c1 2 1 1 2 1v3c0 1 1 1 1 2l-2 2v-2l-1 1h0c-1-1-1-1-1-2 0-2-1-3-1-5v-1h0l1-2z" class="M"></path><path d="M228 303c0-2 2-4 2-5l1 4h1c0 2 0 3-1 5h0c-1 1-1 2-2 3 0 0-1 0-2-1l1-6z" class="a"></path><path d="M230 313c1-1 1-2 1-3l1-2c0-1 1-1 2-2h1l-1 2c0 1 1 2 2 4h1c0 1 0 1 1 2h0l1-1v2l2-2 1 1c0 2-1 2-1 4v2h-1-2l-1-1h1l-1-1c-1 0-2 0-3 1h-3l-1-1-2 1c1-2 2-4 2-6z" class="k"></path><path d="M241 313l1 1c0 2-1 2-1 4v2h-1l-1-1 1-1c0-1-1-1-1-2-1-1-1-1-1-2h0 0l1-1v2l2-2z" class="G"></path><path d="M230 313c2-1 2-2 3-2l2 4-1 2h1 0 4v1l-1 1-1-1c-1 0-2 0-3 1h-3l-1-1-2 1c1-2 2-4 2-6z" class="i"></path><path d="M241 281c0-2 0-2 2-4 1 0 1 0 2 1 1 0 1 1 2 1l2 2-1 1c-2 0-2-1-4 0s-3 3-3 5c-1 4 2 8 4 11l-2 1 2 5v1c-1-1-1-1-1-2-1 1-1 2-1 3h-2l-1-1h1 1v-1c-1-2-2-4-2-5-1-1-1-2-2-3l-1 2h-1c-1 1-1 2-2 3 0-4 2-8 3-11v-1l3-5 1-3z" class="s"></path><path d="M236 298c1-3 2-5 4-8l3 9 2 5v1c-1-1-1-1-1-2-1 1-1 2-1 3h-2l-1-1h1 1v-1c-1-2-2-4-2-5-1-1-1-2-2-3l-1 2h-1z" class="e"></path><path d="M245 298c1 2 1 3 2 4v1s0 1 1 2l1 1 1-1v1c1 2 0 1 1 2v2l-2 6v7 2c-2-1-4-4-5-6 0-1-1-2-2-2h0c0 1 0 0 1 1v1h0l-2-1c0-2 1-2 1-4l-1-1c0-1-1-1-1-2v-3c-1 0-1 1-2-1l2-2v-1l1 1h-1l1 1h2c0-1 0-2 1-3 0 1 0 1 1 2v-1l-2-5 2-1z" class="p"></path><path d="M249 316c-1-2-1-2-1-4-1-1-1-1-1-2l1-1h1l-1 2h1l2-1-2 6z" class="u"></path><path d="M242 314c0-2 0-3 1-5v-2h1v4h1l1 1c0 1 0 1 1 2 1 3 0 6 2 9v2c-2-1-4-4-5-6 0-1-1-2-2-2h0c0 1 0 0 1 1v1h0l-2-1c0-2 1-2 1-4z" class="M"></path><path d="M250 280c1 3 2 7 2 9l2-1c0 2 0 3-1 4 0 2 1 3 1 4v2c-1 2-1 4-2 5h0c0 2-1 3-1 5-1-1 0 0-1-2v-1l-1 1-1-1c-1-1-1-2-1-2v-1c-1-1-1-2-2-4-2-3-5-7-4-11 0-2 1-4 3-5s2 0 4 0l1-1 1-1z" class="X"></path><path d="M250 280c1 3 2 7 2 9l2-1c0 2 0 3-1 4 0 2 1 3 1 4v2c-1 2-1 4-2 5h0c0 2-1 3-1 5-1-1 0 0-1-2v-1l-1 1-1-1c-1-1-1-2-1-2v-1c0-1 1-1 1-2 1-4 2-8 2-12 0-3-1-4-2-6l1-1 1-1z" class="M"></path><path d="M250 288v2c0 1 0 2 1 3 0 3 0 8 1 10 0 2-1 3-1 5-1-1 0 0-1-2v-1l-1 1-1-1c-1-1-1-2-1-2v-1c0-1 1-1 1-2 1-4 2-8 2-12z" class="e"></path><defs><linearGradient id="X" x1="221.861" y1="471.141" x2="215.966" y2="432.218" xlink:href="#B"><stop offset="0" stop-color="#500607"></stop><stop offset="1" stop-color="#7e0e0f"></stop></linearGradient></defs><path fill="url(#X)" d="M197 411c0-1 0-1 1-2 0 3 1 6 3 9 1 1 2 2 4 2 0 1 0 0 1 1s3 1 5 1c2 1 4 0 6 0l3 3 3 5 1 1 3 4c1 2 1 4 2 6 1 0 1 1 1 2 0 2 1 3 2 4l2 1h0 2c0 1 1 1 1 2v1c1 0 2 1 3 1v1l1-1v-4l1-1 1 1c0 2 0 3 1 5 0 1-1 4-1 5v6c0 3-2 5 0 8v1c-1 1-1 3-1 5-1 0-1 1-2 1l-1-1c-1-2-3-3-4-4-2-3-4-5-6-8l-7-9c-1 0-2-2-3-2-5-5-12-11-18-15-2 0-3 0-4-1v-1l1-2v-14h-1v-10-1z"></path><path d="M208 432c1 0 2 0 3 1s3 1 4 1c0 1-1 2-2 3h-1l-1 1-1-1v-2c-1-1-1-2-2-3z" class="h"></path><path d="M222 434l2 3c-1 1-1 0-1 1l-1 1-1-1c-1 1-1 1-1 3h0v1c-1-3-3-3-3-6l1-1 1 1c1-1 1-1 2-1s0 0 1-1z" class="F"></path><path d="M228 444h0c1 0 1 1 1 1l1-2c0 2 1 3 2 4 0 1 0 2 1 4v2 1h1v2l1 1-1 2-6-15z" class="R"></path><path d="M242 447l1 1c0 2 0 3 1 5 0 1-1 4-1 5v6c0 3-2 5 0 8v1c-1 1-1 3-1 5-1 0-1 1-2 1l-1-1c-1-2-3-3-4-4-2-3-4-5-6-8l-7-9h0c6 4 9 9 14 13 1-1 1-2 1-4-1-3-2-5-3-7l1-2-1-1v-2h-1v-1-2c-1-2-1-3-1-4l2 1h0 2c0 1 1 1 1 2v1c1 0 2 1 3 1v1l1-1v-4l1-1z" class="W"></path><path d="M237 466c1 1 2 3 2 5h-1l-1 1-1-2c1-1 1-2 1-4z" class="I"></path><path d="M242 447l1 1c0 2 0 3 1 5 0 1-1 4-1 5 0-1-1-1-1-2l1-1h-1c0 2 0 3-1 5l-1-1v-1h1c1-3 1-7 1-11z" class="Q"></path><path d="M242 447c0 4 0 8-1 11h-1c0-2-2-2-3-4s-2-3-3-6h0 2c0 1 1 1 1 2v1c1 0 2 1 3 1v1l1-1v-4l1-1z" class="E"></path><path d="M197 411c0-1 0-1 1-2 0 3 1 6 3 9 1 1 2 2 4 2 0 1 0 0 1 1s3 1 5 1c2 1 4 0 6 0l3 3 3 5 1 1 3 4c1 2 1 4 2 6 1 0 1 1 1 2l-1 2s0-1-1-1h0c-1 0-4-6-4-7l-2-3c-1 1 0 1-1 1s-1 0-2 1l-1-1-3-1h0c-1 0-3 0-4-1s-2-1-3-1-2 0-3 1c-1 0-1 1-2 1-1 2-1 3-1 4l-1 2c-2 0-3 0-4-1v-1l1-2v-14h-1v-10-1z" class="M"></path><path d="M220 432l2 2c-1 1 0 1-1 1s-1 0-2 1l-1-1-3-1h4c0-1 1-2 1-2z" class="h"></path><path d="M198 436l1 1 3-2v-1h1c-1 2-1 3-1 4l-1 2c-2 0-3 0-4-1v-1l1-2z" class="G"></path><path d="M197 411c0-1 0-1 1-2 0 3 1 6 3 9 0 1 1 3 2 4s1 1 1 2h3c1 2 2 2 2 4h-3c-3-1-5-4-7-6v-1l-1 1h-1v-10-1z" class="S"></path><path d="M201 418c1 1 2 2 4 2 0 1 0 0 1 1s3 1 5 1c2 1 4 0 6 0l3 3 3 5 1 1 3 4c1 2 1 4 2 6 1 0 1 1 1 2l-1 2s0-1-1-1h0c-1 0-4-6-4-7l-2-3-2-2c-1-1-3-3-4-3-2-1-5-1-7-1 0-2-1-2-2-4h-3c0-1 0-1-1-2s-2-3-2-4z" class="j"></path><path d="M201 418c1 1 2 2 4 2 0 1 0 0 1 1s3 1 5 1c2 1 4 0 6 0l3 3 3 5-1-1c-1-1-2-1-3-3h-1l-1 1c-2 1-2 0-4-1h-2c-2-1-3-2-4-2h-3c0-1 0-1-1-2s-2-3-2-4z" class="K"></path><path d="M804 251l1 1v2c0 1-1 3 0 4h0l1 3v6c0 3 1 8-1 10h0v3c1 1 1 1 3 1 0 1-1 2 0 3 1 2 1 5 1 7v1c0 1 1 1 1 2 1 2 1 3 1 5 1 1 1 2 2 3l2 7c0 1-1 1-1 1-2-2-3-6-5-8 1 4 2 7 4 10 0 1 1 4 1 4 1 2 11 20 12 20h1l2 2c1 2 3 5 4 8l-1 1v-1 2l3 5h-1l14 21c2 4 7 8 8 12l-2 1-2-3v1c0 3 1 5 2 7-1 1 0 1-1 1 0 1 1 2 1 3l-1-1c-4-1-8-3-12-5-2-3-4-4-6-6 0-2 0-2-1-4l-2-1h-2l-1 2-4-2c-2 0-3-1-4-2h-1c-1-2-1-3-2-5 0-1-3-4-4-5h0-1v3c-2-1-3-3-5-4l5 10 1 1c-2 0-3 0-5-1-1 0-1-1-2-2-1 0-3 1-5 0l-1 2h-2v-1l-1 1c-2 0-3 0-4-1h-1c-1 0-1-1-2-1l-1-1-5-5-2-5-4-17-3-46c0-7 1-13 4-19 1-5 5-10 8-13l4-4 12-13z" class="H"></path><path d="M807 313v-6c-1-1-1-2 0-3v1l1 1c1 2 1 4 2 6l-1-1c-1 1-1 1-1 2h-1z" class="L"></path><path d="M795 327c0-3-1-6-1-9h1c0 2 1 5 2 7l2 5-2-1v-1 4 3c-1-2-1-5-2-8z" class="F"></path><path d="M794 290l1-1 1 1v1c-2 3-4 7-5 11h-1c0 3 0 6-1 9l-1 1v-3c0-2-1-6 0-8 1-1 1-1 1-2 2-2 5-6 5-9z" class="s"></path><path d="M792 331v-5l1-5c0 1 1 1 1 1 0 2 0 3 1 5h0c1 3 1 6 2 8 1 6 3 11 5 17l-2 1-3-9-5-13z" class="T"></path><defs><linearGradient id="Y" x1="803.348" y1="313.052" x2="803.768" y2="338.471" xlink:href="#B"><stop offset="0" stop-color="#3b0604"></stop><stop offset="1" stop-color="#6f0d0d"></stop></linearGradient></defs><path fill="url(#Y)" d="M807 313h1c0-1 0-1 1-2l1 1v1l-1 1c0 3 1 5 2 8h-1l-3-3h-1 0v2c-1 1-1 2-1 3v1 1c-1 1 0 2 0 4-1 0 0 1 0 2h-1v4c-1 1-1 1-1 2 0 2 0 1-1 2-2-3-2-6-3-10l-2-5c1-1 2-2 3-4h2c0-1 0-2 1-3s2-2 2-3l1-1 1-1z"></path><path d="M797 335v-3-4 1l2 1c1 4 1 7 3 10 1-1 1 0 1-2 0-1 0-1 1-2v-4h1 0c1 0 2-1 2-1 1 1 1 3 2 3h2c0 3 1 3 0 5h-1-1v1c-2-1-3-2-3-3 0 1 0 1 1 2h-3v2l-1 2c1 2 1 2 3 4v3c0 2 0 4 1 6h0v1 2l-1 1v-1l-1-1-3-6c-2-6-4-11-5-17z" class="a"></path><path d="M803 343c1 2 1 2 3 4v3c-1-1-2-2-3-4v-3z" class="m"></path><path d="M797 329l2 1c1 4 1 7 3 10 1-1 1 0 1-2 0-1 0-1 1-2v-4h1 0c1 0 2-1 2-1 1 1 1 3 2 3h2c0 3 1 3 0 5h-1-1v1c-2-1-3-2-3-3 0 1 0 1 1 2h-3v2l-1 2v3c0-2 0-2-1-4h-1c0-2 0-3-1-4v-1c-1-2-1 0-1-2h0l-1-1c0-2 0-3-1-5z" class="B"></path><path d="M805 324c0-1 0-2 1-3v-2h0 1l3 3h1l12 22c1 2 2 3 3 6-2-1-3-3-4-5v-1l-1 1h0c-1-2-2-3-4-3h0c-1 1-2 2-3 2v1l-2 2-1-1v-1c0-1 0-1-1-2l-1 2h-1c0-3 0-3 2-6h1c1-2 0-2 0-5h-2c-1 0-1-2-2-3 0 0-1 1-2 1h0c0-1-1-2 0-2 0-2-1-3 0-4v-1-1z" class="E"></path><path d="M811 339l1 2-1 2 1 1h1v-3l1-1-1-1v-3h2c0 1 1 1 1 2s0 2 1 4h0c-1 1-2 2-3 2v1l-2 2-1-1v-1c0-1 0-1-1-2l-1 2h-1c0-3 0-3 2-6h1z" class="i"></path><path d="M805 324c0-1 0-2 1-3v-2h0 1l3 3h1l12 22c-2-1-3-3-4-4s-1-2-2-2v-1c-1-1-1-2-1-3-1 1-1 0-2 1h0v-1c0-1 0-2-1-3 0-1-1-2-1-3v-1h-1 0c0-2 0-4-1-5l-5 2z" class="j"></path><path d="M804 341v-2h3c-1-1-1-1-1-2 0 1 1 2 3 3v-1h1c-2 3-2 3-2 6h1l1-2c1 1 1 1 1 2v1l1 1 2-2v-1c1 0 2-1 3-2h0c2 0 3 1 4 3h0l1-1v1c1 2 2 4 4 5l5 10 5 8v3l-2 2c-2 3-3 4-6 4h0c-2 1-2 1-3 2-2 0-3-1-4-2h-1c-1-2-1-3-2-5 0-1-3-4-4-5h0-1v3c-2-1-3-3-5-4-1-2-3-6-3-8l1 1v1l1-1v-2-1h0c-1-2-1-4-1-6v-3c-2-2-2-2-3-4l1-2z" class="G"></path><path d="M806 350c0 1 1 1 2 1 0 1 1 3 2 5 0 2 0 4 1 6h0c-2-2-3-4-4-6h0c-1-2-1-4-1-6z" class="D"></path><path d="M814 367c-1-1-1-2-1-3-1-1-1-3-1-3-1-2-1-3-1-5 1 0 2 1 3 1h1l1-1v-2h2v-1h1c0 2 1 3 2 5 0 1 1 2 1 3h-1l1 2h-1v-2l-1-2c-2 0-2-1-3-2 0 1 0 2 1 4-1 0-1 1-2 1-1 1-1 0-2 1v4h0z" class="AG"></path><path d="M814 367v-4c1-1 1 0 2-1 1 0 1-1 2-1-1-2-1-3-1-4 1 1 1 2 3 2l1 2v2h1c1 2 1 3 1 5l1 3-3-1c0 2 0 3 1 5l-4-3c0-1-3-4-4-5z" class="f"></path><path d="M804 341v-2h3c-1-1-1-1-1-2 0 1 1 2 3 3v-1h1c-2 3-2 3-2 6h1l1-2c1 1 1 1 1 2v1l1 1 2-2v-1c1 0 2-1 3-2h0c2 2 3 3 4 6v2 6h0l2 1v6h-1l-1-2h1c0-1-1-2-1-3-1-2-2-3-2-5h0c-1-1-1-2-1-4h-1c-1 1-3 1-4 1-1 1-1 2-3 2 0-1-1-2-1-3v-1h-1v3c-1 0-2 0-2-1v-3c-2-2-2-2-3-4l1-2z" class="k"></path><path d="M817 342h0v5h-1c-2 0-1-1-2-2v-1c1 0 2-1 3-2z" class="B"></path><path d="M817 342c2 2 3 3 4 6h-3l-1-1v-5z" class="F"></path><path d="M804 341l1-1c1 1 1 1 1 2l1 1h-1v4c-2-2-2-2-3-4l1-2z" class="G"></path><path d="M817 342c2 0 3 1 4 3h0l1-1v1c1 2 2 4 4 5l5 10 5 8v3l-2 2c-2 3-3 4-6 4h0c-2 1-2 1-3 2-2 0-3-1-4-2h-1c-1-2-1-3-2-5l4 3c-1-2-1-3-1-5l3 1-1-3c0-2 0-3-1-5h1v-6l-2-1h0v-6-2c-1-3-2-4-4-6h0 0z" class="B"></path><path d="M827 368l1-1v-4h2c1 1 2 1 3 2l-1-1c-2 2-2 2-3 5h-1 0l-1-1z" class="m"></path><path d="M829 369c1-3 1-3 3-5l1 1 1 2h1c0 2 0 3 1 4l-2 2 1-1c-1 0-1-1-2-1 0-1-1-2-1-3l-3 1z" class="G"></path><path d="M823 368c1 1 2 1 3 1l1-1 1 1h0 1l3-1c0 1 1 2 1 3 1 0 1 1 2 1l-1 1c-2 3-3 4-6 4v-1c0-1 0-1 1-2 1 1 1 0 2 0l-2-3-1 1-1-1h-2l-1 1v-1l-1-3z" class="p"></path><path d="M817 342c2 0 3 1 4 3h0l1-1v1c1 2 2 4 4 5l5 10c-1-1-1-1-3-2 0 0 0-1-1-1h-2c-1-1-1-2-1-3-1-2-1-2-3-4v-2c-1-3-2-4-4-6h0 0z" class="J"></path><path d="M822 375c-1-2-1-3-1-5l3 1v1l1-1h2l1 1 1-1 2 3c-1 0-1 1-2 0-1 1-1 1-1 2v1h0c-2 1-2 1-3 2-2 0-3-1-4-2h-1c-1-2-1-3-2-5l4 3z" class="s"></path><path d="M818 372l4 3c2 1 3 2 6 2-2 1-2 1-3 2-2 0-3-1-4-2h-1c-1-2-1-3-2-5z" class="j"></path><path d="M811 322c-1-3-2-5-2-8l1-1c1 1 2 4 3 4l1-1c1 2 11 20 12 20h1l2 2c1 2 3 5 4 8l-1 1v-1 2l3 5h-1l14 21c2 4 7 8 8 12l-2 1-2-3v1c0 3 1 5 2 7-1 1 0 1-1 1 0 1 1 2 1 3l-1-1c-4-1-8-3-12-5-2-3-4-4-6-6 0-2 0-2-1-4l-2-1h-2l-1 2-4-2c1-1 1-1 3-2h0c3 0 4-1 6-4l2-2v-3l-5-8-5-10c-1-3-2-4-3-6l-12-22z" class="AC"></path><defs><linearGradient id="Z" x1="842.739" y1="375.454" x2="850.761" y2="373.046" xlink:href="#B"><stop offset="0" stop-color="#972c34"></stop><stop offset="1" stop-color="#c14449"></stop></linearGradient></defs><path fill="url(#Z)" d="M813 317l1-1c1 2 11 20 12 20h1l2 2c1 2 3 5 4 8l-1 1v-1 2l3 5h-1l14 21c2 4 7 8 8 12l-2 1-2-3c-12-15-20-32-30-49l-9-18z"></path><path d="M826 336h1l2 2c1 2 3 5 4 8l-1 1v-1 2l3 5h-1l-8-17z" class="X"></path><path d="M836 368c1 1 2 2 2 3l1 2 3 5c1 2 5 5 6 8l5 7c0 1 1 2 1 3l-1-1c-4-1-8-3-12-5-2-3-4-4-6-6 0-2 0-2-1-4l-2-1h-2l-1 2-4-2c1-1 1-1 3-2h0c3 0 4-1 6-4l2-2v-3z" class="i"></path><path d="M848 386l5 7c0 1 1 2 1 3l-1-1s-1-1-1-2c-1-1-2-2-4-2-1 0-2-2-2-3h1c0-1 0-1 1-2z" class="V"></path><path d="M842 378c1 2 5 5 6 8-1 1-1 1-1 2h-1c-2-3-3-6-4-10z" class="I"></path><path d="M836 368c1 1 2 2 2 3l1 2c0 2 0 4 1 6 0 1 1 4 1 5-2 0-4-1-5-2 0-1-1-1-2-2l-2-1h-2l-1 2-4-2c1-1 1-1 3-2h0c3 0 4-1 6-4l2-2v-3z" class="m"></path><path d="M836 368c1 1 2 2 2 3-2 4-2 6-6 8h-2l-1 2-4-2c1-1 1-1 3-2h0c3 0 4-1 6-4l2-2v-3z" class="I"></path><path d="M788 312l1-1c1-3 1-6 1-9h1 0c0 2 0 2 1 3-1 3 0 6 0 9 0 2 0 4-1 5 0 1 0 2 1 3v3c-1 2-1 4 0 6l5 13 3 9 2-1 3 6c0 2 2 6 3 8l5 10 1 1c-2 0-3 0-5-1-1 0-1-1-2-2-1 0-3 1-5 0l-1 2h-2v-1l-1 1c-2 0-3 0-4-1h-1c-1 0-1-1-2-1l-1-1-5-5-2-5 3 3v-2c1-1 0-5 0-7 0-5 0-10-1-15-2 2-2 7-2 9-1-3 0-6 0-9v-16-1l1 2v-1c1-2 1-4 1-5s0-1 1-2h0v-2l1-2c0-1 1-2 1-3z" class="F"></path><path d="M789 338c1 1 1 3 1 5 1 1 1 1 2 1 0 1 2 2 3 3l-1 1c-1 0-3-3-4-3s-1 1-3 1l1-2-1-1 1-2v-1c0-1 1-2 1-2z" class="V"></path><path d="M788 340c-1-1-1-2-1-3l1 1v-1c0-1 1-3 0-3 0-1-1-1-1-2s1-4 2-5v-1h0 1l1 1-1 2v1 3c0 1 0 1-1 2v3s-1 1-1 2z" class="K"></path><path d="M790 333c2 4 3 8 5 13l1 1h0l-2-2v-1h-2c-1 0-1 0-2-1 0-2 0-4-1-5v-3c1-1 1-1 1-2z" class="I"></path><path d="M792 322v3c-1 2-1 4 0 6l5 13c-1 0-1 1-2 2-2-5-3-9-5-13v-3h1s0-6 1-8z" class="J"></path><path d="M802 352l3 6c0 2 2 6 3 8l5 10 1 1c-2 0-3 0-5-1-1 0-1-1-2-2-1 0-3 1-5 0l-1 2h-2v-1l-1 1c-2 0-3 0-4-1h-1c-1 0-1-1-2-1l-1-1 1-1h0l1 1 1 1v-1c-1-1-1-1-1-3 1-1 1-1 3-1h0c-1-1-1-2-2-3-1 0-1 0-2-1 1 0 1 0 2-1 0 1 1 1 1 2 1 2 2 2 3 3 2-1 1 0 2 0s1-1 2-1c1 1 2 1 4 2-1-1 0-2 0-4 0-1-4-11-5-13l2-1z" class="a"></path><path d="M793 373v-2l1-1c2 1 3 2 4 3l1 1c0-1 1-3 2-4 1 0 2 0 3 1 1 0 1 1 1 2l2 1c-1 0-3 1-5 0l-1 2h-2v-1l-1 1c-2 0-3 0-4-1h-1c-1 0-1-1-2-1l-1-1 1-1h0l1 1 1 1v-1z" class="G"></path><path d="M802 352l3 6c0 2 2 6 3 8l5 10h-3c-2-2-4-7-5-10 0-1-4-11-5-13l2-1z" class="O"></path><path d="M804 251l1 1v2c0 1-1 3 0 4h0l1 3v6c0 3 1 8-1 10h0c-1 1-1 1-2 3l-1 1c-1 1-1 2-1 3-2 1-3 3-6 3h-2c0 1 0 2 1 3 0 3-3 7-5 9 0 1 0 1-1 2-1 2 0 6 0 8v3c0 1-1 2-1 3l-1 2v2h0c-1 1-1 1-1 2s0 3-1 5v1l-1-2v1 16c0 3-1 6 0 9 0-2 0-7 2-9 1 5 1 10 1 15 0 2 1 6 0 7v2l-3-3-4-17-3-46c0-7 1-13 4-19 1-5 5-10 8-13l4-4 12-13z" class="o"></path><path d="M784 299h2l1-1c0 1 1 2 1 3h0c-1 2 0 6 0 8v3c0 1-1 2-1 3l-1 2v2h0c-1 1-1 1-1 2s0 3-1 5v1l-1-2v1c-1-5-1-10 0-15l1-12z" class="B"></path><path d="M786 317h0c-1-4-1-7-1-11h1c0 1 0 2 1 3h1v3c0 1-1 2-1 3l-1 2z" class="D"></path><path d="M788 283c1-1 2-3 3-5l3 1c1 3 0 5-1 7v1c0 1 0 2 1 3 0 3-3 7-5 9 0 1 0 1-1 2h0c0-1-1-2-1-3l-1 1h-2c1-4 1-6 3-9h-1c1-1 0-1 1-1v-1c1-1 2 0 4 0h0c0-1-1-2-2-3 0-1 0-1-1-1v-1z" class="X"></path><path d="M787 290c1 2 1 8 2 9 0 1 0 1-1 2h0c0-1-1-2-1-3l-1 1h-2c1-4 1-6 3-9z" class="a"></path><path d="M804 251l1 1v2c0 1-1 3 0 4h0l-1 2h-1l-2 4-5 6c-1 1-1 2-1 3l-1 1c-1 1-2 2-1 3 0 1 0 2 1 2l-3-1c-1 2-2 4-3 5l-2 2c-1-2 0-3 1-5 0-1 1-2 1-2 1-1 1-1 1-2l-1-2h-3c-1 1-1 2-2 3s-1 2-2 3l-1 1c1-5 5-10 8-13l4-4 12-13z" class="W"></path><path d="M794 274l1-5c1-3 4-9 7-10l1 1-2 4-5 6c-1 1-1 2-1 3l-1 1z" class="E"></path><path d="M805 258l1 3v6c0 3 1 8-1 10h0c-1 1-1 1-2 3l-1 1c-1 1-1 2-1 3-2 1-3 3-6 3h-2v-1c1-2 2-4 1-7-1 0-1-1-1-2-1-1 0-2 1-3l1-1c0-1 0-2 1-3l5-6 2-4h1l1-2z" class="G"></path><path d="M803 260h1v5c-1-1-2-1-3-1l2-4z" class="c"></path><path d="M801 264c1 0 2 0 3 1 0 3 0 6-3 8-1 1-2 1-4 1 0 0-1 0-2-1 0-1 0-2 1-3l5-6z" class="X"></path><path d="M806 267c0 3 1 8-1 10h0c-1 1-1 1-2 3l-1 1c-1 1-1 2-1 3-2 1-3 3-6 3 1-1 1-3 2-4h1l-1-1c0-2 0-3-1-5h0l1-1c1 0 2 0 2-1 4-2 6-4 7-8z" class="K"></path><defs><linearGradient id="a" x1="910.607" y1="452.568" x2="814.79" y2="521.547" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#240302"></stop></linearGradient></defs><path fill="url(#a)" d="M808 366c2 1 3 3 5 4v-3h1 0c1 1 4 4 4 5 1 2 1 3 2 5h1c1 1 2 2 4 2l4 2 1-2h2l2 1c1 2 1 2 1 4 2 2 4 3 6 6 4 2 8 4 12 5l1 1c0-1-1-2-1-3 1 0 0 0 1-1-1-2-2-4-2-7v-1l2 3 2-1 1 1c1 1 2 3 3 4 2 1 2 1 4 3 2 0 3 3 4 2l1-1c2 2 3 3 6 4 1 1 1 2 1 3h1c2 0 2 0 3 1 0 0 1 0 1-1h1l-1 3c0 1 1 1 1 3 0 1 0 3 1 4l5 7c9 12 17 26 21 41l2-1v-2c1 4 1 7 2 11v3h2v5c0 6 1 13 0 19-1 11-1 22-4 33l-1-1h0c-2-1-1-1-2-3v3c-2-4-2-9-2-13-1-17-1-36-6-52-6-12-13-19-25-23-9-3-16-2-23 2-16 7-29 17-40 29-4 4-8 8-10 13-1 4-1 9-1 13l5 7c1 1 1 1 1 3h-1l-1-1c-6-4-13-7-18-12v-1c-1-1-1-2-1-4l-4-5 1-1 1-1-1-1h-2c2-2 3-4 5-6 0-1 1-2 2-3l4-4 2-6c-1-2-1-3-2-4v-1h1v-1h-3l1-1h1c1-1 2-2 3-2v-2h2c1 0 1 1 2 1s1 1 2 2h1l1-2c1 0 2-2 3-3h0c1-1 1-1 2 0l1-1c-1-1-2-3-3-4l-1-4c1-1 2 0 3 1v-1c-1-2-1-3-1-4-1-3-1-5 1-8h-1v-4l2-1c0-1-1-1-2-1l-1-3c-1-1-2-3-3-4v-2c0-1 0-2-1-3s-1-3-2-5l-1-2 2-2 2-2v-3l-1-1c0 2 0 3-1 4l-2 2v2c-2 0-2 0-3-1 1-1 2-1 2-3 0-1 0-1-1-2 0-1-1-2-2-2l-2 1c0-1 0-1-1-1h0c0-1 0-2 1-2-1-2 0-2-2-3h-2 0 0c-1-1-2-1-3-2l1-1h5c1 0 3 0 4 1 0 1-1 1 1 3l1-1v-2l1-1 1-1c5-2 8-3 14-3h-4v-1h4c1-1 1-1 2-1l-1-1-1-2-1-1-5-10z"></path><path d="M792 473c0 2 0 3-1 4l1 2-1 2-3-3 4-5z" class="o"></path><path d="M860 423l2 1-15 10c1 0 1-1 2-2h0-3l13-8 1-1z" class="G"></path><path d="M911 457c1 4 1 7 2 11v3 4c0 2 1 3 0 4h-1v-4c-1-1-1-3-1-5 0-3-1-7-2-10l2-1v-2zm-41-45c1-1 2-1 3-2v-1-1c0 1 1 2 1 3 1 0 1 0 2 1l3 5-1-1c-2 0-3-2-3-3h-1c-3 4-8 9-12 11h0l-2-1 2-1c4-3 7-6 8-10z" class="k"></path><path d="M810 462c1 0 0 0 1 1-2 2-3 4-4 6-3 4-7 6-9 10-1 2-1 4-3 6h-1c0-1 0-1-1-1 1-2 1-3 2-5h0c3-3 5-6 8-9 2-3 4-6 7-8z" class="S"></path><path d="M788 478l3 3c-1 1-1 2-2 3v3c1 2 4 5 6 7v1c-4-1-6-4-9-7l-4-5 1-1 1-1c1-1 2-2 4-3z" class="e"></path><path d="M788 478l3 3c-1 1-1 2-2 3v-2c-1 0-2 1-3 1 0 1 0 1-1 1s-2-1-2-2l1-1c1-1 2-2 4-3z" class="I"></path><path d="M913 471h2v5c0 6 1 13 0 19h-1l1-1h-1c-1 1-1 2-2 3l-1 1 1-23v4h1c1-1 0-2 0-4v-4z" class="h"></path><path d="M912 497c1-4 1-8 2-12v-9h1c0 6 1 13 0 19h-1l1-1h-1c-1 1-1 2-2 3z" class="I"></path><path d="M786 488c3 3 5 6 9 7 2 1 4 3 6 4 2 2 3 4 5 4 1 1 1 1 1 3h-1l-1-1c-6-4-13-7-18-12v-1c-1-1-1-2-1-4z" class="Af"></path><path d="M912 497c1-1 1-2 2-3h1l-1 1h1c-1 11-1 22-4 33l-1-1c0-3 0-5 1-7v-16-6l1-1z" class="O"></path><path d="M871 429h-7l9-6 2-2c2 1 3 2 4 3l1 2c2 4 6 8 9 12l-6-4c-2-1-3-2-5-3h-1l-6-2z" class="H"></path><path d="M871 429c1-3 4-5 6-6v5l1 2-1 1-6-2z" class="V"></path><defs><linearGradient id="b" x1="909.934" y1="471.559" x2="898.066" y2="478.941" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#330102"></stop></linearGradient></defs><path fill="url(#b)" d="M900 462c1 1 2 1 2 2h1c0-2 0-3-1-5h0 0c1 1 1 3 2 3h0l1 2v2l1 2v2c0 1 1 1 1 2s0 1 1 3c0 6 2 15-1 21v7c0 7 0 14 1 21v3c-2-4-2-9-2-13-1-17-1-36-6-52z"></path><path d="M852 384l2 3 2-1 1 1c1 1 2 3 3 4 2 1 2 1 4 3 2 0 3 3 4 2l1-1c2 2 3 3 6 4 1 1 1 2 1 3h1c2 0 2 0 3 1 0 0 1 0 1-1h1l-1 3c0 1 1 1 1 3 0 1 0 3 1 4l5 7c-2-1-2-1-4-1 2 5 5 10 8 15 1 1 3 4 3 6-5-8-10-16-16-22l-3-5c-1-1-1-1-2-1 0-1-1-2-1-3v1 1c-1 1-2 1-3 2l-1-2c-1-4-4-5-7-7 0-1 0-2-1-4v1c-2-1-5-3-7-4 0-1-1-2-1-3 1 0 0 0 1-1-1-2-2-4-2-7v-1z" class="AQ"></path><path d="M881 405c0 1 1 1 1 3 0 1 0 3 1 4l5 7c-2-1-2-1-4-1-2-1-6-8-7-10l4-3z" class="O"></path><path d="M852 384l2 3 2-1 1 1c1 1 2 3 3 4l1 3c-1-1-2-2-4-2l4 7v1c-2-1-5-3-7-4 0-1-1-2-1-3 1 0 0 0 1-1-1-2-2-4-2-7v-1z" class="j"></path><path d="M856 386l1 1c1 1 2 3 3 4l1 3c-1-1-2-2-4-2-1-1-2-4-3-5l2-1z" class="Af"></path><path d="M860 391c2 1 2 1 4 3 2 0 3 3 4 2l1-1c2 2 3 3 6 4 1 1 1 2 1 3l-3 3c-5-2-9-6-12-11l-1-3z" class="H"></path><path d="M846 432h3 0c-1 1-1 2-2 2-6 5-14 9-21 13-11 6-21 14-30 22l-4 4-4 5c-2 1-3 2-4 3l-1-1h-2c2-2 3-4 5-6 0-1 1-2 2-3l4-4 2-6c-1-2-1-3-2-4v-1h1v-1h-3l1-1h1c1-1 2-2 3-2v-2h2c1 0 1 1 2 1s1 1 2 2h1l1-2c1 0 2-2 3-3h0c1-1 1-1 2 0l1-1c-1-1-2-3-3-4l-1-4c1-1 2 0 3 1v-1c2 0 3 1 5 1v1h-1c1 1 2 2 3 2 2 0 2 0 3-1l1-1h0 1c2-1 4-2 7-2 1 0 3-1 4-2v1c-1 1-2 2-3 2-1 1-2 1-3 2 2-1 3-2 5-1 5-2 11-6 16-9z" class="D"></path><path d="M786 474c1 0 2-1 3-2 0 0 0-1 1-1 2-2 2-3 5-3l1 1-4 4-4 5c-2 1-3 2-4 3l-1-1h-2c2-2 3-4 5-6z" class="p"></path><path d="M827 439c1 0 3-1 4-2v1c-1 1-2 2-3 2-1 1-2 1-3 2 2-1 3-2 5-1l-18 9-1-1-2-2c-1-1-2-3-3-4l-1-4c1-1 2 0 3 1v-1c2 0 3 1 5 1v1h-1c1 1 2 2 3 2 2 0 2 0 3-1l1-1h0 1c2-1 4-2 7-2z" class="h"></path><path d="M806 443l-1-4c1-1 2 0 3 1 1 2 3 4 6 5v1l1 1c0 1 0 1-1 2h-3l-2-2c-1-1-2-3-3-4z" class="S"></path><path d="M827 439c1 0 3-1 4-2v1c-1 1-2 2-3 2-1 1-2 1-3 2 2-1 3-2 5-1l-18 9-1-1h3c1-1 1-1 1-2l-1-1c4 0 10-5 13-7z" class="W"></path><defs><linearGradient id="c" x1="793.463" y1="456.091" x2="809.183" y2="455.444" xlink:href="#B"><stop offset="0" stop-color="#370404"></stop><stop offset="1" stop-color="#5d0d0d"></stop></linearGradient></defs><path fill="url(#c)" d="M809 447l2 2 1 1-20 17 2-6c-1-2-1-3-2-4v-1h1v-1h-3l1-1h1c1-1 2-2 3-2v-2h2c1 0 1 1 2 1s1 1 2 2h1l1-2c1 0 2-2 3-3h0c1-1 1-1 2 0l1-1z"></path><path d="M797 450c1 0 1 1 2 1s1 1 2 2h1c-1 1-2 1-3 1-1-1-3-2-4-2v-2h2z" class="V"></path><defs><linearGradient id="d" x1="835.887" y1="387.137" x2="809.496" y2="426.827" xlink:href="#B"><stop offset="0" stop-color="#000100"></stop><stop offset="1" stop-color="#3a0809"></stop></linearGradient></defs><path fill="url(#d)" d="M808 366c2 1 3 3 5 4v-3h1 0c1 1 4 4 4 5 1 2 1 3 2 5h1c1 1 2 2 4 2l4 2 1-2h2l2 1c1 2 1 2 1 4 2 2 4 3 6 6 4 2 8 4 12 5l1 1c2 1 5 3 7 4v-1c1 2 1 3 1 4 3 2 6 3 7 7l1 2c-1 4-4 7-8 10l-2 1-1 1-13 8c-5 3-11 7-16 9-2-1-3 0-5 1 1-1 2-1 3-2 1 0 2-1 3-2v-1c-1 1-3 2-4 2-3 0-5 1-7 2h-1 0l-1 1c-1 1-1 1-3 1-1 0-2-1-3-2h1v-1c-2 0-3-1-5-1-1-2-1-3-1-4-1-3-1-5 1-8h-1v-4l2-1c0-1-1-1-2-1l-1-3c-1-1-2-3-3-4v-2c0-1 0-2-1-3s-1-3-2-5l-1-2 2-2 2-2v-3l-1-1c0 2 0 3-1 4l-2 2v2c-2 0-2 0-3-1 1-1 2-1 2-3 0-1 0-1-1-2 0-1-1-2-2-2l-2 1c0-1 0-1-1-1h0c0-1 0-2 1-2-1-2 0-2-2-3h-2 0 0c-1-1-2-1-3-2l1-1h5c1 0 3 0 4 1 0 1-1 1 1 3l1-1v-2l1-1 1-1c5-2 8-3 14-3h-4v-1h4c1-1 1-1 2-1l-1-1-1-2-1-1-5-10z"></path><path d="M854 414c2 0 2 0 4 1 0 1 0 1-1 2-1 2-4 3-6 5h-2 0l3-3c1 0 1-1 2-2v-2-1z" class="Q"></path><path d="M812 416h1c1 1 1 1 1 0 1 1 1 1 1 2h1l2 1h1l-10 3c0-1-1-1-2-1l-1-3 1-1 3 3c1-1 2-1 2-2v-2z" class="k"></path><path d="M846 415l8-1v1 2c-1 1-1 2-2 2l-3 3h0-1c2-2 2-2 2-4v-1h-2c-1 0-2-1-2-2z" class="H"></path><path d="M846 415c0 1 1 2 2 2h2v1c0 2 0 2-2 4h1 0 0l-3 3h-2l2-2c1-1 1-2 1-3l-1-1c-2-1-3 0-5 0 0-1-1-2-1-2l6-2z" class="D"></path><path d="M841 390c4 2 8 4 12 5l1 1c2 1 5 3 7 4v-1c1 2 1 3 1 4l-22-11c1 0 3 1 4 1l2 1-1-1-1-1c-1 0-2-1-2-2h-1 0z" class="AF"></path><path d="M845 402l7 7c-2 1-4 1-6 2-3 1-6 3-9 3v-1-1c0-1 1-1 1-2v-1c1 0 2-1 2-1 3-1 4 0 5-2v-4z" class="Q"></path><path d="M834 419l6-2s1 1 1 2c2 0 3-1 5 0l1 1c0 1 0 2-1 3l-2 2h-1c-1 0-2-1-3-1v1c-2-1-3-1-5-1v-2c1-1 1-1 1-2l-2-1z" class="X"></path><path d="M834 419l6-2s1 1 1 2c-1 1-1 1-2 3l1 2v1c-2-1-3-1-5-1v-2c1-1 1-1 1-2l-2-1z" class="G"></path><path d="M816 424h2c6-1 11-3 16-5l2 1c0 1 0 1-1 2l-2 1c-2 0-3 1-5 2l-5 3h0c-1-1-1-1-2-1-2 0-3 0-5 1l-1 1-2-2h1-1l-1-1c2 0 2 0 3-1l1-1z" class="p"></path><path d="M805 394l1-1h0c2 0 3-1 5-1 2-1 4 0 6 0 2 1 4 0 6 1 5 0 11 0 16 3h-14l-3 1c0 1 1 1 1 2h-3-3c-2-1-3-1-5 0l-1 1h-3c-1 0-2 0-3 2 0 0 0 1-1 2h0c-2 0-2-1-3 0h-1l-1-2 2-2 2-2 1-1c0-1 0-2 1-3z" class="T"></path><path d="M811 395c2 0 4 0 6-1 1 0 1 0 2 1h4s1 0 2 1l-3 1c0 1 1 1 1 2h-3-3c-2-1-3-1-5 0l-1 1h-3l2-3c1 0 1-1 2-1l-1-1z" class="V"></path><path d="M805 394h1l1 1c0 1 0 0 1 1l3-1 1 1c-1 0-1 1-2 1l-2 3c-1 0-2 0-3 2 0 0 0 1-1 2h0c-2 0-2-1-3 0h-1l-1-2 2-2 2-2 1-1c0-1 0-2 1-3z" class="E"></path><path d="M805 394h1l1 1c0 1 0 0 1 1-1 1-1 2-2 2h-2l-1 2h-2l2-2 1-1c0-1 0-2 1-3z" class="V"></path><path d="M851 422v2h1l7-3v2h0-1l1 1-13 8c-5 3-11 7-16 9-2-1-3 0-5 1 1-1 2-1 3-2 1 0 2-1 3-2v-1h0l4-3-1-1 1-1-1-1h-1v-5h2v-2c2 0 3 0 5 1v-1c1 0 2 1 3 1h1 2l3-3h2z" class="R"></path><path d="M844 425h2l-1 2c-2 2-5 4-7 5-1 0-2 1-3 2l-1-1 1-1c0-1 1-1 2-2 2-1 4-3 6-5h1z" class="E"></path><path d="M835 424c2 0 3 0 5 1v-1c1 0 2 1 3 1-2 2-4 4-6 5-1 1-2 1-2 2l-1-1h-1v-5h2v-2z" class="O"></path><path d="M835 424c2 0 3 0 5 1h0-3c-1 1 0 2 0 3h-1c-1 1-1 1-1 2v1h-1-1v-5h2v-2z" class="J"></path><path d="M808 366c2 1 3 3 5 4v-3h1 0c1 1 4 4 4 5 1 2 1 3 2 5h1c1 1 2 2 4 2l4 2 1-2h2l2 1c1 2 1 2 1 4 2 2 4 3 6 6h0 1c0 1 1 2 2 2l1 1 1 1-2-1c-1 0-3-1-4-1-3 0-6-3-9-4-5-3-11-5-17-6h-4v-1h4c1-1 1-1 2-1l-1-1-1-2-1-1-5-10z" class="AQ"></path><path d="M814 367h0c1 1 4 4 4 5 1 2 1 3 2 5h1l-4-1c-1-2-3-4-4-6v-3h1z" class="e"></path><path d="M817 376l4 1c1 1 2 2 4 2l4 2 1-2h2l2 1c1 2 1 2 1 4 2 2 4 3 6 6h0c-9-5-16-8-24-14z" class="D"></path><path d="M829 381l1-2h2l2 1c1 2 1 2 1 4l-6-3z" class="E"></path><path d="M811 400l1-1c2-1 3-1 5 0l3 3c-1 2-2 3-3 4 1 0 2 1 3 1v-1l3 1v1l2 3-2 2 1 1c-2 2-3 4-6 5l-2-1h-1c0-1 0-1-1-2 0 1 0 1-1 0h-1v2c0 1-1 1-2 2l-3-3-1 1c-1-1-2-3-3-4v-2c0-1 0-2-1-3s-1-3-2-5h1c1-1 1 0 3 0h0c1-1 1-2 1-2 1-2 2-2 3-2h3z" class="e"></path><path d="M811 400l1-1c2-1 3-1 5 0l3 3c-1 2-2 3-3 4-1 0-2 1-3 1-1-1-2-2-3-4-1-1 0-2 0-3z" class="h"></path><path d="M802 409l1-2h3 0v-1l1-1 3 6c0 1 1 2 0 3 1 1 2 1 2 2v2c0 1-1 1-2 2l-3-3-1 1c-1-1-2-3-3-4v-2c0-1 0-2-1-3z" class="D"></path><path d="M802 409l1-2h3 0v-1l1-1 3 6c0 1 1 2 0 3l-1-2c-1-1-1-2-2-3l-1 1c-1 2-1 3 0 6 1 0 1 1 1 1l-1 1c-1-1-2-3-3-4v-2c0-1 0-2-1-3z" class="a"></path><path d="M820 406l3 1v1l2 3-2 2 1 1c-2 2-3 4-6 5l-2-1h-1c0-1 0-1-1-2 0 1 0 1-1 0h-1c0-1-1-1-2-2 1-1 0-2 0-3 1 1 1 1 2 1 0-1-1-2-1-3v-1h2c0 1 1 2 2 2h1l1-1c-2 0-1 0-2-1h2l1 1h1l1-2v-1z" class="h"></path><path d="M822 409h0c0 1-1 1-1 2s1 1 1 2c-1 1-2 2-3 2l-1-2v-1h0c1-2 2-2 4-3z" class="B"></path><path d="M835 422v2 2h-2v5h1l1 1-1 1 1 1-4 3h0c-1 1-3 2-4 2-3 0-5 1-7 2h-1 0l-1 1c-1 1-1 1-3 1-1 0-2-1-3-2h1v-1c-2 0-3-1-5-1-1-2-1-3-1-4-1-3-1-5 1-8l8-3-1 1c-1 1-1 1-3 1l1 1h1-1l2 2 1-1c2-1 3-1 5-1 1 0 1 0 2 1h0l5-3c2-1 3-2 5-2l2-1z" class="AG"></path><path d="M817 438l2 1v2l-1 1c-1 1-1 1-3 1v-1l-1-1c1 0 1 0 1-1l2-2z" class="a"></path><path d="M813 427l2 2 1-1c2-1 3-1 5-1 1 0 1 0 2 1-5 1-5 2-8 5l-1 1c-1 0-1 0-1-1h0l-1-1-1 2-1 1h-1v-1c-1-3 2-4 4-6v-1z" class="f"></path><path d="M823 428h0l1 1c0 2-2 4-3 6h0c-1 0-2 1-3 1l-3-3c3-3 3-4 8-5z" class="R"></path><path d="M835 422v2 2h-2v5h1l1 1-1 1 1 1-4 3h0c-1 1-3 2-4 2-3 0-5 1-7 2h-1 0v-2l-2-1c2-1 3-1 4-3h0 0c1-2 3-4 3-6l-1-1 5-3c2-1 3-2 5-2l2-1z" class="D"></path><path d="M835 422v2 2h-2v5c-2 2-3 3-6 4l1-3-1-2c1 0 0 0 1-1s0-2 0-4c2-1 3-2 5-2l2-1z" class="e"></path><path d="M828 425c2-1 3-2 5-2 0 1-2 2-2 3-1 3 1 5-3 6l-1-2c1 0 0 0 1-1s0-2 0-4z" class="a"></path><path d="M833 431h1l1 1-1 1 1 1-4 3h0c-1 1-3 2-4 2-3 0-5 1-7 2h-1 0v-2c3-1 5-2 8-4 3-1 4-2 6-4z" class="H"></path><path d="M825 396h14c1 1 2 2 3 4l2 1 1 1v4c-1 2-2 1-5 2 0 0-1 1-2 1v1c0 1-1 1-1 2v1 1l-4 1c-1 0-3 1-4 1l-2 1-8 2h-1c3-1 4-3 6-5l-1-1 2-2-2-3v-1l-3-1v1c-1 0-2-1-3-1 1-1 2-2 3-4l-3-3h3 3c0-1-1-1-1-2l3-1z" class="J"></path><path d="M837 403c2-1 2-1 4-1l-1 2-1 1-2 2-1-1 1-3z" class="e"></path><path d="M844 401l1 1v4c-1-2-3-1-5-2l1-2 2 1c1-1 1-1 1-2z" class="K"></path><path d="M838 409v1c0 1-1 1-1 2v1 1l-4 1v-1c1-1 0-1 1-2 1 0 2-1 2-2l1-1v1l1-1z" class="H"></path><path d="M837 399c2 0 3 0 5 1l2 1c0 1 0 1-1 2l-2-1c-2 0-2 0-4 1v-4z" class="J"></path><path d="M833 403v-3h-1l2-1h0 3v4l-1 3s-1 1-1 2h-1c-1-2-1-3-1-5z" class="H"></path><path d="M832 406c0 2 0 4-1 5-2 2-3 4-4 6l-8 2h-1c3-1 4-3 6-5 1-2 2-3 4-5h0c0-2 0-1 1-2 0 0 1 0 2-1h1z" class="G"></path><defs><linearGradient id="e" x1="829.407" y1="395.238" x2="823.824" y2="408.209" xlink:href="#B"><stop offset="0" stop-color="#4d0909"></stop><stop offset="1" stop-color="#740e11"></stop></linearGradient></defs><path fill="url(#e)" d="M825 396h14c1 1 2 2 3 4-2-1-3-1-5-1h-3 0l-2 1h1v3c-1 1-1 2-1 3h-1c-1 1-2 1-2 1-1 1-1 0-1 2h0c-2 2-3 3-4 5l-1-1 2-2-2-3v-1l-3-1v1c-1 0-2-1-3-1 1-1 2-2 3-4l-3-3h3 3c0-1-1-1-1-2l3-1z"></path><path d="M822 405c1-1 2-1 4-1l-3 3h0l-3-1 2-1z" class="e"></path><path d="M817 399h3l3 1-1 1-2 2c0 1 1 1 2 2l-2 1v1c-1 0-2-1-3-1 1-1 2-2 3-4l-3-3z" class="F"></path><path d="M807 142c10 1 21 1 32 0 4 0 10 1 14 0 3 0 7-3 10-4 1 0 4 2 6 2 4 1 10 1 15 1 13 0 24 0 37 4 5 2 10 4 15 7 3 3 6 5 9 9-2-1-5-2-8-3-7-2-15-2-23-2s-17 0-24 4c-4 1-7 2-9 5-5 3-8 7-12 11-6 7-14 14-18 23-1 3-2 6-1 10v2c-3 0-7 1-10 0l-2-4c-2 1-3 0-4 1-1 2-1 4-2 5h-2c-2 1-3 1-5 3v7c-1 0-2 1-3 2l-1-2c0 2-1 2-2 3v2c0 3 0 4-2 6-1 2-3 6-4 9v1l-2 2-1-1c-2 1-3 1-4 2-1 2-1 3-2 4l-12 13-4 4c0-1-1-1-2-2h0c2-1 3-3 4-4-2 0-2 1-4 2l-3 2c-2 1-3 1-5 1h-2c-1-1-3-2-4-3l-4-6-2-4-1-2c-1-7-1-10 2-16h0c3-5 8-9 13-13h-2 0l1-1c0-1 0-2 1-2v-1h0c1-1 1-2 2-3h0 3 1l-1-1-1-3h0l-2-4c-3-5-2-10-3-15l1-2 2-1v-2c-1-1-2-1-3-1l3-1c2-2 5-2 8-3-1-2-1-3-1-4h3v-1-1c1 0 1-1 2-2h-1c-1 1-1 1-2 1s-2-2-3-3l-2-2-2-2c-1 1-2 1-3 2 0 1-1 1-1 1l-1-1c1-1 1-2 2-3 0 0 1 0 2-1l2-2c1 0 1 1 2 1v-2h0v-1h2c1-1 1-2 2-3l1 1 1-1c0-2-1-3-2-4-1 0-2 0-3-1h0l-4-1c0-1 0-2-1-3s-3-1-5-1v-4c2 1 4 1 5 2 2 1 6 0 8 0v-1c1-1 3-1 4-1l-1-1c-2 0-5 0-7-1v-1l-2-1 1-1c2 0 3 1 5 1h1c3 0 5 0 7 1 3 0 6 0 8-1z" class="AJ"></path><path d="M852 151l9-2 1 2c-7 1-14 3-21 3 2-1 4-1 6-1 1 0 1-1 2-1h2l1-1z" class="AR"></path><path d="M858 168c4-3 9-5 14-7-3 2-6 4-8 6s-3 3-4 5l-2-1h-1c1-1 1-1 1-2v-1z" class="n"></path><path d="M835 162c4-1 10-2 15-2l-5 3-5 3-1-1 1-1c-1-1-4-1-5-2z" class="S"></path><path d="M874 143c4 0 10 0 14 1 1 1 1 2 2 3-10 0-19 1-28 4l-1-2 13-3-7-2 7-1z" class="AB"></path><path d="M836 200h0 0c-1 2-4 5-6 6s-5 3-6 5v1c-1 1-1 2-2 3v1h1c1-1 2-1 2-2 1-1 2-2 2-3l3-1h1l2-2h1l1-1c0-1 0-1 1-1 1 1 1 1 2 1-2 1-3 0-4 1-1 2-1 4-2 5h-2c-2 1-3 1-5 3v7c-1 0-2 1-3 2l-1-2v-5-3c2-7 9-11 15-15z" class="Q"></path><path d="M823 185c4-4 8-8 12-10v1c-1 1-2 1-3 2v1s1-1 2-1h0c-1 1-2 1-2 2-3 1-4 4-7 6-1 0-2 1-2 2v1c1-1 1-2 2-2l3-3c2-2 5-3 7-4l1 1v1c-1 1-1 0-1 1-2 3-5 5-7 8-1 2-3 3-4 5l-1-2c1-2 2-4 4-6h-1c-1 0-2 1-3 1-1 3-4 4-7 5 2-3 6-5 7-9h0z" class="n"></path><path d="M823 189c3-2 5-4 8-6-1 2-3 5-4 7 0 1 0 0 1 1-1 2-3 3-4 5l-1-2c1-2 2-4 4-6h-1c-1 0-2 1-3 1z" class="R"></path><path d="M820 186l3-1h0c-1 4-5 6-7 9 3-1 6-2 7-5 1 0 2-1 3-1h1c-2 2-3 4-4 6h-1l-1 1h0-1c-1 1-2 2-3 2-2 2-4 4-5 6h-4c1-2 3-3 2-5-1 0 0 0-1 1l-3 3h-1c1-2 2-3 3-4l2-2h0c0-1 0-2-1-3v-3l2-2 2 2 2-2 2 1 3-3z" class="I"></path><path d="M813 190l2-2 2 1-4 4-1-1 1-2z" class="k"></path><path d="M811 188l2 2-1 2 1 1-3 3c0-1 0-2-1-3v-3l2-2z" class="a"></path><path d="M850 160c10-5 23-9 35-9 2-1 5-1 8 0-2 1-11 2-13 2-3 1-7 2-10 3-7 2-14 5-20 9-4 2-7 4-10 6l2-2c0-2 3-3 4-4 0-1 0-1-1-2l5-3z" class="T"></path><path d="M884 141c13 0 24 0 37 4 5 2 10 4 15 7-3 0-5-1-8-2l-11-2c-7-1-13-2-19-2l-8 1c-1-1-1-2-2-3-4-1-10-1-14-1h0 19c-1-1-7 0-8-1-1 0-1-1-1-1z" class="k"></path><path d="M900 143h5c2 1 5 0 7 1h1c2 1 3 1 5 2v1c0 1-1 1-1 1-7-1-13-2-19-2h9c-2-1-4-3-7-3z" class="f"></path><path d="M893 143h7c3 0 5 2 7 3h-9l-8 1c-1-1-1-2-2-3-4-1-10-1-14-1h0 19z" class="z"></path><path d="M824 164h1c3 0 6-1 10-2 1 1 4 1 5 2l-1 1 1 1 5-3c1 1 1 1 1 2-1 1-4 2-4 4l-2 2c-2 1-3 2-5 4-4 2-8 6-12 10l-3 1-3 3-2-1-2 2-2-2-2 2-1-1c-2 0-1-1-2-3-1 1-2 1-3 1v-1c1 0 1 0 2-1 1 0 1-1 1-3 1-1 1-1 1-3l2-1c1 0 2-1 4-2l-1-3v-1l-1-1-1-4 4 1h0c1-2 3-2 5-2h2c1 0 2-1 3-2z" class="E"></path><path d="M816 181h1c1 0 2 0 4-1h0c1 0 2-1 2-1l5-3-5 7-3 3-3 3-2-1-2 2-2-2-2 2-1-1c-2 0-1-1-2-3 1-1 4-2 6-3h1l3-2z" class="h"></path><path d="M808 189v-1h1c1-1 3-2 5-2-1 1-2 2-3 2l-2 2-1-1z" class="G"></path><path d="M839 165l1 1-21 13-3 2-3 2h-1c-2 1-5 2-6 3s-2 1-3 1v-1c1 0 1 0 2-1 1 0 1-1 1-3 1-1 1-1 1-3l2-1c1 0 2-1 4-2l13-5c3-1 6-2 10-4l3-2z" class="n"></path><path d="M807 179l2-1c-1 1-1 1-1 3l2 1c1 0 1 0 2-1 2-1 5-2 7-2l-3 2-3 2h-1c-2 1-5 2-6 3s-2 1-3 1v-1c1 0 1 0 2-1 1 0 1-1 1-3 1-1 1-1 1-3z" class="W"></path><path d="M824 164h1c3 0 6-1 10-2 1 1 4 1 5 2l-1 1-3 2c-4 2-7 3-10 4l-13 5-1-3v-1l-1-1-1-4 4 1h0c1-2 3-2 5-2h2c1 0 2-1 3-2z" class="E"></path><path d="M814 168l6 1c-2 0-4 1-5 1s-1-1-2-1l1-1z" class="H"></path><g class="V"><path d="M810 167l4 1-1 1c0 1 0 2-1 3l-1-1-1-4z"></path><path d="M819 166c2 1 4 1 6 1l2-1h0c3 1 5 1 7 0 1-1 1-1 2-1v1 1c-4 2-7 3-10 4 1-1 1-2 2-2 2-1 3-1 5-2-2 0-4 1-6 0h0c-1 0-3 1-5 2-1 0-1-1-2 0l-6-1h0c1-2 3-2 5-2z"></path></g><path d="M824 164h1c3 0 6-1 10-2 1 1 4 1 5 2l-1 1-3 2v-1-1c-1 0-1 0-2 1-2 1-4 1-7 0h0l-2 1c-2 0-4 0-6-1h2c1 0 2-1 3-2z" class="T"></path><path d="M797 153c3 0 6 1 9 1h22c-3 1-6 2-8 2h-5c-1 1-2 3-3 3 1 1 1 1 2 1s1 0 1 1h4v1h-2 0c-1 1-2 2-3 2l-1 1 11-1c-1 1-2 2-3 2h-2c-2 0-4 0-5 2h0l-4-1 1 4 1 1v1l1 3c-2 1-3 2-4 2l-2 1h-1v-1l1-1h-1c-1 1-3 1-3 1-1 0-1 0-2 1l-2 1h0l-4 1c-1-1-1-2-2-2h-1v-1-1c1 0 1-1 2-2h-1c-1 1-1 1-2 1s-2-2-3-3l-2-2-2-2c-1 1-2 1-3 2 0 1-1 1-1 1l-1-1c1-1 1-2 2-3 0 0 1 0 2-1l2-2c1 0 1 1 2 1v-2h0v-1h2c1-1 1-2 2-3l1 1 1-1c0-2-1-3-2-4-1 0-2 0-3-1h0l1-1c1 0 2 1 2 1l1-1c2-1 2 0 4-1h1z" class="n"></path><path d="M788 155l1-1c1 0 2 1 2 1 2 2 6 4 7 7 0 1 1 2 2 3-2 1-2 0-4 1h0l-1-2-1-1-2 1v-1-2l1-1c0-2-1-3-2-4-1 0-2 0-3-1h0z" class="o"></path><path d="M798 162c0 1 1 2 2 3-2 1-2 0-4 1h0l-1-2 1-1 1-1h1zm11 4l-6-7 1-1c1-1 1-1 2-1 1 2 2 3 3 4 1 0 1 0 1 1h1 0l1-1h1c1 0 2 1 2 1 0 1-1 1-1 2h0l-1 1 11-1c-1 1-2 2-3 2h-2c-2 0-4 0-5 2h0l-4-1c0-1-1-1-1-1z" class="W"></path><path d="M798 162c1-1 2-1 3-2l1 1 1 3 4 3 2-1s1 0 1 1l1 4 1 1v1l1 3c-2 1-3 2-4 2l-2 1h-1v-1l1-1h-1c-1-2 0-4-2-6h-1c0-1 0-3-1-4l-1 2c-1-1-2-1-2-2h0-1l-2 1v-2h0c2-1 2 0 4-1-1-1-2-2-2-3z" class="T"></path><path d="M809 166s1 0 1 1l1 4c-1 0-1 0-2-1s-1-1-2-3l2-1zm-11-4c1-1 2-1 3-2l1 1h-1l1 2c0 1 0 1-1 1-1 1-1 0-1 1-1-1-2-2-2-3z" class="c"></path><path d="M804 171l1-1h2v2l2-1 1 2c0 1-1 1-2 2v1l1 1c0-1 1-2 1-2l2-2 1 3c-2 1-3 2-4 2l-2 1h-1v-1l1-1h-1c-1-2 0-4-2-6z" class="H"></path><path d="M787 163h2c1-1 1-2 2-3l1 1v2 1l2-1 1 1 1 2v2l2-1h1 0c0 1 1 1 2 2l1-2c1 1 1 3 1 4h1c2 2 1 4 2 6-1 1-3 1-3 1-1 0-1 0-2 1l-2 1h0l-4 1c-1-1-1-2-2-2h-1v-1-1c1 0 1-1 2-2h-1c-1 1-1 1-2 1s-2-2-3-3l-2-2-2-2c-1 1-2 1-3 2 0 1-1 1-1 1l-1-1c1-1 1-2 2-3 0 0 1 0 2-1l2-2c1 0 1 1 2 1v-2h0v-1z" class="E"></path><path d="M787 163h2c1-1 1-2 2-3l1 1v2 1 1c-1 0-2 1-3 1 0 0-1-2-2-2v-1z" class="T"></path><path d="M796 175h1c1 0 1-1 2-1h3c1 1 1 1 1 3l-2 1c0-1 0-1-1-2l-2 1-2-2z" class="k"></path><path d="M792 178l2-2v-3l2-1v1 2l2 2 2-1c1 1 1 1 1 2v1l-2 1h0l-4 1c-1-1-1-2-2-2h-1v-1z" class="D"></path><path d="M806 177h1l-1 1v1h1c0 2 0 2-1 3 0 2 0 3-1 3-1 1-1 1-2 1v1c1 0 2 0 3-1 1 2 0 3 2 3l1 1v3c1 1 1 2 1 3h0l-2 2c-1 1-2 2-3 4-4 4-7 7-12 10l1-1v-4a30.44 30.44 0 0 1-8 8v1l-1-1-1-3h0l-2-4c-3-5-2-10-3-15l1-2 2-1v-2c-1-1-2-1-3-1l3-1c2-2 5-2 8-3-1-2-1-3-1-4h3 1c1 0 1 1 2 2l4-1h0l2-1c1-1 1-1 2-1 0 0 2 0 3-1z" class="M"></path><path d="M785 210l1-1h0 1c0 1 1 1 2 2 0 0-1 2-2 2 0-1-1-2-2-3z" class="AG"></path><path d="M794 198l2-2h1c1 1 0 2 0 3l1 1h-1l-1 1-2-3z" class="u"></path><path d="M785 210c1 1 2 2 2 3l-1 2v1l-1-1-1-3 1-2z" class="f"></path><path d="M788 195c0 1 0 1 1 2v1l-2 1-2-1h-1c0-1 0-2 1-2 0-1 1-2 2-2v1h1z" class="p"></path><path d="M788 189h0l2 2-1 1c0 1 1 2 2 3l-1 1-1-1h-1-1v-1h0l-1-1c-1 1-2 2-4 2v-2h-3l1-2 2-1 6-1z" class="B"></path><path d="M789 211c0-2 1-1 1-2s0-3 1-3v-1l2 2h1v-1c0-1 0-1 1-2v-3l-1 1h-1c0-1 0-2-1-3l-1-2v-1c2 0 2 1 3 2l2 3 2 2-4 4a30.44 30.44 0 0 1-8 8l1-2c1 0 2-2 2-2z" class="G"></path><path d="M803 187c1 0 2 0 3-1 1 2 0 3 2 3l1 1v3c1 1 1 2 1 3h0l-2 2-3-3-1 1-1-1h-2v-2h-2c-1 0-1 0-2-1-2 0-2 0-3-1h-4l-2-2 2 1c1 0 3-1 4-1v-1c2 0 3-1 4-1h3 2z" class="m"></path><path d="M803 187c1 0 2 0 3-1 1 2 0 3 2 3l1 1v3c1 1 1 2 1 3h0c-2 0-3-2-5-3-1 0-2 1-3 0h0l2-2c-2-2-3-3-6-4h3 2z" class="H"></path><path d="M806 177h1l-1 1v1h1c0 2 0 2-1 3 0 2 0 3-1 3-1 1-1 1-2 1v1h-2-3c-1 0-2 1-4 1v1c-1 0-3 1-4 1l-2-1h0l-6 1v-2c-1-1-2-1-3-1l3-1c2-2 5-2 8-3-1-2-1-3-1-4h3 1c1 0 1 1 2 2l4-1h0l2-1c1-1 1-1 2-1 0 0 2 0 3-1z" class="I"></path><path d="M792 179h1c1 0 1 1 2 2l-5 2c-1-2-1-3-1-4h3z" class="u"></path><path d="M797 182v1c1 0 1 0 2 1h0 1v2h-4-2c-1-1-1-1 0-2l3-2z" class="S"></path><path d="M785 186l4-1c1-1 1-1 2-1l1 1-2 2v1h-1-2l1 1-6 1v-2c-1-1-2-1-3-1l3-1h3z" class="W"></path><path d="M779 187l3-1h3l-1 2c1 1 2 0 3 0l1 1-6 1v-2c-1-1-2-1-3-1zm27-10h1l-1 1v1h1c0 2 0 2-1 3 0 2 0 3-1 3-1 1-1 1-2 1v1h-2l-1-1v-2h-1 0c-1-1-1-1-2-1v-1l2-2h0l2-1c1-1 1-1 2-1 0 0 2 0 3-1z" class="T"></path><path d="M800 184l1-1c0-1 0-2 1-4h1l1 1v2c1 1 1 0 2 0 0 2 0 3-1 3-1 1-1 1-2 1v1h-2l-1-1v-2z" class="c"></path><defs><linearGradient id="f" x1="839.374" y1="170.114" x2="839.615" y2="205.376" xlink:href="#B"><stop offset="0" stop-color="#200000"></stop><stop offset="1" stop-color="#6a0c0c"></stop></linearGradient></defs><path fill="url(#f)" d="M841 179l17-11v1c0 1 0 1-1 2h1l2 1-5 4 1 2c1 1 2 1 2 2-1 1-4 3-5 5s-4 4-6 7c-1 2-3 3-5 4l-6 4h0 0c-6 4-13 8-15 15v3 5c0 2-1 2-2 3v2l-1 1c-1-1-1-1-1-2l1-1v-1l-1-1-1 1c-1 1-1 2-1 4l1 1h0-1-1-2v-3c0-2-1-5-2-7v-2-1l-2-2c0-1 1-3 1-4 1-3 2-5 3-8 1-2 3-4 5-6 1 0 2-1 3-2h1 0l1-1h1l1 2c1-2 3-3 4-5 2-3 5-5 7-8 0-1 0 0 1-1v-1l-1-1h0c2-1 4-1 6-1z"></path><path d="M835 180h0c2-1 4-1 6-1l-6 4c0-1 0 0 1-1v-1l-1-1z" class="C"></path><path d="M819 202h0c2 0 3-1 4-2l1 1c0 2-3 4-4 5h-4v-1l3-3z" class="B"></path><path d="M836 200c7-5 14-11 18-18l-1-1c-1 1-1 1-2 1l1-1-1-2c1-1 3-2 4-3l1 2c1 1 2 1 2 2-1 1-4 3-5 5s-4 4-6 7c-1 2-3 3-5 4l-6 4h0z" class="n"></path><path d="M817 213c1-2 3-4 5-5 0-1 0 0 1-1 3-3 7-5 12-8l1 1c-6 4-13 8-15 15l-1-1h-2l-1-1z" class="G"></path><path d="M823 194l1 2c-2 2-4 4-5 6l-3 3v1h4c-1 2-3 3-4 4v3h1l1 1h2l1 1v3 5c0 2-1 2-2 3v2l-1 1c-1-1-1-1-1-2l1-1v-1l-1-1-1 1c-1 1-1 2-1 4l1 1h0-1-1-2v-3c0-2-1-5-2-7v-2-1l-2-2c0-1 1-3 1-4l3-8c1-2 3-4 5-6 1 0 2-1 3-2h1 0l1-1h1z" class="a"></path><path d="M810 220c1-2 1-2 2-3 0 5 2 8 2 13h-2v-3c0-2-1-5-2-7z" class="B"></path><path d="M816 206h4c-1 2-3 3-4 4v3c1 2 1 4 1 7h-1 0c0-2-1-3-2-4l-1-2c0-1 1-1 2-2h0l-1-1-2 2v-2c1-1 2-2 4-2v-2-1z" class="D"></path><path d="M817 213l1 1h2l1 1v3 5c0 2-1 2-2 3v2l-1 1c-1-1-1-1-1-2l1-1v-1l-1-1c1-1 1-2 1-4h-1c0-3 0-5-1-7h1z" class="e"></path><path d="M821 218v5c0 2-1 2-2 3v2l-1 1c-1-1-1-1-1-2l1-1v-1c2-2 2-4 3-7z" class="k"></path><path d="M823 194l1 2c-2 2-4 4-5 6l-3 3v1 1 2c-2 0-3 1-4 2v2 1 3h0c-1 1-1 1-2 3v-2-1l-2-2c0-1 1-3 1-4l3-8c1-2 3-4 5-6 1 0 2-1 3-2h1 0l1-1h1z" class="B"></path><path d="M809 211c1 1 1 2 2 3h1v3h0c-1 1-1 1-2 3v-2-1l-2-2c0-1 1-3 1-4z" class="h"></path><path d="M812 211l-1-1c1-1 2-1 1-3l3-3v1h1v1 1 2c-2 0-3 1-4 2z" class="K"></path><path d="M823 194l1 2c-2 2-4 4-5 6l-3 3h-1v-1c2-3 5-6 6-9h0l1-1h1z" class="T"></path><path d="M807 142c10 1 21 1 32 0 4 0 10 1 14 0 3 0 7-3 10-4 1 0 4 2 6 2 4 1 10 1 15 1 0 0 0 1 1 1 1 1 7 0 8 1h-19 0l-7 1 7 2-13 3-9 2-1 1h-2c-1 0-1 1-2 1-2 0-4 0-6 1h-13-22c-3 0-6-1-9-1h-1c-2 1-2 0-4 1l-1 1s-1-1-2-1l-1 1-4-1c0-1 0-2-1-3s-3-1-5-1v-4c2 1 4 1 5 2 2 1 6 0 8 0v-1c1-1 3-1 4-1l-1-1c-2 0-5 0-7-1v-1l-2-1 1-1c2 0 3 1 5 1h1c3 0 5 0 7 1 3 0 6 0 8-1z" class="D"></path><path d="M794 145l-1-1v-1c2 1 3 1 5 1l5 1h0l-2 1h-6l-1-1z" class="AA"></path><path d="M786 141c2 0 3 1 5 1h1c2 0 4 1 6 2-2 0-3 0-5-1v1l1 1c-2 0-5 0-7-1v-1l-2-1 1-1zm15 5h1 2l1 1c1 0 1 0 2 1h5 0-7-1l1 2c-2 0-4 0-6-1l-8-1v-1c1-1 3-1 4-1h6z" class="f"></path><path d="M850 149l10-2-1-1h-1l-1-1c2 0 3-1 4-2 0-1 0-1 1-1s2 0 3 1 1 1 2 1l7 2-13 3-9 2c-2-1-2-1-2-2z" class="AQ"></path><defs><linearGradient id="g" x1="814.081" y1="154.215" x2="815.427" y2="144.734" xlink:href="#B"><stop offset="0" stop-color="#f8626b"></stop><stop offset="1" stop-color="#d06d7a"></stop></linearGradient></defs><path fill="url(#g)" d="M778 146c2 1 4 1 5 2 2 1 6 0 8 0l8 1c2 1 4 1 6 1 15 0 30 1 45-1 0 1 0 1 2 2l-1 1h-2c-1 0-1 1-2 1-2 0-4 0-6 1h-13-22c-3 0-6-1-9-1h-1c-2 1-2 0-4 1l-1 1s-1-1-2-1l-1 1-4-1c0-1 0-2-1-3s-3-1-5-1v-4z"></path><path d="M790 151c4 0 7 0 11 1 1 1 3 1 4 1l1 1c-3 0-6-1-9-1h0l-7-2z" class="AF"></path><path d="M783 151h7l7 2h0-1c-2 1-2 0-4 1l-1 1s-1-1-2-1l-1 1-4-1c0-1 0-2-1-3z" class="c"></path><defs><linearGradient id="h" x1="781.871" y1="219.946" x2="790.804" y2="227.218" xlink:href="#B"><stop offset="0" stop-color="#280302"></stop><stop offset="1" stop-color="#4f0808"></stop></linearGradient></defs><path fill="url(#h)" d="M805 202h1l3-3c1-1 0-1 1-1 1 2-1 3-2 5h4l-3 8c0 1-1 3-1 4l2 2v1 2c1 2 2 5 2 7v3h2 1 1 0l-1-1c0-2 0-3 1-4l1-1 1 1v1l-1 1c0 1 0 1 1 2l1-1c0 3 0 4-2 6l-4 9v1l-2 2-1-1c-2 1-3 1-4 2-1 2-1 3-2 4l-12 13-4 4c0-1-1-1-2-2h0c2-1 3-3 4-4-2 0-2 1-4 2l-3 2c-2 1-3 1-5 1h-2c-1-1-3-2-4-3l-4-6-2-4-1-2c-1-7-1-10 2-16h0c3-5 8-9 13-13h-2 0l1-1c0-1 0-2 1-2v-1h0c1-1 1-2 2-3h0 3 1v-1a30.44 30.44 0 0 0 8-8v4l-1 1c5-3 8-6 12-10z"></path><path d="M771 238c1 1 1 1 1 3 0 0 1 1 1 2h0c-1 1-2 1-2 2 0 2-1 4-2 6 0-1-1-3-1-5s2-6 3-8z" class="H"></path><path d="M771 238v-1c0-1 1-2 1-3 2-1 4-1 5-1-1 3-2 4-2 7 0 1 1 1 2 2v1c0 1-1 1-2 2l-2-2h0c0-1-1-2-1-2 0-2 0-2-1-3z" class="L"></path><path d="M794 207v4l-1 1-13 11h-2 0l1-1c0-1 0-2 1-2v-1h0c1-1 1-2 2-3h0 3 1v-1a30.44 30.44 0 0 0 8-8z" class="s"></path><path d="M780 219c1-1 1-2 2-3h0 3c-1 2-2 3-3 4l-2-1h0z" class="AG"></path><path d="M789 224h0c1-2 2-2 2-4l2-2 1 3h0c2 2 4 3 6 4 1 1 2 1 3 1l-2 2-6-2v-1c-2 1-2 2-3 3l-2-2h0-1l-1 1-1-1-2-2 2-1 2 1z" class="T"></path><path d="M785 224l2-1 2 1h0 2l-1 2h0-1l-1 1-1-1-2-2z" class="K"></path><path d="M785 224l2 2 1 1 1-1h1 0l2 2c-1 1-2 2-3 4l-5 4c-1 1-2 3-3 4l-1-1c-2 1-3 2-3 3-1-1-2-1-2-2 0-3 1-4 2-7h1c0-1 1-2 2-3 1-2 3-5 5-6z" class="i"></path><path d="M785 224l2 2c-1 2-1 4-3 5h-3l-1-1c1-2 3-5 5-6z" class="h"></path><path d="M790 226h0l2 2c-1 1-2 2-3 4l-5 4c0-2 0-2 1-4 1-1 2-3 4-4 1 0 1-1 1-2z" class="O"></path><path d="M777 233h1v1l1-1 1 1c2 0 3 0 5-2-1 2-1 2-1 4-1 1-2 3-3 4l-1-1c-2 1-3 2-3 3-1-1-2-1-2-2 0-3 1-4 2-7z" class="E"></path><path d="M785 232c-1 2-1 2-1 4-1 1-2 3-3 4l-1-1v-3l-1-1 1-1c2 0 3 0 5-2z" class="V"></path><path d="M812 203l-3 8c0 1-1 3-1 4l2 2v1l-1 1c0 3 0 3-2 5-1 0-1 1-2 1h-1l-1 1c-1 0-2 0-3-1-2-1-4-2-6-4h0l-1-3 2-2c2-2 5-4 7-6 3-2 4-3 5-5l1-2h4z" class="D"></path><path d="M799 217l3-3c1 1 1 2 1 3l-1 2v1h-1c-1 0-1-1-2-1v-1-1z" class="G"></path><path d="M794 221v-1c2-1 3-3 5-3v1 1c1 0 1 1 2 1h1l1 2-2 2-1 1c-2-1-4-2-6-4h0z" class="h"></path><path d="M794 221c3 0 5 1 7 3l-1 1c-2-1-4-2-6-4z" class="E"></path><path d="M812 203l-3 8c0 1-1 3-1 4l2 2v1l-1 1c0 3 0 3-2 5-1 0-1 1-2 1h-1l-1 1c-1 0-2 0-3-1l1-1 2-2 3-6v-2l1-1 1-4c0-2 0-2-1-4l1-2h4z" class="H"></path><path d="M808 215l2 2v1l-1 1c0 3 0 3-2 5-1 0-1 1-2 1l3-10z" class="a"></path><path d="M789 232l1 1c-1 1-1 2-2 2l2 2v1c0 2 1 4 1 5 1 1 1 2 2 3-1 1-1 1-1 2-1 3-2 4-3 6-2 2-3 6-4 8 0 1 0 2 1 2l-3 2c-2 1-3 1-5 1h-2c-1-1-3-2-4-3l-4-6c1-1 1-1 1-2h1l1 1v-1l-2-5c1-2 2-4 2-6 0-1 1-1 2-2l2 2c1-1 2-1 2-2v-1c0-1 1-2 3-3l1 1c1-1 2-3 3-4l5-4z" class="B"></path><path d="M788 239c1 0 1 0 2-1 0 2 1 4 1 5 0 2 0 2-1 4l-1-2c0-2 0-4-1-6z" class="p"></path><path d="M791 243c1 1 1 2 2 3-1 1-1 1-1 2-1 3-2 4-3 6v-3c1 0 1 0 1-1v-1-2c1-2 1-2 1-4z" class="G"></path><path d="M789 232l1 1c-1 1-1 2-2 2l2 2v1c-1 1-1 1-2 1l-3 4v1 1 1c-1 0-1 0-1 1l-1-1c0-2 1-3 0-5 0 1-1 1-1 2s-1 1-1 2v-1-1h-1v2l1 2h-1l-2 2v-1c-1 0-1 0-3-1v-2c1-1 2-1 2-2v-1c0-1 1-2 3-3l1 1c1-1 2-3 3-4l5-4z" class="a"></path><path d="M780 239l1 1c-2 2-2 5-3 8-1 0-1 0-3-1v-2c1-1 2-1 2-2v-1c0-1 1-2 3-3z" class="O"></path><path d="M769 251c1-2 2-4 2-6 0-1 1-1 2-2l2 2v2c2 1 2 1 3 1v1l-1 6v1 1c-1 2 1 4 0 6l1 2v1l-2 1c-1-1-3-2-4-3l-4-6c1-1 1-1 1-2h1l1 1v-1l-2-5z" class="i"></path><path d="M775 247c2 1 2 1 3 1v1l-1 6s-1-1-1-2h0c-1-2-1-4-1-6z" class="J"></path><path d="M810 218v2c1 2 2 5 2 7v3h2 1 1 0l-1-1c0-2 0-3 1-4l1-1 1 1v1l-1 1c0 1 0 1 1 2l1-1c0 3 0 4-2 6l-4 9v1l-2 2-1-1c-2 1-3 1-4 2-1 2-1 3-2 4l-12 13-4 4c0-1-1-1-2-2h0c2-1 3-3 4-4-2 0-2 1-4 2-1 0-1-1-1-2 1-2 2-6 4-8 1-2 2-3 3-6 0-1 0-1 1-2-1-1-1-2-2-3 0-1-1-3-1-5v-1l-2-2c1 0 1-1 2-2l-1-1c1-2 2-3 3-4s1-2 3-3v1l6 2 2-2 1-1h1c1 0 1-1 2-1 2-2 2-2 2-5l1-1z" class="D"></path><path d="M807 243v-1-2c1 1 2 1 2 2l1 3h0c-2 1-3 1-4 2 0-1 0-3 1-4zm-1-13l4 3 1 1-1 2-1 1-2-1v-2h-1v-4z" class="i"></path><path d="M803 226l1-1c0 3 0 3 2 5v4h0c0 1 1 2 1 4-1 1-2 1-3 2l-1-1c1-3 1-4 0-7h0c-1-2-1-3-2-4h0l2-2z" class="J"></path><path d="M790 262c4-4 9-7 12-11 1-2 3-7 5-8-1 1-1 3-1 4-1 2-1 3-2 4l-12 13-4 4c0-1-1-1-2-2h0c2-1 3-3 4-4z" class="f"></path><path d="M810 218v2c1 2 2 5 2 7v3h0c0 1 1 2 1 3s0 1-1 1h-1l-1-1-4-3c-2-2-2-2-2-5h1c1 0 1-1 2-1 2-2 2-2 2-5l1-1z" class="m"></path><path d="M810 218v2c1 2 2 5 2 7l-1-1h0-2c1 1 1 2 1 3l-1 1c-1 0-1-1-1-1-2-2-1-3-1-5 2-2 2-2 2-5l1-1z" class="G"></path><path d="M792 228c1-1 1-2 3-3v1l6 2h0c1 1 1 2 2 4h-1v1c1 1 1 1 1 2v1 1l-1 1v-2c-1 2 0 2 0 4v2l1 1-1 1c-1 1-1 2-2 3 0 1 0 1-1 2h-1v1c-1-1-1-1-1-3h0-1v1l-1 1h0v-2l-1-1-2 2h0c0-1 0-1 1-2-1-1-1-2-2-3 0-1-1-3-1-5v-1l-2-2c1 0 1-1 2-2l-1-1c1-2 2-3 3-4z" class="k"></path><path d="M797 235c1 1 1 3 1 4-1 1-1 2-2 3v-1c-1-2 0-4 1-6z" class="m"></path><path d="M794 232h1 1v1h1 1c-1 1-1 1-1 2-1 2-2 4-1 6v1c0 1 0 1-1 2h0c0-1 0-1-1-2 0-1-1-1-1-3h0l-2-2h0c1-2 2-3 3-4v-1z" class="G"></path><path d="M794 232c0-1 0-1-1-3h0 2v-2l1 1c1 0 0 0 1 1 2 0 2 0 4-1 1 1 1 2 2 4h-1v1c1 1 1 1 1 2v1 1l-1 1v-2c-1 2 0 2 0 4v2l1 1-1 1c-1 1-1 2-2 3 0 1 0 1-1 2h-1l1-1c1-2 2-5 1-7v-1-3c0-2-1-2-2-4h-1-1v-1h-1-1z" class="m"></path><path d="M393 83l2 2c4 0 8-1 12-3l13 4 9 5v1h1c1 1 3 2 4 3 4 4 7 8 10 13l1 3c1 2 2 4 2 5l3 15c-2-1-4-3-5-5h-1c-1-2-1-3-4-5-5-6-12-10-20-11-5 0-10 1-14 5-5 4-8 10-8 17 0 3 0 6 2 9 1-1 3-2 4-4-1 5-1 9 1 13 1 2 3 4 5 5 3 1 6 0 8-1h1 0 1l1-1 1 1c-3 2-6 2-8 4l-3 3h-1c-1 1-2 1-2 2v-1l1-2h-3v1h1v1l-2 1h0v1c1 1 1 1 3 1l-1 2h-1c-1-1-1 0-1 0h-2v1l2 1h0c0 1 1 2 1 3v2l2 2c0 1 1 2 2 2l-1 1v1c1 1 1 2 1 4h0c-1 0-2-2-4-2l-3-3c0-1-2-2-2-4h0c-2-2-7-9-9-9v1h-1v5h-2l2 1v1h-1c1 2 2 3 3 5v1c2 1 4 3 5 5a30.44 30.44 0 0 1 8 8h0c0 1 1 2 1 3l1 3c0 1 1 2 1 3v3c2 2 2 4 3 6l2 3c1 2 2 4 4 5 1 2 4 3 4 4l-3-1 3 3c-2 1-3 1-5 0-1 0-3 1-4 1h-15-1-17-36c-7 0-14 1-21 0h7v-1c1 1 6 1 7 0h11 0c-1-1-1-2-1-3h0v-2c0-1 0-2-1-3v-3h0l1-1c-2-7-2-16-2-24v-2l-2-4v1c-1 0-2-1-3-1 0-1 0-3-1-4 0-2 0-4 1-5-3-13-5-26-7-39h-1-1l2-1c1-3 1-4 3-6v-2c3-3 7-7 11-10 2-2 5-4 7-6l12-8c3-2 7-4 9-7l15-6c0-1 0-2-1-4v-1-1h1 3z" class="AJ"></path><path d="M402 221l-1-3c1 0 2 0 2 1 2 1 4 4 6 5h0-1c-1 0-1-1-2-1h-2l-2-2z" class="X"></path><path d="M375 194l-1-1c0-1-1-2-2-3v-3h2l-1-1v-1c1 1 2 1 2 2 2 2 3 4 4 6l-1-1c-1 0-2 0-2-1-1 1-1 1-1 2v1z" class="N"></path><path d="M366 177h1c0 2 1 2 1 4 1 1 2 0 2 1 1 1 0 2 1 3h0 1v1l-1 1-1 1c-1-1-2-2-2-3-1-2-2-4-2-6-1-1-1-1 0-2z" class="U"></path><path d="M354 192h1c3 5 8 9 10 13l2 4h-1c-3-3-5-7-7-10-2-2-3-4-5-7z" class="C"></path><path d="M379 181c0-1 1-1 2-2v2l2-1v1c0 2 1 3 2 5 0 2 2 4 4 5l1 1c2 1 3 3 4 4h-1c-6-3-9-11-14-15z" class="P"></path><path d="M367 196c2 2 4 3 7 3l6 7c-2 0-2 0-4 1-3-3-7-7-9-11z" class="t"></path><path d="M419 97c7 1 14 3 18 9v2c1 3 6 7 5 10-3-5-6-11-11-14v-1c-5-3-10-4-15-5l-2-1h1 4z" class="e"></path><path d="M367 196c-4-3-9-9-10-13 1 0 2 3 3 3v-2c1 0 2 1 3 2 3 3 7 8 10 13h1c-3 0-5-1-7-3z" class="w"></path><path d="M354 208h3c3 3 6 5 8 8 0 1 2 1 2 2 2 1 3 3 5 4 1 0 1 0 1 1-1 0-3-1-4-2-2-2-3-3-5-3l-1-1-3 1-1 1-1-1-4-10z" class="C"></path><path d="M347 147c1-3 4-5 6-7h0c-3 7-6 13-7 22-1 1-2 2-4 3l1-7 4-11z" class="f"></path><path d="M402 94c1 0 2 0 3 1h0c1 0 2-1 2-1l1 1c1 1 5 0 8 1 1 0 2 0 3 1h-4-1l2 1c-3 0-6 0-8 2l-4 3c0-2-1-4-2-6-1 0-2 1-4 1v-1l4-3z" class="K"></path><defs><linearGradient id="i" x1="383.949" y1="206.957" x2="385.051" y2="219.043" xlink:href="#B"><stop offset="0" stop-color="#666464"></stop><stop offset="1" stop-color="#8e8984"></stop></linearGradient></defs><path fill="url(#i)" d="M380 206l20 19h0c-6-1-19-13-24-18 2-1 2-1 4-1z"></path><path d="M356 113h0l8-7c1-1 1-1 2-1 1-1 3-2 4-3 2 0 2 0 3 1l2-1v-2l2-1c-1 1-1 2-1 2 0 1-1 2-1 2-2 2-5 6-7 6h-1c-2 2-5 4-7 6l-3 2-1-1 1-2-1-1z" class="T"></path><path d="M342 165c2-1 3-2 4-3l-1 25-2-4v1c-1 0-2-1-3-1 0-1 0-3-1-4 0-2 0-4 1-5l1 2h1c0-4-1-8 0-11z" class="s"></path><path d="M339 179c0-2 0-4 1-5l1 2 2 7v1c-1 0-2-1-3-1 0-1 0-3-1-4z" class="AQ"></path><path d="M391 167l-2-1 1-2-1-1v-2l-1-1c-1-1-1-3-1-4l-1-1h1c1 0 4 5 4 6 1 2 2 4 3 4l1 1c0 1 1 2 2 4 2 2 3 3 6 5-1-2-1-3-2-4-2-2-5-6-6-8v-1l10 13c1 1 2 3 4 4v1c1 1 1 2 1 4h0c-1 0-2-2-4-2l-3-3c0-1-2-2-2-4h0c-2-2-7-9-9-9v1h-1z" class="Z"></path><path d="M389 171v1l2 1v1h-1c1 2 2 3 3 5v1h0l-2-1-2 2c-1 1-3 1-3 3v1l-1 1c-1-2-2-3-2-5v-1l-2 1v-2c-1 1-2 1-2 2-1-1-1-2-1-3 3-1 5-4 8-5h2l1-2z" class="Y"></path><path d="M386 176l2-1c1 1 3 1 3 3v1l-2 2v-1l-2-1 1-1-2-2z" class="b"></path><path d="M386 176l2 2-1 1 2 1v1c-1 1-3 1-3 3v1l-1 1c-1-2-2-3-2-5v-1l-2 1v-2l5-3z" class="q"></path><path d="M375 194v-1c0-1 0-1 1-2 0 1 1 1 2 1l1 1c1 0 1 1 2 2 2 2 6 3 6 7 0 1 1 3 1 3 0 2 0 4 2 6 1 0 2 2 3 3-3 0-3-1-4-2-2-1-3-3-4-4 0-3-6-8-7-11l-3-3z" class="b"></path><path d="M387 202c1 0 3 0 4 1h0c1 3 3 4 5 6 1 1 2 3 3 4 3 0 3 2 5 4 1 1 1 1 1 2l-1-1-1 1c0-1-1-1-2-1l1 3-1 1c0-1-1-1-2-2 0-1-1-2-2-2-2-1-3-3-4-4s-2-3-3-3c-2-2-2-4-2-6 0 0-1-2-1-3z" class="U"></path><path d="M393 83l2 2c4 0 8-1 12-3l13 4 9 5v1c-4-2-8-4-12-5-6-1-12 0-17 1l-10 1c0-1 0-2-1-4v-1-1h1 3z" class="AZ"></path><path d="M389 85v-1-1h1l3 3c2 0 4-1 6 1l1 1-10 1c0-1 0-2-1-4z" class="Ac"></path><path d="M377 164c1-2 1-2 3-3h1l-1 2c2 0 3 1 4 3 0 3 2 2 3 4 0-1-1-1-1-2l1-1v1c0 1 1 2 2 3l-1 2h-2c-3 1-5 4-8 5l-1-1c-1-3-2-5-2-8-1-1 0-1 0-2l1-1h1l1-1h-2l1-1z" class="N"></path><path d="M377 164h2v1 1h2v1h-1l-1 3h-1l-1-1c-1 0-1-1-2-2l1-1h1l1-1h-2l1-1z" class="l"></path><path d="M377 177c-1-3-2-5-2-8-1-1 0-1 0-2 1 1 1 2 2 2l1 1h1 5v1h-2c-1 0-3 1-4 2v1c0 1-1 2-1 3z" class="U"></path><path d="M397 200l1-1c2-1 2-1 3 0l2 3h1l1-1v-1-2-3l2 1 1 3c0 1 1 2 1 3v3c2 2 2 4 3 6l2 3c1 2 2 4 4 5 1 2 4 3 4 4l-3-1c-3-3-8-6-10-10l-12-12z" class="r"></path><path d="M409 212c0-3-4-4-4-6v-3l1-1h0 2v1l-1 1h-1v2c1 2 4 5 6 5h0l2 3c1 2 2 4 4 5 1 2 4 3 4 4l-3-1c-3-3-8-6-10-10z" class="q"></path><path d="M395 93c3-2 5-2 8-2v1h3 0 3l1-1s1 0 2 1l1-1h2l-1 1c-1 0-2 0-3 1v1h3c1-1 3-1 6-1 0 1 1 1 1 3 1 0 3 0 4 1 4 0 11 5 13 9l1 1c1 1 2 1 2 3v3c2 2 3 5 4 7v1l1 2h-1c-1-2-2-3-3-5h0c1-3-4-7-5-10v-2c-4-6-11-8-18-9-1-1-2-1-3-1-3-1-7 0-8-1l-1-1s-1 1-2 1h0c-1-1-2-1-3-1l-4 3h-3l-1-1h-2c0-1 0-1-1-2l4-1z" class="W"></path><path d="M395 93c2 0 3 0 5 1h2l-4 3h-3l-1-1h-2c0-1 0-1-1-2l4-1z" class="T"></path><path d="M347 213v2c0 1 1 2 2 3v-1c1-3 2-6 2-9 0-2 1-4 1-5h1c0 1 0 2 1 3v2l4 10 1 1 1 1c1 2 1 3 1 5 3 0 10 0 12 1h7-36c-7 0-14 1-21 0h7v-1c1 1 6 1 7 0h11 0c-1-1-1-2-1-3h0v-2c0-1 0-2-1-3v-3h0l1-1z" class="R"></path><path d="M358 218l2 4c1 2 1 2 0 3h-1c-1 0-1 0-1-1-2-1-2-5-3-7 0-3-3-8-2-12l1 1v2l4 10z" class="E"></path><path d="M391 179l2 1h0c2 1 4 3 5 5a30.44 30.44 0 0 1 8 8h0c0 1 1 2 1 3l-2-1v3 2 1l-1 1h-1l-2-3c-1-1-1-1-3 0l-1 1c-2-1-3-3-4-4h1c-1-1-2-3-4-4l-1-1c-2-1-4-3-4-5l1-1v-1c0-2 2-2 3-3l2-2z" class="Y"></path><path d="M393 180c2 1 4 3 5 5a30.44 30.44 0 0 1 8 8c-2 0-3 0-4-1v-2l-1-1v2h-1c-1-1-1-2-2-3 0-1-1-1-1-2h-2c-1-1-1 0-2 0-1-2-1-2-1-4 0-1 0-1 1-2h0z" class="P"></path><path d="M391 179l2 1c-1 1-1 1-1 2l-3 3v2c0 1 0 1 1 2h1c0 1 0 1-1 2l1 1 1-1c1 1 1 1 1 2l2 2-1 1c-1-1-2-3-4-4l-1-1c-2-1-4-3-4-5l1-1v-1c0-2 2-2 3-3l2-2z" class="Z"></path><path d="M395 162l-2-4c-1-1-2-3-2-5l3-1c1 1 1 1 1 3h1c0-2-1-3 0-4l1 1v-1-1l3-2c0-2 1-3 2-4h0c1 2 1 4 2 6v1l1 2c1 1 2 2 4 3h1 2 1c1 0 2 0 3-1h1c1 0 2-1 3-1l1-1 1 1c-3 2-6 2-8 4l-3 3h-1c-1 1-2 1-2 2v-1l1-2h-3v1h1v1l-2 1h0v1c1 1 1 1 3 1l-1 2h-1c-1-1-1 0-1 0h-2v1l2 1h0c0 1 1 2 1 3v2l2 2c0 1 1 2 2 2l-1 1c-2-1-3-3-4-4l-10-13z" class="t"></path><path d="M405 163v-2c-1 0-1 0-1 1h-1c0-1 0-1 1-2 0-1 1-1 1-3 0-1 0 0 1-1l1 2 1-1c1 0 2 1 3 1h1 1 1l-3 3h-1c-1 1-2 1-2 2v-1l1-2h-3v1h1v1l-2 1h0z" class="b"></path><path d="M369 129h1l1 1 1-1 1 1h2 0v1l1-1h2l2-1v2h0c1 1 1 2 1 4h2l1 2 1 1c0 1 0 1 1 1-1 1 0 1-2 1-5 1-12 5-15 10-2 3-2 6-3 9v1c-1-1-1-2-1-3h-1c-1-1-1-1-2-1 0-2 1-3 1-5v-6c0-3 1-8-1-11l2-1 1-1v5c2-2 3-5 4-8z" class="K"></path><path d="M376 130h2l2-1v2c-1 1-2 3-3 4-1-1-1-1 0-3-1-1-1-1-2-1l1-1z" class="J"></path><path d="M375 139h1c1-1 3-2 4-3l1 1v2c-2 1-4 1-6 3l-1 1c-2 1-1 0-2 1l-2 2-2 1c0-2 5-6 7-8h0z" class="S"></path><path d="M380 131h0c1 1 1 2 1 4h2l1 2 1 1h-3l-1 1v-2l-1-1c-1 1-3 2-4 3h-1c1-1 1-2 2-4 1-1 2-3 3-4z" class="O"></path><path d="M369 129h1l1 1 1-1 1 1h2 0c-1 3-2 7-3 10-1 1-2 3-4 5-1 2-3 4-5 6h0v-6c0-3 1-8-1-11l2-1 1-1v5c2-2 3-5 4-8z" class="R"></path><path d="M365 132v5l-1 11-1-3c0-3 1-8-1-11l2-1 1-1z" class="B"></path><defs><linearGradient id="j" x1="335.355" y1="134.199" x2="356.461" y2="149.534" xlink:href="#B"><stop offset="0" stop-color="#3a0705"></stop><stop offset="1" stop-color="#771214"></stop></linearGradient></defs><path fill="url(#j)" d="M347 116c-1 2-5 7-7 9l-2 1c-2 2-2 4-2 7 0-1 0 0 1-1v-1c1-1 1-1 1-2l2-2c2-1 5-4 6-6 4-2 7-5 10-8l1 1-1 2 1 1 3-2h1l-3 3c0 2-1 4-1 5l3-3v1c0 1-2 4-1 6h1l2-4h1c-1 2-2 4-2 6l4 1v2l-1 1-2 1-2-1c-2 0-3 1-4 2s-2 4-3 5h0c-2 2-5 4-6 7l-4 11-1 7c-1 3 0 7 0 11h-1l-1-2c-3-13-5-26-7-39h-1-1l2-1c1-3 1-4 3-6v-2c3-3 7-7 11-10z"></path><path d="M358 118c0 2-1 4-1 5v2h-1c-2 1-3 2-4 3s-3 2-4 3c-1 2-1 4-2 5h0c-1 0 0-1 0-2 1-1 1-2 1-4h1c1-2 3-3 4-5 1-1 1-1 2-1 1-1 1-1 1-2 1-2 2-3 3-4z" class="E"></path><path d="M347 130l-1 1c-2 1-4 5-4 7l-1 1h-1c-1 3-1 6-1 9h-1c0-3 1-7 2-10 0-2 1-4 2-6 0-1 3-4 4-4l2 2h-1z" class="V"></path><path d="M357 123l3-3v1c0 1-2 4-1 6h1l2-4h1c-1 2-2 4-2 6l4 1v2l-1 1-2 1-2-1c-2 0-3 1-4 2s-2 4-3 5h0c-2 2-5 4-6 7l-4 11c0-2 1-5 0-7 0-2-1-2 0-4-1-2-1-3-1-5l1-1c0-2 2-3 3-5h0c1-1 1-3 2-5 1-1 3-2 4-3s2-2 4-3h1v-2z" class="e"></path><path d="M360 127l2-4h1c-1 2-2 4-2 6l4 1v2l-1 1-2 1-2-1c-2 0-3 1-4 2s-2 4-3 5h0c-2 2-5 4-6 7 0-3 0-4 1-7 2-4 8-8 11-12l1-1z" class="a"></path><path d="M361 129l4 1v2l-1 1-2 1-2-1-1-1c1-2 1-2 2-3z" class="AG"></path><path d="M377 99h1 3c2-2 7-4 10-5 1 1 1 1 1 2h2l1 1h3v1c-2 2-4 3-5 6-1 1-3 6-2 7 0 1 1 1 1 2s0 1-1 1c-4 5-6 11-6 18 0 2 0 4 1 6v1c-1 0-1 0-1-1l-1-1-1-2h-2c0-2 0-3-1-4h0v-2l-2 1h-2l-1 1v-1h0-2l-1-1-1 1-1-1h-1c-1 3-2 6-4 8v-5-2l-4-1c0-2 1-4 2-6h-1l-2 4h-1c-1-2 1-5 1-6v-1l-3 3c0-1 1-3 1-5l3-3h-1c2-2 5-4 7-6h1c2 0 5-4 7-6 0 0 1-1 1-2 0 0 0-1 1-2z" class="F"></path><path d="M366 116c1-1 2-2 2-3l1 1h2c-2 3-4 6-5 9 0 1-1 2-1 3v4l-4-1c0-2 1-4 2-6h0l3-7z" class="I"></path><path d="M375 103l2 2 1-1c1 1 1 1 1 2a30.44 30.44 0 0 0-8 8h-2l-1-1h0v-4c2 0 5-4 7-6z" class="o"></path><path d="M375 103l2 2c-4 2-6 5-9 8v-4c2 0 5-4 7-6z" class="E"></path><path d="M360 115c2-2 5-4 7-6h1v4h0c0 1-1 2-2 3l-3 7h0-1l-2 4h-1c-1-2 1-5 1-6v-1l-3 3c0-1 1-3 1-5l3-3h-1z" class="J"></path><path d="M360 115c2-2 5-4 7-6h1v4h0c0 1-1 2-2 3-1 0-1 1-2 1 0 1-1 2-2 3-1-2 1-4 1-6-1 0-1 1-2 1h-1z" class="K"></path><path d="M377 99h1 3c2-2 7-4 10-5 1 1 1 1 1 2h2l1 1c-2 1-3 2-5 3s-4 2-7 3c-1 1-2 2-4 3h0c0-1 0-1-1-2l-1 1-2-2s1-1 1-2c0 0 0-1 1-2z" class="O"></path><path d="M392 96h2l1 1c-2 1-3 2-5 3-2-1-2-1-4 0h-1v-1c2-2 4-2 7-3z" class="L"></path><path d="M371 125v-2c1-2 3-2 3-4 1-1 1-2 2-3v-1-1h1c1 0 3-1 4-3 1-1 1 0 2-1 1 0 2-2 2-2h1c-1 1-1 2-1 3h0l3-3c1 0 1 0 2-1v-2l1 1c0-1 1-2 2-2-1 1-3 6-2 7 0 1 1 1 1 2s0 1-1 1c-4 5-6 11-6 18 0 2 0 4 1 6v1c-1 0-1 0-1-1l-1-1-1-2h-2c0-2 0-3-1-4h0v-2l-2 1h-2l-1 1v-1h0-2l-1-1-1 1-1-1h-1c0-1 2-3 2-4z" class="i"></path><path d="M380 129h1c1 1 1 1 1 2s2 2 2 3v3l-1-2h-2c0-2 0-3-1-4h0v-2z" class="K"></path><path d="M378 122c2-2 4-5 5-7h1c0 5-3 11-6 15h-2c1-3 1-6 2-8z" class="B"></path><path d="M371 125c2-1 4-3 5-4 0-1 1-2 2-2 1-1 2-2 3-2v1c-2 1-2 2-3 4s-1 5-2 8l-1 1v-1h0-2l-1-1-1 1-1-1h-1c0-1 2-3 2-4z" class="j"></path><defs><linearGradient id="k" x1="715.158" y1="276.833" x2="763.806" y2="266.068" xlink:href="#B"><stop offset="0" stop-color="#0b0000"></stop><stop offset="1" stop-color="#2e0604"></stop></linearGradient></defs><path fill="url(#k)" d="M719 134c4 1 9 2 13 4h1c3 1 6 2 9 2l1 1c1 1 2 1 3 1l-2 1 14 3c3 1 5 1 7 2h2l2-3h4l5 1v4c2 0 4 0 5 1s1 2 1 3l4 1h0c1 1 2 1 3 1 1 1 2 2 2 4l-1 1-1-1c-1 1-1 2-2 3h-2v1h0v2c-1 0-1-1-2-1l-2 2c-1 1-2 1-2 1-1 1-1 2-2 3l1 1s1 0 1-1c1-1 2-1 3-2l2 2 2 2c1 1 2 3 3 3s1 0 2-1h1c-1 1-1 2-2 2v1 1h-3c0 1 0 2 1 4-3 1-6 1-8 3l-3 1c1 0 2 0 3 1v2l-2 1-1 2c1 5 0 10 3 15l2 4h0l1 3 1 1h-1-3 0c-1 1-1 2-2 3h0v1c-1 0-1 1-1 2l-1 1h0 2c-5 4-10 8-13 13h0c-3 6-3 9-2 16l1 2 2 4 4 6c1 1 3 2 4 3h2c2 0 3 0 5-1l3-2c2-1 2-2 4-2-1 1-2 3-4 4h0c1 1 2 1 2 2-3 3-7 8-8 13-3 6-4 12-4 19l3 46 4 17 2 5 5 5 1 1c1 0 1 1 2 1h1c1 1 2 1 4 1l1-1v1h2l1-2c2 1 4 0 5 0 1 1 1 2 2 2 2 1 3 1 5 1l1 2 1 1c-1 0-1 0-2 1h-4v1h4c-6 0-9 1-14 3l-1 1-1 1v2l-1 1c-2-2-1-2-1-3-1-1-3-1-4-1h-5l-1 1c1 1 2 1 3 2h0-3c-2 1-3 4-6 4-2 1-1 0-2 1h-1c-1 0-1 0-1-1-2 2-2 5-2 7-1-3 0-7 0-10h1v-1l1-1c-1-1-1-1-2-1s-1 1-2 1l-1 1c-2 3-3 6-4 8l-2 4-2 2c0-3 1-6 2-8l1-1-1-5c-1-1-2-2-2-3l-1-1c0-4 0-9 1-13h-1l-1-7-5-53c-3-9-4-17-6-26l-7-24c-2-4-2-8-3-12-4-13-11-24-15-37l-3-7v-2c-1-1-1-4-2-5v-1h1 0c0-2-3-4-4-6l1-1 1 1 1-1-7-4-1-1h-8c-3 1-5 1-7 3-2 1-3 2-5 3 0-1 1-3 1-4s1-1 1-1c1-1 2-3 2-4 0-2 1-3 2-4 0-2 1-3 2-4l2-4c-2 1-3 4-5 6 0 1-1 2-1 2l-1 2-2 3c-1 1-1 0-1 1l-1-3 1-1v-1c0-1 0-2 1-3 1-7 3-14 6-20l4-14h0l1-1h0l3 2 2-2 1-1v-3l1-1h0c1-1 2-1 3-1 2 1 4 1 6 2h1l-3-2c0-1 1-2 1-2z"></path><path d="M699 179v1-1c1 0 2-1 3-1h2c-2 2-4 5-7 7l-2 2h-1c1-1 2-3 2-4 0-2 1-3 2-4h1z" class="o"></path><path d="M752 223l1-1 2 1 3 8h-1l-1 2h1v4c-1-2-2-4-2-6l-3-8z" class="AF"></path><path d="M720 182h3c0 2-2 2 1 3h1v1l3 6v2c-2-3-8-6-8-10h-1c1-1 1-1 1-2z" class="R"></path><path d="M716 168l2 3c1 2 2 3 3 5l1 2h-1v-1l-1-1h-1c1 2 1 4 1 6 0 1 0 1-1 2l-2-3c-2-4-4-4-8-5h1c2 0 4-1 5-1l1-1v-1c1 1 1 1 3 1-1-1-1 0-1-1l-1-1h0c-1-1-1-2-1-4z" class="S"></path><path d="M768 276l3 6 2 6c-1 5-1 9 0 13-1 1-1 3-1 4l-4-29z" class="s"></path><path d="M775 347c1 6 2 12 2 17-1 2-1 4-1 7-1 4-2 8-4 11l-1-4 4-23v-8z" class="j"></path><path d="M757 237v-4h-1l1-2h1l2 4 2 2c1 0 1-1 2-1h0c1 0 1 0 2-1l1 1c-3 6-3 9-2 16-2-1-4-5-5-6l-3-9z" class="Ae"></path><path d="M746 235l2 4v1c1 1 1 1 1 2h0c0 1 1 2 1 3l1 4 2 3c0 2 1 8 3 10v3c0 1 0 2 1 3v1c0-1 0-1-1-2h0v2l-1-1h0l1 4c0 3 1 5 2 8h-1l-1-1v-2h0c-1-2-1-1-1-2v-1h-1l-1 1h0v-2h1 1l-1-2v-1c0-2-1-3-1-4h0v-1-2h-1c0-1 0-1 1-2h0l1 3 1-1v-1c-1-4-3-4-5-7l1-1h0v1c1 1 1 1 2 1v-1c0-1-1-2-1-3h-1c-2 0-3-2-3-3v-2-1-1c-1-2-1-2-1-4l1-1-1-1-1-3v-1z" class="W"></path><path d="M751 249l2 3c-1-1-2-1-3-1-1-1-1-2-1-3 0 0 1 1 2 1h0z" class="o"></path><path d="M760 246c1 1 3 5 5 6l1 2c0 8 3 17 6 25v1l1 2h-2l-3-6c-1-2-2-6-2-8l-6-22z" class="AR"></path><path d="M763 372h0c2-5 2-10 2-15 0-2 1-3 2-5l1 1v6 5c0 1 0 2-1 4 0 4-1 7-2 11v4h1l1-1-1 5c-1 1 0 4 0 6v1l-1-5c-1-1-2-2-2-3l-1-1c0-4 0-9 1-13z" class="o"></path><path d="M766 387c-1-1-1-1-2-1 0-2 0-5 1-7v4h1l1-1-1 5z" class="AC"></path><path d="M766 254l2 4 4 6c1 1 3 2 4 3h2-2v1l3 3c-1 1-1 3-2 4l-1 1 1 1-1 1h0l-1 1v-1l-1 1h-2c-3-8-6-17-6-25z" class="AA"></path><path d="M772 264c1 1 3 2 4 3h2-2v1l3 3c-1 1-1 3-2 4-2-1-3-1-4-2v-2c2-1 1 1 3 1h0c-1-1-2-3-3-3h-1v-5z" class="u"></path><path d="M704 169c2-2 4-3 6-4 3 0 4 1 6 3h0c0 2 0 3 1 4h0l1 1c0 1 0 0 1 1-2 0-2 0-3-1v1l-1 1c-1 0-3 1-5 1h-1l-3 1c-1 1-2 1-2 1h-2c-1 0-2 1-3 1v1-1c0-2 1-3 2-4 0-2 2-4 3-6z" class="I"></path><path d="M710 176v-2c2 0 3 0 5 1-1 0-3 1-5 1z" class="L"></path><path d="M701 175l-1 2 4-1c1 0 0 1 2 1-1 1-2 1-2 1h-2c-1 0-2 1-3 1v1-1c0-2 1-3 2-4z" class="W"></path><path d="M736 186l4 5c1 2 2 3 3 5h1c3 2 4 3 8 3l3-1-2 1c-2 2-2 4-2 6v4l1 5 3 7v2l-2-1-1 1-2-3-4-12c-1-3-2-6-4-8 0-2 0-3-1-4-2-3-4-7-5-10z" class="z"></path><path d="M749 210c1-1 0 0 2-1l1 5 3 7v2l-2-1-1 1-2-3 1-2c0-2 0-2-1-3-1-2-1-3-1-5z" class="AB"></path><path d="M743 196h1c3 2 4 3 8 3l3-1-2 1c-2 2-2 4-2 6v4c-2 1-1 0-2 1 0-2-1-3-2-5 0-4-3-6-4-9z" class="AE"></path><path d="M786 264c2-1 2-2 4-2-1 1-2 3-4 4h0c1 1 2 1 2 2-3 3-7 8-8 13-3 6-4 12-4 19v1 8c-1-1-1-1-1-2l1-12-1 1v3h-1c0 1-1 2 0 3v5c0-2 0-4-1-6-1-4-1-8 0-13l-2-6h2l-1-2v-1h2l1-1v1l1-1h0l1-1-1-1 1-1c1-1 1-3 2-4l-3-3v-1h2c2 0 3 0 5-1l3-2z" class="AI"></path><path d="M771 282h2c1 2 0 3 1 4 0 2 1 2 1 4l-1 1-1-1v-2l-2-6z" class="AF"></path><path d="M786 264c2-1 2-2 4-2-1 1-2 3-4 4h0c-3 3-4 6-7 9-1 2-2 3-3 6h-1l-2-2-1 1v-1h2l1-1v1l1-1h0l1-1-1-1 1-1c1-1 1-3 2-4l-3-3v-1h2c2 0 3 0 5-1l3-2z" class="y"></path><path d="M777 364c0 4 0 9 1 14v5l2 1 2-1c3 1 6 1 9 2 3 0 5 0 7 2v2l-1 1c-2-2-1-2-1-3-1-1-3-1-4-1h-5l-1 1c1 1 2 1 3 2h0-3c-2 1-3 4-6 4-2 1-1 0-2 1h-1c-1 0-1 0-1-1-2 2-2 5-2 7-1-3 0-7 0-10h1v-1l1-1c-1-1-1-1-2-1s-1 1-2 1l-1 1c-2 3-3 6-4 8l-2 4-2 2c0-3 1-6 2-8l1-1v-1c0-2-1-5 0-6l1-5 4-4 1 4c2-3 3-7 4-11 0-3 0-5 1-7z" class="i"></path><path d="M771 378l1 4-2 4c0 1-1 2-2 4 0 0-1 1-2 1v2c0-2-1-5 0-6l1-5 4-4z" class="L"></path><path d="M771 389c1-1 2-5 4-5v2h1c1 2 0 5 0 7 2-2 4-5 7-6 1 0 1-1 2-2 2-1 4-1 6 0 3 0 5 0 7 2v2l-1 1c-2-2-1-2-1-3-1-1-3-1-4-1h-5l-1 1c1 1 2 1 3 2h0-3c-2 1-3 4-6 4-2 1-1 0-2 1h-1c-1 0-1 0-1-1-2 2-2 5-2 7-1-3 0-7 0-10h1v-1l1-1c-1-1-1-1-2-1s-1 1-2 1l-1 1z" class="o"></path><path d="M768 216h1l2 2c2 2 5 3 7 5h0 2c-5 4-10 8-13 13h0l-1-1c-1 1-1 1-2 1h0c-1 0-1 1-2 1l-2-2-2-4-3-8v-2c1 1 1 2 2 3 0 1 1 1 2 2h0c1-2 1-5 2-7l-1-3c2 1 2 1 4 0l1 2c2-1 2-1 3-2z" class="f"></path><path d="M761 226c1-3 0-6 1-8h1 1l2 2s-1 0-1 1h1v1c1 0 2 0 3-1v1c-1 1-1 1-2 1v1 1c-1 0-2 0-3 2l-1 1-1 1c0-1 0-2-1-3z" class="y"></path><path d="M768 216h1l2 2c2 2 5 3 7 5h0-1-2c-1-1-2-1-3-1-1-1-2-1-4-2h0 0v1h-1l-1-1-2-2h-1-1c-1 2 0 5-1 8v-7l-1-3c2 1 2 1 4 0l1 2c2-1 2-1 3-2z" class="AA"></path><path d="M761 219v7c1 1 1 2 1 3 1 2 1 4 0 6h-2l-2-4-3-8v-2c1 1 1 2 2 3 0 1 1 1 2 2h0c1-2 1-5 2-7z" class="B"></path><path d="M725 213l5 5v1c-1 2 0 3 2 5 1 1 2 1 3 2 0 1 1 2 1 3v2c1 1 2 2 2 4 1 1 1 2 2 3h0c1 2 3 4 3 6l2-2v1c0 1-1 1-2 2l-1 1 3 7h0v2l4 10 4 15 3 13v1l-1-2v1h0c-1-2-1-3-2-4l-1-6c-1-1-1-2-1-3s-1-2-1-2v-1l-1-4-1-1v-1l-1-3c-1-1 0 0-1-2 0-1 0-1-1-1v-2-1l-1-2v-3c-1-1-1 0-1-1 1-1 1-1 1-2 0-2-1-3-2-4v-2c-1-2-1-3-2-4h-1c-1 2 1 5 2 7v1c1 3 1 6 2 9 1 4 3 7 4 10l2 7v3c0 1 1 2 1 3s0 1 1 2v2l3 14c1 2 2 7 2 10-3-9-4-17-6-26l-7-24c-2-4-2-8-3-12-4-13-11-24-15-37z" class="W"></path><path d="M744 254c3 6 4 11 6 17v3c0-2-1-3-2-4 0-3-1-5-2-7s-1-5-2-6-1 0-1-1c1-1 1-1 1-2z" class="n"></path><path d="M732 224c1 1 2 1 3 2 0 1 1 2 1 3v2c1 1 2 2 2 4 1 1 1 2 2 3h0c1 2 3 4 3 6l2-2v1c0 1-1 1-2 2l-1 1h0l-3-6c-2-4-4-7-5-11-1-2-1-3-2-5z" class="Q"></path><path d="M779 187c1 0 2 0 3 1v2l-2 1-1 1v2h-1l-2-2h-1c-3 2-6 5-9 8l-2 3c-2 3-3 5-4 8v5l1 3c-1 2-1 5-2 7h0c-1-1-2-1-2-2-1-1-1-2-2-3l-3-7-1-5v-4c0-2 0-4 2-6l2-1c6-4 14-7 21-10 1 0 2-1 3-1z" class="L"></path><path d="M751 209v-4c0-2 0-4 2-6 0 2-1 4-1 6h0c0 2 1 3 1 4s0 1 1 3l2 4c0-1 0-1-1-2v-2l-1-1c0-1 1-2 1-2 1-1 0-1 1-2l-1-1h1 1l1-3c2 0 2 1 3 1 0 1 2 0 3-1-2 3-3 5-4 8v5l1 3c-1 2-1 5-2 7h0c-1-1-2-1-2-2-1-1-1-2-2-3l-3-7-1-5z" class="V"></path><path d="M752 214c2 2 3 4 5 6h1v-4c1-2 2-3 2-5v5l1 3c-1 2-1 5-2 7h0c-1-1-2-1-2-2-1-1-1-2-2-3l-3-7z" class="h"></path><path d="M720 191c7 5 15 14 19 23h0l3 3v1 4l1 1h0c0 1 0 1 1 2s1 3 1 4v1c1 1 1 3 1 4v1 1c-1-1-1-2-1-2h-1c-1-1-1-2-2-2s-1 0-2 1c1 1 1 1 2 1-1 0-1 1-2 0h-1v2c1-1 1 0 2-1 0 1-1 2-1 3h0c-1-1-1-2-2-3 0-2-1-3-2-4v-2c0-1-1-2-1-3-1-1-2-1-3-2-2-2-3-3-2-5v-1l-5-5-3-7v-2c-1-1-1-4-2-5v-1h1 0c0-2-3-4-4-6l1-1 1 1 1-1z" class="L"></path><path d="M735 220h1c2 3 2 3 2 5h-1c0-2-2-2-2-5z" class="I"></path><path d="M739 214l3 3v1 4l1 1h0c0 1 0 1 1 2s1 3 1 4v1c1 1 1 3 1 4l-1-1c0-2-1-4-3-6 0 0-1-1-1-2-1-1 0-1 0-2v-2c-1-3-2-5-2-7z" class="R"></path><path d="M726 201c2 1 6 6 7 7l1 3 1 2c0 1-1 1-2 2h-1c0-1 0-3-1-4l-5-10z" class="Q"></path><path d="M721 198c0-2-3-4-4-6l1-1 1 1c1 1 3 2 4 4 0 1 3 5 3 5l5 10c-1 1 0 1-1 1-3-5-5-10-9-14z" class="c"></path><path d="M775 192h1l2 2h1v-2l1-1-1 2c1 5 0 10 3 15l2 4h0l1 3 1 1h-1-3 0c-1 1-1 2-2 3h0v1c-1 0-1 1-1 2l-1 1c-2-2-5-3-7-5l-2-2h-1c-1 1-1 1-3 2l-1-2c-2 1-2 1-4 0v-5c1-3 2-5 4-8l2-3c3-3 6-6 9-8z" class="D"></path><path d="M768 216l-1-1c0-2-1-2 0-3l1 1 1-1h-1v-1-1l1-1v1c1-1 2-1 3-1l1 1c-1 0-2 0-2 1h0c-1 2-1 3-2 5h-1z" class="u"></path><path d="M782 208l2 4h0l1 3c-1 1-1 0-2 0-2-2-3-3-4-6 1 0 2 0 3-1z" class="F"></path><path d="M773 201l4 2c0 2 1 4 1 6l-3-1v-2h-2-1-1v-1c1-1 1-1 3-1l-1-3z" class="B"></path><path d="M775 192l1 1v1 4l1 5-4-2h2c-2 0-3 1-5 2h-1l3-2v-1-1c0-1 0-1-1-1v-1h0c-1 1-3 2-4 3h-1c3-3 6-6 9-8z" class="p"></path><path d="M775 192h1l2 2h1v-2l1-1-1 2c1 5 0 10 3 15-1 1-2 1-3 1h-1c0-2-1-4-1-6l-1-5v-4-1l-1-1z" class="E"></path><path d="M773 210h2v3c-1 1 0 2 0 3h1l2-1v2h1l1 2h0v1c-1 0-1 1-1 2l-1 1c-2-2-5-3-7-5l-2-2c1-2 1-3 2-5h0c0-1 1-1 2-1z" class="B"></path><path d="M776 216l2-1v2l-1 1-1 1-1-1 1-2z" class="G"></path><path d="M778 217h1l1 2h0v1c-1 0-1 1-1 2l-1 1c-2-2-5-3-7-5h3v1h2l1-1 1-1z" class="u"></path><path d="M773 301c1 2 1 4 1 6v-5c-1-1 0-2 0-3h1v-3l1-1-1 12c0 1 0 1 1 2v-8-1l3 46 4 17 2 5 5 5 1 1c1 0 1 1 2 1h1c1 1 2 1 4 1l1-1v1h2l1-2c2 1 4 0 5 0 1 1 1 2 2 2 2 1 3 1 5 1l1 2 1 1c-1 0-1 0-2 1h-4v1h4c-6 0-9 1-14 3l-1 1-1 1c-2-2-4-2-7-2-3-1-6-1-9-2l-2 1-2-1v-5c-1-5-1-10-1-14 0-5-1-11-2-17 0-5-1-11-1-16l-2-26c0-1 0-3 1-4z" class="AE"></path><path d="M779 346l4 17 2 5-2-2c-1 0-1-1-1-2-1-1-1-1-1-2l-1 2c-1-3-2-8-1-11v-7z" class="a"></path><path d="M780 364l1-2c0 1 0 1 1 2 0 1 0 2 1 2l2 2 5 5 1 1c1 0 1 1 2 1h1c1 1 2 1 4 1l1-1v1h2l1-2c2 1 4 0 5 0 1 1 1 2 2 2 2 1 3 1 5 1l1 2 1 1c-1 0-1 0-2 1h-4v1h4c-6 0-9 1-14 3l-1 1-1 1c-2-2-4-2-7-2-3-1-6-1-9-2l1-1c1 0 0 0 1-1l-3-3h1l3 3h3l-3-2-2-2c-1-2-2-5-3-7v-6z" class="D"></path><path d="M809 376c2 1 3 1 5 1l1 2c-1 1-3 0-4 0-2 0-3 0-4-1 1 0 1-1 2-2z" class="u"></path><path d="M815 379l1 1c-1 0-1 0-2 1h-4v1h4c-6 0-9 1-14 3l-1 1c-1-1-1-2-1-3l2-2c3-1 7-1 11-2 1 0 3 1 4 0z" class="AF"></path><path d="M782 383l1-1c1 0 0 0 1-1l-3-3h1l3 3h3c3 1 8 2 11 1l1-1-2 2c0 1 0 2 1 3l-1 1c-2-2-4-2-7-2-3-1-6-1-9-2z" class="AR"></path><path d="M780 364l1-2c0 1 0 1 1 2 0 1 0 2 1 2l2 2 5 5 1 1v2l1 1c-2 0-3-1-4-1h-1-1-2v-1c-1-1-1-1-1-2l-1-1c-1-1 0-1-1-2h-1v-6z" class="M"></path><path d="M780 364l1-2c0 1 0 1 1 2 0 1 0 2 1 2 1 3 1 5 2 8l-1 1c-1-1-1-1-1-2l-1-1c-1-1 0-1-1-2h-1v-6z" class="k"></path><path d="M779 171l1 1s1 0 1-1c1-1 2-1 3-2l2 2 2 2c1 1 2 3 3 3s1 0 2-1h1c-1 1-1 2-2 2v1 1h-3c0 1 0 2 1 4-3 1-6 1-8 3l-3 1c-1 0-2 1-3 1-7 3-15 6-21 10l-3 1c-4 0-5-1-8-3h-1c-1-2-2-3-3-5l-4-5v-1c-1-2-1-3 0-5l-4-7 2-1c2-1 5-2 7-2h5 0 2v3c2 0 2 0 3 2h0c1 1 2 2 3 2s3 0 4-1 1-2 2-3v-1c1 0 1-1 2-1h1 2c1-1 2-1 3-1h2 1l3 3 1-3h2c1 0 2 0 2 1z" class="AA"></path><path d="M756 189c3 0 4 0 6 2 1 0 1 0 2 1-2 0-4 1-5 1-1-1-1-3-2-4l-1 1v-1z" class="s"></path><path d="M762 186h1c1-2 2-2 4-2l1 1h0v1c0 1 0 1 1 2h-1l-1-2c-1 1-2 1-2 2h-1v-1-1l-2 2v-2z" class="f"></path><path d="M756 183h1c1 1 2 1 3 1l2 2v2h-2l-1-1c-2 2-3 2-5 2-2-1-3-2-4-3 1 0 2 0 3 1h1l3-3-1-1z" class="B"></path><path d="M749 190c0-2-2-3-3-5h3l1 1c1 1 2 2 4 3h2v1l1-1c1 1 1 3 2 4l-5 2v-1c-1 0-1-1-1-2l-2 1-1-1c-1 0-1-1-1-2z" class="y"></path><path d="M756 190l1-1c1 1 1 3 2 4l-5 2v-1c-1 0-1-1-1-2l-2 1-1-1c0-1 0-1 1-1h1 3 1v-1z" class="z"></path><path d="M764 192h0c3-1 10-5 12-4-7 3-15 6-21 10l-3 1c-4 0-5-1-8-3l2-3c1 1 3 3 4 3s4-1 4-1l5-2c1 0 3-1 5-1z" class="AZ"></path><path d="M744 187c2 1 3 3 5 3 0 1 0 2 1 2l1 1 2-1c0 1 0 2 1 2v1s-3 1-4 1-3-2-4-3l-2 3h-1c-1-2-2-3-3-5l1-1 1-1c1 0 2-1 2-2z" class="AE"></path><path d="M742 189l4 4-2 3h-1c-1-2-2-3-3-5l1-1 1-1z" class="Ac"></path><path d="M779 171l1 1s1 0 1-1c1-1 2-1 3-2l2 2 2 2c1 1 2 3 3 3s1 0 2-1h1c-1 1-1 2-2 2v1 1h-3c-1 0-2 1-3 1h-2 0v-1c1-1 1-1 1-2-2 0-2 1-3 2s-1 2-3 3l-1 1h-3c0-1 1-2 0-3v1-1h-1c-1-1-2-1-3-1v-1h0 3l1-3-1-1v-1l1-3h2c1 0 2 0 2 1z" class="M"></path><g class="s"><path d="M784 172l2 2c-1 1-1 1-3 1h-1l-2-2c1 0 2-1 4-1zm-9 3l1-2c1 1 1 1 2 1h1c-1 2-2 2-1 4h1v1c-1 1-1 1-1 2l1 1-1 1h-3c0-1 1-2 0-3v1-1h-1c-1-1-2-1-3-1v-1h0 3l1-3z"></path><path d="M771 170l3 3v1l1 1-1 3h-3 0v1c1 0 2 0 3 1 0 3 0 3-2 6h-1v-2l-1-2v1c0 1-1 2-2 2h0l-1-1c-2 0-3 0-4 2h-1l-2-2c-1 0-2 0-3-1h-1l-3-3v1 1h-2c0-2-1-2-2-4 0-1 1-2 2-3 1 1 2 2 3 2s3 0 4-1 1-2 2-3v-1c1 0 1-1 2-1h1 2c1-1 2-1 3-1h2 1z"></path></g><path d="M760 184l1-2-1-2 2-1h0c2 1 2 1 4 1l1 1 1-1h1c0 2 0 3-1 5l-1-1c-2 0-3 0-4 2h-1l-2-2z" class="AA"></path><path d="M764 176l1-1c0-1 0-1-1-2 2-1 4 0 6-1l1-1 1 1c0 1 0 1 1 2h1l1 1-1 3h-3 0v1c1 0 2 0 3 1 0 3 0 3-2 6h-1v-2l2-2c-1-1 0-1-1-2l-2 1c0-1 0-1-2-2 1-1 1-1 1-2-2 0-3 0-5-1h0z" class="p"></path><path d="M771 170l3 3v1h-1c-1-1-1-1-1-2l-1-1-1 1c-2 1-4 0-6 1 1 1 1 1 1 2l-1 1-2-2c-1 1-1 2-1 3-1 1-2 1-2 2 0 2 0 3-2 4h-1l-3-3v1 1h-2c0-2-1-2-2-4 0-1 1-2 2-3 1 1 2 2 3 2s3 0 4-1 1-2 2-3v-1c1 0 1-1 2-1h1 2c1-1 2-1 3-1h2 1z" class="a"></path><path d="M741 170h5 0 2v3c2 0 2 0 3 2h0c-1 1-2 2-2 3 1 2 2 2 2 4h2v-1-1l3 3 1 1-3 3h-1c-1-1-2-1-3-1l-1-1h-3c1 2 3 3 3 5-2 0-3-2-5-3 0 1-1 2-2 2l-1 1-1 1-4-5v-1c-1-2-1-3 0-5l-4-7 2-1c2-1 5-2 7-2z" class="s"></path><path d="M740 185c1 0 2 0 3 1 0 0 1 0 1 1s-1 2-2 2l-2-4z" class="y"></path><path d="M736 185c-1-2-1-3 0-5l1 2c1 0 2 3 3 3l2 4-1 1-1 1-4-5v-1z" class="Ae"></path><path d="M734 172l1 2h0 1c0-1 0-1 1-2l3 3v1h-2v1c1 1 1 2 2 3-1 1-2 1-3 2l-1-2-4-7 2-1z" class="G"></path><path d="M741 170h5 0 2v3c-1 0-1 1-2 1h-1c-1 0-1 0-2-1-1 0-1 0-2 1l-1 1-3-3c-1 1-1 1-1 2h-1 0l-1-2c2-1 5-2 7-2z" class="V"></path><path d="M743 173l-1-2h0c1-1 3-1 4-1h2v3c-1 0-1 1-2 1h-1c-1 0-1 0-2-1z" class="T"></path><path d="M748 173c2 0 2 0 3 2h0c-1 1-2 2-2 3 1 2 2 2 2 4h2v-1-1l3 3 1 1-3 3h-1c-1-1-2-1-3-1l-1-1c-2-2-3-5-6-5-1-1-2-2-3-4v-1l1-1c1-1 1-1 2-1 1 1 1 1 2 1h1c1 0 1-1 2-1z" class="k"></path><path d="M748 173c2 0 2 0 3 2h0c-1 1-2 2-2 3 1 2 2 2 2 4 0 1 0 1-1 1-2-2-4-7-7-8l-2-1c1-1 1-1 2-1 1 1 1 1 2 1h1c1 0 1-1 2-1z" class="J"></path><path d="M748 173c2 0 2 0 3 2h0c-1 1-2 2-2 3-1-2-2-2-3-4 1 0 1-1 2-1z" class="F"></path><defs><linearGradient id="l" x1="699.315" y1="160.047" x2="719.514" y2="173.487" xlink:href="#B"><stop offset="0" stop-color="#220403"></stop><stop offset="1" stop-color="#420b09"></stop></linearGradient></defs><path fill="url(#l)" d="M710 141v-3l1-1h0c1-1 2-1 3-1v1c1 0 1 0 2 1 1 0 1 1 2 2v1c0 1 1 2 2 4h0c1 2 2 3 3 4h1l1 1h1l3 4 4 2c1 2 2 2 4 3h1l8 3 2 1v1l-1-1-1 1c-1 0-2-1-3-1 0 1 0 2 1 3h0l2 3c1 0 3-1 4-1h1l1 1c-2 1-2 1-4 1h-2 0-5c-2 0-5 1-7 2l-2 1 4 7c-1 2-1 3 0 5v1c1 3 3 7 5 10 1 1 1 2 1 4-1-1-2-3-3-4 0-1-1-1-1-2-1-1-1-2-2-3v-1c-1-1-2-3-3-5 0-1 0-2-1-3s-1-2-2-3c-1-2-2-3-3-4l-10-10c-2-1-3-2-5-3h-3c-2 1-6 5-6 7h1c-1 2-3 4-3 6-1 1-2 2-2 4h-1c0-2 1-3 2-4l2-4c-2 1-3 4-5 6 0 1-1 2-1 2l-1 2-2 3c-1 1-1 0-1 1l-1-3 1-1v-1c0-1 0-2 1-3 1-7 3-14 6-20l4-14h0l1-1h0l3 2 2-2 1-1z"></path><path d="M712 160c3 2 3 2 5 5-2-1-3-2-5-3v-2z" class="R"></path><path d="M699 157h1l1-2 1 1-10 24c0-1 0-2 1-3 1-7 3-14 6-20z" class="s"></path><path d="M703 143l1-1h0l3 2c1 1 2 2 2 3l-1 1h-1c-3 1-2 6-5 8l-1-1-1 2h-1l4-14h0z" class="D"></path><path d="M703 143l1-1h0l3 2c1 1 2 2 2 3l-1 1h-1v-1s-1-1-2-1c-1-1-1-2-2-3z" class="M"></path><path d="M698 174c0-2 2-6 3-8 1-1 2-1 3-2-1-1-1-1-1-2l2-3h5l2 1v2h-3c-2 1-6 5-6 7h1c-1 2-3 4-3 6-1 1-2 2-2 4h-1c0-2 1-3 2-4l2-4c-2 1-3 4-5 6 0 1-1 2-1 2l-1 2-2 3c-1 1-1 0-1 1l-1-3 1-1c1 0 2-1 3-2 1-2 0-1 1-2s1-1 2-3z" class="AC"></path><path d="M698 174c0-2 2-6 3-8 1-1 2-1 3-2-1-1-1-1-1-2l2-3h5c-2 1-3 1-4 3 1 0 1 0 2-1-1 2-2 3-3 4-2 2-3 4-5 6-1 1-1 2-2 3z" class="S"></path><path d="M709 142l1-1 7 7c3 6 7 12 12 17 0 1 0 2 1 3l2 2c2 0 3 0 5-1h3l1 1c-2 0-5 1-7 2l-2 1 4 7c-1 2-1 3 0 5l-9-14-13-18-6-5 1-1c0-1-1-2-2-3l2-2z" class="AE"></path><path d="M709 142c1 2 1 3 2 5 1 1 3 2 3 3v3l-6-5 1-1c0-1-1-2-2-3l2-2z" class="p"></path><path d="M727 171h0c0-3-1-5-2-8 2 3 4 6 7 10l4 7c-1 2-1 3 0 5l-9-14z" class="AQ"></path><path d="M710 141v-3l1-1h0c1-1 2-1 3-1v1c1 0 1 0 2 1 1 0 1 1 2 2v1c0 1 1 2 2 4h0c1 2 2 3 3 4h1l1 1h1l3 4 4 2c1 2 2 2 4 3h1l8 3 2 1v1l-1-1-1 1c-1 0-2-1-3-1 0 1 0 2 1 3h0l2 3c1 0 3-1 4-1h1l1 1c-2 1-2 1-4 1h-2 0-5l-1-1h-3c-2 1-3 1-5 1l-2-2c-1-1-1-2-1-3-5-5-9-11-12-17l-7-7z" class="E"></path><path d="M735 166l2-2-3-2c-2-1-3-2-4-3v-1l4 1c2 1 5 2 7 3 1 1 1 1 2 1 0 1 0 2 1 3h0v1h-3-1c-2 1-4 0-5-1z" class="F"></path><path d="M723 149h1l1 1h1l3 4 4 2c1 2 2 2 4 3h1l8 3 2 1v1l-1-1-1 1c-1 0-2-1-3-1s-1 0-2-1c-2-1-5-2-7-3s-4-2-6-4l-1 1 2 4-1-1c0-1-1-1-2-2-1-2-1-3-2-5 0-1-1-2-1-3z" class="O"></path><path d="M729 165l3 2h1l1-1h1c1 1 3 2 5 1h1 3v-1l2 3c1 0 3-1 4-1h1l1 1c-2 1-2 1-4 1h-2 0-5l-1-1h-3c-2 1-3 1-5 1l-2-2c-1-1-1-2-1-3z" class="B"></path><path d="M710 141v-3l1-1h0c1-1 2-1 3-1v1c1 0 1 0 2 1 1 0 1 1 2 2v1c0 1 1 2 2 4h0c1 2 2 3 3 4 0 1 1 2 1 3 1 2 1 3 2 5 1 1 2 1 2 2l1 1s0 1 1 2l-6-6c-3-3-4-6-7-8l-7-7z" class="S"></path><defs><linearGradient id="m" x1="748.173" y1="142.093" x2="744.133" y2="154.543" xlink:href="#B"><stop offset="0" stop-color="#2a0303"></stop><stop offset="1" stop-color="#4d0706"></stop></linearGradient></defs><path fill="url(#m)" d="M719 134c4 1 9 2 13 4h1c3 1 6 2 9 2l1 1c1 1 2 1 3 1l-2 1 14 3c3 1 5 1 7 2h2l2-3h4l5 1v4c2 0 4 0 5 1s1 2 1 3l4 1h0c1 1 2 1 3 1 1 1 2 2 2 4l-1 1-1-1c-1 1-1 2-2 3h-2v1h0v2c-1 0-1-1-2-1l-2 2c-1 1-2 1-2 1-1 1-1 2-2 3 0-1-1-1-2-1h-2l-1 3-3-3h-1-2c-1 0-2 0-3 1h-2-1c-1 0-1 1-2 1v1c-1 1-1 2-2 3s-3 1-4 1-2-1-3-2h0c-1-2-1-2-3-2v-3c2 0 2 0 4-1l-1-1h-1c-1 0-3 1-4 1l-2-3h0c-1-1-1-2-1-3 1 0 2 1 3 1l1-1 1 1v-1l-2-1-8-3h-1c-2-1-3-1-4-3l-4-2-3-4h-1l-1-1h-1c-1-1-2-2-3-4h0c-1-2-2-3-2-4v-1c-1-1-1-2-2-2-1-1-1-1-2-1v-1c2 1 4 1 6 2h1l-3-2c0-1 1-2 1-2z"></path><path d="M769 145h4l5 1v4c-2 0-4-1-6-2h-1l-1 1-3-1 2-3z" class="AI"></path><path d="M782 157c-1-1-2-2-3-2h-2 0c-1-1-3-2-4-3l-1-1h1 4c2 1 4 1 5 1s1 1 2 1v1l4 1h0c1 1 2 1 3 1 1 1 2 2 2 4l-1 1-1-1c-1 1-1 2-2 3h-2v-2c-1-1-1-2-2-3h0c-1 0-2 0-3-1z" class="n"></path><path d="M785 158h2v2h4 0c-1 1-1 2-2 3h-2v-2c-1-1-1-2-2-3h0z" class="R"></path><path d="M719 134c4 1 9 2 13 4h1c3 1 6 2 9 2l1 1c1 1 2 1 3 1l-2 1 14 3c3 1 5 1 7 2-4 1-8 0-13-1-10-2-22-5-32-9h1l-3-2c0-1 1-2 1-2z" class="z"></path><path d="M732 138h1c3 1 6 2 9 2l1 1c1 1 2 1 3 1l-2 1c-3 0-8-2-11-3h0l-1-2z" class="AB"></path><path d="M719 134c4 1 9 2 13 4l1 2h0l-12-2-3-2c0-1 1-2 1-2z" class="Ad"></path><path d="M724 149c-1-1-1-2-1-2h1c1-1 1-1 2-1h5 0c1 0 1 1 2 1 1 1 3 1 4 1l6 3c1 0 2 1 3 1-1 1-1 1-2 3h1 2 0l1-1 1-1v1l1 1 3-1v1 1l1 1c2 1 3 2 5 3h0-2-2c-1 0-1-1-2-1h-1-1c-1 0-2 1-3 0v3h0v1l-2-1-8-3h-1c-2-1-3-1-4-3l-4-2-3-4h-1l-1-1z" class="V"></path><path d="M726 150h1c1 1 1 1 3 1 1 0 2 0 3 1 3 1 4 1 7 1h3v2c1 0 1 1 3 1 0 0 1 0 2 1v1c1 1 3 1 4 1h-1c-1 0-2 1-3 0v3h0v1l-2-1-8-3h-1c-2-1-3-1-4-3l-4-2-3-4z" class="J"></path><path d="M729 154c2-1 3-1 5 0 1 0 2 1 2 1 2 0 3 0 5 2h1c1 1 2 1 2 2l2-1c0 1 1 1 1 2l-1 2-8-3h-1c-2-1-3-1-4-3l-4-2z" class="B"></path><path d="M769 160c-1-1-1-1-1-2 3-2 5-2 7-2l1 2c1-2 1-2 3-2h1c0 1 1 2 1 2v1c1-1 1-1 1-2 1 1 2 1 3 1h0c1 1 1 2 2 3v2 1h0v2c-1 0-1-1-2-1l-2 2c-1 1-2 1-2 1-1 1-1 2-2 3 0-1-1-1-2-1h-2l-1 3-3-3h-1-2c-1 0-2 0-3 1h-2-1c-1 0-1 1-2 1v1c-1 1-1 2-2 3s-3 1-4 1-2-1-3-2h0c-1-2-1-2-3-2v-3c2 0 2 0 4-1l-1-1h-1c-1 0-3 1-4 1l-2-3h0c-1-1-1-2-1-3 1 0 2 1 3 1l1-1 1 1v-1-1h0v-3c1 1 2 0 3 0h1 1c1 0 1 1 2 1h2 2 0l2-1 2 1h1 1c1-1 1 0 3 0h1z" class="E"></path><path d="M765 169h0c-1 0-3 1-5 0h-1 0v-1h1c2-1 3-1 5-1v2z" class="O"></path><path d="M743 163c1 0 2 1 3 1l1-1c1 2 1 2 1 3h1c1 0 2 0 3 1l1-1v2c0 1-1 1-1 1l-1-1h-1c-1 0-3 1-4 1l-2-3h0c-1-1-1-2-1-3z" class="J"></path><path d="M769 160c-1-1-1-1-1-2 3-2 5-2 7-2l1 2c1-2 1-2 3-2h1c0 1 1 2 1 2v1 1l-1-1-1 1-1 2c-2 1-3 1-4 2h-2v3c-1 1-1 1-1 2-2-1-3-1-6 0v-2h1c1-1 2-1 3-1s1-2 2-2l1-1c-1-1-2-1-3-3h0z" class="I"></path><path d="M782 157c1 1 2 1 3 1h0c1 1 1 2 2 3v2 1h0v2c-1 0-1-1-2-1l-2 2c-1 1-2 1-2 1-1 1-1 2-2 3 0-1-1-1-2-1h-2l-1 3-3-3v-1c0-1 0-1 1-2v-3h2c1-1 2-1 4-2l1-2 1-1 1 1v-1c1-1 1-1 1-2z" class="T"></path><path d="M787 161v2 1h0v2c-1 0-1-1-2-1l-2 2-1-1 1-1v-1l-3 1c0-1 1-1 2-2h0 1c2 0 3-1 4-2z" class="O"></path><path d="M782 157c1 1 2 1 3 1h0l-1 3h0-1-1l-3 3c-1-1-1-1-1-2l1-2 1-1 1 1v-1c1-1 1-1 1-2z" class="o"></path><path d="M780 165l3-1v1l-1 1 1 1c-1 1-2 1-2 1-1 1-1 2-2 3 0-1-1-1-2-1h-2l-1 3-3-3v-1c0-1 0-1 1-2 3-1 3-1 6-1 1 0 1 0 2-1z" class="K"></path><path d="M772 167c3-1 3-1 6-1h-1c-1 1-1 0-2 0l-1 1c1 1 2 1 3 3h-2l-1 3-3-3v-1c0-1 0-1 1-2z" class="H"></path><defs><linearGradient id="n" x1="772.194" y1="361.277" x2="550.734" y2="427.839" xlink:href="#B"><stop offset="0" stop-color="#010303"></stop><stop offset="1" stop-color="#150302"></stop></linearGradient></defs><path fill="url(#n)" d="M692 185c0-1 0 0 1-1l2-3 1-2s1-1 1-2c2-2 3-5 5-6l-2 4c-1 1-2 2-2 4-1 1-2 2-2 4 0 1-1 3-2 4 0 0-1 0-1 1s-1 3-1 4c2-1 3-2 5-3 2-2 4-2 7-3h8l1 1 7 4-1 1-1-1-1 1c1 2 4 4 4 6h0-1v1c1 1 1 4 2 5v2l3 7c4 13 11 24 15 37 1 4 1 8 3 12l7 24c2 9 3 17 6 26l5 53 1 7h1c-1 4-1 9-1 13l1 1c0 1 1 2 2 3l1 5-1 1c-1 2-2 5-2 8l2-2 2-4c1-2 2-5 4-8l1-1c1 0 1-1 2-1s1 0 2 1l-1 1v1h-1c0 3-1 7 0 10 0-2 0-5 2-7 0 1 0 1 1 1h1c1-1 0 0 2-1 3 0 4-3 6-4h3 0 2c2 1 1 1 2 3-1 0-1 1-1 2h0c1 0 1 0 1 1l2-1c1 0 2 1 2 2 1 1 1 1 1 2 0 2-1 2-2 3 1 1 1 1 3 1v-2l2-2c1-1 1-2 1-4l1 1v3l-2 2-2 2 1 2c1 2 1 4 2 5s1 2 1 3v2c1 1 2 3 3 4l1 3c1 0 2 0 2 1l-2 1v4h1c-2 3-2 5-1 8 0 1 0 2 1 4v1c-1-1-2-2-3-1l1 4c1 1 2 3 3 4l-1 1c-1-1-1-1-2 0h0c-1 1-2 3-3 3l-1 2h-1c-1-1-1-2-2-2s-1-1-2-1h-2v2c-1 0-2 1-3 2h-1l-1 1h3v1h-1v1c1 1 1 2 2 4l-2 6-4 4c-1 1-2 2-2 3-2 2-3 4-5 6h2l1 1-1 1-1 1 4 5c0 2 0 3 1 4v1c5 5 12 8 18 12l1 1h1l1 1c-1 1-3 2-5 2-12 2-19 8-25 18l-8 17c1 2 1 4 2 5l-3 3c-4 4-6 8-9 13 0 2 0 3 1 5l1 1c-2 0-2 1-3 0-2 0-5 1-6 2-2 2-4 3-6 5s-4 4-7 4l-3 3h-1c-1 2-4 4-4 6 0 1 0 1-1 2 0 2-2 4-4 5 0 1-1 1-1 1 0 1 2 1 1 2l-2 1v1c-2 0-4 2-6 3v2h-2c-1 0-1 0-2-1v-1 2l-2-1h-2c-1-1-3-1-4-1-3-1-7-1-10-2-3 0-6-1-9-2l-15-5h4c-4-2-7-3-10-4s-6-3-9-4c0-1 1-1 2-2-4-2-8-4-10-8h0c-3-4-7-7-11-10-3-3-5-6-7-10l2-1-2-3c-1-2 0-5 1-7v-1c-1-1-1-3-2-4h-1v-2c-1-1-3-2-5-3-1-2-2-3-3-5l-1-2c-1 1-1 3-1 5l-1-3-1 2-1-2-2-1-1 3-1 4c-2 4-4 6-7 9l-2 3h1c-1 1-1 1-3 2 0 0-1 1-2 1v1h-1c-1 1-2 2-3 2-1 1-1 1-1 2h-2-1-2c-2-1-3-1-3-3l-1-1v-1 1h-1v1h-1l3-11-2 1 1-3 1-6c0-2 1-5 2-7s1-3 1-6l9-34 1-2 1-3v-1c1-1 1-2 2-3l1-1v-1 4h-1v1 1 1c0 1-1 1-1 2h1l10-35 7-26v-1l-1 1h0v-4h0c0-1 2-12 3-14h0l5-15c0-3 1-5 1-7 2-5 3-11 5-16 1-6 4-12 5-17l1-4 20-68 1-1c1-1 1-2 2-4 0 0 1-1 1-2l1 1h-1c0 1 0 1-1 2v2 2c2-5 4-9 6-13 8-13 19-17 33-20 2-6 5-13 6-19v-4h-9-19c0-3 1-5 3-8l-2-6 1-1c0-1 0-1 1-2l10-24z"></path><path d="M762 385l1 1c0 1 1 2 2 3l1 5-1 1v-2c-2-3-2-5-3-8z" class="c"></path><path d="M723 550l-1 7h-1v-2l-1 1v-1-2c1 0 2-2 3-3z" class="C"></path><path d="M692 451l1-6 1-1c1 2 1 5 0 7v1l-2-1z" class="W"></path><path d="M730 358l3 2c0 1 1 4 0 5h-1l-3-7h1z" class="U"></path><path d="M721 359h0c-1 0-2-1-3-2-1 0-1 0-1-1l1-2v-1-1l1 1c1 1 1 2 2 3s1 2 3 3h-3z" class="C"></path><path d="M681 382l1-2c0 2-2 4-3 5h-1c-2 0-4 4-6 5 1-2 3-4 4-5h-3l4-4c1 1 1 0 1 1h1 2z" class="R"></path><path d="M725 260c2 1 2 1 3 2v3c0 1 0 2-1 2h0v3l-4 1c1-2 1-3 2-4l1-1-1-6z" class="O"></path><path d="M726 577l-3-3h-1l6-6c0 1 0 2-1 3v2 1l-1 2 1 1h-1z" class="Q"></path><path d="M736 439c3 4 3 10 3 15-2-4-4-10-4-14h1v-1z" class="P"></path><path d="M747 308c1 1 1 2 2 4 1 6 1 12 2 18h-1c-3-5 0-12-2-17-1-2-1-4-1-5z" class="AJ"></path><path d="M685 370l1-1 1 2-5 9-1 2c-1-1-1-1-2-1v-2c2-2 5-6 6-9zm46 192l2 2c-1 3-3 7-6 10v-1-2c1-1 1-2 1-3l3-6z" class="B"></path><path d="M719 586c-1 2-2 3-4 4v1c-1 1-1 2-2 2l-2 2h-2v-1l-1 1c-3 1-4 1-7 1 7-2 12-7 18-10z" class="h"></path><path d="M735 350c1 4 1 8 2 12v4c-1 2 0 7 0 10h0v-3h0v-2c0-1-1-2-1-3h-1v2c0-1-1-1-1-2l1-1h1c0-2 0-1-1-2v-4l-1-1c1-2 1-3 0-6 1-1 0-1 0-1 0-1 1-2 1-3z" class="C"></path><path d="M739 359c2 5 2 12 5 17 1 2 1 5 1 8-1-1-1-3-2-4l-1 1-2-17c-1-2-1-4-1-5z" class="Z"></path><path d="M743 481c-2 1-2 7-4 9h0c-1-4 3-13 3-16 1-2 1-4 1-6l1 11-1 2z" class="C"></path><path d="M729 358c-2-3-5-6-6-10-1-2-1-5-2-7 4 4 7 11 9 17h-1z" class="Z"></path><path d="M724 252c1-1 2-1 4-1l1 1h-1c0 1 0 1-1 0v1 1c0 3 0 5 2 7 0 1 0 1 1 1 0 1 0 1-1 2l-1 1v-3c-1-1-1-1-3-2l-1-8z" class="S"></path><path d="M726 500c0 2-1 4-2 5-3 4-9 7-11 12l-2 1c0-2 0-4 1-6 2-2 7-3 9-5s4-4 5-7z" class="r"></path><path d="M706 368v-2h1c1 6 4 10 6 15h0c-2 0-2-1-3-2s-2-3-3-4l-1 6v-2-11z" class="D"></path><path d="M726 419c-2-1-4-3-6-4-1 0-1-1-2-2-2-1-3-2-5-2v-1-1c3 1 6 2 9 4 2 1 4 2 6 4l-2 2z" class="l"></path><path d="M689 270l4-5-3 20-2-2c0-1 1-4 1-5v-8z" class="o"></path><path d="M734 267c1-1 1-2 3-3-1-1-1-1-1-2v-2h-1l-1-1 2-1 1 4c1-1 1-2 2-3 1 0 0 0 1 1l1 2v4h-1-1c-1 2-2 4-4 6 0-2 1-3 1-5v-2l-2 2z" class="C"></path><path d="M746 450v15c-1 3-1 7-2 10v4l-1-11 1-11 2-7z" class="Y"></path><path d="M624 490c0-2 0-2 1-3v-2h0c3-3 6-7 9-10 0 3 0 5-1 7l-4 7v-1-4-1c-1 1-3 3-4 5l-1 2z" class="L"></path><path d="M739 546h1c0 2-1 4-1 6-2 4-3 9-6 12l-2-2c0-1 2-3 2-3l6-13z" class="J"></path><path d="M672 390c2-1 4-5 6-5v1l-6 9c-1 1-1 1-2 3h0l-1 1v1c-1 1-1 2-2 3l-1-2v-2s1-2 2-2l4-7z" class="E"></path><path d="M725 579v5s-1 1-1 2c-2 1-8 5-8 7h-3c1 0 1-1 2-2v-1c2-1 3-2 4-4 2-2 4-4 6-7z" class="k"></path><path d="M732 536l5-16 1 3h-1 1v3l-1 1v1c0 1 0 2-1 3l1 1h0 0l-1 4-2 7h0c-1-1-1-1 0-2l1-5v-1-1c1-1 1-2 1-3l-1-1c0 2-1 4-2 6h-1z" class="P"></path><path d="M724 252c-1-4-1-9 0-13 2 4 5 9 6 13-1 1-2 1-3 2v-1-1c1 1 1 1 1 0h1l-1-1c-2 0-3 0-4 1z" class="W"></path><path d="M692 192c2-1 3-2 5-3 2-2 4-2 7-3l3 2c-4 1-7 2-10 4-4 2-6 5-9 8 1-3 3-6 4-8z" class="o"></path><path d="M726 419l2-2c4 7 6 15 8 22v1h-1c0-2-1-4-2-6s-2-3-2-4c-2-4-3-7-5-11z" class="g"></path><path d="M653 457l2-4c0-2 1-2 2-4l1 1c0 1-1 4 0 6h-1v1c1 0 2 1 2 0l1 1c-1 0-2 1-3 1h0l-1 2h0l1 1c1 0 2 0 3 1h2v1h-1-1c-1-1-1 0-2-1-1 0-2-1-3-1v1h0v1c-1-1-1-2-3-2 0-1 1-3 1-4v-1z" class="o"></path><path d="M653 457c2 0 3 0 3 1s-1 2-1 3v1 1h0v1c-1-1-1-2-3-2 0-1 1-3 1-4v-1z" class="W"></path><path d="M603 525v1c0 3 0 5-1 8 0 2-1 3-1 5l-2 2c0 2-1 3-2 4h-1 0c1-5 2-10 4-14 1-2 1-4 3-6z" class="AC"></path><path d="M741 318h1c2 3 3 10 4 14 1 5 1 10 2 14 1 3 2 7 2 10h-1c-1-5-3-9-4-14v-9l-4-15z" class="C"></path><path d="M728 406c2-1 3-2 5-3 1 0 1 1 1 2l1 1-1 1c-2 1-3 3-4 5v2 1c-2-1-3-3-5-4-1 0-1 0-2-1v-2h1c2 0 3 0 4-2z" class="Z"></path><path d="M618 487h1l-3 7v2c-1 0-2 2-1 2 0 1 1 1 1 3-1 1-1 2-1 3l1 1c-1 1-1 0-1 1 0 4-2 7-3 10v3 1h-1v1c0 1 0 0-1 1v1 1c1 1 0 1 0 2v1h-1c0-2 0-3 1-5l1-3c0-2 0-3 1-4 0-1 1-3 0-4 0-1-1-1-1-3 0-1 1-2 1-4 0-1 0-2 1-3l3-9 2-5z" class="X"></path><path d="M704 186h8l1 1 7 4-1 1-1-1-1 1c1 2 4 4 4 6h0-1v1c1 1 1 4 2 5v2c-2-4-4-9-6-13-3-3-5-5-9-5l-3-2z" class="W"></path><path d="M678 386c0 1-1 2-1 3-1 1-2 2-2 3v1c-1 0-1 0-1 2h0 0l1-1c2-1 3-3 4-4s2-1 2-2c1-1 1-2 2-2h0c0-2 2-3 3-5l3-3 1 1c1-1 2-2 2-3l2 3-2 2-1-1v1c-1 2-2 3-3 4h-1l1-2h-3c0 2 0 2-1 3l-1 1-1 3c-1 1-2 2-3 2-2 1-4 6-7 5v-2l6-9z" class="c"></path><path d="M724 531c-1 1-1 1-2 1h0c-1 1-2 1-3 2l1-1c0-2 2-5 3-7v-2l3-1h0c-1 2-1 3-3 4v1c1-1 2-1 2-2 1-1 2-1 3-1h1c-1 2-2 2-3 3l-1 1h1l1-1h1v4c0 1-2 2-2 4h0l-1-1c-2 1-3 2-4 3l-2 1v-1l2-2-1-1c2-1 3-2 4-2v-2z" class="U"></path><path d="M724 531l2-1c0 1 1 1 1 2-1 1-1 1-3 1v-2z" class="Z"></path><path d="M734 354h0c0-1-1-1-1-2-1-1-1-1-1-2-1-3-3-6-2-9h0c-1-2-2-3-2-5s-1-5 0-7h1c1 3 2 5 2 8 2 4 4 9 4 13 0 1-1 2-1 3 0 0 1 0 0 1z" class="AJ"></path><path d="M715 405c-1 0-1-1-1-1 1-2 2-4 3-5s2-1 3-1c0 3 1 4 3 6 1 1 0 2 1 3h1v-2l1-2 1 1v2h1c-1 2-2 2-4 2h-1v2h-1c-2 0-2 0-3-1l-1-1c-2-1-2-1-3-3z" class="U"></path><path d="M715 405h0c1-1 1-2 2-3l1 1c2 1 3 2 3 4l1 1 1-1v1 2h-1c-2 0-2 0-3-1l-1-1c-2-1-2-1-3-3z" class="X"></path><path d="M715 405h0c1-1 1-2 2-3l1 1c-1 1-1 1 0 3s2 3 4 4c-2 0-2 0-3-1l-1-1c-2-1-2-1-3-3z" class="C"></path><path d="M701 596c3 0 4 0 7-1l1-1v1h2c-2 1-4 2-5 4-1 1-2 1-3 1h-2l-1 1s-1 1-2 1l-12-3c2-2 4-2 7-2s5 0 8-1z" class="k"></path><path d="M739 552l1 1v1l-3 11c-2 4-4 8-7 12 1 0 0 0 1 1 0 0 0 1-1 1l-3-2-1-1 1-2c3-3 5-7 6-10 3-3 4-8 6-12z" class="o"></path><path d="M598 549h4c1-1 2-1 4-1l-2 3-3 2-6 3c-2 1-3 2-5 2v-1l1-1c0-1 0-1-1-1l-1-1-1-2 1-1 4-1 5-1z" class="B"></path><path d="M598 549h4c1-1 2-1 4-1l-2 3-3 2c-2-1-3-1-5-1h0c-2 0-2 0-3-2l5-1z" class="G"></path><path d="M705 336c1 0 2-1 3-1-3 9-10 17-15 26-2 2-4 8-6 10l-1-2-1 1c0-1 1-2 1-3 1-1 2-3 3-4 2-4 5-7 7-11 1-2 2-4 3-5 3-4 4-7 6-11h0z" class="O"></path><path d="M721 356c6 4 9 7 9 14v3c1 6 1 10 3 15v2h-1l-1-1c0-1 0-2-1-3v-1-1s0-1-1-2h0c-1-1-1-2-1-3-1-2-1-3-1-5-2-3 1-7-1-11-1-2-3-3-5-4h3c-2-1-2-2-3-3z" class="b"></path><path d="M715 534c2-7 8-12 12-18 1-1 2-2 4-3-1 2-2 3-2 5-1 2-2 3-3 5l-3 1v2c-1 2-3 5-3 7l-1 1c1-1 2-1 3-2h0c1 0 1 0 2-1v2c-1 0-2 1-4 2l-3 4c0-3 0-3 2-5l-1-1c-1 0-2 3-2 3l-1 1v-2-1z" class="N"></path><path d="M714 480c1-3 0-5 0-7 1-1 1-2 1-3l-1-28 1-1 1 1c1 2 1 9 1 11v1c0 4-1 9-1 13v1c0 2 0 2 1 4h0v2 1l-1 1c1-1 1-1 2-1v1c0 1 0 1-1 2-1 2-2 4-2 6-1-1-1-3-1-4z" class="d"></path><path d="M660 587l13 5 9 3c4 1 7 1 11 2-3 0-5 0-7 2-3 0-7-1-9-2-4-2-7-3-10-4s-6-3-9-4c0-1 1-1 2-2z" class="e"></path><path d="M673 592l9 3h-2c-1 1-2 1-4 0-2 0-2-1-3-3z" class="F"></path><path d="M724 471l1-1v-1c1 0 2 1 3 2v1l-2 6v2l-1-1c-3 0-3 3-5 4-1 0-2 1-3 2 0 1 0 0-1 1v-1h-2v-5c0 1 0 3 1 4 0-2 1-4 2-6 1-1 1-1 1-2v-1c-1 0-1 0-2 1l1-1v-1-2c1 1 1 1 2 1 0 1 0 2 1 2h1c0-1 1-2 1-4v-1c1 0 1 0 2 1z" class="Z"></path><path d="M724 471l1-1v-1c1 0 2 1 3 2v1l-2 6h-1s-1-1-2-1c1-1 1-1 1-2h-2c1-1 1-2 2-4h0z" class="d"></path><defs><linearGradient id="o" x1="741.482" y1="361.699" x2="748.518" y2="356.801" xlink:href="#B"><stop offset="0" stop-color="#332820"></stop><stop offset="1" stop-color="#4b4138"></stop></linearGradient></defs><path fill="url(#o)" d="M732 318l9 24 6 20c2 6 4 12 4 19 0-3-2-7-3-11l-12-36c-2-5-4-11-4-16z"></path><path d="M666 399v2l1 2-2 3-2 2c-2 2-6 5-6 6l1 2-4 3-2 2c-3-1-6 4-11 3 4-4 9-8 13-12s8-8 12-13z" class="F"></path><path d="M732 536h1c1-2 2-4 2-6l1 1c0 1 0 2-1 3v1 1l-1 5c-1 1-1 1 0 2h0c0 2 0 3-1 4 0 2-1 4-2 6-1 4-2 7-5 10h-1c1-2 1-3 2-5h-2c0-1 0-1 1-2v-2c1-2 1-2 2-3v-1l-1-2c1-2 1-4 2-7l1-2c1-1 2-2 2-3z" class="X"></path><path d="M728 550l-1-2c1-2 1-4 2-7l1-2c1 3 0 4 0 7l-1 1v1c0 1 0 1-1 2zm0 1l1-1c0-1 0-1 1-1v4c-1 2-2 4-3 5h-2c0-1 0-1 1-2v-2c1-2 1-2 2-3z" class="C"></path><path d="M672 395v2c3 1 5-4 7-5 1 0 2-1 3-2l1-3 1-1c1-1 1-1 1-3h3l-1 2h1l-1 1c-2 1-4 4-6 6v1s-2 2-3 2l1 1c-1 2-3 3-2 6h-1l-1 2v1c-2 1-4 0-5 1-2 1-3 3-5 4l-1-1h-1 0l2-2v-1l2-3c1-1 1-2 2-3v-1l1-1h0c1-2 1-2 2-3z" class="W"></path><path d="M678 395l1 1c-1 2-3 3-2 6h-1l-1 2v1c-2 1-4 0-5 1-2 1-3 3-5 4l-1-1c5-5 10-9 14-14z" class="H"></path><path d="M679 401c0 1 1 1-1 2v1c-1 0-1 1-1 2l-4 4-10 9h0c1-2 2-3 3-5 1 0 0 0 1-1-4 3-8 6-13 6l4-3-1-2c0-1 4-4 6-6l2-2v1l-2 2h0 1l1 1c2-1 3-3 5-4 1-1 3 0 5-1h0l4-4z" class="L"></path><path d="M679 401c0 1 1 1-1 2v1c-1 0-1 1-1 2l-4 4v-2c-1 0-3 2-4 2v-1l-1-1v1c-1 1-3 1-4 2-2 2-4 3-6 5l-1-2c0-1 4-4 6-6l2-2v1l-2 2h0 1l1 1c2-1 3-3 5-4 1-1 3 0 5-1h0l4-4z" class="j"></path><path d="M652 462c2 0 2 1 3 2v-1h0v-1c1 0 2 1 3 1 1 1 1 0 2 1l-1 2v1h0l-1 1v3l-1 1v2h-1c0 1-1 2-1 2l1-1 1 2v1l-1 1c0 1 0 1-1 2h-1v2l-1 1h-1-1 0-2c1-2 1-4 1-6l1-8v-3c0-1 1-3 1-5z" class="K"></path><path d="M652 462c2 0 2 1 3 2v-1h0v-1c1 0 2 1 3 1 1 1 1 0 2 1l-1 2v1h0l-1 1v3l-1 1v2h-1c0 1-1 2-1 2l-1-1 2-1v-2c1-1 1-3 0-4 0-1-1-1-1-2l1-1 1-1c-2 1-2 1-4 0h0c0 1-1 2-2 3 0-1 1-3 1-5z" class="I"></path><path d="M719 473h0c2-4 3-8 5-13v-2c2-1 1-7 1-9h1c-1 1-1 4 0 5v1-4c1 0 1 1 2 1l1 1h1c-1-1-1-2-1-2v-1c1 1 1 2 2 3 1 2 4 5 5 7-1 2 0 2 0 4h-1c-1 1-1 2-1 3l-2-10h-1c0 1-1 1-2 1v4l1 2h-1c-1 3 0 5-1 8v-1c-1-1-2-2-3-2v1l-1 1c-1-1-1-1-2-1v1c0 2-1 3-1 4h-1c-1 0-1-1-1-2z" class="U"></path><path d="M726 455v-4c1 0 1 1 2 1l1 1-1 1v3c0 1-1 2 0 3-1 1-2 1-3 0 0-1 1-1 1-2v-3z" class="Z"></path><path d="M688 283l2 2-15 69v-1h-1c-1-1 1-6 2-8l3-16v-2l9-43v-1z" class="J"></path><path d="M663 482l1-1h0c1-2 2-7 3-9v-2c0-1 1-2 1-3v-1-2h0c3-5 4-12 6-17 1-3 1-6 3-8h1c1 2 0 3 0 4l-2 10c-1 2-1 3-1 5-1 3-1 7-1 11v1l-2 2h-2l-1-1c0-1 0 0-1-1 0 1 0 1-1 2v2c1 1 2 2 2 4l2 1c-3 1-4 1-6 3h-1-1z" class="I"></path><path d="M667 474c1 1 2 2 2 4l2 1c-3 1-4 1-6 3 0-3 1-6 2-8z" class="F"></path><path d="M706 381l1-6c1 1 2 3 3 4s1 2 3 2h0 1l6 8-5 4c-3 1-4 2-6 4h0v-3-1c-2-2-4-4-6-5l-2-2-2-2h2c1 1 1 0 3 1h1c1-1 1-2 1-4z" class="AG"></path><path d="M705 385l2 1c1-2 0-3 1-3h2v1c1 1 1 3 2 4v1c1 1 2 3 3 4-3 1-4 2-6 4h0v-3-1c-2-2-4-4-6-5l-2-2-2-2h2c1 1 1 0 3 1h1z" class="AA"></path><path d="M710 393v-3l1-1 1 2c0 1 0 1-1 2h-1z" class="AE"></path><path d="M703 388c1-1 2-2 4-2 1 1 2 2 4 3l-1 1v3h-1c-2-2-4-4-6-5z" class="f"></path><path d="M730 262c0-1 0-2 2-3 1 2 0 5 0 7h0c0 1-1 2-1 2v1l1 1 2-1v-2l2-2v2c0 2-1 3-1 5 2-2 3-4 4-6h1 1c0 3-1 6-3 8l-1-1c-2 0-6 8-7 10l-2-1h0v-2c-1-1-1-1-1-2v-4l-1-1c-1 2-2 4-2 5l-2-1 1-5v-1l4-1v-3h0c1 0 1-1 1-2l1-1c1-1 1-1 1-2z" class="L"></path><path d="M723 271l4-1v-3h0c2 3 2 5 2 9h1l1-1c0 1 0 1-1 2s-1 3-2 5h0 0v-2c-1-1-1-1-1-2v-4l-1-1c-1 2-2 4-2 5l-2-1 1-5v-1z" class="E"></path><path d="M723 272h2c1-1 0-2 2-1 0 1 0 1-1 1v1h0 0c-1 2-2 4-2 5l-2-1 1-5z" class="i"></path><path d="M601 485v-1c1-1 1-2 2-3l1-1v-1 4h-1v1 1 1c0 1-1 1-1 2h1c-1 3-3 7-3 11l-13 46-2 1 1-3 1-6c0-2 1-5 2-7s1-3 1-6l9-34 1-2 1-3z" class="t"></path><defs><linearGradient id="p" x1="731.976" y1="500.224" x2="752.644" y2="492.419" xlink:href="#B"><stop offset="0" stop-color="#373837"></stop><stop offset="1" stop-color="#575255"></stop></linearGradient></defs><path fill="url(#p)" d="M744 479v-4c1-3 1-7 2-10h1c0 15-2 29-4 43-2 8-3 16-6 24h0 0l-1-1c1-1 1-2 1-3v-1l1-1v-3h-1 1l-1-3c1-3 2-8 2-12l4-27 1-2z"></path><path d="M689 358l1 1c0 1-1 2-1 3h-1c0 1-1 2-1 3-2 2-3 4-4 7-2 1-5 4-5 6l1 1v2c1 0 1 0 2 1h-2-1c0-1 0 0-1-1l-4 4c-9 10-21 20-33 28 4-5 9-9 14-14l15-13 6-9 14-19z" class="O"></path><path d="M702 330c0-2 1-4 2-6 1 3 1 4 0 6v3c-1 1-1 0-1 1 2-2 4-6 6-8v1l-4 9h0c-2 4-3 7-6 11-1 1-2 3-3 5-2 4-5 7-7 11-1 1-2 3-3 4 0 1-1 2-1 3-1 3-4 7-6 9l-1-1c0-2 3-5 5-6 1-3 2-5 4-7 0-1 1-2 1-3h1c0-1 1-2 1-3l-1-1c0-1 2-2 2-4 1-1 2-3 3-5l8-19z" class="W"></path><path d="M702 330c0-2 1-4 2-6 1 3 1 4 0 6v3c-1 1-1 0-1 1 2-2 4-6 6-8v1l-4 9h0v-2l-1 1c0 1-1 2-1 3l-1 1c-1 2-2 3-3 5l3-9c-1-2-1-3 0-5z" class="I"></path><path d="M714 485h2v1c1-1 1 0 1-1 1-1 2-2 3-2 2-1 2-4 5-4l1 1c0 1-1 2-2 3v1c-1 2-1 3 0 5 0 1 0 2-1 2 0 2-1 3-1 4-1 2-1 3-2 4l-3 6v1h0-4v-6l1-14v-1z" class="U"></path><path d="M717 505h-1-1c2-2 2-3 3-5h0c0-1-1-2-1-3 1-2 1-2 3-4l1 1c-1 1-1 2-1 4v1l-3 6z" class="d"></path><path d="M714 486h1v1l1 1v1c0 1-1 2 0 4 0 1-1 2-1 4v3c0 1 0 3-1 4h0l-1-4 1-14z" class="b"></path><path d="M719 488c0-2 1-4 3-5 1-1 1 0 2 0v1c-1 2-1 3 0 5 0 1 0 2-1 2v1h-1-1c-3 0-2-1-3-3l1-1z" class="d"></path><path d="M719 488c0-2 1-4 3-5 1-1 1 0 2 0v1h-2l1 2-2 2h-2z" class="N"></path><defs><linearGradient id="q" x1="734.697" y1="414.5" x2="754.303" y2="419" xlink:href="#B"><stop offset="0" stop-color="#271b1f"></stop><stop offset="1" stop-color="#585d57"></stop></linearGradient></defs><path fill="url(#q)" d="M742 381l1-1c1 1 1 3 2 4 2 18 3 37 3 55 0 9 0 18-1 26h-1v-15l-4-69z"></path><path d="M726 273l1 1v4c0 1 0 1 1 2v2l-3 6c-1 3-3 6-5 9-1 2-1 4-3 5-1 5-4 10-7 13h-1v-3l11-30 2-5 2 1c0-1 1-3 2-5z" class="e"></path><path d="M726 273l1 1v4c0 1 0 1 1 2v2l-3 6c-1 3-3 6-5 9-1 2-1 4-3 5l1-1c0-1 0-2 1-3 0-4 3-8 4-13 1-1 1-2 1-3-1 1-2 1-3 0h-1l2-5 2 1c0-1 1-3 2-5z" class="D"></path><path d="M725 288c0-4 0-7 2-10 0 1 0 1 1 2v2l-3 6z" class="B"></path><path d="M648 455l2-1h1c0 1 1 2 0 4 0 0 0 1-1 1h2l1-1c0 1-1 3-1 4 0 2-1 4-1 5v3l-1 8c-1-1-2-3-3-4l1-2-1-2h-1l-2 3-1 1c0 1-1 2-2 2l-2 1-1-1-1 1v-2-4l-1-2c0-2 0-4 2-6l1-1h1v4h1l1 1c1-4 3-9 6-12h0z" class="S"></path><path d="M638 463l1 1c0 2-1 5-2 7l-1-2c0-2 0-4 2-6z" class="F"></path><path d="M638 476c1-2 3-4 5-5l1 2-1 1c0 1-1 2-2 2l-2 1-1-1z" class="I"></path><path d="M648 455l2-1h1c0 1 1 2 0 4 0 0 0 1-1 1h2l1-1c0 1-1 3-1 4 0 2-1 4-1 5v3c-1 0-1 0-2-1 0-1 0-2-1-3h0v-2c1-2 1-6 1-8 0 0 1 0 1-1h0-2z" class="n"></path><defs><linearGradient id="r" x1="707.846" y1="364.579" x2="692.697" y2="370.853" xlink:href="#B"><stop offset="0" stop-color="#200201"></stop><stop offset="1" stop-color="#480706"></stop></linearGradient></defs><path fill="url(#r)" d="M701 358l3-6c1 4 2 7 2 11 1 0 1 2 1 3h-1v2 11 2c0 2 0 3-1 4h-1c-2-1-2 0-3-1h-2c-2 0-5-4-5-5l-2-3 1-1 5-12c1-1 2-3 3-5z"></path><path d="M701 358l1 1c0 2 0 3-2 4v1l-1-1v1l-1-1c1-1 2-3 3-5z" class="W"></path><path d="M703 380v-2c0-1-1-1-2-2 1-1 0-1 2-2v2l2-1c0-3 0-5 1-7v11c-1 0-2 0-3 1z" class="T"></path><path d="M693 375c1 0 2 1 3 2 0 2 0 3 1 4l1 1 3 2h-2c-2 0-5-4-5-5l-2-3 1-1z" class="j"></path><path d="M703 380c1-1 2-1 3-1v2c0 2 0 3-1 4h-1c-2-1-2 0-3-1l-3-2c1 0 1-1 2-1h1l2-1z" class="Q"></path><path d="M624 490l1-2c1-2 3-4 4-5v1c-1 1 0 2-1 3-3 4-2 8-5 12h0c0 2-1 4-2 6s-1 4-2 6 0 3-1 5l1 5c0 3 1 7 2 10-1 1-1 3-1 5l-1-3-1 2-1-2-2-1-1 3-1 4c-2 4-4 6-7 9-2 0-3 0-4 1h-4 0l1-1c4-2 6-5 9-8v-1c0-1 0-2 1-2 0-1 1-2 1-3h0c1-1 1-2 1-2 1-2 2-3 2-5v-1c1-1 1-2 1-2v-2c0-1 1-2 1-2v-2-1c1-1 1-1 1-2v-2-1h0c0-1 0-2 1-3 0-2 0-4 1-5 2-5 4-10 6-14z" class="j"></path><path d="M614 526c1-4 2-9 4-13v3l1 5c0 3 1 7 2 10-1 1-1 3-1 5l-1-3-1 2-1-2-2-1c1-1 1-2 1-2v-4h-1-1z" class="AA"></path><path d="M616 530c0-3 1-6 2-8 0 4 0 7 1 11l-1 2-1-2-2-1c1-1 1-2 1-2z" class="K"></path><path d="M614 526h1 1v4s0 1-1 2l-1 3-1 4c-2 4-4 6-7 9-2 0-3 0-4 1h-4 0l1-1c4-2 6-5 9-8 3-4 5-9 6-14z" class="M"></path><path d="M736 460c3 4 1 8 1 13-1 6-3 12-5 19-1 4-2 8-4 12-3 6-8 11-11 16-4 6-7 14-10 21 1-4 2-14 3-16l1-7 2-1c2-5 8-8 11-12 1-1 2-3 2-5 0 0 2-5 2-6 1-4 2-7 3-11l3-16c0-1 0-2 1-3h1c0-2-1-2 0-4z" class="P"></path><path d="M711 518l2-1h0v5c-1 1-1 2-2 3h-1l1-7z" class="b"></path><path d="M730 373c1 1 1 2 2 3v-1c2 1 2-1 4 1 0 1 0 1 1 3h0c4 8 1 19 3 28l-1-2c-1-1-1-1-1-2l-2-1v1l1 2c-1-1-2-1-2-2v1 2l-1-1c0-1 0-2-1-2-2 1-3 2-5 3h-1v-2l-1-1 1-1c-1 0-1 0-1-1-1-1-4-6-3-8h0c-1-1-1-2-1-3l2 2v1l3 3 1-1v-3c-1-1-1-3-1-4 0-2-2-3-2-5v-1h-1c0-2-1-4 0-6v1h1v-1l2-2c0 2 0 3 1 5 0 1 0 2 1 3h0c1 1 1 2 1 2v1 1c1 1 1 2 1 3l1 1h1v-2c-2-5-2-9-3-15z" class="Z"></path><path d="M730 373c1 1 1 2 2 3v-1c2 1 2-1 4 1 0 1 0 1 1 3-1 0-2 0-3 2h1 1l1 1v1l-2-1-1 1 1 1-1 1 1 1-1 2h0 0c1-1 1 0 2 0 0 1 1 1 1 2h-1-1-2v-2c-2-5-2-9-3-15z" class="U"></path><path d="M727 402c1 1 2 1 4 0h1 1v-1h0l-2-2h0v-3l-1-2h0v-3c1 1 1 1 1 2l1 1v3l1-1 2 2c2 0 2 0 3 1 0 1 0 1-1 2 0 1 0 1 1 2h0l-2-1v1l1 2c-1-1-2-1-2-2v1 2l-1-1c0-1 0-2-1-2-2 1-3 2-5 3h-1v-2l-1-1 1-1z" class="C"></path><defs><linearGradient id="s" x1="659.711" y1="446.907" x2="656.942" y2="441.567" xlink:href="#B"><stop offset="0" stop-color="#770808"></stop><stop offset="1" stop-color="#a61315"></stop></linearGradient></defs><path fill="url(#s)" d="M668 431c2-1 4-2 7-3v3c1 1 1 1 2 1h1 0 3c3-1 7-1 10-1l-1 1s0 1-1 1l-14 3c-15 6-30 17-42 27v-1l-1-3 8-9c3-5 9-9 13-12l1 1h1 1c1-1 1-1 2-1 3-1 7-4 10-7z"></path><path d="M640 450h3c-2 3-4 5-7 7-2 1-3 2-3 5l-1-3 8-9z" class="F"></path><path d="M668 431c2-1 4-2 7-3v3c1 1 1 1 2 1-2 1-5 2-7 2-3 2-6 4-9 5-1 0-2 0-2 1-1-1-1-1-1-2 3-1 7-4 10-7z" class="K"></path><path d="M640 450c3-5 9-9 13-12l1 1h1 1c1-1 1-1 2-1 0 1 0 1 1 2-5 4-11 6-16 10h-3z" class="J"></path><path d="M694 452c1 3 4 2 7 4l1 1c5 7 7 16 7 25v2c2 3 0 6 0 9v4h0c-2 2-2 5-2 7 0-1 0-3-1-4-2-6-1-14-5-19h-1c-1-1-1-1-1-2-1-1-1-2-2-3 0-1 0-3-1-4 0-2-1-3-1-5-1-4-1-9-1-13v-2z" class="n"></path><path d="M730 283c1-2 5-10 7-10l1 1-9 18c-1 3-3 5-3 8-1 3-3 5-4 8l-11 22c-1 2-2 3-3 5-1 0-2 1-3 1l4-9v-1c-2 2-4 6-6 8 0-1 0 0 1-1v-3c1-2 1-3 0-6l5-12v3h1c3-3 6-8 7-13 2-1 2-3 3-5 2-3 4-6 5-9l3-6h0l2 1z" class="p"></path><path d="M728 282h0l2 1c-7 11-11 23-17 34-1 3-2 7-4 10v-1c-2 2-4 6-6 8 0-1 0 0 1-1v-3c1-2 1-3 0-6l5-12v3h1c3-3 6-8 7-13 2-1 2-3 3-5 2-3 4-6 5-9l3-6z" class="H"></path><defs><linearGradient id="t" x1="689.292" y1="451.584" x2="674.4" y2="476.941" xlink:href="#B"><stop offset="0" stop-color="#1b0604"></stop><stop offset="1" stop-color="#470a08"></stop></linearGradient></defs><path fill="url(#t)" d="M680 451h10 1 1l2 1v2c-1 2-2 4-2 7l-2 1c0 3-2 7-3 10l-2-1c0 2-1 4-1 6v1h-5c-1 0-4 0-6-1v-1l1-2v-4-1c0-4 0-8 1-11 0-2 0-3 1-5v1l1-2v1c1-2 2-2 3-2z"></path><path d="M692 451l2 1v2c-1 2-2 4-2 7l-2 1c0 3-2 7-3 10l-2-1 6-20h1z" class="j"></path><path d="M676 453v1l1-2v1c1-2 2-2 3-2h0v5c0 1 0 3-1 4-1 2-3 3-3 5-1 1-1 3-2 4h0c0-4 0-8 1-11 0-2 0-3 1-5z" class="X"></path><defs><linearGradient id="u" x1="635.056" y1="348.713" x2="646.361" y2="352.887" xlink:href="#B"><stop offset="0" stop-color="#67605a"></stop><stop offset="1" stop-color="#817b71"></stop></linearGradient></defs><path fill="url(#u)" d="M659 282l1-1c1-1 1-2 2-4 0 0 1-1 1-2l1 1h-1c0 1 0 1-1 2v2 2l-34 115c0 3-7 28-8 30v-1l-1 1h0v-4h0c0-1 2-12 3-14h0l5-15c0-3 1-5 1-7 2-5 3-11 5-16 1-6 4-12 5-17l1-4 20-68z"></path><path d="M694 379c0 1 3 5 5 5l2 2-1 2c-1 0-2 0-3 1-2 1-3 3-3 5l-1-1-2 2v1c1 2 0 2 2 2h0v4l-4 1c-1 1-1 2-2 3 0 2 0 3 1 4l-2 1h-3c-1 1-2 1-3 2h-1c-3 4-7 6-11 10v-2-1c0-2 1-3 2-4 2-2 7-7 7-9v-1c0-1 0-2 1-2v-1c2-1 1-1 1-2l-4 4h0v-1l1-2h1c-1-3 1-4 2-6l-1-1c1 0 3-2 3-2v-1c2-2 4-5 6-6l1-1c1-1 2-2 3-4v-1l1 1 2-2z" class="E"></path><path d="M681 393h2c0 1 0 0-1 1v1l-5 7c-1-3 1-4 2-6l-1-1c1 0 3-2 3-2z" class="V"></path><path d="M687 386h3 0c-2 3-5 7-8 9v-1c1-1 1 0 1-1h-2v-1c2-2 4-5 6-6z" class="I"></path><path d="M688 385c1-1 2-2 3-4v-1l1 1c1 1 1 2 1 3v1 3c0 1 0 1 1 1-1 2-1 3-1 4l-2 2c0-1 0-2-1-3-1 1-1 2-2 3s-1 1-2 1c1-3 3-6 5-9 1-1 1-1 1-2h-1l-1 1h0-3l1-1z" class="L"></path><path d="M694 379c0 1 3 5 5 5l2 2-1 2c-1 0-2 0-3 1-2 1-3 3-3 5l-1-1c0-1 0-2 1-4-1 0-1 0-1-1v-3-1c0-1 0-2-1-3l2-2z" class="AC"></path><path d="M688 395c1-1 1-2 2-3 1 1 1 2 1 3v1c1 2 0 2 2 2h0v4l-4 1-1-1v1l-3 3-2-1-1 1h-1c1-2 4-6 4-8-1 1-1 1-2 1l3-3c1 0 1 0 2-1z" class="Q"></path><path d="M691 396c1 2 0 2 2 2h0c-1 1-1 2-2 3h-1l-1-2 2-3z" class="R"></path><path d="M688 395c1-1 1-2 2-3 1 1 1 2 1 3v1l-2 3h-1v-4z" class="V"></path><path d="M679 401c2-1 2-2 4-2 1 0 1 0 2-1 0 2-3 6-4 8h1l1-1 2 1 3-3v-1l1 1c-1 1-1 2-2 3 0 2 0 3 1 4l-2 1h-3c-1 1-2 1-3 2h-1c-3 4-7 6-11 10v-2-1c0-2 1-3 2-4 2-2 7-7 7-9v-1c0-1 0-2 1-2v-1c2-1 1-1 1-2z" class="K"></path><path d="M681 406h1l1-1 2 1 3-3v-1l1 1c-1 1-1 2-2 3 0 2 0 3 1 4l-2 1h-3c-1 1-2 1-3 2h-1c-3 4-7 6-11 10v-2l6-6c3-2 7-5 9-8h-1-1-1l1-1z" class="O"></path><path d="M683 411v-1c0-1 1-1 2-2h1l-1-2h1 1c0 2 0 3 1 4l-2 1h-3z" class="J"></path><path d="M636 469l1 2v4 2l1-1 1 1c0 1 0 1-1 2s-1 2-1 2c-3 8-4 17-6 25 0 3 0 6-1 9-1 2-2 3-2 5l-1 2-3 9c-1 0-1 1-2 2l-1-2c-1-3-2-7-2-10l-1-5c1-2 0-3 1-5s1-4 2-6 2-4 2-6h0c3-4 2-8 5-12 1-1 0-2 1-3v4 1l4-7c1-2 1-4 1-7 1-2 2-4 2-6z" class="O"></path><path d="M618 516c1-2 0-3 1-5s1-4 2-6 2-4 2-6h0c3-4 2-8 5-12 1-1 0-2 1-3v4 1c0 2-1 5-2 7 0 1-1 1-1 2-3 5-5 16-5 22v1l-1-1v-1c1-2 0-3 1-5 0-1 0-3 1-4 0-2-1-3 0-4v-1c-2 4-4 8-3 12 1 2 0 3 0 4l-1-5z" class="T"></path><path d="M737 366c1-2 0-5 1-7v-3c-1-1-1-2 0-2h0c1 1 1 3 1 5 0 1 0 3 1 5l2 17 4 69-2 7c0-1 0-1-1-2h0c1-1 1-3 1-4l-1 1-1-1s2-22 0-27h-1v1c-1 2 0 6-1 9l-1-1v-1-2 1c-2-2-2 0-4 0l1-1v-4 1l-1-1c-1 0-1-1-1-1 0-1 1-1 1-2h-1l-1-1-1-1v-1-1c1-3 2-5 4-7 1-1 1-2 2-3 0-2 0-3-1-4l-1-2v-1l2 1c0 1 0 1 1 2l1 2c-2-9 1-20-3-28v-3c0-3-1-8 0-10z" class="AJ"></path><path d="M737 405l-1-2v-1l2 1c0 1 0 1 1 2l1 2v8c-2-1-3-2-4-3 1-1 1-2 2-3 0-2 0-3-1-4z" class="P"></path><path d="M732 419c1-3 2-5 4-7 1 1 2 2 4 3-1 1-3 7-4 7-1 1-1 1-2 1l-2-4z" class="N"></path><path d="M715 534v1 2l1-1s1-3 2-3l1 1c-2 2-2 2-2 5l3-4 1 1-2 2v1l2-1c1-1 2-2 4-3l1 1h0s0 1-1 2v4l1 1v4l-1 1v2h-2 0c-1 1-2 3-3 3v2 1l-2 4c0 1-1 2-1 3s0 2-1 3v-3l-1 1h1v1c-1 2-1 3-1 5 1 1 0 1 0 2-1 1-1 3-2 5v1c-1 1-2 3-3 4h0s1-1 1-2h0-4v-2h0l-1-1 1-1v-1h-1v-1c-1-4 0-7 1-10l1-1v-5h0c-1-3 1-6 0-9 0-2 2-5 2-7l5-8z" class="C"></path><path d="M711 580c0-2 0-3 1-4s1-1 1-2h-1l-2 2c0 2 0 2-1 4l-1-1c0-1 1-1 1-2 0-2 2-2 2-4-1 0-1 0-2-1 1 0 2-1 2-2h0c0-1 1-1 1-1 0-1 0-2 1-3h0c-1-2-1-1-2-1 0-1 1-1 2-2l3 1v1c-1 2-1 3-1 5 1 1 0 1 0 2-1 1-1 3-2 5v1c-1 1-2 3-3 4h0s1-1 1-2z" class="Y"></path><path d="M715 534v1 2l1-1s1-3 2-3l1 1c-2 2-2 2-2 5-2 3-4 6-6 10-1 2-2 4-2 6 0 1-1 2-1 3-1-3 1-6 0-9 0-2 2-5 2-7l5-8z" class="P"></path><path d="M721 538c1-1 2-2 4-3l1 1h0s0 1-1 2v4l1 1v4l-1 1v2h-2 0c-1 1-2 3-3 3v2 1l-2 4c0-2 0-3-1-4l-3 3v1c-1 1-2 1-3 1v-2c0-3 2-8 4-11 1-2 1-4 3-7h1c0-1 1-1 1-2l1-1z" class="U"></path><path d="M721 538c1-1 2-2 4-3l1 1c-1 1-3 4-3 6v1c1 1 1 2 1 3v1l-1-1c-1 0 0 0-1-1l1-2-1-1-3 4 1-7 1-1z" class="d"></path><path d="M726 536s0 1-1 2v4l1 1v4l-1 1v2h-2l-1-1c0-1 0 0-1-1v-1l2-1 1 1v-1c0-1 0-2-1-3v-1c0-2 2-5 3-6h0z" class="C"></path><path d="M718 541l1 2h-1v2c0 1 0 2-1 3h0l1-1 1 1-1 1c-3 3-2 6-6 9v1l1 1v-1l1-1v1 1c-1 1-2 1-3 1v-2c0-3 2-8 4-11 1-2 1-4 3-7z" class="d"></path><path d="M727 577l3 2c1 2 1 2 2 3 1 0 1 0 2-1 0 0 1 0 2 1l1-1v1h3l-3 3h-1c-1 2-4 4-4 6 0 1 0 1-1 2 0 2-2 4-4 5 0 1-1 1-1 1 0 1 2 1 1 2l-2 1v1c-2 0-4 2-6 3v2h-2c-1 0-1 0-2-1v-1 2l-2-1h-2c-1-1-3-1-4-1-3-1-7-1-10-2-3 0-6-1-9-2l-15-5h4c2 1 6 2 9 2l12 3c1 0 2-1 2-1l1-1h2c1 0 2 0 3-1 1-2 3-3 5-4l2-2h3c0-2 6-6 8-7 0-1 1-2 1-2v-5l1-2h1z" class="m"></path><path d="M725 584l1-3 1-1 1 2c1-1 1 0 2-1v1s0 1-1 1v1c-1 2-2 3-3 4l-2-2c0-1 1-2 1-2z" class="B"></path><path d="M724 586l2 2c-3 3-6 7-11 8 0 1 0 1-1 1h-1c0 1-1 1-2 2 0-2 0-2 1-3h1l3-3c0-2 6-6 8-7z" class="M"></path><path d="M713 593h3l-3 3h-1c-1 1-1 1-1 3 1-1 2-1 2-2h1c1 0 1 0 1-1-2 2-4 4-6 5h-1v1c1 1 1 1 2 1l1-1h2s-1 1-2 1c-1 1-6 0-7-1h-6c1 0 2-1 2-1l1-1h2c1 0 2 0 3-1 1-2 3-3 5-4l2-2z" class="D"></path><path d="M719 597l2-2 6-6c0-1 2-2 2-3 1-1 2-1 3-2l1 1 1-1c0-1 0-1 1-1h0v1 1h1c-1 2-4 4-4 6 0 1 0 1-1 2l-7 6h0l1-1c-1-2 0-1-1-2v-1c-3 1-2 4-5 2z" class="p"></path><path d="M731 593c0 2-2 4-4 5 0 1-1 1-1 1 0 1 2 1 1 2l-2 1v1c-2 0-4 2-6 3v2h-2c-1 0-1 0-2-1v-1 2l-2-1h-2c-1-1-3-1-4-1-3-1-7-1-10-2-3 0-6-1-9-2l-15-5h4c2 1 6 2 9 2l12 3h6c1 1 6 2 7 1 1 0 2-1 2-1l1-1c2-1 3-2 5-4 3 2 2-1 5-2v1c1 1 0 0 1 2l-1 1h0l7-6z" class="f"></path><defs><linearGradient id="v" x1="669.048" y1="280.619" x2="689.196" y2="320.318" xlink:href="#B"><stop offset="0" stop-color="#1c0200"></stop><stop offset="1" stop-color="#480d0c"></stop></linearGradient></defs><path fill="url(#v)" d="M689 270v8c0 1-1 4-1 5v1l-9 43v2l-1 2-8-4h0c-1 0-2 0-2-1 2-9 3-17 7-25l2-8c3-7 6-20 12-23z"></path><path d="M677 293h0c0 1 0 1 1 2v2c-1 1-2 3-3 4l2-8z" class="c"></path><path d="M670 327c0-1 1-1 2-1 0-1 1-1 1-2l1 1c1 0 1 0 2 1 1 0 2 1 3 1v2l-1 2-8-4h0z" class="S"></path><path d="M668 326c0 1 1 1 2 1h0l8 4 1-2c-1 6-2 11-3 16-1 2-3 7-2 8h1v1c-1 6-1 12-4 18-5 9-15 17-23 23l7-23 4-15c0-1 1-1 1-2 0-3 1-6 2-8l6-21z" class="I"></path><path d="M674 353h1v1c-1 6-1 12-4 18l-1-1c-1-3 3-14 4-18z" class="m"></path><defs><linearGradient id="w" x1="686.945" y1="566.317" x2="712.322" y2="572.409" xlink:href="#B"><stop offset="0" stop-color="#380604"></stop><stop offset="1" stop-color="#001313"></stop></linearGradient></defs><path fill="url(#w)" d="M681 591l1-1c1-2 1-5 0-7l1-1v-1c1-1 1-3 1-5 1-2 1-5 2-7s1-4 2-6c1-5 3-9 4-14v-9l2 2c1 1 3 3 5 3l-1-1h1c0 1 1 2 2 3l2-2c1 1 1 2 2 3 0-1 0-1 1-2 0-1 0-1 1-2l1 1c0-1 1-2 2-3 0 2-2 5-2 7 1 3-1 6 0 9h0v5l-1 1c-1 3-2 6-1 10v1h1v1l-1 1 1 1h0v2h4 0c0 1-1 2-1 2-2 4-5 8-9 9l-1 1c-4 1-7 2-12 1l-7-2z"></path><path d="M705 549h0 0l-5 23h0l1-8c1-3 0-5 1-8 0-3 2-5 3-7z" class="N"></path><path d="M701 386l2 2c2 1 4 3 6 5v1 3h0c-2 5-3 7-1 12 3 10 10 17 15 25 4 5 8 11 11 16-6-4-9-10-16-14-3 0-6 0-8-1h-1c-1 0-2 0-3-1-5-1-11-1-17-1 1 0 1-1 1-1l1-1c-3 0-7 0-10 1h-3 0-1c-1 0-1 0-2-1v-3l3-2v-1c1 0 2-1 3-2l1-1s1-1 1-2h1l-1-1 1-1-1-1c-1-1-1-1-1-2 2-1 3-2 4-4l2-1c-1-1-1-2-1-4 1-1 1-2 2-3l4-1v-4h0c-2 0-1 0-2-2v-1l2-2 1 1c0-2 1-4 3-5 1-1 2-1 3-1l1-2z" class="u"></path><path d="M678 426c12-3 23 0 34 6l6 4c-3 0-6 0-8-1h-1c-1 0-2 0-3-1-5-1-11-1-17-1 1 0 1-1 1-1l1-1c-3 0-7 0-10 1h-3 0-1c-1 0-1 0-2-1v-3l3-2z" class="X"></path><path d="M678 432c0-1 1-2 2-2 1-1 2-1 3-2l2 2c7 1 11 0 18 2 1 1 3 1 4 2h-1c-5-1-11-1-17-1 1 0 1-1 1-1l1-1c-3 0-7 0-10 1h-3 0z" class="F"></path><path d="M701 386l2 2c2 1 4 3 6 5v1h-1c0 1-2 2-2 3l-1 6v2c2 6 4 11 6 17-2-2-5-4-6-6l-6-8c-1-2-1-3-3-4-1-2-1-3-2-5v-1h-1c-2 0-1 0-2-2v-1l2-2 1 1c0-2 1-4 3-5 1-1 2-1 3-1l1-2z" class="o"></path><path d="M691 395l2-2 1 1c-1 2-1 2 0 4h-1c-2 0-1 0-2-2v-1z" class="c"></path><path d="M698 392c1 1 1 1 1 2 1 0 1 1 2 2-1 2-1 3-1 5 1 1 1 2 1 3s0 1-1 1c-1-2-2-4-2-7v-6z" class="T"></path><path d="M706 397l-2 3c0 2-1 3-2 4l-1-1c0-3 3-5 3-8v-1l1-1h2 0l1 1c0 1-2 2-2 3z" class="c"></path><path d="M694 398v1c1 2 1 3 2 5 2 1 2 2 3 4l6 8s-1 1-2 1c-1-1 0-2-2-1v1c1 1 1 3 1 4 4 2 7 4 11 7-4-1-6-3-8-4-8-2-16-2-23-2 0 0 1-1 1-2h1l-1-1 1-1-1-1c-1-1-1-1-1-2 2-1 3-2 4-4l2-1c-1-1-1-2-1-4 1-1 1-2 2-3l4-1v-4h0 1z" class="D"></path><path d="M689 403l4-1c0 2 1 4 0 6 0 2-1 2-2 3-1 0-2-1-3-1-1-1-1-2-1-4 1-1 1-2 2-3z" class="C"></path><path d="M696 404c2 1 2 2 3 4h-1v1c0 2 1 5 0 8h-3v1c2 0 2 0 3 2h-5c-1 0-1-1-2-2l1-1c2 0 4-2 5-4-1-2-1-4-2-6h0c0-1 1-1 1-2v-1z" class="F"></path><path d="M698 420c-1-2-1-2-3-2v-1h3c1-3 0-6 0-8v-1h1l6 8s-1 1-2 1c-1-1 0-2-2-1v1c1 1 1 3 1 4-1 0-3-1-4-1z" class="m"></path><path d="M771 389l1-1c1 0 1-1 2-1s1 0 2 1l-1 1v1h-1c0 3-1 7 0 10 0-2 0-5 2-7 0 1 0 1 1 1h1c1-1 0 0 2-1 3 0 4-3 6-4h3 0 2c2 1 1 1 2 3-1 0-1 1-1 2h0c1 0 1 0 1 1-2 1-3 3-5 5l-2 1c-1 0-1 1-2 1-2 1-1 4-3 6l-1 5c-1 0-2-1-2-1l-1 1-2 20h0v-2c-3 4-1 9-2 13s-2 10-2 14l-3 17-7 32c-3 13-9 26-15 39-1 5-3 11-5 15-1 0-1 0-2 1 0 1-1 2-2 3l3-11v-1l-1-1c0-2 1-4 1-6h-1l13-34c2-7 5-14 6-21 5-23 5-47 6-70 0-7-2-12-1-18l2-2 2-4c1-2 2-5 4-8z" class="K"></path><path d="M740 546c1-1 2-3 2-4v-1l1 3-3 10v-1l-1-1c0-2 1-4 1-6z" class="S"></path><path d="M778 394c2 0 1 0 3 1v10 3l-1 5c-1 0-2-1-2-1l-1 1 1-19z" class="AA"></path><path d="M789 389h2c2 1 1 1 2 3-1 0-1 1-1 2h0c1 0 1 0 1 1-2 1-3 3-5 5l-2 1c-1 0-1 1-2 1-2 1-1 4-3 6v-3-10c-2-1-1-1-3-1h0c1-1 0 0 2-1 3 0 4-3 6-4h3 0z" class="Q"></path><path d="M789 389h2c2 1 1 1 2 3-1 0-1 1-1 2h0c1 0 1 0 1 1-2 1-3 3-5 5l-2 1c-1 0-1 1-2 1 1-2 2-3 2-4v-1c1 0 1-1 2-1h1c1-1 1-2 2-3l-1-3-1-1z" class="L"></path><path d="M778 394c1-1 0 0 2-1 3 0 4-3 6-4h3 0l1 1c-1 2-1 3-2 3s-1 0-1-1c-1 1-2 1-2 2-1 1-1 2-1 4-1 1-1 3-2 4l-1 3v-10c-2-1-1-1-3-1h0z" class="E"></path><defs><linearGradient id="x" x1="767.773" y1="465.444" x2="749.57" y2="463.64" xlink:href="#B"><stop offset="0" stop-color="#140402"></stop><stop offset="1" stop-color="#590404"></stop></linearGradient></defs><path fill="url(#x)" d="M771 389l1-1c1 0 1-1 2-1s1 0 2 1l-1 1v1h-1c0 3-1 7 0 10l-6 51c-3 24-8 49-16 72-2 4-3 7-5 10-1 4-2 8-4 11l-1-3v1c0 1-1 3-2 4h-1l13-34c2-7 5-14 6-21 5-23 5-47 6-70 0-7-2-12-1-18l2-2 2-4c1-2 2-5 4-8z"></path><path d="M767 397c1 2 1 3 0 5-1 3-1 7-2 10s0 6-1 9c0-7-2-12-1-18l2-2 2-4z" class="X"></path><defs><linearGradient id="y" x1="622.624" y1="444.955" x2="638.975" y2="461.936" xlink:href="#B"><stop offset="0" stop-color="#090101"></stop><stop offset="1" stop-color="#220302"></stop></linearGradient></defs><path fill="url(#y)" d="M677 406v1c0 2-5 7-7 9-1 1-2 2-2 4v1 2c4-4 8-6 11-10h1c1-1 2-1 3-2h3c-1 2-2 3-4 4 0 1 0 1 1 2l1 1-1 1 1 1h-1c0 1-1 2-1 2l-1 1c-1 1-2 2-3 2v1l-3 2c-3 1-5 2-7 3-3 3-7 6-10 7-1 0-1 0-2 1h-1-1l-1-1c-4 3-10 7-13 12l-8 9 1 3v1l-13 12-1 2c-3 4-6 7-8 10-4 6-6 11-9 17v-2h0l1-1 2-4h-1c-1 1-1 2-2 3 0 1-1 2-1 3-1-2 0-3-1-4 0-4 2-8 3-11l10-35c1 0 2-1 3-2 0-1 2-3 3-4 0-1 0-1 1-1l21-22c5 1 8-4 11-3l2-2c5 0 9-3 13-6-1 1 0 1-1 1-1 2-2 3-3 5h0l10-9 4-4z"></path><path d="M648 437s0 1-1 1c-4 2-9 7-13 11v-1c0-1 2-2 2-3-3 2-7 7-9 8 3-4 6-7 10-10 2-2 5-5 8-5 1 0 2-1 3-2h0v1z" class="X"></path><path d="M620 475v-1c0-2 2-4 3-5l9-10 1 3v1l-13 12z" class="J"></path><path d="M641 424c5 1 8-4 11-3-10 9-21 19-33 26 0-1 0-1 1-1l21-22z" class="D"></path><path d="M677 406v1c0 2-5 7-7 9-1 1-2 2-2 4v1 2c-3 2-5 4-6 7l-1 1v-2c-2 0-3 1-4 2l-4 4-5 2v-1l15-17 10-9 4-4z" class="B"></path><path d="M668 421v2c-3 2-5 4-6 7l-1 1v-2c-2 0-3 1-4 2 3-4 6-8 11-10z" class="V"></path><path d="M683 411h3c-1 2-2 3-4 4 0 1 0 1 1 2l1 1-1 1 1 1h-1c0 1-1 2-1 2l-1 1c-1 1-2 2-3 2v1l-3 2c-3 1-5 2-7 3-3 3-7 6-10 7-1 0-1 0-2 1h-1-1l-1-1 2-2v-1h-2l4-4c1-1 2-2 4-2v2l1-1c1-3 3-5 6-7 4-4 8-6 11-10h1c1-1 2-1 3-2z" class="h"></path><path d="M657 431c1-1 2-2 4-2v2c-2 1-4 3-5 5h-1v-1h-2l4-4z" class="I"></path><path d="M683 417l1 1-1 1 1 1h-1c0 1-1 2-1 2l-1 1c-1 1-2 2-3 2v1l-3 2c-3 1-5 2-7 3-1 0-2 0-2-1v-1h2c1-1 7-5 8-7h2 2l1-3 2-2z" class="a"></path><path d="M683 411h3c-1 2-2 3-4 4 0 1 0 1 1 2l-2 2c-3 0-4 1-6 2-3 2-7 4-10 7v1c-1 1-2 1-3 1 1-3 3-5 6-7 4-4 8-6 11-10h1c1-1 2-1 3-2z" class="F"></path><defs><linearGradient id="z" x1="801.656" y1="454.528" x2="765.919" y2="422.04" xlink:href="#B"><stop offset="0" stop-color="#120402"></stop><stop offset="1" stop-color="#390807"></stop></linearGradient></defs><path fill="url(#z)" d="M793 395l2-1c1 0 2 1 2 2 1 1 1 1 1 2 0 2-1 2-2 3 1 1 1 1 3 1v-2l2-2c1-1 1-2 1-4l1 1v3l-2 2-2 2 1 2c1 2 1 4 2 5s1 2 1 3v2c1 1 2 3 3 4l1 3c1 0 2 0 2 1l-2 1v4h1c-2 3-2 5-1 8 0 1 0 2 1 4v1c-1-1-2-2-3-1l1 4c1 1 2 3 3 4l-1 1c-1-1-1-1-2 0h0c-1 1-2 3-3 3l-1 2h-1c-1-1-1-2-2-2s-1-1-2-1h-2v2c-1 0-2 1-3 2h-1l-1 1h3v1h-1v1c1 1 1 2 2 4l-2 6-4 4c-1 1-2 2-2 3-2 2-3 4-5 6h-1l-1-8-2-2c-1-1-1-1-1-2v-5h-1v1l-2-2-1 1v-1c0-1 1-1 0-2v-3c1-2 1-4 1-5-1 2-1 3-2 5v1c0-4 1-10 2-14s-1-9 2-13v2h0l2-20 1-1s1 1 2 1l1-5c2-2 1-5 3-6 1 0 1-1 2-1l2-1c2-2 3-4 5-5z"></path><path d="M785 459c1-6 0-13 1-20v8h2c0 1 0 2 1 3l1 1v-1l1 1-3 3-1 1c0 2 1 3 0 4h-2z" class="i"></path><path d="M790 455h3v1h-1v1c1 1 1 2 2 4l-2 6-4 4 2-4c-1-2-1-3 0-5-1-1-2 0-3 0-1-1-1-2-2-3h2c1-1 0-2 0-4l2 1 1-1h0z" class="R"></path><path d="M777 413l1-1s1 1 2 1l-2 53 1 6-2-2c-1-1-1-1-1-2v-5h-1v1l-2-2-1 1v-1c0-1 1-1 0-2v-3c1-2 1-4 1-5-1 2-1 3-2 5v1c0-4 1-10 2-14s-1-9 2-13v2h0l2-20z" class="M"></path><path d="M774 457v2h1v1c1-2 1-3 1-4v4c1 1 2 0 2 1v5h0l1 6-2-2c-1-1-1-1-1-2v-5h-1v1l-2-2c0-1 0-4 1-5z" class="AA"></path><path d="M771 458c0-4 1-10 2-14s-1-9 2-13v2l-1 24c-1 1-1 4-1 5l-1 1v-1c0-1 1-1 0-2v-3c1-2 1-4 1-5-1 2-1 3-2 5v1z" class="Q"></path><defs><linearGradient id="AA" x1="794.689" y1="416.925" x2="783.007" y2="407.958" xlink:href="#B"><stop offset="0" stop-color="#240001"></stop><stop offset="1" stop-color="#150503"></stop></linearGradient></defs><path fill="url(#AA)" d="M793 395l2-1c1 0 2 1 2 2 1 1 1 1 1 2 0 2-1 2-2 3 1 1 1 1 3 1v-2l2-2c1-1 1-2 1-4l1 1v3l-2 2-2 2 1 2c1 2 1 4 2 5s1 2 1 3v2c-1 0-1 1-2 1v1l-2-1-1-1-2 2h0v1l-1 1c1 1 1 1 1 3-2 1-3 0-4 1v1c-2 1-3 3-4 5 0 2 0 3-1 6 0 1-1 1-1 2v-3c0-2-1-4-1-6 0 2 0 4-1 5h-1l1-10c-1-3 0-5 0-8 1-5 2-9 4-14 2-2 3-4 5-5z"></path><path d="M794 399v-2l2-1h0l1 1v2s-1 2-2 2c0-1 0-1-1-2z" class="c"></path><path d="M794 399c1 1 1 1 1 2v1c-1 1-1 1-1 2l-1 2h1l1-2v3l-3-1h0c0-1 0-1-1-2 1-2 2-3 3-5z" class="W"></path><path d="M795 404l1-1h0l1 1c0 1 1 1 1 2l-2 2-2 1-2-1h0c-1 1-2 1-3 2 1-2 1-4 2-6 1 1 1 1 1 2h0l3 1v-3z" class="T"></path><path d="M795 404l-1 2h-1l1-2c0-1 0-1 1-2h1 1l1 1h1v-1l1 2c1 2 1 4 2 5s1 2 1 3l-2-3-2-2-1-1c0-1-1-1-1-2l-1-1h0l-1 1z" class="o"></path><path d="M789 410c1-1 2-1 3-2h0l2 1-2 3c-1 1-1 1-1 2-1 3-3 5-5 7 1-4 1-8 3-11z" class="O"></path><path d="M794 409l2-1 2-2 1 1 2 2 2 3v2c-1 0-1 1-2 1v1l-2-1-1-1-2 2h0v1l-1 1c1 1 1 1 1 3-2 1-3 0-4 1v1c-2 1-3 3-4 5 0 2 0 3-1 6 0 1-1 1-1 2v-3c0-2-1-4-1-6l1-6c2-2 4-4 5-7 0-1 0-1 1-2l2-3z" class="j"></path><path d="M794 416l1 2c1 1 1 1 1 3-2 1-3 0-4 1v1l-2-2c-1-1 0-3 1-4l3-1z" class="L"></path><path d="M794 416v-1h0l-1-1c0-1 0-1 2-2v-2h1l3-3 2 2 2 3v2c-1 0-1 1-2 1v1l-2-1-1-1-2 2h0v1l-1 1-1-2z" class="k"></path><path d="M798 414c1-3 1-3 3-5l2 3v2c-1 0-1 1-2 1v1l-2-1-1-1z" class="B"></path><path d="M795 418l1-1v-1h0l2-2 1 1 2 1v-1c1 0 1-1 2-1 1 1 2 3 3 4l1 3c1 0 2 0 2 1l-2 1v4h1c-2 3-2 5-1 8 0 1 0 2 1 4v1c-1-1-2-2-3-1l1 4c1 1 2 3 3 4l-1 1c-1-1-1-1-2 0h0c-1 1-2 3-3 3l-1 2h-1c-1-1-1-2-2-2s-1-1-2-1h-2v2c-1 0-2 1-3 2h-1l-1 1h0l-1 1-2-1 1-1 3-3-1-1v1l-1-1c-1-1-1-2-1-3h-2v-8-3c0-1 1-1 1-2 1-3 1-4 1-6 1-2 2-4 4-5v-1c1-1 2 0 4-1 0-2 0-2-1-3z" class="M"></path><path d="M796 435c0 1 0 2 1 3h-1l-3 3-2-2c2-2 2-3 5-4z" class="F"></path><path d="M802 418c2 1 3 1 4 3h1c1 0 2 0 2 1l-2 1-2 1v-1c-1-1-2-2-3-2v1l-1-2c1 0 0 0 1-1v-1z" class="AG"></path><path d="M802 445v1c0 1-1 3-2 4 1 0 1 0 2 1h1l-1 2h-1c-1-1-1-2-2-2s-1-1-2-1v-1c2-1 3-4 5-4z" class="k"></path><path d="M803 414c1 1 2 3 3 4l1 3h-1c-1-2-2-2-4-3h-1v-2h0v-1c1 0 1-1 2-1z" class="D"></path><path d="M797 438h0c1-1 1-1 2-1l-2-3c1-1 1-1 2-1 0-1 1-2 0-4 2 0 2 1 3 1l1 1c0 2 0 5-1 7v2 1l-2-1-1 2c-2-1-2-1-3-2v-2h1z" class="s"></path><path d="M805 424l2-1v4h1c-2 3-2 5-1 8 0 1 0 2 1 4v1c-1-1-2-2-3-1l1 4c-4-5-2-12-3-17l2-2z" class="j"></path><path d="M797 443l2-1 1 1 2 2c-2 0-3 3-5 4v1h-2v2c-1 0-2 1-3 2h-1l-1 1h0l-1 1-2-1 1-1 3-3-1-1 1-2c2-1 3-2 4-4l2-1z" class="J"></path><path d="M797 443l2-1 1 1 2 2c-2 0-3 3-5 4v1h-2s3-3 3-4-1-2-1-3z" class="a"></path><path d="M795 418l1-1v-1h0l2-2 1 1 2 1h0v2h1v1c-1 1 0 1-1 1l1 2-1 1c0 2-1 3-2 4h-2c0 1-1 1-2 2 0 0-1 3-1 4h0l2 2c-3 1-3 2-5 4l2 2v1l2 2c-1 2-2 3-4 4l-1 2v1l-1-1c-1-1-1-2-1-3h-2v-8-3c0-1 1-1 1-2 1-3 1-4 1-6 1-2 2-4 4-5v-1c1-1 2 0 4-1 0-2 0-2-1-3z" class="G"></path><path d="M795 418l1-1v-1h0l2-2 1 1 2 1h0v2h1v1c-1 1 0 1-1 1l1 2-1 1h-2v3h-1c0-2 1-4-1-6l-1 1 1 1v2h-1c0-1 0-1-1-2h0c-1 1-1 1-1 2-1-1-1-1-2-1v-1c1-1 2 0 4-1 0-2 0-2-1-3z" class="m"></path><path d="M786 436c0-1 1-1 1-2l2 2c-1 0-1 0-1 2 0 1 0 2 1 3l-1 2h3v-4l2 2v1l2 2c-1 2-2 3-4 4l-1 2v1l-1-1c-1-1-1-2-1-3h-2v-8-3z" class="k"></path><path d="M789 450c0-2 0-3 1-5l1-1c0-1 0-1 2-2l2 2c-1 2-2 3-4 4l-1 2v1l-1-1z" class="G"></path><path d="M687 472c1-3 3-7 3-10l2-1c0-3 1-5 2-7 0 4 0 9 1 13 0 2 1 3 1 5 1 1 1 3 1 4 1 1 1 2 2 3 0 1 0 1 1 2h1c4 5 3 13 5 19 1 1 1 3 1 4 0 4 0 9-1 13l-1 17c-1 4-2 8-2 11l-2 2c-1-1-2-2-2-3h-1l1 1c-2 0-4-2-5-3l-2-2v9c-1 5-3 9-4 14-1 2-1 4-2 6s-1 5-2 7c0 2 0 4-1 5v1l-1 1c1 2 1 5 0 7l-1 1c-2 0-4-1-6-2-1-1-3-2-3-3s1-3 1-4h0l1-1v1l-1 4h1 0l1-1v1c1 1 1 2 3 2 0-1 1-1 1-2h0c-1-2-1-3-1-4h1c-1-1-1-1-1-2v-1-2c0-1 1-2 0-3 0-1-1-1-1-2s0-1-1-2l-1-1c0-3 2-7 3-10 1-2 1-4 1-6l1 1c0 1 1 1 1 2h0l1-1c1 1 0 1 1 1h1c-7-7-1-17-2-25l-2-17c0-3-1-5-1-7l1-1 1 1c0-1 1-2 1-3-2-3 1-9 2-12 0-1 1-1 2-2-1-1-1-3-1-4 1-1 1-2 1-4 1-1 1-2 1-4h1c1 1 2 2 3 4h0v-1c0-3-2-3-4-5v-4z" class="I"></path><path d="M680 514v-6c1 1 1 1 1 2s0 1 1 2c2 3 3 8 4 12 0 4 0 7 2 12 2 4 0 16-2 20v1h-1l-1-1c-7-7-1-17-2-25l-2-17z" class="R"></path><defs><linearGradient id="AB" x1="763.099" y1="513.297" x2="749.926" y2="510.461" xlink:href="#B"><stop offset="0" stop-color="#200706"></stop><stop offset="1" stop-color="#430908"></stop></linearGradient></defs><path fill="url(#AB)" d="M771 458v-1c1-2 1-3 2-5 0 1 0 3-1 5v3c1 1 0 1 0 2v1l1-1 2 2v-1h1v5c0 1 0 1 1 2l2 2 1 8h1 2l1 1-1 1-1 1 4 5c0 2 0 3 1 4v1c5 5 12 8 18 12l1 1h1l1 1c-1 1-3 2-5 2-12 2-19 8-25 18l-8 17c1 2 1 4 2 5l-3 3c-4 4-6 8-9 13 0 2 0 3 1 5l1 1c-2 0-2 1-3 0-2 0-5 1-6 2-2 2-4 3-6 5s-4 4-7 4h-3v-1l-1 1c-1-1-2-1-2-1-1 1-1 1-2 1-1-1-1-1-2-3 1 0 1-1 1-1-1-1 0-1-1-1 3-4 5-8 7-12 1-1 2-2 2-3 1-1 1-1 2-1 2-4 4-10 5-15 6-13 12-26 15-39l7-32 3-17z"></path><path d="M770 484c0-5 1-11 2-17l1 2h0 2v5c-1 0-1 0-1 1-1 3-1 8-2 12v-1c-1 0-1 0-2-2z" class="D"></path><path d="M765 508l5-24c1 2 1 2 2 2v1c0 3-1 7-2 11v3h-1v2c-1 1-1 2-2 3l-2 2z" class="G"></path><path d="M737 565c1-1 2-2 2-3 1-1 1-1 2-1-1 3-3 6-4 9-1 1-1 2-1 3 0 2-1 3-2 4l1 1-1 3c-1 1-1 1-2 1-1-1-1-1-2-3 1 0 1-1 1-1-1-1 0-1-1-1 3-4 5-8 7-12z" class="J"></path><path d="M772 487c1-4 1-9 2-12 0-1 0-1 1-1 0 3 1 7 2 10v1c-1 1-1 2-1 3 1 3-5 9-6 13v-3c1-4 2-8 2-11z" class="L"></path><path d="M773 462l2 2v-1h1v5c0 1 0 1 1 2l2 2 1 8h1 2l1 1-1 1-1 1 4 5c0 2 0 3 1 4v1l-6-4h-1-1c-1 0-1-1-2-2v-2-1c-1-3-2-7-2-10v-5h-2 0l-1-2v-4l1-1z" class="AB"></path><path d="M773 462l2 2v5h-2 0l-1-2v-4l1-1z" class="M"></path><path d="M780 480h1 2l1 1-1 1-1 1 4 5c0 2 0 3 1 4v1l-6-4 1-1c0-1 0-2-1-2h-1l1-1-1-1v-4z" class="z"></path><path d="M763 513h1l3-6h1c0 1 0 2-1 3v3c0 1-2 6-2 7 0 5-4 9-5 14v3c1 0 1 1 2 1 0 4-4 8-4 12 1 1 2 1 3 1l-1 3c2 0 4-3 6-4 1 1 2 1 3 2-4 4-6 8-9 13 0 2 0 3 1 5l1 1c-2 0-2 1-3 0-2 0-5 1-6 2-2 2-4 3-6 5s-4 4-7 4h-3v-1l-1 1c-1-1-2-1-2-1l1-3c12-20 20-43 28-65z" class="M"></path><path d="M744 569h0l1-1 1 1h0c0-2 0-3 1-4h2c1 0 1 0 2-1l2 2v1h-1l-1-1h-4c-1 2 0 4-1 6v1l2-1c-1 1-1 2-1 3h1c-1 2-6 4-8 5-1 1-1 1-2 1 1-4 4-9 6-12z" class="e"></path><defs><linearGradient id="AC" x1="765.064" y1="542.949" x2="744.213" y2="561.996" xlink:href="#B"><stop offset="0" stop-color="#2d0a09"></stop><stop offset="1" stop-color="#691516"></stop></linearGradient></defs><path fill="url(#AC)" d="M744 569c3-8 8-16 12-24 3-8 5-17 9-25 0 5-4 9-5 14v3c1 0 1 1 2 1 0 4-4 8-4 12 0 2-1 4-1 6l-4 10-2-2c-1 1-1 1-2 1h-2c-1 1-1 2-1 4h0l-1-1-1 1h0z"></path><path d="M760 554c2 0 4-3 6-4 1 1 2 1 3 2-4 4-6 8-9 13 0 2 0 3 1 5h-6l-7 5h-1c0-1 0-2 1-3l-2 1v-1c1-2 0-4 1-6h4l1 1h1v-1l4-10c0-2 1-4 1-6 1 1 2 1 3 1l-1 3z" class="D"></path><path d="M758 566l1 1 1-2c0 2 0 3 1 5h-6c-1 0-1 0-2-1h1c0-1 0-2 1-3l2 1 1-1z" class="V"></path><path d="M748 572c0-1 1-1 1-2 1 0 2-1 3-1h1c1 1 1 1 2 1l-7 5h-1c0-1 0-2 1-3z" class="i"></path><path d="M760 554c2 0 4-3 6-4 1 1 2 1 3 2-4 4-6 8-9 13l-1 2-1-1c0-4 1-8 2-12z" class="K"></path><path d="M777 484v1 2c1 1 1 2 2 2h1 1l6 4c5 5 12 8 18 12l1 1h1l1 1c-1 1-3 2-5 2-12 2-19 8-25 18l-8 17c1 2 1 4 2 5l-3 3c-1-1-2-1-3-2-2 1-4 4-6 4l1-3c-1 0-2 0-3-1 0-4 4-8 4-12-1 0-1-1-2-1v-3c1-5 5-9 5-14 0-1 2-6 2-7v-3c1-1 1-2 1-3h-1l-3 6h-1l2-5 2-2c1-1 1-2 2-3v-2h1c1-4 7-10 6-13 0-1 0-2 1-3v-1z" class="AJ"></path><path d="M777 484v1 2c1 1 1 2 2 2h1l-2 2c-1 3-4 6-5 9-2 4-3 9-6 13v-3c1-1 1-2 1-3h-1l-3 6h-1l2-5 2-2c1-1 1-2 2-3v-2h1c1-4 7-10 6-13 0-1 0-2 1-3v-1z" class="B"></path><path d="M761 551l1-4v-1c1-3 4-3 5-6 0-1 1-3 2-4v-2c2-4 4-8 7-11h0c-2 4-5 7-6 11l-3 8c0 2-1 4-2 6l1 1c0-1 1-1 1-2v-1c0 1 1 2 0 3h1c1 0 1 0 2 1 0-1 1-1 0-2 0-2-1-3-1-5h1v-1c-1-1-1-1-1-2 1-1 1-2 3-2v-1l1-1h0l1-1c0-1 1-2 1-4 1-1 1-2 3-4l-8 17c1 2 1 4 2 5l-3 3c-1-1-2-1-3-2-2 1-4 4-6 4l1-3z" class="Q"></path><path d="M762 538l1-2c1-1 0 1 1-1s3-4 4-6c6-10 13-22 24-27-5 7-11 13-16 20v1c-3 3-5 7-7 11v2c-1 1-2 3-2 4-1 3-4 3-5 6v1l-1 4c-1 0-2 0-3-1 0-4 4-8 4-12z" class="K"></path><path d="M667 472c1-1 1-1 1-2 1 1 1 0 1 1l1 1h2l2-2v4l-1 2v1c2 1 5 1 6 1h5v-1c0-2 1-4 1-6l2 1v4c2 2 4 2 4 5v1h0c-1-2-2-3-3-4h-1c0 2 0 3-1 4 0 2 0 3-1 4 0 1 0 3 1 4-1 1-2 1-2 2-1 3-4 9-2 12 0 1-1 2-1 3l-1-1-1 1c0 2 1 4 1 7l2 17c1 8-5 18 2 25h-1c-1 0 0 0-1-1l-1 1h0c0-1-1-1-1-2l-1-1c0 2 0 4-1 6-1 3-3 7-3 10l1 1c1 1 1 1 1 2s1 1 1 2c1 1 0 2 0 3v2 1c0 1 0 1 1 2h-1c0 1 0 2 1 4h0c0 1-1 1-1 2-2 0-2-1-3-2v-1l-1 1h0-1l1-4v-1l-1 1h0c0 1-1 3-1 4s2 2 3 3c-1 0-3 0-4-1-1 0-1-1-2-1h-2c-1-1-2-1-3-2-3-2-6-3-9-5-2-1-3-1-5-1h0c-3-4-7-7-11-10-3-3-5-6-7-10l2-1-2-3c-1-2 0-5 1-7v-1c-1-1-1-3-2-4h-1v-2c-1-1-3-2-5-3-1-2-2-3-3-5 1-1 1-2 2-2l3-9 1-2c0-2 1-3 2-5 1-3 1-6 1-9 2-8 3-17 6-25 0 0 0-1 1-2s1-1 1-2l2-1c1 0 2-1 2-2l1-1 2-3h1l1 2-1 2c1 1 2 3 3 4 0 2 0 4-1 6h2 0 1 1l1-1v-2h1c1 1 0 2 2 2v1h2c1-2 2-2 4-2h1 1c2-2 3-2 6-3l-2-1c0-2-1-3-2-4v-2z" class="H"></path><path d="M669 579c2-2 2-3 3-4l1 1c1 1 1 2 2 3l-1 2-1 1c-1-1-2-2-4-3z" class="W"></path><path d="M646 514l2 1v3 1c1 0 1 0 2 1v1 3c-2 0-1 0-2 1-2 0-1-1-2-1 1-3 1-4 1-7h-1v-3z" class="J"></path><path d="M664 484v3h1c1 1 2 2 2 3-1 1-1 2-1 2-2 2-2 3-3 5h-1l2-13z" class="u"></path><path d="M643 516l1 2h1c0 3-1 6-2 9l-1-1c0-1 0-1-1-2s0-2 0-3h0c1-2 2-3 2-5z" class="D"></path><path d="M684 477c1 1 1 1 1 3h0l-1 1v4c0 2 0 4-1 5h-3c1-4 4-8 4-12v-1z" class="u"></path><path d="M656 492c1 1 1 3 1 4l-1 1c-1 1-1 2-1 3l1 1c-1 1-1 1-2 0v2 1 3h0c0 3 0 4-2 7l-1-1 2-5-1-1c1-1 1-1 1-2l-1-1 1-1h-1l1-1v-1l1-1c1-3 1-6 2-8z" class="J"></path><path d="M669 571l1-1c-1 1-1 2-2 2l1 1 1-1h0c0 1-1 1-1 2h0c-1 1-1 1-1 2h-1s-1 1-2 1h-1l-3-3 1-2 1 1h2c1-1 3-1 4-2z" class="E"></path><path d="M663 573c-1-1-1-3-1-4l1-1c0 1 0 1 1 1v-1h-1c-1-1-1-1-1-3h1c2 2 5 3 6 6-1 1-3 1-4 2h-2z" class="T"></path><path d="M654 507c0-1 1-2 1-3l1-2h0l1 1v-2l1-1v-1c1-1 0-2 0-3 0 1 0 1-2 2 1-2 1-2 2-3l1 1c1 2 0 3-1 5s-1 4-1 6-1 4-2 6l-1 1h-2c2-3 2-4 2-7z" class="F"></path><path d="M655 580h1c1 0 1 0 2 1 1 0 2 2 3 2 3-4 1-3 0-5s-1-4-1-5h1v1l3 3h1l2 2h2c-2 2-3 2-4 5 0 1 0 1-1 1-3-2-6-3-9-5z" class="W"></path><path d="M664 555h1c2 2 4 4 6 7 1 1 0 2 0 3l-1 1h-1c-2-1-4-2-5-4s0-5 0-7zm5 24h0c2 1 3 2 4 3h0c0 1-1 3-1 4s2 2 3 3c-1 0-3 0-4-1-1 0-1-1-2-1h-2c-1-1-2-1-3-2 1 0 1 0 1-1 1-3 2-3 4-5z" class="R"></path><path d="M634 558h1c1 1 0 3 2 4l2 1h0c0 2 1 3 3 4s4 5 5 6c2 3 6 4 8 7-2-1-3-1-5-1h0c-3-4-7-7-11-10-3-3-5-6-7-10l2-1z" class="o"></path><path d="M649 501c0-1 1-3 1-4h1l1 1 2 2-1 1v1l-1 1h1l-1 1 1 1c0 1 0 1-1 2l1 1-2 5 1 1h2l-3 4c0 1-1 2-1 3v-1c-1-1-1-1-2-1v-1-3l-2-1 2-9 1-4z" class="B"></path><path d="M649 501h1c1 1 1 1 0 2v2 2h0c-1 1-1 1-1 2v3c0 1-1 2-1 3l-2-1 2-9 1-4z" class="i"></path><path d="M667 548c0-2 0-3 1-5 1-1 2-1 3-1l1 1h0c1 0 2 1 3 1v3l1 1c1 1 1 3 1 4l-3 8-7-8c-1-2-1-3 0-4z" class="R"></path><path d="M671 526c1 1 2 1 2 1 1 1 1 1 2 1 0-1 1-3 1-4 1 1 0 0 0 2l1 1v1c2 1 2 2 2 4v1 2c1 1 0 2 0 2v2 2c-2 2-2 8-2 11 0-1 0-3-1-4l-1-1v-3c-1 0-2-1-3-1h0l-1-1c-1 0-2 0-3 1-1 2-1 3-1 5 0-3 0-5-1-8l5-14z" class="I"></path><path d="M644 473l2-3h1l1 2-1 2c1 1 2 3 3 4 0 2 0 4-1 6h2 0 1 1c0 2 1 3 1 4 2 1 2 1 2 3v1c-1 2-1 5-2 8l-2-2-1-1h-1c0 1-1 3-1 4l-1 4-2 9v3l-1 1h-1l-1-2c0 2-1 3-2 5h0c0 1-1 2 0 3s1 1 1 2l1 1c-1 4-3 7-4 11 0 1-1 3-2 4v3c1 1 0 1 0 2l2 1c0-1 1-2 2-3l1 2c-1 2-3 5-5 6-1 0-1 0-1-1h-1 0c-1 2-1 2-3 3-1-2 0-5 1-7v-1c-1-1-1-3-2-4h-1v-2c-1-1-3-2-5-3-1-2-2-3-3-5 1-1 1-2 2-2l3-9 1-2c0-2 1-3 2-5 1-3 1-6 1-9 2-8 3-17 6-25 0 0 0-1 1-2s1-1 1-2l2-1c1 0 2-1 2-2l1-1z" class="i"></path><path d="M643 491c1 0 1 1 2 1-1 2-1 3-2 4v1h-1v-2c1-1 1-2 1-3v-1z" class="F"></path><path d="M635 527c0 2 0 5 1 7-1 2-2 4-2 6v2c1 2-1 4 0 6l-1 2h1l2-2v-1c0-1 0-1 1-2l-1-1c0-1 0-1 1-2v3c1 1 0 1 0 2l2 1c0-1 1-2 2-3l1 2c-1 2-3 5-5 6-1 0-1 0-1-1h-1 0c-1 2-1 2-3 3-1-2 0-5 1-7v-1c0-1 1-2 1-4l-1-1v-1c-1-1 0 0-1-2h0 1v-4-1c1-1 1-1 1-3 0-1 0-2 1-4z" class="E"></path><path d="M641 521c0-1 0-4 1-5v-2-2h1c-1 0-1-1-2-1l-1-1 2-2-1-2h0c0-1 1-1 1-2 1-3 3-5 4-7s1-3 3-4c-1 2-2 4-3 5l-1 3h1 0c0 1 0 2-1 3l1 1 2-1v1h-2l-1 1 3-1-2 9v3l-1 1h-1l-1-2c0 2-1 3-2 5h0z" class="B"></path><path d="M645 506l3-1-2 9v3l-1 1h-1l-1-2c0-4 1-7 2-10z" class="G"></path><path d="M649 493c1-1 1-2 1-4 0-1 1-2 1-4l1 1c0 1-1 2 0 4l2-2c2 1 2 1 2 3v1c-1 2-1 5-2 8l-2-2-1-1h-1c0 1-1 3-1 4l-1 4-3 1 1-1h2v-1l-2 1-1-1c1-1 1-2 1-3h0-1l1-3c1-1 2-3 3-5z" class="D"></path><path d="M644 473l2-3h1l1 2-1 2c1 1 2 3 3 4 0 2 0 4-1 6v3s1 0 1 1c-1 1-1 1-1 2s0 2-1 3h-1c-1 0 0 0-1 1v-2h1l1-1v-1l-1 1h-1 0v-2h-2c-1 1-1 1-1 2s-1 1-2 2v-3l2-10h-3v1c-1 0-2-1-2-2 1-1 1-1 1-2l2-1c1 0 2-1 2-2l1-1z" class="H"></path><path d="M641 476l2 1v-1s0-1 1-1h0c0 2 0 3-1 5h-3v1c-1 0-2-1-2-2 1-1 1-1 1-2l2-1z" class="i"></path><path d="M638 479c0 1 1 2 2 2v-1h3l-2 10v3c-1 2-1 5 0 8v2s0 1-1 2c-1 2 0 5-2 7-2 4-2 11-3 15-1 2-1 3-1 4 0 2 0 2-1 3v1 4h-1 0c1 2 0 1 1 2v1l1 1c0 2-1 3-1 4-1-1-1-3-2-4h-1v-2c-1-1-3-2-5-3-1-2-2-3-3-5 1-1 1-2 2-2l3-9 1-2c0-2 1-3 2-5 1-3 1-6 1-9 2-8 3-17 6-25 0 0 0-1 1-2z" class="k"></path><path d="M638 479c0 1 1 2 2 2v-1h3l-2 10v-1-1l-1 1c0 1 0 2-1 3v-3-6s0-1-1-1v-1h-1 0s0-1 1-2z" class="B"></path><path d="M624 531l3-9 1-2c0-2 1-3 2-5v3 1l-1 2c0 2 1 1 1 3 1 1-1 4-1 6v5c1 1 1 1 2 1 1-1 1-1 2-1v4h-1 0c1 2 0 1 1 2v1l1 1c0 2-1 3-1 4-1-1-1-3-2-4h-1v-2c-1-1-3-2-5-3-1-2-2-3-3-5 1-1 1-2 2-2z" class="i"></path><path d="M622 533c1-1 1-2 2-2v2c3 2 4 2 5 5h2l1 1h0c1 2 0 1 1 2v1l1 1c0 2-1 3-1 4-1-1-1-3-2-4h-1v-2c-1-1-3-2-5-3-1-2-2-3-3-5z" class="V"></path><path d="M667 472c1-1 1-1 1-2 1 1 1 0 1 1l1 1h2l2-2v4l-1 2v1c2 1 5 1 6 1h5c0 4-3 8-4 12h3c-1 3-1 5-2 7 0 2-1 5-1 7l-1 1c-2 3-3 7-4 11-1 3-2 7-4 10l-5 14-10 23c0 2-1 3-2 4v1h0c0 3-1 4-2 6h-1l-1-1v-1l-1 2c-1-2-3-4-5-6v-7c1-1 1-2 1-3 1-1 1-2 1-3 1-2 2-5 3-8h1l12-50h1c1-2 1-3 3-5 0 0 0-1 1-2 0-1-1-2-2-3h-1v-3-2h1c2-2 3-2 6-3l-2-1c0-2-1-3-2-4v-2z" class="J"></path><path d="M667 472c1-1 1-1 1-2 1 1 1 0 1 1l1 1h2l2-2v4l-1 2v-4c-2 1-1 3-2 4-1-1-1-2-1-3-1 0-2-1-3-1z" class="K"></path><path d="M680 490h3c-1 3-1 5-2 7h0c-1 1-1 2-1 3-1 0-1 0-2 1s-1 1-1 3l-1-1 4-13z" class="M"></path><path d="M671 479h1c0 2-1 4-1 7l-4 18v-1l-2-3c0-2 0-5 1-8 0 0 0-1 1-2 0-1-1-2-2-3h-1v-3-2h1c2-2 3-2 6-3z" class="s"></path><path d="M676 503l1 1c0-2 0-2 1-3s1-1 2-1c0-1 0-2 1-3h0c0 2-1 5-1 7l-1 1c-2 3-3 7-4 11-1 3-2 7-4 10l-5 14-10 23h0-1v-2l13-35c2-8 4-16 8-23zm-14-6h1c1-2 1-3 3-5-1 3-1 6-1 8l2 3v1c-1 5-2 10-4 15-2 9-5 19-10 28h0-2-1l12-50z" class="p"></path><defs><linearGradient id="AD" x1="462.826" y1="239.361" x2="668.877" y2="431.639" xlink:href="#B"><stop offset="0" stop-color="#000505"></stop><stop offset="1" stop-color="#220908"></stop></linearGradient></defs><path fill="url(#AD)" d="M645 129l2 1 1 1c3 1 3 2 5 3l9 6-1 1c4 4 9 9 9 15 0 2-1 3-1 5 1 1 1 1 2 1l-1-1c1-1 1-1 3-1 0-1-1-3 0-4 0-1 2-1 2-2l2 3 3 5c1 2 3 5 5 6v-1c2 1 3 3 5 3h2l5-15 1-2c1-6 2-10 3-15v4l2 1-4 14c-3 6-5 13-6 20-1 1-1 2-1 3v1l-1 1 1 3-10 24c-1 1-1 1-1 2l-1 1 2 6c-2 3-3 5-3 8h19 9v4c-1 6-4 13-6 19-14 3-25 7-33 20-2 4-4 8-6 13v-2-2c1-1 1-1 1-2h1l-1-1c0 1-1 2-1 2-1 2-1 3-2 4l-1 1-20 68-1 4c-1 5-4 11-5 17-2 5-3 11-5 16 0 2-1 4-1 7l-5 15h0c-1 2-3 13-3 14h0v4h0l1-1v1l-7 26-10 35h-1c0-1 1-1 1-2v-1-1-1h1v-4 1l-1 1c-1 1-1 2-2 3v1l-1 3c-2-3-2-4-3-7v-3h-2c-1 0 2 0-1 0h-1v1c-1 0-1 1-1 2l-1-1c-1 1-1 1-1 3v1 1c-1 1-1 1-1 2v1c-1 2-1 3-3 4l-3 11c-1 2-2 5-3 7v3c0 2-1 3-1 5l-8 28-4 13c0 2-1 4-1 5l-5 18-2 5-1-1c-1 0-1 1-2 2v1c-1 3-1 5-1 8-1 1-1 3-1 4-1 1-1 2-1 3v1c-1 1-2 3-2 5h-2v1c-2 1-4 3-4 6h-1v2l-2 2h-1-1 0v-3l-1-1h-1c0-1 0-2-1-4l-1 2-3 1-2 2-4 2v1l-2 1c-1 2-2 3-2 5v3l-1 1c0 1 0 2-1 3h-1c-1-3-1-4 0-7h-3l-1 3v-5l-1-1c0-2 0-5 1-7l-1-4h0l-1-4c0-2-1-4-2-6l-1-4-3-12-5-18-20-68c-3-8-5-16-7-23-1-4-2-7-3-10 0-1 0-1 1-2s0-3 0-4c0-3 0-7-1-10v-2c-2-1-2-1-2-3v-3c-1-1-1-1-1-2v-2c-1-1-1-1-1-2v-1c0-1 0-2-1-2v-1-1-2l-1-1v-2h0c-1-1-1-2-1-3 1-1 1-2 0-3v-3l-1-5c-1-2-1-5-1-7 0-1 0-1-1-2v-4c-1-1-1-2-1-3v-2l-1-2-1-5v-1c-1-3-1-6-1-9-1-3 0-5 0-8l-10-34-1-4-9-27-1-3c-1-2-1-5-2-8l-1-14c0-9 2-20 9-27l4-3c1 1 2 1 3 1 5-3 10-3 15-2l1-1c1-2 3-22 3-24h-3-5c-3-1-6-1-9-1-3-1-6-3-8-4l-4-2-4-2h-1c-2-2-5-3-7-5v-1c0 1 0 1-1 1l-2-2c-1-1-1-2-2-3-3-1-4-4-6-4-2-1-1-1-1-2-1-1-3-2-4-3h-1c-1 0-1 0-1-1-2-1-2-3-5-3h0a30.44 30.44 0 0 0-8-8c-1-2-3-4-5-5v-1c-1-2-2-3-3-5h1v-1l-2-1h2v-5h1v-1c2 0 7 7 9 9h0c0 2 2 3 2 4l3 3c2 0 3 2 4 2h0c0-2 0-3-1-4 2 2 4 4 7 6v1l3 3h1c2 2 6 4 7 6 1 1 1 2 2 3v1c1 3 4 4 7 6s7 5 11 6c3 1 6 3 9 3 1 0 3 1 4 1 2 1 3 1 5 1-1 0 0 0-1-1v-1l-1-1h0c3 0 7 2 10 3 1 0 3 0 3 1 4 1 8 2 11 3 1 0 3-1 4 0 3-1 7-1 10-2s5-1 7-3c1-1 3-3 4-5 1 1 1 2 2 3 0 1 1 2 1 3l3 2 3-3h0 1c3 3 7 5 12 5h5c5 0 10-1 15-2v-1c-1-2-1-2-2-3h0 2l3-1c1 1 2 1 3 1h2c1 0 1 0 2 1 1 0 4-2 5-3h1c2-1 4-1 6-2 1 0 2-1 3-1h1c0-1 0-1-1-2 5-2 8-4 12-7l2-1s1-1 2-1l2-1c0-1 1-2 2-3l1-1 7-5 4-3c1-2 6-6 7-7l7-10c1-2 4-5 5-7 2-2 2-4 3-5 4-4 6-11 8-16v-4h0v-5-2l4-1z"></path><path d="M577 284v1c-1 1-1 1-1 2l-2 1c-1-1-1-1-3-2l6-2z" class="l"></path><path d="M525 493l3 4-1 1v1c0 1-1 2-1 3l-1-9z" class="d"></path><path d="M654 163h0c1-3 3-4 5-6 0 1-1 3-1 4-1 1-2 1-3 2h-1z" class="U"></path><path d="M648 153h1v3l2 1v2l-1 1-2-3c-1-2-1-2 0-4z" class="C"></path><path d="M466 264l-2-3c-2-2-4-3-5-5 2 1 5 3 8 5l-1 3z" class="d"></path><path d="M464 337c1 0 1 0 2 1s1 1 1 2 0 1 1 2c0 1 1 1 1 3l-2 1-3-9z" class="P"></path><path d="M444 254l4-3c1 1 2 1 3 1l-5 4-1-2h-1z" class="AM"></path><path d="M466 310l2 2s0 1 1 1l3 5 2 2c-2-1-2-1-3-1-2-1-2-2-3-4s-1-3-2-5z" class="U"></path><path d="M585 280h2c-1 1-2 2-4 2-3 1-4 4-7 5 0-1 0-1 1-2v-1c0-1 1-1 2-1l6-3z" class="P"></path><path d="M636 167c0-1 1-2 1-3v-1h2c1 1 0 0 1 2h0 1c-1 2-1 2 0 4-2 0-2-1-4-1h-1v-1z" class="X"></path><path d="M640 165c1-1 1-1 1-2h0c0-1 1-1 1-2h-1c0-1 0-2 1-3h0 0v-1h4l2 2h1c-2 0-3 0-5 1h0-1-1l1 1v1c-1 0-1 1-1 2l-1 1h-1z" class="C"></path><path d="M555 286c0-1 1-3 2-4l1-1c1-2 3-4 5-4 0 0 1-2 2-3v1c-1 1-1 3-3 4 0 0-1 0-1 1-1 2-3 4-4 5l-2 1z" class="U"></path><path d="M541 521h1l1-1v-3c1 4 1 13-2 16v-3-9z" class="P"></path><path d="M571 277c2 1 2 1 3 3v-1c1 0 1 0 2-1v1 2h0l-4 2h-4c1-1 2-2 2-4l1-2z" class="Z"></path><path d="M594 216h2v1l1 1c0 1-1 2-2 2h0l-2 1c-3 1-3 1-6 0 2-2 5-3 7-5z" class="X"></path><path d="M524 382c0-3 0-6 2-9 1 0 1-1 1-2s0-1 1-2c0 4-1 7-1 11v1h0c-1 1-1 2-2 3l-1-2z" class="Z"></path><path d="M572 232c1-1 1-1 2 0s1 2 1 4h-3c-2 0-3 1-5 0 0-1 0-2 1-3 1 0 2 0 3-1h1z" class="U"></path><path d="M659 190c1-2 2-3 4-5 2-3 4-7 7-9-3 6-6 11-10 16l-1-2z" class="C"></path><path d="M573 275c1 0 2 0 4 1h0 4s1 1 2 1c-3 1-5 3-7 4h0v-2-1c-1 1-1 1-2 1v1c-1-2-1-2-3-3l2-2z" class="d"></path><path d="M546 342c1 2 2 3 3 5 1 1 2 3 3 4v-1h2c-1 2-2 4-2 7l-1 2c0-2 0-3-1-5-2-4-3-8-4-12z" class="t"></path><path d="M467 261l10 9-1 1c-1 0-2-1-3-1 1-1 1-1 1-2l-1 1h-1c-2-1-4-3-6-5l1-3z" class="N"></path><path d="M504 474c-1-1-1-1-2-1v-1-4l1-2c-1 0-1 0-2-1l2-1 1-1c0-1 0-2 1-3 0 1 0 1 1 2-1 4 0 9-1 12v-6c-1 0-1 2-2 2l2 2c-1 1-1 1-1 2z" class="P"></path><path d="M540 352l1-2v-1-4-1-2h0c1 1 1 2 2 4v3c1 2 1 4 2 7h0l-1 1h0l-3-3v-1l-1-1z" class="U"></path><path d="M530 243c-1 3-2 4-4 6s-6 5-7 8h0c-1 1-1 2-1 3-1 1-1 1-1 2h-2c2-7 8-13 13-18l2-1z" class="C"></path><path d="M587 301v1l1-1v1c0 1-1 1-1 2l-4 8-2 4-2 1c0 2 0 1-1 2h0-1 0v-2h1v-1c1-1 1-2 2-4 0-1 1-2 1-3h1l2-4 3-4z" class="Z"></path><path d="M658 161c-2 8-5 14-9 21 0-2 3-6 3-8l1-1v-4c0-1 1-2 1-2v-1l-1-2 1-1h1c1-1 2-1 3-2z" class="d"></path><path d="M513 261c1 1 1 2 2 2-1 4-1 8-1 12 0 1 0 2-1 3-1-1-1-2-2-3v-8c1 0 1-1 2-2v-2-1-1z" class="AN"></path><path d="M555 286l2-1c1-1 3-3 4-5 0-1 1-1 1-1-2 4-4 9-6 13h0c-1-1 0-1-1 0h0c-1-1-1-1-1-2l-1 1v2h-1c0-1 0-1-1-1 1-2 2-4 4-6z" class="X"></path><path d="M577 271c0-1 0-1 2-1v2l1 1 1 1h3c1 0 2-1 3-1v1s-1 2-2 2c-1 1-2 1-2 1-1 0-2-1-2-1h-4 0c-2-1-3-1-4-1l4-4z" class="C"></path><path d="M577 271l1 1c-1 1-1 1-2 1 1 1 1 1 2 1 0 1 0 1-1 2h0c-2-1-3-1-4-1l4-4z" class="Z"></path><path d="M530 539c1 0 2 0 2 1v1c1 1 1 1 2 3l-1 1-1-1c0 1 0 2-1 2v1l-1-1c-1-1-1 0-1-1v-1-1-1l-1-1c1-2-1-3 0-4l1-1c-1-1-2-1-3-1v-3h-1v-2c0-1-1-2-1-3h0v-1-1-1c-1-1-1-3-1-4h-1v-1h0v1h0c0-1-1-2 0-4 2 5 3 11 4 16 0 0 1 2 2 2 0 1 1 1 1 1v1 3l1 1c0 1 1 2 2 3h0c0-2-1-3-2-4z" class="C"></path><path d="M515 503c1 8-1 17 0 26 0-1-1-2-1-3-1-7-2-14-2-21h0c1-1 2-1 3-2z" class="b"></path><path d="M648 131c3 1 3 2 5 3l9 6-1 1h0l-1-1h-1c-2-1-5-2-7-1-1 1-1 3-2 3 0-4 0-7-1-10l-1-1z" class="a"></path><path d="M458 311c-1-1-2-3-2-3l-4-8c-1-2-1-3 0-5l11 19c-2-1-3-3-5-3z" class="AL"></path><path d="M524 382l1 2c1-1 1-2 2-3h0c0 2 1 8 0 10 0 0-1 1-2 1v1h-1-1s0 2-1 2h0v-5-1-2h0l1-1c0-1 0-2 1-4z" class="X"></path><path d="M531 478l6 13c1 1 2 3 2 4v1l-1 1-1-2-1-1-1 2-1-1-2-6c1-4-1-7-1-11z" class="l"></path><path d="M536 494h-1c-1-1-1-2-1-4 1 1 1 1 2 1h1c1 1 2 3 2 4v1l-1 1-1-2-1-1z" class="Y"></path><path d="M549 319c1 3 1 4 1 7 0 1-1 1-1 2 0 2-1 7 0 9v1c1 1 1 1 1 2 1 1 1 2 1 3l1 1 1 1-1 1c-1-1-1-2-1-2v2 1h1v1h1v-1-1-3c1 1 1 3 1 4v3h-2v-1c-4-7-4-13-4-22v-2-5h0l1-1z" class="C"></path><path d="M542 483l1-20 1 13c0 6 0 14-1 20l-1-3c-1-1-1-2-2-3v-4-2l2-1z" class="Y"></path><path d="M540 484l2-1v10c-1-1-1-2-2-3v-4-2z" class="AK"></path><path d="M533 331c0 1 0 2 1 4h0c-1 4-2 8-2 12-1 2-1 5-3 7v2h-1v1 1 1h-1v1 1l-1 2v1 1 1l-1 1v2c-1 1-1 1-1 2l-1-2c3-6 4-12 6-18v-2c1-1 1-1 0-2l4-16z" class="C"></path><path d="M626 185v-2h0l2-2c0 1 0 0 1 1v-1c0-2 0-3 1-4l2-2v-1c1-1 2-3 2-4v-1l1-1c0-1 0-1 1-1v1h1l1 3h0c-1 2-2 3-2 4-2 1-3 3-4 5l-1 1-4 4h-1z" class="U"></path><path d="M496 437c1 1 1 2 1 3 1 1 1 2 2 3 0 0 0 1 1 1h0v2c-1 1-1 2-1 3-1 1-1 2-1 3l-1 1c-1 0 0 0-1-1v-1c-1 0-2 0-2-1v-3-1l-1 1-1-1c2 0 2 0 3-1l1 1c0-1-1-3-2-3 0-2 1-2 0-3l1-1 1-2z" class="Y"></path><path d="M496 437c1 1 1 2 1 3 1 1 1 2 2 3 0 0 0 1 1 1h0v2l-1-1-1 1c-2-2-3-4-3-7l1-2z" class="b"></path><path d="M568 283h4c-1 1-1 2-3 2l1 1h1c2 1 2 1 3 2-2 1-6 3-8 3h-1c-2 0-3 1-4 1l1-2c0-3 3-5 6-7z" class="U"></path><path d="M570 286h1c2 1 2 1 3 2-2 1-6 3-8 3h-1c-2 0-3 1-4 1l1-2c2-1 6-2 8-4z" class="g"></path><defs><linearGradient id="AE" x1="546.261" y1="517.096" x2="537.239" y2="496.904" xlink:href="#B"><stop offset="0" stop-color="#302f30"></stop><stop offset="1" stop-color="#4d4b4d"></stop></linearGradient></defs><path fill="url(#AE)" d="M540 490c1 1 1 2 2 3l1 3v21 3l-1 1h-1c1-4 1-8 0-12 0-2 0-4-1-6 2-2 1-5 0-8v-5z"></path><path d="M578 229h2c2 7 3 13 4 20h1c2 0 2 0 3 1l-6 2c-1-2-1-5-1-6l-3-17z" class="AH"></path><path d="M558 336c2-4 4-8 6-11 0-1 0-1 1-2h0l1-1c-3 6-5 12-7 18l-5 19-2-2c0-3 1-5 2-7v-3l4-11zm27-87c5-2 11-2 15 0 6 2 9 6 12 11l1 5-1 1c-1-4-3-8-6-11-5-4-11-5-18-5-1-1-1-1-3-1z" class="v"></path><path d="M541 509c1 4 1 8 0 12v9l-4-5 1-1c-1-1-1 0-1-1-1 0-2-1-2-1-1-1-1-2-2-2 0 0-2-1-1-1v-2c1-1-1-4-2-5h1l2 2 1-1c0 1 1 2 1 3l-1 1v1c2 0 2 1 3 2 1 0 1 0 2 1l1-1c2-2 1-8 1-11z" class="d"></path><path d="M525 493l-3-18c2 5 5 10 7 16v1 3c0 2 0 3 1 5l-1 1-2-2v-1l1-1-3-4zm-4 5c0-1-1-2-1-3v-1-1h-1 0v6h0l-1-45h1c0 12 0 23 1 34 1 6 3 12 4 18l-1 1c-1-3-1-6-1-9h-1z" class="P"></path><path d="M507 363l2 1h0v3l1 8 1-1v-4 21h0l-1 10h-1-1v-1c2-12 0-25-1-37z" class="q"></path><path d="M511 275c1 1 1 2 2 3 1-1 1-2 1-3v9 38l-1-1c-1 1-1 2-1 4v-10-9h0c1-2 1-3 0-5v-1c1 1 0 1 1 1v-5h-1v-7l-1-8v-6z" class="AS"></path><path d="M511 275c1 1 1 2 2 3 1-1 1-2 1-3v9c-1 4-1 8-1 12h-1v-7l-1-8v-6z" class="AO"></path><path d="M527 499l2 2 1-1v2c0 1 0 1 1 2l-1 1-1 1 1 4 2 1c1 0 1 0 1 2h1l-1 1-2-2h-1c1 1 3 4 2 5v2c-1 0 1 1 1 1 1 0 1 1 2 2 0 0 1 1 2 1 0 1 0 0 1 1l-1 1c-2-2-6-4-8-7h0c-3-5-3-11-3-16 0-1 1-2 1-3z" class="N"></path><path d="M528 482c-2-4-4-9-5-13v-1c-1-4-1-7-1-11l1 4 8 17c0 4 2 7 1 11l-4-7z" class="q"></path><path d="M641 165l1-1c0-1 0-2 1-2v-1l-1-1h1 1 0c2-1 3-1 5-1 2 4 2 5 2 9-1 0-1 0-1 1-1-1-1-1-1-2h-2l-1-1v-1l-1 1-1 1 1 1v1h-1c-1 0-1 0-2 1h-1l-1 1v2h-2l-2 2c0-1 1-2 2-4h0l-1-3c2 0 2 1 4 1-1-2-1-2 0-4z" class="P"></path><defs><linearGradient id="AF" x1="507.88" y1="500.122" x2="518.117" y2="483.687" xlink:href="#B"><stop offset="0" stop-color="#444046"></stop><stop offset="1" stop-color="#727370"></stop></linearGradient></defs><path fill="url(#AF)" d="M512 464c0-1 1 0 1 0l1-5c0 4 1 10 0 14l1 30c-1 1-2 1-3 2v-34-7z"></path><path d="M512 464c0-1 1 0 1 0l1-5c0 4 1 10 0 14-1 2 0 4-1 6 0-3 1-5-1-8v-7z" class="AL"></path><path d="M498 452l1 1c1-1 1 0 1-1 1 1 1 1 1 2h1v2h2v1h1c1 1 1 0 1 1v2 2c-1-1-1-1-1-2-1 1-1 2-1 3l-1 1-2 1c1 1 1 1 2 1l-1 2v4 1c1 0 1 0 2 1h-1c-3-1-1-1-2-4-1 1-1 1-2 1l-1-1v-4l1 1 1-1h-1v-3-1c0-3-1-5-2-8v-1l1-1z" class="d"></path><path d="M498 452l1 1c1-1 1 0 1-1 1 1 1 1 1 2h1v2h2v1h-1c0 2 0 2-1 4v1c0 1-1 2-2 3v-2l-1-1c0-3-1-5-2-8v-1l1-1z" class="b"></path><path d="M498 452l1 1c1-1 1 0 1-1 1 1 1 1 1 2h1v2h2v1h-1 0l-1 1h-1v-1l-3-3h-1v-1l1-1z" class="N"></path><path d="M547 306c0 2 0 4-1 6 0 3 1 6 0 9l1 1v-2l1-1v1 5 2c0 9 0 15 4 22v1 1c-1-1-2-3-3-4-1-2-2-3-3-5-1-7-2-14-1-22 0-5 1-9 2-14z" class="AL"></path><path d="M490 305c-1 0-1 0-1-1-5-11-10-20-17-30 2 1 4 4 6 6 5 6 9 13 14 21 0 1 1 2 2 3v2c-1 0-2 1-3 2l-1-3z" class="AD"></path><path d="M490 305h0c1-1 2-1 4-1v2c-1 0-2 1-3 2l-1-3z" class="v"></path><path d="M552 357l2 2-2 9c-1 2-2 5-2 7 0 1-1 1-1 2-1 2-1 4-1 6l-2 13c0-2 0-3-2-5l1-4-5-5 1-1c1 2 3 2 5 4l4-21 1-5 1-2z" class="AT"></path><path d="M528 482l4 7 2 6-1 1v1 4c0 1 2 2 2 3 1 1 1 1 1 2v1c1 2 1 5 3 7l-1 1c-1 0-1 0-1-1-2-2-2-3-2-5-1 1-1 2-2 4 0-2 0-2-1-2l-2-1-1-4 1-1 1-1c-1-1-1-1-1-2v-2c-1-2-1-3-1-5 1 0 0 0 1-1h1v1 1l1 1h0v-3-1c1 1 0 1 1 1v-1l-1-1c0-1-1-2-1-3l-1-2-2-4v-1z" class="Y"></path><path d="M531 504l2-1v1c0 1 0 1 1 2-1 0-1 0-2-1v6l-2-1-1-4 1-1 1-1z" class="d"></path><path d="M466 310c-2-3-2-7-4-10s-5-7-7-11c1-1 1-1 2-1 2 2 3 4 5 6 1 2 5 4 6 7l3 6-1 1v-1c0-2-1-3-2-4s-1 0-1-1l-1 1c1 2 3 4 4 6h0l-1-1-1 1c1 1 1 2 1 2v1h-1l-2-2zm58 33l2 1c1 1 1 0 0 1 0 3 1 6 0 8v2 1c0 1 0 1-1 2v2c0 1 0 1-1 2v1c-1 2-2 3-1 6l1 2-1 1c0 1 0 1-1 2l-2 13c0 2-1 4-1 6s1 6 0 7c-1-1-1 0-1-1v-3h0c1-3 0-6 1-9v-6c-1-2 0-4 0-7v-1h1l4-14v-1c0-2 1-4 1-6s0-4-1-6h0v-3z" class="P"></path><path d="M469 293c-1-1-1-2-2-3h-1l1-1c1 1 2 2 2 3l3 3c1 3 2 5 3 7v1c1 1 1 2 1 3l1 1 1 5v4h-1-1v2c-1-1-3-4-4-3 1 1 1 1 0 3l-3-5c-1 0-1-1-1-1h1v-1s0-1-1-2l1-1 1 1h0c-1-2-3-4-4-6l1-1c0 1 0 0 1 1s2 2 2 4v1l1-1c1 1 3 3 5 4 0-2-1-5-2-7v-1c-1-2-1-3-3-5l-2-2 1-1-1-2z" class="N"></path><path d="M626 190s0-1 1-1c1-1 2-2 3-2l1 1-1 2v2l1-1c1 1 1 1 2 1 1 1 1 2 2 2 2 0 2-1 3-2v-1c1-1 2-2 3-2-3 5-8 10-11 14-3 3-5 6-8 8 1-2 1-3 2-5v-2c1-2 2-4 4-6l3-3v-1h-1c-1-1-1-1-1-2h-1v-1h-2v-1z" class="b"></path><path d="M624 206l3-3c1-2 2-3 3-4v1 2 1c-3 3-5 6-8 8 1-2 1-3 2-5z" class="d"></path><path d="M490 425l3 3h1v-2l1 1h0c0 1 1 2 1 2v1l-3 5 1 1h2v1l-1 2-1 1-1 1 1 1-1 1h-1c0 3 0 6-1 9 0 1 1 3 1 4l-1 1v-1h-1c0-1-1-2-1-3 1-4-1-10-1-15l1 2h1l1-2-1-13z" class="C"></path><path d="M491 438c1 2 0 8 0 10v6c-1 0-1-1-2-1 1-4-1-10-1-15l1 2h1l1-2zm14-57l1 2h0c-1 1-1 3-2 4v1s-1 1-1 2h-1c-1 3-2 3-5 4h0c-1 1-1 1-2 1l-1 1c1 0 1 1 2 0 0 1 0 0 1 1v-1c2-1 4-2 5-4s2-3 3-4c2 0 1 0 2 1 0 1 1 1 0 2v3l-1 1 1 2-1 1c0 3 1 8 0 11v1h-1v-7c1-4 0-6 1-10v-4h0l-1 1c-1 2-3 3-4 4l-2 2c-2 1-4 3-5 5v1h-1c-1-1-1-2-1-3l-1-1c-1 0-1 1-2 1 0-1-1-2-2-3h0v-1c0-1 0-2 1-3v1c1 1 2 1 4 2 1-2 3-3 5-4l-2 2c-1 0-1 1-2 2h1c6-4 8-8 11-14z" class="Y"></path><path d="M554 319l2 8 1 2c0 1 1 1 2 1-1 2-1 4-1 6l-4 11c0-1 0-3-1-4 1 0 0-1 0-1v-6l-1-2c-1-4-1-7-1-11h0c0-2 2-3 3-4z" class="Z"></path><path d="M554 319l2 8 1 2c0 1 0 2 1 4h-1c-2 0-2-1-3-2 1-1 1-1 1-2-1-1-2-2-2-4v-1l-2-1h0c0-2 2-3 3-4z" class="U"></path><path d="M545 288s1 0 1 1c-6 13-10 27-13 42l-4 16v-7c0-1 1-1 1-2 0-3 0-6 1-10 2-11 5-21 10-32l1-2c1-1 2-4 3-6z" class="AO"></path><path d="M486 388l1 2c1 0 2-1 3 0h3c1-1 3-2 3-3 5-5 5-12 9-18 0 4 1 8 0 12-3 6-5 10-11 14h-1c1-1 1-2 2-2l2-2c-2 1-4 2-5 4-2-1-3-1-4-2v-1c-1 1-1 2-1 3-1-2 0-5-1-7z" class="r"></path><path d="M505 417c2 1 1 2 2 4l-2 9c-1 4-2 8-4 11-1 1-1 2-1 3-1 0-1-1-1-1-1-1-1-2-2-3 0-1 0-2-1-3v-1h-2l-1-1 3-5v-1h1c0 1 0 2-1 3l-1 2h1c1-1 2-3 3-4v-1-2l2-2c1-1 1-1 1-2 1-1 1-2 3-4v-2z" class="g"></path><path d="M505 417c2 1 1 2 2 4l-2 9c-1-1-1-2-2-3 0-1 1-2 1-3v-1c0-2 0-2 1-3v-1-2z" class="q"></path><path d="M501 425c0 1 1 1 0 2l-1 1v1c1 3-2 7-2 10 0 1 1 3 1 4-1-1-1-2-2-3 0-1 0-2-1-3v-1h-2l-1-1 3-5v-1h1c0 1 0 2-1 3l-1 2h1c1-1 2-3 3-4v-1-2l2-2z" class="l"></path><path d="M551 292c1 0 1 0 1 1h1v-2l1-1c0 1 0 1 1 2h0c1-1 0-1 1 0h0c0 2 0 3-1 5-1 1-1 1-1 2 0 2-1 3-1 4l-4 16-1 1h0v-1l-1 1v2l-1-1c1-3 0-6 0-9 1-2 1-4 1-6v-2-2c1-4 2-7 4-10zm-15 202l1 1 1 2 1-1v-1h1c1 3 2 6 0 8 1 2 1 4 1 6 0 3 1 9-1 11l-1 1c-1-1-1-1-2-1-1-1-1-2-3-2v-1l1-1c0-1-1-2-1-3h-1c1-2 1-3 2-4 0 2 0 3 2 5 0 1 0 1 1 1l1-1c-2-2-2-5-3-7v-1c0-1 0-1-1-2 0-1-2-2-2-3v-4-1l1-1 1 1 1-2z" class="Z"></path><path d="M536 494l1 1 1 2 1-1v-1h1c1 3 2 6 0 8-2-2-3-5-5-7l1-2zm-2-192c1-1 2-2 2-3v-1c1-2 1-3 2-4l3 2c-5 11-8 21-10 32-1-1-1-1-2-1h-1c0-2 1-3 1-5v-4h-1v3h-2c1-2 1-3 1-5l1 1h1v-1c1-2 1-3 2-5 0-1 0-2 1-3l2-6z" class="P"></path><path d="M531 311l2 2c0 2 0 4-2 6h0l-1 3v2c-1 1-1 2-1 3h-1c0-2 1-3 1-5v-4h-1v3h-2c1-2 1-3 1-5l1 1h1v-1c1-2 1-3 2-5z" class="l"></path><path d="M580 293c2-3 5-8 7-9 0 3-1 4-2 6l-7 14c-1 1-2 2-2 3-1 5-5 9-6 13l-6 12c-2 3-2 6-5 8 2-6 4-12 7-18 2-2 3-6 4-9l1-1 1-1c0-1 0-1 1-2 0-1 1-1 1-3h0l2-2-2-1c1-3 4-7 6-10z" class="P"></path><path d="M519 337h0 1 1l1-2v-1c0-2 0-2-1-3v-1c1-1 1-1 2-1l1-2h1c0 2 0 3-1 4v2l-2 2v1c0 3-1 4-1 7l1 1c0-1 1-1 1-2s0-1 1-1v2 3h0c1 2 1 4 1 6s-1 4-1 6v1l-4 14h-1c1-2 1-3 1-5l-1-31z" class="q"></path><path d="M469 293h-1-1c-1-1 0-1-1-2s-2-2-3-4c1 0 1 0 2-1l-1-2h0c1-1 0-1-1-2h1 2v-1h2v-1h-1 0 2v2l-2 1 2 1v1h1c1-1 0-1 1-1l2 2 2 3 1 3h0v1c0 1 0 2 1 4l-1 1h1c1 2 1 3 2 5 0 2 0 7-1 9h0l-1-5-1-1c0-1 0-2-1-3v-1c-1-2-2-4-3-7l-3-3c0-1-1-2-2-3l-1 1h1c1 1 1 2 2 3z" class="AJ"></path><path d="M558 266c1 1 2 1 3 1h0c-6 6-10 14-14 21-1 1 0 1-1 1 0-1-1-1-1-1l1-3-3-2v-1c1-1 1-2 1-3h-1l-1-2c1-1 1-1 3-1 0-1 2-2 3-3l2-2c2-1 3-3 5-4h3v-1z" class="AL"></path><path d="M543 282c1 0 2 0 2-1 1-1 2-1 4-2l-3 6-3-2v-1z" class="b"></path><path d="M548 273l2-2c2-1 3-3 5-4h3c-3 3-6 7-9 12h0c-2 1-3 1-4 2 0 1-1 1-2 1 1-1 1-2 1-3h-1l-1-2c1-1 1-1 3-1 0-1 2-2 3-3z" class="P"></path><path d="M545 276v1c1 0 1 0 2-1v1l1 1c1 0 1 0 1 1h0c-2 1-3 1-4 2 0 1-1 1-2 1 1-1 1-2 1-3h-1l-1-2c1-1 1-1 3-1z" class="Z"></path><path d="M659 190l1 2-20 21c-4 5-7 8-12 12-1 0-1 0-2 1h39 6c-6 1-13 0-19 0-12 0-24 1-36 0l2-1c1 0 1-1 2 0h3c1-1 1-1 2-1 3 0 6-5 8-7l26-27z" class="w"></path><path d="M521 498h1c0 3 0 6 1 9l1-1c1 4 1 12 4 15 2 3 4 5 7 7l6 3c-1 0-3 0-5-1-4-2-7-4-8-8l-1-1h0c-1 4 2 8 4 11 2 2 5 4 5 7v2l-1 1c-1-1-1 0-1-2-1-1-2-3-4-4v3c1 1 2 2 2 4h0c-1-1-2-2-2-3l-1-1v-3-1s-1 0-1-1c-1 0-2-2-2-2-1-5-2-11-4-16 0-2 1-4 0-6v-1-1c-1-2-1-2-1-3 1-1 1-1 0-2v-5z" class="d"></path><path d="M627 174c1 0 1-1 2-1 1-2 1-2 2-3-1 2-2 3-3 5l-1 3c-1 1-2 2-3 4h-1c-1 1-1 1-1 2l-1 1c-2 2-3 4-4 5l-1 1c-1 1-2 3-4 4l-8 7h1c-3 3-5 4-8 6-3 1-5 4-7 6-2 0-3 2-5 3-1 1-4 1-5 1 1-2 4-3 6-4 3-2 5-3 7-5l4-3c3-1 6-4 9-7 1-1 2-3 3-5h1v-1l-2-1v-1h0l1-1c2-1 2-2 2-4 1-2 6-6 7-7 0 2 0 3-2 5l-1 1 1 1 1-1h2v-1l3-3h1c2-2 3-4 4-7z" class="C"></path><path d="M618 179c0 2 0 3-2 5l-1 1 1 1 1-1h2v-1l3-3h1c-5 6-10 14-17 18 1-1 2-3 3-5h1v-1l-2-1v-1h0l1-1c2-1 2-2 2-4 1-2 6-6 7-7z" class="P"></path><path d="M526 321h2v-3h1v4c0 2-1 3-1 5h1c1 0 1 0 2 1-1 4-1 7-1 10 0 1-1 1-1 2v7c1 1 1 1 0 2v2c-2 6-3 12-6 18-1-3 0-4 1-6v-1c1-1 1-1 1-2v-2c1-1 1-1 1-2v-1-2c1-2 0-5 0-8 1-1 1 0 0-1l-2-1v-2c-1 0-1 0-1 1s-1 1-1 2l-1-1c0-3 1-4 1-7v-1l2-2v-2c1-1 1-2 1-4v-2c1-1 1-2 1-4z" class="Y"></path><path d="M585 280v-1h3l1 1v-1l5-5 1 1v1h-1c0 1-1 2-2 3-1 2-2 3-2 5l-1 2-2 4c-2 2-3 4-4 6l-3 5v1h0c-1 1-1 2-2 2l7-14c1-2 2-3 2-6-2 1-5 6-7 9-4 1-5 3-7 6h-1-1v-1l-1-1c-1-1 1-2 1-2 0-1 1-2 1-3h-1c-1 1-1 1-2 1h-1c-1-2-2-2-3-2h1c2 0 6-2 8-3l2-1c3-1 4-4 7-5 2 0 3-1 4-2h-2z" class="Z"></path><path d="M576 287c3-1 4-4 7-5h0l-2 4c-1 1-3 2-5 3-1 1-2 3-4 3h-1c-1 1-1 1-2 1h-1c-1-2-2-2-3-2h1c2 0 6-2 8-3l2-1zm65-146c0 4-1 6-3 10v3l-1 1v1h1v3h0c-1 1-1 1-1 2l-1 1-1 1c0 2-1 3-2 4h0c0 1-1 2-2 3s-1 1-2 3c-1 0-1 1-2 1-1 3-2 5-4 7h-1l-3 3v1h-2l-1 1-1-1 1-1c2-2 2-3 2-5l7-10c1-2 4-5 5-7 2-2 2-4 3-5 4-4 6-11 8-16z" class="U"></path><path d="M618 179l7-10 1 1v1c-1 2-5 6-6 8l1 1 3-2c1-1 1-2 2-2 0-2 0-2 1-2-1 3-2 5-4 7h-1l-3 3v1h-2l-1 1-1-1 1-1c2-2 2-3 2-5z" class="N"></path><path d="M676 204v-2c1-1 1-2 2-4l-1 8v1c2 1 2 4 3 5l2 6c-2 3-3 5-3 8h-8-6l1-1c3-7 7-14 10-21z" class="L"></path><path d="M676 204c1 3-2 9-3 12s-3 6-5 9h-2c3-7 7-14 10-21z" class="F"></path><path d="M572 431c0-1 0-2 1-3v-3-1c1-1 0-2 1-3v-1c1-2 0-1 1-2h0v-2c0-1 0-1 1-2v-3c0-1 0-1 1-2v-2-1c1-2 0-4 1-6v-1-1-1c1-2 1-3 1-5v-1c1-1 2-5 2-6s0-2 1-3v-2h0c1-2 1-3 1-4 0-2 1-2 0-4h0l1-1v-1c0-1 0-3 1-4v-1-3-1l1-1v-4c0-1 0-3 1-3v-3h0c0-1 1-2 0-4h0l1-1v-1c0-1 0-2 1-3 0-3 0 1 0-1l1-2c0-2 0-5 2-6v-1l-1-1h0c1-1 1-1 2 0 0 1 0 2-1 3v2c-1 1-1 1-1 2v1 1c-1 1-1 3-1 4v1h-1c0 3 0 6-1 8-1 3 0 6-1 9-1 2-1 4-1 6 0 3-1 5-1 7v1 1c-1 1-1 1-1 2h1v-4c1-1 1-2 1-3v-1c1-1 0-3 1-4 0 1 1 1 1 2-1 3-1 0-1 2s-1 5-1 7l1-1v-2c2-2 1-3 2-5 0-2 0-4 1-5 0-1 0-2 1-3v-3c1-2 1-3 2-5-1 2-1 4-1 6l-7 27c-2 3-3 7-4 10-1 2-1 2-2 3-1 3-1 6-1 9-1 4-2 7-2 10-1 2-1 3-1 5 0 1 0 0-1 1v1c0 1 0 2-1 3v1c0 1 0 2-1 2z" class="H"></path><path d="M628 192h1c0 1 0 1 1 2h1v1l-3 3c-2 2-3 4-4 6v2c-1 2-1 3-2 5l-3 3c-3 3-6 5-9 8-2 1-4 2-5 4h-11c3-1 6 0 9-1l3-3c2-1 4-3 5-5l-2-3v-2c1-1 2-2 2-3 0 0 0-1-1-1v-1c1-1 1-1 3-1l2-1h3c1-2 1-3 0-4v-1l1-1 1 1 1 1v-1h2l1-1c0 1 0 1 1 1 1-1 1-2 2-4 0-1 1-2 1-4z" class="g"></path><path d="M624 204v2c-1 2-1 3-2 5l-3 3c-1-2 1-5 2-7 1 0 2-2 3-3z" class="P"></path><path d="M610 208v-1c1-1 1-1 3-1l2 2c1 1 2 2 1 3 0 2-2 3-3 4h-1c0-1 1-2 1-3l-2-3s0-1-1-1z" class="Y"></path><path d="M628 192h1c0 1 0 1 1 2h1v1l-3 3c-2 2-3 4-4 6-1 1-2 3-3 3l-2 2c0-1 0-2 1-3v-1h-1l-1 1c-1 0-2 1-2 1l-1 1-2-2 2-1h3c1-2 1-3 0-4v-1l1-1 1 1 1 1v-1h2l1-1c0 1 0 1 1 1 1-1 1-2 2-4 0-1 1-2 1-4z" class="l"></path><path d="M507 421l1-7c1 6 0 13 1 19v15 6h-1v-3-3h-1 0c-1 0-1 0-1 1h0c1 2 0 9 0 11v-2c0-1 0 0-1-1h-1v-1h-2v-2h-1c0-1 0-1-1-2 0 1 0 0-1 1l-1-1c0-1 0-2 1-3 0-1 0-2 1-3v-2h0c0-1 0-2 1-3 2-3 3-7 4-11l2-9z" class="X"></path><path d="M505 439c1 2 0 4 0 6-1 1-1 3-1 4-1 2-1 3 0 6v1h-2v-2h-1c0-1 0-1-1-2 0-1 1-2 2-3-1-3 2-7 3-10z" class="b"></path><path d="M500 444l5-11v6c-1 3-4 7-3 10-1 1-2 2-2 3s0 0-1 1l-1-1c0-1 0-2 1-3 0-1 0-2 1-3v-2z" class="l"></path><path d="M594 216l1-1h0c1 0 1-1 2-1 2-1 0 1 1-1l3-2v-1l1-1c0-1-1 0 0-1l1-1c1-1 2-3 4-3v-1c1 0 1-1 2-1l1-1 2-2c1-1 2-1 2-2 1-1 1-2 1-2l1-1 1-1c0-1 1-1 1-2 1 0 1-2 2-2l2 1c1-1 3-4 4-5v1c-1 1-1 2-2 3h0c1 1 1 1 2 1v1h2v1c0 2-1 3-1 4-1 2-1 3-2 4-1 0-1 0-1-1l-1 1h-2v1l-1-1-1-1-1 1v1c1 1 1 2 0 4h-3l-2 1c-2 0-2 0-3 1v1c-1 1-3 1-5 1 0 1-1 2-2 3h-2c-2 1-3 3-5 4h-2z" class="U"></path><path d="M616 198c1-2 3-4 5-6h1 1v-1h0l1-1v3c0 1 1 2 2 3h1c-1 2-1 3-2 4-1 0-1 0-1-1l-1 1h-2v1l-1-1c1-2 0-1 0-2v-2h-2v1h1v1h-3z" class="P"></path><path d="M616 198h3v-1h-1v-1h2v2c0 1 1 0 0 2l-1-1-1 1v1c1 1 1 2 0 4h-3l-2 1c-2 0-2 0-3 1v1c-1 1-3 1-5 1l3-3c1-2 2-3 3-5 2-2 3-3 5-3z" class="Z"></path><defs><linearGradient id="AG" x1="622.269" y1="292.259" x2="602.521" y2="305.886" xlink:href="#B"><stop offset="0" stop-color="#8b887b"></stop><stop offset="1" stop-color="#bcb8b6"></stop></linearGradient></defs><path fill="url(#AG)" d="M612 260c4 6 4 15 5 21 0 3 1 5 1 7h1v-1-2c1-3 0-3 2-5-1 6-3 12-5 18l-5 22v-1c-1-1-1-1-1-2-1 0-1 2-2 3 0-2 2-4 1-6v1h0v-5-2c4-15 6-28 3-42l1-1-1-5z"></path><path d="M449 328c2 1 3 5 4 7v2c0 1 0 0 1 1v1l2 5 1 4v2c1 0 0 0 1 1v1 1h1v1h0v2c0 1 1 2 1 3v1h1v2 1l1 1v2 1h1 0v2l1-1c1 2 2 3 2 5 0 1 0 1 1 2h0v2c1 1 1 1 1 2v-3c-1-1-1-2-1-4l-1-1v-1c1 0 2 2 2 3v1c0 1 1 1 1 2v-3c-1-1-1-1-1-2s-1-3-1-4v-2c2 7 3 14 5 21l4 21c1 3 2 8 1 12l-28-91h0z" class="AC"></path><path d="M610 208c1 0 1 1 1 1 0 1-1 2-2 3v2l2 3c-1 2-3 4-5 5l-3 3c-3 1-6 0-9 1h-14c2-2 2-2 4-2 1-2 2-2 3-3 3 1 3 1 6 0l2-1h0c1 0 2-1 2-2l-1-1v-1c2-1 3-3 5-4h2c1-1 2-2 2-3 2 0 4 0 5-1z" class="Y"></path><path d="M610 208c1 0 1 1 1 1 0 1-1 2-2 3v2c0 1 0 2-1 4l-1-1-1-1c0-1-1-1-1-1l-3-1 1-2c1-1 2-2 2-3 2 0 4 0 5-1z" class="b"></path><path d="M606 216l1-5v-1h1l1 2v2c0 1 0 2-1 4l-1-1-1-1z" class="Z"></path><path d="M596 216c2-1 3-3 5-4h2l-1 2v1c1 1 2 2 3 2v1h-2c-2 0-3 1-4 2v1 2h-2l-1-1v-1l-2 2-1-1 2-2h0c1 0 2-1 2-2l-1-1v-1z" class="P"></path><path d="M587 221c3 1 3 1 6 0l2-1-2 2 1 1 2-2v1l1 1h2v-2h2l1 1v1h0c1 1 0 1 1 2-3 1-6 0-9 1h-14c2-2 2-2 4-2 1-2 2-2 3-3z" class="Z"></path><path d="M599 221h2v2l-1 1-1-1v-2z" class="X"></path><path d="M458 311c2 0 3 2 5 3l2 5h0 1c3 1 2 4 5 6 0-1-1-2-1-3v-1l1-1 1 1c0 1 1 2 2 2 1 2-1 3 2 4 0 1 0 1 1 2v1l1 1c0 1 0 1-1 2v3 5c1 0 1 0 1 1l-2 3h-1l-1-1c0-1 0-1-1-1 0-1-1-1-1-1-1 0-2-3-2-4-2-3-3-8-5-11l-7-16z" class="C"></path><path d="M477 336c-3-2-1-4-3-6 0-2-1-5-3-7h0l1-2c0 1 1 2 2 2 1 2-1 3 2 4 0 1 0 1 1 2v1l1 1c0 1 0 1-1 2v3z" class="U"></path><path d="M458 311c2 0 3 2 5 3l2 5h0l5 9-1 2h1c0 1 0 2 1 3 1 3 2 7 3 11 0-1 0-1-1-1 0-1-1-1-1-1-1 0-2-3-2-4-2-3-3-8-5-11l-7-16z" class="AT"></path><defs><linearGradient id="AH" x1="428.241" y1="268.029" x2="462.123" y2="308.504" xlink:href="#B"><stop offset="0" stop-color="#6f6b61"></stop><stop offset="1" stop-color="#b4aaa4"></stop></linearGradient></defs><path fill="url(#AH)" d="M436 295l-1-14c0-9 2-20 9-27h1l1 2c-7 10-9 21-8 33 2 11 5 21 8 31 1 2 2 6 3 8h0l-1 1v4l-9-27-1-3c-1-2-1-5-2-8z"></path><path d="M583 210c1 1 1 1 1 2 1-1 1-2 3-2v1c2-2 4-3 5-4h1v2c-2 2-4 3-7 5-2 1-5 2-6 4l-1 1-17 9c-4 1-7 3-10 4-6 1-11 4-16 6 1-1 2-3 3-4v-1h0l1-1c2 0 2-1 4-1 1-1 1-1 2-1 1-1 2 0 3-1h3 0c2-1 3-2 4-3l1-1c0 1 1 1 1 1l2-2v-1l1-1h1c1-1 0-1 2-2h1l2-1v1c1-1 1 0 1-1h1c3 0 4-3 7-3h0c2-3 5-5 7-6z" class="g"></path><path d="M512 325c0-2 0-3 1-4l1 1v127h0l-1 4h0v-1c0-3-1-6-1-8 0-5 1-10 0-15-1-3-1-7-1-10v-28-21l1-45z" class="v"></path><defs><linearGradient id="AI" x1="480.945" y1="432.907" x2="495.373" y2="413.833" xlink:href="#B"><stop offset="0" stop-color="#514a45"></stop><stop offset="1" stop-color="#8c8c82"></stop></linearGradient></defs><path fill="url(#AI)" d="M486 383h0v5c1 2 0 5 1 7v1h0c1 1 2 2 2 3 1 0 1-1 2-1l1 1c0 1 0 2 1 3h1c0 2 0 3-1 5v1c0 1 0 2-1 3 0 2 1 3 1 5l2 3-2 2h0c-1 0-2 0-3-1v5h0l1 13-1 2h-1l-1-2-1-14c1-8-1-18-2-26v-1l-1-7c-1-2-1-3-1-5h2c1-1 1-1 1-2z"></path><path d="M486 383h0v5c1 2 0 5 1 7v1 1c-1 2 0 3-1 4v-3h-1v-1l-1-7c-1-2-1-3-1-5h2c1-1 1-1 1-2z" class="AY"></path><path d="M489 412v-1-1c1 0 2 1 3 1 0 2 1 3 1 5l2 3-2 2h0c-1 0-2 0-3-1l-1-8z" class="X"></path><path d="M487 396h0c1 1 2 2 2 3 1 0 1-1 2-1l1 1c0 1 0 2 1 3h1c0 2 0 3-1 5v1c0 1 0 2-1 3-1 0-2-1-3-1v1 1l-1-7-1-8v-1z" class="P"></path><path d="M488 405c1-2 1-2 3-4v1c0 3 0 4 2 6 0 1 0 2-1 3-1 0-2-1-3-1v1 1l-1-7z" class="Z"></path><path d="M574 303l2 1-2 2h0c0 2-1 2-1 3-1 1-1 1-1 2l-1 1-1 1c-1 3-2 7-4 9l-1 1h0c-1 1-1 1-1 2-2 3-4 7-6 11 0-2 0-4 1-6-1 0-2 0-2-1l-1-2-2-8 1-1h0c0-2 1-3 2-4 3-1 5-4 8-5 1-1 2-2 4-2 1 0 0 0 1-1 1 0 1 0 2-2 0-1 0 0 1-1v1l1-1z" class="N"></path><path d="M555 318l1 1h0l-1 1 2 2c1 0 1 1 2 2l-3 3-2-8 1-1z" class="Y"></path><defs><linearGradient id="AJ" x1="563.749" y1="312.769" x2="569.04" y2="318.503" xlink:href="#B"><stop offset="0" stop-color="#353031"></stop><stop offset="1" stop-color="#43423a"></stop></linearGradient></defs><path fill="url(#AJ)" d="M574 303l2 1-2 2h0c0 2-1 2-1 3-1 1-1 1-1 2l-1 1-1 1c-1 3-2 7-4 9l-1 1h0c-1 1-1 1-1 2-2 3-4 7-6 11 0-2 0-4 1-6 1-1 2-3 2-4v-1l3-8c1 0 1-1 1-2 1-2 2-3 3-5v-1l-2 2-1-2c1-1 2-2 4-2 1 0 0 0 1-1 1 0 1 0 2-2 0-1 0 0 1-1v1l1-1z"></path><path d="M464 337h0c-6-9-9-20-13-30l-2-3c0-1-1-2-1-4 0-3-2-8-3-11-1-1-2-2-2-3 0-2-1-4-2-6l10 13v1l1 1c-1 2-1 3 0 5l4 8s1 2 2 3l7 16c-1 1 1 4 2 6v3 3 1c0-1 0-1-1-2s-1-1-2-1z" class="C"></path><path d="M568 293h1c1 0 1 0 2-1h1c0 1-1 2-1 3 0 0-2 1-1 2l1 1v1h1 1c2-3 3-5 7-6-2 3-5 7-6 10l-1 1v-1c-1 1-1 0-1 1-1 2-1 2-2 2-1 1 0 1-1 1-2 0-3 1-4 2-3 1-5 4-8 5l-1-2c0 1-1 1-1 1l-2 2v-1c1-2 1-4 2-5s2-2 2-3l-1-1h0l4-4h0v-1c-1 0-1 1-2 0v-1c3-2 6-4 10-6z" class="X"></path><path d="M571 298v1h1c-1 1-1 1-2 1-1 1-2 1-2 2l-1 1-1-1-1 1c-1 0-2 1-3 1 1-1 5-5 7-6h2zm-15 14s1-1 1-2c2 0 3-1 5-1l8-5h2c-1 2-1 2-2 2-1 1 0 1-1 1-2 0-3 1-4 2-3 1-5 4-8 5l-1-2z" class="C"></path><path d="M465 327c2 3 3 8 5 11 0 1 1 4 2 4 0 0 1 0 1 1 1 0 1 0 1 1l1 1h1l2-3h1 0c0 3-3 3-2 6h0v2c1 1 1 3 1 5l-2 2 4 12h-2l-1 1v1 2h-2c0 2 0 3-1 5l-1-4-6-27v-1l2-1c0-2-1-2-1-3-1-1-1-1-1-2v-1-3-3c-1-2-3-5-2-6z" class="U"></path><path d="M467 347l2-1h1v1 1c1 1 1 2 1 3h0c1 2 0 3 1 5 0 1-1 1 0 2v6c1 3 3 8 1 10l-6-27z" class="P"></path><path d="M474 358c0-2 1-4 0-6h1l1 5 4 12h-2l-1 1v1 2h-2v-3c-1-3-1-5-1-8 1-1 0-3 0-4z" class="b"></path><path d="M474 362c1 0 2 1 2 2 1 1 0 2 1 4-1 1-1 2-2 2-1-3-1-5-1-8zm-9-35c2 3 3 8 5 11 0 1 1 4 2 4 0 0 1 0 1 1 1 0 1 0 1 1l1 1h1l2-3h1 0c0 3-3 3-2 6h0v2c1 1 1 3 1 5l-2 2-1-5h-1c1 2 0 4 0 6h0v-4c-1-1-1-1-2-3 0-1-2-6-3-6 0-2-1-2-1-3-1-1-1-1-1-2v-1-3-3c-1-2-3-5-2-6z" class="Z"></path><path d="M479 342h0c0 3-3 3-2 6h0v2c1 1 1 3 1 5l-2 2-1-5-3-10s1 0 1 1c1 0 1 0 1 1l1 1h1l2-3h1z" class="AD"></path><path d="M506 298l3 2c0 2 0 2-1 4h1c1 0 1 0 2-1 0-1 0-2-1-2l1-1 1 1c1 2 1 3 0 5h0v9 10l-1 45v4l-1 1-1-8v-3h0l-1-3c-1-2-1-4-1-5l1-1 1 2v3l1-1v-7c-1-1-1-2-1-3l-2-18c-1-1-1-5-1-6 1-2 0-4 1-6h0l-1-1c0-1 0-4 1-6 1-3 0-8-1-11v-3z" class="l"></path><path d="M507 331c1-2-1-4 1-5 1 1 1 2 1 4l1 22c-1-1-1-2-1-3l-2-18z" class="AL"></path><path d="M506 298l3 2c0 2 0 2-1 4h1c1 0 1 0 2-1 0-1 0-2-1-2l1-1 1 1c1 2 1 3 0 5h0v9c0-2 0-3-1-4h-1l-1 3v3 13c0-2 0-3-1-4-2 1 0 3-1 5-1-1-1-5-1-6 1-2 0-4 1-6h0l-1-1c0-1 0-4 1-6 1-3 0-8-1-11v-3z" class="AP"></path><path d="M510 311h-1v1c-1 2-1 4-2 5h0c0-2 1-4 1-5 0-3 0-6 2-7v1h0 1l1-1v1 9c0-2 0-3-1-4h-1z" class="r"></path><path d="M522 395c1 0 1-2 1-2h1l1 2v6h0l-1-1v5 9 3c0 1 0 1 1 1v2h0v3c1 2 1 4 1 6 0 3 0 7 1 10 1 4 1 8 3 12 0 2 1 5 2 7l3 10c2 4 5 10 4 15-2-3-4-8-7-11-3-4-4-10-6-15-2-6-3-15-3-21l-1-41z" class="g"></path><path d="M523 436c1 2 2 5 2 8l1 1c0 2 0 4 1 6v1 1 2c0 1-1 2-1 2-2-6-3-15-3-21z" class="l"></path><path d="M530 291h0c1 0 2-1 2-3 0 0 2-1 3-1l1 1 1 1h0c0 1 1 1 1 2s0 1-1 1-1 1-2 0v1l1 1-1 1v2c0 1 0 1-1 2v3l-2 6c-1 1-1 2-1 3-1 2-1 3-2 5v1h-1l-1-1c0 2 0 3-1 5 0 2 0 3-1 4v2h-1l-1 2c-1 0-1 0-2 1v1c1 1 1 1 1 3v1l-1 2h-1-1 0v-1-3-5h0c1-3 0-6 1-8v-2c1-3 1-7 2-9l1-1v-4l1 1 1 1 1-3c0-2 2-5 2-7s1-3 2-5z" class="r"></path><path d="M527 316l3-10c0-1 0-2 1-3 1 2 0 3 1 5-1 1-1 2-1 3-1 2-1 3-2 5v1h-1l-1-1z" class="q"></path><path d="M530 291h0c1 0 2-1 2-3 0 0 2-1 3-1l1 1c0 1-1 2-2 2h0c-1 1-2 1-2 2s-1 2-1 2v4c-1 2-1 4-2 5 0 2-1 6-2 8h-1 0l1-2c0-1-1-1 0-2 0-2 0-2-1-4 0-2 2-5 2-7s1-3 2-5z" class="b"></path><path d="M531 294v4c-1 2-1 4-2 5-1-1-1-2-1-4 1-2 1-3 3-5z" class="Y"></path><path d="M519 328h0c1-3 0-6 1-8v-2c1-3 1-7 2-9l1-1v-4l1 1 1 1 1-3c1 2 1 2 1 4-1 1 0 1 0 2l-1 2h0 1l-3 16-1 2c-1 0-1 0-2 1v1c1 1 1 1 1 3v1l-1 2h-1-1 0v-1-3-5z" class="l"></path><path d="M492 301h1v1l1 1c0 1 0 0 1 2 0 1 0 1 1 2v1c1 1 1 1 1 2 1 1 2 4 2 5l1 2c1 1 1 2 1 3h1c0-1 0-1 1-2-1-1-1-1-1-2v-1l-1-1v-2c1 1 1 2 2 3v1c1 2 1 6 1 8 1 1 1 1 2 1 0 1 0 5 1 6l2 18c0 1 0 2 1 3v7l-1 1v-3l-1-2-1 1c0 1 0 3 1 5l1 3-2-1c0-4-1-8-2-12-4-14-8-29-14-43 1-1 2-2 3-2v-2c-1-1-2-2-2-3z" class="AD"></path><path d="M499 321h0l1-1c1 2 1 3 2 5v2c1 1 1 3 1 4 1 3 1 7 2 9v1c1 1 1 2 2 3 0 2 0 3 2 5 0 1 0 2 1 3v7l-1 1v-3l-1-2-1 1h0c-1-5-2-11-4-16-1-2-1-4-2-7 0-1-2-5-2-7 0 0 1-1 0-1v-4z" class="g"></path><path d="M492 301h1v1l1 1c0 1 0 0 1 2 0 1 0 1 1 2v1c1 1 1 1 1 2 1 1 2 4 2 5l1 2c1 1 1 2 1 3h1c0-1 0-1 1-2-1-1-1-1-1-2v-1l-1-1v-2c1 1 1 2 2 3v1c1 2 1 6 1 8 1 1 1 1 2 1 0 1 0 5 1 6l2 18c-2-2-2-3-2-5-1-1-1-2-2-3v-1c-1-2-1-6-2-9 0-1 0-3-1-4v-2c-1-2-1-3-2-5l-1 1h0c0-1-1-2-1-4-1-1-1 0 0-1 0 1 1 2 2 3h0l-6-13v-2c-1-1-2-2-2-3z" class="b"></path><path d="M636 175l2-2h2v-2l1-1h1c1-1 1-1 2-1h1v-1l-1-1 1-1 1-1v1l1 1h2c0 1 0 1 1 2 0-1 0-1 1-1v2c-2 7-5 13-9 19h-1c-1 0-2 1-3 2v1c-1 1-1 2-3 2-1 0-1-1-2-2-1 0-1 0-2-1l-1 1v-2l1-2-1-1c-1 0-2 1-3 2-1 0-1 1-1 1-1 0-1 0-2-1h0c1-1 1-2 2-3v-1h1l4-4 1-1c1-2 2-4 4-5z" class="d"></path><path d="M636 177l3-3h3c2-1 4-3 5-5v-1l1 1v2c-1 1-1 2-2 4v1h-1v2h0c-1-1-1-1-3-2v1c-1 0-2 0-2-1l-1-1c-1 1-1 2-1 3l-2-1zm0 4c1 1 2 2 3 2v1c-2 1-2 1-3 2s-1 1-3 2l-1-1c0 1 0 1-1 1l-1-1c-1 0-2 1-3 2-1 0-1 1-1 1-1 0-1 0-2-1h0c1-1 1-2 2-3v-1h1v1h1l2-2c0-1 0-1 1-2h1c0 1 0 1-1 2 1 1 2 2 3 2h1l-1-2h1 1v-3z" class="X"></path><path d="M638 178c0-1 0-2 1-3l1 1c0 1 1 1 2 1v-1c2 1 2 1 3 2-1 2-2 4-3 5 0 1 0 1-1 2l-1-2-1 2h-1l1-1v-1c-1 0-2-1-3-2v-1h-1l1-3 2 1z" class="U"></path><path d="M636 177l2 1c1 2 2 2 3 5h-1l-1 2h-1l1-1v-1c-1 0-2-1-3-2v-1h-1l1-3z" class="w"></path><path d="M646 176l2-1v-1c1-2 0-3 2-5h0l1 1c-2 7-5 13-9 19l-1-1v-1c-1 1 0 0-1 0h-1l2-2c1-1 1-1 1-2 1-1 2-3 3-5h0v-2h1z" class="P"></path><path d="M639 184l-1 1h1l1-2 1 2-2 2h1c1 0 0 1 1 0v1l1 1h-1c-1 0-2 1-3 2v1c-1 1-1 2-3 2-1 0-1-1-2-2-1 0-1 0-2-1l-1 1v-2l1-2c1 0 1 0 1-1l1 1c2-1 2-1 3-2s1-1 3-2zM477 373v-2-1l1-1h2 0c1 1 2 4 3 4 1 3 2 7 3 10 0 1 0 1-1 2h-2c0 2 0 3 1 5l1 7v1c1 8 3 18 2 26v-1l-1-2v-3-1l-1-1c-1-1-3-2-4-3v-1-1c-1-1-1-4-1-5-1-2-1-4-1-6l-3-12c0-3-1-7-2-10 1-2 1-3 1-5h2z" class="d"></path><path d="M480 406c1 1 1 1 1 3 1 0 1 0 2-1 1 1 1 1 1 3l-2 1c0 1 3 2 3 4-1-1-3-2-4-3v-1-1c-1-1-1-4-1-5z" class="P"></path><path d="M477 373v-2-1l1-1h2 0c1 1 2 4 3 4 1 3 2 7 3 10 0 1 0 1-1 2h-2c0 2 0 3 1 5-1 1-1 1-1 3-1-1-1-2-1-2h-1-2c0-1 1-2 1-3 1-1 1 0 1-1l-1-1-1-1c-1 0 0 0-1-1v-1l2 1 1-1v-1h-2v-1c0-1 0-1-1-2v-1-1c0-2 0-2-1-4z" class="N"></path><path d="M480 369c1 1 2 4 3 4 1 3 2 7 3 10 0 1 0 1-1 2h-2l-3-16z" class="AT"></path><path d="M510 401l1-10h0v28c0 3 0 7 1 10 1 5 0 10 0 15 0 2 1 5 1 8v1h0l1-4h0v10l-1 5s-1-1-1 0v7 34h0c-1-2 0-5-1-7-1-3-1-6-1-8 0-7-1-12-1-18v-18-6-15c-1-6 0-13-1-19l-1 7c-1-2 0-3-2-4 1-2 1-3 2-5 0-2 0-10 1-11h1 1z" class="Z"></path><path d="M512 429c1 5 0 10 0 15 0 2 1 5 1 8v1h0l1-4h0v10l-1 5s-1-1-1 0v-35z" class="AS"></path><path d="M508 401h1 1v57c0 3 1 11-1 14v-18-6-15c-1-6 0-13-1-19l-1 7c-1-2 0-3-2-4 1-2 1-3 2-5 0-2 0-10 1-11z" class="l"></path><path d="M518 296l-1-3c1-2 2-3 3-5 0-3 1-10 0-13v-1c0-1 0-1 1-2 0-1 2-2 2-4v-1l1-1 1 1h0l-1-2 2-3h0l1-1 1-1v-1l1-1h-1c1-2 1-3 2-4 1 1 0 2 0 3 0-1 1-1 1-2s0-1 1-1v-1c1-1 1-1 2-1 1-1 2-1 3-2l1-1 3-3c1-2 2-3 4-4h2 1c0-1 2-2 2-2 0-2-1-3 1-5h1v4s1 1 1 2l-2 2c1 0 1 0 2 1 2 0 3 0 5-1l1-1 1 1v-1l2 1 1-1h3l1-1v1h4 1 0c1 0 1 1 2 1s0 0 1 1c-1 1-10 0-12 0l-1 1c-1 0-2-1-2 0h-2 0c1 1 3 1 4 1l1-1c1 0 2 1 4 1-3 1-6 1-8 1-6 0-10 1-15 4-3 1-6 4-8 6l-4 6v1c-2 1-3 2-4 4l-2 3v-1c0-1 1-2 1-4h1l-1-2-2 1h1v2c-1 3-3 5-4 9 0 1-1 2-1 4l1 1c0 2-1 3-1 5v3 1 1c-1 1-1 2-1 3l-2 2z" class="C"></path><path d="M527 264h-1c0-1 1-2 1-3 2-1 3-4 5-5l1 1-1 1c-1 1-2 3-2 5l2-1 1-2 3-3-4 6v1c-2 1-3 2-4 4l-2 3v-1c0-1 1-2 1-4h1l-1-2z" class="Y"></path><path d="M567 246h1l2 1 1-1h0c2 1 4 1 5 1l1 1c-1 4-3 5-6 8l-10 11h0c-1 0-2 0-3-1v1h-3c-2 1-3 3-5 4l-2 2c0-1 1-2 1-3v-1c1-1 2-2 2-3 1-2 2-3 2-5 0-1 0-2 1-3h0l-1-1h0-1v-1c1 0 1-1 2-2-2 0-2 0-4-3h3l1-1c2 0 4 1 5 2h1l-2-2c-1-1-2-1-2-1h-1v-1h3l1-1c2 0 5 0 8-1z" class="C"></path><path d="M554 254h2v1h0v4l-1 1s-1 1-2 1c0-1 0-2 1-3h0l-1-1h0-1v-1c1 0 1-1 2-2z" class="U"></path><path d="M568 255c2-1 4-3 6-4l3-3c-1 4-3 5-6 8h-1c-1 0-1 0-2-1z" class="b"></path><path d="M558 266h0c2-3 7-10 10-11 1 1 1 1 2 1h1l-10 11h0c-1 0-2 0-3-1z" class="l"></path><path d="M567 246h1c-1 1-1 2-1 3s-1 2-1 3c-1 0-1 1-2 2s-1 2-2 2c-1-1-1-1-1-2l-2-1v-1h1l-2-2c-1-1-2-1-2-1h-1v-1h3l1-1c2 0 5 0 8-1z" class="P"></path><path d="M532 360l-1-1c1-1 0-4 1-5s1-1 1-2 0-2 1-3v-2h1v3 2 1l1 6 1 2v2l1 3h1l1-1c0-1 0-2-1-3 0-1-1-1-1-2v-1-1-2c-1-1-1-1-1-3h0v-3h1c0 1 0 2 1 4l1-2 1 1v1l3 3h0c0 3 1 5 1 7h0l2 2c1 0 0 0 1-1h1l1-1-4 21c-2-2-4-2-5-4h-1-1l-1-1c0-1-1-2-1-2-1-1-2-1-2-1 0-2-1-4-2-5v-3c-1-2 0-3-1-5h0v-4z" class="X"></path><path d="M541 354l3 3h0c0 3 1 5 1 7h-2 0c1 1 2 3 2 5h0c-2-1-2 0-4-1l1-1h1v-1h-2l-2-5 1-1c0 1 0 1 1 1h0v-3c-1-2-1-3 0-4z" class="b"></path><path d="M501 394c1-1 3-2 4-4l1-1h0v4c-1 4 0 6-1 10v7c-1 3-1 7-2 9l-3 6c-1 1-2 3-3 4h0-1s-1-1-1-2h0l-1-1v2h-1l-3-3h0v-5c1 1 2 1 3 1h0l2-2-2-3c0-2-1-3-1-5 1-1 1-2 1-3v-1c1-2 1-3 1-5v-1c1-2 3-4 5-5l2-2z" class="q"></path><path d="M494 414l3 2-2 3-2-3 1-2z" class="P"></path><path d="M495 413h4l-2 3-3-2 1-1z" class="Z"></path><path d="M493 421l2 2h1v-1l-1-1h0l1 1 2-2 3-3c0-2 0-3 1-4l1 1c-1 1-1 2-2 3-1 2-4 4-3 6l2 2c-1 1-2 3-3 4h0-1s-1-1-1-2h0l-1-1v2h-1l-3-3h0v-5c1 1 2 1 3 1h0z" class="g"></path><path d="M501 394c1-1 3-2 4-4l1-1h0v4h-1c-1 1-1 2-1 3-2 4-2 7-3 12h-1c0 2-1 4-1 5h-4l-1 1-1 2c0-2-1-3-1-5 1-1 1-2 1-3v-1c1-2 1-3 1-5v-1c1-2 3-4 5-5l2-2z" class="b"></path><path d="M501 394c1 1 2 1 2 2 0 2-1 3-2 4s-1 3-1 5h-1c-1 1-1 2-1 3l1 1h-1-1v-4l-1-1c-1-2 2-6 3-8l2-2z" class="w"></path><path d="M499 396c-1 2-4 6-3 8l1 1v4h1c-1 2-2 3-3 4l-1 1-1 2c0-2-1-3-1-5 1-1 1-2 1-3v-1c1-2 1-3 1-5v-1c1-2 3-4 5-5z" class="b"></path><path d="M493 407c1 0 2 0 3 1v1h1 0 1c-1 2-2 3-3 4l-1 1-1 2c0-2-1-3-1-5 1-1 1-2 1-3v-1z" class="l"></path><path d="M558 455v-7c1-1 1-3 1-4 1-1 0-5 0-6 1-1 1-4 1-6h-1v-3-1h0l-1-1c0-2 0-2-1-4v-1c0-1-1-1-1-2l1-1h1c3 1 5 8 6 11v1c1 1 1 3 1 5 1 1 0 5 0 6v1c0 1 0 1-1 2v2 1 1h1c1-1 2-1 2-3v-2c0-1 1-1 1-2v-3c0-1 0-2 1-3 0-1 1-1 1-2l1-1s0-1 1-2c1 0 1-1 1-2v-1c1-1 1-2 1-3v-1c1-1 1 0 1-1 0-2 0-3 1-5v8l-8 27c-1 3-3 8-3 11-1 3-6 24-7 25h-1v-2l1-2c1-2 1-3 2-5v-2c1-2 0 0 1-1v-2l1-1v-3c1-1 0-1 1-1v-3h0l1-1v-2-1c1-2 1 0 1-2v-1-1l1-2c0-2 1-4 2-5v-2-1l1-3 1-2v-1l-2 2v2c-1 2-2 3-4 4v2h0l-1-1v2 2c0 1 1 1 1 1v3h0v1c-1 1-1 1-1 2-1 1 0 2 0 3l-1 1h0l-1 1c-1-1-3-2-3-3v-1c-1-1-1-1-1-2 1-1 1-4 1-6 0-1-1 0 0-1z" class="I"></path><path d="M560 439c1-2 1-4 1-7 0-1 0-3-1-4 0-2-1-3 0-5 1 1 2 3 2 5l1 1c1 3 1 8 1 12v2c0 1 0 2-1 3l-1 1v-1-1c-1-2-1-3-2-6z" class="J"></path><path d="M560 439c1 3 1 4 2 6v1 1 6 3c1 2 1 2 1 5-1 2-2 4-1 6h0l-1 1c-1-1-3-2-3-3v-1c-1-1-1-1-1-2 1-1 1-4 1-6 0-1-1 0 0-1 2-3 1-7 2-11v-5z" class="K"></path><path d="M611 186c0 2 0 3-2 4l-1 1h0v1l2 1v1h-1c-1 2-2 4-3 5-3 3-6 6-9 7l-4 3v-2h-1c-1 1-3 2-5 4v-1c-2 0-2 1-3 2 0-1 0-1-1-2-2 1-5 3-7 6h0c-3 0-4 3-7 3h-1c0 1 0 0-1 1v-1l-2 1h-1c-2 1-1 1-2 2h-1l-1 1v1l-2 2s-1 0-1-1h-1c-1-2-2-3-3-4l3-1 1-2-3 1v-1c-1-2-1-2-2-3h0 2l3-1c1 1 2 1 3 1h2c1 0 1 0 2 1 1 0 4-2 5-3h1c2-1 4-1 6-2 1 0 2-1 3-1h1c0-1 0-1-1-2 5-2 8-4 12-7l2-1s1-1 2-1l2-1c0-1 1-2 2-3l1-1 7-5 4-3z" class="Y"></path><path d="M556 225l-1-2 2-2 4 1-1 1v1l-2 2s-1 0-1-1h-1z" class="AU"></path><path d="M557 214c1 1 2 1 3 1h2c1 0 1 0 2 1l-7 2-3 1v-1c-1-2-1-2-2-3h0 2l3-1z" class="AH"></path><path d="M608 192l2 1v1h-1c-1 2-2 4-3 5-3 3-6 6-9 7l-4 3v-2h-1c-1 1-3 2-5 4v-1c-2 0-2 1-3 2 0-1 0-1-1-2 8-4 15-10 21-14 1-1 2-1 2-1 1-1 2-2 2-3z" class="q"></path><path d="M608 192l2 1v1h-1c-1 2-2 4-3 5-3 3-6 6-9 7 0-2 1-3 2-4s1-1 3-1c1-2 2-3 2-5 1-1 2-1 2-1 1-1 2-2 2-3z" class="g"></path><path d="M483 276v-1l2 2 1-1 1 1h2v1l1 1c2 0 3 1 5 2 4 2 6 4 8 8l1 1c1 3 2 5 2 8v3c1 3 2 8 1 11-1 2-1 5-1 6l1 1h0c-1 2 0 4-1 6-1 0-1 0-2-1 0-2 0-6-1-8v-1c-1-1-1-2-2-3l-1-3c-1-2-2-3-3-5l-2-4c-2-2-2-5-3-7l-4-6c0-2-2-3-3-5h0l-4-4c1-1 1-2 1-2h1z" class="t"></path><path d="M495 281c4 2 6 4 8 8l1 1c1 3 2 5 2 8v3l-2-2v-3h-1c0-2-2-3-2-5 0-1 0-1-1-1l-2-2h-1l-3-3c1 0 2 0 4 1h0v-1l-3-3v-1z" class="w"></path><path d="M483 276v-1l2 2 1-1 1 1h2v1l1 1c2 0 3 1 5 2v1l3 3v1h0c-2-1-3-1-4-1-1-1-2-1-3-1v3c-1 1-2 0-3 0 0-2-2-3-3-5h0l-4-4c1-1 1-2 1-2h1z" class="b"></path><path d="M482 276h1 0c1 1 1 1 2 1l1 1h2c0 2 0 2 2 3v-1h1c1 1 1 1 1 3-1 1-1 0-2 0-1-1-1-1-2-1v2c-1-2-1-2-3-2h0l-4-4c1-1 1-2 1-2z" class="X"></path><path d="M492 293c1 1 2 1 3 2l1 1v1l1-2h1c1 1 0 1 1 2v-1l1-2h1l2 2h0 1v3l2 2c1 3 2 8 1 11-1 2-1 5-1 6l1 1h0c-1 2 0 4-1 6-1 0-1 0-2-1 0-2 0-6-1-8v-1c-1-1-1-2-2-3l-1-3c-1-2-2-3-3-5l-2-4c-2-2-2-5-3-7z" class="q"></path><path d="M500 309c2 1 2 1 4 3 0 2 0 2-1 4v-1c-1-1-1-2-2-3l-1-3z" class="w"></path><path d="M495 300l1-1 2 1 1-1v1c0 1 0 1 1 2l-1 2c1 0 1 0 2-1v2c-1 0-2-1-4-1l-2-4z" class="t"></path><path d="M499 297v-1l1-2h1l2 2h0 1v3l2 2c1 3 2 8 1 11h-1c-1-1-2-2-2-4v-2h0c-1-2-2-3-3-4v-3c1 0 1 0 2-1-2-2-1-1-4-1z" class="r"></path><path d="M493 329l1 1c0 2 1 4 1 5 1 1 1 2 1 3h0c1 2 1 2 1 4 1 1 2 3 2 5l-2-2v-3l-2 2h0c0 1 0 2-1 3 0 0 0 1-1 2l1 1-1 1h1c-1 2-2 3-4 4-2 2-5 2-5 5l-1 2c1 1 2 1 3 1-1 1-1 2-2 3v1c-1 1-2 2-2 4v2h0c-1 0-2-3-3-4h0l-4-12 2-2c0-2 0-4-1-5v-2h0l3-3c1-3 4-6 6-9h2s1-1 2-1l1-1v-4l2-1h0z" class="N"></path><path d="M477 348h3 0l1 1h-1l-1 1v2h1l1 1-1 1 1 2c-1 1 0 1 0 2h1c1 2 1 2 0 4l3 4v1c-1 1-2 2-2 4v2h0c-1 0-2-3-3-4h0l-4-12 2-2c0-2 0-4-1-5v-2z" class="P"></path><path d="M478 355l3 8c1 3 1 7 2 10h0c-1 0-2-3-3-4h0l-4-12 2-2z" class="AL"></path><path d="M493 329l1 1c0 2 1 4 1 5 1 1 1 2 1 3h0c1 2 1 2 1 4 1 1 2 3 2 5l-2-2v-3l-2 2h0c0 1 0 2-1 3 0 0 0 1-1 2l1 1-1 1h1c-1 2-2 3-4 4-2 2-5 2-5 5l-1 2c1 1 2 1 3 1-1 1-1 2-2 3l-3-4c1-2 1-2 0-4h-1c0-1-1-1 0-2l2-2c1 0 1-1 3-1h1l1-1c1 0 2 1 2 1l1-1-1-1c1-3 2-3 4-5v-2-7h0v-1l-1-1v-3-2-1h0z" class="U"></path><path d="M593 353v-1-3c0-2 1-4 1-6h1-1v-2l-1-1h0l1-2c0-1 0-2 1-3v-3c0-2 1-3 1-4s0-2 1-3v-1h1c0 1 0 2-1 3v3c0 1-1 1-1 2v2c0 1 0 2-1 3v3c0-1 0-2 1-2v1c-1 1 0 1 0 2h1l-1 1v2c0 1 0 2-1 3v2c-1 0-1 2-1 3v3c-1 1-1 1-1 3h0v1l-1 1v2l-1 4v2c-1 1-1 1-1 2l1 1v-3l1-1v-2c1-2 1-3 2-5v-2l2-4v-1c0-1 1-2 1-2v-2c1-1 1-2 1-3s0-2 1-3h0c0-1 0-2 1-3 0-1 0-3 1-4v-1c0-1 0-2 1-3v-1-1l1-1v-1-1-1h1v-1l1-1c-1-1-1-2-1-3v-1l1-1h0c1-1 1-1 1-2l1-5 1-2c0-1-1 1 0-1v-1h0 1v2 5h0v-1c1 2-1 4-1 6 1-1 1-3 2-3 0 1 0 1 1 2v1l-7 26c-1-2-2-2-4-3l-1 2c1 1 2 1 2 2 1 1 1 1 1 2h-1c-3 4-1 10-3 15h-1l-2 6c0 2 0 3-1 5-1 0-1 1-1 2l-3 11h-1v-1l-2-2-1 4c0-1 0-2-1-3l7-27c0-2 0-4 1-6z" class="AC"></path><path d="M609 310v5h0v-1c1 2-1 4-1 6v1l-1-2v3c-1 1-1 2-2 3l4-15z" class="v"></path><defs><linearGradient id="AK" x1="586.119" y1="379.282" x2="595.809" y2="375.248" xlink:href="#B"><stop offset="0" stop-color="#bab1a7"></stop><stop offset="1" stop-color="#cfcecb"></stop></linearGradient></defs><path fill="url(#AK)" d="M593 368l1 1v2-1-1l1 1c0 2 0 3-1 5-1 0-1 1-1 2l-3 11h-1v-1l-2-2 6-17z"></path><path d="M599 345c1 1 2 1 2 2 1 1 1 1 1 2h-1c-3 4-1 10-3 15h-1l-2 6-1-1v1 1-2l-1-1c0-2 1-4 1-6l5-17zm9-25c1-1 1-3 2-3 0 1 0 1 1 2v1l-7 26c-1-2-2-2-4-3l5-18c1-1 1-2 2-3v-3l1 2v-1z" class="x"></path><path d="M507 265c1 1 2 2 2 4v2c0 2 0 4 1 6 0 2 0 4 1 6v-2l1 8v7h1v5c-1 0 0 0-1-1v1l-1-1-1 1c1 0 1 1 1 2-1 1-1 1-2 1h-1c1-2 1-2 1-4l-3-2c0-3-1-5-2-8l-1-1c-2-4-4-6-8-8-2-1-3-2-5-2l-1-1v-1h-2l-1-1-1 1-2-2v1h-1c-2-2-4-3-6-5l1-1c4 2 8 2 12 4h5c-1-1-1-1-1-2l-6-5v-1h1 3v2l1 1c1-1 1 0 2-1v1h1c1 1 2 1 3 1h0c0-1-1-2-1-3v-1l3 2c1 1 2 2 2 3l2 2h0c0 1 0 1 1 2h0c0 2 0 3 2 4v-2c1-4-2-9 0-12z" class="AP"></path><path d="M498 270c0-1-1-2-1-3v-1l3 2c1 1 2 2 2 3l2 2h0c0 1 0 1 1 2h0c0 2 0 3 2 4h0c0 2 1 9 0 10-1-2-1-4-1-5-1 0-1 1-2 2l1 3h-1c-1-2-3-4-4-7l2 1c-1-1-1-2-1-3h0s1 0 1 1h0 1l-1-4c0-2-2-4-2-5v-2h-2z" class="AK"></path><path d="M504 290c1 0 1 1 2 2 0 1 0 2 1 3v-2-4h0c1-2 1-3 3-4v1 1c1 1 1 3 1 5v-1l1-2v7h1v5c-1 0 0 0-1-1v1l-1-1-1 1c1 0 1 1 1 2-1 1-1 1-2 1h-1c1-2 1-2 1-4l-3-2c0-3-1-5-2-8z" class="AL"></path><path d="M512 300v-2l-1 1-1-1v-5h0l1 2v2l1-1h1v5c-1 0 0 0-1-1z" class="AD"></path><path d="M487 267v-1h1 3v2l1 1c1-1 1 0 2-1v1h1c1 1 2 1 3 1h0 2v2c0 1 2 3 2 5l1 4h-1 0c0-1-1-1-1-1h0c0 1 0 2 1 3l-2-1c-2-3-7-6-11-8h5c-1-1-1-1-1-2l-6-5z" class="t"></path><path d="M496 274l1-1 1 1 2-1 1 1-1 1c-1 1-1 1-1 2l-3-3z" class="AK"></path><path d="M487 267v-1h1 3c-1 1-1 2-1 2 1 2 4 4 6 6l3 3c0 1 1 2 2 3h0c0 1 0 2 1 3l-2-1c-2-3-7-6-11-8h5c-1-1-1-1-1-2l-6-5zm49-10c2-2 5-5 8-6 5-3 9-4 15-4l-1 1h-3v1h1s1 0 2 1l2 2h-1c-1-1-3-2-5-2l-1 1h-3c-1 1-2 1-2 2-2 1-4 3-6 3v1l-3 3c-1 2-1 4-2 6s-2 4-2 7l1 1-2 1h0c0-1 0-2 1-2l-1-1c-1 1-2 2-3 4s-2 5-3 8c-2 3-7 7-5 12 0 1 1 1 1 2l-1 6v4l-1 1c-1 2-1 6-2 9v2c-1 2 0 5-1 8h0 0c-1-3-1-8-1-10 1-2 1-4 1-5v-1c-1-1-1-2-1-4 1-1 0-2 0-3h-1v-1c0-1 1-2 1-3v-5l2-2c0-1 0-2 1-3v-1-1-3c0-2 1-3 1-5l-1-1c0-2 1-3 1-4 1-4 3-6 4-9v-2h-1l2-1 1 2h-1c0 2-1 3-1 4v1l2-3c1-2 2-3 4-4v-1l4-6z" class="N"></path><path d="M538 259l1-2c1 0 1 0 1 1s-1 1-1 2h0c-1 2-1 4-2 6-1 0-1 0-2 2 0 1-1 2-2 3l-1-1c1-1 1-1 1-2v-1l3-3h0c1-1 1-1 1-2v-1c1-1 1-1 1-2zm-20 37l2-2c0-1 0-2 1-3v-1c0 2 0 5-1 7h0v2 3l-1 1c0 3 1 7 0 10v-1c-1-1-1-2-1-4 1-1 0-2 0-3h-1v-1c0-1 1-2 1-3v-5z" class="Z"></path><path d="M536 257c2-2 5-5 8-6 5-3 9-4 15-4l-1 1h-3v1h1s1 0 2 1l2 2h-1c-1-1-3-2-5-2l-1 1h-3c-1 1-2 1-2 2-2 1-4 3-6 3v1l-3 3h0c0-1 1-1 1-2s0-1-1-1l-1 2-3 3c-1 1-1 0-1 1l-3 5v1c0 1-1 2-1 3l-1 1-1 1-1-1c1-3 3-4 4-6l1-3v-1l4-6z" class="g"></path><path d="M476 292c3 3 4 7 6 11h0c1 1 1 1 1 2v1c1 1 1 2 1 3 1 1 1 2 2 3v-1c1 1 1 2 1 4l1 2c2 2 3 5 3 8l2 4h0l-2 1v4l-1 1c-1 0-2 1-2 1h-2c-2 3-5 6-6 9l-3 3c-1-3 2-3 2-6h0-1c0-1 0-1-1-1v-5-3c1-1 1-1 1-2l-1-1v-1c-1-1-1-1-1-2-1-2-1-2 0-4h0l-2-2v-1l-2-2c1-2 1-2 0-3 1-1 3 2 4 3v-2h1 1v-4h0c1-2 1-7 1-9-1-2-1-3-2-5h-1l1-1c-1-2-1-3-1-4v-1h0z" class="d"></path><path d="M482 326h1l2 1v2l-1 1c-1 0-1 0-2 1l-1-1c0-1 1-3 1-4z" class="C"></path><path d="M484 309c1 1 1 2 2 3v-1c1 1 1 2 1 4l-1 9c0 1 0 4-1 5v-2l-2-1h-1v-2c1-1 1-1 1-2v-2l1-1v-2-4c1-2 0-2 0-4z" class="U"></path><path d="M476 292c3 3 4 7 6 11h0c1 1 1 1 1 2v1c-1 1 0 2-1 3l-1 1c1 2 0 4 0 6s-1 5-2 7c0-2 0-5-1-7v-4h0c1-2 1-7 1-9-1-2-1-3-2-5h-1l1-1c-1-2-1-3-1-4v-1h0z" class="C"></path><path d="M487 315l1 2c2 2 3 5 3 8l2 4h0l-2 1v4l-1 1c-1 0-2 1-2 1h-2c-2 3-5 6-6 9l-3 3c-1-3 2-3 2-6h0v-1c3-4 5-7 6-12 1-1 1-4 1-5l1-9z" class="AL"></path><path d="M488 317c2 2 3 5 3 8l2 4h0l-2 1v4l-1 1c-1 0-2 1-2 1h-2c2-5 3-10 2-14v-5z" class="Z"></path><path d="M701 138v4l2 1-4 14c-3 6-5 13-6 20-1 1-1 2-1 3v1l-1 1 1 3-10 24c-1-3 0-6 0-9v-11l-2-10c-1-5-3-9-5-13-1-2-2-4-2-6 0-1-1-3 0-4 0-1 2-1 2-2l2 3 3 5c1 2 3 5 5 6v-1c2 1 3 3 5 3h2l5-15 1-2c1-6 2-10 3-15z" class="AG"></path><path d="M682 180v-3h1 0l1 1h1c0 2 0 3-1 5v3c0 1 0 0-1 1 1-3 0-4-1-7z" class="D"></path><path d="M680 179l2 1c1 3 2 4 1 7 0 1-1 2-1 2l-2-10z" class="B"></path><path d="M680 162c1 2 3 5 5 6 1 1 1 2 1 3h-1l-2-2c-2 0-3-1-4-3-1-1 0-3 1-4z" class="AA"></path><path d="M685 167c2 1 3 3 5 3h2c-1 1-1 2-2 3 0 1-1 3-1 3-1-2-1-3-3-5 0-1 0-2-1-3v-1z" class="z"></path><path d="M673 160c0-1-1-3 0-4 0-1 2-1 2-2l2 3c-1 1-2 2-2 3v6c-1-2-2-4-2-6z" class="G"></path><path d="M701 138v4l2 1-4 14c-3 6-5 13-6 20-1 1-1 2-1 3v1l-1 1c-2 2-6 13-7 16 0-8 2-14 5-22 0 0 1-2 1-3 1-1 1-2 2-3l5-15 1-2c1-6 2-10 3-15z" class="AE"></path><path d="M550 251c2 3 2 3 4 3-1 1-1 2-2 2v1h1 0l1 1h0c-1 1-1 2-1 3 0 2-1 3-2 5 0 1-1 2-2 3v1c0 1-1 2-1 3-1 1-3 2-3 3-2 0-2 0-3 1l1 2h1c0 1 0 2-1 3v1l3 2-1 3c-1 2-2 5-3 6l-1 2-3-2c-1 1-1 2-2 4v1c0 1-1 2-2 3v-3c1-1 1-1 1-2v-2l1-1-1-1v-1c1 1 1 0 2 0s1 0 1-1-1-1-1-2h0l-1-1-1-1c-1 0-3 1-3 1 0 2-1 3-2 3h0c-1 2-2 3-2 5s-2 5-2 7l-1 3-1-1-1-1 1-6c0-1-1-1-1-2-2-5 3-9 5-12 1-3 2-6 3-8s2-3 3-4l1 1c-1 0-1 1-1 2h0l2-1-1-1c0-3 1-5 2-7s1-4 2-6l3-3v-1c2 0 4-2 6-3 0-1 1-1 2-2z" class="g"></path><path d="M538 294c0-2 1-3 1-5l1 1h0c1 2 2 3 2 4l-1 2-3-2z" class="N"></path><path d="M530 291c0-2 1-3 2-5s2-4 4-5c0 2 0 2-1 3l1 1s-1 0-1 1v1c-1 0-3 1-3 1 0 2-1 3-2 3h0z" class="P"></path><path d="M543 283l3 2-1 3c-1 2-2 5-3 6 0-1-1-2-2-4h0l1-1 1-4h-1l-1 1-1-2 1-1v1c1 0 1 0 1 1l2-2z" class="Y"></path><path d="M524 298c0-1-1-1-1-2-2-5 3-9 5-12 1-3 2-6 3-8s2-3 3-4l1 1c-1 0-1 1-1 2h0l2-1 1-1c0 2-2 3-3 5-1 3-2 7-4 9h0v-3c1 0 0 0 0-1h0l-4 8c-1 1-1 3-1 5 0 0 0 1-1 2z" class="b"></path><path d="M550 251c2 3 2 3 4 3-1 1-1 2-2 2v1h1 0l1 1h0c-1 1-1 2-1 3 0 2-1 3-2 5 0 1-1 2-2 3v1c0 1-1 2-1 3-1 1-3 2-3 3-2 0-2 0-3 1l1 2h1c0 1 0 2-1 3v1l-2 2c0-1 0-1-1-1v-1-1c0-2-1-4-1-5v-1c0-2 1-4 1-6h0l2-3h-1l-2 1c0 2 0 3-1 4l-1 1h0l-1 1-1-1c0-3 1-5 2-7s1-4 2-6l3-3v-1c2 0 4-2 6-3 0-1 1-1 2-2z" class="Y"></path><path d="M540 282v-1c1-1 1-2 1-3l-1-1c0-2 0-2 2-3h0v3h0l1 2h1c0 1 0 2-1 3v1l-2 2c0-1 0-1-1-1v-1-1z" class="C"></path><path d="M539 260l3-3v-1c2 0 4-2 6-3 0 1 2 2 1 3h0l-2 2c-1 0 0 0-1 1v1h-1c-1 0-2 1-3 2v1l1 1 1-1v1l3 2-1 1v3c1 1 1 1 1 2l-2 2c-1-1-1-2-1-3v-3c-1-1-1-1-2-1h-1l-2 1c0 2 0 3-1 4l-1 1h0l-1 1-1-1c0-3 1-5 2-7s1-4 2-6z" class="U"></path><path d="M494 351h-1l1-1-1-1c1-1 1-2 1-2 1-1 1-2 1-3h0l2-2v3l2 2 1 3 2 8c1 3 3 8 3 11-4 6-4 13-9 18 0 1-2 2-3 3h-3c-1-1-2 0-3 0l-1-2v-5h0c-1-3-2-7-3-10h0v-2c0-2 1-3 2-4v-1c1-1 1-2 2-3-1 0-2 0-3-1l1-2c0-3 3-3 5-5 2-1 3-2 4-4z" class="r"></path><path d="M491 375l1-1 1 1-4 6v-3c1-1 1-2 2-3z" class="N"></path><path d="M491 371l1-1c1-1 1-2 2-3v-1-2-2h1l1 2c-1 4-1 7-3 11l-1-1-1 1h-2c1-1 1-3 1-4h1z" class="P"></path><path d="M497 353h0l-1-2h1v-1-1c1 1 2 1 3 1h0l2 8c0 2 0 4-1 6-1-2-1-4-1-6l-1-1c0-2-1-2-2-4z" class="w"></path><path d="M485 366c1-1 1-2 2-3l1 3h0l1 2h1v1c0 1 0 1 1 2h-1c0 1 0 3-1 4h2c-1 1-1 2-2 3v3l-1 1-1 1h-1 0c-1-3-2-7-3-10h0v-2c0-2 1-3 2-4v-1z" class="U"></path><path d="M487 370c1 1 2 1 3 1 0 1 0 3-1 4h2c-1 1-1 2-2 3 0-3 0-4-2-6v-2z" class="Z"></path><path d="M483 373v-2c0-2 1-3 2-4v5l2 3c-1 1-1 2-1 4 1 1 1 2 2 3l-1 1h-1 0c-1-3-2-7-3-10h0z" class="N"></path><path d="M485 366c1-1 1-2 2-3l1 3h0l1 2h1v1c0 1 0 1 1 2h-1c-1 0-2 0-3-1v2h-2v-5-1z" class="C"></path><path d="M488 366h0l1 2h1v1c0 1 0 1 1 2h-1c-1 0-2 0-3-1 0-1 1-3 1-4z" class="N"></path><path d="M502 358c1 3 3 8 3 11-4 6-4 13-9 18 0 1-2 2-3 3h-3c-1-1-2 0-3 0l-1-2v-5h1c1 0 1 1 3 1h0v-2c1 0 2-2 3-3v1 1l2-1v1l-3 3v1h-2v1h2l3-3v-1c4-4 6-12 6-18 1-2 1-4 1-6z" class="P"></path><path d="M494 351h-1l1-1-1-1c1-1 1-2 1-2 1-1 1-2 1-3h0l2-2v3l2 2 1 3h0c-1 0-2 0-3-1v1 1h-1l1 2h0c0 3 0 5-1 7v4l-1-2h-1v2 2 1c-1 1-1 2-2 3l-1 1c-1-1-1-1-1-2v-1h-1l-1-2h0l-1-3c-1 0-2 0-3-1l1-2c0-3 3-3 5-5 2-1 3-2 4-4z" class="b"></path><path d="M494 351v1 1 3l-3 3c0 2 2 3 1 6h-1c-1 1-1 2-2 3l-1-2h0l-1-3c-1 0-2 0-3-1l1-2c0-3 3-3 5-5 2-1 3-2 4-4z" class="Y"></path><path d="M485 360v1c1 0 1-1 1-2 1 0 1 0 2 1h2c0 1-1 4-1 5l-1 1h0l-1-3c-1 0-2 0-3-1l1-2z" class="Z"></path><path d="M580 229c15 4 29 9 37 23 4 8 6 19 4 28h0c-2 2-1 2-2 5v2 1h-1c0-2-1-4-1-7-1-6-1-15-5-21-3-5-6-9-12-11-4-2-10-2-15 0h-1c-1-7-2-13-4-20z" class="r"></path><defs><linearGradient id="AL" x1="544.349" y1="436.851" x2="540.035" y2="436.528" xlink:href="#B"><stop offset="0" stop-color="#878484"></stop><stop offset="1" stop-color="#a8a6a3"></stop></linearGradient></defs><path fill="url(#AL)" d="M532 360v4h0c1 2 0 3 1 5v3c1 1 2 3 2 5 0 0 1 0 2 1 0 0 1 1 1 2l1 1h1 1l-1 1 5 5-1 4c2 2 2 3 2 5l-3 67-1 20-2 1c0-27-1-54 3-81l-2-3 1-1-1-1-2 1v4 6c-1 0-1 0-2-1h0c-1-2-2-3-3-5s-2-3-4-5c0-1-1-2-2-2l-2-2-1-1v-1c1 0 2-1 2-1 1-2 0-8 0-10v-1c0-4 1-7 1-11 0 0 1-1 1-2s1-2 2-2l1-5z"></path><path d="M535 377s1 0 2 1c0 0 1 1 1 2l1 1h1 1l-1 1 5 5-1 4h0c-3-2-5-5-7-8 0 0 0-1-1-2s-1-3-1-4z" class="N"></path><path d="M532 360v4h0c1 2 0 3 1 5v3h0v1c1 1 1 3 2 5 0 2 1 4 2 6v1c2 2 3 4 5 5 0 1 2 2 2 3v2l-1 8-2-3 1-1-1-1-2 1c0-2 0-4-1-6s-1-4-2-6c-1 0-2 0-3-1l-1-1c1-1 0-2 0-3-1-1-1-1-1-2h0c0-2 0-4-2-5-1 2-1 3-2 5 0-4 1-7 1-11 0 0 1-1 1-2s1-2 2-2l1-5z" class="b"></path><path d="M532 382v-1c1 0 2 1 2 2l1-1 1 2-1 2 1 1c-1 0-2 0-3-1l-1-1c1-1 0-2 0-3z" class="N"></path><path d="M527 380c1-2 1-3 2-5 2 1 2 3 2 5h0c0 1 0 1 1 2 0 1 1 2 0 3l1 1c1 1 2 1 3 1 1 2 1 4 2 6s1 4 1 6v4 6c-1 0-1 0-2-1h0c-1-2-2-3-3-5s-2-3-4-5c0-1-1-2-2-2l-2-2-1-1v-1c1 0 2-1 2-1 1-2 0-8 0-10v-1z" class="Y"></path><path d="M526 394h2l2 1h0 2l1 1v-1-3c-1-1-1-2-1-3h-1c1-2 1-3 1-4l1 1 3 15c0 3 1 5 1 7h0c-1-2-2-3-3-5s-2-3-4-5c0-1-1-2-2-2l-2-2z" class="g"></path><path d="M527 380c1-2 1-3 2-5 2 1 2 3 2 5h0c0 1 0 1 1 2 0 1 1 2 0 3 0 1 0 2-1 4h1c0 1 0 2 1 3v3 1l-1-1h-2 0l-2-1h-2l-1-1v-1c1 0 2-1 2-1 1-2 0-8 0-10v-1z" class="l"></path><path d="M565 464v5s0 1-1 2c0 4-3 6-1 10l-3 15c-1 5-2 10-4 14 0 2-1 5-2 6l-6 21c0 3-1 7-2 9v1c1 3 1 6 2 8h0l-1 2h0c1 1 1 2 1 3h0 2l1 2v1 1c0 1 0 1-1 1l-1 1h3c0 1-1 2 0 3l1 3c-1 1-4 1-6 1l-1-1v-1h-2v-1l1-1-2 1v-1 1c1 1 1 1 0 3h1l1 1c0 1 0 1 2 2h1l-2 2v-1l-1 1h-1l1-1v-1c-1-1-2-1-2-2v-3l-1 1c0 2 0 4 1 6l-1 1v2l1 1c-2 1-1 3-1 5l-2 2v-6h0-1c0 1-1 1-1 2l-1 1c-1 0 0 0-1 1h-1l-1-1v-3c1-2 2-2 1-3-1 0-1 2-2 3h0l-2 2c0 1 0 3-1 5v-3h-1l29-98c1-1 6-22 7-25z" class="AN"></path><path d="M533 583v-2c0-1 1-2 2-2 2-1 4-9 5-12s0-8 1-12c2 5 2 9 1 14l1 1c1 1 1 1 0 3h1l1 1c0 1 0 1 2 2h1l-2 2v-1l-1 1h-1l1-1v-1c-1-1-2-1-2-2v-3l-1 1c0 2 0 4 1 6l-1 1v2l1 1c-2 1-1 3-1 5l-2 2v-6h0-1c0 1-1 1-1 2l-1 1c-1 0 0 0-1 1h-1l-1-1v-3c1-2 2-2 1-3-1 0-1 2-2 3z" class="Aa"></path><g class="AO"><path d="M547 573c0-1-1-2-1-3v-2c0-1-1-2-1-2v-2l-1-4c-1-1-2-4-2-6 1-3 3-8 3-11 2-5 2-11 4-16l5-11h0l-6 21c0 3-1 7-2 9v1c1 3 1 6 2 8h0l-1 2h0c1 1 1 2 1 3h0 2l1 2v1 1c0 1 0 1-1 1l-1 1h3c0 1-1 2 0 3l1 3c-1 1-4 1-6 1z"></path><path d="M552 569h3v-1c1 1 1 1 2 1 0-1 0-1 1-2l2-1h2c-1 2 0 7-2 9 0 2 0 5 1 7l-2 5-1-1c-1 0-1 1-2 2v1c-1 3-1 5-1 8-1 1-1 3-1 4-1 1-1 2-1 3v1c-1 1-2 3-2 5h-2v1c-2 1-4 3-4 6h-1v2l-2 2h-1-1 0v-3l-1-1h-1c0-1 0-2-1-4l-1 2-3 1c0-1 0-2 1-3l2-2c-1-3 0-5 1-7s1-5 2-7c0-2 0-4 1-5v-1-2h0l2-2c0-2-1-4 1-5l-1-1v-2l1-1c-1-2-1-4-1-6l1-1v3c0 1 1 1 2 2v1l-1 1h1l1-1v1l2-2h-1c-2-1-2-1-2-2l-1-1h-1c1-2 1-2 0-3v-1 1l2-1-1 1v1h2v1l1 1c2 0 5 0 6-1l-1-3z"></path></g><path d="M552 580l1-1 1 1v3c0 1 1 2 1 3s0 2-1 2c-1 1-1 1-2 1l-1 1-1-1h0l-2 2h0c-1-1-1-2-2-4l3-3h3v-4z" class="v"></path><path d="M552 580v4h-3l-3 3c-1 1-2 3-3 5-2 1-2 3-4 5 0-2 0-4 1-5v-1-2h0l2-2 6-6h3l1-1z" class="AV"></path><path d="M543 570v-1 1l2-1-1 1v1h2v1l1 1c2 0 5 0 6-1h3v1c0 2 0 3 1 4 1 2 1 6 1 9-1 0-1 1-2 2 0-4 0-8-2-12h-3l-1-1-2 1h-1c-2-1-2-1-2-2l-1-1h-1c1-2 1-2 0-3z" class="AH"></path><path d="M552 569h3v-1c1 1 1 1 2 1 0-1 0-1 1-2l2-1h2c-1 2 0 7-2 9 0 2 0 5 1 7l-2 5-1-1c0-3 0-7-1-9-1-1-1-2-1-4v-1h-3l-1-3z" class="Ab"></path><path d="M546 587c1 2 1 3 2 4h0l2-2h0l1 1-2 1 1 1c0 2 0 3-1 4v2 1-1c1 2 0 2 0 3 1 1 1 0 1 1-1 1-1 2-1 3l1 1c0 2 0 3-1 4v1c-2 1-4 3-4 6h-1v2l-2 2h-1-1 0v-3l-1-1h-1c0-1 0-2-1-4l-1 2-3 1c0-1 0-2 1-3l2-2c-1-3 0-5 1-7s1-5 2-7c2-2 2-4 4-5 1-2 2-4 3-5z" class="x"></path><path d="M534 613c3-1 4-1 6 1v2 2l-1-1h-1c0-1 0-2-1-4l-1 2-3 1c0-1 0-2 1-3z" class="v"></path><path d="M525 393l1 1 2 2c1 0 2 1 2 2 2 2 3 3 4 5s2 3 3 5h0c1 1 1 1 2 1v-6-4l2-1 1 1-1 1 2 3c-4 27-3 54-3 81v2c-1-1-1-1-1-3 1-5-2-11-4-15l-3-10c-1-2-2-5-2-7-2-4-2-8-3-12-1-3-1-7-1-10 0-2 0-4-1-6v-3h0v-2c-1 0-1 0-1-1v-3-9-5l1 1h0v-6l-1-2h1z" class="Z"></path><g class="P"><path d="M529 435h0c1 3 1 5 2 8l1 3h1c0-2 0-3-1-4h1 0 1v3c-1 0 0 2 0 3v3c-1-1-2-3-3-5s0-3-1-4c0-3-1-5-1-7z"></path><path d="M534 428l3 2c0 1 0 4-1 5l-1 1h0v1 1h-1l-1-1v1c-1-2-1-3-1-5v-2l1-2 1-1z"></path></g><path d="M530 426c1 2 1 4 2 5v2c0 2 0 3 1 5 0 2 0 3 1 4h-1 0-1c1 1 1 2 1 4h-1l-1-3c-1-3-1-5-2-8h0c0-3 0-6 1-9z" class="g"></path><path d="M525 393l1 1 2 2c1 0 2 1 2 2 2 2 3 3 4 5s2 3 3 5h0c1 1 1 1 2 1v4 3 2c-1 1-1 1-2 1v11l-3-2-1 1-1 2c-1-1-1-3-2-5 0-2-1-3-1-4-2-1-2-2-4-2h0v-2c-1 0-1 0-1-1v-3-9-5l1 1h0v-6l-1-2h1z" class="N"></path><path d="M536 416l1 2v1 11l-3-2v-9-3l1 1 1-1z" class="q"></path><path d="M536 416l1 2c0 2 0 5-1 7l-1-3-1-3v-3l1 1 1-1z" class="t"></path><path d="M524 405c1 2 1 3 2 4l1 1v-1l1-1v1c2 2 4 4 4 7v1l-1 1c-1-2-1-2-2-3-1 0-2 0-3 1-1 0-1 1-1 2-1 0-1 0-1-1v-3-9z" class="l"></path><path d="M526 416l-1-1c0-1 0-1 1-1v-2h1c1-1 1 0 1-1 2 1 3 4 3 5l1 1-1 1c-1-2-1-2-2-3-1 0-2 0-3 1z" class="d"></path><path d="M526 416c1-1 2-1 3-1 1 1 1 1 2 3 2 4 2 7 2 11l-1 2c-1-1-1-3-2-5 0-2-1-3-1-4-2-1-2-2-4-2h0v-2c0-1 0-2 1-2z" class="q"></path><path d="M525 393l1 1 2 2c1 0 2 1 2 2 2 2 3 3 4 5s2 3 3 5h0c1 1 1 1 2 1v4 3 2c-1 1-1 1-2 1v-1l-1-2-1 1-1-1c-2-5-7-10-9-15h0v-6l-1-2h1z" class="X"></path><path d="M525 393l1 1 2 2-2 2-1-3-1-2h1z" class="P"></path><path d="M525 401h0v-6l1 3c4 5 9 11 10 18l-1 1-1-1c-2-5-7-10-9-15z" class="r"></path><defs><linearGradient id="AM" x1="477.617" y1="459.04" x2="495.883" y2="451.96" xlink:href="#B"><stop offset="0" stop-color="#d6d8cf"></stop><stop offset="1" stop-color="#e4dee2"></stop></linearGradient></defs><path fill="url(#AM)" d="M448 333v-4l1-1 28 91 49 162-2-1c-2-2-3-7-3-9-1 1-1 1-2 1l-1 2-3-1v-1l-1-3c0-2 0-2 1-3l-1-1c0 1-1 1-1 1-1-2 0-5-1-6v-1c0-1 0-1-1-2s-1-1-1-2l-1-5-1-1c0-2 0-3-1-5 0 0 1-1 1-2v-2c0-1-1-1-1-1 0-2 1-4 1-6-2-7-3-14-6-21l-18-61-25-80-10-34-1-4z"></path><path d="M508 533l7 22c1 1 1 2 1 3l1 2 4 11c-1 1-1 1-2 1l-1 2-3-1v-1l-1-3c0-2 0-2 1-3l-1-1c0 1-1 1-1 1-1-2 0-5-1-6v-1c0-1 0-1-1-2s-1-1-1-2l-1-5-1-1c0-2 0-3-1-5 0 0 1-1 1-2v-2c0-1-1-1-1-1 0-2 1-4 1-6z" class="v"></path><path d="M515 572v-1c1 0 1-1 2-1 1-3 0-6 0-8-1-1 0-1 0-2l4 11c-1 1-1 1-2 1l-1 2-3-1v-1zm-7-39l7 22c1 1 1 2 1 3l-1-1-3-9-2-5-1-3v1 5c1 2 1 3 1 5v2c1 1 1 2 1 4-1-1-1-1-1-2l-1-5-1-1c0-2 0-3-1-5 0 0 1-1 1-2v-2c0-1-1-1-1-1 0-2 1-4 1-6z" class="AD"></path><defs><linearGradient id="AN" x1="521.764" y1="594.912" x2="519.394" y2="574.454" xlink:href="#B"><stop offset="0" stop-color="#79736a"></stop><stop offset="1" stop-color="#a4a199"></stop></linearGradient></defs><path fill="url(#AN)" d="M521 571c0 2 1 7 3 9l2 1 2 6h1 1v3c1-2 1-4 1-5l2-2h0c1-1 1-3 2-3 1 1 0 1-1 3v3l1 1h1c1-1 0-1 1-1l1-1c0-1 1-1 1-2h1 0v6h0v2 1c-1 1-1 3-1 5-1 2-1 5-2 7s-2 4-1 7l-2 2c-1 1-1 2-1 3l-2 2-4 2v1l-2 1c-1 2-2 3-2 5v3l-1 1c0 1 0 2-1 3h-1c-1-3-1-4 0-7h-3l-1 3v-5l-1-1c0-2 0-5 1-7l-1-4h0l-1-4c0-2-1-4-2-6l-1-4v-2 1c1 1 1 0 1 1v1h1l-1-2v-1-1h0v-3c1 0 1 0 2 1h0v-3h0c-1-1-1 0-1-1l1-1v-3h0-1v4-4l2-2v-2l-1-1v1c-1-1-1-1 0-2l1-1 1-1v-2l2-2 1-2c1 0 1 0 2-1z"></path><path d="M522 595v-1c1-1 1-1 2-1h2l-1 4-3-2z" class="r"></path><path d="M516 586l1-1v-2c1 0 1 0 2 1l1 2 1 1v2c-1 0-1 0-2 1-1 2 1 5 2 7h0c-1 1-1 1-3 2v-2h0v-3c-1-1-1-2-2-2-1-2-1-4 0-6z" class="AM"></path><path d="M514 591c0-2 1-4 1-6l1 1c-1 2-1 4 0 6 1 0 1 1 2 2v3h0v2c2-1 2-1 3-2 0 3-1 6-2 8h0c-1-2-1-2-3-3v-2-1l-1-1c-1 1-1 2-1 3l-2 2-1-4v-2 1c1 1 1 0 1 1v1h1l-1-2v-1-1h0v-3c1 0 1 0 2 1h0v-3z" class="AK"></path><path d="M524 580l2 1 2 6h1 1v3c-1 2-1 5-2 7l-5 15-3 9-3 6-1 3v-5l-1-1c0-2 0-5 1-7l-1-4h0l-1-4c0-2-1-4-2-6l2-2c0-1 0-2 1-3l1 1v1 2c2 1 2 1 3 3h0c1-2 2-5 2-8h0l1-2 3 2 1-4c0-4-1-9-2-13z" class="AH"></path><path d="M522 595l3 2-5 14v-2c-1-1-1-1-2-1h0c-1-2-2-4-2-6 2 1 2 1 3 3h0c1-2 2-5 2-8h0l1-2z" class="q"></path><path d="M512 603l2-2c0-1 0-2 1-3l1 1v1 2c0 2 1 4 2 6h0c1 0 1 0 2 1v2c0 2-2 8-3 9l-1-3-1-4h0l-1-4c0-2-1-4-2-6z" class="g"></path><path d="M515 604c1 3 2 7 0 9h0l-1-4c1-2 1-3 1-5z" class="t"></path><path d="M512 603l2-2c0-1 0-2 1-3l1 1v1l-1 1v3c0 2 0 3-1 5 0-2-1-4-2-6z" class="r"></path><path d="M533 583c1-1 1-3 2-3 1 1 0 1-1 3v3l1 1h1c1-1 0-1 1-1l1-1c0-1 1-1 1-2h1 0v6h0v2 1c-1 1-1 3-1 5-1 2-1 5-2 7s-2 4-1 7l-2 2c-1 1-1 2-1 3l-2 2-4 2v1l-2 1c-1 2-2 3-2 5v3l-1 1c0 1 0 2-1 3h-1c-1-3-1-4 0-7h-3l3-6 3-9 5-15c1-2 1-5 2-7s1-4 1-5l2-2h0z" class="Ab"></path><path d="M520 621v1c0 1 0 2 1 3h0c-1 1-1 2-1 2h-3l3-6z" class="AT"></path><path d="M525 622h-1l-1-1c-1-1-1 0-1-1 1-2 2-3 4-4l1 1h1c0 1 0 1-1 1l-1 1 1 2-2 1z" class="Aa"></path><path d="M534 607h1c0-1 1-2 2-3-1 2-2 4-1 7l-2 2c-1 1-1 2-1 3l-2 2-1-1c2-3 3-6 4-10z" class="AV"></path><path d="M537 586l1-1c0-1 1-1 1-2h1 0v6h0l-3 4h-1 0-1l1-1h-1c-2-1-2-1-2-3l1-1 2 1h1v-3z" class="x"></path><path d="M540 589v2 1c-1 1-1 3-1 5-1 2-1 5-2 7-1 1-2 2-2 3h-1l3-14 3-4z" class="AX"></path><defs><linearGradient id="AO" x1="490.509" y1="555.611" x2="507.504" y2="547.383" xlink:href="#B"><stop offset="0" stop-color="#97948e"></stop><stop offset="1" stop-color="#b3b0a9"></stop></linearGradient></defs><path fill="url(#AO)" d="M483 501l1-1v1c1 1 1 2 1 2 1 3 2 8 3 10v-3h0c0-1 0-1-1-2v-2c-1-1-1-2-1-3l2 3v2c0 1 1 1 2 2 2 1 2 1 3 3v-4l2-1c0-1 0-1-1-2v-1-1l1 3 1-1h1s1 1 1 2c1 1 2 2 3 4h1c3 7 4 14 6 21 0 2-1 4-1 6 0 0 1 0 1 1v2c0 1-1 2-1 2 1 2 1 3 1 5l1 1 1 5c0 1 0 1 1 2s1 1 1 2v1c1 1 0 4 1 6 0 0 1 0 1-1l1 1c-1 1-1 1-1 3l1 3v1l3 1-2 2v2l-1 1-1 1c-1 1-1 1 0 2v-1l1 1v2l-2 2v4-4h1 0v3l-1 1c0 1 0 0 1 1h0v3h0c-1-1-1-1-2-1v3h0v1 1l1 2h-1v-1c0-1 0 0-1-1v-1 2l-3-12-5-18-20-68z"></path><path d="M500 539c1 2 3 5 3 8v2s1 1 0 2h0-1v1h0l-2-13z" class="AD"></path><path d="M514 569l1 3v1l3 1-2 2v2c-2-1-1-2-1-3l-3-3c1-2 1-2 2-3zm-20-65l1 3 1-1h1s1 1 1 2c1 1 2 2 3 4h1c3 7 4 14 6 21 0 2-1 4-1 6 0 0 1 0 1 1v2c0 1-1 2-1 2h0c-2-1-3-4-4-6l-6-12h0c-2-2-3-4-4-7s-4-5-3-9c2 1 2 1 3 3v-4l2-1c0-1 0-1-1-2v-1-1z" class="AY"></path><path d="M497 526l1-2 2 4 1 1 1 1v-1-1c2 3 5 7 5 11 0 0-1 0-1 1l1 2v2c-2-1-3-4-4-6l-6-12z" class="AS"></path><g class="AT"><path d="M496 506h1s1 1 1 2c1 1 2 2 3 4l3 9c-1 0-1 1-1 2l1 1v2h-1s-1-1-1-2 0-1-1-2-1-2-2-3c-1-2-1-4-1-6h1c-2-2-2-4-3-6v-1z"></path><path d="M459 371l25 80 18 61h-1c-1-2-2-3-3-4 0-1-1-2-1-2h-1l-1 1-1-3v1 1c1 1 1 1 1 2l-2 1v4c-1-2-1-2-3-3-1-1-2-1-2-2v-2l-2-3c0 1 0 2 1 3v2c1 1 1 1 1 2h0v3c-1-2-2-7-3-10 0 0 0-1-1-2v-1l-1 1c-3-8-5-16-7-23-1-4-2-7-3-10 0-1 0-1 1-2s0-3 0-4c0-3 0-7-1-10v-2c-2-1-2-1-2-3v-3c-1-1-1-1-1-2v-2c-1-1-1-1-1-2v-1c0-1 0-2-1-2v-1-1-2l-1-1v-2h0c-1-1-1-2-1-3 1-1 1-2 0-3v-3l-1-5c-1-2-1-5-1-7 0-1 0-1-1-2v-4c-1-1-1-2-1-3v-2l-1-2-1-5v-1c-1-3-1-6-1-9-1-3 0-5 0-8z"></path></g><path d="M485 503l-3-14c1 2 2 4 4 6v1 3c1 1 1 1 1 2s0 2 1 3c0 1 1 2 1 4l1 2c-1-1-2-1-2-2v-2l-2-3c0 1 0 2 1 3v2c1 1 1 1 1 2h0v3c-1-2-2-7-3-10zm-21-96c1 2 1 4 2 6 1 1 1 2 1 3 1 2 1 5 2 7 1 1 0 4 0 7 1 2 2 6 2 9v2c0 1 0 1 1 2v4c1 1 1 2 1 3-2-1-2-1-2-3v-3c-1-1-1-1-1-2v-2c-1-1-1-1-1-2v-1c0-1 0-2-1-2v-1-1-2l-1-1v-2h0c-1-1-1-2-1-3 1-1 1-2 0-3v-3l-1-5c-1-2-1-5-1-7z" class="AD"></path><path d="M494 504l-3-6c-1-2-3-6-2-8 1-1 1 0 1-1h1c1 0 1 0 1 1 1 2 2 5 4 6v-2l-1-1 1-1h-2l-1-3v-2h0c-2 0-2 0-4 1l-1-1s0-1-1-2v-1c0-1 1-1 2-1 0 1 1 2 2 3h2c-1-5-3-9-4-14l-5-16v-1c-1-1-1-2-1-4h1l18 61h-1c-1-2-2-3-3-4 0-1-1-2-1-2h-1l-1 1-1-3z" class="AW"></path><path d="M512 211c1 1 1 2 2 3 0 1 1 2 1 3l3 2 3-3h0 1c3 3 7 5 12 5h5c5 0 10-1 15-2l3-1-1 2-3 1c1 1 2 2 3 4h1l-1 1c-1 1-2 2-4 3h0-3c-1 1-2 0-3 1-1 0-1 0-2 1-2 0-2 1-4 1l-1 1h0v1c-1 1-2 3-3 4-2 2-4 3-6 5l-2 1c-5 5-11 11-13 18v1c-1 0-1-1-2-2v1 1 2c-1 1-1 2-2 2v8 6 2c-1-2-1-4-1-6-1-2-1-4-1-6v-2c0-2-1-3-2-4v-1c0-1 0-1-1-2 1-1 1-1 1-2 0-2-1-4-2-5l-6-7 1-1h1c1 0 0 0 1-1l-1-1c-1 0-1 0-1-1-1-1-1-1 0-3-1 0-4-1-5-2l-3-1c1-1 1-1 1-2l-4-4v-2l4 1v-1l1-1-2-1c2 0 6 1 8 0 1-1 1-1 2-1l1 1 1-1c2 0 2-1 3-2l2-2h0l-1-1-1-1c0-1-1-1-1-2l-1 1v2h0c-2 0-3-1-4-3 3-1 5-1 7-3 1-1 3-3 4-5z" class="AP"></path><path d="M500 238l3 2v4l-3-3c-1 0-4-1-5-2l2-1c2 0 1 1 3 0z" class="r"></path><path d="M520 236h1l1 1-1 1c-1 0-3 1-4 3h1 1c1 0 2 0 3-1h1v1l-1 1v2 1c-2-1-4-1-5-1l-1 1h0v1c-2-1 0-2-1-3h-1l-1 3h0v-3l-1-1v-1c1-1 3-1 4-3l2 1 2-3z" class="AH"></path><path d="M502 230l1-1h1 4l3 1v1l1 1 2-1c-1 3-2 5-2 7l-1 1v1h-1l1-2v-1c-2 0-2 0-4 1h0c-1-1-1 0-1 0h-1-1l-1-1c-1 0-1 0-2-1h3l-1-2 1-2s-1-1-2-1v-1z" class="t"></path><path d="M502 230l1-1h1 4l3 1-1 1c-2 0-3 2-4 3l-2-2s-1-1-2-1v-1z" class="g"></path><path d="M513 261c0-1 0-1 1-2l-1-2s1 0 1-1l1-1c0-1 1-1 1-2h0c0-1 0-1 1-1 1-4 4-6 7-9 1 0 1-1 2-2l2-1c1 0 0 0 1 1-1 0-1 0-2 1l1 2c-5 5-11 11-13 18v1c-1 0-1-1-2-2z" class="AH"></path><path d="M524 226h2 0c0 1 0 1 1 2l-1 1-2 2 2 2h-1-1l-1 1s0 1-1 1c0-1 0 0-1-1h-1c-1-1-1 0-1-1-1 1-2 1-3 2-1-1 0-2-1-4h-1l-2 1-1-1v-1l-3-1 2-1 2 1h0l3-2v1c1 1 2 1 3 1 0-1 0-1 1-2 0 1 0 1 1 2l1-1-1-1c2-1 2-1 4-1z" class="w"></path><path d="M524 226h2 0c0 1 0 1 1 2l-1 1c-1 0-2 1-3 0 0-1 1-1 1-2v-1z" class="g"></path><path d="M492 228c2 0 6 1 8 0 1-1 1-1 2-1v3 1c1 0 2 1 2 1l-1 2 1 2h-3l-1-1c-1-1-4-2-6-3 1 2 2 3 4 4l2 2c-2 1-1 0-3 0l-2 1-3-1c1-1 1-1 1-2l-4-4v-2l4 1v-1l1-1-2-1z" class="w"></path><path d="M492 228c2 0 6 1 8 0 1-1 1-1 2-1v3 1c-1 0-1 0-2-1-2-1-3-1-5 0v2l-2-1v-1l1-1-2-1z" class="b"></path><path d="M500 241l3 3h-1c1 1 2 1 2 2l6 8c1 0 0 0 1-1h2 2v1h-4l-1 1v1c2 2 2 4 3 6v1 2c-1 1-1 2-2 2v8 6 2c-1-2-1-4-1-6-1-2-1-4-1-6v-2c0-2-1-3-2-4v-1c0-1 0-1-1-2 1-1 1-1 1-2 0-2-1-4-2-5l-6-7 1-1h1c1 0 0 0 1-1l-1-1c-1 0-1 0-1-1-1-1-1-1 0-3z" class="AH"></path><defs><linearGradient id="AP" x1="510.491" y1="261.831" x2="507.083" y2="269.991" xlink:href="#B"><stop offset="0" stop-color="#37312f"></stop><stop offset="1" stop-color="#4c4d49"></stop></linearGradient></defs><path fill="url(#AP)" d="M500 247h1c5 6 9 12 10 20v8 6 2c-1-2-1-4-1-6-1-2-1-4-1-6v-2c0-2-1-3-2-4v-1c0-1 0-1-1-2 1-1 1-1 1-2 0-2-1-4-2-5l-6-7 1-1z"></path><path d="M512 211c1 1 1 2 2 3 0 1 1 2 1 3l3 2 3-3h0 1c3 3 7 5 12 5l-3 2 1 1-1 1-2-2-1 1c0 1-1 1-2 2h-2c-2 0-2 0-4 1l1 1-1 1c-1-1-1-1-1-2-1 1-1 1-1 2-1 0-2 0-3-1v-1l-3 2h0l-2-1-2 1h-4-1l-1 1v-3l1 1 1-1c2 0 2-1 3-2l2-2h0l-1-1-1-1c0-1-1-1-1-2l-1 1v2h0c-2 0-3-1-4-3 3-1 5-1 7-3 1-1 3-3 4-5z" class="t"></path><path d="M517 225c0-1 2-2 2-3 3 0 3 0 5 1-2 2-2 1-3 1s-2 1-2 1h-2zm-2-8c1 2 1 2 2 3l1 1h-2l-2-2v-1l-2 1c-1 0-2-1-3-1 0-1 1-1 1-2l2-2h2c0 1 1 2 1 3z" class="g"></path><path d="M524 223c2-1 3-1 5-1l1 1h1l1 1-1 1-2-2-1 1c0 1-1 1-2 2h-2c-2 0-2 0-4 1l1 1-1 1c-1-1-1-1-1-2-1 1-1 1-1 2-1 0-2 0-3-1v-1l-3 2h0l-2-1-2 1h-4-1l-1 1v-3l1 1 1-1c2 0 2-1 3-2l1 1 1-1s1 1 2 1 1 0 2-1l1 1c0-1 1-1 2-1h1 2s1-1 2-1 1 1 3-1z" class="l"></path><path d="M554 219l3-1-1 2-3 1c1 1 2 2 3 4h1l-1 1c-1 1-2 2-4 3h0-3c-1 1-2 0-3 1-1 0-1 0-2 1-2 0-2 1-4 1l-1 1h0l-2 1-1 1-2-1-1 1v1c-1 1-1 1-2 1h0c1-1 1-1 1-2v-1c2 0 3-1 4-2v-1c-3 1-5 3-8 3h0l-1 1-2-2h1l-2-2 2-2 1-1c-1-1-1-1-1-2h0c1-1 2-1 2-2l1-1 2 2 1-1-1-1 3-2h5c5 0 10-1 15-2z" class="AU"></path><path d="M541 225l2-2h1c1 1 2 2 3 4-1 0-1 1-3 0v-1h-1c-1 0-1 0-2-1zm-4 0h1c0 1 1 1 2 1h0l-5 3c-1-1-1-1-1-3 1 0 2 0 3-1z" class="P"></path><path d="M552 222l1-1c1 1 2 2 3 4h1l-1 1c-1 1-2 2-4 3h0l-1-1-1-1c0-3 0-3 2-5z" class="b"></path><path d="M551 228c1-1 2-3 3-3s1 0 2 1c-1 1-2 2-4 3h0l-1-1z" class="Y"></path><path d="M554 219l3-1-1 2-3 1-1 1h-5c0 1-1 1-2 1h-1-1l-2 2v-1h-1-1v-3c5 0 10-1 15-2z" class="t"></path><path d="M534 221h5v3c-1 0-1 0-2 1s-2 1-3 1h0l-3 2h0v-2h0l-2 2h1c-1 1-2 1-3 2l-1 3-2-2 2-2 1-1c-1-1-1-1-1-2h0c1-1 2-1 2-2l1-1 2 2 1-1-1-1 3-2z" class="q"></path><defs><linearGradient id="AQ" x1="586.208" y1="537.181" x2="539.648" y2="507.546" xlink:href="#B"><stop offset="0" stop-color="#d0ccc0"></stop><stop offset="1" stop-color="#f2f0eb"></stop></linearGradient></defs><path fill="url(#AQ)" d="M565 469c0 1 0 2-1 3h0v2 1c0 1 0 1 1 2-1 3-1 8 0 11s2 6 4 8c1 2 3 4 4 6 2 4 3 8 5 12l1 4-8 28-4 13c0 2-1 4-1 5l-5 18c-1-2-1-5-1-7 2-2 1-7 2-9h-2l-2 1c-1 1-1 1-1 2-1 0-1 0-2-1v1h-3c-1-1 0-2 0-3h-3l1-1c1 0 1 0 1-1v-1-1l-1-2h-2 0c0-1 0-2-1-3h0l1-2h0c-1-2-1-5-2-8v-1c1-2 2-6 2-9l6-21c1-1 2-4 2-6 2-4 3-9 4-14l3-15c-2-4 1-6 1-10 1-1 1-2 1-2z"></path><path d="M568 519l2 2h2c1 2 1 2 0 5l-1 1v-2l-1-1c-2-1-3-2-4-4l2-1z" class="AX"></path><path d="M559 514h0l2 1h0l-1-2h1c1 1 2 1 3 3 1 1 2 2 4 3l-2 1-7-6z" class="AH"></path><path d="M561 553c0-1 0 0-1-1v-1c0-2 1-4-1-6h0c0-1 1-1 1-2 0-2 1-2 1-3v-1h1c1 1 2 1 4 2h1l-6 19v-7z" class="v"></path><path d="M548 537c1 1 1 1 3 1l1-2c1 1 0 1 2 1l-1-1c0-1-1-2-1-4 1-1 1-2 2-3h0l1-1h0c0-1-1-1-1-2l1-1c1 0 1 0 2 1h1 0v-3c-1-1-2-1-3-2l1-4c1-2 1-2 3-3l7 6c1 2 2 3 4 4l1 1v2c0 2-1 5-1 8 0 2 0 5-1 7v-2c-1-1-1-2-1-2v-1c-1 1-1 1-1 3v1h0-1c-2-1-3-1-4-2h-1v1c0 1-1 1-1 3 0 1-1 1-1 2h0c2 2 1 4 1 6v1c1 1 1 0 1 1-1-1-2-1-2-2v-2c0-1-2-1-2-2l-1 1 1 1c0 1-1 3 0 4 2 2 2 4 2 6l1 1 1 1c1 1 1 3 1 5h-2l-2 1c-1 1-1 1-1 2-1 0-1 0-2-1v1h-3c-1-1 0-2 0-3h-3l1-1c1 0 1 0 1-1v-1-1l-1-2h-2 0c0-1 0-2-1-3h0l1-2h0c-1-2-1-5-2-8v-1c1-2 2-6 2-9z" class="AN"></path><path d="M562 532h4v3h-1v1s-1 0-1-1c-1-1-2-1-2-3z" class="AX"></path><path d="M548 555h1v2l1 1 1-1v-3l-1-1v-1l-1-1c-1-1 0-3-1-5h1v4c1-1 0-1 1-1l1-1c-1-2-1-1-1-2l1-1c1 1 0 2 1 3v2h1c1 0 1 0 2 1l1 1c1 1 2 2 2 4l1 3v2h2c1 1 1 3 1 5h-2l-2 1c-1 1-1 1-1 2-1 0-1 0-2-1v1h-3c-1-1 0-2 0-3h-3l1-1c1 0 1 0 1-1v-1-1l-1-2h-2 0c0-1 0-2-1-3h0l1-2h0z" class="v"></path><path d="M551 563l1-1c1 0 1 1 2 2l-1 2h-1-3l1-1c1 0 1 0 1-1v-1z" class="x"></path><path d="M392 167v-1c2 0 7 7 9 9h0c0 2 2 3 2 4l3 3c2 0 3 2 4 2h0c0-2 0-3-1-4 2 2 4 4 7 6v1l3 3h1c2 2 6 4 7 6 1 1 1 2 2 3v1c1 3 4 4 7 6s7 5 11 6c3 1 6 3 9 3 1 0 3 1 4 1 2 1 3 1 5 1-1 0 0 0-1-1v-1l-1-1h0c3 0 7 2 10 3 1 0 3 0 3 1 4 1 8 2 11 3 1 0 3-1 4 0 3-1 7-1 10-2 1 2 2 3 4 3h0v-2l1-1c0 1 1 1 1 2l1 1 1 1h0l-2 2c-1 1-1 2-3 2l-1 1-1-1c-1 0-1 0-2 1-2 1-6 0-8 0l2 1-1 1v1l-4-1v2l4 4c0 1 0 1-1 2l3 1c1 1 4 2 5 2-1 2-1 2 0 3 0 1 0 1 1 1l1 1c-1 1 0 1-1 1h-1l-1 1 6 7c1 1 2 3 2 5 0 1 0 1-1 2 1 1 1 1 1 2v1c-2 3 1 8 0 12v2c-2-1-2-2-2-4h0c-1-1-1-1-1-2h0l-2-2c0-1-1-2-2-3l-3-2v1c0 1 1 2 1 3h0c-1 0-2 0-3-1h-1v-1c-1 1-1 0-2 1l-1-1v-2h-3-1v1l-1 1-1-1h-2c-1-1-2-1-2-1-3-1-4-2-6-4h-2c-1-1-1-2-2-3h1 1 1l-1-1v-1c0-1-1-1-2-2h0c-1-1-1-2-1-3h0-2l-2-2 1-1c1-2 3-22 3-24h-3-5c-3-1-6-1-9-1-3-1-6-3-8-4l-4-2-4-2h-1c-2-2-5-3-7-5v-1c0 1 0 1-1 1l-2-2c-1-1-1-2-2-3-3-1-4-4-6-4-2-1-1-1-1-2-1-1-3-2-4-3h-1c-1 0-1 0-1-1-2-1-2-3-5-3h0a30.44 30.44 0 0 0-8-8c-1-2-3-4-5-5v-1c-1-2-2-3-3-5h1v-1l-2-1h2v-5h1z" class="C"></path><path d="M487 251l1-3-1-1 1-2h1l3 2c0 1-1 2-1 4-1-1 0-2-2-3v2l-2 1z" class="Z"></path><path d="M474 249l-1 1h-1v-2c0-1 1-3 2-3l1-1c0-1 0 0 1-1l1 2-1 1h0c1 1 1 2 1 2l-1 1h-2z" class="Y"></path><path d="M477 245h1v-1-1l1-2h1c1 2 2 4 4 5h1l1 1-1 1-1-1h-2 0c-3 1-4 1-5 3-1 1-1 1 0 2l-1 1-2-4h2l1-1s0-1-1-2h0l1-1z" class="N"></path><path d="M478 237l2-1 8 4c2-1 9 4 10 5l2 2-1 1c-3-2-9-7-12-7h-2c-2-2-4-4-7-4z" class="X"></path><path d="M488 240c0-1-1-2-1-3h1c2 0 3 1 4 1l3 1c1 1 4 2 5 2-1 2-1 2 0 3 0 1 0 1 1 1l1 1c-1 1 0 1-1 1h-1l-2-2c-1-1-8-6-10-5z" class="x"></path><path d="M392 167v-1c2 0 7 7 9 9h0c0 2 2 3 2 4l3 3v2c-2-1-3-1-5-3l1-1c-1 0-2-1-3-1 0-2-2-4-4-5l-3-4v-2-1z" class="l"></path><path d="M392 167h1v1l1 1c1 2 1 3 1 5l-3-4v-2-1z" class="N"></path><path d="M437 210c4-1 7 3 11 4 2 1 4 2 7 3h1c2 0 2 1 4 2 2 0 3 1 5 3-9-1-21-6-28-12z" class="g"></path><defs><linearGradient id="AR" x1="469.381" y1="215.361" x2="469.639" y2="221.618" xlink:href="#B"><stop offset="0" stop-color="#595657"></stop><stop offset="1" stop-color="#716e6e"></stop></linearGradient></defs><path fill="url(#AR)" d="M463 214c3 0 7 2 10 3 1 0 3 0 3 1l2 1v1l-1 1h0-3v1 1h-1c-3-1-6-1-8-1-2-2-3-3-5-3-2-1-2-2-4-2 2 0 3 0 4-1 2 1 3 1 5 1-1 0 0 0-1-1v-1l-1-1h0z"></path><path d="M492 247l3 2-2 1v1c1 0 2 1 2 1l-1 3c-1 1-2 2-4 2v1c-2 1-3 1-4 1-1 1-2 1-3 1-1-1-3-1-5-1v-1h-2c-2-1-2-1-2-3 1 1 2 1 3 2h1l-1-1c1 0 2-1 2-1-1 0-2-1-3-2l1-1c1 1 2 2 4 2 3 0 4-1 6-3l2-1v-2c2 1 1 2 2 3 0-2 1-3 1-4z" class="P"></path><path d="M475 224l7 1 9 2 1 1 2 1-1 1v1l-4-1v2l4 4c0 1 0 1-1 2-1 0-2-1-4-1h-1c0 1 1 2 1 3l-8-4-2 1-2-1-4-2v-1l3-3h1 0c-2-1-2-1-3-1l1-1 2-1h1v-1l-2-2z" class="N"></path><path d="M476 236c2-2 2-3 4-4h-3l1-2h2c0-1 0-1 1-1l2 2c2 1 1 0 2 2h0c-2 1-4 2-5 3l-2 1-2-1z" class="l"></path><path d="M481 229c3 0 5 0 8 1v2l4 4c0 1 0 1-1 2-1 0-2-1-4-1h-1c0 1 1 2 1 3l-8-4c1-1 3-2 5-3h0c-1-2 0-1-2-2l-2-2z" class="g"></path><path d="M476 218c4 1 8 2 11 3 1 0 3-1 4 0 3-1 7-1 10-2 1 2 2 3 4 3h0v-2l1-1c0 1 1 1 1 2l1 1 1 1h0l-2 2c-1 1-1 2-3 2l-1 1-1-1c-1 0-1 0-2 1-2 1-6 0-8 0l-1-1-9-2-7-1-2-1h1v-1-1h3 0l1-1v-1l-2-1z" class="q"></path><path d="M491 227c1 0 2-1 2-1 2 0 5 1 7 0 1 0 0-1 2-1 1 0 1 1 2 2h0l-1 1-1-1c-1 0-1 0-2 1-2 1-6 0-8 0l-1-1z" class="AU"></path><path d="M491 221c3-1 7-1 10-2 1 2 2 3 4 3h0l1 2h-2c-1-1-2 0-3 0h0c-2 0-4 0-6 1h-2-1l2-1v-2c-1 0-2-1-3-1z" class="r"></path><path d="M495 225v-1l2-3h3l-1 2c1 0 1 0 2 1-2 0-4 0-6 1zm-19-7c4 1 8 2 11 3 1 0 3-1 4 0 1 0 2 1 3 1v2l-2 1h-5-3-2l-7-1-2-1h1v-1-1h3 0l1-1v-1l-2-1z" class="AK"></path><path d="M484 225l1-2c1 0 2-1 3 0s1 0 1 1l-2 1h-3z" class="AP"></path><path d="M409 180c2 2 4 4 7 6v1l3 3h1c2 2 6 4 7 6 1 1 1 2 2 3v1c1 3 4 4 7 6s7 5 11 6c3 1 6 3 9 3 1 0 3 1 4 1-1 1-2 1-4 1h-1c-3-1-5-2-7-3-4-1-7-5-11-4l-24-19c-3-1-5-4-7-6h0v-1-2c2 0 3 2 4 2h0c0-2 0-3-1-4z" class="r"></path><path d="M419 190h1c2 2 6 4 7 6 1 1 1 2 2 3v1c-1 0-2 0-3-1h0c-2-2-4-4-6-5-1-1-2-1-2-3l1-1z" class="Y"></path><path d="M409 180c2 2 4 4 7 6v1l3 3-1 1c0 2 1 2 2 3l-1 1-5-5-1 1c-3-1-5-4-7-6h0v-1-2c2 0 3 2 4 2h0c0-2 0-3-1-4z" class="b"></path><path d="M406 185l1-1 2 2 1-1c2 1 3 5 5 4l1 1c1-1 1-2 0-3l3 3-1 1c0 2 1 2 2 3l-1 1-5-5-1 1c-3-1-5-4-7-6z" class="l"></path><path d="M495 249c3 3 8 6 9 10h0l2 3c1 1 1 1 1 2v1c-2 3 1 8 0 12v2c-2-1-2-2-2-4h0c-1-1-1-1-1-2h0l-2-2c0-1-1-2-2-3l-3-2v1c0 1 1 2 1 3h0c-1 0-2 0-3-1h-1v-1c-1 1-1 0-2 1l-1-1v-2h-3-1v1l-1 1-1-1h-2c-1-1-2-1-2-1-3-1-4-2-6-4h-2c-1-1-1-2-2-3h1 1 1l-1-1v-1c0-1-1-1-2-2h3c0 2 0 2 2 3h2v1c2 0 4 0 5 1 1 0 2 0 3-1 1 0 2 0 4-1v-1c2 0 3-1 4-2l1-3s-1-1-2-1v-1l2-1z" class="t"></path><path d="M496 259c1-1 1-2 3-3 2 1 3 3 5 3l2 3c1 1 1 1 1 2h-2c-2 0-4-3-5-4v-1l-1 1c-1-1-2-1-3-1z" class="w"></path><path d="M495 262l2-1h0 3c1 1 2 3 4 4v1c0 1 0 1 1 2v1c-2 0-1 0-3-1h-2l-3-2v1c0 1 1 2 1 3h0c-1 0-2 0-3-1h-1v-1c-1 1-1 0-2 1l-1-1v-1c1 0 1-1 1-2h2c1-1 2 0 3 0h1 2l-2-2h-2l-1-1z" class="P"></path><path d="M495 249c3 3 8 6 9 10h0c-2 0-3-2-5-3-2 1-2 2-3 3l-4 2s-1 1-2 1h-7-1v-1h0 1 1l-1-1c1 0 2 0 3-1 1 0 2 0 4-1v-1c2 0 3-1 4-2l1-3s-1-1-2-1v-1l2-1z" class="Z"></path><g class="Y"><path d="M495 252c2 1 2 1 2 2 0 2-2 3-3 5l-1 1-1-1v-1l2-2v-1l1-3z"></path><path d="M471 255h3c0 2 0 2 2 3h2v1c2 0 4 0 5 1l1 1h-1-1 0v1c2 1 8 1 10 1h1 2v-1l1 1h2l2 2h-2-1c-1 0-2-1-3 0h-2c0 1 0 2-1 2v1-2h-3-1v1l-1 1-1-1h-2c-1-1-2-1-2-1-3-1-4-2-6-4h-2c-1-1-1-2-2-3h1 1 1l-1-1v-1c0-1-1-1-2-2z"></path></g><path d="M598 364h0c0 1 0 1-1 2 0 2 1 4 0 6v2c1 1 1 2 1 4v1h2v3c2-1 3-1 4-2-1 0-3-2-3-3h5c0 3 2 5 2 8 0 1 1 1 1 2 2 5 1 10 1 15l1-6 1-3 1-1 1-4 1 1 1-1c1 0 1 1 3 2l2-2s1 0 2 1h0 1 3l1-2h0c0 2-1 4-1 7l-5 15h0c-1 2-3 13-3 14h0v4h0l1-1v1l-7 26-10 35h-1c0-1 1-1 1-2v-1-1-1h1v-4 1l-1 1c-1 1-1 2-2 3v1l-1 3c-2-3-2-4-3-7v-3h-2c-1 0 2 0-1 0h-1v1c-1 0-1 1-1 2l-1-1c-1 1-1 1-1 3v1 1c-1 1-1 1-1 2v1c-1 2-1 3-3 4l-3 11c-1 2-2 5-3 7v3c0 2-1 3-1 5l-1-4c-2-4-3-8-5-12-1-2-3-4-4-6-2-2-3-5-4-8s-1-8 0-11c-1-1-1-1-1-2v-1-2h0c1-1 1-2 1-3v-5c0-3 2-8 3-11l8-27v-8c0-3 1-6 2-10 0-3 0-6 1-9 1-1 1-1 2-3 1-3 2-7 4-10 1 1 1 2 1 3l1-4 2 2v1h1l3-11c0-1 0-2 1-2 1-2 1-3 1-5l2-6h1z" class="AX"></path><path d="M585 416c1 0 2 1 3 1v2h-2l-1-1v-2z" class="x"></path><path d="M587 401l3 3-3 3v1h1l2-1h0c1 1 1 2 0 3 1 1 1 1 2 1h1v1c-1 1-1 1-1 3v1c1 2 1 4 2 6-1 0 0 1-1 1h-2 0c0 2-1 3-1 4l-1 1v-1-1-2c0-2 0-4 1-7l-1-2h-2c-1-1-1-1-1-2l1-1v-1c-2 1-3 2-4 4l4-14zm-5 47v-2c-1-1-1-1-1-2l-1-2-1 1-1-1c0-3 2-2 4-4h1c0 2 1 3 1 5v4h1l2-2c1-1 1 0 3-1 0 3 0 6-1 9v1c0 1-1 1-1 2h-1 0c-1-1-2-1-3-2v2c-1-1-1-2-1-3l-2-2 1-3z" class="AV"></path><path d="M582 448c1 1 2 2 4 3-1 1-2 1-3 2l-2-2 1-3z" class="x"></path><path d="M580 412c1 1 1 0 1 1h1l1-4c0 1 1 1 0 3v1h0c-1 1-1 2-1 3-1 2-2 4-1 6l-7 25-2 9c-1 2-1 4-2 5 0 1-1 3-1 4h0v-1l-1-1c-1 1-1 2-1 3-1 4-2 8-2 11-1-1-1-1-1-2v-1-2h0c1-1 1-2 1-3v-5c0-3 2-8 3-11l8-27 4-14z" class="v"></path><path d="M573 462v-3l1-1v-1c0-1 2-2 3-2-1-1-1-2-1-3 1-1 1-1 1-2v-1c0-1 0-1 1-2 1 1 1 2 1 4h2l2 2c0 1 0 2 1 3v-2c1 1 2 1 3 2h0 1 3c0 2-1 3-1 5 0 1-1 2-1 2l1 1 1 1-4 9c0-2 1-4 0-5h-1v2h-1v-2c0-2 0-3-1-4l-3 3c0-1 0 0-1-1l-1-1h-2c-1 0-1-1-2-1-1-1-1-2-2-3z" class="AN"></path><path d="M584 456v-2c1 1 2 1 3 2h0 1 3c0 2-1 3-1 5 0 1-1 2-1 2 0 3-2 5-3 7v-10c-1-1-2-2-2-4z" class="AX"></path><path d="M573 462c1-1 2-2 3-2h0c2 0 4-1 5 1h0l2-1c1 2 0 3 1 5l-3 3c0-1 0 0-1-1l-1-1h-2c-1 0-1-1-2-1-1-1-1-2-2-3z" class="x"></path><defs><linearGradient id="AS" x1="577.934" y1="402.04" x2="583.736" y2="404.802" xlink:href="#B"><stop offset="0" stop-color="#3e1210"></stop><stop offset="1" stop-color="#331e18"></stop></linearGradient></defs><path fill="url(#AS)" d="M593 377l1 1c-2 5-3 10-5 15 0 3-1 5-2 8l-4 14-2 7c-1-2 0-4 1-6 0-1 0-2 1-3h0v-1c1-2 0-2 0-3l-1 4h-1c0-1 0 0-1-1l-4 14v-8c0-3 1-6 2-10 0-3 0-6 1-9 1-1 1-1 2-3 1-3 2-7 4-10 1 1 1 2 1 3l1-4 2 2v1h1l3-11z"></path><path d="M593 377l1 1c-2 5-3 10-5 15 0 3-1 5-2 8l-4 14-2 7c-1-2 0-4 1-6 0-1 0-2 1-3h0v-1c1-2 0-2 0-3l-1 4h-1c0-1 0 0-1-1l6-23 1-4 2 2v1h1l3-11z" class="AY"></path><path d="M598 364h0c0 1 0 1-1 2 0 2 1 4 0 6v2c1 1 1 2 1 4v1h2v3h0l2 2h-1l-1 1c0 2 1 3 0 4l-1 1s-1 1-1 2v2 3l-2 2c-1 1-1 1-3 1h-1v1h-1c0 1 1 1 1 2l1 1h0-3l-3-3c1-3 2-5 2-8 2-5 3-10 5-15l-1-1c0-1 0-2 1-2 1-2 1-3 1-5l2-6h1z" class="x"></path><path d="M596 399l-1-2v-2l-1-1h1c1 0 2 1 3 3l-2 2z" class="AV"></path><path d="M598 364h0c0 1 0 1-1 2 0 2 1 4 0 6v2c1 1 1 2 1 4v1h2v3h0l2 2h-1l-1 1c0 2 1 3 0 4l-1 1s-1 1-1 2h-2 0-1c-1 1-1 1-2 1 0-2 0-2 1-4-1-2 1-4 1-6v-6-1h0l-1 2-1-1c0-1 0-2 1-2 1-2 1-3 1-5l2-6h1z" class="AO"></path><path d="M598 378v1h2v3h0l2 2h-1l-1 1c0 2 1 3 0 4l-1 1s-1 1-1 2h-2v-2c1-1 0-1 1-2 0-1 0-1-1-2 2-2 1-6 2-8z" class="AN"></path><path d="M570 461v6l1 1c1 0 2-1 2-1h2c1 2 0 3 1 4h1 2 0l2-2v-1l3-3c1 1 1 2 1 4v2h1v-2h1c1 1 0 3 0 5-2 5-3 10-6 15 0 0 1 1 1 2 0 0-1 1-1 3 0 1 0 3-1 4v2c0 1 0 2 1 3h2c-1 2-2 5-3 7v3c0 2-1 3-1 5l-1-4c-2-4-3-8-5-12-1-2-3-4-4-6-2-2-3-5-4-8s-1-8 0-11c0-3 1-7 2-11 0-1 0-2 1-3l1 1v1h0c0-1 1-3 1-4z" class="x"></path><path d="M579 483l2 2c0 1 0 2-1 3h-1-1c0-2 1-3 1-5z" class="AV"></path><path d="M584 465c1 1 1 2 1 4l-2 2 1 3h-2c0-1 0-1-1-2v-3-1l3-3z" class="v"></path><path d="M573 467h2c1 2 0 3 1 4h1 2 0v1c0 2-1 3-1 4h-2c-2-1-2-2-3-3v-3-3z" class="AV"></path><path d="M573 479s1 1 1 2c2 0 3 0 4-1l2-3h1l1 2h-1c0 1-1 1-1 2l-1 2c0 2-1 3-1 5h-1v-4h0l-3 2c-1 0-1 0-2-1l3-1c-1-1-2-2-3-2 1-1 1-2 1-3z" class="AO"></path><path d="M567 466c0-1 0-2 1-3l1 1v1 4c2 2 2 5 2 7l-1 2c1 1 1 1 0 2l2-2s1 0 1 1 0 2-1 3c1 0 2 1 3 2l-3 1h0l-1 1v1h-1c-1-2-2-4-2-6 0-4 1-11-1-15z" class="Aa"></path><path d="M572 485h0l-2-2v-1-1c1 0 1 0 2 1 1 0 2 1 3 2l-3 1h0z" class="AD"></path><path d="M567 466c2 4 1 11 1 15 0 2 1 4 2 6 0 3 0 3 1 5-1 1-1 1-2 0v-1c-1-2-2-2-4-3-1-3-1-8 0-11 0-3 1-7 2-11z" class="AK"></path><path d="M572 485h0c1 1 1 1 2 1h1 1c-1 2 0 3-1 4l1 1 1-1h1v1 1c0 1 0 2-1 2l1 1c1 0 2-1 3-1 0 1 0 3-1 4v2c0 1 0 2 1 3h2c-1 2-2 5-3 7v3c0 2-1 3-1 5l-1-4c-2-4-3-8-5-12-1-2-3-4-4-6-2-2-3-5-4-8 2 1 3 1 4 3v1c1 1 1 1 2 0-1-2-1-2-1-5h1v-1l1-1z" class="AM"></path><path d="M570 487h1c2 5 6 10 8 15 1 3 1 6 1 8v3c0 2-1 3-1 5l-1-4v-1c1-1 1-2 1-3-1-2 0-5-1-7h-1c0-1 0-2-1-3s-1-1-1-2c0 0-1-1-1-2-2-1-2-1-3-3v-1c-1-2-1-2-1-5z" class="AS"></path><path d="M572 485h0c1 1 1 1 2 1h1 1c-1 2 0 3-1 4l1 1 1-1h1v1 1c0 1 0 2-1 2l1 1c1 0 2-1 3-1 0 1 0 3-1 4v2c0 1 0 2 1 3h2c-1 2-2 5-3 7 0-2 0-5-1-8-2-5-6-10-8-15v-1l1-1z" class="v"></path><path d="M606 377c0 3 2 5 2 8 0 1 1 1 1 2 2 5 1 10 1 15l-1 4v1l-2 6c2 4 0 11-1 14-1 2-1 4-2 6v2l-1-1c-1 1-1 4-1 5l-1 1v1c-1 1-2 4-2 6 1 1 0 2 0 3v1l-1-2-1 7c-1 1-1 2-2 3-2 4-4 10-4 15-2 5-4 12-5 18l-3 11h-2c-1-1-1-2-1-3v-2c1-1 1-3 1-4 0-2 1-3 1-3 0-1-1-2-1-2 3-5 4-10 6-15l4-9-1-1-1-1s1-1 1-2c0-2 1-3 1-5h-3c0-1 1-1 1-2v-1c1-3 1-6 1-9h0c0-2 0-4 2-6v-2c1-1 0-2-1-3h0l2-1c0-1 1-2 1-3l-1-2h2v-3-2-4c-1 0-1-1-1-1 0-2 0-2 1-3l1 1 2 2h1v-2l1-1c0-1 0-2 1-3v-2h-1c-1 1-1 2-2 4l-1-1c0-1 0-2 1-3v-2h-1s-1-1 0-2c0-1 0-1 1-2 0 2 0 2 1 4l1-1c0-2-1-3 0-5l1-2v-1c-1-1-1-1-1-2 1-1 1-1 2 0l1-1-1-1h-4v-2c0-1 1-2 1-2l1-1c1-1 0-2 0-4l1-1h1l-2-2h0c2-1 3-1 4-2-1 0-3-2-3-3h5z" class="x"></path><path d="M602 389c1 1 2 1 3 2v2s-1 1-1 2c-2-3-2-3-2-6z" class="AX"></path><path d="M592 454c0-3 1-5 1-8v-3c1-1 0-2 0-3l1-1 1-1c0 3 1 5 0 8v3l-1 1v1c0 1 0 2-1 2l-1 1z" class="AV"></path><path d="M607 413c2 4 0 11-1 14-1 2-1 4-2 6v2l-1-1c-1 1-1 4-1 5l-1 1v1c-1 1-2 4-2 6 1 1 0 2 0 3v1l-1-2-1 7c-1 1-1 2-2 3l2-10 10-36z" class="t"></path><path d="M606 377c0 3 2 5 2 8 0 1 1 1 1 2 2 5 1 10 1 15l-1 4v-1c-2-3 0-6-1-8 0-2-1-4-1-5 1-2 1-3 1-4h-1c-1-1-2-2-2-3s1-1 1-2c-1-1-1-1-2-1l-1 2h-1l-2-2h0c2-1 3-1 4-2-1 0-3-2-3-3h5z" class="AH"></path><path d="M598 392c0-1 1-2 1-2l1-1c1-1 0-2 0-4l1-1c1 2 1 3 1 5 0 3 0 3 2 6 0 1 1 2 1 3 1 2 1 3 1 4v3 1c0 1 0 2-1 3v3h0v1h0-1c-1 1-1 3-1 4h-1v-1c0-2 0-3-1-5v-2h-1c-1 1-1 2-2 4l-1-1c0-1 0-2 1-3v-2h-1s-1-1 0-2c0-1 0-1 1-2 0 2 0 2 1 4l1-1c0-2-1-3 0-5l1-2v-1c-1-1-1-1-1-2 1-1 1-1 2 0l1-1-1-1h-4v-2z" class="AV"></path><path d="M593 455v4 3-1l1-1c0-2 0-2 1-3v-2-2l2-4-2 10c-2 4-4 10-4 15-2 5-4 12-5 18l-3 11h-2c-1-1-1-2-1-3v-2c1-1 1-3 1-4 0-2 1-3 1-3 0-1-1-2-1-2 3-5 4-10 6-15l4-9-1-1-1-1s1-1 1-2c0-2 1-3 1-5l1-2 1 1z" class="AX"></path><path d="M591 456l1-2 1 1c-1 3-1 7-2 10l-1-1-1-1s1-1 1-2c0-2 1-3 1-5z" class="AV"></path><defs><linearGradient id="AT" x1="617.77" y1="441.792" x2="592.043" y2="440.131" xlink:href="#B"><stop offset="0" stop-color="#3a322d"></stop><stop offset="1" stop-color="#5e5246"></stop></linearGradient></defs><path fill="url(#AT)" d="M615 389l1-1c1 0 1 1 3 2l2-2s1 0 2 1h0 1 3l1-2h0c0 2-1 4-1 7l-5 15h0c-1 2-3 13-3 14h0v4h0l1-1v1l-7 26-10 35h-1c0-1 1-1 1-2v-1-1-1h1v-4 1l-1 1c-1 1-1 2-2 3v1l-1 3c-2-3-2-4-3-7v-3h-2c-1 0 2 0-1 0h-1v1c-1 0-1 1-1 2l-1-1c-1 1-1 1-1 3v1 1c-1 1-1 1-1 2v1c-1 2-1 3-3 4 1-6 3-13 5-18 0-5 2-11 4-15 1-1 1-2 2-3l1-7 1 2v-1c0-1 1-2 0-3 0-2 1-5 2-6v-1l1-1c0-1 0-4 1-5l1 1v-2c1-2 1-4 2-6 1-3 3-10 1-14l2-6v-1l1-4 1-6 1-3 1-1 1-4 1 1z"></path><path d="M613 405c1 1 1 2 2 3 0 0 1 1 1 2 1 1 0 1 1 2l-1 1-3-5v-1l1 1v-1l-1-2z" class="P"></path><path d="M603 478l-1-1v-3c0-3 2-4 2-6 1-1 1-2 2-3v1c-1 4-1 8-3 12z" class="l"></path><path d="M608 452h0c1 2 0 4 0 6-1 1-1 3-2 5 0-2 0-4-1-5l-2-4 1-1 1 3 1-1h2v-2-1z" class="g"></path><path d="M591 474h0c0 1-2 6-1 6v-1c1-1 1-1 1-2l1-1c0-1 0-1 1-1l1-1c1 2 1 2 1 3h-1v-1c-2 1-2 3-3 4s-1 1-1 3v1 1c-1 1-1 1-1 2v1c-1 2-1 3-3 4 1-6 3-13 5-18z" class="w"></path><defs><linearGradient id="AU" x1="601.553" y1="457.506" x2="619.699" y2="454.535" xlink:href="#B"><stop offset="0" stop-color="#716763"></stop><stop offset="1" stop-color="#929389"></stop></linearGradient></defs><path fill="url(#AU)" d="M619 423h0v4h0l1-1v1l-7 26-10 35h-1c0-1 1-1 1-2v-1-1-1h1v-4 1l-1 1c-1 1-1 2-2 3v1l2-7c2-4 2-8 3-12 3-6 5-14 7-20l6-23z"></path><path d="M615 389l1-1c1 0 1 1 3 2l2-2s1 0 2 1h0 1 3l1-2h0c0 2-1 4-1 7l-5 15h0-1l-1 3-1 1c0-2 0-3 1-5-1-5-4-7-6-12 0-1-1-2-2-3l1-1 1-4 1 1z" class="AU"></path><path d="M614 388l1 1c0 2 0 3 1 5l4 6c1 2 1 5 1 7l-1 1c-1-5-4-7-6-12 0-1-1-2-2-3l1-1 1-4z" class="r"></path><path d="M611 396h2c-1 1-1 1-1 3v1c0 2 0 4 1 5l1 2v1l-1-1-2 1v1 1c0 1 0 1 1 2 0 1 1 2 1 2 1 2 3 4 3 6 1 1 0 5 0 6-2 2-2 2-2 5 0 1 0 3-1 5h0l-1 1-1-2v-1h-1v-3c-1-1-1-2-1-3l-2-1c-1 2-1 4-3 6 1-2 1-4 2-6 1-3 3-10 1-14l2-6v-1l1-4 1-6z" class="w"></path><path d="M609 407c0 1 1 4 0 5v4c0 1 0 4 1 5v7c1 1 2 2 2 3h-1-1c-1-1-1-2-1-3l-2-1c-1 2-1 4-3 6 1-2 1-4 2-6 1-3 3-10 1-14l2-6z" class="AU"></path><path fill="#fff" d="M580 226h14 11 11c12 1 24 0 36 0 6 0 13 1 19 0h8 19 9v4c-1 6-4 13-6 19-14 3-25 7-33 20-2 4-4 8-6 13v-2-2c1-1 1-1 1-2h1l-1-1c0 1-1 2-1 2-1 2-1 3-2 4l-1 1-20 68-1 4c-1 5-4 11-5 17-2 5-3 11-5 16h0l-1 2h-3-1 0c-1-1-2-1-2-1l-2 2c-2-1-2-2-3-2l-1 1-1-1-1 4-1 1-1 3-1 6c0-5 1-10-1-15 0-1-1-1-1-2 0-3-2-5-2-8h-5c0 1 2 3 3 3-1 1-2 1-4 2v-3h-2v-1c0-2 0-3-1-4v-2c1-2 0-4 0-6 1-1 1-1 1-2h0c2-5 0-11 3-15h1c0-1 0-1-1-2 0-1-1-1-2-2l1-2c2 1 3 1 4 3l7-26 5-22c2-6 4-12 5-18h0c2-9 0-20-4-28-8-14-22-19-37-23h-2v-2-1h2z"></path><path d="M605 350c2 6 2 12 3 17l4 24 1 1-1 1-1 3-1 6c0-5 1-10-1-15 0-1-1-1-1-2 0-3-2-5-2-8-1-3-2-7-2-10l1-2c-1-3 0-12 0-15z" class="v"></path><path d="M600 343c2 1 3 1 4 3l1 4c0 3-1 12 0 15l-1 2c0 3 1 7 2 10h-5c0 1 2 3 3 3-1 1-2 1-4 2v-3h-2v-1c0-2 0-3-1-4v-2c1-2 0-4 0-6 1-1 1-1 1-2h0c2-5 0-11 3-15h1c0-1 0-1-1-2 0-1-1-1-2-2l1-2z" class="AX"></path><path d="M600 343c2 1 3 1 4 3l1 4c0 3-1 12 0 15l-1 2-2-18c0-1 0-1-1-2 0-1-1-1-2-2l1-2z" class="AD"></path><path d="M625 350l3 3c1 1 1 3 1 4s0 1 1 2l-2 5c3 1 4 2 6 1v-2l1-1 2-6v-1-1l1-2v2c-1 5-4 11-5 17-2 5-3 11-5 16h0l-1 2h-3-1 0c-1-1-2-1-2-1l-2 2c-2-1-2-2-3-2l-1 1-1-1 11-38z" class="g"></path><defs><linearGradient id="AV" x1="640.098" y1="271.561" x2="683.489" y2="314.592" xlink:href="#B"><stop offset="0" stop-color="#211915"></stop><stop offset="1" stop-color="#63594f"></stop></linearGradient></defs><path fill="url(#AV)" d="M625 350l6-21 10-35c3-10 5-20 9-29 7-13 17-23 30-28 9-3 17-5 25-8h1l1 1c-1 6-4 13-6 19-14 3-25 7-33 20-2 4-4 8-6 13v-2-2c1-1 1-1 1-2h1l-1-1c0 1-1 2-1 2-1 2-1 3-2 4l-1 1-20 68-1 4v-2l-1 2v1 1l-2 6-1 1v2c-2 1-3 0-6-1l2-5c-1-1-1-1-1-2s0-3-1-4l-3-3z"></path><path d="M659 282c2-5 4-11 7-15 8-13 20-18 34-21l3-8 1-4v-1c1-1 1-1 1-2h0l1-2 1 1c-1 6-4 13-6 19-14 3-25 7-33 20-2 4-4 8-6 13v-2-2c1-1 1-1 1-2h1l-1-1c0 1-1 2-1 2-1 2-1 3-2 4l-1 1z" class="w"></path><path d="M332 135h1c2 13 4 26 7 39-1 1-1 3-1 5 1 1 1 3 1 4 1 0 2 1 3 1v-1l2 4v2c0 8 0 17 2 24l-1 1h0v3c1 1 1 2 1 3v2h0c0 1 0 2 1 3h0-11c-1 1-6 1-7 0v1h-7c7 1 14 0 21 0h36 17 1 15c1 0 3-1 4-1 2 1 3 1 5 0l-3-3 3 1c0-1-3-2-4-4-2-1-3-3-4-5l-2-3c-1-2-1-4-3-6v-3c0-1-1-2-1-3l-1-3c0-1-1-2-1-3 3 0 3 2 5 3 0 1 0 1 1 1h1c1 1 3 2 4 3 0 1-1 1 1 2 2 0 3 3 6 4 1 1 1 2 2 3l2 2c1 0 1 0 1-1v1c2 2 5 3 7 5h1l4 2 4 2c2 1 5 3 8 4 3 0 6 0 9 1h5 3c0 2-2 22-3 24l-1 1c-5-1-10-1-15 2-1 0-2 0-3-1l-4 3c-7 7-9 18-9 27l1 14c1 3 1 6 2 8l1 3 9 27 1 4 10 34c0 3-1 5 0 8 0 3 0 6 1 9v1l1 5 1 2v2c0 1 0 2 1 3v4c1 1 1 1 1 2 0 2 0 5 1 7l1 5v3c1 1 1 2 0 3 0 1 0 2 1 3h0v2l1 1v2 1 1c1 0 1 1 1 2v1c0 1 0 1 1 2v2c0 1 0 1 1 2v3c0 2 0 2 2 3v2c1 3 1 7 1 10 0 1 1 3 0 4s-1 1-1 2c1 3 2 6 3 10 2 7 4 15 7 23l20 68 5 18 3 12 1 4c1 2 2 4 2 6l1 4h0l1 4c-1 2-1 5-1 7l1 1v5l1-3h3c-1 3-1 4 0 7h1c1-1 1-2 1-3l1-1v-3c0-2 1-3 2-5l2-1v-1l4-2 2-2 3-1 1-2c1 2 1 3 1 4h1l1 1v3h0 1 1l2-2v-2h1c0-3 2-5 4-6v-1h2c0-2 1-4 2-5v-1c0-1 0-2 1-3 0-1 0-3 1-4 0-3 0-5 1-8v-1c1-1 1-2 2-2l1 1 2-5 5-18c0-1 1-3 1-5l4-13 8-28c0-2 1-3 1-5v-3c1-2 2-5 3-7l3-11c2-1 2-2 3-4v-1c0-1 0-1 1-2v-1-1c0-2 0-2 1-3l1 1c0-1 0-2 1-2v-1h1 1 2v3c1 3 1 4 3 7l-1 2-9 34c0 3 0 4-1 6s-2 5-2 7l-1 6-1 3 2-1-3 11h1v-1h1v-1 1l1 1c0 2 1 2 3 3h2 1 2c0-1 0-1 1-2 1 0 2-1 3-2h1v-1c1 0 2-1 2-1 2-1 2-1 3-2h-1l2-3c3-3 5-5 7-9l1-4 1-3 2 1 1 2 1-2 1 3c0-2 0-4 1-5l1 2c1 2 2 3 3 5 2 1 4 2 5 3v2h1c1 1 1 3 2 4v1c-1 2-2 5-1 7l2 3-2 1c2 4 4 7 7 10 4 3 8 6 11 10h0c2 4 6 6 10 8-1 1-2 1-2 2 3 1 6 3 9 4s6 2 10 4h-4l15 5c3 1 6 2 9 2 3 1 7 1 10 2 1 0 3 0 4 1h2l2 1v-2 1c1 1 1 1 2 1-1 2-1 3-1 4 0 2-1 4-1 6-1 8-2 17-7 23h2 0c2 2 2 2 3 4v1l1 15h0v5 4c0 2 0 4 1 6h0l2 17c1-1 2-1 3-2l1 3 1 4v1c0 2 1 2 0 4h-1l-1 1 1 1h-1l-1 1c4 8 8 16 13 24l4 5c2 2 5 6 7 7l5 5c4 1 12 5 15 4h1 4l-3 3h-8c-8-1-15-3-21-7-1-1-2-1-2-1-1-1-2-1-3-2s-3-1-4-2l-9-6c-1-1-3-2-4-3l-7-3-1-1c-7-4-14-6-21-7l-1-1c-3 0-3 0-5 2-5 4-7 9-8 15 0 1 0 2-1 3s-4 2-6 3c-7 5-19 15-19 24 0 3 1 6 3 9s5 4 9 5h0c6 1 12-3 17-6l4-3c-4 8-9 14-17 18-8 2-15 2-22-1-3-1-5-2-8-2-8-1-19 6-22 14-2 3-2 6-3 9-1 4 2 11 4 14 3 3 6 5 10 5 8 2 16-3 23-7-5 6-9 10-15 14-15 9-32 11-47 18-12 5-23 12-31 23-5 9-10 20-7 31l1 3h0c1 3 2 5 4 8-1 1-2 1-3 2h0c-12 4-21 14-26 25-2 6-4 12-5 18-3-15-10-26-20-36-5-4-10-6-15-9 5-9 6-16 4-27l-2-4c-3-9-9-16-17-22-15-12-33-17-51-23-7-3-14-5-20-9-4-3-8-7-11-12 1 1 3 2 4 2 3 2 5 3 8 4 5 2 11 2 16 0 4-3 7-6 8-11 2-5 1-12-2-17-5-9-14-15-24-13l-9 3c-5 2-12 2-17 1-10-3-14-11-19-19 4 4 9 8 15 9h6c3-1 5-2 7-4 2-1 3-3 4-6 2-5-1-10-3-14-4-5-10-9-15-13-1-1-7-4-7-5-1-2-1-5-2-7-1-4-2-7-4-11v-1c-5 0-9 2-14 3-13 4-24 12-36 20l-2-2v1c-6 3-12 7-20 9-2 1-5 1-7 2-2-1-3 0-4-2h-2-3c3-2 6-3 10-5 7-5 12-12 17-20 1 0 2-1 3-2l1 1v1l8-11c1 0 1 1 2 1v-1 1c2-3 3-6 4-9h2c1-2 1-6 2-9 1-8 3-16 4-24 0-9-1-17 0-25 0-3 0-6 1-9h0c-1-1-1-2-1-3l1-1v-1c0-1-1-1-1-2v-3c1 0 1 0 1 1 0 2 1 3 2 5l1-1-5-12c0-4-1-7-1-10l-1-3h0v-2l-3-4 1-1-1-1v-1c0-2-4-6-5-8l1-1c0-2-2-5-3-7-5-8-10-15-13-24h1v-2c0-1 0-1-1-3-1-1-1-2-1-3-1-1-1-1-1-2l-1-1 1-2-5-13c-1-2-2-3-2-4v-1c-1-2 0 1-1-1v-1-1c0-1 0-2-1-3h-1c-1-1-2-2-2-3 0-2-1-4-2-6 0-1-1-2-1-3l-1 1c0-1-1-2-1-3l-1-2v-1c-1-1 0-1-1-2v-2c-1-1-1-1-1-2l-1 1h0l-3-8v6c-1-2-2-6-3-7-1 0-2 0-3 1 0-2-1-3-1-4-1-4-2-9-2-13v-33l-2-44v-5c-1-6 0-13 0-19 1-25 2-50 7-75-1 1-1 2-2 2-1 1-1 2-1 3h-1c-1-1-1-2-1-4 1-1 1-3 2-5v-2l2-9c1-4 2-7 2-11v-6l1-4-2-1c0-1-1-2 0-3v-5-1-2-3c1-1 1-3 1-4l-1-1v-3c1-1 2-2 2-3 0-2-2-4-3-6l-1-3-7-9v-1l5 3v-1-5c1 4 3 6 6 9l8 5c1 0 1 0 2-1 0-2 1-3 2-5h0c0 1 1 2 2 3 2 1 5 3 7 5 0-2-1-4-2-5 1-2 1-4 1-5l-1 3c-1-1-1-2-1-2l1-2-1-1-2 2h0l-1-1 2-2c3-5 4-10 7-14h0l1-1 2-2c1-4 3-9 6-11 1-1 1-2 1-3l4-6c4-9 9-18 15-26 1-3 3-6 6-9 4-5 8-10 13-14h1z" class="AJ"></path><path d="M625 711l3-1 2 1c-2 1-3 2-5 3v-1c1 0 0-1 0-2z" class="D"></path><path d="M449 636h2c1 2 1 2 1 3l-2 2c-1 0-1-1-2-1l1-4zm139-62c-2-1-3-1-4-1h-1c0-2-1-2 0-3 2 0 3 1 5 2v2z" class="AC"></path><path d="M354 614c0 2 0 2 1 3l-4 4c0-1 0-1-1-2 0-1 1-2 2-2 1-1 1-2 2-3z" class="X"></path><path d="M481 721c2 1 3 2 5 2l1-1h1l2 3v1c-3-1-4-1-6-2-1-1-2-1-3-1v-2z" class="c"></path><path d="M436 768c1 0 1 0 2-1h1 1v1c1 0 2 1 2 1l-1 1h0v1h-2v1c-2-1-2-3-3-4z" class="Z"></path><path d="M267 487v-3-2c0-2 0-3 1-4l2 13-2-1v-2l-1-1h0z" class="E"></path><path d="M417 572c2 0 3 1 4 2l-5 2-3-3 4-1z" class="AI"></path><path d="M682 682l2-2h0c1 1 1 1 2 1h0 2l1 1c-3 2-6 2-9 3 0-2 1-2 2-3z" class="L"></path><path d="M554 815h1c0 5-3 4-5 6-1 1-1 2-2 2h-1c1-2 3-3 4-5 0-1 1-2 3-3z" class="C"></path><path d="M415 598v-1c1-1 2-1 3-1l3 6v1 1h-1c-1-2-3-4-5-6z" class="G"></path><path d="M499 827l-3-6-3-7c2 2 3 4 4 6 2 2 3 5 3 7h-1z" class="U"></path><path d="M458 720l1-1v1c0 1-1 2-1 2h1l2 3h-1l-1-1v-1c-2 1-2 1-2 3h-2l-1-1c-1 0-2 1-3 1v1-1c0-2 6-4 7-6z" class="o"></path><path d="M350 619c1 1 1 1 1 2-1 1-2 3-3 5l-1-1c0-1 0-1-1-2l-1-1v-1h1l1-1c1 0 2 0 3-1z" class="n"></path><path d="M700 662l-2-1c1-2 4-4 6-5 0 1 0 1 1 1 0 2 0 4-1 5l-1-1c0-1-1-1-2-1-1 1-1 1-1 2z" class="C"></path><path d="M692 672c1 2 1 4 2 6l-2 1c-1 1-1 2-1 2h-1c1-1 1-2 0-3 0-1-1-3-1-4 1-1 2-1 2-2h1z" class="AC"></path><path d="M296 452c0-2 0-3 1-5 1-1 2-2 2-3 1-2 0-3 2-5h2v1l-1 1c-2 3-2 7-5 10l-1 1z" class="C"></path><path d="M629 779c1 0 2 1 3 1h1l1 1 1 1 7 3c-1 1-1 1-2 1-2 0-9-3-11-5h1l-1-1v-1z" class="U"></path><path d="M423 570c2 0 4 1 5 2l-7 2c-1-1-2-2-4-2l2-1c2 0 3-1 4-1z" class="AB"></path><path d="M438 567h10l-12 3h0c-1-1-1-2-2-3h4z" class="a"></path><path d="M563 720v-1c1-2 3-3 5-4v1 2c1 1 4 0 6 1h-1-2l-1 1 2 1v1c-1 0-2-1-3-1l-2-1c-1 0-2 0-2 1v1c-1 0-1-1-2-2z" class="n"></path><path d="M589 738l2 3-1 1v1l-5 4c-1 0-1 0-1-1l5-8z" class="K"></path><path d="M529 816c1 0 1 0 2 1-1 3-1 7-3 10-1-1-1-1-1-2h0c0-2-1-5 0-7 1-1 2-1 2-2z" class="U"></path><path d="M615 715l10-4c0 1 1 2 0 2v1c-3 2-6 3-9 4l-1-1v-2z" class="B"></path><path d="M311 466v-5-12l1 1c1 2 1 7 1 9-1 3-1 7-1 11h-1v-4z" class="AU"></path><path d="M336 624c2-2 4-4 6-5h0c-1 2-2 2-1 4l1 3h0l-2 2h-2v-2l-2-2z" class="R"></path><path d="M406 705h1l1 1h0l1-1 9 6c0 1 2 2 2 3-5-3-10-5-14-9z" class="AK"></path><path d="M284 386c0-1-1-2-1-2 1-2 1-4 1-6 0-3 1-5 1-8 0 0 0-2 1-2 0-1 1-1 1-1v6c-1 4-2 8-3 11v2z" class="C"></path><path d="M310 345h1v8l-1 13c0-2 0-3-1-5v-4l1-12z" class="H"></path><path d="M472 745h2l-1-1 1-1 4 2h-1v1c-2 1-3 2-5 2l-5 1h-3c-1 0-2 0-2-1v-1l1 1c1 0 6-1 8-1l1-2z" class="X"></path><path d="M297 283c-1-3-2-5-2-9h1c2 4 5 8 7 12h-1c-1-1-2-3-3-4 0 1 0 2 1 3v3l-3-5z" class="B"></path><path d="M596 739v1c-1 0-2 1-3 3 2-1 2-2 4-3v1l-11 10v-1c0-1 2-2 3-3l-1-1-2 2-1-1 5-4 6-4z" class="G"></path><path d="M427 568l7-1c1 1 1 2 2 3h0l-8 2c-1-1-3-2-5-2l4-2zm20 153l6-1 1 1c-4 1-10 3-14 3-2-1-4-1-6-2l3-1h10z" class="D"></path><path d="M306 312l2 3c3 7 4 12 2 20v-1l-4-22z" class="n"></path><path d="M406 594c4 0 7-1 10 0h1l1 1v1c-1 0-2 0-3 1v1c-4-2-7-3-11-3 1-1 1-1 2-1z" class="m"></path><path d="M422 723v-2c0 1 1 1 2 1h0 7 3c2 1 4 1 6 2-6 1-13 1-18-1z" class="f"></path><path d="M451 715c2 0 2 1 3 1l2 1 1 1h3 0l-1 1-1 1h-2l-1 1h-1l-1-1-6 1h1c1-1 1-2 3-1 1-1 1-1 2-1s2 0 2-1h-2 0c-2 0-3 0-4-1h1c1-1 1-1 1-2z" class="n"></path><path d="M313 570l1-2c1 1 1 2 1 4h0v-5l1 2v1 1 2 3c0 1-1 3-2 4h-1 0c-1-2-2-3-3-4 1 0 2 0 2-1 1-1 1-3 1-5z" class="U"></path><path d="M476 721c1 1 4 1 5 2 1 0 2 0 3 1h-1-3 0l1 2c0 1-1-1 0 1v1c0 2 1 4 1 5 1 3 1 5 2 7l-2 3 1-3c0-1 0-2-1-3l-2-2c0-1 0-2 1-3v-1c-1-1-2-2-2-3h1c0-2-1-3-1-5-1 0-1 0-2-1h-2l1-1z" class="C"></path><path d="M426 717l11 4-3 1h-3c-4-1-6-2-9-3v-1c2 0 2 0 4-1z" class="a"></path><path d="M391 413c2 3 8 7 9 10l-1 2-8-7v-5z" class="X"></path><path d="M654 742c1-1 2-1 3-2h1v3c-1 2-2 2-3 3l-1 1-2 1v1h-4c1 0 1-1 2-1 0-3 4-3 4-6z" class="U"></path><path d="M572 721c7 1 12 1 18 0 2 2 3 2 5 2-8 1-15 1-23-1v-1z" class="H"></path><path d="M444 631h-1c1-2 2-4 4-5-1 1-1 2 0 3l-1 1c0 1 1 2 2 3v1 3c0-2 0-2 2-4h1l1-2h1l1 1-1 1-1-1h0l-3 4-1 4v6c-1-1-1-2-1-3 0-3 0-10-1-12h-2z" class="C"></path><path d="M470 782h2 0v1l2 3v-1c0-1-1-2-1-2l1-1c1 1 4 5 4 6l1 5c0 1 0 1-1 2h0l-8-13z" class="H"></path><path d="M710 721h1l2 2c1 1 3 3 2 4v1 1h0l-2-1c-3-3-6-3-8-6h3l1 1c0-1 1-2 1-2z" class="W"></path><path d="M442 711c0-1 1-2 0-4h0 1c0 1 1 2 2 4 0 1 1 2 2 3s2 2 2 3c1 1 2 1 4 1-2 0-3 0-4 1-2 1-4 0-6 0h-2c2 0 5 0 6-1v-1h-1c-2-1-2-2-3-3v-1l-1-2z" class="X"></path><path d="M342 385l3-3h1c-2 2-4 4-5 7v3c0 1 0 3-1 4v-2c-1-3-4-5-6-7h0v-1-1l3 3c2-1 1 0 2-1l1-1 2-1z" class="s"></path><path d="M605 766c1 1 0 2 0 4l-6 6-3 4c-1-1-2 0-4 0v-1c2 0 3-3 4-4l9-9z" class="AK"></path><path d="M699 714l8 4 3 3s-1 1-1 2l-1-1h-3l-6-3v-1c1-1 0-2 0-4z" class="L"></path><path d="M305 475h1c1 4-3 5-4 7-1 1-1 2-2 2h-1s-1 0-2 1c0-1 0-2-1-3 1-1 2-2 4-3s3-2 5-4z" class="Y"></path><path d="M644 709c3-3 6-4 10-6l10-6c-1 1-2 1-2 2-1 2-5 4-6 6l-9 3-3 1z" class="F"></path><path d="M507 877l1-1-1-2c1-1 1-1 2-1 0 4 2 8 3 12 1 3 1 5 2 8l-1 1c-1-1-1-2-1-3s0-2-1-3-1-3-1-4c-1-1-2-3-2-4l-1-3z" class="g"></path><path d="M325 410v4c0 2 0 3 1 5-1 1-1 2-1 3-1 1-2 3-4 4 0-3 1-6 0-9 0-2 0-3-1-5l2 2v1l1 1 2-6z" class="X"></path><path d="M592 568l3-2c3 1 8 1 10 3 2 0 4 1 7 2h-1c-1 0-2 0-4-1l-1 1h0l1 1-15-4z" class="D"></path><path d="M530 809h1l1 1c-1 2 0 3-2 5h0c-2 1-2 1-3 1-1 1-2 4-3 5 0 1 0 1-1 1v-3c0-2 1-3 3-4v-3c1-1 2-2 4-3z" class="C"></path><path d="M285 409c0 2 0 5-1 7v4h1v-2h1l2 4c-2 3-3 7-5 10v-7c1-4-1-8 1-13l1-3z" class="U"></path><path d="M505 864c1 1 2 3 3 5v2c1-1 1-1 2-1h0c0 2 0 2-1 3-1 0-1 0-2 1l1 2-1 1c-1-2-1-3-2-5h-1c-1 0-2-3-3-4l1-2c1 1 2 2 2 3 1-3 1-3 0-5h1z" class="b"></path><path d="M314 580l5 3c0-1 1-2 0-3v-4 3 1h-1l-1-2c-1 0-1-1-1-2h2v-5c-1-1-1-2-1-4h-1c1-3 0-5 1-7 2 4 2 9 2 13s1 8 1 11c-3-1-5-2-7-4h1z" class="C"></path><path d="M386 638c1 1 1 2 2 4 0 0 1 0 1 2h-1v1l-1 1-1 1-1 1c-1-1-1-2-1-3l-1-1h-1v2h1v1 1l-2-1c0-2 0-4 1-6l1 2h0c1-2 0-3 2-5h1 0z" class="S"></path><path d="M386 638c1 1 1 2 2 4 0 0 1 0 1 2h-1v1l-1 1-1 1c-1-3 0-6 0-9h0z" class="o"></path><path d="M590 721l12-2c3 1 7-1 9 1-5 2-11 3-16 3-2 0-3 0-5-2z" class="B"></path><path d="M429 734c-2 0-5-4-7-5v-1l1-1c2 1 9 3 10 4 0 1 0 0-1 2h1l1-1h3c-1 2-2 2-2 4h-1 0c-2-1-3-2-4-3l-1 1z" class="AC"></path><path d="M328 432h1c1 1 1 2 2 2h0c-3 2-7 4-10 5-4 2-8 5-12 8 1-2 16-13 19-15z" class="AQ"></path><path d="M399 592c1-3 4-4 7-5 2-1 4-2 6-2v-1l9-3c-2 3-4 4-7 4l-3 2h0c-2 1-5 2-7 3s-2 2-4 2h-1z" class="C"></path><path d="M295 469h1c2-2 2-5 3-7 2-2 4-5 6-7-2 4-3 9-5 13-1 2-3 6-4 7l-2-1h1v-1-4z" class="U"></path><path d="M382 746l-3-3c-1 0-1 0-2-1h-1c-1 0-2-1-3-1-3-2-7-3-9-7l3-3v1 1c1 0 1 1 2 2l2-1v1c1 4 9 7 11 11z" class="C"></path><path d="M567 822h0l3-3c1-2 2-1 3-3l2-2h1l1 2-1 1-1-1c-1 0-1 0-1 1h1 1 0l1-1h1v-1-1h1v2 1c1-1 1-1 1-2l-1-1 1-1v-2h0l1-1v2c-1 2 0 6-1 7 0 1-1 1-1 3-1 0-2-1-2-1 0-2 0-2 1-3h-1l-1 1-1-1c-2 0-2 1-4 2v1c-1 1-2 2-2 3-1 0-1 0-1-1s0 0-1-1z" class="Y"></path><path d="M708 667v3c3 3 2 7 5 11v5 1c0 1 1 1 0 3-1-1-1-3-2-4v-1c0-2-1-4-3-6-1-2-1-6-1-9 0-1 0-2 1-3z" class="R"></path><path d="M708 667v3h0c0 1 0 2 1 3v5l-1 1c-1-2-1-6-1-9 0-1 0-2 1-3z" class="W"></path><path d="M461 671l3-20v1 24c-1 0-2 1-3 1v-6z" class="E"></path><path d="M343 622l3-5c1-1 1-1 2-1-1 1-1 3-1 4l-1 1h-1v1l1 1c1 1 1 1 1 2l1 1-1 4v3l-1-2c-2-1-3-2-4-5h0l1-1-1-1c1 0 1-1 1-2z" class="R"></path><path d="M343 622l2 3v3l2 2v3l-1-2c-2-1-3-2-4-5h0l1-1-1-1c1 0 1-1 1-2z" class="C"></path><path d="M336 624l2 2v2h2l2-2c1 3 2 4 4 5l1 2-1 2h-1l1 2h-1c-1-1-2-1-3-1 0-3 0-3 2-5l-1-1h0c-2 1-3 1-4 1h-1v-2l-1-1h-3-1l3-4z" class="W"></path><path d="M599 726c4-1 7-1 10-3 2-2 4-1 6-2s3-2 5-2l1-1h0c2 0 4 0 6-1h1c-3 1-5 2-7 3-7 2-12 5-17 9 0-1 1-1 1-1l-1-1h0c-2 1-3 0-5-1z" class="c"></path><path d="M705 657l1-3h1c1 3 2 10 1 13-1 1-1 2-1 3l-1-5-1-1v-1c-1 0-1 0-2 1l-1-1c-1 1-2 2-2 3l-3-1 3-3c0-1 0-1 1-2 1 0 2 0 2 1l1 1c1-1 1-3 1-5z" class="R"></path><path d="M309 397c-5-1-10-2-15-1l5-7c1 1 2 1 3 2s0 2 2 3h1c2 0 3 1 5 1 1 1 3 1 5 2h-6z" class="m"></path><path d="M305 446c0-2 0-4 1-6l1-1c0-2 0-3 1-4s0-6 0-7v-1-1h0v-1c1 1 1 2 2 3v1c1 1 1 1 1 2v1 4c0 1 0 2-1 3l-5 7z" class="C"></path><path d="M604 729c5-4 10-7 17-9-3 3-6 5-9 7-4 2-8 6-12 8 1-3 2-5 4-6z" class="Q"></path><path d="M563 699l1 2c-1 2-3 4-4 6h0 1 0c0 2 0 3-1 5h0-1c-2 2-2 6-4 8l-1-1h0c-1-4 3-10 5-13h1l-1-1-2 1 6-7z" class="n"></path><path d="M439 599c2-1 2-1 3-3v-3l1-1c1 1 2 2 2 3l2 3c1 3 1 5 1 9-1-1-2-2-2-3v-1c-2-2-2-2-4-2l-1-1c-1 1-1 2-2 2v-3z" class="X"></path><path d="M324 458c1-5 2-10 4-14l1 13 1 3h0-1c-1 1-1 1-2 1h-1c0-1-1-1-2-1l1-2h-1z" class="E"></path><path d="M318 589c3 1 5 2 8 2 4 1 9 1 13 1-2 1-4 1-7 1h-3c-1 0-1 1-1 1l-2 2c-1 0-1-1-1-1-2 0-2 1-3 1-1-1-1-1-2-1-2 0-2 0-4-1h4v-2c-1-1-1-2-2-2l-1-1h1z" class="a"></path><path d="M682 682v-2l2-3c1-2 4-5 7-6l1 1h-1c0 1-1 1-2 2 0 1 1 3 1 4 1 1 1 2 0 3 0 1-1 1-1 1l-1-1h-2 0c-1 0-1 0-2-1h0l-2 2z" class="W"></path><path d="M325 185c3 2 5 4 8 5 3 4 5 8 8 13 0 1 1 3 1 4v1c-4-8-9-17-18-20h-2l3-3z" class="k"></path><path d="M323 621v-1c2-1 2-2 3-4v-1c2-1 3 0 5-1h1l1 1c0 3-2 5-3 7-1 0-2-1-3 0l1 2-5-3h0z" class="C"></path><path d="M429 734l1-1c1 1 2 2 4 3h0 1c0-2 1-2 2-4v1c2 7 7 13 10 19l-18-18z" class="H"></path><path d="M375 627l1-1c3 1 7 1 10 3-1 1-1 3-1 4v-4h-2-1 0l-1 1v4h0l1-3h0c1 0 1 0 2 1l-2 2 1 1v1l-1 1c-1 0-1 0-2-1v-1h0l-1 2h0v1h-1c-1-3-2-7-3-11z" class="c"></path><defs><linearGradient id="AW" x1="379.032" y1="408.881" x2="369.468" y2="411.619" xlink:href="#B"><stop offset="0" stop-color="#cf2c2e"></stop><stop offset="1" stop-color="#f03a40"></stop></linearGradient></defs><path fill="url(#AW)" d="M367 401c4 4 8 9 12 12l5 5-2 2c-6-5-11-10-15-16v-3z"></path><path d="M612 727h2c0 1 0 1-1 2l-16 12v-1c-2 1-2 2-4 3 1-2 2-3 3-3v-1h0c1-2 2-3 4-4 4-2 8-6 12-8z" class="D"></path><path d="M407 611c3 2 3 3 5 6 2 1 3 2 5 4l1 1c-1 1-1 1-1 2l-1-2-1 2c-1 0-1-1-2-1-1 1 0 2-2 2 0-2-1-2-2-3-1-2-1-3-1-4l1-1c-1-2-1-2-2-3v-3z" class="K"></path><path d="M409 617v-1c2 2 1 3 3 6v-3h0c1 2 3 1 4 3l-1 2c-1 0-1-1-2-1-1 1 0 2-2 2 0-2-1-2-2-3-1-2-1-3-1-4l1-1z" class="L"></path><path d="M558 729c0-2 1-5 2-7 1 0 1 0 1-1 0-3 2-5 4-7h0v-2h0c1 0 1-1 2-2h0c1-2 2-4 4-4h1l-2 2c0 1 0 3-1 4s-1 1-1 2v1c-2 1-4 2-5 4v1h0l1 3v2l-1 2c0 1 0 2-1 3l-1 1c0-2 0-1 1-2v-1c0-1 0-2 1-2v-3c0-1-1-1-1-1h0v2c-1 1 0 2-1 2-1 1-1 2-1 3h-2z" class="X"></path><path d="M453 753h0c0-1-1-2-1-3v-1l-2-3c-1-1-1-1-1-2l5 3 2 2c3 1 7 3 11 2l6-2h2c-1 1-3 1-4 2-3 1-6 2-9 2l1 1v1c-2 0-4-1-5-1l-1-1h-1c0 1 0 1-1 2l-1-1-1-1z" class="I"></path><path d="M520 911l1-2c-1-4-1-8-1-12-1-3-1-5 0-8 0 3 0 6 1 9h0l1-1s0 3 1 4v9l-1 1v4c0 1 0 2 1 3v1 2h1l1 4c0 1 1 2 2 4h-1c-2-2-2-4-3-6-1-1-1-2-1-3h0c0-1-1-2-1-3v-1l-1-2v-3z" class="d"></path><path d="M386 629h0c1 1 2 1 3 1l1-1c2 2 1 12 1 15h-1l-1-1c0-1 0-1 1-2v-1l-2 2c-1-2-1-3-2-4l-2-1v-1c0-1 0-1 1-2v-1c0-1 0-3 1-4z" class="I"></path><path d="M392 705c1 0 2 0 4 1v1c0-1 0-1 1-1l1-1h0l-2-1v-1c5 4 10 6 16 8l1 1v1c-1 0-3-1-3-1h-1-3-1l1 1h0c-4-1-8-3-10-6h-2l-2-2z" class="M"></path><path d="M396 707c4 2 9 3 14 5h-1-3-1l1 1h0c-4-1-8-3-10-6z" class="G"></path><path d="M602 600h1c1 0 1 0 1 1l3-3v1c-1 1-1 2-2 4v1l1 1c0 2-1 4-1 6s-1 3-1 5v2l-4 5-1-1v-2h0c-1 0-2 1-2 1-2 0-3 1-4 2l-1-2 2-1 3-1h1c0-1 0-2 1-3 0 1 0 3 1 4l1-1c1 0 1-1 1-2 1-6 1-11 0-17z" class="T"></path><path d="M412 711l14 6c-2 1-2 1-4 1v1c-6-1-11-3-16-6h0l-1-1h1 3 1s2 1 3 1v-1l-1-1z" class="B"></path><path d="M400 741c2 1 4 1 5 3-1 0-1 1-1 2-2 2-2 3-2 5v-1c-1 0-1-1-2-2 0 0 0-1-1-1h-1l-1-2c-2-2-4-4-4-6-1-1-1 0 0-1 1 0 3 0 4 1l1 1 2 1z" class="U"></path><path d="M398 740l2 1-1 2h0c-1-1-1-2-1-3z" class="Z"></path><path d="M699 719c-2-1-5-1-7-2-1-1 0-1-1-1s-2 0-2-1l-2-1h-2c-1 0-3 0-4-2v-1c1-1 2-1 3-1s1 0 2 1h1l1 1c0 1 1 1 1 1 1 1 0 1 1 2v1-3l2-1c2 0 2 0 3 1l1 1h3c0 2 1 3 0 4v1z" class="T"></path><path d="M442 701c1 0 2 1 2 2 1 1 1 2 2 2l3 3c1 2 3 3 4 5 2 1 2 2 3 4l-2-1c-1 0-1-1-3-1 0 1 0 1-1 2h-1c0-1-1-2-2-3s-2-2-2-3c-1-4-3-7-3-10z" class="L"></path><path d="M447 714h1l1-1h0c1 1 2 1 2 2s0 1-1 2h-1c0-1-1-2-2-3z" class="R"></path><path d="M518 896c1 5 1 10 2 15v3l1 2v1c0 1 1 2 1 3l-1-1-2-2c-1-2-1-5-2-7 0 0-1 0-2-1 0-1 0-5-1-7 2-2 2-3 2-6h2z" class="U"></path><path d="M293 430l1-3v-1h0c0-2 0-3 1-5 0-2 1-4 2-6 3 3 2 7 2 10 0 1-1 2-1 2 0 2 1 4 0 6 0 1 0 1-1 2l1 1h-1l-1-1-1 1s0-1-1-1l2-1-2-4h0-1z" class="C"></path><path d="M311 530c-1-3-2-5-2-8v-2l-1-1c0-2-1-4 1-7 0 2 0 2 1 4v1h0c0 1 0 2 1 3v1 2c0 1 0 1 1 2v1c0 1 0 2 1 3 0 2-1 5 0 8v1s0 1 1 2v3c0 2 0 0 1 2v2 1 1s1 1 1 2h-1c-1-1-2-3-2-4v-1c-1-1-1-2-1-3s0-2-1-4v-4h0 1c-1-2-1-3-1-5z" class="Z"></path><path d="M451 727v-1c1 0 2-1 3-1l1 1h2v4h1c0 1 1 2 1 3s1 2 1 3c1 3 4 6 5 9-1-1-3-2-4-3 0-1 0-1-1-1v1l-2-2c-2-5-5-9-7-13z" class="X"></path><path d="M295 507l1-1 2 3c0 1 0 2 1 3 0 1 1 1 2 2v-1l3 1v1l-2 3v1 1h0 2l2 4-2 1v1l-1-1h-2 0c-1-1-1-1-1-2l1-1-1-2v-1l-1-1-1-1s-1-1-1-2h0c-1-1-1-2-2-3v-1c0-1-1-1-1-1 0-2 0-2 1-3z" class="d"></path><path d="M295 507l1-1 2 3c0 1 0 2 1 3l-2 2c-1-2-2-5-2-7z" class="b"></path><path d="M299 512c0 1 1 1 2 2v-1l3 1v1l-2 3v1h-1c-2-1-3-3-4-5l2-2z" class="l"></path><path d="M301 514v-1l3 1v1l-2 3-2-2c0-1 0-1 1-2z" class="g"></path><path d="M430 586c1 0 2 0 3 1l-11 14-1 2v-1l-3-6v-1h3c2-2 4-4 6-5 0-1 3-4 3-4z" class="L"></path><path d="M421 595c2-2 4-4 6-5 0 0 0 1-1 2l-5 6v-2-1z" class="Q"></path><path d="M428 607l4-8c1 0 3 2 4 3 2-2 1-11 1-14l2 4c1 2 1 4 0 7v3l-2 1v3c-1 1-2 1-3 2h0-4 0l-2-1z" class="H"></path><path d="M537 909h1v3h1v-1h0c0 2 0 2 2 3l1-1v-1l1 1v-1h1 0c1 3 2 5 4 8-1 1-2 1-3 2l1-2c0-2-1-3-1-4l-3 1-1 1c-1 1-4 0-6 0h-1c0-1-1-3-1-4l-1-1c1-1 3-2 4-3l1-1z" class="N"></path><path d="M537 909h1v3 2l-1 1v1c-2-1-2-2-4-3v1l-1-1c1-1 3-2 4-3l1-1z" class="Y"></path><path d="M567 822c1 1 1 0 1 1s0 1 1 1l1 2h-1-2c-1 1-2 2-2 3h-2v3 1c-1 1-1 1-2 1s-2 0-2 1c-1 0-1 0-1 1v2l-1 1-1 1 1 1c0 2-1 3-2 4h1-3l-2-2 1-1c0-1 0-2 1-3h0c2-1 2-2 3-3 3-5 7-10 11-14z" class="d"></path><path d="M367 666c-6-9-12-17-16-27l19 26v2c1 1 2 1 2 3h-1l-3-4h-1z" class="N"></path><path d="M585 783c1 0 1 0 2-1 1 1 0 1 1 1 1-1 2-3 4-4v1c-6 7-13 14-21 21 0 0-2 2-3 2v-1c1 0 1-1 2-2 1 0 2-1 2-2 1 0 2-1 3-1 0-1 1-1 2-2h-1-1c-1 1-2 2-4 3l-2 2c-1 0 0 0-1 1h-3c2-1 3-2 5-3h1c1-1 2-1 3-2 0-1 1-1 2-2 1 0 0 0 1-1h0c2-1 2-1 3-2v-1l-1 1c-1-1-1-1-1-2 2-2 5-5 7-6z" class="d"></path><path d="M515 865h1c1 4 1 8 1 13 0 3-1 6 1 8v10h-2c0 3 0 4-2 6l-1 1v-9l1-1h1v-4h0c1-2 1-3 1-4-1-4-1-9-1-13v-5c-1-1-1-1 0-2z" class="N"></path><path d="M341 584c-2-4 0-11-1-15-1-1-1-1-1-2 1-1 1-5 1-7 1-1 0-2 0-3 1-1 1-1 1-2 1-2 0-7 0-9h0c1-1 1-1 1-2v-1-1h1v-1-1c0-1 0-2 1-2v-1-2c1-1 1 0 1-1v-2c1-1 1-3 2-5h1l-1 3c0 1-1 3-1 5 0 1 0 2-1 3v1h0 1v-2c0 3-1 6-2 8 0 2 0 3-1 5s-2 8-2 11c-1 7 1 16 0 23z" class="R"></path><path d="M323 555l13 38c-2-2-4-4-6-7v-1c-1-2-5-10-4-12 1-1-1-4-1-6-1-4-2-7-2-12z" class="AU"></path><path d="M605 766c1-1 3-4 4-5 6-7 12-13 19-20l1 1c-5 6-11 12-16 19-2 2-5 7-8 9 0-2 1-3 0-4z" class="l"></path><path d="M558 838v-2c0-1 0-1 1-1 0-1 1-1 2-1s1 0 2-1v-1-3h2c0-1 1-2 2-3h2v2l1 2-1 1c0 2-1 3-2 5-1 1-2 2-3 2h-1l-2 1h-1c-1 0-1-1-2-1z" class="N"></path><path d="M569 828l1 2-1 1c-1 1-3 2-5 4v-1c0-1 0-2 1-3 0-1 1-1 2-2 1 0 1 0 2-1z" class="d"></path><path d="M569 831c0 2-1 3-2 5-1 1-2 2-3 2h-1l-2 1v-2c1-1 2-1 3-2 2-2 4-3 5-4z" class="q"></path><path d="M391 644v1h0c-1 1 0 1-1 2 1 1 3 0 5 0 1 4 3 8 3 12l1 1h0c1 1 2 2 2 3-1 2-2 3-4 4v-2l-1-1v-1-4c-1-3-2-8-5-10-1 1 0 1-1 1h-1c-2 0-2 0-4 1l1 1h0v1l-1-1c0-1 0-2 1-2h2c0-2 1-3 1-5l1-1h1z" class="O"></path><path d="M346 358c-1-1-1-2-1-2l-1-3v-1h-1v-1-1c-1 0-1-1-1-2-1-1-2-2-2-3l-1-1c-1-1-1-2-2-3v-1c0-1 0-1-1-2v-1h0c0-1-1-3-1-4h0l-1-1v-1c-1-3-1-5-3-7-1 0-1 0-1-1l2 1c0-1-1-2-2-3v-1c-1-2-2-4-2-6v-5l1-1v-1c0 1 0 3 1 4v1c1 1 1 2 2 3h0c0 2 0 4 1 5v3c0 2 0 3 1 5l1 2h2c-1 1 0 1-1 2 0 0 0 1 1 2 1 8 7 15 10 24h-1z" class="X"></path><path d="M524 800c2-4 4-9 6-14 3-5 6-11 10-16 2-4 5-7 8-11h0c0 2-4 8-6 10h1c0 1-5 8-6 9-3 4-4 8-6 12-2 3-5 6-6 11l-1-1z" class="t"></path><path d="M540 841h0c1-1 1-1 3-1l-12 17c-1 1-2 1-2 3-1 1-2 1-3 1v-1-2-1c1-1 1-1 1-2v-1h1c1 0 1-1 2-1 0-3 2-4 3-6 2-2 4-5 7-6z" class="Y"></path><path d="M287 367c0-4 1-6 2-9s2-6 2-9c1-6 4-12 6-17l-6 30h0l-1-1c-1 1-1 4-1 5l-2 1z" class="N"></path><path d="M307 373h1l1 3c1 2 0 5-1 7-1 1-1 2-1 4h1 0c-2 1-2 1-3 2 0 1 0 1 1 2v1 1l-1 1h-1c-2-1-1-2-2-3s-2-1-3-2c4-5 7-10 8-16z" class="B"></path><path d="M593 602l1-1c2 4 4 8 5 12v3c-1 1-1 2-1 3h-1l-3 1-2 1h0l-1 1-1-3c1-1 2-2 2-4v-1c1-2 1-4 1-5 0-4 1-4 0-7z" class="j"></path><path d="M599 613v3c-1 1-1 2-1 3h-1l-3 1c0-2 0-3 1-5s2-1 4-2z" class="W"></path><path d="M317 560c0-1 0-2 1-2v-1c1 2 1 2 1 3-1 1 0 2 0 3h0v3c1 1 1 2 1 3v1 3h1 0c0-2-1-5 0-8 0 1 1 1 1 2s-1 2 0 3v1l1 1v1h0c0 1 0 2 1 3 0 1-1 2 0 4v1l1-1h0v1c0 2 1 3 2 5-1 1-2 1-3 1s-2 0-2-1l-2-2c0-3-1-7-1-11s0-9-2-13z" class="d"></path><defs><linearGradient id="AX" x1="325.455" y1="402.96" x2="317.856" y2="408.002" xlink:href="#B"><stop offset="0" stop-color="#210000"></stop><stop offset="1" stop-color="#0c0706"></stop></linearGradient></defs><path fill="url(#AX)" d="M318 399v-1l2-2c1 0 1 0 2 1 1 2 1 5 1 8 1 1 1 4 2 5l-2 6-1-1v-1l-2-2c-1-1-1-4-2-5v-4-1c0-1-1-2 0-3z"></path><path d="M553 845h3l-3 3v1 2c-2 0-4 3-6 4-2 2-5 5-7 8l-2-6c0-1 0-2 1-3h0v1c1 1 1 2 2 3v-1c1-2 1-3 2-4 0-1 0-1 1-1h0v-1c2-1 2-2 3-2v-1l1-1 1 1h1c-1-1-1-1 0-2l3-1z" class="Y"></path><path d="M547 849l2 2-3 3c-2 0-2 0-3-1 0-1 0-1 1-1h0v-1c2-1 2-2 3-2z" class="N"></path><path d="M478 788c4 7 7 14 10 21 1 5 4 10 5 15l1 4 1 2 1 5c1 0 1 1 1 2h0c-1-1-1-2-1-3s-1-3-2-4c-3-12-11-23-16-35h0c1-1 1-1 1-2l-1-5z" class="j"></path><path d="M298 509h0c1-1 3-3 3-5h0l2-1h0l-1-1h-1l3-3 2-1c1 3 1 4 1 6l-1 1c1 1 1 0 1 1-1 1-1 2 0 2l-1 1h0v3l-1 2-1 1v-1l-3-1v1c-1-1-2-1-2-2-1-1-1-2-1-3z" class="X"></path><path d="M302 513c1-2 2-3 3-4 0 2 0 4-1 5l-3-1h1z" class="AU"></path><path d="M302 513c-1-1-1-1-2-1h-1v-1l1-1h1l2-2c0-1 1-2 1-3h1c0 1 1 2 1 2l-1 2c-1 1-2 2-3 4z" class="P"></path><path d="M499 827h1c2 3 2 6 3 9 1 2 2 3 2 5v1c1 1 2 2 2 3s1 2 1 2v1h-2v1h2v4c1 3 1 5 0 7l-2-1c0 2 0 2 1 3v-1h1v2h-1-1c-1 0-1 0-1 1h-1v-1h1c-1-1-1-2-1-3 1-2 1-4 0-6-1-10-1-19-5-27z" class="N"></path><path d="M313 459c1 7 1 14 1 21v8c0 1-1 2-1 4h0c0 2 0 3-1 5 0 1 1 1 0 2-1 2 0 6 0 9-1-3 0-6-1-9v-29h1c0-4 0-8 1-11z" class="w"></path><path d="M313 459c1 7 1 14 1 21l-1-3h0c1-1 0-4 0-6 0 1-1 5-1 6v-7c0-4 0-8 1-11z" class="P"></path><defs><linearGradient id="AY" x1="309.532" y1="506.299" x2="318.362" y2="510.154" xlink:href="#B"><stop offset="0" stop-color="#5b4f44"></stop><stop offset="1" stop-color="#69645c"></stop></linearGradient></defs><path fill="url(#AY)" d="M312 508c0-3-1-7 0-9 1-1 0-1 0-2 1-2 1-3 1-5h0c0-2 1-3 1-4l3 41c0 1 0 1-1 2l-1-6h-1c-1-6-2-11-2-17z"></path><path d="M265 416v18c-1 12 0 26 2 39 0 1 0 1-1 2 0-1-1-1-2-2v-3-2l-1-1c-1-3-2-6 0-9-1-1-1 0-1-1l-1-1h1c0-1 0-1 1-2s0-2 1-4v-6-15c0-2-1-5 0-6 1-2 1-5 1-7z" class="E"></path><path d="M515 865l-2-16 2-2h-1c-3-3-4-10-6-14-2-6-5-11-8-16-1-2-6-8-6-10 4 5 8 11 11 17 2 4 3 7 5 11 1 1 1 2 3 3l1 2 1-2 1-1v2c-1 7-1 13-1 19 0 2 0 5 1 7h-1z" class="AN"></path><path d="M504 872h1c1 2 1 3 2 5l1 3c0 1 1 3 2 4 0 1 0 3 1 4s1 2 1 3 0 2 1 3v9c-2 4-5 15-9 18l-1-1 1-2c5-9 8-17 6-27-2-7-4-13-6-19z" class="r"></path><path d="M674 707l11 1 7 2 3 1c9 3 18 9 25 15 2 1 5 5 7 5 1 0 1 1 2 2 2 2 5 4 6 7l-1 1h0l-3-3c-1 0-2-1-2-2l-1-1h-1c-2-3-5-6-7-9h-1l-3-3c-2 0 0 1-2 0v-1c-1 0-2-1-3-2h0l-4-2-2-1c-3-2-6-3-8-4-1 0-1-1-2-1s0 0-1-1h0-1-1-1c-1-1-1-1-2-1h-3c-2-1-2-1-4-1h0c-2-1-6 0-9-1v1c-2 0-4 0-6-1h-1c3 0 5 0 8-1z" class="AC"></path><path d="M543 796h1c1 0 1 0 1-1l1-1c1-2 3-5 6-5-3 5-7 11-10 17-1 1-2 3-3 5-1 3-3 6-5 9l-3 9c1-6 3-12 6-18 1-5 3-10 6-15h0z" class="O"></path><path d="M395 460c1-1 1 0 2-1l-1-1c0-1 0-1 1-2l24 26c1 2 3 7 4 10-4-6-8-13-13-18-5-6-12-10-18-14h1z" class="J"></path><path d="M354 614h1 0l7-5 3-3c-4 0-11 6-14 8l-1-1c4-2 7-5 11-7l12-6v1c2 0 3 0 5 1-5 4-12 6-17 10l-6 5c-1-1-1-1-1-3z" class="AC"></path><path d="M656 705c2-1 3-1 5-3h1l-1 1 2 2 2-1h6l5 1v2h-2 0c-3 1-5 1-8 1-4 1-8 1-12 2l-1-1c-2 0-4 0-6-1l9-3z" class="z"></path><path d="M656 705c2-1 3-1 5-3h1l-1 1 2 2h-2c-2 0-3 1-5 3l-3 1c-2 0-4 0-6-1l9-3z" class="k"></path><path d="M314 525h1l1 6c1-1 1-1 1-2l6 26c0 5 1 8 2 12 0 2 2 5 1 6l-11-41-1-7zm215 365l1 1c0 2-1 7 0 9 0 2-1 3 0 5h1c0-1 0-2-1-3 1-1 2-1 3-1l1 1h-1c0 2 1 3 2 4l2 3-1 1c-1 1-3 2-4 3l1 1c0 1 1 3 1 4l3 6c-2-1-4-3-5-5-5-7-4-21-3-29z" class="r"></path><path d="M310 532v-3l-1-1v-2c-1-2 0-2-1-3 0-1 0-2-1-2v-1l-1-1h1 0v-2c-1-1 0-3 0-4v-2c1-1 1-2 1-3v-1c0-1 0-2 1-3v-1h-1c0-1 1-1 1-2h0v-1-5l1-1v-9h-1c1-2 0-4 1-6 1-4 0-9 1-13v4 29c1 3 0 6 1 9 0 6 1 11 2 17l1 7-1 1v-1-3c-1-1-1-1-1-2v-3c-1-2-1-4-1-5-1-1 0-3 0-4-1-1-1-4-1-5-1-1 0-8 0-10-1 1-1 1-1 2-1 0 0 0 1 1 0 0-1 0-1 1-1 0 0 2 0 2-1 1-1 2-1 3v3c-2 3-1 5-1 7l1 1v2c0 3 1 5 2 8 0 1-1 2-1 2zm416 197h0c0-2-1-3-1-4l1-1v1c1 2 2 3 4 4l2 1 4 5c2 2 5 6 7 7l5 5c-3-1-7-1-10-3-1-1-3-2-4-3h0l1-1c-1-3-4-5-6-7-1-1-1-2-2-2l-1-2z" class="C"></path><path d="M566 759l1-3c-2-2-3-3-3-5 1 0 1 0 1-1h-3l-1-1-9-2c-2 0-5 0-7-1l1-1h4c2 1 5 1 7 1 3 1 9 2 12 4h1l2 1h2c3-3 7-6 9-11v-1c1-1 1-3 2-4v-1c0 2-1 6-3 9-2 4-5 8-8 11v-2c-1 1-2 2-3 2l-1 1c-1 0 0 0-1 1v-1h-1l1 2c-1 1-2 1-3 2z" class="I"></path><path d="M332 422l1 1c-1 1-2 2-1 3 0 1 0 1 1 2-1 0-2 1-3 1-1 1-2 2-2 3-3 2-18 13-19 15l-9 8 3-6 2-3 5-7 4-4c0 3-1 3-3 5v1 2c8-6 16-12 21-21z" class="B"></path><path d="M314 435c0 3-1 3-3 5v1 2c-2 2-6 5-8 6l2-3 5-7 4-4z" class="z"></path><path d="M317 310v-2h-1v-2-2h-1c0-3-1-5-2-8l-1-4c0-1-1-2-1-2v-1c0-1-2-2-2-3-1-1 0-1-1-2v-1-1h-1v-2h-1c0-2 0-1-1-2 1-2-1-5-1-8 0-2 1-5 1-8 1 2 1 6 3 8h1l3 20c2 6 5 13 5 20z" class="o"></path><path d="M397 455v-1l-1-1v-1l3 2c2 1 4 3 5 5l10 9-1-4c3 5 7 13 8 18l-24-26v-1z" class="S"></path><path d="M298 490l1-1h1l1-1h1l1 1v-1l3-2-1-1h0c-2 1-4 1-5 1h0c-1 1-1 2-2 2v-2h0l1-1c1-1 2-1 3-1h1v-1h0l1 1c1-1 2-1 2-1 1 1 2 2 2 3 0 2-1 2-2 3-1 0-1 1-1 2s-1 2-2 3v1c-1 1-1 1-1 2h-1v1c-1 0-2 1-3 2v2l-1-1c-1 0 0 0-1-1h0l-2-2h0c0-3 1-4 2-6l2-2z" class="C"></path><path d="M298 490c1 0 2-1 4-1l1 1 1-1v1 1c-1 1-1 1-1 2h0c-1 1-1 2-1 3-1-1-2-1-3-2s-1-2-3-2l2-2z" class="Y"></path><path d="M296 492c2 0 2 1 3 2s2 1 3 2c-2 1-2 2-4 3h-1l-3-1h0c0-3 1-4 2-6z" class="Z"></path><path d="M593 732c1-2 1-2 2-3s3-3 4-3c2 1 3 2 5 1h0l1 1s-1 0-1 1c-2 1-3 3-4 6-2 1-3 2-4 4h0l-6 4v-1l1-1-2-3 4-6z" class="R"></path><path d="M593 732l1 1v1 1c2 0 2 0 3-1l5-5v1c-1 1-4 4-4 6l-3 2 1 1h0l-6 4v-1l1-1-2-3 4-6z" class="j"></path><path d="M592 568c-1 0-3-1-4-2h0c1-1 2-1 2-1 1 0 2-2 2-3 2-1 4 0 7-1 1-1 2-1 4-1h1 1c1-1 2-1 3-2 0 1 0 1-1 2h1l1 2v1l-1 1-1-1-1 1-1 1c1 1 2 1 3 2v1h0l-3 1c-2-2-7-2-10-3l-3 2z" class="c"></path><path d="M607 560h1l1 2v1l-1 1-1-1-1 1-1 1c-3-2-6-2-9-3l8-1 3-1z" class="V"></path><path d="M607 560h1l1 2v1h-4c-1 0-1-1-1-2l3-1z" class="B"></path><path d="M613 539l1 3c1 4 2 8 2 12h0v4 1c-2 2-4 3-7 3l-1-2h-1c1-1 1-1 1-2v-1h-2l2-2c1 0 1-1 2-1 1-1 0-1 1-2h0v-1l1-1h0c1-2 1-6 1-8l-2 2-1 1v1c0 1 0 0-1 1l-1 1-2 2h0l2 2h0l-1-1h-2-1l2-3c3-3 5-5 7-9z" class="I"></path><path d="M608 560c4-1 6-2 8-6v4 1c-2 2-4 3-7 3l-1-2z" class="a"></path><path d="M295 316c0 1-1 3-1 5l-9 25c-2 6-4 14-7 20h0c1-8 2-15 4-22 3-9 7-20 13-28z" class="P"></path><defs><linearGradient id="AZ" x1="517.349" y1="876.219" x2="524.121" y2="882.255" xlink:href="#B"><stop offset="0" stop-color="#413f46"></stop><stop offset="1" stop-color="#57575a"></stop></linearGradient></defs><path fill="url(#AZ)" d="M526 860v1c1 0 2 0 3-1 0-2 1-2 2-3-3 7-7 14-9 23-1 3-2 6-2 9-1 3-1 5 0 8 0 4 0 8 1 12l-1 2c-1-5-1-10-2-15v-10c0-3 1-6 2-10 0-2 1-5 2-7 0-2 2-4 2-5v-1s0-1 1-1v-1l1-1z"></path><path d="M411 587l1 1c-2 1-3 2-5 3h0l2 2c-1 0-2 0-3 1-1 0-1 0-2 1l-1 1c-3 0-6 0-9 1-6 1-11 3-16 5-2-1-3-1-5-1v-1c3-1 8-4 12-5 2 0 6 0 9-1 1 0 2-1 2-1 1 0 2 0 3-1h1c2 0 2-1 4-2s5-2 7-3z" class="L"></path><path d="M554 798c-1 0-1 1-2 2l-1-1c1-2 3-3 4-5h0c1-1 3-2 4-3 0-1 1-2 2-3 2-3 4-4 6-6l16-17 1 1-1 1v2c-1 2-17 19-20 21-1 0-2 1-3 2h1v1 1h0l6-6-2 4c-2 2-4 3-6 4-2 0-3 1-5 1v1z" class="U"></path><path d="M457 681v-3c-1-5-3-10-4-15-2-6-3-12-4-18h1 0l6 22c1 1 1 2 2 3v1l1 1v1l2-2v6c1 0 2-1 3-1v4c0 2 1 4 2 5 0 1 0 2-1 2l-2-2-1-1h-4c-1-1-1-2-1-3z" class="J"></path><path d="M456 667c1 1 1 2 2 3v1l1 1v1l2-2v6 2l-1 1c-2-3-3-9-4-13z" class="R"></path><path d="M461 677c1 0 2-1 3-1v4c0 2 1 4 2 5 0 1 0 2-1 2l-2-2-1-1h0c1-1 1-2 1-3h-2l-1-1 1-1v-2z" class="V"></path><path d="M392 608v-2h0l2 3c1 1 0 4 2 5v1c0-2-1-3 0-4 1 0 3-1 4 0 1 0 3 2 4 1s2 0 3-1v3c-2 3-3 6-5 8l-2 1-1-1c-1 0-2 1-3 1v-1c-1-1-1-2-1-3-1 0-1 0-2 1l1 1c-1 1-1 1-2 1v-1c0-1 0-2 1-3 0-2 0-4-1-5v-1-4z" class="L"></path><path d="M393 618v-4-1c1 2 2 3 2 6-1 0-1 0-2 1l1 1c-1 1-1 1-2 1v-1c0-1 0-2 1-3z" class="Q"></path><path d="M396 615c0-2-1-3 0-4 1 0 3-1 4 0 1 0 3 2 4 1s2 0 3-1v3c-2 3-3 6-5 8l-2 1-1-1c-1 0-2 1-3 1v-1l1-1h1c2 1 3-1 4-2-1-1-1-2-1-3 0-3-3-2-4-3l-1 1v1z" class="S"></path><path d="M367 401c1-2 1-2 1-4v-1c2 2 3 3 5 3h1c3 4 7 7 11 10 1 1 3 2 4 3l2 1v5c-1-1-2-2-3-2-4-2-7-5-10-7h0l2 3-1 1c-4-3-8-8-12-12h0z" class="k"></path><path d="M386 706c3 0 5 1 8 2v-1h2c2 3 6 5 10 6 5 3 10 5 16 6 3 1 5 2 9 3h-7 0c-1 0-2 0-2-1v2c-8-2-16-6-23-9-6-3-12-7-18-6v-1h1c1-1 3-1 4-1z" class="AA"></path><path d="M315 190c2-1 3-2 5-3s3-1 5-2l-3 3h2c-3 1-4 1-6 3l-8 14c-2 4-5 10-9 13l-6-6h-1l1-1h1l1 1 3 3c2-2 1-4 2-5h1v-1-1c1-1 0-1 1-2h1l1-2c4-4 6-9 9-14z" class="B"></path><defs><linearGradient id="Aa" x1="626.476" y1="712.889" x2="636.545" y2="722.087" xlink:href="#B"><stop offset="0" stop-color="#670706"></stop><stop offset="1" stop-color="#951b1c"></stop></linearGradient></defs><path fill="url(#Aa)" d="M644 709l3-1c2 1 4 1 6 1l1 1c-15 4-28 11-41 19 1-1 1-1 1-2h-2c3-2 6-4 9-7 2-1 4-2 7-3 2 0 14-7 16-8z"></path><path d="M569 735c-1 2-4 6-6 8-2 1-4 1-6 1-1-3 3-5 3-8 1-3 3-6 4-8v-1c0-2 2-3 3-4 2 1 4 3 5 5v2l-1 1h0c0 2-1 3-2 4z" class="c"></path><path d="M569 735c0-1 0-1 1-2h0c-1-1-1-2-3-3l1-2h1s1 0 1 1c1 1 1 1 2 1l-1 1h0c0 2-1 3-2 4z" class="AC"></path><path d="M318 620c2 0 3 0 5 1h0l5 3v1h0v6c-1 6 0 10 2 16l-1 1h0c-5-6-8-15-10-23l-1-5h0z" class="H"></path><path d="M323 621l5 3v1h0v6c-1-1-1-2 0-3v-1c-1 0-2 0-3-1v-1h-2v-4z" class="T"></path><path d="M318 620c2 0 3 0 5 1h0v4c0 3 2 6 2 8h-1c-2-4-2-8-4-12l-2-1h0z" class="c"></path><path d="M527 854c1-1 1-1 1-2 1-2 2-3 2-4 0-2 2-3 2-4h-1-4v-2h-1c-1 2-2 3-3 5v1c-1 2-1 4-1 6-1 3 0 5-2 7-1-1 0-6 1-8 1-6 2-12 5-18l2-2h1c0 1 0 1-1 1 0 1-1 2-1 3-1 1-1 2-2 4h1c0-1 1-1 2-2 1-2 3-3 4-5 2-2 3-4 4-5h1c-1 1-1 1-1 2h-1c0 2-2 3-3 5h1c0 1 1 1 2 1l1 1 1 1 1-1c1 1 1 2 2 3-3 1-5 4-7 6-1 2-3 3-3 6-1 0-1 1-2 1h-1z" class="C"></path><path d="M427 695v-2c2 1 3 1 4 2 2 1 4 2 6 1h1c2 1 3 2 5 2h1l1-1 4 5v3c-2 0-1 0-2-1h-1s-1-1-2-1c0-1-1-2-2-2 0 3 2 6 3 10-1-2-2-3-2-4h-1 0c1 2 0 3 0 4l1 2c-1 0-1 0-2-1l-2-1c-2-1-2-3-2-5h0c-2-3-3-4-5-6l-1-3c-1-1-2-2-4-2z" class="I"></path><path d="M440 700l2 1c0 3 2 6 3 10-1-2-2-3-2-4h-1 0c1 2 0 3 0 4l-1-3c0-3-2-5-2-7l1-1z" class="c"></path><path d="M427 695v-2c2 1 3 1 4 2 2 1 4 2 6 1h1c2 1 3 2 5 2h1l1-1 4 5v3c-2 0-1 0-2-1h-1s-1-1-2-1c0-1-1-2-2-2l-2-1c-3-1-6-1-9-3-1-1-2-2-4-2z" class="J"></path><path d="M333 412c1 1 1 2 2 3-1 2-3 5-3 7-5 9-13 15-21 21v-2-1c2-2 3-2 3-5 3-3 6-6 7-9 2-1 3-3 4-4l1-1 2-2v2c1-2 3-4 4-6 0-1 1-2 1-3z" class="AF"></path><path d="M328 419v2c-3 5-5 10-9 15-2 2-6 3-8 5v-1c2-2 3-2 3-5 3-3 6-6 7-9 2-1 3-3 4-4l1-1 2-2z" class="y"></path><path d="M637 771c-1-1-1-2 0-3v-1-1l-2 2-1-3c1-2 0-3 1-4 4-2 2 0 6 1 0 1 0 1-1 3 1 1 1 2 1 4l-1-3h0v1h-1 0v1c-1 1-1 0-2 0v1s1 1 0 2h1 1l-1-1v-1c1 0 1 0 1 1h1v1c0 1 1 2 2 2l1 1h-1c1 1 1 2 2 2h1c2 3 5 6 8 7 1 1 1 1 2 1v1h2l1-1c1 0 3 0 4 1-4 2-9 3-13 3-1-1-2-1-2-1h-1l-4-2-7-3-1-1-1-1h-1c-1 0-2-1-3-1-2-1-3-1-5-1l1-2c1 0 2 1 3 1h1c4 2 9 4 13 6 2 1 2 1 4 1h0c1-1 1-1 3 0h2s-5-4-6-5c0-2-4-4-6-6 0 0-1-1-2-1v-1z" class="N"></path><defs><linearGradient id="Ab" x1="385.962" y1="690.845" x2="393.826" y2="685.169" xlink:href="#B"><stop offset="0" stop-color="#302f32"></stop><stop offset="1" stop-color="#605f62"></stop></linearGradient></defs><path fill="url(#Ab)" d="M370 665c8 10 17 20 27 29l12 11-1 1h0l-1-1h-1a180.31 180.31 0 0 1-39-39h1l3 4h1c0-2-1-2-2-3v-2z"></path><path d="M445 753c3 2 8 8 11 8h0c-1-3-3-5-3-8l1 1 1 1c1-1 1-1 1-2h1l1 1c-1 2 0 4 1 6l1 1 14 21-1 1s1 1 1 2v1l-2-3v-1h0-2c-3-4-6-9-10-13-5-5-11-10-16-15l1-1z" class="O"></path><path d="M336 351l7 14c1 2 2 5 4 7 3 5 6 10 10 16 3 4 6 9 10 13h0v3l-10-12c-2-4-5-9-9-11l-2 1h-1l2-3c-1-3-3-5-4-8l-8-16v-1c1-1 1-2 1-3z" class="AE"></path><path d="M567 788c2-1 4-3 5-4 0-1 1-2 2-3 2-1 3-3 4-3 1-1 2-2 4-3l2-2c1-1 2 0 3-1 0 0 1-1 2-1 0 0 0 1 1 2h0 0l5-4h0c-1 3-3 5-5 7-1 2-2 4-4 6l1-3h0c-2 0-3 1-4 2s-2 1-2 2c-2 2-2 3-3 5l-2 2h-1v-1c1-1 1-1 1-2-2 0-3 1-4 2-2 1-3 0-5 2h0l-2 1 2-4z" class="d"></path><path d="M350 398l-1-2v-4c2 1 4 5 6 6v1c1 1 1 2 2 4l1 1 2 2 2 3c1 2 2 4 3 5l1 1 2 2c1 1 3 2 4 4 0 1 1 1 1 2l2 1c4 4 9 7 13 11-2-1-3-1-5-2l-5-5c-3-2-5-5-8-7-1 0-1 0-2 1-5-6-10-10-14-16-1-2-2-3-3-4h-1v-4z" class="H"></path><path d="M354 406l3 3-5-11h1c1 0 1 1 2 2 2 4 10 17 14 19h0l1 2c-1 0-1 0-2 1-5-6-10-10-14-16z" class="J"></path><path d="M513 838l1-1c0-9-4-15-6-23 0-2-1-3-2-5 0-1 0 0-1-1 0-1-1-3-1-4-1-3-3-5-5-7v-1h0c0-1-1-2-1-3-1-1-1-1 0-2h0l1 1c2 3 4 7 5 11 1 2 3 4 4 7s3 7 4 11c1 2 2 4 2 6h0l1 1c0-2 0-3-1-4 0-4 1-8 1-11 1 0 1 4 1 5l8-18 1 1-5 15c-1 4-2 9-3 13 0 4 0 8-1 10v-2l-1 1-1 2-1-2z" class="q"></path><path d="M452 782l-3-4s0-1-1-1c-1-3-3-4-4-6 0-2-1-1-1-3-2-3-5-5-7-7 0-2-2-3-3-5-4-4-9-8-13-12l1-1c6 6 11 12 17 18l9 9 10 10 10 10c2 1 3 2 4 4 1 1 2 1 3 3 1 1 2 2 2 3h-2c-1 0-3-2-4-3 0-1-1-1-1-2-1-1-1-2-2-3-2 0-1 1-2 0l-5-5-5-5h-3z" class="Y"></path><path d="M346 358h1c1 3 1 7 3 10h1l2 2 8 11c1 1 2 3 3 4 2 3 5 5 7 8 1 0 2 1 2 2h-1c0 1 0 2 1 2 0 1 1 2 1 2h-1c-2 0-3-1-5-3v1c0 2 0 2-1 4-4-4-7-9-10-13-4-6-7-11-10-16v-1c1 1 1 0 1 1l6 9c0 1 1 2 1 3 1 0 2 1 2 2h0l1 1s1 1 1 2l1 2 1 1c1 0 1 1 2 1 1 2 2 3 3 5h1c0-1-1-2-1-2-1-1-1-2-1-2v-1l-1-1c-1-1 1 0-1-1-1-1-2-4-3-5l-1-2-4-7v-1c-1-1-2-2-3-4h0v1 1h-1l-1-2c0-1-1-2-1-3 0-2-1-3-1-5l-1-1c0-1 0-1-1-1v-4z" class="o"></path><path d="M581 810c0-1 0-2 1-2l1 1h0c-1 3-2 9 0 12v2h1v-1l1-1c0 2-1 3-1 5l-2 1 2 3-1 1 1 1c-1 1-1 2-2 2h-1v-1c0-2-1-4-2-5-1 1-2 2-4 3-1 0 0 0-1 1-1 0-1 0-2 1s-3 2-5 3c1-2 2-3 2-5l1-1-1-2v-2h1l-1-2c0-1 1-2 2-3v-1c2-1 2-2 4-2l1 1 1-1h1c-1 1-1 1-1 3 0 0 1 1 2 1 0-2 1-2 1-3 1-1 0-5 1-7v-2z" class="N"></path><path d="M570 826c1-2 1-2 3-3 1 0 1 0 2 1 1-1 0-1 1-3l1 1c0 1 0 2-1 3h1l1 1v-2h0c1 1 1 2 1 4-1 1-2 2-4 3-1 0 0 0-1 1-1 0-1 0-2 1s-3 2-5 3c1-2 2-3 2-5l1-1-1-2v-2h1 0z" class="l"></path><path d="M570 826h4l-4 4-1-2v-2h1 0z" class="q"></path><path d="M703 664c1-1 1-1 2-1v1l1 1 1 5c0 3 0 7 1 9 2 2 3 4 3 6v1-1h-1v-1-2c-1-1-2-2-2-3h-1c0 1 1 2 1 3l-2 1-1-2h-3 0-2c-1-1-1 0-2-1v-1l-1 1v1c-1 1-2 1-2 1-2 0-2 0-3-1l1-1h0v1h1 1c1-1 1-1 1-2h-1l-1 1v-1c0-1 0-1 1-2h1l-1-3 2-2c1-1 0-2-1-3v-1l1-3 3 1c0-1 1-2 2-3l1 1z" class="n"></path><path d="M696 669v-1l1-3 3 1c0 2 0 2-1 3h-3z" class="c"></path><path d="M703 664c1-1 1-1 2-1v1c0 1-1 3-1 4 1 2 0 3 0 4v-3c0-1-1-2-2-2 0-1 1-2 1-3z" class="S"></path><path d="M702 667c1 0 2 1 2 2v3h0c1 2 1 3 1 5 0 1 0 2-1 3-2 0-3 0-4-1 0-2-2-6-1-7 0-1 2-4 3-5h0zM417 551l-1-1v-2h3l4 4 1 1c1 0 2 1 3 1l2 2c2 1 3 1 5 1h1 1 0l2-1s-1 0-2-1h-2l-3-2h-2v-1c-1 0-1 0-2-1h1l4 1c1 0 0 0 1-1h0 2v-1c3 1 7 2 9 4l1 1c-2 1-2 0-4 0-1 1-3 2-4 3 1 0 2 1 4 1v1 1h3v1c-1 0-2 1-3 1h-3-1c-2-1-5-1-6-1h-1 0c-2-1-3-2-5-3h-1c-1-1-1-1-2-1h0c-1-1-1-1-1-2-2 0-2-1-2-2l-2-1v-2z" class="L"></path><path d="M417 551v-1l1-1c4 4 9 6 14 9 1 1 3 2 5 3l1 1c-2 0-7-1-10-2-5-2-7-5-11-9z" class="E"></path><path d="M380 412l-2-3h0c3 2 6 5 10 7 1 0 2 1 3 2l8 7 1 2h-2v2c2 4 4 8 5 13-4-6-10-11-16-17l-5-5 2-2-5-5 1-1z" class="G"></path><path d="M384 418c1 1 4 3 5 6l-2 1-5-5 2-2z" class="AG"></path><path d="M380 412l-2-3h0c3 2 6 5 10 7 1 0 2 1 3 2l8 7 1 2h-2v2l-1 1h0-2c-2-2-3-5-5-7-3-4-6-8-10-11z" class="D"></path><path d="M391 552c0-2 1-3 2-5 1-3 1-6 1-8v-1c0-4-2-10 0-14v-1c0-2 0-2 1-4l1 1 3 3h1l3 3-2 7-3 10c-4 12-9 22-18 31l-7 6-1 1-3 1 4-4c2-2 5-4 7-6 1-1 1-2 3-3 1 0 1-1 2-2s1-1 1-2l3-4c1-1 1-2 2-3l1-3h1v-2c1-1 1-2 2-3v-2c1-1 1-3 1-5 1-1 0-2 0-3 1-2 1-2 1-3 1-2-1 0 0-2v-1h-1v-4c-1-1-1-1-1-2h0v-1c0-1 0-2 1-3h-1c-1 1-1 1-1 2v6h1v3c1 1 0 4 0 6-1 1 0 2 0 3l-1 1v2c-1 1-1 0-1 1 0 2-1 3-1 4h-1z" class="L"></path><path d="M398 543v-2c-1-3 1-5 0-7s-1-2-1-4c2 2 2 2 4 3l-3 10z" class="h"></path><path d="M399 523h1l3 3-2 7c-2-1-2-1-4-3 1-1 1-2 0-3v-4h2z" class="n"></path><defs><linearGradient id="Ac" x1="288.468" y1="503.587" x2="283.676" y2="504.118" xlink:href="#B"><stop offset="0" stop-color="#3e3831"></stop><stop offset="1" stop-color="#616061"></stop></linearGradient></defs><path fill="url(#Ac)" d="M281 460c1 2 1 3 1 5 1 0 1 1 1 1-1 2-1 4 0 6 0 14 3 29 5 43l5 24c1 4 3 10 3 15h-1l-1-2c-3-7-4-16-6-24-4-18-7-36-8-54l1-6v-8z"></path><path d="M589 806v-1l1-5c1-2 2-4 2-6 1-1 1-2 2-2h0c0 1-1 2-1 4h-1v2 3c-1 1-1 0 0 1h0l3-3h0-2c1-1 2-1 3-2l1-1 1-1s1-1 1-2c2 0 3-1 4-2h1l-1 1c0 1-1 1-1 2l1 1 1-3v-1l2-2c1 0 1 0 2-1v1c-1 1-3 3-3 4s0 1-1 2h0c1 1 1 3 1 4l-1 1-1-1v-1h1v-1h-1c-1 0-2-1-3-1h0v2c1 0 2-1 2-1 1 1 1 1 1 2l-1 1h-1v-1h-1 0l-1 1v1c0 1-1 1-1 1 0 1-1 1 0 2v2h-1c0-1 0-2-1-3v2h0l-1 1h0v1 1l1-1h1c1 0 1 0 2-1 2 1 2 0 4 0h1 0c-1 1-1 1-2 1h-1l-1 1h-2 0c1 1 2 2 2 3l-1 1c1 1 1 0 2 1h0-1l-1-1c-2 1-1 0-2 2h1l-2 2h0l-1-3h-1v2l-1 1c-1 1-1 1-1 2 0-2 0-2-1-3h-2v-9z" class="d"></path><path d="M589 806h1v3h1c1 1 1 1 1 3 0 1-1 2-1 3h-2v-9z" class="N"></path><path d="M412 571l3-1c2 0 3 1 4 1l-2 1-4 1 3 3-37 14h-1l-2-1 1-1c1-1 2-1 2-2 3-2 6-3 9-4l13-9 2 1 1-1 1 1c2-2 5-1 7-3z" class="AB"></path><path d="M403 574l1-1 1 1 3 1-7 2c1-1 1-2 2-3z" class="p"></path><path d="M412 571l3-1c2 0 3 1 4 1l-2 1-4 1c-2 1-4 2-5 2l-3-1c2-2 5-1 7-3z" class="M"></path><path d="M377 588c1-1 2-1 2-2 3-2 6-3 9-4l13-9 2 1c-1 1-1 2-2 3-7 3-17 7-23 13l-2-1 1-1z" class="e"></path><path d="M565 792l2-1h0c2-2 3-1 5-2 1-1 2-2 4-2 0 1 0 1-1 2v1h1l2-2c1-2 1-3 3-5 0-1 1-1 2-2s2-2 4-2h0l-1 3-1 1c-2 1-5 4-7 6 0 1 0 1 1 2l1-1v1c-1 1-1 1-3 2h0c-1 1 0 1-1 1-1 1-2 1-2 2-1 1-2 1-3 2h-1c-2 1-3 2-5 3-1 0-8 6-10 6l-2 1-1 1-1 1-1-1h0l2-1v-1h-1c0-1 2-2 3-3 1 0 2 0 3-1l1-1 3-3v-1c-2-1-3 0-5 0h-1-1v-1c2 0 3-1 5-1 2-1 4-2 6-4z" class="N"></path><path d="M572 833c1-1 1-1 2-1 1-1 0-1 1-1 2-1 3-2 4-3 1 1 2 3 2 5v1c-1 0-1 1-2 1v1h1v1c0 1 0 1-1 2l1 2h1-1c-1 0-2 0-4 1h0-2c-1 1-1 1-2 1h0c-1 0-2 1-2 1h-2 0c-3 1-5 3-9 2-1 1-5 5-6 5v-2-1l3-3h-1c1-1 2-2 2-4l-1-1 1-1 1-1c1 0 1 1 2 1h1l2-1h1c1 0 2-1 3-2 2-1 4-2 5-3z" class="t"></path><path d="M572 833v2l-3 2c-2 2-4 3-6 5-3 2-7 4-10 7v-1l3-3h-1c1-1 2-2 2-4l-1-1 1-1 1-1c1 0 1 1 2 1h1l2-1h1c1 0 2-1 3-2 2-1 4-2 5-3z" class="N"></path><path d="M561 839l2-1h1l-5 4s-1 0-2-1l-1-1 1-1 1-1c1 0 1 1 2 1h1z" class="t"></path><path d="M572 833c1-1 1-1 2-1 1-1 0-1 1-1 2-1 3-2 4-3 1 1 2 3 2 5v1c-1 0-1 1-2 1v1c0 2-1 3-2 4-2 0-4-1-5 1v1h-1 0-1c-1 0-2 0-3 1h-1l2-2c2 0 3-2 5-3 2 0 3-3 4-4-3 2-5 4-8 3l3-2v-2z" class="q"></path><path d="M572 833c1-1 1-1 2-1 1-1 0-1 1-1 2-1 3-2 4-3 1 1 2 3 2 5-1 0-2 1-3 1 0-1 0-1 1-1l-1-1v1c-1 1-2 1-3 1h0l-3 1v-2z" class="b"></path><path d="M594 832h1 1v-1c1 0 2 1 3 2l1-1h-1v-1c1 0 2 1 2 2 1-1 1 0 1-2l1 1c1 1 0 1 2 2l1-1 2 2 1-1 1-1v1 1h2c1 0 1-1 2-2 2 1 5 2 7 2 1 0 3-1 4-1 2-1 3-1 4-1-2 1-4 3-6 4-8 3-17 5-25 5 0-1-1-2-1-2-3 0-4 1-7-1l-1-2h1v-1c1-1 1-1 1-2s0-2 1-3l2 1z" class="g"></path><path d="M589 837h1v-1c1-1 1-1 1-2s0-2 1-3l2 1c0 1-2 1-2 2 0 2 0 3-2 5l-1-2z" class="l"></path><path d="M599 833l1-1h-1v-1c1 0 2 1 2 2 1-1 1 0 1-2l1 1c1 1 0 1 2 2l1-1 2 2 1-1 1-1v1 1h2c1 0 1-1 2-2 2 1 5 2 7 2 1 0 3-1 4-1 2-1 3-1 4-1-2 1-4 3-6 4v-1h0-4c-2 0-3-1-4-1v1h-2l-1 1c-1 0-4 1-5 0v-2c-2 0-3 1-5 0l-1-1c-1 0-2 0-3-1h1z" class="Y"></path><path d="M385 760v-1c0-1-1-2-1-2v-1l-2-2h0v-1l-1-1c-1-2-2-2-3-3v-1c-2-1-3-2-4-4h0c3 2 5 4 7 7 2 1 0-1 1 1 1 1 2 3 3 4l1 1c1 1 2 2 2 4l-1 2c1 0 1 0 1 2h-1v1c1-1 1-2 1-3l2-1v-1l1 1h0 0c2 1 2 1 2 3v1 1c-2 2-5 6-5 8-1 0-2 1-2 2h-2c-2 1-3 2-4 3 0 1-2 2-3 2h-1l-1 1v-1-1l-1 1-1-1c-1 0-2 1-4 1h-1c3-1 5-2 7-4 2-1 3-3 4-6 2-5-1-10-3-14 0 0 2 1 2 2l1 1v1l1 1v1 2l1 1v1c1-2 1-3 2-5v-3h2z" class="U"></path><path d="M376 758s2 1 2 2l1 1v1l1 1v1 2l1 1v1c1-2 1-3 2-5v-3h2 0c0 1 0 2 1 2 0 1 1 2 0 3-1 5-6 12-10 15l-1-1v-1c2-1 3-3 4-6 2-5-1-10-3-14z" class="Y"></path><path d="M294 228c2 0 2 1 3 0s2-1 2-2l1-4h0l3 3h0c-2 3-3 7-4 10l-6 14-7 19-1-2c1-2 1-3 0-5l-1-1 1-3 2-3c-1-1-1-2-2-3 1-1 2-3 2-4 1-4 3-8 4-11h0c1-2 2-4 2-6 1-2 1-1 1-2z" class="F"></path><path d="M282 454v5h1v-1c0-4 2-9 3-13l3-18c0-3 1-6 1-9h-1l-1-3c-1-3 0-10 2-13 0-1 1-1 2-2v1 1c0 1-1 2-1 2v2c-1 2-1 3-1 5s-1 3-1 5l1 1c1-3 1-7 4-9l2 1 1 1-2 4c-1 3-1 5-2 7l-6 34c-1 4-1 9-3 13l-1 4c-1-2-1-4 0-6 0 0 0-1-1-1 0-2 0-3-1-5 0-2 0-4 1-6z" class="b"></path><path d="M379 638v2 1c1 1 1 2 1 3 1 1 1 1 1 2v1c0 2 1 4 1 6h1v1 3c0 3 1 5 2 7l3 14c-3-3-7-7-8-12-1-6-3-12-4-17v-2l-1-1v-3c-1-1-1-3-1-4-1-1-1-2-1-3h0c0-1-1-2-1-3-1-3 0-6-2-9-1 0-1-1-1-2v-1-2h-1c1-1 1-2 2-3l1 1c0 3 0 5 2 8h0c0 1 0 2 1 3h0 1v-1c1 4 2 8 3 11h1z" class="I"></path><path d="M298 352l3-7v10c1 4 1 10 0 14-1 2-1 3-2 4h1c-1 2-1 4-2 6-1 0-1 1-1 2s-1 2-2 4c-1 1-2 3-3 4-1-1-1-2-1-4h2l-1-1-1-1h-1v1h-1c-1 1 0 1-1 2l3-24h0l1 1v2l1-1-1-1h1v-2l1-1c1 2 0 5 0 7 0 1 1 2 2 2 1 2 1 2 1 4h0c0-2 1-3 0-4 0-1 0-1 1-2l-1-7c-1-3 0-6 1-8z" class="C"></path><path d="M298 352c1 0 1 1 2 2v3c-2 1-2 2-2 4v1h0v3 2l-1-7c-1-3 0-6 1-8z" class="U"></path><defs><linearGradient id="Ad" x1="279.581" y1="412.825" x2="288.762" y2="414.551" xlink:href="#B"><stop offset="0" stop-color="#2f2c28"></stop><stop offset="1" stop-color="#54483e"></stop></linearGradient></defs><path fill="url(#Ad)" d="M287 367l2-1c0-1 0-4 1-5l1 1-3 24-3 23-1 3c-2 5 0 9-1 13v7l-1 22c-1 2-1 4-1 6v8l-1 6v-15c0-25 1-49 4-73v-2c1-3 2-7 3-11v-6h0z"></path><path d="M371 615c3-2 7-3 10-4 3-2 6-2 10-3v-3h0 1v1h0v2 4 1c1 1 1 3 1 5-1 1-1 2-1 3v1h-1-1l-2-2 1 2c-1 1-1 1-2 1s-1 1-1 1c-1 1-2 1-3 1-2 1-6 1-9-1-1-3-1-5-2-8l-1-1z" class="c"></path><path d="M392 608v4 1c1 1 1 3 1 5-1 1-1 2-1 3v1h-1-1l-2-2 1 2c-1 1-1 1-2 1s-1 1-1 1c-1-1-1-3-1-4 0-2 0-2-1-4h0c0-1-1-2-1-3v-1l2 2v1c3 0 3 1 5-1l1-1c1-2 1-3 1-5z" class="o"></path><path d="M384 616h1c2 1 4 3 5 6l-2-2 1 2c-1 1-1 1-2 1s-1 1-1 1c-1-1-1-3-1-4 0-2 0-2-1-4z" class="L"></path><path d="M630 753h3c3-1 7-1 9-3 4-4 7-6 12-8 0 3-4 3-4 6-1 0-1 1-2 1-1 1-1 2-2 3 0 2 1-1 0 2h0c-1 1-1 1-1 2l-1 1c0 2-2 3-2 5v1c0 1 1 1 0 2v4 1h0l-1-1c0-2 0-3-1-4 1-2 1-2 1-3-4-1-2-3-6-1-1 1 0 2-1 4l1 3 2-2v1 1c-1 1-1 2 0 3-3-1-7-2-10-2h-1c-2 0-5 0-7 1-1 0-2 1-4 1h0c2-1 4-4 6-4 1 0 1-1 2-1l1 1c0-1 0-1 2-1 0 0 1-1 2 0h1 1l-1-2h2l-1-3c1-1 1-3 1-4-2 0-3 0-5 1 1-1 2-2 4-3v-2z" class="d"></path><path d="M631 757c1 0 2 0 3 1h1v1s-2 1-2 2c-1 1 0 1-2 3h0l-1-3c1-1 1-3 1-4z" class="N"></path><path d="M584 826v1c1 3 2 6 4 8l1 2 1 2c3 2 4 1 7 1 0 0 1 1 1 2h-8c-5 1-9 1-14 2-13 2-27 11-35 22-2 3-4 5-5 8-3 5-4 11-6 17l-1-1c2-10 6-19 11-27 2-3 5-6 7-8 2-1 4-4 6-4 1 0 5-4 6-5 4 1 6-1 9-2h0 2s1-1 2-1h0c1 0 1 0 2-1h2 0c2-1 3-1 4-1h1-1l-1-2c1-1 1-1 1-2v-1h-1v-1c1 0 1-1 2-1h1c1 0 1-1 2-2l-1-1 1-1-2-3 2-1z" class="AK"></path><path d="M584 830c1 3 3 6 5 9l1 1h-2c-3 0-5 1-7 1h-1l-1-2c1-1 1-1 1-2v-1h-1v-1c1 0 1-1 2-1h1c1 0 1-1 2-2l-1-1 1-1z" class="P"></path><path d="M580 836h2l1-1v1c0 1 0 2 1 3 1 0 2 1 4 0h1l1 1h-2c-3 0-5 1-7 1h-1l-1-2c1-1 1-1 1-2v-1z" class="g"></path><path d="M401 511v-1c1-1 2-1 3-1l1 2v1c1 2 0 8 0 10l-1 6c1 1 2 2 2 4l1 2c-2 0-2 0-3 1v1c0 1-1 2-1 3v1l-2 3v1h0l-2 7-15 24h-2c-1 0-1 0-2-1 9-9 14-19 18-31l3-10 2-7-3-3h-1l-3-3v-3h-1l-1-1c1-1 2-1 3-2 1 0 1 0 2-1l-1-1 3-1z" class="AR"></path><path d="M404 528c1 1 2 2 2 4l1 2c-2 0-2 0-3 1v1c0 1-1 2-1 3v1l-2 3v1c0-5 2-11 3-16z" class="D"></path><path d="M401 511v-1c1-1 2-1 3-1l1 2v1c-1 4-1 8-2 12-1 0-2-1-3-2h0v1h-1l-3-3v-3h-1l-1-1c1-1 2-1 3-2 1 0 1 0 2-1l-1-1 3-1z" class="T"></path><path d="M398 512l3-1c-1 2-1 3-1 5 1 2 0 3 1 5l-1 1v1h-1l-3-3v-3h-1l-1-1c1-1 2-1 3-2 1 0 1 0 2-1l-1-1z" class="E"></path><path d="M650 599c-1-1-2-2-3-2 0-1 3 0 5 0l9 4c2 0 3 1 4 1h1s0 1 1 1 2 0 2 1c2 0 4 1 5 1h1c1 1 2 1 3 2l2-1 1 1 2 1 1-1-3-1c0-1-1-1-2-1h-1l-3-2c-4-1-10-2-13-4 2 0 4 2 6 1l-1-1c-1 0-2-1-3-1 1-1 1 0 2 0s1 1 3 1h0c2 0 3 1 4 1l1 1c6 2 11 6 18 6h2 4c1 0 1 1 1 1 0 1 0 1-1 2v-2c-3 0-5 0-7 1 1 2 2 3 4 5l-2 1c-4-2-8-3-12-4h-10c0-1-2-1-2-2-2 0-4-1-6-2-1-1-2-1-4-1-1 0-3-3-4-4-2-1-4-2-5-3z" class="X"></path><path d="M650 599c-1-1-2-2-3-2 0-1 3 0 5 0l9 4 15 6 1 1h-1-2l1 2h-1l-3 1c0-1-2-1-2-2-2 0-4-1-6-2-1-1-2-1-4-1-1 0-3-3-4-4-2-1-4-2-5-3z" class="R"></path><path d="M655 602c2 0 4 1 6 2l3 1-1 2c-1-1-2-1-4-1-1 0-3-3-4-4z" class="X"></path><path d="M664 605l10 3 1 2h-1l-3 1c0-1-2-1-2-2-2 0-4-1-6-2l1-2z" class="AJ"></path><path d="M363 705c2 1 4 1 6 2 2-1 3 0 5 0 1 0 2 1 3 1h2l2-1v1 1c4 4 11 7 17 10s13 6 19 10c10 6 18 16 28 24l-1 1c-2-3-5-5-8-7l-16-13c-2 0-3 0-4-1-1 0-1 0-2-1l-8-3h1 2c-3-1-7-3-8-5-1 0-1-1-2-1-2-1-3-2-4-4-4-2-8-5-13-6-6-2-11-3-17-4h0c-4-1-9-1-14-1 1 0 2-1 3-1s1 1 2 0h-3v-1h0 1c1 0 2 0 3 1 1 0 1-1 2-1l1 1c1 0 2-1 3-2z" class="p"></path><path d="M395 719c4 2 10 4 13 7s8 5 12 8c-2 0-3 0-4-1-1 0-1 0-2-1l-8-3h1 2c-3-1-7-3-8-5-1 0-1-1-2-1-2-1-3-2-4-4z" class="X"></path><path d="M630 753v2c-2 1-3 2-4 3 2-1 3-1 5-1 0 1 0 3-1 4l1 3h-2l1 2h-1-1c-1-1-2 0-2 0-2 0-2 0-2 1l-1-1c-1 0-1 1-2 1-2 0-4 3-6 4l-3 2c-2 2-3 3-4 5-1 0-1 0-1-1-1 1-1 2-2 3-1 2-1 3-1 4h-1 0v-2-1-1c-2 1-2 2-3 4h-1c-1 2-1 2-2 3v-1s0-1 1-2c1-2 3-3 3-5v-1l2-3 1-1 1-2-1-1c-1 2-2 4-4 5h-1l6-6c3-2 6-7 8-9v1s-2 2-2 3c-1 1 0 2-1 3 2-2 4-6 7-7 4-4 8-6 13-8z" class="U"></path><path d="M630 753v2c-2 1-3 2-4 3s-2 1-3 1c0 1-2 2-2 3h-1v-1h-1v1c-2 3-6 5-9 8l7-9c4-4 8-6 13-8z" class="Y"></path><defs><linearGradient id="Ae" x1="426.126" y1="575.984" x2="425.434" y2="592.581" xlink:href="#B"><stop offset="0" stop-color="#160000"></stop><stop offset="1" stop-color="#400505"></stop></linearGradient></defs><path fill="url(#Ae)" d="M421 581c3-1 16-7 18-5l2 1-1 1 1 1 3-2h1 0c-1 1-6 6-8 6 0 1-3 4-4 4-1-1-2-1-3-1 0 0-3 3-3 4-2 1-4 3-6 5h-3l-1-1h-1c-3-1-6 0-10 0 1-1 2-1 3-1l-2-2h0c2-1 3-2 5-3l-1-1h0l3-2c3 0 5-1 7-4z"></path><path d="M417 586c2-1 2-1 4-1h0c-2 2-4 4-7 5l-1-1c1-1 3-2 4-3z" class="c"></path><path d="M411 587h0l3-2 3 1c-1 1-3 2-4 3l1 1-5 3-2-2h0c2-1 3-2 5-3l-1-1z" class="S"></path><path d="M417 594v-1c1-2 4-5 5-6 3-1 7-3 10-4 1-1 3-2 3-2 1 0 1 1 1 1h0l1 1c0 1-3 4-4 4-1-1-2-1-3-1 0 0-3 3-3 4-2 1-4 3-6 5h-3l-1-1z" class="n"></path><path d="M436 582l1 1c0 1-3 4-4 4-1-1-2-1-3-1l1-1c2-1 3-2 5-3z" class="S"></path><path d="M585 747l1 1 2-2 1 1c-1 1-3 2-3 3v1c-7 7-15 13-21 21-3 3-6 6-8 9s-3 6-5 8c-3 0-5 3-6 5l-1 1c0 1 0 1-1 1h-1c3-6 6-12 9-17 3-4 6-7 9-11h0l3-6 2-3c1-1 2-1 3-2l-1-2h1v1c1-1 0-1 1-1l1-1c1 0 2-1 3-2v2c-2 2-4 4-6 7l-4 5h0l12-10c3-3 5-7 8-10 0 1 0 1 1 1z" class="F"></path><defs><linearGradient id="Af" x1="343.182" y1="274.408" x2="326.515" y2="291.589" xlink:href="#B"><stop offset="0" stop-color="#0d0101"></stop><stop offset="1" stop-color="#2c0705"></stop></linearGradient></defs><path fill="url(#Af)" d="M330 297h0l-1-1h0c-2-3-2-7-2-10 1-6 4-9 6-14l1-3v-2-1h1c3 2 4 7 7 10 1 1 3 3 3 5l2 5c-1 1-3 2-3 3-1 2-1 4-1 6 0 1 0 1-1 1 0-2-1-3-3-5 1-1 1-1 1-2l-1-1v1l-2 1h-2v-1l1-1c0-2-2-2-3-3v1c0 2-2 4-2 7v1c-1 1-1 2-1 3h0z"></path><path d="M615 771h0c2 0 3-1 4-1 2-1 5-1 7-1h1c3 0 7 1 10 2v1c1 0 2 1 2 1 2 2 6 4 6 6 1 1 6 5 6 5h-2c-2-1-2-1-3 0h0c-2 0-2 0-4-1-4-2-9-4-13-6h-1c-1 0-2-1-3-1l-1 2c-3 0-6-1-8 0h-2c-3 1-7 3-10 6 0-1 0-2 1-4 1-1 1-2 2-3 0 1 0 1 1 1 1-2 2-3 4-5l3-2z" class="b"></path><path d="M625 776h-7c-1 0-2 0-3 1h-2c-1 0-1 1-2 1s-1 1-2 0c5-3 11-6 17-5 2 0 4 1 7 2 4 1 9 6 13 9h0c-2 0-2 0-4-1-4-2-9-4-13-6h-1c-1 0-2-1-3-1z" class="C"></path><path d="M347 408h0v-4l-1-6c1-1 2 0 4 0v4h1c1 1 2 2 3 4 4 6 9 10 14 16 1 1 5 7 5 8 1 2 3 4 4 6l1 1v1c-5-3-8-7-13-11-2 0-3-1-4-2l-1 2-2-2-5-4-7-8c-1-3-2-5-2-7h0c1 1 1 2 2 3s1 2 2 3l1 1h0l-1-1v-1h0l-1-3z" class="V"></path><path d="M347 408h0v-4l-1-6c1-1 2 0 4 0v4h1l1 6v3l-1-2h-1v-1c-1-1-1-2-2-3h0 0v3h-1z" class="j"></path><path d="M351 402c1 1 2 2 3 4 4 6 9 10 14 16 1 1 5 7 5 8 1 2 3 4 4 6l1 1v1c-5-3-8-7-13-11l-6-8c-1-1-3-2-4-4v-1c-1-2-2-3-2-5l-1-1-1-6z" class="J"></path><path d="M351 402c1 1 2 2 3 4 4 6 9 10 14 16 1 1 5 7 5 8-2-2-5-4-7-6s-4-3-5-5c-3-3-5-7-8-10l-1-1-1-6z" class="h"></path><path d="M471 692c2 2 3 4 5 6 1 2 2 3 2 5h1c0 1 0 2 1 3 0 0 0 1 1 1 3 5 4 11 7 15h-1l-1 1c-2 0-3-1-5-2-4 0-9-4-13-7v-1-1c-1-2-2-4-2-7l1-1v-1h-2l-1-2c0-2 2-3 3-4 2-1 3-3 4-5z" class="n"></path><path d="M592 780c2 0 3-1 4 0-2 2-4 5-5 8-3 4-5 11-10 12h-1l-11 8c-6 6-11 13-17 19-1 2-3 4-4 6-1 1-4 6-5 7-2 0-2 0-3 1h0c-1-1-1-2-2-3 2-2 3-3 4-5 1 0 2-1 3-1v-1c2-2 6-6 7-8 1-3 4-3 5-6 0-1 0-1 1-2v-1h-1l11-11c1 0 3-2 3-2 8-7 15-14 21-21z" class="AP"></path><path d="M558 814l10-9h1c3-2 5-5 8-6 2-1 5-4 6-5-4 5-8 8-13 12l-10 11c-1 0-1 1-2 2-1 2-6 5-6 8-1 2-3 4-4 6-1 1-4 6-5 7-2 0-2 0-3 1h0c-1-1-1-2-2-3 2-2 3-3 4-5 1 0 2-1 3-1v-1c2-2 6-6 7-8 1-3 4-3 5-6 0-1 0-1 1-2v-1z" class="b"></path><path d="M452 782h3l5 5 5 5c1 1 0 0 2 0 1 1 1 2 2 3 0 1 1 1 1 2 1 1 3 3 4 3h2c1 1 1 2 2 3 1 2 2 3 3 5 1 3 3 10 3 13l-1 1c-3-1-2-4-4-5-2 0-3-1-5 0l-1-1 1-1c0-1-2-2-2-2h-3l1-1v-1c0-2 0-2-2-3h1l-3-3v-1c0-1-2-1-3-2s-2-3-3-2c-1 0-2-1-3-1-1-1-1-2-2-3v-3l3 2h1v-1l1 1 1-2c-1-1-1-1-2-1-2-2-4-3-5-5l1-1c-2-1-2-3-3-4z" class="C"></path><path d="M455 793l3 2h1v-1l1 1c4 3 9 7 12 11 1 1 0 1 1 1 2 1 2 3 4 5h-1c-2-1-4-2-5-4-1-1 0-1-1-1-2-1-3-2-4-3 0-1-2-1-3-2s-2-3-3-2c-1 0-2-1-3-1-1-1-1-2-2-3v-3z" class="l"></path><path d="M408 519v-2-2l2 4h0c1 4 2 7 4 11s5 8 8 12l2 1c0 1 1 2 2 2h0c3 2 6 4 9 5v1h-2 0c-1 1 0 1-1 1l-4-1h-1l-2-1h-1l-1-1c-2 0-2 0-2-1l-2-1v-2c-1-1-2-1-2-2l-1 1c1 0 1 0 1 1 0 2-1 1-1 3-1 0-1 3 0 4v2h0c1 1 1 1 1 2 1 0 1 1 2 1 1 1 1 1 1 2 1 1 3 2 5 2 1 1 0 1 2 1 1 2 2 4 4 4h1 0 1l5 1h-4l-7 1c-1 0-1-1-1-2 1 0 1 0 1-1l-2-2c-3-1-4 1-6-1v-2c0-1-1-1-2-2 0-1-1-3-2-4l-2-3c-3-6-4-12-5-19h-1-1c0-2-1-3-2-4l1-6c1-1 1-2 3-3z" class="T"></path><path d="M417 541c2 0 3 0 4 2l1-1 2 1c0 1 1 2 2 2h0c3 2 6 4 9 5v1h-2 0c-1 1 0 1-1 1l-4-1c-4-2-7-4-10-8l-1-2z" class="p"></path><path d="M422 542l2 1c0 1 1 2 2 2h0v2c-1-1-1-1-2-1-2 0-2-2-3-3l1-1z" class="M"></path><path d="M408 519v-2-2l2 4h0c1 4 2 7 4 11s5 8 8 12l-1 1c-1-2-2-2-4-2l1 2c-4-2-6-6-8-11l-2-2h0v2h-1-1c0-2-1-3-2-4l1-6c1-1 1-2 3-3z" class="F"></path><path d="M405 522c1-1 1-2 3-3l2 6h-1c-1 0-1 0-2-1v8h-1c0-2-1-3-2-4l1-6z" class="B"></path><path d="M408 519v-2-2l2 4h0c1 4 2 7 4 11s5 8 8 12l-1 1c-1-2-2-2-4-2-3-5-6-11-7-16l-2-6z" class="AA"></path><path d="M592 818c0-1 0-1 1-2l1-1v-2h1l1 3h0l2-2h-1c1-2 0-1 2-2l1 1h1c0 1 1 1 2 2v1 1 1c0 1 1 3 1 5h1v1c0 1 1 1 1 2h0c0 2 1 2 2 3s2 1 3 2l3 2c-1 1-1 2-2 2h-2v-1-1l-1 1-1 1-2-2-1 1c-2-1-1-1-2-2l-1-1c0 2 0 1-1 2 0-1-1-2-2-2v1h1l-1 1c-1-1-2-2-3-2v1h-1-1l-2-1c-1 1-1 2-1 3s0 1-1 2v1h-1l-1-2c-2-2-3-5-4-8v-1c0-2 1-3 1-5h0c1-1 1-2 2-3l1-1c0-1 0-1 1-2h2c1 1 1 1 1 3z" class="N"></path><path d="M602 829l1-1v-2h-2l-1-3c-1-2-1-4-1-6l1-1v1l3 6h0c0 3 3 5 4 7 1 1 2 1 4 1l3 2c-1 1-1 2-2 2h-2v-1-1l-1 1c-1-1-1-2-3-2l-1-1h-1c-1-1-2-1-2-2z" class="l"></path><path d="M592 831c0-1 1-1 1-2 1 0 1-1 1-1h-1v-4l1-1h1v1l1 1v-1-1h0 1c1 1 2 2 2 3v1l3 2c0 1 1 1 2 2h1l1 1c2 0 2 1 3 2l-1 1-2-2-1 1c-2-1-1-1-2-2l-1-1c0 2 0 1-1 2 0-1-1-2-2-2v1h1l-1 1c-1-1-2-2-3-2v1h-1-1l-2-1z" class="q"></path><path d="M591 815c1 1 1 1 1 3-1 2 0 2 0 4-2 2-1 3-1 6 0 2-1 3-2 5l1 1-1 1h-1c-2-2-3-5-4-8v-1c0-2 1-3 1-5h0c1-1 1-2 2-3l1-1c0-1 0-1 1-2h2z" class="C"></path><defs><linearGradient id="Ag" x1="358.591" y1="304.554" x2="346.969" y2="312.212" xlink:href="#B"><stop offset="0" stop-color="#180201"></stop><stop offset="1" stop-color="#2a0806"></stop></linearGradient></defs><path fill="url(#Ag)" d="M342 296c1 0 1 0 1-1 0-2 0-4 1-6 0-1 2-2 3-3 4 6 8 12 11 19 1 2 1 4 2 6s2 4 2 6h1v-1c1-1 0-1 1-1 1 2 2 4 1 6v2l-2 4v1c-2-1-4-4-5-6-1-1-1-2-2-3-1-2-3-4-5-5-1 0-2 0-4-1h-1l-2-10c0-2-1-5-2-7z"></path><path d="M334 387h0c2 2 5 4 6 7v2l1 4v3h-1c-1-1-1-1-2-3v6h-3l-2 6c0 1-1 2-1 3-1 2-3 4-4 6v-2l-2 2-1 1c0-1 0-2 1-3-1-2-1-3-1-5v-4c-1-1-1-4-2-5 0-3 0-6-1-8-1-1-1-1-2-1l-2 2v1-6h0c1-2 2-3 3-4 3 0 4-1 6-2l1 2c1 0 1-1 2 0v1 4 1l1-1c0-1 1-3 2-4h0c1-1 1-2 1-3z" class="R"></path><path d="M324 391h2c1 1 1 2 1 4l-2 1v-1-1l-1-3z" class="L"></path><path d="M327 387l1 2c1 0 1-1 2 0v1 4 1c-1-1-1-1-2-1v1c0 1 0 1-1 2 0 1 1 1 1 2l-1 1h0v-5c0-2 0-3-1-4h-2c-1-1-2-1-3-2 3 0 4-1 6-2z" class="T"></path><path d="M327 400l1-1c0-1-1-1-1-2 1-1 1-1 1-2v-1c1 0 1 0 2 1 0 2 1 3 0 5v4l-1 6h-1l-1-5v-5z" class="j"></path><path d="M330 404l1 1c1-3 1-4 3-6l-1 5 2 1v1l-2 6c0 1-1 2-1 3-1 2-3 4-4 6v-2l-2 2c0-1 0-3 1-4v-2l1-1v-4h1l1-6z" class="T"></path><path d="M333 404l2 1v1l-2 6c0 1-1 2-1 3-1 2-3 4-4 6v-2c2-5 4-10 5-15z" class="s"></path><path d="M334 387h0c2 2 5 4 6 7v2l1 4v3h-1c-1-1-1-1-2-3v6h-3v-1l-2-1 1-5c-2 2-2 3-3 6l-1-1v-4c1-2 0-3 0-5l1-1c0-1 1-3 2-4h0c1-1 1-2 1-3z" class="f"></path><path d="M335 405c1-2 1-4 1-7l1-1 1 1v2 6h-3v-1z" class="AE"></path><path d="M334 387c2 2 5 4 6 7v2l1 4v3h-1c-1-1-1-1-2-3v-2c0-3-1-6-3-9-1 0-1-1-1-2z" class="j"></path><path d="M334 387h0c0 1 0 2 1 2l-1 10c-2 2-2 3-3 6l-1-1v-4c1-2 0-3 0-5l1-1c0-1 1-3 2-4h0c1-1 1-2 1-3z" class="W"></path><path d="M288 515l1-3v1c0 1 1 3 2 4v1 2l2 5v2h1v3c1 1 1 1 1 2v1l1 1v1c0 4 0 8 2 13v4c1-2 1-4 1-6 0-1 0-2 1-2v-2h1v1c0 2-1 3-1 5l2 2h0v6l1 1c0-1 1-2 1-4 1-1 1-1 1-2 1 0 2 1 3 1 2 0 1 0 3 1l1-1 1-1v1l1 2s0-1 1-2c1 3 1 4 1 7v1 2 1c-1 0-1 0-1 1v1 1h0v-1l1 1-1 1h-2v1 2c0 2 0 4-1 5 0 1-1 1-2 1l-1-3c-1-1-2-2-2-3h0l-1-1c-1 0-2-2-2-3 0-2 1-4 1-6h0c-1 1-1 2-1 2-1 2-1 2-2 2s-1-1-2-1c-1-2-1-3-1-4l-1-1-1 1c-1-2-1-3-2-5h1c0-5-2-11-3-15l-5-24z" class="U"></path><path d="M313 568v2c0 2 0 4-1 5 0 1-1 1-2 1l-1-3c0-1 0 0 1-1v-1 2h1c1-1 1-3 1-4l1-1z" class="d"></path><path d="M302 550v6l1 1-1 1c0 1 0 2-1 3h0c0-1 0-2-1-3v-2c0-2 1-4 2-6z" class="N"></path><path d="M312 552l1-1v1l1 2c0 3 0 4-1 7-2 3-1 7-4 9h-1c0-2 1-3 1-5v-1h-1 0l-2-2v-2c1-2 1-3 1-5 1-1 1-2 1-2v-1c2 0 1 0 3 1l1-1z" class="P"></path><path d="M308 553c1 1 3 2 3 4-1 2-1 3-2 5l-1 2-2-2v-2c1-2 1-3 1-5 1-1 1-2 1-2z" class="Z"></path><path d="M307 555c1 1 2 2 2 3s-2 3-2 4h-1v-2c1-2 1-3 1-5z" class="N"></path><path d="M393 746h1c1 0 1 1 2 2l3 2 1 1h1c1 2 2 3 4 4 1 0 1 0 2-1 0-1 0-1 1-2 0-2 0-3 1-4l2 1c1 1 2 1 4 2 1 0 3 1 4 2h2l1 1c1 0 0 0 1 1 1 0 2 1 2 1h1c1 2 3 3 4 4l3 5c1 1 2 3 3 3 1 1 1 3 3 4v-1h2l1 3v1 1l-1 2c-1 0-1 0-2-1h1c-1-1-1-1-1-2v-1h-1l-1 1c0 1 0 1-1 1l-2-3h-3-2c-2-2-5-2-8-2-2-1-5-5-6-6-7-6-16-12-22-19z" class="Y"></path><path d="M411 749c1 1 2 1 4 2 1 0 3 1 4 2h2l1 1c1 0 0 0 1 1 1 0 2 1 2 1 0 1-1 2-2 3v1c-1-1-1-1-2-1-1 1-2 0-3 0s-2 0-3 1h0c-1 0-2-1-3-2 1 0 1-1 1-2l-2 2c-1-1-2-2-2-3l1-1h0v-1l-1-1v-1h1l-1-2h2z" class="d"></path><path d="M425 756h1c1 2 3 3 4 4l3 5c1 1 2 3 3 3 1 1 1 3 3 4v-1h2l1 3v1 1l-1 2c-1 0-1 0-2-1h1c-1-1-1-1-1-2v-1h-1l-1-1c-1-1-2-1-4-1l-5-2c-2 0-3-1-4-2l-2-1c-2-1-3-2-5-4v-2l-2-1c1-1 2-1 3-1s2 1 3 0c1 0 1 0 2 1v-1c1-1 2-2 2-3z" class="U"></path><path d="M434 771h2c1 1 2 1 2 1 2 0 2 0 3 1l1 2h0v1l-1 2c-1 0-1 0-2-1h1c-1-1-1-1-1-2v-1h-1l-1-1c-1-1-2-1-4-1l1-1z" class="d"></path><path d="M415 760c1-1 2-1 3-1s2 1 3 0c1 0 1 0 2 1l2 1-1 2h1v1l3 3h1v-2h1l2 1h0 0c-1 0-2 1-3 2l-1 1h2c1 1 3 1 4 2l-1 1-5-2c-2 0-3-1-4-2l-2-1c-2-1-3-2-5-4v-2l-2-1z" class="N"></path><defs><linearGradient id="Ah" x1="656.19" y1="678.212" x2="666.194" y2="686.611" xlink:href="#B"><stop offset="0" stop-color="#7e1114"></stop><stop offset="1" stop-color="#b11f20"></stop></linearGradient></defs><path fill="url(#Ah)" d="M701 644h-1v2c1-1 0-1 2-1h0l-19 23c-15 17-33 32-53 43l-2-1c5-3 9-7 14-10 10-8 20-16 27-27v-1l1-1c4-1 5-3 7-6l3-4c3-1 5-3 6-6h1c-1 2-1 2 0 4 3-3 7-6 9-10l5-5z"></path><path d="M686 655h1c-1 2-1 2 0 4-3 2-5 5-8 8l-3 3-4 4c-1 0-2-1-3-1v-1l1-1c4-1 5-3 7-6l3-4c3-1 5-3 6-6z" class="i"></path><defs><linearGradient id="Ai" x1="405.786" y1="440.584" x2="376.714" y2="448.916" xlink:href="#B"><stop offset="0" stop-color="#460602"></stop><stop offset="1" stop-color="#6d1113"></stop></linearGradient></defs><path fill="url(#Ai)" d="M370 421c3 2 5 5 8 7l5 5c2 1 3 1 5 2 3 2 5 6 9 8 1 1 1 1 2 1v-1h-1c-1-2-1-3-2-3l-1-1-1-1-1-1c-1 0-1-1-1-1v-1l3 3c2 1 3 3 4 5h1c0 1 0 1 1 2 0 2 2 5 4 7 3 4 6 8 8 12l1 4-10-9c-1-2-3-4-5-5l-3-2v1l1 1v1l-2-1h0c-2 0-5-2-6-3l-1-1c-2-1-2-2-2-4-1-2-6-6-8-8v-1l-1-1c-1-2-3-4-4-6 0-1-4-7-5-8 1-1 1-1 2-1z"></path><defs><linearGradient id="Aj" x1="351.247" y1="606.594" x2="344.187" y2="596.814" xlink:href="#B"><stop offset="0" stop-color="#cb3334"></stop><stop offset="1" stop-color="#dd5a64"></stop></linearGradient></defs><path fill="url(#Aj)" d="M376 585c1 1 1 2 1 3l-1 1 2 1h1c-19 11-38 21-60 21-1 0-3 0-3 1l-1-1h-1 0l-3 3-1-3h0v-2l-3-4 1-1-1-1v-1c0-2-4-6-5-8l1-1c3 3 6 8 9 10 1 0 2 0 3 1 2 1 8 1 10 1h1c2 0 4-1 6-2 1-1 9-2 11-2 6-1 12-4 17-6l2 1c2-2 6-3 8-5 2-1 4-4 6-6z"></path><path d="M302 594l1-1c3 3 6 8 9 10 6 4 14 4 20 3 1-1 2-1 3 0-3 2-6 2-10 2h0l-1-1c-1 0-2 0-3 1l2 1c-3 0-5-1-7-1-3-1-5-2-8-4l-1-1v-1c0-2-4-6-5-8z" class="z"></path><path d="M360 595l2 1c-6 2-12 5-18 7-2 1-7 2-9 3-1-1-2-1-3 0-6 1-14 1-20-3 1 0 2 0 3 1 2 1 8 1 10 1h1c2 0 4-1 6-2 1-1 9-2 11-2 6-1 12-4 17-6z" class="D"></path><path d="M288 562v-2c0-1 0-1-1-3-1-1-1-2-1-3-1-1-1-1-1-2l-1-1 1-2c5 12 13 25 23 33 2 2 10 7 10 7h-1l1 1c1 0 1 1 2 2v2h-4l-2-1c-2 0-2 0-3 2 2 2 4 4 7 5h0c3 2 6 3 10 3h4c-2 1-4 2-6 2h-1c-2 0-8 0-10-1-1-1-2-1-3-1-3-2-6-7-9-10 0-2-2-5-3-7-5-8-10-15-13-24h1z" class="B"></path><path d="M312 590l-4-3c0-1 0-1-1-2 1-1 1 0 1-1l9 7h-5v-1z" class="K"></path><path d="M300 581l1-1c3 3 5 7 8 10h3v1l2 2c-2 0-2 0-3 2-4-4-7-9-11-14z" class="m"></path><defs><linearGradient id="Ak" x1="302.079" y1="590.805" x2="307.287" y2="585.984" xlink:href="#B"><stop offset="0" stop-color="#230807"></stop><stop offset="1" stop-color="#440909"></stop></linearGradient></defs><path fill="url(#Ak)" d="M303 593c0-2-2-5-3-7-5-8-10-15-13-24h1l9 14c0 1 2 4 3 5 4 5 7 10 11 14 2 2 4 4 7 5h0c3 2 6 3 10 3h4c-2 1-4 2-6 2h-1c-2 0-8 0-10-1-1-1-2-1-3-1-3-2-6-7-9-10z"></path><defs><linearGradient id="Al" x1="539.099" y1="873.317" x2="542.519" y2="912.498" xlink:href="#B"><stop offset="0" stop-color="#101012"></stop><stop offset="1" stop-color="#444348"></stop></linearGradient></defs><path fill="url(#Al)" d="M536 874h0c1 0 1 0 2-1 1 0 1 1 2 1 1 1 1 1 2 1s2 1 2 1c2 1 4-1 6 0v2c-5 9-10 20-7 31l1 3h-1v1l-1-1v1l-1 1c-2-1-2-1-2-3h0v1h-1v-3h-1l-2-3c-1-1-2-2-2-4h1l-1-1c-1 0-2 0-3 1 1 1 1 2 1 3h-1c-1-2 0-3 0-5-1-2 0-7 0-9 2-6 3-12 6-17z"></path><path d="M283 207h0l1-1 2-2c1-4 3-9 6-11l-3 7c0 1-1 2-1 3v8 1 1 2c1-2 1-6 2-8l1 1c2 1 3 2 4 3l-1 1h1c-2 1-3 4-4 6 0 4 1 7 3 10 0 1 0 0-1 2 0 2-1 4-2 6h0c-1 3-3 7-4 11l-1-2c-1-3-2-5-3-7-1-1-2-3-3-4h0c0-2-1-4-2-5 1-2 1-4 1-5l-1 3c-1-1-1-2-1-2l1-2-1-1-2 2h0l-1-1 2-2c3-5 4-10 7-14z" class="B"></path><path d="M286 208c0-2 1-3 2-5v8 1 1 2c0 1-1 2-1 4-1 1-1 2 0 3-2 2-2 5-4 7v1c-2-6 1-10 2-15v-2c1-1 1-3 1-5z" class="E"></path><path d="M283 207h0l1-1 2-2c1-4 3-9 6-11l-3 7c0 1-1 2-1 3-1 2-2 3-2 5-3 5-5 11-7 16l-1 3c-1-1-1-2-1-2l1-2-1-1-2 2h0l-1-1 2-2c3-5 4-10 7-14z" class="AF"></path><path d="M288 215c1-2 1-6 2-8l1 1c2 1 3 2 4 3l-1 1h1c-2 1-3 4-4 6 0 4 1 7 3 10 0 1 0 0-1 2 0 2-1 4-2 6h0c-1 3-3 7-4 11l-1-2c-1-3-2-5-3-7-1-1-2-3-3-4h1c1 0 1 0 1 1l1-1h1 1 0c1 1 1 1 2 1v-1-2l1-1v-1-3l1-1v-3c-1-1-1 0-2-1s-1-2 0-3c0-2 1-3 1-4z" class="i"></path><path d="M287 234c1-1 2-1 2-2 0-2 0-4 1-5 1 2 0 4 0 5s0 2 1 3v1c-1 3-3 7-4 11l-1-2c-1-3-2-5-3-7-1-1-2-3-3-4h1c1 0 1 0 1 1l1-1h1 1 0c1 1 1 1 2 1v-1z" class="K"></path><path d="M280 234h1c1 0 1 0 1 1l1-1h1 1 0c1 1 1 1 2 1 0 3 0 8-1 10-1-3-2-5-3-7-1-1-2-3-3-4z" class="e"></path><path d="M458 684h4l1 1 2 2c1 0 1-1 1-2 2 3 3 5 5 7-1 2-2 4-4 5-1 1-3 2-3 4l1 2h2v1l-1 1c0 3 1 5 2 7v1 1c-7-1-13-8-19-12l-4-5-1-1-1-1c3-3 4-7 8-9 1 0 1 1 2 1s2-1 3-2l2-1z" class="L"></path><path d="M458 689v-2l1-1c1 2-1 0 1 2l1 1c0 2 0 3 1 5l1 2h-2c-1-3-3-4-3-7z" class="j"></path><path d="M443 695c3-3 4-7 8-9 1 0 1 1 2 1h0l-4 3c-2 2-4 4-5 6l-1-1z" class="V"></path><path d="M466 685c2 3 3 5 5 7-1 2-2 4-4 5h0l1-2c-1-1-2 1-4 1h-1l-1-2c-1-2-1-3-1-5l1-1 3 2v-2-1c1 0 1-1 1-2z" class="I"></path><path d="M465 688c1 1 2 2 3 4-1 2-2 2-3 3-1-2 1-3 0-5v-2z" class="n"></path><path d="M462 688l3 2c1 2-1 3 0 5h-1l-2-1c-1-2-1-3-1-5l1-1z" class="o"></path><path d="M458 684h4l1 1 2 2v1 2l-3-2-1 1-1-1c-2-2 0 0-1-2l-1 1v2c-1 1-2 1-3 3-1 1-2 3-3 4-1 0-2 1-3 2 0-1-1-1-2-2 0-1 3-3 4-5h0c1-1 3-2 4-3v-1h-2 0c1 0 2-1 3-2l2-1z" class="Q"></path><path d="M463 685l2 2v1 2l-3-2c0-1 0-2 1-3z" class="c"></path><path d="M697 604c3 1 7 1 10 2 1 0 3 0 4 1h2l2 1v-2 1c1 1 1 1 2 1-1 2-1 3-1 4 0 2-1 4-1 6-1 8-2 17-7 23-2 1-4 3-6 4h0c-2 0-1 0-2 1v-2h1v-1c0-2 0-4 1-6v-2-6-4l-1-2-1-1c-1 0-1-1-2-2l-1-4c-1-1-1-1-1-2v-1h-1v1c-2-2-3-3-4-5 2-1 4-1 7-1v2c-1 0-1 0-1 1h3 2v-1h0c1 1 2 1 4 1 1 0 1-1 1-1-1-1-1-2-3-3h0l-1-1c-3 0-4 0-6-2z" class="p"></path><path d="M705 631c1 0 0 0 1-1l1-1c1 1 1 1 2 0h1c-3 5-5 10-9 14 0-2 0-4 1-6h1c1-2 2-4 2-6z" class="S"></path><path d="M712 615c1 5-1 10-2 14h-1c-1 1-1 1-2 0l-1 1c-1 1 0 1-1 1v-1l1-2c1-2 1-4 1-7h1l1 1v-1c1 0 2-1 2-1v-1c1-1 1-2 1-4z" class="W"></path><path d="M705 621h2c0 3 0 5-1 7l-1 2v1c0 2-1 4-2 6h-1v-2-6-4l-1-2 1-1v2l3-3z" class="E"></path><path d="M705 621h2c0 3 0 5-1 7l-1 2c-1 0-1-1-1-1l1-3-3-2 3-3z" class="J"></path><path d="M697 604c3 1 7 1 10 2 2 1 3 2 4 5l1 4c0 2 0 3-1 4v1s-1 1-2 1v1l-1-1h-1-2l-3 3v-2l-1 1-1-1c-1 0-1-1-2-2l-1-4c-1-1-1-1-1-2v-1h-1v1c-2-2-3-3-4-5 2-1 4-1 7-1v2c-1 0-1 0-1 1h3 2v-1h0c1 1 2 1 4 1 1 0 1-1 1-1-1-1-1-2-3-3h0l-1-1c-3 0-4 0-6-2z" class="c"></path><path d="M706 619l2-2c1 2 1 3 1 4v1l-1-1h-1-2l1-2z" class="O"></path><path d="M700 622c0-1 1-2 3-4l3 1-1 2-3 3v-2l-1 1-1-1z" class="R"></path><path d="M696 613c3-1 6-1 9-1v2h0c-2-1-6 0-9 0v-1z" class="K"></path><path d="M696 614c3 0 7-1 9 0-3 2-5 3-7 6l-1-4c-1-1-1-1-1-2z" class="T"></path><path d="M309 270c-2-10-1-21 1-31 0-2 1-5 2-6 1 1-2 12-2 15 0 8 1 16 2 24 2 7 3 15 5 22 4 16 9 30 16 45 3 7 7 13 10 19 1 2 2 5 3 7 1 1 2 2 2 3-1 0-1 0-2-2l-1-1v-1c-1-1-1-1-1-2l-1-2h-1c0 1 0 1 1 2v1l1 2 1 2 2 3v1 1c-2-2-3-5-4-7l-7-14c0 1 0 2-1 3v1c-5-8-9-17-14-25 1-2 1-3 1-4l-1-1c0-1 0-2 1-3 0-1-9-17-10-19 1 1 2 1 3 3 0 1 1 3 2 4h0c0-7-3-14-5-20l-3-20z" class="H"></path><path d="M322 322c2 3 14 26 14 29 0 1 0 2-1 3v1c-5-8-9-17-14-25 1-2 1-3 1-4l-1-1c0-1 0-2 1-3z" class="s"></path><defs><linearGradient id="Am" x1="561.39" y1="640.638" x2="586.158" y2="661.402" xlink:href="#B"><stop offset="0" stop-color="#130101"></stop><stop offset="1" stop-color="#2a0806"></stop></linearGradient></defs><path fill="url(#Am)" d="M574 624c2 1 3 3 4 5l4 10 1 3c1 4 0 7-1 10-1 9-4 18-8 26h-1l-2-2h0l-1 2v-31c0-4 0-9 1-14 0-1 0-3 1-5h0l1-1h0c1-2 1-2 0-3h1z"></path><path d="M341 392c1 4 1 9 3 14 0 2 1 4 2 7l7 8c0 1 1 2 2 4h-1c-2-1-3-2-4-3l1 3-1 1v1h-1-2v3l4 1 3 1 2 2-2 2c-2-1-4-3-7-3-2 2-3 2-5 3h-2-1c-1 0-3-1-4-1-1-1-2-1-3-1h-1c-1 0-1-1-2-2h-1c0-1 1-2 2-3 1 0 2-1 3-1-1-1-1-1-1-2-1-1 0-2 1-3l-1-1c0-2 2-5 3-7-1-1-1-2-2-3l2-6h3v-6c1 2 1 2 2 3h1v-3l-1-4c1-1 1-3 1-4z" class="D"></path><path d="M339 421l2-1 2 2v1c-1 1-2 1-3 1l-1-1v-2z" class="a"></path><path d="M342 427c1-1 2-1 3-1h1l-1-1c-1-2-1-2-1-4 2 2 3 3 5 6h-2v3c-2-1-3-2-5-3zm2-11l1-1c1 1 1 1 1 2l4 5 1 3-1 1c-3-3-4-6-6-10z" class="h"></path><path d="M335 406h3l-1 2c0 2-1 5-2 7-1-1-1-2-2-3l2-6z" class="AI"></path><path d="M341 392c1 4 1 9 3 14 0 2 1 4 2 7l7 8c0 1 1 2 2 4h-1c-2-1-3-2-4-3l-4-5c0-1 0-1-1-2l-1 1-1-1c0 1 0 2-2 3h-2l-3 3-1-1h1c-1-1 0-1-1-2l1-2c0-1 1-2 2-3v-1l-1-4 1-2v-6c1 2 1 2 2 3h1v-3l-1-4c1-1 1-3 1-4z" class="f"></path><path d="M341 400c1 2 0 8 0 11v1c-1-1-1-2-1-3h-1v1c0 1-1 1-1 2l-1-4 1-2v-6c1 2 1 2 2 3h1v-3z" class="i"></path><path d="M343 415v-3c-1-2-1-3-1-4l1-1c1 2 1 3 2 5 0 1 0 1 1 2v-1l7 8c0 1 1 2 2 4h-1c-2-1-3-2-4-3l-4-5c0-1 0-1-1-2l-1 1-1-1z" class="G"></path><path d="M333 423h0 1 2 0v1h1l2-3h0v2l-2 2h-1 0l-2 1h1 0c2 0 3-1 5 0h1l1 1c2 1 3 2 5 3l4 1 3 1 2 2-2 2c-2-1-4-3-7-3-2 2-3 2-5 3h-2-1c-1 0-3-1-4-1-1-1-2-1-3-1h-1c-1 0-1-1-2-2h-1c0-1 1-2 2-3 1 0 2-1 3-1-1-1-1-1-1-2-1-1 0-2 1-3z" class="f"></path><path d="M329 432c1-1 3-2 5-2l2 2 1 1c-1 0-1 1-3 1l-1-1-1 1h-1c-1 0-1-1-2-2z" class="Ad"></path><path d="M333 628h1 3l1 1v2h1c1 0 2 0 4-1h0l1 1c-2 2-2 2-2 5 1 0 2 0 3 1h1c0 2-1 2-1 3s1 1 1 2c1 1 1 5 1 7l2 5 5 8c-1 1-1 1-1 2 2 4 5 7 7 10 0 0-2 1-3 1l-5-5c-2-1-3-3-5-3l-3-3-1-1h0c-1-1-2-2-3-2s-2-3-3-4l-1-1c-2-3-4-5-5-7 0 0 2 1 2 2h1v-1c-1-1-1-2-1-3-1-2-1-4-2-6-1-5 0-8 2-13z" class="I"></path><path d="M342 644v2h-1l-1-2c1-1 0-1 1-2h-2v-1c-2 0-2 0-3-1l1-2c2 0 2 0 3 1l3 3-1 2zm4 21c1-1 1-2 1-3l-2-2v-2c-1-1 0-3-1-5v-1c0-1 0-1-1-2v-1-1l1-1 1 1 1 2v-1h1l2 5-3 2c1 1 1 3 2 4v1c0 1-1 2-1 3l-1 1zm-15-24h3v-2c0 3 2 5 4 7l1 2-1 1c0 1 0 3 1 4v1c-2-1-3-3-4-5l-2-2c-1-2-1-4-2-6z" class="H"></path><path d="M343 642c1-5-2-3-4-6h-1c-1-1-1-1-1-2 1 0 2-1 3-1h1v1l-1 1 1 1h1c1 0 2 0 3 1h1c0 2-1 2-1 3s1 1 1 2c1 1 1 5 1 7h-1v1l-1-2h0c0-2-1-3-3-4l1-2z" class="S"></path><path d="M348 660c-1-1-1-3-2-4l3-2 5 8c-1 1-1 1-1 2 2 4 5 7 7 10 0 0-2 1-3 1l-5-5c-2-1-3-3-5-3l-3-3-1-1h0c-1-1-2-2-3-2s-2-3-3-4c2 1 3 3 5 3v-1c2 2 3 4 4 6l1-1c0-1 1-2 1-3v-1z" class="O"></path><path d="M348 660c-1-1-1-3-2-4l3-2 5 8c-1 1-1 1-1 2h-1l-1 1c1 1 1 2 1 3h0c-1-1-2-3-2-4l-2-3v-1z" class="E"></path><path d="M348 660l1-1 1 1v4l-2-3v-1z" class="V"></path><path d="M714 670c0 2 0 4 1 6h0l2 17c1-1 2-1 3-2l1 3 1 4v1c0 2 1 2 0 4h-1l-1 1 1 1h-1l-1 1c4 8 8 16 13 24l-2-1c-2-1-3-2-4-4v-1l-1 1c0 1 1 2 1 4h0l-2-2c0-1-1-3-2-4h0v-1c0-2-1-3-2-5h0c-2-3-4-6-7-7h0c-1-1-1-1-2-1-1-2-3-3-4-5l-2-1c-1 0-1-1-2-1 1-1 2-1 4-1l-1-2-3-2h-2c-1 0 0 0-1-1h1v-1l-3-1c-3-1-5-1-7 0h-2l-1 1h-3c3-2 4-3 6-6 0-1 0-1-1-2h-6c2-1 4-1 6-1 3 0 4-1 7-2l1 1c1 1 1 1 2 1 2 0 3-1 4-1l1-1c1 1 1 1 2 1 1 1 1 0 3 1 1 1 2 3 2 5-1 0-2 1-3 1h-1v1c1 1 2 1 2 2 3 1 4 3 6 5v-2c1-1 1 0 1-1-1-1-2-2-2-3l-2-1h1 1l1 1 1-1h-1v-2c-1-1-1-2-1-3v-4c0-1-1 0-1-1v-5c-1-1 0-2 0-3l-1-1c0-1 0-2 1-4h0z" class="c"></path><path d="M714 699l2 2h-2c0 1 1 3 2 5l-1 1-1 1c-1-1-1-1-2-1v1l1 2c-1-1-1-1-2-1-1-2-3-3-4-5v-1h1c2 1 3 1 4 3h1c0-2-1-4-2-6h0c0-1 1-1 3-1z" class="I"></path><path d="M717 693c1-1 2-1 3-2l1 3 1 4v1c0 2 1 2 0 4h-1l-1 1 1 1h-1l-1 1v-3l-2-10z" class="AG"></path><path d="M708 695l6 4c-2 0-3 0-3 1h0c1 2 2 4 2 6h-1c-1-2-2-2-4-3h-1v1l-2-1c-1 0-1-1-2-1 1-1 2-1 4-1l-1-2c1 0 1 0 1-1v-1c0-1 1-1 1-2z" class="K"></path><path d="M697 687c1-1 5-1 7 0 2 0 4-2 6 1 1 1 1 1 1 2h-1v1h-1l-1 1c-1 1 0 2 0 3h0c0 1-1 1-1 2v1c0 1 0 1-1 1l-3-2h-2c-1 0 0 0-1-1h1v-1l-3-1c-3-1-5-1-7 0h-2l-1 1h-3c3-2 4-3 6-6 0-1 0-1-1-2h6 1z" class="Q"></path><path d="M705 692c0-1-1-2 0-3 0-1 1-1 2-1s1 2 3 2v1h-1l-1 1c-1 1 0 2 0 3-1 0-1-1-2-1 1-1 1-1 1-2h-2z" class="S"></path><path d="M697 687c1 2 3 1 5 3h-1c-1 1-3 1-5 1-1 1-3 1-5 2h-1c0-2 2-3 3-4l3-2h1z" class="G"></path><path d="M705 692h2c0 1 0 1-1 2 1 0 1 1 2 1h0c0 1-1 1-1 2v1c0 1 0 1-1 1l-3-2h-2c-1 0 0 0-1-1h1v-1l-3-1 7-2z" class="V"></path><path d="M703 697v-1l4 1v1c0 1 0 1-1 1l-3-2z" class="S"></path><path d="M372 581l1-1c2 0 3 1 4 1h0v1h3 0c-1 1-3 3-4 3-2 2-4 5-6 6-2 2-6 3-8 5l-2-1c-5 2-11 5-17 6-2 0-10 1-11 2h-4c-4 0-7-1-10-3h0c-3-1-5-3-7-5 1-2 1-2 3-2l2 1c2 1 2 1 4 1 1 0 1 0 2 1 1 0 1-1 3-1 0 0 0 1 1 1l2-2s0-1 1-1h3c3 0 5 0 7-1 11-2 20-5 30-10l3-1z" class="AG"></path><path d="M372 581l1-1c2 0 3 1 4 1h0v1l-2 1c-1 1-2 1-3 1v-1h0v-2z" class="y"></path><path d="M377 582h3 0c-1 1-3 3-4 3-2 2-4 5-6 6-2 2-6 3-8 5l-2-1 14-9c1-1 1-2 1-3l2-1z" class="e"></path><path d="M311 595c1-2 1-2 3-2l2 1c2 1 2 1 4 1 1 0 1 0 2 1 1 0 1-1 3-1 0 0 0 1 1 1l2-2c-1 2-1 2-3 3-1-1-1 0-2 0s-2-1-3-1c1 1 2 1 3 1l1 1h4v-1h1l1-1 1 1v1l-1-1-1 1c-2 1-5 0-7 0v1c1 1 1 1 3 1 1 0 2 2 3 3-4 0-7-1-10-3h0c-3-1-5-3-7-5z" class="D"></path><path d="M353 421l5 4 2 2 1-2c1 1 2 2 4 2 5 4 8 8 13 11 2 2 7 6 8 8 0 2 0 3 2 4l1 1c1 1 4 3 6 3h0l2 1v1c-1 1-1 1-1 2l1 1c-1 1-1 0-2 1h-1l-8-5-9-4-5-3c-4-2-8-3-12-5l-2-2-4-5 2-2-2-2-3-1-4-1v-3h2 1v-1l1-1-1-3c1 1 2 2 4 3h1c-1-2-2-3-2-4z" class="m"></path><path d="M355 430c3 0 4 2 6 4v1l-4-1h-1l-2-2c1 0 1 0 2-1l-1-1z" class="B"></path><path d="M368 434l8 7c0 1 1 2 1 3-1-1-3-1-4-2s-1-3-3-4c-1-1-2-2-2-4z" class="e"></path><path d="M350 426l1-1 4 5 1 1c-1 1-1 1-2 1l-3-1-4-1v-3h2 1v-1z" class="B"></path><path d="M355 430l1 1c-1 1-1 1-2 1l-3-1c1-1 3-1 4-1z" class="F"></path><path d="M354 436l2-2h1c1 1 2 2 4 3l3 3c3 2 7 3 9 6-3-2-9-4-11-7-1-1 0 0-2-1v2l-2 1-4-5z" class="AB"></path><path d="M360 427l1-2c1 1 2 2 4 2 5 4 8 8 13 11 2 2 7 6 8 8-2-1-4-2-5-3l-5-2-8-7-8-7z" class="Q"></path><path d="M358 441l2-1v-2c2 1 1 0 2 1 2 3 8 5 11 7 2 1 5 2 7 3h1l-1-2c-2-1-2-1-3-3 0-1-1-2-1-3l5 2c1 1 3 2 5 3 0 2 0 3 2 4l1 1c1 1 4 3 6 3h0l2 1v1c-1 1-1 1-1 2l1 1c-1 1-1 0-2 1h-1l-8-5-9-4-5-3c-4-2-8-3-12-5l-2-2z" class="D"></path><path d="M372 448c1 0 2 0 3 1h1c3 1 9 3 11 5l-1 1-9-4-5-3z" class="a"></path><path d="M376 441l5 2c1 1 3 2 5 3 0 2 0 3 2 4l1 1c1 1 4 3 6 3h0l2 1v1c-1 1-1 1-1 2l1 1c-1 1-1 0-2 1l-1-1v-1c0-2-3-3-4-5l-6-3v-1h-3l-1-2c-2-1-2-1-3-3 0-1-1-2-1-3z" class="E"></path><path d="M376 441l5 2c0 3 1 4 3 6h-3l-1-2c-2-1-2-1-3-3 0-1-1-2-1-3z" class="F"></path><defs><linearGradient id="An" x1="548.101" y1="676.072" x2="591.129" y2="663.36" xlink:href="#B"><stop offset="0" stop-color="#2b0204"></stop><stop offset="1" stop-color="#6f0f0e"></stop></linearGradient></defs><path fill="url(#An)" d="M582 639v-3c1 0 2 1 2 2l2 6c1 1 2 3 3 4l3 3 2 4c1 2 2 3 3 5h0v4l-1 1h1c-1 4-3 9-6 11h-3l-1-1-2 1c-1 0-2-1-3-1 0-1 0-1-1-1l-1-1h-1v2l-2 6c-1 0-1 1-1 2l1 1 1 2-1 1c-2 0-2 1-3 2l-2 3v-1c0-2 2-4 2-6h0l-3 3c-1 2-1 3-2 4-2 3-4 5-6 7l-6 7c-2 2-5 4-8 6 3-5 9-10 12-14 2-3 4-6 5-9 2-4 4-6 4-11l1-2h0l2 2h1c4-8 7-17 8-26 1-3 2-6 1-10l-1-3z"></path><path d="M580 673c2-2 2-4 3-7l3-16v-1h1l1 1c1 0 1 1 2 2l1-1-2-2v-1l3 3 2 4c1 2 2 3 3 5h0v4l-1 1h1c-1 4-3 9-6 11h-3l-1-1-2 1c-1 0-2-1-3-1 0-1 0-1-1-1l-1-1z" class="S"></path><path d="M586 670h0l-1-3c0-1 1-2 1-4h1l1 2v2c0 1 0 2 1 3l-2-2-1 2z" class="I"></path><path d="M589 648l3 3 2 4c1 2 2 3 3 5h0v4l-1 1v1c-2 0-2-1-3-2l1-2-3-6c-1-3-2-4-3-6 1 0 1 1 2 2l1-1-2-2v-1z" class="F"></path><path d="M597 660h0v4l-1 1v1c-2 0-2-1-3-2l1-2h1 1l1-2z" class="E"></path><path d="M589 670h1l3-6c1 1 1 2 3 2v-1h1c-1 4-3 9-6 11h-3l-1-1 1-2c0-1-1-2-2-3l1-2 2 2z" class="V"></path><path d="M407 614c1 1 1 1 2 3l-1 1c0 1 0 2 1 4 1 1 2 1 2 3 2 0 1-1 2-2 1 0 1 1 2 1l1-2 1 2c0-1 0-1 1-2h0c1 2 1 3 2 4v1l1 1c-1 0-1 1-2 1v2h0l1 1 1-2 1 1-1 1-6 3c1 1 1 1 3 0h1l1-1h0 1 0c-1 1-3 2-5 3l-6-3-3 5v1h-1c-3 2-5 4-8 5-1 1-2 1-3 2-2 0-4 1-5 0 1-1 0-1 1-2h0v-1c0-3 1-13-1-15l-1 1c-1 0-2 0-3-1h0c-3-2-7-2-10-3l-1 1v1h-1 0c-1-1-1-2-1-3h0c-2-3-2-5-2-8l-1-1 1-1 1 1c1 3 1 5 2 8 3 2 7 2 9 1 1 0 2 0 3-1 0 0 0-1 1-1s1 0 2-1l-1-2 2 2h1 1c1 0 1 0 2-1l-1-1c1-1 1-1 2-1 0 1 0 2 1 3v1c1 0 2-1 3-1l1 1 2-1c2-2 3-5 5-8z" class="H"></path><path d="M405 626c1 1 0 1 1 1 0 1 1 2 1 3l1 3c0 1-2 4-3 6-1-2-1-6-2-8s-3-2-5-3c-1-1-1-1-1-2h1v-1c-1 0-2-1-3-2l1-1v1c1 0 2-1 3-1l1 1 2-1-3 4v1l3-1v1 1h3v-1h-1 0l-1-1c0-1 1-1 2-1v1z" class="L"></path><path d="M407 614c1 1 1 1 2 3l-1 1c0 1 0 2 1 4 1 1 2 1 2 3v1c-1 1-1 1-2 0v1c-1 0-1-1-2-2l-2 1v-1c-1 0-2 0-2 1l1 1h0 1v1h-3v-1-1l-3 1v-1l3-4c2-2 3-5 5-8z" class="E"></path><path d="M407 625v-3c0-2 1-1 1-3v-1c0 1 0 2 1 4 1 1 2 1 2 3v1c-1 1-1 1-2 0v1c-1 0-1-1-2-2z" class="j"></path><path d="M418 622h0c1 2 1 3 2 4v1l1 1c-1 0-1 1-2 1v2h0l1 1c-1 0-1 0-1 1-2 0-4 1-6 1-1-1-2-2-3-4 0-1-1-2-1-3v-1c1 1 1 1 2 0v-1c2 0 1-1 2-2 1 0 1 1 2 1l1-2 1 2c0-1 0-1 1-2z" class="I"></path><path d="M418 622h0c1 2 1 3 2 4v1l-2 2c-2-2-1-3-1-5 0-1 0-1 1-2z" class="Q"></path><path d="M420 627l1 1c-1 0-1 1-2 1v2h0l1 1c-1 0-1 0-1 1-2 0-4 1-6 1-1-1-2-2-3-4 2 0 5-1 7 0 1 0-1 0 1 0v-1l2-2zm-25 3c1 0 3 0 4 1 1 0 3 2 3 4 1 3-2 5-3 7-2 1-3 2-4 3-2-6-3-9 0-15z" class="S"></path><path d="M398 633h1v3 1c1 1 0 2-1 4h-1v-1l-1-2c0-2 1-3 2-5z" class="T"></path><defs><linearGradient id="Ao" x1="483.725" y1="736.926" x2="455.961" y2="725.863" xlink:href="#B"><stop offset="0" stop-color="#1f0303"></stop><stop offset="1" stop-color="#350907"></stop></linearGradient></defs><path fill="url(#Ao)" d="M449 702c6 4 12 11 19 12 4 3 9 7 13 7v2c-1-1-4-1-5-2l-1 1h2c1 1 1 1 2 1 0 2 1 3 1 5h-1c0 1 1 2 2 3v1c-1 1-1 2-1 3l2 2c1 1 1 2 1 3l-1 3c-2 0-3 1-4 2l-4-2-1 1 1 1h-2c-2 0-3-1-4-2-2-2-3-3-4-6l-2-2-2 1c0-1-1-2-1-3s-1-2-1-3h-1v-4c0-2 0-2 2-3v1l1 1h1l-2-3h-1s1-1 1-2v-1l1-1h0-3l-1-1c-1-2-1-3-3-4-1-2-3-3-4-5l-3-3c-1 0-1-1-2-2 1 0 2 1 2 1h1c1 1 0 1 2 1v-3z"></path><path d="M469 733h1c1 1 2 2 2 3v1l-1 1c-1-2-2-3-2-5z" class="n"></path><path d="M453 713l3-1 1 1c1 1 2 2 4 2 1 1 2 1 3 2 2 0 3 1 5 2h-1l-1 1c0-2-1-2-2-2h-1v-1l-1 1v1l-1-1c-1-1-3-1-5 0l-1-1c-1-2-1-3-3-4z" class="I"></path><path d="M457 726c0-2 0-2 2-3v1l1 1h1v2l-1 1c1 2 2 4 4 6l-1 1 1 2-2-2-2 1c0-1-1-2-1-3s-1-2-1-3h-1v-4z" class="C"></path><path d="M449 702c6 4 12 11 19 12 4 3 9 7 13 7v2c-1-1-4-1-5-2h0l-7-2c-2-1-3-2-5-2-1-1-2-1-3-2-2 0-3-1-4-2l-1-1-3 1c-1-2-3-3-4-5l-3-3c-1 0-1-1-2-2 1 0 2 1 2 1h1c1 1 0 1 2 1v-3z" class="H"></path><path d="M368 329h1v-1s0 1 1 2h0c3 3 3 10 4 14l6 19 10 45v2l-1 2c-1-1-3-2-4-3-4-3-8-6-11-10 0 0-1-1-1-2-1 0-1-1-1-2h1l2 1-1-4-6-49c1-4 0-8-1-12l1-2z" class="R"></path><path d="M374 399s-1-1-1-2c-1 0-1-1-1-2h1l2 1 4 4c2 3 7 7 10 8h1v2l-1 2c-1-1-3-2-4-3-4-3-8-6-11-10z" class="C"></path><path d="M324 458h1l-1 2c1 0 2 0 2 1h1c1 0 1 0 2-1h1 0c1 8 2 17 5 24v1c0 2 0 3 1 5l1 1-1 1-1 6v1c-1 2-1 3-2 5v1c-1 2-1 5-2 7v1c-1 2-1 3-2 5-1 1-1 2-2 3s-2 3-2 4h-1c-1-4-2-7-2-11h-1v3h0c-1-1-1-5-1-7 0-12 0-24 1-36 1-4 1-8 2-12l1-4z" class="j"></path><path d="M330 496h-1-1c0-1-1-2-2-3h0c0-1 0-2 1-3h1c0 1 1 2 2 3l1 1-1 2z" class="W"></path><path d="M333 491c1-2 1-3 1-5v-1h1c0 2 0 3 1 5l1 1-1 1-1 6v1c-1 2-1 3-2 5v-3h0c-1-1-1-1-1-2-1-1-2-2-2-3l1-2v-1c1-1 0-2 2-2z" class="K"></path><path d="M333 491c1-2 1-3 1-5v-1h1c0 2 0 3 1 5l1 1-1 1-1 6c-1-2-1-3-1-5 0-1-1-1-1-2h0z" class="J"></path><path d="M324 458h1l-1 2c1 0 2 0 2 1h1c1 0 1 0 2-1h1 0c1 8 2 17 5 24v1h-1v1c0 2 0 3-1 5-2 0-1 1-2 2v-3c0-1-1-1-1-2s-1-2-1-2v-2c1-1 1-2 1-3h1c-1-1-1-1-2-1v-1c1-1 1-2 0-3-1-4 2-10-1-14h-1l-1 1h0v-1h-3l1-4z" class="V"></path><path d="M323 462h3v1h0l1-1h1c3 4 0 10 1 14l-1 1h-1c-2 3-3 6-3 10 1 2-1 3-1 5 0 1 1 3 1 4 1 3 1 4 0 8v3l1 1 1-1 1-1 2 2h1c1 1 1 2 1 4v1c-1 2-1 3-2 5-1 1-1 2-2 3s-2 3-2 4h-1c-1-4-2-7-2-11h-1v3h0c-1-1-1-5-1-7 0-12 0-24 1-36 1-4 1-8 2-12z" class="S"></path><path d="M326 507l1-1 2 2h1c1 1 1 2 1 4v1c-1 2-1 3-2 5v-3l1-1-1-1v-1c0-2-2-4-3-5z" class="T"></path><defs><linearGradient id="Ap" x1="539.967" y1="670.532" x2="559.466" y2="674.357" xlink:href="#B"><stop offset="0" stop-color="#4a4746"></stop><stop offset="1" stop-color="#636160"></stop></linearGradient></defs><path fill="url(#Ap)" d="M585 546l2-1-3 11-23 83-22 76-12 44-5 18-3 9h0l-1 1v-4c-1-3 1-9 2-11l3-12 8-26 11-42 8-27 16-55 11-37c0-1 1-4 1-5l4-12 2-7 1-3z"></path><defs><linearGradient id="Aq" x1="609.864" y1="578.594" x2="602.659" y2="593.69" xlink:href="#B"><stop offset="0" stop-color="#170201"></stop><stop offset="1" stop-color="#3f0505"></stop></linearGradient></defs><path fill="url(#Aq)" d="M588 572c4 2 8 3 11 3 3 1 6 0 8 1l3 2c2 0 4 2 7 3l6 3c1 1 3 1 5 2h0c4 2 6 3 9 5l-1 1h-1l-2-1c-1 1-2 2-3 4h-1c-4 1-7 3-10 6l-1-1c-6 5-9 11-14 16 0-2 1-3 1-5s1-4 1-6l-1-1v-1c1-2 1-3 2-4v-1l-3 3c0-1 0-1-1-1h-1l-4-15-3-5c0-1-1-2-2-2-2-1-2-1-3-2l-2-2v-2z"></path><path d="M609 597l1-2c-1-2-2-2-2-4 2-1 3-1 4-1 0 1 1 2 0 3l-1-1-1 3s0 1-1 1v1z" class="T"></path><path d="M601 578h1l7 4v1h-3c-1-2-4-2-5-5z" class="C"></path><path d="M602 587l2 1 1 1c1 0 1-1 2-1l1 1c-1 1-2 2-2 3v1 1h-1v-2h-1l-2-1v-4z" class="T"></path><path d="M598 585c1 1 2 1 3 2h0v-1h1v1h0v4l2 1h1v2 1c0 1-1 1-1 2 1 0 3 0 3 1h0l-3 3c0-1 0-1-1-1h-1l-4-15z" class="S"></path><path d="M612 590c1-1 1-1 2-1h2c1 1 2 1 3 2v1l-2 1v1h1c2 0 2 0 3-2v1l-1 2 1 2c3-1 4-3 7-3l1 1c-4 1-7 3-10 6l-1-1c1-1 1 0 0-2v-1c0-1-1-2-2-2l-2-1c-1 0-1 1-2 1v1c-1-1 0-2 0-3 1-1 0-2 0-3z" class="L"></path><path d="M621 592c2-1 2 1 4 1l1-1 1 1h3l-1-1c-1-2-2-3-4-3-1-1-1-1-1-2v-1h4 0c4 2 6 3 9 5l-1 1h-1l-2-1c-1 1-2 2-3 4h-1l-1-1c-3 0-4 2-7 3l-1-2 1-2v-1z" class="c"></path><path d="M609 597v-1c1 0 1-1 1-1l1-3 1 1c0 1-1 2 0 3v-1c1 0 1-1 2-1l2 1c1 0 2 1 2 2v1c1 2 1 1 0 2-6 5-9 11-14 16 0-2 1-3 1-5s1-4 1-6l-1-1v-1c1-2 1-3 2-4v-1h0l2-1z" class="C"></path><path d="M609 597v-1c1 0 1-1 1-1l1-3 1 1c0 1-1 2 0 3v-1c1 0 1-1 2-1l2 1c1 0 2 1 2 2v1c-2 1-8 2-10 3 0 1-1 3-2 4l-1-1v-1c1-2 1-3 2-4v-1h0l2-1z" class="O"></path><path d="M439 631c0 1 0 1 1 2h3l1-2h2c1 2 1 9 1 12 0 1 0 2 1 3v5 2h0c-1 0-2 1-2 2s-1 4 0 5 1 3 1 4v1l1 1v1 2c0 1 1 3 2 5 1 3 3 4 5 5h1c0-1 1-2 0-3 0-1 0-2-1-3v-1h-1v-1c0-1-1-3-1-4s0-1-1-1v-1-1l-1-2v-2-1l-1-1v-1-2-2l-1-1c0-1 1-2 0-3v-1c0-1-1-3-1-4h0v-2l1-1c1 2 1 2 1 4h0-1c1 6 2 12 4 18 1 5 3 10 4 15v3c0 1 0 2 1 3l-2 1c-1 1-2 2-3 2s-1-1-2-1c-4 2-5 6-8 9-6-8-8-16-9-25-1-4-1-7-2-11 2-1 2-1 3-2 0-3 1-6 2-9v-1c1-3 2-5 2-8l-1-1 2-2-3-3 2-2z" class="L"></path><path d="M439 631c0 1 0 1 1 2h3c-1 2-3 4-4 6l-1-1 2-2-3-3 2-2z" class="j"></path><path d="M445 672c-3-3-4-11-4-15l1 1c0 2 0 4 1 6v-1h2v-2c0 1 0 3 1 4l1 5-1 1-1 1z" class="K"></path><path d="M445 672l1-1 1-1c2 3 3 6 6 9h1l3 2c0 1 0 2 1 3l-2 1c-1 1-2 2-3 2s-1-1-2-1c1-1 2-1 2-2-2-2-3-5-5-6s-2-3-3-6z" class="J"></path><path d="M456 685h-1c-1 0-2-3-2-5h-1l2-1 3 2c0 1 0 2 1 3l-2 1z" class="F"></path><path d="M659 606c2 0 3 0 4 1 2 1 4 2 6 2 0 1 2 1 2 2h10c4 1 8 2 12 4l2-1v-1h1v1c0 1 0 1 1 2l1 4c1 1 1 2 2 2l1 1 1 2v4 6 2c-1 2-1 4-1 6v1l-5 5c-2 4-6 7-9 10-1-2-1-2 0-4h-1c-1 3-3 5-6 6 1-2 2-3 3-5 0-1 0-2 1-2v-1c0-1 1-3 1-5v-2c-1-2 0-3-1-4v-2c0-1 0-1 1-2l1 1v-3c1-2-2-4-2-6s-1-2-2-3v-2l-1-1c-1 0-2-1-3-1h0c-1-1-3-2-4-3-3-4-8-7-11-11h0l-4-3z" class="C"></path><path d="M677 616h4l1 1-2 1-1 2c-1-1-2-2-2-4z" class="X"></path><path d="M686 622c-2-1-3-3-4-5 2 1 4 1 5 3l-1 2z" class="c"></path><path d="M689 638c1 0 1 0 1 1v5h-1c-1 0-2 0-3 1 1 1 1 1 1 2h-1l-1 1v-2-3h0 1c2-1 3-3 3-5z" class="R"></path><path d="M681 611c4 1 8 2 12 4l2-1v-1h1v1c0 1 0 1 1 2-2 0-3 1-5 1-4-3-8-4-12-4l1-2z" class="S"></path><path d="M659 606c2 0 3 0 4 1 2 1 4 2 6 2 0 1 2 1 2 2h10l-1 2c-2 0-3-1-5-1s-4 1-5 0l-6-3h-1l-4-3z" class="o"></path><path d="M687 620c2 0 4 1 5 3v1l1-1 1 1h2c2 2 2 7 3 10v6l-1 2-1-2h-1l-1-1c-1 2-1 2-3 2v-2c-1-2-2-3-2-5 0-1 1-2 1-3-1-1-1-2-2-3h0c0-3-1-4-3-6l1-2z" class="R"></path><path d="M695 639v-2c1-1 1-1 3-2l1 1v4l-1 2-1-2h-1l-1-1z" class="T"></path><path d="M690 634c0 2 1 3 2 5v2c2 0 2 0 3-2l1 1h1l1 2-3 6 1 1c-2 4-6 7-9 10-1-2-1-2 0-4h-1c-1 3-3 5-6 6 1-2 2-3 3-5 0-1 0-2 1-2v-1c0-1 1-3 1-5l1-1h1c0-1 0-1-1-2 1-1 2-1 3-1h1v-5c0-1 0-1-1-1 1-2 1-3 1-4z" class="V"></path><path d="M689 644h1c0 1-1 3 0 4h1l1 1-3 5h0v-5-5z" class="W"></path><path d="M695 639l1 1c-2 3-2 6-4 9l-1-1c1-2 1-4 1-7 2 0 2 0 3-2z" class="L"></path><path d="M690 634c0 2 1 3 2 5v2c0 3 0 5-1 7h-1c-1-1 0-3 0-4v-5c0-1 0-1-1-1 1-2 1-3 1-4z" class="AC"></path><path d="M689 644v5h-2v2c0 1-1 2-2 3l1 1c-1 3-3 5-6 6 1-2 2-3 3-5 0-1 0-2 1-2v-1c0-1 1-3 1-5l1-1h1c0-1 0-1-1-2 1-1 2-1 3-1z" class="Q"></path><path d="M398 471v-2h1v3c0 2 1 3 1 5 1 1 1 2 2 2 1 1 0 1 1 2 0 1 1 3 2 4 2 2 3 4 5 6 1 4 2 8 4 11l5 15c2 2 3 4 4 7 2 3 3 7 4 11 1 1 1 6 2 7v1 1 2c-1 0-1-2-3-1-1 0-2-1-2-2l-2-1c-3-4-6-8-8-12s-3-7-4-11h0l-2-4v2 2c-2 1-2 2-3 3 0-2 1-8 0-10v-1l-1-2c-1 0-2 0-3 1v1l-3 1-1-1v-1l1-1c-1-2-1-4-2-6s0-5 0-8c0-1-1-3 0-5v-4-1c1-1 1-2 1-2l1-1c-1-2-1-3 0-5v-6z" class="T"></path><path d="M414 530v-3l1-1c2 1 5 6 7 8 0-3-3-5-3-8l2-1c-1 1-1 1-1 2l1 2v-1h0l3 9h1c0 1 1 2 0 4l-1 1v1l-2-1c-3-4-6-8-8-12z" class="c"></path><path d="M403 485h2c2 2 3 4 5 6 1 4 2 8 4 11l5 15v1c-1-1-1-2-1-3l-1-3-1-1c0-1 0-2-1-3v-1l-1-1h-1-1c0-3-1-6-3-8-1-1-2-2-3-4l1-1c2 0 2 0 3 2 0-2-2-5-3-6 0 1-1 2-2 2v-1h-1c1 2 2 4 2 6-1-1-1-2-2-3h0c0-2 0-3-1-4h0c-1-2-1-2 0-4z" class="R"></path><path d="M413 506h1l1 1v1c1 1 1 2 1 3l1 1 1 3c0 1 0 2 1 3v-1c2 2 3 4 4 7 2 3 3 7 4 11 1 1 1 6 2 7v1 1 2c-1 0-1-2-3-1-1 0-2-1-2-2v-1l1-1c1-2 0-3 0-4h-1l-3-9h0v1l-1-2c0-1 0-1 1-2-1-2-3-7-4-9-1-1-2-2-2-4l-2-6z" class="AC"></path><path d="M398 471v-2h1v3c0 2 1 3 1 5 1 1 1 2 2 2 1 1 0 1 1 2 0 1 1 3 2 4h-2v-1l-1-1h0v1c-1 2-1 2-1 4l1 1v1h0-1c0 4 4 9 5 13 2 5 4 11 4 16h0 0l-2-4v2 2c-2 1-2 2-3 3 0-2 1-8 0-10v-1l-1-2c-1 0-2 0-3 1v1l-3 1-1-1v-1l1-1c-1-2-1-4-2-6s0-5 0-8c0-1-1-3 0-5v-4-1c1-1 1-2 1-2l1-1c-1-2-1-3 0-5v-6z" class="V"></path><path d="M399 472c0 2 1 3 1 5 1 1 1 2 2 2 1 1 0 1 1 2 0 1 1 3 2 4h-2v-1l-1-1h0v1c-1 2-1 2-1 4l1 1v1h0-1c-1-6-2-12-2-18z" class="C"></path><path d="M405 511h0v-3h1c1 2 2 4 2 7v2 2c-2 1-2 2-3 3 0-2 1-8 0-10v-1z" class="AG"></path><path d="M684 687h6c1 1 1 1 1 2-2 3-3 4-6 6h3l1-1h2c2-1 4-1 7 0l3 1v1h-1c1 1 0 1 1 1h2l3 2 1 2c-2 0-3 0-4 1 1 0 1 1 2 1l2 1c1 2 3 3 4 5 1 0 1 0 2 1h0c3 1 5 4 7 7h0c1 2 2 3 2 5v1h0c1 1 2 3 2 4l2 2 1 2c-2 0-5-4-7-5-7-6-16-12-25-15l-3-1-7-2-11-1h0 2v-2l-5-1h-6l-2 1-2-2 1-1h-1c-2 2-3 2-5 3 1-2 5-4 6-6 0-1 1-1 2-2 4-3 8-6 12-7 3-1 5-2 8-3z" class="D"></path><path d="M684 705h3c1 0 2 1 2 1h3l-2 1h1c0 1 0 1 1 2h0v1l-7-2 1-1v-1l-2-1z" class="M"></path><path d="M693 703c1 0 2 0 4 1 1 1 2 2 4 2 1 2 4 4 6 5v1l2 2c-4-3-8-6-13-7l-5-2v-1l2-1z" class="J"></path><path d="M671 704c4-1 9 0 13 1l2 1v1l-1 1-11-1h0 2v-2l-5-1z" class="s"></path><path d="M713 710c3 1 5 4 7 7h0c1 2 2 3 2 5v1h0c1 1 2 3 2 4l-2-2c-3-5-9-8-13-11l-2-2v-1c4 1 7 4 11 6-1-2-3-3-4-4s-1-2-1-3z" class="O"></path><path d="M701 697h2l3 2 1 2c-2 0-3 0-4 1 1 0 1 1 2 1l2 1c1 2 3 3 4 5 1 0 1 0 2 1h0c0 1 0 2 1 3s3 2 4 4c-4-2-7-5-11-6-2-1-5-3-6-5v-1c-1 0-1 0-1-1v-3c-1 0-1 0-2-1h3c1-1 1-2 0-3z" class="m"></path><path d="M691 694c2-1 4-1 7 0l3 1v1h-1c1 1 0 1 1 1 1 1 1 2 0 3h-3c1 1 1 1 2 1v3c0 1 0 1 1 1v1c-2 0-3-1-4-2-2-1-3-1-4-1-1-1-1-2-2-2s-3 0-4-1c0-1-1-1-2-1-1-1-2-1-3-1v-1c2 0 3-1 5-1l1-1 1-1h2z" class="D"></path><path d="M691 694c2-1 4-1 7 0l3 1v1h-1c1 1 0 1 1 1 1 1 1 2 0 3 0-1-1-1-2-1v-2l-1-1c-1 0-2 0-3-1s-2-1-4-1z" class="G"></path><path d="M688 695l1-1 1 1c2 1 4 1 6 2-1 1-2 1-3 2h-1c-1 0-2-1-3-1h-1c-1-1-1-1-1-2l1-1z" class="M"></path><path d="M685 699c-1-1-2-1-3-1v-1c2 0 3-1 5-1 0 1 0 1 1 2h1c1 0 2 1 3 1h1c1 0 4 0 5 1s1 1 2 1v3c0 1 0 1 1 1v1c-2 0-3-1-4-2-2-1-3-1-4-1-1-1-1-2-2-2s-3 0-4-1c0-1-1-1-2-1z" class="B"></path><path d="M684 687h6c1 1 1 1 1 2-2 3-3 4-6 6h3l-1 1c-2 0-3 1-5 1v1c1 0 2 0 3 1 1 0 2 0 2 1 1 1 3 1 4 1s1 1 2 2l-2 1v1c-2-1-4-2-7-3h-8-3l-3 2h-5l-2 1-2-2 1-1h-1c-2 2-3 2-5 3 1-2 5-4 6-6 0-1 1-1 2-2 4-3 8-6 12-7 3-1 5-2 8-3z" class="D"></path><path d="M661 703c3-1 4-2 7-2 1 1 1 2 2 3h-5l-2 1-2-2z" class="S"></path><path d="M677 697c1 1 1 1-1 3h0c-1 0-2 1-3 2l-3 2c-1-1-1-2-2-3 1-1 1-1 2-1 1-1 2-2 3-2 2 0 3-1 4-1z" class="T"></path><path d="M685 695h-2-1-2l1-1h1v-1l-1-1v-1c1 0 4-2 5-2 2 0 3-1 5 0-2 3-3 4-6 6z" class="B"></path><path d="M677 697l2-2c1 1 1 1 1 2v1h-1v1 1c2-1 2 0 3 0 0 0 2-1 3-1s2 0 2 1c1 1 3 1 4 1s1 1 2 2l-2 1v1c-2-1-4-2-7-3h-8-3c1-1 2-2 3-2h0c2-2 2-2 1-3z" class="F"></path><path d="M330 297c0-1 0-2 1-3v-1c0-3 2-5 2-7v-1c1 1 3 1 3 3l-1 1v1h2l2-1v-1l1 1c0 1 0 1-1 2 2 2 3 3 3 5 1 2 2 5 2 7l2 10v4c1 2 1 2 1 4l3 18 3 15v3h1 0c0 1 0 2 1 2-1 1-3 1-3 1l-2-2c1 4 2 6 4 9 1 1 2 3 2 4-1-1-1-1-2-1h-1l-2-2h-1c-2-3-2-7-3-10-3-9-9-16-10-24-1-1-1-2-1-2 1-1 0-1 1-2h-2l-1-2c-1-2-1-3-1-5v-3c-1-1-1-3-1-5h0c-1-1-1-2-2-3v-1c-1-1-1-3-1-4v-1c-1-2-1-4-1-6h0v-3h0l1 1h0l1-1h0z" class="L"></path><path d="M345 321h1c-2 6 1 12 1 17h-1v-2c-1-3-1-5-3-7 0-2 1-4 1-5s1-2 1-3h0z" class="Q"></path><path d="M329 307v-1c-1-2-1-4-1-6h0v-3h0l1 1h0l1-1h0l2 5 1 1c-1 1-1 2-2 3-1 2-1 4-1 5-1-1-1-3-1-4z" class="c"></path><path d="M345 321c0-2 0-4-1-6 0-1-1-1-2-2v-3c1-1 1-1 1-2s0-2-1-3h0v-2h2l2 10v4c1 2 1 2 1 4h-1-1z" class="H"></path><path d="M346 321h1l3 18 3 15v3h1 0c0 1 0 2 1 2-1 1-3 1-3 1l-2-2-3-20c0-5-3-11-1-17z" class="F"></path><path d="M406 532h1 1c1 7 2 13 5 19l2 3c1 1 2 3 2 4 1 1 2 1 2 2v2c2 2 3 0 6 1l2 2c0 1 0 1-1 1 0 1 0 2 1 2l-4 2c-1 0-2 1-4 1-1 0-2-1-4-1l-3 1c-2 2-5 1-7 3l-1-1-1 1-2-1-13 9c-3 1-6 2-9 4 0 1-1 1-2 2 0-1 0-2-1-3 1 0 3-2 4-3h0-3v-1h0c-1 0-2-1-4-1l7-6c1 1 1 1 2 1h2l15-24 2-7h0v-1l2-3v-1c0-1 1-2 1-3v-1c1-1 1-1 3-1l-1-2z" class="AA"></path><path d="M415 570c4-2 8-3 11-4 0 1 0 2 1 2l-4 2c-1 0-2 1-4 1-1 0-2-1-4-1z" class="G"></path><path d="M380 574c1 1 1 1 2 1h2c-2 2-4 4-7 6h0c-1 0-2-1-4-1l7-6z" class="z"></path><path d="M415 554c1 1 2 3 2 4 1 1 2 1 2 2v2c2 2 3 0 6 1l-6 1h0l-6-3-6-3h0 4 1c1 1 2 2 4 3v-1c-1-2-1-3-1-6z" class="e"></path><path d="M401 573c3-2 5-4 6-8-2-2-4-4-7-6h-1v-3h0c4 2 7 4 11 6 2 1 4 1 5 2l1 1h2c-1 1-2 2-4 2h0l-2 4c-2 2-5 1-7 3l-1-1-1 1-2-1z" class="D"></path><path d="M415 564l1 1h2c-1 1-2 2-4 2h0 0c-2 1-4 3-6 3 2-2 4-4 7-6z" class="y"></path><path d="M408 570c2 0 4-2 6-3h0l-2 4c-2 2-5 1-7 3l-1-1c2-1 3-2 4-3z" class="u"></path><path d="M406 532h1 1c1 7 2 13 5 19l2 3c0 3 0 4 1 6v1c-2-1-3-2-4-3h-1-4 0c-3-2-6-4-8-7l2-7h0v-1l2-3v-1c0-1 1-2 1-3v-1c1-1 1-1 3-1l-1-2z" class="k"></path><path d="M408 542c0 2 1 4 1 5v4c1 2 1 3 0 5-3 0-1-2-2-3s-1-1-2-1c0-1-1-2-1-3v-1 2h-2v-2 1h1c0-2 0-3 1-4 1 1 1 3 1 5h1l2-1c0-1-1-1 0-2v-5z" class="B"></path><path d="M401 544v-1l2-3v-1c0-1 1-2 1-3v-1c1-1 1-1 3-1l1 8v5c-1 1 0 1 0 2l-2 1h-1c0-2 0-4-1-5-1 1-1 2-1 4h-1v-1l-1-4h0z" class="i"></path><path d="M406 532h1 1c1 7 2 13 5 19l2 3c0 3 0 4 1 6v1c-2-1-3-2-4-3h-1l-1-2v-4l-1-1v-4c0-1-1-3-1-5l-1-8-1-2z" class="p"></path><path d="M409 547c1 2 1 3 3 5h0c-1 1-1 2-1 2l1 1c1-2 1-2 1-4l2 3c0 3 0 4 1 6v1c-2-1-3-2-4-3h-1l-1-2v-4l-1-1v-4z" class="G"></path><path d="M293 430h1 0l2 4-2 1c1 0 1 1 1 1v2h-1l-1 2 1 1c1 1 1 1 1 2v2 1 1l-1 2 1 2 1 1 1-1c-1 3-4 11-3 14l1 2v2 4 1h-1c-1 0-2 0-3-1h0 0c-1-1-2-1-2-1-1 0-1 0-1 1 1 1 3 3 3 4l2 2c-1 0 0 0-1 1h0v3h-1-2v3l2 14v4l1 1c0 2-1 4 0 5s1 1 1 2v1c0 1 0 2 1 4 0 1 0 2 1 3h0c0 3 1 4 3 6v1h1v-1-1c-2-1-3-3-3-5 1-1 1-1 2-1l2 2-1 1h0l1 1c0 1 0 1 1 2h0 2l1 1v-1l2-1h0c1 1 1 2 2 3 0 1 1 2 1 3s1 2 1 2 1-1 1-2c0 2 0 3 1 5h-1 0v4c1 2 1 3 1 4s0 2 1 3v1c0 1 1 3 2 4v1c-1 1-1 2-1 2l-1-2v-1l-1 1-1 1c-2-1-1-1-3-1-1 0-2-1-3-1 0 1 0 1-1 2 0 2-1 3-1 4l-1-1v-6h0l-2-2c0-2 1-3 1-5v-1h-1v2c-1 0-1 1-1 2 0 2 0 4-1 6v-4c-2-5-2-9-2-13v-1l-1-1v-1c0-1 0-1-1-2v-3h-1v-2l-2-5v-2-1c-1-1-2-3-2-4v-1l-1 3c-2-14-5-29-5-43l1-4h1 0l1 1c1-1 2-5 2-7l1-4 1-6c0-2 0-4 1-6 0-3 0-5 1-7l1-1-1-1c1-2 0-5 1-7z" class="Y"></path><path d="M294 441c1 1 1 1 1 2v2 1 1l-1 2h-1l1-2h-2v-1l1-1v-1c-1 0 0 0-1-1v-1h2v-1z" class="C"></path><path d="M302 529c1-1 2-1 3 0h2c1 1 1 2 1 3-2-1-3-1-5 0h-1c-1 1-2 1-3 2v1c-1-1 0-1 0-2v-2c0-1 1-2 3-2z" class="l"></path><path d="M291 473l-2-2 1-1c0-1 1-1 1-2 1-2 1-3 3-3l1 2v2 4 1h-1c-1 0-2 0-3-1h0z" class="Z"></path><path d="M295 467v2 4 1h-1c-1 0-2 0-3-1 1-2 2-3 3-4 1 0 1-1 1-2z" class="X"></path><path d="M302 550v-1c1-1 1-1 2-3 0 0 0-1 1-2l-3-3 2-2h1l1-1c1 0 1 0 3 1 0 1 0 1-1 2 0 1-1 2-1 3 1 1 1 2 2 2l-1 2c1 1 2 3 4 4l-1 1c-2-1-1-1-3-1-1 0-2-1-3-1 0 1 0 1-1 2 0 2-1 3-1 4l-1-1v-6h0z" class="b"></path><path d="M429 637v-1l2-3 2 1c0-1 0-1 1-2l1 1h1v3c0-1 1-2 1-3l3 3-2 2 1 1c0 3-1 5-2 8v1c-1 3-2 6-2 9-1 1-1 1-3 2 1 4 1 7 2 11 1 9 3 17 9 25l1 1 1 1-1 1h-1c-2 0-3-1-5-2h-1c-2 1-4 0-6-1-1-1-2-1-4-2v2l-4-4v-2c-1-3-1-5-2-7 0-1-1-1-1-2v-5c-1-4-1-9-2-14 0-1 0-3 1-5h0c0-3 0-5 1-8v-1h0c2-1 2-3 2-4l4-4s2-2 3-2z" class="Q"></path><path d="M430 645v5h0c1 0 1-1 2-1v10c1 4 1 7 2 11-1 0-3 0-4 1-1-8-3-18 0-26z" class="h"></path><path d="M429 637c0 1-1 1-1 2h1 0c0 1-1 2-2 3s-1 3-2 5l-3 6 1 1h-1c0 3-1 5 0 8v1l-1 1h0v-4h-1l-1-4h0c0-3 0-5 1-8v-1h0c2-1 2-3 2-4l4-4s2-2 3-2z" class="E"></path><path d="M426 639l1 1-1 1v1l-1 1v2c-1 2-2 5-3 7l-1-1-1-3v-1h0c2-1 2-3 2-4l4-4zm8 31c1 9 3 17 9 25l1 1 1 1-1 1h-1c-2 0-3-1-5-2-4-7-7-17-8-25 1-1 3-1 4-1z" class="F"></path><path d="M419 656l1 4h1v4h0l1-1v-1 1c1 6 1 11 3 17 2 4 3 11 6 14v1c-1-1-2-1-4-2v2l-4-4v-2c-1-3-1-5-2-7 0-1-1-1-1-2v-5c-1-4-1-9-2-14 0-1 0-3 1-5z" class="j"></path><path d="M435 633h1v3c0-1 1-2 1-3l3 3-2 2 1 1c0 3-1 5-2 8v1c-1 3-2 6-2 9-1 1-1 1-3 2v-10c-1 0-1 1-2 1h0v-5c1-4 2-8 5-12z" class="V"></path><path d="M437 633l3 3-2 2h0c-1 1-1 3-3 4h-1c0-2 1-4 2-6 0-1 1-2 1-3z" class="K"></path><path d="M435 633h1v3c-1 2-2 4-2 6-2 1-2 5-2 7-1 0-1 1-2 1h0v-5c1-4 2-8 5-12z" class="a"></path><path d="M573 624c-1-1-1-1-1-2 1-4 3-7 4-10 2-7 3-16 8-22h1c1 1 2 2 3 4l2 3c2 1 3 2 4 4l-1 1c1 3 0 3 0 7 0 1 0 3-1 5v1c0 2-1 3-2 4l1 3 1-1h0l1 2c1-1 2-2 4-2 0 0 1-1 2-1h0v2l1 1c-1 2-1 4-1 6 1 0 2-1 3 0l-2 1s1 0 1 1h0c0 1 0 2-1 2 0 2 1 3 0 5l1 1-1 3v1c-3 2-3 5-6 7h-1l-1 1-3-3c-1-1-2-3-3-4l-2-6c0-1-1-2-2-2v3l-4-10c-1-2-2-4-4-5h-1z" class="W"></path><path d="M591 631h1c1 1 0 1 0 3l-1-1v-2z" class="T"></path><path d="M594 631l1-3h1l1 1-1 2h-2z" class="c"></path><path d="M599 629c1 0 2-1 3 0l-2 1c-2 1-4 3-7 4v-1l1-2h2l1-2h2z" class="R"></path><path d="M578 629l1-1c2 1 3 5 4 7 1 0 1 2 1 3 0-1-1-2-2-2v3l-4-10z" class="V"></path><path d="M593 623c1-1 2-2 4-2 0 0 1-1 2-1h0v2c-3 2-5 5-8 7-3 1-4 4-6 6h-1v-3c2-1 3-3 5-5 0-1 1-2 2-2 1-1 1-2 2-2z" class="K"></path><path d="M590 597c2 1 3 2 4 4l-1 1c1 3 0 3 0 7 0 1 0 3-1 5v1c0 2-1 3-2 4l1 3 1-1h0l1 2c-1 0-1 1-2 2-1 0-2 1-2 2-2 2-3 4-5 5 0-1 0-1 1-2 1-2 1-5 2-8v-10c1-2 2-5 2-8v-1c0-3 0-4 1-6z" class="O"></path><path d="M593 609l-1 1-1-1s1-1 0-2c0-2 1-3 2-5 1 3 0 3 0 7z" class="T"></path><path d="M600 630s1 0 1 1h0c0 1 0 2-1 2 0 2 1 3 0 5l1 1-1 3v1c-3 2-3 5-6 7h-1l-1 1-3-3c-1-1-2-3-3-4 1-2 1-2 1-3s1-2 1-3l5-4c3-1 5-3 7-4z" class="K"></path><path d="M600 642v1l-2-1c0-1-1-2-1-3v-1h-2l1-2c1-2 1 0 2-1 1 0 0-1 2-2 0 2 1 3 0 5l1 1-1 3z" class="Q"></path><path d="M588 638v4c2 0 2 0 3-2 1 0 1-1 1-2 2 0 2 0 3 1s1 2 0 3c-1 3-3 3-3 8h1l-1 1-3-3c-1-1-2-3-3-4 1-2 1-2 1-3s1-2 1-3z" class="T"></path><path d="M573 624c-1-1-1-1-1-2 1-4 3-7 4-10 2-7 3-16 8-22h1c1 1 2 2 3 4-3 5-4 9-5 14 1 4 0 6-1 9l-3 9c-2-1-2-3-3-5-1 0 0 0-1-1-1 1-1 2-1 3v1h-1z" class="n"></path><path d="M583 608c1 4 0 6-1 9v-1l-1-1c0-3 1-5 2-7z" class="S"></path><path d="M309 314l-1-1c-1-1-2-4-2-6h0c1 4 4 8 6 12 0 1 1 2 2 3 2 3 11 22 10 26l-1 1c0 4-1 10 2 13l1 12c0 3 0 5 1 8 1 0 1 1 2 1 0 1 0 1-1 2l-1 2c-2 1-3 2-6 2-1 1-2 2-3 4h0v6c-1 1 0 2 0 3v1c-3-2-5-5-9-6h6c-2-1-4-1-5-2-2 0-3-1-5-1l1-1v-1-1c-1-1-1-1-1-2 1-1 1-1 3-2h0-1c0-2 0-3 1-4 1-2 2-5 1-7l-1-3h-1l2-12c1 2 1 3 1 5l1-13v-8h-1c0-1 0-2 1-3 0 1 0 1 1 1l1-1-1-1c-1-2-1-4-2-6 2-8 1-13-2-20l1-1z" class="j"></path><path d="M313 342v1h0c0 4 1 10 0 13l-1 1c-1-1-1-2-1-4v-8h-1c0-1 0-2 1-3 0 1 0 1 1 1l1-1z" class="R"></path><path d="M311 319c1 2 4 7 4 10-1 0-1 0-1 1h0c1 2 1 1 1 2s0 1 1 2c1 2 2 6 2 8h-1c-1 0-3 1-4 1v-1l-1-1h1v-3c0-2 1-4 1-5 0-5-2-9-3-14z" class="I"></path><path d="M309 314l2 5c1 5 3 9 3 14 0 1-1 3-1 5v3h-1c-1-2-1-4-2-6 2-8 1-13-2-20l1-1z" class="R"></path><path d="M310 366h0l1 3h0c0-1 0-2 1-3 0-2 1-4 1-5s0-1 1-2c0 2-1 3-1 4v7h1v-3 1 2h1l1 1c-2 2-2 5-3 7-1 3-2 6-4 9h-1-1c0-2 0-3 1-4 1-2 2-5 1-7l-1-3h-1l2-12c1 2 1 3 1 5z" class="E"></path><path d="M316 371c0 1 1 1 1 2h1v-5h0c0 3 0 7-1 11v2l2 2h2c1 1 2 1 4 2h1s1 0 2-1v1l-1 2c-2 1-3 2-6 2-1 1-2 2-3 4h0v6c-1 1 0 2 0 3v1c-3-2-5-5-9-6h6c-2-1-4-1-5-2-2 0-3-1-5-1l1-1v-1-1c-1-1-1-1-1-2 1-1 1-1 3-2h0 1c2-3 3-6 4-9 1-2 1-5 3-7z" class="h"></path><path d="M319 386h3c1 0 1 0 2 1l-4 2c-1 0-3 1-4 1l-1-1c1-1 2-2 4-3z" class="Q"></path><path d="M308 387c2 1 4 3 7 5l2 1v3c-1 1-1 1-2 1-2-1-4-1-5-2-2 0-3-1-5-1l1-1v-1-1c-1-1-1-1-1-2 1-1 1-1 3-2z" class="AF"></path><path d="M306 312l-8-22c0-1-1-3-1-5l-1-1v-1l-1-1h1l1 2v-1l3 5v-3c-1-1-1-2-1-3 1 1 2 3 3 4h1l9 17c1 2 10 18 10 19-1 1-1 2-1 3l1 1c0 1 0 2-1 4 5 8 9 17 14 25l8 16c1 3 3 5 4 8l-2 3-3 3-2 1-1 1c-1 1 0 0-2 1l-3-3v1 1c0 1 0 2-1 3h0c-1 1-2 3-2 4l-1 1v-1-4-1c-1-1-1 0-2 0l-1-2 1-2c1-1 1-1 1-2-1 0-1-1-2-1-1-3-1-5-1-8l-1-12c-3-3-2-9-2-13l1-1c1-4-8-23-10-26-1-1-2-2-2-3-2-4-5-8-6-12h0c0 2 1 5 2 6l1 1-1 1-2-3z" class="c"></path><path d="M325 362c-3-3-2-9-2-13l1-1 4 15c-1-1-2-1-3-1z" class="J"></path><path d="M325 362c1 0 2 0 3 1l1 10c1 3 0 7 0 10-1 0-1-1-2-1-1-3-1-5-1-8l-1-12z" class="G"></path><path d="M334 386l-1-1v-2c0-4 2-8 0-13l1-1c2 3 3 7 4 10l4 6-2 1-1 1c-1 1 0 0-2 1l-3-3v1z" class="k"></path><path d="M338 379l4 6-2 1c-2-1-4-2-5-4h2 1v-3z" class="u"></path><path d="M300 288v-3c-1-1-1-2-1-3 1 1 2 3 3 4h1l9 17c1 2 10 18 10 19-1 1-1 2-1 3l1 1c0 1 0 2-1 4-3-5-5-11-7-16l-14-26z" class="AG"></path><path d="M311 614l3-3h0 1l1 1c0-1 2-1 3-1-2 2-2 2-2 5 0 1 0 2-1 3l1 1h1 0l1 5c2 8 5 17 10 23h0l1-1 1 2c1 2 3 4 5 7l1 1c1 1 2 4 3 4s2 1 3 2h0l1 1 3 3c2 0 3 2 5 3l5 5c1 0 3-1 3-1-2-3-5-6-7-10 0-1 0-1 1-2 1 2 9 12 10 13 6 6 12 12 19 18l10 8c1 0 3 1 3 2v1l2 1h0l-1 1c-1 0-1 0-1 1v-1c-2-1-3-1-4-1l2 2v1c-3-1-5-2-8-2-1 0-3 0-4 1-1-1-2-3-3-3h0c-4-3-10-5-13-9l-1 1c-3-2-5-4-6-7l-1-1c-3-3-7-6-10-9-2-2-3-4-4-7h0l-16-19c-3-3-6-7-9-10-1-1-3-1-5-2h0c-1-1-1-2-1-3l1-1v-1c0-1-1-1-1-2v-3c1 0 1 0 1 1 0 2 1 3 2 5l1-1-5-12c0-4-1-7-1-10z" class="m"></path><path d="M364 679h0c2 2 4 3 6 4 2 2 3 4 4 7l-12-9-1-1c1 0 2-1 3-1z" class="i"></path><path d="M381 699l11 6 2 2v1c-3-1-5-2-8-2-2-1-3-2-5-3v-4z" class="z"></path><path d="M354 662c1 2 9 12 10 13l-1 1 1 1 6 6c-2-1-4-2-6-4h0l-4-5c-2-3-5-6-7-10 0-1 0-1 1-2z" class="e"></path><path d="M344 668l16 15 21 16v4l-23-18c0-2-1-2-2-3 0-3-5-5-7-8l-3-3c-1-1-2-1-3-3h1z" class="AB"></path><path d="M340 661c1 0 2 1 3 2h0l1 1 3 3c2 0 3 2 5 3l5 5c1 0 3-1 3-1l4 5c-1 0-2 1-3 1l1 1c-1 0-1 0-1 1l-1 1-16-15c-1-2-3-4-3-5l-1-2z" class="k"></path><path d="M340 661c1 0 2 1 3 2h0l1 1 3 3c2 0 3 2 5 3l5 5c1 0 3-1 3-1l4 5c-1 0-2 1-3 1-2-2-4-4-7-5h0c-1-1-2-3-3-3-3-2-5-4-7-7l-3-2-1-2z" class="H"></path><path d="M344 672c4 4 9 10 14 13l23 18c2 1 3 2 5 3-1 0-3 0-4 1-1-1-2-3-3-3h0c-4-3-10-5-13-9l-1 1c-3-2-5-4-6-7l-1-1c-3-3-7-6-10-9-2-2-3-4-4-7z" class="O"></path><path d="M359 689l4 2c2 1 2 2 3 4h0l-1 1c-3-2-5-4-6-7z" class="S"></path><path d="M314 641h0c-1-1-1-2-1-3l1-1v-1c0-1-1-1-1-2v-3c1 0 1 0 1 1 0 2 1 3 2 5l1-1 1 1h0l8 10c3 5 6 10 11 14-1-2-3-4-5-7l-3-6 1-1 1 2c1 2 3 4 5 7l1 1c1 1 2 4 3 4l1 2c0 1 2 3 3 5h-1c1 2 2 2 3 3l3 3c2 3 7 5 7 8 1 1 2 1 2 3-5-3-10-9-14-13h0l-16-19c-3-3-6-7-9-10-1-1-3-1-5-2z" class="f"></path><path d="M311 614l3-3h0 1l1 1c0-1 2-1 3-1-2 2-2 2-2 5 0 1 0 2-1 3l1 1h1 0l1 5c2 8 5 17 10 23h0l3 6c2 3 4 5 5 7-5-4-8-9-11-14l-8-10h0l-1-1-5-12c0-4-1-7-1-10z" class="o"></path><path d="M319 625c-1 0-1 0-2-1 0-1-1-4-1-5l1 1h1 0l1 5z" class="X"></path><path d="M311 614l3-3h0 1l1 1c-3 9-1 17 2 25h0l-1-1-5-12c0-4-1-7-1-10z" class="z"></path><path d="M439 602c1 0 1-1 2-2l1 1c2 0 2 0 4 2v1c0 1 1 2 2 3 0 2 0 5-1 8 0 1-1 3-1 4 1 2 2 3 3 4-1 1-1 2-2 2v1c-2 1-3 3-4 5h1l-1 2h-3c-1-1-1-1-1-2l-2 2c0 1-1 2-1 3v-3h-1l-1-1c-1 1-1 1-1 2l-2-1-2 3v1c-1 0-3 2-3 2l-4 4c-3 3-6 5-10 8-1 2-1 4-1 6v2l-1 1h-1l-1-2v3c-1-1-2-2-4-1 0 1 0 1-1 3h-2c0-1-1-2-2-3h0l-1-1c0-4-2-8-3-12 1-1 2-1 3-2 3-1 5-3 8-5h1v-1l3-5 6 3c2-1 4-2 5-3h0-1 0l-1 1h-1c-2 1-2 1-3 0l6-3 1-1-1-1-1 2-1-1h0v-2c1 0 1-1 2-1l-1-1v-1c-1-1-1-2-2-4h1c2 0 2 0 4-1 0-2 0-4 1-6l4-8 2 1h0 4 0c1-1 2-1 3-2v-3l2-1z" class="T"></path><path d="M412 651c-1 2-1 4-1 6v2l-1 1h-1l-1-2v-2c1-3 2-4 4-5z" class="B"></path><path d="M424 615l1 1h1c0 2-1 3 1 5h2 1l-3 3c-2 0-3-1-4-2v-1c0-2 0-4 1-6z" class="S"></path><path d="M430 621h0l2-2 1 1c-1 2-3 4-5 6-1 2-5 4-6 6h-1 0l1-1-1-1-1 2-1-1c3-2 6-4 8-7l3-3z" class="Q"></path><path d="M443 620h1 1l-1 1-5 10-2 2c0 1-1 2-1 3v-3h-1l-1-1c2-1 3-4 4-6 2-2 4-4 5-6z" class="i"></path><path d="M418 622h1c2 0 2 0 4-1v1c1 1 2 2 4 2-2 3-5 5-8 7h0v-2c1 0 1-1 2-1l-1-1v-1c-1-1-1-2-2-4z" class="o"></path><path d="M418 622h1c2 0 2 0 4-1v1c0 3 0 2-2 4h-1c-1-1-1-2-2-4z" class="C"></path><path d="M444 621l1 1h2l1 1-1 1v1 1c-2 1-3 3-4 5h1l-1 2h-3c-1-1-1-1-1-2l5-10z" class="c"></path><path d="M439 602c1 0 1-1 2-2l1 1c2 0 2 0 4 2v1c0 1 1 2 2 3 0 2 0 5-1 8 0 1-1 3-1 4 1 2 2 3 3 4-1 1-1 2-2 2v-1l1-1-1-1h-2l-1-1 1-1h-1-1-1l-1-1c1-1 1-2 2-3v-1c0-1 0-1-1-2l1-2v-2c-2 0-2 1-3 1v1l-1-1v-2-1l-4 1c0 2 1 2 1 4l-3 3h-1v-1c0-3 0-4 2-6 1-1 2-1 3-2v-3l2-1z" class="R"></path><path d="M439 602c1 0 1-1 2-2l1 1h1l-1 2c-1 2 0 3-1 4l-4-1v-3l2-1z" class="Q"></path><path d="M442 601c2 0 2 0 4 2v1c0 1 1 2 2 3 0 2 0 5-1 8 0 1-1 3-1 4 1 2 2 3 3 4-1 1-1 2-2 2v-1l1-1-1-1h-2l-1-1 1-1v-1l-1-1 1-1v-1l1-1-1-1c1-2 0-4 0-7 0-1-1-1 0-3h0c-1-2-1-2-2-3h-1z" class="AC"></path><defs><linearGradient id="Ar" x1="433.101" y1="717.996" x2="405.215" y2="654.626" xlink:href="#B"><stop offset="0" stop-color="#2a0705"></stop><stop offset="1" stop-color="#500a0a"></stop></linearGradient></defs><path fill="url(#Ar)" d="M412 651c4-3 7-5 10-8 0 1 0 3-2 4h0v1c-1 3-1 5-1 8h0c-1 2-1 4-1 5 1 5 1 10 2 14v5c0 1 1 1 1 2 1 2 1 4 2 7v2l4 4c2 0 3 1 4 2l1 3c2 2 3 3 5 6h0c0 2 0 4 2 5l2 1c1 1 1 1 2 1v1c1 1 1 2 3 3h1v1c-1 1-4 1-6 1-5-2-8-2-13-5-2-1-4-4-6-6-4-4-9-7-12-12-1-3-2-7-4-10h-3c-1-1 0-1-1-1h-1v-3-1l-2-3v-2l-1-1c0-1 0-1 1-1v1-1l1-1-2-1v-2h0l-1-2v-1c2-1 3-2 4-4h2c1-2 1-2 1-3 2-1 3 0 4 1v-3l1 2h1l1-1v-2c0-2 0-4 1-6z"></path><path d="M423 691l4 4c2 0 3 1 4 2l1 3-1-1c-1 0-2-1-3-1h0c-1-1-1-2-2-2l-1-1c-2-2-2-2-2-4z" class="L"></path><path d="M408 658l1 2h1l1-1v12-1h-1c0 2 0 3-1 4l-1-13v-3z" class="F"></path><path d="M437 706c0 2 0 4 2 5l2 1c1 1 1 1 2 1v1c1 1 1 2 3 3h1v1c-1 1-4 1-6 1-5-2-8-2-13-5 1 0 2 0 3 1l2 1c1 0 0-1 1 0s2 1 3 1l-2-3v-1c1 1 2 3 3 3h1v-2c-1 0-2-1-2-2v-1l-1-1h0l-1-1h1c0-1 0-2 1-3z" class="W"></path><path d="M412 651c4-3 7-5 10-8 0 1 0 3-2 4h0c-2 0-3 1-4 3h0c0 2 0 3-1 4v3c1 3 0 7 1 10l1 6c0 2-1 3-2 5 0 0 0 1-1 2s-1 1-2 1l2-1c-1-1-1-1-1-2-1-2-1-2-1-3-1-1-1-3-1-4v-12-2c0-2 0-4 1-6z" class="O"></path><path d="M412 651c4-3 7-5 10-8 0 1 0 3-2 4h0c-2 0-3 1-4 3h0c0 2 0 3-1 4v3c1 3 0 7 1 10l-1 3h0c-1-2-2-6-2-8 0-3 0-6 1-9v-1l-1-1h-1l-1 6c0-2 0-4 1-6z" class="E"></path><defs><linearGradient id="As" x1="253.422" y1="451.238" x2="262.385" y2="450.096" xlink:href="#B"><stop offset="0" stop-color="#1b0000"></stop><stop offset="1" stop-color="#5d0c0a"></stop></linearGradient></defs><path fill="url(#As)" d="M250 401c3 1 6 8 7 12l2 8c0 2 0 5 1 7l1 1c0 1 0 3 1 4 0 1 0 1-1 3v6c1 1 1 0 1 2 0 1 1 2 1 3 0 2 0 2 1 3-1 2 0 3-1 4s-1 1-1 2h-1l1 1c0 1 0 0 1 1-2 3-1 6 0 9l1 1v2 3c1 1 2 1 2 2 1-1 1-1 1-2l1 5c-1 1-1 2-1 4v2 3h0l1 1v2l2 1c2 7 2 14 4 21l6 24c-1-2-2-3-2-4v-1c-1-2 0 1-1-1v-1-1c0-1 0-2-1-3h-1c-1-1-2-2-2-3 0-2-1-4-2-6 0-1-1-2-1-3l-1 1c0-1-1-2-1-3l-1-2v-1c-1-1 0-1-1-2v-2c-1-1-1-1-1-2l-1 1h0l-3-8v6c-1-2-2-6-3-7-1 0-2 0-3 1 0-2-1-3-1-4-1-4-2-9-2-13v-33l-2-44z"></path><path d="M260 472l-1-8c-3-11-3-22-5-33v-8h0c0 2 1 4 1 6v4 1c0 2 0 3 1 4v1 1c0 2 1 4 1 6 1 0 1 1 2 3l1 11v12z" class="T"></path><path d="M257 446c0-2 0-2 1-3-1-1 0-1-1-2h1v-2h1v9c1 1 1 3 1 5h0c0 1 0 1 1 2 0 0 1 0 1 1h-1l1 1c0 1 0 0 1 1-2 3-1 6 0 9l1 1v2 3c1 1 2 1 2 2 1-1 1-1 1-2l1 5c-1 1-1 2-1 4v2 3c-1-1-1-2-1-3-2-4-2-8-2-12h-1c0-2 0-2-1-3-2-3-1-6-2-9l-1-11c-1-2-1-3-2-3z" class="V"></path><path d="M252 445c2 10 4 20 5 30l4 20v6c-1-2-2-6-3-7-1 0-2 0-3 1 0-2-1-3-1-4-1-4-2-9-2-13v-33z" class="O"></path><path d="M254 491l1-1c1-4 0-7 0-10 0-1 1-3 0-4 0-1 0-3 1-5v1c0 1 0 2 1 3h0l4 20v6c-1-2-2-6-3-7-1 0-2 0-3 1 0-2-1-3-1-4z" class="K"></path><path d="M260 460c1 3 0 6 2 9 1 1 1 1 1 3h1c0 4 0 8 2 12 0 1 0 2 1 3h0l1 1v2l2 1c2 7 2 14 4 21l6 24c-1-2-2-3-2-4v-1c-1-2 0 1-1-1v-1-1c0-1 0-2-1-3h-1c-1-1-2-2-2-3 0-2-1-4-2-6 0-1-1-2-1-3l-1 1c0-1-1-2-1-3l-1-2v-1c-1-1 0-1-1-2v-2c-1-1-1-1-1-2 0-4-2-8-2-11v-2c-1-4-1-7-2-10 0-3-1-5-1-7v-12z" class="I"></path><path d="M267 487l1 1v2l2 1c2 7 2 14 4 21l6 24c-1-2-2-3-2-4v-1c-1-2 0 1-1-1v-1-1c0-1 0-2-1-3h-1c-1-1-2-2-2-3 0-2-1-4-2-6 0-1-1-2-1-3l-1 1c0-1-1-2-1-3l-1-2v-1c-1-1 0-1-1-2v-4h0 0v1c1 1 1 0 1 1 0 2 1 2 2 4 1 1 1 2 2 4v1c1 4 3 7 4 11v-2c0-1 0-2-1-3 0-1-1-3-1-5v-1c-1-1-1-2-1-4 0-1-1-3-1-4s-2-1-2-2v-3l-2-9v-4z" class="V"></path><defs><linearGradient id="At" x1="632.482" y1="583.713" x2="634.223" y2="577.562" xlink:href="#B"><stop offset="0" stop-color="#b32124"></stop><stop offset="1" stop-color="#ed3b3e"></stop></linearGradient></defs><path fill="url(#At)" d="M621 531l1 2c1 2 2 3 3 5 2 1 4 2 5 3v2h1c1 1 1 3 2 4v1c-1 2-2 5-1 7l2 3-2 1c2 4 4 7 7 10 4 3 8 6 11 10h0c2 4 6 6 10 8-1 1-2 1-2 2 3 1 6 3 9 4s6 2 10 4h-4l-66-25-1-1h0l1-1c2 1 3 1 4 1h1c-3-1-5-2-7-2l3-1h0v-1c-1-1-2-1-3-2l1-1 1-1 1 1 1-1v-1c3 0 5-1 7-3v-1-4h0c0-4-1-8-2-12l-1-3 1-4 1-3 2 1 1 2 1-2 1 3c0-2 0-4 1-5z"></path><path d="M650 579h0c2 4 6 6 10 8-1 1-2 1-2 2l-2-1c-4-2-9-3-13-5l1-1c1 0 2-1 3-2 1 0 1 0 3-1z" class="m"></path><path d="M616 559l3 1v1l-1 2 1 1h4v1c1 1 2 2 2 3 1 0 2 1 3 2h-1c-1 0 0 0-1 1h0 1l1 1-1 2c7 3 13 7 20 6-1 1-2 2-3 2l-1 1-29-12h-2c-3-1-5-2-7-2l3-1h0v-1c-1-1-2-1-3-2l1-1 1-1 1 1 1-1v-1c3 0 5-1 7-3z" class="a"></path><path d="M627 574h-1c-2 0-3-1-5-2h0c-2-1-3-1-4-2l2-2h2c1 0 1 1 2 1s1 0 2 1h0v-2c1 0 2 1 3 2h-1c-1 0 0 0-1 1h0 1l1 1-1 2z" class="M"></path><path d="M616 559l3 1v1l-1 2 1 1h4v1 2h-1c-1-1-1-1-2-1v1c-2 1-7 0-10 0l5 3c-1 1 0 1-1 1h-2c-3-1-5-2-7-2l3-1h0v-1c-1-1-2-1-3-2l1-1 1-1 1 1 1-1v-1c3 0 5-1 7-3z" class="Q"></path><path d="M610 567c-1 0-1 0-2-1h4c0-1-1-1-2-2 3-1 6-1 8-3h1l-1 2 1 1h4v1 2h-1c-1-1-1-1-2-1v1c-2 1-7 0-10 0z" class="F"></path><path d="M626 555h3c1 0 2 2 3 4 2 4 4 7 7 10 4 3 8 6 11 10-2 1-2 1-3 1-7 1-13-3-20-6l1-2-1-1h-1 0c1-1 0-1 1-1h1c-1-1-2-2-3-2 0-1-1-2-2-3v-1l-3-4h1s1 1 2 1v-1h1c1 0 1 1 2 1h0l1 1c1 0 2 1 3 1-1-3-3-5-4-8z" class="AA"></path><path d="M635 571l-1 1-1-1-1 1c-2 0-2-1-3-2v-1l3 1 3 1z" class="y"></path><path d="M627 562c1 0 2 1 3 1l5 6c1 1 0 1 1 2h-1 0l-3-1c-2-3-4-5-5-8z" class="F"></path><path d="M626 555h3c1 0 2 2 3 4 2 4 4 7 7 10-1 1-1 1-1 2v1c-1 0-2 0-3-1h0 1c-1-1 0-1-1-2l-5-6c-1-3-3-5-4-8z" class="M"></path><path d="M621 531l1 2c1 2 2 3 3 5 2 1 4 2 5 3v2h1c1 1 1 3 2 4v1c-1 2-2 5-1 7l2 3-2 1c-1-2-2-4-3-4h-3c1 3 3 5 4 8-1 0-2-1-3-1l-1-1h0c-1 0-1-1-2-1h-1v1c-1 0-2-1-2-1h-1l3 4h-4l-1-1 1-2v-1l-3-1v-1-4h0c0-4-1-8-2-12l-1-3 1-4 1-3 2 1 1 2 1-2 1 3c0-2 0-4 1-5z" class="G"></path><path d="M626 561v-3l-2 1h-4c1-1 0-1 1-2 0-1 0-1 1-2h1l-1 1 1 1c1-1 1-2 2-3h0l1 1h0c1 3 3 5 4 8-1 0-2-1-3-1l-1-1zm-7-28l1 3 4 11c0 1 0 2-1 2-2 2-2 3-2 5-1 1-1 1-2 1v-1c0-2-1-4-2-6 1 0 2 0 3-1l-1-1 1-1h1v-2c0-1-1-2-1-2-1-1-2-4-2-6l1-2z" class="J"></path><path d="M621 531l1 2c1 2 2 3 3 5 2 1 4 2 5 3v2c-2 0-3 1-4 1v4l-2-1-4-11c0-2 0-4 1-5z" class="s"></path><path d="M615 532l2 1 1 2c0 2 1 5 2 6 0 0 1 1 1 2v2h-1l-1 1 1 1c-1 1-2 1-3 1l-1-2-1-6h-1v2l-1-3 1-4 1-3z" class="V"></path><path d="M616 546l1-1c0-1 1-2 1-2l1-1 1 3-1 1 1 1c-1 1-2 1-3 1l-1-2z" class="H"></path><path d="M614 535c2 1 2 1 2 3 0 1 0 1-1 2h-1v2l-1-3 1-4z" class="K"></path><path d="M630 543h1c1 1 1 3 2 4v1c-1 2-2 5-1 7l2 3-2 1c-1-2-2-4-3-4h-3 0l-1-1c1-2 1-4 1-6v-4c1 0 2-1 4-1z" class="S"></path><path d="M346 313h1c2 1 3 1 4 1 2 1 4 3 5 5 1 1 1 2 2 3 1 2 3 5 5 6v-1l2-4v-2l3 8-1 2c1 4 2 8 1 12l6 49 1 4-2-1c0-1-1-2-2-2-2-3-5-5-7-8-1-1-2-3-3-4l-8-11h1c1 0 1 0 2 1 0-1-1-3-2-4-2-3-3-5-4-9l2 2s2 0 3-1c-1 0-1-1-1-2h0-1v-3l-3-15-3-18c0-2 0-2-1-4v-4z" class="J"></path><path d="M359 369c0-1 1-2 2-3 1 3 1 5 3 7l-1 1h0c-3-2-3-3-4-5z" class="O"></path><path d="M358 361c1 1 2 0 3 2-1 1-1 2 0 3-1 1-2 2-2 3v-2c-1-1-2-3-2-4l1-2z" class="j"></path><path d="M356 353c1 1 2 2 2 4v4l-1 2-3-7 2-3z" class="Q"></path><path d="M361 342h2 1c-1-1-1-2-2-4 1-1 1-1 2-1l1 3 1-2v4-1c-1 1-2 1-2 2 0 2 1 5 1 7-2-2-3-4-5-6l1-1v-1z" class="H"></path><path d="M350 339c2 1 1-1 3-1 1 0 2 1 3 1-2 1-2 2-2 4h-1c0 3 1 5 2 7 1 1 1 2 1 3l-2 3-1-2-3-15z" class="V"></path><path d="M356 371l1 1v1c0 1 2 3 3 5 2 0 3 2 5 4 1 1 3 2 4 3l-1-19v-1l2 14c0 5 1 9 3 13h1l1 4-2-1c0-1-1-2-2-2-2-3-5-5-7-8-1-1-2-3-3-4l-8-11h1c1 0 1 0 2 1z" class="Q"></path><path d="M368 343l6 49h-1c-2-4-3-8-3-13l-2-14c1-3-1-8-1-11l-1-4 1-1v-2c1-2 1-3 1-4z" class="m"></path><path d="M346 313h1c2 1 3 1 4 1 2 1 4 3 5 5 1 1 1 2 2 3 1 2 3 5 5 6v-1l2-4v-2l3 8-1 2-1-1v-1c0-1 0-2-1-3v-1 2c1 2 0 5 0 7 1 2 1 3 1 4l-1 2-1-3c-1 0-1 0-2 1 1 2 1 3 2 4h-1-2c-1-1-2-2-2-3-1-1 0-1-1-2l-2 1v1c-1 0-2-1-3-1-2 0-1 2-3 1l-3-18c0-2 0-2-1-4v-4z" class="I"></path><defs><linearGradient id="Au" x1="588.731" y1="579.744" x2="553.99" y2="547.022" xlink:href="#B"><stop offset="0" stop-color="#2e2927"></stop><stop offset="1" stop-color="#564d41"></stop></linearGradient></defs><path fill="url(#Au)" d="M591 480l1 1c0-1 0-2 1-2v-1h1 1 2v3c1 3 1 4 3 7l-1 2-9 34c0 3 0 4-1 6s-2 5-2 7l-1 6-1 3-1 3-2 7-4 12c0 1-1 4-1 5l-11 37-2-1c-3-2-4-6-6-9l-1-3v-1l2-9 2-5 5-18c0-1 1-3 1-5l4-13 8-28c0-2 1-3 1-5v-3c1-2 2-5 3-7l3-11c2-1 2-2 3-4v-1c0-1 0-1 1-2v-1-1c0-2 0-2 1-3z"></path><path d="M586 543l-1 3-1 3-2 7v-7c0-2 1-3 1-4v-1l3-1z" class="g"></path><path d="M557 596c1-1 1-1 1-2 1 0 1 0 2-1l1 1 1-1v-2h0c1 2 3 3 1 6v1 1c-1 0-2 0-2-1h-1l-2 2-1-3v-1z" class="l"></path><path d="M561 598c0 2 0 4 1 5 0 1 1 1 1 2s0 2 1 2h1c0-2 1-5 1-7 0-1 0-3 1-4s1 0 1-1c1-3 3-6 3-8 0-3 0-5 2-7 1-2 2-5 3-8l1 1-11 37-2-1c-3-2-4-6-6-9l2-2h1z" class="N"></path><path d="M561 582l5-18v5h0c1 3-1 6-1 8 1 3 0 8-1 11 0 1 0 2-2 3h0v2l-1 1-1-1c-1 1-1 1-2 1 0 1 0 1-1 2l2-9 2-5z" class="w"></path><defs><linearGradient id="Av" x1="599.281" y1="521.367" x2="577.774" y2="499.636" xlink:href="#B"><stop offset="0" stop-color="#2d2a2b"></stop><stop offset="1" stop-color="#474035"></stop></linearGradient></defs><path fill="url(#Av)" d="M591 480l1 1c0-1 0-2 1-2v-1h1 1 2v3c1 3 1 4 3 7l-1 2-9 34c-3 5-4 10-5 16-2-8-4-18-5-27v-3c1-2 2-5 3-7l3-11c2-1 2-2 3-4v-1c0-1 0-1 1-2v-1-1c0-2 0-2 1-3z"></path><path d="M591 480l1 1c0-1 0-2 1-2v-1h1 1 2v3c1 3 1 4 3 7l-1 2h-2v1-1 1l-1 1v-1l-1-1c0 1 0 1-1 2v-1c-2 2 0 2-2 4l-1-1-1 1v-2l-1-2c1-1 0-2 0-3v-1c0-1 0-1 1-2v-1-1c0-2 0-2 1-3z" class="P"></path><path d="M597 481c1 3 1 4 3 7l-1 2h-2v-9z" class="l"></path><path d="M590 493c0-2 0-2 1-3l1-1c0-2 0-3-1-5 0-1 1-3 3-4v1l1 9c0 1 0 1-1 2v-1c-2 2 0 2-2 4l-1-1-1 1v-2z" class="N"></path><path d="M579 675v-2h1l1 1c1 0 1 0 1 1 1 0 2 1 3 1l2-1 1 1h3c1 1 1 2 3 3 0 1 0 1 1 2h1c0 2 0 2 1 3 3 2 6 5 10 5 1 0 5-4 5-5l1-2h-1-2l1-1-1-1c1-1 2-2 3-2 2-2 3-3 3-5v2c1 1 1 1 1 2h-1l-1 2v1h1l1-1h-1l1-1 2 1-1 1c0 2 0 3 1 5v2l-1 1c0 3-1 5-2 7l-1 1h1 0c-2 6-7 10-11 15-2 2-3 3-5 3-1 1-2 1-2 1-1 1-2 1-3 1s-1 0 0-1c0-1-1-2-1-2h-2c-1 1-1 1-2 1h0l-1 1v-1l4-4-1-1c-2 2-3 4-5 6v1c-3 2-5 2-8 2l-5 1c-2-1-5 0-6-1v-2-1-1c0-1 0-1 1-2s1-3 1-4l2-2h-1c-2 0-3 2-4 4h0c-1 1-1 2-2 2h0v2h0c-2 2-4 4-4 7 0 1 0 1-1 1-1 2-2 5-2 7 0 1 0 2-1 2v2c0 1-1 2-1 3h-1l-2-2c-1 1-1 1-1 2v1h-1l9-25h0c1-2 1-3 1-5h0-1 0c1-2 3-4 4-6l-1-2c2-2 4-4 6-7 1-1 1-2 2-4l3-3h0c0 2-2 4-2 6v1l2-3c1-1 1-2 3-2l1-1-1-2-1-1c0-1 0-2 1-2l2-6z" class="o"></path><path d="M578 686c1 1 2 2 4 3h0v1c-1 0-1 1-2 2v-1l-3-4 1-1zm17-1v2l2-1 1 1h-2c-1 2-2 3-3 3-2 0-2 0-4-1 1-1 3-1 4-2l2-2z" class="S"></path><path d="M598 715h0l1-2c1-1 3-4 5-4l1 2c-2 2-3 3-5 3-1 1-2 1-2 1z" class="n"></path><path d="M585 692c2 1 3 3 5 4 0 3 0 4-2 6v1l-1-2 1-2c-1-2-3-4-4-6h0l1-1zm2-14c2 1 3 1 4 2h0l-2 1c0 3-2 5-4 7 0 1-1 1-1 2l-2-1h0c1-2 3-4 4-6 0-2 1-3 1-5z" class="T"></path><path d="M579 675v-2h1l1 1c1 0 1 0 1 1 1 0 2 1 3 1l2-1 1 1h3c1 1 1 2 3 3 0 1 0 1 1 2 0 2 0 2-1 3-1-1-2-3-3-4h0c-1-1-2-1-4-2-3 0-5-2-7-3h-1z" class="O"></path><path d="M613 682h1 0l2-1 1 1v1-1h-1c-1 1-2 2-2 4-1 1-3 4-5 5-3 1-5-1-8-2-1 0-2-1-3-2l-1-1-2 1v-2l-1-1c1-1 1-1 1-3h1c0 2 0 2 1 3 3 2 6 5 10 5 1 0 5-4 5-5l1-2z" class="I"></path><path d="M587 716h-1c0-1 0-2-1-2-1-1-1 0-2-1 0-2 2-1 3-2 3-2 5-6 6-9 2-3 4-5 8-6v1s-2 1-2 2v2s-1 1-1 2l-5 6c-2 2-3 4-5 6v1z" class="L"></path><path d="M579 675h1c2 1 4 3 7 3 0 2-1 3-1 5-1 2-3 4-4 6-2-1-3-2-4-3l-1-2-1-1c0-1 0-2 1-2l2-6z" class="o"></path><path d="M582 689l2 1c0 1 1 2 1 2l-1 1h0c1 2 3 4 4 6l-1 2 1 2c-1 1-2 1-3 2h0l-1-1c-1 0-1 0-2-1-3-1-4 1-7 2l-2-2h-1v-1c1-2 3-4 4-5 1 0 1 0 2-1 0-2 1-3 2-4s1-2 2-2v-1z" class="L"></path><path d="M582 689l2 1c0 1 1 2 1 2l-1 1c-2-1-2-2-2-3v-1z" class="Q"></path><path d="M587 701h-1c-3 0-5-1-8-3l2-3c1-1 2-1 4-2 1 2 3 4 4 6l-1 2z" class="AC"></path><defs><linearGradient id="Aw" x1="363.324" y1="438.166" x2="347.872" y2="509.607" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#6b100f"></stop></linearGradient></defs><path fill="url(#Aw)" d="M342 436c2-1 3-1 5-3 3 0 5 2 7 3l4 5 2 2c4 2 8 3 12 5l5 3v8l1 1c0 1-1 4 0 5 0 1 1 1 1 2s-1 2 0 4c0 1 0 1 1 3v2 2c1 0 2 0 3 1l1-1v-2c1 0 1-1 1-1 3 0 4 0 6 1l1 1c1 0 1 0 2 1h0l1-1 1-1 2-5v6l-2 1v1h1l-2 1h0-1c-1-1-2-1-3-2 0-1-1-1-2-1s-1-1-2-1-1 0-2 1v1c0 1-1 1-1 2-2 0-2 0-3-1h-1v3c-1 0-1 1-1 1l-2 2c-1-1-1-2-2-3l1-1-6-6h0c-1-1-2-1-3-1 0 0-1 0-2-1h0l-1 2v1l-2-1v-2c-2-1-3 0-5-1v2h0c1 5 5 11 5 16l-1 1c-1-2-2-4-3-5l-1 1c-1-1 0-2-1-2-1 1-1 1-1 3-1 2 2 4 3 6 0 1 0 0-1 1h0c0 1-1 1-2 2v-1-1-1c-1 2-1 3-3 3l2 1c-2 0-2 0-3 2 0 2 1 5 1 7v2 2h0v-1c-2-3-2-5-3-8v1c-1 0 0-3 0-4 0-5 0-11 1-16v-3c-1 1-1 2-2 3h-2v-2h-2v1c0 1 0 1-1 2-1 2-2 4-2 7l-2 2c-1-1-1 0-2-1h-1l1-1-1-1c-1-2-1-3-1-5v-1c-3-7-4-16-5-24l-1-3c1 1 1 2 2 3l10-11-1-11v-1-1h2z"></path><path d="M352 497v-5h1l2 2c-1 2-1 3-3 3z" class="H"></path><path d="M357 474l-1-2 1-1c3 0 5 1 8 2h4c3 2 5 5 8 6 1-3 0-7 0-11 1 2 1 4 1 6l2 5v3c-1 0-1 1-1 1l-2 2c-1-1-1-2-2-3l1-1-6-6h0c-1-1-2-1-3-1 0 0-1 0-2-1h0l-1 2v1l-2-1v-2c-2-1-3 0-5-1v2z" class="Q"></path><path d="M342 436l2 21 1 4v1c1 1 2 2 2 4h0c0 1 1 1 1 3 0 1 1 3 1 5l2 1c0 1-1 2-1 3 1 2 1 3 1 5h-1v-3c-1 1-1 2-2 3h-2v-2h-2v1c0 1 0 1-1 2-1 2-2 4-2 7l-2 2c-1-1-1 0-2-1h-1l1-1-1-1c-1-2-1-3-1-5v-1c-3-7-4-16-5-24l-1-3c1 1 1 2 2 3l10-11-1-11v-1-1h2z" class="O"></path><path d="M346 481l3-3h0l1 2c-1 1-1 2-2 3h-2v-2z" class="e"></path><path d="M339 480v3h1v-1c1-1 2-1 3-2l1 1v1c-3 1-5 4-6 6 0-1 0-2-1-3 0-2 1-4 2-5z" class="H"></path><path d="M339 480c0-2 2-6 3-7 1 1 1 0 1 1-1 2 0 3 0 5v1c-1 1-2 1-3 2v1h-1v-3z" class="E"></path><path d="M344 482c0 1 0 1-1 2-1 2-2 4-2 7l-2 2c-1-1-1 0-2-1h-1l1-1 1-3c1-2 3-5 6-6z" class="a"></path><path d="M342 436l2 21c-1 1 0 1 0 2 0 4-4 7-7 9-1 0-2 1-2 2-1 1-1 3 0 5v1c1 2 1 3 1 5s0 4 1 5v1c-1 1 0 1-1 1v-1c0-1 0-2-1-3-3-7-4-16-5-24l-1-3c1 1 1 2 2 3l10-11-1-11v-1-1h2z" class="c"></path><path d="M332 135h1c2 13 4 26 7 39-1 1-1 3-1 5 1 1 1 3 1 4 1 0 2 1 3 1v-1l2 4v2c0 8 0 17 2 24l-1 1-1-3-3-3v-1c0-1-1-3-1-4-3-5-5-9-8-13-3-1-5-3-8-5-2 1-3 1-5 2s-3 2-5 3c-3 5-5 10-9 14l-1 2h-1c-1 1 0 1-1 2v1 1h-1c-1 1 0 3-2 5l-3-3-1-1h-1c-1-1-2-2-4-3l-1-1c-1 2-1 6-2 8v-2-1-1-8c0-1 1-2 1-3l3-7c1-1 1-2 1-3l4-6c4-9 9-18 15-26 1-3 3-6 6-9 4-5 8-10 13-14h1z" class="j"></path><path d="M306 201c2-4 3-7 5-10 1 0 1 1 2 2 0-1 1-2 2-3-3 5-5 10-9 14v-3z" class="J"></path><defs><linearGradient id="Ax" x1="337.184" y1="170.229" x2="315.992" y2="173.201" xlink:href="#B"><stop offset="0" stop-color="#1d0201"></stop><stop offset="1" stop-color="#340606"></stop></linearGradient></defs><path fill="url(#Ax)" d="M332 135h1c2 13 4 26 7 39-1 1-1 3-1 5 1 1 1 3 1 4 1 0 2 1 3 1v-1l2 4v2c0 8 0 17 2 24l-1 1-1-3-3-3v-1c0-1-1-3-1-4-3-5-5-9-8-13 1-1 2 1 4 1l-2-4c-1-2-1-4-2-6-1-8-1-15-2-23-1-3-2-9-4-10-3 0-5 2-7 5l-3 5c-1 1-1 2-3 2h-1c1-1 1-3 2-4 1-2 3-4 3-7 4-5 8-10 13-14h1z"></path><path d="M343 183l2 4v2c0 8 0 17 2 24l-1 1-1-3c-1-8-4-16-4-24l-1-4c1 0 2 1 3 1v-1z" class="AE"></path><path d="M343 183l2 4v2c-1 1 0 2-1 3l-2-1c0-1 0-3-1-4l-1-4c1 0 2 1 3 1v-1z" class="AB"></path><path d="M332 135h1c2 13 4 26 7 39-1 1-1 3-1 5v-1c-2-4-3-10-4-14-1-8-4-17-4-25 0-1 0-3 1-4z" class="AF"></path><defs><linearGradient id="Ay" x1="300.077" y1="177.195" x2="313.604" y2="183.053" xlink:href="#B"><stop offset="0" stop-color="#490605"></stop><stop offset="1" stop-color="#800e0d"></stop></linearGradient></defs><path fill="url(#Ay)" d="M292 193c1-1 1-2 1-3l4-6c4-9 9-18 15-26 1-3 3-6 6-9 0 3-2 5-3 7-1 1-1 3-2 4h1c2 0 2-1 3-2l3-5c2-1 2-1 3-1v3l1 2v1c-2 3-5 7-8 10-5 9-8 18-11 28v4c1 0 1 1 1 1v3l-1 2h-1c-1 1 0 1-1 2v1 1h-1c-1 1 0 3-2 5l-3-3-1-1h-1c-1-1-2-2-4-3l-1-1c-1 2-1 6-2 8v-2-1-1-8c0-1 1-2 1-3l3-7z"></path><path d="M303 203v-5l2-2v4l-2 3z" class="E"></path><path d="M305 200c1 0 1 1 1 1v3l-1 2c-2-1-2-1-3-2l1-1 2-3z" class="H"></path><defs><linearGradient id="Az" x1="357.455" y1="513.123" x2="390.96" y2="510.35" xlink:href="#B"><stop offset="0" stop-color="#4c0c0b"></stop><stop offset="1" stop-color="#7a0d0e"></stop></linearGradient></defs><path fill="url(#Az)" d="M357 474v-2c2 1 3 0 5 1v2l2 1v-1l1-2h0c1 1 2 1 2 1 1 0 2 0 3 1h0l6 6-1 1c1 1 1 2 2 3l2-2s0-1 1-1v-3h1c1 1 1 1 3 1 0-1 1-1 1-2v-1c1-1 1-1 2-1s1 1 2 1 2 0 2 1c1 1 2 1 3 2h1 0l2-1h-1v-1l2-1c-1 2-1 3 0 5l-1 1s0 1-1 2v1 4c-1 2 0 4 0 5 0 3-1 6 0 8s1 4 2 6l-1 1v1l1 1 1 1c-1 1-1 1-2 1-1 1-2 1-3 2l1 1h1v3l-1-1c-1 2-1 2-1 4v1c-2 4 0 10 0 14v1c0 2 0 5-1 8-1 2-2 3-2 5s-2 3-3 4c-1 4-3 7-6 10 0-4-1-7-2-11l-3-3c-1 1-1 2-1 4-1-2-1-2-1-4-2 0-2-1-3-2v-4c-1-2-1-4-2-6v-1c0-1 0-2-1-4-2-3-4-7-7-10-2-2-4-4-5-7-2-1-3-3-5-5h0c-2-3-3-6-3-10v-1c1 3 1 5 3 8v1h0v-2-2c0-2-1-5-1-7 1-2 1-2 3-2l-2-1c2 0 2-1 3-3v1 1 1c1-1 2-1 2-2h0c1-1 1 0 1-1-1-2-4-4-3-6 0-2 0-2 1-3 1 0 0 1 1 2l1-1c1 1 2 3 3 5l1-1c0-5-4-11-5-16h0z"></path><path d="M369 514c2 3 2 7 4 10v1l2 6c-1-1-2-2-2-3v-1c0-1-1-1-1-2-1 0-1-1-2-1v-3-2c-1-2-1 0-1-1v-4z" class="E"></path><path d="M364 520h1s0-1 1-1l4 14h-1c0-2-1-3-2-5s-1-3-3-4v-4z" class="m"></path><path d="M364 524c2 1 2 2 3 4s2 3 2 5h1l1 4-2-2c-2-3-4-7-7-10l2-1z" class="F"></path><path d="M369 535l2 2c1 1 1 3 2 4v4c1 3 2 4 2 7-2 0-2-1-3-2v-4c-1-2-1-4-2-6v-1c0-1 0-2-1-4z" class="h"></path><path d="M373 502l2 1c1 5 0 9 1 14 0 1 0 1-1 2h-2l-3-10v-1l3-6z" class="M"></path><path d="M365 493c1 0 2-1 3-1 2 3 3 9 5 10l-3 6v1c-2-5-3-11-5-16z" class="a"></path><path d="M382 502l1 3c0 2 2 5 2 7v1c1 1 0 2 0 3h1v3h1 1v1c0-1 0-3-1-4l1-1c1 1 1 2 2 4h0l-1 1c0 2 1 5 2 8h-3v1l-3-3v-1c-1-1-1-2-1-2 0-2 0-2-1-3v-2l-1-4c0-1 1-2 1-4h-1l1-1-1-3c0-1 0-2-1-3v-1h1z" class="B"></path><path d="M382 502c-1-2-1-5-2-7h1c1 5 3 9 5 14 0 1 1 3 1 4 1 0 1 0 2 1 1 0 2 0 3-1h4v-1l1-1 1 1 1 1c-1 1-1 1-2 1-1 1-2 1-3 2l1 1-3 2v3h-1l-1-3h0c-1-2-1-3-2-4l-1 1c1 1 1 3 1 4v-1h-1-1v-3h-1c0-1 1-2 0-3v-1c0-2-2-5-2-7l-1-3z" class="k"></path><path d="M360 512v-1c0-1 1-1 1-2l-2-2s-1 1-2 0v-1l3-3 6 16c-1 0-1 1-1 1h-1v4l-2 1c-2-2-4-4-5-7h0 0l3-6z" class="h"></path><path d="M357 518l1 1c1-1 2-5 4-5 0 2-1 3 0 5h0c1 0 2 1 2 1v4l-2 1c-2-2-4-4-5-7h0 0z" class="J"></path><path d="M369 514c-1-3-3-4-5-6 0-4-2-7-4-10 2-3 2-4 5-5 2 5 3 11 5 16l3 10h0v5c-2-3-2-7-4-10zm-16-9c-1-2-1-4-1-6l2-1c1-1 3-2 4-1 1 2 2 4 2 6l-3 3v1c1 1 2 0 2 0l2 2c0 1-1 1-1 2v1l-3 6h0 0c-2-1-3-3-5-5h0c-2-3-3-6-3-10v-1c1 3 1 5 3 8v1h0v-2l1-4z" class="F"></path><path d="M357 514c0-1 0-2-1-3h0c1-2 1-2 3-2 0 1 0 2 1 3l-3 6h0v-4z" class="K"></path><path d="M349 503v-1c1 3 1 5 3 8v1h0v-2l1-4v3c1 2 1 4 3 6h1v4h0c-2-1-3-3-5-5h0c-2-3-3-6-3-10z" class="H"></path><path d="M395 517h1v3l-1-1c-1 2-1 2-1 4v1c-2 4 0 10 0 14v1c0 2 0 5-1 8-1 2-2 3-2 5s-2 3-3 4v-4c-1 0-1 0-1-1l1-1v-1l-1 1v-1 2l-2 1 1-1c0-2-1-3-1-5-1-1 0-1 0-2-1-3-2-4-2-7l1-1c0-1 0-2-1-3l1-1c1 2 1 4 2 6l2-2c0 1 0 1 1 2 0 1 0 2 1 3l2 3h1v-2c1-1 0-1 0-2-1-1 0-1-1-1 0-1 0-1 1-2h0v-2l-1 2h-1v-2c1-1 1-1 1-2l-1 1v-2-4c-1-3-2-6-2-8l1-1 1 3h1v-3l3-2z" class="K"></path><path d="M383 537l1-1c0-1 0-2-1-3l1-1c1 2 1 4 2 6 1 1 1 2 1 4h1c1 1 1 4 1 5 1 2 1 3 1 4l-1 1-1-2v-1l-1 1v-1 2l-2 1 1-1c0-2-1-3-1-5-1-1 0-1 0-2-1-3-2-4-2-7z" class="F"></path><path d="M376 517c1 2 1 3 2 5 1 3 2 4 2 7 0 1 1 1 1 2v1c1 2 2 3 2 5 0 3 1 4 2 7 0 1-1 1 0 2 0 2 1 3 1 5l-1 1 2-1v-2 1l1-1v1l-1 1c0 1 0 1 1 1v4c-1 4-3 7-6 10 0-4-1-7-2-11v-4c-1-1-1-1-1-2s0-1-1-2v-2c-1-1-1 0-1-1v-1h-1v-4c1-1 1-2 1-3l-2-5-2-6v-1-5h0 2c1-1 1-1 1-2z" class="h"></path><path d="M376 517c1 2 1 3 2 5 1 3 2 4 2 7 0 1 1 1 1 2v1l-1 1-1-3c0-2 0-2-1-3-1 1 1 5 1 6l-1 1c-1-2-1-4-2-5 0-1-1-2-1-3s-1-1-2-1v-1-5h0 2c1-1 1-1 1-2z" class="D"></path><path d="M376 517c1 2 1 3 2 5 0 1-2 1-3 2 0-2-1-3-2-5h0 2c1-1 1-1 1-2z" class="G"></path><path d="M377 536l4 12c1 2 3 4 4 6h0c1-1 0-1 0-2l2-1v-2 1l1-1v1l-1 1c0 1 0 1 1 1v4c-1 4-3 7-6 10 0-4-1-7-2-11v-4c-1-1-1-1-1-2s0-1-1-2v-2c-1-1-1 0-1-1v-1h-1v-4c1-1 1-2 1-3z" class="E"></path><path d="M380 479h1c1 1 1 1 3 1 0-1 1-1 1-2v-1c1-1 1-1 2-1s1 1 2 1 2 0 2 1c1 1 2 1 3 2h1 0l2-1h-1v-1l2-1c-1 2-1 3 0 5l-1 1s0 1-1 2v1 4c-1 2 0 4 0 5 0 3-1 6 0 8s1 4 2 6l-1 1v1l-1 1v1h-4c-1 1-2 1-3 1-1-1-1-1-2-1 0-1-1-3-1-4-2-5-4-9-5-14 0-4-1-8-1-12v-1-3z" class="K"></path><path d="M391 495l-1 1h-1v-2c0-2 0-2-1-2-1 2 0 5-2 6h-1c-1-2 0-4-1-6v-2c2-1 3-3 5-3 1 2 1 5 2 8z" class="F"></path><path d="M380 479h1c1 1 1 1 3 1 0-1 1-1 1-2v-1c1-1 1-1 2-1s1 1 2 1 2 0 2 1c1 1 2 1 3 2h1 0l2-1h-1v-1l2-1c-1 2-1 3 0 5l-1 1s0 1-1 2v1 4c-1 2 0 4 0 5 0 3-1 6 0 8s1 4 2 6l-1 1-6-15c-1-3-1-6-2-8v-1c-1-1-2 0-3 1-2-1-2-1-4-3 0 0-1 0-2-1v-1-3z" class="T"></path><path d="M350 480v3c-1 5-1 11-1 16 0 1-1 4 0 4 0 4 1 7 3 10h0c2 2 3 4 5 5 1 3 3 5 5 7 3 3 5 7 7 10 1 2 1 3 1 4v1c1 2 1 4 2 6v4c1 1 1 2 3 2 0 2 0 2 1 4 0-2 0-3 1-4l3 3c1 4 2 7 2 11 3-3 5-6 6-10 1-1 3-2 3-4h1c-1 1 0 2-1 3l-1 1-1 1c0 1-1 2-1 3-1 1-1 2-2 3h0c-2 2-4 3-5 5-1 1-2 1-3 2-1 2-3 4-5 5-1 1-2 1-2 2-1 2-4 3-6 4-2 0-3 1-5 2 0-1 0-2-1-2 0-1-1-2-1-3-1-1-1-3-1-4h-1c0 1 0 1-1 1v1 4c0 2 0 3-1 5-2 2-5 2-8 2-2 0-4 1-5 0v-3c1-7-1-16 0-23 0-3 1-9 2-11s1-3 1-5c1-2 2-5 2-8l1-2v-2h1v-2h1l1-2v-1c0-1 0-2 1-2v-1c1-1 1-2 2-3h0v-2c-1-1-1-1-1-2h-1c-1 1-3 3-3 5-2 4-4 9-5 13v1h0c0 3-1 4-2 6-1 3-1 6-2 9-1 4 0 9-1 12-1 2-1 5-1 6 0 2 0 2-1 3h-1c-2-1-3-3-4-5v-1l-2-2c-1-1-1-5-1-7v-11c0-2 1-4 0-5v-1-2h0v-2-5c1-1 0-2 0-3h1v-3l-3 3s0 1-1 2h0v4 1 2l-1 1v3l-3-25h0v-3h1c0 4 1 7 2 11h1c0-1 1-3 2-4s1-2 2-3c1-2 1-3 2-5v-1c1-2 1-5 2-7v-1c1-2 1-3 2-5v-1l1-6h1c1 1 1 0 2 1l2-2c0-3 1-5 2-7 1-1 1-1 1-2v-1h2v2h2c1-1 1-2 2-3z" class="Q"></path><path d="M333 505l2 2c-1 2-2 6-2 9h-1 0l-1-3v-1c1-2 1-5 2-7zm17 8l1 1 1 1-5 6h0c1-1 1-2 1-3h-1c-1 1-1 2-1 3h-1c1-1 1-3 1-4l1-1 3-3z" class="B"></path><path d="M335 499h0l3-3h0l1 1h-1c-3 3-1 7-3 10l-2-2v-1c1-2 1-3 2-5z" class="D"></path><path d="M363 576h-1c0-2-1-3-2-3 0-2 0-2 1-3l1-1c2 0 2 1 3 2v5h-2z" class="R"></path><path d="M333 516v1 4h0c-1 5 0 10-3 14h-1c0-2 0-4 1-6v-1c0-2 1-4 0-5v-1c1-2 2-3 3-6z" class="E"></path><path d="M329 518c1-2 1-3 2-5l1 3h0 1 0c-1 3-2 4-3 6v1l-4 4h-1v-2c0-1 1-3 2-4s1-2 2-3z" class="i"></path><path d="M341 491c0 2 0 3 1 5 2 2 2 5 3 7l-1 1-5-7-1-1h0l-3 3h0v-1l1-6h1c1 1 1 0 2 1l2-2zm10 23l1-1c2 2 3 4 5 5 1 3 3 5 5 7 3 3 5 7 7 10 1 2 1 3 1 4l-3-3c-1-2-2-3-3-4-1-2-2-4-4-5l-3-4c-1 0-2 0-3 1h0-1c1-1 1-2 2-3-1-3-2-4-3-6l-1-1z" class="p"></path><path d="M366 553c2 0 5-2 6-3 1 1 1 2 3 2 0 2 0 2 1 4 0-2 0-3 1-4l3 3c1 4 2 7 2 11l-3 2-1 1c-2-2-3-8-4-10v-1c-1-1-1-3-1-4h-1c-3 1-5 3-6 5s-2 4-3 5v-2c0-3 2-5 3-9z" class="E"></path><path d="M376 556c0-2 0-3 1-4l3 3c1 4 2 7 2 11l-3 2v-2-2c0-2-2-7-3-8z" class="I"></path><path d="M391 552h1c-1 1 0 2-1 3l-1 1-1 1c0 1-1 2-1 3-1 1-1 2-2 3h0c-2 2-4 3-5 5-1 1-2 1-3 2-1 2-3 4-5 5-1 1-2 1-2 2-1 2-4 3-6 4-2 0-3 1-5 2 0-1 0-2-1-2 0-1-1-2-1-3-1-1-1-3-1-4h0c1 1 1 2 1 3 1 1 1 2 2 3l3-1v-1-2h2v2 1h2l1-1c1-1 3-2 4-3-1-2 0-3 0-5s0-3-1-4c0-1-1-1-1-2l2-2h0c1-1 1-2 2-3 1 2 2 8 4 10l1-1 3-2c3-3 5-6 6-10 1-1 3-2 3-4z" class="S"></path><path d="M372 575c-1-2 0-3 0-5s0-3-1-4c0-1-1-1-1-2l2-2c1 2 2 2 3 4 0 3 0 5-1 7l-2 2z" class="L"></path><path d="M360 527c2 1 3 3 4 5 1 1 2 2 3 4h0-3c-1 2-3 4-3 6l-4 13c-1 1-1 4-1 5l-1 2c-1 1 0 3-1 5 0 0 0 1-1 1 0-2-1-4-1-6 1-1 1-3 1-4l4-20c1-4 1-7 3-11z" class="J"></path><path d="M360 527c2 1 3 3 4 5h0c-1 1-1 2-1 3-1 0-2 1-2 1 0 1 0 2-1 2 0 1-1 2-2 2h-1v-2c1-4 1-7 3-11z" class="h"></path><path d="M350 480v3c-1 5-1 11-1 16 0 1-1 4 0 4 0 4 1 7 3 10h0l-1 1-1-1-2-3c-1-3-2-4-4-6l1-1c-1-2-1-5-3-7-1-2-1-3-1-5 0-3 1-5 2-7 1-1 1-1 1-2v-1h2v2h2c1-1 1-2 2-3z" class="m"></path><path d="M344 481h2v2c0 6 0 13 1 19 0 1 0 1-1 2l-1-1c-1-2-1-5-3-7-1-2-1-3-1-5 0-3 1-5 2-7 1-1 1-1 1-2v-1z" class="K"></path><path d="M364 536h3 0l3 3v1c1 2 1 4 2 6v4c-1 1-4 3-6 3-1 4-3 6-3 9h-1c0 2 0 3-1 4v3h-1c-1 0-3-1-5-2l1-7c0-1 0-4 1-5l4-13c0-2 2-4 3-6z" class="o"></path><path d="M363 548h1v1c-1 1-2 3-2 4v1l2 1c1-1 1-2 2-2-1 4-3 6-3 9h-1l-1-1c-1-2-1-4 0-7 0-1 1-1 0-2 0-1 2-2 2-3v-1z" class="W"></path><path d="M364 536h3 0l3 3v1c1 2 1 4 2 6v4c-1 1-4 3-6 3-1 0-1 1-2 2l-2-1v-1c0-1 1-3 2-4v-1h-1l-1-2c0-2 1-3 0-4h-1c0-2 2-4 3-6z" class="H"></path><path d="M366 542c0 2-1 4-2 6h-1l-1-2c1 0 2-3 4-4z" class="I"></path><path d="M364 536h3 0l3 3v1l-1-1-1 3c-1 0-1 0-2-1v1c-2 1-3 4-4 4 0-2 1-3 0-4h-1c0-2 2-4 3-6z" class="K"></path><path d="M364 536c1 2-1 3 0 5h2v1c-2 1-3 4-4 4 0-2 1-3 0-4h-1c0-2 2-4 3-6z" class="O"></path><path d="M364 549h1c0-2 1-2 2-3l1 1s0 1-1 2v1c1 0 2-2 3-3 0-1 1-1 2-1v4c-1 1-4 3-6 3-1 0-1 1-2 2l-2-1v-1c0-1 1-3 2-4z" class="Q"></path><defs><linearGradient id="BA" x1="474.389" y1="674.641" x2="503.554" y2="664.201" xlink:href="#B"><stop offset="0" stop-color="#302e2d"></stop><stop offset="1" stop-color="#706861"></stop></linearGradient></defs><path fill="url(#BA)" d="M455 530l1-2-1-1c-1-2 0-4 0-5-1-1 0-3 0-5v1l1 4v1h0c0 1 0 1 1 2 0 1 0 2 1 4v-1l9 31 44 158c1-1 1-1 2-1l1 5 1 4c0 2 1 3 2 5-1 9-1 20-1 29v8 1c1 2 1 4 1 6l2 1v-1c0-2-1 1 0-1v-2l1 1c-1 2-3 8-2 11v4l1-1h0l-4 17c-1-3-2-7-3-11l-2-11-44-164 1-2c-2-4-2-9-3-13h1 0v-3c0-1 0-2-1-4l-2-11 1-1v-1h0v-1c-1-2-2-4-1-6 0-3 0-4-1-6l1-1c-1 0-1-1-1-2v-1c0-2-1-5-1-7l-4-19c0-3-1-6-1-9z"></path><path d="M473 613h1l1 1c2 0 2 0 3 2-2 0-3 0-5-1v-2z" class="t"></path><path d="M486 648h1v1l2 2-2 2c-1-3-2-1-4-3h0c2 0 2-1 3-2zm17 61c1 1 2 4 2 5v1l1 1v2l1 1 1 4v1c1 1 1 2 1 4l-1-1h0c-1-2-1-4-2-6s-2-3-2-4v-1c-1-2-1-4-1-6v-1z" class="g"></path><path d="M470 592h1 0c0 2 0 2 1 4l-1 1c0 2 1 3 0 5v1h0l-1 4c0-4-1-7-2-11h1v-2s1-1 1-2z" class="q"></path><path d="M514 759v4l1 1c0-2 1-3 1-5v8 4l-1 6h0l-1-1c1-5 0-12 0-17z" class="v"></path><path d="M478 644v-5h1v1h0 0c1 1 2 1 4 1 1 0 2 0 3-1 1 1 1 2 2 4 1 1 1 1 1 2s0 2 1 3v2h0c1 1 2 4 1 5v1l1 2h0c-1 1-1 1-1 2l1 1h0 0-1c0-1 0-1-1-1l1-1v-2c-1-2-2-3-3-4h-1 0v-1l2-2-2-2v-1h-1-1c-1-1-1-1-1-3 1 0 1-1 1-2h-1l-3 3c0 1 1 1 1 2-1-1-2-1-3-2l-1-2z" class="w"></path><path d="M490 695h1l6 22c1 4 3 7 4 11l4 18c2 9 6 17 6 27l-1 1-2-7-18-72z" class="P"></path><defs><linearGradient id="BB" x1="506.017" y1="747.476" x2="521.983" y2="739.024" xlink:href="#B"><stop offset="0" stop-color="#c9cbc5"></stop><stop offset="1" stop-color="#f8f3f6"></stop></linearGradient></defs><path fill="url(#BB)" d="M511 717c1-1 1-1 2-1l1 5 1 4c0 2 1 3 2 5-1 9-1 20-1 29 0 2-1 3-1 5l-1-1v-4l-1-23c0-7 0-13-2-19z"></path><path d="M516 767v1c1 2 1 4 1 6l2 1v-1c0-2-1 1 0-1v-2l1 1c-1 2-3 8-2 11v4l1-1h0l-4 17c-1-3-2-7-3-11l-2-11c0-2 0-5-1-7v-2-1c-1-1-1-2-1-4l2 7c0 1 1 2 1 3 1 3 2 7 3 10 0-3-1-7 0-11l1 1h0l1-6v-4z" class="q"></path><path d="M516 767v1c1 2 1 4 1 6l2 1v-1c0-2-1 1 0-1v-2l1 1c-1 2-3 8-2 11l-1 8c-2-7-1-14-1-20v-4z" class="Z"></path><defs><linearGradient id="BC" x1="459.28" y1="583.628" x2="468.645" y2="578.676" xlink:href="#B"><stop offset="0" stop-color="#5e5a55"></stop><stop offset="1" stop-color="#7b726c"></stop></linearGradient></defs><path fill="url(#BC)" d="M460 558h1v3h1v1l2 3v4c3 2 3 10 4 13 0 3 1 5 1 7v1c0 1 0 1 1 2 0 1-1 2-1 2v2h-1l-1 1-2-2c0 2 0 3 1 4h-1c0-1 0-2-1-4l-2-11 1-1v-1h0v-1c-1-2-2-4-1-6 0-3 0-4-1-6l1-1c-1 0-1-1-1-2v-1c0-2-1-5-1-7z"></path><path d="M464 569c3 2 3 10 4 13 0 3 1 5 1 7v1c0 1 0 1 1 2 0 1-1 2-1 2h-2v-1c1-2 1-4 0-6-1-3-2-8-2-11 0-2-1-2-1-3l1-1c0-1 0-2-1-3z" class="w"></path><path d="M455 530l1-2-1-1c-1-2 0-4 0-5-1-1 0-3 0-5v1l1 4v1h0c0 1 0 1 1 2 0 1 0 2 1 4v-1l9 31-2-3c-1 0-1-1-1-2-1-2-2-4-2-6h-1c0 2 1 4 1 5 1 3 1 5 1 8l1 1-1 1h0l1 2h0l-2-3v-1h-1v-3h-1l-4-19c0-3-1-6-1-9z" class="AM"></path><path d="M465 599h1c-1-1-1-2-1-4l2 2 1-1c1 4 2 7 2 11 2 12 4 25 8 37l1 2c0 5 2 10 4 14 1 3 2 5 2 7v2l1 1c0 1 0 2 1 2v1 3c1 2 1 6 2 9 0 1 1 2 1 4v2 1l1 3h-1l-19-73c-2-2-2-5-4-7-2-4-2-9-3-13h1 0v-3z" class="Y"></path><path d="M465 599h1c-1-1-1-2-1-4l2 2c1 3 1 7 1 10 1 5 3 10 3 15-2-2-2-5-4-7-2-4-2-9-3-13h1 0v-3z" class="w"></path><path d="M467 615c2 2 2 5 4 7l19 73 18 72c0 2 0 3 1 4v1 2c1 2 1 5 1 7l-44-164 1-2z" class="t"></path><path d="M253 223v-5c1 4 3 6 6 9l8 5c1 0 1 0 2-1 0-2 1-3 2-5h0c0 1 1 2 2 3 2 1 5 3 7 5h0c1 1 2 3 3 4 1 2 2 4 3 7l1 2c0 1-1 3-2 4 1 1 1 2 2 3l-2 3-1 3 1 1c1 2 1 3 0 5l1 2c-1 3-2 7-4 11 0 0 0 2-1 3-8 29-14 59-16 89v45c0 2 0 5-1 7-1 1 0 4 0 6v15 6c-1-1-1-1-1-3 0-1-1-2-1-3 0-2 0-1-1-2v-6c1-2 1-2 1-3-1-1-1-3-1-4l-1-1c-1-2-1-5-1-7l-2-8c-1-4-4-11-7-12v-5c-1-6 0-13 0-19 1-25 2-50 7-75-1 1-1 2-2 2-1 1-1 2-1 3h-1c-1-1-1-2-1-4 1-1 1-3 2-5v-2l2-9c1-4 2-7 2-11v-6l1-4-2-1c0-1-1-2 0-3v-5-1-2-3c1-1 1-3 1-4l-1-1v-3c1-1 2-2 2-3 0-2-2-4-3-6l-1-3-7-9v-1l5 3v-1z" class="B"></path><path d="M258 406v1h2v5h0c-2-2-3-3-3-5h0l1-1z" class="E"></path><path d="M262 334c3 4 1 9 1 13-1 5 0 10-1 15l-1 34 1 18c0 3 1 7 0 10-2-3-2-9-2-12v-5-23l1-28c0-6-1-14 0-20 0-1 1-1 1-2z" class="I"></path><path d="M281 255l2-2v3h0c1 1 1 1 2 1l-1 3 1 1c1 2 1 3 0 5l1 2c-1 3-2 7-4 11 0 0 0 2-1 3v-1c0-1 0-2 1-3v-1c-1 0-1 0-2-1l-1 2h0c-2 5-3 10-4 15-1 2-2 4-2 6-1 2 0 4-1 6 0 2 0 3-1 5 1 1 1 2 0 3l-1 1v1c1 1 1 4 1 5l-1 2v3c-1 1-1 3-2 3h-1l-1-2c0 3-1 8 0 11l-1 1c0 3 0 6-1 9h-1c0-4 2-9-1-13-1-1-1-2-2-2-1-1-2-3-2-4s1-2 1-2v-4c0-2 1-5 1-7v-1c1-2 1-6 2-9 0-3 0-7 2-10 0-2 0-4 1-6 1-3 1-4 3-6 0 2 1 3 1 5h1c1-2 1-3 2-4h2c1-1 7-25 7-29z" class="G"></path><path d="M272 284h2c0 2-1 4-1 7-1 1-2 1-3 1l-1-2h1v-2c1-2 1-3 2-4z" class="K"></path><path d="M269 290l1 2c1 0 2 0 3-1 0 1-1 2-1 3v2l-1 1-2-1c-1 4 0 6-2 9v-12c0-1 1-1 1-2s0-1 1-1z" class="k"></path><path d="M266 326v-10-2c0-2 0-5 1-7 1 0 1 0 2 1h0c-1 2-2 5-2 6 0 2 1 3 1 4 0 3-1 4-1 6 1 1 0 2 0 3v1c1-1 2-2 2-3v-3l1-2h0v-5c1 1 1 4 1 5l-1 2v3c-1 1-1 3-2 3h-1l-1-2z" class="m"></path><path d="M284 260l1 1c1 2 1 3 0 5l1 2c-1 3-2 7-4 11 0 0 0 2-1 3v-1c0-1 0-2 1-3v-1c-1 0-1 0-2-1l-1 2h0c1-6 3-12 5-18z" class="E"></path><path d="M268 283c0 2 1 3 1 5h1v2h-1c-1 0-1 0-1 1s-1 1-1 2v12 2c-1 2-1 5-1 7v2 10c0 3-1 8 0 11l-1 1c0 3 0 6-1 9h-1c0-4 2-9-1-13-1-1-1-2-2-2-1-1-2-3-2-4s1-2 1-2v-4c0-2 1-5 1-7v-1c1-2 1-6 2-9 0-3 0-7 2-10 0-2 0-4 1-6 1-3 1-4 3-6z" class="h"></path><path d="M268 283c0 2 1 3 1 5h1v2l-2-1c-1 1-1 0 0 2-2 1-3 2-3 4h-1c0-2 0-4 1-6 1-3 1-4 3-6z" class="J"></path><path d="M262 305c0-3 0-7 2-10h1v15 17c0 2 0 5-1 6v1l-1-1c-3-4-2-9-2-14v-2-1l-1-1v-1c1-2 1-6 2-9z" class="E"></path><path d="M261 319l1-5h1c0 4-1 8 1 11v8 1l-1-1c-3-4-2-9-2-14zm-3-43c1-2 2-5 3-7l1 1c1 0 1-1 2-1v1 11l1 1c0 2-1 5 0 7-1 2-1 4-1 6-2 3-2 7-2 10-1 3-1 7-2 9v1c0 2-1 5-1 7v4s-1 1-1 2 1 3 2 4c1 0 1 1 2 2 0 1-1 1-1 2-1 6 0 14 0 20l-1 28v23h-2v-1l-1 1h0c-3-4-4-8-7-11-1-6 0-13 0-19 1-25 2-50 7-75-1 1-1 2-2 2-1 1-1 2-1 3h-1c-1-1-1-2-1-4 1-1 1-3 2-5v-2l2-9c1-4 2-7 2-11z" class="O"></path><path d="M261 281c1-4 2-8 3-11v11l1 1c0 2-1 5 0 7-1 2-1 4-1 6-2 3-2 7-2 10-1 1-1 2-1 3v1c-1 0-1 1-1 2v1l-1-3c-1-3-1-7 0-10v-1-1c1-1 1-2 1-4 1-1 0-2 0-3 1-3 1-6 1-9z" class="L"></path><path d="M258 276c1-2 2-5 3-7l1 1c1 0 1-1 2-1v1c-1 3-2 7-3 11 0 3 0 6-1 9-2 3-3 8-3 12-1 1-1 2-2 2-1 1-1 2-1 3h-1c-1-1-1-2-1-4 1-1 1-3 2-5v-2l2-9c1-4 2-7 2-11z" class="AI"></path><path d="M254 298h1l4-13 2-4c0 3 0 6-1 9-2 3-3 8-3 12-1 1-1 2-2 2-1 1-1 2-1 3h-1c-1-1-1-2-1-4 1-1 1-3 2-5z" class="f"></path><path d="M259 322v4s-1 1-1 2 1 3 2 4c1 0 1 1 2 2 0 1-1 1-1 2-1 6 0 14 0 20l-1 28v23h-2v-1c-1-4-2-7-2-11 0-1 1-1 0-2-1-6-1-11-1-17-1-3-1-7-1-10l1-5c1-3 0-7 1-10 0-2 1-3 0-5-1-4 0-6 0-10 1-2 0-4 1-6v-2l-1-2c0-1 2-3 3-4z" class="i"></path><defs><linearGradient id="BD" x1="285.773" y1="244.955" x2="254.425" y2="262.925" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#330505"></stop></linearGradient></defs><path fill="url(#BD)" d="M253 223v-5c1 4 3 6 6 9l8 5c1 0 1 0 2-1 0-2 1-3 2-5h0c0 1 1 2 2 3 2 1 5 3 7 5h0c1 1 2 3 3 4 1 2 2 4 3 7l1 2c0 1-1 3-2 4 1 1 1 2 2 3l-2 3c-1 0-1 0-2-1h0v-3l-2 2c0 4-6 28-7 29h-2c-1 1-1 2-2 4h-1c0-2-1-3-1-5-2 2-2 3-3 6-1-2 0-5 0-7l-1-1v-11-1c-1 0-1 1-2 1l-1-1c-1 2-2 5-3 7v-6l1-4-2-1c0-1-1-2 0-3v-5-1-2-3c1-1 1-3 1-4l-1-1v-3c1-1 2-2 2-3 0-2-2-4-3-6l-1-3-7-9v-1l5 3v-1z"></path><path d="M269 272c0 3-1 6-1 9v2c-2 2-2 3-3 6-1-2 0-5 0-7v-1c1-1 2-2 3-4 0-2 0-3 1-5z" class="Q"></path><path d="M267 232c1 0 1 0 2-1 0-2 1-3 2-5h0c0 1 1 2 2 3l-2 1c0 3 2 5 1 7-1-2-3-3-5-5z" class="n"></path><path d="M278 250c1 1 1 2 2 3l-1 2c-1 2-1 3-2 5h0-1l-1 1-2 1v1l-2 2c2-5 4-10 7-15z" class="J"></path><path d="M276 260v-3l2-2h1c-1 2-1 3-2 5h0-1z" class="i"></path><path d="M276 260h1c0 3 0 6-2 8v-3h-1l-3 9c0 2-2 4-3 7 0-3 1-6 1-9l2-7 2-2v-1l2-1 1-1z" class="F"></path><path d="M268 281c1-3 3-5 3-7l3-9h1v3c-1 3-2 8-3 12v2 2c-1 1-1 2-2 4h-1c0-2-1-3-1-5v-2z" class="h"></path><path d="M283 238c1 2 2 4 3 7l1 2c0 1-1 3-2 4 1 1 1 2 2 3l-2 3c-1 0-1 0-2-1h0v-3l-2 2h0c-1-1-1-1-1-2-1-1-1-2-2-3h0c0-1 1-2 2-3h1l1-1c2-2 1-5 1-8z" class="C"></path><path d="M281 255c0-3 1-6 2-8 1 1 2 3 2 4 1 1 1 2 2 3l-2 3c-1 0-1 0-2-1h0v-3l-2 2h0z" class="D"></path><path d="M280 253c0 1 0 1 1 2h0c0 4-6 28-7 29h-2v-2-2c1-4 2-9 3-12 2-2 2-5 2-8h0c1-2 1-3 2-5l1-2z" class="E"></path><path d="M253 223v-5c1 4 3 6 6 9l8 5c2 2 4 3 5 5 0 7-2 13-4 19-1 2-1 4-2 6s-2 4-2 7c-1 0-1 1-2 1l-1-1c-1 2-2 5-3 7v-6l1-4-2-1c0-1-1-2 0-3v-5-1-2-3c1-1 1-3 1-4l-1-1v-3c1-1 2-2 2-3 0-2-2-4-3-6l-1-3-7-9v-1l5 3v-1z" class="AQ"></path><path d="M255 231c3 3 6 7 8 12 0 1 0 2-1 3v1c1 3 0 5 0 8l-1-1c0-1 0-2 1-3 0-3 0-5-1-8l-2-3c0-2-2-4-3-6l-1-3z" class="p"></path><path d="M259 240l2 3c1 3 1 5 1 8-1 1-1 2-1 3h0c-1-1-1-1-1-2-1 0-2 3-3 4v-2-3c1-1 1-3 1-4l-1-1v-3c1-1 2-2 2-3z" class="e"></path><path d="M259 240l2 3c0 1-1 3-1 4h-1-1l-1-1v-3c1-1 2-2 2-3z" class="k"></path><path d="M253 223v-5c1 4 3 6 6 9l8 5c2 2 4 3 5 5 0 7-2 13-4 19-1 2-1 4-2 6 0-3 0-4 1-6s1-4 1-6l1-1c0-3 2-6 2-8 0-1 0-2-1-2-2-1-2 0-3 1-1 0-2-1-3-1 0-1 0-1-1-2-4-3-7-9-10-13v-1z" class="AA"></path><path d="M263 243c2 4 2 6 2 11l1 1c0-1 1-1 1-2s1-2 1-3c0 2 0 4-1 6s-1 3-1 6c-1 2-2 4-2 7-1 0-1 1-2 1l-1-1c-1 2-2 5-3 7v-6l1-4-2-1c0-1-1-2 0-3v-5-1c1-1 2-4 3-4 0 1 0 1 1 2h0l1 1c0-3 1-5 0-8v-1c1-1 1-2 1-3z" class="M"></path><path d="M257 256c1-1 2-4 3-4 0 1 0 1 1 2h0l1 1-1 1-1 2c-1 2-1 5-1 8l-2-1c0-1-1-2 0-3v-5-1z" class="m"></path><path d="M268 250c0 2 0 4-1 6s-1 3-1 6c-1 2-2 4-2 7-1 0-1 1-2 1l-1-1 4-15 1 1c0-1 1-1 1-2s1-2 1-3z" class="AR"></path><path d="M314 641c2 1 4 1 5 2 3 3 6 7 9 10l16 19h0c1 3 2 5 4 7 3 3 7 6 10 9l1 1c1 3 3 5 6 7l1-1c3 4 9 6 13 9h0c1 0 2 2 3 3h-1l-2 1h-2c-1 0-2-1-3-1-2 0-3-1-5 0-2-1-4-1-6-2-1 1-2 2-3 2l-1-1c-1 0-1 1-2 1-1-1-2-1-3-1h-1 0v1h3c-1 1-1 0-2 0s-2 1-3 1c5 0 10 0 14 1h0c6 1 11 2 17 4 5 1 9 4 13 6 1 2 2 3 4 4 1 0 1 1 2 1 1 2 5 4 8 5h-2-1c-9-2-16-8-24-9l-7-2h-2l-1-1v1l-1 1v1c-8-1-15-1-22 0l-1 1c-5 0-9 2-14 3-13 4-24 12-36 20l-2-2v1c-6 3-12 7-20 9-2 1-5 1-7 2-2-1-3 0-4-2h-2-3c3-2 6-3 10-5 7-5 12-12 17-20 1 0 2-1 3-2l1 1v1l8-11c1 0 1 1 2 1v-1 1c2-3 3-6 4-9h2c1-2 1-6 2-9 1-8 3-16 4-24 0-9-1-17 0-25 0-3 0-6 1-9z" class="C"></path><path d="M349 684c2 2 7 4 8 7h-5 0-3c-1 0-2 1-3 1v-1l-1-2v-1l9 1c-2-1-4-2-5-4v-1z" class="S"></path><path d="M348 719c7-2 15-2 23 0v1c-8-1-15-1-22 0l-1-1z" class="W"></path><path d="M319 669l-1-6c-1-2 0-3 0-5l2-1v1c1-1 0-1 0-2l2-1c1 1 1 2 2 3-2 1-2 1-3 3s0 3 0 4l-1 1h-1v3z" class="c"></path><path d="M305 708h2c-1 6-4 14-8 19-1-1-1-3-1-4l3-6c2-3 3-6 4-9z" class="f"></path><path d="M281 745l-1-1c4-4 10-8 13-13 1-1 1-3 2-5l3-3c0 1 0 3 1 4-5 7-12 14-18 18z" class="D"></path><path d="M321 665c0-1-1-2 0-4s1-2 3-3l3 5 1 1 1 1c-1 2-1 3 0 5-1 1-1 2-2 3 0-1-1-2-1-3l-1-1-2-1-2-3z" class="I"></path><path d="M325 669l1-2c-1 0-1-1-1-2s-1-1-1-2c1-1 2 0 3 0l1 1 1 1c-1 2-1 3 0 5-1 1-1 2-2 3 0-1-1-2-1-3l-1-1z" class="V"></path><path d="M328 664l1 1c-1 2-1 3 0 5-1 1-1 2-2 3 0-1-1-2-1-3 0 0 1-1 2-1 0-2-1-3-1-4l1-1z" class="O"></path><path d="M329 665c1 2 3 5 5 6 2 2 4 2 5 4h0c-1 1-1 2-2 2v1c-1 0-1 1-2 2s-1 1-2 1-2 1-3 2v-4c1 0 1-1 2-2-1-1-3-3-5-4 1-1 1-2 2-3-1-2-1-3 0-5z" class="L"></path><path d="M327 673c1-1 1-2 2-3v1h1l2 2-1 2 2 2v1l-1-1c-1-1-3-3-5-4z" class="I"></path><path d="M348 719l1 1-1 1c-5 0-9 2-14 3-13 4-24 12-36 20l-2-2c13-7 24-15 38-20l14-3z" class="a"></path><path d="M319 674c1 1 1 1 1 2 0 2 0 5 2 6 1 2 1 2 2 2l-1 2c-1 2-2 2-2 5l1 1-2 2v2c-1 1-1 1-1 2v1c-2 1-3 2-3 4v1c-1 1-1 1-1 2h-1c0-3 2-8 2-10h-1c0 1-1 2-1 2h-1v-1l2-1v-3l-1 1h-1c0-2 0-3 1-4h2c1-1 2-1 2-2v-6l-1-1 1-1c0 1 0 1 2 1v-1c0-1-1-3-1-5v-1z" class="c"></path><path d="M313 698h1s1-1 1-2h1c0 2-2 7-2 10h1l-1 4c-1 2-2 4-3 7l-1 2c-5 6-9 12-14 17v1h0c-2 3-5 5-7 8h0-1v-2c1-1 3-3 4-5 2-1 3-3 4-4s2-2 3-2v-1c1-1 1-2 2-2l1-2c1-1 1-2 2-2l1-1v-1s1-1 2-1v-1c1-1 1-1 1-2 1-1 1-2 2-3h0-1l-2-2 1-1c3-4 4-9 5-15z" class="S"></path><path d="M346 692c1 1 2 1 3 1h1v-1h1 0c2 2 6 3 9 4 1 1 2 1 3 1 2-1 5 3 7 3-1-1-4-3-5-4l1-1c3 4 9 6 13 9h0c1 0 2 2 3 3h-1l-2 1-3-2c-1-1-2-1-3-1l-4-2h-1-3-1v-1c-3-1-5-2-7-4h-1-1c-1-1-2-2-4-2h0c-1-1-1-1-2-1-1-1-2-1-3-2v-1z" class="R"></path><path d="M362 698c2 1 3 1 5 2h1s1 1 2 1c2 2 4 3 6 5-1-1-2-1-3-1l-4-2h0l-7-5z" class="W"></path><path d="M357 698h3 2l7 5h0-1-3-1v-1c-3-1-5-2-7-4z" class="Q"></path><path d="M320 666l1-1 2 3 2 1 1 1c0 1 1 2 1 3 2 1 4 3 5 4-1 1-1 2-2 2-2 2-4 5-6 5-1 0-1 0-2-2-2-1-2-4-2-6 0-1 0-1-1-2v-5-3h1z" class="L"></path><path d="M320 666l1-1 2 3c0 4 3 7 2 11h-1c0-4-2-9-4-13z" class="n"></path><path d="M323 668l2 1 1 1c0 1 1 2 1 3 2 1 4 3 5 4-1 1-1 2-2 2-2 2-4 5-6 5-1 0-1 0-2-2 1-1 2-2 2-3h1c1-4-2-7-2-11z" class="R"></path><defs><linearGradient id="BE" x1="334.444" y1="716.783" x2="333.296" y2="712.168" xlink:href="#B"><stop offset="0" stop-color="#200b0a"></stop><stop offset="1" stop-color="#340d0b"></stop></linearGradient></defs><path fill="url(#BE)" d="M351 708c5 0 10 0 14 1h0c-1 1-1 1-2 1-3-1-6 0-9 1h-5c-6 1-13 3-18 6-3 1-8 4-11 5-2 0-3-1-5-2 11-7 23-10 36-12z"></path><path d="M351 705l2 1v1h3c-1 1-1 0-2 0s-2 1-3 1c-13 2-25 5-36 12-7 5-13 11-19 17v-1c5-5 9-11 14-17l11-6c3-1 5-3 8-4 1 0 2-1 4-1 2-1 4-1 6-2 4 0 8 0 12-1z" class="M"></path><path d="M299 716c1 0 1 1 2 1v-1 1l-3 6-3 3c-1 2-1 4-2 5-3 5-9 9-13 13l1 1c-5 4-10 7-16 7h-2-3c3-2 6-3 10-5 7-5 12-12 17-20 1 0 2-1 3-2l1 1v1l8-11z" class="h"></path><path d="M345 699c1-1 2-1 3-2h-1v-1l1 1c1 0 2 0 3-1 2 0 3 1 4 2h1 1c2 2 4 3 7 4v1h1 3 1l4 2c1 0 2 0 3 1l3 2h-2c-1 0-2-1-3-1-2 0-3-1-5 0-2-1-4-1-6-2-1 1-2 2-3 2l-1-1c-1 0-1 1-2 1-1-1-2-1-3-1h-1 0l-2-1c-4 1-8 1-12 1-2 1-4 1-6 2v-2l-3-1 4-2c1-1 2-2 4-2h0c1-1 1-1 2-1s0 0 1-1h2 2z" class="F"></path><path d="M343 699h2l4 2h1l1 2c-2 0-2-1-4 0v-1h0l-1-1c-1 0-2-1-3-2z" class="E"></path><path d="M353 706h3v-1l-3-2h-1v-1c2 0 3 1 5 1h0c2 1 3 1 4 1s2 0 2 1c-1 1-2 2-3 2l-1-1c-1 0-1 1-2 1-1-1-2-1-3-1h-1z" class="m"></path><path d="M334 703c3 0 3-1 6-1l1 1v1c1 0 1 1 2 1h2c1-1 1-1 2-1h0c1 1 2 1 3 1h1c-4 1-8 1-12 1-2 1-4 1-6 2v-2l-3-1 4-2z" class="B"></path><path d="M339 675l4 4c2 1 4 3 6 5v1c1 2 3 3 5 4l-9-1h-3 0c-4 1-8 1-11 3-2 0-3 1-4 1l-4 2-1-1 1-1h-1l-1-1c0-3 1-3 2-5l1-2c2 0 4-3 6-5v4c1-1 2-2 3-2s1 0 2-1 1-2 2-2v-1c1 0 1-1 2-2h0z" class="O"></path><path d="M323 686c2 2 3 1 6 1h0 2v1c-3 1-6 2-8 4h-1l-1-1c0-3 1-3 2-5z" class="W"></path><path d="M339 675l4 4c-2-1-3-1-5-1v3l-1 1c1 1 1 3 2 4-1 1-2 1-3 1-2 0-3 1-5 1v-1h-2 0c-3 0-4 1-6-1l1-2c2 0 4-3 6-5v4c1-1 2-2 3-2s1 0 2-1 1-2 2-2v-1c1 0 1-1 2-2h0z" class="I"></path><path d="M324 684c2 0 4-3 6-5v4h0c-1 1-2 1-4 1v2c1 1 0 1 1 1h2 0c-3 0-4 1-6-1l1-2z" class="T"></path><path d="M331 691c3-2 7-2 11-3h0 3v1l1 2v1 1c1 1 2 1 3 2 1 0 1 0 2 1h0c-1 1-2 1-3 1l-1-1v1h1c-1 1-2 1-3 2h-2-2c-1 1 0 1-1 1s-1 0-2 1h0c-2 0-3 1-4 2l-4 2 3 1v2c-2 0-3 1-4 1-3 1-5 3-8 4l-11 6 1-2c1-3 2-5 3-7l1-4c0-1 0-1 1-2v-1c0-2 1-3 3-4v-1c0-1 0-1 1-2v-2l2-2h1l-1 1 1 1 4-2c1 0 2-1 4-1z" class="H"></path><path d="M331 691c3-2 7-2 11-3h0 3v1l1 2v1 1c1 1 2 1 3 2 1 0 1 0 2 1-2 0-2 0-4-1l-1 1c-1 0-2 0-2 1-2 1-1 0-2 0s-2 1-3 1c1-1 3-2 4-3v-1l1-1v-1c-2 0-3 0-5 1h-3c0-1 0-1-1-2l-3 1-1-1z" class="L"></path><path d="M331 691l1 1 3-1c1 1 1 1 1 2h3c-5 1-9 6-14 6v1c-1 0-2 1-2 1-2 1-2 1-3 2v1l-6 6 1-4c0-1 0-1 1-2v-1c0-2 1-3 3-4v-1c0-1 0-1 1-2v-2l2-2h1l-1 1 1 1 4-2c1 0 2-1 4-1z" class="T"></path><path d="M331 691l1 1-2 1c-1 1-2 1-3 1v-2c1 0 2-1 4-1z" class="W"></path><path d="M323 704c1-1 1-1 3-2l1-1c3-1 7-3 10-3v1h-1c-3 1-10 4-11 7 1 0 1 0 2-1h3l3 1v2c-2 0-3 1-4 1-3 1-5 3-8 4l-11 6 1-2c1-3 2-5 3-7l6-6h3z" class="F"></path><path d="M320 704h3c-1 1-2 1-3 3h0 1 1c1-1 1-1 2-1-1 1-1 1-2 1l-1 1c-1 1-3 2-3 4h0c2 0 3-1 4-1l-1 2-11 6 1-2c1-3 2-5 3-7l6-6z" class="E"></path><defs><linearGradient id="BF" x1="560.69" y1="710.077" x2="511.895" y2="677.993" xlink:href="#B"><stop offset="0" stop-color="#241f1f"></stop><stop offset="1" stop-color="#413f3b"></stop></linearGradient></defs><path fill="url(#BF)" d="M556 588c1-1 1-2 2-2l1 1-2 9v1l1 3c2 3 3 7 6 9l2 1-16 55-8 27-11 42-8 26-3 12-1-1v2c-1 2 0-1 0 1v1l-2-1c0-2 0-4-1-6v-1-8c0-9 0-20 1-29-1-2-2-3-2-5l-1-4-1-5h0 0c1-2 0-4 0-6h1c1-5 0-9-1-14l1-1c-1-1-1-1-1-2s0-2-1-2v-1c1-1 1-2 1-4h0c1-2 1-3 1-5l-1-1c2-3 1-9 1-13 0-2-1-5 0-7v-1-2l1-6v-7-2-1-5-1c0-2 1-3 1-5h0l1-3h3c-1 3-1 4 0 7h1c1-1 1-2 1-3l1-1v-3c0-2 1-3 2-5l2-1v-1l4-2 2-2 3-1 1-2c1 2 1 3 1 4h1l1 1v3h0 1 1l2-2v-2h1c0-3 2-5 4-6v-1h2c0-2 1-4 2-5v-1c0-1 0-2 1-3 0-1 0-3 1-4 0-3 0-5 1-8v-1z"></path><path d="M554 619l2-11c1-2 1-3 2-5v2 4 1c0 2 0 3-1 4v3l-1-3c0 2 0 4-2 5z" class="Z"></path><path d="M547 642l-1 1c2-6 4-11 5-17 1-2 2-5 3-7v7 1h-1c0 2-1 4-1 6 0 0-1 0-1-1-2 2-3 7-4 10z" class="l"></path><path d="M556 600l1-3 1 3c2 3 3 7 6 9l2 1-16 55v-3c1-1 0-2 0-4l1-2v-1-2l1-2-1-1c0-1 0-1 1-2 0-1 0-2 1-3l-1-1v-1c0-1 0-2 1-3v-2c-1-1-1-2-1-3v-1l1 1c0-2 1-3 1-5v-1l1-2c0-2 1-3 1-4 0-2 1-3 0-5l1-1v-3c1-1 1-2 1-4v-1-4-2c0-1-1-2-2-3z" class="b"></path><path d="M556 600l1-3 1 3c2 3 3 7 6 9v3l-1 1c0-1-1-2-1-3 0 0 0-1-1-2l-3-3v-2c0-1-1-2-2-3z" class="g"></path><path d="M554 630h1v-1c0-3 2-6 2-9v-1c1 2 1 2 1 4l-1 1-1 3v2c1-1 2-3 2-5l1-1c0-1 0-2 1-2v2c-2 10-5 19-8 28l-1-1c0-1 0-1 1-2 0-1 0-2 1-3l-1-1v-1c0-1 0-2 1-3v-2c-1-1-1-2-1-3v-1l1 1c0-2 1-3 1-5z" class="Y"></path><defs><linearGradient id="BG" x1="532.948" y1="651.552" x2="543.031" y2="653.83" xlink:href="#B"><stop offset="0" stop-color="#b3b0ae"></stop><stop offset="1" stop-color="#dcd9cf"></stop></linearGradient></defs><path fill="url(#BG)" d="M556 588c1-1 1-2 2-2l1 1-2 9v1l-1 3-36 118-3 12c-1-2-2-3-2-5l-1-4c1-1 1-2 1-3 1 0 2-1 2-2 2-3 4-7 5-11 1-3 2-5 3-8 1-4 3-9 4-13 1-3 2-8 3-11l5-19 14-44c0-2 1-4 2-5v-1c0-1 0-2 1-3 0-1 0-3 1-4 0-3 0-5 1-8v-1z"></path><path d="M515 725c2-2 1-6 4-8 0 1 0 1 1 1h0l-3 12c-1-2-2-3-2-5z" class="AN"></path><path d="M517 641c2 3 1 6 2 8h0c0 3 0 4 1 6v1c1 1 0 1 1 1 1-1 3-2 4-3l-1-2 2 1h0c0 1 0 2 1 3h1v1h2l-1 3c-1 1-1 2-1 3h1c0-1 0-1 1-2h0v3c1 1 0 1 1 0 1-3 2-5 2-8v-1c1-1 1-2 3-2l1 1-5 19c-1 3-2 8-3 11-1 4-3 9-4 13-1 3-2 5-3 8-1 4-3 8-5 11 0 1-1 2-2 2 0 1 0 2-1 3l-1-5h0 0c1-2 0-4 0-6h1c1-5 0-9-1-14l1-1c-1-1-1-1-1-2s0-2-1-2v-1c1-1 1-2 1-4h0c1-2 1-3 1-5l-1-1c2-3 1-9 1-13 0-2-1-5 0-7v-1-2l1-6v-7l1 1 1-4z" class="AW"></path><path d="M527 672c-1-3-1-7 0-9v-1-1-2c-2 0-1 0-2-1 1-1 2-1 3-1h2l-1 3c-1 1-1 2-1 3l-1 1h0v8z" class="AD"></path><path d="M528 663h1c0-1 0-1 1-2h0v3l-1 1c0 1 1 1 1 3 0 1-1 5-1 7 0 1 0 0-1 2v3c0 1-1 1-1 1v4 2 1-1l1-3h1c-1 4-3 9-4 13-1-3-1-5 0-7 1-3 2-5 1-8 0-1 0-1 1-2h0c-1-2 0-3 0-4h-1c1-2 1-3 1-4v-8h0l1-1z" class="AS"></path><path d="M530 664c1 1 0 1 1 0 1-3 2-5 2-8v-1c1-1 1-2 3-2l1 1-5 19c-1 3-2 8-3 11h-1l-1 3v1-1-2-4s1 0 1-1v-3c1-2 1-1 1-2 0-2 1-6 1-7 0-2-1-2-1-3l1-1z" class="AM"></path><path d="M517 641c2 3 1 6 2 8h0c0 3 0 4 1 6v1c1 1 0 1 1 1 1 2 0 7-1 9v1h-1c0 3-1 5-1 7 0 1 1 2 0 3v4 2 5 2 2 1 1 1 5l-1 1v2l2 1h-1-1c0 3 0 8-2 10-1-1-1-3-1-4 1-5 0-9-1-14l1-1c-1-1-1-1-1-2s0-2-1-2v-1c1-1 1-2 1-4h0c1-2 1-3 1-5l-1-1c2-3 1-9 1-13 0-2-1-5 0-7v-1-2l1-6v-7l1 1 1-4z" class="AK"></path><path d="M514 695c-1-1-1-1-1-2s0-2-1-2v-1c1-1 1-2 1-4 1 2 1 3 2 5l-1 2v1 1z" class="AM"></path><path d="M517 641c2 3 1 6 2 8h0c0 3 0 4 1 6v1c1 1 0 1 1 1 1 2 0 7-1 9v1h-1c0 3-1 5-1 7h0c-1-1 0-6 0-9v-2c0-2-1-1-1-3h1l1-2h-1v-2l-1-1c0-2 0-2 1-3-1-3-1-5-2-7l1-4z" class="AS"></path><defs><linearGradient id="BH" x1="534.927" y1="663.551" x2="535.44" y2="619.372" xlink:href="#B"><stop offset="0" stop-color="#827e76"></stop><stop offset="1" stop-color="#b0a899"></stop></linearGradient></defs><path fill="url(#BH)" d="M549 610h2l-14 44-1-1c-2 0-2 1-3 2v1c0 3-1 5-2 8-1 1 0 1-1 0v-3h0c-1 1-1 1-1 2h-1c0-1 0-2 1-3l1-3h-2v-1h-1c-1-1-1-2-1-3h0l-2-1 1 2c-1 1-3 2-4 3-1 0 0 0-1-1v-1c-1-2-1-3-1-6h0c-1-2 0-5-2-8l-1 4-1-1v-2-1-5-1c0-2 1-3 1-5h0l1-3h3c-1 3-1 4 0 7h1c1-1 1-2 1-3l1-1v-3c0-2 1-3 2-5l2-1v-1l4-2 2-2 3-1 1-2c1 2 1 3 1 4h1l1 1v3h0 1 1l2-2v-2h1c0-3 2-5 4-6v-1z"></path><path d="M532 645c1 0 1 1 2 2l-2 2-2-1c1-2 1-2 2-3z" class="AM"></path><path d="M538 625v2h0l1-1c1-1 2-3 4-3v1l-4 6h-1c-1-1-2-2-1-3 0-1 0-2 1-2z" class="AT"></path><path d="M535 631h1c1 2 1 2 2 3v2h1v1c-1 0-2-1-3-1l-1 2h0-1c0-1-1-1-1-2l2-2h1c-1-1-1-2-1-3zm-11 14c1-1 1-2 2-3l-1-1 1-2h0l1-1v8s-1 2 0 2v2l-2 2h-1l1 2c-1 1-3 2-4 3-1 0 0 0-1-1v-1c-1-2-1-3-1-6 1 0 3 1 4 1v1l1-1v-2h0l1-1h1v-1h-1l-1-1z" class="AD"></path><path d="M544 619v-2h1c0-3 2-5 4-6 0 1 0 2-1 4-1 3-3 5-4 8h-1c-2 0-3 2-4 3l-1 1h0v-2c0-1 1-2 1-3s0-1-1-2v-1l-2-4 1-2c1 2 1 3 1 4h1l1 1v3h0 1 1l2-2z" class="Ab"></path><path d="M533 616l3-1 2 4v1c-1 1-2 4-3 6v1l-1 1c0-1 0-1-1-1-1-1-1-1-2 0-1 0-2 0-2-1-1 1-1 0-1 1h0l-2 2v2 5l1 1v1l-1 1h0l-1 2 1 1c-1 1-1 2-2 3l1 1h1v1h-1l-1 1h0v2l-1 1v-1c-1 0-3-1-4-1h0c-1-2 0-5-2-8l-1 4-1-1v-2-1-5-1c0-2 1-3 1-5h0l1-3h3c-1 3-1 4 0 7h1c1-1 1-2 1-3l1-1v-3c0-2 1-3 2-5l2-1v-1l4-2 2-2z" class="Aa"></path><path d="M527 620l2 2v2c-1 1-2 1-3 2-1 0-2 0-3 1 0-2 1-3 2-5l2-1v-1zm4 7c-1-2-1-3-1-4 1-2 1-3 2-4 2 0 2 0 4 1 0 1-1 3-2 5v1h1v1l-1 1c0-1 0-1-1-1-1-1-1-1-2 0z" class="AD"></path><path d="M517 627h3c-1 3-1 4 0 7h1c1 0 1 0 2 1v-1h1v5h-1c-1-1-1-2-1-3h-1-1v1 3 1h2 0c0-1 0-1 1-2l1 2c-1 0-1 0-1 2l1 1h0c-1 1-1 1-1 2l1-1 1 1h1v1h-1l-1 1h0v2l-1 1v-1c-1 0-3-1-4-1h0c-1-2 0-5-2-8l-1 4-1-1v-2-1-5-1c0-2 1-3 1-5h0l1-3z" class="AY"></path><path d="M516 630c2 3 2 8 3 11 1 2 0 6 0 8-1-2 0-5-2-8l-1 4-1-1v-2-1-5-1c0-2 1-3 1-5z" class="AW"></path><path d="M515 636c1 1 1 2 1 4h2l-1 1-1 4-1-1v-2-1-5z" class="AM"></path><defs><linearGradient id="BI" x1="377.699" y1="423.534" x2="421.236" y2="409.511" xlink:href="#B"><stop offset="0" stop-color="#56504e"></stop><stop offset="1" stop-color="#9f9c94"></stop></linearGradient></defs><path fill="url(#BI)" d="M317 228l1 1 2 1 9 2c17 4 34 13 44 29 1 0 2 2 2 3l1 2c5 7 9 17 11 25 2 5 4 10 5 15 2 6 4 11 6 17l13 44v1c1 3 0 4 0 7v1c1 1 0 1 0 2v4c1 1 1 1 2 0h0c0-1 0-1 1-3 1 1 1 1 2 3 0 1 0 1-1 2v3c-1 0-1 0 0 1v-1l1 2h0c1 1 2 2 2 4 1 2 2 5 2 8l-1 1v3c0 1-1 1-1 2s0 3-1 4v3 1c0 2 0 3 1 5-1 1-2 3-3 4l3 10v1l13 45c1 2 1 5 2 7 1-2 2-3 4-4 1 0 2 1 2 1v1l1 1h2s-1 1 0 2c0 1 1 1 1 2l-3-1 1 2h2l1 1-1 1v1h0l1 3h1v-1c1 0 2 0 3-1h0c1 4 1 9 3 12 2 2 3 11 3 14s0 6 1 9c0 3 1 6 1 9l4 19c0 2 1 5 1 7v1c0 1 0 2 1 2l-1 1c1 2 1 3 1 6-1 2 0 4 1 6v1h0v1l-1 1 2 11c1 2 1 3 1 4v3h0-1c1 4 1 9 3 13l-1 2-23-82-39-132-26-81c-6-18-11-38-21-53-5-8-13-14-21-16-5-2-10-3-15-4l-4-21z"></path><path d="M318 229l2 1c2 3 5 11 4 15h-1c-1-5-4-11-5-16z" class="Y"></path><path d="M449 546l1-1v2c0-1 0-2 1-2 0 3 1 5 2 7 0 2 1 4 2 5l-1 2h-1c-1-1-3-11-4-13z" class="q"></path><path d="M408 384c1-2 0-3 1-4v-3h0 1v2l-1 1c1 2 1 2 2 2 1 1 1 1 2 0h0v2h-1l-1 1 1 1c1 2 0 5 0 7v1l-1-1v-3l1-1h-1v1l-1-1v-1h1v-2h-1l-1 1-1-1v-2z" class="AL"></path><path d="M405 370v-6h1c-2-2-2 0-3 0s-1-1-2-1l1-1h2l1-1h-2l-1-1c1-1 2-1 2-2h-1c-2-1-2-6-4-8h-1c0-2 0-2 1-4h1c-1 1-1 2-1 3 1 1 2 2 2 3s1 2 1 4l2 2v-1-2l-1-3c-1-2-1-3-1-4 1 3 3 6 3 9 0 1 0 1 1 2 0 1 1 2 0 4h1c1 2 1 3 1 5-1 1-1 1-2 1l-1 1z" class="AT"></path><path d="M398 371l-1 1v-2c-1-1-1-1-1-2l-2-6v-2c0-1-1-1-1-2v-1l-3-8v-2l1 1 1-1v2c1 1 1 2 2 4v1l3 8v2c0 1 0 1 1 2h0v1 2 2z" class="AL"></path><path d="M453 559h1c2 6 5 11 6 17l2 8 2 11c1 2 1 3 1 4v3h0-1c-2-15-7-29-11-43z" class="g"></path><path d="M398 369l4 13c1 2 2 5 2 7 2 1 3 0 4 2 0 1 1 2 1 3 1 1 0 2 1 3 0 0 1 0 1 1v2l1-1-1-1c0-1 1-2 1-3v-1h0v-1c0-2 1-5 0-7l-1-1 1-1h1v-2c0-1 0-1 1-3 1 1 1 1 2 3 0 1 0 1-1 2v3c-1 0-1 0 0 1v-1l1 2h0c1 1 2 2 2 4 1 2 2 5 2 8l-1 1v3c0 1-1 1-1 2s0 3-1 4v3 1c0 2 0 3 1 5-1 1-2 3-3 4v-3c-1-2-1-3-1-6h0v-1c-2-1-2-2-2-4v-1c-1-1-1-2-3-3l-1 1-2-7-8-29v-2z" class="AP"></path><path d="M404 389c2 1 3 0 4 2 0 1 1 2 1 3 1 1 0 2 1 3 0 0 1 0 1 1v2l1-1-1-1c0-1 1-2 1-3v-1 7h-1c-1-2-2-3-4-5-1-2-2-5-3-7z" class="AM"></path><g class="r"><path d="M406 400l1-1c1 1 1 3 2 4s1 2 2 3l2-1 1 10v-1c-2-1-2-2-2-4v-1c-1-1-1-2-3-3l-1 1-2-7z"></path><path d="M414 402l1 3c2 0 2 0 2 1l1 1c0 1 0 3-1 4v3 1c0 2 0 3 1 5-1 1-2 3-3 4v-3c-1-2-1-3-1-6h0l-1-10 1-3z"></path></g><path d="M414 402l1 3c2 0 2 0 2 1-1 3-2 6-2 9h-1 0l-1-10 1-3z" class="AW"></path><path d="M415 388v-1l1 2h0c1 1 2 2 2 4 1 2 2 5 2 8l-1 1v3c0 1-1 1-1 2l-1-1c0-1 0-1-2-1l-1-3c-1-3 1-8 0-10v-1c0-1 0-2 1-3z" class="AS"></path><path d="M419 402c0-1 0 0-1-1h-1v-8h1c1 2 2 5 2 8l-1 1z" class="AM"></path><defs><linearGradient id="BJ" x1="370.39" y1="331.556" x2="411.72" y2="333.449" xlink:href="#B"><stop offset="0" stop-color="#7c7874"></stop><stop offset="1" stop-color="#a19d94"></stop></linearGradient></defs><path fill="url(#BJ)" d="M373 275v-1c1-2 1-2 0-4v-1c1-1 1-3 1-4l1-1 1 2c5 7 9 17 11 25 2 5 4 10 5 15 2 6 4 11 6 17l13 44v1c1 3 0 4 0 7v1c1 1 0 1 0 2v4c-1 0-1 0-2-2l1-1v-2h-1 0v3c-1 1 0 2-1 4-1-1-1-2-2-2v-2c-1-2 1-5-1-7v-3l1-1c1 0 1 0 2-1 0-2 0-3-1-5h-1c1-2 0-3 0-4-1-1-1-1-1-2 0-3-2-6-3-9h0c-1-5-4-11-6-15-2-5-3-10-6-15-2-4-5-8-7-12-1-3-2-6-4-8l-1 1c-1 2-1 4 0 6h0c-1-1-1-2-1-3v-1l-1-1-1-3v-1-1c-1-1-1 0-1-1l-2-4c0-2-2-4-1-6 0-1 0-1 1-1h1 1l-2-2h0v-2c-1-1-1-1-1-2l-1-1h0c1 0 2 2 3 3v2c1 0 1 0 2 1l1-2v-1c-1-1 0 0-1 0l-1 1h0c-1-1-1-2-1-3h2v-1l-2-1z"></path><path d="M375 285l1 1h0c1 2 2 3 2 4l-1 1v-1c-1-1-2-1-3-2l1-3z" class="AP"></path><path d="M388 299c0 2 1 3 1 5 1 1 1 3 1 4 1 2 2 3 2 4h-1c0-1 0-1-1-2 0-2-2-4-2-5-1-1 0-5 0-6z" class="AL"></path><path d="M387 291c2 5 4 10 5 15 2 6 4 11 6 17-1 0-1-1-2-2v-1-1l-1-1v-1-1l-1-1v-1l-1-4v-1l-2-4v-1l-1 4c0-1 0-3-1-4 0-2-1-3-1-5-1 0-1-3-1-3-1-2-1-3 0-5z" class="AW"></path><defs><linearGradient id="BK" x1="382.73" y1="273.169" x2="375.524" y2="288.35" xlink:href="#B"><stop offset="0" stop-color="#584f4b"></stop><stop offset="1" stop-color="#8b867c"></stop></linearGradient></defs><path fill="url(#BK)" d="M373 275v-1c1-2 1-2 0-4v-1c1-1 1-3 1-4l1-1 1 2c0 3 1 4 2 7l6 14c0 1 1 3 1 5h0-1c-1-1-1-1-2-1h1v2h0c-1-1-1-1-3-2v1l-2-2c0-1-1-2-2-4h0l-1-1v-2h-1l-2-2h0v-2c-1-1-1-1-1-2l-1-1h0c1 0 2 2 3 3v2c1 0 1 0 2 1l1-2v-1c-1-1 0 0-1 0l-1 1h0c-1-1-1-2-1-3h2v-1l-2-1z"></path><path d="M375 283h1c1 1 2 1 2 3h-1-1 0l-1-1v-2z" class="AM"></path><path d="M320 230l9 2c17 4 34 13 44 29 1 0 2 2 2 3l-1 1c0 1 0 3-1 4v1c1 2 1 2 0 4v1c-1 0-1-1-2-2v-1 2h-1l-2-2-1 1c0-1 0-1-1-1h-1c-1 3 1 5 2 8h0c-3-5-5-9-9-14-6-10-17-15-28-18l-6-1-1-2h1c1-4-2-12-4-15z" class="w"></path><path d="M320 230l9 2h-1c-2 1-4-1-6 0l1 2h1c1 0 2 0 4 2v1c-1 0-2-1-3-2h-2c1 1 1 1 2 1 1 1 2 2 4 2l-1 1c-2-1-3-1-4-2 0 4 1 6 4 9 1 0 1 0 2 1l-1 1h1 0l-6-1-1-2h1c1-4-2-12-4-15z" class="b"></path><path d="M408 407l1-1c2 1 2 2 3 3v1c0 2 0 3 2 4v1h0c0 3 0 4 1 6v3l3 10v1l13 45c1 2 1 5 2 7 1-2 2-3 4-4 1 0 2 1 2 1v1l1 1h2s-1 1 0 2c0 1 1 1 1 2l-3-1 1 2h2l1 1-1 1v1h0l1 3h1v-1c1 0 2 0 3-1h0c1 4 1 9 3 12 2 2 3 11 3 14s0 6 1 9c0 3 1 6 1 9l4 19c0 2 1 5 1 7v1c0 1 0 2 1 2l-1 1c1 2 1 3 1 6-1 2 0 4 1 6v1h0v1l-1 1-2-8c-1-6-4-11-6-17l1-2c-1-1-2-3-2-5-1-2-2-4-2-7-1 0-1 1-1 2v-2l-1 1c-1-4-2-9-3-13l-7-23-15-52-16-51z" class="t"></path><path d="M462 575c-2-3-2-5-2-7v-4c0 2 0 3 1 5h0c1 2 1 3 1 6zm-9-23c1-1 1-1 0-2v-3h0v-4l2-2c1 1 1 1 1 2 0 2 0 3-1 5v2l2 1-1 1c0 2 0 6-1 7v-2c-1-1-2-3-2-5z" class="r"></path><path d="M433 487c1-2 2-3 4-4 1 0 2 1 2 1v1l1 1h2s-1 1 0 2c0 1 1 1 1 2l-3-1 1 2h2l1 1-1 1v1h0l1 3h1v-1c1 0 2 0 3-1h0c1 4 1 9 3 12 2 2 3 11 3 14h-1 0v6l-1-2v-1 2-5l-1-1v-2h-1v3c0 2-1 4 0 6 0 0 0 1 1 1 0 1-1 3-1 4v3h-1c-5-9-7-20-10-30-1-2-2-5-3-8-1-4-2-7-3-10z" class="AK"></path><path d="M448 513h1v4h-1v-1h-2c1-2 1-2 2-3z" class="AM"></path><path d="M440 486h2s-1 1 0 2c0 1 1 1 1 2l-3-1 1 2v1h-2c-1 0-2 0-2-1l2-3h1l-1-1 1-1z" class="AP"></path><path d="M444 498h1c0 1 0 1 1 2v1c1 1 1 3 1 5 0 1 1 2 1 3-1 0-1 0-2-1v2l-2-2c1-1 1-2 1-3 1-1 1 0 1-1-1-1-2-1-2-1h-1v-1l2-1c0-1-1-1-1-2v-1z" class="AM"></path><defs><linearGradient id="BL" x1="461.078" y1="565.459" x2="514.534" y2="525.338" xlink:href="#B"><stop offset="0" stop-color="#bbb7ac"></stop><stop offset="1" stop-color="#f3f3f0"></stop></linearGradient></defs><path fill="url(#BL)" d="M452 501h0c1 2 1 4 2 5 0-1 1-3 0-3-1-4 0-9-2-12 0-1-1-2-1-4-1-1-3-9-2-11v1c1 1 2 2 2 3v3c1 1 1 2 1 4 0 1 0 0 1 1v-3c-1-1-1-2-1-3-1-2 0-3 0-5l-1-1v-2l-1-4c-1-1-1-3-1-4l-1-1v-2l-1-3h0 1c0 2 2 5 2 8 0 0 0 1 1 2 1 2 1 4 2 6v2c1 3 1 9 0 12v1c1 2 0 4 1 6 0 1 0 3 1 4h0c1-2 1-2 2-3v-1c-1 4-2 7-2 10 2-3 3-7 5-10l4-8 9-21c1 3 2 6 3 10 2 7 4 15 7 23l20 68 5 18 3 12 1 4c1 2 2 4 2 6l1 4h0l1 4c-1 2-1 5-1 7l1 1v5h0c0 2-1 3-1 5v1 5 1 2 7l-1 6v2 1c-1 2 0 5 0 7 0 4 1 10-1 13l1 1c0 2 0 3-1 5h0c0 2 0 3-1 4v1c1 0 1 1 1 2s0 1 1 2l-1 1c1 5 2 9 1 14h-1c0 2 1 4 0 6h0 0c-1 0-1 0-2 1l-44-158-9-31c0-1-1-4-1-6-2-7-4-14-5-21z"></path><path d="M480 567c1-1 2-2 4-2l1 1c-1 1-1 1-2 1h-3z" class="x"></path><path d="M475 532h3v2l-2 1-2-1-1-1 2-1z" class="AV"></path><path d="M479 573c1 0 1 0 2 1 0 0 0 1 1 1h0c1 1 0 1 1 2l1 1-1 1-1-1c-2-1-3-3-3-5z" class="Ab"></path><path d="M486 594l-1-1c0-2-1-2-1-4l1 1v1h3c0 1 0 1 1 2h0v1h-3z" class="AT"></path><path d="M486 594h3v2l-1 1 1 1-2 1v2h-2v-2c1-1 2-1 3-2l-1-1-1 1-1-1c0-1 0-1 1-2z" class="v"></path><path d="M488 564l-2-1-1-1c-1-1-2-2-3-4l1 1 1-1h0 3 0c0 1 0 2 1 3v2 1zm-12-13l2 2 1 2 1-2c0 3-1 3-2 5h-4v-1c1 0 2 0 3-1l-3-3 2-2z" class="AO"></path><path d="M483 547c0 1 1 1 1 2l-1 1 1 1c-1 0-2 1-2 1-1 0 0 1-1 1h-1l-1 2-1-2h0v-1c1-3 3-4 5-5z" class="x"></path><path d="M480 567h3c0 1 0 1-1 2v1 4c1 1 1 1 1 2v1c-1-1 0-1-1-2h0c-1 0-1-1-1-1-1-1-1-1-2-1v-3h-1c1-1 1-2 2-2v-1z" class="Aa"></path><path d="M484 558l3-1c0-2 1-3 1-4l-1-1c0-2 1-3 2-4h-1l1-1c2 2 1 3 2 5 0 0 1 1 2 1-1 2-4 4-5 5h-1 0-3z" class="v"></path><path d="M477 539l2-1v1h4l1-1v1c1 1 1 2 3 3v-3l-1-1h0c2 2 2 3 2 6h1c1 1 1 1 1 2l-1 1v-1h-3c0-2 0-4-2-5h0c0 1-1 2 0 3l1 2h1l-1 1-1-1c-2 0-1-1-2-2h-2v-3c-1-1-1-1-3-2z" class="x"></path><path d="M476 535l1 1-2 1h1 0 1l-1 1h-2l-3 3-1-2c1-1 1-3 1-4 0-3 0-3-2-6 1-1 1 0 2 0v-2c1 0 3-1 4-2v1h0c-1 2-1 4 0 6l-2 1 1 1 2 1z" class="AN"></path><path d="M496 592v-4c1-1 1-1 2-1 1 2 1 1 3 2 1 0 0 2 1 3h1v2c1-1 1-1 2-1 1 1 3 6 4 8-2-2-3-5-4-7 0 2 0 3-1 4v1h-1c0-2-1-3-3-5h-1c0 2 0 2 1 3l-2 1v1h-1v-1c1-3-1-3-1-5l1-1h-1z" class="AO"></path><path d="M477 539c2 1 2 1 3 2v3h2c1 1 0 2 2 2l-1 1c-2 1-4 2-5 5v1h0l-2-2-3-2h0c-1-1-1-1-1-2h2c0-1 0 0 1-1h1l1-1-1-1h-2l-1-1c1-1 2-1 3-2h1v-2zm11 24h1c1 1 2 1 3 1 0 1-2 3 0 4-1 1-3 3-2 3 1 1 1 1 3 1l1 2c0 2 0 2-1 3l-1 1c0-1 0-1-1-2h0c-1 1-1 1-1 2h-1v-4h0-1l1 1-1 1v1c-1-1-1-2-1-3v3h-1v-3l-2-2c0 1 0 2 1 3-1 0-1 1-2 1 0-1 0-1-1-2v-4h1c1-1 2-1 2-2v-1l1-1 1 2h1l1-1c-1 0-1-1-1-1h-1c1-1 1-1 1-2v-1z" class="v"></path><path d="M489 593v-3l1 1h1v-1-1h0 0v-2l-1 1v-2c0-1 0-1-1-2v-1l1-2c1 0 1 0 3 1 0 0 1 1 1 2v4c0 1 1 1 1 1v3h1 1l-1 1c0 2 2 2 1 5v1h1c0 1 1 2 1 2 1 1 0 1 1 2h0c0 1 0 1-1 1v1c1 1 2 1 3 2v4l-1 2c-1 0-1 0-1 1v1h-1c0-1-1-1-1-2-2 0-2 0-3 1-1-1-1-1-2-1l-1-1h0l-1-1h-1l1-1 1 1 1-2c-1 0-2-1-3-2 0-1 0-2-1-3v-1-2c1-1 1-2 1-3h-1l-1-1 1-1v-2-1z" class="x"></path><path d="M499 605c1 1 2 1 3 2v4l-1 2c-1-1-1-2-1-3v-1c-1-1-1-2-1-4z" class="Ab"></path><path d="M490 598h2l2 3h0l2 3-1 1c-1-1-1-1-1-2-1 0-1 2-3 2l-1-1 1-2-2-1c1-1 1-2 1-3z" class="v"></path><path d="M475 576h0l4 9 1 5v1c1 1 3 3 2 5l1 1v1 1l1 1-1-1h0l1-1 1 1v2h2v-2l2-1h1c0 1 0 2-1 3v2 1c1 1 1 2 1 3 1 1 2 2 3 2l-1 2-1-1-1 1h1l1 1h0l1 1c1 0 1 0 2 1 1-1 1-1 3-1 0 1 1 1 1 2h1v-1c0-1 0-1 1-1l1-2v-4c-1-1-2-1-3-2v-1c1 0 1 0 1-1h0c-1-1 0-1-1-2 0 0-1-1-1-2v-1l2-1c-1-1-1-1-1-3h1c2 2 3 3 3 5h1v-1c1-1 1-2 1-4 1 2 2 5 4 7 0 1 0 3 1 4h0c1 2 1 2 1 4s1 3 2 4l1 1v-1h1 0l1 4c-1 2-1 5-1 7l1 1v5h0c0 2-1 3-1 5v1 5 1 2 7l-1 6v2 1c-1 2 0 5 0 7l-2 2-1 1h-1l-1-2 1-2v-1c-2 0-4-1-5-3l2-1c0-3-1-1-3-3 0 0 0-2-1-3l2-1v-1h-2c0-1-1-2 0-2v-5h0v-1l1-1c-1 0-1-1-1-2h1v-1l-1-2h-2 0c-1 0-1-1-2-1h-1-1c0 2-1 2-2 3-2-1-1-2-2-3v-2l-6-21h-1c0-1-1-2-1-3s0-2-1-3l-9-33z" class="AD"></path><path d="M499 629h1c0 1 0 2 1 2l-2 2-3-1 3-3z" class="AW"></path><path d="M491 618v1c1-1 1 0 2-1 0 2 0 3 1 5l-1 1v1h-1c0-1-1-2-1-3h0c-1-1-1-2-1-3l1-1z" class="v"></path><path d="M492 612h0c0 2 0 3 2 4h0c0-1 0-1 1-1v3h0v1c0 1 0 2 1 3-1 1-1 1-2 1-1-2-1-3-1-5-1-2-1-3-3-4v-1l2-1zm21 16c1 3 1 4 1 7h1v1 5h-1c0-1 0-3-1-4-1 0 0 0-1 1v1-7c0-1 0-2-1-3 1 0 1-1 2-1z" class="AT"></path><path d="M515 651l-1 6v2 1c-1 2 0 5 0 7l-2 2v-2l-1-3 1-1c0-1-1-2-1-3h2 0l-1-1v-4l2-2s0-2 1-2z" class="AW"></path><path d="M501 613l1-2v4 1 1 1l-1-1c-1 0-2 1-3 2v3l-3-4h0v-3c-1 0-1 0-1 1h0c-2-1-2-2-2-4l1 1c1 0 1 0 2 1 1-1 1-1 3-1 0 1 1 1 1 2h1v-1c0-1 0-1 1-1z" class="AO"></path><path d="M489 598h1c0 1 0 2-1 3v2 1c1 1 1 2 1 3 1 1 2 2 3 2l-1 2-1-1-1 1h1l1 1-2 1v1c2 1 2 2 3 4-1 1-1 0-2 1v-1h0l-2-1v-1c0-1-1-1-1-2 0-2 0-2-1-4h0v-1-1c0-1 0-2-1-3v-2-1h-2v-2l-1-1h0l1-1 1 1v2h2v-2l2-1z" class="Ab"></path><path d="M505 624h1v-1c0-1 0-2-1-3v-3l1-1c1 1 2 2 2 4h1v-1l1 1c1 2 2 6 3 8-1 0-1 1-2 1 1 1 1 2 1 3-1 2 0 5-1 7h-1c0-2-1-3-2-4l-1-1s0-1-1-2h0v-2c-1-1-1-2-2-3 0-1 0-2 1-3z" class="AW"></path><path d="M498 599v-1l2-1c-1-1-1-1-1-3h1c2 2 3 3 3 5h1v-1c1-1 1-2 1-4 1 2 2 5 4 7 0 1 0 3 1 4h0c1 2 1 2 1 4s1 3 2 4l1 1v-1h1 0l1 4c-1 2-1 5-1 7l1 1v5h0c0 2-1 3-1 5h-1c0-3 0-4-1-7-1-2-2-6-3-8l-1-1v1h-1c0-2-1-3-2-4l-1 1v3c1 1 1 2 1 3v1h-1l-2-2c-1-1-2-2-3-4l2 6c-1 1-1 1-1 2-1-1-1 0-2-1h0v-1c0-1 0-1-1-2v-3c1-1 2-2 3-2l1 1v-1-1-1-4-4c-1-1-2-1-3-2v-1c1 0 1 0 1-1h0c-1-1 0-1-1-2 0 0-1-1-1-2z" class="AT"></path><path d="M506 616l1-1v1l1 1v-1l1 1v-1l1 1c0 1 0 1 1 2l-1 1-1-1v1h-1c0-2-1-3-2-4z" class="AD"></path><path d="M500 603h2l-1 1 1 1h0c1-1 1-1 2-1-1 2-1 2-2 3-1-1-2-1-3-2v-1c1 0 1 0 1-1h0z" class="Aa"></path><path d="M510 605h0c1 2 1 2 1 4s1 3 2 4l1 1v-1h1 0l1 4c-1 2-1 5-1 7l1 1v5h0c0 2-1 3-1 5h-1c0-3 0-4-1-7-1-2-2-6-3-8l1-1v-1 1c1 2 1 6 3 7v-3c-2-6-3-12-4-18z" class="x"></path><defs><linearGradient id="BM" x1="505.446" y1="604.465" x2="458.756" y2="606.116" xlink:href="#B"><stop offset="0" stop-color="#a7a2af"></stop><stop offset="1" stop-color="#e6e7d0"></stop></linearGradient></defs><path fill="url(#BM)" d="M452 501h0c1 2 1 4 2 5 0-1 1-3 0-3-1-4 0-9-2-12 0-1-1-2-1-4-1-1-3-9-2-11v1c1 1 2 2 2 3v3c1 1 1 2 1 4 0 1 0 0 1 1v-3c-1-1-1-2-1-3-1-2 0-3 0-5l-1-1v-2l-1-4c-1-1-1-3-1-4l-1-1v-2l-1-3h0 1c0 2 2 5 2 8 0 0 0 1 1 2 1 2 1 4 2 6v2c1 3 1 9 0 12v1c1 2 0 4 1 6 0 1 0 3 1 4h0c1-2 1-2 2-3v-1c-1 4-2 7-2 10 2-3 3-7 5-10v1c0 2-1 4-2 6h1c0-1 0-2 1-2h1 0c-1 2-1 2 0 3l-1 1c0 1-1 2-1 3v1c-1 2-1 8-1 10 2 5 3 11 5 16l10 34c0 2 1 4 2 6l9 33c1 1 1 2 1 3s1 2 1 3h1l6 21v2c1 1 0 2 2 3 1-1 2-1 2-3h1 1c1 0 1 1 2 1h0 2l1 2v1h-1c0 1 0 2 1 2l-1 1v1h0v5c-1 0 0 1 0 2h2v1l-2 1c1 1 1 3 1 3 2 2 3 0 3 3l-2 1c1 2 3 3 5 3v1l-1 2 1 2h1l1-1 2-2c0 4 1 10-1 13l1 1c0 2 0 3-1 5h0c0 2 0 3-1 4v1c1 0 1 1 1 2s0 1 1 2l-1 1c1 5 2 9 1 14h-1c0 2 1 4 0 6h0 0c-1 0-1 0-2 1l-44-158-9-31c0-1-1-4-1-6-2-7-4-14-5-21z"></path><defs><linearGradient id="BN" x1="492.17" y1="685.588" x2="519.448" y2="661.825" xlink:href="#B"><stop offset="0" stop-color="#797874"></stop><stop offset="1" stop-color="#a19a8b"></stop></linearGradient></defs><path fill="url(#BN)" d="M486 615h1l6 21v2c1 1 0 2 2 3 1-1 2-1 2-3h1 1c1 0 1 1 2 1h0 2l1 2v1h-1c0 1 0 2 1 2l-1 1v1h0v5c-1 0 0 1 0 2h2v1l-2 1c1 1 1 3 1 3 2 2 3 0 3 3l-2 1c1 2 3 3 5 3v1l-1 2 1 2h1l1-1 2-2c0 4 1 10-1 13l1 1c0 2 0 3-1 5h0c0 2 0 3-1 4v1c1 0 1 1 1 2s0 1 1 2l-1 1c1 5 2 9 1 14h-1c0 2 1 4 0 6h0l-26-97v-1c-1-1-1-2-1-3z"></path><path d="M501 639h2l1 2v1h-1c0 1 0 2 1 2l-1 1v1h-1l1-1c0-1-1-2-1-2h-2l2-2c-1 0-1 0-2-1l1-1z" class="AW"></path><defs><linearGradient id="BO" x1="678.4" y1="620.531" x2="557.362" y2="696.034" xlink:href="#B"><stop offset="0" stop-color="#030000"></stop><stop offset="1" stop-color="#1d0706"></stop></linearGradient></defs><path fill="url(#BO)" d="M630 595c1-2 2-3 3-4l2 1h1l1-1c4 4 10 4 15 6-2 0-5-1-5 0 1 0 2 1 3 2s3 2 5 3c1 1 3 4 4 4l4 3h0c3 4 8 7 11 11 1 1 3 2 4 3h0c1 0 2 1 3 1l1 1v2c1 1 2 1 2 3s3 4 2 6v3l-1-1c-1 1-1 1-1 2 0 2 0-1 0 2 1 1 0 2 1 4v2c0 2-1 4-1 5v1c-1 0-1 1-1 2-1 2-2 3-3 5l-3 4c-2 3-3 5-7 6l-1 1v1c-7 11-17 19-27 27-5 3-9 7-14 10l-3 1-10 4v2l1 1c-2 1-3 1-5 2-2-2-6 0-9-1l-12 2c-6 1-11 1-18 0l-2-1 1-1h2 1l5-1c3 0 5 0 8-2v-1c2-2 3-4 5-6l1 1-4 4v1l1-1h0c1 0 1 0 2-1h2s1 1 1 2c-1 1-1 1 0 1s2 0 3-1c0 0 1 0 2-1 2 0 3-1 5-3 4-5 9-9 11-15h0-1l1-1c1-2 2-4 2-7l1-1v-2c-1-2-1-3-1-5l1-1-2-1-1 1h1l-1 1h-1v-1l1-2h1c0-1 0-1-1-2v-2c0 2-1 3-3 5-1 0-2 1-3 2l1 1-1 1h2 1l-1 2c0 1-4 5-5 5-4 0-7-3-10-5-1-1-1-1-1-3h-1c-1-1-1-1-1-2-2-1-2-2-3-3 3-2 5-7 6-11h-1l1-1v-4h0c-1-2-2-3-3-5l-2-4 1-1h1c3-2 3-5 6-7v-1l1-3-1-1c1-2 0-3 0-5 1 0 1-1 1-2h0c0-1-1-1-1-1l2-1c-1-1-2 0-3 0 0-2 0-4 1-6l4-5v-2c5-5 8-11 14-16l1 1c3-3 6-5 10-6h1z"></path><path d="M651 659h1c0-1 1-1 1-2l2 1c-1 1-2 3-3 4 0-1 0-2-1-3z" class="R"></path><path d="M618 614l6-3c1 1 1 3 2 4-3-1-6 0-8-1z" class="i"></path><path d="M647 614c0-1-1-3 0-4v-1-5h2l1 1v2h0c-2 2-3 4-3 7z" class="T"></path><path d="M650 605l3 3c1 1 3 2 4 4h-2l-2-1-1 1c-2-2-2-3-2-5v-2z" class="C"></path><path d="M675 661v1c0 1 0 2-1 4h0c1 0 2 0 3-1-2 3-3 5-7 6l-1 1 6-11z" class="L"></path><path d="M618 617v-1l-1-1h0l1-1c2 1 5 0 8 1v1h2c-1 0-2 1-2 2-2 0-4 1-6 1l1-2v-1l-3 1z" class="C"></path><path d="M602 719l13-4v2l1 1c-2 1-3 1-5 2-2-2-6 0-9-1z" class="i"></path><path d="M628 615h3c3 2 7 6 10 9l-1 1-1-1h-2l-1-1c-3-3-6-5-8-8z" class="J"></path><path d="M618 600l1 1c-3 3-5 7-8 11-2 2-5 4-7 6v-2c5-5 8-11 14-16z" class="K"></path><path d="M630 595c1-2 2-3 3-4l2 1h1l1-1c4 4 10 4 15 6-2 0-5-1-5 0 1 0 2 1 3 2-6-2-11-4-16-4h-4z" class="X"></path><path d="M660 629c0-3 2-8 4-10 1 0 2 2 3 3-2 3-3 7-5 10l-1 3c0-2 1-5 0-7l-1 1z" class="R"></path><path d="M650 607c0 2 0 3 2 5l1-1 2 1h2c2 1 3 2 4 3-1 4-2 7-3 10 0-1 0-2-1-3v-1-1l-1-1c-1-1-2-2-3-2 0-1 0-2-1-2s-1 1-2 2h0v-1-9h0z" class="c"></path><path d="M650 607c0 2 0 3 2 5 1 0 2 0 3 1h1s-1 1 0 1v3 1c0 1 0 1 1 2l-1-1c-1-1-2-2-3-2 0-1 0-2-1-2s-1 1-2 2h0v-1-9h0z" class="R"></path><path d="M675 661l6-17h0c1 0 1 0 2 1 0 2 0 2-2 3 1 4-2 9-4 13 1-1 2-2 3-2 0-1 1-2 2-3 0-1 0-2 1-3v-2c1-1 1 0 1-1v-1-2c0-1 1-1 1-1v2c0 2-1 4-1 5v1c-1 0-1 1-1 2-1 2-2 3-3 5l-3 4c-1 1-2 1-3 1h0c1-2 1-3 1-4v-1z" class="c"></path><path d="M618 617l3-1v1l-1 2c-1 0-1 0-2 2 1 1 1 1 2 1v2c-1 1-1 1-1 2-1 1-1 1-2 3h-1v-1l-1-1-2 1h-1c-1-1-1-1-2-1l-2 2-4 2s-1 0-2 1h0l-1-1h0c0-1-1-1-1-1l2-1c0-1 2-1 3-2s2-4 4-5h0v2h-1c1 1 1 1 2 1l1-1c2-2 5-3 7-7z" class="J"></path><path d="M618 617l3-1v1l-1 2c-1 0-1 0-2 2 1 1 1 1 2 1v2l-2-2c-1 0-1 1-1 2h-6c2-2 5-3 7-7z" class="V"></path><path d="M628 615c2 3 5 5 8 8l1 1 1 1-1 1c0 1 0 1-1 3l-2 1h0c0-1 0-1-1-2-1 0-1-1-2-2h-1c-3 0-6-1-9 0h-2c0-1 0-1 1-2v-2c-1 0-1 0-2-1 1-2 1-2 2-2 2 0 4-1 6-1 0-1 1-2 2-2v-1z" class="O"></path><path d="M633 623h3l1 1 1 1-1 1c-2 0-2 0-3 1h-1c-1-2 0-3 0-4z" class="L"></path><path d="M628 615c2 3 5 5 8 8h-3c-2-1-3-2-5-4 0-1-1-1-2-1 0-1 1-2 2-2v-1z" class="S"></path><path d="M671 645h0v1c-2 4-3 8-6 12-3 5-8 10-11 16-2 2-4 4-6 7-5 6-11 11-17 17-2 2-4 5-6 6h-1l-3 3-1-1c1-1 1-2 2-3 2-1 5-4 7-6 5-5 11-10 15-15 1-2 3-3 5-5l16-24c1-1 1-2 2-3 2-1 3-3 4-5z" class="d"></path><defs><linearGradient id="BP" x1="657.441" y1="638.778" x2="663.503" y2="642.494" xlink:href="#B"><stop offset="0" stop-color="#0c0000"></stop><stop offset="1" stop-color="#2a0604"></stop></linearGradient></defs><path fill="url(#BP)" d="M667 622c2 2 3 4 4 6-3 12-10 21-16 30l-2-1c0 1-1 1-1 2h-1v1h-1 0c4-8 8-17 11-25l1-3c2-3 3-7 5-10z"></path><path d="M626 658h1l1-1v1l1 1c0 2-1 2 0 4v1c1-3 2-5 4-7l1 1c0 2-1 4-2 6v3l-4 9c-1 2-1 3-2 4l-3 6h-1 0v-1l1-2c1-3 3-6 4-10l1-1 1-2v-1l1-1c0-1 0-1 1-2v-2h1v-1-1-1h1v-2c-4 6-6 12-9 18-1 3-3 6-4 10-1 2-1 4-2 7 0 0 0 1-1 2h-1 0-1l1-1c1-2 2-4 2-7l1-1v-2c-1-2-1-3-1-5l1-1-2-1-1 1h1l-1 1h-1v-1l1-2h1c0-1 0-1-1-2l1-2h2c0-2 2-7 3-9 2-1 3-4 4-6z" class="R"></path><path d="M626 658h1l1-1v1l1 1c0 2-1 2 0 4v1l-3 5h0-1c-2 1-2 2-3 4-3 2-3 5-2 8 0 6-2 9-3 15h-1 0-1l1-1c1-2 2-4 2-7l1-1v-2c-1-2-1-3-1-5l1-1-2-1-1 1h1l-1 1h-1v-1l1-2h1c0-1 0-1-1-2l1-2h2c0-2 2-7 3-9 2-1 3-4 4-6z" class="L"></path><path d="M641 624l3 2 1 2-1 2-1 1c0 3-1 6-2 8 0 2-1 4-1 6 0 1 0 1-1 2l-1 2 1 1-1 1v3h0l-1-1-1 1-1 1h0c0 3-2 8-3 11v1-3c1-2 2-4 2-6l-1-1c-2 2-3 4-4 7v-1c-1-2 0-2 0-4l-1-1v-1l-1 1h-1c-1 2-2 5-4 6-1 2-3 7-3 9h-2l-1 2v-2h0l1-3c0-1 0-1 1-1 0-4 4-8 3-12-2 0-3-1-4-3l1-2h2c2 1 2 1 4 1h0 0 1c0-2 2-3 3-5s1-4 1-6c1-5 3-8 5-12h0l2-1c1-2 1-2 1-3l1-1-1-1h2l1 1 1-1z" class="Q"></path><path d="M626 658c1-4 3-7 3-10 1 2 1 6 0 8l-1 1-1 1h-1z" class="S"></path><path d="M637 641h1l1-1 1 1 1-2c0 2-1 4-1 6 0 1 0 1-1 2l-1 2c-1 3-3 5-6 8l5-15v-1z" class="AC"></path><path d="M641 624l3 2 1 2-1 2-1 1c0 3-1 6-2 8l-1 2-1-1-1 1h-1c1-2 1-3 2-4v-1l-2-1c-1-1-1-2-1-3-1-1-1-1-1-2h-1l2-1c1-2 1-2 1-3l1-1-1-1h2l1 1 1-1z" class="n"></path><path d="M641 624l3 2 1 2-1 2-1 1c-1 0-3-1-4-2l-1-1-2 1c1-2 1-2 1-3l1-1-1-1h2l1 1 1-1z" class="S"></path><path d="M641 624l3 2 1 2-1 2-1-1c-2-2-3-3-5-4l-1-1h2l1 1 1-1z" class="Q"></path><path d="M647 614c0-3 1-5 3-7v9 1h0c1-1 1-2 2-2s1 1 1 2c1 0 2 1 3 2l1 1v1 1c1 1 1 2 1 3l1 5c0-1 1-1 1-1l1-1c1 2 0 5 0 7-3 8-7 17-11 25h0 1v-1c1 1 1 2 1 3l-4 5c-1 2-3 4-4 6l-3 4v1l-3 4c0-2 1-3 1-4 0-5 4-11 6-16l6-14c0-1 0-3 1-4v-1-1h1v-1-1-1c1-1 1-1 1-2v-2c-1-1-2-1-3-1-1 2-2 3-4 4h-1c0 3-2 6-3 9h0c0 2-1 2-2 2l-1 1h-1l-1-1 1-2c1-1 1-1 1-2 0-2 1-4 1-6 1-2 2-5 2-8l1-1 1-2-1-2 3-12z" class="W"></path><path d="M650 617h0c0 2 0 3 1 5 0 2-1 5-2 8h-1l-1-1c0-4 1-8 3-12z" class="n"></path><path d="M647 614c0-3 1-5 3-7v9 1c-2 4-3 8-3 12 0 2-1 5-1 7v1c-1 1-1 5-3 6v1h0c-1 1-1 1-1 2l-1 1v1c-1 1-1 0-1 2h-1l-1-1 1-2c1-1 1-1 1-2 0-2 1-4 1-6 1-2 2-5 2-8l1-1 1-2-1-2 3-12z" class="j"></path><path d="M621 626c3-1 6 0 9 0h1c1 1 1 2 2 2 1 1 1 1 1 2-2 4-4 7-5 12 0 2 0 4-1 6s-3 3-3 5h-1 0 0c-2 0-2 0-4-1h-2l-1 2c1 2 2 3 4 3 1 4-3 8-3 12-1 0-1 0-1 1l-1 3h0c0 2-1 3-3 5-1 0-2 1-3 2l1 1-1 1h2 1l-1 2c0 1-4 5-5 5-4 0-7-3-10-5-1-1-1-1-1-3h-1c-1-1-1-1-1-2-2-1-2-2-3-3 3-2 5-7 6-11h-1l1-1v-4h0c-1-2-2-3-3-5l-2-4 1-1h1c3-2 3-5 6-7v-1l1-3-1-1c1-2 0-3 0-5 1 0 1-1 1-2l1 1h0c1-1 2-1 2-1l4-2 2-2c1 0 1 0 2 1h1l2-1 1 1v1h1c1-2 1-2 2-3h2z" class="R"></path><path d="M617 654c-1-1-1-2 0-3-1-1-1-1-1-2h1 1c0 1 0 2 1 3h-1l-1 2z" class="W"></path><path d="M603 674h1c1 0 2 0 3 1v1l-4 1h-1v-2l1-1z" class="j"></path><path d="M618 649v-2c0-1 2-2 2-3l1 1-1 2v5h-2 1c-1-1-1-2-1-3zm-5 17h1c-1 1 0 2 0 4h1v1 2h0c-1 1-1 0-2 0h-2v1c-2 1-2 2-4 2h0v-1c-1-1-2-1-3-1h4l3-3v1-1c2-1 2-3 2-5z" class="T"></path><path d="M612 684l-1-1-2 1h-1l-1-1v-1c-1 0-1 0-2 1l-1 1-1-1c0-1-1-2-1-3l1-1c1 0 1-1 2-1l1-1v1l-1 2v1h3l3-3h2c-1 0-2 1-3 2l1 1-1 1h2 1l-1 2z" class="S"></path><path d="M602 667l1-4c1-1 2-2 3-2 2-1 3 1 4 2l1-1c1 1 2 3 2 4 0 2 0 4-2 5v1-1l-3 3h-4-1c-1-2-1-5-1-7z" class="R"></path><path d="M611 662c1 1 2 3 2 4 0 2 0 4-2 5v1-1c0-2 0-5-1-8l1-1z" class="I"></path><path d="M601 639l1 2c1-1 1-1 1-2h0c2 1 3 0 4 0s1 0 2 1l1-1v1h0c0 1 1 1 1 2s0 2-1 3l-1 1s0 2 1 2c-2 1-3 1-5 3 0 2 0 3 1 5 0 0 1 1 2 1h1c0 2 1 3 2 5l-1 1c-1-1-2-3-4-2-1 0-2 1-3 2l-1 4c0 2 0 5 1 7l-1 1v2c-1 1-1 0-1 1-1 1-1 0-1 1-1 0-2 1-3 2h0v3c-1-1-1-1-1-3h-1c-1-1-1-1-1-2-2-1-2-2-3-3 3-2 5-7 6-11h-1l1-1v-4h0c-1-2-2-3-3-5l-2-4 1-1h1c3-2 3-5 6-7v-1l1-3z" class="E"></path><path d="M609 640l1-1v1h0c0 1 1 1 1 2s0 2-1 3l-1 1c-1 0-2 0-3 1-1 0-1 0-1-1-1-1-1-2-1-4 1-1 3-1 5-2z" class="R"></path><path d="M600 643h2c1 2 2 3 1 5h0c-4 2-6 4-7 7h-2l-2-4 1-1h1c3-2 3-5 6-7z" class="S"></path><path d="M600 655c0-1 1-2 2-3 1 1 1 0 1 2 1 1 1 2 2 3-1 2-3 3-4 4v1 4c0 1 0 0 1 1 0 2 0 5 1 7l-1 1v2c-1 1-1 0-1 1-1 1-1 0-1 1-1 0-2 1-3 2h0v3c-1-1-1-1-1-3h-1c-1-1-1-1-1-2-2-1-2-2-3-3 3-2 5-7 6-11h-1l1-1v-4h0c-1-2-2-3-3-5h2 4z" class="R"></path><path d="M596 655h4c-1 2 0 3-1 5-1 0 0 0-1-1l-1 1h0c-1-2-2-3-3-5h2z" class="I"></path><path d="M596 681c0-2 2-4 2-6 1-4 1-11 3-14v1 4c0 1 0 0 1 1 0 2 0 5 1 7l-1 1v2c-1 1-1 0-1 1-1 1-1 0-1 1-1 0-2 1-3 2h0v3c-1-1-1-1-1-3z" class="O"></path><path d="M621 626c3-1 6 0 9 0h1c1 1 1 2 2 2 1 1 1 1 1 2-2 4-4 7-5 12 0 2 0 4-1 6s-3 3-3 5h-1 0c1-3 2-7 2-10-3 0-8-2-10 0-1 0-3 3-3 4l1 8c0 2 0 2-1 4-1-3-1-6-3-9h0l1-1-1-1h0c-1 0-1-2-1-2l1-1c1-1 1-2 1-3s-1-1-1-2h0v-1l-1 1c-1-1-1-1-2-1s-2 1-4 0h0c0 1 0 1-1 2l-1-2-1-1c1-2 0-3 0-5 1 0 1-1 1-2l1 1h0c1-1 2-1 2-1l4-2 2-2c1 0 1 0 2 1h1l2-1 1 1v1h1c1-2 1-2 2-3h2z" class="V"></path><path d="M627 638h1v1c0 1-1 2-2 3-1-1-2-1-2-3v-1h3z" class="T"></path><path d="M613 628l2-1 1 1v1c-1 1-1 2-1 4-1 2-1 4-3 5l-1-1h-1 0l1-1c-1-2 0-5 0-7l1-1h1z" class="W"></path><path d="M613 628l2-1 1 1v1c-1 1-1 2-1 4l-2-5z" class="I"></path><path d="M608 629c0 1-1 4-1 6s-1 3-2 4h-2 0c0 1 0 1-1 2l-1-2-1-1c1-2 0-3 0-5 1 0 1-1 1-2l1 1h0c1-1 2-1 2-1l4-2z" class="T"></path><path d="M600 633c1 0 1-1 1-2l1 1c0 3 0 5 1 7 0 1 0 1-1 2l-1-2-1-1c1-2 0-3 0-5z" class="i"></path><path d="M621 626c3-1 6 0 9 0h1c1 1 1 2 2 2l-3 3c-2 2 0 4-4 5h-1c-3 0-5 1-8 1h-1v-3c1-2 1-4 1-5 1-2 1-2 2-3h2z" class="S"></path><path d="M621 626c3-1 6 0 9 0h1l-3 1 1 1-1 1v3h-1v-1l-3-3c-2 0-2-1-3-2z" class="T"></path><path d="M371 735c2 1 4 2 6 2l-1 1 2 2c0-1 0-1 1-2h1v1h2v1-1h0c-2-1-3-3-4-5h0l-1-2c0-2 0-3-2-4h-1l1-1c2 1 3 3 5 5 1 0 4 2 4 4l9 10c6 7 15 13 22 19 1 1 4 5 6 6 3 0 6 0 8 2h2 3l2 3c1 0 1 0 1-1l1-1h1v1c0 1 0 1 1 2h-1c1 1 1 1 2 1l1-2 12 11c1 2 3 3 5 5 1 0 1 0 2 1l-1 2-1-1v1h-1l-3-2v3c1 1 1 2 2 3 1 0 2 1 3 1 1-1 2 1 3 2s3 1 3 2v1l3 3h-1c2 1 2 1 2 3v1l-1 1h3s2 1 2 2l-1 1 1 1c2-1 3 0 5 0 2 1 1 4 4 5l1-1c0 3 2 5 3 8 0 1 1 2 1 3 3 7 5 14 9 21 0 1 1 3 1 4l4 9-1 2c1 1 2 4 3 4 2 6 4 12 6 19 2 10-1 18-6 27l-1 2 1 1h-1c-1-1-1-2-1-4-1 2-3 4-4 6s-1 5-3 6h-1 0c-5-4-10-6-15-9 5-9 6-16 4-27l-2-4c-3-9-9-16-17-22-15-12-33-17-51-23-7-3-14-5-20-9-4-3-8-7-11-12 1 1 3 2 4 2 3 2 5 3 8 4 5 2 11 2 16 0 4-3 7-6 8-11 2-5 1-12-2-17-5-9-14-15-24-13l-9 3c-5 2-12 2-17 1-10-3-14-11-19-19 4 4 9 8 15 9h6 1c2 0 3-1 4-1l1 1 1-1v1 1l1-1h1c1 0 3-1 3-2 1-1 2-2 4-3h2c0-1 1-2 2-2 0-2 3-6 5-8v-1-1c0-2 0-2-2-3h0l-1-3c-1-1-1-2-1-3v-1-1h-1l-1 1v1c-1-1-1-1-1-2v-2c-1 0-1 0-2-1 0 0-1-3-1-4h-1v-1c-2-4-10-7-11-11z" class="Z"></path><path d="M403 782h1c1 3 3 1 5 3h2c1 1 1 1 2 1h1v1h-1c0 2 2 4 4 5l-1 1c-1-1 0-1-1-1h-1c-2-2-4-4-6-4s-3 0-4-1l-1-1h3v-1c-1 0-5 0-6-1h1c0-1 1-1 1-1l1-1zm20 40v-4c1-3 1-8 3-10 1-1 2-1 3-1l1 1c0 2 1 4 0 5-1 2 0 4-1 5l-1 3-2-2-1 1v1c-1 1-1 1-2 1z" class="Y"></path><path d="M399 759c3 2 7 5 11 8 1 2 3 4 5 6l7 10c-5-5-9-13-16-16-1-1-3-2-5-2-4 2-7 5-9 9-1 0-1 1-2 0l2-2c2-2 2-3 1-6l2-1c0-2 0-2-1-3 1-1 1-1 1-2s0 0 1-1c1 1 1 1 2 1l1-1z" class="g"></path><path d="M434 818h-1c0-2 0-3 1-4v-5h0c1 2 1 6 1 8s0 1 1 3v2l-1 2c1 1 2 1 2 2h1v-1l1 1c1 1 1 1 0 2v1c1 1 1 1 1 2h1l1-2v-1c0-1 0-2 1-2 1-3 1-5 2-7v-1c-1-1-1-3-1-4v-1l-1-1 1-1 3 2c-1 9-2 17-8 23h-1c-1-1 0-1-2-1v-3h3l-1-2h-1v-2-1h-1v2h-1c-1-1-2-2-2-3 1-1 1-2 1-4h0v-4z" class="U"></path><path d="M445 841h-1c-3 0-6-1-10-1-10 0-20-1-30-4-2-1-5-2-7-4 2 0 4 1 5 1l6 3c2-1 4-1 6-1h3c2 0 3 0 4 1l1 1c1-1 2-1 4-1l2 1h0 3 1c2 0 4 0 6-1h1l-1 2 1 1 2-2h0v1c1 1 3 1 4 3z" class="t"></path><path d="M417 835c2 0 3 0 4 1-1 1-2 2-4 1-3 0-6 0-9-1 2-1 4-1 6-1h3z" class="l"></path><path d="M377 782c1 0 3-1 3-2 1-1 2-2 4-3h2c0-1 1-2 2-2 0-2 3-6 5-8v-1c1 3 1 4-1 6l-2 2c1 1 1 0 2 0l1 1c1 0 1 0 2-1h2 0c2 0 5-1 7 0h0c1 0 2 0 3 1h2c1 0 2 1 3 2h-1-3l-1-1h-1c-3-1-9-1-12 0h-1c-4 1-7 4-9 6-5 4-11 7-17 6-3-1-4-1-6-3h0 1l2 1c1 0 0-1 1 0s5 1 6 1l1-1h1 0l-1-1h0l5-3z" class="N"></path><path d="M434 818v4h0c0 2 0 3-1 4 0 1 1 2 2 3h1v-2h1v1 2h1l1 2h-3v3c2 0 1 0 2 1-2 1-4 1-6 1h-1-3 0l-2-1c-2 0-3 0-4 1l-1-1c-1-1-2-1-4-1 1-1 1-1 2-1 2-1 3-1 5 0l1-1h-1v-1s2-1 3-2c2-1 3-3 4-5l2-4 1-3z" class="b"></path><path d="M432 837c-1-2-1-2-1-4 1 1 1 1 2 1l3-2v3c2 0 1 0 2 1-2 1-4 1-6 1zm-9-15c1 0 1 0 2-1v-1l1-1 2 2h1l1 1c1 0 2 0 3-1l-2 4c-1 2-2 4-4 5-1 1-3 2-3 2v1h1l-1 1c-2-1-3-1-5 0-1 0-1 0-2 1h-3c-2 0-4 0-6 1l-6-3c4-1 9 0 13-1 1-3 3-6 6-8l2-2h0z" class="d"></path><path d="M423 822c1 0 1 0 2-1v-1l1-1 2 2h1l1 1c1 0 2 0 3-1l-2 4-4 2-1-1c-1 0-1-1-2-2 0-1-1-1-1-2h0z" class="N"></path><path d="M429 821l1 1c1 0 2 0 3-1l-2 4-4 2-1-1c2-1 2-3 3-5z" class="U"></path><path d="M415 832c1-3 3-6 6-8l-1 2c1 1 2 1 3 1s1 0 1 1v1h1c-3 3-9 3-13 5 1 0 1 1 2 1-2 0-4 0-6 1l-6-3c4-1 9 0 13-1z" class="b"></path><path d="M415 832c1-3 3-6 6-8l-1 2c1 1 2 1 3 1s1 0 1 1v1l-9 3z" class="U"></path><path d="M385 742c0-1-2-3-2-4-1-1-2-2-2-4h0c-1-1-1-1-1-2 1 0 4 2 4 4l9 10c6 7 15 13 22 19 1 1 4 5 6 6l6 6h-1c-1 0-2-1-4-2v1l1 1v1l-16-18-2-1h-2c-1-1-1-2-2-3-2-1-3-1-4-3l-1-1c-1-2-2-3-3-3l-2-2c-2-1-4-3-6-5z" class="r"></path><path d="M385 742c0-1-2-3-2-4-1-1-2-2-2-4h0c-1-1-1-1-1-2 1 0 4 2 4 4 0 1 1 2 2 3l7 8 8 7 6 6-2-1h-2c-1-1-1-2-2-3-2-1-3-1-4-3l-1-1c-1-2-2-3-3-3l-2-2c-2-1-4-3-6-5z" class="d"></path><path d="M423 778v-1l-1-1v-1c2 1 3 2 4 2h1c4 4 8 11 13 13 1 0 1 1 2 2 1 0 1 1 2 1 3 3 6 5 9 7-1 1-1 1-1 2h1v1 1l2 1 2 1-1 1c-3-1-3-3-6-2h0l-7-6v-1h-2c-2-1-2-1-3-1-2-1-13-17-15-19z" class="t"></path><path d="M371 735c2 1 4 2 6 2l-1 1 2 2c0-1 0-1 1-2h1v1h2v1-1h0c-2-1-3-3-4-5h0l-1-2c0-2 0-3-2-4h-1l1-1c2 1 3 3 5 5 0 1 0 1 1 2h0c0 2 1 3 2 4 0 1 2 3 2 4v1h-1 0c-1 1-1 1-1 2h0c1 1 2 1 3 1 2 0 2 1 3 2h1c1 1 2 3 3 4 1 2 4 4 5 5s1 0 1 1c2 1 3 2 4 3 3 2 7 5 10 8 1 1 2 2 2 3v1c-2-2-4-4-5-6l-11-8-1 1c-1 0-1 0-2-1-1 1-1 0-1 1s0 1-1 2c1 1 1 1 1 3l-2 1v-1c0-2 0-2-2-3h0l-1-3c-1-1-1-2-1-3v-1-1h-1l-1 1v1c-1-1-1-1-1-2v-2c-1 0-1 0-2-1 0 0-1-3-1-4h-1v-1c-2-4-10-7-11-11z" class="Y"></path><path d="M388 751c-1-1-2-1-2-2l-1-1c1-1 2-1 3 0h0c1 1 1 1 2 1l3 3c-2-1-3-1-5-1z" class="N"></path><path d="M388 751c2 0 3 0 5 1 1 3 4 5 6 7l-1 1c-1 0-1 0-2-1-1 1-1 0-1 1s0 1-1 2c1 1 1 1 1 3l-2 1v-1c0-2 0-2-2-3 0-2 0-3-1-5-1-1 0-1 0-2-1-2-2-3-2-4z" class="b"></path><path d="M390 755c1 1 2 1 2 3 1 0 1 0 2 1l-1 1 1 2c1 1 1 1 1 3l-2 1v-1c0-2 0-2-2-3 0-2 0-3-1-5-1-1 0-1 0-2z" class="N"></path><path d="M421 771c3 0 6 0 8 2h2 3l2 3c1 0 1 0 1-1l1-1h1v1c0 1 0 1 1 2h-1c1 1 1 1 2 1l1-2 12 11c1 2 3 3 5 5 1 0 1 0 2 1l-1 2-1-1v1h-1l-3-2v3c-3 0-3-1-4-2h-1l-1-1c-1 0-2-1-3-2l-2 2c-1 0-1-1-2-1-1-1-1-2-2-2-5-2-9-9-13-13l-6-6z" class="P"></path><path d="M431 773h3l2 3c1 0 1 0 1-1 1 1 0 2 0 2l-1 1c-1-2-3-1-4-2 0-2 0-2-1-3z" class="Z"></path><path d="M446 791v-1h1c1 1 1 2 3 2 0-2 0-2-1-3-2 0-2-1-3 0l-1-1c0-1 1-1 1-1 1-1 1-1 1-2l2 2c1 0 0 0 1 1 1 0 2 0 2 1v1c-1 1-1 1 0 2v1h1v-1-3l1-1 1 1c-1 1-1 1-1 3l1 1v3c-3 0-3-1-4-2h-1l-1-1c-1 0-2-1-3-2z" class="b"></path><path d="M481 889l2 1c1 0 2-1 2-1 1-1 1-2 1-2l1-1-1-2h1 0l2 2c-1 1-1 1-1 3l2-2h0v3c1 0 1 0 2 1h0l1 2-2 2h1c1 1 1 2 2 2 1 1 2 2 2 3v1 1 1 4c-1 2-2 7-2 8v1c1 2 1 3 2 5h0c1-2 1-3 2-5l1-1v-1h0v-2c1-4 0-7 1-10h1v10l1 3v2c-1 2-3 4-4 6s-1 5-3 6h-1 0c-5-4-10-6-15-9 5-9 6-16 4-27l-2-4z" class="N"></path><path d="M497 921l1 2c-1 2-1 5-3 6h-1v-2c-1-1 2-4 3-6z" class="AL"></path><path d="M491 910c1 0 1 0 2 1h0 1c-1 2-1 4-1 6h0v-3h0l-2 2h-1c-1 0-1-1-2-1h0 1v-1l-1-1c1 0 2 1 3 2 0-1 0-2-1-4h0l1-1z" class="Y"></path><path d="M501 912l1 3v2c-1 2-3 4-4 6l-1-2c1-3 3-6 4-9z" class="AS"></path><path d="M481 889l2 1c1 0 2-1 2-1 1-1 1-2 1-2l1-1-1-2h1 0l2 2c-1 1-1 1-1 3l2-2h0v3c1 0 1 0 2 1h0l1 2-2 2h1c1 1 1 2 2 2 1 1 2 2 2 3v1 1 1 4c-1-3-1-7-2-9-1 0-1 1-1 1-1 1-1 2-1 2-1 1-1 1-1 2l1 1-1 1-1-1v-4h1v-2-1c-1-1-1-1-1-2l2-2h-1s-1 0-1 1l-2 4h0c0-2 0-4 1-6h-2v-1-1h-1l-1 2h-1l-1 1-2-4z" class="Y"></path><path d="M446 791c1 1 2 2 3 2l1 1h1c1 1 1 2 4 2 1 1 1 2 2 3 1 0 2 1 3 1 1-1 2 1 3 2s3 1 3 2v1l3 3h-1c2 1 2 1 2 3v1l-1 1h3s2 1 2 2l-1 1 1 1c2-1 3 0 5 0 2 1 1 4 4 5l1-1c0 3 2 5 3 8 0 1 1 2 1 3 3 7 5 14 9 21 0 1 1 3 1 4l4 9-1 2c1 1 2 4 3 4 2 6 4 12 6 19 2 10-1 18-6 27l-1 2 1 1h-1c-1-1-1-2-1-4v-2l-1-3v-10c-1-8-2-15-5-22s-7-13-13-19c-9-11-24-16-38-20-1-2-3-2-4-3v-1h0l-2 2-1-1 1-2c6-6 7-14 8-23 0-2-1-5-2-7-2-3-5-6-7-9h0c1 0 1 0 3 1h2v1l7 6h0c3-1 3 1 6 2l1-1-2-1-2-1v-1-1h-1c0-1 0-1 1-2-3-2-6-4-9-7l2-2z" class="Z"></path><path d="M451 822h2c1 1 2 2 2 3h-1c-1 0-2-1-3-1v-2z" class="U"></path><path d="M451 831c-2-2-2-3-1-5v-1c1 1 2 1 3 1h1l-3 5z" class="C"></path><path d="M488 845c2 1 3 3 4 5 2 3 4 5 6 7l4 9-1 2c-5-8-9-15-15-22l2-1z" class="N"></path><path d="M465 832h2c1 1 3 2 4 4 0 0 1 0 1 1 2 1 3 3 5 5v1h-2v1h2v2c0 1 0 1 1 2 2 1 3 2 3 3-1 1-1 1-3 1h1c0 1 1 2 1 2l-9-6h2 0c1 0 2-1 2-1v-1c-1 0-2-1-3-1l1-1-1-1 1-1c0-1 0-2-1-3v-1c-2-1-3-4-5-4-1 0-1-1-2-2z" class="X"></path><path d="M503 896v1c1 0 1 1 1 2h1c1-1 0-2 1-3 1 5 1 9-1 14v1c-1 2-1 5-1 7l-1 2 1 1h-1c-1-1-1-2-1-4v-2-1c1-1 1-2 1-3-1-1-1-2-1-2v-5c1-3 0-5 1-8z" class="b"></path><path d="M499 878v-1c1-2 0-2 1-4 2 1 3 7 4 9v2c1 3 3 9 2 12-1 1 0 2-1 3h-1c0-1 0-2-1-2v-1h-1v-4c0-3-1-5-2-8 0-1 0-3-1-3 0-1 0-1-1-1l1-1v-1z" class="Y"></path><path d="M502 892l2 2h1c-1-3-1-5-1-7-1-2 0-1 0-2v-1c1 3 3 9 2 12-1 1 0 2-1 3h-1c0-1 0-2-1-2v-1h-1v-4z" class="N"></path><defs><linearGradient id="BQ" x1="498.845" y1="879.599" x2="492.986" y2="883.104" xlink:href="#B"><stop offset="0" stop-color="#47474c"></stop><stop offset="1" stop-color="#605f61"></stop></linearGradient></defs><path fill="url(#BQ)" d="M485 859l3 3 1-1v2h1c1 2 1 2 2 3h1 0c0 2 1 3 2 5 1 0 1 0 1 1v1h1c0 1 0 1-1 2 1 1 1 2 3 3v1l-1 1c1 0 1 0 1 1 1 0 1 2 1 3 1 3 2 5 2 8v4h1c-1 3 0 5-1 8v5s0 1 1 2c0 1 0 2-1 3v1l-1-3v-10c-1-8-2-15-5-22s-7-13-13-19l2-2z"></path><path d="M454 826c1 0 2 0 2 1l1 1 1-1v1l-1 2h1c0-1 1-1 1 0 2 0 4-1 5 1v1h1c1 1 1 2 2 2 2 0 3 3 5 4v1c1 1 1 2 1 3l-1 1 1 1-1 1c1 0 2 1 3 1v1s-1 1-2 1h0-2c-6-4-11-6-17-8l-1-2h-1c-1-1-1-2-1-3h1c0-2-1-2-2-3l1-1 3-5z" class="P"></path><path d="M465 836h1c0 1 1 2 2 2 1 1 1 0 2 1-1 2-1 2-2 3h-2l-1-2 1-1v-1h-1v-2z" class="l"></path><path d="M456 838c-1 0-1-1-1-1v-2c1-2 2-1 4-1h2c1 0 1 2 2 2h2v2h1v1l-1 1 1 2c-1 0-2-1-3-1-1-1-2-1-3-1h-2l-2-2z" class="g"></path><path d="M456 838h1c1 0 3-1 4-2l1 1c-1 1-1 2-2 3h-2l-2-2z" class="q"></path><path d="M446 791c1 1 2 2 3 2l1 1h1c1 1 1 2 4 2 1 1 1 2 2 3 1 0 2 1 3 1 1-1 2 1 3 2s3 1 3 2v1l3 3h-1c2 1 2 1 2 3v1l-1 1h3s2 1 2 2l-1 1 1 1c2-1 3 0 5 0 2 1 1 4 4 5l1-1c0 3 2 5 3 8 0 1 1 2 1 3 3 7 5 14 9 21 0 1 1 3 1 4-2-2-4-4-6-7-1-2-2-4-4-5l-2 1c-6-9-14-17-21-24-4-4-7-8-10-11l-9-6c2 8 2 13 0 21-1 3-2 6-2 9l1 1h1c1 1 2 1 3 1 0-2 0-4 1-5 1 1 2 1 2 3h-1c0 1 0 2 1 3h1l1 2c6 2 11 4 17 8l9 6 5 5-2 2c-9-11-24-16-38-20-1-2-3-2-4-3v-1h0l-2 2-1-1 1-2c6-6 7-14 8-23 0-2-1-5-2-7-2-3-5-6-7-9h0c1 0 1 0 3 1h2v1l7 6h0c3-1 3 1 6 2l1-1-2-1-2-1v-1-1h-1c0-1 0-1 1-2-3-2-6-4-9-7l2-2z" class="AK"></path><path d="M444 835l1 1h1c1 1 2 1 3 1 0-2 0-4 1-5 1 1 2 1 2 3h-1c0 1 0 2 1 3h1l1 2c-4-1-7-2-10-4v-1z" class="d"></path><path d="M469 822c2 0 4 3 6 4l2 1h1c1 1 1 1 1 2s1 1 2 2h0c1 1 2 1 3 2l1-1c1 0 1-1 2 0h1c3 7 5 14 9 21 0 1 1 3 1 4-2-2-4-4-6-7-1-2-2-4-4-5-6-8-13-16-19-23z" class="U"></path><path d="M481 831h0c1 1 2 1 3 2l1-1c1 0 1-1 2 0h1c3 7 5 14 9 21h-1c0-1-1-1-1-2-2-4-4-7-5-10-2-2-3-5-5-6s-3-2-4-4z" class="N"></path><path d="M462 815v-1c2 1 3 2 4 3s2 1 2 2l1 1c1-1 3-2 3-3h1l-1-2h2l-1 1 1 1c2-1 3 0 5 0 2 1 1 4 4 5l1-1c0 3 2 5 3 8 0 1 1 2 1 3h-1c-1-1-1 0-2 0l-1 1c-1-1-2-1-3-2h0c-1-1-2-1-2-2s0-1-1-2h-1l-2-1c-2-1-4-4-6-4-2-1-6-6-7-7z" class="l"></path><path d="M446 791c1 1 2 2 3 2l1 1h1c1 1 1 2 4 2 1 1 1 2 2 3 1 0 2 1 3 1 1-1 2 1 3 2s3 1 3 2v1l3 3h-1c2 1 2 1 2 3v1l-1 1h3s2 1 2 2h-2l1 2h-1c0 1-2 2-3 3l-1-1c0-1-1-1-2-2s-2-2-4-3v1l-12-10c3-1 3 1 6 2l1-1-2-1-2-1v-1-1h-1c0-1 0-1 1-2-3-2-6-4-9-7l2-2z" class="q"></path><path d="M446 791c1 1 2 2 3 2l1 1h1c1 1 1 2 4 2 1 1 1 2 2 3 1 0 2 1 3 1 1-1 2 1 3 2s3 1 3 2v1l3 3h-1c2 1 2 1 2 3-1 0-1 0-2-1h0l-1 1h0l1 1c-1 0-1 0-1 1h-1v-1-3h-1l-1-3c-1 0-1 0-2 1l-1-1-8-6c-3-2-6-4-9-7l2-2z" class="d"></path><path fill="#fff" d="M406 193c3 0 3 2 5 3 0 1 0 1 1 1h1c1 1 3 2 4 3 0 1-1 1 1 2 2 0 3 3 6 4 1 1 1 2 2 3l2 2c1 0 1 0 1-1v1c2 2 5 3 7 5h1l4 2 4 2c2 1 5 3 8 4 3 0 6 0 9 1h5 3c0 2-2 22-3 24l-1 1c-5-1-10-1-15 2-1 0-2 0-3-1l-4 3c-7 7-9 18-9 27l1 14c1 3 1 6 2 8l1 3 9 27 1 4 10 34c0 3-1 5 0 8 0 3 0 6 1 9v1l1 5 1 2v2c0 1 0 2 1 3v4c1 1 1 1 1 2 0 2 0 5 1 7l1 5v3c1 1 1 2 0 3 0 1 0 2 1 3h0v2l1 1v2 1 1c1 0 1 1 1 2v1c0 1 0 1 1 2v2c0 1 0 1 1 2v3c0 2 0 2 2 3v2c1 3 1 7 1 10 0 1 1 3 0 4s-1 1-1 2l-9 21-4 8c-2 3-3 7-5 10 0-3 1-6 2-10v1c-1 1-1 1-2 3h0c-1-1-1-3-1-4-1-2 0-4-1-6v-1c1-3 1-9 0-12v-2c-1-2-1-4-2-6-1-1-1-2-1-2 0-3-2-6-2-8h-1 0l1 3v2l1 1c0 1 0 3 1 4l1 4v2l1 1c0 2-1 3 0 5 0 1 0 2 1 3v3c-1-1-1 0-1-1 0-2 0-3-1-4v-3c0-1-1-2-2-3v-1c-1 2 1 10 2 11 0 2 1 3 1 4 2 3 1 8 2 12 1 0 0 2 0 3-1-1-1-3-2-5h0c1 7 3 14 5 21 0 2 1 5 1 6v1c-1-2-1-3-1-4-1-1-1-1-1-2h0v-1l-1-4v-1c0 2-1 4 0 5 0 1-1 3 0 5l1 1-1 2c-1-3-1-6-1-9s-1-12-3-14c-2-3-2-8-3-12h0c-1 1-2 1-3 1v1h-1l-1-3h0v-1l1-1-1-1h-2l-1-2 3 1c0-1-1-1-1-2-1-1 0-2 0-2h-2l-1-1v-1s-1-1-2-1c-2 1-3 2-4 4-1-2-1-5-2-7l-13-45v-1l-3-10c1-1 2-3 3-4-1-2-1-3-1-5v-1-3c1-1 1-3 1-4s1-1 1-2v-3l1-1c0-3-1-6-2-8 0-2-1-3-2-4h0l-1-2v1c-1-1-1-1 0-1v-3c1-1 1-1 1-2-1-2-1-2-2-3-1 2-1 2-1 3h0c-1 1-1 1-2 0v-4c0-1 1-1 0-2v-1c0-3 1-4 0-7v-1l-13-44c-2-6-4-11-6-17-1-5-3-10-5-15-2-8-6-18-11-25l-1-2c0-1-1-3-2-3-10-16-27-25-44-29l-9-2-2-1-1-1-1-1 1-1h6c7 1 14 0 21 0h36 17 1 15c1 0 3-1 4-1 2 1 3 1 5 0l-3-3 3 1c0-1-3-2-4-4-2-1-3-3-4-5l-2-3c-1-2-1-4-3-6v-3c0-1-1-2-1-3l-1-3c0-1-1-2-1-3z"></path><path d="M415 349h3s0 1 1 2h-1c-1 1-2 2-2 4l-1 2v-1-7z" class="AH"></path><path d="M434 364c0 2 0 4 1 6h0l-2 2h-1c-1-2 0-2 0-4 1-1 1-2 2-4z" class="x"></path><path d="M396 237c1 0 1 0 2-1l-1-2h4-2v2c1 1 1 1 2 1h1 0l1 4h0l-5-3h-1v1 2l-1-4z" class="AH"></path><path d="M401 262c1 1 2 1 2 2 1 2 1 3 2 4 1 4 3 7 3 11-3-5-6-11-7-17z" class="AX"></path><path d="M419 351c0 1 1 2 1 3 0 2 1 4 2 6 1 1 1 1 1 2l1 1 1 1c0 1 0 1-1 2h-1c-1-3-3-5-4-8-1-2 0-4-1-6v-1h1zm-24-121c5 2 11 5 14 10h-1c-2-3-4-5-7-6h-4l1 2c-1 1-1 1-2 1l-1-1v-1l-1-2 1-3z" class="AV"></path><path d="M434 447h1l1 1h0l6 20c-1-1-2-1-2-3 0 0-1-1-1-2h0l-5-16z" class="AL"></path><path d="M395 236l1 1 1 4c2 9 6 18 9 26l-1 1c-1-1-1-2-2-4 0-1-1-1-2-2l-2-11c-1-3-2-6-3-8s-2-3-2-5l1-2z" class="AO"></path><path d="M428 421l7 21c0 1 2 5 1 6h0l-1-1h-1s0-2-1-3l-4-13-2-7 1-3z" class="AY"></path><path d="M414 364c1-1 1-1 0-2h1l1 4 1 12c-1 3-1 5-1 7v4h0l-1-2v1c-1-1-1-1 0-1v-3c1-1 1-1 1-2-1-2-1-2-2-3-1 2-1 2-1 3h0c-1 1-1 1-2 0v-4c0-1 1-1 0-2v-1c0-3 1-4 0-7v-1l3 9v-12z" class="AM"></path><path d="M425 286h2c1 3 3 7 4 11v3c-1 3 1 7 1 10h0l-3-2-4-22z" class="r"></path><path d="M399 251l-1-1c-2-4-2-7-5-10 1 1 1 2 1 3h-1v2c1 1 1 3 1 4l-3-7-1-2c0-2-1-3-2-5-1-1-1-2-2-3l-1-1 1-1h3 0 6l-1 3 1 2v1l-1 2c0 2 1 3 2 5s2 5 3 8z" class="x"></path><path d="M389 230h6l-1 3 1 2v1l-1 2c-1-3-3-5-5-8z" class="v"></path><path d="M445 220c2 1 5 3 8 4 3 0 6 0 9 1l-1 1c-6 1-12 0-17 0-7 0-14 1-21 0h4c1-1 2-2 3-2 1 1 3 1 4 1 2-1 3-1 5-1v-2h4 0 1l1-2z" class="AU"></path><path d="M445 220c2 1 5 3 8 4 3 0 6 0 9 1l-1 1c-3-1-6 0-9-1-2 0-4 0-6-1-2 0-3-1-4 0h-2-1v-2h4 0 1l1-2z" class="Z"></path><path d="M443 469l9 32c1 7 3 14 5 21 0 2 1 5 1 6v1c-1-2-1-3-1-4-1-1-1-1-1-2h0v-1l-1-4v-1c0 2-1 4 0 5 0 1-1 3 0 5l1 1-1 2c-1-3-1-6-1-9s-1-12-3-14c-2-3-2-8-3-12v-1c0-1 0-2-1-3v-1c-1-2-1-5-2-7s-2-5-2-7c-1-3-1-4 0-7z" class="AL"></path><path d="M414 364h0c0-2-1-4-2-6l-4-17c0-2-3-5-2-8 1 4 3 8 5 11 1-1 0-2 0-3 1 1 1 0 1 2l1-2c0 2 1 4 1 6 0 1-1 7 0 8 0 1 1 1 1 1v1h0c0 2 1 3 1 4 2 5 8 9 11 13l-1 1-9-11c-1 0-1 1-1 2l-1-4h-1c1 1 1 1 0 2z" class="AN"></path><path d="M439 463h0c0 1 1 2 1 2 0 2 1 2 2 3l1 1c-1 3-1 4 0 7 0 2 1 5 2 7s1 5 2 7v1c1 1 1 2 1 3v1h0c-1 1-2 1-3 1v1h-1l-1-3h0v-1l1-1-1-1h-2l-1-2 3 1c0-1-1-1-1-2-1-1 0-2 0-2h-2l-1-1v-1l-1-3c-1-1 0-1-1-2l1-1-1-1h-2v-1c1 0 1-1 1-2h1l1-1v-1s-1-1-1-2c-1-2 0-5 2-7z" class="AM"></path><path d="M414 209l3 3v1c1 3 5 5 6 8 1 1 2 1 3 2h1c-1-2-1-3-2-4s-1-1-1-2l1-1v1c2 1 3 2 5 3h0 3 1c2 0 2 0 3-1v-3l4 2 4 2-1 2h-1 0-4v2c-2 0-3 0-5 1-1 0-3 0-4-1-1 0-2 1-3 2h-4-26 1 15c1 0 3-1 4-1 2 1 3 1 5 0l-3-3 3 1c0-1-3-2-4-4-2-1-3-3-4-5l1-1c-1-2-1-3-1-4z" class="Y"></path><path d="M441 218l4 2-1 2h-1 0c-2-1-2-1-3-1l-1-1 2-2z" class="P"></path><path d="M426 223h1c-1-2-1-3-2-4s-1-1-1-2l1-1v1c2 1 3 2 5 3h0l4 2h0 4 0 0v1h-1-2c-1 0-3 0-3-1-1 0-2 1-3 1h-1l-1 1-1-1z" class="g"></path><path d="M417 378c0 9 3 16 5 23l6 20-1 3-1-2h0v3c-2 0-2 0-3-1l-3-4-1-1c-1-2-1-3-2-5v-3c1-1 1-3 1-4s1-1 1-2v-3l1-1c0-3-1-6-2-8 0-2-1-3-2-4v-4c0-2 0-4 1-7z" class="AL"></path><path d="M417 411c1 2 2 4 4 4v4l-1 1-1-1c-1-2-1-3-2-5v-3z" class="AS"></path><path d="M406 267c2 5 3 10 4 15 2 6 3 12 3 17 1 4-1 7 0 10 0 6 2 12 2 19 1 7-1 14 0 21v7s-1 0-1-1c-1-1 0-7 0-8 0-2-1-4-1-6l-1 2c0-2 0-1-1-2 0-6 1-11 1-16s-2-10-2-15c-1-4 1-8 1-12 1-6-2-13-3-19 0-4-2-7-3-11l1-1z" class="x"></path><path d="M406 193c3 0 3 2 5 3 0 1 0 1 1 1h1c1 1 3 2 4 3 0 1-1 1 1 2 2 0 3 3 6 4 1 1 1 2 2 3l2 2c1 0 1 0 1-1v1c2 2 5 3 7 5h1v3c-1 1-1 1-3 1h-1-3 0c-2-1-3-2-5-3v-1l-1 1c0 1 0 1 1 2s1 2 2 4h-1c-1-1-2-1-3-2-1-3-5-5-6-8v-1l-3-3c0 1 0 2 1 4l-1 1-2-3c-1-2-1-4-3-6v-3c0-1-1-2-1-3l-1-3c0-1-1-2-1-3z" class="q"></path><path d="M425 217h2c0-1 1-1 1-1h1v1c1-1 1 0 2-1l1-1v1 1l1 1c1-1 1 0 2-1 1 0 1-1 1-1h1v3c-1 1-1 1-3 1h-1-3 0c-2-1-3-2-5-3z" class="r"></path><path d="M406 193c3 0 3 2 5 3 0 1 0 1 1 1h1c1 1 3 2 4 3 0 1-1 1 1 2 2 0 3 3 6 4 1 1 1 2 2 3l-3-1h-1c-1-2-1-2-3-3s-2-2-3-3l-1-1v1l-2-2-2-2c0 2 1 5 2 7 0 1 1 2 1 4 0 1 0 2 1 4l-1 1-2-3c-1-2-1-4-3-6v-3c0-1-1-2-1-3l-1-3c0-1-1-2-1-3z" class="Z"></path><defs><linearGradient id="BR" x1="429.697" y1="321.423" x2="444.233" y2="322.808" xlink:href="#B"><stop offset="0" stop-color="#7c796c"></stop><stop offset="1" stop-color="#a19a93"></stop></linearGradient></defs><path fill="url(#BR)" d="M432 310c0-3-2-7-1-10l1 2 3 5 1-1v-1l2-2 1 3 9 27 1 4h-2v1l-2 1v-1-1l-2 1v3l-1 1v-2l-1 1 1 1h1v1c-1 0-2 1-2 1-1-1-1-3-2-4 0-3-1-5-2-7-1-1-2-1-3-2l-2-10-2-5v-1l-1-7 3 2h0z"></path><path d="M430 316c2 1 3 1 4 3 0 1 0 3 1 5l-3-3-2-5z" class="AL"></path><path d="M429 308l3 2h0c1 2 1 3 1 4v2h-1 0c-1-1-1-1-2-1l-1-7z" class="AK"></path><path d="M432 321l3 3 2 9c-1-1-2-1-3-2l-2-10z" class="AW"></path><defs><linearGradient id="BS" x1="435.673" y1="310.426" x2="448.457" y2="335.769" xlink:href="#B"><stop offset="0" stop-color="#666057"></stop><stop offset="1" stop-color="#979289"></stop></linearGradient></defs><path fill="url(#BS)" d="M438 303l1 3 9 27 1 4h-2v1l-2 1v-1-1l-2 1v-3c0-2-1-3-1-4l-1-1c1 0 1-1 1-1v-1c0-2 0-3-1-5-1-3-2-7-4-10 0-1-1-1-1-2l-1-4 1-1v-1l2-2z"></path><path d="M438 303l1 3-1 2v1h-1c0 1 0 1-1 2l-1-4 1-1v-1l2-2z" class="t"></path><path d="M415 424c1-1 2-3 3-4-1-2-1-3-1-5v-1c1 2 1 3 2 5l1 1 3 4c1 1 1 1 3 1v-3h0l1 2 2 7 4 13c1 1 1 3 1 3l5 16c-2 2-3 5-2 7 0 1 1 2 1 2v1l-1 1h-1c0 1 0 2-1 2v1h2l1 1-1 1c1 1 0 1 1 2l1 3s-1-1-2-1c-2 1-3 2-4 4-1-2-1-5-2-7l-13-45v-1l-3-10z" class="AP"></path><path d="M423 424c1 1 1 1 3 1v-3h0l1 2 2 7c0-1-1-1-1-2h0l-1 2v1c1 1 1 2 1 4l-1-2-1 2v2c-1-1-1-1-1-2l1-2c0-1-1-2-2-3h0c0-2 0-3-1-4v-3z" class="AM"></path><path d="M431 480c2 2 3 2 6 2v-1h-1c-1-1-1-1-1-2-1 0-1-1-2-2h0v-2-1c1-1 1-3 1-4v-2c0-1-1-1-1-2v-1h1c0 1 0 2 1 2v6l2 1h-1c0 1 0 2-1 2v1h2l1 1-1 1c1 1 0 1 1 2l1 3s-1-1-2-1c-2 1-3 2-4 4-1-2-1-5-2-7z" class="r"></path><defs><linearGradient id="BT" x1="424.426" y1="262.367" x2="460.2" y2="260.126" xlink:href="#B"><stop offset="0" stop-color="#342922"></stop><stop offset="1" stop-color="#6c645e"></stop></linearGradient></defs><path fill="url(#BT)" d="M467 225h3c0 2-2 22-3 24l-1 1c-5-1-10-1-15 2-1 0-2 0-3-1l-4 3c-7 7-9 18-9 27l1 14c1 3 1 6 2 8l-2 2v1l-1 1-3-5-1-2v-3c-1-4-3-8-4-11h-2v-6c0-8 1-16 4-23 6-14 19-21 33-27l5-2c1-1 1-1 1-2l-1-1z"></path><path d="M425 280c1 1 2 2 2 4 1 2 1 3 2 5v-2c1 0 1 0 1-1l1 4h1l1-1c2 2 2 5 2 7 1-1 1 0 1-1 1 3 1 6 2 8l-2 2v1l-1 1-3-5-1-2v-3c-1-4-3-8-4-11h-2v-6z" class="AU"></path><path d="M431 295h1c2 2 3 3 4 5l-1 2-1 1v-1h-2l-1-2v-3-2z" class="t"></path><path d="M431 295l1 1v1c2 2 2 3 2 5h-2l-1-2v-3-2z" class="g"></path><defs><linearGradient id="BU" x1="450.959" y1="242.257" x2="471.855" y2="235.932" xlink:href="#B"><stop offset="0" stop-color="#bcb7ad"></stop><stop offset="1" stop-color="#eae9e8"></stop></linearGradient></defs><path fill="url(#BU)" d="M467 225h3c0 2-2 22-3 24l-1 1c-5-1-10-1-15 2-1 0-2 0-3-1 1-2 1-1 2-2l-1-1h-3c0-3 3-3 4-5 0-1 1-2 2-3h1c0-2-1-1 0-2 1 0 3 1 4 1h0c1-1 2-1 2-2 1-2 2-3 3-5v-2l5-2c1-1 1-1 1-2l-1-1z"></path><path d="M467 228l-2 12h0v-1h-1-3c-1 1-3 1-4 0h0c1-1 2-1 2-2 1-2 2-3 3-5v-2l5-2z" class="AK"></path><path d="M448 251c1-2 1-1 2-2l-1-1h-3c0-3 3-3 4-5 0-1 1-2 2-3h1c0-2-1-1 0-2 1 0 3 1 4 1 1 1 3 1 4 0h3 1v1l-1 7c-5 0-9 0-14 3l-2 1z" class="r"></path><path d="M434 331c1 1 2 1 3 2 1 2 2 4 2 7 1 1 1 3 2 4 0 0 1-1 2-1v-1h-1l-1-1 1-1v2l1-1v-3l2-1v1 1l2-1v-1h2l10 34c0 3-1 5 0 8 0 3 0 6 1 9v1l1 5 1 2v2c0 1 0 2 1 3v4c1 1 1 1 1 2 0 2 0 5 1 7l1 5v3c1 1 1 2 0 3 0 1 0 2 1 3h0v2l1 1v2 1 1c1 0 1 1 1 2v1c0 1 0 1 1 2v2c0 1 0 1 1 2v3c0 2 0 2 2 3v2c1 3 1 7 1 10 0 1 1 3 0 4s-1 1-1 2l-9 21-4 8c-2 3-3 7-5 10 0-3 1-6 2-10l13-24c0-2 2-4 2-6 0-1-1-4-1-5l-4-19c-3-5-3-11-5-16l-10-36-14-47-4-13z" class="AY"></path><path d="M443 352h1c1 0 1 0 1 1 1 1 1 1 1 2l2 1v1h-2c-1 1-1 0-1 1l-2-6z" class="AD"></path><path d="M448 368v-2h1v1h0c1 0 1-1 1-1 0-2-1-1 1-2v1c0 2 1 3 1 5 0 1 1 1 1 2v2 1 1l2 3c1 2 1 4 1 7l-1-3h-1v4c0 1 0 2-1 3v-2c0-1-1-1-1-2v-3c0-1-1-2-1-3 0-3-2-4-2-7-1-1-1-3-1-5z" class="AT"></path><path d="M438 344l2 1v1c0 1 1 2 1 3l2 3 2 6 3 10c0 2 0 4 1 5 0 3 2 4 2 7 0 1 1 2 1 3v3c0 1 1 1 1 2v2 1h-1l-14-47z" class="v"></path><path d="M453 390c1-1 1-2 1-3v-4h1l1 3c0 3 2 6 2 8 0 3 1 5 1 7 1 3 1 5 1 8 1 1 2 3 2 5v6c2 5 3 10 4 15 1 3 1 5 1 8-3-5-3-11-5-16l-10-36h1v-1z" class="AO"></path><path d="M449 337l10 34c0 3-1 5 0 8 0 3 0 6 1 9v1l1 5 1 2v2c0 1 0 2 1 3v4c1 1 1 1 1 2 0 2 0 5 1 7l1 5v3c1 1 1 2 0 3l-4-16v-4l-1-2-1-4v-2l-1-2v-3l-1-4c0-2 0-3-1-5 0-1-1-2-1-3v-1h0c0-1-1-2-1-3s0-3-1-4c0-2 0-4-1-5 0-2-1-3-1-4 1-1 1-1 1-2h-1l-3-7c0-1-1-1-1-2s-1-2-1-3 0-2-1-3c-2-1-2-3-3-5v-3l2-1v1 1l2-1v-1h2z" class="AW"></path></svg>