<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="80 48 484 616"><!--oldViewBox="0 0 624 752"--><style>.B{fill:#7d100d}.C{fill:#2e1111}.D{fill:#6b1110}.E{fill:#440f0e}.F{fill:#341111}.G{fill:#a91210}.H{fill:#75100f}.I{fill:#5b1312}.J{fill:#93110f}.K{fill:#261110}.L{fill:#251010}.M{fill:#491110}.N{fill:#83100e}.O{fill:#6a1211}.P{fill:#740d0c}.Q{fill:#b31311}.R{fill:#f8e2e2}.S{fill:#c41916}.T{fill:#b80e0b}.U{fill:#8b0f0f}.V{fill:#b0110f}.W{fill:#1c1010}.X{fill:#190c0c}.Y{fill:#43100e}.Z{fill:#9f0e0d}.a{fill:#3c1313}.b{fill:#581010}.c{fill:#d61511}.d{fill:#5a1211}.e{fill:#f8b9bb}.f{fill:#dd1713}.g{fill:#f2c1c1}.h{fill:#901615}.i{fill:#cb1915}.j{fill:#db3732}.k{fill:#f8d5d4}.l{fill:#f5cccc}.m{fill:#ef7b7c}.n{fill:#f7dcdd}.o{fill:#fbeaea}.p{fill:#f6c1c3}.q{fill:#f4d6d6}.r{fill:#eb6b69}.s{fill:#da4745}.t{fill:#f6aeb0}.u{fill:#c81714}.v{fill:#fb9c9c}.w{fill:#f9eff0}.x{fill:#e87172}.y{fill:#ea9192}.z{fill:#f39496}</style><path d="M316 256h1v1 6h-1v-7z" class="R"></path><path d="M173 293h1l1 1h0v2l-1 1-1-1v-2h-1l1-1z" class="n"></path><path d="M377 545l1-1v1c1 1 1 1 1 0 1 1 2 1 2 2-1 1-1 1-2 1l-2-2h1l-1-1zm66-185c2 0 2 2 4 2h0-1c-1 1-1 1-2 1s-1-1-2-2l1-1z" class="o"></path><path d="M132 111l3 3c0 1-1 1-1 1h-2v-4z" class="V"></path><path d="M445 351h0c1 2 3 3 3 5h0-1c-1-1-2-2-2-3h0-1c1-1 1-1 1-2z" class="o"></path><path d="M181 349h2l1 1-2 2h1 1v2h0s0-1-1-1l-1 1-1-1v-1-3z" class="n"></path><path d="M338 330h1l1 1c0 1 0 2-1 3h-1c-1-2 0-3 0-4zm-11-54h1s1 0 2 1v2h-1c-1 0-2-1-2-1v-2z" class="c"></path><path d="M388 514c-1-1-1-1-1-2 1-1 2-1 2-1h2 0c0 1-1 3-3 3h0z" class="o"></path><path d="M316 274v-1h2 0v4h-1c-1 0-2-1-2-2l1-1z" class="j"></path><path d="M180 308h-2-1l-1 1h0-1l-1-1c1-1 0-1 1-1h0c1-1 1-1 2-1v1c1 0 2 0 2 1h1z" class="R"></path><path d="M165 235c-2 1-2 1-4 1v-1c0-2 2-1 3-2 0 0 0 1 1 1h0v1h0z" class="q"></path><path d="M182 302c-1 0-2 0-2-1h0c-1 1-1 2-1 3h0l-1 1v-1l-1-1h0v-3h0c1-1 1-1 2-1l-1 1v1c1 0 1 0 2-1 0 1 1 1 2 2z" class="R"></path><path d="M233 470c1 1 1 3 1 4s0 2-1 3c-1 0-1-1-1-1-1-2 1-5 1-6z" class="r"></path><path d="M313 288c1 0 2 0 3-1l1 1-1 1c-1 0-1 1-1 2h-1c-1 0-1 0-3 1v-1l1-1v-2h1z" class="R"></path><path d="M245 509h1c1 1 2 2 3 4h-1c-1 0-2 0-4-1 1-1 1-2 1-3z" class="r"></path><path d="M444 353l-3-1-1-1s0-1 1-1c1-1 1 0 2 0h2v1c0 1 0 1-1 2z" class="n"></path><path d="M398 477c1 5 0 10 1 15-1 0-2-1-2-2v-4h0v-1c1-2 1-5 1-8zM144 237h1v3 10h0v-6-1h0c-1 1-1 2-1 3-1-3 0-6 0-9zm82-137v-3c1 0 2-1 3-1l2 2-2 2h-3z" class="j"></path><path d="M361 438c0 1 0 2 1 3s2 2 2 3h0-1c-2-1-3-2-4-3h-1l3-3z" class="e"></path><path d="M476 284h1c1 1 1 2 1 4 0 1-1 1-1 2h-1c-1-1-1-1-1-2 0-2 0-3 1-4z" class="f"></path><path d="M316 233h1c1 1 2 1 2 3 0 1 0 1-1 2h-1l-2-2c0-2 1-2 1-3z" class="j"></path><path fill="#0e0b0a" d="M557 79c2-1 2-1 4-1l1 4-6 3c1-2 2-4 4-5-1-1-2 0-3-1z"></path><path d="M396 475h1c1-1 0-4 1-5v-1 8c0 3 0 6-1 8v1h0l-1-6v-5z" class="s"></path><path d="M162 258h1v1l1 1v1l1-1c1 0 1 0 2 1v2h-1l-2-1c-1-1-1 0-2 0h-1c0-2 1-3 1-4z" class="R"></path><path d="M397 490c0 1 1 2 2 2v8l-1 1v7c0-2 0-3-1-4 1-5 0-9 0-14z" class="m"></path><path d="M311 297h1c1 1 2 1 3 2 2 1 3 1 5 2l-1 1c-1 0-3-1-4 0l-1-1c-1-1-2-2-3-2v-2z" class="R"></path><path d="M343 345l2 12c-1-1-2-1-3-1v-8c0 1 0 1 1 2v-5z" class="f"></path><path d="M171 298l1-1v2h2v1 1-1h-2v1 2l-1 1h-2c0-2 0-3 1-4v-1l1-1z" class="R"></path><path d="M497 303h1l1 1-1 13h-1v-3s-1-1-1-2l1-9z" class="f"></path><path d="M388 514l-3 3c0 1 0 1-1 1l-1-1v-1h1c-1-1-1-1-1-2-1 0 0-1 0-1 0-1 0-1 1-1s1 0 1 1h1c1 0 1 0 1 1h1 0z" class="R"></path><path d="M374 542c0-1 0-2 1-3 1 0 1 0 2 1 0 1 0 1-1 1 0 1 0 2 1 2h0l3-3v1c0 1-1 3-1 4s0 1-1 0v-1l-1 1h-1c-1 0 0 0 0-1l-2-2zM195 100c1-1 1-2 3-3h2c1-1 2-1 3 0h0v2h0l1 1h-9z" class="w"></path><path d="M118 99l3 2c-1 2 0 4-1 6-1 1-1 1-2 1-1-1 0-7 0-9h0z" class="c"></path><path d="M288 123c1-4 3-7 5-10v4c-2 4-3 7-4 11v-3c1-1 1-1 1-2v-1c-1 0-1 1-1 2l-1-1z" class="T"></path><path d="M176 261h-1l-1-1h-1v1l-1-1v-2h-1l-1 1c-1-1-2 0-3-1 0 0 0-1-1-1h0c1-1 1-1 2-1h1 3 1v1c1 0 1 1 1 2 1 0 1 1 2 1v1z" class="q"></path><path d="M206 401v2l-1 1h-1 0l-2-2c-1-1-1-2-1-3v-1h-1 0-1l-2-1v-1-1 1c1 0 2 1 3 1h1v1c2 0 2 1 3 2 1 0 1 1 2 1z" class="w"></path><path d="M433 367c0-1-1-3-1-4 1-5 1-10 2-14l1 1v9 5c-1 0-1-1-1-2l-1 1v4z" class="j"></path><path d="M313 273l1 1h0 1 1 0l-1 1c0 1 1 2 2 2h1v2c1 0 1 0 1 1l-1 2c0 1 0 1 1 1l-1 1c-1-1-3-2-3-4 0 0 0-1-1-1v-1c0-1-1-3-1-5z" class="o"></path><path d="M496 312c0 1 1 2 1 2v3h1c0 2 0 8-1 10h-1c-1-5-1-10 0-15zm-154 36l-2-21h1c1 3 1 6 1 8l1 10v5c-1-1-1-1-1-2z" class="c"></path><path d="M419 100c7-1 15 1 22 0 0-1 0-1 1-2 2 0 5 0 8 1 2 1 3 1 5 1 3-1 6-1 9 0h-45z" class="t"></path><path d="M253 478h2c1 1 2 0 3 1 0 1 0 2-1 3l-3-2-1 1h1l-1 1h1l1-1v1 1l-1 1h0c2 1 3 1 5 1 1 0 2 0 3 1l1 1h-1c-1-1-3-2-5-2h-2c-1-1-1-1-2-1h-1c0-1 0-1-1-2h1l1-1v-3z" class="g"></path><path d="M293 224c0 5 1 9 2 13-2-1-3-1-5-3h1c0-2 0-2-1-3 0-1 1-2 1-2 0-1 1-1 1-1 1-1 1-2 1-4h0z" class="T"></path><path d="M298 180v-1c1 1 0 5 1 6v-1-1-1-4s1 0 1-1v-2c0-1 1-2 1-2 0 3-1 6-1 9s1 6 0 9c0 2 1 3-1 4l-1-15z" class="c"></path><path d="M184 354l1 1 1 1h0c1 1 1 1 2 1v1l1 1h1l1 1-1 1v1c1 1 1 1 3 1h-2-1v-3h-5c-1-1 0-2 0-3-2 0-2 1-3 0h-1-1v-2h-1c1-1 2-1 2-1l1 1h1 1 0v-1h0z" class="R"></path><path d="M144 246c0-1 0-2 1-3h0v1 6h0c1 5 1 9 0 14-2-5-2-12-1-18zm161-146h3v1c-3 2-8 4-10 7-1 1-2 3-2 4-1 2-2 3-3 5v-4c1-2 3-5 6-8 2-1 5-2 6-5z" class="S"></path><path d="M498 279c2-1 0-2 1-4 0 10 1 20 0 29l-1-1h-1c0-1 1-3 1-4v-7-13z" class="s"></path><path d="M146 170c0-4-1-8-1-12l1 1c2 0 3 1 4 1h1c-1 2-2 5-2 7h0l-3 3z" class="G"></path><path d="M314 278v1c1 0 1 1 1 1 0 2 2 3 3 4l1-1 1 1c-1 0-1 0-2 1 0 1 0 3 1 4v1l-2-2-1-1c-1 1-2 1-3 1l1-1h0l-2-3 2-1v-1-1h0-1v1h-1c-1-1-1-1 0-2 1 0 1-1 2-2h0z" class="v"></path><path d="M314 283c1 1 1 3 2 4-1 1-2 1-3 1l1-1h0l-2-3 2-1z" class="n"></path><path d="M396 480l1 6v4c0 5 1 9 0 14v-3c-1-2 0-4-1-5l-1 1h-2v-1l-1-1 2-2 2 2c0-2 1-2 0-3h-2-1l1-2h0c0-1 0-2 1-3h0c0-1 0-1-1-2 1 0 2 0 2-1v-4z" class="p"></path><path d="M433 367v-4l1-1c0 1 0 2 1 2 0 6 2 14 0 19l-1 1-1-1v-1h0c1-1 1-3 1-4l-1-11z" class="c"></path><path d="M304 249v-14l1-1v8c0 5 1 11 2 17 1 2 1 3 3 5h-3c-2-3-2-12-3-15z" class="k"></path><path d="M159 210h0v2 1h0v1c0 1-1 1-1 2h0 0v-2h-2 0l-1-1c-1 1-2 0-3 2 0-1-1-1-1-1v-3h1v1l1 1h1c1 0 2-1 2-2v-1c-1-1-1-2-2-3 1-2 2-2 3-3h1 0 1v1c0 1 1 1 1 2-1 0-1 0-2 1v1c0 1 0 1 1 1z" class="R"></path><path d="M290 199l3 17c-2-1-2-1-4-1v-2l-2-2v-2h0c0-1-1-1-1-2h0c1-1 2 0 3-1v-1l1-2v-4z" class="G"></path><path d="M390 496c2 0 2 0 3 1h0c1 1 1 2 1 2v1c1 1 1 2 2 2h0v1c-1-1-2-1-2-3-1 0-1 0-1-1s0-1-1-1c0 1 0 1-1 2h-1-1 1c1 0 1 1 1 1l1 1-1 1h-2-3 0c-1 0-1 1-2 1h0v-2h0c-1-1-1-1-1-2 1-1 1-1 2-1l1 1h0c1-1 1-1 1-2 0 0 1-1 2-1l1-1z" class="k"></path><path d="M300 191c1 4 1 9 2 14 1 8 3 16 3 24l-1-1c-1-11-4-21-5-33 2-1 1-2 1-4z" class="j"></path><path d="M256 470c-1 0-1 0-1-1h0-3-2c-1 2 0 2-1 3h-2v-1-1-2h-1c-1 0 0 0-1-1l-1 1h1c0 2 0 2-1 2h-1l-1-1 3-3-1-2v-1c2 0 2 2 3 2s1 0 1-1l-1-1c0-1 0-1 1-2h1l1 1c0-1 0-1-1-1h0c0-1 1-1 1-2l1 1h0-1v1h1c0 1 1 1 1 2h-2l-2-1v1c1 0 1 1 1 2l-1 1 1 1c0-1 1-1 2-1v2c0 1 0 1 1 0 1 1 2 1 3 0l1 2h0z" class="k"></path><path d="M342 375h3l-1 14c0 2 1 5 0 7h0v-3c0 1 0 3-1 4v1c0 1 0 1-1 2 0-3 1-5 1-8 0-5-1-11-1-17z" class="c"></path><path d="M437 343l1-3v4h-1v2 1h0c0 1 0 1-1 2v4c1-1 1-2 2-3h1 0c0-2 0-3 1-5h0 0l-1-1h-1 2v1h0v1 3h0v3c-1 1-2 1-2 3l-1 1h-1v1h1 0c0-1 1-2 1-2h1v1c1 0 1 0 2-1 1 0 2 1 3 2v1h-1c-1 0-2-1-4-1 0 0-1 1-1 2h-3v-9l2-7z" class="o"></path><path d="M175 344h1c0-1 0-1 1-2l1 1v1s1 0 2 1v1c1 0 1 1 1 1 0 1 1 2 2 2h-2v3h0v1h-1v1l-1-1v-1l-1 2h-1c0-1 0-1-1-2h-1v-1h2v-2h0l-1-1-1-1h-1l1-2v-1h0z" class="R"></path><path d="M180 346c1 0 1 1 1 1 0 1 1 2 2 2h-2-1l-1 1h0l-1-1v-1c1-1 2-1 2-2z" class="q"></path><path d="M162 246l1 1c1-2 1-3 3-4l2 2c1 0 2 0 3 1v1c0 1-2 2-3 2 0 0-1 0-2-1 0 0-1 0-1-1s1-1 1-2h0v-1l-2 2v1 1l1 1-1 1v-1h0-1 0v1h1 0l2 1 1 1s0 1-1 1c0 1 0 1-1 1v1-2h0l-1 1 1 1h-1 0l-1 1c-1 0-1 0-2-1v-1-1h2 0 1c-1-1-1-1-1-2h0c-1-1-2-1-3-1v-1c1-1 1-1 2 0h0l1-1c-1-1-1-1-1-2z" class="o"></path><path d="M167 246h1v1l-1 1c-1 0-1 0-1-1l1-1z" class="q"></path><path d="M492 180c1-2 2-6 4-8l1 19c-2-2-4-5-6-7 0-1 1-3 1-4z" class="i"></path><path d="M253 515l4 2c1 1 2 1 4 1h1 0c-1 0-1 1-2 1v2c3 10 2 20 2 30l-1 2v-12l-1-9c0-4 0-8-2-12l-5-5z" class="r"></path><path d="M146 199v5h1v-4h0v18l-1 15c0 2 0 5-1 7v-3h-1c0-2 1-4 1-6v-13c0-6 0-13 1-19z" class="s"></path><path d="M398 508v-7l1-1v17c1 6 2 11 1 16 0 2 0 4-1 5v3h0c-1-1-1-3-1-4l1-6-1-23z" class="r"></path><path d="M309 270c1 1 2 1 3 3l-1 1h0 1c0-1 0-1 1-1h0c0 2 1 4 1 5h0c-1 1-1 2-2 2-1 1-1 1 0 2h1v-1h1 0v1 1l-2 1-1 1v-1c-2-2-3-2-5-2l1-1c1 0 1 0 2-1v-2-1c-1 0-1-1-1-1v-1-1c1-1 1-1 0-3l1-1z" class="e"></path><path d="M304 269c1-1 2-1 3-1l1 1 1 1-1 1c1 2 1 2 0 3v1 1s0 1 1 1v1 2c-1 1-1 1-2 1l-2-1v-1-4c-1-1-2-1-4-1 0-1 1-1 1-1l1-2v-1h1v-1z" class="q"></path><path d="M302 273l1-2v-1h1c0 1 1 1 1 2v2h0v1c-1-1-2-1-4-1 0-1 1-1 1-1z" class="k"></path><path d="M305 274c1 0 1-1 2-1 0-1 0-2 1-2h0v2 1 1 1s0 1 1 1v1 2c-1 1-1 1-2 1l-2-1v-1-4-1z" class="R"></path><path d="M234 458h1v-3c1 1 1 1 2 1h0c0-1 0-2 1-2v2l1-1c0 1-1 1-1 2s0 1 1 2h0l1 1h-1 0-1c-1 1-1 0-1 1-1 0-1 1-2 1v1l1-1c1 0 2 1 2 1v1c0 1-1 1-1 1h-1l-1 1v2l-1 1h-1c-1 0-1-1-1-2v-1c1 0 1 0 2-1h0v-1h-1l-1 1s-1 0-2 1v-2l1-1c0-1 0-1 1-2 0 0 0-1 1-2h1v-1z" class="w"></path><path d="M146 170l3-3-1 10v6l-1 17h0v4h-1v-5c-1-1 0-4 0-6v-17-6z" class="f"></path><path d="M146 170l3-3-1 10v6c-2 2-1 4-1 6l-1-13h0v-6z" class="c"></path><path d="M182 347v-1h1c1 1 1 0 2 0s1 1 1 1h2 0l1 2v1h2c0 1-1 1-1 2l1 1-2 1 1 1 1 1h-1v1l-2 1v-1c-1 0-1 0-2-1h0l-1-1-1-1v-2h-1-1l2-2-1-1c-1 0-2-1-2-2h1z" class="e"></path><path d="M182 347h2v2h1l2 1h-1-2l-1-1c-1 0-2-1-2-2h1z" class="l"></path><path d="M185 349h2l1 1v1h0l1-1h2c0 1-1 1-1 2l1 1-2 1h-1v-2h0-2-2-1-1l2-2h2 1l-2-1z" class="g"></path><path d="M186 352h2 0v2h1l1 1 1 1h-1v1l-2 1v-1c-1 0-1 0-2-1h0l-1-1-1-1v-2h2z" class="l"></path><path d="M184 352h2 1v1c0 1-1 1-1 1l-1 1-1-1v-2zm2 4h0c1 0 1-1 1-1h3v1 1l-2 1v-1c-1 0-1 0-2-1h0z" class="k"></path><path d="M342 356c1 0 2 0 3 1v11 7h-3c-1-3-1-7-1-10s0-6 1-9z" class="G"></path><path d="M439 328c1-3 1-5 2-7v5 9c-1 2-2 4-3 5l-1 3-2 7-1-1c1-2 1-7 0-9l1-1 2-7 2-4z" class="S"></path><path d="M439 328s1 1 1 2c-1 2-1 3-1 5-1-1-1-2-2-3l2-4z" class="U"></path><path d="M439 328c1-3 1-5 2-7v5 4h-1c0-1-1-2-1-2z" class="Q"></path><path d="M437 332c1 1 1 2 2 3l-3 6 1 2-2 7-1-1c1-2 1-7 0-9l1-1 2-7z" class="J"></path><path d="M304 249c1 3 1 12 3 15h3s1 0 1 1v1l-2 2s-1 0-1 1l-1-1c-1 0-2 0-3 1v1h-1v1l-1 2-1-5v-2l-2-15c1-1 1-1 3-1 0 1 0 1-1 2h0v1s1 1 1 2h0l1 1-1 1h1c0 1 0 1-1 2v2h2c0 1 0 1-1 2h-1 0v1c-1 1-1 1 0 1h0l2-1c2 0 0 1 2 3l1-1h0 2v-1h-2c-1-1-1-1-1-2v-1-1c-1 0-1 0-1-1v-1c-1-1 0-1 0-2l-1-3v-3h0v-2h0z" class="w"></path><path d="M301 266v1c1-1 2-2 3-2h1c-1 1-2 1-3 2l1 2h1v1h-1v1l-1 2-1-5v-2z" class="R"></path><path d="M342 400c1-1 1-1 1-2v-1c1-1 1-3 1-4v3h0c1 3 1 5 1 8 1 6 0 15 4 20h4c0 1 0 3-1 4v-2c-1-2-2-1-4-2 0 0 0-1-1-2 0-1-1-3-1-4v-1c0-1-1-1-1-2v-1l-1-1c-1 0-2 2-3 2 0 2 2 5 3 7v2l-4-11c1-4 2-8 2-13z" class="f"></path><path d="M180 292h-1 0v-1c-1 0-1-1-3 0v-1c-1 0-1 0-1-1h-1-1s0-1-1-1h0 0-1l-1-1c1-1 1-1 1-2 1 0 1-1 1-2h2c0-1 1-1 1-2l-1 1h0l-1 1c-1 0-2 0-3-1v-2h-2v-1l1-2 2 2h-1-1 0c1 0 0 0 1 1h0c1-1 1-1 2-1 1-1 2-1 3-1h1v-1h-1l-1 1c-2 0-2 0-4-1-1-1-1-1-1-2s1-1 1-2c1 0 1 1 1 1v2l1-1h3l-1 1h-1v1l3-1v1 2c0 1 1 0 1 1-2 1-2 2-2 3 2 0 2-2 4 0h0c-1 1-1 2-1 2v2 1h1v1h-1l1 1v1c1 0 1 0 2 1h-1z" class="n"></path><path d="M287 209h0v2l2 2v2c2 0 2 0 4 1v4 4h0c0 2 0 3-1 4 0 0-1 0-1 1-1-2-1-6-3-7-1 0-1 0-2 1-1 0-1-7-1-9 0-1-1-2 0-3 0-1 1-1 1-1h1v-1z" class="J"></path><path d="M291 220h2v4h0s-1 0-1-1c-1 0 0 0-1-1 0 0-1 0-1-1l1-1z" class="u"></path><path d="M289 215c2 0 2 0 4 1v4h-2c0-1-1-2-1-3s0-2-1-2z" class="S"></path><path d="M289 213v2c1 0 1 1 1 2l-2 1c0 1 1 1 1 3l-1 1-1-2c1-1 0-2 0-3 1-1 1-1 2-1v-1h0-4v-1-1h4z" class="Z"></path><path d="M442 318h1c0-2 0-3 1-4h1c1 1 1 2 2 2 0 1 0 1 1 2 0-1 0-1 1-2h0l1 32h-1c-1 0-1-1-2-2-1-4 0-10 0-15 1-3-2-9-4-12l-1-1h0z" class="V"></path><path d="M455 283c0-1 1-1 2-2l-1 1c-1 5-4 11-5 17-1 4-1 9-1 14 0 1 0 2-1 3h0c-1 1-1 1-1 2-1-1-1-1-1-2-1 0-1-1-2-2h-1c-1 1-1 2-1 4h-1c0-3 1-5 2-7l11-28z" class="N"></path><path d="M224 438l1 1v2h1 0c1 2 2 5 3 5l1 1h0v1c1 1 2 1 4 1l1 1v1l-2 2h1v2 1c-1-1-1-1-2-1s-1 2-2 3l1 1h-1l-1-1s0-1 1-1c0-1 0-2 1-2 1-1 1-1 2 0h0v-2h-2l-1 2h0-1c-1-1-1-1-2-1h-1 0c-1 0-1 0-2-1v-1l-1-1c0-1 1-1 1-2v-1h-1c-1-1-1 0-1-1-1-1-1 0-2-1v-2h1v-1c-1-1-1-1-1-2l-1-2c1-1 1-1 2-1s1 1 2 1v1 2h-1 1 1 0 1s1 0 1 1h0c0 1 0 2 1 2v-1l-1-1c0-1 0-1-1-2h-1c0-1 0-1 1-2h-1v-1z" class="w"></path><path d="M223 448v-1-2c1-1 1-1 2 0 0 1 0 2-1 2v1h-1z" class="R"></path><path d="M225 445h1v2c1 0 2 1 3 2 0 1-1 1-1 2h0-1v2h-1 0c-1 0-1 0-1-1s-1-1-1-1v-2-1-1c1 0 1-1 1-2z" class="o"></path><path d="M271 485l3 10 11 32c-2-2-3-4-5-6 0-1-1-1-2-2v-2c1 0 1 1 1 1 0 1 0 1 1 1 0 1 0 1 1 2h0v1c1 0 1 1 2 1 0-2-1-3-2-5h0l-1-1v-3h0s0-1-1-1c0-1-1-3-1-5h0c-1 0-1-1-1-1v-2c-1-1-1-1-1-2h0c0-1 0-1-1-2v-1c0-1 0-1-1-2v-1l-1-1c-1 0-1-1-2-1-1 1-1 1-2 1h0c0 1 0 1-1 2v1c-1 0 0 2 0 3-1 0-1 1-1 1l-1 1-5 4c-1 1-2 0-3 0h0l1-1h1v-1c1 0 3-2 3-2v-1c-1 0-1 0-2-1 1 0 1-1 2-1h0l2 1c0-2-1-3 0-4l1-1v-1l1 1h1v-1l-2-1v-2l1-1v-1-1c0-1 0-1 1-2v3h1v-1l1-2c0-1 0-2 1-3z" class="R"></path><path d="M263 501l1 1v1c1-1 1-1 1-2s0-2 1-3v-1l1 1v2 2c-1 1-1 2-3 2v-1h-1c-1 0-1 0-2-1 1 0 1-1 2-1h0z" class="n"></path><path d="M271 485l3 10h-1c-1 0-1 0-1-1h-1c-1 1-1 1-2 1 0 1-1 0-2-1 0-1 0-2 1-3h1v-1l1-2c0-1 0-2 1-3z" class="l"></path><path d="M262 466h2l5 15c1 1 2 3 2 4-1 1-1 2-1 3l-1 2v1h-1v-3-5c-2 0-2 0-3-1 0-1 0-1 1-2 0-2-2-3-2-4s0-1-1-2h0v1c-1 0-1 0-1 1h0-1-1l-1-1c0 1 0 1 1 1 0 1 0 0 1 1l1 1-1 1c0 1 1 1 1 1h2 1l-1 1v1h0v1h-1 0-2 0c-1 0-2-1-3-2h0v-2-2h-2c-1-1-1-2-1-2l1-1h2 0c-1-1-2 0-3-1h0 1 1v-1h-1l1-1v1h2 1l2 2c0-1-1-1-1-2h0c0-1 0-1 1-2h0v-1s0-1-1-1h-3c-1 1-1 2-1 3l-1-1h0 0l1-1c0-2 2-1 3-2l2-1z" class="p"></path><path d="M256 475h3v1c-2 1-2 0-3 0v-1zm6-9h2l5 15c-1 1-1 1-3 1v-1l1-1h0c0-2-2-2-2-4-1-1-1-3-1-4-1-1-1-2-1-3-1-1-1-2-1-2v-1z" class="g"></path><path d="M176 260h1l1 1c-1 0-1 1-1 1v1h1l1-1s1 0 1 1v1c1 0 2 0 3-1v2c1 0 1 0 2-1h2 0l1 1-1 1s-1 0-2-1l-1 1-2 2v1h0l-1 2c-1 2-1 2-1 4v1h2v2l-1 1-2-1v1 1c1 1 1 3 1 3 0 1 0 1-1 1v1h1v2c1 2 1 1 1 3-1-1-2-1-3-1h1v-1h-1v-1-2s0-1 1-2h0c-2-2-2 0-4 0 0-1 0-2 2-3 0-1-1 0-1-1v-2-1l-3 1v-1h1l1-1v-1c-1 0-1-1-1-2h0v-1l-1 1h0c1 1 1 1 0 2h-1-1c0-1 1-1 1-2v-1c-1-1-1-2-1-3l1-1h0l1 1 1 1v-1h2l1-1v-1c-1 0-2-1-2-1-1-1-1-1-1-2l2-2h0v-1z" class="k"></path><path d="M178 271c-1 1-1 1-3 1l1-2 1-1 1 1v1z" class="R"></path><path d="M179 279c-1 0-1-1-1-1l1-1-1-1v-2l2 1v1h2v2l-1 1-2-1v1z" class="g"></path><path d="M180 264c1 0 2 0 3-1v2c1 0 1 0 2-1h2 0l1 1-1 1s-1 0-2-1l-1 1-2 2v1h0l-1-1h0l-1 1v1 1l-1 1c-1 0-1 0-1-1v-1h1v-1c1-1 1-2 2-3h-1l-1-1c1 0 1 0 1-1z" class="e"></path><path d="M159 185c1 1 2 3 2 4 0 2 1 4 1 7 1 1 1 3 1 4s0 0 1 1c0 1 1 2 1 3l-1 1 1 1c0 2 0 4 1 5l-1 1h-1c-1 1-1 2-1 3h0 2 1v1h-3-1-1c-1 0-1-1-2-2 0-1 1-1 2-2h-1c0-1 0-1-1-2-1 0-1 0-1-1v-1c1-1 1-1 2-1 0-1-1-1-1-2v-1h1c0-1 0-1-1-1v1c-1 0-2 0-2-1v-1-1-1c0-1 0-1 1-2h-1-1c0 1 1 1 1 2l-1 1-2-1 1-3c1-1 1-1 3-1v-1h-1l-1-1c0-2-1-2 0-3h1v-1h-2v-1c1-1 1-1 1-2h2 0-1l2-2z" class="n"></path><path d="M161 212l1 1v1l-1 1h-1v-1l1-2z" class="k"></path><path d="M158 200h1l3 3h-1l-2-1c0 1-1 1-1 1h-1l1-1v-2z" class="q"></path><path d="M378 560v1c0 1 1 2 2 2v1c1 1 1 2 1 3 1 1 2 1 1 2h-2c-1 1-1 1-1 2l1-1h2c1 0 1 0 1 1 1 1 1 1 1 2h0 2v1c0 1 0 1 1 2v2 1c1 0 1 0 1 1v1s1 1 2 1h0l1 1h0c0 1-1 2-1 2-1-1-1-1-1-2h-1v1l-1 1-1-1v-2l-1-1c0 1 0 1-1 1h0c1 0 1 0 1 1v2h0-1l-1-1h-1l-1 1c-1 0-1-1-1-1-1 0-1-1-1-1h0v-2c-1 0-1 0-2-1h3c0-1 0-1-1-2h0c-1 0-1 0-2 1 0-1-1-1-1-1 0-1 0-2-1-2v-2h0c-1 0-1-1-2-2l1-1-1-1v-2h0v-1h-1v-2c1-1 1-1 2-1l1 1h0 1l-1 1-1 1h1s1 1 1 2v1h0 1v-1-1l1-1v1h0v2-1l1-1c1 0 1 1 2 1v-1-2c-1 0-2-1-2-1v-1-1h0c-1 0-1 0-2-1 1 0 1-1 1-2h0z" class="o"></path><path d="M257 445c1 3 3 7 3 10l1 3 3 8h-2l-2 1c-1 1-3 0-3 2l-1 1h0l-1-2c-1 1-2 1-3 0-1 1-1 1-1 0v-2c-1 0-2 0-2 1l-1-1 1-1c0-1 0-2-1-2v-1l2 1h2c0-1-1-1-1-2h-1v-1h1 0l2-2c-1 0-2-1-3-1s-1 0-2-1v-2h3 1c1 0 2-1 2-2h1 0 2v-2-2-3h0z" class="p"></path><path d="M260 455l1 3 3 8h-2l-2 1c-1 1-3 0-3 2l-1 1h0l-1-2c-1 1-2 1-3 0-1 1-1 1-1 0v-2c-1 0-2 0-2 1l-1-1 1-1c0-1 0-2-1-2v-1l2 1h2l1-1h1c1 0 1-1 1-1 1-1 1-2 2-3h1v1s-1 0-1 1 0 1-1 2v2c0 1 1 2 2 3v-1c0-1 1-1 2-2v-3c0-1-1-1-1-2l1-1v-3z" class="g"></path><path d="M251 466h2c0 1 1 1 1 2h-1 0-1c-1 1-1 1-1 0v-2zm10-8l3 8h-2l-2 1h-1 0c0-1 1-1 1-2 1 0 0-3 0-4s1-2 1-3z" class="v"></path><path d="M365 539c1 0 1-1 3 0 0 3 0 4 2 5 1 1 1 0 1 1h1 1l1-1v-2l2 2c0 1-1 1 0 1h1l1 1h-1v4c1 1 1 1 1 2s-1 1-2 1v-2-1l-2 1h1c0 1 0 1-1 2v1h1c0 1 0 1 1 1-1 1-1 2-2 2v1c1 1 1 1 2 1s1 1 2 1h0c0 1 0 2-1 2 1 1 1 1 2 1h0v1 1s1 1 2 1v2 1c-1 0-1-1-2-1h0c0-1-1-1-1-1v-1c-1 0-1-1-1-1v-2h0c0-1 0-1-1-1 0-1-1-1-2-2v-1h-1v2h-1c1 0 1 1 2 1v2h-3c-1-1 0-1 0-2v-1c0-1 0-1-1-2v-1l-2-2c-1-1-2-2-2-3v-1-1c-1-1-1-2-2-3v-1c-1 0-2 1-3 2l4-10z" class="R"></path><path d="M364 548h2l1-1h0 0c1 0 1 1 2 1h0 0l1 1c1 0 2 1 2 1v1l1 1h1 0v1c-1 0-2-1-2-1-1-1 0-1-2-1 0 2 1 2 0 3 0 2 1 3 0 4l-2-2c-1-1-2-2-2-3v-1-1c-1-1-1-2-2-3z" class="k"></path><path d="M365 539c1 0 1-1 3 0 0 3 0 4 2 5 1 1 1 0 1 1h1v1 1c-1 0-1 0-1-1l-1-1h-1v3h0c-1 0-1-1-2-1h0 0l-1 1h-2v-1c-1 0-2 1-3 2l4-10z" class="l"></path><path d="M393 459c1-4 2-7 4-10 2-8 5-15 6-22 2 6-1 11-1 16-1 10 2 21 0 31l-3-9c-1 1-1 3-1 4v1c-1 1 0 4-1 5h-1l-1-8c0-3 0-6-2-8z" class="S"></path><path d="M395 467c1 1 2 1 2 3v-1-2-2h0v-3l1-1c0-2-1-3 0-5h1c0 2-1 5 0 7v2c-1 1-1 3-1 4v1c-1 1 0 4-1 5h-1l-1-8z" class="j"></path><path d="M298 180c-1-4-3-7-3-11-1-2 0-4 0-7v-7c0-5 1-10 1-14 0-3 0-11 2-13l1 1s-1 1 0 1v1 5c0 3-1 7 0 10 0 3 1 6 1 8 2 7 2 13 1 19 0 0-1 1-1 2v2c0 1-1 1-1 1v4 1 1 1c-1-1 0-5-1-6v1z" class="u"></path><path d="M521 124c-1 6-1 13-3 20-1 2-4 5-4 7-1 4-1 10-3 12-1 1-1 1-2 1-1-1-1-1-2-3 0-3 0-7-1-10l4-9c3-6 6-13 11-18z" class="f"></path><path d="M188 265h2v3c1 1 1 2 2 2h0c2 0 2 0 3-1l3 7c-1 0-2 0-3-1v2c1 1 2 1 2 3-1 0-1 0-1 1h-1-1c1 1 2 2 2 3l1 1s-1 0-1 1c-1-1-2-1-2-1h-1c-2-1-1-2-3-1v1c1 1 1 1 2 1l1-1 1 1c0 1 0 1-1 1v1h-1v-2h-2-2v-1c-1-1 0-3 0-4h0-2v-1h-2-1-3-1v-1-1l2 1 1-1v-2h-2v-1c0-2 0-2 1-4l1-2h0v-1l2-2 1-1c1 1 2 1 2 1l1-1z" class="s"></path><path d="M192 270c2 0 2 0 3-1l3 7c-1 0-2 0-3-1v2c1 1 2 1 2 3-1 0-1 0-1 1h-1-1v-1-2c1-1 0-1 0-2l-2-2h2v-1c-1-1-2-1-3-2h1 2 0l-2-1z" class="S"></path><path d="M187 277h2s0-1 1-1c1-1 1 0 2 0v1h-2c1 1 1 1 1 2h2v1l-1 1c1 0 2-1 2 1l1 1 1 1 1 1s-1 0-1 1c-1-1-2-1-2-1h-1c-2-1-1-2-3-1v1c1 1 1 1 2 1l1-1 1 1c0 1 0 1-1 1v1h-1v-2h-2-2v-1c-1-1 0-3 0-4v-1l-1-1v1h-1l1-1v-2z" class="x"></path><path d="M182 269h0v-1l2-2 1 1h2 1l-1 1c0 1 1 1 1 1l3 3c0 1 0 1-1 1 0 1 0 1-1 2h-2v2 2l-1 1h1v-1l1 1v1h0-2v-1h-2-1-3-1v-1-1l2 1 1-1v-2h-2v-1c0-2 0-2 1-4l1-2z" class="m"></path><path d="M182 269h0v-1l2-2 1 1 1 1v1c-1 0-1 0-2-1v1c0 1 0 1-1 2v2l-1 1v-2h-1v-1l1-2z" class="v"></path><path d="M184 275c1 0 1-1 2-1l1-1s2 1 3 0h0 0c0 1 0 1-1 2h-2v2 2c-1 0-2-1-3-2 0 0-1 0-1-1h0l1-1z" class="y"></path><path d="M180 275c0-2 0-2 1-4v1h1v2l1-1h1c0 1-1 1 0 2l-1 1h0c0 1 1 1 1 1 1 1 2 2 3 2l-1 1h1v-1l1 1v1h0-2v-1h-2-1-3-1v-1-1l2 1 1-1v-2h-2v-1z" class="e"></path><defs><linearGradient id="A" x1="297.946" y1="244.568" x2="288.061" y2="245.329" xlink:href="#B"><stop offset="0" stop-color="#b6100c"></stop><stop offset="1" stop-color="#de1615"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M286 223c1-1 1-1 2-1 2 1 2 5 3 7 0 0-1 1-1 2 1 1 1 1 1 3h-1c2 2 3 2 5 3 2 5 3 9 4 14l2 15v2h-2c0-1 0-1-1-1-1 1-2 1-4 1l-8-45z"></path><path d="M165 206c0 2 1 3 1 4l1 1h4c1 1 2 2 2 3h3l2 6 4 11c-1 0-1-1-2-1h0c-1 0-2 0-2 1-1 0-1 1-2 1s-1-1-1-1l-1-1c0 1-1 1-1 1h-1c0-1 1-1 2-2h0c-1 0-2 0-3-1l-1-1 1-1c-1 0-2-1-2-2-1 1-2 1-2 2h-2l-1 1-1-1c-1 1-1 1-1 2v1c1 0 1-1 2 0h-1v1c-1 0-1 0-2-1v-2c-1 0-1 0-2-1v-1c-1-1 0-1-1-1-1 1-1 1-2 1l-1-1v-1-1c0 1 0 2-1 3v-1c0-2 0-3 1-4h1l1 1-1 1c1 0 1 0 2-1h2l-1-1c-1 0-1-1-2-1l1-1 2-2 1 1v1c0-1 0-1 1-2 1 0 1 1 2 1v-1c1 1 2 1 2 1l1-1h-1v-1h-1-2 0c0-1 0-2 1-3h1l1-1c-1-1-1-3-1-5z" class="R"></path><path d="M165 222c1 0 1 0 2 1l-1 1c-1 0-1 0-2-1h-1l2-1z" class="l"></path><path d="M165 206c0 2 1 3 1 4l1 1c1 0 1 1 1 2 1 1 1 1 1 2h1 1 1v1c-1 0-1 0-1 1 0 0 0 1 1 1v1l-2 1s-1 0-1 1h-1l-1 1h0v1c-1-1-1-1-2-1l-2 1c0-1 0-1-1-2v-1h-2c1-1 4 0 5-2h-1c1-1 2-1 3-1v-1h-1v-1h-1-2 0c0-1 0-2 1-3h1l1-1c-1-1-1-3-1-5z" class="q"></path><path d="M162 220h2 2 0l-1 2-2 1c0-1 0-1-1-2v-1z" class="w"></path><path d="M167 211h4c1 1 2 2 2 3h3l2 6 4 11c-1 0-1-1-2-1h0c-1 0-2 0-2 1-1 0-1 1-2 1s-1-1-1-1l-1-1c0 1-1 1-1 1h-1c0-1 1-1 2-2h0c-1 0-2 0-3-1l-1-1 1-1c-1 0-2-1-2-2 0 0 1 0 1-2-1 1-1 0-2 0h-1l1-1h1c0-1 1-1 1-1l2-1v-1c-1 0-1-1-1-1 0-1 0-1 1-1v-1h-1-1-1c0-1 0-1-1-2 0-1 0-2-1-2z" class="p"></path><path d="M173 214h3l2 6-2 1c0-1-1-2-1-3s0-1-1-2v-1l-1-1z" class="G"></path><path d="M178 220l4 11c-1 0-1-1-2-1h0c-1-3-3-6-4-9l2-1z" class="T"></path><path d="M167 222l1-1h1c0-1 1-1 1-1 1 1 2 1 4 1h0v1l1 1h1v2h-1c0 1 1 1 1 1v1h-1v1l-1 1h0c-1 0-2 0-3-1l-1-1 1-1c-1 0-2-1-2-2 0 0 1 0 1-2-1 1-1 0-2 0h-1z" class="l"></path><path d="M170 227h1v-1l2 1v-1c0-1 0-1 1-1v-1c-2 0-2 0-3-1v-1h3l1 1h1v2h-1c0 1 1 1 1 1v1h-1v1l-1 1h0c-1 0-2 0-3-1l-1-1z" class="g"></path><path d="M181 292c0 1 0 1 1 2h0v1c1 1 2 1 3 2h0l-1 1v1 2h1v3 1h2l2-2c1 1 2 2 2 3-1 1-2 1-4 1v1c1 1 1 1 2 1 0 0-1 0-1 1h-1c0-1-1-1-2-1v1c-1 0-1 1-2 1h-1-1c0 2 3 3 1 5l-1 1v1l1 1v1l-3 2-1 1c1 0 1 1 2 1 0 1 1 0 1 0 0 1 1 2 1 3l-1 1v1h2v2c1 0 1-1 2 0-1 1-1 1-2 1h0-2-1v3 1h-2v1h2l2-1v1h0v3h0c1 2 1 3 3 4h0c-2 1-2 1-3 2v1h-1s0-1-1-1v-1c-1-1-2-1-2-1v-1l-1-1c-1 1-1 1-1 2h-1v-1c0-1 1-2 1-3l-1-1c-1 1-1 1-2 0-1 0-1-1-1-2l2 1c1-1 2-1 2-2h0 1c-1-1-1-2-1-2-1-1-1-1-1-2-1 0-1 0-1-1 1 0 1-1 1-1 0-1 0-2-1-2v-1h-1 1c0-1 0-2 1-2v-2h0v1h1v-1-1c-1 0-1-1-2-2h1c0-1 1-1 1-1h0 1c0-1 0-1 1-2 0 0-1-1 0-1-1-1 0-1 0-2v-2h-1v-3h1c1 0 1 0 1-1h1-1c0-1-1-1-2-1v-1c1-1 2-1 2-2l1-1h0c1 0 1-1 2-1-1-1-2-1-2-2-1 1-1 1-2 1v-1l1-1h1c0-1-1-1-2-1v-1c1-1 1-1 1-2h0c1-1 1-1 2-1 0 0 0-1-1-1v-1h1z" class="k"></path><path d="M180 337l2-1v1h0v3h0c1 2 1 3 3 4h0c-2 1-2 1-3 2v1h-1s0-1-1-1v-1c-1-1-2-1-2-1v-1-1h2 1c-1-1-1-1-2-1h0v-1c0-1 1-2 1-3zm0-13c0 1 1 0 1 0 0 1 1 2 1 3l-1 1v1h2v2c1 0 1-1 2 0-1 1-1 1-2 1h0-2-1v3 1h-2c0-1-1-1-1-2h0l1-1h0c0-1 1-2 1-3-1-1-2-1-2-2h0 3v-1c-1-1-2 0-2-2 0 0 1 0 2-1z" class="p"></path><path d="M179 330v1h1v2 1h0-1c0-1-1-1-1-1h0c0-1 1-2 1-3z" class="l"></path><path d="M224 438l-1-1 1-1h-2v-1c0-1-1-1-2-2 0-1 0-1-1-2v-1c-1 1-1 1-1 2h-2c-1-1-2-2-2-3 1-1 1-1 2-1h1 0c0-1 1-2 2-3h-1-1v1l-1-1h-1l1 1v1h-1c-1-1-1-1-2-1v-2c-1 0-2-1-2-1v-2h2v-3h-1c-1 0-2-1-2-2v-1h-1l1 1v2c-1 0-2 0-2-1v-1l1-1h-1v-2c-1 0-2-1-3-1h0-1l1-1h3l3 3c1 0 1 0 1-1h-2v-1-1c0-1-1-1-1-1l-1-1h1 0c1 0 1 1 2 1v-1c1 0 1-1 2-1l-1-1h-1c-1 1-1 1-2 1l-1-2 1-1v2h1 1l1-1 2 2c0 1-1 1-1 1l-1 1h0l2 2-1 1v1h2v1l-1 2v1l2-1h0v2l-1 1s0 1-1 1v1l1 1h1c0 1 0 1 1 2h0c1-1 1-1 2-1h2v2h1c1 0 2-1 2-2h1c0 2 0 2 1 3 1 0 1 0 2 1l-1 2v1c-1 1-1 1 0 2h0c0 1 1 1 1 2 0 0 0 1 1 1 1 1 1 2 2 2h0l1-1h1c1-1 2 0 2 0 1 1 0 3 1 3l1 1h0l1 1v-1h1v1h1l1-1-1-1h1v2l1 1h0v2 1l1 1c1 0 1 0 1-1h3c1-1 1-1 1-2 1 2 1 2 2 3 0-1 0 0 1-1h0c0 1 1 2 2 3 0 1 1 2 1 3-1 1-2 1-3 2h-3v2c1 1 1 1 2 1s2 1 3 1h-2l-1-1c-1 0-1 1-2 0h-1 0-2-1 0-2-1l-1 1 1 1-1 1-1-1h0c-1-1-1-1-1-2s1-1 1-2l-1 1v-2c-1 0-1 1-1 2h0c-1 0-1 0-2-1v3h-1c-1 0-1 0-2-1v1l-1 1-1-1c1-1 1-3 2-3s1 0 2 1v-1-2h-1l2-2v-1l-1-1c-2 0-3 0-4-1v-1h0l-1-1c-1 0-2-3-3-5h0-1v-2l-1-1z" class="o"></path><path d="M242 446l-2-2v-1h2v2 1z" class="k"></path><path d="M221 426h1c1 0 2-1 2-2h1c0 2 0 2 1 3 1 0 1 0 2 1l-1 2h0-1-1c0-2-1-1-2-2v-1c-1 0-1 0-2-1z" class="n"></path><path d="M234 449l-1-1v-3-2h1 0l1 1c1 0 1 0 2 1v1l-1 1v2h0l-1 1-1-1z" class="R"></path><path d="M147 134l1 2c0 1 1 3 2 4 4 10 7 21 11 31l8 22c2 7 5 14 7 21h-3c0-1-1-2-2-3h-4l-1-1c0-1-1-2-1-4l-1-1 1-1c0-1-1-2-1-3-1-1-1 0-1-1s0-3-1-4c0-3-1-5-1-7 0-1-1-3-2-4-2-4-2-7-3-12-2-4-3-8-5-13h-1c-1 0-2-1-4-1l-1-1 2-24z" class="B"></path><path d="M161 189s1 1 1 2 1 1 1 2h0l1 1v2c0 1 1 1 1 2-1 2-1 3 0 4s1 2 1 3v1 1l2 2h0c1 1 2 1 3 2h0-4l-1-1c0-1-1-2-1-4l-1-1 1-1c0-1-1-2-1-3-1-1-1 0-1-1s0-3-1-4c0-3-1-5-1-7z" class="G"></path><path d="M245 416l2 1 2 4 1 5 5 15c1 1 2 3 2 4h0v3 2 2h-2 0-1c0 1-1 2-2 2h-1c1-1 2-1 3-2 0-1-1-2-1-3-1-1-2-2-2-3h0c-1 1-1 0-1 1-1-1-1-1-2-3 0 1 0 1-1 2h-3c0 1 0 1-1 1l-1-1v-1-2h0l-1-1v-2h-1l1 1-1 1h-1v-1h-1v1l-1-1h0l-1-1c-1 0 0-2-1-3 0 0-1-1-2 0h-1l-1 1h0c-1 0-1-1-2-2-1 0-1-1-1-1 0-1-1-1-1-2h0c-1-1-1-1 0-2v-1l1-2c-1-1-1-1-2-1 1 0 2-1 3-1v-3c0-1 1-1 1-1 1 1 1 1 1 2h1s1 0 1-1h0c1 0 1 0 2-1 1 1 1 2 2 3v-1c1 0 2 0 3-1v-3h1c0 2-1 2 0 3h1v-1-1c1-2 2-3 3-5z" class="q"></path><path d="M242 445c2 0 4 0 5-1v-1l1 1-1 2h-3c0 1 0 1-1 1l-1-1v-1z" class="l"></path><path d="M243 423l1 1v1c-1 1 0 3-1 5h-1v-3h-1c0-1 1-3 2-4z" class="g"></path><path d="M249 421l1 5c0-1-1-1-2-2-1 0-3-1-4-1v1h0l-1-1v-2l1 1h2 1c1 0 1 0 2-1z" class="l"></path><path d="M241 420c0 2-1 2 0 3h1v-1-1h1v2c-1 1-2 3-2 4s-1 1-1 2c-1-1 0-4 0-6v-3h1z" class="j"></path><path d="M231 438v-1-1-1-2c1 0 1 0 2-1 0 1 1 1 1 1 1 0 1 0 2 1 0 1 0 2-1 3 0 0-1-1-2 0h-1l-1 1h0z" class="R"></path><path d="M245 416l2 1 2 4c-1 1-1 1-2 1h-1-2l-1-1h-1c1-2 2-3 3-5z" class="q"></path><path d="M236 440h1v-4h3l1-1 1 1v1c0 2-1 2-2 2v1l1 1-1 1h-1v-1h-1v1l-1-1h0l-1-1z" class="R"></path><path d="M229 423c0-1 1-1 1-1 1 1 1 1 1 2h1s1 0 1-1h0c1 0 1 0 2-1 1 1 1 2 2 3-1 1-2 1-2 2v2h0-1-1l-2 2-2-1h0-1c0 1-1 1-1 1v-1l1-2c-1-1-1-1-2-1 1 0 2-1 3-1v-3z" class="p"></path><path d="M226 427c1 0 2-1 3-1 0 1 1 3 0 4h0-1c0 1-1 1-1 1v-1l1-2c-1-1-1-1-2-1z" class="k"></path><path d="M164 229c-1-1-1 0-2 0v-1c0-1 0-1 1-2l1 1 1-1h2c0-1 1-1 2-2 0 1 1 2 2 2l-1 1 1 1c1 1 2 1 3 1h0c-1 1-2 1-2 2h1s1 0 1-1l1 1s0 1 1 1 1-1 2-1c0-1 1-1 2-1h0c1 0 1 1 2 1l6 16-2 1v2h-1 0-1l-1 1v2 1c0 1 1 1 1 1l-2 2h1c0 1 0 1 1 1 0 1-1 1-1 2v1 2c-1 1-2 1-3 1v-1c0-1-1-1-1-1l-1 1h-1v-1s0-1 1-1l-1-1h-1c-1 0-1-1-2-1 0-1 0-2-1-2v-1h-1-3l-1-1c-1-1 1-3 0-4v-2c1 0 3-1 3-2v-1c-1-1-2-1-3-1l-2-2c-2 1-2 2-3 4l-1-1-1-1h2v-1h-1-1v-3h0-1-1c0-1 1-2 1-3v-1l1-1c1 1 1 0 1 1v2 1h1v1 2h0c1-1 1-1 1-2l-1-1v-1l1-1h-1c1-1 0-1 1-2 0 0 0-1 1-1h0c0 1 0 1 1 1v1l-1 1h-1 0 1c1 1 1 1 1 2l-1 1h0 1v1l1-1 1-1h0c0-1-1-1-1-2v-1c1-1 2-1 2-1l1-1-1-1h0c-2 0-2 0-3-1v-1c1 0 1-1 3-1h-1-1l-1 1h-2l-1-2 1-1h1 1c1 0 1 0 0-1 0 1-1 1-2 1z" class="n"></path><path d="M167 232c1 0 2 0 3 1v1c-1 0-2-1-2-1l-1-1z" class="e"></path><path d="M178 249v1l1 2v2c1 0 1 1 2 1 0 0 1 1 1 2h1c0 1 0 1 1 1 0 1-1 1-1 2v1 2c-1 1-2 1-3 1v-1c0-1-1-1-1-1l-1 1h-1v-1s0-1 1-1l-1-1h-1c-1 0-1-1-2-1 0-1 0-2-1-2l1-1 1-1s1-1 2-1v-1h-2v1h-1 0v-2-1h1c1-1 2-1 2-1l1-1z" class="p"></path><path d="M174 256h3v2c-1 0-2 0-3 1 0-1 0-2-1-2l1-1z" class="R"></path><path d="M180 230c1 0 1 1 2 1l6 16-2 1v2h-1 0-1l-1 1v2 1c0 1 1 1 1 1l-2 2c0-1-1-2-1-2-1 0-1-1-2-1v-2l-1-2v-1c-1 0-2-1-3-1v-2-3c0-1 0-2-1-2h-1-4-1l1-1h2 0 1c0-1 0-1-1-1l1-1h1 1l-1 2 1-1v-2s-1 0-1-1h-1c-1 0-1-1-1-2 0 0 1 0 1-1 0 0 0-1-1-1l1-1h1s1 0 1-1l1 1s0 1 1 1 1-1 2-1c0-1 1-1 2-1h0z" class="g"></path><path d="M180 230h0v1c0 1 1 3 1 5 1 1 1 1 1 2l1 1v1l1 1-1 1h-2-1v-1l1-1v-1h-2v-1c-1 0-1-1-2-1h0c0-1 0-2 1-3h-1c-1 0-1 1-2 2v-1c0-2 0-2 1-3 1 0 1-1 2-1 0-1 1-1 2-1z" class="t"></path><path d="M180 230c1 0 1 1 2 1l6 16-2 1v2h-1 0-1l-1-1 1-2h0l-2 1v-1-1h0v-1h-1v-1h0c1 1 2 1 3 1h0c-1-1-2-2-3-2v-1h2l1-1-1-1v-1l-1-1c0-1 0-1-1-2 0-2-1-4-1-5v-1z" class="i"></path><path d="M521 88l4-3c11-6 20-15 24-27 1-2 2-5 2-7l3 6c3 7 5 14 7 21-2 0-2 0-4 1 0 1-1 1-2 1-1-1-4 0-6 1l-6 3-7 4-7 4c-2 0-3 1-4 2v-1l-3 1c-2-2-4-2-7-2-3 1-6 2-9 2l15-6z" class="W"></path><path d="M543 84c1-2 2-3 4-4 1 0 1 0 1-1h1 2l-2 1h0v1l-6 3z" class="K"></path><defs><linearGradient id="C" x1="532.614" y1="90.886" x2="516.727" y2="85.616" xlink:href="#B"><stop offset="0" stop-color="#980e0a"></stop><stop offset="1" stop-color="#57110e"></stop></linearGradient></defs><path fill="url(#C)" d="M521 88c3 2 6-4 10-4 1 0 1 0 2 1h4v1l-1 1h0v1l-7 4c-2 0-3 1-4 2v-1l-3 1c-2-2-4-2-7-2-3 1-6 2-9 2l15-6z"></path><path d="M533 85h4v1l-1 1h0v1l-7 4c-2 0-3 1-4 2v-1l-3 1c-2-2-4-2-7-2 3 0 6-1 8-1h1c1 1 2 0 3 0 2-1 7-3 8-4l-2-2h0z" class="M"></path><path d="M554 57c3 7 5 14 7 21-2 0-2 0-4 1 0 1-1 1-2 1-1-1-4 0-6 1v-1h2c0-1 0-1 1-1h1 1c1 0 1-1 2 0v-1c-3 0-6-1-9 0-1 0-2 0-3-1v-1c1-1 3-2 5-4v-1l2 1c0-5 0-9 3-13v1-3z" class="C"></path><path d="M544 76c1-1 3-2 5-4v-1l2 1c2 1 5 2 6 4l1 1c-2 1-8 0-10 0h-4v-1z" class="Q"></path><path d="M188 326h0c1 1 2 1 3 1h1l1 1c0 1 0 1 1 1h1c1 0 1-2 2-1 2 1 2 0 3 0 0 0 0 1 1 1l1-1v1h1 1v-1l1 3c1 1 1 2 1 3s0 2-1 3c1 1 1 1 1 2s0 3 1 4c1 0 1 1 2 1-1 1-1 1-2 1 0 1 0 3 1 5h0v6c-1-1-1-2-2-2l-1 2h-1-1v1l-1 1h-2-1l-1 1c0 1-1 1-2 1h-1 0c0-1 0-1 1-2l-1-1c-1 0-1-1-1-1l-2 1c0-1-1-1-1-1l-1-1-1-1 2-1-1-1c0-1 1-1 1-2h-2v-1l-1-2h0-2s0-1-1-1-1 1-2 0h-1c1-1 1-1 3-2h0c-2-1-2-2-3-4h0v-3h0v-1l-2 1h-2v-1h2v-1-3h1 2 0 2 2 3v-2-1c0-1-1-1-2-1v-2z" class="x"></path><path d="M190 329h2 1l-2 2-1-1v-1z" class="r"></path><path d="M190 347h3 0c1 0 0 0 1 1h2c0 1-1 1-1 2-2 0-3-2-5-2h0v-1z" class="m"></path><path d="M185 344h0c1 0 1-1 3-1 0 0 0 1 1 1s1 0 2 1h1v1h0c-1 0-1 0-2 1h-2 0-2s0-1-1-1-1 1-2 0h-1c1-1 1-1 3-2z" class="v"></path><path d="M197 328c2 1 2 0 3 0 0 0 0 1 1 1l1-1v1h1 1v-1l1 3c1 1 1 2 1 3l-2-1c0 1 0 2-1 3h0-1 0l-2-1h-1c-1 0-1 0-2-1l-1-1 1-1h1 0v-1h-2v-1l1-2z" class="j"></path><path d="M205 331c1 1 1 2 1 3l-2-1h-1v-1c1 0 1-1 2-1h0z" class="i"></path><path d="M204 333l2 1c0 1 0 2-1 3 1 1 1 1 1 2s0 3 1 4l-1 1-1-1c0 1-1 2-2 2h-2v-1h1c-1-1-2-2-2-3h1 0 1v-1h-3v1c-1-1-1-1-1-2h-2 0l1-1 2-2h0 1c0 1 1 1 1 2h1l1-1v-1c1-1 1-2 1-3z" class="s"></path><path d="M204 333l2 1c0 1 0 2-1 3 1 1 1 1 1 2s0 3 1 4l-1 1-1-1c-1-1-1-3-2-4-1 1-3 1-4 0 1 0 2-1 3-1l1-1v-1c1-1 1-2 1-3z" class="S"></path><path d="M188 347h2v1h0c2 0 3 2 5 2l2 2c0 1 0 1-1 2h0 1s1 0 2 1v-1c0-1 0-1 1-1 0 1 1 2 1 3-1 0-1 1-1 1v1h-1l-1 1c0 1-1 1-2 1h-1 0c0-1 0-1 1-2l-1-1c-1 0-1-1-1-1l-2 1c0-1-1-1-1-1l-1-1-1-1 2-1-1-1c0-1 1-1 1-2h-2v-1l-1-2z" class="y"></path><path d="M191 353l1-1c0 1 0 1 1 2 1 0 2 0 2 2v1h2 0l-1 3h-1 0c0-1 0-1 1-2l-1-1c-1 0-1-1-1-1l-2 1c0-1-1-1-1-1l-1-1-1-1 2-1z" class="p"></path><path d="M207 343c1 0 1 1 2 1-1 1-1 1-2 1 0 1 0 3 1 5h0v6c-1-1-1-2-2-2l-1 2h-1-1v1l-1 1h-2v-1s0-1 1-1c0-1-1-2-1-3l-1-1v-1c1 0 1 1 2 1s1 0 2 1l1-1v-1h-1 0v-1-1h-1-1c-1 1-1 1-2 1l-1 1h-1v-1c0-1 1-2 2-2l1-1 1-2h2c1 0 2-1 2-2l1 1 1-1z" class="r"></path><path d="M204 356v-4l2 1v1l-1 2h-1z" class="s"></path><path d="M207 343c1 0 1 1 2 1-1 1-1 1-2 1 0 1 0 3 1 5h0v6c-1-1-1-2-2-2v-1l1-1-1-2c-1 1-1 1-2 1h0v-4l-1-1v1 1h0c-1 0-2-1-3-1l1-2h2c1 0 2-1 2-2l1 1 1-1z" class="j"></path><path d="M190 332v-2l1 1c1 0 2 1 2 2 0 0 0 1-1 2h0c-1-1-1-1-2-1 0 0 0 1-1 2l1 1v2c-1 1-1 0-2 0v2h0c1 0 1 0 2-1 0 1 0 1 1 1h3v1h-3c-1 1-2 1-3 1-2 0-2 1-3 1h0 0c-2-1-2-2-3-4h0v-3h0v-1l-2 1h-2v-1h2v-1-3h1 2 0 2 2 3z" class="z"></path><path d="M187 332h3v1c-1 1-2 0-3 1h0-2c0-1 0-1-1-1h-1v-1h0 2 2z" class="m"></path><path d="M182 340h1l1 1c1 0 2 1 3 0h1c0 1 1 1 2 1h1c-1 1-2 1-3 1-2 0-2 1-3 1h0 0c-2-1-2-2-3-4z" class="t"></path><path d="M178 336h2v-1-3h1 2v1h1c1 0 1 0 1 1h2 1l-1 1h-2l-1 1s1 1 2 1c0 0 0 1 1 1v1l-1 1h-3-1 0v-3h0v-1l-2 1h-2v-1z" class="e"></path><path d="M187 339h-1c-2 0-2-1-3-2v-1h1s1 1 2 1c0 0 0 1 1 1v1z" class="m"></path><path d="M179 280h1 3 1 2v1h2 0c0 1-1 3 0 4v1h2 2v2h1v-1c1 0 1 0 1-1l-1-1-1 1c-1 0-1 0-2-1v-1c2-1 1 0 3 1h1s1 0 2 1c0-1 1-1 1-1l-1-1c0-1-1-2-2-3h1 1c0-1 0-1 1-1h0v2c0 1 1 2 1 3h0c1 1 1 3 2 3l1 3-1 1c1 2 1 4 1 6 0 1 1 3 1 4s0 4 1 6c-1 0-1 0-1 1h1c0 2-1 4 0 6v1h-1l-1-1v1l-2-1c-1 1-1 1-1 2h1l1 1c1 1 1 3 1 4v1c1 1 2 1 3 1h0c0 2 0 3 1 4h-1v1h-1-1v-1l-1 1c-1 0-1-1-1-1-1 0-1 1-3 0-1-1-1 1-2 1h-1c-1 0-1 0-1-1l-1-1h-1c-1 0-2 0-3-1h0v2c1 0 2 0 2 1v1 2h-3-2-2c1 0 1 0 2-1-1-1-1 0-2 0v-2h-2v-1l1-1c0-1-1-2-1-3 0 0-1 1-1 0-1 0-1-1-2-1l1-1 3-2v-1l-1-1v-1l1-1c2-2-1-3-1-5h1 1c1 0 1-1 2-1v-1c1 0 2 0 2 1h1c0-1 1-1 1-1-1 0-1 0-2-1v-1c2 0 3 0 4-1 0-1-1-2-2-3l-2 2h-2v-1-3h-1v-2-1l1-1h0c-1-1-2-1-3-2v-1h0c-1-1-1-1-1-2-1-1-1-1-2-1v-1l-1-1c1 0 2 0 3 1 0-2 0-1-1-3v-2h-1v-1c1 0 1 0 1-1 0 0 0-2-1-3z" class="r"></path><path d="M189 301h2 1s1 1 2 1v2h0v2 1 1h-1 0c-1-2-1-2-2-2 0-1-1-2-2-3-1 0-1 0-2-1l2-1z" class="v"></path><path d="M190 314l1 1h-1c1 1 2 2 3 2h0v1c-1 0-1-1-2-1l-1 1-1 1h2l-1 1v1h0c-2 0-2-1-3-1h-2 0-1v-1h2 1v-1-1h1c1-1-1-1-1-3h3 0z" class="m"></path><path d="M185 310c1 0 2 1 3 2h1c0-1 0-1 1-1v1h0v2h0-3c0 2 2 2 1 3h-1v1 1h-3c-1 0-1 1-2 1v-1l-1-1v-1l1-1c2-2-1-3-1-5h1 1c1 0 1-1 2-1z" class="e"></path><path d="M182 320c1 0 1-1 2-1h3-1-2v1h1 0 2c1 0 1 1 3 1l-1 2h-1 0v1c-1 1-2 1-2 1v1h0 2v2c1 0 2 0 2 1v1 2h-3-2-2c1 0 1 0 2-1-1-1-1 0-2 0v-2h-2v-1l1-1c0-1-1-2-1-3 0 0-1 1-1 0-1 0-1-1-2-1l1-1 3-2z" class="t"></path><path d="M185 328l-1-1c1-2 1-1 2-2v1h0 2v2h-3z" class="m"></path><path d="M180 324c-1 0-1-1-2-1l1-1h1c1 0 1 0 2-1l1 1-2 2s-1 1-1 0z" class="l"></path><path d="M188 328c1 0 2 0 2 1v1 2h-3-2s1 0 2-1h-1 0c-1-1-1-1-1-2h0v-1h3z" class="v"></path><path d="M185 329h1 1 1v1c-1 1-1 1-1 2h-2s1 0 2-1h-1 0c-1-1-1-1-1-2h0z" class="e"></path><path d="M187 320c1 0 1 1 3 1l-1 2h-1 0v1c-1 1-2 1-2 1h0l-1-1c-1-1-1-1-1-2h0c1 0 2-1 2-1l1-1z" class="z"></path><path d="M199 314h1l1-1v2 1l-2-1c-1 1-1 1-1 2h1l1 1c1 1 1 3 1 4v1c1 1 2 1 3 1h0c0 2 0 3 1 4h-1v1h-1-1v-1l-1 1c-1 0-1-1-1-1-1 0-1 1-3 0-1-1-1 1-2 1h-1c-1 0-1 0-1-1l-1-1h-1c-1 0-2 0-3-1h1c1-1 2-1 3 0l1-1v-1h2 1 0v-1c-2 0-3 0-4-2 0 0 1 0 1-1v-1h0 2c0-1 0-1 1-2h-3l1-1h2v-1l-1-1h0c2-1 2 0 4 0z" class="m"></path><path d="M199 317l1 1h-1v2l1 1h0c-1 1-1 1-1 2h-1l-1-2h-3v-1h1c1 0 2-1 3-2 0 0 1 0 1-1z" class="j"></path><path d="M200 318c1 1 1 3 1 4v1c1 1 2 1 3 1h0c0 2 0 3 1 4h-1v1h-1-1v-1l-1 1c-1 0-1-1-1-1l-1-1c-1 0-2-1-3-2 1-1 1-1 2 0 0-1 0-1 1-1l1-1h-1c0-1 0-1 1-2h0l-1-1v-2h1z" class="i"></path><path d="M204 324h0c0 2 0 3 1 4h-1v1-1c-1-1-2-1-2-3h-1v-1l1 1 2-1z" class="c"></path><path d="M197 280h0v2c0 1 1 2 1 3h0c1 1 1 3 2 3l1 3-1 1c1 2 1 4 1 6 0 1 1 3 1 4s0 4 1 6c-1 0-1 0-1 1h1c0 2-1 4 0 6v1h-1l-1-1v-2l-1 1h-1l-1-1h-1v-1c1-1 0-2 1-3h1l1 1h1v-3c-1-1-1-1-2-1v2h-2c0-1 0-1-1-2h3c1-1 1-3 1-3 0-1-1-2-1-2v-2h0l-1 1h-1v-1l1-1c-2-1-2-2-2-4 0-1 1-1 1-2-1 0-1 0-2 1h0l-2-1v-1c1 0 1-1 2-1v-2h-2v-1c1 0 1 0 1-1l-1-1-1 1c-1 0-1 0-2-1v-1c2-1 1 0 3 1h1s1 0 2 1c0-1 1-1 1-1l-1-1c0-1-1-2-2-3h1 1c0-1 0-1 1-1z" class="s"></path><path d="M197 280h0v2c0 1 1 2 1 3h0c1 1 1 3 2 3l1 3-1 1c-1 0-1 0-1 1l-1-1h0c0-1 0-1-1-2 1 0 1 0 1-1h0c-1-1-1-1-2-1 0-1 0-1-1-2h0 1c0-1 1-1 1-1l-1-1c0-1-1-2-2-3h1 1c0-1 0-1 1-1z" class="j"></path><path d="M179 280h1 3 1 2v1h2 0c0 1-1 3 0 4v1h2 2v2h1 2v2c-1 0-1 1-2 1v1l2 1h0c1-1 1-1 2-1 0 1-1 1-1 2 0 2 0 3 2 4l-1 1v1h1v1c-1 1-2 1-3 2l-1 1v-2c-1 0-2-1-2-1h-1-2l-2 1c1 1 1 1 2 1l-2 2h-2v-1-3h-1v-2-1l1-1h0c-1-1-2-1-3-2v-1h0c-1-1-1-1-1-2-1-1-1-1-2-1v-1l-1-1c1 0 2 0 3 1 0-2 0-1-1-3v-2h-1v-1c1 0 1 0 1-1 0 0 0-2-1-3z" class="g"></path><path d="M180 285v2h2v1h1c1 0 2 0 3 1h1 0v1c-1 0-1 0-2 1v2h-1v2h-1v-1h-1 0c-1-1-1-1-1-2-1-1-1-1-2-1v-1l-1-1c1 0 2 0 3 1 0-2 0-1-1-3v-2z" class="e"></path><path d="M179 280h1 3 1 2v1h2 0c0 1-1 3 0 4v1h2c1 0 1 1 2 1l-1 1h0-1c-1 1-1 1-2 1h0-1 0-1c-1-1-2-1-3-1h-1v-1h-2v-2h-1v-1c1 0 1 0 1-1 0 0 0-2-1-3z" class="z"></path><path d="M185 286h2v2 1h-1c-1-1-2-1-3-1 1-1 1-2 2-2z" class="m"></path><path d="M179 280h1 3 1l-2 2v1h3 0v1c-2 0-2 0-2 1v1c1 0 1 0 2-1h0v1c-1 0-1 1-2 2h-1v-1h-2v-2h-1v-1c1 0 1 0 1-1 0 0 0-2-1-3z" class="g"></path><path d="M190 286h2v2h1 2v2c-1 0-1 1-2 1v1l2 1h0c1-1 1-1 2-1 0 1-1 1-1 2 0 2 0 3 2 4l-1 1v1h1v1c-1 1-2 1-3 2l-1 1v-2c-1 0-2-1-2-1h-1-2l1-2h0c-1 0-2 0-3 1h-1v-1-2h0l-1-1v-1h1c1 0 1-1 2-2-1-1-1-2-1-3v-1h1 0c1 0 1 0 2-1h1 0l1-1c-1 0-1-1-2-1z" class="y"></path><path d="M190 286h2v2h1 2v2c-1 0-1 1-2 1 0-1 0-1-1-1-1 1-1 1-2 1s-1-1-1-1c-1 0-1 0-1-1h0c1 0 1 0 2-1h1 0l1-1c-1 0-1-1-2-1z" class="r"></path><path d="M194 302c-1 0-1-1-1-1v-1-1-2-1h0c1-1 1-2 3-2 0 2 0 3 2 4l-1 1v1h1v1c-1 1-2 1-3 2l-1 1v-2z" class="m"></path><path d="M487 193v1c1 3 1 6 1 9 0 2 0 5-1 7 0 2-1 3-1 5-1 7 1 14 1 21v5c0 1 1 2 2 2 0 1 1 1 2 1s2-1 2-1c1-2 1-4 1-5 1-2 1-4 1-6v-19c0-1 0-5 1-6 1 0 1 0 1 1v53c0 5 3 10 2 14-1 2 1 3-1 4 0-2 0-4-1-6l-1 2c-2-1-2-4-2-5-1-4-1-9-2-12-1-2-2-5-3-7 0-1 0-3-2-4-1 0-3 0-4 1-2 1-2 3-3 5s-1 4-2 6c0 4 0 7-1 11v1l-1-1c-1-4 0-9-1-14 0-2-1-11-2-12 0-1 0-1-1-1l-2 2-1-1c0-2 1-2 1-4h-1l1-3h0l5-14 2-5v-1c1-2 2-5 3-7l7-17z" class="f"></path><path d="M473 243c0-1 1-3 1-3v-2l1-1c1 1 1 1 1 2v1c-2 1-2 2-3 3z" class="Q"></path><path d="M481 234l1 1v5h-1s-1-1-1-2 1-2 1-4z" class="S"></path><path d="M480 238h-1c-1-1-1-1-1-2s1-2 2-3h1v1c0 2-1 3-1 4zm2-21c0-2-1-1 0-3v-1l3 3v5h-2v-1h-2c0-2 1-2 1-3z" class="c"></path><path d="M482 217c1 1 1 2 1 3h-2c0-2 1-2 1-3z" class="T"></path><path d="M470 237h1c2-1 3-2 5-3h1l-2 3-1 1v2s-1 2-1 3h-1l-2 2-1-1c0-2 1-2 1-4h-1l1-3h0z" class="V"></path><path d="M477 218h0c0 3-2 5-2 8l-1 1c1 0 1-1 2-1h0c1 0 2-1 3-1-1 0-1-1-1-1v-1-1c1-1 1-2 2-2h1 2v1 1h1c0 1 0 2-1 3v1c-2 1-3 2-4 3 0 2 0 3-1 4l-1 1h0-1c-2 1-3 2-5 3h-1l5-14 2-5z" class="i"></path><path d="M480 220h1 2v1c-1 0-3 1-4 1 0-1 1-1 1-2z" class="V"></path><path d="M478 228v1c0 1-1 2-2 2h-1c-1 1-1 0-2 0 1-1 2-1 2-2v-1h3z" class="c"></path><path d="M478 228h1v1c0 2 0 3-1 4h-1c-1 0-1 0-1-2 1 0 2-1 2-2v-1z" class="G"></path><path d="M475 228h0v-1c1 0 2 0 3-1h0c2 0 3-1 5 0-2 1-3 2-4 3v-1h-1-3zm-181 40c2 0 3 0 4-1 1 0 1 0 1 1h2l1 5s-1 0-1 1c2 0 3 0 4 1v4 1l2 1-1 1c2 0 3 0 5 2v1l1-1 2 3h0l-1 1h-1v2l-1 1v1s-1 1-1 2l1 3v2c1 0 2 1 3 2l1 1c-2 0-2 0-3 1v2c1 4 2 7 6 10h0c2 0 3 0 4 1 2 3 0 7 0 10s0 7 1 9c0 4 2 7 3 11 1 5 0 11 1 17 1 3 3 7 4 11 1 2 2 5 2 8s0 6 1 9c0 5 2 9 3 13-1-1-2-3-2-4l-4-12-8-22-10-31-3-10-3-10-6-20-4-14-2-10-1-3z" class="Z"></path><path d="M310 299h1c1 0 2 1 3 2l1 1c-2 0-2 0-3 1v2l-2-6z" class="l"></path><path d="M308 291h2 0 1v1s-1 1-1 2l1 3v2h-1l-2-8z" class="k"></path><path d="M311 285l1-1 2 3h0l-1 1h-1v2l-1 1h-1 0-2c-1-1-1-2-2-3v-1l1 1 2-1h1l1-2z" class="v"></path><path d="M306 287l1 1 2-1h1v3 1h-2c-1-1-1-2-2-3v-1z" class="R"></path><path d="M294 268c2 0 3 0 4-1 1 0 1 0 1 1h2l1 5s-1 0-1 1c2 0 3 0 4 1v4 1l2 1-1 1c2 0 3 0 5 2v1l-1 2h-1l-2 1-1-1v1h0l-2-2s-1-1-1-2c-1 0-2-1-3-1h-1l1 1h-1c0 1 0 2 1 3 0 3 2 5 1 8l-4-14-2-10-1-3z" class="J"></path><path d="M303 281l2-2v1l2 1-1 1c-1 0-1 0-1 1l-2-2z" class="l"></path><path d="M301 274c2 0 3 0 4 1v4l-2 2c0-1 0-2-1-3l-1-4z" class="g"></path><path d="M306 282c2 0 3 0 5 2v1l-1 2h-1l-2 1-1-1-1-4c0-1 0-1 1-1z" class="o"></path><path d="M294 268c2 0 3 0 4-1 1 0 1 0 1 1h2l1 5s-1 0-1 1l1 4-4-2v4l-1 1-2-10-1-3z" class="T"></path><path d="M294 268c2 0 3 0 4-1 1 0 1 0 1 1s-1 2-1 2c-1 1-2 0-3 1l-1-3z" class="V"></path><path d="M206 354c1 0 1 1 2 2h0c1 2 1 2 1 4h0c0 1 0 1-1 2 1 0 1 1 2 1 0 1 0 1-1 2h1v3c1 1 1 1 1 2 1 1 1 1 2 1l1-2v1c0 1 0 1-1 2v1c1 1 1 1 2 0l1 1c0 1-1 1 0 2h3v-1l1-1h1v-1l1-1h1c0 1 0 2-1 2v2l-1 1c-1 1-1 1-1 2h1c0 1 1 1 1 1 0 1-1 2-1 3v-1h1c1 0 2 1 2 2 1-1 1-1 1-2s1-1 1-2v-1h1v-3h1c0 1 1 1 0 2v1 1h0c2 1 3 3 4 4s1 2 1 3l1 1v1 1c0 1 2 2 2 3v1 1l-1 1v2s-1 0-1 1h0v2c0 1 0 1 1 2v1 2c1 2 0 2 0 3h1l1 1h0c0 2 1 2 2 3h0l1 1 2-1v1c0 1 0 2 1 2l1-1c1 0 1 1 1 1-1 2-2 3-3 5v1 1h-1c-1-1 0-1 0-3h-1v3c-1 1-2 1-3 1v1c-1-1-1-2-2-3-1 1-1 1-2 1h0c0 1-1 1-1 1h-1c0-1 0-1-1-2 0 0-1 0-1 1v3c-1 0-2 1-3 1-1-1-1-1-1-3h-1c0 1-1 2-2 2h-1v-2h-2c-1 0-1 0-2 1h0c-1-1-1-1-1-2h-1l-1-1v-1c1 0 1-1 1-1l1-1v-2h0l-2 1v-1l1-2v-1h-2v-1l1-1-2-2h0l1-1s1 0 1-1l-2-2-1 1h-1-1v-2l1-1h-1v-2h0l-1 1c-1 0-1 0-1-1v-3h0 0v1l-1 1c-1 0-1-1-2-1-1-1-1-2-3-2v-1c0-1 1-1 2-1l-1-1h-1-1l-1-1 1-1 2 1h0v-1c1 0 1 0 1 1v1h0l1-1-1-1h0-1s-1 0-1-1h0l-1 1h-1 0l-1-1v-1c-1 0-1-1-2-1h-2v-3h2l1 1h0l1 1v-1l1 1v1h0 1c-1-1 0-1 0-2 1-1 1 0 2 0v-1l-1-1h0l-2 2c-1 0-1 0-2-1-1 0-1 0-2-1h-1-1-2v-1l1-1c1 0 2-1 2-1h1c-1 1-1 1-1 2l1-1h1v-1c-1 0-1 0-2-1v-1c0-1 1-2 2-3h-2l-1-1h0c-1 0-1 0-2-1v-3c-1-1-1-1-1-2h0l1 1 1 1h0c1 0 1 1 3 1h-1v-2c0-2 1-2 2-3h-2c-1 0-1-1-2-1v-1-1l1-1 1 1c0-1 0 0 1 0h1v-1h-1c-1-1-1-1-1-2v-1h0l-1-1v1 1c-2 0-2 0-3-1v-1l1-1-1-1h-1l-1-1 2-1v-1h1s1 0 1 1l2-1s0 1 1 1l1 1c-1 1-1 1-1 2h0 1c1 0 2 0 2-1l1-1h1 2l1-1v-1h1 1l1-2z" class="n"></path><path d="M195 372c1 1 2 0 2 1v1c1 0 1 1 2 1h0c0 1 0 1 1 1-2 1-2 1-4 1h-1l2-1v-1l-1-1c-1 0-1 0-2-1l1-1z" class="k"></path><path d="M188 358l2-1c1 0 1 1 2 1h2l-1 2c1 1 1 1 2 1h2v1c1 1 1 1 2 1v1 1c0 1 1 2 1 2-1 1-1 1-3 1-1 0-2 1-3 1 0-1-1-1-1-2v-1c2 0 2 1 3 1l1-1-1-1c-1 0-1-1-2-1v-1l2-1h-2l-1-1v1 1c-2 0-2 0-3-1v-1l1-1-1-1h-1l-1-1z" class="e"></path><path d="M199 365c0 1 1 2 1 2-1 1-1 1-3 1v-2c1-1 1 0 2-1z" class="g"></path><path d="M217 408h0 2v3h1 1 2v-1h-1l1-1 1 1v1l1 1v1l-2-1s-1 0-1 1c-2 1-1 0-2 1 0 1 0 1 1 1h1c-1 2-1 2-1 4 0 1-1 1-1 1-1 0-1-1-2-1v-2c-1-1-2-1-2-2s1 0 2-1v-2h-1c0-1 0-1-1-2 0 0-1 0-1-1v-1h2zm-13-23c0-1 0-2 1-3h1c0 1 1 1 1 2 1 0 1 1 2 1h3l1 1c0 1 0 2 1 3h1v1h-1l-1-1h-1v1l2 1v1c-1 0-2-1-3 0l-1 1h0v2h1v1l1 1h-2c-2 0-1-1-1-2h-2v-1l1-1c0-1 0-1-1-2l-2 1c0-1 0-2 1-3h-1 0-2-1 0l1-1-1-2h0v-1l2 1 1-1h0-1z" class="l"></path><path d="M207 384c1 0 1 1 2 1h3l1 1c0 1 0 2 1 3h1v1h-1l-1-1h-1v1l2 1v1c-1 0-2-1-3 0l-1 1v-1c-1 0-1 0-2-1h0l1-1c1-1 1-1 1-2-1 0-1 1-2 1l-1-1v-1s1 0 1-1c-1 0-1-1-2-1l1-1z" class="t"></path><path d="M195 372v-1c0-1 1-1 2-1v1c2 0 2-1 3-2h1c0 2 0 2-1 4h1c1-1 1-1 1-3h0l2 1h2v2c0 1 0 1-1 2h1 2v2h2 0v1c-2 0-2 0-2-1-1 1-1 2-1 3l1 1h0v-2h0c1 0 1 1 2 1 0 1 1 1 2 2v1h0-3v2c-1 0-1-1-2-1 0-1-1-1-1-2h-1c-1 1-1 2-1 3-1-1-1-1-2-1l2-2v-1c1-1 1-1 1-2h0c-1 0-1 0-2 1l-1 1h0v-2c1-1 1-1 1-2-1-1-1-2-2-3 0 0-1 0-1-1-1 1 0 2 0 3-1 0-1 0-1-1h0c-1 0-1-1-2-1v-1c0-1-1 0-2-1z" class="e"></path><path d="M213 397h2 2 0c1 1 2 2 3 2v1l-1 1c1 0 1 1 2 1l1 1v1 1 1c1 0 2-1 3-2l1 1h0l-1 1v1h0c1 1 1 1 1 2v2s1 1 2 1c0 1-1 1-1 1-1 0-1 0-2-1l-1-1v-1l-1-1-1 1h1v1h-2-1-1v-3h-2 0v-1c0-1-1-1-2-1v-1l1-2-1-1c-2 1-2 1-3 2h-1c-1-1-1-1-1-3-1 0-1 0-2 1v-1-2h1l1 2c1 0 2 1 2 0v-1c-1 0-1 0-1-1s0 0 1-1c1 0 1 0 1-1z" class="t"></path><path d="M222 403c-1 1-2 1-3 2 0-2-1-2-3-3h0v-2l1-1v1 1h1 1c1 0 1 1 2 1l1 1z" class="m"></path><path d="M206 354c1 0 1 1 2 2h0c1 2 1 2 1 4h0c0 1 0 1-1 2 1 0 1 1 2 1 0 1 0 1-1 2h1v3c0 1 0 2 1 3v1c0 1 0 2 1 3v2 1 1l2 1c0 1-1 2-1 3 1 1 1 1 1 2v1h2 1c-1 1-2 1-2 3h-1c-1-1-1-2-1-3l-1-1h-3v-2h3 0v-1c-1-1-2-1-2-2-1 0-1-1-2-1h0v2h0l-1-1c0-1 0-2 1-3 0 1 0 1 2 1v-1h0-2v-2h-2-1c1-1 1-1 1-2v-2h-2l-2-1h0c0 2 0 2-1 3h-1c1-2 1-2 1-4 0-1 0-1-1-2 0 0-1-1-1-2v-1-1c-1 0-1 0-2-1v-1h-2c-1 0-1 0-2-1l1-2h-2c-1 0-1-1-2-1v-1h1s1 0 1 1l2-1s0 1 1 1l1 1c-1 1-1 1-1 2h0 1c1 0 2 0 2-1l1-1h1 2l1-1v-1h1 1l1-2z" class="x"></path><path d="M202 358h1c1 1 1 1 1 2v1l-1-1-1 1h-1v-1l-1 1v-3h2z" class="e"></path><path d="M208 356c1 2 1 2 1 4h0c0 1 0 1-1 2 1 0 1 1 2 1 0 1 0 1-1 2h1v3c0 1 0 2 1 3v1c0 1 0 2 1 3v2 1 1h0-1v-3c-1 0-2-1-3-2 1-1 1 0 2-1h0l-1-1 1-1v-1c-1 0-1-1-2-1h0v-1h0 1 1c-1-1-1 0-2-1 0 0-1-2-1-3h-1v-1l1 1v1l1-1v-1c-1 0-1-1-2-1h0c-1-1-1-1-1-2l2-1-1-2h2v-1z" class="s"></path><path d="M191 356s1 0 1 1l2-1s0 1 1 1l1 1c-1 1-1 1-1 2h0 1c1 0 2 0 2-1l1-1h1v3 1c-1 0-1 0-1 2h1c1 0 1-1 2-1l-1 2 2 2v-1s1 0 2-1l1 1v1h-1-1c-1 1-1 2-2 2v1h0 0c0 2 0 2-1 3h-1c1-2 1-2 1-4 0-1 0-1-1-2 0 0-1-1-1-2v-1-1c-1 0-1 0-2-1v-1h-2c-1 0-1 0-2-1l1-2h-2c-1 0-1-1-2-1v-1h1z" class="t"></path><path d="M198 359l1-1h1v3 1l-1-1-1-2z" class="g"></path><path d="M216 386l1-1 1-1h0l1 1c0 1 0 1-1 1l1 2c2 0 2 0 3-1l1 2c0 1 1 1 2 2h1l1 1-1 1 1 1 1-1c1 1 1 1 1 3 0 0 0 1-1 2h1c1-1 1-1 2-1v1 1 1l-1-1c-1 0-2 1-3 2 0 1 2 1 3 2 1 0 1 1 2 2h0c-1 2-1 2-1 4h1l-1 2h-1l1 2-1 1-2-1v1c-1 1 0 1-1 2-1-1-1-1-1-2h-3v-1h2 0v-1c1 1 1 1 2 1 0 0 1 0 1-1-1 0-2-1-2-1v-2c0-1 0-1-1-2h0v-1l1-1h0l-1-1c-1 1-2 2-3 2v-1-1-1l-1-1c-1 0-1-1-2-1l1-1v-1c-1 0-2-1-3-2h0-2-2-1l-1-1v-1h-1v-2h0l1-1c1-1 2 0 3 0v-1l-2-1v-1h1l1 1h1v-1c0-2 1-2 2-3h-1z" class="s"></path><path d="M226 393l1 1 1-1c1 1 1 1 1 3-1 0-1 0-1 1-2 0-2-1-3-1h-1v-1l1-1h1v-1z" class="S"></path><path d="M225 394l-2-1v1c-1 0-2-1-2-1v-2c0-1-1-1-1-2h2 1c0 1 1 1 2 2h1l1 1-1 1v1h-1z" class="c"></path><path d="M221 399s1-1 2-1v1h0l1 1v2l1 1v-2l1-1v1 1l1 1c-1 2 0 2-1 3h-1l1-1h0l-1-1c-1 1-2 2-3 2v-1-1-1l-1-1v-2l1-1h-1z" class="r"></path><path d="M215 389h3 1c-1 1-1 1-1 2h1v1h0l-2-2c-1 0-1 1-2 2h0 1c1 0 1 1 2 1h1l-2 2v1h1c1 0 1-1 1-2l2 2 1 1h-2v1l1 1h1l-1 1v2c-1 0-1-1-2-1l1-1v-1c-1 0-2-1-3-2h0-2-2-1l-1-1v-1h-1v-2h0l1-1c1-1 2 0 3 0v-1l-2-1v-1h1l1 1h1v-1z" class="x"></path><path d="M210 393l1 1h2l1 1c1-1 0-2 2-2v1c0 1 0 1 1 1l-2 2h-2-1l-1-1v-1h-1v-2zm25 11v2c1 2 0 2 0 3h1l1 1h0c0 2 1 2 2 3h0l1 1 2-1v1c0 1 0 2 1 2l1-1c1 0 1 1 1 1-1 2-2 3-3 5v1 1h-1c-1-1 0-1 0-3h-1v3c-1 1-2 1-3 1v1c-1-1-1-2-2-3-1 1-1 1-2 1h0c0 1-1 1-1 1h-1c0-1 0-1-1-2 0 0-1 0-1 1v3c-1 0-2 1-3 1-1-1-1-1-1-3h-1c0 1-1 2-2 2h-1v-2h-2v-2c1 0 1-1 2-3h0c0-2 0-2 1-4h-1c-1 0-1 0-1-1 1-1 0 0 2-1 0-1 1-1 1-1l2 1h0-2v1h3c0 1 0 1 1 2 1-1 0-1 1-2v-1l2 1 1-1-1-2h1l1-2h-1c0-2 0-2 1-4 1 0 2-1 3-1z" class="e"></path><path d="M240 420h0v3c-1 1-2 1-3 1v-2-1c1 0 2 0 2-1h1z" class="g"></path><path d="M239 413h0l1 1 2-1v1c-1 2-2 3-2 4-1 0-2-3-2-3 0-1 1-1 1-2z" class="j"></path><path d="M242 414c0 1 0 2 1 2l1-1c1 0 1 1 1 1-1 2-2 3-3 5v1 1h-1c-1-1 0-1 0-3h-1 0v-2c0-1 1-2 2-4z" class="f"></path><path d="M242 414c0 1 0 2 1 2l-2 3v1h-1 0v-2c0-1 1-2 2-4z" class="t"></path><path d="M235 404v2c1 2 0 2 0 3h1v1 1 2 1c-1 0-2 0-2-1l-1 1v2h-1v-3-1l-1-1 1-2h-1c0-2 0-2 1-4 1 0 2-1 3-1z" class="f"></path><path d="M222 415l1 2 2-1h0c1 1 1 2 1 3l1 1h2 0c-1 2-2 2-3 2l1 2c1-1 1-1 2-1v3c-1 0-2 1-3 1-1-1-1-1-1-3h-1c0 1-1 2-2 2h-1v-2h-2v-2c1 0 1-1 2-3h0c0-2 0-2 1-4z" class="g"></path><path d="M226 419l1 1h2 0c-1 2-2 2-3 2h0l-1 1h-3v-2c1 0 1 0 2-1v-1l1 1 1-1z" class="p"></path><path d="M210 368c1 1 1 1 1 2 1 1 1 1 2 1l1-2v1c0 1 0 1-1 2v1c1 1 1 1 2 0l1 1c0 1-1 1 0 2h3v-1l1-1h1v-1l1-1h1c0 1 0 2-1 2v2l-1 1c-1 1-1 1-1 2h1c0 1 1 1 1 1 0 1-1 2-1 3v-1h1c1 0 2 1 2 2 1-1 1-1 1-2s1-1 1-2v-1h1v-3h1c0 1 1 1 0 2v1 1h0c2 1 3 3 4 4s1 2 1 3l1 1v1 1c0 1 2 2 2 3v1 1l-1 1v2s-1 0-1 1h0v2c0 1 0 1 1 2v1c-1 0-2 1-3 1h0c-1-1-1-2-2-2-1-1-3-1-3-2 1-1 2-2 3-2l1 1v-1-1-1c-1 0-1 0-2 1h-1c1-1 1-2 1-2 0-2 0-2-1-3l-1 1-1-1 1-1-1-1h-1c-1-1-2-1-2-2l-1-2c-1 1-1 1-3 1l-1-2c1 0 1 0 1-1l-1-1h0l-1 1-1 1h-2v-1c0-1 0-1-1-2 0-1 1-2 1-3l-2-1v-1-1-2c-1-1-1-2-1-3v-1c-1-1-1-2-1-3z" class="u"></path><path d="M224 384c1-1 1-1 1-2s1-1 1-2c1 1 1 2 0 3v1h0 0v1l-2-1zm7 17l1-1c1-1 1-1 2-1v2c0 1 0 1 1 2v1c-1 0-2 1-3 1h0c-1-1-1-2-2-2v-1-1h1z" class="i"></path><path d="M230 403v-1-1h1c1 1 2 1 2 3 0 0-1 0-1 1-1-1-1-2-2-2z" class="c"></path><path d="M234 388v1 1c0 1 2 2 2 3v1l-2 1c-1 0-2-2-2-3-1 1-2 1-3 1h-1v-1h1l1-1h1v-1l1 1h0 1v-1l1-1v-1z" class="S"></path><path d="M210 368c1 1 1 1 1 2 1 1 1 1 2 1l1-2v1c0 1 0 1-1 2v1c1 1 1 1 2 0l1 1c0 1-1 1 0 2h0l1 1h2 0l1-1h0v2l-1 1v-1c-1 0-1 0-2 1v2h2 0c0-1 0-1 1-1h1v1h-1v3 1 1h2v1c-1 1-1 1-3 1l-1-2c1 0 1 0 1-1l-1-1h0l-1 1-1 1h-2v-1c0-1 0-1-1-2 0-1 1-2 1-3l-2-1v-1-1-2c-1-1-1-2-1-3v-1c-1-1-1-2-1-3z" class="c"></path><defs><linearGradient id="D" x1="540.366" y1="135.772" x2="498.19" y2="121.403" xlink:href="#B"><stop offset="0" stop-color="#000503"></stop><stop offset="1" stop-color="#1d0d0d"></stop></linearGradient></defs><path fill="url(#D)" d="M555 80c1 0 2 0 2-1 1 1 2 0 3 1-2 1-3 3-4 5h0c-2 2-5 3-7 5-9 7-17 16-24 26-1 2-3 5-4 8-5 5-8 12-11 18l-4 9c-2 3-3 5-4 8-2 4-4 7-5 11l-1 2c-2 2-3 6-4 8l-2 2h-1 0-1l4-10 3-9 1-1-1-1c-1 0-2-1-3-1-1 1-2 3-3 5l-3 3c-2 2-4 4-4 6-1 1-2 2-3 4h-1-2l1-2h-1c-2 3-3 6-5 9 0 1-1 3-1 4 0-1-1-1-1-1v-1c-1-1-1-1-1-2l3-9c1-1 1-2 1-3 1-2 1-4 1-6s1-3 2-5v-1h0l1-2c1-1 1-3 2-4 0-1-1-2-1-2l1-1c0-1 1-2 2-3 1-2 5-6 5-8h-1c0 1-1 1-1 2l-1 1h0v-1l1-1c0-1 1-1 1-2-1 0-3 1-3 2h-1 0v-1-1l1-2v-2h0-1c-2-1-2-1-3-3s-2-4-4-5c-1-1-1-2-2-3s-2-1-2-2c-1 0-2-1-3-1v-2-1h0l-1-1h-2l-1 1v-1l-1-1 1-2c-3 1-5 1-8 2v-1l4-2h10c2-1 4 0 6-1l8-1 8-2h2l2-1h3l6-3c8-3 15-8 22-12 1-1 2-2 4-2l7-4 7-4 6-3c2-1 5-2 6-1z"></path><path d="M490 155h1l1 1c-1 0-2 2-2 2-1-1-2-1-2-2h1l1-1z" class="B"></path><path d="M523 105h2c0 1-3 3-3 4l-2-2c1-1 1-2 3-2zm-27 52l1-2 1 1c0 2-1 4-2 6h0l-1-1c-1 0-2-1-3-1 1-1 3-2 4-3z" class="N"></path><path d="M496 148l1 1-5 7-1-1h-1c1-1 1-3 3-4l3-3z" class="H"></path><path d="M531 97c0 1 1 1 1 2h0c-1 1-2 1-3 1h-1v1h1v1c-1 0-2 0-3-1h0c-1-1-2-1-3-2 1-1 3-1 4-1s2-1 3-1h0 1z" class="X"></path><path d="M532 96c2-3 4-5 8-6-2 2-4 7-7 8l-1 1c0-1-1-1-1-2l1-1z" class="W"></path><path d="M532 96c1 1 1 1 1 2l-1 1c0-1-1-1-1-2l1-1z" class="L"></path><path d="M520 107l2 2h0c0 4-2 5-4 8 0-1-1-1-1-1v-1l-1-1h-1v-2c1 0 1-1 2-1 1-1 2-2 3-4z" class="X"></path><path d="M517 111c0 1 0 1 1 2-1 1-1 1-3 1v-2c1 0 1-1 2-1z" class="I"></path><path d="M520 107l2 2h0c-1 1-2 3-4 4-1-1-1-1-1-2 1-1 2-2 3-4z" class="J"></path><path d="M495 146h2 0l-1 2-3 3c-2 1-2 3-3 4l-1 1h-1v-3h0l3-6 1-2c1 1 2 1 2 2l1-1z" class="L"></path><path d="M491 147l1-2c1 1 2 1 2 2l-3 3c0 1-1 3-2 4l1 1-1 1h-1v-3h0l3-6z" class="O"></path><path d="M513 111h0l1-2c1-2 5-6 7-7l2-1 1 1v2h-2l1 1c-2 0-2 1-3 2-1 2-2 3-3 4-1 0-1 1-2 1v-1h-2z" class="W"></path><path d="M485 165h0c0 2-1 4-3 5 0 1 0 1-1 2h0c1 0 1 0 1-1 0 0 1 0 1-1h1v-1l2-2v-1l1-1c1 0 2-2 3-4v-1c1-1 3-3 3-4h2 0l1 1c-1 1-3 2-4 3s-2 3-3 5l-3 3c-2 2-4 4-4 6-1 1-2 2-3 4h-1-2l1-2h-1s1-3 2-4c0-1 0-2 1-3 1 1 1 1 3 0 1-1 2-3 3-4z" class="C"></path><path d="M479 169c1 1 1 1 3 0l-2 4-2-1h0c0-1 0-2 1-3z" class="B"></path><path d="M478 172h0l2 1v1h2c-1 1-2 2-3 4h-1-2l1-2h-1s1-3 2-4z" class="U"></path><path d="M549 81c2-1 5-2 6-1-9 5-19 9-28 14-4 2-8 5-12 8 0 0-4 2-5 3-2 1-5 3-7 4-6 2-12 3-17 4-4 1-8 1-12 1v-1l8-1 8-2h2l2-1h3l6-3c8-3 15-8 22-12 1-1 2-2 4-2l7-4 7-4 6-3z" class="B"></path><defs><linearGradient id="E" x1="500.47" y1="121.738" x2="510.271" y2="134.559" xlink:href="#B"><stop offset="0" stop-color="#0c0a09"></stop><stop offset="1" stop-color="#2a0c0c"></stop></linearGradient></defs><path fill="url(#E)" d="M513 111h2v1 2h1l1 1v1s1 0 1 1l-4 5-4 5c0 1-1 1-1 2h0l-3 6c-1 2-8 13-9 14l-1-1 1-2h0-2l-1 1c0-1-1-1-2-2l-1 2-2 2v-3c0-1 3-5 3-6l1-1h-1v-1c1-1 1-1 1-2l1-1c1-1 1-2 2-3s1-2 2-3v-1c1-1 1-1 2-1h0l2-3h1c1-1 1-1 1-2l2-2c0-1 1-1 1-2 1 0 1-1 1-1l3-3v-1h0c1 0 2-1 2-2z"></path><path d="M505 135l1-1v-2c1-2 1-3 3-3h0l-3 6h-1z" class="L"></path><path d="M517 116s1 0 1 1l-4 5c-1-1-3 0-4-1h1c2 0 5-4 6-5z" class="C"></path><path d="M497 137l2-4h1 1l-1 2c-1 2-2 3-2 5-1 1-1 1-1 2v-4h0l-1-1h1z" class="E"></path><path d="M510 121c1 1 3 0 4 1l-4 5c-1 0-2-1-2-2 0-2 1-2 2-4zm-14 16l1 1h0v4 1l-2 1v1 1l-1 1c0-1-1-1-2-2 1 0 1-1 1-2l3-6z" class="M"></path><path d="M493 143c2 1 2 1 2 2v1l-1 1c0-1-1-1-2-2 1 0 1-1 1-2z" class="B"></path><path d="M497 146c1-2 3-4 4-5 1-2 1-3 3-4h0v-1l-1-1c-1 0-1-1-1-2l1 1 1-1v2h1 1c-1 2-8 13-9 14l-1-1 1-2z" class="F"></path><path d="M492 139v-1c1-1 1-1 1-2l1-1c1-1 1-2 2-3s1-2 2-3v-1c1-1 1-1 2-1h0c0 1-1 2-1 2-1 1-1 2-1 2l-1 2h0v4h-1l-3 6c0 1 0 2-1 2l-1 2-2 2v-3c0-1 3-5 3-6l1-1h-1z" class="W"></path><path d="M474 114c4 0 8 0 12-1 5-1 11-2 17-4 2-1 5-3 7-4v2h1 0c-2 2-3 4-5 5v2c1-1 1-1 3-2h0 1v1c-1 2-2 3-4 4 0 1 0 1-1 2v1h-1v2c0 1 0 1-1 2h-1l-2 3h0c-1 0-1 0-2 1v1c-1 1-1 2-2 3s-1 2-2 3l-1 1c0 1 0 1-1 2v1h1l-1 1c0 1-3 5-3 6v3l2-2-3 6h0v3c0 1 1 1 2 2l-5 7c-1 1-2 3-3 4-2 1-2 1-3 0-1 1-1 2-1 3-1 1-2 4-2 4-2 3-3 6-5 9 0 1-1 3-1 4 0-1-1-1-1-1v-1c-1-1-1-1-1-2l3-9c1-1 1-2 1-3 1-2 1-4 1-6s1-3 2-5v-1h0l1-2c1-1 1-3 2-4 0-1-1-2-1-2l1-1c0-1 1-2 2-3 1-2 5-6 5-8h-1c0 1-1 1-1 2l-1 1h0v-1l1-1c0-1 1-1 1-2-1 0-3 1-3 2h-1 0v-1-1l1-2v-2h0-1c-2-1-2-1-3-3s-2-4-4-5c-1-1-1-2-2-3s-2-1-2-2c-1 0-2-1-3-1v-2-1h0l-1-1h-2l-1 1v-1l-1-1 1-2c-3 1-5 1-8 2v-1l4-2h10c2-1 4 0 6-1v1z" class="X"></path><path d="M498 118l2 1v1h0c-1 1-2 1-3 1v-1c1-1 1-1 1-2h0z" class="M"></path><path d="M498 118c1-2 2-3 3-5l1 1c0 2-1 3-2 5l-2-1z" class="B"></path><path d="M481 138l4-3c0 1 0 2 1 3l-6 3v-1l1-2z" class="C"></path><path d="M502 114h3c0-1 1-2 1-2v2l-2 1h0c0 1-1 2-2 3s-2 1-2 2v-1c1-2 2-3 2-5z" class="E"></path><path d="M510 107h1 0c-2 2-3 4-5 5 0 0-1 1-1 2h-3l-1-1s1 0 1-1c3-1 6-4 8-5z" class="d"></path><path d="M476 121h1v-1c1 0 2 0 2 1 2 1 1 2 2 3s2 2 2 3v1s-1 1-2 1l-2-2c0-2-2-4-3-6z" class="W"></path><path d="M468 114c2-1 4 0 6-1v1 1c2 1 4 1 6 2l2 1c2 1 5 4 6 6l1 1c1 2 2 5 2 7h-1c-1-5-4-10-8-13h-1c-6-3-12-4-19-4-3 1-5 1-8 2v-1l4-2h10z" class="H"></path><path d="M466 119h3l1-1 1 1h3 0l2 2c1 2 3 4 3 6l2 2c1 0 2-1 2-1l1 3v2c-1 0-1-1-2-1v3l-1 1h0-1c-2-1-2-1-3-3s-2-4-4-5c-1-1-1-2-2-3s-2-1-2-2c-1 0-2-1-3-1v-2-1h0z" class="F"></path><path d="M481 129c1 0 2-1 2-1l1 3c-1 0-2-1-3-2z" class="C"></path><path d="M466 119h3l1-1 1 1h3 0-1v1l1 1c0 1-1 1-2 2 1 2 2 2 3 3l-2 2c-1-1-1-2-2-3s-2-1-2-2c-1 0-2-1-3-1v-2-1h0z" class="K"></path><path d="M466 119l3 3v1c-1 0-2-1-3-1v-2-1z" class="I"></path><path d="M469 122c2 0 2 0 3 1 1 2 2 2 3 3l-2 2c-1-1-1-2-2-3s-2-1-2-2v-1z" class="M"></path><path d="M475 126c1 1 1 2 1 3 1 1 1 1 2 1l-1-1s0-1-1-2v-1l2 2c2 1 3 2 4 4h0v3l-1 1h0-1c-2-1-2-1-3-3s-2-4-4-5l2-2z" class="L"></path><path d="M475 126c1 1 1 2 1 3 1 1 1 1 2 1 1 1 0 4 2 5 0 0 0 1 1 1h-1c-2-1-2-1-3-3s-2-4-4-5l2-2z" class="Y"></path><path d="M492 139h1l-1 1c0 1-3 5-3 6v3l2-2-3 6h0v3c0 1 1 1 2 2l-5 7c-1 1-2 3-3 4-2 1-2 1-3 0-1 1-1 2-1 3-1 1-2 4-2 4-2 3-3 6-5 9 0 1-1 3-1 4 0-1-1-1-1-1v-1c-1-1-1-1-1-2l3-9c1-1 1-2 1-3 1-2 1-4 1-6s1-3 2-5v2c-1 1-1 3-1 4v1c0 1-1 3-1 5 1-2 1-3 2-4h0c1-1 1-1 1-2l1-3 2-2 3-6c3-6 6-13 10-18z" class="Q"></path><path d="M484 159c1-5 3-9 5-13v3l2-2-3 6h0c-2 2-3 4-4 6h0z" class="K"></path><path d="M475 162v2c-1 1-1 3-1 4v1c0 1-1 3-1 5 1-2 1-3 2-4h0c1-1 1-1 1-2l1-3 2-2-10 24c-1-1-1-1-1-2l3-9c1-1 1-2 1-3 1-2 1-4 1-6s1-3 2-5z" class="F"></path><path d="M488 153v3c0 1 1 1 2 2l-5 7c-1 1-2 3-3 4-2 1-2 1-3 0h1c1-2 2-3 3-5l1-1-1-1h0l-1 1h-1l1-1v-1c0-1 1-1 1-1 0-1 0-1 1-1h0c1-2 2-4 4-6z" class="G"></path><path fill="#0e0b0a" d="M118 99l-1-1c-1-2-4-3-6-4-6-4-12-6-19-9-4-1-7-3-11-4 0-11 1-21 3-32 0-2 0-4 1-5 4 12 10 23 20 32 5 4 10 7 16 10 5 3 10 5 15 6 15 6 32 7 48 8h11 9 22 3 76c-1 3-4 4-6 5-3 3-5 6-6 8-2 3-4 6-5 10l1 1c0-1 0-2 1-2v1c0 1 0 1-1 2v3 8l1 63v4l-1 2v1c-1 1-2 0-3 1h0c0 1 1 1 1 2v1h-1s-1 0-1 1c-1 1 0 2 0 3 0 2 0 9 1 9l8 45 1 3 2 10 4 14 6 20 3 10 3 10 10 31 8 22 4 12c0 1 1 3 2 4l3 9 4 11v-2c-1-2-3-5-3-7 1 0 2-2 3-2l1 1v1c0 1 1 1 1 2v1c0 1 1 3 1 4 1 1 1 2 1 2 2 1 3 0 4 2v2 1 3l2 8c1 3 1 6 2 9h0l9 12c0 2 1 7 3 7l17-54 20-67c4-16 8-32 11-49 8-48 7-101-14-147-6-13-15-28-27-37-9-6-19-10-29-14h73 45c7 0 15-1 23-2 6-1 13-2 19-4 3 0 6-1 9-2 3 0 5 0 7 2l3-1v1c-7 4-14 9-22 12l-6 3h-3l-2 1h-2l-8 2-8 1c-2 1-4 0-6 1h-10l-4 2v1c3-1 5-1 8-2l-1 2 1 1v1l1-1h2l1 1h0v1 2c1 0 2 1 3 1 0 1 1 1 2 2s1 2 2 3c2 1 3 3 4 5s1 2 3 3h1 0v2l-1 2v1 1h0 1c0-1 2-2 3-2 0 1-1 1-1 2l-1 1v1h0l1-1c0-1 1-1 1-2h1c0 2-4 6-5 8-1 1-2 2-2 3l-1 1s1 1 1 2c-1 1-1 3-2 4l-1 2h0v1c-1 2-2 3-2 5s0 4-1 6c0 1 0 2-1 3l-3 9c0 1 0 1 1 2v1s1 0 1 1c0-1 1-3 1-4 2-3 3-6 5-9h1l-1 2h2 1c1-2 2-3 3-4 0-2 2-4 4-6l3-3c1-2 2-4 3-5 1 0 2 1 3 1l1 1-1 1-3 9-4 10h1 0 1l2-2c0 1-1 3-1 4-1 3-3 6-4 10v-1l-7 17c-1 2-2 5-3 7v1l-2 5-5 14h0l-1 3h1c0 2-1 2-1 4l1 1-7 11c-1 3-3 7-4 10 0 5-1 10-2 15-1 1-2 1-2 2l-11 28c-1 2-2 4-2 7h0c-1 1-1 2-1 3-1 2-1 4-2 7l-2 4-2 7-1 1-9 27-14 37-4 14c-1 3-3 6-4 9-1 7-4 14-6 22-2 3-3 6-4 10 2 2 2 5 2 8l1 8v5 4c0 1-1 1-2 1 1 1 1 1 1 2h0c-1 1-1 2-1 3h0l-1 2h1 2c-1 1-1 1-2 1h0-1v-3s-1 0-1 1c-1 1-3 0-4 0h-1c-1 0-1 1-1 2l-3 5c0 1-1 2-1 3v2c-1 1-1 2-2 4h-2-1 0l-1 1-11 31-4 10-33 102-43-124-11-32-3-10c0-1-1-3-2-4l-5-15-3-8-1-3c0-3-2-7-3-10 0-1-1-3-2-4l-5-15-1-5-2-4-2-1s0-1-1-1l-1 1c-1 0-1-1-1-2v-1l-2 1-1-1h0c-1-1-2-1-2-3h0l-1-1h-1c0-1 1-1 0-3v-2-1c-1-1-1-1-1-2v-2h0c0-1 1-1 1-1v-2l1-1v-1-1c0-1-2-2-2-3v-1-1l-1-1c0-1 0-2-1-3s-2-3-4-4h0v-1-1c1-1 0-1 0-2h-1v3h-1v1c0 1-1 1-1 2s0 1-1 2c0-1-1-2-2-2h-1v1c0-1 1-2 1-3 0 0-1 0-1-1h-1c0-1 0-1 1-2l1-1v-2c1 0 1-1 1-2h-1l-1 1v1h-1l-1 1v1h-3c-1-1 0-1 0-2l-1-1c-1 1-1 1-2 0v-1c1-1 1-1 1-2v-1l-1 2c-1 0-1 0-2-1 0-1 0-1-1-2v-3h-1c1-1 1-1 1-2-1 0-1-1-2-1 1-1 1-1 1-2h0c0-2 0-2-1-4h0v-6h0c-1-2-1-4-1-5 1 0 1 0 2-1-1 0-1-1-2-1-1-1-1-3-1-4s0-1-1-2c1-1 1-2 1-3s0-2-1-3l-1-3h1c-1-1-1-2-1-4h0c-1 0-2 0-3-1v-1c0-1 0-3-1-4l-1-1h-1c0-1 0-1 1-2l2 1v-1l1 1h1v-1c-1-2 0-4 0-6h-1c0-1 0-1 1-1-1-2-1-5-1-6s-1-3-1-4c0-2 0-4-1-6l1-1-1-3c-1 0-1-2-2-3h0c0-1-1-2-1-3v-2h0c0-2-1-2-2-3v-2c1 1 2 1 3 1l-3-7c-1 1-1 1-3 1h0c-1 0-1-1-2-2v-3h-2l-1-1h0-2c-1 1-1 1-2 1v-2-2-1c0-1 1-1 1-2-1 0-1 0-1-1h-1l2-2s-1 0-1-1v-1-2l1-1h1 0 1v-2l2-1-6-16-4-11-2-6c-2-7-5-14-7-21l-8-22c-4-10-7-21-11-31-1-1-2-3-2-4l-1-2c-1-1-1-3-2-4-1-3-4-9-6-10 0-2-3-5-4-6l-3-3v-1c-3-4-8-7-11-9l-3-2z"></path><path d="M357 486h1c1 1 1 1 0 2h-1v-2zm56-368c1-1 2-2 3-2l1 1-3 2-1-1z" class="W"></path><path d="M233 309h3v2h0v1h-1c-1-1-1-2-2-3zm-2 0c0 1 1 1 1 3v3l-1-2-1-1c0-1 1-2 1-3z" class="L"></path><path d="M151 116c1 0 1 1 2 1 1 1 1 2 1 3h0c-1-1-2-2-3-4z" class="W"></path><path d="M448 277c1 0 2-1 3-1-1 1-1 2-2 4l-2-2 1-1z" class="H"></path><path d="M223 203c-1-1-1-2-1-3l-1-1c0-1 0-1 1-2h0c0-2 0-2 1-4v10z" class="X"></path><path d="M447 278l2 2h-1l-2 2v-3c1 0 1-1 1-1z" class="O"></path><path d="M329 512c1 1 0 2 0 3l1 1c0 1 0 2-1 3h0c0-1-1-1 0-2v-5z" class="W"></path><path d="M232 295h0l1 1v-1c1 1 1 2 2 3v1h-2c0-2-1-3-1-4z" class="L"></path><path d="M413 118l1 1c-1 1-2 2-4 2h0v-1l3-2z" class="X"></path><path d="M221 188v1h1c-1 1-1 3-2 4h-1c0-2 1-4 2-5z" class="L"></path><path d="M198 285l2-2 1 5h0-1 0c-1 0-1-2-2-3z" class="V"></path><path d="M237 307h1c1 1 1 2 2 3v2h-1l-1-1c0-1-1-2-1-3v-1z" class="L"></path><path d="M300 369h1l1 2 1 1v1 1h-1l-2-5z" class="T"></path><path d="M352 484v-1h0 0c1 3 3 5 4 7l-1 2c0-2-1-3-2-4 0-1-1-2-1-4z" class="a"></path><path d="M224 192c0-1 1-1 2-2h0 1-1v4s0 1-1 1c-1-1-1-2-1-3z" class="C"></path><path d="M304 511l-1-10c1 3 2 7 2 10h-1z" class="L"></path><path d="M232 335c0-2 0-3-1-4h0 1s1 0 1 1c1 1 1 1 1 2v2l-1 1h-1v-2h0zm-1-150c1-2 1-4 2-5h1v1c0 3 0 3-2 5l-1-1z" class="M"></path><path d="M191 203l1-1c1 1 3 3 3 4 1 1 1 2 1 4-2-2-3-5-5-7z" class="O"></path><path d="M254 134l1 1-1 1v1h2 0v-2h1v3c-1 2-2 2-1 4h0c-1-1-1-2-2-3v-5z" class="K"></path><path d="M256 114h2v1c0 1 0 3-1 4l-1-1c-1 0-1-1-2-2l2-2z" class="C"></path><path d="M447 274h1c1 0 1 0 2-1 1 0 2-1 2-1l-1 4c-1 0-2 1-3 1-1-2-1-1-2-2 0 0 0-1 1-1z" class="N"></path><path d="M265 109c3-1 8-2 11-3-4 2-7 4-11 5v-2z" class="L"></path><path d="M246 346c1 3 3 8 3 11l-2-1-1-4v-1h1c0-1 0-1-1-2v-3z" class="K"></path><path d="M257 126c1 1 2 1 2 3l-2 6h-1 0v-3c1-2 1-3 1-5h0v-1z" class="F"></path><path d="M433 312c0 4 0 9-1 13v2c0-1 0-1-1-2h0v1h0c0-4 1-7 1-11h0c1 0 1-1 1-3z" class="W"></path><path d="M298 307h0c1 4 3 8 4 12h-1v-1 1h0l-3-6c0-2 0-3-1-4l-1-2h0 1v1h1v-1z" class="X"></path><path d="M228 326v1 5h1c1 1 1 2 2 2 0 1 1 1 0 2h0 0s0-1-1-1c0-1 0-1-1-2v1h0l1 1v1c-1-1-2-2-2-4-1-1-2-2-2-3l-1-3c1 1 1 1 2 1l1-1z" class="M"></path><path d="M275 149c0-1 0-1 1-1v-1-1l2-1v3c-1 4-1 8-1 12-1-1 0-4-1-5h0c1-1 0-3 0-4h0 0c0-1 0-1-1-2z" class="H"></path><path d="M236 126l1-1v1l-1 2-5 5c0-1 0-3 1-4s2-2 4-3z" class="a"></path><path d="M302 339c1 0 2 0 3 1l3 6h-1l-1-2h0v1l-1-1h0v-2l-1 2s-1 0-1 1 1 1 1 2h1v1 2c-1-1-1-2-2-3l-1-2c1-1 2-1 2-3-1 0-1-1-2-1h0v-2z" class="F"></path><path d="M419 184h1l1 2h1v-2l3 6-2 1c0-1 0-1-1-1v2l-3-8z" class="E"></path><path d="M287 351h0c2 2 2 5 4 7-2 0-5-3-6-4h0l-2-1c1 0 1 0 1-1h0 2l1-1z" class="P"></path><path d="M287 334c1 0 1 1 3 1v-1c1 1 1 2 1 4 0 0 1 1 1 2v1h0c-1 0-1 1-2 1l-3-8z" class="O"></path><path d="M278 137s1 1 1 2l-1 9v-3c-1-1-2-2-3-2 0-1 0-1 1-1 0-1 0-1 1-1 0-2 0-2-1-3 1-1 1-1 2-1z" class="P"></path><path d="M234 111h9l1 1c-2 1-13 1-15 0h-1 6v-1z" class="K"></path><path d="M285 340c1 1 0 3 1 4 0 2 1 4 1 6v1h0c-1-1-2-3-3-4l-2-2c1-1 1-1 3-1v-3-1z" class="E"></path><path d="M418 178s1 2 2 2h0v1h2c0 1-1 1 0 2v1 2h-1l-1-2h-1c-1-1-2-5-2-6h1z" class="F"></path><path d="M229 327l1-1c1 2 2 2 2 4v1h-1 0c1 1 1 2 1 4 0-1-1-1-1-2-1 0-1-1-1-1h-1-1v-5l1 1h1l-1-1z" class="a"></path><path d="M197 280c0-2-1-2-2-3v-2c1 1 2 1 3 1l2 7-2 2h0c0-1-1-2-1-3v-2h0z" class="Q"></path><path d="M356 490c1 3 2 5 3 9-1-1-2-2-2-3-1-1-1-2-2-3 0-1-1-2-2-4-1-1-2-3-2-4l1-1c0 2 1 3 1 4 1 1 2 2 2 4l1-2z" class="L"></path><path d="M239 344v-1h-1s0 2 1 2c0 1 1 2 1 3l1 2c1 1 0 2 0 4h0v1-2h0c-1-1-1-3-1-3 0-1 0-1-1-1 0-1 0-2-1-2v-1-1c-1-1-1-1 0-1v-1l-1-1-1 1v-2h0c0-1 0-1 1-2 1 2 2 3 2 5z" class="K"></path><path d="M303 373l1 1c1 3 2 8 3 11v1l-2-1-3-11h1v-1z" class="Q"></path><path d="M284 356c0-1 0-1 1-2 1 1 4 4 6 4 0 0 1 0 1 1s0 2 1 3l-4-2-5-4z" class="L"></path><path d="M297 360h1c1 0 1 1 1 2 1 1 1 2 2 3 1 2 1 4 1 6l-1-2h-1c-2-2-2-6-3-9z" class="V"></path><path d="M446 282l2-2c0 3-1 5-2 8h0c-1 0-2 1-3 1h-1v-1c1-1 1-2 2-3 0-1 1-2 2-3z" class="C"></path><path d="M333 519h0c0 1 1 1 1 2 1 0 0 0 1 1l1-1c2 2 2 7 3 9h0c-2-1-6-6-6-8v-3z" class="W"></path><path d="M292 295v-2h1c1 0 0 1 0 2 1 0 1 0 1 1s1 2 2 3c0 2 0 3 1 4 0 1 1 2 1 4v1h-1v-1-1h0v-1l-1-1h0l-1-1v-1l-2-2c-1-2-1-3-1-5z" class="K"></path><path d="M307 385c2 4 3 9 5 13h0l-1-2h-1c0 1 1 2 1 2l-1 1-5-14 2 1v-1z" class="P"></path><path d="M284 347c1 1 2 3 3 4l-1 1h-2 0c0 1 0 1-1 1l-2-2c-1 0-1-1-2-1 0-1 1-1 1-2l2 1 2-2z" class="H"></path><path d="M299 334c1 1 2 3 3 5v2h0c1 0 1 1 2 1 0 2-1 2-2 3 0-1 0-2-1-2 0-1 0-2-1-3s-1-2-2-3c1-1 0-1 0-3h1z" class="E"></path><path d="M496 103c4 0 8 0 11-1h1c-2 2-4 2-6 3-1 1-1 2-2 2-1-1-1-1-1-2s-2-1-3-2h0z" class="J"></path><path d="M423 312l1 2h0c0-1 1-1 1-1l-1 11v1l-2 2v-1l1-14zm32-29l2-11c1-2 1-4 2-6 0 5-1 10-2 15-1 1-2 1-2 2z" class="H"></path><path d="M425 142l1 3-2 3 2 4v2l1 1c1 2 1 4 1 5l1 3c1 2 1 4 2 6l-1 1v-1h0c0-1-1-2-1-3v-1s0-1-1-2v-1-1h0l-1-2c0-1 0-3-1-4s-1-3-2-5c0-1-1-1-1-2h0c0-1-1-2-1-2v-1c2 0 2-1 3-3z" class="C"></path><path d="M418 129c-1 0-2-1-2-2h0c-1-1-2-1-2-2s1-1 2-1 2 1 3 2c0 0 0 1 1 1 1-1 1-1 1-2 0 1 1 1 1 2l-1 1-2 3-1-1h0v-1z" class="L"></path><path d="M418 130l1-1c0-1 1-1 2-1l-2 3-1-1zm3-5c-1-1-2-1-3-3h0 1c0-1 0-1 1-1v-1h3v1l2 2h-1l-1 2h0v1l-1 1c0-1-1-1-1-2z" class="C"></path><path d="M423 125c-1-1-1-1-2-1 0-1 0-1 1-2h1l1 1-1 2z" class="W"></path><path d="M211 169h0l1 2h1c1 1 1 1 1 2s1 2 2 2c0 1 0 1 1 2h0v1c0-1 0-2 1-2h0v3c1 0 2 1 2 1 0 3-2 4-3 7h0v-1l2-3v-1h-2c0 2 0 3-1 5v-2-2-2-1c1 0 1-1 2-1v-1l-1 1v-1c-1 0-1-1-2-2s-1-3-2-4-2-2-2-3z" class="C"></path><path d="M412 162c1 0 1 1 2 2 0 1 1 2 1 3 1 1 1 2 1 4l2 7h-1c-1-2-2-4-2-6-1-3-2-7-3-10z" class="D"></path><path d="M237 355h0v-3h0c1 0 2 0 2 1s0 2 1 3l1 1v1 1c1 1 1 2 1 3h0-2 0c0-1-1-1-1-1 0-1 0-2-1-3 0-1-1-2-1-3z" class="W"></path><path d="M284 334c0 2 1 4 1 6v1 3c-2 0-2 0-3 1v-1l-2-4v-1h1 0 2c0-1 0-1 1-1v-4z" class="O"></path><path d="M281 339h0 2l1 1v1h-1l-2-2z" class="H"></path><path d="M190 170l-2 1-1-1h2c-1-2-2-3-3-5l-1-1v-1c-1-1-1-2-2-2v-1l-3-3v-1h0v2l-4-4c-1-1-1-1-1-2s1-1 1-2c1 2 3 3 4 5 0 1 1 2 2 3h1v1l7 11z" class="F"></path><path d="M276 151h0 0c0 1 1 3 0 4h0c1 1 0 4 1 5l-2 10h0v-3c-1-1-1-1-1-2v-3c1-1 0-2 1-4v-4c0-1 0-2 1-3z" class="U"></path><path d="M243 120c3-2 5-4 7-6 3-2 6-2 9-3 2-1 4-1 6-2v2c-7 2-13 2-19 8h0c-1 0-2 1-2 1h-1z" class="C"></path><path d="M469 240h1c0 2-1 2-1 4l1 1-7 11c0-2 1-4 2-6l4-10z" class="S"></path><path d="M277 407h0c1 1 2 3 3 4 4 8 9 16 12 24l-2-3c-2-2-13-24-13-25z" class="X"></path><path d="M422 146v-1s-1-1 0-1v-1l-1-1c0-2-2-3-2-4-2-3-4-6-7-9h1c2 1 3 2 4 4l3 3c1 1 2 3 3 5l1 1-1 1h0c-1 1-1 1-1 2h0v1z" class="K"></path><path d="M423 143c-2-3-4-5-5-8h0l2 1c1 1 2 3 3 5l1 1-1 1z" class="F"></path><path d="M289 262c1 1 1 4 2 6 1 5 3 11 3 17v-2l-3-3c0-1-1-4-1-6v-3c1-3-2-6-1-9z" class="K"></path><path d="M176 165c-1-1-1-2-2-2l1-1 6 4c0 1 0 0 1 1s3 2 4 3c3 1 5 4 7 5 1 1 1 2 2 3s1 1 1 2c1 0 1 1 1 1l2 2v2c-1-1-2-2-2-3-2-3-6-8-9-10-1-1-2-2-4-3h0-1c0-1-1-1-2-1-1-1-1-1-2-1 0-1-1-1-1-1-1 0-2-1-2-1z" class="E"></path><path d="M354 450c1 1 1 1 1 2l3 9c1 1 2 3 2 4h1 3v1h0l-1 1v1 1c1 1 1 1 3 1-1 1-2 1-3 1l-1 1h0c-3-8-6-15-8-22z" class="U"></path><path d="M363 467c-1 0-1 0-1-1v-1h1l1 1-1 1z" class="O"></path><path d="M183 110h-4c-6 0-10 1-14 4-2 1-3 2-5 2l2-2c2-2 5-4 8-5 5-1 11 0 15 0v1h-2z" class="N"></path><path d="M233 376l5 16-2 1c0-1-2-2-2-3v-1-1l-1-1 1-5v-1c-1-1 0-1-1-1-1-2 0-2 0-4z" class="Q"></path><path d="M428 313h0 1c1 0 1-1 1-2h1s0 1 1 2l1-1c0 2 0 3-1 3h0c-3 3-1 7-3 9l-1-1v-1 1c-1 2-2 4-2 6h0v-2h-1 0v-2h0l1 1h0c0-1 0-1 1-1v-6c1 0 0 1 0 1l2 2v-1-4h1c0-1-1-2-1-3l-2 2v-1l-1-1h2v-1z" class="L"></path><path d="M291 286c-1-2-3-5-2-7 1 1 1 2 2 2v-1l3 3v2l1 2v1c-1-1-1-1-1-2 0 0 0 1-1 1l-1-1c0 1 0 1 1 2s1 2 1 4h-1 0l-2-3-1-2 2 2h0c0-1 0-3-1-3z" class="C"></path><path d="M237 159l1-1 1 1 2 11c0 3 0 5 1 8l1 2h0c-1 0-1 0-1-1l-1 3-4-23z" class="P"></path><path d="M281 185h0c1 1 1 2 1 3v2c1 1 0 3 1 5v1l1 1h1c1 0 1 1 2 1 1 1 2 2 2 3h-1c-1-1-1-2-2-3h-2l2 2-1 1c1 2 1 3 1 4-1 1-1 1-2 1h-1l-2-21z" class="B"></path><path d="M415 139c2 1 3 3 5 5 0 1 1 2 2 3 0 1 0 1-1 2l1 1v-1h1 0l2 5v1l1 1v1c1 1 1 1 1 2h0c0 1 0 2 1 3h0v1 1l2 6c-1-1-1-2-2-3 0 0 0-2-1-3-1-2-1-5-2-7v-2l-1-1v-2c-1 0-1-1-1-2h-1l-3 5-1-1 2-5h0l1-1c0-2-2-3-3-5-1-1-2-2-3-4z" class="X"></path><path d="M411 150l1 1c1 1 1 1 1 2 1 1 1 2 1 3s0 1 1 2c1 0 1 0 2-1v-2l1-1 1 1-2 3v3 6c1 1 1 1 1 2s-1 1-2 2c0-2 0-3-1-4 0-1-1-2-1-3-1-1-1-2-2-2v-1c-1 0-1-1-1-2h1c0 1 1 1 1 2h0l2 2v1c0 1 0 1 1 1v1h0v1-2-4h0v-1-1-1c0 1 0 1-1 1h0-1c-1-1-1-3-1-4-1-2-1-3-2-5z" class="K"></path><path d="M286 200l-2-2h2c1 1 1 2 2 3h1v4 1c-1 1-2 0-3 1h0c0 1 1 1 1 2v1h-1s-1 0-1 1c-1 1 0 2 0 3h-1l-1-8h1c1 0 1 0 2-1 0-1 0-2-1-4l1-1z" class="U"></path><path d="M286 200l2 2c-1 1-1 2-1 3h-1c0-1 0-2-1-4l1-1z" class="T"></path><path d="M282 121c0-1 1-2 2-3v1c0 3-1 8-2 11-1-1-1-2-2-3h-1v1 1s1 1 0 2-1 0-2 0l-1-1h0c1-1 1-2 2-3 1 0 1 0 1-1 1-1 1-2 2-3h0c0-1 1-1 1-2z" class="M"></path><path d="M438 294l1 2 1 1 1-1c-1 3-2 5-2 7-1 2-1 4-1 6 0 1-1 1-1 2-1-4-1-8 0-11 0-2 0-4 1-6z" class="L"></path><path d="M439 296l1 1 1-1c-1 3-2 5-2 7 0-1 0-2-1-3v-2c0-1 0-1 1-2z" class="X"></path><path d="M425 313l2-11 1 8v1l1 1h0c1-1 0-1 1-2 0 0 1-1 1-2 1-1 0-2 0-3l1-2c1 3 1 6 1 9l-1 1c-1-1-1-2-1-2h-1c0 1 0 2-1 2h-1 0c0-1 0-2-1-3h0v-2h0v-2c0 2 0 3-1 4v2 1 1c-1 1-1 2-1 2v1 1 1 2 2l-1 1 1-11z" class="F"></path><path d="M175 170v-1c1 1 1 2 2 3 0 3 4 7 6 9v2 2c1 1 2 2 2 4 1 2 2 4 3 5v1c-3-4-5-9-8-13l-5-12z" class="O"></path><path d="M175 170c-2-1-3-5-4-7l-4-8c2 2 4 5 7 7h1l-1 1c1 0 1 1 2 2 0 1 1 2 2 4h-1c-1-2-2-4-3-5v-1c-1 3 3 7 4 10h0c-1 0-1-1-1-1-1-1-1-2-2-3v1z" class="C"></path><path d="M474 221s0 1 1 2l-5 14h0 0c-1-1-1 0-1-1l1-1v-1l1-1v-2c1-1 1-2 1-2 0-1 0-2 1-3v-1c-1 1-1 3-2 4 0 0 0 1-1 2 0 1-2 3-1 4h0v1c-1 1 0 1-1 1l-1 3-3 3c1 0 1-1 1-1 1-2 1-3 1-6 3-5 6-10 8-15z" class="K"></path><path d="M277 131c1 0 1 1 2 0s0-2 0-2v-1-1h1c1 1 1 2 2 3l-3 9c0-1-1-2-1-2-1 0-1 0-2 1l-1-1h0v-1-2c1-1 1-1 0-2l1-2 1 1z" class="d"></path><path d="M276 130l1 1c0 1 0 3-1 4l2 2h0c-1 0-1 0-2 1l-1-1h0v-1-2c1-1 1-1 0-2l1-2z" class="D"></path><path d="M243 354c1 0 1 1 1 1 1 1 1 2 2 3h1v-2l2 1c0 3 4 10 3 11h-2v1c0-1-1-2-1-3-1-2-2-5-3-6-2-2-2-4-3-6z" class="C"></path><path d="M466 227c2-2 4-4 5-7 1-1 2-1 3-2h0v3c-2 5-5 10-8 15h0v-3c1-2 2-4 3-5v-1-2h0l-3 2h0z" class="T"></path><path d="M323 591v1 2c1 4 1 9 3 12l1 1c1 1 0 2 0 2v1c0 2 1 4 0 6h0c-2-1-2-6-3-9l-2-13v-3h1z" class="K"></path><path d="M484 104l1-1h4 0s1 0 1-1c1 0 6 0 6 1h0c1 1 3 1 3 2s0 1 1 2c1 0 1-1 2-2l1 1-6 3h-3 0 1v-2h0c-1-2-3-3-5-3s-3-1-6 0z" class="X"></path><path d="M364 466v2h0 2v-1h0l-2-4c0-1-1-1-1-2s0-2-1-2c0-1 0-1-1-1v-1h0c-1-1-2-2-2-3h0c-1 0-1 0-1-1v-1c-1 0-1 0-1-1l-1-1v-1l9 12c0 2 1 7 3 7l-3 9c-1-1-2-4-3-5l1-1c1 0 2 0 3-1-2 0-2 0-3-1v-1-1l1-1h0z" class="J"></path><path d="M363 468c1 1 2 1 3 1v1c-2 0-2 0-3-1v-1z" class="I"></path><path d="M329 512h0c-1-4-2-7-4-10l-6-12c0-1-1-3-1-4h0l18 35-1 1c-1-1 0-1-1-1 0-1-1-1-1-2h0l-3-3-1-1c0-1 1-2 0-3z" class="X"></path><path d="M275 349c0-1 1-1 1-1l1 1 2 1c1 0 1 1 2 1l2 2 2 1h0c-1 1-1 1-1 2l5 4-1 1-3-2c-1-1-3-2-4-3l-1 1h0c-1-1-1-2-1-2 0-2-1-2-1-3-2-1-2-2-3-3z" class="V"></path><path d="M281 351l2 2 2 1h0c-1 1-1 1-1 2l-4-2v-2l1-1z" class="a"></path><path d="M275 349c0-1 1-1 1-1l1 1 2 1c1 0 1 1 2 1l-1 1v2l-2-2c-2-1-2-2-3-3z" class="M"></path><path d="M228 112h-10c-4 0-8 0-12-1 3-1 6-2 9-2 6 0 13 2 19 2v1h-6z" class="Y"></path><path d="M238 392l5 14c-1 1-1 1-2 1-1-1-1-2-2-3 0-1 1-1 0-2 0-1-1-1-1-2-1 0-1 0-1-1s-1-1-2-1v-2l1-1v-1-1l2-1z" class="V"></path><path d="M310 399l1-1s-1-1-1-2h1l1 2h0l9 22c0 1 0 1-1 1l-10-22zm129-115h0c0 2 1 2 2 4h1 0v1h1c1 0 2-1 3-1-1 2-1 3-2 4-1 2-2 3-3 4l-1 1-1-1-1-2 1-5c-1-2 0-3 0-5z" class="E"></path><path d="M443 289c1 0 2-1 3-1-1 2-1 3-2 4h-2-1l2-3z" class="B"></path><path d="M439 284h0c0 2 1 2 2 4h1c-1 1-2 1-2 2h0v2h-1c-1-1-1-2 0-3-1-2 0-3 0-5z" class="b"></path><path d="M269 151c1-1 1-2 1-3 1 0 1 0 1 1 1 0 1 0 2 1v-1-1h0l2 1c1 1 1 1 1 2-1 1-1 2-1 3v4c-1 2 0 3-1 4l-3-4v-1h1c0-1-1-2-1-3-1-1-1-2-2-3z" class="J"></path><path d="M241 193l2 5h-1v4h0v1c1 1 0 4 0 6 1 2 1 4 1 5v4l1 1v3l1 12c-1-1-1-2-1-2l-1-1c-1-2-1-4-1-6 0 0 0-1 1-2h0c0-1 0-3-1-5v-2-5l-1-2h0c-1-1-1-1-1-2h0l1 1v-2-4h0v-3h-1 0c0-1 1-1 1-1v-1-4zm-18 0v-1h-1l1-1v-1-1c0-1 1-1 1-2v2 2h-1l1 1c0 1 0 2 1 3 1 0 1-1 1-1l1 1 1-2-2 7v7l-1 1c-1-1-2-3-2-5v-10z" class="L"></path><path d="M424 325c0 3-1 5-2 8-1 11-3 22-6 33 0-1 0-3 1-4 0-5 1-10 2-15l3-21v1l2-2z" class="d"></path><path d="M299 385c0 1 1 1 1 2 0 0 1 1 1 2h0 0l1-1s1 0 1 1l1-1c1 1 1 5 1 6 2 4 4 9 6 14h0c-1-1-2-1-2-2-2-5-5-9-8-14l-3-6 1-1z" class="M"></path><path d="M301 389h0l1-1s1 0 1 1l1-1c1 1 1 5 1 6l-4-5z" class="C"></path><path d="M337 463l2 1c0 1 1 2 1 3l2 4 1 1c0 1 0 2 1 3 0-1-1-2-1-3l-1-1v-2h0 1v-1c2 3 3 5 4 8h0l1 1v1h0l1 1v2h0v1c1 1 1 1 0 2l2 1v1h-1v-1c-1-1-1 0-1-1v-1h-1 0 0-1v-1l-1-1v-1c-1-2-2-3-3-5l-6-12z" class="L"></path><path d="M343 471h1c2 3 4 7 4 11h-1c0-1 0-2-1-3-1-3-2-6-3-8z" class="F"></path><path d="M418 169h1 0c0 2 0 2 2 3h1v2 1c1 1 1 2 2 3l-1 1v3h0-1v1c-1-1 0-1 0-2h-2v-1h0c-1 0-2-2-2-2l-2-7c1-1 2-1 2-2z" class="C"></path><path d="M419 169h0c0 2 0 2 2 3h1v2 1c1 1 1 2 2 3l-1 1v3h0-1v1c-1-1 0-1 0-2-1-2-2-6-3-8 0-1-1-2-1-2 0-1 1-2 1-2z" class="b"></path><path d="M219 286c1 1 2 3 3 5v3h1c1 0 2 2 2 3s1 1 1 2 1 2 1 4c1 2 2 4 4 6 0 1-1 2-1 3l1 1c-1 0-1-1-1-2h0c-1 0-1 0-1-1h-1c-2-4-5-10-6-15l-1-2v-1l-1-2h-1 0v-1c-1-1-1-1-1-2h0l1-1z" class="K"></path><path d="M271 337l2 1c2 1 3 3 4 4l1 2h0l2 2 2-2v1l2 2-2 2-2-1c0 1-1 1-1 2l-2-1-1-1s0-1-1-1c0-1-1-2-2-3-1-2-1-5-2-7z" class="J"></path><path d="M282 344v1l2 2-2 2-2-1c0 1-1 1-1 2l-2-1c1 0 2-1 3-1h0c-1-1-1 0-2 0v-2l-1-1 1-1 2 2 2-2z" class="U"></path><path d="M231 337l1 2h0v1c0 1 2 3 2 4v3h1l1 1h1 1c1 1 1 1 1 2h-1v-1h-1c-1 0-1 1-1 2h1c1 0 1 0 2 2h0c0 1 1 2 1 3h0c-1-1-1-2-1-3s-1-1-2-1h0v3h0-1v1c0 1 0 1-1 1h-1c-1-2-1-3-2-4l-3-3h1l2 2c1 0 1-1 2-1h1c0-1 0-1 1-1h0l-2-2v2h-1l-2-1c0-1 0-1-1-1-1-1-1-2-1-2h1v-2c-1 0-1 0-1-1l-1-1c0-1 0-1-1-2l3 3v1c1 1 1 3 2 4v1l1 1v-1-2l1-1c0-1-2-5-3-7v-2z" class="K"></path><path d="M230 273l1-1c2 2 3 4 4 7l7 9c1 1 2 3 3 4l1 2h0c-1-1-2-1-2-2-1 1 0 1-2 1 0 1 1 2 1 2 1 1 1 2 1 3-1-1-2-3-3-4l-5-11c-2-4-4-7-6-10z" class="I"></path><path d="M273 120h1l1 1 2 2c0-1 0-1 1-2h1l-1 1v1c0 1 1 1 1 1l1-1v-1-1h2c0 1-1 1-1 2h0c-1 1-1 2-2 3 0 1 0 1-1 1-1 1-1 2-2 3h0l-1 2h0l-1-1c-1 0-1-1-2-1 0-1-1-2-1-3 1-2 2-4 3-5h0v-1l-1-1z" class="F"></path><path d="M273 120h1l1 1 2 2v1h0c-1 1-1 1-1 2l-1 1h-1l1 2h0-3v1c0-1-1-2-1-3 1-2 2-4 3-5h0v-1l-1-1z" class="Y"></path><path d="M185 109c4 0 9 0 13 1 2 0 6 0 8 1 1 0 2 1 3 2l6 3c3 4 6 7 9 11l-1 1c-1-2-3-4-4-6-7-7-14-11-24-11-4-1-9 0-12-1h2v-1z" class="E"></path><path d="M441 275v2h1v-1l1 1h3l1-1h-1v-1c1 1 1 0 2 2l-1 1s0 1-1 1v3c-1 1-2 2-2 3-1 1-1 2-2 3h0-1c-1-2-2-2-2-4v-2l2-7z" class="B"></path><path d="M443 279h1v2h1s1-1 1-2v3c-1 1-2 2-2 3-1-1-1-1-1-2s0-1-1-2c0-1 1-1 1-2z" class="H"></path><path d="M446 275c1 1 1 0 2 2l-1 1s0 1-1 1c0 1-1 2-1 2h-1v-2h-1c0-1-1-1-1-2v-1l1 1h3l1-1h-1v-1z" class="U"></path><path d="M439 282l1 1h1 2c0 1 0 1 1 2-1 1-1 2-2 3h0-1c-1-2-2-2-2-4v-2z" class="D"></path><path d="M397 416h0l1 1c-1 0-1 1-1 2-1 1-1 2-2 3v2s0 1-1 1v1 1l-1 1c0 1-1 2-1 3-1 2-1 3-2 4v1c0 1 0 1-1 2l-1 3v2s-1 0-1 1l-1 2c0 2-1 3-1 5-1 5-4 11-6 17l-1-1 6-18 1-2 1-3 2-5v-1l1-1c0-1 0-2 1-3v-2c1-1 1-2 2-3v-1-1l1-1c0-1 0-2 1-3v-1c1-1 1-3 2-4h0c0-1 0-1 1-2z" class="X"></path><path d="M319 523h0l14 21 11 17c1 1 3 3 3 5-1-1-3-4-4-6-4-5-8-9-11-14-1-1-1-3-2-4l-9-15c-1-1-2-2-2-4z" class="D"></path><path d="M413 129l1-1c1 1 1 2 2 3 1 0 1-1 2-2v1h0l1 1h1l1-1 1 1v1l3 2h0l2-2 1-1h0l1 1 1-1h0c-1 2-2 4-3 5-2 2-2 4-3 6l-1-1c-1-2-2-4-3-5l-3-3c-1-2-2-3-4-4z" class="W"></path><path d="M420 131l1-1 1 1v1l3 2c0 1 0 2-1 3l-1-1c-1-2-1-3-3-5h0z" class="F"></path><path d="M456 258h0c0 1 0 1 1 2h0 1c-1 3-2 6-4 9 0 1-1 2-2 3 0 0-1 1-2 1-1 1-1 1-2 1h-1l1-1h0c1-1 1-2 1-3v-1l1-4v-1-1h2c2-1 3-3 4-5z" class="J"></path><path d="M321 420c1 2 4 6 4 7h-1c0 2 1 4 2 5l5 12c2 3 2 5 3 8 1 2 3 4 3 6-3-4-4-7-6-12-2-3-4-6-5-9-2-4-4-8-5-13 0-1-1-2-1-3 1 0 1 0 1-1z" class="Y"></path><path d="M313 549v-1l1 1 4 16c1 5 1 11 4 16 1 2 4 4 5 6l-5-4 1 8h-1l-9-42z" class="T"></path><defs><linearGradient id="F" x1="427.51" y1="137.041" x2="429.779" y2="138.198" xlink:href="#B"><stop offset="0" stop-color="#2d0b0b"></stop><stop offset="1" stop-color="#231512"></stop></linearGradient></defs><path fill="url(#F)" d="M432 129c1-1 1-1 2-1v-1h2l-1 2c-1 1-1 2-2 3v1l1-1s0-1 1-1v2h-1l-2 2s0 1-1 2c-2 2-4 5-5 8l-1-3c-1 2-1 3-3 3h0c0-1 0-1 1-2h0l1-1c1-2 1-4 3-6 1-1 2-3 3-5l2-2z"></path><path d="M427 136l1 1c-1 2-2 3-3 4v1c-1 2-1 3-3 3h0c0-1 0-1 1-2h0l1-1c1-2 1-4 3-6z" class="E"></path><path d="M432 129c1-1 1-1 2-1v-1h2l-1 2c-3 2-5 6-7 8l-1-1c1-1 2-3 3-5l2-2z" class="F"></path><path d="M280 323l2 5h0c0 2 2 4 2 6v4c-1 0-1 0-1 1h-2 0-1v1l-1-2c-1-1-2-4-1-5v-1h-1l-1-1 1-1 1-1c1-2 1-4 2-6z" class="P"></path><path d="M448 265h2l-1 4v1c0 1 0 2-1 3h0l-1 1c-1 0-1 1-1 1v1h1l-1 1h-3l-1-1v1h-1v-2-7h2l1 1h1v-1-1c1 0 2-1 2-2h1z" class="Q"></path><path d="M448 265h2l-1 4-1-1c-1-1 0-1 0-3z" class="u"></path><path d="M441 268h2l1 1h1 1c0 1-1 3-1 4l-2 4-1-1v1h-1v-2-7z" class="G"></path><path d="M442 276c1 0 2-1 2-2 0 0-1-1 0-2h1v1l-2 4-1-1z" class="Z"></path><path d="M241 197l-2-1-2-2c-1-2 0-3-1-5h1l-1-5h0c1-3-1-7-2-10 0-1 0-3 1-4v1 1-1-2l6 24v4z" class="Y"></path><path d="M270 138l3-3 2 2h0l1 1c1 1 1 1 1 3-1 0-1 0-1 1-1 0-1 0-1 1 1 0 2 1 3 2l-2 1v1 1c-1 0-1 0-1 1l-2-1h0v1 1c-1-1-1-1-2-1 0-1 0-1-1-1 0 1 0 2-1 3v-2c1-1 1-1 1-2l1-1v-2c0-1 0-2-1-3v-3h0z" class="h"></path><path d="M270 138l3-3 2 2h0l1 1c1 1 1 1 1 3-1 0-1 0-1 1-1 0-1 0-1 1v-1c-1 0-2 0-2-1s0-1 1-2c0 0-1 0-1-1-1 1-1 2-1 3-1 1-1 2-1 3 0-1 0-2-1-3v-3h0z" class="N"></path><path d="M270 138l3-3 2 2h0l-2 1-2-1-1 1h0z" class="H"></path><path d="M232 129v-2l-1-1c0-1 0-2 1-4 1 0 1-1 1-1h1 1l2 2h1c1-1 0-2 0-3v-1c0-1 0-1-1-2v-2h1c1 0 1 1 1 1v1h1c0 1 1 2 1 3h0v1l2-1h1s1-1 2-1h0l-3 3h-1-1 0c0 1-1 2-1 3h0 1v2c-1 0-2 1-2 2 0-1 0-1-1-1h0l-1-1-1 1 1-2v-1l-1 1c-2 1-3 2-4 3z" class="K"></path><path d="M240 125h1v2c-1 0-2 1-2 2 0-1 0-1-1-1 0-1 1-2 2-3z" class="C"></path><path d="M236 126l-1-1c0-1 0-2-1-3h0c1 0 2 0 2 1v1c1 0 2 0 3-1s0-3-1-4v-1h1c1 1 1 2 2 4-1 2-3 2-4 4v-1l-1 1z" class="F"></path><path d="M271 118c4-5 9-7 16-8-2 2-5 5-6 8v1h0c-1 0-1 0-2 1v2-1h-1c-1 1-1 1-1 2l-2-2-1-1h-1l-1 1 5-5-1-1-1 1c-1 1-3 2-3 3l-1-1z" class="W"></path><path d="M277 116l1-1h0l1 1-1 1c1 0 1 0 2 1 0 1-2 2-2 3-1 1-1 1-1 2l-2-2-1-1h-1l-1 1 5-5z" class="E"></path><path d="M248 120l2-2c2 3 4 6 7 8v1h0c0 2 0 3-1 5v3h0v2h0-2v-1l1-1-1-1c0-2 0-2-1-3l-2-2h0l-1-1h-1v-1-2h1c1 0 1-1 1-2l-1-1h0v-3l-2 1z" class="L"></path><path d="M251 129c0-2 1-3 2-5h1c0 1 1 2 0 2 0 1-1 2-2 3h-1z" class="W"></path><path d="M423 121l3 1 5 5c-1 2-1 2-2 2h2c0 1-1 1-1 2l-1 1-1-1h0l-1 1-2 2h0l-3-2v-1l-1-1-1 1h-1l2-3 1-1 1-1v-1h0l1-2h1l-2-2z" class="K"></path><path d="M427 132c-1-1-1-1-1-2h0l2 1-1 1z" class="C"></path><path d="M285 359l3 2 1-1 4 2 2 3c1 3 2 7 3 10h0c-1 0-2-1-3-1l-2-3h-1c-1-1-2-2-3-4 0-1 0-1-1-2l-2-3c-1-1-1-2-1-3z" class="T"></path><g class="G"><path d="M288 365c1 0 1-1 2-1l1 1c-1 1-1 2-2 2 0-1 0-1-1-2z"></path><path d="M285 359l3 2 1-1 4 2 2 3c-1 0-2 0-3-1h-1c-2-1-4-2-5-2-1-1-1-2-1-3z"></path></g><path d="M254 230l1-1h0c1 0 1-1 1-1 1 1 1 1 1 2s1 1 1 1l2 4h0l-1 1c1 2 4 10 4 10h-1v1c-1 0-1 1-2 1 0 1 0 1 1 2v3 1h0c-1 0-2-5-2-5l-5-19z" class="B"></path><path d="M300 340c1 1 1 2 1 3 1 0 1 1 1 2l1 2c1 1 1 2 2 3 0 1 0 1 1 2h0v2h0v1 2c0 1 1 1 1 2l-1 2h-2v-1c-1-1-1-1-1-2l-1-1c-1-1-1-2-1-3l-1-1c0-1-1-3-1-4l1-1c-1 0-1-1-2-1l1-1v1h1c-1-1-1-2-1-2l-1-1h1v-1l1-1v-2z" class="a"></path><path d="M300 340c1 1 1 2 1 3 1 0 1 1 1 2l1 2v1h-1v-1c-2-1-2-2-3-3v-1l1-1v-2z" class="B"></path><path d="M271 330h1c-1-1-1-2-1-3v-1h0v-1l2-1c0 1 1 3 1 4l1 1h0c1 0 1 0 2 1l-1 1 1 1h1v1c-1 1 0 4 1 5l1 2 2 4-2 2-2-2h0l-1-2v-1c0-1-1-2-1-3h0c-2-1-4-4-5-5v-3z" class="T"></path><path d="M277 342v-1c0-1-1-2-1-3h0c-2-1-4-4-5-5v-3c1 2 2 3 3 4l4 5c0 1 0 1 1 1v-2l1 2 2 4-2 2-2-2h0l-1-2z" class="G"></path><path d="M295 375v-1h0c1 0 2 1 3 1h0 1c1 2 2 5 3 7s1 4 2 6l-1 1c0-1-1-1-1-1l-1 1h0 0c0-1-1-2-1-2 0-1-1-1-1-2l-1 1c-2-2-5-5-5-8h1v-3h1z" class="E"></path><path d="M294 375h1c0 1 1 2 1 2v1 1c0 2 2 4 3 6l-1 1c-2-2-5-5-5-8h1v-3z" class="B"></path><path d="M419 155l3-5h1c0 1 0 2 1 2v2l1 1c-1 0-2 1-2 1v2h1v2c-1 1-2 3-2 4l-3 5h0-1c0-1 0-1-1-2v-6-3l2-3z" class="D"></path><path d="M417 161v-3l1 1c0 1 1 1 1 1 0 1 1 2 1 4 2-2 3-4 3-6h1v2c-1 1-2 3-2 4l-3 5h0-1c0-1 0-1-1-2v-6z" class="a"></path><path d="M417 161v-3l1 1c0 1 1 1 1 1 0 1 1 2 1 4l-1 1h-1l-1-4z" class="B"></path><path d="M220 276c1 0 1 0 1 1s1 2 2 3c1 2 2 4 4 6v2l-1 1h1c1 1 1 3 2 4s2 2 3 4h-2 0v1h0c1 1 1 1 1 2h0c-1 0-1 0-1-1h-1 0l1 1h-2 0v-2h-1l-1-1h1s0-1-1-1c0-1-2-2-2-2 0-1 1-2 0-2v-1l-1-1v-1-2h1c0-2-2-3-3-4v-1c-1-1-2-3-3-5h1l1 1v-1-1z" class="L"></path><path d="M304 511h1c1 6 4 9 8 13 3 3 6 7 8 10v1l-14-15c2 5 3 11 4 17 1 2 1 4 2 6 0 1 1 2 2 2 2 2 3 6 5 8-3-2-5-4-6-7h0v3l-1-1v1c-1-5-3-10-4-15-2-8-3-15-5-23zm-55-266c-1-5-2-9-2-14-1-7-2-15-2-22l6 25 3 8h-1c-2-2-2-5-2-7-1-3-2-7-3-10 0 5 1 11 3 16 0 1-1 1-1 2s1 4 0 4l-1-1v-1z" class="B"></path><path d="M275 170h0c0 2 0 4 1 7v3c0 4 1 7 1 11l-1-1v-1c-1 0-1 1-2 1l-2-2-3-3h1v-1l-2-5c1 0 2 1 2 1v-3h0c1-1 1-1 2-1h1v-2l2-2v-2z" class="d"></path><path d="M270 184h1 1c0-2 1-3 1-5 0-1 0-1 1-2 0 3-1 8 1 10-1 1-2 1-3 1l-3-3h1v-1z" class="D"></path><path d="M273 174l2-2c0 1 0 3-1 4v1c-1 1-1 1-1 2 0 2-1 3-1 5h-1-1l-2-5c1 0 2 1 2 1v-3h0c1-1 1-1 2-1h1v-2z" class="P"></path><path d="M273 174l2-2c0 1 0 3-1 4 0 0-1 1-2 1v3l-1 1-1-1v-3h0c1-1 1-1 2-1h1v-2z" class="h"></path><path d="M436 104c2 0 3 0 5 1l1 1h2c1 1 2 2 4 2l2-2h0c1-1 2-1 2-2v1 2c1 0 1-1 1-1h2 1l1-1h0v-1c1 1 2 1 3 1h1c1 0 4 0 5-1h2v1 1c-2 1-3 2-5 2h-1l-3-1h-2c0 1 1 2 2 3l2 2h-1-3c0-1-1-1-2-1-4 0-8 0-12-1l2-1v-1c-3-2-5-3-9-4z" class="C"></path><path d="M459 107s-1 0-1-1c1-1 7 0 9-1l1 1c-2 1-3 2-5 2h-1l-3-1z" class="H"></path><path d="M445 108c1 1 2 1 3 1l2-2h1s0 1-1 2h1 1c1 0 2 0 4-1l3 2 2 2h-1-3c0-1-1-1-2-1-4 0-8 0-12-1l2-1v-1z" class="B"></path><path d="M231 185l1 1s-1 1-1 2h1c0 1 0 2-1 3h0c0 2-1 4-1 6-1 3-1 5-1 8v3 3 1c0 2 1 5 0 8l-1-6v1 3 1h-1l-1-3-1-8 1-1v-7l2-7 3-8z" class="L"></path><path d="M231 185l1 1s-1 1-1 2c-2 4-2 8-3 13l-1-2h0l-1 1 2-7c1-3 2-6 3-8z" class="E"></path><path d="M226 200l1-1h0l1 2-1 7 1 6v1 3 1h-1l-1-3-1-8 1-1v-7z" class="I"></path><path d="M226 200l1-1h0l1 2-1 7h0 0s-1-1 0-1v-2c-1 1-1 2-1 4v-2-7z" class="d"></path><path d="M235 398c1 0 2 0 2 1s0 1 1 1c0 1 1 1 1 2 1 1 0 1 0 2 1 1 1 2 2 3 1 0 1 0 2-1 1 4 3 7 4 11l-2-1s0-1-1-1l-1 1c-1 0-1-1-1-2v-1l-2 1-1-1h0c-1-1-2-1-2-3h0l-1-1h-1c0-1 1-1 0-3v-2-1c-1-1-1-1-1-2v-2h0c0-1 1-1 1-1z" class="i"></path><path d="M242 413l1-1h0c0 1 1 2 1 3l-1 1c-1 0-1-1-1-2v-1z" class="p"></path><path d="M235 398c1 0 2 0 2 1s0 1 1 1l-1 1c1 1 1 1 1 2l-1 2v1h1v1c0 1 0 1-1 2h-1-1c0-1 1-1 0-3v-2-1c-1-1-1-1-1-2v-2h0c0-1 1-1 1-1z" class="S"></path><path d="M332 453c2 3 4 7 5 10l6 12c1 2 2 3 3 5v1l1 1v1h1l1 1c0 2-1 0 0 1l1 2s0 1 1 2v1s0-1 0 0l1 2c1 1 2 3 2 4 2 5 5 11 6 16-3-2-5-8-6-12-2-5-5-10-8-15l-9-20c-2-3-5-7-6-11l1-1z" class="F"></path><path d="M270 159c0-1 0-1 1-1l3 4v3c0 1 0 1 1 2v3 2l-2 2v2h-1c-1 0-1 0-2 1h0l-4-7v-4l1-1h1l1-2 1-1v-3z" class="Q"></path><path d="M274 165c0 1 0 1 1 2v3 2l-2 2v-1h0c1-2 1-3 0-4 1-1 1-2 1-3v-1z" class="G"></path><path d="M270 159c0-1 0-1 1-1l3 4v3 1c0 1 0 2-1 3h0c-1-1-1-1-2-1-1 1-1 0-1 0v-1s1 0 1-1c1 0 1 0 1-1-2-1-2 1-3 1l-1-1 1-2 1-1v-3z" class="T"></path><path d="M270 159c0-1 0-1 1-1l3 4v3 1l-1-2c-1-1-1-2-3-2v-3z" class="V"></path><path d="M292 330l1 1c1 0 1 0 2-1l2 3h0l1 1c0 2 1 2 0 3 1 1 1 2 2 3v2l-1 1v1h-1l-1-3v2c0 1 0 1 1 1 0 1-1 2 0 3v1c1 1 1 2 1 3h1v2 1s0 1 1 1h0v2 1c0 1 1 1 1 2v2h0c1 1 1 2 1 3s0 3 1 3v6l-1-1v-1-2c-1-2 0-5-1-6v-1s0-1-1-1c0-2 0-4-1-5v-2-1c-1-1-1-2-1-3-1-1 0-1-1-2 0-1 0-1-1-1v-1c0-1 0-3-1-4l-1-3h-1c-1 0-1 0-2 1v-1c0-1-1-2-1-2 0-2 0-3-1-4l-1-4v-1l1 1 1 1h1v-1z" class="M"></path><path d="M291 338l2-1c0 1 1 2 2 3h-1c-1 0-1 0-2 1v-1c0-1-1-2-1-2z" class="b"></path><path d="M292 330l1 1c1 0 1 0 2-1l2 3h0l1 1c0 2 1 2 0 3 1 1 1 2 2 3v2l-1 1-2-3c-3-3-5-7-7-10l1 1h1v-1z" class="P"></path><path d="M293 331c1 0 1 0 2-1l2 3h0-1 0c-1 1 0 1-1 1-1-1-1-2-2-3z" class="E"></path><path d="M295 334c1 0 0 0 1-1h0 1l1 1c0 2 1 2 0 3l-3-3z" class="F"></path><path d="M262 164l2 2c0 1 1 2 2 4l4 7v3s-1-1-2-1l2 5v1h-1l-1-2v2l-1-1c-1-1-2-1-3-2v-1h-2l-1-1-1-5 1-1h1c-1-1-1-2-1-3v-1h1 0v-2c0-1-1-1-1-2h0 0c1-1 1-1 1-2z" class="Y"></path><path d="M260 175l1-1h1c1 3 3 7 6 9v2l-1-1c-1-1-2-1-3-2v-1h-2l-1-1-1-5z" class="G"></path><path d="M262 164l2 2c0 1 1 2 2 4l4 7v3s-1-1-2-1h0l-2-2-1-1c-1-1-2-3-3-5h-1v-1h1 0v-2c0-1-1-1-1-2h0 0c1-1 1-1 1-2z" class="N"></path><path d="M261 166c1 0 2 1 3 2v1 3l-2-1h-1v-1h1 0v-2c0-1-1-1-1-2h0z" class="U"></path><path d="M262 171l2 1c2 2 4 4 4 7l-2-2-1-1c-1-1-2-3-3-5z" class="P"></path><path d="M294 340h1l1 3c1 1 1 3 1 4v1c1 0 1 0 1 1 1 1 0 1 1 2 0 1 0 2 1 3v1 2c1 1 1 3 1 5 1 0 1 1 1 1v1c1 1 0 4 1 6v2l-1-1c0-2 0-4-1-6-1-1-1-2-2-3 0-1 0-2-1-2h-1l-2-5-5-13c1 0 1-1 2-1h0c1-1 1-1 2-1z" class="E"></path><path d="M292 341c1-1 1-1 2-1v3l-1 1c-1-1-1-2-1-3h0z" class="M"></path><path d="M294 340h1l1 3v1h-1l-1-1v-3z" class="O"></path><path d="M295 355c1-1 2-1 3-1h1v4c0 2 1 3 2 4v3c-1-1-1-2-2-3 0-1 0-2-1-2h-1l-2-5z" class="D"></path><path d="M426 275v1 9 2c0 1 1 1 1 1 0 1 1 2 1 3h-1c0 1 0 3 1 4h0 1v3l1 5h1c0-1 0-2 1-4v4l-1 2c0 1 1 2 0 3 0 1-1 2-1 2-1 1 0 1-1 2h0l-1-1v-1l-1-8-2 11s-1 0-1 1h0l-1-2 3-37z" class="G"></path><path d="M428 295h1v3l1 5h1c0-1 0-2 1-4v4l-1 2c0 1 1 2 0 3 0 1-1 2-1 2-1 1 0 1-1 2h0l-1-1v-1h1c0-2 0-4-1-7v-8z" class="E"></path><path d="M288 123l1 1c0-1 0-2 1-2v1c0 1 0 1-1 2v3 8c-1 1 0 5-1 7v1c1 0 1 2 0 2l1 1c-1 1-3 1-4 1-1 1-2 1-3 1 0-4 1-8 2-12 1-5 2-10 4-14z" class="J"></path><path d="M183 159l1 1c0-2-1-2-1-3l-2-2v-1c-1 0-1-1-2-1v-1-1h0l-3-5c-1-1-1-2-2-3s-3-3-2-4h0c2 2 4 4 5 6 0 1 1 1 1 2l5 7 4 6c1 1 2 3 3 5 0 1 1 2 2 3l1 1c0 1 1 2 2 3v1c1 1 1 2 2 3l2 2 5 8 3 4c3 4 6 8 7 12l1 1 4 5v1h-1 0l-1-2c-2-2-4-5-5-7 0-1 0-1-1-1v-1l-1-1h0v-1c-1-1-1-1-1-2-2-2-4-4-5-7-1-1-2-3-4-4h0c-2-1-3-4-4-6-1 0-1 0-1-1l-1-1-2-3-1-1c0-1 0-1-1-2 0-1 0-1-1-2h0v1s0 1 1 2l-7-11z" class="X"></path><path d="M216 307c2 2 3 5 4 7 0 0 0 1 1 1v2l1 1v1h1l1 1c0-1 0-1 1-1l1 2c1 2 1 4 2 5l-1 1c-1 0-1 0-2-1l1 3c0 1 1 2 2 3 0 2 1 3 2 4 0 1 1 1 1 1v2c1 2 3 6 3 7l-1 1v2 1l-1-1v-1c-1-1-1-3-2-4v-1l-3-3c0-3-2-6-3-8-3-6-5-12-7-18h0c1 1 1 2 1 3l1 1v1c1 0 1 0 2-1-1 0-1-1-1-1v-1l-1-1c-2-2-1-3-2-5-1-1-1-2-1-3z" class="F"></path><path d="M225 319l1 2c1 2 1 4 2 5l-1 1c-1 0-1 0-2-1h0c-1-2-2-4-1-6 0-1 0-1 1-1z" class="I"></path><path d="M225 326h0l1 3c0 1 1 2 2 3 0 2 1 3 2 4 0 1 1 1 1 1v2c1 2 3 6 3 7l-1 1c-2-4-4-8-6-11 0-2-2-3-2-5s-1-4-1-5h0 1 0z" class="W"></path><path d="M188 247c2 6 3 12 6 18l1 4c-1 1-1 1-3 1h0c-1 0-1-1-2-2v-3h-2l-1-1h0-2c-1 1-1 1-2 1v-2-2-1c0-1 1-1 1-2-1 0-1 0-1-1h-1l2-2s-1 0-1-1v-1-2l1-1h1 0 1v-2l2-1z" class="S"></path><path d="M194 265l1 4c-1 1-1 1-3 1h0c0-1 0-1 1-2h1v-3z" class="Q"></path><path d="M184 250h1 0 1c1 1 1 2 1 3 0 0 1 1 1 2v2c-1-1-1-1-2-1h-1l-1-1s-1 0-1-1v-1-2l1-1z" class="j"></path><path d="M182 257l2-2 1 1h1c1 0 1 0 2 1l1 1c0 1 1 2 2 3-1 0-2 0-2 2h1 0c1 0 1 1 1 2h0-1-2l-1-1h0-2c-1 1-1 1-2 1v-2-2-1c0-1 1-1 1-2-1 0-1 0-1-1h-1z" class="i"></path><path d="M244 298c0-1 0-2-1-3 0 0-1-1-1-2 2 0 1 0 2-1 0 1 1 1 2 2h0c1 1 4 6 4 6 1 1 2 1 4 2h0c1 1 2 4 4 4v4l1 3 1 1h-1l-1 1h1v1 2h-1l-4-5h0c-1 0-1 1-1 1l-9-16z" class="D"></path><path d="M250 300c1 1 2 1 4 2h0c0 1 0 2-1 3-1-1-2-2-3-5z" class="E"></path><path d="M254 302c1 1 2 4 4 4v4l-5-5c1-1 1-2 1-3z" class="b"></path><path d="M254 313v-1c0-2-2-5-3-7 2 1 3 2 4 4 1 1 2 4 3 6h1v1 2h-1l-4-5z" class="I"></path><defs><linearGradient id="G" x1="420.678" y1="261.186" x2="433.988" y2="264.18" xlink:href="#B"><stop offset="0" stop-color="#bd170d"></stop><stop offset="1" stop-color="#e31c1e"></stop></linearGradient></defs><path fill="url(#G)" d="M426 240c0-2 0-3 1-4h1c0 3-1 7 0 10 1 2 0 4 0 6s1 5 1 7v1s0 1-1 1v2 6 11c1 4 1 7 1 11h0-1c0-1-1-2-1-3 0 0-1 0-1-1v-2-9-1-35z"></path><path d="M428 263c1 2 3 3 3 4l1 1v1l1 2c0 1 0 3 1 3v1l1-1c0 2 0 3-1 5s-1 4-2 6v3c-1 1 0 4 0 5 0 2-1 4 0 6h0c-1 2-1 3-1 4h-1l-1-5v-3h-1 0c-1-1-1-3-1-4h1 1 0c0-4 0-7-1-11v-11-6z" class="H"></path><path d="M430 282c1 0 1 0 2-1l-1-1 2-1-1 4-1 1h-1v-2z" class="I"></path><path d="M430 279h1c0-1 0-2 1-3v-1c1 2 1 2 1 4h0l-2 1 1 1c-1 1-1 1-2 1v-3z" class="B"></path><path d="M429 270h0c1 1 1 2 1 3v1l2-2v3 1c-1 1-1 2-1 3h-1c-1-2-1-7-1-9z" class="Z"></path><path d="M428 263c1 2 3 3 3 4l1 1v1 3l-2 2v-1c0-1 0-2-1-3h0v-3h-1v2-6z" class="Q"></path><path d="M432 272v-3l1 2c0 1 0 3 1 3v1l1-1c0 2 0 3-1 5s-1 4-2 6v3c-1 1 0 4 0 5 0 2-1 4 0 6h0c-1 2-1 3-1 4h-1l-1-5c1 0 1 0 1-1 1 0 1-2 1-3v-1-2-3c1-2 1-3 1-5l1-4h0c0-2 0-2-1-4v-3z" class="b"></path><path d="M443 249l1-2 2-2h0v2c-1 1-1 2-1 4l-2 10v2h1l1 1h0c0 2 0 3-2 4h-2v7l-2 7v2h0v-1l-1-1h1 0v-3-1h0c0-1 0-2 1-2v-2-1-3c1 0 1 0 1-1v-2l-2-2c-1-1-2-1-2-1v1h0c0 1 0 1 1 2v1 3c-1 1-1 0-2 2h0v3c0 1-1 1-1 2v2s-1 1-1 2l-2 12v5c-1-2 0-4 0-6 0-1-1-4 0-5v-3c1-2 1-4 2-6s1-3 1-5c0-1 1-2 0-3h0l1-1h1 0c1-1 0-2 0-3l-2-5h-1c1-1 0-2 1-3l1 1h0c1 0 1 0 2-1h0c0-1 1-1 2-2l1-2c1-1 2-5 2-6z" class="a"></path><path d="M436 260c1 0 1 0 2-1v3 1h-1c-1-1-1-1-1-3h0z" class="J"></path><path d="M438 259c0-1 1-1 2-2l1-2c-1 3-1 4-3 7v-3h0z" class="H"></path><path d="M441 268l2-7v2h1l1 1h0c0 2 0 3-2 4h-2z" class="Q"></path><path d="M443 268v-2c0-1 0-2 2-2h0c0 2 0 3-2 4z" class="V"></path><defs><linearGradient id="H" x1="386.079" y1="478.511" x2="398.941" y2="468.378" xlink:href="#B"><stop offset="0" stop-color="#fbd8d7"></stop><stop offset="1" stop-color="#fbfbfc"></stop></linearGradient></defs><path fill="url(#H)" d="M393 459c2 2 2 5 2 8l1 8v5 4c0 1-1 1-2 1l-1-1-3-3s0 1-1 1-2-1-2-1c0-1 0-2 1-3-1-1-1-2-1-2l6-17z"></path><path d="M476 107l2-2c2-1 4-2 6-1 3-1 4 0 6 0s4 1 5 3h0v2h-1 0l-2 1h-2l-8 2-8 1c-2 1-4 0-6 1 0-1 0-2 1-2h0c2 0 4-2 6-3 0 0 1-1 1-2z" class="P"></path><path d="M481 106h1c0 1 1 1 2 1v2h-1-2-2 0l1-2 1-1z" class="h"></path><path d="M481 106c1 0 2-1 2-1 2 1 2 1 3 0l-1 1c1 2 1 3 1 4h-3v-1h1v-2c-1 0-2 0-2-1h-1z" class="D"></path><path d="M486 110h4 0l-8 2v-1h-5c2-1 4 0 6-1h3z" class="F"></path><path d="M476 107l2-2c2-1 4-2 6-1 3-1 4 0 6 0h-3c0 1 0 0-1 1h0c-1 1-1 1-3 0 0 0-1 1-2 1l-1 1h-4z" class="O"></path><path d="M469 112h0c2 0 4-2 6-3 0 1 0 1 1 1v1h1 5v1l-8 1c-2 1-4 0-6 1 0-1 0-2 1-2z" class="L"></path><path d="M490 104c2 0 4 1 5 3h0v2h-1 0l-2 1h-2 0-4c0-1 0-2-1-4l1-1h0c1-1 1 0 1-1h3z" class="I"></path><path d="M490 104c2 0 4 1 5 3h0v2h-1 0l-2 1h0v-1c-1 0 0-1-1-2 0-1 0-1-1-1h-1c-1-1-2-1-3-1h0c1-1 1 0 1-1h3z" class="P"></path><path d="M253 342h0c1 0 2 1 2 2 2 2 3 5 5 7 2 3 4 5 6 8 1 3 4 6 5 8l6 12h0l-2-3c-1-2-2-4-3-5 0-1 0-1-1-2h0c0-1-2-3-3-4v-1c0-1-2-2-2-3s-1-2-1-3h-1v-1h0v-1c-1-1-1-1-2-1v1 1l-1-1v1 1c1 1 1 2 1 3v1c1 1 1 3 2 4l1 1v2c1 0 1 1 1 2l1 1 2 2c0 1 1 2 2 3 0 1 1 3 1 4 1 1 1 2 1 3h0v1c-1-2-1-5-2-7-2-2-4-4-5-6-2-3-3-7-5-9h0 0c-1 2 2 6 3 8s1 3 2 5h1l-1 1v-1l-1 1c-5-10-10-20-13-31v-4h1z" class="L"></path><path d="M268 290c0 1 0 2 1 3 0 2 1 4 1 5s1 1 1 2h0c1 3 2 5 3 7l3 6v1h-1c-1-1-1-1-1-2s-1-1-1-2v2c1 0 1 1 1 1h0v1c1 1 1 2 1 3h0v1c1-1 1-2 1-3h0c1 0 1 0 1 1s1 1 1 2v1 1c1 1 1 1 1 2s0 1 1 1v1 1l1 1v2h0 0l-2-5c-1 2-1 4-2 6l-1 1c-1-1-1-1-2-1h0l-1-1c0-1-1-3-1-4v-1l-1-1v-4c1-1 1-2 1-4h0l-2 2v-4l-1-2c-1 0-1 1-2 1h0c-1-1-1-2 0-3h0 1c-1-1-1-2-1-3h0l1-1h0c0-1 0-2 1-3 0 0 0-1-1-1v-1-2c-1 0-1-1-1-2s-1-1-1-2 0-2 1-3z" class="E"></path><path d="M270 301l1 3c1 2 2 2 2 4-1 0-2 0-3-1 0 0-1-2-1-3s0-2 1-3z" class="C"></path><path d="M271 312l1-1c1 0 1 0 1 1 1 1 1 2 2 4 0 1 0 3 1 4v2l1-1v-1-2c1 0 1 0 1 1 1 1 1 1 1 2l1 2c-1 2-1 4-2 6l-1 1c-1-1-1-1-2-1h0l-1-1c0-1-1-3-1-4v-1l-1-1v-4c1-1 1-2 1-4h0l-2 2v-4z" class="d"></path><path d="M272 318l1 1v1l1-1h1c1 1 0 4 1 4h0c0 1-1 4-1 5h-1c0-1-1-3-1-4v-1l-1-1v-4z" class="N"></path><path d="M272 318l1 1v1l1-1-1 4h0l-1-1v-4z" class="D"></path><path d="M279 321l1 2c-1 2-1 4-2 6l-1 1c-1-1-1-1-2-1h0l-1-1h1c0-1 1-4 1-5h0 1 1s0-1 1-2h0z" class="G"></path><path d="M279 321l1 2c-1 2-1 4-2 6 0-1 0-1-1-2v-4h1s0-1 1-2h0z" class="N"></path><path d="M229 211l4 10c2 3 3 4 4 6h1v-4-2-1l1 1v1 1c0 1 0 1 1 2 1 0 1-1 1-2l-1-5c0 1 1 1 1 2v1 1c1 0 1 0 2 1-1 1-1 2-1 2 0 2 0 4 1 6l1 1s0 1 1 2v1c1 1 1 2 1 3l-1 1-1-1c1 2 3 4 2 6h0c-1-1-2-4-4-5-3-2-5-7-8-9-1-2-3-6-5-7-1-1-2-2-3-4v-3l1 3h1v-1-3-1l1 6c1-3 0-6 0-8v-1z" class="a"></path><path d="M244 238h0c-2-2-3-3-4-6-1-1-2-2-2-4h0c3 3 5 6 7 9h0v-2c1 1 1 2 1 3l-1 1-1-1z" class="K"></path><defs><linearGradient id="I" x1="407.699" y1="137.473" x2="417.386" y2="149.173" xlink:href="#B"><stop offset="0" stop-color="#210d0d"></stop><stop offset="1" stop-color="#3d1312"></stop></linearGradient></defs><path fill="url(#I)" d="M411 150c-1-3-4-6-6-9 1 0 1-1 2-1 0-1 1-1 1-2v-1l1-1s0-1 1-1h0l1-1h0l1-1c0 2 2 4 3 6s2 3 3 4c1 2 3 3 3 5l-1 1h0l-2 5-1 1v2c-1 1-1 1-2 1-1-1-1-1-1-2s0-2-1-3c0-1 0-1-1-2l-1-1z"></path><path d="M413 150h0c1-1 3-5 5-5v1c-2 1-3 3-4 5h0-1v-1h0zm2 1l1-1c1-1 2-3 3-3h1l-1 1c-1 2-2 3-3 4l-1 1v-1h0v-1z" class="F"></path><path d="M419 148l1 1-2 5-1 1v2c-1 1-1 1-2 1-1-1-1-1-1-2s0-2-1-3c0-1 0-1-1-2l1-1h0v1h1 0 1v1h0v1l1-1c1-1 2-2 3-4z" class="a"></path><path d="M419 148l1 1-2 5-1 1-1 2v-1c-1-1 0-3 0-4 1-1 2-2 3-4z" class="I"></path><path d="M376 508l11-32s0 1 1 2c-1 1-1 2-1 3 0 0 1 1 2 1s1-1 1-1l3 3 1 1c1 1 1 1 1 2h0c-1 1-1 2-1 3h0l-1 2h1 2c-1 1-1 1-2 1h0-1v-3s-1 0-1 1c-1 1-3 0-4 0h-1c-1 0-1 1-1 2l-3 5c0 1-1 2-1 3v2c-1 1-1 2-2 4h-2-1 0l-1 1z" class="e"></path><path d="M394 490l-2-1h0l1-2h0-1c-2 0-3 0-4-1v-4c1 0 1 1 2 1v1c0 1 1 1 2 1l1-1 1 1c1 1 1 1 1 2h0c-1 1-1 2-1 3z" class="t"></path><path d="M207 255c1 1 1 2 2 3 2 1 3 2 3 4h1v1c0 1 0 2 1 2h0c0 1 1 1 1 2v1h1l1 3 1 1v1l1 1h0l1 2v1 1l-1-1h-1c1 2 2 4 3 5v1c1 1 3 2 3 4h-1v2-1c-1 0-1-1-1-1-1-1-1-3-2-4-1 0-1 0-1-1-1-1-1-2-2-3l-1-1h0c0-1-1-1-1-2h0c-1-1-1-2-2-4h0v-1h0c-1 0-1 1 0 1v3l-1-1c-1-1-1 1-2 1 0 1 0 0-1 1v-3h-1 0l-2-2v-5h0c0-1-1-3-1-4h-1c0-1-1-3-1-4l2-1v-1h1 1v-1z" class="W"></path><path d="M223 287c-4-6-8-12-11-19-1-4-3-7-5-10v-2l2 2c2 1 3 2 3 4h1v1c0 1 0 2 1 2h0c0 1 1 1 1 2v1h1l1 3 1 1v1l1 1h0l1 2v1 1l-1-1h-1c1 2 2 4 3 5v1c1 1 3 2 3 4h-1z" class="K"></path><path d="M280 280l2 2 1 1v1s0 1 1 1c0 0 0 1 1 1l1 2c0 1 2 2 2 3l1 1 1 1s1 0 1 1l1 1c0 2 0 3 1 5l2 2v1 1c0 2 0 3 1 4v1c-1 2 1 5 1 7 1 1 1 3 1 5l1 3c1 1 2 2 2 4 1 2 3 6 3 9l1 3c-1-1-2-1-3-1-1-2-2-4-3-5-3-5-6-9-8-14 0-2-2-3-2-5h1 1c-1-1-1-2-1-3h0v-4-1-1h0v-1l3 3h1v-3-2-1h-1c-1-1-1-2-1-3v-1c-1-3-5-8-7-10s-3-5-5-7v-1z" class="F"></path><path d="M295 317c1 0 1 0 2 1l-1 2-1-1v-2z" class="K"></path><path d="M298 321l1 3c0 1 1 2 2 3l-1-1c-1 0-1 0-2-1h0c0-1-1-1-1-2s-1-2-1-2c1 0 1 0 2 1v-1z" class="C"></path><path d="M290 308c1 2 1 4 2 6l3 6-1 1-4-6h1c-1-1-1-2-1-3h0v-4z" class="P"></path><path d="M299 334c-3-5-6-9-8-14 0-2-2-3-2-5h1l4 6s0 1 1 1c0 2 1 3 2 4 1 2 5 10 7 11l-3-10c-1-1-2-2-2-3 1 1 2 2 2 4 1 2 3 6 3 9l1 3c-1-1-2-1-3-1-1-2-2-4-3-5z" class="K"></path><path d="M344 424v-2c-1-2-3-5-3-7 1 0 2-2 3-2l1 1v1c0 1 1 1 1 2v1c0 1 1 3 1 4 1 1 1 2 1 2 2 1 3 0 4 2v2 1 3l2 8c1 3 1 6 2 9h0v1l1 1c0 1 0 1 1 1v1c0 1 0 1 1 1h0c0 1 1 2 2 3h0v1c1 0 1 0 1 1 1 0 1 1 1 2s1 1 1 2l2 4h0v1h-2 0v-2-1h-3-1c0-1-1-3-2-4l-3-9c0-1 0-1-1-2s-9-23-10-26z" class="Q"></path><path d="M226 318h1 1 3c0 1 1 1 2 2 0 0 1 1 2 0 4 8 7 18 11 26v3c1 1 1 1 1 2h-1v1l1 4v2h-1c-1-1-1-2-2-3 0 0 0-1-1-1-1-1 0-2-1-2 0-1-1-1-1-2v-1h0c0-1 0-2-1-2 0-1 0-2-1-3 0-2-1-3-2-5v-1h-1v-1c-1 0-2 0-2-1v-2c0-1 0-1-1-2 0-1-1-1-1-1v-1c0-2-1-2-2-4l-1 1 1 1h-1l-1-1v-1c-1-1-1-3-2-5h0 1l-1-3z" class="a"></path><path d="M238 337s0 1 1 1v1h-1c-1-1-1-1 0-2z" class="M"></path><path d="M235 333c1 2 2 3 2 5h-1v-1c-1 0-2 0-2-1v-2l1-1z" class="K"></path><path d="M230 326v-1c1-2 0-2-1-3h0l1-1 1 1c0 1 1 1 1 2h1c-1 3-1 3 1 6h0c0 1 0 2 1 3l-1 1c0-1 0-1-1-2 0-1-1-1-1-1v-1c0-2-1-2-2-4z" class="C"></path><path d="M239 344c1 0 1 1 2 2 0 1 1 2 1 3v1h1c0 1 1 1 2 2 0-2 0-2-1-3v-1h0l1 1v1s1 1 1 2l1 4v2h-1c-1-1-1-2-2-3 0 0 0-1-1-1-1-1 0-2-1-2 0-1-1-1-1-2v-1h0c0-1 0-2-1-2 0-1 0-2-1-3z" class="F"></path><path d="M226 318h1 1 3c0 1 1 1 2 2l1 2c0 1 0 2-1 2h0-1c0-1-1-1-1-2l-1-1-1 1h0c1 1 2 1 1 3v1l-1 1 1 1h-1l-1-1v-1c-1-1-1-3-2-5h0 1l-1-3z" class="I"></path><path d="M226 321h0 1c1 1 2 4 2 6l1 1h-1l-1-1v-1c-1-1-1-3-2-5z" class="K"></path><path d="M272 279v-1h0c1 0 1 1 2 2h0c1 0 1 0 2-1h0v-1h0c1 0 2 1 3 1h0s1 1 1 2c2 2 3 5 5 7s6 7 7 10v1c0 1 0 2 1 3h1v1 2 3h-1l-3-3v1h0v1h-1l-1-3-2-2h-1c-1-1-2-2-2-3l-1 2v1h-1 0c0-1-1-2-1-4 0-1-1-2-2-4l-6-15z" class="M"></path><path d="M288 296c2 2 3 5 5 7v1l-2 1c-1-1-1-1-2-1 1-1 1-1 1-2-1-1-1-3-1-4-1 0-1-1-1-2h0z" class="B"></path><path d="M280 289l1-1c0 1 1 2 1 3l1-1 1 1h0c0 1 0 1 1 2l1 1c1 0 1 1 2 2h0c0 1 0 2 1 2 0 1 0 3 1 4 0 1 0 1-1 2l-1-2h-1l1 2-2-2-2-4-4-9z" class="H"></path><path d="M280 289l1-1c0 1 1 2 1 3l1-1 1 1h0c0 1 0 1 1 2h0c0 2 3 4 2 6h-1c0-1-1-2-2-3v1 1l-4-9z" class="N"></path><path d="M276 278h0c1 0 2 1 3 1h0s1 1 1 2c2 2 3 5 5 7 0 2 0 3 1 4v2l-1-1c-1-1-1-1-1-2h0l-1-1-1 1c0-1-1-2-1-3l-1 1c-1-1-2-2-2-3l-4-6c1 0 1 0 2-1h0v-1z" class="D"></path><path d="M276 278h0 1c0 1 1 2 2 2v1c0 1 1 1 1 2h0l-1 1-3-5h0v-1z" class="O"></path><path d="M276 279l3 5c1 2 2 3 3 4s1 2 1 2l-1 1c0-1-1-2-1-3l-1 1c-1-1-2-2-2-3l-4-6c1 0 1 0 2-1z" class="B"></path><path d="M272 279v-1h0c1 0 1 1 2 2h0l4 6c0 1 1 2 2 3l4 9 2 4h-1c-1-1-2-2-2-3l-1 2v1h-1 0c0-1-1-2-1-4 0-1-1-2-2-4l-6-15z" class="Q"></path><path d="M223 215c0 1 0 1 1 2 0 1 1 1 2 3 0 1 1 2 2 3h0l1 2h1v2c1 0 1 1 2 2h0l1 1h1c3 2 5 7 8 9 2 1 3 4 4 5l1 1v1l1 1 1-2v1l1 1c1 0 0-3 0-4s1-1 1-2c-2-5-3-11-3-16 1 3 2 7 3 10 0 2 0 5 2 7h1v1c1 2 2 5 3 7v8c1 4 4 8 4 13-1-1-2-3-3-4 0-1-1-2-2-3h-1l-33-48 1-1z" class="X"></path><path d="M251 241c0 1 1 2 1 3 1 1 1 1 2 3 0 2-1 2-2 3v4h-1c0-1-2-5-2-6v-2l1 1c1 0 0-3 0-4s1-1 1-2z" class="E"></path><path d="M252 254c1-1-1-4-1-6 1-2 1-1 3-1 0 2-1 2-2 3v4z" class="L"></path><path d="M433 226v3h1v1 4 3l1 1c1 1 1 1 0 3h0v1 1l-1 1c0 1 1 2 1 2v6l1 4 1 1v-1c0 1 0 2 1 3h0c-1 1-1 1-2 1h0l-1-1c-1 1 0 2-1 3h1l2 5c0 1 1 2 0 3h0-1l-1 1h0c1 1 0 2 0 3l-1 1v-1c-1 0-1-2-1-3l-1-2v-1l-1-1c0-1-2-2-3-4v-2c1 0 1-1 1-1v-1c0-2-1-5-1-7s1-4 0-6c-1-3 0-7 0-10 1-1 1-3 2-4h0l1 2v-4h1l1-4z" class="O"></path><path d="M430 244l2 2v2s-1 0-2-1v-3z" class="Q"></path><path d="M430 247c1 1 2 1 2 1-1 1-1 2-1 3v1l-2-1 1-4zm5 15l2 5h-1c-2-1-3-2-3-4h1l1-1z" class="T"></path><path d="M431 259l1-1 2 1v3h1l-1 1h-1l-2-4z" class="i"></path><path d="M429 259c1 2 2 5 3 7v2h2c1 1 1 0 1 1-1 1-1 2-2 2h0l-1-2v-1l-1-1c0-1-2-2-3-4v-2c1 0 1-1 1-1v-1z" class="S"></path><path d="M435 242v1l-1 1c0 1 1 2 1 2v6l-1-1v1h0l-1-1h-2c0-1 0-2 1-3v-2l1-2h0c1-2 1-2 2-2z" class="D"></path><path d="M432 246l1-2v7h-2c0-1 0-2 1-3v-2z" class="Z"></path><path d="M433 251l1 1h0v-1l1 1 1 4 1 1v-1c0 1 0 2 1 3h0c-1 1-1 1-2 1h0l-1-1c-1 1 0 2-1 3v-3l-2-1-1 1c-1-3-2-5-2-8l2 1v-1h2z" class="V"></path><path d="M433 251l1 1h0v2l-1 2h0c-1-1-2-3-2-4v-1h2z" class="h"></path><path d="M434 252v-1l1 1 1 4 1 1v-1c0 1 0 2 1 3h0c-1 1-1 1-2 1h0l-1-1c-1 1 0 2-1 3v-3l-1-3h0l1-2v-2z" class="B"></path><path d="M437 256c0 1 0 2 1 3h0c-1 1-1 1-2 1v-4l1 1v-1z" class="Z"></path><path d="M433 226v3h1v1 4 3l1 1c1 1 1 1 0 3h0v1c-1 0-1 0-2 2h0l-1 2-2-2v-1c1-3 1-6 1-9v-4h1l1-4z" class="V"></path><path d="M434 234v3l1 1c1 1 1 1 0 3h0v1c-1 0-1 0-2 2l1-10z" class="O"></path><path d="M466 227l3-2h0v2 1c-1 1-2 3-3 5v3h0c0 3 0 4-1 6 0 0 0 1-1 1 0 3-2 5-3 8-1 2-2 6-3 9h-1 0c-1-1-1-1-1-2h0c-1 2-2 4-4 5h-2v1 1h-2-1c0 1-1 2-2 2v1 1h-1l-1-1c2-1 2-2 2-4 1-1 2-3 2-4 2-4 3-7 6-10l9-16c1-2 3-4 4-7z" class="f"></path><path d="M466 233v3l-4 5-1 1 1-1c0-3 2-6 4-8z" class="i"></path><path d="M453 255c1 1 1 1 2 0h1l1 1s-1 1-1 2c-1 2-2 4-4 5h-2v1 1h-2-1s1-1 1-2c1-1 2-4 3-5 1 0 1 0 1-1l1-2z" class="u"></path><path d="M448 263v1h2v1h-2-1s1-1 1-2z" class="c"></path><path d="M466 236h0c0 3 0 4-1 6 0 0 0 1-1 1 0 3-2 5-3 8-1 2-2 6-3 9h-1 0c-1-1-1-1-1-2h0c0-1 1-2 1-2l-1-1h-1c-1 1-1 1-2 0 1-3 2-6 5-8l3-5 1-1 4-5z" class="G"></path><path d="M466 236h0c0 3 0 4-1 6 0 0 0 1-1 1 0 3-2 5-3 8v-3h0v-1c1-2 1-4 1-6l4-5z" class="P"></path><path d="M453 255c1-3 2-6 5-8h0l1 1 1 2c-1 0-1 0-2 1 0 2 0 3-1 5h0l-1-1h-1c-1 1-1 1-2 0z" class="T"></path><path d="M455 255l1-1-1-1c1 0 1 0 1-1h0l2-2v1c0 2 0 3-1 5h0l-1-1h-1z" class="S"></path><defs><linearGradient id="J" x1="419.455" y1="209.558" x2="430.419" y2="211.239" xlink:href="#B"><stop offset="0" stop-color="#650d0d"></stop><stop offset="1" stop-color="#2a1110"></stop></linearGradient></defs><path fill="url(#J)" d="M422 192v-2c1 0 1 0 1 1l2-1 1 4c2 0 2-1 3-3 0 2 0 3 1 4l1 1c0 1-1 2-1 2v1 5c0 4 0 9-1 14v4l2 2 1 1v-1-2c1 0 1-1 1-2h1l-1 6-1 4h-1v4l-1-2h0c-1 1-1 3-2 4h-1c-1 1-1 2-1 4v-4c-1-4-1-9-1-12 0-11-1-22-3-32z"></path><path d="M423 191l2-1 1 4v3 1h-1l-2-7z" class="F"></path><path d="M426 197c0 1 1 4 0 6 0 1 0 2 1 4h-1c-1-1-1-3-1-5v-2-2h1v-1z" class="Y"></path><path d="M426 203c1 0 1 1 1 2l1-1h2c0 4 0 9-1 14v-8c0-1-1-2-2-3h0c-1-2-1-3-1-4z" class="b"></path><path d="M429 191c0 2 0 3 1 4l1 1c0 1-1 2-1 2v1 5h-2l-1 1c0-1 0-2-1-2 1-2 0-5 0-6v-3c2 0 2-1 3-3z" class="H"></path><path d="M428 204c0-1 0-2-1-3h0c1-1 2-2 3-2v5h-2z" class="B"></path><path d="M429 222l2 2 1 1v-1-2c1 0 1-1 1-2h1l-1 6-1 4h-1v4l-1-2h0c-1 1-1 3-2 4h-1c-1 1-1 2-1 4v-4-4h0c1 0 1 1 1 1 2-2 2-8 2-11z" class="N"></path><path d="M234 181l1 1c0 1 1 2 1 3v1h0 0c0-1-1-2-1-2-1 1-1 1-1 2h1v1s0 1 1 1c0 1-1 1 0 1v3 1l1 2c0 2-1 3 0 5v1 1c1 1 0 2 1 4 0 1-1 0 0 1v3 1c0 2 1 4 1 5l1 2 1 5c0 1 0 2-1 2-1-1-1-1-1-2v-1-1l-1-1v1 2 4h-1c-1-2-2-3-4-6l-4-10v-3-3c0-3 0-5 1-8 0-2 1-4 1-6h0c1-1 1-2 1-3h-1c0-1 1-2 1-2 2-2 2-2 2-5z" class="Y"></path><path d="M229 205l1-2c1 1 2 2 2 3 1 3 1 7 2 10 1 2 3 4 3 6v1 2 1l-2-3-2-2-4-10v-3-3z" class="I"></path><path d="M177 172s0 1 1 1h0c-1-3-5-7-4-10v1c1 1 2 3 3 5h1c-1-2-2-3-2-4 0 0 1 1 2 1 0 0 1 0 1 1 1 0 1 0 2 1 1 0 2 0 2 1h1 0c2 1 3 2 4 3 3 2 7 7 9 10 0 1 1 2 2 3l5 7c1 1 2 3 3 4 2 2 3 3 4 5v2c-4-3-8-6-11-10l-6-5-2-2c-1 0-2-1-3-2h0c-1-1-2-1-3-1-1-1-2-1-3-2-2-2-6-6-6-9z" class="F"></path><path d="M186 183l1-1s0-1 1-1l-1-1v-2h1c1 0 1 0 1 1l2 2c2 3 5 5 6 8h1c1 1 1 2 2 3v1l-6-5-2-2c-1 0-2-1-3-2h0c-1-1-2-1-3-1z" class="Y"></path><path d="M181 168c1 0 2 0 2 1h1 0c2 1 3 2 4 3 3 2 7 7 9 10 0 1 1 2 2 3l5 7-1 1c-3-1-6-8-8-11l-4-4c-1-2-3-5-5-6s-3-2-5-4z" class="b"></path><path d="M262 299l1-1v6c1-1 0-5 1-6h1v2c0 2 2 3 3 5 0 1 0 2 1 3h-1 0c-1 1-1 2 0 3h0c1 0 1-1 2-1l1 2v4l2-2h0c0 2 0 3-1 4v4l1 1v1l-2 1v1h0v1c0 1 0 2 1 3h-1v3c1 1 3 4 5 5h0c0 1 1 2 1 3v1c-1-1-2-3-4-4l-2-1-3-4c-1-2-2-4-3-5l1-1c0-1 0-2-1-3-1-2-2-3-3-5v-1c-1-2-1-3-3-4v1h-1l1-1h1l-1-1-1-3v-4l1 1v1-3l1 2h0l1-1v-3c0-2 0-3 1-4z" class="V"></path><path d="M271 316l2-2h0c0 2 0 3-1 4v4-3c-2 0-1 1-3 1h0v-2l1 1s1-2 1-3h0z" class="B"></path><path d="M266 312h1c1 0 1 1 2 1l2-1v4h0c0 1-1 3-1 3l-1-1-3-6h0z" class="Y"></path><path d="M263 304c1-1 0-5 1-6h1v2c-2 2-1 4 0 6v4l1 2h0c-1 1-1 2-1 3l1 1c0 1 0 1-1 2-1-2-1-4-2-6v-8z" class="M"></path><path d="M266 312l-1-2v-4c-1-2-2-4 0-6 0 2 2 3 3 5 0 1 0 2 1 3h-1 0c-1 1-1 2 0 3h0c1 0 1-1 2-1l1 2-2 1c-1 0-1-1-2-1h-1z" class="C"></path><path d="M262 299l1-1v6 8l-1 1c-1 1 1 3 0 5-1-2-1-3-3-4v1h-1l1-1h1l-1-1-1-3v-4l1 1v1-3l1 2h0l1-1v-3c0-2 0-3 1-4z" class="H"></path><path d="M261 303v11 1l-1-1-1-1-1-3v-4l1 1v1-3l1 2h0l1-1v-3z" class="C"></path><path d="M258 306l1 1v1c1 2 1 3 0 5l-1-3v-4z" class="E"></path><path d="M423 158v-2s1-1 2-1v2c1 2 1 5 2 7 1 1 1 3 1 3 1 1 1 2 2 3h0v1c0 1 0 2 1 4v-1c0-1 1-1 0-2 0-1 0-1-1-2l1-1c0 1 1 2 1 2 0 1 1 2 1 3-1 1-1 1-1 3h0c1 0 1 1 2 2v-1l1 1h0v2c0 1 0 1 1 1 0 0 0 1-1 1 0 2 0 4-1 6v1l-1 1v-1h-2c0 2 0 3 1 5h-2c-1-1-1-2-1-4-1 2-1 3-3 3l-1-4-3-6v-1-1h1 0v-3l1-1c-1-1-1-2-2-3v-1-2h-1c-2-1-2-1-2-3l3-5c0-1 1-3 2-4v-2h-1z" class="B"></path><path d="M426 178c0-2 0-2 1-3l1 1v2h-2z" class="b"></path><path d="M429 170c0 1 1 2 0 4 0 0-1 0-1 1l-1-1v-1l1-1 1-2z" class="I"></path><path d="M424 160h1c-1 2-1 3-1 4v2c-1 0-2-1-2-2s1-3 2-4z" class="J"></path><path d="M428 178l1-1h1c1 2 0 4 1 6v1l1 1c0 1 0 2-1 3 0-1-1-1-1-1v-1c0-2-1-4-3-5 1-1 1-2 1-3z" class="Q"></path><path d="M428 172c-1-1-3-3-3-5l1-1c0-1-1-2-1-2 0-1 0-1 1-2l1 2c1 1 1 3 1 3 0 1 0 2 1 3l-1 2zm4 5c1 0 1 1 2 2v-1l1 1h0v2c0 1 0 1 1 1 0 0 0 1-1 1 0 2 0 4-1 6v1l-1 1v-1h-2v-1-1c1-1 1-2 1-3l1 1v-1c-1-2 0-3-1-5v-3z" class="J"></path><path d="M422 164c0 1 1 2 2 2v2c0 1 1 1 1 2h0c0 2 0 5 1 7v1h2c0 1 0 2-1 3l-1 2h0c-1-2-2-3-2-5-1-1-1-2-2-3v-1-2h-1c-2-1-2-1-2-3l3-5z" class="H"></path><path d="M421 172c0-1 1-2 1-3h1l-1 6v-1-2h-1z" class="D"></path><path d="M423 182h0v-3l1-1c0 2 1 3 2 5h0l1-2c2 1 3 3 3 5v1s1 0 1 1v1 1c0 2 0 3 1 5h-2c-1-1-1-2-1-4-1 2-1 3-3 3l-1-4-3-6v-1-1h1z" class="B"></path><path d="M426 183h0v5 1h0c-1-1-1-2-1-3l1-1v-2z" class="b"></path><path d="M423 182h0v-3l1-1c0 2 1 3 2 5v2l-1 1c-1-1-2-2-2-4z" class="M"></path><path d="M427 181c2 1 3 3 3 5v1s1 0 1 1v1 1c0 2 0 3 1 5h-2c-1-1-1-2-1-4h0l-3-3v-5l1-2z" class="h"></path><path d="M429 191c0-1 0-3 1-3l1 1v1c0 2 0 3 1 5h-2c-1-1-1-2-1-4h0z" class="G"></path><defs><linearGradient id="K" x1="185.958" y1="244.31" x2="202.946" y2="242.668" xlink:href="#B"><stop offset="0" stop-color="#150f0e"></stop><stop offset="1" stop-color="#2c1313"></stop></linearGradient></defs><path fill="url(#K)" d="M204 274h0c-1-1-1-3-2-4-1 0 0-1 0-1-2-2-3-5-4-7-2-6-4-11-6-16-1-2-1-5-2-7l-4-10-2-7c-1-1-2-3-1-4v2c1-1 0-2 0-3 2 4 4 9 7 12 1 2 2 2 4 4 1 2 3 4 4 6s2 5 4 8c1 1 2 3 3 5l2 3v1h-1-1v1l-2 1c0 1 1 3 1 4h1c0 1 1 3 1 4h0l-1 1c-1 0-2 1-2 1v2 1h0c0 1 1 3 1 3z"></path><path d="M191 234h1c1 1 1 2 1 3h-1c0-1-1-2-1-3z" class="L"></path><path d="M205 262v2c0 1 0 1-1 2-1 0-2 0-3-1l1-1c-1-1-1-1-1-2h-1l2-1v-1h-2c0-1-1-2 0-3h0c-2-1-3-4-3-6 0-1 0-3-1-4s-2-2-2-3v-1h3v2c-1 1 1 2 2 3l1-1c0 1 1 1 1 2s0 3 1 4v1h1c1 0 1-1 2-2l2 3v1h-1-1v1l-2 1c0 1 1 3 1 4h1z" class="C"></path><path d="M199 248l1-1c0 1 1 1 1 2s0 3 1 4v1h1c1 0 1-1 2-2l2 3v1h-1-1v1h-1c-1-1-2-1-3 0-1-2 0-1 0-2s-1-2-1-3-1-2-1-4z" class="L"></path><path d="M261 254h0v-1-3c-1-1-1-1-1-2 1 0 1-1 2-1v-1h1c1 2 1 2 3 3 2 3 3 7 5 10 1 2 2 4 4 6 1 0 1 1 1 1 1 1 2 2 2 3l1 2h-1c0 2 3 5 3 7h-1 0v2 1c0-1-1-2-1-2h0c-1 0-2-1-3-1h0v1h0c-1 1-1 1-2 1h0c-1-1-1-2-2-2h0v1l6 15-1 2c-1-4-3-7-4-11-1-1-2-6-3-7l-1-1v-1h-1v4c-1-3-2-6-2-8-2-6-5-12-5-18z" class="D"></path><path d="M274 268v-2h1c0 1 0 1 1 2h0v2h0-1c-1-1-1-1-1-2z" class="H"></path><path d="M264 254c-1 1 0 2-1 2h0v-1c-1-1-1-1-1-2-1-1-1-2-1-3l1 1v-1h1l1 4z" class="B"></path><path d="M263 250h0v-3c1 2 2 5 3 6 1 2 2 3 2 5 2 3 3 6 5 8l1 2c0 1 0 1 1 2h1 0v-2-1-1c1 1 2 2 2 3l1 2h-1c0 2 3 5 3 7h-1 0v2 1c0-1-1-2-1-2h0c-1 0-2-1-3-1h0v1h0c-1 1-1 1-2 1h0c-1-1-1-2-2-2h0v1l-3-6c0-3-3-6-3-9l1-1v-1l-3-8-1-4z" class="d"></path><path d="M276 268v-1-1c1 1 2 2 2 3-1 0-1 0-1 1s0 1 1 2h-1v-1h-1l-1-1h1 0v-2zm-9-6c2 1 2 2 3 4 0 0 0 1 1 1h-1c-1 0-2-1-3-2 1 0 0-1 0-2v-1z" class="O"></path><path d="M271 267c1 1 2 3 3 4 1 2 2 2 3 3 1 2 1 3 2 5h0c-1 0-2-1-3-1h0v-1c-1-2-3-4-4-6-1-1-1-3-2-4h1z" class="H"></path><path d="M272 279l-3-6c0-3-3-6-3-9l1-1c0 1 1 2 0 2 1 1 2 2 3 2 1 1 1 3 2 4 1 2 3 4 4 6v1 1h0c-1 1-1 1-2 1h0c-1-1-1-2-2-2h0v1z" class="G"></path><path d="M267 265c1 1 2 2 3 2 1 1 1 3 2 4 1 2 3 4 4 6v1 1c-3-3-5-6-6-9-1-2-2-3-3-5z" class="P"></path><path d="M268 280v-4h1v1l1 1c1 1 2 6 3 7 1 4 3 7 4 11l1-2c1 2 2 3 2 4 0 2 1 3 1 4h0 1v-1l1-2c0 1 1 2 2 3h1l2 2 1 3h1v1 4h0c0 1 0 2 1 3h-1-1c0 2 2 3 2 5 2 5 5 9 8 14h-1l-1-1h0l-2-3c-1 1-1 1-2 1l-1-1v1h-1l-1-1-1-1v1l1 4v1c-2 0-2-1-3-1 0-2-1-4-2-6l-5-15-4-12-5-13-3-8z" class="Q"></path><path d="M287 320l1 2c0 2 0 2-1 4-1-2 0-4 0-6zm-9-23c1 2 2 3 2 4v3c-1 0-1-1-2-2 0 0 0-1 1-1 0-1-1-1-1-2v-2z" class="G"></path><path d="M288 322l1 2c1 1 1 3 2 4l1 2v1h-1l-1-1-1-1v1h-1c0-2 0-3-1-4 1-2 1-2 1-4z" class="Z"></path><path d="M278 294c1 2 2 3 2 4 0 2 1 3 1 4 1 3 2 5 3 7-1 1 0 2-1 3 0-3-2-5-3-8v-3c0-1-1-2-2-4l-1-1 1-2z" class="B"></path><path d="M283 312c1-1 0-2 1-3l3 6c1 2 1 3 2 4 0 1 1 2 1 3h-1v2l-1-2-1-2-4-8z" class="D"></path><path d="M282 301c1 1 2 3 3 5l1 2c0 1 1 2 1 2 1 1 1 4 1 5h-1l-3-6c-1-2-2-4-3-7h0 1v-1z" class="T"></path><path d="M282 301l1-2c0 1 1 2 2 3h1l2 2 1 3h1v1 4l-3-2s-1-1-1-2l-1-2c-1-2-2-4-3-5z" class="B"></path><path d="M289 307h1v1 4l-3-2s-1-1-1-2l1-1h1 1z" class="N"></path><path d="M287 310l3 2h0c0 1 0 2 1 3h-1-1c0 2 2 3 2 5 2 5 5 9 8 14h-1l-1-1h0l-2-3c-1 1-1 1-2 1l-1-1-1-2c-1-1-1-3-2-4v-2h1c0-1-1-2-1-3-1-1-1-2-2-4h1c0-1 0-4-1-5z" class="J"></path><path d="M293 327c1 1 2 2 2 3-1 1-1 1-2 1l-1-1-1-2c1 0 1 0 2-1z" class="M"></path><path d="M289 324v-2h1l3 5c-1 1-1 1-2 1-1-1-1-3-2-4z" class="E"></path><path d="M183 183c2 1 5 2 7 3l3 3c2 1 4 3 5 5h0 0l-2-1h0c0 1 0 1 1 2 0 1 0 1 1 2l-1 2c1 1 3 4 3 5 0 2 1 3 1 4l1 1c1 1 2 1 2 2 1 1 1 2 1 2h-1s-1 1-1 2v1c2 6 6 11 9 16l5 8c1 2 2 4 2 6 1 1 1 2 2 3 1 2 2 4 4 5l7 7v1l1 2v1c0 1 1 2 1 3v1h-1l-8-12h-1l-3-6c-1-1-2-2-3-4-1-1-2-3-3-4l-2-2c0-2-2-4-3-5-3-6-7-12-10-19-2-2-3-5-4-7 0-2 0-3-1-4 0-1-2-3-3-4l-1 1h0c-1-3-2-5-3-8v-1c-1-1-2-3-3-5 0-2-1-3-2-4v-2z" class="b"></path><path d="M201 208l1 1c1 1 2 1 2 2 1 1 1 2 1 2h-1s-1 1-1 2v1l-3-8h1z" class="h"></path><path d="M195 206c3 1 3 3 4 6 0 1 1 3 1 5h0c-2-2-3-5-4-7 0-2 0-3-1-4z" class="D"></path><path d="M195 198v-1l1-1 1 3c1 1 3 4 3 5 0 2 1 3 1 4h-1c-1-1-1-3-1-4-1 0-1 0-2-1 0-2-1-3-2-5z" class="H"></path><path d="M183 183c2 1 5 2 7 3l3 3c2 1 4 3 5 5h0 0l-2-1h0c0 1 0 1 1 2 0 1 0 1 1 2l-1 2-1-3-1 1v1l-2-2v1c0 1 1 2 0 3v-2c-1-1-1-1-2-1h0c0 1 0 2 1 3v1h0c-1-2-2-5-3-6l-1-1c-1-1-2-3-3-5 0-2-1-3-2-4v-2z" class="I"></path><path d="M193 196h0c0-1 0-2 1-2h0c-1-2-1-1-1-3h0c2 1 2 3 3 5h0l-1 1v1l-2-2z" class="D"></path><path d="M282 149c1 0 2 0 3-1 1 0 3 0 4-1l-1-1c1 0 1-2 0-2v-1c1-2 0-6 1-7l1 63v4l-1 2v-4c0-1-1-2-2-3-1 0-1-1-2-1h-1l-1-1v-1c-1-2 0-4-1-5v-2c0-1 0-2-1-3h0v-5c-1-10-1-21 1-31z" class="Z"></path><path d="M272 119c0-1 2-2 3-3l1-1 1 1-5 5 1-1 1 1v1h0c-1 1-2 3-3 5 0 1 1 2 1 3 1 0 1 1 2 1l1 1h0c1 1 1 1 0 2v2 1l-2-2-3 3h0v3c1 1 1 2 1 3v2l-1 1c0 1 0 1-1 2v2c1 1 1 2 2 3 0 1 1 2 1 3h-1v1c-1 0-1 0-1 1v3l-1 1-1 2h-1l-1 1v4c-1-2-2-3-2-4l-2-2-1-2v-6c-1-2 0-4 0-6h-1v-3-2l-1-1h0v-3c1-3 1-6 2-9 3-5 6-10 10-14l1 1z" class="X"></path><path d="M262 140c1-3 2-8 5-10 0-1 1-2 1-2v-1c0 3 2 4 4 6l-1 1-1-1-2-2-1 1c0 2 0 2-1 4h0-1c-1 1-1 4-1 6v-4l-2 2z" class="Y"></path><path d="M271 118l1 1c-8 6-11 15-12 25v1l-1-1h0v-3c1-3 1-6 2-9 3-5 6-10 10-14z" class="B"></path><path d="M273 120l1 1v1h0c-1 1-2 3-3 5 0 1 1 2 1 3 1 0 1 1 2 1v2h-2c-2-2-4-3-4-6 2-2 3-4 4-6l1-1z" class="b"></path><path d="M274 131l1 1h0c1 1 1 1 0 2v2 1l-2-2-3 3h0v3h0l-1-1c-1 0-2 1-2 2h0c-1-2-1-4-1-6h0c1-2 1-2 1-4l1-1 2 2 1 1 1-1h2v-2z" class="E"></path><path d="M270 138c-1-1-1-2-2-3h0c1 0 1 1 2 1 0-1 0-1 1-2v2l2-1h0 0l-3 3z" class="B"></path><path d="M274 131l1 1h0c1 1 1 1 0 2v2 1l-2-2h0 0l-2 1v-2c-1 0-1 0-1-1l1 1 1-1h2v-2z" class="I"></path><path d="M264 142c0-2 0-5 1-6h1c0 2 0 4 1 6h0c0-1 1-2 2-2l1 1h0c1 1 1 2 1 3v2l-1 1c0 1 0 1-1 2v2c1 1 1 2 2 3 0 1 1 2 1 3h-1l-2-3c0-2-1-2-1-3-1-1-1-3-2-4 0-1-1-2-2-4v-1z" class="H"></path><path d="M264 142c0-2 0-5 1-6h1c0 2 0 4 1 6 0 2 2 8 2 9h-1c-1-1-1-3-2-4 0-1-1-2-2-4v-1z" class="F"></path><path d="M262 140l2-2v4 1c1 2 2 3 2 4 1 1 1 3 2 4h-2-1v2 1h-2 0v2h-2 0c-1-2 0-4 0-6h-1v-3-2-1c1 0 1-1 2-1v-3z" class="H"></path><path d="M261 150v-2c0 1 0 4 1 5 0 1 1 1 1 1v2h-2 0c-1-2 0-4 0-6z" class="O"></path><path d="M260 144c1 0 1-1 2-1l-1 3v2 2h-1v-3-2-1z" class="K"></path><path d="M262 140l2-2v4 1h-1-1v1c0 1 0 1-1 2l1-3v-3z" class="O"></path><path d="M263 154v-7c1 1 3 2 3 4h-1v2 1h-2 0z" class="J"></path><path d="M266 151h2c0 1 1 1 1 3l2 3v1c-1 0-1 0-1 1v3l-1 1-1 2h-1l-1 1v4c-1-2-2-3-2-4l-2-2-1-2v-6h0 2v-2h0 2v-1-2h1z" class="G"></path><path d="M268 157l2 2v3l-1 1c0-1-1-3-1-4v-2z" class="Q"></path><path d="M263 154h0c1 2 1 3 2 5s1 2 1 4h-1c-1-1-1-3-2-3h-1c0 1-1 2-1 2v-6h0 2v-2z" class="P"></path><path d="M266 151h2c0 1 1 1 1 3l2 3v1c-1 0-1 0-1 1l-2-2v2c-1 0-2 0-3-1v1c-1-2-1-3-2-5h2v-1-2h1z" class="G"></path><path d="M266 151h2c0 1 1 1 1 3h-1 0c-1-1-2-2-2-3z" class="N"></path><path d="M265 153h1v1 2l1 1 1-1v1 2c-1 0-2 0-3-1v1c-1-2-1-3-2-5h2v-1z" class="Z"></path><path d="M213 241l2 2c1 1 2 3 3 4 1 2 2 3 3 4l3 6h1l8 12c2 3 4 8 7 11 1 0 2 0 4 1l1 1 1 1h2c1 0 1 1 2 1s1 0 2 1h1l1 1v1 1c1 1 1 2 2 2h0 1v-2c0-1 1-1 2-2l-2-3v-3h1c1 2 3 6 5 8v4 1l-1 6c-1 1-1 2-1 4v3l-1 1h0l-1-2v3-1l-1-1c-2 0-3-3-4-4h0c-2-1-3-1-4-2 0 0-3-5-4-6l-1-2c-1-1-2-3-3-4l-7-9c-1-3-2-5-4-7l-1 1h0l-7-13c-4-6-8-12-10-19z" class="K"></path><path d="M240 280c1 0 2 0 4 1l1 1 1 1h0v3-1c-3-1-5-3-6-5z" class="E"></path><path d="M246 283h2c1 0 1 1 2 1s1 0 2 1h1l1 1v1 1c1 1 1 2 2 2v1l1 1-1 1h-2c-1-1-2-2-2-3l-6-4v-3h0z" class="D"></path><path d="M246 283c1 2 2 3 4 4l2-1 1 1c-1 0-1 1-2 1l1 1v1h0l-6-4v-3z" class="B"></path><path d="M252 290h0v-1l-1-1c1 0 1-1 2-1h1v1c1 1 1 2 2 2v1l1 1-1 1h-2c-1-1-2-2-2-3z" class="b"></path><path d="M254 288c1 1 1 2 2 2v1l1 1-1 1c-1-2-2-3-2-5z" class="B"></path><path d="M245 292l3 3c0 1 2 2 3 3l3 3-1-1h1c-1-2-5-4-5-6h1l7 5v1c0 1 1 2 2 3v2 3-1l-1-1c-2 0-3-3-4-4h0c-2-1-3-1-4-2l-4-6-1-2z" class="a"></path><path d="M257 283v-3h1c1 2 3 6 5 8v4 1l-1 6c-1 1-1 2-1 4v3l-1 1h0l-1-2v-2c-1-1-2-2-2-3v-1c0-1 1-1 1-3h-1l-3-3h2l1-1-1-1v-1h0 1v-2c0-1 1-1 2-2l-2-3z" class="X"></path><path d="M259 286c0 1 0 2-1 3l1 1c0 1 0 3-1 4 0-1 1-2 0-3h-2v-1h0 1v-2c0-1 1-1 2-2z" class="Y"></path><path d="M256 291h2c1 1 0 2 0 3v2h-1l-3-3h2l1-1-1-1z" class="M"></path><path d="M205 267l1-1v5l2 2h0 1v3c1-1 1 0 1-1 1 0 1-2 2-1l1 1c1 4 4 7 6 11l-1 1h0c0 1 0 1 1 2v1h0 1l1 2v1l1 2c1 5 4 11 6 15h1c0 1 0 1 1 1h0c0 1 0 2 1 2l1 2s1 1 1 2 2 3 2 3c-1 1-2 0-2 0-1-1-2-1-2-2h-3-1-1l1 3h-1 0l-1-2c-1 0-1 0-1 1l-1-1h-1v-1l-1-1v-2c-1 0-1-1-1-1-1-2-2-5-4-7l-12-33s-1-2-1-3h0v-1-2s1-1 2-1z" class="a"></path><path d="M212 291c0-1-1-1 0-3 1 1 1 1 1 2l-1 1z" class="F"></path><path d="M222 312l1 2c1 1 1 3 2 5-1 0-1 0-1 1l-1-1v-3c-1-1-2-2-2-3l1 1h0v-2h0z" class="M"></path><path d="M219 290h1l1 2v1l-1 2h-1c0-1-1-2-1-3h1v-2h0z" class="C"></path><path d="M219 290h1l1 2v1c-2-1-2-1-2-3z" class="F"></path><path d="M224 309h0c0-1-1-1-1-2 0-2-2-5-3-7 0-1-1-1-1-2l2-1v-1l1-1c1 5 4 11 6 15h-1c-1 0-2-1-3-1z" class="O"></path><path d="M221 308h1 0v1h2c1 0 2 1 3 1h1 1c0 1 0 1 1 1h0c0 1 0 2 1 2l1 2s1 1 1 2 2 3 2 3c-1 1-2 0-2 0-1-1-2-1-2-2h-3-1-1l1 3h-1 0l-1-2c-1-2-1-4-2-5l-1-2c0-1-1-2-1-4h0z" class="C"></path><path d="M221 308v1c1 0 1 1 2 1l1 3-1 1-1-2c0-1-1-2-1-4h0z" class="L"></path><path d="M226 318c0-1-1-2-1-3s0-1-1-2v-1h0c2-1 2 0 3 0v1c1 0 2 1 2 1 0 1 1 2 1 3-1 0-2 0-3 1h-1z" class="d"></path><path d="M228 310h1c0 1 0 1 1 1h0c0 1 0 2 1 2l1 2s1 1 1 2 2 3 2 3c-1 1-2 0-2 0-1-1-2-1-2-2h-3-1c1-1 2-1 3-1 0-1-1-2-1-3 0 0-1-1-2-1v-1h1l-1-2h1z" class="F"></path><path d="M205 267l1-1v5l2 2h0 1v3c1-1 1 0 1-1 1 0 1-2 2-1l1 1c1 4 4 7 6 11l-1 1h0c0 1 0 1 1 2v1 2h-1l-1-3-2 2c-1 0-1 0-1 1 2 3 4 7 5 11l-5-7c0-1-1-2-1-3-1-1-1-1-1-2h0l1-1h1c0-2-1-2-2-3 0-2-1-3-1-4-1-1-1-2-1-3v-1h1c-1-1-1-2-2-3l-1 1-1-1c0-1 0-1 1-1v-1h-1-1v-2c-1-2-1-4-1-5z" class="C"></path><path d="M217 289c0-1-1-2-1-3h0v-1l2 2c0 1 0 1 1 2v1 2h-1l-1-3z" class="W"></path><defs><linearGradient id="L" x1="447.136" y1="123.161" x2="430.261" y2="97.81" xlink:href="#B"><stop offset="0" stop-color="#0e0b0a"></stop><stop offset="1" stop-color="#230e0e"></stop></linearGradient></defs><path fill="url(#L)" d="M389 107h-2v1c4 0 6 2 10 4 1 0 1 1 2 1l-1 1c-1-1-3-2-4-2l-7-3c-2-1-4-1-5-2-1 0-3 0-3-1h3v1h2 2v-1h-1c-1-1-2 0-3 0l-1-1h-3l-1-1c2-1 4 0 6 1h3 8v-1h1 1 1 1l1-1h0 2 2c2 1 4 1 7 2s6 1 10 1c1-1 3-1 4-1h0c4-1 8-1 12-1 4 1 6 2 9 4v1l-2 1c4 1 8 1 12 1 1 0 2 0 2 1h3 1 8c-1 0-1 1-1 2h-10l-4 2c-2 1-4 2-5 4h-1c0-1 1-2 2-3h-1c-1-1-1-1-2 0s-2 3-3 4c-2 2-4 4-4 6-1 0-4 4-5 4s-1 1-1 1l-1 1v-1c1-1 1-2 2-3l1-2h-2v1c-1 0-1 0-2 1 3-5 8-11 13-15h1-1c-3-1-6-1-9-1h0c-2-1-4-1-5-1l-17-3c-3 0-6-1-9-1l-16-1z"></path><path d="M455 111c1 0 2 0 2 1h3 1 8c-1 0-1 1-1 2h-10l1-1h2c-1 0-2 0-4-1-1 0-1 0-2-1h0z" class="W"></path><path d="M437 112h2l11 2h0-1c-1 0-2 1-3 2-2 1-4 3-5 4-2 2-3 5-5 7h-2v1c-1 0-1 0-2 1 3-5 8-11 13-15h1-1c-3-1-6-1-9-1h0l1-1z" class="C"></path><path d="M424 105c4-1 8-1 12-1 4 1 6 2 9 4v1l-2 1-11-2c-2-1-5-1-7-2 0 0 0-1-1-1z" class="O"></path><path d="M389 107l-1-1h1c3 0 6-1 9-1 2 0 4 1 5 1 4 0 7 0 10 1l24 5-1 1c-2-1-4-1-5-1l-17-3c-3 0-6-1-9-1l-16-1z" class="B"></path><path d="M253 314s0-1 1-1h0l4 5h1v-2-1-1c2 1 2 2 3 4v1c1 2 2 3 3 5 1 1 1 2 1 3l-1 1c1 1 2 3 3 5l3 4c1 2 1 5 2 7 1 1 2 2 2 3 1 0 1 1 1 1s-1 0-1 1c1 1 1 2 3 3 0 1 1 1 1 3 0 0 0 1 1 2h0l1-1c1 1 3 2 4 3 0 1 0 2 1 3l2 3c1 1 1 1 1 2 1 2 2 3 3 4h1l2 3h0v1h-1v3h-1c0 3 3 6 5 8l3 6c3 5 6 9 8 14-1 0-1 1-1 2l24 45-1 1-78-140z" class="i"></path><path d="M260 323h2c1 1 1 4 3 5 1 1 2 3 3 5-1 0-1 0-2 1l-6-11z" class="N"></path><path d="M266 334c1-1 1-1 2-1l3 4c1 2 1 5 2 7 1 1 2 2 2 3 1 0 1 1 1 1s-1 0-1 1c-1-2-2-3-2-5l-7-10z" class="D"></path><path d="M259 315v-1c2 1 2 2 3 4v1c1 2 2 3 3 5 1 1 1 2 1 3l-1 1c-2-1-2-4-3-5h-2l-2-5h1v-2-1z" class="S"></path><path d="M259 316c1 2 2 4 3 7h-2l-2-5h1v-2z" class="O"></path><path d="M292 378h1c0 3 3 6 5 8l3 6c3 5 6 9 8 14-1 0-1 1-1 2l-10-17c-2-4-5-9-6-13z" class="X"></path><path d="M280 357l1-1c1 1 3 2 4 3 0 1 0 2 1 3l2 3c1 1 1 1 1 2 1 2 2 3 3 4h1l2 3h0v1h-1v3h-1-1c-2-1-3-3-3-5-1-1-2-2-2-4h1c-2-3-3-6-4-8s-3-3-4-4z" class="N"></path><path d="M288 369l5 5h-1 0v1l-2-2h-1c-1-1-2-2-2-4h1z" class="V"></path><path d="M289 373h1l2 2v-1h0 1l1 1v3h-1-1c-2-1-3-3-3-5z" class="G"></path><path d="M254 177c1 0 2 0 3 1l1 1v1h2v1l1-1 1 1h2v1c1 1 2 1 3 2l1 1v-2l1 2 3 3 2 2c1 0 1-1 2-1v1l1 1 1 6 3 23c1 3 1 6 2 9 0 3 2 6 2 10h0 0c0-1 0-1-1-2v1l-1-2-1-2s0-1-1-1h-1s0 1 1 1l1 2v1 2c-1-2-2-5-3-7l-2-2-3-2-1-4-1-2-1-2 1-2v-1c-2-3-3-7-4-10l-3-5c0-1-1-3-2-4l-2-2-3-6c0-1 0-2-1-3 0-2-1-5-2-7l-1-3z" class="C"></path><path d="M272 217h0c1 1 1 2 2 3 0 0 0 1 1 1v1-1h0c0-1-1-1-1-2v-1s0-1-1-1h0l1-1 4 10c1 1 1 1 1 3h0-1l-1 1-3-2-1-4-1-2-1-2 1-2v-1z" class="N"></path><path d="M272 218h0c0 1 1 2 2 3v1h-1-1l-1-2 1-2z" class="U"></path><path d="M273 224v-1c2 1 3 3 4 4l1-1c1 1 1 1 1 3h0-1l-1 1-3-2-1-4z" class="b"></path><path d="M262 181h2v1c0 1 0 1 1 2 0 1 1 2 1 2h0c0 2 1 4 2 5 1 4 2 8 4 11 0 1 0 2 1 3l2 5 2 8h-1c-1-2-2-5-3-7l-8-20c0-3-1-5-2-7l-1-3z" class="B"></path><path d="M264 182c1 1 2 1 3 2l1 1v-2l1 2 3 3 2 2c1 0 1-1 2-1v1c-1 1-2 1-3 1v1l-1-1h0l-1 2v1c1 1 2 3 2 5 0 1 0 2-1 3-2-3-3-7-4-11-1-1-2-3-2-5h0s-1-1-1-2c-1-1-1-1-1-2z" class="d"></path><path d="M267 184c0 1 1 1 2 2s2 3 3 5h0l-1 2c0-1-1-2-1-3-1-2-2-3-3-5v-1z" class="I"></path><path d="M268 183l1 2 3 3 2 2c1 0 1-1 2-1v1c-1 1-2 1-3 1v1l-1-1c-1-2-2-4-3-5s-2-1-2-2l1 1v-2z" class="P"></path><path d="M254 177c1 0 2 0 3 1l1 1v1h2v1l1-1 1 1 1 3h-1v-1l-1 2 3 8c1 3 3 5 4 7 2 5 4 11 6 16l-1 1h0c1 0 1 1 1 1v1c0 1 1 1 1 2h0v1-1c-1 0-1-1-1-1-1-1-1-2-2-3h0c-2-3-3-7-4-10l-3-5c0-1-1-3-2-4l-2-2-3-6c0-1 0-2-1-3 0-2-1-5-2-7l-1-3z" class="B"></path><path d="M261 180l1 1 1 3h-1v-1l-1 2c0-1 0-3-1-4l1-1z" class="L"></path><path d="M258 190l1-1 1-1v1s0 2 1 2c0 1 1 1 1 2v2l-1 1-3-6z" class="I"></path><path d="M273 192v-1c1 0 2 0 3-1l1 1 1 6 3 23v3l-1-1v2l-3-6h0l-2-8-2-5c-1-1-1-2-1-3 1-1 1-2 1-3 0-2-1-4-2-5v-1l1-2h0l1 1z" class="L"></path><path d="M271 193l1-2h0l1 1h0c1 0 1 0 1 1h0-2c-1 0 0 0-1 1v-1zm2 6l1 4-1 2c-1-1-1-2-1-3 1-1 1-2 1-3z" class="M"></path><path d="M274 203c1 2 1 4 1 7l-2-5 1-2z" class="F"></path><path d="M273 192v-1c1 0 2 0 3-1l1 1 1 6-1 1-2-1-1-4h0c0-1 0-1-1-1h0z" class="E"></path><path d="M278 197l3 23v3l-1-1-5-25 2 1 1-1z" class="a"></path><path d="M274 228l3 2 2 2c1 2 2 5 3 7 4 7 6 15 7 23-1 3 2 6 1 9v3c0 2 1 5 1 6v1c-1 0-1-1-2-2-1 2 1 5 2 7 1 0 1 2 1 3h0l-2-2h-1l-4-6h0l-1 1-3-4c0-2-3-5-3-7h1l-1-2c0-1-1-2-2-3 0 0 0-1-1-1-2-2-3-4-4-6-2-3-3-7-5-10-2-1-2-1-3-3 0 0-3-8-4-10l1-1h0 1c-1-3-2-5-2-7l4 8 2 3c1 3 2 5 4 7 0 2 1 3 2 4s2 2 3 2c2-2 2-3 2-5 0-1 1-1 1-2-1-1-1-1-1-3h0c-1-1-1-3-1-5-1-1 1-2 1-4s-1-3-2-5z" class="N"></path><path d="M282 261h1v2c1 1 2 2 2 3s1 1 2 1v-1 1 1h-1-1c0 1 2 5 2 7-2-2-3-7-6-7 0-1-1-2-1-3 1 1 1 2 2 2l2-1c0-1-1-3-2-5z" class="J"></path><path d="M281 278c0-2-3-5-3-7h1c2 2 4 5 5 8 2 2 3 5 6 7h1c1 0 1 2 1 3h0l-2-2h-1l-4-6h0l-1 1-3-4z" class="E"></path><path d="M260 235h1c-1-3-2-5-2-7l4 8v2c0 2 2 4 3 6l9 16c1 2 0 3 2 4v1h0-1v-1h-1c0-1-1-2-2-2 0-1-1-2-2-3-2-3-3-7-5-10-2-1-2-1-3-3 0 0-3-8-4-10l1-1h0z" class="J"></path><path d="M263 246s-3-8-4-10l1-1 6 14c-2-1-2-1-3-3z" class="E"></path><path d="M275 237c1 2 1 3 2 4 0 2 1 3 2 4 1 5 0 11 3 16 1 2 2 4 2 5l-2 1c-1 0-1-1-2-2h0c-4-4-6-10-9-15 1 1 2 2 3 2 2-2 2-3 2-5 0-1 1-1 1-2-1-1-1-1-1-3h0c-1-1-1-3-1-5z" class="V"></path><path d="M274 228l3 2 2 2c1 2 2 5 3 7 4 7 6 15 7 23-1 3 2 6 1 9v3c-1-2-1-5-3-7v-1 1c-1 0-2 0-2-1s-1-2-2-3v-2h-1c-3-5-2-11-3-16-1-1-2-2-2-4-1-1-1-2-2-4-1-1 1-2 1-4s-1-3-2-5z" class="Z"></path><path d="M279 232c1 2 2 5 3 7 4 7 6 15 7 23-1 3 2 6 1 9v3c-1-2-1-5-3-7v-1c-2-4-2-8-3-12l-3-12c-1-2-5-7-4-9 1 0 1 0 2-1z" class="M"></path><path d="M203 216v-1c0-1 1-2 1-2h1l3 3h0c1 2 2 3 2 5l6 6c0 1 2 2 3 3l2 2h1v-1l1-1c1 2 2 3 3 5s2 4 4 6c1 2 3 4 4 6s2 5 5 6l1-1v1h1 1c1 1 3 5 4 7 2 3 4 5 5 8l7 12h-1v3l2 3c-1 1-2 1-2 2v2h-1 0c-1 0-1-1-2-2v-1-1l-1-1h-1c-1-1-1-1-2-1s-1-1-2-1h-2l-1-1-1-1c-2-1-3-1-4-1-3-3-5-8-7-11h1v-1c0-1-1-2-1-3v-1l-1-2v-1l-7-7c-2-1-3-3-4-5-1-1-1-2-2-3 0-2-1-4-2-6l-5-8c-3-5-7-10-9-16z" class="G"></path><path d="M213 229c0-1 1 0 1 0 0 1 1 1 1 2v2l-1-1c-1-1-1-2-1-3z" class="T"></path><path d="M232 258c1 1 2 2 2 4 0 0 0 1 1 1-1 0-2 0-3-1v-1-3z" class="J"></path><path d="M232 262c1 1 2 1 3 1 0 1 0 1 1 2l-1 2-2-3-1-2z" class="B"></path><path d="M221 232h1v-1l1-1c1 2 2 3 3 5l-1-1-1 1h-1c0-1-1-2-2-3h0z" class="Z"></path><path d="M227 243l4 6h0l-1-1-1 1h-1c0-2-1-3-2-4l1-2zm10 19h1l1 1c1 2 2 2 3 2 0 0 0 1 1 1h-1c-1 0-1 1-2 1-2-2-3-3-3-5z" class="Q"></path><path d="M248 266l2 3h0c-2 0-2 0-3-1-1 1-1 1-1 2h1l-1 1v-1c-1-1-3-1-4-1v-2c1 1 1 1 2 1h0 2v-1c0-1 1 0 2-1z" class="Z"></path><path d="M247 270h-1c0-1 0-1 1-2 1 1 1 1 3 1h0c1 2 2 3 3 5 0 0-1 0-1 1l-3-3s-2-1-2-2z" class="G"></path><path d="M225 249c3 3 5 6 7 9v3l-7-7 1-1c0-1-1-3-1-4z" class="U"></path><path d="M246 271l1-1c0 1 2 2 2 2l3 3h0l-1 1v1h0v4l1 2 1 2h-1c-1-1-1-1-2-1s-1-1-2-1l1-2c-1-1-2-1-2-2v-1c0-2 0-3-1-5h1v-1l-1-1z" class="N"></path><path d="M249 272l3 3h0l-1 1-2-1v-3z" class="P"></path><path d="M247 272l1 1c1 1 1 1 1 2-1 1 0 2 0 3l1 1v1 1h-1c-1-1-2-1-2-2v-1c0-2 0-3-1-5h1v-1z" class="Z"></path><path d="M219 241c-1-1-1-1-1-2v-1h1c0 1 1 1 1 2s1 2 2 2c0 1 1 1 1 2-1 2 1 4 2 5 0 1 1 3 1 4l-1 1c-2-1-3-3-4-5-1-1-1-2-2-3 0-2-1-4-2-6l1 2 1-1z" class="P"></path><path d="M217 240l1 2 1-1 2 5 1 2v1h-1c-1-1-1-2-2-3 0-2-1-4-2-6zm10 3h-1l1-1h0l3 3c1 0 0 1 0 2 1 0 2 1 2 2 1-1 1-2 2-2 1 2 2 5 5 6l1-1v1l8 13c-1 1-2 0-2 1v1h-2c1-1-2-3-2-4-2-4-6-8-8-12l-3-3-4-6z" class="J"></path><path d="M232 249c1-1 1-2 2-2 1 2 2 5 5 6v1l-1-1h-1v1l-5-5z" class="Q"></path><path d="M233 264l2 3h1c2 1 4 3 6 5l1 1h3c1 2 1 3 1 5v1c0 1 1 1 2 2l-1 2h-2l-1-1-1-1c-2-1-3-1-4-1-3-3-5-8-7-11h1v-1c0-1-1-2-1-3v-1z" class="M"></path><path d="M240 276l2-1 1 2v2h0l-3-3z" class="B"></path><path d="M236 267c2 1 4 3 6 5l1 1 1 1h-1l-1 1h0l-2 1c-2-3-3-6-4-9z" class="H"></path><path d="M243 273h3c1 2 1 3 1 5v1c0 1 1 1 2 2l-1 2h-2l-1-1c0-1-1-2-2-3v-2l-1-2h0l1-1h1l-1-1z" class="I"></path><path d="M243 273h3c1 2 1 3 1 5v1c-3-2-1-3-2-5h-2 1l-1-1z" class="P"></path><path d="M240 253h1 1c1 1 3 5 4 7 2 3 4 5 5 8l7 12h-1v3l2 3c-1 1-2 1-2 2v2h-1 0c-1 0-1-1-2-2v-1-1l-1-1-1-2-1-2v-4h0v-1l1-1h0c0-1 1-1 1-1-1-2-2-3-3-5l-2-3-8-13z" class="D"></path><path d="M253 274l1 3 1 1v1 1 1c-1 0-1 0-1-1l-2-2-1-1h0v-1l1-1h0c0-1 1-1 1-1z" class="N"></path><path d="M251 276l1-1 1 1-1 2-1-1h0v-1z" class="H"></path><path d="M252 283c1-1 1-2 3-2v1 2c1 1 1 1 2 1v-2l2 3c-1 1-2 1-2 2v2h-1 0c-1 0-1-1-2-2v-1-1l-1-1-1-2z" class="d"></path><path d="M257 283l2 3c-1 1-2 1-2 2v2h-1c0-1 0-1-1-2h0c1-1 1-1 1-2 0 0 0-1 1-1v-2z" class="M"></path><path d="M248 120l2-1v3h0l1 1c0 1 0 2-1 2h-1v2 1h1l1 1h0l2 2c1 1 1 1 1 3v5c1 1 1 2 2 3h0c0 1 0 3 1 4h0c-1 2-1 5-1 6 1 2 1 4 1 6 0 0-1 1 0 2v2-2c-1-4 0-8 0-12v-1c0-2 1-3 1-5 0-1 0-1 1-1v3h0l1 1v2 3h1c0 2-1 4 0 6v6l1 2c0 1 0 1-1 2h0 0c0 1 1 1 1 2v2h0-1v1c0 1 0 2 1 3h-1l-1 1 1 5-1 1v-1h-2v-1l-1-1c-1-1-2-1-3-1l1 3c1 2 2 5 2 7h-1-1s0-1-1-2c0-2-1-3-2-5-1-1-2-3-2-4l-1-3c-1-2-1-2-3-4l1-1s0-1 1-1h0l-3-4h0l-1-1c0-2-2-3-3-4l1 17v-2c0-1-1-2-1-3l-2-11-1-1-1 1v-1l-1-10-1-5h0v-7c1-2 1-5 3-8h0c1 0 1 0 1 1 0-1 1-2 2-2l-1 2-1 1v1c-1 1 0 2-1 3 0 0-1 1-1 2 0 2 0 6 1 7v-1h0v-3-1-1-1c1-1 1-2 1-3l1-2 1-1v-1 1c1-1 2-1 2-2v-1l2-3c1-2 1-3 3-4z" class="K"></path><path d="M235 143l2-2v7h-1l-1-5z" class="I"></path><path d="M235 136h1 1v5l-2 2h0v-7z" class="Y"></path><path d="M238 128c1 0 1 0 1 1-1 1-1 2-1 3-1 1-1 2-1 4h-1-1c1-2 1-5 3-8h0z" class="F"></path><path d="M237 148l2 11-1-1-1 1v-1l-1-10h1z" class="Y"></path><path d="M248 120l2-1v3h0l1 1c0 1 0 2-1 2h-1v2 1h1l1 1h-2-1c-1 2-2 4-2 7-1-1-1-1-2-1v-1c0-1-1-1-2-1 0 0 0 1-1 1h0l1-1h-1c-1 1-1 3-2 5 0-2 1-4 1-5 1-1 2-3 3-4 2-3 3-6 5-9z" class="E"></path><path d="M239 138c1-2 1-4 2-5h1l-1 1h0c1 0 1-1 1-1 1 0 2 0 2 1v1c1 0 1 0 2 1-1 0-2 1-2 2v1l-2-2h0c-1 1-1 2-1 3 0 2 0 3 1 4h0c1 1 1 2 2 3 0 1 0 3 1 4 1 2 5 4 5 6h0c1 2 2 3 3 5 1 1 2 2 2 4v1c-4-5-7-11-11-16h0c-3-1-2-4-4-6h0c-1-2-1-5-1-7z" class="I"></path><path d="M251 129h0l2 2c1 1 1 1 1 3v5c1 1 1 2 2 3v1l-2-1-1-1-2 1c0-1 0-1 1-2-1 0-1-1-1-1l-1 1c-2 0-2 0-4 2v2h-1l1 2h0l-1 1-1-1v1c-1-1-1-2-2-3h0c-1-1-1-2-1-4 0-1 0-2 1-3h0l2 2v-1c0-1 1-2 2-2 0-3 1-5 2-7h1 2z" class="d"></path><path d="M251 129h0l2 2h-3c-1 0-1-2-1-2h2z" class="Y"></path><path d="M242 144v-1h2 1l1 1h-1l1 2h0l-1 1-1-1v1c-1-1-1-2-2-3z" class="O"></path><path d="M244 146h0c0-1 0-1 1-2l1 2h0l-1 1-1-1z" class="D"></path><path d="M248 138l4-4s1 0 1 1v3l1 1c1 1 1 2 2 3v1l-2-1-1-1-2 1c0-1 0-1 1-2-1 0-1-1-1-1l-1 1c-2 0-2 0-4 2l2-4z" class="O"></path><path d="M248 138h2v1h2v1c-1 0-1-1-1-1l-1 1c-2 0-2 0-4 2l2-4z" class="I"></path><path d="M247 162c-1-2-4-5-4-6l1-1c0 1 0 1 1 1v1c1 1 2 2 3 4s3 5 5 7c3 3 7 7 7 12h-2v-1l-1-1c-1-1-2-1-3-1l1 3c1 2 2 5 2 7h-1-1s0-1-1-2c0-2-1-3-2-5-1-1-2-3-2-4l-1-3c-1-2-1-2-3-4l1-1s0-1 1-1h0l-3-4h0l1-1h1z" class="b"></path><path d="M247 162l1 1v1c1 0 1 1 1 1v1c-1 0-1 1-1 1l-3-4h0l1-1h1z" class="a"></path><path d="M250 176h1v-2c-1-1 0-1 0-2h0c2 2 2 3 3 5l1 3c1 2 2 5 2 7h-1-1s0-1-1-2c0-2-1-3-2-5-1-1-2-3-2-4z" class="N"></path><path d="M250 140l1-1s0 1 1 1c-1 1-1 1-1 2l2-1 1 1 2 1v-1h0c0 1 0 3 1 4h0c-1 2-1 5-1 6 1 2 1 4 1 6 0 0-1 1 0 2v2-2c-1-4 0-8 0-12v-1c0-2 1-3 1-5 0-1 0-1 1-1v3h0l1 1v2 3h1c0 2-1 4 0 6v6l1 2c0 1 0 1-1 2h0 0c0 1 1 1 1 2v2h0-1v1c0 1 0 2 1 3h-1l-1 1c0-1 0-2-1-3 0-1-1-1-2-1h0c-1-1-1-2-2-2v-2h0v-1c0-2-1-3-2-4-1-2-2-3-3-5h0c0-2-4-4-5-6-1-1-1-3-1-4v-1l1 1 1-1h0l-1-2h1v-2c2-2 2-2 4-2z" class="F"></path><path d="M259 144h0l1 1v2 3h1c0 2-1 4 0 6l-1 1h-1 0-1v4-5c-1-3 0-8 1-12z" class="D"></path><path d="M258 156v-6l1-1v4 4h0-1v4-5z" class="H"></path><path d="M259 153l1-6v3h1c0 2-1 4 0 6l-1 1h-1v-4z" class="C"></path><path d="M259 157h1l1-1v6l1 2c0 1 0 1-1 2h0 0c0 1 1 1 1 2v2h0-1v1c0 1 0 2 1 3h-1l-1 1c0-1 0-2-1-3v-4l-1-7v-4h1 0z" class="J"></path><path d="M259 168v-3c1 1 1 1 1 2h0v1l1 2v1c0 1 0 2 1 3h-1l-1 1c0-1 0-2-1-3v-4z" class="B"></path><path d="M259 157h1l1-1v6l1 2c0 1 0 1-1 2l-1-1c0-3-1-5-1-8h0z" class="E"></path><path d="M246 146c1 2 2 3 4 4 0 1 1 2 2 3s2 1 2 3h1v-2h0l1 6h0l1 4c-2-3-4-5-7-7 0-2-4-4-5-6-1-1-1-3-1-4v-1l1 1 1-1z" class="U"></path><path d="M246 146c1 2 2 3 4 4-1 2 0 2 0 4l-1-1c-1-2-3-3-4-6l1-1z" class="B"></path><path d="M250 150c0 1 1 2 2 3s2 1 2 3h1v-2h0l1 6c-1 0-1-1-1-2-1-1-3-3-5-4 0-2-1-2 0-4z" class="O"></path><path d="M250 140l1-1s0 1 1 1c-1 1-1 1-1 2l2-1 1 1 2 1c0 4 0 7-1 11h0v2h-1c0-2-1-2-2-3s-2-2-2-3c-2-1-3-2-4-4h0l-1-2h1v-2c2-2 2-2 4-2z" class="h"></path><path d="M250 140l1-1s0 1 1 1c-1 1-1 1-1 2l2-1 1 1v3h0v1h0-3v-5-1h-1z" class="J"></path><path d="M254 142l2 1c0 4 0 7-1 11h0l-1-1c0-1 1-2 1-3-1 0-1-1-1-1 0-1 1-1 1-2h0v-1h-1v-1h0v-3z" class="P"></path><path d="M183 181c1 1 2 1 3 2 1 0 2 0 3 1h0c1 1 2 2 3 2l2 2 6 5c3 4 7 7 11 10 2 3 4 5 7 8 1 1 1 2 3 3 1 0 1 0 2 1l-1 1 33 48h1c1 1 2 2 2 3 1 1 2 3 3 4 1 2 2 5 3 7s2 4 3 7l1 1c-1 0-1 1-1 1v2h0l1 1h0c-1 1-1 2-1 3s1 1 1 2 0 2 1 2v2 1c1 0 1 1 1 1-1 1-1 2-1 3h0l-1 1h0c-1-2-3-3-3-5v-2h-1c-1 1 0 5-1 6v-6l-1 1 1-6v-1-4c-2-2-4-6-5-8l-7-12c-1-3-3-5-5-8-1-2-3-6-4-7h-1-1v-1l-1 1c-3-1-4-4-5-6s-3-4-4-6c-2-2-3-4-4-6s-2-3-3-5l-1 1v1h-1l-2-2c-1-1-3-2-3-3l-6-6c0-2-1-3-2-5h0l-3-3s0-1-1-2c0-1-1-1-2-2l-1-1c0-1-1-2-1-4 0-1-2-4-3-5l1-2c-1-1-1-1-1-2-1-1-1-1-1-2h0l2 1h0 0c-1-2-3-4-5-5l-3-3c-2-1-5-2-7-3v-2z" class="F"></path><path d="M263 293c1 0 1 0 1 1 0 2 1 3 2 4l3 6-1 1h0c-1-2-3-3-3-5v-2h-1c-1 1 0 5-1 6v-6l-1 1 1-6z" class="D"></path><path d="M197 195h1c0 2 2 4 4 5 0 1 0 1 1 1s2 1 3 2v1l3 3v1h-4 0-2c0 1 0 1-1 1l-1-1c0-1-1-2-1-4 0-1-2-4-3-5l1-2c-1-1-1-1-1-2z" class="Y"></path><path d="M205 208c1 0 1 0 1-1s-1-1-1-2l1-1 3 3v1h-4 0 0z" class="E"></path><path d="M200 204l2 1 3 3h0-2c0 1 0 1-1 1l-1-1c0-1-1-2-1-4z" class="D"></path><path d="M226 235h1 0c1-2 0-3 0-5 0 1 1 2 2 2h0l17 25c4 6 8 13 12 20l4 7c0 1 1 2 1 2v2c-2-2-4-6-5-8l-7-12c-1-3-3-5-5-8-1-2-3-6-4-7h-1-1v-1l-1 1c-3-1-4-4-5-6s-3-4-4-6c-2-2-3-4-4-6z" class="U"></path><path d="M230 239c1 0 1 1 2 1v1c-1 0-1 0-2-1v-1z" class="N"></path><path d="M183 181c1 1 2 1 3 2 1 0 2 0 3 1h0c1 1 2 2 3 2l2 2 6 5c3 4 7 7 11 10 2 3 4 5 7 8 1 1 1 2 3 3 1 0 1 0 2 1l-1 1 33 48c3 6 6 11 8 18 1 0 1 1 1 1v1h0c-2 0-3-6-4-8-1-1-2-3-3-4 0-1-1-3-2-4-2-3-3-6-5-9-3-6-8-11-12-17l-13-19c-3-5-7-10-11-14-5-5-10-10-16-15h0c-1-2-3-4-5-5l-3-3c-2-1-5-2-7-3v-2z" class="H"></path><path d="M205 208h4c2 1 2 1 4 3v1l4 5 2 2c3 4 8 8 10 13h0c-1 0-2-1-2-2 0 2 1 3 0 5h0-1c-1-2-2-3-3-5l-1 1v1h-1l-2-2c-1-1-3-2-3-3l-6-6c0-2-1-3-2-5h0l-3-3s0-1-1-2c0-1-1-1-2-2 1 0 1 0 1-1h2 0z" class="U"></path><path d="M202 209c1 0 1 0 1-1h2 0c1 1 1 2 2 3 0 0 0 1 1 1v1l-1 1h0c1 0 1 1 1 1v1l-3-3s0-1-1-2c0-1-1-1-2-2z" class="N"></path><path d="M205 208h4c2 1 2 1 4 3v1h-2l-2 2-1-2c-1 0-1-1-1-1-1-1-1-2-2-3z" class="O"></path><path d="M209 214l2-2h2l4 5 2 2-1 2 1 1c-1 1-1 2-3 3v-1c-1-2-2-4-3-5-1-2-2-3-4-5z" class="H"></path><path d="M218 221l-3-2c-1-2-1-2-1-4h1c0 1 1 1 2 2h0l2 2-1 2z" class="N"></path><path d="M219 219c3 4 8 8 10 13h0c-1 0-2-1-2-2 0 2 1 3 0 5h0-1c-1-2-2-3-3-5-1-1 0-1-2-1h0v1h0c-2-1-3-3-5-5 2-1 2-2 3-3l-1-1 1-2z" class="P"></path><path d="M201 288c2 2 3 5 4 8l5 15 18 51 4 11c0 1 1 2 1 3 0 2-1 2 0 4 1 0 0 0 1 1v1l-1 5c0-1 0-2-1-3s-2-3-4-4h0v-1-1c1-1 0-1 0-2h-1v3h-1v1c0 1-1 1-1 2s0 1-1 2c0-1-1-2-2-2h-1v1c0-1 1-2 1-3 0 0-1 0-1-1h-1c0-1 0-1 1-2l1-1v-2c1 0 1-1 1-2h-1l-1 1v1h-1l-1 1v1h-3c-1-1 0-1 0-2l-1-1c-1 1-1 1-2 0v-1c1-1 1-1 1-2v-1l-1 2c-1 0-1 0-2-1 0-1 0-1-1-2v-3h-1c1-1 1-1 1-2-1 0-1-1-2-1 1-1 1-1 1-2h0c0-2 0-2-1-4h0v-6h0c-1-2-1-4-1-5 1 0 1 0 2-1-1 0-1-1-2-1-1-1-1-3-1-4s0-1-1-2c1-1 1-2 1-3s0-2-1-3l-1-3h1c-1-1-1-2-1-4h0c-1 0-2 0-3-1v-1c0-1 0-3-1-4l-1-1h-1c0-1 0-1 1-2l2 1v-1l1 1h1v-1c-1-2 0-4 0-6h-1c0-1 0-1 1-1-1-2-1-5-1-6s-1-3-1-4c0-2 0-4-1-6l1-1-1-3h0 1 0z" class="T"></path><path d="M203 315c0 1 0 2 1 3l-1 1c1 1 1 3 1 5h0 0c-1 0-2 0-3-1v-1c0-1 0-3-1-4l-1-1h-1c0-1 0-1 1-2l2 1v-1l1 1h1v-1z" class="S"></path><path d="M208 350h0c0 2 0 4 1 6v1h0l1 1v1 2 2c1 0 1-1 1-1h0-1 1c0-1 1-1 1-2h0c-1-1-1-2-2-3h1c2 1 3 2 5 3l1 1-1 1c-1 0-1 0-2 1h-1c-1 0-1 0-2 1h0 1c1 1 1 1 2 1 0 1 1 1 1 1l1 1v3h-1v1c1 0 2 0 3 1-1 2-1 2-2 3v1c-1-1 0-1 0-2l-1-1c-1 1-1 1-2 0v-1c1-1 1-1 1-2v-1l-1 2c-1 0-1 0-2-1 0-1 0-1-1-2v-3h-1c1-1 1-1 1-2-1 0-1-1-2-1 1-1 1-1 1-2h0c0-2 0-2-1-4h0v-6z" class="u"></path><path d="M241 170c0 1 1 2 1 3v2l-1-17c1 1 3 2 3 4l1 1h0l3 4h0c-1 0-1 1-1 1l-1 1c2 2 2 2 3 4l1 3c0 1 1 3 2 4 1 2 2 3 2 5 1 1 1 2 1 2h1 1c1 1 1 2 1 3l3 6 2 2c1 1 2 3 2 4l3 5c1 3 2 7 4 10v1l-1 2 1 2 1 2 1 4c1 2 2 3 2 5s-2 3-1 4c0 2 0 4 1 5h0c0 2 0 2 1 3 0 1-1 1-1 2 0 2 0 3-2 5-1 0-2-1-3-2s-2-2-2-4c-2-2-3-4-4-7l-2-3-4-8c0 2 1 4 2 7h-1l-2-4s-1 0-1-1 0-1-1-2c0 0 0 1-1 1h0l-1 1c-2-5-3-9-3-14l-10-34 1-3c0 1 0 1 1 1h0l-1-2c-1-3-1-5-1-8z" class="i"></path><path d="M259 200c0 2 0 3 1 4 0 2 1 4 1 6-2-2-3-6-3-8h1v-2z" class="V"></path><path d="M251 216c2 5 6 10 7 15 0 0-1 0-1-1s0-1-1-2c0 0 0 1-1 1h0l-1 1c-2-5-3-9-3-14z" class="H"></path><path d="M259 200l-2-7c2 1 3 3 4 6 0-1 1-1 2-1 1 1 2 3 2 4l3 5-1 1v-2c-1 0-2 0-2-1s0-2-1-2-1-1-2-1c0-1-1 0-1-1 0 0 0-1-1-1h0c0-2-1-4-2-5 1 1 1 3 1 4 1 1 1 3 1 5-1-1-1-2-1-4z" class="S"></path><path d="M263 198c1 1 2 3 2 4h-1-1l-1-1c-1 0-1-1-1-2s1-1 2-1z" class="V"></path><path d="M251 184c3 1 4 3 4 6 1 1 1 2 1 3h-1v2 3l1 1c0 1-1 1-1 2l1 1v1h-1-1c0-2-1-3-2-5h1 1v-1l-2-2c0-1-1-2-1-3 0-2 0-3-1-4 0-1 0-1-1-2v-1h1v2h1v1s0 1 1 1v1 3c1 1 1 1 1 2h0l1 1v-2c0-1-1-1-1-1v-4h0c-1-1-1-1-1-2-1-1-1-2-1-3z" class="S"></path><path d="M249 179h1v2l2-1c1 2 2 3 2 5 1 1 1 2 1 2h1 1c1 1 1 2 1 3l3 6 2 2c-1 0-2 0-2 1-1-3-2-5-4-6l2 7v2h-1l-2-6v-3c0-1 0-2-1-3 0-3-1-5-4-6h0c0-1-1-2-1-2-1-1 0-2-1-3h0z" class="G"></path><path d="M268 207c1 3 2 7 4 10v1l-1 2 1 2 1 2 1 4c1 2 2 3 2 5s-2 3-1 4c0 2 0 4 1 5h0c0 2 0 2 1 3 0 1-1 1-1 2 0 2 0 3-2 5-1 0-2-1-3-2s-2-2-2-4c-2-2-3-4-4-7 2 2 3 5 4 7 1 1 1 2 2 3h1s1 1 1 2h1 0c2-1 1-3 1-4l1-1v-2c-1 0-1-1-1-2v-1h0c-1-1-1-2-2-3v-2h0c0-1 0-1-1-1 0-1-1-1-1-2v-1l-1-1v-1-1-1h-1v-2s1 0 1-1h0l1 1h0v-2c1 1 1 2 2 2 0 0 0-1-1-1l-1-3h0v-1c0-1 0-1-1-1v-3-3c-2-1-2-1-2-3-1-1-1-2-1-3l1-1z" class="T"></path><path d="M241 170c0 1 1 2 1 3v2l-1-17c1 1 3 2 3 4l1 1h0l3 4h0c-1 0-1 1-1 1l-1 1c2 2 2 2 3 4l1 3c0 1 1 3 2 4l-2 1v-2h-1 0c-1 0-1 0-3-1 0 1-1 2-1 2l1 3v3l2 8c-2-3-2-5-3-8-1-2-2-4-2-6l-1-2c-1-3-1-5-1-8z" class="d"></path><path d="M243 171h-1c0-2 0-3 2-5 0-2 0-3-1-4h0 1l1 1h0l3 4h0c-1 0-1 1-1 1l-1 1-1 1c-1 0-2 0-2 1z" class="O"></path><path d="M245 170v-3s1 0 1-1l-2-1v-2h1l3 4h0c-1 0-1 1-1 1l-1 1-1 1z" class="D"></path><path d="M246 169c2 2 2 2 3 4l1 3c0 1 1 3 2 4l-2 1v-2h-1 0c-1 0-1 0-3-1 0 1-1 2-1 2l1 3v3c-1-2-3-8-2-10 0-2-1-3-1-5 0-1 1-1 2-1l1-1z" class="Z"></path><path d="M246 178c1-1 1-1 1-2 1 1 2 2 2 3h0c-1 0-1 0-3-1z" class="T"></path><path d="M246 169c2 2 2 2 3 4l-2-1c0 1-1 1-1 1h-1s-1 2-1 3c0-2-1-3-1-5 0-1 1-1 2-1l1-1z" class="U"></path><path d="M492 160c1 0 2 1 3 1l1 1-1 1-3 9-4 10h1 0 1l2-2c0 1-1 3-1 4-1 3-3 6-4 10v-1l-7 17c-1 2-2 5-3 7v1l-2 5c-1-1-1-2-1-2v-3h0c-1 1-2 1-3 2-1 3-3 5-5 7h0c-1 3-3 5-4 7l-9 16c-3 3-4 6-6 10 0 1-1 3-2 4h0l-1-1h-1v-2l2-10c0-2 0-3 1-4v-2h0l-2 2-1 2c1-2 2-5 3-7 0-1 1-1 1-2 1-1 1-2 2-2 0-3 0-5 1-7v-1l1 1v-1c2-5 4-12 7-17l2-6-1-1 3-6v-1-2h1 1v1l1-3 2-4 2-3s1 0 1 1c0-1 1-3 1-4 2-3 3-6 5-9h1l-1 2h2 1c1-2 2-3 3-4 0-2 2-4 4-6l3-3c1-2 2-4 3-5z" class="c"></path><path d="M473 192h1 0v2h-1v-2z" class="S"></path><path d="M474 200h1l1 1-1 2h-1 0v-3z" class="f"></path><path d="M467 208h1c0 1-1 3-1 4h0c-1 0-1 1-2 0 0-2 0-3 2-4zm7-25l2 1c-1 1-2 2-2 4v-1h-1l-2 2 3-6z" class="S"></path><path d="M476 178h2 1l-3 6-2-1v-1l1-1c0-1 1-2 1-3z" class="G"></path><path d="M486 168c0 1 1 1 2 1-1 0-1 0-2 1 0 1 0 1-1 2v2h-2c-1 1-1 1-1 2v-1c-2 1-2 2-3 3 1-2 2-3 3-4 0-2 2-4 4-6z" class="i"></path><path d="M488 176l1-1c1-1 0-2 1-3h2l-4 10-2 6c0-1 0-2-1-2h0v-3c1-1 2-2 2-3 0-2 1-3 1-4z" class="T"></path><path d="M451 250c1-1 2-2 2-4 1-1 2-3 3-5s2-3 3-4c1-2 2-4 3-5l3-6 1 1h0c-1 3-3 5-4 7l-9 16h-2z" class="Q"></path><path d="M465 226c1-1 1-2 2-3s2-2 3-4c-1 0 0 0-1-1h0v-1h0l2 1c1-1 1-2 2-3 1-2 2-5 3-7h1c1-1 1-2 2-3l-2 7-3 6h0 0c-1 1-2 1-3 2-1 3-3 5-5 7l-1-1z" class="J"></path><path d="M476 176h1l-1 2c0 1-1 2-1 3l-1 1v1l-3 6c-2 9-8 17-11 26h0 0-1v-1c3-8 8-16 11-25 0-1 1-3 1-4 2-3 3-6 5-9z" class="I"></path><path d="M469 188s1 0 1 1c-3 9-8 17-11 25h-1v-1l2-6-1-1 3-6v-1-2h1 1v1l1-3 2-4 2-3z" class="i"></path><path d="M462 197h1 1v1l-4 9-1-1 3-6v-1-2z" class="b"></path><path d="M492 160c1 0 2 1 3 1l1 1-1 1-3 9h-2c-1 1 0 2-1 3l-1 1h-1c-1 1-1 2-2 3l-4 7c-1 2-2 4-3 5 0 1-1 1-1 2h-1c1-2 2-3 2-4l3-5c2-3 4-6 5-10 1-1 2-3 2-4v-1c-1 0-2 0-2-1l3-3c1-2 2-4 3-5z" class="Q"></path><path d="M492 160c1 0 2 1 3 1l1 1-1 1-1-1c0 1-1 1-1 1-1 1-1 1-2 1v-1c-1 0-2 1-2 2 1-2 2-4 3-5z" class="J"></path><path d="M490 182l2-2c0 1-1 3-1 4-1 3-3 6-4 10v-1l-7 17c-1 2-2 5-3 7v1l-2 5c-1-1-1-2-1-2v-3h0l3-6 2-7 7-17 2-6h1 0 1z" class="X"></path><path d="M477 212c0 1 0 2-1 4 0 1-1 3-1 4 0-1 0-1 1-1 0-1 0-1 1-2h0v1l-2 5c-1-1-1-2-1-2v-3h0l3-6zm13-30l2-2c0 1-1 3-1 4-1 3-3 6-4 10v-1c-1-1 0-2 1-3 0-2 1-4 1-6l1-2z" class="L"></path><path d="M451 230c2-5 4-12 7-17v1h1v1h1 0l-1 3v3c0 2-3 3-3 6l1-1v1h0c0 1 0 1-1 2l2 2-1 1c-1 2 0 3-2 4v-1l-2 5c-1 2-2 4-3 5h0v2c0 1-1 1 0 2v1h1 2c-3 3-4 6-6 10 0 1-1 3-2 4h0l-1-1h-1v-2l2-10c0-2 0-3 1-4v-2h0l-2 2-1 2c1-2 2-5 3-7 0-1 1-1 1-2 1-1 1-2 2-2 0-3 0-5 1-7v-1l1 1v-1z" class="u"></path><path d="M453 240c-1-2 0-2 0-4l1-1h1l-2 5z" class="S"></path><path d="M450 245v2c0 1-1 1 0 2v1c-1 2-1 5-2 6-1 0-1 0-1 1h0 0c-1-3 1-5 1-7s0-3 2-5z" class="f"></path><path d="M450 231v-1l1 1v-1 4c-1 6-4 11-6 17 0-2 0-3 1-4v-2h0l-2 2-1 2c1-2 2-5 3-7 0-1 1-1 1-2 1-1 1-2 2-2 0-3 0-5 1-7z" class="d"></path><path d="M451 230c2-5 4-12 7-17v1h1v1h1 0l-1 3v3c-2 3-4 5-5 8 0 2 0 3-1 5h-2v-4z" class="h"></path><path d="M459 215h1 0l-1 3-2 2-1-1 3-4z" class="O"></path><path d="M456 219l1 1 2-2v3c-2 3-4 5-5 8h-1c0-3 2-5 2-8h0c1 0 1-1 1-1v-1z" class="J"></path><path d="M462 115l-1 2 1 1v1l1-1h2l1 1h0v1 2c1 0 2 1 3 1 0 1 1 1 2 2s1 2 2 3c2 1 3 3 4 5s1 2 3 3h1 0v2l-1 2v1 1h0 1c0-1 2-2 3-2 0 1-1 1-1 2l-1 1v1h0l1-1c0-1 1-1 1-2h1c0 2-4 6-5 8-1 1-2 2-2 3l-1 1s1 1 1 2c-1 1-1 3-2 4l-1 2h0v1c-1 2-2 3-2 5s0 4-1 6c0 1 0 2-1 3l-3 9c0 1 0 1 1 2v1l-2 3-2 4-1 3v-1h-1-1v2 1l-3 6 1 1-2 6c-3 5-5 12-7 17v1l-1-1v1c-1 2-1 4-1 7-1 0-1 1-2 2 0 1-1 1-1 2-1 2-2 5-3 7 0 1-1 5-2 6l-1 2c-1 1-2 1-2 2-1-1-1-2-1-3v1l-1-1-1-4v-6s-1-1-1-2l1-1v-1-1h0c1-2 1-2 0-3l-1-1v-3-4-1h-1v-3l1-6h-1c0 1 0 2-1 2v2 1l-1-1-2-2v-4c1-5 1-10 1-14v-5-1s1-1 1-2l-1-1h2c-1-2-1-3-1-5h2v1l1-1v-1c1-2 1-4 1-6 1 0 1-1 1-1-1 0-1 0-1-1v-2h0l-1-1v1c-1-1-1-2-2-2h0c0-2 0-2 1-3 0-1-1-2-1-3 0 0-1-1-1-2-1-2-1-4-2-6l-1-3c0-1 0-3-1-5l-1-1v-2l-2-4 2-3c1-3 3-6 5-8 1-1 1-2 1-2l2-2h1v-2c1 0 4-4 5-4 0-2 2-4 4-6 1-1 2-3 3-4s1-1 2 0h1c-1 1-2 2-2 3h1c1-2 3-3 5-4v1c3-1 5-1 8-2z" class="U"></path><path d="M432 213h0l1 1h1c0 1-1 1-1 2 0 0-1 0-1-1v-2z" class="B"></path><path d="M435 206v-2c1 1 1 2 1 3v1l-2-1 1-1z" class="J"></path><path d="M432 195v3h-1 0l-1 2v-2s1-1 1-2l-1-1h2z" class="B"></path><path d="M436 207h0l1 1-2 7-1 1c0 1 0 2-1 2v-2c0-1 1-1 1-2 0-2 1-4 2-6v-1z" class="h"></path><path d="M432 213c-1 0 0-2 0-3 0-2 0-3 2-5l1 1-1 1 2 1c-1 2-2 4-2 6h-1l-1-1h0z" class="D"></path><path d="M434 207l2 1c-1 2-2 4-2 6h-1l-1-1 2-6z" class="G"></path><path d="M437 187c1 1 1 1 2 1l-2 3 1 1c0 1-1 2-2 3v3c-1 1-2 3-3 4 0 2 0 2-1 4v1h-1c0-1 0-2 1-3h0c0-4 2-6 3-10 0-1 0-2 1-3l1-4z" class="H"></path><path d="M436 184l1-1c1 1 1 2 1 4h-1l-1 4c-1 1-1 2-1 3-1 4-3 6-3 10h0 0c0-2-1-4 0-6v-3c-1-2-1-3-1-5h2v1l1-1v-1c1-2 1-4 1-6l1 1z" class="N"></path><path d="M436 184l1-1c1 1 1 2 1 4h-1l-1 4c-1 1-1 2-1 3l-1 1h-1c1-2 1-4 2-5l1-1c0-2 1-4 0-5z" class="h"></path><path d="M445 170v-2 3c0 4 0 9-2 13v-1h-1c-1 1 0 2-1 4l-1 3c-1-2 1-4 0-6l-1 4c-1 0-1 0-2-1h1c0-2 0-3-1-4 1-1 1-3 1-4 1-1 1-2 2-2 1-2 0-4 1-6l1-2h1c0 1 1 1 2 1z" class="N"></path><path d="M441 177l1 1v2c-1 2-1 5-1 7l-1 3c-1-2 1-4 0-6 0-2 1-5 1-7z" class="B"></path><path d="M441 171l1-2h1c0 1 1 1 2 1 0 1 0 1-1 1l-3 6h0-1c1-2 0-4 1-6z" class="D"></path><path d="M438 179c1-1 1-2 2-2h1 0c0 2-1 5-1 7l-1 4c-1 0-1 0-2-1h1c0-2 0-3-1-4 1-1 1-3 1-4z" class="P"></path><path d="M439 167h3c0 2-1 3-1 4-1 2 0 4-1 6-1 0-1 1-2 2 0 1 0 3-1 4l-1 1-1-1c1 0 1-1 1-1-1 0-1 0-1-1v-2h0l-1-1v1c-1-1-1-2-2-2h0c0-2 0-2 1-3l1-1 1-3c1-1 3-2 4-3z" class="Z"></path><path d="M435 170l2 2c-1 1-1 2-2 3l-1-1h0v-1l1-3z" class="G"></path><path d="M436 182c0-1 0-2 1-3l1 1v-1h0c0 1 0 3-1 4l-1 1-1-1c1 0 1-1 1-1z" class="B"></path><path d="M443 186h0c1 3 0 7-1 9v3 1-1c0-1 0-1 1-1 0-2 2-2 2-3l1-1v-1c1-1 2-2 2-3 1-1 2-3 2-4l3-3v-1l1 1-2 2s0 1-1 2v1c-1 1-1 2-3 3v1 1c-1 1-1 1-1 2-1 1-2 3-3 4v1c-1 1 0 1-1 1v2h-1 0c-2 2-3 6-4 9h1c0 1 0 2-1 3l1 1v3l-2 5c-1 2-1 3-1 5v1h-1l-1-1v2-1h-1v-3l1-6 1-5 2-7-1-1c0-5 3-10 4-15v-2l1-3c1-2 0-3 1-4h1v1 2z" class="S"></path><path d="M434 228l1-6c1 1 1 1 2 1-1 2-1 3-1 5v1h-1l-1-1z" class="B"></path><path d="M438 211h1c0 1 0 2-1 3l1 1v3l-2 5c-1 0-1 0-2-1 1-4 2-7 3-11zm3-24c1-2 0-3 1-4h1v1 2l-1 6c-1 3-1 5-2 8 0 3-2 5-3 8l-1-1c0-5 3-10 4-15v-2l1-3z" class="I"></path><path d="M441 187c1-2 0-3 1-4h1v1 2l-1 6v-3c-1 1-1 2-2 3v-2l1-3z" class="d"></path><path d="M457 163h1 3c1-1 1-2 2-3v1 1c-1 1-1 2-1 3l1 1-10 15v1l-3 3c0 1-1 3-2 4 0 1-1 2-2 3v1l-1 1c0 1-2 1-2 3-1 0-1 0-1 1v1-1-3c1-2 2-6 1-9h0v-2c2-4 2-9 2-13 1-1 0-3 1-4h2s0 1 2 1c1-1 0-2 1-3h0 1c2 0 3-1 4-1 0-1 1-1 1-1z" class="f"></path><path d="M470 144l1 1v3 4c-1 1-1 3-2 4l-2 2c0 1 0 1-1 2l-3 6-1-1c0-1 0-2 1-3v-1-1c-1 1-1 2-2 3h-3-1s-1 0-1 1c-1 0-2 1-4 1h-1 0c-1 1 0 2-1 3-2 0-2-1-2-1h-2c-1 1 0 3-1 4v-3h0v-7h0v-4c0-2 0-3 1-4h1 1v-2h2 0c0 1-1 2 0 3v1l3-5h0l1-2h3 1l1-3h1v1 2 1c1 1 1 1 2 0 1 1 2 1 2 1v1h1v-1c1-1 2-1 2-2 1 0 1-1 1-1 0-1 1-2 2-3z" class="u"></path><path d="M451 165c1 0 1 0 2-1h1 0v-1l-1-1h4v1s-1 0-1 1c-1 0-2 1-4 1h-1z" class="c"></path><path d="M455 157h2c1 0 1-2 1-3 1 0 2-1 2-1h1l-1 1c-1 1-1 2-1 3v1h0-2c0 1 0 1-1 2-1 0-1 0-1-1v-2z" class="Q"></path><path d="M458 148l1-3h1v1 2 1c1 1 1 1 2 0 0 1 0 1-1 2l-1 2s-1 1-2 1c0 1 0 3-1 3h-2c1-2 2-3 2-5 0-1 0-1 1-1l-1-1 1-2z" class="G"></path><path d="M470 144l1 1v3 4c-1 1-1 3-2 4l-2 2c0 1 0 1-1 2v-4c1-1 1-1 1-2h-1c-1 1-2 2-3 2-1-1-1-1-2-3h0-1l1-2c1-1 1-1 1-2 1 1 2 1 2 1v1h1v-1c1-1 2-1 2-2 1 0 1-1 1-1 0-1 1-2 2-3z" class="V"></path><path d="M467 148h2v1h0v2c0 1-1 3-1 3v-1h-1 0l-2 1-1-1s0-1-1-1h0l1-2v1h1v-1c1-1 2-1 2-2z" class="G"></path><path d="M470 144l1 1v3 4c-1 1-1 3-2 4l-2 2c0-1 0-2 1-4 0 0 1-2 1-3v-2h0v-1h-2c1 0 1-1 1-1 0-1 1-2 2-3z" class="J"></path><path d="M454 148h3 1l-1 2c0 1-1 1-1 2s-1 3-2 4h0l-1 1c0 2-1 2-2 3h-1c0-1 0-1-1-2 0 1 0 1-1 2v1c0 1 0 2-2 3h0c-1 1 0 2-1 4v-7h0v-4c0-2 0-3 1-4h1 1v-2h2 0c0 1-1 2 0 3v1l3-5h0l1-2z" class="G"></path><path d="M454 148h3 1l-1 2c0 1-1 1-1 2l-1 1c-1-2 0-2-1-3h-1 0l1-2z" class="Z"></path><path d="M448 151h2 0c0 1-1 2 0 3v1c0 1 0 2-1 3-1 0-1 1-1 1 0 1-1 1-1 1h-2v1h0v-4c0-2 0-3 1-4h1 1v-2z" class="B"></path><path d="M445 157c0-2 0-3 1-4h1c0 2 1 2 1 3h-2c-1 1-1 3-1 4h0v1h0v-4z" class="U"></path><path d="M475 141l1-1c1 1 1 0 2 1l2-1v1 1h0 1c0-1 2-2 3-2 0 1-1 1-1 2l-1 1v1h0l1-1c0-1 1-1 1-2h1c0 2-4 6-5 8-1 1-2 2-2 3l-1 1s1 1 1 2c-1 1-1 3-2 4l-1-1c0 1 0 1-1 1v1h-1 0v-2c-1 1-2 4-3 5-3 2-4 5-6 8-1 1-2 2-2 3v1l-6 10c-2 3-3 6-5 9 0 1 0 2-1 3l-1 3-4 8-3 6h-1c-1 0-2 1-2 1l-1-1c1-1 1-2 1-3h-1c1-3 2-7 4-9h0 1v-2c1 0 0 0 1-1v-1c1-1 2-3 3-4 0-1 0-1 1-2v-1-1c2-1 2-2 3-3v-1c1-1 1-2 1-2l2-2-1-1 10-15 3-6c1-1 1-1 1-2l2-2c1-1 1-3 2-4v-4-3l-1-1 3-3v-1h0l1-1 1 1v1z" class="f"></path><path d="M462 174v1l-6 10c-2 3-3 6-5 9 0 1 0 2-1 3 0 0-1 1-2 1 0 1-1 1-2 1l16-25z" class="d"></path><path d="M446 199c1 0 2 0 2-1 1 0 2-1 2-1l-1 3-4 8-3 6h-1c-1 0-2 1-2 1l-1-1c1-1 1-2 1-3h-1c1-3 2-7 4-9h0c0 2-1 4-1 6 1-3 3-6 5-9z" class="b"></path><path d="M475 141l1-1c1 1 1 0 2 1l2-1v1 1h0 1c0-1 2-2 3-2 0 1-1 1-1 2l-1 1v1h0l1-1c0-1 1-1 1-2h1c0 2-4 6-5 8-1 1-2 2-2 3l-1 1s1 1 1 2c-1 1-1 3-2 4l-1-1c0 1 0 1-1 1v1h-1 0v-2l3-5h0c-2 2-3 3-4 5l-14 18c-2 2-3 4-4 6l-1-1 10-15 3-6c1-1 1-1 1-2l2-2c1-1 1-3 2-4v-4-3l-1-1 3-3v-1h0l1-1 1 1v1z" class="C"></path><path d="M475 141l1-1c1 1 1 0 2 1l2-1v1 1c-4 4-7 8-10 13 0 0 0 1-1 1 1-1 1-3 2-4v-4-3l-1-1 3-3v-1h0l1-1 1 1v1z" class="E"></path><path d="M473 140l1-1 1 1v1l-2 5c-1 2-2 4-2 6v-4-3l-1-1 3-3v-1h0z" class="U"></path><path d="M473 140l1-1 1 1v1l-2 5v-5-1h0z" class="P"></path><path d="M470 163c1-1 2-4 3-5v2h0 1v-1c1 0 1 0 1-1l1 1-1 2h0v1c-1 2-2 3-2 5s0 4-1 6c0 1 0 2-1 3l-3 9c0 1 0 1 1 2v1l-2 3-2 4-1 3v-1h-1-1v2 1l-3 6 1 1-2 6c-3 5-5 12-7 17v1l-1-1v1c-1 2-1 4-1 7-1 0-1 1-2 2 0 1-1 1-1 2-1 2-2 5-3 7 0 1-1 5-2 6l-1 2c-1 1-2 1-2 2-1-1-1-2-1-3v1l-1-1-1-4v-6s-1-1-1-2l1-1v-1-1h0c1-2 1-2 0-3l-1-1v-3-4-2l1 1h1v-1c0-2 0-3 1-5l2-5v-3s1-1 2-1h1l3-6 4-8 1-3c1-1 1-2 1-3 2-3 3-6 5-9l6-10v-1c0-1 1-2 2-3 2-3 3-6 6-8z" class="f"></path><path d="M449 222l-3-3c1-1 1-2 1-3v1c1 0 1 0 1 1h1 1l-1 2v2z" class="S"></path><path d="M436 242v2c0 1 0 1 1 2v1c0 1-1 2-1 3h0v5l1 1s0-1 1-1l-1 1v1l-1-1-1-4v-6l1-1v-2-1z" class="c"></path><path d="M434 230v-2l1 1h1v-1 3l1 1v-2-1h0l1 1 1-2c-1 2-1 4-1 6-1 2-1 4-2 6v2 1 2l-1 1s-1-1-1-2l1-1v-1-1h0c1-2 1-2 0-3l-1-1v-3-4z" class="Q"></path><path d="M434 230v-2l1 1h1c0 2 0 7-1 9l-1-1v-3-4z" class="D"></path><path d="M449 220c1 1 1 1 1 2s-1 2-1 4c1 1 0 3 0 4h1v1c-1 2-1 4-1 7-1 0-1 1-2 2l-1-1-1-2 4-15v-2z" class="B"></path><path d="M448 234v3l1 1c-1 0-1 1-2 2l-1-1c1-2 2-3 2-5z" class="I"></path><path d="M449 230h1v1c-1 2-1 4-1 7l-1-1v-3h-1l2-4z" class="h"></path><path d="M439 215s1-1 2-1h1c0 3-1 7-2 10-1 1-1 3-1 4l-1 2-1-1h0v1 2l-1-1v-3c0-2 0-3 1-5l2-5v-3z" class="N"></path><path d="M439 218v-1h1l-3 12h0v1 2l-1-1v-3c0-2 0-3 1-5l2-5z" class="T"></path><path d="M444 240l1-3 1 2 1 1c0 1-1 1-1 2-1 2-2 5-3 7 0 1-1 5-2 6l-1 2c-1 1-2 1-2 2-1-1-1-2-1-3l1-1c0-1 1-3 2-4v-1c1-1 1-2 2-4l2-6z" class="h"></path><path d="M444 240l1-3 1 2 1 1c0 1-1 1-1 2v-1h-1l-1 2h0v-3z" class="D"></path><path d="M455 197l1 1 1 2h1l2 2h0c1-1 1-2 2-2l-3 6 1 1-2 6c-3 5-5 12-7 17v1l-1-1v1-1h-1c0-1 1-3 0-4 0-2 1-3 1-4s0-1-1-2l1-2v-4l1-6 1-3 3-7v-1z" class="U"></path><path d="M452 205h2c0 2-1 3-1 4l-2-1 1-3z" class="d"></path><path d="M450 214l1-6 2 1-2 6c0-1 0-1-1-1z" class="B"></path><path d="M450 214c1 0 1 0 1 1 0 2-1 5-1 7 0-1 0-1-1-2l1-2v-4z" class="D"></path><path d="M460 202c1-1 1-2 2-2l-3 6h0c-1 1-1 1-1 2l-2 1c1-1 1-1 1-2v-2c0-1 1-2 2-2l1-1h0z" class="P"></path><path d="M455 197l1 1 1 2c0 2-1 2-2 3l-1 2h-2l3-7v-1z" class="B"></path><path d="M455 197l1 1 1 2c0 2-1 2-2 3 0-1 0-2 1-4h0l-1-1v-1z" class="N"></path><path d="M459 206l1 1-2 6c-3 5-5 12-7 17v1l-1-1 2-8c1-2 1-5 1-7 1-2 3-4 3-6l2-1c0-1 0-1 1-2h0z" class="E"></path><path d="M462 175c1-1 2-2 2-3l1 1c0 1-1 2-2 3l1 1v2h1c0 2-3 6-2 9 0 2-3 4-3 6v3l1 1 1-1v2 1c-1 0-1 1-2 2h0l-2-2h-1l-1-2-1-1v-1c-4 3-4 10-8 14 1-3 3-7 3-10h-1l1-3c1-1 1-2 1-3 2-3 3-6 5-9l6-10z" class="G"></path><path d="M458 185v3l1 1-1 1c-2 2-3 4-4 6h-1c0-4 3-8 5-11z" class="M"></path><path d="M458 185l5-9 1 1v2l-5 10-1-1v-3z" class="a"></path><path d="M464 179h1c0 2-3 6-2 9 0 2-3 4-3 6v3l1 1 1-1v2 1c-1 0-1 1-2 2h0l-2-2h-1l-1-2-1-1v-1h1l-1-1-1 1c1-2 2-4 4-6l1-1 5-10z" class="G"></path><path d="M458 199c1 0 2 1 3 1l1-1v1c-1 0-1 1-2 2h0l-2-2v-1z" class="Z"></path><path d="M458 195v4 1h-1l-1-2c1-1 1-2 2-3z" class="J"></path><path d="M458 190v1c0 1 0 2 1 3l-1 1c-1 1-1 2-2 3l-1-1v-1h1l-1-1-1 1c1-2 2-4 4-6z" class="U"></path><path d="M470 163c1-1 2-4 3-5v2h0 1v-1c1 0 1 0 1-1l1 1-1 2h0v1c-1 2-2 3-2 5s0 4-1 6c0 1 0 2-1 3l-3 9c0 1 0 1 1 2v1l-2 3-2 4-1 3v-1h-1-1l-1 1-1-1v-3c0-2 3-4 3-6-1-3 2-7 2-9h-1v-2l-1-1c1-1 2-2 2-3l-1-1c0 1-1 2-2 3v-1c0-1 1-2 2-3 2-3 3-6 6-8z" class="N"></path><path d="M465 173l2-3c1 0 1 1 1 1-1 2-2 5-4 6l-1-1c1-1 2-2 2-3zm5-10c1-1 2-4 3-5v2h0 1v-1c1 0 1 0 1-1l1 1-1 2h0-1c-1 1-1 2-2 3 0 2-1 3-2 4v1c0-1-1-1-1-2 1-1 1-2 1-4z" class="F"></path><path d="M462 174c0-1 1-2 2-3 2-3 3-6 6-8 0 2 0 3-1 4 0 1 1 1 1 2l-2 2s0-1-1-1l-2 3-1-1c0 1-1 2-2 3v-1z" class="U"></path><path d="M467 170l2-3c0 1 1 1 1 2l-2 2s0-1-1-1z" class="C"></path><path d="M464 184l1-2c1-1 1-2 2-3 0-1 1-4 1-4 2 0 2 0 3 1l-3 9c0 1 0 1 1 2v1l-2 3-2 4-1 3v-1h-1-1l-1 1-1-1v-3c0-2 3-4 3-6l1-4z" class="Z"></path><path d="M466 189c0 1 1 1 1 2l-2 4c0-1 0-1-1-2l2-4z" class="a"></path><path d="M464 193c1 1 1 1 1 2l-1 3v-1h-1l1-3v-1z" class="M"></path><path d="M466 189l2-4c0 1 0 1 1 2v1l-2 3c0-1-1-1-1-2z" class="E"></path><path d="M464 184h1v1l-2 5c1 2 1 3 1 4l-1 3h-1l-1 1-1-1v-3c0-2 3-4 3-6l1-4z" class="T"></path><path d="M462 115l-1 2 1 1v1l1-1h2l1 1h0v1 2c1 0 2 1 3 1 0 1 1 1 2 2s1 2 2 3c2 1 3 3 4 5s1 2 3 3h1 0v2l-1 2-2 1c-1-1-1 0-2-1l-1 1v-1l-1-1-1 1h0v1l-3 3c-1 1-2 2-2 3 0 0 0 1-1 1 0 1-1 1-2 2v1h-1v-1s-1 0-2-1c-1 1-1 1-2 0v-1-2-1h-1l-1 3h-1-3l-1 2h0l-3 5v-1c-1-1 0-2 0-3h0-2v2h-1-1c-1 1-1 2-1 4v4h0v7h0v2c-1 0-2 0-2-1h-1l-1 2c0-1 1-2 1-4h-3c-1 1-3 2-4 3l-1 3-1 1c0-1-1-2-1-3 0 0-1-1-1-2-1-2-1-4-2-6l-1-3c0-1 0-3-1-5l-1-1v-2l-2-4 2-3c1-3 3-6 5-8 1-1 1-2 1-2l2-2h1v-2c1 0 4-4 5-4 0-2 2-4 4-6 1-1 2-3 3-4s1-1 2 0h1c-1 1-2 2-2 3h1c1-2 3-3 5-4v1c3-1 5-1 8-2z" class="F"></path><path d="M438 135l-2-2 1-1c1 0 1 0 2 1v1l-1 1z" class="I"></path><path d="M444 121l1 1c0 1-1 1 0 2h-1l-2 1v1c1 0 1 1 1 1h-1l-1-1c-1 0-1 1-1 1 0-2 2-4 4-6z" class="C"></path><path d="M439 133c1 0 1-1 2-2 1 1 2 1 2 1v5 1c1 2 0 5-1 7v-1c-1 1-1 2-2 4h0v-1l1-1v-3-1c2-1 2-2 2-4v-1l-1-1h0c-1 0-2-1-3-2v-1z" class="Y"></path><path d="M444 121c1-1 2-3 3-4s1-1 2 0h1c-1 1-2 2-2 3h1l-3 8c0 1-1 1-1 2h0v-2h0l-1 1 1-5c-1-1 0-1 0-2l-1-1z" class="X"></path><path d="M440 147v1 1c0 1-1 2-1 3v2c1 0 2-1 2 0l1 1-2-1c0 1 0 1-1 2v2l-1 1h-1v-2l-1-1-1-1c1-2 1-3 1-4l1-2c1-1 2-2 3-2z" class="I"></path><path d="M438 155h0l1 3-1 1h-1v-2c0-1 0-1 1-2z" class="M"></path><path d="M436 151l1-2v1l1 1c0 1-1 1-1 2l1 1v1c-1 1-1 1-1 2l-1-1-1-1c1-2 1-3 1-4z" class="B"></path><path d="M439 134c1 1 2 2 3 2h0l1 1v1c0 2 0 3-2 4v1h0c-2-1-2-1-3-3-1 0-2 0-2-1-1 0-2 1-2 1l-2-1v1h-1c0-1-1-1-1-2h1v-1c1-1 1-2 1-2 1-1 2-1 3 0l3 3h0l1-2-1-1 1-1z" class="I"></path><path d="M431 138h1v-1s0-1 1-1h1 0c0 1 1 2 2 3-1 0-2 1-2 1l-2-1v1h-1c0-1-1-1-1-2h1z" class="B"></path><path d="M439 134c1 1 2 2 3 2h0l1 1v1l-1 2c-1 0-2 0-3-1v-2-1l-1-1 1-1z" class="b"></path><path d="M441 137h1v1l-1 1h-1v-1l1-1z" class="B"></path><path d="M432 140v-1l2 1s1-1 2-1c0 1 1 1 2 1 1 2 1 2 3 3h0v3l-1 1c-1 0-2 1-3 2l-1 2c0-1 0-2-1-2v1h-1v-2l-3-4 1-1v-3h0z" class="H"></path><path d="M438 144v-1h1 2 0v3c-1-1-2-1-3-2z" class="h"></path><path d="M437 145l-2-1v-3l1 1c1 0 1 1 2 2h0c1 1 2 1 3 2l-1 1c-1 0-2 1-3 2v-4z" class="B"></path><path d="M432 140v-1l2 1 1 1c-1 1-1 2-1 2v2c2-1 2 0 3 0v4l-1 2c0-1 0-2-1-2v1h-1v-2l-3-4 1-1v-3h0z" class="D"></path><path d="M432 140v-1l2 1 1 1c-1 1-1 2-1 2h-1c-1-1-1-2-1-3h0z" class="J"></path><path d="M434 145c2-1 2 0 3 0v4l-1 2c0-1 0-2-1-2 0-2 0-3-1-4z" class="h"></path><path d="M439 156c1-1 1-1 1-2l2 1v5h0 1c1 0 1 1 2 1h0 0v7h0v2c-1 0-2 0-2-1h-1l-1 2c0-1 1-2 1-4h-3v-1h-1c0-2 1-3 1-4v-1l-1-1-1 1 1-2 1-1v-2z" class="D"></path><path d="M439 156h1 1c0 2-2 3-2 5l-1-1-1 1 1-2 1-1v-2z" class="H"></path><path d="M439 166c1-2 2-3 3-4h1c0 2 0 4-1 5h-3v-1z" class="J"></path><path d="M445 161h0v7h0v2c-1 0-2 0-2-1h-1l-1 2c0-1 1-2 1-4 1-1 1-3 1-5l1 2c1-1 1-2 1-3h0z" class="H"></path><path d="M449 120c1-2 3-3 5-4v1c-4 3-6 8-7 13v2 7h1v-1 3 1s0 1-1 2v-2 10s0 1 1 1h-1-1c-1 1-1 2-1 4l1-29c1-3 2-5 3-8z" class="P"></path><path d="M447 142v-10 7h1v-1 3 1s0 1-1 2v-2z" class="K"></path><path d="M436 156l1 1v2h1l-1 2 1-1 1 1v1c0 1-1 2-1 4h1v1c-1 1-3 2-4 3l-1 3-1 1c0-1-1-2-1-3 0 0-1-1-1-2-1-2-1-4-2-6 1-1 1 0 2 0l1-1h1l1 1 1-1-1-1c0-1-1-1-1-2l2-2 1-1z" class="I"></path><path d="M437 159h1l-1 2c0 1-1 2-1 3s0 2-1 4h-1v-1-2c0-1-1-1-1-1v-2l1 1 1-1h1c0-1 1-2 1-3z" class="D"></path><path d="M436 156l1 1v2c0 1-1 2-1 3h-1l-1-1c0-1-1-1-1-2l2-2 1-1z" class="O"></path><path d="M433 159l2-2 1 1c-1 1-1 2-2 2v1c0-1-1-1-1-2z" class="B"></path><path d="M429 163c1-1 1 0 2 0l1-1h1v2s1 0 1 1v2 1 1c0 1-1 2-1 3l-1-1s-1-1-1-2c-1-2-1-4-2-6z" class="I"></path><path d="M432 171v-1 1h1v-1-1-3l1 1v1 1c0 1-1 2-1 3l-1-1z" class="h"></path><path d="M462 115l-1 2 1 1-3 3c0 2-1 3-2 4l-5 9c-1 1-2 2-2 4l-1 2-1 1v-3 1h-1v-7-2c1-5 3-10 7-13 3-1 5-1 8-2z" class="Y"></path><path d="M457 125v-2c0-1 1-1 2-2 0 2-1 3-2 4z" class="a"></path><defs><linearGradient id="M" x1="448.612" y1="123.358" x2="454.991" y2="128.911" xlink:href="#B"><stop offset="0" stop-color="#090808"></stop><stop offset="1" stop-color="#271112"></stop></linearGradient></defs><path fill="url(#M)" d="M462 115l-1 2 1 1c-1-1-2-1-3-1-1 1-3 2-4 3-4 3-6 8-7 13v5 1h-1v-7-2c1-5 3-10 7-13 3-1 5-1 8-2z"></path><path d="M426 145c1-3 3-6 5-8v1h-1c0 1 1 1 1 2h1 0v3l-1 1 3 4v2h1v-1c1 0 1 1 1 2s0 2-1 4l1 1-1 1-2 2c0 1 1 1 1 2l1 1-1 1-1-1h-1l-1 1c-1 0-1-1-2 0l-1-3c0-1 0-3-1-5l-1-1v-2l-2-4 2-3z" class="J"></path><path d="M429 152c2 1 1 2 2 3h-4l-1-1c1 0 1 0 2-1 0 0 0-1 1-1h0z" class="B"></path><path d="M426 152c1-1 1-2 2-3 1 1 1 1 1 3h0c-1 0-1 1-1 1-1 1-1 1-2 1v-2z" class="E"></path><path d="M427 155h4v2c0 1-1 2-2 3h-1c0-1 0-3-1-5zm7-7v2h1v-1c1 0 1 1 1 2s0 2-1 4c-1-1-1-1-1-2h0c-1-1-1-1-2-1h0v-1c1 0 1-1 2-2h0v-1z" class="I"></path><path d="M431 157c1 0 1 1 2 2 0 1 1 1 1 2l1 1-1 1-1-1h-1l-1 1c-1 0-1-1-2 0l-1-3h1c1-1 2-2 2-3zm-5-12c1-3 3-6 5-8v1h-1c0 1 1 1 1 2h1 0v3l-1 1 3 4v1l-3-2v-1c0-1-1-1-2-1v1c-1 0-1-1-2-1v2c1 0 1 1 1 2-1 1-1 2-2 3l-2-4 2-3z" class="b"></path><path d="M429 145c0-1 0-2-1-3l1-1c1 1 1 2 2 3l3 4v1l-3-2v-1c0-1-1-1-2-1z" class="U"></path><path d="M462 118v1l1-1h2l1 1h0v1 2h-1v2l-3 3 1 1v1h0l-1 1v1c0 1-1 2-2 3l-2 3c-1 3-3 6-4 9h0l1-1v1l-1 2-1 2h0l-3 5v-1c-1-1 0-2 0-3h0-2v2c-1 0-1-1-1-1v-10 2c1-1 1-2 1-2v-1l1-1 1-2c0-2 1-3 2-4l5-9c1-1 2-2 2-4l3-3z" class="K"></path><path d="M465 118l1 1h0v1 2h-1c-1-1-1-1-1-2-1 1-2 1-2 1h-1c1-1 2-2 4-3zm-9 12l1-1c1-2 3-5 5-6h0v1c-2 2-3 4-4 6 0 1-1 2-1 2v-1l-1-1z" class="M"></path><path d="M462 127l1 1v1h0l-1 1v1c0 1-1 2-2 3l-2 3 1-2s0-1 1-1h0v-1h-1c-1 1-2 2-3 4 1-2 2-4 3-5 1-2 2-3 3-5z" class="I"></path><path d="M456 130l1 1v1c-1 2-2 3-3 5l-6 9v-4-1l1-1 1-2 6-8z" class="P"></path><path d="M454 137l-1 2c1 0 1 0 2-1l-4 7c-1 1-1 2-1 3h-1c-1 0-1 0-1 1s1 1 0 2v2c-1 0-1-1-1-1v-10 2c1-1 1-2 1-2v4l6-9z" class="M"></path><path d="M456 137c1-2 2-3 3-4h1v1h0c-1 0-1 1-1 1l-1 2c-1 3-3 6-4 9h0l1-1v1l-1 2-1 2h0l-3 5v-1c-1-1 0-2 0-3h0-2c1-1 0-1 0-2s0-1 1-1h1c0-1 0-2 1-3l4-7 1-1z" class="D"></path><path d="M451 145v3l-1 2h1v-1-1c1 0 1-1 1-1v-1 4h1 0l-3 5v-1c-1-1 0-2 0-3h0-2c1-1 0-1 0-2s0-1 1-1h1c0-1 0-2 1-3z" class="P"></path><path d="M465 122h1c1 0 2 1 3 1 0 1 1 1 2 2s1 2 2 3c2 1 3 3 4 5s1 2 3 3h1 0v2l-1 2-2 1c-1-1-1 0-2-1l-1 1v-1l-1-1-1 1h0v1l-3 3c-1 1-2 2-2 3 0 0 0 1-1 1 0 1-1 1-2 2v1h-1v-1s-1 0-2-1c-1 1-1 1-2 0v-1-2-1h-1l-1 3h-1-3l1-2v-1l-1 1h0c1-3 3-6 4-9l2-3c1-1 2-2 2-3v-1l1-1h0v-1l-1-1 3-3v-2z" class="E"></path><g class="I"><path d="M466 133l3-4 1 1v1c-1 1-1 2-1 3l1 2v1h1l-1 1h0c-1 0-1-1-1-1v-2c-1-1-2-2-3-2z"></path><path d="M472 132l1-1 2 2s1 1 1 2h-1l-1 1v1c-1 0-1 0-2-1l-1 1h0-1v-1h1l1-1v-1l-1-1 1-1z"></path></g><path d="M472 134c0 1 0 1 1 1h1v1 1c-1 0-1 0-2-1l-1 1h0-1v-1h1l1-1v-1z" class="M"></path><path d="M467 130l-1 1-1-1 1-1c2-1 1-1 2-3 0-1 2-1 3-1 1 1 1 2 2 3 2 1 3 3 4 5-1-1-1-1-2-1v1l-2-2-1 1h-1v-1s-1-1 0-2h0v-2h-1c-2 1-2 2-3 3z" class="C"></path><path d="M475 133v-1c1 0 1 0 2 1 1 2 1 2 3 3h1 0v2l-1 2-2 1c-1-1-1 0-2-1l-1 1v-1l-1-1-1 1 1-3v-1l1-1h1c0-1-1-2-1-2z" class="K"></path><path d="M474 136l1-1h1c1 2 1 3 0 5l-1 1v-1l-1-1-1 1 1-3v-1z" class="D"></path><path d="M465 122h1c1 0 2 1 3 1 0 1 1 1 2 2-1 0-3 0-3 1-1 2 0 2-2 3l-1 1 1 1 1-1v1h-1c-1 0-1 1-2 2l-1-1c1 0 1-1 2-2-1 0-1 1-2 1v-2h0v-1l-1-1 3-3v-2z" class="a"></path><path d="M465 124h1v2l-2 1c0 1 0 1-1 2v-1l-1-1 3-3z" class="Y"></path><path d="M466 133c1 0 2 1 3 2v2s0 1 1 1h0l1-1h0l1-1c1 1 1 1 2 1l-1 3h0v1l-3 3c-1 1-2 2-2 3l-1-1 1-2c2-1 2-3 3-4l-1-1-1 2-2-1c-1-1 0-2-1-3-2 1-2 1-3 1h-1c0 1 0 1-1 1 0 1 0 1-1 2l-2-1c1-1 1-2 2-2 0-1 1-2 1-2h1l1 1 1-2v-2l1 1 1-1z" class="O"></path><path d="M471 137l1-1c1 1 1 1 2 1l-1 3h0-1v-3h-1z" class="B"></path><path d="M458 140l2 1c1-1 1-1 1-2 1 0 1 0 1-1h1c1 0 1 0 3-1 1 1 0 2 1 3l2 1 1-2 1 1c-1 1-1 3-3 4l-1 2 1 1s0 1-1 1c0 1-1 1-2 2v1h-1v-1s-1 0-2-1c-1 1-1 1-2 0v-1-2-1h-1l-1 3h-1-3l1-2v-1c1-2 2-4 3-5z" class="U"></path><path d="M460 149h1v-1c0-1 1-1 2-2 0 1 0 1 1 2 1-1 1-1 2-1v1l-1 1v1 1h-1v-1s-1 0-2-1c-1 1-1 1-2 0z" class="J"></path><path d="M467 140l2 1-2 3c-1 0-1 0-1-1-1 1-1 1-1 2v1l-1 1-1-1v-2c1-1 2-3 4-3v-1z" class="D"></path><path d="M458 140l2 1c1-1 1-1 1-2 1 0 1 0 1-1h1c1 0 1 0 3-1 1 1 0 2 1 3v1c-2 0-3 2-4 3-1-1-1-2-2-2h0c-1 1-1 2-1 2-1 1-2 1-3 1s-1 1-2 1h0v-1c1-2 2-4 3-5z" class="H"></path></svg>