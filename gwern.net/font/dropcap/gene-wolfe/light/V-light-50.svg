<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="96 96 856 880"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#020202}.C{fill:#fdfdfd}.D{fill:#b7b5b4}.E{fill:#4f4f52}.F{fill:#85868a}.G{fill:#acabae}.H{fill:#c1c0be}.I{fill:#969699}.J{fill:#e8e8e8}.K{fill:#6c6e72}.L{fill:#737377}.M{fill:#d8d9d9}.N{fill:#a0a2a7}.O{fill:#3a3a3c}.P{fill:#5b5d62}.Q{fill:#e3e3e2}.R{fill:#1a1a1b}.S{fill:#4a4c53}.T{fill:#282829}</style><path d="M574 175v-12h11l-2 2-1 1-8 9z" class="E"></path><path d="M519 936c0 1 0 2-1 3v1c0 1-1 1-1 2l-1 1h0v3c1 2 2 3 4 4v1l-1 2-1 2h1v2c-5 0-11 1-17 0-3 0-5 0-8-1h-13c3-1 7 0 10-1 6 0 11-2 16-1l1 1h1c4-5 6-13 10-19h0z" class="H"></path><defs><linearGradient id="A" x1="520.179" y1="951.554" x2="531.846" y2="947.598" xlink:href="#B"><stop offset="0" stop-color="#bbb8a8"></stop><stop offset="1" stop-color="#d8d7cc"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M526 945l1 1 2 1h1c1 1 1 1 2 1h1c1 0 1 0 3 1h2c1 0 1 0 2 1h0c1 0 2 0 3 1h0c1 1 2 1 2 2h-2c-1-1-3-2-4-2h-1c0-1-1-1-2-1h-1-3 1l1 2-1 1h0 1l1 1c1 0 1 0 2 1v-1h1 1 9c-2 1-8 1-10 1-6 1-13 2-19 2v-2h-1l1-2 1-2v-1c-2-1-3-2-4-4h2l1 1c0-1 1-1 2-1 2 1 5 2 7 3 0-1-1-2-2-3v-1z"></path><defs><linearGradient id="C" x1="436.386" y1="229.913" x2="454.462" y2="249.892" xlink:href="#B"><stop offset="0" stop-color="#9c9685"></stop><stop offset="1" stop-color="#dfe0d7"></stop></linearGradient></defs><path fill="url(#C)" d="M439 225h5 26 7s0 1 1 1c-2 1-4 0-5 1h-5c1 1 2 1 3 2s1 1 1 2c-1 1-2 2-3 2l-1 1c-1 1-1 1-2 1-1 1-2 2-3 2-2 1-5 3-7 5h-1l-5 5-3 3c0 1-1 2-1 2l-2 3c-1 1-1 2-2 2l-3 6c-1 2-2 3-3 4-1 2-1 3-1 5h1c0 1-1 2 0 3v2 1c-1-3-1-5-2-7-1-12-1-24 1-35 1-4 3-8 4-11z"></path><path d="M439 225h5l-3 2v1h-2v1l-1 1c1 1 3 1 4 2-1 0-1 1-2 1 0 1-1 0-1 1-1 1-2 2-4 2 1-4 3-8 4-11z" class="I"></path><defs><linearGradient id="D" x1="840.628" y1="270.479" x2="856.557" y2="279.689" xlink:href="#B"><stop offset="0" stop-color="#bcbaaa"></stop><stop offset="1" stop-color="#dbdbd2"></stop></linearGradient></defs><path fill="url(#D)" d="M820 218c1 1 1 2 2 3l3 4v1c2 2 3 6 5 9 3 8 4 15 6 24v-1c1-1 1-5 1-7l1 6 11-22 6-11 1-1 3-3c2 1 4 1 7 1l24 1 1 1h-1-2c0 1-1 1-2 1h0-2c0 1 0 1-1 1 1 0 1 0 2 1s1 2 1 4c-1 2-2 3-4 4s-3 3-5 4c-1 1-3 4-4 5l-3 3c0 1-1 1-1 2l-2 2v1l-2 2c0 1-1 2-1 2l-3 6-3 6h0c-1 1-1 2-2 3l-7 14c0 1-1 3-1 4l-3 5-6 13v1c-1 1-1 2-2 3h0c0 1 0 1-1 2l-4 6-2 4c-1 1-1 1-1 2s-1 2-2 3l-1 2c0 2-1 3-2 4l-1 1v2l-1 1v1c-1 1-1 2-1 3-1 0-1 1-1 2l-1 1v1l-1 3c-1 1-3 4-3 5v1c-1 1-2 2-3 4v1l-1 2h-1l-1 2-1 1c-1 1-1 1-1 2l-1 2c-2 2-4 4-5 6 0 1 0 1-1 1-1 1 0 0-1 2l-2 2c-1 1-2 2-2 3l-3 3h-1l5-7 7-9 2-3c3-5 7-9 9-14l2-4c1-1 2-4 2-6h1v-1c0-1 1-1 1-2l2-4v-1-2l1-1 1-6v-1-1c0-2 0-3 1-5v-1h0c0-2 1-5 0-8v-1-2l-1-1v-3-4h0c-1-1-1-2-1-3v-1-3l-1-1 1-2-1 1c0 2 1 6 0 8 0 1-1 3 0 4 0 2 0 3-1 5v-14-1-4l1-1h0l1-4v6c2-4 4-9 6-13-1-1-1-2 0-3v-2c1-2 1-6 1-8 0-13-2-24-7-35l-3-9z"></path><path d="M856 223l3-3c2 1 4 1 7 1-1 1-1 2-2 3 1 0 1 0 2 1 3 0 5 1 7 2 1 2 1 2-1 4 0 1-1 2-2 2-2 1-4 2-5 4-2 1-4 1-5 3-1 1-1 2-2 3-1 2-3 3-4 5-2 3-3 6-6 8 0 1 1 1 1 2l-4 4c0 1 0 2-1 3-1 3-2 5-3 7 0 2-2 4-3 6l-4 7c-2 5-4 12-8 16-1-1-1-2-2-3h0 0c-1-1-1-2-1-3v-1-3l-1-1 1-2-1 1c0 2 1 6 0 8 0 1-1 3 0 4 0 2 0 3-1 5v-14-1-4l1-1h0l1-4v6 1c0 1 0 3 1 4l1 2c2-1 2-6 3-8s2-3 2-4c1-2 3-4 4-6 1-1 1-3 2-5l6-11c1-2 1-4 2-5 3-3 4-7 6-10 5-6 11-10 16-15 2-1 2-1 3-3-3-3-10-4-13-5z" class="H"></path><path d="M820 218c1 1 1 2 2 3l3 4v1c2 2 3 6 5 9 3 8 4 15 6 24v-1c1-1 1-5 1-7l1 6 11-22 6-11 1-1c3 1 10 2 13 5-1 2-1 2-3 3-5 5-11 9-16 15-2 3-3 7-6 10-1 1-1 3-2 5l-6 11c-1 2-1 4-2 5-1 2-3 4-4 6 0 1-1 2-2 4s-1 7-3 8l-1-2c-1-1-1-3-1-4v-1c2-4 4-9 6-13-1-1-1-2 0-3v-2c1-2 1-6 1-8 0-13-2-24-7-35l-3-9z" class="D"></path><path d="M860 227l1 2c0 1-2 2-2 3l-1 1h-3c1-2 3-4 5-6z" class="I"></path><path d="M855 224c2 1 4 2 5 3-2 2-4 4-5 6-2 2-3 2-6 2l6-11z" class="F"></path><path d="M212 163h1c-6 3-11 5-16 8-18 10-35 27-42 46l-1 4v1c-1 2-1 5-2 7-1-1-3-5-4-5-1-2-2-4-3-5-12-18-29-32-49-40v-16h116z" class="R"></path><defs><linearGradient id="E" x1="883.29" y1="233.074" x2="838.846" y2="134.529" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#37393d"></stop></linearGradient></defs><path fill="url(#E)" d="M766 163c1 0 3-1 4 0h0 162v14c-7 1-14 2-20 4-19 5-36 17-48 32l-5 7-3 3-1 1-6 11-11 22-1-6c0 2 0 6-1 7v1c-2-9-3-16-6-24-2-3-3-7-5-9v-1l-3-4c-1-1-1-2-2-3-1-2-3-3-4-5-4-6-7-11-12-15-15-17-35-27-55-35 3 0 15 1 17 0z"></path><path d="M766 163c1 0 3-1 4 0h0-1c14 7 29 15 39 27h-1c-2-1-3-2-5-2-5-3-8-6-12-9-8-6-18-9-24-16z" class="J"></path><defs><linearGradient id="F" x1="837.5" y1="220.176" x2="803" y2="217.824" xlink:href="#B"><stop offset="0" stop-color="#bebcbf"></stop><stop offset="1" stop-color="#e7e6e4"></stop></linearGradient></defs><path fill="url(#F)" d="M802 188c2 0 3 1 5 2h1c3 2 6 6 8 9 10 12 19 30 20 46h0c1 2 1 4 1 6s0 6-1 7v1c-2-9-3-16-6-24-1-5-3-9-5-13-6-12-13-24-23-34z"></path><path d="M836 258v-3c0-4-1-7 0-10 1 2 1 4 1 6s0 6-1 7z" class="H"></path><path d="M749 163c3 0 15 1 17 0 6 7 16 10 24 16 4 3 7 6 12 9 10 10 17 22 23 34 2 4 4 8 5 13-2-3-3-7-5-9v-1l-3-4c-1-1-1-2-2-3-1-2-3-3-4-5-4-6-7-11-12-15-15-17-35-27-55-35z" class="G"></path><defs><linearGradient id="G" x1="693.176" y1="359.672" x2="512.902" y2="417.215" xlink:href="#B"><stop offset="0" stop-color="#a1a8ad"></stop><stop offset="1" stop-color="#ebedea"></stop></linearGradient></defs><path fill="url(#G)" d="M643 196c4 0 8 1 11 2 7 2 13 4 20 7 2 0 5 1 7 3 1 0 2 1 3 2l-1 1c-3-2-6-3-10-4l-13-4c-2 0-3-1-5 1 2 1 4 3 6 5 0 0 3 1 3 2 1 2 2 4 4 5l6 8c14 22 20 49 15 75-2 11-7 21-11 32-3 5-6 12-9 17-2 5-5 11-8 16l-17 34-59 123-18 37c-1 3-4 10-6 11-1-4-3-9-5-13l-2-5h0c1 1 2 3 3 5l1 2h0l3-3v-2c1-1 1-1 1-2l4-6 2-4 2-4c0-1 1-2 1-3 1-1 1-1 1-2h1l2-4 1-2 7-12 2-3 1-2v-2l5-8h0l1-2c0-1 0-1 1-2v-1h0c1-1 0-2 0-3l2-1 1-2c0-1 0-2 1-3h0l2-4 1 1c1-1 1-2 1-4h0v-2l2-2c0-2 0-2 1-3v1 1h0l2-3 1-3h1l1-2v-2c1-1 1-2 2-3-1 1-2 2-2 4-1 0-1 0-1 1v1h-1v-2c0-1 1-2 1-2 1-1 1-2 2-3h0l1-2c0-2 1-3 1-4h1c0-1 0-2 1-3 0-1 0-2 1-3l2-2v1l1-1c0-1 0-1 1-2 0-2 1-3 2-4l1-3 2-4v-2l1-1v-2c1-2 2-4 2-5h0l1-1v-2-1c0-1 0-1 1-2v1l5-14c0-2 1-4 1-5 1-1 1-1 1-2v-2c1-1 1-1 1-2v-2c1-1 1-1 1-2v-2c1-2 1-2 1-3v-2c1-2 1-2 1-3v-3c1-1 1-3 1-5 1-1 0-3 0-5v-9-3c-1-2-1-2-1-3v-2c-1-1-1-1-1-2v-1c0-1 0-1-1-2v-1l-1-4c-1-1-1-1-1-2v-1l-3-6c-1-1-2-2-2-3-1-1-2-2-2-3l-3-4v-1l-2-2c0-1-1-1-1-2l-3-3c-1-2-4-3-5-6l-2-2c-2-1-4-4-6-6-1 0 0 1-1 0l-3-3-2-1-2-2-2-1-1-1c-2-1-4-3-6-4-1-1-2-2-4-2v-1l-2-1c-1 0-2-1-3-1l-2-2c-1 0-2-1-3-1l-6-5c-2-1-2-2-3-3-2-1-4-2-5-3-1-2-2-3-2-5l1-1h-1v-2-1c0-1 0-2 1-3v-2h0l1-1c0-1 1-2 2-3 1-2 2-3 3-4 0-1-1 0 0-1l2-2c1-1 2-2 2-3h-3c3-3 8-6 11-9 11-8 22-14 34-19 10-5 20-9 31-12h1c0-1-1-2-2-2z"></path><path d="M643 196c4 0 8 1 11 2 7 2 13 4 20 7 2 0 5 1 7 3 1 0 2 1 3 2l-1 1c-3-2-6-3-10-4l-13-4c-2 0-3-1-5 1-4-1-7 0-10 1-13 3-26 8-37 15-2 1-4 2-6 4 0 0-1 0-1 1-1 0-1 1-1 1-2 2-6 3-7 4h-1v-1h0c-1 0-2 1-3 1 2-3 4-4 6-6 8-5 17-10 26-14 4-2 9-4 12-6 1-1 1-1 2-1l2-1c2-1 4-2 7-4h1c0-1-1-2-2-2z" class="N"></path><defs><linearGradient id="H" x1="686.755" y1="239.922" x2="627.73" y2="290.864" xlink:href="#B"><stop offset="0" stop-color="#484849"></stop><stop offset="1" stop-color="#9fa9ae"></stop></linearGradient></defs><path fill="url(#H)" d="M602 224c2-2 4-3 6-4 11-7 24-12 37-15 3-1 6-2 10-1 2 1 4 3 6 5 0 0 3 1 3 2 1 2 2 4 4 5l6 8c14 22 20 49 15 75-2 11-7 21-11 32-3 5-6 12-9 17v-3l1-1v-1h1v-5c0-1 1-3 1-4v-1c1-1 0-1 0-2 1-3 1-5 1-8 1-4 1-9 1-14v-3c0-1-1-4-1-6 0-1-1-2-1-3 0-3-1-5-1-7-2-6-3-11-6-16-2-4-4-8-7-11l-5-7h-1c-2-3-4-5-7-7-7-4-16-9-25-11h-1c-4-1-9-2-13-4-2-1-3-2-4-4 0-2 0-3 1-5 1 0 1 0 1-1h-2z"></path><path d="M661 209s3 1 3 2c1 2 2 4 4 5l6 8c-1 0-2 0-3-1s-2-2-3-2l-1-1c-3-3-5-7-6-11z" class="O"></path><defs><linearGradient id="I" x1="413.619" y1="413.495" x2="483.934" y2="366.851" xlink:href="#B"><stop offset="0" stop-color="#191a1d"></stop><stop offset="1" stop-color="#48576a"></stop></linearGradient></defs><path fill="url(#I)" d="M372 352c-11-18-21-37-29-57-3-10-7-21-8-32-1-6-1-13-1-19 1 2 0 5 1 7v-2c3 0 4 1 6 1l31 9 43 14c6 2 15 3 21 7l1 2 7 21 26 61 10 23c2 3 4 6 5 9l69 155 2 5c-1-1-2-2-2-3-1-1-1-1-1-2-1-2-3-5-3-7h0c-1-2-2-2-3-2l-1 1-1-1h0c-7-1-14-11-17-17-1-1-1-3-2-4-3-5-6-11-7-16v-2c-5-8-7-18-13-26-2-2-3-5-4-7l-9-10c-1-2-2-3-4-5l-36-33c-5-3-8-7-12-11-3-2-6-4-8-7-1-1-3-2-4-4-1-1-3-2-4-3 0-1-2-3-3-3h-1c-1-1-1-3-3-4-1 1 0 1 0 2 1 1 0 2 0 3-1-3-1-5-2-7h-1c0-1-1-1-1-2v-1h-1c-1-1-1-2-2-2-1-1-1-2-2-3v-1c-1-2-2-2-4-2l1 3c1 5 2 10 4 14v1c-3-1-5-3-7-5-13-10-22-24-31-38z"></path><path d="M417 342c1-2 2-3 4-4 2 0 3 0 5 1 1 1 1 2 2 3-2 1-8 0-11 0z" class="M"></path><path d="M417 342c3 0 9 1 11 0v1c1 2 0 4-1 6-2 2-4 1-6 1s-2-1-3-2c-2-2-1-4-1-6z" class="D"></path><path d="M405 377c-2-7-5-14-9-20-1-2-3-5-3-6 7 8 12 16 17 25 2 4 5 8 6 12h-1c0-1-1-1-1-2v-1h-1c-1-1-1-2-2-2-1-1-1-2-2-3v-1c-1-2-2-2-4-2z" class="C"></path><path d="M366 282h4c2 1 3 2 3 4 1 1 1 4 0 5-1 2-2 2-4 2-1 1-2 1-4 0-1 0-2-2-2-3-1-1-1-3 0-5 0-1 2-2 3-3z" class="F"></path><path d="M406 308c2-1 3-1 5 0 1 1 3 3 3 5 1 2 1 4-1 6-1 1-2 1-3 1-2 1-3 1-5 1-1-1-3-2-3-4-1-1-1-3-1-5 1-2 3-3 5-4z" class="C"></path><defs><linearGradient id="J" x1="438.832" y1="347.053" x2="464.031" y2="333.128" xlink:href="#B"><stop offset="0" stop-color="#4a576a"></stop><stop offset="1" stop-color="#6a7788"></stop></linearGradient></defs><path fill="url(#J)" d="M485 396l-1 2 1 3h-2c-3-3-7-5-9-9-2-1-2-3-4-5-1-1-3-2-4-4h0c-1-2-2-5-4-6s-3-2-4-4-3-5-4-6c-4-4-6-9-10-14h0l-1-2c-2-1-2-3-4-5-2-3-4-7-6-10-1-2-2-5-3-7l-7-10c0-1-1-2-2-3-1-3-3-5-4-8 0-1 0-1-1-2 0-1-1-2-1-3v-1l-1-1c0-2-1-3-1-5v-2c-1-1-1-1-1-2 1-2 0-2 0-3 0-2 3-4 4-6s3-3 5-5c3-1 3-1 7 0 2 1 5 1 7 3l1 1h1l7 21 26 61 10 23c2 3 4 6 5 9z"></path><path d="M212 163c3-1 11 0 14 0h4c4-1 8 0 12 0 5 0 10-1 14 0h2 6c4-1 8 0 12 0 7-1 14-1 21 0h3 13c4-1 10 0 15 0 1 0 4-1 6 0h0 17 14c3 0 7-1 10 0 13-1 28 0 42 0h81l-1 13v12h-12c-13 2-24 8-33 17-5 6-10 13-13 20-1 3-3 7-4 11-2 11-2 23-1 35 1 2 1 4 2 7v2c-6-4-15-5-21-7l-43-14-31-9c-2 0-3-1-6-1v2c-1-2 0-5-1-7 0-11 5-22 12-31v-2h0v-1c-1 0-2 1-4 1-7 1-16 5-23 8-2 1-4 1-6 3v3c0 3-1 6-3 8v1h0c-3 4-7 5-11 6-1 0-2 0-3-1h-1c-1 0-2 0-2-1h-1c-1 0-2-1-3-2s-2-3-4-3l-12 9c-2 1-3 3-5 5v1c-1 0-2 1-3 1l-2 2 1 1c-3 3-4 7-3 12 0 2 1 4 2 6-1 0-1 1-1 2l-1 1c-1 5 1 8 3 12h-1c-2-2-2-4-3-7h0c-1-2 0-6 0-9h0v-5l-1 1c-1 4-4 10-7 13-1 1-2 3-3 4l1 3v2l16 33 4 8 16 36c1 2 0 5-1 8-1 5-3 11-3 16-2 7-3 14-4 21-2 13-3 27-4 41 0 5 0 12 1 17h0c-2-2-3-5-4-7l-7-14-34-68c-4-8-6-16-10-23-3 0-4-6-6-8h0c-3-3-5-9-6-12l-24-46c-1-3-2-6-4-8-1-5-3-11-4-16-2-12 0-24 2-36v-1 1h-1v-1c1-3 1-8 0-10l-2 4-1 1c-2 2-3 7-4 10-1 9-2 18 0 26l1 5-13-23c-2-2-3-6-4-8-1-3 0-8 0-12l1-4c7-19 24-36 42-46 5-3 10-5 16-8h-1z" class="B"></path><path d="M219 320l2 1 2 21c-2-5-4-11-4-17v-5z" class="K"></path><path d="M206 325c2 3 4 6 5 9l1-1c4 7 6 15 8 22-3 0-4-6-6-8l-6-14c0-3-1-5-2-8z" class="L"></path><path d="M209 254c0 4-1 8-1 12 1 5 1 10 2 14 0 4 2 8 3 12 2 7 3 15 3 23-4-16-8-34-8-50 0-4 0-8 1-11z" class="E"></path><defs><linearGradient id="K" x1="224.928" y1="256.169" x2="235.013" y2="264.283" xlink:href="#B"><stop offset="0" stop-color="#b5b8b9"></stop><stop offset="1" stop-color="#e0dcdf"></stop></linearGradient></defs><path fill="url(#K)" d="M256 208c-1 4-4 6-6 9l-7 11c-3 4-6 10-8 15 0 1-1 3-1 4-1 5-4 9-2 13l-3 15c-1 2-1 7-2 8 0-14 1-27 6-40 2-7 5-12 8-18l15-17z"></path><path d="M220 220h1c1 3-2 7-4 10-4 9-6 22-6 32 1 1 1 1 1 3l1 1v2h0c1 1 1 2 2 4 0 1 1 2 1 4v3c0 1 1 2 2 3l3 39-2-1v5l-3-10c0-8-1-16-3-23-1-4-3-8-3-12-1-4-1-9-2-14 0-4 1-8 1-12-1-2 0-6 1-8 2-10 5-17 10-26z" class="L"></path><defs><linearGradient id="L" x1="214.322" y1="293.306" x2="220.178" y2="291.194" xlink:href="#B"><stop offset="0" stop-color="#a19fa3"></stop><stop offset="1" stop-color="#bdbfbe"></stop></linearGradient></defs><path fill="url(#L)" d="M211 262c1 1 1 1 1 3l1 1v2h0c1 1 1 2 2 4 0 1 1 2 1 4v3c0 1 1 2 2 3l3 39-2-1c-3-20-8-39-8-58z"></path><path d="M313 163c4-1 10 0 15 0 1 0 4-1 6 0h0 17c-3 2-6 2-9 2l-13 4c-27 6-54 19-73 39l-15 17c0-1 0-1-1-2-2 0-4 1-6 1-2 1-4 1-6 0 1-2 2-2 2-5-13 19-13 41-12 63-1-1-2-2-2-3v-3c0-2-1-3-1-4-1-2-1-3-2-4h0v-2l-1-1c0-2 0-2-1-3 0-10 2-23 6-32 2-3 5-7 4-10h-1 0l-3 3c-2 0-4 1-5 0h-1-1c0-4 2-7 4-10 5-7 11-12 17-19l-1-1c1 0 1-1 1-2h0c20-14 45-24 69-28h13z" class="H"></path><path d="M302 172c1-2 3-4 5-4h2c4-2 8-3 13-2l-20 6z" class="D"></path><defs><linearGradient id="M" x1="254.888" y1="170.22" x2="261.488" y2="215.433" xlink:href="#B"><stop offset="0" stop-color="#111213"></stop><stop offset="1" stop-color="#373638"></stop></linearGradient></defs><path fill="url(#M)" d="M300 163h13-1c-3 2-7 2-10 3l-14 5c-23 7-45 20-60 39-2 3-5 7-7 10h-1 0l-3 3c-2 0-4 1-5 0h-1-1c0-4 2-7 4-10 5-7 11-12 17-19l-1-1c1 0 1-1 1-2h0c20-14 45-24 69-28z"></path><path d="M334 163h17c-3 2-6 2-9 2l-13 4c-27 6-54 19-73 39l-15 17c0-1 0-1-1-2-2 0-4 1-6 1-2 1-4 1-6 0 1-2 2-2 2-5h0c2-3 5-6 8-9 17-19 40-29 64-38l20-6 12-3z" class="R"></path><path d="M212 163c3-1 11 0 14 0h4c4-1 8 0 12 0 5 0 10-1 14 0h2 6c4-1 8 0 12 0 7-1 14-1 21 0h3c-24 4-49 14-69 28h0c-17 11-29 27-34 46-1 9-2 18-1 27v11l6 29 10 29-1 1c-1-3-3-6-5-9 1 3 2 5 2 8l6 14h0c-3-3-5-9-6-12l-24-46c-1-3-2-6-4-8-1-5-3-11-4-16-2-12 0-24 2-36v-1 1h-1v-1c1-3 1-8 0-10l-2 4-1 1c-2 2-3 7-4 10-1 9-2 18 0 26l1 5-13-23c-2-2-3-6-4-8-1-3 0-8 0-12l1-4c7-19 24-36 42-46 5-3 10-5 16-8h-1z" class="B"></path><path d="M207 206l-1-1c-4 5-7 10-10 15h0v-1l1-2v-1l1-1v-1l1-1h0c-1-3 3-8 5-10l-1-1 8-8v1 1h1v1c-1 2-3 4-3 6h0c1-2 2-3 4-4 2-3 5-7 8-9 1 0 1-1 2-1l1-1c1 0 1-1 2-2h2c-4 3-8 6-12 10l-7 8-2 2z" class="O"></path><path d="M230 163c4-1 8 0 12 0 5 0 10-1 14 0h2c-6 1-13 4-19 7-18 9-38 21-50 38-5 6-8 13-11 21v-1 1h-1v-1c1-3 1-8 0-10 0-2 1-4 2-5 14-24 39-42 66-50h-5-10z" class="D"></path><path d="M206 207l-1 4c-1 2-3 4-4 6-4 7-6 12-8 20 0 2-1 4-1 7s1 7-1 10c0 4 0 9 1 13v1c0 2 1 3 1 5 1 3 2 7 3 10 0 3 0 3 1 4 0-1 0-3-1-5v-1-2-4l6 29 10 29-1 1c-1-3-3-6-5-9 1 3 2 5 2 8-3-4-4-9-6-14-9-21-18-46-15-70 1-7 3-14 6-21 1-1 2-4 3-5 3-6 6-12 10-16z" class="E"></path><path d="M206 207l-1 4c-1 2-3 4-4 6-4 7-6 12-8 20 0 2-1 4-1 7s1 7-1 10c0 4 0 9 1 13v1c0 2 1 3 1 5 1 3 2 7 3 10 0 3 0 3 1 4 0-1 0-3-1-5v-1-2-4l6 29 10 29-1 1c-1-3-3-6-5-9l-6-20c-2-7-5-14-6-21-3-13-4-26-4-39 1-6 3-11 3-16v-1c1-1 2-4 3-5 3-6 6-12 10-16z" class="I"></path><defs><linearGradient id="N" x1="255.226" y1="238.816" x2="218.515" y2="194.453" xlink:href="#B"><stop offset="0" stop-color="#8a898d"></stop><stop offset="1" stop-color="#c9c8c9"></stop></linearGradient></defs><path fill="url(#N)" d="M264 163c4-1 8 0 12 0 7-1 14-1 21 0h3c-24 4-49 14-69 28h0c-17 11-29 27-34 46-1 9-2 18-1 27v11 4 2 1c1 2 1 4 1 5-1-1-1-1-1-4-1-3-2-7-3-10 0-2-1-3-1-5v-1c-1-4-1-9-1-13 2-3 1-7 1-10s1-5 1-7c2-8 4-13 8-20 1-2 3-4 4-6l1-4 1-1 2-2 7-8c4-4 8-7 12-10 16-11 31-17 49-23h-13z"></path><path d="M212 163c3-1 11 0 14 0h4 10 5c-27 8-52 26-66 50-1 1-2 3-2 5l-2 4-1 1c-2 2-3 7-4 10-1 9-2 18 0 26l1 5-13-23c-2-2-3-6-4-8-1-3 0-8 0-12l1-4c7-19 24-36 42-46 5-3 10-5 16-8h-1z" class="B"></path><defs><linearGradient id="O" x1="195.958" y1="212.983" x2="177.413" y2="183.893" xlink:href="#B"><stop offset="0" stop-color="#89888c"></stop><stop offset="1" stop-color="#bbbabc"></stop></linearGradient></defs><path fill="url(#O)" d="M212 163c3-1 11 0 14 0-22 5-45 20-57 39-8 12-11 25-11 39-2-2-3-6-4-8-1-3 0-8 0-12l1-4c7-19 24-36 42-46 5-3 10-5 16-8h-1z"></path><defs><linearGradient id="P" x1="357.044" y1="167.171" x2="371.854" y2="262.392" xlink:href="#B"><stop offset="0" stop-color="#787779"></stop><stop offset="1" stop-color="#cacccf"></stop></linearGradient></defs><path fill="url(#P)" d="M351 163h14c3 0 7-1 10 0 13-1 28 0 42 0h81l-1 13c-19-4-38-4-57-4-35 4-71 14-94 41v-2h0v-1c-1 0-2 1-4 1-7 1-16 5-23 8-2 1-4 1-6 3v3c0 3-1 6-3 8v1h0c-3 4-7 5-11 6-1 0-2 0-3-1h-1c-1 0-2 0-2-1h-1c-1 0-2-1-3-2s-2-3-4-3l-12 9c-2 1-3 3-5 5v1c-1 0-2 1-3 1l-2 2 1 1c-3 3-4 7-3 12 0 2 1 4 2 6-1 0-1 1-1 2l-1 1c-1 5 1 8 3 12h-1c-2-2-2-4-3-7h0c-1-2 0-6 0-9h0v-5l-1 1c-1 4-4 10-7 13-1 1-2 3-3 4l1 3v2c-2-4-4-8-5-12l-10-20-3 5c-2-4 1-8 2-13 0-1 1-3 1-4 2-5 5-11 8-15l7-11c2-3 5-5 6-9 19-20 46-33 73-39l13-4c3 0 6 0 9-2z"></path><path d="M300 217c0-1 1-1 2-1 3 0 4 1 6 3s2 3 2 5c-1 1-2 2-4 3s-4 1-6 1c-1 0-2-1-3-2 0-2-1-4-1-6 1-1 2-2 4-3z" class="J"></path><path d="M375 163c13-1 28 0 42 0h81l-1 13c-19-4-38-4-57-4l1-1c4 1 8 1 11 1 6 0 12 0 18-1h0c2 1 3 1 5 1h3c3 1 6 0 9 1 1 0 2 1 3 0h0c1 1 2 1 3 1v-1c-1 0-2 0-4-1l-4-1h-2c-2 0-6 0-8-1-1 0-2 0-3-1-3-1-6 0-9 0h-18c-2 0-5 0-7-1h0l2-1h-4c-2-1-5-1-7-1h-13v-1h-4c-2-1-4-1-5-1h-4-14c-2 0-4 1-5 0h-1-5c-1 1-3 1-4 1-2 0-4 1-6 1h-1c-2 1-3 1-5 0l13-3z" class="K"></path><path d="M351 163h14c3 0 7-1 10 0l-13 3c-12 3-24 6-36 10-13 5-26 10-38 17-17 10-32 25-42 41-3 4-6 9-9 14-1 2-2 4-2 7l-3 5c-2-4 1-8 2-13 0-1 1-3 1-4 2-5 5-11 8-15l7-11c2-3 5-5 6-9 19-20 46-33 73-39l13-4c3 0 6 0 9-2z" class="M"></path><path d="M440 172c19 0 38 0 57 4v12h-12c-13 2-24 8-33 17-5 6-10 13-13 20-1 3-3 7-4 11-2 11-2 23-1 35 1 2 1 4 2 7v2c-6-4-15-5-21-7l-43-14-31-9c-2 0-3-1-6-1v2c-1-2 0-5-1-7 0-11 5-22 12-31 23-27 59-37 94-41z" class="B"></path><path d="M424 194h2c2 0 3 1 4 2s1 3 1 5-1 3-3 4h-2c-2 0-4-1-5-2-1-2-1-4 0-6 0-1 1-2 3-3z" class="F"></path><path d="M268 247c2-2 3-4 5-5l12-9c2 0 3 2 4 3s2 2 3 2h1c0 1 1 1 2 1h1c1 1 2 1 3 1 4-1 8-2 11-6h0v-1c2-2 3-5 3-8v-3c2-2 4-2 6-3 7-3 16-7 23-8 2 0 3-1 4-1v1h0v2c-7 9-12 20-12 31 0 6 0 13 1 19 1 11 5 22 8 32 8 20 18 39 29 57 9 14 18 28 31 38 2 2 4 4 7 5v-1c-2-4-3-9-4-14l-1-3c2 0 3 0 4 2v1c1 1 1 2 2 3 1 0 1 1 2 2h1v1c0 1 1 1 1 2h1c1 2 1 4 2 7 1 5 2 11 2 16v11 6c1 2 2 5 4 6-2 0-2 0-3-1h0c1 2 1 4 1 5v1c1 6 0 12 0 17 0 6 0 11-1 17 0 1 0 0-1 1v5l-1 1v2c0 1 0 2-1 4v2c0 1 0 1-1 2l-3 6c0 1-1 1-1 2h-1c0 1-1 2-1 3-1 1-1 2-2 2l-1 1 1 1h0c1-2 2-2 3-3-1 2-3 8-4 9h-1c-2 3-4 9-8 11-1 3-3 5-5 8-1 2-2 4-4 5l-2 4-1 4 1 1c-1 1-2 3-2 5l1-1c0 3-1 5-1 8l-2-2c-2 1-6 8-8 11l-16 19c-5 9-12 17-18 24l-2 3-1-1 1-1 1-2h0l-12-25c-2-4-6-10-6-14 5-5 3-12 6-17v-1c-2-1-2-1-3-2-4-5-10-8-15-12l-1-3c-3-2-5-5-8-8-2-2-4-4-6-7-7-10-13-21-17-33-1-4-2-7-2-11-1-2-1-5-2-7-1-5-1-12-1-17 1-14 2-28 4-41 1-7 2-14 4-21 0-5 2-11 3-16 1-3 2-6 1-8l-16-36-4-8-16-33v-2l-1-3c1-1 2-3 3-4 3-3 6-9 7-13l1-1v5h0c0 3-1 7 0 9h0c1 3 1 5 3 7h1c-2-4-4-7-3-12l1-1c0-1 0-2 1-2-1-2-2-4-2-6-1-5 0-9 3-12l-1-1 2-2c1 0 2-1 3-1v-1z" class="Q"></path><defs><linearGradient id="Q" x1="327.263" y1="473.263" x2="341.583" y2="467.413" xlink:href="#B"><stop offset="0" stop-color="#46464c"></stop><stop offset="1" stop-color="#7a8390"></stop></linearGradient></defs><path fill="url(#Q)" d="M321 461h0 1c0 1 0 1 1 2 1 0 3-1 4 0l8 1h5l-2 1c0 1 1 1 1 2l1 1c1 2 2 3 3 5l2 1-1 1c0 1 0 1 1 2 0 0 1 0 2 1l-5 1c-2 1-4 1-5 2h3 0v1c-1 0-2 0-2-1v2h-2c-2-1-3-3-4-4-5-5-8-12-11-18z"></path><path d="M332 405c0 2-2 4-4 6-3 4-6 8-8 13-2 4-3 11-1 16v1h0v2c0 3 4 9 5 11 2 2 4 5 7 7 1 1 2 1 4 1 1 0 1 1 2 1-1 1-1 1-2 1l-8-1c-1-1-3 0-4 0-1-1-1-1-1-2h-1 0c-5-9-8-21-6-31s9-19 17-25z" class="S"></path><path d="M290 454c2 2 2 4 2 6 1 1 1 2 1 3l1 5c1 2 2 5 3 7 1 1 2 2 3 4 1 3 2 4 3 6l1 1c0 1 1 2 2 4 2 2 5 3 7 6 1 2 3 3 5 4l-7-7-1-1c-1 0-1-1-2-2-1-2-3-4-5-6 0-4-2-5-3-8l-1-2v-2l-1-1c0-1-1-2-1-2v-3c0-1 0-1-1-3h0c-1-2 0-13 1-15 0-1 0-1 1-2l2 1c-2 0-3 1-3 3-1 3 0 11 2 14 1 2 1 5 2 6 1 4 3 8 4 11 1 2 2 3 3 5l6 6a170.44 170.44 0 0 0 19 19c3 3 6 6 9 8 0 1 1 1 1 2h0c-12-5-22-14-31-23-4-4-9-8-11-13-1-2-1-2-3-3-1-5-3-9-5-14-1-4-2-9-3-13v-1z" class="H"></path><defs><linearGradient id="R" x1="320.037" y1="480.603" x2="343.486" y2="469.18" xlink:href="#B"><stop offset="0" stop-color="#1f1e20"></stop><stop offset="1" stop-color="#4d4d4e"></stop></linearGradient></defs><path fill="url(#R)" d="M315 474c-5-9-9-21-7-32h1c2 2 3 11 4 15 6 21 25 37 46 42l1 1c-1 2-4 5-6 7l-1 2h-2c-14-10-26-20-36-35z"></path><defs><linearGradient id="S" x1="292.94" y1="452.366" x2="344.604" y2="516.712" xlink:href="#B"><stop offset="0" stop-color="#070606"></stop><stop offset="1" stop-color="#404043"></stop></linearGradient></defs><path fill="url(#S)" d="M300 447c3 3 4 12 6 17 1 4 2 7 4 10l1 2 1 1 5 7h1c1-1 1 2 2 2-1-2-3-4-4-6 0-1 0-2-1-2v-4c10 15 22 25 36 35l-1 1 1 1c-3 3-6 6-8 10 0-1-1-1-1-2-3-2-6-5-9-8a170.44 170.44 0 0 1-19-19l-6-6c-1-2-2-3-3-5-1-3-3-7-4-11-1-1-1-4-2-6-2-3-3-11-2-14 0-2 1-3 3-3z"></path><path d="M327 496c7 5 15 11 23 14l1 1c-3 3-6 6-8 10 0-1-1-1-1-2 1 0 1-1 2-1h-1l-1-1 1-1h2s1 0 1-1c-1-1-2-2-4-2-1 0-1-1-2-1-2-2-6-6-6-8 0-3-3-4-5-5-1-1-2-2-2-3z" class="O"></path><path d="M315 474c10 15 22 25 36 35l-1 1c-8-3-16-9-23-14-7-6-13-13-16-20l1 1 5 7h1c1-1 1 2 2 2-1-2-3-4-4-6 0-1 0-2-1-2v-4z" class="M"></path><defs><linearGradient id="T" x1="287.499" y1="378.628" x2="330.541" y2="386.836" xlink:href="#B"><stop offset="0" stop-color="#a2a1a2"></stop><stop offset="1" stop-color="#cfcecf"></stop></linearGradient></defs><path fill="url(#T)" d="M312 292l6-12c1 2 2 3 3 5 3 3 5 7 7 11l11 18 9 12 9 13 3 4c-1 0-2 0-2-1l-2-2v-1s-1-1-1-2h-1v-1l-2-2-1-1v-1h-1v-2h-1v-1l-1-2-1-1-2-2c0-1-1-1-2-2l-4-4c-5-6-11-14-19-16-1 0-1 1-2 2v1l-7 14c-3 10-6 20-8 29l-1 7-1 9-2 12-1 4v5c-1 4 0 10-2 14v6c-1 6 0 11-1 17-1 3-1 6-1 9 0 1 0 2-1 3 0 4 0 7-1 10l-2 10v1l-2-1c-1 0-2 1-2 3-3 7 2 20 4 28v-1c-2-1-2-2-2-4l-2-5c0-1-1-2-1-2v-2h-1s0 1 1 2h0v2c0 1 1 2 1 3h-1c-1 2-1 4 0 6 1 3 2 7 4 11l5 10c1 3 3 5 3 8l4 7c3 3 6 7 9 10 2 3 5 4 7 7 1 1 3 2 5 4 2 1 6 7 9 7h0l-2 3v-1c-2-1-2-1-3-2-4-5-10-8-15-12l-1-3c-3-2-5-5-8-8-2-2-4-4-6-7-7-10-13-21-17-33-1-4-2-7-2-11-1-2-1-5-2-7-1-5-1-12-1-17 1-14 2-28 4-41 1-7 2-14 4-21 0-5 2-11 3-16 1-3 2-6 1-8 2-2 3-7 3-9 5-18 11-35 18-52 2-1 2-4 3-5l2-6z"></path><path d="M312 292l6-12c1 2 2 3 3 5h-1v-1c0-1-1-1-2-2l-3 7v3c-1 4-4 7-5 11-6 14-12 29-16 44-4 13-6 28-8 41-1 6-1 13-3 18v2c0 2-1 4-1 5 0 2 0 4-1 6s0 5-1 8v1 1l-1 6v5h0c0 2-1 3 0 5 0 1 0 1-1 2 0 0 0 1 1 1 0 3 0 6-1 9 0 2 0 5 1 7 1 15 5 29 13 42 1 2 3 4 4 6l1 1 4 7c3 3 6 7 9 10 2 3 5 4 7 7 1 1 3 2 5 4 2 1 6 7 9 7h0l-2 3v-1c-2-1-2-1-3-2-4-5-10-8-15-12l-1-3c-3-2-5-5-8-8-2-2-4-4-6-7-7-10-13-21-17-33-1-4-2-7-2-11-1-2-1-5-2-7-1-5-1-12-1-17 1-14 2-28 4-41 1-7 2-14 4-21 0-5 2-11 3-16 1-3 2-6 1-8 2-2 3-7 3-9 5-18 11-35 18-52 2-1 2-4 3-5l2-6z" class="F"></path><path d="M282 388v7 2l-1 1h0v6l-4 29c0 2 0 3-1 5-1 23 1 47 13 67 2 3 4 5 6 7 0 1 1 2 1 2 1 2 2 2 3 3 0 1 1 2 2 3 3 3 6 7 9 10 2 3 5 4 7 7 1 1 3 2 5 4 2 1 6 7 9 7h0l-2 3v-1c-2-1-2-1-3-2-4-5-10-8-15-12l-1-3c-3-2-5-5-8-8-2-2-4-4-6-7-7-10-13-21-17-33-1-4-2-7-2-11-1-2-1-5-2-7-1-5-1-12-1-17 1-14 2-28 4-41 1-7 2-14 4-21z" class="L"></path><path d="M290 485c-2-8-7-21-4-28 0-2 1-3 2-3l2 1c1 4 2 9 3 13 2 5 4 9 5 14 2 1 2 1 3 3 2 5 7 9 11 13 9 9 19 18 31 23h0c2-4 5-7 8-10 0 3 2 6 3 9 3 5 5 10 7 15 1 3-2 6-3 9-4 7-9 13-12 20-5 9-8 19-9 29 4-12 10-22 16-32l10-13 2-2h1c1 3 3 6 4 9l1 1c-2 7-6 13-9 19 4-3 7-7 10-10-2 6-12 13-12 19-5 9-12 17-18 24l-2 3-1-1 1-1 1-2h0l-12-25c-2-4-6-10-6-14 5-5 3-12 6-17l2-3h0c-3 0-7-6-9-7-2-2-4-3-5-4-2-3-5-4-7-7-3-3-6-7-9-10l-4-7c0-3-2-5-3-8l-5-10c-2-4-3-8-4-11-1-2-1-4 0-6h1c0-1-1-2-1-3v-2h0c-1-1-1-2-1-2h1v2s1 1 1 2l2 5c0 2 0 3 2 4v1z" class="B"></path><path d="M362 575c4-3 7-7 10-10-2 6-12 13-12 19-5 9-12 17-18 24l-2 3-1-1 1-1 1-2h0c2-4 5-8 7-12l14-20z" class="L"></path><defs><linearGradient id="U" x1="311.297" y1="509.658" x2="298.982" y2="515.653" xlink:href="#B"><stop offset="0" stop-color="#a7a6a9"></stop><stop offset="1" stop-color="#cac8c9"></stop></linearGradient></defs><path fill="url(#U)" d="M297 513c0-3-2-5-3-8l-5-10c-2-4-3-8-4-11-1-2-1-4 0-6h1c0-1-1-2-1-3v-2h0c-1-1-1-2-1-2h1v2s1 1 1 2l2 5c0 2 0 3 2 4v1c5 11 10 23 17 33 4 6 9 11 13 16 3 3 11 11 11 14h0c-3 0-7-6-9-7-2-2-4-3-5-4-2-3-5-4-7-7-3-3-6-7-9-10l-4-7z"></path><defs><linearGradient id="V" x1="317.998" y1="521.648" x2="326.261" y2="510.473" xlink:href="#B"><stop offset="0" stop-color="#1b1b1c"></stop><stop offset="1" stop-color="#444446"></stop></linearGradient></defs><path fill="url(#V)" d="M298 482c2 1 2 1 3 3 2 5 7 9 11 13 9 9 19 18 31 23l-9 14-4-4c-9-7-16-16-21-26l-9-17-2-6z"></path><path d="M300 488c1 1 3 2 3 3l6 10v1l2 3h-1-1l-9-17z" class="O"></path><defs><linearGradient id="W" x1="418.016" y1="504.419" x2="353.628" y2="418.973" xlink:href="#B"><stop offset="0" stop-color="#afaeb2"></stop><stop offset="1" stop-color="#f4f2f4"></stop></linearGradient></defs><path fill="url(#W)" d="M405 377c2 0 3 0 4 2v1c1 1 1 2 2 3 1 0 1 1 2 2h1v1c0 1 1 1 1 2h1c1 2 1 4 2 7 1 5 2 11 2 16v11 6c1 2 2 5 4 6-2 0-2 0-3-1h0c1 2 1 4 1 5v1c1 6 0 12 0 17 0 6 0 11-1 17 0 1 0 0-1 1v5l-1 1v2c0 1 0 2-1 4v2c0 1 0 1-1 2l-3 6c0 1-1 1-1 2h-1c0 1-1 2-1 3-1 1-1 2-2 2l-1 1 1 1h0c1-2 2-2 3-3-1 2-3 8-4 9h-1c-2 3-4 9-8 11-1 3-3 5-5 8-1 2-2 4-4 5l-2 4-1 4 1 1c-1 1-2 3-2 5l1-1c0 3-1 5-1 8l-2-2c-2 1-6 8-8 11l-16 19c0-6 10-13 12-19-3 3-6 7-10 10 3-6 7-12 9-19l-1-1c-1-3-3-6-4-9h-1l-2 2-10 13c-6 10-12 20-16 32 1-10 4-20 9-29 3-7 8-13 12-20 1-3 4-6 3-9 2-2 3-6 4-8l5-14c1-3 1-6 2-9 3-14 15-25 23-37 6-9 11-19 13-30 1-1 1-3 1-5l2-27c-1-3 0-8-1-10v-1c-2-4-3-9-4-14l-1-3z"></path><path d="M409 505h0c1-2 2-2 3-3-1 2-3 8-4 9h-1c-2 3-4 9-8 11l10-17z" class="H"></path><path d="M406 380c4 3 8 17 9 22 0 2 0 4 1 5v1c1 1 0 5 0 7-1-5 0-12-3-16l-1-1c0-2-1-3-2-4-2-4-3-9-4-14z" class="C"></path><path d="M365 546c6-8 10-16 15-23l10-15c1-2 3-5 4-7 2-4 7-10 10-11l-2 3-9 15-8 12c-1 3-3 6-4 9l-1 1-5 5c-2 3-5 10-9 11h-1z" class="K"></path><path d="M414 474c1-3 3-5 3-8 2-8 2-16 4-23 0 6-1 13-2 19 0 8-1 16-3 23-1 2-1 5-2 7s-3 4-4 6l-15 28c-2 3-4 6-5 9l-2 4-1 4 1 1c-1 1-2 3-2 5l1-1c0 3-1 5-1 8l-2-2c-2 1-6 8-8 11l-16 19c0-6 10-13 12-19l3-6c2-5 5-10 7-15 3-8 7-16 11-23l3-6h0c0-3 2-5 3-8 1-2 2-6 3-8s2-4 3-5c2-2 3-4 5-6 2-3 5-10 4-14z" class="F"></path><path d="M388 539l-1 4 1 1c-1 1-2 3-2 5-2 3-5 7-8 10h0c1-2 3-4 4-6 2-5 3-10 6-14z" class="H"></path><path d="M414 474c1-3 3-5 3-8 2-8 2-16 4-23 0 6-1 13-2 19v1c-1 10-5 19-7 28l-1 1c0 1 0 1-1 2l-1 1c0 1-1 2-2 4-5 4-7 12-11 16h0c0-3 2-5 3-8 1-2 2-6 3-8s2-4 3-5c2-2 3-4 5-6 2-3 5-10 4-14z" class="L"></path><path d="M414 474c1 4-2 11-4 14-2 2-3 4-5 6-1 1-2 3-3 5s-2 6-3 8c-1 3-3 5-3 8h0l-3 6c-4 7-8 15-11 23-2 5-5 10-7 15l-3 6c-3 3-6 7-10 10 3-6 7-12 9-19l-1-1c-1-3-3-6-4-9 4-1 7-8 9-11l5-5 1-1c1-3 3-6 4-9l8-12 9-15 2-3c5-5 7-10 10-16z" class="P"></path><path d="M402 493l1 1c-3 5-5 11-8 16l-19 38-3 4c0 1-1 3-2 4l-1-1c-1-3-3-6-4-9 4-1 7-8 9-11l5-5 1-1c1-3 3-6 4-9l8-12 9-15z" class="S"></path><path d="M393 508c0 3-2 4-2 7 0 1-1 2-1 3h-1c0 2-2 2-2 4l-4 10c-2 2-3 4-4 6-1 3-2 5-4 7h0c1-4 3-7 4-10h0c-1-1 2-5 2-6 1-3 3-6 4-9l8-12z" class="E"></path><path d="M381 529c0 1-3 5-2 6h0c-1 3-3 6-4 10h0c-1 3-1 5-2 7 0 1-1 3-2 4l-1-1c-1-3-3-6-4-9 4-1 7-8 9-11l5-5 1-1z" class="P"></path><path d="M381 529c0 1-3 5-2 6h0c-1 3-3 6-4 10h0c-1 3-1 5-2 7 0 1-1 3-2 4l-1-1c2-3 3-8 4-11 1-5 4-9 6-14l1-1z" class="O"></path><path d="M410 395v-1c1 1 2 2 2 4l1 1c3 4 2 11 3 16 1 16 0 31-4 46v1c-1 4-4 7-6 10-6 9-13 16-18 25-4 6-7 12-10 18-1 4-2 7-4 11-2 3-3 6-5 9s-5 6-6 10c0 1-1 2 0 3l-10 13c-6 10-12 20-16 32 1-10 4-20 9-29 3-7 8-13 12-20 1-3 4-6 3-9 2-2 3-6 4-8l5-14c1-3 1-6 2-9 3-14 15-25 23-37 6-9 11-19 13-30 1-1 1-3 1-5l2-27c-1-3 0-8-1-10z" class="L"></path><path d="M410 395v-1c1 1 2 2 2 4l1 1c3 4 2 11 3 16 1 16 0 31-4 46v1c-1 4-4 7-6 10-6 9-13 16-18 25-4 6-7 12-10 18-1 4-2 7-4 11-2 3-3 6-5 9s-5 6-6 10c0 1-1 2 0 3l-10 13c0-3 1-5 2-7 1-3 3-5 5-7 2-3 4-7 6-11l11-25 6-12c4-7 10-14 15-21s10-16 13-25c4-14 5-27 2-42 0-2 0-5-1-7v-5h-1v6c-1-3 0-8-1-10z" class="I"></path><path d="M411 405v-6h1v5h0c0 4 1 8 1 12 1 7 0 15-1 23-2 14-9 27-17 39-6 8-16 17-18 27-2 6-5 12-7 17l-6 12c-1 2-1 4-2 5-1 2-3 4-4 5 1-3 4-6 3-9 2-2 3-6 4-8l5-14c1-3 1-6 2-9 3-14 15-25 23-37 6-9 11-19 13-30 1-1 1-3 1-5l2-27z" class="E"></path><path d="M408 437c1 2 1 4 1 5-3 11-7 21-14 30-1 2-4 4-5 6-2 3-4 6-6 8-3 4-5 8-7 12-1 2-3 4-4 7l-1-1c3-14 15-25 23-37 6-9 11-19 13-30z" class="O"></path><defs><linearGradient id="X" x1="356.348" y1="395.182" x2="395.725" y2="521.861" xlink:href="#B"><stop offset="0" stop-color="#565960"></stop><stop offset="1" stop-color="#707377"></stop></linearGradient></defs><path fill="url(#X)" d="M332 405c9-7 23-11 34-10 14 1 26 6 35 16 3 4 6 10 7 15 0 2 0 5 1 6 0 2 0 4-1 5-2 11-7 21-13 30-8 12-20 23-23 37-1 3-1 6-2 9l-5 14c-1 2-2 6-4 8-2-5-4-10-7-15-1-3-3-6-3-9l-1-1 1-1h2l1-2c2-2 5-5 6-7l-1-1h1c-9-4-17-9-24-16h2v-2c0 1 1 1 2 1v-1h0-3c1-1 3-1 5-2l5-1c-1-1-2-1-2-1-1-1-1-1-1-2l1-1-2-1c-1-2-2-3-3-5l-1-1c0-1-1-1-1-2l2-1h-5c1 0 1 0 2-1-1 0-1-1-2-1-2 0-3 0-4-1-3-2-5-5-7-7-1-2-5-8-5-11v-2h0v-1c-2-5-1-12 1-16 2-5 5-9 8-13 2-2 4-4 4-6z"></path><path d="M379 439l1 1-11 22-9 9c0-1 0-2 1-2 1-2 2-3 2-5v-1l-23 1h-5c1 0 1 0 2-1-1 0-1-1-2-1-2 0-3 0-4-1-3-2-5-5-7-7-1-2-5-8-5-11v-2l1 1v2c1 3 3 6 5 9h1c4 6 9 8 16 9h9 13l5-5c4-5 7-11 9-17l1-1z" class="P"></path><path d="M387 416v-6h1c0 3 2 3 4 5 1 0 1 1 2 1h1l-2-1v-1h1c6 7 8 16 8 25h0c0 6-3 14-6 19-1 1-2 2-3 4 0-3 2-6 3-9 3-7 3-15 1-22 0-3-2-8-4-10-1-3-3-3-6-5zm-47 48l23-1v1c0 2-1 3-2 5-1 0-1 1-1 2-4 3-9 5-13 7-1-1-2-1-2-1-1-1-1-1-1-2l1-1-2-1c-1-2-2-3-3-5l-1-1c0-1-1-1-1-2l2-1z" class="F"></path><path d="M338 483v-2c0 1 1 1 2 1v-1c4 2 7 2 11 3 1 0 2 1 4 1l3 1h2 1c1 0 2 0 4 1v-1h6c1-1 1 0 2-1v1l-1 1-7 8c-1 2-3 3-4 4h-1c-9-4-17-9-24-16h2z" class="O"></path><path d="M338 483v-2c0 1 1 1 2 1v-1c4 2 7 2 11 3 1 0 2 1 4 1l3 1h2 1c1 0 2 0 4 1v-1h6c1-1 1 0 2-1v1l-1 1v-1c-8 4-17 3-25 0-3-1-6-3-9-3h0z" class="P"></path><path d="M351 462c-5-1-10-1-13-3l-5-2v-1c-4-1-7-5-9-9-1-1-1-2-2-4-2-6-3-13 0-20h1c2-5 6-10 10-14 6-5 15-10 23-11h8c8 1 14 4 19 10 3 3 3 6 3 10 0 2-1 4-2 5 0-5 0-10-4-14-5-5-13-8-20-8-3 0-5 1-8 2-9 2-16 5-22 13-3 3-6 7-7 11v1c-1 2-1 7 0 10l1 2v2h1l1 4c1 1 1 2 2 2v1c1 2 3 4 5 4l5 3c1 1 2 1 3 1h1c5 1 13 2 18 0l2-1h1c2 0 3-1 4-2 3-2 5-5 7-9l4-5c-2 6-5 12-9 17l-5 5h-13z" class="K"></path><path d="M393 421c2 2 4 7 4 10 2 7 2 15-1 22-1 3-3 6-3 9-4 6-9 11-14 17-3 2-5 4-8 5-7 1-13-1-20 0-4-1-7-1-11-3h0-3c1-1 3-1 5-2l1 1c7-1 15 0 21-2 3-1 5-2 8-3 9-5 18-15 20-26 2-4 3-9 2-14 0-5 0-8-3-12v-2h1 1z" class="I"></path><path d="M387 416c3 2 5 2 6 5h-1-1v2c3 4 3 7 3 12 1 5 0 10-2 14-2 11-11 21-20 26-3 1-5 2-8 3-6 2-14 1-21 2l-1-1 5-1c4-2 9-4 13-7l9-9 11-22c0-1 1-3 1-4 3-6 5-13 6-20z" class="M"></path><defs><linearGradient id="Y" x1="374.532" y1="423.753" x2="388.262" y2="448.303" xlink:href="#B"><stop offset="0" stop-color="#949394"></stop><stop offset="1" stop-color="#d6d4d6"></stop></linearGradient></defs><path fill="url(#Y)" d="M387 416c3 2 5 2 6 5h-1-1v2c3 4 3 7 3 12 1 5 0 10-2 14h-2v-2h0v-3c0-1 1-1 1-1 1-1 0-4 0-5v-2-1c-1 2-1 4-1 5v2c-2-3 0-7-1-10l1-1v-3l-1-1c0 1-1 2-1 3s-1 3-2 5c-2 5-5 11-8 17-1 2-3 4-3 7l-1-1v1c-2 1-4 2-5 3l11-22c0-1 1-3 1-4 3-6 5-13 6-20z"></path><path d="M367 454c-1 1-2 2-4 2h-1l-2 1c-5 2-13 1-18 0h-1c-1 0-2 0-3-1l-5-3c-2 0-4-2-5-4v-1c-1 0-1-1-2-2l-1-4h-1v-2l-1-2c-1-3-1-8 0-10v-1c1-4 4-8 7-11 6-8 13-11 22-13 3-1 5-2 8-2 7 0 15 3 20 8 4 4 4 9 4 14-1 6-3 12-5 16l-1 1-4 5c-2 4-4 7-7 9z" class="N"></path><path d="M349 405c1 0 2-1 3-1h1 2 1c1-1 6-1 7-1l1 1h2 1v1h2 1c1 1 1 1 2 1l1 1c1 0 1 1 2 1v1l2 1h0c1 1 1 1 1 2l2 2h0c0 3 1 4 2 7 0 2 0 5-1 7 0 1 0 3-1 5h0c-1 4-5 8-6 12-2 4-4 7-7 9h-4v1h-2-1c-3 1-11 2-14 0-2-2-5-1-7-2l-2-2h1c2 0 5 1 7 1 3 1 7 2 11 1 7 0 13-5 17-10s8-14 7-21c0-5-2-10-6-13-8-6-16-5-25-4z" class="I"></path><path d="M367 454c-1 1-2 2-4 2h-1l-2 1c-5 2-13 1-18 0h-1c-1 0-2 0-3-1l-5-3c-2 0-4-2-5-4v-1c-1 0-1-1-2-2l-1-4h-1v-2l-1-2c-1-3-1-8 0-10v-1c1-4 4-8 7-11 6-8 13-11 22-13 3-1 5-2 8-2 7 0 15 3 20 8 4 4 4 9 4 14-1 6-3 12-5 16l-1 1-4 5c1-4 5-8 6-12h0c1-2 1-4 1-5 1-2 1-5 1-7-1-3-2-4-2-7h0l-2-2c0-1 0-1-1-2h0l-2-1v-1c-1 0-1-1-2-1l-1-1c-1 0-1 0-2-1h-1-2v-1h-1-2l-1-1c-1 0-6 0-7 1h-1-2-1c-1 0-2 1-3 1-8 3-15 7-20 14-1 3-2 5-3 8-1 2 0 6 0 8v1l3 6v1c1 2 3 4 5 5 1 1 2 2 3 2v1l2 2c2 1 5 0 7 2 3 2 11 1 14 0h1 2v-1h4z" class="F"></path><defs><linearGradient id="Z" x1="345.078" y1="447.882" x2="358.156" y2="409.671" xlink:href="#B"><stop offset="0" stop-color="#aab2b9"></stop><stop offset="1" stop-color="#fffffd"></stop></linearGradient></defs><path fill="url(#Z)" d="M353 408c5 0 11 0 15 3 4 2 5 6 6 10 1 7-2 14-6 19h0c1 0 2-1 3-1l-1 2-1 1c-1 1-2 2-3 2-2 1-4 3-6 4-4 0-7 1-11 1-1 0-2 0-3-1h-1c-2 0-5-2-7-3v-1c-3-1-6-5-7-8l-2-8 2 2v1 1c2-1 0-5 0-6 0-2 2-5 3-6 4-7 12-10 19-12z"></path><defs><linearGradient id="a" x1="250.396" y1="274.02" x2="393.076" y2="298.987" xlink:href="#B"><stop offset="0" stop-color="#d3d4d5"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#a)" d="M268 247c2-2 3-4 5-5l12-9c2 0 3 2 4 3s2 2 3 2h1c0 1 1 1 2 1h1c1 1 2 1 3 1 4-1 8-2 11-6h0v-1c2-2 3-5 3-8v-3c2-2 4-2 6-3 7-3 16-7 23-8 2 0 3-1 4-1v1h0v2c-7 9-12 20-12 31 0 6 0 13 1 19 1 11 5 22 8 32 8 20 18 39 29 57v2l2 4 6 8c1 1 1 1 1 2-3-2-5-5-7-8l-3-3c-4-4-7-9-11-14l-3-4-9-13-9-12-11-18c-2-4-4-8-7-11-1-2-2-3-3-5l-6 12-2 6c-1 1-1 4-3 5-7 17-13 34-18 52 0 2-1 7-3 9l-16-36-4-8-16-33v-2l-1-3c1-1 2-3 3-4 3-3 6-9 7-13l1-1v5h0c0 3-1 7 0 9h0c1 3 1 5 3 7h1c-2-4-4-7-3-12l1-1c0-1 0-2 1-2-1-2-2-4-2-6-1-5 0-9 3-12l-1-1 2-2c1 0 2-1 3-1v-1z"></path><path d="M348 326h2c3 4 6 8 7 13l-9-13zm-57-66c2 2 3 3 5 3h1c1 2 1 2 3 2 1 0 1 1 2 1h-1l-1 1c-2 0-6 1-8 0s-3-2-3-3c-1-1-1-3 0-4h2z" class="C"></path><path d="M290 247c1 1 0 3 1 5 0 5 6 8 10 9l1 1c1 0 1 0 2 1 0 1 0 2-2 3h0c-1 0-1-1-2-1-2 0-2 0-3-2h-1c-2 0-3-1-5-3-1-1-2-2-2-3-2-4-1-7 1-10z" class="B"></path><path d="M304 263c0 1 0 2-2 3h0c-1 0-1-1-2-1-2 0-2 0-3-2 2 1 4 1 7 0z" class="M"></path><path d="M274 249c2 0 4 1 6 2l1 2h0c0 2 1 4 0 6-1 1-3 2-5 3h-2c-3-1-4-2-5-4 0-2-1-4 0-5v-1c1-1 1 0 2-1s2-1 3-2z" class="J"></path><path d="M273 307c0-2-1-5 0-7h0c1-1 1-2 1-2l2-1c1 0 1 0 2-1h-2c1 0 3-1 4 0h0c3 2 6 3 7 8 1 1 1 2 0 3-3 2-5 4-9 3-2 0-4-1-5-3z" class="Q"></path><path d="M293 283c-1 1-1 2-1 4l1-1v1h0c-1 3-1 5 1 8 1 2 3 4 5 4 2 1 6 0 8-1h3c-1 1-1 4-3 5h0c-3 2-5 3-8 3s-7-2-9-4c-2-3-3-6-3-10 1-4 3-7 6-9z" class="N"></path><defs><linearGradient id="b" x1="269.845" y1="270.246" x2="271.392" y2="287.374" xlink:href="#B"><stop offset="0" stop-color="#a09fa2"></stop><stop offset="1" stop-color="#cacbcd"></stop></linearGradient></defs><path fill="url(#b)" d="M263 270c3 3 6 5 11 5 1 0 3-1 5-1l2 1c0 3-2 8-5 10-1 2-4 4-7 3-2 0-3-1-5-3-2-4-4-7-3-12l1-1c0-1 0-2 1-2z"></path><path d="M293 283c1-1 3-2 5-2 4 0 7 0 10 3 0 1 1 1 1 2h1c1 2 1 3 1 5 0 1 0 1 1 1l-2 6h-3c-2 1-6 2-8 1-2 0-4-2-5-4-2-3-2-5-1-8h0v-1l-1 1c0-2 0-3 1-4z" class="Q"></path><path d="M295 242c4 0 7 0 10 2 2 1 4 4 5 6 0 4 0 7-2 10-1 1-3 2-4 3-1-1-1-1-2-1l-1-1c-4-1-10-4-10-9-1-2 0-4-1-5 1-3 3-4 5-5z" class="O"></path><path d="M290 247c1-3 3-4 5-5 0 1-1 2-1 3 0 0-1 1-1 2-1 1-2 3-2 4s2 4 3 5 3 2 4 3c3 0 6 1 8-2 1 0 2-1 2-3 1-1 1-2 2-4 0 4 0 7-2 10-1 1-3 2-4 3-1-1-1-1-2-1l-1-1c-4-1-10-4-10-9-1-2 0-4-1-5z" class="T"></path><defs><linearGradient id="c" x1="275.163" y1="312.901" x2="277.813" y2="326.551" xlink:href="#B"><stop offset="0" stop-color="#a4a3a5"></stop><stop offset="1" stop-color="#c2c2c4"></stop></linearGradient></defs><path fill="url(#c)" d="M266 320l1 1 1 1v-2-3c0-3-1-8 0-12 0-2 0-4 2-5 0 2 1 5 2 7h1c1 2 3 3 5 3 4 1 6-1 9-3 0 3-1 4-3 6l2 1c-1 5-2 9-6 12-2 2-4 2-7 2l-2-1-1 1-4-8z"></path><path d="M268 247c1 0 2 0 3-1h2c1 0 2 0 4 1h0c-4 1-6 1-8 4-2 2-3 4-2 7 0 2 1 3 3 5 2 1 5 1 8 0 2 0 4-2 5-4s0-4 0-6c-1-1-1-2-2-3v-1c3 4 7 7 7 12 0 2-1 4-2 6h0c-2 3-4 5-7 7-2 0-4 1-5 1-5 0-8-2-11-5-1-2-2-4-2-6-1-5 0-9 3-12l-1-1 2-2c1 0 2-1 3-1v-1z" class="D"></path><path d="M264 252h0v1c-1 3-1 7 0 10s4 5 7 7c4 1 7 0 11-1 1-1 2-2 4-2-2 3-4 5-7 7-2 0-4 1-5 1-5 0-8-2-11-5-1-2-2-4-2-6-1-5 0-9 3-12z" class="K"></path><defs><linearGradient id="d" x1="310.265" y1="589.532" x2="265.788" y2="611.665" xlink:href="#B"><stop offset="0" stop-color="#949ea3"></stop><stop offset="1" stop-color="#d5e2ec"></stop></linearGradient></defs><path fill="url(#d)" d="M145 219c1 1 2 3 3 5 1 0 3 4 4 5 1-2 1-5 2-7v-1c0 4-1 9 0 12 1 2 2 6 4 8l13 23-1-5c-2-8-1-17 0-26 1-3 2-8 4-10l1-1 2-4c1 2 1 7 0 10v1h1v-1 1c-2 12-4 24-2 36 1 5 3 11 4 16 2 2 3 5 4 8l24 46c1 3 3 9 6 12h0c2 2 3 8 6 8 4 7 6 15 10 23l34 68 7 14c1 2 2 5 4 7h0c1 2 1 5 2 7 0 4 1 7 2 11 4 12 10 23 17 33 2 3 4 5 6 7 3 3 5 6 8 8l1 3c5 4 11 7 15 12 1 1 1 1 3 2v1c-3 5-1 12-6 17 0 4 4 10 6 14l12 25h0l-1 2-1 1 1 1 2-3 165 346c-5-1-10 1-16 1-3 1-7 0-10 1l-10-1c-3 1-7 0-10 0s-7 0-10-1h1c2 0 3 1 5 0-1-1 0-1 0-2-1-1-1-2-1-3-1-1 0-3 0-5h-1v-2-1c-1-1-1-1-1-2l-2-4h0l-2-2v-1l-2-3-1-3h-1c0-1-1-1-1-2-1-2-2-4-3-5-1-2-3-3-3-5-1-1-2-3-3-4l-2-3c-2-4-4-8-7-11-1-2-1-3-2-4 0-1-1-1-1-2-1-1-1-1-1-2l-2-2c-1-1-1-2-2-3l-1-2-2-3c-1-1-1-3-2-4-2-2-3-4-4-6s-2-3-3-4c-1-2-1-3-2-4l-1-2c-1-1-2-3-3-4s-1-3-2-4-2-3-3-4c-1-2-3-5-4-7h0l-1-3-4-5v-1l-1-2-5-8-3-5c-3-5-6-12-9-17v-2c-1-1-1-1-1-2-1 0-1-1-1-2-2-4-5-8-6-13l-3-4c0-2-1-3-1-4l-2-4-1-3-3-7c-2-2-3-5-4-7-1-1-1-2-1-3h0l-2-2v-2l-9-19c-1 0-1-1-2-2v-1c-1-1-2-2-2-3l-6-11-2-6c-1 0-1-1-2-2 0 0 0-1-1-2l-2-3-1-3c-1-1-1-2-2-3h0l-1-3-5-7c-1-1-1-2-2-3 0 0-1-1-1-2l-2-2c0-1 0-1-1-2l-4-6c-1-1-1-2-2-3l-2-3c-1-1-2-3-3-4l-2-3-1-2h0l-2-2c0-1 0-1-1-2l-3-5-1-1c-1-1-1-2-2-3 0-1-1-1-1-2l-1-1-1-2v-1l-2-2-3-6-2-3c-1-1-1-2-2-3l-1-2c-1-1-1-2-2-3l-2-4-4-8-1-1c0-1-1-3-2-4v-1l-3-5c0-1 0-2-1-3h0c0-2-1-3-2-5 0-1-1-2-1-3h0c-1-1 0-1-1-2 0-2-1-2-1-3-2-2-1-4-2-5-1-2-1-4-2-6l-1-1v-2c-1-1-1-2-1-3v-2c-1-1-1-3-2-5l-1-1v-1c0-1 0-2-1-3v-1h0l-1-1v-3c-1-1-1-1-1-2v-1c0-1 0-1-1-2 0-1 0-3-1-4l-1-1v-2c-1-1 0-1-1-2l-1-3-1-4v-1c-1-1-1-1-1-2l-1-4c-1-1-2-3-2-4v-1c-1 0-1-1-2-2 0-1 0-2-1-2v-2c-1-1-1-2-2-2 0-3-2-5-2-7-1-1-1-2-2-3v-1l-2-4c-1-1-1-1-1-2h0l-2-3v-1c-1-1-1-3-2-4l-5-9c0-2-1-3-1-4v-1l-1-1c-1-1-1-2-2-3h0l-1-3h-1c0-1 0-2-1-3h0l-1-3h-1v-2c-1-1-1-1-1-2-1-1-1-2-2-4l-6-13c-1-1-3-3-3-5h0l-10-20-2-2v-1l-2-3-3-7-4-8-1-1c-1-1-1-2-2-3v-1l-4-7-1-2c0-1 0-1-1-1v-2l-4-6-2-6-1-1c-1-1-1-2-2-2v-2l-1-2c-1-1-2-2-2-3l-1-3c-1-1-2-2-2-3l-1-2c-1-1-2-2-2-3l-2-4-1-2-6-10-1-3-4-5-2-4-1-2-9-15c0-1 0-1-1-2-2-2-3-5-5-7l-4-7c-1-1-1-1-1-3-1-1 0-2 0-3l-1-1c0-1 1-2 1-2v-2l1-1c0-1 0-3 1-4h1c1-2 2-3 3-5h-1l1-1h-1l-1 1h0-1v-1c6-5 13-10 20-13l13-7c3-2 9-3 11-5h0z"></path><defs><linearGradient id="e" x1="332.372" y1="584.404" x2="300.765" y2="600.022" xlink:href="#B"><stop offset="0" stop-color="#595a5d"></stop><stop offset="1" stop-color="#9ea9ae"></stop></linearGradient></defs><path fill="url(#e)" d="M177 218c1 2 1 7 0 10v1h1v-1 1c-2 12-4 24-2 36 1 5 3 11 4 16 2 2 3 5 4 8l24 46c1 3 3 9 6 12h0c2 2 3 8 6 8 4 7 6 15 10 23l34 68 7 14c1 2 2 5 4 7h0c1 2 1 5 2 7 0 4 1 7 2 11 4 12 10 23 17 33 2 3 4 5 6 7 3 3 5 6 8 8l1 3c5 4 11 7 15 12 1 1 1 1 3 2v1c-3 5-1 12-6 17 0 4 4 10 6 14l12 25h0l-1 2-1 1 1 1 2-3 165 346c-5-1-10 1-16 1-3 1-7 0-10 1l-10-1c3 0 5 1 7-1v-1c1-2 1-3 1-4v-1-1c1-1 1-2 1-3v-1-5l-1-1c0-1 0-2-1-3h0l-4-8c0-1 0-2-1-3h0l-1-2c0-1-1-2-1-2 0-1 0-2-1-2 0-1-1-1-1-2l-2-3c0-1 0-2-1-3h0l-1-2c-1-1-1-2-2-3l-6-13-3-5c0-1-1-2-2-2v-2l-7-13c-1-3-3-6-4-8l-1-2-2-4c-1-1-2-2-2-3l-1-1v-2c-1-1-2-2-2-3l-1-2-1-1c-1-1-1-1-1-2-1-1 0-1-1-2l-5-11-1-1c-1-1-1-2-2-3v-1c-2-4-4-9-7-13l-1-3-1-1v-1l-1-1c0-1-1-2-1-3h0l-10-19c0-1 0-1-1-2h0l-1-3-2-4c-1-1-1-2-1-3h0l-1-2c-1-1-1-1-1-2h0c-3-5-6-10-8-16l-1-1-1-3-1-1v-2h-1c0-1 0-1-1-2v-2l-2-3c-1-2-1-3-2-4v-1s-1-1-1-2c0 0-1-1-1-2 0 0 0-1-1-1v-2h-1c0-1-1-2-1-3s-1-2-1-3l-1-1v-1l-1-1c-1-1-1-1-1-2-1-1-1-2-1-2l-1-2c0-1-1-2-2-3h0c0-1-1-2-1-3h0c-1-1-1-2-2-3h0l-2-5-10-19v-2c-1-1-1-1-1-2-1-1-1-1-1-2-1-1-1-1-1-2l-1-1c0-1 0-1-1-2 0-1 0 0-1-1l-3-8c-1-1-2-2-2-3l-2-4-1-2-1-1c-1-2-2-3-2-4l-1-1-1-2-1-1c0-1-1-2-1-3v-1c-1-1 0 1-1-1-4-5-6-11-10-16l-2-3-1-2-2-2c0-1 0-1-1-2 0 0-1-1-1-2l-1-2-2-2c-1-1 1 0-1-1l-2-2c0-1-1-2-2-3h0c-1-2-2-3-3-4 0-1-1-2-1-2v-1l-2-3-1-3c-1 0-1-1-2-2l-1-3c-1-1-2-2-2-3l-2-3c-1-1-1-2-2-3h0l-1-3-1-1v-1s0-1-1-2v-1l-1-1c-1-1-1-2-1-3l-1-1c0-1-1-1-1-3 0 0 0-1-1-1v-1l-1-2c0-1-1-2-1-3s-1-2-1-3h0l-1-1v-2l-1-1-3-12c-1 0-1-1-1-2h0l-1-1v-2c-1-1-1-2-1-3s0-1-1-2v-2-1h-1v-2l-1-2v-2l-1-1v-2l-1-4-1-1v-1c0-1 0-1-1-2 0-1-1-4-1-5-1-3-1-4-2-6 0-2-1-3-2-4 0-2-1-3-1-4s0-2-1-2v-1l-1-2c0-1-1-2-1-3v-1l-2-2v-2c-1-1-1-1-1-2h0l-2-2v-2c-1-1-1-2-1-3h0l-2-2-3-8c-1-1-1-1-1-2h0l-2-2v-1c0-1 0-1-1-2v-2l-2-3c-1-1-1-2-1-3h0l-2-2v-2h-1v-2c-1-1-1-2-2-3h0c0-1-1-1-1-3h0l-2-3h0c-1-1-1-2-1-3h0l-4-6c0-1 0-2-1-3h0c0-1-1-1-1-3h0c-1-1-1-1-2-3h0c0-1-1-1-1-3h0l-2-2v-1c0-1 0-1-1-2v-1l-4-7c0-1 0-1-1-2h0c0-1-1-2-1-3h0l-2-3v-1l-1-1c0-1 0-1-1-2v-1l-7-13-1-2c0-1 0-1-1-1v-2l-5-8v-1c-1-1-1-2-2-3h0c0-1-1-2-1-3h0c-1-1-1-2-2-3h0l-1-2c0-1 0-1-1-2v-1l-4-7-3-5v-1c-1-1-1-1-1-2h0l-5-8v-2c-1-1-1-1-1-2h0c-1-1-1-1-2-3h0l-1-2c0-1 0-1-1-1v-2l-2-2-3-6-1-2-4-7-4-6-3-4s-1-1-1-2l-1-2c-1-1-2-1-2-2l-1-1c-1-1-2-2-3-4h0c-3-1-5-4-7-6-2-1-3-3-4-5l-1-1c0-2 0-4 1-5 0-1 1-2 1-3h-1c3-3 7-5 11-7 3-1 9-2 11-4h0c1 0 3 4 4 5 1-2 1-5 2-7v-1c0 4-1 9 0 12 1 2 2 6 4 8l13 23-1-5c-2-8-1-17 0-26 1-3 2-8 4-10l1-1 2-4z"></path><defs><linearGradient id="f" x1="314.196" y1="555.968" x2="318.948" y2="569.983" xlink:href="#B"><stop offset="0" stop-color="#4b4b52"></stop><stop offset="1" stop-color="#66686b"></stop></linearGradient></defs><path fill="url(#f)" d="M314 551l3 6c1-2 3-4 4-5 0 2 1 4-1 7 0 1-1 3-2 4-2 3-3 8-4 12h0 0c-1 0-1 1-1 2h-1v2 1c-1 2-1 3-1 4l-2 4h-1c0 1-1 3-2 3-1 1-2 1-2 1 0-1 0-1 1-2 0-1 0-2 1-2l1-2v-1c1-1 0-1 1-1v-2c1-1 1-1 1-2 1-1 0-2 0-3 1-1 1-1 1-2 1-1 0-2 0-3 1-1 1-2 1-3v-2c1-1 1-1 1-2v-2h1v-2l1-1c0-1 0-1 1-2 2-2-1-5-1-7z"></path><path d="M326 548c1 1 1 1 3 2v1c-3 5-1 12-6 17-3 1-4 3-6 6 0 1 1 1 0 2s-1 2-2 3v1c-1 1-1 1-1 2l-1 1h-1l1-1v-3-1c0-1 1-1 1-2v-1h0c1-4 2-9 4-12 1-1 2-3 2-4 2-3 1-5 1-7l4-3 1-1z" class="K"></path><path d="M321 552l4-3c0 2-1 3-1 6-1 1 0 3-1 5v-3c-1 1-2 1-3 2 2-3 1-5 1-7z" class="P"></path><path d="M314 551l-12-23 8 7 1 1c5 4 11 7 15 12l-1 1-4 3c-1 1-3 3-4 5l-3-6z" class="B"></path><defs><linearGradient id="g" x1="165.884" y1="262.528" x2="187.616" y2="241.472" xlink:href="#B"><stop offset="0" stop-color="#878589"></stop><stop offset="1" stop-color="#aeafb1"></stop></linearGradient></defs><path fill="url(#g)" d="M177 218c1 2 1 7 0 10v1h1v-1 1c-2 12-4 24-2 36 1 5 3 11 4 16 0-1 0-1-1-1l-3-6c-1-2-3-6-3-9-1-2-1-5-1-8-1-6-1-12 0-18 0-6 2-11 3-17l2-4z"></path><path d="M718 163c5 0 10-1 14 0h17c20 8 40 18 55 35 5 4 8 9 12 15 1 2 3 3 4 5l3 9c5 11 7 22 7 35 0 2 0 6-1 8v2c-1 1-1 2 0 3-2 4-4 9-6 13v-6l-1 4h0l-1 1v4 1 14c1-2 1-3 1-5-1-1 0-3 0-4 1-2 0-6 0-8l1-1-1 2 1 1v3 1c0 1 0 2 1 3h0v4 3l1 1v2 1c1 3 0 6 0 8h0v-1-4c-1 1-1 2-2 3h0v1l-1 1v1l1 1v2c0 1 0 1-1 2v2c0 1 0 1-1 2v2c-1 1 0 0-1 2h0c0 2-1 2-1 3 0 2-1 3-1 4l-1 2c-1 1-1 0-1 1s0 1-1 2-2 3-3 5c0 1-1 2-2 3l-1 3c-1 1-1 1-1 2l-1 1v1l-3 3v1h0c-1 1-1 2-2 3l-2 4-1 1v1c-1 1-1 3-2 5l-4 5-1 2-1 1-2 2v3l-1 1c0 1-1 2-2 3l-1 2-3 5c0 1-1 2-1 2v1l-1 1v1c0 1-1 2-1 3l-1 1v1c0 1-1 2-1 2v1h-1v1 1c-1 1 0 1-1 2 0 1-1 2-1 3l-1 2-4 8-1 3c0 1 0 1-1 2l-1 3-3 7-1 2-1 3-2 5c-1 1-1 2-2 4l-2 3c0 1-1 3-1 4l-3 5-3 7c0 1-1 2-1 3l-4 7c0 1-1 2-1 4l-6 11c0 1 0 0-1 1 0 1 0 1-1 2 0 2-2 5-3 7v1l-1 3c-1 0-1 1-1 1l-1 2-1 1-6 12c-1 4-3 7-4 10l-3 5c0 1-1 3-1 4l-1 2c-1 1-1 1-1 2 0 0-1 1-1 2l-1 2-2 4-5 11-1 2c0 1 0 1-1 2 0 1 0 2-1 3l-1 2-1 4c-1 1-1 2-2 3l-1 2-2 5c-2 4-3 8-5 11-1 3-3 6-4 9v1h-1l-2 5-1 3-3 7-2 3-1 4-2 3-1 4v1l-4 8-1 1-2 7-1 1-2 4-4 11-1 1v1l-4 9-1 1c0 1 0 2-1 3l-2 4-3 7c-2 3-3 7-5 10l-3 7-1 2c0 1-1 2-1 3l-3 5c0 1-1 2-1 4l-2 3c-1 1-1 3-2 4l-7 14c0 1-1 2-1 4l-12 22v2l-1 1-1 2-1 1-6 13c-1 1-2 3-2 4-1 1-1 2-2 3l-5 10-2 3c0 1-1 2-2 3l-1 4-3 4-2 5-1 1c0 1-1 2-2 3l-1 2h0c0 2-1 3-2 5l-6 10h0c0 1-1 2-1 2l-1 3h-1c0 1-1 2-2 3l-1 3c0 1-1 2-2 3l-1 4-7 10-1 3-5 8-1 3h-1c0 1-1 2-2 3l-1 3h0c-1 2-2 3-3 5l-2 2-1 3v1l-1 1-1 3h-1c0 2-1 3-2 5l-1 4s-1 1-1 2v2c0 1 0 2-1 4h0c1 1 1 2 1 3v1 1c1 1 2 2 2 3-2-1-5-2-7-3-1 0-2 0-2 1l-1-1h-2v-3h0l1-1c0-1 1-1 1-2v-1c1-1 1-2 1-3h0c-4 6-6 14-10 19h-1l-1-1-165-346c6-7 13-15 18-24l16-19c2-3 6-10 8-11l2 2c0-3 1-5 1-8l-1 1c0-2 1-4 2-5l-1-1 1-4 2-4c2-1 3-3 4-5 2-3 4-5 5-8 4-2 6-8 8-11h1c1-1 3-7 4-9-1 1-2 1-3 3h0l-1-1 1-1c1 0 1-1 2-2 0-1 1-2 1-3h1c0-1 1-1 1-2l3-6c1-1 1-1 1-2v-2c1-2 1-3 1-4v-2l1-1v-5c1-1 1 0 1-1 1-6 1-11 1-17 0-5 1-11 0-17v-1c0-1 0-3-1-5h0c1 1 1 1 3 1-2-1-3-4-4-6v-6-11c0-5-1-11-2-16 0-1 1-2 0-3 0-1-1-1 0-2 2 1 2 3 3 4h1c1 0 3 2 3 3 1 1 3 2 4 3 1 2 3 3 4 4 2 3 5 5 8 7 4 4 7 8 12 11l36 33c2 2 3 3 4 5l9 10c1 2 2 5 4 7 6 8 8 18 13 26v2c1 5 4 11 7 16 1 1 1 3 2 4 3 6 10 16 17 17h0l1 1 1-1c1 0 2 0 3 2h0c0 2 2 5 3 7 0 1 0 1 1 2 0 1 1 2 2 3 2 4 4 9 5 13 2-1 5-8 6-11l18-37 59-123 17-34c3-5 6-11 8-16 3-5 6-12 9-17 4-11 9-21 11-32 5-26-1-53-15-75l-6-8c-2-1-3-3-4-5 0-1-3-2-3-2-2-2-4-4-6-5 2-2 3-1 5-1l13 4c4 1 7 2 10 4l1-1c-1-1-2-2-3-2-2-2-5-3-7-3-7-3-13-5-20-7-3-1-7-2-11-2-1 0-2-1-3-1-2-1-5-2-7-3-6-2-12-2-18-4l-12-1c-10 0-20 1-29 2l-1-10 1-4 8-9 1-1 2-2h15 6c9-1 18 0 26 0h49c3 0 20-1 22 0h6 9z" class="J"></path><path d="M693 404c3-2 5-3 8-4 1 0 2 0 2 1-2 1-4 2-5 3h-5z" class="S"></path><path d="M484 786l1 1c2 3 3 4 6 6 2 2 4 4 6 5v1c-2 0-3-1-5-1-4-3-7-7-8-12z" class="E"></path><path d="M760 349c1-1 2-3 4-4-2 6-4 13-7 18l-1-1h1l1-3-1-1v1h-2v-1c1-2 2-3 2-4l1-1 1-2c0-1 0-1 1-2z" class="D"></path><path d="M560 778c2 1 3 4 5 5 2 2 8 5 11 4v1c-4 1-7 1-10-1-4-2-5-5-6-9z" class="I"></path><path d="M560 769c2 2 1 3 2 5 0 2 1 4 3 6 1 2 4 5 7 6h1 1c1 1 1 1 2 1-3 1-9-2-11-4-2-1-3-4-5-5-1-3 0-6 0-9z" class="G"></path><path d="M540 707c2 0 3 1 4 2v3c-1 2-2 2-3 3h-2c-1-1-2-2-2-3v-3c1-1 1-2 3-2z" class="C"></path><path d="M469 678c5-3 11-4 17-3 4 0 7 2 10 4h-3l-4-1c-6-3-12-2-17 1l-3-1z" class="F"></path><path d="M526 802c1-1 2-2 3-2v1c0 2-1 5 1 7 2 4 7 6 11 7l-1 1c-3 1-7-1-9-2 0 0-3-1-3-2-1-3-1-7-1-9l-1-1z" class="D"></path><path d="M720 260c0-3 0-6 1-9 1 2 1 4 2 5h1v4c-2 4-1 8 1 12v1h-1l-5 8v1c-2 2-4 4-6 7l-1-1c1-1 3-2 4-3v-1l1-1v-1c1 0 1-1 2-1v-1l1-1v-1l1-1v-1l1-1c0-1 0-1 1-2h0c0-1 0-2-1-2v-1c0-4-1-7-2-10z" class="H"></path><path d="M599 605c3-3 7-5 11-5 6-2 13 0 18 3l-2 2c-5-3-12-4-18-3-3 1-5 2-7 4l-2-1z" class="N"></path><path d="M719 243c0-2 1-3 1-4 1 0 1 1 1 2h0v2c-1 5-2 12-1 17h0c1 3 2 6 2 10h0l-1 3-1-1v-1-3c-1-2-1-2-1-4-1-1 0-1 0-2-1-1-1-2-1-3-1-2 0-8 0-10 1-1 0-2 0-3 1-1 1-1 1-3h0zm99 63l1-3 1 1v7l-1 3c0 3-3 7-2 11-1 3-3 7-4 10-2 4-3 8-5 12 0-2 0-1 1-2v-2-1c1-1 2-3 2-5l1-1h-1v1l-2 2c2-4 4-8 5-12s2-9 2-13c1-2 1-4 1-6h1v1c1-1 0-2 0-3z" class="G"></path><path d="M714 312c0 2-2 4-3 5 0-3 0-5 2-8v-2c1-4 4-9 6-13h1l2-4c0 2 0 3-1 4v1c0 2-2 4-2 6s0 3 1 4c-1 2-4 5-6 7z" class="C"></path><path d="M714 312c0-2 2-5 3-8 0-1 1-2 2-3 0 2 0 3 1 4-1 2-4 5-6 7z" class="D"></path><path d="M821 283l1 3-1 1v4 1 14c0 6-2 13-4 19-1-4 2-8 2-11l1-3v-7l-1-1-1 3c0 1 1 2 0 3v-1h-1l1-14-1-4 2-1v-4h1 1v-2z" class="N"></path><path d="M821 283l1 3-1 1c-3 3-1 8-2 12v4l-1 1v2c0 1 1 2 0 3v-1h-1l1-14-1-4 2-1v-4h1 1v-2z" class="I"></path><path d="M817 290l2-1v-4h1 1l-2 2v7h-1 0l-1-4z" class="F"></path><path d="M483 775l1 1c1 1 1 3 1 5 3 7 8 13 16 15 2 1 6 1 8 2-4 1-7 2-12 1h0v-1c-2-1-4-3-6-5-3-2-4-3-6-6l-1-1c-2-3-1-7-1-11z" class="P"></path><path d="M799 251v-1h1l1 1 2-1c7 8 13 16 16 26-1 0-1-1-2-1h-2c-3-6-5-14-11-18-1-2-3-4-4-6h-1zM628 603c4 4 7 7 9 12v1c2 5 1 12-2 17-2 5-7 10-13 12h-1c3-3 8-4 9-8 4-5 6-11 5-17-1-7-3-11-9-15l2-2z" class="L"></path><defs><linearGradient id="h" x1="581.339" y1="785.707" x2="580.202" y2="770.829" xlink:href="#B"><stop offset="0" stop-color="#afaeae"></stop><stop offset="1" stop-color="#d3d3d4"></stop></linearGradient></defs><path fill="url(#h)" d="M583 772v-3l-1-1v-2h0c0 1 1 1 1 1v-1h0c3 4 3 8 2 12s-4 8-7 9c-1 1-1 1-2 1v-1c-1 0-1 0-2-1h-1-1c-3-1-6-4-7-6l2-1c1 2 2 3 4 4 2 0 5 0 7-1s4-4 5-7l1-1v-2h-1z"></path><path d="M484 743c2 5 5 7 10 9 3 1 6 1 8 0h2 1c0 1-1 1-2 2h-1c0 1-1 2-2 2-3 1-8 1-11 0h0-1c-2-1-4-3-4-5-1-2-1-6 0-8z" class="C"></path><path d="M630 517c2 4 4 6 8 8 3 1 6 0 9-1l2-1 2-2v1c0 2-1 5-3 6-4 3-8 3-11 2-3 0-6-2-7-5-1-2-1-6 0-7v-1z" class="D"></path><path d="M630 517c2 4 4 6 8 8 3 1 6 0 9-1l2-1c0 2-1 3-3 4-2 2-6 2-9 1h0c-1-1-2-1-2-1-2-1-4-3-4-5l-1-2v-2-1z" class="G"></path><path d="M485 604c-3 8-6 14-10 21-8 13-17 26-23 40v1l-2 6s-1 1-1 2h0l-1 5h0v-3c0-1 0-2 1-3 0-1 0-3 1-5h0c2-6 5-11 8-16 0-1 0 0 1-1 1-3 2-5 3-7s2-3 3-5v-1c-1 0-1-1-1-1l5-5v-1c4-8 8-17 10-26v1h0v3h0l-1 1 1 1h0c1-1 2-2 2-3l4-4z" class="C"></path><path d="M599 605l2 1c-5 5-8 9-8 17 0 5 3 12 7 16 5 4 9 5 15 5s11-3 15-7c-1 4-6 5-9 8-1 1-3 1-5 1-5 1-11 0-15-4-5-4-9-11-10-17 0-9 2-14 8-20z" class="E"></path><path d="M526 802l1 1c0 2 0 6 1 9 0 1 3 2 3 2 2 1 6 3 9 2l1-1 7-3c-2 4-5 8-10 9h0c-3 1-5 1-8-1s-5-5-6-9c-1-3 0-6 2-9z" class="G"></path><path d="M565 763c4-3 6-3 10-3-1 1 0 1-1 1s-2 0-3 1h-1c-1 1-3 3-4 5v1 3 1c0 1 0 1 1 2h0c0 1 0 1 1 2v1c1 1 2 2 3 2 2 1 4 0 5 0h1l1-1c1 0 2-2 3-3s1-2 2-3h1v2l-1 1c-1 3-3 6-5 7s-5 1-7 1c-2-1-3-2-4-4l-2 1c-2-2-3-4-3-6-1-2 0-3-2-5 1-3 3-5 5-6z" class="C"></path><path d="M565 763h1l-1 1c-1 3-2 8-1 11 1 2 2 3 3 4l-2 1c-2-2-3-4-3-6-1-2 0-3-2-5 1-3 3-5 5-6z" class="H"></path><path d="M529 706h2c0 3-1 7 1 9v1c0 1 1 2 2 3l3 1c2 1 5 1 7 0h1c0-1 1-1 2-2h0c2-2 2-4 3-6l1 1c0 2 0 4-1 6s-2 4-4 5c-2 2-6 3-9 2-3 0-6-2-8-5s-3-7-2-10v-1c1-1 1-2 2-4zm-43 23v1c1 2 0 4 1 5 0 4 3 8 6 10s7 3 11 2c2 0 3-1 5-2h2c-2 4-4 5-9 7-2 1-5 1-8 0-5-2-8-4-10-9v-1c-1-5-1-9 2-13z" class="F"></path><path d="M376 565c2-3 6-10 8-11l2 2c1 7 3 12 9 18l4 3h-1l-2-1h0c-3-2-6-6-8-9l-1-2v-1c-1 0-1-1-1-2-1 0-2-1-2-2l-2 1c0 1 0 3 1 4-1 1-1 1-1 2l1 1v2 3l1 1h0-1v1 4h1v2c1 0 1 1 1 2h0l1 1v1l1 1v2l1 1c1 1 0 0 1 2l1 1 4 10 2 4c1 1 1 2 2 3v1h0c1 1 1 2 1 3l1 1v1l1 2 2 4c1 1 0 1 1 2h0v3c-3-6-6-13-8-19l-20-42z" class="G"></path><path d="M401 533h-1l1-1c4-3 6-7 9-11 4-4 15-14 20-13 2 0 3 1 4 2 3 4 5 11 4 15v1c0 2 0 4-1 6 1 1 1 1 1 2h1c-1 5-4 11-7 15 1-6 3-11 4-17 2-5 1-15-3-19 0-1-1-2-3-2s-4 1-6 2c-8 5-13 13-20 19l-3 1z" class="C"></path><path d="M518 767l4 2c3 2 7 4 11 4h6 1c0 1 1 1 2 1h-2 7v1c-1 2-1 3-3 4l-2 1c-1 1-1 1-2 1h-2l-1 1c-2 0-3 0-5-1l-1-1h-3l-2-2c-1 0-2-1-3-2-1 0-1 0-2-1h0l-1 2-2-10z" class="D"></path><path d="M469 678l3 1-6 4c-2 2-4 5-5 8v1c-1 3-1 7 0 10v2c0 1 1 2 2 3h0v1l1 1c3 4 8 7 14 8s12-1 18-5c1-1 2-2 2-3l1-1c3-5 4-12 2-17-1-5-5-9-8-12h3l3 3c4 5 5 11 5 17s-3 11-8 15-10 6-16 5c-6 0-12-3-16-7-3-5-6-11-5-17 1-8 4-12 10-17z" class="E"></path><path d="M801 268l2 6c0 9 1 18 0 27 0 9-3 16-4 25-1 6-5 12-7 18-1 2-1 3-1 5-1 0-1 1-2 2v1 1c0 1 0 1-1 2s-1 2-1 3h-1l-1-1c-1 1-1 2-2 2h-1c0-1 0-2 1-2v-1l5-9c1-1 1-1 1-2l1-1v-1h-1v1h-1c1-1 1-1 1-2h0l2-3v-1l1-1c2-7 5-13 7-19v-2-1-1c1-1 1-2 1-3v-2c1-2 1-3 1-4v-3c2-2 1-19 1-22-2-2-1-9-1-12z" class="M"></path><path d="M803 274c3 8 2 19 2 28 0 4-1 9-1 13-2 9-7 17-4 26v4 1 1 2 1c-1 4-3 11-6 13h0c1-2 1-4 1-6 1-2 0-9 0-11v-1c0-1 0-2-1-3v-1c-1 1 0 1-1 2l-1 2c0 2 0 2-1 3v1c0-2 0-3 1-5 2-6 6-12 7-18 1-9 4-16 4-25 1-9 0-18 0-27z" class="N"></path><path d="M756 369c2-3 2-6 4-9 2-6 5-12 8-17 2-6 4-13 6-19 1-3 4-7 5-10 1-1 1-2 2-3v3c0 3-1 8-3 10-1 2-1 4-2 6-4 12-9 25-15 37 0 1 0 0 1 1l1 1h-4c-2 1-4 4-5 6l-2 6c0-4 2-8 4-12zM486 729c2-3 5-5 7-6 1 0 1 0 2 1-1 1-2 2-2 4h0c-1 2-1 3-1 4v2 1c0 1 1 2 2 3h1c1 0 0 0 1 1 1 0 2 1 3 1l1 1 1 1h-2c-1-1-3-1-3-1 0 1 1 1 1 2h2l1-1h2l3-3c2-1 3-2 5-3h1 0c1 1 1 2 1 3v-3l-1-1v-1h1c1 3 1 7 0 10l-1 1h-2c-2 1-3 2-5 2-4 1-8 0-11-2s-6-6-6-10c-1-1 0-3-1-5v-1z" class="M"></path><defs><linearGradient id="i" x1="729.053" y1="302.172" x2="727.947" y2="332.328" xlink:href="#B"><stop offset="0" stop-color="#8a8b8e"></stop><stop offset="1" stop-color="#b4b3b6"></stop></linearGradient></defs><path fill="url(#i)" d="M742 296c1-2 2-7 4-9 0 2-3 7-2 9 2-3 3-7 6-9-5 13-13 24-21 34-5 6-9 12-14 17l11-21c3-3 5-9 7-14 2-2 4-5 6-8 1 0 1 0 3 1z"></path><path d="M742 296c1-2 2-7 4-9 0 2-3 7-2 9-2 3-4 6-6 8 1-3 2-5 4-8z" class="L"></path><path d="M754 229v-1c4 2 6 6 7 10 5 21-1 49-12 68-4 6-9 11-13 17-3 4-9 8-10 12-4 3-6 6-10 8l1-2c1-2 3-4 4-5l14-16 9-12c1 0 1-1 1-1 5-10 12-21 14-32 0-1 1-2 1-4h0v-4l1-4c1-7 0-15-1-21 0-4-1-9-4-11h-2v-2z" class="C"></path><path d="M491 763c-1 4-2 8-2 12 1 3 2 7 5 9v1c2 3 4 4 8 5h0c4 2 9 0 12-2l2-1c1-1 2-1 3-1-2 5-4 9-9 12h-1c-2-1-6-1-8-2-8-2-13-8-16-15 0-2 0-4-1-5l-1-1c1-5 4-8 8-12z" class="F"></path><defs><linearGradient id="j" x1="777.848" y1="325.355" x2="768.347" y2="368.025" xlink:href="#B"><stop offset="0" stop-color="#a4a4a6"></stop><stop offset="1" stop-color="#c6c6c9"></stop></linearGradient></defs><path fill="url(#j)" d="M762 368c-1-1-1 0-1-1 6-12 11-25 15-37 1-2 1-4 2-6 3 2 7 6 10 7-2 1-2 2-3 4-5 7-9 15-14 22h0l-3 4h0c-1 1-1 1-1 2-1 2-3 4-5 5z"></path><path d="M737 214l3 1 11 4 2 6 1 4h0v2c3 15 3 30-1 45-1 3-2 8-3 11h0c-3 2-4 6-6 9-1-2 2-7 2-9 6-16 10-35 6-52 0-2-1-3-2-5 0-2-1-3-2-4l-1 1c-2-2-4-5-6-8-1-1-3-3-5-3l1-2z" class="K"></path><path d="M740 215l11 4 2 6 1 4c-2-1-2-4-3-5-1 0-2-1-3-1-4-2-6-5-8-8z" class="E"></path><defs><linearGradient id="k" x1="582.592" y1="167.37" x2="583.661" y2="176.351" xlink:href="#B"><stop offset="0" stop-color="#b3b1b2"></stop><stop offset="1" stop-color="#dadada"></stop></linearGradient></defs><path fill="url(#k)" d="M582 166c1 1 1 1 3 1h1c2 1 9 1 11 0h6 1 1 0 1c2 0 6-1 7 0 1 0 2 0 3 1 2 0 4-1 5 0 1 0 3 0 4 1h0v1h-1-2c1 1 1 0 2 1h1 3v1c-1 0-2 0-2 1h-1v-1h-1c-1 0-4 1-5 0-2 0-5 0-6-1-2 0-9 0-10 1-2 0-5-1-6 0h-3c2 1 2 0 4 0h5c4 0 7-1 10 0h5v1c-10-1-20-1-30 2-5 1-10 2-15 4l1-4 8-9z"></path><path d="M736 216c2 0 4 2 5 3 2 3 4 6 6 8l1-1c1 1 2 2 2 4 1 2 2 3 2 5 4 17 0 36-6 52-2 2-3 7-4 9-2-1-2-1-3-1 2-6 5-12 7-19 3-10 3-20 2-31 0-7-1-14-5-21-2-2-3-4-6-5-1-1-1-2-1-3z" class="F"></path><path d="M404 532c8 0 14 0 20 5 5 5 8 10 8 17 1 7-2 13-6 17-4 5-10 7-16 8-4 0-8-1-11-2l-4-3c1 0 2 0 3 1 6 2 12 1 18-1 3 0 5-2 8-4l1-2v-2c1-3 3-7 3-9-1-3 0-8-2-11h0c-3-5-8-8-13-10h-11c-1 0-2 0-3 1h-4c2-1 3-2 5-3l1-1 3-1z" class="E"></path><path d="M400 534c1 0 1 1 2 1 5-1 9-1 13 0l2 1h1-5-11c-1 0-2 0-3 1h-4c2-1 3-2 5-3z" class="T"></path><path d="M413 536h5c4 1 8 5 10 9s2 13 1 17v1l-1 1c-1 2-2 4-4 6l1-2v-2c1-3 3-7 3-9-1-3 0-8-2-11h0c-3-5-8-8-13-10z" class="R"></path><path d="M753 225c1 0 3 1 4 3 5 7 6 16 7 24 2 12-1 27-4 38-2 5-4 10-5 15-1 2-2 3-3 5l-3 5v1l-1 1c0 1-1 1-1 2l-1 1-7 5-13 10c1-4 7-8 10-12 4-6 9-11 13-17 11-19 17-47 12-68-1-4-3-8-7-10v1h0l-1-4z" class="D"></path><path d="M764 252c2 12-1 27-4 38-2 5-4 10-5 15-1 2-2 3-3 5l-3 5v1l-1 1c0 1-1 1-1 2l-1 1-7 5 1-1c1-4 4-7 6-10 2-2 4-6 6-9s3-7 5-11c5-14 7-27 7-42z" class="G"></path><path d="M751 219c1 0 4 1 4 2 2 1 4 1 6 2 0 2 0 3 1 4l4 13c2 6 2 15 2 21 0 2 0 6-1 8l-3 15c-1 6-3 13-6 19-2 6-4 10-8 14-2 1-3 3-4 3l1-1c0-1 1-1 1-2l1-1v-1l3-5c1-2 2-3 3-5 1-5 3-10 5-15 3-11 6-26 4-38-1-8-2-17-7-24-1-2-3-3-4-3l-2-6z" class="N"></path><path d="M755 221c2 1 4 1 6 2 0 2 0 3 1 4l4 13h-1 0c-1-2-1-5-2-7-2-3-7-9-8-12z" class="E"></path><defs><linearGradient id="l" x1="739.487" y1="348.092" x2="749.091" y2="363.429" xlink:href="#B"><stop offset="0" stop-color="#88878a"></stop><stop offset="1" stop-color="#b1b1b3"></stop></linearGradient></defs><path fill="url(#l)" d="M763 331c1-1 2-2 3-4h0l-3 8-8 13c-1 2-3 4-3 6v1h0c-1 1-1 2-2 2l-2 2c-1 1-1 2-2 3s0-1-1 1c0 1-1 1-2 2 0 1-1 1-1 2-1 1-4 4-6 5h0c-1 1-1 1-2 1h0c-2 0-4 0-5 1 0-1-1-1-1-2v1c-3 4-7 6-9 10-1 1-2 1-3 2-1 0-2 2-3 2-2 3-5 5-6 8l-2 1 1 1 1 1c2 0 4-1 6 0h1 1c1 1 1 1 2 1s2 1 4 2h1v1c-7-3-12-4-19-3 0-3 5-7 7-9l14-15 15-16 15-19 3-3h2 0c2-2 3-3 4-6z"></path><path d="M794 281h1c0 12-1 25-4 38-1 4-1 8-3 12-3-1-7-5-10-7 2-2 3-7 3-10l5-11 6-15c1-3 1-5 2-7z" class="B"></path><path d="M815 275h2c1 0 1 1 2 1 1 2 1 4 2 7v2h-1-1v4l-2 1 1 4-1 14c0 2 0 4-1 6 0 4-1 9-2 13s-3 8-5 12c0 1-1 2-1 3v1c-1 1-1 1-1 2l-1 1c-1 2-3 4-3 6-1 3-3 5-4 7-2 2-2 4-3 6h-1-1v1c-1 2 0 0-1 1l-1 3c-1 2-2 3-3 5-1-3 2-6 3-8 0-1 0 0 1-1 0-1 0-1 1-2v-1c3-2 5-9 6-13 4-10 8-20 10-31 0 1 0 2 1 3l3-32v-1c1-3 0-5 0-8v-3-2l1-1z" class="C"></path><defs><linearGradient id="m" x1="818.052" y1="275.998" x2="818.43" y2="286.968" xlink:href="#B"><stop offset="0" stop-color="#6a6c6e"></stop><stop offset="1" stop-color="#817f83"></stop></linearGradient></defs><path fill="url(#m)" d="M815 275h2c1 0 1 1 2 1 1 2 1 4 2 7v2h-1-1v4l-2 1c0-4-1-9-3-14l1-1z"></path><path d="M633 506c3-2 6-3 10-3 3 0 5 2 7 4 2 3 3 6 3 9 0 2-1 4-2 5l-2 2-2 1c-3 1-6 2-9 1-4-2-6-4-8-8 0-4 1-8 3-11z" class="K"></path><path d="M633 506v1c1 5-1 9 3 13 3 2 6 4 9 4h1 1c-3 1-6 2-9 1-4-2-6-4-8-8 0-4 1-8 3-11z" class="E"></path><path d="M642 506c2-1 3-1 5 0 1 1 2 2 2 3 1 2 1 3 0 5s-2 3-4 3h-1c-2 0-4-1-5-3-1-1-2-3-1-5s2-2 4-3z" class="C"></path><path d="M606 163c9-1 18 0 26 0h49c3 0 20-1 22 0v1c1 0 7 5 7 6h-1l-1-1-2-2-1-1c-1 0-1 0-2-1h-1c-2-1-15-1-18-1-1 1-3 1-4 1-2 1-35 0-40 0-2 1-6 1-8 1h3c1 1 4 1 6 1h0-5v1h-2c-2 0-1-1-3-1h-1c-1 1-2 0-3 0-1 1-4 1-5 1h0 5 3 0 0c-2 1-3 1-5 1-1-1-3-1-4-1-1-1-3 0-5 0-1-1-2-1-3-1-1-1-5 0-7 0h-1 0-1-1-6c-2 1-9 1-11 0h-1c-2 0-2 0-3-1l1-1 2-2h15 6z" class="G"></path><path d="M585 163h15 6 1 10l-9 1h1c-8 0-18 2-26 1l2-2z" class="I"></path><defs><linearGradient id="n" x1="757.314" y1="312.322" x2="767.239" y2="349.164" xlink:href="#B"><stop offset="0" stop-color="#7b7b7f"></stop><stop offset="1" stop-color="#a9a8aa"></stop></linearGradient></defs><path fill="url(#n)" d="M774 291c1-1 1-2 1-3 1-2 1-2 1-3 1-1 0-2 0-3 1-2 0-3 1-5 1-5 0-10 0-15h1c1 28-3 57-14 83-2 1-3 3-4 4h-1l-3 3c-2 1-3 2-4 3v-1c0-2 2-4 3-6l8-13c1-2 2-5 3-8h0c-1 2-2 3-3 4-1 3-2 4-4 6h0-2l-3 3 3-6c1-1 1-2 2-3l4-7 2-5 1-1v-2c1-1 1-1 1-2 1-3 2-5 3-8v-2l4-13z"></path><path d="M757 337l7-9v1c-1 0-1 1-1 1v1c-1 3-2 4-4 6h0-2z" class="L"></path><path d="M737 219c3 1 4 3 6 5 4 7 5 14 5 21 1 11 1 21-2 31-2 7-5 13-7 19-2 3-4 6-6 8-2 5-4 11-7 14 0-3 1-4 3-7h0v-3h0v-1-1-1l1-1c0-1 1-2 1-3h1c0-1 0-2 1-3h0l1-2v-1c0-1 0-1 1-2l3-6 2-6c1-3 3-5 3-8 3-14 5-30 0-44h-1c0-1-1-1-2-1 0 2 1 4 0 6v1 1h0c1 1 0 2 0 4 2 2 0 12 0 15v6h-1c1 6-2 12-4 17v2l-1 1c0 1 0 2-1 3v-2l-1 1c0 1 0 2-1 3 0 1-1 2-1 2l4-14c2-6 4-11 4-18h0c1-11 2-25-1-36z" class="D"></path><defs><linearGradient id="o" x1="523.81" y1="769.388" x2="538.298" y2="750.493" xlink:href="#B"><stop offset="0" stop-color="#a4a3a6"></stop><stop offset="1" stop-color="#c3c4c7"></stop></linearGradient></defs><path fill="url(#o)" d="M524 739v1c0 2-1 3-1 5-1 3-1 8 1 11h0c1 1 2 2 2 3l3 3c1 0 1 0 2 1 2 1 9 1 11 1l1-1c1 0 2 0 3-1h1c1-1 1-1 2-1 0-2 1-2 2-3v-1-1-1c0-1 1-1 1-2 0 1 1 1 1 2v1-1-2-1c2 4 1 9-1 13h0c-3 4-6 7-11 8h-2-6c-4 0-8-2-11-4l-4-2-2-7c-1-6 0-12 4-17l4-4z"></path><path d="M553 752c2 4 1 9-1 13h0-1l1-2v-2l-2 3h-1v-2c2-2 3-3 4-5v-1-1-2-1zm-29-13v1c0 2-1 3-1 5-1 3-1 8 1 11h0v3h0l-1 1c-3-5-2-8-2-14 0-2 0-2-1-3l4-4z" class="D"></path><defs><linearGradient id="p" x1="742.425" y1="245.821" x2="710.967" y2="298.724" xlink:href="#B"><stop offset="0" stop-color="#8c8e92"></stop><stop offset="1" stop-color="#c3c2c5"></stop></linearGradient></defs><path fill="url(#p)" d="M726 256v1c2-3 3-6 4-8 1-1 1-1 1-2l2-4c1 0 3-1 4-1-1-2-1-3 0-4v16l1 1c0 7-2 12-4 18l-4 14-3 6c-2 4-4 8-7 12-1-1-1-2-1-4s2-4 2-6v-1c1-1 1-2 1-4 3-5 3-11 3-17v-1c-2-4-3-8-1-12l2-4z"></path><defs><linearGradient id="q" x1="707.289" y1="212.733" x2="697.383" y2="227.366" xlink:href="#B"><stop offset="0" stop-color="#817f82"></stop><stop offset="1" stop-color="#b7b7ba"></stop></linearGradient></defs><path fill="url(#q)" d="M655 204c2-2 3-1 5-1l13 4c4 1 7 2 10 4l1-1c-1-1-2-2-3-2l1-2h0v-1h0c1 1 2 0 3 0l1 1h3c1 3 4 3 7 4l10 4c1 1 1 1 2 1h2c1 0 2 1 3 2 2 1 6 3 8 5 0 1 1 2 1 4s0 6-1 9c0 1 0 2-1 4 0 1-1 2-1 4l-1-2c-1-1-2-3-3-4v-1c-2-3-5-4-6-7h0c-1 0-1 0-2-1s-1-2-2-2l-2-2-2-1c-1-1-2-1-3-2-1 0 0 0-1-1l-2-1c-1-1-2-1-2-1l-1-1-3-2h-3v1c-2 0-2 0-4-1 0-1-1-1-1-2h-2c-2-1-4-2-5-3h-3c-1-1-1-2-3-2v1l-1-1h-1-1-1l-2-1h0c-1 0-2-1-3-1 0-1 0 0-1-1l3 3c1 1 0-1 1 1l2 1 5 4h1c0 1 1 1 2 2l3 3c3 1 6 5 8 7 1 1 1 2 2 3v1c-2-1-4-4-6-6-5-5-9-9-15-13 0-1-3-2-3-2-2-2-4-4-6-5z"></path><defs><linearGradient id="r" x1="743.378" y1="298.227" x2="733.026" y2="350.801" xlink:href="#B"><stop offset="0" stop-color="#62686c"></stop><stop offset="1" stop-color="#adacb0"></stop></linearGradient></defs><path fill="url(#r)" d="M746 320c1 0 2-2 4-3 4-4 6-8 8-14 3-6 5-13 6-19l3-15c-1 13-3 26-7 38-3 6-6 12-10 19-10 18-22 35-35 52 0-2 0-2 1-3l1-1v-1c1-2 2-2 3-3s1-2 2-3 2-2 2-3c1-1 1-2 2-2 0-1 0-1 1-2l1-1c1-2 2-3 3-4s0-1 1-2c1 0 1-1 1-2h-2-1l-1-1h-1l-1-1c-1-1-2-1-3-1h0l-1-1h-2l-1-1h-1c-2-1-2-1-2-2-2 1-2 2-3 3s0-1-2 0c-1 1-1 2-2 3h-1c-1 1 0 1-2 1 0 1-1 1-1 2-1 0-1 0-2 1l-1 1h-1l-1 2h-1l-2 2h-1l19-16c4-2 6-5 10-8l13-10 7-5z"></path><path d="M531 737c2-1 7-1 9 0h1c4 1 8 5 10 9 2 2 2 3 2 6v1 2 1-1c0-1-1-1-1-2 0 1-1 1-1 2v1 1 1c-1 1-2 1-2 3-1 0-1 0-2 1h-1c-1 1-2 1-3 1l-1 1c-2 0-9 0-11-1-1-1-1-1-2-1l-3-3c0-1-1-2-2-3h0c-2-3-2-8-1-11 0-2 1-3 1-5v-1c2-1 5-2 7-2z" class="Q"></path><path d="M524 739c2-1 5-2 7-2 0 1-1 2-2 3s-1 1-2 1v1c0 1 0 1-1 2-1 3 0 5-1 7 0 1-1 2 0 3v1c-1 1 0 1-1 1-2-3-2-8-1-11 0-2 1-3 1-5v-1z" class="C"></path><defs><linearGradient id="s" x1="406.624" y1="507.385" x2="435.387" y2="536.116" xlink:href="#B"><stop offset="0" stop-color="#98979a"></stop><stop offset="1" stop-color="#c1c1c2"></stop></linearGradient></defs><path fill="url(#s)" d="M423 501c2-3 4-4 6-6 2 2 4 4 6 5h1c1 1 2 3 2 5l1-1v1c0 1 0 3 1 4v1 4c1 0 0 1 0 2v4h1v-2h1c1 2 0 3 0 5-1 4-2 7-3 11h-1c0-1 0-1-1-2 1-2 1-4 1-6v-1c1-4-1-11-4-15-1-1-2-2-4-2-5-1-16 9-20 13-3 4-5 8-9 11l-1 1h1l-1 1c-2 1-3 2-5 3-3 4-6 7-8 11l-1 1c0-2 1-4 2-5l-1-1 1-4 2-4c2-1 3-3 4-5 2-3 4-5 5-8 4-2 6-8 8-11h1c1-1 3-7 4-9l5-6h1v1c1 1 1 2 2 3v6c1-1 1-1 1-2 1-1 2-2 2-3z"></path><path d="M399 522c4-2 6-8 8-11h1c-4 8-8 16-13 23-2 4-5 6-7 10l-1-1 1-4 2-4c2-1 3-3 4-5 2-3 4-5 5-8z" class="C"></path><defs><linearGradient id="t" x1="506.342" y1="786.965" x2="505.304" y2="762.05" xlink:href="#B"><stop offset="0" stop-color="#cbcacb"></stop><stop offset="1" stop-color="#f2f3f3"></stop></linearGradient></defs><path fill="url(#t)" d="M491 763c1 0 3-1 4-2h1c2-1 5-1 8 0l4 1c2 0 2 1 3 2s1 0 2 1h0c0 1 1 1 1 2l2 2c0 1 1 2 1 3v1c1 1 1 2 1 3 1 1 1 2 1 3v1 6c-1 0-2 0-3 1l-2 1c-3 2-8 4-12 2h0c-4-1-6-2-8-5v-1c-3-2-4-6-5-9 0-4 1-8 2-12z"></path><path d="M491 763c1 0 3-1 4-2h1c2-1 5-1 8 0l4 1h-2-3c-2 1-4 1-6 1-1 2-1 4-1 6h0c-1 1-1 1-1 2 0 2 0 2-1 3-1 0-1 0-2 1h-3c0-4 1-8 2-12z" class="Q"></path><path d="M503 762h3v1c3 2 4 2 5 5s-1 5-2 7c-2 1-3 2-5 1-2 0-4-2-4-4-1-2-1-4 0-6l3-3h0v-1z" class="J"></path><path d="M761 223c6 3 15 6 19 11 3 3 5 7 6 10 5 11 9 25 9 37h-1c-1 2-1 4-2 7l-6 15-5 11v-3c1-3 2-8 2-12s-1-10 0-15c1-6 2-13 1-20 0-3 1-7 0-10-2-5-4-9-7-14-1-1-2-3-3-4 2 8 5 18 4 26h-1c0 5 1 10 0 15-1 2 0 3-1 5 0 1 1 2 0 3 0 1 0 1-1 3 0 1 0 2-1 3 0-5 1-9 2-14v-15-8c-1-2-1-5-2-6-1-2-1-4-2-6 0-2-2-5-3-7v-1c-2-2-4-5-7-7-1-1-1-2-1-4z" class="K"></path><path d="M792 288l-1-2c1-3 0-10 2-11 0 2 0 4 1 6-1 2-1 4-2 7zm-8-40c2 3 2 5 3 8 3 11 2 23 0 34 0 5-2 9-1 13l-5 11v-3c1-3 2-8 2-12 1-2 2-5 2-8 1-9 2-18 1-27 0-5-2-10-2-16z" class="F"></path><path d="M774 236c-1-2-2-5-3-7 2 2 3 5 5 7 4 3 6 8 8 12 0 6 2 11 2 16 1 9 0 18-1 27 0 3-1 6-2 8 0-4-1-10 0-15 1-6 2-13 1-20 0-3 1-7 0-10-2-5-4-9-7-14-1-1-2-3-3-4z" class="D"></path><defs><linearGradient id="u" x1="809.609" y1="259.328" x2="796.107" y2="341.781" xlink:href="#B"><stop offset="0" stop-color="#6a6a6c"></stop><stop offset="1" stop-color="#939396"></stop></linearGradient></defs><path fill="url(#u)" d="M801 268c-1-9-4-19-8-27 4 3 7 6 10 9l-2 1-1-1h-1v1h1c1 2 3 4 4 6 6 4 8 12 11 18l-1 1v2 3c0 3 1 5 0 8v1l-3 32c-1-1-1-2-1-3-2 11-6 21-10 31v-1-2-1-1-4c-3-9 2-17 4-26 0-4 1-9 1-13 0-9 1-20-2-28l-2-6z"></path><path d="M799 251h1c1 2 3 4 4 6 6 4 8 12 11 18l-1 1v2 3c0 3 1 5 0 8v1l-3 32c-1-1-1-2-1-3 4-23-1-47-11-68z" class="M"></path><path d="M804 257c6 4 8 12 11 18l-1 1v2 3c0 3 1 5 0 8v1c-1-5-1-11-3-16-2-6-5-11-7-17z" class="F"></path><path d="M612 690c4 0 7 0 11 2 5 2 7 6 8 11l-8 17c-2 2-3 2-5 3-4 0-7 0-10-2-4-2-7-5-8-10-2-3-1-9 1-12 2-5 6-7 11-9z" class="B"></path><path d="M616 700h3c1 0 3 2 3 3 1 2 1 3 0 5-1 1-2 2-4 3-1 0-3-1-4-1-1-1-2-2-3-4 0-1 0-3 1-4s3-2 4-2z" class="K"></path><path d="M687 478c4 0 7 0 10 2 4 2 7 6 8 10 1 5 1 10-2 14-3 5-7 7-13 8-4 0-7-1-11-3-3-2-7-6-7-11-1-5-1-10 2-13 4-5 8-6 13-7z" class="B"></path><path d="M687 490h0c2 0 4 0 6 1 1 2 1 3 1 4-1 2-2 2-3 3-1 0-1 1-2 1s-3-1-4-2-1-3-1-4c0-2 1-2 3-3z" class="E"></path><path d="M756 369c-2 4-4 8-4 12l2-6c1-2 3-5 5-6h4c6 3 10 5 13 12 1 4 1 9 0 13-2 4-6 8-11 10-4 1-10 1-14-1s-8-6-10-11c-1-4 0-10 2-14 3-5 7-8 13-9zm-96 183c4 0 8 1 11 4 4 2 7 7 8 12 0 5-1 10-4 14-4 5-8 6-13 7-5 0-8-1-12-3-4-3-7-8-8-13s1-9 4-13c3-5 8-7 14-8z" class="B"></path><path d="M659 565h4l3 3c0 2 0 3-1 5 0 1-2 2-3 2h-2c-2 0-4-1-4-3-1-1-1-2-1-4 2-2 2-2 4-3z" class="E"></path><path d="M536 832c4 0 7 1 10 2 5 3 9 8 10 13 2 6 1 10-2 15-4 6-9 8-15 10-5 0-10-1-14-4s-7-8-8-14c0-5 1-10 5-15 3-4 8-6 14-7z" class="B"></path><path d="M537 844c1-1 2 0 3 0 3 0 4 1 6 3 1 2 1 4 1 6-1 2-2 4-5 5-1 1-2 1-3 1-3-1-4-3-6-5-1-1-1-4-1-6 1-2 3-4 5-4z" class="L"></path><path d="M709 163h9 1 8c-1 1-1 1-1 2v1c1 1 2 3 4 3l1 1c4 4 9 6 13 9 11 7 21 14 31 23 3 3 6 6 8 10 2 2 4 4 5 6 2 3 3 5 4 7 1 1 1 3 2 4l-1 1h0l-83-60c0-1-6-6-7-6v-1h6z" class="R"></path><path d="M709 163h9 1 8c-1 1-1 1-1 2v1c1 1 2 3 4 3l1 1c4 4 9 6 13 9 11 7 21 14 31 23 3 3 6 6 8 10 2 2 4 4 5 6 2 3 3 5 4 7 1 1 1 3 2 4l-1 1h0v-1l-13-17c-13-16-30-28-48-38l-22-11h-1z" class="G"></path><path d="M472 679c5-3 11-4 17-1l4 1c3 3 7 7 8 12 2 5 1 12-2 17l-1 1c0 1-1 2-2 3-6 4-12 6-18 5s-11-4-14-8l-1-1v-1h0c-1-1-2-2-2-3v-2c-1-3-1-7 0-10v-1c1-3 3-6 5-8l6-4z" class="B"></path><defs><linearGradient id="v" x1="477.486" y1="711.481" x2="497.998" y2="695.962" xlink:href="#B"><stop offset="0" stop-color="#adacae"></stop><stop offset="1" stop-color="#d8d7d8"></stop></linearGradient></defs><path fill="url(#v)" d="M478 682c3 0 7 0 9 1 4 1 8 4 9 8 2 4 2 9 0 13s-6 7-10 9h-8c-4-1-7-3-9-6-2-4-3-9-2-14h0l1-2c1-5 6-7 10-9z"></path><path d="M482 683h2 1c2 1 3 1 5 2s3 3 4 5h-1c0 3 1 5 0 8s-3 5-6 6-5 1-7 0-4-3-5-5c-1-3-1-6 1-9 1-2 3-4 6-5h0v-2z" class="J"></path><path d="M395 537h4c1-1 2-1 3-1h11c5 2 10 5 13 10h0c2 3 1 8 2 11 0 2-2 6-3 9v2l-1 2c-3 2-5 4-8 4-6 2-12 3-18 1-1-1-2-1-3-1-6-6-8-11-9-18 0-3 1-5 1-8 2-4 5-7 8-11z" class="B"></path><defs><linearGradient id="w" x1="394.967" y1="563.969" x2="402.596" y2="566.735" xlink:href="#B"><stop offset="0" stop-color="#606062"></stop><stop offset="1" stop-color="#858486"></stop></linearGradient></defs><path fill="url(#w)" d="M403 571c-3-2-6-4-8-7-2-4-3-9-2-13s4-8 8-10 8-2 12-1c-2 0-4 0-6 1-1 0-3 1-4 1-1 1-3 4-3 5-1 3-1 6 0 9s4 5 6 6h4c-2 1-4 1-6 1h-1c-1 1-1 1-1 2 1 1 2 1 2 3v1h-2c1 1 1 1 1 2z"></path><defs><linearGradient id="x" x1="408.661" y1="559.91" x2="418.673" y2="567.197" xlink:href="#B"><stop offset="0" stop-color="#8d8b8d"></stop><stop offset="1" stop-color="#bdbdbf"></stop></linearGradient></defs><path fill="url(#x)" d="M423 548c2 4 2 8 1 13-1 4-4 8-8 9-2 1-5 2-7 2h-1c-2 0-3-1-5-1 0-1 0-1-1-2h2v-1c0-2-1-2-2-3 0-1 0-1 1-2h1c2 0 4 0 6-1 2 0 4 0 6-2 5-3 6-7 7-12z"></path><path d="M408 572c-1-1-2-1-3-2h2s1-1 2-1l1 1c-1 0-1 1-1 2h0-1z" class="N"></path><path d="M410 562h-4c-2-1-5-3-6-6s-1-6 0-9c0-1 2-4 3-5 1 0 3-1 4-1 2-1 4-1 6-1 4 2 7 4 10 8-1 5-2 9-7 12-2 2-4 2-6 2z" class="J"></path><path d="M601 606c2-2 4-3 7-4 6-1 13 0 18 3 6 4 8 8 9 15 1 6-1 12-5 17-4 4-9 7-15 7s-10-1-15-5c-4-4-7-11-7-16 0-8 3-12 8-17z" class="B"></path><defs><linearGradient id="y" x1="621.442" y1="637.885" x2="613.383" y2="609.392" xlink:href="#B"><stop offset="0" stop-color="#bebdbf"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#y)" d="M610 607c6 0 10 0 14 4 4 3 6 7 6 12s-1 10-5 13c-2 2-5 4-9 4h0c-3 1-6 0-9-1-4-2-7-6-9-11-1-4 0-10 2-14 2-3 6-6 10-7z"></path><defs><linearGradient id="z" x1="603.6" y1="629.804" x2="608.962" y2="638.025" xlink:href="#B"><stop offset="0" stop-color="#878689"></stop><stop offset="1" stop-color="#aba9ab"></stop></linearGradient></defs><path fill="url(#z)" d="M616 640h0c-3 1-6 0-9-1-4-2-7-6-9-11-1-4 0-10 2-14 2-3 6-6 10-7-1 1-1 2-2 3-4 2-4 4-6 8-1 3 0 7 2 10 1 3 5 6 8 7h5l-1 1h-4c-1 1-1 2-2 2v1c1 0 2 1 4 0 0 0 1 0 2 1h0z"></path><path d="M436 500c-1-3-3-5-5-7 0-2 1-3 2-4 1-3 2-4 5-4 5 3 8 7 12 11l2 3h1l-1-3 1-1v-2c0 1 1 3 2 3 1 3 3 6 4 9-1 3 2 10 3 14 2 8 2 15 1 24v5 1 3l-1-1c-2 3-4 7-5 11-1 5-5 10-7 15-1 1-2 2-3 2l-1 1-6 8c-2 3-4 5-5 8l-3 5v1c-2 4-5 7-7 11 0-2 0-4 1-5 3-5 7-10 10-15l6-13c2-3 2-6 4-8 0-1 1-1 1-2v-2h1v-1-2c1-2 1-3 1-4v-1-9c0-1 1-3 0-4v-5c-1-1-1 0-1-1v-2-2l-1-5c-1-1-1-2-1-3s-1-1-1-2-1-2-1-4l-1-1c-1-2-1-1-1-2l1-1c-1-1-1-1-1-2v-2 2 1h-1v2h-1v-4c0-1 1-2 0-2v-4-1c-1-1-1-3-1-4v-1l-1 1c0-2-1-4-2-5z" class="N"></path><path d="M439 505c0-3-1-6-2-8v-1l1 1c1 0 1 1 2 1 1 1 1 0 1 1l7 8 3 5 1 4c1 1 2 3 2 4v1s1 1 1 2v1c0 1 1 2 1 2v2c0 2 0 3 1 4 0 3 0 5-1 8v4c0 3-1 6-1 9h-1 0c-1 0-1 0-1 1v1h-1v-1c0-1 1-2 1-3l-1-1v-2-1-4-4l-3-8v-4h0c-1-1-1-2-1-3l-3-5-1-2c0-1 0-1-1-2h0l-1-2-1-5v10 2h-1v-4c0-1 1-2 0-2v-4-1c-1-1-1-3-1-4z" class="G"></path><path d="M453 493c0 1 1 3 2 3 1 3 3 6 4 9-1 3 2 10 3 14 2 8 2 15 1 24v5 1 3l-1-1c-2 3-4 7-5 11-1 5-5 10-7 15-1 1-2 2-3 2l-1 1-6 8c-2 3-4 5-5 8l-3 5v1c-2 4-5 7-7 11 0-2 0-4 1-5 3-5 7-10 10-15l6-13c2-3 2-6 4-8 0-1 1-1 1-2v-2h1v-1-2c1-2 1-3 1-4v-1-9c0-1 1-3 0-4v-5c-1-1-1 0-1-1v-2-2l-1-5c-1-1-1-2-1-3s-1-1-1-2-1-2-1-4l-1-1c-1-2-1-1-1-2l1-1c-1-1-1-1-1-2v-2 2 1h-1v-10l1 5 1 2h0c1 1 1 1 1 2l1 2 3 5c0 1 0 2 1 3h0v4c1 3 2 5 3 8v4 4 1 2l1 1c0 1-1 2-1 3v1h1v-1c0-1 0-1 1-1h0c0 2-2 6-1 8v1 1c2-4 3-9 4-12 6-17 3-36-5-52h1l-1-3 1-1v-2z" class="D"></path><path d="M453 493c0 1 1 3 2 3 1 3 3 6 4 9-1 3 2 10 3 14 2 8 2 15 1 24v5 1 3l-1-1c-2 3-4 7-5 11-1 5-5 10-7 15-1 1-2 2-3 2l-1 1c1-3 3-6 5-9 1-2 1-3 2-5 3-5 6-11 7-16 5-18 1-35-7-51l-1-3 1-1v-2z" class="M"></path><path d="M436 602l1-1c5-3 11-3 17-2h4l4 2 2 2c4 5 7 10 6 17 0 6-3 13-8 18h-1c-2 2-3 3-5 4s-5 1-7 1c-6 1-11-1-16-5-2-2-4-4-5-6l-1-1c-3-6-3-13-2-18 2-4 5-7 7-11l2-1 2 1z" class="B"></path><path d="M457 638h4c-2 2-3 3-5 4s-5 1-7 1c-6 1-11-1-16-5h1c2 0 3 1 5 2 6 2 12 1 18-2z" class="P"></path><path d="M434 601l2 1c0 1 0 1-2 1-3 2-6 9-7 13l-1 1c-1 3 0 8 1 11 0 1 1 2 1 4l-1-1c-3-6-3-13-2-18 2-4 5-7 7-11l2-1z" class="T"></path><path d="M454 599h4l4 2 2 2c4 5 7 10 6 17 0 6-3 13-8 18h-1-4a30.44 30.44 0 0 0 8-8c3-8 4-14 1-21-2-6-7-8-12-10z" class="S"></path><path d="M446 637c-3-1-7-2-10-5-2-2-4-6-4-9v-8h1c1-3 1-4 3-6 3-4 7-6 12-6s9 2 12 6h0c-5-1-12-5-16-2-2 1-4 3-5 5-1 5-2 12 2 16 1 2 3 3 4 5 1 1-1 1 1 3v1z" class="G"></path><path d="M446 637v-1c-2-2 0-2-1-3-1-2-3-3-4-5-4-4-3-11-2-16 1-2 3-4 5-5 4-3 11 1 16 2h0c4 4 4 9 3 14 0 4-3 8-7 11-3 2-6 2-10 3z" class="H"></path><path d="M445 608h5c2 3 4 1 7 2 2 0 3 2 4 3 0 4 1 7-1 11-1 3-2 4-5 5-2 2-4 2-6 2-3 0-5-2-7-4-3-4 0-13 0-17 1-1 2-1 3-2z" class="C"></path><path d="M453 612h4l3 3c0 2 0 4-2 6h0l-1 1c0 1-1 1-1 2h-1c0 1-1 1-2 2 0 0-2 0-3 1h-1-1c-2-1-3-2-4-3 1-4 3-8 6-9l3-3z" class="J"></path><path d="M703 399c7-1 12 0 19 3 5 4 9 9 10 16s-1 14-5 19-10 8-16 9c-5 0-12 0-16-4h-1l-1-1c-5-4-8-10-8-16 0-9 2-15 8-21h5c1-1 3-2 5-3 0-1-1-1-2-1l2-1z" class="B"></path><path d="M719 403c5 2 8 6 10 12v1h0c2 6 0 13-3 18 0 1 0 1-1 2s-2 1-3 2l-1 1-1 1h-1c4-3 7-7 8-12 0-2 1-4 1-6v-1l1 1v-1-2l-1-1c0-3-1-6-3-8s-5-4-6-7h0z" class="T"></path><path d="M704 439l-4-1c-3-2-6-6-7-9l-1-1c0-3-1-6 0-9h0c1-2 1-4 2-6 3-4 6-6 11-7l-3 3c-3 1-3 4-5 6-1 2-2 5-1 8 1 7 6 10 8 16z" class="L"></path><path d="M693 404h5a30.44 30.44 0 0 0-8 8c-2 3-3 8-3 11 0 6 2 11 6 15s12 5 17 5c3 0 6-1 8-3h1 1l1-1 1-1c1-1 2-1 3-2-2 3-3 4-6 6h-1c-6 3-13 4-19 1-1 0-3-1-5-1l-1-1c-5-4-8-10-8-16 0-9 2-15 8-21z" class="O"></path><path d="M703 399c7-1 12 0 19 3 5 4 9 9 10 16s-1 14-5 19-10 8-16 9c-5 0-12 0-16-4h-1c2 0 4 1 5 1 6 3 13 2 19-1h1c3-2 4-3 6-6 1-1 1-1 1-2 3-5 5-12 3-18h0v-1c-2-6-5-10-10-12-5-3-10-3-16-2 0-1-1-1-2-1l2-1z" class="P"></path><path d="M705 406c5-1 9 0 14 3 3 3 6 7 6 11s-1 8-3 12c-2 3-5 6-10 7-2 1-6 1-8 0-2-6-7-9-8-16-1-3 0-6 1-8 2-2 2-5 5-6l3-3z" class="G"></path><path d="M704 439c-2-6-7-9-8-16-1-3 0-6 1-8 0 6 0 10 4 14 2 4 5 5 9 6 5 0 8-1 12-3-2 3-5 6-10 7-2 1-6 1-8 0z" class="I"></path><path d="M707 408h7l3 1c1 1 3 2 4 4h0c0 2 1 3 0 5v1 1c-1 4-3 6-6 8h0c-2 1-3 1-5 1h0-2c-3-1-5-3-7-6-1-4 0-9 1-12 1-2 3-2 5-3z" class="J"></path><path d="M664 211c6 4 10 8 15 13 2 2 4 5 6 6 3 5 6 9 9 14 12 24 16 54 11 81-3 11-6 21-11 31l-16-25c4-11 9-21 11-32 5-26-1-53-15-75l-6-8c-2-1-3-3-4-5z" class="B"></path><defs><linearGradient id="AA" x1="653.678" y1="182.036" x2="646.742" y2="207.319" xlink:href="#B"><stop offset="0" stop-color="#505052"></stop><stop offset="1" stop-color="#818184"></stop></linearGradient></defs><path fill="url(#AA)" d="M573 179c5-2 10-3 15-4 10-3 20-3 30-2 7 1 13 2 20 4 14 5 28 12 42 17l51 18 6 2-1 2c0 1 0 2 1 3 3 11 2 25 1 36h0l-1-1v-16c-1 1-1 2 0 4-1 0-3 1-4 1l-2 4c0 1 0 1-1 2-1 2-2 5-4 8v-1l-2 4v-4h-1c-1-1-1-3-2-5-1 3-1 6-1 9h0c-1-5 0-12 1-17v-2h0c0-1 0-2-1-2 1-2 1-3 1-4 1-3 1-7 1-9s-1-3-1-4c-2-2-6-4-8-5-1-1-2-2-3-2h-2c-1 0-1 0-2-1l-10-4c-3-1-6-1-7-4h-3l-1-1c-1 0-2 1-3 0h0v1h0l-1 2c-2-2-5-3-7-3-7-3-13-5-20-7-3-1-7-2-11-2-1 0-2-1-3-1-2-1-5-2-7-3-6-2-12-2-18-4l-12-1c-10 0-20 1-29 2l-1-10z"></path><path d="M721 241l4-24c1-1 2-1 3-2 2 1 2 5 3 7v7c1 1 0 1 0 3v1c0 2 0 3-1 5l-6 18h-1c-1-1-1-3-2-5-1 3-1 6-1 9h0c-1-5 0-12 1-17v-2h0z" class="M"></path><path d="M721 241l4-24c1-1 2-1 3-2 2 1 2 5 3 7v7c1 1 0 1 0 3v1c0 2 0 3-1 5v-5c1-4 0-8 0-12l-2-1c-1 0-1 0-1 1-1 1-1 3-1 4l-1 10c0 4-1 8-2 12-1 1-1 4-2 4-1 3-1 6-1 9h0c-1-5 0-12 1-17v-2h0z" class="D"></path><defs><linearGradient id="AB" x1="724.407" y1="216.482" x2="741.075" y2="241.99" xlink:href="#B"><stop offset="0" stop-color="#4c4b4c"></stop><stop offset="1" stop-color="#868688"></stop></linearGradient></defs><path fill="url(#AB)" d="M731 212l6 2-1 2c0 1 0 2 1 3 3 11 2 25 1 36h0l-1-1v-16c-1 1-1 2 0 4-1 0-3 1-4 1l-2 4c0 1 0 1-1 2-1 2-2 5-4 8v-1l-2 4v-4l6-18c1-2 1-3 1-5v-1c0-2 1-2 0-3v-7c1 4 1 8 1 12 1-3 1-8 1-11-1-3-3-9-2-11z"></path><path d="M731 222c1 4 1 8 1 12 0 5-2 10-4 15-1 2-2 5-2 7l-2 4v-4l6-18c1-2 1-3 1-5v-1c0-2 1-2 0-3v-7z" class="G"></path><path d="M642 192h-3-1c-1-1-2-1-4-2h-1s-1 0-1-1h1l4 1h1c1 0 1 0 3 1l-1-2c-2 0-4-1-6-1-1 0-2-1-2-1h-3l-1-1c2 0 5 0 7 1 16 2 32 8 47 14 9 3 18 7 27 12l1 1-2 1c-1 0-1 0-2-1l-10-4c-3-1-6-1-7-4h-3l-1-1c-1 0-2 1-3 0h0v1h0l-1 2c-2-2-5-3-7-3-7-3-13-5-20-7-3-1-7-2-11-2-1 0-2-1-3-1-2-1-5-2-7-3-6-2-12-2-18-4h3 1 0c2 0 2 0 4 1h2 1 1l1 1h2c1 0 2 0 3 1h0c2 1 6 3 8 2l1-1z" class="F"></path><path d="M615 188h3 1 0c2 0 2 0 4 1h2 1 1l1 1h2c1 0 2 0 3 1h0c2 1 6 3 8 2l1-1h0c4 1 7 2 11 3l21 7c2 0 7 2 8 3v1h0l-1 2c-2-2-5-3-7-3-7-3-13-5-20-7-3-1-7-2-11-2-1 0-2-1-3-1-2-1-5-2-7-3-6-2-12-2-18-4z" class="I"></path><defs><linearGradient id="AC" x1="768.228" y1="272.84" x2="788.589" y2="171.993" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#28292b"></stop></linearGradient></defs><path fill="url(#AC)" d="M718 163c5 0 10-1 14 0h17c20 8 40 18 55 35 5 4 8 9 12 15 1 2 3 3 4 5l3 9c5 11 7 22 7 35 0 2 0 6-1 8v2c-1 1-1 2 0 3-2 4-4 9-6 13v-6c0-15-8-27-16-39-2-1-3-3-5-5l-9-8 1-1c-1-1-1-3-2-4-1-2-2-4-4-7-1-2-3-4-5-6-2-4-5-7-8-10-10-9-20-16-31-23-4-3-9-5-13-9l-1-1c-2 0-3-2-4-3v-1c0-1 0-1 1-2h-8-1z"></path><defs><linearGradient id="AD" x1="790.458" y1="225.579" x2="796.809" y2="219.157" xlink:href="#B"><stop offset="0" stop-color="#bdbbbe"></stop><stop offset="1" stop-color="#e2e3e2"></stop></linearGradient></defs><path fill="url(#AD)" d="M718 163c5 0 10-1 14 0h-4c1 1 6 3 6 4 19 8 34 21 48 36 11 11 20 25 25 40-2-1-3-3-5-5l-9-8 1-1c-1-1-1-3-2-4-1-2-2-4-4-7-1-2-3-4-5-6-2-4-5-7-8-10-10-9-20-16-31-23-4-3-9-5-13-9l-1-1c-2 0-3-2-4-3v-1c0-1 0-1 1-2h-8-1z"></path><path d="M734 167c19 8 34 21 48 36-4-1-4-3-7-4-1-2 1 0-1-1 0-1-1-1-1-2-2-1-4-3-6-4l-16-13c-6-3-13-6-17-11v-1z" class="J"></path><path d="M409 505l-1-1 1-1c1 0 1-1 2-2 0-1 1-2 1-3h1c0-1 1-1 1-2l3-6c1-1 1-1 1-2v-2c1-2 1-3 1-4v-2l1-1v-5c1-1 1 0 1-1 1-6 1-11 1-17 0-5 1-11 0-17v-1c0-1 0-3-1-5h0c1 1 1 1 3 1 3 4 7 7 11 9 1 1 4 2 5 3h0c2 2 3 3 5 4h1c2 1 3 2 5 3v1c-2-1-3-2-4-3s-2-1-4-1c0-1-1-1-1-1l-2-1h0c10 6 20 13 28 21 0 2 4 4 5 6 0 1 1 1 1 2 5 4 8 10 12 15 3 5 6 10 8 15 6 15 8 31 6 47-1 4-2 9-3 13-1 7-3 13-5 20l-7 17-4 4c0 1-1 2-2 3h0l-1-1 1-1h0v-3h0v-1c-2 9-6 18-10 26v1l-5 5c-1 1-2 2-4 3-1 1-1 1-2 1l-2 1h0c2-1 3-2 5-4h1c5-5 8-12 8-18 1-7-2-12-6-17l-2-2-4-2h-4c-6-1-12-1-17 2l-1 1-2-1-2 1v-1l3-5c1-3 3-5 5-8l6-8 1-1c1 0 2-1 3-2 2-5 6-10 7-15 1-4 3-8 5-11l1 1v-3-1-5c1-9 1-16-1-24-1-4-4-11-3-14-1-3-3-6-4-9-1 0-2-2-2-3v2l-1 1 1 3h-1l-2-3c-4-4-7-8-12-11-3 0-4 1-5 4-1 1-2 2-2 4 2 2 4 4 5 7h-1c-2-1-4-3-6-5-2 2-4 3-6 6 0 1-1 2-2 3 0 1 0 1-1 2v-6c-1-1-1-2-2-3v-1h-1l-5 6c-1 1-2 1-3 3h0z" class="H"></path><defs><linearGradient id="AE" x1="442.807" y1="473.668" x2="445.642" y2="448.781" xlink:href="#B"><stop offset="0" stop-color="#78777b"></stop><stop offset="1" stop-color="#b8b7b6"></stop></linearGradient></defs><path fill="url(#AE)" d="M433 453h0c-2-2-4-6-5-8 5 2 8 5 12 8 13 8 23 18 33 29h-6c-1 0-1-1-2-1-1-1-3-2-3-3l-1-1c-1 0-1-1-2-1-2-1-3-1-4 0 1 1 2 1 2 2 1 1 0 1 1 2h0l-1 1-9-9-9-13c-1-1-3-5-4-5-1-1-1-1-2-1z"></path><defs><linearGradient id="AF" x1="436.072" y1="499.691" x2="423.464" y2="432.041" xlink:href="#B"><stop offset="0" stop-color="#b9b9bd"></stop><stop offset="1" stop-color="#f2f2f1"></stop></linearGradient></defs><path fill="url(#AF)" d="M409 505l-1-1 1-1c1 0 1-1 2-2 0-1 1-2 1-3h1c0-1 1-1 1-2l3-6c1-1 1-1 1-2v-2c1-2 1-3 1-4v-2l1-1v-5c1-1 1 0 1-1 1-6 1-11 1-17 0-5 1-11 0-17v-1c0-1 0-3-1-5h0c1 1 1 1 3 1 3 4 7 7 11 9 1 1 4 2 5 3h0c2 2 3 3 5 4h1c2 1 3 2 5 3v1c-2-1-3-2-4-3s-2-1-4-1c0-1-1-1-1-1l-2-1h0c10 6 20 13 28 21 0 2 4 4 5 6 0 1 1 1 1 2-3-1-5-5-7-7-1-1-2-2-3-2l-2-2c-2-1-1 0-2-1l-11-9c-3-2-6-5-9-7-6-3-11-6-16-10 2 5 3 11 5 16l1 1 4 7c0 2 2 4 3 5 0 1 0 2 1 2l1 2 4 6 1 1 4 6h0l3 5c1 1 1 2 2 3v2l-1 1 1 3h-1l-2-3c-4-4-7-8-12-11-3 0-4 1-5 4-1 1-2 2-2 4 2 2 4 4 5 7h-1c-2-1-4-3-6-5-2 2-4 3-6 6 0 1-1 2-2 3 0 1 0 1-1 2v-6c-1-1-1-2-2-3v-1h-1l-5 6c-1 1-2 1-3 3h0z"></path><path d="M443 478l1 1 4 6h0l3 5c-1 0-1 0-2-1l-1-1c-1 0-1-1-2-2-2-2-4-5-3-8z" class="D"></path><path d="M417 496v-1c0-1 2-3 3-3h0c1 2 1 4 1 6v4 2c0 1 0 1-1 2v-6c-1-1-1-2-2-3v-1h-1zm6 5l1-2c0-2 1-4 2-5 1-2 3-1 4-4v-1c1-2 1-4 3-5 1-2 2-2 4-2v-1c5 2 11 10 13 15-4-4-7-8-12-11-3 0-4 1-5 4-1 1-2 2-2 4 2 2 4 4 5 7h-1c-2-1-4-3-6-5-2 2-4 3-6 6z" class="G"></path><defs><linearGradient id="AG" x1="464.392" y1="589.606" x2="481.757" y2="618.805" xlink:href="#B"><stop offset="0" stop-color="#a1a0a3"></stop><stop offset="1" stop-color="#c7c6c7"></stop></linearGradient></defs><path fill="url(#AG)" d="M457 481l1-1h0c-1-1 0-1-1-2 0-1-1-1-2-2 1-1 2-1 4 0 1 0 1 1 2 1l1 1c0 1 2 2 3 3 1 0 1 1 2 1h6c7 7 13 16 16 25l3 8c1 2 2 6 3 9 2 11 3 23 1 34-2 13-4 25-9 36v1c-1 3-2 6-4 7v-1l-2 2h0c-1 1-1 2-2 3v-1c-2 9-6 18-10 26v1l-5 5c-1 1-2 2-4 3-1 1-1 1-2 1l-2 1h0c2-1 3-2 5-4h1c5-5 8-12 8-18 1-7-2-12-6-17 4-7 7-15 12-22 3-6 5-12 7-18 4-14 5-29 1-43-1-3-2-6-4-9-1-5-4-11-7-14-2-3-3-5-5-7-1 0-5-5-6-6-1 0-4-2-5-3z"></path><path d="M457 481l1-1h0c-1-1 0-1-1-2 0-1-1-1-2-2 1-1 2-1 4 0 1 0 1 1 2 1l1 1c0 1 2 2 3 3 1 0 1 1 2 1h6c7 7 13 16 16 25l3 8-2-1-3-3c0-2-1-3-2-4 0-1 0-2-1-3l-4-5-2-4c-1-1-2-3-4-4-3-1-4-4-8-5-2-1-2-2-4-2-1 0-4-2-5-3z" class="K"></path><path d="M468 490l1-1c1 1 2 3 4 3 5 3 13 23 15 29 4 16 4 35 0 52 0 3 0 5-1 8l-4 17-7-17c3-6 5-12 7-18 4-14 5-29 1-43-1-3-2-6-4-9-1-5-4-11-7-14-2-3-3-5-5-7z" class="B"></path><defs><linearGradient id="AH" x1="474.586" y1="575.647" x2="430.232" y2="582.776" xlink:href="#B"><stop offset="0" stop-color="#9d9c9f"></stop><stop offset="1" stop-color="#f0f1f3"></stop></linearGradient></defs><path fill="url(#AH)" d="M433 453c1 0 1 0 2 1 1 0 3 4 4 5l9 13 9 9c1 1 4 3 5 3 1 1 5 6 6 6 2 2 3 4 5 7 3 3 6 9 7 14 2 3 3 6 4 9 4 14 3 29-1 43-2 6-4 12-7 18-5 7-8 15-12 22l-2-2-4-2h-4c-6-1-12-1-17 2l-1 1-2-1-2 1v-1l3-5c1-3 3-5 5-8l6-8 1-1c1 0 2-1 3-2 2-5 6-10 7-15 1-4 3-8 5-11l1 1v-3-1-5c1-9 1-16-1-24-1-4-4-11-3-14-1-3-3-6-4-9-1 0-2-2-2-3-1-1-1-2-2-3l-3-5h0l-4-6-1-1-4-6c-1-1-1-3-1-4-2-2-4-5-4-8v-3c0-1 0-3-1-4z"></path><path d="M434 601c2-2 4-3 7-4 6-2 12-1 17 2h-4c-6-1-12-1-17 2l-1 1-2-1z" class="E"></path><path d="M446 580l1-1c1 0 2-1 3-2 2-5 6-10 7-15 1-4 3-8 5-11l1 1v1c-3 8-6 16-12 24l-10 13-1-2 6-8z" class="Q"></path><path d="M448 485c1 0 2-1 3 0 5 2 10 8 13 13 4 6 7 12 9 19 5 13 6 31 2 44-1 3-2 5-3 7l-1 3h0l-8-18v-1-3-1-5c1-9 1-16-1-24-1-4-4-11-3-14-1-3-3-6-4-9-1 0-2-2-2-3-1-1-1-2-2-3l-3-5z" class="B"></path><path d="M459 505c7 14 9 29 4 44v-1-5c1-9 1-16-1-24-1-4-4-11-3-14z" class="Q"></path><path d="M448 485c1 0 2-1 3 0 5 2 10 8 13 13 4 6 7 12 9 19 5 13 6 31 2 44-1 3-2 5-3 7l1-13c2-16 0-30-7-44-2-5-4-9-7-13-1-2-2-2-4-2-1 0-2-2-2-3-1-1-1-2-2-3l-3-5z" class="P"></path><defs><linearGradient id="AI" x1="441.007" y1="480.959" x2="461.718" y2="473.501" xlink:href="#B"><stop offset="0" stop-color="#a3a3a9"></stop><stop offset="1" stop-color="#cecccd"></stop></linearGradient></defs><path fill="url(#AI)" d="M433 453c1 0 1 0 2 1 1 0 3 4 4 5l9 13 9 9c1 1 4 3 5 3 1 1 5 6 6 6 2 2 3 4 5 7 3 3 6 9 7 14 2 3 3 6 4 9 4 14 3 29-1 43-2 6-4 12-7 18-5 7-8 15-12 22l-2-2 6-13c2-3 4-7 6-11v-2-3h-1c1-2 2-5 2-6 2-8 4-14 4-22 1-17-5-39-17-52 1 1 1 2 1 3l1 1v2c-3-5-8-11-13-13-1-1-2 0-3 0h0l-4-6-1-1-4-6c-1-1-1-3-1-4-2-2-4-5-4-8v-3c0-1 0-3-1-4z"></path><path d="M444 479c0-1 1-1 2-1l2 1h1c1 0 4 3 5 4l8 9c1 1 1 2 1 3l1 1v2c-3-5-8-11-13-13-1-1-2 0-3 0h0l-4-6z" class="I"></path><path d="M474 577c3-5 5-11 6-16 2-11 3-21 2-32 0-3-1-6-2-9l-1-3-1-2v-2c-2-3-3-7-5-9s-3-5-5-7l-1-1h1l2 2c1-2-1-1-1-4l1 1s1 1 2 1l1 1c3 3 6 9 7 14 2 3 3 6 4 9 4 14 3 29-1 43-2 6-4 12-7 18-5 7-8 15-12 22l-2-2 6-13c2-3 4-7 6-11z" class="Q"></path><path d="M483 555c0-4 1-8 1-12 0-8-1-17-3-25-1-2-2-5-1-7 2 3 3 6 4 9l-1 1c1 9 2 18 1 27 0 2 0 5-1 7z" class="J"></path><path d="M483 555c1-2 1-5 1-7 1-9 0-18-1-27l1-1c4 14 3 29-1 43l-1-2v-4l1-2z" class="C"></path><path d="M360 584l16-19 20 42c2 6 5 13 8 19l129 280-11 23c-1 2-3 6-3 7h0c-4 6-6 14-10 19h-1l-1-1-165-346c6-7 13-15 18-24z" class="B"></path><defs><linearGradient id="AJ" x1="661.628" y1="584.499" x2="460.11" y2="352.49" xlink:href="#B"><stop offset="0" stop-color="#010000"></stop><stop offset="1" stop-color="#2f343b"></stop></linearGradient></defs><path fill="url(#AJ)" d="M678 331l16 25-166 348-20-44-17-41c-1-3-4-8-4-12 4-8 8-17 10-26-1-1 0-2 0-4 6-21 8-41 3-63-2-5-3-9-5-13l-2-2c-3-7-8-15-13-20-2-2-10-10-12-10-8-8-18-15-28-21h0l2 1s1 0 1 1c2 0 3 0 4 1s2 2 4 3v-1c-2-1-3-2-5-3h-1c-2-1-3-2-5-4h0c-1-1-4-2-5-3-4-2-8-5-11-9-2-1-3-4-4-6v-6-11c0-5-1-11-2-16 0-1 1-2 0-3 0-1-1-1 0-2 2 1 2 3 3 4h1c1 0 3 2 3 3 1 1 3 2 4 3 1 2 3 3 4 4 2 3 5 5 8 7 4 4 7 8 12 11l36 33c2 2 3 3 4 5l9 10c1 2 2 5 4 7 6 8 8 18 13 26v2c1 5 4 11 7 16 1 1 1 3 2 4 3 6 10 16 17 17h0l1 1 1-1c1 0 2 0 3 2h0c0 2 2 5 3 7 0 1 0 1 1 2 0 1 1 2 2 3 2 4 4 9 5 13 2-1 5-8 6-11l18-37 59-123 17-34c3-5 6-11 8-16 3-5 6-12 9-17z"></path><path d="M534 554h2c1 1 2 2 2 3 1 1 1 3 0 4s-2 2-3 2-1 1-2 0c-1 0-2-1-3-2-1-2 0-3 0-4 1-2 2-2 4-3z" class="H"></path><path d="M468 469c-8-8-18-15-28-21h0l2 1s1 0 1 1c2 0 3 0 4 1s2 2 4 3v-1c-2-1-3-2-5-3h-1c-2-1-3-2-5-4h0c5 2 11 6 16 9 19 12 35 29 43 50 8 19 7 41 3 61-1 5-3 10-5 15-1-1 0-2 0-4 6-21 8-41 3-63-2-5-3-9-5-13l-2-2c-3-7-8-15-13-20-2-2-10-10-12-10z" class="C"></path></svg>