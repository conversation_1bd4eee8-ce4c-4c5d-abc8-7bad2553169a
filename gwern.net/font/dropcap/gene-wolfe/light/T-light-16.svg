<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="147 69 724 852"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#cdcbca}.C{fill:#474644}.D{fill:#a9a7a5}.E{fill:#b9b7b6}.F{fill:#3c3b39}.G{fill:#c0bebc}.H{fill:#131211}.I{fill:#8e8c89}.J{fill:#63625e}.K{fill:#262523}.L{fill:#b1afad}.M{fill:#343230}.N{fill:#d8d6d6}.O{fill:#2f2c2a}.P{fill:#fff}.Q{fill:#9a9895}.R{fill:#686664}.S{fill:#504e4c}.T{fill:#c5c4c2}.U{fill:#a19f9d}.V{fill:#797775}.W{fill:#807e7c}.X{fill:#eae9e9}.Y{fill:#e3e2e1}.Z{fill:#1c1b1a}.a{fill:#706e6b}.b{fill:#575653}.c{fill:#868482}.d{fill:#d3d1d0}.e{fill:#181715}.f{fill:#f3f3f2}.g{fill:#959391}.h{fill:#201f1c}.i{fill:#0a0908}</style><path d="M644 150c2-1 4-2 7-2h0c0 2-2 3-3 4-1-1-2-1-3-1l-1-1z" class="M"></path><path d="M206 264c1 0 1 1 2 1l-1 3h1l1-1h0c0 1 0 3-2 4h-1l-2-1-4 1c1-1 3-1 4-3 1-1 1-3 2-4z" class="H"></path><path d="M242 490c0 7 1 13 3 20h-3l-1-6c1-4-1-9 1-14z" class="O"></path><path d="M672 507l26 3-19 1c-1-2-4-2-6-3l-1-1z" class="d"></path><path d="M717 136v1 3c-1 5-1 10 1 15v1h-2v1c-1 1 0 1-1 2-2-7 0-16 2-23z" class="K"></path><path d="M394 658v1c1 3 2 5 5 6 2 1 3 1 5 1l-1 1c-1 1-3 1-5 1-2-1-5-3-6-4v-1-2l1-1 1-2z" class="H"></path><path d="M646 392c1 0 2-1 3-2 2-3 1-8 1-12 2 5 4 8 3 14h0c0 1 0 3-1 4v-2c-1-1 0-2-1-3h0l-1-1v1c-2 1-3 2-4 2v-1z" class="R"></path><path d="M204 270l2 1v1c-2 1-2 3-3 5-1 3 0 6 2 9-2 1-2 1-3 2-1-2-1-5-2-6l-2 2v-1c1-1 2-3 3-4 0-2 0-3 1-5 1-1 1-2 2-3v-1z" class="h"></path><path d="M510 159c-1-4 1-7 2-11h1c-1 2-2 4-2 6 0 4 0 8 1 12v1c-1 0-2 1-2 2 0 2 0 2-1 4l-1-1-1-1c1-3 1-5 2-7 0-2 0-3 1-5z" class="H"></path><path d="M342 115c5-4 17-3 23-2 1 0 2 1 3 1l1 1v1l-4-2h-2c-2-1-6-1-8 0-1 0-1 1-2 2l-11-1z" class="d"></path><path d="M384 159h1c-2 6-5 11-10 15l-3 2h0c-1 1-1 1-2 1-1 1-1 1-2 1h-2c-2 1-2 1-4 1l-1-1c4-1 8-2 11-4 6-3 10-9 12-15z" class="K"></path><path d="M288 74c6 3 11 9 13 15l1 2c0 2 1 4 0 6v1l-1 1c-1-7-3-11-7-16l1-1c-1-4-5-5-7-8z" class="d"></path><path d="M715 159c1-1 0-1 1-2v-1h2v1c0 4 3 6 4 8l1 1c2 0 3 1 5 2l-1 1h-1l-2-1h-1c1 1 2 2 4 3l-2 1c-1 0-1-1-2-1-1-3-3-4-5-6l-3-6z" class="C"></path><path d="M629 279a19.81 19.81 0 0 0 11-11c1-5 1-12-1-16-1-1-1-2-1-4 2 3 5 5 6 9v2 4h0-1v1c-2 4-3 7-6 10v1c-2 2-4 4-7 5l-1-1z" class="R"></path><path d="M423 147l3 8h0c2 6 2 16 8 19 1 1 2 1 3 0 4-1 7-3 10-5h0c-3 3-6 5-9 7-1 1-1 1-3 1-1 0-3-1-4-2-2-1-3-3-3-5-1-5-2-9-3-13 0-4-2-7-2-10z" class="K"></path><path d="M616 150l7-1h1c1-1 2-1 3-2h0 1l8-5h1c-1 3-3 4-6 5 1 0 2 0 3-1h1l1-1v-1h5l-3 3-1-1 1-1h0l-5 2-5 3-5 2h-1-3c-2 1-5 1-7 1l1-1c1 0 1 0 2-1l1-1z" class="X"></path><path d="M319 884l23-10c2 1 5 0 7 0-2 1-2 1-4 3l-10 3c-2 1-5 1-6 2 0 0-1 0-1 1-1 0-2 0-3 1h-2c-1 0-2 1-3 0h-1z" class="e"></path><path d="M626 312c4 4 6 7 5 12-1 6-4 11-8 14-3 3-6 4-9 5h-2c1-1 1-1 1-3l2-1c6-3 12-8 14-15 1-3 1-6-1-8 0-2-1-3-1-4h-1z" class="Z"></path><path d="M652 396c1-1 1-3 1-4h0c1 6-2 10-6 14-4 5-8 8-14 9l1-3v-1c-2 0-4 0-6-1h-1c3-1 3 0 5-2h7c4-1 9-5 12-9l1-3z" class="h"></path><defs><linearGradient id="A" x1="433.425" y1="645.104" x2="426.405" y2="648.991" xlink:href="#B"><stop offset="0" stop-color="#1c1a13"></stop><stop offset="1" stop-color="#2f2d2e"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M428 633c1 5 1 9 3 14l3 14-2-1c0 1 0 1-1 2h0c-1-1-2-3-2-4l-1-3h0l-2-21 2-1z"></path><path d="M428 655l1-2c1 2 2 5 3 7 0 1 0 1-1 2h0c-1-1-2-3-2-4l-1-3zm195-532h2v1c-10 0-20 2-27 10-4 4-8 12-7 18 0 6 3 11 7 16h-1l-3-4c-2-2-3-5-4-8v-1c-1-4 0-8 1-13 1-3 3-7 5-10 7-6 18-9 27-9z" class="i"></path><path d="M447 169c5-5 9-10 11-17v-1c1-3 1-8 0-11-3-10-10-15-19-20 3 1 5 1 8 2 1 1 6 5 7 6 0 1 1 2 1 2 4 7 7 14 5 22-1 6-7 14-13 17h0z"></path><path d="M652 394v2l-1 3c-3 4-8 8-12 9h-7c-2-1-4-1-5-3h1c1 0 2 1 3 1-1-1-2-2-3-2 1-1 2-1 3 0h1c1-1 2-2 3-2h1 0c2 0 4 0 5-1 5-2 8-3 11-7z" class="T"></path><path d="M217 194c1 1 2 2 2 3-2 0-3-1-4-2-3-3-4-7-4-11 0-3 0-4 2-6 4-3 7-3 11-3s6 2 8 4l1 1v-1c0-2-1-3-2-4-2-2-5-2-8-2h0 5 0l3 1c5 2 2 5 4 9v1 2c-1 1 0 3 0 5 0 0 1 2 0 3v1c0 1 0 1 1 2 0-1 0-1 1-3v-1c0 1 0 2 1 4-1 1-1 1-2 1-1-1-2-2-4-3v-5c1-4 0-7-1-10-1-2-3-3-5-4-2 0-8 0-11 1-1 1-3 3-3 5 0 3 1 9 4 11 0 1 0 1 1 1z" class="Z"></path><path d="M511 154c1 1 2 2 2 3 1 1 0 1 1 1 2-1 2-2 2-4 1-4 3-9 6-11l-3 9v1c0 2 0 6 1 8s1 4 1 6c-1 0-2 1-2 2-2-2-3-5-4-7-1 1 0 6-1 8h0l-2-4c-1-4-1-8-1-12z" class="G"></path><defs><linearGradient id="C" x1="375.255" y1="126.116" x2="372.793" y2="137.172" xlink:href="#B"><stop offset="0" stop-color="#100c0d"></stop><stop offset="1" stop-color="#2b2e2a"></stop></linearGradient></defs><path fill="url(#C)" d="M364 124c0-1 1-2 2-2s2 0 4 1h0c0 2 3 3 4 4 2 2 4 4 6 7h1c1 2 2 4 3 7 1 0 2 1 2 2v5 9c0 1 0 1-1 2h-1c1-4 1-8 0-11-3-11-11-19-20-24z"></path><path d="M616 468h1c2 11 12 20 21 26 8 6 17 9 26 11 3 1 6 2 8 2l1 1h-2c-11-2-21-4-31-8-2-2-6-4-8-6v-1h2c-8-7-15-15-18-25z" class="R"></path><path d="M627 357c0-1 1-2 2-3 2-4 3-9 2-13v-1h0c1 0 1 0 2 1 2 2 2 5 3 8h-2c-1 6-3 12-7 16h-4l-1-2h-2l-1-1c3-1 5-3 8-5z" class="J"></path><path d="M622 363c3-1 6-3 7-4 3-4 4-10 4-14v-1h0c1 2 1 4 1 5-1 6-3 12-7 16h-4l-1-2z" class="B"></path><path d="M522 143c2-2 5-4 8-4l-1-1c-4 0-6 0-10 2h0c5-3 9-3 15-1h0-2c-3 0-6 2-8 4-3 4-4 9-3 14l2 8h0c2-1 2-1 4-1 2 2 3 4 5 6 3 3 8 6 12 7h1c1 0 2 0 2 1h1c1 0 1 0 2 1v1l-3-1c-2 0-4 0-5-1-4 0-10-4-12-4-1-2-2-5-3-7v-1l-1 2h-1v-1h0c-2-1-3 0-4 0 0-2 0-4-1-6s-1-6-1-8v-1l3-9z" class="J"></path><path d="M737 123c3-1 5-2 8-1h-1c-7 2-12 5-15 11-2 5-3 11-1 15 1 5 5 9 10 12h-1l-1-1c-6-3-9-7-11-13l-2 2c-1-6 0-11 3-17 1-3 6-6 9-8h2z"></path><path d="M726 131c1-3 6-6 9-8h2c-6 5-11 9-12 17v6l-2 2c-1-6 0-11 3-17z" class="N"></path><path d="M717 136c0-1 1-1 2-2 0-1 0-2 1-2l1-1c0-2 1-3 3-4 1-1 2-2 3-2l1-1c1-1 3-1 5-2 1-1 3-1 5-1 1-1 2-1 3-1l1 1c1 0 2 0 3 1-3-1-5 0-8 1h-2c-3 2-8 5-9 8-3 3-4 6-6 9 0 2-1 4-1 5-1 3 0 8-1 10-2-5-2-10-1-15v-3-1z" class="f"></path><path d="M249 123v-1c0-2-1-4-1-6-1-3-1-5-1-8h1c1 5 2 10 4 15h1v1c1 0 1 1 1 2 4 6 8 10 15 12 4 2 9 1 13 0h1l-2 2v2h0c-5 1-9 0-14-1l-2-1s-1 0-2-1c-1 0-1-1-2-1h0c-5-3-11-10-12-15z" class="D"></path><path d="M162 416l1 1 1-1c2 1 4 1 6 3h0c2 1 2 1 3 2v7c-2-2-2-4-4-6-1-1-2-2-4-3l-1-1c-1 3 1 9 2 12 2 7 6 14 11 20 1 1 2 3 4 4 0 1 1 1 1 1-4-6-6-11-8-17l-1-5c1 1 1 3 2 4v1h1v-1s0-1 1-2c1 11 5 18 12 26-6-3-12-8-16-14-6-7-12-21-11-31z" class="H"></path><path d="M174 438c2 6 4 11 8 17 0 0-1 0-1-1-2-1-3-3-4-4-5-6-9-13-11-20-1-3-3-9-2-12l1 1c2 1 3 2 4 3 2 2 2 4 4 6v5l1 5z" class="B"></path><path d="M165 419c2 1 3 2 4 3 2 2 2 4 4 6v5l1 5c-2-2-3-4-4-6-1-1-1-2-2-3h-1c-1-3-2-6-2-10z" class="U"></path><path d="M595 124c1-1 2-1 3-2h1c3-1 8-2 11-1 2 1 3 0 5 0 3 1 5 1 8 2-9 0-20 3-27 9-2 3-4 7-5 10h-1v-5c1-4 5-6 7-9l-4 3-1-1 2-2v-1l-3 2v-1h1l1-2h0l-3 2-1-1c1-1 2-2 3-2l1-1h2z" class="N"></path><path d="M595 124h1c2-1 4-1 7-2h8l-4 1c-3 1-8 2-9 4l-1 1-4 3-1-1 2-2v-1l-3 2v-1h1l1-2h0l-3 2-1-1c1-1 2-2 3-2l1-1h2z" class="f"></path><path d="M720 171c-3-1-5-3-8-4-2-2-4-5-4-8h0l1 2c2 5 9 8 14 10 1 0 1 1 2 1l2-1c-2-1-3-2-4-3h1l2 1h1c3 2 6 3 9 3 1-1 1-2 2-2h12c0 2 0 3 2 4s4 2 5 3c3 1 6 1 9 0h5c-3 1-5 1-7 2-5 1-9 0-14 0 1-1 3-1 5-1h0c-2-2-4-2-6-2-5 0-10 0-15-1h-1c-4-1-9-3-13-4z" class="F"></path><path d="M727 171c-2-1-3-2-4-3h1l2 1h1c3 2 6 3 9 3 1-1 1-2 2-2h12c0 2 0 3 2 4l-1 1c-2 0-4 0-5-1-3-1-6 0-8 0-4-1-7-2-11-3h0z" class="e"></path><path d="M230 221c0 3-5 7-7 9-1 3-3 8-3 11h0l2-3v1c0 3-2 7-1 10l4-5v2c-1 1-1 1-2 3v2h0c-2 1-4 2-5 3v1c-1 2-3 5-5 6l-4 4h-1 0c-1 0-1-1-2-1h-4 0 4c1-1 2-3 2-5v-1h-3l1-1c3 0 4 0 5-2s3-3 4-5c2-5 3-10 5-15s6-10 10-14z" class="h"></path><path d="M218 254v1c-1 2-3 5-5 6l-4 4h-1l1-1c1-5 4-7 9-10z" class="L"></path><defs><linearGradient id="D" x1="719.305" y1="151.332" x2="728.459" y2="167.904" xlink:href="#B"><stop offset="0" stop-color="#959394"></stop><stop offset="1" stop-color="#b8b6b3"></stop></linearGradient></defs><path fill="url(#D)" d="M720 140l2 10c0 1-1 1 0 3 2 3 5 6 8 9 0 1 0 1 2 2 1 0 2 1 3 1 1 1 2 1 3 3h2v1l-2 1h0c-1 0-1 1-2 2-3 0-6-1-9-3l1-1c-2-1-3-2-5-2l-1-1c-1-2-4-4-4-8v-1-1c1-2 0-7 1-10 0-1 1-3 1-5z"></path><path d="M720 140l2 10c0 1-1 1 0 3 2 3 5 6 8 9h-2c-1 0-2-1-3-2v-1c-1-1-2-2-3-4l-2-4c-1-2-1-4-1-6 0-1 1-3 1-5z" class="a"></path><path d="M718 157c4 7 9 10 17 12 1 0 2 0 3 1h0c-1 0-1 1-2 2-3 0-6-1-9-3l1-1c-2-1-3-2-5-2l-1-1c-1-2-4-4-4-8z" class="O"></path><path d="M623 406l1-3h2c1 1 1 1 2 1s2 1 3 2c-1 0-2-1-3-1h-1c1 2 3 2 5 3-2 2-2 1-5 2h1c2 1 4 1 6 1v1l-1 3c-1 1-4 1-5 2-3 2-6 8-8 11l-3 6c-1-1-1-2-2-4 2-8 5-16 8-24z"></path><path d="M426 759h1v5c3 11-1 21-6 31-2 8-5 15-9 22-3 6-9 12-13 18v1l-2 2c-1 2-2 3-4 4h-1c14-17 23-35 29-56 2-4 3-8 4-12 1-5 0-10 1-15h0z" class="K"></path><path d="M512 166l2 4h0c1-2 0-7 1-8 1 2 2 5 4 7 0-1 1-2 2-2s2-1 4 0h0v1c-6 5-13 10-20 14h-2c-2 1-4 1-6 1l-7-1h0-18v-1h3c4 1 9-2 13-1 3 1 4 1 6 1 3-1 7-2 8-5 2-1 3-3 5-5l1 1 1 1c1-2 1-2 1-4 0-1 1-2 2-2v-1z"></path><path d="M230 152c-2-14-3-28-1-42 2-10 7-23 16-29h0c-2 3-5 5-7 8-7 11-8 27-6 40h1v-1c0-1 0-3 1-4 0 1 1 1 0 2v11l1 6-1 1v4l-1 1h0c1 1 1 2 1 3v1h0c0 3-2 5-3 6 0-3 1-8-1-10v1 2z" class="H"></path><path d="M234 124c0 1 1 1 0 2v11l1 6-1 1c-1-5-2-10-2-15h1v-1c0-1 0-3 1-4z" class="T"></path><path d="M217 194c-1 0-1 0-1-1-3-2-4-8-4-11 0-2 2-4 3-5 3-1 9-1 11-1 2 1 4 2 5 4 1 3 2 6 1 10v5c2 1 3 2 4 3 0 2 0 3-1 5v2c-1 0-2-1-2-2v3l-1 1-1 1c-1-3-2-5-2-7 0-5 1-13-2-17h-1v-2c-3 0-4-1-6 0s-3 2-4 4c0 3 2 6 1 8z" class="X"></path><path d="M232 195c2 1 3 2 4 3 0 2 0 3-1 5v2c-1 0-2-1-2-2-1-3-1-5-1-8z" class="M"></path><path d="M217 194c-1-2-2-4-3-7 0-2 1-4 2-6 3-2 6-2 9-2 0 1 1 2 1 3-3 0-4-1-6 0s-3 2-4 4c0 3 2 6 1 8z"></path><path d="M225 179c3 2 5 3 6 7 1 3 0 6 0 9 0 4 0 8 1 12h0l-1 1c-1-3-2-5-2-7 0-5 1-13-2-17h-1v-2c0-1-1-2-1-3z" class="e"></path><defs><linearGradient id="E" x1="364.093" y1="876.215" x2="367.303" y2="844.928" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#383635"></stop></linearGradient></defs><path fill="url(#E)" d="M388 845c1 2 1 2 0 4l1 3c-5 5-11 8-17 11-8 4-14 9-23 11-2 0-5 1-7 0 5-3 10-6 15-8 11-6 22-13 31-21z"></path><path d="M239 490c1-1 1-2 2-3 1 1 1 2 1 3-2 5 0 10-1 14l1 6c1 3 2 6 4 9l3 6-1 1 1 1c0 1 0 0 1 1 0 1 0 1 1 2v1c-2-1-4-3-6-4 1 2 3 4 4 6v1c1 2 4 5 6 6 1 1 2 1 3 2v1 1h1l2 2h-1c-4 0-5-6-9-3-1-1-1-2-2-3-3-4-5-8-7-12-5-12-7-25-3-38z" class="d"></path><path d="M243 522l-1-1v-2-1c-1-3-2-6-2-9 0-2 0-3 1-5l1 6c1 3 2 6 4 9l3 6-1 1 1 1c0 1 0 0 1 1 0 1 0 1 1 2v1c-2-1-4-3-6-4l-2-5z" class="X"></path><path d="M239 490v1c0 4-1 7-1 10-1 10 1 23 8 30 1-2-4-6-3-9l2 5c1 2 3 4 4 6v1c1 2 4 5 6 6 1 1 2 1 3 2v1 1h1l2 2h-1c-4 0-5-6-9-3-1-1-1-2-2-3-3-4-5-8-7-12-5-12-7-25-3-38z" class="K"></path><path d="M628 150c-1 1-1 2-2 2h-2v1h-4l-2 1h-1-4l4 1 1 1h-2c-1 1-5 2-7 3-1 1-2 2-4 3l1 1-1 1v4c1 1 2 2 3 2 4 4 10 6 15 8h-4 0 0l-1 1c-5 0-11 0-15-2-3-4-7-7-10-11l1-2 3 4h1 2 0c-1-3-2-6-1-9 3-4 7-6 11-7 2-1 4-1 6-2l-1 1c-1 1-1 1-2 1l-1 1c2 0 5 0 7-1h3 1l5-2z" class="G"></path><path d="M613 154l4 1 1 1h-2c-1 1-5 2-7 3-1 1-2 2-4 3v-1c0-1 0-2 1-2 1-3 4-4 7-5z" class="Y"></path><path d="M610 152h2-1c-5 1-8 4-10 8l-1 1c1 3 3 5 5 7 1 1 2 2 3 2 4 4 10 6 15 8h-4 0 0l-1 1c-1-1-3-2-5-3s-5-3-7-4-4-4-6-4c-1-3-2-6-1-9 3-4 7-6 11-7z" class="F"></path><path d="M594 164l3 4h1 2 0c2 0 4 3 6 4s5 3 7 4 4 2 5 3c-5 0-11 0-15-2-3-4-7-7-10-11l1-2z" class="W"></path><path d="M342 115l11 1c15 2 34 7 44 21h0v1c1 1 3 3 3 5h-2l-1-1c-1-1-2-3-3-4h-2c-2-1-4-3-6-5v1 1c1 1 3 3 3 5l-1-2c-2-2-5-4-7-4h-1c-2-3-4-5-6-7-1-1-4-2-4-4h0c-2-1-3-1-4-1s-2 1-2 2c-10-5-19-7-30-7l8-2z"></path><path d="M370 123c5 2 12 5 16 10v1 1c1 1 3 3 3 5l-1-2c-2-2-5-4-7-4h-1c-2-3-4-5-6-7-1-1-4-2-4-4z" class="F"></path><path d="M381 134c2 0 5 2 7 4l1 2c3 5 5 9 6 15 0 2 1 3 0 5 0 1 0 2-1 4h0c-1 1 0 1-1 2l-1 1c0 1-1 2-1 3l-1-1c0-1 1-2 1-3s1-2 1-3c-2 3-3 6-6 9h0l-3 2v1c-2 1-6 5-9 5h-1c2-2 4-1 5-4l3-3v-1c-2 0-6 5-9 4l3-2c5-4 8-9 10-15 1-1 1-1 1-2v-9-5c0-1-1-2-2-2-1-3-2-5-3-7z" class="U"></path><path d="M389 161c-1-1 0-2 0-3v-10l-1-4 3 5v1 1c-1 3 0 5 0 8-1 1-2 1-2 2z" class="B"></path><path d="M389 161c0-1 1-1 2-2 0-3-1-5 0-8v-1c1 3 3 9 2 12l-1 1c-2 3-3 6-6 9h0l-2-1 3-6c1-1 1-2 2-4z" class="Y"></path><path d="M550 130v-1c-1-2-1-3 0-4h0c1 0 2 1 2 2l1 1c1 2 2 3 3 5v2h1c0 1 0 4-1 6v2c0 1 0 1-1 2h0c0 2-2 5-2 7-1 1-1 1-1 2v1h-1v1c0 1 0 2 1 4v2h1c0 1 1 2 1 3h1c1 2 3 4 4 5h2l3 3h0c2 1 3 1 4 2h-1c-4 0-6-2-9-3l2 2 3 1c1 1 2 1 3 1 2 1 4 1 5 2h1l2 1h2l1 1h-1-5l-1 1c-2-1-3-1-5-1-2-2-4-3-6-4-3-3-5-6-9-8-1-2-2-5-4-7v-1c-1-3-1-6 1-9l3-7 2-2h-2c1-4 1-8 1-12h-1z" class="f"></path><path d="M552 142v-1c1-2 1-3 2-5v-2c1 2 1 5 0 7-1 4-3 8-4 12-2 4 0 8 1 12l-2-2-1-1c-1-2-1-6 0-8v-1c0-1-1-1-1-2l3-7 2-2z" class="K"></path><path d="M547 151c0 1 1 1 1 2v1c-1 2-1 6 0 8l1 1 2 2c2 1 3 3 4 4s1 1 3 2v1l2 2 3 1c1 1 2 1 3 1 2 1 4 1 5 2h1l2 1h2l1 1h-1-5l-1 1c-2-1-3-1-5-1-2-2-4-3-6-4-3-3-5-6-9-8-1-2-2-5-4-7v-1c-1-3-1-6 1-9z" class="C"></path><defs><linearGradient id="F" x1="625.614" y1="267.738" x2="627.394" y2="294.893" xlink:href="#B"><stop offset="0" stop-color="#000003"></stop><stop offset="1" stop-color="#252220"></stop></linearGradient></defs><path fill="url(#F)" d="M644 257c3 7 2 12-1 19s-11 15-18 18c-4 4-9 6-15 6v1h5-1c-4 2-11 1-14-2h1l2-2c1 0 3 0 4-1h1c3-1 6-1 9-3-4 1-8 0-12 0l1-1c4 0 8 0 12-1 7-1 15-8 20-15l-1-1h0v-1c3-3 4-6 6-10v-1h1 0v-4-2z"></path><path d="M601 299h2l1-1 21-4c-4 4-9 6-15 6v1h5-1c-4 2-11 1-14-2h1z" class="b"></path><path d="M637 275v-1c3-3 4-6 6-10v-1h1c-2 11-6 17-14 24-4 3-9 5-13 6s-8 0-12 0l1-1c4 0 8 0 12-1 7-1 15-8 20-15l-1-1h0z" class="B"></path><path d="M550 130h1c0 4 0 8-1 12h2l-2 2-3 7c-2 3-2 6-1 9v1c2 2 3 5 4 7 4 2 6 5 9 8 2 1 4 2 6 4l-3-1c-3 1-6 0-9 1h-3v-1c-1-1-1-1-2-1h-1c0-1-1-1-2-1-2-1-4-3-6-5-2-4-3-10-2-13v-1l3-6h0c2-4 5-6 8-9 3-4 3-9 2-13z" class="F"></path><path d="M541 168c-1-3-2-5-2-8-1-8 6-13 11-18h2l-2 2c-2 1-4 3-5 5v1c-3 3-4 8-4 12 0 2 0 4 1 5l-1 1z" class="P"></path><path d="M542 167l1 1c2 4 5 6 8 9l1-1c2 0 5 2 7 3-3-3-7-6-9-10v-1c4 2 6 5 9 8 2 1 4 2 6 4l-3-1c-3 0-7 1-9-1-3 1-5-2-7-2l-2-2c-2-1-4-3-4-5l1-1c0 2 1 3 2 4h1c-1-1-2-3-3-4l1-1z" class="I"></path><path d="M545 150v-1c1-2 3-4 5-5l-3 7c-2 3-2 6-1 9v1c2 2 3 5 4 7v1c2 4 6 7 9 10-2-1-5-3-7-3l-1 1c-3-3-6-5-8-9l-1-1c-1-1-1-3-1-5 0-4 1-9 4-12z" class="D"></path><path d="M543 168c0-3-1-5 1-7 0 5 3 11 7 15h1l-1 1c-3-3-6-5-8-9z" class="C"></path><path d="M542 167c-1-1-1-3-1-5 0-4 1-9 4-12 0 2-1 3-1 5-1 2 0 4 0 6-2 2-1 4-1 7l-1-1z" class="S"></path><path d="M245 81v-1c1 0 2-1 2-1 1-1 2-1 3-2h1c2-2 4-3 7-4 1 0 1 0 2-1h2c1-1 2-1 3-1h3l1-1c1 0 7 0 9 1h4c2 1 3 2 5 2 1 0 0 0 1 1 2 3 6 4 7 8l-1 1c-8-6-15-9-25-8-10 2-20 6-26 14-3 4-4 8-5 13h-1v-3h0c-4 6-3 17-4 24l1 1c-1 1-1 3-1 4v1h-1c-2-13-1-29 6-40 2-3 5-5 7-8h0z" class="f"></path><path d="M665 152l-1-2c2-1 3-1 5-1 3 0 6 0 9 1 3 0 5 0 7 1 4 1 7 2 10 5 4 2 7 6 10 8 1 1 1 0 1 1h0c1 3 8 6 11 7 1 1 2 1 3 2-6-1-9-2-14-5 2 1 3 2 4 3 2 1 4 2 4 3-1 0-2 0-3-1-2-1-3-2-5-3 0 1 1 2 2 3-2 0-6-4-7-4h-3l-5-2c-5-3-8-8-14-11-4-2-11-2-14-5z" class="T"></path><defs><linearGradient id="G" x1="672.811" y1="163.883" x2="690.561" y2="155.95" xlink:href="#B"><stop offset="0" stop-color="#000403"></stop><stop offset="1" stop-color="#241f1d"></stop></linearGradient></defs><path fill="url(#G)" d="M665 152c3-2 9 0 12 0 10 3 16 11 24 18h-3l-5-2c-5-3-8-8-14-11-4-2-11-2-14-5z"></path><path d="M706 165c3 2 7 5 11 6l3 2v-2c4 1 9 3 13 4h1c5 1 10 1 15 1 2 0 4 0 6 2h0c-2 0-4 0-5 1l-6 1h-14-1c-2 1-6 0-9 0-5-1-11-1-16 0l-2-2h-4l-5 1c-3 0-5 2-8 1l-2-1c1-1 2-1 3-2h-1l1-1-10 1h0c-1-1-1-1-2-1h-2-1c2-2 8-1 10-2h1c2-1 3-1 4-2h1c1-1 2-1 3-3 1 0 2 0 3-1l5 2h3c1 0 5 4 7 4-1-1-2-2-2-3 2 1 3 2 5 3 1 1 2 1 3 1 0-1-2-2-4-3-1-1-2-2-4-3 5 3 8 4 14 5-1-1-2-1-3-2-3-1-10-4-11-7h0z" class="a"></path><path d="M720 171c4 1 9 3 13 4-1 1-3 1-5 1h-1c-1 0-3 0-4-1h-1l-2-2v-2z" class="c"></path><path d="M699 172c4 0 7 3 11 4l-2 1h0l-6 1h-4c1 0 1-1 2-1h1v-2l-2-1h1l-1-2z" class="L"></path><path d="M701 175c3 0 5 1 7 2h0l-6 1h-4c1 0 1-1 2-1h1v-2z" class="K"></path><path d="M730 178c3-1 12-2 15 0h0c0 1 0 1-1 2h-14-1l1-2z" class="U"></path><path d="M702 178l6-1 22 1-1 2c-2 1-6 0-9 0-5-1-11-1-16 0l-2-2z" class="I"></path><path d="M695 173l4-1 1 2h-1l2 1v2h-1c-1 0-1 1-2 1l-5 1c-3 0-5 2-8 1l-2-1c1-1 2-1 3-2l3-1-1-1h1c1-1 3-1 4-2h0 2z" class="H"></path><path d="M695 173l4-1 1 2h-1l-10 2-1-1h1c1-1 3-1 4-2h0 2z" class="E"></path><path d="M693 168l5 2-3 3h-2 0c-1 1-3 1-4 2h-1l1 1-3 1h-1l1-1-10 1h0c-1-1-1-1-2-1h-2-1c2-2 8-1 10-2h1c2-1 3-1 4-2h1c1-1 2-1 3-3 1 0 2 0 3-1z" class="I"></path><path d="M693 168l5 2-3 3h-2 0c-1 0-3 0-4 1-1 0-2 1-4 1v-1l2-1v-1c1-1 2-1 3-3 1 0 2 0 3-1z" class="D"></path><path d="M300 155c6 2 11 7 17 10 2 1 5 1 7 2l4 1c8 2 19 1 27-3 4-3 8-6 9-11 2-9-4-16-8-24 6 5 11 11 14 17 1 2 1 4 1 6l-1 1v-2c-2 1 0 3-2 4 0 1-1 1-1 2l-1-1-1 2c0 4-2 7-5 9-4 3-9 4-14 5h-2l-2-1c-2 1-5 0-8 0l-19-2h-2v-1c-3 0-3-1-6-2h0v-2c-3-1-5-2-7-2v-1h0c-1-1-2-2-3-2-2 0-2 0-4-1l2-1 2-2v-1c1-1 1-1 3 0z" class="a"></path><path d="M366 150v-5h0l3 3 1-1c1 2 1 4 1 6l-1 1v-2c-2 1 0 3-2 4 0 1-1 1-1 2l-1-1c1-3 1-5 0-7z" class="c"></path><path d="M297 155c1-1 1-1 3 0h-1l2 2 15 8c1 1 3 3 4 3-1 1-4 1-5 2h-2v-1c-3 0-3-1-6-2h0v-2c-3-1-5-2-7-2v-1h0c-1-1-2-2-3-2-2 0-2 0-4-1l2-1 2-2v-1z" class="L"></path><path d="M293 159l2-1c2 1 4 1 6 1v1c1 0 2 0 3 1l-1 2c-1-1-2-1-3-1h0c-1-1-2-2-3-2-2 0-2 0-4-1z" class="G"></path><path d="M300 162c1 0 2 0 3 1 3 0 5 1 7 1s4 2 6 1c1 1 3 3 4 3-1 1-4 1-5 2h-2v-1c-3 0-3-1-6-2h0v-2c-3-1-5-2-7-2v-1z" class="I"></path><path d="M307 165h3c2 0 3 2 5 3l-2 1c-3 0-3-1-6-2h0v-2z" class="D"></path><path d="M366 150c1 2 1 4 0 7l-1 2c0 4-2 7-5 9-4 3-9 4-14 5h-2l-2-1c-2 1-5 0-8 0l-19-2c1-1 4-1 5-2 4 0 7 1 10 2h13 1c6-2 12-5 18-9 3-3 4-7 4-11z" class="P"></path><path d="M365 159c0 4-2 7-5 9-4 3-9 4-14 5h-2l-2-1c6-1 11-2 16-5 2-2 5-5 7-8z" class="g"></path><path d="M234 137c1-2 1-3 2-4l1 1 2 6c0 1 1 1 1 2h1c1 1 2 4 4 5v-1c2 1 2 3 2 4l1 2c1 2 1 4 1 6l-3 3-1-2v1l-1-1v-1h-1v4s1 1 1 2 0 5-1 7h0v1c0 1 0 1-1 2s-1 5-1 7h-1v10c-2 0-2 0-3-1v3 1c-1 2-1 2-1 3-1-1-1-1-1-2v-1c1-1 0-3 0-3 0-2-1-4 0-5v-2-1c0-5 2-11 0-16-1-2-3-3-4-4-1-3-1-8-1-11v-2-1c2 2 1 7 1 10 1-1 3-3 3-6h0v-1c0-1 0-2-1-3h0l1-1v-4l1-1-1-6z"></path><path d="M234 156c1 2 2 4 2 6h-4c0-3 1-4 2-6z" class="P"></path><defs><linearGradient id="H" x1="241.01" y1="179.088" x2="236.859" y2="189.75" xlink:href="#B"><stop offset="0" stop-color="#878584"></stop><stop offset="1" stop-color="#a3a19f"></stop></linearGradient></defs><path fill="url(#H)" d="M239 176c1 2 1 3 1 5v10c-2 0-2 0-3-1l2-14z"></path><path d="M235 143c0 1 0 1 1 2v2c1 2 1 3 1 4l1 1c0 2 0 3-1 5 0 2 3 7 3 9 0 1 1 2 1 2 0 2 1 3 2 3v1c0 1 0 1-1 2s-1 5-1 7h-1c0-2 0-3-1-5 1-5 0-10-2-14h-1c0-2-1-4-2-6 1-3 0-5 0-8v-4l1-1z" class="W"></path><path d="M234 137c1-2 1-3 2-4l1 1 2 6c0 1 1 1 1 2h1c1 1 2 4 4 5v-1c2 1 2 3 2 4l1 2c1 2 1 4 1 6l-3 3-1-2v1l-1-1v-1h-1v4s1 1 1 2 0 5-1 7h0c-1 0-2-1-2-3 0 0-1-1-1-2 0-2-3-7-3-9 1-2 1-3 1-5l-1-1c0-1 0-2-1-4v-2c-1-1-1-1-1-2l-1-6z" class="a"></path><path d="M243 171c-2-8-5-16-4-23l1-1c2 4 5 8 5 12v1l-1-1v-1h-1v4s1 1 1 2 0 5-1 7z" class="O"></path><path d="M240 142h1c1 1 2 4 4 5v-1c2 1 2 3 2 4l1 2c1 2 1 4 1 6l-3 3-1-2c0-4-3-8-5-12 0-1 0-1-1-2h0l1-1v-2z" class="d"></path><path d="M389 140c0-2-2-4-3-5v-1-1c2 2 4 4 6 5h2c1 1 2 3 3 4l1 1h2c0-2-2-4-3-5v-1c3 3 6 8 7 12 1-1 1-1 1-2 1 4 3 7 3 11 0 7-1 11-5 16-1 0-2 0-3-1l-3 4-1-1-5 3c1-2 3-3 4-4-1 0-2 0-3 1v-1-1l-4 4h0l-7 3c1-1 3-3 3-4-1 1-3 3-5 3 0 1-1 1-2 1 2-3 5-4 6-6v-1l3-2h0c3-3 4-6 6-9 0 1-1 2-1 3s-1 2-1 3l1 1c0-1 1-2 1-3l1-1c1-1 0-1 1-2h0c1-2 1-3 1-4 1-2 0-3 0-5-1-6-3-10-6-15z" class="g"></path><path d="M396 162h0l1 1c0 1 0 2-1 4 0 2-2 4-4 6l-1-1c2-3 3-7 5-10z" class="d"></path><path d="M396 154c-1-3-2-7-3-10h1c2 3 3 6 4 9v5h1 1v3c0 2 1 5 0 6s-2 3-2 4l-3 3h-1l1-1c4-7 3-11 1-19z" class="E"></path><path d="M398 153h0 1 1c0 1 1 2 2 3 1 3 1 7 0 10v1c0 1-1 3-2 4v1l-4 4-5 3c1-2 3-3 4-4h1c1-1 2-3 2-4s1-3 2-4 0-4 0-6v-3h-1-1v-5z" class="L"></path><path d="M389 140c0-2-2-4-3-5v-1-1c2 2 4 4 6 5 5 5 9 11 10 18-1-1-2-2-2-3h-1-1 0c-1-3-2-6-4-9h-1c1 3 2 7 3 10l-1 1c-1-6-3-10-6-15z" class="V"></path><defs><linearGradient id="I" x1="398.537" y1="143.251" x2="404.477" y2="156.047" xlink:href="#B"><stop offset="0" stop-color="#1c1b1a"></stop><stop offset="1" stop-color="#494942"></stop></linearGradient></defs><path fill="url(#I)" d="M397 137c3 3 6 8 7 12 1-1 1-1 1-2 1 4 3 7 3 11 0 7-1 11-5 16-1 0-2 0-3-1l-3 4-1-1 4-4v-1c1-1 2-3 2-4v-1c1-3 1-7 0-10-1-7-5-13-10-18h2c1 1 2 3 3 4l1 1h2c0-2-2-4-3-5v-1z"></path><path d="M405 147c1 4 3 7 3 11 0 7-1 11-5 16-1 0-2 0-3-1 2-2 3-5 4-8h0c1-6 0-11 0-16 1-1 1-1 1-2z" class="N"></path><path d="M614 440l1-10c1 2 1 3 2 4 0 4 1 7 4 10l6 3 1 1c2 0 4 1 6 0l2-1c2-3 2-6 2-9v-2h0c3 2 4 4 4 8 1 5-2 10-5 13-6 6-12 9-20 11h-1l-3-12 1-7c-1-3 0-6 0-9z" class="H"></path><path d="M638 438c1 1 3 2 3 5s-1 6-3 9h0l-2-1-1 1h-1l2-3h0-2v-1l2-1c2-3 2-6 2-9z" class="J"></path><path d="M614 440l1-10c1 2 1 3 2 4 0 4 1 7 4 10l6 3 1 1c2 0 4 1 6 0v1h2 0l-2 3h1l1-1 2 1c-2 2-4 4-7 4h0c-3 1-5 1-7 1-4-1-6-3-8-5-1-2-1-2-1-4v-2l-1 3c-1-3 0-6 0-9z" class="M"></path><defs><linearGradient id="J" x1="614.688" y1="439.869" x2="625.284" y2="441.335" xlink:href="#B"><stop offset="0" stop-color="#0f0e0d"></stop><stop offset="1" stop-color="#2c2a29"></stop></linearGradient></defs><path fill="url(#J)" d="M614 440l1-10c1 2 1 3 2 4 0 4 1 7 4 10l6 3 1 1h-2 0l-1 1-3-1c-3-2-6-4-8-8z"></path><path d="M616 452l1-1c2 2 3 3 6 3-1-1-2-1-3-2-2-1-3-3-4-5 3 2 4 3 7 3 1 1 1 1 2 1 4 1 6 0 9-2h2 0l-2 3h1l1-1 2 1c-2 2-4 4-7 4h0c-3 1-5 1-7 1-4-1-6-3-8-5z" class="W"></path><path d="M634 349h2c0 2-1 5-1 7-2 5-4 8-5 12-3 6-2 16 3 21 2 2 6 4 10 4l3-1v1c1 0 2-1 4-2v-1l1 1h0c1 1 0 2 1 3-3 4-6 5-11 7-1 1-3 1-5 1h0-1c-1 0-2 1-3 2h-1c-1-1-2-1-3 0-1 0-1 0-2-1h-2c0-3 0-5-1-7-1-6-4-12-4-18 0-5 4-9 8-13s6-10 7-16z" class="H"></path><path d="M636 402l-3-3c-2 0-2-2-4-3-1-1-1-1-2-1-2-4-4-9-4-14 0-2 0-4 1-6l1 1c1 4 3 8 5 12v1c2 1 4 3 6 4h1c2 1 6 1 9 0 1 0 2-1 4-2v-1l1 1h0c1 1 0 2 1 3-3 4-6 5-11 7-1 1-3 1-5 1h0z" class="F"></path><path d="M651 391c-1 3-2 4-5 6-2 1-6 1-8 1-3-1-7-5-8-8v-1c2 1 4 3 6 4h1c2 1 6 1 9 0 1 0 2-1 4-2v-1l1 1z" class="d"></path><path d="M223 251l2 2 2-2 1 1-1 2h1l3-1v1l-1 1h1c1-1 2-2 4-3v1h0c-1 1-3 4-3 5l1 1-1 3h0l1 1-1 1-1 1 1 1c-1 2-1 4-3 5h0-1v-1h-1c-1 2-2 3-3 4l-1 1c-1 0-2 1-3 3 1 0 1 0 0 1l-1 1h0v-3h-2c-1 1-3 2-4 4h0c-2 3-3 4-2 8h-1l-1 1v-1s0-1-1-1v2h-1l-2-4c-2-3-3-6-2-9 1-2 1-4 3-5v-1h1c2-1 2-3 2-4h0l-1 1h-1l1-3h0 1l4-4c2-1 4-4 5-6v-1c1-1 3-2 5-3z" class="I"></path><path d="M214 267v-1c0-1 1-3 2-4 2-3 5-4 7-6-1 3-4 6-6 9l-3 2z" class="G"></path><path d="M213 261c0 3-2 4-3 6 1 0 2-1 3-2v1c0 1-1 3-2 4h1c1 0 2-2 2-3l3-2 1 2c-1 2-3 4-5 5l-1 1h-2 0v1c1 0 2 1 3 2l-1 1v1c-1 0-2 1-2 2h-3v-2l-1-1c1-1 2-2 2-3l-2-2v-1h1c2-1 2-3 2-4h0l-1 1h-1l1-3h0 1l4-4z" class="R"></path><path d="M207 278c1-1 1-3 3-3 0 1 1 1 2 2v1c-1 0-2 1-2 2h-3v-2z" class="P"></path><path d="M206 272l2 2c0 1-1 2-2 3l1 1v2h3c0-1 1-2 2-2h0c0 1 0 2-1 3h2 0c-2 3-3 4-2 8h-1l-1 1v-1s0-1-1-1v2h-1l-2-4c-2-3-3-6-2-9 1-2 1-4 3-5z" class="Q"></path><path d="M207 280h3c0 2 0 2 1 3l-3 3-1-2c-1-1 0-3 0-4z" class="T"></path><path d="M218 267h1 3v1h0l1 1 2-1v1l-1 1v1l1-1h2c-1 2-2 3-3 4l-1 1c-1 0-2 1-3 3 1 0 1 0 0 1l-1 1h0v-3h-2c-1 1-3 2-4 4h-2c1-1 1-2 1-3h0v-1l1-1c-1-1-2-2-3-2v-1h0 2l1-1c2-1 4-3 5-5z" class="b"></path><path d="M213 276c0-2 3-3 4-5 1-1 3-2 4-3l-3 5h1 1c0 1-1 2-2 3l-1 1c-1 1-3 2-4 4h-2c1-1 1-2 1-3h0v-1l1-1z" class="O"></path><path d="M227 251l1 1-1 2h1l3-1v1l-1 1h1c1-1 2-2 4-3v1h0c-1 1-3 4-3 5l1 1-1 3h0l1 1-1 1-1 1 1 1c-1 2-1 4-3 5h0-1v-1h-1-2l-1 1v-1l1-1v-1l-2 1-1-1h0v-1h-3c1-2 3-3 4-4 0-1 1-1 2-1 1-1 2-3 3-3 0-1 0-1 1-2h-1c0-1 0-1-1-2l-3 2h0c1-1 1-2 1-3l-2 1v-1l2-1 2-2z" class="J"></path><path d="M238 197h0v4l1 1c-1 2-2 5-1 8l1 13v5c0 3 2 12 2 14h-1v-2c-1-1-1-2 0-3h-1v4c0 3-1 4-2 5s-1 2-2 3v1 1-1l3-3v1c0 2-1 4-3 5v-1c-2 1-3 2-4 3h-1l1-1v-1l-3 1h-1l1-2-1-1-2 2-2-2h0v-2c1-2 1-2 2-3v-2l-4 5c-1-3 1-7 1-10v-1l-2 3h0c0-3 2-8 3-11 2-2 7-6 7-9 1-5 2-9 1-13l1-1 1-1v-3c0 1 1 2 2 2v-2c1-2 1-3 1-5 1 0 1 0 2-1z" class="C"></path><path d="M238 210l1 13v5c-1 1-2 1-2 2l-1 1c-1 1-2 4-3 6l-1 1h-1l1-2v-1l-4 4h0c3-7 7-13 8-20h-1l2-1c1-1 0-7 1-8z" class="O"></path><path d="M238 197h0v4l1 1c-1 2-2 5-1 8-1 1 0 7-1 8l-2 1c0-2 1-3 1-4h-2l-1 3c0 3-3 8-5 10l-1 2-1 1s0 1-1 2l-1 1c0 1-2 3-2 4l-2 3h0c0-3 2-8 3-11 2-2 7-6 7-9 1-5 2-9 1-13l1-1 1-1v-3c0 1 1 2 2 2v-2c1-2 1-3 1-5 1 0 1 0 2-1z" class="c"></path><path d="M238 197h0v4l1 1c-1 2-2 5-1 8-1 1 0 7-1 8l-2 1c0-2 1-3 1-4h-2v-3l1-2c0-2 1-3 0-5v-2c1-2 1-3 1-5 1 0 1 0 2-1z" class="K"></path><path d="M238 201l1 1c-1 2-2 5-1 8-1 1 0 7-1 8l-2 1c0-2 1-3 1-4v-6c1-1 0-1 0-2 1-2 1-4 2-6z" class="h"></path><path d="M239 228c0 3 2 12 2 14h-1v-2c-1-1-1-2 0-3h-1v4c0 3-1 4-2 5s-1 2-2 3v1 1-1l3-3v1c0 2-1 4-3 5v-1c-2 1-3 2-4 3h-1l1-1v-1l-3 1h-1l1-2-1-1-2 2-2-2h0v-2c1-2 1-2 2-3v-2c0-2 2-4 3-5h0l4-4v1l-1 2h1l1-1c1-2 2-5 3-6l1-1c0-1 1-1 2-2z" class="M"></path><path d="M229 250l6-10c0 3 0 4-1 6l-1 1c0 1 0 1-1 1l-2 2h0-1z" class="F"></path><path d="M229 245c3-3 6-7 9-10l-3 5-6 10c-1 0-2 1-2 1l-2 2-2-2h0v-2c1-2 1-2 2-3l1 1c1-2 1-2 3-2z" class="R"></path><path d="M225 246l1 1c1-2 1-2 3-2l-6 6v-2c1-2 1-2 2-3z" class="O"></path><path d="M816 145c0-1 1-1 2-1 4-2 7-4 10-6 4-2 6-6 10-7l-12 18-1-1-1-1c-2 3-5 5-8 7-2 2-5 4-7 6-2 0-3 1-4 1-2 0-3 0-4 1-1 0-2 0-4 1v-1l1-1c1 0 1 0 2-1v-1l-2 1-4 1h1c-2 1-3 1-5 1v1c-2 0-3 0-4-1l-2-1-4-2s-3-2-4-3c-4-2-9-4-12-6h2c17-1 34 0 50-5z" class="X"></path><path d="M806 156c2 0 3-1 5 0h0c1-1 2-1 3-2l9-6 1-1c-2 3-5 5-8 7-2 2-5 4-7 6-2 0-3 1-4 1-2 0-3 0-4 1-1 0-2 0-4 1v-1l1-1c1 0 1 0 2-1v-1l6-3z" class="L"></path><path d="M816 145c-1 2-5 2-7 3-8 1-15 2-23 2-1 0-5 0-5 1s1 0 2 1l4 2c1 0 1 1 2 1 1 1 4 1 6 0h1c2 0 3 0 6-1h3 1l-2 1c1 1 1 1 2 1l-6 3-2 1-4 1h1c-2 1-3 1-5 1v1c-2 0-3 0-4-1l-2-1-4-2s-3-2-4-3c-4-2-9-4-12-6h2c17-1 34 0 50-5z" class="E"></path><path d="M790 158h1 1l1-1h3l2 3-4 1c-2-1-3-2-4-3z" class="f"></path><path d="M804 155c1 1 1 1 2 1l-6 3-2 1-2-3c3 0 5-1 8-2z" class="Y"></path><path d="M780 159s-3-2-4-3c-4-2-9-4-12-6h2c5 0 10 2 14 4 3 1 7 4 10 4 1 1 2 2 4 3h1c-2 1-3 1-5 1v1c-2 0-3 0-4-1l-2-1-4-2z" class="K"></path><path d="M780 159c2-1 3-1 4-1l2 1c2 1 3 2 4 3h0v1c-2 0-3 0-4-1l-2-1-4-2z"></path><path d="M720 140c2-3 3-6 6-9-3 6-4 11-3 17l2-2c2 6 5 10 11 13l1 1h1c2 0 5 1 7 2 10 1 20 0 29 0 3 0 7 0 10-1l2 1c1 1 2 1 4 1v-1c2 0 3 0 5-1h-1l4-1 2-1v1c-1 1-1 1-2 1l-1 1v1c2-1 3-1 4-1 1-1 2-1 4-1l-10 4c-5 1-11 2-15 5 0 0 1 0 1 1l-2 1v1c-5 2-9 3-13 4-3 1-6 1-9 0-1-1-3-2-5-3s-2-2-2-4h-12 0l2-1v-1h-2c-1-2-2-2-3-3-1 0-2-1-3-1-2-1-2-1-2-2-3-3-6-6-8-9-1-2 0-2 0-3l-2-10z" class="G"></path><path d="M720 140c2-3 3-6 6-9-3 6-4 11-3 17 2 6 5 10 11 13l6 3c-1 0-3-1-4-1-7-3-11-7-14-13l-2-10z" class="b"></path><path d="M725 146c2 6 5 10 11 13l1 1c2 1 3 2 4 2h1c3 1 6 1 9 2-1 1-2 1-3 1-1-1-2-1-3-1-2-1-3 0-5 0l-6-3c-6-3-9-7-11-13l2-2z" class="B"></path><path d="M738 160c2 0 5 1 7 2 10 1 20 0 29 0 3 0 7 0 10-1l2 1c1 1 2 1 4 1-2 0-4 1-6 1-1 1-3 2-5 2h-2l-10 3h-2c-1 0 0 0-1 1h-4-2l-1 1c1 1 2 2 3 2l1 1h2c-1 1-2 1-3 1v-1h-3l-3-2-1 1c-1-1 0-1-1-2 0-1 0-2 1-2l1-1 1-1h1c1-1 1-1 2-1h4c2-1 3-1 5-1 0 0 1-1 2-1h1 0c-2-1-5-1-7 0h-12c-3-1-6-1-9-2h-1c-1 0-2-1-4-2h1z" class="Q"></path><path d="M800 159v1c-1 1-1 1-2 1l-1 1v1c2-1 3-1 4-1 1-1 2-1 4-1l-10 4c-5 1-11 2-15 5 0 0 1 0 1 1l-2 1v1c-5 2-9 3-13 4-3 1-6 1-9 0l1-1-1-1c-2-1-3-1-4-2l1-1 3 2h3v1c1 0 2 0 3-1h-2l-1-1c-1 0-2-1-3-2l1-1h2 4c1-1 0-1 1-1h2l10-3h2c2 0 4-1 5-2 2 0 4-1 6-1v-1c2 0 3 0 5-1h-1l4-1 2-1z" class="D"></path><path d="M767 169c2 0 4 0 6-1h4l-4 1 1 1-11 3h-2c-1 0-2-1-3-3h2 4c1-1 0-1 1-1h2z" class="E"></path><path d="M797 163c2-1 3-1 4-1 1-1 2-1 4-1l-10 4c-5 1-11 2-15 5-4 1-11 5-15 4l-2-1 11-3c4-1 8-3 12-4 3-1 8-1 11-3z" class="H"></path><path d="M763 173l2 1c4 1 11-3 15-4 0 0 1 0 1 1l-2 1v1c-5 2-9 3-13 4-3 1-6 1-9 0l1-1-1-1c-2-1-3-1-4-2l1-1 3 2h3v1c1 0 2 0 3-1h-2l-1-1c-1 0-2-1-3-2l1-1c1 2 2 3 3 3h2z" class="c"></path><path d="M367 158c0-1 1-1 1-2 2-1 0-3 2-4v2l1-1c1 3 1 6 1 10v1c-1 5-4 8-8 11-1 1-2 1-3 2l-1 1h1l1 1c2 0 2 0 4-1h2c1 0 1 0 2-1 1 0 1 0 2-1h0c3 1 7-4 9-4v1l-3 3c-1 3-3 2-5 4h1c3 0 7-4 9-5-1 2-4 3-6 6 1 0 2 0 2-1 2 0 4-2 5-3 0 1-2 3-3 4l7-3h0l4-4v1 1c1-1 2-1 3-1-1 1-3 2-4 4l5-3 1 1 3-4c1 1 2 1 3 1l-2 3 1 1-3 3h4l2-1h3c2-1 2-2 4-1h0c1 2 2 2 3 3h4-45c-30 0-61 1-89-11 4 0 10 3 14 4l12 3c0-1 1-2 2-2 2-1 6 1 7 0l2-1c0-1 2 0 3-1v-1h3c2 1 4 0 6-1 3 0 6 1 8 0l2 1h2c5-1 10-2 14-5 3-2 5-5 5-9l1-2 1 1z" class="M"></path><path d="M400 173c1 1 2 1 3 1l-2 3c-3 3-5 4-9 4h0c1-2 3-3 5-4l3-4z" class="L"></path><path d="M388 178h0l4-4v1 1c1-1 2-1 3-1-1 1-3 2-4 4l5-3 1 1c-2 1-4 2-5 4h-1-5l2-2v-1z" class="b"></path><path d="M367 158c0-1 1-1 1-2 2-1 0-3 2-4v2c1 7 0 12-4 17-2 2-4 4-7 5h-1l1-1c2-1 3-3 5-5-2 2-4 4-6 5-8 4-17 4-25 3-3 0-6-1-9 0s-10 0-13 0c0-1 1-2 2-2 2-1 6 1 7 0l2-1c0-1 2 0 3-1v-1h3c2 1 4 0 6-1 3 0 6 1 8 0l2 1h2c5-1 10-2 14-5 3-2 5-5 5-9l1-2 1 1z" class="Q"></path><path d="M334 172c3 0 6 1 8 0l2 1h2l1 1c-1 1-2 1-3 2h3 0c-7 1-14 0-21-1h0 7c-2-1-3-1-5-2 2 1 4 0 6-1z" class="a"></path><path d="M334 172c3 0 6 1 8 0l2 1-3 1c-1 1-3 0-4 0s-3 1-4 1c-2-1-3-1-5-2 2 1 4 0 6-1z" class="W"></path><path d="M366 157l1 1c-1 6-4 11-9 14-3 3-7 4-11 4h-3c1-1 2-1 3-2l-1-1c5-1 10-2 14-5 3-2 5-5 5-9l1-2z" class="S"></path><path d="M480 133l-1-1 1-1h1l8 8c1-1 1-1 1-2h1c2 1 4 3 7 5 1 2 5 4 5 6 3 4 5 8 5 13l2-2c-1 2-1 3-1 5-1 2-1 4-2 7-2 2-3 4-5 5-1 3-5 4-8 5-2 0-3 0-6-1-4-1-9 2-13 1-2-1-3 0-5 0v-1h-2l1-1h1 1l-1-1h0c4-1 7-3 11-5s7-5 8-10c3-12-4-21-9-30z" class="R"></path><path d="M505 152c1 2 2 7 1 9 0 2-1 3-1 5-1-5-2-8-3-12v-1c2 0 2 0 3-1z" class="N"></path><path d="M498 163v-1-4-1h1v1 1h2v-3c1 4 1 9-2 13-1 1-1 1-2 1 1-3 1-5 1-7z" class="G"></path><path d="M498 173l1-3c0 2-1 4-2 6h1c2-1 3-3 4-5 0-1 1-3 2-4l-3 8 1 1c-1 3-5 4-8 5-2 0-3 0-6-1-4-1-9 2-13 1-2-1-3 0-5 0v-1h1c4 0 8-1 12-3 2 1 4 0 7 0h1c1 0 3-2 3-3h1 0c0 1-1 2 0 2h1c1-1 2-2 2-3z" class="Z"></path><path d="M489 139c1-1 1-1 1-2h1c2 1 4 3 7 5 1 2 5 4 5 6l2 4c-1 1-1 1-3 1v1c-2-4-5-10-9-13v1h0c4 4 7 9 8 14v3h-2v-1-1c0-2 0-4-1-5l-1 1c0 1 1 2 0 3h-1v-3c-1-6-4-10-7-14z" class="X"></path><path d="M483 177c3-2 6-5 9-8 3-5 4-11 3-16h0c-2-8-8-16-14-21v-1l8 8c3 4 6 8 7 14v3h1c1-1 0-2 0-3l1-1c1 1 1 3 1 5h-1v1 4 1c0 2 0 4-1 7v2h0l1 1c0 1-1 2-2 3h-1c-1 0 0-1 0-2h0-1c0 1-2 3-3 3h-1c-3 0-5 1-7 0z" class="S"></path><path d="M496 156h1c1-1 0-2 0-3l1-1c1 1 1 3 1 5h-1v1 4 1c0 3-3 7-5 9l2-5c1-4 1-7 1-11z" class="N"></path><path d="M480 133l-1-1 1-1h1v1c6 5 12 13 14 21h0c1 5 0 11-3 16-3 3-6 6-9 8-4 2-8 3-12 3h-1-2l1-1h1 1l-1-1h0c4-1 7-3 11-5s7-5 8-10c3-12-4-21-9-30z" class="O"></path><path d="M480 133l-1-1 1-1h1v1c6 5 12 13 14 21h0c1 5 0 11-3 16-3 3-6 6-9 8-4 2-8 3-12 3 2-2 8-2 11-4 5-2 8-5 9-10l1-1c1-4 1-10-1-15-2-6-5-14-11-17z" class="X"></path><path d="M271 151c1-1 3-1 5-2 3-1 6-4 8-7h0l1 1h1 0v1c-1 4-5 3-6 7 0 2 0 3 1 4 1 2 2 2 4 3l1 1v-1l11-3v1l-2 2-2 1c2 1 2 1 4 1 1 0 2 1 3 2h0v1c2 0 4 1 7 2v2h0c3 1 3 2 6 2v1h2l19 2c-2 1-4 2-6 1h-3v1c-1 1-3 0-3 1l-2 1c-1 1-5-1-7 0-1 0-2 1-2 2l-12-3c-4-1-10-4-14-4h-1-1c-2-1-5-2-7-4l-5-4-8-6h0c-1-1-4-3-6-4-3-2-9-7-10-10h1c2 2 3 3 5 4h1l-2-2c2 0 4 1 6 2 1 0 3 1 4 1l6 3 1-1 2 1z" class="T"></path><path d="M288 164h2c2 1 3 1 5 2l-1 1c-2 0-5-1-8-2h0l2-1z" class="P"></path><path d="M320 173l-1-1c-5-1-9 0-14-1-1 0-1-1-2-1-3 0-7-1-9-2 1-1 2 0 3 0h7l-1-1h-1c2-1 3 0 5 0h0c3 1 3 2 6 2v1h2l19 2c-2 1-4 2-6 1h-3-3-2z" class="V"></path><path d="M299 175v-1-1c-2 0-6-1-8-2l1-1 5 2 7 2c2-1 4-2 6-2 3 1 7 1 10 1h2 3v1c-1 1-3 0-3 1l-2 1c-1 1-5-1-7 0-1 0-2 1-2 2l-12-3z" class="D"></path><path d="M304 174c2-1 4-2 6-2 3 1 7 1 10 1h2l-1 1c-1 0-4 1-5 2-4-1-8-1-12-2z" class="B"></path><path d="M263 157c-1-1-4-3-6-4-3-2-9-7-10-10h1c2 2 3 3 5 4h1c6 5 14 10 22 13 4 1 6 3 11 4h1l-2 1h0c-1 0 0 0-1 1 1 0 1 1 3 1l1 1h0v1s-1-1-2-1h-1c-1 0-1-1-2-1-7-3-14-6-21-10z" class="X"></path><path d="M271 151c1-1 3-1 5-2 3-1 6-4 8-7h0l1 1h1 0v1c-1 4-5 3-6 7 0 2 0 3 1 4 1 2 2 2 4 3l1 1v-1l11-3v1l-2 2-2 1c2 1 2 1 4 1 1 0 2 1 3 2h0v1c-3 0-5 0-8-1h-3l-1 1h1c1 0 1 0 2 1h-1-2-1c-5-1-7-3-11-4-8-3-16-8-22-13l-2-2c2 0 4 1 6 2 1 0 3 1 4 1l6 3 1-1 2 1z" class="L"></path><path d="M297 155v1l-2 2-2 1c-3 2-6 3-10 2l-1-1c1-1 2-1 4-1v-1l11-3z" class="Z"></path><path d="M271 151c1-1 3-1 5-2 3-1 6-4 8-7h0l1 1h1 0v1c-1 4-5 3-6 7 0 2 0 3 1 4 1 2 2 2 4 3l1 1c-2 0-3 0-4 1l-4-1c-1 0-6-3-7-3l-9-5h4c2 1 3 1 4 0h1z" class="T"></path><path d="M277 156h-1c-2-1-4-2-5-3 1-1 3-2 5-2l1 5z" class="Y"></path><path d="M285 143h1 0v1c-1 4-5 3-6 7 0 2 0 3 1 4 1 2 2 2 4 3l1 1c-2 0-3 0-4 1l-4-1c0-1-1-2-1-3l-1-5h0c3-2 8-5 9-8z" class="h"></path><path d="M589 127l1 1 3-2h0l-1 2h-1v1l3-2v1l-2 2 1 1 4-3c-2 3-6 5-7 9v5h1c-1 5-2 9-1 13v1c1 3 2 6 4 8l-1 2c3 4 7 7 10 11l-1 1h-1-1-1-1c-1-1-1-1-1-2l-7-5v1c2 1 5 4 5 6h1c1 0 3 2 4 3-2-1-6-2-7-2-2 1-4 1-6 1h-2l-1-1c-2-3-4-4-8-4l-1-1c-2-4-4-7-4-12-1-9 2-19 8-26v-1c3-3 6-6 10-8z" class="a"></path><path d="M592 130l1 1-3 4-2-2c1-1 3-3 4-3z" class="Y"></path><path d="M576 154c1 2 1 3 2 4v1-1-2h1 0l1 8c1 4 5 8 7 11 1 1 1 0 1 1-3-2-5-4-7-7-3-5-5-9-5-15z" class="G"></path><path d="M580 148c1 3 0 5 1 7l1 1c0 1 1 2 1 3h1l2 2c1 3 3 6 5 8l6 6v1l-7-5c-1-1-1-1-2-1l-3-3v-1c-1-1-2-3-3-5l-1-1h0c-2-3-1-8-1-12z" class="c"></path><path d="M589 127l1 1 3-2h0l-1 2h-1v1h0c-2 2-4 3-5 4-6 7-8 14-7 23h-1v2 1-1c-1-1-1-2-2-4 0-6 0-12 3-18v-1c3-3 6-6 10-8z" class="Y"></path><defs><linearGradient id="K" x1="580.021" y1="162.19" x2="568.811" y2="156.935" xlink:href="#B"><stop offset="0" stop-color="#181817"></stop><stop offset="1" stop-color="#3b3a37"></stop></linearGradient></defs><path fill="url(#K)" d="M575 174c-2-4-4-7-4-12-1-9 2-19 8-26-3 6-3 12-3 18s2 10 5 15c2 3 4 5 7 7 1 2 3 3 5 3-2 1-4 1-6 1h-2l-1-1c-2-3-4-4-8-4l-1-1z"></path><path d="M575 174c1-1 1-1 1-2h2v-1c-1-1-2-2-2-3l1-1c0 1 1 1 2 1v1h1 1c2 3 4 5 7 7 1 2 3 3 5 3-2 1-4 1-6 1h-2l-1-1c-2-3-4-4-8-4l-1-1z" class="H"></path><path d="M593 131l4-3c-2 3-6 5-7 9v5h1c-1 5-2 9-1 13v1c1 3 2 6 4 8l-1 2c3 4 7 7 10 11l-1 1h-1-1-1-1c-1-1-1-1-1-2v-1l-6-6c-2-2-4-5-5-8l-2-2h-1c0-1-1-2-1-3l-1-1c-1-2 0-4-1-7 2-5 4-10 8-15l2 2 3-4z" class="c"></path><path d="M589 160c-1-7-2-11-1-17l-1-1c1-2 2-3 3-5v5h1c-1 5-2 9-1 13v1c1 3 2 6 4 8l-1 2-4-6z" class="T"></path><path d="M588 133l2 2c-4 6-8 13-7 21h0l1 3h-1c0-1-1-2-1-3l-1-1c-1-2 0-4-1-7 2-5 4-10 8-15z" class="N"></path><path d="M583 156c1 0 1-1 2-2 0-1 0-2 1-3h0 0c1 1 1 2 1 3 0 2 1 4 2 6l4 6c3 4 7 7 10 11l-1 1h-1-1-1-1c-1-1-1-1-1-2v-1l-6-6c-2-2-4-5-5-8l-2-2-1-3z" class="C"></path><path d="M583 156c1 0 1-1 2-2 0-1 0-2 1-3h0 0c1 1 1 2 1 3l-1-1c0 3 0 5 1 7v1l-1-1v1l-2-2-1-3z" class="a"></path><path d="M420 655c1-1 1-2 3-2 2 1 3 3 5 5h1c0 1 1 3 2 4h0c1-1 1-1 1-2l2 1c3 12 4 24 2 36l-1-3c-1 9-1 19-1 28l1 16c0 5 1 10 3 14v3l1 8h-1 0v5l1 4v1c0 2 0 4-1 6l-1-3c-1-3-1-7-2-10l-1-5-1-2c-2 1-4 3-6 5v-5h-1c1-4 2-7 2-10 1-7 1-15 1-23 0-14 0-29-2-43-1-6-2-11-4-15-1-3-4-7-4-9 0-1 1-3 1-4z" class="i"></path><path d="M427 759c1-2 3-4 4-7l2 7c-2 1-4 3-6 5v-5z" class="X"></path><path d="M433 742c1-1 1-3 1-5h0v5l1-4c0 5 1 10 3 14v3l1 8h-1 0c-1-7-5-14-5-21z" class="E"></path><defs><linearGradient id="L" x1="434.473" y1="691.151" x2="422.758" y2="690.878" xlink:href="#B"><stop offset="0" stop-color="#c7c6c5"></stop><stop offset="1" stop-color="#fcfcfb"></stop></linearGradient></defs><path fill="url(#L)" d="M420 655c1-1 1-2 3-2 2 1 3 3 5 5h1c0 1 1 3 2 4h0c1-1 1-1 1-2l2 1c3 12 4 24 2 36l-1-3c-1 9-1 19-1 28l1 16-1 4v-5h0c0 2 0 4-1 5-1-5-1-11-1-17v-29c0-7 0-14-2-21-1-4-2-7-4-10s-4-5-5-8h0l-1-2z"></path><path d="M432 725c2-1 1-5 1-7v-1-3-6h1c-1 3-1 10 0 13v1l1 16-1 4v-5h0c0 2 0 4-1 5-1-5-1-11-1-17z" class="B"></path><path d="M420 655c1-1 1-2 3-2 2 1 3 3 5 5h1c0 1 1 3 2 4h0c1-1 1-1 1-2l2 1c3 12 4 24 2 36l-1-3 1-4c1-8-2-21-8-28-1-1-4-5-7-5l-1-2z" class="e"></path><path d="M447 122c2 1 4 1 6 2s3 2 5 3c2 2 5 4 6 7l3 3c2 4 3 8 3 12v3h1 3v1l-3 5c-2 6-5 12-8 18 1 1 2 1 3 2h4l1 1h-1-1l-1 1h2v1h-13c0 1 0 1-1 1l-4 1-33-1h-4l1-1c2-1 3-3 3-5l1-4v-1l2-6 1-1v3c0-2 1-3 2-5-2-6-3-11-6-17v-2l4 4c0 3 2 6 2 10 1 4 2 8 3 13 0 2 1 4 3 5 1 1 3 2 4 2 2 0 2 0 3-1 3-2 6-4 9-7 6-3 12-11 13-17 2-8-1-15-5-22 0 0-1-1-1-2-1-1-6-5-7-6z" class="S"></path><path d="M447 122c2 1 4 1 6 2s3 2 5 3c2 2 5 4 6 7l3 3c0 2 1 4 1 5 2 7 0 14-3 20h-1c0-1 0-1 1-2l1-3c2-5 2-11 0-16-3-5-7-11-12-13-1-1-6-5-7-6zm8 8c4 2 7 7 8 11 2 4 1 12-1 16-3 7-9 12-16 16-2 1-4 3-7 3h0-1c3-2 6-4 9-7 6-3 12-11 13-17 2-8-1-15-5-22z" class="X"></path><path d="M419 143l4 4c0 3 2 6 2 10 1 4 2 8 3 13 0 2 1 4 3 5 1 1 3 2 4 2 2 0 2 0 3-1h1c-1 1-2 1-2 2 3 1 8-3 11-4 7-4 14-13 16-21 1-2 1-4 2-6 1 4-1 9-3 13v2l-1 1c0 2-1 3-2 4-3 3-6 6-10 8-3 2-10 6-14 4h-1c-1-1-2-1-3-1-5-4-6-10-7-16-2-6-3-11-6-17v-2z" class="G"></path><path d="M438 181c4 0 7 0 11-2l5-2c2-1 4-2 5-3h1c2-1 3-3 4-4s1-2 2-2v-1c1-2 2-3 3-5 0-1 1-1 1-2 0-2 0-2 1-2-2 6-5 12-8 18 1 1 2 1 3 2h4l1 1h-1-1l-1 1h2v1h-13c0 1 0 1-1 1l-4 1-33-1h-4l1-1c2-1 3-3 3-5l1-4v-1l2-6 1-1v3c0 1 1 3 1 5 2 3 4 7 8 8h3c1 0 2 0 3 1z" class="K"></path><path d="M424 172c2 3 4 7 8 8h3c1 0 2 0 3 1-3 0-5-1-8-1-1 0-2 0-3-1h-2c-1 0-1 0-2-1 0-2 0-4 1-6z" class="M"></path><path d="M365 113h5c1 1 2 0 4 1h1c2 0 4 1 6 2 1 0 2 0 2 1 3 0 5 1 8 2 2 1 4 2 6 4l2 1h0c6 4 13 10 18 16l2 3v2c3 6 4 11 6 17-1 2-2 3-2 5v-3l-1 1-2 6v1l-1 4c0 2-1 4-3 5l-1 1c-1-1-2-1-3-3h0c-2-1-2 0-4 1h-3l-2 1h-4l3-3-1-1 2-3c4-5 5-9 5-16 0-4-2-7-3-11 0 1 0 1-1 2-1-4-4-9-7-12h0c-10-14-29-19-44-21 1-1 1-2 2-2 2-1 6-1 8 0h2l4 2v-1l-1-1c-1 0-2-1-3-1z" class="E"></path><path d="M409 142c6 6 7 14 7 21 0 3-1 5-2 7v1h-1l-1 2v-1c-1 0 0-5 1-5 0-5 0-9-1-13v-1-3h0c-1-2-1-4-2-6-1-1-1-1-1-2z" class="X"></path><path d="M393 127c2 1 2 2 4 2v1c5 3 9 8 12 12h0c0 1 0 1 1 2 1 2 1 4 2 6h0v3l-3-5c0 3 0 5 1 8v3h-1-1v-1c0-4-2-7-3-11-1-5-2-8-5-12-1 0-1 0-1-1-2-2-4-3-5-5 0 0 0-1-1-2z" class="N"></path><path d="M400 135c4 3 8 9 9 13 0 3 0 5 1 8v3h-1-1v-1c0-4-2-7-3-11-1-5-2-8-5-12z" class="M"></path><path d="M353 116c1-1 1-2 2-2 2-1 6-1 8 0h2l4 2c8 2 16 5 23 10 0 1 1 1 1 1 1 1 1 2 1 2 1 2 3 3 5 5 0 1 0 1 1 1 3 4 4 7 5 12 0 1 0 1-1 2-1-4-4-9-7-12h0c-10-14-29-19-44-21z" class="X"></path><path d="M409 148l3 5v1c1 4 1 8 1 13-1 0-2 5-1 5v1l1-2c2 0 3-1 4 0h0c1 0 1 1 2 1h1l-1 4c0 2-1 4-3 5l-1 1c-1-1-2-1-3-3h0c-2-1-2 0-4 1h-3l-2 1h-4l3-3-1-1 2-3c4-5 5-9 5-16v1h1 1v-3c-1-3-1-5-1-8z" class="S"></path><path d="M412 154c1 4 1 8 1 13-1 0-2 5-1 5v1l1-2c2 0 3-1 4 0h0c1 0 1 1 2 1h1l-1 4v-2c-1-1-1 1-3 1v-1h0l-5 3c-1 0-3 0-4 1s-2 1-4 1v-1l6-6v-2c2-6 1-11 3-16z" class="D"></path><path d="M365 113h5c1 1 2 0 4 1h1c2 0 4 1 6 2 1 0 2 0 2 1 3 0 5 1 8 2 2 1 4 2 6 4l2 1h0c6 4 13 10 18 16l2 3v2c3 6 4 11 6 17-1 2-2 3-2 5v-3l-1 1-2 6c-2-1-2-13-3-16-1-4-3-8-5-11-8-11-16-18-28-23-5-3-10-5-15-6l-1-1c-1 0-2-1-3-1z" class="X"></path><path d="M417 144h1l1 1c3 6 4 11 6 17-1 2-2 3-2 5v-3l-1 1c0-8-2-14-5-21z" class="M"></path><path d="M399 124c6 4 13 10 18 16l2 3v2l-1-1h-1c-4-3-6-8-9-11s-6-6-9-8v-1z" class="V"></path><path d="M620 363h2l1 2h4c-4 4-8 8-8 13 0 6 3 12 4 18 1 2 1 4 1 7l-1 3c-3 8-6 16-8 24l-1 10c0 3-1 6 0 9l-1 7 3 12c3 10 10 18 18 25h-2v1l-4-3c-1 0-3-1-4 0l-4-3 1-2c-1-1-2-3-3-4-1-3-3-5-4-7-2-3-4-6-6-8l-3-8c-1-3-1-6-2-9-1-4 0-8 1-12-1-5 1-8 2-13l3-11c2-7 4-14 2-22l-4-11c0-2 1-2 1-3 0-3 1-6 2-9h1v2l2-2c0-1 3-3 4-4h0c1-1 2-1 3-2z" class="Q"></path><path d="M614 394c2 2 4 4 3 8v3l-3 3v-2c2-4 1-9 0-12z" class="D"></path><path d="M614 408l3-3-1 6s1 1 1 2c-2 4-4 9-6 13v1l-1 1c-1-4 2-13 3-17l1-3z" class="G"></path><defs><linearGradient id="M" x1="600.915" y1="452.038" x2="615.585" y2="460.962" xlink:href="#B"><stop offset="0" stop-color="#a2a29f"></stop><stop offset="1" stop-color="#c2bfbf"></stop></linearGradient></defs><path fill="url(#M)" d="M604 438l1-1v-2c0-1 0-3 1-4h0v3h1c0 5-1 13 0 17l1 3c1 7 4 14 7 20l-1 1c-2-3-4-6-6-8l-3-8c-1-3-1-6-2-9-1-4 0-8 1-12z"></path><path d="M604 438l1-1v-2c0-1 0-3 1-4h0v3c0 4 0 8-1 12s0 8 0 13c-1-3-1-6-2-9-1-4 0-8 1-12z" class="I"></path><path d="M620 363h2l1 2h4c-4 4-8 8-8 13 0 6 3 12 4 18v2l-1-1-1 1h0c0 5-1 10-4 15 0-1-1-2-1-2l1-6v-3c1-4-1-6-3-8l-3-10c-1-2-1-4-3-6 0-3 1-6 2-9h1v2l2-2c0-1 3-3 4-4h0c1-1 2-1 3-2z" class="L"></path><path d="M617 365h0l2 1c-1 1-1 2-2 3l1 1c-1 1-2 1-2 2-1 0-2 1-2 1-1 1-1 2-1 3h-1c0-2 1-4 2-6l-1-1c0-1 3-3 4-4z" class="U"></path><path d="M620 363h2l1 2h4c-4 4-8 8-8 13 0 6 3 12 4 18v2l-1-1-3-9v-3c-1-1-1-2-1-4l-2-9c0-1 1-1 2-2l-1-1c1-1 1-2 2-3l-2-1c1-1 2-1 3-2z" class="N"></path><path d="M620 363h2l1 2c-2 2-4 3-5 5l-1-1c1-1 1-2 2-3l-2-1c1-1 2-1 3-2z" class="Q"></path><path d="M611 384h1l4 5c-1-3-1-6-3-8 2-2 0-4 1-7 1 2 1 4 1 6 1 2 2 4 2 6l4 12c0 5-1 10-4 15 0-1-1-2-1-2l1-6v-3c1-4-1-6-3-8l-3-10z" class="B"></path><path d="M623 396c1 2 1 4 1 7l-1 3c-3 8-6 16-8 24l-1 10c0 3-1 6 0 9l-1 7 3 12c3 10 10 18 18 25h-2v1l-4-3c-1 0-3-1-4 0l-4-3 1-2c-1-1-2-3-3-4-1-3-3-5-4-7l1-1c-3-6-6-13-7-20 0-9 1-19 3-28 2-4 4-9 6-13 3-5 4-10 4-15h0l1-1 1 1v-2z" class="C"></path><path d="M623 396c1 2 1 4 1 7l-1 3-2-1c1-3 1-5 0-7l1-1 1 1v-2z" class="B"></path><path d="M615 474l5 8c2 3 6 6 8 9-1 0-3-1-4 0l-4-3 1-2c-1-1-2-3-3-4-1-3-3-5-4-7l1-1z" class="U"></path><path d="M612 462c0-3 0-6 1-9v3l3 12c3 10 10 18 18 25h-2c-9-8-17-19-20-31z" class="E"></path><path d="M621 405l2 1c-3 8-6 16-8 24l-1 10c0 3-1 6 0 9l-1 7v-3c-1 3-1 6-1 9-3-10-2-27 2-37 2-7 5-13 7-20z" class="L"></path><path d="M438 755c3 2 3 3 3 6 1 2 1 5 1 7v1c-1 4-1 7-2 11-1 10-4 21-9 30v1 2c-4 4-6 11-10 14-4 1-7 7-10 10h-1c1-3 3-4 5-6l-1-1c-3 3-5 7-8 10-2 2-6 5-6 8l-3 3-23 14v-1l-2-1c6-3 12-6 17-11l-1-3c1-2 1-2 0-4l4-3h1c2-1 3-2 4-4l2-2v-1c4-6 10-12 13-18 4-7 7-14 9-22 5-10 9-20 6-31 2-2 4-4 6-5l1 2 1 5c1 3 1 7 2 10l1 3c1-2 1-4 1-6v-1l-1-4v-5h0 1l-1-8z" class="B"></path><path d="M434 761l1 5-1 1v-1l-1 1v-1h-1c-2 1-1 6-1 8-1-2-1-8 0-11 1 0 2-1 3-2z" class="G"></path><path d="M429 780l1 3 1-1h0c0 5-4 10-5 15l-5 12-3 6-1-1c1-3 3-6 4-9 3-8 7-16 8-25z" class="b"></path><path d="M431 774c0-2-1-7 1-8h1c0 5 1 8 0 13v1l-1 3-3 10c0 2-2 3-3 4 1-5 5-10 5-15h0l-1 1-1-3 2-6z" class="R"></path><path d="M432 783l1-3v-1c0 2 0 4 1 6v3h1l-7 17c-1-1-1-3-1-4-1 3-3 7-5 10l-1-2 5-12c1-1 3-2 3-4l3-10z" class="I"></path><path d="M432 783l1-3v-1c0 2 0 4 1 6v3h1l-7 17c-1-1-1-3-1-4 1-4 4-7 5-11 0-2 1-4 0-7z" class="V"></path><path d="M417 814l1 1c-7 14-16 28-29 37l-1-3c1-2 1-2 0-4l4-3h1c2-1 3-2 4-4l2-2v-1l1 1c1-1 2-3 3-4 2-2 3-4 6-6l-6 8c6-4 11-13 14-20z" class="F"></path><path d="M421 809l1 2c-2 2-3 5-4 8-2 3-5 6-5 10l1 1c-3 3-5 7-8 10-2 2-6 5-6 8l-3 3-23 14v-1l-2-1c6-3 12-6 17-11 13-9 22-23 29-37l3-6z" class="Q"></path><path d="M413 829l1 1c-3 3-5 7-8 10-2 2-6 5-6 8l-3 3-23 14v-1c2-2 5-3 7-4 10-7 19-14 27-24 1-2 3-4 5-7z" class="C"></path><path d="M438 755c3 2 3 3 3 6 1 2 1 5 1 7v1c-1 4-1 7-2 11-1 10-4 21-9 30v1 2c-4 4-6 11-10 14-4 1-7 7-10 10h-1c1-3 3-4 5-6l-1-1-1-1c0-4 3-7 5-10 1-3 2-6 4-8 2-3 4-7 5-10 0 1 0 3 1 4l7-17h-1v-3c-1-2-1-4-1-6 1-5 0-8 0-13v1l1-1v1l1-1c1 3 1 7 2 10l1 3c1-2 1-4 1-6v-1l-1-4v-5h0 1l-1-8z" class="F"></path><path d="M438 755c3 2 3 3 3 6 0 2-1 10-2 11l-1-4v-5h0 1l-1-8z" class="b"></path><path d="M435 766c1 3 1 7 2 10 0 4-1 8-2 12h-1v-3c-1-2-1-4-1-6 1-5 0-8 0-13v1l1-1v1l1-1z" class="D"></path><path d="M426 809c1 0 1 0 2-2 2-4 3-9 6-13h1c-2 6-5 11-7 17l1 1c1-1 1-1 1-2l1 1v2c-4 4-6 11-10 14-4 1-7 7-10 10h-1c1-3 3-4 5-6l-1-1-1-1c0-4 3-7 5-10 1-3 2-6 4-8 2-3 4-7 5-10 0 1 0 3 1 4-1 1-2 3-2 4z" class="S"></path><path d="M427 801c0 1 0 3 1 4-1 1-2 3-2 4-3 4-4 7-6 11-1 2-3 4-4 6s0 2 0 3c0 0-1 1-1 2l-1-1-1-1c0-4 3-7 5-10 1-3 2-6 4-8 2-3 4-7 5-10z" class="J"></path><path d="M643 143h0c1-1 2-2 3-2l2 1v1c-2 2-3 4-4 7l1 1c1 0 2 0 3 1-1 1-2 3-1 6 0 2 2 3 3 5h1c1 0 2 1 2 1 8 5 19 5 28 5h9c-1 2-2 2-3 3h-1c-1 1-2 1-4 2h-1c-2 1-8 0-10 2h1l-1 1c-1 0-2 1-3 1-3 0-6 0-8 1-3 0-6-1-9 0l-16-1c-3 0-7-1-10-2h0 1v-1l-10-4v1l-2-1c-2-1-3-2-4-5 0-2-1-3 1-5l-2-2c2-1 6-2 7-3h2l-1-1-4-1h4 1l2-1h4v-1h2c1 0 1-1 2-2l5-3 5-2h0l-1 1 1 1 3-3 2-1z" class="g"></path><path d="M616 171l1-1c2 1 4 2 6 2l-8-5h1c2 0 6 3 9 4v1h0l3 3c2 0 4 1 6 1l1 2c-3 0-7-1-10-2h0 1v-1l-10-4z" class="V"></path><path d="M616 156l2 1v1h5 0l-4 1c-1 0-5 2-6 3-1 2-1 3-1 5 1 1 2 2 2 4-2-1-3-2-4-5 0-2-1-3 1-5l-2-2c2-1 6-2 7-3z" class="G"></path><path d="M616 156l2 1v1c-3 2-5 3-7 3l-2-2c2-1 6-2 7-3z" class="Q"></path><path d="M628 150l5-3 5-2h0l-1 1 1 1-4 4c-1-1-1-1-2 0-4 1-7 3-10 5-1 0-3 0-4 1l-2-1h2l-1-1-4-1h4 1l2-1h4v-1h2c1 0 1-1 2-2z" class="B"></path><path d="M643 143h0c1-1 2-2 3-2l2 1-6 5c-4 5-7 9-12 12l-10 4c-1 0-2 0-3-1 2-2 5-2 7-3 7-4 14-9 19-16z" class="d"></path><path d="M635 178l-1-2c-2 0-4-1-6-1l-3-3h0v-1l9 3h0 9 0c2-1 2-1 3 0 7 2 14 3 22 4-3 0-6 0-8 1-3 0-6-1-9 0l-16-1z" class="O"></path><path d="M635 178l-1-2c-2 0-4-1-6-1l-3-3h0v-1l9 3h0c1 2 1 2 2 2 3 0 5 1 7 1h7c1 0 1 1 2 1 2 1 6 0 8 1-3 0-6-1-9 0l-16-1z" class="S"></path><path d="M640 166h1c8 7 20 8 30 10h1l-1 1c-1 0-2 1-3 1-8-1-15-2-22-4-1-1-1-1-3 0l-5-1-1-2c0-1 0-1-1-2 2 0 3-2 4-3z" class="i"></path><path d="M642 147c-1 2-1 3-1 4l1 1h-1c-1 1-1 2-1 3s-1 3-1 4-1 2-1 4h-1c-1 3-4 4-6 5s-3 1-4 0h-2c-1 0-2 0-3-1-2 0-5-1-6-3l1-1h3l10-4c5-3 8-7 12-12z" class="H"></path><path d="M630 159h0c1 1 0 1 1 2l-3 1c-1 1-2 2-4 2h-2l-1 1c2 2 4 2 6 2l1 1 1-1c1 0 2-3 3-3h0c2-1 2-1 3-2h0c1-2 3-2 4-3 0 1-1 2-1 4h-1c-1 3-4 4-6 5s-3 1-4 0h-2c-1 0-2 0-3-1-2 0-5-1-6-3l1-1h3l10-4z" class="e"></path><defs><linearGradient id="N" x1="674.997" y1="165.42" x2="677.247" y2="173.452" xlink:href="#B"><stop offset="0" stop-color="#999b9f"></stop><stop offset="1" stop-color="#c0b9b4"></stop></linearGradient></defs><path fill="url(#N)" d="M648 142v1c-2 2-3 4-4 7l1 1c1 0 2 0 3 1-1 1-2 3-1 6 0 2 2 3 3 5h1c1 0 2 1 2 1 8 5 19 5 28 5h9c-1 2-2 2-3 3h-1c-1 1-2 1-4 2h-1c-2 1-8 0-10 2-10-2-22-3-30-10h-1c-2 1-3 1-4 2h-5c2-1 5-2 6-5h1c0-2 1-3 1-4s1-3 1-4 0-2 1-3h1l-1-1c0-1 0-2 1-4l6-5z"></path><path d="M645 151c1 0 2 0 3 1-1 1-2 3-1 6 0 2 2 3 3 5h0c-2-1-4-3-5-4v-2h-1l1 2c1 5 6 7 10 9h0c-4-1-7-2-11-5-1-2-2-4-2-6 0-3 1-4 3-6z" class="K"></path><path d="M641 166c-1-2-2-3-2-5h1 0c2 5 6 7 11 8v1c2 1 4 1 6 1 1 1 2 0 3 1h4c4-1 8 0 11 0 4 0 8-1 11 0-1 1-2 1-4 2h-1c-2 1-8 0-10 2-10-2-22-3-30-10z" class="D"></path><path d="M238 102c1-5 2-9 5-13 6-8 16-12 26-14 10-1 17 2 25 8 4 5 6 9 7 16v4h-1v-1c0-6-3-12-7-16-5-5-12-7-18-6-7 1-14 4-18 10-5 6-8 16-6 25 0 3 1 6 2 8h-1c-2-5-3-10-4-15h-1c0 3 0 5 1 8 0 2 1 4 1 6v1c1 5 7 12 12 15h0c1 0 1 1 2 1 1 1 2 1 2 1l2 1c5 1 9 2 14 1h0v-2l2-2h-1 3c1 1 1 1 1 3 1 1 0 2 0 2h0-1l-1-1h0c-2 3-5 6-8 7-2 1-4 1-5 2l-2-1-1 1-6-3c-1 0-3-1-4-1-2-1-4-2-6-2l2 2h-1c-2-1-3-2-5-4h-1c1 3 7 8 10 10 2 1 5 3 6 4h0c-2 0-3-1-4-2-6-2-10-7-14-11h-1l1 2v1c-2-1-3-4-4-5h-1c0-1-1-1-1-2l-2-6-1-1c-1 1-1 2-2 4v-11c1-1 0-1 0-2l-1-1c1-7 0-18 4-24h0v3h1z"></path><path d="M237 114c1 0 2 1 2 2 1 1 1 3 1 5 1 4 3 8 5 12h1l4 7-3-4c-2-2-3-2-5-3-3-7-5-12-5-19z" class="F"></path><path d="M234 124l-1-1c1-7 0-18 4-24h0v3h1l-1 12c0 7 2 12 5 19 1 2 3 4 5 6 1 2 4 4 5 6l2 2h-1c-2-1-3-2-5-4h-1c1 3 7 8 10 10 2 1 5 3 6 4h0c-2 0-3-1-4-2-6-2-10-7-14-11h-1l1 2v1c-2-1-3-4-4-5h-1c0-1-1-1-1-2l-2-6-1-1c-1 1-1 2-2 4v-11c1-1 0-1 0-2z" class="B"></path><path d="M234 126c3 3 4 7 6 10 1 2 2 7 4 8l1 2v1c-2-1-3-4-4-5h-1c0-1-1-1-1-2l-2-6-1-1c-1 1-1 2-2 4v-11z" class="c"></path><path d="M246 133c-1-3-4-8-3-10 0-1 1-1 1-2-1-3-1-6-1-9 0-2 1-7 2-8v3c-1 3 0 7 0 10h0v-8c1 2 2 5 2 8l2 6c1 5 7 12 12 15h0c1 0 1 1 2 1 1 1 2 1 2 1l2 1c5 1 9 2 14 1h0v-2l2-2h-1 3c1 1 1 1 1 3 1 1 0 2 0 2h0-1l-1-1h0c-2 3-5 6-8 7-2 1-4 1-5 2l-2-1-1 1-6-3c-1 0-3-1-4-1-2-1-4-2-6-2-1-2-4-4-5-6-2-2-4-4-5-6 2 1 3 1 5 3l3 4-4-7z" class="R"></path><path d="M283 138l2 1v2h-1c-1 1-2 1-2 2l-1-1v-2l2-2z" class="E"></path><path d="M247 117l2 6c1 5 7 12 12 15h0v1c3 2 5 3 9 4 3 1 5 1 8 1v1l-6 2h-1-2-2c-2-1-4 0-6-1-3-2-8-6-9-9s-4-7-5-10c-1-1-2-3-1-4v1l5 6h0l-3-7c-1-2-2-4-1-6z" class="P"></path><path d="M256 138h1c2 1 4 2 5 4v1 1c-3-2-5-3-6-6z" class="X"></path><path d="M698 510h6l1 1c-2 3-11 6-14 8-40 22-71 45-76 94-1 10-1 21 1 32l-8-3 3-1-2-1c-9-11-14-24-16-38-3-20 1-40 14-57 17-22 45-31 72-34l19-1zM328 883l32-7 45-12c38-10 73-24 99-54 3 3 6 7 10 11 10 9 22 17 35 23 14 7 29 12 44 16l74 19 19 4c2 1 5 1 7 1h1l1-1c3 2 7 3 10 5v2c-2 1-6 1-8 1h-23-91-189-58-17-9-1v-3c2-2 6-3 10-4h1c1 1 2 0 3 0h2c1-1 2-1 3-1zm-84-739h1c4 4 8 9 14 11 1 1 2 2 4 2l8 6 5 4c2 2 5 3 7 4h1 1c28 12 59 11 89 11h45l33 1 4-1c1 0 1 0 1-1h13c2 0 3-1 5 0h-3v1h18 0l7 1c2 0 4 0 6-1h2c7-4 14-9 20-14h1v1c-3 13-1 27-1 40 0 23-10 48-29 61-11 7-22 10-34 14-4 1-7 3-11 4-1 0-1 0-2 1h0c-2 1-4 1-5 2 1 1 1 1 2 1s1 0 2 1c-5 0-10-2-14-3h-3c-2-1-5-1-8-2-3 2-10 1-14 1-2 0-3 0-5 1-3 0-5 0-8 1l-1-1c-4 1-8 2-12 4v-1l2-2v-1l1-1c-2-1-3 0-4 1-3 0-4 1-7 2-1 0-6 3-7 4l-3 1-3 3c-2 0-6 2-8 1l-1 1v1h0 1 2l1 1c-2 1-4 1-6 2h1l6-1v1h0c-2 0-2 1-3 2h-4 0c-4 1-8 1-11 4h1c1-1 5-2 6-2l-1 1h1c1 1 1 0 2 0h7c4 0 7-1 11 1-4 0-8 0-12 1-1 0-3 0-4 1-2 0-3 0-4 1-7 2-12 5-17 9-1 1-2 1-3 2-1 3-4 3-6 5h1 0c2-1 5-2 7-3-2 3-6 4-8 5-2 0-2 0-3 1h1 1l-1 3-5-1h-3c-1 0-1 1-1 1l3 1 10 2-1 1h-6c0 1 6 2 8 2-1 1-2 1-3 1-2-1-2-1-4-1h0c-1-1-2 0-3 0-2-1-3-1-4-1v1l-1 1 5 1 1 2c-1 1-1 1-2 0-6-1-33-8-36-6h-1c-3 0-7-1-9-3h-9 2c-1-1-2-2-3-2-2 1-3 2-4 4v1l-2-2v-1c-1-1-2-3-3-5-1-1-3-2-3-4-1-2-2-3-2-5h0-4l-1-1v-1l-1-1c-1 0-1-1-2-1h-1c0-1 0-2-1-3-1 0-2 0-2-1l-4-4c0-1 0-1-1-3l1-1c-1-3-1-5-2-8l1-5-1-1v-1h0c0-2 0-1 1-2v-1l1-1v-2l1-1v-1l1-1c1-1 1-1 1-3-2 1-1 0-2 1v1l-1 1-1 2c-1 1 0 1-1 3v1l-1 2v2 1c0 1 0 3-1 5v-4h-1l1-1c-1-1-1-2 0-3v-6h0v-1l-1 1h-1v-4c1-1 1 0 1-2-1 1-2 1-3 2v-2l1-1c1-1 1-1 0-1 1-2 2-3 3-3l1-1c1-1 2-2 3-4h1v1h1 0c2-1 2-3 3-5l-1-1 1-1 1-1-1-1h0l1-3-1-1c0-1 2-4 3-5h0c2-1 3-3 3-5v-1l-3 3v1-1-1c1-1 1-2 2-3s2-2 2-5v-4h1c-1 1-1 2 0 3v2h1c0-2-2-11-2-14v-5l-1-13c-1-3 0-6 1-8l-1-1v-4h0c-1-2-1-3-1-4v-3c1 1 1 1 3 1v-10h1c0-2 0-6 1-7s1-1 1-2v-1h0c1-2 1-6 1-7s-1-2-1-2v-4h1v1l1 1v-1l1 2 3-3c0-2 0-4-1-6l-1-2c0-1 0-3-2-4l-1-2z"></path><path d="M268 316l2-2c0 1 2 3 2 4v2c-2-1-3-2-4-4z" class="C"></path><path d="M266 306c2 2 2 4 3 6l1 2-2 2c-2-3-2-6-2-10zm42 9c1 0 2-1 3 0 1 0 2 1 2 2l-1 1c-1 0-2 1-3 1s-1 0-2-1c0-2 0-2 1-3z" class="b"></path><path d="M266 306c1-2 1-4 2-5 1-3 3-6 6-7v2l2 1h-1c0 1-1 1-1 2-1 1-2 3-2 5l-1-1v-4c-1 1-1 1-2 3v6c-1 1 0 3 0 4-1-2-1-4-3-6z" class="R"></path><path d="M263 309h0c0 3 0 6 1 8v2c-1 0 0 0-1 1l1 1 1-1v1l1 1h-3c-2 1-3 2-5 1 0-5 3-9 5-14z" class="M"></path><path d="M359 239c3-1 7-3 11-3 2-1 3 0 5 0 2-1 5-1 7-2l1 1c-3 2-7 3-10 3-2-1-3 0-6 1-4 1-8 2-12 2l4-2z" class="K"></path><path d="M253 340c1-5 2-9 4-14h1c5-2 12-1 17 1-2 1-3 2-5 2h0l-11 3-1 1h0c2 0 4-1 5 0l-1 1h3-1l-7 2c-2 1-3 2-4 4z" class="W"></path><path d="M227 308v-1c1-1 1 0 1-1h1l3 3 1-1h1v1c1 2 1 2 3 3v-1-4h0c1 1 1 3 1 4l1 2v1c-2 1-3 1-5 1h-2s0 1 1 1c1 1 4 2 5 3s2 1 3 1c2 1 3 3 4 4 0 1 0 1 1 2s1 5 2 6v1c-1-1-3-2-3-4-1-2-2-3-2-5h0-4l-1-1v-1l-1-1c-1 0-1-1-2-1h-1c0-1 0-2-1-3-1 0-2 0-2-1l-4-4c0-1 0-1-1-3l1-1z" class="Z"></path><path d="M265 334h-3l1-1c-1-1-3 0-5 0h0l1-1 11-3c-1 1-2 2-3 2 3 0 6 1 8 0 7 0 12-2 19-1v1c3 0 7 1 10 3h1l6 2c-1 0-1 1-1 1v1h-1l-26-4c-6-1-12-1-18 0z" class="M"></path><path d="M305 334l6 2c-1 0-1 1-1 1v1l-7-2 2-2z" class="E"></path><path d="M303 336h0c-2 0-5-2-7-3s-3-1-5-2h1 2c3 0 7 1 10 3h1l-2 2z" class="L"></path><path d="M349 240l2 1c-3 1-6 4-9 5l-9 5c0 1 0 1 1 1l-1 1-17 11-3 1h0c-2 0-2 0-4 1-1 0 0 0-1 1l-1-1c6-6 15-9 21-15 1-1 1-1 1-2-2 1-4 2-6 4-7 4-12 9-19 14l-19 13h0l13-11c6-4 12-11 18-14l11-7c2-1 4-1 5-2h1v-1c5-2 10-3 16-5z" class="K"></path><path d="M349 240l2 1c-3 1-6 4-9 5l-1-2-9 5-1 1v-1h-1l4-2-1-1v-1c5-2 10-3 16-5z" class="Z"></path><path d="M355 241c4 0 8-1 12-2 3-1 4-2 6-1-5 2-11 3-16 6v1c-1 1-2 1-3 2-2 0-3 2-5 3l-10 5c-2 2-3 3-6 4-1 1-2 2-4 3s-4 3-6 4v1h-2l1-3c-2 1-3 1-4 1h0-1c-1 0-1 0-1-1l17-11 1-1c-1 0-1 0-1-1l9-5c3-1 6-4 9-5l-2-1c4-1 6-2 10-1l-4 2z" class="F"></path><path d="M322 264c3-1 6-4 9-5s5-3 8-4c-2 2-3 3-6 4-1 1-2 2-4 3s-4 3-6 4v1h-2l1-3z" class="S"></path><path d="M342 247l6-1h1 1c1-1 2-1 3-2h1l1 1c-2 1-4 2-6 2h0c-3 0-5 2-7 3s-4 2-6 2c-1 1-2 1-3 1l1-1c2-2 6-3 8-5z" class="C"></path><path d="M349 240c4-1 6-2 10-1l-4 2c-4 2-9 4-13 6-2 2-6 3-8 5-1 0-1 0-1-1l9-5c3-1 6-4 9-5l-2-1z" class="H"></path><path d="M333 253c1 0 2 0 3-1 2 0 4-1 6-2s4-3 7-3c-4 3-7 5-11 7-7 3-14 7-20 11h-1c-1 0-1 0-1-1l17-11z" class="R"></path><path d="M340 312h1c1-1 5-2 6-2l-1 1h1c1 1 1 0 2 0h7c4 0 7-1 11 1-4 0-8 0-12 1-1 0-3 0-4 1-2 0-3 0-4 1-7 2-12 5-17 9-1 1-2 1-3 2-1 3-4 3-6 5h1 0c2-1 5-2 7-3-2 3-6 4-8 5-2 0-2 0-3 1h1 1l-1 3-5-1h-3l-6-2h-1c-3-2-7-3-10-3v-1c2 1 7 1 9 0l1-1-1-1h1c12-1 18-4 27-10 3-3 6-5 9-6z" class="X"></path><path d="M327 326c-1 3-4 3-6 5h1 0c2-1 5-2 7-3-2 3-6 4-8 5-2 0-2 0-3 1h1 1l-1 3-5-1v-2h2l1-1h1c-3 0-5-1-7-1v-1l-4-2c8 1 13 0 20-3z" class="a"></path><path d="M304 329h3l4 2v1c2 0 4 1 7 1h-1l-1 1h-2v2h-3l-6-2h-1c-3-2-7-3-10-3v-1c2 1 7 1 9 0l1-1z" class="H"></path><path d="M304 334c3-2 8 0 13-1l-1 1h-2v2h-3l-6-2h-1z" class="J"></path><defs><linearGradient id="O" x1="291.681" y1="343.959" x2="267.819" y2="328.541" xlink:href="#B"><stop offset="0" stop-color="#aeaba9"></stop><stop offset="1" stop-color="#d3d3d1"></stop></linearGradient></defs><path fill="url(#O)" d="M265 334c6-1 12-1 18 0 2 0 26 4 26 4h1v-1l3 1 10 2-1 1h-6c0 1 6 2 8 2-1 1-2 1-3 1-2-1-2-1-4-1h0c-1-1-2 0-3 0-2-1-3-1-4-1v1l-1 1 5 1 1 2c-1 1-1 1-2 0-6-1-33-8-36-6h-1c-3 0-7-1-9-3h-9 2c-1-1-2-2-3-2l7-2h1z"></path><path d="M257 336l7-2-2 2c1 1 5 1 6 1h5c1-1 3-1 5-1l2 2c-4 0-10-1-13 0h-9 2c-1-1-2-2-3-2z" class="G"></path><path d="M289 340c3-1 4-2 7-1 1 0 3 0 5 1l4 1h1c1 0 1 0 2 1h2 0v1l-1 1c-7-1-13-2-20-4z" class="E"></path><path d="M280 338l4 1 5 1c7 2 13 3 20 4l5 1 1 2c-1 1-1 1-2 0-6-1-33-8-36-6h-1c-3 0-7-1-9-3 3-1 9 0 13 0z" class="N"></path><path d="M313 265h0l3-1c0 1 0 1 1 1h1 0c1 0 2 0 4-1l-1 3c-2 1-4 2-6 4-2 1-3 3-5 4l-2 3v-1 3c-2 2-4 3-5 5l-2 3-2 3c-2 4-3 6-3 10v1c-2-3-1-7-1-10l1-3c0-1-1-1-2-1-2 1-3 2-4 3l-4 2-3 3-1 1v-1-4l2-4v-1c3-4 6-7 10-10 2-2 5-4 7-6s4-4 6-5l1 1c1-1 0-1 1-1 2-1 2-1 4-1z" class="b"></path><path d="M302 281h0c1-1 3-3 4-3 1-1 1-1 2-1v3c-2 2-4 3-5 5l-1-1h0l1-2-1-1z" class="C"></path><path d="M290 291l-2-1 1-2c1-1 5-5 7-5l1 1c1-1 1-1 2-1v1c-1 1-3 3-5 4s-3 2-4 3z" class="O"></path><path d="M318 265h0c1 0 2 0 4-1l-1 3c-2 1-4 2-6 4-2 1-3 3-5 4 1-2 3-4 5-6-1 1-2 1-2 1-2 2-4 3-7 4l11-9h1z" class="C"></path><path d="M307 266l1 1c1-1 0-1 1-1 2-1 2-1 4-1l-17 12h-2c2-2 5-4 7-6s4-4 6-5z" class="h"></path><path d="M302 281l1 1-1 2h0l1 1-2 3-2 3c-2 4-3 6-3 10v1c-2-3-1-7-1-10l1-3h1c1-3 2-5 4-6l1-2z" class="O"></path><path d="M302 281l1 1-1 2h0l1 1-2 3c-1 0-1-2-1-2 1-2 1-2 1-3l1-2zm-8-4h2c-1 2-1 2-1 4l-6 6c-2 2-3 4-5 6l-2-1 2-4v-1c3-4 6-7 10-10z" class="M"></path><path d="M284 288l1-1c1-2 2-3 4-4h1c-1 1-1 2-1 4-2 2-3 4-5 6l-2-1 2-4z" class="H"></path><path d="M384 237l8-3c3-1 7-2 11-2 0 0 2-1 3-1 0 0 1 1 2 1l6-1h1c-3 2-6 3-9 4l-1 1c-1 1-2 1-4 1-6 2-11 3-16 6l-14 7h0c-1 0-3 1-3 1-2 0-1-1-3 0-4 0-7 2-10 3l-11 5c-2 1-5 3-8 3-2 0-4 1-5 3h0c-2 2-5 3-8 5h-3l3-3v-1c2-1 4-3 6-4s3-2 4-3c3-1 4-2 6-4l10-5c2-1 3-3 5-3 1-1 2-1 3-2v-1c5-3 11-4 16-6 3 0 7-1 10-3l-1-1c2 0 5-1 7 0l-5 2h0v1z" class="S"></path><defs><linearGradient id="P" x1="395.211" y1="230.799" x2="394.238" y2="239.137" xlink:href="#B"><stop offset="0" stop-color="#090808"></stop><stop offset="1" stop-color="#2f2d2c"></stop></linearGradient></defs><path fill="url(#P)" d="M384 237l8-3c3-1 7-2 11-2 0 0 2-1 3-1 0 0 1 1 2 1-8 3-18 4-24 8-1 1-3 3-5 3h-1 0c1-1 3-2 4-2l1-1v-1h-6l7-2z"></path><path d="M382 234c2 0 5-1 7 0l-5 2h0v1l-7 2h6v1l-1 1c-1 0-3 1-4 2-2 0-3 1-4 1l-1 1-7 2-1-1c-2 1-4 2-7 3 1-1 2-1 2-2h0c-4 0-7 2-11 3 2-1 3-3 5-3 1-1 2-1 3-2v-1c5-3 11-4 16-6 3 0 7-1 10-3l-1-1z" class="C"></path><path d="M365 246c0-1 9-4 10-5 2 0 5-1 7 0-1 0-3 1-4 2-2 0-3 1-4 1l-1 1-7 2-1-1z" class="b"></path><path d="M382 234c2 0 5-1 7 0l-5 2h0v1l-7 2-20 6v-1c5-3 11-4 16-6 3 0 7-1 10-3l-1-1z" class="H"></path><path d="M365 246l1 1-8 5c-1 0-3 1-3 1-6 3-13 5-19 9-2 0-4 1-5 3h0c-2 2-5 3-8 5h-3l3-3v-1c2-1 4-3 6-4s3-2 4-3c3-1 4-2 6-4l10-5c4-1 7-3 11-3h0c0 1-1 1-2 2 3-1 5-2 7-3z" class="V"></path><path d="M323 266c4 0 5-1 7-3s5-3 7-4c6-3 11-6 17-7l1 1c-6 3-13 5-19 9-2 0-4 1-5 3h0c-2 2-5 3-8 5h-3l3-3v-1z" class="U"></path><path d="M284 287v1l-2 4v4 1l1-1 3-3 4-2c1-1 2-2 4-3 1 0 2 0 2 1l-1 3c0 3-1 7 1 10 2 0 3 2 5 3h0l-2 1c1 1 3 1 4 2h0l-2 1c-3 2-5 4-7 6-5 3-9 8-15 8-1-1-2-1-3-1-2 0-3-1-4-2v-2c0-1-2-3-2-4l-1-2c0-1-1-3 0-4v-6c1-2 1-2 2-3v4l1 1c0-2 1-4 2-5 0-1 1-1 1-2h1c2-5 4-7 8-10z" class="B"></path><path d="M292 305l-1 1c-1 0-1 0-2-1 1-1 1-1 1-2l-1-1-1 1-1-1v-1c1-1 2-3 3-4 0-2 0-3 1-4 1 1 1 1 1 2-1 2-2 3-2 5s0 3 1 4l1 1h0z" class="X"></path><path d="M283 296l1 1v1h-1l1 1c2-2 3-4 5-5h0l1 1c-2 2-5 5-4 8 0 1 1 2 1 3l3 1v1c-2 1-2 1-4 1l-1-1s-1-1-2-1l-3-6c2-2 2-2 2-4h0l1-1z" class="Y"></path><path d="M292 305l1-1c1 1 2 1 3 1h0c-1-2-1-3-2-5 0-3 0-5 1-8 0 3-1 7 1 10 2 0 3 2 5 3h0l-2 1c1 1 3 1 4 2h0l-2 1c-3 2-5 4-7 6-5 3-9 8-15 8-1-1-2-1-3-1-2 0-3-1-4-2v-2c0-1-2-3-2-4l-1-2c0-1-1-3 0-4v-6c1-2 1-2 2-3v4l1 1c-1 1-1 3-1 5l2 2 1 1c2 2 4 4 7 4h0l-2-2h1 0c2 1 4 1 6 2-1-2-3-3-4-4l1-1c1 1 2 1 3 2v-2h0c2 0 4 1 6 0v-1h1c1-1 1-2 2-2 0-1-1-2-2-3h-1 0z" class="V"></path><path d="M272 318c1 1 2 1 4 1h0v-1c1 0 2 1 3 1l2-2c2 1 4 0 6 0-1 2-4 3-6 4-2 0-3 0-5 1-2 0-3-1-4-2v-2z" class="K"></path><path d="M287 317l1-1v-1h2c2-2 5-2 7-4 1-2 2-1 4-2-3 2-5 4-7 6-5 3-9 8-15 8-1-1-2-1-3-1 2-1 3-1 5-1 2-1 5-2 6-4zm-48-76c1 2 1 4 0 6 0 1 1 3 0 5 0 1-4 5-4 6s0 1 1 2c0-2 0-4 2-5h1v-1c1 0 1 0 1 2 0 1-1 2-1 3 0 2 0 5-1 7 0 2 0 4-1 5 0 2-3 3-2 5l1-2c1 0 1 0 2 1 1 2-1 5 2 7h1v-15h0v22c0 5 1 11 1 16v2c0 2 0 2-1 4h0v-3-13h0c-1 2-1 5-1 7s0 4-1 5l-1-1-1-1v2h0v4 1c-2-1-2-1-3-3v-1h-1l-1 1-3-3h-1c0 1 0 0-1 1v1c-1-3-1-5-2-8l1-5-1-1v-1h0c0-2 0-1 1-2v-1l1-1v-2l1-1v-1l1-1c1-1 1-1 1-3-2 1-1 0-2 1v1l-1 1-1 2c-1 1 0 1-1 3v1l-1 2v2 1c0 1 0 3-1 5v-4h-1l1-1c-1-1-1-2 0-3v-6h0v-1l-1 1h-1v-4c1-1 1 0 1-2-1 1-2 1-3 2v-2l1-1c1-1 1-1 0-1 1-2 2-3 3-3l1-1c1-1 2-2 3-4h1v1h1 0c2-1 2-3 3-5l-1-1 1-1 1-1-1-1h0l1-3-1-1c0-1 2-4 3-5h0c2-1 3-3 3-5v-1l-3 3v1-1-1c1-1 1-2 2-3s2-2 2-5z" class="H"></path><path d="M236 276h1l1 4v4c1 0 1 1 1 2-1 3 2 8 0 10h0v-1-3l-1 1-1 1c0 1 0 2-1 3v-1c0-2-1-2-2-4l1-4 1-1c0-1-1-2-1-3h0c-1-2-2-2-4-2h0c0-2 3-5 5-6z" class="h"></path><path d="M234 292l1-4 1 2 1-2 1 1c0 2 0 3-1 5 0 1 0 2-1 3v-1c0-2-1-2-2-4z" class="M"></path><path d="M231 282h0c0-2 3-5 5-6v1l-1 1 1 1h1v1l-1 1v1l1 1h1l-2 4c0-1-1-2-1-3h0c-1-2-2-2-4-2z" class="K"></path><path d="M232 264l1-1-1-1h0l1-3-1-1c0-1 2-4 3-5l1 2-2 4s1 0 1 1l-1 2c1 0 1 0 2-1h1c-1 2-1 2 0 3v2c0 3-1 4-2 7l-1 1c-2 4-5 6-7 9-2 2-4 6-4 9v3c-1-1-1-2 0-3v-6h0v-1l-1 1h-1v-4c1-1 1 0 1-2-1 1-2 1-3 2v-2l1-1c1-1 1-1 0-1 1-2 2-3 3-3l1-1c1-1 2-2 3-4h1v1h1 0c2-1 2-3 3-5l-1-1 1-1z" class="F"></path><path d="M228 274c-1 2-2 4-3 7h-1l-1-1c0-1 0 0-1-1 0-1 0-2 1-3v-1l1-1c2 1 2 1 4 0z" class="K"></path><path d="M232 264c1 0 1 1 2 2v1c1 1-1 3-2 4-1 2-2 3-4 3-2 1-2 1-4 0 1-1 2-2 3-4h1v1h1 0c2-1 2-3 3-5l-1-1 1-1z" class="S"></path><path d="M231 282c2 0 3 0 4 2h0c0 1 1 2 1 3l-1 1-1 4c1 2 2 2 2 4v1c1-1 1-2 1-3l1-1 1-1v3 1h0c1 3 1 7 0 10h-1l-1-1v2h0v4 1c-2-1-2-1-3-3v-1h-1l-1 1-3-3h-1c0 1 0 0-1 1v1c-1-3-1-5-2-8l1-5h0c0-3 1-5 3-8 0-2 1-3 2-5z" class="C"></path><path d="M237 294l1-1 1-1v3 1h0c1 3 1 7 0 10h-1l-1-1v2h0v4 1c-2-1-2-1-3-3l1-1v-1-2-2c0-2 1-4 1-6 1-1 1-2 1-3z" class="K"></path><path d="M231 282c2 0 3 0 4 2h0c0 1 1 2 1 3l-1 1-1 4v1c-1 3-2 9 0 13l-3-3v-2c0-1 0-1-1-1 0-1 1-3 1-3 0-3 0-5 1-7h0c0-1 1-2 1-3l-1-1c-1 0-2 1-3 1 0-2 1-3 2-5z" class="J"></path><path d="M229 287c1 0 2-1 3-1l1 1c0 1-1 2-1 3h0c-1 2-1 4-1 7 0 0-1 2-1 3v3h-1 0c0-1-1-2-1-2l-3-1 1-5h0c0-3 1-5 3-8z" class="a"></path><path d="M228 301c0-3 0-5 1-8h1l-1-2 1-1h2c-1 2-1 4-1 7 0 0-1 2-1 3v3h-1 0c0-1-1-2-1-2z" class="W"></path><path d="M244 144h1c4 4 8 9 14 11 1 1 2 2 4 2l8 6 5 4c2 2 5 3 7 4h1 1c28 12 59 11 89 11h45l33 1 4-1c1 0 1 0 1-1h13c2 0 3-1 5 0h-3v1h18 0l7 1c-2 2-4 4-7 5l-36 1h-52c-12 0-25 0-37 1-28 2-56 13-77 33-22 23-32 53-36 83l-1 16v9c-2-9-3-18-4-28v-3l-1-16-5-39v-3c0-2-2-11-2-14v-5l-1-13c-1-3 0-6 1-8l-1-1v-4h0c-1-2-1-3-1-4v-3c1 1 1 1 3 1v-10h1c0-2 0-6 1-7s1-1 1-2v-1h0c1-2 1-6 1-7s-1-2-1-2v-4h1v1l1 1v-1l1 2 3-3c0-2 0-4-1-6l-1-2c0-1 0-3-2-4l-1-2z" class="X"></path><path d="M271 163l5 4c-3 0-5-1-7-2l2-2z" class="P"></path><defs><linearGradient id="Q" x1="492.102" y1="186.858" x2="486.898" y2="183.142" xlink:href="#B"><stop offset="0" stop-color="#585756"></stop><stop offset="1" stop-color="#6e6d6c"></stop></linearGradient></defs><path fill="url(#Q)" d="M470 181c2 0 3-1 5 0h-3v1h18 0l7 1c-2 2-4 4-7 5l-36 1 3-2h1c4 0 12 0 14-3v-1h-7-13 0l4-1c1 0 1 0 1-1h13z"></path><path d="M470 181c2 0 3-1 5 0h-3v1h18 0-34c1 0 1 0 1-1h13z" class="H"></path><defs><linearGradient id="R" x1="248.678" y1="259.216" x2="242.904" y2="259.662" xlink:href="#B"><stop offset="0" stop-color="#949390"></stop><stop offset="1" stop-color="#bbb9b9"></stop></linearGradient></defs><path fill="url(#R)" d="M239 223h0 1c1-1 1-2 2-4h0c1 2 2 3 1 5 0 1 1 2 1 3l1 1 1 6 2 2 1-3c1 3 1 3 0 6l2-1-1-1h1v-1l1-2-1 12-1 12-1 22-1 14c0 1 1 4 0 5 0 1 0 1-1 2 0 0 1 1 0 2v-3l-1-16-5-39v-3c0-2-2-11-2-14v-5z"></path><path d="M248 245v3c1 0 2-1 3-2l-1 12c-1-2-1-4-1-6h-1v1c-1-3 0-5 0-8z" class="R"></path><path d="M249 233c1 3 1 3 0 6l2-1-1-1h1v-1l1-2-1 12c-1 1-2 2-3 2v-3-9l1-3z" class="C"></path><path d="M246 284h1v-4-1-4 8h1l1-3-1 14c0 1 1 4 0 5 0 1 0 1-1 2 0 0 1 1 0 2v-3l-1-16z" class="L"></path><path d="M239 223h0 1c1-1 1-2 2-4h0c1 2 2 3 1 5-1 1-1 1-1 3v1c0 2 1 7 0 9 0 1 0 1-1 3 0 1 1 3 0 5v-3c0-2-2-11-2-14v-5z" class="G"></path><path d="M243 171c1-2 1-6 1-7s-1-2-1-2v-4h1v1l1 1v-1l1 2c6 21 8 41 7 62 0 4 0 8-1 11l-1 2v1h-1l1 1-2 1c1-3 1-3 0-6l-1 3-2-2-1-6-1-1c0-1-1-2-1-3 1-2 0-3-1-5h0c-1 2-1 3-2 4h-1 0l-1-13c-1-3 0-6 1-8l-1-1v-4h0c-1-2-1-3-1-4v-3c1 1 1 1 3 1v-10h1c0-2 0-6 1-7s1-1 1-2v-1h0z" class="H"></path><path d="M241 191l1 5c0 1 1 1 2 2v1 2c1 1 1 3 1 5 2 5 2 14 1 19-1-5-1-11-2-16 0-3 0-6-1-9-1-1-1-1-1-2-1-3-1-5-1-7z" class="K"></path><path d="M240 181h1v10c0 2 0 4 1 7 0 1 0 1 1 2 1 3 1 6 1 9 1 5 1 11 2 16 0 1 0 2 1 4 1-2 1-3 1-5h1v6c-1 1 0 2 0 3l-1 3-2-2-1-6-1-1c0-1-1-2-1-3 1-2 0-3-1-5h0c-1 2-1 3-2 4h-1 0l-1-13c-1-3 0-6 1-8l-1-1v-4h0c-1-2-1-3-1-4v-3c1 1 1 1 3 1v-10z" class="R"></path><path d="M237 190c1 1 1 1 3 1 1 2 1 5-1 8h0c-1 0-1-1-1-2h0c-1-2-1-3-1-4v-3z" class="Q"></path><defs><linearGradient id="S" x1="245.005" y1="206.672" x2="233.995" y2="217.328" xlink:href="#B"><stop offset="0" stop-color="#a9a5a1"></stop><stop offset="1" stop-color="#c9c8c9"></stop></linearGradient></defs><path fill="url(#S)" d="M239 202c1-1 1-3 2-4 1 1 0 3 0 5v6c1 2 2 6 2 8l-1 2h0c-1 2-1 3-2 4h-1 0l-1-13c-1-3 0-6 1-8z"></path><path d="M252 157c3-1 8 5 9 6 4 3 8 6 12 8 19 12 40 16 61 19-3 2-9 3-13 4-13 5-25 13-35 22-14 13-23 29-31 46 2-13 5-26 5-40 1-12 0-24-1-37-1-5-2-11-4-16-1-4-3-8-3-12z"></path><path d="M269 228h1c-2 5-6 10-9 15 1-4 2-7 3-10 1-2 1-3 2-4h0 1l2-1z" class="c"></path><path d="M270 185c0-1 0-3-1-4v-1c-1-1-1-3 0-4h0c1 2 1 4 2 5h5v1c-2 1-3 2-3 3 0 3 2 7 4 10h1l-1 1c-3-1-6-8-7-11z" class="P"></path><path d="M263 187v1c1 0 1 1 1 1 0 2 1 2 1 4h1v1 1c2 2 4 4 7 5 1 1 2 1 3 1h3c2 0 4-1 6-2l1 1c-3 3-8 3-12 2-4 0-7-3-9-6s-3-6-2-9z" class="E"></path><path d="M265 180c1 1 2 3 2 5l1 1c0 3 0 4 1 6s1 3 3 4 3 3 4 5c-1 0-2 0-3-1-3-1-5-3-7-5v-1-1h-1c0-2-1-2-1-4 0 0 0-1-1-1v-1c1-2 2-5 2-7z" class="g"></path><path d="M284 195h-1c-2-1-4-1-5-3-1-1 0-3 0-4l1-1h1c1 2 1 3 2 5 4 2 11 0 16-1 0 1 0 1-1 2s-1 2-1 4h1c6-3 12-4 19-5-3 2-18 7-21 6l-2-4c-2 2-4 5-7 6l-1-1c1 0 2-1 3-3 1 0 1 0 1-1h-2-3z" class="Q"></path><path d="M265 180c1-2 2-4 4-6l1 1-1 1h0c-1 1-1 3 0 4v1c1 1 1 3 1 4 1 3 4 10 7 11l1-1c1 0 1 0 2 1h3l1-1h3 2c0 1 0 1-1 1-1 2-2 3-3 3-2 1-4 2-6 2h-3c-1-2-2-4-4-5s-2-2-3-4-1-3-1-6l-1-1c0-2-1-4-2-5z" class="C"></path><path d="M265 180c1-2 2-4 4-6l1 1-1 1h0c-1 1-1 3 0 4v1c1 1 1 3 1 4h0c0 3 1 6 2 8-2-2-3-4-4-7l-1-1c0-2-1-4-2-5z" class="U"></path><path d="M525 168h1v1c-3 13-1 27-1 40 0 23-10 48-29 61-11 7-22 10-34 14-4 1-7 3-11 4-1 0-1 0-2 1h0c-2 1-4 1-5 2 1 1 1 1 2 1s1 0 2 1c-5 0-10-2-14-3h-3c-2-1-5-1-8-2-3 2-10 1-14 1-2 0-3 0-5 1-3 0-5 0-8 1l-1-1c-4 1-8 2-12 4v-1l2-2v-1l1-1c-2-1-3 0-4 1-3 0-4 1-7 2-1 0-6 3-7 4l-3 1-3 3c-2 0-6 2-8 1h1c2-1 4-3 5-5-3 2-6 4-10 5-3 1-6 2-9 2l9-7v-1c-4 3-7 5-11 7-2-1-2-1-3-2l-1 1h-1-1c-3 0-5 0-8 1-1 0-2 0-3 1l-9 3c-3 0-6 1-8 0-2 0-3 0-4-1h0c-2-1-3-3-5-3v-1c0-4 1-6 3-10l2-3 2-3c1-2 3-3 5-5v-3 1l2-3c2-1 3-3 5-4 2-2 4-3 6-4h2l-3 3h3c3-2 6-3 8-5h0c1-2 3-3 5-3 3 0 6-2 8-3l11-5c3-1 6-3 10-3 2-1 1 0 3 0 0 0 2-1 3-1h0l14-7c5-3 10-4 16-6 2 0 3 0 4-1l8-2c4-1 7-3 10-5l27-14 26-16c1-1 3-2 5-3l14-8h-5c3-1 5-3 7-5 2 0 4 0 6-1h2c7-4 14-9 20-14z" class="Y"></path><path d="M497 183c2 0 4 0 6-1h2c-3 3-7 5-10 6h-5c3-1 5-3 7-5z" class="F"></path><path d="M455 215c2 0 5-3 7-4-1 1-2 2-2 3v1c-1 1-3 2-5 3-3 2-6 5-10 7h0-2c-1-1-1-1-2 0-2 1-2 2-5 3v-1l19-12z" class="E"></path><path d="M408 247c4-1 7-1 10-1-1 0-4 1-5 2v1h-1-1c-5 1-11 3-17 5l-3-1c3-2 7-2 10-5h0 3l2-1h2z" class="U"></path><defs><linearGradient id="T" x1="463.535" y1="210.696" x2="468.555" y2="202.151" xlink:href="#B"><stop offset="0" stop-color="#aca9a9"></stop><stop offset="1" stop-color="#d0cfcd"></stop></linearGradient></defs><path fill="url(#T)" d="M450 215l26-16c-2 2-3 3-5 4l1 1h0 1l1-1 1 1-13 7c-2 1-5 4-7 4-1 0-2 0-3 1l-2-1z"></path><path d="M436 227v1c3-1 3-2 5-3 1-1 1-1 2 0h2c-4 2-7 4-11 6l-9 3-6 3h-3v-1c-2 0-4 1-6 2h-3l7-3c1 0 2-1 3 0 3 0 8-4 10-5l9-3z" class="D"></path><path d="M496 263c4-3 7-6 10-10 6-7 10-16 13-24h0c0 4-2 8-4 12-6 9-11 17-19 24-5 3-10 6-15 8-2 1-5 3-7 3h0c-1 0-2-1-3-1l3-1c1 0 2-1 2-1l6-3h1c2 0 4-2 7-3 2-1 4-3 6-4z" class="L"></path><path d="M521 217c0 2 0 3-1 5h1v-1l1 1-3 7h0c-3 8-7 17-13 24-3 4-6 7-10 10 0-3 5-6 6-8 0-1 0-2 1-2v-1c3-3 6-7 8-10h0c2-3 4-6 5-9v-4l3-6 2-6z" class="N"></path><path d="M450 215l2 1c1-1 2-1 3-1l-19 12-9 3c-2 1-7 5-10 5-1-1-2 0-3 0l-1-1c4-1 7-3 10-5l27-14z" class="W"></path><path d="M447 229v-1c1-1 2-1 3-2h2c1-1 3-2 4-2-1 1-3 2-5 4-1 1-2 2-2 3-2 2-3 3-5 4l-1 1 1 1h-1l-1 1-1-1c-1 0-1 0-2 1s-4 1-5 2l-8 3h-1l-5 2c-1 1-1 1-2 1-3 0-6 0-10 1v-1l1-1c7-1 13-4 20-6 6-3 12-5 18-10h0z" class="d"></path><path d="M499 255c3-3 6-6 8-9 1-1 2-4 4-4-2 3-5 7-8 10v1c-1 0-1 1-1 2-1 2-6 5-6 8-2 1-4 3-6 4-3 1-5 3-7 3h-1l-6 3s-1 1-2 1c0-2 0-2 1-4h0c-2 1-3 1-4 1h-1l14-8c1 1 0 0 2 1 1-1 3-2 4-3s2-1 4-2c1-1 3-3 5-4z" class="Q"></path><path d="M485 269c-2-1-3-1-4-1h0l10-5c-1 2-2 3-4 5l-2 1z" class="D"></path><path d="M491 263c4-3 8-8 12-11v1c-1 0-1 1-1 2-1 2-6 5-6 8-2 1-4 3-6 4-3 1-5 3-7 3h-1l3-1 2-1c2-2 3-3 4-5z" class="B"></path><path d="M447 229h0c-6 5-12 7-18 10-7 2-13 5-20 6l-1 1v1h-2l-2 1h-3 0c-9 3-17 8-25 12-2 1-4 1-6 2-1 0-2 1-3 1l2-2c7-4 14-10 22-12l26-8c10-3 20-7 30-12z" class="D"></path><path d="M521 205c0 4 1 8 0 12l-2 6-3 6v4c-1 3-3 6-5 9h0c-2 0-3 3-4 4-2 3-5 6-8 9-2 1-4 3-5 4-2 1-3 1-4 2s-3 2-4 3c-2-1-1 0-2-1 2-2 7-4 8-7-4 2-7 5-11 7h0l2-2c5-4 11-8 16-12 2-1 6-6 7-8 2-3 6-6 8-10l-1-1c0-1 1-2 1-3 0 0 1-1 1-2l3-7v-2h1c2-4 1-6 2-10v-1z" class="T"></path><path d="M492 256h2c1-1 3-3 3-4l4-4 1 1h-1c-1 1-4 4-3 5 0 1 0 0 1 1-2 1-4 3-5 4-2 1-3 1-4 2s-3 2-4 3c-2-1-1 0-2-1 2-2 7-4 8-7z" class="E"></path><path d="M401 248c-3 3-7 3-10 5l3 1-12 7h-1c-3 3-6 5-10 7-5 3-11 5-16 8v-2h0c-2 1-4 3-6 3l2-2v-1h0c1-1 3-3 5-3 2-1 3-1 4-2h1c-2 0-4-1-6 0-1 1-2 1-3 0h-1 0c2-1 4-2 6-2 4-1 7-3 10-4 1 0 2-1 3-1 2-1 4-1 6-2 8-4 16-9 25-12z" class="I"></path><path d="M391 253l3 1-12 7h-1c-3 0-5 3-7 3l-1-1 18-10zm130-69c1 3 1 7 1 10l-1 11v1c-1 4 0 6-2 10h-1v2l-3 7c0 1-1 2-1 2 0 1-1 2-1 3l1 1c-2 4-6 7-8 10-1 2-5 7-7 8l-1-1c1-2 4-4 6-6l2-3c1-2 1-2 1-3l-1-1v-4h0l5-8-1-1-1 1h-1l1-3v-1l-4 7h0v1c-1 2-3 4-4 5 0-2 1-3 2-5 1-1 0-4 1-6l1-2c1-4 3-8 5-12l5-13 2-1c0 2-2 5-1 7 1-3 2-8 4-10 1-1 1-5 1-6z" class="E"></path><path d="M518 209l1 1c-1 3-1 5-2 7l-1-1-1-1v-1c2-1 2-2 3-5z" class="B"></path><path d="M518 209v-1c1-3 1-7 2-10 1 1 1 4 0 6 0 2 0 4-1 5v1l-1-1z" class="G"></path><path d="M509 219c0-3 3-7 5-10 1-2 2-5 3-8 1 3-1 10-2 13v1l-4 8h0l-1-1-1 1h-1l1-3v-1z" class="D"></path><path d="M515 215l1 1 1 1c-1 5-3 9-6 13-1 2-3 3-4 6l-1-1v-4h0l5-8h0l4-8z" class="N"></path><path d="M515 194l2-1c0 2-2 5-1 7-2 3-3 5-4 8 0 2-1 3-1 4l-3 5c0 1-1 1-1 2s-1 2-1 3c-1 1-2 2-1 4h0 0v1c-1 2-3 4-4 5 0-2 1-3 2-5 1-1 0-4 1-6l1-2c1-4 3-8 5-12l5-13z" class="D"></path><path d="M509 219v1l-1 3h1l1-1 1 1-5 8h0v4l1 1c0 1 0 1-1 3l-2 3c-2 2-5 4-6 6l1 1c-5 4-11 8-16 12l-2 2h0c-1 1-1 1-2 1l-2 2c-4 2-7 0-11 2l-1-1c1-1 3-2 5-3l10-6c0-1 0-2 1-3 0-1 2-1 2-1l3-3-1-1c-2 0-3 2-4 2v-1l1-1 1-1 3-3h1c0-2 2-3 3-4 2-3 4-5 5-8 2-2 4-6 6-9l3-4c-1 2 0 5-1 6-1 2-2 3-2 5 1-1 3-3 4-5v-1h0l4-7z" class="U"></path><path d="M503 240c1-1 2-1 3-1l-2 3c-2 2-5 4-6 6l-2 2-1-1c1-2 3-2 4-4 0-3 2-4 4-5z" class="B"></path><path d="M506 231h0v4l1 1c0 1 0 1-1 3-1 0-2 0-3 1 0-1-1-1-1-1l-1-1 5-7z" class="T"></path><path d="M486 251l2-1c1 0 2-2 2-3h1l3-3c0 4-2 5-5 8-3 2-6 5-9 6 0-1 0-2 1-3 0-1 2-1 2-1l3-3z" class="K"></path><path d="M490 242c3 0 4-2 6-3l4-5v1c0 1-1 1-1 2-1 1-1 2-1 4-2 1-3 2-4 3l-3 3h-1c0 1-1 3-2 3l-2 1-1-1c-2 0-3 2-4 2v-1l1-1 1-1 3-3h1c0-2 2-3 3-4z" class="e"></path><path d="M498 248l1 1c-5 4-11 8-16 12l-2 2h0c-1 1-1 1-2 1l-2 2c-4 2-7 0-11 2l-1-1c1-1 3-2 5-3l1 1c7-1 19-10 25-15l2-2z" class="R"></path><path d="M518 180l2-1h1v5c0 1 0 5-1 6-2 2-3 7-4 10-1-2 1-5 1-7l-2 1-5 13c-2 4-4 8-5 12l-1 2-3 4c-2 3-4 7-6 9v-4l-1-1-1 1h-1c1-2 3-4 4-6l-3-1c2-2 3-4 4-5l-1-1h0l-1-1s0-1 1-2v-3c1-2 2-3 3-5 0-2 0-4 1-5 2-2 3-4 4-6h0c1 0 2-2 3-2 2-2 4-5 5-7v-1h0c2-2 4-4 6-5z" class="U"></path><path d="M518 180c0 1 0 1-1 2s-1 2-2 3c0 2-2 3-3 4 0 0 0 1-1 2-2 3-5 7-7 10l-5 5c0-2 0-4 1-5 2-2 3-4 4-6h0c1 0 2-2 3-2 2-2 4-5 5-7v-1h0c2-2 4-4 6-5z" class="R"></path><path d="M504 201c-1 2-3 4-4 7-1 1 0 1 0 2h1c1-2 3-4 4-6 0 2-2 4-3 6 0 0 1 0 1 1 0 0-1 1-1 2 0 0 1 1 0 2 0 1 0 2-1 3h1c1-1 2-2 3-4h1c3-7 5-14 8-20h1l-5 13c-2 4-4 8-5 12l-1 2-3 4c-2 3-4 7-6 9v-4l-1-1-1 1h-1c1-2 3-4 4-6l-3-1c2-2 3-4 4-5l-1-1h0l-1-1s0-1 1-2v-3c1-2 2-3 3-5l5-5z" class="V"></path><path d="M496 217v-1c1-2 3-3 5-5-1 3-2 4-3 7 0 1 1 0 1 2-1 2-2 2-1 4-1 2-4 4-4 5l-1 1h-1c1-2 3-4 4-6l-3-1c2-2 3-4 4-5l-1-1z" class="S"></path><path d="M498 224l3-3v4c-2 3-4 7-6 9v-4l-1-1c0-1 3-3 4-5z" class="M"></path><path d="M413 234l1 1-7 3h3c2-1 4-2 6-2v1h3l-1 1-3 3h2l-26 8c-8 2-15 8-22 12-4 1-8 3-11 4v-2h-3c-1 1-2 2-4 2-1 1-2 2-4 2v-2c2-2 5-3 8-5 1-1 2-2 3-4h0c1-1 3-2 5-3h0c1 0 1-1 2-2 2-1 1 0 3 0 0 0 2-1 3-1h0l14-7c5-3 10-4 16-6 2 0 3 0 4-1l8-2z" class="B"></path><path d="M416 237h3l-1 1c-2 0-2 0-4 1-3 3-9 5-13 5h-4l19-7z" class="E"></path><path d="M413 234l1 1-7 3h3c2-1 4-2 6-2v1l-19 7c-1 0-3 1-4 1-1-1-1-1-2-1v-1h-6c5-3 10-4 16-6 2 0 3 0 4-1l8-2z" class="W"></path><path d="M413 234l1 1-7 3c-4 0-9 4-13 4l-1-1 6-2c1 0 2-1 2-2 2 0 3 0 4-1l8-2zm-28 9h6v1c1 0 1 0 2 1-7 2-13 5-19 9 0 0-5 3-5 2-2 4-7 6-11 7h-3c-1 1-2 2-4 2-1 1-2 2-4 2v-2c2-2 5-3 8-5 1-1 2-2 3-4h0c1-1 3-2 5-3h0c1 0 1-1 2-2 2-1 1 0 3 0 0 0 2-1 3-1h0l14-7z" class="V"></path><path d="M355 263c3-2 6-4 9-5l5-2c-2 4-7 6-11 7h-3z" class="D"></path><path d="M385 243h6v1c-4 1-7 2-10 4-1 1-6 4-7 4h-1l1-1v-1c-1 0-2 1-3 1v-1h0l14-7z" class="Q"></path><path d="M365 251c2-1 1 0 3 0 0 0 2-1 3-1v1c-2 1-4 2-6 4l-12 9c-1 0-1 0-2 1h0c-1 1-2 2-4 2v-2c2-2 5-3 8-5 1-1 2-2 3-4h0c1-1 3-2 5-3h0c1 0 1-1 2-2z" class="D"></path><defs><linearGradient id="U" x1="341.774" y1="255.602" x2="356.213" y2="259.943" xlink:href="#B"><stop offset="0" stop-color="#838581"></stop><stop offset="1" stop-color="#a3a0a0"></stop></linearGradient></defs><path fill="url(#U)" d="M336 262c3 0 6-2 8-3l11-5c3-1 6-3 10-3-1 1-1 2-2 2h0c-2 1-4 2-5 3h0c-1 2-2 3-3 4-3 2-6 3-8 5v2c2 0 3-1 4-2 2 0 3-1 4-2h3v2c3-1 7-3 11-4l-2 2c-3 1-6 3-10 4-2 0-4 1-6 2-1 0-2 0-4 1s-5 1-7 2c-1 2-2 2-3 3l-2 1h-1-1-1v-1c-1 1-1 1-2 1h-1-1l-1 1-2-1c-3 1-4 3-6 5v-1l-3-1-7 9h-1v-3h-1l-3 3-3 3h-2l2-3 2-3c1-2 3-3 5-5v-3 1l2-3c2-1 3-3 5-4 2-2 4-3 6-4h2l-3 3h3c3-2 6-3 8-5h0c1-2 3-3 5-3z"></path><path d="M358 256c-1 2-2 3-3 4-3 2-6 3-8 5l-4 2c-3 1-5 1-7 2h0l-1-1c2-1 4-3 5-4 6-3 13-5 18-8z" class="T"></path><path d="M323 273c6-3 11-7 17-9-1 1-3 3-5 4l1 1h0c-1 2-5 3-6 4h-1c1-1 1-2 2-2v-1c-2 1-3 2-4 3h0c-1 2-1 2-2 3-3 1-4 3-6 5v-1l-3-1c2-2 4-4 7-6z" class="Q"></path><path d="M358 263v2c3-1 7-3 11-4l-2 2c-3 1-6 3-10 4-2 0-4 1-6 2-1 0-2 0-4 1s-5 1-7 2c-1 2-2 2-3 3l-2 1h-1-1-1v-1c-1 1-1 1-2 1h-1-1l-1 1-2-1c1-1 1-1 2-3h0c1-1 2-2 4-3v1c-1 0-1 1-2 2h1c1-1 5-2 6-4 2-1 4-1 7-2l4-2v2c2 0 3-1 4-2 2 0 3-1 4-2h3z" class="W"></path><path d="M358 263v2c3-1 7-3 11-4l-2 2c-3 1-6 3-10 4-2 0-4 1-6 2-1 0-2 0-4 1s-5 1-7 2c-1 1-2 1-4 1 3-2 8-5 11-6 2 0 3-1 4-2 2 0 3-1 4-2h3z" class="E"></path><path d="M331 265l8-3c-5 4-11 7-17 10l1 1c-3 2-5 4-7 6l-7 9h-1v-3h-1l-3 3-3 3h-2l2-3 2-3c1-2 3-3 5-5v-3 1l2-3c2-1 3-3 5-4 2-2 4-3 6-4h2l-3 3h3c3-2 6-3 8-5z" class="D"></path><path d="M321 267h2l-3 3c-4 2-9 7-12 10v-3 1l2-3c2-1 3-3 5-4 2-2 4-3 6-4z" class="R"></path><path d="M304 288l-1-1c1-2 3-3 5-4 2-3 5-6 8-7l6-4 1 1c-3 2-5 4-7 6l-7 9h-1v-3h-1l-3 3z" class="S"></path><path d="M351 269h1c1 1 2 1 3 0 2-1 4 0 6 0h-1c-1 1-2 1-4 2-2 0-4 2-5 3h0v1l-2 2-3 2-2 2c-1 0-1 1-2 1-1 1-3 2-4 2-1 2-2 3-4 4v1h0c-1 2-2 2-3 2-2 1-2 1-3 3l1 1 1 1 2-2h1c-1 1-2 2-2 4h0 5l-1 2v1h-1-1c-3 0-5 0-8 1-1 0-2 0-3 1l-9 3c-3 0-6 1-8 0-2 0-3 0-4-1h0c-2-1-3-3-5-3v-1c0-4 1-6 3-10h2l3-3 3-3h1v3h1l7-9 3 1v1c2-2 3-4 6-5l2 1 1-1h1 1c1 0 1 0 2-1v1h1 1 1l2-1c1-1 2-1 3-3 2-1 5-1 7-2s3-1 4-1h0z" class="Y"></path><path d="M323 289c1 2 0 3 0 5l-2 2v-1c-1-2 1-4 2-6zm-5 7h-1l-2-2 1-2h4c-1 1-1 3-2 4z" class="f"></path><path d="M332 285v3c-2 3-3 5-6 6l-1-1c0-1 0-2 2-4 2 0 4-3 5-4z" class="d"></path><path d="M320 292c0-1 1-2 2-3h1c-1 2-3 4-2 6v1h3c-1 1-1 1-2 1-1 1-1 1-1 2h-3 0c-2-1-2-1-3-3l2 1 1-1c1-1 1-3 2-4z" class="P"></path><path d="M301 291c0 1 0 2-1 3 0 2-2 2-2 4 2 1 2 1 3 4 0 2 2 3 4 4-2 0-3 0-4-1h0c-2-1-3-3-5-3v-1c0-4 1-6 3-10h2z" class="d"></path><path d="M301 302c1-1 1-2 2-2h1v-4 1h0c1 2 3 4 4 5l1 1 2 1h1l1-1h3l-3 1v1 1c-3 0-6 1-8 0s-4-2-4-4z" class="P"></path><path d="M327 277l1-1h1 1c1 0 1 0 2-1v1h1 1 1c-2 1-3 3-5 3l-5 3c-3 2-5 5-8 7-1-3 0-4 1-7l1-1c2-2 3-4 6-5l2 1z" class="M"></path><path d="M325 276l2 1c-3 2-5 4-9 5l1-1c2-2 3-4 6-5z" class="F"></path><path d="M330 296l2-2h1c-1 1-2 2-2 4h0 5l-1 2v1h-1-1c-3 0-5 0-8 1-1 0-2 0-3 1l-9 3v-1-1l3-1c1-1 1-1 2-1 1-1 1-1 2-1-1-1-1-2-2-2h3c0-1 0-1 1-2 1 0 1 0 2-1h2c1 0 1 0 2 1 1 0 1 0 2-1z" class="d"></path><path d="M351 269h1c1 1 2 1 3 0 2-1 4 0 6 0h-1c-1 1-2 1-4 2-2 0-4 2-5 3h0v1l-2 2-3 2-2 2c-1 0-1 1-2 1-1 1-3 2-4 2h0 0c-1 0-1 1-2 1l-1 2-1-1-2 2v-3c-1 1-3 4-5 4 1-3 2-5 4-8h-1c-2 1-3 1-5 1l5-3c2 0 3-2 5-3l2-1c1-1 2-1 3-3 2-1 5-1 7-2s3-1 4-1h0z" class="M"></path><path d="M340 272c2-1 5-1 7-2s3-1 4-1h0c-7 4-14 7-20 12h0-1c-2 1-3 1-5 1l5-3c2 0 3-2 5-3l2-1c1-1 2-1 3-3z" class="Q"></path><path d="M351 274v1l-2 2-3 2-2 2c-1 0-1 1-2 1-1 1-3 2-4 2h0 0c-1 0-1 1-2 1l-1 2-1-1-2 2v-3c5-5 12-9 19-11z" class="P"></path><path d="M411 255c7 0 13-1 20-3 4-1 9-3 13-4-3 6-8 9-14 11-5 1-9 1-13 1l-10 1c-1 1-2 1-3 2l1 1-10 3-9 3-5 4-2 2-7 3c-1 2-3 2-5 3s-4 2-7 2l2 2c-1 1-3 2-4 3-2 0-3 2-4 4-2 0-3 1-4 2-4 3-7 5-11 7-2-1-2-1-3-2l-1 1v-1l1-2h-5 0c0-2 1-3 2-4h-1l-2 2-1-1-1-1c1-2 1-2 3-3 1 0 2 0 3-2h0v-1c2-1 3-2 4-4 1 0 3-1 4-2 1 0 1-1 2-1l2-2 3-2c2 0 4-2 6-3h0v2c5-3 11-5 16-8 4-2 7-4 10-7h1c-1 2-2 3-3 5 1 0 2-1 3-2 1 0 1-1 2-1 6-2 11-6 18-7l1-1h3c1-1 2-1 4-1l1 1z" class="L"></path><path d="M396 264c4-1 7-2 11-3-1 1-2 1-3 2l1 1-10 3-1-1h-1 0c1 0 1 0 2-1l1-1z" class="F"></path><path d="M396 264l-1 1c-1 1-1 1-2 1h0 1l1 1-9 3c-2 1-5 2-7 3h-2 0c2-2 4-4 8-4 3-2 7-4 11-5z" class="b"></path><path d="M377 273h0 2c2-1 5-2 7-3l-5 4-2 2-7 3c-1 2-3 2-5 3h-2c1-2 2-3 3-4l3-2 6-3z" class="D"></path><path d="M368 278h1c1 0 3-1 4-2v1l-1 1v1c-1 2-3 2-5 3h-2c1-2 2-3 3-4z" class="G"></path><path d="M357 281c2-2 4-3 6-4 5-3 10-7 16-8l-5 3-3 3v1l-3 2c-1 1-2 2-3 4h2c-2 1-4 2-7 2-3 2-7 5-10 6v-1l6-4c2 0 4-2 5-3v-1h-4zm25-20c-1 2-2 3-3 5 1 0 2-1 3-2 1 0 1-1 2-1 6-2 11-6 18-7l1-1h3c1-1 2-1 4-1l1 1c-1 1-2 1-3 1h-1c-1 0-1 0-2 1h-2-1c-1 0-1 0-2 1-2 1-5 1-7 3h0l1 1c-2 0-6 2-7 2h-2c-1 0-3 1-3 1-4 2-8 5-12 7-3 2-7 3-10 5-3 1-4 3-7 4h-1c-1 0-2 1-2 1l-1-1 2-2v-1c-2 1-3 1-5 1l3-2c2 0 4-2 6-3h0v2c5-3 11-5 16-8 4-2 7-4 10-7h1z" class="B"></path><defs><linearGradient id="V" x1="420.907" y1="259.256" x2="419.793" y2="252.492" xlink:href="#B"><stop offset="0" stop-color="#adabab"></stop><stop offset="1" stop-color="#e3e2e0"></stop></linearGradient></defs><path fill="url(#V)" d="M411 255c7 0 13-1 20-3 4-1 9-3 13-4-3 6-8 9-14 11-5 1-9 1-13 1 1 0 2-1 2-1h-3l11-2c1 0 1 0 2-1-4 0-8 1-12 2-4 0-9 0-13 1s-7 1-10 3l-1-1h0c2-2 5-2 7-3 1-1 1-1 2-1h1 2c1-1 1-1 2-1h1c1 0 2 0 3-1z"></path><path d="M346 279c2 0 3 0 5-1v1l-2 2 1 1s1-1 2-1h1l-5 5c1 0 2-1 3-1 2-2 4-3 6-4h4v1c-1 1-3 3-5 3l-6 4v1c3-1 7-4 10-6l2 2c-1 1-3 2-4 3-2 0-3 2-4 4-2 0-3 1-4 2-4 3-7 5-11 7-2-1-2-1-3-2l-1 1v-1l1-2h-5 0c0-2 1-3 2-4h-1l-2 2-1-1-1-1c1-2 1-2 3-3 1 0 2 0 3-2h0v-1c2-1 3-2 4-4 1 0 3-1 4-2 1 0 1-1 2-1l2-2z" class="N"></path><path d="M336 298h0c1-2 5-5 6-6 3-1 5-3 7-5 2-1 3-1 4-2h3l-6 4-1 1v1l1 1c-1 0-2 1-2 1-2 1-4 2-5 3-2 1-3 2-5 2l-2 2-1 1v-1l1-2z" class="X"></path><path d="M334 289c3-2 5-4 8-5l3-2c-2 2-4 3-6 4-2 2-3 4-5 6 2-1 3-2 5-3 1-1 2-3 4-3 0-1 1-1 1-1l2-1h3l-3 3-7 6c-1 0-1 0-2 1-1 0-2 1-3 2-1 0-2 1-3 2h0c0-2 1-3 2-4h-1l-2 2-1-1-1-1c1-2 1-2 3-3 1 0 2 0 3-2h0z" class="f"></path><path d="M350 289v1c3-1 7-4 10-6l2 2c-1 1-3 2-4 3-2 0-3 2-4 4-2 0-3 1-4 2-4 3-7 5-11 7-2-1-2-1-3-2l2-2c2 0 3-1 5-2 1-1 3-2 5-3 0 0 1-1 2-1l-1-1v-1l1-1z" class="Y"></path><path d="M512 185v1c-1 2-3 5-5 7-1 0-2 2-3 2h0c-1 2-2 4-4 6-1 1-1 3-1 5-1 2-2 3-3 5v3c-1 1-1 2-1 2l1 1h0l1 1c-1 1-2 3-4 5l3 1c-1 2-3 4-4 6h1l1-1 1 1v4c-1 3-3 5-5 8-1 1-3 2-3 4h-1l-3 3c-1 1 1-1-1 1l-1 1v1l-16 10c-1 1-3 2-4 3h0c-2 2-4 3-6 4h-1l1-2v-1h-3 0l3-3-1-2c-2 1-2 4-5 3-1 1-1 2-1 3v1l3 1v1l1 1h1 0c-2 1-3 1-4 3v1h0-1-2c-1-2-3-4-5-6-10-8-23-6-36-5l-1-1c1-1 2-1 3-2l10-1c4 0 8 0 13-1 6-2 11-5 14-11-4 1-9 3-13 4-7 2-13 3-20 3l-1-1c8-1 18-2 25-6 20-8 33-27 48-41 9-9 19-15 29-22z"></path><path d="M466 243v1c2-1 2-2 4-3h1c-1 2-2 2-3 3l2 2c-2 1-3 2-4 4-1 1-3 3-3 4 0 3-3 6-4 8v1l2-1c0 2-1 1 0 3h0c-2 2-4 3-6 4h-1l1-2v-1h-3 0l3-3-1-2c-2 1-2 4-5 3l4-5c-1-1-1 0-2-1 2-3 5-6 7-8l8-7z" class="M"></path><path d="M458 250l1 1 5-4v1c0 1 0-1 0 1 0 1-1 1-1 2-1 1-1 2-2 3-2 2-4 3-6 6h2l-2 3-1-2c-2 1-2 4-5 3l4-5c-1-1-1 0-2-1 2-3 5-6 7-8z" class="Z"></path><path d="M466 243v-2-1c3-4 6-7 9-10 4-5 8-11 12-16v7h3c2-2 1-4 1-7 0-1 2-2 2-2v-3c1-3 5-5 6-7 0-1 0-2 1-2 1-1 1-2 2-3h0v-1c0-1 1-1 2-1h0c-1 2-2 4-4 6-1 1-1 3-1 5-1 2-2 3-3 5v3c-1 1-1 2-1 2l1 1h0l1 1c-1 1-2 3-4 5l3 1c-1 2-3 4-4 6h1l1-1 1 1v4c-1 3-3 5-5 8-1 1-3 2-3 4h-1l-3 3c-1 1 1-1-1 1l-1 1v1l-16 10c-1 1-3 2-4 3-1-2 0-1 0-3l-2 1v-1c1-2 4-5 4-8 0-1 2-3 3-4 1-2 2-3 4-4l-2-2c1-1 2-1 3-3h-1c-2 1-2 2-4 3v-1z" class="F"></path><path d="M466 243h0c1-1 2-1 2-2l1-1c1-3 6-7 8-10l4-4h3c-3 4-5 8-7 12-3 2-5 6-7 8l-2-2c1-1 2-1 3-3h-1c-2 1-2 2-4 3v-1z" class="S"></path><path d="M496 217l1 1c-1 1-2 3-4 5l3 1c-1 2-3 4-4 6h1c-1 2-3 4-5 6v1l-2-1c0 1 0 1-2 1-1 0-1 0-2 1l-2-1-2 1h-1c2-4 4-8 7-12 1-2 3-4 4-5l2 1h1c2-1 3-3 5-5h0z" class="H"></path><path d="M486 236c2-2 3-3 4-5h0c0-2 1-2 0-3v-1c2-1 2-3 3-4l3 1c-1 2-3 4-4 6h1c-1 2-3 4-5 6v1l-2-1z" class="F"></path><path d="M484 226c1-2 3-4 4-5l2 1c-3 5-6 10-10 15l-2 1h-1c2-4 4-8 7-12z" class="D"></path><path d="M494 229l1 1v4c-1 3-3 5-5 8-1 1-3 2-3 4h-1l-3 3c-1 1 1-1-1 1l-1 1v1l-16 10c-1 1-3 2-4 3-1-2 0-1 0-3l-2 1v-1c1-2 4-5 4-8 0-1 2-3 3-4 1-2 2-3 4-4 2-2 4-6 7-8h1l2-1 2 1c1-1 1-1 2-1 2 0 2 0 2-1l2 1v-1c2-2 4-4 5-6l1-1z" class="W"></path><path d="M474 245l1 1h1 3l-4 4s-1 0-2 1-2 2-4 2c0-1 2-2 2-3 1-3 1-3 3-5z" class="G"></path><path d="M480 239c2 1 3 2 4 3l-5 4h-3-1l-1-1 6-6z" class="T"></path><path d="M469 253c2 0 3-1 4-2s2-1 2-1h3c0 1-1 1-1 2 1 0 2-1 4-1v1l-16 10v-1c0-2 4-3 5-5-3 1-5 2-8 4l6-6 1-1z" class="L"></path><path d="M494 229l1 1v4c-1 3-3 5-5 8-1 1-3 2-3 4h-1l-3 3c-1 1 1-1-1 1l-1 1c-2 0-3 1-4 1 0-1 1-1 1-2h-3l4-4 5-4c-1-1-2-2-4-3l2-1c1-1 1-1 2-1 2 0 2 0 2-1l2 1v-1c2-2 4-4 5-6l1-1z" class="H"></path><path d="M486 236l2 1c-2 1-3 3-4 5-1-1-2-2-4-3l2-1c1-1 1-1 2-1 2 0 2 0 2-1z" class="D"></path><path d="M481 252c1 0 2-2 4-2l1 1-3 3s-2 0-2 1c-1 1-1 2-1 3l-10 6c-2 1-4 2-5 3l1 1c4-2 7 0 11-2l2-2c1 0 1 0 2-1 4-2 7-5 11-7-1 3-6 5-8 7l-14 8h1c1 0 2 0 4-1h0c-1 2-1 2-1 4l-3 1c1 0 2 1 3 1h0l-8 3c-1 1-2 2-4 2-1 1-2 1-3 2h-1c-2 1-4 2-5 2h-1l-2 1c-2 0-3 1-5 2 1 1 2 1 4 1-2 1-4 1-5 2 1 1 1 1 2 1s1 0 2 1c-5 0-10-2-14-3h-3c-2-1-5-1-8-2-3 2-10 1-14 1-2 0-3 0-5 1-3 0-5 0-8 1l-1-1c-4 1-8 2-12 4v-1l2-2v-1l1-1c-2-1-3 0-4 1-3 0-4 1-7 2-1 0-6 3-7 4l-3 1-3 3c-2 0-6 2-8 1h1c2-1 4-3 5-5-3 2-6 4-10 5-3 1-6 2-9 2l9-7v-1c1-1 2-2 4-2 1-2 2-4 4-4 1-1 3-2 4-3l-2-2c3 0 5-1 7-2s4-1 5-3l7-3 2-2 5-4 9-3 10-3c13-1 26-3 36 5 2 2 4 4 5 6h2 1 0v-1c1-2 2-2 4-3h0-1l-1-1v-1l-3-1v-1c0-1 0-2 1-3 3 1 3-2 5-3l1 2-3 3h0 3v1l-1 2h1c2-1 4-2 6-4h0c1-1 3-2 4-3l16-10z" class="H"></path><path d="M438 277h1c0-1 0-1-1-2 2-1 4 0 6 1 1 1 3 1 4 2 0 1 1 2 2 2s2 0 3 1c-2 1-3 1-5 1h-2l-2-1c-1-1-5-3-6-4z" class="O"></path><defs><linearGradient id="W" x1="452.963" y1="271.75" x2="469.768" y2="274.141" xlink:href="#B"><stop offset="0" stop-color="#1f1d1b"></stop><stop offset="1" stop-color="#3a3839"></stop></linearGradient></defs><path fill="url(#W)" d="M492 256c-1 3-6 5-8 7l-14 8h1c1 0 2 0 4-1h0c-1 2-1 2-1 4l-3 1c-4 1-8 3-12 4h-3l-3 2c-1-1-2-1-3-1s-2-1-2-2c1-1 1-2 3-3 1-1 1 0 2-1 2 0 11-3 12-5h-2c-1 1-2 1-4 2l-1-1c1-1 5-2 7-3l1 1c4-2 7 0 11-2l2-2c1 0 1 0 2-1 4-2 7-5 11-7z"></path><path d="M451 275h1c1 1 3 1 4 1h1v1h0c0 1 0 2-1 2l-3 2c-1-1-2-1-3-1s-2-1-2-2c1-1 1-2 3-3z" class="K"></path><path d="M470 271h1c1 0 2 0 4-1h0c-1 2-1 2-1 4l-3 1c-4 1-8 3-12 4 1-2 4-3 6-4h1l2-2h-2c1-2 3-2 4-2z" class="C"></path><path d="M459 279c4-1 8-3 12-4 1 0 2 1 3 1h0l-8 3c-4 1-8 2-11 4-2 0-4 1-5 2-2 0-5 0-6 1v1h-1v2c-1 0-2-1-3-1h0v-1c-1-1-3-1-5-2h4c-6-4-13-8-20-9h-11c3-1 6 0 9 0 2-1 4-1 7-1s5 1 8 1c2 0 4 1 6 1 1 1 5 3 6 4l2 1h2c2 0 3 0 5-1l3-2h3z" class="I"></path><path d="M408 276c3-1 6 0 9 0 2-1 4-1 7-1s5 1 8 1c2 0 4 1 6 1 1 1 5 3 6 4h-2c1 1 3 2 4 3-2 1-2 0-3-1-3-1-8-5-10-4 2 3 7 4 10 7v1c-2-1-3-2-4-2h0c-6-4-13-8-20-9h-11z" class="C"></path><path d="M408 276h11c7 1 14 5 20 9h-4c2 1 4 1 5 2v1h0c1 0 2 1 3 1v-2h1v-1c1-1 4-1 6-1 1-1 3-2 5-2 3-2 7-3 11-4-1 1-2 2-4 2-1 1-2 1-3 2h-1c-2 1-4 2-5 2h-1l-2 1c-2 0-3 1-5 2 1 1 2 1 4 1-2 1-4 1-5 2 1 1 1 1 2 1s1 0 2 1c-5 0-10-2-14-3h-3c-2-1-5-1-8-2-3 2-10 1-14 1-2 0-3 0-5 1-3 0-5 0-8 1l-1-1c-4 1-8 2-12 4v-1l2-2v-1l1-1c-2-1-3 0-4 1-3 0-4 1-7 2l4-3 1-1-1-1c1-1 3-1 5-2v-1l4-1-1-1c-2 0-6 3-9 4l-1-1c1-1 2-2 3-2 2-1 4-1 5-2l1-1c1 0 2 0 3-1h0c6-2 13-3 19-3z" class="G"></path><path d="M395 290c9-3 19-4 28-2-3 2-10 1-14 1-2 0-3 0-5 1-3 0-5 0-8 1l-1-1z" class="P"></path><defs><linearGradient id="X" x1="421.098" y1="282.599" x2="420.412" y2="285.854" xlink:href="#B"><stop offset="0" stop-color="#33312d"></stop><stop offset="1" stop-color="#666564"></stop></linearGradient></defs><path fill="url(#X)" d="M407 281c10 0 19 2 28 4 2 1 4 1 5 2v1c-11-2-23-4-34-4-3 0-6 0-9 1-1 0-1 1-1 1l-1-1c1 0 2-1 3-1l9-3z"></path><path d="M408 276h11c7 1 14 5 20 9h-4c-9-2-18-4-28-4l-9 3c-3-1-10 3-12 1l-6 3-1-1c1-1 3-1 5-2v-1l4-1-1-1c-2 0-6 3-9 4l-1-1c1-1 2-2 3-2 2-1 4-1 5-2l1-1c1 0 2 0 3-1h0c6-2 13-3 19-3z" class="N"></path><path d="M386 285l7-3c4-1 10-3 14-1l-9 3c-3-1-10 3-12 1z" class="U"></path><path d="M405 264c13-1 26-3 36 5 2 2 4 4 5 6h0c-2 0-4-1-6-2-2 0-5-1-7-1-6-1-11-1-16 0-10 0-19 2-28 6v1h0c-1 1-2 1-3 1l-1 1c-1 1-3 1-5 2-1 0-2 1-3 2l1 1c3-1 7-4 9-4l1 1-4 1v1c-2 1-4 1-5 2l1 1-1 1-4 3c-1 0-6 3-7 4l-3 1-3 3c-2 0-6 2-8 1h1c2-1 4-3 5-5-3 2-6 4-10 5-3 1-6 2-9 2l9-7v-1c1-1 2-2 4-2 1-2 2-4 4-4 1-1 3-2 4-3l-2-2c3 0 5-1 7-2s4-1 5-3l7-3 2-2 5-4 9-3 10-3z" class="B"></path><path d="M360 296c1-1 1-2 3-2h0l-2 3h1 3l-3 3c-2 0-6 2-8 1h1c2-1 4-3 5-5z" class="Q"></path><path d="M379 276h2 0c3-2 6-2 9-3l1 1h-1c-5 2-10 6-15 8-3 2-5 2-7 3l-1 1c-5 2-9 6-12 9h-1v-2c1-2 2-4 4-4 1-1 3-2 4-3l-2-2c3 0 5-1 7-2s4-1 5-3l7-3z" class="P"></path><path d="M217 277h2v3h0v2c1-1 2-1 3-2 0 2 0 1-1 2v4h1l1-1v1h0v6c-1 1-1 2 0 3l-1 1h1v4c1-2 1-4 1-5v-1-2l1-2v-1c1-2 0-2 1-3l1-2 1-1v-1c1-1 0 0 2-1 0 2 0 2-1 3l-1 1v1l-1 1v2l-1 1v1c-1 1-1 0-1 2h0v1l1 1-1 5c1 3 1 5 2 8l-1 1c1 2 1 2 1 3l4 4c0 1 1 1 2 1 1 1 1 2 1 3h1c1 0 1 1 2 1l1 1v1l1 1h4 0c0 2 1 3 2 5 0 2 2 3 3 4 1 2 2 4 3 5v1l2 2v-1c1-2 2-3 4-4 1 0 2 1 3 2h-2 9c2 2 6 3 9 3h1c3-2 30 5 36 6 1 1 1 1 2 0l-1-2-5-1 1-1v-1c1 0 2 0 4 1 1 0 2-1 3 0h0c2 0 2 0 4 1 1 0 2 0 3-1-2 0-8-1-8-2h6l1-1-10-2-3-1s0-1 1-1h3l5 1 1-3h-1-1c1-1 1-1 3-1 2-1 6-2 8-5-2 1-5 2-7 3h0-1c2-2 5-2 6-5 1-1 2-1 3-2l7-1 1 1 9-1c4 0 8 0 11 1 1 1 1 2 1 3l8 1h6c2 1 4 0 6 1v1c-3 0-5 0-8 1l10 1c4 0 7 1 10 3 5 2 9 4 14 6 4 1 8 2 11 5l-1 1c1 1 4 5 4 6v1l3 3c3 5 6 11 9 16 3 6 5 13 6 20v8 10 5h1l1 6c0 3 0 6-1 9 0 2 0 3-1 4 0 1 0 2 1 3 0 1-1 1-1 2v4c-1 1-1 1-1 2l-1 3v2c-1 3-1 7-2 10 0 3 0 5-1 8v1h-2v4l-1-4h0l-1 1c-1 2-2 6-2 7l-2 5-2 3 1 1h-1c-1 3-1 7 0 10 1 7 4 13 10 17 2 1 5 3 7 5l14 16h1l1-1 1-2c2 1 3 2 5 3 5 3 11 8 14 13 6 6 9 16 9 24-1 5-3 10-7 14l-1 1c-4 2-7 3-12 2-4-2-7-4-10-8v-1c-1-5 0-12 0-18-1-16-10-33-24-41-5-3-11-6-18-5h0c3 3 8 5 11 7 11 8 21 21 24 34 1 12 0 26 0 38l-1 5v1 2 2 1-5c0-1-1-1-1-3-1 6-1 12-1 17 0 2 0 5-1 6-1 3 1 7-1 10v-5c-2-1-2-2-3-4l-1-4c-1 3 0 7-1 10l-1-2-2-1-3 10c0 2 1 4 1 5l-1 1-1-1c0 1 0 2 1 3v4c-2-5-2-9-3-14l-2 1v-14c-2 2-5 5-8 7-9 6-25 10-25 24 0 3 0 5 1 7l-1 2-1 1v2l-2-2c-3-5-5-14-3-20v-1c3-14 16-19 22-30 6-9 4-20 1-29-8 10-23 20-37 20-7 0-13-1-20 1s-13 8-16 13c-2 3-3 6-5 9 2-10 5-17 12-25 5-4 11-7 16-10l4-2c7-4 13-8 15-17h1c1-5 1-9 1-13-1-6-3-13-7-17-3-4-7-7-10-11h0l-1 2c-2 13-5 27-17 34-5 2-10 4-15 8-5 3-9 8-10 14-1 5-1 8 2 12 1 1 1 1 1 2-1 0-1 0-1-1-3-1-5-6-5-9-2-8 0-16 5-22 2-3 5-5 7-8 3-4 5-11 4-15s-4-7-7-9c-1 0-3 2-4 3-7 10-16 21-28 25-9 3-23 2-32-2-5-3-9-7-11-12 0-3-1-4 1-6l-2-2c4-3 5 3 9 3h1l-2-2h-1v-1-1c-1-1-2-1-3-2-2-1-5-4-6-6v-1c-1-2-3-4-4-6 2 1 4 3 6 4v-1c-1-1-1-1-1-2-1-1-1 0-1-1l-1-1 1-1-3-6c-2-3-3-6-4-9h3c2 5 5 8 7 13l7 6c0-1 3-6 3-7 0-2-2-6-3-8-2-4-3-8-3-13h-1v-2s0-1 1-1h0l1 1c3 4 7 8 10 11l5-4c2-2 5-5 6-8l3-6c1-1 2-3 3-4 1-4 0-9 0-12-1-16-7-32-15-46-4-6-9-13-14-18-3 2-6 5-9 8 3 13 8 26 10 39-4-9-10-17-16-25-3-4-6-8-10-12-3 0-4-2-6-2-1-1-2 0-3 0-1 1-2 3-3 4 1 2 1 3 2 5l-6-4c-1 0-2-1-3 0-1 6 1 12 4 17-6-4-12-11-15-17-1 0-5 1-6 2-1 2 0 4 0 5 0 7 2 13 4 19-4-3-9-11-11-15-2-3-4-8-6-10-2 0-2 0-4 2v7c-1 1-1 2-1 2v1h-1v-1c-1-1-1-3-2-4v-5-7c-1-1-1-1-3-2h0c-2-2-4-2-6-3l-1 1-1-1-1-1c-9-4-11-11-11-21v-4c-1-2-5-5-6-6-3-4-6-8-5-13 0-3 1-6 4-8 3-4 8-4 12-6 8-5 15-10 22-15 10-7 21-14 30-21l6-6h0l-4-2h6v-1l-1-1c-1-1-3-2-3-4h-1v-1-3-2c-1-2-3-3-4-5-2-3-3-5-4-8 1-1 1-1 3-2l2 4h1v-2c1 0 1 1 1 1v1l1-1h1c-1-4 0-5 2-8h0c1-2 3-3 4-4z"></path><path d="M332 445c2-1 3 0 5 1v4h-1l-4-5z" class="c"></path><path d="M272 403h1c0 2 1 5 0 6l-1-1-3-3c1-1 2-2 3-2z" class="M"></path><path d="M324 484l1 1c0 2-2 5-3 7 0 0-2-4-2-5h0c1-1 3-2 4-3z" class="J"></path><path d="M317 481l7 3c-1 1-3 2-4 3h0l-3-3c-1-2-1-2 0-3z" class="T"></path><path d="M360 500h0c-2 1-6 5-9 4l-1-1 6-3 10-5c-2 2-5 3-6 4v1z" class="H"></path><path d="M240 405l2-1v1l-2 2v1h0c-3 1-9 3-11 3 1-1 1-1 2-1h1c-1-1-1 0-1-1l1-1c1-1 1-1 2-1h0c1-1 1-1 2-1h1c1 0 1 0 2-1h1zm104 75h2c1 0 1-1 1-1l1 1c1-1 1-1 2-1h1c-1 2-2 4-4 6l-1-1 1-2h0c-1 0-1 0-1 1-1 3-2 4-4 6 0-3 1-6 2-9z" class="M"></path><path d="M244 435l4 3c1 0 0 0 1-1 1 1 4 13 4 15-1-1-2-4-2-5-3-4-5-7-7-11v-1z" class="B"></path><defs><linearGradient id="Y" x1="440.576" y1="561.744" x2="438.076" y2="572.791" xlink:href="#B"><stop offset="0" stop-color="#787471"></stop><stop offset="1" stop-color="#908e8c"></stop></linearGradient></defs><path fill="url(#Y)" d="M438 574l1-21c2 6 2 12 2 18l-1 1c-1 1-1 0-1 1h-1v1z"></path><path d="M326 456h0c2 0 6 2 8 3 0 1-1 3-1 4-1 1 0 1-1 1-2-2-6-5-6-8z" class="E"></path><path d="M443 579c0-5 1-9 1-13h1v2 1c1 1 1 1 1 3v1c0 1-1 3 0 4 0 2 1 10 0 12 0 2 0 4-1 6v-21l-1-1c0 2 0 4-1 6z" class="O"></path><path d="M265 521c4 5 9 8 13 12l1 1h-1c-2 0-9-5-10-6-2-3-3-4-3-7z" class="K"></path><path d="M265 520c1-1 1-1 1-3 2 4 5 7 8 9l1 2c1 2 3 3 3 5-4-4-9-7-13-12v-1z" class="Q"></path><path d="M210 301c2 1 4 2 5 4h0l-2 1c1 1 2 2 3 2l3 2-2 2c-1 0-2-1-3-1-1-1-3-2-3-4h-1v-1-3-2z" class="f"></path><path d="M301 524c1 1 2 1 2 2s-1 3-1 4c-2 3-5 8-8 8l-3 1c-1-1-1-2-1-3 2 0 4 0 6-1v-2h1c1-1 1-1 1-2l1-2c2-1 2-2 2-5z" class="M"></path><path d="M322 467c3 1 6 3 9 4l-2 5c0 1-1 1-1 1-1-1-1-2-2-3-1-2-3-3-4-5v-2z" class="G"></path><path d="M357 460h0v4c-1 1-1 3-1 4v1l-1 2v1 2 1h-1v1 1 1h-1v1l2-1h1-1c-3 4-6 9-9 14h0l3-6h-1l-1-1c2-2 3-4 4-6h0c4-3 5-15 6-19z" class="h"></path><path d="M338 479l6-26v3 2c1 3 1 5 0 8 0 1-1 1-1 2 0 3-3 9-5 11z" class="G"></path><path d="M357 456v-1c1-1 1-1 1-2l1 2c0 3-1 7 0 10 0 2 0 4 1 5v4l1 1-1 1h0l-1 1v-3l-2-2v-2 1 1c-1-1 0-5 0-7 1-1 0-4 0-5h0 0v-4zm-160-85l1 1c-1 4-1 6-3 8v2c-2 4-4 6-7 8 0-1 1-3 2-5 1 0 1-1 2-2v-1c1-2 1-3 1-5h0l1-1h1c0-2 0-3 2-5z" class="M"></path><path d="M184 418c1 1 2 1 3 3v9c1 3 3 6 5 9l2 5c-6-6-10-16-10-26z" class="N"></path><path d="M286 543c1 0 2 0 2 1s-1 3-3 3c-2 1-4 1-7 1-2-1-5-3-6-5 1 0 1-1 2 0 2 0 3 0 5 1 0 1 0 1 2 2 1-1 3-2 5-3z" class="Q"></path><path d="M305 539h0c-3 3-6 7-9 10h-1c-3 2-6 4-9 5-5 2-10 2-14 0v-1c3 1 7 1 10 1 9-2 15-7 21-15v1l2-1zm150-5l1-2c2 1 3 2 5 3 5 3 11 8 14 13-3-1-4-5-7-6-1-1-2-2-3-2v-1c-1 0-2-1-3-1l1 2c1 0 1 1 2 1 2 2 4 4 4 6-2-2-4-5-7-6h0c-2-3-4-5-8-6l1-1z" class="Z"></path><path d="M410 614c2-3 4-5 5-8 1-4 1-10 1-15 1 3 1 7 1 10 0 0 1-1 2-1v-9-2c2 7 1 14-3 20-1 3-3 5-5 7l-1-2z" class="M"></path><path d="M313 450h2c1-1 1-2 1-2 1-1 1-2 2-2h0c-1 2-1 3-2 5v1l2-2v1c1 0 1 0 2-1h2c1-1 1-1 2 0l1 1h-1-1c-1 0-1 0-2 1h-1-1-1c0 1 0 1 1 2h0l1-1h2c1 1 1 1 0 2v1l1 1v1-1c0-1 0 0 1-1 0-2 1-3 1-5l1-1c-1 4-2 10-5 14 0-2 0-4 1-6v-1-2-1h-1c-1 0-1 1-2 1l-1-1h-1l-3 3h0c0-2-1-3 0-5v-1h-1v-1z" class="h"></path><path d="M305 496c-2 1-1 2-2 4-1-1-1-1-3-1l-1-1c-1 0-1 0-2-1l-3 3-1-1c-1-2-1-4 0-6 1 1 2 1 4 1 1 0 1-1 2-1 1 1 2 1 4 1 1 0 2 1 2 1v1z" class="O"></path><path d="M286 397h3l1 1c0 3 0 6 1 9 0 2 0 4 1 5v1h-1c0-1-1-2-1-3-1-4-6-9-9-12l5-1z" class="H"></path><path d="M177 343h2l-1 1v1s-1 1-1 2l-11 7c-2 2-4 2-7 2l18-13z" class="Y"></path><path d="M255 501v-2s0-1 1-1h0l1 1c0 3 4 7 6 10 0 0 1 2 0 3v1c1 1 2 3 3 4 0 2 0 2-1 3-4-6-7-12-9-19h-1z" class="X"></path><defs><linearGradient id="Z" x1="357.198" y1="473.082" x2="349.302" y2="459.918" xlink:href="#B"><stop offset="0" stop-color="#30302a"></stop><stop offset="1" stop-color="#605e5e"></stop></linearGradient></defs><path fill="url(#Z)" d="M354 459c0-2 0-3 1-4v1c0-2 0-3 2-5v5 4c-1 4-2 16-6 19v-3c0-3 2-7 2-11 0-2 0-4 1-6z"></path><path d="M203 422h1c1-1 4-1 5 0-1 4 0 10 1 13v1h0c-4-4-7-8-8-13l1-1z" class="V"></path><path d="M379 482l1-1c1-1 2-3 3-5 0-1 0-1-1-2 0-1 1-2 2-2h0v2 1l1 2c-6 10-14 18-25 23v-1c1-1 4-2 6-4h0a30.44 30.44 0 0 0 8-8c2-2 3-4 5-5z" class="K"></path><path d="M306 493l5 3c2 1 5 2 7 4h0l-4-1v1h0c1 0 2 1 2 2 0 2 0 3-1 5-3-1-6-6-8-8l-2-3v-1c1-1 1-1 1-2z" class="T"></path><path d="M464 577c-1 0-1 0-2-1-3-2-3-5-3-8 0 1 0 1-1 2 0 4 1 5 3 8-1 0-1-1-2-2-2-1-3-4-3-6s1-3 2-4c2-2 3-2 5-2l1 1c-1 1-2 2-3 4 0 1 0 2 2 3 1 2 2 2 4 2 2-1 3-3 4-4 0 2 0 3-2 4-1 1-2 1-3 2h-1c0 1-1 1-1 1z" class="N"></path><path d="M404 547v-4c-1-4-4-8-5-12-1-1-1-2-1-3 1 2 2 5 4 7l3 6 2 8v9c0 1 1 3 0 5 0 1 0 1-1 2 0 1-1 2-1 3v-11c1-2-1-7-1-10z" class="K"></path><defs><linearGradient id="a" x1="246.525" y1="510.13" x2="245.948" y2="521.395" xlink:href="#B"><stop offset="0" stop-color="#2e2a2d"></stop><stop offset="1" stop-color="#4e4e48"></stop></linearGradient></defs><path fill="url(#a)" d="M242 510h3c2 5 5 8 7 13 1 2 5 7 6 8 2 1 4 2 5 4l-1 1c-3-1-6-5-8-7 0-1-3-4-3-4-1 0-1 0-1 1l-1-1-3-6c-2-3-3-6-4-9z"></path><path d="M176 405s1 1 2 1c3 1 8-4 11-5l1-1v2l-4 4 6-1v1l-4 1c1 1 1 1 2 1 1 1 3 2 4 3-6 0-14-2-19-5l1-1z" class="C"></path><defs><linearGradient id="b" x1="193.433" y1="424.793" x2="186.945" y2="428.389" xlink:href="#B"><stop offset="0" stop-color="#676662"></stop><stop offset="1" stop-color="#818180"></stop></linearGradient></defs><path fill="url(#b)" d="M184 418l1-1c2 0 5 1 7 2v1c-2 7 0 12 0 19-2-3-4-6-5-9v-9c-1-2-2-2-3-3z"></path><path d="M244 436c-6-5-10-12-14-19 2 1 3 3 5 4l-1-3v-1c1 1 2 1 3 2v-3c1-1 2-1 3-1h1l1 2v1c-1 1-1 0-1 1-1 1-1 1-1 2 0 2 1 4 1 6l-3-6-1 1c0 2 2 5 3 7 2 2 3 4 4 6v1z" class="U"></path><path d="M272 543c-4-4-5-7-7-12 7 5 13 9 21 12-2 1-4 2-5 3-2-1-2-1-2-2-2-1-3-1-5-1-1-1-1 0-2 0z" class="B"></path><defs><linearGradient id="c" x1="255.821" y1="408.347" x2="260.179" y2="391.653" xlink:href="#B"><stop offset="0" stop-color="#5e5f56"></stop><stop offset="1" stop-color="#817d83"></stop></linearGradient></defs><path fill="url(#c)" d="M242 405c7-4 16-7 25-10h6l-1 1h1l-33 12v-1l2-2z"></path><path d="M242 417l7 20c-1 1 0 1-1 1l-4-3c-1-2-2-4-4-6-1-2-3-5-3-7l1-1 3 6c0-2-1-4-1-6 0-1 0-1 1-2 0-1 0 0 1-1v-1z" class="P"></path><path d="M213 318h1l-3 2-2 2c-5 4-12 9-15 13s-8 5-11 8c-3 0-4 3-6 4 0-1 1-2 1-2v-1l1-1h-2l36-25z" class="B"></path><path d="M183 343h0c2-1 5-2 7-2l-2 2h-2c-2 1-3 3-5 4l-1 1v1l-1 1c-1 0-1 0-1 1 1 0 1 0 2 1h-1c-3 2-4 3-7 3-4 2-8 2-13 2l-1-1h1c3 0 5 0 7-2l11-7c2-1 3-4 6-4z" class="E"></path><path d="M166 354h1c4 0 6-3 9-4h1c-2 2-4 3-5 5-4 2-8 2-13 2l-1-1h1c3 0 5 0 7-2z" class="N"></path><defs><linearGradient id="d" x1="450.398" y1="595.626" x2="436.602" y2="601.874" xlink:href="#B"><stop offset="0" stop-color="#66615f"></stop><stop offset="1" stop-color="#a4a8a5"></stop></linearGradient></defs><path fill="url(#d)" d="M443 579c1-2 1-4 1-6l1 1v21 3c-1 6-1 12-1 17 0 2 0 5-1 6-2-8-2-20-1-28l1-6v-8z"></path><path d="M263 509c4 3 7 6 12 8 8 4 17 5 26 8h-1l-2 3c-4 0-9-2-13-4-2-2-6-2-9-4s-6-5-10-6c-1-1-2-2-3-2 1-1 0-3 0-3z" class="Y"></path><defs><linearGradient id="e" x1="392.192" y1="563.293" x2="384.308" y2="565.707" xlink:href="#B"><stop offset="0" stop-color="#9c9b99"></stop><stop offset="1" stop-color="#bbbab9"></stop></linearGradient></defs><path fill="url(#e)" d="M389 554c-2-9-7-16-13-23 1 0 2 1 3 2 3 3 5 6 7 10 3 5 5 10 5 16 0 4 0 8-1 12v2c-3 2-3 5-5 8-1 1-2 2-2 3l-3 4c0-2 2-4 3-6l3-6c0-3 2-5 2-8 1-5 1-9 1-14z"></path><path d="M222 286l1-1v1h0v6c-1 1-1 2 0 3l-1 1h1v4h0v2c-2 0-6-2-8-3h-1l-4-5v-2h0c-1-1-1 0-3-1v-1h1v-2c1 0 1 1 1 1v1l1-1h1v2 1c0 1 1 2 2 3 0 1 0 0 1 1h0c1-1 1-2 2-2l1 1h0c2-3 2-6 4-9h0 1z" class="C"></path><path d="M223 296v4h0v2c-2 0-6-2-8-3h3c0-1 2 0 3-1l1 1h0l1-3z" class="O"></path><path d="M222 286l1-1v1h0v6c-1 1-1 2 0 3l-1 1s-1 0-1 1c-2-2-2-2-2-4s1-4 3-7z" class="c"></path><path d="M222 286l1-1v1h0c-1 3-1 4-1 7h-3c0-2 1-4 3-7z" class="Q"></path><defs><linearGradient id="f" x1="465.905" y1="550.171" x2="475.497" y2="565.07" xlink:href="#B"><stop offset="0" stop-color="#91918c"></stop><stop offset="1" stop-color="#adabaa"></stop></linearGradient></defs><path fill="url(#f)" d="M462 541c3 1 5 4 7 6 5 7 7 12 6 21-1 3-2 6-5 8-2 1-4 2-6 1h0s1 0 1-1h1c1-1 2-1 3-2 2-1 2-2 2-4h0c1-4 1-8 0-11-1-7-5-13-9-18z"></path><defs><linearGradient id="g" x1="343.487" y1="468.469" x2="355.013" y2="460.031" xlink:href="#B"><stop offset="0" stop-color="#555251"></stop><stop offset="1" stop-color="#878581"></stop></linearGradient></defs><path fill="url(#g)" d="M349 451c2 2 2 3 2 6-1 2-1 3-1 5l1 1 1 2h1c0 4-2 8-2 11v3h0-1c-1 0-1 0-2 1l-1-1s0 1-1 1h-2l2-7 1-5c0-1 0-3 1-4v-5l1-8z"></path><path d="M348 464v1c1 1 1 2 1 2v1h-1 0-1c0-1 0-3 1-4z" class="R"></path><path d="M346 473h1c1 1 1 3 1 5 2 0 2-1 3-2v3h0-1c-1 0-1 0-2 1l-1-1s0 1-1 1h-2l2-7z" class="F"></path><path d="M245 527c2 1 4 3 6 4v-1c-1-1-1-1-1-2-1-1-1 0-1-1l-1-1 1-1 1 1h0a30.44 30.44 0 0 0 8 8c1 0 3 2 4 4v2c0 1-1 2-2 3h-2v-1c-1-1-2-1-3-2-2-1-5-4-6-6v-1c-1-2-3-4-4-6z" class="N"></path><path d="M277 438c-6-9-11-17-18-25l1-1c6 6 9 12 14 19h1c1-6-3-8-4-13h0l6 8 6 13v4c-1-2-1-3-2-5l-1 1c1 1 1 2 1 3-1 0-1 0-2-1 0-1 0-1-1-2 0-1-1-1-1-1z" class="R"></path><defs><linearGradient id="h" x1="383.133" y1="474.156" x2="387.189" y2="458.048" xlink:href="#B"><stop offset="0" stop-color="#292725"></stop><stop offset="1" stop-color="#605d5a"></stop></linearGradient></defs><path fill="url(#h)" d="M385 454v4 2l2-2 1-1 1 1c0 2-1 4-1 6-1 2-1 5-1 7h0c0 2-1 4-2 6l-1-2v-1-2h0c-1 0-2 1-2 2 1 1 1 1 1 2-1 2-2 4-3 5l-1 1v-1l-2 2-1-1c1-1 2-1 2-2 1-1 1-2 2-2 1-2 2-3 2-5l-1 1v-1-2l-1 1v1h-1c0-1 2-5 2-6l1-1v-2-1-1c1-1 1-3 2-4h1v-4z"></path><path d="M389 554c0 5 0 9-1 14-1 0-3 1-3 3v1l-1 2c-1 2-2 2-3 4-1 1-2 2-3 4l-5 5c-4 0-8 1-12 3l-1-1 4-2c7-4 13-8 15-17h1c1-5 1-9 1-13v7h1c0 1-1 1-1 2v2c0 1 0 0-1 2h0v1c-1 3-2 5-3 7h1c1 0 1-1 1-2s0-1 1-2h0 1c1-1 1-1 2-1v-1c1-1 1-2 1-3l-1-1v-5c1-1 1-1 1-2h0 1v7c1-2 3-2 3-5 1-1 1-3 0-4v-1c1-1 0-2 1-4z" class="O"></path><path d="M283 424c1 2 4 7 3 9 1 1 3 8 2 8l-1 1c1 3 2 6 2 9v1c1 7 1 12 0 18v-1c1-6-2-13-3-19l-3-7v-4l-6-13c0 1 1 1 1 2h1l3 6v1c1 1 1 2 2 3h0v1-3-3l-1-1v-1s0-1-1-2h0c0-3 0-3 1-5z" class="K"></path><path d="M287 442c-1-3-2-5-2-8l1-1c1 1 3 8 2 8l-1 1zm-120-66c3-1 8-2 11-1 2 1 3 1 5 2 2 3 2 4 2 7l1-1v-3h1v2c1 0 1 1 2 2 0-1 1-2 1-3l2 1v1c-1 1-1 2-2 2-1 2-2 4-2 5l-6 4c0-4 0-8-1-11 0-3-3-4-5-5-2 0-3-1-5-1-1-1-2-1-4-1z" class="F"></path><path d="M167 376c3-1 8-2 11-1l1 1 4 4c-3 0-4-3-7-2-2 0-3-1-5-1-1-1-2-1-4-1z" class="e"></path><path d="M217 277h2v3h0v2c1-1 2-1 3-2 0 2 0 1-1 2v4h0c-2 3-2 6-4 9h0l-1-1c-1 0-1 1-2 2h0c-1-1-1 0-1-1-1-1-2-2-2-3v-1-2c-1-4 0-5 2-8h0c1-2 3-3 4-4z" class="D"></path><path d="M213 281h1v2h1l1 1c-1 3-2 5-5 7v-2c-1-4 0-5 2-8z" class="N"></path><path d="M284 493c1 0 2 2 3 3 1 2 3 5 6 7 3 4 9 8 14 10 1 0 0 0 1 1-3 0-6-2-9-4 1 2 4 3 6 5h1 0c-2 1-2 1-3 1v1h0c-2 0-3 0-4-1h0 0l2 3h0c-3-1-5-3-7-6-5-5-11-13-10-20z" class="X"></path><path d="M172 355c3 0 4-1 7-3-1 2-1 2 0 3h2l-2 1 7 1v1h0l-1 1c2 1 3 1 5 2 3 2 6 4 8 6l2 2c-2 1-2 1-2 3h0l-1-1c0-1-1-3-2-4l-1-1c-2-1-3-3-5-4h-1c-4-2-7-2-11 0-4-3-10-1-14-1-1-1-2-2-4-2l-2-1v-1l1-1 1 1c5 0 9 0 13-2z" class="U"></path><path d="M159 359c5-1 9 1 13 1 4-1 9-2 13-1 2 1 3 1 5 2 3 2 6 4 8 6l2 2c-2 1-2 1-2 3h0l-1-1c0-1-1-3-2-4l-1-1c-2-1-3-3-5-4h-1c-4-2-7-2-11 0-4-3-10-1-14-1-1-1-2-2-4-2z" class="O"></path><defs><linearGradient id="i" x1="349.065" y1="495.744" x2="360.606" y2="476.151" xlink:href="#B"><stop offset="0" stop-color="#13120f"></stop><stop offset="1" stop-color="#32302e"></stop></linearGradient></defs><path fill="url(#i)" d="M364 464h1v1l-2 10c-1 0-1 1-1 2v2c1-1 1-3 2-3l1 1h0c0 1 0 2 1 3-2 4-4 6-6 9h-1v-1h0c-1 0-1 0-2 1l-3 4-1 1c-1 1-2 2-2 3l-2 2v-1c0-1 0 0 1-1h-2l3-5c3-4 4-9 5-13 1-2 1-3 1-5h0v-2l2 2v3l1-1h0l1-1-1-1v-4 3l1 1c1-3 2-7 3-10z"></path><path d="M362 479c1-1 1-3 2-3l1 1h0c0 1 0 2 1 3-2 4-4 6-6 9h-1v-1s1-1 1-2l2-2c0-2 0-2-1-3l1-2z" class="O"></path><path d="M303 555c-6 6-14 9-23 9-7 0-17-3-22-9-2-2-4-5-6-8 9 8 18 13 30 13 4 0 7-1 10-3l2 1 1-1 2 1c1 0 3-1 4-2 0 0 1 0 2-1h0z" class="g"></path><path d="M399 550v-3c-1-6-3-12-4-18 2 2 3 6 4 9 1 2 2 3 3 4 1 2 1 4 2 5 0 3 2 8 1 10-2 2 0 6-1 9-1 4-2 8-4 12v-1l-1 2 1-4c-1 1-2 1-3 2 1-7 5-14 2-21v-2l1-1h0c0-2 0-2-1-3z" class="F"></path><path d="M400 575l2-11c0-1 1-2 1-3 1 2 1 3 1 5-1 4-2 8-4 12v-1l-1 2 1-4z" class="O"></path><path d="M404 566c1-3-1-7 1-9v11c-2 9-5 16-13 22-3 1-6 3-8 4l-1-2-1 1-1-1c2-2 4-4 5-7h1c2-2 2-6 4-9 1-1 2-2 3-2l-3 9h2c1-2 3-4 4-6 1-1 2-1 3-2l-1 4 1-2v1c2-4 3-8 4-12z" class="I"></path><defs><linearGradient id="j" x1="397.538" y1="579.057" x2="392.221" y2="584.193" xlink:href="#B"><stop offset="0" stop-color="#43423e"></stop><stop offset="1" stop-color="#5f5d5b"></stop></linearGradient></defs><path fill="url(#j)" d="M400 575l-1 4 1-2v1c-1 3-5 7-8 10 0-2 0-3 1-5s3-4 4-6c1-1 2-1 3-2z"></path><path d="M387 585c2-2 2-6 4-9 1-1 2-2 3-2l-3 9h2c-1 2-1 3-1 5-2 1-3 1-5 1-2 1-3 2-4 3l-1 1-1-1c2-2 4-4 5-7h1z" class="G"></path><path d="M391 583h2c-1 2-1 3-1 5-2 1-3 1-5 1 2-2 3-4 4-6z" class="R"></path><path d="M277 438s1 0 1 1c1 1 1 1 1 2 1 1 1 1 2 1 0-1 0-2-1-3l1-1c1 2 1 3 2 5l3 7c1 6 4 13 3 19v1c1-6 1-11 0-18l1-1c1 1 1 1 1 2v1c1 1 1 1 1 2v2c1 1 0 3 0 4-1 5-1 11-3 16l-3 8c0-3 1-9 0-12-1-1-1-1-1-2 0-12-4-23-8-34z" class="a"></path><path d="M286 450c1 6 4 13 3 19v1 3c-1 1-1 2-1 3v-2c-1-1-1-1-1-2-1-1 0-3 0-4-2-3 0-7-2-10v-2-3c0-1 1-2 1-3z" class="J"></path><path d="M356 526h1c0 4 0 8-1 11-2 11-7 20-16 26l-10 6-3 3-3 3v-1c1-2 3-4 5-5 1-2 2-3 3-4 0-2 1-3 2-4s1-2 2-2h1c1 0 3-1 4-2 0-1 1-1 2-2h-2v-2c1-3 1-5 2-7v2h0l1-1c0 2 1 4 0 6h0l2-2c2-3 4-7 5-11 1-1 1-3 2-4 0-2 1-2 2-3 1-3 1-5 1-7z" class="O"></path><path d="M283 424c-1-1-1-2-2-3-1-2-2-4-3-5v-1c1 2 4 4 5 7l1 3 1 1s0 1 1 2v2-2-1l-1-1v-2-1h-1v-1-2c0-1 0-1 1-1v-1l1 1c0 1 0 2 1 2v-2c0-1-1-1-1-2l1-1c2 2 3 4 3 6 1 2 1 3 1 4s1 1 1 2v2h1v-1-1h0c-1-2 0-3 0-4 1 2 1 7 0 9 1 6 1 14 0 20l-1 5v-2c0-1 0-1-1-2v-1c0-1 0-1-1-2l-1 1v-1c0-3-1-6-2-9l1-1c1 0-1-7-2-8 1-2-2-7-3-9z" class="H"></path><path d="M288 433h0c0-2-2-8-1-9 2 2 1 6 3 9 1-2-2-10-2-13 2 4 2 9 5 13h0c1 6 1 14 0 20v-8c0-2-1-2-2-4-1-3 0-6-3-8z" class="O"></path><defs><linearGradient id="k" x1="292.41" y1="442.963" x2="290.09" y2="452.537" xlink:href="#B"><stop offset="0" stop-color="#444240"></stop><stop offset="1" stop-color="#61615c"></stop></linearGradient></defs><path fill="url(#k)" d="M288 433c3 2 2 5 3 8 1 2 2 2 2 4v8l-1 5v-2c0-1 0-1-1-2v-1c0-1 0-1-1-2l-1 1v-1c0-3-1-6-2-9l1-1 1 5c1-4 0-8-1-13z"></path><path d="M213 373l4-3v2c-2 2-4 4-6 7 0 1-1 2-1 2v2l8-7c-2 4-7 6-9 11l-11 8c-1 1-4 3-5 3l-3 2-1 1c-3 1-8 6-11 5-1 0-2-1-2-1l-1 1-2-2c3-3 10-5 13-7 3-1 5-2 7-4 3-2 5-4 8-6l9-10 3-4z" class="J"></path><path d="M176 405l2-1c1-1 12-6 13-6h2l-3 2-1 1c-3 1-8 6-11 5-1 0-2-1-2-1z" class="H"></path><path d="M213 373l4-3v2c-2 2-4 4-6 7 0 1-1 2-1 2v2c-4 4-9 9-15 11l-2-1c3-2 5-4 8-6l9-10 3-4z" class="E"></path><path d="M262 393c8-3 16-4 24-5 5 1 8 1 12 2 1 0 4 1 4 1l1 1c2 1 5 2 7 3-5-1-11-2-16-2s-9 1-13 1c-2 1-6 2-8 2h-1l1-1h-6c-9 3-18 6-25 10v-1l-2 1v-2h0c-1 1-1 1-2 1l-3 1-1 1c-2 1-3 1-5 1 3-2 7-3 10-4 8-4 15-7 23-10z" class="F"></path><path d="M267 395c4-2 8-2 13-3 7-1 15-2 22 0l-8 1c-5 0-9 1-13 1-2 1-6 2-8 2h-1l1-1h-6z" class="W"></path><defs><linearGradient id="l" x1="397.143" y1="542.31" x2="390.38" y2="563.697" xlink:href="#B"><stop offset="0" stop-color="#1a1a1a"></stop><stop offset="1" stop-color="#5c5a53"></stop></linearGradient></defs><path fill="url(#l)" d="M392 535c-1-1-1-2-2-3l1-1c1 1 2 2 2 3l2 3c1 1 1 2 1 4h1c0 2 1 6 1 7l1 1v1h0c1 1 1 1 1 3h0l-1 1v2c3 7-1 14-2 21-1 2-3 4-4 6h-2l3-9c-1 0-2 1-3 2-2 3-2 7-4 9l-1-1 1-2 2-4c1-2 1-3 1-5v-2c1-4 1-8 1-12 1 1 1 2 1 3v1h1c1-4 1-9 1-13-1-2-1-4-2-6v-2h0l-1-1v-1h0c0-1 0-2-1-2l-1-3h0l2 2h1v-2z"></path><path d="M397 564c-1 3-1 7-3 10h0c-1 0-2 1-3 2-2 3-2 7-4 9l-1-1 1-2 2-4c1-2 1-3 1-5v-2c1 0 2-1 2-1l1-1h0v3c1-1 3-6 3-7v-1h1z" class="U"></path><defs><linearGradient id="m" x1="395.193" y1="558.237" x2="396.804" y2="580.728" xlink:href="#B"><stop offset="0" stop-color="#413f40"></stop><stop offset="1" stop-color="#63625d"></stop></linearGradient></defs><path fill="url(#m)" d="M392 535c-1-1-1-2-2-3l1-1c1 1 2 2 2 3l2 3c1 1 1 2 1 4h1c0 2 1 6 1 7l1 1v1h0c1 1 1 1 1 3h0l-1 1v2c3 7-1 14-2 21-1 2-3 4-4 6h-2l3-9h0c2-3 2-7 3-10 0-1 1-1 1-2v-1c0-1 1-4 0-5l-1-1v-4l-1-5c-1-1-1-1-1-3h0v-1c0-1 0-2-1-3v-1c-1-1-1-2-2-3z"></path><defs><linearGradient id="n" x1="413.39" y1="634.321" x2="389.053" y2="634.176" xlink:href="#B"><stop offset="0" stop-color="#333332"></stop><stop offset="1" stop-color="#5b5a58"></stop></linearGradient></defs><path fill="url(#n)" d="M411 616l-1 1c3-1 6-4 7-6s2-6 4-7c0 2-1 3-1 5h-1c-2 7-8 12-13 17 8-5 12-11 16-19h1c0 3-4 10-6 12-10 12-28 15-26 34 0 2 1 5 2 7l-1 1c-3-5-4-10-4-16 1-13 14-23 22-31l1 2z"></path><defs><linearGradient id="o" x1="331.207" y1="436.734" x2="348.99" y2="438.377" xlink:href="#B"><stop offset="0" stop-color="#a5a3a1"></stop><stop offset="1" stop-color="#cecdcc"></stop></linearGradient></defs><path fill="url(#o)" d="M333 407c1 0 2-1 3-1 5 6 9 12 11 20h1l3 9v3 13 6h0c0-3 0-4-2-6l-1 8-3 11-2-2c0-1 1-1 1-2 1-3 1-5 0-8v-2-3-6c1-4 1-8 0-13h0c-1-11-5-19-11-27z"></path><path d="M348 426l3 9v3 13 6h0c0-3 0-4-2-6 0-9 0-17-2-25h1z" class="c"></path><defs><linearGradient id="p" x1="450.488" y1="589.36" x2="428.103" y2="605.574" xlink:href="#B"><stop offset="0" stop-color="#978d86"></stop><stop offset="1" stop-color="#d3d8db"></stop></linearGradient></defs><path fill="url(#p)" d="M438 574v-1h1c0-1 0 0 1-1l1-1-1 13c0 1-1 3 0 5h0v6 3h1v-5c1-1 0-3 0-4l2-2-1 6c-1 8-1 20 1 28-1 3 1 7-1 10v-5c-2-1-2-2-3-4l-1-4c0-1 0-2-1-3v-2l-1-3c-1-3 0-8 0-11-1 0-1 1-1 2-1 1 1 1-1 2l-2-1 1-16 3-11 1 3 1-4z"></path><path d="M433 586l3-11 1 3-1 21c-1 0-1 1-1 2-1 1 1 1-1 2l-2-1 1-16z" class="S"></path><path d="M440 584c0 1-1 3 0 5h0v6 3h1v-5c1-1 0-3 0-4l2-2-1 6c-1 8-1 20 1 28-1 3 1 7-1 10v-5c0-7-2-13-3-19-1-7-1-16 1-23z" class="F"></path><path d="M263 512c1 0 2 1 3 2 4 1 7 4 10 6s7 2 9 4c4 2 9 4 13 4l2-3h1v-1c0 3 0 4-2 5l-1 2c0 1 0 1-1 2h-1v2c-2 1-4 1-6 1 0 1 0 2 1 3-3-1-6-1-9-3l-3-2-1-1c0-2-2-3-3-5l-1-2c-3-2-6-5-8-9-1-1-2-3-3-4v-1z" class="D"></path><path d="M263 512c1 0 2 1 3 2l12 9h0c-3 0-6-2-9-4l6 5v2h-1c-3-2-6-5-8-9-1-1-2-3-3-4v-1z" class="f"></path><path d="M301 525v-1c0 3 0 4-2 5l-1 2c0 1 0 1-1 2h-1v2c-2 1-4 1-6 1 0 1 0 2 1 3-3-1-6-1-9-3l-3-2-1-1c0-2-2-3-3-5 3 2 5 4 9 5h0c-1-1-1-2-1-4l2 2c1 1 2 2 3 1l-2-2h1c1 1 2 1 4 1 1 0 1 0 2 1h1 0c-1-1-4-2-5-3-2 0-3-1-4-1h-1v-1c4 1 7 3 12 3 1 0 1 0 1-1h-2c-3-1-8-2-10-5 4 2 9 4 13 4l2-3h1z" class="C"></path><defs><linearGradient id="q" x1="369.588" y1="589.172" x2="370.152" y2="595.436" xlink:href="#B"><stop offset="0" stop-color="#969492"></stop><stop offset="1" stop-color="#b6b5b5"></stop></linearGradient></defs><path fill="url(#q)" d="M373 587l5-5c1-2 2-3 3-4 1-2 2-2 3-4l1-2v-1c0-2 2-3 3-3 0 3-2 5-2 8l-3 6c-1 2-3 4-3 6l3-4c0-1 1-2 2-3 2-3 2-6 5-8 0 2 0 3-1 5l-2 4-1 2 1 1h-1c-1 3-3 5-5 7l1 1 1-1 1 2c-4 1-8 2-12 2-5 1-11 0-17 1-6 2-11 6-15 11 0-1 1-3 2-4l3-3-1-2c5-4 11-7 16-10l1 1c4-2 8-3 12-3z"></path><path d="M380 588l-2 1h-1c-1 1-1 1-2 1l-1-1h-1c4-2 7-7 9-10 1-2 2-2 4-3l-3 6c-1 2-3 4-3 6z" class="Q"></path><path d="M383 584c0-1 1-2 2-3 2-3 2-6 5-8 0 2 0 3-1 5l-2 4-1 2c-2 4-5 8-10 9h0c0-1 1-2 2-2 2-2 3-3 5-6v-1z" class="L"></path><defs><linearGradient id="r" x1="344.21" y1="595.994" x2="371.742" y2="589.791" xlink:href="#B"><stop offset="0" stop-color="#272625"></stop><stop offset="1" stop-color="#494747"></stop></linearGradient></defs><path fill="url(#r)" d="M360 589l1 1c4-2 8-3 12-3-6 5-13 6-20 9-3 1-5 3-8 5l-1-2c5-4 11-7 16-10z"></path><path d="M213 318c2-2 4-3 7-4h0c-2 2-6 4-6 7v1c2-1 3-3 7-4l1-1 1 1c-1 1-3 4-3 5s0 1-1 2c-2 1-4 5-6 7-1 0-3 2-4 2h0c-3 0-6 5-8 7-2 1-4 3-5 5l-1 1c0 1-1 1-2 2-2 1-5 2-6 4l-1 1c-2 0-3 1-5 1h-2c-1-1-1-1 0-3h1c-1-1-1-1-2-1 0-1 0-1 1-1l1-1v-1l1-1c2-1 3-3 5-4h2l2-2c-2 0-5 1-7 2h0c3-3 8-4 11-8s10-9 15-13l2-2 3-2h-1z" class="R"></path><path d="M213 318c2-2 4-3 7-4h0c-2 2-6 4-6 7v1c2-1 3-3 7-4l1-1 1 1c-1 1-3 4-3 5s0 1-1 2c-2 1-4 5-6 7-1 0-3 2-4 2h0l1-4 1-2c-2 0-5 2-6 3-1 2-2 3-4 3l-4 4-1-1c2-2 5-3 7-5 1-1 1-2 2-4 0-1 3-2 4-4v-2l2-2 3-2h-1z" class="S"></path><path d="M222 317l1 1c-1 1-3 4-3 5s0 1-1 2c-2 1-4 5-6 7-1 0-3 2-4 2h0l1-4 1-2 3-3c2-2 5-4 7-7l1-1z" class="d"></path><path d="M198 341l12-11-1 4c-3 0-6 5-8 7-2 1-4 3-5 5l-1 1c0 1-1 1-2 2-2 1-5 2-6 4l-1 1c-2 0-3 1-5 1h-2c-1-1-1-1 0-3h1c-1-1-1-1-2-1 0-1 0-1 1-1l1-1v-1l1-1c2-1 3-3 5-4h2 0 3 0l-2 2h0c3-1 6-3 9-4z" class="I"></path><path d="M188 343h0 3 0l-2 2h0c3-1 6-3 9-4-1 2-2 3-4 5l-7 3c-2 1-4 1-6 1l3-1v-2c2-1 3-2 4-2v-1l-2-1h2z" class="J"></path><defs><linearGradient id="s" x1="422.371" y1="588.074" x2="438.129" y2="591.426" xlink:href="#B"><stop offset="0" stop-color="#a4aaa7"></stop><stop offset="1" stop-color="#d1c8c9"></stop></linearGradient></defs><path fill="url(#s)" d="M428 633c2-9 1-18 1-26l1-45c0-8-1-17 0-25l1 1c4 3 5 10 6 15v16c-1 2-1 4-1 6l-3 11-1 16 2 1c2-1 0-1 1-2 0-1 0-2 1-2 0 3-1 8 0 11l1 3v2c1 1 1 2 1 3-1 3 0 7-1 10l-1-2-2-1-3 10c0 2 1 4 1 5l-1 1-1-1c0 1 0 2 1 3v4c-2-5-2-9-3-14z"></path><path d="M432 615v-17c0-2-1-3-1-4 1-2 0-9 0-11l1-1v-2c-1-1-1-2-1-3l1-1v-4l1 14-1 16c0 4 1 9 0 13z" class="U"></path><defs><linearGradient id="t" x1="427.08" y1="608.631" x2="439.024" y2="622.412" xlink:href="#B"><stop offset="0" stop-color="#535256"></stop><stop offset="1" stop-color="#898881"></stop></linearGradient></defs><path fill="url(#t)" d="M432 602l2 1c2-1 0-1 1-2 0-1 0-2 1-2 0 3-1 8 0 11l1 3v2c1 1 1 2 1 3-1 3 0 7-1 10l-1-2-2-1-3 10v1-1c-1-3 0-5 0-8l1-12c1-4 0-9 0-13z"></path><path d="M436 610l1 3v2c1 1 1 2 1 3-1 3 0 7-1 10l-1-2-2-1c1-5 1-10 2-15z" class="T"></path><defs><linearGradient id="u" x1="426.65" y1="547.332" x2="440.072" y2="575.061" xlink:href="#B"><stop offset="0" stop-color="#51504f"></stop><stop offset="1" stop-color="#7f7d7b"></stop></linearGradient></defs><path fill="url(#u)" d="M431 538c4 3 5 10 6 15v16c-1 2-1 4-1 6l-3 11-1-14c1-7 1-14 1-21-1-4-2-8-2-13z"></path><path d="M345 470l3-11v5c-1 1-1 3-1 4l-1 5-2 7c-1 3-2 6-2 9-7 21-15 42-30 59-3 2-6 5-9 7h0c-1 1-2 1-2 1-1 1-3 2-4 2l-2-1-1 1-2-1c6-3 9-6 14-10v-1c15-19 24-44 32-67 2-2 5-8 5-11l2 2z" class="Q"></path><path d="M343 468l2 2c-10 23-14 47-29 67-2 4-5 8-9 10l-1-1c15-19 24-44 32-67 2-2 5-8 5-11z" class="N"></path><defs><linearGradient id="v" x1="382.713" y1="461.875" x2="374.882" y2="446.322" xlink:href="#B"><stop offset="0" stop-color="#807e7c"></stop><stop offset="1" stop-color="#b1afae"></stop></linearGradient></defs><path fill="url(#v)" d="M374 434c0-3-1-6 0-9 2 7 3 13 4 19 1 1 2 1 3 0v-3l-1-1c1-1 0-3 1-4 1 4 1 10 1 14l-1 5 1-1c0-1 1-2 1-3l1-1c1 1 1 2 1 4h0v4h-1c-1 1-1 3-2 4v1 1 2l-1 1c0 1-2 5-2 6 0 2 0 2-1 4h0c-1 1-1 2-2 2h-1v-1h-1c0 2-2 3-3 5l-1 2-1 1-2 2-2 2-2 1h-1l2-2 2-2h0c-1 1-2 1-3 1-1 1-1 2-2 2h-1l1-1h-1c2-3 4-5 6-9-1-1-1-2-1-3h0l-1-1c-1 0-1 2-2 3v-2c0-1 0-2 1-2l2-10 1-2c1-1 1-2 2-4 1-3 1-5 2-8 0-2 0-4 1-6v-3l-1-3v-1c-1-1-2-3-2-5l-1-7 3 4 1-1c1 2 2 3 3 5z"></path><defs><linearGradient id="w" x1="378.078" y1="467.88" x2="383.502" y2="455.437" xlink:href="#B"><stop offset="0" stop-color="#42413f"></stop><stop offset="1" stop-color="#62615d"></stop></linearGradient></defs><path fill="url(#w)" d="M381 455l1-1c0-1 1-2 1-3l1-1c1 1 1 2 1 4h0v4h-1c-1 1-1 3-2 4v1 1 2l-1 1c0 1-2 5-2 6 0 2 0 2-1 4h0c-1 1-1 2-2 2h-1v-1h-1c0 2-2 3-3 5l-1 2-1 1-2 2c0-1 3-4 3-6 6-7 9-18 11-27z"></path><path d="M374 434c0-3-1-6 0-9 2 7 3 13 4 19 0 7 0 15-1 22-1 1-1 3-2 4h0l-1 1c0-2 0-4 1-6v-1c0-3 0-5-1-8 1-7 1-14 0-22z" class="b"></path><defs><linearGradient id="x" x1="375.299" y1="457.621" x2="368.781" y2="436.854" xlink:href="#B"><stop offset="0" stop-color="#93918f"></stop><stop offset="1" stop-color="#b9b8b6"></stop></linearGradient></defs><path fill="url(#x)" d="M367 426l3 4 1-1c1 2 2 3 3 5 1 8 1 15 0 22l-2 9c-1 0 0 0-1-1 1-6 1-13 0-19v-3l-1-3v-1c-1-1-2-3-2-5l-1-7z"></path><path d="M367 426l3 4c0 4 1 8 1 12l-1-3v-1c-1-1-2-3-2-5l-1-7z" class="D"></path><defs><linearGradient id="y" x1="360.556" y1="483.523" x2="374.35" y2="475.809" xlink:href="#B"><stop offset="0" stop-color="#13100f"></stop><stop offset="1" stop-color="#3a3b38"></stop></linearGradient></defs><path fill="url(#y)" d="M374 456c1 3 1 5 1 8v1c-1 2-1 4-1 6l1-1h0l-5 11v1c0 2-3 5-3 6l-2 2-2 1h-1l2-2 2-2h0c-1 1-2 1-3 1-1 1-1 2-2 2h-1l1-1h-1c2-3 4-5 6-9 1-2 3-4 3-7l3-8 2-9z"></path><path d="M371 445c1 6 1 13 0 19 1 1 0 1 1 1l-3 8c0 3-2 5-3 7-1-1-1-2-1-3h0l-1-1c-1 0-1 2-2 3v-2c0-1 0-2 1-2l2-10 1-2c1-1 1-2 2-4 1-3 1-5 2-8 0-2 0-4 1-6z" class="J"></path><path d="M363 475c2-2 2-5 4-7 1-2 2-4 3-5 0 3-2 8-1 10 0 3-2 5-3 7-1-1-1-2-1-3h0l-1-1c-1 0-1 2-2 3v-2c0-1 0-2 1-2z" class="F"></path><path d="M167 376c-7 1-14 2-21-2-1 0-4-2-5-4 0-1 0-3 1-4 2-3 6-6 10-6 1 0 3 1 4 2 8 2 13 2 21 0 4-2 7-2 11 0h1c2 1 3 3 5 4l1 1c1 1 2 3 2 4-2 2-2 3-2 5h-1l-1 1h0c0 2 0 3-1 5l-2-1c0 1-1 2-1 3-1-1-1-2-2-2v-2h-1v3l-1 1c0-3 0-4-2-7-2-1-3-1-5-2-3-1-8 0-11 1z" class="B"></path><path d="M145 371l-1-1c0-2-1-3 1-4l3-3h5c1 1 0 1 1 1h2c3 1 6 2 8 2l5-1v-1h3c1 0 1-1 2-1l1 1c1-1 4-1 6-1-2 2-6 2-9 2l-3 1h-3 0c-1 0-2 1-2 1-3 1-6 1-9 0-4-1-6-3-10 0v4z" class="Y"></path><path d="M195 367c1 1 2 3 2 4-2 2-2 3-2 5h-1l-1 1h0c0 2 0 3-1 5l-2-1c0 1-1 2-1 3-1-1-1-2-2-2v-2h-1v3l-1 1c0-3 0-4-2-7h0v-1c2 0 3-1 4-2h0c1 0 1 1 2 1 0-1-1-2-1-3h1l3 3v-1c-1-1-1-1-2-3 1 1 1 1 2 0v-1l3-3z" class="C"></path><path d="M181 363h0c1 0 2-1 3-1v1l-1 1c2 1 3 1 5 1v1h-1v1h2c-1 1-1 1-1 2h-3c-2 1-6 1-8 2 2 0 3-1 5 0-1 1 0 1-1 2-3 1-6 0-8 0-6 1-11 3-17 2-2 0-4 0-6-1l-5-3v-4c4-3 6-1 10 0 3 1 6 1 9 0 0 0 1-1 2-1h0 3l3-1c3 0 7 0 9-2z" class="f"></path><path d="M304 455l1-2c0-1 2-4 3-5 0-1 3-2 4-2l-1 1-1 1h1l2 2v1h1v1c-1 2 0 3 0 5h0l3-3h1l1 1c1 0 1-1 2-1h1v1 2 1c-1 2-1 4-1 6-1 4-2 8-3 11 0 2-1 4-1 6-1 1-1 1 0 3-2 0-2 0-3 2-1 1-2 2-3 4h0l-2 2s0 1 1 1c0 1 0 2 1 3l-5-3c0 1 0 1-1 2 0 0-1-1-2-1-2 0-3 0-4-1-1 0-1 1-2 1-2 0-3 0-4-1l4-14 4-16c1-3 2-6 3-8z" class="O"></path><path d="M314 478c0-1 0-2 1-2v-1l-1-1v-4c1 0 2 1 2 1 0 2 0 3-1 5 0 0 0 1-1 2z" class="F"></path><path d="M304 455c2-1 3-2 4-3l1 1v2h2c1 1 1 2 1 3s0 2-1 3l1 2c1-2 1-3 3-4v5l-1 1-2 6h-2-2v-3-5h-1c0 1 0 2-1 2h-1l1-5v-1l-5 4 3-8z" class="S"></path><path d="M310 471c0-1 1-2 1-4 1-1 2-2 2-4l2 1-1 1-2 6h-2z" class="J"></path><path d="M309 455h2c1 1 1 2 1 3s0 2-1 3l1 2c0 1 0 1-1 2 0-2 0-5-2-7 0 1-1 2-2 2 0-2 0-3 2-5z" class="F"></path><path d="M312 471l2-6c1 2 3 2 1 4l-1 1v4l1 1v1c-1 0-1 1-1 2v1c-1 0 0 0-1 1 0 1 0 2-1 3-1 0-2 1-2 2h0c-1 3-3 4-4 6v2c0 1 0 1-1 2 0 0-1-1-2-1-2 0-3 0-4-1-1 0-1 1-2 1-2 0-3 0-4-1l4-14v1 7 2h1v-1c2 0 3 1 5 2v-2c1 0 1-1 1-2 1-1 1 0 1-1s0 0 1-1h1l1-6c1-2 2-5 2-7h2z" class="S"></path><path d="M307 484l1-6 1 1h1 0v1c-1 1-1 3-1 5h-1l-1-1z" class="R"></path><path d="M310 471h2l-1 3c0 2 0 4-1 5h0-1l-1-1c1-2 2-5 2-7z" class="a"></path><path d="M299 493l-1-1 1-1 2 1h2l1-1 1 1 1-1v2c0 1 0 1-1 2 0 0-1-1-2-1-2 0-3 0-4-1z" class="F"></path><path d="M301 463l5-4v1l-1 5h1c1 0 1-1 1-2h1v5 3h2c0 2-1 5-2 7l-1 6h-1c-1 1-1 0-1 1s0 0-1 1c0 1 0 2-1 2v2c-2-1-3-2-5-2v1h-1v-2-7-1l4-16z" class="J"></path><path d="M305 474l2 2v4h-1c-1-1-1-1-1-2l-1-1 1-3z" class="R"></path><path d="M303 472h1v-1c1-1 2-3 3-4l-2 7-1 3c-1 3-2 5-4 7 0-4 1-8 3-12z" class="W"></path><path d="M301 463l5-4v1l-1 5-2 7c-2 4-3 8-3 12-1 1-1 3-2 4v1h-1v-2-7-1l4-16z" class="Q"></path><defs><linearGradient id="z" x1="318.658" y1="386.664" x2="292.427" y2="418.961" xlink:href="#B"><stop offset="0" stop-color="#aaa8a6"></stop><stop offset="1" stop-color="#f2f2f4"></stop></linearGradient></defs><path fill="url(#z)" d="M268 383c5-2 11-1 16-1l22 4v1c2 0 7 2 9 1 2 1 5 2 7 3s5 3 7 3c3 0 8 5 11 8 1 0 2 2 3 2 7 8 12 18 15 29 0 5 1 10 2 15 1 1 0 3 0 5v-1c-1-1-1 0-1-1v4l-1-2c0 1 0 1-1 2v1-5c-2 2-2 3-2 5v-1c-1 1-1 2-1 4-1 2-1 4-1 6h-1l-1-2-1-1c0-2 0-3 1-5h0v-6-13-3l-3-9h-1c-2-8-6-14-11-20-1 0-2 1-3 1l-9-9c-1 0-5-2-5-3-6-2-11-2-17-4 0 0-3-1-4-1-4-1-7-1-12-2-8 1-16 2-24 5v-2h-4c3-1 4-1 6-3 1-1 0-1 1-1 2 0 2 0 3-1s2-1 4-2h-8-1v1h0l-1-1c1-1 4-1 6-1z"></path><path d="M352 434c0-3-1-5-2-8v-2c1 2 2 4 2 6 1 2 2 9 3 10h1c1 2 1 8 1 10v1c-2 2-2 3-2 5v-1c-1 1-1 2-1 4-1-7 0-13-1-20 0-1 0-4-1-5z" class="a"></path><defs><linearGradient id="AA" x1="289.205" y1="381.351" x2="278.211" y2="385.42" xlink:href="#B"><stop offset="0" stop-color="#9a9b99"></stop><stop offset="1" stop-color="#c4bfc0"></stop></linearGradient></defs><path fill="url(#AA)" d="M268 383c5-2 11-1 16-1l22 4v1c-6-3-16-2-23-2-2-1-7 0-9-1h-3c-2 0-2-1-3-1z"></path><defs><linearGradient id="AB" x1="346.56" y1="458.447" x2="357.236" y2="444.67" xlink:href="#B"><stop offset="0" stop-color="#807d7f"></stop><stop offset="1" stop-color="#b0b1a7"></stop></linearGradient></defs><path fill="url(#AB)" d="M352 434c1 1 1 4 1 5 1 7 0 13 1 20-1 2-1 4-1 6h-1l-1-2-1-1c0-2 0-3 1-5h0v-6-13-3l1-1z"></path><path d="M352 434c1 1 1 4 1 5 0 0 0 1-1 2l-1-3v-3l1-1z" class="E"></path><path d="M286 388c15-2 33 2 45 11 8 7 14 17 17 27h-1c-2-8-6-14-11-20-1 0-2 1-3 1l-9-9c-1 0-5-2-5-3-6-2-11-2-17-4 0 0-3-1-4-1-4-1-7-1-12-2z" class="b"></path><path d="M324 398l1-1c4 3 8 6 11 9-1 0-2 1-3 1l-9-9z" class="U"></path><defs><linearGradient id="AC" x1="347.747" y1="430.264" x2="355.375" y2="426.761" xlink:href="#B"><stop offset="0" stop-color="#44423e"></stop><stop offset="1" stop-color="#6f6d6b"></stop></linearGradient></defs><path fill="url(#AC)" d="M329 394c3 0 8 5 11 8 1 0 2 2 3 2 7 8 12 18 15 29 0 5 1 10 2 15 1 1 0 3 0 5v-1c-1-1-1 0-1-1v4l-1-2c0 1 0 1-1 2v1-5-1c0-2 0-8-1-10l-1-7c-3-15-13-30-26-39z"></path><path d="M310 379c2-2 10 4 12 1 1-1 1-1 2 0h1l4 2c1 0 0 0 1 1 1 0 1 0 2 1 3 0 6 4 9 4l1-1c-2-1-4-2-5-4l15 8c1 0 2 0 4 1l-1-1-1-2 1-1c2 2 5 3 8 5l1 1h-2c1 2 5 6 7 8-2 0-3-1-5-2h-1c1 2 3 3 4 5-2-1-3-2-5-1 1 1 3 5 4 5 3 5 7 11 8 16-1 3 0 6 0 9-1-2-2-3-3-5l-1 1-3-4 1 7c0 2 1 4 2 5v1l1 3v3c-1 2-1 4-1 6-1 3-1 5-2 8-1 2-1 3-2 4l-1 2v-1h-1c-1 3-2 7-3 10l-1-1v-3c-1-1-1-3-1-5-1-3 0-7 0-10v-4c0 1 0 0 1 1v1c0-2 1-4 0-5-1-5-2-10-2-15-3-11-8-21-15-29-1 0-2-2-3-2-3-3-8-8-11-8-2 0-5-2-7-3s-5-2-7-3c2 0 3 0 5 1 1 0 2 0 3 1l1-1c-1-1-3-1-4-2l1-1c2 1 4 1 6 2 1 1 2 1 3 1-3-2-7-4-11-6h0 3v-1c-2 0-4 0-5-1-1 0-2-1-3-1l-4-1z" class="R"></path><path d="M315 388c2 0 3 0 5 1 1 0 2 0 3 1l1-1c6 2 15 9 20 14l-1 1c-1 0-2-2-3-2-3-3-8-8-11-8-2 0-5-2-7-3s-5-2-7-3z" class="W"></path><path d="M353 407c2 0 4 3 5 5 1 3 5 5 6 8h1c1 1 2 4 2 6l1 7-2-3c1 4 1 7 1 11-1-1-1-1-1-2-1-1-1-3-1-4-1-3-2-7-3-9-2-7-5-14-9-19z" class="L"></path><path d="M365 420c1 1 2 4 2 6l1 7-2-3v-5l-2-5h1z" class="Q"></path><defs><linearGradient id="AD" x1="348.534" y1="443.759" x2="364.999" y2="429.212" xlink:href="#B"><stop offset="0" stop-color="#737171"></stop><stop offset="1" stop-color="#a6a6a1"></stop></linearGradient></defs><path fill="url(#AD)" d="M343 404l1-1c6 6 9 12 13 19 1 2 3 5 3 8 1 2 1 4 2 6l1-1h0l1-1h0l1 1c0 1 0 3 1 4 0 1 0 1 1 2 0-4 0-7-1-11l2 3c0 2 1 4 2 5v1l1 3v3c-1 2-1 4-1 6-1 3-1 5-2 8-1 2-1 3-2 4l-1 2v-1h-1c-1 3-2 7-3 10l-1-1v-3c-1-1-1-3-1-5-1-3 0-7 0-10v-4c0 1 0 0 1 1v1c0-2 1-4 0-5-1-5-2-10-2-15-3-11-8-21-15-29z"></path><path d="M359 455v-4c0 1 0 0 1 1v1c1 4 1 7 1 12l3-6v4 1c-1 3-2 7-3 10l-1-1v-3c-1-1-1-3-1-5-1-3 0-7 0-10z" class="S"></path><defs><linearGradient id="AE" x1="359.152" y1="450.494" x2="368.848" y2="446.506" xlink:href="#B"><stop offset="0" stop-color="#4a484d"></stop><stop offset="1" stop-color="#706e65"></stop></linearGradient></defs><path fill="url(#AE)" d="M362 436l1-1h0l1-1h0l1 1c0 1 0 3 1 4 0 9 1 16 0 24l-1 2v-1h-1v-1-4c-1-2 0-7 0-10 0-4-1-9-2-13z"></path><path d="M366 430l2 3c0 2 1 4 2 5v1l1 3v3c-1 2-1 4-1 6-1 3-1 5-2 8-1 2-1 3-2 4 1-8 0-15 0-24 0 1 0 1 1 2 0-4 0-7-1-11z" class="g"></path><defs><linearGradient id="AF" x1="352.084" y1="403.549" x2="374.975" y2="419.129" xlink:href="#B"><stop offset="0" stop-color="#494643"></stop><stop offset="1" stop-color="#71706f"></stop></linearGradient></defs><path fill="url(#AF)" d="M310 379c2-2 10 4 12 1 1-1 1-1 2 0h1l4 2c1 0 0 0 1 1 1 0 1 0 2 1 3 0 6 4 9 4l1-1c-2-1-4-2-5-4l15 8c1 0 2 0 4 1l-1-1-1-2 1-1c2 2 5 3 8 5l1 1h-2c1 2 5 6 7 8-2 0-3-1-5-2h-1c1 2 3 3 4 5-2-1-3-2-5-1 1 1 3 5 4 5 3 5 7 11 8 16-1 3 0 6 0 9-1-2-2-3-3-5l-1 1-3-4c0-2-1-5-2-6h-1c-1-3-5-5-6-8-1-2-3-5-5-5h-1c0-1-1-2-2-3l1-1 3 2h0c-1-2-1-3-3-3h-1c-1-1-4-5-6-6l-9-6-1-1c-2-2-9-7-12-7-2 0-4 0-5-1-1 0-2-1-3-1l-4-1z"></path><defs><linearGradient id="AG" x1="359.885" y1="415.416" x2="372.115" y2="423.584" xlink:href="#B"><stop offset="0" stop-color="#a7a6a1"></stop><stop offset="1" stop-color="#c2c0c1"></stop></linearGradient></defs><path fill="url(#AG)" d="M365 420l-3-6c-1-2-1-3 0-5 3 5 9 12 9 17v3l-1 1-3-4c0-2-1-5-2-6z"></path><path d="M348 397l9 6 4 4 1 2c-1 2-1 3 0 5l3 6h-1c-1-3-5-5-6-8-1-2-3-5-5-5h-1c0-1-1-2-2-3l1-1 3 2h0c-1-2-1-3-3-3-1-2-3-3-3-5z" class="a"></path><path d="M358 412l1-1-3-5 4 3 1-2 1 2c-1 2-1 3 0 5l3 6h-1c-1-3-5-5-6-8z" class="I"></path><path d="M310 379c2-2 10 4 12 1 1-1 1-1 2 0h1l4 2c1 0 0 0 1 1 1 0 1 0 2 1 3 0 6 4 9 4 4 3 8 6 12 10h-1l-4-3c-2-1-3-2-5-3 2 2 4 3 5 5 0 2 2 3 3 5h-1c-1-1-4-5-6-6l-9-6-1-1c-2-2-9-7-12-7-2 0-4 0-5-1-1 0-2-1-3-1l-4-1z" class="C"></path><defs><linearGradient id="AH" x1="342.153" y1="387.095" x2="356.146" y2="395.249" xlink:href="#B"><stop offset="0" stop-color="#4d4b44"></stop><stop offset="1" stop-color="#747370"></stop></linearGradient></defs><path fill="url(#AH)" d="M337 383l15 8c1 0 2 0 4 1l-1-1-1-2 1-1c2 2 5 3 8 5l1 1h-2c1 2 5 6 7 8-2 0-3-1-5-2h-1c1 2 3 3 4 5-2-1-3-2-5-1l-2-2c-6-5-11-10-18-15-2-1-4-2-5-4z"></path><path d="M360 402l-3-6 3 1 3 3c1 2 3 3 4 5-2-1-3-2-5-1l-2-2z" class="Q"></path><path d="M355 391l-1-2 1-1c2 2 5 3 8 5l1 1h-2c1 2 5 6 7 8-2 0-3-1-5-2-1-1-2-3-3-3l-9-6c1 0 2 0 4 1l-1-1z" class="F"></path><path d="M226 309c1 2 1 2 1 3l4 4c0 1 1 1 2 1 1 1 1 2 1 3h1c1 0 1 1 2 1l1 1v1l1 1h4 0c0 2 1 3 2 5 0 2 2 3 3 4 1 2 2 4 3 5v1l2 2-1 1c-3 0-8 2-10 4-1 1-1 1-1 2l-1 1-2 2-4 3c-2 2-7 5-9 6-2 0-3 2-5 3h0c-2 2-6 7-7 10l-3 4-9 10v-2-5c1-1 1-2 2-3h0v-1c0-1 0-2-1-3v1c-1-2-1-3-2-5l-2-2c-2-2-5-4-8-6-2-1-3-1-5-2l1-1h0v-1l-7-1 2-1c2 0 3-1 5-1l1-1c1-2 4-3 6-4 1-1 2-1 2-2l1-1c1-2 3-4 5-5 2-2 5-7 8-7h0c1 0 3-2 4-2 2-2 4-6 6-7 1-1 1-1 1-2s2-4 3-5l-1-1 3-4 1-2v-2z" class="I"></path><path d="M237 327h-4v1l-1-1c1-2 3-4 6-4l1 1h4 0c0 2 1 3 2 5 0 2 2 3 3 4 1 2 2 4 3 5v1l-1 1-2-3c-1-1-1-1-2-1 0-3-3-4-4-5v-2s-1-1-1-2h-2l-1-1-1 1z" class="C"></path><path d="M212 337l3-2c-1 1-1 2-2 3s-3 2-4 4l-3 3 6-4v1l-1 1-7 7 4-2c1-1 2-1 4-1h-1c-1 2-2 3-4 5-1 1-2 1-3 2-2 2-4 3-6 5 1-2 2-4 3-5 0-1 1-2 1-2v-2c1-2 2-3 2-5v-1c3-2 5-5 8-7z" class="G"></path><path d="M220 342c-4 4-8 7-11 12l-3 4c0 1-1 2-1 2h1c-1 2-2 6-3 7l-1-1v-1l-1-1-1 1h-1l-1-1v-2c-1-1-1 0-1-1-2 0-3 0-4-1h-2v-2l4-4h0-3v-1l1-1v1c2 0 2-1 4-2 2-2 4-4 7-6v-1 1c0 2-1 3-2 5v2s-1 1-1 2c-1 1-2 3-3 5 2-2 4-3 6-5 1-1 2-1 3-2 2-2 3-3 4-5h1c2-2 5-5 8-5z" class="B"></path><path d="M195 354h0c1-1 2-1 3-2h1c-1 2-2 3-2 5l-3 3h-1-2v-2l4-4z" class="N"></path><path d="M198 362v-1c1-1 2-3 4-4h1l4-4 1-1c0 2-7 10-8 12l-1 1-1-1v-2z" class="Y"></path><path d="M213 332h0c1 0 1 0 2-1 1 0 4-2 6-2l1 1-5 3-2 2-3 2c-3 2-5 5-8 7v1c-3 2-5 4-7 6-2 1-2 2-4 2v-1l-1 1v1h3 0l-4 4v2h2c1 1 2 1 4 1 0 1 0 0 1 1v2l1 1h1l-2 2c-2-2-5-4-8-6-2-1-3-1-5-2l1-1h0v-1l-7-1 2-1c2 0 3-1 5-1l1-1c1-2 4-3 6-4 1-1 2-1 2-2l1-1c1-2 3-4 5-5 2-2 5-7 8-7h0c1 0 3-2 4-2z" class="T"></path><path d="M181 355c2 0 3-1 5-1l1-1c1-2 4-3 6-4l-1 2v1c-1 0-2 1-3 2v1l1 1v1h-2 0l2 2v2c-2-1-3-1-5-2l1-1h0v-1l-7-1 2-1z" class="D"></path><path d="M193 352l2-1c2-1 1-3 3-5 1-1 3-2 4-3 3-3 5-6 9-8l1 2c-3 2-5 5-8 7v1c-3 2-5 4-7 6-2 1-2 2-4 2v-1z" class="Y"></path><path d="M226 309c1 2 1 2 1 3l4 4c0 1 1 1 2 1 1 1 1 2 1 3h1c1 0 1 1 2 1l1 1v1c-3 0-5 2-6 4l1 1v-1h4c-3 2-6 3-9 4-3 3-7 3-9 6l-2 1v-1h-1l1-1c1-1 1-2 0-3l5-3-1-1c-2 0-5 2-6 2-1 1-1 1-2 1h0c2-2 4-6 6-7 1-1 1-1 1-2s2-4 3-5l-1-1 3-4 1-2v-2z" class="R"></path><path d="M222 330l3-2c-1 3-4 6-7 8l1 1-2 1v-1h-1l1-1c1-1 1-2 0-3l5-3z" class="c"></path><path d="M219 325l1 1 3-3c0 1 1 1 0 2l-2 1 1 1c1 0 1 0 3-1h0c1-1 3-3 5-3l-5 5-3 2-1-1c-2 0-5 2-6 2-1 1-1 1-2 1h0c2-2 4-6 6-7z" class="U"></path><path d="M226 309c1 2 1 2 1 3l4 4c0 1 1 1 2 1 1 1 1 2 1 3h1c1 0 1 1 2 1l1 1v1c-3 0-5 2-6 4l1 1v-1h4c-3 2-6 3-9 4l-1-1 1-3c2-1 4-3 6-5h0-3c1-1 1-2 1-3-1 0-1-1-2-1h-1l-1-1 1-1-3-3v-2-2z" class="b"></path><path d="M226 311v2l3 3-1 1-1 1c0 1 0 1 1 2l1-1v1l-1 1-3 4v1h0c-2 1-2 1-3 1l-1-1 2-1c1-1 0-1 0-2l-3 3-1-1c1-1 1-1 1-2s2-4 3-5l-1-1 3-4 1-2z" class="Q"></path><path d="M226 311v2l3 3-1 1-1 1c0 1 0 1 1 2l1-1v1l-1 1v-1c-2 1-3 1-5 2l-1 1 4-9-1-1 1-2z" class="a"></path><path d="M220 342c2-3 5-5 8-7 1-1 2-2 3-2h1c1-1 4-2 5-3h3v2h0c2 0 2 0 3 2v1h2v2h0c2 1 2 2 3 3h0l1 1c-2 1-3 1-5 2-1 1-3 2-4 3h-1c-2 0-3 1-4 2h0c-2 0-5 3-6 4l1 1c2 1 3-2 4-2 1-1 3 0 4 0l-4 3c-2 2-7 5-9 6-2 0-3 2-5 3h0c-2 2-6 7-7 10l-3 4-9 10v-2-5c1-1 1-2 2-3h0v-1c0-1 0-2-1-3v1c-1-2-1-3-2-5l-2-2 2-2 1-1 1 1v1l1 1c1-1 2-5 3-7h-1s1-1 1-2l3-4c3-5 7-8 11-12z" class="L"></path><path d="M225 347c3 0 5-4 7-5 1-1 2-1 3-1-1 1-4 4-4 5l-6 6c-2 0-5 3-6 5h0c-2 0-2 1-3 2h-1c2-2 4-4 6-7 1-1 2-3 4-5z" class="T"></path><path d="M221 357h2c0 2-2 4-3 6-2 2-6 7-7 10l-3 4c0-2 1-4 2-6h0v-2c0-1 1-2 2-3l7-9z" class="K"></path><path d="M202 373c1-1 2-3 3-4h0v3l2 2c1 0 4-4 5-5v2h0c-1 2-2 4-2 6l-9 10v-2-5c1-1 1-2 2-3h0v-1c0-1 0-2-1-3z" class="H"></path><path d="M231 346v1c-1 2-2 4-3 5v1l1-1 1 1c2 1 3-2 4-2 1-1 3 0 4 0l-4 3c-2 2-7 5-9 6-2 0-3 2-5 3h0c1-2 3-4 3-6h-2l4-5h0l6-6z" class="W"></path><path d="M231 346v1c-1 2-2 4-3 5v1l1-1 1 1c2 1 3-2 4-2 1-1 3 0 4 0l-4 3h-1-1-1-1l-1-1-1 1c0 1-1 1-2 1 0-2 0-1-1-3h0l6-6z" class="c"></path><path d="M220 342c2-3 5-5 8-7 1-1 2-2 3-2h1c1-1 4-2 5-3h3v2l-5 4c-1 0-2 1-2 2l-5 4c-3 3-7 7-12 8-3 4-7 7-10 10h-1s1-1 1-2l3-4c3-5 7-8 11-12z" class="P"></path><path d="M216 350c2-2 7-7 10-8v1l2-1c-3 3-7 7-12 8z" class="B"></path><path d="M235 336l5-4h0c2 0 2 0 3 2v1h2v2h0c2 1 2 2 3 3h0l1 1c-2 1-3 1-5 2-1 1-3 2-4 3h-1c-2 0-3 1-4 2h0c-2 0-5 3-6 4l-1 1v-1c1-1 2-3 3-5v-1c0-1 3-4 4-5-1 0-2 0-3 1-2 1-4 5-7 5 2-2 7-6 8-9 0-1 1-2 2-2z" class="D"></path><path d="M243 336h1v1l-2 2h2v1c-3 2-6 5-9 6l-1 1h-1v-1c3-4 6-7 10-10z" class="B"></path><path d="M235 336h1c2-1 3-2 6-2v1l-7 6c-1 0-2 0-3 1-2 1-4 5-7 5 2-2 7-6 8-9 0-1 1-2 2-2z" class="N"></path><path d="M253 340c1-2 2-3 4-4 1 0 2 1 3 2h-2 9c2 2 6 3 9 3h1l18 4-1 1h-1c1 1 1 1 2 1l8 2-1 1h-6 1c1 1 1 1 2 1 1 1 2 1 3 2h-2-3l2 1 5 3 6 3c1 0 2 1 2 1v1c2 3 6 5 9 6h0l4 2-1 2-7-3c1 2 2 2 3 3 0 1-1 2-1 2 1 1 3 2 4 2l6 3h1c1 1 6 2 7 4s3 3 5 4l-1 1c-3 0-6-4-9-4-1-1-1-1-2-1-1-1 0-1-1-1l-4-2h-1c-1-1-1-1-2 0-2 3-10-3-12-1l4 1c1 0 2 1 3 1 1 1 3 1 5 1v1h-3 0c4 2 8 4 11 6-1 0-2 0-3-1-2-1-4-1-6-2l-1 1c1 1 3 1 4 2l-1 1c-1-1-2-1-3-1-2-1-3-1-5-1-2 1-7-1-9-1v-1l-22-4c-5 0-11-1-16 1-2 0-5 0-6 1l1 1h0v-1h1 8c-2 1-3 1-4 2s-1 1-3 1c-1 0 0 0-1 1-2 2-3 2-6 3h4v2c-8 3-15 6-23 10-3 1-7 2-10 4-8 3-16 5-25 5-3 0-7-1-10-1-1-1-3-2-4-3-1 0-1 0-2-1l4-1v-1l-6 1 4-4v-2l3-2c1 0 4-2 5-3l11-8c2-5 7-7 9-11l-8 7v-2s1-1 1-2c2-3 4-5 6-7v-2l-4 3c1-3 5-8 7-10h0c2-1 3-3 5-3 2-1 7-4 9-6l4-3 2-2 1-1c0-1 0-1 1-2 2-2 7-4 10-4l1-1v-1z" class="X"></path><path d="M229 367c2-2 4-4 7-4-3 4-8 9-12 12 1-2 1-3 2-4l3-2v-2z" class="E"></path><path d="M212 398h-1c-1 2-3 3-5 5l8-2h1c-3 1-8 3-11 5h7c-3 1-7 1-10 2l-2-1c-1 0-1 0-2-1l1-1c5-2 9-5 14-7z" class="d"></path><path d="M229 367v2l-3 2c-1 1-1 2-2 4-3 3-6 6-10 8-2 2-3 3-5 4 2-5 7-7 9-11v-1l11-8z" class="L"></path><path d="M198 395c0 1-1 2-1 3h0l2-1 1 1c-1 1-2 1-3 2v1c1-1 2-1 3-2 2-2 3-2 5-3 3-1 5-2 8-4 6-4 11-8 17-12-2 3-5 5-6 9-4 3-8 7-12 9l-2-1c-4 2-9 6-14 7l-1 1h-2-1l-6 1 4-4v-2l3-2c1 0 4-2 5-3z" class="G"></path><path d="M291 368c4 0 11 2 15 4l2-2 3 1c3 0 5 2 8 3 1 1 3 2 4 2l6 3h1c1 1 6 2 7 4s3 3 5 4l-1 1c-3 0-6-4-9-4-1-1-1-1-2-1-1-1 0-1-1-1l-4-2h-1c-1-1-1-1-2 0-2 3-10-3-12-1-1 0-3-1-4-1h-1l-1-1c-1 0-1-1-2-1l-10-2c-1-1-1-1-2-1 2-1 7 0 9 1h9l-17-6z" class="Q"></path><path d="M308 370l3 1 3 3v1c-1 0-1 0-2-1-2-1-4-2-6-2l2-2z" class="L"></path><path d="M322 380c-2-1-7-2-8-3 2-1 4 0 6 1 3 1 6 2 9 2l1-1c1 1 6 2 7 4s3 3 5 4l-1 1c-3 0-6-4-9-4-1-1-1-1-2-1-1-1 0-1-1-1l-4-2h-1c-1-1-1-1-2 0z" class="F"></path><path d="M289 377l-8-1h-2v-1c11 1 22 3 32 6h2l1-1c1 0 2 1 3 1 1 1 3 1 5 1v1h-3 0c4 2 8 4 11 6-1 0-2 0-3-1-2-1-4-1-6-2l-1 1c1 1 3 1 4 2l-1 1c-1-1-2-1-3-1-2-1-3-1-5-1-2 1-7-1-9-1v-1l-22-4c1 0 2-1 3-1v-1l-5-1v-1c1 0 2 0 4 1h4 2l3 1c1 0 2 0 3 1h1 1 2 1c1 1 1 0 3 1h0 3l-14-3h-2c-1-1-3-1-4-1v-1z" class="g"></path><path d="M289 377c2 0 5 1 7 1 7 2 14 3 20 6v1c-5 0-10-2-15-3-2 0-3-1-4 0l12 3c-1 1-2 1-3 1l-22-4c1 0 2-1 3-1v-1l-5-1v-1c1 0 2 0 4 1h4 2l3 1c1 0 2 0 3 1h1 1 2 1c1 1 1 0 3 1h0 3l-14-3h-2c-1-1-3-1-4-1v-1z" class="d"></path><path d="M296 357h0 3 5l6 3c1 0 2 1 2 1v1c2 3 6 5 9 6h0l4 2-1 2-7-3c1 2 2 2 3 3 0 1-1 2-1 2-3-1-5-3-8-3l-3-1-2 2c-4-2-11-4-15-4h0l-13-2h0c3 0 5-1 8 0h1 1l-2-1 1-1h0c-1-3-7-1-9-2h4 0l-4-1h5c3-1 5-1 7-1 1 1 2 0 3 1h2l1 1h2l3 1h1 1 2c1 1 1 1 2 1-2-4-9-3-11-7z" class="T"></path><path d="M300 365c3-2 9 1 12 2h1c2 1 3 1 4 2 1 2 2 2 3 3 0 1-1 2-1 2-3-1-5-3-8-3l-3-1-7-2-1-1c3 1 7 2 11 2-3-2-7-3-11-4z" class="R"></path><path d="M296 357h0 3 5l6 3c1 0 2 1 2 1v1c2 3 6 5 9 6h0l4 2-1 2-7-3c-1-1-2-1-4-2h-1c-3-1-9-4-12-2h0-1c-1-1-3-1-4-1s0 0-1-1v-1h2c2 1 3 1 5 1h1 1 2c1 1 1 1 2 1-2-4-9-3-11-7z" class="I"></path><path d="M296 357c6 2 11 6 17 8l1 1-1 1h-1c-3-1-9-4-12-2h0-1c-1-1-3-1-4-1s0 0-1-1v-1h2c2 1 3 1 5 1h1 1 2c1 1 1 1 2 1-2-4-9-3-11-7z" class="E"></path><defs><linearGradient id="AI" x1="228.753" y1="394.675" x2="235.623" y2="406.609" xlink:href="#B"><stop offset="0" stop-color="#c4c2c1"></stop><stop offset="1" stop-color="#f0efef"></stop></linearGradient></defs><path fill="url(#AI)" d="M236 398h0l12-6c3-1 7-2 11-3 1-1 1-1 2-1h3c-2 2-3 2-6 3h4v2c-8 3-15 6-23 10-3 1-7 2-10 4-8 3-16 5-25 5-3 0-7-1-10-1-1-1-3-2-4-3-1 0-1 0-2-1l4-1v-1h1 2l1-1c5-1 10-5 14-7l2 1c-5 2-9 5-14 7l-1 1c1 1 1 1 2 1l2 1c3-1 7-1 10-2h2c8-3 15-5 23-8z"></path><path d="M236 398h1v1c-1 0-1 0-2 1-5 4-16 7-22 7v-1c8-3 15-5 23-8z" class="B"></path><defs><linearGradient id="AJ" x1="263.621" y1="377.6" x2="245.057" y2="349.015" xlink:href="#B"><stop offset="0" stop-color="#b2b1b0"></stop><stop offset="1" stop-color="#efeeed"></stop></linearGradient></defs><path fill="url(#AJ)" d="M253 340c1-2 2-3 4-4 1 0 2 1 3 2h-2 9c2 2 6 3 9 3h1l18 4-1 1h-1c1 1 1 1 2 1l8 2-1 1h-6 1c1 1 1 1 2 1 1 1 2 1 3 2h-2-3l2 1 5 3h-5-3 0c-8-1-15-4-22-6l-2-1c-7 0-12 1-18 3l-1-1h1c-2 0-3 1-5 1-2 1-2-1-4 1h1l-1 1c-2 0-3 2-5 3h1l-1 1h0c-2 1-3 3-4 4-3 0-5 2-7 4l-11 8v1l-8 7v-2s1-1 1-2c2-3 4-5 6-7v-2l-4 3c1-3 5-8 7-10h0c2-1 3-3 5-3 2-1 7-4 9-6l4-3 2-2 1-1c0-1 0-1 1-2 2-2 7-4 10-4l1-1v-1z"></path><path d="M272 350h-2l-1-1h3v-1c-2 0-5 0-8-1 3-1 9 0 12 0l4 1c2 0 3 1 5 2h-10c-1 1-1 0-1 1l-2-1z" class="E"></path><path d="M236 354l-3 4c-4 4-8 8-13 11 0 1-2 3-3 3v-2c5-6 12-11 19-16z" class="B"></path><path d="M240 349l1 2-5 3c-7 5-14 10-19 16l-4 3c1-3 5-8 7-10h0c2-1 3-3 5-3 2-1 7-4 9-6l4-3 2-2z" class="S"></path><path d="M274 351c0-1 0 0 1-1h10c3 1 8 3 12 3l2 1 5 3h-5-3 0c-8-1-15-4-22-6z" class="U"></path><path d="M253 340c1-2 2-3 4-4 1 0 2 1 3 2h-2l-1 2v1c2 1 4 1 6 1 5 1 9 1 13 3h0-2c-11-2-23 1-33 6l-1-2 1-1c0-1 0-1 1-2 2-2 7-4 10-4l1-1v-1z" class="V"></path><path d="M253 340c1-2 2-3 4-4 1 0 2 1 3 2h-2l-1 2v1c2 1 4 1 6 1-7 1-14 2-21 6h-1c0-1 0-1 1-2 2-2 7-4 10-4l1-1v-1z" class="E"></path><path d="M258 338h9c2 2 6 3 9 3h1l18 4-1 1h-1c1 1 1 1 2 1l8 2-1 1h-6l-15-5h-5c-4-2-8-2-13-3-2 0-4 0-6-1v-1l1-2z" class="P"></path><path d="M330 324l7-1 1 1 9-1c4 0 8 0 11 1 1 1 1 2 1 3l8 1h6c2 1 4 0 6 1v1c-3 0-5 0-8 1l10 1c4 0 7 1 10 3 5 2 9 4 14 6 4 1 8 2 11 5l-1 1c1 1 4 5 4 6v1l3 3c3 5 6 11 9 16 3 6 5 13 6 20v8 10 5h1l1 6c0 3 0 6-1 9 0 2 0 3-1 4 0 1 0 2 1 3 0 1-1 1-1 2v4c-1 1-1 1-1 2l-1 3v2c-1 3-1 7-2 10 0 3 0 5-1 8v1h-2v4l-1-4h0l-1 1c-1 2-2 6-2 7l-2 5-2 3c0 1-2 2-3 3-4 3-10 6-16 5-3 0-6-2-7-4-5-4-6-9-7-14v-3-2h-2 0c0-2 0-5 1-7 0-2 1-4 1-6l-1-1-1 1-2 2v-2-4h0c0-2 0-3-1-4l-1 1c0 1-1 2-1 3l-1 1 1-5c0-4 0-10-1-14-1 1 0 3-1 4l1 1v3c-1 1-2 1-3 0-1-6-2-12-4-19-1-5-5-11-8-16-1 0-3-4-4-5 2-1 3 0 5 1-1-2-3-3-4-5h1c2 1 3 2 5 2-2-2-6-6-7-8h2l-1-1c-3-2-6-3-8-5l-1 1 1 2 1 1c-2-1-3-1-4-1l-15-8c-1-2-6-3-7-4h-1l-6-3c-1 0-3-1-4-2 0 0 1-1 1-2-1-1-2-1-3-3l7 3 1-2-4-2h0c-3-1-7-3-9-6v-1s-1-1-2-1l-6-3-5-3-2-1h3 2c-1-1-2-1-3-2-1 0-1 0-2-1h-1 6l1-1-8-2c-1 0-1 0-2-1h1l1-1-18-4c3-2 30 5 36 6 1 1 1 1 2 0l-1-2-5-1 1-1v-1c1 0 2 0 4 1 1 0 2-1 3 0h0c2 0 2 0 4 1 1 0 2 0 3-1-2 0-8-1-8-2h6l1-1-10-2-3-1s0-1 1-1h3l5 1 1-3h-1-1c1-1 1-1 3-1 2-1 6-2 8-5-2 1-5 2-7 3h0-1c2-2 5-2 6-5 1-1 2-1 3-2z" class="i"></path><path d="M406 393c5 6 11 20 11 28-1 6 0 13-3 18h-1v1h0l-1-1 1-1v-2-1h0c3-7 1-17-2-23v-1c1-1 0-2 0-2 0-4-1-5-2-8 0-1 0-1-1-2-1-2-2-4-2-6z" class="Z"></path><defs><linearGradient id="AK" x1="421.259" y1="415.446" x2="418.673" y2="402.964" xlink:href="#B"><stop offset="0" stop-color="#1c1b1b"></stop><stop offset="1" stop-color="#3a3a37"></stop></linearGradient></defs><path fill="url(#AK)" d="M403 371c4 3 7 7 10 11l3 9 2 4c1 2 2 6 3 7l1 3c1 3 1 12 0 15v-2-1l-1 3v-1c-1-8-5-14-7-22v-1c-1-3-2-7-4-10-3-4-6-8-10-11l1-1 7 7v-1l-1-2c-2-2-4-4-4-7z"></path><path d="M403 371c4 3 7 7 10 11l3 9 2 4h-1c-2-5-7-9-9-14v-1l-1-2c-2-2-4-4-4-7z" class="I"></path><path d="M405 451c2-1 4-2 6-1h0c-6 3-11 6-14 12-1 4-2 8-1 11 1 4 4 5 7 6h0c-3 0-4-1-6-3 0 2 3 4 4 4h1l2 2c-1 0-3-1-4-1-2-1-4-3-5-5 0-1-1-2-1-3 0 3 1 4 2 6l-1 1c-1 0-3-4-3-4-1 2 0 3 1 5l2 3h-1c-1-1-1-2-1-2l-2-2c0 1 0 1 1 2 1 3 1 4 3 6 0 0 1 1 1 2-5-4-6-9-7-14v-3-2h-2 0c0-2 0-5 1-7 0-2 1-4 1-6v-4l2-2c1 0 1 0 1 1s0 3 1 4h0c1 1 1 2 1 3l5-5 2-2 4-2z" class="N"></path><defs><linearGradient id="AL" x1="392.238" y1="465.086" x2="389.155" y2="458.024" xlink:href="#B"><stop offset="0" stop-color="#2b2929"></stop><stop offset="1" stop-color="#464442"></stop></linearGradient></defs><path fill="url(#AL)" d="M391 452c1 0 1 0 1 1s0 3 1 4h0c1 1 1 2 1 3-3 5-5 9-5 16v-3-2h-2 0c0-2 0-5 1-7 0-2 1-4 1-6v-4l2-2z"></path><defs><linearGradient id="AM" x1="415.045" y1="430.064" x2="407.009" y2="423.48" xlink:href="#B"><stop offset="0" stop-color="#32302e"></stop><stop offset="1" stop-color="#4c4c48"></stop></linearGradient></defs><path fill="url(#AM)" d="M400 406c2 1 3 2 4 3h1 1c1 2 1 3 2 4v-1c1 2 1 4 2 6l1-1c1-1 0-1 1-2-1-1-1-2-1-3 3 6 5 16 2 23h0l-1 1h-1 0l-2 2v3h-1c0-2 0-5-2-5-1-2-2-4-2-6h2c0-2-2-5-3-7l-1-2 1-1v-4l-1-1v-2c0-3-1-5-2-7z"></path><path d="M408 427l1-5v-1h1l1 3 1-1v5h-1-1 0c-1 2-1 3-2 5h-1c0-1-1-2-1-3h2v-3z" class="C"></path><path d="M406 421l1-7 3 5h0c1 1 1 3 2 4l-1 1-1-3h-1v1l-1 5-1 1c0-2 0-4-1-6v-1z" class="J"></path><defs><linearGradient id="AN" x1="406.706" y1="425.99" x2="403.341" y2="421.97" xlink:href="#B"><stop offset="0" stop-color="#666462"></stop><stop offset="1" stop-color="#7a7978"></stop></linearGradient></defs><path fill="url(#AN)" d="M402 421l1-1v-4l-1-1v-2c1 2 2 4 3 7 0 0 0 1 1 1v1c1 2 1 4 1 6l1-1v3h-2c0-2-2-5-3-7l-1-2z"></path><path d="M400 406c2 1 3 2 4 3h1 1c1 2 1 3 2 4v-1c1 2 1 4 2 6v1h0l-3-5-1 7c-1 0-1-1-1-1-1-3-2-5-3-7 0-3-1-5-2-7z" class="V"></path><path d="M384 345c5 0 8 2 13 4 2 1 4 1 7 2 2 1 3 3 5 4s4 2 5 3c5 6 8 13 11 20 2 6 3 11 3 17l1 1-1 1c0 1 0 0 1 1 0 2-1 3-1 5-1-1 0-2-1-4 0-1-1-1-1-2v2h-3l-2-5c-1 0-1 0-1 1 0 2 1 4 1 7h0c-1-1-2-5-3-7l-2-4-3-9c1-1 1-1 2-1h1c0-1 0-2 1-3l-2-2h0l-6-9c-2-3-4-5-7-7h3c-1-1-2-2-5-3l-1-3c-1-1-2-1-4-2h-1c-2-3-7-5-10-7z" class="K"></path><path d="M399 354c8 4 16 9 18 18v1c-2-2-3-4-5-7-2-2-4-4-7-6-1-1-2-2-5-3l-1-3z" class="b"></path><path d="M402 360h3c3 2 5 4 7 6 2 3 3 5 5 7l2 3c-2 0-2 0-3-1l-1 1h0l-6-9c-2-3-4-5-7-7z" class="W"></path><path d="M415 376l1-1c1 1 1 1 3 1 2 2 3 6 4 9 2 2 3 8 3 12v2h-3l-2-5c-1 0-1 0-1 1 0 2 1 4 1 7h0c-1-1-2-5-3-7l-2-4-3-9c1-1 1-1 2-1h1c0-1 0-2 1-3l-2-2z" class="M"></path><path d="M417 378c1 2 2 5 3 6l1 5h0-1l-2-3c0-1-2-4-3-5h1c0-1 0-2 1-3z" class="a"></path><path d="M413 382c1-1 1-1 2-1 1 1 3 4 3 5-1 1-1 3-2 5l-3-9z" class="C"></path><path d="M415 376l1-1c1 1 1 1 3 1 2 2 3 6 4 9h-1c-1-1-1-1-2-1-1-1-2-4-3-6l-2-2z" class="S"></path><defs><linearGradient id="AO" x1="388.676" y1="397.096" x2="396.244" y2="394.775" xlink:href="#B"><stop offset="0" stop-color="#6f6e6d"></stop><stop offset="1" stop-color="#8b8a85"></stop></linearGradient></defs><path fill="url(#AO)" d="M358 371h5c-1 1-2 2-2 3 3 1 6 1 9 1 4 1 9 0 13 1 11 3 16 8 23 17 0 2 1 4 2 6 1 1 1 1 1 2 1 3 2 4 2 8 0 0 1 1 0 2v1c0 1 0 2 1 3-1 1 0 1-1 2l-1 1c-1-2-1-4-2-6v1c-1-1-1-2-2-4h-1-1c-1-1-2-2-4-3 0 0-1-2-2-2-3-5-7-10-12-13-1-1-2-2-3-2-2-1-4-3-7-3-2 0-4-1-6-2l-1-1c-2-1-4-1-6-2-1-1-3-1-5-1l-10-3h11c-1 0-3-1-4-1s-2-1-4-1h0 5c0-1 0-1-2-2v-1l4-1z"></path><path d="M398 404l1-1c2 1 2 2 4 3 1 0 2 2 3 3h0-1-1c-1-1-2-2-4-3 0 0-1-2-2-2z" class="c"></path><path d="M381 381c3-1 6-1 9 0h0l-1 1 3 2c-1 0-1 0-1 1-4-2-7-3-10-4h0z" class="F"></path><path d="M390 381c5 2 8 6 11 10 2 2 5 5 6 8 2 3 2 6 4 10 0 0 1 1 0 2v1c0 1 0 2 1 3-1 1 0 1-1 2l-1 1c-1-2-1-4-2-6 0-2-1-4-2-6v-2c-4-7-9-14-15-19 0-1 0-1 1-1l-3-2 1-1z" class="C"></path><path d="M406 404c2 2 3 4 5 6v1 1c0 1 0 2 1 3-1 1 0 1-1 2l-1 1c-1-2-1-4-2-6 0-2-1-4-2-6v-2z" class="J"></path><path d="M358 371h5c-1 1-2 2-2 3 3 1 6 1 9 1 4 1 9 0 13 1 11 3 16 8 23 17 0 2 1 4 2 6 1 1 1 1 1 2 1 3 2 4 2 8-2-4-2-7-4-10-1-3-4-6-6-8-3-4-6-8-11-10h0c-3-1-6-1-9 0h0-1l-1 1c3 1 6 2 8 4-1 0-1 0-2-1-1 0-2-1-3-1v1c1 1 2 2 3 2v1l1 1v1h0c-1-1-2-1-3-1-2-1-4-3-7-3-2 0-4-1-6-2l-1-1c-2-1-4-1-6-2-1-1-3-1-5-1l-10-3h11c-1 0-3-1-4-1s-2-1-4-1h0 5c0-1 0-1-2-2v-1l4-1z" class="O"></path><path d="M363 381c5-1 8 1 12 1l-9-4c5 1 10 1 15 3h0-1l-1 1c3 1 6 2 8 4-1 0-1 0-2-1-1 0-2-1-3-1v1c1 1 2 2 3 2v1l1 1v1h0c-1-1-2-1-3-1-2-1-4-3-7-3-2 0-4-1-6-2l-1-1c-2-1-4-1-6-2z" class="S"></path><path d="M370 384c3 0 5 0 7 1l1-1-1-1h0c1-1 2 0 3 0h1l1 1v1c1 1 2 2 3 2v1l1 1v1h0c-1-1-2-1-3-1-2-1-4-3-7-3-2 0-4-1-6-2z" class="J"></path><defs><linearGradient id="AP" x1="362.897" y1="380.599" x2="366.258" y2="365.056" xlink:href="#B"><stop offset="0" stop-color="#a19f9e"></stop><stop offset="1" stop-color="#dcdad9"></stop></linearGradient></defs><path fill="url(#AP)" d="M317 358c3 1 6 2 9 2 2 0 1 0 2 1h1v-1l22 6h1c3 0 7 1 10-1 4 1 8 2 12 1 3 0 5 1 7 0l2-1c1 1 2 1 3 1l1-1c5 2 10 5 14 8v1l-1 1c4 3 7 7 10 11 2 3 3 7 4 10v1c-4-6-9-12-14-17-11-9-24-10-37-9h-5l-4 1v1c2 1 2 1 2 2h-5 0c2 0 3 1 4 1s3 1 4 1h-11c-8 0-15-5-23-7l-4-2h0c-3-1-7-3-9-6v-1l8 3c-1-2-1-3-3-3-2-1-3-1-4-2h0 3l1-1z"></path><path d="M362 365c4 1 8 2 12 1v2c-7 0-16 0-22-2 3 0 7 1 10-1z" class="c"></path><path d="M317 358c3 1 6 2 9 2 2 0 1 0 2 1h1v-1l22 6c-4 2-9 0-13-1l-12-3c-3-1-6-2-10-3l1-1z" class="Q"></path><path d="M387 365c5 2 10 5 14 8v1l-1 1c-3-1-5-3-7-4-6-3-12-3-19-3v-2c3 0 5 1 7 0l2-1c1 1 2 1 3 1l1-1z" class="D"></path><path d="M312 361l8 3c3 1 7 2 10 3 9 3 18 4 28 4l-4 1v1c2 1 2 1 2 2h-5 0c2 0 3 1 4 1s3 1 4 1h-11c-8 0-15-5-23-7l-4-2h0c-3-1-7-3-9-6v-1z" class="S"></path><path d="M321 368c2 0 4 1 6 1l14 4c3 1 7 1 10 2h0c2 0 3 1 4 1s3 1 4 1h-11c-8 0-15-5-23-7l-4-2z" class="Z"></path><path d="M353 340l17-1h0c-1 1-2 1-3 1v1h1 2l1 1 13 3c3 2 8 4 10 7h1c2 1 3 1 4 2l1 3c3 1 4 2 5 3h-3c3 2 5 4 7 7l6 9h0l2 2c-1 1-1 2-1 3h-1c-1 0-1 0-2 1-3-4-6-8-10-11 0 3 2 5 4 7l1 2v1l-7-7v-1c-4-3-9-6-14-8l-1 1c-1 0-2 0-3-1l-2 1c-2 1-4 0-7 0-4 1-8 0-12-1-3 2-7 1-10 1h-1l-22-6v1h-1c-1-1 0-1-2-1-3 0-6-1-9-2l-1 1h-3 0c1 1 2 1 4 2 2 0 2 1 3 3l-8-3s-1-1-2-1l-6-3-5-3-2-1h3 2c-1-1-2-1-3-2-1 0-1 0-2-1h-1 6l1-1-8-2c-1 0-1 0-2-1h1l1-1-18-4c3-2 30 5 36 6 1 1 1 1 2 0l-1-2-5-1 1-1v-1c1 0 2 0 4 1 1 0 2-1 3 0h0c2 0 2 0 4 1 1 0 2 0 3-1 1 0 2 0 2 1h1c3 0 7 3 10 2h5l10-2c-1-1-2-1-3-1h3v-1h-2c1-1 2-1 3-2z" class="g"></path><path d="M353 353c5-1 11-1 15 0-2 1-3 0-5 2-2 0-8-2-10-2z" class="U"></path><path d="M346 348l14-1 6 1v1h-22l2-1z" class="R"></path><path d="M310 342c1 0 2 0 4 1 1 0 2-1 3 0h0c2 0 2 0 4 1 1 0 2 0 3-1 1 0 2 0 2 1h1c3 0 7 3 10 2l5 2h4l-2 1-1 1c-6 0-11-2-17-2l-12-3-5-1 1-1v-1z" class="V"></path><path d="M310 342c1 0 2 0 4 1 1 0 2-1 3 0h0c2 0 2 0 4 1 1 0 2 0 3-1 1 0 2 0 2 1h1c3 0 7 3 10 2l5 2c-9 1-19-3-28-5h-4v-1z" class="L"></path><path d="M337 358c-1-1 0-1-1-1l-1-1h-1 4 0l11 2 21 5c4 1 8 2 11 3-2 1-4 0-7 0-4 1-8 0-12-1l-13-3-12-4z" class="N"></path><path d="M387 365c-4-2-9-4-13-5-10-3-21-4-31-8 3-1 8 0 10 1 2 0 8 2 10 2 10 2 21 3 30 8 4 2 7 5 10 8 0 3 2 5 4 7l1 2v1l-7-7v-1c-4-3-9-6-14-8z" class="B"></path><defs><linearGradient id="AQ" x1="399.25" y1="385.766" x2="385.75" y2="349.234" xlink:href="#B"><stop offset="0" stop-color="#5d5d55"></stop><stop offset="1" stop-color="#989595"></stop></linearGradient></defs><path fill="url(#AQ)" d="M368 353h2c1 1 3 0 4 1h3c9 1 18 6 25 12l3-1 4 2 6 9h0l2 2c-1 1-1 2-1 3h-1c-1 0-1 0-2 1-3-4-6-8-10-11-3-3-6-6-10-8-9-5-20-6-30-8 2-2 3-1 5-2z"></path><defs><linearGradient id="AR" x1="410.383" y1="374.554" x2="405.416" y2="364.549" xlink:href="#B"><stop offset="0" stop-color="#aba9a7"></stop><stop offset="1" stop-color="#c8c7c5"></stop></linearGradient></defs><path fill="url(#AR)" d="M402 366l3-1 4 2 6 9h0l2 2c-1 1-1 2-1 3l-14-15z"></path><path d="M360 347c10 0 18 1 27 4 2 0 7 2 8 1 2 1 3 1 4 2l1 3c3 1 4 2 5 3h-3c3 2 5 4 7 7l-4-2-3 1c-7-6-16-11-25-12-5-2-11-3-16-4h-1c2-1 4-1 6-1h0v-1l-6-1z" class="T"></path><path d="M398 357c1 1 3 2 4 3 3 2 5 4 7 7l-4-2c-3-2-7-4-7-8z" class="E"></path><defs><linearGradient id="AS" x1="390.083" y1="361.526" x2="377.605" y2="342.522" xlink:href="#B"><stop offset="0" stop-color="#625f5c"></stop><stop offset="1" stop-color="#807f7d"></stop></linearGradient></defs><path fill="url(#AS)" d="M360 347c10 0 18 1 27 4 2 0 7 2 8 1 2 1 3 1 4 2l1 3c3 1 4 2 5 3h-3c-1-1-3-2-4-3-7-3-14-6-21-7l-11-1h0v-1l-6-1z"></path><path d="M353 340l17-1h0c-1 1-2 1-3 1v1h1 2l1 1 13 3c3 2 8 4 10 7h1c-1 1-6-1-8-1-9-3-17-4-27-4l-14 1h-4l-5-2h5l10-2c-1-1-2-1-3-1h3v-1h-2c1-1 2-1 3-2z" class="b"></path><path d="M353 340l17-1h0c-1 1-2 1-3 1v1h1 2l1 1h-1c-1 1-2 1-3 1-3 1-7 1-9 1h-5-1c-1-1-2-1-3-1h3v-1h-2c1-1 2-1 3-2z" class="C"></path><path d="M352 342h18c-1 1-2 1-3 1-3 1-7 1-9 1h-5-1c-1-1-2-1-3-1h3v-1z" class="D"></path><path d="M352 343c2 0 4-1 6 0v1h-5-1c-1-1-2-1-3-1h3z" class="E"></path><defs><linearGradient id="AT" x1="358.856" y1="342.782" x2="357.144" y2="350.218" xlink:href="#B"><stop offset="0" stop-color="#949591"></stop><stop offset="1" stop-color="#cdc7c8"></stop></linearGradient></defs><path fill="url(#AT)" d="M352 344h1c4 1 8 1 12 1s9 1 13 1c5 1 10 3 15 5l1 1h1c-1 1-6-1-8-1-9-3-17-4-27-4l-14 1h-4l-5-2h5l10-2z"></path><path d="M277 341c3-2 30 5 36 6 1 1 1 1 2 0l-1-2 12 3c1 1 2 2 2 3h3l8 2c2 1 4 1 7 3l3 2-11-2h0-4 1l1 1c1 0 0 0 1 1l12 4 13 3c-3 2-7 1-10 1h-1l-22-6v1h-1c-1-1 0-1-2-1-3 0-6-1-9-2l-1 1h-3 0c1 1 2 1 4 2 2 0 2 1 3 3l-8-3s-1-1-2-1l-6-3-5-3-2-1h3 2c-1-1-2-1-3-2-1 0-1 0-2-1h-1 6l1-1-8-2c-1 0-1 0-2-1h1l1-1-18-4z" class="G"></path><path d="M339 353c2 1 4 1 7 3l3 2-11-2c-2-1-7-1-9-3 1 0 8 1 10 1v-1z" class="L"></path><path d="M299 354c4 0 7 1 11 2 2 0 5 1 7 2l-1 1h-3 0c1 1 2 1 4 2 2 0 2 1 3 3l-8-3s-1-1-2-1l-6-3-5-3z" class="D"></path><path d="M326 359c-5-1-10-3-15-5 2 0 5 0 7 1h1c-5-3-11-4-15-6 3 0 6 1 9 2l24 7 12 4 13 3c-3 2-7 1-10 1h-1l-22-6-3-1z" class="I"></path><path d="M326 359l1-1 6 1 4 1 7 2c2 1 3 0 5 0l13 3c-3 2-7 1-10 1h-1l-22-6-3-1z" class="V"></path><defs><linearGradient id="AU" x1="368.458" y1="440.932" x2="385.039" y2="397.875" xlink:href="#B"><stop offset="0" stop-color="#080706"></stop><stop offset="1" stop-color="#31302e"></stop></linearGradient></defs><path fill="url(#AU)" d="M317 369l7 3 1-2c8 2 15 7 23 7l10 3c2 0 4 0 5 1 2 1 4 1 6 2l1 1c2 1 4 2 6 2 3 0 5 2 7 3 1 0 2 1 3 2 5 3 9 8 12 13 1 0 2 2 2 2 1 2 2 4 2 7v2l1 1v4l-1 1 1 2c1 2 3 5 3 7h-2c0 2 1 4 2 6 0 1 1 3 0 4 0-1 0-2-1-3 0-1 0-2-1-3 0 0 0-1-1-1l-1-3h0l2 16c0 1 0 3 1 5l-4 2-2 2-5 5c0-1 0-2-1-3h0c-1-1-1-3-1-4s0-1-1-1l-2 2v4l-1-1-1 1-2 2v-2-4h0c0-2 0-3-1-4l-1 1c0 1-1 2-1 3l-1 1 1-5c0-4 0-10-1-14-1 1 0 3-1 4l1 1v3c-1 1-2 1-3 0-1-6-2-12-4-19-1-5-5-11-8-16-1 0-3-4-4-5 2-1 3 0 5 1-1-2-3-3-4-5h1c2 1 3 2 5 2-2-2-6-6-7-8h2l-1-1c-3-2-6-3-8-5l-1 1 1 2 1 1c-2-1-3-1-4-1l-15-8c-1-2-6-3-7-4h-1l-6-3c-1 0-3-1-4-2 0 0 1-1 1-2-1-1-2-1-3-3z"></path><defs><linearGradient id="AV" x1="407.029" y1="440.093" x2="393.929" y2="435.427" xlink:href="#B"><stop offset="0" stop-color="#11100c"></stop><stop offset="1" stop-color="#3f3f3e"></stop></linearGradient></defs><path fill="url(#AV)" d="M390 412h1c2 1 3 4 4 6 0-2-1-4-1-6l2 4h3l3 5 1 2c1 2 3 5 3 7h-2c0 2 1 4 2 6 0 1 1 3 0 4 0-1 0-2-1-3 0-1 0-2-1-3 0 0 0-1-1-1l-1-3h0l2 16c0 1 0 3 1 5l-4 2c0-4 1-8 1-12-1-11-7-19-12-29z"></path><path d="M400 422l3 1c1 2 3 5 3 7h-2l-4-8z" class="C"></path><path d="M396 416h3l3 5 1 2-3-1c-2-2-3-4-4-6z" class="J"></path><defs><linearGradient id="AW" x1="393.854" y1="454.624" x2="391.146" y2="444.876" xlink:href="#B"><stop offset="0" stop-color="#4d4b4b"></stop><stop offset="1" stop-color="#73716f"></stop></linearGradient></defs><path fill="url(#AW)" d="M391 422c2 4 3 7 4 10l1 3c1 4 2 7 2 10s-1 8 1 10l-5 5c0-1 0-2-1-3h0c-1-1-1-3-1-4s0-1-1-1v-2c0-2 0-5 1-7v-1h0c1-2 1-4 1-6h-2v-3-2-2-1h-1c0-2 0-3 1-5v-1z"></path><path d="M395 432l1 3-2 3c0-2 0-4-1-5h2v-1z" class="W"></path><path d="M391 422c2 4 3 7 4 10v1h-2c-1-2-1-3-1-5s0-3-1-5v-1z" class="g"></path><defs><linearGradient id="AX" x1="398.879" y1="452.211" x2="391.121" y2="445.789" xlink:href="#B"><stop offset="0" stop-color="#3b3a36"></stop><stop offset="1" stop-color="#636161"></stop></linearGradient></defs><path fill="url(#AX)" d="M396 435c1 4 2 7 2 10s-1 8 1 10l-5 5c0-1 0-2-1-3 1-7 2-12 1-19l2-3z"></path><path d="M349 381c1 0 2 0 3 1h0c2 0 2 1 4 1l3 1c1 0 2 1 2 1 1 0 2 0 3 1l1-1c2 0 3 1 5 2 1 1 2 1 4 1h0c1 1 2 2 2 3h-2c0 1 3 4 4 5l16 16c0 2 1 4 1 6-1-2-2-5-4-6h-1c-2-2-4-5-6-7-8-8-17-14-27-19-1-1-2-2-4-2l-4-3z" class="a"></path><path d="M317 369l7 3 1-2c8 2 15 7 23 7l10 3c2 0 4 0 5 1 2 1 4 1 6 2-2 0-3 0-4 1v1l-1 1c-1-1-2-1-3-1 0 0-1-1-2-1l-3-1c-2 0-2-1-4-1h0c-1-1-2-1-3-1l4 3c2 0 3 1 4 2l-4-1-1 1c-1-1-1-1-2-1v1l3 3c0 1 1 1 2 2l1 1c-2-1-3-1-4-1l-15-8c-1-2-6-3-7-4h-1l-6-3c-1 0-3-1-4-2 0 0 1-1 1-2-1-1-2-1-3-3z" class="J"></path><path d="M332 375l8 3c3 1 5 1 8 2l1 1 4 3c2 0 3 1 4 2l-4-1c-4-2-9-4-14-6l-2-2-5-2z" class="S"></path><path d="M317 369l7 3h0l8 3 5 2 2 2-3-1c-2 0-5-1-7-1v2l-6-3c-1 0-3-1-4-2 0 0 1-1 1-2-1-1-2-1-3-3z" class="M"></path><path d="M317 369l7 3h0l-1 1 2 2h0l-2 1c-1 0-3-1-4-2 0 0 1-1 1-2-1-1-2-1-3-3z" class="S"></path><path d="M329 379v-2c2 0 5 1 7 1l3 1c5 2 10 4 14 6l-1 1c-1-1-1-1-2-1v1l3 3c0 1 1 1 2 2l1 1c-2-1-3-1-4-1l-15-8c-1-2-6-3-7-4h-1z" class="Z"></path><path d="M369 383l1 1c2 1 4 2 6 2 3 0 5 2 7 3 1 0 2 1 3 2 5 3 9 8 12 13 1 0 2 2 2 2 1 2 2 4 2 7v2l1 1v4l-1 1-3-5h-3l-2-4-16-16c-1-1-4-4-4-5h2c0-1-1-2-2-3h0c-2 0-3 0-4-1-2-1-3-2-5-2v-1c1-1 2-1 4-1z" class="I"></path><path d="M387 399h0c-2-3-7-6-8-8l1-1 10 7c1 2 3 4 4 5-2 0-4-2-6-3h-1z" class="E"></path><path d="M387 399h1c2 1 4 3 6 3 2 3 4 6 5 10h1v3l-1 1c-3-7-7-12-12-17z" class="T"></path><path d="M369 383l1 1c2 1 4 2 6 2 3 0 5 2 7 3 1 0 2 1 3 2l-1 1c2 1 3 3 5 5h0l-10-7c-2-1-3-2-5-3l-1 1h0c-2 0-3 0-4-1-2-1-3-2-5-2v-1c1-1 2-1 4-1z" class="W"></path><path d="M390 397h0c-2-2-3-4-5-5l1-1c5 3 9 8 12 13 1 0 2 2 2 2 1 2 2 4 2 7v2l1 1v4l-1 1-3-5 1-1v-3h-1c-1-4-3-7-5-10-1-1-3-3-4-5z" class="U"></path><path d="M364 394c12 6 21 16 27 28v1c-1 2-1 3-1 5h1v1c0 2 0-1 0 2v2 3h2c0 2 0 4-1 6h0v1c-1 2-1 5-1 7v2l-2 2v4l-1-1-1 1-2 2v-2-4h0c0-2 0-3-1-4l-1 1c0 1-1 2-1 3l-1 1 1-5c0-4 0-10-1-14-1 1 0 3-1 4l1 1v3c-1 1-2 1-3 0-1-6-2-12-4-19-1-5-5-11-8-16-1 0-3-4-4-5 2-1 3 0 5 1-1-2-3-3-4-5h1c2 1 3 2 5 2-2-2-6-6-7-8h2z" class="T"></path><defs><linearGradient id="AY" x1="382.7" y1="446.214" x2="383.8" y2="439.286" xlink:href="#B"><stop offset="0" stop-color="#8a8681"></stop><stop offset="1" stop-color="#a2a2a0"></stop></linearGradient></defs><path fill="url(#AY)" d="M380 431l1 1h0l1 2c1 1 1 3 2 5 0-1 0-1-1-2v-3-3c1 3 2 6 2 9s1 6 0 9c-1 0-1-1-1-2-1 1-1 2-2 3 0-4 0-10-1-14l-1-5z"></path><path d="M364 400c2 1 3 2 5 2l2 2 7 10c2 3 6 11 6 13l2 13h-1c0-3-1-6-2-9 0-3-1-5-3-8-3-7-8-12-13-18-1-2-3-3-4-5h1z" class="I"></path><defs><linearGradient id="AZ" x1="370.227" y1="431.379" x2="379.097" y2="425.564" xlink:href="#B"><stop offset="0" stop-color="#b6b5b1"></stop><stop offset="1" stop-color="#e3e1e2"></stop></linearGradient></defs><path fill="url(#AZ)" d="M366 409c3 1 6 4 8 7s4 10 5 14c0 0 0 1 1 1l1 5c-1 1 0 3-1 4l1 1v3c-1 1-2 1-3 0-1-6-2-12-4-19-1-5-5-11-8-16z"></path><defs><linearGradient id="Aa" x1="386.842" y1="458.532" x2="384.158" y2="447.968" xlink:href="#B"><stop offset="0" stop-color="#605e59"></stop><stop offset="1" stop-color="#797876"></stop></linearGradient></defs><path fill="url(#Aa)" d="M384 427c2 2 3 3 4 6v2 2l2 7 1-8h2c0 2 0 4-1 6h0v1c-1 2-1 5-1 7v2l-2 2v4l-1-1-1 1-2 2v-2-4h0c0-2 0-3-1-4l-1 1c0 1-1 2-1 3l-1 1 1-5c1-1 1-2 2-3 0 1 0 2 1 2 1-3 0-6 0-9h1l-2-13z"></path><path d="M389 454c-1-1 0-3 0-4h1 1v2l-2 2z" class="R"></path><path d="M384 427c2 2 3 3 4 6v2 2 1c-1 1 0 5 0 7-1-1-1-4-2-5l-2-13z" class="D"></path><path d="M364 394c12 6 21 16 27 28v1c-1 2-1 3-1 5h1v1c0 2 0-1 0 2v2 3l-1 8-2-7v-2-2c-1-3-2-4-4-6 0-2-4-10-6-13l-7-10-2-2c-2-2-6-6-7-8h2z" class="G"></path><path d="M371 404c4 0 8 5 10 8 2 4 4 7 6 11 1 3 2 7 2 10v2h-1v-2c-1-3-2-4-4-6 0-2-4-10-6-13l-7-10z" class="P"></path><path d="M378 414c2 1 3 2 4 4h1c0 1 0 1 1 2 0 1 1 3 2 4l1-1c1 3 2 7 2 10v2h-1v-2c-1-3-2-4-4-6 0-2-4-10-6-13z" class="N"></path><path d="M330 324l7-1 1 1 9-1c4 0 8 0 11 1 1 1 1 2 1 3l8 1h6c2 1 4 0 6 1v1c-3 0-5 0-8 1l10 1c4 0 7 1 10 3 5 2 9 4 14 6 4 1 8 2 11 5l-1 1c1 1 4 5 4 6v1l3 3c3 5 6 11 9 16 3 6 5 13 6 20v8 10 5h1l1 6c0 3 0 6-1 9 0 2 0 3-1 4 0 1 0 2 1 3 0 1-1 1-1 2v4c-1 1-1 1-1 2l-1 3v2c-1 3-1 7-2 10 0 3 0 5-1 8v1h-2v4l-1-4h0l-1 1c-1 2-2 6-2 7l-2 5-2 3c0 1-2 2-3 3-4 3-10 6-16 5-3 0-6-2-7-4 0-1-1-2-1-2-2-2-2-3-3-6-1-1-1-1-1-2l2 2s0 1 1 2h1l-2-3c-1-2-2-3-1-5 0 0 2 4 3 4l1-1c-1-2-2-3-2-6 0 1 1 2 1 3 1 2 3 4 5 5 1 0 3 1 4 1l-2-2h-1c-1 0-4-2-4-4 2 2 3 3 6 3h0 4c4-1 7-4 9-8 4-6 6-14 8-21 5-16 9-30 10-46 0-4 0-10-1-14-3-11-7-22-14-31-10-13-22-18-38-20h-11l-17 1c-1 1-2 1-3 2h2v1h-3c1 0 2 0 3 1l-10 2h-5c-3 1-7-2-10-2h-1c0-1-1-1-2-1-2 0-8-1-8-2h6l1-1-10-2-3-1s0-1 1-1h3l5 1 1-3h-1-1c1-1 1-1 3-1 2-1 6-2 8-5-2 1-5 2-7 3h0-1c2-2 5-2 6-5 1-1 2-1 3-2z" class="Y"></path><path d="M381 332c4 0 7 1 10 3h-6c-5-1-10-1-15-1h-16v-1h7 13c3 0 5 0 7-1z" class="N"></path><path d="M319 337l1-3h-1-1c1-1 1-1 3-1 1 0 4 0 5 1 0 0 0 1 1 1 0 0 2 0 3-1h10l14-1v1l-28 2-7 1z" class="I"></path><path d="M329 328c1-1 1-1 2-1l1 1c0 1 0 0 1 1s3 1 4 2c-1 0-2 1-3 2 2 1 4 0 6 1h-10c-1 1-3 1-3 1-1 0-1-1-1-1-1-1-4-1-5-1 2-1 6-2 8-5z" class="E"></path><path d="M311 336h3l5 1 7-1-2 2c1 0 3 1 4 1 4 0 11 1 15 0-1 1-1 1-2 1h-2l1 1h4c-7 1-14 0-21-1l-10-2-3-1s0-1 1-1z" class="T"></path><path d="M426 455c2 3-2 10-3 13 0 1-2 3-2 5h1c-3 4-7 7-11 10h0c-1 0-2 1-3 1h-6c-2-1-4-3-6-5-1-2-2-3-2-6 0 1 1 2 1 3 1 2 3 4 5 5 1 0 3 1 4 1h0c2 0 5 0 7-1 9-5 12-17 15-26z" class="F"></path><path d="M337 331c2 1 7 0 10-1h12c3 1 9 2 12 1l10 1c-2 1-4 1-7 1h-13-7l-14 1c-2-1-4 0-6-1 1-1 2-2 3-2z" class="Y"></path><path d="M343 339c1-2 1-2 3-2h1 1c1-1 3-1 4-1 4-1 9 1 14 1 3 0 10-1 12 1h2l1 1h-11l-17 1-9 1h-4l-1-1h2c1 0 1 0 2-1z" class="P"></path><path d="M323 340c7 1 14 2 21 1l9-1c-1 1-2 1-3 2h2v1h-3c1 0 2 0 3 1l-10 2h-5c-3 1-7-2-10-2h-1c0-1-1-1-2-1-2 0-8-1-8-2h6l1-1z" class="B"></path><path d="M323 340c7 1 14 2 21 1l9-1c-1 1-2 1-3 2h2v1h-3c-10 1-18 0-27-2l1-1z" class="Q"></path><path d="M330 324l7-1 1 1 9-1c4 0 8 0 11 1 1 1 1 2 1 3l8 1h6c2 1 4 0 6 1v1c-3 0-5 0-8 1s-9 0-12-1h-12c-3 1-8 2-10 1-1-1-3-1-4-2s-1 0-1-1l-1-1c-1 0-1 0-2 1-2 1-5 2-7 3h0-1c2-2 5-2 6-5 1-1 2-1 3-2z" class="G"></path><path d="M359 330c-1-1-3-1-4-1-1 1-5 1-7 0h-13 1c8-3 16-3 23-2l8 1h6c2 1 4 0 6 1v1c-3 0-5 0-8 1s-9 0-12-1z" class="N"></path><path d="M437 416h1l1 6c0 3 0 6-1 9 0 2 0 3-1 4 0 1 0 2 1 3 0 1-1 1-1 2v4c-1 1-1 1-1 2l-1 3v2c-1 3-1 7-2 10 0 3 0 5-1 8v1h-2v4l-1-4h0l-1 1h-1c-1 1-2 3-3 3l1-3c-2 0-2 1-4 2 0-2 2-4 2-5 1-3 5-10 3-13 1-6 4-13 6-19l5-20z" class="I"></path><path d="M431 454c0 2-1 5 0 7h0c1-3 1-9 3-12l1 2c-1 3-1 7-2 10 0 3 0 5-1 8v1h-2v4l-1-4h0l-1 1h-1c-1 1-2 3-3 3l1-3c1 0 2-2 2-3s0-2 1-4v-1-1c1-1 1-1 1-2v-2c1-1 1-3 1-4h1z" class="R"></path><path d="M432 436c2 4-4 11-2 15 1-3 2-7 4-10v2l-1 5c-1 2-1 4-2 6h-1c0 1 0 3-1 4v2c0 1 0 1-1 2v1 1c-1 2-1 3-1 4s-1 3-2 3c-2 0-2 1-4 2 0-2 2-4 2-5 1-3 5-10 3-13 1-6 4-13 6-19z" class="a"></path><path d="M425 471l-1 3c1 0 2-2 3-3h1c-1 2-2 6-2 7l-2 5-2 3c0 1-2 2-3 3-4 3-10 6-16 5-3 0-6-2-7-4 0-1-1-2-1-2-2-2-2-3-3-6-1-1-1-1-1-2l2 2s0 1 1 2h1l-2-3c-1-2-2-3-1-5 0 0 2 4 3 4l1-1c2 2 4 4 6 5h6c1 0 2-1 3-1h0c4-3 8-6 11-10h-1c2-1 2-2 4-2z" class="E"></path><path d="M422 473l1 1v1l1 1c0 2-2 4-4 5h0c-2 0-6 5-8 6h0v-1h-1l-3 2h-1c1-2 5-3 4-5h0c4-3 8-6 11-10z" class="c"></path><path d="M425 471l-1 3c1 0 2-2 3-3h1c-1 2-2 6-2 7l-2 5-2 3c0 1-2 2-3 3l-1-2-3 1c-2 2-7 3-10 2-4 0-7-3-10-6l4 3c3 2 6 2 10 2 5-1 8-4 11-8h0c2-1 4-3 4-5l-1-1v-1l-1-1h-1c2-1 2-2 4-2z" class="W"></path><path d="M422 481c1-2 2-3 3-4l1 1-2 5c-1 0-2-2-2-2z" class="g"></path><path d="M422 481s1 2 2 2l-2 3c0 1-2 2-3 3l-1-2-3 1c3-2 5-5 7-7z" class="D"></path><path d="M396 490c0-1-1-2-1-2-2-2-2-3-3-6-1-1-1-1-1-2l2 2s0 1 1 2h1 0c3 3 6 6 10 6 3 1 8 0 10-2l3-1 1 2c-4 3-10 6-16 5-3 0-6-2-7-4z" class="B"></path><path d="M838 131c3-4 5-7 8-10l-8 27c-16 59-28 119-33 179 0 14-1 28 0 42 1 4 1 9 2 13-11-2-20-5-30-12-1-1-4-3-5-5s-1-8-2-11l-4-15c-11-41-36-80-77-96-26-10-55-9-82-8-6 9-10 19-10 30 4 7 11 13 19 15 4 1 8 1 12 0l1-1 1 1c3-1 5-3 7-5h0l1 1c-5 7-13 14-20 15-4 1-8 1-12 1l-1 1c4 0 8 1 12 0-3 2-6 2-9 3h-1c-1 1-3 1-4 1l-2 2h-1c3 3 10 4 14 2h1c6-1 8-3 12-7-4 7-8 9-15 11-4 1-9 1-14 1 1 7 1 12 6 17l6 3h0c3 1 8 0 11-2 2-1 3-2 3-4 1-2 0-5 0-7 0-1-1-1-1-2 0-2 0-1 1-2 1 1 2 1 2 3h1c0 1 1 2 1 4 2 2 2 5 1 8-2 7-8 12-14 15l-2 1c0 2 0 2-1 3h-9l1 1c4 1 11 1 15 0v1c-2 3-7 3-9 6 1 2 1 3 2 4 2 1 5 1 8 1 2 0 3-1 5 0 1 0 1 1 2 1-3 2-5 4-8 5l1 1c-1 1-2 1-3 2h0c-1 1-4 3-4 4l-2 2v-2h-1c-1 3-2 6-2 9 0 1-1 1-1 3l4 11c2 8 0 15-2 22l-3 11c-1 5-3 8-2 13-1 4-2 8-1 12 1 3 1 6 2 9l3 8c2 2 4 5 6 8 1 2 3 4 4 7 1 1 2 3 3 4l-1 2 4 3c1-1 3 0 4 0l4 3c2 2 6 4 8 6 10 4 20 6 31 8h2c2 1 5 1 6 3-27 3-55 12-72 34-13 17-17 37-14 57 2 14 7 27 16 38l2 1-3 1-13-4c0 36-2 74 3 111 6 36 21 74 47 100 10 10 22 19 34 26l16 8-1 1h-1c-2 0-5 0-7-1l-19-4-74-19c-15-4-30-9-44-16-13-6-25-14-35-23-4-4-7-8-10-11-26 30-61 44-99 54l-45 12-32 7c0-1 1-1 1-1 1-1 4-1 6-2l10-3c2-2 2-2 4-3 9-2 15-7 23-11l2 1v1l23-14 3-3c0-3 4-6 6-8 3-3 5-7 8-10l1 1c-2 2-4 3-5 6h1c3-3 6-9 10-10 4-3 6-10 10-14v-2-1c5-9 8-20 9-30 1-4 1-7 2-11v-1c0-2 0-5-1-7 0-3 0-4-3-6v-3c-2-4-3-9-3-14l-1-16c0-9 0-19 1-28l1 3c2-12 1-24-2-36l-3-14v-4c-1-1-1-2-1-3l1 1 1-1c0-1-1-3-1-5l3-10 2 1 1 2c1-3 0-7 1-10l1 4c1 2 1 3 3 4v5c2-3 0-7 1-10 1-1 1-4 1-6 0-5 0-11 1-17 0 2 1 2 1 3v5-1-2-2-1l1-5c0-12 1-26 0-38-3-13-13-26-24-34-3-2-8-4-11-7h0c7-1 13 2 18 5 14 8 23 25 24 41 0 6-1 13 0 18v1c3 4 6 6 10 8 5 1 8 0 12-2l1-1c4-4 6-9 7-14 0-8-3-18-9-24-3-5-9-10-14-13-2-1-3-2-5-3l-1 2-1 1h-1l-14-16c-2-2-5-4-7-5-6-4-9-10-10-17-1-3-1-7 0-10h1l-1-1 2-3 2-5c0-1 1-5 2-7l1-1h0l1 4v-4h2v-1c1-3 1-5 1-8 1-3 1-7 2-10v-2l1-3c0-1 0-1 1-2v-4c0-1 1-1 1-2-1-1-1-2-1-3 1-1 1-2 1-4 1-3 1-6 1-9l-1-6h-1v-5-10-8c-1-7-3-14-6-20-3-5-6-11-9-16l-3-3v-1c0-1-3-5-4-6l1-1c-3-3-7-4-11-5-5-2-9-4-14-6-3-2-6-3-10-3l-10-1c3-1 5-1 8-1v-1c-2-1-4 0-6-1h-6l-8-1c0-1 0-2-1-3-3-1-7-1-11-1l-9 1-1-1-7 1c5-4 10-7 17-9 1-1 2-1 4-1 1-1 3-1 4-1 4-1 8-1 12-1-4-2-7-1-11-1h-7c-1 0-1 1-2 0h-1l1-1c-1 0-5 1-6 2h-1c3-3 7-3 11-4h0 4c1-1 1-2 3-2h0v-1l-6 1h-1c2-1 4-1 6-2l-1-1h-2-1 0v-1l1-1c2 1 6-1 8-1l3-3 3-1c1-1 6-4 7-4 3-1 4-2 7-2 1-1 2-2 4-1l-1 1v1l-2 2v1c4-2 8-3 12-4l1 1c3-1 5-1 8-1 2-1 3-1 5-1 4 0 11 1 14-1 3 1 6 1 8 2h3c4 1 9 3 14 3-1-1-1-1-2-1s-1 0-2-1c1-1 3-1 5-2h0c1-1 1-1 2-1 4-1 7-3 11-4 12-4 23-7 34-14 19-13 29-38 29-61 0-13-2-27 1-40v-1l1-2v1c1 2 2 5 3 7 2 0 8 4 12 4 1 1 3 1 5 1l3 1h3c3-1 6 0 9-1l3 1c2 0 3 0 5 1l1-1h5 1l-1-1h-2l-2-1h-1c-1-1-3-1-5-2-1 0-2 0-3-1l-3-1-2-2c3 1 5 3 9 3h1c-1-1-2-1-4-2h0c2 0 6 1 7 1 2 1 4 1 5 1 4 0 6 1 8 4l1 1h2c2 0 4 0 6-1 1 0 5 1 7 2-1-1-3-3-4-3h-1c0-2-3-5-5-6v-1l7 5c0 1 0 1 1 2h1 1 1 1l1-1c4 2 10 2 15 2l1-1h0 0 4c-5-2-11-4-15-8-1 0-2-1-3-2v-4l1-1-1-1c2-1 3-2 4-3l2 2c-2 2-1 3-1 5 1 3 2 4 4 5l2 1v-1l10 4v1h-1 0c3 1 7 2 10 2l16 1c3-1 6 0 9 0 2-1 5-1 8-1 1 0 2-1 3-1l1-1h2c1 0 1 0 2 1h0l10-1-1 1h1c-1 1-2 1-3 2l2 1c3 1 5-1 8-1l5-1h4l2 2c5-1 11-1 16 0 3 0 7 1 9 0h1 14l6-1c5 0 9 1 14 0 2-1 4-1 7-2h-5c4-1 8-2 13-4v-1l2-1c0-1-1-1-1-1 4-3 10-4 15-5l10-4c1 0 2-1 4-1 2-2 5-4 7-6 3-2 6-4 8-7l1 1 1 1 12-18z"></path><path d="M502 671h0c2 0 3 1 4 3h0c-2 0-4 1-5 0 0-1 0-1 1-3z" class="X"></path><path d="M600 401v-1c0-1 1-1 3-2v6s0 1-1 2h0c0-2-1-3-2-5z" class="i"></path><path d="M535 736c1 1 2 1 2 3 0 0-1 1-2 1-1-1-3-1-4-1 1-2 2-3 4-3z" class="W"></path><path d="M526 284c1 0 1 0 1 1 2 2 5 7 5 10l-1-1c-2-3-4-6-5-10z" class="i"></path><path d="M604 341c3 0 6 0 9-1 0 2 0 2-1 3h-9c-1 1-1 1-2 0h0c2-1 4-1 6-1h0c-1 0-2-1-3-1z" class="H"></path><path d="M535 263c1 0 2 0 2 2 1 1 1 4 1 6l-1 5h0-1l-1-13z" class="J"></path><path d="M538 280c1 1 2 3 3 4s1 3 0 5v1l-2-4-1-2v-4z" class="i"></path><path d="M476 661c2 0 2 0 4 1l-4 4-2 1v-1c0-1 1-4 2-5z" class="R"></path><path d="M542 781c2-2 5-6 7-6-1 3-3 6-5 8h-1c0-1 0-1-1-2z" class="c"></path><path d="M517 607h0c2-1 2-3 3-5h0 1l3 8c-3-1-5-2-7-3z" class="V"></path><path d="M506 646c2 1 2 2 3 4 0 2-1 4-1 6 0 1 0 1-1 1 1-2 1-3 0-5l-2-2c0-2 0-2 1-4z" class="L"></path><path d="M519 295c3 0 7 0 9 2h0c-1 2-1 2-1 3l-4-3c-2 0-3 0-4-1v-1z" class="I"></path><path d="M535 262c1-2 1-2 1-3l1-1 2 2c0 2 1 3 0 5h0l-1 6c0-2 0-5-1-6 0-2-1-2-2-2v-1zm-13 546c2 1 3 1 5 1 1-1 1-1 1-2l3 3v2h0l-2-1c-1 1-1 2-1 3l-4-4h0l-2-2z" class="S"></path><path d="M530 653c2 1 5 2 7 3l1 1-2 2-1 1h-1l-4-7z" class="b"></path><path d="M542 366v3c1 3 2 10 1 13-1-1-2-2-2-4 1-2 0-5 0-7v-5h1z" class="H"></path><path d="M563 833h1c3 1 5 1 8-1l2-1c0 1 0 1-1 2-2 3-5 3-9 3l-3-2 2-1z" class="E"></path><path d="M526 787h1c1 0 2-1 3-2l1 9-4-3h0c0-1-1-2-2-3l1-1z" class="U"></path><path d="M534 756c2 0 5 0 7 2 0 1 1 2 1 4l-2-3h-2c-2-1-4 0-6 2v-2c0-2 1-2 2-3z" class="J"></path><path d="M519 783l2-1c1 1 1 3 2 4l1 1c0 1 1 1 1 1 1 1 2 2 2 3h-2c-1-2-2-3-3-3l-1 1h1l-1 1h0l-1 1h0c-1-2-1 0-1-2h1v-5l-1-1z" class="e"></path><path d="M513 284h1v1c0 1 0 3-1 4-1 2-2 3-4 3-1 0-1 0-1-1s0-1 1-2l4-5z" class="F"></path><path d="M513 620c0-2 0-3 1-4 2 2 4 3 7 4l-1 4c-3-1-5-2-7-4z" class="D"></path><path d="M537 714c1 1 1 3 1 5l3 6 2 7c-2-3-5-6-6-10-1-2-1-5 0-8z" class="Q"></path><path d="M539 383h0c-1-5-2-9-2-14l4 9c0 2 1 3 2 4v3l-3-3-1 1z" class="S"></path><path d="M541 437l1 1c-3 5-2 8-1 13h-1l-3-9c1-2 2-3 4-5z" class="R"></path><path d="M487 442c2-2 3-4 5-5l3-1h5l1 1-3 1c-2 0-6 2-8 4h-3z" class="H"></path><path d="M515 296c3-3 5-6 5-10 0-2 0-2 1-3 1 3 2 6 1 9 0 1-2 1-3 2v1c-1 0-2 1-4 1z" class="b"></path><path d="M531 809c1 4 3 7 6 11-1 0-2 0-3-1-2-1-6-3-6-5 0-1 0-2 1-3l2 1h0v-2-1z" class="Z"></path><path d="M506 646c1-1 0-3 0-4h3c1 0 2 2 3 3l-1 1-1 5h0l-1-1c-1-2-1-3-3-4z" class="R"></path><path d="M489 660c-2 1-4 0-6-1-4-2-5-6-6-10 2 3 4 6 8 8h1l-1 1h1c0 1 0 0 1 1s1 1 2 1z" class="G"></path><path d="M498 635c-1 0-2 0-3-1-2-1-4-1-4-2l1-2c2 0 3 1 4 1l-2-2 1-1 1 1h1c0 1 1 2 1 2v1 1h3l-1 1h-1l-1 1z" class="Z"></path><path d="M536 714c0-3 2-5 3-7v4c0 2 1 5 1 7s1 4 1 7l-3-6c0-2 0-4-1-5h-1z" class="K"></path><path d="M539 505h0l1 1c0 1-1 3-1 4-2 3-6 5-9 6h-1c3-4 7-7 10-11z" class="D"></path><path d="M521 620h0 5c1-1 4-4 5-3 0 1-1 2-1 3-3 3-5 3-8 4h-2l1-4z" class="G"></path><path d="M516 663l13 1c-1 1-3 1-5 1v-1h-2v1h1c-1 1-1 1-1 2h-4 0c-2 0-2 0-3 1h-2v-1l2-2c0-1 0-1 1-2z" class="M"></path><path d="M539 383l1-1 3 3 1 19c-1-2-1-5-1-8l-4-13z" class="J"></path><path d="M537 286h2l2 4c2 6 3 11 2 17v-3l-1 1h-1l-1 3v-5c1-2 2-8 1-10l-4-7z" class="D"></path><path d="M544 604c1 5 0 15-3 20 0 1-1 2-2 3-1 0-2 0-3 1l3-4v-1c1-1 2-2 2-4 2-5 2-10 3-15z" class="C"></path><path d="M600 299h-2c-1-3 0-7-1-10v-2h1v1h0c1 2 1 4 2 6 2 2 5 2 7 2-1 1-3 1-4 1l-2 2h-1z" class="H"></path><path d="M539 358l-2-5h1c0-6-2-10-4-15l5 5v6c1 3 1 6 2 9h-2z" class="a"></path><path d="M541 366c-3-6-8-10-12-14 2 1 8 3 9 6l1 1v-1h2c0 2 0 5 1 8h-1z" class="S"></path><path d="M597 374c-1-2-1-6-1-9 1-2 2-6 1-8h0l1-1c1 0 1 0 2 1v1 2c0 1-1 4-1 6-1 3-1 5-2 8z" class="H"></path><path d="M505 677h5v4 1h-4c-1 0-3 0-4-1 0-1-1-2 0-3l3-1z" class="P"></path><path d="M509 527l2-2c2-3 2-3 5-3-2 2-4 4-5 7v1c0 3 0 5 1 7h0v1l-2-2c0-1-1-1-2-3l1-6z" class="b"></path><path d="M540 573l1 3v1c-1 1-2 2-3 2-3 2-8 0-12-1 3-1 5 0 7-1 3 0 5-2 7-4h0z" class="N"></path><defs><linearGradient id="Ab" x1="539.358" y1="287.886" x2="536.142" y2="293.614" xlink:href="#B"><stop offset="0" stop-color="#7b7977"></stop><stop offset="1" stop-color="#908f8c"></stop></linearGradient></defs><path fill="url(#Ab)" d="M532 283c2 1 3 3 5 3l4 7c1 2 0 8-1 10v-2c-1-7-4-12-8-18z"></path><path d="M598 288c2 3 4 4 7 5h0c4 0 8 1 12 0-3 2-6 2-9 3h-1c-2 0-5 0-7-2-1-2-1-4-2-6z" class="O"></path><path d="M543 632h1c-1 2-1 4-2 5-2 2-3 3-6 3-1 1-3 1-5 1l1-2-1-1c1-1 2-1 3-2h2c0 1 0 1 1 1 0 0 1-1 2-1h1c1-1 2-2 3-4z" class="D"></path><path d="M547 828c3 0 5 1 8 3 0-1 0-1 1-1h1l4 4 3 2h-3c-4-1-12-4-14-8z" class="S"></path><path d="M519 460l1-1h-1c0-1-1-1-2-1l1-1c2 0 3 0 5 2 3 3 4 6 3 11v4h-1v-2h-1l-1-1c1-1 1-3 1-4 0-3-2-5-5-7z" class="K"></path><path d="M548 785h1c1 4 2 6 3 10-2 4-3 6-6 8l-1 2c-1-6 5-10 3-15v-5z" class="b"></path><path d="M526 729c1 4-4 7-3 11h-1v2c1 0 1-1 1-1v-1l1-1c2-2 4-5 7-6l2 2c-5 2-8 5-11 10-1-2-1-5-1-7l5-9z" class="H"></path><path d="M506 326l14 7 3 3c-1 0-1 0-2 1-2-2-5-5-7-5v1 1c1 1 2 2 2 3l-3-2c-1-2-6-5-6-7-1-1 0 0-1-2h0z" class="O"></path><path d="M513 674h0c-2-2-2-3-2-6h-1v-1c2-1 3-4 6-4-1 1-1 1-1 2l-2 2v1h2c1-1 1-1 3-1h0 4 0 1c2-1 3-1 5 0h-4c-2 1-3 1-5 2s-4 3-6 5z" class="H"></path><path d="M521 331l2 2 1-1c5 5 9 12 12 19l-13-15-3-3c0-1 0-1 1-2zm36 499v-1c-1-2 0-4 1-6s3-4 5-4c2-1 3-1 5 0v1c-1 1 0 0-1 0-2 1-4 1-6 3-1 1-2 4-1 6 0 2 1 3 3 4l-2 1-4-4z" class="P"></path><path d="M538 271l1-6h0c1 3 1 5 2 8h1v-1h1c-1 4-2 8-2 12-1-1-2-3-3-4 0-2-1-3-2-4h1 0l1-5z" class="F"></path><path d="M524 699c-1 3-2 5-3 6-3 3-4 7-6 9h0c-1-1-1-2-1-3v-3c0-1 0-2 1-3s1-1 1-2h0 1v3c1-1 1-2 2-3 0-1 1-1 1-1 1 0 1-2 2-2h1l1-1z" class="O"></path><path d="M487 499l1 1c3 0 6 0 8-2 3-2 3-7 3-10 3 2 6 4 7 7h-1l-1 1-3-4h0c0 1 0 1-1 2-1 3 0 5-4 7-1 0-3 1-4 1-1-2-2-1-4-1 0 0-1-1-1-2z" class="G"></path><path d="M550 806c2 0 3 0 5 1 1 1 1 2 1 4h0l-2-2h-5c-2 1-3 4-4 6s-1 3-1 5c-1 0-1 0-1-1-1-2-1-5 0-7 1-3 4-5 7-6z" class="g"></path><path d="M526 412h1c1 0 2 0 3 1 1 3 1 6 0 9-1 2-3 5-5 6-3 1-5 0-7-1-1-1-2-1-2-2l4 1c1 1 4 1 5 0 2-1 3-3 3-5 1-2 0-4-1-6-1-1-1-1-1-2v-1z" class="I"></path><path d="M509 334h0c-1-2-2-3-3-4l3 3c1 1 3 1 4 2l3 2c3 4 6 7 8 12l-1-2h-1l-1 1-9-10c0-1-3-3-3-4z" class="G"></path><path d="M522 467c-2-2-2-4-5-5s-7-1-9 0c-1 1-2 3-2 5h-1v-2c1-2 2-4 4-5 3-2 7-1 10 0 3 2 5 4 5 7 0 1 0 3-1 4 0-1 0-2-1-4z" class="N"></path><path d="M533 241c1-1 1-1 2-1h0c-1-2-2-3-1-5 2 3 4 6 8 6 3 1 6 0 9-1-3 2-5 5-8 5h-5l-4-4h-1z" class="c"></path><path d="M505 628c6 2 12 1 17 0 3-1 5-2 7-3h1v1c-4 3-8 5-13 6h-9l4-1h-2c-1-1-3-1-5-2h0v-1z" class="g"></path><path d="M543 202c-1 0-2-1-3-1-4-3-6-5-9-8v-1c4 1 11 3 14 6l-2 4z" class="Z"></path><path d="M485 288c3 0 6 1 8 1l1-1 11 9v1c-2 0-3-1-5-1-5-2-10-5-15-8v-1z" class="D"></path><path d="M516 522c3-3 10-5 14-5 3 0 6 1 9 0 1 1 1 1 1 3h-1-1c-3-1-11-1-14 0-6 3-10 5-13 10v-1c1-3 3-5 5-7zm17 119c2 0 4 0 6 1 4 3 4 6 6 11-1-1-3-3-3-4-5-4-7-5-12-3-2 0-2 1-3 0v-1c2-2 3-3 6-4z" class="L"></path><path d="M620 356c2 0 3-1 5 0 1 0 1 1 2 1-3 2-5 4-8 5-6 2-11 2-17-1l-1-1 1-1c1 1 3 2 5 2 5 0 13-1 17-5h-4z" class="d"></path><path d="M513 674c2-2 4-4 6-5 2 1 4 2 6 2h2l1 2c-1 1-2 1-3 1-3 0-6 0-9 2-1 1-2 3-4 3 0-1 1-3 1-5zm-1-231c8-1 14 1 20 6l-1 1c-2-1-3-2-5-2h0v1c2 1 2 2 3 3h-3l-4-3-1-1c-1-3-6-3-8-4l-1-1z" class="N"></path><path d="M520 547c-1 0-2-1-2-1-4-2-10-7-11-11s0-6 2-8l-1 6c1 2 2 2 2 3l2 2v-1l4 4 1-1c2 1 3 3 5 4 0 0-1 1-1 2h-1v1z" class="C"></path><path d="M517 540c2 1 3 3 5 4 0 0-1 1-1 2h-1l-2-2c-1-1-2-1-3-2l1-1 1-1z" class="K"></path><path d="M535 245c-2-1-3-2-4-4-1-1-1-2-2-3v-6c1-3 0-8 1-11 1-2 4-4 5-7v-1c1-2 3-3 6-4-5 6-8 10-9 17-1 2-1 5-1 7-1 1-1 3 0 5l1 1c0 1 1 1 1 2h1c0 2 0 2 1 3v1z" class="i"></path><path d="M527 671h0c3 0 5 2 7 5 1 1 2 3 2 4l-2 9-2-6c-1-4-3-7-7-9 1 0 2 0 3-1l-1-2z" class="I"></path><path d="M495 436c2-2 6-2 8-3h1c3-1 6-1 10-1 2-1 5-2 7-1s3 1 4 1c-7 3-14 2-22 4l-2 1-1-1h-5z" class="O"></path><path d="M519 295h0v1c1 1 2 1 4 1l-5 1c-2 0-4 2-5 4-1 1-1 2 0 3h2c4 1 9 3 10 7h0-1c-3-2-5-3-8-4-1-1-2-2-3-2l-1 4h0c-1 0-1 0-1-1-1-1-2-4-2-6 1-3 4-5 6-7 2 0 3-1 4-1z" class="J"></path><path d="M479 438l11-6c-1 1-1 2-2 3h0c-1 1-2 1-4 1v1 1c-4 2-8 5-11 8-4 3-8 9-10 14l-2-3c3-6 7-10 12-15l6-4z" class="K"></path><path d="M469 668c0-6 1-11 2-17l3-6c1 5 0 10-2 15-1 2-1 6-1 9l1 1v2c0 1 0 3 1 5v4 2c-2-4-3-10-4-15z" class="e"></path><path d="M477 672c2-2 6-4 9-5 3 0 6 2 8 4 3 3 4 6 5 10-3-4-5-6-10-6-1-1-1 0-2-1 2-1 2-1 3 0v-1c-2-1-5-2-7-3-2 1-3 1-5 2v1l-1-1z" class="L"></path><path d="M519 508l2-2c2-3 2-6 1-9 0-2-2-4-4-5-3-1-6-2-9-1h0-1v-1c2-1 5-2 8-1 3 0 6 1 7 4 2 3 3 6 2 9v2c-1 2-3 3-4 4h-2z" class="d"></path><path d="M600 401c1 2 2 3 2 5s0 8 1 10h0l-2 11-2 3c-2-6 0-11 0-16l1-13z" class="H"></path><defs><linearGradient id="Ac" x1="454.021" y1="503.011" x2="462.979" y2="501.489" xlink:href="#B"><stop offset="0" stop-color="#1e1c19"></stop><stop offset="1" stop-color="#393835"></stop></linearGradient></defs><path fill="url(#Ac)" d="M443 477l3 6c3 6 5 10 9 14h0c3 4 11 9 12 13-5-1-11-7-14-11-3-5-7-10-9-16l-1-1c-1-2 0-4 0-5z"></path><path d="M541 209h0c2-1 5-1 7-1l1 1c-1 0-1 1-2 1-5 1-9 4-11 8-1 2-2 3-2 5v1c-1 3-1 6-1 9l1 2h0c-1 2 0 3 1 5h0c-1 0-1 0-2 1 0-1-1-1-1-2l-1-1c-1-2-1-4 0-5 0-2 0-5 1-7 1-7 4-11 9-17z" class="M"></path><path d="M546 217h3c2 0 5 2 7 3l-1 1c-1-1-3-1-4-1-2-1-4 0-6 1-1 1-2 3-2 4-1 2 0 4 1 5 1 2 3 2 5 2s3-1 4-2h1 0v2l-2 2c-2 1-5 1-7 0-1 0-3-2-4-3-1-3-1-6 0-9 1-2 3-4 5-5z" class="R"></path><path d="M532 283c-2-2-3-4-5-5l-1-2c-1-2-5-5-6-7l9 6-8-7c8 3 13 9 17 16l1 2h-2c-2 0-3-2-5-3z" class="I"></path><path d="M526 586c-3-2-7-4-8-7 1-1 1-1 2-1 2 0 4 1 6 2 5 3 9 6 13 11 1 2 2 4 3 7h-1c-1-1-1-2-2-2-1-3-1-4-3-5-2 0-5-5-7-6-2 0-2 0-3 1z" class="U"></path><path d="M509 650l1 1h0l1-5 1-1c2 5 3 9 1 13s-4 7-8 8h0c0-2 1-7 2-9 1 0 1 0 1-1 0-2 1-4 1-6z" class="D"></path><path d="M530 174c2 0 8 4 12 4 1 1 3 1 5 1l3 1h3c3-1 6 0 9-1l3 1c2 0 3 0 5 1l1-1h5l1 1c1 0 2 0 3 1-1 0-2 0-4-1h-4c-1 1-2 0-3 0h-1c-1-1-3 0-5 0-1-1-7-1-9-1-1 1-4 1-5 1h-2c-2-1-15-1-17-1 0-2-1-4 0-6z" class="H"></path><defs><linearGradient id="Ad" x1="514.624" y1="599.86" x2="513.376" y2="612.14" xlink:href="#B"><stop offset="0" stop-color="#504f4c"></stop><stop offset="1" stop-color="#686864"></stop></linearGradient></defs><path fill="url(#Ad)" d="M513 620l-1-2c-3-6 1-12 3-18 2-3 3-6 4-9v4l2 7h-1 0c-1 2-1 4-3 5h0-1c-2 2-3 4-2 7v1 1c-1 1-1 2-1 4z"></path><path d="M441 430h1v1 1c0 2 0 4 1 5-1 8-3 18-1 26v10l-1-2c-2-5-2-11-2-17v-1h0v-4h1v-1c1-2 1-4 1-6h0c0-2 0-2-1-4v4 1c-1 1-1 1-1 2v1 2c0 1 0 2-1 4l-1 1c0-8 2-16 4-23z" class="F"></path><path d="M540 308l1-3h1l1-1v3c0 5-2 10-5 13-4 3-7 5-12 5-2-1-3-1-4-2 0-1 1-1 2-1h1v1h1l1-1 3-1v-1c2 0 2 0 3-1 5-3 6-6 7-11z" class="G"></path><path d="M524 472h1v2h1c-2 4-3 6-6 8-4 2-9 2-13 0l-6-3 1-1h1c1 1 0 1 1 1h2c2 1 5 1 7 0 5-1 8-3 11-7z" class="B"></path><path d="M524 472h1v2c-1 3-2 4-5 5-5 2-9 3-14 1l-2-1h2c2 1 5 1 7 0 5-1 8-3 11-7z" class="Z"></path><path d="M526 542h-1c2 1 5 1 7 0 5-2 8-8 10-12 1 2 2 5 3 7l-1 1h-1v-3c-2 1-3 3-4 4-3 5-9 6-14 5h-3c-2-1-3-3-5-4-2-3-3-5-2-8h0l1 2c1 3 2 4 4 5 1 1 3 1 5 1l-1 1 2 1z" class="E"></path><path d="M477 672l1 1v-1c2-1 3-1 5-2 2 1 5 2 7 3v1c-1-1-1-1-3 0 1 1 1 0 2 1-3 1-5 0-7 2-4 3-4 8-5 12-1-4-3-9-2-13 0-2 1-3 2-4z" class="F"></path><path d="M528 667c5 1 9 3 12 8-1 0-2 1-3 3 2 3 2 8 1 11v-1c0-2-1-3-1-4l-1-4c0-1-1-3-2-4-2-3-4-5-7-5h0-2c-2 0-4-1-6-2 2-1 3-1 5-2h4z" class="T"></path><defs><linearGradient id="Ae" x1="538.166" y1="673.398" x2="529.787" y2="671.089" xlink:href="#B"><stop offset="0" stop-color="#92918f"></stop><stop offset="1" stop-color="#b0b0af"></stop></linearGradient></defs><path fill="url(#Ae)" d="M528 667c5 1 9 3 12 8-1 0-2 1-3 3-2-4-3-7-7-8l-1-1v-1c-1 0-3 0-5-1h4z"></path><path d="M525 540h1c2-1 3-3 4-5s1-4 0-6c-1-1-2-3-4-3-2-1-5-1-8 0 0 1-1 1-1 1 0-1 0-1 1-1 2-2 4-4 7-4h1c2-1 3 0 5 1 2 2 3 4 4 7 0 4-1 7-4 9-1 1-3 2-5 3l-2-1 1-1z" class="N"></path><path d="M486 657c3-1 6-1 8-4 1-2 1-6 0-7-1-4-4-5-7-7 4 0 7 0 9 3s3 7 2 10c0 2-2 5-4 6-1 1-3 2-5 2-1 0-1 0-2-1s-1 0-1-1h-1l1-1z" class="B"></path><defs><linearGradient id="Af" x1="463.177" y1="510.119" x2="477.531" y2="505.661" xlink:href="#B"><stop offset="0" stop-color="#424242"></stop><stop offset="1" stop-color="#7a7671"></stop></linearGradient></defs><path fill="url(#Af)" d="M455 497c2 1 4 3 5 4l1-2c6 5 12 9 20 13-1 1-2 2-2 4h1s1 1 1 2c-5-2-10-5-14-8-1-4-9-9-12-13z"></path><path d="M499 790c1-2 2-5 3-7 0-1 0-2 1-2 0-1 1-1 1-1 0-1-1-1 0-2 3 2 3 8 5 11 1 3 4 7 6 10 2 4 5 6 7 9l2 2h0 0c-2-1-5-4-6-5h-1l-1-2-1-1c-1-1-2-1-2-2l-1-1c-2-2-5-5-5-8h0c-1-1-1-2-2-2v-1h0c0 1-1 5 0 6v2c1 1 1 2 2 3 0 1 0 1 1 2s1 1 1 2v1c-2-3-4-5-5-8 1-3 0-7 0-11h0c-2 2-1 5-3 8v-1c0-1 0-1 1-2v-1h-1c0 2-1 3-2 5-1 1-1 2-2 3h0c0-2 2-5 2-7z" class="O"></path><path d="M542 786c2-1 3-2 4-2v1l-1 1h2v1c-1 0-1 1-2 1s-2 1-3 1v1l-1 2h0c0 2-2 4-3 7v10c1 3 2 5 1 8v1c-4-4-6-9-5-14 0-7 3-14 8-18z" class="K"></path><path d="M497 453v5 1l2 1c-1 2-1 4-2 7h1v2c1 3 2 5 5 7 4 2 8 2 12 1 3-1 5-3 7-5v-5c1 2 1 3 1 4l1 1c-3 4-6 6-11 7-2 1-5 1-7 0h-2c-1 0 0 0-1-1h-1l-1 1v-1c-4-2-6-6-7-10-1-5 0-10 3-15z" class="L"></path><path d="M497 467c0-1-1-1-1-2 0-2 0-4 1-6l2 1c-1 2-1 4-2 7z" class="B"></path><path d="M492 502c1 0 3-1 4-1 4-2 3-4 4-7 1-1 1-1 1-2h0l3 4 1-1h1c1 3 0 6-1 8-1 1-2 1-2 2l-2 1c-1-1-2-1-2-1h-1v-1h-3c-2 1-5 1-7 0l-7-1-4-2 1-2c1 1 2 1 3 1h1c3 1 6 3 10 2z" class="J"></path><path d="M495 504c1-1 2-1 3-2 3-3 3-4 3-7v-1h1c0 2 1 3 1 4 0 2-1 3-2 5l-2 2c2-1 4-2 5-3s1-4 0-6l1-1h1c1 3 0 6-1 8-1 1-2 1-2 2l-2 1c-1-1-2-1-2-1h-1v-1h-3z" class="B"></path><path d="M520 546h1c9 4 19 5 22 15 1 4 2 11 0 14 0 1-1 2-2 2v-1l-1-3v-1-2c-2 1-4 1-5 1-2 0-2 0-2-1h3c2-1 3-2 4-3 0-2 0-4-1-6-3-7-13-11-19-14v-1z" class="G"></path><path d="M500 752l2 8 4 13v4h1c0 3 2 4 2 6 1 2 2 5 3 7 1 3 3 6 3 9-2-3-5-7-6-10-2-3-2-9-5-11-1 1 0 1 0 2 0 0-1 0-1 1-1 0-1 1-1 2-1 2-2 5-3 7-1-2 2-9 2-12v-1c1-2 1-6 0-7h0v2h-1 0c1-4 0-8-1-12l3 8c0-5-2-11-2-16z" class="a"></path><path d="M470 489v-3c0-1-1-1-1-2 0-2 0-9 2-11 0 4 0 8 1 11 1 0 1 0 2 1l1 1c0 1 1 3 1 3l4 4h2 0v2c1 2 2 3 5 4h0c0 1 1 2 1 2 2 0 3-1 4 1-4 1-7-1-10-2h-1c-1 0-2 0-3-1-4-3-7-6-8-10z" class="B"></path><path d="M470 489v-3c0-1-1-1-1-2 0-2 0-9 2-11 0 4 0 8 1 11s2 8 4 10 4 4 5 6c-1 0-2 0-3-1-4-3-7-6-8-10z" class="W"></path><path d="M502 738c1 7 4 14 4 21h1c1 4 1 7 2 11h-1v1 3h-1l-1-1h0l-4-13-2-8c0-1-1-2-2-4l1-3 1 3h2l-1-4 1-6z" class="E"></path><path d="M506 759h1c1 4 1 7 2 11h-1v1 3h-1l-1-1c2-3 0-11 0-14z" class="W"></path><path d="M499 745l1 3h2c1 4 2 9 0 12l-2-8c0-1-1-2-2-4l1-3z" class="d"></path><path d="M637 275l1 1c-5 7-13 14-20 15-4 1-8 1-12 1l-1 1h0c-3-1-5-2-7-5h0v-1c2 1 4 2 6 2 4-1 8 0 12 0l1-1c-5 0-10 0-14-3h1l4 1c4 1 10 2 13 0v-1c3 0 6-1 8-2a30.44 30.44 0 0 0 8-8z" class="V"></path><path d="M625 316h2v2c1 2 0 3 0 5v1c-1 2-2 4-3 5h-1c-1 0-1 0-2 1l-5 3h-1c-4 1-9 1-12-2-1 0-3-1-3-3 1 0 1 1 2 0v-1l7 2c-2-2-5-3-7-5l10 4c4 0 9-1 12-4 2-3 1-5 1-8z" class="P"></path><path d="M533 694l2-2v1c0 2-1 3-2 5l1 1h0c-1 1-1 1-1 2h1l-3 5-3 6-7 12c-4 7-7 15-9 23-1-1-1-2-2-4 2-8 5-15 9-22 5-9 11-17 14-27z" class="T"></path><path d="M481 512c3 1 8 2 12 2v1c-1 1-2 1-2 3 0 1 0 1 1 1 4 1 7 0 11 1h3c3-1 5-1 8-2l-2 2c-2 1-3 1-5 2-6 3-16 1-22-1-1-1-1-1-2-1 0-1 0-1 1-2h-3c0-1-1-2-1-2h-1c0-2 1-3 2-4z" class="e"></path><path d="M481 512c3 1 8 2 12 2v1c-1 1-2 1-2 3 0 1 0 1 1 1 4 1 7 0 11 1-7 1-13 0-19-2h-3c0-1-1-2-1-2h-1c0-2 1-3 2-4z" class="V"></path><defs><linearGradient id="Ag" x1="519.793" y1="793.608" x2="514.07" y2="795.406" xlink:href="#B"><stop offset="0" stop-color="#5d5d58"></stop><stop offset="1" stop-color="#787776"></stop></linearGradient></defs><path fill="url(#Ag)" d="M506 773l1 1h1v-3-1c1 4 1 7 4 10l1 2 1 3c0 2 1 5 2 7 1 1 2 4 3 5 2 4 6 7 9 10 0 1 0 1-1 2-2 0-3 0-5-1-2-3-5-5-7-9 0-3-2-6-3-9-1-2-2-5-3-7 0-2-2-3-2-6h-1v-4h0z"></path><path d="M527 300c0-1 0-1 1-3 4 4 6 7 6 13 1 2 0 4-2 6l-1 2c3-2 5-4 6-7 1-1 1-1 1-2 1 3 0 4-2 6l-3 3v1c-1 1-1 1-3 1v1l-3 1-1 1h-1v-1h-1c-1 0-2 0-2 1-2-1-2-2-2-4h1c1-1 1-1 2-1 3-1 6-3 6-6 1-5 0-8-2-12z" class="D"></path><defs><linearGradient id="Ah" x1="531.804" y1="181.922" x2="536.523" y2="184.407" xlink:href="#B"><stop offset="0" stop-color="#575552"></stop><stop offset="1" stop-color="#6e6b6a"></stop></linearGradient></defs><path fill="url(#Ah)" d="M567 189l-38-1v-6h29l8 1h-1c-4 0-10-1-14 1-1 0-2 0-2 1v1c2 2 3 1 5 1l1 1c3 0 9-1 12 1z"></path><path d="M624 313c0-1-1-1-1-2 0-2 0-1 1-2 1 1 2 1 2 3h1c0 1 1 2 1 4 2 2 2 5 1 8-2 7-8 12-14 15l-2 1c-3 1-6 1-9 1-2 0-3-1-5-1 4 0 7-1 10-1-1-1 0-1-1-1-4 0-6-1-10-3 3 1 6 1 9 2 5 0 6 1 10-1 2-1 5-2 5-4-2 1-3 2-5 3h-1l-1 1c-4 1-9 0-12-2l-2-1 6 1c4 1 6 1 9-1l5-3c1-1 1-1 2-1h1c1-1 2-3 3-5v-1c0-2 1-3 0-5v-2h-2 0v-3h-1z" class="B"></path><defs><linearGradient id="Ai" x1="546.402" y1="803.438" x2="531.403" y2="806.949" xlink:href="#B"><stop offset="0" stop-color="#9e9c9a"></stop><stop offset="1" stop-color="#d7d6d5"></stop></linearGradient></defs><path fill="url(#Ai)" d="M542 781c1 1 1 1 1 2h1l-2 3c-5 4-8 11-8 18-1 5 1 10 5 14 3 3 7 6 11 8 2 1 4 2 6 4-1 0-1 0-1 1-3-2-5-3-8-3-1 0-2-1-2-2-3-1-6-4-8-6-3-4-5-7-6-11v-5c0-10 4-17 11-23z"></path><path d="M471 473h0c2 3 2 6 4 8v1l3 3c3 2 5 3 8 3v-1l-5-2c-6-2-7-8-9-13 0-3 2-6 3-9 0-1 1-1 2-2v1c-2 5-3 10-1 15 2 3 5 6 8 7s6 0 9 0v1c0 1 0 1-1 2-2 2-9 4-12 4-1 0-2-1-4-2 0 0-1-2-1-3l-1-1c-1-1-1-1-2-1-1-3-1-7-1-11z" class="E"></path><defs><linearGradient id="Aj" x1="445.756" y1="483.596" x2="456.483" y2="476.265" xlink:href="#B"><stop offset="0" stop-color="#2e2e2b"></stop><stop offset="1" stop-color="#7d7977"></stop></linearGradient></defs><path fill="url(#Aj)" d="M443 455c0 2 1 3 2 4 0 5 1 10 2 14 3 10 7 19 14 26l-1 2c-1-1-3-3-5-4h0c-4-4-6-8-9-14l-3-6-1-4v-10h1v-8z"></path><defs><linearGradient id="Ak" x1="608.162" y1="268.777" x2="607.836" y2="281.327" xlink:href="#B"><stop offset="0" stop-color="#35302b"></stop><stop offset="1" stop-color="#686767"></stop></linearGradient></defs><path fill="url(#Ak)" d="M597 265c4 7 11 13 19 15 4 1 8 1 12 0l1-1 1 1c3-1 5-3 7-5h0a30.44 30.44 0 0 1-8 8c-2 1-5 2-8 2v1c-7-1-13-3-18-7l-2-2c-4-4-4-6-4-12z"></path><path d="M637 275h0a30.44 30.44 0 0 1-8 8c-2 1-5 2-8 2v1c-7-1-13-3-18-7l1-1h0c7 4 12 6 20 4l6-2c3-1 5-3 7-5z" class="P"></path><defs><linearGradient id="Al" x1="531.205" y1="458.335" x2="534.338" y2="453.093" xlink:href="#B"><stop offset="0" stop-color="#6a686b"></stop><stop offset="1" stop-color="#807f7c"></stop></linearGradient></defs><path fill="url(#Al)" d="M529 452c-1-1-1-2-3-3v-1h0c2 0 3 1 5 2l1-1c5 4 9 12 10 18 1 1 1 3 2 4v1c1 5 1 13-2 17 0 1-1 3-2 4-1-2 0-4 0-7s0-8-1-11c-1-9-4-16-10-23z"></path><path d="M542 489c-1-4-1-7-1-10-1-9-2-15-6-23l1-1c2 4 4 7 6 11v1h0c1 1 1 3 2 4v1c1 5 1 13-2 17z" class="H"></path><path d="M528 722h0c0-1 1-1 1-2l3-5c0-1 0-1 1-1 0-1 0-1 1-2 0-2 1-3 2-4l2-4 1-1v-2c1-2 1-2 1-3v-1c0-1 0-2 1-2v-2l1-2c0-2 1-2 1-2 0 6-2 12-4 18-1 2-3 4-3 7-3 5-7 10-10 15l-5 9c0 2 0 5 1 7-1 2-2 5-3 7h-1c-1 1-1 4-2 6l-2-4 2-5v-5c2-6 5-14 9-18 1-2 2-2 2-4h1z" class="D"></path><path d="M518 744l3-6c0 2 0 5 1 7-1 2-2 5-3 7h-1c-1 1-1 4-2 6l-2-4 2-5 2-5z" class="K"></path><path d="M516 749l2-5c1 3 0 5 0 8-1 1-1 4-2 6l-2-4 2-5z" class="C"></path><path d="M535 244c2 1 2 2 3 3s2 2 2 3l1 1 2 2v1c1 1 1 2 2 3l-1 1h0v-1h-1v4c1 3 1 8 0 11h0-1v1h-1c-1-3-1-5-2-8 1-2 0-3 0-5l-2-2-1 1c0 1 0 1-1 3l-2-5h0c-2-2-5-4-5-6l2 1h1c-1-1-1-2-1-3 1-1 0-1 0-2v-1h2 2 0l1-1v-1z" class="H"></path><path d="M533 257h1c1-2-1-3 0-5 0-1-1-2-1-3 2 1 2 2 3 3 0 3 2 6 3 8l-2-2-1 1c0 1 0 1-1 3l-2-5z" class="C"></path><path d="M536 252c5 6 6 12 6 20v1h-1c-1-3-1-5-2-8 1-2 0-3 0-5-1-2-3-5-3-8z" class="J"></path><defs><linearGradient id="Am" x1="522.693" y1="409.509" x2="526.509" y2="434.306" xlink:href="#B"><stop offset="0" stop-color="#aaaca8"></stop><stop offset="1" stop-color="#d8d6d5"></stop></linearGradient></defs><path fill="url(#Am)" d="M534 398c1-1 2-1 3-2 1 1 1 5 2 7 1 4 3 7 3 11 0 6-1 10-5 15-8 8-18 7-28 9-2-1-4-1-6-2 8-2 15-1 22-4 6-2 11-6 13-11 2-4 2-8 1-12-2-3-5-5-8-6l-1-1h1c0-2 1-3 3-4z"></path><path d="M531 402c0-2 1-3 3-4h2c2 2 2 3 2 6h0l-1-1c-1-1-5-1-6-1z" class="Z"></path><path d="M480 632l2-1c-3 4-6 9-8 14l-3 6c-1 6-2 11-2 17 1 5 2 11 4 15v1l-2 1c1 4 2 8 4 12 1 2 2 3 3 6-4-4-6-9-8-13v-3l-3-11-1-6v-13-1c1-4 2-8 4-11 1-2 3-3 4-4 2-4 4-6 6-9z" class="Q"></path><path d="M470 687c1-3-1-7-2-11v-13h1v4 1c1 5 2 11 4 15v1l-2 1c1 4 2 8 4 12 1 2 2 3 3 6-4-4-6-9-8-13v-3z" class="E"></path><defs><linearGradient id="An" x1="511.628" y1="779.798" x2="521.306" y2="770.732" xlink:href="#B"><stop offset="0" stop-color="#1a1b1a"></stop><stop offset="1" stop-color="#373432"></stop></linearGradient></defs><path fill="url(#An)" d="M516 758c1-2 1-5 2-6h1c-1 6-2 11-1 16l1 1 2 5c-1 2 1 5 2 8l3 4v1l-1 1s-1 0-1-1l-1-1c-1-1-1-3-2-4l-2 1 1 1v5h-1l-1-1c-1 1 0 2-1 3 0 1 1 1 1 2l2 3-1 1c-1-1-2-4-3-5-1-2-2-5-2-7l-1-3-1-2-1-7h1c1 1 1 1 2 1v-3l1-3c-1-3 0-6 0-9l1-1z"></path><path d="M516 785c1 2 0 4 0 7-1-2-2-5-2-7h2z" class="Z"></path><path d="M518 772c0 3 2 7 3 10l-2 1h0c-1-3-2-8-1-11z" class="O"></path><path d="M515 768v4l1 1c1 3-1 8 0 12h-2l-1-3v-1h2v-2c0-3 0-6-1-8l1-3z" class="M"></path><path d="M511 773h1c1 1 1 1 2 1v-3c1 2 1 5 1 8v2h-2v1l-1-2-1-7z" class="C"></path><path d="M516 758c1-2 1-5 2-6h1c-1 6-2 11-1 16l1 1 2 5c-1 2 1 5 2 8l3 4v1l-1 1s-1 0-1-1l-1-1c-1-1-1-3-2-4-1-3-3-7-3-10-1-2-1-5-2-7 0-2 1-4-1-6l1-1z" class="F"></path><path d="M501 437l2-1c2 1 4 1 6 2-11 2-23 8-29 18l-3 6h0v-1c-1 1-2 1-2 2-1 3-3 6-3 9 2 5 3 11 9 13l5 2v1c-3 0-5-1-8-3l-3-3v-1c-2-2-2-5-4-8h0c-2 2-2 9-2 11 0 1 1 1 1 2v3l-2-2c-2-9 0-18 4-25v-2c1-2 3-4 4-6 3-3 5-6 9-8 1 0 0 0 1-1 1 0 0 0 1-1 1 0 2-1 3-1v-1c2-2 6-4 8-4l3-1z" class="a"></path><path d="M490 442c2-2 6-4 8-4h3v1l-3 1c-9 4-17 12-22 21-1-2 0-2 0-3-1 1-2 1-2 2l-2 2v-2c1-2 3-4 4-6 3-3 5-6 9-8 1 0 0 0 1-1 1 0 0 0 1-1 1 0 2-1 3-1v-1z" class="M"></path><path d="M600 358l2 1h0l-1 1 1 1c6 3 11 3 17 1l1 1c-1 1-2 1-3 2h0c-1 1-4 3-4 4l-2 2v-2h-1c-1 3-2 6-2 9 0 1-1 1-1 3l4 11-1 1c0-1-1-3-1-4-2-2-3-5-4-8 0-2 0-4-1-6l-2 1v5c1 2 1 3 1 5-2-2-4-5-5-8l-1-4c1-3 1-5 2-8 0-2 1-5 1-6v-2z" class="C"></path><path d="M603 368c0-2 0-3 1-4 2 0 4 1 6 2h-1-2l-1 1h0l-1-1c-2 1-1 0-2 2z" class="b"></path><path d="M598 378l1-7c1-1 1-2 2-3 0 1 0 2-1 3-1 2 0 6 1 9 0 0 0 1 1 1 1 2 1 3 1 5-2-2-4-5-5-8z" class="K"></path><path d="M600 358l2 1h0l-1 1 1 1v3c0 2-1 3-1 4-1 1-1 2-2 3l-1 7-1-4c1-3 1-5 2-8 0-2 1-5 1-6v-2z" class="h"></path><path d="M610 366l7-1c-1 1-4 3-4 4l-2 2v-2h-1c-1 3-2 6-2 9 0 1-1 1-1 3l4 11-1 1c0-1-1-3-1-4-2-2-3-5-4-8 0-2 0-4-1-6l-2 1c0-3 1-6 1-8 1-2 0-1 2-2l1 1h0l1-1h2 1z" class="c"></path><path d="M603 368c1-2 0-1 2-2l1 1h0l1-1h2l-1 2c-1 1-2 2-2 4v8c-1 1 0 1-1 1 0-2 0-4-1-6l-2 1c0-3 1-6 1-8z" class="J"></path><defs><linearGradient id="Ao" x1="520.431" y1="600.958" x2="541.327" y2="609.56" xlink:href="#B"><stop offset="0" stop-color="#b0aeab"></stop><stop offset="1" stop-color="#d7d5d5"></stop></linearGradient></defs><path fill="url(#Ao)" d="M526 586c1-1 1-1 3-1 2 1 5 6 7 6 2 1 2 2 3 5 1 0 1 1 2 2h1c1 2 1 4 2 6-1 5-1 10-3 15 0 2-1 3-2 4v1l-3 4c-1 1-3 2-5 3 1-1 1-2 2-3l2-2-1-1c-1 1-2 1-3 1h-1v-1l2-2c4-5 6-10 5-16 0-8-5-16-11-21z"></path><path d="M536 591c2 1 2 2 3 5 1 0 1 1 2 2h1c1 2 1 4 2 6-1 5-1 10-3 15 0 2-1 3-2 4v1c-1-2-1-3 0-4l2-5c2-9-1-17-5-24z" class="W"></path><defs><linearGradient id="Ap" x1="534.203" y1="683.813" x2="542.422" y2="687.518" xlink:href="#B"><stop offset="0" stop-color="#8b8b86"></stop><stop offset="1" stop-color="#a9a7a7"></stop></linearGradient></defs><path fill="url(#Ap)" d="M540 675c3 4 3 8 3 14 0 0-1 0-1 2l-1 2v2c-1 0-1 1-1 2v1c0 1 0 1-1 3v2l-1 1-2 4c-1 1-2 2-2 4-1 1-1 1-1 2-1 0-1 0-1 1l-3 5c0 1-1 1-1 2h0-1l-2 2h-1l2-4-1-1c2-3 4-6 5-8v-1c1-1 1-2 1-4l3-5h-1c0-1 0-1 1-2h0l-1-1c1-2 2-3 2-5v-1l-2 2 1-5 2-9 1 4c0 1 1 2 1 4v1c1-3 1-8-1-11 1-2 2-3 3-3z"></path><path d="M536 680l1 4c0 1 1 2 1 4v1l-3 9c0 1-1 2-1 3h-1c0-1 0-1 1-2h0l-1-1c1-2 2-3 2-5v-1l-2 2 1-5 2-9z" class="d"></path><path d="M536 680l1 4c0 3-1 5-2 8l-2 2 1-5 2-9z" class="L"></path><defs><linearGradient id="Aq" x1="465.557" y1="464.061" x2="478.974" y2="471.684" xlink:href="#B"><stop offset="0" stop-color="#10100d"></stop><stop offset="1" stop-color="#3d3b3a"></stop></linearGradient></defs><path fill="url(#Aq)" d="M487 442h3v1c-1 0-2 1-3 1-1 1 0 1-1 1-1 1 0 1-1 1-4 2-6 5-9 8-1 2-3 4-4 6v2c-4 7-6 16-4 25l2 2c1 4 4 7 8 10l-1 2 4 2c-1 0-2 1-3 1v1c-4-1-7-4-10-7-1-4-2-7-3-11l-1-3c-1-5-2-11 0-15h0v-1h1c2-3 2-8 4-11h1c3-3 6-10 11-11 2-1 4-2 6-4z"></path><defs><linearGradient id="Ar" x1="468.931" y1="499.027" x2="478.89" y2="501.271" xlink:href="#B"><stop offset="0" stop-color="#81807a"></stop><stop offset="1" stop-color="#999898"></stop></linearGradient></defs><path fill="url(#Ar)" d="M465 487l-1-3c-1-5-2-11 0-15h0v-1h1c-1 8-1 19 4 26 2 3 5 5 8 7l4 2c-1 0-2 1-3 1v1c-4-1-7-4-10-7-1-4-2-7-3-11z"></path><path d="M473 684c1 5 3 9 5 13 5 8 11 15 16 24 3 5 6 11 8 17l-1 6 1 4h-2l-1-3-1 3-3-9-3-6c-1 0 0 0-1-1h0v-2-2-1h-1v-2c0-1 0-1-1-2l-1-3c-1-1-4-8-5-9-1-3-5-6-5-8-1-3-2-4-3-6-2-4-3-8-4-12l2-1z" class="X"></path><path d="M494 721c3 5 6 11 8 17l-1 6c-1-4-2-8-4-11-1-4-4-8-3-12z" class="N"></path><path d="M488 720c3 2 3 5 4 9 3 5 5 10 7 16l-1 3-3-9-3-6c-1 0 0 0-1-1h0v-2-2-1h-1v-2c0-1 0-1-1-2l-1-3z" class="P"></path><path d="M474 447c1-1 2-2 4-3-4 4-6 8-9 13-2 3-2 8-4 11h-1v1h0c-2 4-1 10 0 15l1 3c1 4 2 7 3 11 0-1-1-1-2-1h0l-4-4v-1c-1-1-1-1-1-2h0l-2-2h0l-1-1c-1-3-3-7-3-10l1-1c1-7 2-13 5-19l2 3c2-5 6-11 10-14l1 1z" class="S"></path><path d="M474 447c1-1 2-2 4-3-4 4-6 8-9 13-2 3-2 8-4 11h-1v1h0c-2 4-1 10 0 15l1 3-1 1-1-1c-2-4-2-12-1-16v-1h-1c0 2-1 3-1 5-1-5 1-11 3-15 2-5 6-11 10-14l1 1z" class="F"></path><path d="M463 460c2-5 6-11 10-14l1 1c-5 6-8 12-11 18-1 2-2 3-2 5s-1 3-1 5c-1-5 1-11 3-15z" class="H"></path><path d="M501 633h0l-4-4v-1l-2-3v-1c2 1 2 0 3 1 2 0 5 2 7 3v1h0c2 1 4 1 5 2h2l-4 1h9c5-1 9-3 13-6h1c1 0 2 0 3-1l1 1-2 2c-1 1-1 2-2 3 2-1 4-2 5-3s2-1 3-1l-1 2c-2 2-4 3-5 4-2 1-3 2-4 3-3 2-8 4-12 4-7 1-12-3-19-5l1-1h1l1-1z" class="F"></path><path d="M520 636h-3c-6 1-12-1-16-5v-1h2c1 1 3 1 5 2h9l-3 3v1h6z" class="T"></path><path d="M530 626h1c1 0 2 0 3-1l1 1-2 2c-1 1-1 2-2 3-3 3-7 5-11 5h-6v-1l3-3c5-1 9-3 13-6z" class="Y"></path><path d="M531 706c0 2 0 3-1 4v1c-1 2-3 5-5 8l1 1-2 4h1l2-2h1-1c0 2-1 2-2 4-4 4-7 12-9 18v5l-2 5 2 4-1 1c0 3-1 6 0 9l-1 3v3c-1 0-1 0-2-1h-1l1 7c-3-3-3-6-4-10h1c-1-4-1-7-2-11 0-5 2-11 3-16 1 2 1 3 2 4 2-8 5-16 9-23l7-12 3-6z" class="I"></path><path d="M513 748l1 1c1-1 1-4 2-5v5l-2 5-2 11-1-1c0-5 0-11 2-16z" class="G"></path><path d="M512 765l2-11 2 4-1 1c0 3-1 6 0 9l-1 3v3c-1 0-1 0-2-1h-1v-6c1-1 1-1 1-2z" class="J"></path><defs><linearGradient id="As" x1="506.607" y1="756.293" x2="512.742" y2="754.258" xlink:href="#B"><stop offset="0" stop-color="#979691"></stop><stop offset="1" stop-color="#aea9ad"></stop></linearGradient></defs><path fill="url(#As)" d="M507 759c0-5 2-11 3-16 1 2 1 3 2 4l-2 10c-1 3 0 7 0 10h0l-1-1v3 1c-1-4-1-7-2-11z"></path><path d="M525 719l1 1-2 4h1l2-2h1-1c0 2-1 2-2 4-4 4-7 12-9 18-1 1-1 4-2 5l-1-1c1-7 4-15 7-21l4-5c0-1 1-2 1-3h0z" class="d"></path><defs><linearGradient id="At" x1="463.789" y1="323.071" x2="503.93" y2="299.299" xlink:href="#B"><stop offset="0" stop-color="#bbb9b8"></stop><stop offset="1" stop-color="#f0eeee"></stop></linearGradient></defs><path fill="url(#At)" d="M449 289h0c1-1 1-1 2-1h1c2 1 4 1 6 2l13 2c13 2 22 8 32 16 4 3 8 6 12 10s7 9 9 14l-1 1-2-2-8-6h-2v-1-1h1c1 1 2 2 4 2 0-2-5-4-7-5v-1c-1-5-7-6-10-10 0-1-1-1-1-2h-2v-1c-3-3-9-6-13-8l-4-1c0-1-1-1-2-1-1-1-2 0-3 0-1 1-4-1-5-1-3-1-12-4-16-3v1h2-4 0-3c-1-1-1-1-2-1s-1 0-2-1c1-1 3-1 5-2z"></path><path d="M602 376l2-1c1 2 1 4 1 6 1 3 2 6 4 8 0 1 1 3 1 4l1-1c2 8 0 15-2 22l-3 11c-1 5-3 8-2 13-1 4-2 8-1 12 1 3 1 6 2 9l3 8v3c-1-2-1-3-3-4 0-1-1-2-2-4l-2-6-1-5c-1-2-2-3-2-5v-1c0-5 1-10 1-15l2-3 2-11 3-10 1-1c2-7 0-12-4-19 0-2 0-3-1-5v-5z" class="J"></path><path d="M600 451l1-12c0 2 0 6 1 8 0 3 0 6-1 9l-1-5z" class="C"></path><path d="M601 427v12l-1 12c-1-2-2-3-2-5v-1c0-5 1-10 1-15l2-3z" class="K"></path><path d="M602 376l2-1c1 2 1 4 1 6 1 3 2 6 4 8 0 1 1 3 1 4 1 6 0 9-1 14 0 2 0 4-1 6l-3 11-1 1c0-2 0-2 1-3v-2c1-3 3-8 3-12h-1c0-1 0-1-1-2l1-1c2-7 0-12-4-19 0-2 0-3-1-5v-5z" class="U"></path><path d="M533 735l2 1c-2 0-3 1-4 3 1 0 3 0 4 1-4 1-6 2-8 6-3 4-3 8-3 13 1 4 3 8 7 11l3 1c2 1 3 1 4 2-1 2-1 2-3 3l-1-1c-1-1-1-1-2-1l-1 10-1 1c-1 1-2 2-3 2h-1v-1l-3-4c-1-3-3-6-2-8l-2-5-1-1c-1-5 0-10 1-16 1-2 2-5 3-7 3-5 6-8 11-10z" class="J"></path><path d="M522 752c0 5-1 12 1 16h1l1 2c-1 1 0 0-1 0l-1 1c-1-1-2-2-2-3-2-4-1-11 1-16z" class="V"></path><path d="M531 739c1 0 3 0 4 1-4 1-6 2-8 6-3 4-3 8-3 13-1 0-1 0-1 1 0 2 1 3 1 5 2 2 3 4 5 6v1c-2-1-2-2-3-3l-1-1h-1-1c-2-4-1-11-1-16 2-5 5-9 9-13z" class="I"></path><path d="M524 768h1l1 1c1 1 1 2 3 3v-1c-2-2-3-4-5-6 0-2-1-3-1-5 0-1 0-1 1-1 1 4 3 8 7 11l3 1c2 1 3 1 4 2-1 2-1 2-3 3l-1-1c-1-1-1-1-2-1l-1 10-1 1c-1 1-2 2-3 2h-1v-1l-3-4c-1-3-3-6-2-8l-2-5h1l1 1c1 3 3 4 6 5h1v-1c-2-1-4-1-5-3l1-1c1 0 0 1 1 0l-1-2z" class="D"></path><path d="M521 774c2 1 4 2 7 2v1 1 2 1l1 2 2 1-1 1c-1 1-2 2-3 2h-1v-1l-3-4c-1-3-3-6-2-8z" class="K"></path><path d="M527 783c-1 0-1 0-2 1v-1-1c0-1-1-2-1-3 1-1 1-1 3-2l1 1v2l-1 3z" class="e"></path><path d="M528 780v1l1 2 2 1-1 1c-1 1-2 2-3 2h-1v-1l1-3 1-3z" class="L"></path><path d="M466 300c7 2 14 2 21 5 4 2 7 4 12 4 3 4 9 5 10 10v1c2 1 7 3 7 5-2 0-3-1-4-2h-1v1 1h2l8 6c-1 1-1 1-1 2l-14-7h0c1 2 0 1 1 2 0 2 5 5 6 7-1-1-3-1-4-2l-3-3c1 1 2 2 3 4h0c0 1 3 3 3 4v1 4 1l-3-4c-3-4-9-9-13-11-1-2-7-5-9-6-1-1-8-4-10-5h0l-10-4h3c1 1 3 1 4 1 1 1 2 0 4 0l1 1h1c2 1 3 1 5 0-3-2-7-3-10-3l2-1c-1 0-1-1-2-1l-1-1 2-1-12-4-6-3 13 2-5-3v-1z" class="N"></path><path d="M480 310c2 0 5 2 7 2l-4-4c1 0 2 0 3 1h1c1 1 2 1 2 1 3 2 8 5 11 5h1c1-1 7 4 8 5 2 1 7 3 7 5-2 0-3-1-4-2h-1v1 1h2l8 6c-1 1-1 1-1 2l-14-7c-2-2-4-3-7-4h-1l1 1h-1-1c1-1 1-1 0-1l1-1h1l-1-1c-5-3-10-6-16-8l-2-2h0z" class="E"></path><path d="M476 309h1c1 0 2 0 3 1h0l2 2c6 2 11 5 16 8l1 1h-1l-1 1c1 0 1 0 0 1h1 1l-1-1h1c3 1 5 2 7 4h0c1 2 0 1 1 2 0 2 5 5 6 7-1-1-3-1-4-2l-3-3c1 1 2 2 3 4h0c0 1 3 3 3 4v1 4 1l-3-4c-3-4-9-9-13-11-1-2-7-5-9-6-1-1-8-4-10-5h0l-10-4h3c1 1 3 1 4 1 1 1 2 0 4 0l1 1h1c2 1 3 1 5 0-3-2-7-3-10-3l2-1c-1 0-1-1-2-1l-1-1 2-1z" class="B"></path><path d="M509 334l-6-5-9-7c-1-1-3-2-3-3 2 0 5 3 6 4h1 1l-1-1h1c3 1 5 2 7 4h0c1 2 0 1 1 2 0 2 5 5 6 7-1-1-3-1-4-2l-3-3c1 1 2 2 3 4h0z" class="L"></path><path d="M423 288c3 1 6 1 8 2h3c4 1 9 3 14 3h3 0 4-2v-1c4-1 13 2 16 3 1 0 4 2 5 1 1 0 2-1 3 0 1 0 2 0 2 1l4 1c4 2 10 5 13 8v1h2c0 1 1 1 1 2-5 0-8-2-12-4-7-3-14-3-21-5v1l5 3-13-2 6 3h-5c-5-2-11-3-16-3l-12-3c-4-1-8-1-12-1-6-2-12 0-18-1-4 0-9 1-13 0v-1h3c0-1 1-1 2-1 1-1 1-2 2-2h1c3-1 5-2 8-2v-1c2-1 3-1 5-1 4 0 11 1 14-1z" class="B"></path><path d="M446 299c1-1 3 0 5 0l15 2 5 3-13-2-12-3z" class="Y"></path><path d="M455 293h-2v-1c4-1 13 2 16 3 1 0 4 2 5 1 3 1 7 2 9 5l-18-4c-3-1-6-1-9-2h-3c-1 0-1 0-1-1h4l-1-1z" class="P"></path><defs><linearGradient id="Au" x1="408.03" y1="298.528" x2="403.97" y2="290.472" xlink:href="#B"><stop offset="0" stop-color="#cecbcb"></stop><stop offset="1" stop-color="#f3f3f2"></stop></linearGradient></defs><path fill="url(#Au)" d="M391 296c3 0 6-1 10-2 1 0 3 0 5-1 3-1 6 0 9 0 2 0 7 0 9-1 2 0 7-1 8 0h3l1 1-1 1c-4-1-11-2-14-1l-1 1h-1c-4 0-7 0-10 1h-1l-2 1c-2 1-4 1-5 1-4 0-9 1-13 0v-1h3z"></path><path d="M466 300l-19-3h-1c-3-1-8-1-10-3h2c1 1 3 1 4 1 5 2 11 1 17 2 8 1 18 2 26 5 4 1 7 4 11 5h2c0 1 1 1 1 2-5 0-8-2-12-4-7-3-14-3-21-5z" class="Y"></path><path d="M401 297c1 0 3 0 5-1l2-1h1c3-1 6-1 10-1h1c3 1 7 0 10 1 4 0 7 2 10 3 2 1 4 1 6 1l12 3 6 3h-5c-5-2-11-3-16-3l-12-3c-4-1-8-1-12-1-6-2-12 0-18-1z" class="G"></path><defs><linearGradient id="Av" x1="506.902" y1="714.453" x2="502.982" y2="707.336" xlink:href="#B"><stop offset="0" stop-color="#4f4d4d"></stop><stop offset="1" stop-color="#6c6a65"></stop></linearGradient></defs><path fill="url(#Av)" d="M496 688c5-2 8-2 12-2h9c3 2 5 2 6 5 2 3 2 5 1 8l-1 1h-1c-1 0-1 2-2 2 0 0-1 0-1 1-1 1-1 2-2 3v-3h-1 0c0 1 0 1-1 2s-1 2-1 3v3l-3 8-3 14c-1 1-1 4-2 5l-3-9-1-3h0v-2l-7-16c-1-3-3-6-3-9-1-2-1-4 0-6s2-4 4-5z"></path><path d="M517 686c3 2 5 2 6 5h0-1c-1 1-1 3-1 4l-3-3c1-2 0-4-1-6z" class="B"></path><path d="M508 686h9c1 2 2 4 1 6-1-1-2-2-3-2-2-2-4-3-7-4z" class="Y"></path><path d="M505 717c3-3 0-7 2-10v6c1-2 1-8 2-9 1 1 0 4 1 6h0c1-1 1-3 1-4s1-1 1-2c0 1 0 3 1 4h1v3l-3 8c-1-2-1-4 0-6-2 1-1 5-3 6v-5h0c-1 4-1 7-1 11h-1v-3c0-2-1-3-1-5z" class="C"></path><path d="M506 722v3h1c0-4 0-7 1-11h0v5c2-1 1-5 3-6-1 2-1 4 0 6l-3 14c-1 1-1 4-2 5l-3-9-1-3c1 0 1 0 1-1v-2c1-4 0 1 0-2 1-1 1-1 1-2 0 1 0 3 1 5l1-2z" class="Z"></path><path d="M492 693v5c2 1 2 1 3 3 1 0 2-1 3-2l1-1c1 1 1 2 2 3l2 1h-2l4 15c0 2 1 3 1 5l-1 2c-1-2-1-4-1-5 0 1 0 1-1 2 0 3 1-2 0 2v2c0 1 0 1-1 1h0v-2l-7-16c-1-3-3-6-3-9-1-2-1-4 0-6z" class="W"></path><path d="M502 724c2-2-2-6-2-9 0-4-1-7 0-11 0-1 0-1 1-2l4 15c0 2 1 3 1 5l-1 2c-1-2-1-4-1-5 0 1 0 1-1 2 0 3 1-2 0 2v2c0 1 0 1-1 1h0v-2z" class="K"></path><path d="M496 688c5-2 8-2 12-2 3 1 5 2 7 4 1 1 2 2 3 4v1l-2-1c-1 1-1 2-1 3l-3-2v3h0l-2-1v1c0 1-1 3-2 4v-1l-1-1-1 1c-1 1-2 1-2 1h-1l-2-1c-1-1-1-2-2-3l-1 1c-1 1-2 2-3 2-1-2-1-2-3-3v-5c1-2 2-4 4-5z" class="P"></path><path d="M492 693c1-2 2-4 4-5v1c0 1 0 2-1 3 0 2 0 2 1 3 4 1 5-1 8-2 2 0 6 0 7 1l1 1h0 0v3h0l-2-1v1c0 1-1 3-2 4v-1l-1-1-1 1c-1 1-2 1-2 1h-1l-2-1c-1-1-1-2-2-3l-1 1c-1 1-2 2-3 2-1-2-1-2-3-3v-5z" class="f"></path><path d="M497 453h0c3-6 9-9 15-10l1 1c2 1 7 1 8 4l1 1 4 3h3c6 7 9 14 10 23 1 3 1 8 1 11s-1 5 0 7c-4 11-11 18-22 23 0 1-4 2-4 2-3 1-5 1-8 2h-3c-4-1-7 0-11-1-1 0-1 0-1-1 0-2 1-2 2-3v-1h1c2-1 4-2 6-2 1-1 3-2 4-3h2c2-1 6-1 8-1h5 0 2c1-1 3-2 4-4 7-8 10-17 9-28 0-7-2-15-8-20h-1c-2 0-4-1-5-2-3-1-5-1-7-1-3 0-5 1-8 3-2 1-3 2-5 3l-1 1-2-1v-1-5z" class="P"></path><path d="M499 454c1-3 3-5 6-6 5-3 11-2 16 0l1 1h-1l-1-1c-4 0-9 0-12 1h0c-3 1-6 4-8 7l-1 2c-1-2 0-2 0-4z" class="X"></path><path d="M500 459c0-2 1-3 3-4 3-3 8-4 13-4h1c3 1 6 2 9 5h-1c-2 0-4-1-5-2-3-1-5-1-7-1-3 0-5 1-8 3-2 1-3 2-5 3z" class="M"></path><path d="M497 453h0c3-6 9-9 15-10l1 1c2 1 7 1 8 4-5-2-11-3-16 0-3 1-5 3-6 6l-2 4v-5z" class="G"></path><path d="M506 509c2-1 6-1 8-1h5c-5 5-14 7-20 7h-6v-1h1c2-1 4-2 6-2 1-1 3-2 4-3h2z" class="O"></path><path d="M539 475c1 3 1 8 1 11s-1 5 0 7c-4 11-11 18-22 23 0 1-4 2-4 2-3 1-5 1-8 2 3-1 4-2 6-3 3-2 6-3 9-5 8-5 15-14 17-24v-1c1-4 1-8 1-12z" class="V"></path><defs><linearGradient id="Aw" x1="445.53" y1="585.774" x2="496.776" y2="536.169" xlink:href="#B"><stop offset="0" stop-color="#b6b4b2"></stop><stop offset="1" stop-color="#e2e1e0"></stop></linearGradient></defs><path fill="url(#Aw)" d="M436 481h1c0 2 0 3 1 4v1l3 6c2 4 5 8 8 12l1-1c-1 0-1-1-2-2 0-1 0 0-1-1v-1c-2-3-5-7-6-10l-1-4-1-1v-2c-1-1 0-1-1-1v-1l1-1c5 17 14 31 30 39 6 3 12 6 17 9 12 7 21 18 25 31 5 15 2 30-5 45-6 11-16 18-24 28l-2 1c1-3 3-4 5-6h-1c0-3 3-5 4-7s4-5 6-7l4-5-1-1v1c-1 0-2 1-2 1h-1c-1 2-2 2-3 3l6-8c0-2 1-4 1-7l1-1h0c1-2 1-3 2-5 1-4 1-8 1-12v-1c1-2 1-5 1-7-1-1-1-2-1-3l1-1h1s0 1 1 2v-3l1 1c1-1 1-2 1-3l-2-5c0-6-4-10-7-15a30.44 30.44 0 0 0-8-8v-1c-2 0-2-1-4-2l-5-3c-1-1-2-1-2-2-2-2-5-3-7-4l-7-4-4-2v-1l-4-2c-3-4-7-8-11-12-4-6-8-13-10-21z"></path><path d="M479 527c11 4 23 15 28 26 4 9 6 24 3 33-1 2-2 3-3 5-2 6-5 12-9 17-1 2-2 4-3 4h-1l4-5-1-1v1c-1 0-2 1-2 1h-1c-1 2-2 2-3 3l6-8c0-2 1-4 1-7l1-1h0c1-2 1-3 2-5 1-4 1-8 1-12v-1c1-2 1-5 1-7-1-1-1-2-1-3l1-1h1s0 1 1 2v-3l1 1c1-1 1-2 1-3l-2-5c0-6-4-10-7-15a30.44 30.44 0 0 0-8-8v-1c-2 0-2-1-4-2l-5-3c-1-1-2-1-2-2z" class="I"></path><path d="M507 563l2 6c1 3 1 11 0 14v-1h-1v-1c-1-2 0-5-1-7l-1 1v-1c0-2-1-4-1-6v-3l1 1c1-1 1-2 1-3z" class="B"></path><path d="M505 565l1 1c3 5 2 9 2 15-1-2 0-5-1-7l-1 1v-1c0-2-1-4-1-6v-3z" class="L"></path><defs><linearGradient id="Ax" x1="500.135" y1="587.109" x2="506.931" y2="592.443" xlink:href="#B"><stop offset="0" stop-color="#bfbdbf"></stop><stop offset="1" stop-color="#e2e2df"></stop></linearGradient></defs><path fill="url(#Ax)" d="M506 574v1l1-1c1 2 0 5 1 7v1h1v1c-1 6-5 14-8 20-1 1-2 3-3 4h0l-1-1v1c-1 0-2 1-2 1h-1c-1 2-2 2-3 3l6-8h0c3-2 4-5 5-8 2-7 4-13 4-21z"></path><path d="M504 566s0 1 1 2c0 2 1 4 1 6 0 8-2 14-4 21-1 3-2 6-5 8h0c0-2 1-4 1-7l1-1h0c1-2 1-3 2-5 1-4 1-8 1-12v-1c1-2 1-5 1-7-1-1-1-2-1-3l1-1h1z" class="U"></path><path d="M504 566s0 1 1 2c0 2 1 4 1 6 0 8-2 14-4 21-1-1 0-5 0-7 1-7 3-15 1-22h1z" class="J"></path><path d="M455 392l1-1c1 8 4 16 4 24 1 3 0 7 1 10h0 1c0 2 0 3-1 4v2l1 1c-1 2-1 4-1 6 1-1 1-2 2-3v-1c5-1 5-5 8-8 0-1 1-1 1-2h0 1v2c1-1 2-2 2-3l2 3-1 1h0l-3 3c1-1 2-1 4-1v1c-1 0-1 0-1 2-2 1-2 1-3 4l-3 3h2v2h1v1h0c-5 5-9 9-12 15s-4 12-5 19l-1 1c0 3 2 7 3 10l1 1h0l2 2h0c0 1 0 1 1 2v1l4 4h0c1 0 2 0 2 1 3 3 6 6 10 7v-1c1 0 2-1 3-1l7 1c2 1 5 1 7 0h3v1h1s1 0 2 1l2-1h0c1 1 2 1 3 1 0 1 0 1-1 2l1 1h-2c-1 1-3 2-4 3-2 0-4 1-6 2h-1c-4 0-9-1-12-2-8-4-14-8-20-13-7-7-11-16-14-26-1-4-2-9-2-14l1-7 2-14 1-5c1-3 2-7 2-10 0-8 0-16-1-24 1 0 1 1 2 2h1v1c1 1 1 3 3 4h0l1 1v1c0-5-1-11-2-16z" class="H"></path><path d="M451 442h2l-1 9c0-1-1-1-1-1-1-1-1-3-1-5l1-3z" class="W"></path><path d="M461 431l1 1c-1 2-1 4-1 6-1 0-1 1-1 2s-1 1-1 2 0 1-1 2c0 1 0 2 1 3-1 1-2 3-3 4l-1 4-1 1c0 2 1 6-1 8h-1c0-4 1-9 1-12 0-1 0-2 1-3 0 1 0 1 1 2h0c0-4 2-7 3-10 1-1 2-3 2-5l1-5z" class="K"></path><path d="M456 431c0 2 0 5 1 7l2-7c0 1 0 4 1 5 0 2-1 4-2 5-1 3-3 6-3 10h0c-1-1-1-1-1-2v-7-1c-1-3 1-7 2-10z" class="J"></path><defs><linearGradient id="Ay" x1="483.708" y1="500.487" x2="492.792" y2="510.013" xlink:href="#B"><stop offset="0" stop-color="#a3a2a3"></stop><stop offset="1" stop-color="#d5d4d1"></stop></linearGradient></defs><path fill="url(#Ay)" d="M481 503l7 1c2 1 5 1 7 0h3v1h1s1 0 2 1h-1c-8 4-14 2-22-1v-1c1 0 2-1 3-1z"></path><path d="M455 392l1-1c1 8 4 16 4 24 1 3 0 7 1 10h0 1c0 2 0 3-1 4v2l-1 5c-1-1-1-4-1-5l-2 7c-1-2-1-5-1-7-1-2 0-5 0-7h0c0-1 0-3 1-4 1-4 1-8 1-11l-1-1c0-5-1-11-2-16z" class="D"></path><path d="M458 409c1 5 0 12 0 17v1 1h-1v-1c0-1 0-2-1-3h0c0-1 0-3 1-4 1-4 1-8 1-11z" class="G"></path><defs><linearGradient id="Az" x1="468.352" y1="462.708" x2="459.039" y2="445.085" xlink:href="#B"><stop offset="0" stop-color="#bab9bb"></stop><stop offset="1" stop-color="#fbf8f7"></stop></linearGradient></defs><path fill="url(#Az)" d="M470 439h2v2h1v1h0c-5 5-9 9-12 15s-4 12-5 19l-1 1v-3c-1-13 5-23 13-33l2-2z"></path><path d="M461 438c1-1 1-2 2-3v-1c5-1 5-5 8-8 0-1 1-1 1-2h0 1v2c1-1 2-2 2-3l2 3-1 1h0l-3 3c1-1 2-1 4-1v1c-1 0-1 0-1 2-2 1-2 1-3 4l-3 3-2 2v-4h0l-1-1c-3 4-6 7-8 11-1-1-1-2-1-3 1-1 1-1 1-2s1-1 1-2 0-2 1-2z" class="M"></path><path d="M473 430c1-1 2-1 4-1v1c-1 0-1 0-1 2-2 1-2 1-3 4l-3 3-2 2v-4h0l-1-1 6-6zm-25 8c2 2 2 5 2 7h0c0 2 0 4 1 5 0 0 1 0 1 1l-2 10c1 4 0 9 1 14 1 3 3 8 4 11 2 3 4 6 5 9v1c-2-4-4-7-6-11-1-2-2-5-3-6-1-2-1-4-2-5h0v2-1h-1v-2h-1 0c-1-4-2-9-2-14l1-7 2-14z" class="e"></path><path d="M450 445h0c0 2 0 4 1 5 0 0 1 0 1 1l-2 10c0 2 0 4-1 6 0-7-1-15 1-22z" class="J"></path><defs><linearGradient id="BA" x1="445.355" y1="425.429" x2="461.728" y2="416.479" xlink:href="#B"><stop offset="0" stop-color="#a5a5a2"></stop><stop offset="1" stop-color="#e5e2e3"></stop></linearGradient></defs><path fill="url(#BA)" d="M450 399c1 0 1 1 2 2h1v1c1 1 1 3 3 4h0l1 1v1l1 1c0 3 0 7-1 11-1 1-1 3-1 4h0c0 2-1 5 0 7-1 3-3 7-2 10l-1 1h-2l-1 3h0c0-2 0-5-2-7l1-5c1-3 2-7 2-10 0-8 0-16-1-24z"></path><path d="M451 423l1 1v-1l1 1-2 18-1 3h0c0-2 0-5-2-7l1-5c1-3 2-7 2-10z" class="b"></path><path d="M450 399c1 0 1 1 2 2 1 7 2 15 1 23h0l-1-1v1l-1-1c0-8 0-16-1-24z" class="c"></path><defs><linearGradient id="BB" x1="460.323" y1="499.841" x2="415.479" y2="346.214" xlink:href="#B"><stop offset="0" stop-color="#bab8b7"></stop><stop offset="1" stop-color="#edebeb"></stop></linearGradient></defs><path fill="url(#BB)" d="M367 328v-1c2-1 4-1 6 0h6c15 4 30 9 43 19 3 2 6 5 9 7l-1 1 1 1c0 1 0 1 1 2 2 4 6 8 8 12 3 4 4 8 6 12l2 4c1 2 2 4 2 6l1 4c1 1 1 4 1 6-1-1-1-2-2-2 1 8 1 16 1 24 0 3-1 7-2 10l-1 5-2 14-1 7c-1-1-2-2-2-4v8h-1c-2-8 0-18 1-26-1-1-1-3-1-5v-1-1h-1c-2 7-4 15-4 23-1 8 0 18 2 26l-1 1v1c1 0 0 0 1 1v2l1 1 1 4c1 3 4 7 6 10v1c1 1 1 0 1 1 1 1 1 2 2 2l-1 1c-3-4-6-8-8-12l-3-6v-1c-1-1-1-2-1-4h-1c-3-5-3-14-3-20 1-3 1-7 2-10v-2l1-3c0-1 0-1 1-2v-4c0-1 1-1 1-2-1-1-1-2-1-3 1-1 1-2 1-4 1-3 1-6 1-9l-1-6h-1v-5-10-8c-1-7-3-14-6-20-3-5-6-11-9-16l-3-3v-1c0-1-3-5-4-6l1-1c-3-3-7-4-11-5-5-2-9-4-14-6-3-2-6-3-10-3l-10-1c3-1 5-1 8-1v-1c-2-1-4 0-6-1h-6z"></path><path d="M419 353c10 12 17 25 19 41 1 3 1 6 1 9 1 4 2 8 2 13v1c0 1-1 3-1 5-1 2 0 4-1 7v1c0 1-1 3-2 5 1-1 1-2 1-4 1-3 1-6 1-9l-1-6h-1v-5-10-8c-1-7-3-14-6-20-3-5-6-11-9-16l-3-3v-1z" class="G"></path><path d="M437 401c3 5 3 16 2 21l-1-6h-1v-5-10z" class="L"></path><defs><linearGradient id="BC" x1="439.504" y1="440.325" x2="448.996" y2="417.675" xlink:href="#B"><stop offset="0" stop-color="#7f7e7d"></stop><stop offset="1" stop-color="#a6a4a2"></stop></linearGradient></defs><path fill="url(#BC)" d="M444 414h0c-1-2-1-4-1-6v-10l1-1c1 4 0 9 3 13 0 5 1 10 0 15v5 3h0c0 2 0 3-1 5v2h1v-1-2l1-2c0-1 0-1 1-2l-1 5-2 14-1 7c-1-1-2-2-2-4v8h-1c-2-8 0-18 1-26-1-1-1-3-1-5v-1-1h-1c0-5 2-11 3-16z"></path><path d="M443 455c0-3 0-6 1-9 0 2 0 5 1 7v-1h1l-1 7c-1-1-2-2-2-4z" class="W"></path><path d="M444 414h0c1 7 0 16-1 23-1-1-1-3-1-5v-1-1h-1c0-5 2-11 3-16z" class="C"></path><path d="M447 433h0c0 2 0 3-1 5v2h1v-1-2l1-2c0-1 0-1 1-2l-1 5-2 14h-1v1c-1-2-1-5-1-7l1-9c1-2 1-3 2-4z" class="I"></path><path d="M367 328v-1c2-1 4-1 6 0h6c15 4 30 9 43 19 3 2 6 5 9 7l-1 1 1 1c0 1 0 1 1 2 2 4 6 8 8 12 3 4 4 8 6 12l2 4c1 2 2 4 2 6l1 4c1 1 1 4 1 6-1-1-1-2-2-2-1-2-2-7-2-9-2-6-5-12-8-18l-1 1c1 2 2 5 2 7-1-1-3-5-3-6-4-7-8-14-13-20-3-3-6-5-9-8s-7-4-11-5c-5-2-9-4-14-6-3-2-6-3-10-3l-10-1c3-1 5-1 8-1v-1c-2-1-4 0-6-1h-6z" class="E"></path><path d="M438 374v-1-1c-1-1-1-3-2-4-1-2-2-3-2-5 1 1 3 3 4 5l2 4-1 1c1 2 2 5 2 7-1-1-3-5-3-6z" class="d"></path><path d="M375 292c3-1 4-2 7-2 1-1 2-2 4-1l-1 1v1l-2 2v1c4-2 8-3 12-4l1 1c3-1 5-1 8-1v1c-3 0-5 1-8 2h-1c-1 0-1 1-2 2-1 0-2 0-2 1h-3v1c4 1 9 0 13 0 6 1 12-1 18 1 4 0 8 0 12 1l12 3c5 0 11 1 16 3h5l12 4-2 1 1 1c1 0 1 1 2 1l-2 1c3 0 7 1 10 3-2 1-3 1-5 0h-1l-1-1c-2 0-3 1-4 0-1 0-3 0-4-1h-3l10 4h0v1l3 3c2 2 6 8 9 9v1l-2 2c1 2 2 4 4 6h-2l-8-8h-2c-3-3-7-6-11-8-1-1-3-2-5-3s-4-1-5-4c-4 0-8-2-11-3-2 0-5-1-6-1-3 0-10 0-13-1l-23-2-3 1h-2l-17-1c-2-1-3-1-5-1v1h1l-1 1h-5l-4-1c-2 0-2 0-3-1l-15-1h0 4c1-1 1-2 3-2h0v-1l-6 1h-1c2-1 4-1 6-2l-1-1h-2-1 0v-1l1-1c2 1 6-1 8-1l3-3 3-1c1-1 6-4 7-4z" class="P"></path><path d="M414 308l-26-1h0c8-1 17-1 26 0v1z" class="T"></path><path d="M405 310c1 0 2-1 3-1 2 0 5 0 6 1 3 0 8-1 11 1h3 1l-1 1-23-2z" class="N"></path><path d="M382 303c8-1 17-1 25-1s18-1 26 1l4 1c-1 0-2 1-4 1l-51-2z" class="B"></path><path d="M434 302c3 0 6 1 9 0 5 0 11 1 16 3h5l12 4-2 1 1 1c1 0 1 1 2 1l-2 1c-4 0-8-2-12-3l-22-5-4-1-4-1 1-1z" class="N"></path><path d="M459 305h5l12 4-2 1h-1c-5-2-10-2-15-4 1 0 1-1 1-1z" class="D"></path><path d="M434 302c3 0 6 1 9 0 5 0 11 1 16 3 0 0 0 1-1 1-6-1-11-2-17-1l-4-1-4-1 1-1z" class="U"></path><path d="M414 307l28 2c8 1 17 2 25 5l10 4h0v1l3 3c2 2 6 8 9 9v1l-2 2c1 2 2 4 4 6h-2l-8-8h-2c-3-3-7-6-11-8-1-1-3-2-5-3s-4-1-5-4c-4 0-8-2-11-3-2 0-5-1-6-1-1-1-1 0-2-1 0 0 0-1-1-1l-24-3v-1z" class="L"></path><path d="M458 317c8 3 18 8 23 15h-2c-3-3-7-6-11-8-1-1-3-2-5-3s-4-1-5-4z" class="G"></path><path d="M438 311c10-1 22 2 31 6 2 1 3 1 5 2 1 1 1 1 1 3 1 0 1 0 2 1 2 1 6 4 6 6h0c-10-7-21-13-33-15-1 0 0 0-1-1h-2v1c-2 0-5-1-6-1-1-1-1 0-2-1 0 0 0-1-1-1z" class="d"></path><path d="M375 292c3-1 4-2 7-2 1-1 2-2 4-1l-1 1v1l-2 2v1c4-2 8-3 12-4l1 1c3-1 5-1 8-1v1c-3 0-5 1-8 2h-1c-1 0-1 1-2 2-1 0-2 0-2 1h-3v1c4 1 9 0 13 0 6 1 12-1 18 1 4 0 8 0 12 1l12 3c-3 1-6 0-9 0l-1 1c-8-2-18-1-26-1s-17 0-25 1h-2c-2 1-3 1-5 2 0 0-1 1-2 1s-1 1-2 2c0 0 0 1 1 1h3 3v1h1l-1 1h-5l-4-1c-2 0-2 0-3-1l-15-1h0 4c1-1 1-2 3-2h0v-1l-6 1h-1c2-1 4-1 6-2l-1-1h-2-1 0v-1l1-1c2 1 6-1 8-1l3-3 3-1c1-1 6-4 7-4z" class="C"></path><path d="M419 298c4 0 8 0 12 1l12 3c-3 1-6 0-9 0l-26-2c4-1 8-1 11-2z" class="D"></path><path d="M366 309h0c-1-1-3-2-4-2s-2 0-2-1c0 0 1 0 2-1 6-1 12-2 18-2-2 1-3 1-5 2 0 0-1 1-2 1s-1 1-2 2c0 0 0 1 1 1h3 3v1h1l-1 1h-5l-4-1c-2 0-2 0-3-1z" class="L"></path><path d="M396 291c3-1 5-1 8-1v1c-3 0-5 1-8 2h-1c-1 0-1 1-2 2-1 0-2 0-2 1h-3v1c4 1 9 0 13 0 6 1 12-1 18 1-3 1-7 1-11 2-5 0-11-1-16-1-4 0-8 1-12 1h-1l2-1v-1c1-1 3-2 5-3l6-3c2 0 3 0 4-1z" class="E"></path><path d="M375 292c3-1 4-2 7-2 1-1 2-2 4-1l-1 1v1l-2 2v1c4-2 8-3 12-4l1 1c-1 1-2 1-4 1l-6 3c-2 1-4 2-5 3h0c-2 1-3 1-4 2-5 3-10 3-15 4l-2-1c-1 0-2 1-3 1l-1-1h-2-1 0v-1l1-1c2 1 6-1 8-1l3-3 3-1c1-1 6-4 7-4z" class="B"></path><path d="M362 300c2 0 5-1 7-1-3 2-5 3-9 4-1 0-2 1-3 1l-1-1h-2-1 0v-1l1-1c2 1 6-1 8-1z" class="H"></path><path d="M375 292c3-1 4-2 7-2 1-1 2-2 4-1l-1 1c-3 2-5 4-8 5-2 1-6 4-8 4s-5 1-7 1l3-3 3-1c1-1 6-4 7-4z" class="Q"></path><path d="M368 296l2 1c2 0 4-2 7-2-2 1-6 4-8 4s-5 1-7 1l3-3 3-1z" class="a"></path><path d="M423 343c5 2 8 6 13 8 0-1-1-2-1-2l1-1 6 3c3 0 3 0 5 1 5 3 10 6 13 10l1 1c2 3 5 7 6 11l5 9 3 1h1c0 1 1 1 1 2h1 0c1 2 2 3 2 5l1 5 1 3c0 1 2 5 1 6l-1 1v5l-1 1c0 2-1 2-2 4s-1 4 0 6c0 2-1 2-2 4l-2-3c0 1-1 2-2 3v-2h-1 0c0 1-1 1-1 2-3 3-3 7-8 8v1c-1 1-1 2-2 3 0-2 0-4 1-6l-1-1v-2c1-1 1-2 1-4h-1 0c-1-3 0-7-1-10 0-8-3-16-4-24l-1 1c1 5 2 11 2 16v-1l-1-1h0c-2-1-2-3-3-4v-1h-1c0-2 0-5-1-6l-1-4c0-2-1-4-2-6l-2-4c-2-4-3-8-6-12-2-4-6-8-8-12-1-1-1-1-1-2l-1-1 1-1v-1l-4-4h1l1-1c-2-1-4-2-5-4h-1z" class="P"></path><path d="M457 371l3 4h0c1 2 0 1 1 2 4 4 4 11 6 15v3c1 1 0 2 1 3v1 9 7c0 2 0 4-1 6-2 1-2 2-3 4-1-1 0-4-1-5l-1 5h-1 0c-1-3 0-7-1-10l1 4 1 1 1-11c1 2 1 8 1 11h1v-1-1c1-1 1-4 1-6 0-13-2-28-9-39v-2z" class="E"></path><path d="M423 343c5 2 8 6 13 8 8 3 16 13 21 20v2l-1 1c1 3 3 5 4 8h-1v-1 3h0l-1-1c-3-10-10-22-19-28-2-1-3-3-5-3 1 2 4 5 6 7v1h0l1 1c1 3 4 5 6 8 3 6 8 15 9 22l-1 1c1 5 2 11 2 16v-1l-1-1h0c-2-1-2-3-3-4v-1h-1c0-2 0-5-1-6l-1-4c0-2-1-4-2-6l-2-4c-2-4-3-8-6-12-2-4-6-8-8-12-1-1-1-1-1-2l-1-1 1-1v-1l-4-4h1l1-1c-2-1-4-2-5-4h-1z" class="B"></path><path d="M451 395c0-6-2-12-4-17-1-2-1-4-2-6-1-3-3-6-4-8l2 3c6 7 9 17 12 25 1 5 2 11 2 16v-1l-1-1h0c-2-1-2-3-3-4v-1h-1c0-2 0-5-1-6z" class="Y"></path><path d="M436 351c0-1-1-2-1-2l1-1 6 3c3 0 3 0 5 1 5 3 10 6 13 10l1 1c2 3 5 7 6 11l5 9 3 1h1c0 1 1 1 1 2h1 0c1 2 2 3 2 5l1 5 1 3c0 1 2 5 1 6l-1 1v5l-1 1c0 2-1 2-2 4s-1 4 0 6c0 2-1 2-2 4l-2-3c0 1-1 2-2 3v-2h-1 0c0 1-1 1-1 2-3 3-3 7-8 8v1c-1 1-1 2-2 3 0-2 0-4 1-6l-1-1v-2c1-1 1-2 1-4l1-5c1 1 0 4 1 5 1-2 1-3 3-4 1-2 1-4 1-6v-7-9-1c-1-1 0-2-1-3v-3c-2-4-2-11-6-15-1-1 0 0-1-2h0l-3-4c-5-7-13-17-21-20z" class="V"></path><path d="M468 415h1v5h1c1-2 2-4 2-6v7c-1 2-2 3-3 5h0l-1-1-1-1c0 1 0 2-1 3v1l-2 2c-1 1-1 1-1 2h-1l-1-1v-2c1-1 1-2 1-4l1-5c1 1 0 4 1 5 1-2 1-3 3-4 1-2 1-4 1-6z" class="I"></path><defs><linearGradient id="BD" x1="468.441" y1="405.339" x2="476.673" y2="400.87" xlink:href="#B"><stop offset="0" stop-color="#9d9a93"></stop><stop offset="1" stop-color="#b4b4b4"></stop></linearGradient></defs><path fill="url(#BD)" d="M470 385c4 10 7 20 7 31-1 1-1 2-2 3h-1c0-6-1-12-2-18l-3-14c0-1 0-1 1-2z"></path><defs><linearGradient id="BE" x1="466.863" y1="406.604" x2="479.637" y2="399.896" xlink:href="#B"><stop offset="0" stop-color="#252727"></stop><stop offset="1" stop-color="#565251"></stop></linearGradient></defs><path fill="url(#BE)" d="M467 374l5 9c3 8 6 15 7 24v9c-1 2-1 4 0 6 0 2-1 2-2 4l-2-3c1-3 1-5 2-7 0-11-3-21-7-31v-1c0-3-2-6-3-9h0v-1z"></path><defs><linearGradient id="BF" x1="476.659" y1="400.079" x2="475.946" y2="383.395" xlink:href="#B"><stop offset="0" stop-color="#babab9"></stop><stop offset="1" stop-color="#eae9e9"></stop></linearGradient></defs><path fill="url(#BF)" d="M476 384c0 1 1 1 1 2h1 0c1 2 2 3 2 5l1 5 1 3c0 1 2 5 1 6l-1 1v5l-1 1c0 2-1 2-2 4v-9c-1-9-4-16-7-24l3 1h1z"></path><path d="M479 407h0c1-2 2-7 1-9v-3c0-1 0-2-1-2v-1l1-1 1 5 1 3c0 1 2 5 1 6l-1 1v5l-1 1c0 2-1 2-2 4v-9z" class="I"></path><path d="M436 351c0-1-1-2-1-2l1-1 6 3c3 0 3 0 5 1 5 3 10 6 13 10l1 1c2 3 5 7 6 11v1h0c1 3 3 6 3 9v1c-1 1-1 1-1 2v2c0 5 2 10 2 14 1 4 1 7 1 11 0 2-1 4-2 6h-1v-5h-1v-7-9-1c-1-1 0-2-1-3v-3c-2-4-2-11-6-15-1-1 0 0-1-2h0l-3-4c-5-7-13-17-21-20z" class="L"></path><path d="M470 384l-17-26c3 1 3 4 6 4h1l1 1c2 3 5 7 6 11v1h0c1 3 3 6 3 9z" class="V"></path><path d="M436 351c0-1-1-2-1-2l1-1 6 3 1 1c5 4 11 8 15 15 0 2 1 3 2 5v2 1l-3-4c-5-7-13-17-21-20z" class="Q"></path><path d="M432 470v-1c1-3 1-5 1-8 0 6 0 15 3 20 2 8 6 15 10 21 4 4 8 8 11 12l4 2v1l4 2 7 4c2 1 5 2 7 4 0 1 1 1 2 2l5 3c2 1 2 2 4 2v1a30.44 30.44 0 0 1 8 8c3 5 7 9 7 15l2 5c0 1 0 2-1 3l-1-1v3c-1-1-1-2-1-2h-1l-1 1c0 1 0 2 1 3 0 2 0 5-1 7 0-1-1-2-1-3v-4c-1-2-1-3-1-5-1-3-2-5-3-8h0c-2-4-5-8-8-12 0-1-2-2-3-3l-2 1 3 4-1 3v-1h-2l-1-2c-1 3 1 4 1 7l-2-3-1 2-3-5-5-4c-4-4-8-7-12-10v1c-2-1-3-2-5-3l-1 2-1 1h-1l-14-16c-2-2-5-4-7-5-6-4-9-10-10-17-1-3-1-7 0-10h1l-1-1 2-3 2-5c0-1 1-5 2-7l1-1h0l1 4v-4h2z" class="B"></path><path d="M479 533l-4-3-7-5-3-2 1-1c4 3 8 4 13 7l5 3-1 1c-1-1-3-2-4-3l-1 1 1 2z" class="L"></path><path d="M479 533l-1-2 1-1c1 1 3 2 4 3l1-1c1 1 1 1 2 0 2 1 2 2 4 2v1a30.44 30.44 0 0 1 8 8c3 5 7 9 7 15-1-2-1-4-3-5-1-2-2-3-3-4-1-2-3-3-4-4-1-2-3-3-4-5l-1 1h-2-1c-1-1-3-1-4-3v-1l5 3h0c0-1-3-4-5-5-1 0-3-1-4-2z" class="G"></path><path d="M490 541l1-1c1 2 3 3 4 5 1 1 3 2 4 4 1 1 2 2 3 4 2 1 2 3 3 5l2 5c0 1 0 2-1 3l-1-1v3c-1-1-1-2-1-2h-1l-1 1c0 1 0 2 1 3 0 2 0 5-1 7 0-1-1-2-1-3v-4c-1-2-1-3-1-5l-3-8h0v-1c0-5-6-12-9-15h2z" class="d"></path><path d="M495 547c2 2 3 3 4 5l1 1c1 1 1 2 2 4l-3-1-2-3c-1-2-2-4-2-6z" class="G"></path><path d="M499 556l3 1c1 3 3 5 3 8v3c-1-1-1-2-1-2-2-3-4-7-5-10z" class="U"></path><path d="M488 541h2c1 2 3 4 5 6 0 2 1 4 2 6l2 3c1 3 3 7 5 10h-1l-1 1c0 1 0 2 1 3 0 2 0 5-1 7 0-1-1-2-1-3v-4c-1-2-1-3-1-5-1-3-2-5-3-8h0v-1c0-5-6-12-9-15z" class="I"></path><defs><linearGradient id="BG" x1="448.881" y1="484.049" x2="438.119" y2="505.951" xlink:href="#B"><stop offset="0" stop-color="#8e8d8c"></stop><stop offset="1" stop-color="#c7c6c4"></stop></linearGradient></defs><path fill="url(#BG)" d="M432 470v-1c1-3 1-5 1-8 0 6 0 15 3 20 2 8 6 15 10 21 4 4 8 8 11 12h0-2l-1 1c-2-1-2-1-3-3h-1c-1-1-1-2-3-3l-1 1 2 3h0c-3-3-6-6-8-9-3-5-5-10-7-15-1-1-1-2-2-3l-1 1h0c0-2 0-3-1-4 0-3 0-6 1-9v-4h2z"></path><path d="M432 470c0 4-1 8 0 12 0 3 1 6 2 9 3 7 6 13 12 19h0l2 3h0c-3-3-6-6-8-9-3-5-5-10-7-15-1-1-1-2-2-3l-1 1h0c0-2 0-3-1-4 0-3 0-6 1-9v-4h2z" class="V"></path><defs><linearGradient id="BH" x1="452.305" y1="525.925" x2="457.103" y2="520.504" xlink:href="#B"><stop offset="0" stop-color="#1b1a19"></stop><stop offset="1" stop-color="#383636"></stop></linearGradient></defs><path fill="url(#BH)" d="M435 503c5 8 11 14 18 19 9 6 19 11 27 18l4 3 3 4-1 3v-1h-2l-1-2c-1 3 1 4 1 7l-2-3-1 2-3-5-5-4c-4-4-8-7-12-10-1-2-4-4-5-5-6-4-11-8-16-13-2-3-5-7-7-10 1-2 1-2 2-3z"></path><path d="M480 540l4 3 3 4-1 3v-1l-2-2c0-1 0-1-1-2-2-2-2-3-3-5z" class="F"></path><path d="M474 540c3 2 7 4 9 7h0c-1 3 1 4 1 7l-2-3-1 2-3-5v-3l-4-5z" class="C"></path><path d="M478 545c2 2 3 4 4 6l-1 2-3-5v-3z" class="b"></path><path d="M456 529c2 0 3 1 5 2 1 1 3 1 5 3 3 2 5 5 8 6l4 5v3l-5-4c-4-4-8-7-12-10-1-2-4-4-5-5z" class="C"></path><path d="M473 544v-1l-7-8v-1c3 2 5 5 8 6l4 5v3l-5-4z" class="S"></path><path d="M428 471l1-1h0l1 4c-1 3-1 6-1 9 1 1 1 2 1 4h0c1 6 3 11 5 16-1 1-1 1-2 3 2 3 5 7 7 10 5 5 10 9 16 13 1 1 4 3 5 5v1c-2-1-3-2-5-3l-1 2-1 1h-1l-14-16c-2-2-5-4-7-5-6-4-9-10-10-17-1-3-1-7 0-10h1l-1-1 2-3 2-5c0-1 1-5 2-7z" class="c"></path><path d="M423 496v-6c1 1 1 1 1 2 0 3 1 5 2 7 0 2 1 4 3 6l1 2 3 6-1-1c-5-3-8-11-9-16z" class="C"></path><path d="M440 516c5 5 10 9 16 13 1 1 4 3 5 5v1c-2-1-3-2-5-3l-1 2c-4-3-7-7-10-11-2-2-4-4-5-7z" class="J"></path><defs><linearGradient id="BI" x1="437.405" y1="496.217" x2="423.193" y2="489.083" xlink:href="#B"><stop offset="0" stop-color="#3e3c3b"></stop><stop offset="1" stop-color="#565654"></stop></linearGradient></defs><path fill="url(#BI)" d="M427 488c-1-3-1-7 0-9l1 1c0 1 1 2 1 3 1 1 1 2 1 4h0c1 6 3 11 5 16-1 1-1 1-2 3-3-5-6-13-6-18z"></path><path d="M428 471l1-1h0l1 4c-1 3-1 6-1 9 0-1-1-2-1-3l-1-1c-1 2-1 6 0 9-1-2-1-4-1-5-2 2-1 4-1 6 1 4 2 6 3 9s2 6 2 9l-1-2c-2-2-3-4-3-6-1-2-2-4-2-7 0-1 0-1-1-2v6l-1 1c-1-3-1-7 0-10h1l-1-1 2-3 2-5c0-1 1-5 2-7z" class="J"></path><path d="M477 318c2 1 9 4 10 5 2 1 8 4 9 6 4 2 10 7 13 11l3 4v-1-4-1l9 10 1-1h1l1 2c7 11 10 24 7 38-2 7-6 15-10 21-8 10-19 17-29 23l-2 1-11 6-6 4h0v-1h-1v-2h-2l3-3c1-3 1-3 3-4 0-2 0-2 1-2v-1c-2 0-3 0-4 1l3-3h0l1-1c1-2 2-2 2-4 1-1 1-2 3-3l6-3c3-1 5-2 7-3l5-3h0v-2h-1v-6-3-7 4h1v-8h1v2c2-1 3-2 4-4 2-3 0-11-1-15-1-3-2-6-3-8 0-1-1-2-1-3-1-1-2-2-2-4 0-1-1-2-2-4l-1-2c0-1-1-2-1-3 0-2-4-6-5-7h2c-2-2-3-4-4-6l2-2v-1c-3-1-7-7-9-9l-3-3v-1z" class="X"></path><path d="M512 345c7 9 10 18 12 29-1 4-1 7-1 11l-1 5h-1c1-4 1-8 1-13v-3h-1c0-1-1-2-1-3v-4h0c-1-6-3-9-5-14-1-3-3-5-3-8z" class="C"></path><path d="M484 428c1 0 2 1 3 1 2-1 5-3 7-4l7-4 1-1v1l-9 5 1 1v-1c1-1 1 0 2-1 1 0 1 0 2-1h2l1-1h0c-2 2-3 2-6 3-4 2-12 6-15 10v1c-1 0-1 1-1 1l-6 4h0v-1h-1v-2h-2l3-3c4-3 8-6 11-8z" class="f"></path><path d="M524 374c1 3 0 6 0 10l2-1c1 1-2 8-2 10-1 3-2 5-4 7l-2 2c0 1-1 3-2 4l-1-1 1-1-1-1c0-2 1-3 2-5 2-3 3-10 4-14 0-2 0-5 1-7 0 5 0 9-1 13h1l1-5c0-4 0-7 1-11z" class="N"></path><path d="M512 399h1c2-3 3-6 4-9l1 1c0 2-2 5-2 7h1c-1 2-2 3-2 5a57.31 57.31 0 0 1-11 11l-7 5-2-2h0l6-6a30.44 30.44 0 0 0 8-8c0-1 2-3 3-4z" class="I"></path><path d="M509 403c1 1 1 1 0 2-1 2-2 3-3 4-2 2-2 2-2 5h0l-7 5-2-2h0l6-6a30.44 30.44 0 0 0 8-8z" class="W"></path><path d="M495 413l5-3 1 1-6 6h0l2 2c-4 3-9 6-13 9-3 2-7 5-11 8 1-3 1-3 3-4 0-2 0-2 1-2v-1c-2 0-3 0-4 1l3-3h0l1-1c1-2 2-2 2-4 1-1 1-2 3-3l6-3c3-1 5-2 7-3z" class="M"></path><path d="M481 423h1c2 0 2-1 4-1l1-1 4-1v1 1h-1c-3 1-4 3-6 4l-3 2-5 4c0-2 0-2 1-2v-1c-2 0-3 0-4 1l3-3c1 0 4-3 5-4z" class="h"></path><path d="M495 413l5-3 1 1-6 6h0c-1 0-3 1-4 1-2 1-8 2-9 4 0 1 0 1-1 1-1 1-4 4-5 4h0l1-1c1-2 2-2 2-4 1-1 1-2 3-3l6-3c3-1 5-2 7-3z" class="Y"></path><defs><linearGradient id="BJ" x1="517.178" y1="354.4" x2="501.822" y2="370.1" xlink:href="#B"><stop offset="0" stop-color="#161610"></stop><stop offset="1" stop-color="#3c3c40"></stop></linearGradient></defs><path fill="url(#BJ)" d="M477 318c2 1 9 4 10 5 2 1 8 4 9 6 4 2 10 7 13 11l3 4v1c0 3 2 5 3 8 2 5 4 8 5 14h0v4c0 1 1 2 1 3h1v3c-1 2-1 5-1 7-1 4-2 11-4 14h-1c0-2 2-5 2-7l-1-1c-1 3-2 6-4 9h-1c-1 1-3 3-3 4a30.44 30.44 0 0 1-8 8l-1-1h0v-2h-1v-6-3-7 4h1v-8h1v2c2-1 3-2 4-4 2-3 0-11-1-15-1-3-2-6-3-8 0-1-1-2-1-3-1-1-2-2-2-4 0-1-1-2-2-4l-1-2c0-1-1-2-1-3 0-2-4-6-5-7h2c-2-2-3-4-4-6l2-2v-1c-3-1-7-7-9-9l-3-3v-1z"></path><path d="M514 377c0 5 0 11-3 15-1 1-2 1-3 2 1-2 2-4 2-7l1-3 1-1h0v-1c1-1 1-3 2-5z" class="R"></path><path d="M514 357h1c2 4 3 11 3 15 0 5 0 10-1 15h-1v-3c0 1-1 2-1 3 2-10 2-20-1-30z" class="E"></path><path d="M515 353c2 5 4 8 5 14h0v4 8c-2-2-1-5-2-7 0-4-1-11-3-15h-1c-1-1-2-2-2-4l2 1h0 1v-1z" class="F"></path><defs><linearGradient id="BK" x1="517.769" y1="381.674" x2="519.456" y2="388.773" xlink:href="#B"><stop offset="0" stop-color="#605e5e"></stop><stop offset="1" stop-color="#777773"></stop></linearGradient></defs><path fill="url(#BK)" d="M520 371c0 1 1 2 1 3h1v3c-1 2-1 5-1 7-1 4-2 11-4 14h-1c0-2 2-5 2-7l-1-1c-1 3-2 6-4 9h-1c2-4 4-8 5-12 1-5 1-10 1-15 1 2 0 5 2 7v-8z"></path><path d="M496 329c4 2 10 7 13 11l3 4v1c0 3 2 5 3 8v1h-1 0l-2-1h0c-1-1-2-3-3-5-2-4-4-8-8-11 1 3 2 6 4 8 1 3 4 5 4 8 1 2 1 5 1 7-1-1-1-3-2-4-1-7-7-15-11-20 1-1 1-1 2-1h0c0-3-3-3-3-6z" class="M"></path><path d="M509 340l3 4v1c0 3 2 5 3 8v1h-1 0c-1-1-1-2-2-3-1-2-2-5-3-7s-1-2 0-4z" class="O"></path><path d="M515 387c0-1 1-2 1-3v3h1c-1 4-3 8-5 12-1 1-3 3-3 4a30.44 30.44 0 0 1-8 8l-1-1h0v-2h-1v-6-3-7 4h1v-8h1v2 2c0 1 0 2 1 2 3-1 6-4 8-7 0 3-1 5-2 7l-1 2c1 0 3-1 4-2l4-7z" class="B"></path><path d="M507 396c1 0 3-1 4-2-2 4-4 7-7 9l-1-2h1c1-2 1-3 3-5z" class="C"></path><path d="M499 402l2-2h1v1l-1 1h1l1-1 1 2c-2 1-3 2-4 3l1 1c2 0 4-2 6-3-2 3-4 5-7 6v-2h-1v-6z" class="J"></path><path d="M499 392v4h1v-8h1v2 2c0 1 0 2 1 2 3-1 6-4 8-7 0 3-1 5-2 7l-1 2c-2 2-2 3-3 5h-1l-1 1h-1l1-1v-1h-1l-2 2v-3-7z" class="V"></path><path d="M477 318c2 1 9 4 10 5 2 1 8 4 9 6 0 3 3 3 3 6h0c-1 0-1 0-2 1 4 5 10 13 11 20 1 1 1 3 2 4 2 5 3 11 4 17-1 2-1 4-2 5v1h0l-1 1-1 3c-2 3-5 6-8 7-1 0-1-1-1-2v-2c2-1 3-2 4-4 2-3 0-11-1-15-1-3-2-6-3-8 0-1-1-2-1-3-1-1-2-2-2-4 0-1-1-2-2-4l-1-2c0-1-1-2-1-3 0-2-4-6-5-7h2c-2-2-3-4-4-6l2-2v-1c-3-1-7-7-9-9l-3-3v-1z" class="D"></path><path d="M506 358v-2l1-1 1 1c1 1 1 3 2 4 2 5 3 11 4 17-1 2-1 4-2 5v1h0l-1 1c0-8 0-15-4-22l-1-4z" class="I"></path><path d="M494 347c3 1 7 5 8 9v1c4 7 9 19 6 28v3h-1c-1 0-2 1-3 2s-2 1-3 2v-2c2-1 3-2 4-4 2-3 0-11-1-15-1-3-2-6-3-8 0-1-1-2-1-3-1-1-2-2-2-4 0-1-1-2-2-4l-1-2c0-1-1-2-1-3z" class="E"></path><path d="M508 385c0-1-1-1-1-2s0-4-1-5l-3-14c0-3-1-5-1-7h0c4 7 9 19 6 28z" class="B"></path><path d="M477 318c2 1 9 4 10 5 2 1 8 4 9 6 0 3 3 3 3 6h0c-1 0-1 0-2 1 4 5 10 13 11 20l-1-1-1 1v2c-3-8-9-14-14-21-1-2-2-3-3-5v-1c-3-1-7-7-9-9l-3-3v-1z" class="V"></path><path d="M480 322c3 1 4 1 6 4l6 6c1 0 1 1 1 1-1 0-1 0-2-1l-2-1h0c-3-1-7-7-9-9z" class="I"></path><path d="M487 323c2 1 8 4 9 6 0 3 3 3 3 6h0c-1 0-1 0-2 1-1-2-3-3-3-4-1-2-2-3-3-4s-3-2-4-3v-2z" class="C"></path><defs><linearGradient id="BL" x1="363.333" y1="373.052" x2="450.094" y2="320.992" xlink:href="#B"><stop offset="0" stop-color="#c8c6c5"></stop><stop offset="1" stop-color="#f0f0ef"></stop></linearGradient></defs><path fill="url(#BL)" d="M351 308l15 1c1 1 1 1 3 1l4 1h5l1-1h-1v-1c2 0 3 0 5 1l17 1h2l3-1 23 2c3 1 10 1 13 1 1 0 4 1 6 1 3 1 7 3 11 3 1 3 3 3 5 4s4 2 5 3c4 2 8 5 11 8h2l8 8c1 1 5 5 5 7 0 1 1 2 1 3l1 2c1 2 2 3 2 4 0 2 1 3 2 4 0 1 1 2 1 3 1 2 2 5 3 8 1 4 3 12 1 15-1 2-2 3-4 4v-2h-1v8h-1v-4 7 3 6h1v2h0l-5 3c-2 1-4 2-7 3l-6 3c-2 1-2 2-3 3-1-2-1-4 0-6s2-2 2-4l1-1v-5l1-1c1-1-1-5-1-6l-1-3-1-5c0-2-1-3-2-5h0-1c0-1-1-1-1-2h-1l-3-1-5-9c-1-4-4-8-6-11l-1-1c-3-4-8-7-13-10-2-1-2-1-5-1l-6-3-1 1s1 1 1 2c-5-2-8-6-13-8h1c1 2 3 3 5 4l-1 1h-1l4 4v1c-3-2-6-5-9-7-13-10-28-15-43-19h-6c-2-1-4-1-6 0v1l-8-1c0-1 0-2-1-3-3-1-7-1-11-1l-9 1-1-1-7 1c5-4 10-7 17-9 1-1 2-1 4-1 1-1 3-1 4-1 4-1 8-1 12-1-4-2-7-1-11-1h-7c-1 0-1 1-2 0h-1l1-1c-1 0-5 1-6 2h-1c3-3 7-3 11-4z"></path><path d="M445 343h0c4 1 8 4 11 7v1l-2-1-9-5h0 3c-1-1-2-2-3-2z" class="P"></path><path d="M367 318h9c4 0 9 0 13 3-8 0-14-1-22-3z" class="B"></path><path d="M367 318c-5-1-12-1-18 0l-7 2h0c1-1 3-1 4-2 8-3 17-3 25-2h5v2h-9z" class="L"></path><path d="M337 323c7-1 14-2 20-2 13 0 26 2 38 5l-1 1c-2-1-5-1-7-2-8-1-15-2-23-2-2 0-5-1-8 0h-9l-9 1-1-1z" class="D"></path><path d="M395 326c8 2 15 5 22 8 7 4 13 10 19 14l-1 1s1 1 1 2c-5-2-8-6-13-8-5-5-13-8-19-11-3-2-7-4-10-5l1-1z" class="E"></path><defs><linearGradient id="BM" x1="403.51" y1="319.814" x2="402.468" y2="327.86" xlink:href="#B"><stop offset="0" stop-color="#aeadac"></stop><stop offset="1" stop-color="#cbcbc6"></stop></linearGradient></defs><path fill="url(#BM)" d="M376 316c19 2 38 5 53 17h1c3 2 6 4 8 6l1 1 1 1v1c-1-1-2-1-3-2-3-2-7-5-10-7-1-1-2-1-3-1l2 2-1 1c-3-2-6-4-9-5-8-4-18-7-27-9-4-3-9-3-13-3v-2z"></path><defs><linearGradient id="BN" x1="388.934" y1="342.566" x2="397.829" y2="327.827" xlink:href="#B"><stop offset="0" stop-color="#bebdba"></stop><stop offset="1" stop-color="#e0dfde"></stop></linearGradient></defs><path fill="url(#BN)" d="M347 323h9c3-1 6 0 8 0 8 0 15 1 23 2 2 1 5 1 7 2 3 1 7 3 10 5 6 3 14 6 19 11h1c1 2 3 3 5 4l-1 1h-1l4 4v1c-3-2-6-5-9-7-13-10-28-15-43-19h-6c-2-1-4-1-6 0v1l-8-1c0-1 0-2-1-3-3-1-7-1-11-1z"></path><path d="M465 363c2 0 1 0 2 1 0-2 0-2-1-3s-2-1-3-2l-3-3 1-2a53.56 53.56 0 0 1 15 15c1 2 1 4 3 5l1 1c1 4 3 8 5 12l1-1v-1l3 8h0 1c0 2 1 4 1 5l1 1c0-3 0-6-1-9v-2-6c2 4 2 8 3 11 0 5 1 10-3 13h-2 0-2v1c-2 1-1 1-3 1l-1-3c1-1-1-5-1-6l-1-3-1-5c0-2-1-3-2-5h0-1c0-1-1-1-1-2h-1l-3-1-5-9c-1-4-4-8-6-11v-1-2h0c2 1 3 1 4 3h0z" class="f"></path><path d="M465 363h0c6 6 10 13 13 21h0v2h0 0-1c0-1-1-1-1-2-2-7-7-15-12-21h1z" class="P"></path><path d="M461 363v-1-2h0c2 1 3 1 4 3h-1c5 6 10 14 12 21h-1l-3-1-5-9c-1-4-4-8-6-11z" class="X"></path><path d="M479 374l1 1c1 4 3 8 5 12l1-1v-1l3 8h0 1c0 2 1 4 1 5l1 1c0-3 0-6-1-9v-2-6c2 4 2 8 3 11 0 5 1 10-3 13h-2 0-2v1c-2 1-1 1-3 1l-1-3c1-1-1-5-1-6l-1-3h1v1-2-3l-1-1c0-1 0-2 1-3v1l1 2 1-1h0 1c-2-5-5-10-6-16z" class="E"></path><path d="M486 385l3 8h0v11l-1-3c0-2 0-4-1-6-1-3-1-6-2-8l1-1v-1z" class="N"></path><path d="M481 396h1v1-2-3l-1-1c0-1 0-2 1-3v1l1 2 1-1h0 1c1 5 2 10 1 15-1-4-2-7-3-11v5h-1l-1-3zm-115-82c5-2 13-1 19 0h1c15 4 31 5 46 10 5 1 9 3 13 5l7 3c1 1 3 2 5 3 13 9 23 20 30 35 2 4 3 8 4 12v6 2c1 3 1 6 1 9l-1-1c0-1-1-3-1-5h-1 0l-3-8c-2-5-4-11-7-17-2-4-5-8-8-12s-7-8-11-12c-2 0-3-3-5-4-1 0-2-1-2-1-1-1-2-1-3-2-1 0-1-1-2-1 0-2-1-2-2-3l-1 1h0l-1-1-4-2-9-4-1-1-1-1h-1c-1 0-2-1-3-1s-1 0-2 1h1c3 1 8 6 10 8v1c-1-1-1-1-2-1l-1-1-1 1h-1c-15-12-34-15-53-17h-5c-3-2-5-2-8-2h-1 4z" class="Y"></path><path d="M460 344c3 1 5 3 6 5 7 6 11 12 15 20 2 4 4 8 5 12 1 2 1 3 3 5h0c1-3-1-9-2-11v-1-1-3c2 4 3 8 4 12v6 2c1 3 1 6 1 9l-1-1c0-1-1-3-1-5h-1 0l-3-8c-2-5-4-11-7-17-2-4-5-8-8-12s-7-8-11-12z" class="T"></path><defs><linearGradient id="BO" x1="432.373" y1="338.055" x2="438.98" y2="321.613" xlink:href="#B"><stop offset="0" stop-color="#b7b6b4"></stop><stop offset="1" stop-color="#e7e6e5"></stop></linearGradient></defs><path fill="url(#BO)" d="M351 308l15 1c1 1 1 1 3 1l4 1h5l1-1h-1v-1c2 0 3 0 5 1l17 1h2l3-1 23 2c3 1 10 1 13 1 1 0 4 1 6 1 3 1 7 3 11 3 1 3 3 3 5 4s4 2 5 3c4 2 8 5 11 8h2l8 8c1 1 5 5 5 7 0 1 1 2 1 3l1 2c1 2 2 3 2 4 0 2 1 3 2 4 0 1 1 2 1 3 1 2 2 5 3 8 1 4 3 12 1 15-1 2-2 3-4 4v-2h-1v8h-1v-4 7 3 6h1v2h0l-5 3c-2 1-4 2-7 3l-6 3c-2 1-2 2-3 3-1-2-1-4 0-6s2-2 2-4l1-1v-5l1-1 1 3c2 0 1 0 3-1v-1h2 0 2c4-3 3-8 3-13-1-3-1-7-3-11-1-4-2-8-4-12-7-15-17-26-30-35-2-1-4-2-5-3l-7-3c-4-2-8-4-13-5-15-5-31-6-46-10h-1c-6-1-14-2-19 0-2 0-6-1-8 0h-3l-1 1h-3c-1 0-1 0-2 1h-2v-1c1-1 2-1 4-1 1-1 3-1 4-1 4-1 8-1 12-1-4-2-7-1-11-1h-7c-1 0-1 1-2 0h-1l1-1c-1 0-5 1-6 2h-1c3-3 7-3 11-4z"></path><path d="M351 308l15 1c1 1 1 1 3 1l4 1h5l1-1h-1v-1c2 0 3 0 5 1l17 1h2c4 1 9 2 14 2-2 1-2 1-4 0-2 0-8-1-10 0h-1-1c4 1 9 1 12 3h0c-5 0-11-2-17-2v-1h-2-1c-2-1-5-1-7-1-1-1-1 0-2 0v1h1l1 1c-6-1-14-2-19 0-2 0-6-1-8 0h-3l-1 1h-3c-1 0-1 0-2 1h-2v-1c1-1 2-1 4-1 1-1 3-1 4-1 4-1 8-1 12-1-4-2-7-1-11-1h-7c-1 0-1 1-2 0h-1l1-1c-1 0-5 1-6 2h-1c3-3 7-3 11-4z" class="B"></path><defs><linearGradient id="BP" x1="504.084" y1="386.545" x2="481.632" y2="372.405" xlink:href="#B"><stop offset="0" stop-color="#373235"></stop><stop offset="1" stop-color="#575a52"></stop></linearGradient></defs><path fill="url(#BP)" d="M457 335c2-1 7 4 9 5 5 4 11 9 14 15 6 7 9 15 12 24 1 3 1 9 3 12 1-13-9-35-17-45-3-3-6-7-10-10h0c14 9 24 27 29 43l2 13v7 3 6h1v2h0l-5 3c-2 1-4 2-7 3l-6 3c-2 1-2 2-3 3-1-2-1-4 0-6s2-2 2-4l1-1v-5l1-1 1 3c2 0 1 0 3-1v-1h2 0 2c4-3 3-8 3-13-1-3-1-7-3-11-1-4-2-8-4-12-7-15-17-26-30-35z"></path><path d="M496 401v-3h1l2 1v3 6h1v2h0l-5 3c-2 1-4 2-7 3v-1l2-2c2 0 3-3 4-4v-1c2-2 2-4 2-7z" class="H"></path><path d="M496 401v-3h1l2 1v3 6h1v2h0l-5 3c1-1 3-3 3-5s0-5-2-7z" class="O"></path><path d="M494 393c1 4 2 11 0 15-1 1-1 1-2 1l-2 2h-1c-1 1-2 2-4 3-1-1-1-5-2-6 0 3 0 7-2 10l1 1c-2 1-2 2-3 3-1-2-1-4 0-6s2-2 2-4l1-1v-5l1-1 1 3c2 0 1 0 3-1v-1h2 0 2c4-3 3-8 3-13z" class="V"></path><path d="M405 310l23 2c3 1 10 1 13 1 1 0 4 1 6 1 3 1 7 3 11 3 1 3 3 3 5 4s4 2 5 3c4 2 8 5 11 8h2l8 8c1 1 5 5 5 7 0 1 1 2 1 3l1 2c1 2 2 3 2 4 0 2 1 3 2 4 0 1 1 2 1 3 1 2 2 5 3 8 1 4 3 12 1 15-1 2-2 3-4 4v-2h-1v8h-1v-4l-2-13c-5-16-15-34-29-43l-2-1v-1c-1 0-1-1-2-1l-1-1-2-1c-1 0-1-1-2-1l-1-1h-1-1c-1 0-1-1-2-1h-1c-1-1-3-2-4-2-3-1-5-2-7-3h-1l-5-3-3-1c-2 0-4-1-5-1v-1l-1-1-4-1c-1-1 0 0-1 0l-3-1c-1-1-2-1-3-1-5 0-10-1-14-2l3-1z" class="X"></path><path d="M457 324c-4-1-7-4-11-5-3-1-7-2-10-3-2 0-7-1-8-2 7 0 13 2 20 4 2 1 5 1 7 2 3 0 7 3 9 2l-1-1c2 1 4 2 5 3 4 2 8 5 11 8 2 2 4 4 4 7-6-5-17-15-26-15z" class="N"></path><path d="M457 324c9 0 20 10 26 15 0-3-2-5-4-7h2l8 8c1 1 5 5 5 7 0 1 1 2 1 3l1 2c1 2 2 3 2 4 0 2 1 3 2 4 0 1 1 2 1 3 1 2 2 5 3 8 1 4 3 12 1 15-1 2-2 3-4 4v-2h-1v8h-1v-4l-2-13v1c1 0 1 1 1 2h0v-1-1-2c0-17-13-32-24-43-4-4-11-9-17-11z" class="B"></path><path d="M485 343c4 3 8 7 10 12 1 5 4 10 6 15 0 2 1 4 1 6v5c1 2 1 4 0 6h-1c-1-4-1-9-2-13 0-5-2-10-4-14s-4-8-7-12c-1-1-2-3-3-4v-1z" class="Y"></path><path d="M457 324c9 0 20 10 26 15l1 1c3 3 6 4 9 7 1 2 2 5 3 7-1 1 0 1-1 1-2-5-6-9-10-12v-1c-3-3-7-6-11-7-4-4-11-9-17-11z" class="E"></path><path d="M824 147l1 1 1 1c-9 13-19 26-27 39-6 10-11 20-15 31-10 30-11 64-5 95 3 20 9 40 20 58h-1c-7-9-12-20-16-30-4-12-8-25-11-37-3-16-6-32-12-48-7-15-16-29-29-41-17-15-38-23-60-26-13-1-25-1-38-1h-44-15-6c-3-2-9-1-12-1l-1-1c-2 0-3 1-5-1v-1c0-1 1-1 2-1 4-2 10-1 14-1h1l-8-1c3 0 7 1 10 0h0 12c-1-1-2-1-3-1l-1-1h1l-1-1h-2l-2-1h-1c-1-1-3-1-5-2-1 0-2 0-3-1l-3-1-2-2c3 1 5 3 9 3h1c-1-1-2-1-4-2h0c2 0 6 1 7 1 2 1 4 1 5 1 4 0 6 1 8 4l1 1h2c2 0 4 0 6-1 1 0 5 1 7 2-1-1-3-3-4-3h-1c0-2-3-5-5-6v-1l7 5c0 1 0 1 1 2h1 1 1 1l1-1c4 2 10 2 15 2l1-1h0 0 4c-5-2-11-4-15-8-1 0-2-1-3-2v-4l1-1-1-1c2-1 3-2 4-3l2 2c-2 2-1 3-1 5 1 3 2 4 4 5l2 1v-1l10 4v1h-1 0c3 1 7 2 10 2l16 1c3-1 6 0 9 0 2-1 5-1 8-1 1 0 2-1 3-1l1-1h2c1 0 1 0 2 1h0l10-1-1 1h1c-1 1-2 1-3 2l2 1c3 1 5-1 8-1l5-1h4l2 2c5-1 11-1 16 0 3 0 7 1 9 0h1 14l6-1c5 0 9 1 14 0 2-1 4-1 7-2h-5c4-1 8-2 13-4v-1l2-1c0-1-1-1-1-1 4-3 10-4 15-5l10-4c1 0 2-1 4-1 2-2 5-4 7-6 3-2 6-4 8-7z" class="Y"></path><path d="M558 172c3 1 5 3 9 3h1c-1-1-2-1-4-2h0c2 0 6 1 7 1 2 1 4 1 5 1 4 0 6 1 8 4l1 1h2c1 0 3 0 3 1 1 0 1 1 1 1h3c5 0 11-1 16 0l-44 1-8-1c3 0 7 1 10 0h0 12c-1-1-2-1-3-1l-1-1h1l-1-1h-2l-2-1h-1c-1-1-3-1-5-2-1 0-2 0-3-1l-3-1-2-2z" class="F"></path><path d="M576 175c4 0 6 1 8 4l1 1-4-1h-1v-1c-1-1-2-1-3-1h-1c0-1-1-1-2-1s-2 0-3-2c2 1 4 1 5 1z" class="Z"></path><path d="M590 171l7 5c0 1 0 1 1 2h1 1 1 1l1-1c4 2 10 2 15 2l1-1h0 0 4l13 2h-1c-1 1-1 0-2 0v1h6v1c-2 0-4 0-5-1-1 0-2 0-2 1h0c3 0 7-1 10 0h-32c-5-1-11 0-16 0h-3s0-1-1-1c0-1-2-1-3-1 2 0 4 0 6-1 1 0 5 1 7 2-1-1-3-3-4-3h-1c0-2-3-5-5-6v-1z" class="O"></path><path d="M698 178h4l2 2c5-1 11-1 16 0 3 0 7 1 9 0h1c5 1 10 0 14 2h6c-7 1-15 0-22 0h-48 0 4l1-2c3 1 5-1 8-1l5-1z" class="C"></path><path d="M698 178h4l2 2c-3 0-9 0-11-1l5-1z" class="a"></path><path d="M720 180c3 0 7 1 9 0h1c5 1 10 0 14 2h-36c4-1 8-2 12-2z" class="e"></path><path d="M809 160v1h1v1c-7 5-14 8-22 11-12 5-25 9-38 9h-6c-4-2-9-1-14-2h14l6-1c5 0 9 1 14 0 2-1 4-1 7-2h-5c4-1 8-2 13-4v-1l2-1c0-1-1-1-1-1 4-3 10-4 15-5l10-4c1 0 2-1 4-1z" class="Z"></path><path d="M779 173c4-2 12-6 17-5-6 3-13 6-19 8-2 1-4 1-6 1h-5c4-1 8-2 13-4z" class="a"></path><defs><linearGradient id="BQ" x1="793.641" y1="170.276" x2="793.847" y2="161.224" xlink:href="#B"><stop offset="0" stop-color="#999597"></stop><stop offset="1" stop-color="#babbb8"></stop></linearGradient></defs><path fill="url(#BQ)" d="M809 160v1h1v1c-3 0-5 1-7 2l-7 4c-5-1-13 3-17 5v-1l2-1c0-1-1-1-1-1 4-3 10-4 15-5l10-4c1 0 2-1 4-1z"></path><path d="M609 159l2 2c-2 2-1 3-1 5 1 3 2 4 4 5l2 1v-1l10 4v1h-1 0c3 1 7 2 10 2l16 1c3-1 6 0 9 0 2-1 5-1 8-1 1 0 2-1 3-1l1-1h2c1 0 1 0 2 1h0l10-1-1 1h1c-1 1-2 1-3 2l2 1-1 2h-4 0-38c-3-1-7 0-10 0h0c0-1 1-1 2-1 1 1 3 1 5 1v-1h-6v-1c1 0 1 1 2 0h1l-13-2c-5-2-11-4-15-8-1 0-2-1-3-2v-4l1-1-1-1c2-1 3-2 4-3z" class="I"></path><path d="M605 168v-4l1-1c1 2 1 3 1 4s1 1 1 2h0v1h0c-1 0-2-1-3-2z" class="U"></path><path d="M672 176h2c1 0 1 0 2 1h0c-2 1-4 1-7 2-6 2-17 3-24 2l6-1h-5-6c3-1 7 0 10-1h1c3-1 6 0 9 0 2-1 5-1 8-1 1 0 2-1 3-1l1-1z" class="a"></path><path d="M676 177l10-1-1 1h1c-1 1-2 1-3 2l2 1-1 2h-4 0-38c-3-1-7 0-10 0h0c0-1 1-1 2-1 1 1 3 1 5 1v-1h-6v-1c1 0 1 1 2 0h1c3 0 6 1 9 1 7 1 18 0 24-2 3-1 5-1 7-2z" class="K"></path><path d="M676 177l10-1-1 1h1c-1 1-2 1-3 2l2 1-1 2h-4c-1 0-3 0-4-1h-7v-2c3-1 5-1 7-2z" class="O"></path><path d="M676 181c1-1 5-2 7-2l2 1-1 2h-4c-1 0-3 0-4-1z" class="Z"></path><path d="M797 175h2c-1 3-3 6-5 8l-9 17c-11 22-14 45-16 70-5-18-11-33-23-47-8-11-20-19-32-25l-16-8c19-2 39 1 58-2 15-1 28-6 41-13z"></path><path d="M727 193l9-1-6 6h0c-1-2 0-3-2-4v-1h-1z" class="C"></path><path d="M738 203l7 2h1v1h1 3c-2 1-5 1-8 1-2 0-5-2-6-4h0c2 1 0 1 2 1v-1z" class="g"></path><path d="M728 193c-1 2-2 3-2 5v1c-2 0-3-1-4-3 0-1 0-1 1-2l4-1h1z" class="R"></path><path d="M747 197h0c2 0 8 1 9 2l-4 1c-3 0-5 1-7 1l-1-1c1-2 2-2 3-3z" class="V"></path><path d="M773 210c2 3 2 5 1 10v3c-1-3-1-6-2-8 0-1-1-3-2-4h1l2-1z" class="Q"></path><path d="M736 203c-2-1-2-2-2-4 0-1 0-2 1-3 2-3 6-4 9-4h0c-3 1-5 2-7 5-1 1-1 3 0 4 0 1 1 1 1 2v1c-2 0 0 0-2-1h0z" class="I"></path><path d="M757 213h3 0c-2 2-4 3-6 5-1 3-2 4-1 7v1c-1-1-1-2-2-3 0-2-1-4 0-5 1-3 4-4 6-5z" class="N"></path><path d="M746 205c8-1 17-2 24 3l3 2-2 1h-1l-1 1-1-2c-6-5-11-4-18-4h-3-1v-1z" class="L"></path><path d="M770 211c1 1 2 3 2 4 1 2 1 5 2 8-1 2-2 5-5 7-2 2-5 4-7 3-4-1-6-2-8-5h0c1 1 3 2 4 3 3 0 6 0 8-1s4-4 5-6c1-5 0-8-2-12l1-1z" class="G"></path><path d="M744 192h7c4 1 8 1 12 1 2 0 4-1 6-1 1 0 3 1 5 1s4 0 6-1c2 0 4-2 6-3-2 3-5 6-8 7-4 0-7-2-10-4v1c1 5 6 9 10 11 0 2 0 2-1 3-5-2-9-7-14-10-3-2-8-3-12-4-2-1-5-1-7-1h0z" class="c"></path><path d="M461 534c4 3 8 6 12 10l5 4 3 5 1-2 2 3c0-3-2-4-1-7l1 2h2v1l1-3-3-4 2-1c1 1 3 2 3 3 3 4 6 8 8 12h0c1 3 2 5 3 8 0 2 0 3 1 5v4c0 1 1 2 1 3v1c0 4 0 8-1 12-1 2-1 3-2 5h0l-1 1c0 3-1 5-1 7l-6 8c1-1 2-1 3-3h1s1-1 2-1v-1l1 1-4 5c-2 2-5 5-6 7s-4 4-4 7h1c-2 2-4 3-5 6-2 3-4 5-6 9-1 1-3 2-4 4-2 3-3 7-4 11v1 13l1 6 3 11v3c2 4 4 9 8 13 0 2 4 5 5 8 1 1 4 8 5 9l1 3c1 1 1 1 1 2v2h1v1 2 2h0c1 1 0 1 1 1l3 6 3 9c1 2 2 3 2 4 0 5 2 11 2 16l-3-8c1 4 2 8 1 12h0 1v-2h0c1 1 1 5 0 7v1c0 3-3 10-2 12 0 2-2 5-2 7h0c1-1 1-2 2-3 1-2 2-3 2-5h1v1c-1 1-1 1-1 2v1c2-3 1-6 3-8h0c0 4 1 8 0 11-1 2-2 4-3 5-8 13-20 22-33 30-20 12-40 19-63 24 6-4 12-8 17-13 13-13 22-29 27-47 3-11 4-24 5-36v-59-95-22-2c3 4 6 6 10 8 5 1 8 0 12-2l1-1c4-4 6-9 7-14 0-8-3-18-9-24-3-5-9-10-14-13v-1z" class="i"></path><path d="M473 779c1 0 1-1 2 0v2h-2l-1-1 1-1z" class="Z"></path><path d="M467 605c0 1 0 1-1 2-4 2-5 4-8 7h0v-2-1c1-1 3-4 5-5l4-1z" class="M"></path><path d="M482 728c3 1 3 2 4 4-1 3-2 6-2 9h-2c1-4 1-9 0-13z" class="U"></path><path d="M466 714l1-1c1 1 3 3 4 3-1-1-1-1 0-3 1 2 3 4 5 6v1h-2c-3 0-6-4-8-6z" class="L"></path><path d="M464 705c2 1 2 2 3 4s2 2 4 4c-1 2-1 2 0 3-1 0-3-2-4-3l-1 1-1-2c0-2-1-5-1-7z" class="J"></path><path d="M486 808c4-2 5-6 8-9l1 2c-2 2-6 9-9 10-1 0-2 1-3 1 0-2 1-3 3-4z" class="K"></path><path d="M466 741c-2-2-4-5-4-9 0-2 1-3 2-4h1l-1 3c0 2 0 4 1 6l1-1c1-1 1-2 3-3v2c-1 1-1 2-2 3v2l-1 1z" class="c"></path><path d="M482 741h2l-3 6c-1 1-3 3-3 5l-1 1c-1 1-2 1-4 0v-1h0c4-3 7-6 9-11z" class="T"></path><path d="M469 735c1 2 2 5 4 7 1 2 4 3 5 5h-1-1c-3-3-7-4-10-6l1-1v-2c1-1 1-2 2-3z" class="U"></path><path d="M458 790c2 0 5 2 7 3 1 1 2 2 2 4-3-1-8-2-10-3 0-2 0-3 1-4z" class="a"></path><path d="M469 793c-2-4-5-9-7-14 2 1 3 4 5 4 2-2 2-4 3-6h1l-2 8c0 1 1 1 1 3h-1l1 1v1c-1 1 0 2 0 3h-1z" class="B"></path><path d="M479 755c3 1 4-4 7-5v2c-1 2-2 4-4 6-2 0-5 0-6-1s-2-2-3-4c2 1 3 1 4 0h0c1 1 0 1 1 2h1z" class="J"></path><path d="M447 826h1c5 0 10-1 15-2-3 2-7 3-9 5-1 1-3 2-5 2h0c-1-2-1-3-2-5z" class="c"></path><path d="M480 813c-1 1-2 1-3 2-7 1-11 2-16-1-2-1-3-1-4-2s-1-1-1-2h1c2 2 5 2 7 3h0c5 3 11-1 16 0z" class="I"></path><path d="M449 831c-2 0-3 0-4-1s-2-3-2-5c0-4 5-9 8-12-1 3-4 6-5 9-1 2 0 3 1 4 1 2 1 3 2 5h0z" class="Q"></path><path d="M461 616c1 0 2 0 3-1 1 0 1 0 2-1h3 4c-8 5-13 9-16 18 0-5 1-11 4-16z" class="P"></path><defs><linearGradient id="BR" x1="484.661" y1="806.476" x2="472.984" y2="814.022" xlink:href="#B"><stop offset="0" stop-color="#211f1f"></stop><stop offset="1" stop-color="#3b3a38"></stop></linearGradient></defs><path fill="url(#BR)" d="M485 805h1c0 1 0 1-1 2l1 1c-2 1-3 2-3 4-1 1-2 2-3 1-5-1-11 3-16 0h0c6 0 12-2 17-5 1-1 3-2 4-3z"></path><defs><linearGradient id="BS" x1="460.966" y1="672.476" x2="460.534" y2="655.012" xlink:href="#B"><stop offset="0" stop-color="#5b5a58"></stop><stop offset="1" stop-color="#8d8c88"></stop></linearGradient></defs><path fill="url(#BS)" d="M460 655l1-1s1-1 2-1l-1 7v9c0 1 1 3 0 4v1c0 1-1 2-1 2h0l-1-3v5h0c-1-8-1-16 0-23z"></path><path d="M474 807h6l1 1c-5 3-11 5-17 5-2-1-5-1-7-3 6 0 12-1 17-3z" class="E"></path><path d="M488 795v1c1-1 1 0 1-1l4-4 1-1c-1 4-4 7-5 10h-1c-1 2-2 3-2 5h-1c-1 1-3 2-4 3l-1-1h-6c4-2 10-6 12-10l-1-1 1-1c0-1 1 0 2 0z" class="Q"></path><path d="M485 805c-1-1-1-1-2-1h0c0-2 2-3 3-4h2c-1 2-2 3-2 5h-1z" class="I"></path><path d="M485 608c1-1 1-1 2-1h1c-2 3-5 5-6 8h-1l-1 1c-1 3-5 5-6 8-1 1-2 2-2 3l-8 14v-6c2-7 9-15 15-19 0-1 1-1 1-2h1c1-2 2-3 4-5h1l-1-1z" class="e"></path><path d="M458 757c1 2 2 6 4 8 2 1 4 2 6 2h7l-10 3c-4 1-7 4-9 7v1c2-7 3-14 2-21h0z" class="b"></path><path d="M486 732c1 1 2 3 2 4s1 3 1 4c-1 2-1 3-2 5 0 2-1 3-1 5-3 1-4 6-7 5h-1c-1-1 0-1-1-2h0l1-1c0-2 2-4 3-5l3-6c0-3 1-6 2-9z" class="W"></path><path d="M488 736c0 1 1 3 1 4-1 2-1 3-2 5l-1-2v-3c0-1 1-1 1-2s0-1 1-2z" class="J"></path><path d="M486 743l1 2c0 2-1 3-1 5-3 1-4 6-7 5l4-4 2-4c0-1 0-2 1-3v-1z" class="R"></path><defs><linearGradient id="BT" x1="452.63" y1="697.561" x2="468.876" y2="713.444" xlink:href="#B"><stop offset="0" stop-color="#2c2926"></stop><stop offset="1" stop-color="#454644"></stop></linearGradient></defs><path fill="url(#BT)" d="M460 684l7 19h-1c0-1 0 0-1-1 0 1 0 1 1 2h-1c-1 0-1 0-1 1 0 2 1 5 1 7s0 4-1 6c0 3-3 6-4 7 0-6 1-13 2-20 0-7-2-14-2-21z"></path><path d="M500 772h1v-2h0c1 1 1 5 0 7v1c0 3-3 10-2 12 0 2-2 5-2 7l-2 4-1-2c-3 3-4 7-8 9l-1-1c1-1 1-1 1-2 0-2 1-3 2-5h1c1-3 4-6 5-10 1-2 3-4 3-6 1-4 3-8 3-12z" class="F"></path><path d="M489 800h0c3-1 5-5 7-7h0c0 2-1 4-2 6-3 3-4 7-8 9l-1-1c1-1 1-1 1-2 0-2 1-3 2-5h1z" class="Z"></path><path d="M495 589l3-6v1c-1 8-4 15-11 20-1 1-2 1-3 1-1 4-3 5-6 7-1 0-2 1-3 1l-2 1h-4-3c-1 1-1 1-2 1-1 1-2 1-3 1 5-9 13-8 21-12h0c5-2 9-7 11-12l2-3z" class="T"></path><path d="M475 613l-1-1h-1c-2 1-3 0-4-1 3-3 10-4 15-6-1 4-3 5-6 7-1 0-2 1-3 1zm-15 65h0v-5l1 3h0s1-1 1-2v-1l3 14c2 4 3 8 4 12h0l8 15c0 2 0 3-1 5-2-2-4-4-5-6-2-2-3-2-4-4s-1-3-3-4c0-1 0-1 1-1h1c-1-1-1-1-1-2 1 1 1 0 1 1h1l-7-19v-6z" class="C"></path><path d="M467 703l6 9h0l1-1c-1-1-1-1-1-2l-1-1c-2-3-4-6-3-9l8 15c0 2 0 3-1 5-2-2-4-4-5-6-2-2-3-2-4-4s-1-3-3-4c0-1 0-1 1-1h1c-1-1-1-1-1-2 1 1 1 0 1 1h1z" class="F"></path><path d="M501 570v4c0 1 1 2 1 3v1c0 4 0 8-1 12-1 2-1 3-2 5h0l-1 1c0 3-1 5-1 7l-6 8-4 4c0 1-1 1-2 2-3 1-5 4-7 5-1 1-3 3-3 4l-3 1c0-1 1-2 2-3 1-3 5-5 6-8l1-1h1c1-3 4-5 6-8h-1c-1 0-1 0-2 1-2 2-4 4-7 4 3-2 5-3 6-7 1 0 2 0 3-1 7-5 10-12 11-20l1-1h1l1-13z" class="K"></path><path d="M501 570v4c0 1 1 2 1 3v1c0 4 0 8-1 12-1 2-1 3-2 5h0-1l-3 3v-1c3-4 4-10 5-14l1-13z" class="J"></path><path d="M478 622c0-1 1-2 2-3 6-3 14-17 18-23 0 3-1 5-1 7l-6 8-4 4c0 1-1 1-2 2-3 1-5 4-7 5z" class="I"></path><path d="M454 605v-22c1 2 1 4 2 6 3 4 5 7 10 8h1c3 1 7 2 10 1h4c2 0 3-1 4-1v2c-2 1-5 5-8 5s-6-1-9 1h-1l-4 1c-2 1-4 4-5 5v1l-3 3c0-3 0-7-1-9v-1z"></path><defs><linearGradient id="BU" x1="486.263" y1="641.612" x2="472.568" y2="630.98" xlink:href="#B"><stop offset="0" stop-color="#c3c2c2"></stop><stop offset="1" stop-color="#fdfbfb"></stop></linearGradient></defs><path fill="url(#BU)" d="M491 611c1-1 2-1 3-3h1s1-1 2-1v-1l1 1-4 5c-2 2-5 5-6 7s-4 4-4 7h1c-2 2-4 3-5 6-2 3-4 5-6 9-1 1-3 2-4 4-2 3-3 7-4 11v1 13h-1c-1 0-1-1-2-2l-1-8 1-7c-1 0-2 1-2 1l-1 1c1-5 2-10 4-14l8-14 3-1c0-1 2-3 3-4 2-1 4-4 7-5 1-1 2-1 2-2l4-4z"></path><path d="M470 645c3-10 11-19 18-26-1 2-4 4-4 7h1c-2 2-4 3-5 6-2 3-4 5-6 9-1 1-3 2-4 4z" class="B"></path><path d="M472 627l3-1c-5 8-11 17-12 27-1 0-2 1-2 1l-1 1c1-5 2-10 4-14l8-14z" class="I"></path><path d="M462 673c1-1 0-3 0-4v-9l1 8c1 1 1 2 2 2h1l1 6 3 11v3c2 4 4 9 8 13 0 2 4 5 5 8 1 1 4 8 5 9l1 3c1 1 1 1 1 2v2h1v1 2 2h0c1 1 0 1 1 1l3 6-1 1c1 3 3 6 2 10 0 2 2 6 1 8-1-1-1-2-2-3h0v-5-1c-1-4-2-8-4-11-1-3-2-7-5-9 0-1-1-1-1-2s-1-1-1-2h0-2c-1-4-3-7-5-11l-8-15h0c-1-4-2-8-4-12-1-4-2-9-3-14z" class="d"></path><path d="M469 692c0-1-1-3-1-3-1-3-2-6-2-8v-1c0-2 0-3 1-4l3 11v3c0 1 0 1-1 2z" class="P"></path><path d="M462 673c1-1 0-3 0-4v-9l1 8c1 1 1 2 2 2 0 3 0 7 1 11 0 2 0 4-1 6-1-4-2-9-3-14z" class="G"></path><path d="M469 692c1-1 1-1 1-2 2 4 4 9 8 13 0 2 4 5 5 8 1 1 4 8 5 9l1 3c1 1 1 1 1 2v2h1v1 2l-2-2c-2-1-4-3-5-4-2-4-4-10-7-14-1-4-4-7-6-10v-1c-1-2-1-5-2-7h0z" class="f"></path><defs><linearGradient id="BV" x1="501.451" y1="567.729" x2="493.722" y2="565.325" xlink:href="#B"><stop offset="0" stop-color="#22221d"></stop><stop offset="1" stop-color="#3a373b"></stop></linearGradient></defs><path fill="url(#BV)" d="M484 543l2-1c1 1 3 2 3 3 3 4 6 8 8 12h0c1 3 2 5 3 8 0 2 0 3 1 5l-1 13h-1l-1 1v-1l-3 6-2 3c-2 5-6 10-11 12 1-3 4-4 4-6l-1-1c-1 0-2 1-4 1l2-2v-2c0-1 3-3 4-4v-1c0-1 2-4 3-5l1-4-1-1h1c1-2 2-2 2-4 0-3 0-6-1-9-1-5-3-11-6-16l1-3-3-4z"></path><path d="M497 569c1 2 1 3 1 5 0 1 0 2 1 3l-1 2 1 1v3l-1 1v-1l-3 6-2-3 1-3c2-4 3-9 3-14z" class="F"></path><path d="M494 564l2-2 1 7c0 5-1 10-3 14h-1c2-6 2-12 1-19z" class="B"></path><path d="M484 543l2-1c1 1 3 2 3 3 1 2 2 3 2 4 3 4 4 9 5 13l-2 2c-1-6-3-11-7-17l-3-4z" class="E"></path><defs><linearGradient id="BW" x1="492.862" y1="552.102" x2="487.233" y2="563.701" xlink:href="#B"><stop offset="0" stop-color="#454440"></stop><stop offset="1" stop-color="#6b6966"></stop></linearGradient></defs><path fill="url(#BW)" d="M487 547c4 6 6 11 7 17 1 7 1 13-1 19-1 2-2 3-2 4h-1v-1c1-2 2-4 1-6l-1-1h1c1-2 2-2 2-4 0-3 0-6-1-9-1-5-3-11-6-16l1-3z"></path><path d="M491 580c1 2 0 4-1 6v1h1c0-1 1-2 2-4h1l-1 3 2 3-2 3c-2 5-6 10-11 12 1-3 4-4 4-6l-1-1c-1 0-2 1-4 1l2-2v-2c0-1 3-3 4-4v-1c0-1 2-4 3-5l1-4z" class="Z"></path><path d="M493 586l2 3-2 3-1-4 1-2z" class="M"></path><path d="M491 580c1 2 0 4-1 6v1h1c0-1 1-2 2-4h1l-1 3-1 2c-3 4-5 7-9 8h0v-2c0-1 3-3 4-4v-1c0-1 2-4 3-5l1-4z" class="E"></path><defs><linearGradient id="BX" x1="474.208" y1="562.349" x2="467.36" y2="593.518" xlink:href="#B"><stop offset="0" stop-color="#c6c4c3"></stop><stop offset="1" stop-color="#f6f5f5"></stop></linearGradient></defs><path fill="url(#BX)" d="M461 534c4 3 8 6 12 10l5 4 3 5 1-2 2 3c0-3-2-4-1-7l1 2h2v1c3 5 5 11 6 16 1 3 1 6 1 9 0 2-1 2-2 4h-1l1 1-1 4c-1 1-3 4-3 5v1c-1 1-4 3-4 4v2l-2 2h-4c-3 1-7 0-10-1h-1c-5-1-7-4-10-8-1-2-1-4-2-6v-2c3 4 6 6 10 8 5 1 8 0 12-2l1-1c4-4 6-9 7-14 0-8-3-18-9-24-3-5-9-10-14-13v-1z"></path><path d="M464 589c5 1 8 0 12-2 0 2-1 3-3 4-3 1-6 1-8 0-1 0-1 0-1-1v-1z" class="P"></path><path d="M456 589c3 1 5 3 8 4 2 1 5 2 7 2v1c-1 0-3 0-4 1h-1c-5-1-7-4-10-8z" class="E"></path><path d="M488 574l1 1h0 0v3c0 2 0 4 1 6-1 1-3 4-3 5v1c-1 1-4 3-4 4v2l-2 2h-4c-3 1-7 0-10-1 1-1 3-1 4-1v-1l3-1 6-6c1-1 2-3 4-4 1-1 2-4 3-6 0-1 1-3 1-4z" class="Q"></path><path d="M487 578c1 2 1 3 0 5-2 4-6 9-10 10l-3 1 6-6c1-1 2-3 4-4 1-1 2-4 3-6z" class="T"></path><path d="M487 589v1c-1 1-4 3-4 4v2l-2 2h-4c-3 1-7 0-10-1 1-1 3-1 4-1 7-1 11-2 16-7z" class="B"></path><path d="M483 547l1 2h2v1c3 5 5 11 6 16 1 3 1 6 1 9 0 2-1 2-2 4h-1l1 1-1 4c-1-2-1-4-1-6v-3h0 0l-1-1c0 1-1 3-1 4-1 2-2 5-3 6 0-1 1-2 1-3 3-10 0-20-4-28l1-2 2 3c0-3-2-4-1-7z" class="V"></path><path d="M488 574v-5h1c1 2 0 4 1 5 1 2 1 4 1 5h-1l1 1-1 4c-1-2-1-4-1-6v-3h0 0l-1-1z" class="I"></path><path d="M483 547l1 2h2v1c3 5 5 11 6 16 1 3 1 6 1 9-2-3-1-7-2-10 0-1 0 0-1-1v-1h0v7l-2-10c-2-2-3-4-4-6 0-3-2-4-1-7z" class="M"></path><path d="M483 547l1 2c2 4 3 7 4 11-2-2-3-4-4-6 0-3-2-4-1-7z" class="S"></path><path d="M482 725h2 0c0 1 1 1 1 2s1 1 1 2c3 2 4 6 5 9 2 3 3 7 4 11v1 5h0c1 1 1 2 2 3 1-2-1-6-1-8 1-4-1-7-2-10l1-1 3 9c1 2 2 3 2 4 0 5 2 11 2 16l-3-8c1 4 2 8 1 12h0c0 4-2 8-3 12 0 2-2 4-3 6l-1 1-4 4c0 1 0 0-1 1v-1c-1 0-2-1-2 0l-1 1c-1 1-3 2-4 3-3 1-6 0-8-1s-3-3-4-5h1c0-1-1-2 0-3v-1l-1-1h1 4c3 0 5-1 6-3 1-1 2-3 1-4 0-4-4-7-7-9h-1c3-1 5-2 8-3 1-1 3-3 4-5 0-2 0-3 1-5 0-2 1-4 0-7v-2c0-2 1-3 1-5 1-2 1-3 2-5 0-1-1-3-1-4s-1-3-2-4c-1-2-1-3-4-4v-3z" class="D"></path><path d="M487 745c1-2 1-3 2-5l1 5c0 5-2 12-3 16-1 1-1 1-1 2l-1 1c0-2 0-3 1-5 0-2 1-4 0-7v-2c0-2 1-3 1-5z" class="C"></path><path d="M490 758c1-3 1-5 2-7 0 5 0 9-1 14 0 1-1 3-2 4-1 2-3 4-3 6v1h-1c0-1 0-1-1-1 0-1-1-1-1-2 4-4 6-9 7-14v-1z" class="P"></path><path d="M482 725h2 0c0 1 1 1 1 2s1 1 1 2c3 2 4 6 5 9 2 3 3 7 4 11v1 5 1 4c0 1 0 1 1 2v8c-1 1-1 0-1 1v-3h-1v-3h0l-1 1c0 1 0 1-1 2 0-2 0-2-1-3 1-5 1-9 1-14-1 2-1 4-2 7 0-4 1-9 0-13l-1-5c0-1-1-3-1-4s-1-3-2-4c-1-2-1-3-4-4v-3z" class="E"></path><path d="M491 738c2 3 3 7 4 11v1 5 1 4c0 1 0 1 1 2v8c-1 1-1 0-1 1v-3-3l-1-5c-1-3-1-7-1-10 0-2-1-4-2-7v-5z" class="B"></path><path d="M486 763l1 1c-1 2 0 2-1 4s-4 4-6 5h1c1 1 2 2 2 4 1 0 2 3 2 3 0 1-1 2-1 3-1 3-2 5-4 8l-4 4h-2c-2-1-3-4-4-6l-1-1h1 4c3 0 5-1 6-3 1-1 2-3 1-4 0-4-4-7-7-9h-1c3-1 5-2 8-3 1-1 3-3 4-5l1-1z" class="P"></path><path d="M495 755h0c1 1 1 2 2 3 1-2-1-6-1-8 1-4-1-7-2-10l1-1 3 9c1 2 2 3 2 4 0 5 2 11 2 16l-3-8c1 4 2 8 1 12h0c0 4-2 8-3 12 0 2-2 4-3 6l-1 1-4 4c0 1 0 0-1 1v-1l1-2-1-1 1-1c1-1 1-2 1-3v-3c1-3 3-6 3-10l1-2v-5h1v3c0-1 0 0 1-1v-8c-1-1-1-1-1-2v-4-1z" class="G"></path><path d="M545 198c10 6 21 12 28 21-9 11-14 25-17 39-2 13-2 27-2 40v55l1 159c18-21 26-46 39-70 2 11 6 22 12 32l1-1c-1-1-1-2-2-3-1-3-1-5-2-8 1 2 2 3 2 4 2 1 2 2 3 4v-3c2 2 4 5 6 8 1 2 3 4 4 7 1 1 2 3 3 4l-1 2 4 3c1-1 3 0 4 0l4 3c2 2 6 4 8 6 10 4 20 6 31 8h2c2 1 5 1 6 3-27 3-55 12-72 34-13 17-17 37-14 57 2 14 7 27 16 38h0c-4-1-17-15-20-19l1-1-9-11c1-1 2-1 3-1 1 2 2 3 3 5 1 1 1 1 2 1l1-1-2-2c-4-13-5-26-1-39 5-18 13-33 28-45 4-3 8-5 12-7l15-7 9-2c-22-5-41-16-53-35l-6-15c-7 15-13 30-23 43-2 3-4 6-6 8l-6 7c-1 0-2 1-2 2-1 1 1 6 1 8 2 4 5 9 8 12s7 5 11 7c-10 7-17 14-19 26-2 11-2 23-2 34v46 59c0 19 0 37 3 55 3 17 10 35 20 49 13 20 32 35 53 46 5 2 10 5 16 7v-2l4 2 1-1c3 1 9 3 11 6l16 5c5 1 11 2 16 4h-1c-2 0-5 0-7-1l-19-4-74-19c-15-4-30-9-44-16-13-6-25-14-35-23-4-4-7-8-10-11-26 30-61 44-99 54l-45 12-32 7c0-1 1-1 1-1 1-1 4-1 6-2l10-3c2-2 2-2 4-3 9-2 15-7 23-11l2 1v1l23-14 3-3c0-3 4-6 6-8 3-3 5-7 8-10l1 1c-2 2-4 3-5 6h1c3-3 6-9 10-10 4-3 6-10 10-14v-2-1c5-9 8-20 9-30 1-4 1-7 2-11v-1c0-2 0-5-1-7 0-3 0-4-3-6v-3c-2-4-3-9-3-14l-1-16c0-9 0-19 1-28l1 3c2-12 1-24-2-36l-3-14v-4c-1-1-1-2-1-3l1 1 1-1c0-1-1-3-1-5l3-10 2 1 1 2c1-3 0-7 1-10l1 4c1 2 1 3 3 4v5c2-3 0-7 1-10 1-1 1-4 1-6 0-5 0-11 1-17 0 2 1 2 1 3v5-1-2-2-1l1-5c0-12 1-26 0-38-3-13-13-26-24-34-3-2-8-4-11-7h0c7-1 13 2 18 5 14 8 23 25 24 41 0 6-1 13 0 18v1 2 22 95 59c-1 12-2 25-5 36-5 18-14 34-27 47-5 5-11 9-17 13 23-5 43-12 63-24 13-8 25-17 33-30 1-1 2-3 3-5 1 3 3 5 5 8 5 7 11 13 18 18 11 9 24 17 38 22 13 5 27 9 41 13-3-2-6-4-8-6-4-3-7-6-10-9-17-16-30-40-36-63-3-12-4-25-4-38h0c-1-5 0-13 0-18v-51-65c-1-2 0-5 0-7v-14c0-8 1-18 5-26 3-4 6-8 10-11-8-6-13-13-14-23-2-7-1-15-1-22v-26-103-77c0-12-1-26 1-38 2-15 8-29 16-41-7-6-14-12-22-17l2-4z" class="Y"></path><path d="M646 868l4 2 1-1c3 1 9 3 11 6-5-1-11-3-16-5v-2z" class="S"></path><path d="M624 491c1-1 3 0 4 0l4 3c2 2 6 4 8 6-4 0-9-3-12-5v-1c-1-1-3-2-4-3z" class="L"></path><path d="M397 851h1l3-1v1c-8 7-19 14-29 17l2-3 23-14z" class="W"></path><path d="M349 874c9-2 15-7 23-11l2 1v1l-2 3c-8 3-17 8-27 9 2-2 2-2 4-3z" class="J"></path><path d="M445 598c0 2 1 2 1 3v5-1-2-2-1l1-5v45c0 7 1 14 0 21-1 0-1-1-1-2 0-4 2-12-1-16v-10l-1-18c0-5 0-11 1-17z" class="M"></path><path d="M603 462c1 2 2 3 2 4 2 1 2 2 3 4v-3c2 2 4 5 6 8 1 2 3 4 4 7 1 1 2 3 3 4l-1 2 4 3c1 1 3 2 4 3v1c-8-5-17-12-22-21l1-1c-1-1-1-2-2-3-1-3-1-5-2-8z" class="C"></path><path d="M608 467c2 2 4 5 6 8 1 2 3 4 4 7 1 1 2 3 3 4l-1 2c-1-2-3-3-5-5 0-3-1-4-3-6l-4-7v-3z" class="W"></path><path d="M444 615l1 18v10c3 4 1 12 1 16 0 1 0 2 1 2v22h-2v-3l-1-11c0-3 0-7-1-10l-1-28c2-3 0-7 1-10 1-1 1-4 1-6z" class="b"></path><path d="M445 643c3 4 1 12 1 16 0 1 0 2 1 2v22h-2v-3l-1-11 1-26z" class="O"></path><path d="M421 827c4-3 6-10 10-14l-1 6h1c-8 13-18 23-30 32v-1l-3 1h-1l3-3c0-3 4-6 6-8 3-3 5-7 8-10l1 1c-2 2-4 3-5 6h1c3-3 6-9 10-10z" class="F"></path><path d="M415 831c-2 2-4 3-5 6h1c3-3 6-9 10-10-2 3-4 6-7 8l-7 7h1c2-1 12-12 13-12-1 4-16 17-20 19v1l-3 1h-1l3-3c0-3 4-6 6-8 3-3 5-7 8-10l1 1z" class="I"></path><path d="M548 607v22c0 1 1 2 1 3v24c0-2 0-4 1-5 0-3-1-7 0-9v-2c1 7-1 16 0 23v5c0 2 0 3 1 5v7 3c0 6 1 13 0 20v4l-1 21c0 4 0 10-1 14v2c0-1 0-2-1-3h0c-1-5 0-13 0-18v-51-65z" class="T"></path><path d="M548 672c1 3 2 5 2 8 0 12 0 24-1 36 0 2-1 3-1 4v14 7c-1-5 0-13 0-18v-51z" class="D"></path><path d="M447 683l1 53c0 19-1 39-6 57-2 9-7 18-11 26h-1l1-6v-2-1c5-9 8-20 9-30 1-4 1-7 2-11v-1c0-2 0-5-1-7 0-3 0-4-3-6v-3l1-1c-1-1-1-1 0-2 1-3 1-12 1-16l1-19h1 0v-3h1c1-3 1-5 1-8l1-20h2z" class="i"></path><path d="M444 703c1 4 2 14 0 18-1-3-1-7-1-10 1-3 1-5 1-8z" class="O"></path><defs><linearGradient id="BY" x1="446.975" y1="733.307" x2="436.525" y2="730.193" xlink:href="#B"><stop offset="0" stop-color="#373831"></stop><stop offset="1" stop-color="#5f5a5c"></stop></linearGradient></defs><path fill="url(#BY)" d="M442 711h1c0 3 0 7 1 10l-1 34v8c0 2-1 3-1 5h0c0-2 0-5-1-7 0-3 0-4-3-6v-3l1-1c-1-1-1-1 0-2 1-3 1-12 1-16l1-19h1 0v-3z"></path><path d="M439 751c0 2 0 2 1 3 1-1 1-3 1-4 1 0 1 1 1 2v2l1 1v8c0 2-1 3-1 5h0c0-2 0-5-1-7 0-3 0-4-3-6v-3l1-1z" class="K"></path><defs><linearGradient id="BZ" x1="414.682" y1="687.958" x2="457.365" y2="662.119" xlink:href="#B"><stop offset="0" stop-color="#b2b4b1"></stop><stop offset="1" stop-color="#e7e3e3"></stop></linearGradient></defs><path fill="url(#BZ)" d="M438 618l1 4c1 2 1 3 3 4v5l1 28c1 3 1 7 1 10l1 11v3l-1 20c0 3 0 5-1 8h-1v3h0-1l-1 19c0 4 0 13-1 16-1 1-1 1 0 2l-1 1c-2-4-3-9-3-14l-1-16c0-9 0-19 1-28l1 3c2-12 1-24-2-36l-3-14v-4c-1-1-1-2-1-3l1 1 1-1c0-1-1-3-1-5l3-10 2 1 1 2c1-3 0-7 1-10z"></path><path d="M439 688h1c0 1 0 2-1 3 0 3 1 6 1 8l1 15-1 19-1-11v-6c0-7-1-14 0-21v-7z" class="i"></path><path d="M435 694l1 3v30c1 0 1 0 1-1v-1h1c0 1 1 2 1 3v-6l1 11c0 4 0 13-1 16-1 1-1 1 0 2l-1 1c-2-4-3-9-3-14l-1-16c0-9 0-19 1-28z"></path><path d="M439 722l1 11c0 4 0 13-1 16l-3-22c1 0 1 0 1-1v-1h1c0 1 1 2 1 3v-6z" class="L"></path><defs><linearGradient id="Ba" x1="447.861" y1="701.557" x2="434.139" y2="686.943" xlink:href="#B"><stop offset="0" stop-color="#8a857e"></stop><stop offset="1" stop-color="#b0b0b3"></stop></linearGradient></defs><path fill="url(#Ba)" d="M439 667c0 2 1 3 1 4 1 3 2 7 2 10h1 1l1-1v3l-1 20c0 3 0 5-1 8h-1v3h0-1l-1-15c0-2-1-5-1-8 1-1 1-2 1-3h-1v-1-1c1-2 1-11 0-13s0-4 0-6z"></path><path d="M445 680v3l-1 20c0 3 0 5-1 8h-1v-30h1 1l1-1z" class="b"></path><path d="M438 618l1 4c1 2 1 3 3 4v5l1 28c1 3 1 7 1 10l1 11-1 1h-1-1c0-3-1-7-2-10 0-1-1-2-1-4v-5c-1-5-2-9-2-14-1-7-1-14 0-20 1-3 0-7 1-10z" class="F"></path><path d="M443 659c1 3 1 7 1 10l1 11-1 1h-1c0-3-1-6-1-10-1-4-1-7 0-11v7h1v-8z" class="V"></path><path d="M439 622c1 2 1 3 3 4v5l1 28v8h-1v-7l-3-38z" class="Q"></path><path d="M646 870c-6-2-11-5-16-7-21-11-40-26-53-46-10-14-17-32-20-49-3-18-3-36-3-55v-59-46c0-11 0-23 2-34 2-12 9-19 19-26-4-2-8-4-11-7s-6-8-8-12c0-2-2-7-1-8 0-1 1-2 2-2l6-7c2-2 4-5 6-8 10-13 16-28 23-43l6 15c12 19 31 30 53 35l-9 2-15 7c-4 2-8 4-12 7-15 12-23 27-28 45-4 13-3 26 1 39l2 2-1 1c-1 0-1 0-2-1-1-2-2-3-3-5-1 0-2 0-3 1l9 11-1 1c3 4 16 18 20 19h0l2 1-3 1-13-4c0 36-2 74 3 111 6 36 21 74 47 100 10 10 22 19 34 26l16 8-1 1c-5-2-11-3-16-4l-16-5c-2-3-8-5-11-6l-1 1-4-2v2z" class="i"></path><path d="M627 511l-3-5c3 1 6 3 9 4h-6v1z" class="H"></path><path d="M587 530h0c-1 1-3 2-5 2v-2c2-1 3-2 5-3v3z" class="D"></path><path d="M568 520c0 1 1 2 0 3v1c-2 1-2 3-3 5v1c-1-1-1-2-1-3 0-3 2-6 4-7z" class="U"></path><path d="M569 524c1-1 0-2 0-4h1l1 2h0c1 3 2 5 4 8h-1c-2-2-4-3-5-6z" class="c"></path><path d="M588 496c3-3 7-3 11-3l-3 5c0-1-1-2-2-2-2-1-2 0-4 1 0-1-1-1-2-1z" class="B"></path><path d="M639 859c3 2 9 5 10 9-3-1-7-3-10-5 0-2 0-2-1-3l1-1z" class="e"></path><path d="M611 523h2l-7 8-2 1c0-2 1-5 2-7 2-2 3-2 5-2z" class="V"></path><path d="M606 505c1 0 1-1 2-2 5-2 8-2 13-1-2 1-5 1-8 2-2 1-2 1-3 3h-1-3l2-2h-2z" class="G"></path><defs><linearGradient id="Bb" x1="567.02" y1="572.013" x2="566.98" y2="579.503" xlink:href="#B"><stop offset="0" stop-color="#62615f"></stop><stop offset="1" stop-color="#787775"></stop></linearGradient></defs><path fill="url(#Bb)" d="M564 574l1-2h0c1 0 1 0 2 1l2 2 2 4-1 1c-2-1-2-1-4 0v1l-2-7z"></path><path d="M582 818c-2-2-5-6-6-10l-2-4-1-1c0-1 0-1 1-2 0 1 0 1 1 1l5 10 3 5v2h0l-1-1z" class="h"></path><path d="M590 497c2-1 2-2 4-1 1 0 2 1 2 2 1 1 2 2 3 2l2 1v1c-2 1-3 1-5 1h-1l-1-2v-1l-2-2c-1 0-1 0-2-1z" class="X"></path><path d="M599 500h4c1 2 2 4 3 5h2l-2 2h-3l-1-1c0-1 0-1-2-2-2 1-1 3-3 4-1-2 0-3-1-5 2 0 3 0 5-1v-1l-2-1z" class="P"></path><path d="M573 510l-1-1c1-3 2-5 3-7v1c2 3 4 5 7 8l3 3h-2c-3-2-6-2-9-6v-1l-1 1 1 1-1 1z" class="I"></path><path d="M627 511v-1h6c2 1 4 2 6 2-3 3-8 4-12 4h-1l1-2c1-1 1-2 0-3z" class="M"></path><path d="M639 512c1 0 1 0 2 1h1l-15 7c-1-2-2-2-3-3l-2 1c-1-1-2-1-3-2h0 7 1c4 0 9-1 12-4z" class="e"></path><path d="M575 526l1 2h1l2 15c-3-1-6-2-7-4l4-4-2-5h1c1-2 0-3 0-4z" class="J"></path><path d="M589 817c7 9 14 18 23 26l-1 1v1l-2-2c-1-1-5-3-5-5v-1c-4-4-9-10-12-14l-4-5 1-1z" class="F"></path><defs><linearGradient id="Bc" x1="564.906" y1="561.484" x2="565.354" y2="572.587" xlink:href="#B"><stop offset="0" stop-color="#373635"></stop><stop offset="1" stop-color="#5d5d5b"></stop></linearGradient></defs><path fill="url(#Bc)" d="M564 574c-1-3-3-9-1-12 0-1 1-1 2-2h1l3 15-2-2c-1-1-1-1-2-1h0l-1 2z"></path><defs><linearGradient id="Bd" x1="604.814" y1="837.055" x2="604.701" y2="844.938" xlink:href="#B"><stop offset="0" stop-color="#3f3e3f"></stop><stop offset="1" stop-color="#5a5952"></stop></linearGradient></defs><path fill="url(#Bd)" d="M595 831h1 1l7 7c0 2 4 4 5 5l2 2v1h-2l-6-5c-4-3-8-6-11-10 1 0 1 1 3 0z"></path><path d="M591 518l1-1c3 2 5 4 7 6 1 6 2 9-1 16v-2h1l-2-1 1-1v-2l-1 1c-1 3-2 8-5 10l4-9c1-2 1-4 1-6 0-5-3-8-6-11z" class="Z"></path><path d="M568 520c1-1 3-2 4-3 3 0 6 1 9 2v-1c2 1 4 1 5 3h0-3l-1-1c-1 0-2 0-3 1-1 2-3 3-4 5 0 1 1 2 0 4-2-3-3-5-4-8h0l-1-2h-1c0 2 1 3 0 4h-1v-1c1-1 0-2 0-3z" class="g"></path><path d="M571 522c2-1 3-3 5-3 1 1 2 1 3 2-1 2-3 3-4 5 0 1 1 2 0 4-2-3-3-5-4-8z" class="Z"></path><path d="M574 509l-1-1 1-1v1c3 4 6 4 9 6h2c1 1 3 3 4 3l2 1c3 3 6 6 6 11 0 2 0 4-1 6 0-2 0-4-1-6l-1-2-1-1v-3c-1-1-2-2-4-3s-4-3-6-4c-3-3-7-3-9-7z" class="J"></path><path d="M587 555h2l-1 2c0 1-1 3-1 4v1c-1 1-1 2-1 4v2c-2 2-2 4-3 6-1 1-2 3-3 4v2c0 1-1 2-2 2l-1-3c1-2 1-3 2-4 1-2 2-2 3-4-2 0-2 0-3 1h-2l-1-1c1-1 2-1 4-2h0 1c2-1 3-3 3-5l2-4v-2c1-1 1-2 1-3z" class="h"></path><path d="M574 509c2 4 6 4 9 7 2 1 4 3 6 4s3 2 4 3v3l-2-2c-1 0-2-1-3-2-1 0-1 0-2-1h0c-1-2-3-2-5-3-2 0-6-2-8-3s-3-2-3-4v-1l3 2 1-1-1-1 1-1z" class="D"></path><path d="M579 521c1-1 2-1 3-1l1 1h3c1 1 1 1 2 1 1 1 2 2 3 2l2 2 1 1c-2 1-2 1-2 3h-5v-3-1c-1-1-3-2-5-1-2 0-4 2-5 3h-1l-1-2c1-2 3-3 4-5z" class="G"></path><path d="M606 507h3 1l-1 5c0 2 0 2 2 4 2 0 4 0 6 1h1c-4 1-9 3-12 2 0-1 0-1-1-2v-1l1-1c-1 0-2-1-3-2-1-2-1-4 0-6h3z" class="a"></path><path d="M606 507h3c-2 3-1 5-1 8h-2c-1 0-2-1-3-2-1-2-1-4 0-6h3z" class="D"></path><path d="M621 502l2 1h0c-3 3-7 2-9 6v1c1 2 3 4 5 6h0l-1 1h-1c-2-1-4-1-6-1-2-2-2-2-2-4l1-5c1-2 1-2 3-3 3-1 6-1 8-2z" class="C"></path><path d="M609 512h1c0-3 1-4 3-5l1 1c-2 1-2 2-3 4l1 1c1-1 2-1 2-3 1 2 3 4 5 6h0l-1 1h-1c-2-1-4-1-6-1-2-2-2-2-2-4z" class="F"></path><path d="M592 530c0-2 0-2 2-3l1 2c1 2 1 4 1 6l-4 9c-6 6-13 9-19 13 4-5 12-8 16-14 2-2 3-5 3-7 1-2 1-4 0-6z" class="D"></path><path d="M592 530c0-2 0-2 2-3l1 2c0 2 0 5-2 6l-1 1c1-2 1-4 0-6z" class="B"></path><path d="M594 501l1 2h1c1 2 0 3 1 5 2-1 1-3 3-4 2 1 2 1 2 2l1 1c-1 2-1 4 0 6 1 1 2 2 3 2l-1 1v1c1 1 1 1 1 2-3-1-5-2-7-4h0l-2-2h-1c-2-2-4-5-5-8l2-2 1-2z" class="V"></path><path d="M597 508c2-1 1-3 3-4 2 1 2 1 2 2-2 3-1 6-1 10-1-3-2-5-3-7l-1-1z" class="L"></path><path d="M593 503c2 2 2 5 4 7v3h-1c-2-2-4-5-5-8l2-2z" class="E"></path><path d="M638 860c-20-12-37-28-50-47-1-1-1-2-1-3 4 7 12 15 18 21 11 10 22 20 34 28l-1 1z" class="M"></path><defs><linearGradient id="Be" x1="625.673" y1="848.598" x2="622.384" y2="857.743" xlink:href="#B"><stop offset="0" stop-color="#504d50"></stop><stop offset="1" stop-color="#6a6a65"></stop></linearGradient></defs><path fill="url(#Be)" d="M612 843c8 7 18 15 27 20 3 2 7 4 10 5l2 1-1 1-4-2c-8-3-15-7-22-11-5-3-10-7-15-11h2v-1-1l1-1z"></path><path d="M580 812v-3c1-1 0-1 0-1-2-3-3-5-4-8v-2s1 1 1 2l9 13c1 1 3 3 3 4l-1 1 4 5c3 4 8 10 12 14v1l-7-7h-1-1c-2 1-2 0-3 0l-10-13 1 1h0v-2l-3-5z" class="H"></path><path d="M582 818l1 1h0v-2c4 5 7 10 12 14-2 1-2 0-3 0l-10-13z" class="O"></path><path d="M585 508c0-4 0-9 3-12 1 0 2 0 2 1 1 1 1 1 2 1l2 2v1l-1 2-2 2c1 3 3 6 5 8h1l2 2h0-1c-1 0-1 0-1-1-2 1-2 1-3 2l-2 1-1 1-2-1c-1 0-3-2-4-3l-3-3v-1c1-1 2-1 3-2z" class="T"></path><path d="M590 515l4 1h0l-2 1-1 1-2-1c0-1 0-1 1-2z" class="O"></path><path d="M585 508c0 2 1 4 2 5l3 2h0c-1 1-1 1-1 2-1 0-3-2-4-3l-3-3v-1c1-1 2-1 3-2z" class="M"></path><path d="M594 516v-1c-3-2-5-5-6-9 0-2 0-5 1-6v1c1 1 1 2 1 3l1 1c1 3 3 6 5 8h1l2 2h0-1c-1 0-1 0-1-1-2 1-2 1-3 2h0z" class="C"></path><defs><linearGradient id="Bf" x1="572.995" y1="578.621" x2="577.391" y2="608.603" xlink:href="#B"><stop offset="0" stop-color="#767575"></stop><stop offset="1" stop-color="#bcbaba"></stop></linearGradient></defs><path fill="url(#Bf)" d="M571 579c4 11 9 22 16 32h1l2 2-1 1c-1 0-1 0-2-1-1-2-2-3-3-5-1 0-2 0-3 1l9 11-1 1h0c-10-11-18-26-23-40v-1c2-1 2-1 4 0l1-1z"></path></svg>