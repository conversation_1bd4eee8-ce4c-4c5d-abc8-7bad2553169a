<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="157 97 734 828"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#3b3433}.C{fill:#393434}.D{fill:#231f1f}.E{fill:#262526}.F{fill:#6a5b59}.G{fill:#806c69}.H{fill:#423a39}.I{fill:#5e504e}.J{fill:#dbc5ac}.K{fill:#eee4d6}.L{fill:#453f3e}.M{fill:#c0a68f}.N{fill:#564746}.O{fill:#4b3d3c}.P{fill:#d0b6a4}.Q{fill:#7f6763}.R{fill:#736361}.S{fill:#4e4847}.T{fill:#2b2828}.U{fill:#65504e}.V{fill:#a3817d}.W{fill:#947b77}.X{fill:#b59791}.Y{fill:#e8d9cb}.Z{fill:#937a76}.a{fill:#ac8e89}.b{fill:#d4beaa}.c{fill:#343030}.d{fill:#8b736f}.e{fill:#564d4c}.f{fill:#211f20}.g{fill:#050505}.h{fill:#f3ede4}.i{fill:#efe2d3}.j{fill:#6f5c5a}.k{fill:#c1a7a0}.l{fill:#121213}.m{fill:#765a57}.n{fill:#cca9a2}.o{fill:#f7f5f0}.p{fill:#d7bfa3}.q{fill:#9c8581}.r{fill:#e8d6c2}.s{fill:#c6ab8e}.t{fill:#877673}.u{fill:#a28e89}.v{fill:#0a0a0a}.w{fill:#dfc2ba}.x{fill:#fff}</style><path d="M538 181c1 1 1 2 1 4h0-3l2-4z" class="r"></path><path d="M403 158l3-1c1 1 2 1 2 2v3h0l-1-2h-1c-1-1-2-1-3-2z" class="H"></path><path d="M533 157c-1-3-1-5 0-8h0c2 2 1 5 1 8h-1z" class="o"></path><path d="M541 175c0 3 0 7-1 9-1 0-1 0-1 1 0-2 0-3-1-4 1-2 1-3 1-5l2-1z" class="K"></path><path d="M750 368c3 0 5 0 7 1h0c-1 1-2 2-3 1h-2-2v-2z" class="h"></path><path d="M614 673c-1 0-3-1-5-1l8-4c-1 1-2 2-3 4v1z" class="J"></path><path d="M412 742c2-1 4-2 6-4-1 3-3 5-3 8h-1c0-2-1-2-2-4z" class="i"></path><path d="M543 791h1c1 1 2 2 2 3 0 2 0 2-1 3h-1c-1-2-1-4-1-6z" class="Y"></path><path d="M408 159l3 2h0c0 1 0 2 1 3l1 1-2 1-3-4v-3z" class="C"></path><path d="M723 522c3 0 4 0 6 1l2 2c-2 1-5 0-7-1l-1-2z" class="h"></path><path d="M660 705c1 0 2 0 3 1v3l-2 1c-1-1-2-1-3-2 1-2 1-2 2-3z" class="J"></path><path d="M395 160l8-2c1 1 2 1 3 2h1c-2 0-3 0-4 1-3 0-6 0-8-1z" class="O"></path><path d="M719 350c2 0 4 1 6 2 1 0 3 1 4 1l1 3-1 1c-2-2-3-3-5-3-1-2-4-3-5-4z" class="Y"></path><path d="M392 160h3c2 1 5 1 8 1l-2 1h-2-3-4l-1 1c-1 0-2 0-2 1l-1-1c1-1 1-1 2-1h1 0l-1-1 2-1z" class="B"></path><path d="M286 362h-4c-2-1-3-1-5-2h-1 0l1-1c2-1 5-1 7 0v1s2 1 2 2z" class="Y"></path><path d="M844 194c1 0 3 1 4 2-1 1-3 2-5 2l-1-1h-1l-1-1h0l-6-1c4-1 7 1 10-1z" class="T"></path><path d="M390 601c2 2 4 4 7 5v4c-1-1-1-1-1-2v-1h-1l-1 1c-2-1-4-3-5-4l1-3z" class="i"></path><path d="M357 433c1 0 2-1 4 0 1 1 3 4 4 6h0-1c-2-1-4-3-7-3l1-1h1l-2-2z" class="s"></path><path d="M367 698h2l1 1c1 1 1 2 1 3l-2 1h-2l-2-2c1-1 1-2 2-3z" class="h"></path><path d="M550 782c0-2 0-2 1-4 2-1 3-2 4-2 2 0 2 1 4 1l-1 2h-1-3c-1 1-2 2-2 3h-2z" class="K"></path><path d="M691 414l2 1c0-2 2-3 3-4 0 4 1 5-1 8h-1-2v-2h0c-1-1-1-2-1-3z" class="h"></path><path d="M644 721c1 2 3 3 3 5-1 1-2 1-4 1h-3v-1l4-5z" class="i"></path><path d="M324 598c-4 1-11 1-15 0v-1l7-2h2v1 1c2 1 4 1 6 1z" class="s"></path><path d="M311 317l8-14 1-1c1 1-1 3-2 5-2 4-4 9-6 14 0-1 0-3-1-4z" class="J"></path><path d="M618 696h3l3 3-3 3c-1 0-1 0-2-1h-1l-1 1v-1c0-2 0-3 1-5z" class="p"></path><path d="M336 404c2 1 3 3 5 3 0 1 1 2 1 3l-1 1h-3c-2-1-3-2-3-3 1-2 1-3 1-4z" class="K"></path><path d="M650 667l1-1h1c3 2 5 3 8 3 0 1-1 2-2 2-3 3-7 5-10 6l2-2s1-1 2-1h1c1-1 1-2 3-2v-1-1c-2 0-3 0-4-1v-1l-2-1z" class="J"></path><path d="M702 156c-1 2-1 3 0 5-2 1-3 2-5 3h0v-2h0l-1-1c-1 0-1-1-2-1v-1l8-3z" class="f"></path><path d="M716 496c3 5 4 10 10 12h1l-2 2c-2 0-5-4-7-5v-2c-1-2-2-4-3-7h1z" class="i"></path><path d="M431 713c4-2 8-4 12-5l1 1h0c-3 1-6 2-9 4s-5 4-8 5h0c0-1 0-2 1-3 0-1 2-1 3-2z" class="b"></path><path d="M755 168c4 2 7 4 11 6-2 1-3 1-5 1s-2 0-4-2l-1-1-2-1v-2l1-1z" class="D"></path><path d="M607 786c2 4 7 8 10 12 1 1 1 1 0 3h-1c0-2-1-3-2-4-1-2-5-8-7-9l-1 1c-1 1-1 2-2 3h-2v-1c1-2 3-3 5-5z" class="K"></path><path d="M533 157h1c0 7 3 12 7 18l-2 1c-2-3-5-7-5-11-1-2-1-5-1-8z" class="i"></path><path d="M650 631c1 2 1 3 1 5-1 6-6 11-10 16l-1-2c0-2 2-3 3-4h0c2-2 6-6 7-9v-1-5z" class="J"></path><path d="M686 425h1c2 3 3 4 5 6s4 5 6 8h-2l-3-3c-2-3-4-4-6-7 0-1-1-2-1-4z" class="Y"></path><path d="M714 370c-1-1-2-1-1-3 2 1 4 2 6 2 1 0 2 1 3 1l1 1-1 1c-2-1-4-2-6-1l1 1c0 1 0 2 1 3s1 2 2 3c0 2 0 5-2 7h0c1-4 1-6-1-9v-1c-1-2-2-3-3-5z" class="p"></path><path d="M607 691h2l1 1c1 1 2 2 2 4-1 1-2 1-3 2-1 0-1 0-2-1-1 0-1 0-2-1 0-3 0-3 2-5z" class="h"></path><path d="M766 174h9c2 0 7-2 8-1 1 0 3 2 4 3-1 0-2-1-3 0h1v2l-3-4-2 1h-14-5c2 0 3 0 5-1z" class="T"></path><path d="M323 332l1 1c0 1 0 1-1 2v2c-3 1-5 3-6 5v1h-1v1h-1v-2c1-4 4-7 8-10z" class="D"></path><path d="M729 353c2 2 3 5 5 7l3-4c-1 2-2 4-2 6v1c0 2 1 3 3 4h-1c-4-1-4-4-6-7s-5-4-8-6h1c2 0 3 1 5 3l1-1-1-3z" class="K"></path><path d="M351 702l2-1c2 0 3 2 4 3 0 2-1 3-2 4s-1 1-2 1-2-1-2-1c-1-2-1-3-1-5l1-1z" class="h"></path><path d="M247 170c6 1 11 1 17 0 3 0 7-1 10-2-1 1-1 2-2 2h-1c-2 1-4 2-6 2-4 1-7 1-11 0-2 0-5 0-7-2z" class="T"></path><path d="M331 579l1 3c-4 5-9 8-12 13h-2-2c2-3 6-5 9-8 2-3 4-5 6-8z" class="J"></path><path d="M398 629l2 7-1 1v2l-2-1c-1 1-2 3-3 4h-3l5-9c0-1 1-3 2-4z" class="p"></path><path d="M397 638v-1c0-1 0-2 1-2l1 2v2l-2-1z" class="J"></path><path d="M311 317c1 1 1 3 1 4-1 3-1 7-2 10l-1 1c-2 0-3 2-5 3 1-3 4-4 4-6-1-3 0-5 2-8l-1-1-1 1h-1c1-2 3-3 4-4z" class="i"></path><path d="M447 762c1-1 2-1 2-2 4-3 6-6 7-10 0 1 1 1 0 2h1l1 1h0c-1 1-1 2-1 2-2 5-5 8-9 10l-3 2c1-2 2-3 2-5z" class="Y"></path><path d="M279 294l14-18 2 1h1l-1 1v1c-2 2-4 3-6 5 0 2-2 2-2 3l-2 3c-3 0-4 4-6 5v-1z" class="l"></path><path d="M520 181c0-5 3-15 7-19h1 3v1h-3c-4 4-4 12-6 17 0 1 0 2 1 3h-3v-1-1z" class="i"></path><path d="M308 129c2 0 5 0 7 1l15 3v4c-2-2-2-2-4-2-3-2-6-2-10-3-3 0-5-1-8-1v-1-1z" class="c"></path><path d="M313 359h2c2 1 1 1 3 1 2 3 6 3 9 3 2 2 4 7 5 10-1-2-3-5-5-6h0c-2-1-4-1-6-2s-4-2-5-3-2-2-2-3h-1z" class="J"></path><path d="M601 223c1-2 3-3 5-4 4-2 7-7 10-10l-1 4v2c-1 1-2 3-3 4-1 0-2 0-4 1-1 1 1 0-1 0-2 1-4 4-5 5-1-1-2-1-2-1l1-1z" class="b"></path><path d="M406 793c1 7 1 17 0 23-1-6-2-13-4-19-1-3-3-5-3-7h0c1 2 2 5 4 6h1l1-3h1z" class="J"></path><path d="M394 642c-1 2-2 3-3 5-1 0-2 1-2 2-2 4-3 7-4 11-1 2-2 4-2 6-1 2 0 4 0 5-1 0-1 0-1-1v-4c0-4 1-10 3-15 2-3 4-5 6-8v-1h3z" class="K"></path><path d="M622 230l1 2c1 1 2 1 2 1l-5 5-3 3h-2c-2 0-2-1-2-2l-1-1c3-3 7-4 10-8z" class="M"></path><path d="M613 239c1-1 2-3 4-3 1-1 2 0 3 0v2l-3 3h-2c-2 0-2-1-2-2z" class="Y"></path><path d="M564 764c4 0 10 5 14 7l5-3h1l-1 2c-1 3-2 4-4 5-5-1-12-7-15-11zm53-96l4-5c0 1-1 3 0 4l-2 2h0v4c4 1 7 6 10 8v2c-4-4-9-7-15-10v-1c1-2 2-3 3-4z" class="P"></path><path d="M619 673c-1-1-2-1-3-2l2-2h1 0v4z" class="M"></path><path d="M559 777c1 2 1 3 2 4 0 2-2 3-3 4-2 2-3 2-5 1-1-1-2-2-3-4h2c0-1 1-2 2-3h3 1l1-2z" class="J"></path><path d="M552 782c0-1 1-2 2-3h3v3c-1 2-1 3-3 3-1-1-2-1-2-3z" class="o"></path><path d="M336 476c1 1 1 3 2 5 0 2 1 3 0 5v10l-2-2c-1-7-2-12-1-18h1 0z" class="h"></path><path d="M373 151v1c-1 1-2 2-2 3l-7 4-4 1c-1-1-3-1-4-1-2 0-4 0-6-1h0c8-1 16-3 23-7z" class="D"></path><path d="M308 131c3 0 5 1 8 1 4 1 7 1 10 3h-4s-2 1-1 2c-1 0-1 0-1 1h-1c-2-1-3-2-6-3v1l-1 1-1-1h0l-2-2c-1 0-1 0-2-1l1-2z" class="P"></path><path d="M313 135c-1 0-1-1-1-1v-1l10 2s-2 1-1 2c-1 0-1 0-1 1h-1c-2-1-3-2-6-3z" class="D"></path><path d="M424 708c1 0 1 0 2 1s2 1 2 3c1 1 1 1 2 1h1c-1 1-3 1-3 2-1 1-1 2-1 3h0l-1 1-2-1-2-2-1-1c-1-1-1-2-1-4 1-1 2-2 4-3z" class="i"></path><path d="M422 716c1 0 1-1 1-1h2 3c-1 1-1 2-1 3h0l-1 1-2-1-2-2z" class="J"></path><path d="M424 708c1 0 1 0 2 1s2 1 2 3c1 1 1 1 2 1h1c-1 1-3 1-3 2h-3c0-2 1-2 1-4l-2-1 1-1-1-1z" class="r"></path><path d="M279 330c4-2 7-3 10-5 4-3 7-5 10-8-2 5-5 10-10 13l-2-1-2 1-6 2-1-1h2l-1-1z" class="D"></path><path d="M686 624c1-2 1-2 3-3 2 0 3 0 4 1s2 2 3 4l-3 3c-2 1-3 0-4 0-2-1-3-2-3-3-1-1 0-1 0-2z" class="K"></path><path d="M686 624c1-1 2-1 3-1 2 1 2 1 2 2l1 3 1 1c-2 1-3 0-4 0-2-1-3-2-3-3-1-1 0-1 0-2z" class="o"></path><path d="M308 505h0v3c1 1 1 3 0 4 0 2-2 4-3 4-2 1-4 0-6-1-5-2-13 1-18 3l-1-1h-1l2-1c7-3 15-4 23-2 2-1 3-4 4-6v-3z" class="J"></path><path d="M279 294v1c2-1 3-5 6-5h0l-1 1c-1 1-1 2-2 3l1 1c-5 6-10 12-16 17 3-6 7-13 12-18z" class="E"></path><defs><linearGradient id="A" x1="424.51" y1="169.511" x2="421.213" y2="158.572" xlink:href="#B"><stop offset="0" stop-color="#080606"></stop><stop offset="1" stop-color="#2b2727"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M411 161c9 3 18 4 27 5-1 0-1 1-2 1s-3-1-4 0c-2 0-4 0-6 1h-1l-12-3-1-1c-1-1-1-2-1-3z"></path><path d="M336 494l2 2v1c1 2 4 4 4 7v3s1 1 2 1v1c2 0 3 2 5 3 1 0 1 0 2 1l-1 1c-2 0-4-1-6-3l-3-2c-1 0-2-2-3-3s-2-3-3-5h0v-1-2h0c1 1 2 5 3 7 1 0 1 1 2 1-2-4-4-8-4-12z" class="K"></path><path d="M351 389c-4-2-7-5-11-7-3-2-6-3-9-5h0l1-1c1 0 5 0 7 1 0 0 1 1 1 2 2 1 4 2 5 3 2 0 2 1 4 0l2 7z" class="Y"></path><path d="M719 369c1-1 1-1 2-1 1 1 2 0 3 0 4 2 8 3 9 7 1 0 2 1 3 1l3-3h0l1-1 4 1c-2 1-5 2-7 4h0c-1 1-1 2-1 4h0l-1-2-1-1c0-2-2-3-4-3l-1-1c-2 0-3 1-4 1-2 0-2-2-3-3l1-1-1-1c-1 0-2-1-3-1z" class="i"></path><path d="M388 667l1-1c7-5 17-6 25-5l13 13c-1 1-2 1-3 3 1 1 2 2 4 3l3 2c1-1 1-1 2-1v1 1c-2 0-3 0-4-1-2-1-3-1-5-1h0c-1 0-1 0-2-1v-1l2-3v-1-2c-3-3-6-8-10-10h0l-1-1h-3-2 0-6c-1 0-1 0-2 1h-2-1c-2 1-3 2-4 2l-1 1c-1 0-2 0-3 1h-1z" class="K"></path><path d="M451 741c1 0 2 1 2 2 0 2-1 3-3 4-3 2-6 6-8 9 0 1 0 2-1 3l-2-1v-1-3c1-3 3-5 5-7l3-2c2-1 3-3 4-4z" class="i"></path><path d="M600 224s1 0 2 1c1-1 3-4 5-5 2 0 0 1 1 0 2-1 3-1 4-1h0c-2 3-4 6-7 7-1 0-3 1-4 2 0 0 1 0 1 1 0 0 1 1 2 1s2 0 3 1c1 3 0 7 1 10-2-2-4-6-6-9s-4-5-6-7c1-1 3-1 4-1z" class="J"></path><path d="M427 674c2 2 5 4 8 6 0 1 3 2 3 3 1 1 1 3 1 4l4 12c2 8 6 17 8 26 0-1-1-1-2-2h0v-1l-3-10v-1s-1-1-1-2v-1h0l-1-1c0-1-1-3-1-4v-1l-2-5c0-1 0-2-1-3v-2l-1-1 1-1c-1 0-1-1-1-2-1-1-1-2-1-3l-1-1c0-1-1-1-1-2h-1c-1-1-2-1-2-1-1 0-1 0-2 1l-3-2c-2-1-3-2-4-3 1-2 2-2 3-3zm269-235h2c8 11 14 23 15 38v-1c-2-3-2-9-5-11l-3-9c-3-6-6-12-9-17z" class="r"></path><path d="M417 781c2-5 7-11 11-13 1-1 1-1 3-1s4 4 5 5c4-3 7-8 11-10 0 2-1 3-2 5-2 3-5 7-8 8h-3c-2-2-3-5-5-6-4 3-8 8-11 12h-1z" class="J"></path><path d="M296 358c4-2 12-3 17-2 2 1 4 2 5 4-2 0-1 0-3-1h-2c-4 0-7-1-11 0s-12 6-16 3h0c0-1-2-2-2-2v-1c5 0 7 1 12-1z" class="K"></path><path d="M284 359c5 0 7 1 12-1v1c-1 1-3 1-4 2h-4s-1 1-2 1h0c0-1-2-2-2-2v-1z" class="b"></path><path d="M335 408c-1-3-2-6-4-7h0c-2 0-4-1-6-2s-4-4-7-6c-1-1-3-2-4-3h-6-12 1v-1c4-1 8-1 12-1 8-1 16 7 21 12v-3h1c1 3 3 5 5 7 0 1 0 2-1 4z" class="i"></path><path d="M296 326h0v1c1 3-3 11-5 14-5 8-12 10-20 12 2-1 3-2 5-4v-1l-2-1c-3-1-6 0-9 0l2-1c3-1 6-1 9 0l5 2c8-6 9-13 14-22h1z" class="p"></path><path d="M324 437c5-9 14-15 22-22 3-2 4-4 7-6l1 1v1c-5 6-11 11-17 16l-1-1-3 3c-3 3-5 6-9 8h0z" class="J"></path><path d="M405 793v-5-1c2 0 5 1 7 2 1 0 1 0 1-1 2-3 2-12 1-15l-2-4c2 1 2 2 3 4 3-5 4-8 10-10 1 0 2-1 3-1 1-2 3-5 5-6v1c-1 3-4 6-7 7s-5 2-6 4l-1 1c-3 4-3 7-3 13h0l1-1h1v1c-1 4-1 7-3 10-2 1-3 1-4 0-2 0-4-2-5-3l-1 1 1 3h-1z" class="r"></path><path d="M365 570c3 5 6 10 9 14 5 7 10 12 16 17l-1 3c-5-4-9-7-12-11-3-2-4-6-6-9s-5-7-6-11v-3z" class="K"></path><path d="M337 456c1-4 5-8 7-11 3-4 8-11 13-12l2 2h-1l-1 1c-2 3-4 5-6 7l-8 11-1-1-3 3h-2z" class="i"></path><path d="M711 378c1 1 2 1 3 3v1h0l-1-1c-2 2-3 3-4 5l-3 8-10 17c-1 1-3 2-3 4l-2-1c4-8 8-16 13-24l2-1c0-1 1-2 1-3l1-1c1-1 2-3 2-3 0-1 0-1 1-2h0v-1-1z" class="K"></path><path d="M720 353l3 1c3 2 6 3 8 6s2 6 6 7h1c-2-1-3-2-3-4v-1l3 3 9 3h3v2c-1 1-6 4-6 3l-4-1-1 1h0l-3 3c-1 0-2-1-3-1-1-4-5-5-9-7 3 0 6 2 8 3h2c1-1 1 0 2 0 1-1 2-1 3-2l-1-1h0c-2 0-4-1-4-2-1-1-2-2-2-3-2-4-5-6-9-8-1 0-2-1-2-1l-1-1z" class="o"></path><path d="M647 668h5v1c1 1 2 1 4 1v1 1c-2 0-2 1-3 2h-1c-1 0-2 1-2 1l-2 2c-6 3-11 6-15 11l-4-5v-2l3 3c1 0 0 0 1-1 2-5 12-10 18-12h1l-5-3z" class="M"></path><path d="M389 649l1 1c-3 4-3 8-5 13 9-4 18-6 27-5l2 3c-8-1-18 0-25 5l-1 1c-2 1-3 3-5 4 0-1-1-3 0-5 0-2 1-4 2-6 1-4 2-7 4-11z" class="s"></path><path d="M337 456h2l3-3 1 1c-4 6-8 14-7 22h0-1c-1 6 0 11 1 18 0 4 2 8 4 12-1 0-1-1-2-1-1-2-2-6-3-7-1-5-3-11-3-17v-9-2c0-2 0 1 0-1 0-1 0-2 1-3v-3l3-6 1-1z" class="r"></path><path d="M332 472v-2c0-2 0 1 0-1 0-1 0-2 1-3v-3l3-6 2 2c0 1 0 1-1 2-2 3-3 7-5 11zm89 249l2 1-2 3h-1c-1 2-3 2-5 4s-5 5-7 8c-1 1-2 3-2 5 2 0 5-1 7-2 4-1 6-6 7-9 1-2 1-3 1-4l4-2h1c-3 4-5 9-8 13-2 2-4 3-6 4l-5 3c-2 1-2 0-3 0v-2l-1 1c-2 2-3 2-5 2h-1c3-3 5-7 8-10 4-6 10-11 16-15z" class="K"></path><path d="M373 151h1c2 3 4 6 9 8 3 1 6 1 9 1l-2 1 1 1h0-1c-1 0-1 0-2 1l1 1v1h-4v1l2 2c1 1 1 2 2 3-1 0-2 0-3 1l1 2h0l-1-1h-2-1c-2-2-3-5-5-8-2-4-3-8-5-12v-1-1z" class="O"></path><path d="M373 151h1c2 3 4 6 9 8-1 1-2 2-2 3 1 2 2 3 3 5l1 2v1h-2c-1-1-1-2-2-3s-2-2-3-2c-2-4-3-8-5-12v-1-1z" class="l"></path><path d="M601 740c-4-7-11-11-17-16 1-2 0-1 0-2-2-1-5-2-6-4 1 0 1 0 3-1h0l1 1-1 1c1 2 6 3 8 5 5 3 9 7 12 11s6 8 9 11c-1 1-1 2-2 2-1-1-1-1-2-1-1-1-1-2-2-2-2 0-3 0-4 1l-1-1c-3-3-8-4-11-8-3-3-4-8-7-11v-1c2 1 3 3 4 4 4 6 7 14 17 13 0-2 0-1-1-2z" class="P"></path><path d="M426 719l-2 2c0 1-1 2-1 2l-2 2v1c2-1 3-2 5-2l2-2c2-1 11-6 12-5h1l-6 3c-1 1-1 1-2 1l-1 1h-2l-2 2c-1 0-1 1-2 1h0-1l-4 2c0 1 0 2-1 4-1 3-3 8-7 9-2 1-5 2-7 2 0-2 1-4 2-5 2-3 5-6 7-8s4-2 5-4h1l2-3-2-1v-1c1 0 1-1 2-1 1-1 1 0 1-1l2 1z" class="o"></path><path d="M601 740c-3-2-6-7-9-9l-2-2h-1l-1-2c-3-1 0 2-1 0l-3-3h-1v-1h0c-1-1-2-1-2-2h-1l-1-1c-3-1-5-2-7-4h-2l-3-2-2-1c-1 0-1-1-2-1-1-1-1-1-2-1v-2h1c1 0 1 0 2 1h1 2 0 1 4v-1c2 0 3 0 5 1 3 1 4 3 5 6 1 1 1 2 1 4h2l1 1c1 0 2 1 3 2v1c-2-2-7-3-8-5l1-1-1-1h0c-2 1-2 1-3 1 1 2 4 3 6 4 0 1 1 0 0 2 6 5 13 9 17 16z" class="K"></path><path d="M562 132c1-1 1-2 2-3 1 2 1 6 1 8 3 12 13 21 24 24 5 2 9 2 14 3h6v1h-9-4c-1 0-1-1-2-1h-2l-1-1c-2 0-3-1-5-1l-1-1c-2-1-4-2-5-2-4-2-6-4-9-4h0c-4-3-7-7-8-12 0 0 0-3-1-3 0-1-1-1-2-2l2-6z" class="v"></path><path d="M578 653l3-1-1 2c1 1 2 1 3 1h-1c0 3 0 5 1 7 0 4 0 10-2 12-5 4-10 3-14 9l-1 1 1 3c1 0 1-1 2-1s2 1 3 2l-1 2h-2l-6 3 4-15 1 1 8-7c0-2 1-4 1-5 0-3-1-4 0-6 0-3 1-5 1-8z" class="b"></path><path d="M577 661c1 1 1 2 2 5 0 1 0 2-1 3s-1 2-2 3c0-2 1-4 1-5 0-3-1-4 0-6z" class="Y"></path><path d="M583 662l-2 2v1l-1-1v-3-2c0-2 1-3 2-4 0 3 0 5 1 7z" class="J"></path><path d="M780 175l2-1 3 4v-2h-1c1-1 2 0 3 0 5 4 10 8 17 10 13 5 27 5 40 8-3 2-6 0-10 1h-2c-2 0-3 0-4 1-3-1-7-1-10 0h-2c0-1-1-1-2-2h-2 0c-1-1-2-2-3-2l-5-1-4-1-3-2c-4-1-7-4-9-6s-4-5-7-7l-1 2h-2l2-2z" class="D"></path><path d="M780 175l2-1 3 4c7 9 19 13 29 15l18 2c-2 0-3 0-4 1-3-1-7-1-10 0h-2c0-1-1-1-2-2h-2 0c-1-1-2-2-3-2l-5-1-4-1-3-2c-4-1-7-4-9-6s-4-5-7-7l-1 2h-2l2-2z" class="Z"></path><path d="M836 239c-12-9-17-19-20-33 1-1 1-2 2-3 6-7 14-6 22-7l1 1h1l1 1c-6 2-17 2-20 8-2 2 0 7 0 10 3 11 11 18 20 23 1 1 2 1 1 3h-1-1v-1c-2 0-4-1-6-2z" class="D"></path><path d="M192 186h0l15-3c10-3 19-6 29-10 2-1 8-3 11-3 2 2 5 2 7 2l-1 2c-3-1-5-1-7-1-4 0-9 2-12 5-1 1-3 2-3 4h0c-1-1-1 0-1-1l-3 1h0l-1 3c-2 0-5 1-7 2h-4c-4 1-9 1-12 2s-6 1-9 1l-1-1 1-1c-2 0-4 0-6 1 2-1 3-1 4-3z" class="X"></path><path d="M222 181c2-1 6-3 8-3-1 1-2 3-3 4h0c-3 0-3 0-6 1l1-2z" class="j"></path><path d="M209 184l13-3-1 2c-1 1-2 1-4 1-3 1-5 2-8 0z" class="F"></path><path d="M221 183c3-1 3-1 6-1l-1 3c-2 0-5 1-7 2h-4c0-1 2-1 2-3 2 0 3 0 4-1z" class="O"></path><path d="M209 184c3 2 5 1 8 0 0 2-2 2-2 3-4 1-9 1-12 2s-6 1-9 1l-1-1 1-1 10-2c2-1 3-1 5-2z" class="D"></path><path d="M194 188l10-2h2v1c-3 2-7 2-10 2h-3l1-1z" class="C"></path><path d="M407 160l1 2h0l3 4v1l-1 1h0v4h-1c-1 0-2 0-3 1-2-1-2-1-4 0-1 0-1 0-1 1l-1-1c-2 0-2 0-3-1l-3-2-2 1 2 2c-1 1 0 2 0 3l2 1v1l-1-1-1 1c1 0 1 1 2 2-3 1-1-1-3-1l-1 1h-2c-3-1-5-4-7-7h1 2l1 1h0l-1-2c1-1 2-1 3-1-1-1-1-2-2-3l-2-2v-1h4v-1c0-1 1-1 2-1l1-1h4 3 2l2-1c1-1 2-1 4-1z" class="Q"></path><path d="M396 162h3l-2 2v1h1v1c-1 1-2 1-4 1h0l2-1v-3-1z" class="m"></path><path d="M389 164c0-1 1-1 2-1l1-1h4v1 3l-2 1-2-1c-1 0-2 0-3-1v-1z" class="U"></path><path d="M392 166l1-1-2-1c1-1 1 0 3 0 0 0 1-1 2-1v3l-2 1-2-1z" class="j"></path><path d="M407 160l1 2h0l3 4v1l-1 1h0v4h-1c-1 0-2 0-3 1-2-1-2-1-4 0-1 0-1 0-1 1l-1-1c-2 0-2 0-3-1l-3-2-2 1 2 2c-1 1 0 2 0 3-1-1-2-3-4-4v-3s1-1 1 0h4 4v-1h2c1 0 3 0 4-1l-3-3h-1l-1 1-1-1c1 0 1-1 2-2l2-1c1-1 2-1 4-1z" class="Z"></path><path d="M408 162h0l3 4v1l-1 1h0v4h-1c-1 0-2 0-3 1-2-1-2-1-4 0-1 0-1 0-1 1l-1-1c-2 0-2 0-3-1l2-2 1 1 1-1h7c0-2 0-3-1-5h0c0-1 0-2 1-3h0z" class="V"></path><path d="M297 165c1-1 2-3 3-5 0-2 0-5-1-7-3-4-12-7-17-8h-1c-3 0-9 2-11 0 0 0 0-1 1-1 1-3 4-4 7-6 9-6 18-7 30-9v1 1c-9 0-18 3-26 7v1c1 1 2 1 4 2l6 1 6 3c3 2 6 4 7 8v2 1 1l-1 4-5 5h-1-1l-1 1h0-1c-1 1-1 1-2 1 2-1 3-1 4-3z" class="g"></path><path d="M520 181v1 1h3l13 2h3v1c5 1 10 3 15 4 14 6 28 15 38 26-2-2-4-4-7-5-1-1-2-1-3 0-7-5-14-10-22-14-17-9-39-13-59-12l-14 3c-3 1-5 2-7 1h-1l2-1c2 0 5-1 7-2 8-3 16-2 24-4 3 0 4 1 7 1l1-2zM400 636c3 8 7 15 12 22-9-1-18 1-27 5 2-5 2-9 5-13l-1-1c0-1 1-2 2-2 1-2 2-3 3-5 1-1 2-3 3-4l2 1v-2l1-1z" class="K"></path><path d="M397 638l2 1-9 11-1-1c0-1 1-2 2-2 1-2 2-3 3-5 1-1 2-3 3-4z" class="P"></path><path d="M254 172c4 1 7 1 11 0 2 0 4-1 6-2h1l-3 3c-1 1-1 2-2 4v1l3 5 1 1h-1c-1 0-2-1-3-1h-1-1c0-1-1-1-2-2h-1l-1 1-4-2-2 1c-1 0-3 1-4 1v-1-1h-3c-2 0-2 0-4 1-1 1-1 1-2 1-1 2-2 3-3 4h-1c-3 1-3 1-6 0h-3l1 1-2 1-2-1v-2l1-3h0l3-1c0 1 0 0 1 1h0c0-2 2-3 3-4 3-3 8-5 12-5 2 0 4 0 7 1l1-2z" class="c"></path><path d="M234 184c3-2 4-2 8-2-1 2-2 3-3 4h-1c-3 1-3 1-6 0l2-2z" class="B"></path><path d="M231 182h1c1-1 2-1 3-2h0l1-1 1 1h-1c-1 2-2 3-2 4l-2 2h-3l1 1-2 1-2-1v-2l1-3h0l3-1c0 1 0 0 1 1h0z" class="T"></path><path d="M227 182l3-1c0 1 0 0 1 1-1 1-2 3-2 4l1 1-2 1-2-1v-2l1-3h0z" class="M"></path><path d="M251 180v-1h-3c-1 0-2 0-3-1h0-1 0l-2-1c3-3 8 0 12 0h0 2l1 1s0 1-1 1h-1c-2 1-3 1-4 2v-1z" class="G"></path><path d="M254 172c4 1 7 1 11 0 2 0 4-1 6-2h1l-3 3c-1 1-1 2-2 4v1l3 5 1 1h-1c-1 0-2-1-3-1h-1-1c0-1-1-1-2-2h-1l-1 1-4-2-2 1c-1 0-3 1-4 1v-1c1-1 2-1 4-2h1c1 0 1-1 1-1l-1-1h-2l-4-2-4-2c2 0 4 0 7 1l1-2z" class="V"></path><path d="M261 176h2c1 0 1 1 2 1 0 0 1-1 2-1v1 1h0c-2-1-2-1-3-1l-1 1c1 1 2 1 3 2h1-1v1l-3-2v1h-1c-1-2-2-3-4-4h3z" class="d"></path><path d="M254 172c4 1 7 1 11 0 2 0 4-1 6-2h1l-3 3c-1 1-1 2-2 4v-1c-1 0-2 1-2 1-1 0-1-1-2-1h-2-3c-1 0-3 0-4-1h-4l-4-2c2 0 4 0 7 1l1-2z" class="G"></path><path d="M246 173c2 0 4 0 7 1 2 0 6 0 8 1v1h-3c-1 0-3 0-4-1h-4l-4-2zm-4 9c1 0 1 0 2-1 2-1 2-1 4-1h3v1 1l-2 2 2 2c2 0 3 1 5 1l1 2c1 0 1 0 2 1l1 1h-2l1 1v3l-3 1-1 1c0 1 0 1-1 2-2 1-3 1-4 1-1-1-2-1-4-2h0l-1 2-9-5-1-1c-5-3-10-6-16-7 2-1 5-2 7-2v2l2 1 2-1-1-1h3c3 1 3 1 6 0h1c1-1 2-2 3-4z" class="m"></path><path d="M248 189c1 0 1 0 1-1l-3-3 1-1h2l2 2c2 0 3 1 5 1l1 2c1 0 1 0 2 1l1 1h-2-1c-1 1-1 0-2 1-1-1-2 0-3-1l-2 1v-1h0v-1c-1 0-2 1-3 1l1-2z" class="W"></path><path d="M251 186c2 0 3 1 5 1l1 2c-1 0-2 0-3-1h-1c-1 1-2 1-3 1 0-1 0-2 1-3z" class="V"></path><path d="M232 186c3 1 3 1 6 0h1 0 2v1h-2l1 1h0c2 0 3 0 4 1h2l1 1v-1h1l-1 2c1 0 2-1 3-1v1h0v1c1 1 1 1 2 1v1h-1l-2-2h-1c-1 0-2 1-3 1s-3-2-4-2l-2 1-9-5-1-1h3z" class="j"></path><path d="M250 192l2-1c1 1 2 0 3 1 1-1 1 0 2-1h1l1 1v3l-3 1-1 1c0 1 0 1-1 2-2 1-3 1-4 1-1-1-2-1-4-2l1-2-8-4 2-1c1 0 3 2 4 2s2-1 3-1h1l2 2h1v-1c-1 0-1 0-2-1z" class="W"></path><path d="M247 196l5 2 3-1c0 1 0 1-1 2-2 1-3 1-4 1-1-1-2-1-4-2l1-2z" class="i"></path><path d="M250 192l2-1c1 1 2 0 3 1 1-1 1 0 2-1h1l1 1v3l-3 1h-3-1c-1 0-2-1-4-2v-2h1l2 2h1v-1c-1 0-1 0-2-1z" class="G"></path><path d="M219 187c2-1 5-2 7-2v2l2 1 2-1 9 5 8 4-1 2h0l-1 2-9-5-1-1c-5-3-10-6-16-7z" class="k"></path><path d="M230 187l9 5 8 4-1 2h0c-3 0-9-4-11-5-1-2-2-3-3-4l-4-1 2-1z" class="K"></path><path d="M282 138c8-4 17-7 26-7l-1 2c1 1 1 1 2 1l2 2h0c-1 1 0 7 1 9h2v5c1 2 1 5 0 7h-1l-2 5-1-1v-4s0-3 1-3c-1 1-2 1-3 2-2 0-2 0-3 1v-1-1-2c-1-4-4-6-7-8l-6-3-6-1c-2-1-3-1-4-2v-1z" class="l"></path><path d="M286 141l1-1c3-1 6 0 10 0-2 0-3 1-4 1l-1 1-6-1z" class="D"></path><path d="M297 140c1-1 2-1 3-1v4l1 2h-3l-6-3 1-1c1 0 2-1 4-1z" class="O"></path><path d="M282 138c8-4 17-7 26-7l-1 2c1 1 1 1 2 1l2 2h0c-1 1 0 7 1 9h2v5c1 2 1 5 0 7h-1l-2 5-1-1v-4s0-3 1-3c0-2 0-4-1-5l1-4c-1-2-1-4-1-6l-1-3-1-1-11 1h-7c-3 1-6 3-8 2z" class="Z"></path><path d="M312 145h2v5c1 2 1 5 0 7h-1l-1-9v-3z" class="N"></path><defs><linearGradient id="C" x1="309.03" y1="154.459" x2="303.586" y2="138.612" xlink:href="#B"><stop offset="0" stop-color="#352f2e"></stop><stop offset="1" stop-color="#4d3633"></stop></linearGradient></defs><path fill="url(#C)" d="M309 136l1 3c0 2 0 4 1 6l-1 4c1 1 1 3 1 5-1 1-2 1-3 2-2 0-2 0-3 1v-1-1-2c-1-4-4-6-7-8h3l-1-2v-4h6 1c1-1 1-2 2-3z"></path><path d="M308 145h3l-1 4c1 1 1 3 1 5-1 1-2 1-3 2l1-6c-1 0-1-1-2-1 0-2 1-3 1-4z" class="O"></path><path d="M308 145h3l-1 4c0-1 0-1-1-1h-1l1-1-1-2z" class="H"></path><defs><linearGradient id="D" x1="342.609" y1="140.46" x2="336.49" y2="152.348" xlink:href="#B"><stop offset="0" stop-color="#0b0706"></stop><stop offset="1" stop-color="#2b2523"></stop></linearGradient></defs><path fill="url(#D)" d="M330 133c2 1 4 1 4 3v1c0 5 2 9 6 13 1 2 3 4 6 5 1 1 3 1 4 3h0c2 1 4 1 6 1 1 0 3 0 4 1-2 1-8 0-10 0l3 2h-5-2c-2 0-4-1-5-3v1c0 1 1 2 2 3l-1 2c-5-4-9-9-13-14h0c-2-2-3-4-4-6h-5l-4-4-4-4 1-1v-1c3 1 4 2 6 3h1c0-1 0-1 1-1-1-1 1-2 1-2h4c2 0 2 0 4 2v-4z"></path><path d="M333 147c1 2 3 6 6 7h1v1c3 3 6 4 10 5l3 2h-5-2c-2 0-4-1-5-3v1c0 1 1 2 2 3l-1 2c-5-4-9-9-13-14l1-1c1 1 2 2 4 3h1l-3-3h1 1c-1-1-1-2-1-3h0z" class="m"></path><defs><linearGradient id="E" x1="327.391" y1="138.117" x2="327.11" y2="149.847" xlink:href="#B"><stop offset="0" stop-color="#634241"></stop><stop offset="1" stop-color="#846763"></stop></linearGradient></defs><path fill="url(#E)" d="M326 135c2 0 2 0 4 2l-1 1 2 2c0 2 2 5 2 7h0c0 1 0 2 1 3h-1-1l3 3h-1c-2-1-3-2-4-3l-1 1h0c-2-2-3-4-4-6h-5l-4-4-4-4 1-1v-1c3 1 4 2 6 3h1c0-1 0-1 1-1-1-1 1-2 1-2h4z"></path><path d="M322 137c2 0 5 0 7 1l2 2-1 2c-2-1-3-3-4-4-2 1-3 0-4-1z" class="N"></path><path d="M326 135c2 0 2 0 4 2l-1 1c-2-1-5-1-7-1h-1c-1-1 1-2 1-2h4z" class="O"></path><path d="M313 136c4 3 9 6 12 9h-5l-4-4-4-4 1-1zm58 19c0 2 0 5 1 7 0 2 0 5 1 8 1 1 1 2 1 3-1 2-3 3-4 5l-3 3-1 1c-2-1-3-2-4-3-3-1-5-2-7-4h0l-8-6-5-4 1-2c-1-1-2-2-2-3v-1c1 2 3 3 5 3h2 5l-3-2c2 0 8 1 10 0l4-1 7-4z" class="a"></path><path d="M353 165h1c0 1 1 1 1 2 1 0 1 0 2 1l-1 1c-2-1-3-1-4-2 0-1 0-1 1-2z" class="X"></path><path d="M355 175h1c1 0 2 1 3 1l2-2c1-1 2-1 3-1h2 1v1c1 0 1 1 2 2l1 2-3 3-1 1c-2-1-3-2-4-3-3-1-5-2-7-4z" class="n"></path><path d="M362 179c1 0 3 0 4-1 1 0 1-1 2-2h1l1 2-3 3-1 1c-2-1-3-2-4-3z" class="M"></path><path d="M371 155c0 2 0 5 1 7 0 2 0 5 1 8l-1 2-2-1v-1h2v-1l-1-1c-2 1-3 2-5 2v-1c-1-1-2-2-4-2h-1c-2-1-5-2-7-2h-1c-1-1-2-1-3-1h-4c1 1 0 1 1 1 1 1 2 3 4 3-1 1-3 1-4 1l-5-4 1-2c-1-1-2-2-2-3v-1c1 2 3 3 5 3h2 5l-3-2c2 0 8 1 10 0l4-1 7-4z" class="Z"></path><path d="M371 155c0 2 0 5 1 7-2 2-3 3-6 4h0-4v-1l-9-3-3-2c2 0 8 1 10 0l4-1 7-4z" class="Q"></path><path d="M360 160l4-1c-1 1-2 2-4 3l1 2h2v1h-1l-9-3-3-2c2 0 8 1 10 0z" class="j"></path><path d="M832 195h2l6 1h0c-8 1-16 0-22 7-1 1-1 2-2 3 3 14 8 24 20 33h-1c-2-1-5-4-7-6 0 0-3-2-3-3-2 0-3-1-5-1h0l-3-1-1 1 1 2v1l-5 4-4-4-3-4h-1l-3-5c0-2 1-3 2-5h0v-1c1-1 1-2 2-3 0 0-1-1-1-2l1-2c0-1 1-2 1-4 2 0 3-1 4-2l2-2c1-2 3-3 4-5l2-1c3-1 7-1 10 0 1-1 2-1 4-1z" class="M"></path><path d="M818 196c3-1 7-1 10 0-3 1-6 2-9 4-3 0-5 1-7 2 1-2 3-3 4-5l2-1z" class="B"></path><path d="M812 211l2-2h1c1 2 1 4 2 6l1 3h-1l-2-1h0-2c-1 0-1-2-2-3v-1l1-1v-1z" class="d"></path><path d="M812 211c2 2 2 4 3 6h-2c-1 0-1-2-2-3v-1l1-1v-1z" class="e"></path><path d="M812 202c2-1 4-2 7-2-1 1-3 2-4 3s-3 3-3 5l-3 2-4 4s-1-1-1-2l1-2c0-1 1-2 1-4 2 0 3-1 4-2l2-2z" class="F"></path><path d="M811 214c1 1 1 3 2 3h2 0l2 1h1c2 4 4 8 7 12-2 0-3-1-5-1h0l-3-1-1 1h-1v-2c-1-3-1-5-3-7 0 0-1-1-1-2v-4z" class="Z"></path><path d="M815 227v-3c-1-1-1-2-1-3h2c1 1 1 2 1 4l-1 2 2 1h-1l-1 1h-1v-2z" class="Q"></path><path d="M811 214c1 1 1 3 2 3h2 0l1 4h-2c0 1 0 2 1 3v3c-1-3-1-5-3-7 0 0-1-1-1-2v-4z" class="F"></path><path d="M805 214l4-4 3 2-1 1v1 4c0 1 1 2 1 2 2 2 2 4 3 7v2h1l1 2v1l-5 4-4-4-3-4h-1l-3-5c0-2 1-3 2-5h0v-1c1-1 1-2 2-3z" class="g"></path><path d="M811 213v1 4h-2v2l-1 1c-2 0-3 1-4 1l1-2 6-7z" class="C"></path><path d="M803 218c1 0 2 1 2 2l-1 2c0 2 0 4 1 6h-1l-3-5c0-2 1-3 2-5z" class="M"></path><path d="M805 214l4-4 3 2-1 1-6 7c0-1-1-2-2-2h0v-1c1-1 1-2 2-3z" class="n"></path><path d="M812 220c2 2 2 4 3 7v2h1l1 2v1l-5 4-4-4 1-1c1-1 2-1 2-2 2-3 1-6 1-9z" class="d"></path><path d="M311 136l1 1 4 4 4 4h5c1 2 2 4 4 6h0c4 5 8 10 13 14l5 4 8 6h0c2 2 4 3 7 4 1 1 2 2 4 3l1-1 3-1h1c-1 1-2 2-2 3l1 1 1-2c0 2 0 3 1 5h-2-1c-1 0-2 1-3 1v1l-1-2h0c-1 0-2-1-3-1h-1c-6-1-12 0-18-1h4 4v-1c-4-2-8-5-11-8l-3-3-6-6c0-1 0-2-1-3l-5-5h0c-1-2-2-3-3-5l-1-2c-1-1-3-3-4-3l-1 1h0-2v-5h-2c-1-2-2-8-1-9z" class="H"></path><path d="M334 161l18 16c1 1 10 7 11 7l2 3c-1 0-2-1-3-1l-5-3c-2-2-5-3-7-5l-3-3c-1-1-3-2-5-3l-1-1c-1-2-2-3-4-4-2-2-2-3-3-6z" class="B"></path><path d="M355 175h0c2 2 4 3 7 4 1 1 2 2 4 3l1-1 3-1h1c-1 1-2 2-2 3l1 1 1-2c0 2 0 3 1 5h-2-1c-1 0-2 1-3 1v1l-1-2h0l-2-3c-1 0-10-6-11-7l3-2z" class="w"></path><path d="M366 184h-1c0-1 1-2 2-2l1 2h0l-1 1-1-1z" class="P"></path><path d="M370 184l1-2c0 2 0 3 1 5h-2-1c-1 0-2 1-3 1v1l-1-2h0l-2-3h3l1 1 2 1 1-2z" class="M"></path><path d="M311 136l1 1 4 4-1 1c3 5 7 9 11 13 1 1 4 4 4 5 0 2 0 2 1 3l-1 1-5-5h0c-1-2-2-3-3-5l-1-2c-1-1-3-3-4-3l-1 1h0-2v-5h-2c-1-2-2-8-1-9z" class="C"></path><defs><linearGradient id="F" x1="325.848" y1="162.343" x2="349.152" y2="158.657" xlink:href="#B"><stop offset="0" stop-color="#bb8d81"></stop><stop offset="1" stop-color="#d9beba"></stop></linearGradient></defs><path fill="url(#F)" d="M320 145h5c1 2 2 4 4 6h0c4 5 8 10 13 14l5 4 8 6-3 2-18-16-14-16z"></path><path d="M311 154c-1 0-1 3-1 3v4l1 1c-1 2-2 8-4 9l-2 5-1 1c-1 2-2 4-4 6-1 1-2 2-4 2l-1 2-16 2h-3 0l-1-1-3 1h-1l2-1c-1-1-2 0-3-1 0 0 0-1-2-1-1-1-1-2-2-3h1c1 0 2 1 3 1h1l-1-1-3-5v-1c1-2 1-3 2-4l3-3c1 0 1-1 2-2h1l3 1 1 1c5 1 14-3 18-5-1 2-2 2-4 3 1 0 1 0 2-1h1 0l1-1h1 1l5-5 1-4c1-1 1-1 3-1 1-1 2-1 3-2z" class="R"></path><path d="M300 166v4 2c0 1 1 2 1 2v2c-2-2-2-2-2-4 0-1-1-1-1-1 0-2 0-2-1-3l3-2z" class="e"></path><path d="M267 177c1-2 1-3 2-4h1c1 0 1 0 2-1l2 2-1 1-1-1c-2 0-2 0-3 2 0 1 0 1 1 2v1l-1 1 2 2s-1 0-1 1l-3-5v-1z" class="L"></path><path d="M283 177c1-1 1-1 3-1 1 0 1-1 3-2h0c1 0 2 0 4-1l2 1-3 2c-1 1-2 1-3 1h-1c-1 1-3 1-5 2v-2z" class="G"></path><path d="M283 177v2l-6 3-1 1-1-1-1 1c-1 0-2 0-3 1l-1-1c0-1 1-1 1-1l-2-2 1-1h1 1 1l1 1 1-1 1 1c3-1 5-2 7-3z" class="Z"></path><path d="M297 165c-1 2-2 2-4 3 1 0 1 0 2-1h1 0l1-1h1 1l5-5c-1 2-2 4-2 6h-1v-1h-1l-3 2c-2 1-4 2-6 2s-3 1-4 2c-4 1-8 1-10 3-1 0-2 0-3-1l-2-2c-1 1-1 1-2 1h-1l3-3c1 0 1-1 2-2h1l3 1 1 1c5 1 14-3 18-5z" class="f"></path><path d="M311 154c-1 0-1 3-1 3v4l1 1c-1 2-2 8-4 9 0-1-1-1-2-2l-4 5s-1-1-1-2v-2-4h1v1h1c0-2 1-4 2-6l1-4c1-1 1-1 3-1 1-1 2-1 3-2z" class="C"></path><path d="M300 170h1c2-3 3-4 4-7v4 2l-4 5s-1-1-1-2v-2z" class="O"></path><path d="M311 154c-1 0-1 3-1 3-1 4-3 7-5 10v-4c1-3 2-5 3-7 1-1 2-1 3-2z" class="B"></path><path d="M310 157v4l1 1c-1 2-2 8-4 9 0-1-1-1-2-2v-2c2-3 4-6 5-10z" class="n"></path><path d="M305 169c1 1 2 1 2 2l-2 5-1 1c-1 2-2 4-4 6-1 1-2 2-4 2l-1 2-16 2h-3 0l-1-1-3 1h-1l2-1c-1-1-2 0-3-1 0 0 0-1-2-1-1-1-1-2-2-3h1c1 0 2 1 3 1h1c1-1 2-1 3-1l1-1 1 1 1-1 6-3c2-1 4-1 5-2h1c1 0 2 0 3-1l3-2h0 2v1l-1 1v1h1 3l1-1v-2l4-5z" class="k"></path><path d="M295 174h0 2v1l-1 1v1h1 3c-2 2-4 3-6 5 0-1 2-3 2-3l-3 1-1-1c1-1 1-1 2-1h1c-1-1-1-1-2-1-1 1-3 2-5 2-1 1-3 2-4 3h-3-1c-1 1-1 1-2 1l-1-1 6-3c2-1 4-1 5-2h1c1 0 2 0 3-1l3-2z" class="a"></path><defs><linearGradient id="G" x1="287.604" y1="176.477" x2="296.522" y2="185.071" xlink:href="#B"><stop offset="0" stop-color="#c5a59b"></stop><stop offset="1" stop-color="#e7d3c7"></stop></linearGradient></defs><path fill="url(#G)" d="M305 169c1 1 2 1 2 2l-2 5-1 1c-1 2-2 4-4 6-1 1-2 2-4 2l-1 2-16 2h-3 0l-1-1c1 0 2 0 2-1 2 0 3-2 5-3h0l-1 1 1 1c4-1 9-1 12-4 2-2 4-3 6-5l1-1v-2l4-5z"></path><path d="M324 437h0c4-2 6-5 9-8l3-3 1 1c-15 15-24 32-24 54 0 14 4 29 15 39 3 3 8 7 12 6 3 0 7-3 10-5v1c-2 2-5 3-6 5 0 7-2 13-5 20 0 1-2 3-2 3 0 1 1 4 2 5 0 3 1 7 1 11 0 1 1 3 0 4 0 2-2 4-3 6-3 5-7 10-11 14-1 0-3 2-3 3v1c2 1 4 0 6-1 3-1 6-3 9-5 9-4 16-14 21-23 1 0 3-2 4-3 0 1-1 2-1 3h0v1c-3 2-3 6-6 10-5 8-12 14-21 18-3 2-7 4-11 4h0c-2 0-4 0-6-1v-1-1h2c3-5 8-8 12-13l-1-3 4-5 1-4v-6c-1-4-2-8-2-12v-2l5-12 1-8c-4-1-8-2-11-4-10-7-16-20-19-32-3-20 2-41 14-57z" class="M"></path><path d="M335 574c0 3 0 4-2 7 0 0-1 0-1 1l-1-3 4-5z" class="p"></path><path d="M334 552c1 1 2 3 2 5 1 2 0 3 1 5l-1 2c-1-4-2-8-2-12z" class="J"></path><path d="M334 550l5-12c0 2 0 4-1 6 1 1-1 4-2 6h-1-1z" class="s"></path><path d="M314 150h2 0l1-1c1 0 3 2 4 3l1 2c1 2 2 3 3 5h0l5 5c1 1 1 2 1 3l6 6 3 3c3 3 7 6 11 8v1h-4-4l-48 2 1-2c2 0 3-1 4-2 2-2 3-4 4-6l1-1 2-5c2-1 3-7 4-9l2-5h1c1-2 1-5 0-7z" class="Z"></path><path d="M325 159l5 5c1 1 1 2 1 3-1-2-3-3-5-4l-1-1c-1-1-2-1-4-2h1c1 0 2-1 3-1z" class="m"></path><path d="M321 160c2 1 3 1 4 2l1 1c-5 0-7 1-10 3 1-3 3-4 5-6z" class="G"></path><path d="M326 167c3 1 4 3 6 5l3 3-3 2h-1l-3 3h-3l1-1c0-1 0 0 1-1l2-2v-2-2c0-2-1-3-3-5z" class="n"></path><path d="M332 172l3 3-3 2h-1v-3c1-1 1 1 1-1v-1z" class="V"></path><path d="M313 177v-1c1-3 5-7 7-8s4-1 6-1c2 2 3 3 3 5v2c-3 3-6 4-11 4-2 1-3 0-5-1z" class="g"></path><defs><linearGradient id="H" x1="309.834" y1="152.858" x2="316.718" y2="170.857" xlink:href="#B"><stop offset="0" stop-color="#3e3536"></stop><stop offset="1" stop-color="#766361"></stop></linearGradient></defs><path fill="url(#H)" d="M314 150h2 0l1-1c1 0 3 2 4 3l1 2c1 2 2 3 3 5h0c-1 0-2 1-3 1h-1c-2 2-4 3-5 6-5 3-5 9-9 11-1-1-1-1-2-1l2-5c2-1 3-7 4-9l2-5h1c1-2 1-5 0-7z"></path><path d="M321 160h-2 0c0-2 1-3 2-4 1 1 2 2 3 2l1 1h0c-1 0-2 1-3 1h-1z" class="I"></path><path d="M329 174v2l-2 2c-1 1-1 0-1 1l-1 1h3l3-3h1l3-2h1l1-2 3 3c3 3 7 6 11 8v1h-4-4l-48 2 1-2c2 0 3-1 4-2 2-2 3-4 4-6l2 2h1v-1h1c1 0 1-1 2-1l2-2v1c1 1 1 0 1 1 2 1 3 2 5 1 5 0 8-1 11-4z" class="P"></path><path d="M304 177l2 2h1v-1h1c1 0 1-1 2-1 0 2 0 2 2 4h2c1 0 1 0 2 1h-11c-1 0-1 1-2 1-1 1-2 1-3 0 2-2 3-4 4-6z" class="n"></path><path d="M329 174v2l-2 2c-1 1-1 0-1 1l-1 1v1h-1l4 1c-3 0-10 1-12 0-1-1-1-1-2-1h-2c-2-2-2-2-2-4l2-2v1c1 1 1 0 1 1 2 1 3 2 5 1 5 0 8-1 11-4z" class="M"></path><path d="M336 175l1-2 3 3c3 3 7 6 11 8v1h-4c-2-4-2-2-5-3-5-1-10 0-14 0l-4-1h1v-1h3l3-3h1l3-2h1z" class="X"></path><path d="M336 175l1-2 3 3v1c-1 1-1 0 0 1-2 0-1-1-3-1h-1v-2z" class="V"></path><path d="M335 175h1v2h1c-2 1-3 2-5 2h-1l-3 1 3-3h1l3-2z" class="a"></path><path d="M373 152v1c2 4 3 8 5 12 2 3 3 6 5 8 2 3 4 6 7 7h2l1-1c2 0 0 2 3 1-1-1-1-2-2-2l1-1 1 1v-1l-2-1c0-1-1-2 0-3v-1l1 1h2l1 1v3l1 1 1 1c1 1 2 1 3 1l2 1c1 0 2 1 4 1h1c0 1 1 2 1 3 2 0 4-1 5 0 2 0 1 0 2-1h7v1c2 0 2 1 3 2l-5 2c-1-1-2-1-3-2v1c-1 1-3 2-5 3h-2c-1 2-1 2-3 3l-1 2c-1 1-2 2-3 4l-5 10c0 1-1 2-2 3h0v1l1 1c0 1 0 2-2 3h-1l-1-8h0v-2c-1-3-1-5-1-7-1-1-2-2-2-3s0-2-1-2h-1v2l-1 2-1 1h-1c0-2 1-3 2-5l-1-2c0-1-1-2-1-2-1-3-2-3-4-4v-1l-2-1-1 1c-1 0-2 0-3-1-2 0-4 1-6 1-1-2-1-3-1-5l-1 2-1-1c0-1 1-2 2-3h-1l-3 1 3-3c1-2 3-3 4-5 0-1 0-2-1-3-1-3-1-6-1-8-1-2-1-5-1-7 0-1 1-2 2-3z" class="n"></path><path d="M384 178l2 2 4 5c-3-2-5-3-7-5h0l1-2z" class="j"></path><path d="M394 188h2l-1 5-2 1v-2c0-1 0-3 1-4z" class="X"></path><path d="M395 193c0 1 1 2 1 3l2 1v1c0 1 0 0-1 1 0 1 0 3-1 5l1 2 1 1c-1 1-1 1-2 1v2-2c-1-3-1-5-1-7 0-3-1-5-2-7l2-1z" class="H"></path><path d="M373 159c1 1 2 3 3 4l3 8c2 2 4 4 5 7l-1 2c-6-6-8-12-10-21z" class="I"></path><path d="M374 178l1-2c1 1 2 2 2 3 3 2 5 4 8 6l3 3c1-1 2 0 3 0h1c-1 2 0 2-1 3s-1 1-2 3c0-1-1-2-1-2-1-3-2-3-4-4v-1l-2-1-1 1c-1 0-2 0-3-1-2 0-4 1-6 1-1-2-1-3-1-5l1-1c0-1 1-2 2-3z" class="G"></path><path d="M374 178l1-2c1 1 2 2 2 3l-1 1h-1l-1-1v-1z" class="m"></path><path d="M378 186c-1-1-1-1-2-1l-2-2h0v-1c1-1 1-1 2-1h2 0l4 4h3l3 3c1-1 2 0 3 0h1c-1 2 0 2-1 3s-1 1-2 3c0-1-1-2-1-2-1-3-2-3-4-4v-1l-2-1-1 1c-1 0-2 0-3-1z" class="I"></path><path d="M373 153c2 4 3 8 5 12 2 3 3 6 5 8 2 3 4 6 7 7h2l1-1c2 0 0 2 3 1-1-1-1-2-2-2l1-1 1 1v-1l-2-1c0-1-1-2 0-3v-1l1 1h2l1 1v3l1 1 1 1c1 1 2 1 3 1v2c-2 1-3 0-5 0-2 1-4 0-6 0l-3-1c-1 0-1-1-2-2l-1 1-2-2c-1-3-3-5-5-7-1-3-2-5-3-8-1-1-2-3-3-4l-1-1c0-2 0-3 1-5z" class="V"></path><path d="M394 173v-1l1 1h2l1 1v3c-1 0-1-1-2-1-1-1-1-2-2-3h0z" class="k"></path><path d="M403 180l2 1c1 0 2 1 4 1h1c0 1 1 2 1 3 2 0 4-1 5 0 2 0 1 0 2-1h7v1c2 0 2 1 3 2l-5 2c-1-1-2-1-3-2v1c-1 1-3 2-5 3h-2-1c0-1 0-1-1-2l-10-1h0-5-2l-1-1c0-1 1-2 2-3-3-1-4-1-6-3l3 1c2 0 4 1 6 0 2 0 3 1 5 0v-2z" class="w"></path><path d="M397 185c2-1 5 0 7 0h-4v2h-2l-1-2z" class="n"></path><path d="M425 185c2 0 2 1 3 2l-5 2c-1-1-2-1-3-2h-1v-2h6z" class="P"></path><path d="M401 188c6-1 13 0 19 0-1 1-3 2-5 3h-2-1c0-1 0-1-1-2l-10-1h0z" class="E"></path><path d="M403 180l2 1c1 0 2 1 4 1h1c0 1 1 2 1 3h-7c-2 0-5-1-7 0h0l-2-1c-3-1-4-1-6-3l3 1c2 0 4 1 6 0 2 0 3 1 5 0v-2z" class="X"></path><path d="M403 180l2 1 1 2c-1 1-2 1-3 0h0-3l-1 1h-1c-2-1-4-1-6-2 2 0 4 1 6 0 2 0 3 1 5 0v-2z" class="a"></path><path d="M396 188h5 0l10 1c1 1 1 1 1 2h1c-1 2-1 2-3 3l-1 2c-1 1-2 2-3 4l-5 10c0 1-1 2-2 3h0v1l1 1c0 1 0 2-2 3h-1l-1-8h0v-2c1 0 1 0 2-1l-1-1-1-2c1-2 1-4 1-5 1-1 1 0 1-1v-1l-2-1c0-1-1-2-1-3l1-5z" class="F"></path><g class="H"><path d="M399 203c0-1 1-2 1-4h1c1-2 3-3 5-4-2 2-3 4-4 7-1 1-1 4-3 5l1-2-1-2z"></path><path d="M399 203l1 2-1 2c1 2 0 4 0 6v1l1 1c0 1 0 2-2 3h-1l-1-8h0v-2c1 0 1 0 2-1l-1-1c1-1 1-2 2-3z"></path></g><path d="M399 203l1 2-1 2v3h-3 0v-2c1 0 1 0 2-1l-1-1c1-1 1-2 2-3z" class="c"></path><path d="M398 197l1 1c1-1 1-1 1-2v-1-1l2 1c2-2 4-3 7-2-1 0-3 2-3 2-2 1-4 2-5 4h-1c0 2-1 3-1 4-1 1-1 2-2 3l-1-2c1-2 1-4 1-5 1-1 1 0 1-1v-1z" class="E"></path><path d="M396 188h5 0l10 1c1 1 1 1 1 2-1 1-2 1-3 2-3-1-5 0-7 2l-2-1v1 1c0 1 0 1-1 2l-1-1-2-1c0-1-1-2-1-3l1-5z" class="g"></path><path d="M780 177l1-2c3 2 5 5 7 7s5 5 9 6l3 2 4 1 5 1c1 0 2 1 3 2h0 2c1 1 2 1 2 2h2l-2 1c-1 2-3 3-4 5l-2 2c-1 1-2 2-4 2 0 2-1 3-1 4l-1 2c0 1 1 2 1 2-1 1-1 2-2 3v1h0c-1 2-2 3-2 5l3 5-1 1c-3-4-6-8-10-11 0-3-2-4-4-7h-2l-1-2c-3-1-3-2-5-4l-1-1-2-1v-1l-2-2c-1-2-1-3-2-5v-2c-1-6 3-11 6-16z" class="t"></path><path d="M785 201v2c1 1 3 1 4 1l1 1v1c-1 0-1 0-2-1l-1 1h0v1l-2-1h0v-3h-3l1-1c1-1 1 0 2-1h0z" class="G"></path><path d="M801 208c0 1 1 1 2 1l3-3c0 2-1 3-1 4l-1 2c0-1-1-1-2-2-1 2-3 3-5 4 1-1 1-2 1-2l3-4z" class="j"></path><path d="M799 220v-2l-1-2v-1l2 1v-1h1l1-1c1 2 1 2 1 4h0c-1 2-2 3-2 5-1-1-1-2-2-3z" class="F"></path><path d="M806 204c3-3 6-5 10-7-1 2-3 3-4 5l-2 2c-1 1-2 2-4 2l-3 3c-1 0-2 0-2-1 1-1 3-2 5-4z" class="U"></path><path d="M789 211h4c1 1 1 2 1 3s1 2 2 4l3 2c1 1 1 2 2 3l3 5-1 1c-3-4-6-8-10-11 0-3-2-4-4-7z" class="W"></path><path d="M816 196h2l-2 1c-4 2-7 4-10 7-4 1-7 4-11 7 2-3 3-5 6-7 1-1 4-3 5-4v-1h2l1-1c3-2 4-2 7-2z" class="q"></path><path d="M800 190l4 1 5 1c1 0 2 1 3 2h0 2c1 1 2 1 2 2-3 0-4 0-7 2l-1-1-1 1-1-1-2 2-1-1h1c-1-1-1-1-2-1 0 0-1 0-1-1l1-1h3l1 1 2-2v-1h-3c-1 1-2 1-4 1h-1l-1-1c-1 0-1-1-3-1l1-1v1h3 1 0l-2-1 1-1z" class="F"></path><path d="M783 187v1c2-1 2-1 4-1v1l-2 2c-1 1-2 1-2 2v3l2 1 2 2h1c1-1 3-1 4 0v-1c1-1 1-1 2-1l1-2v1c0 1-1 1-2 3l-1 1h-7-1l1-1v-1h-1-2c0 2 2 3 3 4h0c-1 1-1 0-2 1l-1 1h3v3h0c-1 0-2 0-4-1l-1-1-2-1v-1c1-1 1-3 1-5v-1s0-1 1-1l-1-1c0-2 1-1 1-3s1-3 3-4z" class="R"></path><path d="M779 197c1 0 2 2 2 2 0 2 0 2 1 4h-1c-1 1 0 1-1 1l-2-1v-1c1-1 1-3 1-5z" class="e"></path><path d="M787 188v1l2 1h3v-1h2l-1-1h1l1 1c1 1 2 1 4 2l2 1h0-1-3v-1l-1 1 1 1s0 1 1 2h-1c-1 1-2 3-3 3h-1c1-2 2-2 2-3v-1l-1 2c-1 0-1 0-2 1v1c-1-1-3-1-4 0h-1l-2-2-2-1v-3c0-1 1-1 2-2l2-2z" class="d"></path><path d="M787 189l2 1h3c1 0 1 1 2 2 0 1-1 2-2 3-2 1-3 0-5 0-1 0-2-1-3-2 1-2 2-3 3-4z" class="D"></path><path d="M780 177l1-2c3 2 5 5 7 7s5 5 9 6l3 2-1 1c-2-1-3-1-4-2l-1-1h-1l1 1h-2v1h-3l-2-1v-1-1c-2 0-2 0-4 1v-1c-2 1-3 2-3 4s-1 1-1 3l1 1c-1 0-1 1-1 1v1c0 2 0 4-1 5l-2-2c-1-2-1-3-2-5v-2c-1-6 3-11 6-16z" class="C"></path><path d="M778 191c0 2 0 4 1 5v1c0 2 0 4-1 5l-2-2 2-2v-7z" class="S"></path><path d="M782 182h1c1 1 1 1 2 1 1 1 2 1 3 2-2 0-4 1-5 2-2 1-3 2-3 4s-1 1-1 3l1 1c-1 0-1 1-1 1-1-1-1-3-1-5-1-2 0-2-2-3 1-1 1-1 1-2 2-1 3-2 4-4h1z" class="e"></path><path d="M782 182l-1-1c0-1-1-1-1-2h1c1 0 2 0 2 1l1 1c0 1 2 1 4 1 2 2 5 5 9 6l3 2-1 1c-2-1-3-1-4-2l-1-1h-1l1 1h-2v1h-3l-2-1v-1-1c-2 0-2 0-4 1v-1c1-1 3-2 5-2-1-1-2-1-3-2-1 0-1 0-2-1h-1z" class="N"></path><path d="M788 185c2 1 3 2 4 2v2 1h-3l-2-1v-1-1c-2 0-2 0-4 1v-1c1-1 3-2 5-2z" class="G"></path><path d="M192 186c-1 2-2 2-4 3 2-1 4-1 6-1l-1 1 1 1c3 0 6 0 9-1 2 1 3 2 5 2h0c2 3 3 5 2 9v3l-1 3-3 5c-3 4-6 7-10 9-2 2-5 3-7 4-2 0-3 0-4 1-1 0-3 1-4 2h0c-1 0-2-1-3 0-1 0-2 0-2 1 1 1 2 3 5 3l1 1-1 1h1c2 2 3 4 4 6l-1 1 1 1v1l1 2v1 1c1 4 0 9 0 13h0c-1-1-1-1-1-2h-3l-1-1v2c-1-1 0-2-1-4 1-9-1-15-8-21l-6-6c1-2 8-2 10-3 2 0 4-1 5-1v-23c-1-2-1-4-2-6-2-3-6-4-9-5l21-3z" class="g"></path><path d="M186 192h2c5 0 12 0 15 5 2 1 2 3 2 5-1 7-5 11-10 15-4 3-8 4-13 6v-17c0-3 0-8 2-11 0-2 1-2 2-3z" class="x"></path><path d="M603 164c14 0 28-2 41-8v1c5 3 12 5 18 3 6-3 10-9 13-14 3 3 4 9 8 12 3 1 4 1 7 1l1 2h0c-1 1-1 1-1 2l-1-1h0c-1 1-1 0-1 2h-1l-1-1c-2 0-3 0-4 2-1 1 0 2 0 4h0v1c-1 1-1 1-1 2h-1c0-1 0-2-1-3h-1v-1-8c-1-3-1-7-2-10-1 3-2 5-3 7h-1l-1 3v2s-3 4-3 5c-2 1-3 2-4 4-1 3-4 5-6 7 0 0-1 1-1 2l-2 1c-3 0-5 1-9 2h0v-1c1 0 1-1 1-1l-3 1h-2c-2 0-4 0-6 1h-3l1-2h-3 0-1c-2 0-3 0-4-1l-3-1h0c1 1 1 1 2 1v1h-1c-1 0 0 0-1 1v-1h-3l-2 1h-1-2l-7-2h0 3v-1h-2c-1 0-2 0-3-1h-1c-2-1-5-2-7-3-2 0-4-2-6-2v-1l-2-2h-1c-1-2-3-3-4-4 2 0 8 3 10 2v-2h1c-2-1-5-2-8-2-4-1-8-3-11-5-2-1-4-2-6-4 3 0 5 2 9 4 1 0 3 1 5 2l1 1c2 0 3 1 5 1l1 1h2c1 0 1 1 2 1h4 9v-1h-6z" class="l"></path><path d="M678 160c0 1 0 2 1 3h0c0 2 0 2 1 4h0v1h1l1 1h0v1c-1 1-1 1-1 2h-1c0-1 0-2-1-3h-1v-1-8z" class="T"></path><path d="M645 162c4 1 6 2 10 1h6v1l-3 2-2-1-2 1-1-1c-2 0-3-2-5-2-1 0-2 0-3-1h0z" class="B"></path><path d="M645 162c1 1 2 1 3 1 2 0 3 2 5 2l1 1 2-1 2 1-1 1-6 2-1-1h1l-1-1c-1 0-1 1-2 0 1 0 1 0 1-1l-2-1c-1 1-2 1-3 2l-1-2 2-3z" class="N"></path><path d="M604 166h16c4 0 8-1 11-2 0 1-1 2-1 2h-3c0 1-1 2-2 2v1c-1 0-2 0-3-1h-2c-6 0-10 0-16-2z" class="I"></path><path d="M637 163c2 0 2 0 4-1 2 0 3-1 4 0h0l-2 3 1 2-3 3c-2 0-3 0-5-1h0-2c-1 0-1-1-2-1h-7c1 0 2-1 2-2h3s1-1 1-2h0l5-1h1z" class="E"></path><path d="M637 163c2 0 2 0 4-1 2 0 3-1 4 0h0l-2 3c0 1 0 1-1 1v1h-3c0-2 0-3-2-4h0z" class="D"></path><path d="M636 163v1l-3 1 1 1h0c1-1 1-1 2 0l-1 2 1 1h-2c-1 0-1-1-2-1h-7c1 0 2-1 2-2h3s1-1 1-2h0l5-1z" class="B"></path><path d="M666 161c1-1 0-1 1-1v2h1c0-1 1-1 1-2 1-1 2-2 3-2v-1l-1 3v2s-3 4-3 5c-2 1-3 2-4 4-1 3-4 5-6 7 0 0-1 1-1 2l-2 1c-3 0-5 1-9 2h0v-1c1 0 1-1 1-1l-3 1 1-1c0-1 0-1 1-1v-1c-1 0-2 0-4-1l2-1v-1c1-1 1-2 1-3 1-1 2-2 3-2l3-2 6-2 1-1 3-2v-1c2-1 3-2 5-2z" class="U"></path><path d="M655 175h2c-1 1-2 3-4 3 0 1-1 1-2 1 1-1 1-2 1-3h0c1-1 2-1 3-1z" class="Q"></path><path d="M652 176c0 1 0 2-1 3l-4 2-3 1 1-1c0-1 0-1 1-1v-1c0-1 1-1 1-1l5-2z" class="G"></path><path d="M651 173h1c1 1 2 1 4 1l-1 1c-1 0-2 0-3 1h0l-5 2c1-2 2-2 2-4l2-1z" class="N"></path><path d="M663 168c1-1 3-4 3-5h2c1 0 2-2 3-3v2s-3 4-3 5c-2 1-3 2-4 4l-5 4-1-1c2-1 4-3 5-6h0z" class="O"></path><path d="M648 171l4-1-1 3-2 1c0 2-1 2-2 4 0 0-1 0-1 1-1 0-2 0-4-1l2-1v-1c1-1 1-2 1-3 1-1 2-2 3-2z" class="V"></path><path d="M644 177l5-3c0 2-1 2-2 4 0 0-1 0-1 1-1 0-2 0-4-1l2-1z" class="m"></path><path d="M666 161c1-1 0-1 1-1v2h1c0-1 1-1 1-2 1-1 2-2 3-2v-1l-1 3c-1 1-2 3-3 3h-2c0 1-2 4-3 5-2 2-4 4-7 6-2 0-3 0-4-1h-1l1-3-4 1 3-2 6-2 1-1 3-2v-1c2-1 3-2 5-2z" class="D"></path><path d="M666 161c0 1 0 1-1 2s-2 2-3 2l-1-1v-1c2-1 3-2 5-2z" class="O"></path><path d="M661 164l1 1c-1 2-3 3-4 4s-2 1-3 2l3-3-1-1 1-1 3-2z" class="F"></path><path d="M657 167l1 1-3 3-3 2h-1l1-3-4 1 3-2 6-2z" class="d"></path><path d="M644 167c1-1 2-1 3-2l2 1c0 1 0 1-1 1 1 1 1 0 2 0l1 1h-1l1 1-3 2c-1 0-2 1-3 2 0 1 0 2-1 3v1l-2 1c2 1 3 1 4 1v1c-1 0-1 0-1 1l-1 1h-2c-2 0-4 0-6 1h-3l1-2h-3 0-1c-2 0-3 0-4-1l-3-1h0c1 1 1 1 2 1v1h-1c-1 0 0 0-1 1v-1h-3l-2 1h-1-2l-7-2h0 3v-1h-2c-1 0-2 0-3-1h-1c-2-1-5-2-7-3-2 0-4-2-6-2v-1l-2-2h-1c-1-2-3-3-4-4 2 0 8 3 10 2v-2h1 8c6 2 10 2 16 2h2c1 1 2 1 3 1v-1h7c1 0 1 1 2 1h2 0c2 1 3 1 5 1l3-3z" class="V"></path><path d="M621 175c1 1 2 1 3 1v2h-2l-2-2 1-1z" class="u"></path><path d="M627 176v-3l1-1 3 1c-1 1-2 2-4 3z" class="W"></path><path d="M625 168h7c1 0 1 1 2 1h2 0c-2 2-2 3-3 6-2 0-3 1-4 2h0l3 1h-3l-1-1c-2-1-1 0-1-1h0c2-1 3-2 4-3l-3-1 1-2h0-4v-1-1z" class="G"></path><path d="M629 170v-1c1 1 2 1 3 1l-1 3-3-1 1-2h0zm-44-4c2 0 8 3 10 2v-2h1 8c6 2 10 2 16 2h2c1 1 2 1 3 1v1c-6 1-11-1-17-1-2 0-3 0-5-1h-7l5 7c-4-3-7-4-11-5h-1c-1-2-3-3-4-4z" class="Q"></path><path d="M608 176c-3-1-4-2-6-4h-1v-1c-1 0-2-1-2-2 1 1 1 1 2 1h1 3 0 5 5l2 2c1 0 2 0 3-1h1l2 2c-1 0-1 0-2 2l-1 1 2 2v1h1 0c1 1 1 1 2 1v1h-1c-1 0 0 0-1 1v-1h-3l-2 1h-1-2l-7-2h0 3v-1h-2c-1 0-2 0-3-1h-1c1-1 2-1 3-2z" class="a"></path><path d="M608 176c1 0 2 0 4 1l2 1c1 0 2 1 3 1-1 1 0 1-1 1-1 1-1 1-1 2l-7-2h0 3v-1h-2c-1 0-2 0-3-1h-1c1-1 2-1 3-2z" class="W"></path><path d="M644 167c1-1 2-1 3-2l2 1c0 1 0 1-1 1 1 1 1 0 2 0l1 1h-1l1 1-3 2c-1 0-2 1-3 2 0 1 0 2-1 3v1l-2 1c2 1 3 1 4 1v1c-1 0-1 0-1 1l-1 1h-2c-2 0-4 0-6 1h-3l1-2h-3 0-1c-2 0-3 0-4-1l-3-1h-1v-1h2 5 3l-3-1h0c1-1 2-2 4-2 1-3 1-4 3-6 2 1 3 1 5 1l3-3z" class="X"></path><path d="M633 175l1-1c1-1 2-1 3-1s2 1 3 1l1-1 1 1c-3 2-6 3-10 4l-3-1h0c1-1 2-2 4-2z" class="U"></path><path d="M642 178c2 1 3 1 4 1v1c-1 0-1 0-1 1l-1 1h-2c-2 0-4 0-6 1h-3l1-2h-3 0-1c-2 0-3 0-4-1h8c3 0 5-1 8-2z" class="W"></path><path d="M644 167c1-1 2-1 3-2l2 1c0 1 0 1-1 1 1 1 1 0 2 0l1 1h-1l1 1-3 2c-1 0-2 1-3 2-1 0-2 1-3 1l-1-1-1 1c-1 0-2-1-3-1s-2 0-3 1l-1 1c1-3 1-4 3-6 2 1 3 1 5 1l3-3z" class="R"></path><defs><linearGradient id="I" x1="721.298" y1="135.319" x2="742.527" y2="177.275" xlink:href="#B"><stop offset="0" stop-color="#070605"></stop><stop offset="1" stop-color="#2b2929"></stop></linearGradient></defs><path fill="url(#I)" d="M702 156c3-3 6-7 8-10s3-6 4-8c1-1 2-1 3-1 7 0 14 1 21 1 4 1 10 0 14 2s7 5 9 8l2 2c-10-1-20-5-30 3-2 1-4 4-4 6s1 5 2 7c3 3 8 4 12 5 2 0 3 0 5-1 2 0 5-3 7-2l-1 1v2l2 1 1 1-1 3-2 3c-2 1-3 2-5 4l-2 1h-2l1 2h-1-1-3-5c-1 0-2-1-3-1h-2-2v1l-2 1c-1 0-2-1-3-2-1 1-2 2-4 1h-7c-6 0-12-1-19 0h0l-1-1 1-2 8-13-1-2-5 6c0-1 0-2-1-3l-2 1v-1c-1-1-1-2 0-3h0-1l-1-1 3-2h2l-1-1h0l2-2v2h0c2-1 3-2 5-3-1-2-1-3 0-5z"></path><path d="M714 146c0-2 1-5 3-6 1-1 2 0 3 0l-1 2-1 1c1 2 1 3 2 4l1 2h0c-1 2-1 3-2 3l-1 1v-4c-1 0-1 0-2-1v1h-2-2l2-3z" class="b"></path><path d="M712 149l2-3h2l1 1-1 1v1h-2-2z" class="W"></path><path d="M718 149v-6c1 2 1 3 2 4l1 2h0c-1 2-1 3-2 3l-1 1v-4z" class="O"></path><path d="M735 141c5 0 10 1 15 2 1 1 3 1 4 2-10 1-21 1-27 8l-2 3h-1c-1-2 0-3 0-5 1-2 3-3 5-3h0v1c2-1 4-2 5-2h1c2-1 3-1 5-2 1-1 2-1 3-1h5 0l-5-1h-2c-2-1-3-1-6-1v-1z" class="Q"></path><path d="M721 149c1-2 1-3 3-4h7c4-1 8-3 12-1-1 0-2 0-3 1-2 1-3 1-5 2h-1c-1 0-3 1-5 2v-1h0c-2 0-4 1-5 3 0 2-1 3 0 5h1l-2 3h-1l-1-3v-7h0z" class="U"></path><path d="M724 151c0 2-1 3 0 5h1l-2 3h-1l-1-3c1-1 1-2 1-3l2-2z" class="m"></path><path d="M720 140c5 1 10 1 15 1v1c3 0 4 0 6 1h2l5 1h0-5c-4-2-8 0-12 1h-7c-2 1-2 2-3 4l-1-2c-1-1-1-2-2-4l1-1 1-2z" class="D"></path><path d="M720 140c5 1 10 1 15 1v1c-5 0-10 1-16 0l1-2z" class="n"></path><path d="M718 153l1-1c1 0 1-1 2-3v7l1 3h1c1 5 1 7 6 11l-1 1-2-1h-1v3c0 2 1 3 2 5v2c-1 0-3-5-4-6l-1-1v-2c-1 0-1-1-1-1-1-1-1-2-2-2l-1-15z" class="L"></path><path d="M722 159h1c1 5 1 7 6 11l-1 1-2-1h-1l-1-1c-1 0-1-1-2-2h0c-1-1-1-1-2-1 1-1 1-1 1-2h1c-1-1-1-1-1-2v-1c0-1 1-2 1-2z" class="S"></path><path d="M716 149v-1c1 1 1 1 2 1v4l1 15c1 0 1 1 2 2 0 0 0 1 1 1v2l1 1v2l-2 1h0-4 0v-2l-1-1v-1l-1-12v-8h0l-2-2 1-2h2z" class="X"></path><path d="M714 149h2l-1 4-2-2 1-2z" class="m"></path><path d="M717 175c1-1 2-1 3-1l1 3h-4 0v-2z" class="a"></path><path d="M719 168c1 0 1 1 2 2 0 0 0 1 1 1v2l1 1v2l-2 1h0l-1-3-1-6z" class="O"></path><path d="M712 149h2l-1 2 2 2h0v1c-1 1-1 2-1 2-1 1-1 2-2 2h-2c-1 4-5 8-8 12l-1-2-5 6c0-1 0-2-1-3l-2 1v-1c-1-1-1-2 0-3h0-1l-1-1 3-2h2l-1-1h0l2-2v2h0c2-1 3-2 5-3l4-4v-1c1-2 2-2 4-2l2-5z" class="T"></path><path d="M696 165h1c0 1-1 2-2 4-1-2-1-2-1-4h2z" class="D"></path><path d="M694 165c0 2 0 2 1 4l-2 2c-1-1-1-2 0-3h0-1l-1-1 3-2z" class="B"></path><path d="M698 169v-1c0-2 5-6 7-7 0 2-2 5-3 6-2 0-3 1-4 2z" class="L"></path><path d="M698 169c1-1 2-2 4-2l-1 1-5 6c0-1 0-2-1-3l3-2z" class="N"></path><path d="M712 149h2l-1 2 2 2h0v1c-1 1-1 2-1 2-1 1-1 2-2 2h-2c-1 4-5 8-8 12l-1-2 1-1c1-1 3-4 3-6 2-2 3-5 5-7l2-5z" class="Q"></path><path d="M713 151l2 2h0v1c-1 1-1 2-1 2-1 1-1 2-2 2h-2c1-2 2-5 3-7z" class="U"></path><path d="M729 170l2 2h1 3 5c0 1-1 2-1 3l1 1h1c1-1 2 0 4 0 1 0 2 0 3-1 1 0 1 0 3-1l1-1 2 1 2-2 1 1-1 3-2 3c-2 1-3 2-5 4l-2 1h-2l1 2h-1-1-3-5c-1 0-2-1-3-1h-2-2v1l-2 1c-1 0-2-1-3-2-2-2-3-5-3-8l2-1v-2c1 1 3 6 4 6v-2c-1-2-2-3-2-5v-3h1l2 1 1-1z" class="W"></path><path d="M731 172h1l2 2c0 1-1 1-2 1l2 1v1h-1l-2-1v-3-1z" class="q"></path><path d="M747 184l1-1c1-1 2-3 3-4l2-3h3l-2 3c-2 1-3 2-5 4l-2 1z" class="u"></path><path d="M729 170l2 2v1h0v3h-1c-1-1-1-2-2-3h-1c0 1 0 1 1 3 0 1 0 1-1 2-1-2-2-3-2-5v-3h1l2 1 1-1z" class="R"></path><path d="M732 172h3c2 1 3 1 4 2l-1 2 2 1h1c1-1 2 0 3 0l2 2-6 1-5-5-1-1-2-2z" class="B"></path><path d="M735 172h5c0 1-1 2-1 3l1 1h1c1-1 2 0 4 0 1 0 2 0 3-1 1 0 1 0 3-1l1-1 2 1c-2 2-5 4-8 5l-2-2c-1 0-2-1-3 0h-1l-2-1 1-2c-1-1-2-1-4-2z" class="H"></path><path d="M723 174c1 1 3 6 4 6v-2c1-1 1-1 1-2l6 6-1 1h0v1 1h-2-2v1l-2 1c-1 0-2-1-3-2-2-2-3-5-3-8l2-1v-2z" class="Q"></path><path d="M727 182h2v-1l4 3v1h-2-2v1c-1-1-1-2-2-4z" class="V"></path><path d="M721 177l2-1c1 2 3 4 2 7l1 1c0-1 0-1 1-2 1 2 1 3 2 4l-2 1c-1 0-2-1-3-2-2-2-3-5-3-8z" class="C"></path><defs><linearGradient id="J" x1="704.506" y1="168.182" x2="716.202" y2="182.839" xlink:href="#B"><stop offset="0" stop-color="#65514e"></stop><stop offset="1" stop-color="#7d6d6c"></stop></linearGradient></defs><path fill="url(#J)" d="M715 153v8l1 12v1l1 1v2h0 4 0c0 3 1 6 3 8-1 1-2 2-4 1h-7c-6 0-12-1-19 0h0l-1-1 1-2 8-13c3-4 7-8 8-12h2c1 0 1-1 2-2 0 0 0-1 1-2v-1z"></path><path d="M712 177l2 1v2l-2-1v-2z" class="R"></path><path d="M699 183c1-1 2-1 3-2l1 2v1l-1 1-3-2z" class="F"></path><path d="M718 181h0c1 1 1 1 1 2s1 2 1 3h-7c2-1 4-2 5-3v-1-1z" class="Z"></path><path d="M705 179c2 1 5 1 7 2v3h-1-2c-1-1-3-4-4-5z" class="R"></path><path d="M715 153v8h-1l-2 2-1-1 1-2h1l-1-1v-1c1 0 1-1 2-2 0 0 0-1 1-2v-1z" class="F"></path><path d="M712 177c-1-1-2-1-2-2v-2c2 2 2 3 4 4l2-2v-1l1 1v2h0l1 3h-3v-1l-1-1-2-1z" class="d"></path><path d="M710 173l2-3h1c1 0 1 2 1 2l2 1v1 1l-2 2c-2-1-2-2-4-4z" class="E"></path><path d="M721 177h0c0 3 1 6 3 8-1 1-2 2-4 1 0-1-1-2-1-3s0-1-1-2h0v-1l-1-3h4z" class="M"></path><path d="M706 171c0 3 0 6-2 9-1 1-1 2-1 3l-1-2c-1 1-2 1-3 2-2 1-1 1-3 1 0-1 0-1 1-1h1c3-3 5-8 8-12z" class="N"></path><path d="M710 158h2v1c-1 1-2 1-3 3 1 1 1 0 1 1-1 3-2 5-4 8-3 4-5 9-8 12h-1c-1 0-1 0-1 1l-2-1 8-13c3-4 7-8 8-12z" class="T"></path><path d="M375 470l4 12 2-2 1 1h0c2 2 1 3 2 5 0 4 1 7 0 11v17 3h1 0l1-5v2l1-2c0 3 1 9 0 12v1l1 1 3 12c-1 3-1 4-3 5l-4 3c2 0 3-1 4-2l1-1h2c2-2 4-3 6-5l1 1c-1 2-11 8-11 9 1 0 1 1 1 2h1 1v-1c1-2 1-2 2-2l1 1v1h1c7-2 13-6 20-7 1 1 2 1 3 2l2 3-2 4c-2 2-4 5-6 7l-4 4v2c0 1 0 2 1 2 0 4-2 8-4 10-1 1-1 3-3 3v-1l-1-1c-1-1-2-1-3-1l1 4c1 8-1 17-2 25l1 1c-3-1-5-3-7-5-6-5-11-10-16-17-3-4-6-9-9-14-1-1-2-3-3-5h0c0-1 1-2 1-3-1 1-3 3-4 3-5 9-12 19-21 23h0c2-1 4-3 6-5h0l3-3c1-1 3-3 4-5l3-4v-1c1-1 3-3 4-5v-3-2-1h-1c-1-2 0-3 0-5-1 0-1-1-1-2l-1-3c-1-1-1-2-1-3-1-2-1-1-1-2-1-2 0-1 0-2s-1-2-1-3c0-3 1-6 0-9 0 0 0-1 1-1v-5h0c1-1 0-3 0-4v-1-1c0-1 1-3 1-4-1-1-1-1-1-3l-1-1c-1 0-1-1-2-2h0c-1 0-1 0-2-1l2-1 6 3c0-1 1-5 0-6s-2-1-3-1c2-2 3-1 3-1 1 0 1-3 1-3l3-3c2-6 3-11 7-15 1-2 1-3 3-3l2-2c0-1 1-2 2-3l1-2z" class="K"></path><path d="M359 565v-6h0 3l2 2-1 1c-1 1-3 3-4 3z" class="r"></path><path d="M358 555c-1-4-3-7-3-10-2-7-2-14-1-21h1c0 11 2 20 6 30-1 0-2 0-3 1z" class="P"></path><path d="M363 562l1-1c5 8 9 16 15 24a79.93 79.93 0 0 0 13 13c1 2 1 2 1 3 1 1 3 3 3 4l1 1c-3-1-5-3-7-5-6-5-11-10-16-17-3-4-6-9-9-14-1-1-2-3-3-5h0c0-1 1-2 1-3z" class="s"></path><path d="M384 546c2 0 3-1 4-2l1-1h2c2-2 4-3 6-5l1 1c-1 2-11 8-11 9 1 0 1 1 1 2h1 1v-1c1-2 1-2 2-2l1 1v1h1c7-2 13-6 20-7 1 1 2 1 3 2l2 3-2 4c-2 2-4 5-6 7l-4 4v2c0 1 0 2 1 2 0 4-2 8-4 10-1-3-3-5-3-8-2-5-1-10-5-13-3-2-5-2-8-2-8 1-16 3-24 3-2 0-5 0-6-1 1-1 2-1 3-1 3-1 7-1 10-2l1-1 12-5z" class="g"></path><path d="M387 548c1 0 1 1 1 2h1l-4 1h-1c-4 0-7 2-10 2 4-2 9-3 13-5z" class="D"></path><path d="M384 546c2 0 3-1 4-2l1-1h2c2-2 4-3 6-5l1 1c-1 2-11 8-11 9-4 2-9 3-13 5l-5 1-1-1 2-1h1l1-1 12-5z" class="u"></path><path d="M375 470l4 12 2-2 1 1h0c2 2 1 3 2 5 0 4 1 7 0 11v17 3h1 0l1-5v2l1-2c0 3 1 9 0 12v1l1 1 3 12c-1 3-1 4-3 5l-4 3-12 5-1 1c-3 1-7 1-10 2-4-10-6-19-6-30 0-4-1-8 1-12 1 1 1 2 2 3 1-1 1-4 2-6-1 0-2 0-3-1 0-5 2-9 3-13 2-6 3-11 7-15 1-2 1-3 3-3l2-2c0-1 1-2 2-3l1-2z" class="h"></path><path d="M374 472v1c0 1-1 2-1 2l-4 6c-4 6-5 13-7 20-1 2-2 5-2 8-1 0-2 0-3-1 0-5 2-9 3-13 2-6 3-11 7-15 1-2 1-3 3-3l2-2c0-1 1-2 2-3z" class="J"></path><defs><linearGradient id="K" x1="387.625" y1="536.511" x2="367.492" y2="486.539" xlink:href="#B"><stop offset="0" stop-color="#0d0d0d"></stop><stop offset="1" stop-color="#33302e"></stop></linearGradient></defs><path fill="url(#K)" d="M379 482l2-2 1 1h0c2 2 1 3 2 5 0 4 1 7 0 11v17 3h1 0l1-5v2l1-2c0 3 1 9 0 12v1l1 1c-2 2-3 5-5 7-3 4-6 9-10 12 1-6 3-11 5-17 5-14 6-32 1-46z"></path><path d="M504 138c1-3 1-5 1-8l1-1c2 2 4 6 4 9 2 9-2 21-7 29-3 5-8 10-13 14-3 2-6 4-9 7l-2 1c-6 5-13 9-19 14s-11 12-14 19c-1 4-3 8-3 12v5c0 2-1 3-1 4-1 3 0 6 1 9 0 1-1 2-2 3v1l1 3h0c1 11 3 23 8 32l2 2v1l1 1c1 1 7 3 8 4-1 1-2 1-3 1l-5 1c-1 1 0 8 1 10l3 16h1c2 2 3 6 4 8v5h-2l7 25h0l2 3v3h-4 0l-3-1c0 1 0 2 1 3-2 0-3-1-4-2l-1 1-2-2h-4-1-2-1-1-1c-1 0-2 0-4-1v1l2 1c0 1-1 2-1 3l-1 1-1 2c-2-1-5-3-7-4l-1 1h0c-3-1-5-2-7-2l2 10-1 1c1 2 2 5 2 7l-2-2c-1 0-1-3-1-4l-2 1-2-5-2-3c0-3-1-6-2-10h0c0-1-1-3-1-5l-6-9c-1 1-1 2-1 4h-1v-1c-3-1-2-2-3-4v-1-2c0-1 0-3-1-4-1-3-3-3-3-6v-1 2h-1v-2l-4-18c-1-6-1-11-3-16l-1-10c-1 1 0 2-1 3 0 0 0-1-1-2v-1s0-1-1-2h0v-1c-1 1 0 2-2 2h0-1l-2-4c0-3-1-6-1-9v-2c-1-1-2-2-2-3v-1l-2 1v1l-1-1-1-1-2-5c1-3 2-7 3-10h0c0-2 0-3 1-4 0-2 1-3 0-4 3 0 4-1 5-3l2-3v-4h2c0-2 2-4 2-7h0l2-3 1-6c0-1 0-2 1-3h0c1 2 1 3 1 5h1l1-3c0-2-1-4-1-6h1c2-1 2-2 2-3l-1-1v-1h0c1-1 2-2 2-3l5-10c1-2 2-3 3-4l1-2c2-1 2-1 3-3h2c2-1 4-2 5-3v-1c1 1 2 1 3 2l5-2c-1-1-1-2-3-2v-1h-7c-1 1 0 1-2 1-1-1-3 0-5 0 0-1-1-2-1-3h-1c-2 0-3-1-4-1l-2-1c-1 0-2 0-3-1l-1-1-1-1v-3l-1-1h-2l-1-1v1l-2-2 2-1 3 2c1 1 1 1 3 1l1 1c0-1 0-1 1-1 2-1 2-1 4 0 1-1 2-1 3-1h1v-4h0l1-1v-1l2-1 12 3h1c2-1 4-1 6-1 1-1 3 0 4 0s1-1 2-1h14c17 0 35-4 47-17 3-3 4-7 5-11z" class="v"></path><path d="M418 234c0-3 0-5 2-7v3c-1 5 0 9-1 13-1-2 0-6 0-9h-1z" class="T"></path><path d="M430 231c0-1-1 0 0-1v-1c0-2 0 0 1-2 0-1-1-2 0-3 0-1 0-1-1-2 1-1 1-2 2-3v-1c1-1 1-2 2-3l1-2c0-1 1-1 1-2l1-1c0-1 1-2 2-3l1 1-1 1s0 1-1 2c0 2-3 5-3 7s-1 3-1 5c-1 4-3 8-4 12v1 5-2l1 1c0 1 0 2-1 3-2-3-1-8 0-12z" class="l"></path><path d="M425 249v-13h0c1 1 0 3 1 5v7-1c1-2 1-4 0-6v-3h1c0-2 0-3 1-4h1c0-1 0-2 1-3-1 4-2 9 0 12 0 4 1 8 1 11v5c-2-6-3-12-3-17h-1v13l-2-5v-1z" class="E"></path><path d="M416 239c-1-1-1-3-1-4v-1-1l1-1v-2c1-1 0-1 1-2 0-1 0-1 1-2 0-1 1-2 1-2l1-1v-3h1v3h0l1-1v1c1-1 1-2 1-2v-1-1c2-5 6-11 10-16v1l-3 7c-1 0-1 1-2 2 0 2-2 4-2 6 0 1 0 2-1 4-1-2 0-3 0-5l-3 8-1 1v-2l-1 5v-3c-2 2-2 4-2 7h0c-2 2-2 3-2 5z" class="f"></path><path d="M418 234h1c0 3-1 7 0 9l1 1v10-6h2 1v-1l1-2c-1-1-1 0-1-1 1-1 0-1 1 0 1 2 1 2 0 4-1 5 0 9-2 14v2c1 2 1 3 1 5v1c-1-1-2-1-3-2h0c-1-3-1-6-2-9 1 0 0 0 1-1h1c-1-2-1-4-1-5v-1-6c-1-1-1-3-1-4h-1v-1-2c1-2 1-3 1-5h0z" class="c"></path><path d="M508 140c0 9-4 17-11 24-6 6-15 11-24 14-2 1-5 2-8 2-2-1-4-1-6-1h-2l1-1h4v-1l1 1c1 0 1 0 2-1l4-1c5 0 9-2 14-4 11-6 19-12 23-25 0 0 1-3 1-4s0-2 1-3z" class="V"></path><defs><linearGradient id="L" x1="448.318" y1="174.028" x2="441.004" y2="191.397" xlink:href="#B"><stop offset="0" stop-color="#b98b89"></stop><stop offset="1" stop-color="#d8c0b4"></stop></linearGradient></defs><path fill="url(#L)" d="M448 177l1-1c1 1 2 2 3 2 1-1 3-1 4-1s2 1 2 1l-1 1h2c2 0 4 0 6 1l-37 7c-1-1-1-2-3-2v-1h2c3-1 4 0 6-2v-2c1-1 4-1 6-1v1l-1 1-1-1h-1v1h3c3 0 5 0 7-2l2-2z"></path><path d="M448 177l1-1c1 1 2 2 3 2-2 1-4 1-6 1l2-2z" class="a"></path><path d="M506 147c-4 13-12 19-23 25-5 2-9 4-14 4l-4 1h1c1-1 2-1 4-1l3-2c3 0 6-2 8-4l2-1h-1c1-1 1-1 1-2-4 1-9 2-13 0 2-1 4-1 6-1l16-6c1-1 2-1 3-2s2-2 3-2h0l3-4c1-1 1-2 2-3s2-1 3-2z" class="U"></path><path d="M476 166l16-6c1-1 2-1 3-2s2-2 3-2c0 3-1 4-4 7h-1c-2 1-2 1-5 1-4 1-8 2-12 2z" class="O"></path><defs><linearGradient id="M" x1="460.766" y1="167.632" x2="456.99" y2="179.131" xlink:href="#B"><stop offset="0" stop-color="#816966"></stop><stop offset="1" stop-color="#a88481"></stop></linearGradient></defs><path fill="url(#M)" d="M466 168l4-1c4 2 9 1 13 0 0 1 0 1-1 2h1l-2 1c-2 2-5 4-8 4l-3 2c-2 0-3 0-4 1h-1c-1 1-1 1-2 1l-1-1v1h-4s-1-1-2-1-3 0-4 1c-1 0-2-1-3-2l-2-1c-3 1-5 0-8-1 2 0 3-1 4 0h2l8-1h1v-2h7l5-2h0v-1z"></path><defs><linearGradient id="N" x1="476.151" y1="168.846" x2="469.718" y2="176.037" xlink:href="#B"><stop offset="0" stop-color="#846664"></stop><stop offset="1" stop-color="#9d7c76"></stop></linearGradient></defs><path fill="url(#N)" d="M456 177c4-1 10-1 12-4 2-1 2-2 4-2 2-1 7-2 9-1-2 2-5 4-8 4l-3 2c-2 0-3 0-4 1h-1c-1 1-1 1-2 1l-1-1v1h-4s-1-1-2-1z"></path><path d="M418 234c0 2 0 3-1 5v2 1h1c0 1 0 3 1 4v6 1c0 1 0 3 1 5h-1c-1 1 0 1-1 1 1 3 1 6 2 9h0l-2-1c0 2 1 3 1 4l-1 2c2 2 3 4 5 6h1l-3 2-10-12-5-8-3-5c0-2-2-4-3-6l1-1 3 2v-1c-1-4-1-6-1-10 3 7 5 15 9 21h1l-1 1c0 1 0 1 1 1 0 1 2 4 3 4v-2h0c-1-2-1-2-2-2v-1c-1-1-1-3-1-4 1-2 0-11 0-14 1-2 1-7 1-8l1 7s1 0 1-1h0v-2-1c0-2 0-3 2-5z" class="T"></path><path d="M417 242h1c0 1 0 3 1 4v6 1c0 1 0 3 1 5h-1c-1 1 0 1-1 1 1 3 1 6 2 9h0l-2-1c0 2 1 3 1 4l-1 2-2-3 1-1c1-2 0-3 0-5-2-3-1-8-1-11v-7l1-4z" class="C"></path><path d="M417 264v-9h0c1 1 1 3 1 4 1 3 1 6 2 9h0l-2-1c0 2 1 3 1 4l-1 2-2-3 1-1c1-2 0-3 0-5z" class="S"></path><defs><linearGradient id="O" x1="414.527" y1="264.024" x2="409.466" y2="266.462" xlink:href="#B"><stop offset="0" stop-color="#c19d99"></stop><stop offset="1" stop-color="#d4b8ad"></stop></linearGradient></defs><path fill="url(#O)" d="M404 250c3 7 7 13 12 20l2 3c2 2 3 4 5 6h1l-3 2-10-12-5-8-3-5c0-2-2-4-3-6l1-1 3 2v-1z"></path><path d="M420 187c1 1 2 1 3 2-13 10-19 28-20 44-1 2-1 5 0 7 0 4 0 6 1 10v1l-3-2c0-1 0-1-1-2v-1c-1-3-1-6-2-9 1-2 1-4 2-6-2-1-2-4-2-7 0-2-1-4-1-6h1c2-1 2-2 2-3l-1-1v-1h0c1-1 2-2 2-3l5-10c1-2 2-3 3-4l1-2c2-1 2-1 3-3h2c2-1 4-2 5-3v-1z" class="b"></path><path d="M413 191h2l-5 7-1-2 1-2c2-1 2-1 3-3z" class="D"></path><path d="M401 210h1 1l2-2-3 7h-2l-1-1v-1h0c1-1 2-2 2-3z" class="e"></path><path d="M401 210l5-10c1-2 2-3 3-4l1 2-5 10h0l-2 2h-1-1z" class="O"></path><path d="M400 215h2c-1 4-2 6-1 10 0 2-1 4-1 6-2-1-2-4-2-7 0-2-1-4-1-6h1c2-1 2-2 2-3z" class="S"></path><path d="M425 249v1l2 5v-13h1c0 5 1 11 3 17l1 4c1 5 4 10 5 15 1 2 2 5 4 7l-3 1c-3-1-4-2-6-4v2c-1-1-1 0-1-1l-2-2c-1-1-1-2-2-2-1-1-2-1-3-2l-1 2c-2-2-3-4-5-6l1-2c0-1-1-2-1-4l2 1h0 0c1 1 2 1 3 2v-1c0-2 0-3-1-5v-2c2-5 1-9 2-14l1 1z" class="C"></path><path d="M425 262c-1-2 0-3 0-5l1-1c1 2 0 5 0 6h-1z" class="O"></path><path d="M427 276h1l3 3c0 1 0 2 1 3v2c-1-1-1 0-1-1l-2-2-2-5z" class="U"></path><path d="M418 273l1-2c0-1-1-2-1-4l2 1c0 4 2 6 4 9l-1 2c-2-2-3-4-5-6z" class="F"></path><path d="M426 262c0 2 1 3 1 4v-4-1c2 2 2 4 2 7h2c-1-2-1-2-1-3l2-2c1 5 4 10 5 15 1 2 2 5 4 7l-3 1c-3-1-4-2-6-4-1-1-1-2-1-3l-3-3h-1v-1c-1-2-2-4-2-7v-6h1z" class="S"></path><path d="M432 279l-1-1v-3h1s1 0 1 1c0 2 3 4 4 5v1h-3c-1-1-2-2-2-3z" class="L"></path><path d="M437 278c1 2 2 5 4 7l-3 1c-3-1-4-2-6-4-1-1-1-2-1-3h1c0 1 1 2 2 3h3v-1-2l-1-1h1z" class="e"></path><path d="M397 172c1 1 1 1 3 1l1 1c0-1 0-1 1-1 2-1 2-1 4 0 1-1 2-1 3-1h1v-4h0l1-1s1 1 1 2c1 2 2 3 4 4l3 3c2 0 3 1 4 1h2l4-1c2-2 7-2 10-2 3 1 5 2 8 1l2 1-1 1-2 2c-2 2-4 2-7 2h-3v-1h1l1 1 1-1v-1c-2 0-5 0-6 1v2c-2 2-3 1-6 2h-2-7c-1 1 0 1-2 1-1-1-3 0-5 0 0-1-1-2-1-3h-1c-2 0-3-1-4-1l-2-1c-1 0-2 0-3-1l-1-1-1-1v-3l-1-1h-2l-1-1v1l-2-2 2-1 3 2z" class="X"></path><path d="M401 175h1 3 2c1 1 2 1 3 2 2 0 3 1 4 1 0 1 0 0 1 1h-4c-1-1 0-1-1-1l-5-1c-2 0-3 0-4-2z" class="k"></path><path d="M429 176c2-2 7-2 10-2 3 1 5 2 8 1l2 1-1 1h-1c-3 0-9 2-12 1 0-1-1-1-2-1s-1 1-2 1c-3 0-5 1-8-1h2l4-1z" class="a"></path><path d="M429 176c2-2 7-2 10-2 3 1 5 2 8 1l2 1-1 1h-1l-1-1c-1 0-2 1-4 1-1 0-1-1-2-1h-4-7z" class="V"></path><path d="M398 174c1 0 1 1 3 1h0c1 2 2 2 4 2l5 1c1 0 0 0 1 1h4 0c2 0 3 0 5 2h0c-1 1-1 2-1 3v-1h3 1 1 1v-1l2 1h0v-1c1-1 1-1 2-1s3-1 4-1v2c-2 2-3 1-6 2h-2-7c-1 1 0 1-2 1-1-1-3 0-5 0 0-1-1-2-1-3h-1c-2 0-3-1-4-1l-2-1c-1 0-2 0-3-1l-1-1-1-1v-3z" class="n"></path><path d="M415 179h0l1 1v1h-3c0 1-1 1-1 0-2 0-2-1-2-2h1 4z" class="w"></path><path d="M499 149c3-3 4-7 5-11 2 0 3 1 4 1v1c-1 1-1 2-1 3s-1 4-1 4c-1 1-2 1-3 2s-1 2-2 3l-3 4h0c-1 0-2 1-3 2s-2 1-3 2l-16 6c-2 0-4 0-6 1l-4 1v1h0l-5 2h-7v2h-1l-8 1h-2c-1-1-2 0-4 0-3 0-8 0-10 2l-4 1h-2c-1 0-2-1-4-1l-3-3c-2-1-3-2-4-4 0-1-1-2-1-2v-1l2-1 12 3h1c2-1 4-1 6-1 1-1 3 0 4 0s1-1 2-1h14c17 0 35-4 47-17z" class="v"></path><path d="M499 149c3-3 4-7 5-11 2 0 3 1 4 1v1c-1 1-1 2-1 3s-1 4-1 4c-1 1-2 1-3 2s-1 2-2 3v-1l1-1v-1h0v-1c1-1 1-1 1-2-2 1-1 2-4 3z" class="D"></path><path d="M413 165l12 3c6 1 12 1 18 1 8 0 15 1 23-1v1h0l-5 2h-7v2h-1l-8 1h-2c-1-1-2 0-4 0-3 0-8 0-10 2l-4 1h-2c-1 0-2-1-4-1l-3-3c-2-1-3-2-4-4 0-1-1-2-1-2v-1l2-1z" class="W"></path><path d="M416 173c2 0 2 0 4 1h4v-1h-2l1-1h2v2h1l1-1h0l1 1h-1c-1 1-1 1-2 3h-2c-1 0-2-1-4-1l-3-3z" class="V"></path><path d="M413 165l12 3c6 1 12 1 18 1 8 0 15 1 23-1v1h0l-5 2h-7c-2 0-4 0-6 1h-16c-1-1-2-1-3-1-2 0-3-1-4-1-2 0-3-1-4 0l-2-1h0c-2 1-4 0-5-1-1 0-1 1-2 1 0-1-1-2-1-2v-1l2-1z" class="Q"></path><defs><linearGradient id="P" x1="412.571" y1="284.185" x2="457.567" y2="365.249" xlink:href="#B"><stop offset="0" stop-color="#010303"></stop><stop offset="1" stop-color="#2b2a2b"></stop></linearGradient></defs><path fill="url(#P)" d="M395 222h0c1 2 1 3 1 5h1l1-3c0 3 0 6 2 7-1 2-1 4-2 6 1 3 1 6 2 9v1c1 1 1 1 1 2l-1 1c1 2 3 4 3 6l3 5 5 8 10 12 3-2h-1l1-2c1 1 2 1 3 2 1 0 1 1 2 2l2 2c0 1 0 0 1 1v-2c2 2 3 3 6 4l3-1 1 2c2 2 5 6 7 7 1 0 2 0 3-1v1l1 1c1 1 7 3 8 4-1 1-2 1-3 1l-5 1c-1 1 0 8 1 10l3 16h1c2 2 3 6 4 8v5h-2l7 25h0l2 3v3h-4 0l-3-1c0 1 0 2 1 3-2 0-3-1-4-2l-1 1-2-2h-4-1-2-1-1-1c-1 0-2 0-4-1v1l2 1c0 1-1 2-1 3l-1 1-1 2c-2-1-5-3-7-4l-1 1h0c-3-1-5-2-7-2l2 10-1 1c1 2 2 5 2 7l-2-2c-1 0-1-3-1-4l-2 1-2-5-2-3c0-3-1-6-2-10h0c0-1-1-3-1-5l-6-9c-1 1-1 2-1 4h-1v-1c-3-1-2-2-3-4v-1-2c0-1 0-3-1-4-1-3-3-3-3-6v-1 2h-1v-2l-4-18c-1-6-1-11-3-16l-1-10c-1 1 0 2-1 3 0 0 0-1-1-2v-1s0-1-1-2h0v-1c-1 1 0 2-2 2h0-1l-2-4c0-3-1-6-1-9v-2c-1-1-2-2-2-3v-1l-2 1v1l-1-1-1-1-2-5c1-3 2-7 3-10h0c0-2 0-3 1-4 0-2 1-3 0-4 3 0 4-1 5-3l2-3v-4h2c0-2 2-4 2-7h0l2-3 1-6c0-1 0-2 1-3z"></path><path d="M446 332c-1 0-1-1-1-2h-1v-3h1l2 2 1-1c1 1 1 2 2 4-1-1-2-1-4-1v1h0z" class="B"></path><path d="M442 375c0-2-1-2-2-3v1 1h-1v-3-1-1-1c-1 0-2-1-3-1v-2l2 2h1c1 1 1 2 3 3l2 1c0 1-1 2-1 3l-1 1z" class="E"></path><path d="M425 369l1-1c3 2 6 3 8 5l-1 1h0c-3-1-5-2-7-2l-1-3z" class="g"></path><path d="M442 357l1 1v1 1l1 2h-2l1 2-1 2c-1 0-1 0-2-1l-1 1v-1l2-2c-2-1-2-2-3-3 1 0 1-1 2-2 1 1 1 2 2 3v-1-3z" class="c"></path><path d="M417 324c1 2 1 3 1 6h1v-3c0 2 1 5 1 7v7c1 3 1 7 1 10-1-2-1-5-1-7l-2-8-1-12z" class="E"></path><path d="M412 281l4 3-1 1-1-1c1 2 2 1 2 2h-1-2c-3 0-5 0-8 2v-4l2-2c2 0 3 0 5-1z" class="a"></path><defs><linearGradient id="Q" x1="423.262" y1="287.168" x2="416.637" y2="287.843" xlink:href="#B"><stop offset="0" stop-color="#826b6b"></stop><stop offset="1" stop-color="#9e877e"></stop></linearGradient></defs><path fill="url(#Q)" d="M416 284l3 1c4 2 7 5 12 7l4 1 1 2c-8-1-16-6-23-9h2 1c0-1-1 0-2-2l1 1 1-1z"></path><path d="M401 296c1 2 1 3 2 5s1 2 1 4c0 1 0 8 1 9 0-1 0-1 1-2 0-4 0-9 1-13h0c0 2 1 3 0 5 1-1 1-3 2-4l1-2c0-1 0-1 1-2v-1c1-1 2-1 3-2l1-2v1 5c-5 1-5 4-7 8-1 2-1 4-1 6s-1 4-1 5l-1 1v-1c0-1-2-3-3-4l-1-16z" class="f"></path><path d="M443 360c2 0 3 1 4 2h2v1l2 3 4 4h1-4-1-2-1-1-1c-1 0-2 0-4-1v1c-2-1-2-2-3-3h-1l1-1 1-1c1 1 1 1 2 1l1-2-1-2h2l-1-2z" class="S"></path><path d="M443 364v1c0 1-1 2-2 3h0c-1-1-2-1-2-1h-1l1-1 1-1c1 1 1 1 2 1l1-2z" class="H"></path><path d="M442 369l1-2c2 0 3 1 5 2l1 1h-1-1-1c-1 0-2 0-4-1z" class="F"></path><path d="M449 363l2 3 4 4h1-4l-1-1c-2-1-2-4-2-6z" class="B"></path><path d="M417 322v2l1 12 2 8h-3c-1-1-1-1-1 0l-1-3-2-5c1-1-1-3-1-4 0-2-1-4-1-6l2-2c1 0 2 0 3-1l1-1z" class="G"></path><path d="M415 341v-1-3c1-1 0-1 0-2v-1l3 2 2 8h-3c-1-1-1-1-1 0l-1-3z" class="Z"></path><defs><linearGradient id="R" x1="417.493" y1="283.595" x2="421.64" y2="276.685" xlink:href="#B"><stop offset="0" stop-color="#2d292a"></stop><stop offset="1" stop-color="#483835"></stop></linearGradient></defs><path fill="url(#R)" d="M411 269l10 12 3-2h-1l1-2c1 1 2 1 3 2 1 0 1 1 2 2l2 2c0 1 0 0 1 1v-2c2 2 3 3 6 4l3-1 1 2c2 2 5 6 7 7 1 0 2 0 3-1v1l1 1c1 1 7 3 8 4-1 1-2 1-3 1l-1-1h-2c-4 0-9-2-13-3-2 0-4 0-6-1l-1-2-4-1c-5-2-8-5-12-7-2-3-6-7-8-10 0-1-1-3-2-4v-1c0-1 1-1 2-1z"></path><path d="M435 290c0-1 1-1 2-1 3 3 5 4 9 5 1 0 2 1 4 1l-1-1c1 0 2 0 3-1v1l1 1c1 1 7 3 8 4-1 1-2 1-3 1l-1-1h-2c-1-1-8-3-10-3-2-1-3-2-5-2l-5-4z" class="D"></path><path d="M432 284v-2c2 2 3 3 6 4l3-1 1 2c2 2 5 6 7 7l1 1c-2 0-3-1-4-1-4-1-6-2-9-5l-5-5z" class="R"></path><path d="M438 286l3-1 1 2v1 1h0c-2 0-3-1-4-3z" class="F"></path><path d="M424 277c1 1 2 1 3 2 1 0 1 1 2 2l2 2c0 1 0 0 1 1l5 5c-1 0-2 0-2 1l5 4h0c-7-1-15-8-19-13l3-2h-1l1-2z" class="V"></path><path d="M424 277c1 1 2 1 3 2 1 0 1 1 2 2l2 2c0 1 0 0 1 1l5 5c-1 0-2 0-2 1-4-2-8-8-11-11h-1l1-2z" class="S"></path><path d="M407 311c0-2 0-4 1-6 2-4 2-7 7-8v7 1l1 10 1 7-1 1c-1 1-2 1-3 1l-2 2c0 2 1 4 1 6 0 1 2 3 1 4v1l-1 1h0c0-2-1-3 0-4h0v-1l-1-1-1-1v-2l-1-3c0 2 0 3 1 5v1l2 10c0 3-1 6-1 9-1-3-3-6-4-8v-1l-1-1v-3l-1-5-1-12-2-9c1 1 3 3 3 4v1l1-1c0-1 1-3 1-5z" class="t"></path><path d="M409 333c1 3 1 4 1 7-1 1-1 2-3 2h0l-1-1v-3h2l1-5z" class="F"></path><path d="M409 332v1l-1 5h-2l-1-5h2c1 0 1-1 2-1z" class="S"></path><path d="M413 313l3 5c-1 2 0 3-1 4l-1 1c-1-1-2-1-2-2s-1-1-1-2c0-2 1-4 2-6z" class="c"></path><path d="M412 321l1-1 2 1v1l-1 1c-1-1-2-1-2-2z" class="T"></path><path d="M415 305l1 10v3l-3-5c-1-1 0-2-1-3v-2-1c0-1 0-1 1-2h2z" class="V"></path><path d="M407 311c0-2 0-4 1-6 2-4 2-7 7-8v7l-1-3c-1 0-1 0-2 1-3 3-2 8-3 12-2 5-1 12 0 18-1 0-1 1-2 1h-2l-1-12-2-9c1 1 3 3 3 4v1l1-1c0-1 1-3 1-5z" class="C"></path><path d="M407 311v8h-2l-1-1v1 2l-2-9c1 1 3 3 3 4v1l1-1c0-1 1-3 1-5z" class="B"></path><path d="M407 343c1 2 3 5 4 8 0-3 1-6 1-9l-2-10v-1c-1-2-1-3-1-5l1 3v2l1 1 1 1v1h0c-1 1 0 2 0 4h0l1-1v-1l2 5 1 3c0-1 0-1 1 0h3c0 2 0 5 1 7 1 5 3 10 3 15 1 1 1 3 1 3l1 3 2 10-1 1c1 2 2 5 2 7l-2-2c-1 0-1-3-1-4l-2 1-2-5-2-3c0-3-1-6-2-10h0c0-1-1-3-1-5l-6-9c-1 1-1 2-1 4h-1v-1c-3-1-2-2-3-4v-1-2h1v-1c-1-2 0-3 0-5z" class="q"></path><path d="M415 355h1l4 3 2 6c-1-1-3-4-5-5h-1c0-1 0-2-1-3v-1z" class="X"></path><path d="M415 355l-1-1h2 0l1 1c1-2 0-3-1-5v-1c1 0 2 2 2 2 2 2 1 5 2 7l-4-3h-1z" class="a"></path><path d="M408 348h0l3 5c-1 1-1 2-1 4h-1v-1c-3-1-2-2-3-4v-1-2h1v-1h1z" class="O"></path><path d="M407 348h1c0 3 1 5 1 8-3-1-2-2-3-4v-1-2h1v-1z" class="u"></path><path d="M417 362h1c1 0 2 2 3 3v1c1 1 1 3 2 5v5l2 2v1c1 1 1 2 2 4h0c1 2 2 5 2 7l-2-2c-1 0-1-3-1-4l-2 1-2-5-2-3c0-3-1-6-2-10h0c0-1-1-3-1-5z" class="a"></path><path d="M418 367c2 2 3 3 3 5 1 1 1 2 1 3v1 4l-2-3c0-3-1-6-2-10z" class="q"></path><path d="M423 376l2 2v1c1 1 1 2 2 4h0c1 2 2 5 2 7l-2-2c-1 0-1-3-1-4-2-2-2-5-3-8z" class="d"></path><path d="M458 327c2 2 3 6 4 8v5h-2l7 25h0l2 3v3h-4 0l-3-1c0 1 0 2 1 3-2 0-3-1-4-2l-1 1-2-2h-1l-4-4-2-3v-1h-2c-1-1-2-2-4-2v-1-1l-1-1c-1-1-1-2-2-3h0v-2l-1-3h1v-3c0-1 0-1-1-1v-6c-1-3-2-6-2-9h1v3l1 5 1-1v-1-1-3l1 1c0-2 1-2 1-3 1 0 1 0 1 1v1l3 5v-5h0v-1c2 0 3 0 4 1 0 1 0 1 1 1 2 0 2-1 4-1 0 1 0 1 1 2 0 1 1 1 2 1v-1l-1-1 1-1c-1-1-1-1-1-2v-1l1-2z" class="c"></path><path d="M449 336h1l1 2c1 1 2 2 1 4h0l-2-1c0-2-1-3-1-5z" class="B"></path><path d="M448 343h2v1c0 1 2 2 3 2h1 0l3 3h-1c0-1-1-1-2-1s-1-1-2-1h-1l-1 1c-1-2-2-3-2-5z" class="T"></path><path d="M440 336c2 1 2 1 3 3 0 1 1 2 1 4h1v3c-2 0-3-1-3-3v-1c0-1-1-1-1-2-1-1-1-2-1-3v-1z" class="E"></path><path d="M458 327c2 2 3 6 4 8v5h-2l-3-11 1-2z" class="p"></path><path d="M447 346v-3h1c0 2 1 3 2 5l1-1h1c1 0 1 1 2 1 2 1 3 3 4 6l-1 1-1 1-2-2c-1-1-1-2-2-2s-1 1-1 1v-2c0-2-1-2-1-2h-3v-3z" class="E"></path><path d="M443 352c-1-3-2-3-1-5 1 1 2 1 4 2l1-3v3h3s1 0 1 2v2c0 1 1 2 2 3v1h-3c-1-1-2-2-3-2v-1c0-1 0-1-1-2h-1c-1-1-1-1-2-1v1z" class="H"></path><path d="M447 349h3s1 0 1 2h-1v2l-1 1c-2-2-2-3-2-5z" class="B"></path><path d="M443 351c1 0 1 0 2 1h1c1 1 1 1 1 2v1l1 6c0 1 1 1 1 1h-2c-1-1-2-2-4-2v-1-1l-1-1c-1-1-1-2-2-3h1 1v-2h1v-1z" class="C"></path><path d="M443 351c1 0 1 0 2 1h1c1 1 1 1 1 2v1l1 6c-1 0-2-1-2-2-1-1-1-2-1-2l-1-2c0-1 0-2-1-4z" class="N"></path><path d="M447 355c1 0 2 1 3 2h3l3 3-1 1 1 1 3-1 2 2h2 0c2 1 2 2 4 2h0l2 3v3h-4 0l-3-1c0 1 0 2 1 3-2 0-3-1-4-2l-1 1-2-2h-1l-4-4-2-3v-1s-1 0-1-1l-1-6z" class="O"></path><path d="M459 368v-2-1h2c0 1 0 1 1 1s2 1 3 1c0 1 1 2 1 3l-1 1-3-1h-1l-2-2z" class="N"></path><path d="M452 362h1l2 4c1 1 1 2 3 2h1l2 2h1c0 1 0 2 1 3-2 0-3-1-4-2l-1 1-2-2h-1l-4-4c2-1 0-2 1-4z" class="H"></path><path d="M459 371v-1l-1-1h1l2 1h1c0 1 0 2 1 3-2 0-3-1-4-2z" class="T"></path><path d="M447 355c1 0 2 1 3 2h3l3 3-1 1 1 1h-3-1c-1 2 1 3-1 4l-2-3v-1s-1 0-1-1l-1-6z" class="E"></path><path d="M450 357h3l3 3-1 1 1 1h-3-1v-1c-1-1-2-3-2-4z" class="L"></path><defs><linearGradient id="S" x1="396.495" y1="315.562" x2="402.176" y2="315.145" xlink:href="#B"><stop offset="0" stop-color="#836e6a"></stop><stop offset="1" stop-color="#a6928e"></stop></linearGradient></defs><path fill="url(#S)" d="M395 222h0c1 2 1 3 1 5h1l1-3c0 3 0 6 2 7-1 2-1 4-2 6 1 3 1 6 2 9v1c1 1 1 1 1 2l-1 1c1 2 3 4 3 6l3 5 5 8c-1 0-2 0-2 1v1c1 1 2 3 2 4 2 3 6 7 8 10l-3-1-4-3c-2 1-3 1-5 1l-2 2v4c-2 3-3 5-4 8l1 16 2 9 1 12 1 5v3l1 1v1c0 2-1 3 0 5v1h-1c0-1 0-3-1-4-1-3-3-3-3-6v-1 2h-1v-2l-4-18c-1-6-1-11-3-16l-1-10c-1 1 0 2-1 3 0 0 0-1-1-2v-1s0-1-1-2h0v-1c-1 1 0 2-2 2h0-1l-2-4c0-3-1-6-1-9v-2c-1-1-2-2-2-3v-1l-2 1v1l-1-1-1-1-2-5c1-3 2-7 3-10h0c0-2 0-3 1-4 0-2 1-3 0-4 3 0 4-1 5-3l2-3v-4h2c0-2 2-4 2-7h0l2-3 1-6c0-1 0-2 1-3z"></path><path d="M393 281h0c1 2 3 3 4 5-1 1-1 2-1 3v2c-1 3-1 8-1 11h0l-1 2-1-10c-1 1 0 2-1 3 0 0 0-1-1-2v-1l-1-7v-2l2-2 1-2z" class="G"></path><path d="M392 283v3l-2 1v-2l2-2z" class="Z"></path><path d="M392 286c1 2 1 5 1 8-1 1 0 2-1 3 0 0 0-1-1-2v-1l-1-7 2-1zm1-5h0c1 2 3 3 4 5-1 1-1 2-1 3v2c-1 3-1 8-1 11h0c0-2-1-7 0-10h0v-1c0-2-1-5-2-7 0-1 0-1-1-1l1-2z" class="t"></path><path d="M405 284v4c-2 3-3 5-4 8l1 16 2 9 1 12 1 5v3 1l-1 1-1-6c0-3-1-6-2-9-2-13-4-25-3-38 2-3 3-5 6-6z" class="k"></path><path d="M396 267h0c1 1 1 1 1 2 2 1 3 2 5 3v2c0 1 0 2 1 3 0 1 0 2-1 3h4l-2 1c1 1 1 1 2 1h1l-2 2c-3 1-4 3-6 6 0-2 1-4-1-5 0 1 0 1-1 2v-1c-1-2-3-3-4-5h0l-1 2-2 2v-2c-1-1-1-2-2-2l-1-1c0-2 0-2 1-3l-1-2c0-1 1-2 1-3s3-4 4-4c1-1 2 0 3-1h1z" class="M"></path><path d="M391 279h-1v-1-2-2-1l1 1h1l1 3h0c0 1 0 2 1 3l-1 1h0l-1 2-2 2v-2c0-1 0-3 1-4z" class="a"></path><path d="M391 279h0l2 2-1 2-2 2v-2c0-1 0-3 1-4z" class="W"></path><path d="M394 272c1-2 2-2 4-1 2 0 3 2 4 3 0 1 0 2 1 3-2 1-3 2-4 2h-1c-2-1-4-1-5-2h0l-1-3c0-1 1-2 2-2z" class="D"></path><path d="M394 272c0 2 0 3 2 5h1l1 2c-2-1-4-1-5-2h0l-1-3c0-1 1-2 2-2z" class="E"></path><path d="M393 277c1 1 3 1 5 2h1c1 0 2-1 4-2 0 1 0 2-1 3h4l-2 1c1 1 1 1 2 1h1l-2 2c-3 1-4 3-6 6 0-2 1-4-1-5 0 1 0 1-1 2v-1c-1-2-3-3-4-5l1-1c-1-1-1-2-1-3z" class="q"></path><path d="M393 277c1 1 3 1 5 2h1c1 0 2-1 4-2 0 1 0 2-1 3l-2 3v2h0l-1-1c0-1 0-1-1-2 0 1 0 1-1 1l-3-3c-1-1-1-2-1-3z" class="k"></path><path d="M399 258v-8h1c1 2 3 4 3 6l3 5 5 8c-1 0-2 0-2 1v1c1 1 2 3 2 4 2 3 6 7 8 10l-3-1-4-3c-2 1-3 1-5 1h-1c-1 0-1 0-2-1l2-1h-4c1-1 1-2 1-3-1-1-1-2-1-3v-2c-2-1-3-2-5-3 0-1 0-1-1-2h0v-2c0-1-1-1-1-2s1-4 2-5h2z" class="R"></path><path d="M402 263l1-1c0-1-1-1-1-1l-1-1v-3c1-1 1-1 2-1l3 5c-1 1-2 2-4 2z" class="I"></path><path d="M397 258h2v3c1 1 1 2 1 3 1 1 1 1 1 2s1 2 1 3c-1-1-1-1-2-1-1-1-2-2-2-3v-2c-1 1-2 1-2 2 0-1-1-1-1-2s1-4 2-5z" class="X"></path><path d="M402 263c2 0 3-1 4-2l5 8c-1 0-2 0-2 1v1h-2 0l1-2c-1 0-2-1-3-1l1-1-1-3h-3v-1z" class="e"></path><path d="M396 265c0-1 1-1 2-2v2c0 1 1 2 2 3 1 0 1 0 2 1l3 3 1 1c0 1 1 2 2 3 1 2 4 3 4 5-2 1-3 1-5 1h-1c-1 0-1 0-2-1l2-1h-4c1-1 1-2 1-3-1-1-1-2-1-3v-2c-2-1-3-2-5-3 0-1 0-1-1-2h0v-2z" class="u"></path><path d="M406 273c0 1 1 2 2 3 1 2 4 3 4 5-2 1-3 1-5 1h-1c-1 0-1 0-2-1l2-1 1 1 1-1-1-1h-3c1-1 1-1 2-1v-1h-1c0-1 0-3 1-4z" class="V"></path><path d="M395 222h0c1 2 1 3 1 5h1l1-3c0 3 0 6 2 7-1 2-1 4-2 6 1 3 1 6 2 9v1c1 1 1 1 1 2l-1 1h-1v8h-2c-1 1-2 4-2 5s1 1 1 2v2h-1c-1 1-2 0-3 1-1 0-4 3-4 4s-1 2-1 3l1 2c-1 1-1 1-1 3l1 1c1 0 1 1 2 2v2 2l1 7s0-1-1-2h0v-1c-1 1 0 2-2 2h0-1l-2-4c0-3-1-6-1-9v-2c-1-1-2-2-2-3v-1l-2 1v1l-1-1-1-1-2-5c1-3 2-7 3-10h0c0-2 0-3 1-4 0-2 1-3 0-4 3 0 4-1 5-3l2-3v-4h2c0-2 2-4 2-7h0l2-3 1-6c0-1 0-2 1-3z" class="a"></path><path d="M385 289l1-1c1-2 0-3 0-5l1-1c1 2 2 3 2 6v2l1 1c-1 1 0 2-2 2h0-1l-2-4z" class="q"></path><path d="M389 252h1 1l6 3 1 1-1 2c-1 1-2 4-2 5s1 1 1 2v2h-1s0-1-1-1l1-1c-1-2 0-3-1-4-1 1-1 1-1 3h-3c-1-1-1 0-1-1-1-1-1-1-1-2 1-3 1-5 2-8l-1-1z" class="W"></path><path d="M389 252h1 1l2 3c0 2-1 4-2 6h0c-1-1-1-1 0-2v-3-1c-1-1-1-1-1-2l-1-1z" class="G"></path><path d="M379 259c1-2 2-4 4-5 0-1 1-1 2-2s1-3 2-4l1 6v-3h1v1l1 1c-1 3-1 5-2 8l-2 12c-1 3-2 5-2 7v-2c-1-1-2-2-2-3v-1l-2 1v1l-1-1-1-1-2-5c1-3 2-7 3-10h0z" class="R"></path><path d="M379 259v5 1l-1 1v3c1 1 1 2 1 3 1 1 1 1 2 1l-2 2-1-1-2-5c1-3 2-7 3-10z" class="Z"></path><path d="M388 254v-3h1v1l1 1c-1 3-1 5-2 8l-2 12c-1 3-2 5-2 7v-2c-1-1-2-2-2-3v-1l-2 1v1l-1-1 2-2 3-6c2-5 3-8 4-13z" class="X"></path><path d="M395 222h0c1 2 1 3 1 5h1l1-3c0 3 0 6 2 7-1 2-1 4-2 6 1 3 1 6 2 9v1c1 1 1 1 1 2l-1 1h-1v8h-2l1-2-1-1-6-3h-1-1v-1h-1v3l-1-6c-1 1-1 3-2 4s-2 1-2 2c-2 1-3 3-4 5 0-2 0-3 1-4 0-2 1-3 0-4 3 0 4-1 5-3l2-3v-4h2c0-2 2-4 2-7h0l2-3 1-6c0-1 0-2 1-3z" class="a"></path><path d="M390 245l1-2c2 1 3 1 4 2s2 3 3 4c0 2 0 3-1 4-2 0-3 0-4-1-3-2-3-4-3-7z" class="E"></path><path d="M395 222h0c1 2 1 3 1 5h1l1-3c0 3 0 6 2 7-1 2-1 4-2 6 1 3 1 6 2 9v1h-1l-1-3-1 1v-1l-1 1c1 1 2 2 2 3v1c-1-1-2-3-3-4s-2-1-4-2l-1 2h-1c0-1 0-2 1-3s1-2 1-3c2-3 2-5 2-8l1-6c0-1 0-2 1-3z" class="M"></path><path d="M396 232v-3h1c1 2 1 3 1 5l-2-2z" class="V"></path><path d="M396 232l2 2v3c1 3 1 6 2 9v1h-1l-1-3c0-1-1-3-2-4v-8z" class="W"></path><path d="M396 605c1-8 3-17 2-25l-1-4c1 0 2 0 3 1l1 1v3 1c1 0 0 0 1 1 0 0 0 1-1 1v7c0 6-1 12 0 18 2 2 1 7 2 10 0 10 3 24 10 32v-1l5 8c3 4 9 10 14 13 5 4 11 7 17 10 8 2 15 3 23 3l8 1 5 1c1 0 2 0 3 1s2 2 3 2l1 1 4 4 2 2 1-1c-1-1-2-3-3-4h2c1 1 1 3 3 5v-5l1 2h0l-1-1c0-2 0-3 1-5h0v1c1 1 1 2 1 3v1h1v-2-2-1l1-1h-1c1-1 1-1 2-1l1 2 2 2v1-1c0-3-3-6-3-9l-1-4c0-2 0-5-1-7v-5c-1-2-1-4-1-6-1-2-2-4-2-6v-2-2c0 1 1 2 1 3l3 8h1c0-1-3-9-4-11v1c2 1 2 3 3 5h1v-1-1-2l1 1v-1-1c-1-1-1-1-1-3h1 2l1 3 1 2 5 11c1 0 2 2 2 2l6 11c0-1 1-2 2-2 1 1 0 1 1 3 1 0 1-1 2-1v1h1c2 2 3 6 4 9l1 3c0 1 1 3 0 5l2-1 1 1v-1c0-1 1-3 1-5h0v-6-1-1c0-2 1-3 0-5h0l1-1 1-3c0 2-1 4-1 6h1c1-1 1 0 1-1v-2l1-1h0 0l1-1h0v-1-2l-1-1c1-2 0-3 1-4v2h1v-2c1-2 1-4 2-6v-3c1-2 2-2 3-3 0-2 1-3 2-5-1-2 0-4 1-5l4-13c-1-2-2-4-2-6 1-4 4-9 6-12 2-1 3-3 4-4l1-1 24-13h1l3-1h4l-1 1c-2 1-6 4-8 6-1 2-1 5-2 7l-1 6-5 16v1l-14 50-4 15-5 16c0 2-2 5-2 7s-1 4-1 5c-2 3-2 6-3 8l-31 118-11 44c-2 6-3 13-5 20l-54-186c-2-9-6-18-8-26l-4-12c0-1 0-3-1-4 0-1-3-2-3-3-3-2-6-4-8-6l-13-13-2-3c-5-7-9-14-12-22l-2-7c-1-6-1-12-1-19v-4l-1-1z" class="g"></path><path d="M528 721c-1-3-2-4-1-7 1 2 1 2 1 4v3h0z" class="l"></path><path d="M563 669v1 3c0 2 0 3-2 4v-1c-1-1-1-2 0-3v-3l2-1z" class="D"></path><path d="M571 626c1 1-1 5-1 7 1-1 1-2 1-3s1-2 1-3h1c-1 4-2 8-4 12 0 2-1 3-2 4h-1 0c-1-1-1-1 0-1v-1-1l1-1v-2h0l1-3h0l1-1v-1-1s0-1 1-2l1-3z" class="f"></path><g class="C"><path d="M530 729l-10 33-2-2c0-2 0-3-1-5h0c0-6-3-12-5-18l3-3c0 1 1 3 1 4l-1 1h0l-1 1 1 3 3 8v1c1 0 1 1 2 1l-1 1v3h1c1 0 1 0 1-1s1-2 1-2c0-1 0-2 1-3v-1l3-12 1-5 1-1v-1c0-1 0-1 1-2h1z"></path><path d="M516 738h1v-5c-1-2 0-3-1-5v-2l-1-1h0c1-1 1-2 0-2 0-1-1-1-1-2s0-1 1-2v1s0 1 1 2c0 1 1 3 2 5 0 1-1 0 0 1l2-2v4l1 1v1c0 1 0 1 1 2h0 2v2s-1 1-1 2 1 0 0 1v1h-1v-1h-1c-2 0-2 0-2-1 1-1 1-2 3-2-2-1-3-1-4-3v1 2 2l1 2c0 1 0 2 1 3v5l-1 1c0-1-1-2-1-2v-1c-1-1-1-2-1-2l1-1-1-1v-1l-1-1v-1-1z"></path></g><path d="M518 728l2-2v4c0 1 0 2 1 3v1h-1 0c-1-2-2-4-2-6z" class="B"></path><path d="M590 592h1c-10 7-22 12-29 23-1 2-3 5-4 7v1c-1 1-1 2-1 4v1c-1-2-2-4-2-6 1-4 4-9 6-12 2-1 3-3 4-4l1-1 24-13z" class="P"></path><defs><linearGradient id="T" x1="451.157" y1="683.962" x2="454.374" y2="677.979" xlink:href="#B"><stop offset="0" stop-color="#786663"></stop><stop offset="1" stop-color="#9f9893"></stop></linearGradient></defs><path fill="url(#T)" d="M413 650l5 8c3 4 9 10 14 13 5 4 11 7 17 10 8 2 15 3 23 3-1 1-2 1-3 1h-2 1c2 0 2 0 4 2-8-1-16-1-24-4-10-3-17-11-25-18-4-4-8-9-10-14v-1z"></path><path d="M513 788c1 1 1 2 1 4 0 1 0 2 1 2v1l1 1-7 73c0 3 0 8-2 10l6-91z" class="P"></path><path d="M556 637h0l3-9c0 3 0 5-1 7-1 3-1 6-2 9l1-1v-1c1-1 1-3 1-4v1c0 2-1 4-1 7 0 2 0 3-1 5 0 1-1 2-1 3 0 2-1 4-2 6 0 3-1 5-1 7-2 5-3 11-4 16s-4 10-4 16v2 6c0 2 0 5-1 7 0-2 0-4-1-5v-6c0-6 2-12 3-17l6-25c2-8 4-16 5-24z" class="c"></path><path d="M507 662c1 1 2 1 2 2 0 2 1 2 2 3l2 3c1 1 3 2 4 3 5 9 10 17 15 24 2 3 3 6 6 6 1 2 2 3 2 4l2 2c1 1 1 3 1 5 1 2 1 5 1 7h0l-1-2c0-1 0-1-1-2-4-4-7-9-10-13-1-2-3-3-4-5-3-4-6-6-8-10-2-1-6-8-7-11-2-4-5-10-6-16z" class="H"></path><path d="M513 678c1 0 1 0 2 1l2 2 1 1c0 2 1 3 2 4-1 0-1-1-2-1 1 1 2 2 2 4-2-1-6-8-7-11z" class="C"></path><path d="M507 662c1 1 2 1 2 2v2l1 1 1 5 1-1c1 1 2 1 2 3s2 5 3 6v1l-2-2c-1-1-1-1-2-1-2-4-5-10-6-16z" class="S"></path><defs><linearGradient id="U" x1="543.977" y1="665.138" x2="553.023" y2="668.362" xlink:href="#B"><stop offset="0" stop-color="#baa6a1"></stop><stop offset="1" stop-color="#ead7ce"></stop></linearGradient></defs><path fill="url(#U)" d="M557 628v-1c0-2 0-3 1-4l1 5-3 9h0c-1 8-3 16-5 24l-6 25c-1 5-3 11-3 17v6l-2-2c0-1-1-2-2-4-1-3-4-6-5-8 2 1 3 2 4 4 1 0 0 0 1-1l-3-5 2-1 1 1v-1c0-1 1-3 1-5h0v-6-1-1c0-2 1-3 0-5h0l1-1 1-3c0 2-1 4-1 6h1c1-1 1 0 1-1v-2l1-1h0 0l1-1h0v-1-2l-1-1c1-2 0-3 1-4v2h1v-2c1-2 1-4 2-6v-3c1-2 2-2 3-3 0-2 1-3 2-5-1-2 0-4 1-5l4-13z"></path><path d="M540 695v1 1 6l1 1 1-1v6l-2-2c0-1-1-2-2-4-1-3-4-6-5-8 2 1 3 2 4 4 1 0 0 0 1-1l1 1c0-1 1-3 1-4z" class="K"></path><path d="M557 628v-1c0-2 0-3 1-4l1 5-3 9h0 0v-1-1l1-1-1-1c1-1 1-1 1-2v-1l-5 16h0c-1-2 0-4 1-5l4-13z" class="n"></path><path d="M545 663c1-2 1-4 2-6v-3c1-2 2-2 3-3l-7 29c-1 5-3 10-3 15 0 1-1 3-1 4l-1-1-3-5 2-1 1 1v-1c0-1 1-3 1-5h0v-6-1-1c0-2 1-3 0-5h0l1-1 1-3c0 2-1 4-1 6h1c1-1 1 0 1-1v-2l1-1h0 0l1-1h0v-1-2l-1-1c1-2 0-3 1-4v2h1v-2z" class="U"></path><path d="M539 679c0-2 1-3 0-5h0l1-1 1-3c0 2-1 4-1 6h1c1-1 1 0 1-1v-2l1-1h0 0c0 2 0 3-1 4v2l-1 1v2c0 1 0 1-1 2l-1 4v-6-1-1z" class="N"></path><path d="M507 646h2l1 3 1 2 5 11c1 0 2 2 2 2l6 11c0-1 1-2 2-2 1 1 0 1 1 3 1 0 1-1 2-1v1h1c2 2 3 6 4 9l1 3c0 1 1 3 0 5l3 5c-1 1 0 1-1 1-1-2-2-3-4-4 1 2 4 5 5 8-3 0-4-3-6-6-5-7-10-15-15-24-1-1-3-2-4-3l-2-3c-1-1-2-1-2-3 0-1-1-1-2-2l-1-3c0-1-3-9-4-11v1c2 1 2 3 3 5h1v-1-1-2l1 1v-1-1c-1-1-1-1-1-3h1z" class="G"></path><path d="M518 671c0-2 0-3-1-4 0-1 0-2 1-3l6 11 1 3c-1 1-1 3 0 5-1-2-2-3-3-4 0-1 0-1-1-2 0-2-2-4-3-6z" class="w"></path><path d="M507 646h2l1 3 1 2 5 11c1 0 2 2 2 2-1 1-1 2-1 3 1 1 1 2 1 4-2-2-2-3-3-5-2-2-4-3-5-5l-1-1-3-8v-2l1 1v-1-1c-1-1-1-1-1-3h1z" class="V"></path><path d="M507 646h2l1 3 1 2-1 2c-2-3-2-5-3-7z" class="M"></path><path d="M510 653l1-2 5 11c1 0 2 2 2 2-1 1-1 2-1 3 1 1 1 2 1 4-2-2-2-3-3-5-1-5-4-9-5-13z" class="P"></path><path d="M524 675c0-1 1-2 2-2 1 1 0 1 1 3 1 0 1-1 2-1v1h1c2 2 3 6 4 9l1 3c0 1 1 3 0 5l3 5c-1 1 0 1-1 1-1-2-2-3-4-4-3-4-7-8-8-12-1-2-1-4 0-5l-1-3z" class="Y"></path><path d="M527 681c1-1 1-1 2-1s1 0 2-1v1h0c0 3 1 5 3 7l1 1c0 1 1 3 0 5v-1l-8-11z" class="u"></path><path d="M524 675c0-1 1-2 2-2 1 1 0 1 1 3 1 0 1-1 2-1v1h1c2 2 3 6 4 9l1 3-1-1c-2-2-3-4-3-7h0v-1c-1 1-1 1-2 1s-1 0-2 1l-2-3-1-3z" class="q"></path><path d="M503 658c1 2 1 3 1 4 1 2 1 4 2 5 0 3 1 5 1 7 1 1 2 2 2 3 3 6 5 11 9 16 0 1 1 3 2 3 1 2 3 5 3 8 2 1 2 1 2 3v1c0 2 1 4 2 6h0c-1 3 0 4 1 7l1 1c-1 2-1 4-2 6h-1l-1-1c1-1 0-3 0-4v2 5c1 1 1 3 1 4l-1 1c-1-1-1-2-1-4h0 0l-1 1-2-1-1-1v-4l-2 2c-1-1 0 0 0-1l1-1v-2c-1-2-1-4-1-7-1-2 0-1 0-3v-1-2h-2c1-2 1-2 2-3l-1-1s0-1-1-2l-3-7c-1-1-1-1-1-2l-3-6v-1c0-3-3-6-3-9l-1-4c0-2 0-5-1-7v-5c-1-2-1-4-1-6z" class="f"></path><path d="M523 722c1-1 1-1 2-1h0v1c-1 1-1 3-2 4h0c0-1 0-2-1-3h0l1-1z" class="T"></path><g class="D"><path d="M520 696c1 2 3 5 3 8 2 1 2 1 2 3v1c0 2 1 4 2 6-2-1-2-3-4-3v-2l1 1h0c0-1 0-2-1-3-2-4-3-6-3-11z"></path><path d="M518 708c1 1 1 3 1 4h0v1c1 1 1 1 1 2l1 1h0v3l2 3-1 1h0c1 1 1 2 1 3h0c1 2 2 4 1 5h0 0l-1 1-2-1-1-1v-4l-2 2c-1-1 0 0 0-1l1-1v-2c-1-2-1-4-1-7-1-2 0-1 0-3v-1-2h-2c1-2 1-2 2-3z"></path></g><path d="M523 726l-1-1c0 1 0 1-1 2v-1c-2-3 0-3-1-6l1-1 2 3-1 1h0c1 1 1 2 1 3z" class="c"></path><path d="M472 684l8 1 5 1c1 0 2 0 3 1s2 2 3 2l1 1 4 4 2 2 1-1c-1-1-2-3-3-4h2c1 1 1 3 3 5v-5l1 2h0l-1-1c0-2 0-3 1-5h0v1c1 1 1 2 1 3v1h1v-2-2-1l1-1h-1c1-1 1-1 2-1l1 2 2 2v1l3 6c0 1 0 1 1 2l3 7c1 1 1 2 1 2l1 1c-1 1-1 1-2 3h2v2 1c0 2-1 1 0 3 0 3 0 5 1 7v2l-1 1c-1-2-2-4-2-5-1-1-1-2-1-2v-1c-1 1-1 1-1 2s1 1 1 2c1 0 1 1 0 2h0l1 1v2c1 2 0 3 1 5v5h-1c0-1-1-3-1-4l-3 3c2 6 5 12 5 18h0c1 2 1 3 1 5l2 2 10-33c1-3 2-9 4-11 0 3-1 5-1 8l-6 21c-4 16-9 32-11 49l-1-1v-1c-1 0-1-1-1-2 0-2 0-3-1-4v-1l-15-47c-2-5-3-10-5-14l-7-24-2-7-2-4-1-3-9-1c-2-2-2-2-4-2h-1 2c1 0 2 0 3-1z" class="Y"></path><path d="M484 695c1 0 1 0 3 1 1 2 3 4 3 7l1 3h0c0 1 0 2 1 3v1 1h0 0c-2-2-2-4-3-7v-1c0-1 0-2-1-3l-1 2h-1l-2-7z" class="k"></path><path d="M504 743c1-1 1-2 2-3l3 8c0 1 0 4 1 4 0 2 0 4 1 6l-1 2c-1-2-2-5-3-7l-3-10z" class="M"></path><path d="M487 702c0 2 1 3 1 5h1v-1h1v1c0 2 1 4 1 5 2 3 4 7 4 11 1 1 1 2 1 3 0 2 1 2 1 3 1 2 1 3 0 5 0 2 1 3 1 6-2-5-3-10-5-14l-7-24h1z" class="P"></path><path d="M490 703c1 1 2 1 2 2 1 2 2 5 3 7 1 3 2 7 4 10v3c1 0 1 1 2 2 2 4 5 9 5 13-1 1-1 2-2 3-1-4-3-9-5-14-1-1-2-3-2-5v-1l-5-12h0v-1-1c-1-1-1-2-1-3h0l-1-3z" class="X"></path><path d="M482 691l5 3c4 2 6 5 10 8 2 2 5 7 6 10h-1l1 4c0 1 0 1-1 3 0 0 0 1 1 2 0 1 0 0-1 1l-1 1v4c-1-1-1-2-2-2v-3c-2-3-3-7-4-10-1-2-2-5-3-7 0-1-1-1-2-2 0-3-2-5-3-7-2-1-2-1-3-1l-2-4z" class="F"></path><path d="M482 691l5 3c2 3 4 7 6 9 1 1 1 1 1 3l3 5c1 4 3 8 4 12v4c-1-1-1-2-2-2v-3c-2-3-3-7-4-10-1-2-2-5-3-7 0-1-1-1-2-2 0-3-2-5-3-7-2-1-2-1-3-1l-2-4z" class="W"></path><path d="M511 746h2c0 1 1 2 1 4 1 1 2 3 3 5h0c1 2 1 3 1 5l2 2-5 21c-1-5-1-10-2-15l-3-8 1-2c-1-2-1-4-1-6-1 0-1-3-1-4l1-1c0 1 1 3 2 4 0-2 0-3-1-5z" class="q"></path><path d="M509 748l1-1c0 1 1 3 2 4 1 2 1 4 1 6 1 1 2 1 2 2v1 3 2h1v1l-2-1v-1s-1-1-1-2v-1c-1 0 0 0-1-1-1-2-1-5-2-8-1 0-1-3-1-4z" class="G"></path><path d="M472 684l8 1 5 1c1 0 2 0 3 1s2 2 3 2l1 1 4 4 2 2 1-1c-1-1-2-3-3-4h2c1 1 1 3 3 5 0 2 2 6 2 9h-1v1c0 2 1 4 2 6h-1c-1-3-4-8-6-10-4-3-6-6-10-8l-5-3-1-3-9-1c-2-2-2-2-4-2h-1 2c1 0 2 0 3-1z" class="k"></path><path d="M496 691h2c1 1 1 3 3 5 0 2 2 6 2 9h-1c-2-4-4-7-6-11l2 2 1-1c-1-1-2-3-3-4z" class="R"></path><path d="M481 688c9 3 16 9 20 17l1 1c0 2 1 4 2 6h-1c-1-3-4-8-6-10-4-3-6-6-10-8l-5-3-1-3z" class="E"></path><path d="M502 705h1l1 5 1 1c2 0 2 1 3 3v1l3 5c0 2 1 3 1 5 1 2 1 4 3 6l-1 1c1 1 1 1 1 2l-3 3c2 6 5 12 5 18-1-2-2-4-3-5 0-2-1-3-1-4h-2c1 2 1 3 1 5-1-1-2-3-2-4l-1 1-3-8c0-4-3-9-5-13v-4l1-1c1-1 1 0 1-1-1-1-1-2-1-2 1-2 1-2 1-3l-1-4h1 1c-1-2-2-4-2-6v-1z" class="Z"></path><path d="M505 711c2 0 2 1 3 3v1l3 5c0 2 1 3 1 5 1 2 1 4 3 6l-1 1c1 1 1 1 1 2l-3 3-5-15c0-4-2-8-2-11z" class="H"></path><path d="M502 705h1l1 5 1 1c0 3 2 7 2 11l-3-4v1c0 1 0 1 1 2s1 1 1 2h-1v-1h-1v1c1 2 2 4 2 6v3c1 2 2 4 2 7 1 2 2 5 3 7h0c1 2 1 3 1 5-1-1-2-3-2-4l-1 1-3-8c0-4-3-9-5-13v-4l1-1c1-1 1 0 1-1-1-1-1-2-1-2 1-2 1-2 1-3l-1-4h1 1c-1-2-2-4-2-6v-1z" class="d"></path><path d="M504 690v-2-1l1-1h-1c1-1 1-1 2-1l1 2 2 2v1l3 6c0 1 0 1 1 2l3 7c1 1 1 2 1 2l1 1c-1 1-1 1-2 3h2v2 1c0 2-1 1 0 3 0 3 0 5 1 7v2l-1 1c-1-2-2-4-2-5-1-1-1-2-1-2v-1c-1 1-1 1-1 2s1 1 1 2c1 0 1 1 0 2h0l1 1v2c1 2 0 3 1 5v5h-1c0-1-1-3-1-4s0-1-1-2l1-1c-2-2-2-4-3-6 0-2-1-3-1-5l-3-5v-1c-1-2-1-3-3-3l-1-1-1-5c0-3-2-7-2-9v-5l1 2h0l-1-1c0-2 0-3 1-5h0v1c1 1 1 2 1 3v1h1v-2z" class="I"></path><path d="M506 703c1-1 1-1 3-1v4c1 1 0 4 0 5-1-1-1-2-2-3v-1c0-2-1-2-1-4z" class="F"></path><path d="M510 696l3 2 3 7c1 1 1 2 1 2l1 1c-1 1-1 1-2 3h2v2 1c0 2-1 1 0 3 0 3 0 5 1 7v2l-1 1c-1-2-2-4-2-5-1-1-1-2-1-2v-1c-1 1-1 1-1 2s1 1 1 2c1 0 1 1 0 2h0l1 1v2c1 2 0 3 1 5v5h-1c0-1-1-3-1-4s0-1-1-2l1-1c-2-2-2-4-3-6 0-2-1-3-1-5l2-1v-1l-1-1c1-1 0-1 0-2v-3h1v3h1c2-1 2-1 2-3l-2-2 1-1c0-1 0-4-1-5l-2-4c-1-1-1-2-2-4h0z" class="N"></path><path d="M504 690v-2-1l1-1h-1c1-1 1-1 2-1l1 2 2 2v1l3 6c0 1 0 1 1 2l-3-2v-1h-1v1 1c0 1 0 2 1 3l-2 1h-1c0-1 0-2-1-3 0-1 0-2-1-3l-1 1h1c1 2 0 5 1 7h0c0 2 1 2 1 4v1c1 1 1 2 2 3-1 1-1 2-1 4v-1c-1-2-1-3-3-3l-1-1-1-5c0-3-2-7-2-9v-5l1 2h0l-1-1c0-2 0-3 1-5h0v1c1 1 1 2 1 3v1h1v-2z" class="e"></path><path d="M504 690v-2-1l1-1h-1c1-1 1-1 2-1l1 2 2 2v1l3 6c0 1 0 1 1 2l-3-2v-1c0-2-1-3-2-4h-1v3h-1c-1-1-1-1-1-3l-1-1z" class="O"></path><path d="M350 267c4-1 7-1 11-1 3 1 5 1 7 2l1-1h4c-1 2 0 3 1 6h0c2 3 4 6 5 9v-2l1-1-1-2-1 1-1-1c1-2 1-2 1-3l1 1 1 1v-1l2-1v1c0 1 1 2 2 3v2c0 3 1 6 1 9l2 4h1 0c2 0 1-1 2-2v1h0c1 1 1 2 1 2v1c1 1 1 2 1 2 1-1 0-2 1-3l1 10c2 5 2 10 3 16l4 18v2h1v-2 1c0 3 2 3 3 6 1 1 1 3 1 4v2 1c1 2 0 3 3 4v1h1c0-2 0-3 1-4l6 9c0 2 1 4 1 5h0c1 4 2 7 2 10l2 3 2 5c0 4 2 7 3 11 0 2 0 4 1 7v4c0 1 1 3 1 3 0 3 1 6 2 9l-1 2 7 21c1 2 5 10 4 12s1 6 2 9c1 2 1 6 3 9l6 12 3 7c3 4 5 8 7 12v1c0 1 1 2 1 3v2s-1 1-2 1c0 1 0 4-1 5l-1 3v1l-6 5h-1l-4 3h0c-1 0-2 1-3 2v-1l-1 1h-1c-1 0-2 1-3 2h-1l3-3c-2 1-4 1-6 1h-1l-3 4h-1c-1 2-3 3-5 5-3 1-5 2-8 2h4v2h-2c-1 0-1 0-2 1l-1 1c-1-1-2-1-3-2-7 1-13 5-20 7h-1v-1l-1-1c-1 0-1 0-2 2v1h-1-1c0-1 0-2-1-2 0-1 10-7 11-9l-1-1c-2 2-4 3-6 5h-2l-1 1c-1 1-2 2-4 2l4-3c2-1 2-2 3-5l-3-12-1-1v-1c1-3 0-9 0-12l-1 2v-2l-1 5h0-1v-3-17c1-4 0-7 0-11-1-2 0-3-2-5h0l-1-1-2 2-4-12-24-81-2-7c-3-8-5-17-9-25l-2 1c-3 1-6 2-8 2 3-2 6-4 10-7l-4-10c-1-3-2-7-3-10l-10 4v-2c1-1 1-1 1-2l-1-1 7-6c-2-12-6-26-14-36-7 3-15 6-22 10 3-4 7-7 12-10h0c3-1 5-3 8-4h2c2-1 4-3 6-4l-1 1h1l1-1h0c3-1 7-4 9-6l8-4 10-5z" class="l"></path><path d="M361 334l3-1 1 1-3 3v1h0-1c-1 1-2 1-3 1v-1c1-1 2-2 3-4zm12-19h0c1 1 0 1 1 1 1-1 0-1 1-1v-2l1-1c0 2-1 7-2 8v1l-1 1-1-1-2 1c1-3 1-5 3-7z" class="C"></path><path d="M358 338v1c1 0 2 0 3-1-4 5-11 10-16 13l13-13z" class="e"></path><path d="M362 332c1-1 2-1 2-2 1-2 3-4 4-6v-2c1-1 1-2 1-3v-1h1 0c1-1 1-1 1-2-1-2-1 0-1-1 0-2 1-4 0-5s-1-1 0-2c0 0 1 0 2 1h-1v1 3h1c0-2 0-3 1-4h1c-1 1-1 2-1 3h1l1 1c-2 0-2 0-2 2-2 2-2 4-3 7-1 5-3 7-6 11l-3 1 1-2z" class="f"></path><path d="M387 437c0-10-1-19-2-29l1-1c0 2 0 3 1 5 2 6 2 14 2 21 0 4 0 8-1 11v-10-4c-1 2-1 4-1 6v1z" class="c"></path><path d="M373 322c0 1-1 2-1 4l2-3v-1c1-2 2-3 3-5l1-1 2-3 1 1 1-1h1v-2c1 2 1 2 1 4h0c0 1-1 2-1 2 0 1 0 1-1 2 0 2-2 5-3 7-1 1 0 2 0 3l-2 2c0 2 4 5 3 6l-1-1c-1-1-3-2-5-2l-1-1h-1-1c-1 0-2 0-3-1l-3 2-1-1c3-4 5-6 6-11l2-1 1 1z" class="L"></path><path d="M374 325c0 1 0 2 1 3h0l-3 4h-3l5-7z" class="G"></path><path d="M374 325l2-3c1-1 0-1 1-1h1c0 3-1 3-2 5h1c0-1 1-1 1-2l1-1c0 2-2 5-3 6-1 2-1 3-3 4h-1c1-1 3-2 3-4v-1h0c-1-1-1-2-1-3z" class="R"></path><defs><linearGradient id="V" x1="368.45" y1="418.67" x2="409.55" y2="447.83" xlink:href="#B"><stop offset="0" stop-color="#1b2020"></stop><stop offset="1" stop-color="#605454"></stop></linearGradient></defs><path fill="url(#V)" d="M390 416l-1-1v-2h-1c1-2 1-6 1-7v-6c-1-1-1 0-1-1v-1-1-5c0-1 2-2 2-4l-1-10c0-2 0-4 1-6v11c0 2 0 5 1 8v2l1 9v1l1 1v2h1c1 1 0 2 0 3 0 3 0 8 1 11 0 1 1 5 0 6l-1 1h1v-1c1-1 1-2 1-4-1 0 0-1 0-2l1 1v3c0 2 0 6 1 8l-2 19h1v-2h1c-1 1-1 2 0 3 0 5-1 10-3 14l-3 10c0 1-1 3-1 3-1 3-2 7-3 10h-1l-1 3h-1c0 2 0 4-1 5 1-4 0-7 0-11-1-2 0-3-2-5h0l-1-1c1-2 3-3 3-5 1 0 1-2 1-2l-2-2h-1v-6l1-3 4-25v-1c0-2 0-4 1-6v4 10 6c1-1 1-1 1-2s1-1 1-2v-1l1-1c-1-1-1-1-1-2s0-2-1-3h0l1-1c1-2 0-4 0-6 1-1 1-2 1-2l-1-2v-2h0c0-2 1-5 0-7v-1-2h0z"></path><path d="M394 427c0 1 0 1-1 2v-2c0-1 0-1 1-2l1 1-1 1zm-4-11v-3h1 0c0 1 1 2 1 3v1s-1-1-2-1h0z" class="c"></path><path d="M392 402h0l-1-3v1c0-1-1-3 0-4v-3l1 9z" class="f"></path><path d="M388 461v-3c1 1 2 2 1 4 0 1 0 2-1 3l-1-3 1-1z" class="L"></path><path d="M383 462c1-1 2-1 2-2h1v1h2l-1 1v1c-2 0-3 1-5 2l1-3z" class="I"></path><path d="M396 451h1v-2h1c-1 1-1 2 0 3 0 5-1 10-3 14l-3 10c0 1-1 3-1 3-1 3-2 7-3 10h-1v-1l1-3-1-1v-6c1-1 1-3 1-4 0 1 1 2 1 4v-2-1h1v-3h1c3-7 4-14 5-21z" class="n"></path><path d="M388 474c0 1 1 2 1 4v-2-1h1v-3h1l-3 13-1-1v-6c1-1 1-3 1-4z" class="I"></path><path d="M387 463v-1l1 3v3h1c0 2-1 4-1 6 0 1 0 3-1 4v6l1 1-1 3v1l-1 3h-1c0 2 0 4-1 5 1-4 0-7 0-11-1-2 0-3-2-5h0l-1-1c1-2 3-3 3-5 1 0 1-2 1-2l-2-2h-1v-6c2-1 3-2 5-2z" class="R"></path><path d="M387 463v-1l1 3v3l-2 2c0-2 0-4 1-7z" class="S"></path><path d="M382 481c1-1 1-1 1-2l1-1 1-1h0v3l-1 6c-1-2 0-3-2-5z" class="N"></path><path d="M385 480c0 1 0 2 1 3v4l1 1v1l-1 3h-1c0 2 0 4-1 5 1-4 0-7 0-11l1-6z" class="G"></path><path d="M368 268l1-1h4c-1 2 0 3 1 6h0c2 3 4 6 5 9 5 13 8 26 10 39l4 24 1 7 1 5-2 2 1 9c0 1 0 3 1 4 0 4 0 7 1 11v9c2 5 1 14 1 20v4 5 3-3l-1-1c0 1-1 2 0 2 0 2 0 3-1 4v1h-1l1-1c1-1 0-5 0-6-1-3-1-8-1-11 0-1 1-2 0-3h-1v-2l-1-1v-1l-1-9v-2c-1-3-1-6-1-8v-11h0c-1-1-1 0-1-1v-3-2-1-1c-1-3 0-6 0-9v-2c1-1 1 0 2-1 0 0-1-5 0-5 0-1 0-1 1-2v-2c-1-1-1-3-1-4v-2l-1 1h-1v-1c0-2 0-4-1-6h0v-3l-1-1c0-2 0-3 1-4v-1-2c-1-1-1-1-1-3h0c-1-1 0-2 0-4-1 1-1 0-2 1v-2h1v-2-1-1l-1-2v-1-1c-1-2-1-4-2-6v-1c0-1 0-1-1-2s0-2-1-3v-1l-1-3c0-1-1-2-1-3h-1l-1-1c1-1 1-1 1-2h-1 0c0-1-1-2-1-3s-1-2-2-3c-2 3-4 6-4 10h1l1-1v1c0 3 0 5-1 8 1 0 1-1 1-1l1-1c1 1 1 1 1 3 0 1 1 2 1 3h1 1c1-1 2-1 3-1l1 1c-1 1-1 1-2 1h-1c-2 1-5 2-7 3-3 1-6 2-9 2-1 1-3 1-5 2l-6 2 9-11v-1h0v-2h1l2-2-1-1h0l1-1c0-1 1-2 1-3h1c0-2 0-5-1-6l-2-1c0-1 0-1-1-2 1-2 4-4 6-5 1-1 1-2 2-3v-1l-1-1z" class="E"></path><path d="M392 354l2-2 1 5-2 2-1-5z" class="N"></path><path d="M392 354v-1c0-3-1-5 1-8l1 7-2 2z" class="H"></path><path d="M368 268l1-1h4c-1 2 0 3 1 6h0c-2-2-3-3-5-4l-1-1z" class="b"></path><path d="M396 392c2 5 1 14 1 20v4 5 3-3l-1-1c0 1-1 2 0 2 0 2 0 3-1 4v1h-1l1-1c1-1 0-5 0-6v-14-7l1 3v-3h0v-2c1-2 0-4 0-5z" class="L"></path><path d="M364 298l2 1c0-1 1-1 1-2 1-1 1-2 2-3 1 1 0 2 1 4h1v-2c0-1 1-1 1-1l1-1h0c1 2 1 3 2 5h0 3c-2 1-5 2-7 3-3 1-6 2-9 2v-1c1-2 1-4 2-5z" class="C"></path><path d="M363 293c2-1 3-1 4-3 0 2-1 3-1 5h-1l-1 3c-1 1-1 3-2 5v1c-1 1-3 1-5 2l-6 2 9-11 3-4z" class="e"></path><path d="M357 306l-1-1c2-1 5-5 6-7l1-1c1-2 1-2 2-2l-1 3c-1 1-1 3-2 5v1c-1 1-3 1-5 2z" class="G"></path><path d="M362 280c1-2 1-3 2-4 2-2 4-3 6-4h1c0 1 1 2 0 2-2 2-2 4-3 6s0 4-1 7h0c-1 1-1 2-2 2-1 1-1 3-2 4l-3 4v-1h0v-2h1l2-2-1-1h0l1-1c0-1 1-2 1-3h1c0-2 0-5-1-6l-2-1z" class="g"></path><defs><linearGradient id="W" x1="378.973" y1="363.423" x2="400.53" y2="358.701" xlink:href="#B"><stop offset="0" stop-color="#d6b19e"></stop><stop offset="1" stop-color="#e8dbd2"></stop></linearGradient></defs><path fill="url(#W)" d="M378 274l1 1 1 1v-1l2-1v1c0 1 1 2 2 3v2c0 3 1 6 1 9l2 4h1 0c2 0 1-1 2-2v1h0c1 1 1 2 1 2v1c1 1 1 2 1 2 1-1 0-2 1-3l1 10c2 5 2 10 3 16l4 18v2h1v-2 1c0 3 2 3 3 6 1 1 1 3 1 4v2 1c0 3 1 8-1 11 1 1 2 2 2 3-1 1-1 2-1 4 2 2 2 6 2 8l1 7v6h-1s-1-1-1 0c-1 0-2 0-2-1l-2 1v6c-1 18-1 37-5 55-1-1-1-2 0-3h-1v2h-1l2-19c-1-2-1-6-1-8v-3-5-4c0-6 1-15-1-20v-9c-1-4-1-7-1-11-1-1-1-3-1-4l-1-9 2-2-1-5-1-7-4-24c-2-13-5-26-10-39v-2l1-1-1-2-1 1-1-1c1-2 1-2 1-3z"></path><path d="M395 357l2 19-2-4c-1-1-1-3-1-4l-1-9 2-2z" class="U"></path><path d="M395 372l2 4c0 4 1 9 2 13v29c0 5 0 10-1 14-1-2-1-6-1-8v-3-5-4c0-6 1-15-1-20v-9c-1-4-1-7-1-11z" class="I"></path><defs><linearGradient id="X" x1="402.165" y1="330.076" x2="390.544" y2="333.372" xlink:href="#B"><stop offset="0" stop-color="#957f7b"></stop><stop offset="1" stop-color="#b49994"></stop></linearGradient></defs><path fill="url(#X)" d="M390 291v1h0c1 1 1 2 1 2v1c1 1 1 2 1 2 1-1 0-2 1-3l1 10c2 5 2 10 3 16l4 18v2h1v-2 1c0 3 2 3 3 6 1 1 1 3 1 4v2 1c0 3 1 8-1 11 1 1 2 2 2 3-1 1-1 2-1 4 2 2 2 6 2 8l1 7v6h-1s-1-1-1 0c-1 0-2 0-2-1l-2 1v6l-1-11v-6l-1-9-3-18-5-25c-1-12-3-23-6-35h1 0c2 0 1-1 2-2z"></path><path d="M401 338v2c1 3 2 8 2 11h-2v-6l-1-4v-1c0-1 1-1 1-2z" class="a"></path><path d="M401 340h1v-2 1c0 3 2 3 3 6 1 1 1 3 1 4v2 1c0 3 1 8-1 11 1 1 2 2 2 3-1 1-1 2-1 4 2 2 2 6 2 8l1 7v6h-1s-1-1-1 0c-1 0-2 0-2-1l-2 1v6l-1-11v-6l-1-9v-1l1 2v-2c0-2-1-6 0-8l1-1c0-1-1-2-1-2 0-3-2-7-1-9v1h2c0-3-1-8-2-11z" class="q"></path><path d="M406 351v1c0 3 1 8-1 11 1 1 2 2 2 3-1 1-1 2-1 4v-1c0-1-1-2 0-4h0l-1-1c0 2-1 4 0 6v5 1h0l-2 7c0 1 0 2-1 3v-6l1-11c1-2 0-4 1-5 0-1 1-2 1-3 0-3-1-8 1-10z" class="Z"></path><path d="M402 386c1-1 1-2 1-3l2-7h0v-1-5c-1-2 0-4 0-6l1 1h0c-1 2 0 3 0 4v1c2 2 2 6 2 8l1 7v6h-1s-1-1-1 0c-1 0-2 0-2-1l-2 1v6l-1-11z" class="G"></path><path d="M350 267c4-1 7-1 11-1 3 1 5 1 7 2l1 1v1c-1 1-1 2-2 3-2 1-5 3-6 5 1 1 1 1 1 2l2 1c1 1 1 4 1 6h-1c0 1-1 2-1 3l-1 1h0l1 1-2 2h-1v2h0v1l-9 11c-2 1-2 2-3 3s-2 2-3 4c-3 3-6 7-9 10h1c2-2 5-4 7-6s4-3 6-4l3-3h6c2 0 3 1 4 0l2 1c1 4-1 13-3 17v2l-1 2c-1 2-2 3-3 4l-13 13c-2 0-2 1-4 1-2-2-2-6-4-8l-1-1c-1-3-2-7-3-10l-10 4v-2c1-1 1-1 1-2l-1-1 7-6c-2-12-6-26-14-36-7 3-15 6-22 10 3-4 7-7 12-10h0c3-1 5-3 8-4h2c2-1 4-3 6-4l-1 1h1l1-1h0c3-1 7-4 9-6l8-4 10-5z" class="v"></path><defs><linearGradient id="Y" x1="384.736" y1="479.009" x2="440.313" y2="454.771" xlink:href="#B"><stop offset="0" stop-color="#3e3535"></stop><stop offset="1" stop-color="#977f7b"></stop></linearGradient></defs><path fill="url(#Y)" d="M406 352c1 2 0 3 3 4v1h1c0-2 0-3 1-4l6 9c0 2 1 4 1 5h0c1 4 2 7 2 10l2 3 2 5c0 4 2 7 3 11 0 2 0 4 1 7v4c0 1 1 3 1 3 0 3 1 6 2 9l-1 2 7 21c1 2 5 10 4 12s1 6 2 9c1 2 1 6 3 9l6 12 3 7c3 4 5 8 7 12v1c0 1 1 2 1 3v2s-1 1-2 1c0 1 0 4-1 5l-1 3v1l-6 5h-1l-4 3h0c-1 0-2 1-3 2v-1l-1 1h-1c-1 0-2 1-3 2h-1l3-3c-2 1-4 1-6 1h-1l-3 4h-1c-1 2-3 3-5 5-3 1-5 2-8 2h4v2h-2c-1 0-1 0-2 1l-1 1c-1-1-2-1-3-2-7 1-13 5-20 7h-1v-1l-1-1c-1 0-1 0-2 2v1h-1-1c0-1 0-2-1-2 0-1 10-7 11-9l-1-1c-2 2-4 3-6 5h-2l-1 1c-1 1-2 2-4 2l4-3c2-1 2-2 3-5l-3-12-1-1v-1c1-3 0-9 0-12l-1 2v-2l-1 5h0-1v-3-17c1-1 1-3 1-5h1l1-3h1c1-3 2-7 3-10 0 0 1-2 1-3l3-10c2-4 3-9 3-14 4-18 4-37 5-55v-6l2-1c0 1 1 1 2 1 0-1 1 0 1 0h1v-6l-1-7c0-2 0-6-2-8 0-2 0-3 1-4 0-1-1-2-2-3 2-3 1-8 1-11z"></path><path d="M432 463s-1-1-2-1v-1h4v1l-2 1z" class="Z"></path><path d="M419 449c0 2 0 3 1 4v1l-1-1v1h0l2 1h0-2c-1-1-1-1-1-2-1-2-2-1-2-3 1 0 1 0 2-1h1zm14 14l3 3v2 2l-2-2h-2c0-2-2-3-2-4l3-1z" class="G"></path><path d="M419 449l1-1 1-2h0c0 1 0 2 1 4s3 4 5 6h-2c-1 0-1-1-2-2 0-1 0-1-1-1h-2c-1-1-1-2-1-4h0z" class="Q"></path><path d="M447 521l1-1-1 2h1c0 1-1 2-1 2-2 1-4 2-5 4-2 1-4 1-6 1h-1l1-1h0 1l1-1c2 0 3-2 4-3h2c2 0 2-2 3-3z" class="G"></path><path d="M409 437l1 1 1 1v-2-1h0c1 3 1 7 0 10v1c0 1 0 1-1 2l-1 1h0l-1 4h0l1-17z" class="U"></path><path d="M451 496c-3-1-5-2-8-4-1-1-2-2-2-4v-1c3 0 6 3 8 5l2 2v2z" class="D"></path><path d="M434 527c-1-1-1-1-2-1h0c-1-1-1-2-2-3 0-1 0-2-1-3l-1-1c1-1 1-1 1-2 0-2 1-4 1-5v-1-1h0l1-2c0-1 1-1 2-2-1 3-4 10-2 13 0 1 1 3 2 4 1 0 2 1 2 2l1 1v2h0c-1-1-1-1-2-1z" class="Q"></path><path d="M451 494l1 1h1c2 0 3 2 4 4 2 2 5 6 6 9-1-1-1 0-1-1l-3-3v2c1 3 1 6 1 9v-1-5h1v1c0 1 0 4-1 5l-1 3c0-2-1-4-1-7 0-1 1-3 0-4v-1l-2-4c-1-3-2-4-5-6v-2z" class="G"></path><path d="M408 454h0l1-4h0l1-1v9 3c1 1 3 2 3 3s-1 3-1 4 0 1-1 1c-1 1-1 2-2 3 1-1 1-2 1-4-2 0-2 0-3-2h-1c0-2 1-10 2-12z" class="I"></path><path d="M420 419l1-1c1 2 1 3 2 5v1c0 1 1 3 0 4l-1 1c-2-1-3-1-5-1-3 2-3 5-4 9l1 4c0 2 0 3 1 5h1v-1h1l1 1c-2 1-2 1-3 1-2-3-1-3-2-6 0-3-1-6 0-9l1-1c0-1 0-2 1-3s1-3 2-4l3-5h0z" class="R"></path><path d="M420 419l1-1c1 2 1 3 2 5v1h0c-1 1-1 1-2 1v-2l1-2-2-2h0z" class="U"></path><path d="M407 539c2 0 3-1 5-3 1-1 3-2 5-3 0 1-9 8-9 8-2 1-1 0-2 0-2 1-3 2-5 3 2 0 4-1 6-1 2-1 4-2 6-2 1 0 2 0 2-1h3 4v2h-2c-1 0-1 0-2 1l-1 1c-1-1-2-1-3-2-7 1-13 5-20 7l-1-1c1-1 3-2 5-3l9-6z" class="F"></path><path d="M414 542l4-1c1 0 2 0 4-1v2h-2c-1 0-1 0-2 1l-1 1c-1-1-2-1-3-2z" class="B"></path><path d="M433 506l4-6 1-2h0l1 1h1c-1 1-1 2-1 3v1c-2 1-3 3-4 6 0 1-1 5-2 6 0 2-1 3 0 5l1 1v1c1 1 3 1 3 2 1 1 0 2 1 3l-1 1h-1v-2l-1-1c0-1-1-2-2-2-1-1-2-3-2-4-2-3 1-10 2-13z" class="d"></path><path d="M400 538h2c1-1 2-1 3-2h0c1-1 2-1 2-1l2-1h1c1 0 1-1 2-2h1v1h-1c-2 1-4 4-5 6l-9 6c-2 1-4 2-5 3l1 1h-1v-1l-1-1c-1 0-1 0-2 2v1h-1-1c0-1 0-2-1-2 0-1 10-7 11-9l2-1z" class="E"></path><path d="M453 511s1 1 0 2l-2 2c0 2-1 4-2 5h-1l-1 1c-1 1-1 3-3 3h-2c-1 1-2 3-4 3-1-1 0-2-1-3 0-1-2-1-3-2v-1l-1-1c-1-2 0-3 0-5 1 1 1 3 2 4l2 1c7 0 12-5 16-9z" class="u"></path><path d="M438 527c-1-1 0-2-1-3 0-1-2-1-3-2v-1l-1-1c-1-2 0-3 0-5 1 1 1 3 2 4v1l3 3v1h4c1-1 3-3 5-3-1 1-1 3-3 3h-2c-1 1-2 3-4 3z" class="W"></path><path d="M434 527c1 0 1 0 2 1l-1 1-3 4h-1c-1 2-3 3-5 5-3 1-5 2-8 2h-3l-1-1c6-5 12-8 18-11l2-1z" class="l"></path><path d="M434 527c1 0 1 0 2 1l-1 1-3 4h-1l1-5 2-1z" class="f"></path><path d="M425 440l1-1c2 1 3 1 4 2s2 4 3 5l1-1 1 2c0 2 0 5-1 6h0c-1 2-2 3-3 4-2 0-2 0-4-1-2-2-4-4-5-6 0-5 0-7 3-10z" class="l"></path><path d="M433 446l1-1 1 2c0 2 0 5-1 6h0c0-2-1-5-1-7z" class="F"></path><path d="M413 437c1-4 1-7 4-9 2 0 3 0 5 1 1 2 3 3 4 5l-2 2c1 1 1 1 1 3v1c-3 3-3 5-3 10-1-2-1-3-1-4h0l-1 2-1 1-1-3-1-1h-1v1h-1c-1-2-1-3-1-5l-1-4z" class="T"></path><path d="M413 437l2 2c1-1 1-1 2-1 1 1 1 3 2 5v-1-1-4l1-1c0 1 1 1 1 2h1c-1 3-2 4-2 7h-1c-1-1-1-2-2-4v-1c-1 1-1 1-1 2-2 0-1 0-2-1l-1-4z" class="H"></path><path d="M422 438v-1h1v1l2 1v1c-3 3-3 5-3 10-1-2-1-3-1-4h0l-1 2-1 1-1-3-1-1h-1v1h-1c-1-2-1-3-1-5 1 1 0 1 2 1 0-1 0-1 1-2v1c1 2 1 3 2 4h1c0-3 1-4 2-7z" class="S"></path><path d="M428 479v1l2 3c2 3 2 7 1 11-1 2-2 5-4 8-4 9-10 19-17 27-3 3-7 6-10 9l-2 1-1-1 10-9 1-1c1-3 4-7 6-10 1-1 3-2 3-4l2-3c-1-1-1-2-1-3l1-2c1-1 1-2 2-3l1-2 2-1 2-5c2-4 4-11 2-16z" class="X"></path><path d="M421 503l1-2 2-1c-1 4-3 8-5 11-1-1-1-2-1-3l1-2c1-1 1-2 2-3z" class="Q"></path><path d="M439 503c4-2 7-4 11-3 2 1 3 2 4 3 1 3 1 4 0 6l-1 2c-4 4-9 9-16 9l-2-1c-1-1-1-3-2-4 1-1 2-5 2-6 1-3 2-5 4-6z" class="g"></path><defs><linearGradient id="Z" x1="448.485" y1="461.068" x2="434.689" y2="475.41" xlink:href="#B"><stop offset="0" stop-color="#524747"></stop><stop offset="1" stop-color="#776461"></stop></linearGradient></defs><path fill="url(#Z)" d="M434 441h1c1 1 1 2 1 3l2 4c0-1 1 0 0-1 0-1 0 0-1-1v-1-2l-1-1h1c1 2 5 10 4 12s1 6 2 9c1 2 1 6 3 9l6 12 3 7c-2-2-4-4-6-7v1 1h-1l-1-1-2-1c-2 0-2-2-2-3h-1l-1 2c0-1 0 0-1-1 0-1-1-3-2-3h-1l-2-6c0-1-1-1-2-1 1 0 2 0 4-1-1-1-1 0-1-1v-2-2l-3-3h-1l2-1v-1-2c0-1 1-2 1-3l-1-3c1-1 1-4 1-6l1-1-2-5z"></path><path d="M449 485c-1-1-2-1-2-2-1-2-1-2 0-4 1 3 2 4 5 5l3 7c-2-2-4-4-6-7v1zm-13-39l2 11c0 3 1 6 1 9v1 1c0 2 1 4 2 6h-1 0-1s-2-1-2-2 0-3-1-4v-2l-3-3h-1l2-1v-1-2c0-1 1-2 1-3l-1-3c1-1 1-4 1-6l1-1z" class="R"></path><path d="M434 462v1l1-2h1v3c1 1 0 2 0 2l-3-3h-1l2-1z" class="d"></path><path d="M406 352c1 2 0 3 3 4v1h1c0-2 0-3 1-4l6 9c0 2 1 4 1 5h0c1 4 2 7 2 10l2 3 2 5c0 4 2 7 3 11 0 2 0 4 1 7v4c0 1 1 3 1 3 0 3 1 6 2 9l-1 2 7 21h-1l1 1v2 1c1 1 1 0 1 1 1 1 0 0 0 1l-2-4c0-1 0-2-1-3h-1l2 5-1 1-1-2-1 1c-1-1-2-4-3-5s-2-1-4-2l-1 1v-1c0-2 0-2-1-3l2-2c-1-2-3-3-4-5l1-1c1-1 0-3 0-4v-1c-1-2-1-3-2-5l-1 1v-3h0v-1l1-1v-2l-2 1c-1 0-2 0-3-1s-3-3-3-4c-1-3-1-10-1-13-1 0-1-1-2-2v1c0-2 0-5-1-8v-1l-1-7c0-2 0-6-2-8 0-2 0-3 1-4 0-1-1-2-2-3 2-3 1-8 1-11z" class="F"></path><path d="M411 378l2-3c0 1 1 2 1 3s0 3-1 4v1l-1 1c0-1-1-1-1-2v-4z" class="Q"></path><path d="M408 378c1-2 0-5 2-7l1 7v4c0 1 1 1 1 2v11c-1 0-1-1-2-2v1c0-2 0-5-1-8v-1l-1-7z" class="q"></path><path d="M406 352c1 2 0 3 3 4v1l1 14c-2 2-1 5-2 7 0-2 0-6-2-8 0-2 0-3 1-4 0-1-1-2-2-3 2-3 1-8 1-11z" class="W"></path><path d="M410 357c0-2 0-3 1-4l6 9c0 2 1 4 1 5v3h-1v3c1 2 0 3 1 4 2 3 2 6 2 9v1 3l-1 1c-1-3-3-6-4-8l-1-2 1-2c0-1 0-1 1-3-1-3-1-8-4-12-1-2-1-5-2-7z" class="L"></path><path d="M415 383l-1-2 1-2c0-1 0-1 1-3l4 11v3l-1 1c-1-3-3-6-4-8z" class="R"></path><path d="M418 367h0c1 4 2 7 2 10l2 3 2 5c0 4 2 7 3 11 0 2 0 4 1 7v4l-2-4v4 1l-1 1v-2l-1 7h0c-1-3 0-6 0-10-1-5-2-9-4-14v-3-1c0-3 0-6-2-9-1-1 0-2-1-4v-3h1v-3z" class="S"></path><path d="M420 377l2 3 2 5c0 4 2 7 3 11 0 2 0 4 1 7v4l-2-4-3-15c-1-3-2-7-3-11z" class="t"></path><path d="M413 383h2c1 2 3 5 4 8l1-1c2 5 3 9 4 14-1 3-1 5-1 7s-1 5-2 7l-1 1v-3h0v-1l1-1v-2l-2 1c-1 0-2 0-3-1s-3-3-3-4c-1-3-1-10-1-13v-11l1-1z" class="v"></path><path d="M419 391l1-1c2 5 3 9 4 14-1 3-1 5-1 7s-1 5-2 7l-1 1v-3h0v-1l1-1v-2-2c2-7 0-13-2-19z" class="t"></path><path d="M426 403l2 4c0 1 1 3 1 3 0 3 1 6 2 9l-1 2 7 21h-1l1 1v2 1c1 1 1 0 1 1 1 1 0 0 0 1l-2-4c0-1 0-2-1-3h-1l2 5-1 1-1-2-1 1c-1-1-2-4-3-5s-2-1-4-2l-1 1v-1c0-2 0-2-1-3l2-2c-1-2-3-3-4-5l1-1c1-1 0-3 0-4v-1c-1-2-1-3-2-5 1-2 2-5 2-7s0-4 1-7c0 4-1 7 0 10h0l1-7v2l1-1v-1-4z" class="C"></path><path d="M427 412l3 9c-1 1-1 2-2 3l-1-1c0-3 0-6-1-9 0-1 0-1 1-2z" class="B"></path><path d="M426 403l2 4c0 1 1 3 1 3 0 3 1 6 2 9l-1 2h0l-3-9c0-1-1-3-1-5v-4zm-2 1c0 4-1 7 0 10 0 2 0 5 1 7h1c0 3 3 7 1 10 3 3 4 7 7 10l2 5-1 1-1-2-1 1c-1-1-2-4-3-5s-2-1-4-2l-1 1v-1c0-2 0-2-1-3l2-2c-1-2-3-3-4-5l1-1c1-1 0-3 0-4v-1c-1-2-1-3-2-5 1-2 2-5 2-7s0-4 1-7z" class="I"></path><path d="M426 421c0 3 3 7 1 10-1-1-2-3-3-4l1-2c0-2 0-3 1-4z" class="S"></path><path d="M425 439c0-2 0-2-1-3l2-2 4 7c-1-1-2-1-4-2l-1 1v-1z" class="D"></path><defs><linearGradient id="a" x1="372.737" y1="454.736" x2="425.967" y2="429.698" xlink:href="#B"><stop offset="0" stop-color="#1e1d1f"></stop><stop offset="1" stop-color="#7f6e6a"></stop></linearGradient></defs><path fill="url(#a)" d="M403 397v-6l2-1c0 1 1 1 2 1 0-1 1 0 1 0h1v15c1 8 2 16 1 24 0 3 0 5-1 7l-1 17c-1 2-2 10-2 12h1c1 2 1 2 3 2 0 2 0 3-1 4v2 1c-2 2-5 5-6 8-2 3-4 6-5 9s-2 8-3 10c-4 3-4 13-6 17 0 2-2 4-2 6v-1c1-3 0-9 0-12l-1 2v-2l-1 5h0-1v-3-17c1-1 1-3 1-5h1l1-3h1c1-3 2-7 3-10 0 0 1-2 1-3l3-10c2-4 3-9 3-14 4-18 4-37 5-55z"></path><path d="M394 495v-3-1c0-1 1-2 1-2 0 1 1 1 1 2-1 1-1 1-1 3l-1 1z" class="g"></path><path d="M406 426c0 3 0 5-1 7 0-2 0-5 1-7z" class="I"></path><path d="M401 474v-3h0v-3l1 1v-1c1-1 1-3 0-4h0c1-2 1-5 1-6 1 1 0 8 0 10h0l1 2h-1c-1 1-1 0-1 1v3h-1z" class="C"></path><path d="M395 489v-2l1 2c2-1 2-4 4-6l1-1c-1-1-1-2-1-3l1-1 2 1c0 1 1 0 0 1v3c-2 3-4 6-5 9l-2-1c0-1-1-1-1-2z" class="E"></path><path d="M402 471l1 1v1l1 1 1-1h0 2l2 2c-2 2-5 5-6 8v-3c1-1 0 0 0-1l-2-1v-4h0 1v-3z" class="c"></path><path d="M402 471l1 1v1l1 1c0 1 0 2-1 3h-1v-3-3z" class="T"></path><path d="M405 466h2c1 2 1 2 3 2 0 2 0 3-1 4v2 1l-2-2h-2 0l-1 1-1-1v-1l-1-1c0-1 0 0 1-1h1l-1-2c1 0 1-1 2-2z" class="L"></path><path d="M403 468c1 0 1-1 2-2v7l-1 1-1-1v-1l-1-1c0-1 0 0 1-1h1l-1-2z" class="B"></path><path d="M396 491l2 1c-1 3-2 8-3 10-4 3-4 13-6 17 0 2-2 4-2 6v-1c1-3 0-9 0-12l3-14c1-2 0-4 1-5 0-1 1-1 2-2 0 1-1 3 0 5 0 1-1 1-1 2v1c1 2 1 2 1 3 1-2 0-5 1-7h0l1-1c0-2 0-2 1-3z" class="l"></path><path d="M388 489c1-3 2-7 3-10 0 3-1 6-1 8l1 1h-1c-1 3 0 7 0 10l-3 14-1 2v-2l-1 5h0-1v-3-17c1-1 1-3 1-5h1l1-3h1z" class="E"></path><path d="M387 489h1c-1 8-3 15-3 23 0 1 0 2-1 2v-17c1-1 1-3 1-5h1l1-3z" class="V"></path><path d="M409 474c3-1 6-3 10-3 2 1 6 5 8 7v1h1c2 5 0 12-2 16l-2 5-2 1-1 2c-1 1-1 2-2 3l-1 2c0 1 0 2 1 3l-2 3c0 2-2 3-3 4-2 3-5 7-6 10l-1 1-10 9c-2 2-4 3-6 5h-2l-1 1c-1 1-2 2-4 2l4-3c2-1 2-2 3-5l-3-12-1-1c0-2 2-4 2-6 2-4 2-14 6-17 1-2 2-7 3-10s3-6 5-9c1-3 4-6 6-8v-1z" class="g"></path><defs><linearGradient id="b" x1="422.416" y1="488.594" x2="415.055" y2="499.705" xlink:href="#B"><stop offset="0" stop-color="#1a1a1c"></stop><stop offset="1" stop-color="#363535"></stop></linearGradient></defs><path fill="url(#b)" d="M427 479h1c2 5 0 12-2 16l-2 5-2 1-1 2h-1-1v-1c-2-1-3-1-5-1 2-3 3-8 3-11 1-2 1-6 2-7h0c1 1 2 10 2 12-1 1-1 1 0 2l-1 1c0 1 0 1 1 2h0l1-1c0-1 0-1 1-2h0l1-2c0-1 0-1 1-2v1h1v-2c1-2 1-2 1-3 1-2 0-7 0-10z"></path><path d="M414 501c2 0 3 0 5 1v1h1 1c-1 1-1 2-2 3l-1 2c0 1 0 2 1 3l-2 3c0 2-2 3-3 4-2 3-5 7-6 10l-1 1-10 9c-2 2-4 3-6 5h-2l-1 1c-1 1-2 2-4 2l4-3c2-1 2-2 3-5 1 0 8-10 10-12 5-6 8-12 12-19v-3l1-3z" class="t"></path><defs><linearGradient id="c" x1="409.237" y1="508.138" x2="412.263" y2="517.862" xlink:href="#B"><stop offset="0" stop-color="#665f5f"></stop><stop offset="1" stop-color="#7f706f"></stop></linearGradient></defs><path fill="url(#c)" d="M414 501c2 0 3 0 5 1v1h1 1c-1 1-1 2-2 3l-1 2-3 4c-2 2-3 4-4 6l-1 2h-1c-1 1-1 3-1 4v1c-1 1-1 3-3 4h-1v-1-1c1-1 1-1 1-2-1 0 0 0-1 1h-3c5-6 8-12 12-19v-3l1-3z"></path><path d="M414 501c2 0 3 0 5 1v1h1 1c-1 1-1 2-2 3l-1 2-3 4 1-7c-1 1-2 1-3 2v-3l1-3z" class="e"></path><path d="M414 501c2 0 3 0 5 1v1h1 1c-1 1-1 2-2 3v-1h0c-1 0-1 0-2-1s-2 0-4 0l1-3z" class="L"></path><defs><linearGradient id="d" x1="513.311" y1="547.995" x2="768.699" y2="437.884" xlink:href="#B"><stop offset="0" stop-color="#eee4d8"></stop><stop offset="1" stop-color="#fcfefc"></stop></linearGradient></defs><path fill="url(#d)" d="M678 303c1-2 2-4 4-5 8-1 12 5 18 9l1-1c0-5-12-13-15-17l1-1c1-1 2 0 4 1 1 0 2 1 3 2l6 3 3 3c1 2 3 3 5 5 2 4 3 8 5 11 0 3 1 5 3 7-1 1-1 1-1 2 0 2 1 5 2 7 1 5 1 10 1 15v4h-1l2 2h0c1 1 4 2 5 4h-1l-3-1 1 1s1 1 2 1c4 2 7 4 9 8 0 1 1 2 2 3 0 1 2 2 4 2h0l1 1c-1 1-2 1-3 2-1 0-1-1-2 0h-2c-2-1-5-3-8-3-1 0-2 1-3 0-1 0-1 0-2 1-2 0-4-1-6-2-1 2 0 2 1 3-2 0-1 0-2 1v1 1l1 1v2h-1l-1 2v1 1h0c-1 1-1 1-1 2 0 0-1 2-2 3l-1 1c0 1-1 2-1 3l-2 1c-5 8-9 16-13 24 0 1 0 2 1 3h0v2h2 1c-3 2-5 4-8 6h-1c0 2 1 3 1 4 2 3 4 4 6 7l3 3c3 5 6 11 9 17l3 9c3 2 3 8 5 11v1l1 8v5c1 1 2 1 2 2v4h-1c1 3 2 5 3 7v2c2 1 5 5 7 5l2-2c4 0 8 2 12 4l2 1 1 1c1 0 2-1 3 0h0l-1 1c-2 1-3 2-4 2-1 1-2 1-2 2l-2-1c-2 1-2 2-3 4l-1 3h-1l-2-2c-2-1-3-1-6-1-3 1-8 0-11 0-2 2-3 6-4 8-2 7-6 15-7 23 0 2 0 5 1 7v1c2 7 8 13 10 20 1 2 2 2 2 4 0 1 1 3 3 3l1 1 1 1c-2 3-4 5-5 8l2 3h-1c-5-1-8-6-12-10 0-1-1-1-2-1-1-1-3-1-5-1-2-1-4-1-6-1-3-2-5-6-6-9l-8 9h0c-6 7-12 13-19 18-4 3-7 6-11 8l4 17v5 1c-1 3-5 7-7 9h0c-1 1-3 2-3 4l1 2c1 1 1 3 2 4 0 3 4 10 7 11l2 1h-5l5 3h-1c-6 2-16 7-18 12-1 1 0 1-1 1l-3-3c-3-2-6-7-10-8v-4h0l2-2c-1-1 0-3 0-4 1-3 2-6 3-10 1-6-2-11-6-16l-18 9c-6 3-12 5-17 9-1 0-2 0-3-1l1-2-3 1c0 3-1 5-1 8-1 2 0 3 0 6 0 1-1 3-1 5l-8 7-1-1 14-50v-1l5-16 1-6c1-2 1-5 2-7 2-2 6-5 8-6l1-1h-4c1-1 2-2 2-3v-6h0l-2-2 2-6v-1l1-2c2 0 5 1 7 0h2c1-1 3-1 4-2s5-3 7-4l2-1c1-1 1-2 3-2l5-5 3-3 3-5c3-4 6-8 8-12l1-4 2-5 1 1h2c2-4 4-8 6-13l-1-2 2-2-1-3c0-1-1-3 0-4v-1l-1-1h-1c-1-2-3-4-5-7v-1c-2 0-3-1-4-2 0-1-1-2-1-3-1-1 0-4-1-6v-3h1 1c0-1-1-4-1-5 0-3 0-5-1-8v-1l2-1v1-2l2-1c-1-2-3-4-4-6l2-2c-2-2-4-5-5-7l-1 1h-3c0-1 0-2-1-2h-1l-1-1 2-6c0-1 0-2 1-2v-2c0-2 1-3 2-4 0-4 1-9 2-13l1-3 6-20 1-3 20-62c4-9 6-18 11-26l2-3z"></path><path d="M579 648c1 1 2 2 2 3v1l-3 1 1-5z" class="J"></path><path d="M715 346l3 2h-1l2 2c-2 0-2 0-3 1v2c-1-2-1-5-1-7zM591 632h1 2c1 1 1 1 2 1l-1 1c-1 1-1 1-2 1s-1 0-2-2v-1z" class="i"></path><path d="M579 648c0-2 0-2 2-4l1 1v5l1 1h-2c0-1-1-2-2-3z" class="r"></path><path d="M719 350h0c1 1 4 2 5 4h-1l-3-1h-4v-2c1-1 1-1 3-1z" class="P"></path><path d="M689 580c1 0 2-1 3-1l1 1v3l3 2-4-1c-2-1-2-2-3-4zm11-18c2 3 3 6 5 8h-1 0c0 1 0 3-1 4-2-1-1-4-2-6v-1-1c-1-2-1-2-1-4zm-13-133l-1-1v1h-1c0-1-1-1-1-2-1-1-1-2-1-3 0-2 1-3 3-4v5c0 2 1 3 1 4z" class="h"></path><path d="M684 570c1-3 3-8 6-11h1v4c-1 0-1 0-2 1-1 2-2 5-5 6z" class="K"></path><path d="M704 390h0c0-1 1-2 1-3 1-3 2-5 4-8 0 0 0-1 1-2 0-1 0-2 2-3v1 1l-1 2v1 1h0c-1 1-1 1-1 2 0 0-1 2-2 3l-1 1c0 1-1 2-1 3l-2 1z" class="h"></path><path d="M592 619c1 0 1 1 2 1-2 4-6 7-10 8h-3v-1c4-2 8-5 11-8z" class="s"></path><path d="M686 420l3-5h0v2l1 1 2-1h0v2h2 1c-3 2-5 4-8 6h-1v-5z" class="K"></path><path d="M683 385l7-16c0 2 0 3 1 5-2 4-4 8-6 13-1 2-3 6-5 7 0-1 1-2 1-4 1-1 2-3 2-5z" class="J"></path><path d="M709 519v-2l6 3h1c4 1 9-1 12 2h2v1h1c1-1 2-3 2-4l1-1c1 0 1-1 2-1v1c-2 1-2 2-3 4l-1 3h-1l-2-2c-2-1-3-1-6-1-3 1-8 0-11 0l-1-1-2-2z" class="p"></path><path d="M619 669c7 3 11 8 14 14-1 1 0 1-1 1l-3-3c-3-2-6-7-10-8v-4z" class="s"></path><path d="M587 605c2 5 4 9 9 12h2v1c-2 0-3 1-4 2-1 0-1-1-2-1-3-2-5-5-6-8l1-6z" class="M"></path><path d="M698 585c6 2 9 5 13 10 1 1 2 3 3 3l2 3h-1c-5-1-8-6-12-10 0-1-2-3-3-4-1 0-2-1-2-2zm-15-200c0 2-1 4-2 5 0 2-1 3-1 4-1 2-2 3-3 5-1 3-7 11-10 13l-3-2c1 0 2-1 2-2 8-5 13-15 17-23zm2 134c2-2 4-7 4-10 0-1 1-1 1-2l1 1c-1 8-3 17-7 24 0-2 1-3-1-5l2-8z" class="s"></path><path d="M586 611c1 3 3 6 6 8-3 3-7 6-11 8l5-16z" class="K"></path><path d="M644 397l1-3 14 8c2 2 5 5 7 6 0 1-1 2-2 2-6-4-13-9-20-13z" class="p"></path><path d="M708 465c3 2 3 8 5 11v1l1 8v5c1 1 2 1 2 2v4h-1c1 3 2 5 3 7v2l-3-3-3-3v-5c0-5 1-10 0-15l-4-14z" class="K"></path><path d="M715 502h0l-1-1c-1-2-1-5 0-7h1v2c1 3 2 5 3 7v2l-3-3z" class="h"></path><defs><linearGradient id="e" x1="682.39" y1="543.165" x2="675.11" y2="535.335" xlink:href="#B"><stop offset="0" stop-color="#bc9b7f"></stop><stop offset="1" stop-color="#c4b08d"></stop></linearGradient></defs><path fill="url(#e)" d="M683 527c2 2 1 3 1 5-3 11-9 21-16 30v-1c0-1 2-3 3-5 0 0 0-1 1-2v-3l11-24z"></path><path d="M642 643h1l5-6v2l-1 1-1 1-4 5h1c-1 1-3 2-3 4l1 2c1 1 1 3 2 4 0 3 4 10 7 11l2 1h-5c-1 0-1 0-2-1-5-5-7-12-8-20 2-1 3-3 5-4z" class="s"></path><path d="M638 417l17 10c-3 4-7 9-11 13-1 0-2 2-3 2 0 1-2 1-2 1l-1-1 12-13c-4-3-8-7-13-9l1-3z" class="b"></path><path d="M709 519l2 2 1 1c-2 2-3 6-4 8-2 7-6 15-7 23 0 2 0 5 1 7v1c2 7 8 13 10 20h0l-7-11c-2-2-3-5-5-8v-4c-1-2-1-5-1-7 0-4 2-7 3-10l5-16c1-2 2-4 2-6z" class="K"></path><path d="M646 457h1c2-1 3-3 4-4 5-4 10-10 15-13 4 3 6 6 9 10 2 2 2 4 2 7v1c-3-5-7-10-11-14l-1 1c-2 3-5 6-9 9-2 3-4 5-7 6l-3-3zm40 8c2 2 2 5 2 8 1 4 2 9 3 13 1 7 1 15 0 22h0l-1-1c0 1-1 1-1 2 0 3-2 8-4 10 0-6 4-12 3-18v-2h0l-2-20c0-2 0-6-1-8-1-1 0-4 1-6z" class="b"></path><path d="M637 420c5 2 9 6 13 9l-12 13-3-9c0-4 1-9 2-13z" class="o"></path><path d="M694 291l6 3 3 3c1 2 3 3 5 5 2 4 3 8 5 11 0 3 1 5 3 7-1 1-1 1-1 2 0 2 1 5 2 7 1 5 1 10 1 15v4l-3-2c0-7-1-13-2-19-1-3-1-6-2-8-1-3-3-5-3-7-2-5-3-9-5-13-3-3-7-5-9-8h0z" class="r"></path><path d="M684 570c3-1 4-4 5-6 1-1 1-1 2-1l-6 12 1 1c1 1 2 2 3 4s1 3 3 4l4 1h2c0 1 1 2 2 2 1 1 3 3 3 4 0-1-1-1-2-1-1-1-3-1-5-1-2-1-4-1-6-1-3-2-5-6-6-9l-8 9h0-1-1c-3 1-5 4-7 6h0l-1-1c2-2 4-4 6-7h0c4-5 9-10 12-16z" class="P"></path><path d="M676 306l2-3c11 11 15 22 17 37 2 11-1 23-4 34-1-2-1-3-1-5v-6c1-3 2-6 2-9v-8c0-12-3-24-10-33-2-3-4-5-6-7z" class="s"></path><path d="M641 615l-1-2c1-2 3-3 5-4 10-7 18-14 27-23-2 3-4 5-6 7l1 1h0c2-2 4-5 7-6h1 1c-6 7-12 13-19 18-4 3-7 6-11 8l4 17v5 1c-1 3-5 7-7 9h0-1l4-5 1-1 1-1v-2l-5 6h-1c1-2 4-4 5-6 1-4-3-13-4-16l-2-6z" class="M"></path><path d="M641 615l1-2c0 1 1 1 1 2 1 2 1 4 0 6l-2-6z" class="p"></path><path d="M583 651l23-12 11-7c2-1 4-3 6-3 1 0 1 0 1 1 0 2-2 3-2 5 0 0 0 1 1 2 4 4 5 11 5 17-1 5-4 10-7 13-1-1 0-3 0-4 1-3 2-6 3-10 1-6-2-11-6-16l-18 9c-6 3-12 5-17 9-1 0-2 0-3-1l1-2v-1h2z" class="M"></path><path d="M633 437c0-2 1-3 2-4l3 9 1 1c2 4 4 9 7 13v1l3 3c5 5 14 12 21 12 2-1 4-1 6-3 1-2 2-3 2-5 1-4 0-8 1-12 1 1 2 3 3 4h0c2 5 1 10 0 14l-5 10h0 0-1c-1 0-1-1-2-2 1-1 1-2 1-3l-3 1v1l-1 1c-2-2-3-3-6-3-10-4-17-11-23-19-2-2-4-5-5-7l-1 1h-3c0-1 0-2-1-2h-1l-1-1 2-6c0-1 0-2 1-2v-2z" class="X"></path><path d="M631 448l-1-1 2-6c0-1 0-2 1-2v-2c1 4 2 8 4 12l-1 1h-3c0-1 0-2-1-2h-1z" class="f"></path><path d="M633 437c0-2 1-3 2-4l3 9 1 1c2 4 4 9 7 13l-2 2c-4-6-8-13-9-19l-2-2z" class="C"></path><defs><linearGradient id="f" x1="668.011" y1="473.175" x2="674.054" y2="455.172" xlink:href="#B"><stop offset="0" stop-color="#252424"></stop><stop offset="1" stop-color="#453330"></stop></linearGradient></defs><path fill="url(#f)" d="M678 464c1-4 0-8 1-12 1 1 2 3 3 4h0l-2-1c0 1 1 2 1 4 0 3 0 5-1 8l-1 1c-1 2-3 5-5 6-3 1-5 1-8 0v-1c-9-3-16-8-22-15l2-2v1l3 3c5 5 14 12 21 12 2-1 4-1 6-3 1-2 2-3 2-5z"></path><path d="M682 456c2 3 3 6 4 9-1 2-2 5-1 6 1 2 1 6 1 8l2 20h0v2c-3 3-5 3-9 4-1-1-3-1-5-1-1 0-2 0-3 1l-4 2h-1v-1l1-1-2-1 6-3 3-2c1 0 3 0 4-1h1v-1l-5-13c-1-2-1-4 0-6 1 1 1 2 2 2h1 0 0l5-10c1-4 2-9 0-14h0z" class="c"></path><path d="M674 499c1 0 3 0 4-1 2 1 3 1 4 2-6 1-10 2-15 5l-2-1 6-3 3-2z" class="K"></path><path d="M674 478c1 1 1 2 2 2h1 0c-1 6 2 12 5 17 1 2 2 3 4 4h-1l-3-1c-1-1-2-1-4-2h1v-1l-5-13c-1-2-1-4 0-6z" class="b"></path><path d="M685 471c1 2 1 6 1 8l2 20h0c-2-2-4-4-6-7-1-2-2-5-2-7 0-1 1-3 1-4l4-10z" class="o"></path><path d="M649 460c3-1 5-3 7-6 4-3 7-6 9-9l1-1c4 4 8 9 11 14h0l1 2v4c0 2-1 3-2 5-2 2-4 2-6 3-7 0-16-7-21-12z" class="x"></path><path d="M642 456c6 8 13 15 23 19 3 0 4 1 6 3l1-1v-1l3-1c0 1 0 2-1 3-1 2-1 4 0 6l5 13v1h-1c-1 1-3 1-4 1l-3 2-6 3h-1c-2 0-3-3-5-4-1-2-3-3-5-5v2l3 3h0 0c-1 0-2-1-2-1l-1 1v1l-2 2h0-1c-1-2-3-4-5-7v-1c-2 0-3-1-4-2 0-1-1-2-1-3-1-1 0-4-1-6v-3h1 1c0-1-1-4-1-5 0-3 0-5-1-8v-1l2-1v1-2l2-1c-1-2-3-4-4-6l2-2z" class="R"></path><path d="M667 498l2-1c1-2 0-4 2-6h3c-1 1-1 2-1 3h-1 0c1 2 1 4 2 5l-3 2c-1-1-2-1-2-2v-1l-1 1-1-1z" class="F"></path><path d="M671 478l1-1v-1l3-1c0 1 0 2-1 3-1 2-1 4 0 6l5 13v1h-1c-1 1-3 1-4 1-1-1-1-3-2-5h0 1c0-1 0-2 1-3l-2-2c-1 0-1 1-2 2l-1-1-1-1c0-1 1-2 2-3h1c0 1 0 0 1 1 0-1 1-2 0-4v-1l1-1h0c-1-1-1-1-2-1l1-1-1-1z" class="j"></path><path d="M673 494c0 1 0 1 2 2l1 1 3 1h-1c-1 1-3 1-4 1-1-1-1-3-2-5h0 1z" class="t"></path><path d="M642 456c6 8 13 15 23 19 3 0 4 1 6 3l1 1-1 1c1 0 1 0 2 1h0l-1 1-5-2c-2-1-3-2-5-3-5-4-10-6-15-11v1h0c-2 0-2-1-3-3s-3-4-4-6l2-2z" class="S"></path><path d="M641 490c-1-1 0-4-1-6v-3h1 1c2 5 5 8 10 10 4 2 8 1 13-1v1l-2 1-2 2h1l1 1c-1 1-2 0-3 0 1 1 2 1 3 1 1-1 1-2 1-3 1-1 1-1 2-1l1-1v2c0 1-1 1-1 2 1 0 1 0 0 1l-1 1 1 1h1l1 1 1-1v1c0 1 1 1 2 2l-6 3h-1c-2 0-3-3-5-4-1-2-3-3-5-5v2l3 3h0 0c-1 0-2-1-2-1l-1 1v1l-2 2h0-1c-1-2-3-4-5-7v-1c-2 0-3-1-4-2 0-1-1-2-1-3z" class="t"></path><path d="M641 490c-1-1 0-4-1-6v-3h1c1 3 2 4 3 6l1 1c1 1 3 3 5 4 1 1 3 2 4 3v2l3 3h0 0c-1 0-2-1-2-1l-1 1v1l-2 2h0-1c-1-2-3-4-5-7v-1c-2 0-3-1-4-2 0-1-1-2-1-3z" class="C"></path><path d="M641 490c-1-1 0-4-1-6v-3h1c1 3 2 4 3 6l1 1-1 1c1 1 2 2 2 4h-1c-1-1-2-2-3-4v-1c-1 0-1 1-1 2z" class="H"></path><path d="M650 492c1 1 3 2 4 3v2l3 3h0 0c-1 0-2-1-2-1l-1 1v1l-2 2-4-8 1-1h1v-2z" class="L"></path><path d="M640 468v-1l2-1v1c9 5 20 9 26 17l1 1c0 2-2 3-3 4l-1 1c-5 2-9 3-13 1-5-2-8-5-10-10 0-1-1-4-1-5 0-3 0-5-1-8z" class="g"></path><path d="M652 488c-2-2-4-4-4-7-1-1-1-4 0-6h1c7 2 12 6 17 11h0c1 1 1 0 1 1 1-1 1-2 1-3l1 1c0 2-2 3-3 4l-1 1c-5 2-9 3-13 1 1-1 1-1 0-3z" class="x"></path><path d="M668 484l1 1c0 2-2 3-3 4l-1 1c-5 2-9 3-13 1 1-1 1-1 0-3h1c3 1 5 1 8 1 2-1 4-1 5-3h0c1 1 1 0 1 1 1-1 1-2 1-3z" class="C"></path><path d="M676 306c2 2 4 4 6 7 7 9 10 21 10 33v8c0 3-1 6-2 9v6l-7 16c-4 8-9 18-17 23-2-1-5-4-7-6l-14-8 20-62c4-9 6-18 11-26z" class="x"></path><path d="M666 408c0-5 8-11 10-15 6-10 8-21 14-30v6l-7 16c-4 8-9 18-17 23z" class="o"></path><path d="M654 495c2 2 4 3 5 5 2 1 3 4 5 4h1l2 1-1 1v1h1l4-2c1-1 2-1 3-1 2 0 4 0 5 1 4-1 6-1 9-4 1 6-3 12-3 18l-2 8-11 24v3c-1 1-1 2-1 2-1 2-3 4-3 5v1c-2 4-5 7-7 10-10 12-21 20-34 29-3 2-6 5-10 6-1 1-4-1-5-2-5-3-10-9-13-14h-1 0-4c1-1 2-2 2-3v-6h0l-2-2 2-6v-1l1-2c2 0 5 1 7 0h2c1-1 3-1 4-2s5-3 7-4l2-1c1-1 1-2 3-2l5-5 3-3 3-5c3-4 6-8 8-12l1-4 2-5 1 1h2c2-4 4-8 6-13l-1-2 2-2-1-3c0-1-1-3 0-4v-1l-1-1h0l2-2v-1l1-1s1 1 2 1h0 0l-3-3v-2z" class="M"></path><path d="M629 562l5-4c0 1 1 2 1 3v5l3 12c1 3 2 5 2 8 0 1-1 2-2 2-2-8-5-18-9-26z" class="J"></path><path d="M615 573c3 3 3 15 4 20l1 9-4 2c-9-4-13-15-19-22h-1l-2-2 2-6c7 2 12 1 19-1z" class="h"></path><path d="M629 562c4 8 7 18 9 26l-14 12c-2 0-2 1-3 1l-1 1-1-9c-1-5-1-17-4-20 1-1 2-2 4-3 4-2 7-5 10-8z" class="K"></path><path d="M623 590l1 10c-2 0-2 1-3 1l-1 1-1-9h1c1 0 2-2 3-3z" class="J"></path><path d="M619 570c2 6 3 14 4 20-1 1-2 3-3 3h-1c-1-5-1-17-4-20 1-1 2-2 4-3z" class="M"></path><path d="M646 543c2 4 4 8 5 11 2 4 4 7 5 10h2l2 4-1 2-3 3c-4 5-9 9-14 13l-7-25c0-1-1-2-1-3l12-15z" class="h"></path><path d="M651 554c2 4 4 7 5 10h2l2 4-1 2-3 3-2-9c-1-3-3-6-3-10z" class="b"></path><path d="M658 564l2 4-1 2c-1-2-2-3-3-6h2z" class="P"></path><defs><linearGradient id="g" x1="646.923" y1="545.848" x2="629.077" y2="532.652" xlink:href="#B"><stop offset="0" stop-color="#1b1a1a"></stop><stop offset="1" stop-color="#454344"></stop></linearGradient></defs><path fill="url(#g)" d="M654 495c2 2 4 3 5 5 2 1 3 4 5 4h1l2 1-1 1v1h1l4-2c1-1 2-1 3-1 2 0 4 0 5 1-10 3-18 8-23 18l-8 17-2 3-12 15-5 4c-3 3-6 6-10 8-2 1-3 2-4 3-7 2-12 3-19 1v-1l1-2c2 0 5 1 7 0h2c1-1 3-1 4-2s5-3 7-4l2-1c1-1 1-2 3-2l5-5 3-3 3-5c3-4 6-8 8-12l1-4 2-5 1 1h2c2-4 4-8 6-13l-1-2 2-2-1-3c0-1-1-3 0-4v-1l-1-1h0l2-2v-1l1-1s1 1 2 1h0 0l-3-3v-2z"></path><path d="M654 495c2 2 4 3 5 5 2 1 3 4 5 4h1l2 1-1 1c-14 8-17 23-24 36-9 16-26 32-46 31l1-2c2 0 5 1 7 0h2c1-1 3-1 4-2s5-3 7-4l2-1c1-1 1-2 3-2l5-5 3-3 3-5c3-4 6-8 8-12l1-4 2-5 1 1h2c2-4 4-8 6-13l-1-2 2-2-1-3c0-1-1-3 0-4v-1l-1-1h0l2-2v-1l1-1s1 1 2 1h0 0l-3-3v-2z" class="b"></path><path d="M644 528l1 1h2c-5 13-13 23-24 33h-1l5-5 3-3 3-5c3-4 6-8 8-12l1-4 2-5z" class="L"></path><path d="M654 495c2 2 4 3 5 5 2 1 3 4 5 4-5 4-8 8-11 12l-1-2 2-2-1-3c0-1-1-3 0-4v-1l-1-1h0l2-2v-1l1-1s1 1 2 1h0 0l-3-3v-2z" class="e"></path><path d="M653 509c1-2 2-3 3-5h1v2l1 1h-1l-1 2c-1 1-1 1-1 2l-1 1-1-3z" class="I"></path><path d="M654 495c2 2 4 3 5 5 2 1 3 4 5 4-5 4-8 8-11 12l-1-2 2-2 1-1c0-1 0-1 1-2l1-2h1c0-1 1-2 2-3h0c0-1-1-3-3-4h0l-3-3v-2z" class="j"></path><path d="M688 501c1 6-3 12-3 18l-2 8-11 24c-3 3-5 7-7 11-2 2-4 4-5 6l-2-4h-2c-1-3-3-6-5-10-1-3-3-7-5-11l2-3 8-17c5-10 13-15 23-18 4-1 6-1 9-4z" class="x"></path><path d="M648 540l1 2c4 6 7 15 9 22h-2c-1-3-3-6-5-10-1-3-3-7-5-11l2-3z" class="M"></path><path d="M446 472c-2-3-2-7-3-9-1-3-3-7-2-9 2 4 4 7 6 11s5 7 8 10c4 4 8 8 13 11 6 4 12 7 18 10 2 1 5 3 6 3 2 0 2 0 3 1 2 0 3 1 4 1l6 1-3-3-1-1h5l3 1 2 3c1 0 3 2 5 2 1 0 3 0 4 1 3 1 6 1 9 1 6 2 12 0 18 0l-1 1c4-1 9-1 13-3h2l4-1v1c2-1 4-1 5-2h4c2 0 6-4 8-5 3-3 8-7 11-10 1-3 5-7 8-8l-7 19c-1 1-2 4-2 6l-3 9-6 22-9 28-3 12-5 17c-1 6-4 12-5 18-2 3-5 8-6 12 0 2 1 4 2 6l-4 13c-1 1-2 3-1 5-1 2-2 3-2 5-1 1-2 1-3 3v3c-1 2-1 4-2 6v2h-1v-2c-1 1 0 2-1 4l1 1v2 1h0l-1 1h0 0l-1 1v2c0 1 0 0-1 1h-1c0-2 1-4 1-6l-1 3-1 1h0c1 2 0 3 0 5v1 1 6h0c0 2-1 4-1 5v1l-1-1-2 1c1-2 0-4 0-5l-1-3c-1-3-2-7-4-9h-1v-1c-1 0-1 1-2 1-1-2 0-2-1-3-1 0-2 1-2 2l-6-11s-1-2-2-2l-5-11-1-2-1-3h-2-1c0 2 0 2 1 3v1 1l-1-1v2 1 1h-1c-1-2-1-4-3-5v-1c1 2 4 10 4 11h-1l-3-8c0-1-1-2-1-3v2 2c0 2 1 4 2 6 0 2 0 4 1 6v5c1 2 1 5 1 7l1 4c0 3 3 6 3 9v1-1l-2-2-1-2c-1 0-1 0-2 1h1l-1 1v1 2 2h-1v-1c0-1 0-2-1-3v-1h0c-1 2-1 3-1 5l1 1h0l-1-2v5c-2-2-2-4-3-5h-2c1 1 2 3 3 4l-1 1-2-2-4-4-1-1c-1 0-2-1-3-2s-2-1-3-1l-5-1-8-1c-8 0-15-1-23-3-6-3-12-6-17-10-5-3-11-9-14-13l-5-8v1c-7-8-10-22-10-32-1-3 0-8-2-10-1-6 0-12 0-18v-7c1 0 1-1 1-1-1-1 0-1-1-1v-1-3 1c2 0 2-2 3-3 2-2 4-6 4-10-1 0-1-1-1-2v-2l4-4c2-2 4-5 6-7l2-4-2-3 1-1c1-1 1-1 2-1h2v-2h-4c3 0 5-1 8-2 2-2 4-3 5-5h1l3-4h1c2 0 4 0 6-1l-3 3h1c1-1 2-2 3-2h1l1-1v1c1-1 2-2 3-2h0l4-3h1l6-5v-1l1-3c1-1 1-4 1-5 1 0 2-1 2-1v-2c0-1-1-2-1-3v-1c-2-4-4-8-7-12l-3-7-6-12z" class="g"></path><path d="M439 555c-1-1-1-3-1-5 2 0 2 1 3 0v2c2 1 2 2 2 4l-1-1c-1-1-2 0-3 0z" class="D"></path><path d="M440 538c1-2 3-4 5-4l1 3c-7 5-13 13-18 20l-2-1c1-3 5-8 7-10l7-8z" class="Z"></path><path d="M457 529h0c2-1 3-2 5-2l6-2-2 3h-1c-1-1-3 0-4 1-4 3-7 9-7 14l-1 3 1 4h-2-1 0l-1 1v2c0 1 0 3-1 4l-1 1v-4h-1-2v-2c-1 1-1 1-1 2h-1 0c0-1 0-3 1-3v-2l1-2c1-1 1-1 1-2h1c0 1 0 1 1 2v-1c0-1 0-2 1-2 3-5 5-9 8-13v-2z" class="f"></path><path d="M444 549l1-1s1 0 1-1l1 2h1l1-3h1c2 0 2 1 3 2v-2l1 4h-2-1 0l-1 1v2c0 1 0 3-1 4l-1 1v-4h-1-2v-2c-1 1-1 1-1 2h-1 0c0-1 0-3 1-3v-2z" class="E"></path><path d="M443 554h1c0-1 0-1 1-2v2h2 1v4c0 1 1 1 0 3l2 7v3c1 6 3 12 4 18v1 2-1c-1-1-2-1-2-2 0-2-1-3-1-4l-1-2-1-2c-1-2-2-5-3-7-1-1-2-6-3-7l-1-1c-1-1-1-4-2-5 0 0-1-3-1-4v-2c1 0 2-1 3 0l1 1c0 1 1 2 1 3 0-2 0-3-1-5z" class="Q"></path><path d="M446 569l1-1h0v-1c-1-2 0-4 0-6h1l2 7v3h-1 0v1c-1-1-1-1-1-2 0 3 1 5 2 8h-1l-1-3-1-1c0-2-1-3-1-5z" class="F"></path><path d="M443 554h1c0-1 0-1 1-2v2h2 1v4c0 1 1 1 0 3h-1c0 2-1 4 0 6v1h0l-1 1c-1-3-3-5-4-8h-1-1s-1-3-1-4v-2c1 0 2-1 3 0l1 1c0 1 1 2 1 3 0-2 0-3-1-5z" class="L"></path><path d="M443 554h1c0-1 0-1 1-2v2h2v4c-1 0-1 0-2-1v3l-1-1c0-2 0-3-1-5z" class="B"></path><path d="M439 555c1 0 2-1 3 0l1 1c0 1 1 2 1 3l1 1v1c-1 0-1-1-2-1h-1c0-1-1-2-2-2l-1-1v-2z" class="C"></path><path d="M432 543s1 0 1-1c2-2 4-3 6-5l1 1-7 8c-2 2-6 7-7 10l2 1c-5 8-9 16-12 25-1 1-1 2-2 3v1c-2 0-2 1-3 2 0-2 1-5 2-7l-1-1c1-4 3-8 5-12 0 0 1-3 2-4l6-12h0l4-5c1-1 2-2 3-4z" class="F"></path><path d="M432 543s1 0 1-1c2-2 4-3 6-5l1 1-7 8v-1c0-1 1-2 1-3-1 2-3 4-5 5 1-1 2-2 3-4z" class="R"></path><path d="M442 566l1 1c1 1 2 6 3 7 1 2 2 5 3 7l1 2 1 2c0 1 1 2 1 4 0 1 1 1 2 2v1l5 13c2 6 4 13 6 19 1 3 3 7 3 10-1 1 0 2 0 3v1 3c1 1 0 2 0 3v1l-12-38c-2-2-13-37-14-41z" class="V"></path><path d="M456 607v-1l1 3 4 10h0c1 0 1 1 2 2 0 2 1 3 1 5l1 1v-1-1c-1-2-1-3-1-4l-1-3c-1-1-2-2-2-4v-1-1c-1-1-1-1-1-2-1-1-1-1-1-2h0c0-1-1-1-1-2l1-1c2 6 4 13 6 19 1 3 3 7 3 10-1 1 0 2 0 3v1 3c1 1 0 2 0 3v1l-12-38z" class="X"></path><path d="M462 503l3 6c0 1 1 3 1 4 1 2 1 5 3 7l1 1h1 0c0 1 1 1 1 1 1 0 1-1 2-1 0 0 1 1 2 1 7 2 11 6 17 10 2 3 5 5 7 7 1 2 2 3 3 5s3 3 3 6l-13-13c-3-3-7-7-11-9s-8-3-12-3h-2l-6 2c-2 0-3 1-5 2h0c-4 2-7 5-11 8l-1-3c-2 0-4 2-5 4l-1-1c-2 2-4 3-6 5 0 1-1 1-1 1 1-1 1-2 1-4 0-1 4-5 5-5l7-5c1-1 2-2 3-2h0l4-3h1l6-5v-1l1-3c1-1 1-4 1-5 1 0 2-1 2-1v-2c0-1-1-2-1-3v-1z" class="Q"></path><path d="M462 503l3 6c0 1 1 3 1 4 1 2 1 5 3 7l1 1h1 0c-2 0-2 1-3 2h0l-2-1h-2-1c-2 1-3 3-5 3-4 1-8 5-11 7 1-2 2-3 3-4 4-3 7-4 8-9h1v-1l1-3c1-1 1-4 1-5 1 0 2-1 2-1v-2c0-1-1-2-1-3v-1z" class="F"></path><path d="M404 618h0 1l1-1v-1-1-3-1c1 0 1-1 1-1l1-3c0-2 2-4 3-6l1 1c4 8 6 17 8 25l14 42-1 1-1 1c-5-3-11-9-14-13l-5-8c-4-6-7-14-8-22-1-3-1-7-1-10z" class="f"></path><path d="M414 644c0-1-1-2-1-3-3-11-4-20-2-31 0-2 0-5 1-8 4 8 6 17 8 25l-1 1c0 1 0 1-1 3v2c0 1 0 1-1 2v1c0 1-1 2-1 3l-1 1-1 1 1 2-1 1z" class="Y"></path><path d="M420 627l14 42-1 1c-8-7-16-15-19-26l1-1-1-2 1-1 1-1c0-1 1-2 1-3v-1c1-1 1-1 1-2v-2c1-2 1-2 1-3l1-1z" class="i"></path><path d="M435 529h1c2 0 4 0 6-1l-3 3h1c1-1 2-2 3-2h1l1-1v1l-7 5c-1 0-5 4-5 5 0 2 0 3-1 4-1 2-2 3-3 4l-4 5h0l-6 12c-1 1-2 4-2 4-2 4-4 8-5 12l1 1c-1 2-2 5-2 7 1-1 1-2 3-2v-1c1-1 1-2 2-3l-3 11c0 3-1 6-2 8s-3 4-3 6l-1 3s0 1-1 1v1 3 1 1l-1 1h-1 0c0 3 0 7 1 10 1 8 4 16 8 22v1c-7-8-10-22-10-32-1-3 0-8-2-10-1-6 0-12 0-18v-7c1 0 1-1 1-1-1-1 0-1-1-1v-1-3 1c2 0 2-2 3-3 2-2 4-6 4-10-1 0-1-1-1-2v-2l4-4c2-2 4-5 6-7l2-4-2-3 1-1c1-1 1-1 2-1h2v-2h-4c3 0 5-1 8-2 2-2 4-3 5-5h1l3-4z" class="F"></path><path d="M435 529h1c2 0 4 0 6-1l-3 3h-1c-1 1-2 1-2 2l-1-1-3 1 3-4zm-16 18h3l-1 2c0 1-1 1-1 2s-1 2-2 2l-1-2 2-4h0zm-13 38h1c-1 4-3 8-4 12v3h-1c0-1 0-2 1-2v-1-1c-1-2 0-5 1-8l2-3z" class="Q"></path><path d="M418 540c3 0 5-1 8-2-1 3-4 6-7 9h0l-2-3 1-1c1-1 1-1 2-1h2v-2h-4z" class="N"></path><path d="M433 539c0 2 0 3-1 4-1 2-2 3-3 4l-4 5h0l-1-1c0-1 0-2 1-2 2-4 5-7 8-10z" class="Z"></path><path d="M424 551l1 1-6 12c-1 1-2 4-2 4h-1l-1 1c0-1 1-1 0-3 1-5 6-11 9-15z" class="M"></path><path d="M417 551l1 2c-1 1-1 2-2 4-2 3-6 6-6 10h0l-2-1c-1 0-1-1-1-2v-2l4-4c2-2 4-5 6-7z" class="d"></path><path d="M415 566c1 2 0 2 0 3l1-1h1c-2 4-4 8-5 12l1 1c-1 2-2 5-2 7 1-1 1-2 3-2v-1c1-1 1-2 2-3l-3 11c0 3-1 6-2 8s-3 4-3 6l-1 3s0 1-1 1v1 3 1 1l-1 1h-1 0c0 3 0 7 1 10 1 8 4 16 8 22v1c-7-8-10-22-10-32 0-4-1-8 0-12 2-14 7-28 12-41z" class="P"></path><defs><linearGradient id="h" x1="413.679" y1="593.498" x2="409.675" y2="589.925" xlink:href="#B"><stop offset="0" stop-color="#3e3f3f"></stop><stop offset="1" stop-color="#564b4c"></stop></linearGradient></defs><path fill="url(#h)" d="M412 580l1 1c-1 2-2 5-2 7 1-1 1-2 3-2v-1c1-1 1-2 2-3l-3 11c0 3-1 6-2 8s-3 4-3 6l-1 3s0 1-1 1v1 3 1 1l-1 1h-1 0c1-13 4-26 8-38z"></path><path d="M468 525h2c4 0 8 1 12 3s8 6 11 9c0 1-1 2-1 3 0 2-1 4-2 5v5c-3 3-3 6-3 10v1l3 1c0 6 0 13 2 18 0 4 1 8 2 12 2 12 5 23 9 35l2 5-1 1v7l2 6c0 2 0 2 1 3v1 1l-1-1v2 1 1h-1c-1-2-1-4-3-5v-1c1 2 4 10 4 11h-1l-3-8c0-1-1-2-1-3v2 2c0 2 1 4 2 6 0 2 0 4 1 6v5c1 2 1 5 1 7l1 4c0 3 3 6 3 9v1-1l-2-2-1-2c-1 0-1 0-2 1h1l-1 1v1 2 2h-1v-1c0-1 0-2-1-3v-1h0c-1 2-1 3-1 5l1 1h0l-1-2v5c-2-2-2-4-3-5h-2c1 1 2 3 3 4l-1 1-2-2-4-4-1-1c-1 0-2-1-3-2s-2-1-3-1l-5-1-10-33-2-7v-1c0-1 1-2 0-3v-3-1c0-1-1-2 0-3 0-3-2-7-3-10-2-6-4-13-6-19l-5-13v-2-1c-1-6-3-12-4-18v-3l-2-7c1-2 0-2 0-3l1-1c1-1 1-3 1-4v-2l1-1h0 1 2l-1-4 1-3c0-5 3-11 7-14 1-1 3-2 4-1h1l2-3z" class="g"></path><path d="M476 538c1 2 2 4 3 7-1 1-1 1-1 2l-3-2 1-7z" class="e"></path><path d="M477 581c1 0 1 0 1 1l1 5 2-1h0l3 13-2-2v1h0c-2-6-4-12-5-17z" class="Q"></path><path d="M482 598v-1l2 2 3 8 2 5c0 3 0 6 2 8l-1 2-8-24z" class="W"></path><defs><linearGradient id="i" x1="479.337" y1="566.183" x2="474.902" y2="566.857" xlink:href="#B"><stop offset="0" stop-color="#6a5856"></stop><stop offset="1" stop-color="#86716e"></stop></linearGradient></defs><path fill="url(#i)" d="M475 545l3 2c-2 9-2 17 0 26l1-1v-1c-1-3-1-7 0-10 0 3-1 6 0 9l1 1c0 1-1 3 0 4h-1c0 4 1 7 2 11l-2 1-1-5c0-1 0-1-1-1-1-3-1-6-2-8-1-10-2-18 0-28z"></path><defs><linearGradient id="j" x1="483.75" y1="528.891" x2="473.434" y2="557.536" xlink:href="#B"><stop offset="0" stop-color="#0b0c0d"></stop><stop offset="1" stop-color="#474445"></stop></linearGradient></defs><path fill="url(#j)" d="M470 525c4 0 8 1 12 3s8 6 11 9c0 1-1 2-1 3 0 2-1 4-2 5h0c-1 0-2 0-2-1l-2 1v-1h-1v2c0 1 0 1-1 2s-1 4-1 6v-3l-2 1 1-2h-2v2l-1 1c0 2-1 5 0 8-1 3-1 7 0 10v1l-1 1c-2-9-2-17 0-26 0-1 0-1 1-2-1-3-2-5-3-7v-2c-1-2-1-4-2-6h0c-2-2-3-3-4-5z"></path><path d="M479 553v-1c0-2 0-3 1-4 1-2 1-3 2-4v1 5h-2v2l-1 1z" class="H"></path><path d="M474 530c2 1 3 3 3 5l1 1c1 2 2 6 1 9-1-3-2-5-3-7v-2c-1-2-1-4-2-6z" class="c"></path><defs><linearGradient id="k" x1="476.303" y1="599.628" x2="468.003" y2="603.641" xlink:href="#B"><stop offset="0" stop-color="#645351"></stop><stop offset="1" stop-color="#7c6966"></stop></linearGradient></defs><path fill="url(#k)" d="M455 552l13 40c4 10 9 20 13 30 5 9 9 18 17 24v1l1 1v2c-1 0-1-1-2-1l-1 1 1 1h-1c-2 0-2-1-3-2l-1-1c-2 0-3-1-4-2-1-4-3-10-5-14v-1c-3-3-4-7-6-11l-3-6c-1-2-2-4-4-6 0-2-2-8-3-10-1-3-2-8-4-11v-1c-1-6-4-12-6-17-1-1 0-2-1-4-2-2-2-5-3-7s-1-3-1-5l3-1z"></path><path d="M492 648l-2-3 1-1c2 1 4 4 6 4l1-1 1 1v2c-1 0-1-1-2-1l-1 1 1 1h-1c-2 0-2-1-3-2l-1-1z" class="I"></path><path d="M451 550h0 1 2l1 2-3 1c0 2 0 3 1 5s1 5 3 7c1 2 0 3 1 4 2 5 5 11 6 17v1c2 3 3 8 4 11 0 1-1 3-1 4l-1 1h0c0 1 0 2-1 2s-1-1-2-1h-1c-1-2-1-3-2-5v-1c-1-1-1-2-2-3 0-2-1-4-3-5v-1c-1-6-3-12-4-18v-3l-2-7c1-2 0-2 0-3l1-1c1-1 1-3 1-4v-2l1-1z" class="O"></path><path d="M458 585h2l2 2c-1 1 0 0-2 1v2h-2c0-1 0-1 1-2l-1-1h-1l1-2z" class="U"></path><path d="M448 558l1-1c1-1 1-3 1-4v-2l1-1v2c1 2 0 3 0 5s0 3 1 4v4c-1-1-1-2-2-4l-1 1h1v6l-2-7c1-2 0-2 0-3z" class="C"></path><path d="M453 558c1 2 1 5 3 7 1 2 0 3 1 4l-1 1c0-1 0-2-1-2-1 2 1 8 1 12-1-1-1-2-1-3l-1-1c-1-3-1-4 0-7 0-1-1-2-1-3-1-3 0-6 0-8zm7 32c1 2 1 2 1 4l1 2c1 1 1 3 1 5 1 1 2 1 3 1l-1 1h0c0 1 0 2-1 2s-1-1-2-1h-1c-1-2-1-3-2-5l3 2v-1c0-2-1-4-2-6-1-1-1-2-2-4h2z" class="N"></path><path d="M456 580c0-4-2-10-1-12 1 0 1 1 1 2 1 1 2 3 2 4l2 7c0 1 1 2 0 4h0-2c0-2-1-4-2-5z" class="F"></path><path d="M456 570l1-1c2 5 5 11 6 17v1c2 3 3 8 4 11 0 1-1 3-1 4-1 0-2 0-3-1 0-2 0-4-1-5l-1-2c0-2 0-2-1-4v-2c2-1 1 0 2-1l-2-2h0c1-2 0-3 0-4l-2-7c0-1-1-3-2-4z" class="O"></path><path d="M483 554c0-2 0-5 1-6s1-1 1-2v-2h1v1l2-1c0 1 1 1 2 1h0v5c-3 3-3 6-3 10v1l3 1c0 6 0 13 2 18 0 4 1 8 2 12 2 12 5 23 9 35l2 5-1 1v7l2 6c0 2 0 2 1 3v1 1l-1-1v2 1 1h-1c-1-2-1-4-3-5v-1l-2-3-10-23 1-2c-2-2-2-5-2-8l-2-5-3-8-3-13h0c-1-4-2-7-2-11h1c-1-1 0-3 0-4l-1-1c-1-3 0-6 0-9-1-3 0-6 0-8l1-1v-2h2l-1 2 2-1v3z" class="I"></path><path d="M483 588c1 1 2 2 4 3l-2 2c-1-1-1-1-1-2-1-1-1-2-1-3z" class="N"></path><path d="M489 612c2 3 3 7 4 10h0c-1-1-2-1-2-2-2-2-2-5-2-8z" class="G"></path><path d="M480 552v-2h2l-1 2 2-1v3h0v2l-1 8h0c-2-1 0-9-2-12z" class="S"></path><path d="M481 552l2-1v3h0v2l-1 1c-1-1 0-4-1-5z" class="H"></path><path d="M481 586v-2h0c1 1 2 3 2 4s0 2 1 3c0 1 0 1 1 2l1 1 1-1v2c0 2 1 3 2 4 0 1 0 3 1 4l-1 1c-1-1-1-2-1-3l-4-4 3 7v3l-3-8-3-13z" class="O"></path><path d="M483 554c0-2 0-5 1-6s1-1 1-2v-2h1v1l2-1c0 1 1 1 2 1h0v5c-3 3-3 6-3 10v1 1c0 4-1 8 0 12 0 4 2 8 2 12h0c-1-1-2-2-3-4v-3 2c-2 1 0 3-1 4h-1l1-2c-1-1-1-3-1-4-1-2-1-3-1-5-1-1-1 0-1-1v-1-3h0v1h1c-1-2-1-4-1-6l1-8v-2h0z" class="U"></path><path d="M483 554c0-2 0-5 1-6s1-1 1-2v-2h1v1l2-1c0 1 1 1 2 1h0v5c-3 3-3 6-3 10v1 1l-1-1v-14l-2 5c0 1 0 1-1 2h0z" class="B"></path><defs><linearGradient id="l" x1="481.879" y1="589.525" x2="505.641" y2="594.377" xlink:href="#B"><stop offset="0" stop-color="#8f7071"></stop><stop offset="1" stop-color="#aa988b"></stop></linearGradient></defs><path fill="url(#l)" d="M487 561l3 1c0 6 0 13 2 18 0 4 1 8 2 12 2 12 5 23 9 35l2 5-1 1c-2-3-2-6-5-9l-1-5-9-33c0-4-2-8-2-12-1-4 0-8 0-12v-1z"></path><defs><linearGradient id="m" x1="501.944" y1="634.039" x2="494.235" y2="639.942" xlink:href="#B"><stop offset="0" stop-color="#7b6361"></stop><stop offset="1" stop-color="#9f817b"></stop></linearGradient></defs><path fill="url(#m)" d="M493 622c1 2 3 7 4 8v-2-1l-2-10c0-1-1-2 0-3v2l3 6v-3l1 5c3 3 3 6 5 9v7l2 6c0 2 0 2 1 3v1 1l-1-1v2 1 1h-1c-1-2-1-4-3-5v-1l-2-3-10-23 1-2c0 1 1 1 2 2h0z"></path><path d="M499 624c3 3 3 6 5 9v7l-5-16z" class="a"></path><defs><linearGradient id="n" x1="484.418" y1="610.089" x2="455.871" y2="660.787" xlink:href="#B"><stop offset="0" stop-color="#585250"></stop><stop offset="1" stop-color="#ab8a88"></stop></linearGradient></defs><path fill="url(#n)" d="M454 590c2 1 3 3 3 5 1 1 1 2 2 3v1c1 2 1 3 2 5h1c1 0 1 1 2 1s1-1 1-2h0l1-1c0-1 1-3 1-4 1 2 3 8 3 10 2 2 3 4 4 6l3 6c2 4 3 8 6 11v1c2 4 4 10 5 14 1 1 2 2 4 2l1 1c1 1 1 2 3 2h1l-1-1 1-1c1 0 1 1 2 1v-2l-1-1v-1h1 0l1-1 2 3c1 2 4 10 4 11h-1l-3-8c0-1-1-2-1-3v2 2c0 2 1 4 2 6 0 2 0 4 1 6v5c1 2 1 5 1 7l1 4c0 3 3 6 3 9v1-1l-2-2-1-2c-1 0-1 0-2 1h1l-1 1v1 2 2h-1v-1c0-1 0-2-1-3v-1h0c-1 2-1 3-1 5l1 1h0l-1-2v5c-2-2-2-4-3-5h-2c1 1 2 3 3 4l-1 1-2-2-4-4-1-1c-1 0-2-1-3-2s-2-1-3-1l-5-1-10-33-2-7v-1c0-1 1-2 0-3v-3-1c0-1-1-2 0-3 0-3-2-7-3-10-2-6-4-13-6-19l-5-13v-2z"></path><path d="M468 634c2 6 5 11 6 17l-1 1v-2c-1-2-1-3-2-4v1 1l-1 1v1 2l-2-7v-1c0-1 1-2 0-3v-3-1c0-1-1-2 0-3z" class="n"></path><path d="M470 652v-2-1l1-1v-1-1c1 1 1 2 2 4v2l1-1v1c1 2 1 5 2 7 1 5 3 10 4 15h1v4c1-1 2-1 3-1l1 1c0 1 0 0-1 1h1l2-2 2-1c3-1 3-2 6-1-3 1-6 1-8 4l1 1c1-1 1-1 3-1 0 1-1 2-1 3l-1 2 2 5c-1 0-2-1-3-2s-2-1-3-1l-5-1-10-33z" class="X"></path><path d="M487 679l1 1c1-1 1-1 3-1 0 1-1 2-1 3l-1 2c-2-1-3-2-3-4l1-1z" class="Z"></path><defs><linearGradient id="o" x1="472.813" y1="638.733" x2="487.299" y2="637.436" xlink:href="#B"><stop offset="0" stop-color="#645654"></stop><stop offset="1" stop-color="#7f6a67"></stop></linearGradient></defs><path fill="url(#o)" d="M471 620l-1-6c-1-2-1-2-1-3v-2-2l1 1c2 2 3 4 4 6l3 6c2 4 3 8 6 11v1c2 4 4 10 5 14 1 1 2 2 4 2l1 1c0 2 1 3 0 5-1 0-1 0-1 1l-1-1c-1 0-2 0-3 1l1 2c0 2 0 4 1 6-1 0-2-1-3-2l1 1-2 1v-1c-1-1-1-2-2-2l1-2h0c-1 0-2 1-3 1-1-4-3-8-5-12-3-8-5-18-6-27z"></path><path d="M471 620l-1-6c-1-2-1-2-1-3v-2-2l1 1v1c0 1 1 1 1 2l2 7h-1 0c0-1 0-1-1-2v4h0z" class="N"></path><path d="M483 632c2 4 4 10 5 14 1 1 2 2 4 2l1 1c0 2 1 3 0 5-1 0-1 0-1 1l-1-1c-1 0-2 0-3 1 0-3-1-6-1-8-1-5-4-10-4-15z" class="I"></path><path d="M488 646c1 1 2 2 4 2l1 1c0 2 1 3 0 5-1 0-1 0-1 1l-1-1-3-8z" class="F"></path><path d="M500 645l2 3c1 2 4 10 4 11h-1l-3-8c0-1-1-2-1-3v2 2c0 2 1 4 2 6 0 2 0 4 1 6v5c1 2 1 5 1 7l1 4c0 3 3 6 3 9v1-1l-2-2-1-2c-1 0-1 0-2 1h1l-1 1v1 2 2h-1v-1c0-1 0-2-1-3v-1h0c-1 2-1 3-1 5l1 1h0l-1-2v5c-2-2-2-4-3-5h-2c1 1 2 3 3 4l-1 1-2-2-4-4-1-1-2-5 1-2c0-1 1-2 1-3-2 0-2 0-3 1l-1-1c2-3 5-3 8-4-3-1-3 0-6 1l-2 1-2-5c0-2-1-4-2-6 0-3-1-5-1-7 1 0 2-1 3-1h0l-1 2c1 0 1 1 2 2v1l2-1-1-1c1 1 2 2 3 2-1-2-1-4-1-6l-1-2c1-1 2-1 3-1l1 1c0-1 0-1 1-1 1-2 0-3 0-5 1 1 1 2 3 2h1l-1-1 1-1c1 0 1 1 2 1v-2l-1-1v-1h1 0l1-1z" class="H"></path><path d="M498 669c2 2 3 2 3 4h-1v-1l-2 1v-4z" class="L"></path><path d="M500 645l2 3c1 2 4 10 4 11h-1l-3-8c0-1-1-2-1-3v2 2c0 2 1 4 2 6 0 2 0 4 1 6v5c1 2 1 5 1 7l-1-2-1-1v-2h0v-2h0-2v-4l1 2h0v-1h1v-1-1c-1-1-1 0-1-1-2-3 0-6-2-9-1-2 0-4-1-6l-1-1v-1h1 0l1-1z" class="B"></path><path d="M499 648c1 2 0 4 1 6l-2 3h-1c0-1 0-1-1-2-1 2 0 4 0 6 1 1 2 2 2 4v4 4-1c-1-1-1-1-1-3-2-2-3-4-4-7l-1 4c-1-1 0-1-1-1 0-3-1-5-1-8h-1l-1-2c1-1 2-1 3-1l1 1c0-1 0-1 1-1 1-2 0-3 0-5 1 1 1 2 3 2h1l-1-1 1-1c1 0 1 1 2 1v-2z" class="N"></path><path d="M490 657l1-1c1 1 2 2 2 4v2h0l-1 4c-1-1 0-1-1-1 0-3-1-5-1-8z" class="L"></path><path d="M489 657h1c0 3 1 5 1 8 1 0 0 0 1 1 1 2 1 4 2 6l1 2h3c-1 1-2 1-3 1h0c-3-1-3 0-6 1l-2 1-2-5c0-2-1-4-2-6 0-3-1-5-1-7 1 0 2-1 3-1h0l-1 2c1 0 1 1 2 2v1l2-1-1-1c1 1 2 2 3 2-1-2-1-4-1-6z" class="F"></path><g class="G"><path d="M488 662l1 2v1h-3v-2l2-1z"></path><path d="M489 676l-1-5h-1l-1-1h2c0-1-1-2-1-3v-1l1-1c0 1 1 2 1 3s1 2 1 3l1 1h1l1 2h2 3c-1 1-2 1-3 1h0c-3-1-3 0-6 1z"></path></g><path d="M491 679h0 1l-1-1c1-1 1-2 2-2h2v1c0 1 0 1 1 1v-2h1c0 2 2 5 1 7l1 3c1 2 1 3 2 5v5c-2-2-2-4-3-5h-2c1 1 2 3 3 4l-1 1-2-2-4-4-1-1-2-5 1-2c0-1 1-2 1-3z" class="I"></path><path d="M498 683l1 3c1 2 1 3 2 5v5c-2-2-2-4-3-5l1-1c0-2 0-5-1-6h0v-1z" class="F"></path><path d="M490 682c1 1 2 2 2 4 1 1 1 2 2 3-1 0-1 0-2 1l-1-1-2-5 1-2z" class="R"></path><path d="M494 689h0c0-1-1-2-1-3v-7h0c3 3 3 5 4 8l1 4h-2c1 1 2 3 3 4l-1 1-2-2-4-4c1-1 1-1 2-1z" class="j"></path><path d="M496 691h0l-1-1v-3h2l1 4h-2z" class="F"></path><defs><linearGradient id="p" x1="481.02" y1="529.712" x2="592.979" y2="601.613" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#302d2d"></stop></linearGradient></defs><path fill="url(#p)" d="M446 472c-2-3-2-7-3-9-1-3-3-7-2-9 2 4 4 7 6 11s5 7 8 10c4 4 8 8 13 11 6 4 12 7 18 10 2 1 5 3 6 3 2 0 2 0 3 1 2 0 3 1 4 1l6 1-3-3-1-1h5l3 1 2 3c1 0 3 2 5 2 1 0 3 0 4 1 3 1 6 1 9 1 6 2 12 0 18 0l-1 1c4-1 9-1 13-3h2l4-1v1c2-1 4-1 5-2h4c2 0 6-4 8-5 3-3 8-7 11-10 1-3 5-7 8-8l-7 19c-1 1-2 4-2 6l-3 9-6 22-9 28-3 12-5 17c-1 6-4 12-5 18-2 3-5 8-6 12 0 2 1 4 2 6l-4 13c-1 1-2 3-1 5-1 2-2 3-2 5-1 1-2 1-3 3v3c-1 2-1 4-2 6v2h-1v-2c-1 1 0 2-1 4l1 1v2 1h0l-1 1h0 0l-1 1v2c0 1 0 0-1 1h-1c0-2 1-4 1-6l-1 3-1 1h0c1 2 0 3 0 5v1 1 6h0c0 2-1 4-1 5v1l-1-1-2 1c1-2 0-4 0-5l-1-3c-1-3-2-7-4-9h-1v-1c-1 0-1 1-2 1-1-2 0-2-1-3-1 0-2 1-2 2l-6-11s-1-2-2-2l-5-11-1-2-1-3h-2-1l-2-6v-7l1-1-2-5c-4-12-7-23-9-35-1-4-2-8-2-12-2-5-2-12-2-18l-3-1v-1c0-4 0-7 3-10v-5c1-1 2-3 2-5 0-1 1-2 1-3l13 13c0-3-2-4-3-6s-2-3-3-5c-2-2-5-4-7-7-6-4-10-8-17-10-1 0-2-1-2-1-1 0-1 1-2 1 0 0-1 0-1-1h0-1l-1-1c-2-2-2-5-3-7 0-1-1-3-1-4l-3-6c-2-4-4-8-7-12l-3-7-6-12z"></path><path d="M547 621h1c1-1 1-1 1-2h0c1 4-2 8-3 11l-1-1c0-1 1-1 0-2h0c0-2 1-4 2-6z" class="B"></path><path d="M490 550v12l-3-1v-1c0-4 0-7 3-10z" class="m"></path><path d="M576 529v1 2l-1 2c0 2-1 7-3 8v-2c-1 2-1 3-2 5v-1-1l-1 2c0-3 3-10 5-13l2-3z" class="f"></path><path d="M528 615c2 4 3 8 5 11v5c1 3 2 9 1 12-2-3-2-6-2-9v-1c-1-1-1 0-1-1 0-2-1-4-1-6h-1l1-1h1l-1-3c-1-2-1-3-2-4v-3z" class="T"></path><path d="M532 674l2-1v-1h1l2 20-2 1c1-2 0-4 0-5l-1-3c-1-3-2-7-4-9 1-2 0-2 0-4h2v2z" class="R"></path><path d="M530 672h2v2 2c1 1 1 1 1 2h1v1 6c-1-3-2-7-4-9 1-2 0-2 0-4z" class="G"></path><path d="M508 532c5 6 9 12 13 18l3 5c0 1 1 2 1 3l-1-1-3-3c-2-2-3-3-5-6 1-1-6-9-7-11s-1-3-1-5z" class="e"></path><path d="M559 579c1-3 2-4 2-6 2-4 3-9 5-12l3-6c0-1 0 0 1-1s1-3 2-3v-2l1-1c0-1 1-2 1-3v1c0 3-1 6-2 9l-7 12c-1 2-1 5-2 7s-3 3-4 5z" class="E"></path><path d="M572 555h1l1-3v-1s1 0 1 1-4 13-5 14v1l-1-1c-3 5-4 10-5 16-1 1-2 3-2 4s1 0 0 1l-2 2v2l-1-1s1-1 1-2h-1v-3c1-1 1 0 1-1-1 0-2 0-3 1l2-6c1-2 3-3 4-5s1-5 2-7l7-12z" class="D"></path><path d="M560 591v-2l2-2c1-1 0 0 0-1s1-3 2-4c1-6 2-11 5-16l1 1v1c-1 2-1 3-1 5-1 0-1 1-1 2l-4 16c-1 1-1 2-2 3v2c-1 1-2 3-2 4-1 2-2 3-3 4-1 2-1 5-2 7-1 3-1 6-3 8-1-1-1-1-2-1l1-2c0-1 0-2 1-3l1-2h0v-2h1v-1h1 1v-1-1l-2-1v-4l1-1c1-4 3-6 5-9z" class="L"></path><path d="M553 513c7-2 13-4 19-7l-5 20h-2c-3-4-6-6-9-10-1-1-1-2-3-3h0z" class="K"></path><path d="M501 498h5l3 1 2 3c1 0 3 2 5 2 1 0 3 0 4 1 3 1 6 1 9 1 6 2 12 0 18 0l-1 1c4-1 9-1 13-3h2l4-1v1c-16 5-31 6-47 3-6-1-12-3-18-3-1-2-2-2-4-2-1-2-3-2-4-3 2 0 2 0 3 1 2 0 3 1 4 1l6 1-3-3-1-1z" class="I"></path><path d="M501 498h5l3 1 2 3c1 0 3 2 5 2 1 0 3 0 4 1 3 1 6 1 9 1 6 2 12 0 18 0l-1 1h-1 0c-5 2-11 1-16 0-3 0-8 0-11-1-4-1-7-3-11-4h-2l-3-3-1-1z" class="S"></path><path d="M501 498h5l3 1 2 3-9-3-1-1z" class="I"></path><path d="M545 639v-3c0-1 2-2 2-2v-3l-1-1h1c0-1 0-2 1-3h0 1c0 1 0 1-1 2v1c1 4-3 8-2 12h0c1-1 1-2 1-3l1 1c0-1 1-2 1-2 2 1 1 2 1 4h1 1v-3l1-1v-2h0v-1c1-1 1-1 1-2h0v-1c1-1 1-1 1-2v-3-1-1h-1v-2-1c0-1 0-2 1-2v-1c0-1 0-2 1-2 1-1 1-2 1-2l1-1v-2h0v-1c1-2 1-3 3-4v-1-1l1-1v-3c1-1 1-1 1-2v-1l2-6s1-1 1-2 0-2 1-2l1-6v-2h1c0-2 1-3 2-5l-5 17c-1 6-4 12-5 18-2 3-5 8-6 12 0 2 1 4 2 6l-4 13c-1 1-2 3-1 5-1 2-2 3-2 5-1 1-2 1-3 3v3c-1 2-1 4-2 6v2h-1v-2c-1 1 0 2-1 4l1 1v2 1h0l-1 1h0 0l-1 1v2c0 1 0 0-1 1h-1c0-2 1-4 1-6 1-3 0-5 0-8v-2-5c1-2 2-4 2-6 0-4 0-7 2-10z" class="B"></path><path d="M541 662h1c0-1 0-2 1-3v-2c0-2 1-5 1-7 0 0 1-2 2-2 0 5-3 11-1 15v2h-1v-2c-1 1 0 2-1 4l1 1v2 1h0l-1 1h0 0l-1 1v2c0 1 0 0-1 1h-1c0-2 1-4 1-6 1-3 0-5 0-8z" class="I"></path><path d="M517 524c-1-1 0-3 0-5-1 0-2-2-2-2l-3-7 20 4c-1 0-2 1-2 1-1 0-1-1-2-1v1c1 1 1 2 1 3h0l-1 1h0c-1-2-1-1-3-2-1 1-2 3-3 3 0 2 1 2 1 4 1 1 2 3 2 5 1 2 2 8 3 10l2 5c-1 2-3 4-3 6l-3 3v-2l-4-14c-1-4-3-8-3-13z" class="r"></path><path d="M525 517v-1l2-1h1v1l1 2-1 1h0c-1-2-1-1-3-2z" class="b"></path><path d="M517 524h0c0-2 0-2 1-3h1c0 1 0 3 1 3 1 2 1 2 1 4 1 1 2 2 2 3 1 1 1 3 1 4 1 1 1 2 1 3 1 2 1 3 2 5h1c1-1 0-1 0-1v-3l2 5c-1 2-3 4-3 6l-3 3v-2l-4-14c-1-4-3-8-3-13z" class="Y"></path><defs><linearGradient id="q" x1="543.05" y1="617.702" x2="555.84" y2="619.225" xlink:href="#B"><stop offset="0" stop-color="#222122"></stop><stop offset="1" stop-color="#4b3f3e"></stop></linearGradient></defs><path fill="url(#q)" d="M557 585c1-1 2-1 3-1 0 1 0 0-1 1v3h1c0 1-1 2-1 2l1 1c-2 3-4 5-5 9l-1 1c-1 2-1 4-2 7-1 1-1 2-1 3-2 4-3 6-4 10-1 2-2 4-2 6h0c1 1 0 1 0 2l1 1v1c-1 2-2 5-2 8h1c-2 3-2 6-2 10 0 2-1 4-2 6v5 2c0 3 1 5 0 8l-1 3-1 1h0c1 2 0 3 0 5v1 1l-1-1v-17-8c0-1 1-2 0-3 1-2 0-4 1-6v-4c0-1 0-2 1-3v-4c0-1 0-2 1-4v-3c0-1 0-2 1-3v-2h0l1-1h0c0-1 0-1 1-2v-2h0c1-1 1-2 1-2 0-1 1-2 1-2l11-29z"></path><path d="M539 679c-1-3 0-5 0-8v-5h0v-2c0-2-1-9 1-11v7h1v2c0 3 1 5 0 8l-1 3-1 1h0c1 2 0 3 0 5z" class="L"></path><defs><linearGradient id="r" x1="519.705" y1="590.958" x2="505.132" y2="663.406" xlink:href="#B"><stop offset="0" stop-color="#151616"></stop><stop offset="1" stop-color="#7f6b69"></stop></linearGradient></defs><path fill="url(#r)" d="M492 580h0c1 3 1 5 2 8s1 7 2 10c2-1 2-3 4-3 1-2 0-4 2-5 1-1 2-1 2-2h1l3 3c0-1 1-2 1-3l-1-1h1c1 1 1 3 2 5 0 1 2 3 2 4h1c2 1 3 2 4 3 2 2 2 4 4 5 1 1 2 2 2 4s2 4 3 7h1v3c1 1 1 2 2 4l1 3h-1l-1 1h1c0 2 1 4 1 6 0 1 0 0 1 1v1c0 3 0 6 2 9 0 6 1 11 1 16v6 7h-1v1l-2 1v-2h-2c0 2 1 2 0 4h-1v-1c-1 0-1 1-2 1-1-2 0-2-1-3-1 0-2 1-2 2l-6-11s-1-2-2-2l-5-11-1-2-1-3h-2-1l-2-6v-7l1-1-2-5c-4-12-7-23-9-35-1-4-2-8-2-12z"></path><path d="M511 599s1 1 1 2 0 1-1 1h0l-1-1v-2 1l1-1z" class="E"></path><path d="M515 612v1c-1-1-2-2-2-3-1-1-2-3-2-5l2 3h0c1 2 2 2 2 4z" class="C"></path><path d="M522 655h1v1c-1 0 0 0-1 1l2 1-1 2c-1-1-2-1-2-3 0-1 0-1 1-2z" class="F"></path><path d="M503 627c2 0 2 1 3 3 1 1 1 3 1 4v1l-2-2v-1l-2-5z" class="N"></path><path d="M505 633l2 2c0 1 1 2 1 3l2 3v2c1 1 1 3 1 5h0l-1 1-1-3c0-4-3-9-4-13z" class="F"></path><path d="M505 632v1c1 4 4 9 4 13h-2-1l-2-6v-7l1-1z" class="n"></path><path d="M513 608l1-1c2 1 2 2 3 4v1c1 1 1 3 2 5 0 0 1 0 1 1 1 1 1 2 1 3l-2-2c-1-1-1-2-2-3 0 1 0 1 1 2 0 2-1 3-1 5v-2c0-2-1-3-1-5v-1c-1-1-1-2-1-3 0-2-1-2-2-4z" class="D"></path><path d="M510 649l1-1c1 1 1 2 2 2v2c1 1 1 2 2 3v1c1 1 2 2 3 2 1 1 1 2 1 4h1v2c1 1 1 2 3 4h1v-1h-1l1-1h2 0c1 1 1 1 2 1v1c1 2 1 2 2 3v1c0 2 1 2 0 4h-1v-1c-1 0-1 1-2 1-1-2 0-2-1-3-1 0-2 1-2 2l-6-11s-1-2-2-2l-5-11-1-2z" class="t"></path><path d="M517 623c0-2 1-3 1-5-1-1-1-1-1-2 1 1 1 2 2 3l2 2c0 2 1 4 1 6l3 6c-1 1-1 2-1 2 0 2 1 3 0 4v-1c-1 0-1-1-1-2h-1v1c0 2 1 3 1 5-1 0-2-1-3-1-1-2-2-4-2-6h0 0l-1-1h1c1-1 0-1 0-2h-1 0c-1-1-1-1-1-2-1-2-1-2 0-3 1 0 1 0 2 1 0 2 0 3 1 4h0l1-1c-1-2-1-5-3-8z" class="I"></path><defs><linearGradient id="s" x1="532.445" y1="648.161" x2="525.099" y2="652.125" xlink:href="#B"><stop offset="0" stop-color="#574947"></stop><stop offset="1" stop-color="#756360"></stop></linearGradient></defs><path fill="url(#s)" d="M532 633v1c0 3 0 6 2 9 0 6 1 11 1 16v6 7h-1v1l-2 1v-2h-2v-1c-1-1-1-1-2-3v-1-2-3h-2v-2l2-2c-1-1-1-2-1-4-1-1-2-2-2-3v-1c-1-1-1-1 0-2h0l1-3c-2-1-1 0-2-1l1-1h1l-1-1-1-1v-1l-1 2h0c0-2-1-3-1-5v-1h1c0 1 0 2 1 2v1l2 3c0 1 0 0 1 1v-2c0-1-1-2-1-3l1-1c1 1 1 2 1 3h1v-2h0v1c1 1 1 2 1 3 0-1 0-3 1-4v1-1c0-2 0-3 1-5z"></path><path d="M533 661c1-3 1-5-1-8 0-1 0-2 1-3 1 3 0 7 2 9v6c-1-2-1-3-2-4z" class="S"></path><path d="M532 633v1c0 3 0 6 2 9 0 6 1 11 1 16-2-2-1-6-2-9h-1c0-3-1-6-2-8 0-1 0-3 1-4v1-1c0-2 0-3 1-5z" class="O"></path><path d="M528 658h0c1 1 1 0 2 1v-2l1 1v3h0 0 0l2 1v-1c1 1 1 2 2 4v7h-1v1l-2 1v-2h-2v-1c-1-1-1-1-2-3v-1-2-3h-2v-2l2-2z" class="R"></path><path d="M528 668l1-1v-2h1c0 2 0 4 1 5l-1 1c-1-1-1-1-2-3z" class="G"></path><path d="M533 662v-1c1 1 1 2 2 4v7h-1v1l-2 1v-2c1-1 1-8 1-10z" class="F"></path><path d="M446 472c-2-3-2-7-3-9-1-3-3-7-2-9 2 4 4 7 6 11s5 7 8 10c4 4 8 8 13 11 6 4 12 7 18 10 2 1 5 3 6 3 1 1 3 1 4 3 2 0 3 0 4 2l-3 1h0l-4 5c1 2 1 3 2 5l4 5 9 12h0c0 2 0 3 1 5s8 10 7 11c2 3 3 4 5 6l3 3 1 1 2 3c2 3 3 6 5 9 1 4 3 8 4 12 2 3 2 6 3 10 1 3 4 7 3 10v1l1 1c0 1-1 2-1 3-1-5-4-8-7-12l-8-14-6-9c-1-1-2-3-3-4l-7-11c-1-2-3-4-5-7 0-3-2-4-3-6s-2-3-3-5c-2-2-5-4-7-7-6-4-10-8-17-10-1 0-2-1-2-1-1 0-1 1-2 1 0 0-1 0-1-1h0-1l-1-1c-2-2-2-5-3-7 0-1-1-3-1-4l-3-6c-2-4-4-8-7-12l-3-7-6-12z" class="Z"></path><path d="M482 518c2 1 3 1 4 3 2 3 9 5 10 9l-5-2c-1-1-3-3-4-3-1-1-1-2-2-3-1-2-2-3-3-4zm37 37c2 1 3 1 5 2l1 1 2 3c2 3 3 6 5 9-3-1-5-4-6-7v-1c-1-1-2-2-3-2h-1c-1-1-1-1-1-2l-2-3z" class="G"></path><path d="M519 555c2 1 3 1 5 2l1 1 2 3h0c-1-1-2-1-3-2h0c-1-1-2-1-3-1l-2-3z" class="Q"></path><path d="M505 536c4 2 7 7 10 11l1 1c2 3 3 4 5 6l3 3c-2-1-3-1-5-2-1 0-3-4-4-6-3-4-8-8-10-13z" class="j"></path><path d="M480 516l2 2c1 1 2 2 3 4 1 1 1 2 2 3 1 0 3 2 4 3l5 2h-1l1 1v2c-1 0-1-1-2-1h-1c-6-4-10-8-17-10 1 0 1 0 1-1v-1h0c2 1 2 2 4 2 0-1 1-1 0-2h0c1-2 0-2-1-4z" class="S"></path><path d="M491 528l5 2h-1l1 1v2c-1 0-1-1-2-1-1-1-2-3-3-4z" class="R"></path><path d="M495 530l9 8c1 2 2 3 3 4 2 3 4 5 6 8h-1c-1 0-1-1-2-1v1c0 1 1 3 1 5 0 1 1 1 0 2-1-2-3-4-5-7 0-3-2-4-3-6s-2-3-3-5c-2-2-5-4-7-7h1c1 0 1 1 2 1v-2l-1-1z" class="j"></path><path d="M495 530l9 8c1 2 2 3 3 4 0 2 0 3 2 5h0v1l-3-3c-1-1-2-1-2-2-1-2-1-2-3-4h-1c-2-2-5-4-7-7h1c1 0 1 1 2 1v-2l-1-1z" class="U"></path><path d="M484 515c0-2-2-3-3-5 3 0 4 2 7 4l1-1c0 1 1 2 1 2h3c0 1 1 2 2 2h0v-1-1l4 5 9 12h0c0 2 0 3 1 5s8 10 7 11l-1-1c-3-4-6-9-10-11-8-6-14-14-21-21z" class="I"></path><path d="M488 514l1-1c0 1 1 2 1 2h3c0 1 1 2 2 2h0v-1-1l4 5h-4c-2-2-5-4-7-6z" class="e"></path><path d="M511 557c1-1 0-1 0-2 0-2-1-4-1-5v-1c1 0 1 1 2 1h1c3 4 5 9 9 12v1c0 1 0 2 1 3h0v-1-1c2 1 5 6 6 9 1 1 2 2 2 3 1 1 1 1 1 2l3 6 6 17h-1c-2-3-4-7-5-10 0-3-2-5-3-7-2-4-4-7-6-10v1 1c1 1 1 2 1 4v1l-6-9c-1-1-2-3-3-4l-7-11z" class="I"></path><path d="M521 572v-1l2 1h0c0-2-1-3-1-4h0l4 6v1 1c1 1 1 2 1 4v1l-6-9z" class="j"></path><defs><linearGradient id="t" x1="477.437" y1="480.673" x2="455.128" y2="498.414" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#4d4645"></stop></linearGradient></defs><path fill="url(#t)" d="M446 472c-2-3-2-7-3-9-1-3-3-7-2-9 2 4 4 7 6 11s5 7 8 10c4 4 8 8 13 11 6 4 12 7 18 10 2 1 5 3 6 3 1 1 3 1 4 3 2 0 3 0 4 2l-3 1h0l-4 5c1 2 1 3 2 5v1 1h0c-1 0-2-1-2-2h-3s-1-1-1-2l-1 1c-3-2-4-4-7-4 1 2 3 3 3 5-4-3-9-9-13-13l-1 1c2 3 4 5 5 8l5 5c1 2 2 2 1 4h0c1 1 0 1 0 2-2 0-2-1-4-2h0v1c0 1 0 1-1 1s-2-1-2-1c-1 0-1 1-2 1 0 0-1 0-1-1h0-1l-1-1c-2-2-2-5-3-7 0-1-1-3-1-4l-3-6c-2-4-4-8-7-12l-3-7-6-12z"></path><path d="M465 509h1v1l2 2v1c1 2 2 3 2 5-1-2-2-4-4-5 0-1-1-3-1-4z" class="O"></path><path d="M466 513c2 1 3 3 4 5 1 1 0 1 0 3l-1-1c-2-2-2-5-3-7z" class="N"></path><path d="M469 505c2 2 4 5 6 6l5 5c1 2 2 2 1 4h0c1 1 0 1 0 2-2 0-2-1-4-2h0c0-1 0-1 1-2 0 1 1 2 2 3v-1s0-1-1-2c-1 0-1 0-2-1h2v-1c-1-1-2-1-3-1l-1-1c-1 0-1-1-2-2-2-1-3-2-4-3 1-2 0-2 0-4z" class="O"></path><path d="M463 495c2 0 6 5 8 7h0l-1 1c2 3 4 5 5 8-2-1-4-4-6-6-2-1-2-3-3-5s-2-4-3-5z" class="N"></path><path d="M446 472c-2-3-2-7-3-9-1-3-3-7-2-9 2 4 4 7 6 11s5 7 8 10c-2 0-3-1-4-2l-1-3h-2l-3-4h0c1 2 1 4 1 6z" class="B"></path><defs><linearGradient id="u" x1="480.294" y1="492.874" x2="474.206" y2="509.626" xlink:href="#B"><stop offset="0" stop-color="#1d2022"></stop><stop offset="1" stop-color="#5c504e"></stop></linearGradient></defs><path fill="url(#u)" d="M463 495l-2-2v-1c0-1-1-2-1-3s-1-2-1-3l-1-1v-2c1 1 0 0 1 2h1 2v1l3 3v1l6 7h0c3 2 5 4 8 5l2 1c3 1 6 2 9 2s4-2 6-3c2 0 3 0 4 2l-3 1h0l-4 5c1 2 1 3 2 5v1 1h0c-1 0-2-1-2-2h-3s-1-1-1-2l-1 1c-3-2-4-4-7-4 1 2 3 3 3 5-4-3-9-9-13-13h0c-2-2-6-7-8-7z"></path><path d="M471 497c3 2 5 4 8 5l2 1c3 1 6 2 9 2s4-2 6-3c2 0 3 0 4 2l-3 1h0l-4 5c-1 0-1 0-2-1-2-1-4-1-6-2-3 0-5-1-7-3h1l-1-1s-1 0-2-1c-2-1-4-2-5-5z" class="e"></path><path d="M496 502c2 0 3 0 4 2l-3 1h0c-3 0-5 1-7 2-2 0-4-1-6-2-1-1-3-1-3-2 3 1 6 2 9 2s4-2 6-3z" class="j"></path><path d="M553 513h0c2 1 2 2 3 3 3 4 6 6 9 10h2l-14 47-6 17c-1 3-2 7-2 10-1 2-1 3-2 4l-1-1v-1c1-3-2-7-3-10-1-4-1-7-3-10-1-4-3-8-4-12-2-3-3-6-5-9l-2-3c0-1-1-2-1-3l-3-5s1 1 2 1h1v2l3-3c0-2 2-4 3-6l-2-5c-1-2-2-8-3-10 0-2-1-4-2-5 0-2-1-2-1-4 1 0 2-2 3-3 2 1 2 0 3 2h0l1-1h0c0-1 0-2-1-3v-1c1 0 1 1 2 1 0 0 1-1 2-1 7 1 15 0 21-1z" class="i"></path><path d="M529 518h0 0c0 1 2 5 2 6-1 1-2 3-2 3-1 0-1 1-1 1-1-1-1-1-1-2 1-2 0-5 1-7h0l1-1zm1 26l4-4c0 1 0 1-1 2l1 1c-1 2-3 2-4 4-1 1-2 3-3 4v-1c0-2 2-4 3-6zm15-11c1 0 1 0 2-1 3-2 5-4 8-6-1 4-3 7-7 10l-5 4h0c0-1-1-1-1-1-1-1-2-1-3-2 1-2 4-3 5-4h1z" class="J"></path><path d="M553 513c2 1 2 2 3 3 3 4 6 6 9 10-1 1 0 1-1 2l-8-10c-1 2 0 5-1 7v1c-3 2-5 4-8 6-1 1-1 1-2 1 2-3 5-5 7-8v-1c1-1 1-3 1-4 0-3-1-4 0-7zm-26 38v2l2 4 9-6c4-2 8-4 12-5v1l-2 3c-2 0-5 2-7 4l-1 1-6 4c-1 0-1 0-1 1l-1-1c-1-1-2-1-3-1-2-2-4-3-5-5l3-3v1z" class="p"></path><defs><linearGradient id="v" x1="541.319" y1="564.442" x2="528.386" y2="590.151" xlink:href="#B"><stop offset="0" stop-color="#211f1e"></stop><stop offset="1" stop-color="#423d3f"></stop></linearGradient></defs><path fill="url(#v)" d="M524 551v2c1 2 3 3 5 5 1 0 2 0 3 1l1 1c1 6 3 12 5 18l7 22c-1 2-1 3-2 4l-1-1v-1c1-3-2-7-3-10-1-4-1-7-3-10-1-4-3-8-4-12-2-3-3-6-5-9l-2-3c0-1-1-2-1-3l-3-5s1 1 2 1h1z"></path><path d="M553 544l3-3h1c1 1 1 1 1 3-1 0-2 0-2 1-1 1-1 2-1 3h-1c-1 3-1 6-1 8 0 5-2 10-3 14-1 5-3 9-4 13-1-1-1-1-1-2v-6c1-7 1-14 2-20v-2-1h0l1-1c-2 0-2 0-3 1v1h-1l-1 1-3 1 1-1c2-2 5-4 7-4l2-3v-1l3-2z" class="r"></path><path d="M550 546l3-2 1 2-1 1h-2c-1 2-1 2-3 3h0l2-3v-1z" class="J"></path><path d="M255 181l2-1 4 2 1-1h1c1 1 2 1 2 2h1c1 1 1 2 2 3 2 0 2 1 2 1 1 1 2 0 3 1l-2 1h1l3-1 1 1h0 3l16-2 48-2c6 1 12 0 18 1h1c1 0 2 1 3 1h0l1 2v-1c1 0 2-1 3-1h1 2c2 0 4-1 6-1 1 1 2 1 3 1l1-1 2 1v1c2 1 3 1 4 4 0 0 1 1 1 2l1 2c-1 2-2 3-2 5h1l1-1 1-2v-2h1c1 0 1 1 1 2s1 2 2 3c0 2 0 4 1 7v2h0l1 8c0 2 1 4 1 6l-1 3h-1c0-2 0-3-1-5h0c-1 1-1 2-1 3l-1 6-2 3h0c0 3-2 5-2 7h-2v4l-2 3c-1 2-2 3-5 3 1 1 0 2 0 4-1 1-1 2-1 4h0c-1 3-2 7-3 10l2 5c0 1 0 1-1 3l1 1 1-1 1 2-1 1v2c-1-3-3-6-5-9h0c-1-3-2-4-1-6h-4l-1 1c-2-1-4-1-7-2-4 0-7 0-11 1l-10 5-8 4c-2 2-6 5-9 6h0l-1 1h-1l1-1c-2 1-4 3-6 4h-2c-3 1-5 3-8 4h0c0-1 1-1 1-2 1-1 3-2 4-4-3-2-5-3-8-3-8 3-13 9-20 14l-1-1c1-1 1-2 2-3l1-1h0l2-3c0-1 2-1 2-3 2-2 4-3 6-5v-1l1-1h-1l-2-1 1-2c-4-3-7-4-12-4-10 0-17 3-24 9-3 3-5 7-8 11-4 6-7 12-7 20 0 6 3 10 7 14 5 5 11 8 18 8 4 0 8-1 11-2l1 1h-2l1 1 6-2 2-1 2 1c-3 3-8 5-12 7-6 3-12 5-18 6-9 2-19 4-27 9-3 2-6 7-7 11v1c-2 7 0 16 2 23 4 20 17 41 35 52 8 6 19 10 30 10 4 0 9-1 14-2-6 4-12 7-19 8-19 4-37-5-52-15-16-11-27-25-38-41-6-10-12-21-15-32-4-14-4-28-7-42 0-3-1-6-3-7-4 4-6 10-11 14-1 1-2 1-3 1 0-1-1-1 0-1 0-2 2-5 3-7 3-5 4-10 7-15 2-6 4-13 5-20 0-3-1-6 0-9s3-5 4-8c3-6 4-12 4-19 1 2 0 3 1 4v-2l1 1h3c0 1 0 1 1 2h0c0-4 1-9 0-13v-1-1l-1-2v-1l-1-1 1-1c-1-2-2-4-4-6h-1l1-1-1-1c-3 0-4-2-5-3 0-1 1-1 2-1 1-1 2 0 3 0h0c1-1 3-2 4-2 1-1 2-1 4-1 2-1 5-2 7-4 4-2 7-5 10-9l3-5 1-3v-3c1-4 0-6-2-9h0c-2 0-3-1-5-2 3-1 8-1 12-2h4c6 1 11 4 16 7l1 1 9 5 1-2h0c2 1 3 1 4 2 1 0 2 0 4-1 1-1 1-1 1-2l1-1 3-1v-3l-1-1h2l-1-1c-1-1-1-1-2-1l-1-2c-2 0-3-1-5-1l-2-2 2-2c1 0 3-1 4-1z" class="v"></path><path d="M181 301v6c1 1 1 3 1 4 0 0-1 1-1 2 0-1-1-2-1-3-1-1 0-3-1-4l-1 1c0-2 2-4 3-6z" class="g"></path><path d="M317 249c1 1 2 1 3 2l-1 3h1l1-1v1c-1 1-2 1-3 1h-1c0-1-1-2-1-3v-1l1-2z" class="T"></path><path d="M316 252l1-1 2 1c-1 2 0 2-2 3 0-1-1-2-1-3z" class="C"></path><path d="M325 229c0 1 0 1-1 1-1 1-2 1-3 1-2 0-1 0-2 1h0c-2 1-4 2-6 2l-2-1c2-1 3-1 4-2 1 0 4-1 5-1 2 0 3-1 5-1z" class="D"></path><path d="M275 228h0c4 0 8-1 12-1l1 2c-2 1-4 1-6 1-3-1-5 0-8-1l1-1z" class="C"></path><path d="M274 229l-1-1c-6-2-8-5-10-10l11 10h1l-1 1z" class="b"></path><path d="M317 249l1-2h2l1-1h1c1 1 1 3 2 4-1 2-2 3-3 4v-1l-1 1h-1l1-3c-1-1-2-1-3-2z" class="f"></path><path d="M223 328c-3-2-10-7-11-10l3 3h1c1 2 3 3 5 4h2c1 1 2 2 3 2h1 3c1 1 2 2 4 3v1c-4 0-7-3-11-3z" class="S"></path><path d="M310 226c3 0 6-1 10-2v1c-1 0-1 1-2 1 0 1 0 0 1 1v1h0c1 1 1 1 1 2-1 0-4 1-5 1l-2-1v-1l-2-1h-2c0-1 1-1 1-2z" class="j"></path><path d="M311 228h8c1 1 1 1 1 2-1 0-4 1-5 1l-2-1v-1l-2-1z" class="T"></path><path d="M223 328c4 0 7 3 11 3 4 1 8 3 12 5 5 1 10 1 14 1-13 2-26-2-37-9z" class="e"></path><path d="M287 227l23-1c0 1-1 1-1 2l-21 1-1-2z" class="L"></path><path d="M223 413c4 3 7 8 12 12 0 3 3 5 6 8h-1c-6-6-14-12-19-19l2-1z" class="S"></path><path d="M232 314l3 1c2 4 4 9 7 12l1 1c2 2 4 3 6 5l-10-3c-2-1-3-1-5-2h2 0v-1h3v-2c-1 0-1-1-2-2h0c-1-1-1-1-1-2v-1c-1 0-1-1-1-1l-1-1c0-1-1-2-2-4z" class="D"></path><path d="M311 233c-5 3-9 6-14 9-2 1-11 7-13 7l13-8c3-2 5-4 7-6 1-2 2-3 3-5h6l2 1c-1 1-2 1-4 2z" class="H"></path><path d="M215 320c-4-4-6-12-8-17h1 0 1s1 1 2 1c0 1 1 1 2 2v1h0v-1-1c-1-2 0-2-1-3l1-2v2l1 5c0 1 1 2 1 2v2h-1c0 2 0 3 1 4 0 1 1 2 1 3 0 0-1 1-1 2z" class="B"></path><path d="M208 389l15 24-2 1c-6-8-10-15-15-24l2-1z" class="u"></path><path d="M194 347c3 15 7 28 14 42l-2 1c-7-12-10-26-13-40 1-1 1-2 1-3z" class="V"></path><path d="M337 206h1c-3 3-7 5-10 8-1 1-3 3-4 3s-7-2-9-2c-5-1-11-1-17 0h0v-1h5 0c2-1 5-1 7 0h4 1c1 1 1 0 2 1h2l2 1h3l1-2h0c-3-1-6-2-9-2v-1h-5c-2-1-7 0-9 0v-1h2c1-1 2 0 3 0v-1c2 0 3 0 5-1 2 1 3 1 4 1h1 3l-4 2h1 2 0c2 1 4 1 6 2l1-1v1h2c1 0 3-2 4-3l1-1 1-1c2-1 2-1 3-2z" class="E"></path><path d="M330 217c1-2 2-2 3-3 1-2 3-3 4-5h1c1 0 0 0 1-1l1-1c2-1 3-2 4-3l2-1h0l2-1c2-2 4-2 6-4 0-1 1-2 2-2h1v2c0 1-1 1-1 2-1 2-1 3-3 4l-3 3h-1l2-2c0-1 0-1 1-1l2-2c0-1 0-1-1-2-2 1-4 2-6 5 0 1 1 2 2 3l-1 1h1c1-1 1-1 2-1 1-1 2-1 4-1-2 2-5 4-8 5v-1c-1-1-1-1 0-2-1-2-1-2-2-3-5 2-8 5-12 8l-3 3z" class="g"></path><path d="M339 222l1 1 1 1h0c1-1 1 0 2-1l1 1c-2 1-3 2-4 5l-2 3c0-1-1-2-1-3-3 0-4 0-6 1l-12 2h0c1-1 0-1 2-1 1 0 2 0 3-1 1 0 1 0 1-1s2-2 2-2c2 0 3-1 4-2l6-1 2-2z" class="E"></path><path d="M340 223l1 1h0c1-1 1 0 2-1l1 1c-2 1-3 2-4 5l-2 3c0-1-1-2-1-3h1v-1h-2v-1-2c2 0 3-1 4-2h0z" class="R"></path><path d="M181 301l1-3 1-1c1-2 1-3 1-5 0-1 0 0 1-1l2-4h0l-3 11h1c2 3 1 5 0 8 0 3 0 6 1 8 0 1 0 2-1 4h-1c-1 0-1-1-2-2l-1-3c0-1 1-2 1-2 0-1 0-3-1-4v-6z" class="D"></path><path d="M217 300v-4c-1-2 0-3-1-4-1 1-1 2-1 3l-1 1v2h0l-1-1v2l-1 1v-2l-1-1v-1-1h0l-1 1c-1-2 0-3 0-5h0l1-1h1v-1 1h0c1 2 1 3 2 5v-2-1-2-2c0-1 0-2 1-3 2 3 1 5 2 8v-5l1-1c1 2 0 6 1 9v2l1 1 1-1 2 2c-1 1-1 2-1 2 0 2 1 3 1 5l-1-1v2-1c-1-2-2-3-2-5v-1l-2 2c0-1 0-2-1-2v-1z" class="g"></path><path d="M235 425c7 5 14 11 22 16 2 1 5 3 7 4s5 2 7 3c1 0 2 0 4 1h1c1 0 2 0 3 1 2 0 6-1 8 0-2 1-7 1-9 1l-1-1c-2 0-3 0-5-1-9-2-18-6-26-11-2-1-4-3-6-5h1c-3-3-6-5-6-8z" class="E"></path><path d="M306 258c1-1 2-1 4-2h3 4v1l-1 2-6 6-3 4-1 1-2-5h-1c-1 1-2 2-2 3h0c-1 2-1 5-1 6v1 1c-2 1-4 2-5 3v-1l1-1 2-2h1l-1-2v-1c1-1 1-1 1-3l1-3c1-1 1-1 1-2v-1l5-5z" class="f"></path><path d="M306 258c1-1 2-1 4-2h3 4v1l-1 2v-1h0s-1-1-2-1c-2 0-2 1-4 3v-1l-2 2v1h-1c-1 0-1 0-2 1v1l-1-1c0-1 1-2 2-3v-1-1z" class="D"></path><path d="M213 302c1 0 1 0 2 1 0 2 2 4 3 4 0 2-1 2 0 4 1 1 1 2 2 3l3 3c0 1 1 1 1 2 2 3 5 5 6 8h-3-1c-1 0-2-1-3-2h-2c-2-1-4-2-5-4l-1-1c0-1 1-2 1-2 0-1-1-2-1-3-1-1-1-2-1-4h1v-2s-1-1-1-2l-1-5z" class="H"></path><path d="M223 325l-2-2 1-2 5 6h-1c-1 0-2-1-3-2z" class="I"></path><path d="M221 325c-2-3-2-3-3-6l2-2h0l1 1c0 1 1 2 1 3l-1 2 2 2h-2z" class="e"></path><path d="M255 181l2-1 4 2 1-1h1c1 1 2 1 2 2h1c1 1 1 2 2 3 2 0 2 1 2 1 1 1 2 0 3 1l-2 1h1l3-1 1 1h0 3c-6 3-12 5-18 8-4 2-9 5-14 8 1-2 2-2 3-3v-1-1c1 0 2 0 4-1 1-1 1-1 1-2l1-1 3-1v-3l-1-1h2l-1-1c-1-1-1-1-2-1l-1-2c-2 0-3-1-5-1l-2-2 2-2c1 0 3-1 4-1z" class="n"></path><path d="M265 183h1c1 1 1 2 2 3l-3 1v-4z" class="M"></path><path d="M260 191l3 1-4 3v-3l-1-1h2z" class="d"></path><path d="M260 191l2-2h2c2 0 5 0 7-1h0l-1 1c-2 0-4 2-7 3l-3-1z" class="V"></path><path d="M255 181c3 1 6 2 7 4h0c-1 2-1 2-3 2h-3c-2 0-3-1-5-1l-2-2 2-2c1 0 3-1 4-1z" class="l"></path><path d="M333 214c4-3 7-6 12-8 1 1 1 1 2 3-1 1-1 1 0 2v1l-1 1v1h2 0l-1 1v1c-1 2-3 3-4 4-1 0-3 2-4 2l-2 2-6 1c-1 1-2 2-4 2 0 0-2 1-2 2-2 0-3 1-5 1 0-1 0-1-1-2h0v-1c-1-1-1 0-1-1 1 0 1-1 2-1v-1c0-1 3-1 4-2 2-1 4-4 6-5l3-3z" class="Z"></path><path d="M333 214l1 1h1c2-1 4 0 6-2h1l1 1c-4 2-11 2-14 6 0 1 2 1 3 2l7-3c-1 1-3 2-4 3 0 1 1 1 2 2l-6 1c-1 1-2 2-4 2 0 0-2 1-2 2-2 0-3 1-5 1 0-1 0-1-1-2h0v-1c-1-1-1 0-1-1 1 0 1-1 2-1v-1c0-1 3-1 4-2 2-1 4-4 6-5l3-3z" class="L"></path><path d="M319 227l16-5c0 1 1 1 2 2l-6 1c-1 1-2 2-4 2 0 0-2 1-2 2-2 0-3 1-5 1 0-1 0-1-1-2h0v-1z" class="W"></path><path d="M319 228c4 0 8-2 12-3-1 1-2 2-4 2 0 0-2 1-2 2-2 0-3 1-5 1 0-1 0-1-1-2h0z" class="C"></path><defs><linearGradient id="w" x1="358.928" y1="201.466" x2="365.801" y2="205.265" xlink:href="#B"><stop offset="0" stop-color="#5b4b4a"></stop><stop offset="1" stop-color="#635e5d"></stop></linearGradient></defs><path fill="url(#w)" d="M378 186c1 1 2 1 3 1l1-1 2 1c-1 1-2 2-4 2l-2 1c-4 2-6 4-8 8-1 2-2 4-2 6l-1 2h-1l-1-1c-1 1-1 1-1 2l-1 1h-1v-1l-3 3v1c-3 3-7 4-10 7l-5 2h-1c1-1 3-2 4-4v-1l1-1h0-2v-1l1-1c3-1 6-3 8-5h0l3-3c4-5 6-10 7-17l1 2v-1c1 0 2-1 3-1h1 2c2 0 4-1 6-1z"></path><path d="M358 204h1c1-1 2-1 4-2-2 3-4 5-6 7l-2-2 3-3z" class="u"></path><path d="M355 207h0l2 2c-3 3-6 5-10 7v-1l1-1h0-2v-1l1-1c3-1 6-3 8-5z" class="q"></path><path d="M358 204c4-5 6-10 7-17l1 2v-1c1 3 1 6 0 9l-3 5c-2 1-3 1-4 2h-1z" class="k"></path><path d="M378 186c1 1 2 1 3 1l1-1 2 1c-1 1-2 2-4 2l-2 1h-1c-1-1-2 0-3 0v1c-1 0-1 0-2-1h-2c-2-1-1-1-1-3h1 2c2 0 4-1 6-1z" class="C"></path><path d="M369 187h1l1 1h3c0 1 0 0-1 1l-1 1h-2c-2-1-1-1-1-3zM226 313v-2h0 2-1c0-1 0-2-1-2v-1-2c0-2-1-4-1-5v-1c-1-2-1-5 0-6v-1-2c1 2 1 7 1 10v1c0 2 1 3 1 6 1 1 1 1 2 3v-1c-1-2-1-3-1-5v-1-3c-1-2-2-2-1-3 0-1 0-1 1-1 0-2-1-2 0-4 0 0 1 0 2 1 1-1 1-1 2-1v-2h1c0 2-2 3-1 4v7c0 5 1 9 3 13l-3-1c1 2 2 3 2 4l1 1s0 1 1 1v1c0 1 0 1 1 2h0c1 1 1 2 2 2v2h-3v1h0-2c-1-1-1-2-2-2h-2c1 1 3 2 4 4-2-1-3-2-4-3-1-3-4-5-6-8 0-1-1-1-1-2l-3-3c-1-1-1-2-2-3-1-2 0-2 0-4v-1c0-1 0-1 2-2l-2-1c-1 1 0 1-1 2l-1-1v-1-1c0-1 1-1 1-2v1c1 0 1 1 1 2l2-2v1c0 2 1 3 2 5v1l3 3-1 1 1 1h1 0z" class="B"></path><path d="M234 318v1h-1v-2h-1l-1-1v-2l-1-1 1-1c1 1 1 1 1 2h0c1 2 2 3 2 4zm-17-18v1c1 0 1 1 1 2l2-2v1c0 2 1 3 2 5v1l3 3-1 1 1 1h1 0 2c0 1 1 1 1 3 0 1 2 2 3 3 0 1 0 2 1 2l2 2h0c1 1 2 2 2 3-1 0-1 0-2 1h-1c-1-1-1-2-3-2 0-1-1-1-2-1v-1c0-1-4-4-5-5 0-3-3-6-5-8v-2l-1-1v-1c0-1 0-1 2-2l-2-1c-1 1 0 1-1 2l-1-1v-1-1c0-1 1-1 1-2z" class="C"></path><path d="M226 194l3 3c1 0 2 1 4 1v1l3-1h1l-2-2c-1 0-1 0-2-1 1 0 1 0 2-1l1 1 9 5 1-2h0c2 1 3 1 4 2v1 1c-1 1-2 1-3 3-11 10-21 22-32 33h-2v-2h0v-4l4-3 3-3 2-3 2-3c0-1 1-2 1-3v-1-3l1-1c0-3-1-5 1-7v-1c0-3 0-6-1-9v-1z" class="P"></path><path d="M229 210h3 1c-1-2 0-3 0-5h1c1 1 2 2 4 3l-8 8c0-2 0-3 1-4v-1h-2v-1z" class="I"></path><path d="M227 204c1 1 2 1 2 2s0 1-1 1c0 2 0 2 1 3v1h2v1c-1 1-1 2-1 4l-6 7-11 13v-4l4-3 3-3 2-3 2-3c0-1 1-2 1-3v-1-3l1-1c0-3-1-5 1-7v-1z" class="Q"></path><path d="M227 204c1 1 2 1 2 2s0 1-1 1c0 2 0 2 1 3v1h2v1c-1 1-1 2-1 4l-6 7c1-1 1-1 1-2 1-1 1-3 1-4 1-2 1-4 1-6v-6-1z" class="N"></path><path d="M226 194l3 3c1 0 2 1 4 1v1l3-1h1l-2-2c-1 0-1 0-2-1 1 0 1 0 2-1l1 1 9 5 2 1c-1 1-2 1-3 2l-6 5c-2-1-3-2-4-3h-1c0 2-1 3 0 5h-1-3c-1-1-1-1-1-3 1 0 1 0 1-1s-1-1-2-2c0-3 0-6-1-9v-1z" class="D"></path><path d="M235 199h2l1 1c-1 1-1 1-2 1l-1-2z" class="E"></path><path d="M236 195l9 5 2 1c-1 1-2 1-3 2l-3-3c-2-2-3-2-5-5z" class="m"></path><path d="M226 194l3 3c1 1 2 1 3 2l-1 1h-1l1 1 3 4h-1c0 2-1 3 0 5h-1-3c-1-1-1-1-1-3 1 0 1 0 1-1s-1-1-2-2c0-3 0-6-1-9v-1z" class="B"></path><path d="M219 187c6 1 11 4 16 7-1 1-1 1-2 1 1 1 1 1 2 1l2 2h-1l-3 1v-1c-2 0-3-1-4-1l-3-3v1c1 3 1 6 1 9v1c-2 2-1 4-1 7l-1 1v3 1c0 1-1 2-1 3l-2-2c0-1 0-3-1-4h0v1h-2c0-1-2-2-3-2-2 0-5 2-7 3h0c1-1 2-2 2-3l-2 1-1-1v-1l-2-1 3-5 1-3v-3c1-4 0-6-2-9h0c-2 0-3-1-5-2 3-1 8-1 12-2h4z" class="O"></path><path d="M216 197v1l-1 2c0 1 0 2-1 3-1 0-1 1-1 2l-1 1v-4c1-1 1-2 1-3l3-2z" class="H"></path><path d="M209 206l3-4v4c-2 1-2 4-4 6l-2-1 3-5z" class="B"></path><path d="M222 198h1c0-1 1-2 2-2v7h0l-1-1-1 1h0 1v1c-1 0-2 0-2 1l-1 1c-1-1-2-1-3-2h0 2v-1-1l2-1v-3h0z" class="D"></path><g class="I"><path d="M225 196l1-1c1 3 1 6 1 9v1c-2 2-1 4-1 7-1-1-1-2-1-3h-2-1c0-1 0-1 1-2v-1l-1-1c0-1 1-1 2-1v-1h-1 0l1-1 1 1h0v-7z"></path><path d="M222 209h1 2c0 1 0 2 1 3l-1 1v3 1c0 1-1 2-1 3l-2-2c0-1 0-3-1-4h0v1h-2c1-1 1-2 2-3h-3v-1c1 0 1 0 2-1 1 0 1 0 2-1z"></path></g><path d="M222 209h1 2c0 1 0 2 1 3l-1 1h-1l-2-1 1-2-1-1z" class="B"></path><path d="M219 187c6 1 11 4 16 7-1 1-1 1-2 1 1 1 1 1 2 1l2 2h-1l-3 1v-1c-2 0-3-1-4-1l-3-3v1l-1 1c-1 0-2 1-2 2h-1v-1c-1 1-2 1-3 2l-1-1h-1-1v-1l-3 2c0 1 0 2-1 3l-3 4 1-3v-3c1-4 0-6-2-9h0c-2 0-3-1-5-2 3-1 8-1 12-2h4z" class="l"></path><path d="M215 191c3 0 6 0 9 1l2 2v1l-1 1c-1 0-2 1-2 2h-1v-1c-1 1-2 1-3 2l-1-1h-1v-1-5l-2-1h0z" class="C"></path><path d="M218 198c1-1 2-1 3-2s2-2 3-4l2 2v1l-1 1c-1 0-2 1-2 2h-1v-1c-1 1-2 1-3 2l-1-1z" class="H"></path><path d="M208 191h1c1-1 4-1 6 0h0l2 1v5 1h-1v-1l-3 2c0 1 0 2-1 3l-3 4 1-3v-3c1-4 0-6-2-9z" class="E"></path><path d="M213 199v-4h1 1v2h1l-3 2z" class="B"></path><path d="M215 191l2 1v5 1h-1v-1h-1v-2h-1l1-1c-1 0-1 0-2-1 0-1 1-1 2-2z" class="T"></path><path d="M206 211l2 1v1l1 1 2-1c0 1-1 2-2 3h0c2-1 5-3 7-3 1 0 3 1 3 2h2v-1h0c1 1 1 3 1 4l2 2-2 3-2 3-3 3-4 3v4h0c-2 1-4 0-6 0-2-1-8-3-9-2-4-1-8-3-12-4-1 0-1 1-2 1-1-1-1-2-2-1 1 1 1 2 2 2 2 0 2 1 3 2v1c-2 0-4-2-5-3l-1-1c-3 0-4-2-5-3 0-1 1-1 2-1 1-1 2 0 3 0h0c1-1 3-2 4-2 1-1 2-1 4-1 2-1 5-2 7-4 4-2 7-5 10-9z" class="v"></path><path d="M221 215v-1h0c1 1 1 3 1 4l2 2-2 3-2 3-3 3c-1-1-1-3 0-4l2-2c-3 1-4 1-7 0-1 0-2-1-3-2l1-1 2 2c1 0 2 1 3 1 2 0 3-1 4-2 2-1 2-4 2-6z" class="m"></path><path d="M222 218l2 2-2 3-2-1c1-1 2-3 2-4z" class="U"></path><path d="M206 211l2 1v1l1 1 2-1c0 1-1 2-2 3h0c-2 2-8 4-11 5l-1 1h-1c-2 1-4 2-6 2l-5 2c-1 0-1 1-2 1h-1-1 0c1-1 3-2 4-2 1-1 2-1 4-1 2-1 5-2 7-4 4-2 7-5 10-9z" class="H"></path><path d="M207 221h1l1-1h1l-1 1c1 1 2 2 3 2 3 1 4 1 7 0l-2 2c-1 1-1 3 0 4l-4 3v4h0c-2 1-4 0-6 0-2-1-8-3-9-2-4-1-8-3-12-4l-1-1h0c2 0 5 0 7-1s3-1 5-2c1 0 2-1 3-2 1 0 3 0 4-1s1-1 3-2z" class="O"></path><path d="M192 228c2-1 3-1 5-2l4 1-1 1c-1 0-1 1-1 1l-2-1-1 1c-1-1-2-1-4-1z" class="c"></path><path d="M207 221h1l1-1h1l-1 1c1 1 2 2 3 2 0 1-1 2-1 3-1-1-1 0-1-1-1 0-1-1-2 0-1-1-1-2-1-4z" class="B"></path><path d="M197 226c1 0 2-1 3-2 1 0 3 0 4-1s1-1 3-2c0 2 0 3 1 4-1 1-1 0-3 1-1 1-3 1-4 1h0l-4-1z" class="E"></path><path d="M207 236c2-2 2-2 2-4l-3-2h0l2-1c3 0 5-1 7-4h2c-1 1-1 3 0 4l-4 3v4h0c-2 1-4 0-6 0z" class="U"></path><defs><linearGradient id="x" x1="170.656" y1="318.989" x2="231.445" y2="266.052" xlink:href="#B"><stop offset="0" stop-color="#aa807c"></stop><stop offset="1" stop-color="#dab2a3"></stop></linearGradient></defs><path fill="url(#x)" d="M187 234c-1-1-1-2-3-2-1 0-1-1-2-2 1-1 1 0 2 1 1 0 1-1 2-1 4 1 8 3 12 4 1-1 7 1 9 2 2 0 4 1 6 0v2h2c-6 8-10 17-14 26-3 7-6 13-7 20-4 14-5 30-3 44l3 19c0 1 0 2-1 3l-3-12c-2-11-3-23-2-35 1-7 2-15 4-23 0-1 0-3 1-5 1-1 1-2 1-3l1-1c1-2 5-13 5-15 0 0 0-1-1-1-2-2-3-7-5-9l-4-8c-1 0-3-2-3-3v-1z"></path><defs><linearGradient id="y" x1="194.138" y1="228.188" x2="199.06" y2="251.903" xlink:href="#B"><stop offset="0" stop-color="#020201"></stop><stop offset="1" stop-color="#252526"></stop></linearGradient></defs><path fill="url(#y)" d="M187 234c-1-1-1-2-3-2-1 0-1-1-2-2 1-1 1 0 2 1 1 0 1-1 2-1 4 1 8 3 12 4 1-1 7 1 9 2 2 0 4 1 6 0v2l-1 2-1-1h-1l-10 16v-1c-1-3-4-6-5-9-2-2-3-5-5-7-1 0-3-2-3-3v-1z"></path><path d="M198 234c1-1 7 1 9 2 2 0 4 1 6 0v2l-1 2-1-1h-1c-2-2-9-4-12-5z" class="h"></path><path d="M187 234l1 1c1 0 1 0 2 1 1 0 2 1 3 2h1c0 2 0 2 1 4v1 2h0c-2-2-3-5-5-7-1 0-3-2-3-3v-1z" class="g"></path><defs><linearGradient id="z" x1="163.881" y1="258.296" x2="190.411" y2="275.902" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2a2828"></stop></linearGradient></defs><path fill="url(#z)" d="M182 232c1 1 3 3 5 3 0 1 2 3 3 3l4 8c2 2 3 7 5 9 1 0 1 1 1 1 0 2-4 13-5 15l-1 1c0 1 0 2-1 3-1 2-1 4-1 5-4 1-8 7-11 10 0 1-1 1-2 2-1 2-3 4-4 6-2 4-4 10-7 12 2-6 4-13 5-20 0-3-1-6 0-9s3-5 4-8c3-6 4-12 4-19 1 2 0 3 1 4v-2l1 1h3c0 1 0 1 1 2h0c0-4 1-9 0-13v-1-1l-1-2v-1l-1-1 1-1c-1-2-2-4-4-6h-1l1-1z"></path><path d="M194 246c2 2 3 7 5 9 0 1-1 2-2 4h0c0-1 0-1 1-2l-1-2c0-1 0-1-1-2-1-2-2-4-2-7z" class="f"></path><path d="M175 298c0-2 1-3 1-5 1-3 2-5 4-7 0 2 0 4-1 5v1c-1 2-3 4-4 6z" class="H"></path><path d="M180 286c0-3 3-7 5-9h0c1-1 3-3 4-3 0 1-1 2-2 2-1 2-2 3-3 5 0 1 0 1-1 2l-3 3z" class="C"></path><path d="M189 274h1 1l2-1v-1h1c0 1 0 2-1 3l-9 6c1-2 2-3 3-5 1 0 2-1 2-2z" class="D"></path><path d="M184 281l9-6c-1 2-1 4-1 5-4 1-8 7-11 10 0 1-1 1-2 2v-1c1-1 1-3 1-5h0l3-3c1-1 1-1 1-2z" class="B"></path><path d="M180 286l3-3c0 2-1 3-1 4v1c-1 1-1 0-1 2 0 1-1 1-2 2v-1c1-1 1-3 1-5h0z" class="O"></path><path d="M384 187v1c2 1 3 1 4 4 0 0 1 1 1 2l1 2c-1 2-2 3-2 5h1l1-1 1-2v-2h1c1 0 1 1 1 2s1 2 2 3c0 2 0 4 1 7v2h0l1 8c0 2 1 4 1 6l-1 3h-1c0-2 0-3-1-5h0c-1 1-1 2-1 3l-1 6-2 3h0c0 3-2 5-2 7h-2v4l-2 3c-1 2-2 3-5 3 1 1 0 2 0 4-1 1-1 2-1 4h0c-1 3-2 7-3 10l2 5c0 1 0 1-1 3l1 1 1-1 1 2-1 1v2c-1-3-3-6-5-9h0c-1-3-2-4-1-6h-4l-1 1c-2-1-4-1-7-2-4 0-7 0-11 1l-10 5-8 4c-2 2-6 5-9 6h0l-1 1h-1l1-1c-2 1-4 3-6 4h-2c-3 1-5 3-8 4h0c0-1 1-1 1-2 1-1 3-2 4-4 0-1 1-2 1-3 7-9 17-16 24-26 4-6 6-12 4-19l-2-4 2-3c1-3 2-4 4-5l-1-1c-1 1-1 0-2 1h0l-1-1-1-1c1 0 3-2 4-2h1l5-2c3-3 7-4 10-7v-1l3-3v1h1l1-1c0-1 0-1 1-2l1 1h1l1-2c0-2 1-4 2-6 2-4 4-6 8-8l2-1c2 0 3-1 4-2z" class="E"></path><path d="M338 261l2-2h4l-4 4-2-2z" class="k"></path><path d="M367 217c-1 4-2 9-5 12h0l-5 9c-2 3-3 6-5 8l2-4c1-2 2-5 3-7l10-18z" class="C"></path><path d="M352 264h2c-1 1-2 2-3 2h-1 0v1l-10 5v-2c4-4 7-6 12-6z" class="H"></path><path d="M334 266l4-5 2 2-26 23c-3 1-5 3-8 4h0c0-1 1-1 1-2h1c2-1 4-2 5-3 4-3 8-7 12-11 3-2 7-4 9-8z" class="X"></path><path d="M352 238l1-1c1 1 0 2 0 3l1 2-2 4-8 13h-4l10-18 2-3z" class="P"></path><path d="M312 281h1 0c5-1 6-6 10-8 2-1 3-4 6-6 1-1 3-1 5-1-2 4-6 6-9 8-4 4-8 8-12 11-1 1-3 2-5 3h-1c1-1 3-2 4-4 0-1 1-2 1-3z" class="G"></path><path d="M353 227v2c0 2 0 4-1 5 2-1 2-4 4-6s5-6 6-8v-1c1-1 1 0 3 0 0-2 1-3 1-4h1v2l-10 18c-1 2-2 5-3 7l-1-2c0-1 1-2 0-3l-1 1-2 3 1-3v-2h-3-1v-1h4c0-1 1-1 1-2 0-2 1-4 1-6z" class="q"></path><path d="M352 238h0c1-3 3-8 5-9h1c-1 2-1 4-1 6-1 2-2 5-3 7l-1-2c0-1 1-2 0-3l-1 1z" class="M"></path><defs><linearGradient id="AA" x1="325.652" y1="256.292" x2="353.651" y2="242.31" xlink:href="#B"><stop offset="0" stop-color="#675958"></stop><stop offset="1" stop-color="#816c69"></stop></linearGradient></defs><path fill="url(#AA)" d="M340 229c1 1 2 2 3 4 0 1 0 1 1 2h3v1h1 3v2l-1 3-10 18-2 2-4 5c-2 0-4 0-5 1-3 2-4 5-6 6-4 2-5 7-10 8h0-1c7-9 17-16 24-26 4-6 6-12 4-19l-2-4 2-3z"></path><path d="M340 229c1 1 2 2 3 4 0 1 0 1 1 2h3v1c-2 1-4 1-7 0l-2-4 2-3z" class="G"></path><path d="M359 211v-1l3-3v1h1l1-1c0-1 0-1 1-2l1 1c0 1-1 2-1 2 1 2 1 2 3 2v5h-1-1c0 1-1 2-1 4-2 0-2-1-3 0v1c-1 2-4 6-6 8s-2 5-4 6c1-1 1-3 1-5v-2c0 2-1 4-1 6 0 1-1 1-1 2h-4-3c-1-1-1-1-1-2-1-2-2-3-3-4 1-3 2-4 4-5l-1-1c-1 1-1 0-2 1h0l-1-1-1-1c1 0 3-2 4-2h1l5-2c3-3 7-4 10-7z" class="d"></path><path d="M339 222c1 0 3-2 4-2h1l-2 2c1 0 1 0 2-1 1 0 2 0 2 1h3c-2 1-4 1-5 2l-1-1c-1 1-1 0-2 1h0l-1-1-1-1z" class="Q"></path><path d="M365 208c1 2 1 2 3 2l-2 2c0 1 1 1 0 2l-1-2c-2 1-2 3-2 5l-1-1h-1v-1 1c-1 1-1 1-3 2l7-10z" class="G"></path><path d="M359 211v-1l3-3v1h1l1-1c0-1 0-1 1-2l1 1c0 1-1 2-1 2l-7 10-2 4-1 1h-1c-1-1-1-3-1-4v-1h0-4 0c3-3 7-4 10-7z" class="F"></path><path d="M359 211h0c-1 3-4 4-5 6h0 1l1 1v2l-2 1 2 1-1 1h-1c-1-1-1-3-1-4v-1h0-4 0c3-3 7-4 10-7z" class="j"></path><path d="M349 222l3 2h0 1v3c0 2-1 4-1 6 0 1-1 1-1 2h-4-3c-1-1-1-1-1-2-1-2-2-3-3-4 1-3 2-4 4-5 1-1 3-1 5-2z" class="l"></path><path d="M352 224h1v3c0 2-1 4-1 6 0 1-1 1-1 2h-4-3c-1-1-1-1-1-2l3 1c1 0 2-1 3-2 3-2 3-5 3-8z" class="a"></path><path d="M360 254l-1-2c0-2 1-2 2-4 0-3 2-7 3-11 0-1 1-1 1-2 1-3 2-5 2-8l1-3c0-2 1-4 2-4v2 2 1c-1 2-1 4-1 7 1 1 0 2 1 3v6l1 2 1 3c0 2 1 5 0 7h0v1l1 1 2-2c0-2 1-7 0-9-1-1-1-1-1-2-1-3-1-5-1-8v-1c1 2 1 3 2 5h3v1l1-3 1 1c1-1 1-2 1-3l1 2h2c0 2 1 3 2 4s1 3 1 5l-2 3c-1 2-2 3-5 3 1 1 0 2 0 4-1 1-1 2-1 4h0c-1 3-2 7-3 10l2 5c0 1 0 1-1 3l1 1 1-1 1 2-1 1v2c-1-3-3-6-5-9h0c-1-3-2-4-1-6h-4l-1 1c-2-1-4-1-7-2-4 0-7 0-11 1v-1h0 1c1 0 2-1 3-2h-2c1-2 1-4 3-5h1c1-1 1-2 2-3 0-1 1-2 2-2z" class="c"></path><path d="M360 254c1-1 1-2 2-3 0-2 1-2 2-4 0-1 0-2 1-4v-1h3v1 1l2 1c-1 1 0 2-1 3v6c-1 1-1 2-2 2-2 0-3 1-4 2h-1l-2 1c-2-1-2-2-2-3s1-2 2-2z" class="O"></path><path d="M366 254l1-2 1 1c-1 1-1 2-1 3-2 0-3 1-4 2h-1l1-2 3-2z" class="C"></path><path d="M363 256h-2 0l-1-1 1-1h2v-3c0-1 0 0 1-1v1l2-2h0v5l-3 2z" class="I"></path><path d="M359 262c2-1 3-1 4-1l1 1c1-1 2-1 3-1h3v-1-1l-1-1v-1c0-1 1-2 2-3v4h0c-1 1-1 2 0 2s1 0 2 1c1-1 1-2 1-3l2-2c0 4-1 7-2 10l-1 1h-4l-1 1c-2-1-4-1-7-2-4 0-7 0-11 1v-1h0 1c1 0 2-1 3-2s4-1 5-2z" class="L"></path><path d="M365 263c3 0 6 2 9 3l-1 1h-4l-1 1c-2-1-4-1-7-2v-1l4-2z" class="M"></path><path d="M359 262c2 0 4 1 6 1l-4 2v1c-4 0-7 0-11 1v-1h0 1c1 0 2-1 3-2s4-1 5-2z" class="a"></path><defs><linearGradient id="AB" x1="384.715" y1="253.529" x2="370.32" y2="257.515" xlink:href="#B"><stop offset="0" stop-color="#c09793"></stop><stop offset="1" stop-color="#dcd7cc"></stop></linearGradient></defs><path fill="url(#AB)" d="M379 236l1 1c1-1 1-2 1-3l1 2h2c0 2 1 3 2 4s1 3 1 5l-2 3c-1 2-2 3-5 3 1 1 0 2 0 4-1 1-1 2-1 4h0c-1 3-2 7-3 10l2 5c0 1 0 1-1 3l1 1 1-1 1 2-1 1v2c-1-3-3-6-5-9h0c-1-3-2-4-1-6l1-1c1-3 2-6 2-10l2-17 1-3z"></path><path d="M382 236h2c0 2 1 3 2 4s1 3 1 5l-2 3c-1 2-2 3-5 3-1-6 0-10 2-15z" class="E"></path><path d="M386 240c1 1 1 3 1 5l-2 3c-1 0-2-1-2-1-1-2-2-5-1-7h4z" class="g"></path><path d="M384 187v1c2 1 3 1 4 4 0 0 1 1 1 2l1 2c-1 2-2 3-2 5h1l1-1 1-2v-2h1c1 0 1 1 1 2s1 2 2 3c0 2 0 4 1 7v2h0l1 8c0 2 1 4 1 6l-1 3h-1c0-2 0-3-1-5h0c-1 1-1 2-1 3l-1 6-2 3h0c0 3-2 5-2 7h-2v4c0-2 0-4-1-5s-2-2-2-4h-2l-1-2c0 1 0 2-1 3l-1-1-1 3v-1h-3c-1-2-1-3-2-5h0c-1-4-1-7-2-10 0-2 1-6-1-8h-2v-5c-2 0-2 0-3-2 0 0 1-1 1-2h1l1-2c0-2 1-4 2-6 2-4 4-6 8-8l2-1c2 0 3-1 4-2z" class="d"></path><path d="M382 206l1-1c1 1 1 1 2 3-1 1-1 1-2 3l-1 3h-2 0 0-2c-1 1-3 2-3 4-1-1-1 0-1-1l3-4 2-2c1-1 1-2 1-3l2-2z" class="I"></path><path d="M382 206l1-1c1 1 1 1 2 3-1 1-1 1-2 3l-1-1-1-1 1-2v-1z" class="B"></path><path d="M380 214v1 3 1l-1 1c-1 3 0 6-3 9l-2-2c0-3 1-5 2-8 1-2 2-3 4-5h0z" class="V"></path><path d="M366 206h1l1 6c2 1 3 2 5 2v-1l1 1c1-1 2-1 3-1l-3 4-1-1v1l-1 6 1 1v1 1 5 2c-1-4-1-7-2-10 0-2 1-6-1-8h-2v-5c-2 0-2 0-3-2 0 0 1-1 1-2z" class="R"></path><path d="M374 227l2 2c3-3 2-6 3-9l1-1c0 1 1 1 2 2-1 4-2 10-3 15l-1 3v-1h-3l2-2-1-1v1l-1-1 1-1c-2-2-2-4-2-7z" class="W"></path><path d="M370 198c1-1 2-1 3-2 1 0 1-1 1 0l3 3v1l1 3h3c1 1 1 2 1 3l-2 2c0 1 0 2-1 3l-2 2c-1 0-2 0-3 1l-1-1v1c-2 0-3-1-5-2l-1-6 1-2c0-2 1-4 2-6z" class="v"></path><path d="M378 203h3c1 1 1 2 1 3l-2 2c0 1 0 2-1 3l-2 2c-1 0-2 0-3 1l-1-1h0c4-3 5-5 5-10z" class="G"></path><path d="M384 187v1c2 1 3 1 4 4 0 0 1 1 1 2l1 2c-1 2-2 3-2 5l-1 2-1 4-1 1c-1-2-1-2-2-3l-1 1c0-1 0-2-1-3h-3l-1-3v-1l-3-3c0-1 0 0-1 0-1 1-2 1-3 2 2-4 4-6 8-8l2-1c2 0 3-1 4-2z" class="g"></path><path d="M379 193l1-1 3 2-1 1h0l-1 1c-1-1-1-2-2-3z" class="E"></path><path d="M384 193h1v3h2l-1 2h-3l-2-1v-1l1-1h0l1-1 1-1z" class="B"></path><path d="M379 193c1 1 1 2 2 3v1c0 1 0 2-1 3h-1-2v-1c0-3 0-4 2-6z" class="T"></path><path d="M384 188c2 1 3 1 4 4 0 0 1 1 1 2l1 2h-3-2v-3h-1l1-2-1-3z" class="E"></path><path d="M387 196h3c-1 2-2 3-2 5l-1 2-1 4-1 1c-1-2-1-2-2-3l-1 1c0-1 0-2-1-3h-3l-1-3h2 1c1-1 1-2 1-3l2 1h3l1-2z" class="I"></path><path d="M387 196h3c-1 2-2 3-2 5l-1 2h-1l-1 1-1-1-1-5h3l1-2z" class="H"></path><path d="M387 196h3c-1 2-2 3-2 5l-1 2h-1v-5l1-2z" class="N"></path><path d="M391 198v-2h1c1 0 1 1 1 2s1 2 2 3c0 2 0 4 1 7v2h0l1 8c0 2 1 4 1 6l-1 3h-1c0-2 0-3-1-5h0c-1 1-1 2-1 3l-1 6-2 3h0c0 3-2 5-2 7h-2v4c0-2 0-4-1-5s-2-2-2-4h-2l-1-2c0 1 0 2-1 3l-1-1c1-5 2-11 3-15-1-1-2-1-2-2v-1-3-1h0 2l1-3c1-2 1-2 2-3l1-1 1-4 1-2h1l1-1 1-2z" class="P"></path><path d="M384 222c1 2 1 3 2 4-1 1-2 2-2 4 0 1 0 1-1 2h0-1l2-10z" class="I"></path><path d="M385 208l1-1c-1 5-3 9-4 14-1-1-2-1-2-2v-1-3-1h0 2l1-3c1-2 1-2 2-3z" class="m"></path><path d="M390 200l1-2c0 3 0 5 1 8-1 1-2 2-2 3v1 3l-3 3h-1c0-6 2-11 4-16z" class="L"></path><path d="M390 210h1v6l-1 5c-1-1-2-1-3-1 0 2 0 4-1 6-1-1-1-2-2-4l2-6h1l3-3v-3z" class="N"></path><path d="M390 210h1v6l-1 5c-1-1-2-1-3-1l1-1c1-2 1-4 2-6v-3z" class="Z"></path><path d="M391 198v-2h1c1 0 1 1 1 2s1 2 2 3c0 2 0 4 1 7v2h0l1 8c0 2 1 4 1 6l-1 3h-1c0-2 0-3-1-5h0l-1-3-3-3v-6h-1v-1c0-1 1-2 2-3-1-3-1-5-1-8z" class="P"></path><path d="M390 210v-1c0-1 1-2 2-3 1 4 2 8 2 13l-3-3v-6h-1z" class="e"></path><path d="M391 216l3 3 1 3c-1 1-1 2-1 3l-1 6-2 3h0c0 3-2 5-2 7h-2v4c0-2 0-4-1-5s-2-2-2-4h-2l-1-2 1-2h1 0c1-1 1-1 1-2 0-2 1-3 2-4 1-2 1-4 1-6 1 0 2 0 3 1l1-5z" class="I"></path><path d="M390 228s1 0 1 1c1 2 0 3 0 5h0c0 1-1 1-2 2h0-1v-2c0-1 0-2 1-3 0-1 0-2 1-3z" class="L"></path><path d="M390 223h0c1 1 1 2 1 3 1 0 2 0 3-1l-1 6-2 3c0-2 1-3 0-5 0-1-1-1-1-1l-1-1 1-4z" class="S"></path><path d="M391 216l3 3 1 3c-1 1-1 2-1 3-1 1-2 1-3 1 0-1 0-2-1-3h0v-2l1-5z" class="H"></path><path d="M387 220c1 0 2 0 3 1v2l-1 4c-2 2-4 4-5 7v2h-2l-1-2 1-2h1 0c1-1 1-1 1-2 0-2 1-3 2-4 1-2 1-4 1-6z" class="q"></path><path d="M479 189h1c2 1 4 0 7-1 4-1 9-2 14-3 20-1 42 3 59 12 8 4 15 9 22 14 1-1 2-1 3 0 3 1 5 3 7 5 3 2 6 6 9 7l-1 1c-1 0-3 0-4 1 2 2 4 4 6 7s4 7 6 9l3 3v-2c0-1 0-2 1-4l1 1c0 1 0 2 2 2h2l-1 2s-1 1-1 2c0 0 1 2 1 3 2 5 5 10 6 15 1 3 2 11 5 14v1c0 2 1 6 1 8 1 11 0 21-3 31l-4 17-3 8c0 1-1 2-1 3l-8 29c-1-5-1-10-1-16h0c1-10 4-18 8-28-3 2-7 9-9 12l-3 7-3 9c-1 3-1 7-2 11h-1v2h-1l-1 2v2c0 1-1 2-1 3-1 0-1 1-1 2v2c0 1 0 1 1 1 1 2 9 11 9 13l-23 85 2 2 1-1h0c1-1 3-1 4-2-3 4-6 7-9 10l-2 3h2 1c-1 2-3 3-5 5-2 1-3 3-5 4-1 1-3 1-5 2v-1l-4 1h-2c-4 2-9 2-13 3l1-1c-6 0-12 2-18 0-3 0-6 0-9-1-1-1-3-1-4-1-2 0-4-2-5-2l-2-3-3-1h-5l1 1 3 3-6-1c-1 0-2-1-4-1-1-1-1-1-3-1-1 0-4-2-6-3-6-3-12-6-18-10-5-3-9-7-13-11-3-3-6-6-8-10s-4-7-6-11c1-2-3-10-4-12l-7-21 1-2c-1-3-2-6-2-9 0 0-1-2-1-3v-4c-1-3-1-5-1-7-1-4-3-7-3-11l2-1c0 1 0 4 1 4l2 2c0-2-1-5-2-7l1-1-2-10c2 0 4 1 7 2h0l1-1c2 1 5 3 7 4l1-2 1-1c0-1 1-2 1-3l-2-1v-1c2 1 3 1 4 1h1 1 1 2 1 4l2 2 1-1c1 1 2 2 4 2-1-1-1-2-1-3l3 1h0 4v-3l-2-3h0l-7-25h2v-5c-1-2-2-6-4-8h-1l-3-16c-1-2-2-9-1-10l5-1c1 0 2 0 3-1-1-1-7-3-8-4l-1-1v-1l-2-2c-5-9-7-21-8-32h0l-1-3v-1c1-1 2-2 2-3-1-3-2-6-1-9 0-1 1-2 1-4v-5c0-4 2-8 3-12 3-7 8-14 14-19s13-9 19-14z" class="o"></path><path d="M582 211c1-1 2-1 3 0 3 1 5 3 7 5 3 2 6 6 9 7l-1 1c-1 0-3 0-4 1l-14-14z" class="p"></path><path d="M612 238l1 1c0 1 0 2 2 2h2l-1 2s-1 1-1 2c0 0 1 2 1 3 2 5 5 10 6 15 1 3 2 11 5 14v1c0 2 1 6 1 8 1 11 0 21-3 31-1-5 0-9 0-13 0-14-2-28-6-40-1-3-9-18-8-20v-2c0-1 0-2 1-4z" class="b"></path><path d="M612 238l1 1c0 1 0 2 2 2h2l-1 2-2 1h-1-1c0-1 0-1-1-2 0-1 0-2 1-4z" class="J"></path><defs><linearGradient id="AC" x1="561.184" y1="218.674" x2="523.684" y2="284.368" xlink:href="#B"><stop offset="0" stop-color="#bba188"></stop><stop offset="1" stop-color="#dbc6ac"></stop></linearGradient></defs><path fill="url(#AC)" d="M444 235c2-4 6-7 10-10 8-6 16-11 25-15 23-8 47-9 69 1 26 12 44 32 53 58 6 14 8 32 7 47-1 6-2 12-2 19 0-2 0-2-1-3-2-2-2-3-3-5h0v-1c1-1 1-1 1-2 2-5 2-10 2-14 0-20-6-41-16-58 0-3-2-5-4-6h-1v2h0c-1-1-2-1-3-1 1-2 0-3 0-5l-1-1h-2-1 2v-1c-2 0-3-1-5 0h0-1c2-2 5-2 7-2-3-2-7-6-10-9-5-4-11-8-17-11l-5-2-3-2c-8-3-16-5-24-6h-11-6c-3 1-5 1-8 2v4h-1v-3h-1c-1-1-3 0-5 0-5 2-11 4-16 6l-1 1c-1 1-2 2-3 2-2 1-3 2-5 4-4 2-8 4-11 7-3 2-6 5-8 7l-1-1v-2h0z"></path><path d="M521 208h6c5-1 10 1 14 3h2l-1 1h1c1 0 1 1 2 2-8-3-16-5-24-6z" class="J"></path><path d="M496 214v-4c3-1 5-1 8-2h6 11c8 1 16 3 24 6l3 2 5 2c6 3 12 7 17 11 3 3 7 7 10 9-2 0-5 0-7 2-3 1-6 3-9 3s-5-1-8-1c-3-1-5-1-8 0-5 1-8 6-13 7-1 0-2-1-3-1l-10-2c-4-1-9-1-13-2-1-1-1-2-1-3 0 1-1 2-2 3h-2c-4 0-9 0-13 1l-15 4-12 6c-4 2-7 5-11 7-4-2-8-5-12-6v-1c1-1 2-2 2-3-1-3-2-6-1-9 0-1 1-2 1-4v-5l1 1h0v2l1 1c2-2 5-5 8-7 3-3 7-5 11-7 2-2 3-3 5-4 1 0 2-1 3-2l1-1c5-2 11-4 16-6 2 0 4-1 5 0h1v3h1z" class="o"></path><path d="M548 216l5 2-3 2v-1c-1 0-2 0-3-1 1 0 1-1 1-2z" class="h"></path><path d="M504 244c3-5 2-9 2-14 1 1 1 3 2 5 0 1 1 3 0 4v2c0 1-1 2-2 3h-2z" class="p"></path><path d="M494 211h1v3h1 0c1 2 1 5 1 7s2 4 3 5l-1 1c-2-3-5-7-5-10v-2h0v-4z" class="r"></path><path d="M464 224c2-2 3-3 5-4 1 0 2-1 3-2l1 1h3l1 1c-1 1-1 1-2 1-2 0-3 0-4 1h-2c0 2 1 2-1 3-1-1-1-1-3-2l-1 1z" class="K"></path><path d="M464 224l1-1c-1 2-1 3-1 4l-1 1-1 4v4c1 4-4 9-4 13 0 1 2 4 3 4v1h1 1l6-3h2c2-1 3-2 5-2h0l-12 6c-4 2-7 5-11 7-4-2-8-5-12-6v-1c1-1 2-2 2-3-1-3-2-6-1-9 0-1 1-2 1-4v-5l1 1h0v2l1 1c2-2 5-5 8-7 3-3 7-5 11-7z" class="h"></path><path d="M573 240h1 0c2-1 3 0 5 0v1h-2 1 2l1 1c0 2 1 3 0 5 1 0 2 0 3 1h0v-2h1c2 1 4 3 4 6 10 17 16 38 16 58 0 4 0 9-2 14 0 1 0 1-1 2v1h0c1 2 1 3 3 5 1 1 1 1 1 3s0 5 1 7l-3 7-3 9c-1 3-1 7-2 11h-1v2h-1l-1-5c0-2-1-5-2-6s-1-2-2-4c-1-5-2-11-5-16h0c-1-8-5-16-9-24h0c-1-1-2-2-3-4-1-4-4-8-7-12l-1-1c-8-12-23-24-36-30-7-3-14-4-21-5 0-1-2-1-2-1-4 0-8-1-12-1v-1c-9 1-19 5-26 9l-1 2-8 5c-5 4-7 10-11 14-5-9-7-21-8-32h0l-1-3c4 1 8 4 12 6 4-2 7-5 11-7l12-6 15-4c4-1 9-1 13-1h2c1-1 2-2 2-3 0 1 0 2 1 3 4 1 9 1 13 2l10 2c1 0 2 1 3 1 5-1 8-6 13-7 3-1 5-1 8 0 3 0 5 1 8 1s6-2 9-3z" class="o"></path><path d="M479 257h1c1-1 2 0 3 0-3 0-7 1-9 3-1 1-2 1-3 1h0c-1 1-2 2-3 2-2 1-4 3-6 4l-3 3-1-1c1-2 6-6 8-7s6-4 8-5h4 1z" class="K"></path><path d="M581 247c1 0 2 0 3 1-4 8-10 16-17 22l6 5c0 1 2 2 2 3 0 3 1 1 2 4h0-1c-2-2-3-3-5-4-1-1-2-2-3-2-2-2-4-4-6-7h1l-1-1v-1l2-1v1h1v-1l2-1c6-5 10-12 14-18z" class="M"></path><path d="M571 278c2 1 3 2 5 4h1 0c-1-3-2-1-2-4l3 3 1 1 6 8c1 2 2 4 4 4 0 0 0-1 1-1 1-2-1-4 1-6h0v5c0 3 0 6 1 8 3 5 6 10 8 16h0c1 3 2 7 2 10v1h0c1 2 1 3 3 5 1 1 1 1 1 3s0 5 1 7l-3 7v-1c0-5-1-8-2-12-2-6-3-12-5-18-6-16-16-28-26-40z" class="p"></path><path d="M602 327c1 2 1 3 3 5 1 1 1 1 1 3s0 5 1 7l-3 7v-1c0-5-1-8-2-12 1-1 2-1 2-3l-2-2h-1v-1-1s1-1 1-2z" class="J"></path><path d="M584 248c2 1 2 2 3 4h0c2 3-1 9-2 12v1c0 2 1 3 0 5v3 1c0 3-1 5-3 7h-1c-1 0-1 1-2 1l-1-1-3-3c0-1-2-2-2-3l-6-5c7-6 13-14 17-22h0z" class="o"></path><path d="M585 265c0 2 1 3 0 5v3 1c0 3-1 5-3 7h-1c-1 0-1 1-2 1l-1-1c4-4 5-11 7-16z" class="K"></path><path d="M573 240h1 0c2-1 3 0 5 0v1h-2 1 2c-6 0-10 4-15 5s-11-2-16-1c-3 1-7 5-10 7h-1-2c-5 0-10-2-14-3-8-1-17-2-25-1-7 1-15 2-22 5-3 2-8 4-11 6-4 2-7 4-10 6h-4c-1-1-7-6-8-6h0l-1-3c4 1 8 4 12 6 4-2 7-5 11-7l12-6 15-4c4-1 9-1 13-1h2c1-1 2-2 2-3 0 1 0 2 1 3 4 1 9 1 13 2l10 2c1 0 2 1 3 1 5-1 8-6 13-7 3-1 5-1 8 0 3 0 5 1 8 1s6-2 9-3z" class="s"></path><path d="M496 261c18-3 36 2 50 12 15 11 27 24 35 40 6 12 10 27 14 40l3 16v2h-1l-1-5c0-2-1-5-2-6s-1-2-2-4c-1-5-2-11-5-16h0c-1-8-5-16-9-24h0c-1-1-2-2-3-4-1-4-4-8-7-12l-1-1c-8-12-23-24-36-30-7-3-14-4-21-5 0-1-2-1-2-1-4 0-8-1-12-1v-1z" class="M"></path><path d="M584 248v-2h1c2 1 4 3 4 6 10 17 16 38 16 58 0 4 0 9-2 14 0 1 0 1-1 2 0-3-1-7-2-10h0c-2-6-5-11-8-16-1-2-1-5-1-8v-5h0c-2 2 0 4-1 6-1 0-1 1-1 1-2 0-3-2-4-4l-6-8c1 0 1-1 2-1h1c2-2 3-4 3-7v-1-3c1-2 0-3 0-5v-1c1-3 4-9 2-12h0c-1-2-1-3-3-4z" class="o"></path><path d="M584 248v-2h1c2 1 4 3 4 6v1c1 6 1 11 2 17 0 7 1 15 0 22v-5h0c-2 2 0 4-1 6-1 0-1 1-1 1-2 0-3-2-4-4l-6-8c1 0 1-1 2-1h1c2-2 3-4 3-7v-1-3c1-2 0-3 0-5v-1c1-3 4-9 2-12h0c-1-2-1-3-3-4z" class="P"></path><path d="M586 272c1-2 2-4 2-6l1-1v2c0 3-1 5-1 7l-2-2z" class="r"></path><path d="M589 267l1 6c0 1 0 2-1 3l1 1s0 1 1 2h-1c-1 2-1 5-1 7l-1-12c0-2 1-4 1-7z" class="J"></path><path d="M586 272l2 2 1 12c-1 3 0 6 0 8-2 0-3-2-4-4l-6-8c1 0 1-1 2-1h1c2-2 3-4 3-7l1-2zm-117 0l1-2c7-4 17-8 26-9v1c4 0 8 1 12 1 0 0 2 0 2 1 7 1 14 2 21 5 13 6 28 18 36 30l1 1c3 4 6 8 7 12 1 2 2 3 3 4h0c4 8 8 16 9 24-4 3-6 7-9 11-2 3-4 6-7 8v1l-6 6v1c-10 9-22 16-34 19-4 0-7 1-10 1-5 1-10 0-14 1-10 0-24-2-33-6l-4-8-1-3v-3l-2-3h0l-7-25h2v-5c-1-2-2-6-4-8h-1l-3-16c-1-2-2-9-1-10l5-1c1 0 2 0 3-1-1-1-7-3-8-4l-1-1v-1l-2-2c4-4 6-10 11-14l8-5z" class="h"></path><path d="M481 270h3c-5 4-12 7-18 9l-2 2 1-1c2-4 8-5 11-8h1l4-2z" class="Y"></path><path d="M529 338c-1 2-1 4-3 5-1 1-2 1-2 1l-1 1c-2 1-7 2-8 5l-1 1h0-1c-1 1-1 2-1 4v-1-1c0-2 0-5 1-6 6-2 11-5 16-9z" class="r"></path><path d="M481 366v2l-1 1-1-1c-1 0-2 1-3 1h-5l1 1h0c1 0 3 2 4 3h1l3 1 4 1 4 2h-4l-1-1-6-2h-1l-1-1c-2 0-3 0-5 1l-1-3v-3h4c2 0 4-1 7-2h1z" class="Y"></path><path d="M530 337l1-1 2 2-3 3c-4 5-10 6-14 11l-2-1 1-1c1-3 6-4 8-5l1-1s1 0 2-1c2-1 2-3 3-5l1-1z" class="J"></path><path d="M530 337l1-1 2 2-3 3v-4z" class="K"></path><path d="M494 378h4v1h2l1 1h1l1-1h1 2l1 1-3 3c1 1 4 3 5 4 4 1 8-1 12 0-5 1-10 0-14 1l-6-6c-1 0-2-1-3-2-2 0-3 0-4-2z" class="Y"></path><path d="M470 271c2 1 3 0 4 0h1l-1 1h1s1 0 1-1l1 1h-1c-3 3-9 4-11 8l-1 1c-2 2-5 4-7 7-1 2-3 4-5 6v-1l-2-2c4-4 6-10 11-14l8-5 1-1z" class="b"></path><path d="M509 269h6c1 1 2 1 3 2 1 0 1 0 2 1 3 0 6 3 7 5l7 7c1 1 2 2 2 3 1 3 3 6 4 8v-1 1c1 1 1 1 1 2v2c1 1 1 1 1 2l1-1c1 3 2 9 0 12v4c-1 8-5 16-10 22l-2-2 2-3c6-9 9-21 6-32-2-10-8-20-18-26-4-3-7-4-12-6z" class="J"></path><path d="M462 335c4 4 8 9 13 11l1 1c4 2 9 3 14 5l-1 5c-1 3-2 6-4 8h-4v1h-1c-3 1-5 2-7 2h-4l-2-3c1 0 1 0 2 1h1c1 0 3-1 4-2h1c1-1 2-1 3-2l1-1c4-2 6-5 9-8h-2c-2 1-3 0-5 0h-2v-1c-2 0-3 0-4-1s-1-1-2-1c-5-3-7-7-11-10v-5z" class="b"></path><path d="M473 368l-1-1c3 0 5-2 7-2l1 1c-3 1-5 2-7 2z" class="p"></path><path d="M481 365h0c2-2 4-4 6-7l1-1h1c-1 3-2 6-4 8h-4z" class="J"></path><path d="M470 374c2-1 3-1 5-1l1 1h1l6 2 1 1h4 2c1 0 2 1 3 1h1c1 2 2 2 4 2 1 1 2 2 3 2l6 6c-10 0-24-2-33-6l-4-8z" class="i"></path><path d="M460 340h2c4 3 6 7 11 10 1 0 1 0 2 1s2 1 4 1v1h2c2 0 3 1 5 0h2c-3 3-5 6-9 8l-1 1c-1 1-2 1-3 2h-1c-1 1-3 2-4 2h-1c-1-1-1-1-2-1h0l-7-25z" class="K"></path><path d="M469 272l1-2c7-4 17-8 26-9v1c4 0 8 1 12 1 0 0 2 0 2 1l1 1c11 4 22 12 27 22 2 4 4 9 5 13l-1 1c0-1 0-1-1-2v-2c0-1 0-1-1-2v-1 1c-1-2-3-5-4-8 0-1-1-2-2-3l-7-7c-1-2-4-5-7-5-1-1-1-1-2-1-1-1-2-1-3-2h-6c-6-1-11-2-17-1l-8 2h-3l-4 2-1-1c0 1-1 1-1 1h-1l1-1h-1c-1 0-2 1-4 0l-1 1z" class="M"></path><path d="M481 270h1c1-2 3-3 5-2h5l-8 2h-3z" class="J"></path><path d="M469 272l1-2c7-4 17-8 26-9v1c4 0 8 1 12 1 0 0 2 0 2 1l1 1c-2 0-4 0-6-1-3-1-9 0-12 0-4 1-7 2-11 3s-8 3-12 4l-1 1z" class="p"></path><path d="M489 284c5-1 9-1 14 0h0c7 2 13 7 16 12 4 7 5 15 3 22s-7 14-14 18c-6 3-12 3-18 2 2-1 4-1 6-2l3-1c2-1 5-1 7-3 1-2 2-3 4-4 1-2 2-1 4-2 2-2 4-5 5-9 2-6 0-13-4-18-3-7-9-10-16-12h-5c-1-1-3 0-4-1s-1-1-1-2z" class="s"></path><path d="M510 328c1-2 2-1 4-2-1 2-2 4-2 6l-1 1c-1-2-1-3-1-5z" class="i"></path><defs><linearGradient id="AD" x1="557.096" y1="350.272" x2="531.481" y2="328.875" xlink:href="#B"><stop offset="0" stop-color="#bfa486"></stop><stop offset="1" stop-color="#e8d4bd"></stop></linearGradient></defs><path fill="url(#AD)" d="M575 313v-1c1 2 2 3 3 4h0v1c-1 1-2 3-2 5l-5 11c-9 17-24 31-42 35-5 1-11 1-15-1-2-1-2-2-3-4s0-6 1-9h0v1c0-2 0-3 1-4h1 0l2 1c-1 3-2 8-1 11 1 1 2 2 3 2 2 1 4 0 7 0 6-1 13-3 18-7l8-5c0-1 0 0-1-2 0-1 0-2 1-3h1 1c6-1 11-11 14-16l1-1-1-1v1h-1v-2-1h2c1-1 1 0 1-1 3-2 6-11 6-14z"></path><path d="M552 348l-1 1c3 0 5-1 7-2l-7 6c0-1 0 0-1-2 0-1 0-2 1-3h1z" class="Y"></path><path d="M543 316l12 10c4 0 6 2 10 3h1v2h1v-1l1 1-1 1c-3 5-8 15-14 16h-1-1c-1 1-1 2-1 3 1 2 1 1 1 2l-8 5c-5 4-12 6-18 7-3 0-5 1-7 0-1 0-2-1-3-2-1-3 0-8 1-11 4-5 10-6 14-11l3-3c5-6 9-14 10-22z" class="o"></path><path d="M555 326c4 0 6 2 10 3h1v2h1v-1l1 1-1 1h0c-5-1-8-3-12-6z" class="P"></path><path d="M510 264c7 1 14 2 21 5 13 6 28 18 36 30l1 1c3 4 6 8 7 12v1c0 3-3 12-6 14 0 1 0 0-1 1h-2v1h-1c-4-1-6-3-10-3l-12-10v-4c2-3 1-9 0-12-1-4-3-9-5-13-5-10-16-18-27-22l-1-1z" class="o"></path><path d="M568 300c3 4 6 8 7 12v1l-1-1c-1-2-3-3-5-5h-2c-1 0-1 0-1-1 0-2 1-4 2-6h0z" class="h"></path><path d="M543 316v-4c7 6 15 13 24 16h1-2v1h-1c-4-1-6-3-10-3l-12-10z" class="s"></path><path d="M490 338c-6-2-11-4-15-9-4-6-8-15-6-23 1-7 4-13 10-16 4-2 7-4 10-6h0c0 1 0 1 1 2s3 0 4 1h5c7 2 13 5 16 12 4 5 6 12 4 18-1 4-3 7-5 9-2 1-3 0-4 2-2 1-3 2-4 4-2 2-5 2-7 3l-3 1c-2 1-4 1-6 2z" class="o"></path><path d="M492 326c0-1 0-1 1 0 2 0 2-1 3 1l-1 2v-1h-1-1c-1-1-1-1-1-2z" class="K"></path><path d="M490 338c-6-2-11-4-15-9-4-6-8-15-6-23 1-7 4-13 10-16 4-2 7-4 10-6h0c0 1 0 1 1 2s3 0 4 1h5c-8 1-16 3-21 9l-2 2c1 2 1 4 2 6v1c-2-2-2-3-3-5-1 1-2 3-2 5h0c-2 8 0 13 3 19 2 3 4 6 6 8 1-1 3-3 4-3l2-1c1 0 2 0 3 1v2c1 1 1 0 1 1 1 1 2 1 2 2h2c1 1 2 1 3 1l-3 1c-2 1-4 1-6 2z" class="b"></path><path d="M486 329l2-1 2 2c0 2-1 3-2 4-1 0-1-1-2-1l1-3-1-1z" class="J"></path><path d="M488 328c1 0 2 0 3 1v2c1 1 1 0 1 1 1 1 2 1 2 2h2c1 1 2 1 3 1l-3 1c-3-1-5-1-8-2 1-1 2-2 2-4l-2-2z" class="i"></path><path d="M587 340h0c3 5 4 11 5 16 1 2 1 3 2 4s2 4 2 6l1 5-1 2v2c0 1-1 2-1 3-1 0-1 1-1 2v2c0 1 0 1 1 1 1 2 9 11 9 13l-23 85 2 2 1-1h0c1-1 3-1 4-2-3 4-6 7-9 10l-2 3h2 1c-1 2-3 3-5 5-2 1-3 3-5 4-1 1-3 1-5 2v-1l-4 1h-2c-4 2-9 2-13 3l1-1c-6 0-12 2-18 0-3 0-6 0-9-1-1-1-3-1-4-1-2 0-4-2-5-2l-2-3-3-1h-5l1 1 3 3-6-1c-1 0-2-1-4-1-1-1-1-1-3-1-1 0-4-2-6-3-6-3-12-6-18-10-5-3-9-7-13-11-3-3-6-6-8-10s-4-7-6-11c1-2-3-10-4-12l-7-21 1-2c-1-3-2-6-2-9 0 0-1-2-1-3v-4c-1-3-1-5-1-7-1-4-3-7-3-11l2-1c0 1 0 4 1 4l2 2c0-2-1-5-2-7l1-1-2-10c2 0 4 1 7 2h0l1-1c2 1 5 3 7 4l1-2 1-1c0-1 1-2 1-3l-2-1v-1c2 1 3 1 4 1h1 1 1 2 1 4l2 2 1-1c1 1 2 2 4 2-1-1-1-2-1-3l3 1h0 4l1 3 4 8c9 4 23 6 33 6 4-1 9 0 14-1 3 0 6-1 10-1 12-3 24-10 34-19v-1l6-6v-1c3-2 5-5 7-8 3-4 5-8 9-11z" class="K"></path><path d="M495 433h8l2 2h-5v1c-2-1-3-2-5-3z" class="Y"></path><path d="M564 483l2-1v5l1 1 1-1 1 1-5 3v-8z" class="r"></path><path d="M561 468c1 2 4 6 4 8 0 1-1 1 0 2v3l1 1-2 1c-1-2-1-4-1-6-1-2-1-4-1-6l-1-3z" class="Y"></path><path d="M514 490h2c1 1 1 1 2 1l-4-4c-2-2-4-3-6-5v-1c4 3 8 5 12 8l6 6c-4-1-8-3-12-5z" class="J"></path><path d="M488 432c1 0 2-1 3-1h3 0c2 1 4 0 6 0 3 0 7 1 10 2l3 1c-3 1-5 2-8 1l-2-2h-8c-2 0-4-1-7-1z" class="r"></path><path d="M487 430c6-1 13-2 19-1-1 1-3 1-5 1 2 0 2 1 4 1h2l3 2c-3-1-7-2-10-2-2 0-4 1-6 0h0-3c-1 0-2 1-3 1h0l-1-2z" class="P"></path><path d="M538 463h2 0l-1 1c-3 1-5 3-8 3l-3 1-2 2 1 1c-1 0-1 0-1 1h0l-6-3-1-1c-1 0-3-1-4-1l1-1c2 0 6 2 8 1h1 0c2 1 4 0 5 0 2-1 3-1 5-2 1-1 2-2 3-2z" class="Y"></path><path d="M529 496c8 2 17 1 26-1l-1 1c-8 3-17 4-25 2l1-1h-2l1-1z" class="c"></path><path d="M506 489c2 1 3 1 5 1 1 1 2 0 3 0h0c4 2 8 4 12 5l3 1-1 1h2l-1 1-9-2c-5-2-9-4-14-7z" class="C"></path><path d="M552 474c2 2 2 3 3 6 0 1 0 1 1 2h0c1 3 1 7 0 9-2 1-3 1-5 1h-1c-1 0-2-1-3-1l-6-7c4 1 8 6 11 6 0-1 1-1 1-2v-3c0-3-1-7-1-11z" class="p"></path><path d="M525 445l2-1 1 2v1c-3 0-7 2-10 3-4 2-9 2-13 5-1 4 1 7 3 11l-1 1-1 1h-1c1-4-3-9-1-13s11-6 15-8c2 0 4-1 6-2z" class="J"></path><path d="M501 476h1c1 1 1 1 1 2 1 1 5 2 5 3v1c2 2 4 3 6 5l4 4c-1 0-1 0-2-1h-2 0c-1 0-2 1-3 0-2 0-3 0-5-1-1 0-2-1-3-2 1-1 1-1 1-3-1-2-2-5-3-8z" class="Y"></path><path d="M504 484c3 3 6 4 10 6-1 0-2 1-3 0-2 0-3 0-5-1-1 0-2-1-3-2 1-1 1-1 1-3z" class="B"></path><path d="M554 496l6-1c-2 1-3 2-5 3l-1 1c-3-1-3 1-5 1h-1c-3 1-4 2-7 1h0c-2 1-1 1-2 2l-1-2c-3-1-6 0-9-1-3 0-5-1-7-1l-2-1-5-1c-1 0-2-1-2-1h7l9 2c8 2 17 1 25-2z" class="k"></path><path d="M551 446h2l2 2h1c6 8 10 19 12 28v2h1c0 1 0 2 1 3h0c0 2-1 4-2 6l-1 1-1-1v-5l-1-1v-3c-1-1 0-1 0-2 0-2-3-6-4-8-1-8-6-15-10-22z" class="J"></path><path d="M510 499h4c1 0 3 1 3 1 2 0 4 0 5-1 2 0 4 1 7 1 3 1 6 0 9 1l1 2c1-1 0-1 2-2h0c3 1 4 0 7-1h1c1 1 1 1 2 3-7 2-13 2-20 2l-2 1c-3 0-6 0-9-1-1-1-3-1-4-1-2 0-4-2-5-2l-2-3h1z" class="b"></path><path d="M519 502l3 1 9 2-2 1c-3 0-6 0-9-1l-1-3z" class="R"></path><path d="M510 499h4c1 0 3 1 3 1h4v2l1 1-3-1 1 3c-1-1-3-1-4-1-2 0-4-2-5-2l-2-3h1z" class="F"></path><path d="M510 499h4c1 0 3 1 3 1h4v2l1 1-3-1c-3 0-6-2-9-3z" class="k"></path><path d="M506 429l12 3c12 4 22 13 29 24 5 6 9 17 9 26-1-1-1-1-1-2-1-3-1-4-3-6-2-5-4-10-6-14-7-13-19-22-33-26l-3-1-3-2h-2c-2 0-2-1-4-1 2 0 4 0 5-1z" class="s"></path><path d="M581 481l2 2 1-1h0c1-1 3-1 4-2-3 4-6 7-9 10l-2 3h2 1c-1 2-3 3-5 5-2 1-3 3-5 4-1 1-3 1-5 2v-1l-4 1h-2c-4 2-9 2-13 3l1-1c-6 0-12 2-18 0l2-1c7 0 13 0 20-2-1-2-1-2-2-3 2 0 2-2 5-1l1-1c2-1 3-2 5-3l-6 1 1-1 9-4 5-3c4-1 9-4 12-7z" class="P"></path><path d="M581 481l2 2c-2 2-4 3-6 4-5 3-11 6-17 8l-6 1 1-1 9-4 5-3c4-1 9-4 12-7z" class="L"></path><path d="M584 482h0c1-1 3-1 4-2-3 4-6 7-9 10l-6 5h-1c-4 2-7 4-11 6s-10 2-12 5h-2c-6 0-12 2-18 0l2-1c7 0 13 0 20-2l5-1c9-3 16-8 22-14 3-1 5-3 6-6z" class="F"></path><path d="M579 490l-2 3h2 1c-1 2-3 3-5 5-2 1-3 3-5 4-1 1-3 1-5 2v-1l-4 1h-2c-4 2-9 2-13 3l1-1h2c2-3 8-3 12-5s7-4 11-6h1l6-5z" class="C"></path><path d="M579 490l-2 3h2c-2 2-5 3-7 5 0-1 0-2 1-3l6-5z" class="k"></path><path d="M549 506c2-3 8-3 12-5s7-4 11-6h1c-1 1-1 2-1 3-7 5-16 6-23 8z" class="u"></path><defs><linearGradient id="AE" x1="482.389" y1="459.681" x2="506.111" y2="506.319" xlink:href="#B"><stop offset="0" stop-color="#997f77"></stop><stop offset="1" stop-color="#c9b3ad"></stop></linearGradient></defs><path fill="url(#AE)" d="M460 457c1 1 4 2 5 2 0-1 0-1 2-2 0 1 1 2 2 2s1 0 2 1l2 3c5 6 12 16 20 19 2 0 4 1 5 2l5 3c1 1 2 2 3 2 5 3 9 5 14 7h-7s1 1 2 1l5 1 2 1c-1 1-3 1-5 1 0 0-2-1-3-1h-4-1l-3-1h-5l-3-1c0-1-5-3-6-4l-3-3-2-2h1c-2-3-7-6-10-9-5-5-10-10-14-16l-2-2-2-4z"></path><path d="M489 490l17 8h-5l-3-1c0-1-5-3-6-4l-3-3z" class="F"></path><path d="M460 457c1 1 4 2 5 2 0-1 0-1 2-2 0 1 1 2 2 2s1 0 2 1l2 3h-1c-1-1-1-1-3-1l3 3v1l-1-1c-1 1-1 1-2 1 0 0-1 0-1-1-2-1-3-2-4-2l-2-2-2-4z" class="Q"></path><path d="M462 461h1c1 0 3 1 4 1h1l-1-2c2 0 1 0 2 2l3 3v1l-1-1c-1 1-1 1-2 1 0 0-1 0-1-1-2-1-3-2-4-2l-2-2z" class="t"></path><path d="M469 462c2 0 2 0 3 1h1c5 6 12 16 20 19 2 0 4 1 5 2-2 0-5-1-8-1-6-4-15-10-18-17v-1l-3-3z" class="G"></path><path d="M490 483c3 0 6 1 8 1l5 3c1 1 2 2 3 2 5 3 9 5 14 7h-7-1c-8-2-16-8-22-13z" class="W"></path><path d="M442 369c2 1 3 1 4 1h1 1 1 2 1 4l2 2 1-1c1 1 2 2 4 2-1-1-1-2-1-3l3 1h0 4l1 3 4 8 3 6h0l5 9-4 2c1 6 5 12 5 17v1l1 3 2 7 1 3 1 2 5 18 7 22 1 4c1 3 2 6 3 8 0 2 0 2-1 3l-5-3c-1-1-3-2-5-2-8-3-15-13-20-19l-2-3c-1-1-1-1-2-1s-2-1-2-2c-2 1-2 1-2 2-1 0-4-1-5-2l2 4 2 2c4 6 9 11 14 16 3 3 8 6 10 9h-1l2 2 3 3c1 1 6 3 6 4l3 1 1 1 3 3-6-1c-1 0-2-1-4-1-1-1-1-1-3-1-1 0-4-2-6-3-6-3-12-6-18-10-5-3-9-7-13-11-3-3-6-6-8-10s-4-7-6-11c1-2-3-10-4-12l-7-21 1-2c-1-3-2-6-2-9 0 0-1-2-1-3v-4c-1-3-1-5-1-7-1-4-3-7-3-11l2-1c0 1 0 4 1 4l2 2c0-2-1-5-2-7l1-1-2-10c2 0 4 1 7 2h0l1-1c2 1 5 3 7 4l1-2 1-1c0-1 1-2 1-3l-2-1v-1z" class="l"></path><path d="M450 378l3 2-2 2-2-2 1-2z" class="I"></path><path d="M443 374h1v1 1l1 1s0 1 1 2l-5-2 1-2 1-1z" class="C"></path><path d="M461 416s4 6 4 7v1h-1c-1-2-3-5-3-8z" class="D"></path><path d="M446 379c-1-1-1-2-1-2l-1-1v-1l6 3-1 2-3-1z" class="U"></path><path d="M472 413l-3-3c-2-3-2-5-3-8l3 4c0-1 0-1 1-1v-1c1 1 2 3 3 4v3c-1-1-2-1-3-2l-1-1c1 2 2 3 3 5z" class="f"></path><path d="M480 429l1-1h-2-3c0-1-3-3-4-4v-1c0-1-2-1-2-2l-1-1c-1-2-4-5-5-7v-2h-1l1-1 3 3 2 4 1 1v1c1 0 1 0 1 1v1l1 1c1 1 2 1 2 3l3 3 2-1h0c2 1 3 1 4 1l1 1v3h-1l-1-1c0-1-1-1-1-2h-1z" class="D"></path><path d="M472 413c-1-2-2-3-3-5l1 1c1 1 2 1 3 2v-3l7 7c0 1 0 2 1 3h0c0 1 1 2 1 2l1 1v1-1-1-2c-1-1 0-1 0-2v1l1 3 2 7c-2-1-2-1-4-4l-1 1c1 2 2 2 2 4-1 0-2 0-4-1h0l-2 1-3-3c0-2-1-2-2-3l-1-1v-1c0-1 0-1-1-1v-1l-1-1h1c2 2 3 4 5 6 0-1-1-1-1-2h0c-1-2-2-3-3-4s-1-2-2-3v-1c2 2 4 5 6 7v1c1 1 3 3 4 3 0-1-1-2-2-2h0c-1-2-3-4-4-5v-1c0-1-1-1-1-2v-1z" class="B"></path><path d="M483 416v1l1 3 2 7c-2-1-2-1-4-4l-1 1c-2-1-3-3-4-4v-1c1 1 1 1 2 1v-2-1l1 1c0 1 1 2 2 2h0l1 1v1-1-1-2c-1-1 0-1 0-2z" class="H"></path><path d="M481 424l1-1c2 3 2 3 4 4l1 3 1 2 5 18 7 22c-3-3-3-5-4-8-1-2-2-5-3-7v-1l-1-1c0-1 0-1-1-2 0-1-1-1-1-2-1-1-2-1-3-3h-1-1v-1c-1-1-2-2-2-3s0-2-1-3h0c-1-2-4-6-4-8h0c0-2-1-2-1-3 1-2 1-1 3-1h1c0 1 1 1 1 2l1 1h1v-3l-1-1c0-2-1-2-2-4z" class="E"></path><path d="M481 424l1-1c2 3 2 3 4 4l1 3 1 2 5 18c-3-2-3-8-5-11v-1c-1 0-1-2-2-2 0-1-1-1-1-2h1v-1l-2-1 1-1-1-2-1-1c0-2-1-2-2-4z" class="N"></path><defs><linearGradient id="AF" x1="463.823" y1="423.678" x2="448.88" y2="440.051" xlink:href="#B"><stop offset="0" stop-color="#232526"></stop><stop offset="1" stop-color="#594d4c"></stop></linearGradient></defs><path fill="url(#AF)" d="M444 413h0c1-2-1-3-1-5 1-1 1-1 3-2 3 8 5 16 8 23 5 11 12 20 17 31-1-1-1-1-2-1s-2-1-2-2h0c-1-2-4-4-6-5-4-3-7-9-9-14v-2-1c-1 0-1-1-1-2-1-3-2-4-4-6 0-2 0-3 1-4-2-3-3-7-4-10z"></path><path d="M448 423c2 4 4 7 5 12h-1c-1 0-1-1-1-2-1-3-2-4-4-6 0-2 0-3 1-4z" class="e"></path><path d="M453 435c3 2 6 7 7 11l1 1h0c-2-1-3-2-4-4s-2-4-4-5c-1-1-1-1-1-2v-1h1z" class="I"></path><path d="M426 372c2 0 4 1 7 2h0c4 5 5 13 8 19 1 5 3 9 5 13-2 1-2 1-3 2 0 2 2 3 1 5h0 0-1-1c-2-2-3-5-5-8-1-3-1-4-3-5 0 1 1 3 1 4-1 1-1 2-1 3l-5-17c0-2-1-5-2-7l1-1-2-10z" class="E"></path><path d="M427 383l1-1 6 18c0 1 1 3 1 4-1 1-1 2-1 3l-5-17c0-2-1-5-2-7z" class="k"></path><defs><linearGradient id="AG" x1="474.818" y1="380.214" x2="448.981" y2="388.696" xlink:href="#B"><stop offset="0" stop-color="#131212"></stop><stop offset="1" stop-color="#302c2c"></stop></linearGradient></defs><path fill="url(#AG)" d="M442 369c2 1 3 1 4 1h1 1 1 2 1 4l2 2 1-1c1 1 2 2 4 2-1-1-1-2-1-3l3 1h0 4l1 3 4 8 3 6h0l5 9-4 2v-2c-5-2-11-4-15-8-2-1-5-2-6-4-2 0-4-2-6-3l2-2-3-2-6-3v-1h-1c0-1 1-2 1-3l-2-1v-1z"></path><path d="M462 370l3 1h0l-1 1c1 1 3 2 3 4-2-1-3-2-4-3s-1-2-1-3z" class="f"></path><path d="M453 380l8 5c-2 1-2 0-4 0s-4-2-6-3l2-2z" class="B"></path><path d="M457 385c2 0 2 1 4 0l7 5c-2 0-3 0-5-1h0c-2-1-5-2-6-4z" class="O"></path><path d="M442 369c2 1 3 1 4 1h1 1l1 1c2 0 3 1 5 3h0c0 1 1 2 1 3h0l-11-6-2-1v-1z" class="Q"></path><path d="M468 390c4 1 8 3 10 6v1c-5-2-11-4-15-8h0c2 1 3 1 5 1z" class="c"></path><path d="M449 370h2c1 1 2 1 3 2 0 1 2 2 3 3 2 1 4 3 6 3l1 2h-3c1 1 1 1 1 2l-1-1c-2-1-4-2-6-4h0c0-1-1-2-1-3h0c-2-2-3-3-5-3l-1-1h1z" class="j"></path><path d="M457 375c2 1 4 3 6 3l1 2h-3c1 1 1 1 1 2l-1-1v-1l-2-1c0-2-1-3-2-4z" class="U"></path><path d="M451 370h1 4l2 2 1-1c1 1 2 2 4 2 1 1 2 2 4 3l-1 1-3-1c1 2 3 2 3 3-1 0-2 0-3-1h0c-2 0-4-2-6-3-1-1-3-2-3-3-1-1-2-1-3-2z" class="N"></path><path d="M458 374l-1-1v-1h1l1 2h-1 0z" class="U"></path><path d="M467 376c2 1 4 9 5 11 1 1 1 1 1 2h0c-2-1-4-2-5-3-2-1-4-2-6-4 0-1 0-1-1-2h3l-1-2h0c1 1 2 1 3 1 0-1-2-1-3-3l3 1 1-1z" class="D"></path><path d="M464 380c1 1 2 1 3 3h-1c1 1 1 2 2 3-2-1-4-2-6-4 0-1 0-1-1-2h3z" class="N"></path><path d="M424 385l2-1c0 1 0 4 1 4l2 2 5 17c0-1 0-2 1-3 0-1-1-3-1-4 2 1 2 2 3 5 2 3 3 6 5 8h1 1 0c1 3 2 7 4 10-1 1-1 2-1 4 2 2 3 3 4 6 0 1 0 2 1 2v1 2c2 5 5 11 9 14 2 1 5 3 6 5h0c-2 1-2 1-2 2-1 0-4-1-5-2l2 4 2 2c4 6 9 11 14 16 3 3 8 6 10 9h-1l2 2 3 3c1 1 6 3 6 4l3 1 1 1 3 3-6-1c-1 0-2-1-4-1-1-1-1-1-3-1-1 0-4-2-6-3-6-3-12-6-18-10-5-3-9-7-13-11-3-3-6-6-8-10s-4-7-6-11c1-2-3-10-4-12l-7-21 1-2c-1-3-2-6-2-9 0 0-1-2-1-3v-4c-1-3-1-5-1-7-1-4-3-7-3-11z" class="G"></path><path d="M443 444h0c2 1 2 1 2 3h-1c-1-1-1-2-1-3z" class="d"></path><path d="M429 410c1 1 2 2 2 3h1c2 2 1 5 2 8v1c0 1 0 1 1 2l-1 1h0c1 1 1 1 1 2s1 2 2 2c0 2 1 4 1 5-1 0-1 1-1 1-1-4-3-7-4-10 0-3-1-4-2-6-1-3-2-6-2-9z" class="t"></path><defs><linearGradient id="AH" x1="435.463" y1="408.184" x2="421.037" y2="391.316" xlink:href="#B"><stop offset="0" stop-color="#8c7776"></stop><stop offset="1" stop-color="#a3908b"></stop></linearGradient></defs><path fill="url(#AH)" d="M424 385l2-1c0 1 0 4 1 4l5 18c1 3 1 4 0 6v1h-1c0-1-1-2-2-3 0 0-1-2-1-3v-4c-1-3-1-5-1-7-1-4-3-7-3-11z"></path><path d="M432 406c1 3 1 4 0 6-1-3-1-3 0-6z" class="t"></path><path d="M431 419c1 2 2 3 2 6 1 3 3 6 4 10 0 2 1 3 2 5l4 12c1 2 2 3 2 5s1 3 2 5v3c-2-4-4-7-6-11 1-2-3-10-4-12l-7-21 1-2z" class="F"></path><defs><linearGradient id="AI" x1="465.1" y1="437.074" x2="449.348" y2="451.886" xlink:href="#B"><stop offset="0" stop-color="#c2aeac"></stop><stop offset="1" stop-color="#e3c8b9"></stop></linearGradient></defs><path fill="url(#AI)" d="M434 400c2 1 2 2 3 5 2 3 3 6 5 8h1 1 0c1 3 2 7 4 10-1 1-1 2-1 4 2 2 3 3 4 6 0 1 0 2 1 2v1 2c2 5 5 11 9 14 2 1 5 3 6 5h0c-2 1-2 1-2 2-1 0-4-1-5-2l2 4 2 2c4 6 9 11 14 16 3 3 8 6 10 9h-1c-4-2-7-5-11-7-4-4-9-8-13-13s-7-12-11-18c-7-14-13-27-18-43 0-1 0-2 1-3 0-1-1-3-1-4z"></path><path d="M440 419c2 1 2 2 3 5h1c1 0 1 1 2 2l1 1c2 2 3 3 4 6 0 1 0 2 1 2v1 2c-1-2-2-4-4-6l-2 1-4-9-2-5z" class="S"></path><path d="M442 424c2 1 3 2 4 3 0 1 1 1 1 2s1 2 1 3l-2 1-4-9z" class="I"></path><path d="M446 433l2-1c2 2 3 4 4 6 2 5 5 11 9 14 2 1 5 3 6 5h0c-2 1-2 1-2 2-1 0-4-1-5-2l-14-24z" class="U"></path><path d="M434 400c2 1 2 2 3 5 2 3 3 6 5 8h1 1 0c1 3 2 7 4 10-1 1-1 2-1 4l-1-1c-1-1-1-2-2-2h-1c-1-3-1-4-3-5-2-5-3-10-5-15 0-1-1-3-1-4z" class="L"></path><defs><linearGradient id="AJ" x1="486.338" y1="505.867" x2="461.145" y2="451.806" xlink:href="#B"><stop offset="0" stop-color="#4e4644"></stop><stop offset="1" stop-color="#7c6968"></stop></linearGradient></defs><path fill="url(#AJ)" d="M447 462l2 1c1-1 0 0 0-2v-2c-1-1-1-1-1-2v-1c1-1 1-1 1-2l2 2c1 2 2 3 3 5h0l-1-4v-1c1 1 1 2 2 3 0 1 1 1 2 2l7 12c2 2 5 4 7 5l1 1s1 0 2 1l2 1c4 2 7 5 11 7l2 2 3 3c1 1 6 3 6 4l3 1 1 1 3 3-6-1c-1 0-2-1-4-1-1-1-1-1-3-1-1 0-4-2-6-3-6-3-12-6-18-10-5-3-9-7-13-11-3-3-6-6-8-10v-3z"></path><path d="M498 497l3 1 1 1 3 3-6-1v-1c1 0 1 0 2-1-1-1-2-1-3-2z" class="H"></path><path d="M587 340h0c3 5 4 11 5 16 1 2 1 3 2 4s2 4 2 6l1 5-1 2v2c0 1-1 2-1 3-1 0-1 1-1 2v2c0 1 0 1 1 1 1 2 9 11 9 13l-23 85c-3 3-8 6-12 7l-1-1c1-2 2-4 2-6h0c-1-1-1-2-1-3h-1v-2c-2-9-6-20-12-28h-1l-2-2h-2c-2-2-3-4-5-6-11-11-22-17-37-20-5-1-11-1-17-1-3 0-5 0-8 1l-1-3v-1c0-5-4-11-5-17l4-2-5-9h0l-3-6c9 4 23 6 33 6 4-1 9 0 14-1 3 0 6-1 10-1 12-3 24-10 34-19v-1l6-6v-1c3-2 5-5 7-8 3-4 5-8 9-11z" class="h"></path><path d="M544 400h1c1 2 1 2 1 3l-2 1 1 2s0 1-1 1h-1c1-3 1-5 1-7z" class="Y"></path><path d="M550 399l-4-3-2 2c-2-1-3-2-3-3l1-1c4 0 7 2 11 4-1 1-2 1-3 1zm42-43c1 2 1 3 2 4s2 4 2 6l1 5-1 2c-2-3-4-9-4-13v-4z" class="r"></path><path d="M594 407c2-3 3-5 3-8 1-2 0-7 2-8 1 1 1 1 1 2 1 5-3 13-5 18-1-1-1-2-1-4z" class="s"></path><path d="M495 398c1-1 1-1 2 0v1l-2 2c-1 1-1 2-3 3 0 1 0 2-1 3l-1-2c-1 0-2-1-2-2h0c0-1 1-2 2-3 1-3 1-1 3-1 1 0 1-1 2-1zm58 0l4 3c5 4 8 8 11 13-2-1-3-3-5-4s-4-4-7-6c-2-2-4-3-6-5 1 0 2 0 3-1z" class="i"></path><path d="M587 340h0v1 2c0 2-3 5-4 7-4 6-8 12-14 17l-1-1s1-1 1-2c1-1 2-1 2-2v-2-1c3-2 5-5 7-8 3-4 5-8 9-11z" class="s"></path><path d="M569 478c1-1 1-2 1-4s1-5 2-7c2-6 4-11 7-17 1-2 2-5 3-7 2-5 4-11 5-16s3-9 4-14c1-2 1-4 3-6 0 2 0 3 1 4l-25 70h0c-1-1-1-2-1-3z" class="P"></path><path d="M483 417h1 5l2-2h9 1c3 0 7 1 10 2 4 1 9 2 13 3 5 2 9 6 13 9 7 5 15 11 19 18v1h-1l-2-2h-2c-2-2-3-4-5-6-11-11-22-17-37-20-5-1-11-1-17-1-3 0-5 0-8 1l-1-3z" class="b"></path><path d="M521 387c3 0 6-1 10-1 12-3 24-10 34-19v-1l6-6v2c0 1-1 1-2 2 0 1-1 2-1 2l1 1c-18 18-40 24-64 24-9 0-18-2-27-4l-1 1h0l-3-6c9 4 23 6 33 6 4-1 9 0 14-1z" class="P"></path><path d="M556 145c0-2 1-4 1-6l1-1c0-1 0-2 1-3 1-2 2-2 3-3l-2 6c1 1 2 1 2 2 1 0 1 3 1 3 1 5 4 9 8 12h0c2 2 4 3 6 4 3 2 7 4 11 5 3 0 6 1 8 2h-1v2c-2 1-8-2-10-2 1 1 3 2 4 4h1l2 2v1c2 0 4 2 6 2 2 1 5 2 7 3h1c1 1 2 1 3 1h2v1h-3 0l7 2h2 1l2-1h3v1c1-1 0-1 1-1h1v-1c-1 0-1 0-2-1h0l3 1c1 1 2 1 4 1h1 0 3l-1 2h3c2-1 4-1 6-1h2l3-1s0 1-1 1v1h0c4-1 6-2 9-2l2-1c0-1 1-2 1-2 2-2 5-4 6-7 1-2 2-3 4-4 0-1 3-5 3-5v-2l1-3h1c1-2 2-4 3-7 1 3 1 7 2 10v8 1h1c1 1 1 2 1 3h1c0-1 0-1 1-2v-1h0c0-2-1-3 0-4 1-2 2-2 4-2l1 1h1c0-2 0-1 1-2h0l1 1c0-1 0-1 1-2h0l-1-2c1-1 3 0 4 0v1c1 0 1 1 2 1l1 1h0l-2 2h0l1 1h-2l-3 2 1 1h1 0c-1 1-1 2 0 3v1l2-1c1 1 1 2 1 3l5-6 1 2-8 13-1 2 1 1h0c7-1 13 0 19 0h7c2 1 3 0 4-1 1 1 2 2 3 2l2-1v-1h2 2c1 0 2 1 3 1h5 3 1 1l-1-2h2l2-1c2-2 3-3 5-4l2-3 1-3c2 2 2 2 4 2h5 14l-2 2h2c-3 5-7 10-6 16v2c1 2 1 3 2 5l2 2v1l2 1 1 1c2 2 2 3 5 4l1 2h2c2 3 4 4 4 7 4 3 7 7 10 11l1-1h1l3 4 4 4 5-4v-1l-1-2 1-1 3 1h0c2 0 3 1 5 1 0 1 3 3 3 3 2 2 5 5 7 6h1c2 1 4 2 6 2v1h1c-3 10 0 18 5 27 2 4 4 7 7 10 1 1 4 3 5 5 1 1-1 9-2 12 0 3 0 7 1 10l1 1c2 8 7 15 12 22 2 1 8 11 10 13-7-3-12-7-18-11l-1 9c-1-2 0-7-2-9-4 1-7 4-9 8-3 6-3 13-4 19-4 19-11 35-22 50-18 24-45 43-75 48-12 1-28 1-38-7l12 1c4 1 9 0 13-1 15-3 30-9 41-19 11-9 20-20 25-34 2-7 3-16 4-24 0-7-2-14-7-20-8-8-18-6-28-7-19 0-38-5-51-18l-6-7c-2-2-3-4-3-7-2-3-3-7-5-11-2-2-4-3-5-5l-3-3-6-3c-1-1-2-2-3-2-2-1-3-2-4-1l-1 1c3 4 15 12 15 17l-1 1c-6-4-10-10-18-9-2 1-3 3-4 5l-2 3c-5 8-7 17-11 26l-20 62-1 3-6 20-1 3c-1 4-2 9-2 13-1 1-2 2-2 4v2c-1 0-1 1-1 2l-2 6 1 1h1c1 0 1 1 1 2h3l1-1c1 2 3 5 5 7l-2 2c1 2 3 4 4 6l-2 1v2-1l-2 1v1c1 3 1 5 1 8 0 1 1 4 1 5h-1-1v3c1 2 0 5 1 6 0 1 1 2 1 3 1 1 2 2 4 2v1c2 3 4 5 5 7h1l1 1v1c-1 1 0 3 0 4l1 3-2 2 1 2c-2 5-4 9-6 13h-2l-1-1-2 5-1 4c-2 4-5 8-8 12l-3 5-3 3-5 5c-2 0-2 1-3 2l-2 1c-2 1-6 3-7 4s-3 1-4 2h-2c-2 1-5 0-7 0l-1 2v1l-2 6 2 2h0v6c0 1-1 2-2 3l-3 1h-1l-24 13-1 1c-1 1-2 3-4 4 1-6 4-12 5-18l5-17 3-12 9-28 6-22 3-9c0-2 1-5 2-6l7-19c-3 1-7 5-8 8-3 3-8 7-11 10-2 1-6 5-8 5h-4c2-1 3-3 5-4 2-2 4-3 5-5h-1-2l2-3c3-3 6-6 9-10-1 1-3 1-4 2h0l-1 1-2-2 23-85c0-2-8-11-9-13-1 0-1 0-1-1v-2c0-1 0-2 1-2 0-1 1-2 1-3v-2l1-2h1v-2h1c1-4 1-8 2-11l3-9 3-7c2-3 6-10 9-12-4 10-7 18-8 28h0c0 6 0 11 1 16l8-29c0-1 1-2 1-3l3-8 4-17c3-10 4-20 3-31 0-2-1-6-1-8v-1c-3-3-4-11-5-14-1-5-4-10-6-15 0-1-1-3-1-3 0-1 1-2 1-2l1-2 3-3 5-5s-1 0-2-1l-1-2c1-3 4-5 4-8-7-15-24-19-37-27-7-4-14-10-20-16-5-6-11-14-12-23-1-3-1-7-1-11z" class="v"></path><path d="M697 240l2 2c1 1 2 2 2 3-3-2-5-2-8-3h0 1c2 0 2 0 3-2z" class="F"></path><path d="M699 242l3-2c1 2 2 3 3 5l-2 1h1v2l-3-3c0-1-1-2-2-3zm65-17v2c-1 1-1 1-2 1l-1-1c-1-1-2-1-3-3v-2l3 2 3 1z" class="B"></path><path d="M842 334l2 1 3 1-1 8c-1-1-1-3-1-4v-1l-2-1-1-4zm-148-72c1 0 3 0 4 1l-10 7c2-3 3-5 6-8z" class="C"></path><path d="M592 504s1 0 2 1l-2 7c-1 0-1 2-1 2-1-1-1-1-2-1l3-9z" class="a"></path><path d="M704 248v-2h-1l2-1c2 3 4 6 6 10-3-1-5-5-7-7z" class="f"></path><path d="M751 221c-2 0-3-1-4-2l1-2v-1h3l-1 2h1v1c1 0 1 1 2 1 1 1 2 2 4 2v1h0l-1 1c-1-2-1-1-2-2-1 0-2-1-3-2v1zM625 420c1-1 1-2 1-3h0c1 0 1 1 2 1v1c0 1 1 2 2 4h-1c-1-1-1-2-2-4-1 1 0 9-1 11-1-2-1-4-1-6v-4z" class="B"></path><path d="M654 284c1 0 2 0 3-1h1l4 1 6 2v1c-2 0-9-3-11-2-1 1-1 2-2 3 0-1 0-2-1-3v-1z" class="k"></path><path d="M679 246l2 1c-4 3-6 6-8 10v-5l3-6 1 1c1 0 1-1 2-1z" class="m"></path><path d="M694 262l5-5h1v2h3v1l-5 3h0c-1-1-3-1-4-1z" class="f"></path><path d="M730 216c1 0 2 1 4 1l1 3h1c-1 0-2 0-3 1l-6-2c1-1 1-2 2-3h1z" class="N"></path><path d="M601 454l4-9v6 1l-3 6-1-4z" class="C"></path><path d="M776 243c1-2-2-3-2-5h1l1 1h1c0 1 1 2 1 3h0c2 1 3 1 4 3h0c-1 0-1 1-2 1l1 1s1 1 1 2l1 2v2c-1-1-2-2-2-4l-1-1h0l-1-1c-1-1-2-2-3-4z" class="c"></path><path d="M804 338l2 3 3 3v3c-1 0-2-1-2-1l-6-6 1-1 2-1z" class="L"></path><path d="M764 446c-5 4-13 5-19 6 3-2 6-2 8-3l10-4 1 1z" class="c"></path><path d="M775 238h2s1 1 2 1c1 1 2 2 2 3 1 1 1 1 1 2 1 1 1 1 1 2l1 2 1 1v1c1 0 1 1 1 1l4 9h0c-2-3-4-6-5-9-1-2-3-4-3-6h0c-1-2-2-2-4-3h0c0-1-1-2-1-3h-1l-1-1z" class="B"></path><path d="M805 272c0-2 0-4 1-6 0-3 1-7 2-10v9c-1 1 0 2-1 4 0 1 0 3-1 4v1 2l-1-4h0z" class="g"></path><path d="M590 592c3-3 3-8 4-12l2 2h0v6c0 1-1 2-2 3l-3 1h-1z" class="Y"></path><path d="M617 405c3 2 6 5 8 9v6 4c-1-1-1-3-1-4-1-1-1-3-1-5-1-1-1-2-2-3-1-3-3-4-4-7z" class="f"></path><path d="M794 309c1-2 2-3 3-5l2-3c1 0 0-1 1-2 1-3 3-5 4-7 1-1 1-2 2-3l-1 3v2 1c-1 1-1 0-1 1 0 2-2 4-3 5l-1 2c-1 1-1 1-1 2s-4 4-5 4z" class="E"></path><path d="M841 324h2v4l-1 6 1 4-1 2h0c-1-1-1-1-1-2v1c0 1-1 2-2 2v3c0 2 0 2-1 4v2 1c0 1 0 1-1 2v2l-1-1c0-1 1-2 1-4l1-9 2-2c1-5 1-10 1-15z" class="B"></path><path d="M674 273c2 5 5 12 9 16 3 2 6 4 8 7l-5-3c-1-1-2-1-3-2-4-5-7-9-10-15 0-1 0-2 1-3z" class="Q"></path><path d="M625 408c2 3 3 6 5 9h0c1 2 2 3 2 5v4 1c-1-1-1-4-2-5h0v-1c-1-1-1-2-1-2v-1h-1c-1 0-1-1-2-1h0c0 1 0 2-1 3v-6c1-2-1-4-1-6h1z" class="C"></path><path d="M599 463c-3 3-6 7-9 10l-1-1 12-18 1 4-1 1-2 3v1z" class="E"></path><path d="M820 381v-2h1c2-2 3-6 3-8l1-2c0 1 1 1 0 2l-1 2v2l1 1c-1 1-1 3-2 5 0 1-1 2-1 3l-1 1c0 1-1 2-2 3l-1 1-3 4v-1l1-1v-1-2c1-2 3-5 4-7z" class="C"></path><path d="M589 513c1 0 1 0 2 1l-7 22-1-1 6-22z" class="k"></path><path d="M691 236c1 1 2 1 3 2h1c-1 1-1 2-2 3l1 1h-1 0c-5 1-8 2-12 5l-2-1c4-4 9-5 12-10z" class="G"></path><path d="M601 479c1-2 1-3 2-3l-9 29c-1-1-2-1-2-1 0-2 1-5 2-6l7-19z" class="W"></path><path d="M612 427c0 1-1 2 0 3 0 2 0 2-1 4 0 2-1 3-2 5-1 4-2 9-4 13v-1-6-2c1-3 2-5 3-7 0-2 1-3 2-5l2-4z" class="H"></path><path d="M616 395l-4-7 1-1 6 11c1-2 1-4 2-5l2 2v1c-1 1-1 2-3 3 4 5 9 11 10 18h0c-2-3-3-6-5-9l-9-13z" class="M"></path><path d="M789 315v3h-1l1 1c1-2 3-3 5-4h0c-1 2-9 11-12 12-2-1-2-1-3 0h-1l1-1 5-3h0-2-1l-2 1c0 1 0 0-1 0 1-1 3-3 5-4 1 0 2-1 3-2l3-3z" class="C"></path><path d="M713 206c1-1 2 0 3-1 1 0 1-1 2 0 3 0 6 0 9 1h3c1 1 1 0 2 1 1 0-1-1 1 0l1 1c1 0 1 0 2 1h3 1 0l1 1 1 1-1-1-2 2h-1v1 1h-1c-1-1-2-1-2-2h-1c1 0 1-1 1-1l-1-1c-2-1-4-2-7-2-1 0-1 0-2-1h-1-1c-2-1-7-1-9-1h-1z" class="B"></path><path d="M851 315l6 9c-3 0-6 0-8 3-1-2-1-4-1-5v-1l1-4v-1c0-1 2-1 2-1z" class="O"></path><path d="M686 235l1-2h1 0c1 1 1 2 2 3h1 1-1c-3 5-8 6-12 10-1 0-1 1-2 1l-1-1 1-1c1-1 1-1 2-1s1-2 2-3c0-1 0-2 1-3l2-3h2z" class="U"></path><path d="M686 235h2c0 1-1 1-1 2-2 1-4 3-6 4 0-1 0-2 1-3l2-3h2z" class="F"></path><path d="M583 535l1 1-7 27h-1v-1c-1 0 0 0-1 1h-1l9-28z" class="X"></path><path d="M694 213c2 2 2 5 4 7l1-1 7 20c-2 0-5-7-6-9-3-6-5-11-6-17z" class="P"></path><path d="M684 191c2 1 3 3 4 5 1-1 1 0 1-1 4 7 8 16 10 24l-1 1c-2-2-2-5-4-7-2-3-3-7-4-10-1-2-2-3-3-5 0-1 0-2-1-3-1-2-2-3-2-4z" class="b"></path><path d="M743 212h4-1l1-1c-2-1-3 0-4-1s-1 0-2-1l1-1c-1 0-1 0-2 1v-1l1-2 1 1 1 1h-1c1 1 2 1 3 2l1-1 1 1c2-1 2 0 4-1l1-1v1c2 0 2 0 3 1l-1 1c-1-1-2-1-3-1h-1l1 1h1v1l-1-1-3 2v1l-1 1h1 1c1-1 1 0 2 0l1-1s1 1 2 1h0-2l1 1h1c1 2 2 3 4 4h0c-1 0-1 1-2 0-1 0-1-1-2-2s-2-1-3-2h-3v1l-1 2c1 1 2 2 4 2l1 1v1l-2-1c-1 1-1 1-2 1v-1h1c-1-1-3-1-5-1 0-1 0-2 1-3h0c1-1 1-2 1-3 1-2 1 0 1-2-1 0-2 0-4-1z" class="E"></path><path d="M804 296l2 1-3 6c-2 3-4 7-7 10l-2 2h0c-2 1-4 2-5 4l-1-1h1v-3l5-6c1 0 5-3 5-4s0-1 1-2l1-2c1-1 3-3 3-5z" class="L"></path><path d="M698 231c0-1 1-1 2-1 1 2 4 9 6 9 1 3 8 19 7 21-1-2-1-4-2-5-2-4-4-7-6-10-1-2-2-3-3-5l-1-2c0-2-2-5-3-7z" class="m"></path><path d="M713 313c1 2 2 3 4 4s4 3 6 5c5 4 10 7 17 9-1 0-2 1-2 0h-1-1l-5-3h0-1 0c0 1 0 1 1 2h1v1h1v1c-2-2-6-5-9-6-1 0 0 0-1-1h0c-1-1-2-1-2-1-1-1 0-1-1-1 0 1 2 2 2 3v1l-6-7c-2-2-3-4-3-7zm25-68h6v1h-4-2 0v1 2c1 0 1 1 2 2v1h1v1h-2c-1 1-3 0-4 0h-2-1 0c-1 0-2 0-3-1h0v1h1c1 0 2 0 3 1h3 1c1 0 2 0 2 1l4 1h-1l-3-1-12-2v-1c-3 0-7 2-10 2h0l21-9z" class="C"></path><path d="M681 218c1 1 1 3 1 4h3l1 2 2 3c2 2 4 5 5 7l4 6c-1 2-1 2-3 2l-1-1c1-1 1-2 2-3h-1c-1-1-2-1-3-2h1-1-1c-1-1-1-2-2-3h0-1l-1 2h-2c0-2 0-2 2-3v-3h1c-1-1-2 0-3 0l-2-7-2-4h1z" class="m"></path><path d="M682 222h3l1 2 2 3c-1 0-2 0-4-1-1-1-1-3-2-4z" class="U"></path><path d="M805 272h0l1 4v2l-1 1v1h0c-1 2-1 3-2 4h0 0-1v1h1c0 1-1 2-1 3v1 2h0c0 1-1 1-1 2v2h-1c0 2 0 3-1 4-1 0-1 1-1 2s-2 2-3 3v1l-1 1-3 3h0l3-4-1-1-2 3h-1l5-8 1-2c0-1 1-2 1-3l1-2c1-1 1-2 1-3l1-2c1-2 1-4 2-6 1-1 1 0 1-2v-1c1-2 1-4 2-6z" class="E"></path><path d="M849 310c1 1 2 3 2 5 0 0-2 0-2 1v1l-1 4v1c0 1 0 3 1 5 0 2-1 3-2 5v4l-3-1-2-1 1-6v-4h0v-1c1-2 1-4 1-6h1v3 1c1-2 1-3 1-5h1v1h0c1-1 1-1 1-2h2v-1c-1-1-1-2-1-3v-1z" class="S"></path><path d="M849 310c1 1 2 3 2 5 0 0-2 0-2 1v1l-1 4v1 3h-1v-8h0c1-1 1-1 1-2h2v-1c-1-1-1-2-1-3v-1z" class="N"></path><path d="M843 328c0 2 1 3 1 4h1c1-1 1-2 1-3 1 0 1 0 1 1v2 4l-3-1-2-1 1-6z" class="L"></path><path d="M710 214h9 7 4v1 1h-1c-1 1-1 2-2 3l-15-1-7 1c0-2-1-3-1-4l4-1h2z" class="U"></path><path d="M704 215l4-1v3c1 1 2 1 4 1l-7 1c0-2-1-3-1-4z" class="N"></path><path d="M726 214h4v1 1h-1c-2 0-5-1-6 0h-1c-1 0-2-1-3-2h7z" class="B"></path><path d="M631 448h1c1 0 1 1 1 2h3l1-1c1 2 3 5 5 7l-2 2c1 2 3 4 4 6l-2 1v2-1l-2 1v1 2h-1c0-1 0-2-1-4l-2-2c-1-4-3-6-5-9 0-2-1-3-1-5h1c1 2 1 5 3 6l-3-8z" class="j"></path><path d="M640 458c1 2 3 4 4 6l-2 1v2-1c-1-1-2-1-3-2s-1-2-2-4c1 1 2 1 2 2l1-1-1-1 1-2z" class="t"></path><path d="M631 448h1c1 0 1 1 1 2 1 3 4 6 6 8h1l-1 2 1 1-1 1c0-1-1-1-2-2 0 0-1-1-1-2l-2-2-3-8z" class="F"></path><path d="M637 449c1 2 3 5 5 7l-2 2h-1c-2-2-5-5-6-8h3l1-1zm154-110h-2s1-1 1-2h2 0 2-1v-1l1-1 1 1 1-1h2l1-1s1 0 2 1l1-2c0 1 0 1 1 2l-2 1h0c2 0 2 1 3 1v-2h0c1 0 1 1 1 2 1-1 2-3 3-3s2-1 3-1l1-1v1h2v1h-1l1 1c1 1 0 3 0 5h0v1l-2 2h0v2-1c-1-1-1-2-1-4l-1-1h-2l-2 2-2-3-2 1-1 1c-4-1-7-1-10-1z" class="T"></path><path d="M791 339v-1h5c1 0 4-1 5-1s2 1 3 1l-2 1-1 1c-4-1-7-1-10-1z" class="C"></path><path d="M574 563h1c1-1 0-1 1-1v1l-7 30c-1 4-3 9-3 12l-1 1c-1 1-2 3-4 4 1-6 4-12 5-18l5-17 3-12z" class="n"></path><path d="M806 289c0-1 1-2 1-4 0-1 1-3 1-4 1 0 1 0 2-1v-1c1-2 1-5 2-7h1v-4l1 1c1 1 2 2 2 4v2 2c-1 2 0 4-1 7l-1 3-1-1-1-1v1l-1 1v2l-2 1-2 5-1 2-2-1c0-1 0 0 1-1v-1-2l1-3z" class="B"></path><path d="M810 285c0-2 1-4 2-5 0-1 1-1 1-2v-2c0-1 1-2 2-3l1 2v2c-1 2 0 4-1 7l-1 3-1-1-1-1v1l-1 1v2l-2 1 1-5z" class="L"></path><path d="M810 285c1-1 3-4 5-4v3l-1 3-1-1-1-1v1l-1 1v2l-2 1 1-5z" class="I"></path><path d="M734 210l1 1s0 1-1 1h1c0 1 1 1 2 2h1v-1-1h1l2-2 1 1 1 1c2 1 3 1 4 1 0 2 0 0-1 2 0 1 0 2-1 3h0-1c-1 2-2 3-4 4l-1 1h1c-1 1-1 1-2 1h0v-1l-2-1-3-1c1-1 2-1 3-1h-1l-1-3c-2 0-3-1-4-1v-1-1h-4c1 0 2-1 3-1l1-1c1 0 2-1 3-1s0 0 1-1z" class="C"></path><path d="M730 215h5c0 1 1 1 2 2h-3c-2 0-3-1-4-1v-1z" class="D"></path><path d="M737 217c1-1 2-1 4-2h0 3l1-1h1c-1 1-1 1-1 2s-1 2-1 2c-1 2-2 3-4 4l-1 1h1c-1 1-1 1-2 1h0v-1l-2-1-3-1c1-1 2-1 3-1h-1l-1-3h3 0z" class="E"></path><path d="M737 217h0 1 1c1 1 1 1 0 2v2l-3 1-3-1c1-1 2-1 3-1h-1l-1-3h3z" class="B"></path><path d="M737 217h0 1 1c1 1 1 1 0 2 0 0 0-1-1-1s-2 1-3 2l-1-3h3z" class="H"></path><path d="M808 339h2l1 1c0 2 0 3 1 4v1c1 1 1 3 3 3v-2l-1-1 1-1h0l1-1c0 5 1 13 0 18 0 1 0 2-1 3 1 1 1 2 1 3-1 0-1-1-1-1-1 0-1 0-1 1 0 0 0 1-1 1-1-8-2-14-6-22 0 0 1 1 2 1v-3l-3-3 2-2z" class="S"></path><path d="M808 339h2l1 1c0 2 0 3 1 4v1c1 1 1 3 3 3v-2l-1-1 1-1h0l1-1c0 5 1 13 0 18v-1c0-2-1-3-1-4-1-2-1-3-1-4v-1c-1-1-1-2-2-3h0c-3-3 1 0-1-2l-1 2-1-1v-3l-3-3 2-2z" class="C"></path><path d="M806 341l2-2c1 2 2 3 2 5h-1l-3-3z" class="c"></path><path d="M763 445c5-2 10-6 14-9 5-3 9-6 13-9l1-1c2-2 4-5 6-6 3-1 6-5 8-7l6-8 5-7h0l5-8 1-1c0-1 1-2 2-4v3c-7 13-17 26-28 35-5 4-9 8-13 12-6 4-13 8-19 11l-1-1z" class="S"></path><path d="M595 524h0c1 1 0 1 0 2v1 4h0c0-1 1-3 1-4v-1h1c0 2 0 3-1 4v1 1l-1 4v1 3-1c1-1 0-2 0-3 1-1 0-1 1-2 0-1 2-2 2-3h2c-1-2-1-2 0-4h0l1 7v-4c1-1 1-2 1-4h1c0 3-1 6-1 8-1 2-1 4-1 5v1l-1 2v1l-1 2v2h0 0v-1-3c-1 1-1 1-1 2v1h0l-1-1c1-1 1-3 1-4l-1 3v2l-1 1v1 1c-1 1-1 2-2 3 0-1 1-3 1-4l1-3c0-2 0-4 1-5v-2c1-1 0-1 1-1v-4h0c-1 1-1 1-1 3-1 1-1 2-1 3v3c-1 1-1 0-1 1v2c0 2-1 4-2 5h0 0c1-2 0-3 0-4 1-1 1-2 1-3v1c-1 1-1 3-2 5l-1-2v1h-1l1-3v-1c1-1 1-1 1-2v-1c0-1 1-3 1-5h0v-1-1c0-2 0-4 1-6v-1c0-1 0-2 1-3z" class="E"></path><path d="M599 382c0-1 0-2 1-3 5 6 8 12 13 17l1-1h2l9 13h-1c0 2 2 4 1 6-2-4-5-7-8-9-6-7-14-14-18-23z" class="k"></path><path d="M614 395h2l9 13h-1c-3-4-7-8-11-12l1-1z" class="L"></path><path d="M686 200c1 0 2 1 2 1 0 1 1 1 2 2 1 3 2 7 4 10 1 6 3 11 6 17-1 0-2 0-2 1-2-2-3-4-4-7l-8-13-1-3v-4h1 0v-4z" class="W"></path><path d="M685 204h1l1 4-1 3-1-3v-4z" class="Z"></path><path d="M752 223h1l1 1c2 1 4 1 5 3 1 1 2 1 3 2l5 4c0-1-2-2-1-3h1l1 1h-1l1 1 1-1c1 1 2 1 2 2v1l1 1h1l1 1h0c0-1-1-2-2-3-2-1-1 0-1-2-1-1-4-4-6-4l-1-2h0l-3-1c1 0 3-1 3 0 1 0 2 2 3 3 2 0 5 3 5 5 2 3 5 4 7 7-1 0-2-1-2-1h-2-1c0 2 3 3 2 5 0-1 0-1-1-2 0 1 1 2 1 3l1 1c1 2 3 4 4 6v1l2 4v1 1c1 1 1 2 1 3l1 1v1l1 1v1 1h0l1 1v2c1 2 1 3 1 5 1 1 1 2 1 3s0 4 1 5v1l-1 2v1c-1 2-1 5-1 8h-1v-2-2c0-1 0-1 1-2 1-2 0-7 0-10h0c-1-2-1-3-1-4h0c0-2 0-2-1-3v-2l-1-3v-1c-2-4-3-10-5-14-5-10-13-17-21-24-2-1-5-2-7-4z" class="T"></path><path d="M839 296l4 4 1 3c1 1 5 5 5 7v1c0 1 0 2 1 3v1h-2c0 1 0 1-1 2h0v-1h-1c0 2 0 3-1 5v-1-3h-1c0 2 0 4-1 6v1h0-2c-1-1-1-1 0-2-1-2-1-4-1-6v-1-1c-1-2-2-7-1-9 1 1 1 1 1 2s0 1 1 2c1-2-1-7-2-10h0 1l-1-3z" class="I"></path><path d="M839 296l4 4 1 3h-1l-2-1-1-3-1-3z" class="R"></path><path d="M840 315v-1l2 1 1 8v1h0-2c-1-1-1-1 0-2-1-2-1-4-1-6v-1z" class="L"></path><path d="M809 290l2-1v-2l1-1v-1l1 1 1 1v2h2 0v3 1h1 1v1h1 1 0v2l-4 4-5 5v-1l1-1-1-1c0 1-1 1-1 2l-1-3c-1 2-2 4-3 4 0-1 0-1 1-2l-2-1c-1 0-1 1-2 1l3-6 1-2 2-5z" class="F"></path><path d="M811 298c0-1 1-2 2-4h0l2-2c0 1 0 1 1 1v1h0c-1 1-1 1-2 1v3h-3zm-4-3v-1c2-1 2-2 4-4 1 2 1 2 0 4v1c-1 1-1 2-1 4h0l-1 2c-1 2-2 4-3 4 0-1 0-1 1-2l-2-1c-1 0-1 1-2 1l3-6 1-2z" class="G"></path><path d="M817 293h1v1h1 1 0v2l-4 4-5 5v-1l1-1-1-1c0 1-1 1-1 2l-1-3 1-2 1-1h3v-3c1 0 1 0 2-1h0v-1h1z" class="d"></path><path d="M817 293h1v1h1 1 0v2l-4 4c0-2 0-3 1-5v-2z" class="F"></path><path d="M814 298c0 2-2 3-3 4 0 1-1 1-1 2l-1-3 1-2 1-1h3z" class="q"></path><path d="M684 207l1 1 1 3 8 13c1 3 2 5 4 7 1 2 3 5 3 7l1 2-3 2-2-2-4-6c-1-2-3-5-5-7l-2-3h1 1v-2h0-1v1c0-1 0-2-1-3-1-2-1-3-1-5l-3-5c1-1 1-2 2-3z" class="S"></path><path d="M684 207l1 1 1 3 8 13c1 3 2 5 4 7 1 2 3 5 3 7-3-4-7-8-10-12l-6-11-3-5c1-1 1-2 2-3z" class="X"></path><defs><linearGradient id="AK" x1="780.684" y1="320.721" x2="813.747" y2="305.896" xlink:href="#B"><stop offset="0" stop-color="#876e69"></stop><stop offset="1" stop-color="#a08985"></stop></linearGradient></defs><path fill="url(#AK)" d="M803 303c1 0 1-1 2-1l2 1c-1 1-1 1-1 2 1 0 2-2 3-4l1 3c0-1 1-1 1-2l1 1-1 1v1c-7 8-16 17-26 22-3 2-7 4-10 6 2-3 4-4 7-6 3-1 11-10 12-12l2-2c3-3 5-7 7-10z"></path><path d="M818 261l7 18c0 1 1 3 1 4v5h0v2h-1c1 1 0 1 1 1-1 1-2 2-4 3l-2 2v-2h0-1-1v-1h-1-1v-1-3h0-2v-2l1-3c1-3 0-5 1-7 1 0 1 1 2 2v1-1-1-4-13z" class="f"></path><path d="M825 279c0 1 1 3 1 4v5h0c-1 1 0 1-1 1h-1c0 1 0 2-1 2h-2c-1-1 0-2 0-4v2h1l1-1c0-1 2-3 2-4l-1-1 1-4z" class="B"></path><path d="M818 274c2 2 3 4 3 7v1 1h0v4c0 2-1 3 0 4h2c1 0 1-1 1-2h1c1 0 0 0 1-1v2h-1c1 1 0 1 1 1-1 1-2 2-4 3l-2 2v-2h0-1-1v-1h-1-1v-1-3h0-2v-2l1-3c1-3 0-5 1-7 1 0 1 1 2 2v1-1-1-4z" class="D"></path><path d="M817 288c1 1 1 1 1 2 0 0 1 1 1 2v1h0c2 0 2 0 3 1l-2 2v-2h0-1-1v-1h-1-1v-1-3l1-1z" class="U"></path><path d="M818 274c2 2 3 4 3 7v1 1h0-1v3l-1-1v-4l-1 1h0c0 2 0 3-1 4v2l-1 1h0-2v-2l1-3c1-3 0-5 1-7 1 0 1 1 2 2v1-1-1-4z" class="B"></path><path d="M703 219h-2v-3-5l1-1v-2c1 0 1-1 2-1v-1h1c1-1 1-1 3-1v1l2-1c1 1 1 0 3 1h1c2 0 7 0 9 1h1 1c1 1 1 1 2 1 3 0 5 1 7 2-1 1 0 1-1 1s-2 1-3 1l-1 1c-1 0-2 1-3 1h-7-9-2l-4 1c0 1 1 2 1 4h-2z" class="H"></path><path d="M703 219h-2v-3-5l1-1v-2c1 0 1-1 2-1v-1h1c1-1 1-1 3-1v1l2-1c1 1 1 0 3 1h1l-1 1-2 1h0c-2 0-3 1-4 0-1 1-1 2-2 3 1 0 2 0 4 1 2 0 4 0 6-1 1 1 1 1 2 1h3l-1 1h-1-2c-2 1-4 0-6 1h-2l-4 1c0 1 1 2 1 4h-2z" class="C"></path><path d="M703 219c-1-1-1-2-1-3 1-1 1-1 2-1 0 1 1 2 1 4h-2zm41-1h1c-1 1-1 2-1 3 2 0 4 0 5 1h-1v1c1 0 1 0 2-1l2 1c2 2 5 3 7 4 8 7 16 14 21 24 2 4 3 10 5 14v1l1 3v2h-1l-1 1v1c0 1 0 1 1 2v5h1c0 3 0 7-1 10v-6c0-17-9-36-21-48-6-5-16-13-24-13h-1l1-1c2-1 3-2 4-4z" class="H"></path><defs><linearGradient id="AL" x1="669.508" y1="280.458" x2="676.72" y2="273.317" xlink:href="#B"><stop offset="0" stop-color="#0b0d0e"></stop><stop offset="1" stop-color="#3d3433"></stop></linearGradient></defs><path fill="url(#AL)" d="M667 262h1c1 6 2 10 5 14 3 6 6 10 10 15 1 1 2 1 3 2h-2v1h0-1c-2-1-3-2-5-2-1-1-3-1-5-2h0v-1s-4-1-5-2v-1l-6-2 1-1h2v-1h-1c-1-2-1-3-1-5h0c0-1-1-2-1-2 0-3-1-4-1-6l2-1c0 1 0 2 1 4h1v-1-2-5c1 0 1-1 2-2z"></path><path d="M684 293c-1 0-2 0-3-1 0-1-1-2-2-3h1c0 1 1 1 2 2h1c1 1 2 1 3 2h-2z" class="l"></path><path d="M665 264c1 2 1 3 1 4 1 1 1 1 1 2 0 4 0 5 2 8l1 2c1 2 3 4 4 6l3 4h0l-4-1s-4-1-5-2v-1l-6-2 1-1h2v-1h-1c-1-2-1-3-1-5h0c0-1-1-2-1-2 0-3-1-4-1-6l2-1c0 1 0 2 1 4h1v-1-2-5z" class="B"></path><path d="M663 277c4 3 7 7 11 9l3 4h0l-4-1s-4-1-5-2v-1l-6-2 1-1h2v-1h-1c-1-2-1-3-1-5h0z" class="W"></path><path d="M701 168l1 2-8 13-1 2 1 1h0c7-1 13 0 19 0h7c2 1 3 0 4-1 1 1 2 2 3 2 1 1 2 1 3 0 1 1 1 2 0 2v1c-8-1-36-4-40 0-1 1-1 2-1 2v3c0 1 0 0-1 1-1-2-2-4-4-5 0-1-2-2-2-3h0v-3l1 1 2 1h1l2-4c-2-1-2-2-2-4 0-1-1-2-1-2 2-2 3-1 6-1 1 1 2 1 4 0l1-2 5-6z" class="w"></path><path d="M685 177c2-2 3-1 6-1 1 1 2 1 4 0-2 4-5 10-9 11l2-4c-2-1-2-2-2-4 0-1-1-2-1-2z" class="j"></path><path d="M690 159c1-1 3 0 4 0v1c1 0 1 1 2 1l1 1h0l-2 2h0l1 1h-2l-3 2 1 1h1 0c-1 1-1 2 0 3v1l2-1c1 1 1 2 1 3l-1 2c-2 1-3 1-4 0-3 0-4-1-6 1 0 0 1 1 1 2 0 2 0 3 2 4l-2 4h-1l-2-1-1-1-1-1c-1-1-1-3-1-4-1-4-2-7-2-11h1c1 1 1 2 1 3h1c0-1 0-1 1-2v-1h0c0-2-1-3 0-4 1-2 2-2 4-2l1 1h1c0-2 0-1 1-2h0l1 1c0-1 0-1 1-2h0l-1-2z" class="c"></path><path d="M680 180l1-1 2 2c-1 1-1 2-2 3-1-1-1-3-1-4z" class="H"></path><path d="M690 159c1-1 3 0 4 0v1c1 0 1 1 2 1-2 0-3 0-5 2v1h-1c0-1-1-1-1-2h0l1 1c0-1 0-1 1-2h0l-1-2z" class="T"></path><path d="M683 186h1s1 0 2-1l-1-1v-1c-1-1 0-2-1-3s-1-1-1-2v-1h-2c0-3 1-4 3-7v1c0 1 0 2-1 4h1l1-1v1h-1l1 2s1 1 1 2c0 2 0 3 2 4l-2 4h-1l-2-1z" class="I"></path><path d="M685 166l2-1c1 0 1 0 2 1 1 0 2 0 2 1l1 1h1 0c-1 1-1 2 0 3v1l2-1c1 1 1 2 1 3l-1 2c-2 1-3 1-4 0-3 0-4-1-6 1l-1-2h1v-1l-1 1h-1c1-2 1-3 1-4v-1c0-1 1-3 1-4z" class="U"></path><path d="M693 171v1l2-1c1 1 1 2 1 3l-1 2c-2 1-3 1-4 0 0-1 1-2 1-3h-2v-1l1-1h2z" class="L"></path><path d="M685 166l2-1c1 0 1 0 2 1 1 0 2 0 2 1l1 1h-1c0 2-1 2-2 4-2-1-2 0-3-1l-1-5z" class="N"></path><path d="M689 166c1 0 2 0 2 1l1 1h-1-1c-1 0-1 1-2 0h0l1-2z" class="D"></path><path d="M819 350h0c1-2 2-3 1-4v-1-1c1-1 0-3 1-5h0c0 2 0 4 1 5v1 2c0 1 0 2 1 3h0c0 3 0 6-1 9 0 6 0 13-2 18 0 2 0 2-1 2v1l1 1c-1 2-3 5-4 7-1 1-2 2-3 4h0c-1 1-2 1-2 2l-1 1-1 2c0 1 0 1-1 1 0-2 2-4 3-7v-4c1-2 2-5 2-7v-12c1 0 1-1 1-1 0-1 0-1 1-1 0 0 0 1 1 1 0-1 0-2-1-3 1-1 1-2 1-3 1-5 0-13 0-18 0-1 0-2 1-3v-1l1 1-1 2v2h1v2l-1 1h0v5h0v-2h2z" class="E"></path><path d="M817 352v-2h2 2l-1 2v11c-1 4-2 8-2 12-1 0-1 0-1-1-1-1 0-4 0-5v-11-6z" class="C"></path><path d="M612 512l16-55c1 2 1 4 2 6 1 7 5 17 1 24-1 2-3 3-4 4-6 5-10 8-13 15-1 2-1 4-2 6z" class="h"></path><path d="M671 187l1 1 2 1c3 0 3 2 5 3h1c1 1 3 2 3 3l4 3c1 2 2 3 3 5-1-1-2-1-2-2 0 0-1-1-2-1v4h0-1v4l-1-1c-1 1-1 2-2 3l3 5c0 2 0 3 1 5 1 1 1 2 1 3v-1h1 0v2h-1-1l-1-2h-3c0-1 0-3-1-4h-1c-1-2-2-3-3-5l-2-2-6-3h1l1-1c-1 0-1-1-2-2h1 1v-1c-1-1-2-2-3-4l1-1 2-1v-1l-1-2v-4c-1-1-1-1-2-1l1-1v-2h2z" class="T"></path><path d="M669 199c3 2 5 5 7 8l-4-2-1-1c-1-1-2-2-3-4l1-1z" class="B"></path><path d="M678 210c3 3 5 8 7 12h-3c0-1 0-3-1-4-1-2-2-4-4-6l1-2z" class="S"></path><path d="M671 204l1 1 4 2 2 3-1 2c2 2 3 4 4 6h-1c-1-2-2-3-3-5l-2-2-6-3h1l1-1c-1 0-1-1-2-2h1 1v-1z" class="e"></path><path d="M671 204l1 1c1 2 2 4 3 5v1l-6-3h1l1-1c-1 0-1-1-2-2h1 1v-1z" class="W"></path><path d="M671 187l1 1 2 1c0 1 0 1 2 3l8 15c-1 1-1 2-2 3l-10-16c0-1-1-2-2-3s-1-1-2-1l1-1v-2h2z" class="P"></path><path d="M671 187l1 1c1 2 1 3 0 6 0-1-1-2-2-3s-1-1-2-1l1-1v-2h2z" class="b"></path><path d="M674 189c3 0 3 2 5 3h1c1 1 3 2 3 3l4 3c1 2 2 3 3 5-1-1-2-1-2-2 0 0-1-1-2-1v4h0-1v4l-1-1-8-15c-2-2-2-2-2-3z" class="a"></path><path d="M674 189c3 0 3 2 5 3l2 4c-2-1-3-3-5-4-2-2-2-2-2-3z" class="V"></path><path d="M680 192c1 1 3 2 3 3l4 3c1 2 2 3 3 5-1-1-2-1-2-2 0 0-1-1-2-1v4h0-1l-4-8-2-4h1z" class="d"></path><path d="M683 195l4 3c1 2 2 3 3 5-1-1-2-1-2-2 0 0-1-1-2-1h0c-2-1-3-3-3-5z" class="Z"></path><defs><linearGradient id="AM" x1="614.868" y1="455.397" x2="601.043" y2="428.404" xlink:href="#B"><stop offset="0" stop-color="#1e2124"></stop><stop offset="1" stop-color="#4e3e39"></stop></linearGradient></defs><path fill="url(#AM)" d="M618 418h0c1 1 1 2 0 4 1 1 2 1 2 3 0 1 0 1-1 1-1 3 1 7 0 9l-1 6v1 2l-2 3v1c0 1 0 2-1 3h0l-2 4-1 2h-1c-1 1-2 1-2 3l-1 2h-2l-4 6h-1c-3 2-5 5-8 7h0c1-1 1-2 2-3 1 0 0-1 1-1 0-1 1-2 1-3l3-4h1l-2-1v-1l2-3 1-1 3-6c2-4 3-9 4-13 1-2 2-3 2-5 1-2 1-2 1-4-1-1 0-2 0-3 0-2 1-3 2-4v-2c1-2 2-2 4-3z"></path><path d="M618 418h0c1 1 1 2 0 4v1c-1 3 0 6-1 8-2 11-6 21-11 31l-4 6h-1c8-11 11-26 14-39 1-4 3-7 3-11z" class="n"></path><path d="M618 422c1 1 2 1 2 3 0 1 0 1-1 1-1 3 1 7 0 9l-1 6v1 2l-2 3v1c0 1 0 2-1 3h0l-2 4-1 2h-1c-1 1-2 1-2 3l-1 2h-2c5-10 9-20 11-31 1-2 0-5 1-8v-1z" class="c"></path><path d="M618 444c1 2 0 4 0 7-3 8-9 18-15 25-1 0-1 1-2 3-3 1-7 5-8 8-3 3-8 7-11 10-2 1-6 5-8 5h-4c2-1 3-3 5-4 2-2 4-3 5-5h-1-2l2-3c3-3 6-6 9-10 1 0 4-4 5-5 3-2 5-5 8-7h1l4-6h2l1-2c0-2 1-2 2-3h1l1-2 2-4h0c1-1 1-2 1-3v-1l2-3z" class="D"></path><path d="M609 462v4c-2 2-4 5-5 7l-1-1c1-1 2-2 2-4s3-4 4-6z" class="l"></path><path d="M606 462h2l1-2c0-2 1-2 2-3 0 1-1 2-1 4l-1 1c-1 2-4 4-4 6l-8 9c-1 1-2 1-3 1 1-3 5-7 8-10l4-6z" class="C"></path><path d="M601 468h1c-3 3-7 7-8 10l-8 9c-2 2-5 4-6 6h-1-2l2-3c3-3 6-6 9-10 1 0 4-4 5-5 3-2 5-5 8-7z" class="P"></path><path d="M672 157h1c1-2 2-4 3-7 1 3 1 7 2 10v8 1c0 4 1 7 2 11 0 1 0 3 1 4l1 1v3h0c0 1 2 2 2 3s1 2 2 4c1 1 1 2 1 3l-4-3c0-1-2-2-3-3h-1c-2-1-2-3-5-3l-2-1-1-1h-2-3-4 0c-2-1-3-1-5 0h-2 0l-1-1h0c2-1 4-3 4-5l-1-1c0-1 1-2 1-2 2-2 5-4 6-7 1-2 2-3 4-4 0-1 3-5 3-5v-2l1-3z" class="V"></path><path d="M677 180l2 2-1 2h1c0 1 1 3 1 4h0l-2-1v1c1 0 1 1 2 1v2 1h-1c-2-1-2-3-5-3v-1l1-2s0 1 1 1v-2h1 1l-1-1v-3-1z" class="G"></path><path d="M674 165h1c0 5 0 11 2 15v1 3l1 1h-1-1v2c-1 0-1-1-1-1l-1-1c0-1 0-2-1-3h0c0-2 0-2 1-3h0c-1-1 0-1-1-1v-1c1-4 1-8 1-12z" class="R"></path><path d="M673 165h1c0 4 0 8-1 12v1c1 0 0 0 1 1h0c-1 1-1 1-1 3h0c1 1 1 2 1 3l1 1-1 2v1l-2-1-1-1h-2-3l2-3v-1-1l2 1c0-2 0-4 1-5v-1c0-1 1-2 1-4h1v-7-1z" class="k"></path><path d="M668 184l3 1v2h-2-3l2-3z" class="n"></path><path d="M674 188l-1-1c-1-1 0 0-2-1 0-4 1-6 2-9v1c1 0 0 0 1 1h0c-1 1-1 1-1 3h0c1 1 1 2 1 3l1 1-1 2z" class="t"></path><path d="M673 166v7h-1c0 2-1 3-1 4v1c-1 1-1 3-1 5l-2-1v1 1l-2 3h-4 0c1-2 1-5 3-6l1-3h0c1-1 1-2 2-3 2-1 4-7 5-9z" class="u"></path><path d="M668 182v-2c1-1 1-3 1-4l2 1v1c-1 1-1 3-1 5l-2-1z" class="M"></path><path d="M665 181h1l1-1c0 2-1 2-2 3l1 1s1-1 2-1v1l-2 3h-4 0c1-2 1-5 3-6z" class="a"></path><path d="M672 157h1c1-2 2-4 3-7 1 3 1 7 2 10v8c-1-2-1-4-1-5v-1c0-1-1-2-1-2v-2c-1 2-1 3-2 4 0 1 0 2-1 3v1c-1 2-3 8-5 9v-1l-1-1v1c-1 0-2 1-3 2 1-1 1-2 1-4l1-1 1-1c0-2 1-2 1-3s3-5 3-5v-2l1-3z" class="d"></path><path d="M671 162c1-2 2-2 2-4l1 1c0 1 0 2-1 4l-1 2c0 2-2 8-4 9l-1-1v1c-1 0-2 1-3 2 1-1 1-2 1-4l1-1 1-1c0-2 1-2 1-3s3-5 3-5z" class="u"></path><path d="M668 167c0 1-1 1-1 3l-1 1-1 1c0 2 0 3-1 4 1-1 2-2 3-2v-1l1 1v1c-1 1-1 2-2 3h0l-1 3c-2 1-2 4-3 6-2-1-3-1-5 0h-2 0l-1-1h0c2-1 4-3 4-5l-1-1c0-1 1-2 1-2 2-2 5-4 6-7 1-2 2-3 4-4z" class="V"></path><path d="M664 176c1-1 2-2 3-2v-1l1 1v1c-1 1-1 2-2 3h0c-2 2-2 3-5 4l3-6z" class="M"></path><path d="M661 182c3-1 3-2 5-4l-1 3c-2 1-2 4-3 6-2-1-3-1-5 0h-2c3-2 4-3 6-5z" class="k"></path><path d="M653 288l1-3c1 1 1 2 1 3l-32 107-2-2c-1 1-1 3-2 5l-6-11h0c1 0 2 0 3-1 0-2 1-3 2-5v1 3h0 1v-1c2-2 2-6 3-9h0c1-2 1-3 2-4 0-1 0-2 1-3 2-3 3-7 4-10 1-2 2-4 3-5s1-2 1-3l2-5h0v-2h1v-1-1-1c1-1 1 0 1-1v-2l1-1c0-1 1-3 1-4h0c0-1 0-3 1-3v-2c1-1 1-3 2-4v-2l1-1v-1-1s1-1 1-2v-1c1 0 0 0 1-1h0c2-4 2-7 3-11 1-5 3-10 5-15z" class="X"></path><path d="M622 375h0c1-2 1-3 2-4 0-1 0-2 1-3 2-3 3-7 4-10 1-2 2-4 3-5s1-2 1-3l2-5h0v-2h1v-1-1-1c1-1 1 0 1-1v-2l1-1c0-1 1-3 1-4h0c0-1 0-3 1-3v-2c1-1 1-3 2-4v-2l1-1v-1-1s1-1 1-2v-1c1 0 0 0 1-1h0c-2 9-5 18-8 27l-16 52c-1 1-1 3-2 5l-6-11h0c1 0 2 0 3-1 0-2 1-3 2-5v1 3h0 1v-1c2-2 2-6 3-9z" class="E"></path><defs><linearGradient id="AN" x1="817.425" y1="305.401" x2="834.544" y2="300.006" xlink:href="#B"><stop offset="0" stop-color="#b57e72"></stop><stop offset="1" stop-color="#ddc9c4"></stop></linearGradient></defs><path fill="url(#AN)" d="M808 241c3 1 4 3 5 6 1 0 1 1 1 1h1v-2l2 1 4 6h-1c-1-1-2-3-4-4 1 2 2 6 4 8l10 25 3 9c4 2 4 4 6 8h0c1 3 3 8 2 10-1-1-1-1-1-2s0-1-1-2c-1 2 0 7 1 9v1 1c0 2 0 4 1 6-1 1-1 1 0 2 0 5 0 10-1 15l-2 2-1 9c0 2-1 3-1 4-3 12-6 23-12 34v-3c0-1 1-3 2-5 2-4 4-9 5-13 5-19 5-37 2-55-1-7-2-14-4-20l-3-1c-1 0 0 0-1-1h1v-2h0v-5c0-1-1-3-1-4l-7-18c0-1-1-4-2-5-2-5-5-10-8-15z"></path><path d="M826 283l3 9-3-1c-1 0 0 0-1-1h1v-2h0v-5z" class="m"></path><defs><linearGradient id="AO" x1="842.461" y1="333.482" x2="836.119" y2="326.024" xlink:href="#B"><stop offset="0" stop-color="#413a39"></stop><stop offset="1" stop-color="#664f4f"></stop></linearGradient></defs><path fill="url(#AO)" d="M837 322c1-1 1-1 0-2 0-1 0-1 1-1v4h1v-3c0-2 0-3 1-4 0 2 0 4 1 6-1 1-1 1 0 2 0 5 0 10-1 15l-2 2-1-19z"></path><path d="M833 291c4 2 4 4 6 8h0c1 3 3 8 2 10-1-1-1-1-1-2s0-1-1-2c-1 2 0 7 1 9v1 1c-1 1-1 2-1 4v3h-1v-4c-1 0-1 0-1 1 1 1 1 1 0 2l-4-31z" class="m"></path><defs><linearGradient id="AP" x1="676.513" y1="267.645" x2="666.079" y2="262.661" xlink:href="#B"><stop offset="0" stop-color="#8b6c6a"></stop><stop offset="1" stop-color="#a4807a"></stop></linearGradient></defs><path fill="url(#AP)" d="M669 208l6 3 2 2c1 2 2 3 3 5l2 4 2 7c1 0 2-1 3 0h-1v3c-2 1-2 1-2 3l-2 3c-1 1-1 2-1 3-1 1-1 3-2 3s-1 0-2 1l-1 1-3 6v5c-1 3-2 5-2 8l3 8c-1 1-1 2-1 3-3-4-4-8-5-14v-2l1-4-1-6c0-3-1-4-2-6 0-2-1-5-1-7l2-3-1-1c0-2-1-3-1-4v-3-11l1-2c1-2 1-4 3-5z"></path><path d="M678 238h1v-2h1c0 3-2 5-4 7l1 2-1 1-3 6v5c-1 3-2 5-2 8v-3-8l1-1-1-1c0-2 0-4-1-6v-2l2 2c0 1 0 0 1 2v-2c2-2 3-5 5-8z" class="d"></path><path d="M684 229c1 0 2-1 3 0h-1v3c-2 1-2 1-2 3l-2 3c-1 1-1 2-1 3-1 1-1 3-2 3s-1 0-2 1l-1-2c2-2 4-4 4-7h-1v2h-1c-1 0-2 0-3-1l2-1c-1-1-1-1-1-2-1-1 0-2 0-3h2c2-1 4-1 6-2z" class="G"></path><path d="M676 231h2v1 3l-1 1c-1-1-1-1-1-2-1-1 0-2 0-3z" class="W"></path><path d="M665 229v-3l2 2 2 2 4 1h1 2c0 1-1 2 0 3 0 1 0 1 1 2l-2 1c1 1 2 1 3 1-2 3-3 6-5 8v2c-1-2-1-1-1-2l-2-2v2h-1c0 3 1 7 0 10l-1-6c0-3-1-4-2-6 0-2-1-5-1-7l2-3-1-1c0-2-1-3-1-4z" class="q"></path><path d="M665 229v-3l2 2 2 2h0c0 1 1 2 1 3v1c-2 0-3-1-4-1 0-2-1-3-1-4z" class="M"></path><path d="M665 237l2-3 1 3c2 3 1 6 1 9s1 7 0 10l-1-6c0-3-1-4-2-6 0-2-1-5-1-7z" class="Q"></path><path d="M674 231h2c0 1-1 2 0 3 0 1 0 1 1 2l-2 1c1 1 2 1 3 1-2 3-3 6-5 8v2c-1-2-1-1-1-2l-2-2c0-2 0-3-1-5v-1c1-1 2-2 2-3s-1-1 0-2l2-1v-1h1z" class="R"></path><path d="M674 231h2c0 1-1 2 0 3 0 1 0 1 1 2l-2 1-1-1v-2-1c-1 1-1 1-1 2s0 2-1 3l-3 1v-1c1-1 2-2 2-3s-1-1 0-2l2-1v-1h1z" class="d"></path><path d="M669 208l6 3 2 2c1 2 2 3 3 5l2 4 2 7c-2 1-4 1-6 2h-2-2-1l-4-1-2-2-2-2v-11l1-2c1-2 1-4 3-5z" class="v"></path><path d="M667 228h5l1 1 1 2h-1l-4-1-2-2z" class="E"></path><path d="M682 222l2 7c-2 1-4 1-6 2h-2-2l-1-2-1-1h0 1l2 2v-1c0-1 1-2 2-2 3-1 3-2 5-5z" class="l"></path><path d="M610 569l-4 1c-3 1-6 1-9 1l7-30c2-5 3-9 5-14-1 3-1 7 1 9l1 1c2-2 1-7 2-7l1 1-1 1 2 2 1-1c0 1 0 1 1 1 2 1 3 5 4 7 3 3 5 6 7 9 0 2 1 3 2 4l-3 3-5 5c-2 0-2 1-3 2l-2 1c-2 1-6 3-7 4z" class="Y"></path><path d="M627 557l-3-6c-2-3-4-5-6-8-1-2-4-4-4-7v-2l2 3c2 1 3 2 4 4h1c3 3 5 6 7 9 0 2 1 3 2 4l-3 3z" class="J"></path><defs><linearGradient id="AQ" x1="635.362" y1="512.202" x2="608.994" y2="488.883" xlink:href="#B"><stop offset="0" stop-color="#040403"></stop><stop offset="1" stop-color="#313031"></stop></linearGradient></defs><path fill="url(#AQ)" d="M631 455c2 3 4 5 5 9l2 2c1 2 1 3 1 4h1v-2c1 3 1 5 1 8 0 1 1 4 1 5h-1-1v3c1 2 0 5 1 6 0 1 1 2 1 3 1 1 2 2 4 2v1c2 3 4 5 5 7h1l1 1v1c-1 1 0 3 0 4l1 3-2 2c0 1-1 1-2 2 0-2 0-4-1-6h0c0-2-1-3-2-4h-1c0-1-1-1-1-2-1-1-1-2-2-4 0 2 0 2 1 3l-3 1c-4-1-6-1-9-1-1 1-1 2-2 2s-2 1-3 2l-2 2-1 2c-1 1-2 2-2 3h-2l-1-2-1 2c-2 5-5 10-5 16-1 0 0 5-2 7l-1-1c-2-2-2-6-1-9h0v-1l-1-1c1-1 1-2 1-3v-1l1-2v-1l1-2c0-2 0-3 1-4 1-2 1-4 2-6 3-7 7-10 13-15 1-1 3-2 4-4 4-7 0-17-1-24l1 1 1-1c0 1 1 1 2 2v-1-1c-1-2-1-3-2-5-1-1-1-2-1-3z"></path><path d="M619 512c4-5 7-7 13-9-1 1-1 2-2 2s-2 1-3 2l-2 2-1 2c-1 1-2 2-2 3h-2l-1-2z" class="M"></path><path d="M631 455c2 3 4 5 5 9l2 2c1 2 1 3 1 4h1v-2c1 3 1 5 1 8 0 1 1 4 1 5h-1-1v3c1 2 0 5 1 6 0 1 1 2 1 3 1 1 2 2 4 2v1c2 3 4 5 5 7h1l1 1v1c-1 1 0 3 0 4l1 3-2 2c0 1-1 1-2 2 0-2 0-4-1-6h0c0-2-1-3-2-4h-1c0-1-1-1-1-2-1-1-1-2-2-4l-3-3c-3 0-6 0-8 1-4 1-8 2-11 5l-1 1v-1c1-1 2-2 4-3l2-1v-1c-1 0-2 1-3 1-1 1 0 1-1 0 1-2 6-5 8-6 4-3 5-7 6-11-1-1-1-2-1-3v-2l-1-4h0c1 0 0 0 2-1 0-1-1-1-1-2l1-1-1-1v-2-1c0-1 0-1-1-2-1-2-1-3-2-5-1-1-1-2-1-3z" class="L"></path><path d="M636 482v-5c1 1 1 5 1 6 0 5-2 6-5 9 0 1-1 1-2 1 4-3 5-7 6-11z" class="e"></path><path d="M631 455c2 3 4 5 5 9l2 2c1 2 1 3 1 4h1v-2c1 3 1 5 1 8 0 1 1 4 1 5h-1-1v3c1 2 0 5 1 6 0 1 1 2 1 3l-1 1h-1v2l-2-2c-1 1-2 1-3 1v-1l3-6h0l-1-5c0-1 0-5-1-6v5c-1-1-1-2-1-3v-2l-1-4h0c1 0 0 0 2-1 0-1-1-1-1-2l1-1-1-1v-2-1c0-1 0-1-1-2-1-2-1-3-2-5-1-1-1-2-1-3z" class="O"></path><path d="M639 488c1 2 1 3 1 4l1 2h-1v2l-2-2v-1c0-2 0-3 1-5z" class="C"></path><path d="M638 488v-5l1 1v4c-1 2-1 3-1 5v1c-1 1-2 1-3 1v-1l3-6h0z" class="B"></path><path d="M631 455c2 3 4 5 5 9 0 1 0 2 1 4 1 1 0 1 0 2h-1v1l1 1 1-1v4 3c1 2 1 4 1 6l-1-1v5l-1-5c0-1 0-5-1-6v5c-1-1-1-2-1-3v-2l-1-4h0c1 0 0 0 2-1 0-1-1-1-1-2l1-1-1-1v-2-1c0-1 0-1-1-2-1-2-1-3-2-5-1-1-1-2-1-3z" class="C"></path><path d="M757 173c2 2 2 2 4 2h5 14l-2 2h2c-3 5-7 10-6 16v2c1 2 1 3 2 5l2 2v1l2 1 1 1c2 2 2 3 5 4l1 2h2c2 3 4 4 4 7 4 3 7 7 10 11l1-1h1l3 4 4 4 5-4v1c0 4 0 6 3 9v1s-1 0-2 1c0 1 1 1 1 2l1 1c1 2 4 6 4 9l-3-3-4-6-2-1v2h-1s0-1-1-1c-1-3-2-5-5-6 0 0-1-1-1-2l-7-8c-11-12-23-23-37-31-6-3-11-6-17-8-5-1-10-2-14-2h-2v-1c1 0 1-1 0-2-1 1-2 1-3 0l2-1v-1h2 2c1 0 2 1 3 1h5 3 1 1l-1-2h2l2-1c2-2 3-3 5-4l2-3 1-3z" class="n"></path><path d="M733 185c1 0 2 1 3 1h5c0 1 1 1 2 2l-2-1c-4 1-7 0-10 2l1 1h-2v-1c1 0 1-1 0-2-1 1-2 1-3 0l2-1v-1h2 2z" class="k"></path><path d="M733 185c1 0 2 1 3 1h5c0 1 1 1 2 2l-2-1h-7c-1-1-2-1-3-2h2z" class="X"></path><path d="M744 186c0 1 1 1 2 1h1v-2h1c2 1 3 2 5 3l3 3c2 2 4 2 6 4-3 0-5-2-8-4-4-1-8-2-11-3-1-1-2-1-2-2h3z" class="a"></path><path d="M756 191h2c1 0 2 2 2 2h2c2 1 2 2 4 2h1 0 2c1 0 1 0 2-1l1 1-2 2h2l2-2c1 2 1 3 2 5l2 2v1l2 1 1 1c2 2 2 3 5 4l1 2h2c2 3 4 4 4 7l-5-5c-2-2-5-3-7-5-7-4-13-9-19-13-2-2-4-2-6-4z" class="q"></path><path d="M767 195h2c1 0 1 0 2-1l1 1-2 2h2l2-2c1 2 1 3 2 5l2 2v1c-2 0-6-3-7-4l-4-4z" class="R"></path><path d="M788 213l5 5c4 3 7 7 10 11l12 17v2h-1s0-1-1-1c-1-3-2-5-5-6 0 0-1-1-1-2l-7-8c0-5-6-9-10-12-1-2-3-3-3-5l1-1z" class="w"></path><path d="M805 228l3 4 4 4 5-4v1c0 4 0 6 3 9v1s-1 0-2 1c0 1 1 1 1 2l1 1c1 2 4 6 4 9l-3-3-4-6-2-1-12-17 1-1h1z" class="Z"></path><path d="M817 247c0-1 0-2-1-3 0 0 0-1-1-2l1 1c1-2 0-2 0-3l2 2h2v1s-1 0-2 1c0 1 1 1 1 2l1 1c1 2 4 6 4 9l-3-3-4-6z" class="W"></path><path d="M757 173c2 2 2 2 4 2h5 14l-2 2h2c-3 5-7 10-6 16v2l-2 2h-2l2-2-1-1c-1 1-1 1-2 1h-2 0-1c-2 0-2-1-4-2h-2s-1-2-2-2h-2l-3-3c-2-1-3-2-5-3h-1v2h-1c-1 0-2 0-2-1h1 1l-1-2h2l2-1c2-2 3-3 5-4l2-3 1-3z" class="Z"></path><path d="M762 183h1l-2 1c1 1 1 1 1 2h-1l-1 1h-3l-1-1c0-1 0-1 1-1v1l1-1c1-1 2-1 4-2z" class="Q"></path><path d="M749 183c2-2 3-3 5-4l-1 3h1l3-1c-1 2-1 3-3 4h0-1-1-1l-2-2z" class="t"></path><path d="M757 173c2 2 2 2 4 2h5 14l-2 2c-1 1-2 1-2 2-1 1-1 2-2 3v1-2h-1l-1-2c-1 1-2 1-3 1l2-3c-1 0-1 1-2 1-3 2-5 2-8 3v-1h-1c-1 0-2 1-3 1l-3 1h-1l1-3 2-3 1-3z" class="N"></path><path d="M757 173c2 2 2 2 4 2h5c1 1 2 1 3 1l-1 1-2-1-2 1h-1c-1 0-2 1-4 2h1s2-1 3-1h0l-2 2h-1c-1 0-2 1-3 1l-3 1h-1l1-3 2-3 1-3z" class="R"></path><path d="M774 183v-1c1-1 1-2 2-3 0-1 1-1 2-2h2c-3 5-7 10-6 16v2l-2 2h-2l2-2-1-1c-1 1-1 1-2 1h-2 0-1c-2 0-2-1-4-2 0-1-1-2-1-3v-2h0c0-1 0-1 1-1l1-1h0v-1c1-2 1-2 3-3h4v-1l1 1v1h1l1-2h1v2z" class="d"></path><path d="M767 195l-2-2v-1h2 1v1h4l1 1c0 1 0 0-1 1l-1-1c-1 1-1 1-2 1h-2 0z" class="G"></path><path d="M763 186c1 1 1 3 3 3l1 1h0 2l-1 2h-1-2v1l2 2h-1c-2 0-2-1-4-2 0-1-1-2-1-3v-2h0c0-1 0-1 1-1l1-1h0z" class="t"></path><path d="M773 181h1v2 1h-2c-1 1-1 2-1 4l-2 2h-2 0l-1-1c-2 0-2-2-3-3v-1c1-2 1-2 3-3h4v-1l1 1v1h1l1-2z" class="L"></path><path d="M766 189h1v-2h0v-2c0-1 1-1 2-2l1 1v1c-1 0-1 0-2 1 1 0 2 1 3 2l-2 2h-2 0l-1-1z" class="I"></path><path d="M643 500c1 2 1 3 2 4 0 1 1 1 1 2h1c1 1 2 2 2 4h0c1 2 1 4 1 6 1-1 2-1 2-2l1 2c-2 5-4 9-6 13h-2l-1-1-2 5-1 4c-2 4-5 8-8 12l-3 5c-1-1-2-2-2-4-2-3-4-6-7-9-1-2-2-6-4-7-1 0-1 0-1-1l-1 1-2-2 1-1-1-1c0-6 3-11 5-16l1-2 1 2h2c0-1 1-2 2-3l1-2 2-2c1-1 2-2 3-2s1-1 2-2c3 0 5 0 9 1l3-1c-1-1-1-1-1-3z" class="o"></path><path d="M627 507c0 1 0 2-1 3s-2 0-2 2c4 5 9 9 13 14 2 2 3 5 5 7l-1 4c-7-9-15-16-23-23l1-2 1 2h2c0-1 1-2 2-3l1-2 2-2z" class="b"></path><path d="M643 500c1 2 1 3 2 4 0 1 1 1 1 2h1c1 1 2 2 2 4h0c1 2 1 4 1 6 1-1 2-1 2-2l1 2c-2 5-4 9-6 13h-2l-1-1c1-3 1-6 1-9 1-6-1-11-4-15l3-1c-1-1-1-1-1-3z" class="D"></path><path d="M646 506h1c1 1 2 2 2 4h0c1 2 1 4 1 6 1-1 2-1 2-2l1 2c-2 5-4 9-6 13h-2c0-2 1-3 2-5h0c1-5 2-9 1-14l-2-4z" class="e"></path><path d="M618 514c8 7 16 14 23 23-2 4-5 8-8 12l-3 5c-1-1-2-2-2-4-2-3-4-6-7-9-1-2-2-6-4-7-1 0-1 0-1-1l-1 1-2-2 1-1-1-1c0-6 3-11 5-16z" class="K"></path><path d="M628 550h0c2 0 2 0 3-1-1-2-3-5-4-6h0v-1c1 1 3 2 3 4h1c1 1 2 2 2 3l-3 5c-1-1-2-2-2-4z" class="i"></path><defs><linearGradient id="AR" x1="858.461" y1="283.878" x2="834.594" y2="291.555" xlink:href="#B"><stop offset="0" stop-color="#0a0b0a"></stop><stop offset="1" stop-color="#282222"></stop></linearGradient></defs><path fill="url(#AR)" d="M816 229l1-1 3 1h0c2 0 3 1 5 1 0 1 3 3 3 3 2 2 5 5 7 6h1c2 1 4 2 6 2v1h1c-3 10 0 18 5 27 2 4 4 7 7 10 1 1 4 3 5 5 1 1-1 9-2 12 0 3 0 7 1 10l1 1c2 8 7 15 12 22h-3c-3-1-6-6-9-9l-7-10c-3-3-6-7-10-10h0l-4-4 1 3h-1c-2-4-2-6-6-8l-3-9-10-25c-2-2-3-6-4-8 2 1 3 3 4 4h1l3 3c0-3-3-7-4-9l-1-1c0-1-1-1-1-2 1-1 2-1 2-1v-1c-3-3-3-5-3-9v-1-1l-1-2z"></path><path d="M835 285c2 0 3 1 4 2s1 2 1 2v1c0 1 1 2 1 2l-5-6-1-1z" class="T"></path><path d="M839 296h0c0-4-2-6-4-9l1-1 5 6 6 8c-1 0-2-1-3-2l-1 2h0l-4-4z" class="m"></path><path d="M830 282s1 1 2 1v1s2 1 3 1h0l1 1-1 1c2 3 4 5 4 9h0l1 3h-1c-2-4-2-6-6-8l-3-9z" class="j"></path><path d="M843 300l1-2c1 1 2 2 3 2 5 5 9 11 13 17 3 4 6 9 9 12-3-1-6-6-9-9l-7-10c-3-3-6-7-10-10z" class="Q"></path><path d="M846 289c1-2 0-3-1-5v-2c2 1 4 4 5 7 0 1 0 1 1 2l1-1-1-3h1c1 1 1 3 2 4-1 0-1 0-2-1 1 4 1 6 2 10 0 1 1 3 2 5-2-2-5-5-5-7-1-3-3-6-5-9z" class="O"></path><path d="M820 257c2 1 3 3 4 4l6 9c-1 0-1 0-2-1v1h0c1 1 1 2 1 2 1 3 2 6 4 8l-1 2c1 0 1 0 2 1 0 0 1 1 1 2-1 0-3-1-3-1v-1c-1 0-2-1-2-1l-10-25z" class="H"></path><path d="M831 251c1 0 2-1 3-1 0 2 0 2 1 3h1c1 2 1 4 2 7s2 6 4 9c-2 0-2-1-3-3h-3l-2-1c0-1 0-3-1-4-1 0-1-1-1-1 0-1 0-2-1-3 0-1-1-1-1-2-1-2 0-2 0-3h1v-1z" class="N"></path><path d="M833 261c1 1 2 1 3 2v-1h0l-2-2h0l1-1 1 1c1 1 2 2 2 3 0 2 1 2 1 3h-3l-2-1c0-1 0-3-1-4z" class="B"></path><path d="M827 236l1-3c2 2 5 5 7 6h1c2 1 4 2 6 2v1c-3 1-5 3-6 6v5h-1c-1-1-1-1-1-3v-4c-1-1-2-1-3-1l-1-1 1-2c0-1-1-2-2-3h0l1-1-3-2z" class="U"></path><path d="M820 243h2 2c2 0 4-2 5-4 1 1 2 2 2 3l-1 2 1 1c1 0 2 0 3 1v4c-1 0-2 1-3 1v1h-1c0-1-1-1-2-2h0c-2-2-3-2-6-2v-1l-1-1-1 1-1-1c0-1-1-1-1-2 1-1 2-1 2-1z" class="Q"></path><path d="M831 245c1 0 2 0 3 1v4c-1 0-2 1-3 1-1-1-3-2-4-4 1-1 2-1 3-2h1z" class="D"></path><path d="M820 257c-2-2-3-6-4-8 2 1 3 3 4 4h1l3 3c1 3 4 6 5 9 1 2 3 4 5 6 1 1 0 0 0 1l2 1c1 1 1 2 1 3l4 5v1c2 2 3 5 5 7 2 3 4 6 5 9l-21-28-6-9c-1-1-2-3-4-4z" class="X"></path><path d="M836 266h3c1 2 1 3 3 3l10 18h-1l1 3-1 1c-1-1-1-1-1-2-1-3-3-6-5-7v2c1 2 2 3 1 5-2-2-3-5-5-7l1-1-1-2c-3-4-3-9-5-13z" class="H"></path><path d="M816 229l1-1 3 1h0c2 0 3 1 5 1 0 1 3 3 3 3l-1 3 3 2-1 1h0c-1 2-3 4-5 4h-2-2v-1c-3-3-3-5-3-9v-1-1l-1-2z" class="l"></path><path d="M816 229l1-1 3 1h0c1 1 3 2 3 3l-3-2c-2 1-2 2-3 3v-1-1l-1-2z" class="q"></path><path d="M820 229c2 0 3 1 5 1 0 1 3 3 3 3l-1 3-4-4c0-1-2-2-3-3z" class="d"></path><path d="M820 247l1-1 1 1v1c3 0 4 0 6 2h0c1 1 2 1 2 2s-1 1 0 3c0 1 1 1 1 2 1 1 1 2 1 3 0 0 0 1 1 1 1 1 1 3 1 4l2 1c2 4 2 9 5 13l1 2-1 1v-1l-4-5c0-1 0-2-1-3l-2-1c0-1 1 0 0-1-2-2-4-4-5-6-1-3-4-6-5-9 0-3-3-7-4-9z" class="m"></path><path d="M822 248c3 0 4 0 6 2h0-5l-1-2z" class="j"></path><path d="M691 289l-3-3c0-2 2-3 3-4 4-4 8-8 12-10 11-7 26-9 38-6 11 3 20 10 25 19 5 10 8 20 6 31-2 5-8 10-13 13-6 3-12 4-19 2s-12-5-17-9c-2-2-4-4-6-5s-3-2-4-4c-2-3-3-7-5-11-2-2-4-3-5-5l-3-3-6-3c-1-1-2-2-3-2z" class="x"></path><path d="M556 145c0-2 1-4 1-6l1-1c0-1 0-2 1-3 1-2 2-2 3-3l-2 6c1 1 2 1 2 2 1 0 1 3 1 3 1 5 4 9 8 12h0c2 2 4 3 6 4 3 2 7 4 11 5 3 0 6 1 8 2h-1v2c-2 1-8-2-10-2 1 1 3 2 4 4h1l2 2v1c2 0 4 2 6 2 2 1 5 2 7 3h1c1 1 2 1 3 1h2v1h-3 0l7 2h2 1l2-1h3v1c1-1 0-1 1-1h1v-1c-1 0-1 0-2-1h0l3 1c1 1 2 1 4 1h1 0 3l-1 2h3c2-1 4-1 6-1h2l3-1s0 1-1 1v1h0c4-1 6-2 9-2l2-1 1 1c0 2-2 4-4 5h0l1 1h0 2c2-1 3-1 5 0h0 4 3v2l-1 1c1 0 1 0 2 1v4l1 2v1l-2 1-1 1c1 2 2 3 3 4v1h-1-1c1 1 1 2 2 2l-1 1h-1c-2 1-2 3-3 5l-1 2v11 3c0 1 1 2 1 4l1 1-2 3c0 2 1 5 1 7 1 2 2 3 2 6l1 6-1 4v2h-1c-1 1-1 2-2 2v5 2 1h-1c-1-2-1-3-1-4l-2 1c0 2 1 3 1 6 0 0 1 1 1 2h0c0 2 0 3 1 5h1v1h-2l-1 1-4-1h-1c-1 1-2 1-3 1v1l-1 3c-2 5-4 10-5 15-1 4-1 7-3 11h0c-1 1 0 1-1 1v1c0 1-1 2-1 2v1 1l-1 1v2c-1 1-1 3-2 4v2c-1 0-1 2-1 3h0c0 1-1 3-1 4l-1 1v2c0 1 0 0-1 1v1 1 1h-1v2h0l-2 5c0 1 0 2-1 3s-2 3-3 5c-1 3-2 7-4 10-1 1-1 2-1 3-1 1-1 2-2 4h0c-1 3-1 7-3 9v1h-1 0v-3-1c-1 2-2 3-2 5-1 1-2 1-3 1h0l-1 1 4 7h-2l-1 1c-5-5-8-11-13-17-1 1-1 2-1 3h0c-1 0-2 0-3 1h-1c-1 0-1 0-1-1v-2c0-1 0-2 1-2 0-1 1-2 1-3v-2l1-2h1v-2h1c1-4 1-8 2-11l3-9 3-7c2-3 6-10 9-12-4 10-7 18-8 28h0c0 6 0 11 1 16l8-29c0-1 1-2 1-3l3-8 4-17c3-10 4-20 3-31 0-2-1-6-1-8v-1c-3-3-4-11-5-14-1-5-4-10-6-15 0-1-1-3-1-3 0-1 1-2 1-2l1-2 3-3 5-5s-1 0-2-1l-1-2c1-3 4-5 4-8-7-15-24-19-37-27-7-4-14-10-20-16-5-6-11-14-12-23-1-3-1-7-1-11z" class="v"></path><path d="M626 222c1 4 1 8 0 12l-1-1s-1 0-2-1l-1-2c1-3 4-5 4-8z" class="Y"></path><path d="M622 263c2-1 2-1 2-2 1 5 4 11 3 16-3-3-4-11-5-14z" class="h"></path><path d="M639 230l1-3h0v-2h0l1-2c-1-1-1-1-1-2 0-3 0-7 2-9v1c1 0 2 1 2 2 1 1 1 2 1 4l-1-1v-1h-1v1 4l-1 2-3 6z" class="g"></path><path d="M642 224c-2-2-1-7-1-9l1-1v4h1v4l-1 2z" class="f"></path><path d="M651 231l1 1c-1 2-3 3-5 4l2 1c0 1 0 1-1 2h-1 1l-1 1c-1 0-2-1-2-2l-1 1 1 1-1 1h-1l-1-1-2 1h-1l-1-1 2-3c4-1 6-1 9-3h0c2-1 2-2 2-3z" class="E"></path><path d="M645 238v-1l1-1 1 1v1 1h1l-1 1c-1 0-2-1-2-2z" class="H"></path><path d="M657 234c0 5 0 11-1 16h0v-3-1c-1 1-1 2-2 3l-1 1h0c-1 2-2 4-4 5v-3c-1-5-5-8-9-11l2-1 1 1h1l1-1-1-1 1-1c0 1 1 2 2 2l1-1h-1 1c1 1 2 1 2 3v1c-1 0-2 0-2-1h-1c0 1-1 1-2 2 3 3 4 5 5 9h1v-1-2c1-1 1-1 1-2h1c0-1 1-2 1-2 1-1 1-2 2-2v-2c1-3 0-6 1-8z" class="B"></path><path d="M645 200c5 5 7 13 9 20 1 5 3 9 3 14-1 2 0 5-1 8v-9c-1-1-2-2-2-4h-1v-1c0-2 0-1-1-2 0-1-1-2-2-4 1-2 1-3 0-5v-1l1 1c-1-3-1-5-2-8-2-3-4-6-4-9z" class="D"></path><path d="M638 253l2-2c1 1 2 2 1 4v1 1c1 0 1 1 3 2h3l-1 1c-2 2-5 5-9 5h0-3-1 0c-2-1-3-2-5-3l1-1c4-2 6-4 9-8z" class="B"></path><path d="M633 265l1-1h1l-1-1v-1-1h-1c1-1 1-1 2-1v1h1l1-1-2-1h1c1 0 2-1 3-1l1 1v2c-1 0-2 1-3 2l-1 1 1 1h0-3-1 0z" class="c"></path><path d="M639 258l2-1c1 0 1 1 3 2h3l-1 1c-2 2-5 5-9 5l-1-1 1-1c1-1 2-2 3-2v-2l-1-1z" class="E"></path><path d="M640 259l1-1 1 1-2 2v-2z" class="B"></path><path d="M640 249v-4c-1-1-1-2-2-3s0-1 0-2l1 1h1c4 3 8 6 9 11v3 2l-2 2h-3c-2-1-2-2-3-2v-1-1c1-2 0-3-1-4l-2 2c1-1 2-3 2-4z" class="l"></path><path d="M640 249l1-3h1v3c1 2 1 3 1 5l-2 2v-1c1-2 0-3-1-4l-2 2c1-1 2-3 2-4z" class="D"></path><path d="M643 218v-1h1v1l1 1h1v1c0 1 1 1 1 2 1 1 1 3 0 4 0 2 0 2 1 3v1c1 0 2 0 3 1 0 1 0 2-2 3h0c-3 2-5 2-9 3l-2-1c-2 0-2 1-3 2l-1-1c2-2 4-4 5-6v-1l3-6 1-2v-4z" class="B"></path><path d="M643 218v-1h1v1l1 1h1v1c0 1 1 1 1 2 1 1 1 3 0 4 0 2 0 2 1 3v1h-1c-1-1 0-3-1-5h0c-1 1-2 1-3 2v-4-1-4z" class="D"></path><path d="M643 218v-1h1v1c0 2 0 3 1 5v1h2s0 1-1 1h0c-1 1-2 1-3 2v-4-1-4z" class="T"></path><path d="M638 236c1 0 1-1 1-1 1-3 3-5 5-6h1v2c1 1 1 2 1 3h3c-3 2-5 2-9 3l-2-1z" class="H"></path><path d="M641 273c3 2 8 4 11 7 1 1 2 2 2 4v1l-1 3c-2-1-4-2-5-3-5-3-10-7-15-8-1 0-2-1-2-1 1-2 3-2 5-2l2-1h3z" class="T"></path><path d="M637 265c4 0 7-3 9-5l1 2c1 0 2 0 3 1l-1 3c1 1 0 3 2 4h2v3c0 2 0 4-1 7-3-3-8-5-11-7v-1c-2-2-5-5-8-7h1 3 0z" class="G"></path><path d="M641 272h3v-1c1 1 2 1 3 2l4 2 2-2c0 2 0 4-1 7-3-3-8-5-11-7v-1z" class="b"></path><path d="M616 369l-1-1v-1l-1-1 1-1v-2l1-2c0-1 1-3 1-4l1-1c1 2-2 9-3 11 1-1 2-3 3-4v-1-1c0-1 1-2 1-3l1-2c1-1 1-1 1-2l1-3v-1h-1c0-1 0-2 1-3 0-1 0-2 1-3 0-1 0-3 1-4l1 1c0 1 1 1 0 2l1 1h0v-2c0-1 0-2 1-3h0c0-1 0-2 1-3v-3l2-1 1 1c-1 3-3 6-3 10v3c-1 3-1 5-1 8-3 3-5 8-5 11-1 2-1 3-2 4l-1 1c-2 3-2 7-5 10 0-1 0-2 1-3 0-1 0-1-1-2v-2h1l-2-1c0-1 1-2 1-3 2 0 0 1 1 2 0-1 0-1 1-2z" class="E"></path><path d="M616 369s1 0 1-1l3-7c1-2 1-3 2-4h0c1 1 0 1 0 3v1l-1 1v2c-1 1-1 3-1 4v1l-1 1c-2 3-2 7-5 10 0-1 0-2 1-3 0-1 0-1-1-2v-2h1l-2-1c0-1 1-2 1-3 2 0 0 1 1 2 0-1 0-1 1-2z" class="B"></path><path d="M626 234c2 1 3 2 5 4 1 4 2 9 1 13v1c-1 4-5 6-8 9 0 1 0 1-2 2-1-5-4-10-6-15 0-1-1-3-1-3 0-1 1-2 1-2l1-2 3-3 5-5 1 1z" class="x"></path><path d="M623 179l3 1c1 1 2 1 4 1h1 0 3l-1 2h3c2-1 4-1 6-1h2l3-1s0 1-1 1v1h0c4-1 6-2 9-2l2-1 1 1c0 2-2 4-4 5h0c-1-1-1-2-3-2v1l-1 1v3l1 2-1-1-3 2-1-1-1 1v1c3 2 4 5 6 8 2 6 5 12 7 18l-1-1c-2-2-2-5-3-7l-1 2c1 2 2 4 2 7h0-1c-2-7-4-15-9-20l-2-2c-3-5-10-7-15-9-2 0-6-2-8-3h3 3c2 1 2 2 4 1-1 0-2-1-3-1s-1-1-2-1v-1c-3-1-5-1-8-2h1l2-1h3v1c1-1 0-1 1-1h1v-1c-1 0-1 0-2-1h0z" class="n"></path><path d="M625 184l3 1c4 1 8 3 12 3l3 1h0v1 1h1-3l-1-1c-1 1-6 0-8-1h-4c-2 0-6-2-8-3h3 3c2 1 2 2 4 1-1 0-2-1-3-1s-1-1-2-1v-1z" class="P"></path><path d="M623 179l3 1c1 1 2 1 4 1h1 0 3l-1 2h-1c0 1 1 2 2 2h1l1 1h1c2 1 2 1 3 2h0c-4 0-8-2-12-3l-3-1c-3-1-5-1-8-2h1l2-1h3v1c1-1 0-1 1-1h1v-1c-1 0-1 0-2-1h0z" class="Z"></path><path d="M617 182h1l2-1h3v1c2 0 3 0 5 1h2v1c-1 0-2 0-2 1l-3-1c-3-1-5-1-8-2z" class="X"></path><path d="M655 181l2-1 1 1c0 2-2 4-4 5h0c-1-1-1-2-3-2v1l-1 1v3l1 2-1-1-3 2-1-1h-2-1v-1-1h0l-3-1h0c-1-1-1-1-3-2h-1l-1-1h-1c-1 0-2-1-2-2h1 3c2-1 4-1 6-1h2l3-1s0 1-1 1v1h0c4-1 6-2 9-2z" class="j"></path><path d="M655 181l2-1 1 1c0 2-2 4-4 5h0c-1-1-1-2-3-2v1c-2 1-3 1-5 1v-1c3-1 6-2 9-4z" class="a"></path><path d="M633 183h3c2-1 4-1 6-1l-5 2v1h2 2l1 1h2c0-1 1-1 2-1v1l-6 2c-1-1-1-1-3-2h-1l-1-1h-1c-1 0-2-1-2-2h1z" class="Q"></path><path d="M651 185l-1 1v3l1 2-1-1-3 2-1-1h-2-1v-1-1h0l-3-1h0l6-2c2 0 3 0 5-1z" class="w"></path><path d="M643 189h4 0c0-1 0-1 1-1s1 1 2 2l-3 2-1-1h-2-1v-1-1z" class="K"></path><path d="M607 342c2-3 6-10 9-12-4 10-7 18-8 28h0c0 6 0 11 1 16l8-29v5 2h0l-1-1c-1 2-1 4-2 7 0 1 0 2-1 3l-2 8c0 1-1 3-1 4l-1 1c-1 0-1 0-1-1 0 2 0 3 1 4l2 5v-10h1c1 2 0 2 0 4h1l1-1c1 1 1 1 1 2-1 1-1 2-1 3 3-3 3-7 5-10 0 2-1 4-1 6h0 1l1 4v-2l1-1c0-1 0-1 1-2-1 3-1 7-3 9v1h-1 0v-3-1c-1 2-2 3-2 5-1 1-2 1-3 1h0l-1 1 4 7h-2l-1 1c-5-5-8-11-13-17-1 1-1 2-1 3h0c-1 0-2 0-3 1h-1c-1 0-1 0-1-1v-2c0-1 0-2 1-2 0-1 1-2 1-3v-2l1-2h1v-2h1c1-4 1-8 2-11l3-9 3-7z" class="D"></path><path d="M598 369h1v8h0v-3c-1 0-2 1-3 1v-2l1-2h1v-2z" class="p"></path><path d="M614 375c1 1 1 1 1 2-1 1-1 2-1 3h0c0 2 0 3-1 4-1-1-1-5-1-7v-1h0 1l1-1z" class="C"></path><path d="M596 375c1 0 2-1 3-1v3h0l-3 6h-1c-1 0-1 0-1-1v-2c0-1 0-2 1-2 0-1 1-2 1-3z" class="Y"></path><path d="M614 395c-2-3-5-6-7-8 0-1 0-1-1-2 0-1-1-3-1-4v-1c-1 0-1-1-1-2v-1l1 1c0 1 1 3 2 4v1l1 2v1l3 3v-2c-2-2-3-4-4-7l-1-5c0-1 0 0-1-1 0-1 0-2 1-3 1 1 1 3 1 4 1 2 1 3 2 4l2 4c0 1 1 3 2 4h0l-1 1 4 7h-2z" class="S"></path><path d="M607 342c2-3 6-10 9-12-4 10-7 18-8 28h0c-2 3-1 6-1 9h0c0-1 0-1-1-2h-1v-4 6c-2 1 0 4-1 6-1-1-2-2-1-4v-7c0-2 1-3 0-5v3 1l-1 2-1-1 1-4h-1l3-9 3-7z" class="E"></path><path d="M651 185v-1c2 0 2 1 3 2l1 1h0 2c2-1 3-1 5 0h0 4 3v2l-1 1c1 0 1 0 2 1v4l1 2v1l-2 1-1 1c1 2 2 3 3 4v1h-1-1c1 1 1 2 2 2l-1 1h-1c-2 1-2 3-3 5l-1 2v11 3h-1l-1-5-1 1-3-3-1-3c-2-6-5-12-7-18-2-3-3-6-6-8v-1l1-1 1 1 3-2 1 1-1-2v-3l1-1z" class="e"></path><path d="M660 200h1c0 2 0 3-1 5h0c1 1 1 2 1 3-1 2-2 2-3 3-1 0-1-1-2-1v-1c0-2-1-3-2-5-1-1-1-2-1-3 2 1 2 2 2 4 1 1 1 1 2 1h0c-1-2-1-2-1-4h1v1h1v-1c0-1 1-1 2-2z" class="W"></path><path d="M661 209c0 1 0 2 1 3v1h1l1 1 1 1v11 3h-1l-1-5c-1-3-3-7-3-10 0-1 0-1-1-2v1h-1c1-1 2-2 2-3l1-1z" class="a"></path><path d="M668 200c1 2 2 3 3 4v1h-1-1c1 1 1 2 2 2l-1 1h-1c-2 1-2 3-3 5l-1 2-1-1-1-1h-1v-1c-1-1-1-2-1-3l5-7 2-2z" class="X"></path><path d="M662 212c2-3 5-4 7-7 1 1 1 2 2 2l-1 1h-1c-2 1-2 3-3 5l-1 2-1-1-1-1h-1v-1z" class="u"></path><path d="M651 185v-1c2 0 2 1 3 2l1 1h0 2c2-1 3-1 5 0h0 4 3v2l-1 1c1 0 1 0 2 1v4l1 2v1l-2 1-1 1-2 2h0-3c0-1 1-2 2-4h-1-1c-1 0-2-1-2-1l-2 2c-1-1-2-1-3-1l-1-1h-2c-3-1-5-3-6-5l3-2 1 1-1-2v-3l1-1z" class="D"></path><path d="M661 197s1-1 2-1l1-2h1v4h0-1-1c-1 0-2-1-2-1z" class="B"></path><path d="M651 191l4-1 7 1c-1 2-2 3-3 5h1c-1 0-1 1-2 1h-2-1-2c-3-1-5-3-6-5l3-2 1 1z" class="l"></path><path d="M651 185v-1c2 0 2 1 3 2l1 1h0 2c2-1 3-1 5 0h0 4 3v2l-1 1h-3l-3 1-7-1-4 1-1-2v-3l1-1z" class="h"></path><path d="M666 187h3v2l-1 1h-3l-3-3h4z" class="K"></path><path d="M651 185v-1c2 0 2 1 3 2l1 1h0 2 0c-1 1-2 1-3 1l-2 1v1h3l-4 1-1-2v-3l1-1z" class="M"></path><defs><linearGradient id="AS" x1="587.084" y1="177.947" x2="595.856" y2="159.924" xlink:href="#B"><stop offset="0" stop-color="#e4c9bf"></stop><stop offset="1" stop-color="#f2e9dc"></stop></linearGradient></defs><path fill="url(#AS)" d="M556 145c0-2 1-4 1-6l1-1c0-1 0-2 1-3 1-2 2-2 3-3l-2 6c1 1 2 1 2 2 1 0 1 3 1 3 1 5 4 9 8 12h0c2 2 4 3 6 4 3 2 7 4 11 5 3 0 6 1 8 2h-1v2c-2 1-8-2-10-2 1 1 3 2 4 4h1l2 2v1c2 0 4 2 6 2 2 1 5 2 7 3h1c1 1 2 1 3 1h2v1h-3 0l7 2h2c3 1 5 1 8 2v1c1 0 1 1 2 1s2 1 3 1c-2 1-2 0-4-1h-3-3l-17-3c-12-3-26-7-35-15-7-7-11-13-12-23z"></path><path d="M588 174l1-2h3v1c2 0 4 2 6 2 2 1 5 2 7 3h1c1 1 2 1 3 1h2v1h-3 0-4l-11-3-2-1c-1-1-2-1-3-2z" class="k"></path><path d="M588 174l1-2h3v1h-1c1 1 1 2 2 3v1l-2-1c-1-1-2-1-3-2z" class="X"></path><path d="M591 176c-12-3-21-8-27-18-5-7-6-11-4-20 1 1 2 1 2 2 1 0 1 3 1 3 0 2-1 5 0 6 0 3 1 5 3 7l4 7c1 3 5 6 7 7l2 1 2 1 7 2c1 1 2 1 3 2z" class="D"></path><path d="M563 149c-1-1 0-4 0-6 1 5 4 9 8 12h0c2 2 4 3 6 4 3 2 7 4 11 5 3 0 6 1 8 2h-1v2c-2 1-8-2-10-2 1 1 3 2 4 4h1l2 2h-3l-1 2-7-2-2-1-2-1c-2-1-6-4-7-7l-4-7c-2-2-3-4-3-7z" class="m"></path><path d="M563 149c-1-1 0-4 0-6 1 5 4 9 8 12h0c2 2 4 3 6 4-3 0-6-1-8-3-1 0-1-1-2-1-2-1-2-4-4-5v-1z" class="H"></path><path d="M566 156h1v1c2 2 5 6 7 6 3 0 9 1 11 3h-4-5c0 2 1 3 1 4-2-1-6-4-7-7l-4-7z" class="Q"></path><path d="M576 166h5 4c1 1 3 2 4 4h1l2 2h-3l-1 2-7-2-2-1-2-1c0-1-1-2-1-4z" class="W"></path><path d="M583 168l1 1-3 3-2-1c1-2 2-2 4-3z" class="V"></path><path d="M576 166h5 4c1 1 3 2 4 4l-5-1-1-1c-1-1-2-1-4-1-1 0-2 0-3-1z" class="d"></path><path d="M584 169l5 1h1l2 2h-3l-1 2-7-2 3-3z" class="a"></path><path d="M654 220h1 0c0-3-1-5-2-7l1-2c1 2 1 5 3 7l1 1 1 3 3 3 1-1 1 5h1c0 1 1 2 1 4l1 1-2 3c0 2 1 5 1 7 1 2 2 3 2 6l1 6-1 4v2h-1c-1 1-1 2-2 2v5 2 1h-1c-1-2-1-3-1-4l-2 1c0 2 1 3 1 6 0 0 1 1 1 2h0c0 2 0 3 1 5h1v1h-2l-1 1-4-1h-1c-1 1-2 1-3 1 0-2-1-3-2-4 1-3 1-5 1-7v-3h-2c-2-1-1-3-2-4l1-3c-1-1-2-1-3-1l-1-2 1-1 2-2v-2c2-1 3-3 4-5h0l1-1c1-1 1-2 2-3v1 3h0c1-5 1-11 1-16s-2-9-3-14z" class="w"></path><path d="M659 222l3 3 1-1 1 5h1c0 1 1 2 1 4l1 1-2 3v-3c-1 2-2 3-3 4l-1 2c0-6-1-12-2-18z" class="Q"></path><path d="M662 238c0-1 0-2 1-3v-1-3h1l1 3c-1 2-2 3-3 4z" class="F"></path><path d="M664 229h1c0 1 1 2 1 4l1 1-2 3v-3l-1-3v-2z" class="d"></path><path d="M657 267c1 2 0 4 1 5 1 2 1 2 4 3 0 0 1 1 1 2h0c0 2 0 3 1 5h1v1h-2l-1 1-4-1h-1v-7-9z" class="V"></path><path d="M657 276l2 2h2c-1 2-2 2-3 4-1 0 0 1 0 1h-1v-7z" class="X"></path><path d="M649 255c2-1 3-3 4-5h0l1-1c1-1 1-2 2-3v1 3h0c0 5-1 10-2 15l-1 5h-2c-2-1-1-3-2-4l1-3c-1-1-2-1-3-1l-1-2 1-1 2-2v-2z" class="N"></path><path d="M649 257c2-1 2-1 3-3h0l1 1c0 1-1 5-1 6v1c-1 1-1 2-1 4 0 1-1 2 0 3 1-1 1-2 2-3v-1h1l-1 5h-2c-2-1-1-3-2-4l1-3c-1-1-2-1-3-1l-1-2 1-1 2-2z" class="F"></path><defs><linearGradient id="AT" x1="668.543" y1="256.563" x2="656.939" y2="251.258" xlink:href="#B"><stop offset="0" stop-color="#454141"></stop><stop offset="1" stop-color="#6b6160"></stop></linearGradient></defs><path fill="url(#AT)" d="M662 238c1-1 2-2 3-4v3c0 2 1 5 1 7 1 2 2 3 2 6l1 6-1 4v2h-1c-1 1-1 2-2 2v5 2 1h-1c-1-2-1-3-1-4l-2 1c0 2 1 3 1 6-3-1-3-1-4-3-1-1 0-3-1-5l4-27 1-2z"></path><path d="M660 267c1-1 1-1 3-2v1 2l-2 1-1-2z" class="L"></path><path d="M658 272c1-2 1-3 2-5l1 2c0 2 1 3 1 6-3-1-3-1-4-3zm7-26l1-2c1 2 2 3 2 6l1 6-1 4v2h-1-1c-1 0-1 0-1-1h0-1l-1-1c1-1 2-4 1-5v-1c0-2 0-7 1-8z" class="S"></path><path d="M665 246l1-2c1 2 2 3 2 6l1 6-1 4v-2c-1-2-1-3-2-5-1-3-1-5-1-7z" class="F"></path></svg>