<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="200 119 654 811"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#302f2d}.C{fill:#474644}.D{fill:#43413f}.E{fill:#52504d}.F{fill:#595651}.G{fill:#54514c}.H{fill:#9d9788}.I{fill:#201f1e}.J{fill:#aca495}.K{fill:#938c7d}.L{fill:#736f67}.M{fill:#5a5955}.N{fill:#3d3c3b}.O{fill:#141311}.P{fill:#393836}.Q{fill:#6c6a63}.R{fill:#7e786c}.S{fill:#262424}.T{fill:#353534}.U{fill:#bdb5a7}.V{fill:#67635b}.W{fill:#a09a8c}.X{fill:#c2baac}.Y{fill:#2b2a29}.Z{fill:#c0b7a4}.a{fill:#413e39}.b{fill:#d7cfc0}.c{fill:#7b776e}.d{fill:#beb6a5}.e{fill:#d4ccbf}.f{fill:#9d9586}.g{fill:#8c8371}.h{fill:#b5ad9d}.i{fill:#837f76}.j{fill:#d9d3c6}.k{fill:#b3a996}.l{fill:#918a7c}.m{fill:#1c1b1b}.n{fill:#010000}.o{fill:#cbc4b3}.p{fill:#686763}.q{fill:#e0dace}.r{fill:#f4f1e3}</style><path d="M662 661h3v2c-2-1-3-1-5-1l2-1zm-87-172c1 0 1-1 1-1 2-1 7-1 9 0l-1 1h0-9z" class="b"></path><path d="M650 660l12 1-2 1c-3 0-8 0-11-2h1z" class="q"></path><path d="M356 661l-1 3c-4-1-9-1-14-1l1-1c3 0 9 0 13-1h1z" class="D"></path><path d="M705 567c1 0 0 0 1 1l-1 1c-1 2-2 4-4 5-3 1-4 1-7-1-1 0-1 0-1-1h1c3 1 4 1 6 0 1 0 2-2 2-3 1-1 2-1 3-2z" class="L"></path><path d="M555 481l4-8 1 12-2-1v-1c-2 0-3 0-4 1l1-3z" class="F"></path><path d="M415 690c0 2 0 4-2 5l-1-1c-1-4 1-10 3-13h1c-1 3-1 5-1 9z" class="E"></path><path d="M575 489h9c-3 2-6 0-8 2v1h-9l-1-1h0v-1l3-1c2-1 4 0 6 0z" class="W"></path><path d="M366 654c1-1 4-1 6-1h13l-4 1v2h2 0-2c-2 0-3 0-4-1-1 0-2 1-3 1-3 0-5-1-8-1v-1z" class="j"></path><path d="M264 505c3 0 7 1 10-1v2 2c-2-1-5 0-7 0l-16-1h4 6 4c-2-1-4 0-6 0h-1c-2-2-5-1-7-1 4 0 9 0 13-1h0z" class="e"></path><path d="M649 660c3 2 8 2 11 2 2 0 3 0 5 1-6 1-13 0-19 1-3-1-7 0-10-2 3 0 10 1 13-2h0z" class="a"></path><path d="M762 506c1-2 2-4 4-5s4-1 5-1c3 1 5 3 6 5v2h-1c0-1 0-2-1-3-2-2-4-1-6-1h-1l-1 1c-1 0-1 1-1 2l-1 1v3l-2-2c0-1 0 0-1-1v-1z" class="c"></path><path d="M504 932v-6c2 0 2 0 3 1 0 3 0 6 1 8l-1 12-3-15z" class="C"></path><path d="M424 673c1-1 2-1 4-2l-1 1c-1 1-2 1-3 2s-1 2-1 3c-1 2-3 4-4 6h0 0c-1 3-3 4-4 7 0-4 0-6 1-9s3-5 5-7c2 0 2 0 3-1z" class="U"></path><path d="M657 418c1-5 2-14 5-18h1c1 4 0 8-1 11-1 2-2 6-4 7h-1 0z" class="B"></path><path d="M561 699l31 2c-4 0-11-1-14 1h-6c-4 0-8 0-12-1 0-1 1-1 1-2z" class="e"></path><path d="M284 543l-13 10c5-8 12-14 19-20 3-2 5-4 8-5 0 1-3 3-4 4-3 1-9 5-11 8v1 2h1z" class="R"></path><path d="M548 482c1 0 0 0 1 1l1 1c1-5-2-11 0-15l1 10c1 1 2 2 4 2l-1 3-2 4-1 2h0-1-2l-1-1v-1c0-1-1-1-1-2 0-2-1-2-2-2 1-1 2-1 3-1h1v-1z" class="M"></path><path d="M551 479c1 1 2 2 4 2l-1 3-2 4c-2-2-1-6-1-9z" class="q"></path><path d="M570 570c2-2 5-3 7-5 2-1 3-3 5-3-6 7-13 12-20 17v-6l4-2 4-1z" class="S"></path><path d="M219 510l-57-2h58l-1 2z" class="a"></path><path d="M258 507h-8 0c-1-1-6-1-7-1-1 1-2 0-3 1h-1-9v-1h-12-1c-1-1-4 0-5 0h-1-16-7l76-1h0c-4 1-9 1-13 1 2 0 5-1 7 1z" class="j"></path><path d="M367 495c-3 0-21 0-23-1v-3h2c7 1 16 1 23 0v3l-2 1z" class="a"></path><path d="M675 524l22 1c4 0 8 0 12 1-5 1-11 1-16 1l-18 1-1-1c0-1 0-2 1-3z" class="O"></path><path d="M592 701c5-1 12-1 18-1 2 1 5 0 7 1v1 2c-13-2-26-1-39-2 3-2 10-1 14-1z" class="q"></path><path d="M765 510v-3l1-1c0-1 0-2 1-2l1-1h1c2 0 4-1 6 1 1 1 1 2 1 3-1 2-2 4-4 5h-5c0-1-1-1-2-2z" class="g"></path><path d="M729 502c8 1 16 2 23 1h3v1h0v1h1c2 0 4 0 6 1v1 1c-1 0-1-1-2-1h-5v2l-3-2c-1 1-2 0-3 0l-5-2h-2c-1-1-2-1-3-1h-7l-3-2z" class="f"></path><path d="M739 504c3 0 11-1 13 0v2 1c-1 1-2 0-3 0l-5-2h-2c-1-1-2-1-3-1z" class="b"></path><path d="M744 505c2 0 5 0 7 1h1v1c-1 1-2 0-3 0l-5-2z" class="q"></path><path d="M377 182c1-2 2-3 3-5 11-2 23-1 35-1 13 0 26 0 39 1h-18c-2 0-4 1-6 1h-37c-2 0-9-1-11 1h-2 0l-3 3z" class="U"></path><path d="M346 491h7 10v-1h-12-4v-1c7 1 14 0 21 0v-1h-3l21-2c-3 3-5 3-8 3-1 1-3 1-4 1l-5 1c-7 1-16 1-23 0z" class="j"></path><path d="M608 810c4 2 10 5 14 8l-18 1-1-1c2-1 7-1 8-2-4 0-11 2-15 0l2-1c3 0 6-1 10-1h0l1-1-1-3z" class="S"></path><path d="M374 490v1l-2 1v1l2 1c-1 1 0 1-1 1 0 1 0 1 1 2h0 1v1c1 1 1 1 2 1 0 1 0 1-1 2l-3 2h0c-2-1-2 0-3 0l-1 1v2l-1 1v-1h-3c0-1-1-1-2-1s-1 0-2-1h5v-3l1-6 2-1v-3l5-1z" class="b"></path><path d="M374 490v1l-2 1v1l2 1c-1 1 0 1-1 1 0 1 0 1 1 2h0c-2 0-4 0-6-1-1 2-1 6 0 9v1h-3c0-1-1-1-2-1s-1 0-2-1h5v-3l1-6 2-1v-3l5-1z" class="D"></path><path d="M390 702h0-1c1-1 1 0 1-1h1-1l51-2v1 1c-1 1-3 1-4 1-9 1-18 0-27 0-6 1-14 1-20 0z" class="q"></path><path d="M441 700v1c-1 1-3 1-4 1-2-1-3-1-4-1h-1-1c2-1 7-1 10-1z" class="b"></path><path d="M777 505h7 11 19c4 0 8 0 12 1-1 1-6 0-8 0s-9 0-10 1h-3-5 1 17 12 11c5 0 9 0 13 1h2 0c-1 1-2 1-3 0h-23-36l-12-1c-2 0-4 1-5 0v-2z" class="e"></path><path d="M358 659l2 1h17c-2 1-4 1-5 1 1 0 2 1 4 1 5 0 12 0 16 3l1 2c-4-1-8-2-13-2-8-1-17 0-25-1l1-3c0-1 2-2 2-2z" class="a"></path><path d="M360 660h17c-2 1-4 1-5 1 1 0 2 1 4 1h-10-8v-1l2-1z" class="Y"></path><path d="M329 503l37-2v3h-5c1 1 1 1 2 1s2 0 2 1l-5 1c-1 1-2 1-4 1h-14l-4-1v-1c1-2 8 0 10-1v-1c-4 0-12-1-14 0-2 0-3 0-5-1z" class="k"></path><path d="M584 673l3 1c1 1 2 2 2 3v1h0c0 1 1 2 1 2 3 4 4 7 4 12v2c-1-1-1 0-1-1-2-4-4-10-9-12h-1l-1 1v-2c1-1 2-1 3-1v-1l-2-1c-1 2-4 2-6 3h-1l-3-2h-1l1-2h0c2-2 4-2 6-2h1s2 1 3 1l1-2z" class="c"></path><path d="M573 676h0c2-2 4-2 6-2-1 2-1 2-3 4-1 0-1 0-1-1h-2v-1z" class="Y"></path><path d="M584 673l3 1c1 1 2 2 2 3v1h0c0 1 1 2 1 2l-7-5 1-2z" class="Z"></path><defs><linearGradient id="A" x1="244.829" y1="504.251" x2="246.47" y2="514.031" xlink:href="#B"><stop offset="0" stop-color="#48433f"></stop><stop offset="1" stop-color="#6e5c4a"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M251 507l16 1c2 0 5-1 7 0-2 1-3 2-4 3-4 1-10 0-14 0l-37-1 1-2 31-1z"></path><path d="M496 903l1-3h1c0-2 2-4 4-4h1c1-1 2-1 3-1-1 1-1 2-3 2l-1 1c-1 0-2 1-3 2v1c-2 2-1 4-1 6 1 3 3 6 7 8 1 0 4 0 6-1l2-1v2c-2 1-2 1-4 1h-2 0-3c-1-1-1-1-2-1 1 1 3 2 3 4h1l1 2c1 2 2 3 2 5v1 3 2l-1 3c-1-2-1-5-1-8-1-1-1-1-3-1v6l-1-2h0v-5c-1-1-1-2-2-4h0c0-1 0-2-1-3s0-1-1-2c0-1-1-3-2-4s-1-3-2-5v-1c0-1 1-2 1-3z" class="Q"></path><path d="M503 925v-1-3-2c1 1 1 0 1 1s1 2 2 3c0 1 2 2 2 3s0 3 1 4v2l-1 3c-1-2-1-5-1-8-1-1-1-1-3-1v6l-1-2h0v-5z" class="V"></path><path d="M555 282c1 1 2 2 3 2v-1c2-3-1-9 0-12 0-1 1-2 2-3v57h-2l1-1c-1-1-2-2-3-4 1-2 0-5 0-7v-25l-3-3c0-1-1-2 0-3h2 0zm118 176c0-2 0-3 1-4h0l1 1v31 15c-1 1-2 2-3 2l-1 1v-23-11-2c2-2 1-5 1-8 1 2 0 5 1 7v-9z" class="J"></path><defs><linearGradient id="C" x1="613.757" y1="661.925" x2="615.186" y2="672.829" xlink:href="#B"><stop offset="0" stop-color="#313030"></stop><stop offset="1" stop-color="#4a4843"></stop></linearGradient></defs><path fill="url(#C)" d="M587 673c1 1 3 1 3 1 1 0 3-1 3-1l9-6c4-2 8-4 13-6 3-1 7 0 11 0h-4c4 2 10 1 14 1 3 2 7 1 10 2h-1c-4 1-8 1-13 1s-10-1-15 0c-9 1-17 6-24 12h-2-2c0-1-1-2-2-3v-1z"></path><path d="M573 678l3 2h1c2-1 5-1 6-3l2 1v1c-1 0-2 0-3 1v2c-4 4-11 7-17 8h0c-2 0-4 0-6-1 0-3 0-6 1-8v-2c2 0 3 1 4 2s1 1 3 1c1-4 1-1 4-2 0 0 2-1 2-2z" class="O"></path><path d="M573 678l3 2h1c2-1 5-1 6-3l2 1v1c-1 0-2 0-3 1-8 2-13 5-20 2l-2-1v-2c2 0 3 1 4 2s1 1 3 1c1-4 1-1 4-2 0 0 2-1 2-2z" class="H"></path><defs><linearGradient id="D" x1="575.827" y1="553.092" x2="587.173" y2="557.908" xlink:href="#B"><stop offset="0" stop-color="#908e82"></stop><stop offset="1" stop-color="#b6afa5"></stop></linearGradient></defs><path fill="url(#D)" d="M587 547l1-1 3-3c2-1 3-1 4 0l-1 2h0c-2 3-10 15-12 17-2 0-3 2-5 3-2 2-5 3-7 5-1-1-1-1-1-2h0c1-1 3-3 4-5 1 0 1-2 1-3l2-2c2-2 5-4 6-6l5-5z"></path><path d="M560 701c4 1 8 1 12 1h6c13 1 26 0 39 2-1 0 0 0-2 1l-51 1h-4c-1-2-1-3 0-5z" class="O"></path><defs><linearGradient id="E" x1="676.771" y1="398.53" x2="670.229" y2="400.47" xlink:href="#B"><stop offset="0" stop-color="#726b58"></stop><stop offset="1" stop-color="#888070"></stop></linearGradient></defs><path fill="url(#E)" d="M664 408c3-6 5-12 8-18 0-2 1-4 2-5l1 70-1-1h0c-1 1-1 2-1 4-1-5 0-10 0-15v-28c0-3 1-10 0-13-1 0-1 0-2-1-1 1-2 1-3 2-1 2-2 5-3 7 0-1 0-1-1-2z"></path><path d="M366 654v1c3 0 5 1 8 1 1 0 2-1 3-1 1 1 2 1 4 1l-1 1c0 1 0 0 1 1h14c0 1 1 1 1 2 6 3 12 6 17 9l2 2h-3l-8-5c-9-5-17-6-27-6h-17l-2-1c2-1 4-3 5-4l3-1z" class="H"></path><path d="M366 654v1c3 0 5 1 8 1 1 0 2-1 3-1 1 1 2 1 4 1l-1 1c0 1 0 0 1 1h-12c-2 0-4 0-6-1v-2l3-1z" class="e"></path><path d="M571 773v-2c-1-1 0-1 0-2s-1-1-1-2v-2c7 13 13 23 23 33 2 3 5 5 8 7 1 1 6 4 7 5l1 3-1 1h0c-4 0-7 1-10 1 1-1 3-2 5-3-1-2-3-4-4-5s-1-2-2-2v-1c-3-2-6-3-8-5-8-8-13-18-18-26z" class="f"></path><path d="M377 660c10 0 18 1 27 6l8 5c1 1 5 5 6 4 1 0 2 0 3-1-2 2-4 4-5 7h-1c-1-2-8-7-10-8l-12-6-1-2c-4-3-11-3-16-3-2 0-3-1-4-1 1 0 3 0 5-1z" class="O"></path><path d="M377 660c10 0 18 1 27 6h-1c-2 0-5-1-7-1l-1-1c1 0 1 0 2 1h2 0c-2-2-5-2-7-3-3-1-6-1-9 0h3c2 0-1 0 2 0 0 0 1 0 2 1h1c2 1 2 1 3 3l1 1h1c1-1 7 3 8 4 0 0 1 1 1 2l-12-6-1-2c-4-3-11-3-16-3-2 0-3-1-4-1 1 0 3 0 5-1z" class="I"></path><path d="M597 634l20 18c3 3 12 2 16 1 0 1-2 2-2 2-2 0-4-1-7 0l-2 1h1c-3 1-8-1-10 1l-11 2v-1c1-3 0-6-1-9v-5l-1-1 2 2 1 1c1 0 3 3 4 3 0-5-5-7-8-10h-2 0c-1-1 0-1 0-2s-1-1 0-2v-1z" class="o"></path><path d="M561 633h27c3 0 7 0 9 1v1c-1 1 0 1 0 2s-1 1 0 2h0 2c3 3 8 5 8 10-1 0-3-3-4-3l-1-1-2-2 1 1v5l-1-2c-2-1-2-2-4-2l-6-2h-1c-2-1-5-1-8-1h-1-1c0-1-1-1-1-2 1-2 1-1 3-2l-1-1c0-2-4-2-5-3l-4-1h-4c-2 1-4 0-6 0z" class="X"></path><path d="M597 639c2 1 3 2 3 3h-1c0 1 1 3 1 4h0-1c-1-1-3-3-3-4l1-3z" class="d"></path><path d="M575 634c2 0 4-1 6 1h2 1 1c1 1 1 2 0 3v1h-2c1-1 1-1 1-2l-3 3v2h-1-1c0-1-1-1-1-2 1-2 1-1 3-2l-1-1c0-2-4-2-5-3z" class="h"></path><path d="M510 875c1-3 2-6 4-10 0 5-1 9-2 13h1 1c1 2 0 3-1 5 0 1 1-1 0 1l-1 1c0 2-1 4-1 6v1 1c1-1 0-1 1-2 0-1 1-1 1-2v6h0c3 2 6 5 6 9 0 2 0 4-1 6v1c-1 2-4 5-6 7l-2 7-1 5v-3-1c0-2-1-3-2-5l-1-2h-1c0-2-2-3-3-4 1 0 1 0 2 1h3 0 2c2 0 2 0 4-1v-2s0-1 1-2c0 0 1-1 1-2s1-1 1-2l1-1c0-6-2-6-5-10h-1v-2c-1-1-4 0-5 0h-1-2v-3-1c0-1-1-1 0-2h1 3v-4c1-1 2-6 3-9z" class="K"></path><path d="M510 925l-1-1v-5l3-1-2 7z" class="f"></path><path d="M510 875c1-3 2-6 4-10 0 5-1 9-2 13h1v3c0 1-1 1-1 2v1l-2 2v-11z" class="J"></path><path d="M454 177h60c-3 1-8 0-11 2h-34-43-8c-1 0-1 1-1 1l-1 4v2h-1c0-2 1-4 1-6v-1h-24-1v2l-1 1v-3h-6l-1 1-1-1c2-2 9-1 11-1h37c2 0 4-1 6-1h18z" class="W"></path><path d="M718 511l23-1c3 1 11 0 13 1v2c-3 1-7 0-11 1l-68 1c-1-1 0-2 0-3l1-1h24c6 0 12 1 18 0z" class="S"></path><path d="M503 930l-4 23c-1-6-2-12-5-16-1-4-6-8-6-12 0-2 0-4 2-5 1-2 3-3 5-3 3 1 5 3 6 4 1 2 1 3 2 4v5z" class="n"></path><path d="M720 501c3 1 6 1 9 1l3 2h7c1 0 2 0 3 1h2l5 2c1 0 2 1 3 0l3 2-1 1h-13l-23 1h-5v-1c-2 0-3 0-5-1h5v-2h0c1-2 1-2 3-4 0 0 1 0 1-1 2 0 2 0 3-1z" class="g"></path><path d="M713 507h4c2 0 4 0 6 1-4 1-7 1-10 1v-2h0z" class="W"></path><path d="M730 506c4 3 13 0 18 2h-25c-2-1-4-1-6-1 4-1 9-1 13-1z" class="U"></path><path d="M742 505h2l5 2-1 1c-5-2-14 1-18-2 4-1 8-1 12-1z" class="j"></path><path d="M720 501c3 1 6 1 9 1l3 2h7c1 0 2 0 3 1-4 0-8 0-12 1-4 0-9 0-13 1h-4c1-2 1-2 3-4 0 0 1 0 1-1 2 0 2 0 3-1z" class="r"></path><path d="M720 501c3 1 6 1 9 1l3 2-7-1c-3 0-6 1-9 0 0 0 1 0 1-1 2 0 2 0 3-1z" class="U"></path><path d="M762 507c1 1 1 0 1 1l2 2c1 1 2 1 2 2h5c2-1 3-3 4-5h1c1 1 3 0 5 0l12 1h36 23l-76 3-1 1c-1 2-3 4-6 5-2 0-3 0-5-2-2-1-3-3-4-5-2 1-4 1-6 2v2h-12c4-1 8 0 11-1v-2c-2-1-10 0-13-1h13l1-1v-2h5c1 0 1 1 2 1v-1z" class="a"></path><path d="M556 679c1 3 1 6 1 9v1c0 1 0 0 1 1v4l1 1v1l1-1c0 2 0 3 1 4 0 1-1 1-1 2-1 2-1 3 0 5h4l-4 1v9c0 12 2 24 6 35 1 5 3 10 4 14v2c0 1 1 1 1 2s-1 1 0 2v2h-1c-9-19-12-38-13-59 0-4-1-10-1-14 1-1 1-2 0-3h1l-2-2 1-16z" class="J"></path><path d="M506 895h4 1v1h1c3 4 5 4 5 10l-1 1c0 1-1 1-1 2s-1 2-1 2c-1 1-1 2-1 2l-2 1c-2 1-5 1-6 1-4-2-6-5-7-8 0-2-1-4 1-6v-1c1-1 2-2 3-2l1-1c2 0 2-1 3-2z" class="R"></path><path d="M502 898h1l-2 2v1 3 4l-1-2c-1 0-1-1-2-1v2c0-2-1-4 1-6v-1c1-1 2-2 3-2z" class="K"></path><path d="M501 908c0 1 0 2 1 3 1 2 3 2 6 3l1-1c2 0 3-1 4-3s2-4 2-6l-1-1 1-1c1 2 1 3 1 5 0 1-1 1-1 2s-1 2-1 2c-1 1-1 2-1 2l-2 1c-2 1-5 1-6 1-4-2-6-5-7-8v-2c1 0 1 1 2 1l1 2z" class="f"></path><path d="M385 653c3 0 5-2 8-4 2-3 5-5 8-8 3-2 5-5 8-7v2c1 2 1 3 1 5h1c0 1 0 2 1 3-1 0-1 1-2 1-2 1-2 3-3 5l-1 1c-1 2-2 4-1 5 0 2 1 3 1 4l-2-1h-3l-1-1h-5-14c-1-1-1 0-1-1l1-1h2 0-2v-2l4-1z" class="b"></path><path d="M399 645h0v1c0 1 0 1 1 2l1-1v1l-2 1-2 1c0-1-1-1-1-2 0-2 2-2 3-3z" class="e"></path><path d="M677 503c4-1 11 0 16 0 9 0 18-1 27-2-1 1-1 1-3 1 0 1-1 1-1 1-2 2-2 2-3 4h0v2h-5c2 1 3 1 5 1v1h5c-6 1-12 0-18 0h-24l-1 1c0 1-1 2 0 3h0l-1 1c-1-2 0-10 1-12v1l2-1v-1z" class="d"></path><path d="M675 515l-1 1c-1-2 0-10 1-12v1c1 1 2 2 3 2l2-1c1 0 1 0 2 1 0 0 1 0 1-1 1 1 1 1 1 3l1-1h0c0-1 0-2 1-2 1 1-1-2 0 1l1 1 1-2h0 1v2l2-2s0 1 1 1c-1 1-1 2-1 3h-14l-1 1-1 1c0 1-1 2 0 3h0z" class="F"></path><path d="M699 505v-1l15-1c-1 2-1 3-2 4h1v2h-5c-2-1-6 1-9 0v-2-2z" class="r"></path><path d="M712 507h1v2h-5c-2-1-6 1-9 0v-2h13z" class="l"></path><path d="M692 507v-1l1-1v2l1 1c0-2 0-2 1-3v2h1 0 1 1v-1l1-1v2 2c3 1 7-1 9 0s3 1 5 1v1h5c-6 1-12 0-18 0h-24l1-1h14c0-1 0-2 1-3z" class="c"></path><path d="M439 604h1c2 1 1 6 1 7v22h0c-1 1-1 1-1 2s0 2 1 4c0 1-1 2-1 4l-1 2h1v1c-1 0-1 0-2-1 0 2 1 3 2 4h0c0 1 0 2 1 3h0c-3-1-6-2-8-4v-1c-1-1-2-1-3-2-1 1-3-1-4-1h0-2l-1-1 1-1h-3l-6 1v-1h-2 0c-1-1-1-2-2-3l-1-2-1-1v-2c4-1 11-1 15-1l4 1c2 0 5-1 6 0 2 0 5 0 7-1l-1-28-1-1z" class="Q"></path><path d="M426 644c1-1 1-1 3-1l1-1c-1-1-1 0-1-1s1-1 2-2h1c1-1 2-1 4-1l2 1-1 1h-2c0 1 1 1 2 1l-1 1h-5v1c2 0 3 0 5 1l-1 1-5-1v1c-1 1-3-1-4-1z" class="K"></path><path d="M428 634c2 0 5-1 6 0h1l2 1v1l-1 1c1 0 1 0 2 1h-2c-2 0-3 0-4 1h-1c-1 1-2 1-2 2s0 0 1 1l-1 1c-2 0-2 0-3 1h0-2l-1-1 1-1h1c1-2 3-3 5-3 0 0 0-1 1-1-1-1-1-1-2-1v-1h2v-1c-1 0-1-1-2-1h-1z" class="H"></path><path d="M409 634c4-1 11-1 15-1l4 1h1c1 0 1 1 2 1v1h-2v1c1 0 1 0 2 1-1 0-1 1-1 1-2 0-4 1-5 3h-1-3l-6 1v-1h-2 0c-1-1-1-2-2-3l-1-2-1-1v-2z" class="X"></path><path d="M424 633l4 1h1c1 0 1 1 2 1v1h-2v1c1 0 1 0 2 1-1 0-1 1-1 1-2 0-4 1-5 3h-1-3c1-1 1-2 2-2l1-1c0-1 0-1-1-2h-1l1-1h2c1 0 2-1 4-1h0-3l-2-2z" class="h"></path><path d="M423 637h2v3 2h-1-3c1-1 1-2 2-2l1-1c0-1 0-1-1-2z" class="d"></path><path d="M581 177h168l-1 45v-12-23c0-1 1-5 0-5 0-1 0-1-1-2v2c0-1 0-2-1-3h-7-85-1-73-6l-1-1c3 0 5 0 8-1h0z" class="l"></path><path d="M420 785l5-11v1c0 2 0 3-1 4h2v-1c0-1 1-2 3-3l-3 6c1 1 2 0 3 0l3 1h2l-1 1-4 8c-4 6-9 12-15 18h2 0l-2 1-1 1 1 1-2 1v1c2 0 2 0 4 1-3 0-6-1-8-1-4 0-7-1-10 2h-1c1-2 2-3 4-4 7-9 13-17 19-27z" class="D"></path><path d="M420 785c0 1 1 3 1 3-2 5-6 9-9 12l-1 2c-3 3-5 7-9 10h-1c7-9 13-17 19-27z" class="E"></path><path d="M432 782h2l-1 1-4 8c-4 6-9 12-15 18h2 0l-2 1-1 1 1 1-2 1v1c2 0 2 0 4 1-3 0-6-1-8-1l-1-1v-1h2v-2c1-4 19-25 23-28z" class="J"></path><defs><linearGradient id="F" x1="568.251" y1="254.301" x2="545.857" y2="264.09" xlink:href="#B"><stop offset="0" stop-color="#aaa595"></stop><stop offset="1" stop-color="#d4cbba"></stop></linearGradient></defs><path fill="url(#F)" d="M561 232c1-1 2-2 4-3h2c1 1 1 1 2 3 1-1 1-3 3-3-2 3-6 10-6 14l-4 12-2 13h0c-1 1-2 2-2 3-1 3 2 9 0 12v1c-1 0-2-1-3-2h0c-2-1-3-3-4-4-1-15 2-28 8-41l2-5z"></path><path d="M558 256c2-1 2-3 2-5h0c1-2 2-3 3-5h0c0 1 0 2-1 3l-1 5 1 1-2 13h0c-1 1-2 2-2 3-1 3 2 9 0 12v1c-1 0-2-1-3-2h1c1-1 0-9 0-11 1-4 1-11 2-15z" class="h"></path><path d="M561 232c1-1 2-2 4-3h2c1 1 1 1 2 3 1-1 1-3 3-3-2 3-6 10-6 14l-4 12-1-1 1-5c1-1 1-2 1-3h0c-1 2-2 3-3 5h0c0 2 0 4-2 5l2-7 2-7v-1c0-2-2-3-3-4l2-5z" class="W"></path><defs><linearGradient id="G" x1="658.525" y1="436.213" x2="628.249" y2="447.909" xlink:href="#B"><stop offset="0" stop-color="#060605"></stop><stop offset="1" stop-color="#323130"></stop></linearGradient></defs><path fill="url(#G)" d="M664 408c1 1 1 1 1 2 1 1 1 2 1 2l-5 17c-1 2-1 3-2 5 0 2-1 4-1 6h0-1c-1-1-1-1-1-2v-3l-3 6c-1 3-3 6-5 9h0-2v1h-1c-1 2-2 4-4 5-4 5-9 11-15 14 2-3 5-6 7-9 5-7 10-16 13-25 2-5 3-11 5-16v-1h1l4-1h1 0 0 1c2-1 3-5 4-7v2l2-5z"></path><path d="M652 422l3-1c1 3 0 5-1 7l1-3c-1-1-2-1-3-1v-2z" class="n"></path><path d="M651 419h1l4-1h1c-1 1-1 2-2 3l-3 1c0-1 0-2-1-3z" class="b"></path><path d="M664 408c1 1 1 1 1 2 1 1 1 2 1 2l-5 17c-1 2-1 3-2 5 0 2-1 4-1 6h0-1c-1-1-1-1-1-2v-3l-3 6c-1 3-3 6-5 9h0-2v1h-1c-1 2-2 4-4 5 1-3 4-6 6-9 3-6 5-13 7-19 1-2 2-4 1-7 1-1 1-2 2-3h0 0 1c2-1 3-5 4-7v2l2-5z" class="k"></path><path d="M664 408c1 1 1 1 1 2 1 1 1 2 1 2l-5 17c-1 2-1 3-2 5 0 2-1 4-1 6h0-1c-1-1-1-1-1-2v-3h0c0-1 0-1 1-2v-1-1c1-4 3-5 2-9v-1c2-1 3-4 4-5-1 0-1 0-2 1h0c0-2 1-3 1-4l2-5z" class="K"></path><path d="M428 463l1 1h1l1 3-2 2c-1 1-2 1-3 2l-1 1-1 1c-2 1-2 1-3 1h-1v2c-1 0-3 2-4 2-1 1-1 2-2 3-2 0-3 1-4 2l-1 1v1h-2c-1 0-3 1-5 2l-9 5v1c0 1 0 1-1 2l3 3h-4-6-1v1l-2-1 1-1h-2-6-1 0c-1-1-1-1-1-2 1 0 0 0 1-1l-2-1v-1l2-1v-1c1 0 3 0 4-1 3 0 5 0 8-3 6-1 12-5 18-8 1-1 15-8 17-10 3-1 5-3 7-5z" class="e"></path><path d="M393 493c0 1 0 1-1 2l3 3h-4-6l8-5z" class="K"></path><path d="M385 489c2-1 4-1 7-2v1c-1 1-2 2-4 3h0 1c0 1 0 2 1 3-1 1-2 1-3 2l-1-1 1-3v-1h-2v-2z" class="U"></path><path d="M386 486c6-1 12-5 18-8-1 1-1 2-3 2l-7 4c-1 1-2 1-3 2l1 1c-3 1-5 1-7 2-1 0-1 1-2 1h-6c-1 1-2 1-3 1v-1c1 0 3 0 4-1 3 0 5 0 8-3z" class="o"></path><path d="M383 490c1 0 1-1 2-1v2h2v1l-1 3c-2 1-2 1-3 2h-2-6-1 0c-1-1-1-1-1-2 1 0 0 0 1-1l-2-1v-1l2-1c1 0 2 0 3-1h6z" class="J"></path><path d="M374 491c1 0 2 0 3-1h6c-2 2-5 3-8 3l-1-1h0c-1 1-1 1-2 1v-1l2-1z" class="b"></path><defs><linearGradient id="H" x1="685.903" y1="539.518" x2="660.597" y2="522.982" xlink:href="#B"><stop offset="0" stop-color="#767463"></stop><stop offset="1" stop-color="#afa396"></stop></linearGradient></defs><path fill="url(#H)" d="M671 504l1-1c1 0 2-1 3-2v2h2v1l-2 1v-1c-1 2-2 10-1 12l1-1c0 3 1 7 0 9-1 1-1 2-1 3l1 1v11 24l-1 48v2c-1-1-2-3-3-5l-3-6h-1 0c0-2 0-3 1-4v-2c0-1-1-3-2-4v-2h-1l-2 1v-2c1 0 0 0 1-1h1 1l-1-1h1l6 6c-1-4 0-8 0-12h-1v-21h-1v-13h-1l-1-2h1c1-2 0-4 0-6v-1c0-2 1-8 0-10-1 0-4-1-5-1h6v-3h-2c1-1 2-1 2-1l1-1v-2-16z"></path><path d="M670 523l1-1v17c1 3 1 6 2 9v16 9 1 7h-1 0-1v-21h-1v-13h-1l-1-2h1c1-2 0-4 0-6v-1c0-2 1-8 0-10-1 0-4-1-5-1h6v-3h-2c1-1 2-1 2-1z" class="W"></path><path d="M672 581c0-3-1-7 0-9l1 2v7h-1z" class="J"></path><path d="M670 523l1-1v17 21h-1v-13h-1l-1-2h1c1-2 0-4 0-6v-1c0-2 1-8 0-10-1 0-4-1-5-1h6v-3h-2c1-1 2-1 2-1z" class="B"></path><path d="M673 573l1 1v19c0 6-1 13 0 18v2c-1-1-2-3-3-5l-3-6h-1 0c0-2 0-3 1-4v-2c0-1-1-3-2-4v-2h-1l-2 1v-2c1 0 0 0 1-1h1 1l-1-1h1l6 6c-1-4 0-8 0-12h0 1v-7-1z" class="I"></path><path d="M672 581h1c0 5 1 11 0 16-1-1-1-2-1-2v-2c-1-4 0-8 0-12h0z" class="Z"></path><path d="M668 598c1 1 2 3 3 4l1 1v1 1 3h-1l-3-6h-1 0c0-2 0-3 1-4z" class="O"></path><path d="M666 587l6 6v2s0 1 1 2v2h-1c-1-1-3-2-4-3 0-1-1-3-2-4v-2h-1l-2 1v-2c1 0 0 0 1-1h1 1l-1-1h1z" class="k"></path><path d="M368 527c1-1 3-1 5-1s3 0 5 1v1c1 0 2 1 3 1h2l1 1c1 1 2 1 4 1-1 1-2 2-3 2s0 0-1 1h5v1c1 1 1 0 1 1h-3l-1 1h-3l-1 2c2-1 2-1 4 0h1 1-1-1-2-5 0c-4 0-9 0-12-3-1 1-1 2-1 3v1s1 1 2 1h0v-1c1 0 2 2 3 2 1 1 2 0 3 2h-1c-3-1-7-3-11-2h0l-2-1h-1v1 1l-1-1-2-1h0v-1h-1c-2 2-7 0-10 1h-1l-1 1h-1l-1-1s0 1-1 1-6 0-7-1h4c3 0 5-1 8-2l1-1c0-2 0-4-1-6 1-1 1-2 2-2l-2-2c3 0 3 0 5-1h4 14z" class="X"></path><path d="M370 532l1-1 1 1c0 1 0 3 1 4h-2-1c-1 0-2 0-3-1v-1h4 0l-1-2z" class="d"></path><path d="M354 527h14 4c-1 1-2 1-4 1l-12 1-2-2z" class="f"></path><path d="M372 532l1 1 1-1 1 1v-1h4l1 1h2c1 0 0 0 2 1h0 5v1c1 1 1 0 1 1h-3l-1 1h0c-3-1-7 0-10 0-1-1-2-1-3-1-1-1-1-3-1-4z" class="U"></path><path d="M350 527h4l2 2h-1-4v1 2 2 1l2-1 1 1h-1v1l1 1h0v2h0c-2 1-6 1-9 1v-1l1-1c0-2 0-4-1-6 1-1 1-2 2-2l-2-2c3 0 3 0 5-1z" class="J"></path><path d="M347 530c1 0 2 1 2 1v6c-1 2-2 2-4 3v-1l1-1c0-2 0-4-1-6 1-1 1-2 2-2z" class="G"></path><path d="M368 527c1-1 3-1 5-1s3 0 5 1v1c1 0 2 1 3 1h2l1 1c1 1 2 1 4 1-1 1-2 2-3 2s0 0-1 1h0c-2-1-1-1-2-1h-2l-1-1h-4v1l-1-1-1 1-1-1-1-1-1 1h0-3c-1 0-3 0-4 1h0-1v1c1 1 1 0 2 1v5h-1s-1-1-2-1c-1-1-2-2-2-3l1-1v-3c-1-1-1 0 0-1h1c1-1 2-1 3-1 2 0 2-1 4-1l1 1c1 0 2 0 4-1h0v-2h-1-4z" class="o"></path><path d="M368 527c1-1 3-1 5-1s3 0 5 1v1l-1 1 1 1c-1 1-2 1-3 1-1-1-1-1-2-1v-1-2h-1-4z" class="C"></path><path d="M633 653c2 0 7 0 9 1 1 1 2 1 4 3 1 1 2 2 4 3h-1 0c-3 3-10 2-13 2-4 0-10 1-14-1h4c-4 0-8-1-11 0-5 2-9 4-13 6l-9 6s-2 1-3 1c0 0-2 0-3-1v1l-3-1-1 2c-1 0-3-1-3-1h-1l-2-2-1-1v-1h2l-1-2v-2h0v-1-1c1-1 4-1 5-1h1 0c2 1 4 0 6 0h2s1 0 2-1h2c2-1 4-2 6-4v1h0 1l11-2c2-2 7 0 10-1h-1l2-1c3-1 5 0 7 0 0 0 2-1 2-2z" class="J"></path><path d="M577 672l-1-1v-1h2l-1-2v-2h0v-1-1l1 1c1 1 0 2 0 3 1 1 1 1 1 2 2 0 4 1 6 2h1l1 1v1l-3-1-1 2c-1 0-3-1-3-1h-1l-2-2z" class="H"></path><path d="M580 674c-1-1-1-1-1-2h2 1c1 0 1 0 2 1l-1 2c-1 0-3-1-3-1z" class="W"></path><path d="M623 656c4 0 8 0 12 1h0c-3 1-6 1-8 1-5 0-10 0-15 1-1 1 0 1-1 0h1l-1-1h1l1-1c2-2 7 0 10-1z" class="X"></path><path d="M633 653c2 0 7 0 9 1-1 1-1 1-1 2l-2 2-4-1h0c-4-1-8-1-12-1h-1l2-1c3-1 5 0 7 0 0 0 2-1 2-2z" class="q"></path><path d="M646 657c1 1 2 2 4 3h-1 0c-3 3-10 2-13 2-4 0-10 1-14-1h4l12-1h5v-2l3-1z" class="O"></path><path d="M646 657c1 1 2 2 4 3h-1 0-11 5v-2l3-1zm93-478h7c1 1 1 2 1 3v-2c1 1 1 1 1 2 1 0 0 4 0 5v23 12l1 158c-2-4-4-8-4-12h-1c-1-8-2-15-5-22v-4h-1c0-2 1-4 0-5 0-2-1-4-1-6h0l1-1c1-1 1-2 2-3 2 1 1 4 4 4 0 1 1 3 1 4h2v-5-49c-1-3 0-8 0-11v-32c0-4 1-11-1-14v-1c-2 2-4 3-5 5l-1 1v-1c4-4 2-15 2-21v-8-13-1h1l-1-2h1v-1l-1-1h1v-1l-4-1z" class="i"></path><defs><linearGradient id="I" x1="738.316" y1="339.44" x2="744.739" y2="348.327" xlink:href="#B"><stop offset="0" stop-color="#4b463c"></stop><stop offset="1" stop-color="#6e675e"></stop></linearGradient></defs><path fill="url(#I)" d="M739 346l1-11h0c3 3 4 6 5 10 1 1 2 2 2 3-1 2-1 2-1 4l-1 1c-1-1 0-2-1-4l-1 1h0c1 1 1 3 1 4v1 2l1 1c-1 1-1 2-1 3v1c0 2 0 3 1 5v1h-1c-1-8-2-15-5-22z"></path><path d="M747 281l1-12v21 38 15 8l-1-3c0-1-1-2-2-3-1-4-2-7-5-10h0l-1 11v-4h-1c0-2 1-4 0-5 0-2-1-4-1-6h0l1-1c1-1 1-2 2-3 2 1 1 4 4 4 0 1 1 3 1 4h2v-5-49z" class="O"></path><path d="M738 330c1-1 1-2 2-3 2 1 1 4 4 4 0 1 1 3 1 4h2v8c0-1-1-1-1-2-2-4-5-7-8-11z" class="K"></path><path d="M739 179h7c1 1 1 2 1 3v11c0 4 1 26-1 28 1 1 1 1 1 2h-1c-2 2-4 3-5 5l-1 1v-1c4-4 2-15 2-21v-8-13-1h1l-1-2h1v-1l-1-1h1v-1l-4-1z" class="U"></path><path d="M742 207c2 3 2 10 2 14h0l1 1 1-1c0-1-1-3-1-4 0-3 0-7 1-10 1-4-1-10 1-14 0 4 1 26-1 28 1 1 1 1 1 2h-1c-2 2-4 3-5 5l-1 1v-1c4-4 2-15 2-21z" class="Z"></path><path d="M665 410c1-2 2-5 3-7 1-1 2-1 3-2 1 1 1 1 2 1 1 3 0 10 0 13v28c0 5-1 10 0 15v9c-1-2 0-5-1-7 0 3 1 6-1 8v2h-3 0c-3 0-5 0-7-1l-2-3v-1c-1-1-1-2-2-3 1-2 4-4 5-5v-1l-1-1-1 1-1-2h0l-1 2h1v1l-1 1-1-2 1-3v-2l-1 2-1-1c1-1 1-2 2-3h1v-1c0-2 0-3 1-4h0v-11-1l1 1c1-2 0-3 0-4l5-17s0-1-1-2z" class="U"></path><path d="M668 463h2v-1c0-5-1-12 1-16v22 2h-3 0c0-1-2-2-4-2 1-1 3 0 4-1-1 0 0 0-1-1l1-1v-2z" class="D"></path><path d="M665 433l2-1h1c0 1 1 1 1 2s0 0 1 1c1-2 0-10 0-12l1-1v24c-2 4-1 11-1 16v1h-2v-1h1c0-1 0-2-1-3 1 0 1-3 1-4l-1-9-1-1c1 0 1-1 1-1l-2-1c1 0 1 0 2-1 0-2 1-3 0-5l-1-1c-1-1-1-2-2-3z" class="P"></path><path d="M662 456c1 0 1-1 1-2v-2h-1 2v3h1l1 2v-2c0-1 0-1-1-2v-1c1 0 2 1 2 1h1l1 2c0 1 0 4-1 4 1 1 1 2 1 3h-1v1 2l-1 1c1 1 0 1 1 1-1 1-3 0-4 1 2 0 4 1 4 2h0 0c-3 0-5 0-7-1l-2-3v-1c-1-1-1-2-2-3 1-2 4-4 5-5v-1z" class="H"></path><path d="M668 462c-1 0-1-1-2-1 0 1 0 2-1 3 0 1 0 1 1 2h-3l-1-3c-1-1 0-1-1-2l2-2c2-1 3 0 5 0 1 1 1 2 1 3h-1z" class="B"></path><path d="M658 449h1v-1c0-2 0-3 1-4h0v-11-1l1 1c0 2 0 3 1 5v2c2 2 2 2 4 2 1-2 1-3 2-5 1 2 0 3 0 5-1 1-1 1-2 1l2 1s0 1-1 1l1 1 1 9-1-2h-1s-1-1-2-1v1c1 1 1 1 1 2v2l-1-2h-1v-3h-2 1v2c0 1 0 2-1 2l-1-1-1 1-1-2h0l-1 2h1v1l-1 1-1-2 1-3v-2l-1 2-1-1c1-1 1-2 2-3z" class="i"></path><path d="M658 453c1-1 2-3 2-5 0-1 0 0 1-1h1c1 1 2 3 2 4v1h-2 1v2c0 1 0 2-1 2l-1-1-1 1-1-2h0l-1 2h1v1l-1 1-1-2 1-3zm8-41c1-1 2-4 4-5 1 1 1 2 1 3v4 8l-1 1c0 2 1 10 0 12-1-1-1 0-1-1s-1-1-1-2h-1l-2 1c1 1 1 2 2 3l1 1c-1 2-1 3-2 5-2 0-2 0-4-2v-2c-1-2-1-3-1-5 1-2 0-3 0-4l5-17z" class="S"></path><path d="M671 410v4c-1 0-1 1-2 1l-1 1v-1-3c1-1 2-1 3-2z" class="I"></path><path d="M667 436l-1 3s-1 1-2 1l-1-1 1-2c-1-2-2-2-1-4h2c1 1 1 2 2 3z" class="n"></path><defs><linearGradient id="J" x1="311.158" y1="526.143" x2="311.33" y2="542.202" xlink:href="#B"><stop offset="0" stop-color="#414040"></stop><stop offset="1" stop-color="#726c62"></stop></linearGradient></defs><path fill="url(#J)" d="M351 512h12c2 0 3-1 5-1v2 2c0 1-1 2-1 2-2-1-4-1-6-1h-1c-1 1-4 2-6 3l-3 2c-4 1-8 1-12 1l-4 1c3 0 10-1 12 1h-1l1 1 3 2c-2 1-2 1-5 1l2 2c-1 0-1 1-2 2h0c-6-4-19-3-27-2-12 2-23 7-34 13h-1v-2-1c2-3 8-7 11-8 1-1 4-3 4-4 2-2 6-4 8-4l1-1h1c3-2 6-3 10-4 6-1 12-3 18-4h0v-1c2-1 6 0 8 0 1-1 3 0 4-1v-1h3z"></path><path d="M294 532c2 0 3-1 5-1l1 1-17 9v-1c2-3 8-7 11-8z" class="Z"></path><path d="M318 530c1 0 2-1 2-1 1-1 2-1 4-1 5-1 11-1 16 0l4 1 1-1h0l2 2c-1 0-1 1-2 2h0c-6-4-19-3-27-2z" class="M"></path><path d="M329 524h4v1h-1c-12 1-21 3-32 7l-1-1c3-2 7-4 11-5l14-2h5z" class="j"></path><path d="M306 524l-2 2c-1 0-1 0-2 1h1c5-2 10-2 16-3 1-1 3-1 5-1 2-1 4-1 6-1l-1 2h-5l-14 2c-4 1-8 3-11 5-2 0-3 1-5 1 1-1 4-3 4-4 2-2 6-4 8-4z" class="X"></path><path d="M330 522h4 5l-4 1c3 0 10-1 12 1h-1l1 1 3 2c-2 1-2 1-5 1h0c-4-2-8-2-13-3h1v-1h-4l1-2z" class="Z"></path><path d="M351 512h12c2 0 3-1 5-1v2 2c0 1-1 2-1 2-2-1-4-1-6-1h-1c-1 1-4 2-6 3l-3 2c-4 1-8 1-12 1h-5-4c-2 0-4 0-6 1-2 0-4 0-5 1-6 1-11 1-16 3h-1c1-1 1-1 2-1l2-2 1-1h1c3-2 6-3 10-4 6-1 12-3 18-4h0v-1c2-1 6 0 8 0 1-1 3 0 4-1v-1h3z" class="e"></path><defs><linearGradient id="K" x1="367.115" y1="512.906" x2="345.769" y2="515.617" xlink:href="#B"><stop offset="0" stop-color="#0d0d0d"></stop><stop offset="1" stop-color="#2b2b2b"></stop></linearGradient></defs><path fill="url(#K)" d="M351 512h12c2 0 3-1 5-1v2 2c0 1-1 2-1 2-2-1-4-1-6-1h-1c-6-1-14-2-20-1-2 1-4 1-6 1l-10 2c-2 1-4 2-6 1 6-1 12-3 18-4h0v-1c2-1 6 0 8 0 1-1 3 0 4-1v-1h3z"></path><path d="M375 497h6 2l-1 1 2 1v-1h1 6l-1 1 3 1h0-8l-1 5c-2-1-2-1-3-2-1 0-1 0-2-1-1 2 0 4-1 6l-1 1v10h1 2c1 0 3 0 5 1v-1h2 2 8v-1h15l1 1h-1l-1 1c2 1 8 0 10 0v1h-1c0 1-1 1-2 1v1h-6l-2 1-2 1v2c-1 2-1 2-2 3h-1v-2l-1-1c-1 0-2 1-2 0-3 0-3 0-5 1-1 1-2 2-4 2l-1 1h-4c-2 0-3 0-4-1l-1-1h-2c-1 0-2-1-3-1v-1c-2-1-3-1-5-1s-4 0-5 1h-14-4l-3-2-1-1h1c-2-2-9-1-12-1l4-1c4 0 8 0 12-1l3-2c2-1 5-2 6-3h1c2 0 4 0 6 1 0 0 1-1 1-2v-2l1-1v7h0l1-1v-4c0-3 1-7-1-10l1-1c1 0 1-1 3 0h0l3-2c1-1 1-1 1-2-1 0-1 0-2-1v-1z" class="E"></path><path d="M398 523h14l-2 1-2 1-10-1h2l-2-1z" class="K"></path><path d="M374 522l24 1 2 1h-2c-9 1-17 0-26 1l-1-1h3v-1h-4 0l4-1z" class="f"></path><path d="M375 497h6 2l-1 1 2 1v-1h1 6l-1 1 3 1h0-8l-1 5c-2-1-2-1-3-2-1 0-1 0-2-1-1 2 0 4-1 6l-1 1v-6c-3 3 0 14-1 16-1-1-1-2-1-4v-11h-1c-2 4-1 11 0 15-1 1-1 0-2 0 1-5 1-11-1-16v10 4l1 1c-1 1-2 2-4 3v-1-5-2l1-1v7h0l1-1v-4c0-3 1-7-1-10l1-1c1 0 1-1 3 0h0l3-2c1-1 1-1 1-2-1 0-1 0-2-1v-1z" class="d"></path><path d="M398 524l10 1v2c-1 2-1 2-2 3h-1v-2l-1-1c-1 0-2 1-2 0-3 0-3 0-5 1-1 1-2 2-4 2l-1 1h-4c-2 0-3 0-4-1l-1-1h-2c-1 0-2-1-3-1v-1c-2-1-3-1-5-1s-3 0-5-1h4c9-1 17 0 26-1z" class="B"></path><path d="M378 527l1-1 1 1c2 0 6-1 7 0l1 1c0 1 2-1 2-1 2-1 4 0 5 0l2 1c-1 1-2 2-4 2l-1 1h-4c-2 0-3 0-4-1l-1-1h-2c-1 0-2-1-3-1v-1z" class="a"></path><path d="M368 515v5 1c2-1 3-2 4-3l-1-1v-4-10c2 5 2 11 1 16v1h0l1 1 1 1-4 1h0 4v1h-3l1 1h-4c2 1 3 1 5 1-2 0-4 0-5 1h-14-4l-3-2-1-1h1c-2-2-9-1-12-1l4-1c4 0 8 0 12-1l3-2c2-1 5-2 6-3h1c2 0 4 0 6 1 0 0 1-1 1-2z" class="F"></path><path d="M357 521c2 0 5 0 8-1l2 2c1 0 2 1 3 1h0 4v1h-3l1 1h-4c2 1 3 1 5 1-2 0-4 0-5 1h-14-4l-3-2 1-1 1 1c2-2 5-3 8-4z" class="N"></path><path d="M349 525c2-2 5-3 8-4 1 1 2 1 4 2l1 2h-13z" class="h"></path><path d="M357 521c2 0 5 0 8-1l2 2c1 0 2 1 3 1h0 4v1h-3c-3 1-7 1-9 1l-1-2c-2-1-3-1-4-2z" class="W"></path><defs><linearGradient id="L" x1="311.064" y1="521.458" x2="305.277" y2="499.95" xlink:href="#B"><stop offset="0" stop-color="#292c24"></stop><stop offset="1" stop-color="#4d4547"></stop></linearGradient></defs><path fill="url(#L)" d="M274 504l1-1c1 0 2-1 4 0 12 1 25 0 38 0 4 0 9-1 12 0 2 1 3 1 5 1 2-1 10 0 14 0v1c-2 1-9-1-10 1v1l4 1h14c2 0 3 0 4-1l5-1h3v1l1-1v-2c2 3 1 7 1 10v4l-1 1h0v-7l-1 1v-2c-2 0-3 1-5 1h-12-3v1c-1 1-3 0-4 1-2 0-6-1-8 0l-56-2v1c-2 0-3 1-5 0 0 0-1-1-2-1 0-1-2-1-3-1 1-1 2-2 4-3v-2-2z"></path><path d="M274 504l1-1c1 0 2-1 4 0 1 1 2 2 4 3l1 1h7 2l21 1h0 2c-1 1-3 0-4 0-2 0-5 1-7 1l-1 1-8-1h-3c-4 0-7-1-11-1h-1l-1-2-1 1 1 1-2 1c-1 0-2 0-3-1 1 0 1-1 1-2h1v-1l-3 1v-2z" class="H"></path><path d="M314 508h2c-1 1-3 0-4 0-2 0-5 1-7 1l-1 1-8-1h5c3-2 9-1 13-1z" class="K"></path><path d="M334 504c2-1 10 0 14 0v1c-2 1-9-1-10 1v1l4 1c-3 0-5 0-8 1-4 2-9 0-13 1h-17l1-1c2 0 5-1 7-1 1 0 3 1 4 0h-2 0l2-1c-1-1-1 0 0-1s2 0 3 0l8-1h4c1 0 1 0 3-1z" class="U"></path><path d="M338 507l4 1c-3 0-5 0-8 1-4 2-9 0-13 1h-17l1-1c2 0 5-1 7-1 1 0 3 1 4 0h9c4-1 8-1 13-1z" class="g"></path><path d="M369 504c2 3 1 7 1 10v4l-1 1h0v-7l-1 1v-2c-2 0-3 1-5 1h-12l-20-1c-3 0-7 0-10-1 4-1 9 1 13-1 3-1 5-1 8-1h14c2 0 3 0 4-1l5-1h3v1l1-1v-2z" class="V"></path><path d="M356 508c2 0 3 0 4-1l5-1h3v1 3h-1c-4-1-12 0-15-2h4z" class="I"></path><path d="M342 508h14-4-7c0 1 1 1 2 1v1h-7c-3 1-6 0-9 1-3 0-7 0-10-1 4-1 9 1 13-1 3-1 5-1 8-1z" class="L"></path><path d="M279 503c12 1 25 0 38 0 4 0 9-1 12 0 2 1 3 1 5 1-2 1-2 1-3 1h-4l-8 1c-1 0-2-1-3 0s-1 0 0 1l-2 1-21-1h-2-7l-1-1c-2-1-3-2-4-3z" class="X"></path><path d="M409 636l1 1 1 2c1 1 1 2 2 3h0 2v1l6-1h3l-1 1 1 1h2 0c1 0 3 2 4 1 1 1 2 1 3 2v1c2 2 5 3 8 4l1 2-1 1-15 8c-2 0-4 1-6 1 1 0 3 1 5 1 1 0 3 0 4 1h2v1h1c1 0 2 0 3 1h1l-2 2 2 2 1 1-1 1-1 2c-2 1-4 1-6 1-2-1-2-1-4 0h-2c0-1 0-2 1-3s2-1 3-2l1-1c-2 1-3 1-4 2s-1 1-3 1c-1 1-2 1-3 1-1 1-5-3-6-4h3l-2-2c-5-3-11-6-17-9 0-1-1-1-1-2h5l1 1h3l2 1c0-1-1-2-1-4-1-1 0-3 1-5l1-1c1-2 1-4 3-5 1 0 1-1 2-1-1-1-1-2-1-3h-1c0-2 0-3-1-5z" class="d"></path><path d="M415 671l2 2c2 0 2 0 3-1h3v-1h-1-3c-1 0-1-1-1-1l1-1-9-7v-1l2 1c2 2 7 6 10 5 3 0 4-1 7-1l-1 2c-1 0-3 1-4 0h-3v1c1 0 3 1 4 2l-1 2c-1 1-1 1-3 1-1 1-2 1-3 1-1 1-5-3-6-4h3z" class="f"></path><path d="M429 666h0 2v1h1c1 0 2 0 3 1h1l-2 2 2 2 1 1-1 1-1 2c-2 1-4 1-6 1-2-1-2-1-4 0h-2c0-1 0-2 1-3s2-1 3-2l1-1c-2 1-3 1-4 2l1-2c-1-1-3-2-4-2v-1h3c1 1 3 0 4 0l1-2z" class="l"></path><path d="M428 671c2 0 4 0 6 1h0l-2 1c2 0 3 0 4 1l-1 2c-2 1-4 1-6 1-2-1-2-1-4 0h-2c0-1 0-2 1-3s2-1 3-2l1-1z" class="O"></path><path d="M428 671c2 0 4 0 6 1h0l-2 1c-3 0-5 1-7 4h-2c0-1 0-2 1-3s2-1 3-2l1-1z" class="k"></path><defs><linearGradient id="M" x1="439.95" y1="654.752" x2="430.983" y2="650.816" xlink:href="#B"><stop offset="0" stop-color="#171618"></stop><stop offset="1" stop-color="#2f2e2a"></stop></linearGradient></defs><path fill="url(#M)" d="M421 642h3l-1 1 1 1h2 0c1 0 3 2 4 1 1 1 2 1 3 2v1c2 2 5 3 8 4l1 2-1 1-15 8c-2 0-4 1-6 1-1-1-4-2-5-3s-3-2-4-4c-1-1-1-2-1-4 0-1 1-2 2-4 1-1 3-2 5-3 0-1 0-1 1-2-1-1-2 0-3-1h0l6-1z"></path><path d="M421 642h3l-1 1 1 1h2 0c1 0 3 2 4 1 1 1 2 1 3 2v1c-6-3-10-3-16-2 0-1 0-1 1-2-1-1-2 0-3-1h0l6-1z" class="k"></path><path d="M412 649c1 0 1 1 2 0h1 1l3-1c2-1 6-1 8 0l6 3c1 1 2 2 4 2 0 1 0 1-1 1h-2c-1-1-4-1-4-1-1-1-1-2-2-2l-2-1c-2 0-4 1-6 1l-2 3 2 1c0 1-1 1-1 2l-1 1h-1l-1 2-4-3h-1c-1-1-1-2-1-4 0-1 1-2 2-4z" class="M"></path><path d="M411 655v-1c3-3 5-2 9-3l-2 3c-2 2-5 0-7 1z" class="Q"></path><path d="M418 654l2 1c0 1-1 1-1 2l-1 1h-1l-1 2-4-3-1-1v-1c2-1 5 1 7-1z" class="i"></path><path d="M411 656l5 1 1 1-1 2-4-3-1-1z" class="R"></path><defs><linearGradient id="N" x1="426.828" y1="650.691" x2="418.078" y2="658.529" xlink:href="#B"><stop offset="0" stop-color="#66625b"></stop><stop offset="1" stop-color="#7d786e"></stop></linearGradient></defs><path fill="url(#N)" d="M420 651c2 0 4-1 6-1l2 1c1 0 1 1 2 2 0 0 3 0 4 1h2c-2 2-4 5-7 6s-5 2-8 2c-2 0-3-1-5-2l1-2h1l1-1c0-1 1-1 1-2l-2-1 2-3z"></path><path d="M421 662v-1-1c1-2 0-2 1-3l1-1c2 0 4-2 7-1 1 0 1-1 2-1v1c-1 1-2 3-4 3-1 0-1 0-2 1 1 1 1 0 3 1-3 1-5 2-8 2z" class="R"></path><path d="M362 542h0c4-1 8 1 11 2h1c-1-2-2-1-3-2-1 0-2-2-3-2v1h0c-1 0-2-1-2-1v-1c0-1 0-2 1-3 3 3 8 3 12 3 1 1 2 1 4 1 1-1 3 0 4 0-2 0-4 0-5 1l5 5v-1l2 1h0 1v1h1 1l1 1v1h1l1 1c1 0 1 0 2 1h3l1 2h3c1 1 2 1 3 0h2c2-2 4-4 5-6 1 2 1 2 3 4-1 0-1 0-2 1 1 1 2 1 3 2h-6c-1 0-2 1-3 0-1 2-3 3-5 5l9 3c1 4 7 4 8 7l-1 1 7 7h-2v1c-1-1-3-4-5-4 2 3 4 5 6 7l1 2c0 1-1 1-1 2-4-6-9-12-16-16-1-1-4-3-6-3-1 0-2 1-3 1s-1-2-2-3c-2-2-6-4-9-5-13-5-28-9-42-10-11-1-21-1-32-1 9-3 18-3 27-3v-1h2 5 0c2 1 3 0 5 0v-1c1 0 2 1 3 0h1 5c1 1 1 1 3 1h1c1 0 1 1 3 1l-1-1h-2c-1-1-2-1-3-1-1-1-1 0-2 0l-1-1z" class="P"></path><path d="M343 545v-1h2 5 0c2 1 3 0 5 0 3 1 6 0 9 1 6 1 12 2 18 4 3 1 6 1 9 1l1-1v1h1c0 1 0 1-1 3-1-1-1 0-3 0-1 0-4-1-5-1-5-2-11-3-16-4l-15-3h-10 0z" class="X"></path><path d="M414 547c1 2 1 2 3 4-1 0-1 0-2 1 1 1 2 1 3 2h-6c-1 0-2 1-3 0-2 1-4 3-6 5l-14-6c2 0 2-1 3 0 1-2 1-2 1-3 1 0 1 0 1-1l1 1c1 0 1 0 2 1h3l1 2h3c1 1 2 1 3 0h2c2-2 4-4 5-6z" class="k"></path><path d="M394 549l1 1c1 0 1 0 2 1h3l1 2h-9c1-2 1-2 1-3 1 0 1 0 1-1z" class="U"></path><path d="M362 542h0c4-1 8 1 11 2h1c-1-2-2-1-3-2-1 0-2-2-3-2v1h0c-1 0-2-1-2-1v-1c0-1 0-2 1-3 3 3 8 3 12 3 1 1 2 1 4 1 1-1 3 0 4 0-2 0-4 0-5 1l5 5v-1l2 1h0 1v1h1 1l1 1v1h1c0 1 0 1-1 1h-1v-1l-1 1c-3 0-6 0-9-1-6-2-12-3-18-4-3-1-6 0-9-1v-1c1 0 2 1 3 0h1 5c1 1 1 1 3 1h1c1 0 1 1 3 1l-1-1h-2c-1-1-2-1-3-1-1-1-1 0-2 0l-1-1z" class="b"></path><path d="M382 541l5 5h-1c-1 0-1-1-2-1h-2c-1-1-1-2-2-3l2-1z" class="j"></path><path d="M550 694l1-1c2 1 4 2 5 4 1 1 1 2 0 3 0 4 1 10 1 14 1 21 4 40 13 59h1c5 8 10 18 18 26 2 2 5 3 8 5v1c1 0 1 1 2 2s3 3 4 5c-2 1-4 2-5 3l-2 1h-4c1-1 1-3 2-4v-1l4-1c-5-3-9-6-14-10-2-2-4-5-7-8-3 0-3-1-5-3h-1l-1 2v-2h-1l1-1v-1l-1 1h-1-1v-3 1l-2-3h0c-4-6-7-16-15-19-1 0-2 0-3 1l-1-6v-1-1c1 0 2-1 3-1h1c3-1 5 0 8-2-5-15-7-29-8-45l-1-1c-1 0-2-2-2-3v-5c1-1 1-2 1-3-1-1 0-1 0-2l2 2v-2-1z" class="X"></path><path d="M548 695l2 2v-2-1 15l-1-1c-1 0-2-2-2-3v-5c1-1 1-2 1-3-1-1 0-1 0-2z" class="B"></path><path d="M586 799l2 2h0c1 0 2 1 2 1 1 1 1 1 1 3 2 1 8 4 8 6h0c1 1 2 1 3 1v-1l-1-1c-1-1-2-1-2-3h0c1 1 3 3 4 5-2 1-4 2-5 3l-2 1h-4c1-1 1-3 2-4v-1l4-1c-5-3-9-6-14-10l2-1z" class="U"></path><path d="M563 768h1l1 3h0v-2-1 1c3 7 8 15 13 21 2 2 5 4 6 7l2 2-2 1c-2-2-4-5-7-8-5-6-12-16-14-24z" class="d"></path><path d="M558 754c1 5 2 10 5 14 2 8 9 18 14 24-3 0-3-1-5-3h-1l-1 2v-2h-1l1-1v-1l-1 1h-1-1v-3 1l-2-3h0c-4-6-7-16-15-19-1 0-2 0-3 1l-1-6v-1-1c1 0 2-1 3-1h1c3-1 5 0 8-2z" class="m"></path><path d="M546 758l1 1 1 1s1 1 1 0h1c1-1 3-1 4-1 3 1 5 8 7 12 1 4 3 8 6 12v2 1l-2-3h0c-4-6-7-16-15-19-1 0-2 0-3 1l-1-6v-1z" class="T"></path><path d="M550 343v-26c4 2 6 5 8 8h2l-1 2v116l-1-1v-7c0 1 0 2-1 3l-1-1-1 1h0c-1-1-2-2-3-4-1-3-1-5-1-8l-1-11v-72z" class="b"></path><path d="M557 326c2 3 1 9 1 13v18l-1-2c0 1 1 3-1 4l1-33z" class="J"></path><path d="M556 359c2-1 1-3 1-4l1 2v30c0 3 1 7 0 11 0 2 0 5-2 7v-46z" class="k"></path><path d="M558 398v18 7 12c0 1 0 2-1 3l-1-1-1 1h0c-1-1-2-2-3-4-1-3-1-5-1-8h0l1 6 2 1 1 1v-3c1-2 1-4 1-6-1-6 0-13 0-20 2-2 2-5 2-7z" class="J"></path><path d="M558 398v18 7-1-13c-2 5 0 11-2 16-1-6 0-13 0-20 2-2 2-5 2-7z" class="h"></path><path d="M557 326c-2-1-3-3-4-4h0c2 2 4 3 6 5v116l-1-1v-7-12-7-18c1-4 0-8 0-11v-30-18c0-4 1-10-1-13z" class="L"></path><path d="M746 224c2 3 1 10 1 14v32c0 3-1 8 0 11v49h-1v2 2c-1-2-1-3-2-5h0l-11-17 2-1v-5l-1-8c-1-2-1-7-1-8-1-8-2-15-4-22-2-1-2-4-4-6l-1-3h1 0c1 1 3 1 4 1l10-5 3-2 1 2 2-1v-1h-2v-1l-1-13c0-3-1-8 0-11 1-1 1-1 2-1 1-1 2-2 2-3z" class="Z"></path><path d="M742 228c2 3 2 6 2 10s0 8-1 12v1 1l-1-13c0-3-1-8 0-11z" class="k"></path><path d="M742 253l1 2v44c0 9-2 21 1 30l-11-17 2-1v-5l-1-8c1 1 1 1 1 2 2-1 3-1 5-1v-7h1v-26-1-9c-1-1-1-1-2-1h0l3-2z" class="B"></path><path d="M735 300c2-1 3-1 5-1 0 1 0 3 1 4 1 3 0 9 0 12l-4-4c-1-2-2-9-2-11z" class="G"></path><path d="M729 260l10-5h0c1 0 1 0 2 1v9 1 26h-1v7c-2 0-3 0-5 1 0-1 0-1-1-2-1-2-1-7-1-8-1-8-2-15-4-22-2-1-2-4-4-6l-1-3h1 0c1 1 3 1 4 1z" class="M"></path><path d="M741 266v26h-1c0-2 1-5 0-6 0-1-1-2-1-2v-1c1 0 1-1 2-1-1-5 0-11-1-15-1 0-1 0-2 1v2l-1-1v-2c1-1 2-1 4-1z" class="G"></path><path d="M733 280v-4l2 1h2c0 1 1 1 0 1 0 2 0 5-1 7s0 5-1 8c-1-2-1-3-1-5-1-2-1-3-1-5v-3z" class="L"></path><path d="M729 260l10-5h0c1 0 1 0 2 1v9 1c-2 0-3 0-4 1v2 3l-3 2h0 3v1l-2 2-2-1v4c-1-2-2-5-2-7s-1-3-2-5h0c-2-1-2-4-4-6l-1-3h1 0c1 1 3 1 4 1z" class="R"></path><path d="M729 260l10-5h0c1 0 1 0 2 1v9h0-4l1-2h-1c0-2 1-2 1-3h-1l-1 2v1h-1v-2c1-2 3-3 4-4h1v-1h-1l-1 1c-2 1 0 0-1 0-2 1-4 2-5 3l-3 1c-2 0-2 0-4-1v-1c1 1 3 1 4 1z" class="E"></path><defs><linearGradient id="O" x1="560.887" y1="725.432" x2="537.129" y2="742.62" xlink:href="#B"><stop offset="0" stop-color="#181715"></stop><stop offset="1" stop-color="#363534"></stop></linearGradient></defs><path fill="url(#O)" d="M528 692l1-2c1-2 4-3 6-4l-1 1v1c1 0 1 0 1 1l1 1h1 5 0c1 1 2 2 3 2h1 1c0 1 0 2 1 3 0 1-1 1 0 2 0 1 0 2-1 3v5c0 1 1 3 2 3l1 1c1 16 3 30 8 45-3 2-5 1-8 2h-1c-1 0-2 1-3 1v1 1l-5-16-2-5h0l-3-1v1l-2-2h-4c-1-1-1-2-2-3 2-1 5 0 7 0v-1l1-1v-1l-1-3-1-4v-2c-1-1-1-2-1-4l-6-13 1-2v-10z"></path><path d="M544 742c2 0 3-1 4 0l-3 2-1-1v-1z" class="P"></path><path d="M538 718h1c2-1 3-2 5-3s3-3 4-4v7 1 4 1h-1v-6h-3l-2 2c-1-1-2-1-4-1v-1zm6 21l1-1 2-2 1-2s1-2 2-2c0 2 2 7 0 9 0 1-1 1-2 1-1-1-2 0-4 0v-1-2z" class="B"></path><path d="M536 721l2 3c1 2 3 5 5 5h2l-2 2 1 1h0-1l-1 1h2v1 2c-1 2-1 2-1 4h0c1-1 1 0 1-1v2h-2c0-2 0-3 1-5h-1l-2-2v-1c1-2 0-3-1-4-1-2-1-3-1-4-1-1-2-1-2-3v-1z" class="Y"></path><path d="M536 722c0 2 1 2 2 3 0 1 0 2 1 4 1 1 2 2 1 4v1l2 2c-1 1-2 1-2 2h-1 0l-3-1v1l-2-2h-4c-1-1-1-2-2-3 2-1 5 0 7 0v-1l1-1v-1l-1-3-1-4s1 0 2-1z" class="P"></path><path d="M536 722c0 2 1 2 2 3l-1 1 1 1-1 1c-1 0-2-1-2-1l-1-4s1 0 2-1z" class="T"></path><path d="M530 736c-1-1-1-2-2-3 2-1 5 0 7 0v1c0 1 1 1 1 2h1v-2l1 1c1 1 1 1 1 3l-3-1v1l-2-2h-4z" class="G"></path><path d="M538 719c2 0 3 0 4 1l2-2h3v6h1 0c0 2 0 3 1 4-1 2-3 3-5 4l-1-1 2-2h-2c-2 0-4-3-5-5l-2-3 2-2z" class="P"></path><defs><linearGradient id="P" x1="530.948" y1="697.645" x2="542.318" y2="713.052" xlink:href="#B"><stop offset="0" stop-color="#2a2929"></stop><stop offset="1" stop-color="#454443"></stop></linearGradient></defs><path fill="url(#P)" d="M528 692l1-2c1-2 4-3 6-4l-1 1v1c1 0 1 0 1 1l1 1h1 5 0c1 1 2 2 3 2h1 1c0 1 0 2 1 3 0 1-1 1 0 2 0 1 0 2-1 3v5c0 1 1 3 2 3-3 3-7 6-11 10v1l-2 2v1c-1 1-2 1-2 1v-2c-1-1-1-2-1-4l-6-13 1-2v-10z"></path><path d="M535 702c2 0 2 1 3 2h0-1c-1 0-1 0-2-2zm-2 15h0c1-1 1-2 1-3h1v1l1 1c-1 1 0 2-1 3l1 2v1c-1 1-2 1-2 1v-2c-1-1-1-2-1-4z" class="N"></path><path d="M528 692c1 0 1 0 2 1 1 0 2 1 2 2 0 3-1 6 0 9v3c1 2 1 4 1 6h-1c0-1-1-3-1-4-1-3-1-5-3-7v-10z" class="S"></path><path d="M528 692l1-2c1-2 4-3 6-4l-1 1v1c1 0 1 0 1 1l1 1h1 5 0c1 1 2 2 3 2h1 1c0 1 0 2 1 3 0 1-1 1 0 2 0 1 0 2-1 3v5 2l-15-12c0-1-1-2-2-2-1-1-1-1-2-1z" class="C"></path><path d="M431 497c2 1 8 2 9 1 2 0 4 0 5 1h6c0 2 0 4 1 6l-1 2 1 2-1 10v-4h0-5c-5 1-9 0-14 0-3 0-5 0-7-1-1 0-2 0-4 1-2 0-7 0-10-1h0l-5 1-2 2c-2 1-5 0-7 1v1h-8-2-2v1c-2-1-4-1-5-1h-2-1v-10l1-1c1-2 0-4 1-6 1 1 1 1 2 1 1 1 1 1 3 2l1-5h8 0l-3-1 1-1h4 9c1 0 2 1 3 1l7 1 3-1h0c2 0 3-1 5 0h2v1h9l-2-2v-1z" class="U"></path><path d="M426 503h5c0 1 0 2 1 2h2 0c-1 1-3 0-5 0h-5c0-1 1-1 2-2z" class="C"></path><path d="M395 498h9c1 0 2 1 3 1l7 1 3-1h0c2 0 3-1 5 0h2v1h-6v1c2 1 4 1 6 1l-1 1h3c-1 1-2 1-2 2h-7v-1c-2 1-5 1-6 1h-12-2-2-1c-3 1-8 1-10 0l1-5h8 0l-3-1 1-1h4z" class="D"></path><path d="M411 500h2v2h0v1h-1-1c-2 0-2 0-3-1l3-2z" class="a"></path><path d="M395 498h9c1 0 2 1 3 1l7 1 3-1h0c2 0 3-1 5 0h2v1h-6v1c2 1 4 1 6 1l-1 1h3c-1 1-2 1-2 2h-7v-1c-2 1-5 1-6 1 1 0 1-1 2-2v-1h0v-2h-2-18 0l-3-1 1-1h4z" class="M"></path><path d="M423 503h0c-3 0-6 0-8-1l1-2h2v1c2 1 4 1 6 1l-1 1z" class="a"></path><path d="M395 498h9c1 0 2 1 3 1l-14 1-3-1 1-1h4z" class="e"></path><path d="M397 518h0c-3-1-11 0-13-1 0-3-1-8 0-10l2 1h21 1c1 1 3 1 4 1l1 1c1 0 1 0 3-1v1c-1 1-2 2-2 3h-1l-2 1h0l-5 1-2 2c-2 1-5 0-7 1z" class="m"></path><path d="M386 508h21 1c1 1 3 1 4 1l1 1c1 0 1 0 3-1v1c-1 1-2 2-2 3h-1l-2 1h0-16-1l1-1 2-2h6v-1c-2 0-8 1-10 0s-5-2-7-2z" class="B"></path><defs><linearGradient id="Q" x1="450.033" y1="510.205" x2="421.509" y2="510.627" xlink:href="#B"><stop offset="0" stop-color="#0d0c0c"></stop><stop offset="1" stop-color="#333131"></stop></linearGradient></defs><path fill="url(#Q)" d="M431 497c2 1 8 2 9 1 2 0 4 0 5 1h6c0 2 0 4 1 6l-1 2 1 2-1 10v-4h0-5c-5 1-9 0-14 0-3 0-5 0-7-1-1 0-2 0-4 1-2 0-7 0-10-1l2-1h1c0-1 1-2 2-3v-1c-2 1-2 1-3 1l-1-1c-1 0-3 0-4-1h-1 19 4l-1-3c2 0 4 1 5 0h0-2c-1 0-1-1-1-2h-5-3l1-1c-2 0-4 0-6-1v-1h6 9l-2-2v-1z"></path><path d="M439 506h5c2 0 3 0 4 1l-10 1 1-2z" class="k"></path><path d="M429 505c2 0 4 1 5 0l5 1-1 2h-8l-1-3z" class="h"></path><path d="M407 508h19c-2 1-3 1-4 1h-1l-1 2h2s1 0 1 1h1l1 1c1 0 1-1 2-1h1v1h1c3 0 10-2 13 1-1 1-3 0-4 0h-13c-1 0-2 0-4 1-2 0-7 0-10-1l2-1h1c0-1 1-2 2-3v-1c-2 1-2 1-3 1l-1-1c-1 0-3 0-4-1h-1z" class="T"></path><defs><linearGradient id="R" x1="449.183" y1="497.594" x2="420.34" y2="507.389" xlink:href="#B"><stop offset="0" stop-color="#100f0f"></stop><stop offset="1" stop-color="#45433f"></stop></linearGradient></defs><path fill="url(#R)" d="M431 497c2 1 8 2 9 1 2 0 4 0 5 1h6c0 2 0 4 1 6l-1 2h-3c-1-1-2-1-4-1h-5l-5-1h0-2c-1 0-1-1-1-2h-5-3l1-1c-2 0-4 0-6-1v-1h6 9l-2-2v-1z"></path><path d="M431 497c2 1 8 2 9 1 2 0 4 0 5 1-4 0-9 1-12 1l-2-2v-1z" class="H"></path><path d="M431 503c3 0 10 0 12 2l1 1h-5l-5-1h0-2c-1 0-1-1-1-2z" class="N"></path><path d="M606 534c19 4 35 13 46 30 4 8 8 16 11 25v2l2-1h1v2c1 1 2 3 2 4v2c-1 1-1 2-1 4h0l1 23-7-15h-1c0 1 0 2-1 3-2-2-3-10-4-13l-4-12c-2-6-6-13-10-18-8-11-20-19-33-22-4-1-9-2-14-3h0l1-2c3-3 4-6 7-8 1 0 2-1 4-1z" class="n"></path><path d="M663 591l2-1h1v2l-1 5-2-6z" class="d"></path><path d="M656 435v3c0 1 0 1 1 2h1 0l-3 7-6 9-5 7c-2 3-4 5-6 7-1 3-2 4-4 7-4 4-8 10-13 14-1 2-5 5-5 8h0 4c2 0 3 1 4 0h6 3l-1 3c-3-1-4-1-7-1h0v1c-1 1-2 2-2 3s-1 0-1 0h-3c1-1 1-1 1-2-1 0-2 1-2 2h-1c-2 1-2 0-3 0h-5v-2c0-1 1-2 2-3h-12c-3-1-6-1-9 0h-13l-10-1c-2 1-6 0-8 0 0-3-1-7 0-10h3 7l-3 1v1h0l1 1h9v-1c2-2 5 0 8-2h0l1-1c13-2 25-6 36-14l4-4h1c6-3 11-9 15-14 2-1 3-3 4-5h1v-1h2 0c2-3 4-6 5-9l3-6z" class="R"></path><path d="M577 500l-3-1c2-2 11-2 15-2l1 1v2h-13z" class="d"></path><path d="M619 480h0l-1 2c-3 1-5 2-7 3-8 4-15 5-23 7-7 2-14 2-21 5l-1 2h1c-2 1-6 0-8 0 0-3-1-7 0-10h3 7l-3 1v1h0l1 1h9c15-1 30-5 43-12z" class="T"></path><path d="M562 489h7l-3 1v1h0l1 1-6-1h-1l2-2z" class="l"></path><path d="M619 480c4 0 6-3 9-5s6-5 10-8c-1 3-6 7-6 9l2 1c-4 4-8 10-13 14-1 2-5 5-5 8h0 4c2 0 3 1 4 0h6 3l-1 3c-3-1-4-1-7-1h0v1c-1 1-2 2-2 3s-1 0-1 0h-3c1-1 1-1 1-2-1 0-2 1-2 2h-1c-2 1-2 0-3 0h-5v-2c0-1 1-2 2-3h-12c-3-1-6-1-9 0v-2c4-1 10 0 14 0 2 0 6 1 7 0-2-2-9 0-11-1-1 0-1 0-2-1 0-1 0-1 1-2l1 1s1 0 1-1l1-1c2-1 7-2 8-1 2 0 2 0 3-1l-1-1v-1c0-2 0-2-1-4 2-1 4-2 7-3l1-2z" class="f"></path><path d="M611 500h3c1 2 0 3 0 5h-5v-2c0-1 1-2 2-3z" class="I"></path><path d="M618 505h-2l-1-3 1-2c2 1 4 0 6 1 1 2 0 3 0 4h-3c1-1 1-1 1-2-1 0-2 1-2 2z" class="Y"></path><path d="M613 491l1 3c0 1 0 1-1 1-2-1-2 0-4 0-1-1-2-1-4 0h-1l-1-1v1l-1 1-2-1s1 0 1-1l1-1c2-1 7-2 8-1 2 0 2 0 3-1z" class="l"></path><path d="M627 480l-1 1-3 3c-2 1-4 1-5 4l2 2-4 4c-1-1 0-3-1-4 0-1-1-2-2-2v-1c1-2 3-1 4-1 1-1 3-2 4-3l6-3z" class="W"></path><path d="M619 480c4 0 6-3 9-5s6-5 10-8c-1 3-6 7-6 9l2 1c-4 4-8 10-13 14-2 0-5 4-7 6h0l2-3 4-4-2-2c1-3 3-3 5-4l3-3 1-1 1-2c-2 0-3 1-4 2l-1 1h-1-1c-1 1-2 1-3 1l1-2z" class="K"></path><path d="M628 478h1c0 1 0 2-1 3l-1 1c-1 1-2 2-2 3-1 2-3 3-5 5l-2-2c1-3 3-3 5-4l3-3 1-1 1-2z" class="k"></path><defs><linearGradient id="S" x1="622.504" y1="477.323" x2="612.589" y2="466.16" xlink:href="#B"><stop offset="0" stop-color="#b5afa5"></stop><stop offset="1" stop-color="#ddd1b6"></stop></linearGradient></defs><path fill="url(#S)" d="M656 435v3c0 1 0 1 1 2h1 0l-3 7-6 9-5 7c-2 3-4 5-6 7-1 3-2 4-4 7l-2-1c0-2 5-6 6-9-4 3-7 6-10 8s-5 5-9 5h0c-13 7-28 11-43 12v-1c2-2 5 0 8-2h0l1-1c13-2 25-6 36-14l4-4h1c6-3 11-9 15-14 2-1 3-3 4-5h1v-1h2 0c2-3 4-6 5-9l3-6z"></path><path d="M656 435v3c0 1 0 1 1 2h1 0l-3 7-6 9-5 7c-2 3-4 5-6 7-1 3-2 4-4 7l-2-1c0-2 5-6 6-9-4 3-7 6-10 8s-5 5-9 5h0c2-2 6-4 8-6 9-6 15-14 21-24 2-3 4-6 5-9l3-6z" class="c"></path><path d="M648 454l1 2-5 7c-1-1-1 0-1-1s1-2 1-2c1-2 2-4 3-5l1-1z" class="i"></path><path d="M652 447h3l-6 9-1-2c0-1 1-3 2-4 0-1 1-2 2-3z" class="l"></path><path d="M656 438c0 1 0 1 1 2h1 0l-3 7h-3c1-3 2-6 4-9z" class="H"></path><path d="M559 606c1 8 1 18 1 27h1c2 0 4 1 6 0h4l4 1c1 1 5 1 5 3l1 1c-2 1-2 0-3 2 0 1 1 1 1 2h1 1c3 0 6 0 8 1h1l6 2c2 0 2 1 4 2l1 2c1 3 2 6 1 9v1h-1 0v-1c-2 2-4 3-6 4h-2c-1 1-2 1-2 1h-2c-2 0-4 1-6 0h0-1c-1 0-4 0-5 1v1 1h0v2l1 2h-2v1l1 1 2 2c-2 0-4 0-6 2h0l-1 2h1c0 1-2 2-2 2-3 1-3-2-4 2-2 0-2 0-3-1s-2-2-4-2v2c-1 2-1 5-1 8 2 1 4 1 6 1h-6l1 5-1 1v-1l-1-1v-4c-1-1-1 0-1-1v-1c0-3 0-6-1-9v-9l1-20v-11c0-1 0-3 1-4h0c-1-5 0-12 0-17l1 2v-14z" class="a"></path><path d="M562 673l-2-1c0-1 0-2 1-2l1-1h-1c-1-1-1-2-1-3 1 0 2 0 2-1s0-1 1-2h1v2h-1c0 1 0 1-1 2h0 0l1 2-2 2h1l1 1-1 1z" class="D"></path><path d="M560 633h1c2 0 4 1 6 0l1 3h-4-3c-1-2 0-2-1-3z" class="E"></path><path d="M564 660s0-1-1-1h-1v-1h2v-1l-2-2c1-2 0-3 0-4s1-2 2-3c0 0 0-1-1-1l1-1v-2h-1c1-1 2-1 2-2h-2v-1h3c0 1 0 1 1 1v1l-1 1h0l1 1h0c0 1 1 1 1 2l-1 2 2 2c1 0 2 0 3-1h1 0c0 1-1 1-2 2l-2 2c1 1 2 1 2 2h0l-1 1h0l5 3c0 1-1 2-2 4h0-1v-1l-2-1h-2l-2-1-1-1h-1z" class="G"></path><path d="M569 651l-2 2h-1l1-1c-1-1-1-2-2-3l1-1v-1l-1-1 2-1c0 1 1 1 1 2l-1 2 2 2z" class="F"></path><path d="M566 661l2-1h1l-2-2v-2-1l3 2h0l5 3c0 1-1 2-2 4h0-1v-1l-2-1h-2l-2-1z" class="Q"></path><path d="M570 662c2-2 3-1 4-1v-1c-2-1-3-1-4-3h0l5 3c0 1-1 2-2 4h0-1v-1l-2-1z" class="g"></path><path d="M559 606c1 8 1 18 1 27h-1c-1 4 0 7 0 11v24 14 8l1 5-1 1v-1l-1-1v-4c-1-1-1 0-1-1v-1c0-3 0-6-1-9v-9l1-20v-11c0-1 0-3 1-4h0c-1-5 0-12 0-17l1 2v-14z" class="H"></path><path d="M567 633h4l4 1c1 1 5 1 5 3l1 1c-2 1-2 0-3 2 0 1 1 1 1 2h1v1h0l-3 3v1c-1 1-3 2-4 3h-1c-1 1-2 1-3 1l-2-2 1-2c0-1-1-1-1-2h0l-1-1h0l1-1v-1c-1 0-1 0-1-1 0 0 1 0 1-1h-2c-1 0-2 0-3-1l2-2v-1h4l-1-3z" class="g"></path><path d="M567 645c1-1 2-1 3-2v1c1 1 2 1 3 0h2c0 2-2 3-4 4 0 1 0 1 1 2-1 1-2 1-3 1l-2-2 1-2c0-1-1-1-1-2h0z" class="L"></path><path d="M568 636h0 2v1h-1v1h2 0c-1 1-1 1-2 1v1h3c-1 1-2 1-3 1l1 1h1l-1 1c-1 1-2 1-3 2l-1-1h0l1-1v-1c-1 0-1 0-1-1 0 0 1 0 1-1h-2c-1 0-2 0-3-1l2-2v-1h4z" class="M"></path><path d="M568 636h0v1l-2-1-1 1 1 1h1v1h-1l-1 1c-1 0-2 0-3-1l2-2v-1h4z" class="D"></path><path d="M571 633l4 1c1 1 5 1 5 3l1 1c-2 1-2 0-3 2 0 1 1 1 1 2h1v1h-6l1-2-2-1 1-1c-1-1-1-1-1-2 1 0 1 0 2-1-1-1-3 0-4-1v-2z" class="f"></path><path d="M575 641l1-2c1 0 2 0 2-1h0v-1h2v1-1l1 1c-2 1-2 0-3 2 0 1 1 1 1 2h1v1h-6l1-2z" class="J"></path><path d="M572 664h1 0c1-2 2-3 2-4l7 3c-1 0-4 0-5 1v1 1h0v2l1 2h-2v1l1 1 2 2c-2 0-4 0-6 2h0l-1 2h1c0 1-2 2-2 2-3 1-3-2-4 2-2 0-2 0-3-1s-2-2-4-2h0c1-1 1-1 1-2v-3h1v-1l1-1-1-1h-1l2-2-1-2h0 0c1-1 1-1 1-2h1v-2-3h1l1 1 2 1h2l2 1v1z" class="M"></path><path d="M570 662l2 1v1l1 2h1c0 1-1 2-1 2l2 1v1l-3 1-1 1h-1l-2-1c2-2 1-3 2-4l-1-1v-1h1l-1-1c1-1 1-1 1-2z" class="R"></path><path d="M577 672l2 2c-2 0-4 0-6 2h0l-1 2h1c0 1-2 2-2 2-3 1-3-2-4 2-2 0-2 0-3-1l1-3h1l1 1 2-2c0-1 1-2 2-2 1-1 2-1 4-2v-1h2z" class="i"></path><path d="M572 664h1 0c1-2 2-3 2-4l7 3c-1 0-4 0-5 1v1 1h0v2l1 2h-2v1l1 1h-2-4l1-1 3-1v-1l-2-1s1-1 1-2h-1l-1-2z" class="l"></path><path d="M564 660h1l1 1 2 1-3 2 1 2h-1v3l-1 1v1h3v1h-2v1c1 0 1 1 2 0h4c-1 1-2 1-2 2h-2c-1 1-1 2-1 3h-1l-1 3c-1-1-2-2-4-2h0c1-1 1-1 1-2v-3h1v-1l1-1-1-1h-1l2-2-1-2h0 0c1-1 1-1 1-2h1v-2-3z" class="F"></path><path d="M561 677v-3h1 3l1 2v1h-1-4z" class="T"></path><path d="M581 642c3 0 6 0 8 1h1l6 2c2 0 2 1 4 2l1 2c1 3 2 6 1 9v1h-1 0v-1c-2 2-4 3-6 4h-2c-1 1-2 1-2 1h-2c-2 0-4 1-6 0h0-1l-7-3-5-3h0l1-1h0c0-1-1-1-2-2l2-2c1-1 2-1 2-2h0c1-1 3-2 4-3v-1l3-3h0v-1h1z" class="M"></path><path d="M591 654h1c0-2 0-3 1-4 1 0 1 1 2 2 1 0 1 1 1 2 0 0 1 0 1 1 0 2-1 3-3 5-1 1-2 1-2 1-2 0-3 0-5 1v-3c1 0 3-1 4-1v-4z" class="Q"></path><path d="M581 642c3 0 6 0 8 1h1l6 2-1 2s-1 0-2 1v-1h-1c-3-1-8-2-11-1l-4 1v-1l3-3h0v-1h1z" class="W"></path><path d="M596 645c2 0 2 1 4 2l1 2c1 3 2 6 1 9v1h-1 0v-1c-2 2-4 3-6 4h-2c-1 1-2 1-2 1h-2l3-2s1 0 2-1c2-2 3-3 3-5v-2c0-3-2-4-4-5 1-1 2-1 2-1l1-2z" class="d"></path><path d="M578 660c0-1-1-1-1-1v-1c-1-1-2-3-4-4h0c3-1 3-2 5-4h2 7 2c2 1 1 2 1 4h1v4c-1 0-3 1-4 1v3c-3 0-6 0-9-2z" class="Q"></path><path d="M590 654h1v4c-1 0-3 1-4 1v3c-3 0-6 0-9-2 2-1 3 0 4 0l2-2c0-1 0 0 1-1 0 1 0 1 1 2 1-1 3-1 4-3-1 0-2 0-2-2h2z" class="L"></path><path d="M452 446v1c0 3-3 5-2 8 1 1 1 1 2 0h1l-1 18 2 1v7 8 2l-1 1-1-1-1 8h-6c-1-1-3-1-5-1-1 1-7 0-9-1v1l2 2h-9v-1h-2c-2-1-3 0-5 0h0l-3 1-7-1c-1 0-2-1-3-1h-9l-3-3c1-1 1-1 1-2v-1l9-5c2-1 4-2 5-2h2v-1l1-1c1-1 2-2 4-2 1-1 1-2 2-3 1 0 3-2 4-2v-2h1c1 0 1 0 3-1l1-1 1-1c1-1 2-1 3-2l2-2-1-3h-1l-1-1 9-6 4-4h0c3-1 5-2 7-4h1c1-1 2-1 3-3z" class="o"></path><path d="M409 484l1-1v1l2 1v1l-1 1h-2v-3zm6 7v-2c0-1-1-3-1-4h-1l1-1h2c0 1 0 1 1 2l3-3v1l-1 4h-3v1c3 1 6 0 10 0l-1 1h-9l-1 1h0z" class="d"></path><path d="M426 489h1v1 1h2v1l1 1-5 1c-2-1-5-1-7-1h0c-1-1-2-1-3-2l1-1h9l1-1z" class="H"></path><path d="M415 491h0c1 1 2 1 3 2h0c2 0 5 0 7 1 1 1 1 1 1 2h-2l1 1h0c1 0 2 1 2 1h4l2 2h-9v-1h-2c-2-1-3 0-5 0h0l-3 1v-2h1v-3h0v-1h-1v-1c1 0 1-1 1-2z" class="X"></path><path d="M417 499l-1-1c1-1 1-2 2-2 2 0 4 0 6 1v2h-2c-2-1-3 0-5 0z" class="B"></path><path d="M407 485h1c1 5 1 9 6 12v-3h1v1h0v3h-1v2l-7-1c-1 0-2-1-3-1h-9l-3-3c1-1 1-1 1-2v-1l9-5c2-1 4-2 5-2z" class="f"></path><path d="M404 498c2 0 8-1 10 0v2l-7-1c-1 0-2-1-3-1z" class="U"></path><path d="M406 486h1c0 2 2 5 2 7-2 0-3 0-4-1l-1-1 1-2c1 0 1 0 2-1h-1-2c0-1 1-1 2-2z" class="X"></path><path d="M425 488v-1h1c1 0 1 0 2 1l1-1-1-2v-1l1 1 1-1-1-1 1-1c1 0 1 1 1 1v1c1 0 1 0 2 1h1l1-1c1 1 0 2 0 3l3-1v-1-1h4c1 1 1 2 1 3v1h2l-1 1h-2c1 1 1 1 2 1-1 2-1 2 0 4l-1 2h0-2l-1 2c-1 1-7 0-9-1v1h-4s-1-1-2-1h0l-1-1h2c0-1 0-1-1-2l5-1-1-1v-1h-2v-1-1h-1c-4 0-7 1-10 0v-1h3 6z" class="M"></path><path d="M434 492h1c2 0 2 0 4 1h-1l-2 1 2 2c-1 0-2 1-4 0-1 0-2 0-3 1h0v1h-4s-1-1-2-1h0l-1-1h2c0-1 0-1-1-2l5-1h1c1-1 2-1 3-1z" class="W"></path><path d="M434 492h1v1h-3-1c-1 1-4 2-5 3 0-1 0-1-1-2l5-1h1c1-1 2-1 3-1z" class="J"></path><path d="M425 488v-1h1c1 0 1 0 2 1l1-1-1-2v-1l1 1 1-1-1-1 1-1c1 0 1 1 1 1v1c1 0 1 0 2 1h1l1-1c1 1 0 2 0 3-1 0-2 0-3 1h8c-1 1-3 1-4 1 1 1 3 1 4 1v1h-5l-1 1c-1 0-2 0-3 1h-1l-1-1v-1h-2v-1-1h-1c-4 0-7 1-10 0v-1h3 6z" class="f"></path><path d="M428 463l9-6c1 0 1 0 1 1 0 0-2 2-2 3v2c1 1 0 2 0 4l-1 1c-1 1-1 1-1 2 0 2 1 3 1 4h1v1h0v6h0c1 1 2 2 2 4h0v1l-3 1c0-1 1-2 0-3l-1 1h-1c-1-1-1-1-2-1v-1s0-1-1-1l-1 1 1 1-1 1-1-1v1l1 2-1 1c-1-1-1-1-2-1h-1v1h-6l1-4v-1-1c-1-2-2-2-4-4 1 0 3-2 4-2v-2h1c1 0 1 0 3-1l1-1 1-1c1-1 2-1 3-2l2-2-1-3h-1l-1-1z" class="J"></path><path d="M420 484h0 2c1 1 2 2 3 4h-6l1-4z" class="h"></path><path d="M421 476c2 1 2 2 3 3s2 2 2 3l-1 1c-1 0-3 0-4-1l1-1c-1-1-1-2-2-3l1-2z" class="d"></path><path d="M435 474h1v1h0v6h0c1 1 2 2 2 4h0v1l-3 1c0-1 1-2 0-3 0-2-1-2-2-3v-1-1c1-2 1-3 2-5z" class="W"></path><defs><linearGradient id="T" x1="455.364" y1="472.169" x2="438.403" y2="477.049" xlink:href="#B"><stop offset="0" stop-color="#100f0e"></stop><stop offset="1" stop-color="#2a2924"></stop></linearGradient></defs><path fill="url(#T)" d="M452 446v1c0 3-3 5-2 8 1 1 1 1 2 0h1l-1 18 2 1v7 8 2l-1 1-1-1-1 8h-6c-1-1-3-1-5-1l1-2h2 0l1-2c-1-2-1-2 0-4-1 0-1 0-2-1h2l1-1h-2v-1c0-1 0-2-1-3h-4v1h0c0-2-1-3-2-4h0v-6h0v-1h-1c0-1-1-2-1-4 0-1 0-1 1-2l1-1c0-2 1-3 0-4v-2c0-1 2-3 2-3 0-1 0-1-1-1l4-4h0c3-1 5-2 7-4h1c1-1 2-1 3-3z"></path><path d="M444 494l2-1 1 1c-1 1-2 1-4 2l1-2z" class="B"></path><path d="M452 473l2 1v7h-2v-8z" class="f"></path><path d="M452 491v-10h2v8 2l-1 1-1-1z" class="g"></path><path d="M442 475h3c0 2 1 6 0 8h-6c1-1 2-1 2-1l1-7z" class="a"></path><path d="M436 475h6l-1 7s-1 0-2 1c0 0-1 0-1 1v1h0c0-2-1-3-2-4h0v-6h0z" class="K"></path><path d="M442 463c1 0 1-1 3-1 1 1 1 1 1 3l-1 10h-3-6v-1h2c1 1 4 0 5-1-1-3 0-7-1-10zm6-14c1 2 0 4-1 5v3c-1 1-2 3-3 4-1-1-2-2-3-2l-1-1v-1c1-1 2-2 2-3l-1-1h0c3-1 5-2 7-4z" class="T"></path><path d="M441 453l1 1c0 1-1 2-2 3v1l1 1c1 0 2 1 3 2l-6 5-2 1c0-2 1-3 0-4v-2c0-1 2-3 2-3 0-1 0-1-1-1l4-4z" class="R"></path><path d="M442 463c1 3 0 7 1 10-1 1-4 2-5 1h-2-1c0-1-1-2-1-4 0-1 0-1 1-2l1-1 2-1c1 0 3-2 4-3z" class="V"></path><path d="M435 468h2c0 2 1 4 1 6h-2-1c0-1-1-2-1-4 0-1 0-1 1-2z" class="L"></path><path d="M585 812c3-1 6-1 9-1v1c-1 1-1 3-2 4h4c4 2 11 0 15 0-1 1-6 1-8 2l1 1-9 1c5 4 10 5 15 9-9 1-19 2-28 4-24 6-47 19-61 39-2 2-3 4-4 6 0 2-1 3-2 5 0 2-1 5-2 6 0 1-1 1-1 2-1 1 0 1-1 2v-1-1c0-2 1-4 1-6l1-1c1-2 0 0 0-1 1-2 2-3 1-5h-1-1c1-4 2-8 2-13 3-6 7-12 12-18l9-9c1 0 2-2 3-2 2-2 4-4 7-5 3-3 7-5 10-7 2 1 4 4 6 5v-2l-1-2v-1-2c1 0 2-1 2-1l2-2h0c1 0 2-1 2-1l19-6z" class="n"></path><path d="M592 816h4c4 2 11 0 15 0-1 1-6 1-8 2-6 0-12 2-19 3l-14 5-4 2c-1-1-1-2-2-1v2c-4 3-9 5-13 8-1 1-5 4-6 4-2 0-2 1-4 2l2-3 18-11v-2l-1-2v-1c2 1 3 1 5 2l27-10z" class="F"></path><path d="M585 812c3-1 6-1 9-1v1c-1 1-1 3-2 4l-27 10c-2-1-3-1-5-2v-2c1 0 2-1 2-1l2-2h0c1 0 2-1 2-1l19-6z" class="e"></path><defs><linearGradient id="U" x1="539.727" y1="859.244" x2="528.13" y2="842.846" xlink:href="#B"><stop offset="0" stop-color="#b3ac9e"></stop><stop offset="1" stop-color="#d9ceba"></stop></linearGradient></defs><path fill="url(#U)" d="M555 824c2 1 4 4 6 5l-18 11-2 3c2-1 2-2 4-2-10 8-19 19-25 30-1 2-3 4-3 6v1c0 2-1 3-2 5 0 2-1 5-2 6 0 1-1 1-1 2-1 1 0 1-1 2v-1-1c0-2 1-4 1-6l1-1c1-2 0 0 0-1 1-2 2-3 1-5h-1-1c1-4 2-8 2-13 3-6 7-12 12-18l9-9c1 0 2-2 3-2 2-2 4-4 7-5 3-3 7-5 10-7z"></path><path d="M541 843l-2 2c-2 1-3 2-4 3h-2c1-3 4-5 6-7l1-1 4-2c0 1 0 1-1 2h0l-2 3z" class="X"></path><path d="M545 831c-2 2-4 4-5 7-1 0-1 0-2 1h-1c-2 1-3 3-5 4-2 2-3 4-6 4l9-9c1 0 2-2 3-2 2-2 4-4 7-5z" class="b"></path><path d="M530 410h3l2 1 2-1c3 2 7 3 10 3h1 1l1-1v3l1 11c0 3 0 5 1 8 1 2 2 3 3 4h0l1-1 1 1c1-1 1-2 1-3v7l1 1c1 3 7 9 6 11h-1c-1 0-1 0-2-1l-1 1 1 1c-1 2-1 2-1 4 0 1 0 1 1 3h-1l-4-3h0c0 2 0 3-1 4s-2 3-4 4h0l-2 2c-2 4 1 10 0 15l-1-1c-1-1 0-1-1-1v1h-1c-1 0-2 0-3 1 1 0 2 0 2 2 0 1 1 1 1 2v1l-2 2-2-1v-1s-1 0-1-1c-2 0-3-2-4-2h-3v1c-2-1-2-2-3-3h0l-3-1c1-3 0-7 0-10v-6-23-7l-1-26c1 0 1-1 2-1z" class="m"></path><path d="M533 447h0c0-1 0-1-1-1h0v-1c2-1 4-1 5 0h1l1 1c-1 1-1 1-2 1h-4z" class="N"></path><path d="M542 430c2 1 3 3 4 4-3-1-3-1-5 0h-5c-2 0-3 0-5-1v-2l-1-1c4 0 9 1 12 0z" class="P"></path><path d="M531 431c2 0 5 1 7 1 0 1-1 1-2 2-2 0-3 0-5-1v-2z" class="B"></path><path d="M538 445h7 0l1 3c1 1 2 3 2 4h-1-6-3c-2 0-4 0-6-2v-1c0-1 0-1 1-2h4c1 0 1 0 2-1l-1-1z" class="C"></path><path d="M538 452c1-1 0-1 2-1h0v-1h-2l-1-1c3-1 5-1 9-1 1 1 2 3 2 4h-1-6-3z" class="M"></path><path d="M545 445l5 1c1 2 7 9 6 10s-2 1-3 2c-2 0-3-1-5-3 0 2 1 3-1 4-1 1-2 2-4 3l-1-1-4-1c-2 1-4 0-6 0h-1c0-2 0-3 1-4l1-1-1-1c2-2 6 0 8-2h1 6 1c0-1-1-3-2-4l-1-3z" class="a"></path><path d="M536 454h11 0c0 1 0 1-1 1h0c-4 1-7 0-10-1z" class="M"></path><path d="M539 458c1 0 2 0 2 1h-2l-1 1c-2 1-4 0-6 0 1-1 1 0 1-1 1-1 4-1 6-1z" class="G"></path><path d="M539 458c-1-1-2-1-3-1l1-1c2 0 5 1 8 1h1l1-1v1 2c-1 1-2 2-4 3l-1-1-4-1 1-1h2c0-1-1-1-2-1z" class="Q"></path><path d="M531 436h0l-1 1v2h-1v-5l2-1c2 1 3 1 5 1h5c2-1 2-1 5 0 4 7 10 14 15 20l1 1c-1 2-1 2-1 4 0 1 0 1 1 3h-1l-4-3-1-1-3 1v-1c1-1 2-1 3-2s-5-8-6-10l-5-1h0-7-1c2-1 2-1 3-1l-1-1-2-1c-2 0-5 0-7-1l1-1v-1l1-1c0-1 0-2-1-2z" class="Y"></path><path d="M532 436l8-1 3 1v1h-6c-2 0-3 0-5-1z" class="P"></path><path d="M531 436h0l-1 1v2h-1v-5l2-1c2 1 3 1 5 1h5l-4 1h3l-8 1h-1z" class="O"></path><path d="M534 438h11l1 1c1 1 1 2 1 3 1 2 2 2 3 3v1l-5-1h0-7-1c2-1 2-1 3-1l-1-1-2-1c-2 0-5 0-7-1l1-1h2l-1-1 2-1z" class="P"></path><path d="M534 438h11l1 1c-3 1-9 0-12 0v-1z" class="G"></path><path d="M537 442h10c1 2 2 2 3 3v1l-5-1h0-7-1c2-1 2-1 3-1l-1-1-2-1z" class="B"></path><path d="M530 410h3l2 1 2-1c3 2 7 3 10 3h1 1l1-1v3l1 11c0 3 0 5 1 8 1 2 2 3 3 4h0l1-1 1 1c1-1 1-2 1-3v7l1 1c1 3 7 9 6 11h-1c-1 0-1 0-2-1l-1 1c-5-6-11-13-15-20-1-1-2-3-4-4-4-6-9-13-12-20z" class="k"></path><path d="M552 434c1 2 2 3 3 4h0l1-1 1 1c1-1 1-2 1-3v7l1 1c1 3 7 9 6 11h-1c-1 0-1 0-2-1-2-3-3-6-5-9s-4-6-5-9v-1z" class="H"></path><defs><linearGradient id="V" x1="549.639" y1="419.638" x2="536.464" y2="420.33" xlink:href="#B"><stop offset="0" stop-color="#1c1b1b"></stop><stop offset="1" stop-color="#3e3e3e"></stop></linearGradient></defs><path fill="url(#V)" d="M535 411l2-1c3 2 7 3 10 3h1 1l1-1v3l1 11c0 3 0 5 1 8v1l-6-9-11-15z"></path><path d="M547 459c2-1 1-2 1-4 2 2 3 3 5 3v1l3-1 1 1h0c0 2 0 3-1 4s-2 3-4 4h0l-2 2c-2 4 1 10 0 15l-1-1c-1-1 0-1-1-1v1h-1c-1 0-2 0-3 1 1 0 2 0 2 2 0 1 1 1 1 2v1l-2 2-2-1v-1s-1 0-1-1c-2 0-3-2-4-2h-3v1c-2-1-2-2-3-3h0l-3-1c1-3 0-7 0-10v-6c1-2 1-5 1-8l1 1h1c2 0 4 1 6 0l4 1 1 1c2-1 3-2 4-3z" class="O"></path><path d="M534 471h1 1 2 3l-1 1 1 7c-1-1-2-2-3-2h-1c-1 0-2-1-3-1v-2c1 0 1 0 1-1h-1v-2z" class="D"></path><path d="M540 472l1 7c-1-1-2-2-3-2h-1l1-1c-1-1-1-1-1-2l3-2z" class="M"></path><path d="M532 484l1-1-1-1 1-1c0-1-1-2-1-3l1-1c0-1 0-2 1-3v2c1 0 2 1 3 1h1c1 0 2 1 3 2s2 2 4 3h3v1h-1c-1 0-2 0-3 1 1 0 2 0 2 2 0 1 1 1 1 2v1l-2 2-2-1v-1s-1 0-1-1c-2 0-3-2-4-2h-3v1c-2-1-2-2-3-3h0z" class="Q"></path><path d="M538 484h1l2 2h1v-1l1-1c1 2 1 3 1 5l-1 1v-1s-1 0-1-1l-4-4z" class="c"></path><path d="M544 484c1 0 2 0 2 2 0 1 1 1 1 2v1l-2 2-2-1 1-1c0-2 0-3-1-5h1z" class="E"></path><path d="M532 484l1-1-1-1 1-1c0-1-1-2-1-3l1-1c0-1 0-2 1-3v2c1 0 2 1 3 1v4c0 1 1 2 1 3l4 4c-2 0-3-2-4-2h-3v1c-2-1-2-2-3-3h0z" class="I"></path><path d="M534 476c1 0 2 1 3 1v4h-1 0c-1-2-1-3-2-5z" class="G"></path><path d="M536 481h1c0 1 1 2 1 3l4 4c-2 0-3-2-4-2h-3v1c-2-1-2-2-3-3h4c-1-1-2-2-2-3h0 2z" class="C"></path><path d="M529 467c1-2 1-5 1-8l1 1h1c2 0 4 1 6 0l4 1-1 2h-1c-1 0-2 2-3 3v1h0l-2 2-2 1c0 1 0 0 1 1v2h1c0 1 0 1-1 1-1 1-1 2-1 3l-1 1c0 1 1 2 1 3l-1 1 1 1-1 1-3-1c1-3 0-7 0-10v-6z" class="O"></path><path d="M547 459c2-1 1-2 1-4 2 2 3 3 5 3v1l3-1 1 1h0c0 2 0 3-1 4s-2 3-4 4h0l-2-1-2 1c-2 1-5 4-7 4h-3-2-1-1c-1-1-1 0-1-1l2-1 2-2h0v-1c1-1 2-3 3-3h1l1-2 1 1c2-1 3-2 4-3z" class="Q"></path><path d="M538 471l2-2c3-2 5-3 8-2-2 1-5 4-7 4h-3zm12-5c1-2 4-4 5-6 1-1 0 0 2-1 0 2 0 3-1 4s-2 3-4 4h0l-2-1z" class="G"></path><path d="M547 459c2-1 1-2 1-4 2 2 3 3 5 3v1c-3 1-6 2-8 4-3 2-6 4-10 6l2-2h0v-1c1-1 2-3 3-3h1l1-2 1 1c2-1 3-2 4-3z" class="S"></path><path d="M517 402l1 1v1c1 1 2 2 2 3h1c-1 1-2 1-2 2l-1 1c0 2 1 3 3 4 2 0 3 0 4-2l1-1h0l2-1v1l1 26v7 23 6c0 3 1 7 0 10l3 1h0c1 1 1 2 3 3v-1h3c1 0 2 2 4 2 0 1 1 1 1 1 0 2 0 3 1 4l2 2-1 1v2 1l-1 1h-1-2l-9-9-1-1h-2l-1 4-1-1c-1 2-2 4-4 5 0 1-1 1-1 2-4-4-8-5-13-7h-5l-1-4c1-1 1-2 1-3l4-1 1-18v-30-28c-1-2-1-3 0-5h1 2c2 0 3-1 5-2z" class="k"></path><path d="M514 423c0-1 1-2 1-3l1-1c0 1 1 1 1 2s-1 2-1 3l-1-1h-1zm9-5s1 0 1 1v3 9 4 4c-2-2-1-4-1-6l-1-2v-5c0-1-1-1-1-2 1-1 1-1 1-2 0-2 1-3 1-4z" class="Z"></path><defs><linearGradient id="W" x1="517.196" y1="423.546" x2="515.363" y2="451.058" xlink:href="#B"><stop offset="0" stop-color="#b6ad9b"></stop><stop offset="1" stop-color="#d4cdbe"></stop></linearGradient></defs><path fill="url(#W)" d="M517 421c1 0 2 0 2 1s-1 1-1 2v4c0 2-1 10 1 12 0 1-1 2 0 3v2 5 2h-5l-1-16v-13h1 1l1 1c0-1 1-2 1-3z"></path><path d="M517 402l1 1v1c1 1 2 2 2 3h1c-1 1-2 1-2 2l-1 1c-1 0-1 0-1-1h0c-2 0-3 0-5-1l1 1c0 1 1 1 2 2 2 1 3 2 4 4h0c-1 1-1 0-1 1l-5-5v12 13l1 16v1 10c-1 0-1 0-1-1l-2-2h0v-4c-1-1-1-2-2-4v15h0v-30-28c-1-2-1-3 0-5h1 2c2 0 3-1 5-2z" class="h"></path><path d="M513 436c0-1-1-1-1-2-1-1 0-4 0-6-1-1 0-1 0-2l-1-1c-1-1 0-3 0-4-1-4 0-8-1-12v-1h2l1 1c0 1 1 1 2 2 2 1 3 2 4 4h0c-1 1-1 0-1 1l-5-5v12 13z" class="U"></path><path d="M512 408c2 1 3 1 5 1h0c0 1 0 1 1 1 0 2 1 3 3 4 2 0 3 0 4-2l1-1h0l2-1v1l1 26v7 23 6c0 3 1 7 0 10l3 1h0c1 1 1 2 3 3v-1h3c1 0 2 2 4 2 0 1 1 1 1 1 0 2 0 3 1 4l2 2-1 1v2 1l-1 1h-1-2l-9-9-1-1h-2l-1 4-1-1c-1 2-2 4-4 5l1-2c0-1-1-2-1-2 0-1 1-2 1-3v-7-7-29c1-3 0-9 0-13v-4-9-3c0-1-1-1-1-1v-1c-2 0-2 0-3 1l-2-2c0-1 0 0 1-1h0c-1-2-2-3-4-4-1-1-2-1-2-2l-1-1z" class="l"></path><path d="M526 411h0l2-1v1l1 26v7c-1 2-1 4-1 6h-2c0-2-1-2 0-4v-1c-1-1 0-5 0-7l-1-18v-5h0c0-1 1-2 1-3v-1z" class="H"></path><path d="M524 448c2 3 1 8 1 11l1 20h3v-6c0 3 1 7 0 10l3 1h0c1 1 1 2 3 3v-1h3c1 0 2 2 4 2 0 1 1 1 1 1 0 2 0 3 1 4l2 2-1 1v2 1l-1 1h-1-2l-9-9-1-1h-2l-1 4-1-1c-1 2-2 4-4 5l1-2c0-1-1-2-1-2 0-1 1-2 1-3v-7-7-29z" class="M"></path><path d="M526 479h3c0 1 0 3-1 5h-2v-5h0z" class="g"></path><path d="M524 484l5 4-2 5c-1 2-2 4-4 5l1-2c0-1-1-2-1-2 0-1 1-2 1-3v-7z" class="k"></path><path d="M529 483l3 1h0c1 1 1 2 3 3v-1h3c1 0 2 2 4 2 0 1 1 1 1 1 0 2 0 3 1 4l2 2-1 1v2 1l-1 1h-1v-1l-2-1-7-7v-1l-1-1c-1-1-3-4-4-4v-2z" class="V"></path><path d="M529 483l3 1h0c1 1 1 2 3 3v-1c3 3 7 5 9 9h0l-9-7v1s1 1 1 2l-1-1h-1l-1-1c-1-1-3-4-4-4v-2z" class="E"></path><path d="M519 450c1 1 1 2 1 3l2 1v3 9c0 1 0 1 1 2 0 2-1 4 0 6v1l1 1v1 7 7c0 1-1 2-1 3 0 0 1 1 1 2l-1 2c0 1-1 1-1 2-4-4-8-5-13-7h-5l-1-4c1-1 1-2 1-3l4-1 1-18h0v-15c1 2 1 3 2 4v4h0l2 2c0 1 0 1 1 1v-10-1h5v-2z" class="o"></path><path d="M518 468v-6l1 2v15c-1 1-1 1-2 1l1-12z" class="h"></path><path d="M519 450c1 1 1 2 1 3-2 3-1 8-1 11l-1-2v6c0-3 1-12-1-15h-3v-1h5v-2zm-5 29l1 1v4h1l1-2h1v1c1 1 2 1 2 3v2c-1 1-1 1-3 2v-2c-2-1-3 1-4 0v-2c0-1 0-2 1-2v-1-1-3z" class="Z"></path><path d="M509 493h3 0l2-1c1-1 1-1 2-1l1 1h0 1 0c2-1 4-2 6-1 0 1-1 2-1 3 0 0 1 1 1 2l-1 2c0 1-1 1-1 2-4-4-8-5-13-7z" class="X"></path><path d="M514 453h3c2 3 1 12 1 15l-1 12h-1-1l-1-1v-10-6-10z" class="j"></path><path d="M509 467h0v-15c1 2 1 3 2 4v4h0l2 2c0 1 0 1 1 1v6 10 3 1 1c-1 0-1 1-1 2-1 0-2 0-3-1h0-2l1-18z" class="d"></path><path d="M514 177h21 22c8 0 17-1 24 0h0c-3 1-5 1-8 1l1 1h6 73 1v5 3h-9c-2-1-4 0-7-1h-1c-2-1-3-1-5-2h-31c-3 1-5 1-7 2-5 2-13-1-18 1h-2c-2 1-2 1-3 1h-1l-3 2h-7-2-4c-3 0-8-1-11-1-2 1-3 1-5 1l-5-1h0c-2 0-3 0-3-1l-1-1h-17-4c-3 0-8-1-11 0h-1v-1h-1c-1 0 0 0-1 1h-5-10-29-15c-5 0-13 0-19-1v-2l1-4s0-1 1-1h8 43 34c3-2 8-1 11-2z" class="b"></path><path d="M653 179h1v5 3h-9c-2-1-4 0-7-1h-1c-2-1-3-1-5-2h15 6v-5z" class="X"></path><path d="M416 184h33 17l-21 1-1 1c-3 0-6-1-9 1-5 0-13 0-19-1v-2z" class="o"></path><path d="M466 184h28c1 0 2 1 2 2h-1c-1 0 0 0-1 1h-5-10-29-15c3-2 6-1 9-1l1-1 21-1z" class="d"></path><path d="M514 177h21 22c8 0 17-1 24 0h0c-3 1-5 1-8 1l1 1h6-77c3-2 8-1 11-2z" class="f"></path><path d="M540 187l1-3h1v1c4-1 10-1 15-1h44c-3 1-5 1-7 2-5 2-13-1-18 1h-2c-2 1-2 1-3 1h-1l-3 2h-7-2-4c-3 0-8-1-11-1-2 1-3 1-5 1l-5-1h0c-2 0-3 0-3-1l-1-1h-17 28z" class="Z"></path><defs><linearGradient id="X" x1="555.362" y1="193.428" x2="551.138" y2="181.572" xlink:href="#B"><stop offset="0" stop-color="#161212"></stop><stop offset="1" stop-color="#333434"></stop></linearGradient></defs><path fill="url(#X)" d="M540 187h14 20c-2 1-2 1-3 1h-1l-3 2h-7-2-4c-3 0-8-1-11-1-2 1-3 1-5 1l-5-1h0c-2 0-3 0-3-1l-1-1h-17 28z"></path><defs><linearGradient id="Y" x1="670.217" y1="567.512" x2="588.32" y2="548.658" xlink:href="#B"><stop offset="0" stop-color="#9b9382"></stop><stop offset="1" stop-color="#b8ac95"></stop></linearGradient></defs><path fill="url(#Y)" d="M559 522c2 2 4 1 6 2h10 1c5 1 47 0 48 0 1 1 3 4 4 5l1 2c2 2 6 5 9 8l5 5c1 1 2 2 3 4h0-3c5 6 10 12 14 18 1 4 4 4 4 9a104.13 104.13 0 0 0 5 12h-1l1 1h-1-1c-1 1 0 1-1 1-3-9-7-17-11-25-11-17-27-26-46-30-2 0-3 1-4 1-3 2-4 5-7 8-1-1-2-1-4 0l-3 3-1 1-5 5c-1 2-4 4-6 6l-2 2c0 1 0 3-1 3-1 2-3 4-4 5h0c0 1 0 1 1 2l-4 1-4 2h-2c0 1-1 1-1 2v2-10c-2-2-1-5-1-8l-1 4h0v-15c0-1 0-2-2-3h-1v3h-1l1-11h-1v-3l2-4 2 1 2 3h0v-6h0v-6z"></path><path d="M637 543c2-1 3 0 6 1 1 1 2 2 3 4h0-3l-6-5z" class="T"></path><path d="M559 528h5c2 0 7-1 9 0h0c5 1 10 1 15 1-5 1-10 1-15 1-2 0-4 0-5 1h-1c-1 1-4 1-6 1h-1c0 1 0 3 1 3l1 1c-1 1-1 1-3 1v-3-6h0z" class="S"></path><path d="M568 531c13 0 25 0 38 3-2 0-3 1-4 1-3 2-4 5-7 8-1-1-2-1-4 0l-3 3-1 1-5 5 1-3c1 0 2-2 3-3-1-1-1-2-2-3-2-1-3-3-6-5l-1-1-1-1c-3 1-5 1-7 2-1 2-1 4-3 6l-3-3-1-1c0-1 0-1-1-2h-1v3h0l-1 1v-5c2 0 2 0 3-1l-1-1c-1 0-1-2-1-3h1c2 0 5 0 6-1h1z" class="I"></path><path d="M590 541l5-6h7c-3 2-4 5-7 8-1-1-2-1-4 0l-3 3-1 1 3-6z" class="W"></path><path d="M562 536c5-2 10-3 15-2h9c1 1 3 2 5 3v1c0 1-1 2-1 3l-3 6-5 5 1-3c1 0 2-2 3-3-1-1-1-2-2-3-2-1-3-3-6-5l-1-1-1-1c-3 1-5 1-7 2-1 2-1 4-3 6l-3-3-1-1c0-1 0-1-1-2h-1v3h0l-1 1v-5c2 0 2 0 3-1z" class="a"></path><path d="M584 543h0c-1-3-5-6-7-8 2 1 7 5 9 5h3v1c-1 1-2 3-3 5-1-1-1-2-2-3z" class="L"></path><path d="M562 536c5-2 10-3 15-2-3 1-7 1-9 3h-2c-1 0-1 0-2 1 0 1 0 2-1 3l-1-1c0-1 0-1-1-2h-1v3h0l-1 1v-5c2 0 2 0 3-1z" class="B"></path><path d="M559 522c2 2 4 1 6 2h10 1c5 1 47 0 48 0 1 1 3 4 4 5l1 2c2 2 6 5 9 8l5 5c-3-1-4-2-6-1l-2-1c-5-3-9-6-14-8-4-2-9-3-14-4-6-2-13-2-20-2-4 0-10-1-14 0-2-1-7 0-9 0h-5v-6z" class="I"></path><path d="M613 526c2 0 5-1 6 0v1l-4 1c-1-1-1-1-3-1h-2c1-1 2-1 3-1z" class="O"></path><path d="M587 528c4-1 7-1 10-1h6c2 0 5 1 8 1s8-1 11 2l3 3c-6-2-12-6-18-3-6-2-13-2-20-2z" class="B"></path><path d="M607 530c6-3 12 1 18 3 1 1 2 1 3 2 2 1 6 4 7 7-5-3-9-6-14-8-4-2-9-3-14-4zm-53 7h-1v-3l2-4 2 1 2 3h0v3 5l1-1h0v-3h1c1 1 1 1 1 2l1 1 3 3c2-2 2-4 3-6 2-1 4-1 7-2l1 1 1 1c3 2 4 4 6 5 1 1 1 2 2 3-1 1-2 3-3 3l-1 3c-1 2-4 4-6 6l-2 2c0 1 0 3-1 3-1 2-3 4-4 5h0c0 1 0 1 1 2l-4 1-4 2h-2c0 1-1 1-1 2v2-10c-2-2-1-5-1-8l-1 4h0v-15c0-1 0-2-2-3h-1v3h-1l1-11z" class="a"></path><path d="M560 573h0v-2c1-4 5-6 9-7h-1c-1 2-2 3-3 4 0 1 0 0 1 1l-1 2h1l-4 2h-2z" class="c"></path><path d="M574 560c0 1 0 3-1 3-1 2-3 4-4 5h0c0 1 0 1 1 2l-4 1h-1l1-2c-1-1-1 0-1-1 1-1 2-2 3-4h1l1-2c1 0 3-1 4-2z" class="K"></path><path d="M569 561s1 0 1-1 1-2 1-3l-1-2c2-1 2 1 3 2h1c1 1 2 1 2 1l-2 2c-1 1-3 2-4 2s-2 1-2 1c-2 1-4 2-5 2h-1l1-1 6-3z" class="L"></path><path d="M562 542c2 1 3 2 4 4v1c-1 3 2 7 2 11v2h-1l1 1h1l-6 3c1-2-2-3-1-5 0-1 1-3 1-4-1-4 0-9-1-13z" class="D"></path><path d="M562 559c2 0 4 0 6 1h-1l1 1h1l-6 3c1-2-2-3-1-5z" class="M"></path><path d="M554 537h-1v-3l2-4 2 1 2 3v9 24c-2-2-1-5-1-8l-1 4h0v-15c0-1 0-2-2-3h-1v3h-1l1-11z" class="J"></path><path d="M554 537h-1v-3l2-4 2 1 2 3v9l-1-1c-1-1-1-1-1-3v-2h-3z" class="f"></path><path d="M566 544c2-2 2-4 3-6 2-1 4-1 7-2l1 1 1 1c3 2 4 4 6 5 1 1 1 2 2 3-1 1-2 3-3 3l-1 3c-1 2-4 4-6 6 0 0-1 0-2-1h-1c-1-1-1-3-3-2l1 2c0 1-1 2-1 3s-1 1-1 1h-1l-1-1h1v-2c0-4-3-8-2-11v-1c-1-2-2-3-4-4v-2l1 1 3 3z" class="F"></path><path d="M566 544c2-2 2-4 3-6 2-1 4-1 7-2l1 1c-2 0-2 1-4 1-1 1-2 1-3 2s0 3 0 4l-2 1s-1-1-2-1z" class="E"></path><defs><linearGradient id="Z" x1="571.764" y1="543.809" x2="579.842" y2="552.378" xlink:href="#B"><stop offset="0" stop-color="#69635a"></stop><stop offset="1" stop-color="#898476"></stop></linearGradient></defs><path fill="url(#Z)" d="M574 557c-1-4-4-7-6-10v-1c1 0 2 1 3 2h0l-1-2 1-3v1l5 5v1h1l1-1v-1h1l3-2c1 0 1 1 2 2h-1v1l-1 3c-1 2-4 4-6 6 0 0-1 0-2-1z"></path><defs><linearGradient id="a" x1="575.956" y1="537.592" x2="579.051" y2="548.646" xlink:href="#B"><stop offset="0" stop-color="#69655c"></stop><stop offset="1" stop-color="#827a6c"></stop></linearGradient></defs><path fill="url(#a)" d="M578 538c3 2 4 4 6 5 1 1 1 2 2 3-1 1-2 3-3 3v-1h1c-1-1-1-2-2-2l-3 2h-1v1l-1 1h-1v-1l-5-5v-1-3c1-1 2-1 3-1h1v-1h3z"></path><path d="M658 440c0-2 1-4 1-6 1-2 1-3 2-5 0 1 1 2 0 4l-1-1v1 11h0c-1 1-1 2-1 4v1h-1c-1 1-1 2-2 3l1 1 1-2v2l-1 3 1 2 1-1v-1h-1l1-2h0l1 2 1-1 1 1v1c-1 1-4 3-5 5 1 1 1 2 2 3v1l2 3c2 1 4 1 7 1h0 3v11 23 16h-17-7c-2-1-6 0-9 0h1l1-2-2-1h4c3-1 3-2 4-4 0-2-1-3 0-5h-7c-1-1-2 0-3 0h-4-1-4l1-2v-1c-1 0-1 0-2-1-1 0-2 1-3 1 0-1 1-2 2-3v-1h0c3 0 4 0 7 1l1-3h-3-6c-1 1-2 0-4 0h-4 0c0-3 4-6 5-8 5-4 9-10 13-14 2-3 3-4 4-7 2-2 4-4 6-7l5-7 6-9 3-7z" class="V"></path><path d="M637 485h1 0c2-3 5-5 6-7h1l1-2c1-2 3-4 5-6l1 1c-1 2-6 4-5 7-1 2-4 3-5 5l-2 3v1c-1 1 0 2-2 2h-1l1-1v-1-1l-1-1z" class="Q"></path><path d="M640 487c2-2 6-6 8-7-2 2-6 6-6 8h0c-2 1-2 1-3 2-2 1-4 1-6 3-2 0-3 0-4 1h-1l-1-1c2-2 5-3 8-5-1-1-1 0-1-1l2-2h1l1 1v1 1l-1 1h1c2 0 1-1 2-2z" class="R"></path><path d="M656 471l1-1c1 1 1 4 1 6l1 2c1 0 2 0 2-1v-2h1v4h-1c-1 2-1 4 0 6v2l-4 1h-8l-2-2-2-1-3 3c0-2 4-6 6-8 3-2 5-6 7-9h1z" class="L"></path><path d="M657 488l-4-1c1-1 1-1 1-2v-1h3c1 0 2 0 4 1v2l-4 1z" class="R"></path><path d="M648 480c3-2 5-6 7-9h1c1 3 1 7-1 10v1h-1v-2-1l-1 1-1-1c-1 1-2 1-3 2l-1 1v1l2 1h0c-1 1-3 1-4 2v-1h-1l-3 3c0-2 4-6 6-8z" class="m"></path><path d="M653 461h1l-3 5c-1 1-1 1-1 2l-1 1c-2 1-4 5-6 8l-6 7-1 1-2 2c0 1 0 0 1 1-3 2-6 3-8 5l-3 3c-2 1-3 1-4 3h-4 0c0-3 4-6 5-8 5-4 9-10 13-14 2-3 3-4 4-7l2 2h-1c-1 1-1 3-1 4-1 2-3 3-4 5l3 1 16-21z" class="B"></path><path d="M634 481l3 1c-4 5-8 7-13 11l-3 3 13-15z" class="f"></path><path d="M658 440c0-2 1-4 1-6 1-2 1-3 2-5 0 1 1 2 0 4l-1-1v1 11h0c-1 1-1 2-1 4v1h-1c-1 1-1 2-2 3l1 1 1-2v2l-1 3c-1 1-3 3-4 5l-16 21-3-1c1-2 3-3 4-5 0-1 0-3 1-4h1l-2-2c2-2 4-4 6-7l5-7 6-9 3-7z" class="D"></path><path d="M638 476c2-2 4-7 6-10l5-5 3-5 1-1c3-1 2-5 5-7v1c-1 1-1 2-2 3l1 1 1-2v2l-1 3c-1 1-3 3-4 5l-16 21-3-1c1-2 3-3 4-5z" class="g"></path><path d="M658 476h1c1 0 1 0 1-1l1-1c-2-2-4-4-5-6v-1c2 1 2 2 3 3l2-1c2 1 4 1 7 1h0 3v11c-1 4-1 9 0 14 0 2 0 4-1 6-2-1-5-1-7-2h0l-1-2v-1l-1-1c1-1 0-1 1-1l1-1h0l1-1c-1 0-2-1-4-1h2c-1-2-1-2-1-4v-2c-1-2-1-4 0-6h1v-4h-1v2c0 1-1 1-2 1l-1-2z" class="P"></path><path d="M661 469c2 1 4 1 7 1l2 1v1c-2 1-3 0-4 0-2 1-4 0-5 0l-2-2 2-1z" class="S"></path><path d="M658 476h1c1 0 1 0 1-1l1-1c-2-2-4-4-5-6v-1c2 1 2 2 3 3l2 2 3 3v4 2 1h0 1v1 1h-1v10 4l-1 1-1-2v-1l-1-1c1-1 0-1 1-1l1-1h0l1-1c-1 0-2-1-4-1h2c-1-2-1-2-1-4v-2c-1-2-1-4 0-6h1v-4h-1v2c0 1-1 1-2 1l-1-2z" class="Q"></path><path d="M642 488l3-3 2 1 2 2h8l4-1c0 2 0 2 1 4h-2c2 0 3 1 4 1l-1 1h0l-1 1c-1 0 0 0-1 1l1 1v1l1 2h0l-2-1c-3-1-5-1-8-1h-4-1-1l-1 1h-1c-2-1-3 0-5 0h-1c-1-1-2 0-3 0s-1 1-3 1h-3-6c-1 1-2 0-4 0 1-2 2-2 4-3l3-3 1 1h1c1-1 2-1 4-1 2-2 4-2 6-3 1-1 1-1 3-2h0z" class="F"></path><path d="M642 488l3-3 2 1 2 2h-4-2-1 0 0z" class="c"></path><path d="M627 493l1 1h1c1-1 2-1 4-1l-4 3-2 1-3 2c-1 1-2 0-4 0 1-2 2-2 4-3l3-3z" class="Q"></path><path d="M637 492l4 2v1h2v1c-2 1-4 1-7 2-1 0-1 1-3 1h-3c0-2 0-2 1-2h3l2-1-1-1 2-3z" class="E"></path><path d="M637 492h1c1-1 1-1 2-1h1c2-1 4-1 5-1h4l-1 1 1 1 1 1-1-1c1-1 3-1 4-1l1 1c-2 1-2 2-3 2-2 0-2 0-3-1h-4l-1 1c-1 0-2 1-3 0l-4-2z" class="Q"></path><path d="M657 491h3 0c2 0 3 1 4 1l-1 1h0l-1 1c-1 0 0 0-1 1l1 1v1l1 2h0l-2-1c-3-1-5-1-8-1h-4-1-1l-1 1h-1c-2-1-3 0-5 0h-1c-1-1-2 0-3 0 3-1 5-1 7-2v-1h-2v-1c1 1 2 0 3 0l1-1h4c1 1 1 1 3 1 1 0 1-1 3-2l2-1z" class="M"></path><path d="M656 494c2 0 3 0 5 1l1 1v1-1h-3c-1 0-1 0-2-1l-1-1z" class="F"></path><path d="M657 491h3 0c2 0 3 1 4 1l-1 1h0l-1 1c-1 0 0 0-1 1-2-1-3-1-5-1v-1l1-2z" class="c"></path><path d="M670 501c1-2 1-4 1-6-1-5-1-10 0-14v23 16h-17-7c-2-1-6 0-9 0h1l1-2-2-1h4c3-1 3-2 4-4 0-2-1-3 0-5h-7c-1-1-2 0-3 0h-4-1-4l1-2v-1c-1 0-1 0-2-1-1 0-2 1-3 1 0-1 1-2 2-3v-1h0c3 0 4 0 7 1l1-3c2 0 2-1 3-1s2-1 3 0h1c2 0 3-1 5 0h1l1-1h1 1 4c3 0 5 0 8 1l2 1c2 1 5 1 7 2z" class="I"></path><path d="M667 513c1-1 1-2 4-2 0 2 0 5-1 6h-9 0v-2h3c2-1 2 0 3-2z" class="O"></path><path d="M628 506h1c2-2 3 0 4-2h1c0 1 0 0 1 1l1-1 1 1h0l3-3h1l-1 1c0 1-1 1-1 2l1-1h2 3l1 1v3h-7c-1-1-2 0-3 0h-4-1-4l1-2z" class="h"></path><path d="M670 501c1-2 1-4 1-6-1-5-1-10 0-14v23 16h-17-7c-2-1-6 0-9 0h1l1-2-2-1h4c3-1 3-2 4-4 0 2 0 3 2 4 4 1 20 2 22 0 1-1 1-4 1-6s0-3-1-6c0-1-1-2 0-4z" class="V"></path><path d="M636 498c1 0 2-1 3 0h1c2 0 3-1 5 0h1l1-1h1 1 4l-3 2h-1l-1 3v1l2-2c0 1 0 2-1 2l-1 2v1c0 2 1 3 1 5l3 1h1c1 0 1 1 2 1h8c1 1 1 1 1 2h-3c-3 0-9 1-11-1s-3-5-3-8h0c0-2 1-4 0-5-2 0-3-1-4 0h-1-2 0-3-2-1l-1 1h-1l1-3c2 0 2-1 3-1z" class="H"></path><path d="M670 505c1 3 1 4 1 6-3 0-3 1-4 2-1 2-1 1-3 2 0-1 0-1-1-2h-8c-1 0-1-1-2-1h-1l-3-1c0-2-1-3-1-5h14l8-1z" class="a"></path><path d="M670 505c1 3 1 4 1 6-3 0-3 1-4 2l1-2h0v-3h1c-1-2-5-1-7-2h0l8-1z" class="B"></path><path d="M653 497c3 0 5 0 8 1l2 1c2 1 5 1 7 2-1 2 0 3 0 4l-8 1h-14v-1l1-2c1 0 1-1 1-2l-2 2v-1l1-3h1l3-2zm-99-13c1-1 2-1 4-1v1l2 1c0 1 0 3-1 4-1 3 0 7 0 10v10 11h-2-4-3l-2 1v2h-16l-1 3-1-1c-1 2-2 3-3 4l-1 2c-2 2-4 4-6 5h-1c-3 0-8 3-11 2l-4-1h0c-4-1-11-3-12-6h0l-1-1c0-1-2-1-3-2l-2 2-1-1c-1-3-2-7-2-11 1-5 2-11 5-15s10-7 14-9h1l1-1h5c5 2 9 3 13 7 0-1 1-1 1-2 2-1 3-3 4-5l1 1 1-4h2l1 1 9 9h2 1l1-1v-1-2l1-1-2-2c-1-1-1-2-1-4v1l2 1 2-2 1 1h2 1 0l1-2 2-4z" class="e"></path><path d="M529 490h2l1 1c-1 1-1 2-1 3-1 2 0 3-1 5l-1-1v-3c1-1 1-1 1-2v-1l-1-2z" class="j"></path><path d="M527 493l1 1c-3 4-8 10-12 11l-1-2h1l1-2c-1 0-1-1-1-2h0l1-1h-1l1-1 1 1-1 2 1 1c2 0 2-1 4-1 0-1 1-1 1-2 2-1 3-3 4-5z" class="F"></path><path d="M513 505l2-2 1 2v3l-1 1h-2-2c-2 1-4 3-6 5v-2c1-2 3-3 3-5l3-1h2v-1z" class="r"></path><path d="M532 491l9 9h2 1l-1 1h-2l1 1h0l1 1v1h-1c-1-1 0-1-1-1l-1 1v1l-1 1-5 2-2 1h0l-3 2-3-1-3-6c2 0 5-2 7-3l1-1-1-1c1-2 0-3 1-5 0-1 0-2 1-3z" class="Z"></path><path d="M523 504c2 0 5-2 7-3l-1 2h0 1c1 1 1 2 1 3 1 1 1 1 2 1l1-1c2-1 3-1 5-1l1-1v1l-1 1-5 2-2 1h0l-3 2-3-1-3-6z" class="h"></path><path d="M529 503h0v2c1 1 0 3 0 4h0l-2-2c-1-1-1-1-1-2s2-2 3-2z" class="U"></path><path d="M519 508c1-2 2-3 4-4l3 6 3 1 3-2v14l-1 3-1-1c-1 2-2 3-3 4l-1 2h-1v-2h-2l1-1v-1c0-1 1-2 1-3-1-1-1-3-1-4v-1l-2 2c0-2 1-4 0-5 0-2 0-2-1-3l-1 4c0-3 0-4-2-6 1-1 0-2 1-3z" class="F"></path><path d="M527 513c1 1 2 3 2 4 1 2 1 4 1 6v2c-1 2-2 3-3 4 0-2 1-5 1-7l-1-9z" class="D"></path><path d="M526 510l3 1 3-2v14l-1 3-1-1v-2c0-2 0-4-1-6 0-1-1-3-2-4l-2-2 1-1z" class="B"></path><path d="M519 508c1-2 2-3 4-4l3 6-1 1-2-2h-1c0 1 1 1 2 2 2 2 2 5 2 7v3c0 1-1 2-1 3-1-1-1-3-1-4v-1l-2 2c0-2 1-4 0-5 0-2 0-2-1-3l-1 4c0-3 0-4-2-6 1-1 0-2 1-3z" class="G"></path><path d="M519 508h0c2 2 5 3 5 7v4l-2 2c0-2 1-4 0-5 0-2 0-2-1-3l-1 4c0-3 0-4-2-6 1-1 0-2 1-3z" class="h"></path><path d="M511 512l1-1h0c1-1 2-1 3-1 2 0 2 1 3 1 2 2 2 3 2 6l1-4c1 1 1 1 1 3 1 1 0 3 0 5-1 1-2 2-2 3l-1 1-1-1h-1c-1 2-2 3-4 3h-1-4 0l-4-2h-1l-1-2c0-1 0-1 1-1h2l1-3v-1c0-3 2-5 5-6z" class="U"></path><path d="M511 512l1-1h0c1-1 2-1 3-1 2 0 2 1 3 1 2 2 2 3 2 6 0 1 0 2-1 3l-1 1h-1l1-2-1-1h0c0-1 0-2-1-3-1-2-3-3-5-3z" class="J"></path><path d="M511 512c2 0 4 1 5 3 1 1 1 2 1 3h0c-1 2-2 4-3 5-2 1-4 1-6 0-1-1-2-2-2-4v-1c0-3 2-5 5-6z" class="I"></path><path d="M524 519v1c0 1 0 3 1 4 0 1-1 2-1 3v1l-1 1h2v2h1c-2 2-4 4-6 5h-1c-3 0-8 3-11 2l-4-1h0c-4-1-11-3-12-6h0c2-1 5-4 6-5 2-1 3-2 4-3l1 2h1l4 2h0 4 1c2 0 3-1 4-3h1l1 1 1-1c0-1 1-2 2-3l2-2z" class="B"></path><path d="M504 536h-2c-2-1-3-2-4-3l-1-2h-1v-1l1-1v2c2 0 4 1 6 3l1 1v1z" class="G"></path><path d="M524 527v1l-1 1h2v2h1c-2 2-4 4-6 5h-1c-3 0-8 3-11 2v-1h0l-4-1v-1l4 1h4c5-1 9-5 12-9z" class="V"></path><path d="M524 519v1c-2 5-5 9-10 11-3 1-5 1-8 0h0-1c-1-1-3-2-4-3v-3h2 1l4 2h0 4 1c2 0 3-1 4-3h1l1 1 1-1c0-1 1-2 2-3l2-2z" class="H"></path><defs><linearGradient id="b" x1="554.499" y1="499.249" x2="541.499" y2="499.257" xlink:href="#B"><stop offset="0" stop-color="#aba191"></stop><stop offset="1" stop-color="#cbc2ae"></stop></linearGradient></defs><path fill="url(#b)" d="M554 484c1-1 2-1 4-1v1l2 1c0 1 0 3-1 4-1 3 0 7 0 10v10 11h-2-4-3l-2 1v2h-16v-14h0l2-1 5-2 1-1v-1l1-1c1 0 0 0 1 1h1v-1l-1-1h0l-1-1h2l1-1 1-1v-1-2l1-1-2-2c-1-1-1-2-1-4v1l2 1 2-2 1 1h2 1 0l1-2 2-4z"></path><path d="M560 485c0 1 0 3-1 4-1 3 0 7 0 10v10 11h-2v-14-11-5h1c0-2 0-2-1-4h0l1-2 2 1z" class="R"></path><path d="M554 484c1-1 2-1 4-1v1l-1 2h0c-3 5-7 10-12 13v-1-2l1-1-2-2c-1-1-1-2-1-4v1l2 1 2-2 1 1h2 1 0l1-2 2-4z" class="C"></path><path d="M543 490l2 1 2-2 1 1c1 0 2 1 2 2s-1 2-2 3h-2l-2-2c-1-1-1-2-1-4v1z" class="P"></path><path d="M541 505h10c2 1 4 1 6 1v14h-4-3c1 0 1 0 1-1s0-2-1-2l-1-1 2-9v-1h-9l-1-1z" class="W"></path><path d="M540 505h1l1 1h9v1l-2 9 1 1c1 0 1 1 1 2s0 1-1 1l-2 1v2h-16v-14h0l2-1 5-2 1-1z" class="X"></path><path d="M534 508v1h2v10h0v-2l-2-2c-1-2 1-1-2-3v-3l2-1z" class="Z"></path><path d="M532 509h0v3c3 2 1 1 2 3l2 2v2l-2 2c3 1 5 1 7 1 2-1 5-1 7-1v2h-16v-14z" class="J"></path><path d="M504 493h5c5 2 9 3 13 7-2 0-2 1-4 1l-1-1 1-2-1-1-1 1h1l-1 1h0c0 1 0 2 1 2l-1 2h-1l-2 2v1h-2l-3 1c0 2-2 3-3 5v2c0 1-1 2-2 2-1-1-1-2-2-3h0v4l-1 1h0l1 2h0l2 2c-1 0-1 0-1 1-1 1-2 2-4 3-1 1-4 4-6 5l-1-1c0-1-2-1-3-2l-2 2-1-1c-1-3-2-7-2-11 1-5 2-11 5-15s10-7 14-9h1l1-1z" class="V"></path><path d="M491 513l-2-1v-1c3-5 8-10 13-12 1 0 4 0 5 1-7 1-10 4-15 9v3l-1 1z" class="C"></path><path d="M488 503l2 1c0-1 1-1 3-2-3 3-4 5-6 8s-3 6-2 9l1 1h0v5s0 1 1 1c1 1 1 1 2 1l-1 1-2 2-1-1c-1-3-2-7-2-11 1-5 2-11 5-15z" class="a"></path><path d="M507 500c2 1 4 3 6 3l1-1 1 1-2 2c-1 0-1-1-3-2s-5 1-8 2c0 0 0 1-1 1l-3 3h0l-2 2v-1c0-1 1-2 2-3h-1l-4 3c-1 1-1 1-1 2v-3c5-5 8-8 15-9z" class="F"></path><path d="M492 512c0-1 0-1 1-2l4-3h1c-1 1-2 2-2 3v1l2-2 1 1c-1 1-2 2-2 4h1 1c0-2 0-2 1-3h1v1 1 4l-1 1h-1-7l-2-1h-3v-3c2-2 2-1 4-1l1-1z" class="J"></path><path d="M498 509l1 1c-1 1-2 2-2 4h1 1c0-2 0-2 1-3h1v1c-1 1-2 3-3 4-3 0-6 0-8-1l-1-1c3 0 4 0 5-2l2-1 2-2z" class="Z"></path><path d="M498 509h0l3-3c1 0 1-1 1-1 3-1 6-3 8-2s2 2 3 2v1h-2l-3 1c0 2-2 3-3 5v2c0 1-1 2-2 2-1-1-1-2-2-3h0v-1-1h-1c-1 1-1 1-1 3h-1-1c0-2 1-3 2-4l-1-1z" class="U"></path><path d="M508 507c0 2-2 3-3 5v2c0 1-1 2-2 2-1-1-1-2-2-3 2-3 4-5 7-6z" class="b"></path><path d="M487 526c1-3-1-5 0-8l3-1 2 1h7 1 0l1 2h0l2 2c-1 0-1 0-1 1-1 1-2 2-4 3-1 1-4 4-6 5l-1-1c0-1-2-1-3-2l1-1c-1 0-1 0-2-1z" class="S"></path><path d="M489 520h1c1 1 1 1 1 2s0 1-1 2v1l-1 1v-1c-1-2-1-3 0-5z" class="O"></path><path d="M492 518h7 1 0c-1 2-2 3-3 5h-1c-1-1-1-2-2-2h-1l-2-1 1-1v-1z" class="N"></path><path d="M500 518l1 2h0l2 2c-1 0-1 0-1 1-1 1-2 2-4 3-1 1-4 4-6 5l-1-1c0-1-2-1-3-2l1-1 7-4h1c1-2 2-3 3-5z" class="U"></path><path d="M500 518l1 2h0l2 2c-1 0-1 0-1 1-1 1-2 2-4 3h-1l2-1c-1-2-1-2-2-2 1-2 2-3 3-5z" class="o"></path><path d="M504 493h5c5 2 9 3 13 7-2 0-2 1-4 1l-1-1 1-2-1-1-1 1h1l-1 1h0c0 1 0 2 1 2l-1 2h-1l-1-1-1 1c-2 0-4-2-6-3-1-1-4-1-5-1 1-1 3-1 4-2-5 1-9 2-13 5-2 1-3 1-3 2l-2-1c3-4 10-7 14-9h1l1-1z" class="B"></path><path d="M506 497c2-1 5 0 7 1l3 1h0c0 1 0 2 1 2l-1 2h-1l-1-1-1 1c-2 0-4-2-6-3-1-1-4-1-5-1 1-1 3-1 4-2z" class="D"></path><path d="M470 520v-1c2-2 7-2 10-2l3 1c0 4 1 8 2 11l1 1 2-2c1 1 3 1 3 2l1 1h0c1 3 8 5 12 6h0l4 1c3 1 8-2 11-2h1c2-1 4-3 6-5l1-2c1-1 2-2 3-4l1 1 1-3h16v-2l2-1h3 4 2 0v2 6h0v6h0l-2-3-2-1-2 4v3h1l-1 11h1v14 4c0 1-1 2-1 3 0 3 0 9-1 11l-2 1h0 0v11c-1-1-1-3-1-4v-1h-2l-1-1c-1-1-1-2-2-3h-6l1-1c0-1 0-2 1-2l-1-2-2-3v-1c-1-1-3-4-4-5l1-1c0 1 1 1 2 2l-1-2-1-1c-1-2-2-3-4-4l-3-8v-1h-1l-2 1h-1c-4 2-7 3-12 4-3 0-8 0-11-1h-1c-3 0-6-2-9-3h-1 0c-1-1-2-1-4-1v-2h-1c0-1 0-2 1-3-8-7-12-14-15-24v-5z" class="b"></path><path d="M478 535l2-1c0-1 0-1 1-1s2-1 3-1v1h0v3c-1 1-2 1-3 1h-2c-1 0-1 0-2 1v-1c0-1 0-1 1-2z" class="X"></path><path d="M470 520c1 1 2 1 2 3v1 2l2 5 2 4h2c-1 1-1 1-1 2v1c1 1 1 1 2 1 5 1 6 5 10 7 2 1 7 2 9 2s3 0 5 1v7c-8-1-12-4-18-7-8-7-12-14-15-24v-5z" class="Z"></path><path d="M530 525l1 1-2 6c-3 6-11 10-17 12-2 0-4 1-7 0h-1 0-2c-4-1-8-3-11-6-1 0-4-5-5-7h1s1 0 2 1h1l2-1c1 3 8 5 12 6h0l4 1c3 1 8-2 11-2h1c2-1 4-3 6-5l1-2c1-1 2-2 3-4z" class="a"></path><path d="M508 538c3 1 8-2 11-2-5 3-9 5-14 4-1-1-1-1-1-3l4 1z" class="c"></path><path d="M491 538c-1 0-4-5-5-7h1s1 0 2 1h1l2-1c1 3 8 5 12 6h0c0 2 0 2 1 3h-3-1c-1 0-4 0-6-1-1 0-2-1-4-1z" class="P"></path><path d="M490 532l2-1c1 3 8 5 12 6h0c0 2 0 2 1 3h-3c-5-2-8-5-12-8z" class="L"></path><path d="M503 549c0-2 0-3 1-4v2c1 2 1 4 1 6 1 1 3 0 4 1h2c1 0 2 1 4 2 4-1 9-3 13-5l3-2 2 1 3 6 5 5 3 3c1 1 2 1 2 2v3c-1 1-1 1-1 3v1h-4c-2 0-3-2-4-2l-1-1-1-2-1-1c-1-2-2-3-4-4l-3-8v-1h-1l-2 1h-1c-4 2-7 3-12 4-3 0-8 0-11-1h-1c-3 0-6-2-9-3h-1 0c-1-1-2-1-4-1v-2h-1c0-1 0-2 1-3 6 3 10 6 18 7v-7z" class="Y"></path><path d="M531 549l2 1 3 6h0c-3 0-6-3-8-5l3-2z" class="O"></path><path d="M515 556c-4 1-8 0-11 0v-9c1 2 1 4 1 6 1 1 3 0 4 1h2c1 0 2 1 4 2z" class="Z"></path><path d="M545 572c-1-1-2-1-2-2-4-5-9-8-12-13l-1-4 2 2 7 6h2l3 3c1 1 2 1 2 2v3c-1 1-1 1-1 3z" class="N"></path><path d="M557 520h2 0v2 6h0v6h0l-2-3-2-1-2 4v3h1l-1 11h1v14 4c0 1-1 2-1 3 0 3 0 9-1 11l-2 1h0 0v11c-1-1-1-3-1-4v-1h-2l-1-1c-1-1-1-2-2-3h-6l1-1c0-1 0-2 1-2l-1-2-2-3v-1c-1-1-3-4-4-5l1-1c0 1 1 1 2 2l1 1c1 0 2 2 4 2h4v-1c0-2 0-2 1-3v-3c0-1-1-1-2-2l-3-3-5-5-3-6-2-1c4-3 7-6 10-9l-2-1-1-1-3-2-3-2c-1-1-2-1-3-2l2-6 1-3h16v-2l2-1h3 4z" class="I"></path><path d="M548 557c1 3 1 7 1 10-1 1-2 2-3 2v-3c1-2 1-7 2-9z" class="Y"></path><path d="M541 542h2c1 2 4 2 6 4v3l-1 1-2-2-1-1-1-1c-1 0-2-2-3-4z" class="T"></path><path d="M546 569c1 0 2-1 3-2v6 4l-1 4 1 1c-1 1-3 1-5 1h0-6l1-1c0-1 0-2 1-2l-1-2-2-3v-1c-1-1-3-4-4-5l1-1c0 1 1 1 2 2l1 1c1 0 2 2 4 2h4v-1c0-2 0-2 1-3z" class="E"></path><path d="M546 569c1 0 2-1 3-2v6 4h0v-1l-1-1c-1 0-2 0-4-1-1 0-5 0-6-1 0-1-1-2-1-2 1 0 2 2 4 2h4v-1c0-2 0-2 1-3z" class="P"></path><defs><linearGradient id="c" x1="536.203" y1="548.208" x2="548.686" y2="560.205" xlink:href="#B"><stop offset="0" stop-color="#313130"></stop><stop offset="1" stop-color="#4a4949"></stop></linearGradient></defs><path fill="url(#c)" d="M533 550c1 0 4 1 5-1h0c0-2 0-3 1-4s1-1 2 0c2 1 5 4 6 6s2 3 1 6c-1 2-1 7-2 9 0-1-1-1-2-2l-3-3-5-5-3-6z"></path><path d="M532 523h16l-3 7-3 8-1 2-2-1-1-1-3-2-3-2c-1-1-2-1-3-2l2-6 1-3z" class="X"></path><path d="M542 538v-2-3c1-2 2-2 3-3l-3 8z" class="Z"></path><defs><linearGradient id="d" x1="544.265" y1="545.03" x2="559.273" y2="559.488" xlink:href="#B"><stop offset="0" stop-color="#a19082"></stop><stop offset="1" stop-color="#c3bda7"></stop></linearGradient></defs><path fill="url(#d)" d="M557 520h2 0v2 6h0v6h0l-2-3-2-1-2 4v3h1l-1 11h1v14 4c0 1-1 2-1 3 0 3 0 9-1 11l-2 1h0v-42l1-10v-5c0-2 1-2 2-4h4z"></path><path d="M551 529h1c1-1 2-1 2-2 2 0 3 0 5 1v6h0l-2-3-1-1h-2c-2 3-2 5-4 9l1-10z" class="R"></path><path d="M557 520h2 0v2 6h0c-2-1-3-1-5-1 0 1-1 1-2 2h-1v-5c0-2 1-2 2-4h4z" class="L"></path><path d="M557 520h2 0v2c-1 0-2 1-2 1l-6 1c0-2 1-2 2-4h4z" class="T"></path><defs><linearGradient id="e" x1="458.454" y1="545.637" x2="415.33" y2="556.652" xlink:href="#B"><stop offset="0" stop-color="#070605"></stop><stop offset="1" stop-color="#302f30"></stop></linearGradient></defs><path fill="url(#e)" d="M411 514c3 1 8 1 10 1 2-1 3-1 4-1 2 1 4 1 7 1 5 0 9 1 14 0h5 0v4l1 1v2l1 64-1 11s0-7-1-8c-2 0-2 1-2 3h0c0 1-1 1-1 1l-9 7c-1-4-2-7-3-10h1l1 1c0-2 0-3 1-4l-1-1-1-2 1-1c-1-1-1-3 0-4-2-3-2-6-2-8-1-2 0-3 1-4l-6-1c-4 1-8-2-11 0l-7-4-9-3c2-2 4-3 5-5 1 1 2 0 3 0h6c-1-1-2-1-3-2 1-1 1-1 2-1-2-2-2-2-3-4-1 2-3 4-5 6h-2c-1 1-2 1-3 0h-3l-1-2h-3c-1-1-1-1-2-1l-1-1h-1v-1l-1-1h-1-1v-1h-1 0l-2-1v1l-5-5c1-1 3-1 5-1-1 0-3-1-4 0-2 0-3 0-4-1h0 5 2 1 1-1-1c-2-1-2-1-4 0l1-2h3l1-1h3c0-1 0 0-1-1v-1h-5c1-1 0-1 1-1s2-1 3-2h4l1-1c2 0 3-1 4-2 2-1 2-1 5-1 0 1 1 0 2 0l1 1v2h1c1-1 1-1 2-3v-2l2-1 2-1h6v-1c1 0 2 0 2-1h1v-1c-2 0-8 1-10 0l1-1h1l-1-1h-15c2-1 5 0 7-1l2-2 5-1h0z"></path><path d="M436 528h5l1-1h0-1c1 0 4-1 5 0l1 1c1 2 2 2 2 4 0 1-1 2-1 3 0-2 0-3-1-5-3-2-7-1-10-1l-9 1-1-1c3-1 6-1 9-1z" class="O"></path><path d="M418 523h15l1 1c-3 1-20 1-24 0l2-1h6z" class="g"></path><path d="M411 514c3 1 8 1 10 1h4c0 1 1 1 1 2 6 1 13 0 19 1-4 1-8 1-11 1-2 0-4 0-6 1h7c2 0 4 1 6 1-5 1-10 1-15 1h-8c1 0 2 0 2-1h1v-1c-2 0-8 1-10 0l1-1h1l-1-1h-15c2-1 5 0 7-1l2-2 5-1h0z" class="G"></path><path d="M411 514c3 1 8 1 10 1h4c0 1 1 1 1 2-3 2-6 2-9 1h-1c-1 0-2-1-2-2l-1 1h-9l2-2 5-1h0z" class="O"></path><path d="M408 527h4c2 1 3 0 5 1 0 1 0 1-1 2h1c1 0 1 0 2-1s2-1 3-1l1-1h4 10l-1 1c-3 0-6 0-9 1l1 1h-3c-3 1-5 2-6 5l-2 3h1c-1 3-2 6-4 9-1 2-3 4-5 6h-2l1-1c-2-4 1-3 3-6v-3h3c0-1 1-2 1-3h-1c1-1 2-2 2-3v-1l-2 2c-3 0-9 0-11-1 3 0 7 0 11-1 1 0 1 0 2-1l-2-1c0-1 0-2 1-2 0-1 0-1-1-2-3 0-6 1-9 2-1-1-1 0-1-1l1-1h1c1-1 1-1 2-3z" class="F"></path><path d="M411 543h3c-2 3-2 6-6 9-2-4 1-3 3-6v-3z" class="J"></path><defs><linearGradient id="f" x1="412.103" y1="536.123" x2="386.408" y2="531.318" xlink:href="#B"><stop offset="0" stop-color="#aca491"></stop><stop offset="1" stop-color="#c8c0b5"></stop></linearGradient></defs><path fill="url(#f)" d="M397 528c2-1 2-1 5-1 0 1 1 0 2 0l1 1v2l-1 1c0 1 0 0 1 1 3-1 6-2 9-2 1 1 1 1 1 2-1 0-1 1-1 2l2 1c-1 1-1 1-2 1-4 1-8 1-11 1-2-1-4 0-5-1h-8c0-1 0 0-1-1v-1h-5c1-1 0-1 1-1s2-1 3-2h4l1-1c2 0 3-1 4-2z"></path><path d="M397 528c2-1 2-1 5-1 0 1 1 0 2 0l1 1v2l-1 1c0 1 0 0 1 1h-4c-3-1-6 0-9-1l1-1c2 0 3-1 4-2z" class="g"></path><defs><linearGradient id="g" x1="401.585" y1="531.882" x2="391.339" y2="547.232" xlink:href="#B"><stop offset="0" stop-color="#b0a38d"></stop><stop offset="1" stop-color="#cdc7b9"></stop></linearGradient></defs><path fill="url(#g)" d="M390 536h8c1 1 3 0 5 1s8 1 11 1l2-2v1c0 1-1 2-2 3h1c0 1-1 2-1 3h-3v3c-2 3-5 2-3 6l-1 1c-1 1-2 1-3 0h-3l-1-2h-3c-1-1-1-1-2-1l-1-1h-1v-1l-1-1h-1-1v-1h-1 0l-2-1v1l-5-5c1-1 3-1 5-1-1 0-3-1-4 0-2 0-3 0-4-1h0 5 2 1 1-1-1c-2-1-2-1-4 0l1-2h3l1-1h3z"></path><path d="M401 548v-2c1-2 2-4 4-4h2c2 0 1 0 2 1h2v3c-2 3-5 2-3 6l-1 1c-1 1-2 1-3 0h-3l-1-2h-3c-1-1-1-1-2-1l-1-1h-1v-1l2-3c1 0 2-1 4-1 1 1 1 1 1 2h0v1 2h1v-1z" class="h"></path><path d="M400 551h5l-1 2h-3l-1-2z" class="Z"></path><path d="M401 548v-2c1-2 2-4 4-4h2c2 0 1 0 2 1v1c-2 0-3-1-4-1 0 1 0 1 1 1 0 1 1 1 1 2s-2 2-3 3c-1 0-2-1-3-1z" class="H"></path><path d="M437 529c3 0 7-1 10 1 1 2 1 3 1 5h0c-1 3 0 6-1 8v1 3 1c-1 3-3 6-3 9h1v1 1l-1 1-2 2c0-1-1-2-1-3l-1-1h0l1-1v-8c1-3 1-4 0-6h0c1-2 1-4 0-6h-3c1 1 1 2 1 4h0l1 1c-2-1-3-1-5 0-1 0-2 1-3 2v-2-2c-2 0-3 0-4-1-1 0-1 0-1-1 1 0 1 0 2-1-4 0-7 0-11 1h-1l2-3c1-3 3-4 6-5h3l9-1z" class="I"></path><path d="M437 529l2 1c0 2 1 3 1 5l-10 1h0l8 1c1 1 1 2 1 4h0l1 1c-2-1-3-1-5 0-1 0-2 1-3 2v-2-2c-2 0-3 0-4-1-1 0-1 0-1-1 1 0 1 0 2-1-4 0-7 0-11 1h-1l2-3c1-3 3-4 6-5h3l9-1z" class="J"></path><path d="M438 537h3c1 2 1 4 0 6h0c1 2 1 3 0 6v8l-1 1h0c0 2 0 3 1 5v1h2l-2 2h0l2 1v10 1h0c0 1 0 3-1 4v2c1 1 1 2 1 4l-1 1c0 1 1 1 1 1l3-1c1-1 2-1 3-2l1 1c-1 1-1 1-2 1 0 1 0 1 1 3h0c0 1-1 1-1 1l-9 7c-1-4-2-7-3-10h1l1 1c0-2 0-3 1-4l-1-1-1-2 1-1c-1-1-1-3 0-4-2-3-2-6-2-8-1-2 0-3 1-4l-6-1c-4 1-8-2-11 0l-7-4-9-3c2-2 4-3 5-5 1 1 2 0 3 0h6c-1-1-2-1-3-2 1-1 1-1 2-1-2-2-2-2-3-4 2-3 3-6 4-9 4-1 7-1 11-1-1 1-1 1-2 1 0 1 0 1 1 1 1 1 2 1 4 1v2 2c1-1 2-2 3-2 2-1 3-1 5 0l-1-1h0c0-2 0-3-1-4z" class="l"></path><path d="M437 567h2l-1 1c-1 2 0 4 0 6v1c0 2 1 3 1 4 1 2 0 5 0 7h-1l-1-2 1-1c-1-1-1-3 0-4-2-3-2-6-2-8-1-2 0-3 1-4z" class="J"></path><path d="M446 589c1-1 2-1 3-2l1 1c-1 1-1 1-2 1 0 1 0 1 1 3h0c0 1-1 1-1 1h-1c-2 2-5 4-8 5l1-1c1-3 1-4 3-5l3-3z" class="I"></path><path d="M432 544c1-1 2-2 3-2 2-1 3-1 5 0 1 3 1 4 0 7-1 1-2 2-4 2h-1c-2-1-3-2-4-4l1-3z" class="O"></path><defs><linearGradient id="h" x1="429.541" y1="555.666" x2="413.73" y2="565.983" xlink:href="#B"><stop offset="0" stop-color="#b0a693"></stop><stop offset="1" stop-color="#cec6b8"></stop></linearGradient></defs><path fill="url(#h)" d="M412 554h6v1h0c4 1 8 3 13 3l2 1c1 1 1 1 1 3l3 3-1 1h-2-3c-4 1-8-2-11 0l-7-4-9-3c2-2 4-3 5-5 1 1 2 0 3 0z"></path><defs><linearGradient id="i" x1="426.508" y1="563.996" x2="409.992" y2="553.004" xlink:href="#B"><stop offset="0" stop-color="#c1b6a5"></stop><stop offset="1" stop-color="#dfd7c7"></stop></linearGradient></defs><path fill="url(#i)" d="M412 554h6v1h0c4 1 8 3 13 3-1 2-1 3-2 3-4 0-8 0-11-1-3-2-8-2-12-2 3-1 4-1 6-4z"></path><path d="M418 538c4-1 7-1 11-1-1 1-1 1-2 1 0 1 0 1 1 1 1 1 2 1 4 1v2 2l-1 3c1 2 2 3 4 4v1l-1 1s1 1 0 2c0 1-1 1-2 2-2 0-6 0-8-1h-1c-1-1-3-1-5-1h0v-1c-1-1-2-1-3-2 1-1 1-1 2-1-2-2-2-2-3-4 2-3 3-6 4-9z" class="X"></path><path d="M432 540v2 2l-1 3v-1c-1-1-1-2-2-3l3-3h0z" class="U"></path><path d="M559 499c2 0 6 1 8 0l10 1h13c3-1 6-1 9 0h12c-1 1-2 2-2 3v2h5c1 0 1 1 3 0h1c0-1 1-2 2-2 0 1 0 1-1 2h3s1 1 1 0c1 0 2-1 3-1 1 1 1 1 2 1v1l-1 2h4 1 4c1 0 2-1 3 0h7c-1 2 0 3 0 5-1 2-1 3-4 4h-4l2 1-1 2h-1c3 0 7-1 9 0h7 17v2l-1 1s-1 0-2 1h2v3h-6c1 0 4 1 5 1 1 2 0 8 0 10v1c0 2 1 4 0 6h-1l1 2h1v13h1v21h1c0 4-1 8 0 12l-6-6a104.13 104.13 0 0 1-5-12c0-5-3-5-4-9-4-6-9-12-14-18h3 0c-1-2-2-3-3-4l-5-5c-3-3-7-6-9-8l-1-2c-1-1-3-4-4-5-1 0-43 1-48 0h-1-10c-2-1-4 0-6-2v-2h0v-11-10z" class="R"></path><path d="M655 547c2 2 4 4 3 7 0 1 0 1-1 2h-1c-1-2-2-3-2-5l-1-1v-1l2-2zm12 30l-3-9c-1-1-1-1-1-3 1 1 1 2 2 3 0 0 2 0 3 1 1 0 2 1 2 2v1c-1 2 0 4-1 5h-2z" class="n"></path><path d="M670 560h1v21 6c-3-3-3-7-4-10h2c1-1 0-3 1-5v-1c0-1-1-2-2-2l2-2c1-2 0-5 0-7z" class="m"></path><path d="M669 547h1v13c0 2 1 5 0 7-1-1-2-2-3-2h-2v-4c0-2 3-4 3-6v-1c-2 2-3 3-6 3 2-1 2-1 2-4h1l4-4v-1l-2 1 2-2z" class="O"></path><path d="M665 553l1 1c1-1 2-2 3-2h0v2h-1c-2 2-3 3-6 3 2-1 2-1 2-4h1z" class="n"></path><path d="M640 533v-1c0-1 0-1-1-2-1-2-1-3-2-5 2 2 6 5 8 6s5 4 7 5c0 3 1 4 3 6l1 2h0-1l-2-2v1c-2-1-3-2-4-4-1-1-3-2-5-3l11 11-2 2-1-1v1l-1-1v-1l-1-1c-1-2-3-3-3-5-2-3-4-5-7-7v-1z" class="F"></path><path d="M630 524h15 3 3v3h-2l-1 1-1 1-1-1c0 1 0 1 1 2s4 4 5 6c-2-1-5-4-7-5s-6-4-8-6c1 2 1 3 2 5 1 1 1 1 1 2v1 1l-3-1-3-2c-1 0 0 0-1-1l-5-4v-1l2-1z" class="O"></path><path d="M648 524h3v3h-2l-1 1v-4z" class="I"></path><path d="M633 526c2 2 5 4 7 7v1l-3-1-3-2c-1-1-2-3-3-4l2-1z" class="V"></path><path d="M630 524h15c-4 1-8 0-12 1v1l-2 1c1 1 2 3 3 4-1 0 0 0-1-1l-5-4v-1l2-1z" class="Y"></path><path d="M575 524h55l-2 1v1l5 4c1 1 0 1 1 1l3 2c4 4 7 8 11 13 1 2 3 4 4 6 0 1 1 3 2 4v2-1c-2-2-4-5-6-7h-1v1c4 4 6 10 10 13 0-1 0-2-1-3l3-3v1 1c1 1 1 2 1 4s2 3 2 5v1c-1 2 2 3 1 6 0 2 2 5 3 8v1 2a104.13 104.13 0 0 1-5-12c0-5-3-5-4-9-4-6-9-12-14-18h3 0c-1-2-2-3-3-4l-5-5c-3-3-7-6-9-8l-1-2c-1-1-3-4-4-5-1 0-43 1-48 0h-1z" class="C"></path><path d="M651 527h2 9 2c1 0 4 1 5 1 1 2 0 8 0 10v1c0 2 1 4 0 6h-1l1 2-2 2 2-1v1l-4 4h-1c0 3 0 3-2 4l-1 1h0-1v-2-6c0-1-2-2-2-2l-5-5v-1l2 2h1 0l-1-2c-2-2-3-3-3-6-1-2-4-5-5-6s-1-1-1-2l1 1 1-1 1-1h2z" class="G"></path><path d="M669 539c0 2 1 4 0 6h-1c-1 1-2 3-3 3-1-1-2-2-2-4 2-1 3-2 4-3l2-2zm-9-4c1 0 2-1 3 0l2 2-1 1c0 1 1 2 2 2h0c-1 2-2 2-4 2-1-1-2-1-2-3-1-1-2-3-1-4h1z" class="O"></path><path d="M660 535c1 0 2-1 3 0s0 1 0 2h-1c-1 0-2-1-2-2z" class="S"></path><path d="M660 532c2-1 3-1 5 0s3 2 3 4c0 1-1 3-2 4-1 0-2-1-2-2l1-1-2-2c-1-1-2 0-3 0h-1c0-2 1-2 1-3z" class="n"></path><path d="M651 527h2 9 2c1 0 4 1 5 1 1 2 0 8 0 10v1l-2 2-1-1h0c1-1 2-3 2-4 0-2-1-3-3-4s-3-1-5 0c0-3-6-2-8-3-1 0-1-1-1-1-2 0-2 1-4 2-1-1-1-1-1-2l1 1 1-1 1-1h2z" class="D"></path><path d="M638 520c3 0 7-1 9 0h7 17v2l-1 1s-1 0-2 1h2v3h-6-2-9-2v-3h-3-3-15-55-10c-2-1-4 0-6-2v-2h1 4c4 0 9 1 13 0l15 1c7-1 15 0 22-1 2 1 5 0 7 0h17z" class="K"></path><path d="M654 520h17v2l-1 1c-7-2-15-1-23-1-1 0-1-1-2-1h-5 2 3 0c3-1 6-1 9-1z" class="Y"></path><path d="M651 524h17 2v3h-6-2-9-2v-3z" class="O"></path><defs><linearGradient id="j" x1="560.168" y1="523.553" x2="602.672" y2="518.964" xlink:href="#B"><stop offset="0" stop-color="#181717"></stop><stop offset="1" stop-color="#424141"></stop></linearGradient></defs><path fill="url(#j)" d="M560 520h4c4 0 9 1 13 0l15 1c2 0 4 1 7 0 1 0 2 1 3 1h1c1 0 0 0 1 1h-25c-5 0-9 0-14 1-2-1-4 0-6-2v-2h1z"></path><path d="M638 520c3 0 7-1 9 0h7c-3 0-6 0-9 1h0-3-2 5c1 0 1 1 2 1-6 1-12 0-19 1h-24c-1-1 0-1-1-1h-1c-1 0-2-1-3-1-3 1-5 0-7 0 7-1 15 0 22-1 2 1 5 0 7 0h17z" class="D"></path><path d="M559 499c2 0 6 1 8 0l10 1h13c3-1 6-1 9 0h12c-1 1-2 2-2 3v2h5c1 0 1 1 3 0h1c0-1 1-2 2-2 0 1 0 1-1 2h3s1 1 1 0c1 0 2-1 3-1 1 1 1 1 2 1v1l-1 2h4 1 4c1 0 2-1 3 0h7c-1 2 0 3 0 5-1 2-1 3-4 4h-4l2 1-1 2h-1-17c-2 0-5 1-7 0-7 1-15 0-22 1l-15-1c-4 1-9 0-13 0h-4-1 0v-11-10z" class="G"></path><path d="M623 505c1 0 2-1 3-1 1 1 1 1 2 1v1l-1 2c-2 0-5 0-8 1h-1l-1 1v-1l-1-1 3-3h3s1 1 1 0z" class="X"></path><path d="M633 512c4 1 7 0 11 0l1 1-2 2h-2c-1 0-1 1-2 1h-2l-1-1c-1 0-2 0-3 1h-2v-1c0-1 1-2 2-3z" class="O"></path><path d="M616 512h0c1 0 2 0 3-1 2 1 2 1 5 1v-1c3-2 15-2 19-1l1 1v1c-4 0-7 1-11 0-2 1-4 1-7 1v1h-5-1c-1 0-3-1-4 0-1 0-1 1-2 1v1l-1-1h0c1-1 1-1 1-2 1 0 1 0 2-1z" class="a"></path><path d="M618 505c0-1 1-2 2-2 0 1 0 1-1 2l-3 3-1 1c0-1 0 0-1-1-2 0-5-1-6 1h-1c-2-3-7 0-10-1v-1c-3 1-5 0-8 0 2-1 5 0 7-2h8 1 2 2 5c1 0 1 1 3 0h1z" class="h"></path><path d="M621 514h5v-1c3 0 5 0 7-1-1 1-2 2-2 3v1h2c1-1 2-1 3-1l1 1v1h1l2 1-1 2h-1-17c-2 0-5 1-7 0s-2-1-4-1l2-1c2 0 2-1 2-2v-1c1 0 1-1 2-1 1-1 3 0 4 0h1z" class="Q"></path><path d="M637 517h1l2 1-1 2c-1-1-2-1-2-1v-2z" class="c"></path><path d="M620 514h1v3h-1c-1 1-2 1-3 1-2-1 0 0 0-1s-1-1-1-2c1 0 3-1 4-1z" class="B"></path><path d="M621 514h5v-1c3 0 5 0 7-1-1 1-2 2-2 3v1l-1 1-3-2c-2 1-2 4-5 3l-1-1v-3z" class="I"></path><defs><linearGradient id="k" x1="561.176" y1="497.342" x2="582.839" y2="508.84" xlink:href="#B"><stop offset="0" stop-color="#1e1d1d"></stop><stop offset="1" stop-color="#43413e"></stop></linearGradient></defs><path fill="url(#k)" d="M559 499c2 0 6 1 8 0l10 1h13c3-1 6-1 9 0h12c-1 1-2 2-2 3v2h-2-2-1-8c-2 2-5 1-7 2v1h-1v-1c-2 0-6 1-8 0-1 0-1 0-1 1l-2-2-1 1c-2-1-3-1-5-1l-1 1h-1-1 0-5l-1 1c1 1 2 0 4 0h1c-3 1-5 0-8 1h0v-10z"></path><path d="M571 506c-2 0-7 0-9-1l10-1h2l1 1c0-1 1-1 2-1h2c6 0 12 1 17 1-2 2-5 1-7 2v1h-1v-1c-2 0-6 1-8 0-1 0-1 0-1 1l-2-2-1 1c-2-1-3-1-5-1z" class="U"></path><path d="M559 509c3-1 5 0 8-1 2 1 5 0 7 1h1c3 1 8 0 12 0 1 0 2 1 4 1h5c1 1 1 1 1 2h2c2-1 0-3 3-2l1-1 1 2h3c1 0 1-1 2-1h3l1 1h0c1-1 1-1 2-1v1c0 1 0 1 1 1-1 1-1 1-2 1 0 1 0 1-1 2h0l1 1c0 1 0 2-2 2l-2 1c2 0 2 0 4 1-7 1-15 0-22 1l-15-1c-4 1-9 0-13 0h-4-1 0v-11h0z" class="m"></path><path d="M573 513h10 3c2-1 2-3 4-2l-2 2h1 25c0 1 0 1-1 2h-9-9c-2 0-4 0-6-1l-1 2-1 1-1-1c1-1 1 0 1-1-1-1-3-1-4-1h-10v-1z" class="F"></path><path d="M588 516l1-2c2 1 4 1 6 1h9 9 0l1 1c0 1 0 2-2 2l-2 1-22-1-1-1 1-1z" class="I"></path><path d="M604 515h9 0l-2 2-6-1-1-1zm-16 1l1-2c2 1 4 1 6 1v1l-1 1-2-1-2 1h-1l-1-1z" class="O"></path><path d="M559 509h0l1 4h2 6 0c2 0 4-1 5 0v1h10c1 0 3 0 4 1 0 1 0 0-1 1l1 1 1 1 22 1c2 0 2 0 4 1-7 1-15 0-22 1l-15-1c-4 1-9 0-13 0h-4-1 0v-11z" class="Y"></path><path d="M560 520l1-1c1 0 3-1 4-1 2-1 3-1 5-1l1 2c-3 0-5 0-7 1h-4z" class="D"></path><path d="M568 513c2 0 4-1 5 0v1h10c1 0 3 0 4 1 0 1 0 0-1 1h-10l-1 1 2-1v-1h6c-1-1-11 1-13 0l-2-2z" class="B"></path><path d="M575 517l1-1h10l1 1 1 1c-6 0-12 1-18 2h7c-4 1-9 0-13 0 2-1 4-1 7-1l-1-2h5z" class="G"></path><path d="M588 518l22 1c2 0 2 0 4 1-7 1-15 0-22 1l-15-1h-7c6-1 12-2 18-2z" class="R"></path><path d="M413 562l7 4c3-2 7 1 11 0l6 1c-1 1-2 2-1 4 0 2 0 5 2 8-1 1-1 3 0 4l-1 1 1 2 1 1c-1 1-1 2-1 4l-1-1h-1c1 3 2 6 3 10l9-7s1 0 1-1h0c0-2 0-3 2-3 1 1 1 8 1 8l1 11c-1 8 0 17-1 25v10 7 19 13l2-1 7-3v7 4l-1 16-3 16h0l-1 2h-2l1 1h1c-2 12-5 23-9 34l-1 2-2 3-5 10c-1 3-3 7-5 9h-2l-3-1c-1 0-2 1-3 0l3-6c-2 1-3 2-3 3v1h-2c1-1 1-2 1-4v-1c0-2 3-6 4-8l5-15c4-15 6-30 6-45l-41-1h-7-3-1l1-2 1-1c6 1 14 1 20 0 9 0 18 1 27 0 1 0 3 0 4-1v-1-1c-1-2 0-5 0-7-4-1-7-2-10-4-3-1-5-4-7-6l-3 1v-1l-2 1h0c1-2 3-4 4-6h2c2-1 2-1 4 0 2 0 4 0 6-1l1-2 1-1-1-1-2-2 2-2h-1c-1-1-2-1-3-1h-1v-1h-2c-1-1-3-1-4-1-2 0-4-1-5-1 2 0 4-1 6-1l15-8 1-1-1-2h0c-1-1-1-2-1-3h0c-1-1-2-2-2-4 1 1 1 1 2 1v-1h-1l1-2c0-2 1-3 1-4-1-2-1-3-1-4s0-1 1-2h0v-22c0-1 1-6-1-7h-1c-2 1-5 4-7 6-1-10-2-16-6-25 0-1 1-1 1-2l-1-2c-2-2-4-4-6-7 2 0 4 3 5 4v-1h2l-7-7 1-1c-1-3-7-3-8-7z" class="n"></path><path d="M413 562l7 4c4 3 8 6 11 10 2 3 4 6 6 8l1 2 1 1c-1 1-1 2-1 4l-1-1h-1c-1-2-2-5-3-7-3-5-8-10-12-14-1-3-7-3-8-7z" class="R"></path><path d="M435 676h0c1 1 1 1 2 1v-1h1v4c1 0 1 0 1 1l-6-1c3 2 7 3 10 4-4 1-8-1-12-3-1 0-3-1-5-1l-2 1v1l-3 1v-1l-2 1h0c1-2 3-4 4-6h2c2-1 2-1 4 0 2 0 4 0 6-1z" class="H"></path><defs><linearGradient id="l" x1="438.251" y1="576.311" x2="419.182" y2="568.039" xlink:href="#B"><stop offset="0" stop-color="#b1a796"></stop><stop offset="1" stop-color="#dad3c3"></stop></linearGradient></defs><path fill="url(#l)" d="M420 566c3-2 7 1 11 0l6 1c-1 1-2 2-1 4 0 2 0 5 2 8-1 1-1 3 0 4l-1 1c-2-2-4-5-6-8-3-4-7-7-11-10z"></path><path d="M454 681l7-3v7 4l-1 16-3 16h-3v1c-3 0-3 0-4 1v1c-1 1-2 1-3 1l5-43 2-1z" class="e"></path><path d="M452 682l2-1-1 17c0 4-2 21-1 23-1 0-2 1-2 2v1c-1 1-2 1-3 1l5-43z" class="K"></path><path d="M450 723c1-1 1-1 4-1v-1h3 0l-1 2h-2l1 1h1c-2 12-5 23-9 34l-1 2-2 3-5 10c-1 3-3 7-5 9h-2l-3-1c-1 0-2 1-3 0l3-6 6-12c5-12 9-25 12-38 1 0 2 0 3-1v-1z" class="e"></path><path d="M435 763c0 1 1 2 1 2-1 5-3 10-6 14 0 1-1 1-1 2-1 0-2 1-3 0l3-6 6-12z" class="h"></path><path d="M450 723c1-1 1-1 4-1v-1h3 0l-1 2h-2l1 1c-3 0-4 1-6 3l-13 38s-1-1-1-2c5-12 9-25 12-38 1 0 2 0 3-1v-1z" class="J"></path><path d="M441 633c1 0 2 1 4 1h2v1 1h0c1 3 0 5 1 8 0 7 1 14 1 22 0 2-1 5 0 8h0c-1 2-1 4-1 5l1 2h-1c-2 1-3 2-5 3-3-1-7-2-10-4l6 1c0-1 0-1-1-1v-4h-1v1c-1 0-1 0-2-1h0l1-2 1-1-1-1-2-2 2-2h-1c-1-1-2-1-3-1h-1v-1h-2c-1-1-3-1-4-1-2 0-4-1-5-1 2 0 4-1 6-1l15-8 1-1-1-2h0c-1-1-1-2-1-3h0c-1-1-2-2-2-4 1 1 1 1 2 1v-1h-1l1-2c0-2 1-3 1-4-1-2-1-3-1-4s0-1 1-2h0z" class="a"></path><path d="M441 655v1h0c-2 1-3 2-4 4 1 1 1 1 2 1l1 5h-2v1h3v1h-1c0 1 1 1 2 1-1 2-2 1-2 2l1 1-2 2h0c2 0 2 0 3 1v1c-1 1 0 2 0 3l-1 1 1 1h-3c0-1 0-1-1-1v-4h-1v1c-1 0-1 0-2-1h0l1-2 1-1-1-1-2-2 2-2h-1c-1-1-2-1-3-1h-1v-1h-2c-1-1-3-1-4-1-2 0-4-1-5-1 2 0 4-1 6-1l15-8z" class="L"></path><path d="M426 663h1c1 0 2 0 3-1v1h3 1l-1 2v1h-2-2c-1-1-3-1-4-1-2 0-4-1-5-1 2 0 4-1 6-1z" class="W"></path><path d="M445 634h2v1 1h0c1 3 0 5 1 8 0 7 1 14 1 22 0 2-1 5 0 8h0c-1 2-1 4-1 5l1 2h-1c-1 0-1 0-2 1h-4 0c1 0 2-1 3-2s2-1 2-2l-2-1v-1l1-1c-1-1-1-1-1-2-1-1 0 0 0-1s-1-1-1-2c1 0 1 0 2-1l-1-1v-4-2c0-2-1-4-1-5l-1-1 1-1c0-1 0-1 1-2v-1l-1-1c0-2 0-3 1-4l-2-2c0-1 0-3 1-4h0c0-2 0-3-1-5-1-1-1 0 0-1l2-1h0z" class="I"></path><path d="M416 809l3 1 21 5 10 3c5 2 10 4 14 7 6 3 11 7 16 12 1 1 4 2 4 4h0l2 2-1-2 2-2c-1 0-1-1-2-2l1-1 1 1h2v-4c1-2 3-3 4-4l1 1 1-1 1 1 2-1 1 1v6c0 1 1 2 1 2h1 1c1 1 1 0 1 0 1 2 1 2 1 3v2c0 2 0 4 1 5 1-1 1-1 1-2h1c0 1 0 2 1 3 1-1 1-1 1-2v2l-1 1c2 1 0-1 2 1h1l1 1 1 1h2l3-3h1l2-2c3-3 6-7 10-10h2 1 1l-9 9c-5 6-9 12-12 18-2 4-3 7-4 10s-2 8-3 9v4h-3-1c-1 1 0 1 0 2v1 3h2 1c1 0 4-1 5 0v2-1h-1-4c-1 0-2 0-3 1h-1c-2 0-4 2-4 4h-1l-1 3h-1c0 1-1 2-1 2-3-2-4-8-5-11s-2-6-4-9c-3-6-7-12-12-18-16-19-40-31-64-35-8-2-16-3-25-3l13-13h1c3-3 6-2 10-2 2 0 5 1 8 1-2-1-2-1-4-1v-1l2-1-1-1 1-1 2-1z" class="n"></path><path d="M482 849c5 5 11 12 15 18 0 2 0 3 1 5l3 6c0 1 0 2 1 3 0 1 0 2 1 4v3c-1 1 0 1 0 2v1 3h2 1c1 0 4-1 5 0v2-1h-1-4c-1 0-2 0-3 1h-1c-2 0-4 2-4 4h-1l-1 3h-1 0c1-3 3-7 6-9h2c-2-4-2-9-3-12-4-12-10-21-18-31v-2z" class="g"></path><path d="M408 814c2 0 5 1 8 1l32 9c11 6 24 14 32 23l-1 1c-1 0-4-3-5-5-6-4-11-8-17-12-17-9-39-16-59-15 3-3 6-2 10-2z" class="c"></path><path d="M416 809l3 1 21 5 10 3-1 3-1 1 1 1s1 0 1 1h-2l-32-9c-2-1-2-1-4-1v-1l2-1-1-1 1-1 2-1z" class="b"></path><path d="M450 818c5 2 10 4 14 7 6 3 11 7 16 12 1 1 4 2 4 4h0l-4 6c-8-9-21-17-32-23h2c0-1-1-1-1-1l-1-1 1-1 1-3z" class="q"></path><path d="M450 818c5 2 10 4 14 7l-8-1c-1 0-3-1-3-1-1-1 0-1-1-2-2 1-1 1-2 3 0-1-1-1-1-1l-1-1 1-1 1-3z" class="d"></path><defs><linearGradient id="m" x1="524.539" y1="864.228" x2="498.075" y2="827.049" xlink:href="#B"><stop offset="0" stop-color="#212122"></stop><stop offset="1" stop-color="#4e4d4c"></stop></linearGradient></defs><path fill="url(#m)" d="M495 829l1 1 2-1 1 1v6c0 1 1 2 1 2h1 1c1 1 1 0 1 0 1 2 1 2 1 3v2c0 2 0 4 1 5 1-1 1-1 1-2h1c0 1 0 2 1 3 1-1 1-1 1-2v2l-1 1c2 1 0-1 2 1h1l1 1 1 1h2l3-3h1l2-2c3-3 6-7 10-10h2 1 1l-9 9c-5 6-9 12-12 18-2 4-3 7-4 10s-2 8-3 9v4h-3-1v-3c-1-2-1-3-1-4-1-1-1-2-1-3l-3-6c-1-2-1-3-1-5-4-6-10-13-15-18v2c-1-1 0-2-1-3h-2l1-1 4-6 2 2-1-2 2-2c-1 0-1-1-2-2l1-1 1 1h2v-4c1-2 3-3 4-4l1 1 1-1z"></path><path d="M487 839c1 2 1 3 1 5l-1-1h-1l-1-2 2-2z" class="B"></path><path d="M512 852l1 1h2l-2 2-1-1h-5-2v-1l1 1c2-1 3-1 5-2v-1l1 1z" class="C"></path><path d="M500 843c1-1 2-1 4-2v2l-3 4h-1 0v-4z" class="B"></path><path d="M500 847l-2-1h0l-1-1c-1-2 0-4 0-6l2-1c0 2 0 4 1 5v4z" class="N"></path><path d="M499 836c0 1 1 2 1 2h1 1c1 1 1 0 1 0 1 2 1 2 1 3-2 1-3 1-4 2-1-1-1-3-1-5v-2z" class="T"></path><path d="M499 859c3 4 5 8 6 12 1 2 2 5 2 8 1 2 0 4 0 5v4h-3-1v-3c-1-2-1-3-1-4-1-1-1-2-1-3l-3-6c-1-2-1-3-1-5l5 5c0-2-1-4-2-7-1-2-1-3-1-5v-1z" class="J"></path><path d="M505 871c1 2 2 5 2 8 1 2 0 4 0 5v4h-3-1v-3c-1-2-1-3-1-4 0 1 1 1 1 2v1-4h0c1-3 1-6 2-9z" class="W"></path><path d="M480 847l4-6 2 2h1l1 1 11 15v1c0 2 0 3 1 5 1 3 2 5 2 7l-5-5c-4-6-10-13-15-18v2c-1-1 0-2-1-3h-2l1-1z" class="j"></path><path d="M480 847l4-6 2 2h1c-2 2-3 4-5 6v2c-1-1 0-2-1-3h-2l1-1z" class="C"></path><path d="M580 221l13-1c2 1 5 1 7 0l8 1 21 5c7 2 14 4 20 8h1l9 4 1 1 8 5c2 1 4 3 5 3l2 2c4 3 8 6 12 10l1 1c2-2 3-4 4-6 7 5 12 11 17 17 9 13 18 26 24 41l11 17h0c1 2 1 3 2 5v-2-2h1v5h-2c0-1-1-3-1-4-3 0-2-3-4-4-1 1-1 2-2 3l-1 1h0c0 2 1 4 1 6 1 1 0 3 0 5h1v4c3 7 4 14 5 22v24c-4-13-7-26-14-38-1 3-1 8-1 11-3-6-4-12-6-17-7-19-17-37-29-53-24-30-58-51-96-56-1 1-1 1-2 1v-1c-9-1-18-2-28-2l-2 6c0-4 4-11 6-14l1-1c1-3 5-5 7-7z" class="n"></path><path d="M731 326c1 0 2 1 2 1 2 1 1 4 3 6l1-2c0 2 1 4 1 6 1 1 0 3 0 5l-1 1v-1l-6-16z" class="V"></path><defs><linearGradient id="n" x1="706.519" y1="301.01" x2="722.705" y2="284.24" xlink:href="#B"><stop offset="0" stop-color="#a7a596"></stop><stop offset="1" stop-color="#d4c6b4"></stop></linearGradient></defs><path fill="url(#n)" d="M688 260c2-2 3-4 4-6 7 5 12 11 17 17 9 13 18 26 24 41l11 17h0c1 2 1 3 2 5v-2-2h1v5h-2c0-1-1-3-1-4-3 0-2-3-4-4-1 1-1 2-2 3l-1 1h0l-1 2c-2-2-1-5-3-6 0 0-1-1-2-1-7-19-17-36-30-52-4-5-9-9-12-14h-1z"></path><path d="M555 545c2 1 2 2 2 3v15h0l1-4c0 3-1 6 1 8v10-2c0-1 1-1 1-2h2v6c-1 1-3 1-3 2-1 2 0 5 0 7v18 14l-1-2c0 5-1 12 0 17h0c-1 1-1 3-1 4v11l-1 20v9l-1 16 2 2h-1c-1-2-3-3-5-4l-1 1v1 2l-2-2c-1-1-1-2-1-3h-1-1c-1 0-2-1-3-2h0-5-1l-1-1c0-1 0-1-1-1v-1l1-1c-2 1-5 2-6 4l-1 2v10l-1 2h0v-1l-1-1v-1c-1-3 0-7-2-10v-1c0-2-1-4-1-6v-2-12-2c-1-2-1-3-1-5v-2h-1v1l-1-2c1-1 2-1 2-2h-2c-1-4 0-10 0-14v-3h-3c-1 0-2 0-3-1-2-2-1-10-1-13v-11-10h-1v1-1h-1v-11c0-4 0-8 1-12v-6-2l1-2v-1-3c0-1-1-2-1-4h1l-2-3h2 0 1v-1l-2-1-1-1c5-1 8-2 12-4h1l2-1h1v1l3 8c2 1 3 2 4 4l1 1 1 2c-1-1-2-1-2-2l-1 1c1 1 3 4 4 5v1l2 3 1 2c-1 0-1 1-1 2l-1 1h6c1 1 1 2 2 3l1 1h2v1c0 1 0 3 1 4v-11h0 0l2-1c1-2 1-8 1-11 0-1 1-2 1-3v-4-14-3h1z" class="b"></path><path d="M556 634l1 5v11l-1-1c-1-5-1-10 0-15z" class="U"></path><path d="M550 581h0v22h0v-8h-1c-2-3-2-4-2-8h2v1c0 1 0 3 1 4v-11h0z" class="B"></path><path d="M549 595h1v8h0v48c-1-2 0-5 0-8v-14l-1-1-1-1 1-1c1-5-1-9-3-14 0-1 0-2-1-4v-2l-1-1-2-3h1c1 0 1 1 2 2h0v-2h0l2 3 2 4v-8-6z" class="I"></path><path d="M555 545c2 1 2 2 2 3v15h0l1-4c0 3-1 6 1 8v10-2c0-1 1-1 1-2h2v6c-1 1-3 1-3 2-1 2 0 5 0 7v18 14l-1-2c0 5-1 12 0 17h0c-1 1-1 3-1 4l-1-5c0-4-1-11 0-15 1-1 1-3 1-4v-1-15c0-2 1-5 0-8-1-6-1-11-1-17 0-3-1-7-1-10v-6c2-1 0-5 0-7v-6z" class="Z"></path><path d="M560 573h2v6c-1 1-3 1-3 2-1 2 0 5 0 7v18 14l-1-2 1-41v-2c0-1 1-1 1-2z" class="I"></path><defs><linearGradient id="o" x1="548.247" y1="594.223" x2="533.753" y2="610.777" xlink:href="#B"><stop offset="0" stop-color="#1c1b1b"></stop><stop offset="1" stop-color="#4c4b4a"></stop></linearGradient></defs><path fill="url(#o)" d="M538 583h6c1 1 1 2 2 3l1 1c0 4 0 5 2 8v6 8l-2-4-2-3h0v2h0c-1-1-1-2-2-2h-1l2 3 1 1v2c1 2 1 3 1 4 2 5 4 9 3 14l-1 1c-1-3-3-6-5-8-1-1-3-4-4-4l-1-2 1-1c-2-2-1-3 0-6v-2l-2-2c-1-3-1-7-2-11v-1c0-2-1-6 1-8l2 1z"></path><path d="M538 583h6c1 1 1 2 2 3l1 1c0 4 0 5 2 8v6l-3-5h0c-2-4-6-6-7-10h-1c-1-1 0-1-1-1l-1-1h0c-1 2 0 5 0 7h-1v-1c0-2-1-6 1-8l2 1z" class="D"></path><defs><linearGradient id="p" x1="553.639" y1="659.968" x2="528.556" y2="663.608" xlink:href="#B"><stop offset="0" stop-color="#1b1a1a"></stop><stop offset="1" stop-color="#494948"></stop></linearGradient></defs><path fill="url(#p)" d="M543 619c2 2 4 5 5 8l1 1 1 1v14c0 3-1 6 0 8l1 39 4 5 2 2h-1c-1-2-3-3-5-4l-1 1v1 2l-2-2c-1-1-1-2-1-3h-1-1c-1 0-2-1-3-2h0-5-1l-1-1c0-1 0-1-1-1v-1l1-1 1-1c-3-3-5-6-7-10-1-3-2-5-2-8v-3-5c1-2 0-5 0-7h1c1 1 2 2 4 3 0-1 1-2 1-3 3 1 6 3 10 3 2 1 3 1 4 0v-1-9c0-7 2-16-3-21 0-2-1-4-1-5z"></path><path d="M541 685c1 1 2 2 5 2v-1c1 2 0 3 1 5v1h-1c-1-2-3-4-5-5v-2h0z" class="B"></path><path d="M541 685l-1-1c0-1-1-2-2-3h0 3c0-1 0-2 1-3v3l1 1c2 1 3 2 3 4v1c-3 0-4-1-5-2z" class="D"></path><path d="M527 652h1c1 1 2 2 4 3 0-1 1-2 1-3 3 1 6 3 10 3 2 1 3 1 4 0 1 3 1 5 1 7l-1 13v1l-1 1h-1l-1-1c-1-1-1-2-2-2-1-1-7-1-9-1l-1 1h1v1h-4c-1-3-2-5-2-8v-3-5c1-2 0-5 0-7z" class="C"></path><path d="M532 667c1 2 1 2 3 3v-1-1h0c1 0 1 1 2 2 1 0 1-1 2-1 2 1 3 2 5 2h0c1 1 2 1 2 2v1h0c-1-1-2-1-3-2-2-1-5-1-7-2-1 0-2 0-3-1-1 0-1-1-1-2z" class="F"></path><path d="M533 652c3 1 6 3 10 3 2 1 3 1 4 0 1 3 1 5 1 7-2-1-3 0-5-2l2-1-1-1c-2-2-4-2-7-2-1 0-1 0-2-1h-3c0-1 1-2 1-3z" class="T"></path><path d="M527 652h1c1 1 2 2 4 3h0c1 1 2 2 3 2-1 2-3 3-5 4-1 0-2 1-3 3v-5c1-2 0-5 0-7z" class="V"></path><path d="M527 667l1-1c1-1 2-2 2-3l4-2 1 1c1 1 1 1 2 1 0 1 0 1-1 1-2 1-3 1-4 3 0 1 0 2 1 2 1 1 2 1 3 1 2 1 5 1 7 2 1 1 2 1 3 2h0c0 1 1 1 1 2l-1 1h-1l-1-1c-1-1-1-2-2-2-1-1-7-1-9-1l-1 1h1v1h-4c-1-3-2-5-2-8z" class="B"></path><path d="M531 583c1-1 1-2 2-3l1 1h3 0c1 1 1 0 2 1l-1 1-2-1c-2 2-1 6-1 8v1c1 4 1 8 2 11l2 2v2c-1 3-2 4 0 6l-1 1 1 2c1 0 3 3 4 4 0 1 1 3 1 5 5 5 3 14 3 21v9 1c-1 1-2 1-4 0-4 0-7-2-10-3 0 1-1 2-1 3-2-1-3-2-4-3h-1v-6-3-1l-1 1 1-61 1 2c1-1 2-1 3-1z" class="K"></path><path d="M537 602l2 2v2l-2-1c-1 0-2 1-3 2h0c2-2 2-3 3-5z" class="a"></path><path d="M527 634c0-4-1-12 1-16h0c0 2 0 3 1 4l1-2v2c-1 3-1 7-1 9-1 1-1 1-1 2l-1 1z" class="V"></path><path d="M531 583c1-1 1-2 2-3l1 1h3 0c1 1 1 0 2 1l-1 1-2-1h-1l-1 1c0 5-1 9-1 14l-1-6v-4c0-1 0-3-1-4z" class="L"></path><path d="M530 622l2 3h0c-1 2-1 5 0 7 1 1 2 1 3 2l2 2h1v-2l1 1c0 1-1 1-1 2l1 1h-1v4c1-1 1-1 2-1l-1 2h-1-1l-1-1v-2l1-1v-1l-1-1-1 1-1-1h-2c-1-1-1-2-2-2s-2-1-3-1l1-1c0-1 0-1 1-2 0-2 0-6 1-9z" class="E"></path><path d="M529 631l5 6h-2c-1-1-1-2-2-2s-2-1-3-1l1-1c0-1 0-1 1-2z" class="L"></path><path d="M534 625h0c1 1 2 1 3 1l6 9c-1 0-1 0-2 1l-2-1-1-1v2h-1l-2-2c-1-1-2-1-3-2-1-2-1-5 0-7l2 3 1-1-1-2z" class="M"></path><path d="M534 625h0c1 1 2 1 3 1l6 9c-1 0-1 0-2 1l-2-1-1-1v2h-1l1-2h4c-1-1-2-2-4-3s-2-1-3-3h-1l1-1-1-2z" class="G"></path><path d="M528 584c1-1 2-1 3-1 1 1 1 3 1 4v4l1 6v1c-2 2-2 2-4 2-2-2-1-13-1-16h0z" class="S"></path><path d="M532 591c-1 2-1 5-1 7h-1c0-4-1-9 1-12h0l1 1v4z" class="I"></path><path d="M534 607c1-1 2-2 3-2l2 1c-1 3-2 4 0 6l-1 1 1 2c-1 0-2-1-3-2v-1c-3 4 1 9-2 13h0l1 2-1 1-2-3h0l-2-3v-2c-1-2 0-4 0-6v-2c1-2 2-4 4-5h0z" class="S"></path><path d="M534 607c1-1 2-2 3-2l2 1c-1 3-2 4 0 6l-1 1c-1-2-3-3-4-6z" class="C"></path><path d="M534 625h0c3-4-1-9 2-13v1c1 1 2 2 3 2s3 3 4 4c0 1 1 3 1 5 5 5 3 14 3 21v9 1c-1 1-2 1-4 0-4 0-7-2-10-3 0 1-1 2-1 3-2-1-3-2-4-3h-1v-6c3-3 6-4 9-6v2l1 1h1 1l1-2c-1 0-1 0-2 1v-4h1l-1-1c0-1 1-1 1-2l2 1c1-1 1-1 2-1l-6-9c-1 0-2 0-3-1h0z" class="N"></path><path d="M536 613c1 1 2 2 3 2s3 3 4 4c0 1 1 3 1 5-2-2-3-4-5-6 0-2-2-3-3-5z" class="B"></path><path d="M540 648l2-1c2 2 4 4 5 7v1c-1 1-2 1-4 0l-1-4c-1-1-2-1-2-3z" class="F"></path><path d="M536 640v2l1 1h1 1l1-2c1 1 1 2 2 2l1 1 1 1h-1c-1 1 0 1-1 0h-2c1 1 1 2 2 2l-2 1c0 2 1 2 2 3l1 4c-4 0-7-2-10-3 0 1-1 2-1 3-2-1-3-2-4-3h-1v-6c3-3 6-4 9-6z" class="D"></path><path d="M533 652l-1-1 1-1h1l3 2c0-1 0-1-1-2 1-2 2-2 4-2 0 2 1 2 2 3l1 4c-4 0-7-2-10-3z" class="G"></path><path d="M536 640v2 1 1c-3 3-5 3-6 7l-2 1h-1v-6c3-3 6-4 9-6z" class="T"></path><path d="M523 555h1l2-1h1v1l3 8c2 1 3 2 4 4l1 1 1 2c-1-1-2-1-2-2l-1 1c1 1 3 4 4 5v1l2 3 1 2c-1 0-1 1-1 2-1-1-1 0-2-1h0-3l-1-1c-1 1-1 2-2 3-1 0-2 0-3 1l-1-2-1 61 1-1v1 3 6c0 2 1 5 0 7v5 3c0 3 1 5 2 8 2 4 4 7 7 10l-1 1c-2 1-5 2-6 4l-1 2v10l-1 2h0v-1l-1-1v-1c-1-3 0-7-2-10v-1c0-2-1-4-1-6v-2-12-2c-1-2-1-3-1-5v-2h-1v1l-1-2c1-1 2-1 2-2h-2c-1-4 0-10 0-14v-3h-3c-1 0-2 0-3-1-2-2-1-10-1-13v-11-10h-1v1-1h-1v-11c0-4 0-8 1-12v-6-2l1-2v-1-3c0-1-1-2-1-4h1l-2-3h2 0 1v-1l-2-1-1-1c5-1 8-2 12-4z" class="C"></path><path d="M530 578c1 1 2 1 3 2h0c-1 1-1 2-2 3-1 0-2 0-3 1l-1-2v-2h0c0 1 0 1 1 2 0-1 0-1 2-2l-1-1 1-1z" class="i"></path><path d="M524 563h0c0-2 0-3 1-5v3 23c-1-2 0-4-1-6l-1 1v-2l1-14z" class="g"></path><path d="M523 579l1-1c1 2 0 4 1 6v34c-1 4-1 9-1 13h0 0l-1-15v-37z" class="H"></path><path d="M526 556l1-1 3 8c2 1 3 2 4 4l1 1 1 2c-1-1-2-1-2-2l-1 1c1 1 3 4 4 5v1l2 3 1 2c-1 0-1 1-1 2-1-1-1 0-2-1h0-3l-1-1h0c-1-1-2-1-3-2 0-2-1-4-2-6-1-3-1-7-2-10l-1-1v-3-1l1-1z" class="K"></path><path d="M525 558v-1l1-1c1 2 1 4 0 6l-1-1v-3z" class="E"></path><path d="M536 578h3l1 2c-1 0-1 1-1 2-1-1-1 0-2-1h0-3c1-1 1-1 2-1h1v-1l-1-1z" class="i"></path><path d="M533 570c1 1 2 3 4 5l2 3h-3l-2-2c-1-2-1-4-1-6z" class="c"></path><path d="M530 563c2 1 3 2 4 4l1 1 1 2c-1-1-2-1-2-2l-1 1c1 1 3 4 4 5v1c-2-2-3-4-4-5-2-2-3-5-3-7z" class="D"></path><path d="M524 631h0 0c0-4 0-9 1-13 0 5-1 27 1 30v-5l1-1v1 3 6c0 2 1 5 0 7v5 3c0 3 1 5 2 8 2 4 4 7 7 10l-1 1c-2 1-5 2-6 4l-1 2v10l-1 2h0v-1l-1-1v-1c-1-3 0-7-2-10v-1c0-2-1-4-1-6v-2-12l1-39z" class="R"></path><path d="M526 643l1-1v1 3 6c0 2 1 5 0 7v5 3c0 3 1 5 2 8 2 4 4 7 7 10l-1 1c-2 1-5 2-6 4l-1 2c-1-1 0-1 0-2s-1-1-1-3c1-1 2-2 4-2s1 1 3 0c-2-2-4-4-5-6-2-3-2-6-3-8-1-8 0-16 0-23v-5z" class="W"></path><path d="M519 609h1l1 2s1 1 1 2c-1 1-1 1-1 3h1 1l1 15-1 39v-2c-1-2-1-3-1-5v-2h-1v1l-1-2c1-1 2-1 2-2h-2c-1-4 0-10 0-14v-3h-3c-1 0-2 0-3-1-2-2-1-10-1-13v-11c1-1 1-1 2-1 2-1 3-1 5-1-1-2 0-3-1-5z" class="D"></path><path d="M513 616c1-1 1-1 2-1 2-1 3-1 5-1v7 16l-1 1h-2v1h2v1h-5c-2-2-1-10-1-13v-11z" class="M"></path><path d="M513 616c1-1 1-1 2-1 2-1 3-1 5-1v7h-5v9h-1c0-1 0-2-1-3h0v-11z" class="F"></path><path d="M515 617h4v1c-1 1-2 1-3 1l-1 1h0v-3z" class="M"></path><path d="M513 627l1-10h1v3h0v1 9h-1c0-1 0-2-1-3z" class="Q"></path><path d="M523 555h1l2-1h1v1l-1 1-1 1v1c-1 2-1 3-1 5h0l-1 14v2 37h-1-1c0-2 0-2 1-3 0-1-1-2-1-2l-1-2h-1c1 2 0 3 1 5-2 0-3 0-5 1-1 0-1 0-2 1v-10h-1v1-1h-1v-11c0-4 0-8 1-12v-6-2l1-2v-1-3c0-1-1-2-1-4h1l-2-3h2 0 1v-1l-2-1-1-1c5-1 8-2 12-4z" class="C"></path><path d="M520 568l1-1 1 1h-1v3 11c-1 0-2-1-3-2v-1-3-1c-1-2-1-1-2-1v-1l-1-1-1 2v2c0 1 0 1-1 2l-1-1v-2l1-2v-1-3h1l1-1v2c1-1 1-1 3-2v1h1l1-1z" class="S"></path><path d="M523 555h1l2-1h1v1l-1 1-1 1v1c-1 2-1 3-1 5h0l-1-1c-1 0-1 1-2 1v1c0 1-1 1-1 2h-1l1 2-1 1h-1v-1c-2 1-2 1-3 2v-2l-1 1h-1c0-1-1-2-1-4h1l-2-3h2 0 1v-1l-2-1-1-1c5-1 8-2 12-4z" class="E"></path><path d="M518 559l2 3c-1 2-1 2-1 4l1 2-1 1h-1v-1l-1-1h-1c1-3 1-5 2-8z" class="Q"></path><path d="M523 555h1l2-1h1v1l-1 1-1 1v1c-1 2-1 3-1 5h0l-1-1c-1 0-1 1-2 1v1c0 1-1 1-1 2h-1c0-2 0-2 1-4l-2-3v-1c2 0 3-1 5-3z" class="M"></path><path d="M515 594l1-3-1-1c0-1 0-1 2-2l2 2v18l-4 1v1c2 0 3-2 4-1 1 2 0 3 1 5-2 0-3 0-5 1-1 0-1 0-2 1v-10-3h2v-1h-1v-2l-1-1c0-2 0-2 2-4v-1z" class="V"></path><path d="M512 577l1 1c1-1 1-1 1-2v-2l1-2 1 1v1c1 0 1-1 2 1v1c-1 0-1 0-2 1 1 1 0 1 1 3 1 1 1 2 2 4v6l-2-2c-2 1-2 1-2 2l1 1-1 3v1c-2 2-2 2-2 4l1 1v2h1v1h-2v3h-1v1-1h-1v-11c0-4 0-8 1-12v-6z" class="a"></path><path d="M511 595c1 2 2 1 1 3v2 1c1 1 0 3 0 5v1-1h-1v-11z" class="B"></path><path d="M517 580c1 1 1 2 2 4v6l-2-2c-2 1-2 1-2 2l1 1-1 3c-1 0-1-1-2-2 1-2 0-4 1-6 0-1 1-1 2-2v1c-1 0 0 0-1 1l1 1h1l-1-1 1-1c1-2 0-3 0-5z" class="L"></path><path d="M472 685h1v-1c1-2 2-2 3-3l2 2-2 3h2c1 1 3 1 4 2 1 2 2 3 2 4 0 3 0 7-1 10l-1 7v3l-6 15c0 1 0 3 1 4h0c0-1 0-1 1-2 1 1 1 1 1 2h1c0 1 0 2 1 3-1 1-2 3-1 5h-2l-2 4-1 3c-2 4-3 8-4 12-1 6-2 11-1 18 0 4 2 9 4 13l4 6c7 6 14 11 22 12l1 1 1 1-1 1s1 0 1 1c1 1 0 5 1 7 0 3 1 7 2 11-1 1-2 1-2 3v6s0 1-1 0h-1-1s-1-1-1-2v-6l-1-1-2 1-1-1-1 1-1-1c-1 1-3 2-4 4v4h-2l-1-1-1 1c1 1 1 2 2 2l-2 2 1 2-2-2h0c0-2-3-3-4-4-5-5-10-9-16-12-4-3-9-5-14-7l-10-3-21-5-3-1h0-2c6-6 11-12 15-18l4-8 1-1c2-2 4-6 5-9l5-10 2-3 1-2c4-11 7-22 9-34h-1l-1-1h2l1-2h0l3-16 1-16v-4c1-2 1-2 3-2v2l1 1v-2h1c1 1 1 1 3 2h3v-1z" class="C"></path><path d="M473 806h1 0c-1 2-1 2 0 3h-4v1l-2-2h1c1-1 2-1 3-2h1z" class="Y"></path><path d="M449 781l4-1c-2 3-4 6-8 8l-1 1-1 1 6-9z" class="T"></path><path d="M449 810h1v1c-1 1-4 1-5 2 2 1 4 0 6 1v1l-2 1-8-2c-1 0-3-1-4-2l2-1c1 1 2 1 3 0h1c2 0 4-1 6-1z" class="E"></path><path d="M433 783c1 1 1 2 2 3 2 3 6 6 9 8 1 1 1 0 1 1-1 0-2 0-2-1-3-1-5-3-8-5-1 0-2 1-3 1l-1 1h-2l4-8z" class="Y"></path><path d="M433 792h1l3 1c2 1 3 2 5 4h1c2 1 4 2 5 4h0c1 2 3 4 5 5l-1 1c-1 0-1 0-3-1-2-4-7-8-11-11l-5-3zm56 41h-2c-1-2 0-3-2-5-1-1-1-1-1-3 1 0 1 0 2-1h0l3 2c2 0 4 0 6-1h0l1 1v1h-1v2l-1 1-1-1c-1 1-3 2-4 4z" class="E"></path><path d="M464 770c0 8 2 16 5 23v1l-1 1c-4-3-5-10-8-14-1-1 0 1-1-1 0-2 1-4 2-6 0-1 0 0 1-1 0-1 1-2 2-3z" class="B"></path><path d="M441 814l8 2c1 1 1 1 2 1 3 0 4 1 6 2 8 3 15 8 23 13 2 2 3 4 5 5 1 1 1 2 2 2l-2 2 1 2-2-2h0c0-2-3-3-4-4-5-5-10-9-16-12-4-3-9-5-14-7l-10-3 1-1z" class="P"></path><path d="M473 803v1h1l2-1c1 1 1 2 2 2 2 1 3 3 4 3l3 1v-1-1c1 0 2 0 3-1 5 2 8 4 13 4 0 0 1 0 1 1 1 1 0 5 1 7 0 3 1 7 2 11-1 1-2 1-2 3v6s0 1-1 0h-1-1s-1-1-1-2v-6c0-2-1-3-2-4s-1-2-2-2c-1 1-1 1-2 1v-2c1 0 1 0 1-1-2 0-3-1-5-2-3-2-6-4-9-5-3-2-7-3-9-5h3v-1c-1-1-1-1 0-3h0-1v-1c-2 0-3 1-5 1v-1l4-2h1z" class="B"></path><path d="M488 815l1-1c1 1 7 6 8 8v2c-1 0-1 0-2-1h-2c1 0 1 0 1-1-2 0-3-1-5-2h1v-1h-1v-2h-1v-2z" class="P"></path><path d="M474 810h1c1 1 2 1 4 0h1v1 3c1-1 1-1 2-1 1 1 2 2 2 3 1 0 2-1 3-1h1v2h1v2h1v1h-1c-3-2-6-4-9-5-3-2-7-3-9-5h3z" class="S"></path><defs><linearGradient id="q" x1="504.08" y1="814.887" x2="490.334" y2="827.548" xlink:href="#B"><stop offset="0" stop-color="#0e0d0b"></stop><stop offset="1" stop-color="#393839"></stop></linearGradient></defs><path fill="url(#q)" d="M485 809v-1-1c1 0 2 0 3-1 5 2 8 4 13 4 0 0 1 0 1 1 1 1 0 5 1 7 0 3 1 7 2 11-1 1-2 1-2 3v6s0 1-1 0h-1-1s-1-1-1-2v-6c0-2-1-3-2-4s-1-2-2-2c-1 1-1 1-2 1v-2h2c1 1 1 1 2 1h1c0 1 0 1 1 2h0l1 2v2 1h1c-1-2-1-4-1-7h-1c0-3-3-5-5-7-1-2-2-5-3-6-2 0 0 1-2 0l-1-1h-1c-1-1-1-1-2-1z"></path><path d="M429 791h2l2 1 5 3c4 3 9 7 11 11l-2 2h-1v1h3c1-1 2-1 4-1 1 1 3 1 4 2-2 1-4 0-6 0l-1-1-1 1c-2 0-4 1-6 1h-1c-1 1-2 1-3 0l-2 1c1 1 3 2 4 2l-1 1-21-5-3-1h0-2c6-6 11-12 15-18z" class="T"></path><path d="M434 798c2-1 2-1 4-1l4 4h-5l1-2-1-1h-3z" class="N"></path><path d="M434 798h3l1 1-1 2c-2 1-3 1-5 0 0-2 1-2 2-3z" class="D"></path><path d="M433 792l5 3v1h-3c-2 0-3 1-5 1v1c-1 2-3 4-5 5l3-6c1 0 2-1 2-2 1-1 2-1 3-3z" class="B"></path><path d="M437 801h5c1 1 3 2 4 4v1 1l-1-1h-3c-1-1-2-1-3-1-1-1-2-2-3-2l-4-1v-1c2 1 3 1 5 0z" class="E"></path><path d="M436 803c1-1 1-1 3 0 2 0 2 1 3 2v1c-1-1-2-1-3-1-1-1-2-2-3-2z" class="C"></path><path d="M429 791h2l2 1c-1 2-2 2-3 3 0 1-1 2-2 2l-3 6c-2 2-6 6-9 6h-2c6-6 11-12 15-18z" class="m"></path><path d="M419 810l1-1h-1c1-1 3-3 4-3l3 1 11 1 2 1c1 0 5 0 6 1l-3 1h1-1c-1 1-2 1-3 0l-2 1c1 1 3 2 4 2l-1 1-21-5z" class="D"></path><path d="M455 758l3 1c2 0 4-1 6 1 0 2-2 3-2 5h1c1 1 1 0 1 2l-1-1-1 1v2l-3 3c-2 3-4 5-6 8l-4 1-6 9h-1l-1 1c2 1 2 1 3 3-3-2-7-5-9-8-1-1-1-2-2-3l1-1c2-2 4-6 5-9l5-10 2-3h1c1 0 3 0 4-1l4-1z" class="F"></path><path d="M457 765v-1c0 3 0 3-2 5h-2 1c1-2 2-3 3-4z" class="C"></path><path d="M452 772l1 1c-1 2-3 3-4 4h-1v-1c1-1 0-2 0-3h3l1-1z" class="N"></path><path d="M446 774c-1 1-2 3-2 5-1 0-2 0-3 1v-1l1-3 1-1c1-1 2-1 3-1z" class="S"></path><path d="M441 786h-1-1c0-1-1-2-1-3 1-2 2-3 3-4v1c1-1 2-1 3-1l-3 7z" class="Y"></path><path d="M444 768c3 1 6 2 8 4l-1 1h-3-2l-3-3 1-2z" class="I"></path><path d="M434 782c2-2 4-6 5-9l1 2h1s1 0 1 1l-1 3c-1 1-2 2-3 4 0 1 1 2 1 3h1 1l-1 3-2-2c-1-1-2-1-3-2v1c-1-1-1-2-2-3l1-1z" class="T"></path><path d="M455 758l3 1c2 0 4-1 6 1 0 2-2 3-2 5h1c1 1 1 0 1 2l-1-1-1 1v2l-3 3c-2 3-4 5-6 8l-4 1 1-2h0c0-2 2-3 3-4 2-4 5-7 8-10v-2c-1-1-2-2-4-2v2l-6-4 4-1z" class="B"></path><path d="M453 775c2 0 2-1 3-2h1c-1 1-2 2-3 4-1 1-3 2-4 2h0c0-2 2-3 3-4zm2-17l3 1c2 0 3 0 5 2l-2 2v1 1-2c-1-1-2-2-4-2v2l-6-4 4-1z" class="N"></path><path d="M446 760h1c1 0 3 0 4-1l6 4v1 1c-1 1-2 2-3 4h-1-1c-1-1-2-1-3-2-2 0-4-1-5 0v1l-1 2 3 3v1c-1 0-2 0-3 1l-1 1c0-1-1-1-1-1h-1l-1-2 5-10 2-3z" class="B"></path><path d="M446 760h1c1 0 3 0 4-1l6 4v1 1c-1 1-2 2-3 4h-1-1c1 0 2-1 2-1v-1c0-1-1-2-2-2-1-1-2-1-3-2h0c-2-1-3 0-5 0l2-3z" class="T"></path><path d="M451 759l6 4v1 1c-1 0-1 0-2-1-2-1-3-3-5-3h-1c-1 0-2 0-2-1 1 0 3 0 4-1z" class="B"></path><path d="M474 739h1c1 1 1 3 1 4l-1 3c-2 4-3 8-4 12-1 6-2 11-1 18 0 4 2 9 4 13l4 6c7 6 14 11 22 12l1 1 1 1-1 1c-5 0-8-2-13-4-1 1-2 1-3 1v1 1l-3-1c-1 0-2-2-4-3-1 0-1-1-2-2l-2 1h-1v-1h-1l-4 2v1l-1 1v1h-1c-2-2-5-1-7-1-1-1-3-1-4-1v-1h3l1-1c4-2 10-5 13-9l-6 2c-1 0-1-1-1-2-1-2-3-3-3-6-1-2-2-4-4-6-4 3-7 8-10 11h-1l11-13 6 9c0 2 1 4 3 5h1l1-1v-1c-3-7-5-15-5-23l-1-2c1 0 1 0 1-1 0-2 0-1-1-2h-1c0-2 2-3 2-5-2-2-4-1-6-1l-3-1-4 1c-1 1-3 1-4 1h-1l1-2 1-1 1-1 9 1h8 0v-1-1c1 1 1 1 1 2l1 1 6-19z" class="S"></path><path d="M447 758l1-1 7 1-4 1c-1 1-3 1-4 1h-1l1-2z" class="a"></path><path d="M463 803c2-1 4-3 6-3 1-1 2 0 3 0h0l-2 1 1 1h0 2v1h-1l-4 2v1l-1 1v1h-1c-2-2-5-1-7-1-1-1-3-1-4-1v-1h3l1-1c0 1 1 1 2 1l2-2z" class="T"></path><path d="M468 805v-1l1-2c1 0 1 1 3 1l-4 2z" class="D"></path><path d="M463 803h1 2c0 1 0 1-1 2s-2 1-3 1l-1-1h0l2-2z" class="C"></path><path d="M474 739h1c1 1 1 3 1 4l-1 3c-2 4-3 8-4 12-1 6-2 11-1 18 0 4 2 9 4 13l4 6c7 6 14 11 22 12l1 1 1 1-1 1c-5 0-8-2-13-4-2-1-4-2-6-4-8-6-14-15-15-25-1-6 0-13 1-19l6-19z" class="Z"></path><defs><linearGradient id="r" x1="475.853" y1="751.622" x2="464.751" y2="759.306" xlink:href="#B"><stop offset="0" stop-color="#8c8679"></stop><stop offset="1" stop-color="#a99d8e"></stop></linearGradient></defs><path fill="url(#r)" d="M474 739h1c1 1 1 3 1 4l-1 3c-2 4-3 8-4 12-1 6-2 11-1 18l-2-3c0-3 1-5 0-7-1 2-1 5 0 7 0 2 0 2-1 4-1-6 0-13 1-19l6-19z"></path><path d="M468 724c0-1 1-1 1-1 2-1 3-2 4-3s1-2 2-2c1-1 0-1 1-2l2-2c-2 4-4 10-3 14l1-3v2c0 1 0 3 1 4h0c0-1 0-1 1-2 1 1 1 1 1 2h1c0 1 0 2 1 3-1 1-2 3-1 5h-2l-2 4c0-1 0-3-1-4h-1l-6 19-1-1c0-1 0-1-1-2v1 1h0-8l-9-1-1 1-1 1c4-11 7-22 9-34h-1l-1-1h2l1-2 1 2c1 0 1 0 1 1l2-1h1c2 0 2-1 4 0l2 1z" class="B"></path><path d="M458 725l3 2c-1 0-2 1-3 1v-3z" class="S"></path><path d="M461 735l1 1c-1 3-4 7-5 10 0 1-1 2-1 2l-1 1c1 1 2 1 3 2-1 0-3 0-4 1l-1 1h-1l4-8 5-10z" class="E"></path><path d="M455 724l-1-1h2l1-2 1 2c1 0 1 0 1 1l2-1h1c2 0 2-1 4 0l2 1c-1 0-2 0-3 1l3 1c1 1 2 1 4 2 0 1 0 1 1 1 0 2 1 3 1 4h-1-1c-1 0-2-2-4-2-2-2-5-3-7-4l-3-2-1-1h-1-1z" class="D"></path><path d="M468 724c0-1 1-1 1-1 2-1 3-2 4-3s1-2 2-2c1-1 0-1 1-2l2-2c-2 4-4 10-3 14l1-3v2c0 1 0 3 1 4h0c0-1 0-1 1-2 1 1 1 1 1 2h1c0 1 0 2 1 3-1 1-2 3-1 5h-2l-2 4c0-1 0-3-1-4h-1l1-5v-3h0v1h0l-1-1h0v2c0-1-1-2-1-4-1 0-1 0-1-1-2-1-3-1-4-2l-3-1c1-1 2-1 3-1z" class="T"></path><path d="M475 734l3 1v4l-2 4c0-1 0-3-1-4h-1l1-5z" class="M"></path><path d="M461 735h0c1-2 1-4 1-5 2 0 4 1 5 2 2 0 2 1 3 2 0 2 0 3-1 4h0c-1 4-5 9-8 11-1 1-1 1-1 2h-2c-1-1-2-1-3-2l1-1s1-1 1-2c1-3 4-7 5-10l-1-1z" class="C"></path><path d="M462 736c1 1 1 2 2 4-1 3-4 5-6 7-1 1-2 2-3 2l1-1s1-1 1-2c1-3 4-7 5-10z" class="D"></path><defs><linearGradient id="s" x1="485.057" y1="693.143" x2="467.61" y2="710.113" xlink:href="#B"><stop offset="0" stop-color="#1a1a1a"></stop><stop offset="1" stop-color="#555453"></stop></linearGradient></defs><path fill="url(#s)" d="M472 685h1v-1c1-2 2-2 3-3l2 2-2 3h2c1 1 3 1 4 2 1 2 2 3 2 4 0 3 0 7-1 10l-1 7v3l-6 15v-2l-1 3c-1-4 1-10 3-14l-2 2c-1 1 0 1-1 2-1 0-1 1-2 2s-2 2-4 3c0 0-1 0-1 1l-2-1c-2-1-2 0-4 0h-1l-2 1c0-1 0-1-1-1l-1-2h0l3-16 1-16v-4c1-2 1-2 3-2v2l1 1v-2h1c1 1 1 1 3 2h3v-1z"></path><path d="M479 708c3-1 2-6 2-8h1v4h0l1-3v1l-1 7v3l-6 15v-2l-1 3c-1-4 1-10 3-14 1-1 1-4 1-6z" class="S"></path><path d="M463 705v-3c5-3 11-8 15-12l1 1c-3 5-7 7-11 11l-1 1v1l1-1c1-1 3-1 5-2v1c-1 1-3 2-4 2l-1 1-1 1c1 1 1 1 1 2v1 1l-1 1c-1-2-2-4-4-6h0z" class="F"></path><path d="M472 685h1v-1c1-2 2-2 3-3l2 2-2 3h2c1 1 3 1 4 2-2 1-2 1-3 3l-1-1c-4 4-10 9-15 12v3c-1-1-1 0-1-1l-1-1-1 2 1-16v-4c1-2 1-2 3-2v2l1 1v-2h1c1 1 1 1 3 2h3v-1z" class="D"></path><path d="M468 694h0c3-2 6-3 8-4l-9 7h0v-2l1-1z" class="G"></path><path d="M461 685c1-2 1-2 3-2v2l1 1h0c-1 2-1 3-3 4h-1v-1-4z" class="T"></path><path d="M466 684c1 1 1 1 3 2h3v-1 4l1 1h-2c-2 1-4 2-5 4h1 1l-1 1c-1 0-2 0-2-1l-1-1c0-1 0-2 1-3v-1h0l1-2-1-1h0v-2h1z" class="E"></path><path d="M466 684c1 1 1 1 3 2h3v-1 4c-1 0-1 0-2-1s-1-1-2-1-2 1-2 2v1c-1 1-1 2-1 4l-1-1c0-1 0-2 1-3v-1h0l1-2-1-1h0v-2h1z" class="C"></path><path d="M460 705l1-2 1 1c0 1 0 0 1 1h0c2 2 3 4 4 6h1c2 1 3 1 6 1h1l4-4c0 2 0 5-1 6l-2 2c-1 1 0 1-1 2-1 0-1 1-2 2s-2 2-4 3c0 0-1 0-1 1l-2-1c-2-1-2 0-4 0h-1l-2 1c0-1 0-1-1-1l-1-2h0l3-16z" class="P"></path><path d="M468 711c2 1 3 1 6 1h1c-4 4-8 8-13 11h-1v-1c3-2 7-4 10-7l-3-4z" class="G"></path><path d="M460 705l1-2 1 1c0 1 0 0 1 1h0l-1 5c0 1-1 1-1 2v3c1 1 1 1 3 0h1c-1 2-1 2-2 3l-1-1c-1 1-1 3-1 5h0v1l-2 1c0-1 0-1-1-1l-1-2h0l3-16z" class="B"></path><path d="M475 712l4-4c0 2 0 5-1 6l-2 2c-1 1 0 1-1 2-1 0-1 1-2 2s-2 2-4 3c0 0-1 0-1 1l-2-1c-2-1-2 0-4 0 5-3 9-7 13-11z" class="N"></path><path d="M559 231l1 1c1-2 2-3 3-4h0c0 1-1 1-1 2-1 1-1 1-1 2l-2 5c-6 13-9 26-8 41 1 1 2 3 4 4h-2c-1 1 0 2 0 3l3 3v25c0 2 1 5 0 7 1 2 2 3 3 4l-1 1c-2-3-4-6-8-8v26 72-3l-1 1h-1-1c-3 0-7-1-10-3l-2 1-2-1h-3c-1 0-1 1-2 1v-1l-2 1h0l-1 1c-1 2-2 2-4 2-2-1-3-2-3-4l1-1c0-1 1-1 2-2h-1c0-1-1-2-2-3v-1l-1-1c-2 1-3 2-5 2v-3l2-1h1c0-3 0-4-1-6v-8l1-1c2 0 4 1 5 0l-1-2h-1l-2-2c-1-1-1-2-2-3l-1-1c1-1 0-3 0-4v-2-1c0-2 1-3 0-4v-2l1-1c0-1 0-3-1-4h0c1-2 1-3 1-4-1-1-1-4-1-6l2-1c-1 0-2-1-3-2 1 0 1-1 1-2h0v-3l-1-1c1-2 1-5 1-7l2 1c0-1 0-2-1-3l1-1 1-1v-1c2 0 2-2 3-4 1 0 3-1 4-1 0-3-2-5-3-8 0-2-1-4-1-6h0c-1-1-1-2-1-3 0-2 0-3-1-5v-1h0 1c0 1 0 2 1 3h0v-2c-1-1-2-2-2-4 0-1 0-1-1-3h0l1-2c-1-1-1-5-1-6-1-8-2-15-1-23l2-2 2-1h0c-1 2-1 3-2 4 0 2 1 5 2 8v-3c1-1 1-2 1-3v-1l2-2h1l3-2 2-2 1 1 1 1c0 2-2 3-3 5 0 1 0 3 1 4 0 1-1 2-2 2h1c0 1 1 2 1 3l1 1h0c0 1 1 3 1 4l11-13c8-10 12-25 18-36h0z" class="M"></path><path d="M547 278v1c1 1 1 1 2 3-2 1-2 1-3 1l-3-3 1-1 1 1 1-1c-1 0-1 0-1-1h1 1z" class="V"></path><path d="M548 289c1 2 1 5 1 8v20 1h-1v-9c1-1 0-2-1-3v-3c0-3 1-7 1-10-1-2 0-3 0-4z" class="E"></path><path d="M533 290c1-3 1-5 2-8 0-1 1-2 2-3h0c1-1 1-2 2-2 1-1 1 0 2 0v1 2c-2 0-2 2-4 3v2 1 1c-1 1-3 2-3 2v3c0 3 1 6 0 9l1 1h1l1 1c-1 0-2-1-3 0l-1 25-1-1c-1-2-1-5-1-8v-1-2c1-1 0-1 0-2 0-3-1-10 1-12 1-2 0-5 0-7l1-5z" class="D"></path><path d="M548 289l-1-1v-1h2v-2h1l1 30 5 5c1 2 2 3 3 4l-1 1c-2-3-4-6-8-8v26-4l-1-1v2h-2c3-3-1-10 1-13v-1h0v-3c1-2 1-3 1-5v-1-20c0-3 0-6-1-8z" class="N"></path><path d="M550 285c1-1 0-1 1-2l2 2 3 3v25c0 2 1 5 0 7l-5-5-1-30z" class="b"></path><defs><linearGradient id="t" x1="523.599" y1="260.516" x2="569.671" y2="314.801" xlink:href="#B"><stop offset="0" stop-color="#201f1e"></stop><stop offset="1" stop-color="#3e3d3c"></stop></linearGradient></defs><path fill="url(#t)" d="M559 231l1 1c1-2 2-3 3-4h0c0 1-1 1-1 2-1 1-1 1-1 2l-2 5c-6 13-9 26-8 41h-1l-6-6c0 2 3 4 4 6h-1-1c-1-2-1-2-3-3l-1 1 2 2-1 1-2-1v-1c-1 0-1-1-2 0-1 0-1 1-2 2h0c-1 1-2 2-2 3-1 3-1 5-2 8l-1 5c0 2 1 5 0 7-2 2-1 9-1 12 0 1 1 1 0 2v2 1c0 3 0 6 1 8l-1 1c1 1 2 1 2 1 1 1 2 1 2 2v1l3 2c1 1 2 2 2 3-1-1-3-2-4-3h-1v-1h-1c1 3 0 4 0 6v7h-1l-1-1-1 1s-1 1-1 2v-3c-1-5 0-10 0-14v-33-10c0-3-1-5 0-8l11-13c8-10 12-25 18-36h0z"></path><path d="M533 290v-6c0-3 2-5 4-7h1c0-2 1-4 3-5h1v-1l1-1 1 2c0 2 3 4 4 6h-1-1c-1-2-1-2-3-3l-1 1 2 2-1 1-2-1v-1c-1 0-1-1-2 0-1 0-1 1-2 2h0c-1 1-2 2-2 3-1 3-1 5-2 8z" class="T"></path><path d="M540 337c0-1-1-2-2-3l-3-2v-1c0-1-1-1-2-2 0 0-1 0-2-1l1-1 1 1c4 4 9 8 14 12h2v-2l1 1v4 72-3l-1 1h-1-1c-3 0-7-1-10-3l-2 1-2-1h-3c-1 0-1 1-2 1v-1-1l6-3h1l-6-11v-2c1-3 0-7 0-11l1-20-1-1 1-16v3c0-1 1-2 1-2l1-1 1 1h1v-7c0-2 1-3 0-6h1v1h1c1 1 3 2 4 3z" class="Y"></path><path d="M537 410h-2c1 0 2-1 3 0s2 1 3 1 1 0 2 1h2 1l-1-1c-1 0-2 0-2-1 1 0 2 1 4 1 1-2 0-4 1-6 0 0 1-2 2-2v9l-1 1h-1-1c-3 0-7-1-10-3z" class="I"></path><path d="M535 383l1 1-2 2v4l1 1 1-1v1h-2c1 1 0 1 1 1 1 1 2 2 3 2 1 2 4 1 6 1l1 1 2-1v-1h0v-2c0-2-1-3 0-4v-5c1 1 2 2 2 5l-2 1c1 1 2 1 2 3l-1 1v2l-1 1 1 2h-2 0l-1 1c2 0 2 0 3 2-1 1-1 1-3 2 1 1 2 1 2 2l-1 1c-3-3-7-3-11-4 3-1 6 0 8 1h1 0c-1-2-2-2-4-2-3-2-3-3-5-6l-3-3 1-2v-5l2-2z" class="N"></path><path d="M535 383l-2-1c1-4-1-8 0-12h1l2 2c2 0 3 1 4 1 0 0 1 0 1 1 1 1 3 2 4 4v1c1 1 2 2 2 4v5c-1 1 0 2 0 4v2h0v1l-2 1-1-1c-2 0-5 1-6-1-1 0-2-1-3-2-1 0 0 0-1-1h2v-1l-1 1-1-1v-4l2-2-1-1z" class="C"></path><path d="M534 370l2 2c1 1 2 1 3 2 2 1 7 6 7 9 1 1 0 1 0 2v1 2h-1c0 1-1 1-2 2h1v1h-2v-2l-1-1s-1-1-2-1v-2c-1-1-1-1-1-2l1-1c-2-2-2-4-3-6-2-2-2-4-2-6z" class="E"></path><path d="M541 388v-1h2 1v1l-1 1v1h1v1h-2v-2l-1-1z" class="M"></path><defs><linearGradient id="u" x1="546.34" y1="342.226" x2="536.037" y2="351.61" xlink:href="#B"><stop offset="0" stop-color="#434241"></stop><stop offset="1" stop-color="#5a5a58"></stop></linearGradient></defs><path fill="url(#u)" d="M531 346l1-1 1 1h1v-7c0-2 1-3 0-6h1v1h1c1 1 3 2 4 3 3 2 6 4 8 6 1 1 1 1 1 3v34h-1v-1l-1-1-3-3-7-6-4-4-1-2-1-1h-1l-1-1 1-16v3c0-1 1-2 1-2z"></path><path d="M530 345v3c0-1 1-2 1-2 0 6 0 12 1 17l-1-1h-1l-1-1 1-16z" class="C"></path><path d="M548 343c1 1 1 1 1 3v34h-1v-1-1c1-3 1-8 0-11 0-1-1-1 0-2v-1c0-1-1-2-1-3 0-3 0-8-1-11 0-1-2-2-3-3 1 0 2 1 4 1h1v-2h0v-3z" class="N"></path><path d="M533 365v-10-7l7 8c2 3 7 8 7 12l-1 2h0c1 3 1 5 1 8l-3-3-7-6-4-4z" class="i"></path><path d="M536 356c2 0 2 0 3 1 0 2 2 5 4 7v1c-2 0-2-2-4-3h-1c-2-2-2-4-2-6zm1 13c0-2-1-3-1-5 0-1 1-1 1-1 2 1 4 4 6 5 0 1 1 2 1 4 0 0 0 1 1 1l-1 2-7-6z" class="H"></path><path d="M526 261l2-2 1 1 1 1c0 2-2 3-3 5 0 1 0 3 1 4 0 1-1 2-2 2h1c0 1 1 2 1 3l1 1h0c0 1 1 3 1 4-1 3 0 5 0 8v10 33c0 4-1 9 0 14l-1 16 1 1-1 20c0 4 1 8 0 11v2l6 11h-1l-6 3v1l-2 1h0l-1 1c-1 2-2 2-4 2-2-1-3-2-3-4l1-1c0-1 1-1 2-2h-1c0-1-1-2-2-3v-1l-1-1c-2 1-3 2-5 2v-3l2-1h1c0-3 0-4-1-6v-8l1-1c2 0 4 1 5 0l-1-2h-1l-2-2c-1-1-1-2-2-3l-1-1c1-1 0-3 0-4v-2-1c0-2 1-3 0-4v-2l1-1c0-1 0-3-1-4h0c1-2 1-3 1-4-1-1-1-4-1-6l2-1c-1 0-2-1-3-2 1 0 1-1 1-2h0v-3l-1-1c1-2 1-5 1-7l2 1c0-1 0-2-1-3l1-1 1-1v-1c2 0 2-2 3-4 1 0 3-1 4-1 0-3-2-5-3-8 0-2-1-4-1-6h0c-1-1-1-2-1-3 0-2 0-3-1-5v-1h0 1c0 1 0 2 1 3h0v-2c-1-1-2-2-2-4 0-1 0-1-1-3h0l1-2c-1-1-1-5-1-6-1-8-2-15-1-23l2-2 2-1h0c-1 2-1 3-2 4 0 2 1 5 2 8v-3c1-1 1-2 1-3v-1l2-2h1l3-2z" class="l"></path><path d="M526 261l2-2 1 1 1 1c0 2-2 3-3 5l-1 1c-1 2 1 3-1 4 1 2 3 5 3 7-1 1-1 1-1 2-1-1-1-1-2-1-1-1 0-2-1-2h-1v2c1 2 1 5 1 8h0l-2-2c0-3 0-5-1-8l-2-2v-3-3c1-1 1-2 1-3v-1l2-2h1l3-2z" class="K"></path><path d="M522 263h1l3-2-2 3-1 3c0 1 1 3 1 4h0l-2-2c-1 2-1 3-1 6v2l-2-2v-3-3c1-1 1-2 1-3v-1l2-2z" class="g"></path><path d="M526 261l2-2 1 1 1 1c0 2-2 3-3 5l-1 1c-1 2 1 3-1 4h-1c0-1-1-3-1-4l1-3 2-3z" class="H"></path><path d="M526 261l2-2 1 1c-1 2-2 5-4 6-1-1-1-1-1-2l2-3z" class="k"></path><defs><linearGradient id="v" x1="518.945" y1="336.269" x2="525.501" y2="338.259" xlink:href="#B"><stop offset="0" stop-color="#979381"></stop><stop offset="1" stop-color="#b5aa97"></stop></linearGradient></defs><path fill="url(#v)" d="M523 323c1 1 2 2 2 3 0 3 1 8 0 11-1 4-1 9-1 13l-1-1h-4-1c-1 0-2-1-3-1s-2-1-3-2c1 0 1-1 1-2h0v-3l-1-1c1-2 1-5 1-7l2 1c0-1 0-2-1-3l1-1 1-1v-1c2 0 2-2 3-4 1 0 3-1 4-1z"></path><path d="M523 323c1 1 2 2 2 3 0 3 1 8 0 11-1-3 0-9-2-11h-1c-2 1-2 2-2 4h-2-3l1-1v-1c2 0 2-2 3-4 1 0 3-1 4-1z" class="V"></path><path d="M520 330h1v1c-1 2 0 3 0 5l1 1h0v3h-1v-2c-1 0-1-1-1-1l-1-1h-1l3 6h0c-2-1-5-5-6-8 0-1 0-2-1-3l1-1h3 2z" class="K"></path><path d="M513 333l2 1c1 3 4 7 6 8h0c2 2 2 5 2 7h-4-1c-1 0-2-1-3-1s-2-1-3-2c1 0 1-1 1-2h0v-3l-1-1c1-2 1-5 1-7z" class="W"></path><path d="M519 344h1c1 1 1 2 1 4h-3c-1-1-1 0-2-1l1-1c0-1 1-1 2-2z" class="k"></path><path d="M515 348c1 0 2 1 3 1h1 4l1 1v36c-1-1-2-1-3-1l-1-4c0-5 0-15-4-18l-1 1 1 1c1 1 1 1 1 3 1 1 0 2 1 4h-2 0-1l-2 1v-2-1c0-2 1-3 0-4v-2l1-1c0-1 0-3-1-4h0c1-2 1-3 1-4-1-1-1-4-1-6l2-1z" class="Z"></path><path d="M517 261l2-1h0c-1 2-1 3-2 4 0 2 1 5 2 8v3l2 2c1 3 1 5 1 8 1 1 1 2 1 3v2 2h1v-3h1v7c0 4 1 9 1 14l-1 11v5c0-1-1-2-2-3 0-3-2-5-3-8 0-2-1-4-1-6h0c-1-1-1-2-1-3 0-2 0-3-1-5v-1h0 1c0 1 0 2 1 3h0v-2c-1-1-2-2-2-4 0-1 0-1-1-3h0l1-2c-1-1-1-5-1-6-1-8-2-15-1-23l2-2z" class="F"></path><path d="M517 261l2-1h0c-1 2-1 3-2 4 0 2 1 5 2 8v3 1c0 1 0 2-1 4h2v1c-1 1-1 1-1 2s0 2 1 2v4c1 0 1 1 1 2l-1 1c-2-1-1-1-2-1l1-1-1-2v-3c-1-2-1-3-1-5v-1c0-3 0-5-1-8 0-3 1-5-1-8l2-2z" class="V"></path><path d="M519 275l2 2c1 3 1 5 1 8 1 1 1 2 1 3v2 2h1v-3h1v7h-1v3l-1-1v-3c0-1-1-1-1-1l-2-2 1-1c0-1 0-2-1-2v-4c-1 0-1-1-1-2s0-1 1-2v-1h-2c1-2 1-3 1-4v-1z" class="R"></path><path d="M525 321c0-3 1-7-1-9l-2-4-1-1 1-1c0-1 0-1-1-2l1-1c-1-1 0-1-1-2h0l1-1-1-1c-1-1-1-1-1-2h0 1v-1l-2-2v-1l2 1h1s1 0 1 1v3l1 1v-3h1c0 4 1 9 1 14l-1 11z" class="L"></path><path d="M525 271c2-1 0-2 1-4l1-1c0 1 0 3 1 4 0 1-1 2-2 2h1c0 1 1 2 1 3l1 1h0c0 1 1 3 1 4-1 3 0 5 0 8v10 33c0 4-1 9 0 14l-1 16 1 1-1 20c0 4 1 8 0 11-2-1-3-3-5-4v-2h1c0 1 0 1 1 1v-1h2v-1l-3-1 1-1c-1-8 0-16-1-23-1-12 2-26 2-38l1-1v-10h0l-1-1v1c0-3-1-7 0-11v-1h0l-1 1v-11c0-2 0-1 1-2-1-3-1-6 0-8 0-1 0-1 1-2 0-2-2-5-3-7z" class="J"></path><path d="M525 361c1-2 1-4 1-6 0 7-1 14 0 20l1 10 1 1c1-1 0-1 1-2v-1-8-2h0c-1-2-1-3 0-4v-1c-1-1-1-3-1-4v-9-1l1-6v13l1 1-1 20c0 4 1 8 0 11-2-1-3-3-5-4v-2h1c0 1 0 1 1 1v-1h2v-1l-3-1 1-1c-1-8 0-16-1-23z" class="X"></path><path d="M518 372c-1-2 0-3-1-4 0-2 0-2-1-3l-1-1 1-1c4 3 4 13 4 18l1 4c1 0 2 0 3 1l1-1 3 1v1h-2v1c-1 0-1 0-1-1h-1v2c2 1 3 3 5 4v2l6 11h-1l-6 3v1l-2 1h0l-1 1c-1 2-2 2-4 2-2-1-3-2-3-4l1-1c0-1 1-1 2-2h-1c0-1-1-2-2-3v-1l-1-1c-2 1-3 2-5 2v-3l2-1h1c0-3 0-4-1-6v-8l1-1c2 0 4 1 5 0l-1-2h-1l-2-2c-1-1-1-2-2-3l-1-1c1-1 0-3 0-4l2-1h1 0 2z" class="Q"></path><path d="M518 372c0 3 1 7 1 11h-1l-2-2c-1-1-1-2-2-3l-1-1c1-1 0-3 0-4l2-1h1 0 2z" class="X"></path><path d="M517 401c-1 0-1 0-1-1 0-2-1-12 0-14 1 0 2 0 3 1l1 10v1c-1 2-1 4-2 5l-1-1v-1z" class="Z"></path><path d="M517 401v-3c1-2 1-1 2-2l1 2c-1 2-1 4-2 5l-1-1v-1z" class="U"></path><path d="M524 389c2 1 3 3 5 4v2c-1-1-2-1-3-1-1 2 0 4 1 6v1l1 1c-3 2-5 4-7 5h-1c0-1-1-2-2-3v-1c1-1 1-3 2-5v-1h3c1 1 0 1 1 1v-1h-1c-2-2-2-2-2-4 2-2 1-1 4-1v-1h-1v-2z" class="J"></path><path d="M521 407c2-1 4-3 7-5l-1-1v-1c-1-2-2-4-1-6 1 0 2 0 3 1l6 11h-1l-6 3v1l-2 1h0l-1 1c-1 2-2 2-4 2-2-1-3-2-3-4l1-1c0-1 1-1 2-2z" class="e"></path><path d="M441 453v-97c0-21 1-42-2-62-1-12-4-23-8-33-8-16-20-27-36-33-3 1-5 4-7 6l2-7-5-1c-13-2-27 0-37 7-4 2-7 6-10 8h-1v-2c4-11 11-22 17-32 1-2 3-5 5-7 1-1 5-2 7-3-2 3-3 4-5 7-3 4-7 10-8 16 1-1 0-1 1-1s2 0 2-1c4-2 7-5 10-7 6-4 13-7 20-7 2-1 4-1 5 0v1h-2l1 1h1c4-1 8 0 12 0 2 0 5 0 7 1h4l1-1 5 2c1 1 3 1 5 2v1c1 0 2 0 2 1 2 1 4 2 5 3 2 0 2 1 3 2 0 0 1 0 1 1h1v1c12 8 20 28 22 42v1l2 11c0 3 1 5 0 8l1 2s2 0 2 1h2s1-1 2-1c1-1 0 0 2-1 1 0 1 0 2 1l1 1h0 1v1c1 1 1 1 2 1v1l1 2h0c1 2 2 4 1 6v3c1 1 1 1 1 2 0 2 0 1 1 2 0 1 0 4-1 5h0c2 2 2 2 1 5l1 1-2 2-3 2-4 4h0l-1 2c-3 1-4 3-5 5 0 3 0 6-1 9 0 3 1 6 0 10v2s1 3 1 4l-1 1c0 1 1 1 1 1 0 1-1 1 0 3v6l-3 1h-2v19c0 4 1 10 0 14v1l1-1 1 1h1l2-3h1l2 1c-1 2-1 1-1 4l-2 3h2l-2 2c-1 2 0 3 0 5v1l-1 1c-1 1-2 2-4 3-2 2-6 8-8 8-1 2-1 1 0 3-1 2-1 5-1 8v11c-1 2-2 2-3 3h-1c-2 2-4 3-7 4z" class="n"></path><path d="M448 254c1 2 2 3 2 4l1 5h1l4-1c1 0 2-1 3-1v1l-7 2c1 7 2 13 2 20 0 2-1 7 0 9 0 2 0 3-1 5-1-2 0-5 0-7l-1-13c-1-8-2-16-4-24z" class="R"></path><path d="M459 262l2 11c0 3 1 5 0 8h-3 0l-3 1v-1 4 2 6h-1c-1-2 0-7 0-9 0-7-1-13-2-20l7-2z" class="b"></path><path d="M391 206c4-1 8 0 12 0 2 0 5 0 7 1h4l1-1 5 2c1 1 3 1 5 2v1c1 0 2 0 2 1 2 1 4 2 5 3 2 0 2 1 3 2 0 0 1 0 1 1h1v1c-5-2-10-4-16-5h0c8 5 14 11 19 19l-2 1c-8-11-16-18-29-23-1 0-3 2-4 3 1-2 1-2 2-3h0c-6-2-11-4-16-5z" class="D"></path><path d="M403 206c2 0 5 0 7 1h4l1-1 5 2c1 1 3 1 5 2v1c1 0 2 0 2 1h-2l-4-1s-2 0-2-1h-1c-2 0-2 0-3 1l2 1h-3c0-1-1-1-2-1-1-1-2-1-2-1-1 0-2-1-3-1h-1l-3-3z" class="a"></path><path d="M440 233c-5-8-11-14-19-19h0c6 1 11 3 16 5 12 8 20 28 22 42-1 0-2 1-3 1l-4 1h-1l-1-5c0-1-1-2-2-4-3-7-5-14-10-20l2-1z" class="e"></path><path d="M440 233l2 2v1c3 3 4 7 6 11l2 5c1 2 4 7 3 10h-1c-1-1 0-3-1-5-1 1 0 1-1 1 0-1-1-2-2-4-3-7-5-14-10-20l2-1z" class="h"></path><path d="M454 293h1v-6-2-4 1l3-1h0 3l1 2s2 0 2 1h2s1-1 2-1c1-1 0 0 2-1 1 0 1 0 2 1l1 1h0 1v1c1 1 1 1 2 1v1l1 2h0c1 2 2 4 1 6v3c1 1 1 1 1 2 0 2 0 1 1 2 0 1 0 4-1 5h0c2 2 2 2 1 5l1 1-2 2-3 2-4 4h0l-1 2c-3 1-4 3-5 5 0 3 0 6-1 9 0 3 1 6 0 10v2s1 3 1 4l-1 1c0 1 1 1 1 1 0 1-1 1 0 3v6l-3 1h-2v19c0 4 1 10 0 14v1l1-1 1 1h1l2-3h1l2 1c-1 2-1 1-1 4l-2 3h2l-2 2c-1 2 0 3 0 5v1l-1 1c-1 1-2 2-4 3-2 2-6 8-8 8-1-2-1-6-1-9 1-5 1-10 1-15v-35-8-2c0-1 0-1 1-2h-1c1-7 0-13 0-19v-36c1-2 1-3 1-5z" class="j"></path><path d="M466 396h1l2 1c-1 2-1 1-1 4l-2 3h2l-2 2c-1 2 0 3 0 5v1l-1 1c-1 1-2 2-4 3 1-6 0-12 0-17l1-1 1 1h1l2-3z" class="N"></path><path d="M466 396h1l2 1c-1 2-1 1-1 4l-2 3h2l-2 2c-1 2 0 3 0 5v1l-1 1v-5c0-3 0-5 1-7v-5z" class="E"></path><defs><linearGradient id="w" x1="456.903" y1="343.897" x2="469.449" y2="348.051" xlink:href="#B"><stop offset="0" stop-color="#383438"></stop><stop offset="1" stop-color="#51534e"></stop></linearGradient></defs><path fill="url(#w)" d="M472 321h0l-1 2c-3 1-4 3-5 5 0 3 0 6-1 9 0 3 1 6 0 10v2s1 3 1 4l-1 1c0 1 1 1 1 1 0 1-1 1 0 3v6l-3 1h-2v-11c0-2 1-4 0-5h-2l1-1c2-1 1-4 1-6v-16c3 0 5-1 6-3l2-1 3-1z"></path><path d="M470 282c1 0 1 0 2 1l1 1h0 1v1c1 1 1 1 2 1v1l1 2h0c1 2 2 4 1 6v3c1 1 1 1 1 2 0 2 0 1 1 2 0 1 0 4-1 5h0c2 2 2 2 1 5l1 1-2 2-3 2-4 4-3 1-2 1c-1 2-3 3-6 3l1-43s2 0 2 1h2s1-1 2-1c1-1 0 0 2-1z" class="V"></path><path d="M469 313l1-1c-1-2-1-3-1-4 1-1 2-2 2-3v-1h2c1-1 2-1 3-2 1 1 0 2 0 3l1 1-1 1h-2l-3 3h1 2 0l-1 1c-1 1-3 2-3 4v1l-1-3z" class="L"></path><path d="M470 316v-1c0-2 2-3 3-4l1-1h0-2-1l3-3h2v1c1 1 1 1 2 3v1c0 1 0 2 1 3l-3 2-4 4-3 1c0-1-1-3-1-4l2-2z" class="c"></path><path d="M476 308c1 1 1 1 2 3v1c0 1 0 2 1 3l-3 2v-2h-1l1-5c-1 0 0 0-1-1l1-1z" class="Q"></path><path d="M473 284h1v1c1 1 1 1 2 1v1l1 2h0c1 2 2 4 1 6v3c1 1 1 1 1 2 0 2 0 1 1 2 0 1 0 4-1 5h0c2 2 2 2 1 5l-1-2v-1c-1-1-1-3 0-4v-1c-1 0-1-1-1-2 0 0 0-1-1-1h-1l1-1-1-1h-1c0-1 1-2 2-3h-2-1l1-1-1-1-1 1-1-1h-1l-4 4c0-2-1-2-1-4l1-1-1-1h0c0-1 1-2 2-3h-1c-1 1-1 1-2 0 1-1 3-2 5-3l3-2z" class="F"></path><path d="M468 289c1 0 3-1 4-1h0c0 1-1 2-1 2h0 4v2h2c-1 2-2 2-2 4h-1l1-1-1-1-1 1-1-1h-1l-4 4c0-2-1-2-1-4l1-1-1-1h0c0-1 1-2 2-3z" class="M"></path><defs><linearGradient id="x" x1="457.515" y1="303.375" x2="468.765" y2="306.048" xlink:href="#B"><stop offset="0" stop-color="#373437"></stop><stop offset="1" stop-color="#4a4b47"></stop></linearGradient></defs><path fill="url(#x)" d="M470 282c1 0 1 0 2 1l1 1h0l-3 2c-2 1-4 2-5 3 1 1 1 1 2 0h1c-1 1-2 2-2 3h0l1 1-1 1c0 2 1 2 1 4-2 3-1 8-1 12-1 2-1 3 1 5l2-2 1 3-2 2c0 1 1 3 1 4l-2 1c-1 2-3 3-6 3l1-43s2 0 2 1h2s1-1 2-1c1-1 0 0 2-1z"></path><path d="M469 313l1 3-2 2c0 1 1 3 1 4l-2 1c-1-2-1-5-1-7l1-1 2-2z" class="p"></path><path d="M503 725h1v4c2 1 4 1 5 1h1 2 0 7 1 2v-1l1-1 1 1v1 1c1 0 2 1 2 1h9v1c-2 0-5-1-7 0 1 1 1 2 2 3h4l2 2v-1l3 1h0l2 5 5 16 1 6c1-1 2-1 3-1 8 3 11 13 15 19h0l2 3v-1 3h1 1l1-1v1l-1 1h1v2l1-2h1c2 2 2 3 5 3 3 3 5 6 7 8 5 4 9 7 14 10l-4 1c-3 0-6 0-9 1l-19 6s-1 1-2 1h0l-2 2s-1 1-2 1v2 1l1 2v2c-2-1-4-4-6-5-3 2-7 4-10 7-3 1-5 3-7 5-1 0-2 2-3 2h-1-1-2c-4 3-7 7-10 10l-2 2h-1l-3 3h-2l-1-1-1-1h-1c-2-2 0 0-2-1l1-1v-2c0 1 0 1-1 2-1-1-1-2-1-3h-1c0 1 0 1-1 2-1-1-1-3-1-5v-2c0-1 0-1-1-3v-6c0-2 1-2 2-3-1-4-2-8-2-11-1-2 0-6-1-7 0-1-1-1-1-1l1-1-1-1-1-1c-8-1-15-6-22-12l-4-6c-2-4-4-9-4-13-1-7 0-12 1-18 1-4 2-8 4-12l1-3 2-4h2c-1-2 0-4 1-5-1-1-1-2-1-3h-1c1-1 3-1 4-1h4 11l4-1h1v-4z" class="C"></path><path d="M563 815h2l-1 1c-1 1-1 1-3 1l-1-1 3-1z" class="N"></path><path d="M568 811h3c-1 2-2 2-4 3-1 0-2 1-2 1h-2l1-2c1-1 3-1 4-2z" class="E"></path><path d="M539 823l3-1h0c-1 2-4 4-6 5l-3 2v-1c1-2 4-4 6-5z" class="D"></path><path d="M564 813l-1 2-3 1c-1 0-3 0-4 1h-1v-3h1 4l4-1z" class="G"></path><path d="M580 805c1 0 1 0 1 1l-3 3s-1 0-1 1c-2 0-4 1-6 1h-3v-1-1h2 1c2-1 3 0 5 0l4-4z" class="P"></path><path d="M576 801c-1 2-1 3-2 5-4 2-10 1-14 1 2-2 4-1 7-2 2 0 3-1 5-1 1 0 1-1 2-1l-1-1c1-1 2-1 3-1zm5 5l1 2c2 2 3 1 3 4l-19 6v-1l12-4 1-1h0c-2-1-2 0-4 0v-1h1l1-1c0-1 1-1 1-1l3-3z" class="T"></path><path d="M581 806l1 2v1c-1 1-2 1-3 1s-2 0-3 1l1-1c0-1 1-1 1-1l3-3z" class="D"></path><path d="M524 829v-1c3-1 5-4 8-5 3-2 8-7 12-7h4c1 1 1 1 1 2l-2-1-2 2c-3 0-5 1-7 3h-1-2c-2 2-5 4-8 6-1 0-2 2-4 3s-3 3-5 4c0 1-1 2 0 3l-1 1v-2c0-1 0-2 1-3h1c1-2 3-4 5-5z" class="P"></path><path d="M518 838c-1-1 0-2 0-3 2-1 3-3 5-4s3-3 4-3c3-2 6-4 8-6h2l2 1c-2 1-5 3-6 5-2 1-3 2-4 2l-1 1-6 3c-2 1-3 2-4 4h0z" class="E"></path><path d="M529 814c2 1 3 0 6 0 3-1 7 0 10-2l1 1 1-1 2 1v1c-2 1-5 1-8 1-3 1-6 3-9 4-2 1-4 3-6 4 0 1-2 2-2 3-1 0-1 0-1-1 0 0 0-1 1-2s2-1 3-2 2-1 3-2c1 0 2-1 3-2 2 0 4-1 5-2-2 0-4 1-6 1-2 1-3 1-4 1l-1-2 2-1z" class="N"></path><path d="M519 826c1 0 2-1 3-1 0 1-1 1-2 2 0 1-1 2-1 2l-1 1c0 1-1 2-1 3 1-1 4-5 6-5l1 1c-2 1-4 3-5 5h-1c-1 1-1 2-1 3v2 2c0 3 3 5 4 7l-2 2h-1l-3 3h-2l-1-1c1-2 4-4 6-5h1c-1-1-1 0-1-1l-1-1c-1-2-3-3-4-5s-1-4 0-5l6-9z" class="E"></path><path d="M562 798v-2l4-1c0-1 1-2 2-3 1 1 2 1 2 2l1 2h-1l-1-1h-1v1c1 1 0 1 1 1s2 1 3 1l1 2 1 1c1-1 1-1 3-1l-1 1c-1 0-2 0-3 1l1 1c-1 0-1 1-2 1-2 0-3 1-5 1-3 1-5 0-7 2-1 0-3 0-4-1l7-7-1-1z" class="N"></path><path d="M568 799c2 0 3 0 4 1h1l1 1c1-1 1-1 3-1l-1 1c-1 0-2 0-3 1l-2 1-3-3v-1z" class="I"></path><path d="M571 803c-3 0-5-1-8-2l2-2c1-1 2-1 3 0v1l3 3z" class="B"></path><path d="M570 791l1-2h1c2 2 2 3 5 3 3 3 5 6 7 8 5 4 9 7 14 10l-4 1c-3 0-6 0-9 1 0-3-1-2-3-4l-1-2c0-1 0-1-1-1-1-2-2-3-3-5-2 0-2 0-3 1l-1-1-1-2c-1 0-2-1-3-1s0 0-1-1v-1h1l1 1h1l-1-2v-3z" class="O"></path><path d="M569 788l1-1v1l-1 1h1v2 3c0-1-1-1-2-2-1 1-2 2-2 3l-4 1v2s-1 0-2 1c-1 0-2 1-3 2l-7 8c4-1 10-1 14-1h3v1l-1 1h-17c-3-1-7 1-9 2h-5c-2 1-4 2-6 2l-2 1 1 2c-2 0-3 1-5 3-1 1-3 3-4 5v1l-6 9-1-1h-1v-3c2-3 2-5 4-8 1-3 1-4 3-6 0-1 0-1 1-2h0c0-2 2-3 3-5l-1-2 7-3h2l-1 1c-1 1-1 1-1 2h2 2l1-1h2 6v1l-1 1h-5v1c2 0 6 1 7-1h4c2-1 4-2 6-4s5-5 7-8l10-8v-1z" class="T"></path><path d="M535 807h6v1h-6c-2 1-4 2-6 2h-1v-2h2 2l1-1h2z" class="D"></path><path d="M515 823l3-3h1v1 1c-4 4-5 8-7 12h-1v-3c2-3 2-5 4-8z" class="N"></path><path d="M528 805h2l-1 1c-1 1-1 1-1 2v2c-1-1-2-1-4-1-2 3-4 6-6 8 0-1 0-1 1-2h0c0-2 2-3 3-5l-1-2 7-3z" class="Y"></path><path d="M545 825c1-1 1-2 2-3 2-1 3-3 5-3 1 0 2 1 3 0h1c3 2 5-1 8 0h0l-2 2s-1 1-2 1v2 1l1 2v2c-2-1-4-4-6-5-3 2-7 4-10 7-3 1-5 3-7 5-1 0-2 2-3 2h-1-1-2c-4 3-7 7-10 10-1-2-4-4-4-7v-2l1-1h0c1-2 2-3 4-4l6-3 1-1c1 0 2-1 4-2v1l3-2c3 0 5-1 7-3l2 1z" class="N"></path><path d="M530 832h4c-2 1-4 2-5 4h-1c-1 1-4 0-5 0l7-4z" class="F"></path><path d="M555 824l9-5-2 2s-1 1-2 1v2 1l1 2v2c-2-1-4-4-6-5z" class="Z"></path><path d="M536 827c3 0 5-1 7-3l2 1-11 7h-4l3-3 3-2z" class="E"></path><path d="M533 828v1l-3 3-7 4c-2 1-4 2-5 3v1l-1 1v-2l1-1h0c1-2 2-3 4-4l6-3 1-1c1 0 2-1 4-2z" class="Y"></path><path d="M547 765c1-1 2-1 3-1 8 3 11 13 15 19h0l2 3v-1 3h1 1v1l-10 8c-2 3-5 6-7 8s-4 3-6 4h-4c-1 2-5 1-7 1v-1h5l1-1v-1h-6-2l-1 1h-2-2c0-1 0-1 1-2l1-1h-2c5-4 8-7 12-12 5-8 7-17 7-28z" class="N"></path><path d="M555 780c3 1 5 4 8 5l-2 2c-2-2-4-5-6-7zm-13 29c1-1 2-1 3-2 1-2 2-3 3-4h1c0 2-1 3-2 4 1 0 2-1 3-2h1 1c-2 2-4 3-6 4h-4z" class="C"></path><path d="M554 778l1 2c2 2 4 5 6 7h1v1l-1 1h-1-1v-1c-2-3-5-5-6-8l-1-1 2-1z" class="B"></path><path d="M550 764c8 3 11 13 15 19h0l2 3h-2l-1-1c-2-3-4-5-6-8 0 0 0-1-1-2-2-3-5-7-7-9v-2z" class="m"></path><path d="M561 789h2v-2h2c0 2-1 2-2 3 0 0-1 1-2 1s-1 1-2 1v1h-1-1l-2 1c0 1 1 1 1 2 0 0-3 3-4 3l-2 2c-1 1-2 1-3 1v-1c-1 0-1 0-2-1h-1c-2-1-3-1-5 0v-1l1-1h5 1 1c3-1 5-5 7-6 1 0 1 0 1-1 2-1 3-2 4-2h1 1z" class="D"></path><path d="M528 805c5-4 8-7 12-12 0 1-1 2 0 3 1 0 2 1 4 0l1-1v2s1 0 1 1h-1-5l-1 1v1c2-1 3-1 5 0h1c-1 2-1 2-2 3l-1-1v1h0 1v1c-1 1-1 1-2 1v2h-6-2l-1 1h-2-2c0-1 0-1 1-2l1-1h-2z" class="I"></path><path d="M535 807c0-1 0-1 1-1v-1c1 0 1-1 2-1 1 1 1 1 3 1h0v2h-6z" class="B"></path><path d="M528 805c5-4 8-7 12-12 0 1-1 2 0 3 1 0 2 1 4 0l1-1v2s1 0 1 1h-1-5l-1 1v1h-1-1l1 2v1c-1-1-2-1-2-1-2 0-2 2-4 3-1 1-1 1-1 2l1-1 1 1-1 1h-2-2c0-1 0-1 1-2l1-1h-2z" class="T"></path><defs><linearGradient id="y" x1="538.168" y1="774.052" x2="553.396" y2="788.93" xlink:href="#B"><stop offset="0" stop-color="#1c1c1c"></stop><stop offset="1" stop-color="#3e3d3c"></stop></linearGradient></defs><path fill="url(#y)" d="M547 765c1-1 2-1 3-1v2c-1 3 0 5 1 8l3 4-2 1 1 1c1 3 4 5 6 8v1c-1 0-2 1-4 2 0 1 0 1-1 1-2 1-4 5-7 6h-1c0-1-1-1-1-1v-2l-1 1c-2 1-3 0-4 0-1-1 0-2 0-3 5-8 7-17 7-28z"></path><path d="M552 791c-1 0-1-1-2-1h-1l-1-1h0-1c0-3 1-5 2-7v-7h1c1 2 2 3 2 4l1 1c1 3 4 5 6 8v1c-1 0-2 1-4 2 0 1 0 1-1 1-2 1-4 5-7 6v-2c1-1 3-2 3-2 1-2 0-2 2-3z" class="N"></path><path d="M553 780c1 3 4 5 6 8v1c-1 0-2 1-4 2 0 1 0 1-1 1-2 1-4 5-7 6v-2c1-1 3-2 3-2 1-2 0-2 2-3 1 1 0 1 1 1 0-1-1-2-2-4 0-1 1 1 0-1h0l1-1 2 3v-1c0-1-2-4-2-6h0l-1-1 2-1z" class="C"></path><defs><linearGradient id="z" x1="514.667" y1="763.848" x2="540.402" y2="792.578" xlink:href="#B"><stop offset="0" stop-color="#949184"></stop><stop offset="1" stop-color="#b8b0a5"></stop></linearGradient></defs><path fill="url(#z)" d="M537 741c2 0 3 1 4 2l5 16 1 6c0 11-2 20-7 28-4 5-7 8-12 12l-7 3 1 2c-1 2-3 3-3 5h0c-1 1-1 1-1 2-2 2-2 3-3 6-2 3-2 5-4 8v3h1l1 1c-1 1-1 3 0 5s3 3 4 5l1 1c0 1 0 0 1 1h-1c-2 1-5 3-6 5l-1-1h-1c-2-2 0 0-2-1l1-1v-2c0 1 0 1-1 2-1-1-1-2-1-3h-1c0 1 0 1-1 2-1-1-1-3-1-5v-2c0-1 0-1-1-3v-6c0-2 1-2 2-3-1-4-2-8-2-11-1-2 0-6-1-7 0-1-1-1-1-1l1-1-1-1 2 2h1s1 1 2 1l2-2h-1-3l-1-1v-6c1-3 1-8 1-11v-1c2 0 4-1 6-1l-1-1 1-3c1 2 2 2 2 4h1l-1 7c0 2-1 9 0 11l9-2c3-1 5-3 8-4l3-3c5-4 8-10 10-16s2-12 2-19c-1-5-3-10-5-15l-2-7z"></path><path d="M510 789l-1-1 1-3c1 2 2 2 2 4h1l-1 7c0 2-1 9 0 11l-1 3h0c-1 1-1 3 0 4h-1v-1-1l-1 1-1-1-1 1h-1l-1-1v1c1 2 0 0 1 1l1 1h1c-1 1-2 1-4 0-1-1-1-3 0-5 0 0 1 1 2 1l2-2h-1-3l-1-1v-6c1-3 1-8 1-11v-1c2 0 4-1 6-1z" class="C"></path><path d="M505 804c1-1 2-1 3-1s2 0 2 1l-1 1-1 1-3-2z" class="B"></path><path d="M510 789l1 1v1c-3 1-5 0-7 0v-1c2 0 4-1 6-1z" class="O"></path><path d="M504 809c0-1 0-1 1-2v-2h-1l1-1 3 2 1 1v2h-1-1-3z" class="I"></path><path d="M510 789l-1-1 1-3c1 2 2 2 2 4h1l-1 7c0 2-1 9 0 11l-1 3v-19-1l-1-1z" class="H"></path><defs><linearGradient id="AA" x1="516.312" y1="807.978" x2="511.566" y2="850.563" xlink:href="#B"><stop offset="0" stop-color="#1f1e1c"></stop><stop offset="1" stop-color="#454444"></stop></linearGradient></defs><path fill="url(#AA)" d="M507 830v-1h0 2c3-4 3-14 3-18l9-3 1 2c-1 2-3 3-3 5h0c-1 1-1 1-1 2-2 2-2 3-3 6-2 3-2 5-4 8v3h1l1 1c-1 1-1 3 0 5s3 3 4 5l1 1c0 1 0 0 1 1h-1c-2 1-5 3-6 5l-1-1h-1c-2-2 0 0-2-1l1-1v-2c0 1 0 1-1 2-1-1-1-2-1-3h-1c0 1 0 1-1 2-1-1-1-3-1-5v-2c0-1 0-1-1-3v-6c0-2 1-2 2-3l1 2 1-1z"></path><path d="M507 830l1 1v3h-3l1-3 1-1z" class="e"></path><path d="M505 834h3c0 4 0 9-1 12h-1l-1-12z" class="X"></path><path d="M503 838v-6c0-2 1-2 2-3l1 2-1 3 1 12c0 1 0 1-1 2-1-1-1-3-1-5v-2c0-1 0-1-1-3z" class="I"></path><path d="M539 748c2 5 4 10 5 15 0 7 0 13-2 19s-5 12-10 16l-3 3c-3 1-5 3-8 4l-9 2c-1-2 0-9 0-11l1-7c1-3 1-6 3-9 3-6 7-12 9-19 1-4 0-8 0-12h1c2 2 3 2 6 2 1 0 1-1 2 0 0 0 0 1 2 1 1 1 0 1 1 1l1 1 2-1v-1c-1-1-1-2-1-3v-1z" class="B"></path><path d="M526 799v-1l2-2 1 1c0 1 0 1-1 2l-1 1-1-1z" class="S"></path><path d="M521 790h2c1 1 1 2 2 3l-1 1v2l-2 1h-1c1-3 1-5 0-7z" class="C"></path><path d="M521 805c0-1 1-2 1-3l-1-2 1-1h4l1 1 2 1c-3 1-5 3-8 4z" class="T"></path><path d="M522 797l-1 2-1 1c-1 1-2 1-3 1l1-1v-1c-1 0-2-1-2-1v-1l1-1v-1l-1-2v-1c1 0 2 0 2 1 1 1 1 1 1 3 1 1 1 1 2 1h1z" class="D"></path><path d="M528 779v2h0l-1 1c1 4-2 7 1 12l-4 2v-2l1-1c-1-1-1-2-2-3h-2v-1h1 1c0-3 3-5 3-8l2-2z" class="N"></path><path d="M533 764v-1c1 0 1-1 2-2 1 0 0 0 1-1 1 0 1-1 3-1v1c-6 4-8 8-10 14l-2 2c0 2 0 3-1 4s-2 3-3 4c0-3 1-6 2-9h0l1-1c0-2 1-3 2-4l3-3 2-3z" class="D"></path><path d="M529 778l1 1 1 4c0 3 2 5 3 8l-1 1-5 2c-3-5 0-8-1-12l1-1h0v-2l1-1z" class="C"></path><path d="M535 768h1c0 1 0 2-1 4l1 2h0c0 2-1 3 0 4h0c1-1 1-2 3-2 1 0 1 1 2 2 0 2 0 3 1 4-2 6-5 12-10 16v-1l1-1v-4l1-1c-1-3-3-5-3-8l-1-4-1-1c0-1 0-1 1-2h0v-1c1-1 2-2 3-4l2-3z" class="P"></path><path d="M539 748c2 5 4 10 5 15 0 7 0 13-2 19-1-1-1-2-1-4-1-1-1-2-2-2-2 0-2 1-3 2h0c-1-1 0-2 0-4h0l-1-2c1-2 1-3 1-4h-1l-2 3c-1 1-2 2-4 3h0c2-6 4-10 10-14 0-1 1-1 2-2v-1h0v-2c-1 0-2 0-3 1l-2-1v-3c1 1 0 1 1 1l1 1 2-1v-1c-1-1-1-2-1-3v-1z" class="S"></path><path d="M535 768l3-3v1c0 1 0 2-1 3 0 2 0 3-1 5l-1-2c1-2 1-3 1-4h-1z" class="Y"></path><path d="M536 774v1c0 1 0 1 1 2v-1l1-1c1-3 1-5 3-7 1-2 2-2 2-4l-1-1h1 0v3l1-3c0 7 0 13-2 19-1-1-1-2-1-4-1-1-1-2-2-2-2 0-2 1-3 2h0c-1-1 0-2 0-4z" class="B"></path><defs><linearGradient id="AB" x1="517.938" y1="770.564" x2="536.398" y2="775.125" xlink:href="#B"><stop offset="0" stop-color="#151515"></stop><stop offset="1" stop-color="#434242"></stop></linearGradient></defs><path fill="url(#AB)" d="M525 749h1c2 2 3 2 6 2 1 0 1-1 2 0 0 0 0 1 2 1v3l2 1c1-1 2-1 3-1v2h0v1c-1 1-2 1-2 2v-1c-2 0-2 1-3 1-1 1 0 1-1 1-1 1-1 2-2 2v1c-2-1-2-1-4 0l-1 1 1 2c0 1-2 3-3 4l-2 1c0 1 0 1-1 2-4 5-9 15-8 22v4l1 1-2 2c-1-1 0-2-1-3 0-1 0-3-1-4l1-7c1-3 1-6 3-9 3-6 7-12 9-19 1-4 0-8 0-12z"></path><path d="M503 725h1v4c2 1 4 1 5 1h1 2 0 7 1 2v-1l1-1 1 1v1 1c1 0 2 1 2 1h9v1c-2 0-5-1-7 0 1 1 1 2 2 3h4l2 2v-1l3 1h0l2 5c-1-1-2-2-4-2l2 7v1c0 1 0 2 1 3v1l-2 1-1-1c-1 0 0 0-1-1-2 0-2-1-2-1-1-1-1 0-2 0-3 0-4 0-6-2h-1c0 4 1 8 0 12-2 7-6 13-9 19-2 3-2 6-3 9h-1c0-2-1-2-2-4l-1 3 1 1c-2 0-4 1-6 1v1c0 3 0 8-1 11v6l1 1h3 1l-2 2c-1 0-2-1-2-1h-1l-2-2-1-1c-8-1-15-6-22-12l-4-6c-2-4-4-9-4-13-1-7 0-12 1-18 1-4 2-8 4-12l1-3 2-4h2c-1-2 0-4 1-5-1-1-1-2-1-3h-1c1-1 3-1 4-1h4 11l4-1h1v-4z" class="O"></path><path d="M489 741l2 1c1 0 0 0 1-1h1v1 1h-1l-1 1s1 0 2 1v1l-1 1c1 2 0 7 0 9 0-1-1-2-1-3-2 2-2 3-2 5v1c-1-5 0-13 0-18z" class="W"></path><path d="M489 759v-1c0-2 0-3 2-5 0 1 1 2 1 3 0 5 2 10 5 14v3c1 3 3 5 2 7-4-6-9-13-10-21z" class="H"></path><path d="M497 770c2 4 6 9 7 14 0 1 0 2 1 3l1 2h-1c-1 0 0 0-1 1v1c0 3 0 8-1 11v6l1 1h3 1l-2 2c-1 0-2-1-2-1h-1l-2-2 1-1v-6c0-8 0-14-3-21 1-2-1-4-2-7v-3z" class="W"></path><path d="M475 746h0v4l1-2c3 1 5 1 8 2 0 4 1 9 2 13 0 2 2 5 2 7-2-2-2-3-4-5l-4-4v1 1c-1 0-4-5-6-6l-1 1h-2c1-4 2-8 4-12z" class="B"></path><path d="M492 756c0-2 1-7 0-9l1-1v10h1c1 1 1 2 2 2 4 1 7 2 11 1h7c-1 0-2 1-3 1l1 1v1c-1 1-1 1-1 2 0 2 0 2-2 4l-2 2h0 0l1 1c-1 0-1 1-2 1l-1-1-2 1v1 1l2 3h0c-1 1-1 0-1 1l1 1c2 2 2 4 2 8h1c1-2 1-5 3-6h0c0 1-1 3-1 4l-1 3 1 1c-2 0-4 1-6 1 1-1 0-1 1-1h1l-1-2c-1-1-1-2-1-3-1-5-5-10-7-14-3-4-5-9-5-14z" class="C"></path><path d="M501 762c2-2 7-1 10-2l1 1v1c-1 1-1 1-1 2 0 2 0 2-2 4l-2 2h0 0l1 1c-1 0-1 1-2 1l-1-1-2 1c-1-1-2-1-2-2-1-3-4-6-5-9 1 0 2-1 3 0 1 0 1 0 2 1h0z" class="G"></path><path d="M501 762c2-2 7-1 10-2l1 1v1c-4 0-7 2-11 0z" class="B"></path><path d="M521 746h1c0 1 1 1 2 1-1 2 0 3-1 4 0 1 1 2 1 2v7 1h1c-2 7-6 13-9 19-2 3-2 6-3 9h-1c0-2-1-2-2-4 0-1 1-3 1-4h0c-2 1-2 4-3 6h-1c0-4 0-6-2-8l-1-1c0-1 0 0 1-1h0l-2-3v-1-1l2-1 1 1c1 0 1-1 2-1l-1-1h0 0l2-2c2-2 2-2 2-4 0-1 0-1 1-2v-1l6-3c1 0 2 0 3-1v-11h0z" class="E"></path><path d="M511 781c3-5 8-12 10-18v-1c1-4 1-7 1-11h1c0 1 1 2 1 2v7 1h1c-2 7-6 13-9 19-2 3-2 6-3 9h-1c0-2-1-2-2-4 0-1 1-3 1-4z" class="W"></path><path d="M473 758l1-1c2 1 5 6 6 6v-1-1l4 4c2 2 2 3 4 5 4 7 8 13 10 20v8 5 1c1 0 1 1 1 1 1 1 1 2 1 2-8-1-15-6-22-12l-4-6c-2-4-4-9-4-13-1-7 0-12 1-18h2z" class="D"></path><path d="M483 793l1 2-1 1h-2l-1-1c1-1 2-1 3-2z" class="S"></path><path d="M483 793v-1c1 1 2 1 3 2-1 1 0 1 0 3h-1c-1-1-2-1-4-1h2l1-1-1-2z" class="Y"></path><path d="M486 797c0-2-1-2 0-3l5 5c1 1 2 1 3 2 2 1 3 2 4 4-3-1-5-2-7-4-1-1-1-1-3-1-1 0-2-2-2-3z" class="I"></path><path d="M498 803c-3-3-3-7-5-10l-4-9c-1-1-2-3-2-5l5 9c1 3 3 5 4 7v1c1 0 2 1 2 2v5z" class="S"></path><path d="M482 785h1v2c-1 1-1 2-1 3h-1v1c0 1 0 1-1 2 0 1-1 2-2 2l-4-6c1-1 2-2 4-2l1-1 1 1 1-2h1 0z" class="E"></path><path d="M478 787l1-1 1 1 1-2h1c-1 2-1 4-2 6h-1c-1-1-1-3-1-4z" class="T"></path><path d="M484 765c2 2 2 3 4 5 4 7 8 13 10 20v8c0-1-1-2-2-2v-1c-1-2-3-4-4-7 1-3-1-4-2-8 0 0-1-1-1-2-1-2-2-5-3-7 0-2-1-4-2-6z" class="P"></path><defs><linearGradient id="AC" x1="480.212" y1="768.008" x2="467.273" y2="777.597" xlink:href="#B"><stop offset="0" stop-color="#2c2a2a"></stop><stop offset="1" stop-color="#464543"></stop></linearGradient></defs><path fill="url(#AC)" d="M473 758c2 2 4 4 5 7 3 3 4 6 5 10 0 3-1 7-1 10h0-1l-1 2-1-1-1 1c-2 0-3 1-4 2-2-4-4-9-4-13-1-7 0-12 1-18h2z"></path><path d="M503 725h1v4c2 1 4 1 5 1h1 2 0 7 1 2v-1l1-1 1 1v1 1c1 0 2 1 2 1h9v1c-2 0-5-1-7 0 1 1 1 2 2 3h4l2 2v-1l3 1h0l2 5c-1-1-2-2-4-2l2 7v1c0 1 0 2 1 3v1l-2 1-1-1c-1 0 0 0-1-1-2 0-2-1-2-1-1-1-1 0-2 0-3 0-4 0-6-2h-1c0 4 1 8 0 12h-1v-1-7s-1-1-1-2c1-1 0-2 1-4-1 0-2 0-2-1h-1 0v11c-1 1-2 1-3 1l-6 3-1-1c1 0 2-1 3-1h-7c-4 1-7 0-11-1-1 0-1-1-2-2h-1v-10-1c-1-1-2-1-2-1l1-1h1v-1-1h-1c-1 1 0 1-1 1l-2-1-1-2h-3-5c-1-2 0-4 1-5-1-1-1-2-1-3h-1c1-1 3-1 4-1h4 11l4-1h1v-4z" class="m"></path><path d="M521 741c1 1 1 1 1 2v2h1l1-3h0c1 1 1 5 1 7 0 4 1 8 0 12h-1v-1-7s-1-1-1-2c1-1 0-2 1-4-1 0-2 0-2-1h-1v-5z" class="h"></path><path d="M508 739c1-1 2-2 2-3h2c1-1 1-1 2-1 2 1 5 1 7 1h2 7 4l2 2v-1l3 1h0l2 5c-1-1-2-2-4-2 0-1-1-1-1-2h-1-27z" class="R"></path><path d="M530 736h4l2 2v-1l3 1h0l2 5c-1-1-2-2-4-2 0-1-1-1-1-2v-1h-1c-2 0-2 0-4-1-1-1-1 0-2 0h-1-2c-1 0-2 0-3-1h7z" class="L"></path><path d="M509 732h17 9v1c-2 0-5-1-7 0 1 1 1 2 2 3h-7-2c-2 0-5 0-7-1-1 0-1 0-2 1h-2c0 1-1 2-2 3h-4c1-1 0-1 0-2h1v-4c1-1 3-1 4-1z" class="F"></path><path d="M505 737v-4c1-1 3-1 4-1l1 1c0 1-1 1 0 2l-2 2h-1c-1-1-1-1-2 0h0z" class="S"></path><path d="M536 739c0 1 1 1 1 2l2 7v1c0 1 0 2 1 3v1l-2 1-1-1c-1 0 0 0-1-1-2 0-2-1-2-1-1-1-1 0-2 0-3 0-4 0-6-2 0-2 0-4 2-5 1 0 1 0 2-1 3 0 4-1 5-4h1z" class="O"></path><path d="M503 725h1v4c2 1 4 1 5 1h1 2 0 7 1 2v-1l1-1 1 1v1 1c1 0 2 1 2 1h-17c-1 0-3 0-4 1v4h-1c0 1 1 1 0 2h-1-2l-1 1h0c-2 1-3 1-4 1-1 1-1 1-1 2l-2-1v-1h-1c-1 1 0 1-1 1l-2-1-1-2h-3-5c-1-2 0-4 1-5-1-1-1-2-1-3h-1c1-1 3-1 4-1h4 11l4-1h1v-4z" class="e"></path><path d="M480 731l24 1h1c1-1 3-1 4 0-1 0-3 0-4 1v4h-1v-2c-1 0-4-1-5 0h-1c-1-1-1-1-2-1h0c-3-1-7-1-10 0h-1c-2 0-3-1-4 0-1-1-1-2-1-3z" class="M"></path><path d="M481 734c1-1 2 0 4 0h1c3-1 7-1 10 0h0c1 0 1 0 2 1h1c1-1 4 0 5 0v2c0 1 1 1 0 2h-1-2l-1 1h0c-2 1-3 1-4 1-1 1-1 1-1 2l-2-1v-1h-1c-1 1 0 1-1 1l-2-1-1-2h-3-5c-1-2 0-4 1-5z" class="R"></path><path d="M488 739h13l-1 1h0c-2 1-3 1-4 1-1 1-1 1-1 2l-2-1v-1h-1c-1 1 0 1-1 1l-2-1-1-2z" class="I"></path><path d="M493 741s0-1 1-1h6 0c-2 1-3 1-4 1-1 1-1 1-1 2l-2-1v-1z" class="O"></path><path d="M481 734c1-1 2 0 4 0h1c1 0 2 0 3 1 0 2 0 2-1 3l-3-1v2h-5c-1-2 0-4 1-5z" class="K"></path><path d="M481 734c1-1 2 0 4 0h-1-2l-1 2v1h3 1v2h-5c-1-2 0-4 1-5z" class="c"></path><path d="M501 739h2v1 3 2l1 1c2 0 2 0 3 1 0 1 0 2 1 3h0l1 1h1l-1-1v-2h2c0 1-1 3 0 4 1-2 1-2 1-3v-1c0-1 0-2-1-3v-2-3c3-1 7 0 10 0v1 5h0v11c-1 1-2 1-3 1l-6 3-1-1c1 0 2-1 3-1h-7c-4 1-7 0-11-1-1 0-1-1-2-2h-1v-10-1c-1-1-2-1-2-1l1-1h1v-1l2 1c0-1 0-1 1-2 1 0 2 0 4-1h0l1-1z" class="n"></path><path d="M493 742l2 1c0 4 0 10-1 13h-1v-10-1c-1-1-2-1-2-1l1-1h1v-1z" class="I"></path><path d="M496 758l8-1h2c1 1 3 1 5 1h0l2-1c1 0 2 0 4-1 0 1 0 1 1 2l-6 3-1-1c1 0 2-1 3-1h-7c-4 1-7 0-11-1z" class="O"></path><path d="M483 312v13c1-2 1-5 1-7v-5l3 1v4 8h8v-2l-1-1 5 1h10c1 0 3-1 5 0 2-1 5-1 8-2l-3-5 1-2c1 3 3 5 3 8-1 0-3 1-4 1-1 2-1 4-3 4v1l-1 1-1 1c1 1 1 2 1 3l-2-1c0 2 0 5-1 7l1 1v3h0c0 1 0 2-1 2 1 1 2 2 3 2l-2 1c0 2 0 5 1 6 0 1 0 2-1 4h0c1 1 1 3 1 4l-1 1v2c1 1 0 2 0 4v1 2c0 1 1 3 0 4l1 1c1 1 1 2 2 3l2 2h1l1 2c-1 1-3 0-5 0l-1 1v8c1 2 1 3 1 6h-1l-2 1v3h-2-1c-1 2-1 3 0 5v28 30l-1 18-4 1c0 1 0 2-1 3l1 4-1 1h-1c-4 2-11 5-14 9s-4 10-5 15l-3-1c-3 0-8 0-10 2v1 5c-1-2-1-2-3-3l-1-1h-1-3l-9-1h-1l-1-1 1-10-1-2 1-2c-1-2-1-4-1-6l1-8 1 1 1-1v-2-8-7l-2-1 1-18h-1c-1 1-1 1-2 0-1-3 2-5 2-8v-1-11c0-3 0-6 1-8-1-2-1-1 0-3 2 0 6-6 8-8 2-1 3-2 4-3l1-1v-1c0-2-1-3 0-5l2-2h-2l2-3c0-3 0-2 1-4l-2-1h-1l-2 3h-1l-1-1-1 1v-1c1-4 0-10 0-14v-19h2l3-1v-6c-1-2 0-2 0-3 0 0-1 0-1-1l1-1c0-1-1-4-1-4v-2c1-4 0-7 0-10 1-3 1-6 1-9 1-2 2-4 5-5l1-2h0l4-4 3-2 2-2 2-1z" class="H"></path><path d="M462 443l3-1h1c0 1 1 2 1 2-2 1-3 0-4 2l-1 5c1 1 2 1 3 1h4l1 1h-5l-4 1v-7c-2 2-2 4-5 5-1 0-2 2-3 3h-1l10-12z" class="m"></path><path d="M456 460h4c2 2 1 10 1 12l-2 1c-2 0-3 0-5-1 1-3 1-9 2-12z" class="U"></path><path d="M454 474c2-2 4-1 7 0v14 1 2c-2 1-5 1-7 0v-2-8-7z" class="e"></path><path d="M486 440h0v11c-1 0-2 0-3 1h-4v1c-3 1-6 0-9 0l-1-1h-4c-1 0-2 0-3-1l1-5c1-2 2-1 4-2 0 0-1-1-1-2h-1c2-2 12-1 16-1 1 0 3 0 5-1z" class="P"></path><path d="M466 442l5 1 1 1c-2 1-3 1-5 1v-1s-1-1-1-2z" class="S"></path><path d="M484 446l1 1 1 2h-1c-2 0-4 0-6-1v-1c2 0 3 0 5-1zm-7-1c-1-1-1-1-1-2l9-1 1 1-1 2h-2-6z" class="I"></path><path d="M477 445h6c0 1 1 1 1 1-2 1-3 1-5 1h-4v1l3 1v1h-1l-2 2h-6l1-1c1 0 1 0 2-1-2 0-3 0-4-1h-1 6c0 1 0 0 1 1 1-1 0-1 0-2l1-1c-1 0-2-1-2-1l2-1h2z" class="F"></path><defs><linearGradient id="AD" x1="464.21" y1="448.066" x2="470.524" y2="445.719" xlink:href="#B"><stop offset="0" stop-color="#2a2929"></stop><stop offset="1" stop-color="#474746"></stop></linearGradient></defs><path fill="url(#AD)" d="M467 444v1h8l-2 1s1 1 2 1l-1 1c0 1 1 1 0 2-1-1-1 0-1-1h-6 1c1 1 2 1 4 1-1 1-1 1-2 1l-1 1h-4c-1 0-2 0-3-1l1-5c1-2 2-1 4-2z"></path><path d="M465 452c1-1 2-1 2-2h-2c0-1 1-1 2-1h1c1 1 2 1 4 1-1 1-1 1-2 1l-1 1h-4z" class="D"></path><path d="M479 453v-1h4c1-1 2-1 3-1v9 3 1h-8-2c-4-2-11-1-15 0v-3-7l4-1h5c3 0 6 1 9 0z" class="G"></path><path d="M461 454l4-1-1 1h1v2h1-1l1 1h5v1h-1v-1c-2 2-5 2-7 2v1h4l-6 1v-7z" class="B"></path><path d="M484 460h2v3 1h-8-2c-4-2-11-1-15 0v-3l6-1h9 8z" class="m"></path><path d="M484 460h2v3c-3 0-9 1-11-1l1-2h8z" class="O"></path><path d="M479 453v-1h4c1-1 2-1 3-1v9h-2l1-1h-1c-3 0-6 0-9-1l-1-1c2 0 2 0 3-1h-3-1c-2 0-5 0-7 1l-1-1h1-1v-2h-1l1-1h5c3 0 6 1 9 0z" class="T"></path><path d="M465 453h5c3 0 6 1 9 0v1c-2 1-6 1-9 1l-5-1h-1l1-1z" class="F"></path><path d="M461 464c4-1 11-2 15 0h2 8v1 4 4h-2-1l-1 3h-1-5l-1-1h-1c-1 0-2 0-3 1v-1l-1-1c-3 0-5-1-8-1v1l-1-7v-3z" class="C"></path><path d="M474 468l-3-1c0-1-1-2-1-3 2-1 4 0 6 0h2 0c-3 1-3 2-4 4h0z" class="M"></path><path d="M471 469h2c1 1 1 2 1 3v3c-1 0-2 0-3 1v-1l-1-1 1-1v-1h-2c0-1-1-2 0-3h2z" class="V"></path><path d="M469 469h2v2l2 2h-2v-1h-2c0-1-1-2 0-3z" class="G"></path><path d="M461 467c2 0 3 0 4 1h4v1c-1 1 0 2 0 3h2v1l-1 1c-3 0-5-1-8-1v1l-1-7z" class="P"></path><path d="M462 473v-1c2 1 3 0 5 0l-2-1v-1-1h4c-1 1 0 2 0 3h2v1l-1 1c-3 0-5-1-8-1z" class="F"></path><path d="M478 464h8v1 4 4h-2-1l-1 3h-1-5l-1-1h-1v-3l2 1v-1-2l-2-2h0c1-2 1-3 4-4h0z" class="T"></path><path d="M484 470l2-1v4h-2v-1c-1 0-2 0-3-1 1 0 2 0 3-1z" class="S"></path><path d="M475 475c2-2 6-2 8-2l-1 3h-1-5l-1-1zm11-10v4l-2 1c-2-1-6 0-9-2 1-1 1-1 2-1 3 0 6-1 9-2z" class="I"></path><path d="M462 473c3 0 5 1 8 1l1 1v1c1-1 2-1 3-1h1l1 1h5 1l1-3h1 2v1 5l1 7c-4 2-7 5-10 9h0c-1 1-2 1-4 1l2 1c-2 3 0-1-1 1l-1 2h0c-1 0-2 1-2 1-1 1-1 1-2 1h0 0c-2-1-4-1-5-2l-4-3-7-5 1-1c2 1 5 1 7 0v-2-1l1-2c-1-4 0-9 0-12v-1z" class="Y"></path><path d="M464 493h5v1c-1 1-3 1-4 1-1-1-1-1-1-2z" class="I"></path><path d="M471 487l1 6h-3-5-1l-1-1 1-1 6 1c0-1 1-1 1-1 0-2 0-3 1-4h0z" class="B"></path><path d="M474 484h9v1c-2 2-5 1-8 2h0-1v-3z" class="N"></path><path d="M471 476c1-1 2-1 3-1v8 1 3c0 2 0 5-1 6h-1l-1-6v-6-5z" class="H"></path><path d="M461 491c-1 2-1 3 0 4l2 1h0c1 1 1 1 1 2 2 0 2-1 4 0l1 2-1 1 1 1c-2-1-4-1-5-2l-4-3-7-5 1-1c2 1 5 1 7 0z" class="N"></path><path d="M462 486h3 2 1c1 0 2 1 3 1-1 1-1 2-1 4 0 0-1 0-1 1l-6-1-1 1 1 1v3l-2-1c-1-1-1-2 0-4v-2-1l1-2z" class="I"></path><path d="M462 486h3 2 1c1 0 2 1 3 1-1 1-1 2-1 4 0 0-1 0-1 1v-2l-1-1h-3l-1-1-3 1v-1l1-2z" class="S"></path><path d="M484 473h2v1 5c-3 1-9 0-11 2v1l-1 1v-8h1l1 1h5 1l1-3h1z" class="D"></path><path d="M486 476v2l-4 1c1-2 2-2 3-3h1z" class="S"></path><path d="M484 473h2v1 2h-1-3l1-3h1z" class="m"></path><path d="M476 476h5 1 3c-1 1-2 1-3 3h-6v-1-2zm-14-3c3 0 5 1 8 1l1 1v1 5 6h0c-1 0-2-1-3-1h-1-2-3c-1-4 0-9 0-12v-1z" class="B"></path><path d="M470 474l1 1v1 5 6h0c-1 0-2-1-3-1 1 0 1-1 2-2 0-1 0-2-1-3h-2c0-1-1-2-2-3s0-1 0-2c1-1 2-1 4-1h0l1-1z" class="a"></path><path d="M497 482c1-1 2-1 4-1v1c1 3 1 6 1 9 0 1 1 2 1 3h-1c-4 2-11 5-14 9s-4 10-5 15l-3-1c-3 0-8 0-10 2v1 5c-1-2-1-2-3-3l-1-1h-1-3l-9-1h-1l-1-1 1-10-1-2 1-2c-1-2-1-4-1-6l1-8 1 1 7 5 4 3c1 1 3 1 5 2h0 0c1 0 1 0 2-1 0 0 1-1 2-1h0l1-2c1-2-1 2 1-1l-2-1c2 0 3 0 4-1h0c3-4 6-7 10-9l1-1h1l3-1c1-1 3-1 5-2z" class="e"></path><path d="M501 482c1 3 1 6 1 9-1-2-1-3-2-4-2 0-4 1-6 1-2 1-4 3-6 3 0-1 11-6 13-8v-1z" class="Z"></path><path d="M472 515h1c1 0 2-1 2-1h6c1-1 1-2 1-2 1-1 1-2 2-3v-1c1-1 0-2 1-3l1-1c0-1 1-2 2-3 2-3 7-7 10-7h1v-1c1 0 2 1 3 1-4 2-11 5-14 9s-4 10-5 15l-3-1h2c-2-1-8-1-11-1l1-1z" class="o"></path><path d="M487 486l1-1h1c-2 2-3 4-5 5-2 3-5 5-6 8-3 4-5 12-6 17l-1 1c3 0 9 0 11 1h-2c-3 0-8 0-10 2v1 5c-1-2-1-2-3-3l-1-1h-1-3v-1c0-1 0-2 1-4s0-4 1-6v-1-3-1-1c0-2-1-2 0-4 1 1 3 1 5 2h0 0c1 0 1 0 2-1 0 0 1-1 2-1h0l1-2c1-2-1 2 1-1l-2-1c2 0 3 0 4-1h0c3-4 6-7 10-9z" class="U"></path><path d="M469 502c1 0 1 0 2-1 0 0 1-1 2-1h0l1-2c1-2-1 2 1-1l-2-1c2 0 3 0 4-1h0c-4 6-8 13-7 21h0 1c3 0 9 0 11 1h-2c-3 0-8 0-10 2v1 5c-1-2-1-2-3-3v-2l-1-1c1 0 1-1 1-2-1-1-1-1 0-1 0-2 0-3 1-4v-1c0-1 0 0-1-1h0l1-1c0-1 0-1 1-2 0-1 1-1 1-2l-1-1v-2z" class="N"></path><path d="M464 500c1 1 3 1 5 2h0 0v2l1 1c0 1-1 1-1 2-1 1-1 1-1 2l-1 1h0c1 1 1 0 1 1v1c-1 1-1 2-1 4-1 0-1 0 0 1 0 1 0 2-1 2l1 1v2l-1-1h-1-3v-1c0-1 0-2 1-4s0-4 1-6v-1-3-1-1c0-2-1-2 0-4z" class="G"></path><path d="M464 500c1 1 3 1 5 2h0 0c-1 1-2 2-2 3h-3v-1c0-2-1-2 0-4z" class="V"></path><path d="M452 491l1 1 7 5 4 3c-1 2 0 2 0 4v1 1 3 1c-1 2 0 4-1 6s-1 3-1 4v1l-9-1h-1l-1-1 1-10-1-2 1-2c-1-2-1-4-1-6l1-8z" class="J"></path><path d="M460 497l4 3c-1 2 0 2 0 4v1 1c-2 0-4 1-5 0l1-3v-1c-1-2 0-3 0-5z" class="H"></path><g class="Z"><path d="M452 509h7v2c-1 0-2 1-3 1l1 1h2c1 1 0 3 0 4l-1 1v1 1h-5-1l-1-1 1-10h0z"></path><path d="M452 491l1 1 7 5c0 2-1 3 0 5-1 1-1 2-2 3h-2v1c1 0 2 0 3 1-1 0-3 1-5 0h0l-2 2h0l-1-2 1-2c-1-2-1-4-1-6l1-8z"></path></g><path d="M452 505c1 0 1 0 2 1v1l-2 2h0l-1-2 1-2z" class="U"></path><path d="M486 411c2 2 4 3 6 4h1 1c1 0 3 1 4 1h4v5l1-2v15h1v4c2-1 3-1 5-1v30l-1 18-4 1c0 1 0 2-1 3l1 4-1 1c0-1-1-2-1-3 0-3 0-6-1-9v-1c-2 0-3 0-4 1-2 1-4 1-5 2l-3 1h-1l-1 1-1-7v-5-1-4-4-1-1-3-9-11h0v-22-2-2-3z" class="X"></path><path d="M489 420h1v25c0 2 0 7 1 8l-1 1v1h1c1-1 1 0 2 0h0l2 2c-1 0-3 0-4 1v2l-1 4 1 12v7l1 1-3 1h-1l1-1v-2c1-3 0-6 0-9v-14-39z" class="c"></path><path d="M490 464c0-2 0-6 1-9h2l2 2c-1 0-3 0-4 1v2l-1 4z" class="f"></path><path d="M495 457c2 1 2 2 3 4 1 3 1 7 1 10 0 4 0 7-2 10v1c-2 1-4 1-5 2l-1-1v-7l-1-12 1-4v-2c1-1 3-1 4-1z" class="U"></path><path d="M495 457c2 1 2 2 3 4 1 3 1 7 1 10 0 4 0 7-2 10v1c-2 1-4 1-5 2l-1-1c2-1 2-2 4-3 1 0 2-1 3-2 0-1-1-2-1-3v-4c0-4 0-8-1-11l-1-1v-1c-2 1-3 1-4 2v-2c1-1 3-1 4-1z" class="J"></path><path d="M486 411c2 2 4 3 6 4h1 1c1 0 3 1 4 1h4v5 2 19c0 4 1 8 0 11h0-11c-1-1-1-6-1-8v-25h-1c0-2 0-5-1-7l-2 1v-3z" class="X"></path><path d="M486 411c2 2 4 3 6 4h1 1c1 0 3 1 4 1h4v5 2c-2 9 1 19-1 29h-1c-1-2 0-22 0-26 0-1 0-5-1-6h-3c-2-1-3-3-5-2l-1 2h-1c0-2 0-5-1-7l-2 1v-3z" class="L"></path><path d="M502 421l1-2v15h1v4c2-1 3-1 5-1v30l-1 18-4 1c0 1 0 2-1 3l1 4-1 1c0-1-1-2-1-3 0-3 0-6-1-9v-1c-2 0-3 0-4 1v-1c2-3 2-6 2-10 0-3 0-7-1-10-1-2-1-3-3-4l-2-2h0c-1 0-1-1-2 0h-1v-1l1-1h11 0c1-3 0-7 0-11v-19-2z" class="O"></path><path d="M502 421l1-2v15h1v4 48c0 1 0 2-1 3l-1-36h0c1-3 0-7 0-11v-19-2z" class="e"></path><path d="M491 453h11l1 36 1 4-1 1c0-1-1-2-1-3 0-3 0-6-1-9v-1c-2 0-3 0-4 1v-1c2-3 2-6 2-10 0-3 0-7-1-10-1-2-1-3-3-4l-2-2h0c-1 0-1-1-2 0h-1v-1l1-1z" class="Q"></path><path d="M493 455h6c1 5 1 11 0 16 0-3 0-7-1-10-1-2-1-3-3-4l-2-2h0z" class="W"></path><path d="M469 380l9-7 1 1-4 3h3v1l1 1c1 0 1 1 2 2s1 1 1 2h1v2 10 2l-2 4c-1 1-3 3-3 4l1 1c2 1 4 2 5 3l2 1v1 3 2 2 22h0 0c-2 1-4 1-5 1-4 0-14-1-16 1l-3 1-10 12c-1 1-1 1-2 0-1-3 2-5 2-8v-1-11c0-3 0-6 1-8-1-2-1-1 0-3 2 0 6-6 8-8 2-1 3-2 4-3l1-1v-1c0-2-1-3 0-5l2-2h-2l2-3c0-3 0-2 1-4l-2-1h-1l-2 3h-1l-1-1-1 1v-1c1-4 0-10 0-14l6-3c1 0 2-1 2-1z" class="o"></path><path d="M479 406c2 1 4 2 5 3l-1 1c-1 3-2 6-4 8l-1 2c0-1 0-2-1-3-2 2-4 4-5 7-2 2-5 7-8 8 1-3 4-7 7-10l7-9-1-1 1-1c1-1 0-2 0-3l1-2z" class="J"></path><path d="M479 406c2 1 4 2 5 3l-1 1h0l-5 3-1-1 1-1c1-1 0-2 0-3l1-2z" class="P"></path><path d="M484 409l2 1v1 3 2 2 22h0 0c-2 1-4 1-5 1-4 0-14-1-16 1l-3 1 3-5 7-9 6-9 1-2c2-2 3-5 4-8l1-1z" class="a"></path><path d="M473 433l12-2v1c0 1 1 2 0 3h-14l-1-1c1-1 1-1 3-1z" class="I"></path><path d="M472 429v1 2c0 1 0 0 1 1-2 0-2 0-3 1l1 1-1 2h1 14c0 1 1 2 1 3h0 0c-2 1-4 1-5 1-4 0-14-1-16 1l-3 1 3-5 7-9z" class="Y"></path><path d="M468 439v-1h4 11v1h-4-11z" class="E"></path><path d="M465 438c1 1 1 2 3 1h11 6l1 1c-2 1-4 1-5 1-4 0-14-1-16 1l-3 1 3-5zm19-29l2 1v1 3 2 2l-1 3v1 8h-13v-1l6-9 1-2c2-2 3-5 4-8l1-1z" class="I"></path><path d="M486 416v2l-1 3-3-1c0-1 1-1 1-2h1 1l1-2z" class="T"></path><path d="M485 422h-1c-1 0-3 0-4 1v1h-2 0l1-1c0-1 1-2 2-3h1l3 1v1z" class="O"></path><path d="M469 380l9-7 1 1-4 3h3v1l1 1c1 0 1 1 2 2s1 1 1 2h1v2 10 2l-2 4c-1 1-3 3-3 4l1 1-1 2c0 1 1 2 0 3l-1 1 1 1-7 9c-3 3-6 7-7 10-1 0-2 3-2 3 0-3 0-14-2-16-2 1-5 4-6 5l-1 3c-1-2-1-1 0-3 2 0 6-6 8-8 2-1 3-2 4-3l1-1v-1c0-2-1-3 0-5l2-2h-2l2-3c0-3 0-2 1-4l-2-1h-1l-2 3h-1l-1-1-1 1v-1c1-4 0-10 0-14l6-3c1 0 2-1 2-1z" class="Y"></path><path d="M478 408c0 1 1 2 0 3l-1 1 1 1-7 9-2-1-3 2-3-4c2-3 4-5 6-7 2 0 3-1 4-2 2-1 4-2 5-2z" class="C"></path><path d="M477 412l-1 1c-1 1-2 1-2 2h-1 0c-1 0-1 0-2 1l1-2c0-1 0-1 1-2s3-2 4-2l1 1-1 1z" class="G"></path><path d="M473 391c2 0 4-2 5-3h2c0 2-1 9 0 11-1 4-4 7-7 9l-4 2-3 2v-1c0-2-1-3 0-5l2-2h-2l2-3c0-3 0-2 1-4l-2-1h-1l-2 3h-1l-1-1-1 1v-1c2-1 3-2 5-3l2-1 3-2 1-1h1z" class="L"></path><path d="M468 401l3-3 1-1c-1-1-1 0 0-1s2-2 4-2h0v4-2h-1c-1 1-5 4-5 6v1c-1 1-1 2-2 3v2c0 1 1 1 1 1v1l-3 2v-1c0-2-1-3 0-5l2-2h-2l2-3z" class="V"></path><path d="M473 391c2 0 4-2 5-3h2c0 2-1 9 0 11-3-1-2 0-4 1v-2-4h0c-2 0-3 1-4 2s-1 0 0 1l-1 1-3 3c0-3 0-2 1-4l-2-1h-1l-2 3h-1l-1-1-1 1v-1c2-1 3-2 5-3l2-1 3-2 1-1h1z" class="M"></path><path d="M472 391c0 2-1 2-2 3l-1 1c-1 0-1 0-2 1h-1l-2 3h-1l-1-1-1 1v-1c2-1 3-2 5-3l2-1 3-2 1-1z" class="B"></path><path d="M469 380l9-7 1 1-4 3h3v1l1 1c1 0 1 1 2 2s1 1 1 2c-1 1-2 2-3 4-2 1-4 2-6 4h0-1l-1 1-3 2-2 1c-2 1-3 2-5 3 1-4 0-10 0-14l6-3c1 0 2-1 2-1z" class="p"></path><path d="M468 394v-5l1-1v-2c1-1 1-1 1-2l1-1c1-2 3-3 5-4v2c-2 1-3 2-4 3v3c-1 0-1 1-1 1v4l-3 2z" class="c"></path><path d="M471 392v-4s0-1 1-1v-3c1-1 2-2 4-3 0 1 1 3 1 4l2 2c-2 1-4 2-6 4h0-1l-1 1z" class="K"></path><path d="M473 391l-1-1c1-2 2-3 3-4h0l2-1 2 2c-2 1-4 2-6 4z" class="i"></path><path d="M469 380l9-7 1 1-4 3c-2 1-8 5-9 8h0v1 2 1c-1 1-1 4 0 5v1c-2 1-3 2-5 3 1-4 0-10 0-14l6-3c1 0 2-1 2-1z" class="D"></path><path d="M483 312v13c1-2 1-5 1-7v-5l3 1v4 8h8v-2l-1-1 5 1h10c1 0 3-1 5 0 2-1 5-1 8-2l-3-5 1-2c1 3 3 5 3 8-1 0-3 1-4 1-1 2-1 4-3 4v1l-1 1-1 1c1 1 1 2 1 3l-2-1c0 2 0 5-1 7l1 1v3h0c0 1 0 2-1 2 1 1 2 2 3 2l-2 1c0 2 0 5 1 6 0 1 0 2-1 4h0c1 1 1 3 1 4l-1 1v2c1 1 0 2 0 4v1 2c0 1 1 3 0 4l1 1c1 1 1 2 2 3l2 2h1l1 2c-1 1-3 0-5 0l-1 1v8c1 2 1 3 1 6h-1l-2 1v3h-2-1c-1 2-1 3 0 5v28c-2 0-3 0-5 1v-4h-1v-15l-1 2v-5h-4c-1 0-3-1-4-1h-1-1c-2-1-4-2-6-4v-1l-2-1c-1-1-3-2-5-3l-1-1c0-1 2-3 3-4l2-4v-2-10-2h-1c0-1 0-1-1-2s-1-2-2-2l-1-1v-1h-3l4-3-1-1-9 7s-1 1-2 1l-6 3v-19h2l3-1v-6c-1-2 0-2 0-3 0 0-1 0-1-1l1-1c0-1-1-4-1-4v-2c1-4 0-7 0-10 1-3 1-6 1-9 1-2 2-4 5-5l1-2h0l4-4 3-2 2-2 2-1z" class="m"></path><path d="M502 354l2 1v24c-1-4-1-8-1-12h0l-1 3v-16z" class="X"></path><path d="M503 331c1 1 1 1 1 2-1 4 0 10 0 15v7l-2-1h0-5-1-3c1-1 1-1 2-1 2 0 3 0 5-2h0c1 0 1 0 2-1v-3c2-5 0-11 1-16z" class="k"></path><path d="M503 331v-2h1 1 1c1 0 2 0 2 1h1c2 1 2 2 3 3h1c0 2 0 5-1 7l1 1v3h0c0 1 0 2-1 2h-1-2c0-3 1-10 0-13l-2-1-3 1c0-1 0-1-1-2z" class="L"></path><path d="M507 332l-1-1c2 0 3 0 4 1 0 0 1 1 1 2s1 4 0 5c0 1-1 1 0 2 0 2 1 3 0 5h-2c0-3 1-10 0-13l-2-1z" class="K"></path><path d="M502 370l1-3h0c0 4 0 8 1 12v38c0 6 1 12 0 17h-1v-15l-1 2v-5h-4 0c1-1 2-1 3-1l1-2c-1 0-2 0-2-1v-5l1-17c-3-1-8-2-10-5v-1h1v-1h10v-1c-3-1-6 0-8-1h-1 6 3v-11z" class="b"></path><path d="M492 384l10-1v1 29c-1 0-2 0-2-1v-5l1-17c-3-1-8-2-10-5v-1h1z" class="c"></path><path d="M491 385h9c1 2 1 3 1 5-3-1-8-2-10-5z" class="W"></path><path d="M496 354h1 5 0v16 11h-3-6v-1c1-7 1-17 0-24h0c1-1 2-1 3-2z" class="J"></path><path d="M495 363h1v1l-1 3h-1v-3h0l1-1z" class="W"></path><path d="M497 354h5 0v16 11h-3c1-3 2-24 1-26h-2l-1-1z" class="i"></path><path d="M509 346h2 1c1 1 2 2 3 2l-2 1c0 2 0 5 1 6 0 1 0 2-1 4h0c1 1 1 3 1 4l-1 1v2c1 1 0 2 0 4v1 2c0 1 1 3 0 4l1 1c1 1 1 2 2 3l2 2h1l1 2c-1 1-3 0-5 0l-1 1v8c1 2 1 3 1 6h-1l-2 1v3h-2-1v-12-18-28z" class="Z"></path><path d="M514 378c1 1 1 2 2 3l2 2h1l1 2c-1 1-3 0-5 0h0l-1-1h1c-2 0-2-1-4-1v-1c1-1 1-2 1-3h1l1-1z" class="d"></path><path d="M515 385h0l-1 1v8c1 2 1 3 1 6h-1l-2 1v-16h3z" class="c"></path><path d="M509 346h2 1c1 1 2 2 3 2l-2 1c0 2 0 5 1 6 0 1 0 2-1 4h0c1 1 1 3 1 4l-1 1v2c1 1 0 2 0 4v1 2c0 1 1 3 0 4-1 0 0 0-1-1s-1-3-1-5c0-3-1-5-1-8-1-1 0-3 0-5-1 2 0 6 0 7l-1 1v8h0v-28z" class="H"></path><path d="M509 366v-12c0-1 0-3 1-4v5 1l2 1h0c0 2-1 6 0 8l1 1c1 1 0 2 0 4v1 2c0 1 1 3 0 4-1 0 0 0-1-1s-1-3-1-5c0-3-1-5-1-8-1-1 0-3 0-5-1 2 0 6 0 7l-1 1z" class="U"></path><path d="M491 403v-3h0c-1-2-2-11-1-13l1-3v1c2 3 7 4 10 5l-1 17v5c0 1 1 1 2 1l-1 2c-1 0-2 0-3 1h0c-1 0-3-1-4-1h-1-1c-2-1-4-2-6-4v-1l-2-1c-1-1-3-2-5-3l-1-1c0-1 2-3 3-4l2-4 1 1 3-1h0c0 2-1 2 0 3 0 2 1 3 2 3h0l2 1v-1z" class="J"></path><path d="M489 403l2 1v-1c2 0 2 0 4 1v1c-1 1-2 2-2 3l1 1 1 3c1 1 0 0 2 1h-1l-1 1v-1l-1-1-1 1h-2v-2-4c0-1-1-2-2-4h0z" class="H"></path><path d="M491 407c2 1 2 3 3 5l-1 1h-2v-2-4z" class="q"></path><path d="M487 397h0c0 2-1 2 0 3 0 2 1 3 2 3 1 2 2 3 2 4v4 2h2l1-1 1 1c-1 1-1 1-1 2h-1-1c-2-1-4-2-6-4v-1l-2-1c-1-1-3-2-5-3l-1-1c0-1 2-3 3-4l2-4 1 1 3-1z" class="X"></path><path d="M487 397h0c0 2-1 2 0 3 0 2 1 3 2 3 1 2 2 3 2 4h-1c0-1-1-1-2-2-2 0-3-1-4-3v-4l3-1zm-6 4l2 1v2c1 1 0 1 1 1 0 2 1 3 2 5l-2-1c-1-1-3-2-5-3l-1-1c0-1 2-3 3-4z" class="b"></path><path d="M484 405c3 1 5 4 7 6v2h2l1-1 1 1c-1 1-1 1-1 2h-1-1c-2-1-4-2-6-4v-1c-1-2-2-3-2-5z" class="j"></path><path d="M520 315c1 3 3 5 3 8-1 0-3 1-4 1-1 2-1 4-3 4v1l-1 1-1 1c1 1 1 2 1 3l-2-1h-1c-1-1-1-2-3-3h-1c0-1-1-1-2-1h-1-1-1v2c-1 5 1 11-1 16v3c-1 1-1 1-2 1h0c-2 2-3 2-5 2-1 0-1 0-2 1h3c-1 1-2 1-3 2h0c0-1-1-3-1-4v-1c-1-5 0-9-1-13 0-2 1-2 1-3h2 1c0-3 0-3-1-5-1-1-1-1-1-2h-5v4c-1 0-1-2-1-2v-4h8v-2l-1-1 5 1h10c1 0 3-1 5 0 2-1 5-1 8-2l-3-5 1-2z" class="F"></path><path d="M520 315c1 3 3 5 3 8-1 0-3 1-4 1-6 2-11 1-17 2h0-1-3c-1 2-1 2-3 2v-2-2l-1-1 5 1h10c1 0 3-1 5 0 2-1 5-1 8-2l-3-5 1-2z" class="B"></path><path d="M494 330h3c2-1 2-1 4-1 1 2 1 5 1 8s0 6-1 9v2c0 1-1 1-1 2v1h0c-2 2-3 2-5 2-1 0-1 0-2 1h3c-1 1-2 1-3 2h0c0-1-1-3-1-4v-1c-1-5 0-9-1-13 0-2 1-2 1-3h2 1c0-3 0-3-1-5z" class="W"></path><path d="M494 330h3c2-1 2-1 4-1 1 2 1 5 1 8-1-1-1-2-1-3v-1h-1v-1c0-1 0-1-1-2-1 1-1 1-2 3 0 1 0 1-1 2 1 1 1 2 1 3v1 1c0 1 0 4-1 5v-5-1c-1-1-1-3-1-4 0-3 0-3-1-5z" class="K"></path><path d="M483 312v13c1-2 1-5 1-7v-5l3 1v4 8 4s0 2 1 2v-4h5c0 1 0 1 1 2 1 2 1 2 1 5h-1-2c0 1-1 1-1 3 1 4 0 8 1 13v1c0 1 1 3 1 4 1 7 1 17 0 24v1h1c2 1 5 0 8 1v1h-10v1h-1l-1 3c-1 2 0 11 1 13h0v3 1l-2-1h0c-1 0-2-1-2-3-1-1 0-1 0-3h0l-3 1-1-1v-2-10-2h-1c0-1 0-1-1-2s-1-2-2-2l-1-1v-1h-3l4-3-1-1-9 7s-1 1-2 1l-6 3v-19h2l3-1v-6c-1-2 0-2 0-3 0 0-1 0-1-1l1-1c0-1-1-4-1-4v-2c1-4 0-7 0-10 1-3 1-6 1-9 1-2 2-4 5-5l1-2h0l4-4 3-2 2-2 2-1z" class="d"></path><path d="M487 397h2c0 2 1 3 0 4v2h0c-1 0-2-1-2-3-1-1 0-1 0-3z" class="W"></path><path d="M483 325c1-2 1-5 1-7v-5l3 1v4 8 4s0 2 1 2v-4h5c0 1 0 1 1 2 1 2 1 2 1 5h-1-1c-1-1 0 0-1-2l-1-1c-1-1-1-2-1-2h-1v5c-1 4-1 8-1 12v27c0 7 1 15 1 22l-2-1v-12-50h-4v-3-3-2z" class="H"></path><path d="M481 333v-1c1-2 0-1 0-3l1 1c0-1 1-2 1-3v3 3h4v50 12l2 1c-1 0-1 1-2 1l-3 1-1-1v-2-10-2h-1c0-1 0-1-1-2s-1-2-2-2l-1-1v-1h-3l4-3 3-1v1 2h-1v1h2v-23-3l-1 1-1-13s-1 0-1-1v-2l1-1v-2z" class="d"></path><path d="M487 383v12l2 1c-1 0-1 1-2 1l-3 1-1-1v-2l3-2v-5c0-2 1-3 1-5z" class="J"></path><path d="M481 333v-1c1-2 0-1 0-3l1 1c0-1 1-2 1-3v3 3 52-2h-1c0-1 0-1-1-2s-1-2-2-2l-1-1v-1h-3l4-3 3-1v1 2h-1v1h2v-23-3l-1 1-1-13s-1 0-1-1v-2l1-1v-2z" class="E"></path><path d="M481 333v-1c1-2 0-1 0-3l1 1c0-1 1-2 1-3v3 21l-1 1-1-13s-1 0-1-1v-2l1-1v-2z" class="P"></path><path d="M482 352l1-1v3 23h-2v-1h1v-2-1l-3 1-1-1-9 7s-1 1-2 1l-6 3v-19h2l3-1 2-2h0l3-2 6-4 1-2 3-2h1z" class="H"></path><path d="M472 362h4v3l-1-2c-1 0-1 0-2 1s-1 2-1 4c-1 4-3 6-3 11v1s-1 1-2 1c0-1 1-1 0-2 0-3-1-9 1-12h0c1-2 2-3 4-5z" class="c"></path><path d="M463 365c0 2 0 1 1 2 1-1 1-1 2-1 2-3 4-4 7-7h3c-2 1-3 2-4 3-2 2-3 3-4 5h0c-2 3-1 9-1 12 1 1 0 1 0 2l-6 3v-19h2z" class="F"></path><path d="M482 352l1-1v3 23h-2v-1h1v-2-1l-3 1-1-1 2-2h-2l-1 1-1-1v-6-3h-4c1-1 2-2 4-3h-3c-3 3-5 4-7 7-1 0-1 0-2 1-1-1-1 0-1-2l3-1 2-2h0l3-2 6-4 1-2 3-2h1z" class="P"></path><path d="M482 352l1-1v3c-2 1-3 1-5 0l3-2h1z" class="I"></path><path d="M476 359h0c1-1 2-1 2-1h1c1 1 0 1 0 2 1 2 1 2 1 4v1c2 2 2 2 2 5l-2 1h-2l-1 1-1-1v-6-3h-4c1-1 2-2 4-3z" class="L"></path><path d="M483 312v13 2c0 1-1 2-1 3l-1-1c0 2 1 1 0 3v1 2l-1 1v2c0 1 1 1 1 1l1 13h-1l-3 2-1 2-6 4-3 2h0l-2 2v-6c-1-2 0-2 0-3 0 0-1 0-1-1l1-1c0-1-1-4-1-4v-2c1-4 0-7 0-10 1-3 1-6 1-9 1-2 2-4 5-5l1-2h0l4-4 3-2 2-2 2-1z" class="F"></path><path d="M469 346v-1-1-4l1-1 1 1h0v-1c1 1 1 2 2 3v1h1l2-2 1 1h-1l-4 4h-3z" class="p"></path><path d="M471 339c0-2 0-4 2-5 0 1 0 2 1 3 1-1 2-2 2-3h1v2c0 1 0 2 1 2-1 2-1 2-2 3l-2 2h-1v-1c-1-1-1-2-2-3z" class="L"></path><path d="M477 342l1-1c1 1 1 1 1 2 0 3 1 7 2 9l-3 2-1 2-6 4-3 2h0 0c-1-1-1-2-1-3v-5c0-3 2-4 2-7v-1h3l4-4h1z" class="H"></path><path d="M468 362v-4c0-1 2-2 2-2v-3l1 1c-1 2-1 3-1 5l1 1-3 2z" class="f"></path><path d="M470 353c2 0 2-1 3-2l1 3h1 1l1 2-6 4-1-1c0-2 0-3 1-5l-1-1h0z" class="J"></path><path d="M470 353l1-1-1-1c2-3 4-5 7-6 1 1 1 5 1 7 0 0-1 1-2 1v1h-1-1l-1-3c-1 1-1 2-3 2z" class="f"></path><path d="M473 351l2-1c1 1 1 1 1 2v1 1h-1-1l-1-3z" class="X"></path><path d="M483 312v13 2c0 1-1 2-1 3l-1-1c0 2 1 1 0 3v1l-1-2h-1c0-1 0-1-1-2h0c-1 0-1 0-2-1 0-2 0-5-1-6l-2 2-1 3h0l-2-1c-1 1 0 3-1 4v2l-2 1 1 6c0 1 0 1-1 2v5h1l-3 3v-2c1-4 0-7 0-10 1-3 1-6 1-9 1-2 2-4 5-5l1-2h0l4-4 3-2 2-2 2-1z" class="E"></path><defs><linearGradient id="AE" x1="485.811" y1="318.08" x2="477.6" y2="324.914" xlink:href="#B"><stop offset="0" stop-color="#21231f"></stop><stop offset="1" stop-color="#413f3f"></stop></linearGradient></defs><path fill="url(#AE)" d="M483 312v13 2c0 1-1 2-1 3l-1-1c0 2 1 1 0 3v1l-1-2v-2c0-1-1-2 0-3h0c-1-2-1-3-2-4h-1c-1 0-1 0-2-1l-1 1c-1 0-2 1-3 1l1-2h0l4-4 3-2 2-2 2-1z"></path><path d="M472 321h1c1-1 2-2 3-2h2c1 1 1 1 1 2l-1 1h-1c-1 0-1 0-2-1l-1 1c-1 0-2 1-3 1l1-2z" class="C"></path><path d="M654 179h85l4 1v1h-1l1 1v1h-1l1 2h-1v1 13 8c0 6 2 17-2 21v1l1-1c1-2 3-3 5-5v1c0 1-1 2-2 3-1 0-1 0-2 1-1 3 0 8 0 11l1 13v1h2v1l-2 1-1-2-3 2-10 5c-1 0-3 0-4-1h0-1l1 3c2 2 2 5 4 6 2 7 3 14 4 22 0 1 0 6 1 8l1 8v5l-2 1c-6-15-15-28-24-41-5-6-10-12-17-17-1 2-2 4-4 6l-1-1c-4-4-8-7-12-10l-2-2c-1 0-3-2-5-3l-8-5-1-1-9-4h-1c-6-4-13-6-20-8l-21-5-8-1c-2 1-5 1-7 0h0l-4-4c-2 0-5-5-7-6-2-2-5-4-8-6l-3-2c-1 0-2 1-4 2v-1l2-2v-1l-2-1c-2-1-3-2-4-2-3-3-6-5-9-7h4 2 7l3-2h1c1 0 1 0 3-1h2c5-2 13 1 18-1 2-1 4-1 7-2h31c2 1 3 1 5 2h1c3 1 5 0 7 1h9v-3-5z" class="I"></path><path d="M626 190h4 1 4l-2 1v3l-4-2v-1l-3-1z" class="P"></path><path d="M635 190c4 1 9 0 13 0-1 1-3 1-3 2h-2c-1 0-1-1-2 0-2 1-3 1-4 3v1c0-1 0-1-1-1h0c-2 0-2-1-3-1h0v-3l2-1z" class="D"></path><path d="M735 183h7l1 2h-1c-6 1-12 2-19 2 4-1 8-2 12-4z" class="Z"></path><path d="M633 194h0c1 0 1 1 3 1h0c1 0 1 0 1 1v-1c1-2 2-2 4-3 1-1 1 0 2 0-1 1-4 3-5 4l-1 2c-1 0-1 0-2 1s-2 1-4 2c1-2 1-3 1-5v-1-1c-1 0-2-1-3-2h0l4 2z" class="F"></path><path d="M698 184l37-1c-4 2-8 3-12 4h-40v-1c5 0 10-1 15-2z" class="d"></path><path d="M654 184c7-1 13 0 20 1 8 0 15-2 24-1-5 1-10 2-15 2v1h-29v-3z" class="o"></path><path d="M702 190c2-1 5 0 8 0h17c3 0 5-1 8-1 2 0 5 1 7 1v-4 13 1l-1-3c0-1-1-2-1-3-2-2-4-2-7-2-4 0-5 1-7 4l-1-2c-2-1-6-1-8-2-5 0-10-1-15-2z" class="B"></path><path d="M691 191c3-1 7-1 11-1 5 1 10 2 15 2 2 1 6 1 8 2l1 2c-1 2-2 5-2 8-1-1-1-1-1-2-1-1-2-1-4-1-1-1-2-3-4-4h-1c-2-2-5-3-8-5h-1 0c-4-2-10-1-14-1z" class="G"></path><path d="M715 197h4l4 4v1c-1-1-2-1-4-1-1-1-2-3-4-4z" class="R"></path><path d="M706 192h3 1c3 2 6 2 9 5h-4-1c-2-2-5-3-8-5z" class="Q"></path><path d="M601 184h31c2 1 3 1 5 2h1c3 1 5 0 7 1h-69c5-2 13 1 18-1 2-1 4-1 7-2z" class="d"></path><path d="M650 190c9 0 18 0 27 1 4 0 8-1 12 0h1 0l-6 2-3 1h0l-5 3v-2l-3-2v-1l-7 1-3 1-1-1-1 1v1h0c1 1 2 1 3 3h0v2h-2-1v-2c0-1-1-2-2-2l-4-3c-1-2-2-1-3-1s-3-1-3-2h1z" class="N"></path><path d="M673 193l7-1h2l2 1-3 1h0l-5 3v-2l-3-2z" class="C"></path><path d="M676 195l2-2h1c1 0 1 1 2 1l-5 3v-2zm-27-5h1c4 1 6 0 10 1 2 0 3 1 5 1h8l-7 1-3 1-1-1-1 1v1h0c1 1 2 1 3 3h0v2h-2-1v-2c0-1-1-2-2-2l-4-3c-1-2-2-1-3-1s-3-1-3-2z" class="F"></path><path d="M673 192v1l3 2v2l-2 3h0-1c0 1-1 1-1 2h-1c-1 1-1 2-2 4v1l-1-1c-2 1-3 2-4 3v-2-1h-1l-1-1v-2-1-2h2v-2h0c-1-2-2-2-3-3h0v-1l1-1 1 1 3-1 7-1z" class="p"></path><path d="M666 200v-1c-1 0-1-1-2-1l1-1 3 2v-1h1l-1 2h-2z" class="Q"></path><path d="M673 192v1l3 2v2l-2 3-2-1c-1-3-4-4-6-6l7-1z" class="M"></path><path d="M669 198c1 1 2 1 3 2h1c0 1-1 1-1 2h-1c-1 1-1 2-2 4v1l-1-1c-2 1-3 2-4 3v-2-1h-1l-1-1v-2-1h3l1-1v-1h2l1-2z" class="L"></path><path d="M666 201v3l-2 1h0l-2-2v-1h3l1-1z" class="Q"></path><path d="M691 191c4 0 10-1 14 1h0 1c3 2 6 3 8 5h1c2 1 3 3 4 4 1 2 3 3 3 5l2 1 1-1 3 5 3 6h-2c0 3 0 3 1 5h-1l-1 2-1-1h1c1-2 0-4-1-5-1-2-1-4-3-6v-2c-1 0-1-1-1-1 0-2-1-3-3-4-1 0 0 1-2 1l-2-2h-1v1h-1-3c-1-1-1-1-2-1l-2-1c-1-1-3-2-5-3-3 0-8-1-11 1-1 1-2 1-4 1h0c1-1 2-1 3-2s2-2 4-3l-5 1h-3l-1-1 1-1h-1c-1 0-2 0-3 1h0l-1-1 2-1-2-1 3-1 6-2h1z" class="K"></path><path d="M724 210l1 1 1 3h1c1-1 1-1 0-2l1-1 3 6h-2c0 3 0 3 1 5h-1l-1 2-1-1h1c1-2 0-4-1-5-1-2-1-4-3-6v-2z" class="W"></path><path d="M683 195c7-3 15-4 23-2-4 2-9 0-13 1-2 0-4 0-6 1h-1c-1 0 0 0-1 1-1 0-2 0-3 1h0l-1-1 2-1z" class="B"></path><path d="M706 193c1 1 2 1 4 2-1 1-2 1-3 2h-2-5-6l-5 1h-3l-1-1 1-1h-1c1-1 0-1 1-1h1c2-1 4-1 6-1 4-1 9 1 13-1z" class="N"></path><path d="M685 196c1-1 0-1 1-1h1c2-1 4-1 6-1l-2 1c2 1 5 1 7 1s5 0 7 1h-5-6l-5 1h-3l-1-1 1-1h-1z" class="D"></path><path d="M710 195c4 3 7 6 10 10-1 0 0 1-2 1l-2-2h-1v1h-1-3c-1-1-1-1-2-1l-2-1c-1-1-3-2-5-3-3 0-8-1-11 1-1 1-2 1-4 1h0c1-1 2-1 3-2s2-2 4-3h6 5 2c1-1 2-1 3-2z" class="Q"></path><path d="M710 195c4 3 7 6 10 10-1 0 0 1-2 1l-2-2h-1c-2-1-3-3-6-4l-1-1-2-2c-2 1-3 1-5 0h-1 5 2c1-1 2-1 3-2z" class="C"></path><path d="M654 179h85l4 1v1h-1l1 1v1h-1-7l-37 1c-9-1-16 1-24 1-7-1-13-2-20-1v-5z" class="q"></path><path d="M597 191c2 0 3 0 4 1s1 0 2 0l-1-1 1-1c4 0 11 1 15 0 3-1 5 0 8 0l3 1v1h0c1 1 2 2 3 2v1 1c0 2 0 3-1 5h-4c-1 0-1 0-2-1-1 1-2 3-3 4l1 1c1 0 1-1 2-1h1c-1 3-1 3-4 5v1h-5c-2 0-3 0-4 1l-3-5c-3-5-7-9-11-13l-2-2z" class="a"></path><path d="M625 199l3-3c1-1 3 0 4 0 0 2 0 3-1 5h-4c-1 0-1 0-2-1v-1z" class="V"></path><path d="M605 193l-1-1h3c2 0 4 0 6 1 1 2 2 2 4 3-2 0-4 1-5 0l-1 1h1v1c-2-1-4-3-6-3l-1-2z" class="F"></path><path d="M605 193c2 0 4 1 6 1l1 2-1 1h1v1c-2-1-4-3-6-3l-1-2z" class="M"></path><path d="M606 195c2 0 4 2 6 3v-1l3 1c2 1 4 1 6 1l-1 2v1c-1 1 0 1-1 1h-1c-1-1-4-1-5-2-3-2-5-4-7-6z" class="L"></path><path d="M614 206l-1-1h0l9 1c-3-1-6-1-9-3h3c2 1 3 1 5 1h1l1 1c1 0 1-1 2-1h1c-1 3-1 3-4 5v1h-5l-3-1c-1 0-1 0-2-2l2-1z" class="i"></path><path d="M614 206c2 2 5 3 8 3v1h-5l-3-1c-1 0-1 0-2-2l2-1z" class="B"></path><path d="M613 193h6c3-1 4-1 7 1 0 2-1 3-1 4v1 1c-1 1-2 3-3 4h-1c-2 0-3 0-5-1h2 1c1 0 0 0 1-1v-1l1-2c-2 0-4 0-6-1l-3-1h-1l1-1c1 1 3 0 5 0-2-1-3-1-4-3z" class="G"></path><path d="M621 199c1-1 2-1 3 0-1 1-2 3-3 5-2 0-3 0-5-1h2 1c1 0 0 0 1-1v-1l1-2z" class="Q"></path><path d="M726 196c2-3 3-4 7-4 3 0 5 0 7 2 0 1 1 2 1 3l1 3v-1 8c0 6 2 17-2 21v1l1-1c1-2 3-3 5-5v1c0 1-1 2-2 3-1 0-1 0-2 1-1 3 0 8 0 11l1 13v1h2v1l-2 1-1-2h-1c0-4 1-9 0-12-1 0-1 0-2 1v-8c-1-1-1-2-2-4-1-1-2-3-3-4l-1-1h-1l-1-1v1l-2-1 1-2c-1-2-1-2-1-5h2l-3-6-3-5-1 1-2-1c0-2-2-3-3-5 2 0 3 0 4 1 0 1 0 1 1 2 0-3 1-6 2-8z" class="G"></path><path d="M740 205l2-1c0 1-1 3-1 4l-2 2c-2 2-4 3-5 5l-3 9v1l-2-1 1-2c-1-2-1-2-1-5h2v1c1-1 1-1 1-2s2-3 2-3l6-6c-2-1-3 1-4 2h-2l-1-2h3c2 0 2-1 4-2z" class="X"></path><path d="M738 213h1c0 1 0 2 1 3h0c1 1 1 5 1 7 0 0-1 1-1 2v1c0 1-1 2 0 3v1l-1-1c-2-1-4-4-4-6l-1-4c1-2 2-4 4-6z" class="l"></path><path d="M725 200c1 1 2 1 3 3h0c0 3 2 4 4 6h1 1 2c1-1 2-3 4-2l-6 6s-2 2-2 3 0 1-1 2v-1l-3-6-3-5c-1-1 0-5 0-6z" class="E"></path><path d="M733 209l1 1v1h-2c-3 0-3-2-4-4h-1l-1-1v-1-1l2-1c0 3 2 4 4 6h1z" class="P"></path><path d="M726 196c2-3 3-4 7-4 3 0 5 0 7 2 0 1 1 2 1 3l1 3v4l-2 1c-2 1-2 2-4 2h-3l1 2h-1-1c-2-2-4-3-4-6h0c-1-2-2-2-3-3 0 1-1 5 0 6l-1 1-2-1c0-2-2-3-3-5 2 0 3 0 4 1 0 1 0 1 1 2 0-3 1-6 2-8z" class="g"></path><path d="M725 200c2-3 3-5 5-6v2h1v3l-2-1c0 1-1 1-1 2v3c-1-2-2-2-3-3z" class="C"></path><path d="M730 194c3 0 6-1 8 1 3 2 3 6 3 9l-2-1c-1 0-1 0-1-1v-1c-2-1-5-4-7-5h-1v-2z" class="S"></path><path d="M731 196c2 1 5 4 7 5v1c0 1 0 1 1 1l2 1-1 1c-2 1-2 2-4 2h-3l1 2h-1-1c-2-2-4-3-4-6h0v-3c0-1 1-1 1-2l2 1v-3z" class="T"></path><path d="M731 199c2 1 3 2 4 4h3v1l-3 1c1 2 0 1 1 2h-3v-2l1 1 1-1c-1-1-2-1-3-1-1-2-1-3-1-5z" class="C"></path><path d="M728 200c0-1 1-1 1-2l2 1h0c0 2 0 3 1 5 1 0 2 0 3 1l-1 1-1-1v2l1 2h-1-1c-2-2-4-3-4-6h0v-3z" class="M"></path><path d="M728 200c0-1 1-1 1-2l2 1h0c0 2 0 3 1 5-1 0-2-1-2-1-2-1-2-2-2-3z" class="i"></path><path d="M648 190h1c0 1 2 2 3 2s2-1 3 1l4 3c1 0 2 1 2 2v2h1v2 1 2l1 1h1v1 2c-1 2-3 4-5 6 0 1-2 2-4 3-1 1-1 1-2 1l-3 1-1 1-1 1v-2-1h-4l-2-1c-1-1-2 0-3-1l-2-1 1-1-1-2c-1 0-1-1-2-2 0-5 0-9 2-13l1-2c1-1 4-3 5-4h2c0-1 2-1 3-2z" class="R"></path><path d="M647 205l2 1 2 4c1-1 2-3 4-4v2l-1 2h-4v-1c-1 1-1 1-2 1l-1-1 1-2-1-2z" class="K"></path><path d="M647 195h1 5c2 1 4 3 5 4v3l-1-1h-4c-3 0-4 0-6-1v-1l-1 1h1l-1 1c-1 0-1-1-2 0l-1-1-2 1 2-4 4-2z" class="E"></path><path d="M651 197c2 0 3 1 4 1v1h-1-7c1-2 3-2 4-2z" class="P"></path><path d="M648 195h5c2 1 4 3 5 4v3l-1-1h-4c0-1 0-1 1-2h1v-1c-1 0-2-1-4-1l-3-2z" class="C"></path><path d="M648 190h1c0 1 2 2 3 2s2-1 3 1c0 1 0 1 1 2s2 2 2 4h0c-1-1-3-3-5-4h-5-1l-4 2h-1c-2 1-2 2-2 3h-1v-3l-1-1c1-1 4-3 5-4h2c0-1 2-1 3-2z" class="M"></path><path d="M643 192h2 0c-1 2-3 3-4 4l6-3 1 1-1 1-4 2h-1c-2 1-2 2-2 3h-1v-3l-1-1c1-1 4-3 5-4z" class="Q"></path><path d="M638 196l1 1v3h1c0-1 0-2 2-3h1l-2 4c-1 1-1 2-1 4v2h0v3c1 1 2 2 3 4-1 0-2-2-3-1h0v1c-1-1-2-1-3-1s-1-1-2-2c0-5 0-9 2-13l1-2z" class="i"></path><path d="M661 200h1v2 1 2l1 1h1v1 2c-1 2-3 4-5 6 0 1-2 2-4 3-1 1-1 1-2 1l-3 1-1 1-1 1v-2-1h-4l-2-1c-1-1-2 0-3-1l-2-1 1-1-1-2c1 0 2 0 3 1v-1h0c1-1 2 1 3 1-1-2-2-3-3-4v-3c2 4 4 5 7 7h1 5 0c3-1 4-2 6-4 1-3 2-5 2-8v-2h0z" class="H"></path><path d="M640 214v-1h0c1-1 2 1 3 1-1-2-2-3-3-4v-3c2 4 4 5 7 7h1 5v1c1 0 0 0 1 1l-1-1c-1 1-2 1-3 2-2-1-3 0-5-1-1 0-2-1-2-1h-2l-1-1z" class="K"></path><path d="M662 205l1 1h1v1 2c-1 2-3 4-5 6 0 1-2 2-4 3-1 1-1 1-2 1l-3 1-1 1-1 1v-2-1c1-1 3-1 4-1 4-1 6-5 8-8 2-1 2-3 2-5z" class="i"></path><path d="M567 190l18-1h9c1 0 2 1 3 2h0l2 2c4 4 8 8 11 13l3 5c1-1 2-1 4-1h5v-1c3-2 3-2 4-5h-1c-1 0-1 1-2 1l-1-1c1-1 2-3 3-4 1 1 1 1 2 1h4c2-1 3-1 4-2s1-1 2-1c-2 4-2 8-2 13 1 1 1 2 2 2l1 2-1 1 2 1c1 1 2 0 3 1l2 1h4v1h-2l1 2 2 1 1-1c1 1 3 2 5 3 0 1 0 1-1 2h-1c-1-1-2-1-3-2 1 3 0 6-1 9-6-4-13-6-20-8l-21-5-8-1c-2 1-5 1-7 0h0l-4-4c-2 0-5-5-7-6-2-2-5-4-8-6l-3-2c-1 0-2 1-4 2v-1l2-2v-1l-2-1c-2-1-3-2-4-2-3-3-6-5-9-7h4 2 7z" class="b"></path><path d="M629 226s0-1-1-1c-3-1-6-2-8-4l1-1c1-1 1-1 2-1h2c2-1 4 0 6 0l14 4c1 1 4 2 5 2 1 3 0 6-1 9-6-4-13-6-20-8z" class="q"></path><path d="M631 201c2-1 3-1 4-2s1-1 2-1c-2 4-2 8-2 13 1 1 1 2 2 2l1 2-1 1 2 1c1 1 2 0 3 1l2 1h4v1h-2l1 2 2 1 1-1c1 1 3 2 5 3 0 1 0 1-1 2h-1c-1-1-2-1-3-2-1 0-4-1-5-2l-14-4c-5-1-9-2-13-3-1 0-1 0-2 1l2 5h0c-2-3-4-8-5-11 1-1 2-1 4-1h5v-1c3-2 3-2 4-5h-1c-1 0-1 1-2 1l-1-1c1-1 2-3 3-4 1 1 1 1 2 1h4z" class="Q"></path><path d="M633 217l-1-1c-1-1-1-1-2-1h-1v-3c-1-1-1 0 0-1 1 0 1 0 2 1l-1 1h1 2v-1l-1-2h-4c-1 0-1-1-2-2l2-2h1l-1 2 1 1c2 0 3 0 5-1 0 1 0 2 1 3h0c1 1 1 2 2 2l1 2-1 1 2 1c-2 0-2 1-3 1l-3-1z" class="L"></path><path d="M618 216l1-1h1s1 0 1 1c2 0 4 0 6 1h5c1 1 0 1 2 1l-1-1h0l3 1c1 0 1-1 3-1 1 1 2 0 3 1l2 1h4v1h-2l1 2 2 1 1-1c1 1 3 2 5 3 0 1 0 1-1 2h-1c-1-1-2-1-3-2-1 0-4-1-5-2l-14-4c-5-1-9-2-13-3z" class="V"></path><defs><linearGradient id="AF" x1="568.286" y1="209.628" x2="585.291" y2="199.325" xlink:href="#B"><stop offset="0" stop-color="#1c1d20"></stop><stop offset="1" stop-color="#3b3935"></stop></linearGradient></defs><path fill="url(#AF)" d="M567 190l18-1h9c1 0 2 1 3 2h0l2 2c4 4 8 8 11 13-3 0-4-3-6-4-1 0-2-1-3-2v-1h0v2c-2-1-3-2-4-4h-3v2h-1c1 1 2 2 2 3v1c1 1 3 3 4 5 1 0 1 1 2 2l2 3 3 6c1 0 1 1 2 2l-8-1c-2 1-5 1-7 0h0l-4-4c-2 0-5-5-7-6-2-2-5-4-8-6l-3-2c-1 0-2 1-4 2v-1l2-2v-1l-2-1c-2-1-3-2-4-2-3-3-6-5-9-7h4 2 7z"></path><path d="M574 204c1-2 1-2 0-3 2-1 4 1 5 2 3 2 5 4 7 6 1 2 5 5 5 7h-2c-2 0-5-5-7-6-2-2-5-4-8-6z" class="E"></path><defs><linearGradient id="AG" x1="580.831" y1="211.592" x2="601.805" y2="205.367" xlink:href="#B"><stop offset="0" stop-color="#b9b19b"></stop><stop offset="1" stop-color="#dad2c5"></stop></linearGradient></defs><path fill="url(#AG)" d="M576 198c2-1 3 0 5 0 1 0 3 0 5 1l1-1-2-2v-1l1 1c1 0 2 1 3 1 2 2 5 4 6 6 1 1 3 3 4 5 1 0 1 1 2 2l2 3 3 6c1 0 1 1 2 2l-8-1c-4-4-7-9-11-13s-8-7-13-9z"></path><path d="M567 190l18-1h9c1 0 2 1 3 2h0l2 2c4 4 8 8 11 13-3 0-4-3-6-4-1 0-2-1-3-2v-1h0v2c-2-1-3-2-4-4h-3v2h-1c1 1 2 2 2 3v1c-1-2-4-4-6-6-1 0-2-1-3-1l-1-1v1l2 2-1 1c-2-1-4-1-5-1-2 0-3-1-5 0l-8-4h0c-2 0-7-2-8-3v-1h7z" class="k"></path><path d="M567 190l18-1h9c1 0 2 1 3 2h0l2 2c-5-1-9 0-13-1 0 1 0 1-1 2l-1-1c-2-1-3 0-4 0-4 1-9 0-12 1h0c-2 0-7-2-8-3v-1h7z" class="V"></path><path d="M681 194h0l2 1-2 1 1 1h0c1-1 2-1 3-1h1l-1 1 1 1h3l5-1c-2 1-3 2-4 3s-2 1-3 2h0c2 0 3 0 4-1 3-2 8-1 11-1 2 1 4 2 5 3l2 1c1 0 1 0 2 1h3 1v-1h1l2 2c2 0 1-1 2-1 2 1 3 2 3 4 0 0 0 1 1 1v2c2 2 2 4 3 6 1 1 2 3 1 5h-1l1 1 1-2h1l-1 2 2 1v-1l1 1h1l1 1c1 1 2 3 3 4 1 2 1 3 2 4v8c1-1 1-1 2-1 1 3 0 8 0 12h1l-3 2-10 5c-1 0-3 0-4-1h0-1l1 3c2 2 2 5 4 6 2 7 3 14 4 22 0 1 0 6 1 8l1 8v5l-2 1c-6-15-15-28-24-41-5-6-10-12-17-17-1 2-2 4-4 6l-1-1c-4-4-8-7-12-10l-2-2c-1 0-3-2-5-3l-8-5-1-1-9-4h-1c1-3 2-6 1-9 1 1 2 1 3 2h1c1-1 1-1 1-2-2-1-4-2-5-3l-1 1-2-1-1-2h2v2l1-1 1-1 3-1c1 0 1 0 2-1 2-1 4-2 4-3 2-2 4-4 5-6 1-1 2-2 4-3l1 1v-1c1-2 1-3 2-4h1c0-1 1-1 1-2h1 0l2-3 5-3z" class="M"></path><path d="M719 283c1 1 3 2 4 2 1 2 1 2 1 3 0 3 2 5 3 7l3 6h-1l-10-18z" class="R"></path><path d="M711 272h1l2 2c1 1 2 1 3 2 0 2 2 4 3 5h0c1 1 2 3 3 4-1 0-3-1-4-2-1-3-6-8-8-11z" class="g"></path><path d="M675 249c1-2 3-4 4-6l12 8c-2 3-2 5-3 7l-1 1c-4-4-8-7-12-10zm-20-24v-1l2 1c1 0 1 0 2-1 0 1 1 3 2 3 1 1 2 0 3 1v2c-1 0-2 1-3 0l-2-1c-1 0-1 0-2 1l5 2c0 1 0 2-1 3 0 1-1 3-2 3l-9-4h-1c1-3 2-6 1-9 1 1 2 1 3 2h1c1-1 1-1 1-2z" class="e"></path><path d="M654 229l1 1h-1l1 2-2 1-1-1 2-3z" class="j"></path><path d="M651 234c1 0 2 0 3-1h3c2 1 3 1 4 2 0 1-1 3-2 3l-9-4h1z" class="Z"></path><path d="M657 233c2 1 3 1 4 2 0 1-1 3-2 3l-2-5z" class="U"></path><path d="M655 225v-1l2 1c1 0 1 0 2-1 0 1 1 3 2 3 1 1 2 0 3 1v2c-1 0-2 1-3 0l-2-1c-1 0-1 0-2 1-1-1-2-1-4-2h0c-2 1-2 4-2 6h-1-1c1-3 2-6 1-9 1 1 2 1 3 2h1c1-1 1-1 1-2z" class="L"></path><path d="M660 239l4-7c5 3 9 7 14 10l-5 5c-1 0-3-2-5-3l-8-5z" class="j"></path><path d="M664 209c1-1 2-2 4-3l1 1v-1c1-2 1-3 2-4h1l-1 2-1 3-1 1c0 1-1 2-1 3-1 1 0 2-1 4h1l1 2-1 1h0c-1 1-1 2-1 4l-2 3c1 2 2 2 2 4h-1l-2-1c-1-1-2 0-3-1-1 0-2-2-2-3-1 1-1 1-2 1l-2-1v1c-2-1-4-2-5-3l-1 1-2-1-1-2h2v2l1-1 1-1 3-1c1 0 1 0 2-1 2-1 4-2 4-3 2-2 4-4 5-6z" class="g"></path><path d="M668 215l1 2-1 1h0c-1 1-1 2-1 4l-2 3-1-1h-4c-1-1-1-2-2-2 2-2 4-1 6-2v-2c1 0 1 0 3 1 0-1 0-3 1-4z" class="i"></path><path d="M664 209c1-1 2-2 4-3l1 1v-1c1-2 1-3 2-4h1l-1 2-1 3-1 1c0 1-1 2-1 3-1 1 0 2-1 4h1c-1 1-1 3-1 4-2-1-2-1-3-1-3 1-4 2-7 2-1 1-1 0-1 0-1 0-5 1-7 1l1-1 3-1c1 0 1 0 2-1 2-1 4-2 4-3 2-2 4-4 5-6z" class="p"></path><path d="M702 250h2c2 0 2 0 4 1l-1 2 2 1-1 1h-1c1 2 2 2 2 3 1 2 5 4 5 6h-2v2l2 1 1-1c0 1 0 1 1 2l-1 1c-1 0-1 2-2 2-1 1-1 0-1 1h-1l-18-19v-1l1-1v-1h8z" class="J"></path><path d="M700 255l-3-2 1-1h2c1 1 3 2 4 4v1c-1 0-2-1-2 0-1-1-1-2-2-2z" class="h"></path><path d="M702 250h2l-1 2h-3-2l-1 1 3 2c-1 1 0 1-1 1s-2 0-3-1c0-1-1-2-2-2h-1v-1l1-1v-1h8z" class="K"></path><path d="M693 253h1c1 0 2 1 2 2 1 1 2 1 3 1s0 0 1-1c1 0 1 1 2 2 0 1 4 4 4 4l4 4v1l-1 1h1v1l2 2 1-1h2c-1 0-1 2-2 2-1 1-1 0-1 1h-1l-18-19z" class="H"></path><path d="M706 261l4 4v1l-1 1h1c0 1-1 1-1 2-1-2-3-4-5-6l2-2z" class="l"></path><defs><linearGradient id="AH" x1="713.529" y1="284.512" x2="731.561" y2="264.434" xlink:href="#B"><stop offset="0" stop-color="#958e7e"></stop><stop offset="1" stop-color="#bbb2a2"></stop></linearGradient></defs><path fill="url(#AH)" d="M710 252c2-2 2-2 4-2s5 0 6 2l1 2c1 1 1 3 3 5l1 3c2 2 2 5 4 6 2 7 3 14 4 22 0 1 0 6 1 8l1 8c-3-3-3-6-5-9l-6-9c0-1 0-1-1-3-1-1-2-3-3-4h0c-1-1-3-3-3-5-1-1-2-1-3-2l-2-2c0-1 0 0 1-1 1 0 1-2 2-2l1-1c-1-1-1-1-1-2l-1 1-2-1v-2h2c0-2-4-4-5-6 0-1-1-1-2-3h1l1-1-2-1 1-2 1-1v2h1z"></path><path d="M716 268c1 1 1 1 2 3l-1 1-3 2-2-2c0-1 0 0 1-1 1 0 1-2 2-2l1-1z" class="N"></path><path d="M710 252c2-2 2-2 4-2s5 0 6 2l1 2c1 1 1 3 3 5l1 3c-1 0-1-1-2-3 0 0-1-1-1-2-1-1-1-2-1-2-2 0-3 0-5-1l-2-3c-1 0-2 1-3 1h-1z" class="J"></path><path d="M714 274l3-2c1 0 2 0 3 1v1c1 2 6 8 5 11l-2-3c-1-1-2-1-3-1h0c-1-1-3-3-3-5-1-1-2-1-3-2z" class="H"></path><path d="M714 274l3-2c1 0 2 0 3 1l-3 3c-1-1-2-1-3-2z" class="W"></path><path d="M721 233l1 1c3-1 6-7 7-10l2 1v-1l1 1h1l1 1c1 1 2 3 3 4 1 2 1 3 2 4v8c1-1 1-1 2-1 1 3 0 8 0 12h1l-3 2-10 5c-1 0-3 0-4-1h0-1c-2-2-2-4-3-5l-1-2c-1-2-4-2-6-2s-2 0-4 2h-1v-2l-1 1c-2-1-2-1-4-1h-2l1-1c2-1 5-1 7-3h1c1-1 4-2 5-4h0v-1c0-2 0-2 1-4h1c0-1 1-2 1-3h1l1-1z" class="L"></path><path d="M729 256h2l4-1h1 2 1l-10 5c-1-1-1-2-1-3l1-1z" class="H"></path><path d="M725 240h1c1 0 2-1 2-1 2-1 4-1 6 0v1c1 1 2 2 2 3h0c-1-1-1-1-2-1 1 1 2 2 2 3h0-1c-2-2-3-3-6-3-2 0-3 1-5 2-1 1-2 2-3 4 0 2 2 5 4 7 1 1 2 1 4 1l-1 1c0 1 0 2 1 3-1 0-3 0-4-1h0-1c-2-2-2-4-3-5l1-1c-1-1-1-1-1-2h0l-1-1 1-1v-2h-1l2-2-1-1-2-1c1 0 1-1 2-1s2-1 4-2z" class="K"></path><path d="M730 253l3 1v-1c1-1 2-2 2-4 0-1 0-1 1-2h0v4c0 1 1 0 0 1 0 1-2 2-3 2h-1c-1 1-1 1-2 1-2 0-4-1-6-2-1-2-1-3-1-4 1-2 0-2 2-4h1c1-1 1-1 2-1h1c1-1 1-1 2 0l3 2c0 1 0 3-1 5 0 1-2 1-3 2z" class="l"></path><path d="M731 244l3 2c0 1 0 3-1 5 0 1-2 1-3 2h0-2 0l-2-1v-2h2c0 1 0 0 1 1l2-3 2 1v-1c0-2-1-2-2-3v-1h0z" class="K"></path><path d="M721 233l1 1c3-1 6-7 7-10l2 1v-1l1 1h1l1 1c1 1 2 3 3 4l-1 1 1 3c-1 0-1 1-1 2s1 2 2 3c-1 1-1 1-2 1-1-1-1-1-2 0v-1c-2-1-4-1-6 0 0 0-1 1-2 1h-1c-2 1-3 2-4 2s-1 1-2 1l2 1 1 1-2 2h1v2l-1 1 1 1h0c0 1 0 1 1 2l-1 1-1-2c-1-2-4-2-6-2s-2 0-4 2h-1v-2l-1 1c-2-1-2-1-4-1h-2l1-1c2-1 5-1 7-3h1c1-1 4-2 5-4h0v-1c0-2 0-2 1-4h1c0-1 1-2 1-3h1l1-1z" class="L"></path><defs><linearGradient id="AI" x1="735.531" y1="228.773" x2="729.031" y2="234.038" xlink:href="#B"><stop offset="0" stop-color="#6c6659"></stop><stop offset="1" stop-color="#827d72"></stop></linearGradient></defs><path fill="url(#AI)" d="M734 226c1 1 2 3 3 4l-1 1c-2 1-4 2-5 4-1-1-2-1-3-2l3-3h0 1 0 1l1-1v-1-2z"></path><path d="M719 243l2 1 1 1-2 2h1v2l-1 1 1 1h0l-6-3h-1v-1c0-1 1-2 2-2s2-1 3-2zm2-10l1 1c3-1 6-7 7-10l2 1c-3 5-10 14-15 17v-1c0-2 0-2 1-4h1c0-1 1-2 1-3h1l1-1z" class="H"></path><path d="M736 231l1 3c-1 0-1 1-1 2s1 2 2 3c-1 1-1 1-2 1-1-1-1-1-2 0v-1c-2-1-4-1-6 0 0 0-1 1-2 1h-1c-1-1-2-2-2-3l1-1c2 0 1 1 3 1 1-1 1-2 1-4 1 1 2 1 3 2 1-2 3-3 5-4z" class="g"></path><path d="M681 194h0l2 1-2 1 1 1h0c1-1 2-1 3-1h1l-1 1 1 1h3l5-1c-2 1-3 2-4 3s-2 1-3 2c-3 2-4 4-5 8h0c-1 2-1 4 0 6h-2l-1 1c3 1 5 2 7 3h2c1 1 2 2 2 3h-1v1l1 1h1c1 1 1 1 2 1h1l1 1c1 0 1-1 1-2h1l1 3c0 1 1 2 1 2v1c1 1 2 2 4 3 1 1 2 1 3 2 0 1 0 1 1 1l2 1h1v2l-1 2v1l-2 3c-4 2-12 3-16 2l-1-1h-1c-2 0-1 0-3-1h-2c-1-1-3-2-3-4 1 1 2 1 3 2 2 1 2 2 4 2-1-1-5-4-6-5-2-1-1-1-2 0l-2-2c-7-5-10-9-11-17 0-2 0-3 1-4h0l1-1-1-2h-1c1-2 0-3 1-4 0-1 1-2 1-3l1-1 1-3 1-2c0-1 1-1 1-2h1 0l2-3 5-3z" class="H"></path><path d="M689 247v-1h0 1c2-1 4 0 6 0h0c2-1 3 0 4 0l1-1c1 0 3 0 4-1 2-1 2-1 4-1l-2 3c-4 2-12 3-16 2l-1-1h-1z" class="K"></path><path d="M679 217c3 1 5 2 7 3h2c1 1 2 2 2 3h-1v1l1 1h-2 1l1 1-1 1c-1 0-1 0-1-1l-1 1 1 1-1 1-2-2h-1l-1 1c0 1 2 0 0 2 0 1-1 2-2 2v1h-2l-1-3h-1l-1 1h1c0 2 0 3 1 5v1c-1-1-3-2-4-3-2-1-2-2-3-3v-1l-1-1h1l-1-2 1-1 3 4v-1c-1-1-1-2-2-3l1-1 1 2 1-1c-1-1-1-1-1-2v-1l3 3v-1c-1-2-1-2-1-3l1-1v-1-1l2-1v-1z" class="i"></path><path d="M677 221c1 1 3 1 5 2s4 2 6 2h1l1 1-1 1c-1 0-1 0-1-1l-1 1 1 1-1 1-2-2h-1-3c-1-1-2-1-3-1 1 1 1 1 1 2l-1 1-1-3v-1c-1-2-1-2-1-3l1-1z" class="G"></path><path d="M679 217c3 1 5 2 7 3h2c1 1 2 2 2 3h-1v1l1 1h-2c-2 0-4-1-6-2s-4-1-5-2v-1-1l2-1v-1z" class="F"></path><path d="M677 220c2 1 4 1 6 2 2 0 4 0 6 1v1l1 1h-2c-2 0-4-1-6-2s-4-1-5-2v-1z" class="f"></path><path d="M683 234h0c3-4 8-3 11-4 2 0 4 1 5 1 1 1 2 2 4 3 1 1 2 1 3 2 0 1 0 1 1 1l2 1h1v2l-1 2h-2c-3 1-6 2-9 1h-5c-1 0-1-1-2-2-1 0-3 0-4-1h-1c-2-1-4-3-5-4 0-1 1-1 2-2z" class="R"></path><path d="M683 234h0c3-4 8-3 11-4 2 0 4 1 5 1 1 1 2 2 4 3 1 1 2 1 3 2 0 1 0 1 1 1 0 1 0 3 1 4h-1 0c0-1-1-2-2-2v-1-1c-1-2-2-2-4-2l-1-1c-3 0-7 0-9-1h-2c-1-1-3 0-3 0-1 1-2 1-3 1z" class="G"></path><path d="M691 241c3 0 10 1 12 0 0-1-1-1-1-1 0-1-8 0-10 0-1 0-2-1-3-1l-1-2c0-1 1-2 1-2h1c0 1 1 1 1 2 1 1 1 0 2 1v1h1c0-2 0-2-1-2h1l7-1h1c-1 0-1 0-1-1 2 0 3 0 4 2v1 1c1 0 2 1 2 2h0 1c-1-1-1-3-1-4l2 1h1v2l-1 2h-2c-3 1-6 2-9 1h-5c-1 0-1-1-2-2z" class="g"></path><path d="M701 235c2 0 3 0 4 2v1c-1 1-1 1-2 1-1-1-2-1-3-1l1-2h1c-1 0-1 0-1-1z" class="l"></path><path d="M681 194h0l2 1-2 1 1 1h0c1-1 2-1 3-1h1l-1 1 1 1h3l5-1c-2 1-3 2-4 3s-2 1-3 2c-3 2-4 4-5 8h0c-1 2-1 4 0 6h-2l-1 1v1l-2 1v1 1l-1 1c0 1 0 1 1 3v1l-3-3v1c0 1 0 1 1 2l-1 1-1-2-1 1c1 1 1 2 2 3v1l-3-4-1 1 1 2h-1c-2-2-2-8-2-11h0l1-1-1-2h-1c1-2 0-3 1-4 0-1 1-2 1-3l1-1 1-3 1-2c0-1 1-1 1-2h1 0l2-3 5-3z" class="c"></path><path d="M675 211h1c0 3-1 7 0 11 0 1 0 1 1 3v1l-3-3c1-4 0-8 1-12z" class="W"></path><path d="M676 211c1 0 1-1 2-2l1 1h1c-1 2-2 3-2 5 0 1 0 2 1 3l-2 1v1 1l-1 1c-1-4 0-8 0-11z" class="K"></path><path d="M681 194h0l2 1-2 1 1 1h0c1-1 2-1 3-1h1l-1 1 1 1h3l5-1c-2 1-3 2-4 3s-2 1-3 2c-3 2-4 4-5 8h0c-1 2-1 4 0 6h-2l-1 1v1c-1-1-1-2-1-3 0-2 1-3 2-5h-1l-1-1c-1 1-1 2-2 2h-1v-1l-1-1c0-1 0-2 1-4l1-2-1-1-3 6h-1c0-3 3-5 3-7v-1h0l2-3 5-3z" class="L"></path><path d="M675 210c0-2 1-4 2-6h1c1 2 0 3 0 5-1 1-1 2-2 2h-1v-1z" class="H"></path><path d="M685 197l1 1h3l5-1c-2 1-3 2-4 3s-2 1-3 2c-3 2-4 4-5 8h0c-1 2-1 4 0 6h-2l-1 1v1c-1-1-1-2-1-3 0-2 1-3 2-5h0c1-2 1-4 3-6h0v-1c-1 0-1 1-2 1v-1l-1-1 1-1c0-2 1-2 2-3l2-1z" class="p"></path><path d="M685 197l1 1h3l-6 4v-3-1l2-1z" class="C"></path><path d="M687 202h0c2 0 3 0 4-1 3-2 8-1 11-1 2 1 4 2 5 3l2 1c1 0 1 0 2 1h3 1v-1h1l2 2c2 0 1-1 2-1 2 1 3 2 3 4 0 0 0 1 1 1v2c2 2 2 4 3 6 1 1 2 3 1 5h-1l1 1 1-2h1l-1 2c-1 3-4 9-7 10l-1-1-1 1h-1c0 1-1 2-1 3h-1c-1 2-1 2-1 4v1h0c-3 2-6 3-9 4l2-3v-1l1-2v-2h-1l-2-1c-1 0-1 0-1-1-1-1-2-1-3-2-2-1-3-2-4-3v-1s-1-1-1-2l-1-3h-1c0 1 0 2-1 2l-1-1h-1c-1 0-1 0-2-1h-1l-1-1v-1h1c0-1-1-2-2-3h-2c-2-1-4-2-7-3l1-1h2c-1-2-1-4 0-6h0c1-4 2-6 5-8z" class="c"></path><path d="M684 213l1-1c2 0 2 1 3 2v1h-3l-1-2z" class="M"></path><path d="M707 223l-2-1c1-1 1-2 2-3h1l-1-2c0-1 1-1 2-2 2 0 1 0 3 1-1 3-1 5-5 7h0z" class="H"></path><path d="M688 205c2-3 6-4 9-4s5 0 7 1l1 1c2 1 3 2 5 4-2 0-2-1-3-2l-1 1 1 1c-1 0-2-1-3-1-1-1-4 0-5 0h-1-3c-2 0-4 0-6 1h0 0c1-1 1-2 2-3-1 0-2 1-3 1h0z" class="Q"></path><path d="M693 218v-1h0l1 1h1v-1l2-2 2 2c1 1 1 2 2 4 0 1 1 2 3 3v1h-4-3-1c0 1 0 2-1 2l-1-1h-1c-1 0-1 0-2-1h-1l-1-1v-1h1c0-1-1-2-2-3h1 1c1 0 2-1 3-2z" class="U"></path><path d="M693 218v-1h0l1 1h1v-1l2-2 2 2-1 4c-1 1-2 1-3 1l-1-1c0-1 0-1-1-2v-1z" class="J"></path><path d="M687 202h0c2 0 3 0 4-1 3-2 8-1 11-1 2 1 4 2 5 3l2 1c1 0 1 0 2 1h3 1l1 1h-1l-1 1s0 1 1 1l-2 1c1 1 2 3 2 4h-2c0 1 1 3 0 4 0 2-2 4-4 5-1 1-1 2-2 3h-2-5 4v-1s2-1 3-1h0c4-2 4-4 5-7h0c0-4 0-6-2-9-2-2-3-3-5-4l-1-1c-2-1-4-1-7-1s-7 1-9 4c-3 1-4 4-4 7v1l1 2 3 4c1 0 1 0 1 1h-1-2c-2-1-4-2-7-3l1-1h2c-1-2-1-4 0-6h0c1-4 2-6 5-8z" class="W"></path><path d="M709 204c1 0 1 0 2 1h3 1l1 1h-1l-1 1s0 1 1 1l-2 1c1 1 2 3 2 4h-2v-1c0-1 0-2-1-3h0c-1-2-2-4-3-5z" class="R"></path><path d="M715 204h1l2 2c2 0 1-1 2-1 2 1 3 2 3 4 0 0 0 1 1 1v2c2 2 2 4 3 6 1 1 2 3 1 5h-1l1 1 1-2h1l-1 2c-1 3-4 9-7 10l-1-1-1 1h-1c0 1-1 2-1 3h-1c-1 2-1 2-1 4v1h0c-3 2-6 3-9 4l2-3v-1l1-2v-2h-1l-2-1c-1 0-1 0-1-1-1-1-2-1-3-2-2-1-3-2-4-3v-1s-1-1-1-2l-1-3h3 5 2c1-1 1-2 2-3 2-1 4-3 4-5 1-1 0-3 0-4h2c0-1-1-3-2-4l2-1c-1 0-1-1-1-1l1-1h1l-1-1v-1z" class="L"></path><path d="M719 230l3 1c-1 1-1 1-1 2l-1 1h-1c-1-1-2-1-4-1l4-3z" class="g"></path><path d="M715 233c-2 0-3-1-4-2l1-1h7l-4 3z" class="R"></path><path d="M716 217l1-1h1c1 1 0 2 1 2h2v1c-1 0-1 1-2 1s-1 1-2 1c-1 1-2 2-3 4h1v1h-2-2c2-1 4-3 5-5 1-1 0-2 0-4z" class="H"></path><g class="R"><path d="M717 221v1c0 1 0 2-1 3 2 0 3-1 5-1v2h2l3-2h0l1-1 1 1 1-2h1l-1 2c-1 3-4 9-7 10l-1-1c0-1 0-1 1-2l1-1-1-1c-1-1-7 0-8 0h-1c-3-1-5-2-8-2h0l1-1h1c1 0 2 1 3 1h2 4l-1-1v-1h-1c1-2 2-3 3-4z"></path><path d="M697 225h3 5l-1 1c1 2 2 3 3 4l6 5c1 0 3 1 4 2-1 2-1 2-1 4v1h0c-3 2-6 3-9 4l2-3v-1l1-2v-2h-1l-2-1c-1 0-1 0-1-1-1-1-2-1-3-2-2-1-3-2-4-3v-1s-1-1-1-2l-1-3z"></path></g><path d="M704 230l-2-3h1l1-1c1 2 2 3 3 4h-3z" class="K"></path><path d="M707 230l6 5v3l1 1-1 1c-1-1-1-1-2-1l-1 1v-2c-1-3-4-6-6-8h3z" class="l"></path><path d="M710 240l1-1c1 0 1 0 2 1l1-1-1-1v-3c1 0 3 1 4 2-1 2-1 2-1 4v1h0c-3 2-6 3-9 4l2-3v-1l1-2z" class="f"></path><defs><linearGradient id="AJ" x1="723.775" y1="215.106" x2="714.145" y2="221.852" xlink:href="#B"><stop offset="0" stop-color="#7b766b"></stop><stop offset="1" stop-color="#918c7d"></stop></linearGradient></defs><path fill="url(#AJ)" d="M715 204h1l2 2c2 0 1-1 2-1 2 1 3 2 3 4 0 0 0 1 1 1v2c2 2 2 4 3 6 1 1 2 3 1 5h-1l-1 1h0l-3 2h-2v-2c-2 0-3 1-5 1 1-1 1-2 1-3v-1c1 0 1-1 2-1s1-1 2-1v-1h-2c-1 0 0-1-1-2h-1l-1 1c-1-1-1-3-1-4s-1-3-2-4l2-1c-1 0-1-1-1-1l1-1h1l-1-1v-1z"></path><path d="M715 204h1l2 2c2 0 1-1 2-1 2 1 3 2 3 4 0 0 0 1 1 1v2c2 2 2 4 3 6l-1 1c0-1-1-2-1-3-1-2-2-2-3-2h-1c-1-1-2-2-3-2l-1-1c-2 0-2-2-2-3-1 0-1-1-1-1l1-1h1l-1-1v-1z" class="Q"></path><path d="M715 204h1l2 2c2 0 1-1 2-1 2 1 3 2 3 4 0 0 0 1 1 1v2h0c-3-1-5-2-7-4l-1-2-1-1v-1z" class="M"></path><path d="M453 520l9 1h3 1l1 1c2 1 2 1 3 3 3 10 7 17 15 24-1 1-1 2-1 3h1v2c2 0 3 0 4 1h0 1c3 1 6 3 9 3h1c3 1 8 1 11 1l1 1 2 1v1h-1 0-2l2 3h-1c0 2 1 3 1 4v3 1l-1 2v2 6c-1 4-1 8-1 12v11h1v1-1h1v10 11c0 3-1 11 1 13 1 1 2 1 3 1h3v3c0 4-1 10 0 14h2c0 1-1 1-2 2l1 2v-1h1v2c0 2 0 3 1 5v2 12 2c0 2 1 4 1 6v1c2 3 1 7 2 10v1l1 1v1h0l6 13c0 2 0 3 1 4v2l1 4 1 3v1l-1 1h-9s-1-1-2-1v-1-1l-1-1-1 1v1h-2-1-7 0-2-1c-1 0-3 0-5-1v-4h-1v4h-1l-4 1h-11-4c-1 0-3 0-4 1 0-1 0-1-1-2-1 1-1 1-1 2h0c-1-1-1-3-1-4l6-15v-3l1-7c1-3 1-7 1-10 0-1-1-2-2-4-1-1-3-1-4-2h-2l2-3-2-2c-1 1-2 1-3 3v1h-1v1h-3c-2-1-2-1-3-2h-1v2l-1-1v-2c-2 0-2 0-3 2v-7l-7 3-2 1v-13-19-7-10c1-8 0-17 1-25l-1-11 1-11-1-64v-2h1z" class="e"></path><path d="M461 644c0 1 1 2 1 3l4 1v2h-1c-1 0-2 0-3 1l-1 3v-10z" class="C"></path><path d="M453 586c0 1 0 2 1 2v2c0 6 0 13-1 18h0l-1-11 1-11z" class="K"></path><path d="M463 635h2v7l1 6h0l-4-1c0-1-1-2-1-3v-5l-1 1-1-1c1 0 1-1 2-1v-1l2-2z" class="P"></path><path d="M452 522h9v2c-2 0-4 0-6-1v38 18c0 4 0 8-1 11v-2c-1 0-1-1-1-2l-1-64z" class="J"></path><defs><linearGradient id="AK" x1="467.961" y1="533.233" x2="457.349" y2="537.268" xlink:href="#B"><stop offset="0" stop-color="#272625"></stop><stop offset="1" stop-color="#403f3f"></stop></linearGradient></defs><path fill="url(#AK)" d="M453 520l9 1h3 1l-1 1 1 3c1 3 2 8 3 12v2c-1 2 0 3 1 5h0v1l-4 3c-2 1-3 2-5 3v-27-2h-9v-2h1z"></path><path d="M453 608h0v21c0 4 1 9 1 13v1h1 0l-2 2 1 1c0 1 0 4-1 6v3c0 2-1 8 0 10 0 1 0 1 1 2v13c2-1 6-2 7-4v-2l2-1c1 0 1 0 3 1h2c0-1 1-1 2-1 1-1 2-1 3-2l4-1h3l-1 2c-1 1-1 1 0 2v1h2c1 0 2-1 3-1-1 3-3 5-4 8-1 0-1 1-2 1l-2-2c-1 1-2 1-3 3v1h-1v1h-3c-2-1-2-1-3-2h-1v2l-1-1v-2c-2 0-2 0-3 2v-7l-7 3-2 1v-13-19-7-10c1-8 0-17 1-25z" class="D"></path><path d="M479 672c-1 1-1 1 0 2v1h2c1 0 2-1 3-1-1 3-3 5-4 8-1 0-1 1-2 1l-2-2c-1 1-2 1-3 3v1h-1v1h-3c-2-1-2-1-3-2h-1c0-2 1-1 1-2l-1-2c0-1 0-2 1-3 2-3 7-3 11-5h2z" class="E"></path><path d="M466 684c1-2 1-2 3-3h2c1 1 1 2 1 3v2h-3c-2-1-2-1-3-2z" class="G"></path><path d="M481 675c1 0 2-1 3-1-1 3-3 5-4 8-1-1-2-2-2-3v-1l-1 1c-1 1-2 1-4 1 1-1 2-1 3-2l3-3h2z" class="N"></path><path d="M479 672c-1 1-1 1 0 2v1l-3 3c-1 1-2 1-3 2h-2-3-1v-2l-1-1c2-3 7-3 11-5h2z" class="G"></path><path d="M479 672c-1 1-1 1 0 2v1l-3 3v-2c1-2 1-2 1-4h2z" class="F"></path><defs><linearGradient id="AL" x1="478.331" y1="541.065" x2="462.417" y2="550.233" xlink:href="#B"><stop offset="0" stop-color="#070706"></stop><stop offset="1" stop-color="#4d4d4c"></stop></linearGradient></defs><path fill="url(#AL)" d="M466 521l1 1c2 1 2 1 3 3 3 10 7 17 15 24-1 1-1 2-1 3-1 3-1 6-1 8-2 5-4 9-6 13l-2 3c0 1-1 1-2 2-1-2-1-2-3-2l-2-2 2 3c-1 1-3 3-5 4-1 2-2 3-4 4v-12-20-2c2-1 3-2 5-3l4-3v-1h0c-1-2-2-3-1-5v-2c-1-4-2-9-3-12l-1-3 1-1z"></path><path d="M472 557c1 1 1 2 0 4v1h0c0 2-1 3 0 4h1 0l-1 1h-1v-5h-1c0-1 2-5 2-5z" class="D"></path><path d="M473 543v7l1 1 1-1c0 3 0 5-1 8v1c0-2 0-3-1-5 0 1-1 1-1 3 0 0-2 4-2 5l-1 3c-1-1 0-2 0-3 1-1 1-1 1-2v-1c1-3 2-9 1-11l-1-2 3-3z" class="Y"></path><path d="M468 574c-1 0 0 0-1-1-2-3-3-5-2-8l3-3h0 0 1c0 1-1 2 0 3l1-3h1v5h1l1-1c2-2 1-3 4-4l1 1c-1 4-1 6-1 10l-2 3c0 1-1 1-2 2-1-2-1-2-3-2l-2-2z" class="F"></path><path d="M470 545c1-1 2-3 3-4v2l-3 3 1 2c1 2 0 8-1 11v1c0 1 0 1-1 2h-1 0 0l-3 3c-1 3 0 5 2 8 1 1 0 1 1 1l2 3c-1 1-3 3-5 4-1 2-2 3-4 4v-12-20-2c2-1 3-2 5-3l4-3z" class="D"></path><path d="M461 551c2-1 3-2 5-3v1c0 1 0 2-1 3v2 1l-1 1v-1c-1-2 0 0-2-1v-1h-1v-2z" class="C"></path><path d="M461 551c2-1 3-2 5-3v1l-4 4h-1v-2z" class="P"></path><path d="M461 573c2 1 3 2 4 4 0 1 0 2-1 4v1l1-1h0c-1 2-2 3-4 4v-12z" class="E"></path><path d="M487 618h0c1-1 0-6 0-8s1-4 1-6l1 2c2 5 0 12 1 17v18 2 5 8h0v15 5 24h-1c-1 0-1-1-2-1l-3 11-2 2v-3l1-7c1-3 1-7 1-10 0-1-1-2-2-4-1-1-3-1-4-2h-2l2-3c1 0 1-1 2-1 1-3 3-5 4-8-1 0-2 1-3 1h-2v-1c-1-1-1-1 0-2l1-2h-3l-4 1c-1 1-2 1-3 2-1 0-2 0-2 1h-2c-2-1-2-1-3-1l-2 1v-7-13l1-3c1-1 2-1 3-1h1v-2h0c1 0 1 1 1 2h2 1c2-1 5-3 6-5h-1l-1-1 3-1c1-1 0-2 0-3l-2-3h2l2-1 1-1c0-1 1-2 2-3 0-3 0-6 1-9h0 1l1-3c1 0 1-1 2-2z" class="R"></path><path d="M487 629c0 4 1 9 1 13-1-1-1-1-1-3-1 0-1-1-2-2h0 1c1-3 0-6 1-8zm1 16c1 1 1 2 1 3s-1 2 0 3v9h-1v-5c-1-1-1-3-1-5v-4l1-1z" class="V"></path><path d="M484 662v-1-2l1-1h0l1-1c2 2 0 4 1 6s2 0 2 3v2c-1 1-1 2 0 3v2l-1 1-1-1-1 1h0c0-1 0-1-1-2 1-2 1-4 1-5 0-2 0-3-1-4l-1-1z" class="Q"></path><path d="M485 620l2 1v8c-1 2 0 5-1 8h-1 0c-2 0-2 1-4 0v-1l-1-1c0-1 1-2 2-3 0-3 0-6 1-9h0 1l1-3z" class="N"></path><path d="M483 623h1c0 4 0 9 1 12v1 1h0c-2 0-2 1-4 0v-1l-1-1c0-1 1-2 2-3 0-3 0-6 1-9h0z" class="i"></path><path d="M480 635l1 1v1c2 1 2 0 4 0 1 1 1 2 2 2 0 2 0 2 1 3v3l-1 1h-1l-2-1s-1 0-2-1h0-2c-1 0-2 0-3-1 1-1 0-2 0-3l-2-3h2l2-1 1-1z" class="Q"></path><path d="M477 640c3 1 5 3 7 5 0 0-1 0-2-1h0-2c-1 0-2 0-3-1 1-1 0-2 0-3z" class="P"></path><path d="M480 635l1 1v1c2 1 2 0 4 0 1 1 1 2 2 2l-3 1-2-2c-1 0-1 0-2 1h0l-3-2 2-1 1-1z" class="l"></path><path d="M485 672c1 1 1 1 1 2h0c1 2 1 3 1 6 0 6 1 13 0 19l-3 11-2 2v-3l1-7c1-3 1-7 1-10 0-1-1-2-2-4-1-1-3-1-4-2h-2l2-3c1 0 1-1 2-1 1-3 3-5 4-8l1-2z" class="D"></path><path d="M485 672c1 1 1 1 1 2h0c1 2 1 3 1 6-1 1-2 2-4 2h0-2l-1 1h1c1 0 2 1 3 1 2 3 1 7 1 10-1 0-1-1-1-2s-1-2-2-4c-1-1-3-1-4-2h-2l2-3c1 0 1-1 2-1 1-3 3-5 4-8l1-2z" class="W"></path><path d="M486 674h0c1 2 1 3 1 6-1 1-2 2-4 2h0-2c1-3 3-6 5-8z" class="B"></path><path d="M467 650h2 1c2-1 5-3 6-5h-1l-1-1 3-1c1 1 2 1 3 1h2 0c1 1 2 1 2 1l2 1c0 3 0 4-2 6l-1 1c-1 1-2 2-4 3l1-2-1-3-3 3-8 5-1 1h-1l-1 1v-3s1-1 1-2h-1v-1l2-2v-3z" class="F"></path><path d="M480 644h2 0c1 1 2 1 2 1l2 1c0 3 0 4-2 6l-1 1-2-2 1-2-2-5z" class="B"></path><path d="M468 659v-4h2l1-2c-1 0-1 0-1-1 2-1 3-1 5-3l1-1 2 2c1 0 1-1 2-2l1 1v1l-2 1-3 3-8 5z" class="G"></path><path d="M479 651l1 3-1 2h-1c1 1 1 1 2 1 1 1 3 3 4 5l1 1c1 1 1 2 1 4 0 1 0 3-1 5l-1 2c-1 0-2 1-3 1h-2v-1c-1-1-1-1 0-2l1-2h-3l2-1c0-1-1-2-1-3v-1h-1-1l1-2-1-1h-1c-1 0-1-1-2-1l2-2v-1h-1c-2 1-4 1-6 2l-1 1v-1l1-1 8-5 3-3z" class="D"></path><path d="M485 663c1 1 1 2 1 4l-4 2-2 1h-3l2-1c2-2 2-4 3-6h3z" class="B"></path><path d="M482 669l4-2c0 1 0 3-1 5l-1 2c-1 0-2 1-3 1h-2v-1c-1-1-1-1 0-2l1-2 2-1z" class="D"></path><path d="M482 669v1c0 2-1 3-1 5h-2v-1c-1-1-1-1 0-2l1-2 2-1z" class="G"></path><defs><linearGradient id="AM" x1="458.892" y1="660.081" x2="477.877" y2="666.958" xlink:href="#B"><stop offset="0" stop-color="#393938"></stop><stop offset="1" stop-color="#5e5d5b"></stop></linearGradient></defs><path fill="url(#AM)" d="M466 648c1 0 1 1 1 2v3l-2 2v1h1c0 1-1 2-1 2v3l1-1h1v1l1-1c2-1 4-1 6-2h1v1l-2 2c1 0 1 1 2 1h1l1 1-1 2h1 1v1c0 1 1 2 1 3l-2 1-4 1c-1 1-2 1-3 2-1 0-2 0-2 1h-2c-2-1-2-1-3-1l-2 1v-7-13l1-3c1-1 2-1 3-1h1v-2h0z"></path><path d="M476 662l1 1-1 2h1 1v1c0 1 1 2 1 3l-2 1-4 1 2-1h1v-1h1 1c-1-2-1-2-3-3 0-1 0-2 1-4z" class="E"></path><path d="M468 574l2 2c2 0 2 0 3 2 1-1 2-1 2-2 1 1 1 2 1 3 2 3 2 3 2 7l-1 1c1 0 1 3 1 4-1 0-1 0-1 1h2l1 4-1 1 1 1h0c2 2 3 2 5 2h1 1c1 1 1 2 1 4s-1 4-1 6 1 7 0 8h0c-1 1-1 2-2 2l-1 3h-1 0c-1 3-1 6-1 9-1 1-2 2-2 3l-1 1-2 1h-2l2 3c0 1 1 2 0 3l-3 1 1 1h1c-1 2-4 4-6 5h-1-2c0-1 0-2-1-2l-1-6v-7h-2l-2 2v-14-11-6l1-12-1-2v-7c2-1 3-2 4-4 2-1 4-3 5-4l-2-3z" class="C"></path><path d="M481 607c0 1 1 2 1 3 0 2 0 2-2 3s-4 2-5 3c1-3 3-6 6-9z" class="O"></path><path d="M476 602v2h2v1c-1 0-1 0-2 2-4 5-11 8-11 15h-2l-1-2c5-6 8-13 14-18z" class="P"></path><path d="M482 610c0 1 1 2 1 2l-1 4-1 1-2-2c-2 3-4 6-6 8-2 1-2 1-3 3l-1 1-1 1c-1 2-2 4-3 7h0-2c3-6 7-14 12-19 1-1 3-2 5-3s2-1 2-3z" class="B"></path><path d="M464 604c2-1 3-1 4-3 1-3 3-5 6-7l-1 3h1c1 1 1 4 2 5-6 5-9 12-14 18 0 1-1 2-1 3v-11-6c1 0 2-1 3-2z" class="G"></path><path d="M464 604c2-1 3-1 4-3 1-3 3-5 6-7l-1 3c-2 4-6 7-8 11-1 1-2 4-4 4v-6c1 0 2-1 3-2z" class="Y"></path><defs><linearGradient id="AN" x1="484.386" y1="620.904" x2="473.772" y2="626.656" xlink:href="#B"><stop offset="0" stop-color="#333432"></stop><stop offset="1" stop-color="#575656"></stop></linearGradient></defs><path fill="url(#AN)" d="M483 612v11h0c-1 3-1 6-1 9-1 1-2 2-2 3l-1 1-2 1h-2v-1c0-1-1-2-1-3-1-1-3-4-2-6h0c1 0 1 0 1-1h1l-1-1v-2c2-2 4-5 6-8l2 2 1-1 1-4z"></path><path d="M472 627h2-1l1 1v1h1c1 1 1 2 1 3 1 1 1 1 1 2 1 0 1 1 1 1v1h1l-2 1h-2v-1c0-1-1-2-1-3-1-1-3-4-2-6h0z" class="M"></path><path d="M481 624c0-1 0-1 1-2l1 1c-1 3-1 6-1 9-1 1-2 2-2 3l-1 1h-1l1-10h0 1c0-1 1-1 1-2z" class="D"></path><path d="M481 624c0-1 0-1 1-2l1 1c-1 3-1 6-1 9l-1-1c0-1-1-3-1-3 0-1 1-1 1-2v-2z" class="P"></path><path d="M474 594h0l1-1v-2c0-1 1-1 1-2s1-2 1-2c1 0 1 3 1 4-1 0-1 0-1 1h2l1 4-1 1 1 1h0c2 2 3 2 5 2h1 1c1 1 1 2 1 4s-1 4-1 6 1 7 0 8h0c-1 1-1 2-2 2l-1 3h-1v-11s-1-1-1-2-1-2-1-3l-3-2v-1h-2v-2c-1-1-1-4-2-5h-1l1-3z" class="K"></path><path d="M480 598h0c2 2 3 2 5 2-1 1-1 1-2 1l-1 1c-1 0-3 0-4-1 1-1 0-2 2-3z" class="J"></path><path d="M486 618v-1c0-1-1-1-1-2l-1-1v-3l-1-1c0-2-2-3-2-5l1-1c1 1 1 2 1 3 1 1 2 1 2 2 0 2 0 1 1 2v7z" class="R"></path><path d="M474 594h0l1-1v-2c0-1 1-1 1-2s1-2 1-2c1 0 1 3 1 4-1 0-1 0-1 1h2l1 4-1 1c-1-2-1-3-1-4-1 1-1 1-1 2v1c0 3 0 5 1 8h-2v-2c-1-1-1-4-2-5h-1l1-3z" class="C"></path><path d="M486 600h1c1 1 1 2 1 4s-1 4-1 6 1 7 0 8h0-1v-7c-1-1-1 0-1-2 0-1-1-1-2-2 0-1 0-2-1-3 1-2 1-1 3-1 0 0 1-1 1-2v-1z" class="V"></path><path d="M465 635c1-3 2-5 3-7l1-1 1-1c1-2 1-2 3-3v2l1 1h-1c0 1 0 1-1 1h0c-1 2 1 5 2 6 0 1 1 2 1 3v1l2 3c0 1 1 2 0 3l-3 1 1 1h1c-1 2-4 4-6 5h-1-2c0-1 0-2-1-2l-1-6v-7h0z" class="E"></path><path d="M465 635h0 0c2 1 3 1 4 1h1l-1 1c-1 0-2 1-2 2l-1-1c0 2 0 2-1 4v-7z" class="C"></path><path d="M468 574l2 2c2 0 2 0 3 2 1-1 2-1 2-2 1 1 1 2 1 3 2 3 2 3 2 7l-1 1s-1 1-1 2-1 1-1 2v2l-1 1h0c-3 2-5 4-6 7-1 2-2 2-4 3-1 1-2 2-3 2l1-12-1-2v-7c2-1 3-2 4-4 2-1 4-3 5-4l-2-3z" class="V"></path><path d="M466 590h0c3 0 5-1 7-2l1 1c-1 0 0 1-1 2-2-1-3 0-4 1-2 0-3 1-4 2h-3l-1-2c2-1 3-1 5-2z" class="P"></path><path d="M466 590c3-2 8-5 12-4l-1 1s-1 1-1 2-1 1-1 2v2l-1 1h0c-3 2-5 4-6 7-1 2-2 2-4 3 3-5 7-9 9-13 1-1 0-2 1-2l-1-1c-2 1-4 2-7 2h0z" class="I"></path><path d="M468 574l2 2c2 0 2 0 3 2 1-1 2-1 2-2 1 1 1 2 1 3 2 3 2 3 2 7-4-1-9 2-12 4-2 1-3 1-5 2v-7c2-1 3-2 4-4 2-1 4-3 5-4l-2-3z" class="Q"></path><path d="M469 582v1c0 1 0 2-1 3 0 1-1 0-2 1l-1-1c0-3 2-3 4-4z" class="c"></path><path d="M484 552h1v2c2 0 3 0 4 1h0 1c3 1 6 3 9 3h1c3 1 8 1 11 1l1 1 2 1v1h-1 0-2l2 3h-1c0 2 1 3 1 4v3 1l-1 2v2 6c-1 4-1 8-1 12v11l-1-1h0l-1 1v-1c-2 1-4 1-5 3 0-2-1-2-2-3v40c-1-2-1-5-1-7-2 0-3 0-5 1 1 1 2 1 4 1v1c0 1 0 2-1 2l2 2c-1 1-2 1-3 2v-1h-3l-2 2h-2-1v-5-2-18c-1-5 1-12-1-17l-1-2c0-2 0-3-1-4h-1-1c-2 0-3 0-5-2h0l-1-1 1-1-1-4h-2c0-1 0-1 1-1 0-1 0-4-1-4l1-1c0-4 0-4-2-7 0-1 0-2-1-3l2-3c2-4 4-8 6-13 0-2 0-5 1-8z" class="a"></path><path d="M492 576l3-1v3h0c-1 1-1 3-2 5h0v1h0c0-3 0-5-1-8z" class="Q"></path><path d="M488 586v1h1c0 6 1 13 0 19l-1-2c0-2 0-3-1-4l1-14z" class="H"></path><path d="M488 559l1 1v5 22h-1v-1l-1-2c1-3 1-5 1-7-1-6 0-12 0-18z" class="K"></path><path d="M493 566l2-1v2c0 1 0 1 1 2v2h1v-1l1 4c0 1 0 2 1 3l-2 2c0 2 0 3-1 5l-1-2v-4h0v-3l-3 1c0-1 1-3 1-5 0-1 1-3 0-5z" class="E"></path><path d="M493 571l2 1v3l-3 1c0-1 1-3 1-5z" class="V"></path><path d="M491 623v-5c0-1-1-2 0-3v-6l-1-1c2-4 1-9 0-14s0-11 0-17h1v5c1 5-1 10 1 14 0 3-1 5 1 7v4l-1 2h1c0 2 0 3-1 4h2v3h0l1 1c-1 2 0 6-1 7l-3-1z" class="L"></path><path d="M493 584v-1h0c1-2 1-4 2-5v4l1 2c0 3 0 7 1 10v2c-1 0-1 1-2 2-1 2 0 6 0 9v2 1 2l-1 1h-2c1-1 1-2 1-4h-1l1-2v-4c0-3-1-7-1-9l1-10z" class="c"></path><path d="M495 582l1 2c0 3 0 7 1 10v2c-1 0-1 1-2 2v-16z" class="F"></path><path d="M484 552h1v2c2 0 3 0 4 1h0c-1 1-1 2-1 4h0c0 6-1 12 0 18 0 2 0 4-1 7l1 2-1 14h-1-1c-2 0-3 0-5-2h0l-1-1 1-1-1-4h-2c0-1 0-1 1-1 0-1 0-4-1-4l1-1c0-4 0-4-2-7 0-1 0-2-1-3l2-3c2-4 4-8 6-13 0-2 0-5 1-8z" class="g"></path><path d="M486 583l1 1 1 2-1 14h-1-1c-2 0-3 0-5-2 2-3 0-9 2-13 1-2 2-2 4-2z" class="m"></path><path d="M484 552h1v2c2 0 3 0 4 1h0c-1 1-1 2-1 4h0c0 6-1 12 0 18 0 2 0 4-1 7l-1-1h1v-3h0l-1-2h-3-1v-2c0-2 1-3 3-5 1 0 0 0 1-1v-2-1c-1-1-1-2-1-3v-1l-2 2v4h0c-1 1-1 1-1 2-1-1-2-1-2-2v-1c1-1 2-2 3-4v-4c0-2 0-5 1-8z" class="G"></path><path d="M498 574h2v1 1c1 1 1 2 1 3h0c0-2 0-3 1-4v21 8 1 40c-1-2-1-5-1-7-2 0-3 0-5 1 1 1 2 1 4 1v1c0 1 0 2-1 2l2 2c-1 1-2 1-3 2v-1h-3l-2 2h-2c0-2-1-4 0-6v-10-9l3 1c1-1 0-5 1-7l-1-1h0v-3l1-1v-2-1-2c0-3-1-7 0-9 1-1 1-2 2-2v-2c-1-3-1-7-1-10 1-2 1-3 1-5l2-2c-1-1-1-2-1-3z" class="Q"></path><path d="M499 580l1 2v9l-1-2v-1c0-1-1-1-1-2 0-2 0-4 1-6z" class="F"></path><path d="M491 642h1 1v-2h1l1 1 1-1c0 1 0 1 1 2h0l1 1v1h-1l-2 2-2 2h-2c0-2-1-4 0-6z" class="L"></path><path d="M498 574h2v1 1c1 1 1 2 1 3h0c0-2 0-3 1-4v21 8 1 40c-1-2-1-5-1-7-1-6-1-13-1-20 0-4 1-11 0-15 0-1 0-2 1-2l-1-1c0-1 0-1-1-2 1-2 1-3 0-4 1-1 1-1 1-2v-1-9l-1-2-2-1 2-2c-1-1-1-2-1-3z" class="N"></path><path d="M498 574h2v1 7l-1-2-2-1 2-2c-1-1-1-2-1-3z" class="G"></path><path d="M488 559h0c0-2 0-3 1-4h1c3 1 6 3 9 3h1c3 1 8 1 11 1l1 1 2 1v1h-1 0-2l2 3h-1c0 2 1 3 1 4v3 1l-1 2v2 6c-1 4-1 8-1 12v11l-1-1h0l-1 1v-1c-2 1-4 1-5 3 0-2-1-2-2-3v-1-8-21c-1 1-1 2-1 4h0c0-1 0-2-1-3v-1-1h-2l-1-4v1h-1v-2c-1-1-1-1-1-2v-2l-2 1-1-1c-1 0-1 0-1-1h-1 0l-1 1v-5l-1-1z" class="Q"></path><path d="M491 560c1 0 3 1 5 1v1l-2 1c-1 0-2 0-3-1v-2z" class="R"></path><path d="M510 569c1 0 2 2 3 3v1h-3v-3-1z" class="L"></path><path d="M497 570l1-1v-3l-1-1v-1c2 1 5 6 5 7v4c-1 1-1 2-1 4h0c0-1 0-2-1-3v-1-1h-2l-1-4z" class="Y"></path><path d="M490 555c3 1 6 3 9 3h1c3 1 8 1 11 1l1 1 2 1v1h-1 0-2l2 3h-1c0 2 1 3 1 4v3c-1-1-2-3-3-3v1l-3 1c-2 0-3 0-4-1s-2-3-2-4v-4h-1v2h-1v-3l-1 1h-1c-1-4-5-4-7-7z" class="G"></path><path d="M509 567c-2 0-3 0-4 1h0l-3-2c0-2 0-2 2-4 1 0 2-1 4 0h3 0l2 3h-1c0 2 1 3 1 4v3c-1-1-2-3-3-3l-1-1-1 1h-1l2-2z" class="Y"></path><path d="M509 567l1-1v1h1v-2h1c0 2 1 3 1 4v3c-1-1-2-3-3-3l-1-1-1 1h-1l2-2z" class="F"></path><path d="M502 604h1v-2h1c-1-2-1-4-1-5l1-1c-1-3 0-7 0-11 0-1-1-1-1-2s0-3 1-3c-1-2-1-6-1-8l1-1v1 3 1l1 1 2-2h0 5v2 6c-1 4-1 8-1 12v11l-1-1h0l-1 1v-1c-2 1-4 1-5 3 0-2-1-2-2-3v-1z" class="V"></path><path d="M507 582h0c2 0 2 1 2 2 0 2 1 6 0 8-1-1-1 0-1-1-1 1-1 1-2 1l-2-2c1-2 1-5 1-7l2-1z" class="S"></path><path d="M509 605v-1h-1-3l-1-1c2 0 3 0 4-1-1-1-2 0-3 0l-1-1c1-1 2-1 2-3h-2c1-1 1-1 1-2h-1v-1c1-1 2-1 3-1h3v5c0 1-1 2 0 3v2 1l-1 1v-1z" class="B"></path><path d="M510 605h0l1 1h1v1-1h1v10 11c0 3-1 11 1 13 1 1 2 1 3 1h3v3c0 4-1 10 0 14h2c0 1-1 1-2 2l1 2v-1h1v2c0 2 0 3 1 5v2 12 2c0 2 1 4 1 6v1c2 3 1 7 2 10v1l1 1v1h0l6 13c0 2 0 3 1 4v2l1 4 1 3v1l-1 1h-9s-1-1-2-1v-1-1l-1-1-1 1v1h-2-1-7 0-2-1c-1 0-3 0-5-1v-4h-1v4h-1l-4 1h-11-4c-1 0-3 0-4 1 0-1 0-1-1-2-1 1-1 1-1 2h0c-1-1-1-3-1-4l6-15 2-2 3-11c1 0 1 1 2 1h1v-24-5-15h0v-8h1 2l2-2h3v1c1-1 2-1 3-2l-2-2c1 0 1-1 1-2v-1c-2 0-3 0-4-1 2-1 3-1 5-1 0 2 0 5 1 7v-40c1 1 2 1 2 3 1-2 3-2 5-3v1l1-1z" class="Z"></path><path d="M504 725c1-1 3-1 5-1 1 2 1 4 0 6-1 0-3 0-5-1v-4z" class="T"></path><path d="M506 727h1l1 1v1h-3l1-2z" class="S"></path><path d="M511 705c2 2 0 4 2 7h-1c1 1 1 1 2 1v1h-1v1h2l-1 1h-1c0 1 0 1 1 1l-2 2h1 0c1 1 1 1 2 1l-1 2-2-1v1l1 1c1 0 1 1 1 2l2 1 2-1c1 2 2 3 2 5h-1-7 0l-1-1v-24z" class="P"></path><path d="M512 730l3-3h2l2 3h-7z" class="V"></path><path d="M514 717h2c1-1 2 0 4 0h0l1 1h0v1c1 1 1 1 2 1l1 2h1v2l2 1-2 1c-1 1-1 2-1 3l-1-1-1 1v1h-2c0-2-1-3-2-5l-2 1-2-1c0-1 0-2-1-2l-1-1v-1l2 1 1-2c-1 0-1 0-2-1h0-1l2-2z" class="C"></path><path d="M520 717l1 1h0v1c1 1 1 1 2 1l1 2h1v2l-1-1-1 1h-1c-2-2-2-3-5-4 1 0 2-1 3-1v-2h0z" class="T"></path><defs><linearGradient id="AO" x1="506.211" y1="630.376" x2="523.761" y2="634.107" xlink:href="#B"><stop offset="0" stop-color="#333235"></stop><stop offset="1" stop-color="#67655c"></stop></linearGradient></defs><path fill="url(#AO)" d="M511 606h1v1-1h1v10 11c0 3-1 11 1 13 1 1 2 1 3 1h3v3c0 4-1 10 0 14l-1 1h0c-1 0-2 1-3 1h-1l-1-2-2 1h0c-1 1 0 1-1 2v-55z"></path><path d="M517 641h3v3c-2 1-2 1-4 1-1 0-1 0-2 1 0-2-1-3 0-4l3-1z" class="V"></path><path d="M504 608c1-2 3-2 5-3v1 87 19 6l-1 1c0 1 2 2 1 3s-3 1-4 1c-2-2-1-5-1-7v-23-85z" class="m"></path><path d="M520 658h2c0 1-1 1-2 2l1 2v-1h1v2c0 2 0 3 1 5v2 12 2c0 2 1 4 1 6v1c2 3 1 7 2 10v1l1 1v1h0l6 13c0 2 0 3 1 4v2l1 4 1 3v1l-1 1h-9s-1-1-2-1v-1-1c0-1 0-2 1-3l2-1-2-1v-2h-1l-1-2c-1 0-1 0-2-1v-1h0l-1-1h0c-2 0-3-1-4 0h-2c-1 0-1 0-1-1h1l1-1h-2v-1h1v-1c-1 0-1 0-2-1h1c-2-3 0-5-2-7-1-4 0-9 0-13v-31c1-1 0-1 1-2h0l2-1 1 2h1c1 0 2-1 3-1h0l1-1z" class="M"></path><path d="M522 678h-1c-1 1-6 1-8 1v-3c3 0 6 0 9 1v1z" class="Q"></path><path d="M524 691c2 3 1 7 2 10v1l1 1-1 1v3h-1v-1c-1-1 0-1-1-2v-13z" class="H"></path><path d="M524 690h-1v-4c0 1 0 4-1 4l-1 1v1c-1 0-1-1-1-2v-4l-1-1v-1h4c0 2 1 4 1 6z" class="G"></path><path d="M522 677l-2-2c1-2 0 0 2-1v-1c-1 0-1 0-2-1v-1l1-1 1 1v-1l-1-2c0-3-2-6-1-8l1 2v-1h1v2c0 2 0 3 1 5v2 12c-1-2-1-2-1-4v-1z" class="E"></path><path d="M520 717v-1-2h0v-1-1-3h0l1-2 1 2c-1 0-1 1-1 2h1l1-1-1-2h1v-5-5c1 1 0 7 1 9v1 14l-1-2c-1 0-1 0-2-1v-1h0l-1-1z" class="C"></path><path d="M527 703v1 3c0 2 1 4 1 5 0 2 0 3 1 4 0 1-1 1-2 2v3h0l-2-1v1h1l-1 1h-1v-14-4c1 1 0 1 1 2v1h1v-3l1-1z" class="W"></path><path d="M525 720c1-3 0-10 2-13 0 2 1 4 1 5 0 2 0 3 1 4 0 1-1 1-2 2v3h0l-2-1z" class="X"></path><path d="M527 704h0l6 13c0 2 0 3 1 4v2l1 4 1 3v1l-1 1h-9s-1-1-2-1v-1-1c0-1 0-2 1-3l2-1-2-1v-2l1-1h-1v-1l2 1h0v-3c1-1 2-1 2-2-1-1-1-2-1-4 0-1-1-3-1-5v-3z" class="K"></path><path d="M529 716c1 2 0 2 1 3s1 2 1 3c0 2 1 3 2 5h-2v-2l-1-1c-1 0-2 1-3 1l-2-1v-2l1-1h-1v-1l2 1h0v-3c1-1 2-1 2-2z" class="J"></path><path d="M526 721c1 1 2 1 4 2v1c-1 0-2 1-3 1l-2-1v-2l1-1z" class="a"></path><path d="M527 725c1 0 2-1 3-1l1 1v2h2c0 2 1 2 3 3v1l-1 1h-9s-1-1-2-1v-1-1c0-1 0-2 1-3l2-1z" class="B"></path><path d="M527 725c1 0 2-1 3-1l1 1c-2 1-3 3-4 3s-2-1-2-2l2-1z" class="S"></path><path d="M531 727h2c0 2 1 2 3 3v1l-1 1h-9s-1-1-2-1v-1c2 0 7 0 8-1l-1-2z" class="j"></path><path d="M501 645l-2-2c1 0 1-1 1-2v-1c-2 0-3 0-4-1 2-1 3-1 5-1 0 2 0 5 1 7v49h0v18c0 3 0 6 1 9v3 1 4h-1l-4 1h-11-4c-1 0-3 0-4 1 0-1 0-1-1-2-1 1-1 1-1 2h0c-1-1-1-3-1-4l6-15 2-2 3-11c1 0 1 1 2 1h1v-24-5-15h0v-8h1 2l2-2h3v1c1-1 2-1 3-2z" class="F"></path><path d="M500 666c1 0 1 1 1 2h-1c-1 0-2-1-2-2h2z" class="p"></path><path d="M491 693l2 6c0 2 1 3 0 5 0-1 0-1-1-2-1-2-1-7-1-9z" class="E"></path><path d="M501 645v4 1c-1 2-1 6 0 8v5c-1 1-2 1-3 1l-1-1h-1c-1 1-3 1-5 1v-2l2-2h0l-2-1c0-2 0-2-1-3h0v-8h1 2l2-2h3v1c1-1 2-1 3-2z" class="p"></path><path d="M501 645v4c-2 0-4 0-6 1v-1h-4c0 2 1 5-1 7v-8h1 2l2-2h3v1c1-1 2-1 3-2z" class="V"></path><path d="M490 676c2 3 1 9 1 12 0 2 0 3 1 4l-1 1c0 2 0 7 1 9 1 1 1 1 1 2l-1 1 1 1h8c0-4-1-9 1-13v1 18c0 3 0 6 1 9v3 1 4h-1l-4 1h-11-4c-1 0-3 0-4 1 0-1 0-1-1-2-1 1-1 1-1 2h0c-1-1-1-3-1-4l6-15 2-2 3-11c1 0 1 1 2 1h1v-24z" class="C"></path><path d="M503 721v3 1 4h-1l-4 1c0-2 0-3 1-5v-1-1c1-1 2-2 4-2z" class="P"></path><path d="M503 724v1 4h-1c0-1-1-2-2-3v-1c0-1 2-1 3-1z" class="C"></path><path d="M483 730v-1c1-2 1-3 1-5h0c1-1 1-1 3-2l2 1 1 1h0v2c1 1 0 2 0 3h-3v1h-4z" class="B"></path><path d="M487 699c1 0 1 1 2 1h1l-1 21v1h-2c-2 1-2 1-3 2h0c0 2 0 3-1 5v1c-1 0-3 0-4 1 0-1 0-1-1-2-1 1-1 1-1 2h0c-1-1-1-3-1-4l6-15 2-2 3-11z" class="U"></path><path d="M482 712l2-2h0c0 4-3 7-2 10 3 1 5 0 7 1v1h-2c-2 1-2 1-3 2h0c0 2 0 3-1 5v1c-1 0-3 0-4 1 0-1 0-1-1-2-1 1-1 1-1 2h0c-1-1-1-3-1-4l6-15z" class="R"></path><path d="M382 179l1 1 1-1h6v3l1-1v-2h1 24v1c0 2-1 4-1 6h1c6 1 14 1 19 1h15 29 10 5c1-1 0-1 1-1h1v1h1c3-1 8 0 11 0h4 17l1 1c0 1 1 1 3 1h0l5 1c2 0 3 0 5-1 3 0 8 1 11 1 3 2 6 4 9 7 1 0 2 1 4 2l2 1v1l-2 2v1c2-1 3-2 4-2l3 2c3 2 6 4 8 6 2 1 5 6 7 6l4 4h0l-13 1c-2 2-6 4-7 7l-1 1c-2 0-2 2-3 3-1-2-1-2-2-3h-2c-2 1-3 2-4 3 0-1 0-1 1-2 0-1 1-1 1-2h0c-1 1-2 2-3 4l-1-1h0c-6 11-10 26-18 36l-11 13c0-1-1-3-1-4h0l-1-1c0-1-1-2-1-3h-1c1 0 2-1 2-2-1-1-1-3-1-4 1-2 3-3 3-5l-1-1-1-1-2 2-3 2h-1l-2 2v1c0 1 0 2-1 3v3c-1-3-2-6-2-8 1-1 1-2 2-4h0l-2 1-2 2c-1 8 0 15 1 23 0 1 0 5 1 6l-1 2h0c1 2 1 2 1 3 0 2 1 3 2 4v2h0c-1-1-1-2-1-3h-1 0v1c1 2 1 3 1 5 0 1 0 2 1 3h0c0 2 1 4 1 6l-1 2 3 5c-3 1-6 1-8 2-2-1-4 0-5 0h-10l-5-1 1 1v2h-8v-8-4l-3-1v5c0 2 0 5-1 7v-13l-2 1-1-1c1-3 1-3-1-5h0c1-1 1-4 1-5-1-1-1 0-1-2 0-1 0-1-1-2v-3c1-2 0-4-1-6h0l-1-2v-1c-1 0-1 0-2-1v-1h-1 0l-1-1c-1-1-1-1-2-1-2 1-1 0-2 1-1 0-2 1-2 1h-2c0-1-2-1-2-1l-1-2c1-3 0-5 0-8l-2-11v-1c-2-14-10-34-22-42v-1h-1c0-1-1-1-1-1-1-1-1-2-3-2-1-1-3-2-5-3 0-1-1-1-2-1v-1c-2-1-4-1-5-2l-5-2-1 1h-4c-2-1-5-1-7-1-4 0-8-1-12 0h-1l-1-1h2v-1c-1-1-3-1-5 0-7 0-14 3-20 7-3 2-6 5-10 7 0 1-1 1-2 1s0 0-1 1c1-6 5-12 8-16 2-3 3-4 5-7 0-1 0-2 1-3l4-4 3-4c1-2 2-3 3-4l3-3h0 2z" class="I"></path><path d="M508 187h4 17l1 1c0 1 1 1 3 1l-2 1c-2 0-2-1-3-2s-2 0-3 0c-5 0-13 1-17-1z" class="O"></path><path d="M425 191c4-2 9 0 13 0h11c0 1 1 1 1 2h2l-3 3-1-1c-1-1-2-1-4-1-3-1-6-1-9-1-3-2-4-2-7-2l1 1h0c-1 0-2-1-4-1z" class="T"></path><path d="M429 192h0l-1-1c3 0 4 0 7 2 3 0 6 0 9 1 2 0 3 0 4 1l1 1-2 1c-1 1-2 1-2 3l-16-8z" class="N"></path><path d="M497 190c2 0 5 0 7 1l3-1c2 1 4 0 6 0h13c3 1 9 2 12 4l4 2c-1 0-3 1-5 1h-2c-2-1-4-1-6-1l-1 1c-4-3-9-2-14-2h-1c-1 0-2 0-2-1-2-1-3-1-4-2-1 1-2 1-3 1s-1 1-3 1c-1-2-1-2-3-3l-1-1z" class="B"></path><path d="M507 192c1-1 1-1 3-1v1h5 0c0 1 0 2-1 3h-1c-1 0-2 0-2-1-2-1-3-1-4-2z" class="P"></path><path d="M515 192c2 0 4 1 6 1 1 0 3-1 4 0 4 1 3 2 7 1h4 2l4 2c-1 0-3 1-5 1h-2c-2-1-4-1-6-1l-1 1c-4-3-9-2-14-2 1-1 1-2 1-3z" class="D"></path><path d="M452 193l1-1c3-3 10-2 14-2h9l-5 2-1 1c2 1 3 1 5 1l-2 1c-1 0-3 1-4 2-2 0-3 0-4 1-2 0-4 2-5 2h-1c-2 2-4 2-7 3h-2c-2-1-3-2-5-3 0-2 1-2 2-3l2-1 3-3z" class="c"></path><path d="M460 200c3-3 7-6 10-7 2 1 3 1 5 1l-2 1c-1 0-3 1-4 2-2 0-3 0-4 1-2 0-4 2-5 2z" class="Y"></path><path d="M447 197c1 0 2 2 2 2h6 2 0c1 1 1 1 2 1-2 2-4 2-7 3h-2c-2-1-3-2-5-3 0-2 1-2 2-3z" class="i"></path><defs><linearGradient id="AP" x1="549.036" y1="191.575" x2="547.812" y2="200.541" xlink:href="#B"><stop offset="0" stop-color="#5c5b59"></stop><stop offset="1" stop-color="#7a7971"></stop></linearGradient></defs><path fill="url(#AP)" d="M533 189h0l5 1c2 0 3 0 5-1 3 0 8 1 11 1 3 2 6 4 9 7 1 0 2 1 4 2l2 1v1l-2 2v1l-1 1c-5 1-10 5-14 7-1 0-2 0-3 1h0c0-2-1-2-2-3l1-2c1 0 1-1 2-1h0v-1c1-1 2-2 4-3h0c1 0 1 0 1-1-7-5-15-9-22-13z"></path><path d="M538 190c2 0 3 0 5-1 3 0 8 1 11 1 3 2 6 4 9 7 1 0 2 1 4 2l-3 1c-2-1-3-1-5-2s-4-2-7-3c-3-2-8-3-11-5h-3z" class="G"></path><path d="M538 190c2 0 3 0 5-1 3 0 8 1 11 1 3 2 6 4 9 7l-2-1c-3 0-6-2-8-3-4-2-8-2-12-3h-3z" class="C"></path><path d="M559 198c2 1 3 1 5 2l3-1 2 1v1l-2 2v1l-1 1c-5 1-10 5-14 7-1 0-2 0-3 1h0c0-2-1-2-2-3l1-2c1 0 1-1 2-1h0v-1c1-1 2-2 4-3h0l1 2c2-1 3-1 5-3h2c-1-2-2-3-3-4z" class="p"></path><path d="M507 192c1 1 2 1 4 2 0 1 1 1 2 1h1c5 0 10-1 14 2l1-1c2 0 4 0 6 1h2c2 0 4-1 5-1l11 6v1h1c-2 1-3 2-4 3v1h0c-1 0-1 1-2 1l-1 2c1 1 2 1 2 3h0c1-1 2-1 3-1-1 1-3 2-4 4l1 2h0l-2-1v-1-1l-2 2c0 1-2 1-3 2l3-3v-2l-1-1-1-3c-2 0-4 0-5-1-1 0-1-1-2-1s0 1-1-1c-2 0-2 0-3 1 0-1-1-1-1-1-1 0-1 0-2 1-2 1-4 1-6 1h-3c-1 0-1 0-2 1v1c0 1 0 1-1 2 0 1-1 1-1 3 0 1 0 1 1 2l1 1c-1 1-1 0-2 0h-3v1c1 2 1 4 1 6-1-2-1-5-2-7-1-1-1-1-2-1-1 1 0 4-1 5v-8c-1-5-1-9-1-14 0-2 0-4-1-6v-3z" class="G"></path><path d="M512 198h-1v-1l1-1c1 0 1 1 2 1l1 1-1 1-2-1z" class="E"></path><path d="M512 198l2 1 3 3c1 2 1 3 3 5h0v1c-1-1-2-1-3-1-2-1-4-3-6-5 0-2 0-2 1-4z" class="F"></path><path d="M514 195c5 0 10-1 14 2l1-1c2 0 4 0 6 1h-5v2h0v1c-1-1-1-1-1-2l-1-1-1 1 1 1v2c-1 0-1-2-2-1l-1 1c-2 0-4-1-6 1 0 1 1 2 1 4v1h0c-2-2-2-3-3-5l1-1v-2l1-1-1-1-1 2-1-1c0-1 0-1 1-2-1 0-2-1-3-1z" class="E"></path><path d="M509 215v-7c3 1 5 1 7 1h1c-1 1-1 2-1 3h1v1c0 1-1 1-1 3 0 1 0 1 1 2l1 1c-1 1-1 0-2 0h-3v1c1 2 1 4 1 6-1-2-1-5-2-7-1-1-1-1-2-1-1 1 0 4-1 5v-8z" class="C"></path><path d="M542 196l11 6v1h1c-2 1-3 2-4 3v1h0c-1 0-1 1-2 1l-1 2c1 1 2 1 2 3h0c1-1 2-1 3-1-1 1-3 2-4 4l1 2h0l-2-1v-1-1l-2 2c0 1-2 1-3 2l3-3v-2l-1-1-1-3c-2 0-4 0-5-1-1 0-1-1-2-1s0 1-1-1h0 1l-1-1c-2-1-3-1-5 0h-1v-4l-1-1v-2l-1-1 1-1 1 1c0 1 0 1 1 2v-1h0v-2h5 2c2 0 4-1 5-1z" class="F"></path><path d="M547 204h0l1 1c0 1 0 1-1 2l-2-2 2-1z" class="p"></path><path d="M542 196l11 6v1h1c-2 1-3 2-4 3l-1-2v-1l-2 1h0l-2-2c-1-1-1-2-3-2-2-1-3-2-5-3 2 0 4-1 5-1z" class="C"></path><defs><linearGradient id="AQ" x1="514.296" y1="230.887" x2="531.667" y2="214.679" xlink:href="#B"><stop offset="0" stop-color="#4d4c4c"></stop><stop offset="1" stop-color="#6a6966"></stop></linearGradient></defs><path fill="url(#AQ)" d="M523 209c2 0 4 0 6-1 1-1 1-1 2-1 0 0 1 0 1 1 1-1 1-1 3-1 1 2 0 1 1 1s1 1 2 1c1 1 3 1 5 1l1 3 1 1v2l-3 3c1-1 3-1 3-2l2-2v1 1c-1 1-2 3-3 5-1 1-1 2-2 3l-9 21c-2 3-4 5-6 7l-8 7-2 1v-1c1-2 1-3 0-5v-1s1 1 2 1h0 1c0-1-1-2-1-4l1 1c1 0 1 1 2 1 0-1 0-1 1-1h1c0-1 0-1-1-2l-1-1v-1-1c0-1-1-2-1-3v-1c-1-2-1-4-2-6-1-3-4-7-5-11 0-2 0-4-1-6v-1h3c1 0 1 1 2 0l-1-1c-1-1-1-1-1-2 0-2 1-2 1-3 1-1 1-1 1-2v-1c1-1 1-1 2-1h3z"></path><path d="M521 224h2l1 1-1 1v1l-1-1c0-1-1-1-1-2z" class="G"></path><path d="M536 219c2 0 3 0 5 1-2 3-2 4-5 6v2c0-1 0-1-1-2h0l-1-1h1c-1 0-2 0-3-1v-1h-2l-1-1h2v-1l1-1h1 3 0v-1z" class="V"></path><path d="M523 209c2 0 4 0 6-1 1-1 1-1 2-1 0 0 1 0 1 1 1-1 1-1 3-1 1 2 0 1 1 1s1 1 2 1c1 1 3 1 5 1l1 3 1 1v2l-3 3-1 1c-2-1-3-1-5-1-1-1-2-2-3-2h0c-1-1-1-1-1-2l1-1h-1c-1-1-1-1-2 0l-1-2c-1 0-1 0-2 1h0c-1 0-2-1-3-1l-1-3z" class="Q"></path><path d="M544 213l1 1v2c-1 0-1-1-2-1l1-2z" class="L"></path><path d="M542 219c1-1 3-1 3-2l2-2v1 1c-1 1-2 3-3 5-1 1-1 2-2 3l-9 21c-2 3-4 5-6 7l-8 7-2 1v-1c1-2 1-3 0-5v-1s1 1 2 1h0 1c0-1-1-2-1-4l1 1c1 0 1 1 2 1 0-1 0-1 1-1h1c0-1 0-1-1-2l-1-1v-1-1c0-1-1-2-1-3v-1l1-1-1-2v-1h2 0v-1l1-1h1l1-1h0 1 0l1-1c-1 0-1-1-2-1v-1c-1-1-1-1-1-2-1-1 0-1-1-2h0c1-1 1-2 2-2h1c1 1 1 1 1 2 2 0 2 0 3 2h1c0-1 2-2 3-2l1 1v-1l1-1h-1v-2c3-2 3-3 5-6l1-1z" class="G"></path><path d="M523 239c1 0 2 1 2 1h1v1l-1 1 1 1h1 1c0-1 0-2 1-2h0c0 1 0 1-1 2h0c1 1 1 1 1 2 2-2 3-4 4-6v-1l1 1h1c0-1 0-2 1-2h0v-2c1-2 1-3 2-4 1-2 2-6 4-6l-9 21c-2 3-4 5-6 7l-8 7-2 1v-1c1-2 1-3 0-5v-1s1 1 2 1h0 1c0-1-1-2-1-4l1 1c1 0 1 1 2 1 0-1 0-1 1-1h1c0-1 0-1-1-2l-1-1v-1-1c0-1-1-2-1-3v-1l1-1-1-2v-1h2 0z" class="C"></path><path d="M509 223c1-1 0-4 1-5 1 0 1 0 2 1 1 2 1 5 2 7 1 4 4 8 5 11 1 2 1 4 2 6v1c0 1 1 2 1 3v1 1l1 1c1 1 1 1 1 2h-1c-1 0-1 0-1 1-1 0-1-1-2-1l-1-1c0 2 1 3 1 4h-1 0c-1 0-2-1-2-1v1c1 2 1 3 0 5v1l-2 2c-1 8 0 15 1 23 0 1 0 5 1 6l-1 2h0c1 2 1 2 1 3 0 2 1 3 2 4v2h0c-1-1-1-2-1-3h-1 0v1c1 2 1 3 1 5 0 1 0 2 1 3h0c0 2 1 4 1 6l-1 2 3 5c-3 1-6 1-8 2-2-1-4 0-5 0h-10l-5-1 1 1v2h-8v-8-4l-1-1-1-1s1-1 2-1c2-1 5-2 7-3h0l2-2h1c1-1 1-1 1-2v-1h2v1h1c-1-1-1-2 0-3 0-2 0-2 2-3h2l-1-1c-1 0-1-1-2-1v-4h1v-1h-2c1-3 1-5 1-8h0c0-1-1-2-1-3 2-7 1-15 2-22 0-4 0-9 1-13 2 0 4 1 5 0h2l-1-8v-13l-1-1z" class="K"></path><path d="M515 303l2-2c1 2 1 3 1 5 0 1 0 2 1 3h0c0 2 1 4 1 6l-1 2-4-14z" class="S"></path><path d="M510 247h1v1l-1 1 1 41-2 1c1 1 2 1 3 1v2c0 1 0 1-2 2-1 1-3 1-4 1h-2c-1 0-1-1-2-1v-4h1v-1c0-2 0-4 1-7v2c1 1 1 0 2 1 1-1 2-1 3-2v-3-1c1-2 0-4 0-6v-12-1c0-4-1-12 1-15z" class="j"></path><path d="M511 245v2h-1c-2 3-1 11-1 15v1 12c0 2 1 4 0 6v1 3c-1 1-2 1-3 2-1-1-1 0-2-1v-2c-1 3-1 5-1 7h-2c1-3 1-5 1-8h0c0-1-1-2-1-3 2-7 1-15 2-22 0-4 0-9 1-13 2 0 4 1 5 0h2z" class="b"></path><path d="M511 245v2h-1c-2 0-4-1-5 0h-1c1 1 1 1 1 2 1 1 0 4 0 5 0 6 0 12-1 19v1-5c0-1-1-1 0-2v-4c-1-2-1-4-1-5 0-4 0-9 1-13 2 0 4 1 5 0h2z" class="f"></path><path d="M501 280c2-7 1-15 2-22 0 1 0 3 1 5v4c-1 1 0 1 0 2v5c0 3-1 7 0 10-1 3-1 5-1 7h-2c1-3 1-5 1-8h0c0-1-1-2-1-3z" class="K"></path><path d="M509 223c1-1 0-4 1-5 1 0 1 0 2 1 1 2 1 5 2 7 1 4 4 8 5 11 1 2 1 4 2 6v1c0 1 1 2 1 3v1 1l1 1c1 1 1 1 1 2h-1c-1 0-1 0-1 1-1 0-1-1-2-1l-1-1c0 2 1 3 1 4h-1 0c-1 0-2-1-2-1v1c1 2 1 3 0 5v1l-2 2c-1 8 0 15 1 23 0 1 0 5 1 6l-1 2h0c1 2 1 2 1 3 0 2 1 3 2 4v2h0c-1-1-1-2-1-3h-1 0v1l-2 2v-1c0-2-1-3-2-4v-1h2c0-2-1-3-1-5 0-12-1-23-2-35l-1-9v-1-2l-1-8v-13l-1-1z" class="I"></path><path d="M509 223c1-1 0-4 1-5 1 0 1 0 2 1 1 2 1 5 2 7 1 4 4 8 5 11 1 2 1 4 2 6v1c0 1 1 2 1 3v1 1l1 1c1 1 1 1 1 2h-1c-1 0-1 0-1 1-1 0-1-1-2-1v-1c0-1-1-2-1-3l-3-6-2-9-1-1v-2c-1-2-1-4-3-5v-3h1 1v-1c0-1 0-2-1-2-1 1-1 3-1 4v1l-1-1z" class="P"></path><path d="M519 248c0 1 1 2 1 3v1l-1-1c0 2 1 3 1 4h-1 0c-1 0-2-1-2-1v1c1 2 1 3 0 5v1l-2 2c-1 8 0 15 1 23 0 1 0 5 1 6l-1 2h0c1 2 1 2 1 3 0 2 1 3 2 4v2h0c-1-1-1-2-1-3h-1 0v1l-2 2v-1c0-2-1-3-2-4v-1h2c0-2-1-3-1-5 0-12-1-23-2-35h2v1l-1 1v2c0 4 0 8 1 11v-1c0-2-1-7 1-9h0c0-2 0-3 1-4 1 0 1-1 1-1-1-2-3-3-4-4l1-2c0-1 0-1 1-2 1 0 2 0 3-1h1z" class="B"></path><path d="M500 304h1c-1-1-1-2 0-3 0-2 0-2 2-3h2c1 0 3 0 5 1h1c0 1 1 2 0 3h-2v1h2c1 5 0 13 3 16 1 1 1 2 2 4l-2 1c-2-1-4 0-5 0h-10l-5-1 1 1v2h-8v-8-4l-1-1-1-1s1-1 2-1c2-1 5-2 7-3h0l2-2h1c1-1 1-1 1-2v-1h2v1z" class="b"></path><path d="M506 319c2 0 3 0 5 1v1l-2 1h-6v-1c1-2 1-2 3-2z" class="r"></path><path d="M500 304h1c-1-1-1-2 0-3 0-2 0-2 2-3-1 2-1 2-2 3l2 2-1 1c0 1 0 3-1 5v6c-1 2-2 5-2 7h-1c0 1 0 1 1 2l-5-1-1-2 2-2 5-15z" class="l"></path><path d="M494 308h0l2-2h1c1-1 1-1 1-2v-1h2v1l-5 15-2 2 1 2 1 1v2h-8v-8-4l-1-1-1-1s1-1 2-1c2-1 5-2 7-3z" class="C"></path><path d="M491 310h3 0c0 3-2 5-1 8-1 1-2 1-2 3-1-4 1-7 0-11z" class="R"></path><path d="M487 311h0c2 0 3 0 4-1 1 4-1 7 0 11v3h4v2h-8v-8-4l-1-1-1-1s1-1 2-1z" class="g"></path><path d="M487 318h1v-2c1 2 1 4 1 6v2h2 0 4v2h-8v-8z" class="K"></path><path d="M567 204c2-1 3-2 4-2l3 2c3 2 6 4 8 6 2 1 5 6 7 6l4 4h0l-13 1c-2 2-6 4-7 7l-1 1c-2 0-2 2-3 3-1-2-1-2-2-3h-2c-2 1-3 2-4 3 0-1 0-1 1-2 0-1 1-1 1-2h0c-1 1-2 2-3 4l-1-1h0c-6 11-10 26-18 36l-11 13c0-1-1-3-1-4h0l-1-1c0-1-1-2-1-3h-1c1 0 2-1 2-2-1-1-1-3-1-4 1-2 3-3 3-5l-1-1-1-1-2 2-3 2h-1l-2 2v1c0 1 0 2-1 3v3c-1-3-2-6-2-8 1-1 1-2 2-4h0l8-7c2-2 4-4 6-7l9-21c1-1 1-2 2-3 1-2 2-4 3-5l2 1h0l-1-2c1-2 3-3 4-4 4-2 9-6 14-7l1-1z" class="b"></path><path d="M533 246v1c0 2-2 5-4 7l6 6h0c-2 1-4-4-6-4l-1 3-2 2-3 2h-1l2-4c1-2 1-2 2-3s0-1 1-1h1l-1-2c2-2 4-4 6-7z" class="H"></path><path d="M527 253l1 2h-1c-1 0 0 0-1 1s-1 1-2 3l-2 4-2 2v1c0 1 0 2-1 3v3c-1-3-2-6-2-8 1-1 1-2 2-4h0l8-7z" class="R"></path><path d="M528 259l1-3c2 0 4 5 6 4 1 2 3 3 4 6l2 1-11 13c0-1-1-3-1-4h0l-1-1c0-1-1-2-1-3h-1c1 0 2-1 2-2-1-1-1-3-1-4 1-2 3-3 3-5l-1-1-1-1z" class="d"></path><path d="M567 204c2-1 3-2 4-2l3 2c3 2 6 4 8 6 2 1 5 6 7 6l4 4h0l-13 1c-2 2-6 4-7 7l-1 1c-2 0-2 2-3 3-1-2-1-2-2-3h-2c-2 1-3 2-4 3 0-1 0-1 1-2 0-1 1-1 1-2h0c-1 1-2 2-3 4l-1-1h0c-1-1-1-3-1-4l-2-2-2-2-1-1c-1 0-3-3-3-3 1-1 1 1 3 1 1-2 3-2 5-3 4-1 7-3 11-5h1c2-1 4 0 5-1l-2-2v-1c2 1 3 2 5 3 1 0 1 0 1 1h0-2 0c-2 1-5 1-7 2-1 0-3 1-5 2-1 0-2 0-3 1-2 1-4 2-6 4v1c1 0 2 1 3 1v1c2 0 4 0 5-1l3-1-1-1 2-1c-1 0-2 1-2 1h-1c-2 1-3 1-4 2l-1-1s1-1 2-1c3-1 6-3 9-5 2-1 5-1 7-2h1 1c1 0 1-1 2 0h1v-2h-1c0-1-1-3-3-3h-1c-1-1-2-2-3-2v-1c-2-1-4 0-6 0l-3-1 1-1z" class="X"></path><path d="M559 231l1-2c4-6 13-7 20-8-2 2-6 4-7 7l-1 1c-2 0-2 2-3 3-1-2-1-2-2-3h-2c-2 1-3 2-4 3 0-1 0-1 1-2 0-1 1-1 1-2h0c-1 1-2 2-3 4l-1-1z" class="O"></path><path d="M497 190l1 1c2 1 2 1 3 3 2 0 2-1 3-1s2 0 3-1v3c1 2 1 4 1 6 0 5 0 9 1 14v8l1 1v13l1 8h-2c-1 1-3 0-5 0-1 4-1 9-1 13-1 7 0 15-2 22l-3 11v2h0l2-20c0-1 1-5 1-6-1-1-2-2-2-3-1 0-2-1-3-2h-1 0c-2-2-5-3-6-5l-1-1-3-3c1 0 2 0 3-1h1c-1-2 0-2-1-4h0-2 0l-1-1c1-1 2-1 2-2v-1c-1-1-2-2-3-2h-1l1-2v-1c-1 1-2 0-2 1-4-2-4-5-5-7s-3-3-3-5c-1-1-1-1-1-2-1-1 0-2-1-3 0-1-1-3-2-4-3-1-3-2-5-5h0v-1l2-1-1-1c-1 0-3 1-4 1h-1c-3-3-7-6-11-9h2c3-1 5-1 7-3h1c1 0 3-2 5-2 1-1 2-1 4-1 1-1 3-2 4-2l2-1 5-2c2-1 2-1 4-1h5c3 0 6 0 8-1z" class="p"></path><path d="M464 205c-1 2-2 3-3 4-1 0-2-1-3-1l1-1c2-1 3-2 5-3v1z" class="N"></path><path d="M481 202v-1h4l1-1c0 2 1 2 0 4v1l-2-1h0c-2 1-3 1-5 1 1-1 1-1 3-2l1-1c-1 0-1 0-2 1v-1zm9 7l1 1c1 2 3 4 4 6-2 1-2 1-4 0l-1-1c-1-2-1-4 0-6z" class="G"></path><path d="M464 204c4-3 7-5 12-7l2 2c-5 1-10 3-14 6v-1z" class="C"></path><path d="M468 215l5-4c3-2 6-2 10-4h1 0c-1 2-3 2-5 3l-3 1c-2 1-3 1-4 2 0 1 1 1 1 2v3l-2-2c-1-1-2-1-3-1h0z" class="M"></path><path d="M460 200c1 0 3-2 5-2 1-1 2-1 4-1-2 2-5 3-7 5-1 1-3 1-5 1-1 1-1 1-3 2l-2-2c3-1 5-1 7-3h1z" class="E"></path><path d="M466 211c2-2 4-3 6-5 1-2 3-3 5-4 1 1 2 0 4 0v1c1-1 1-1 2-1l-1 1c-2 1-2 1-3 2-1 2-2 2-3 3l-2-1v1h-1c-2 1-4 2-6 4l-1-1z" class="F"></path><path d="M497 190l1 1c2 1 2 1 3 3-1 1-1 2-1 4v1c-1 1-2 3-3 4h0c-2 1-3 2-6 2-2 0-2-1-3-2s-1-1-1-2v-2c-1-1-1-2-2-4 0 2 0 2-1 3h0v-1c-2 0-4 1-6 2l-2-2 3-1-3-2c0 1-1 1-2 1h-1l2-1 5-2c2-1 2-1 4-1h5c3 0 6 0 8-1z" class="N"></path><path d="M480 192c2 0 3 0 4 1-1 1-4 3-5 3l-3-2c0 1-1 1-2 1h-1l2-1 5-2z" class="E"></path><path d="M485 195c1-1 1-2 3-3h0c1 1 2 1 3 1v2c0 1 1 1 2 2-2 0-3 0-4 1l-1 2v1 2c-1-1-1-1-1-2v-2c-1-1-1-2-2-4z" class="D"></path><path d="M498 191c2 1 2 1 3 3-1 1-1 2-1 4-1 1-2 1-3 1h-2-1-1c-1 1-1 1-2 1h0l-2-2c1-1 2-1 4-1h1l1-2-1-1c1 0 1 0 3 1 0-1 1-3 1-4z" class="C"></path><path d="M494 197v1l-3 2-2-2c1-1 2-1 4-1h1z" class="M"></path><path d="M497 199c0-1 0-3 1-4s2-1 3-1c-1 1-1 2-1 4-1 1-2 1-3 1z" class="E"></path><path d="M500 198v1c-1 1-2 3-3 4h0c-2 1-3 2-6 2-2 0-2-1-3-2v-2-1l1-2 2 2h0c1 0 1 0 2-1h1 1 2c1 0 2 0 3-1z" class="M"></path><path d="M488 201v-1l1-2 2 2h0c1 0 1 0 2-1h1 1c-1 2-2 3-4 4-1-1-2-1-3-2z" class="G"></path><path d="M474 218l1 1v1l1-1c1 2 2 0 4 1l1-1-1-2h2s1 0 1 1h2v-4-1-1l1-1h0v4c1 1 2 1 3 1h2c2 1 2 1 4 0v1l1 1s1 1 1 2-1 2-1 3l-1 1 1 1h-1v2h2l-3 4h0c1 2-1 2 1 4h0l-1 3c-1 0-1 1-2 2 0 1 0 1-1 1v1c-1 1-2 1-3 2h-1c-1-1-2-2-3-2h-1l1-2v-1c-1 1-2 0-2 1-4-2-4-5-5-7s-3-3-3-5c-1-1-1-1-1-2-1-1 0-2-1-3 0-1-1-3-2-4-3-1-3-2-5-5l3 1h0c1 0 2 0 3 1l2 2h1z" class="E"></path><path d="M477 224c0 1 1 2 1 2l-1 1-2-1c-1-1-2-3-2-4l2-1c1 1 2 2 2 3z" class="C"></path><path d="M491 216c2 1 2 1 4 0v1l1 1s1 1 1 2-1 2-1 3v-2l-1-1h-1l1-2h0c-2 0-2 1-3 1l-3-3h2z" class="D"></path><path d="M484 240l1-1c-1-1-2 0-3 0l2-2-2-2c1-1 1-1 2-1 0-1 1-1 2-2 1 2-2 2 1 4h1l-1 2 1 1c1 0 1 0 2-1v1l1 2v1c-1 1-2 1-3 2h-1c-1-1-2-2-3-2h-1l1-2zm-10-22l1 1v1l1-1c1 2 2 0 4 1l1-1-1-2h2s1 0 1 1h2v-4-1-1l1-1h0v4c1 1 2 1 3 1l3 3c-1 2-3 3-3 5 1 1 2 0 3 0l1 1c-1 1-2 1-3 2 0 2 1 2-1 4l-1 1h-1v-2l-1-1h-1c0 1 1 1 1 2h0c-1 1-1 1-2 1l-1 1s-1 0-1 1l-3 2-1-1 3-3v-1c1-2 2-3 4-5l1 1h1v-2c-2 0-3 1-5 1v1h-2c0 1 0 1-1 1l-1-1 2-2h3c0-2-1-2-2-3v1c-1 1-2 1-4 1 0-1-1-2-2-3h-1l-1-1h1v-2z" class="F"></path><path d="M507 192v3c1 2 1 4 1 6 0 5 0 9 1 14v8l1 1v13l1 8h-2c-1 1-3 0-5 0-1 4-1 9-1 13-1 7 0 15-2 22l-3 11v2h0l2-20c0-1 1-5 1-6-1-1-2-2-2-3-1 0-2-1-3-2h-1 0c-2-2-5-3-6-5l-1-1-3-3c1 0 2 0 3-1h1c-1-2 0-2-1-4h0-2 0l-1-1c1-1 2-1 2-2v-1h1c1-1 2-1 3-2v-1c1 0 1 0 1-1 1-1 1-2 2-2l1-3h0c-2-2 0-2-1-4h0l3-4h-2v-2h1l-1-1 1-1c0-1 1-2 1-3s-1-2-1-2l-1-1v-1c-1-2-3-4-4-6l-1-1c0-1-1-1-1-2h2c3-1 4-1 6-3v-1c1-1 2-3 3-4v-1c0-2 0-3 1-4 2 0 2-1 3-1s2 0 3-1z" class="m"></path><path d="M498 237l3-3c-1 3-2 6-1 9v2h0l-1-2h-1c0 1 1 2 1 3-1 0-1 0-2 1l-2 2v-2c1 0 1-1 1-1l-1-2 1-1 1-1h-1l-1-1h0l1-1 2-3z" class="Y"></path><path d="M496 240l1 1 1-1 1 1c0 1-1 1-1 2-1 0 0 2 0 2l-1 2-2 2v-2c1 0 1-1 1-1l-1-2 1-1 1-1h-1l-1-1h0l1-1z" class="T"></path><path d="M488 248l1-1h2c1-1 2-2 2-4l2-2 1 1h1l-1 1-1 1 1 2s0 1-1 1v2c-2 1-3 2-5 3h-1c-1-2 0-2-1-4z" class="D"></path><path d="M494 238h0l2-2 2 1-2 3-1 1h0l-2 2c0 2-1 3-2 4h-2l-1 1h0-2 0l-1-1c1-1 2-1 2-2v-1h1c1-1 2-1 3-2v-1c1 0 1 0 1-1 1-1 1-2 2-2z" class="E"></path><path d="M494 238h0l2-2 2 1-2 3-1 1-1-1-2 2h-1v-1c1 0 1 0 1-1 1-1 1-2 2-2z" class="D"></path><path d="M505 230v1 8l1 1c2-1 3-2 4-3l1 8h-2c-1 1-3 0-5 0v-1c0-4 0-9 1-14z" class="o"></path><path d="M504 244l1-1c1 0 1-1 1-2h1v2c1 1 2 1 2 2-1 1-3 0-5 0v-1z" class="j"></path><defs><linearGradient id="AR" x1="498.923" y1="223.337" x2="495.982" y2="233.777" xlink:href="#B"><stop offset="0" stop-color="#2c2c2c"></stop><stop offset="1" stop-color="#484746"></stop></linearGradient></defs><path fill="url(#AR)" d="M500 223c1 1 2 1 1 2v1 8l-3 3-2-1-2 2h0l1-3h0c-2-2 0-2-1-4h0l3-4h1l1-2s-1 0 0-1l1-1z"></path><path d="M495 235c1-1 3-3 3-5 1-1 2-2 3-4v8l-3 3-2-1-2 2h0l1-3z" class="B"></path><path d="M491 210l1-1c2 1 3 6 4 6l1-1c1-1 2-1 3-2 2 1 2 1 4 1v-2l1 1h0v-1h1c0 4 0 8-1 12v-7h-1c0 1 0 2-1 3-1 0-3 0-5 1l2 3-1 1c-1 1 0 1 0 1l-1 2h-1-2v-2h1l-1-1 1-1c0-1 1-2 1-3s-1-2-1-2l-1-1v-1c-1-2-3-4-4-6z" class="T"></path><path d="M495 249l2-2c0 1 0 1-1 2h3l1 1 1-1c-1 2-1 1-2 2-1 2 0 3 0 5 0 0-3 2-3 3-1 0-1 0-1 1 1 0 1 1 1 2h-1 0c-2-2-5-3-6-5l-1-1-3-3c1 0 2 0 3-1h1 1c2-1 3-2 5-3z" class="B"></path><path d="M497 250h1 0l-1 1c-1 1-2 2-3 4h0c-1 0-1-1-2-2 1-1 3-2 5-3z" class="N"></path><path d="M489 252h1c-1 2 0 3-1 5l-1-1-3-3c1 0 2 0 3-1h1z" class="C"></path><path d="M496 249h3l1 1 1-1c-1 2-1 1-2 2-1 2 0 3 0 5 0 0-3 2-3 3-1 0-1 0-1 1 1 0 1 1 1 2h-1 0v-5h0c1-1 2-2 3-4-2 0-2 1-3 2h-1c1-2 2-3 3-4l1-1h0-1l-1-1z" class="S"></path><path d="M507 195c1 2 1 4 1 6 0 5 0 9 1 14v8l1 1v13c-1 1-2 2-4 3l-1-1v-8-1-7c1-4 1-8 1-12v-3l1-13z" class="j"></path><path d="M507 192v3l-1 13v3h-1v1h0l-1-1v2c-2 0-2 0-4-1-1 1-2 1-3 2l-1 1c-1 0-2-5-4-6l-1 1-1-1c0-1-1-1-1-2h2c3-1 4-1 6-3v-1c1-1 2-3 3-4v-1c0-2 0-3 1-4 2 0 2-1 3-1s2 0 3-1z" class="C"></path><path d="M498 205l2-1v6h0c-1-2-2-3-2-5z" class="F"></path><path d="M500 199c1 3 1 3 0 5l-2 1-1-1v-1c1-1 2-3 3-4z" class="E"></path><path d="M382 179l1 1 1-1h6v3l1-1v-2h1 24v1c0 2-1 4-1 6h-1c-2 1-5 1-7 1h-18c-2 0-5-1-6 0-3 1-7 7-8 9 4-2 8-5 12-6 3-1 6-1 9-1 10 0 20 0 29 2 2 0 3 1 4 1l16 8c2 1 3 2 5 3 4 3 8 6 11 9h1c1 0 3-1 4-1l1 1-2 1v1h0c2 3 2 4 5 5 1 1 2 3 2 4 1 1 0 2 1 3 0 1 0 1 1 2 0 2 2 3 3 5s1 5 5 7c0-1 1 0 2-1v1l-1 2h1c1 0 2 1 3 2v1c0 1-1 1-2 2l1 1h0 2 0c1 2 0 2 1 4h-1c-1 1-2 1-3 1l3 3 1 1c1 2 4 3 6 5h0 1c1 1 2 2 3 2 0 1 1 2 2 3 0 1-1 5-1 6l-2 20h0v-2l3-11c0 1 1 2 1 3h0c0 3 0 5-1 8h2v1h-1v4c1 0 1 1 2 1l1 1h-2c-2 1-2 1-2 3-1 1-1 2 0 3h-1v-1h-2v1c0 1 0 1-1 2h-1l-2 2h0c-2 1-5 2-7 3-1 0-2 1-2 1l1 1 1 1-3-1v5c0 2 0 5-1 7v-13l-2 1-1-1c1-3 1-3-1-5h0c1-1 1-4 1-5-1-1-1 0-1-2 0-1 0-1-1-2v-3c1-2 0-4-1-6h0l-1-2v-1c-1 0-1 0-2-1v-1h-1 0l-1-1c-1-1-1-1-2-1-2 1-1 0-2 1-1 0-2 1-2 1h-2c0-1-2-1-2-1l-1-2c1-3 0-5 0-8l-2-11v-1c-2-14-10-34-22-42v-1h-1c0-1-1-1-1-1-1-1-1-2-3-2-1-1-3-2-5-3 0-1-1-1-2-1v-1c-2-1-4-1-5-2l-5-2-1 1h-4c-2-1-5-1-7-1-4 0-8-1-12 0h-1l-1-1h2v-1c-1-1-3-1-5 0-7 0-14 3-20 7-3 2-6 5-10 7 0 1-1 1-2 1s0 0-1 1c1-6 5-12 8-16 2-3 3-4 5-7 0-1 0-2 1-3l4-4 3-4c1-2 2-3 3-4l3-3h0 2z" class="e"></path><path d="M458 216l4 6v1h-1c0-1-1-2-2-3 0 2 4 6 2 8-1-3-3-8-6-9l3-3z" class="U"></path><path d="M482 253l1-1h1l1 1 3 3c-2 0-3 1-4 2h0c-2 1-2 1-4 1h-1l-1 2-1-1c0-1 1-2 2-3l1-1c0-2 1-2 2-3z" class="Z"></path><path d="M484 252l1 1 3 3c-2 0-3 1-4 2h0v-6z" class="U"></path><path d="M358 215l1-2c1-2 3-4 5-5l3-2c1-1 1-1 3-2 1 0 1-1 2-1l2-2h1l1-1h4l3-1h0c1 1 2 0 4 0 1-1 1 0 3 0h0c2 0 3 0 4 1-3 2-6 1-10 2-1 0-2 0-3 1h-2c-1 0-2 1-3 1h-1c-1 1-1 1-2 1s-1 1-2 1c-2 1-3 1-4 2l-1 1c-1 1-2 1-3 2l-1 1h-1c0 1-1 1-1 2-1 0-2 0-2 1z" class="d"></path><path d="M466 211l1 1-2 1v1h0c2 3 2 4 5 5 1 1 2 3 2 4 1 1 0 2 1 3 0 1 0 1 1 2 0 2 2 3 3 5s1 5 5 7c0-1 1 0 2-1v1l-1 2h1c1 0 2 1 3 2v1c0 1-1 1-2 2l1 1h0 2 0c1 2 0 2 1 4h-1c-1 1-2 1-3 1l-1-1h-1l-1 1v-1-2c0-1-1-2-2-3l-3-6c-4-7-6-15-10-22-1-2-3-4-4-6l-5 3-3 3-1-1 7-6h1c1 0 3-1 4-1z" class="D"></path><path d="M488 248h0c1 2 0 2 1 4h-1c-1 0-1-1-2-2l2-2z" class="N"></path><path d="M394 200c9-2 16 1 25 2h2v1c-2 1-2 1-3 1l2 1-4-1-1 2-1 1h-4c-2-1-5-1-7-1-4 0-8-1-12 0h-1l-1-1h2v-1c-1-1-3-1-5 0-7 0-14 3-20 7-3 2-6 5-10 7 0 1-1 1-2 1 1-2 2-3 4-4 0-1 1-1 2-1 0-1 1-1 1-2h1l1-1c1-1 2-1 3-2l1-1c1-1 2-1 4-2 1 0 1-1 2-1s1 0 2-1h1c1 0 2-1 3-1h2c1-1 2-1 3-1 4-1 7 0 10-2z" class="h"></path><path d="M386 204c9-3 21-2 30 0l-1 2-1 1h-4c-2-1-5-1-7-1-4 0-8-1-12 0h-1l-1-1h2v-1c-1-1-3-1-5 0z" class="O"></path><path d="M416 204l4 1c6 3 13 6 18 10 4 3 7 5 10 8 1 0 2-1 3-2v1l-2 2c1 3 4 6 6 9 3 6 5 12 8 18 0 1 2 3 2 4l-1 2h0c1 1 1 3 1 4v1 1c0 1 0 2 1 4h0 1l4 5-1 1v-1h-3c1-1 1-1 0-2h0-2c-1 2-2 2-4 3l-2-11v-1c-2-14-10-34-22-42v-1h-1c0-1-1-1-1-1-1-1-1-2-3-2-1-1-3-2-5-3 0-1-1-1-2-1v-1c-2-1-4-1-5-2l-5-2 1-2z" class="m"></path><path d="M466 267h1l4 5-1 1v-1h-3c1-1 1-1 0-2h0-2s0-1-1-2h1l1 1h1l-1-2z" class="S"></path><path d="M416 204l4 1c6 3 13 6 18 10h-1c-1 0-2 0-3-1l-1-1c-2-1-3-2-5-2 2 1 3 2 4 3 2 1 4 2 5 3v1h-1c0-1-1-1-1-1-1-1-1-2-3-2-1-1-3-2-5-3 0-1-1-1-2-1v-1c-2-1-4-1-5-2l-5-2 1-2z" class="Y"></path><defs><linearGradient id="AS" x1="481.28" y1="278.603" x2="465.72" y2="290.897" xlink:href="#B"><stop offset="0" stop-color="#181615"></stop><stop offset="1" stop-color="#3f403e"></stop></linearGradient></defs><path fill="url(#AS)" d="M465 255c2 4 4 9 7 12l1-1c1-2 1-3 3-4 0-1 0-1 1-2l1 1 1-2h1c2 0 2 0 4-1h0c1-1 2-2 4-2l1 1c1 2 4 3 6 5h0 1c1 1 2 2 3 2 0 1 1 2 2 3 0 1-1 5-1 6l-2 20h0v-2l3-11c0 1 1 2 1 3h0c0 3 0 5-1 8h2v1h-1v4c1 0 1 1 2 1l1 1h-2c-2 1-2 1-2 3-1 1-1 2 0 3h-1v-1h-2v1c0 1 0 1-1 2h-1l-2 2h0c-2 1-5 2-7 3-1 0-2 1-2 1l1 1 1 1-3-1v5c0 2 0 5-1 7v-13l-2 1-1-1c1-3 1-3-1-5h0c1-1 1-4 1-5-1-1-1 0-1-2 0-1 0-1-1-2v-3c1-2 0-4-1-6h0l-1-2v-1c-1 0-1 0-2-1v-1h-1 0l-1-1c-1-1-1-1-2-1-2 1-1 0-2 1-1 0-2 1-2 1h-2c0-1-2-1-2-1l-1-2c1-3 0-5 0-8 2-1 3-1 4-3h2 0c1 1 1 1 0 2h3v1l1-1-4-5h-1 0c-1-2-1-3-1-4v-1-1c0-1 0-3-1-4h0l1-2z"></path><path d="M465 270h2 0c1 1 1 1 0 2h3v1c-1 0-2 1-2 2l1 1c1-1 2-2 3-2h1v1 1c1 1 2 2 2 3l1 1c0 1 1 2 1 3h0c1 2 1 4 0 6h0l-1-2v-1c-1 0-1 0-2-1v-1h-1 0l-1-1c-1-1-1-1-2-1-2 1-1 0-2 1-1 0-2 1-2 1h-2c0-1-2-1-2-1l-1-2c1-3 0-5 0-8 2-1 3-1 4-3z" class="Y"></path><path d="M464 284c1-2 3-3 4-4 2-1 3-2 5-3l-3 5c-2 1-1 0-2 1-1 0-2 1-2 1h-2z" class="D"></path><path d="M487 279c1 3 1 5 1 8l-1 10v1 1c1 1 2 1 3 0l1-1c-1-1-1-2 0-4l1 2-1 6h1c-1 2-1 3-2 5h1 2c1 0 0 0 1 1-2 1-5 2-7 3-1 0-2 1-2 1l1 1 1 1-3-1v5c0 2 0 5-1 7v-13-30c1 2 1 4 1 6 1 1 1 1 2 3l1-1c-1-3 0-7 0-11z" class="f"></path><path d="M487 311v-1l-1-1c1-2 1-5 1-8l4 2v-1h1c-1 2-1 3-2 5h1 2c1 0 0 0 1 1-2 1-5 2-7 3z" class="K"></path><path d="M484 288c1 1 1 1 2 3l1-1c0 2-1 4-1 6v5c0 1-1 1-1 2v5 2h1l-2 2h0v-4c-1-6 0-13 0-20z" class="J"></path><path d="M477 260l1 1 1-2h1v1c3 3 6 6 10 8-1 3-2 6-2 9-1 1-1 2-1 2 0 4-1 8 0 11l-1 1c-1-2-1-2-2-3 0-2 0-4-1-6-1-4-7-9-10-12v-2-2h0c1-2 1-3 3-4 0-1 0-1 1-2z" class="X"></path><path d="M477 268l2 2-1 1h-2l-1-2 1-1h1z" class="b"></path><path d="M477 260l1 1 1-2h1v1c-1 2-1 2-2 3 0 0 0 1 1 1-1 1-1 2-2 2-1 1 0 1 0 2h-1 0l-3 2v-2-2h0c1-2 1-3 3-4 0-1 0-1 1-2z" class="U"></path><path d="M473 266c1-2 1-3 3-4v3 1 2l-3 2v-2-2h0z" class="Z"></path><path d="M488 256l1 1c1 2 4 3 6 5h0 1c1 1 2 2 3 2 0 1 1 2 2 3 0 1-1 5-1 6l-2 20h0v-2l3-11c0 1 1 2 1 3h0c0 3 0 5-1 8h2v1h-1v4c1 0 1 1 2 1l1 1h-2c-2 1-2 1-2 3-1 1-1 2 0 3h-1v-1h-2v1c0 1 0 1-1 2h-1l-2 2h0c-1-1 0-1-1-1h-2-1c1-2 1-3 2-5h-1l1-6-1-2c-1 2-1 3 0 4l-1 1c-1 1-2 1-3 0v-1-1l1-10c0-3 0-5-1-8 0 0 0-1 1-2 0-3 1-6 2-9-4-2-7-5-10-8v-1c2 0 2 0 4-1h0c1-1 2-2 4-2z" class="V"></path><path d="M495 262h1c1 1 2 2 3 2l-2 3h0c1 5-1 11-1 16v1l-1-5c0-2 0-3 1-4v-6-1l-2 3v-1c0-2 0-6 1-8z" class="L"></path><path d="M494 270v1l2-3v1 6c-1 1-1 2-1 4l1 5c-1 1-1 0-1 1v1c-2 0 0-3-2-5l-1-1 2-10z" class="R"></path><path d="M491 284l1-4 1 1c2 2 0 5 2 5v-1c0-1 0 0 1-1v1l1 2-1 1v1c-1 1-1 2-1 4 0 0 1 1 1 2s0 1-1 2l1 1-2 2h1v1c-1 1-1 2-1 4l-1-1v-1l-1-1h-1l1-6-1-2c-1 0-1-2 0-3v-7z" class="L"></path><path d="M491 284l1-4 1 1c0 4 0 8-1 12v3l-1-2c-1 0-1-2 0-3v-7z" class="K"></path><path d="M498 293h0v-2l3-11c0 1 1 2 1 3h0c0 3 0 5-1 8h2v1h-1v4c1 0 1 1 2 1l1 1h-2c-2 1-2 1-2 3-1 1-1 2 0 3h-1v-1h-2v1c0 1 0 1-1 2h-1l-2 2h0c-1-1 0-1-1-1h-2-1c1-2 1-3 2-5l1 1v1l1 1c0-2 0-3 1-4v-1h-1l2-2-1-1c1-1 1-1 1-2l1-1 1-1z" class="F"></path><path d="M488 256l1 1c1 2 4 3 6 5h0c-1 2-1 6-1 8l-2 10-1 4v7c-1 1-1 3 0 3-1 2-1 3 0 4l-1 1c-1 1-2 1-3 0v-1-1l1-10c0-3 0-5-1-8 0 0 0-1 1-2 0-3 1-6 2-9-4-2-7-5-10-8v-1c2 0 2 0 4-1h0c1-1 2-2 4-2z" class="W"></path><path d="M484 258h0c1 1 3 2 4 4l-1 1h1l1-1h0l1-1c1 1 1 1 2 3 0 1-1 2-2 4-4-2-7-5-10-8v-1c2 0 2 0 4-1z" class="d"></path><path d="M488 277h0v-1c1-3 3-7 4-10 0 4-1 9-1 13v5 7c-1 1-1 3 0 3-1 2-1 3 0 4l-1 1c-1 1-2 1-3 0v-1-1l1-10c0-3 0-5-1-8 0 0 0-1 1-2z" class="J"></path></svg>