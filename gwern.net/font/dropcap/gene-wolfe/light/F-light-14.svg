<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="241 39 641 934"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#bdbcba}.C{fill:#9c9b9a}.D{fill:#adadab}.E{fill:#d9d7d3}.F{fill:#c3c2c0}.G{fill:#c9c8c4}.H{fill:#a6a5a4}.I{fill:#b3b2b1}.J{fill:#d1d0cc}.K{fill:#181717}.L{fill:#4c4c4c}.M{fill:#787877}.N{fill:#5c5c5c}.O{fill:#1d1d1d}.P{fill:#2a2a29}.Q{fill:#dddcd8}.R{fill:#3e3e3e}.S{fill:#656565}.T{fill:#222122}.U{fill:#343434}.V{fill:#a2a2a0}.W{fill:#cac9c7}.X{fill:#959493}.Y{fill:#d9d7d4}.Z{fill:#383838}.a{fill:#2e2f2e}.b{fill:#252525}.c{fill:#d8d7d5}.d{fill:#dfdedb}.e{fill:#848382}.f{fill:#121212}.g{fill:#0e0d0d}.h{fill:#908f8f}.i{fill:#898988}.j{fill:#545453}.k{fill:#6b6b6a}.l{fill:#e6e4e2}.m{fill:#70706f}.n{fill:#7e7e7d}.o{fill:#454545}.p{fill:#090909}.q{fill:#8c8c8b}.r{fill:#f5f4ef}.s{fill:#010101}.t{fill:#fdfcf4}</style><path d="M742 589c2 0 3 1 3 3v1l-1 1h-1l-1-5z" class="D"></path><path d="M749 299h3v1c0 2 0 2-1 3h0 0c-1 0-2-1-3-1 0-2 0-2 1-3z" class="r"></path><path d="M327 308h1c1 1 1 1 1 2l1 1c-1 1-2 3-3 5-1-3-1-5 0-8z" class="O"></path><path d="M685 813h0c1 2 2 3 4 4v1h-1c-1 1-3 1-5 0 1-1 2-2 2-4v-1z" class="h"></path><path d="M445 144c1 0 2 1 3 2-1 2-3 4-5 6h0-1l1-1c1-2 1-3 1-5h-1l2-2z" class="B"></path><path d="M257 329c1 0 2 1 2 2 1 1 0 3-1 4h-1l-1-1c0-2 0-4 1-5z" class="C"></path><path d="M485 194l-1-1v-3h-1v2h0c-1-1 0-3 0-4h0l1-1c1-2 0 0 0-2 1-1 2-2 2-3v7l-1 5z" class="r"></path><path d="M315 717l2 1h0c1 0 3-1 4-1s1 0 2 1l1-1v2h2 0c-3 0-8 2-10 1l-1-3z" class="S"></path><path d="M477 173v1 2h-1c0 3 0 4-1 6 0 1 0 1-1 2l-1-1 2-8c1-1 1-1 1-2h1z" class="Y"></path><path d="M299 656c2 0 5 1 6 2 0 1-1 2-2 2h-4l2-1-1-1c-1 0-2 0-3-1h-1l3-1z" class="c"></path><path d="M548 895c2-1 3-2 5-3 0 3 0 5 1 8-3-2-4-3-6-5z" class="R"></path><path d="M282 694c2-2 3-1 5-3v1 1h1 1c1 1 2 0 3 0v1s-1 0-1 1c-3 0-6 0-9-1z" class="L"></path><path d="M687 747v-1c1-1 3-2 3-5 0-1-1-1-1-2h0c2 0 3 1 4 2 0 3-1 5-3 7-1 0-2-1-3-1z" class="M"></path><path d="M742 691c1 0 2-1 3-1h1 4c1 0 2 1 3 1l-13 3v-2c1 0 2 0 2-1z" class="R"></path><path d="M662 732c2-1 5-2 8-2l-7 4-2 1c-1 0-2 0-2 1h-4c2-2 4-3 7-4z" class="C"></path><path d="M664 725v1c0 1 1 1 1 2h0c3-1 5 0 8 0-1 0-3 2-3 2-3 0-6 1-8 2l-1-2 2-2c0-1 0-2 1-3z" class="E"></path><path d="M527 254c3-1 6-1 10-1v1 1h-3v1h2 1v1c-2 0-4 0-4-1h-1c-1-1-4-1-5 0h-2c-1 1-3 1-4 0h0 3c1-1 0-1 2-1h3l-2-1z" class="G"></path><path d="M512 261c-4-1-7-1-10-2-2 0-3 0-4-1h1l15 2c2 1 4 1 6 2 3 1 7 1 10 1l1 1 1 1c-2 0-5 0-7-1-1 0-2-1-3-1-4-1-7-2-10-2z" class="J"></path><path d="M469 892c0 3 0 6-1 9l-1 1c-2 0-2 3-3 5-2 3-5 6-8 7 7-6 9-11 11-20 1 0 1-1 2-2z" class="S"></path><path d="M731 547l1-1 2 1c-2 4-6 7-9 9 0-1 0-1 1-1 1-1 0-2 1-3v-1l1-2c1-1 2-1 3-1v-1z" class="a"></path><path d="M725 220h3l2-2c2 0 3 0 5 1h-1c-1 1-2 1-3 2h0c1 1 3 1 4 1l1 1c-4 0-8-1-11 1-1 0-1 0-1 1l-1-1c0-2 1-3 2-4z" class="U"></path><path d="M600 835c4 2 9 3 13 3v2c-4 0-11 0-14-2h1v-3z" class="L"></path><path d="M725 218l6-3h1l1 1h2 1l-2 2h1v1h0c-2-1-3-1-5-1l-2 2h-3-3l3-2z" class="a"></path><path d="M768 626c1-1 3 0 4 0l2 1c0 1 0 3-1 4v-3c-1 1-1 1-1 2v1c-2 1-3 1-5 2-2-1-4-1-6-2h2 1 1 3-1v-2c0-2 0-2 1-3z" class="r"></path><path d="M396 232h1c0 2-1 3-1 4l1 1v5h1 1l2 2-1 1h0v-1l-1 1h-1 0-1v-2 1c0 1 0 0-1 1v1c-1 1-1 2-2 3h-1l1-1c0-1 0-1 1-2h0c-1-2-1-2-1-3v-1h0v-1h0v-1c1-1 1-1 1-2h0v-4l1-1v-1z" class="E"></path><path d="M285 414h-2c-3-1-8-2-9-5-3-3-3-12-2-16l1 1c0 5 0 12 4 16 2 2 6 3 8 4z" class="D"></path><path d="M319 678l2 1c1 1 1 2 2 3-1 0-6 2-6 4-2-1-3-2-4-3 1-2 3-4 6-5z" class="o"></path><path d="M755 373l4-4c-5 6-10 14-15 19l-1-1c-1-1-1-1-1-2l13-12z" class="L"></path><path d="M594 829h1v2l-1 1c1 2 4 3 6 3v3h-1-3c-1 1-1 3-2 4l-1-1-1-1-1-2c1-3 3-6 3-9z" class="R"></path><path d="M437 220h1c1 0 1 0 2-1h2 0c2-2 4-3 7-4-3 2-6 4-8 7 1-1 2-1 4-2 1 0 1 0 2-1l1 1c-1 0-2 1-3 1v1c-2 1-3 1-4 1s-1 0-2 1h1v1h-1l-4 1 1-1c1-1 1-1 2-1l1-1h0v-1h-3c-3 0 1 0-2 0 1-1 2-2 3-2z" class="l"></path><path d="M357 324h1c1 1 1 0 1 1l1 1c2 0 1 0 3 1h4c1-1 3 0 4-1 1 0 1-1 2-1h4 1 1c1-1 1-1 2-1h3c1-1 2-1 3-1l-5 3-8 2c-3-1-5-1-8 0-1 0-2 0-3-1-2 0-4 0-5-1v-1l-1-1z" class="Y"></path><path d="M349 324h4c2 1 3 0 4 0l1 1v1c1 1 3 1 5 1 1 1 2 1 3 1 3-1 5-1 8 0-3 1-5 2-8 1-1 0-1 0-1 1h-1c-4-1-8-2-12-4h0l-1 1c-1-1-1-2-2-3z" class="Q"></path><path d="M401 315h3c-4 4-10 8-15 10l-7 1 5-3c5-2 9-5 14-8z" class="J"></path><path d="M757 366c4-3 8-7 12-9 0 1-2 3-3 4-2 2-4 6-7 8l-4 4-1-1-1-1c2-1 3-3 4-5z" class="B"></path><path d="M491 131c0 1 0 2-1 4l-3 6c-1 1-1 1-1 2h0 1v3l-1 1-2 2c-1 1-1 0-1 1l-1-1 1-1v-2h0c-2 2-2 3-3 5 0 1-1 3-3 4 1-1 1-2 2-3s1-2 1-3h-1c0-1 1-1 1-1 0-1 1-2 2-2l1-1c0-1 1-2 1-4l7-10z" class="Y"></path><path d="M446 286h-2c-1 1-2 1-3 1-1 1-2 1-3 1v1h-3l11-4c2-1 4-1 6-1 3-1 5-2 9-1h5l2 1h-3c-2 2-7 1-9 1-3 1-6 1-10 2v-1z" class="J"></path><path d="M709 650s3 2 4 2c2 1 4 2 6 2 3 1 6 1 8 1 3 1 6 1 9 0 3 0 6-1 9-1-2 2-6 2-9 2h0c-5 1-11 2-15 0-2 0-3-1-4-1-2 0-2-1-3-1-2-1-5-3-5-4z" class="G"></path><path d="M774 560h1c1 0 2 1 2 2l-1 2c-1 2-3 3-5 5-1 0-2 0-3-1h0c1 0 2-2 3-3l1-1h-1c-2 1-1 1-2 1h-4-1l1-1c1 0 3-1 4-1l5-3h0z" class="E"></path><path d="M468 250l2-1 1 1-14 5-4 1-2 1s-1 0-2 1h-1-1l-2 1s-1 1-2 1-1 0-2 1h0 0-2l-1-1c1-1 2 0 4 0 0-1 0-1 1-1 2-1 3-1 5-2h1c-1-1-1 0-2-1h0 1c0-1 2-1 3-2h0 1 3l2-1c1-1 3-1 5-2 2 0 3-1 5-1h1z" class="G"></path><path d="M514 260v-1h-2 0c-1-1-3-1-4-1h-1c-2 0-3-1-5-1l1-1h2c1 1 2 1 3 1 1 1 2 0 4 0 1 1 3 1 5 1h0c2 0 4 1 6 0h0 1c2 0 2 1 4 2h-1-6v1h7l-4 1h1c1 0 2 0 3 1h2c-3 0-7 0-10-1-2-1-4-1-6-2z" class="Y"></path><path d="M334 750c-2-2-5-2-7-2-6-1-11-3-16-6v-1c6 5 13 5 20 7 0-2-2-3-2-5-1-1 0-2 0-3 2-1 2-1 4-1h0l-2 2 1 2c0 2 1 3 3 4h0l-1 3z" class="h"></path><path d="M478 179h1l1-2c1-2 1-2 2-3v2c1 0 1-1 1-2l1-1v3 1 2c-1 2-1 4-2 6-1-1-1 0-1-1 1-2 1-4 1-5-1 0-1 1-1 2l-3 7-1-1v-1c1-1 1-1 1-2v-1h0v-1c-1 3-2 6-4 8h-1c1-3 2-5 3-7s1-3 2-4z" class="G"></path><path d="M479 168c0-2 1-3 2-5 0 1 0 4 1 4v-2c0-1 1-2 1-2h1v3c-1 1-1 2-1 3v1h1v-1l1-1v2h1 0v3c0 1 0 1-1 2l-1 2v-1-3l-1 1c0 1 0 2-1 2v-2c-1 1-1 1-2 3l-1 2h-1l1-2v-3c1-2 1-4 1-6h-1z" class="J"></path><path d="M757 243c2 0 4 1 5 2 3 1 4 2 7 3 0 0 1 0 1 1l1-1c3 3 5 6 7 10l1 3-4-2c-1 0-1 0-2-1 1 0 2 0 3 1h0l1-1c-2-2-5-4-7-6-4-3-9-6-13-9z" class="F"></path><path d="M536 249h0c4-1 7-2 10-2l1 1c-2 1-5 3-7 3v1h1v1h0c-1 1-1 2-2 2h-1c-1 1-1 1-2 1h-2v-1h3v-1-1c-4 0-7 0-10 1h-4c3-1 8-1 11-3h3c1-1 2 0 4-2h-1-1-2v1l-1-1z" class="W"></path><path d="M771 674l16-9c-4 6-9 12-14 16l-2-1c1-1 1-1 3-1v-1-3h-1-3l2-1h-1z" class="U"></path><path d="M384 256c0-1 1-1 2-2h0v1c1-1 2-2 3-2-1 2-3 5-5 7-3 4-6 7-9 11l-1-1h0c-1 1-2 2-3 2l-1-1c3-3 7-6 9-9 0-1 4-6 5-6z" class="d"></path><path d="M716 651c8 3 16 1 25 1v1l-5 1v1h0c-3 1-6 1-9 0-2 0-5 0-8-1-2 0-4-1-6-2 1-1 2-1 3-1z" class="Q"></path><path d="M745 675c3-1 5-2 8-4 2-2 4-5 7-6l1 1c0 3-5 8-6 11h0l-3 1c-1-1-1-1-2-1-1-1-2-1-4 0l-1-2z" class="P"></path><path d="M335 747c11 3 22 0 33-2-7 5-21 8-29 6l-5-1 1-3z" class="V"></path><path d="M614 150l-1-1c0-2-2-3-3-4 1-1 1-2 3-2 1 1 4 3 5 4h0c1 2 1 3 2 4v2 4-1c-1 0-1-1-1-2s0-1-1-2c-1 2 0 5 0 7-1 1-3 2-3 4l-1-1v-1l1-1v-1c1-2 1-5 0-7 0-1-1-1-1-2z" class="d"></path><path d="M297 692h2 0c0 1 0 1 1 1 2 0 2 1 3 1 1 1 2 2 3 2 1 1 3 2 4 2 2 0 3 2 5 2-2 1-3 1-4 3l-2-1-9-5c-3-2-6-2-9-2 0-1 1-1 1-1h3c1 0 1-1 2-2z" class="R"></path><path d="M480 119c1 2 1 2 1 4v1h0c-1 1-1 2-1 3h0 0c2-1 2-3 3-4v-1h1c0 1 0 3-1 4 1 2 0 1 1 2 0 0-2 1-2 2-1 1-2 1-2 2-1 0-2 1-2 1-1 1-2 2-3 2v1c-1 0 0 0-1 1-2 1-4 3-6 4h-1l2-2 4-4 1-1h-1c-1 1-1 0-2 1 0-1 0-1 1-2 1 0 2-1 2-1 3-2 4-4 4-7l1-2c1-1 1-2 1-4z" class="E"></path><path d="M327 308c2-5 6-8 10-10 1-1 3-2 4-1l-1 1v1h2v1h0c-4 1-6 2-8 5l1 1-2 1h1c1 1 1 1 1 2l-1 1h1 2c0 1 0 1-1 2v1c0-1-1-1-2-1l-1-2c-1 1-1 1-1 2l-2-1-1-1c0-1 0-1-1-2h-1z" class="b"></path><defs><linearGradient id="A" x1="736.291" y1="396.463" x2="739.834" y2="386.582" xlink:href="#B"><stop offset="0" stop-color="#282a2a"></stop><stop offset="1" stop-color="#484747"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M742 385c0 1 0 1 1 2l1 1-12 14-2 3v-4c-1-3 1-5 0-8l3-2v1c-1 1-1 2-1 2 1 0 2-1 3-2l7-7z"></path><path d="M730 393l3-2v1c-1 1-1 2-1 2l-1 2h1 0l1-1c0 2-1 5-1 7l-2 3v-4c-1-3 1-5 0-8z" class="f"></path><path d="M421 231l5-7c2-1 3-3 4-4 3-2 5-5 8-7h0c-2 2-4 5-6 8h1c2-1 4-3 6-5l1 1-3 3c-1 0-2 1-3 2 3 0-1 0 2 0h3v1h0l-1 1c-1 0-1 0-2 1h-2l-2 1c-3 1-5 3-8 5v-1c1-1 1-1 1-3l-3 4h-1z" class="d"></path><path d="M425 227c0-1 1-2 1-3l1 1h1c1 0 2 0 4 1-3 1-5 3-8 5v-1c1-1 1-1 1-3z" class="E"></path><path d="M613 838h1 1c4-2 8 0 12-2h1l1 1h4v1c-1 1-1 1-2 1h-1c-1 1-2 1-4 1h0c-4 1-7 0-11 0l-3 9c-1 4-1 11 0 14h0c1 3 2 5 3 7-3-2-3-5-4-9s-1-11 0-14v-1c1-1 3-5 2-6h0v-2z" class="V"></path><path d="M690 748c7-1 15-3 21-7h1c-7 6-14 6-23 8-3 1-7 2-11 3-2 0-5-1-7-1-5-1-12-2-16-5 1 0 1-1 1-1 3 1 5 2 8 3 8 1 15 2 23-1 1 0 2 1 3 1z" class="C"></path><path d="M484 128l2-1c-1 2-1 2-1 3h1c0 3-3 7-2 11 0 2-1 3-1 4l-1 1c-1 0-2 1-2 2 0 0-1 0-1 1-1 0-2 1-3 1-2 0-3 1-4 3l-1-1 4-4v-1l2-2c0-2 2-2 3-4h0l1-1c0-2 1-3 2-4s1-2 2-3h0c-1-1-1-1-1 0h-2l1-1h-1v1c-1 1-4 5-6 5h-1l1-1h1c2-2 5-4 5-7 0-1 2-2 2-2z" class="J"></path><path d="M446 286v1c-12 4-22 10-32 19l-10 9h-3l4-4c2-1 3-3 4-3l3-3c0-2 3-4 5-5 1 0 0 0 1-1s1-1 3-2v1 1h0l2-2 4-2 4-3h1l1-1h1c1-1 2-1 3-1 0-1 1-1 2-1 0-1 1-1 2-1h0l1-1 1 1c1-1 1-1 3-2z" class="Q"></path><path d="M741 652h1c-1-1-2-1-3-2h2 1c2 0 3 0 5-1l1-1c2 0 4-3 5-5h0l3-6v3h1s0-1 1-1c1 2 0 3-1 5l-1 1s0 1-1 2h0c-1 2-3 4-5 4-2 1-3 2-5 3-3 0-6 1-9 1h0v-1l5-1v-1z" class="E"></path><path d="M728 690l1-1c1 1 1 1 2 1l-2 2h-1v1 1h2l-2 2c-7 0-13 5-19 8v-1h-1c0 1-1 1-2 1 0 0 0-1-1-2 2-1 3-3 5-3 1-1 0 0 2-1s3-1 5-3c1-1 4-2 5-2s4-1 5-1l1-1h0v-1z" class="R"></path><path d="M559 919c5 1 24 6 27 4l10 1c-11 3-27 3-38 0-1 0 0-1 0-1h2 1c1 1 4 1 6 1 1 0 0-1 1 0h1v-2c-2 0-4 0-6-1h0c-2-1-2 0-4-1v-1z" class="m"></path><path d="M734 232h1c2 0 4 1 7 2v-1h1c7 3 14 6 20 10 3 2 6 3 8 5l-1 1c0-1-1-1-1-1-3-1-4-2-7-3-1-1-3-2-5-2-3-2-8-4-11-5-1-1-2-2-3-2s-2 0-4-1c-1-1-1 0-2-1-1 0-2-1-2-1l-1-1z" class="I"></path><path d="M735 251c1 1 3 4 5 4h0c4 2 6 5 9 7l1 1c1 0 2 1 2 1 2 1 3 1 5 2l4 1h0c-1 1-2 1-3 0h-5-1c-4-1-9-3-13-5l-1-1c1-1 0-1-1-2 1-1 2 0 3 0v-1-1l-2-1c-1-1-2-1-3-2 0-1 0-2-1-2h0c0-1 0-1 1-1z" class="J"></path><path d="M724 266l1 1h1c-1-1-1-2-2-2v-2h3c1 1 2 2 4 2v1c1 0 2 1 2 1 6 4 12 7 19 8 2 1 6 1 9 0l-5 4v-1-1c-3 0-7-1-10-2l-1-1c-2 0-4-1-6-2s-4-1-6-1h-1-3l-1-1-1 1 2 2h-1 0c-2 0-2 0-3-1s-1-2-1-3l2 1-2-4z" class="i"></path><path d="M422 839h1c2-1 3-2 5-3 2 2 3 4 4 6v2l1 1h-2l-3 6-4 4v-8l-2-8z" class="X"></path><path d="M552 240h1c1 0 2-1 3-1h0c-3 2-8 6-11 7-6 2-13 3-19 4-3 0-6 0-8 1h-1c-1 1 1 0-1 1-1 0-5 0-6-1l1-1h4 2c0-1 0-1 1-1s3 1 4 0h1 2c-1 0-2 0-3-1h-1c-1 1-1 0-2 0 1 0 2-1 3-1h5c1-1 1-1 2-1h1 2c0-1 0-1 2-1h2c1-1 4 0 5-1h2c3-1 4-2 7-3l2-1z" class="E"></path><path d="M727 679c3-1 7-1 11-2l1 1-1 1c-1 1 0 1-1 1 1 1 2 1 3 1v1 1l-1-1c-2 0-4 0-5 1h-4 0-4c-2 1-4 0-6 1h-2l1-2c-2 0-2 1-4 2-2-1-3-1-4-1l-1-1c6-2 10-3 16-3h1z" class="a"></path><path d="M726 679h1 4 1l1 2c-2 1-6 1-8 1 0-1 1-2 1-3z" class="O"></path><path d="M614 150c0 1 1 1 1 2 1 2 1 5 0 7v1l-1 1c-3 2-4 4-7 6h0-1c-2 2-8 6-9 9h-1 0v-1l-1-1c0-2 1-3 2-5l2-1c1-2 4-3 6-4l3-3 3-3v-1l2-2v-2c0-1 0-2 1-3z" class="l"></path><path d="M640 826c3-3 7-5 11-6l4-1h1c4-1 8-1 11 0 6 2 10 5 14 10h0c1 2 2 3 2 5l1 1v2l1 1c0 2 0 7-1 10h0c0 1 0 2-1 3h0c0 1-1 1-1 2v1c-1 0-1 0-1 1l-1-1 2-2v-2c1-2 1-2 1-4 0-1 0 0 1-1v-7h-1v-4h-1c-1-3-4-9-7-10l-1-1h0c-2-1-5-2-7-3h-11 0c-5 1-16 6-19 10-1 2-2 6-4 8v-1c0-1 0-1 1-2l3-5c0-2 1-3 3-4z" class="D"></path><path d="M500 262h-2c-11-2-21-1-32 0-3 0-10 2-12 1 2-1 3-1 4-1l13-1c2-1 5 0 7-1 3 0 7-1 10 0 4 1 7 0 10 1h4c3 1 6 1 8 1l2-1c3 0 6 1 10 2-2 1-3 0-4 2 2 1 4 0 6 2h-1v1l-3-1c-2 0-4-1-7-1h0c-1 0-2 0-3-1h-1c-1 0-2 0-2-1-3-1-5-1-7-2z" class="Y"></path><path d="M572 224c0 3-1 4-3 6l-1 1 3-1-3 3c-2 0-6 3-8 4v2c-1 1-3 2-3 3-3 2-6 3-9 5l-1 1-1-1c-3 0-6 1-10 2h0l-18 2c2-1 5-1 8-1 6-1 13-2 19-4 3-1 8-5 11-7h0c-1 0-2 1-3 1h-1c3-2 6-4 8-6 3-2 6-5 9-8l1 1v-2l2-1z" class="H"></path><path d="M477 155c2-1 3-3 3-4 1-2 1-3 3-5h0v2l-1 1 1 1c0-1 0 0 1-1l2-2c-1 1-1 2-2 3l-1 2v1l-2 3c-1 1-1 1-1 2l-1 2h-1 0l-1 1h1l-1 1v1l-5 11c-1 2-2 3-2 5-1 1-1 1-1 2l-1 1c-1 4-4 8-7 11h0l1-3c1-1 1-2 2-3 0-1 0-1 1-2s1-3 2-4v-1h-1l2-2v-1l2-2v-1c0-1 1-1 1-2l-1 1-1 1v-1-2l1-1v-1c-1-1-1 0 0-2 0-1 0 0 1-1l5-8c1-1 1-2 1-3z" class="G"></path><path d="M470 167h1 1l1-1h0c0 2-1 4-2 5v1l-1 1-1 1v-1-2l1-1v-1c-1-1-1 0 0-2z" class="E"></path><path d="M725 575c2 3 3 5 4 9 1 8-1 14-4 22-1 2-3 4-3 7-1 4-1 10 0 14s2 8 4 11c1 2 2 3 2 4v1c-3-4-5-8-6-12v-1c-2-5-3-12-2-17 1-1 1-2 1-3 4-7 7-16 5-24-1-3-2-6-3-10h0c1 0 1 0 2-1z" class="V"></path><path d="M468 284h2v-1c-1 0-2 0-2-1 2 0 4 1 6 2h2l-2 1c1 0 1 0 2 1h-5c-12 0-25 2-36 8-6 2-11 6-16 10-1 0-1 1-2 1-1 1-2 1-3 1 10-9 20-15 32-19 4-1 7-1 10-2 2 0 7 1 9-1h3z" class="H"></path><path d="M552 234c2-1 3-2 5-4 4-3 7-7 11-10 0 1 0 2 1 2s1 0 2-1h1 0l-2 3v1 2l-1-1c-3 3-6 6-9 8l1-1c0-1 0-1 1-2-1 1-2 1-3 2l-3 2h0l-1 1c-4 2-10 4-14 5l-22 3c-3 0-6 1-8 0h3c4 0 9-1 13-2 6-1 12-2 18-4 3 0 6-1 8-3-2 0-3 2-5 1h0 1c1-1 2-1 3-2z" class="I"></path><path d="M755 160c17-5 34-7 51-9 6 0 13 0 18 1 12 2 20 8 27 17l1 1h-1c-1-1-1-1-2-1 0-1-1-1-1-2l-5-3c-1-1-2-2-4-2-2-1-4-2-7-2-4-1-9-2-13-2h12 1c0-1-2-1-3-1-2-1-4-1-6-1 1 0 3-1 5 0 2 0 5 1 7 1-2-3-9-4-13-4l-1-1c-9-1-19 0-28 1-3 1-6 1-8 1-7 1-15 2-21 4-3 1-6 1-9 2z" class="H"></path><path d="M565 151l5-15c1 7 0 16 2 23h0l-2 1v5c-1 0-1-1-1-1h0v1c-1 1-1 3-1 4v-12c-1 4-3 8-4 12h-1c0-2 1-4-1-5v-1l4-11-1-1z" class="I"></path><path d="M566 152v-1c2-3 3-7 3-10 1 1 1 1 0 2l-1 14c-1 4-3 8-4 12h-1c0-2 1-4-1-5v-1l4-11z" class="l"></path><path d="M317 611c1-2 1-3 1-5-1-3-3-6-2-9 0-2 2-3 4-5l1 2v1c-2 1-2 3-2 5v1l1 1h1c0 1 1 1 2 2s3 1 4 2c0 1 1 2 2 3l-1 1c1 1 0 2 1 4 0 1-1 3-1 4v1c0 1-1 1 0 2v3h-1 0c0-1 0-1-1-2 0-1 0 0-1-1-4 2-6 2-10 1h1 2 1c2 0 4 0 5-1s1-1 1-2c0-2 0-7-1-9-1-1-3-2-4-3v-1l-3 6v-1z" class="C"></path><path d="M546 253h-1c0 1-1 1-2 1s0 0-1 1h6c-2 1-3 2-5 3h-2 6 1c2 0 3 0 4-1h0v2h0c-2 0-2 1-4 1-1 1-3 1-5 1v1h3v1l-15 1-1-1h-2c-1-1-2-1-3-1h-1l4-1h-7v-1h6 1c0-1 0-1 1-1 1-1 3 0 5-1h3c1-1 1 0 2 0 2-1 2-1 3-1v-1c-1 1-1 1-2 1-2-1-2-1-3-1h-1c1 0 1 0 2-1h1c1 0 1-1 2-2h5z" class="Q"></path><defs><linearGradient id="C" x1="758.775" y1="318.81" x2="751.202" y2="306.327" xlink:href="#B"><stop offset="0" stop-color="#9f9e9d"></stop><stop offset="1" stop-color="#bebdbc"></stop></linearGradient></defs><path fill="url(#C)" d="M741 311c2-1 3-1 4-2 6-2 14-2 20 0h1c2 1 5 1 6 4v1c-2-1-5-1-7-2-5-1-10 0-15 1-4 1-8 3-12 4-1 0-2 1-3 2h-2l2-2c2-2 5-3 6-6z"></path><path d="M557 242h2c0 1-1 2-2 3s-1 2-2 3h2s1 0 2 1c-1 1-2 1-3 1v1h2l-1 2-4 3-6 2h-6 2c2-1 3-2 5-3h-6c1-1 0-1 1-1s2 0 2-1h1-5 0v-1h-1v-1c2 0 5-2 7-3l1-1c3-2 6-3 9-5z" class="L"></path><path d="M547 248l1-1c1 1 1 1 1 2 1 0 1 0 2-1v1h1 1c-2 3-4 3-7 4h-5 0v-1h-1v-1c2 0 5-2 7-3z" class="R"></path><path d="M342 299c2-1 2-1 3 0h2c1 1 1 1 2 1l1 1c-1 1-2 2-2 4l-3 6c-2 0-3 1-5 1v1c-2 0-1-1-2-1h-1l-1 1v-1c1-1 1-1 1-2h-2-1l1-1c0-1 0-1-1-2h-1l2-1-1-1c2-3 4-4 8-5h0v-1z" class="c"></path><path d="M754 372l1 1-13 12-7 7c-1 1-2 2-3 2 0 0 0-1 1-2v-1l-3 2-3 3h0l3-8c1-3 3-5 5-7h2c4-1 7-2 10-4h1c1-1 2-2 4-3l2-2z" class="i"></path><path d="M735 381h2l1 2h2c1 0 1-1 2 0 0 1-1 2-2 2h0c-1 0-2 0-3 1-1 0-1 1-2 1l1-2h0-1v-3-1z" class="C"></path><path d="M754 372l1 1-13 12-7 7c0-1 0-1 1-2v-1c1-2 2-3 4-4h0c1 0 2-1 2-2-1-1-1 0-2 0h-2l-1-2c4-1 7-2 10-4h1c1-1 2-2 4-3l2-2z" class="H"></path><path d="M434 145h1v3c0 1 0 2 1 3l-1 3c-1 3-5 7-6 10l-1 1v-1-2c-2 2-5 4-7 6l1 1h-1v1c-2 3-5 4-7 7l-1 1c0-1 0-1 1-2-1 0-3 2-3 3l-1 1-2 2c-1 1-1 2-2 3l-1 1c-2 2-3 5-5 8v1c-1 1-1 0-1 1s-1 2-1 2v-3c0-1 1-2 1-3l3-4v-1l3-3 1-2c1-2 4-5 6-7l6-6c2-2 3-4 5-5 3-2 7-6 8-9l1-1c0-1 0-1 1-2v-2c1-1 1-2 1-4v-1z" class="d"></path><path d="M323 591l5 8h-1c3 3 4 6 6 9h-1c0 1 0 1-1 2 0 1 0 1-1 3l-1 9c0 5-1 11-4 14l-1 1v-1c2-4 4-7 4-12v-3c-1-1 0-1 0-2v-1c0-1 1-3 1-4-1-2 0-3-1-4l1-1c-1-1-2-2-2-3-1-1-3-1-4-2s-2-1-2-2h-1l-1-1v-1c0-2 0-4 2-5v-1l-1-2 3-1z" class="N"></path><path d="M323 591l5 8h-1 0-1c0-1 0-1-1-1v1c0 1-1 2-1 3l-2-1c-1 0-1 0-2 1l-1-1v-1c0-2 0-4 2-5v-1l-1-2 3-1z" class="j"></path><path d="M321 595c0-1 1-1 1-2h0 1c0 1 1 2 1 3h-1l-1 3h-1v-2-2z" class="U"></path><path d="M727 365c2 4 5 7 9 9h6c6-2 10-4 15-8-1 2-2 4-4 5l1 1-2 2c-2 1-3 2-4 3h-1l-4 2-3 1c-3 0-6 0-9-1 1-3-1-2-1-4l-1-1c-1 0-1 0-1-1v-2c0-1-2-4-2-5l1-1z" class="n"></path><path d="M732 374c2 1 4 2 6 2 0 1 0 1 1 1h2c1 0 2 1 2 2l-3 1c-2-2-3-2-5-2l-1-1c-1-1-1-1-2-3z" class="C"></path><path d="M742 374c6-2 10-4 15-8-1 2-2 4-4 5l1 1-2 2c-2 1-3 2-4 3h-1l-4 2c0-1-1-2-2-2h-2c-1 0-1 0-1-1-2 0-4-1-6-2l-1-1c4 0 7 3 11 1h0z" class="F"></path><path d="M738 376c5 2 9-2 14-2-2 1-3 2-4 3h-1l-4 2c0-1-1-2-2-2h-2c-1 0-1 0-1-1z" class="I"></path><path d="M295 575c1 1 2 1 3 2-4 10-5 17-1 27 2 4 4 8 8 10 2 1 6 1 9 0l1-1h0c1 0 1-1 2-2v1c1 1 0 2 0 2-1 1-2 2-3 2-4 1-9 0-12-3h-1c0 11 0 21-7 30l-1-1c1-1 2-3 3-4 2-4 3-8 3-11v-1c1-4 2-9 1-12s-2-5-3-7c-4-8-7-15-5-23v-1c1-3 2-6 3-8z" class="D"></path><path d="M431 827l3-1c2-1 3-2 4-4l-1-1c0-1 0-3 1-4 0-2-1-4 0-6v-1 8c0 1 0 1 1 2v1 1l3 1c-4 6-8 10-14 13-2 1-3 2-5 3h-1c-3 0-6 1-8 1h-6-1-5c-3 0-8 0-11 1h-1l1-1-2-2h1 1 0c2 1 5 1 6 1 9 0 17-1 25-4l3-1c2-1 7-4 8-6l-2-1z" class="T"></path><path d="M273 394h0 3l3 4c1 1 1 2 2 2l1 2h0c1 2 5 8 7 9l1 2-2 1c-1-1-2-1-3 0-2-1-6-2-8-4-4-4-4-11-4-16z" class="r"></path><defs><linearGradient id="D" x1="778.612" y1="217.174" x2="786.888" y2="234.326" xlink:href="#B"><stop offset="0" stop-color="#878889"></stop><stop offset="1" stop-color="#a9a4a0"></stop></linearGradient></defs><path fill="url(#D)" d="M752 216v1c7 2 14 5 21 6 9 3 19 4 27 4 4 1 10 1 14 0l9-1c1-1 2-1 3-2l1 1c-12 5-26 4-39 4-8-1-15-2-22-4-1-1-3-1-3-2h-1c0-2-9-5-11-6l1-1z"></path><path d="M612 196l1-1h0l1 1c0 1 0 0 1 1l-2 3-2 2c1 2 0 0 1 1s1 3 1 5c1-1 1-1 1-2h1v5h1 0c0-1 0-2 1-2 1 2-1 5 1 6 0-1 0-1 1-2 0-1 0-2 1-2h0l-1 3v4h1 0c1-1 1-2 2-3l1-1h0c0 2-1 4-2 7v1h2l-1 2v1c3 0 4-1 6-2l1 1c-1 1-4 3-4 4h0v1h-2c0 1 1 2 1 4-1-2-3-4-4-6-2-3-2-7-4-10-3-5-7-9-9-13 1-3 3-6 5-8z" class="I"></path><path d="M267 646l-2-2v-1h0c-1-1-1-3-1-4-1-2-1-4-1-6l1-1 1-3c1 2 0 5 1 8v1l1 2h0c0 1 1 2 1 3h1c0 1 0 1 1 2v1c1 1 2 1 3 2l1 1h0 2c1 1 4 0 5 1l-1 1c1 0 0 0 1 1h4c5 1 10 1 14 0h3s3-1 4-1h0 3l2 1c-3 1-6 2-9 2l-1 1c-5 1-12 1-17 0-4-1-8-2-12-4-2-2-3-4-5-5h0z" class="r"></path><path d="M620 151c2 3 1 9 1 12-2 6-5 11-7 17-1 2-2 3-2 5h1 1v1c0 1-1 2-1 2 0 1-1 2-1 3l-2 3 2 1v1c-2 2-4 5-5 8l-1-1c-1-1-1-2-2-3 3-3 2-4 3-8 1-3 2-7 2-10 1-9 11-16 11-25v-4-2z" class="G"></path><path d="M610 194l2 1v1c-2 2-4 5-5 8l-1-1v-2h0c0-2 1-2 1-4l3-3z" class="J"></path><path d="M478 115c1 1 1 2 2 3h1c0-2 0-4-1-6 3 2 7 5 9 8h1c1 2 1 5 2 7l4-8v-1l1 1-1 2v1c-1 1-1 2-1 3-1 1-1 2-2 4l-2 2-7 10c-1-4 2-8 2-11h-1c0-1 0-1 1-3l-2 1c-1-1 0 0-1-2 1-1 1-3 1-4h-1v1c-1 1-1 3-3 4h0 0c0-1 0-2 1-3h0v-1c0-2 0-2-1-4l-3-3 1-1z" class="B"></path><path d="M322 646c3 2 3-3 6-3l-1 2 1 1h0v4l-1 1c-1 0-1 1-2 2v1l2 1s1 0 1 1h-4-3v-1-1c-1 1-3 0-3 0l-1-1c-2 0-3 2-5 1-3 1-5 2-6 4h-1c-1-1-4-2-6-2l-3 1c-5 0-9 0-14-1-3-1-5-2-7-3h0c-4-1-7-4-8-7h0c2 1 3 3 5 5 4 2 8 3 12 4 5 1 12 1 17 0l1-1c3 0 6-1 9-2 3-2 6-3 8-4l3-2z" class="H"></path><path d="M299 656h2l6-2c4-1 9-3 13-5v1h-1l-1 1-6 3c-3 1-5 2-6 4h-1c-1-1-4-2-6-2z" class="W"></path><path d="M328 646h0v4l-1 1c-1 0-1 1-2 2v1l2 1s1 0 1 1h-4-3v-1-1c1-2 3-3 5-6 0-1 1-2 2-2z" class="B"></path><path d="M689 133h3c1 1 2 2 4 2v1l1-1c2-1 7-1 9-1h5l1 1h0 3 2c-1 1-2 2-3 2-3 2-5 3-6 7h-1v1l1 3c1 1 1 0 1 2 1 1 2 3 2 4l-1 1c0-1 0-2-1-2 0-1-1-3-2-4h0c-1-2-1-2-3-4l-1 1v2 1h-1v1c-1 1-1 2-1 3h-3c1-1 1-3 2-4 0-2 0-3 1-4v-2c0-3-7-6-9-8-1 0-2-1-3-2z" class="I"></path><path d="M689 133h3c1 1 2 2 4 2v1l1-1c2-1 7-1 9-1h5l1 1h-8v8c-1 1-1 0-2 1l-1 1v-2c0-3-7-6-9-8-1 0-2-1-3-2z" class="D"></path><path d="M461 283v-2h5v-1h-5c-1 1-2 1-3 1h-3-5-1l-2 1c-1 0-2 0-3-1l4-1h9 1l2-1h7 0 0v-1h-1c-2 0-3-1-5 0s-5 0-7 0c-2 1-4 1-5 0h0 1c2-1 3 0 5-1 5-1 10 0 15 0h3c1 0 1 0 1 1 2 1 3 1 5 1 4 0 12 3 15 5h0c0 1 0 1 1 2l-1 1c-1-1-2-1-2-1l-1 1c-1-2-3-2-5-2s-4-1-6-2c-2 0-4-1-6-1l2 1v1h0-2c-2-1-4-2-6-2 0 1 1 1 2 1v1h-2l-2-1h-5z" class="G"></path><path d="M479 279c4 0 12 3 15 5h0c0 1 0 1 1 2l-1 1c-1-1-2-1-2-1l-1 1c-1-2-3-2-5-2s-4-1-6-2c3-2 7 0 10 0-4-1-7-3-11-4z" class="C"></path><path d="M360 753h4l-1 1h-2c-1 0-2 1-3 2s-1 4-1 6l1 2c0 3 1 4 3 7 0 1 0 1 1 2 3-5 6-6 11-7 3 0 5 1 7 3s2 4 2 7h-1c-1-1-2-1-2-3-1-1-1-1-1-2-3-1-5-1-8-1-3 1-5 3-6 6h-1v3c0 9 8 8 13 14 1 1 0 2 0 3h0c-3-5-10-6-14-11-2-3-2-7-1-11h0c-1-1-2-2-2-3-3-3-4-9-3-13 1-2 2-4 4-5z" class="X"></path><path d="M384 256c1-4 4-7 4-11v-1c1-3 1-6 0-9 0-3 0-8 1-12 1-9 3-21 9-28v3l-1 1v2h-1l-3 9v2l-1 1v2l-1 2v4h0c0 1 1 1 1 2 1 1 0 4 1 6 0 1-1 5 0 6 0 1 1 1 0 2 0 2-1 6 0 7l-1 2v1c0 2-2 4-3 6-1 0-2 1-3 2v-1h0c-1 1-2 1-2 2z" class="F"></path><path d="M516 941c1-1 0-1 1-1 1-1 1 0 2 0l2 1c-3 6-7 15-6 22v1c0 1-2 2-2 3-2 1-2 4-3 6 0-4-1-6-4-9v-2l3 1v-1-1c1-2 1-3 1-4v-1-2-1h1c1-1 0-5 0-7s1-3 2-5v-1c1 0 2 0 3 1z" class="a"></path><path d="M513 941c1 0 2 1 2 3-1 1-2 2-4 2 0-2 1-3 2-5z" class="T"></path><path d="M642 216v4l-1 1c0 3-2 5-4 8l-4 4-1 1c-1 2-1 2-2 3v1c-1 1-1 1-1 2-1-1-2-2-3-4l-1-1c0-1 0-1-1-2 0-2-1-3-1-4h2v-1h0c0-1 3-3 4-4l-1-1h0c2-2 4-2 6-3l5-1 1-1c1 0 1-2 2-2z" class="c"></path><path d="M635 221c2-1 3-1 5-1l-7 5h-1-1l-1-1c1 0 1-1 2-1s1-1 2-1l1-1z" class="Y"></path><path d="M633 225v1h0c-2 2-4 5-6 6h0l-1 1v1c0 1 0 0-1 1 0-1 0-1-1-2 0-2-1-3-1-4h2v-1h4l3-3h1z" class="E"></path><path d="M628 223h0c2-2 4-2 6-3l1 1-1 1c-1 0-1 1-2 1s-1 1-2 1l1 1h1l-3 3h-4 0c0-1 3-3 4-4l-1-1z" class="d"></path><path d="M626 236l2 1 2-2-1-1v-1c2-2 3-4 6-6 3-1 4-4 6-6 0 3-2 5-4 8l-4 4-1 1c-1 2-1 2-2 3v1c-1 1-1 1-1 2-1-1-2-2-3-4z" class="W"></path><path d="M501 929l2 2c0 1 1 2 1 2 0 1 1 1 1 1 1 1 1 1 1 2l1 1c1 1 2 3 2 5v1 1 1c1 1 1 1 1 3v2l1 1c0 1-1 1-1 2v1 2 1c0 1 0 2-1 4v1 1l-3-1v-1c-1-10-4-17-10-25l1-2c2-1 2-2 4-2l-1-1h-2l2-1 1-1z" class="T"></path><path d="M501 929l2 2c0 1 1 2 1 2 0 1 1 1 1 1 1 1 1 1 1 2h-3l-1 1-1-1c-1-2-1-2 0-3v-1l-1-1h-2l2-1 1-1zm9 19h-2c-1-1-2-2-2-4-1-2-3-5-3-6h1c1-1 1-1 3-1 1 1 2 3 2 5v1 1 1c1 1 1 1 1 3z" class="a"></path><defs><linearGradient id="E" x1="843.483" y1="177.637" x2="834.617" y2="163.799" xlink:href="#B"><stop offset="0" stop-color="#adaaaa"></stop><stop offset="1" stop-color="#c6c7c4"></stop></linearGradient></defs><path fill="url(#E)" d="M839 162c2 0 3 1 4 2l5 3c0 1 1 1 1 2 1 0 1 0 2 1h1c2 2 4 6 6 8-5-3-10-5-16-7-9-2-20-1-29 0l-13 2h-2c-1 1-1 1-2 1h-3c1-1 2-1 3-1l2-1c2 0 4 0 5-1h2 2c1-1 0-1 2-1l9-1 5-1c2 0 3-1 5-1h2c2-1 3 0 5-1 1-1 2-1 3-1v-1c-1 0-1 0-1-1 0 0 1 0 1-1h1z"></path><path d="M839 162c2 0 3 1 4 2l1 1c1 1 1 2 3 3-3 0-6-1-9-1-4 1-7 1-10 0h2c2-1 3 0 5-1 1-1 2-1 3-1v-1c-1 0-1 0-1-1 0 0 1 0 1-1h1z" class="E"></path><path d="M695 712v1c2 1 4 1 6 1l2 2c2-1 3-1 5-1-1 2-2 3-3 5l-5-1c-4 0-7 0-11 1l-2 1c-1 1-3 2-4 4h0c0 2 0 3-1 4-1 0-1-1-3-2h-3c-1 0-1 0-2-1 1-1 1-1 1-2s1-1 1-2h2l1-3 7-4c2-1 4-2 7-2 0 0 1 0 2-1z" class="Z"></path><path d="M686 715c2-1 4-2 7-2-2 1-4 2-5 3l-1 1c-1 1-3 1-4 2-2 0-3 2-5 3l1-3 7-4z" class="R"></path><path d="M695 713c2 1 4 1 6 1 0 1 0 1-1 2l-1-1c-1 1-2 1-3 2 1 0 1 1 2 0v1 1h2c-4 0-7 0-11 1l-2 1 2-2c1 0 1 0 2-1 1 0 3-1 4-1l2-2-2-2z" class="m"></path><path d="M701 714l2 2c2-1 3-1 5-1-1 2-2 3-3 5l-5-1h-2v-1-1c-1 1-1 0-2 0 1-1 2-1 3-2l1 1c1-1 1-1 1-2z" class="o"></path><path d="M389 838c-2-1-3-4-3-6l-1-1c-1-4-10-7-14-9-7-3-15-3-23 1-3 1-8 7-9 11l-1 3c-1 2-1 5-1 8v1c1 2 1 4 2 6l1 1c1 2 4 5 7 7h0 0v1l-3-2c-4-2-7-7-8-12h0c-1-4 0-9 1-12 2-6 6-12 12-14 8-4 20-3 27 2l9 6c-4-7-11-13-14-21h0l-2-11 1-1v1c1 1 1 2 1 3 0 2 1 5 2 7 1-3 1-5 2-8 0 4-2 11 0 15 4 7 13 14 20 17h-3c-1-1-2-2-3-2-1 1-1 1-1 2v1h-1c0 1 1 3 1 4l2 2h-1z" class="h"></path><path d="M553 256l1 1-1 2h0c2-2 3-4 5-5 1 0 2 0 2 1l-5 6 1 1c-3 2-6 4-10 5-1 1-2 1-2 1 2 1 3 0 5 0v1l-1 1h2c-1 1-1 1-2 1h0-1-3v-1-1c-2 0-5 1-7-1h-2-1c-2 0-5 1-7 0h-4v-1h1c-2-2-4-1-6-2 1-2 2-1 4-2 1 0 2 1 3 1 2 1 5 1 7 1l-1-1 15-1v-1h-3v-1c2 0 4 0 5-1 2 0 2-1 4-1h0v-2h0c-1 1-2 1-4 1h-1l6-2z" class="B"></path><path d="M532 265c4 0 9 0 13-1l6-3 1 1c-3 2-7 3-9 5-1 0-1 0-2 1h-4-2-1c-2 0-5 1-7 0h-4v-1h1c-2-2-4-1-6-2 1-2 2-1 4-2 1 0 2 1 3 1 2 1 5 1 7 1z" class="d"></path><path d="M266 690c-4-1-8-3-12-5-7-5-14-12-18-19 6 4 12 7 19 10 3 1 5 2 8 2v3h1v1h-1v2h0c1 1 1 1 2 1 0 1-1 1-1 2l3 1-1 2z" class="U"></path><path d="M256 681h-1l-1 1h-1v-1l1-1-1-1-2 1h-1v-1c-1-2-2-1-4-2l1-1c2 0 4 1 6 1 1-1 2-1 2-1 3 1 5 2 8 2v3h1v1h-1v2h0c1 1 1 1 2 1 0 1-1 1-1 2-2-1-5-1-6-2l-2-1v-3z" class="b"></path><path d="M263 681h1v1h-1v2h0c1 1 1 1 2 1 0 1-1 1-1 2-2-1-5-1-6-2l-2-1v-3h1v2h2c0 1 0 0 1 1l3-3z" class="f"></path><defs><linearGradient id="F" x1="293.934" y1="524.27" x2="275.533" y2="529.107" xlink:href="#B"><stop offset="0" stop-color="#dbd8d7"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#F)" d="M292 501h4l-7 7-3 3c-1 1-1 2-2 4-1 1-1 3-2 4v3 1l1 3v2h0c1 5 1 11 2 15 0 1-1 2-1 3-1 0-1 1-2 2l-1 1v1h-1v-1-3c-1-1-1-4-1-5-1-3 0-11 0-14v-1-5l1-1c-1-1-1-2-1-2l1-1c0-2 1-5 2-6l1-1 2-4h1l1-1c2-2 3-3 5-4z"></path><path d="M644 767c4-1 8-1 12 1 2 1 5 5 5 8 1 3 0 7-2 10-3 4-11 5-12 11l3 8c2-3 3-6 2-9h1c0 1-1 3-1 4 0 5-2 10-4 13-3 5-8 9-12 13l-2 1c2-4 7-6 9-10 4-4 5-9 4-14 0-2-1-5-1-7 0-6 8-7 11-11 2-2 2-5 1-8 0-2-1-4-3-5-2-2-6-3-9-2-2 0-2 1-3 2-1 2-1 2-2 3h-1v-3c1-2 2-4 4-5z" class="q"></path><path d="M711 693h4c1 0 2-2 3-2s1 1 4 1c1 0 2-1 3-2v1h1l2-1v1h0l-1 1c-1 0-4 1-5 1s-4 1-5 2c-2 2-3 2-5 3s-1 0-2 1c-2 0-3 2-5 3 1 1 1 2 1 2v3h-2c1 1 2 2 1 3h-1-2l-6-1c-1-1-2-1-2-2h-2c-2 0-2-1-4-1 1-1 1-2 3-3l6-4 8-3c2-1 4-1 6-3z" class="S"></path><path d="M705 702c1 1 1 2 1 2v3h-2c1 1 2 2 1 3h-1-2l-6-1c0-2 0-2 1-4l1 1 7-4z" class="O"></path><path d="M696 709c0-2 0-2 1-4l1 1 1 1c1 1 2 1 3 1s2 0 2-1c1 1 2 2 1 3h-1-2l-6-1z" class="o"></path><path d="M711 693h4c1 0 2-2 3-2s1 1 4 1c1 0 2-1 3-2v1h1l2-1v1h0l-1 1c-1 0-4 1-5 1s-4 1-5 2c0 0-1-1-2-1-2 1-3 3-6 3-2 0-3 2-5 3 0 0-2 1-3 1s-2 1-3 2h-1l-5 4c-2 0-2-1-4-1 1-1 1-2 3-3l6-4 8-3c2-1 4-1 6-3z" class="M"></path><path d="M723 558l1-1 2 3c2 2 2 5 1 7l-3 6 1 2c-1 1-1 1-2 1h0c-3 2-6 3-8 5-1 0-2 3-2 4-1 0-2 1-2 1-1 1-1 2-2 3l-1-1c3-5 5-9 6-14l2-5 1-5v-1c1-2 2-3 2-5h0 4z" class="U"></path><path d="M723 558l1-1 2 3s-1 0-1 1c-1 1-1 2-1 2-2-1-1-3-1-5z" class="j"></path><path d="M726 560c2 2 2 5 1 7l-3 6c-2 0-2 1-4 2h-1-2c-1 0-1 0-1-1 4-1 5-5 8-7v-1l-1 1-1-1 2-2v-1s0-1 1-2c0-1 1-1 1-1z" class="N"></path><path d="M714 574l2-5c1 0 2 0 2 1l-1 1c0 1-1 2-1 3s0 1 1 1h2 1c2-1 2-2 4-2l1 2c-1 1-1 1-2 1h0c-3 2-6 3-8 5-1 0-2 3-2 4-1 0-2 1-2 1-1 1-1 2-2 3l-1-1c3-5 5-9 6-14z" class="o"></path><path d="M724 573l1 2c-1 1-1 1-2 1l-1-1v1c-1 0-2 1-3 1h-3v-1l1-1h2 1c2-1 2-2 4-2z" class="L"></path><path d="M613 200l1 1v3h1c0-2 1-4 2-5s2-1 3-2c6-6 13-11 18-17 0-1 0-2 1-3h1c0 2 0 3-1 5h0c-1 2-2 3-3 4v1c-2 4-5 7-8 10-3 4-5 7-7 11l-1 3h0c-1 0-1 1-1 2-1 1-1 1-1 2-2-1 0-4-1-6-1 0-1 1-1 2h0-1v-5h-1c0 1 0 1-1 2 0-2 0-4-1-5s0 1-1-1l2-2z" class="F"></path><path d="M621 208h0v-2c-1-2-1 0-2 0h-1c3-4 5-9 9-12 2-2 3-4 5-5h2c0-1 1-2 2-2-2 4-5 7-8 10-3 4-5 7-7 11z" class="G"></path><path d="M573 229l1 1c-1 2-4 3-5 6h1 2v1c-1 1-2 1-2 2l-2 1v2h1 1 1c1-1 2-1 3-1h0c-1 1-1 2-2 3s-1 1-1 2-2 2-2 3l-3 1v-2c-1 0-1 1-2 2s-2 2-2 3l-2 2c0-1-1-1-2-1-2 1-3 3-5 5h0l1-2-1-1 4-3 1-2h-2v-1c1 0 2 0 3-1-1-1-2-1-2-1h-2c1-1 1-2 2-3s2-2 2-3h-2c0-1 2-2 3-3v-2c2-1 6-4 8-4l3-3 2-1z" class="i"></path><path d="M560 239h1s1-1 2-1v1l-2 1v1 1l-3 3h-1c1-1 2-2 2-3h-2c0-1 2-2 3-3z" class="M"></path><path d="M558 245h4 0l-1 1 1 1 2-1c-1 1-4 5-6 5h-2v-1c1 0 2 0 3-1-1-1-2-1-2-1h-2c1-1 1-2 2-3h1z" class="k"></path><path d="M573 229l1 1c-1 2-4 3-5 6h1 2v1c-1 1-2 1-2 2l-2 1v2h1 1 1c1-1 2-1 3-1h0c-1 1-1 2-2 3s-1 1-1 2-2 2-2 3l-3 1v-2c-1 0-1 1-2 2s-2 2-2 3l-2 2c0-1-1-1-2-1-2 1-3 3-5 5h0l1-2-1-1 4-3h1c3 0 4-3 6-5l5-4h-1-1c-2 0-2 1-3 1 0-1 1-2 2-3h-2l2-2v-1h-1 0l2-3h0-1-1l3-3 3-3 2-1z" class="I"></path><path d="M425 89l1-1v-3l1 1h1 0l-1 1c1 3 2 10 4 12 0 3 3 22 3 22 1 9 5 17 3 26v4h-1c-1-1-1-2-1-3v-3h-1c-1-1-1-2-1-4h0l-1-1v-7c-1-1-1-2-1-3-1-1 0-3 0-4l-1-1v-6h0c-1-2-1-3-1-4-1-1 0-3 0-4-1-2-1-3-1-4v-4c-1-1-1-1-1-2v-4c-1-1-1-1-1-2v-3c-1-2-1-2-1-3z" class="Q"></path><path d="M464 106c2 1 3 1 4 2h0c2 0 3 1 4 2h0c2 1 3 2 4 3l2 2-1 1 3 3c0 2 0 3-1 4l-1 2c0 3-1 5-4 7 0 0-1 1-2 1v-1 1c-1 0-1 0-2 1h-1-1c-1 1-1 2-2 2-1 1-1 2-2 2-1-1-1-1-2-1 2-1 3-2 5-3v-3l1-3 1-2v-6c0-3-1-6-2-8l-3-6z" class="F"></path><path d="M468 108c2 0 3 1 4 2h0c2 1 3 2 4 3l2 2-1 1 3 3c0 2 0 3-1 4l-1 2v-2-2c0-1 0-1-1-2l-1 1 1 7c-1-3-2-7-3-10l-1-1v-1l-1-1c-1 0-1-1-1-2h-1c0-2-1-3-2-4z" class="Q"></path><path d="M464 106c2 1 3 1 4 2h0c1 1 2 2 2 4h1c0 1 0 2 1 2l1 1v1 2c1 3 1 5 1 7l-1 1c-1-1-1-1-1-2l-1 5-1-1v-2h-1v-6c0-3-1-6-2-8l-3-6z" class="J"></path><path d="M335 712l1-1 1 1v2l1 1-1 1c5 1 10 4 14 7 2 1 3 2 4 3v1h-1c-1 0-1 1-2 1v1h-1c-3-2-6-3-10-2l-1 2-1-1h0v-1c0-1 0-2-1-3-2-3-6-5-9-5h-3-2v-2l1-1-2-2h-3v-1h2c1 0 3-1 5-1l2 1c0-1 0-1 1-2 1 1 2 1 4 1h1z" class="L"></path><path d="M345 721c1 1 2 1 3 3h-3-2l2-3z" class="a"></path><path d="M335 712l1-1 1 1v2l1 1-1 1c-2-1-4-2-6-2 0-1 0 0 1-1l2-1h1z" class="M"></path><path d="M339 723c0-1-1-1-2-2-1 0-2-1-2-2l-1-1c-1 0-2 0-3-1v-1h2l6 3c2 0 4 1 6 2h0l-2 3c-1-1-2-1-4-1z" class="Z"></path><path d="M322 713c1 0 3-1 5-1l2 1c0-1 0-1 1-2 1 1 2 1 4 1h1-1l-2 1c-1 1-1 0-1 1 1 1 1 1 2 1v1h-2v1c1 1 2 1 3 1l1 1c0 1 1 2 2 2 1 1 2 1 2 2 1 1 2 2 2 4h0l-1 2-1-1h0v-1c0-1 0-2-1-3-2-3-6-5-9-5h-3-2v-2l1-1-2-2h-3v-1h2z" class="N"></path><path d="M564 169c1-4 3-8 4-12v12c-1 3-2 6-1 9l-1 1v7c-1 2-2 3-2 5l-2-1v-3h-1-1-1 0c1-1 1-2 2-2l-1-1c-1 1-3 2-5 2h0l-1 1-1-1c0-1-1-2-1-3l1-2h0l2-3 1-2c0-1 0-2 1-3h1c0-1 0-1 1-2 0-3 2-5 3-8v1c2 1 1 3 1 5h1z" class="Q"></path><path d="M558 178l3-3v2c0 1-1 1-1 2 0 2-2 3-3 5h-1v-2c1-1 1-3 2-4z" class="H"></path><path d="M564 169l1 1v1c-1 1-1 2-1 4-1 1-1 3-1 5h0v1c-1 1-1 1-1 2l1 1-2 3h-1-1 0c1-1 1-2 2-2l-1-1c-1 1-3 2-5 2h0l1-2h1c1-2 3-3 3-5 2-3 3-6 4-10z" class="C"></path><path d="M564 175h1l-1 1c0 1 0 1 1 2v1 1l1-1h0v7c-1 2-2 3-2 5l-2-1v-3h-1l2-3-1-1c0-1 0-1 1-2v-1h0c0-2 0-4 1-5z" class="B"></path><path d="M564 169c1-4 3-8 4-12v12c-1 3-2 6-1 9l-1 1h0l-1 1v-1-1c-1-1-1-1-1-2l1-1h-1c0-2 0-3 1-4v-1l-1-1h0z" class="E"></path><path d="M562 163v1c1 3-2 5-2 8 1 1-1 4-2 6-1 1-1 3-2 4v2l-1 2-1 1-1-1c0-1-1-2-1-3l1-2h0l2-3 1-2c0-1 0-2 1-3h1c0-1 0-1 1-2 0-3 2-5 3-8z" class="C"></path><path d="M553 181h0l2-3c0 1 0 3-1 4h1 1v2l-1 2-1 1-1-1c0-1-1-2-1-3l1-2z" class="e"></path><path d="M330 613c2 0 2 0 3 1l1 1c2 2 3 4 3 7v1 1s-2 2-2 3c0 2-2 4-3 6h0c-1 1-1 2-2 3-1 5-4 8-8 10l-3 2c-2 1-5 2-8 4l-2-1h-3 0c3-2 6-2 9-4 0-1 0-1 1-1 1-1 3-2 4-3 0-3 2-5 4-7v1l1-1c3-3 4-9 4-14l1-9z" class="Q"></path><path d="M330 613c2 0 2 0 3 1l-1 1v3 2c-2 1-2 1-3 2l1-9z" class="E"></path><path d="M334 615c2 2 3 4 3 7v1 1s-2 2-2 3c0 2-2 4-3 6h0l-1-1c-1 1-1 2-2 3-2 3-5 6-7 8-2 1-3 2-6 3 1-1 3-2 4-3l3-3c2-2 4-4 6-7l3-6c0-2 1-4 2-5s0-5 0-7z" class="B"></path><path d="M316 646c3-1 4-2 6-3 2-2 5-5 7-8 1-1 1-2 2-3l1 1c-1 1-1 2-2 3-1 5-4 8-8 10l-3 2c-2 1-5 2-8 4l-2-1h-3 0c3-2 6-2 9-4 0-1 0-1 1-1z" class="d"></path><path d="M319 648c2-4 8-9 11-12-1 5-4 8-8 10l-3 2z" class="F"></path><defs><linearGradient id="G" x1="811.543" y1="143.836" x2="797.957" y2="165.164" xlink:href="#B"><stop offset="0" stop-color="#e3dfe1"></stop><stop offset="1" stop-color="#fcfbf3"></stop></linearGradient></defs><path fill="url(#G)" d="M785 154c2 0 5 0 8-1 9-1 19-2 28-1l1 1c4 0 11 1 13 4-2 0-5-1-7-1-2-1-4 0-5 0-2 0-3 0-5 1-1 0-1 0-2-1h-8l-2 1h-1c1 1 2 0 3 0 2 0 7-1 8 0v1c-2 2-6 0-8 2l-1-1h-3c-1 0-1 0-2 1h-3c-2-1-4-1-6-1h-2c-2-1-3 0-5-3h-1 0c-1 0-1 0-2-1h0l2-1z"></path><path d="M316 706h1l2 2v-1c1-1 1-1 2 0l2 2-2 1v1h1l-1 1 1 1h-2v1h3l2 2-1 1-1 1c-1-1-1-1-2-1s-3 1-4 1h0l-2-1v-1c-2-1-4-2-6-2-5-2-10-1-15-1-4 0-10-3-13-4l32-3h1 2z" class="U"></path><path d="M294 713c1-1 5 0 5-1 1 0 1-1 2-2l1 2 1-1h6l1 1-1 2c-5-2-10-1-15-1z" class="o"></path><path d="M316 706h1l2 2v-1c1-1 1-1 2 0l2 2-2 1v1h1l-1 1 1 1h-2v1h3l2 2-1 1-1 1c-1-1-1-1-2-1s-3 1-4 1h0l-2-1v-1c-2-1-4-2-6-2l1-2c1 1 0 0 1 0 1-1 0-1 1-2l1 1h2l1-1v-1h-2l-1-1h0v-2h1 2z" class="Z"></path><path d="M313 706h1 2c1 1 1 2 2 3-1 1-1 1-2 1l1 2v1l-1-1-2 1c-1 0-1 0-1-2h0 2l1-1v-1h-2l-1-1h0v-2z" class="b"></path><path d="M297 692c-1-1-1-1-1-2h2l1-1c1 1 2 1 3 1h1l5 2c2 0 3 1 5 1 1 0 2 0 2 1l6 3 6 3 6 3 1 1v1c-1 1-3 2-4 2-2 0-4 1-6 2h-1l-2-2c-1-1-1-1-2 0v1l-2-2h-1-2v-1l-3-2c1-2 2-2 4-3-2 0-3-2-5-2-1 0-3-1-4-2-1 0-2-1-3-2-1 0-1-1-3-1-1 0-1 0-1-1h0-2z" class="N"></path><path d="M315 700l5 3c1 0 2 1 4 2h0v-1c2 0 2 2 4 2l1-1 1 1v1c-2 0-4 1-6 2h-1l-2-2c-1-1-1-1-2 0v1l-2-2h-1-2v-1l-3-2c1-2 2-2 4-3z" class="U"></path><path d="M317 706l-1-1c1-1 2-1 3 0 1 0 2 1 3 2h-1c-1-1-1-1-2 0v1l-2-2z" class="b"></path><path d="M297 692c-1-1-1-1-1-2h2l1-1c1 1 2 1 3 1h1l5 2c2 0 3 1 5 1 1 0 2 0 2 1l6 3 6 3c-1 1-1 0-2 0h-2 0c2 1 4 2 5 4h0c-1 0-2-1-3-1-2 1-4-3-7-3h0l-2-1c-2-1-3-3-6-3-1 0-1 0-1-1h0-1-1l-4-2v-1h-1c1 1 0 1 1 2h0c-1 0-1-1-3-1-1 0-1 0-1-1h0-2z" class="k"></path><path d="M312 654c2 1 3-1 5-1l1 1s2 1 3 0v1 1h3 4 2v2 4h0c1 1 1 2 1 3h-2v-1h-2c-1-1-1-1-2-1-2-1-3 0-4 0-2-1-3-1-4-1-2-1-3 0-5 0l-1-1-1 1c-2 0-4 1-5 0h-9v1h0v2h4l1 1h-7c-3 0-8 0-11-1-1-1-3-1-5-1 0 0-1-1-2-1l12-2c4-1 7-1 11-1h4c1 0 2-1 2-2h1c1-2 3-3 6-4z" class="D"></path><path d="M321 654v1 1h3 4 2v2h-1c-1 0-3 0-4 1h-2c-2 1-4 0-6-1v-1c0-1 0-2 1-3 0 0 2 1 3 0z" class="Z"></path><path d="M321 654v1 1h3c-2 0-5 1-7 1 0-1 0-2 1-3 0 0 2 1 3 0z" class="D"></path><path d="M312 654c2 1 3-1 5-1l1 1c-1 1-1 2-1 3v1 1l-1 1c-1 0-2 0-3-1v-1l-1 1c-3 0-4 1-6-1 1-2 3-3 6-4z" class="B"></path><path d="M425 827c0 1 1 2 1 3s-1 1-1 2c-1 1 0 2 0 2l-3 1c-8 3-16 4-25 4-1 0-4 0-6-1h0-1l-2-2c0-1-1-3-1-4h1v-1c0-1 0-1 1-2 1 0 2 1 3 2h3c3 0 7 1 10 0h0c7 0 13-3 20-4z" class="J"></path><path d="M425 827c0 1 1 2 1 3s-1 1-1 2c-1 1 0 2 0 2l-3 1c-1-1-1-1-3-1l-1 1c-1 0-2 0-3-1l6-2c2-1 3-2 3-3-2-1-4 0-6 1-5 1-9 2-14 2-2 1-4 0-6 0s-4-1-5 0l1 1-2 1-4-2v-1c0-1 0-1 1-2 1 0 2 1 3 2h3c3 0 7 1 10 0h0c7 0 13-3 20-4z" class="V"></path><path d="M388 836c0-1-1-3-1-4h1l4 2c3 1 7 0 9 2v1c4 0 9-2 14-3 1 1 2 1 3 1l1-1c2 0 2 0 3 1-8 3-16 4-25 4-1 0-4 0-6-1h0-1l-2-2z" class="D"></path><path d="M388 836c0-1-1-3-1-4h1l4 2c3 1 7 0 9 2v1h-1c-3 2-6 0-9 1h-1l-2-2z" class="d"></path><path d="M388 836c4 0 9 0 12 1-3 2-6 0-9 1h-1l-2-2z" class="J"></path><defs><linearGradient id="H" x1="743.345" y1="154.819" x2="756.155" y2="173.181" xlink:href="#B"><stop offset="0" stop-color="#dedbde"></stop><stop offset="1" stop-color="#f5f4ea"></stop></linearGradient></defs><path fill="url(#H)" d="M755 160c3-1 6-1 9-2 6-2 14-3 21-4l-2 1h0c1 1 1 1 2 1h0 1c-1 1-2 1-3 0l-3 1c-1 1-2 1-3 2h-2l-2 1h-2-1c-1 1-3 0-4 1-1 0-1 0-2 1h-1-3l-1 1h-2c-1 0-1 0-2 1h-2c-1 1-1 1-2 1-2 1-3 1-5 2h-1c-1 1-2 1-3 2h-2l-1 1h-3l-5 2c-1 1-2 1-3 1-1 1-3 2-4 1-1 0-1 0-2 1h-1c-2-1-3-1-4-3v-1l3-1 4-1h1c1-1 3-1 4-2l4-1 9-3 3-1c3-1 7-3 10-2z"></path><path d="M634 827l2-1h4c-2 1-3 2-3 4l-3 5c-1 1-1 1-1 2h-4l-1-1h-1c-4 2-8 0-12 2h-1-1c-4 0-9-1-13-3-2 0-5-1-6-3l1-1v-2s1-1 1-2l1 1v-1c4 0 10 2 14 3 2 1 4 1 6 1h1 1c5 1 9 0 13-2v-1c1 0 1-1 2-1z" class="W"></path><path d="M632 829v-1l2 1 1 1h0l-2 1c-2 3-5 4-9 4h-1l5-2h0c0-1 0-1 1-2s2-1 3-2z" class="J"></path><path d="M597 827c4 0 10 2 14 3 2 1 4 1 6 1h1 1c5 1 9 0 13-2-1 1-2 1-3 2s-1 1-1 2h0l-5 2h-2-1-3v1h-3-1c-2-2-7-2-10-3-2-1-4-3-6-2h-1-1v-2s1-1 1-2l1 1v-1z" class="G"></path><path d="M617 836c-1-1-1-1-2-1h-1v-1c2 0 3-1 5-1l2 2h-1-3v1z" class="Y"></path><path d="M629 831c-1 1-1 1-1 2h0l-5 2h-2l-2-2c3-1 7-1 10-2z" class="Q"></path><path d="M746 238c3 1 8 3 11 5 4 3 9 6 13 9 2 2 5 4 7 6l-1 1h0c-1-1-2-1-3-1-4-1-7-3-11-5-2-1-4-1-5-2-1 0-5-1-6-2h-1c-3-1-7-1-9-2-2 0-3 0-5-1h0c-1-1-3-2-4-3h2v-1h-1c1-1 2-1 2 0l1-1h2c1 0 2 1 4 1l-1-1c-1-1-2-1-3-2h0 1c3-1 5-1 7-1z" class="J"></path><path d="M746 238c3 1 8 3 11 5 4 3 9 6 13 9-2 0-3-1-4-2-2-1-5-2-7-4h0l-2-1c-1 0-1 0-2-1l-3-1-4-1c-1-1-2-1-3-1 0 0-1 0-2-1h-1l-1 1c-1-1-2-1-3-2h0 1c3-1 5-1 7-1z" class="E"></path><path d="M736 246c-1-1-3-2-4-3h2v-1h-1c1-1 2-1 2 0l1-1 6 3c3 1 7 2 10 3 0 1 0 1-1 1 1 1 2 1 3 1l1 1h1l1 1c-1 0-5-1-6-2h-1c-3-1-7-1-9-2-2 0-3 0-5-1h0z" class="F"></path><path d="M637 176c0-1 1-2 1-3l1-1c1 1 1 3 1 4v1h-1c-1 1-1 2-1 3-5 6-12 11-18 17-1 1-2 1-3 2s-2 3-2 5h-1v-3l-1-1 2-3c-1-1-1 0-1-1l-1-1h0l-1 1v-1l-2-1 2-3c0-1 1-2 1-3 0 0 1-1 1-2v-1h-1c1-2 3-2 4-3s4-2 5-2h1c4-1 11 0 14-4z" class="d"></path><path d="M614 191l3 1-2 2c-1 0-1 1-2 1h0l-1 1v-1l-2-1 2-3h2z" class="E"></path><path d="M617 182c1-1 4-2 5-2h1c4-1 11 0 14-4 0 2 0 4-2 5l-10 6h-1-1l-1 1c-1 1-3 2-5 4l-3-1h-2c0-1 1-2 1-3 0 0 1-1 1-2v-1h-1c1-2 3-2 4-3z" class="J"></path><path d="M617 182c0 1-1 1-1 2 2 1 5-2 8-2-2 2-6 3-8 5-1 1-2 3-2 4h-2c0-1 1-2 1-3 0 0 1-1 1-2v-1h-1c1-2 3-2 4-3z" class="B"></path><defs><linearGradient id="I" x1="464.571" y1="903.315" x2="468.216" y2="913.584" xlink:href="#B"><stop offset="0" stop-color="#a19f9d"></stop><stop offset="1" stop-color="#c6c5c6"></stop></linearGradient></defs><path fill="url(#I)" d="M475 889v5c1-1 1-2 2-4v-2h0c0 2 1 5 0 7v1c0 4-4 9-6 12l-4 4-5 5c1 1 2 1 3 2 1 0 1 0 2 1-8 1-14 2-21 4h-2c-2 0-6 1-7-1l-1 1c-3 1-8 0-10-1-3-1-5-3-7-5h0c2 1 4 2 5 3h1c3 2 10 2 14 1h1c4-1 8-3 12-5 1-1 3-2 4-3 3-1 6-4 8-7 1-2 1-5 3-5l1-1h0c1-1 2-2 2-4 1-1 2-1 3-2 1-2 1-4 2-6z"></path><path d="M446 924l-2-1c7-1 12-3 18-6 1 1 2 1 3 2 1 0 1 0 2 1-8 1-14 2-21 4z" class="N"></path><path d="M468 901h0c1-1 2-2 2-4 1-1 2-1 3-2-1 8-8 17-14 21h-1c2-2 4-4 6-7 1-2 2-5 3-7l1-1z" class="j"></path><path d="M456 914c3-1 6-4 8-7 1-2 1-5 3-5-1 2-2 5-3 7-2 3-4 5-6 7h1c-5 4-12 6-18 6h-1c4-1 8-3 12-5 1-1 3-2 4-3z" class="C"></path><path d="M655 736h4c0-1 1-1 2-1-2 2-4 3-5 5s-2 4-2 6c-2 7-4 12-9 18-1-1-1-2-1-3l-1-1h-1l1-1c1-1 1-2 2-4h0l-3-1s0-1-1-1l-1 1c-1 0-3-1-4-2h-2 0-2l-1 1-2-1 5-5c4-3 8-5 12-7h1c1-2 6-3 8-4z" class="N"></path><path d="M649 754c-1 3-2 4-4 6l-1 1-1-1h-1l1-1c1-1 1-2 2-4h0l4-1z" class="b"></path><path d="M647 740v1h-1v1h1 0c2-1 4-1 5-2l3-2v1c0 1-2 2-3 3h-2l-1 2h-1v-1l-1 1h0c-1 1-2 1-3 1h-1-1v-2c-1 1-1 2-2 2-1 4-4 5-6 7h0-2l-1 1-2-1 5-5c4-3 8-5 12-7h1z" class="L"></path><path d="M640 745c1 0 1-1 2-2v2h1 1c1 0 2 0 3-1l1 1c0 2 1 2 0 4l1 2v1 1 1l-4 1-3-1s0-1-1-1l-1 1c-1 0-3-1-4-2h-2c2-2 5-3 6-7z" class="S"></path><path d="M634 752c2-2 5-3 6-7 1 2 2 4 2 6-1 0-2 1-3 0l-1 1h-2-2z" class="N"></path><path d="M729 273l-2-2 1-1 1 1h3 1c2 0 4 0 6 1s4 2 6 2l1 1c3 1 7 2 10 2v1 1c-2 2-4 3-6 3-6 2-13 4-19 3h-5 0-1c-1-2-3-3-5-4h2v-2h5v-1l-1-1 3-2h-1l1-1 1 1 1-1-2-1z" class="G"></path><path d="M733 271c2 0 4 0 6 1s4 2 6 2l-2 1c-3 0-7-2-10-4z" class="V"></path><path d="M729 275c0 1 1 2 1 3v1c1 0 3 1 4 2h1c4 2 9 0 13 0l-1 1h0 3c-6 2-13 4-19 3h-5 0-1c-1-2-3-3-5-4h2v-2h5v-1l-1-1 3-2z" class="D"></path><path d="M421 226l4-6h0c1 0 1 0 0 1s-2 3-3 5v3l-1 1v1h1l3-4c0 2 0 2-1 3v1c3-2 5-4 8-5l2-1c-2 2-5 4-8 7-2 1-3 3-3 6 0 1-1 2-1 3h-1c-2 2-2 6-5 8-1-1-2-1-3-1-2-1-3-1-5-2v-1c-2-1-3-3-4-5h3l1 1v-3-1-3h-1c-1-1-1-3-1-4l1-1c0-1 0-1 1-2v-1h0v4c1-2 2-3 3-5h0v1 1 1 1c0-1 0-1 1-1l1 1v-1l1 1h0 1l1-1 2-2c0 1-1 1-1 2 1-1 1-1 2-1l1-1h1z" class="W"></path><path d="M408 238v-3h1v4h1v-4h1v3c1 1 1 2 1 3h1l-2 1v-1c-1 1-1 2-2 3l-1 1c-2-1-3-3-4-5h3l1 1v-3z" class="D"></path><path d="M421 226l4-6h0c1 0 1 0 0 1s-2 3-3 5v3l-1 1v1h1l3-4c0 2 0 2-1 3v1 1l-1 2c-1-1 0-1-1-1h-1c0 2-2 3-2 5 0 1-1 2-1 3l-1-1-2 3-2-2h-1c0-1 0-2-1-3 1-1 1-3 2-4v2l1 1v-1c1-1 2-3 3-4h1c1-1 1-2 2-4 1-1 1-2 1-2z" class="G"></path><path d="M412 241l1-2 2 1 2-2c0-1 0 0 1-1l1 1c0 1-1 2-1 3l-1-1-2 3-2-2h-1z" class="H"></path><path d="M432 226l2-1c-2 2-5 4-8 7-2 1-3 3-3 6 0 1-1 2-1 3h-1c-2 2-2 6-5 8-1-1-2-1-3-1-2-1-3-1-5-2v-1l1-1c1-1 1-2 2-3v1l2-1 2 2 2-3 1 1c0-1 1-2 1-3 0-2 2-3 2-5h1c1 0 0 0 1 1l1-2v-1c3-2 5-4 8-5z" class="X"></path><path d="M408 245l1-1c1-1 1-2 2-3v1l2-1 2 2-1 2c-1-1-1 0-1-1l-1 1c-1 0-1 1-2 1h-2v-1z" class="C"></path><path d="M266 383c2 0 4 0 6 1 5 1 7 2 10 5v2l4 8c1 3 4 7 6 10l4 4v1c1 2 4 3 4 5h-6c-2-1-4-3-6-4v-1l2-1-1-2c-2-1-6-7-7-9h0l-1-2c-1 0-1-1-2-2l-3-4c-1-1-2-2-4-3v-1l-1-1h-1c-1 0-2 0-3-1h0c-3-1-7-1-10-1-1 1 0 1-2 1-1 0-3 1-4 1h-2l3-3c1 0 2-1 2-1 1 0 2-1 3-1h2c2-1 4-1 7-1z" class="Q"></path><path d="M272 384c5 1 7 2 10 5v2l4 8c1 3 4 7 6 10l4 4v1c1 2 4 3 4 5h-6c-2-1-4-3-6-4v-1l2-1-1-2c-2-1-6-7-7-9 1 0 2 1 3 3l3 3v-1c-2-3-3-6-4-8-2-2-2-4-4-6-1-1 0-2-1-4-2-1-5-2-6-4l-1-1z" class="I"></path><path d="M289 411h1c2 2 6 5 8 7-1 0-2 1-4 1-2-1-4-3-6-4v-1l2-1-1-2z" class="O"></path><path d="M710 682l1 1c1 0 2 0 4 1 2-1 2-2 4-2l-1 2h2c2-1 4 0 6-1h4 0v1l1 2c2 0 4 0 6-1v1 1h1l2-1h2c0 1 1 2 1 3l-1 1v1c0 1-1 1-2 1v2c-4 1-8 2-12 2l2-2h-2v-1-1h1l2-2c-1 0-1 0-2-1l-1 1-2 1h-1v-1c-1 1-2 2-3 2-3 0-3-1-4-1s-2 2-3 2h-4c0-1 1-2 2-2h0v-1c-2 0-5 1-6 2-2 0-2-1-3 0h0-1v-2c0-2 0-2 1-4v-1l-1-1h1l2-2c1 1 1 1 3 1 1 0 1 0 1-1z" class="P"></path><path d="M726 683h4 0v1c-1 0-2 0-3 1l1 1v1c-1 1-1 1-2 1v-1s-1-1-2-1c1-1 1-2 2-3z" class="b"></path><path d="M730 694c0-1 0-1 1-2v2h1s1-1 2-1h0c2-1 4-1 6-1v2c-4 1-8 2-12 2l2-2z" class="j"></path><path d="M710 682l1 1c1 0 2 0 4 1 2-1 2-2 4-2l-1 2h2c2-1 4 0 6-1-1 1-1 2-2 3s-2 1-3 1-1-1-2-1l-1-1-1 1 2 2c-2 1-2 1-4 1h-1c1-1 1-1 1-2h-2l-1-1v1c-2 1-4 1-6 2l-3 1c0-2 0-2 1-4v-1l-1-1h1l2-2c1 1 1 1 3 1 1 0 1 0 1-1z" class="K"></path><path d="M455 103v-3-1l4 4 2 2h1c1 0 1 0 2 1h0l3 6c1 2 2 5 2 8v6l-1 2-1 3v3c-2 1-3 2-5 3-1 1-3 2-5 4l-4 3c-1-1-1-2-1-2v-2c1-1 2-1 2-2l1-3c0-1 0-1 1-2h1 2v-2l1-1v-8c0-2-1-5-1-7v-2-1s-1-1-1-2v-1l-1-1-2-5z" class="Y"></path><path d="M460 122h0c1 3 1 7 1 10 2 0 5-3 7-4l-1 3c-1 1-2 3-3 3-2 0-3 1-5 2l-2 2c0-1-1-2-1-3h-1c0-1 0-1 1-2h1 2v-2l1-1v-8z" class="E"></path><path d="M467 131v3c-2 1-3 2-5 3-1 1-3 2-5 4l-4 3c-1-1-1-2-1-2v-2c1-1 2-1 2-2l1-3h1c0 1 1 2 1 3l2-2c2-1 3-2 5-2 1 0 2-2 3-3z" class="G"></path><path d="M408 258v-3c1-1 1-1 1-2l-3-3v-1h-1l-1 1h0l-1-1v-1l-1-1c0-2 0-2-1-4l-1-2-1-2v-2h0v-3c-1-1-1-5-1-6l1-1c0-1 0-2 1-3h0c0-1 1-2 2-3h1c-1 1-1 2-1 3l1-1c0-1 1-1 2-2s1-2 2-4l1-1v-1-1l1-1h0c1-1 0-1 1-1-1 3-3 6-4 9v3c-1 1-1 2-1 4-1 0 0 1 0 3-1 0-1 3-1 4h1 1 0 1c1 1 0 1 1 2v1 3l-1-1h-3c1 2 2 4 4 5v1c2 1 3 1 5 2 1 0 2 0 3 1l1 1h-1 0l-1 1 1 1s1 0 1-1h3l2-2c1 0 1 0 2 1h1c1 1 1 1 2 0h0l-1-1c1-1 2-1 4-2v1l1 1v1h-3l-1 1c1 1 3 0 5 0l-1 1-2 1-4 1-6 1c-6 1-11 5-16 9 0-2 3-4 5-6z" class="F"></path><path d="M408 258l7-7-3-1h-1c-3-1-6-6-7-8s-2-5-1-7c1 2 1 3 1 5 1 2 2 4 4 5v1c2 1 3 1 5 2 1 0 2 0 3 1l1 1h-1 0l-1 1 1 1s1 0 1-1h3l2-2c1 0 1 0 2 1h1c1 1 1 1 2 0h0l-1-1c1-1 2-1 4-2v1l1 1v1h-3l-1 1c1 1 3 0 5 0l-1 1-2 1-4 1-6 1c-6 1-11 5-16 9 0-2 3-4 5-6z" class="K"></path><path d="M282 376h0l4 15c1 0 2 0 3 1s1 2 0 4l3 3h1 1l1 5h-1-2 0v1c1 1 2 1 2 3h-1l-1 1c-2-3-5-7-6-10l-4-8v-2c-3-3-5-4-10-5-2-1-4-1-6-1-3 0-5 0-7 1h-2c-1 0-2 1-3 1 0 0-1 1-2 1 0-1 0-2 1-3 2-2 7-5 10-6l3-1c4-1 10-1 14 0 1 1 1 1 2 0z" class="R"></path><path d="M286 391c1 0 2 0 3 1s1 2 0 4c0 1 0 2 1 3l-1 1c-2-2-3-7-3-9z" class="G"></path><path d="M289 400l1-1c-1-1-1-2-1-3l3 3h1 1l1 5h-1-2 0v1c1 1 2 1 2 3h-1c-2-2-3-6-4-8z" class="D"></path><path d="M263 377l3-1c4-1 10-1 14 0l-1 1c2 2 2 2 3 4l-2 2h-1l-2-1v2-1h-7c1-1 1-2 2-3h2 0l-1-1c-4-3-6-1-10-2h0z" class="F"></path><path d="M252 386c0-1 0-2 1-3 2-2 7-5 10-6h0c4 1 6-1 10 2l1 1h0-2c-1 1-1 2-2 3h-4 0c-3 0-5 0-7 1h-2c-1 0-2 1-3 1 0 0-1 1-2 1z" class="d"></path><path d="M257 384l2-1c1-1 2-1 3-1h3 2l-1-2h6c-1 1-1 2-2 3h-4 0c-3 0-5 0-7 1h-2z" class="E"></path><path d="M331 610c1-1 1-1 1-2l2 1 6 9 2 2 7 10-3 1c0 1 0 2 1 3h1v1h1v1c-1 0-1 1-2 1l-1-1v-2l-1 1c0 1 0 2-1 2-1-1-2-2-2-3l-1-1c-1 1-2 2-2 4-1 1-2 2-2 4h-1c-1 2-2 4-2 6v2 2h-4-3l1-1v-4h0l-1-1 1-2c-3 0-3 5-6 3 4-2 7-5 8-10 1-1 1-2 2-3h0c1-2 3-4 3-6 0-1 2-3 2-3v-1-1c0-3-1-5-3-7l-1-1c-1-1-1-1-3-1 1-2 1-2 1-3z" class="C"></path><path d="M342 634l3-3v1 1h1v1l-1 1c0 1 0 2-1 2-1-1-2-2-2-3z" class="B"></path><path d="M331 642c1-1 2-3 3-5l3-3c1-3 3-4 4-6l1 1-1 2 1 1-1 1c-1 1-2 2-2 4-1 1-2 2-2 4h-1c-3 0-3 1-4 3l-1 1v-3z" class="F"></path><path d="M331 645l1-1c1-2 1-3 4-3-1 2-2 4-2 6v2 2h-4-3l1-1v-4h0c1-2 2-3 3-4v3z" class="E"></path><path d="M328 646c1-2 2-3 3-4v3c-1 1-1 3-1 4v2h-3l1-1v-4h0z" class="D"></path><path d="M331 610c1-1 1-1 1-2l2 1 6 9 2 2 7 10-3 1v-1h-1c-1 0-2-1-2-1 0-2 0-3-1-4s-1 0-2-1h0-1c-1 2-2 5-4 7 0-2 1-2 0-4 0-1 2-3 2-3v-1-1c0-3-1-5-3-7l-1-1c-1-1-1-1-3-1 1-2 1-2 1-3z" class="R"></path><path d="M331 610c3 3 6 7 8 10l1 1v3h-1c-1 2-2 5-4 7 0-2 1-2 0-4 0-1 2-3 2-3v-1-1c0-3-1-5-3-7l-1-1c-1-1-1-1-3-1 1-2 1-2 1-3z" class="V"></path><path d="M478 166v-4h1v-1c1-2 2-3 3-4l1-2v-1l4-7v-1c1-1 1 0 1-1s0-2 1-3l1-1v-1l4-9c1-1 1-1 1-2l1-1v-1c0-1 0-2 1-2v-1c0-1 1-2 2-3-1 7-6 13-8 20v4c0 2-1 4 0 7v2c0 1-1 2-1 3s0 1 1 2v-2l1-4c1-3 3-4 3-7l1 3v4c1 2 0 6 1 8v1c1 1 2 0 3 0h4 0v2c-2 1-6 1-8 3-2 1-3 3-4 5l-1 3v-9h0c0 3 0 5-1 8-1-3 0-5 0-8l-3 1v1 1l-1 1h0-1v-2l-1 1v1h-1v-1c0-1 0-2 1-3v-3h-1s-1 1-1 2v2c-1 0-1-3-1-4-1 2-2 3-2 5 0 1-2 3-2 5h-1l2-7z" class="Q"></path><path d="M478 166l6-12 2-4v1c1 0 1 0 0 1v1c0 1 0 0-1 1 0 1-2 5-2 6 1 0 2-1 2-2 0-2 1-3 2-4 1-2 1-3 2-5 1 1 0 2 0 3-1 1-1 2-1 3h1v2c-1 1 0 1-1 2 0 1 0 1-1 2l1 1c0 1 1 2 0 3 0 1-1 1-1 2v1 1l-1 1h0-1v-2l-1 1v1h-1v-1c0-1 0-2 1-3v-3h-1s-1 1-1 2v2c-1 0-1-3-1-4-1 2-2 3-2 5 0 1-2 3-2 5h-1l2-7z" class="F"></path><path d="M442 228l5-1c2 0 4-1 6-1 4-2 8-4 13-5-3 2-7 5-10 6v1 1l1 1 3-1h0c-1 1-1 1-1 2 0 0 1 0 1 1-1 1-3 1-4 1h-3l-8 1-6 1c-5 1-8 3-12 7h-1c-2 2-3 5-4 7l-2 2h-3c0 1-1 1-1 1l-1-1 1-1h0 1l-1-1c3-2 3-6 5-8h1c0-1 1-2 1-3 0-3 1-5 3-6 3-3 6-5 8-7h2l-1 1 4-1v1l-4 3c1 0 2 0 3-1h0 1l1 1 1-1h1z" class="W"></path><path d="M426 232l1-1c1 0 2 0 3-1h0c-1 1-2 3-4 3v2 1c-1 1-1 0-1 1 1 0 2-1 2-2h1c0-1 1-2 3-2 1-1 2-1 3-1-1 2-3 4-5 4-1 3-3 4-5 5 0-1 0-1 1-2h-1l-3 3v-1h1c0-1 1-2 1-3 0-3 1-5 3-6z" class="V"></path><path d="M435 232c1-1 6-3 8-3s3 0 5-1c1 0 2 0 3 1h-1v1h0c2 0 4 0 6-1l1 1 3-1h0c-1 1-1 1-1 2 0 0 1 0 1 1-1 1-3 1-4 1l-4-1c-2 0-4-1-6 0h-3l-1-1c-1 1-3 2-4 2h-2l-1 1v-1-1z" class="H"></path><path d="M456 229l1 1 3-1h0c-1 1-1 1-1 2 0 0 1 0 1 1-1 1-3 1-4 1l-4-1c-1-1-4-1-6-1l1-1c1 0 2-1 3-1v1h0c2 0 4 0 6-1z" class="D"></path><path d="M435 232v1 1l1-1h2c1 0 3-1 4-2l1 1h3c2-1 4 0 6 0l4 1h-3l-8 1-6 1c-5 1-8 3-12 7h-1c-2 2-3 5-4 7l-2 2h-3c0 1-1 1-1 1l-1-1 1-1h0 1l-1-1c3-2 3-6 5-8v1l3-3h1c-1 1-1 1-1 2 2-1 4-2 5-5 2 0 4-2 5-4h1z" class="e"></path><path d="M416 249c3-2 3-6 5-8v1 2h0l1-2h1l1 1h-1c-1 2-1 3-2 5l1 1-2 2h-3c0 1-1 1-1 1l-1-1 1-1h0 1l-1-1z" class="b"></path><path d="M726 255c1 0 3 1 4 1 1 1 2 2 2 3-1 0-2 0-3 1 0 1 1 2 2 3v2h0c-2 0-3-1-4-2h-3v2c1 0 1 1 2 2h-1l-1-1 2 4-2-1c0 1 0 2 1 3s1 1 3 1h0 1l2 1-1 1-1-1-1 1h1l-3 2 1 1v1h-5v2h-2c2 1 4 2 5 4-2 0-2 0-4-1-1-2-4-2-6-4v1l2 2h0-2c-1 1-1 3-1 5 0 4 0 8 1 12h2v1h0-3l1 1c0 1-1 1-1 2v3h0-1-1c0-1 0-1 1-2v-5-2c1-3 0-8 0-12v-26h2 3c2-1 4-1 6-2l2-3z" class="O"></path><path d="M716 268v-1l1-1 4 1 2-2h1v1l2 4-2-1c0 1 0 2 1 3l-3-2-1 1c-1-1-1 0-1-1l-1-1c-1-1-1-1-2-1h-1z" class="T"></path><path d="M716 268h1c1 0 1 0 2 1l1 1c0 1 0 0 1 1-1 2-1 2 0 3-2 1 1 0-2 1-1 0-1 1-1 2l-2-1v1c-1-1-1-1-2-1 1-2 1-2 2-3h1l-1-2 1-1h0l-1-2z" class="a"></path><path d="M726 255c1 0 3 1 4 1 1 1 2 2 2 3-1 0-2 0-3 1 0 1 1 2 2 3v2l-7-7 2-3z" class="d"></path><path d="M721 271l1-1 3 2c1 1 1 1 3 1h0 1l2 1-1 1-1-1-1 1h1l-3 2 1 1v1h-5v2h-2c-2-1-3-3-4-4v-1l2 1c0-1 0-2 1-2 3-1 0 0 2-1-1-1-1-1 0-3z" class="X"></path><path d="M714 307v-3c0-1 1-1 1-2l-1-1h3 0v-1h-2c-1-4-1-8-1-12 0-2 0-4 1-5h2 0l-2-2v-1c2 2 5 2 6 4 2 1 2 1 4 1h1c6 5 11 6 18 8 0 1-1 1-2 1-3 2-7 3-11 4l1 3h0c-3 0-6 2-9 2-2 1-4 1-6 1l-1 1-2 2z" class="M"></path><path d="M717 293l1 1 2 1s0-1 1-2h2c1 0 1 1 2 1l5 4h-5c1 1 2 1 3 1v1c0 1-2 1-3 1s-1 0-2 1c-1 0-1 0-2-1-2 0-4-3-5-5l1-1-1-1 1-1z" class="B"></path><path d="M717 293h1l-1-1v-2l-1-1c2-1 2 0 3 1 0-2-2-2-1-3 1 0 2 1 3 1h0l-2-2 1-1c1 1 2 2 3 2l1-1c3 1 7 4 10 5s6 1 8 3c-3 2-7 3-11 4v-1h-1c-2-1-3-2-5-3-1 0-1-1-2-1h-2c-1 1-1 2-1 2l-2-1-1-1z" class="I"></path><path d="M733 330c4-2 9-3 13-4 3 0 4 0 6 1h0l-5 3c-2 2-5 4-6 7l-4 5-2 2c0 1 0 1-1 2-2 1-4 3-5 5-1 1-2 2-3 2v2c0 1-2 3-3 3v-1-3-1l-1-1c0-1 0-2 1-4h0l-1-1-3 3v5h0c0-4-1-9 0-12s3-5 5-6l5-4c1-1 2-2 4-2v-1z" class="Y"></path><path d="M729 333c1-1 2-2 4-2-5 4-10 8-10 14 1-1 1 0 1-1s1-1 1-1v4h0l1 1-1 2h0c1 1 0 1 0 2l-1 1 1 2c-1 0-1 1-2 2v-3-1l-1-1c0-1 0-2 1-4h0l-1-1-3 3v5h0c0-4-1-9 0-12s3-5 5-6l5-4z" class="O"></path><defs><linearGradient id="J" x1="727.512" y1="341.69" x2="743.942" y2="338.101" xlink:href="#B"><stop offset="0" stop-color="#bab9b5"></stop><stop offset="1" stop-color="#e1dedf"></stop></linearGradient></defs><path fill="url(#J)" d="M725 343c2-1 3-3 4-4l1 1c-1 0-1 1-1 2h0c2-1 2-1 3-2l4-5v1l1-1c0-1 0-1 1-2h1l3-2c1 0 2-1 3-1h0 2c-2 2-5 4-6 7l-4 5-2 2c0 1 0 1-1 2-2 1-4 3-5 5-1 1-2 2-3 2v2c0 1-2 3-3 3v-1c1-1 1-2 2-2l-1-2 1-1c0-1 1-1 0-2h0l1-2-1-1h0v-4z"></path><path d="M499 121h0l7-15 2-4 4-6c0-2-1-1 0-2l2-3 2-2 2-5 3-4c0-1 1-1 1-2h0l1-1c1-2 3-5 4-6l1-1c1-2 2-2 3-4v2 1h0v1l-13 21v1c-5 8-9 18-13 27-1 2-3 10-5 11v2c-1 3-2 5 0 8l-1 2c-1 1-2 1-3 1 0-1 0-2-1-3v-1 5 2c0 3-2 4-3 7l-1 4v2c-1-1-1-1-1-2s1-2 1-3v-2c-1-3 0-5 0-7v-4c2-7 7-13 8-20z" class="E"></path><path d="M495 139c0-1 0-2 1-3 1-2 1-5 4-6v2c-1 3-2 5 0 8l-1 2c-1 1-2 1-3 1 0-1 0-2-1-3v-1z" class="c"></path><path d="M480 906l6 2c-1 1-2 1-2 2v1l-1 1v2h0-3-1c7 3 14 7 18 12l3 3h1l-1 1-2 1h2l1 1c-2 0-2 1-4 2l-1 2c-6-7-11-11-20-14 0 0-2-1-3-1-2 0-4-1-6-1-1-1-1-1-2-1-1-1-2-1-3-2l5-5 4-4c0 1 0 1 1 2l4-2h2l2-2h0z" class="O"></path><path d="M484 917l1 1v1c-1 1-2 2-3 2s-1 0-1-1l3-3z" class="f"></path><path d="M476 922c3 0 6-1 7 1 1 1 2 1 3 1s3 2 4 3c0 0 0 1 1 2 2 1 6 1 8 1l1-1h1l-1 1-2 1h2l1 1c-2 0-2 1-4 2l-1 2c-6-7-11-11-20-14z" class="P"></path><path d="M480 906l6 2c-1 1-2 1-2 2v1l-1 1v2h0-3-1l-5-1h-3c-1 0-1 0-2 1l-2-2 4-4c0 1 0 1 1 2l4-2h2l2-2h0z" class="N"></path><path d="M474 913c1-1 2-2 4-3l2 1 1-2 2 2 1-1v1l-1 1v2h0-3-1l-5-1z" class="M"></path><path d="M470 275h0-5c-1-1-3-1-3-1-2-1-7-1-9-1-1 0-2 1-3 0h0c3-1 6-1 9-1 4 0 8-1 11 0s6 1 9 2c1-1 2-2 3-2 3 1 7 1 10 2l7 3c1 0 1 0 2 1h1l2 1h0c3 1 5 3 7 4 1 1 3 2 4 3h0c1 0 2 0 3 1h2c1 1 2 1 3 2v1l7 3c-1 1-4 0-6 0h-6l-8-4c-3-1-5-2-8-4 0 0-2-2-3-2 0 1 2 2 2 4l-7-3h0c-3-2-11-5-15-5-2 0-3 0-5-1h3c1 1 3 0 4 0v-1h-2l-9-2z" class="E"></path><path d="M470 275h0-5c-1-1-3-1-3-1-2-1-7-1-9-1-1 0-2 1-3 0h0c3-1 6-1 9-1 7 0 15 1 22 3l12 3-1 1c-3-1-5-1-7-2l-10-2h-5z" class="Y"></path><path d="M479 274c1-1 2-2 3-2 3 1 7 1 10 2l7 3c1 0 1 0 2 1h1l2 1h0c3 1 5 3 7 4 1 1 3 2 4 3h0c1 1 1 1 2 1l1 1c-3 0-6-3-9-3-4 0-5-3-8-5-2-1-4-2-6-2l-9-3-7-1z" class="G"></path><path d="M560 866h1v2h2 0c1 2 2 3 3 4v8 1l-2-3c-3 0-4 1-6 3-3 3-4 6-5 11-2 1-3 2-5 3l-4-4-1-1-1-2c0-3 0-6 2-8l1-3h-3l1-2 1-1c1-2 2-3 3-4l2-3v1 2-1h1 1c3-2 5-3 9-3z" class="Z"></path><path d="M542 888c0-3 0-6 2-8 1 1 4 3 5 4-1 1-1 2-2 3h0v-2c-1 0-1 1-1 2v1l-3 2-1-2z" class="N"></path><path d="M560 866h1v2h0c0 2 1 2 1 4v1c-1 1-1 1-2 1l-1-1v1c-1 1-5 1-7 2v1c3 0-1 0 2 0h0l-1 1c-1 0-2 0-3-1 0 1 0 2 1 3l-2 1v1 2c-1-1-4-3-5-4l1-3h-3l1-2 1-1c1-2 2-3 3-4l2-3v1 2-1h1 1c3-2 5-3 9-3z" class="S"></path><path d="M547 870l2-3v1 2-1h1 1c-2 3-4 5-6 8h0-3l1-2 1-1c1-2 2-3 3-4z" class="f"></path><path d="M560 866h1v2h0c0 2 1 2 1 4v1c-1 1-1 1-2 1l-1-1v1c-1 1-5 1-7 2v1c3 0-1 0 2 0h0l-1 1c-1 0-2 0-3-1v-1c-1 1-1 1-2 1l-1-1h2 2 0v-2l4-4h-1 4v-2h1l1-2z" class="n"></path><path d="M755 677c3 0 7 1 10-1 2 0 4-1 6-2h1l-2 1h3 1v3 1c-2 0-2 0-3 1l2 1-9 6-11 4c-1 0-2-1-3-1h-4-1c-1 0-2 1-3 1v-1l1-1c0-1-1-2-1-3h-2l-2 1h-1v-1-1c-2 1-4 1-6 1l-1-2v-1h4c1-1 3-1 5-1l1 1v-1-1c-1 0-2 0-3-1 1 0 0 0 1-1l1-1-1-1 7-2 1 2c2-1 3-1 4 0 1 0 1 0 2 1l3-1z" class="T"></path><path d="M744 680h1c1 0 2-1 3-1 2 1 2 1 4 1 1 0 1 1 3 1v-1c1 0 1 0 1 1-1 1-6 1-8 0h0c-1 0-2 0-3-1h-1z" class="O"></path><path d="M746 677c2-1 3-1 4 0 1 0 1 0 2 1v1h-4c-1 0-2 1-3 1h-1 0c-1-2 0-2 0-3h2z" class="f"></path><path d="M765 684l-1 3-11 4c-1 0-2-1-3-1 2 0 2-1 3-2h4v-1c1 0 2 0 3-1h3l2-2z" class="U"></path><path d="M765 684l1-3c1-1 2-2 3-2l2 1 2 1-9 6 1-3z" class="Z"></path><path d="M739 678l1 1h0c1 1 2 2 4 2h0l1 1h2 1c1 1 2 1 3 1h1c-1 1-2 1-3 0-2 1-2 0-3 1v2h-1c-1 0-1-1-2 0h-1-2l-2 1h-1v-1-1c-2 1-4 1-6 1l-1-2v-1h4c1-1 3-1 5-1l1 1v-1-1c-1 0-2 0-3-1 1 0 0 0 1-1l1-1z" class="O"></path><defs><linearGradient id="K" x1="526.77" y1="273.873" x2="512.524" y2="283.77" xlink:href="#B"><stop offset="0" stop-color="#d0ccc5"></stop><stop offset="1" stop-color="#eeefe7"></stop></linearGradient></defs><path fill="url(#K)" d="M520 287c1 0 1 1 2 1s1 0 2 1h2 1 1c1 1 1 1 2 1h0-1c-1-1-1-1-2-1h-1c0-1-1-1-1-1-1-1-1 0-2-1h0c-2-1-2-1-3-1h-1c-1-2-4-3-5-5-1 0-2-1-3-1v-1l-2-1-2-1c-1 0-1 0-2-1-1 0-1 0-1-1v-1c1 0 2 0 3 1-1-1-1-1-2-1h-1c-1-1-1-2-2-3l-4-2h0l-1-1c-2 0-2 0-3-1v-1h1c1 0 1 0 2 1h1v-1c2 1 3 1 4 2l4 1c8 3 14 9 22 13l9 3c2 0 4 1 6 1l2-1v1h-1l1 1c1-1 1-1 3-1 0 0 6-4 7-4l8-5 2 1c-2 1-4 2-5 3h0c-1 1-2 2-4 2v1c-4 2-8 5-12 7l-5 2-8 2-7-2c2 0 5 1 6 0l-7-3v-1c-1-1-2-1-3-2z"></path><path d="M521 285h0c-1-1-2-1-3-2h1c1 0 1 0 2 1l2-1 5 3v1c-3-1-5-2-7-2z" class="d"></path><path d="M537 285c2 0 4 1 6 1l1 1c-2 1-4 2-6 2-1-1-2-2-4-2h0v-1h3v-1z" class="F"></path><path d="M521 285c2 0 4 1 7 2 2 2 7 3 10 2h0c2 0 4-1 6-2l-1-1 2-1v1h-1l1 1c1-1 1-1 3-1-4 3-6 4-11 5v-1c-6 0-11-2-16-5z" class="D"></path><defs><linearGradient id="L" x1="539.11" y1="292.687" x2="531.89" y2="290.313" xlink:href="#B"><stop offset="0" stop-color="#888883"></stop><stop offset="1" stop-color="#a2a3a2"></stop></linearGradient></defs><path fill="url(#L)" d="M555 282l8-5 2 1c-2 1-4 2-5 3h0c-1 1-2 2-4 2v1c-4 2-8 5-12 7l-5 2-8 2-7-2c2 0 5 1 6 0l-7-3v-1c1 0 1 0 2 1h2c3 1 7 1 10 1 5-1 7-2 11-5 0 0 6-4 7-4z"></path><path d="M737 212h1c2 0 4-1 6-1v1l1 1c1 1 3 2 4 2l2 1h1l-1 1c2 1 11 4 11 6h1c0 1 2 1 3 2 0 1 0 1 1 1 0 1 1 1 1 2 1 0 2 1 3 1 4 3 10 5 15 7 1 0 2 0 3 1h2c4 1 9 0 13 1l1 1h0-5c-4 0-8 0-11-1l-2-1h-1-2c-3 0-7-1-10-3l-25-7-8-3c-2 0-4 0-5-1l-1-1c-1 0-3 0-4-1h0c1-1 2-1 3-2h1 0v-1h-1l2-2h-1-2l-1-1h-1l5-2 1-1z" class="I"></path><path d="M737 212h1c2 0 4-1 6-1v1l1 1c1 1 3 2 4 2l2 1h1l-1 1-9-3c-3-1-3 0-6-1l1-1z" class="g"></path><path d="M731 215l5-2c3 1 3 0 6 1l-3 1c-1 2-1 3-4 4v-1h-1l2-2h-1-2l-1-1h-1z" class="R"></path><path d="M745 222l-3-1s-1-1-2-1h-1c2-2 2-2 5-2h0c1 1 3 1 4 1l5 2h2c2 1 5 2 6 3 2 2 4 3 7 4 1 0 2 1 3 1 4 3 10 5 15 7 1 0 2 0 3 1h2c4 1 9 0 13 1l1 1h0-5c-4 0-8 0-11-1l-2-1h-1-2c-3 0-7-1-10-3h0c-1-2-5-2-7-3h-3l-19-7h-2v-1l6 1c2 1 3 2 5 2-1-1-3-2-4-2l-5-2z" class="J"></path><path d="M745 222l-3-1s-1-1-2-1h-1c2-2 2-2 5-2h0v2c2 0 3 1 4 1s2 0 2 1c-1 0-4-1-5 0z" class="B"></path><path d="M679 695c2 0 2 0 3 1 0 2 0 3 1 5h0c1 2 2 3 3 4l2 1c2 0 2 1 4 1h2c0 1 1 1 2 2l6 1h2 1c1-1 0-2-1-3h2v-3c1 0 2 0 2-1h1v1l-1 1v1l33 3c-4 2-9 4-13 4-6 1-13-1-18 1l-2 1c-2 0-3 0-5 1l-2-2c-2 0-4 0-6-1v-1h-7l-1-1-1 1c-2 0-2 0-4-2-2 1-3 0-5-1 1-1 1-1 1-2l-1-3c-1-2-3-3-5-4-2 0-5 0-6 1v-3h2l1-3c2 1 2 1 4 1s5 0 6-1z" class="a"></path><path d="M710 714l-2-1 1-1c1 0 2-1 3-1h1 7l1 1 7 1c-6 1-13-1-18 1z" class="L"></path><path d="M679 695c2 0 2 0 3 1 0 2 0 3 1 5h0c1 2 2 3 3 4l2 1c2 0 2 1 4 1h2c0 1 1 1 2 2l6 1 2 1 1 1-1-1c-5 0-11-1-16-2-3-1-7-1-10-2l-1-3c-1-2-3-3-5-4-2 0-5 0-6 1v-3h2l1-3c2 1 2 1 4 1s5 0 6-1z" class="I"></path><path d="M678 700c2 0 3 0 5 1h0c1 2 2 3 3 4h-2c-1 1-1 1-2 1 0-3-2-4-4-6z" class="Y"></path><path d="M679 695c2 0 2 0 3 1 0 2 0 3 1 5-2-1-3-1-5-1h0l-2-2c-1 0-2 0-3-1v-1c2 0 5 0 6-1z" class="B"></path><path d="M733 503l4 5c7 8 8 20 7 29 0 3-1 11-3 13l-1-1-1-2h-1c-1-2-1-2-2-3h0l-1-1h-1l-2-1v-2c-1-1-2-1-3-1h-1c1-1 1-2 2-2 0 1 0 1 1 1h0c1-2 1-4 1-7v-3c0-2 0-4-1-6v-2c-1-1-1-2-2-3l1-1-2-2h0c0-1-1-2-2-4l5 4h1c-1-1-2-1-2-3 1 0 2-1 3-2l1-1c-1-1-1-3-1-5z" class="Z"></path><path d="M736 518l-1-2h-1c1-1 1-1 1-2-1-1-1-2-1-3l1-1v-2h0l1 2c0 2 1 4 2 6-1 1-1 2-1 4l-1-2z" class="N"></path><path d="M736 510c3 3 5 6 6 10h-3l-1-4c-1-2-2-4-2-6z" class="l"></path><path d="M739 520h3c2 10 1 20-1 30l-1-1-1-2h-1c-1-2-1-2-2-3 2-8 3-16 3-24z" class="r"></path><path d="M728 514l8 4 1 2c0-2 0-3 1-4l1 4c0 8-1 16-3 24h0l-1-1h-1l-2-1v-2c-1-1-2-1-3-1h-1c1-1 1-2 2-2 0 1 0 1 1 1h0c1-2 1-4 1-7v-3c0-2 0-4-1-6v-2c-1-1-1-2-2-3l1-1-2-2z" class="K"></path><path d="M737 520c0-2 0-3 1-4l1 4c0 8-1 16-3 24h0l-1-1h-1l-2-1v-2l1-1h2v-1-2c1-1 1-2 0-3l1-1v-1-1-1-1l1-1c0-1-1-1-1-3 1 0 1 0 2-1-1 0-2 0-3-1l1-1 1-1h0z" class="a"></path><path d="M586 164h0v-2l1-2c1-1 1-2 1-3h0c1-1 1-2 1-3s1-1 1-2h1l1 1c2 4 2 12 1 16v1 2h1l-1 2c-2 1-2 2-3 4-1 1-2 2-3 4-1 1-2 1-2 2h-1c1-1 1-2 1-3h-3c0-1 0-2 1-3v-1l-2 2-1-1h-2l-1-1 1-1-1 1h-2l-1 2c-2 0-2 0-3-1l-1-3v-1 1c-1 1-1 2-1 4l-1-2c-1 0-1 0-1 1-1-3 0-6 1-9 0-1 0-3 1-4v-1h0s0 1 1 1v-5l2-1h0c1 1 2 1 4 2 0 1 0 1 1 1l2 1c2 1 3 1 5 2v1l2-2z" class="d"></path><path d="M586 164h0v-2l1-2c1-1 1-2 1-3h0c1-1 1-2 1-3s1-1 1-2h1l1 1v6l-2 2c-1 4-4 10-6 12l-1-1 1-1v-1h-1v-1h0c1-2 2-3 3-5z" class="E"></path><path d="M592 153c2 4 2 12 1 16v1 2h1l-1 2c-2 1-2 2-3 4-1 1-2 2-3 4-1 1-2 1-2 2h-1c1-1 1-2 1-3l1-2c1-2 1-3 2-4l-1-1v-2-1c1-3 3-5 4-8 0-1 0-1-1-2l2-2v-6z" class="Q"></path><path d="M592 159v5 1 2c-1 3-2 6-4 8l-1-1v-2-1c1-3 3-5 4-8 0-1 0-1-1-2l2-2z" class="Y"></path><path d="M572 159c1 1 2 1 4 2 0 1 0 1 1 1l2 1v2c-1 1-2 1-3 1h-1l-2 1 1 1 1-1v2c1-1 4-2 5-2 0 1-1 1-2 2h-1c-1 1-2 3-3 4-1 2-1 2-2 3l1 1h0 1 1 2-2l-1 2c-2 0-2 0-3-1l-1-3v-1 1c-1 1-1 2-1 4l-1-2c-1 0-1 0-1 1-1-3 0-6 1-9 0-1 0-3 1-4v-1h0s0 1 1 1v-5l2-1h0z" class="E"></path><path d="M579 165c-2 0-5 0-6-1v-2l-1-1 1-1c1 1 2 2 4 3v-1l2 1v2z" class="Y"></path><path d="M568 169c0-1 0-3 1-4v-1h0s0 1 1 1v-5l2-1c0 4 0 7-1 10v6h-1v-1 1c-1 1-1 2-1 4l-1-2c-1 0-1 0-1 1-1-3 0-6 1-9z" class="D"></path><path d="M705 540c0-1 0-3 1-4 1 1 2 1 4 1 0 2-1 3-1 5l1-1c1 1 1 1 2 1l2-2h-1v-2c1 1 2 1 4 2l1 1h2c1 0 2-1 3-1l1-1c1 1 3 0 4 0h1c1 0 2 0 3 1v2l2 1h1l1 1-2 3-2-1-1 1v1c-1 0-2 0-3 1l-1 2v1c-1 1 0 2-1 3-1 0-1 0-1 1l-1 1-1 1h-4 0c0 2-1 3-2 5v1l-1 5-2 5v-4l-1-1v-5h0c-2-4-5-7-5-12 0-2-1-3-2-4h1v-1s-1 0-2-1l1-1-3-1c0-1 0-2 1-3h1l1-1h-1z" class="b"></path><path d="M713 564c1-2 0-1-1-3 0-1 1-3 1-3 2 1 4 0 6 0 0 2-1 3-2 5v1l-1 5-2 5v-4l-1-1v-5h0z" class="D"></path><path d="M729 539c1 0 2 0 3 1v2l2 1h1l1 1-2 3-2-1-1 1v1c-1 0-2 0-3 1l-1 2v1c-2 0-2 0-4 1-1 1-2 2-2 4h-1c-1-1-1-1-1-2l1-2c1-2 3-3 3-6l-1-1 1-5v-1l1-1c1 1 3 0 4 0h1z" class="f"></path><path d="M723 540l1-1c1 1 3 0 4 0l1 3-2 2c-1-2 0-3-2-4-1 0-1 1-2 1v-1z" class="T"></path><path d="M734 543h1l1 1-2 3-2-1-1 1c-1-1 0-1-1-2l2-1h1l1-1z" class="P"></path><path d="M705 540c0-1 0-3 1-4 1 1 2 1 4 1 0 2-1 3-1 5l1-1c1 1 1 1 2 1l2-2h-1v-2c1 1 2 1 4 2l1 1h2c1 0 2-1 3-1v1l-1 5c-1 2-2 6-5 7-2 2-4 2-7 1-1 0-1 0-1-1s1-3 2-4l1 1h2l1-1 1-1h-1l-1 1h-1v-1c0-1 1-1 1-2h0c-1 1-3 1-4 1 0 2-1 4-2 5 0-2-1-3-2-4h1v-1s-1 0-2-1l1-1-3-1c0-1 0-2 1-3h1l1-1h-1z" class="C"></path><path d="M706 543l1-2c1 0 1 0 2 1v1h-2-1z" class="H"></path><path d="M706 545c1 0 2 0 3 1 1-2 1-2 1-3h1c0 2-1 3-1 4 0 2-1 4-2 5 0-2-1-3-2-4h1v-1s-1 0-2-1l1-1z" class="I"></path><path d="M571 246h2v1h-1l1 1c1 0 0 0 1 1h1l-2 2h1c0 1-1 2-1 3h0 1l1 1c1-1 3-1 4-2l1 1-3 2h0l-1 2 1 1h0 0 2v1h0c0 1 0 1-1 2h0l-2 1-1-1c-2 0-4 1-6 2h-1v1h1c0 2 0 2-1 3l1 1 2-1v1c-2 1-3 2-5 3-3 1-6 3-8 5-2 0-4 1-4 2h-1v-2h0c-3 1-8 2-11 0h-1v-1c4 0 7-3 10-5 1 0 1 0 1-1h-1c2-2 3-2 3-4-1 0-5 1-5 2-2 0-3 1-5 0 0 0 1 0 2-1 4-1 7-3 10-5l-1-1 5-6 2-2c0-1 1-2 2-3s1-2 2-2v2l3-1c0-1 2-2 2-3z" class="h"></path><path d="M571 246h2v1h-1l-1 2c0 2-1 2-1 4 0 1 1 0 0 2h0c1 1 1 2 2 3-1 0-2 1-4 1 0-1 0 0 1-1v-1l-2 1v-1l1-1v-4c0-1 0-2 1-2v-1c0-1 2-2 2-3z" class="H"></path><path d="M556 262h0c2-2 8-9 10-9h0c0 1-1 2-1 2 0 1 0 1 1 2l-4 5v1c-2-1-3 0-4 1h-1l-3 2c-1 0-5 1-5 2-2 0-3 1-5 0 0 0 1 0 2-1 4-1 7-3 10-5z" class="M"></path><path d="M572 258c-1-1-1-2-2-3h0c1-2 0-1 0-2 0-2 1-2 1-4l1-2 1 1c1 0 0 0 1 1h1l-2 2h1c0 1-1 2-1 3h0 1l1 1c1-1 3-1 4-2l1 1-3 2h0l-1 2 1 1h0 0 2v1h0c0 1 0 1-1 2h0l-2 1-1-1c-2 0-4 1-6 2 1-1 2-2 2-3h-2c1-1 3-2 4-3v-1l-1 1z" class="I"></path><path d="M562 262l4-2v1l-2 2h1 2c-1 1-2 1-3 2v1l1 1c-1 0-1 1-1 2 1 0 2-1 3-1 0 1-1 1-1 2l-1 1h0c1 1 1 0 1 1-3 1-6 3-8 5-2 0-4 1-4 2h-1v-2l2-1c2-1 4-3 6-5l-1-1c0 1-1 1-2 2 1-2 2-2 2-4h-2l1-1v-1-1c1-1 1-2 3-2v-1z" class="k"></path><path d="M554 266l3-2h1c1-1 2-2 4-1-2 0-2 1-3 2v1 1l-1 1h2c0 2-1 2-2 4 1-1 2-1 2-2l1 1c-2 2-4 4-6 5l-2 1h0c-3 1-8 2-11 0h-1v-1c4 0 7-3 10-5 1 0 1 0 1-1h-1c2-2 3-2 3-4z" class="U"></path><path d="M554 266l3-2h1c1-1 2-2 4-1-2 0-2 1-3 2v1c-3 2-5 4-8 5 1 0 1 0 1-1h-1c2-2 3-2 3-4z" class="C"></path><path d="M500 262c2 1 4 1 7 2 0 1 1 1 2 1h1c1 1 2 1 3 1h0c3 0 5 1 7 1l3 1h4c2 1 5 0 7 0h1 2c2 2 5 1 7 1v1 1h3 1 0c1 0 1 0 2-1h-2l1-1v-1c0-1 4-2 5-2 0 2-1 2-3 4h1c0 1 0 1-1 1-3 2-6 5-10 5v1h1c3 2 8 1 11 0h0v2c-2 1-2 2-4 2h-1l-1-1c-1 1-1 1-2 1h-3l-1 1c-2 0-3 0-4-1h-2-2 0l2 1v1c-3 0-5-1-7-2v1c-8-4-14-10-22-13l-4-1 6 1v-1h0-1c-2-1-1-1-2-1s-2-1-3-1h-1 0v-1h-2c-1-1-1-1-2-1h-1l-2-1h0c1-1 4 0 6-1z" class="Q"></path><path d="M554 266c0 2-1 2-3 4h1c0 1 0 1-1 1-3 2-6 5-10 5v1h-1c-2 0-3-1-5-2h5 0c-3-2-7-2-11-3h0 9 7c1 0 2 0 3-1 1 0 1 0 2-1h-2l1-1v-1c0-1 4-2 5-2z" class="D"></path><path d="M500 262c2 1 4 1 7 2 0 1 1 1 2 1h1c1 1 2 1 3 1h0l5 2h1c1 0 3 0 4 1h4c1 1 2 1 3 1h-2l2 1h-3l-9-1c-6-1-11-3-17-5h-2c-1-1-1-1-2-1h-1l-2-1h0c1-1 4 0 6-1z" class="r"></path><path d="M502 268l6 1c2 1 5 2 8 3 4 0 10 2 14 4v-1l1-1c1 0 3 0 4 1 2 1 3 2 5 2h1 1c3 2 8 1 11 0h0v2c-2 1-2 2-4 2h-1l-1-1c-1 1-1 1-2 1h-3l-1 1c-2 0-3 0-4-1h-2-2 0l2 1v1c-3 0-5-1-7-2v1c-8-4-14-10-22-13l-4-1z" class="B"></path><path d="M530 276v-1l1-1c1 0 3 0 4 1 2 1 3 2 5 2h1 1c3 2 8 1 11 0h0v2c-2 1-2 2-4 2h-1l-1-1c-3-1-5-1-9-1-2 0-6 0-7-2l-1-1z" class="C"></path><path d="M530 276v-1l1-1c1 0 3 0 4 1 2 1 3 2 5 2h1 1l-1 1c-3 0-6 0-10-1l-1-1z" class="J"></path><path d="M689 128c1-1 3-3 2-4 0-2-1-2-1-3 4-1 11-1 15-1h0c0-1-1-2-1-3v-4c-1-1-2-3-2-4v-4c1-2 1-5 1-7 1-2 0-5 1-6l2 1v6c0 1 0 2 1 3 1 5 1 7-2 12 0 0 0 1 1 2 0 1 1 2 1 4h0c3 0 9 0 12 2 0 1-2 2-2 4l2 2c0 1 1 2 2 3-1 1-1 2-2 3h-1l-1 1h-2-3 0l-1-1h-5c-2 0-7 0-9 1l-1 1v-1c-2 0-3-1-4-2h-3l-1-2c0-1 1-2 1-3z" class="h"></path><path d="M689 128h3v5h-3l-1-2c0-1 1-2 1-3z" class="E"></path><path d="M715 135c1-2 1-4 1-5s0-2 1-3l2 1c0 1 1 2 2 3-1 1-1 2-2 3h-1l-1 1h-2z" class="l"></path><path d="M705 100l1 2 1 4c0 2-1 4-2 5l-1 1v-1c-1-2-1-3-1-5 1-2 1-4 2-6z" class="r"></path><path d="M697 135h-1l-3-3c0-1 0-1 1-1 0 0 2 0 3-1h13 5l-1-1c-1-1-7 0-8 0-4 0-9 1-13 0v-2c0-1 0-2 1-3h21v4 1 2c0 1-1 1-1 2l-3 1h-5c-2 0-7 0-9 1z" class="E"></path><path d="M552 118c1 1 1 2 1 3l2 4c1 1 1 3 2 5l1-1v-1h1v6c1 2 1 3 1 5 1 4 1 9 3 13l1 1 1-2 1 1-4 11c-1 3-3 5-3 8-1 1-1 1-1 2h-1c-1 1-1 2-1 3v-4l-1 2h-1c0-1 0-1-1-2 0 1 0 1-1 1l1-1-2-4v-1c0-3 0-5 1-8v-2-1c-2-2-2-6-1-8v-1h-1l-1-1c0-2 0-3 1-5 0-1 1-2 1-2l-1-1v-3c1-2 1-2 1-4v-2c0-2 0-5 1-7v-4z" class="I"></path><path d="M557 161l1-2 1 3h0c0-1 1-1 1-2 1-1 1-2 1-3h-1c2-3 2-4 1-6 0-1 0-1-1-2-1-2 1-5 0-6l-1-1h0v-1l1-2c1 4 1 9 3 13l1 1 1-2 1 1-4 11c-1 3-3 5-3 8-1 1-1 1-1 2h-1c-1 1-1 2-1 3v-4l-1 2h-1c0-1 0-1-1-2 0 1 0 1-1 1l1-1-2-4v-1c0-3 0-5 1-8v-2l1 3v5c2-1 3-3 4-4z" class="i"></path><path d="M552 157l1 3v5c2-1 3-3 4-4v4c-1 1-2 2-2 4v1h0l-2-2v4c0 1 0 1-1 1l1-1-2-4v-1c0-3 0-5 1-8v-2z" class="V"></path><path d="M552 118c1 1 1 2 1 3l2 4c1 1 1 3 2 5v4 1h1v1l-1 1h0c0 2-1 3-1 5-1 1-1 1-1 2 0 2-1 3-2 4 0 3 1 6 0 8 0 2 1 3 0 4l-1-3v-1c-2-2-2-6-1-8v-1h-1l-1-1c0-2 0-3 1-5 0-1 1-2 1-2l-1-1v-3c1-2 1-2 1-4v-2c0-2 0-5 1-7v-4z" class="B"></path><path d="M550 141v5c1-1 2-4 2-6 1 1 1 1 1 2v6h0c0 3 1 6 0 8 0 2 1 3 0 4l-1-3v-1c-2-2-2-6-1-8v-1h-1l-1-1c0-2 0-3 1-5z" class="D"></path><path d="M553 121l2 4c1 1 1 3 2 5v4 1h1v1l-1 1h0c0 2-1 3-1 5-1 1-1 1-1 2 0 2-1 3-2 4h0v-6c1-3 1-7 1-10-1 0-1 2-2 3v-1c0-2 1-7 1-9v-4z" class="W"></path><path d="M666 710l1-1c1 0 2 0 4-1 1 1 0 3 2 3v-2l1-1 3 1c2 1 3 2 5 1 2 2 2 2 4 2l1-1 1 1h7c-1 1-2 1-2 1-3 0-5 1-7 2l-7 4-1 3h-2c0 1-1 1-1 2s0 1-1 2c1 1 1 1 2 1h-3v1c-3 0-5-1-8 0h0c0-1-1-1-1-2v-1c-1 1-1 2-1 3l-2 2 1 2c-3 1-5 2-7 4-2 1-7 2-8 4h-1c0-3 2-3 4-5h-2 0c1 0 3-1 4-1-1-3-2-4-2-7 1-1 1-1 2-1v-3c2 0 2-1 4-2l1-1 1-1h1c0-1-1-2-1-2h0l2-1h0c-1-1-1-1-1-2 2 0 4-2 5-4h2 0z" class="X"></path><path d="M673 727h-6c1-1 1-1 1-2 4-1 7-4 11-6l-1 3h-2c0 1-1 1-1 2s0 1-1 2c1 1 1 1 2 1h-3z" class="L"></path><path d="M686 712l1-1 1 1h7c-1 1-2 1-2 1-3 0-5 1-7 2l-1-1-1 1c-3 0-3 1-5 2l-4 2-2 1-1-1c2-1 6-2 7-3l-2-1h0l3-2c2-1 4-1 6-1z" class="i"></path><path d="M657 724h0c1 0 2 0 3-1h3l1-1h1c0 1 0 2-1 2l-2 2c1 0 1 0 2-1-1 1-1 2-1 3l-2 2 1 2c-3 1-5 2-7 4-2 1-7 2-8 4h-1c0-3 2-3 4-5h-2 0c1 0 3-1 4-1-1-3-2-4-2-7 1-1 1-1 2-1h2 0c0-1 1-2 1-2h2z" class="C"></path><path d="M655 729c0-1 1-2 2-2h0 2v-1l1 1-2 2h1l2-2 2 1-2 2-3 2h-1c-2-1-2-2-2-3z" class="H"></path><path d="M655 729c0 1 0 2 2 3h1l3-2 1 2c-3 1-5 2-7 4-2 1-7 2-8 4h-1c0-3 2-3 4-5h-2 0c1 0 3-1 4-1s1 0 2-1l-1-2h-1c1-1 2-1 2-2h1z" class="B"></path><path d="M666 710l1-1c1 0 2 0 4-1 1 1 0 3 2 3v-2l1-1 3 1c2 1 3 2 5 1 2 2 2 2 4 2-2 0-4 0-6 1l-3 2h0l-3 2c-1 1-2 2-4 2-1 1-3 3-4 3v-1-2c-2 0-3 2-5 2-1 1-1 0-2 0-1 1-2 2-2 3h-2s-1 1-1 2h0-2v-3c2 0 2-1 4-2l1-1 1-1h1c0-1-1-2-1-2h0l2-1h0c-1-1-1-1-1-2 2 0 4-2 5-4h2 0z" class="e"></path><path d="M674 714l3 1h0l-3 2c-1-1-1 0-2-1h0c1-1 1-2 2-2z" class="M"></path><path d="M666 710l1-1c1 0 2 0 4-1 1 1 0 3 2 3v-2l1-1 3 1c2 1 3 2 5 1 2 2 2 2 4 2-2 0-4 0-6 1l-3 2-3-1 3-2v-1h-1-1c-1 0-2 1-3 1l-1-1h-1-4v1c-1 1-1 1-3 1v1h-2c-1 2 0 2-1 4l-2-1 2-1h0c-1-1-1-1-1-2 2 0 4-2 5-4h2 0z" class="S"></path><path d="M701 671l4-2c5-3 11-4 17-2-1 1-2 1-3 2-3 1-15 5-15 9 1 1 1 3 2 4l-2 2h-1l1 1v1c-1 2-1 2-1 4v2h1 0c1-1 1 0 3 0 1-1 4-2 6-2v1h0c-1 0-2 1-2 2-2 2-4 2-6 3l-8 3-6 4c-2 1-2 2-3 3l-2-1c-1-1-2-2-3-4h0c-1-2-1-3-1-5-1-1-1-1-3-1h-3v-1h1c1-1 2-1 3-2v-1l3-3c2-3 4-5 7-7l3-3 7-6 1-1z" class="T"></path><path d="M687 690l6-4c-1 2-1 3-1 5l-1 1h-4v-2z" class="l"></path><path d="M697 681l6 3 1 1v1c-4 2-8 4-12 5 0-2 0-3 1-5s3-3 4-5z" class="r"></path><path d="M701 671c0 2-1 3-2 5h0c-1 0-2 1-2 2-1 0-1 0-2 1l-1 1v1 1c1 0 0 0 1-1l1-1 1 1c-1 2-3 3-4 5l-6 4h0v-2-1l-5 5-2-1 3-3c2-3 4-5 7-7l3-3 7-6 1-1z" class="P"></path><path d="M682 696h1v-1c-1 0-1-1-2-1 0-2 0-2 2-2 0 1 1 1 1 1l1 1h3c2 1 0 2 3 2v-1l1 1h3l1-1c2-2 5-2 7-2 0 2-3 2-4 4-1 0-2 1-2 2l-6 4c-2 1-2 2-3 3l-2-1c-1-1-2-2-3-4h0c-1-2-1-3-1-5z" class="p"></path><path d="M682 696h1v-1c-1 0-1-1-2-1 0-2 0-2 2-2 0 1 1 1 1 1l1 1v1h1v1h-2v1c0 1 1 1 2 1v1c-1 0-2 0-2 1s0 1-1 1h0c-1-2-1-3-1-5z" class="K"></path><path d="M701 671l4-2c5-3 11-4 17-2-1 1-2 1-3 2-3 1-15 5-15 9 1 1 1 3 2 4l-2 2h-1l-6-3-1-1-1 1c-1 1 0 1-1 1v-1-1l1-1c1-1 1-1 2-1 0-1 1-2 2-2h0c1-2 2-3 2-5z" class="O"></path><path d="M614 161v1l1 1c0-2 2-3 3-4 0-2-1-5 0-7 1 1 1 1 1 2s0 2 1 2v1c0 9-10 16-11 25 0 3-1 7-2 10-1 4 0 5-3 8h-1c-3-2-6-8-9-10v-2l-2 1-1 1h1v3l1 1v1l-3-3c-1 0-2-1-3-1v-1c-1 0-2 0-3-1l-1-1c-1-1-3-2-4-4v-1c-2 0-3-1-4-2v-2l2-2 1 1h2l1 1 2-2v1c-1 1-1 2-1 3h3c0 1 0 2-1 3h1c0-1 1-1 2-2 1-2 2-3 3-4 1-2 1-3 3-4l1-2c0-1 1-2 3-3-1 2-2 3-2 5l1 1v1h0 1c1-3 7-7 9-9h1 0c3-2 4-4 7-6z" class="J"></path><path d="M590 192v-2-1l-1-1 1-1v1h1v-1c0-1 1-1 2-2 1 1 1 2 1 3l-2 1-1 1h1v3l1 1v1l-3-3zm17-25h0c3-2 4-4 7-6l-1 3-2 2v1l-3 3-3 5-1 1s-1 0-1 1c-2 1-4 2-6 4 1-2 4-5 5-7v-2c1-2 4-3 5-5z" class="Q"></path><path d="M608 174h0l1-1v1h0v1c0 1 0 2-1 3h1v4c0 3-1 7-2 10-1 4 0 5-3 8h-1c-3-2-6-8-9-10 3-2 1-2 3-4v1l1 1v-1l-1-1c2-3 5-6 7-9h0l-1 3h1c1-2 3-4 4-6z" class="I"></path><path d="M608 174h0l1-1v1h0v1c0 1 0 2-1 3h1v4c0 3-1 7-2 10-1 4 0 5-3 8h-1v-1c0-1-1-2-1-3 2-3 1-6 1-9 0-2 1-4 3-5 0-1-1 0-1-1s2-4 3-5v-2z" class="E"></path><path d="M298 577l21 8 4 5h1l-1 1-3 1c-2 2-4 3-4 5-1 3 1 6 2 9 0 2 0 3-1 5-1 1-1 2-2 2h0l-1 1c-3 1-7 1-9 0-4-2-6-6-8-10-4-10-3-17 1-27z" class="t"></path><path d="M319 585l4 5h1l-1 1-3 1v-2c0-1-1-1-1-2-1-1 0-2 0-3z" class="l"></path><path d="M263 678c1-1 4 0 5-1-2-4-6-7-7-11l1-1c2 2 4 4 7 6 2 2 6 4 10 5 9 4 20 1 29 5 0 2 0 2 1 3s2 0 4 2v1l1 1v1l-3-1v1c0 1 1 1 2 1 1 1 2 2 2 4h0c0-1-1-1-2-1-2 0-3-1-5-1l-5-2h-1c-1 0-2 0-3-1l-1 1h-2c0 1 0 1 1 2-1 1-1 2-2 2h-3v-1c-1 0-2 1-3 0h-1-1v-1-1c-2 2-3 1-5 3l-16-4 1-2-3-1c0-1 1-1 1-2-1 0-1 0-2-1h0v-2h1v-1h-1v-3z" class="O"></path><path d="M268 688v-1h0l-1-1 1-1 2 2v-1c2 0 2 0 3 1h0c-1 1-2 1-2 2l-1 1-2-2z" class="K"></path><path d="M308 691v-1h2v-1h-2v-2c1 0 2 1 3 1v1c0 1 1 1 2 1 1 1 2 2 2 4h0c0-1-1-1-2-1-2 0-3-1-5-1v-1z" class="b"></path><path d="M293 688v-2h1v-2h2v2h1s1 0 1-1h3 1l-2 2 2 1v-1c1-1 1 0 2-1l1 2h-1v1h1v1c1 0 2 0 3 1v1l-5-2v-1c-2 0-2 0-4-1v-1c-2 1-3 1-5 1h-1z" class="K"></path><path d="M293 688h1c2 0 3 0 5-1v1c2 1 2 1 4 1v1h-1c-1 0-2 0-3-1l-1 1h-2c0 1 0 1 1 2-1 1-1 2-2 2h-3v-1c-1 0-2 1-3 0h-1-1v-1-1h3l1-1h-2 0v-1c2 0 3 0 4-1z" class="o"></path><path d="M273 687l1-1c1 0 1 0 2-1 1 0 1 1 2 1v-2h1s1 1 1 2l-3 3h-2v-1l-3 1v1c1 1 2 1 3 1h2c2-2 4-4 6-5 1-1 1-1 2-1h1l-1 1c-1 0-2 1-3 2l1 1v-1c1-1 4-2 5-2h1c1 1 2 1 2 1l1 1h-2-1c-1-1-2-1-3-1l1 1h1l1 1v1h0 2l-1 1h-3c-2 2-3 1-5 3l-16-4 1-2h1l2 2 1-1c0-1 1-1 2-2h0z" class="P"></path><path d="M307 525l1 1 2 2v25h0l-2 2s-1 0-2 1h0c-1 0-2 0-3 1l-4 1-5-1c-2 0-2 0-3-1 0-3-1-5-3-8-1-1-1-3-2-4l-2 2c0-1 1-2 1-3-1-4-1-10-2-15h0v-2h1c2 1 4 1 5 1h1l4-1h4v-1h9z" class="d"></path><path d="M307 525l1 1 2 2h-2l-1 2c-6 1-12 1-17 3l-2-2v-2l3-1h2 0c-1-1-2-1-3-1l4-1h4v-1h9z" class="G"></path><path d="M307 525l1 1 2 2h-2-4c-2-1-4 0-7 0h-4 0c-1-1-2-1-3-1l4-1h4v-1h9z" class="c"></path><path d="M294 526h4l3 1c-2 0-3 0-4 1h-4 0c-1-1-2-1-3-1l4-1z" class="d"></path><path d="M292 538c0-1 1-2 2-3h5c1-1 3-1 5 0v-1h1v1h-1c1 1 2 2 2 3 1 1 0 3 0 5s1 5 0 8c-1-2-2-3-3-5l-8-8h-3z" class="J"></path><path d="M283 526h1c2 1 4 1 5 1h1c1 0 2 0 3 1h0-2l-3 1v2l2 2v2c0 2-1 3-1 4 1 1 1 1 2 0l1-1h3l8 8c1 2 2 3 3 5l1 1h1c1 0 1 0 2 1l-2 2s-1 0-2 1h0c-1 0-2 0-3 1l-4 1-5-1c-2 0-2 0-3-1 0-3-1-5-3-8-1-1-1-3-2-4l-2 2c0-1 1-2 1-3-1-4-1-10-2-15h0v-2z" class="C"></path><path d="M302 552l-9-6c-1-1-3-2-4-2-1-1-1-3-1-4l1-1c1 1 1 1 2 0v2c3 0 4 2 5 4 2 2 6 5 6 7z" class="J"></path><path d="M283 526h1c2 1 4 1 5 1h1c1 0 2 0 3 1h0-2l-3 1v2l2 2v2h-1v2c0 1-1 1-1 2-3-3-3-7-3-11h-2 0v-2z" class="c"></path><path d="M283 526h1c2 1 4 1 5 1h1c1 0 2 0 3 1h0-2c-1 1-3 1-4 0h-2 0-2 0v-2z" class="l"></path><path d="M288 548h2c0 1 1 1 1 1h1l2 2 2 1 1-1c3 1 3 2 6 2v1h3v2c-1 0-2 0-3 1l-4 1-5-1c-2 0-2 0-3-1 0-3-1-5-3-8z" class="j"></path><path d="M294 557c0-1 2-1 2-3v-1c1 1 2 1 3 1h2 2 3v2c-1 0-2 0-3 1l-4 1-5-1z" class="o"></path><path d="M292 538h3l8 8c1 2 2 3 3 5l1 1h1c1 0 1 0 2 1l-2 2s-1 0-2 1h0v-2h-3v-1l-1-1c0-2-4-5-6-7-1-2-2-4-5-4v-2l1-1z" class="G"></path><path d="M743 209c3-1 8-4 11-4 1 2 3 2 5 3l11 3c5 1 11 3 17 4s13 1 20 3c4 1 9 2 13 3 2 1 7 3 9 3 1 0 2-2 3-1-1 1-2 1-3 2h-2l-1-1c-1 1-2 1-3 2l-9 1c-4 1-10 1-14 0-8 0-18-1-27-4-7-1-14-4-21-6v-1h-1l-2-1c-1 0-3-1-4-2l-1-1v-1c-2 0-4 1-6 1h-1l3-2 3-1z" class="F"></path><path d="M743 209c3-1 8-4 11-4 1 2 3 2 5 3l11 3c-3 0-8-1-10 0 3 1 5 1 8 2-5 0-12-4-16 0h-1c0 1 1 1 2 2h-3-1c-1 0-3-1-4-2l-1-1v-1c-2 0-4 1-6 1h-1l3-2 3-1z" class="M"></path><path d="M743 209c3-1 8-4 11-4 1 2 3 2 5 3l-1 1h-1-4c-2 1-4 1-6 2h-1l2 2h-3l-1-1v-1c-2 0-4 1-6 1h-1l3-2 3-1z" class="b"></path><path d="M787 217c-1 0-2 0-3-1h-2-1v-1c1 0 2 0 3 1 3 1 7 0 10 1 2 0 4 0 6 1h4 1c6 1 15 3 21 6h0 0c-1 1-2 1-3 2l-9 1c-4 1-10 1-14 0l3-1c1 1 2 1 3 1h2 3c0-1 0-1 1-1h1c-2 0-4 0-5-1s-1 0-2-1l-5-2c-1 0-2-1-3-1h0c-1-1-1-1-2-1l-4-2h-2c-1 0-2-1-3-1z" class="G"></path><defs><linearGradient id="M" x1="777.276" y1="206.446" x2="789.224" y2="235.554" xlink:href="#B"><stop offset="0" stop-color="#d4cdc5"></stop><stop offset="1" stop-color="#ecefeb"></stop></linearGradient></defs><path fill="url(#M)" d="M755 215v-1c3 1 6 1 9 2 2 0 5-1 7 1v-1l-2-1h0l3 1h4l1 1h1 1c1 0 3 0 4 1h3l1-1c1 0 2 1 3 1h2l4 2c1 0 1 0 2 1h0c1 0 2 1 3 1l5 2c1 1 1 0 2 1s3 1 5 1h-1c-1 0-1 0-1 1h-3-2c-1 0-2 0-3-1l-3 1c-8 0-18-1-27-4-7-1-14-4-21-6v-1h-1c2 0 3 0 4-1z"></path><path d="M755 215v-1c3 1 6 1 9 2 2 1 4 2 7 2h-1c-1 1-2 1-3 1-2 0-4-1-6-2-1-1-3-2-6-2z" class="Q"></path><path d="M764 216c2 0 5-1 7 1v-1l-2-1h0l3 1h4l1 1h1 1c1 0 3 0 4 1h3l1-1c1 0 2 1 3 1h2l4 2c1 0 1 0 2 1h0c-1 0-2-1-3-1-2 0-3 0-4-1h-5 2 0l-1 1c3 1 6 1 10 3h-1l-11-3h-1-1-3c-2-1-7-3-9-2-3 0-5-1-7-2z" class="J"></path><defs><linearGradient id="N" x1="306.825" y1="377.469" x2="286.238" y2="358.09" xlink:href="#B"><stop offset="0" stop-color="#b9b9b3"></stop><stop offset="1" stop-color="#ebe9e3"></stop></linearGradient></defs><path fill="url(#N)" d="M287 358c0-1 1-2 1-3l1-1 2-2c2-3 4-5 6-7 1 0 2-1 4-2 1-2 4-4 6-4l1 1c-2 2-4 3-6 6v1c-1 1-1 3-1 5 0 1 0 0-1 1 0 2 0 6 1 9l1 4c0 2 1 3 2 4l1-1c0 3-1 3-2 5l-1 1 1 2h2-1l1 1 1 1s0 1 1 2c-1 1-1 1 0 1-2 1-2 1-3 2h-2c0 1 0 1-1 2v-1h-1l-1 2v1c-1 5-2 8-1 13l-1 1-1-1v-2h-1-1-1-1l-3-3c1-2 1-3 0-4s-2-1-3-1l-4-15h0c0-1-1-3 0-4v-3c0-2 1-3 1-5h0c1-3 2-4 4-6z"></path><path d="M303 377h2-1c-2 2-2 4-4 6v-3l-2-1c1-1 2-1 3-1h1l1-1z" class="F"></path><path d="M296 364v-1c0-1 0-3-1-3v-1-3h1v2h1v2c1 2 0 4 1 7l-1 1-1-4z" class="d"></path><path d="M298 367c0 1 1 1 1 2l1 1c1 2 2 3 2 4h1l-1 1h-2c0-1-1-1-1-3 0 0 0-1-1-2l-1-2 1-1zm-8 0h1v3l-1 1v3l1 1v6l-1-1v-1-2-1c-1-1-1-1-1-2v-1c-1-1-1-3-1-5 1 1 1 1 2 1v-2z" class="J"></path><path d="M301 378c-2 0-3-1-4-2 0-2-1-4-1-6v-6l1 4 1 2c1 1 1 2 1 2 0 2 1 2 1 3h2l1 2-1 1h-1z" class="B"></path><path d="M289 383h0c1 1 2 3 2 5l5 10c0-1 0-3 1-4 1-2 0-4 1-6h1c-1 5-2 8-1 13l-1 1-1-1v-2h-1-1-1l-4-16z" class="o"></path><defs><linearGradient id="O" x1="297.157" y1="386.132" x2="278.343" y2="375.868" xlink:href="#B"><stop offset="0" stop-color="#b9b9bd"></stop><stop offset="1" stop-color="#e2e1da"></stop></linearGradient></defs><path fill="url(#O)" d="M287 358v4c0 1 0 1-1 2v5c1 2 1 3 1 5v1l-1 1c0 1 0 1 1 2v3 2 1h1v-2l1 1 4 16h-1l-3-3c1-2 1-3 0-4s-2-1-3-1l-4-15h0c0-1-1-3 0-4v-3c0-2 1-3 1-5h0c1-3 2-4 4-6z"></path><path d="M640 772v3h1c1-1 1-1 2-3 1-1 1-2 3-2 3-1 7 0 9 2 2 1 3 3 3 5 1 3 1 6-1 8-3 4-11 5-11 11 0 2 1 5 1 7 1 5 0 10-4 14-2 4-7 6-9 10-1 0-1 1-2 1v1c-4 2-8 3-13 2h-1-1c1-3 1-6 1-9-3-1-5-1-7-3h-1 2v-2-1l1-1 2 2c3 0 6 0 9 1l-1-2h5 1 0c2-1 4-1 6-2 3-2 4-4 5-7l1-3c2-6 1-13-2-19-1-2-5-8-8-9l1-1 2-2 5-1h1z" class="t"></path><defs><linearGradient id="P" x1="618.375" y1="824.078" x2="630.331" y2="817.814" xlink:href="#B"><stop offset="0" stop-color="#727271"></stop><stop offset="1" stop-color="#8d8b89"></stop></linearGradient></defs><path fill="url(#P)" d="M612 819v-2-1l1-1 2 2c3 0 6 0 9 1h5c1 1 2 1 3 1 1 1 1 1 2 3-2 2-11 1-14 1h-1c0 1-1 6-1 8h-1c1-3 1-6 1-9-3-1-5-1-7-3h-1 2z"></path><path d="M612 819v-2-1l1-1 2 2c3 0 6 0 9 1h5c1 1 2 1 3 1-6 0-12 1-17 0h-1-1-1z" class="Y"></path><path d="M310 662l1-1 1 1c2 0 3-1 5 0 1 0 2 0 4 1 1 0 2-1 4 0 1 0 1 0 2 1h2v1h2v3c0 2 1 2 1 4l1 1h-1l-2-2h0l-1 1c1 1 3 2 4 3v2l2 1v1l2 3h-1c2 2 3 2 4 5h-2c1 1 2 2 2 3l-1 1h0c1 2 0 4 0 6v1l-1 5-2 2h-2v-1l-1-1-6-3-6-3-6-3h0c0-2-1-3-2-4-1 0-2 0-2-1v-1l3 1v-1l-1-1v-1c-2-2-3-1-4-2s-1-1-1-3l5 2c1 1 2 2 4 3 0-2 5-4 6-4-1-1-1-2-2-3l-2-1c-2-2-4-4-6-5-4-2-9-2-11-6l-1-1-1-1h-4v-2h0v-1h9c1 1 3 0 5 0z" class="I"></path><path d="M310 662l1-1 1 1c2 0 3-1 5 0 1 0 2 0 4 1 1 0 2-1 4 0 1 0 1 0 2 1h2v1h2v3c-1 0-2 0-4-1h-2c-1 0-1-1-3-1s-3-1-5-3h-6 0l-1-1z" class="H"></path><path d="M302 667c1-2 3-1 5-1 6 0 11 4 16 7v1c1 2 2 4 4 5 0 2 1 2 2 3-3 0-3-2-5-3h-3l-2-1c-2-2-4-4-6-5-4-2-9-2-11-6z" class="g"></path><path d="M318 667l-1-1c1 0 2 0 3 1s2 1 3 1c2 1 4 3 6 4 1 1 3 2 4 3v2l2 1v1l2 3h-1c2 2 3 2 4 5h-2c1 1 2 2 2 3l-1 1-4-3c-2-4-5-6-8-9-2-1-3-3-4-5v-1h0c-1-2-3-3-6-5l1-1z" class="W"></path><path d="M323 673h0c-1-2-3-3-6-5l1-1c2 1 3 2 5 4h1l6 6v-1h1l3 4h-1c-2 0-3-1-4-2v1l-6-5v-1z" class="V"></path><path d="M323 674l6 5v-1c1 1 2 2 4 2h1l2 2c2 2 3 2 4 5h-2c1 1 2 2 2 3l-1 1-4-3c-2-4-5-6-8-9-2-1-3-3-4-5z" class="a"></path><path d="M329 679v-1c1 1 2 2 4 2h1l2 2c2 2 3 2 4 5h-2c-2-3-6-5-9-8z" class="D"></path><path d="M327 679c3 3 6 5 8 9l4 3h0c-1 1-2 2-3 2s-3-1-4 0c-2 0-1 1-4 0h0-1c-3-1-4-3-5-5l-5-2c0-2 5-4 6-4-1-1-1-2-2-3h3c2 1 2 3 5 3-1-1-2-1-2-3z" class="r"></path><path d="M322 688c3 2 8 4 11 4v-2l2 1 1 2c-1 0-3-1-4 0-2 0-1 1-4 0h0-1c-3-1-4-3-5-5z" class="U"></path><path d="M327 679c3 3 6 5 8 9l4 3h0c-1 1-2 2-3 2l-1-2-2-1c-3-2-4-5-7-7-1-2-1-2-3-1-1-1-1-2-2-3h3c2 1 2 3 5 3-1-1-2-1-2-3z" class="K"></path><path d="M308 681l5 2c1 1 2 2 4 3l5 2c1 2 2 4 5 5h1 0c3 1 2 0 4 0 1-1 3 0 4 0s2-1 3-2c1 2 0 4 0 6v1l-1 5-2 2h-2v-1l-1-1-6-3-6-3-6-3h0c0-2-1-3-2-4-1 0-2 0-2-1v-1l3 1v-1l-1-1v-1c-2-2-3-1-4-2s-1-1-1-3z" class="g"></path><path d="M334 704h2c0-2 0-2-1-3 1-2 3-2 4-3l-1 5-2 2h-2v-1zm-13-7v-1h1c1 0 2 1 3 1h2c1 0 3 1 4 2l2 4-6-3-6-3z" class="O"></path><path d="M315 694h0c0-2-1-3-2-4-1 0-2 0-2-1v-1l3 1c2 2 3 3 5 3l5 3c1 1 2 1 3 2h-2c-1 0-2-1-3-1h-1v1l-6-3z" class="U"></path><path d="M308 681l5 2c1 1 2 2 4 3l5 2c1 2 2 4 5 5h1 0c-3 0-5-1-9-1-2 0-3-1-5-3v-1l-1-1v-1c-2-2-3-1-4-2s-1-1-1-3z" class="P"></path><path d="M658 689h1c0 2 0 3-1 4h1l2-1h1c2 1 3 2 3 4 2-1 2-1 4-1l-1 3h-2v3c1-1 4-1 6-1 2 1 4 2 5 4l1 3c0 1 0 1-1 2l-3-1-1 1v2c-2 0-1-2-2-3-2 1-3 1-4 1l-1 1h0-2c-1 2-3 4-5 4 0 1 0 1 1 2h0l-2 1h0s1 1 1 2h-1l-1 1-1 1c-2 1-2 2-4 2v3c-1 0-1 0-2 1 0 3 1 4 2 7-1 0-3 1-4 1v-4c0-1 1-3 1-4h-1c-1-1-2-1-3-1s-2-1-3-1c-2 0-5 1-6 0-1 0-2 0-3-1s-1-1-2-1l1-1-1-2c3-1 4-4 5-7 0-2-1-4-2-7h0 3 1v-1l2-2c1-1 2-2 4-3l-1-3 5-5c1 1 2 1 3 2l2-1 2-2h1l2-2z" class="g"></path><path d="M643 724v-1-1l1-1 1 1c1-1 1-1 2-1s1 0 2 1v5h-1c-1-1-2-1-3-1s-2-1-3-1l1-1z" class="I"></path><path d="M672 700c2 1 4 2 5 4l1 3c0 1 0 1-1 2l-3-1-1 1v2c-2 0-1-2-2-3-2 1-3 1-4 1l-1 1c1-2 1-3 2-4h1 6l1-1c-1-1-1-2-2-2h-1l-1-3z" class="b"></path><path d="M649 722h1c0-1 1-3 2-3s2 1 2 1c2-1 2-2 2-3 1-1 1-1 2-1v1h0s1 1 1 2h-1l-1 1-1 1c-2 1-2 2-4 2v3c-1 0-1 0-2 1 0 3 1 4 2 7-1 0-3 1-4 1v-4c0-1 1-3 1-4v-5z" class="n"></path><path d="M637 711c1 0 2 1 3 1v1c-1 3-2 4-3 7l-1 1v2c1 1 0 1 2 0 1 0 3 0 5 1l-1 1c-2 0-5 1-6 0-1 0-2 0-3-1s-1-1-2-1l1-1-1-2c3-1 4-4 5-7l1-2z" class="C"></path><path d="M658 689h1c0 2 0 3-1 4h1l2-1h1c2 1 3 2 3 4 2-1 2-1 4-1l-1 3h-2v3h-1-1c-5 2-7 5-9 11v1c-3 0-9-1-11-2h-2c-1 0-1 1-2 1s-2-1-3-1l-1 2c0-2-1-4-2-7h0 3 1v-1l2-2c1-1 2-2 4-3l-1-3 5-5c1 1 2 1 3 2l2-1 2-2h1l2-2z" class="D"></path><path d="M658 689h1c0 2 0 3-1 4h1c-1 2-3 2-5 4h0v-1c1-1 1-2 2-3l-1-2h1l2-2z" class="M"></path><path d="M661 692h1c2 1 3 2 3 4-4 2-7 3-10 6l-1-1 1-1c1-1 3-2 5-3l2-2h-2v-1c0-1 1-1 1-2z" class="C"></path><path d="M654 701l1 1-4 6c-1 0-1 0-2-1s0-1-1-1-1 0-1-1c2-2 4-3 7-4z" class="Z"></path><path d="M655 691l1 2c-1 1-1 2-2 3v1c-3 0-3 2-5 4-1 0-3 0-4 1l-1 1h2l1 1h-1c-2 0-2 0-4 2l-2 1v1c-1 1 0 1-1 1-1 1-1 1-2 1h-1l1 1-1 2c0-2-1-4-2-7h0 3 1v-1l2-2c1-1 2-2 4-3l-1-3 5-5c1 1 2 1 3 2l2-1 2-2z" class="i"></path><path d="M648 692c1 1 2 1 3 2-1 1-2 1-3 2-1 0-3 3-4 4l-1-3 5-5z" class="U"></path><path d="M665 696c2-1 2-1 4-1l-1 3h-2v3h-1-1c-5 2-7 5-9 11v1c-3 0-9-1-11-2h-2c-1 0-1 1-2 1s-2-1-3-1l-1-1h1c1 0 1 0 2-1 1 0 0 0 1-1v-1l2-1v2c1 1 2 1 3 1h0c2 2 2 2 5 2 0-1 0-2 1-3l4-6c3-3 6-4 10-6z" class="B"></path><path d="M664 701l-1-1 1-2h1v3h-1z" class="F"></path><path d="M823 156c2 0 4 0 6 1 1 0 3 0 3 1h-1-12c4 0 9 1 13 2 3 0 5 1 7 2h-1c0 1-1 1-1 1 0 1 0 1 1 1v1c-1 0-2 0-3 1l-1-1c-1 1-6 1-8 1-2-2-9-1-12-1h0c-1 1-3 1-4 2h-7c-2 0-3 0-4 1-4 1-7 1-11 2h-5 0l13-3 10-2h-7v-1c-1 0-1 0-2 1-2 0-4 1-6 1h0 2v1h-7c-3 1-7 1-9 3-1 0-2-1-2-1v-3l3-1c-1 0-30 8-34 10l-11 4c-2 1-4 1-5 2l-2 1-12 4 1-6c0-1 1-1 2-2l1-1c2 0 4-1 6-3 1 1 3 0 4-1 1 0 2 0 3-1l5-2h3l1-1h2c1-1 2-1 3-2h1c2-1 3-1 5-2 1 0 1 0 2-1h2c1-1 1-1 2-1h2l1-1h3 1c1-1 1-1 2-1 1-1 3 0 4-1h1 2l2-1h2c1-1 2-1 3-2l3-1c1 1 2 1 3 0 2 3 3 2 5 3h2c2 0 4 0 6 1h3c1-1 1-1 2-1h3l1 1c2-2 6 0 8-2v-1c-1-1-6 0-8 0-1 0-2 1-3 0h1l2-1h8c1 1 1 1 2 1 2-1 3-1 5-1z" class="E"></path><path d="M717 178c1 1 2 1 4 1l9-2c-3 2-6 2-10 4 0 0 0 1 1 1h2c1-1 0 0 1 0h2 0l-12 4 1-6c0-1 1-1 2-2z" class="W"></path><path d="M832 160c3 0 5 1 7 2h-1c0 1-1 1-1 1 0 1 0 1 1 1v1c-1 0-2 0-3 1l-1-1c-1 1-6 1-8 1-2-2-9-1-12-1l1-1h2c3 0 7 0 10-1h5c0 1 1 1 2 1-1-1-2-1-2-1 1-1 2-1 3-2h0c-1-1-1-1-2-1h-1z" class="Q"></path><path d="M712 159l2 13c1 0 2 0 3-1v1c1 2 2 2 4 3h1c1-1 1-1 2-1-2 2-4 3-6 3l-1 1c-1 1-2 1-2 2l-1 6 12-4 2-1h1l-9 4c-1 1-3 1-4 2 0 0-1 3-1 4-1 1 0 2 0 4h1c-1 2-1 2-1 3-1 1 0 1 0 2-1 3-1 7 0 10l1 1-2 2 1 2c0 1 1 1 1 2h3l2-2 2 2 1-1 1 2-3 2h3c-1 1-2 2-2 4l1 1 6 3c2 1 3 1 5 2l8 3h-1v1c-3-1-5-2-7-2h-1l1 1s1 1 2 1c1 1 1 0 2 1 2 1 3 1 4 1s2 1 3 2c-2 0-4 0-7 1h-1 0c1 1 2 1 3 2l1 1c-2 0-3-1-4-1h-2l-1 1c0-1-1-1-2 0h1v1h-2c1 1 3 2 4 3h0c0 3 3 2 0 5 1 1 4 3 4 4h0c-2 0-4-3-5-4-1 0-1 0-1 1h0c1 0 1 1 1 2 1 1 2 1 3 2l2 1v1 1c-1 0-2-1-3 0 1 1 2 1 1 2l1 1c-2 0-4 0-6-1l-1-2c0-1-1-2-2-3-1 0-3-1-4-1l-2 3c-2 1-4 1-6 2h-3-2v-58-31c0-4-1-8-1-12z" class="P"></path><path d="M718 177l-3 1-1-2c1-2 1-2 3-4 1 2 2 2 4 3h1c1-1 1-1 2-1-2 2-4 3-6 3z" class="F"></path><path d="M718 239c-1 0-2 0-3-1v-1c0-1 1-2 2-2h0l-1 2h1l2-2h2c2 0 3 0 4 1h1l-2 1c-2 1-4 2-6 2z" class="J"></path><path d="M721 215l2 2 1-1 1 2-3 2-7 2h0v-4l1-1h3l2-2z" class="Q"></path><path d="M715 260c0-1-1-2 0-3v-2-1l1-1c3 0 5 0 8 1v1h2l-2 3c-2 1-4 1-6 2h-3z" class="E"></path><path d="M715 260c0-1-1-2 0-3v-2-1l1-1c3 0 5 0 8 1v1l-1 1-2-2v1h-1-4v1c1 1 2 2 2 3v1h-3z" class="e"></path><path d="M716 233l-1-1v-1h2v-1h-2c0-1 0-1 1-1h0l-1-1v-3h0c5 0 10 2 14 4l1-1c2 1 3 1 5 2l8 3h-1v1c-3-1-5-2-7-2h-1c-2 0-3-1-4-2l-1 1h0v1c-2-1-3 0-5 0h-3l-1 1h-4z" class="G"></path><path d="M721 232h3c2 0 3-1 5 0v-1h0l1-1c1 1 2 2 4 2l1 1s1 1 2 1c1 1 1 0 2 1 2 1 3 1 4 1s2 1 3 2c-2 0-4 0-7 1h-1 0c1 1 2 1 3 2l1 1c-2 0-3-1-4-1h-2l-1 1c0-1-1-1-2 0h1v1h-2c1 1 3 2 4 3l-10-3c-1 0-3-1-5-1 0-1-2-1-3-1v-2c2 0 4-1 6-2l2-1h-1c-1-1-2-1-4-1-1-1-2-1-4-1h0l-1-1h4l1-1z" class="h"></path><path d="M731 236h1c-2 2-4 1-5 4-2-1-2-1-3-3l2-1c1 1 4 0 5 0z" class="R"></path><path d="M721 232c1 0 4 2 5 2s2 0 2-1c1 0 2 1 3 1v2c-1 0-4 1-5 0h-1c-1-1-2-1-4-1-1-1-2-1-4-1h0l-1-1h4l1-1z" class="W"></path><path d="M718 239c2 0 4-1 6-2 1 2 1 2 3 3 1 0 3 0 4 1h0c-2 1-3 1-5 0v2h0c-1 0-3-1-5-1 0-1-2-1-3-1v-2z" class="K"></path><path d="M735 233s1 1 2 1c1 1 1 0 2 1 2 1 3 1 4 1s2 1 3 2c-2 0-4 0-7 1h-1 0c1 1 2 1 3 2l1 1c-2 0-3-1-4-1h-2l-1 1 1-1c-2-1-2-1-5-1v-1c2 0 5-1 7-2l-1-1-2 1-1-1c1 0 2-1 2-1l-1-2z" class="G"></path><path d="M718 241c1 0 3 0 3 1 2 0 4 1 5 1l10 3h0c0 3 3 2 0 5 1 1 4 3 4 4h0c-2 0-4-3-5-4-1 0-1 0-1 1h0c1 0 1 1 1 2 1 1 2 1 3 2l2 1v1 1c-1 0-2-1-3 0 1 1 2 1 1 2l1 1c-2 0-4 0-6-1l-1-2c0-1-1-2-2-3-1 0-3-1-4-1h-2v-1h2c0-2-1-2-2-3l-2-1c-3-1-5-1-7-2v-1h1v-1c-1 0-1 0-1-1l1-1v-1h2 0l-1-2h1z" class="l"></path><path d="M724 248c2 0 3 1 5 3h0l-1 1-4-1-2-1 2-2z" class="E"></path><path d="M724 251l4 1 3 2-1 2c-1 0-3-1-4-1h-2v-1h2c0-2-1-2-2-3z" class="Z"></path><path d="M731 254c3 2 5 6 7 7l1 1c-2 0-4 0-6-1l-1-2c0-1-1-2-2-3l1-2z" class="o"></path><path d="M718 241c1 0 3 0 3 1 2 0 4 1 5 1l10 3h0c0 3 3 2 0 5 1 1 4 3 4 4h0c-2 0-4-3-5-4-3-2-8-4-12-5h-2v1l3 1-2 2c-3-1-5-1-7-2v-1h1v-1c-1 0-1 0-1-1l1-1v-1h2 0l-1-2h1z" class="D"></path><path d="M721 242c2 0 4 1 5 1l10 3h0c0 3 3 2 0 5l-2-2c-2-1-3-2-5-2-2-1-3-2-5-3l-3-1v-1z" class="J"></path><path d="M572 224l3-3c1-2 2-3 3-5 0-1 1-1 1-1l13 7 1 2h0c-1 1-1 1-1 2 0 0-1 1-2 1-1 1-1 0-2 2 1 1 2 1 3 2 1 0 2 0 2 1h0-1-1v-1h-6c1 1 1 1 1 2 3 0 5 2 7 3l1 1c5 2 8 6 10 11h-5l-1 1v1l1 1h1v1 4c-1 0-2 1-2 2h1 0c1-1 1 0 2-1 1 1 1 2 2 3l1 3v1c-1 0-3 0-3 1v1h-1 0c-3 1-6 0-9 1-1 1-1 1-3 1l2-2h-1c0-1 0-1-1-1l-1 1-4 1c-1 0-2 0-3 1h-1c-1 1-4 1-5 1v-1c1-1 2-2 3-2s0 0 1-1h-1l-3 2h-4c1-2 4-4 5-5l1 1 2-1h0c1-1 1-1 1-2h0v-1h-2 0 0l-1-1 1-2h0l3-2-1-1c-1 1-3 1-4 2l-1-1h-1 0c0-1 1-2 1-3h-1l2-2h-1c-1-1 0-1-1-1l-1-1h1v-1h-2c0-1 0-1 1-2s1-2 2-3h0c-1 0-2 0-3 1h-1-1-1v-2l2-1c0-1 1-1 2-2v-1h-2-1c1-3 4-4 5-6l-1-1-2 1-3 1 1-1c2-2 3-3 3-6z" class="Q"></path><path d="M593 246h1v2l-3 3h-1v-1l1-1c1-2 1-2 2-3zm4 15c2 0 2-1 4 1l-1 1h-4-4c2-1 3-2 5-2z" class="l"></path><path d="M579 259l1-2h2 0c2 0 2 0 4-1h2 1c-2 2-6 4-10 4v-1z" class="J"></path><path d="M576 263l-2 2h1 1 0l1-1h2v-1l1 1h0c2-1 5-2 7-2l-5 4 1 1c-1 0-2 0-3 1h-1c-1 1-4 1-5 1v-1c1-1 2-2 3-2s0 0 1-1h-1l-3 2h-4c1-2 4-4 5-5l1 1z" class="B"></path><path d="M586 233c3 0 5 2 7 3l1 1c5 2 8 6 10 11h-5-1v-1c1 0 1-1 2-1-1-2-3-2-5-3l1-2c-1 0-1-1-2-1-2-1 0-1 0-2-1-2-7-3-8-5z" class="X"></path><path d="M570 236h1c0-1 1-1 2-2l1 1 1 1v2l3-1 2 1c-1 1-1 1-2 1l-2 2c2 0 3-1 4-1 0 1-1 1-2 2l-1 2v1 1l1-1h1l1 1-2 2c0 1 1 1 2 2l2-1-2 2 1 1s1-1 2-1c1-1 2-1 3-2-1 2-3 3-5 5 0 1 0 1-1 1l-2 2-1-1h0l3-2-1-1c-1 1-3 1-4 2l-1-1h-1 0c0-1 1-2 1-3h-1l2-2h-1c-1-1 0-1-1-1l-1-1h1v-1h-2c0-1 0-1 1-2s1-2 2-3h0c-1 0-2 0-3 1h-1-1-1v-2l2-1c0-1 1-1 2-2v-1h-2z" class="E"></path><path d="M572 224l3-3c1-2 2-3 3-5 0-1 1-1 1-1l13 7 1 2h0c-1 1-1 1-1 2-2-1-4-2-6-1-1 1-2 3-4 3l-1 1-2 1h0c2 0 3 0 4-1v1c-2 2-5 3-7 5l-1 1-1-1-1-1c-1 1-2 1-2 2h-1-1c1-3 4-4 5-6l-1-1-2 1-3 1 1-1c2-2 3-3 3-6z" class="Y"></path><path d="M579 230h-2-1l5-4c2-1 2-1 5-1-1 1-2 3-4 3l-1 1-2 1z" class="l"></path><path d="M578 222l3-3c1 1 1 1 2 1l1 2c-1 1-4 3-6 3v-2-1z" class="d"></path><path d="M572 224l3-3c1-2 2-3 3-5 0-1 1-1 1-1l13 7-1 1c-2 0-3-2-5-3h-3c-1 0-1 0-2-1l-3 3-7 8 1-1h1l-2 1-3 1 1-1c2-2 3-3 3-6z" class="F"></path><path d="M713 585c0-1 1-4 2-4 2-2 5-3 8-5 1 4 2 7 3 10 2 8-1 17-5 24 0 1 0 2-1 3-3 2-9 5-14 3-2-1-3-4-4-6-1 2-4 7-5 7-2 1-10 14-12 16l-3 5h-1c0-1-1-2-1-3-3 4-8 9-10 14-1 2-2 3-4 4-1 2-2 3-4 4h0c-1 2-3 3-4 4-1 0-2-1-3 0h-1c1-1 1-1 1-2l2-3-1-2h0c2-2 3-3 4-5 0-1 3-3 4-4 2-2 4-4 6-7l6-6c1-3 4-5 5-7 6-8 12-16 16-24 1 0 2-1 2-2h1l1-1c1 1 1 0 1 1v1c2-2 4-6 6-8l-1-2c0-1 0-1 1-2l1 1c1-1 1-2 2-3 0 0 1-1 2-1z" class="D"></path><path d="M680 635l5-4h0v1 1l-3 5h-1c0-1-1-2-1-3z" class="V"></path><path d="M699 599h1l1-1c1 1 1 0 1 1v1c0 2-1 4-1 5-1 2-3 3-4 5-3 3-4 6-7 10 0 0 0 1-1 1 0 1 0 1-1 1-1 1-1 1-1 2s-1 2-2 3h0c-2 4-7 8-10 11l-2 2-1-1 2-1c0-1 1-2 1-2 2-2 3-4 5-6 1-1 2-3 3-4v-1l1-1 3-3v-1l-3 3-1 1v1l-4 4-3 3h0c1-3 4-5 5-7 6-8 12-16 16-24 1 0 2-1 2-2z" class="M"></path><path d="M676 632h0l3-3 4-4v-1l1-1 3-3v1l-3 3-1 1v1c-1 1-2 3-3 4-2 2-3 4-5 6 0 0-1 1-1 2l-2 1 1 1 2-2c-1 3-3 5-5 7 0 1-1 1-1 2v1h-1v1h0 2c-1 2-2 3-4 4-1 2-2 3-4 4h0c-1 2-3 3-4 4-1 0-2-1-3 0h-1c1-1 1-1 1-2l2-3-1-2h0c2-2 3-3 4-5 0-1 3-3 4-4 2-2 4-4 6-7l6-6z" class="q"></path><path d="M668 649h-1c-2 1-3 2-5 3v-1c1-1 3-4 5-5l1 2v1z" class="C"></path><path d="M713 585c0-1 1-4 2-4 2-2 5-3 8-5 1 4 2 7 3 10 2 8-1 17-5 24l-2 1c-4 4-9 3-13 3l-3-4v-1c0-1 1-2 1-3l5-8c1-4 2-9 4-13z" class="t"></path><path d="M642 216v-4c1 0 1 0 1 1 1 1 2 4 3 5 2-1 3-2 5-3l1-1c2-2 5-5 6-9 0-3 1-1 2-3l1-1v1c0 1-1 3-1 4l-1 1c1 0 1-1 2-1 6-5 13-10 20-15 1-1 3-1 5-2l5-4 4-2 1 1v4l-2 2c1 0 2-1 2 1v3l-3 2-21 17c-5 4-10 8-14 12-2 2-5 5-6 7 1 1 2 1 3 2l-3 4h-1l-1 1h-1v-1h-1l-2 7v1l-1-1c-1 0-1 0-2 1h-1c-1 2-1 3-2 5v2 4h-1l-2 2c0 1 0 1-1 2l2 1-1 1-4-3-1-2c-1 0-2-1-3-1-1-2-3-4-4-6v-1l1-1s0-1 1-2c0-1 0 0 1-1h0c1-2 1-2 2-3-1-1-1-2-1-3s0-1 1-2v-1c1-1 1-1 2-3l1-1 4-4c2-3 4-5 4-8l1-1v-4z" class="l"></path><path d="M649 228h0l1 5h1l1-1c1 1 2 1 3 2l-3 4h-1l-1 1h-1v-1h-1-1c0-3 0-6 2-9v-1z" class="L"></path><path d="M649 229c0 2 1 4 1 6 0 1 0 2 1 3l-1 1h-1v-1h-1-1c0-3 0-6 2-9z" class="W"></path><path d="M630 243l1-1 1 1-2 2h0c2-1 3-3 5-5 2-5 5-9 6-14v-2l1-1c0 2 0 3-1 6-1 4-3 8-5 12v2c-1 1-2 3-3 5s-3 4-3 6l-1 3c-1-2-3-4-4-6v-1l1-1s0-1 1-2c0-1 0 0 1-1h0c1-2 1-2 2-3z" class="D"></path><path d="M633 248c-1 2-1 3 0 5l3-3c0-1 0-1 1-1 0-1 0-1 1-2l-1-1h1c2-2 4-8 6-11-1 5-2 8-5 12l-1 3c0 2-1 4-1 6l-1 2s1 0 1 1 0 1-1 2l2 1-1 1-4-3-1-2c-1 0-2-1-3-1l1-3c0-2 2-4 3-6z" class="W"></path><path d="M638 250c0 2-1 4-1 6l-1 2s1 0 1 1 0 1-1 2l2 1-1 1-4-3-1-2c1 0 2-2 3-3 0 0 0-1 1-2 0-1 1-2 2-3z" class="Q"></path><path d="M649 228c-1-1-2-2-2-3s0-1-1-2h0l1-1h1l4-3c2-1 3-3 5-4 4-3 6-6 9-9 1-1 3-2 5-3 1-1 2-2 4-3h1c2-1 2-1 3-1-1 2-3 3-4 4s0 0-2 1l-2 2-1 1c-1 1 0 0-2 1l-4 4c0 1-1 2-2 3-3 3-6 5-8 8l-2 2h-1v1l-2 2h0z" class="c"></path><path d="M679 199c2-1 3-2 5-2h1 0c1-1 2-2 4-3v-1h3l1 3-21 17c-5 4-10 8-14 12-2 2-5 5-6 7l-1 1h-1l-1-5 2-2v-1h1l2-2c2-3 5-5 8-8 1-1 2-2 2-3l4-4c2-1 1 0 2-1l1-1 2-2c2-1 1 0 2-1s3-2 4-4z" class="r"></path><path d="M352 695c2 0 5-1 7 0 1 0 3 1 4 1l4 5h1 1 0 2c1-1 1-2 1-4l2 2h0l1-1h2l2 1 2 1c1 1 1 1 2 1l1 1 3 3-1 1 2 1v1l-1 1c-1 0-2-1-4-1 0-1 0-1-1-1l-1 1c0 2 1 3 0 4 1 1 2 3 3 4l-1 4c1 1 1 1 1 3h-1 4l1 1c-4 0-8 0-12-1h-2-1v2h0c-1 3 0 6 0 9-2 0-3-1-5-2-1 0-2 0-3-1h-2c-2-2-3-2-5-3-1 0-2-1-3-1v-1c-1-1-2-2-4-3-4-3-9-6-14-7l1-1-1-1v-2l-1-1-1 1h-1c-2 0-3 0-4-1-1 1-1 1-1 2l-2-1c-2 0-4 1-5 1l-1-1 1-1h-1v-1l2-1h1c2-1 4-2 6-2 1 0 3-1 4-2h2l2-2 1-5v-1c1-1 1-2 2-2 3 1 8 0 11 0z" class="D"></path><path d="M360 702c2 2 4 3 5 5-2 0-3 0-4-1 0-1 0-2-1-4z" class="T"></path><path d="M377 698l2 1 2 1c1 1 1 1 2 1l1 1 3 3-1 1h-3c-1-2-2-4-4-5-1-1-2-2-2-3z" class="R"></path><path d="M376 712v-2c-1-1-1-1-1-2s1-1 2-2l1-1h1v2c1 1 1 1 2 1 0 2 1 3 0 4l-1-1-2 1h-2z" class="B"></path><path d="M359 707l2-1c1 1 2 1 4 1 1 2 2 4 3 5h8 2c-1 1-1 2-3 2h-4l-1 2h-1-1c-2-1-4-3-5-5l-2-2-2-2z" class="K"></path><path d="M348 700h7l5 2c1 2 1 3 1 4l-2 1 2 2 2 2c-1 1-2 2-3 2l-1-1c-1 1 0 1-1 2-1-1-1-1-1-2h-3l1-2c-1 0-2 0-3-1 0 1-1 1-2 1l-1-1-1-1c-1-1-1-1-2-1h-1l-1-1c1-2 2-3 3-5l1-1z" class="T"></path><path d="M347 704h4 0l1 2h-2-1-3v-1l1-1z" class="f"></path><path d="M355 700l5 2c1 2 1 3 1 4l-2 1c0-1-1-1-2-1-1-1-2-1-3-2h-1c-1 0-1 0-2-1v-2l4-1z" class="g"></path><path d="M354 704c1 1 2 1 3 2 1 0 2 0 2 1l2 2 2 2c-1 1-2 2-3 2l-1-1c-1 1 0 1-1 2-1-1-1-1-1-2h-3l1-2c1-2 1-1 2-3-2-1-2 0-4 0l-1-2c1 0 1 0 1-1h1z" class="P"></path><path d="M359 712v-1l2-2 2 2c-1 1-2 2-3 2l-1-1z" class="T"></path><path d="M352 695c2 0 5-1 7 0 1 0 3 1 4 1l4 5c-1 1-2 1-3 2h-1c0-1 0-2-1-3-2-2-5-2-7-2h-6l-1 2-1 1c-1 2-2 3-3 5l1 1h1c1 0 1 0 2 1l1 1c-3 0-3 0-5 1-2 0-3 0-5 1h-3l-1 1h-1c-2 0-3 0-4-1-1 1-1 1-1 2l-2-1c-2 0-4 1-5 1l-1-1 1-1h-1v-1l2-1h1c2-1 4-2 6-2 1 0 3-1 4-2h2l2-2 1-5v-1c1-1 1-2 2-2 3 1 8 0 11 0z" class="W"></path><path d="M339 697c1-1 1-2 2-2 3 1 8 0 11 0 1 1 1 1 1 2-3 1-5 0-7 0-1 1-2 2-2 3-2 1-4 3-6 3l1-5v-1z" class="G"></path><path d="M344 706l1 1h1c1 0 1 0 2 1l1 1c-3 0-3 0-5 1-2 0-3 0-5 1h-3l-1 1h-1c-2 0-3 0-4-1-1 1-1 1-1 2l-2-1c-2 0-4 1-5 1l-1-1 1-1c7-2 14-2 21-4l1-1z" class="P"></path><path d="M336 711h3c2-1 3-1 5-1 2-1 2-1 5-1l1 1c1 0 2 0 2-1 1 1 2 1 3 1l-1 2h3c0 1 0 1 1 2 1-1 0-1 1-2l1 1c1 0 2-1 3-2 1 2 3 4 5 5h1 1l1-2h4c2 0 2-1 3-2l2-1 1 1c1 1 2 3 3 4l-1 4c1 1 1 1 1 3h-1 4l1 1c-4 0-8 0-12-1h-2-1v2h0c-1 3 0 6 0 9-2 0-3-1-5-2-1 0-2 0-3-1h-2c-2-2-3-2-5-3-1 0-2-1-3-1v-1c-1-1-2-2-4-3-4-3-9-6-14-7l1-1-1-1v-2l-1-1z" class="m"></path><path d="M363 711c1 2 3 4 5 5l-2 2c-1-1-2-2-3-2h-1c-1-1-2-1-2-1v-2c1 0 2-1 3-2z" class="a"></path><path d="M347 718l1-1-4-3-1-1h2c1 1 2 0 3 0 1 1 2 2 3 2 1 1 2 2 4 2l-3 1c0 1 1 1 2 2 0 0 1 1 1 2l-2-1-1-1h-2c-2 0-3-1-3-2z" class="h"></path><path d="M355 717v1l1-1c1 0 2 1 3 2l1-1c1 1 3 2 4 3h1v1 1h1l-2 2-1-1c-1 0-2 0-3-1l-1-1v1l1 1h-1l-1-1c-2 0-2 0-3-1 0-1-1-2-1-2-1-1-2-1-2-2l3-1z" class="X"></path><path d="M338 715c3 0 6 1 9 3 0 1 1 2 3 2h2l1 1 2 1c1 1 1 1 3 1l1 1h1l-1-1v-1l1 1c1 1 2 1 3 1l1 1 2-2h-1v-1h1c1-1 0-1 1-1s1 0 2 1h1v1c-1 0-1 0-1 1l1 1v1c2 1 2 1 2 3 0 1-1 2-1 3h-1-1-1c-1 0-2 0-3-1h-2c-2-2-3-2-5-3-1 0-2-1-3-1v-1c-1-1-2-2-4-3-4-3-9-6-14-7l1-1z" class="C"></path><path d="M358 728v-1h0l-2-2h1c1 1 2 1 3 2 3 0 3 1 6 1l3 3 1-1c-1-1-2-2-3-2l1-1c2 0 2 1 4 2h0c0 1-1 2-1 3h-1-1-1c-1 0-2 0-3-1h-2c-2-2-3-2-5-3z" class="D"></path><path d="M378 712l2-1 1 1c1 1 2 3 3 4l-1 4c1 1 1 1 1 3h-1 4l1 1c-4 0-8 0-12-1h-2-1v2h0c-1 3 0 6 0 9-2 0-3-1-5-2h1 1 1c0-1 1-2 1-3 0-2 0-2-2-3v-1l-1-1c0-1 0-1 1-1v-1h-1c-1-1-1-1-2-1s0 0-1 1h-1v-1c0-1 1-2 1-3l2-2h1 1l1-2h4c2 0 2-1 3-2z" class="Z"></path><path d="M378 712l2-1 1 1c1 1 2 3 3 4l-1 4c1 1 1 1 1 3h-1s-1 0-2-1h-2-1c0-2-1-4-2-5v1l1 2h-1l-2-2c-2-1-3-1-4-2l1-2h4c2 0 2-1 3-2z" class="g"></path><path d="M378 712l2-1 1 1c1 1 2 3 3 4l-1 4c-1-1-1-1-2-1h0c-1 0-1-1-1-1-1-1 0-4 0-6h-2z" class="O"></path><defs><linearGradient id="Q" x1="431.955" y1="107.697" x2="448.558" y2="94.271" xlink:href="#B"><stop offset="0" stop-color="#cfcdca"></stop><stop offset="1" stop-color="#fffcf4"></stop></linearGradient></defs><path fill="url(#Q)" d="M425 89v-4h0c-1-2-1-5-1-6-2-2 0-7-2-8v-3l1-1 1 1v1l3 3c1 2 3 2 4 4s3 3 4 5l3 3c2 1 3 3 4 5 2 2 5 4 6 6l2 2c1 2 1 2 2 3 0 1 1 1 1 2l1-1v1c1 0 1 1 1 1l2 5 1 1v1c0 1 1 2 1 2v1 2c0 2 1 5 1 7v8l-1 1v2h-2-1c-1 1-1 1-1 2l-1 3c0 1-1 1-2 2v2s0 1 1 2c-1 1-2 1-3 2h-2c-1-1-2-2-3-2l-2 2c-1 2-4 4-6 7v-2-4c2-9-2-17-3-26 0 0-3-19-3-22-2-2-3-9-4-12l1-1h0-1l-1-1v3l-1 1z"></path><path d="M453 126c1 0 1 1 1 2-1 1 0 3 0 5v1-1l1-2c0-1 0-2 1-2v-1c1-1 1-3 1-4 0-2 1-4 0-6h0c0 3 0 7 1 10v2l1 1v2h-2-1c-1 1-1 1-1 2l-1 3c-2-3-1-9-1-12z" class="Q"></path><path d="M431 99v1c1 2 1 5 2 8l1 11c1 1 1 2 1 3h1v-1c-1-4-1-8-2-13 2 3 3 7 4 10 0 1 1 2 1 3v-6s0 1 1 2c1 3 2 5 3 8h0c0-4-2-9-1-12l1 3c0 2 1 4 2 6v1l-1-1c0 4 1 8 0 11h-1v2h-1v-3l1-2c0-1 0-3-1-4v5h-1c0-2 0-4-1-5h0v4l-2-1h0v2h-1v-1c-1-1-1-1-1-2v-1-2h-1c0-2 0-2-1-3v-1s-3-19-3-22z" class="q"></path><path d="M440 101v-1c-1-1-1-3-2-4s-2-2-2-4v-1c1 2 1 1 3 2 1 1 2 3 3 5v-1l4 3c1 1 2 2 2 4h0c1 1 3 5 3 5v4h-1 0c-1 1-1 1-2 1h-1v2h-1c-1-1-1-1-1-2 0-2 0-3-1-4 0 2 0 4-1 6l-1-3v-1-2c0-1-1 0-2-1v-2-1-2c-1-1 0-2 0-3z" class="Y"></path><path d="M446 107l-1-3 1-1c1 3 1 6 3 8l1-1-2-6c1 1 3 5 3 5v4h-1 0c-1 1-1 1-2 1h-1v2h-1c-1-1-1-1-1-2l1-1c1-2 1-4 0-6z" class="E"></path><path d="M440 101v1c0 1 1 2 1 3h0l1-1c0-2-1-4-1-6 2 3 2 7 4 10v-1h1c1 2 1 4 0 6l-1 1c0-2 0-3-1-4 0 2 0 4-1 6l-1-3v-1-2c0-1-1 0-2-1v-2-1-2c-1-1 0-2 0-3z" class="Q"></path><path d="M443 116c1-2 1-4 1-6 1 1 1 2 1 4 0 1 0 1 1 2h1v-2h1c1 0 1 0 2-1h0 1v-4c2 5 3 11 2 15v2c0 3-1 9 1 12 0 1-1 1-2 2v2s0 1 1 2c-1 1-2 1-3 2h-2c-1-1-2-2-3-2l-2 2c-1 2-4 4-6 7v-2-4c2-9-2-17-3-26v1c1 1 1 1 1 3h1v2 1c0 1 0 1 1 2v1h1v-2h0l2 1v-4h0c1 1 1 3 1 5h1v-5c1 1 1 3 1 4l-1 2v3h1v-2h1c1-3 0-7 0-11l1 1v-1c-1-2-2-4-2-6z" class="k"></path><path d="M451 109c2 5 3 11 2 15l-1 2c-2 3-3 6-5 8-1 1-1 2-1 4h1 0l-2 2-2-1c0-3 0-4 2-6 1-1 1-1 2-3 0 0 1-1 1-2 1-1 1-2 0-3h1c0-1 1-2 1-2 0-2 0-2 1-3l1 1v-5l-1-3v-4z" class="i"></path><path d="M443 116c1-2 1-4 1-6 1 1 1 2 1 4 0 1 0 1 1 2h1v-2h1c1 0 1 0 2-1h0 1l1 3v5l-1-1c-1 1-1 1-1 3 0 0-1 1-1 2h-1l-1-2-1 3h0c0-1-1-2-1-3v-1c-1-2-2-4-2-6z" class="B"></path><path d="M445 122c1-1 1-2 1-3l1-1v3h1v-4c1 1 1 1 2 1v-1h1v3c-1 1-1 1-1 3 0 0-1 1-1 2h-1l-1-2-1 3h0c0-1-1-2-1-3v-1z" class="V"></path><path d="M452 126l1-2v2c0 3-1 9 1 12 0 1-1 1-2 2v2s0 1 1 2c-1 1-2 1-3 2h-2c-1-1-2-2-3-2 0-1 1-1 1-2 1-1 1-2 1-4h-1c0-2 0-3 1-4 2-2 3-5 5-8z" class="W"></path><path d="M451 137c0-1 0-1-1-1v-1l-1-1c1-2 1-2 3-3 0 2 0 3-1 5v1z" class="d"></path><path d="M452 126l1-2v2c0 3-1 9 1 12 0 1-1 1-2 2h0c0-2-1-2-1-3v-1c1-2 1-3 1-5h0v-5z" class="F"></path><path d="M447 134c1 1 1 2 2 4v1 5l-1 2c-1-1-2-2-3-2 0-1 1-1 1-2 1-1 1-2 1-4h-1c0-2 0-3 1-4z" class="I"></path><path d="M631 776c3 1 7 7 8 9 3 6 4 13 2 19l-1 3c-1 3-2 5-5 7-2 1-4 1-6 2h0-1-5l1 2c-3-1-6-1-9-1l-2-2-1 1v1 2h-2 1c2 2 4 2 7 3 0 3 0 6-1 9-2 0-4 0-6-1-4-1-10-3-14-3l1-17c-1 1-1 2-1 3h0l-2-12 1-1 1 1c1 1 1 1 2 1 3 0 5-3 7-4 1-1 1-3 2-4 0-1 1-2 1-3 2 0 2-1 3-2l4 1 2-2s-1-1-1-2l1-1h1c1 0 2 0 3-2 1-1 2-1 2-2v-1-2h2 0c0-1 0-1 1-2 2 0 2 0 4 1v-1z" class="t"></path><path d="M612 810c1 2 1 3 1 4v1l-1 1v1 2h-2l-1-1c-1-1-1-2 0-3 0-2 2-3 3-5z" class="k"></path><path d="M620 791l1-1a53.56 53.56 0 0 1-15 15l1-3v-2h1c2 0 2-1 3-2l2-2c1-1 2-3 3-4v-1c2 1 2 0 4 0z" class="R"></path><path d="M618 788h1c1 1 1 2 1 3-2 0-2 1-4 0v1c-1 1-2 3-3 4l-2 2v-2l-2 2h-1-2c1-1 1-3 2-4 0-1 1-2 1-3 2 0 2-1 3-2l4 1 2-2z" class="P"></path><path d="M611 796v-1h-1v-1h2c1 0 1-1 2-2h2c-1 1-2 3-3 4l-2 2v-2z" class="O"></path><path d="M611 796v2c-1 1-1 2-3 2h-1v2l-1 3-7 4-1 1c-1 1-1 2-1 3h0l-2-12 1-1 1 1c1 1 1 1 2 1 3 0 5-3 7-4h2 1l2-2z" class="b"></path><path d="M599 809c1-2 2-6 5-6 1-1 1 0 3-1l-1 3-7 4z" class="U"></path><path d="M631 776c3 1 7 7 8 9 3 6 4 13 2 19l-1 3c-1 3-2 5-5 7-2 1-4 1-6 2h0-1-5l1 2c-3-1-6-1-9-1l-2-2v-1c0-1 0-2-1-4 2 0 3 1 4 1 5 1 14 3 19 0 2-2 3-6 4-9 1-8-2-13-7-20l-3-1h-2l-6 9-1 1c0-1 0-2-1-3h-1s-1-1-1-2l1-1h1c1 0 2 0 3-2 1-1 2-1 2-2v-1-2h2 0c0-1 0-1 1-2 2 0 2 0 4 1v-1z" class="C"></path><path d="M612 810c2 0 3 1 4 1l-1 4c1 1 10 1 12 1h2-1-5l1 2c-3-1-6-1-9-1l-2-2v-1c0-1 0-2-1-4z" class="l"></path><path d="M631 776c3 1 7 7 8 9 3 6 4 13 2 19l-1 3c0-2 0-3 1-5 0-2 1-6 0-8v-1c-1-2-1-3-2-5l-1-1 1-1-2-2c0-1-1-1-1-1-1 0-3-3-4-4h0c-1-1-1-1-2-1l-1 1 3 3-3-1h-2l-6 9-1 1c0-1 0-2-1-3h-1s-1-1-1-2l1-1h1c1 0 2 0 3-2 1-1 2-1 2-2v-1-2h2 0c0-1 0-1 1-2 2 0 2 0 4 1v-1z" class="j"></path><path d="M619 788c1-1 2-3 3-3 2-1 3-3 4-4h1l-6 9-1 1c0-1 0-2-1-3z" class="U"></path><path d="M716 305l1-1c2 0 4 0 6-1 3 0 6-2 9-2h0 1c1 2 2 3 2 5h0c1 4-1 8-1 11l1-1c2-2 4-3 6-5-1 3-4 4-6 6l-2 2h2c1-1 2-2 3-2-2 2-4 4-6 7 0 3 0 4 1 6v1c-2 0-3 1-4 2l-5 4v-2c-2 1-4 1-7 1-1 1-9 0-12 0h-1-2-3-3-15l-2-1-1-1-2-6-1-2h2c3-1 5-2 7-3l1-1v-1h0v-3h4 0c2-1 3-2 4-2v-1c-1 1-1 1-2 1v-2h2v-1c1-1 1-1 2-1 2 0 1-3 2-4s1-1 2-1l2-1h0c1 0 2 0 3 1h1c2-1 3-1 4 0h4 1 0l2-2z" class="H"></path><path d="M716 322c1 0 1 0 3-1v1l1 1-1 1c0 1 0 2 1 3l-1 1-3-3v-3z" class="C"></path><path d="M713 307h1c0 3 0 5 1 7h0v3l1 1c0 2 0 2-1 4h1v3l3 3v1l-1 1v1c1 0 1 0 2 1v-1l1-1 1 4h1 1v1c-2 1-4 1-7 1-1-1-3 0-5-1 1-4 1-8 1-12 0-3 1-6 0-9v-3-1-3z" class="U"></path><path d="M716 325l3 3v1l-1 1v1c1 0 1 0 2 1v-1l1-1 1 4h-7v-2c0-1 0-1 1-3v-3-1z" class="e"></path><path d="M733 319h2c1-1 2-2 3-2-2 2-4 4-6 7 0 3 0 4 1 6v1c-2 0-3 1-4 2l-5 4v-2-1h-1-1l-1-4c1 0 2-2 2-3 2-1 3-2 5-4h0l5-4z" class="I"></path><path d="M728 323l1 1c0 3-4 4-5 7v1l-1-1v-1l-1 2 1 2h-1l-1-4c1 0 2-2 2-3 2-1 3-2 5-4z" class="B"></path><path d="M732 324c0 3 0 4 1 6v1c-2 0-3 1-4 2-1-1-1-1-2-1l-1-1 1-2 1-1c1-1 2-3 4-4z" class="F"></path><path d="M716 305l1-1c2 0 4 0 6-1 3 0 6-2 9-2h0 1c1 2 2 3 2 5h0c1 4-1 8-1 11l1-1c2-2 4-3 6-5-1 3-4 4-6 6l-2 2-5 4v-2c-2 0-3 4-6 4v-1h0l-2-1 1-2c1-5-1-9-3-13l-2-2v-1z" class="t"></path><path d="M732 301h1c1 2 2 3 2 5h0c1 4-1 8-1 11l1-1c2-2 4-3 6-5-1 3-4 4-6 6l-2 2-5 4v-2c-2 0-3 4-6 4v-1h0c2-1 5-4 7-6 3-5 4-11 3-17h0z" class="X"></path><path d="M701 306c1 0 2 0 3 1h1c2-1 3-1 4 0h4v3 1 3c1 3 0 6 0 9 0 4 0 8-1 12 2 1 4 0 5 1-1 1-9 0-12 0h-1-2-3v-13c1-2 1-4 1-6v-2c0-2 0-1 2-3-1-1-1-1-2-1-1-1 0-1-1-2v-2h0l2-1h0z" class="W"></path><path d="M702 336v-6c0-3 0-6 1-9 0-2-1-4 0-6l2 2c0 1 0 2-1 3-1 4 0 11 0 16h-2z" class="B"></path><path d="M701 306c1 0 2 0 3 1h1c2-1 3-1 4 0h4v3c-1 0-2 0-3-1v2h-3c-1 1 0 1-1 1-4-1-2-1-3-3 0-1-2-2-2-3z" class="F"></path><path d="M707 311l-2-2 1-1 4 1v2h-3z" class="c"></path><path d="M705 336v-5c2-2 0-10 1-13h0c0-1 0-1 1-2h2 2 0c1 2 0 4 1 6v1 1 3c-1 1 0 3 0 5-1 1-1 1-1 2l1 1c2 1 4 0 5 1-1 1-9 0-12 0zm-12-23c1-1 1-1 2-1 2 0 1-3 2-4s1-1 2-1h0v2c1 1 0 1 1 2 1 0 1 0 2 1-2 2-2 1-2 3v2c0 2 0 4-1 6v13h-3-15l-2-1-1-1-2-6-1-2h2c3-1 5-2 7-3l1-1v-1h0v-3h4 0c2-1 3-2 4-2v-1c-1 1-1 1-2 1v-2h2v-1z" class="F"></path><path d="M681 328l2-3 2-1v1c1 1 0 1 1 2-1 1-2 1-4 1h-1z" class="c"></path><path d="M684 323l2 1c-1 0-1 1-1 0l-2 1-2 3c-1-1-2-1-4 0h-1l-1-2h2c3-1 5-2 7-3z" class="J"></path><path d="M691 314h2v-1h3l-2 2 1 1c-3 2-6 4-10 6v-1h0v-3h4 0c2-1 3-2 4-2v-1c-1 1-1 1-2 1v-2z" class="k"></path><path d="M676 328h1c1 1 3 2 5 2 3 1 7 0 10-1h2c1 2 1 4 2 6v1h-15l-2-1-1-1-2-6z" class="s"></path><path d="M395 774l1 1h2 0c1 0 1 0 2-1h1v1l1-1 1 1 1 3c1 1 1 1 1 2 0 2 2 3 3 5 2 2 2 4 4 6v2c1 1 2 2 2 3 1 3 5 6 8 7h1l3 1-1 6h0-1c0 1-1 5 0 7v1l1 9c-7 1-13 4-20 4h0l-3-8c-4 0-11 0-14-2v-1-1c1-1 1-1 2-1s1 0 2-1v-1h-2c-2-1-4-1-5-2-5-4-6-11-6-16 0-9 4-16 10-21h2v-1l4-2z" class="t"></path><path d="M404 795c0-1 0-2 1-3h5 0 0v2c0 1 1 1 2 2l1-1c-1-1 0-1-1-2h0c1 1 2 2 2 3 1 3 5 6 8 7h1l3 1-1 6h0-1c-8-5-13-8-20-15z" class="O"></path><path d="M406 813h1v-1l1-1c1 0 3 1 4 2 1 2 1 3 0 5-2 2-6 3-9 4l2 9h0l-3-8c-4 0-11 0-14-2v-1-1c1-1 1-1 2-1s1 0 2-1v-1c3 0 7 0 11-2h1 1l1-1z" class="m"></path><path d="M406 813h1v-1l1-1v3l1 2c-2 2-3 2-5 3-5 1-10 1-14 2l-2-1v-1c1-1 1-1 2-1s1 0 2-1v-1c3 0 7 0 11-2h1 1l1-1z" class="l"></path><path d="M395 774l1 1h2 0c1 0 1 0 2-1h1v1l1-1 1 1 1 3c1 1 1 1 1 2 0 2 2 3 3 5 2 2 2 4 4 6v2h0c1 1 0 1 1 2l-1 1c-1-1-2-1-2-2v-2h0 0-5c-1 1-1 2-1 3-4-4-7-8-9-13l-2-1 3-3c-2-1-3-1-5-1v-1l4-2z" class="P"></path><path d="M395 774l1 1h2 0l1 3 1-1v1h-1l-1 1h1v1h-1l-2 2h-1l-2-1 3-3c-2-1-3-1-5-1v-1l4-2z" class="M"></path><path d="M389 777h2c2 0 3 0 5 1l-3 3c-5 4-8 7-10 13v1c-1 3 0 7 0 11h1c0 3 1 5 4 6 2 1 6 1 9 1 2-1 4-3 7-3 1 1 1 1 2 3l-1 1h-1-1c-4 2-8 2-11 2h-2c-2-1-4-1-5-2-5-4-6-11-6-16 0-9 4-16 10-21z" class="h"></path><path d="M390 816c2-1 5-2 7-2 1 0 2 0 3-1s1-1 2-1v1l1 1c-4 2-8 2-11 2h-2z" class="V"></path><path d="M512 155c2-1 3-1 5-1 0 1 1 1 1 2h0c-2 1-4 2-5 4 0 1-1 2 0 3h1s1 1 2 1l2-2h1v1c-1 1-1 1-3 1v1h1 2v1l1-1h2c1 0 2 0 2-1v-1h-1c1-2 2-3 3-4l2-2h0c-1 0-1 0-1-1 1 0 1-1 2-1l1 2c1 1 2 2 2 4v2h1l1 2c0 3-1 5-2 7-2 2-3 5-5 6s-3 2-4 3c-2 1-3 1-5 3-3 1-6 3-7 6h-1c-7 5-12 12-18 17-1 4-4 6-6 9l-2 2c-2 2-4 4-7 6h0l-3 1-1 2-9 3-2 1-2 1c0-1-1-1-1-1 0-1 0-1 1-2h0l-3 1-1-1v-1-1c3-1 7-4 10-6-5 1-9 3-13 5-2 0-4 1-6 1l-5 1c2-1 2-1 4-1 1-1 2-1 3-1h1c1-1 1-1 2-1h1 1 1c1-1 2-2 3-2 1-1 3-1 4-2 1 0 0 0 1-1h0v-1c1 1 2 0 4 0l3-2 1-1 5-3c3-2 4-7 6-10 1-1 3-3 3-5 0-1 1-2 1-3 1-1 1-1 1-2l-1 1-4 5h-1c0-2 2-4 3-5h1l1-5v-7l2-4c1-2 0-2 2-4h0c1-3 1-5 1-8h0v9l1-3c1-2 2-4 4-5 2-2 6-2 8-3v-2h0-4c-1 0-2 1-3 0v-1s1 0 2-1c1 0 1-1 2-1 3 0 4-1 7-2 1-1 2-1 4-2h0z" class="G"></path><path d="M469 226l1-1c1 0 2-1 2-1 1 0 1 0 2-1h1c4-2 8-7 12-10h0c-2 2-2 3-3 5-2 2-4 4-7 6h0l-3 1-5 1z" class="J"></path><path d="M458 228c1-1 2-2 4-2s3-1 4-2c4-2 5-3 8-5h0 0c-1 1-2 3-3 4-2 1-3 3-5 4h-1v1l2-1s1 0 2-1l5-1-1 2-9 3-2 1-2 1c0-1-1-1-1-1 0-1 0-1 1-2h0l-3 1-1-1v-1h2z" class="E"></path><path d="M458 228h0c2 0 3 0 4-1v1 1l2 1-2 1-2 1c0-1-1-1-1-1 0-1 0-1 1-2h0l-3 1-1-1v-1h2z" class="B"></path><path d="M512 155c2-1 3-1 5-1 0 1 1 1 1 2h0c-2 1-4 2-5 4 0 1-1 2 0 3h1s1 1 2 1l-3 3v1h1c1-1 1-1 2-1v1c0 1-1 0-1 1-1 0-2 3-2 4l-9 11c0-3 0-6-2-8-1 0-1 0-2 1 0 2-1 4-1 6h-1v-3c-1 0-2 1-3 1l-5 6v2c-1-1-1-2-2-3l1-2c-1-2-1-4-1-6 1-2 0-2 2-4h0c1-3 1-5 1-8h0v9l1-3c1-2 2-4 4-5 2-2 6-2 8-3v-2h0-4c-1 0-2 1-3 0v-1s1 0 2-1c1 0 1-1 2-1 3 0 4-1 7-2 1-1 2-1 4-2h0z" class="g"></path><path d="M508 157c-1 2-2 2-1 4h1v1 1c-1 0-2 1-2 2h-1s-1 1-1 2h1c0 1 0 1-1 1l-8 8-2 1v-1c-1 0-1 1-1 1 0 2 0 3-1 4l1 1h0l3-3c1 0 2 0 3-1s-1-1 1-1c0 2-1 4-1 6h-1v-3c-1 0-2 1-3 1l-5 6v2c-1-1-1-2-2-3l1-2c-1-2-1-4-1-6 1-2 0-2 2-4h0c1-3 1-5 1-8h0v9l1-3c1-2 2-4 4-5 2-2 6-2 8-3v-2h0-4c-1 0-2 1-3 0v-1s1 0 2-1c1 0 1-1 2-1 3 0 4-1 7-2z" class="P"></path><path d="M488 178c1-2 0-2 2-4 0 4-1 10 0 13v2c-1-1-1-2-2-3l1-2c-1-2-1-4-1-6z" class="Q"></path><path d="M496 176h-1v-4l3-3c2 0 5-1 6-1l-8 8z" class="I"></path><defs><linearGradient id="R" x1="508.005" y1="169.627" x2="518.099" y2="189.579" xlink:href="#B"><stop offset="0" stop-color="#d0ccca"></stop><stop offset="1" stop-color="#f5f5ee"></stop></linearGradient></defs><path fill="url(#R)" d="M519 165v1l1-1h2c1 0 2 0 2-1v-1h-1c1-2 2-3 3-4l2-2h0c-1 0-1 0-1-1 1 0 1-1 2-1l1 2c1 1 2 2 2 4v2h1l1 2c0 3-1 5-2 7-2 2-3 5-5 6s-3 2-4 3c-2 1-3 1-5 3-3 1-6 3-7 6h-1c-7 5-12 12-18 17 1-1 1-1 1-2h1c0-2 1-2 2-3-1-3 4-8 6-10l1-3 2-2 2-4 1-1 3-3v-1c2-3 5-6 7-9-2 1-3 2-5 4 0-1 1-4 2-4 0-1 1 0 1-1v-1c-1 0-1 0-2 1h-1v-1l3-3 2-2h1v1c-1 1-1 1-3 1v1h1 2z"></path><path d="M530 166c0 2 1 2 0 4l-3 6-1-1c0-2 1-3 1-5 1-1 2-2 3-4z" class="E"></path><path d="M529 161c0 1 0 2 1 3 1 0 1 0 1 1 0 0 0 1-1 1-1 2-2 3-3 4h-2c2-3 3-6 4-9z" class="J"></path><path d="M519 165v1l1-1h2c1 0 2 0 2-1v-1h-1c1-2 2-3 3-4l2-2h0c-1 0-1 0-1-1 1 0 1-1 2-1l1 2-1 4c-1 3-2 6-4 9-2 4-5 8-9 10-3 2-4 4-6 5h-1l-3 2h-1l2-4 1-1 3-3v-1c2-3 5-6 7-9-2 1-3 2-5 4 0-1 1-4 2-4 0-1 1 0 1-1v-1c-1 0-1 0-2 1h-1v-1l3-3 2-2h1v1c-1 1-1 1-3 1v1h1 2z" class="P"></path><path d="M518 173c1-3 3-4 5-6h1c0 2-1 4-3 6l-1 1v2l-1-1v-1h0l-1 1h-1l1-2z" class="O"></path><path d="M511 179c1-1 2-3 3-4h1c1-1 2-2 3-2l-1 2h1l1-1h0v1l-3 5c-3 2-4 4-6 5h-1l-3 2h-1l2-4 1-1 3-3z" class="l"></path><path d="M322 551h9 1c1 0 2 0 4 1h0l8-1h3 3c3 0 5 1 8 0l1 1v2c0 2 0 3-1 5h-2l1 2v11h0c-3 1-4 1-7 1l1 1h0 1l1 1c0 1 0 2 1 3l-3 3h-1c-3 2-8 2-11 4l-1 1h-6l-1 1v-2h-3 0v2h-2l-1 1-1-1v1l-1 2-4-5-21-8c-1-1-2-1-3-2-1-2-2-4-3-5 0-2 0-3-1-4v-10c1 1 1 1 3 1l5 1 4-1c1-1 2-1 3-1h0c1-1 2-1 2-1l2-2h0l2-2c1 1 3 1 5 1h0c2 0 3 0 5-1z" class="E"></path><path d="M310 553l2-2c1 1 3 1 5 1 1 0 2 0 2 1-1 2-2 5-4 6h-1c0-1-1-1-2-1h-1l-1-2-2-1 2-2h0z" class="O"></path><g class="R"><path d="M322 551h9 1c1 0 2 0 4 1h0c0 1 0 0 1 2v2 1c0 1 0 1 1 2v1c0 1 0 1 1 2-1 1-1 1-2 1v1h1l-2 3h2c-1 1-1 1-2 0s-1-1-2-1v-1c1-2 1-6 0-7-1-2-2-3-4-4-4-2-7-2-11-1 0-1-1-1-2-1h0c2 0 3 0 5-1z"></path><path d="M319 561v-2c1-2 2-3 4-3 1-1 2-1 4 0 1 0 2 1 3 2v6l1 1h3v1l-1 6-2 13h-3 0v2h-2c-1-1-1-5-2-7l-3-10v-3c-1-1-1-4-2-6z"></path></g><path d="M324 560v-1c1-1 2-1 3-1 0 2 1 2 0 4-1 0-2-1-3-2z" class="K"></path><path d="M330 572l-1 3h-4c-1-1-1-1-1-2 1-1 1-1 2-1h3 1z" class="M"></path><path d="M324 580h0c1-2 2-2 3-4v2h1l1-1c0 1 0 1-1 2v6 2h-2c-1-1-1-5-2-7z" class="S"></path><path d="M319 561v-2c1-2 2-3 4-3 1-1 2-1 4 0v2c-1 0-2 0-3 1v1l-1 2-4-1z" class="T"></path><path d="M330 571c1 0 2 1 3 1l-2 13h-3 0v-6c1-1 1-1 1-2v-2l1-3v-1z" class="G"></path><path d="M330 571c0-1 0-1-1-2-2 0-3 1-5 1-1-1-2-1-2-2l1-1c0 1 0 1 1 2 0-1 0-2-1-3h-1v-1l2-2c1 1 1 2 3 3 1 0 0 0 1-1h-1v-1h3l1 1h3v1l-1 6c-1 0-2-1-3-1z" class="F"></path><path d="M306 556c1-1 2-1 2-1l2 1 1 2h1c1 0 2 0 2 1-1 0-2 1-3 2 0 1 0 3 1 4v3l1 2h-1c1 1 2 2 3 2l2-2h4l3 10c1 2 1 6 2 7l-1 1-1-1v1l-1 2-4-5-21-8c-1-1-2-1-3-2-1-2-2-4-3-5 0-2 0-3-1-4v-10c1 1 1 1 3 1l5 1 4-1c1-1 2-1 3-1h0z" class="J"></path><path d="M299 558l4-1c0 1 0 2-1 3l-1 1c-2-1-2-2-2-3z" class="Q"></path><path d="M302 560c1 1 2 1 3 2 1 2 2 4 2 6h0l-1 1-1-2c0-1 0-2-1-2-1-2-2-3-2-5z" class="W"></path><path d="M293 567v-6h2l-1-2h1c1 0 2 1 3 1-1 2 0 2 0 3-1 0-1-1-2 0 0 1 0 1 1 2l-1 1c-1 0-2 0-3 1z" class="G"></path><path d="M306 556c2 3 2 8 2 12h0-1c0-2-1-4-2-6-1-1-2-1-3-2 1-1 1-2 1-3 1-1 2-1 3-1h0z" class="l"></path><path d="M306 556c0 1 0 2-1 3v3c-1-1-2-1-3-2 1-1 1-2 1-3 1-1 2-1 3-1z" class="J"></path><path d="M298 560c2 3 3 7 5 10 2 2 3 2 3 4-2 0-3-1-4-2-1-2-5-1-6-3h-1-2 0l-1 1c0-2 0-3-1-4h1c0 1 0 2 1 3v-2c1-1 2-1 3-1l1-1c-1-1-1-1-1-2 1-1 1 0 2 0 0-1-1-1 0-3z" class="H"></path><path d="M298 560c2 3 3 7 5 10-1 0-2 0-3-1l-3-3 1-1 1 1h1l-1-2-1-1c0-1-1-1 0-3z" class="C"></path><path d="M310 556l1 2h1c1 0 2 0 2 1-1 0-2 1-3 2 0 1 0 3 1 4v3l1 2h-1c1 1 2 2 3 2l2-2h4l3 10c1 2 1 6 2 7l-1 1-1-1v1l-1 2-4-5-21-8c-1-1-2-1-3-2-1-2-2-4-3-5l1-1h0 2 1c1 2 5 1 6 3 1 1 2 2 4 2 0 1 1 2 2 3 0 1 2 2 3 3h-1v1h1l9 3-5-6h1v-2h-2c-1-1-2-2-2-4-2-3-3-12-2-16z" class="M"></path><path d="M293 569h0 2 1c1 2 5 1 6 3 1 1 2 2 4 2 0 1 1 2 2 3-3 0-6-1-8-3-1 0-2-2-4-2h0c-2 0-2-1-3-3z" class="C"></path><path d="M315 572l2-2h4l3 10c1 2 1 6 2 7l-1 1-1-1v1c-2-2-3-8-4-11-1-1 0-3-1-5h-4z" class="D"></path><path d="M347 551h3c3 0 5 1 8 0l1 1v2c0 2 0 3-1 5h-2l1 2v11h0c-3 1-4 1-7 1l1 1h0 1l1 1c0 1 0 2 1 3l-3 3h-1c-3 2-8 2-11 4l-1 1h-6l-1 1v-2l2-13 1-6c1 0 1 0 2 1s1 1 2 0h-2l2-3h-1v-1c1 0 1 0 2-1-1-1-1-1-1-2v-1c-1-1-1-1-1-2v-1-2c-1-2-1-1-1-2l8-1h3z" class="U"></path><path d="M345 573v-3l-1-1 1-1c1 0 2-2 3-2 0 2 1 3 0 5l-3 2z" class="R"></path><path d="M351 559c2 0 3 0 4 1v2h1c0-1 0-1 1-1v11h0c-3 1-4 1-7 1-1 0-2 0-4 1-1 0 0 0-1 1h0-1l1-2 3-2c1-2 0-3 0-5l1-1h0l-1-1-1 1v-1l3-1c1-1 1-3 1-4z" class="d"></path><path d="M349 565h1 0l1 1c1-1 2-3 4-4l1 2c-1 1-1 2-2 2-1 1-3 1-5 1v-2h0z" class="l"></path><path d="M342 562c0 1 1 1 2 1l1 1-1 2h1v1c-2 2-3 4-3 6-1 2-1 4-2 6s-2 4-1 6l-1 1h-6l-1 1v-2l2-13 1-6c1 0 1 0 2 1s1 1 2 0h-2l2-3c2 0 2 1 3 0l1-2z" class="X"></path><path d="M333 579c1 1 1 2 1 3 1-1 2-2 3-2l1-1h2c-1 2-2 4-1 6l-1 1h-6c0-2 0-5 1-7z" class="h"></path><path d="M334 566c1 0 1 0 2 1s1 1 2 0h1v1l-2 1v1h0l3 1-2 2c-1-1-1-1 0-2h-1c-1 1-1 1-1 2l-1-1-1 1 1 1 1 2c-2 1-2 1-3 3s-1 5-1 7l-1 1v-2l2-13 1-6z" class="U"></path><path d="M342 562c0 1 1 1 2 1l1 1-1 2h1v1c-2 2-3 4-3 6l-2-2h0l-3-1h0v-1l2-1v-1h-1-2l2-3c2 0 2 1 3 0l1-2z" class="D"></path><path d="M347 551h3c3 0 5 1 8 0l1 1v2c0 2 0 3-1 5h-2l1 2c-1 0-1 0-1 1h-1v-2c-1-1-2-1-4-1 0 1 0 3-1 4l-3 1h-2l-1-1c-1 0-2 0-2-1l-1 2c-1 1-1 0-3 0h-1v-1c1 0 1 0 2-1-1-1-1-1-1-2v-1c-1-1-1-1-1-2v-1-2c-1-2-1-1-1-2l8-1h3z" class="Y"></path><path d="M338 559c0-3 1-4 4-5-1 1-2 2-2 3v1l1-2 1 1v-1c-1 2-1 3-1 5l1 1-1 2c-1 1-1 0-3 0h-1v-1c1 0 1 0 2-1-1-1-1-1-1-2v-1z" class="B"></path><path d="M342 556c2 0 3-1 5 0s3 2 4 3c0 1 0 3-1 4l-3 1h-2l-1-1c-1 0-2 0-2-1l-1-1c0-2 0-3 1-5z" class="f"></path><path d="M342 556c2 0 3-1 5 0h-2c-1 3-1 4-1 7h0c-1 0-2 0-2-1l-1-1c0-2 0-3 1-5z" class="T"></path><path d="M347 551h3c3 0 5 1 8 0l1 1v2c0 2 0 3-1 5h-2l-3-3-3-3c-2-1-6 0-8 1-3 1-4 2-4 5-1-1-1-1-1-2v-1-2c-1-2-1-1-1-2l8-1h3z" class="U"></path><path d="M353 556v-3-1h3c0 1 1 1 2 1l1 1c0 2 0 3-1 5h-2l-3-3z" class="f"></path><path d="M438 858c3-1 5 0 7 0l14 5 9 6c10 6 19 14 28 22 7 6 15 13 23 17 11 6 22 10 34 14l-1 1c-2-1-4-1-6-1-3 0-8 3-11 5-5 4-9 9-14 14l-2-1c-1 0-1-1-2 0-1 0 0 0-1 1-1-1-2-1-3-1v1c-1 2-2 3-2 5s1 6 0 7h-1c0-1 1-1 1-2l-1-1v-2c0-2 0-2-1-3v-1-1-1c0-2-1-4-2-5l-1-1c0-1 0-1-1-2 0 0-1 0-1-1 0 0-1-1-1-2l-2-2h-1l-3-3c-4-5-11-9-18-12h1 3 0v-2l1-1v-1c0-1 1-1 2-2l-6-2h0l-2 2h-2l-4 2c-1-1-1-1-1-2 2-3 6-8 6-12v-1c1-2 0-5 0-7h0v2c-1 2-1 3-2 4v-5c-1 2-1 4-2 6-1 1-2 1-3 2 0 2-1 3-2 4h0c1-3 1-6 1-9-1 1-1 2-2 2 0-6-2-10-6-15l-1-1h-3l-3 3c0-4 1-9 2-13l-6-3c-4-3-8-5-12-7z" class="s"></path><path d="M514 924c0-1 1-2 2-2 1-1 1 0 2-1v-1h1l2 2c-1 1 0 2-1 3l-2-1c-2 1-2 1-4 0h0z" class="U"></path><path d="M521 922l1-2v-1l1-1c2 0 1 1 3 1h3 0c-2 2-4 4-6 7h0v-1c-1-1-2-2-2-3z" class="Z"></path><path d="M507 916v-1c2-1 2-1 3-3v-3l1 1h0v3l2-2c1 2 1 5 0 8l-1 1v-1h-3 0c-1-1-1-2-2-3z" class="N"></path><path d="M469 891l1 1v1c1-1 1 0 1-1v-1c0-2 0-3 1-4h1 0c0-2 0-2 1-3l1 1v4c-1 2-1 4-2 6-1 1-2 1-3 2 0 2-1 3-2 4h0c1-3 1-6 1-9v-1z" class="K"></path><defs><linearGradient id="S" x1="466.176" y1="878.462" x2="463.889" y2="889.233" xlink:href="#B"><stop offset="0" stop-color="#1c1c1c"></stop><stop offset="1" stop-color="#353738"></stop></linearGradient></defs><path fill="url(#S)" d="M460 878c2-1 1-2 2-3h0c1 2 3 3 4 4s1 3 1 4c1 3 2 5 2 8v1c-1 1-1 2-2 2 0-6-2-10-6-15l-1-1z"></path><path d="M477 895l1 2 1-1v-5h1v5c-1 1-1 2-1 3l-1 1 1 2-1 2h-1v1l3 1h0l-2 2h-2l-4 2c-1-1-1-1-1-2 2-3 6-8 6-12v-1z" class="R"></path><path d="M521 922h0c0 1 1 2 2 3v1l-1 1-2 2c0 1 0 1-1 2l-2-1h-1v2l-5-3c1-2 2-3 2-4l1-1c2 1 2 1 4 0l2 1c1-1 0-2 1-3z" class="N"></path><path d="M521 922h0c0 1 1 2 2 3v1l-1 1h-1-2l-2-1h-1v1c-1-1-2-1-3-2l1-1c2 1 2 1 4 0l2 1c1-1 0-2 1-3z" class="o"></path><path d="M480 904l2-2h2c0-1 1-1 2-1h6l1 2c1 1 2 3 4 4h0 0c-1 2-2 2-3 3-2-1-3-2-4-2-2 0-2 0-3-1-2-1-5-2-7-3z" class="a"></path><path d="M486 901h6l1 2c1 1 2 3 4 4h0-3c0-1-1-1-1-2l-2-2v2h-1c0-2 0-2-2-3l-1 1-1-2z" class="g"></path><path d="M523 926h0c1 2 2 2 3 4l3-3c3-1 4-1 6 0-5 4-9 9-14 14l-2-1c-1 0-1-1-2 0-1 0 0 0-1 1-1-1-2-1-3-1 1-2 2-4 3-5l1-1c1-1 1-2 2-3s1-1 1-2l2-2 1-1z" class="P"></path><path d="M523 926h0c1 2 2 2 3 4-1 0-2 1-3 2l-2 1v1h0l2 1c-2 2-3 0-4 3l1 1-1 1c-1 0-1-1-2 0-1 0 0 0-1 1-1-1-2-1-3-1 1-2 2-4 3-5l1-1c1-1 1-2 2-3s1-1 1-2l2-2 1-1z" class="K"></path><path d="M516 941l-2-2 1-1h4l1 1-1 1c-1 0-1-1-2 0-1 0 0 0-1 1z" class="f"></path><path d="M480 906l-3-1v-1h1 2c2 1 5 2 7 3 1 1 1 1 3 1 1 0 2 1 4 2 1-1 2-1 3-3 1 1 1 1 2 1h0 1 0v1l1 1c0 1 0 1 1 2 0 0 1 1 1 2 0 0 0 1-1 1 1 1 1 1 3 1l2 5c0 1 1 2 1 2v-2-1l-1-2-1-1 1-1c1 1 1 2 2 3h0 3v1 4h0l-2 2 1 1c0-1 1-1 1-2 1 0 1-1 2-1h0l-1 1c0 1-1 2-2 4-1-1-2-3-3-4-1-2-2-3-4-3l1-1v-1l-3 1-1-1v-1-1l-1 1c-1-1-2-1-2-2h-1c-2-1-2-2-5-1l-2-1c-1-1-1-1-1-2l-1 1-1-1-1 1-1-1c0-1 1-1 1-2h0-2v-1c0-1 1-1 2-2l-6-2z" class="K"></path><path d="M512 924h-2c-1-1-2-3-2-5h1 3v1 4z" class="k"></path><path d="M486 908l6 3h1c1 1 2 1 2 3l2-1c1 1 2 2 3 2l1 1s1 1 2 1c1 1 1 1 2 3h0l-3 1-1-1v-1-1l-1 1c-1-1-2-1-2-2h-1c-2-1-2-2-5-1l-2-1c-1-1-1-1-1-2l-1 1-1-1-1 1-1-1c0-1 1-1 1-2h0-2v-1c0-1 1-1 2-2z" class="N"></path><path d="M486 908l6 3h1c-1 1-2 1-4 1-2 1 0-2-2-3l-1 2h-2v-1c0-1 1-1 2-2z" class="L"></path><path d="M486 911h0c0 1-1 1-1 2l1 1 1-1 1 1 1-1c0 1 0 1 1 2l2 1c3-1 3 0 5 1h1c0 1 1 1 2 2l1-1v1 1l1 1 3-1v1l-1 1c2 0 3 1 4 3 1 1 2 3 3 4l5 3v-2h1l2 1c-1 1-1 2-2 3l-1 1c-1 1-2 3-3 5v1c-1 2-2 3-2 5s1 6 0 7h-1c0-1 1-1 1-2l-1-1v-2c0-2 0-2-1-3v-1-1-1c0-2-1-4-2-5l-1-1c0-1 0-1-1-2 0 0-1 0-1-1 0 0-1-1-1-2l-2-2h-1l-3-3c-4-5-11-9-18-12h1 3 0v-2l1-1h2z" class="e"></path><path d="M516 935l-1-1h-2c-2-1-2-1-3 0l-2-1h-1c0-1 0-2 1-3 1 1 1 2 3 2h1c2 0 3 1 5 2l-1 1z" class="M"></path><path d="M492 916c3-1 3 0 5 1h1c0 1 1 1 2 2l1-1v1 1l1 1 3-1v1l-1 1c2 0 3 1 4 3 1 1 2 3 3 4l5 3v-2h1l2 1c-1 1-1 2-2 3-2-1-3-2-5-2h-1c-2 0-2-1-3-2v-1h-3v-1h-2v-1l2-1 1-1h-1l-1 1c-2-1-1 0-2 0s-2-1-2-1c-1 0-1 0-2-1l1-3h-1v1l-1-1-1-1c-2-2-3-2-4-4z" class="m"></path><path d="M499 921l1 1s1 1 2 1v1l-2 1c-1 0-1 0-2-1l1-3z" class="M"></path><path d="M728 181c1-1 3-1 5-2l11-4c4-2 33-10 34-10l-3 1v3s1 1 2 1c-2 1-4 1-5 2-1-1-1-2-2-1-7 2-13 2-19 5l-1 1h-2l-1 1c5 0 11-1 17-1l1 1h8v-1c1-1 3-1 4-1l1-1c3-1 7-1 9-1l1 1h2c-3 2-7 3-10 4l-3 1c0 1 1 1 2 1h2c1 0 0 0 1 1h5l3 1c1 0 3 0 5 1h1l8 2c1 1 1 1 2 1l7 4h0l3 3c-3 2-11-1-14 1-2 0-4-1-5 0h-1c-1 0-2 0-3 1h-2-1c-1 0-1 0-2 1h-1-3c-1 1-3 1-5 1h-1c-1 1-1 1-2 1l-5 1-3 1s-1 1-2 1l-4 1c-1 0-2 1-2 0-2 2-3 2-6 2s-8 3-11 4l-3 1-3 2-1 1-5 2-6 3-1-2-1 1-2-2-2 2h-3c0-1-1-1-1-2l-1-2 2-2-1-1c-1-3-1-7 0-10 0-1-1-1 0-2 0-1 0-1 1-3h-1c0-2-1-3 0-4 0-1 1-4 1-4 1-1 3-1 4-2l9-4h-1z" class="r"></path><path d="M784 188h1c2-1 10 0 12 1h-1c-2 0-5-1-8 0h-5c-2 1-4 0-5 1h-2c-1 0-2 1-3 1h-2-1c-2 1-2 1-3 1h-3l-1 1c-1 0-2 0-3-1h2c1-1 1-1 3-1h3c1-1 1-1 3-1h3c1-1 1-1 3-1h0c3 0 5-1 7-1zm-45 19c-1 0-4 0-5-1h-1-1c-1-1-1-1-1-2l1-1 1 1v1h1l2 1h0c1-1 2-1 3-1h2c1-1 5-1 7 0h0c1 0 1 0 2-1 3-1 6 0 9-1h1c-2 2-3 2-6 2s-8 3-11 4l1-1c-3 0-3 0-5-1z" class="d"></path><path d="M739 207c2-1 3-1 5-1v1 1c-3 0-3 0-5-1z" class="E"></path><defs><linearGradient id="T" x1="750.351" y1="191.535" x2="759.649" y2="176.465" xlink:href="#B"><stop offset="0" stop-color="#d3cdc9"></stop><stop offset="1" stop-color="#edf0e9"></stop></linearGradient></defs><path fill="url(#T)" d="M741 187c4-2 8-3 13-4 2 0 6-1 8-1 0-1 1-2 1-2h3l1 1h0v1h1c0 1-1 0 0 1 1 0 1 0 2 1h0-3-5l-13 2c-1 1-3 2-5 2-1 0-2-1-3-1z"></path><path d="M741 187c1 0 2 1 3 1 2 0 4-1 5-2l13-2h5c-8 1-16 3-24 5-2 1-3 1-5 2h-2l-1 1h-4c-3 1-5 3-8 3v-1c1-2 7-3 9-4 3-1 5-2 8-3h1z" class="B"></path><path d="M723 206c2-2 4-3 7-4 1-1 0-1 2-1h0l2 1h0l-1 2-1-1-1 1c0 1 0 1 1 2h1 1c1 1 4 1 5 1 2 1 2 1 5 1l-1 1-3 1c-1 0-3 0-4-1v-1c-3 0-5 1-7-1v-1h-3v1l2 1c-2 2-6 1-8 2h-1v-2l3-1 1-1z" class="l"></path><path d="M723 195c3 0 5-2 8-3h4l-1 1c-1 0-2 0-3 1-1 0-2 1-4 1l-3 3c-1 0-2 1-3 1 0 1-1 1-1 2h1c3 0 6-2 9-3l4-2h1l2-1h1 1l2-1h1c-1 1-1 1-3 1 0 1-1 1-2 1l-2 1h0c-1 1-2 1-4 1l-3 2-8 3-1 1v1c2 0 2 0 4 1l-1 1c-2-1-4 1-6 1h-1l1-4v-2c0-1 0-1 1-1 0-1 0-2 1-3h0c1-2 3-2 5-3z" class="c"></path><path d="M745 176c3 1 3 0 6 0l-1 1h-2l-1 1c5 0 11-1 17-1-2 2-6 1-9 2h-3c-3 1-6 1-8 3-4 1-8 3-11 4l-1 1 1 1v-1h2 1 3 0 1c-3 1-5 2-8 3h-6-2-1l2-1c4-2 6-4 8-7 1-1 2-1 3-2 2-2 7-3 9-4z" class="d"></path><path d="M719 210h1c2-1 6 0 8-2l-2-1v-1h3v1c2 2 4 1 7 1v1c1 1 3 1 4 1l-3 2-1 1-5 2-6 3-1-2-1 1-2-2-2 2h-3c0-1-1-1-1-2 1-1 1-1 2-3l-1-1 3-3v2z" class="r"></path><path d="M719 208v2l-1 2c2 1 4 0 7 0h2v1l-6 1v1l-2 2h-3c0-1-1-1-1-2 1-1 1-1 2-3l-1-1 3-3z" class="B"></path><path d="M736 209c1 1 3 1 4 1l-3 2-1 1-5 2-6 3-1-2-1 1-2-2v-1l6-1v-1l9-3z" class="c"></path><path d="M727 213h2c0 1-3 3-4 3h-1l-1 1-2-2v-1l6-1z" class="E"></path><path d="M728 181c1-1 3-1 5-2l11-4c4-2 33-10 34-10l-3 1v3s1 1 2 1c-2 1-4 1-5 2-1-1-1-2-2-1-7 2-13 2-19 5-3 0-3 1-6 0-2 1-7 2-9 4-1 1-2 1-3 2-2 3-4 5-8 7l-2 1h1 2 6c-2 1-8 2-9 4v1c-2 1-4 1-5 3h0c-1 1-1 2-1 3-1 0-1 0-1 1v2l-1 4h1c2 0 4-2 6-1l-3 1-3 3 1 1c-1 2-1 2-2 3l-1-2 2-2-1-1c-1-3-1-7 0-10 0-1-1-1 0-2 0-1 0-1 1-3h-1c0-2-1-3 0-4 0-1 1-4 1-4 1-1 3-1 4-2l9-4h-1z" class="q"></path><path d="M775 166v3s1 1 2 1c-2 1-4 1-5 2-1-1-1-2-2-1-7 2-13 2-19 5-3 0-3 1-6 0 3-1 5-2 7-2l12-4c4-1 8-2 11-4z" class="Q"></path><path d="M723 194c-1 0-2 1-4 1 0 0 0-1-1-1v-1c0-2 0-2 2-3v-2c1 0 2-1 3-1 3-2 6-4 10-5-2 3-4 5-8 7l-2 1h1 2 6c-2 1-8 2-9 4z" class="J"></path><path d="M685 633c2-2 10-15 12-16l-2 5c1 1 1 2 2 4l3 9c2 2 3 4 4 6 1 1 2 3 4 4 2 2 5 4 8 6-1 0-2 0-3 1-1 0-4-2-4-2 0 1 3 3 5 4 1 0 1 1 3 1 1 0 2 1 4 1-2 1-4 1-5 1 2 2 3 3 6 3 2 0 5 0 7 1h3 4 1 1c1 1 1 1 2 1 1 1 2 1 3 1v1c-5 2-9 2-14 2-2 0-5 0-7 1-6-2-12-1-17 2l-4 2-1 1-7 6-3 3c-3 2-5 4-7 7l-3 3v1c-1 1-2 1-3 2h-1v1h3c-1 1-4 1-6 1s-2 0-4-1c-2 0-2 0-4 1 0-2-1-3-3-4h-1l-2 1h-1c1-1 1-2 1-4h-1c-1-1-1-3-3-3l-1-1-1-1-2 1 1-4-2-1c-2 0-4-3-6-4l-1-1 5-3 6-6c1-2 3-3 4-5 1-1 3-2 4-4h0c2-1 3-2 4-4 2-1 3-2 4-4 2-5 7-10 10-14 0 1 1 2 1 3h1l3-5z" class="I"></path><path d="M691 667v-4c2-1 4 0 7-1h1c1 1 1 2 1 3l-1 1c-1-1-2-1-3-1-1 1-2 3-3 4h-1-1c1-1 1-2 2-2v-1l-2 1z" class="X"></path><path d="M690 681h-2c-1 0-2 1-3 1h-1 0c1-2 3-4 4-5 1 0 1-1 2-2 2 0 1 1 2 0 1-2 2-3 4-4 1 0 1-1 2-2 1 0 1-1 2-1s1-1 2-1c1-1 2-1 3-1v1s-1 1-2 1h0c-3 1-3 2-3 4l-7 6-3 3z" class="B"></path><path d="M703 655c1 1 1 2 2 3h3c1 1 1 1 1 2l2-1h4c1 0 1 1 2 0-1-1-2-1-2-2h0 1c2 2 3 3 6 3 2 0 5 0 7 1h3c-1 0-1 1-2 1-1 1-1 0-2 1v-1c-3 0-10 0-12 1h-1c-2 0-5 0-7 1-2 0-3-1-4 0h-4v1c0-1 0-2-1-3h-1c-3 1-5 0-7 1v4 1h-1v-1-2c-1 0-2 0-4 1l2 2c0 1-1 3-1 4v1h0c-1 0-1 0-1-1s0-2-1-3h0v-1h1c-1-1-1-2-2-2 0 0-1 0-1 1s-1 1-1 2h-5-1c0-1-1-1-2-2v1h-1v-1l-2 1h-2c0-2 1-1 2-2l-2-2v-1c-1 1-2 2-2 3-2-1-1-1-2-2l3-2c1-1 0-3 0-4l2-2c3 0 6 0 8-1h8 6 11z" class="H"></path><path d="M705 658h3c1 1 1 1 1 2l2-1h4c1 0 1 1 2 0-1-1-2-1-2-2h0 1c2 2 3 3 6 3h-2c-2 1-4 0-6 1h-4c-1 1-4 1-6 1l-1-1c1-1 1-1 1-2l1-1h0z" class="G"></path><path d="M703 655c1 1 1 2 2 3h0c-2 1-4 1-5 1l-12-1-1 2-1-1c-1 1-1 1-1 2l-2 2c0 1-3 1-4 1h0l-1 1-1 1v-1c-1 1-2 1-3 2v1h-1v-1l-2 1h-2c0-2 1-1 2-2l-2-2v-1c-1 1-2 2-2 3-2-1-1-1-2-2l3-2c1-1 0-3 0-4l2-2c3 0 6 0 8-1h8 6 11z" class="T"></path><path d="M669 663l2-1h0c1 0 1 0 1-1h2c1-1 2-2 3-2h2 0c2 0 2 0 4 1v-1c1-1 3-1 5-1l-1 2-1-1c-1 1-1 1-1 2l-2 2c0 1-3 1-4 1h0l-1 1-1 1v-1c-1 1-2 1-3 2v1h-1v-1l-2 1h-2c0-2 1-1 2-2l-2-2v-1z" class="n"></path><path d="M671 668l2-1v1h1v-1c1 1 2 1 2 2h1 5c2 1 2 1 3 3v3l-1 3h-2l-1 1-1-1v-2l-1-1c-2 0-3 2-5 3v1h0 2 0l-2 1 1 2c-1 1-1 0-1 2h2 0c1-1 2-2 4-2l-2 2h2v1c-1 1-2 1-4 1l-1 1c1 1 2 1 3 1l2 1c1-1 2-1 3-1l-3 3v1c-1 1-2 1-3 2h-1v1h3c-1 1-4 1-6 1s-2 0-4-1c-2 0-2 0-4 1 0-2-1-3-3-4h-1l-2 1h-1c1-1 1-2 1-4h-1c-1-1-1-3-3-3l-1-1-1-1-2 1 1-4-2-1c2 0 3 1 5 2 0-1 2-1 3-2h2l1-1h1l2-1 5-5 3-2h0-1v-3z" class="H"></path><path d="M671 668l2-1v1h1v-1c1 1 2 1 2 2h1 5c2 1 2 1 3 3v3l-3-3c-3 0-5 1-8 2l-2-3h0-1v-3z" class="U"></path><path d="M671 668l2-1v1h1v-1c1 1 2 1 2 2h1l-5 2h-1v-3z" class="C"></path><path d="M669 673l3-2 2 3c-2 1-4 3-5 5l-2 1v1c1 1 2 1 2 2l-1 1c1 1 1 1 2 1 1 1 1 0 1 1l-3-1c-1 0-2 0-3-1h-2c0-1-2-1-3-3l2-2 2-1 5-5z" class="b"></path><path d="M664 678v1c1 0 1 0 2 1 0 1-1 2-2 3 0 0 1 0 1 1h-2c0-1-2-1-3-3l2-2 2-1z" class="K"></path><path d="M650 680c2 0 3 1 5 2 0-1 2-1 3-2h2l1-1h1l-2 2c1 2 3 2 3 3h2c1 1 2 1 3 1l3 1v1h4c1 1 2 1 3 1l2 1c1-1 2-1 3-1l-3 3v1c-1 1-2 1-3 2h-1v1h3c-1 1-4 1-6 1s-2 0-4-1c-2 0-2 0-4 1 0-2-1-3-3-4h-1l-2 1h-1c1-1 1-2 1-4h-1c-1-1-1-3-3-3l-1-1-1-1-2 1 1-4-2-1z" class="I"></path><path d="M663 684h2c1 1 2 1 3 1l3 1v1c0 1-1 1-2 2-2-1-3-2-4-3l-2-2z" class="G"></path><path d="M671 687h4c1 1 2 1 3 1-1 1-2 2-4 3l-1-1c-1 0-2 0-4-1 1-1 2-1 2-2z" class="F"></path><path d="M678 688l2 1c1-1 2-1 3-1l-3 3v1c-1 1-2 1-3 2l-2-2h0-1v-1c2-1 3-2 4-3z" class="D"></path><path d="M675 692l2-2c1 0 2 1 3 2-1 1-2 1-3 2l-2-2z" class="F"></path><path d="M652 681c1 1 3 2 4 4 3 2 6 5 10 7h2c2 2 5 2 8 2v1h3c-1 1-4 1-6 1s-2 0-4-1c-2 0-2 0-4 1 0-2-1-3-3-4h-1l-2 1h-1c1-1 1-2 1-4h-1c-1-1-1-3-3-3l-1-1-1-1-2 1 1-4z" class="U"></path><path d="M670 649c2-5 7-10 10-14 0 1 1 2 1 3h1l2 1c-2 3-5 6-8 10-1 2-3 5-6 6v1l-2 2c0 1 1 3 0 4l-3 2c1 1 0 1 2 2 0-1 1-2 2-3v1l2 2c-1 1-2 0-2 2h2v3h1 0l-3 2-5 5-2 1h-1l-1 1h-2c-1 1-3 1-3 2-2-1-3-2-5-2s-4-3-6-4l-1-1 5-3 6-6c1-2 3-3 4-5 1-1 3-2 4-4h0c2-1 3-2 4-4 2-1 3-2 4-4z" class="g"></path><path d="M655 677v-1c0-1 1-1 2-2 1 0 0 0 1-1 0-1 2-2 2-2 1-1 0-2 1-3h1c1-1 1-2 1-3-1 0-1 0-2-1 2-1 3-2 4-3l3-3c0 1 1 3 0 4l-3 2c1 1 0 1 2 2l-2 2-1 1v1 1l-1-1h-1v2c-2 2-5 5-7 5z" class="O"></path><path d="M670 649c2-5 7-10 10-14 0 1 1 2 1 3h1l2 1c-2 3-5 6-8 10-1 2-3 5-6 6v-1c-4 2-6 6-9 7l-1 1c0 1-1 2-2 3l-2-1-2 2c1-2 3-3 4-5 1-1 3-2 4-4h0c2-1 3-2 4-4 2-1 3-2 4-4z" class="P"></path><path d="M670 649c2-5 7-10 10-14 0 1 1 2 1 3-7 10-16 18-25 26l-2 2c1-2 3-3 4-5 1-1 3-2 4-4h0c2-1 3-2 4-4 2-1 3-2 4-4z" class="C"></path><path d="M667 666c0-1 1-2 2-3v1l2 2c-1 1-2 0-2 2h2v3h1 0l-3 2-5 5-2 1h-1l-1 1h-2c-1 1-3 1-3 2-2-1-3-2-5-2s-4-3-6-4l-1-1 5-3c1 1 1 1 3 2 1 1 2 2 4 3 2 0 5-3 7-5v-2h1l1 1v-1-1l1-1 2-2z" class="M"></path><path d="M669 668h2v3h1 0l-3 2h0c0-1-1-2-1-3h0l1-2z" class="e"></path><path d="M648 672c1 1 1 1 3 2 0 1 1 2 0 3h-1c-1-1-1-1-2-1l-1-1c-1 1-2 1-3 1l-1-1 5-3z" class="P"></path><path d="M685 633c2-2 10-15 12-16l-2 5c1 1 1 2 2 4l3 9c2 2 3 4 4 6 1 1 2 3 4 4 2 2 5 4 8 6-1 0-2 0-3 1-1 0-4-2-4-2 0 1 3 3 5 4 1 0 1 1 3 1 1 0 2 1 4 1-2 1-4 1-5 1h-1 0c0 1 1 1 2 2-1 1-1 0-2 0h-4l-2 1c0-1 0-1-1-2h-3c-1-1-1-2-2-3h-11-6-8c-2 1-5 1-8 1v-1c3-1 5-4 6-6 3-4 6-7 8-10l-2-1 3-5z" class="F"></path><path d="M699 653l-1-5c2 1 3 2 4 3 1 2 2 2 1 3-2 0-2 1-4-1z" class="c"></path><path d="M692 655v-1-1-1c1-1 3-2 5-2l1 1v2 1l1-1c2 2 2 1 4 1v1h-11z" class="W"></path><path d="M709 650c-1-1-2-1-2-2-2-1-3-2-4-4-2-2-4-3-4-7h1l1 1c1 1 1 2 3 3h0c1 1 2 3 4 4 2 2 5 4 8 6-1 0-2 0-3 1-1 0-4-2-4-2z" class="c"></path><path d="M685 633c2-2 10-15 12-16l-2 5c1 1 1 2 2 4-1-1-1-1-2-1v1c-1 0-2 1-2 1l-3 4c1 4 2 7 3 11h-1c-1-2-1-3-1-5-1-1-2-1-2-2l-1-1c-1 1-1 0-1 1-1 1-2 2-3 4l-2-1 3-5z" class="U"></path><path d="M684 639c1-2 2-3 3-4 0-1 0 0 1-1l1 1c0 1 1 1 2 2-1 0-2 0-2 1-1 1-1 2-2 3v3c1 1 2 3 2 5h0c-2 0-2 0-3-1-1 1 0 3-2 4v-1h-3v1c1 0 1 0 2 1 1 0 2 0 3 1v1h-8c-2 1-5 1-8 1v-1c3-1 5-4 6-6 3-4 6-7 8-10z" class="m"></path><path d="M676 649c1 1 1 2 1 3h-2v1c1 0 1 1 2 2h1c-2 1-5 1-8 1v-1c3-1 5-4 6-6z" class="S"></path><path d="M677 655c1-1 2-3 3-3 2-2 3-3 4-6-1-1-1-1-1-2h2c0-2 1-3 1-5h0 1v1c-1 1-1 2-1 4h1c1 1 2 3 2 5h0c-2 0-2 0-3-1-1 1 0 3-2 4v-1h-3v1c1 0 1 0 2 1 1 0 2 0 3 1v1h-8-1z" class="G"></path><defs><linearGradient id="U" x1="534.22" y1="116.792" x2="547.133" y2="113.632" xlink:href="#B"><stop offset="0" stop-color="#a5a4a4"></stop><stop offset="1" stop-color="#cecdcb"></stop></linearGradient></defs><path fill="url(#U)" d="M531 66h0l2-2h1c0 2 1 2 1 4 0 1 0 2 1 3v1l6 21 1 3c1 1 2 3 2 4l2 7c1 1 3 5 3 6l2 5v4c-1 2-1 5-1 7v2c0 2 0 2-1 4v3l1 1s-1 1-1 2c-1 2-1 3-1 5l1 1c-1 2-1 4-2 6h0c-1-1-1-1-1-2-1-1 1-1-1-2l-2 2 1-6c-3 2-3 11-6 15 0 0 0 1-1 1 1-2 2-4 2-7 1-2 1-4 1-6v1c-1 1-2 2-2 3-2 2-2 5-3 8 0 2-1 3-2 5l-1-2h-1v-2c0-2-1-3-2-4l-1-2c-1 0-1 1-2 1 0 1 0 1 1 1h0l-2 2c-1 1-2 2-3 4h1v1c0 1-1 1-2 1h-2l-1 1v-1h-2-1v-1c2 0 2 0 3-1v-1h-1l-2 2c-1 0-2-1-2-1h-1c-1-1 0-2 0-3 1-2 3-3 5-4h0c0-1-1-1-1-2-2 0-3 0-5 1h0c-2 1-3 1-4 2-3 1-4 2-7 2-1 0-1 1-2 1-1 1-2 1-2 1-1-2 0-6-1-8v-4l-1-3v-2-5 1c1 1 1 2 1 3 1 0 2 0 3-1l1-2c-2-3-1-5 0-8v-2c2-1 4-9 5-11 4-9 8-19 13-27v-1l13-21v-1h0v-1-2z"></path><path d="M531 66h0l2-2h1c-1 1-1 2 0 3v2c0 1 0 1 1 2v3l1 1v1 2c-1 4 1 8 1 12v3h0l-1-1c0-2 0-4-1-6v-2h0l-1-1c-1-3-1-11-3-13v-1h0v-1-2z" class="l"></path><path d="M539 127v-11c1 2 1 5 1 8v14c1 2 0 4 1 6 3-2 3-12 3-16 1 2 1 2 1 4l-2 13h1c2-3 2-12 3-15 1-5 0-11 0-16h1v14c-1 6-1 11-3 17h0c-3 2-3 11-6 15 0 0 0 1-1 1 1-2 2-4 2-7 1-2 1-4 1-6v1c-1 1-2 2-2 3-1-1-1-1-1-3 1-1 1-3 1-4v-18z" class="h"></path><defs><linearGradient id="V" x1="539.379" y1="139.074" x2="556.763" y2="128.151" xlink:href="#B"><stop offset="0" stop-color="#a9a9a4"></stop><stop offset="1" stop-color="#d2d0d2"></stop></linearGradient></defs><path fill="url(#V)" d="M548 114l1-1h1l2 5v4c-1 2-1 5-1 7v2c0 2 0 2-1 4v3l1 1s-1 1-1 2c-1 2-1 3-1 5l1 1c-1 2-1 4-2 6h0c-1-1-1-1-1-2-1-1 1-1-1-2l-2 2 1-6h0c2-6 2-11 3-17v-14z"></path><defs><linearGradient id="W" x1="532.923" y1="114.383" x2="539.094" y2="113.547" xlink:href="#B"><stop offset="0" stop-color="#c5c5bf"></stop><stop offset="1" stop-color="#efeced"></stop></linearGradient></defs><path fill="url(#W)" d="M534 83l1 1h0v2c1 2 1 4 1 6l1 1h0l2 34v18l-3 1v-13-8c-1-2-2-4-4-5-1-1-1-1-1 0l-2-1v-1-1l1-1c2-1 2-1 3-3l-3-3-1-1v-1l1-1-2-2-1-1 2-1 1 1h1c1 1 3 1 4 2h1l-2-23z"></path><path d="M536 113v7l-1 1c-1-1-2-1-3-1-1-1-1-1-1 0l-2-1v-1-1l1-1c2-1 2-1 3-3 1 1 1 1 2 1l1-1z" class="L"></path><path d="M527 104l2-1 1 1h1c1 1 3 1 4 2h1v6 1l-1 1c-1 0-1 0-2-1l-3-3-1-1v-1l1-1-2-2-1-1z" class="n"></path><path d="M529 108c3 0 4 1 6 3l1 1v1l-1 1c-1 0-1 0-2-1l-3-3-1-1v-1z" class="U"></path><path d="M531 70c2 2 2 10 3 13l2 23h-1c-1-1-3-1-4-2h-1l-1-1-2 1 1 1c-3 2-5 3-7 5h-1c-1 1-2 1-2 2h-1l-3 3h-1-1l1-2h-2c0-1 1-2 1-2l1-1-1-2h0c1-1 1-2 1-4 1-3 5-9 5-12v-1l13-21z" class="C"></path><path d="M531 70c2 2 2 10 3 13l2 23h-1c-1-1-3-1-4-2v-1c1-2 2 0 4-1-2-1-2-2-3-3l1-1h1c-1-1-2-2-3-2l1-2c1 0 2-1 2-2h-1l-1-3 1 1v-1l-1-1c1-1 1-1 1-2h-2c1-1 1-1 1-2h-1 0c1-1 1-1 1-2v-3-1-2l-1-1v-3c-2 2-4 5-5 8l-7 11h-1l13-21z" class="R"></path><path d="M527 99h3c1-1 1-2 1-3 1 0 2 1 3 2h-1l-1 1c1 1 1 2 3 3-2 1-3-1-4 1v1h-1l-1-1-2 1c-2 2-4 3-7 5l-1 1c-1 0-2 1-2 1-1 1-2 1-3 1 1-1 1-2 2-2-1 0-1 0-2-1l2-2h-1l12-8h0z" class="H"></path><path d="M527 99h3c1-1 1-2 1-3 1 0 2 1 3 2h-1l-1 1c1 1 1 2 3 3-2 1-3-1-4 1v1h-1l-1-1c-1-1-1-1-1-2v-1c-4 2-8 4-12 7h-1l12-8h0z" class="M"></path><path d="M531 84h1c0 1 0 1-1 2h2c0 1 0 1-1 2l1 1v1l-1-1 1 3h1c0 1-1 2-2 2l-1 2c0 1 0 2-1 3h-3 0l-12 8h-1c0-1 0 0 1-1v-1c0-1 0-1 1-2 0-1 1-3 2-4 1-2 3-5 4-7 2-3 5-4 6-7h0l3-1z" class="D"></path><path d="M528 92v-1c-1 1-2 1-3 1h1c1-3 2-5 5-6h2c0 1 0 1-1 2l1 1v1l-1-1c-1 1-3 2-4 3z" class="C"></path><path d="M528 92c1-1 3-2 4-3l1 3h1c0 1-1 2-2 2l-1 2c0 1 0 2-1 3h-3 0v-1c-3-1-2-1-5 0l2-3c1-1 2-2 3-2l1-1z" class="i"></path><path d="M527 93c1 0 1 0 2-1v2c-1 0-2 0-2 1h0 3v1l-3 3h0v-1c-3-1-2-1-5 0l2-3c1-1 2-2 3-2z" class="V"></path><path d="M518 92c0 3-4 9-5 12 0 2 0 3-1 4h0l1 2-1 1s-1 1-1 2h2l-1 2h1 1l3-3h1c0-1 1-1 2-2h1c2-2 4-3 7-5l2 2-1 1v1l1 1 3 3c-1 2-1 2-3 3l-1 1v1 1 1 3c-1 1-1 1 0 2h1c0 1-1 2-1 2l1 4h-1-1v1c-1 1-3 2-5 3l-11 6v1l1 1h1-2v1l-1-1h-2 0-1c-2 1-3 1-5 0l-3-3c-2-3-1-5 0-8v-2c2-1 4-9 5-11 4-9 8-19 13-27z" class="q"></path><path d="M516 122c1-1 1-1 3-1h-1v1 1l-2 2v1h3c-2 1-4 3-6 4-1 0-2 1-3 1 2-2 2-3 4-5v-1l-2 1v-1c1-1 2-1 4-2v-1z" class="C"></path><path d="M525 127h1c0 1 0 2-1 3l-2 2-6 2-1 1c-2 0-3 1-5 2l-1-1 2-1c2-1 6-5 8-5 3 0 3-1 5-3z" class="k"></path><path d="M510 111c0-1 0-2 1-2 0-2 1-3 2-5 0 2 0 3-1 4h0l1 2-1 1s-1 1-1 2h2l-1 2h1 1l-2 2h2c-1 1-1 2-2 3 0 1 0 1-1 2v-1-1l-2 1v-1l2-2v-1l-2 1c0-2 0-3 1-4v-3z" class="h"></path><path d="M500 132h4v1h1l5-1h0l-3 2 2 1h0 1 2l-2 1 1 1c-3 2-6 3-8 6l-3-3c-2-3-1-5 0-8z" class="P"></path><path d="M507 134l2 1h0 1 2l-2 1h-1c-2 1-7 4-9 3v-1c2-2 5-3 7-4z" class="e"></path><path d="M518 92c0 3-4 9-5 12-1 2-2 3-2 5-1 0-1 1-1 2-1 2-2 5-2 6 0 2 0 1-1 3v1l-1 1 1 1h-2l-1 1c2 2 3 1 5 2-1 0-1 0-2 1-1 0-1 1-2 2v1c1 0 2 0 2-1 0 2-2 3-2 4h-1v-1h-4v-2c2-1 4-9 5-11 4-9 8-19 13-27z" class="Z"></path><path d="M523 132h0l-1 2h1v1l-11 6v1l1 1h1-2v1l-1-1h-2 0-1c-2 1-3 1-5 0 2-3 5-4 8-6 2-1 3-2 5-2l1-1 6-2z" class="q"></path><path d="M514 115l3-3h1c0-1 1-1 2-2h1c2-2 4-3 7-5l2 2-1 1v1l1 1 3 3c-1 2-1 2-3 3l-1 1v1 1 1 3c-1 1-1 1 0 2h1c0 1-1 2-1 2l1 4h-1-1v1c-1 1-3 2-5 3v-1h-1l1-2h0l2-2c1-1 1-2 1-3h-1v-1-1h-1c-2 1-3 1-5 1h-3v-1l2-2v-1-1h1c-2 0-2 0-3 1h-1-1-3c1-1 1-1 1-2 1-1 1-2 2-3h-2l2-2z" class="e"></path><path d="M524 122h3l1 2h0c-1 1 0 1 0 2-1 1-1 0-2 1h0-1v-1-1h-1-1v-1l-2 1v-1l3-2z" class="h"></path><path d="M525 110c1-1 3-1 4-2v1l1 1 3 3c-1 2-1 2-3 3l-1-2 1-1c-1 0-2-1-2-2h-1l-1-1h-1z" class="S"></path><path d="M518 122c2-1 4-2 5-3s1-1 2-1v1h1c-1 1-1 2-2 3l-3 2v1l2-1v1h1c-2 1-3 1-5 1h-3v-1l2-2v-1z" class="X"></path><path d="M514 115l3-3h1c0-1 1-1 2-2h1c2-2 4-3 7-5l2 2-1 1c-1 1-3 1-4 2l-3 3c1 0 2-1 3-1h0l-1 2c2 1 3-1 4 0 0 2-1 3-2 3l-1 1c-1 0-1 0-2 1s-3 2-5 3v-1h1c-2 0-2 0-3 1h-1-1-3c1-1 1-1 1-2 1-1 1-2 2-3h-2l2-2z" class="H"></path><path d="M514 115v1 1l2 1-1 2c1-1 2-1 3-2v1h0l-3 3h-1-3c1-1 1-1 1-2 1-1 1-2 2-3h-2l2-2z" class="C"></path><path d="M529 119l2 1c0-1 0-1 1 0 2 1 3 3 4 5v8 13l3-1c0 1 0 3-1 4 0 2 0 2 1 3-2 2-2 5-3 8 0 2-1 3-2 5l-1-2h-1v-2c0-2-1-3-2-4l-1-2c-1 0-1 1-2 1 0 1 0 1 1 1h0l-2 2c-1 1-2 2-3 4h1v1c0 1-1 1-2 1h-2l-1 1v-1h-2-1v-1c2 0 2 0 3-1v-1h-1l-2 2c-1 0-2-1-2-1h-1c-1-1 0-2 0-3 1-2 3-3 5-4h0c0-1-1-1-1-2-2 0-3 0-5 1h0c-2 1-3 1-4 2-3 1-4 2-7 2-1 0-1 1-2 1-1 1-2 1-2 1-1-2 0-6-1-8v-4l-1-3v-2-5 1c1 1 1 2 1 3 1 0 2 0 3-1l1-2 3 3c2 1 3 1 5 0h1 0 2l1 1v-1h2-1l-1-1v-1l11-6c2-1 4-2 5-3v-1h1 1l-1-4s1-1 1-2h-1c-1-1-1-1 0-2v-3-1z" class="N"></path><path d="M529 155h1v-3h0c1 2 3 5 3 7l-1 1 1 3h-1v-2c0-2-1-3-2-4l-1-2z" class="F"></path><path d="M531 120c0-1 0-1 1 0 2 1 3 3 4 5l-1 3s-1 0-2 1c-1-3-2-6-2-9z" class="P"></path><path d="M536 125v8 13 1h-1v-11-1c-1 0-1-1-2-1h-1c0-2-1-4-2-5l1-2h1c0 2 0 3 2 4h0l-1-2c1-1 2-1 2-1l1-3z" class="U"></path><path d="M539 145c0 1 0 3-1 4 0 2 0 2 1 3-2 2-2 5-3 8 0 2-1 3-2 5l-1-2-1-3 1-1 1 1c1-4 2-8 2-13v-1l3-1z" class="I"></path><path d="M523 147h0v4 1c-1 2-3 0-3 4 1 0 1 0 1-1 1-1 3-2 4-4h1c-1 3-4 2-2 5l-3 2h1c2 0 1-1 3-1 0-1 0 0 1 0-2 2-3 4-4 6h1l-1 1-1-1c-1 1-1 2-2 2h-2-1v-1c2 0 2 0 3-1v-1h-1l-2 2c-1 0-2-1-2-1h-1c-1-1 0-2 0-3 1-2 3-3 5-4h0c0-1-1-1-1-2-2 0-3 0-5 1l6-4c0-1 1-1 1-2l4-2z" class="K"></path><path d="M519 141h-1l2-2 1 1c1-1 2-1 3-2h2l-1-1h0c1-1 1-1 3-1 1 1 3 4 3 5 0 2 0 2 2 3v-2h1v1c-1 1-1 3 0 4v1h0c-1 0-2-1-2-2h-2-1c-2 1-4 3-5 5h1c-1 2-3 3-4 4 0 1 0 1-1 1 0-4 2-2 3-4v-1-4h0c1-1 1-2 2-2v-1c-1 1-1 1-2 1v1h-1c-1 0-2 0-3 1-1 0-2 0-2 1l-1-1 1-1h0c2-2 4-2 6-4l-1-1h-3z" class="j"></path><path d="M517 146h0l-1 1 1 1c0-1 1-1 2-1 1-1 2-1 3-1h1v-1c1 0 1 0 2-1v1c-1 0-1 1-2 2l-4 2c0 1-1 1-1 2l-6 4h0c-2 1-3 1-4 2-3 1-4 2-7 2-1 0-1 1-2 1-1 1-2 1-2 1-1-2 0-6-1-8v-4-1h1c1 2 2 2 2 4h0l2 1c1 0 2 0 4-1h-1c1-1 2-1 4-2 3-1 6-3 9-4z" class="i"></path><path d="M514 150c-1 2-2 3-4 3l-1 1h-1c-2 2-4 3-6 3-1 0-1-1-2-2h4v-1h-4-1l1-1c-2 0-2 0-2-1v-1l1 1 2 1c1 0 2 0 4-1 1 0 2-1 4 0 2-1 3-1 5-2z" class="H"></path><path d="M517 146h0l-1 1 1 1c0-1 1-1 2-1 1-1 2-1 3-1h1v-1c1 0 1 0 2-1v1c-1 0-1 1-2 2l-4 2c0 1-1 1-1 2l-6 4h0c-1-1-2-1-3-1l1-1c2 0 3-1 4-3-2 1-3 1-5 2-2-1-3 0-4 0h-1c1-1 2-1 4-2 3-1 6-3 9-4z" class="N"></path><path d="M514 150c1-1 1-1 3-2v1l-2 1c1 0 2 0 3 1l-6 4h0c-1-1-2-1-3-1l1-1c2 0 3-1 4-3z" class="M"></path><path d="M495 139v1c1 1 1 2 1 3 1 0 2 0 3-1l1-2 3 3c2 1 3 1 5 0h1 0 2l1 1v-1h2-1c2-1 5-1 6-2h3l1 1c-2 2-4 2-6 4-3 1-6 3-9 4-2 1-3 1-4 2h1c-2 1-3 1-4 1l-2-1h0c0-2-1-2-2-4h-1v1l-1-3v-2-5z" class="W"></path><path d="M495 144c2 0 3 0 4 1 1 0 2 0 3 1 3 1 6 1 8 1-2 2-6 3-9 3l-2-2h-2-1v1l-1-3v-2z" class="G"></path><path d="M519 141h3l1 1c-2 2-4 2-6 4-3 1-6 3-9 4-2 1-3 1-4 2h1c-2 1-3 1-4 1l-2-1h0c0-2-1-2-2-4h2l2 2c3 0 7-1 9-3 1 0 2-1 3-1 1-1 2-1 2-2l-1-1h-1c2-1 5-1 6-2z" class="C"></path><path d="M342 620l2-2 1 1 1 1v2h1c2 2 3 5 5 8 1 2 4 3 5 5 2 2 4 6 7 8 4 7 13 12 18 19 2 2 6 5 9 6 2 1 4 3 5 5 0 1 2 3 3 4h1c1 1 1 2 1 2l2 1c3 4 26 14 27 16l-18-8c-1 0-2 1-2 2v1l1 2c2 3 3 7 4 10l-1 1-3 3c-1 0-1 1-1 1h-1c-2-1-3 0-4 0l-1-1-1-1c-3 0-5-2-8-4-1-1-2-3-3-5l-2 2-3-2h-2l-1 1 1 2s-1 1-1 2l-1-1c-1 0-1 0-2-1l-2-1-2-1h-2l-1 1h0l-2-2c0 2 0 3-1 4h-2 0-1-1l-4-5c-1 0-3-1-4-1-2-1-5 0-7 0-3 0-8 1-11 0-1 0-1 1-2 2 0-2 1-4 0-6h0l1-1c0-1-1-2-2-3h2c-1-3-2-3-4-5h1l-2-3v-1l-2-1v-2c-1-1-3-2-4-3l1-1h0l2 2h1l-1-1c0-2-1-2-1-4v-3c0-1 0-2-1-3h0v-4-2h-2c0-1-1-1-1-1l-2-1v-1c1-1 1-2 2-2h3 4v-2-2c0-2 1-4 2-6h1c0-2 1-3 2-4 0-2 1-3 2-4l1 1c0 1 1 2 2 3 1 0 1-1 1-2l1-1v2l1 1c1 0 1-1 2-1v-1h-1v-1h-1c-1-1-1-2-1-3l3-1-7-10z" class="G"></path><path d="M349 630l4 6-4-1h-1v-1h-1c-1-1-1-2-1-3l3-1z" class="a"></path><path d="M354 637l3 4 4 4c-1 1-1 2-3 2h-1c0-1-1-1-2-2v-1h-1-1c0-1 0-1 1-2 0-1-1-2-1-2 0-2 1-2 1-3z" class="K"></path><path d="M354 637l3 4h-2v3h-1-1c0-1 0-1 1-2 0-1-1-2-1-2 0-2 1-2 1-3z" class="P"></path><path d="M361 645l12 13h-1c-2-1-3-1-5-2-2-3-5-7-8-9h-1c2 0 2-1 3-2z" class="a"></path><path d="M359 647c3 2 6 6 8 9 2 1 3 1 5 2h1l5 5-2 2c-1 0-1 0-2-1h-2v-2c-2-1-2 0-3-2l-1-1c-1-1-3-2-5-2-1 0-2 0-3-1h-1c1-3 0-6 0-9z" class="C"></path><path d="M367 656c2 1 3 1 5 2h1l5 5-2 2c-1 0-1 0-2-1h-2v-2c-1-2-3-4-5-6z" class="Z"></path><defs><linearGradient id="X" x1="396.431" y1="673.915" x2="388.171" y2="677.802" xlink:href="#B"><stop offset="0" stop-color="#222020"></stop><stop offset="1" stop-color="#3c3e3d"></stop></linearGradient></defs><path fill="url(#X)" d="M382 666l19 15-1 2-1-1-1 1c-3-1-6-5-9-7h-1-2v1h-2l-3-8-1-1c1 0 2-1 2-2z"></path><path d="M381 669c2 0 6 5 8 7h-1-2v1h-2l-3-8z" class="M"></path><path d="M389 676c3 2 6 6 9 7l-1 1c-3 1-7 3-10 6h0-1c0-5-1-9-2-13h2v-1h2 1z" class="q"></path><path d="M387 690v-3c1 0 1-1 2-1v-3l2-1c2 0 4 1 6 2-3 1-7 3-10 6h0zm-27-34c1 1 2 1 3 1 2 0 4 1 5 2l1 1c1 2 1 1 3 2v2h2c1 1 1 1 2 1-2 2-4 3-6 5l-7 4-2-7v-5l-1-6z" class="D"></path><path d="M361 662v-1c1 0 2-1 2-2 2 0 4 1 5 3l-1 1v-1c-3 2-4 2-6 5v-5z" class="G"></path><path d="M361 667c2-3 3-3 6-5v1 1l-2 2c0 1 1 1 1 2 1-1 1-1 1-2h2l1 1v1 2l-7 4-2-7z" class="W"></path><defs><linearGradient id="Y" x1="377.092" y1="666.115" x2="379.497" y2="685.133" xlink:href="#B"><stop offset="0" stop-color="#020201"></stop><stop offset="1" stop-color="#313130"></stop></linearGradient></defs><path fill="url(#Y)" d="M378 663l4 3c0 1-1 2-2 2l1 1 3 8c1 4 2 8 2 13l-1-1c-1 0-2-1-3-1h0c0-1 0-1-1-1v-1h0l-1-1c-1 0-3 0-4 1h-2c0-1-1-1-1-2-1-2-2-2-4-3v-1-1-1c-1 1-1 1-1 2-1-1-3-2-4-2s-1 0-1-1c-1-1-1-2-1-3h1l7-4c2-2 4-3 6-5l2-2z"></path><path d="M381 680l1 3c1 1 2 2 2 3 1 1 1 2 1 3-1 0-2-1-3-1h0c0-1 0-1-1-1v-1h0v-1h0c0-1 0-1-1-2-1 0-1-1-2-2h3v-1z" class="m"></path><path d="M378 663l4 3c0 1-1 2-2 2 0-1 0-1-1-2-2 0-4 2-6 4l-1 1c-1 0-1 0-1 1h0 1 2c-2 2-3 2-6 4h-1c-1 1-3 1-4 1-1-1-1-2-1-3h1l7-4c2-2 4-3 6-5l2-2z" class="O"></path><defs><linearGradient id="Z" x1="375.55" y1="677.532" x2="371.847" y2="683.295" xlink:href="#B"><stop offset="0" stop-color="#7d7d7b"></stop><stop offset="1" stop-color="#959592"></stop></linearGradient></defs><path fill="url(#Z)" d="M374 672c1 0 2-1 4 0 2 2 2 4 3 7v1 1h-3c1 1 1 2 2 2 1 1 1 1 1 2h0v1l-1-1c-1 0-3 0-4 1h-2c0-1-1-1-1-2-1-2-2-2-4-3v-1-1-1c-1 1-1 1-1 2-1-1-3-2-4-2s-1 0-1-1c1 0 3 0 4-1h1c3-2 4-2 6-4z"></path><path d="M376 682c-1 1 0 1-1 1 1-1 1-3 3-3v1c1 1 1 2 2 2 1 1 1 1 1 2h0v1l-1-1c-1 0-3 0-4 1v-4z" class="n"></path><path d="M376 682c1 1 2 1 2 2 1 0 1 0 2 1-1 0-3 0-4 1v-4z" class="i"></path><path d="M374 672c1 0 2-1 4 0 2 2 2 4 3 7h-3l1-1c-1-1 0-1-1-1-2-1-2-2-2-3h-2c-1 1-1 1-2 1 0 1-1 1-1 1v1l-1-1c-2 0-1 1-2 1-2 1-3 1-4 1s-1 0-1-1c1 0 3 0 4-1h1c3-2 4-2 6-4z" class="L"></path><path d="M359 656h1l1 6v5l2 7h-1c0 1 0 2 1 3 0 1 0 1 1 1s3 1 4 2c0-1 0-1 1-2v1 1 1c2 1 3 1 4 3 0 1 1 1 1 2l1 2h0c1 1 0 1 1 2h-1v3l-1 1v-1h-2l-9-8-3-3-3-3c-3-3-6-7-9-8-3-2-6-2-8-2h3v-4l-2-1h2c2 0 3 0 4-1h0l1-1h2c1-1 1-1 1-2h-1l2-2h1c1 1 3 1 4 1h1l1-3z" class="H"></path><path d="M343 665l2 1v1 2h0-2v-4z" class="B"></path><path d="M360 682c1-1 2-1 4-1l1 1 1-1c0 1 0 2 1 3l-1 1h0l-1-1-2 1-3-3z" class="D"></path><path d="M375 688c-1-1-2-1-3-1h-1v-1c0-1 0-1-1-2 0 0 0-2-1-3l-2 1-1-2h-3l-1-1c-2 0-3-2-4-3 1-1 2-1 3-2h1c0 1 0 2 1 3 0 1 0 1 1 1s3 1 4 2c0-1 0-1 1-2v1 1 1c2 1 3 1 4 3 0 1 1 1 1 2l1 2z" class="C"></path><path d="M359 656h1l1 6v5l2 7h-1-1v-4c-2 0-2 0-4-1 0-1 0-1 1-2l-1-1c-1-1-2-1-3-2l2-1-1-1c-1 0-1 0-2 1h-1v-3h-1-1l2-2h1c1 1 3 1 4 1h1l1-3z" class="S"></path><path d="M359 656h1l1 6v5l2 7h-1-1v-4c-1-1-1-2-2-2l1-1-2-3c0-2 0-3-1-4v-1h1l1-3z" class="a"></path><path d="M401 681l11 7c-1 0-2 1-2 2v1l1 2c2 3 3 7 4 10l-1 1-3 3c-1 0-1 1-1 1h-1c-2-1-3 0-4 0l-1-1-1-1c-3 0-5-2-8-4-1-1-2-3-3-5l-2 2-3-2h-2l-1 1 1 2s-1 1-1 2l-1-1c-1 0-1 0-2-1l-2-1-2-1-1-1-4-4h2v1l1-1v-3h1c-1-1 0-1-1-2h0l-1-2h2c1-1 3-1 4-1l1 1h0v1c1 0 1 0 1 1h0c1 0 2 1 3 1l1 1h1 0c3-3 7-5 10-6l1-1 1-1 1 1 1-2z" class="p"></path><path d="M406 696l2-1-1-2c1 0 1 1 1 1l2 2v-4h0c1 3 2 6 1 10-1 1-1 2-2 3 0-1-1-1-2-2v-4c0-1 0-2-1-3z" class="f"></path><path d="M410 691l1 2c2 3 3 7 4 10l-1 1-3 3c-1 0-1 1-1 1h-1c-2-1-3 0-4 0l-1-1c2-1 3-1 5-2 1-1 1-2 2-3 1-4 0-7-1-10v-1z" class="E"></path><path d="M391 696h1l2 1v1l1-1v-1c2-1 2-2 5-2l2 1s1-1 2-1l2 2c1 1 1 2 1 3v4c1 1 2 1 2 2-2 1-3 1-5 2l-1-1c-3 0-5-2-8-4-1-1-2-3-3-5l-1-1z" class="O"></path><path d="M401 681l11 7c-1 0-2 1-2 2v1c-2-1-3-4-4-5-2-1-1 0-2-1 0 0-1-1-2-1 0 0-1 0-1 1v2c-1 2-2 2-3 3h-2-1c-1 1-2 2-4 2-1 2-1 2 0 4l1 1-2 2-3-2h-2l-1 1 1 2s-1 1-1 2l-1-1c-1 0-1 0-2-1l-2-1-2-1-1-1-4-4h2v1l1-1v-3h1c-1-1 0-1-1-2h0l-1-2h2c1-1 3-1 4-1l1 1h0v1c1 0 1 0 1 1h0c1 0 2 1 3 1l1 1h1 0c3-3 7-5 10-6l1-1 1-1 1 1 1-2z" class="M"></path><path d="M381 689s1 1 1 2c1 0 2 0 3 1-1 1 0 2-2 2h-1 0-1v-1-1h-1l1-1v-2h0z" class="e"></path><path d="M401 681l11 7c-1 0-2 1-2 2v1c-2-1-3-4-4-5-2-1-1 0-2-1 0 0-1-1-2-1 0 0-1 0-1 1v2c-2-1-3 0-5 1l-3 1c-2 1-3 2-4 3l-2-1v-1h0c3-3 7-5 10-6l1-1 1-1 1 1 1-2z" class="O"></path><path d="M380 685l1 1h0v3h0v2l-1 1h1v1 1h1c1 1 1 2 1 4v1s-1 0-1 1h-1l-2-1-2-1-1-1-4-4h2v1l1-1v-3h1c-1-1 0-1-1-2h0l-1-2h2c1-1 3-1 4-1z" class="X"></path><path d="M376 697c0-1 1-2 1-3h1s0 1 1 2v3l-2-1-1-1z" class="D"></path><path d="M380 685l1 1h0v3h0c-1 0-1-1-1-1h-1l-1 1h-1v-2-1h-1c1-1 3-1 4-1z" class="q"></path><path d="M340 669c2 0 5 0 8 2 3 1 6 5 9 8l3 3 3 3 9 8 4 4 1 1h-2l-1 1h0l-2-2c0 2 0 3-1 4h-2 0-1-1l-4-5c-1 0-3-1-4-1-2-1-5 0-7 0-3 0-8 1-11 0-1 0-1 1-2 2 0-2 1-4 0-6h0l1-1c0-1-1-2-2-3h2c-1-3-2-3-4-5h1l-2-3v-1l-2-1v-2c-1-1-3-2-4-3l1-1h0l2 2h1 1v1 2l1 1 1-1h2c-1-1-1-1-2-1l-1-1v-3c2-1 3-1 5-2z" class="G"></path><path d="M354 693c-1-2-2-3-4-4l-1 1v-1c1 0 3-1 4 0 0 0 0 1 2 1l1-1 1 1-2 2-1 1z" class="D"></path><path d="M363 690c0-1 1-1 1-1l11 9-1 1h0l-2-2-1-1-2 1h0l-1-1h-1c0-2-1-3-3-4l-2 1s-1-1-2-1c1-1 2-1 2-2h1z" class="C"></path><path d="M344 676h1c1-1 1-1 2-1h1v1c1 1 1 1 2 1l2-1c1 2 2 3 4 4 0 3 1 4 2 6-2 0-3-1-5 0l-1-1s0-1-1-2h0v-1h2v-1h-2l-3-3c0-1-1-1-1-1h-2l-1-1z" class="V"></path><path d="M340 690l1 1c1 1 3 2 5 3 3 0 6 0 8-1l1-1 2-2 3 3 3 3c-1 0-3-1-4-1-2-1-5 0-7 0-3 0-8 1-11 0-1 0-1 1-2 2 0-2 1-4 0-6h0l1-1z" class="Z"></path><path d="M340 669c2 0 5 0 8 2l-3 1c3 1 5 2 7 4l-2 1c-1 0-1 0-2-1v-1h-1c-1 0-1 0-2 1h-1l-1-2-1-1-1 1-1 1-1-2-1 1c-1 0-1 1-2 1l-1-1v-3c2-1 3-1 5-2z" class="e"></path><path d="M340 669c2 0 5 0 8 2l-3 1c-3-1-5-1-8 1h-1l-1-2c2-1 3-1 5-2z" class="U"></path><path d="M348 671c3 1 6 5 9 8l3 3 3 3 9 8 4 4 1 1h-2l-11-9s-1 0-1 1c-3-1-4-2-5-4s-2-3-2-6c-2-1-3-2-4-4-2-2-4-3-7-4l3-1z" class="R"></path><path d="M356 680l8 9s-1 0-1 1c-3-1-4-2-5-4s-2-3-2-6z" class="h"></path><path d="M329 672l1-1h0l2 2h1 1v1 2l1 1 1-1h2c-1-1-1-1-2-1 1 0 1-1 2-1l1-1 1 2 1-1 1-1 1 1-1 2c1 1 1 2 2 3v2l1 1-1 1 2 1 1-1 1 1v2 1h-3l-1-1c-1 1-2 3-3 5l-1-1c0-1-1-2-2-3h2c-1-3-2-3-4-5h1l-2-3v-1l-2-1v-2c-1-1-3-2-4-3z" class="H"></path><path d="M337 682c1 0 1 1 2 1 1 1 2 1 3 2l-1 2h-1c-1-3-2-3-4-5h1z" class="B"></path><path d="M336 641h1c0-2 1-3 2-4 0-2 1-3 2-4l1 1c0 1 1 2 2 3 1 0 1-1 1-2l1-1v2l1 1c1 0 1-1 2-1v-1l4 1 1 1c0 1-1 1-1 3 0 0 1 1 1 2-1 1-1 1-1 2h1 1v1c1 1 2 1 2 2h1 1c0 3 1 6 0 9l-1 3h-1c-1 0-3 0-4-1h-1l-2 2h1c0 1 0 1-1 2h-2l-1 1h0c-1 1-2 1-4 1h-2l2 1v4h-3c-2 1-3 1-5 2v3l1 1c1 0 1 0 2 1h-2l-1 1-1-1v-2-1h-1l-1-1c0-2-1-2-1-4v-3c0-1 0-2-1-3h0v-4-2h-2c0-1-1-1-1-1l-2-1v-1c1-1 1-2 2-2h3 4v-2-2c0-2 1-4 2-6z" class="W"></path><path d="M336 641h1c0-2 1-3 2-4 0-2 1-3 2-4l1 1c0 1 1 2 2 3 1 0 1-1 1-2l1-1v2c0 1-1 2-2 3h0c-1 2-2 3-3 5l-1 1-1 2-1-2h1c0-1 0-1 1-2 0-1 1-1 2-2 0-2-1-3-2-4h-1c0 1-1 2-1 3s-1 1-1 2c-1 1-1 1-1 2v1c0 1 0 2-1 3v7l-1-1h0v-3-2-2c0-2 1-4 2-6z" class="I"></path><path d="M346 636l1 1c0 2 0 3-1 4v3c-2 2-3 1-4 3l1 1-1 2h2l-1 1h-1v2c-1 0-1 0-2 1-1-1-2-3-4-3v-1-1c2 0 2 0 3 1h2v-1h-1c-1-1-1-2-1-2l1-2 1-1c1-2 2-3 3-5h0c1-1 2-2 2-3z" class="J"></path><path d="M349 635l4 1 1 1c0 1-1 1-1 3 0 0 1 1 1 2-1 1-1 1-1 2h1v1c-1 1-2 1-2 1v3l-1 1h0-5l-3 3h-1v-2h1l1-1h-2l1-2-1-1c1-2 2-1 4-3v-3c1-1 1-2 1-4 1 0 1-1 2-1v-1z" class="B"></path><path d="M353 644h1v1c-1 1-2 1-2 1v3l-1 1c-1-1-1-3-1-4 1-2 1-2 3-2z" class="E"></path><path d="M349 635l4 1 1 1c0 1-1 1-1 3h-2v-1l-2-1v-2-1z" class="Z"></path><path d="M349 636v2 5 1c-1 2-1 3-1 4s0 1-1 1h-1l-2-1h-1l-1-1c1-2 2-1 4-3v-3c1-1 1-2 1-4 1 0 1-1 2-1z" class="W"></path><path d="M354 644h1v1c1 1 2 1 2 2h1 1c0 3 1 6 0 9h-2-11l-2-1-1-2 3-3h5 0l1-1v-3s1 0 2-1v-1z" class="D"></path><path d="M355 645c1 1 2 1 2 2h1 1c0 3 1 6 0 9h-2v-3h-1c1-2 0-4-1-5v-3z" class="P"></path><path d="M343 653l3-3h5l2 1v4c-2 1-4 0-7 1l-2-1-1-2z" class="J"></path><path d="M335 655c3 0 6 1 9 0l2 1h11 2l-1 3h-1c-1 0-3 0-4-1h-1l-2 2h1c0 1 0 1-1 2h-2l-1 1h0c-1 1-2 1-4 1l1-2h-2 0-1c-2 0-3 0-4-1v-1c-1-1-1-1-2 0 0-1-1-2-1-3v-3l1 1z" class="b"></path><path d="M337 660h1 2l1-1 2 1c1-1 1-1 2-1h1 1 2l1 1h1c0 1 0 1-1 2h-2l-1 1h0c-1 1-2 1-4 1l1-2h-2 0-1c-2 0-3 0-4-1v-1z" class="X"></path><path d="M327 651h3 4v3h0v3c0 1 1 2 1 3 1-1 1-1 2 0v1c1 1 2 1 4 1h1 0 2l-1 2h-2l2 1v4h-3c-2 1-3 1-5 2v3l1 1c1 0 1 0 2 1h-2l-1 1-1-1v-2-1h-1l-1-1c0-2-1-2-1-4v-3c0-1 0-2-1-3h0v-4-2h-2c0-1-1-1-1-1l-2-1v-1c1-1 1-2 2-2z" class="F"></path><path d="M330 651h4v3c-2 0-2-1-3 0h-1v-3z" class="Q"></path><path d="M331 654c1-1 1 0 3 0h0v3 3h-2l-1-1 1-1-1-4z" class="B"></path><path d="M334 657c0 1 1 2 1 3 1-1 1-1 2 0v1c1 1 2 1 4 1h1 0 2l-1 2h-2c-2 2-3 2-6 3 0 1 0 0-1 1 0-2 0-3 1-4-1-2-1-3-1-4h0v-3z" class="C"></path><path d="M341 664l2 1v4h-3c-2 1-3 1-5 2v3l1 1c1 0 1 0 2 1h-2l-1 1-1-1v-2-1h-1l-1-1c1-2 2-3 2-4 1-1 1 0 1-1 3-1 4-1 6-3z" class="G"></path><path d="M723 357v1c1 0 3-2 3-3v-2c1 0 2-1 3-2 1-2 3-4 5-5-3 4-9 11-8 16l1 3-1 1c0 1 2 4 2 5v2c0 1 0 1 1 1l1 1c0 2 2 1 1 4 3 1 6 1 9 1l3-1 4-2c-3 2-6 3-10 4h-2c-2 2-4 4-5 7l-3 8h0l3-3c1 3-1 5 0 8v4l-2 6c14-3 26-10 34-22 5-8 9-19 10-28v-5-1h0c1 1 1 2 1 4-1 5-2 11-4 16-4 13-14 26-26 33-5 2-10 3-15 5 2 8 2 13 8 18 1 0 2 0 2-1v-1h-3c-1-1-1-3-1-4s1-1 1-2l1-1c0-2 2-4 4-4 4-2 8-1 11 1 2 1 3 2 5 4 1 1 2 4 1 6 1 2 2 6 1 8 0 4-1 7-1 11-1 6 0 10-3 15-7 8-19 11-24 20 1 3 0 7-1 9h-1v-2c-1 1 0 2 0 4v1c1 3 4 5 5 8 0 2 0 4 1 5l-1 1c-1 1-2 2-3 2 0 2 1 2 2 3h-1l-5-4c1 2 2 3 2 4h0l2 2-1 1c1 1 1 2 2 3v2c1 2 1 4 1 6v3c0 3 0 5-1 7h0c-1 0-1 0-1-1-1 0-1 1-2 2h1-1c-1 0-3 1-4 0l-1 1c-1 0-2 1-3 1h-2l-1-1c-2-1-3-1-4-2v2h1l-2 2c-1 0-1 0-2-1l-1 1c0-2 1-3 1-5-2 0-3 0-4-1-1 1-1 3-1 4h-1v-1h-1c0 1-1 1-1 1h-1l-3-1v-4c0-2-2-2-3-4h0 0c0-1-1-1-1-1v-2l5 1 1-1h1l-2-3h1c-1-2-1-3-2-4-2-1-3-2-5-3 3-2 5 0 7-1l-1-2v-1-1l-2-1h-1c0-1 0-1-1-2l6-9 4-6v-3-1c0-2 0-2 1-4l4 8c1-1 1-2 1-2 1-3 2-5 4-7 0-1 1-4 2-5 1-3 2-5 2-8V355h0v-5l3-3 1 1h0c-1 2-1 3-1 4l1 1v1 3z" class="s"></path><path d="M741 433l1-1c1 0 1 0 2 1l-1 1h1v1l-3 3c-2 1-3 3-5 3v-2-1c0-2 4-3 5-5z" class="W"></path><path d="M726 362l1 3-1 1c0 1 2 4 2 5v2c0 1 0 1 1 1l1 1c0 2 2 1 1 4h-1c-2-1-6-5-7-8l1-1 1 1v-1h-1c0-2-1-3-1-5 1 0 1 0 2-1-1 0-1 0-1-1l2-1z" class="S"></path><path d="M741 438h1l1 1-2 2v1 2l-1 2 2 1c-2 2-3 3-5 4l-5 2c0-3 2-9 4-12 2 0 3-2 5-3z" class="j"></path><path d="M744 433c1 1 2 1 3 2l-2 1v1l2 2c-1 6-5 11-8 16-1-2-1-3-2-4 2-1 3-2 5-4l-2-1 1-2v-2-1l2-2-1-1h-1l3-3v-1h-1l1-1z" class="P"></path><path d="M741 442c1-2 2-3 4-3l1 1c0 2-1 4-3 5l-1 2-2-1 1-2v-2z" class="M"></path><path d="M715 486h0c0 1-1 2-1 4h0c0 2-1 3-2 5h1 2v1 2h1l2-1h1 1v1c0 1 0 1-1 2h-1-1l-1 3c-1-1-1-1-2-1v1l-2 1-5 2c-1 1-1 1-2 1h-3-1c-1 1-2 2-3 4l-1 1h-1c0-1 0-1-1-2l6-9 4-6v-3-1c0-2 0-2 1-4l4 8c1-1 1-2 1-2 1-3 2-5 4-7z" class="f"></path><path d="M705 501h1c0 1 1 1 2 1h-1 0-1c-2 0-3 2-4 3h-1v-2c2-1 3-1 4-2z" class="p"></path><path d="M701 501l4-6v3c0 1 1 1 1 2l-1 1c-1 1-2 1-4 2v-2z" class="T"></path><path d="M701 501v2 2 1c3 0 3-1 5-2 2 0 5-2 8-2v1l-2 1-5 2c-1 1-1 1-2 1h-3-1c-1 1-2 2-3 4l-1 1h-1c0-1 0-1-1-2l6-9z" class="P"></path><path d="M726 529h0v-1-3c0-3 0-6-1-9l-1-1h1c0 1 1 2 1 3s0 1 1 2h0c0-2 0-3-1-5h1l2 2c1 1 1 2 2 3v2c1 2 1 4 1 6v3c0 3 0 5-1 7h0c-1 0-1 0-1-1-1 0-1 1-2 2h1-1c-1 0-3 1-4 0l-1 1c-1 0-2 1-3 1h-2l-1-1 1-1v-2c0-2 0-5-1-6h-1c1-1 1-1 0-2h2l1-2 1-1 1 1h0c2-1 2-1 3 0h1c1 1 1 2 1 2z" class="m"></path><path d="M728 539v-1h0v-2c1-3 1-6 1-8v-1-1-2h0 1v3l2 1v3c0 3 0 5-1 7h0c-1 0-1 0-1-1-1 0-1 1-2 2z" class="e"></path><path d="M719 527l1-1 1 1h0c2-1 2-1 3 0h1c1 1 1 2 1 2l-1 2h-1l-1 1c1 0 2 1 2 2l-3 1h-1 0c-1 1-1 2-1 3h-1s0-1-1-1c0-2 0-5-1-6h-1c1-1 1-1 0-2h2l1-2z" class="H"></path><path d="M714 502c1 0 1 0 2 1 3 3 5 5 4 9 0 2-1 4-2 6-1 1-2 2-4 3 0-1-1-1-1-2l-2 2c0-1-1-2-2-2h-1v-1h-1v-1h-3l1-2h-1-2-1l-2-3c1-2 2-3 3-5h3c1 0 1 0 2-1l5-2 2-1v-1z" class="N"></path><path d="M708 518h0c3-2 5 0 8 0h2c-1 1-2 2-4 3 0-1-1-1-1-2l-2 2c0-1-1-2-2-2h-1v-1z" class="X"></path><path d="M714 503c1 1 3 1 4 3s1 3 1 5l-3 3-1-1c-1 1-2 2-3 2v-1h-2 0c0-1 1-2 2-2h0c2 0 2 0 3-1v-5l-1-1v-1h-2l2-1z" class="K"></path><path d="M712 504h2v1l1 1v5c-1 1-1 1-3 1h0c0-4-1-4-4-6l1 7-4 2h-1-2-1l-2-3c1-2 2-3 3-5h3c1 0 1 0 2-1l5-2z" class="p"></path><path d="M697 512l1-1c1-2 2-3 3-4h1c-1 2-2 3-3 5l2 3h1 2 1l-1 2h3v1h1v1h1c1 0 2 1 2 2l2-2c0 1 1 1 1 2l-1 1c0 1 1 4 2 5h4l-1 2h-2c1 1 1 1 0 2h1c1 1 1 4 1 6v2l-1 1c-2-1-3-1-4-2v2h1l-2 2c-1 0-1 0-2-1l-1 1c0-2 1-3 1-5-2 0-3 0-4-1-1 1-1 3-1 4h-1v-1h-1c0 1-1 1-1 1h-1l-3-1v-4c0-2-2-2-3-4h0 0c0-1-1-1-1-1v-2l5 1 1-1h1l-2-3h1c-1-2-1-3-2-4-2-1-3-2-5-3 3-2 5 0 7-1l-1-2v-1-1l-2-1z" class="k"></path><path d="M708 519c0 1-1 2 0 3h2v1c-2 1-3 2-5 2v-2h-1c0 1 0 1-1 2 0-1 0-1-1-1l1-2c-1-1-1-2-1-4 1 0 1 0 1-1v-1l1 1h3v1h1v1z" class="p"></path><path d="M700 525c2 2 4 2 7 2h2 3 1l3 2c1 1 1 1 0 2h1c1 1 1 4 1 6v2l-1 1c-2-1-3-1-4-2v2h1l-2 2c-1 0-1 0-2-1l-1 1c0-2 1-3 1-5v-1-6c-2-2-8-1-11-1l1-1h1l-2-3h1z" class="O"></path><path d="M713 527l3 2c1 1 1 1 0 2h1c1 1 1 4 1 6v2l-1 1c-2-1-3-1-4-2v-3l-1-1 1-1v-6z" class="I"></path><path d="M694 528l5 1c3 0 9-1 11 1v6 1c-2 0-3 0-4-1-1 1-1 3-1 4h-1v-1h-1c0 1-1 1-1 1h-1l-3-1v-4c0-2-2-2-3-4h0 0c0-1-1-1-1-1v-2z" class="D"></path><path d="M694 528l5 1c3 0 9-1 11 1h-2c-1 1-1 2-2 3-1-1-2-1-2-2-2 0-4 1-5 1s-2-1-4-1c0-1-1-1-1-1v-2z" class="X"></path><defs><linearGradient id="a" x1="743.624" y1="477.921" x2="741.187" y2="457.662" xlink:href="#B"><stop offset="0" stop-color="#121312"></stop><stop offset="1" stop-color="#2f2d2e"></stop></linearGradient></defs><path fill="url(#a)" d="M750 451h1s1-1 1-2v-3l1-2 1-8c1-1 1-2 1-3v-8l1-1c0 2 0 4 1 6v-1c1 2 2 6 1 8 0 4-1 7-1 11-1 6 0 10-3 15-7 8-19 11-24 20v-4h-1c-1 1-2 1-3 2v1l-1 1-1-1 1-1c0-1 0-1 1-2s2-3 3-5c0-1 1-2 3-3l7-6 2-2h-1l1-1h1c3-1 6-3 7-6-2 2-4 3-7 4-1 1-3 2-5 2 3-2 6-3 8-5l1-1s4-4 4-5z"></path><path d="M750 451h1s1-1 1-2v-3l1-2 1-8c1-1 1-2 1-3v-8l1-1c0 2 0 4 1 6 1 6-1 15-3 21-1 4-5 8-8 10l-3 2-1-1c3-1 6-3 7-6-2 2-4 3-7 4-1 1-3 2-5 2 3-2 6-3 8-5l1-1s4-4 4-5z" class="Q"></path><path d="M738 429h-3c-1-1-1-3-1-4s1-1 1-2l1-1c0-2 2-4 4-4 4-2 8-1 11 1 2 1 3 2 5 4 1 1 2 4 1 6v1c-1-2-1-4-1-6l-1 1v8c0 1 0 2-1 3l-1 8-1 2v3c0 1-1 2-1 2h-1c0 1-4 5-4 5l-10 6v-2c1-3 1-3 3-5 3-5 7-10 8-16l-2-2v-1l2-1c-1-1-2-1-3-2s-1-1-2-1l-1 1c-2 0-3-1-5-2 1 0 2 0 2-1v-1z" class="r"></path><path d="M738 429h-3c-1-1-1-3-1-4s1-1 1-2l1-1c0-2 2-4 4-4 4-2 8-1 11 1 2 1 3 2 5 4 1 1 2 4 1 6v1c-1-2-1-4-1-6l-1 1v8c0 1 0 2-1 3l-1 8-1 2v3c0 1-1 2-1 2h-1s1-2 1-3c1-2 1-5 1-8l1-3v-2c1-3 1-7 1-10l-2-2h0-4c-2-1-4-1-6-1h-1v1c-2 0-3 1-4 3v2l1 1z" class="B"></path><path d="M567 178c0-1 0-1 1-1l1 2c0-2 0-3 1-4v-1 1l1 3c1 1 1 1 3 1l1-2h2l1-1-1 1-2 2v2c1 1 2 2 4 2v1c1 2 3 3 4 4l1 1c1 1 2 1 3 1v1c1 0 2 1 3 1l3 3v-1l-1-1v-3h-1l1-1 2-1v2c3 2 6 8 9 10h1c1 1 1 2 2 3l1 1c2 4 6 8 9 13 2 3 2 7 4 10 1 2 3 4 4 6 1 1 1 1 1 2l1 1c1 2 2 3 3 4 0 1 0 2 1 3-1 1-1 1-2 3h0c-1 1-1 0-1 1-1 1-1 2-1 2l-1 1v1c1 2 3 4 4 6 1 0 2 1 3 1l1 2 4 3c1 2 1 2 0 4v1h0c-2 3-3 5-6 5h-1c0 1 1 3 1 5l1 3 2 10c0 1 1 3 1 4l3 11c0 2 1 5 2 6l1 3-1 1h0l-2-2h0v-1l-3-3c-1-1-2-1-3-2-1 0-2-1-3-1-1-1-1-1-2-1 0-2 0-2 1-3h2c1 1 2 1 3 2v1h1c-1-1-2-2-2-4h-1c-1-2-2-3-3-4 0-1 0-2-1-3l-1 1-2 1-1-1h-1c-1 1-3 2-3 3-1 0-2 0-2-1h-5v-1c-3 0-4 1-6 1-2 1-3 1-5 3v1h-1-1v2h-1l-3-2-2-1-1-1c0-1 0-1-1-1-3-1-6-3-10-3h-2c-2-1-6 0-8-1l1-1c2 0 6-1 9 0 1 1 3 1 5 1l-1-2c-2 0-4-1-7-1h-2c-1 1-3 0-4 0h-2c-1-1-3-1-4-2l2-2h0-3l1-1-1-1c0-1 1-2 1-2l1-1-1-1c-1-1-1 1-3 0l1-2h0l-4 1h0c1-1 3-2 5-3l-2-1-8 5c-1 0-7 4-7 4-2 0-2 0-3 1l-1-1h1v-1l-2 1c-2 0-4-1-6-1l-9-3v-1c2 1 4 2 7 2v-1l-2-1h0 2 2c1 1 2 1 4 1l1-1h3c1 0 1 0 2-1l1 1h1c2 0 2-1 4-2h1c0-1 2-2 4-2 2-2 5-4 8-5 2-1 3-2 5-3v-1l-2 1-1-1c1-1 1-1 1-3h-1v-1h1c2-1 4-2 6-2-1 1-4 3-5 5h4l3-2h1c-1 1 0 1-1 1s-2 1-3 2v1c1 0 4 0 5-1h1c1-1 2-1 3-1l4-1 1-1c1 0 1 0 1 1h1l-2 2c2 0 2 0 3-1 3-1 6 0 9-1h0 1v-1c0-1 2-1 3-1v-1l-1-3c-1-1-1-2-2-3-1 1-1 0-2 1h0-1c0-1 1-2 2-2v-4-1h-1l-1-1v-1l1-1h5c-2-5-5-9-10-11l-1-1c-2-1-4-3-7-3 0-1 0-1-1-2h6v1h1 1 0c0-1-1-1-2-1-1-1-2-1-3-2 1-2 1-1 2-2 1 0 2-1 2-1 0-1 0-1 1-2h0l-1-2-13-7s-1 0-1 1c-1 2-2 3-3 5l-3 3-2 1v-1l2-3h0-1c-1 1-1 1-2 1s-1-1-1-2c-4 3-7 7-11 10-2 2-3 3-5 4h0v-1c2-3 6-6 8-8l-2 1c-3 1-5 2-7 3h-1-2l2-1 1-1h1c2-1 4-3 6-5-2 1-4 3-6 3h-1l5-3c1-1 3-2 3-3-1 0-2 1-4 1l-1-1c-3 1-5 3-8 4 0-3 3-4 5-6s4-3 5-5h0l1-1h-1v-1l3-3c2-2 3-4 5-5 1-1 1-2 2-3 1 0 1-1 1-2l2-2v2c1 2 1 2 0 4h1c-1 1 0 1-1 2h0l1 1c1-1 2-2 2-4s1-3 2-5l-1-1 1-1 1-1h-2l1-1c0-2-1-3 0-5-1 0-2 0-3-1v-1l-2-1c-1 1-2 2-2 3h-1v-7l1-1z" class="Q"></path><path d="M623 253l-1-1-1-1c2-1 2-1 4-1v1 1c-1 0-1 0-2 1z" class="Y"></path><path d="M617 273c-1-1-1-1-1-2l1-1 2 2v1l-2 1v-1z" class="l"></path><path d="M615 259l1-1 2 1-1 1-2 2c1 1 1 2 1 4h0c0-2 0-3-1-4s0-2 0-3z" class="E"></path><path d="M620 258c1 0 2 0 3 1v2c-1 1-1 1-2 1-1 1-1 2-1 2-1 0-1 0-1-1s-1-2-2-2v-1l1-1 2-1z" class="l"></path><path d="M617 273v1l2 1c1 1 1 3 1 4v1c0 1 0 2 1 3l-1 1-3-3-2-1v-1c1 1 1 1 2 1l1-1c-1-2 0-3-1-4l-1-1h0l1-1z" class="E"></path><path d="M625 251c1 2 3 4 4 6 1 0 2 1 3 1l1 2h-3 0-1c-2-1-6-5-6-7 1-1 1-1 2-1v-1z" class="l"></path><path d="M608 240l-2-2h-1c-1-1 0-1-1-1s-2 0-2-1h0 3c1 1 0 1 2 1h1 1 1c3 1 6 5 8 7h0c-1 0-2 0-3-1s-3-2-4-2c-1-1-2-1-3-1z" class="G"></path><path d="M596 275c4 1 7 2 11 3h-2c-3-1-6-1-10 0h-4c-1-1-1-1-2-1-1-1-1-1-1-2h8z" class="B"></path><path d="M577 280l6-1 1 1c1 0 8 0 9 1h-3v1h-5-3c-1 1-3 1-4 2l-2 2c-2 0-4 1-6 1l-1-1c3-3 6-2 8-6h0z" class="E"></path><path d="M614 249c1-1 2-1 3-1h0v-1l1-1c1 1 1 1 1 2 1 0 1 1 1 3h1v6l-1 1-2 1-2-1-6-6c2-1 3-2 4-3z" class="B"></path><path d="M610 245c2 1 3 1 4 3v1c-1 1-2 2-4 3l6 6-1 1-1-1-1 1c-2 1-4-3-5-4h-3l-2-2-3-2h-1l-1-1v-1l1-1h5l1 2c2 0 2-1 3-1h1c1 0 2 0 3-1-1-1-1-2-2-3z" class="d"></path><path d="M631 299c-1 0-1-1-2-2v-1c0-2-1-2-2-2 0-1-1-2-1-3v-1c-1-1-2-3-4-4l1-1 1 1v-1c-1-2-1-5-2-8 0-1-1-1-1-2 0-4-1-7-2-10 2 2 2 5 5 6 0-1-1-4 0-6l1 1v3c1 1 0 4 1 6v1c0 2 1 3 1 4v2c0 1 0 2 1 3v2l2 9v1c1 0 1 1 1 2z" class="B"></path><path d="M633 260l4 3c1 2 1 2 0 4v1h0c-2 3-3 5-6 5h-1 0c-1-1-2-2-2-3h-1c0 1 0 2 1 3-1 1-1 1 0 2h-1l-1 1v-1c-1-2 0-5-1-6v-3l-1-1 1-1h2 1l2-1v-3h0 3z" class="Y"></path><path d="M630 263h1c1 1 1 1 1 2l-2 1v2c-1 0 0 0-1-1h0c-1 1-1 1-2 1v-1l-2-1-1-1 1-1h2 1l2-1z" class="l"></path><path d="M625 266l2 1v1c1 0 1 0 2-1h0c1 1 0 1 1 1h2c-1 1-1 1-1 2l1 1h1l1-1c0-1 0-2 2-2h1 0c-2 3-3 5-6 5h-1 0c-1-1-2-2-2-3h-1c0 1 0 2 1 3-1 1-1 1 0 2h-1l-1 1v-1c-1-2 0-5-1-6v-3z" class="J"></path><path d="M567 283c1 0 2-1 3-1s2-1 3-1c2-1 3-1 4-1h0c-2 4-5 3-8 6l1 1c2 0 4-1 6-1l2-2c3 0 4 0 6 2 1 1 1 2 1 4l1-1c0 1 1 2 1 3l-2 1c-2 0-4-1-7-1h-2c-1 1-3 0-4 0h-2c-1-1-3-1-4-2l2-2h0-3l1-1-1-1c0-1 1-2 1-2l1-1z" class="c"></path><path d="M578 284c3 0 4 0 6 2 1 1 1 2 1 4l-1 1c-2 0-4-3-6-4h-3l1-1 2-2z" class="L"></path><path d="M567 283c1 0 2-1 3-1s2-1 3-1c2-1 3-1 4-1h0c-2 4-5 3-8 6l1 1c2 0 4-1 6-1l-1 1h-1c-1 1-1 1-2 1l-3 1c1 1 1 1 3 2 1 0 3 0 5 1h1-2c-1 1-3 0-4 0h-2c-1-1-3-1-4-2l2-2h0-3l1-1-1-1c0-1 1-2 1-2l1-1z" class="D"></path><path d="M626 276l1-1h1c-1-1-1-1 0-2-1-1-1-2-1-3h1c0 1 1 2 2 3h0c0 1 1 3 1 5l1 3 2 10c0 1 1 3 1 4l3 11c0 2 1 5 2 6l1 3-1 1h0l-2-2h0v-1l-3-3c-1-1-2-1-3-2-1 0-2-1-3-1-1-1-1-1-2-1 0-2 0-2 1-3h2c1 1 2 1 3 2v1h1c-1-1-2-2-2-4h-1c1-1 0-2 0-3s0-2-1-2v-1l-2-9v-2c-1-1-1-2-1-3v-2c0-1-1-2-1-4z" class="l"></path><path d="M628 287c1 0 2 1 2 2 1 2 1 5 2 7s1 3 2 5h2v3c1 1 1 2 2 2 0 2 1 5 2 6l1 3-1 1h0l-2-2h0v-1l-3-3c-1-1-2-1-3-2-1 0-2-1-3-1-1-1-1-1-2-1 0-2 0-2 1-3h2c1 1 2 1 3 2v1h1c-1-1-2-2-2-4h-1c1-1 0-2 0-3s0-2-1-2v-1l-2-9z" class="d"></path><path d="M600 251l3 2 2 2 2 1c2 1 3 2 3 4h1c1 2 1 3 1 4l1 1c1 3 0 8 0 12h0c-1 0-2 0-2-1-1 1-1 2-2 2h-1-1c-4-1-7-2-11-3-3-1-5-2-8-2h-1l-1-1c0-1 0-1-1-1-2-1-3-1-4-1v1h-2l1-1c2 0 2 0 4-1v-1l3-1v-1l1-1c1 0 1 0 1 1h1l-2 2c2 0 2 0 3-1 3-1 6 0 9-1h0 1v-1c0-1 2-1 3-1v-1l-1-3c-1-1-1-2-2-3-1 1-1 0-2 1h0-1c0-1 1-2 2-2v-4-1z" class="E"></path><path d="M586 272c2 0 4-1 6 0 3 0 6 1 9 1 0 0 1 0 1 1l9 2c-1 1-1 2-2 2h-1-1c-4-1-7-2-11-3-3-1-5-2-8-2h-1l-1-1z" class="d"></path><path d="M569 264c2-1 4-2 6-2-1 1-4 3-5 5h4l3-2h1c-1 1 0 1-1 1s-2 1-3 2v1c1 0 4 0 5-1h1c1-1 2-1 3-1l4-1v1l-3 1v1c-2 1-2 1-4 1l-1 1h2v-1c1 0 2 0 4 1 1 0 1 0 1 1l1 1h1c3 0 5 1 8 2h-8c0 1 0 1 1 2 1 0 1 0 2 1l-8 1-6 1c-1 0-2 0-4 1-1 0-2 1-3 1s-2 1-3 1l-1-1c-1-1-1 1-3 0l1-2h0l-4 1h0c1-1 3-2 5-3l-2-1-8 5c-1 0-7 4-7 4-2 0-2 0-3 1l-1-1h1v-1l-2 1c-2 0-4-1-6-1l-9-3v-1c2 1 4 2 7 2v-1l-2-1h0 2 2c1 1 2 1 4 1l1-1h3c1 0 1 0 2-1l1 1h1c2 0 2-1 4-2h1c0-1 2-2 4-2 2-2 5-4 8-5 2-1 3-2 5-3v-1l-2 1-1-1c1-1 1-1 1-3h-1v-1h1z" class="C"></path><path d="M555 282c0-1 1-2 1-3 2-2 4-3 6-3 2 2 3 0 5 0v1l-2 1-2-1-8 5z" class="h"></path><path d="M553 279h1c0 1-1 1-1 2-3 1-5 3-8 3h-1c-2 1-4 0-6 0-1 0-3 0-3-1v-1l-2-1h0 2 2c1 1 2 1 4 1l1-1h3c1 0 1 0 2-1l1 1h1c2 0 2-1 4-2z" class="G"></path><path d="M582 273l4-1 1 1h1c3 0 5 1 8 2h-8c-5 1-11 3-16 2l-1-1v-1l5-1c2 0 4-1 6-1z" class="J"></path><path d="M583 267l4-1v1l-3 1v1c-2 1-2 1-4 1l-1 1h2v-1c1 0 2 0 4 1 1 0 1 0 1 1l-4 1c-2 0-4 1-6 1-1-1-2-1-2-1-2 1-4 1-6 1 1-2 2-2 3-3h1v-2l2-1v1c1 0 4 0 5-1h1c1-1 2-1 3-1z" class="G"></path><path d="M582 273l-1-1c-1-1-2-1-4-1v1l-1-1c3-2 5-2 8-3v1c-2 1-2 1-4 1l-1 1h2v-1c1 0 2 0 4 1 1 0 1 0 1 1l-4 1z" class="B"></path><path d="M578 284c1-1 3-1 4-2l1 1c3 1 7 1 10 1h1 1v1c-2 1-5 0-8 2l1 1 2 1h2l1-1h2c5-1 9-2 14-1l1 1h1 1c0 1 1 1 2 1h0 1c1 0 2 1 2 1h1c1 0 1 1 2 1 1 1 2 1 3 2 1 0 3 1 4 2l-1 1-2 1-1-1h-1c-1 1-3 2-3 3-1 0-2 0-2-1h-5v-1c-3 0-4 1-6 1-2 1-3 1-5 3v1h-1-1v2h-1l-3-2-2-1-1-1c0-1 0-1-1-1-3-1-6-3-10-3h-2c-2-1-6 0-8-1l1-1c2 0 6-1 9 0 1 1 3 1 5 1l-1-2 2-1c0-1-1-2-1-3l-1 1c0-2 0-3-1-4-2-2-3-2-6-2z" class="W"></path><path d="M594 300c1 0 1 0 1-1l2-2c2-1 4-2 6-2-3 2-6 4-8 7l-2-1 1-1z" class="V"></path><path d="M603 295l1 1h-1c0 1 0 1-1 2h1 1 2c-2 1-3 1-5 3v1h-1-1v2h-1l-3-2c2-3 5-5 8-7z" class="T"></path><path d="M604 296c3-3 8-4 12-3 2 1 5 2 7 3h-1c-1 1-3 2-3 3-1 0-2 0-2-1h-5v-1c-3 0-4 1-6 1h-2-1-1c1-1 1-1 1-2h1z" class="K"></path><path d="M587 292c3-1 7-2 11-2 1-1 3 0 5-1h7c-2 2-9 1-12 2-3 0-6 1-9 2l1 1c6-2 11-3 18-2l-1 1c-1 0-3-1-4-1-1 1-4 1-5 1-1 1-2 1-3 2-2 0-4 0-5 1l1 1v1l1-1c1 0 2-1 3-1v1h0c-1 1-1 1-1 2v1l-1 1-1-1c0-1 0-1-1-1-3-1-6-3-10-3h-2c-2-1-6 0-8-1l1-1c2 0 6-1 9 0 1 1 3 1 5 1l-1-2 2-1z" class="D"></path><path d="M567 178c0-1 0-1 1-1l1 2c0-2 0-3 1-4v-1 1l1 3c1 1 1 1 3 1l1-2h2l1-1-1 1-2 2v2c1 1 2 2 4 2v1c1 2 3 3 4 4l1 1c1 1 2 1 3 1v1c1 0 2 1 3 1l3 3v-1l-1-1v-3h-1l1-1 2-1v2c3 2 6 8 9 10h1c1 1 1 2 2 3l1 1c2 4 6 8 9 13 2 3 2 7 4 10 1 2 3 4 4 6 1 1 1 1 1 2l1 1c1 2 2 3 3 4 0 1 0 2 1 3-1 1-1 1-2 3h0c-1 1-1 0-1 1-1 1-1 2-1 2-1-1-1 0-1-2h0c-1-2 0-3-1-4s-1-1-1-2c-1-1-1-1-1-2h-1v-3c-1-2-1-4-2-5l-1-1c-1-2-3-5-4-5-1-2-2-3-3-4l-2-5c-2-2-3-4-5-6-1-2-2-3-3-4-2-3-3-5-7-7l-1 1c-3-3-7-6-10-7-2-1-4-2-5-3h0l-1-1c-1-1-1-2-3-3-1 0-2 0-3-1v-1l-2-1c-1 1-2 2-2 3h-1v-7l1-1z" class="r"></path><path d="M571 184c0-1-1-2 0-4h2c2 1 4 3 6 4 1 2 3 3 4 4l1 1c-2 0-2 0-4-1v-2l-2-1c-1 0-1 1-1 2 0-1-1-2-1-3h-1c0 2 3 4 3 6l-1-1c-1-1-1-2-3-3-1 0-2 0-3-1v-1z" class="G"></path><path d="M567 178c0-1 0-1 1-1l1 2c0-2 0-3 1-4v-1 1l1 3c1 1 1 1 3 1l1-2h2l1-1-1 1-2 2v2c1 1 2 2 4 2v1c-2-1-4-3-6-4h-2c-1 2 0 3 0 4l-2-1c-1 1-2 2-2 3h-1v-7l1-1z" class="C"></path><path d="M574 191c1 0 2 0 3 1h1c1 0 1 0 2 1s2 1 4 1c3 1 5 4 8 6v1h0c-1 0-2-1-3-2l-1 1h1v1c2 1 3 2 5 3a30.44 30.44 0 0 1 8 8c-1 0-2-1-3-2h-1 0c3 3 6 5 8 8v1c-2-1-3-2-4-3h-1l6 6c1 1 2 2 3 4l3 6-6-6-1 1c3 2 6 4 8 7 0 1-1 2-2 3l-3-2v1l1 1h-1-1-1c-2 0-1 0-2-1h-3 0c0 1 1 1 2 1s0 0 1 1h1l2 2c2 1 3 3 5 3l2 2h-3c-2-2-5-4-7-6l-1 1c2 2 4 4 6 5h0c1 1 1 2 2 3-1 1-2 1-3 1h-1c-1 0-1 1-3 1l-1-2c-2-5-5-9-10-11l-1-1c-2-1-4-3-7-3 0-1 0-1-1-2h6v1h1 1 0c0-1-1-1-2-1-1-1-2-1-3-2 1-2 1-1 2-2 1 0 2-1 2-1 0-1 0-1 1-2h0l-1-2-13-7s-1 0-1 1c-1 2-2 3-3 5l-3 3-2 1v-1l2-3h0-1c-1 1-1 1-2 1s-1-1-1-2c-4 3-7 7-11 10-2 2-3 3-5 4h0v-1c2-3 6-6 8-8l-2 1c-3 1-5 2-7 3h-1-2l2-1 1-1h1c2-1 4-3 6-5-2 1-4 3-6 3h-1l5-3c1-1 3-2 3-3-1 0-2 1-4 1l-1-1c-3 1-5 3-8 4 0-3 3-4 5-6s4-3 5-5h0l1-1h-1v-1l3-3c2-2 3-4 5-5 1-1 1-2 2-3 1 0 1-1 1-2l2-2v2c1 2 1 2 0 4h1c-1 1 0 1-1 2h0l1 1c1-1 2-2 2-4s1-3 2-5l-1-1 1-1 1-1h-2l1-1z" class="r"></path><path d="M598 228c3 0 4 2 5 3s2 1 3 2h-2 0l-1-1c-3 0-3 0-5-1-1-1 0-2 0-3z" class="E"></path><path d="M574 191c1 0 2 0 3 1h1c1 0 1 0 2 1s2 1 4 1c3 1 5 4 8 6v1h0c-1 0-2-1-3-2l-1 1h1v1h-2c-2 0-2 0-3-1h-2l1 1 2 1c1 1 2 1 3 2l1 1h0-1c-2-1-4-2-6-2-1-1-3-1-4-1l-1 1h0l-1 1v-3l-1-1 2-1h0l-3-1v-2l2-2c2 1 3 1 5 2h1 1c-3-2-6-3-8-4h-2l1-1z" class="Y"></path><path d="M572 208h1c1-1 1-2 1-3-1-1-1-1-1-2 1-1 1-2 2-3l1 1v3l1-1 1 1c1 2 3 2 5 3 5 2 11 3 15 7h0c-7-3-13-5-19-8-1 1-1 2-1 4h1c4 1 9 4 13 7h-1c-3-1-7-4-11-5l-2 2 1 1s-1 0-1 1c-1 2-2 3-3 5l-3 3-2 1v-1l2-3h0-1c-1 1-1 1-2 1s-1-1-1-2c-4 3-7 7-11 10-2 2-3 3-5 4h0v-1c2-3 6-6 8-8l-2 1c-3 1-5 2-7 3h-1-2l2-1 1-1h1c2-1 4-3 6-5-2 1-4 3-6 3h-1l5-3h0 3-1l1 1c1-1 1-2 2-2 4-2 6-5 9-8 0-2 1-3 2-4v-1z" class="W"></path><path d="M568 220c1-2 3-4 5-6l4-7c0 3-1 5-3 7 0 1 1 2 0 3s-1 3-2 4h0-1c-1 1-1 1-2 1s-1-1-1-2z" class="B"></path><path d="M575 192c2 1 5 2 8 4h-1-1c-2-1-3-1-5-2l-2 2v2l3 1h0l-2 1c-1 1-1 2-2 3 0 1 0 1 1 2 0 1 0 2-1 3h-1v1c-1 1-2 2-2 4-3 3-5 6-9 8-1 0-1 1-2 2l-1-1h1-3 0c1-1 3-2 3-3-1 0-2 1-4 1l-1-1c-3 1-5 3-8 4 0-3 3-4 5-6s4-3 5-5h0l1-1h-1v-1l3-3c2-2 3-4 5-5 1-1 1-2 2-3 1 0 1-1 1-2l2-2v2c1 2 1 2 0 4h1c-1 1 0 1-1 2h0l1 1c1-1 2-2 2-4s1-3 2-5l-1-1 1-1 1-1z" class="H"></path><path d="M575 192c2 1 5 2 8 4h-1-1c-2-1-3-1-5-2l-2 2v2l3 1h0l-2 1c-1 1-1 2-2 3 0 1 0 1 1 2 0 1 0 2-1 3h-1l-2 2c-1 1-2 1-3 2v-1c2-2 3-5 4-8-3 2-4 5-7 7v-1-1-1-1c1-2 2-4 2-6v-1c1 0 1-1 1-2l2-2v2c1 2 1 2 0 4h1c-1 1 0 1-1 2h0l1 1c1-1 2-2 2-4s1-3 2-5l-1-1 1-1 1-1z" class="I"></path><path d="M556 212h0l1-1h-1v-1l3-3c2-2 3-4 5-5-1 2-2 2-1 4v1h0l-1 1c-1 1-1 2-2 4h1v2 1c2-2 4-4 6-5-2 4-5 6-8 9-1 0-2 1-4 1l-1-1c-3 1-5 3-8 4 0-3 3-4 5-6s4-3 5-5z" class="e"></path><path d="M539 152c0-1 1-2 2-3v-1c0 2 0 4-1 6 0 3-1 5-2 7 1 0 1-1 1-1 3-4 3-13 6-15l-1 6 2-2c2 1 0 1 1 2 0 1 0 1 1 2h0c1-2 1-4 2-6h1v1c-1 2-1 6 1 8v1 2c-1 3-1 5-1 8v1l2 4-1 1c1 0 1 0 1-1 1 1 1 1 1 2h1l1-2v4l-1 2-2 3h0l-1 2c0 1 1 2 1 3l1 1 1-1h0c2 0 4-1 5-2l1 1c-1 0-1 1-2 2h0 1 1 1v3l2 1c0-2 1-3 2-5h1c0-1 1-2 2-3l2 1v1c1 1 2 1 3 1-1 2 0 3 0 5l-1 1h2l-1 1-1 1 1 1c-1 2-2 3-2 5s-1 3-2 4l-1-1h0c1-1 0-1 1-2h-1c1-2 1-2 0-4v-2l-2 2c0 1 0 2-1 2-1 1-1 2-2 3-2 1-3 3-5 5l-3 3v1h1l-1 1h0c-1 2-3 3-5 5s-5 3-5 6c3-1 5-3 8-4l1 1c2 0 3-1 4-1 0 1-2 2-3 3l-5 3h1c2 0 4-2 6-3-2 2-4 4-6 5h-1l-1 1-2 1h2 1c2-1 4-2 7-3l2-1c-2 2-6 5-8 8v1h0c-1 1-2 1-3 2h-1 0c2 1 3-1 5-1-2 2-5 3-8 3-6 2-12 3-18 4-4 1-9 2-13 2l2-2c2 0 4 1 6 0h1 3l-1-1c-2 0-2 0-3 1-1 0-2 0-2-1h2c1-1 1-1 3-1h2c1-1 2-1 3-1h3c1-1 1-1 2-1h3v-1h3 1c0-1 0-1 1-1h1l6-2v-1l-3 1h-2-1-1c0 1-1 1-1 1h-1-3c-1 1-1 1-2 1-1-1-1 0-2 0h-4v1c-2-1-2-1-3-1-1 1-2 1-3 1h-1-3 0c1-1 1-1 2-1l-1-1c-1 0-2 1-3 1l-3 1h-2l-1 1h-2-4c-2 1-6 1-8 1-1 2-2 1-3 2h-2c-1 0-1 0-2 1h-2c-1 0-2 0-3 1l-4-1-12 4h0c-1 0-2 1-2 1l-2-1h0v-1l-5 3c-1 0-3 1-4 1-2 1-2 2-5 2v1c-1 0-2 1-3 1l-1 1c-2 0-2 0-3 1-1 0-2 0-2-1l-1 1h-2c-1 0-3 0-4 1h-2c-1 1-2 1-3 1h0c-2 0-2 0-2 1-2 0-2 0-3-1-1 0-1 0-1-1h-1l-1 1v-1h-1c-1 0-2-1-3-1l6-1 4-1 2-1 1-1c-2 0-4 1-5 0l1-1h3v-1l-1-1v-1c-2 1-3 1-4 2l1 1h0c-1 1-1 1-2 0h-1c-1-1-1-1-2-1 1-2 2-5 4-7h1c4-4 7-6 12-7l6-1 8-1h3c1 0 3 0 4-1l2-1 2-1 9-3 1-2 3-1h0c3-2 5-4 7-6l2-2c2-3 5-5 6-9 6-5 11-12 18-17h1c1-3 4-5 7-6 2-2 3-2 5-3 1-1 2-2 4-3s3-4 5-6c1-2 2-4 2-7 1-2 2-3 2-5 1-3 1-6 3-8z" class="G"></path><path d="M491 215c1 0 1 0 2-1l1 1 2-1c0 1 0 1-1 2-2 1-4 3-6 4 0-3 0-3 2-5z" class="J"></path><path d="M537 194l1 1-1 1h1c-2 3-4 3-6 4v-1c1-1 0-1 0-2l5-3z" class="Y"></path><path d="M547 206c1 0 2-1 3-2l-2 4c-1 1-3 3-4 5h-2c1-1 1-1 1-2 1-2 2-4 4-5z" class="J"></path><path d="M506 229h-3c-2 1-5 1-7 1 4-2 11-5 16-3-2 0-4 1-6 2z" class="r"></path><path d="M525 199c3 0 5-1 7-2 0 1 1 1 0 2v1l-1 1h-1l1-1c-2-1-4 3-8 2h0l-1-1c0-1 2-2 3-2z" class="c"></path><path d="M533 192c0 1 1 1 1 2 4-1 5-4 8-6l1-1c-1 1-1 2-1 2l-5 5-5 3c-2 1-4 2-7 2 3-2 4-4 6-6h1l1-1z" class="b"></path><path d="M489 225c2 0 3 0 4 1-1 1-3 2-4 2l-7 4c-1 0-1 1-2 0h0c1-4 6-5 9-7z" class="J"></path><path d="M482 242c2-1 3-2 5-2l13-3c1 1 2 1 3 1-1 0-3 0-4 1h-1c-1 2-2 1-3 2h-2c-1 0-1 0-2 1h-2c-1 0-2 0-3 1l-4-1z" class="r"></path><path d="M551 225h1c2 0 4-2 6-3-2 2-4 4-6 5h-1l-1 1-2 1h2 1c-1 1-2 1-2 1l-1 1c-2-1-8 0-10 0l-4 1c0-1 1-1 1-1h2 1c4-2 8-4 13-6zm-39 2l11-5c2 0 4-1 5-2-2 0-3 1-5 1 4-3 8-3 12-5l3-1v1c-1 1-1 1-2 1l-1 1-1 1c-2 0-3 1-4 2-2 2-21 8-24 8 2-1 4-2 6-2z" class="c"></path><path d="M548 198s1 0 2-1 1-2 3-3h0v2 4c-1 1-2 3-3 4h0c-1 1-2 2-3 2 0-1 1-1 1-2l2-5h0c-3 2-5 6-7 9-1 1-3 2-4 2l2-1c2-2 3-4 3-7h0c1-2 2-3 4-4z" class="I"></path><path d="M551 214c2 0 3-2 5-2-1 2-3 3-5 5s-5 3-5 6h-1c-5 3-11 5-17 6h-1l5-1c4-2 7-3 11-5 1 0 0 0 1-1-2-1-5 1-6 1 0-1 2-1 3-2 3-2 7-4 9-6l1-1z" class="E"></path><path d="M466 245c4-2 8-4 12-4l9-3 2-1 16-5h2l-1 1h2c-8 3-15 4-23 6-3 1-6 4-10 4l-9 3v-1z" class="r"></path><path d="M488 216c1 0 0 0 1-1h1 1c-2 2-2 2-2 5-2 1-4 2-6 4-2 1-2 2-3 3-2 2-6 4-9 6l2-2v-1c1-1 4-3 6-4l-1-1c-1 1-2 1-3 2 1-1 1-2 2-3h0c3-2 5-4 7-6l2-2 1 1 1-1z" class="B"></path><path d="M446 251l6-5v-2l8-7c1 0 2-1 4-2s3-1 5-1c-2 1-4 3-6 4-2 2-4 6-7 7 1 0 2 0 3-1h1s1-1 2-1l2-2 1 1h0c-3 3-9 6-13 7-3 1-4 2-6 2z" class="J"></path><path d="M555 186h0c2 0 4-1 5-2l1 1c-1 0-1 1-2 2h0 1v2c-2 3-3 4-4 7h1c0 2-1 4-2 6l-1 1h0v2l-1-1c-2 1-3 2-3 4h-2l2-4h0c1-1 2-3 3-4v-4-2h0c-2 1-2 2-3 3s-2 1-2 1c1-2 2-4 4-6l1-1 1-4 1-1z" class="S"></path><path d="M555 186h0c2 0 4-1 5-2l1 1c-1 0-1 1-2 2-2 0-3 2-3 3-1 1-1 2-1 3v1c0 2-1 4-2 6v-4-2h0c-2 1-2 2-3 3s-2 1-2 1c1-2 2-4 4-6l1-1 1-4 1-1z" class="X"></path><path d="M534 208c-1 1-2 1-2 1-2 1-4 3-6 3h-1l1-1-1-1-19 6c-1 0 0 0-1-1l10-3v-1c-3 0-6 2-9 2-1 1-2 1-3 0 1 0 3-1 4-1l12-4c2 0 5-2 7-2h2l1-1c2-1 4-2 6-4v2l-3 3 1 1h0 2l-1 1z" class="Q"></path><path d="M543 193h0c1-1 1-2 1-3l1-1c0-1 1-1 2-1h0l-1 2h1 1v1l1 1v-1l2-1c0 1 1 1 1 2-2 2-3 4-4 6-2 1-3 2-4 4-3 3-6 4-10 6l1-1h-2 0l-1-1 3-3v-2l1-1c2-1 2-2 3-4h-1-1l1-1-1-1 5-5v1c0 1-1 2-2 3v1l1-1c1 0 0 0 1-1l1 1z" class="R"></path><path d="M543 193h0c1-1 1-2 1-3l1-1c0-1 1-1 2-1h0l-1 2h1 1v1 1c-1 1-1 2-2 3-1 2-3 5-5 6h0c-1-1 0-1 0-2v-2c1-1 1-3 2-4z" class="O"></path><path d="M542 189v1c0 1-1 2-2 3v1l1-1c1 0 0 0 1-1l1 1c-1 1-1 3-2 4-1 2-1 4-3 6h0 0c-1 1-2 3-3 4h-2 0l-1-1 3-3v-2l1-1c2-1 2-2 3-4h-1-1l1-1-1-1 5-5z" class="B"></path><path d="M541 180l1-1c1-1 2-1 2-2 1-1 1-1 2-1l-1 2 2 1c0-1 1-1 2-2v1c0 1 0 1 1 2h-1v2c1 0 2-2 3-3l1 2-1 2c0 1 1 2 1 3l1 1-1 4-1 1c0-1-1-1-1-2l-2 1v1l-1-1v-1h-1-1l1-2h0c-1 0-2 0-2 1l-1 1c0 1 0 2-1 3h0l-1-1c-1 1 0 1-1 1l-1 1v-1c1-1 2-2 2-3v-1s0-1 1-2l-1 1c-3 2-4 5-8 6 0-1-1-1-1-2 2-2 4-4 6-7 0-2 2-3 2-5z" class="L"></path><path d="M549 191c1-2 1-3 1-5 0-1 1-2 2-3 0 1 1 2 1 3l1 1-1 4-1 1c0-1-1-1-1-2l-2 1z" class="k"></path><path d="M543 187l1-2v-1c-1 0 0 0-1-1h0l1-2 1 1-1 2h1c1-1 1-1 2-1 1 1 1 1 1 2v2 1 2h-1-1l1-2h0c-1 0-2 0-2 1l-1 1c0 1 0 2-1 3h0l-1-1c-1 1 0 1-1 1l-1 1v-1c1-1 2-2 2-3v-1s0-1 1-2z" class="o"></path><path d="M539 185l5-5v1l-1 2h0c1 1 0 1 1 1v1l-1 2-1 1c-3 2-4 5-8 6 0-1-1-1-1-2 2-2 4-4 6-7z" class="f"></path><path d="M474 225l3-1c-1 1-1 2-2 3 1-1 2-1 3-2l1 1c-2 1-5 3-6 4v1l-2 2-2 1c-2 0-3 0-5 1s-3 2-4 2l-8 7v2l-6 5h-1c-1 1-2 1-3 2l-13 2c-2 0-3 0-4-1l4-1c4 0 8 0 11-2v-1l1-1h1c2-1 4-3 6-5-1-1-2-1-3-2v-1c1-2 1-3 1-4-1-1-1-2-1-3l8-1h3c1 0 3 0 4-1l2-1 2-1 9-3 1-2z" class="I"></path><path d="M473 227c-1 1-3 2-5 4-1 1-3 2-5 3v-2l-1-1 2-1 9-3z" class="R"></path><path d="M460 232l2-1 1 1v2c-2 1-7 3-8 3l-2-4h3c1 0 3 0 4-1z" class="a"></path><path d="M445 234l8-1 2 4c-3 2-5 4-7 7-1-1-2-1-3-2v-1c1-2 1-3 1-4-1-1-1-2-1-3z" class="p"></path><path d="M439 235l6-1c0 1 0 2 1 3 0 1 0 2-1 4v1c1 1 2 1 3 2-2 2-4 4-6 5h-1l-1 1v1c-3 2-7 2-11 2l2-1 1-1c-2 0-4 1-5 0l1-1h3v-1l-1-1v-1c-2 1-3 1-4 2l1 1h0c-1 1-1 1-2 0h-1c-1-1-1-1-2-1 1-2 2-5 4-7h1c4-4 7-6 12-7z" class="O"></path><path d="M426 249c-1 0-1-1-2-2l1-1c1-1 2-1 4-1l1 2c-2 1-3 1-4 2z" class="p"></path><path d="M439 235l6-1c0 1 0 2 1 3h-3c-1 1-2 1-3 1h-1v-3z" class="K"></path><path d="M566 186h1c0-1 1-2 2-3l2 1v1c1 1 2 1 3 1-1 2 0 3 0 5l-1 1h2l-1 1-1 1 1 1c-1 2-2 3-2 5s-1 3-2 4l-1-1h0c1-1 0-1 1-2h-1c1-2 1-2 0-4v-2l-2 2c0 1 0 2-1 2-1 1-1 2-2 3-2 1-3 3-5 5l-3 3v1h1l-1 1h0c-2 0-3 2-5 2l4-3v-3c-1 1-2 2-4 2h0c-3 1-5 2-7 3 1-2 3-4 4-5h2c0-2 1-3 3-4l1 1v-2h0l1-1c1-2 2-4 2-6h-1c1-3 2-4 4-7v-2h1 1v3l2 1c0-2 1-3 2-5z" class="X"></path><path d="M559 198c0 1 0 2-1 3h3c-2 2-5 4-6 7-1 0-1 0-2-1l-2 3c-3 1-5 2-7 3 1-2 3-4 4-5h2c0-2 1-3 3-4l1 1v-2h0l1-1c2-1 3-2 4-4z" class="m"></path><path d="M566 186h1c0-1 1-2 2-3l2 1v1h-1v1 1l-1 1c0 1 0 5-1 6l-3 5-1 2h-1c0-2-1-4 0-6h0v-1l-4 4c-1 2-2 3-4 4 1-2 2-4 2-6h-1c1-3 2-4 4-7v-2h1 1v3l2 1c0-2 1-3 2-5z" class="H"></path><path d="M560 187h1 1v3l2 1c-1 2-2 3-3 4-1 0-2 0-2-1 1-2 1-2 1-4v-1-2zm5 12l-1-1c1-2 1-4 2-5 1-2 1-5 2-6l1 1c0 1 0 5-1 6l-3 5z" class="I"></path><path d="M541 180c0 2-2 3-2 5-2 3-4 5-6 7l-1 1h-1c-2 2-3 4-6 6-1 0-3 1-3 2l-5 2c-3 2-8 2-11 4-2 1-3 2-5 3s-3 2-5 4h0l-2 1-1-1c-1 1-1 1-2 1h-1-1c-1 1 0 1-1 1l-1 1-1-1c2-3 5-5 6-9 6-5 11-12 18-17h1c1-3 4-5 7-6 2-2 3-2 5-3l-3 4c-1 1-1 1-2 1-1 1-2 1-3 2h0l1 1c0 1-1 1-2 2 1 0 2-1 4-1l1-1 1 1h1 2c2 0 3 0 4-1l1-1c1 0 2-1 3-2h1 1v-1c2-1 3-3 5-3 1-1 2-2 3-2z" class="Q"></path><path d="M541 180c0 2-2 3-2 5-2 3-4 5-6 7l-1 1h-2-1 0l1-1v-1h-3c4-3 8-5 11-9 1-1 2-2 3-2z" class="R"></path><path d="M520 194l7-3h3v1l-1 1h0 1 2-1c-2 2-3 4-6 6-1 0-3 1-3 2l-5 2c-3 2-8 2-11 4-2 1-3 2-5 3s-3 2-5 4h0l-2 1-1-1c-1 1-1 1-2 1h-1-1c-1 1 0 1-1 1 0-1 1-2 2-2 6-4 10-10 17-14 2-1 6-5 9-5h1c1-1 1-1 3-1z" class="Y"></path><path d="M520 194l7-3h3v1l-1 1h0 1 2-1c-2 1-3 2-4 3l-1-1h0l-3 1v-1l1-1h-4z" class="o"></path><path d="M488 216c0-1 1-2 2-2 6-4 10-10 17-14 2-1 6-5 9-5h1c1-1 1-1 3-1h4l-1 1v1c-1 1-2 1-3 1-1 1-1 1-2 1h-1v-1c-7 1-12 5-17 10-1 1-3 2-4 4l-1 1c0 1-1 2-2 2-1 1-1 1-2 1h-1-1c-1 1 0 1-1 1z" class="E"></path><path d="M539 152c0-1 1-2 2-3v-1c0 2 0 4-1 6 0 3-1 5-2 7 1 0 1-1 1-1 3-4 3-13 6-15l-1 6 2-2c2 1 0 1 1 2 0 1 0 1 1 2h0c1-2 1-4 2-6h1v1c-1 2-1 6 1 8v1 2c-1 3-1 5-1 8v1l2 4-1 1c1 0 1 0 1-1 1 1 1 1 1 2h1l1-2v4l-1 2-2 3h0l-1-2c-1 1-2 3-3 3v-2h1c-1-1-1-1-1-2v-1c-1 1-2 1-2 2l-2-1 1-2c-1 0-1 0-2 1 0 1-1 1-2 2l-1 1c-1 0-2 1-3 2-2 0-3 2-5 3v1h-1-1c-1 1-2 2-3 2l-1 1c-1 1-2 1-4 1h-2-1l-1-1-1 1c-2 0-3 1-4 1 1-1 2-1 2-2l-1-1h0c1-1 2-1 3-2 1 0 1 0 2-1l3-4c1-1 2-2 4-3s3-4 5-6c1-2 2-4 2-7 1-2 2-3 2-5 1-3 1-6 3-8z" class="n"></path><path d="M549 170l2-2 2 4c-2 1-2 1-3 2v1l-2-3 1-2z" class="h"></path><path d="M547 163c2-1 2-2 3-3v-1h2c-1 3-1 5-1 8v1l-2 2v-2-3c-1 0-1 1-2 2 0-1-1-3 0-4z" class="i"></path><path d="M539 163l5-5h0c-1 2-2 3-3 5l2-2c0-1 1-1 2-2v2c-1 1-1 2-1 3l-2 8c0-1-1-2-1-2l-1-1 1-2c0-1-1-1-1-2 0 0 0-1-1-2z" class="j"></path><path d="M542 172l2-8c1 3 1 4 0 7v1h1 0c1-1 1-2 2-3l1 1c0 2-1 3-1 5l-1 1c-1 0-1 0-2 1 0 1-1 1-2 2l-1 1c-1 0-2 1-3 2-2 0-3 2-5 3 2-3 7-6 8-9l-1-1 2-3z" class="m"></path><path d="M539 163c1 1 1 2 1 2 0 1 1 1 1 2l-1 2 1 1s1 1 1 2l-2 3-8 5h0c1-2 2-3 2-5 0 0 0-1 1-2v-1-1c1-1 1-2 2-3l2-5z" class="U"></path><path d="M537 168l2 2c0 1 0 1 1 2l-3 3c-2-1-2-2-2-3v-1c1-1 1-2 2-3z" class="K"></path><path d="M550 147h1v1c-1 2-1 6 1 8v1 2h-2v1c-1 1-1 2-3 3v-4l-1 1c0-1 0-3-1-4 0-1 1-1 0-3-1 2-1 2-2 2l1-4 2-2c2 1 0 1 1 2 0 1 0 1 1 2h0c1-2 1-4 2-6z" class="C"></path><defs><linearGradient id="b" x1="532.632" y1="177.397" x2="527.368" y2="190.103" xlink:href="#B"><stop offset="0" stop-color="#c7c5c4"></stop><stop offset="1" stop-color="#ecebe8"></stop></linearGradient></defs><path fill="url(#b)" d="M540 175l1 1c-1 3-6 6-8 9v1h-1-1c-1 1-2 2-3 2l-1 1c-1 1-2 1-4 1h-2-1c2-1 4-2 6-4 1-1 2-3 4-4l2-2c3-2 5-3 8-5z"></path><path d="M532 172h1s1-1 1-2 0-1 1-2v3 1 1c-1 1-1 2-1 2 0 2-1 3-2 5h0l-2 2c-2 1-3 3-4 4-2 2-4 3-6 4l-1-1-1 1c-2 0-3 1-4 1 1-1 2-1 2-2l-1-1h0c1-1 2-1 3-2 1 0 1 0 2-1l3-4c1-1 2-2 4-3s3-4 5-6z" class="J"></path><path d="M532 172h1s1-1 1-2 0-1 1-2v3 1 1c-1 1-1 2-1 2 0 2-1 3-2 5v-1-3h0c-2-1-3 2-4 2h-1c2-1 3-4 5-6z" class="B"></path><path d="M412 688l18 8c4 3 11 5 17 7h0v25 17 9 29 13 3c-4 3-8 6-9 11v1c-1 2 0 4 0 6-1 1-1 3-1 4l1 1c-1 2-2 3-4 4l-3 1 2 1c-1 2-6 5-8 6 0 0-1-1 0-2 0-1 1-1 1-2s-1-2-1-3l-1-9v-1c-1-2 0-6 0-7h1 0l1-6-3-1h-1c-3-1-7-4-8-7 0-1-1-2-2-3v-2c-2-2-2-4-4-6-1-2-3-3-3-5 0-1 0-1-1-2l-1-3-1-1-1 1v-1h-1c-1 1-1 1-2 1h0-2l-1-1-4 2v1h-2c0-2-1-3-2-5h0c-7-5-12-11-16-18l-1-2-1-5c-1-3-1-5-3-8-3-3-6-6-11-8-1 0-2-1-3-2v-1c1 0 1-1 2-1h1c1 0 2 1 3 1 2 1 3 1 5 3h2c1 1 2 1 3 1 2 1 3 2 5 2 0-3-1-6 0-9h0v-2h1 2c4 1 8 1 12 1l-1-1h-4 1c0-2 0-2-1-3l1-4c-1-1-2-3-3-4 1-1 0-2 0-4l1-1c1 0 1 0 1 1 2 0 3 1 4 1l1-1v-1l-2-1 1-1-3-3c0-1 1-2 1-2l-1-2 1-1h2l3 2 2-2c1 2 2 4 3 5 3 2 5 4 8 4l1 1 1 1c1 0 2-1 4 0h1s0-1 1-1l3-3 1-1c-1-3-2-7-4-10l-1-2v-1c0-1 1-2 2-2z" class="p"></path><path d="M419 781c2 1 3 3 6 3v1l-3 1c-2-2-2-3-3-5z" class="a"></path><path d="M425 785v1c2 0 3 0 5 1h0v1h-1c-1 1 1 2 1 3h3c-2 2-3 3-4 5h0-2l-1-1s-1-1-2-1h-1l1-1 1-1c0-2-1-2-2-4 0-1 0-1-1-2l3-1z" class="b"></path><path d="M425 785v1h0l2 4v1l1 1c0 1-1 2-2 3 0 0-1-1-2-1h-1l1-1 1-1c0-2-1-2-2-4 0-1 0-1-1-2l3-1z" class="Z"></path><path d="M390 749l2-2 3 3v1h0c1 0 1 0 2 1s2 1 3 1l1-2 1 1v1c1 0 2 1 3 2l1 3c2 0 3 1 5 3v1h1 0 1 0v1c1 1 1 2 1 3 1 0 1 1 1 2 1 2 2 3 4 3 0 1 1 2 2 3s2 2 4 3l1 2c-2-1-5-2-7-3h0c-5-3-8-7-12-11-4-2-8-5-12-7l2-2-3-3-4-4z" class="B"></path><path d="M402 753c1 0 2 1 3 2l1 3c-2-1-3-1-4-2-1-2 0-2 0-3z" class="F"></path><path d="M378 750l1-1h1l2 1h1l1 1 1 1c1 0 2 0 3-1 1 1 1 1 3 2v-1h0c1 1 2 1 3 1l3 3-2 2-3-1h-4c-2 0-3 0-4 2h0c1 1 1 1 2 0h4c2 3 2 2 5 3 0 1 1 2 2 2l1 1h-2-1 0c1 1 1 2 2 3 0 2 0 3-2 5v1l-4 2v1h-2c0-2-1-3-2-5h0c-7-5-12-11-16-18l-1-2h1 2c1-1 1-1 2-1s2 0 3-1z" class="K"></path><path d="M391 753v-1h0c1 1 2 1 3 1l3 3-2 2-3-1c-2-1-5-2-7-2l2-2 1 1h1l2-1z" class="V"></path><path d="M378 750l1-1h1l2 1h1l1 1 1 1c1 0 2 0 3-1 1 1 1 1 3 2l-2 1h-1l-1-1-2 2c-3 0-7-1-10-1h-1c-2 0-2-1-3 0l-1-2h1 2c1-1 1-1 2-1s2 0 3-1z" class="q"></path><path d="M378 750l1-1h1l2 1h1l1 1h0l-1 1h-1l-1-1-2 2h-3l-1 1h0-1c-2 0-2-1-3 0l-1-2h1 2c1-1 1-1 2-1s2 0 3-1z" class="m"></path><path d="M371 754c1-1 1 0 3 0 0 3 3 3 4 6 0 1 1 1 2 2h1 1c2 0 2 0 3 1h2l1 2h0v-2c1 0 1 0 1 1 1 1 3 2 4 3 0 2 0 4 1 5 1 0 1 0 1 1v1l-4 2v1h-2c0-2-1-3-2-5h0c-7-5-12-11-16-18z" class="a"></path><path d="M390 771c2 0 3-1 4 1h-1c-1 1-1 2-2 4v1h-2c0-2-1-3-2-5h0 1c1 0 2-1 2-1z" class="P"></path><path d="M371 754c1-1 1 0 3 0 0 3 3 3 4 6 0 1 1 1 2 2h1 1v3c2 1 3 0 4 2l2 2h0l-1-2 1-1c1 2 1 2 1 4h1v1s-1 1-2 1h-1 0 0c-7-5-12-11-16-18z" class="N"></path><path d="M397 768c-1-1-1-2-2-3h0 1 2c5 4 10 7 14 11l2 2c2 0 3 1 3 2h1l1 1c1 2 1 3 3 5 1 1 1 1 1 2 1 2 2 2 2 4l-1 1-1 1h1c1 0 2 1 2 1l1 1h2c-1 2-1 3-2 5v2l-1 1-3-1h-1c-3-1-7-4-8-7 0-1-1-2-2-3v-2c-2-2-2-4-4-6-1-2-3-3-3-5 0-1 0-1-1-2l-1-3-1-1-1 1v-1h-1c-1 1-1 1-2 1h0-2l-1-1v-1c2-2 2-3 2-5z" class="L"></path><path d="M397 768c2 2 4 4 5 6l-1 1v-1h-1c-1 1-1 1-2 1h0-2l-1-1v-1c2-2 2-3 2-5z" class="T"></path><path d="M403 775l1-1c3 0 3 1 5 3 1 1 1 2 2 3l-2 3-1 2c-1-2-3-3-3-5 0-1 0-1-1-2l-1-3z" class="m"></path><path d="M409 783l2-3c0 1 1 2 1 3l1 1 1-1v3c1 1 2 2 2 3h2v1h-1c0 1 0 1 1 1v1c1 0 1 1 1 2l1-1h3c-1-2-2-1-2-3h1c1 1 1 2 2 3l-1 1h1c1 0 2 1 2 1l1 1h2c-1 2-1 3-2 5v2l-1 1-3-1h-1c-3-1-7-4-8-7 0-1-1-2-2-3v-2c-2-2-2-4-4-6l1-2z" class="M"></path><path d="M409 783l2-3c0 1 1 2 1 3l-2 2-1-2z" class="e"></path><path d="M418 791v1c1 0 1 1 1 2l1-1h3l-1 1-1 1 1 1c0 1 0 2-1 3l-1 1h-1l1-1-1-1-1 1h0l1-2c-2-2-1-2-2-4 0-1 0-1 1-2z" class="S"></path><path d="M423 793c-1-2-2-1-2-3h1c1 1 1 2 2 3l-1 1h1c1 0 2 1 2 1l1 1h2c-1 2-1 3-2 5v2l-1 1-3-1h1s0-1-1-1c0-1-1-2-2-3 1-1 1-2 1-3l-1-1 1-1 1-1z" class="j"></path><path d="M427 796h2c-1 2-1 3-2 5v2l-1 1-3-1h1s1 0 1-1 1-2 0-3c0-1-1-1-1-2 1 0 2 0 3-1h0z" class="R"></path><path d="M433 791c3-2 6-3 11-3l1 1c0 1-1 1-1 2 0 2 0 3-1 4 2 0 2-1 3 0l-2 2c-2 1-3 3-4 5-1 1-2 3-3 5 3-4 6-8 10-11h0v3c-4 3-8 6-9 11v1c-1 2 0 4 0 6-1 1-1 3-1 4l1 1c-1 2-2 3-4 4l-3 1 2 1c-1 2-6 5-8 6 0 0-1-1 0-2 0-1 1-1 1-2s-1-2-1-3l-1-9v-1c-1-2 0-6 0-7h1 0l1-6 1-1v-2c1-2 1-3 2-5h0c1-2 2-3 4-5z" class="T"></path><path d="M432 825c2-1 3-1 4-2-2-3-2-10-1-14 1-5 4-9 8-13l1 1c-2 1-3 3-4 5-1 1-2 3-3 5 3-4 6-8 10-11h0v3c-4 3-8 6-9 11v1c-1 2 0 4 0 6-1 1-1 3-1 4l1 1c-1 2-2 3-4 4l-3 1h-1 0c0-1 1-1 2-2h0z" class="e"></path><path d="M431 817v-1c0-2 0-3 1-5v-2c0-2 1-3 2-4 0-1 0-2 1-2v2l-1 4-1 1c0 4 0 8 1 12-1 0-1 0-1 1l-1 2h0c-1 1-2 1-2 2h0 1l2 1c-1 2-6 5-8 6 0 0-1-1 0-2 0-1 1-1 1-2s-1-2-1-3l-1-9c2 0 1 4 2 5h0l1-1v-2l-1-1v-1-2-1c0-1 0-2 1-3v4c1 1 2 1 3 2l1-1z" class="M"></path><path d="M433 791c3-2 6-3 11-3-2 2-3 2-4 4 1 0 2-1 3-2v1c-2 1-4 4-6 5-2 3-4 6-5 10h0c-1 3-2 8-1 11l-1 1c-1-1-2-1-3-2v-4c-1 1-1 2-1 3v1 2 1l1 1v2l-1 1h0c-1-1 0-5-2-5v-1c-1-2 0-6 0-7h1 0l1-6 1-1v-2c1-2 1-3 2-5h0c1-2 2-3 4-5z" class="q"></path><path d="M429 796c2 0 2 1 3 2-1 0-2 1-2 2l-1 3h-1v-2h-1c1-2 1-3 2-5h0z" class="e"></path><path d="M433 791c3-2 6-3 11-3-2 2-3 2-4 4 1 0 2-1 3-2v1c-2 1-4 4-6 5l-2 2c0 1-1 1-2 2v-1h0c0-1 1-2 1-2l1-1v-1l-3 3c-1-1-1-2-3-2 1-2 2-3 4-5zm9-50c1 0 2 1 4 2v1l1 1v9 29h-1c-7-1-14-2-20-4l-1-2c-2-1-3-2-4-3s-2-2-2-3c-2 0-3-1-4-3 0-1 0-2-1-2 0-1 0-2-1-3v-1h0l-1-3h2 0v-3c1-2 0-2 2-4 0 1 0 2 1 3l1-1v-1c1-1 1-2 1-3v-1c1-2 1-3 1-5h2 1l2 1v-3h2v3c1 0 2-2 3-2 0 1 0 1-1 2h1l1-1 2 1c1 0 0 0 1-1 1 0 2-1 4-1h0c1-1 2-2 4-2z" class="M"></path><path d="M446 743v1l1 1v9h0c0 1 0 2-1 3h-1c-1-1 0-2 0-3h-2c0-1 0-1-1-1l-1 1c-2 0-1-3-2-3-2-1-2 0-3-2v-1c2 0 2 0 4 1 0 1 0 1 1 2 0-1 2-2 3-2 0 0 0 1 2 1l-1-1-1-1c0-1 1-2 1-2s0-2 1-3z" class="R"></path><path d="M424 766h1c0-3-1-5-1-7l1-1v-2c1-1 1-1 2-1l-1 3v2c-1 2 0 4 1 6l2 3c4 5 10 8 16 10h0v1h0c-3 0-5-1-7-2l-3-1c-1 0-2-1-3-1h0c-1-1-1-1-2-1h0l-3-2h0l-3-5c0-1-1-1-1-2h1 0z" class="i"></path><path d="M427 755c1-2 2-5 4-6l1 1c2 2 4 3 7 4 2 1 3 3 5 4 0 0 2 1 3 2 0 2-1 5-1 7h-2v-3c0-1 0-1-1-1 0-2-2-4-3-4l-2 2-1-1h1c-1-1 0-1-1-2l1-1-3-3-2 1c-3 1-4 2-5 5l-2-2 1-3z" class="T"></path><path d="M428 760c1-3 2-4 5-5l2-1 3 3-1 1c1 1 0 1 1 2h-1l1 1 2-2c1 0 3 2 3 4 1 0 1 0 1 1v3h2c1 3 1 9 0 12h-1 0c-6-2-12-5-16-10l-2-3c-1-2-2-4-1-6v-2l2 2z" class="R"></path><path d="M427 766c-1-2-2-4-1-6v-2l2 2-1 1h2v1l-2 1h1 1c-1 1-1 2-2 3z" class="a"></path><path d="M446 767c1 3 1 9 0 12h-1 0c-1-2-2-4-2-5h1 1l-1-2 1-2c0-1 0-2-1-3h2z" class="b"></path><path d="M437 760l1 1 2-2c1 0 3 2 3 4 1 0 1 0 1 1-1 1-2 2-2 3-2 2-4 2-6 5h0c-1 0-1-1-2-1h-1c1-1 1-2 2-2-1-1-1-1-1-2l1-1v-1h-1c1-3 1-4 3-5z" class="Z"></path><path d="M420 744h2 1l2 1v-3h2v3c1 0 2-2 3-2 0 1 0 1-1 2h1l1-1 2 1c-3 2-5 4-7 7-1 1-1 3-2 5s-1 5 0 8v1h0-1c0 1 1 1 1 2l3 5h0l3 2h0c3 3 9 5 13 6 1 0 2 1 3 2-7-1-14-2-20-4l-1-2c-2-1-3-2-4-3s-2-2-2-3c-2 0-3-1-4-3 0-1 0-2-1-2 0-1 0-2-1-3v-1h0l-1-3h2 0v-3c1-2 0-2 2-4 0 1 0 2 1 3l1-1v-1c1-1 1-2 1-3v-1c1-2 1-3 1-5z" class="H"></path><path d="M388 724l1-1v-2h1c0 1 1 2 1 3 1 1 3 0 5 1 2 0 4 1 6 1l2 1h1l3 1 2 2v1l2-1c3 3 4 5 6 8l1 3c0 1 0 2 1 3 0 2 0 3-1 5v1c0 1 0 2-1 3v1l-1 1c-1-1-1-2-1-3-2 2-1 2-2 4v3h0-2l1 3h-1 0-1v-1c-2-2-3-3-5-3l-1-3c-1-1-2-2-3-2v-1l-1-1-1 2c-1 0-2 0-3-1s-1-1-2-1h0v-1l-3-3-2 2 4 4c-1 0-2 0-3-1h0v1c-2-1-2-1-3-2-1 1-2 1-3 1l-1-1-1-1h-1l-2-1h-1l-1 1c-1 1-2 1-3 1s-1 0-2 1h-2-1l-1-5c-1-3-1-5-3-8-3-3-6-6-11-8-1 0-2-1-3-2v-1c1 0 1-1 2-1h1c1 0 2 1 3 1 2 1 3 1 5 3h2c1 1 2 1 3 1 2 1 3 2 5 2 0-3-1-6 0-9h0v-2h1 2c4 1 8 1 12 1z" class="W"></path><path d="M405 727l3 1 2 2c-1 1-3 0-4 0-1-1-1-2-2-3h1zm5 13l1-1-3-3h1c2 1 2 1 3 3v2l-1 2c-1-1-2-1-2-2l1-1z" class="B"></path><path d="M410 731l2-1c3 3 4 5 6 8l1 3c0 1 0 2 1 3 0 2 0 3-1 5v1c0 1 0 2-1 3v1l-1 1c-1-1-1-2-1-3-2 2-1 2-2 4v3h0-2l1 3h-1 0-1v-1c-2-2-3-3-5-3l-1-3c-1-1-2-2-3-2v-1l1-1v-1h1v-1l4-1v-2l-1-2c-2-1-4-3-5-5h0 0c1 0 2 1 3 2h1l-1-2c2 0 3 0 5 1l-1 1c0 1 1 1 2 2l1-2v-2l2 1v-2c0-3-2-6-4-7z" class="I"></path><g class="D"><path d="M418 738l1 3-2 2-1-1c0-2 1-2 2-4z"></path><path d="M412 759c0-1-1-2-3-2l-1-1h1l1-1h0l1-1c0-2-1-4-1-5v-1l1 1h1l2 1c1-1 1-1 1-3-1 0-1 0-3-1 1-1 1-1 1-2h1v-2c1 1 2 2 2 3 1 1 1 1 1 3v2l1 1c-1 1-1 1-2 1-2 2-1 2-2 4v3h0-2z"></path></g><path d="M355 727c1 0 2 1 3 1 2 1 3 1 5 3h2c1 1 2 1 3 1 2 1 3 2 5 2h-3c3 2 5-1 7 3 1-1 2-1 3 0 2 1 4 1 6 2s4 1 6 1v1l1-1v-1h-1l-4-2 1-1c2 1 6 3 8 5v1c1 1 2 1 3 2h0c1-1 2 0 3 0l1-1c-1-1-2-1-3-2h0c-1-1-2-1-3-1h0l1-1h1v-1h2v1h0c1 2 3 4 5 5l1 2v2l-4 1v1h-1v1l-1 1-1-1-1 2c-1 0-2 0-3-1s-1-1-2-1h0v-1l-3-3-2 2 4 4c-1 0-2 0-3-1h0v1c-2-1-2-1-3-2-1 1-2 1-3 1l-1-1-1-1h-1l-2-1h-1l-1 1c-1 1-2 1-3 1s-1 0-2 1h-2-1l-1-5c-1-3-1-5-3-8-3-3-6-6-11-8-1 0-2-1-3-2v-1c1 0 1-1 2-1h1z" class="B"></path><path d="M355 731c2 0 3 0 4 1 4 1 6 3 10 5-1 1-1 1-1 3l-2-1c-3-3-6-6-11-8z" class="L"></path><path d="M383 744c1-1 1-2 2-2l1-1c2 0 3 1 5 2h0c1 0 1 1 2 1l1 1 4 4c1 0 1 0 2-1h1v-1c2 0 2 1 3 2v1h-1v1l-1 1-1-1-1 2c-1 0-2 0-3-1s-1-1-2-1h0v-1l-3-3-2 2-7-5z" class="E"></path><path d="M369 737c5 2 9 4 14 7l7 5 4 4c-1 0-2 0-3-1h0v1c-2-1-2-1-3-2-1 1-2 1-3 1l-1-1-1-1h-1l-2-1h-1l-1 1c-1 1-2 1-3 1s-1 0-2 1h-2-1l-1-5c-1-3-1-5-3-8l2 1c0-2 0-2 1-3z" class="L"></path><path d="M366 739l2 1s1 1 2 1c1 2 2 3 3 5h-1l-1-1-1 1 1 1h-2c-1-3-1-5-3-8z" class="N"></path><path d="M369 747h2l2 1c-1 1-1 1-1 2h1l5-2v1h0v1c-1 1-2 1-3 1s-1 0-2 1h-2-1l-1-5z" class="S"></path><path d="M369 737c5 2 9 4 14 7l7 5 4 4c-1 0-2 0-3-1h0v1c-2-1-2-1-3-2-1 1-2 1-3 1l-1-1-1-1h-1l-2-1c1-1 2-1 4-1l-2-3h-3l-3 1v-1l2-2-1-1-1 1c-1 0-3 0-4-1-1 0-1-1-2-1s-2-1-2-1c0-2 0-2 1-3z" class="Z"></path><path d="M380 749c1-1 2-1 4-1h0 3 0c-1 1-1 1-2 1 1 1 2 1 3 2-1 1-2 1-3 1l-1-1-1-1h-1l-2-1z" class="N"></path><path d="M412 688l18 8c4 3 11 5 17 7h0v25 17l-1-1v-1c-2-1-3-2-4-2-2 0-3 1-4 2h0c-2 0-3 1-4 1-1 1 0 1-1 1l-2-1-1 1h-1c1-1 1-1 1-2-1 0-2 2-3 2v-3h-2v3l-2-1h-1-2c-1-1-1-2-1-3l-1-3c-2-3-3-5-6-8l-2 1v-1l-2-2-3-1h-1l-2-1c-2 0-4-1-6-1-2-1-4 0-5-1 0-1-1-2-1-3h-1v2l-1 1-1-1h-4 1c0-2 0-2-1-3l1-4c-1-1-2-3-3-4 1-1 0-2 0-4l1-1c1 0 1 0 1 1 2 0 3 1 4 1l1-1v-1l-2-1 1-1-3-3c0-1 1-2 1-2l-1-2 1-1h2l3 2 2-2c1 2 2 4 3 5 3 2 5 4 8 4l1 1 1 1c1 0 2-1 4 0h1s0-1 1-1l3-3 1-1c-1-3-2-7-4-10l-1-2v-1c0-1 1-2 2-2z" class="f"></path><path d="M423 710c2 0 2 2 4 2s2 0 3 1v-1c1 1 1 1 1 2l2 2-2 2a30.44 30.44 0 0 1-8-8z" class="o"></path><path d="M408 718h1c2-2 4-3 5-4v-2c-1-1-1-1 0-2h1v1l2 2-1 1v1l3 3h-2v1c-1 0-2 0-3-1h-6z" class="b"></path><path d="M415 710l12 9c5 4 10 8 16 11h-1l-5-2c-1 0-1 0-2 1 1 0 1 0 2 1h-1l-5-2c-1-2-3-4-5-4l-1-1-1-1-5-4-3-3v-1l1-1-2-2v-1z" class="e"></path><path d="M388 708v-1h2l2 1v1l3 3c1 1 1 2 3 2-1 1-1 1-2 1h-1v1c-2 2-2 3-4 3h-1c-1 1-3 0-5 0l-1-3c-1-1-2-3-3-4 1-1 0-2 0-4l1-1c1 0 1 0 1 1 2 0 3 1 4 1l1-1z" class="s"></path><path d="M381 708l1-1c1 0 1 0 1 1 2 0 3 1 4 1 0 2-1 5 0 7l3 3c-1 1-3 0-5 0l-1-3c-1-1-2-3-3-4 1-1 0-2 0-4z" class="F"></path><path d="M384 716l1 3c2 0 4 1 5 0h1c2 0 2-1 4-3l2 2c1 0 2-1 3-1l1 1h2 1c1 1 2 0 3 0h1 6l-1 3-1 1c-1 1-1 1-2 1l1 1-2 1 1 2-2 1-3-1h-1l-2-1c-2 0-4-1-6-1-2-1-4 0-5-1 0-1-1-2-1-3h-1v2l-1 1-1-1h-4 1c0-2 0-2-1-3l1-4z" class="m"></path><path d="M384 716l1 3 2 4h-4 1c0-2 0-2-1-3l1-4z" class="T"></path><g class="N"><path d="M395 716l2 2c1 0 2-1 3-1l1 1v2c-2 0-3 0-6 2h1l-1 1c0-1 0-1-1-1v2c-2-1-3-3-3-5 2 0 2-1 4-3z"></path><path d="M401 718h2 1c1 1 2 0 3 0h1 6l-1 3-1 1c-1 1-1 1-2 1l1 1-2 1 1 2-2 1-3-1 1-2c-1-1-2-2-3-2h-1l-1-1 1-2h-1v-2z"></path></g><path d="M401 718h2 1c1 1 2 0 3 0h1 6l-1 3-1 1-2-1c-1 0-2 1-2 0-1 0-1-1-2-1l-1-1v1h-2v1 2h-1l-1-1 1-2h-1v-2z" class="R"></path><path d="M384 702c0-1 1-2 1-2l-1-2 1-1h2l3 2 2-2c1 2 2 4 3 5 3 2 5 4 8 4l1 1 1 1c1 0 2-1 4 0h1s0-1 1-1l3-3 1-1c-1 4-1 7-4 9-2 1-4 2-6 2-5-1-8-4-13-6l-2-1h-2v1-1l-2-1 1-1-3-3z" class="F"></path><path d="M384 702c0-1 1-2 1-2l-1-2 1-1h2l3 2 2-2c1 2 2 4 3 5v1 1h0l-1-1h-1l-1 1-2-2-1 1c0 1 0 3 1 4h-2v1-1l-2-1 1-1-3-3z" class="X"></path><path d="M414 718c1 1 2 1 3 1v-1h2l5 4 1 1 1 1c2 0 4 2 5 4l5 2h1c-1-1-1-1-2-1 1-1 1-1 2-1l5 2h1s2 1 3 1l1-3h0v17l-1-1v-1c-2-1-3-2-4-2-2 0-3 1-4 2h0c-2 0-3 1-4 1-1 1 0 1-1 1l-2-1-1 1h-1c1-1 1-1 1-2-1 0-2 2-3 2v-3h-2v3l-2-1h-1-2c-1-1-1-2-1-3l-1-3c-2-3-3-5-6-8l-2 1v-1l-2-2 2-1-1-2 2-1-1-1c1 0 1 0 2-1l1-1 1-3z" class="o"></path><path d="M431 733h2l1-1c1 1 1 2 2 3l-1 2 1 1h-1c-2 0-3 1-4 1-1-1-1-1-1-2s0-2 1-3v-1h0z" class="N"></path><path d="M440 740v-2h1c0 1 1 1 2 1h1c-1-2-3-2-4-3s-2-3-3-4l4 2c1 1 3 2 5 3 1 1 0 5 0 7v-1c-2-1-3-2-4-2l-2-1z" class="P"></path><path d="M440 740l2 1c-2 0-3 1-4 2h0c-2 0-3 1-4 1-1 1 0 1-1 1l-2-1-1 1h-1c1-1 1-1 1-2l1-1c0-1 1-1 2-1v-1h7z" class="h"></path><path d="M427 732l3 1h1v1c-1 1-1 2-1 3s0 1 1 2c1 0 2-1 4-1-2 1-3 2-4 3v1l-1 1c-1 0-2 2-3 2v-3h0c0-3-1-4-3-6h0l-2-2 1-1h2l1-1h1z" class="i"></path><path d="M427 732l3 1-2 2c0 1 1 1-1 1l-1-1-2 1h0l-2-2 1-1h2l1-1h1z" class="X"></path><path d="M424 722l1 1 1 1c2 0 4 2 5 4h0l2 2-1 1-1 2h0-1l-3-1h-1c-3-1-5-2-8-5l1-3 2 1v-1c1 0 0 0 1-1h0l2 1v-1-1z" class="N"></path><path d="M426 727c2 2 3 3 6 4l-1 2h0-1l-3-1c-1-2-1-3-1-5z" class="g"></path><path d="M419 724l2 1v-1l5 3c0 2 0 3 1 5h-1c-3-1-5-2-8-5l1-3z" class="K"></path><path d="M414 718c1 1 2 1 3 1v-1h2l5 4v1 1l-2-1h0c-1 1 0 1-1 1v1l-2-1-1 3c3 3 5 4 8 5l-1 1h-2l-1 1 2 2h0c2 2 3 3 3 6h0-2v3l-2-1h-1-2c-1-1-1-2-1-3l-1-3c-2-3-3-5-6-8l-2 1v-1l-2-2 2-1-1-2 2-1-1-1c1 0 1 0 2-1l1-1 1-3z" class="C"></path><path d="M416 730l-2-2v-3l2-1 2 1h0v2c3 3 5 4 8 5l-1 1h-2l-1 1 2 2c0 1 0 1-1 2h-1c-1-1-1-2-1-4h0c-2-1-3-3-5-4z" class="D"></path><path d="M414 718c1 1 2 1 3 1v-1h2l5 4v1 1l-2-1h0c-1 1 0 1-1 1v1l-2-1-1 3v-2h0l-2-1-2 1v3l2 2v1 1c-1 0-2-1-3-2h-1l-2 1v-1l-2-2 2-1-1-2 2-1-1-1c1 0 1 0 2-1l1-1 1-3z" class="j"></path><path d="M411 724v3c1-3 3-3 5-5l1 1h2v1l-1 3v-2h0l-2-1-2 1v3l2 2v1 1c-1 0-2-1-3-2h-1l-2 1v-1l-2-2 2-1-1-2 2-1z" class="V"></path><path d="M412 688l18 8c4 3 11 5 17 7h0v25h0l-1-2c-3-1-5-3-8-4-1-1-2-1-3-2v-1c-2 0-3 0-4-1l2-2-2-2c0-1 0-1-1-2v1c-1-1-1-1-3-1s-2-2-4-2l-2-1v1l-1-1c0-2-1-2-2-3v-1h1c-3-2-2-5-3-7v-1-1c0-1 0 0-1-1l-1 1c-1-1-1-2-2-3h0-1l-1-2v-1c0-1 1-2 2-2z" class="p"></path><path d="M447 703h0v25h0l-1-2c-3-1-5-3-8-4-1-1-2-1-3-2v-1c-2 0-3 0-4-1l2-2c2 1 3 2 5 4 1 0 2 1 3 0 0 0 1-1 1-2 1 1 2 2 4 3 1-2 0-4 0-5s-1-1-1-2l-1-1c-1-1-1-1-1-2 1 1 2 1 3 1v-1c-1 0-2-1-3-1v-1c2 0 2 0 3 1 1-2 1-5 1-7z" class="N"></path><path d="M412 693c2 0 3 0 4 1 1 2 1 3 2 5 1 1 3 2 4 3h1v-1h0c-1 0-1-1-2-1-1-1-1-1-2-1v-3h1l1 1h-1 2c0-1 3 0 3 1h1c0 1 0 2 1 3h1l1-1c-1 1-1 3-1 4l-1 1c-1 0-1 1-2 1h-1l2 3-1 1 2 2c-2 0-2-2-4-2l-2-1v1l-1-1c0-2-1-2-2-3v-1h1c-3-2-2-5-3-7v-1-1c0-1 0 0-1-1l-1 1c-1-1-1-2-2-3h0z" class="U"></path><path d="M429 700c1-1 1-1 2 0 1 2 2 4 2 6 2 4 5 9 9 12 0 1-1 2-1 2-1 1-2 0-3 0-2-2-3-3-5-4l-2-2c0-1 0-1-1-2v1c-1-1-1-1-3-1l-2-2 1-1-2-3h1c1 0 1-1 2-1l1-1c0-1 0-3 1-4z" class="m"></path><path d="M703 149v-1-2l1-1c2 2 2 2 3 4h0c1 1 2 3 2 4 1 0 1 1 1 2l1-1c0 2 0 3 1 4v1c0 4 1 8 1 12v31 58 26c0 4 1 9 0 12v2 5c-1 1-1 1-1 2h1-4c-1-1-2-1-4 0h-1c-1-1-2-1-3-1h0l-2 1c-1 0-1 0-2 1s0 4-2 4c-1 0-1 0-2 1v1h-2v2c1 0 1 0 2-1v1c-1 0-2 1-4 2h0-4v3h0v1l-1 1c-2 1-4 2-7 3h-2c-1-2-4-5-6-7-1 0-1-1-2-1l-7-4-2-1h-5v-1h-7v-3c-1 1-3 5-5 6l-1-3c-1-1-2-4-2-6l-3-11c0-1-1-3-1-4l-2-10-1-3c0-2-1-4-1-5h1c3 0 4-2 6-5h0v-1c1-2 1-2 0-4l1-1-2-1c1-1 1-1 1-2l2-2h1v-4-2c1-2 1-3 2-5h1c1-1 1-1 2-1l1 1v-1l2-7h1v1h1l1-1h1l3-4c-1-1-2-1-3-2 1-2 4-5 6-7 4-4 9-8 14-12l21-17 3-2v-3c0-2-1-1-2-1l2-2v-4l-1-1c1-1 1-2 1-3 0-7 0-15 2-20v-6h0v-1h3c0-1 0-2 1-3v-1h1z" class="Y"></path><path d="M713 300c-3 0-9 0-11-1 1-1 2 0 3 0v-19c0-3 1-8 0-10-1-4-1-7-1-11v-13c0-4 0-9 1-12h0v1c1 3 1 6 1 9v13 7h0c1-2 1-3 1-5 1 3 0 7 0 11v28h6v2z" class="F"></path><path d="M696 266h1v2 24c0 1-1 5 0 6 0 1 0 1 1 1h1c-2 2-2 1-3 4v2c3 1 7 0 10 0h6 1c-1 1-1 1-1 2h1-4c-1-1-2-1-4 0h-1c-1-1-2-1-3-1h0l-2 1c-1 0-1 0-2 1s0 4-2 4c-1 0-1 0-2 1v1h-2l3-12c2-4 2-10 2-15 1-7 1-14 0-21z" class="U"></path><path d="M692 291c1-2 1-4 1-6v-14c0-4-1-8 0-12h1c1 2 2 5 2 7 1 7 1 14 0 21 0 5 0 11-2 15l-3 12v2c1 0 1 0 2-1v1c-1 0-2 1-4 2h0l-2-3c0-1 1-3 1-4l1-3 2-7c0-3 1-6 1-10z" class="Y"></path><path d="M689 308l2-7v1c0 3-2 5-1 8l1-1v-1c1-3 0 1 1-2 0-1 0-1 1-2v-1l1-1-3 12v2c1 0 1 0 2-1v1c-1 0-2 1-4 2h0l-2-3c0-1 1-3 1-4l1-3z" class="G"></path><path d="M697 252c0 5 1 10 0 14h-1c0-2-1-5-2-7h-1c-1 4 0 8 0 12v14c0 2 0 4-1 6s-1 3-2 5c-2 0-2 0-4-1 1-1 1 0 0-1v-4h-4l-2-3 1-1c2-1 2-3 2-5l-1-1h0-10c-2 0-4 1-6 1v-2c-1 1-1 1-1 3l-2-1c1-2 2-2 4-4 2-1 4-3 6-5l4-4 15-12c2-1 4-2 5-4z" class="s"></path><path d="M682 280h3c1 4 0 7 1 10h-4l-2-3 1-1c2-1 2-3 2-5l-1-1h0z" class="f"></path><path d="M698 153h3c0-1 0-2 1-3v-1h1c-1 1-1 3-1 4 0 0 1 1 2 1v1c1 2 1 3 1 5-2 8 0 58-2 60l1 1c0 2 1 12-1 15v18c0 4 0 8 1 12v3c-1 1-1 3-1 5l1 12h-1v-19c-2-3 1-9-1-11 0 2-1 10 0 13 1 1 0 3 0 5 0 5 0 9-1 14 0 3 0 6-1 9h0c-2-2-1-6-1-9h0c-1-1-1-3-1-5v-2c-1-2 0-5 0-8 0-1 0-4-1-5v-2c1-4 0-9 0-14v-1-6l-2-1v-2-1-1-6h0l1-5h0c0-2 0-6-1-8l1-9v-3l-1-1c-2 0-6 3-8 4 1-2 4-4 6-5-1-1-1-1-1-2l-1-1c-1 2-1 3-3 3s-5 1-7 2l-6 4h-3l21-17 3-2v-3c0-2-1-1-2-1l2-2v-4l-1-1c1-1 1-2 1-3 0-7 0-15 2-20v-6h0v-1z" class="Q"></path><path d="M696 205l1 1v39l-2-1v-2-1-1-6h0l1-5h0c0-2 0-6-1-8l1-9v-3l-1-1c-2 0-6 3-8 4 1-2 4-4 6-5 1-1 2-1 3-2z" class="T"></path><defs><linearGradient id="c" x1="683.542" y1="196.534" x2="685.394" y2="210.974" xlink:href="#B"><stop offset="0" stop-color="#252220"></stop><stop offset="1" stop-color="#3c4040"></stop></linearGradient></defs><path fill="url(#c)" d="M698 153h3c0-1 0-2 1-3v-1h1c-1 1-1 3-1 4 0 0 1 1 2 1v1c-2-1-3-1-4 0s-1 1-2 3v2c-1 2-1 3-1 5v7 34l-1-1c-1 1-2 1-3 2-1-1-1-1-1-2l-1-1c-1 2-1 3-3 3s-5 1-7 2l-6 4h-3l21-17 3-2v-3c0-2-1-1-2-1l2-2v-4l-1-1c1-1 1-2 1-3 0-7 0-15 2-20v-6h0v-1z"></path><path d="M681 209c3-3 9-5 12-8 0-1 1-1 2-2l1 1v3h-1l-1 1c1 0 1 0 2 1-1 1-2 1-3 2-1-1-1-1-1-2l-1-1c-1 2-1 3-3 3s-5 1-7 2z" class="D"></path><path d="M674 284l8-4 1 1c0 2 0 4-2 5l-1 1 2 3h4v4c1 1 1 0 0 1 2 1 2 1 4 1 1-2 1-3 2-5 0 4-1 7-1 10l-2 7-1 3c0 1-1 3-1 4l2 3h-4v3h0v1l-1 1c-2 1-4 2-7 3h-2c-1-2-4-5-6-7-1 0-1-1-2-1l-7-4-2-1h-5v-1h-7v-3l9-10-5 2c1-3 5-6 7-7s5-3 6-5l3-2c3-2 5-3 8-3z" class="p"></path><path d="M663 289l3-2c3-2 5-3 8-3-7 5-13 10-19 15l-5 2c1-3 5-6 7-7s5-3 6-5z" class="B"></path><path d="M660 314v-1-1-1c0-1-1-2 0-3v-2-1c2-2 0-3 0-5h1l2-2c2 2 3 5 4 8l-1 1h-1c-1-1-1-1-2-1 0 1 1 2 2 2-1 1 0 1-1 1-1 1-1 1-1 2 0 2 1 3 2 5h1c1 1 1 1 1 2l-7-4z" class="K"></path><path d="M677 286l-1-1h1c1 0 2 1 3 2l2 3h4v4c1 1 1 0 0 1 2 1 2 1 4 1 1-2 1-3 2-5 0 4-1 7-1 10l-2 7-1 3-1-2-1-1s0-1-1-1l-2-2c0-2-1-5-2-6l-1-1v-1c0-2-1-2-2-4h0v-1-1-1-1c-1-1-1-1-1-3z" class="M"></path><path d="M677 286c2 2 2 3 4 5h-1 0c-1 1-1 0 0 1 3 5 6 11 7 17l-1-1s0-1-1-1l-2-2c0-2-1-5-2-6l-1-1v-1c0-2-1-2-2-4h0v-1-1-1-1c-1-1-1-1-1-3z" class="O"></path><path d="M682 290h4v4c1 1 1 0 0 1 2 1 2 1 4 1 1-2 1-3 2-5 0 4-1 7-1 10l-2 7c-1-1-1-3-1-4-1-5-3-10-6-14z" class="b"></path><path d="M667 306c1 4 4 8 6 12 0-1-2-5-2-6 4 0 8 0 12 2 1 0 3 1 4 1l2 3h-4v3h0v1l-1 1c-2 1-4 2-7 3h-2c-1-2-4-5-6-7-1 0-1-1-2-1 0-1 0-1-1-2h-1c-1-2-2-3-2-5 0-1 0-1 1-2 1 0 0 0 1-1-1 0-2-1-2-2 1 0 1 0 2 1h1l1-1z" class="M"></path><path d="M683 314c1 0 3 1 4 1l2 3h-4l-2-1v-3h0z" class="C"></path><path d="M669 319c2 0 2 1 4 2 0-1 0-1-1-2h1 1c0-1 0-1 1-1 3 1 6 1 8 3h2v1l-1 1c-2 1-4 2-7 3h-2c-1-2-4-5-6-7z" class="s"></path><path d="M692 235c1 0 2-1 3-1v6 1 1 2l2 1v6 1c-1 2-3 3-5 4l-15 12-4 4c-2 2-4 4-6 5-2 2-3 2-4 4l2 1c0-2 0-2 1-3v2c2 0 4-1 6-1h10 0l-8 4c-3 0-5 1-8 3l-3 2c-1 2-4 4-6 5s-6 4-7 7l5-2-9 10c-1 1-3 5-5 6l-1-3c-1-1-2-4-2-6l-3-11 2 1v-2h2v-2c1-1 1-2 1-3h0c1-1 1-2 1-3s1-3 1-4h1l1-5 1-2v-2l1-4 1-1v-2-2-1-1c1-1 1-2 1-3v-3c0-1 0 0 1-1h0c1 1 1 1 1 2l-1 4v1l2-1h0l1-1h0c1-2 1-3 3-3h1c1-2 3-3 4-6h1 0c0 2-1 3-2 5h0c2-1 3-3 4-3h1c1-2 2-3 4-4 1-1 3-4 4-5l1-1c2 0 3-1 3-2l1-1h1v3l2 2 1-1 1 1c2 0 5-2 6-3h2l2-2c0-1 1-2 0-3h-1l1-2z" class="Y"></path><path d="M678 258h1l1 1-3 3h-2v-1c1-2 2-2 3-3z" class="R"></path><path d="M687 250h1c1 0 2 1 3 2l-2 2h-3l-1-1c1-1 1-2 2-3z" class="l"></path><path d="M692 235c1 0 2-1 3-1v6 1 1l-1-1c-3 0-5 5-7 5 0-1 1-2 2-3l1-1 2-2c0-1 1-2 0-3h-1l1-2z" class="E"></path><path d="M695 244l2 1v6 1c-1 2-3 3-5 4l-1-1c1-1 2-1 2-2-1-2 0-1 0-3 0 0-2-2-2-3v-1l4-2z" class="c"></path><path d="M695 244l2 1v6c-1-4-4-2-6-5l4-2z" class="O"></path><path d="M673 243c2 0 3-1 3-2l1-1h1v3l2 2c-1 1-1 2-2 3-3 1-6 3-9 5l-8 8-4 3c-2 4-4 5-5 8 3-3 7-6 11-8h0c-6 5-11 9-14 16l-1 1h-1c-2 0-3 2-3 3-1 1 0 1-1 2v-3l1-3 2-6s0-1 1-1v-1c1-2 2-4 3-5s1-1 1-2v-1c-2 1-3 1-3 3v1h-1v-2-2-1-1c1-1 1-2 1-3v-3c0-1 0 0 1-1h0c1 1 1 1 1 2l-1 4v1l2-1h0l1-1h0c1-2 1-3 3-3h1c1-2 3-3 4-6h1 0c0 2-1 3-2 5h0c2-1 3-3 4-3h1c1-2 2-3 4-4 1-1 3-4 4-5l1-1z" class="F"></path><path d="M663 253h1c1-2 2-3 4-4-1 1-2 3-3 5h1c-2 3-3 3-5 3h-1l3-4z" class="Q"></path><path d="M657 264c-2 4-4 5-5 8 0 1-4 4-4 5v-2c0-2 1-4 3-5 1-1 3-2 4-4 0-1 1-1 2-2z" class="d"></path><path d="M673 243c2 0 3-1 3-2l1-1h1v3l2 2c-2 1-3 2-5 3l-9 6h-1c1-2 2-4 3-5s3-4 4-5l1-1z" class="G"></path><path d="M647 268h1v-1c0-2 1-2 3-3v1c0 1 0 1-1 2s-2 3-3 5v1c-1 0-1 1-1 1l-2 6-1 3v3c1-1 0-1 1-2 0-1 1-3 3-3h1l1-1v1h1v1h1l1-1h0c3-3 7-7 10-8 0 1 1 1 1 2l-3 3v2h1l6-6 2-3h0c-1 1-2 3-2 5 1-2 3-4 5-4h1c-2 2-4 4-6 5-2 2-3 2-4 4l2 1c0-2 0-2 1-3v2c2 0 4-1 6-1h10 0l-8 4c-3 0-5 1-8 3l-3 2c-1 2-4 4-6 5s-6 4-7 7l5-2-9 10c-1 1-3 5-5 6l-1-3c-1-1-2-4-2-6l-3-11 2 1v-2h2v-2c1-1 1-2 1-3h0c1-1 1-2 1-3s1-3 1-4h1l1-5 1-2v-2l1-4 1-1z" class="d"></path><path d="M643 294l1 1h0c2-1 4-1 6-2v-1c3-1 8-6 10-8l1 1c-2 2-5 3-6 6-1 2-5 4-6 6 0 1 1 1 0 2s-1 2-1 3l-2 1c-1 1-2 1-4 1 1-3 3-5 5-6v-1h0-1c-1 1-3 2-4 4 0 1 0 1-1 2h-1v-2c0-3 1-5 3-7z" class="J"></path><path d="M665 282c0-2 0-2 1-3v2c2 0 4-1 6-1h10 0l-8 4c-3 0-5 1-8 3l-3 2h-1l-5 4c0 1-1 1-1 1 1-5 8-8 9-12z" class="E"></path><path d="M647 268h1v-1c0-2 1-2 3-3v1c0 1 0 1-1 2s-2 3-3 5v1c-1 0-1 1-1 1l-2 6-1 3v3c1-1 0-1 1-2 0-1 1-3 3-3h1l1-1v1h1v1h1l1-1h0c3-3 7-7 10-8-1 3-4 3-6 6-5 4-9 10-13 15-2 2-3 4-3 7v2h1c1-1 1-1 1-2 1-2 3-3 4-4h1 0v1c-2 1-4 3-5 6 2 0 3 0 4-1l2-1 2-1 5-2-9 10c-1 1-3 5-5 6l-1-3c-1-1-2-4-2-6l-3-11 2 1v-2h2v-2c1-1 1-2 1-3h0c1-1 1-2 1-3s1-3 1-4h1l1-5 1-2v-2l1-4 1-1z" class="D"></path><path d="M648 281l1-1v1h1v1h1l1-1-9 12c0-3 2-5 3-7s1-4 2-5z" class="c"></path><path d="M650 301l5-2-9 10c-1 1-3 5-5 6l-1-3 1 1v-2c0-1-1-1-1-2 1 0 1-1 2-1h0l1-1v-1l1-1h1v1 1c1 0 2-1 3-1v-1l-2-2 2-1 2-1z" class="I"></path><path d="M681 209c2-1 5-2 7-2s2-1 3-3l1 1c0 1 0 1 1 2-2 1-5 3-6 5 2-1 6-4 8-4l1 1v3l-1 9c1 2 1 6 1 8h0l-1 5h0c-1 0-2 1-3 1l-1 2h1c1 1 0 2 0 3l-2 2h-2c-1 1-4 3-6 3l-1-1-1 1-2-2v-3h-1l-1 1c0 1-1 2-3 2l-1 1c-1 1-3 4-4 5-2 1-3 2-4 4h-1c-1 0-2 2-4 3h0c1-2 2-3 2-5h0-1c-1 3-3 4-4 6h-1c-2 0-2 1-3 3h0l-1 1h0l-2 1v-1l1-4c0-1 0-1-1-2h0c-1 1-1 0-1 1v3c0 1 0 2-1 3v1 1 2 2l-1 1-1 4v2l-1 2-1 5h-1c0 1-1 3-1 4s0 2-1 3h0c0 1 0 2-1 3v2h-2v2l-2-1c0-1-1-3-1-4l-2-10-1-3c0-2-1-4-1-5h1c3 0 4-2 6-5h0v-1c1-2 1-2 0-4l1-1-2-1c1-1 1-1 1-2l2-2h1v-4-2c1-2 1-3 2-5h1c1-1 1-1 2-1l1 1v-1l2-7h1v1h1l1-1h1l3-4c-1-1-2-1-3-2 1-2 4-5 6-7 4-4 9-8 14-12h3l6-4z" class="c"></path><path d="M643 270c1-1 1-2 2-4l1 1v-1h1v2l-1 1-3 1z" class="E"></path><path d="M667 228h2c-1 2-1 2-3 4h-1c-1-1 1-3 2-4z" class="J"></path><path d="M651 240l2-1v9l-2-1v-1-2c1-2 0-3 0-4z" class="d"></path><path d="M687 218l1 1c-3 2-5 5-7 8 0 1-1 1-2 2h-1v-1-1c2-1 2-2 3-4l6-5zm-34 21c1-1 1-1 3 0l2 1c0 1 1 1 1 2 1 1 1 2 1 3l-1 1c-2-1-2-2-3-3h-1l-1 1c0 2 2 4 1 6l-2-2v-9z" class="E"></path><path d="M660 251c-2 0-4 3-5 4h-1v-2c1-1 1-2 2-3s3-2 3-3l1-1h1 1l2-1v1c1-1 2-2 3-2 0 1-2 2-2 3h1s1-1 1-2c1 0 1-1 1-2 1-1 2-1 3-1v1h2 0l-1 1c-1 1-3 4-4 5-2 1-3 2-4 4h-1c-1 0-2 2-4 3h0c1-2 2-3 2-5h0-1z" class="d"></path><path d="M661 251c1-1 1-1 0-2 1-2 2-2 3-2 1 1 0 1 0 3h0l5-5c1-1 2-1 3-1-1 1-3 4-4 5-2 1-3 2-4 4h-1c-1 0-2 2-4 3h0c1-2 2-3 2-5h0z" class="J"></path><path d="M694 212h1c0-1 1-2 1-3v3c-1 1 0 2-1 3-1 0-1 0-1 1-1 1-1 2-2 3l-1-1c-2 0-2 0-3 1l-1-1-6 5c-4 1-7 5-11 8l3-4c0-1 1-1 1-2s2-2 3-3l3-3c1 0 2 0 3-1 2-3 6-4 9-7v-1h2v2z" class="d"></path><path d="M694 212h1c0-1 1-2 1-3v3c-1 1 0 2-1 3-1 0-1 0-1 1-1 1-1 2-2 3l-1-1c-2 0-2 0-3 1l-1-1c1-1 2-3 4-4 1-1 2-1 3-2z" class="Y"></path><path d="M692 219c1-1 1-2 2-3 0-1 0-1 1-1 1-1 0-2 1-3l-1 9c1 2 1 6 1 8h0l-1 5h0c-1 0-2 1-3 1l-1 2h1c1 1 0 2 0 3l-2 2h-2c-1 1-4 3-6 3l-1-1-1 1-2-2c0-3 0-4 2-6h2v-1c0-1 1-1 1-2 0-2 1-3 1-4 2-4 5-8 8-11z" class="G"></path><path d="M689 233h0l4-4c0-1 1-2 1-3v-2h1v-2h-1l1-1h0c1 2 1 6 1 8h0c-2 1-3 1-4 2s-2 2-3 2z" class="X"></path><path d="M689 233c1 0 2-1 3-2s2-1 4-2l-1 5h0c-1 0-2 1-3 1l-1-1c-1 1-3 1-4 1 1 0 2-1 2-2z" class="c"></path><path d="M678 243c0-3 0-4 2-6h2c0 1-1 1-1 3h0v2l1 1c2 0 3-1 4-2v-1l2-2 1 1v1c-1 0-1 1-1 2-1 1-4 3-6 3l-1-1-1 1-2-2z" class="E"></path><path d="M687 235c1 0 3 0 4-1l1 1-1 2h1c1 1 0 2 0 3l-2 2h-2c0-1 0-2 1-2v-1l-1-1-2 2h-1l-1-1 1-2 2-2z" class="Q"></path><path d="M681 209c2-1 5-2 7-2s2-1 3-3l1 1c0 1 0 1 1 2-2 1-5 3-6 5-3 3-7 5-10 7h0c-2 1-3 2-5 3-1 1-1 2-2 3s0 0-2 0c-1 1-4 5-5 6-1 2-2 3-3 4 0 1 0 2-1 3 0 1-1 1-1 2l-2-1c-2-1-2-1-3 0l-2 1-1-1 1-1h1l3-4c-1-1-2-1-3-2 1-2 4-5 6-7 4-4 9-8 14-12h3l6-4z" class="G"></path><path d="M658 225c2 0 3-2 5-3 1 0 2-1 4-1-4 4-10 8-12 13-1-1-2-1-3-2 1-2 4-5 6-7z" class="j"></path><path d="M658 225c4-4 9-8 14-12h3l-8 8c-2 0-3 1-4 1-2 1-3 3-5 3z" class="o"></path><path d="M659 238c-1-1-1-1-2 0h0-1l1-2-1-1c1-2 2-3 4-4 3-4 7-8 12-11 1 0 2-1 4-2h0l1 1c-2 1-3 2-5 3-1 1-1 2-2 3s0 0-2 0c-1 1-4 5-5 6-1 2-2 3-3 4 0 1 0 2-1 3z" class="E"></path><path d="M648 238h1v1c-3 11-4 23-9 34l1 1 1-1c1-1 1-2 1-3l3-1-1 4v2l-1 2-1 5h-1c0 1-1 3-1 4s0 2-1 3h0c0 1 0 2-1 3v2h-2v2l-2-1c0-1-1-3-1-4l-2-10-1-3c0-2-1-4-1-5h1c3 0 4-2 6-5h0v-1c1-2 1-2 0-4l1-1-2-1c1-1 1-1 1-2l2-2h1v-4-2c1-2 1-3 2-5h1c1-1 1-1 2-1l1 1v-1l2-7z" class="B"></path><path d="M646 269l-1 4v2l-1 2-1 5h-1c0 1-1 3-1 4-2 1-3 2-3 5h0-1c0-2 0-4 1-6v-1c1-2 2-3 3-5h1l-1-1h-1v-5l1 1 1-1c1-1 1-2 1-3l3-1z" class="F"></path><path d="M637 263l1-1v2l1 1v1c-1 1-1 2-1 2h1 1c0 1-1 3-1 4-1 0-3 2-3 2v1l1 1c-1 1-1 2-2 3v1c-1 0-2 0-3 1l-1-3c0-2-1-4-1-5h1c3 0 4-2 6-5h0v-1c1-2 1-2 0-4z" class="H"></path><path d="M630 273h1c3 0 4-2 6-5-1 3-2 5-3 7-2 1-2 1-3 3 0-2-1-4-1-5z" class="L"></path><path d="M643 246c1-1 1-1 2-1l1 1v-1c0 4-1 9-2 14-1 1-1 3-1 4-2 2-3 2-4 5h-1s0-1 1-2v-1l-1-1v-2l-2-1c1-1 1-1 1-2l2-2h1v-4-2c1-2 1-3 2-5h1z" class="c"></path><path d="M640 253c1-1 1-2 2-3l1 1c-1 1-1 2-1 3v1l-2 2v-4z" class="Q"></path><path d="M637 259l2-2c0 2 1 4 0 6l-1 1v-2l-2-1c1-1 1-1 1-2z" class="E"></path><path d="M640 253v-2c1-2 1-3 2-5h1v5l-1-1c-1 1-1 2-2 3z" class="Y"></path><path d="M265 423l1-1c7-6 15-3 23-2 3 1 6 1 9 1v-1c1 0 1-1 2-1h6 2c3 1 7 1 11 0h8c2 0 4 0 6 1 1 1 1 1 1 3l-1 1h0l2 2h5l-1 1h7 16 11c2 0 5 1 7 0h1 7 0c1 0 2 1 3 1l-3 8c0 1-1 2-1 3-1 2-2 3-2 5-1 5-2 11-2 16 0 3 1 5 1 7 0 3 1 9 1 12v3c2 4 2 9 4 12l-3 3-3 3c-1 1-1 2-2 3h0v1h-1v1l-2 4c-2 2-3 5-4 8l-1 2c1 1 0 5 0 7h0v4c-1 1-1 2-1 3h-1c-2 3-4 4-6 6h1c1 1 2 1 2 2 2 1 3 1 4 2l1 3v2c1 1 1 2 1 2l-2 1-1-1c-2 0-4 1-6 1h-2-5 0c-3 1-5 0-8 0h-3-3l-8 1h0c-2-1-3-1-4-1h-1-9c-2 1-3 1-5 1h0c-2 0-4 0-5-1l-2 2v-25l-2-2-1-1h-9v1h-4l-4 1h-1c-1 0-3 0-5-1h-1l-1-3v-1-3c1-1 1-3 2-4 1-2 1-3 2-4l3-3 7-7h-4l1-1c-2-3-1-3-2-5v-2c0-2 0-3 2-5 1 0 2 0 3-1v-1c-3-1-6-5-9-8-5-4-12-7-17-11-4-4-5-7-6-12v-5c0-3 0-7-1-11v-5-5-3c1-1 1-2 2-3z" class="W"></path><path d="M289 476l8 9c0 1 0 1-1 2v-1c-3-1-6-5-9-8l2-2z" class="L"></path><path d="M293 499v-4h1 3 1s1-1 2-1c1-1 1-1 2-1l1 2h1c1 1 1 1 1 2h0 1c1-2 4-5 6-7l1 1v1 1h-1c-2 0-2 1-4 2l1 1-2 2c1 0 2 0 2 1h0c-1 2-1 2-1 3-1 1-2 1-3 1-1-1-1-3-2-4 0-1 1-1 0-2-4 0-7 1-10 2z" class="c"></path><path d="M264 455l1 1h0c4 9 16 14 24 20l-2 2c-5-4-12-7-17-11-4-4-5-7-6-12z" class="Z"></path><path d="M279 465c2 1 3 1 4 2l2 1 3 2c2 1 4 2 6 4 1 1 3 3 5 4 0 0 1 0 2 1h0v-1c-1-1-1-2-1-3h0l6 6c1 0 1 1 2 2v1c-1 1-2 1-3 1l-1 1c-1 1-3 1-5 2v-1l1-1h1 0 1c0-3-6-6-7-8-1-1-1-2-2-3l-2-1-2-2-8-5c-1 0-1-1-2-2z" class="G"></path><path d="M322 484l3-1h1c2-1 5-1 7-2h3l1-1c3 0 8 0 11 1v1c-2 1-3 0-4 1 2 2 5 0 7 2h1l1 1-2 1h0-3-2c-2-1-3-1-5-1-3-1-6 0-9 0l2-2h2l-1-1-5 1c-1 1-4 0-6 1l-1 1-1-2z" class="d"></path><path d="M341 486c2 0 3 0 5 1h2c2 0 4 1 6 1l3 2-2 1c-1 0-2 0-2-1-2-1-2 0-4-1h-2v-1h-9c1 1 1 1 1 2l-1 1h-1-1c-1 1-2 2-2 3v2l-1 1h-1v3h0c-1 1-1 3-1 4-3-1-4-3-5-4 0-1-1-2-1-2l-1-1 1-1v-6c-1-1-1-1-2-1l-2 2c-1-1-2-1-3-1l-1 1c0 1 0 2-1 3l-2-1c1-2 1-3 3-3l2-1c1-1 3-1 5-2h6l1-1h10z" class="I"></path><path d="M325 490l2-2v3c-1 2 0 4 1 6 0 1 1 2 3 3h1c-1 1-1 3-1 4-3-1-4-3-5-4 0-1-1-2-1-2l-1-1 1-1v-6z" class="j"></path><path d="M333 497l1-1v-2c0-1 1-2 2-3h1 1l1-1c0-1 0-1-1-2h9v1h2c2 1 2 0 4 1 0 1 1 1 2 1l2-1h2v1c1 0 1 0 2 1v1 5l-1 1v2c-2 0-7 0-8-1-1-2-1-2-2-2-1-1 0-1-1-1h-1c-1 1-1 1-1 3-1 1 0 1-1 2l-1-1v-1h1c-1-2-1-2-2-2h-4l-1-1-2 1c-2 0-2 0-4-1z" class="B"></path><path d="M334 475c6 0 12 0 18 1l-1 1c1 1 1 0 2 1 1 0 1 1 2 1s2 1 3 2l-2-1h-4c-2-1-3-1-5-1v1h2l1 1h-2c-3-1-8-1-11-1l-1 1h-3c-2 1-5 1-7 2h-1l-3 1-1 1c-1 0-1 0-2 1l-1-1h-1c-2 0-3 1-5 1 0 1 0 1-1 1-1 1-2 1-3 2h-1l-2 1-1 1h0c-2 0-3 1-4 1-1 1-1 1-2 1h-2 0-2l1-2h-2v-1c1 0 1 0 3-1 1 0 2 0 3-1h0c2-1 4-1 5-2l1-1c1 0 2 0 3-1s3-2 4-3c0-1 1 0 2-1 4-2 10-3 14-4l6-1z" class="Q"></path><path d="M318 485c1-1 1-1 2-1h1l1-1c1 0 2-1 3-1l3-1c1 0 2-1 3-1 4-1 8-1 12-1h1c0-1 0 0 1-1 2-1 4 0 6-1 1 1 1 0 2 1 1 0 1 1 2 1s2 1 3 2l-2-1h-4c-2-1-3-1-5-1v1h2l1 1h-2c-3-1-8-1-11-1l-1 1h-3c-2 1-5 1-7 2h-1l-3 1-1 1c-1 0-1 0-2 1l-1-1z" class="E"></path><path d="M316 494c1-1 1-2 1-3l1-1c1 0 2 0 3 1l2-2c1 0 1 0 2 1v6l-1 1 1 1s1 1 1 2c1 1 2 3 5 4l-1 1c-2 1-4 1-7 1l1 1h6l1 1h-1c-1 1-3 1-3 1h-12l-4-1h1v-1h-4v-1l-1-1 1-3c0-1 0-1 1-3h0c0-1-1-1-2-1l2-2-1-1c2-1 2-2 4-2h1 1l2 1z" class="H"></path><path d="M309 499l13 2c1 1 1 2 1 3-1 0-2 0-3 1l-1-1c-1 1-1 1-2 1h0c0-1 0-1-1-1h-4l1 1h0c-1 0-1 0-1-1h-2l-1 1s-1 0-1 1l-1-1 1-3c0-1 0-1 1-3z" class="O"></path><path d="M316 494c1-1 1-2 1-3l1-1c1 0 2 0 3 1l2-2c1 0 1 0 2 1v6l-1 1 1 1s1 1 1 2l-1 2c1 1 2 2 3 2-1 1-3 1-4 1l-1-1c0-1 0-2-1-3l-13-2h0c0-1-1-1-2-1l2-2-1-1c2-1 2-2 4-2h1 1l2 1z" class="E"></path><path d="M309 496h1 0l2 2 1 1h-4c0-1-1-1-2-1l2-2zm4-3h1l2 1c1 1 2 1 3 1l1 1c1 0 2 0 2 1h-1c-1 1-2 1-2 1-3 0-6-2-7-3v-2h1z" class="Q"></path><path d="M332 500h0v-3h1c2 1 2 1 4 1l2-1 1 1-1 1v-1l-1 1c-1 0-2 0-2 1-1 1-2 2-2 4h0v2 1c-1 2-1 4-1 6v1c-1 2 0 4 0 6h-3c-2-1-10-1-13 0h-4-4v-1c-1-1-1-3 0-4 0-1 0-1-1-2 0-1-1-3-1-5h4l4 1h12s2 0 3-1h1l-1-1h-6l-1-1c3 0 5 0 7-1l1-1c0-1 0-3 1-4z" class="J"></path><g class="E"><path d="M308 513c0-1-1-3-1-5h4l4 1h-5c0 1 0 2 1 2h1c1 3 0 6 0 8l1 1h-4v-1c-1-1-1-3 0-4 0-1 0-1-1-2z"></path><path d="M293 499c3-1 6-2 10-2 1 1 0 1 0 2 1 1 1 3 2 4 1 0 2 0 3-1l-1 3 1 1v1h4v1h-1-4c0 2 1 4 1 5 1 1 1 1 1 2-1 1-1 3 0 4v1l-1 1v5l-1-1h-9v1h-4l-4 1h-1c-1 0-3 0-5-1h-1l-1-3v-1-3c1-1 1-3 2-4 1-2 1-3 2-4l3-3 7-7h-4l1-1v-1z"></path></g><path d="M289 513l8-10c0 1 0 2 1 4h0l-1 1-2 2c-2 2-3 3-6 3z" class="U"></path><path d="M289 513c3 0 4-1 6-3h0c-2 4-5 6-7 9-1 2-2 5-4 7h-1l-1-3 1-1h0 0l1-1c1-3 3-6 5-8z" class="S"></path><path d="M293 499c3-1 6-2 10-2 1 1 0 1 0 2 1 1 1 3 2 4v4l-1 1h-1c0 2-1 3-3 4h0-2 0c0-1-1-2-1-4l1-1h0c-1-2-1-3-1-4l1-2h0c-2 2-6 6-9 7l7-7h-4l1-1v-1z" class="p"></path><path d="M298 507c2-1 2-1 4 0l1 1c0 2-1 3-3 4h0-2 0c0-1-1-2-1-4l1-1z" class="q"></path><path d="M295 510l2-2c0 2 1 3 1 4h0 2l-2 5c-1 1-1 2-2 3 1 1 1 1 1 2l-1 1v2h2v1h-4l-4 1h-1c-1 0-3 0-5-1 2-2 3-5 4-7 2-3 5-5 7-9h0z" class="D"></path><path d="M298 512h2l-2 5c-1 1-1 2-2 3 1 1 1 1 1 2l-1 1v2h2v1h-4l1-1c-1-1 0-1-1-1v-1l2-4c0-1-1-1-1-2s0-1 1-2 1-2 2-3z" class="C"></path><path d="M295 510l2-2c0 2 1 3 1 4-1 0-1-1-2-1-1 1-2 2-2 4-1 0-1 1-2 1l-3 6 4-4h0c0 1-1 2-1 3l-3 6c-1 0-3 0-5-1 2-2 3-5 4-7 2-3 5-5 7-9h0z" class="V"></path><path d="M305 503c1 0 2 0 3-1l-1 3 1 1v1h4v1h-1-4c0 2 1 4 1 5 1 1 1 1 1 2-1 1-1 3 0 4v1l-1 1v5l-1-1h-9-2v-2l1-1c0-1 0-1-1-2 1-1 1-2 2-3l2-5h0c2-1 3-2 3-4h1l1-1v-4z" class="f"></path><path d="M305 512l1 1 1 1 1-1c1 1 1 1 1 2-1 1-1 3 0 4v1l-1 1v5l-1-1v-2l-2-11z" class="D"></path><path d="M305 503c1 0 2 0 3-1l-1 3 1 1v1h4v1h-1-4c0 2 1 4 1 5l-1 1-1-1-1-1c-1-1-1-3-1-4l1-1v-4z" class="C"></path><path d="M298 517c1 1 2 0 2 2l2-1c0 1 1 1 1 2s1 1 1 2h1l1 1h1 0v2h-9-2v-2l1-1c0-1 0-1-1-2 1-1 1-2 2-3z" class="S"></path><path d="M352 476l6 1h1s1 1 2 1c0-1 2-2 2-2v-1c0-1 1-1 1-2 0 3 0 5 1 7h4c0-1 0-1 1-1v-1h1l1 1 1-1h1 3 0c1-1 3 0 4 0 2 0 2-1 4 1v3c2 4 2 9 4 12l-3 3-3 3c-1 1-1 2-2 3h0v1h-1v1h-1l-4-2c-2 0-3 1-4 2 0-1-1 0-1 0h-2c0-1 0-1-1-2-1 1-1 1-2 1l-1 1h0l-1-1-1-1c-1 0-1-1-2-2v-2l1-1v-5-1c-1-1-1-1-2-1v-1h-2l-3-2c-2 0-4-1-6-1h3 0l2-1-1-1h-1c-2-2-5 0-7-2 1-1 2 0 4-1v-1h2l-1-1h-2v-1c2 0 3 0 5 1h4l2 1c-1-1-2-2-3-2s-1-1-2-1c-1-1-1 0-2-1l1-1z" class="J"></path><path d="M352 480h4l2 1h0c1 1 1 2 1 2h-1c-1-1-1-1-3-1h-1c-2-1-2-1-2-2z" class="Q"></path><path d="M360 501v-2l1-1c1 1 3 1 4 2-1 2-1 3-2 4l-1-1c-1 0-1-1-2-2z" class="D"></path><path d="M359 490v-1-2c1-1 0-1 1-1 1 1 4 3 5 4v1l1 2c-1 0-3 1-4 0h-1v-1c-1-1-1-1-2-1v-1z" class="W"></path><path d="M375 502h0l-2-1v-4h2c1 1 2 2 4 2 2 1 2 1 4 1-1 1-1 2-2 3h0v1h-1v1h-1l-4-2v-1z" class="D"></path><path d="M368 498c1-1 1-1 2-1h1l1-1c1 0 2 0 3 1h-2v4l2 1h0v1c-2 0-3 1-4 2 0-1-1 0-1 0h-2c0-1 0-1-1-2-1 1-1 1-2 1l-1 1h0l-1-1c1-1 1-2 2-4 1-1 1-2 3-2z" class="L"></path><path d="M368 498c1-1 1-1 2-1h1l1-1c1 0 2 0 3 1h-2v4l2 1h0c-3 0-5-3-7-4z" class="B"></path><path d="M365 480h4c0-1 0-1 1-1v-1h1l1 1 1-1h1 3 0c1-1 3 0 4 0 2 0 2-1 4 1v3c2 4 2 9 4 12l-3 3-3 3c-1 0-2-4-3-4s-1 1-3 0l1-1-1-2h-1l1-1-2-3c-3-1-5-3-7-5-2-1-2-2-3-4z" class="P"></path><path d="M375 489c1 0 2 0 3-1 3 0 3 0 5 2 1 2 1 5 0 7l-3-3c-1-2 1-1-1-3l-1 1-1 1h-1l1-1-2-3z" class="H"></path><path d="M365 480h4c0-1 0-1 1-1v-1h1l1 1 1-1h1 3 0c1-1 3 0 4 0l-2 1v3c1 1 4 0 5 0 0 2 0 4-1 6-2 0-2 0-3-1l-2 1c-1 1-2 1-3 1-3-1-5-3-7-5-2-1-2-2-3-4z" class="K"></path><path d="M365 480h4c0-1 0-1 1-1v-1h1l1 1 1-1h1v1c0 1-1 1-1 2 1 1 1 1 1 2 1 0 1 1 1 1l-2 2c-2-1-3-3-4-2h-1c-2-1-2-2-3-4z" class="g"></path><path d="M266 424c1-1 1-1 3-1v-1h3c2-1 4-1 7 0l-1 1h1c1 1 2 1 3 2s3 2 5 2l1 1h-1-1 0c-1 0-1 1-1 1-1 3-2 4-5 5h-2c-1-1-1-1-3 0l-1 1c0 2 0 6 1 8 2 5 7 10 10 16h0v-1l3 2v-1h1c2 2 3 3 5 4l1 1 4 4 2 1 8 2h1c2 1 6 1 8 1h0-5-1c-1 1 0 2 1 3l1 5c-1 1-2 0-2 1-1 1-3 2-4 3v-1c-1-1-1-2-2-2l-6-6h0c0 1 0 2 1 3v1h0c-1-1-2-1-2-1-2-1-4-3-5-4-2-2-4-3-6-4l-3-2-2-1c-1-1-2-1-4-2h0l-2-1-1-1c-1 0-2-1-3-1v-1h-1c-2-2-4-4-5-6v-3c-1-2-1-6-2-8 0-2-1-5-1-7 0-1-1-2-1-3v-5-3c1-1 1-2 2-3l1 1z" class="q"></path><path d="M295 464l4 4-2 1c-1 0-4-3-5-3 1-2 1-2 3-2z" class="L"></path><path d="M309 471h1c2 1 6 1 8 1h0-5-1c-1 1 0 2 1 3-1 0-2 1-3 1-1 1 0 2-1 2h0c-1-1-1-2-2-2h-1v-1c2-1 2-1 2-3l1-1z" class="K"></path><path d="M285 459v-1l3 2v-1h1c2 2 3 3 5 4l1 1c-2 0-2 0-3 2-2-1-3-3-5-3l-2-4z" class="N"></path><path d="M299 468l2 1 8 2-1 1c0 2 0 2-2 3v1l1 2h0c-4-3-7-6-10-9l2-1z" class="f"></path><path d="M306 475c-1-1-1-1-1-2 2-1 2-1 3-1 0 2 0 2-2 3z" class="p"></path><path d="M271 455c4 4 9 6 14 9s10 7 15 11c0 1 0 2 1 3v1h0c-1-1-2-1-2-1-2-1-4-3-5-4-2-2-4-3-6-4l-3-2-2-1c-1-1-2-1-4-2h0l-2-1c1-1 0-1 0-1 0-1-1-2-2-2l-3-2v-1c-2 0-2-1-2-2l1-1z" class="Q"></path><path d="M263 434v-5-3c1-1 1-2 2-3l1 1c-1 3-1 5 0 8 2 4 1 9 3 14 0 3 1 6 2 9l-1 1c0 1 0 2 2 2v1l3 2c1 0 2 1 2 2 0 0 1 0 0 1l-1-1c-1 0-2-1-3-1v-1h-1c-2-2-4-4-5-6v-3c-1-2-1-6-2-8 0-2-1-5-1-7 0-1-1-2-1-3z" class="E"></path><path d="M267 430v-1c-1-2 0-3 1-5 3-2 7-1 10-1h1c1 1 2 1 3 2s3 2 5 2l1 1h-1-1 0c-1 0-1 1-1 1-1 3-2 4-5 5h-2c-1-1-1-1-3 0l-1 1c0 2 0 6 1 8 2 5 7 10 10 16h0l2 4c-1 0 0 0-1-1h-1c-3-2-6-3-9-5-5-4-7-11-8-17 0-4 0-7-1-10z" class="r"></path><path d="M267 430c3 2 3 3 3 6v3l-1-1-1 2c0-4 0-7-1-10z" class="E"></path><path d="M288 428h2l3 2 1 2-3 6-1 1c-1 2 0 4 0 7 1-2 1-2 1-4v-1c1-1 1-1 1-2 3-2 6-1 8 0l1-1v1h3 1 3c0 1 0 1-1 1-2 1-3 2-4 4-1 3-1 6 0 8l1 1 2 3c2 2 4 2 7 3 1 0 5 1 5 1s1 0 1 1l-1 2 1 1-1 1v2h0l1 1c1 1 3 2 3 3-1 0-2 1-4 1s-6 0-8-1h-1l-8-2-2-1-4-4-1-1c-2-1-3-2-5-4h-1v1l-3-2v1h0c-3-6-8-11-10-16-1-2-1-6-1-8l1-1c2-1 2-1 3 0h2c3-1 4-2 5-5 0 0 0-1 1-1h0 1 1z" class="H"></path><path d="M288 459c-1-1-2-3-2-4v-1c1 1 1 2 2 3h2l3 3c1 1 1 2 1 3-2-1-3-2-5-4h-1z" class="q"></path><path d="M275 443h0c1 0 1 1 1 1 1 1 1 2 2 3l1-1h0v-1c1 0 0 0 1 1 2 1 3 2 3 4 1 2 3 2 3 4v1c0 1 1 3 2 4v1l-3-2v1h0c-3-6-8-11-10-16z" class="C"></path><path d="M290 446c1-2 1-2 1-4v-1c1-1 1-1 1-2 3-2 6-1 8 0-1 0-2 0-3 1v1l-3 3h-1v5l-1 1c1 2 4 9 3 10-1 0-1-2-2-4l-1-1c-1-3-1-6-2-9z" class="i"></path><path d="M288 428h2l3 2 1 2-3 6-1 1c-1 2 0 4 0 7 1 3 1 6 2 9h-1c-1-2-3-3-4-4-1-3-4-6-6-8s-4-3-4-6c0-1-1-2-2-3 2-1 2-1 3 0h2c3-1 4-2 5-5 0 0 0-1 1-1h0 1 1z" class="l"></path><path d="M275 434c2-1 2-1 3 0l3 1-3 2h-1c0-1-1-2-2-3z" class="E"></path><path d="M277 437h1c2 2 5 3 7 5 1 2 2 4 4 6l2 7c-1-2-3-3-4-4-1-3-4-6-6-8s-4-3-4-6z" class="B"></path><path d="M288 428h2l3 2 1 2-3 6-1 1c0-2-1-3-2-4 0 1-1 2-2 3-1-1-3-2-5-3h0l-3-1h2c3-1 4-2 5-5 0 0 0-1 1-1h0 1 1z" class="S"></path><path d="M288 428h2l3 2 1 2-3 6-1 1c0-2-1-3-2-4h1c0-1 1-3 0-4h-2c0-1-1-1-2-2 0 0 0-1 1-1h0 1 1z" class="W"></path><path d="M301 463c-3-2-3-4-5-7v-1c0-1 0-3-1-4h0v-4c0-1 0-1 1-2 1-2 3-3 4-4h1l2 3c-1 3-1 6 0 8l1 1 2 3c2 2 4 2 7 3 1 0 5 1 5 1s1 0 1 1l-1 2 1 1-1 1v2h0l1 1c1 1 3 2 3 3-1 0-2 1-4 1s-6 0-8-1h-1l-8-2 2-1c-1-1-2-2-3-2h0c-1-1-2-1-2-2 1 0 1-1 2-1h1 0z" class="F"></path><path d="M301 463c-3-2-3-4-5-7v-1c0-1 0-3-1-4h0v-4c0-1 0-1 1-2 1-2 3-3 4-4h1l2 3c-1 3-1 6 0 8l1 1 2 3h0c-2-1-3-3-4-3l-2-2v-1c0-1 0-2-1-4h-1c-1 1 0 3 0 4v1 1 1-1l-1-4-1-1v1l1 1s-1 1-1 2v1c2 2 2 4 3 7 1 0 1 0 1 1s1 2 1 3z" class="I"></path><path d="M316 464h2l-1-1h1l1 1-1 1v2h0l1 1c1 1 3 2 3 3-1 0-2 1-4 1s-6 0-8-1h-1l-8-2 2-1c-1-1-2-2-3-2h0c-1-1-2-1-2-2 1 0 1-1 2-1h1 0c2 1 5 2 7 3 3-1 5-2 8-2z" class="D"></path><path d="M308 466c3-1 5-2 8-2v1c-1 1-2 2-3 2s-3 0-5-1z" class="B"></path><path d="M316 464h2l-1-1h1l1 1-1 1v2h0l1 1c1 1 3 2 3 3-1 0-2 1-4 1s-6 0-8-1h2v-1c1-1 3 0 4 0-1-2 0-4 0-5v-1z" class="C"></path><path d="M265 423l1-1c7-6 15-3 23-2 3 1 6 1 9 1v-1c1 0 1-1 2-1h6 2c3 1 7 1 11 0h8c2 0 4 0 6 1 1 1 1 1 1 3l-1 1h0l2 2h5l-1 1h7c-2 1-3 2-5 3v1l-3 1c0 1-1 1-2 1h0v1 1l-1 1v1c-1 1-1 2-2 3h0 0l3 1c1 1 1 1 0 2l-1 2-1 1c-3 1-4-1-6 2l-3 1c-2 0-3 0-5 1v1 2l-3-1-1-1h1c-1-2-1-3-1-4h-3c0-2 0-4-1-6-1 0-3-1-5-1 1 0 1 0 1-1h-3-1-3v-1l-1 1c-2-1-5-2-8 0 0 1 0 1-1 2v1c0 2 0 2-1 4 0-3-1-5 0-7l1-1 3-6-1-2-3-2h-2l-1-1c-2 0-4-1-5-2s-2-1-3-2h-1l1-1c-3-1-5-1-7 0h-3v1c-2 0-2 0-3 1l-1-1z" class="s"></path><path d="M324 441l9-1 3 1c1 1 1 1 0 2l-1 2-1 1c-3 1-4-1-6 2l-3 1c-2 0-3 0-5 1 0-1 1-2 0-3 1 0 1-1 2-2v1l-1 2h1 2l2-2h0l1-1c1 0 1 0 2-1l-1-1c0 1-1 1-2 2-2 0-3-2-5-4 1 1 2 0 3 0z" class="P"></path><path d="M339 427h7c-2 1-3 2-5 3v1l-3 1c0 1-1 1-2 1h0v1 1l-1 1v1c-1 1-1 2-2 3h0 0l-9 1c-1 0-2 1-3 0 1-1 3-2 4-4l5-6c3-3 5-4 9-4z" class="h"></path><path d="M330 437l3-3c1 0 1 0 1-1 1-1 2-1 3-2 2-1 3 0 4 0l-3 1c0 1-1 1-2 1h0v1 1l-1 1c-2 1-3 1-5 1z" class="V"></path><path d="M335 436v1c-1 1-1 2-2 3h0 0l-9 1h0c0-1 1-2 2-3 2 0 3-1 4-1 2 0 3 0 5-1z" class="I"></path><path d="M293 430h4c1 1 2 0 3 0h2l2 1h0c2-1 3-1 5 0l1 1c1 1 2 1 3 0 1 2 1 2 3 2 1 1 1 2 1 3 1 0 2-1 2 1v2c-1 1-2 2-2 4v2h1 1l-1 3v1 2h1v-1c0-1 0-2 1-4h0c1 1 0 2 0 3v1 2l-3-1-1-1h1c-1-2-1-3-1-4h-3c0-2 0-4-1-6-1 0-3-1-5-1 1 0 1 0 1-1h-3-1-3v-1l-1 1c-2-1-5-2-8 0 0 1 0 1-1 2v1c0 2 0 2-1 4 0-3-1-5 0-7l1-1 3-6-1-2z" class="T"></path><path d="M291 438v-1c1 0 2-1 3-2h2v-1c2 0 4 0 6-1 1 0 2 0 3 1h2c1 0 1 0 2 1h1c2 1 3 3 5 4v1h-1-1l-1 1h0c-1 0-3-1-5-1 1 0 1 0 1-1h-3-1-3v-1l-1 1c-2-1-5-2-8 0 0 1 0 1-1 2v1c0 2 0 2-1 4 0-3-1-5 0-7l1-1z" class="n"></path><path d="M265 423l1-1c7-6 15-3 23-2 3 1 6 1 9 1v-1c1 0 1-1 2-1h6 2c3 1 7 1 11 0h8c2 0 4 0 6 1 1 1 1 1 1 3l-1 1h-2c-2 1-6 1-8 1h-23c-2 0-5-1-7-1-1 0-3 2-3 3v1h0-2l-1-1c-2 0-4-1-5-2s-2-1-3-2h-1l1-1c-3-1-5-1-7 0h-3v1c-2 0-2 0-3 1l-1-1z" class="c"></path><path d="M279 422l4 1c2 0 5 1 7 1h-3c-1 2-1 2 0 3-2 0-4-1-5-2s-2-1-3-2h-1l1-1z" class="P"></path><path d="M290 424h41c-2 1-6 1-8 1h-23c-2 0-5-1-7-1-1 0-3 2-3 3v1h0-2l-1-1c-1-1-1-1 0-3h3z" class="K"></path><path d="M340 498h4c1 0 1 0 2 2h-1v1l1 1c1-1 0-1 1-2 0-2 0-2 1-3h1c1 0 0 0 1 1 1 0 1 0 2 2 1 1 6 1 8 1 1 1 1 2 2 2l1 1 1 1h0l1-1c1 0 1 0 2-1 1 1 1 1 1 2h2s1-1 1 0c1-1 2-2 4-2l4 2h1l-2 4c-2 2-3 5-4 8l-1 2c1 1 0 5 0 7h0v4c-1 1-1 2-1 3h-1c-2 3-4 4-6 6h1c1 1 2 1 2 2 2 1 3 1 4 2l1 3v2c1 1 1 2 1 2l-2 1-1-1c-2 0-4 1-6 1h-2-5 0c-3 1-5 0-8 0h-3-3l-8 1h0c-2-1-3-1-4-1h-1-9c-2 1-3 1-5 1h0c-2 0-4 0-5-1l-2 2v-25l-2-2v-5l1-1h4 4c3-1 11-1 13 0h3c0-2-1-4 0-6v-1c0-2 0-4 1-6v-1-2h0c0-2 1-3 2-4 0-1 1-1 2-1l1-1v1l1-1z" class="Y"></path><path d="M332 529c1-1 1-1 2-1l2 1 1 1c0 1-1 1-1 2-2-1-3 0-4-1v-2z" class="c"></path><path d="M342 532h1c2-2 4-3 6-6 1 1 1 1 3 0l2 2c-3 1-5 2-6 4h-6z" class="P"></path><path d="M313 520h4v1c-2 2-2 4-3 7h-4l-2-2v-5l1-1h4z" class="f"></path><path d="M334 519h12c1 2 0 5-1 7 0 0 0 1-1 1l-5 1c-1 0-2-1-3-1 0 0-1 0-1-1-1-2-2-5-1-7z" class="s"></path><path d="M367 519c-1-1-1-3-1-5 1 1 1 2 2 3l1-1 5 1-1 2c1 1 0 5 0 7h0v4h-2c-2 0-4-1-6-1-1 1-1 1-2 1h-2v-1h-1c0-1 0-2-1-2h-3l-2 1-2-2c-2 1-2 1-3 0 0-2 0-5 1-7h5c4 0 8 1 12 0z" class="T"></path><path d="M367 519c-1-1-1-3-1-5 1 1 1 2 2 3l1-1 5 1-1 2c1 1 0 5 0 7h0c0-1 0-4-1-5-2-1-3-2-5-2z" class="G"></path><path d="M360 523c2 0 4 0 6-1 0 2 1 2 2 3v1 2h1l2 2c-2 0-4-1-6-1-1 1-1 1-2 1h-2v-1l-1-4v-2z" class="S"></path><path d="M360 525c1 1 2 2 3 1 2 0 2 1 3 1 0 1-1 2-1 2-1 1-1 1-2 1h-2v-1l-1-4z" class="M"></path><path d="M349 526c0-2 0-5 1-7h5 1c2 1 5 1 8 1-2 1-3 2-4 3v2l1 4h-1c0-1 0-2-1-2h-3l-2 1-2-2c-2 1-2 1-3 0z" class="g"></path><path d="M352 526c1-1 3-1 5-1h2 1l1 4h-1c0-1 0-2-1-2h-3l-2 1-2-2z" class="L"></path><path d="M350 498c1 0 1 0 2 2 1 1 6 1 8 1 1 1 1 2 2 2l1 1 1 1h0l1-1c1 0 1 0 2-1 1 1 1 1 1 2h2s1-1 1 0c1-1 2-2 4-2l4 2h1l-2 4c-2 2-3 5-4 8l-5-1v-2c1 1 1 1 2 0h1l-1-1-1-1c0-1-1-1-2-2h-3v1c-2-1-3 0-4-1h-3-1-6c-1 2 0 4-1 6h0v-9c0-1 1-1 2-1h0l-2-2v-6z" class="S"></path><path d="M375 503l4 2-2 2h-1c-1-1-2-1-3 0l-2-2c1-1 2-2 4-2z" class="K"></path><path d="M350 498c1 0 1 0 2 2 1 1 6 1 8 1 1 1 1 2 2 2l-1 1-1-1c-2 1-3 2-4 1h-6v-6z" class="Z"></path><path d="M351 510v-2h13c3 0 6-1 9 0l-1 2 1 1-1 1h1v3h0l-2-1h1l-1-1-1-1c0-1-1-1-2-2h-3v1c-2-1-3 0-4-1h-3-1-6z" class="G"></path><path d="M340 498h4c1 0 1 0 2 2h-1v1l1 1c1-1 0-1 1-2 0-2 0-2 1-3h1c1 0 0 0 1 1v6l2 2h0c-1 0-2 0-2 1-2 1-1 2-2 3v4c0 1-1 2-2 3v2h-12c-1 2 0 5 1 7 0 1 1 1 1 1 1 0 2 1 3 1l-2 2-1-1-2-1c-1 0-1 0-2 1h-1c0 1 0 0-1 1 0 1 0 1-1 2-1 0-1 0-2-1l-1 1-1-1-5-1v-1c-1-3-1-6-3-8h0v-1c3-1 11-1 13 0h3c0-2-1-4 0-6v-1c0-2 0-4 1-6v-1-2h0c0-2 1-3 2-4 0-1 1-1 2-1l1-1v1l1-1z" class="E"></path><path d="M317 520c3-1 11-1 13 0 0 2 1 5 0 7l-2-2h-1v1c-1 0-2-1-2-1h-2v2c1 1 1 1 3 1-1 1-1 3-1 3l-5-1v-1c-1-3-1-6-3-8h0v-1z" class="s"></path><path d="M334 519c1-3 0-7 0-10h2c2 1 3 1 5 1 0 0 1-1 2-1 1 1 1 1 2 1l1-1 1 1c-1 3-1 5-1 7v2h-12z" class="c"></path><defs><linearGradient id="d" x1="362.308" y1="537.725" x2="318.706" y2="541.892" xlink:href="#B"><stop offset="0" stop-color="#bbbab7"></stop><stop offset="1" stop-color="#eae8e6"></stop></linearGradient></defs><path fill="url(#d)" d="M354 528l2-1h3c1 0 1 1 1 2h1v1h2c1 0 1 0 2-1 2 0 4 1 6 1h2c-1 1-1 2-1 3h-1c-2 3-4 4-6 6h1c1 1 2 1 2 2 2 1 3 1 4 2l1 3v2c1 1 1 2 1 2l-2 1-1-1c-2 0-4 1-6 1h-2-5 0c-3 1-5 0-8 0h-3-3l-8 1h0c-2-1-3-1-4-1h-1-9c-2 1-3 1-5 1h0c-2 0-4 0-5-1l-2 2v-25h4c-1 2-1 2-1 4h2c4 1 7 1 11 2 1 0 2 0 3-1h8c1 0 2-1 3 0 1 0 2 0 2-1h6c1-2 3-3 6-4z"></path><path d="M313 532h2l2 2c0 1 0 1-1 1-1-1-2-1-3-2v-1z" class="E"></path><path d="M373 546v2c1 1 1 2 1 2l-2 1-1-1h-1l1-1c0-1-1-2-1-2v-1h3z" class="F"></path><path d="M335 538c2-1 5-1 7 0v1c-1 0-2 1-3 0-1 0-3 1-4 0v-1zm23 10v1c-1 0-2 0-3 1-2 0-5 0-7-1l1-1h9z" class="Q"></path><path d="M358 548c2-1 7-2 8 0 1 0 1 0 2 1l1 1h-2l-1-1h0c-3-1-6 0-8 0v-1zm-14-1c2 0 3-1 5-1l-1 1h-1s1 1 2 1h0l-1 1c2 1 5 1 7 1-2 0-4 0-5 1h-3-3c-1 0-1-1-2-2h1l1-2z" class="W"></path><path d="M343 549h2l2 2h-3c-1 0-1-1-2-2h1z" class="d"></path><path d="M358 549c2 0 5-1 8 0h0l-1 2h-2-5 0c-3 1-5 0-8 0 1-1 3-1 5-1 1-1 2-1 3-1z" class="B"></path><path d="M313 533c1 1 2 1 3 2l2 1c2 0 3-1 5-1v1 1h-2l-1 2h-1c-1 0 0 0-1 1 3 1 7-1 10 0-4 0-12 1-15 0v-7z" class="Y"></path><path d="M359 544h0v-1h1c1-2 2-3 3-4 2-3 2-4 5-4h1v-1h-3 0-2 0l-1-1h1c2-1 4-1 7-1l1 1h-1c-2 3-4 4-6 6h1c1 1 2 1 2 2 2 1 3 1 4 2l1 3h-3c-1 1-1 1-2 1l-2-2h-1c-2 1-3 1-4 1l-2-2z" class="h"></path><path d="M359 544c2-1 5-3 7-2 1 0 2 1 3 2h1v1h-4-1c-2 1-3 1-4 1l-2-2z" class="F"></path><path d="M354 528l2-1h3c1 0 1 1 1 2h1v1l-2 1h0c3 1 4 1 6 0h1c-1 0-1 0-2 1h-3 0c-5 4-13 4-18 4-1-1-2-1-2-1h-1c-2 0-2-1-3-2 1 0 2-1 3 0 1 0 2 0 2-1h6c1-2 3-3 6-4z" class="c"></path><path d="M354 528l2-1h3c1 0 1 1 1 2h1v1l-2 1c-4 0-7 1-11 1 1-2 3-3 6-4z" class="S"></path><path d="M310 528h4c-1 2-1 2-1 4v1 7c3 1 11 0 15 0 4 1 9 0 12 2h0c2 0 3 0 4 1h0-2v2l1-1 1 1h0c-1 0-2 1-3 2h3l-1 2h-1c1 1 1 2 2 2l-8 1h0c-2-1-3-1-4-1h-1-9c-2 1-3 1-5 1h0c-2 0-4 0-5-1l-2 2v-25z" class="d"></path><path d="M330 545h2c0 1-1 2-1 3-1 0-1-1-2-1 0-1 0-2 1-2z" class="B"></path><path d="M332 551c1-1 1-2 2-3v-2c3 0 5 0 7 1h3l-1 2h-1c1 1 1 2 2 2l-8 1h0c-2-1-3-1-4-1z" class="F"></path><path d="M336 552c-1-1-1-2-2-3l1-2 2 1c2 0 3 1 5 1 1 1 1 2 2 2l-8 1h0z" class="Y"></path><path d="M314 543c1 1 2 1 3 3 0 0-1 1-1 2 1 0 1 1 2 1l2-1h1v-1h-2v-1h0l7-1h4c-1 0-1 1-1 2 1 0 1 1 2 1v3h-9c-2 1-3 1-5 1l-3-1c-1-1 0-3 0-4v-4z" class="c"></path><path d="M326 545h4c-1 0-1 1-1 2 1 0 1 1 2 1v3h-9c0-1 1-1 2-2h2c1 0 1 1 3 1v-1c-1-1-1-1-3-1v-3z" class="W"></path><path d="M310 528h4c-1 2-1 2-1 4v1 7c3 1 11 0 15 0 4 1 9 0 12 2h0v1h0c-2-1-5-1-7-1-7-1-13 0-20 0v1h1 0v4c0 1-1 3 0 4l3 1h0c-2 0-4 0-5-1l-2 2v-25z" class="k"></path><path d="M346 427h16 11c2 0 5 1 7 0h1 7 0c1 0 2 1 3 1l-3 8c0 1-1 2-1 3-1 2-2 3-2 5-1 5-2 11-2 16 0 3 1 5 1 7 0 3 1 9 1 12-2-2-2-1-4-1-1 0-3-1-4 0h0-3-1l-1 1-1-1h-1v1c-1 0-1 0-1 1h-4c-1-2-1-4-1-7 0 1-1 1-1 2v1s-2 1-2 2c-1 0-2-1-2-1h-1l-6-1c-6-1-12-1-18-1l-6 1c-4 1-10 2-14 4l-1-5c-1-1-2-2-1-3h1 5 0c2 0 3-1 4-1 0-1-2-2-3-3l-1-1h0v-2l1-1-1-1 1-2c0-1-1-1-1-1s-4-1-5-1c-3-1-5-1-7-3l-2-3-1-1c-1-2-1-5 0-8 1-2 2-3 4-4 2 0 4 1 5 1 1 2 1 4 1 6h3c0 1 0 2 1 4h-1l1 1 3 1v-2-1c2-1 3-1 5-1l3-1c2-3 3-1 6-2l1-1 1-2c1-1 1-1 0-2l-3-1h0 0c1-1 1-2 2-3v-1l1-1v-1-1h0c1 0 2 0 2-1l3-1v-1c2-1 3-2 5-3z" class="p"></path><path d="M328 476h0c-1-2 0-3 0-5 2-1 2 0 4-1l1 1c0 1 0 2 1 3l1-2c2 2 5 1 8 2h2c-2 2-8 1-11 1l-6 1z" class="T"></path><path d="M345 474v-4c2-2 2-1 4-1h3l1 1s1-1 2-1h0c1 0 2 0 3 1h1 0c1-1 3-1 4 0s1 2 1 3-1 1-1 2v1s-2 1-2 2c-1 0-2-1-2-1h-1l-6-1c-6-1-12-1-18-1 3 0 9 1 11-1z" class="N"></path><path d="M347 456h10c2 0 4 0 6 1l1 1h6 6c2 0 4 0 6 1v4 1l-44 1h-2l-1-3c-1 0-2 0-3-1s-1-1 0-3h8l3-1v1c2 0 2 0 4-2z" class="J"></path><path d="M340 458c0 1-1 3-1 5 0 0 1 0 0 1l-2-2h-2c-1 0-2 0-3-1s-1-1 0-3h8z" class="F"></path><path d="M370 458h6c2 0 4 0 6 1v4c-2 0-4 1-5 0v-2c-1-1-3 0-4-1l1-2h-4z" class="B"></path><path d="M307 440c2 0 4 1 5 1 1 2 1 4 1 6h3c0 1 0 2 1 4h-1l1 1 3 1v-2-1c2-1 3-1 5-1l3-1 7-1h6c1 1 1 2 1 4s-1 3-2 5h7c-2 2-2 2-4 2v-1l-3 1h-8c-1 2-1 2 0 3s2 1 3 1l1 3h2l-10 1h-5l-4 1v1l-1-1h0v-2l1-1-1-1 1-2c0-1-1-1-1-1l-5-1c-3-1-5-1-7-3l-2-3-1-1c-1-2-1-5 0-8 1-2 2-3 4-4z" class="R"></path><path d="M317 452c0 2-1 3-2 4h-1c1 1 0 1 0 1l-2 1c0-1-1-2-1-4h2v-1c1-1 2-1 3-2l1 1z" class="L"></path><path d="M325 449l1 3-1 1c2 1 2 1 3 1 2 0 3 2 4 3h-7l-2 1h0-4c0-2 1-3 1-5h0v-2-1c2-1 3-1 5-1z" class="V"></path><path d="M323 458v-2l1-1h1l1 1-1 1-2 1z" class="D"></path><path d="M325 449l1 3-1 1c-2-1-3 0-5-2v-1c2-1 3-1 5-1zm3 9h4c-1 2-1 2 0 3s2 1 3 1l1 3h2l-10 1h-5-3l2-7 6-1z" class="I"></path><path d="M323 466h-3l2-7 6-1-1 2v1c1 0 1 0 1 1-1 1-2 0-3 0v-1h-2c0 1 0 2 1 3h0 2l2 2h-5z" class="C"></path><path d="M307 440c2 0 4 1 5 1 1 2 1 4 1 6s-1 3-2 5l-1 1h-1-1v2h-1l-1-2h-2l-1-1c-1-2-1-5 0-8 1-2 2-3 4-4z" class="p"></path><path d="M335 447h6c1 1 1 2 1 4s-1 3-2 5h7c-2 2-2 2-4 2v-1h-11c-1-1-2-3-4-3-1 0-1 0-3-1l1-1-1-3 3-1 7-1z" class="W"></path><path d="M336 451c2-1 4-1 6 0 0 2-1 3-2 5h0c-1 0-1-1-1-2h0v-2c-1-1-1-1-3-1z" class="J"></path><path d="M335 447h6c1 1 1 2 1 4-2-1-4-1-6 0h-1c-1-1 0-2 0-4z" class="c"></path><path d="M326 452h4 0c1 0 2-1 3-1 2 1 1 1 1 3 2 1 4 2 6 2h0 7c-2 2-2 2-4 2v-1h-11c-1-1-2-3-4-3-1 0-1 0-3-1l1-1z" class="B"></path><path d="M356 442c1-1 3-1 5-1 1 0 1 0 1 1v1l1 1v-1-1h4c0 1 0 2 1 2h3 8 1 4 1c-1 5-2 11-2 16 0 3 1 5 1 7-1-1-1-2-2-3v-1-4c-2-1-4-1-6-1h-6-6l-1-1c-2-1-4-1-6-1h-10-7c1-2 2-3 2-5s0-3-1-4l1-1c0-1 1-1 2-2 1 0 2-1 3-1 3-1 6-1 9-1z" class="l"></path><path d="M347 451h2c0 1-1 2-1 3-1 1-2 1-2 1-2-1-2-1-2-2 1-1 2-1 3-2z" class="c"></path><path d="M349 451h3c3-1 7-1 10-1v3c-2 1-7 1-10 1h-4c0-1 1-2 1-3z" class="J"></path><path d="M365 456c1 0 1 0 2-1h-1l-1-1 1-2h2 2v-1c1 0 2 0 3 1h0l10-1c0 2 0 4-1 5h-12-5z" class="B"></path><path d="M373 452h0 1c2 1 3 1 4 1l1 1v2h-6c1-1 1-2 2-3h-3c-1 0-1 0-2-1h3z" class="Y"></path><path d="M365 456c1 0 1 0 2-1h-1l-1-1 1-2h2 2v-1c1 0 2 0 3 1h-3c1 1 1 1 2 1h3c-1 1-1 2-2 3h-3-5z" class="Q"></path><path d="M356 442c1-1 3-1 5-1 1 0 1 0 1 1v1l1 1v-1-1h4c0 1 0 2 1 2h3 8 1 4 1c-1 5-2 11-2 16 0 3 1 5 1 7-1-1-1-2-2-3v-1-4c-2-1-4-1-6-1h-6-6l-1-1 2-1h5 12c1-1 1-3 1-5l1-4-10-1h-3c-3-1-8-1-11-1-2 0-3 1-4 0v-2-1z" class="S"></path><path d="M342 446c0-1 1-1 2-2 1 0 2-1 3-1 3-1 6-1 9-1v1 2c1 1 2 0 4 0 3 0 8 0 11 1h3v1c3 2 6 0 9 0 0 2 0 2-1 3s-1 1-2 1c-3-3-13-1-18-1-3 0-7 0-10 1h-3-2-3l-1 1v-4c0-1 1-1 2-2h-3z" class="E"></path><path d="M342 446c0-1 1-1 2-2 1 0 2-1 3-1 3-1 6-1 9-1v1 2c1 1 2 0 4 0 3 0 8 0 11 1h-26-3z" class="n"></path><path d="M346 427h16 11c2 0 5 1 7 0h1 7 0c1 0 2 1 3 1l-3 8c0 1-1 2-1 3-1 2-2 3-2 5h-1-4-1-8-3c-1 0-1-1-1-2h-4v1 1l-1-1v-1c0-1 0-1-1-1-2 0-4 0-5 1-3 0-6 0-9 1-1 0-2 1-3 1-1 1-2 1-2 2l-1 1h-6l-7 1c2-3 3-1 6-2l1-1 1-2c1-1 1-1 0-2l-3-1h0 0c1-1 1-2 2-3v-1l1-1v-1-1h0c1 0 2 0 2-1l3-1v-1c2-1 3-2 5-3z" class="r"></path><path d="M381 434c2 0 4 0 6 1 0 1 0 2-1 3l-3 1h-6c2 0 3 0 5-1v-3l-6-1h5z" class="J"></path><path d="M340 436l1-1c4-1 7-1 12-1-4 1-8 1-11 2l-2 2c1 1 1 1 2 1l-3 1h-6 0c1-1 1-2 2-3h1c2-1 3-1 4-1z" class="c"></path><path d="M381 427h7 0c1 0 2 1 3 1l-3 8c0 1-1 2-1 3h-4l3-1c1-1 1-2 1-3-2-1-4-1-6-1l1-3v-1c0-1-1-1-1-2v-1z" class="C"></path><path d="M383 439h4c-1 2-2 3-2 5h-1-4-1-8-3c-1 0-1-1-1-2h-4v1 1l-1-1v-1c0-1 0-1-1-1-2 0-4 0-5 1-3 0-6 0-9 1-1 0-2 1-3 1-1 1-2 1-2 2l-1 1h-6l-7 1c2-3 3-1 6-2l1-1 1-2c1-1 1-1 0-2l-3-1h0 6l3-1h31 4 6z" class="j"></path><path d="M333 440h6 8v1h-5v1 1c-2 2-4 2-7 2l1-2c1-1 1-1 0-2l-3-1h0z" class="U"></path><path d="M383 439h4c-1 2-2 3-2 5h-1-4-2l-1-1c-1-1-2-1-3-1h-2c0-2 0-2 1-3h4 6z" class="b"></path><path d="M346 427h16 11c2 0 5 1 7 0h1v1c0 1 1 1 1 2v1l-1 3h-5-23c-5 0-8 0-12 1l-1 1c-1 0-2 0-4 1h-1v-1l1-1v-1-1h0c1 0 2 0 2-1l3-1v-1c2-1 3-2 5-3z" class="B"></path><path d="M346 427h16-1-13l-4 4c-1 1-2 2-3 2 0 1 0 2-1 3-1 0-2 0-4 1h-1v-1l1-1v-1-1h0c1 0 2 0 2-1l3-1v-1c2-1 3-2 5-3z" class="H"></path><path d="M393 494h1l2 1c1 0 1-1 2-1h0v1l-3 4 2-1h4c-1 2-4 7-5 8l-2 4c1-1 2-1 3-1l4 4c3 2 5 4 7 6l2 3h1l1-1 1-1v1 5h0l1 2c1-1 0-2 1-4v5c0 1 1 2 1 3 1 0 2 1 2 1 4 3 5 5 6 10v6c0 4-1 8-1 13h23c1 0 5-1 7 0l-1 9v8 5h0l-1-1c0 1 0 2-1 3l-2 1c-1 7-1 13-1 19v11 40h-1c2 2 1 5 1 7v15 19 5h0c-6-2-13-4-17-7-1-2-24-12-27-16l-2-1s0-1-1-2h-1c-1-1-3-3-3-4-1-2-3-4-5-5-3-1-7-4-9-6-5-7-14-12-18-19-3-2-5-6-7-8-1-2-4-3-5-5-2-3-3-6-5-8h-1v-2l-1-1-1-1-2 2-2-2-6-9-2-1h1c-2-3-3-6-6-9h1l-5-8 1-1h-1l1-2v-1l1 1 1-1h2v-2h0 3v2l1-1h6l1-1c3-2 8-2 11-4h1l3-3c-1-1-1-2-1-3l-1-1h-1 0l-1-1c3 0 4 0 7-1h0v-11l-1-2h2c1-2 1-3 1-5v-2l-1-1h0 5 2c2 0 4-1 6-1l1 1 2-1s0-1-1-2v-2l-1-3c-1-1-2-1-4-2 0-1-1-1-2-2h-1c2-2 4-3 6-6h1c0-1 0-2 1-3v-4h0c0-2 1-6 0-7l1-2c1-3 2-6 4-8l2-4v-1h1v-1h0c1-1 1-2 2-3l3-3h4l2-1 1-2z" class="s"></path><path d="M452 571v8 5h0l-1-1c0 1 0 2-1 3l-2 1 4-16z" class="D"></path><path d="M418 622v-2h2v-2l-2 1v-2c1 0 1 0 3 1v-1l-1-1 1-1v1l1 1c0 1-1 1-1 2h1v-1h1v-1-2c1-2 1-1 1-2h0l1-1c1 1 1 3 1 4v2l1 1-2 1c-1-1-1-1-2 0s-1 1-2 1-2 1-3 1z" class="R"></path><path d="M388 605v-3c1 0 1 1 2 2 1 0 3-1 4-2 1 1 1 4 1 5v5c0 1 1 2 0 2h-1-1c0-1 0-2-1-3v1h-2l-3 2v-3c0-2 2-3 2-4s0-1-1-2z" class="k"></path><path d="M415 597h-24v-1c1-1 3-2 5-3 4 1 9 1 14 1h1 4 0c0 1-1 1-2 1h-3c1 1 1 1 2 1s2 0 3 1z" class="V"></path><path d="M421 568h3 1c2 0 4 0 5 1v1h2l1-1 1 1-2 2-1 1h1c1 0 0 0 1 1h3c-1 1-2 1-3 2h0c2 0 3 0 4-1l1 1c-1 1-2 1-4 1h-1c-1 1-1 1-3 1l-2-2c-1 0-2 0-3 1 0-1-1-2-1-3l-1-1v-1l-2 1h-1v1l-1-1c-1 0-1 0-2-1h1 3 1l-1-4z" class="a"></path><path d="M421 568h3 1c0 1 1 1 1 2l2 2v1l-1-1v1 1 1l1 1c-1 0-2 0-3 1 0-1-1-2-1-3l-1-1v-1l-2 1h-1v1l-1-1c-1 0-1 0-2-1h1 3 1l-1-4z" class="m"></path><path d="M421 568h3v3h-1c-1 0 0 0-1 1l-1-4z" class="e"></path><path d="M423 620l1 1 3-1c0-2 0-3 1-4v-3-1l-1-1v-1c1 0 1-1 0-2 1 0 2-1 3-1 1 2 1 4 2 6v1c1 3 2 8 2 11-1 1-3 3-5 3v-1l-3 3 1 2c0 1-1 1-2 1 0-1 0-1-1-2v-1h0-4l-1-1 1-2c0-1 0-1-1-1h0v-1h1v-1h-1l-1-2c1 0 2-1 3-1s1 0 2-1z" class="Z"></path><path d="M429 627v-6h1v4l1-1 1-1c0 2 1 2 2 2-1 1-3 3-5 3v-1z" class="L"></path><path d="M402 580l8 3c1 0 3 1 5 1h0c2 1 3 0 4 2h0v3 1h-1c0 1 0 1 1 2h1c1-1 1-1 2-1l1-2 3 3c2 0 4 0 6 1h0l-1 1-1 1h4v1c-4 1-9 1-13 1h-6c-1-1-2-1-3-1s-1 0-2-1h3c1 0 2 0 2-1h0-4-1c-5 0-10 0-14-1l3-1c2-3 3-4 2-7v-1c-3 1-5 1-8 0h7 4l-1-1c0-1 0-2-1-3z" class="S"></path><path d="M409 585h-1c1-2 1-2 2-2s3 1 5 1h0l-1 2h0l-2-1-1 1-2-1z" class="T"></path><path d="M399 592h1c1 0 1 0 2-1 1 0 2 0 3 1v-1l-1-1h1l2 2s1 0 2 1l1 1c-5 0-10 0-14-1l3-1z" class="n"></path><path d="M402 580l8 3c-1 0-1 0-2 2h1c-1 1-1 0-1 1-1 0-2 1-3 1v-1h1l-1-2c-1 1-2 1-3 1h-1v-1c-3 1-5 1-8 0h7 4l-1-1c0-1 0-2-1-3z" class="U"></path><path d="M415 584c2 1 3 0 4 2h0v3 1h-1l-2 1h-1l1-1-1-1c-1 0-2-1-2-1-1-1-2 1-3 1l-1-1c1 0 3-1 4-2h1 0l1-2z" class="K"></path><path d="M406 558c0-1 0-1 1-2v2 4h2c1-1 1-2 1-4s-1-2-3-3c1-1 1-2 2-3 0 4 2 4 4 7h1c1 0 2 0 2 1 1 0 2 1 3 1l-1 1v4 1c1 1 2 1 3 1l1 4h-1-3-1c1 1 1 1 2 1l1 1v-1h1l2-1v1l1 1c0 1 1 2 1 3-1 1-1 1-2 1 1 1 1 3 2 3l3 3v1c-1 0-2-1-3-1-2 0-3 0-5 1 0 2 0 2-1 4v-3h0c-1-2-2-1-4-2h0c-2 0-4-1-5-1l-8-3h0c1-1 1-2 1-3l2-1 2 1 1 1 5 1c0-1 0 0-1-1v-1c2-1 2-1 3-2l-1-1 1-1h1c-2-1-3-1-5-2-1 0-1 0-2-1h0 3c0-1 1-1 1-2l-4-2h0c-2-1-4-3-5-4l1-2 1-2z" class="q"></path><path d="M413 568h0c1 1 2 1 3 1l2 1v1c-2 0-6-1-7 0-1 0-1 0-2-1h0 3c0-1 1-1 1-2z" class="D"></path><path d="M405 576l2 1 1 1c0 1-1 2 0 3 3 1 6 1 7 3-2 0-4-1-5-1l-8-3h0c1-1 1-2 1-3l2-1z" class="C"></path><path d="M420 574v-1h1l2-1v1l1 1c0 1 1 2 1 3-1 1-1 1-2 1 1 1 1 3 2 3l3 3v1c-1 0-2-1-3-1l-3-2c-1-1-2 1-4 0l1-1-3-3h0 2l2 1h1c-1-2-3-3-3-5h2z" class="S"></path><path d="M390 573c3 0 5-1 7 1l1 1c1 0 2-1 3 0l1-1 1 1 2 1-2 1c0 1 0 2-1 3h0c1 1 1 2 1 3l1 1h-4-7 0-2-15l-23 1c-1-1-2-1-3-1v-3h1c2 0 2 0 3-1h2c2 0 5 1 7 0h2c1 0 2 0 4-1 0-1 1-1 1-1h1c2-1 4-1 6-1 0 0 1-1 2-1h2 2l7-3z" class="o"></path><path d="M398 575c1 0 2-1 3 0l1-1 1 1 2 1-2 1c0 1 0 2-1 3l-3-2c-1-1-1-2-1-3zm-21 2s1-1 2-1h2 2c2 1 4-1 5 0v2c1 0 2 1 3 1l1 1 5 1h0c-1 1-2 1-3 2l-1-1h-1v1h7l1 1h-7 0-2-15c3-1 6-1 9-1l1-2h2v-1h-5-3l2-2c-2 0-3 0-5-1z" class="D"></path><path d="M369 579c0-1 1-1 1-1h1c2-1 4-1 6-1 2 1 3 1 5 1l-2 2h3 5v1h-2l-1 2c-3 0-6 0-9 1l-23 1c-1-1-2-1-3-1v-3h1c2 0 2 0 3-1h2c2 0 5 1 7 0h2c1 0 2 0 4-1z" class="Y"></path><path d="M363 580c2 0 5 1 7 1l1 1c-3 1-8 1-11 0h-1c-1-1-2-1-3-2 2 0 5 1 7 0z" class="l"></path><path d="M383 580h5v1h-2c-4 2-11 2-15 1l-1-1 1-1c2 1 3 1 5 1l7-1z" class="Q"></path><path d="M369 579c0-1 1-1 1-1h1c2-1 4-1 6-1 2 1 3 1 5 1l-2 2h3l-7 1c-2 0-3 0-5-1l-1 1c-2 0-5-1-7-1h2c1 0 2 0 4-1z" class="G"></path><path d="M369 579c2 0 4 0 7 2-2 0-3 0-5-1l-1 1c-2 0-5-1-7-1h2c1 0 2 0 4-1z" class="c"></path><path d="M372 590l1-1h2 3l5 5c1 1 1 0 1 1 1 1 1 2 1 3l-1 3 1 2v1h1l2 2v-1c1 1 1 1 1 2s-2 2-2 4v3 1h-1l-3 4-6 5-2 1c-2-3-6-6-9-10h-1v-1l3-5v2l1-3v-1l-3-5c-1-3-1-5 1-7 1-2 3-3 5-5z" class="J"></path><path d="M376 603l1-1c1-2 1-3 2-3 1-1 2-2 3-2 0 1 0 2 1 2-2 2-4 10-8 10l-3-3c2-1 2-3 4-3z" class="o"></path><path d="M372 606c2-1 2-3 4-3l2 3c-1 1-2 1-3 2v1l-3-3z" class="n"></path><path d="M384 601l1 2v1h1c-2 1-2 2-3 3v1c-1 2-1 3-2 5h-1v2l-1 1-4 3-1-1c-3-2-4-4-6-7l1-3 2 2c1 2 3 4 5 6 2-3 5-7 6-11 1-1 1-2 2-3v-1z" class="L"></path><path d="M386 604l2 2v-1c1 1 1 1 1 2s-2 2-2 4v3 1h-1l-3 4c-1-2-2-2-4-3l1-1v-2h1c1-2 1-3 2-5v-1c1-1 1-2 3-3z" class="H"></path><path d="M386 604l2 2v-1c1 1 1 1 1 2s-2 2-2 4v3 1h-1v-2h-2v-1l1-1v-2l-2-1v-1c1-1 1-2 3-3z" class="N"></path><path d="M366 615h-1v-1l3-5v2c2 3 3 5 6 7l1 1 4-3c2 1 3 1 4 3l-6 5-2 1c-2-3-6-6-9-10z" class="F"></path><path d="M374 618l1 1 2 1v1h-3l-2-2 2-1z" class="d"></path><path d="M372 590l1-1h2 3l5 5c1 1 1 0 1 1l-1 1c1 1 1 1 1 2l-1 1c-1 0-1-1-1-2-1 0-2 1-3 2-1 0-1 1-2 3l-1 1c-2 0-2 2-4 3h0c-1 1-1 2-1 4l-2-2v-1l-3-5c-1-3-1-5 1-7 1-2 3-3 5-5z" class="N"></path><path d="M366 602c0-1 0-2 1-3h1c1 3 2 4 3 6 1 0 1 1 1 1-1 1-1 2-1 4l-2-2v-1l-3-5z" class="B"></path><path d="M372 590l1-1h2 3l-2 1h0l3 3-1 1c1 2 1 2 0 3-1 0-1 0-2-1v-3l-2-1-1 1h1v1c0 1-1 2-2 3 0 1 0 1-1 2l-1-1-1 1h-1-1c-1 1-1 2-1 3-1-3-1-5 1-7 1-2 3-3 5-5z" class="R"></path><path d="M372 590c0 1 0 2-1 2-2 2-3 5-3 7h-1c-1 1-1 2-1 3-1-3-1-5 1-7 1-2 3-3 5-5z" class="D"></path><path d="M410 522h1l1-1 1-1v1 5h0l1 2c1-1 0-2 1-4v5c0 1 1 2 1 3 1 0 2 1 2 1 4 3 5 5 6 10v6c0 4-1 8-1 13-2 1-3 3-5 4v-4l1-1c-1 0-2-1-3-1 0-1-1-1-2-1h-1c-2-3-4-3-4-7-1 1-1 2-2 3 2 1 3 1 3 3s0 3-1 4h-2v-4-2c-1 1-1 1-1 2l-1 2c-2-2-3-2-5-3-1-1-2-3-3-4l-1 1c-1-3-5-8-4-11v1l3 6c4-2 8-5 12-9l5-3h0v-1c0-1 0-2-1-3v-2c0-2 1-6-1-9v-1z" class="V"></path><path d="M406 546c2-2 4-4 7-5v1c-1 2-1 2-1 3h-2c-1 1 0 1-1 2 0 1-1 3-1 3v-4h-2zm3 6h1l2-2c1 0 1 0 2 1h1l1 1v1h-2v1c0 1 0 2 1 3h-1c1 1 2 1 3 1v1l-1 1c0-1-1-1-2-1h-1c-2-3-4-3-4-7z" class="D"></path><path d="M406 546h2v4c-3 3-3 4-2 8l-1 2c-2-2-3-2-5-3-1-1-2-3-3-4 3-2 6-4 9-7z" class="B"></path><path d="M412 538c1 0 2 0 3 1v-1c1 0 1 1 1 2 2 4 4 9 3 14h-1v-2c0-2-2-5-3-7h0c-1-1-1-2-2-3v-1c-3 1-5 3-7 5-3 3-6 5-9 7l-1 1c-1-3-5-8-4-11v1l3 6c4-2 8-5 12-9l5-3h0z" class="b"></path><path d="M410 522h1l1-1 1-1v1 5h0l1 2c1-1 0-2 1-4v5c0 1 1 2 1 3 1 0 2 1 2 1 4 3 5 5 6 10v6c0 4-1 8-1 13-2 1-3 3-5 4v-4l1-1c-1 0-2-1-3-1l1-1h2l1-1v-6c1 0 1-1 1-1 0-3-1-8-3-11h0-2c0-1 0-2-1-2v1c-1-1-2-1-3-1v-1c0-1 0-2-1-3v-2c0-2 1-6-1-9v-1z" class="B"></path><path d="M410 522h1l1-1 1-1v1 5h0l1 2c1-1 0-2 1-4v5c0 1 1 2 1 3 1 0 2 1 2 1 4 3 5 5 6 10h-1c-1-2-1-5-2-7-1-1-3-2-4-2s-1-1-2-1v1c3 2 3 3 3 6h0-2c0-1 0-2-1-2v1c-1-1-2-1-3-1v-1c0-1 0-2-1-3v-2c0-2 1-6-1-9v-1z" class="M"></path><path d="M339 585c3-2 8-2 11-4v3c1 0 2 0 3 1l-1 1v1h3v1l-1 1 1 1c2 0 3 0 4 2 1 1 3 1 4 2 1 0 2 0 4 1-2 2-2 4-1 7l3 5v1l-1 3v-2l-3 5v1h1c0 2-1 3-1 5h0l-2 2-4-5-3-4-1-1-2-1c-1-1-2-2-3-4l-1 1 1 1-1 1c-1-1-2-3-3-3s0 2-1 3l1 1v1c-1 0-1 1-2 1 0-1 0-2-1-3h0-4l2 4-1 4-6-9-2-1h1c-2-3-3-6-6-9h1l-5-8 1-1h-1l1-2v-1l1 1 1-1h2v-2h0 3v2l1-1h6l1-1z" class="L"></path><path d="M346 601c0-1-1-2-1-2h-1v1c-2 0-3-1-5-1l1-1h0l1-1h3c1 2 2 2 4 3l3 3 1 1-2 1-4-4z" class="R"></path><path d="M358 596l2 1 1 1c-1 0-1 0-2 1-1 0-2 1-2 1h-2c-1 0-1-2-2-1-2 1-4 1-5 1-2-1-3-1-4-3l14-1z" class="K"></path><path d="M346 601l4 4c3 2 4 4 5 7l-2-1c-1-1-2-2-3-4l-1 1 1 1-1 1c-1-1-2-3-3-3s0 2-1 3c-1-1-2-2-2-4h0l-3-3 1-1c1 0 3 1 4 1l1 1h0v-3z" class="C"></path><path d="M350 592c2 0 6 0 7 1v2h1c1 0 1 0 2 1h-2-23-2l2-2c1-1 5 0 6 0 2 0 4-1 7-1h2v-1z" class="G"></path><path d="M329 596h1 3 2l-1 3c0 1 0 1 1 1 2 1 3 1 4 2 0 1 1 2 1 4 1 1 2 3 3 4h-4l2 4-1 4-6-9-2-1h1c-2-3-3-6-6-9h1l1-1v-2z" class="i"></path><path d="M334 609c1 0 2 1 3 3l1 1 1 1c0-2 0-2-1-3v-1h1l2 4-1 4-6-9z" class="D"></path><path d="M333 596h2l-1 3c0 1 0 1 1 1 2 1 3 1 4 2-1 1-2 1-2 2l-1-1-1 1c-2-2-4-6-5-8h3z" class="S"></path><path d="M363 594c1 0 2 0 4 1-2 2-2 4-1 7l3 5v1l-1 3v-2l-3 5v1h1c0 2-1 3-1 5h0l-2 2-4-5-3-4-1-1c-1-3-2-5-5-7l2-1-1-1-3-3c1 0 3 0 5-1 1-1 1 1 2 1h2s1-1 2-1c1-1 1-1 2-1l-1-1h3c0-1 0-1 1-2l-1-1z" class="M"></path><path d="M366 604h0c0 1 0 1-1 2v2c1 0 3-1 4-1v1l-1 3v-2l-3 5v1h1c0 2-1 3-1 5h0-1v-7h0l-1-1v-2h1c1-2 0-4 0-5l2-1z" class="q"></path><path d="M358 604h0c2 2 3 4 3 7v3c1 1 2 0 2 2v3c-2-1-3-3-3-4v-1-2h-1-1v-1c0-1 0-1-1-2 0-1 0-3 1-4v-1z" class="V"></path><path d="M352 604h1c1-1 1-1 1-2 1 1 1 1 1 2l1 1h0l2-1v1c-1 1-1 3-1 4 1 1 1 1 1 2l-2 2-1-1c-1-3-2-5-5-7l2-1z" class="i"></path><path d="M363 594c1 0 2 0 4 1-2 2-2 4-1 7l3 5c-1 0-3 1-4 1v-2c1-1 1-1 1-2h0c-1-1-2-1-3-1l-1 1v1h-1c1-2 1-3 1-4v-1-1h-1c-1 1-2 2-2 3-1 0-2 1-3 2h-1c0-1 0-1-1-2 0 1 0 1-1 2h-1l-1-1-3-3c1 0 3 0 5-1 1-1 1 1 2 1h2s1-1 2-1c1-1 1-1 2-1l-1-1h3c0-1 0-1 1-2l-1-1z" class="O"></path><path d="M339 585c3-2 8-2 11-4v3c1 0 2 0 3 1l-1 1v1h3v1l-1 1 1 1c2 0 3 0 4 2 1 1 3 1 4 2l1 1c-1 1-1 1-1 2h-3l-2-1h0 2c-1-1-1-1-2-1h-1v-2c-1-1-5-1-7-1v1h-2c-3 0-5 1-7 1-1 0-5-1-6 0l-2 2h-3-1v2l-1 1-5-8 1-1h-1l1-2v-1l1 1 1-1h2v-2h0 3v2l1-1h6l1-1z" class="E"></path><path d="M329 596v-1c2-1 4-1 6-1l-2 2h-3-1z" class="B"></path><path d="M328 585h0 3v2h0l2 1-1 2h4 1c-1 1-3 1-4 2h-7v-1-1h-2-1l1-2v-1l1 1 1-1h2v-2z" class="Q"></path><path d="M328 585h0 3v2h0-1v1h-5l1-1h2v-2z" class="W"></path><path d="M339 585c3-2 8-2 11-4v3c1 0 2 0 3 1l-1 1v1h3v1l-1 1 1 1c2 0 3 0 4 2 1 1 3 1 4 2l1 1c-1 1-1 1-1 2h-3l-2-1h0 2c-1-1-1-1-2-1h-1v-2c-1-1-5-1-7-1h-7l-1-1c2 0 4 0 5-1h-2v-1l2-1v-1h-2 0c-4 0-8 0-12 1l-2-1h0l1-1h6l1-1z" class="e"></path><path d="M393 494h1l2 1c1 0 1-1 2-1h0v1l-3 4 2-1h4c-1 2-4 7-5 8l-2 4c1-1 2-1 3-1l4 4c3 2 5 4 7 6l2 3v1c2 3 1 7 1 9v2c1 1 1 2 1 3v1h0l-5 3c-4 4-8 7-12 9l-3-6v-1c-1 3 3 8 4 11l1-1c1 1 2 3 3 4 2 1 3 1 5 3l-1 2c1 1 3 3 5 4h0l4 2c0 1-1 1-1 2h-3 0c1 1 1 1 2 1 2 1 3 1 5 2h-1l-1 1 1 1c-1 1-1 1-3 2v1c1 1 1 0 1 1l-5-1-1-1-2-1-2-1-1-1-1 1c-1-1-2 0-3 0l-1-1c-2-2-4-1-7-1l-7 3h-2-2c-1 0-2 1-2 1-2 0-4 0-6 1h-1s-1 0-1 1c-2 1-3 1-4 1h-2c-2 1-5 0-7 0h-2c-1 1-1 1-3 1l3-3c-1-1-1-2-1-3l-1-1h-1 0l-1-1c3 0 4 0 7-1h0v-11l-1-2h2c1-2 1-3 1-5v-2l-1-1h0 5 2c2 0 4-1 6-1l1 1 2-1s0-1-1-2v-2l-1-3c-1-1-2-1-4-2 0-1-1-1-2-2h-1c2-2 4-3 6-6h1c0-1 0-2 1-3v-4h0c0-2 1-6 0-7l1-2c1-3 2-6 4-8l2-4v-1h1v-1h0c1-1 1-2 2-3l3-3h4l2-1 1-2z" class="J"></path><path d="M391 561l-2-2v-1h1 0c1 1 1 1 2 1l3 3-4-1z" class="E"></path><path d="M384 564c2 0 3 1 5 2h0-1l-4 2v-4z" class="F"></path><path d="M380 544h0l3 6 1 1c1 2 1 3 0 4h0c-2-1-3-2-3-4l-3-6 2-1z" class="d"></path><path d="M384 568l4-2-1 3h1l2-1c3 0 4 1 6 2s4 1 5 1l1 1c1 1 2 2 1 3l-1-1-1 1c-1-1-2 0-3 0l-1-1c-2-2-4-1-7-1-2-1-3 0-5 0h-1l2-3-1-1-1-1z" class="W"></path><path d="M383 500l3-3h4c-1 2-2 3-3 6s-5 7-5 11c0 1-1 2-1 3h-1-1c-1 4-1 9-1 13s-1 8 0 12h-1c-2-2-1-4-2-7 0 2 1 2 0 3h-1l1 1v2c0 2 0 3 1 5 1 1 1 1 1 2l-1 1-3-1v-2l-1-3c-1-1-2-1-4-2 0-1-1-1-2-2h-1c2-2 4-3 6-6h1c0-1 0-2 1-3v-4h0c0-2 1-6 0-7l1-2c1-3 2-6 4-8l2-4v-1h1v-1h0c1-1 1-2 2-3z" class="Q"></path><path d="M368 541h1l3-3 1 1v1c0 1-1 2-1 3-1-1-2-1-4-2z" class="J"></path><path d="M383 500l3-3h4c-1 2-2 3-3 6s-5 7-5 11c0 1-1 2-1 3h-1-1c1-2 2-5 3-7 1-1 1-1 1-2s0-1 1-2l3-6-1-1c-1 1-3 4-4 5-1 3-2 4-4 5l2-4v-1h1v-1h0c1-1 1-2 2-3z" class="E"></path><defs><linearGradient id="e" x1="404.203" y1="576.782" x2="402.385" y2="560.978" xlink:href="#B"><stop offset="0" stop-color="#a7a6a6"></stop><stop offset="1" stop-color="#c5c4c0"></stop></linearGradient></defs><path fill="url(#e)" d="M390 536v1c1 1 1 2 1 3v1s1 0 1 1v1c-1 3 3 8 4 11l1-1c1 1 2 3 3 4 2 1 3 1 5 3l-1 2c1 1 3 3 5 4h0l4 2c0 1-1 1-1 2h-3 0c1 1 1 1 2 1 2 1 3 1 5 2h-1l-1 1 1 1c-1 1-1 1-3 2v1c1 1 1 0 1 1l-5-1-1-1-2-1-2-1c1-1 0-2-1-3l-1-1c-4-4-8-5-10-10l4 1c1 1 3 3 4 3h0c0-1 0 0-1-1s-2-3-3-4v-1l-6-9v-2c1-1 0-1 0-2 0-2-1-4-1-5v-1s1 0 1-1 1-2 1-3z"></path><path d="M402 572c3 1 6 2 8 4 0 1 1 2 2 2 1 1 1 0 1 1l-5-1-1-1-2-1-2-1c1-1 0-2-1-3z" class="B"></path><path d="M390 536v1c1 1 1 2 1 3v1s1 0 1 1v1c-1 3 3 8 4 11l1-1c1 1 2 3 3 4 2 1 3 1 5 3l-1 2c1 1 3 3 5 4h0l1 1c1 0 1 1 2 2l-1 1v-1c-4 0-9-3-11-6-1-1-3-3-4-3l-1-1-6-9v-2c1-1 0-1 0-2 0-2-1-4-1-5v-1s1 0 1-1 1-2 1-3z" class="F"></path><path d="M396 554l1-1c1 1 2 3 3 4 2 1 3 1 5 3l-1 2c-3-2-6-6-8-8z" class="L"></path><path d="M393 494h1l2 1c1 0 1-1 2-1h0v1l-3 4 2-1h4c-1 2-4 7-5 8l-2 4c-2 1-3 4-3 6h0l-1 2-1 6h0c-1 1-2 2-2 3-1 2-1 2 0 4v4c0 2 0 4 1 6 0 1 1 3 1 5-2 0-2-3-3-4h0v2c0 2 1 3 1 4l-1 1c0-2-1-4-2-6v-1c0-2 0-4-1-5-1-6-2-13 0-19 0-1 0-1-1-2l-1 2v5 1h-1v-5-1l1-1c0-1 1-2 1-3 0-4 4-8 5-11s2-4 3-6l2-1 1-2z" class="Q"></path><path d="M393 494h1l2 1c1 0 1-1 2-1h0v1l-3 4 2-1h4c-1 2-4 7-5 8h-1c-1 1-2 1-2 2h-1c-1 2-2 3-2 5-1 1-1 1-1 2h0c-1-1 0-2 1-3v-3h-1c0 2-1 6-3 6 0 2 0 3-1 4h-1c2-5 3-8 5-12 0-2 3-5 3-6v-2c0-1 2-3 2-4l-1-1z" class="B"></path><path d="M386 515c1-2 2-5 3-7 1-3 3-5 5-7h3c-1 2-3 4-4 6v1h-1c-1 2-2 3-2 5-1 1-1 1-1 2h0c-1-1 0-2 1-3v-3h-1c0 2-1 6-3 6z" class="l"></path><path d="M371 550l1 1 2-1c2 2 3 4 3 7 0 1 0 1-1 1 0 1-2 2-3 2 0 2 0 2 2 3h3c2 0 4 0 6 1v4l1 1 1 1-2 3h1c2 0 3-1 5 0l-7 3h-2-2c-1 0-2 1-2 1-2 0-4 0-6 1h-1s-1 0-1 1c-2 1-3 1-4 1h-2c-2 1-5 0-7 0h-2c-1 1-1 1-3 1l3-3c-1-1-1-2-1-3l-1-1h-1 0l-1-1c3 0 4 0 7-1h0v-11l-1-2h2c1-2 1-3 1-5v-2l-1-1h0 5 2c2 0 4-1 6-1z" class="b"></path><path d="M378 563c2 0 4 0 6 1v4l1 1c-2 2-4 4-6 5 1-1 1-2 2-3v-2h-2l-1 2h-2l1-2v-2h1v-1-3z" class="Z"></path><path d="M370 561c1 0 2 0 3-1 0 2 0 2 2 3h3v3 1h-1v2l-1 2h-3l-2 1v-1l1-3c-1-1-1-2-1-2-1-2-1-3-1-5z" class="m"></path><path d="M385 569l1 1-2 3h1c2 0 3-1 5 0l-7 3h-2-2c-1 0-2 1-2 1-2 0-4 0-6 1h-1s-1 0-1 1c-2 1-3 1-4 1h-2c-2 1-5 0-7 0h-2c-1 1-1 1-3 1l3-3c-1-1-1-2-1-3l-1-1h-1 0l-1-1c3 0 4 0 7-1 1 1 3 0 5 0h6 1c-2 1-3 1-4 2v1c2 0 3 0 5 2l9-3c2-1 4-3 6-5z" class="F"></path><path d="M368 572h1c-2 1-3 1-4 2v1c2 0 3 0 5 2l-16 1c-1-1-1-2-1-3l-1-1h-1 0l-1-1c3 0 4 0 7-1 1 1 3 0 5 0h6z" class="a"></path><path d="M371 550l1 1 2-1c2 2 3 4 3 7 0 1 0 1-1 1 0 1-2 2-3 2-1 1-2 1-3 1 0 2 0 3 1 5 0 0 0 1 1 2l-1 3v1h-2-1-6c-2 0-4 1-5 0h0v-11l-1-2h2c1-2 1-3 1-5v-2l-1-1h0 5 2c2 0 4-1 6-1z" class="M"></path><path d="M362 559h3c2 0 3 2 4 2v1l-1 1c2 2 0 4 2 6l-2 2v-2c-1-2-1-3-1-5v-2l-5-1v-2z" class="C"></path><path d="M371 550l1 1 2-1c2 2 3 4 3 7 0 1 0 1-1 1 0 1-2 2-3 2-1 1-2 1-3 1v-1l1-1-1-1c-1 1-2 1-3 1v-2h-3c0-2 0-2 1-3l-2-3h2c2 0 4-1 6-1z" class="P"></path><path d="M371 550l1 1h1c0 1 1 2 2 3 0 1 0 1-1 2-1 0-2 0-2-1l-2 1-1-1h-3 0l-1-1-2-3h2c2 0 4-1 6-1z" class="O"></path><path d="M357 561h1l1-2h3v2l5 1v2c0 2 0 3 1 5v2 1h-6c-2 0-4 1-5 0h0v-11z" class="D"></path><path d="M362 572c0-1 0-2 1-2l5 1v1h-6z" class="C"></path><path d="M394 510c1-1 2-1 3-1l4 4c3 2 5 4 7 6l2 3v1c2 3 1 7 1 9v2c1 1 1 2 1 3v1h0l-5 3c-4 4-8 7-12 9l-3-6v-1-1c0-1-1-1-1-1v-1c0-1 0-2-1-3v-1c0 1-1 2-1 3s-1 1-1 1v1c-1-2-1-4-1-6v-4c-1-2-1-2 0-4 0-1 1-2 2-3h0l1-6 1-2h0c0-2 1-5 3-6z" class="J"></path><path d="M397 536l2-1v2h-1c0 1-1 2-1 3h-3c0-1 1-2 1-3h1l1-1z" class="E"></path><path d="M395 526c1 0 2 1 3 2-1 3-1 6-1 8l-1 1h-1c0 1-1 2-1 3-1-1-2-2-4-3v-1c0-1 0-2 1-3v1l2-1c1 1 1 2 1 3l2-1v-3h1c-2-1-2-1-3-3l1-1v-2z" class="Q"></path><path d="M389 525c1 0 1-1 2-1h1 1l-1-1c-1-1-1-1-2-1l1-3c1-1 0-1 1-2 1 1 2 2 3 2 0 2 0 2 1 4h0-1l-1 1h0l-1 1s1 0 2 1v2l-1 1c1 2 1 2 3 3h-1v3l-1-2c-1-1-2-2-4-3h-1v-2c0-1-1-2-1-3z" class="d"></path><path d="M388 541c-1-2-1-4-1-6v-4c-1-2-1-2 0-4 0-1 1-2 2-3h0v1c0 1 1 2 1 3v2h1c2 1 3 2 4 3l1 2-2 1c0-1 0-2-1-3l-2 1v-1c-1 1-1 2-1 3s-1 2-1 3-1 1-1 1v1z" class="J"></path><path d="M405 521c1 1 1 1 2 3 0 1 0 2-1 3l1 1v2l1 1c0 1 0 2 1 3h-1c-2 2-1 4-1 7-4 4-8 7-12 9l-3-6h1c1 1 2 1 2 3h1v1l1-1v-1l1-2c1 1 1 1 2 1 1-1 2-2 2-3 1-2 2-4 2-5 0-2 0-5 1-7h0c0-1-1-2-2-3h0l1-1v2h1c0-2 0-3 1-5l-1-1v-1z" class="F"></path><path d="M394 510c1-1 2-1 3-1l4 4c3 2 5 4 7 6l2 3v1c2 3 1 7 1 9v2c1 1 1 2 1 3v1h0l-5 3c0-3-1-5 1-7h1c-1-1-1-2-1-3l-1-1v-2l-1-1c1-1 1-2 1-3-1-2-1-2-2-3v1l1 1c-1 2-1 3-1 5h-1v-2l-1-3-1 1h0-1l-1-1-1-1c-2-1-2-2-4-3-1 0-2-1-3-2-1 1 0 1-1 2l-1 3c1 0 1 0 2 1l1 1h-1-1c-1 0-1 1-2 1v-1l1-6 1-2h0c0-2 1-5 3-6z" class="H"></path><path d="M411 534c1 1 1 2 1 3v1h0l-1-1-2 1h-1l1-2v-2h2zm-3-15l2 3v1c-1 2-1 3-1 4-1-1-2-3-2-4l1-4z" class="C"></path><path d="M410 523c2 3 1 7 1 9-2-2-2-2-2-5 0-1 0-2 1-4z" class="X"></path><path d="M402 524c-1-3-3-5-5-7l-1-1c-1 0-2-1-3-2l1-1h1l1-1 1 1h1v1 1 1h1c2 0 5 4 6 5v1l1 1c-1 2-1 3-1 5h-1v-2l-1-3-1 1z" class="W"></path><path d="M402 604v-1l1-1c2 1 3 1 5 2h1v2l1 1h1 1c2 8 2 16 5 24h1v1h0l1 2h1v-4h4 0v1c1 1 1 1 1 2 1 0 2 0 2-1l-1-2 3-3v1c2 0 4-2 5-3 1 0 2 1 3 1 3-2 6-5 9-7h0l1-2v40h-1c2 2 1 5 1 7v15 19 5h0c-6-2-13-4-17-7-1-2-24-12-27-16l-2-1s0-1-1-2h-1c-1-1-3-3-3-4-1-2-3-4-5-5-3-1-7-4-9-6-5-7-14-12-18-19-3-2-5-6-7-8-1-2-4-3-5-5-2-3-3-6-5-8h-1v-2l-1-1-1-1-2 2-2-2 1-4-2-4h4 0c1 1 1 2 1 3 1 0 1-1 2-1v-1l-1-1c1-1 0-3 1-3s2 2 3 3l1-1-1-1 1-1c1 2 2 3 3 4l2 1 1 1 3 4 4 5 2-2h0c0-2 1-3 1-5 3 4 7 7 9 10l2-1 6-5 3-4h1v-1l3-2h2v-1c1 1 1 2 1 3h1 1c1 0 0-1 0-2v-5c1 0 1 0 2-1 0 0 0-1 1-1v-1h1l1 1c1 0 1 0 2-1z" class="n"></path><path d="M418 659c2 3 4 5 7 7 3 3 8 5 11 9-1 0-2-1-3-2l-3-2-2-1-3-3h-1-1 0c-3-3-4-4-5-8z" class="G"></path><path d="M441 670s1-1 1-2c1-1 0 1 1-1l3-1c0 4 0 9 1 13v19c-3-1-5-3-8-4-2-1-3-2-5-3v-1c2 0 5 3 6 3l3-1 1 1c1 1 1 0 2 0 0-3 1-9-1-11-1-3-5-4-8-6v-1l3 1c1 1 3 2 5 1v-1h-1l-1-1h2c0-1-1-1-1-2l-3-3z" class="T"></path><path d="M406 625l1-1c-1-1-1-2-1-3-1-2 0-3 0-4-1-1-1-2-1-3v-1-3c1 1 1 2 1 3 1 1 1 2 1 4v1c1 1 1 1 1 3v2 1h1c1 4 2 8 5 12 1 2 3 5 4 7 1 4 4 7 6 9 1 2 3 4 4 6l8 8h-2c-3-2-6-4-7-7-5-6-11-12-14-20l-5-11h0c0 1 1 4 2 6s2 5 3 7l-1 2c-3-5-5-12-6-18z" class="L"></path><path d="M424 652v-2c-1-1-1-1-1-2h1v1c1 2 2 2 4 2 2 3 6 7 9 8 1 0 2 0 3-1v2l-2 2 1 1 1-1 1-1 1-1c1-1 2-2 3-2l1-1c2 2 1 5 1 7v15c-1-4-1-9-1-13l-3 1c-1 2 0 0-1 1 0 1-1 2-1 2-3-1-5-2-7-4h2l-8-8c-1-2-3-4-4-6z" class="i"></path><path d="M443 661l3-2c1 1 1 5 0 7l-3 1c-1 2 0 0-1 1 0 1-1 2-1 2-3-1-5-2-7-4h2l2-2c2-1 4-2 5-3z" class="s"></path><path d="M443 661c0 2 1 3-1 4-1 0-3 0-4-1 2-1 4-2 5-3z" class="g"></path><path d="M431 677l4 2 1-2 3 2h1c2 1 4 2 5 3 2 2 1 8 1 11-1 0-1 1-2 0l-1-1-3 1c-1 0-4-3-6-3v1l-4-2-11-6h0c-1-2-2-2-2-3l8 3 1-1v-1c1 1 2 1 3 1h0l-1-3 1-1c1-1 0-1 1-1h1z" class="q"></path><path d="M436 677l3 2h1c2 1 4 2 5 3 2 2 1 8 1 11-1 0-1 1-2 0l-1-1c-1 0-2 0-3-1v-2l5 1v-1l-1-1 1-1c-2-3-9-4-12-7l12 5h0c-3-3-7-5-10-6l1-2z" class="K"></path><path d="M417 680l8 3 5 2h0l8 3c1 0 2 0 2 1v2c1 1 2 1 3 1l-3 1c-1 0-4-3-6-3v1l-4-2-11-6h0c-1-2-2-2-2-3z" class="Z"></path><path d="M430 685l8 3c1 0 2 0 2 1v2 1c-2-1-4-2-5-3-2-1-3-2-5-3v-1z" class="P"></path><path d="M402 604v-1l1-1c2 1 3 1 5 2h1v2l1 1h1 1c2 8 2 16 5 24 1 7 6 15 11 20-2 0-3 0-4-2v-1h-1c0 1 0 1 1 2v2c-2-2-5-5-6-9-1-2-3-5-4-7-3-4-4-8-5-12h-1v-1-2c0-2 0-2-1-3v-1c0-2 0-3-1-4 0-1 0-2-1-3v3 1c0 1 0 2 1 3 0 1-1 2 0 4 0 1 0 2 1 3l-1 1-3-17c0-2 0-3-1-4z" class="h"></path><path d="M402 604v-1l1-1c2 1 3 1 5 2h1v2l1 1h1v3c0 1-1 1-1 2 0 0 0 2 1 3h-1c0 2 0 2 1 4 0 1-1 3 0 4 0 2 1 5 1 7-1-1 0-2-1-3v-3-2c0-1 0 0-1-1v-4h0c-1-2-1-4-1-6 0-1-1-2-1-2-2 3 0 11 1 15h-1v-1-2c0-2 0-2-1-3v-1c0-2 0-3-1-4 0-1 0-2-1-3v3 1c0 1 0 2 1 3 0 1-1 2 0 4 0 1 0 2 1 3l-1 1-3-17c0-2 0-3-1-4z" class="p"></path><path d="M414 663c2 2 4 4 7 5h3l-1-1h1 1l3 3 2 1 3 2c0 2 1 2 2 3h1v1h0l-1 2-4-2h-1c-1 0 0 0-1 1l-1 1 1 3h0c-1 0-2 0-3-1v1l-1 1-8-3c0-1-2-2-3-2l-6-5c-1 0-3-1-3-2l-10-6h3c1 1 2 1 2 0 3 0 6 1 8 3l3 2h1c-1-2-2-2-2-4l6 3h4c-3-2-5-3-7-5l1-1z" class="W"></path><path d="M408 673h2c3 2 7 3 10 5 1-1 1 0 1-2h1v-1c1 2 2 3 4 3h1l1-1v1 1l1 3h0c-1 0-2 0-3-1v1l-1 1-8-3c0-1-2-2-3-2l-6-5z" class="D"></path><path d="M414 663c2 2 4 4 7 5h3l-1-1h1 1l3 3 2 1 3 2c0 2 1 2 2 3h1v1h0l-1 2-4-2h-1c-1 0 0 0-1 1l-1 1v-1-1l-1 1h-1c-2 0-3-1-4-3h-1-1c0-4-2-1-3-3l2-1-3-2h4c-3-2-5-3-7-5l1-1z" class="Z"></path><path d="M419 671l12 6h-1c-1 0 0 0-1 1l-1 1v-1-1l-1 1h-1c-2 0-3-1-4-3h-1-1c0-4-2-1-3-3l2-1z" class="V"></path><path d="M414 663c2 2 4 4 7 5h3l-1-1h1 1l3 3 2 1 3 2c0 2 1 2 2 3h1v1c-6-2-10-7-16-8-3-2-5-3-7-5l1-1z" class="H"></path><path d="M398 604h1l1 1c1 0 1 0 2-1 1 1 1 2 1 4l3 17c1 6 3 13 6 18 1 4 4 7 6 10s4 4 5 7c3 5 8 8 12 12-1 0-3-2-4-3l-4-3c-2-2-5-4-7-6-1-2-2-3-4-4 1 1 1 2 2 3 1 4 2 5 5 8h0l1 1h-3c-3-1-5-3-7-5-8-9-14-23-17-35l-1-7c0-2 0-4-1-7 1 0 0-1 0-2v-5c1 0 1 0 2-1 0 0 0-1 1-1v-1z" class="I"></path><path d="M395 607c1 0 1 0 2-1 0 0 0-1 1-1v-1 8h-3v-5z" class="P"></path><path d="M400 618v-7h1l2 10c-1-1-1-2-3-3z" class="d"></path><path d="M398 612c0-1 0-2 1-3v1l1-1h0l1-1v3h0-1v7l-2 1v-7z" class="J"></path><path d="M395 612h3 0v7l1 6c-1-1-2-2-3-4 0-2 0-4-1-7 1 0 0-1 0-2z" class="R"></path><path d="M400 618c2 1 2 2 3 3 1 6 3 12 5 17 1 4 2 7 4 10 1 2 3 4 4 6l-3-3h0c-5-4-6-9-9-14h0l-1 1-1-2-3-11-1-6 2-1z" class="Q"></path><path d="M396 621c1 2 2 3 3 4l3 11 1 2 1-1h0c3 5 4 10 9 14h0l1 3 2 2c1 1 1 2 2 3 1 4 2 5 5 8h0l1 1h-3c-3-1-5-3-7-5-8-9-14-23-17-35l-1-7z" class="S"></path><path d="M414 654l2 2c1 1 1 2 2 3 1 4 2 5 5 8h0l1 1h-3c-4-5-7-8-11-13h3 0l1-1z" class="F"></path><path d="M402 636l1 2 1-1h0c3 5 4 10 9 14h0l1 3-1 1h0-3c-3-6-6-13-8-19z" class="W"></path><path d="M446 619h0l1-2v40h-1l-1 1c-1 0-2 1-3 2l-1 1-1 1-1 1-1-1 2-2v-2c-1 1-2 1-3 1-3-1-7-5-9-8-5-5-10-13-11-20h1v1h0l1 2h1v-4h4 0v1c1 1 1 1 1 2 1 0 2 0 2-1l-1-2 3-3v1c2 0 4-2 5-3 1 0 2 1 3 1 3-2 6-5 9-7z" class="s"></path><path d="M437 649h3l1-1-1-1c0-1 0-2 1-3 0 2 1 3 1 5l1 1c-2 2-3 4-5 7l-1 1-2-2 2-1c-1-1-1-2-1-3h1l1-1-1-2z" class="m"></path><path d="M446 619h0l1-2v40h-1l-1 1c-1 0-2 1-3 2l-1 1-1 1-1 1-1-1 2-2v-2h0l6-6c1-1 1-3 0-4l-1-1c0-1 1 0 1-1l1-16c0-2 0-5-1-8v-3z" class="M"></path><path d="M431 650c1-2 7-14 9-15v2c0 3 0 5 1 7-1 1-1 2-1 3l1 1-1 1h-3c-1 1-2 1-2 2l-1 1c-2 0-2-1-3-1v-1z" class="j"></path><path d="M437 643h2v1c0 2 1 2 0 4h-3l-1-1c1 0 1 0 1-1h1l-1-1 1-2z" class="k"></path><path d="M437 626c3-2 6-5 9-7v3l-10 10h0c1-1 2-1 3-1h0c-2 2-4 3-6 6-1 1-3 3-3 4l1 1h-1v2l-1 2h1c0 1 0 2-1 3l1 2 1-1v1c1 0 1 1 3 1l1-1c0-1 1-1 2-2l1 2-1 1h-1c0 1 0 2 1 3l-2 1 2 2v1c-3-1-7-5-9-8-5-5-10-13-11-20h1v1h0l1 2h1v-4h4 0v1c1 1 1 1 1 2 1 0 2 0 2-1l-1-2 3-3v1c2 0 4-2 5-3 1 0 2 1 3 1z" class="T"></path><path d="M435 656h-1c-2-2-5-5-6-7h1l1 2 1-1v1c1 0 1 1 3 1l1-1c0-1 1-1 2-2l1 2-1 1h-1c0 1 0 2 1 3l-2 1z" class="L"></path><path d="M437 626c3-2 6-5 9-7v3l-10 10c-2 1-9 7-10 8l-1 2v-1h-1-1c0-1-1-1-1-2 1-1 1-2 2-3l6-5h-1c-1-1 0-2 0-3 2 0 4-2 5-3 1 0 2 1 3 1z" class="D"></path><path d="M434 625c1 0 2 1 3 1l-7 5h-1c-1-1 0-2 0-3 2 0 4-2 5-3z" class="R"></path><path d="M387 614l3-2h2v-1c1 1 1 2 1 3h1 1c1 3 1 5 1 7l1 7c3 12 9 26 17 35l-1 1c2 2 4 3 7 5h-4l-6-3c0-1-1-1-2-1-8-6-16-11-23-18-2-1-13-15-15-17-3-2-5-5-7-8l2-2h0c0-2 1-3 1-5 3 4 7 7 9 10l2-1 6-5 3-4h1v-1z" class="c"></path><path d="M385 647l1-1c1 1 2 3 4 4h0c-1-1-3-3-3-4v-1c8 7 16 14 26 19 2 2 4 3 7 5h-4l-6-3c0-1-1-1-2-1-8-6-16-11-23-18z" class="R"></path><path d="M366 615c3 4 7 7 9 10l2 3h-3l-1 1c4 6 9 11 14 16v1c0 1 2 3 3 4h0c-2-1-3-3-4-4l-1 1c-2-1-13-15-15-17-3-2-5-5-7-8l2-2h0c0-2 1-3 1-5z" class="L"></path><path d="M387 614l3-2h2v-1c1 1 1 2 1 3h1 1c1 3 1 5 1 7l1 7h0c-1-2-2-3-2-4-1-1-1-2-1-3-1 2-1 2-1 4v1c-1 0-1 0-2-1v-1l-3 1h-2c0 1-2 3-2 3-2 1-3-1-5 1l-2-1-2-3 2-1 6-5 3-4h1v-1z" class="Y"></path><path d="M386 615h1v4l1 1h-1v2l-2 2-1-1c-2 2-4 5-7 5l-2-3 2-1 6-5 3-4z" class="Z"></path><path d="M377 624h2c1-1 2-1 3-3 1 1 1 0 2 1v1c-2 2-4 5-7 5l-2-3 2-1z" class="U"></path><path d="M345 610c1-1 0-3 1-3s2 2 3 3l1-1-1-1 1-1c1 2 2 3 3 4l2 1 1 1 3 4 4 5c2 3 4 6 7 8 2 2 13 16 15 17 7 7 15 12 23 18 1 0 2 0 2 1 0 2 1 2 2 4h-1l-3-2c-2-2-5-3-8-3 0 1-1 1-2 0h-3l10 6c0 1 2 2 3 2l6 5c1 0 3 1 3 2s1 1 2 3h0l11 6 4 2c2 1 3 2 5 3 3 1 5 3 8 4v5h0c-6-2-13-4-17-7-1-2-24-12-27-16l-2-1s0-1-1-2h-1c-1-1-3-3-3-4-1-2-3-4-5-5-3-1-7-4-9-6-5-7-14-12-18-19-3-2-5-6-7-8-1-2-4-3-5-5-2-3-3-6-5-8h-1v-2l-1-1-1-1-2 2-2-2 1-4-2-4h4 0c1 1 1 2 1 3 1 0 1-1 2-1v-1l-1-1z" class="X"></path><path d="M378 652l-1 1-3-3c-2-1-4-2-5-4-1-1-2-2-3-4l1-1 11 11z" class="H"></path><path d="M339 610h4 0c1 1 1 2 1 3 1 0 1-1 2-1v-1l2 3c1 2 3 4 4 6l7 9-1 1-1-1c-2-3-4-6-7-9 1 3 1 5 2 7l1 1c1 3 4 5 6 8l5 6v1c-3-2-5-6-7-8-1-2-4-3-5-5-2-3-3-6-5-8h-1v-2l-1-1-1-1-2 2-2-2 1-4-2-4z" class="e"></path><path d="M339 610h4 0c1 1 1 2 1 3 1 0 1-1 2-1v3l-1-1v1l1 2h0-1v1 1l-1-1-2 2-2-2 1-4-2-4z" class="h"></path><path d="M341 614l3 4-2 2-2-2 1-4z" class="H"></path><path d="M359 629c6 9 13 16 21 23 4 4 7 7 11 9l4 4 10 6c0 1 2 2 3 2l6 5c1 0 3 1 3 2s1 1 2 3h0-1l-26-17c-3-5-9-10-14-14l-11-11c0-1-2-2-3-3l-6-8 1-1z" class="R"></path><defs><linearGradient id="f" x1="421.649" y1="677.161" x2="404.555" y2="685.268" xlink:href="#B"><stop offset="0" stop-color="#8d8c8b"></stop><stop offset="1" stop-color="#b9b9b6"></stop></linearGradient></defs><path fill="url(#f)" d="M391 668a30.44 30.44 0 0 1-8-8c1 0 1 1 1 1 3 2 5 4 8 5h0l26 17h1l11 6 4 2c2 1 3 2 5 3 3 1 5 3 8 4v5h0c-6-2-13-4-17-7-1-2-24-12-27-16l-2-1s0-1-1-2h-1c-1-1-3-3-3-4-1-2-3-4-5-5z"></path><path d="M403 680c1 0 2 1 3 1l19 10 1 1 2-2h0l2-1 4 2c2 1 3 2 5 3 3 1 5 3 8 4v5h0c-6-2-13-4-17-7-1-2-24-12-27-16z" class="k"></path><path d="M426 692l2-2h0l2-1 4 2v1c1 1 3 2 4 3s3 2 3 3c-3 0-6-2-9-4l-4-1-2-1z" class="e"></path><path d="M426 692l2-2h0l1 1s2 1 3 1v1 1l-4-1-2-1z" class="q"></path><path d="M345 610c1-1 0-3 1-3s2 2 3 3l1-1-1-1 1-1c1 2 2 3 3 4l2 1 1 1 3 4 4 5c2 3 4 6 7 8 2 2 13 16 15 17 7 7 15 12 23 18 1 0 2 0 2 1 0 2 1 2 2 4h-1l-3-2c-2-2-5-3-8-3 0 1-1 1-2 0h-3l-4-4c-4-2-7-5-11-9-8-7-15-14-21-23l-7-9c-1-2-3-4-4-6l-2-3-1-1z" class="G"></path><path d="M345 610c1-1 0-3 1-3s2 2 3 3l1-1-1-1 1-1c1 2 2 3 3 4l2 1 1 1 3 4 4 5c2 3 4 6 7 8-2 0-3 0-4-2-2-2-4-6-7-7 0 2 0 3 2 5 0 4 4 5 5 8 2 5 8 9 12 14 1 1 2 2 2 4-8-7-15-14-21-23l-7-9c-1-2-3-4-4-6l-2-3-1-1z" class="J"></path><path d="M345 610c1-1 0-3 1-3s2 2 3 3l1-1-1-1 1-1c1 2 2 3 3 4l2 1 1 1 3 4h-1c-2-1-4-2-5-4l-1 1 2 2v1l-2-2-1 1 1 4c-1-2-3-4-4-6l-2-3-1-1z" class="B"></path><path d="M419 304c5-4 10-8 16-10 11-6 24-8 36-8 0 1 1 2 1 3l-1 1h5 0c-1 1-2 2-4 2h-1 0l-1 1h5l-1 1c-8 0-14 1-22 4l-6 2-3 1h-5c-2 1-4 1-5 3l2 1c-1 0-3 0-4 1h1c-2 1-2 0-4 1l-1 1v1h3c-3 2-7 4-10 6s-5 4-8 6c-7 5-14 10-21 14-2 0-3 1-4 1-3 1-7 1-9 3v1c2 1 4 1 6 2 4 0 8 1 11 2 8 2 14 5 21 9l-1 2c2 2 5 4 8 6 4 3 8 6 10 10h1v-1c1 1 1 1 3 2h1c4 6 7 12 9 18 2 3 2 7 3 10l-6 2c2 6 2 12 2 17v1c0 6 1 13-1 18v1h0c0-2-1-2-2-3v-2h-1-1l-2-2c0 3 5 7 3 11 0 1 0 1-1 2 0 0 0 1-1 2l1 1-1 1c0 3 0 3 2 5s2 5 1 7v1 5c1-1 1-2 1-4h0 1c1 1 1 2 1 3v33c0 5 1 10 0 15h0v12c0 2 1 4 0 6h-23c-2 0-3 0-5 1 0 0-1-1-2-1 0-1-1-2-1-3v-5c-1 2 0 3-1 4l-1-2h0v-5-1l-1 1-1 1h-1l-2-3c-2-2-4-4-7-6l-4-4c-1 0-2 0-3 1l2-4c1-1 4-6 5-8h-4l-2 1 3-4v-1h0c-1 0-1 1-2 1l-2-1h-1l-1 2-2 1h-4l3-3c-2-3-2-8-4-12v-3c0-3-1-9-1-12 0-2-1-4-1-7 0-5 1-11 2-16 0-2 1-3 2-5 0-1 1-2 1-3l3-8c-1 0-2-1-3-1h0-7-1c-2 1-5 0-7 0h-11-16-7l1-1h-5l-2-2h0l1-1c0-2 0-2-1-3-2-1-4-1-6-1h-8c-4 1-8 1-11 0h-2-6c0-2-3-3-4-5v-1l-4-4 1-1h1c0-2-1-2-2-3v-1h0 2 1l-1-5h1 1v2l1 1 1-1c-1-5 0-8 1-13v-1l1-2h1v1c1-1 1-1 1-2h2c1-1 1-1 3-2-1 0-1 0 0-1-1-1-1-2-1-2l-1-1-1-1h1-2l-1-2 1-1c1-2 2-2 2-5 1-3 3-5 4-7 3-4 7-9 12-12l15-12c1-1 2-2 3-2 3-3 5-5 7-8h-2l-3-1c-6-1-11-6-14-11 1-2 2-4 3-5l2 1c0-1 0-1 1-2l1 2c1 0 2 0 2 1l1-1h1c1 0 0 1 2 1v-1c2 0 3-1 5-1l3-6c0-2 1-3 2-4 1 0 2 0 3 1 1 0 1 1 2 2h2c-1 0-2 1-2 2 0 0 1 0 0 1v1c0 1-1 1-1 2l1 1-2 3c0 2-2 3-3 5-1 1-2 3-2 4l1 1c1 1 1 2 2 3l1-1h0c4 2 8 3 12 4h1c0-1 0-1 1-1 3 1 5 0 8-1l8-2 7-1c5-2 11-6 15-10l10-9c1 0 2 0 3-1 1 0 1-1 2-1z" class="s"></path><path d="M381 364h0c1-1 2-1 3-2v1c-1 0-2 1-3 2l-1 1 2 2v-1h2c0-1 1-1 2-2 0 0 1 0 2 1-2 2-5 4-7 5-2 0-4 0-5-1l2-2 2-2h-2c1-2 1-2 3-2zm22 29l4 1h0c1 1 2 1 3 1h0l-1 2c-1 2-2 2-3 3h-2-2v-2-1c0-2 0-3 1-4z" class="O"></path><path d="M403 393l4 1c-1 1-2 1-3 1 0 1 0 1 1 2l-1 3h-2v-2-1c0-2 0-3 1-4z" class="a"></path><path d="M408 403c4-2 6-6 11-8v1c1-1 1-1 1-2h0l6-3-10 9c0 1-3 4-4 4h-3-1v-1z" class="S"></path><path d="M406 465c1 0 0 0 1-1-1-1-1-2-2-4 0 0-1-1-1-2v-2-2c-1-1-1-1-1-2h1v-2-4-1c1-1 1-3 2-5h0c2-3 2-5 5-7l-6 12c-1 6-1 12 2 18l3 4v3c-1 0-1 1-2 1h-1c-1 0-1-1-1-2h0l-1-1 1-3z" class="O"></path><path d="M345 311l3-6c0-2 1-3 2-4 1 0 2 0 3 1 1 0 1 1 2 2h2c-1 0-2 1-2 2 0 0 1 0 0 1v1c0 1-1 1-1 2l1 1-2 3c-1 0-2-1-3-2h0c0-2 1-2 3-3h0c0-1 0-1 1-2h0v-1h-2c-1 1-2 0-3 0-1 1-1 1-1 2h4l-1 1h-3l-2 2h-1zm20 40l-1-1-2 1-1-1v-1l3-3h1 2v1 1l1 2h0c2-1 4-1 5 0s1 1 0 3h1l3-2 1 2h1 0l1 2-2 1v2l-3 2h0v-1l1-1h-1l2-3v-1c-2 1-3 3-4 4-1-2-1-2 0-4l-1-1c-1 0-1 0-2-1-1 0 0 0-1-1l1-1c-2 0-3 1-5 1z" class="g"></path><defs><linearGradient id="g" x1="450.795" y1="299.413" x2="457.205" y2="287.587" xlink:href="#B"><stop offset="0" stop-color="#757273"></stop><stop offset="1" stop-color="#a0a2a1"></stop></linearGradient></defs><path fill="url(#g)" d="M438 301c0-1 1-2 1-2l2-1c5-5 14-7 21-8 2 0 5 0 7-1 1 0 1 0 2 1h-1-1c0 1-1 1-1 1-2 0-3 1-4 1-2 1-5 1-6 2-3 1-6 1-9 2-2 0-4 1-5 2l-1 1c1 0 2 0 3 1l-3 1h-5z"></path><path d="M471 290h5 0c-1 1-2 2-4 2h-1 0l-1 1h5l-1 1c-8 0-14 1-22 4l-6 2c-1-1-2-1-3-1l1-1c1-1 3-2 5-2 3-1 6-1 9-2 1-1 4-1 6-2 1 0 2-1 4-1 0 0 1 0 1-1h1 1z" class="D"></path><path d="M389 368l1-1h1c1-1 2-2 2-3l2-2v1c-1 2-2 3-3 5 0 1 0 1-1 1-1 2-1 3-2 4l-15 17h-1c0-1 0 0-1-1h-1 0l-2 2h0l-1 1h-1-2-5l1-1h2 1 2c3-2 6-5 9-7 2-2 6-4 7-7l6-6c0-1 0-1 1-2v-1z" class="T"></path><path d="M373 358c1-1 2-3 4-4v1l-2 3h1l-1 1v1h0l3-2v3c1-1 2-1 3-2v-1l1-1 1 1c-1 1-1 2-2 3l-1 1h2c0 1-1 1-1 2-2 0-2 0-3 2h2l-2 2h-2v-1c-1 0-5 4-6 5-1 0-1 0-1-1l1-1-1-1-1 1h0c0-3 1-4 2-5h0l3-3c0-1 0-1 1-2l-2-1 1-1z" class="U"></path><path d="M402 456c0-6 1-12 3-17 0 1 0 2-1 4v2h0c-1 2 0 3 0 4v1c-1 0-1 1-1 2v2c-1 4-1 10 0 14v1c0-2 0-4 1-6l1 2h0v5-2c0-1 1-2 1-3l-1 3 1 1h0c0 1 0 2 1 2h1c1 0 1-1 2-1h0c1 1 2 2 2 3h2c1 1 1 2 1 3l-9 4h-1c-3-8-4-16-3-24z" class="f"></path><path d="M410 470h0c1 1 2 2 2 3h2v2l-2-1-1 1h-1c-1-1-2-2-2-4 1 0 1-1 2-1z" class="p"></path><path d="M428 426v-1c0-1 0 0-1-1v-2l-1-1v-2c0-1-1-1-1-1v-1l1-1c0 1 1 1 1 2 1 1 2 1 3 2l1 1c0 2-1 2 1 3 1 0 2-1 3-2 0-2 0-2 2-3 1-1 1-1 3-1 1 1 1 3 1 4l-2 4c1 0 1 1 2 1v1c1 2 2 4 2 6h-1-1l-2-2-1-2-1 1c-2-1-5-1-7-3 2 0 3 0 4-1-2-1-4-1-6-1z" class="g"></path><path d="M435 427l1-1h1c0-1 0-1 1-1v2l1 1h2c1 2 2 4 2 6h-1-1l-2-2-1-2-1 1c-2-1-5-1-7-3 2 0 3 0 4-1h1z" class="a"></path><path d="M434 427h1c3 2 6 4 7 7h-1l-2-2-1-2-1 1c-2-1-5-1-7-3 2 0 3 0 4-1z" class="G"></path><defs><linearGradient id="h" x1="372.587" y1="387.614" x2="363.491" y2="383.705" xlink:href="#B"><stop offset="0" stop-color="#6a6c6d"></stop><stop offset="1" stop-color="#959291"></stop></linearGradient></defs><path fill="url(#h)" d="M388 366h0l-3 4h1c1 0 2-2 3-2v1c-1 1-1 1-1 2l-6 6c-1 3-5 5-7 7-3 2-6 5-9 7h-2-1-2l-1 1-2 1v-2c1-2 4-2 5-4l11-7-2-3 7-4h0l2-2c2-1 5-3 7-5z"></path><path d="M388 366h0l-3 4c-1 0-2 1-3 2l1 1 1-1h0c-1 2-3 3-4 5-1 1-2 1-3 3v-1c-1 0-1 0-2 1l-1-1v1l-2-3 7-4h0l2-2c2-1 5-3 7-5z" class="k"></path><path d="M379 373v1c-1 2-3 4-5 5v1l-2-3 7-4z" class="C"></path><path d="M428 426c2 0 4 0 6 1-1 1-2 1-4 1 2 2 5 2 7 3l1-1 1 2c0 3 5 7 3 11h-1v-1-1c-1-1 0-2-1-3h-1v1c0 1-1 2-2 3 0-2 0-2 1-3v-2c-1-2-4-4-6-5-6-1-11 1-16 4-3 2-6 6-7 10l-1-3v1h-1c0-1-1 1-2 1l6-12c6-4 10-6 17-7z" class="F"></path><path d="M432 432v-1h-3-1c-4-1-10 1-13 4-2 2-3 4-5 5 2-3 4-6 7-7 1-1 2-1 3-1v-2c3-1 7-1 10-2 2 2 5 2 7 3l1-1 1 2c0 3 5 7 3 11h-1v-1-1c-1-1 0-2-1-3h-1v1c0 1-1 2-2 3 0-2 0-2 1-3v-2c-1-2-4-4-6-5z" class="h"></path><path d="M418 364l-9-6c-5-3-10-5-15-7-4-1-8-2-11-3-2 0-5 0-6-1v-2-1l2-1c1 1 1 1 2 1 7 2 15 3 21 6l13 5c2 2 5 4 8 6-1 2-1 2-2 3h-1-2z" class="S"></path><path d="M370 372c1-1 5-5 6-5v1h2l-2 2c1 1 3 1 5 1l-2 2h0l-7 4 2 3-11 7c-1 2-4 2-5 4h-1-4-2c3-3 5-6 7-9l2-2c1-3 5-9 8-10h0l1-1 1 1-1 1c0 1 0 1 1 1z" class="L"></path><path d="M368 370h0l1-1 1 1-1 1c0 1 0 1 1 1-2 4-7 6-9 9h-1v-1c1-3 5-9 8-10z" class="R"></path><path d="M376 370c1 1 3 1 5 1l-2 2h0l-7 4-9 6c0-2 3-2 4-4 2-4 6-6 9-9z" class="a"></path><path d="M363 383l9-6 2 3-11 7c-1 2-4 2-5 4h-1-4l10-8z" class="I"></path><path d="M414 306c1 0 2 0 3-1 1 0 1-1 2-1-12 12-27 25-44 29-3 1-6 2-9 2-6-1-14-4-18-8 0 0-1-1-1-2 0 0 0-1 1-2l1 1c1 1 1 2 2 3l1-1h0c4 2 8 3 12 4h1c0-1 0-1 1-1 3 1 5 0 8-1l8-2 7-1c5-2 11-6 15-10l10-9z" class="X"></path><path d="M382 326l7-1c-6 4-13 6-19 7h-6 0c-2-1-4-1-6-1-2-1-3-1-5-2-1-1-2-1-2-2l1-1h0c4 2 8 3 12 4h1c0-1 0-1 1-1 3 1 5 0 8-1l8-2z" class="F"></path><path d="M423 361c4 3 8 6 10 10h1v-1c1 1 1 1 3 2h1c4 6 7 12 9 18 2 3 2 7 3 10l-6 2c-1-4-2-8-4-12-5-11-13-19-22-26h2 1c1-1 1-1 2-3z" class="L"></path><path d="M430 397l6-2h1 0l-2 1v2h-1c-1 1-2 2-4 3l-1 1c-1 0-1 1-2 2 1-1 3-2 4-2s2-1 3-1c1-1 2-2 3-2 0 1-2 2-4 3l-12 9h0c-7 7-12 13-15 23 0 1-1 3-1 5-2 5-3 11-3 17l-1-1c-1-2-1-5 0-7 0-1 0-2-1-2 0 2-1 5-1 7-1 2 0 6-1 8v-9c-1-3-1-8 0-11l1-3 1-4c1-2 1-4 3-6h2l4-7-1-1v-1c-2 1-3 2-4 3v1h-1v-1c-2 2-2 3-4 4l2-4-2-1 1-2v-1-1h-2v-1c1-1 2-3 2-4l-2-1v-1-1c1 0 1-1 2-2h-1c0-1 0-2 1-2h1l1 1 2-2v-1-1h2l2 1v1h1 3c1 0 4-3 4-4 1 1 2 2 3 2l3-2c3-1 5-2 8-3z" class="Z"></path><path d="M422 400l8-3v2h-3l-1 1v2s-1 0-2 1l-2-3z" class="S"></path><path d="M400 434c1-2 1-4 3-6h2c-1 1-3 8-4 8h-1v1l-1 1 1-4z" class="F"></path><path d="M399 438l1-1v-1h1l-3 16c-1-3-1-8 0-11l1-3z" class="W"></path><path d="M400 446c2-8 5-16 9-23l2-2c2-4 6-7 10-11v1c-7 7-12 13-15 23 0 1-1 3-1 5-2 5-3 11-3 17l-1-1c-1-2-1-5 0-7 0-1 0-2-1-2z" class="H"></path><defs><linearGradient id="i" x1="418.513" y1="399.93" x2="415.43" y2="411.42" xlink:href="#B"><stop offset="0" stop-color="#5c5c5b"></stop><stop offset="1" stop-color="#737374"></stop></linearGradient></defs><path fill="url(#i)" d="M422 400l2 3h0c1 1 1 1 1 2l-5 4c-1 1-3 2-4 4v1c-3 2-5 4-7 7l-1-1v-1c-2 1-3 2-4 3v1h-1v-1c-2 2-2 3-4 4l2-4 1-1c4-7 11-13 17-19l3-2z"></path><path d="M416 414v-2l1-1-1-1h0c0-1 1-2 2-3v-1h1l1 3c-1 1-3 2-4 4v1z" class="k"></path><path d="M419 406l5-3c1 1 1 1 1 2l-5 4-1-3z" class="N"></path><path d="M402 421h1c1 0 1-1 1-1 1-1 1-1 1-2 2-2 5-4 8-5h0c-2 2-3 4-5 6-2 1-3 2-4 3v1h-1v-1c-2 2-2 3-4 4l2-4 1-1z" class="H"></path><defs><linearGradient id="j" x1="411.439" y1="402.048" x2="411.555" y2="411.941" xlink:href="#B"><stop offset="0" stop-color="#191818"></stop><stop offset="1" stop-color="#303332"></stop></linearGradient></defs><path fill="url(#j)" d="M412 404c1 0 4-3 4-4 1 1 2 2 3 2-6 6-13 12-17 19l-1 1-2-1 1-2v-1-1h-2v-1c1-1 2-3 2-4l-2-1v-1-1c1 0 1-1 2-2h-1c0-1 0-2 1-2h1l1 1 2-2v-1-1h2l2 1v1h1 3z"></path><path d="M408 404h1 3l-4 5-2-3 2-2z" class="e"></path><path d="M406 406l2 3-3 4c-1-2-2-2-3-3l4-4z" class="X"></path><path d="M402 410c1 1 2 1 3 3l-5 6v-1-1h-2v-1c1-1 2-3 2-4l2-2z" class="V"></path><path d="M404 402h2l2 1v1l-2 2-4 4-2 2-2-1v-1-1c1 0 1-1 2-2h-1c0-1 0-2 1-2h1l1 1 2-2v-1-1z" class="P"></path><path d="M398 417h2v1 1l-1 2 2 1-2 4c2-1 2-2 4-4v1h1v-1c1-1 2-2 4-3v1l1 1-4 7h-2c-2 2-2 4-3 6l-1 4-1 3c-1 3-1 8 0 11v9c-1 4 0 9 1 13v3c1 1 0 3 1 5h0c0 1 0 2 1 3h0-2l-1-2c0 1-1 1-1 2v1c1 3 2 7 3 10h-1l-2 2-2 1 3-4v-1h0c-1 0-1 1-2 1l-2-1h-1l-1 2-2 1h-4l3-3c-2-3-2-8-4-12v-3c0-3-1-9-1-12 0-2-1-4-1-7 0-5 1-11 2-16 0-2 1-3 2-5 0-1 1-2 1-3l3-8 1-2 6-9z" class="F"></path><defs><linearGradient id="k" x1="387.445" y1="440.09" x2="400.18" y2="429.229" xlink:href="#B"><stop offset="0" stop-color="#262324"></stop><stop offset="1" stop-color="#303530"></stop></linearGradient></defs><path fill="url(#k)" d="M399 421l2 1-2 4c-3 6-6 12-7 19-1-1-2-1-3-2 2-8 6-15 10-22z"></path><path d="M388 475c-3-9-1-23 1-32 1 1 2 1 3 2-1 4-1 8-1 12v-1c-1-2-1-2 0-4l-1-1v1 3c-1 2-1 4-2 6-1 3 0 7 0 10v4z" class="Z"></path><path d="M388 475v-4c0-3-1-7 0-10 1-2 1-4 2-6v-3-1l1 1c-1 2-1 2 0 4v1 5c1 8 2 18 6 24 1 3 2 7 3 10h-1l-2 2-2 1 3-4v-1h0c-1 0-1 1-2 1l-2-1h-1l-1 2c-1-3-1-1-3-3l1-1h1c1-3-1-6-2-9l1-1v-1h-1c0-1 1-1 1-2l-1-1c-1-1-1-2-1-3z" class="g"></path><path d="M391 492h0c2 0 3-1 4-2l1-1c1 2 2 3 2 5h0c-1 0-1 1-2 1l-2-1h-1l-1 2c-1-3-1-1-3-3l1-1h1z" class="D"></path><path d="M399 426c2-1 2-2 4-4v1h1v-1c1-1 2-2 4-3v1l1 1-4 7h-2c-2 2-2 4-3 6l-1 4-1 3c-1 3-1 8 0 11v9c-1 4 0 9 1 13v3c1 1 0 3 1 5h0c0 1 0 2 1 3h0-2l-1-2c0 1-1 1-1 2v1c-4-6-5-16-6-24v-5c0-4 0-8 1-12 1-7 4-13 7-19z" class="Q"></path><path d="M393 452c0 6 0 13 2 19 1 4 3 7 3 12 0 1-1 1-1 2v1c-4-6-5-16-6-24l1-7 1 2v-5z" class="F"></path><path d="M399 426c2-1 2-2 4-4v1h1v-1c1-1 2-2 4-3v1l1 1-4 7h-2c-2 2-2 4-3 6l-2-1c-1 2-2 5-3 8s-2 7-2 11v5l-1-2-1 7v-5c0-4 0-8 1-12 1-7 4-13 7-19z" class="I"></path><path d="M398 433c1-2 2-4 3-5s1-2 2-3h1l-1 3c-2 2-2 4-3 6l-2-1z" class="W"></path><path d="M404 425l1-1h0c1-2 2-3 3-4l1 1-4 7h-2l1-3z" class="V"></path><path d="M409 446c1-4 4-8 7-10 5-3 10-5 16-4 2 1 5 3 6 5v2c-1 1-1 1-1 3 1-1 2-2 2-3v-1h1c1 1 0 2 1 3v1 1h1c0 1 0 1-1 2 0 0 0 1-1 2l1 1-1 1c0 3 0 3 2 5s2 5 1 7v1 5c-2 5-5 9-9 10l-4 2-7-1h-2c-1-1-1-1-2 0h-1l-2 1c-1 0-2 1-2 2h-3l-1 1h-1-2l-1-2 9-4c0-1 0-2-1-3h-2c0-1-1-2-2-3h0v-3l-3-4c-3-6-3-12-2-18 1 0 2-2 2-1h1v-1l1 3z" class="K"></path><path d="M413 458c-1-3-2-5-1-8s4-10 8-12c2 0 3-1 4-1 3-2 4-1 7-1v1 2h-1c-1 1-1 2-3 2-3 0-4 2-7 4-1 0-1 1-1 2h-1v-2c-3 3 0 6-4 8l1 1h1 0l-1 1-1 1h0l-1 1v1h0z" class="L"></path><path d="M413 458v-1l1-1h0l1-1 1-1h0-1l-1-1c4-2 1-5 4-8v2h1c0-1 0-2 1-2 0 3 0 5 1 8l-1 1v1h1v2h0v4h1 0c1 5 2 7 6 10-1 1-1 1-2 1l-4-4c0-1 0-2-1-2 0 1 1 2 1 3h0l-2-2-1 1c-1-1-2-3-3-4-1-2-2-3-3-6z" class="k"></path><path d="M405 445c1 0 2-2 2-1h1v-1l1 3c-2 4-2 8 0 12h4 0c1 3 2 4 3 6 1 1 2 3 3 4l3 3c-1 0-2-1-4-1h-2-3c-1 0-2-2-3-3l-3-4c-3-6-3-12-2-18z" class="E"></path><path d="M413 458h0c1 3 2 4 3 6 1 1 2 3 3 4l3 3c-1 0-2-1-4-1-5-3-7-6-9-12h4z" class="a"></path><path d="M431 436l7 3c-1 1-1 1-1 3l-2 3-2 3-5 4c-3 2-5 5-6 9h0-1v-4h0v-2h-1v-1l1-1c-1-3-1-5-1-8 3-2 4-4 7-4 2 0 2-1 3-2h1v-2-1z" class="g"></path><path d="M431 436l7 3c-1 1-1 1-1 3l-2 3c-1-1-2-2-3-2v-1h0c0-1-1-2-2-3h1v-2-1z" class="j"></path><g class="P"><path d="M421 453h3c2-1 5-3 6-5h0 3l-5 4c-3 2-5 5-6 9h0-1v-4h0v-2h-1v-1l1-1z"></path><path d="M437 442c1-1 2-2 2-3v-1h1c1 1 0 2 1 3v1 1h1c0 1 0 1-1 2 0 0 0 1-1 2l1 1-1 1c-1 1-2 2-4 3-1 0-2 1-3 2v1l-1 2h0l1 2h1c1 0 2 2 1 3v2h0l-1 3 1 2c-2 1-2 1-4 1-1 1-2 1-3 1-4-3-5-5-6-10 1-4 3-7 6-9l5-4 2-3 2-3z"></path></g><path d="M428 469l1-1 1-1h4l1 2c-2 1-2 1-4 1l-3-1z" class="Z"></path><path d="M432 457l1 2h1c1 0 2 2 1 3v2l-8-2c1-2 3-4 5-5z" class="g"></path><path d="M428 452h4c-3 2-5 3-5 7-1 2-2 4-1 7 1 1 1 2 2 3l3 1c-1 1-2 1-3 1-4-3-5-5-6-10 1-4 3-7 6-9z" class="E"></path><path d="M437 442c1-1 2-2 2-3v-1h1c1 1 0 2 1 3v1 1h1c0 1 0 1-1 2 0 0 0 1-1 2l1 1-1 1c-1 1-2 2-4 3-1 0-2 1-3 2-2 1-4 3-6 5 0-4 2-5 5-7h-4l5-4 2-3 2-3z" class="D"></path><path d="M432 452l2-2c2-2 3-4 6-5v1l-4 4v2c-1 0-2 1-3 2-2 1-4 3-6 5 0-4 2-5 5-7z" class="Y"></path><path d="M440 449c0 3 0 3 2 5s2 5 1 7v1 5c-2 5-5 9-9 10l-4 2-7-1h-2c-1-1-1-1-2 0h-1l-2 1c-1 0-2 1-2 2h-3l-1 1h-1-2l-1-2 9-4c0-1 0-2-1-3h-2c0-1-1-2-2-3h0v-3c1 1 2 3 3 3h3 2c2 0 3 1 4 1l-3-3 1-1 2 2h0c0-1-1-2-1-3 1 0 1 1 1 2l4 4c1 0 1 0 2-1 1 0 2 0 3-1 2 0 2 0 4-1l-1-2 1-3h0v-2c1-1 0-3-1-3h-1l-1-2h0l1-2v-1c1-1 2-2 3-2 2-1 3-2 4-3z" class="B"></path><path d="M435 464c2 0 2 1 4 1 0 1 1 2-1 3l-1 1h-2 0l-1-2 1-3z" class="o"></path><path d="M432 457c2 0 5 1 6 4 1 1 1 2 1 4-2 0-2-1-4-1h0v-2c1-1 0-3-1-3h-1l-1-2h0z" class="a"></path><path d="M440 449c0 3 0 3 2 5s2 5 1 7v1c-1-2-1-3-3-5s-4-2-7-2v-1c1-1 2-2 3-2 2-1 3-2 4-3z" class="f"></path><path d="M422 474c2 0 4 1 6 1 4 0 8-1 10-3s3-4 3-7h1c0 3-1 4-2 6l-1 1c-2 2-7 5-10 5h-2-1-1-3l1 1h-2c-1-1-1-1-2 0h-1l-2 1c-1 0-2 1-2 2h-3l-1 1h-1-2l-1-2 9-4c0-1 0-2-1-3h-2c0-1-1-2-2-3h0v-3c1 1 2 3 3 3 3 2 5 4 9 4z" class="n"></path><path d="M410 467c1 1 2 3 3 3 3 2 5 4 9 4l1 1-2 2v-1c-1 0-1 0-2-1v1h-4c0-1 0-2-1-3h-2c0-1-1-2-2-3h0v-3z" class="a"></path><path d="M400 446c1 0 1 1 1 2-1 2-1 5 0 7l1 1c-1 8 0 16 3 24h1l1 2h2 1l1-1h3c0-1 1-2 2-2l2-1h1c1-1 1-1 2 0h2l7 1 4-2c4-1 7-5 9-10 1-1 1-2 1-4h0 1c1 1 1 2 1 3v33c0 5 1 10 0 15h0v12c0 2 1 4 0 6h-23c-2 0-3 0-5 1 0 0-1-1-2-1 0-1-1-2-1-3v-5c-1 2 0 3-1 4l-1-2h0v-5-1l-1 1-1 1h-1l-2-3c-2-2-4-4-7-6l-4-4c-1 0-2 0-3 1l2-4c1-1 4-6 5-8h-4l2-2h1c-1-3-2-7-3-10v-1c0-1 1-1 1-2l1 2h2 0c-1-1-1-2-1-3h0c-1-2 0-4-1-5v-3c-1-4-2-9-1-13 1-2 0-6 1-8 0-2 1-5 1-7z" class="I"></path><path d="M408 498c-1 3 1 3 2 6h-1l-3-3v-2c1-1 0-1 0-2l1-1 1 2z" class="G"></path><path d="M407 496c-1-2-2-2-2-4l3 3c0 1 1 3 2 3a30.44 30.44 0 0 0 8 8c0 2 0 2-1 4l-4-4 1-1-1-1-1-2-2-2-2-2-1-2z" class="V"></path><path d="M418 506c3 3 6 5 10 6l5 3h3l1-1v1h2c1 0 2 0 2 1v1c1 0 1 1 2 2h0-1c1 1 2 2 3 2l-1 1h0c-2-1-3 0-5 0 0 1-1 0-2 0h-1c-2 0-3-1-5-2h0 2c0-1-5-3-5-3-1-1-2-1-3-1-3-1-5-4-8-6 1-2 1-2 1-4z" class="C"></path><path d="M412 495c1 2 3 4 5 5h1c4 3 8 6 13 9l6 3c3 0 7 0 8 2l-1 2h-3c0-1-1-1-2-1h-2v-1l-1 1h-3l-5-3c-4-1-7-3-10-6a30.44 30.44 0 0 1-8-8h2v-1l-1-1 1-1z" class="F"></path><path d="M412 495c1 2 3 4 5 5 1 1 1 2 2 3-2 0-3-1-5-1 0-2 0-2-1-3l-1-1v-1l-1-1 1-1z" class="J"></path><path d="M400 446c1 0 1 1 1 2-1 2-1 5 0 7l1 1c-1 8 0 16 3 24l1 3-1 2h0c1 1 1 1 1 2 1 3 3 5 6 8l-1 1 1 1v1h-2c-1 0-2-2-2-3l-3-3c0-1 0-2-1-3l-1 1c0 1 0 0 1 1v2 1h-2c0-2-1-3-2-5l-1-4h2 0c-1-1-1-2-1-3h0c-1-2 0-4-1-5v-3c-1-4-2-9-1-13 1-2 0-6 1-8 0-2 1-5 1-7z" class="F"></path><path d="M401 482v-4c-1-2-1-4-1-6h1c1 3 1 5 1 7-1 1 0 2-1 3z" class="J"></path><path d="M401 482c1-1 0-2 1-3l4 8c1 3 3 5 6 8l-1 1 1 1v1h-2c-1 0-2-2-2-3l-3-3c0-1 0-2-1-3 0-2-2-5-3-7z" class="c"></path><path d="M445 514h1v12c0 2 1 4 0 6h-23v-5c0-3 0-5 1-8 6 3 13 5 21 5-2-1-3-2-6-2 2 0 3-1 5 0h0l1-1c-1 0-2-1-3-2h1 0c-1-1-1-2-2-2v-1h3l1-2z" class="s"></path><path d="M401 498c2 2 4 5 6 7 5 6 11 11 17 14h0c-1 3-1 5-1 8v5c-2 0-3 0-5 1 0 0-1-1-2-1 0-1-1-2-1-3v-5c-1 2 0 3-1 4l-1-2h0v-5-1l-1 1-1 1h-1l-2-3c-2-2-4-4-7-6l-4-4c-1 0-2 0-3 1l2-4c1-1 4-6 5-8z" class="g"></path><path d="M397 509h2l2 1s0-1 1-1l-1-1v-1l1-1 2 2h0 1c-2 0-2 0-3 1l1 3h1v-1c1-1 1-1 2-1 3 1 5 5 6 7 1 0 2 2 2 3 0 0 1 0 1-1v5c-1 2 0 3-1 4l-1-2h0v-5-1l-1 1-1 1h-1l-2-3c-2-2-4-4-7-6l-4-4z" class="o"></path><path d="M401 513c5 0 7 2 9 5 1 0 1 1 2 2h1l-1 1-1 1h-1l-2-3c-2-2-4-4-7-6z" class="S"></path><path d="M415 519c1-1 1-1 3-2 0 0 1 1 2 1v2c1 0 1-1 2 1 0 1 0 4 1 6v5c-2 0-3 0-5 1 0 0-1-1-2-1 0-1-1-2-1-3v-5-5z" class="c"></path><path d="M443 467c1-1 1-2 1-4h0 1c1 1 1 2 1 3v33c0 5 1 10 0 15h0-1c-1-2-5-2-8-2l-6-3c-5-3-9-6-13-9h-1c-2-1-4-3-5-5-3-3-5-5-6-8 0-1 0-1-1-2h0l1-2-1-3h1l1 2h2 1l1-1h3c0-1 1-2 2-2l2-1h1c1-1 1-1 2 0h2l7 1 4-2c4-1 7-5 9-10z" class="p"></path><path d="M429 482c4 0 6 1 10 1h1c1 1 2 3 2 4-2-1-5-3-7-3l-6-2z" class="K"></path><path d="M433 501l2-1 1 1c1 1 0 1 1 0l2 1c0 1 0 1 1 2s3 1 5 2l-1 1c-1-1-3-1-4-2h-1c-1-1-1-1-1-2-3 0-5 4-8 5v-2s0-1 1-2c0-1 1-2 2-3z" class="h"></path><path d="M426 485c1 0 3 0 4 1l5 1h1c1 1 3 2 4 4h0c-6-2-11-3-18-3l2-1-1-2h2 1z" class="X"></path><path d="M435 484c2 0 5 2 7 3 2 2 3 4 3 7v1c-2-1-4-2-5-4h0c-1-2-3-3-4-4h-1l-5-1c2-1 3-1 5-2z" class="N"></path><path d="M443 467c1-1 1-2 1-4h0 1c1 1 1 2 1 3v33c0 5 1 10 0 15-1-3 0-6 0-9v-19-4c0-1-2-2-3-2l1-1h-3-2-3 0l-2 1h-1c-2 0-2-1-3-1h0l4-2c4-1 7-5 9-10z" class="K"></path><path d="M430 490c3 0 6 1 9 2 2 1 4 4 6 5v2c-2-1-1-1-2-1 0-1-1-1-1-1v1h0c-1 0-2-1-3-1l-1 1v1s1 0 1-1l1 1c1 1 3 3 5 3v1c-1 0-1 0-1-1h-2s-1-1-2-1c0-1 0 0-1-1l-2 1c-1 1 0 1-1 0l-1-1-2 1c-1 1-2 2-2 3-1 1-1 2-1 2l-1-1 1-1v-1l-1 1h-1l-1 1c0-1-1-1-1-1l-3-3c-1-1-2-2-2-4 0-1 1-2 2-3s5-1 7-1c-1-1 0-1-1-2h1c0-1 1-1 0-1z" class="n"></path><path d="M424 501h3 6c-1 1-2 2-2 3-1 1-1 2-1 2l-1-1 1-1v-1l-1 1h-1l-1 1c0-1-1-1-1-1l-3-3h1z" class="N"></path><path d="M423 501c-1-1-2-2-2-4 0-1 1-2 2-3s5-1 7-1l2 1c1 1 1 2 0 3h-1v-1h-2-1c0-1-1-1-1-1h-1v1l-2 5h-1z" class="O"></path><path d="M406 480l1 2h2 1l1-1h3c0-1 1-2 2-2l2-1h1c1-1 1-1 2 0l-1 2 1 1c1-1 1-1 2-1h0l1 1c1 1 3 1 5 1l6 2c-2 1-3 1-5 2-1-1-3-1-4-1h-1-2l1 2-2 1h-1l-4 1 1 1c4-1 8-1 12 0 1 0 0 0 0 1h-1c1 1 0 1 1 2-2 0-6 0-7 1s-2 2-2 3c0 2 1 3 2 4l3 3s1 0 1 1l1-1h1l1-1v1l-1 1 1 1v2l1 1c-5-3-9-6-13-9h-1c-2-1-4-3-5-5-3-3-5-5-6-8 0-1 0-1-1-2h0l1-2-1-3h1z" class="k"></path><path d="M415 486c1 0 2 0 3 1h3v1h0l-4 1c-2 0-2 0-4 1-1 0-1 0-2-1v-1c1-1 3-2 4-2z" class="H"></path><path d="M415 486c-1-1 0-1-2-1h0l-1-1h4l1 1c1 1 3 0 5 0v-1c1 0 3 0 4 1h-1-2l1 2-2 1h-1 0v-1h-3c-1-1-2-1-3-1z" class="e"></path><path d="M406 483c4 6 7 12 12 17h-1c-2-1-4-3-5-5-3-3-5-5-6-8 0-1 0-1-1-2h0l1-2z" class="G"></path><path d="M418 490c4-1 8-1 12 0 1 0 0 0 0 1h-1c1 1 0 1 1 2-2 0-6 0-7 1s-2 2-2 3c0 2 1 3 2 4l3 3s1 0 1 1h-1c-1-1-2-1-2-2l-1-1c-2-1-3-2-4-4 0-2-1-3 1-5v-1l-2 1h-3 0-1l4-3z" class="C"></path><path d="M339 383h1c1 0 2-1 3-1v1c-1 1-3 2-4 4v1l-2 1-5 3v1c2 1 4 0 6 0h0v1l20-1 2-1h5 2 1l1-1h0l2-2h0 1c1 1 1 0 1 1h1l2 2h0l4-1v1h3 5l8 1 3-1 4 1c-1 1-1 2-1 4v1 2h2 2v2h-2v1 1l-2 2-1-1h-1c-1 0-1 1-1 2h1c-1 1-1 2-2 2v1 1l2 1c0 1-1 3-2 4v1l-6 9-1 2c-1 0-2-1-3-1h0-7-1c-2 1-5 0-7 0h-11-16-7l1-1h-5l-2-2h0l1-1c0-2 0-2-1-3-2-1-4-1-6-1h-8c2-2 5-4 5-7v-3l2-1v-1h-2v-2c1 0 1 0 1-1 1-2 2-3 3-4 1-2 1-3 0-4h0-1l-1-1 2-2c1 0 2 0 3-1l1-1h0l1-1v-1l1-1c0-2 3-4 5-5z" class="J"></path><path d="M369 401c-3 0-8 1-10-1h1c3-1 6-1 9 0v1z" class="P"></path><path d="M335 396c2-1 4-1 6-1v1c-1 0-2 1-2 1l-1 3c1 0 1 0 3 1-1 0-1 1-2 1h-1v-2h-1v-3h-3l1-1z" class="G"></path><path d="M367 392h1l1-1h0l2-2h0 1c1 1 1 0 1 1h1l2 2h0l4-1v1h3c1 1 1 2 0 4 0 1-1 2-1 4h-3v-3c-1-1-1-1-3-1 0-1 1-2 2-3l-1-1h-12 2z" class="h"></path><path d="M367 392h1l1-1h0l2-2h0 1c1 1 1 0 1 1h1l2 2h0l4-1v1h-13z" class="K"></path><path d="M358 403h33 13v1l-2 2-1-1h-1c-1 0-1 1-1 2h-1v-1-1h-3-6l-11-1-19 1-1-2z" class="T"></path><path d="M378 404l11 1h6 3v1 1h1 1c-1 1-1 2-2 2v1 1c-2 0-3 1-5 1h-13c0-1 1-2 1-2l1-2c1 0 1 0 2 1 0-1-1-2-1-2v-1c-2 0-3-1-5-1v-1z" class="G"></path><path d="M339 383h1c1 0 2-1 3-1v1c-1 1-3 2-4 4v1l-2 1-5 3v1c2 1 4 0 6 0h0v1l-5 1h-2v1h4l-1 1h3v3h1v2h1c1 0 1-1 2-1h1c2-1 3-1 5-1-1 2-2 1-3 3 1 0 3 0 4 1 3-1 7-1 10-1l1 2-2-1c-3 1-9 1-12 1h-7c0 1 0 1-1 1h-4c-2 0-2 0-4 1h-1-1-1-2v-2c1 0 1 0 1-1 1-2 2-3 3-4 1-2 1-3 0-4h0-1l-1-1 2-2c1 0 2 0 3-1l1-1h0l1-1v-1l1-1c0-2 3-4 5-5z" class="D"></path><path d="M334 397h3v3h-3c0-2-1-2 0-3z" class="W"></path><path d="M342 401c2-1 3-1 5-1-1 2-2 1-3 3 1 0 3 0 4 1h-9v-1l3-1v-1z" class="F"></path><path d="M328 400l1 1h4c0 1 0 2-1 3-1 0-3 0-5 1h9l1-1h1v1c0 1 0 1-1 1h-4c-2 0-2 0-4 1h-1-1-1-2v-2c1 0 1 0 1-1 1-2 2-3 3-4z" class="X"></path><defs><linearGradient id="l" x1="393.049" y1="397.409" x2="387.258" y2="394.494" xlink:href="#B"><stop offset="0" stop-color="#575757"></stop><stop offset="1" stop-color="#6f6e6e"></stop></linearGradient></defs><path fill="url(#l)" d="M383 392h5l8 1 3-1 4 1c-1 1-1 2-1 4v1 2h2 2v2h-2v1h-13l1-1h-3c-2-1-5 0-7-1h-13v-1h10 3c0-2 1-3 1-4 1-2 1-3 0-4z"></path><path d="M396 393l3-1c-1 2-1 2-2 3s-1 2-1 3l-1 1-1-1c-1-1 0-1 0-3h-2v-1h3l1-1z" class="L"></path><path d="M395 400h5 2 2 2v2h-2-7c-1 0-2-1-3-1s-3 1-5 0h0-1c1 0 1-1 2-1h4 1z" class="K"></path><path d="M383 392h5l1 2c-1 1-2 1-4 2 1 1 2 2 2 4h-5c0-2 1-3 1-4 1-2 1-3 0-4z" class="M"></path><path d="M399 392l4 1c-1 1-1 2-1 4v1 2h-2-5v-1l1-1c0-1 0-2 1-3s1-1 2-3z" class="R"></path><path d="M338 405h7v3c-1 1-2 3-2 5h0v4c-2 1-4 3-6 3-1-1-1-1-4-1h-6-8c2-2 5-4 5-7v-3l2-1v-1h1 1 1c2-1 2-1 4-1h4c1 0 1 0 1-1z" class="g"></path><path d="M326 407h1 1 1c2-1 2-1 4-1 1 1 2 1 3 3l-1 1c-1 0-2 1-3 1l-1 1 1 1 2 1c-3 0-6 1-8 0-1-2 0-2 1-4 0 0 0-1-1-2h0v-1z" class="I"></path><path d="M338 405h7v3c-1 1-2 3-2 5h0-3-1c-2 0-3 1-5 0v1l-2-1-1-1 1-1c1 0 2-1 3-1l1-1c-1-2-2-2-3-3h4c1 0 1 0 1-1z" class="B"></path><path d="M332 413v-2h2c1 1 1 1 0 2v1l-2-1z" class="E"></path><path d="M339 413h-1v-3l1-1c1 1 1 2 1 3v1h-1z" class="Y"></path><path d="M345 405c3 0 9 0 12-1l2 1 19-1v1c2 0 3 1 5 1v1s1 1 1 2c-1-1-1-1-2-1l-1 2s-1 1-1 2l-37 1c0-2 1-4 2-5v-3z" class="d"></path><path d="M380 412h13c2 0 3-1 5-1l2 1c0 1-1 3-2 4v1l-6 9-1 2c-1 0-2-1-3-1h0-7-1c-2 1-5 0-7 0h-11-16-7l1-1h-5l-2-2h0l1-1c0-2 0-2-1-3-2-1-4-1-6-1h6c3 0 3 0 4 1 2 0 4-2 6-3v-4h0l37-1z" class="Q"></path><path d="M367 425l-13-1-1-1c0-1 2-2 3-2l14-2c2 0 3 0 4 1h2c2 0 4 1 5 2l1 1-2 1-13 1z" class="E"></path><path d="M383 418c2 0 3 0 5 1h1v2h1c1 0 2 0 3 1v1c-1 0-1 1-2 2l1 1-1 2c-1 0-2-1-3-1h0-7-1c-2 1-5 0-7 0h-11-16-7l1-1h1l26-1h0l13-1h7l-6-6h2z" class="P"></path><path d="M391 425l1 1-1 2c-1 0-2-1-3-1h0-7-1l1-1h2l2 1c1-1 0-1 1-1-2 0-4 0-6-1h-1 12z" class="f"></path><path d="M383 418c2 0 3 0 5 1h1v2h1c1 0 2 0 3 1v1c-1 0-1 1-2 2h-12c-3-1-8 0-12 0h0l13-1h7l-6-6h2z" class="H"></path><path d="M343 417c1-1 2-1 4-1h3v-1c3 1 5 0 8 0l17 1h4c1 1 3 1 4 2h-2-10-1-6c-5 1-11 0-16 2h-4c-1 2-1 2-2 3h-2l-1 2s1 0 2 1h-1-5l-2-2h0l1-1c0-2 0-2-1-3-2-1-4-1-6-1h6c3 0 3 0 4 1 2 0 4-2 6-3z" class="e"></path><defs><linearGradient id="m" x1="374.473" y1="404.914" x2="369.178" y2="420.637" xlink:href="#B"><stop offset="0" stop-color="#110d0e"></stop><stop offset="1" stop-color="#2d2f2e"></stop></linearGradient></defs><path fill="url(#m)" d="M380 412h13c2 0 3-1 5-1l2 1c0 1-1 3-2 4v1l-6 9-1-1c1-1 1-2 2-2v-1c-1-1-2-1-3-1h-1v-2h-1c-2-1-3-1-5-1-1-1-3-1-4-2h-4l-17-1c-3 0-5 1-8 0v1h-3c-2 0-3 0-4 1v-4h0l37-1z"></path><path d="M375 416h20 1 2v1l-6 9-1-1c1-1 1-2 2-2v-1c-1-1-2-1-3-1h-1v-2h-1c-2-1-3-1-5-1-1-1-3-1-4-2h-4z" class="S"></path><path d="M339 336h1l6-5h1v2h2 0c2 0 3 2 4 4h1l1 1-1 1c2 0 2-1 3 0l-1 1 1 1h2v1h1 2c2 1 3 1 4 2v1l-2 1h0l-3 3v1l1 1 2-1 1 1c2 0 3-1 5-1l-1 1c1 1 0 1 1 1 1 1 1 1 2 1l1 1c-1 2-1 2 0 4l-1 1 2 1c-1 1-1 1-1 2l-3 3h0c-1 1-2 2-2 5-3 1-7 7-8 10l-2 2c-2 3-4 6-7 9h2 4 1v2l-20 1v-1h0c-2 0-4 1-6 0v-1l5-3 2-1v-1c1-2 3-3 4-4v-1c-1 0-2 1-3 1h-1c-2 1-5 3-5 5l-1 1v1l-1 1h0l-1 1c-1 1-2 1-3 1l-2 2 1 1h1 0c1 1 1 2 0 4-1 1-2 2-3 4 0 1 0 1-1 1v2h2v1l-2 1v3c0 3-3 5-5 7-4 1-8 1-11 0h-2-6c0-2-3-3-4-5v-1l-4-4 1-1h1c0-2-1-2-2-3v-1h0 2 1l-1-5h1 1v2l1 1 1-1c-1-5 0-8 1-13v-1l1-2h1v1c1-1 1-1 1-2h2c1-1 1-1 3-2-1 0-1 0 0-1-1-1-1-2-1-2l-1-1-1-1h1-2l-1-2 1-1c1-2 2-2 2-5 1-3 3-5 4-7 3-4 7-9 12-12l15-12c1-1 2-2 3-2z" class="Y"></path><path d="M308 383v-1-4h1c1 3 2 4 1 7v-1l-1 1-1-2z" class="G"></path><path d="M320 357l1 1c-2 2-3 4-4 6l-2 2h-1c-1 0-1 1-2 1h-1l1-1c1-2 3-3 4-5 2-1 3-2 4-4z" class="Q"></path><path d="M310 366c4-5 8-9 12-13 2-1 3-3 5-4v1c-2 2-6 4-7 7-1 2-2 3-4 4-1 2-3 3-4 5h-2z" class="F"></path><path d="M301 386c1-1 1-1 1-2h2c1-1 1-1 3-2l1 1 1 2c0 1 1 2 1 4-1 2-2 2-4 2s-3-2-4-3l-1-2z" class="N"></path><path d="M321 360l2-1h1l-5 5 10 1c4 0 7 0 10-1v1l-3 1c-5 1-11 1-16 1l-2 4 1 2-2 1c-1 1-1 1 0 2v2l-1 4h0v-1c-1-2-1-7-1-10l1-4h1c0-3 2-5 4-7z" class="E"></path><path d="M299 387c1 1 3 2 3 4h0c1 1 2 0 2 2l-1 1c0 3-1 6 0 9 0 1 1 2 2 3-1 1-2 1-3 2-2-2-1-4-2-5l-2-2c-1-5 0-8 1-13v-1z" class="Q"></path><path d="M343 365c2-2 4-3 7-4l-2 2v1l1-1h2c-1 0-1 1-1 1l-1 1 1 1v2l-1 1c-2 1-3 2-5 3-2-1-5 1-7 2l-11 8-1-1c1-1 3-2 5-3 1-1 3-2 4-4s5-4 6-7h0c1-1 2-1 3-2z" class="B"></path><path d="M343 365c2-2 4-3 7-4l-2 2v1l1-1h2c-1 0-1 1-1 1l-1 1 1 1v2l-1 1h-3l1-2c-2 0-4 2-6 2h0l1-1v-2l1-1z" class="D"></path><path d="M339 336h1l6-5h1v2l-4 4c-2 2-4 3-5 4s-1 1-2 1c-3 3-7 5-9 7-2 1-3 3-5 4-4 4-8 8-12 13 0 2-3 5-3 8l1 2h0c-1 0-2 0-3 1h-2l-1-2 1-1c1-2 2-2 2-5 1-3 3-5 4-7 3-4 7-9 12-12l15-12c1-1 2-2 3-2z" class="Q"></path><path d="M309 401l1 1 1-2c0-1 0-1 1-2v-2c1 1 1 0 3-1h5c1-1 2-4 3-5s3-1 5-2c2-2 5-3 7-5 2-1 4-3 6-4s4-3 6-3l-1 2-7 5c-2 1-5 3-5 5l-1 1v1l-1 1h0l-1 1c-1 1-2 1-3 1l-2 2 1 1h1 0c1 1 1 2 0 4-1 1-2 2-3 4 0 1 0 1-1 1h-1c0 1 0 1-1 2l-2-5v1c0 1-1 2-1 3h0v1l-2-1v-2c0-1 0-2-1-2h-1l-1-1c-1 1-1 4-1 5-2-1-3-3-4-5z" class="B"></path><path d="M320 401c0-3 0-5 2-7 1 1 1 0 3 1-1 1-2 2-3 2 1 1 1 2 2 2v1l-4 1z" class="E"></path><path d="M328 396h0c1 1 1 2 0 4-1 1-2 2-3 4 0 1 0 1-1 1h-1c0 1 0 1-1 2l-2-5v-1l4-1c2-1 3-2 4-4z" class="b"></path><path d="M303 403c0-2 0-5 1-6 0 1 0 1 1 1l1-1-1-2 2-2c1 3 0 4 0 6l2 2c1 2 2 4 4 5 0-1 0-4 1-5l1 1h1c1 0 1 1 1 2v2l2 1v-1h0c0-1 1-2 1-3v-1l2 5c1-1 1-1 1-2h1v2h2v1l-2 1v3c0 3-3 5-5 7-4 1-8 1-11 0h-2-6c0-2-3-3-4-5v-1l-4-4 1-1h1c0-2-1-2-2-3v-1h0 2 1l-1-5h1 1v2l1 1 1-1 2 2c1 1 0 3 2 5 1-1 2-1 3-2-1-1-2-2-2-3z" class="V"></path><path d="M313 406c0-1 0-4 1-5l1 1c1 2 2 3 2 5-2 0-3 0-4-1z" class="E"></path><path d="M303 403c0-2 0-5 1-6 0 1 0 1 1 1l1-1-1-2 2-2c1 3 0 4 0 6-2 0-1 0-3 1l2 4 1 1v-1h0v-2l3 3c-1 1-1 1-2 1 0 2 2 4 3 6l3 3h-1c-1 0-4-2-5-4 0 0 1 0 0-1 0-1-2-3-3-4s-2-2-2-3z" class="F"></path><path d="M298 401l2 2c1 1 0 3 2 5 1-1 2-1 3-2 1 1 3 3 3 4 1 1 0 1 0 1l-2-2v1c1 2 4 5 6 7h3v1c-3 0-5 1-7 0l-1-1c-6-3-8-9-10-15l1-1z" class="Y"></path><path d="M294 399h1 1v2l1 1c2 6 4 12 10 15l1 1v1h-2-6c0-2-3-3-4-5v-1l-4-4 1-1h1c0-2-1-2-2-3v-1h0 2 1l-1-5z" class="Z"></path><path d="M293 408h1c0-2-1-2-2-3v-1h0 2 1c1 5 4 11 9 14 0 1 1 1 2 1h-6c0-2-3-3-4-5v-1l-4-4 1-1z" class="V"></path><path d="M343 337l4-4h2 0c2 0 3 2 4 4h1l1 1-1 1c2 0 2-1 3 0l-1 1 1 1h2v1h1 2c2 1 3 1 4 2v1l-2 1h0l-3 3v1l1 1 2-1 1 1c-3 2-8 4-11 6l-3 2c-2 1-7 4-10 4-1 0-1 0-2 1-3 1-6 1-10 1l-10-1 5-5h-1l-2 1c2-2 4-4 7-6 2-1 4-2 6-4 1-1 2-2 4-3 0-1 1-3 2-4l-1-1h-3c1 0 1 0 2-1s3-2 5-4z" class="N"></path><path d="M355 353c3-1 4-5 6-7l-4 3h-1c2-2 4-3 4-6h-2v-1h0l-2 1c-1 0-1-1-1-1l1-2 1 1h2v1h1 2c2 1 3 1 4 2v1l-2 1h0l-3 3v1l1 1 2-1 1 1c-3 2-8 4-11 6l-3 2v-1c0-2 2-4 4-5z" class="Z"></path><path d="M362 342c2 1 3 1 4 2v1l-2 1v-1l-2 1v-1l-1-1 1-2z" class="g"></path><path d="M343 337c2 0 3-1 5-3v1l-1 1c2 1 3-1 5 1l-1 1-1 1-4 4h1c0-1 1-1 2-1l-2 2v2c2-1 4-3 6-4h1c-1 1-2 1-2 2s-1 2-2 3l-3 3-2 2-2 1v-3c1-1 1 0 2-2-2 2-3 2-5 2h0-1c-1-1-4 0-5 0 1-1 2-2 4-3 0-1 1-3 2-4l-1-1h-3c1 0 1 0 2-1s3-2 5-4z" class="C"></path><path d="M338 341h0c2 0 3-1 4-2 1 0 1 0 2 1h1l1 2c-2 2-6 4-8 6h1l7-4-6 6h-1c-1-1-4 0-5 0 1-1 2-2 4-3 0-1 1-3 2-4l-1-1h-3c1 0 1 0 2-1z" class="G"></path><path d="M347 350c4 0 7-4 9-6 0 2-1 4-3 6h0c-1 3-3 5-5 7h0l6-5 1 1c-2 1-4 3-4 5v1c-2 1-7 4-10 4-1 0-1 0-2 1-3 1-6 1-10 1v-2c1 0 2-2 4-2 3-1 5-3 7-5l3-3 2-1 2-2z" class="C"></path><path d="M347 350c4 0 7-4 9-6 0 2-1 4-3 6-4 0-8 8-12 11h-3 0l5-4c-1 0-2 0-3-1l3-3 2-1 2-2z" class="i"></path><path d="M343 353l2-1 1 1c-1 2-1 3-3 4h0c-1 0-2 0-3-1l3-3z" class="h"></path><path d="M340 350h0c2 0 3 0 5-2-1 2-1 1-2 2v3l-3 3c-2 2-4 4-7 5-2 0-3 2-4 2v2l-10-1 5-5h-1l-2 1c2-2 4-4 7-6 2-1 4-2 6-4 1 0 4-1 5 0h1z" class="D"></path><path d="M334 350c1 0 4-1 5 0-5 3-9 7-15 9h-1l-2 1c2-2 4-4 7-6 2-1 4-2 6-4z" class="c"></path><path d="M365 351c2 0 3-1 5-1l-1 1c1 1 0 1 1 1 1 1 1 1 2 1l1 1c-1 2-1 2 0 4l-1 1 2 1c-1 1-1 1-1 2l-3 3h0c-1 1-2 2-2 5-3 1-7 7-8 10l-2 2c-2 3-4 6-7 9h2 4 1v2l-20 1v-1h0c-2 0-4 1-6 0v-1l5-3 2-1v-1c1-2 3-3 4-4v-1c-1 0-2 1-3 1h-1l7-5 1-2 2-1-2-1c0-1 0-1 1-1v-1h-1c-2 0-5 2-6 2l3-2c2-1 3-2 5-3l1-1v-2l-1-1 1-1s0-1 1-1h-2l-1 1v-1l2-2c2-1 3-2 4-3v-1c3-2 8-4 11-6z" class="L"></path><path d="M365 353l4-2c1 1 0 1 1 1 1 1 1 1 2 1l1 1c-1 2-1 2 0 4l-1 1c0 1-2 3-3 4h-2c0-2 3-5 3-7-1 1-2 1-2 2-1 0-2 1-2 2l-4 3c1-2 3-4 4-6h-1l-2 2c0-1 1-2 2-3l1-1c-1-1-1-1-1-2z" class="P"></path><path d="M349 382h1l2-2c6-4 11-11 17-16l1 1c-1 1-2 2-2 5-3 1-7 7-8 10l-2 2v-3-1h0c-2 2-6 6-8 6v-1l-1-1z" class="C"></path><path d="M366 360l-3 3 1 1 1-1h0c-1 2-3 4-4 6 0 2-5 7-7 8-1 1-3 3-5 4-3 0-6 4-10 6 1-2 3-3 4-4v-1c-1 0-2 1-3 1h-1l7-5 1-2 2-1-2-1c0-1 0-1 1-1v-1h-1 0l2-2c2 0 3 0 4-1v-1c1-1 1-1 2-1s2-1 3-1h0l1-2v1h1c1 0 2-1 2-2l4-3z" class="i"></path><path d="M358 366c0 1 0 2-1 3 0 0 1 0 2-1h1c-1 2-3 3-4 4s-2 3-4 3v-2l-3 2-2-1c0-1 0-1 1-1v-1h-1 0l2-2c2 0 3 0 4-1v-1c1-1 1-1 2-1s2-1 3-1z" class="H"></path><path d="M353 369l-1 2h1l2-2v1c-1 1-1 2-3 3h0l-3 2-2-1c0-1 0-1 1-1v-1h-1 0l2-2c2 0 3 0 4-1z" class="B"></path><path d="M349 375l3-2v2c-1 1-1 2-2 2 2 1 2-1 4 0-1 1-3 3-5 4-3 0-6 4-10 6 1-2 3-3 4-4v-1c-1 0-2 1-3 1h-1l7-5 1-2 2-1z" class="V"></path><path d="M349 375l3-2v2l-6 3 1-2 2-1z" class="I"></path><path d="M365 351c2 0 3-1 5-1l-1 1-4 2c0 1 0 1 1 2l-1 1c-1 1-2 2-2 3l2-2h1c-1 2-3 4-4 6 0 1-1 2-2 2h-1v-1l-1 2h0c-1 0-2 1-3 1s-1 0-2 1v1c-1 1-2 1-4 1l-2 2h0c-2 0-5 2-6 2l3-2c2-1 3-2 5-3l1-1v-2l-1-1 1-1s0-1 1-1h-2l-1 1v-1l2-2c2-1 3-2 4-3v-1c3-2 8-4 11-6z" class="M"></path><path d="M350 364h3 1c0 1-1 2-2 2 2 0 3-2 5-2 0 1-1 2-1 2l-2 1-1 1v1c-1 1-2 1-4 1l-2 2h0c-2 0-5 2-6 2l3-2c2-1 3-2 5-3l1-1v-2l-1-1 1-1z" class="V"></path><path d="M365 351c2 0 3-1 5-1l-1 1-4 2c-2 0-3 2-4 3h1c-1 1-1 1-2 1-2 1-3 3-4 5l-2 2h-1-3s0-1 1-1h-2l-1 1v-1l2-2c2-1 3-2 4-3v-1c3-2 8-4 11-6z" class="X"></path><path d="M349 382l1 1v1c2 0 6-4 8-6h0v1 3c-2 3-4 6-7 9h2 4 1v2l-20 1v-1h0c-2 0-4 1-6 0v-1l5-3 2-1v-1c4-2 7-6 10-6l-3 2h1l2-1z" class="G"></path><path d="M340 393h3c1-1 1-1 2-1h1 1c2 0 1-1 2-2 0-1 1-2 2-2 1-2 3-3 5-5l2-4v3c-2 3-4 6-7 9h2 4 1v2l-20 1v-1h2z" class="H"></path><path d="M339 387c4-2 7-6 10-6l-3 2-1 1c3 1 4-1 4 2h0c-1 1-2 1-2 2h-1c-2 1-4 3-6 5h-2 0c-2 0-4 1-6 0v-1l5-3 2-1v-1z" class="F"></path><path d="M560 281l4-1h0l-1 2c2 1 2-1 3 0l1 1-1 1s-1 1-1 2l1 1-1 1h3 0l-2 2c1 1 3 1 4 2h2c1 0 3 1 4 0h2c3 0 5 1 7 1l1 2c-2 0-4 0-5-1-3-1-7 0-9 0l-1 1c2 1 6 0 8 1h2c4 0 7 2 10 3 1 0 1 0 1 1l1 1 2 1 3 2h1v-2h1 1v-1c2-2 3-2 5-3 2 0 3-1 6-1v1h5c0 1 1 1 2 1 0-1 2-2 3-3h1l1 1 2-1 1-1c1 1 1 2 1 3 1 1 2 2 3 4h1c0 2 1 3 2 4h-1v-1c-1-1-2-1-3-2h-2c-1 1-1 1-1 3 1 0 1 0 2 1 1 0 2 1 3 1 1 1 2 1 3 2l3 3v1h0l2 2h0l1-1c2-1 4-5 5-6v3h7v1h5l2 1 7 4c1 0 1 1 2 1 2 2 5 5 6 7l1 2 2 6 1 1 2 1h15 3 3 2 1c3 0 11 1 12 0 3 0 5 0 7-1v2c-2 1-4 3-5 6s0 8 0 12v118c0 3-1 5-2 8-1 1-2 4-2 5-2 2-3 4-4 7 0 0 0 1-1 2l-4-8c-1 2-1 2-1 4v1 3l-4 6-6 9c1 1 1 1 1 2h1l2 1v1 1l1 2c-2 1-4-1-7 1 2 1 3 2 5 3 1 1 1 2 2 4h-1l2 3h-1l-1 1-5-1v2s1 0 1 1h0 0c1 2 3 2 3 4v4l3 1h1s1 0 1-1h1v1h1 1l-1 1h-1c-1 1-1 2-1 3l3 1-1 1c1 1 2 1 2 1v1h-1c1 1 2 2 2 4 0 5 3 8 5 12h0v5l1 1v4c-1 5-3 9-6 14-1 1-1 1-1 2l1 2c-2 2-4 6-6 8v-1c0-1 0 0-1-1l-1 1h-1c0 1-1 2-2 2-4 8-10 16-16 24-1 2-4 4-5 7l-6 6c-2 3-4 5-6 7-1 1-4 3-4 4-1 2-2 3-4 5h0l1 2-2 3c0 1 0 1-1 2h1c1-1 2 0 3 0-1 2-3 3-4 5l-6 6-5 3 1 1c2 1 4 4 6 4l2 1-1 4 2-1 1 1 1 1c2 0 2 2 3 3l-2 2h-1l-2 2-2 1c-1-1-2-1-3-2l-5 5 1 3c-2 1-3 2-4 3l-2 2v1h-1-3 0c1 3 2 5 2 7-1 3-2 6-5 7l1 2-1 1c1 0 1 0 2 1s2 1 3 1c1 1 4 0 6 0 1 0 2 1 3 1s2 0 3 1h1c0 1-1 3-1 4v4h0 2c-2 2-4 2-4 5-4 2-8 4-12 7l-5 5 2 1 1-1h2 0 2c1 1 3 2 4 2l1-1c1 0 1 1 1 1l3 1h0c-1 2-1 3-2 4l-1 1h1l1 1c0 1 0 2 1 3l-1 1v2c-2 1-3 3-4 5h-1l-5 1-2 2-1 1v1c-2-1-2-1-4-1-1 1-1 1-1 2h0-2v2 1c0 1-1 1-2 2-1 2-2 2-3 2h-1l-1 1c0 1 1 2 1 2l-2 2-4-1c-1 1-1 2-3 2 0 1-1 2-1 3-1 1-1 3-2 4-2 1-4 4-7 4-1 0-1 0-2-1l-1-1-1 1 2 12h0c0-1 0-2 1-3l-1 17v1l-1-1c0 1-1 2-1 2h-1c0 3-2 6-3 9l1 2 1 1 1 1c-4 8-10 15-18 19-4 3-9 5-13 7h-2v-2h-1c-4 0-6 1-9 3h-1-1v1-2-1l-2 3c-1 1-2 2-3 4l-1 1-1 2h3l-1 3c-2 2-2 5-2 8l1 2 1 1 4 4c2 2 3 3 6 5 1 2 2 4 4 6 1 0 2 0 3 1l6 3 1 1c2 0 3 1 5 2l4 1c2 0 4 1 7 2 4 0 8 0 12 1 3-1 7-1 10-2-3 2-6 4-10 5-3 1-6 2-10 3-3 2-22-3-27-4v1c2 1 2 0 4 1h0c2 1 4 1 6 1v2h-1c-1-1 0 0-1 0-2 0-5 0-6-1h-1-2s-1 1 0 1c-2 0-4-1-6-1l1-1c-12-4-23-8-34-14-8-4-16-11-23-17-9-8-18-16-28-22l-9-6-14-5c-2 0-4-1-7 0h0c-5 0-11 0-16 1l2-4 4-4 3-6h2l-1-1v-2c-1-2-2-4-4-6 6-3 10-7 14-13l-3-1v-1-1c-1-1-1-1-1-2v-8c1-5 5-8 9-11v-3-13-29-9-17-25-5-19-15c0-2 1-5-1-7h1v-40-11c0-6 0-12 1-19l2-1c1-1 1-2 1-3l1 1h0v-5-8l1-9c-2-1-6 0-7 0h-23c0-5 1-9 1-13v-6c-1-5-2-7-6-10 2-1 3-1 5-1h23c1-2 0-4 0-6v-12h0c1-5 0-10 0-15v-33c0-1 0-2-1-3h-1 0c0 2 0 3-1 4v-5-1c1-2 1-5-1-7s-2-2-2-5l1-1-1-1c1-1 1-2 1-2 1-1 1-1 1-2 2-4-3-8-3-11l2 2h1 1v2c1 1 2 1 2 3h0v-1c2-5 1-12 1-18v-1c0-5 0-11-2-17l6-2c-1-3-1-7-3-10-2-6-5-12-9-18h-1c-2-1-2-1-3-2v1h-1c-2-4-6-7-10-10-3-2-6-4-8-6l1-2c-7-4-13-7-21-9-3-1-7-2-11-2-2-1-4-1-6-2v-1c2-2 6-2 9-3 1 0 2-1 4-1 7-4 14-9 21-14 3-2 5-4 8-6s7-4 10-6h-3v-1l1-1c2-1 2 0 4-1h-1c1-1 3-1 4-1l-2-1c1-2 3-2 5-3h5l3-1 6-2c8-3 14-4 22-4l1-1h-5l1-1h0 1c2 0 3-1 4-2h0-5l1-1c0-1-1-2-1-3h5c-1-1-1-1-2-1l2-1h0v-1l-2-1c2 0 4 1 6 1 2 1 4 2 6 2s4 0 5 2l1-1s1 0 2 1l1-1c-1-1-1-1-1-2l7 3c0-2-2-3-2-4 1 0 3 2 3 2 3 2 5 3 8 4l8 4h6l7 2 8-2 5-2c4-2 8-5 12-7v-1c2 0 3-1 4-2z" class="t"></path><path d="M406 333l2-1h1 1 0v4h-5l1-3z" class="T"></path><path d="M391 335c3 0 8-2 11-3v1c0 1-1 1-1 2v1h-14c1 0 2-1 4-1z" class="Z"></path><path d="M583 329l2 1c0 1-1 1-1 2h0c1 1 1 1 1 2l2 1c0 1 0 1-1 1h-28 18c1-1 3 0 5-1h0 1v-3c0-2 0-1 1-2v-1z" class="L"></path><path d="M705 480c2 4 4 9 6 13 0 0 0 1-1 2l-4-8c0-3-3-4-1-7z" class="V"></path><path d="M610 504v1h1c1-1 2-2 2-3 2-2 4-6 6-6-3 5-6 10-10 15l-1-1c1-1 1-2 2-2-1-2-1-3 0-4z" class="a"></path><path d="M629 457l1-1 5 9h0l-1-1v1c0 2 0 3 1 5v3h-1c-1-3-1-8-4-11l-1 1v3l-1-1c0-3 0-5 1-8h0z" class="E"></path><path d="M697 446c1 2 1 3 2 4v1l1 1v-1l2 23h-1c-3-6-2-11-3-17h0l-1-11z" class="D"></path><defs><linearGradient id="n" x1="699.912" y1="434.928" x2="695.567" y2="442.263" xlink:href="#B"><stop offset="0" stop-color="#7a7877"></stop><stop offset="1" stop-color="#969796"></stop></linearGradient></defs><path fill="url(#n)" d="M695 430c1-1 1-3 2-4l1 3 2 22v1l-1-1v-1c-1-1-1-2-2-4h0l-1-11-1-5z"></path><path d="M695 430c1-1 1-3 2-4l1 3c-1 3-1 4-2 6l-1-5z" class="m"></path><defs><linearGradient id="o" x1="426.379" y1="355.932" x2="424.121" y2="368.068" xlink:href="#B"><stop offset="0" stop-color="#0c090c"></stop><stop offset="1" stop-color="#484b48"></stop></linearGradient></defs><path fill="url(#o)" d="M416 353c8 5 17 11 22 19h-1c-2-1-2-1-3-2v1h-1c-2-4-6-7-10-10-3-2-6-4-8-6l1-2z"></path><defs><linearGradient id="p" x1="620.729" y1="606.043" x2="618.64" y2="617.15" xlink:href="#B"><stop offset="0" stop-color="#7b7b7a"></stop><stop offset="1" stop-color="#9e9e9e"></stop></linearGradient></defs><path fill="url(#p)" d="M621 601v1c1 9 2 17-1 25-1 2-1 4-2 5h0l-1-1 2-4v-2-1c1-2 1-4 1-5s-2-2-2-3v-9l-1-4h1c1-1 2-1 3-2z"></path><path d="M621 601v1c0 2 0 3-2 4l-1 1-1-4h1c1-1 2-1 3-2z" class="k"></path><defs><linearGradient id="q" x1="511.053" y1="856.277" x2="522.029" y2="864.414" xlink:href="#B"><stop offset="0" stop-color="#8c8c8c"></stop><stop offset="1" stop-color="#b5b4b3"></stop></linearGradient></defs><path fill="url(#q)" d="M506 842l1 1c1 1 1 1 1 3l4 9c1-1 2-2 3-2 0-1 0-1-1-2v-1-2h0l7 16h0c0 2-1 3-1 4l-1 2h1c1 1 1 3 1 4h0c-6-10-12-20-15-32z"></path><defs><linearGradient id="r" x1="611.443" y1="627.674" x2="617.849" y2="631.803" xlink:href="#B"><stop offset="0" stop-color="#b2b0ae"></stop><stop offset="1" stop-color="#d4d3d2"></stop></linearGradient></defs><path fill="url(#r)" d="M618 616c0 1 2 2 2 3s0 3-1 5v1 2l-2 4 1 1c-1 1-2 3-1 5l-9 6-1 1-2-1v-2c4-2 6-5 8-9 3-5 4-11 5-16z"></path><path d="M629 457h0c-1 3-1 5-1 8l1 1v1 2l-1 1c-1 1 0 2 0 3l1-1 1 1c-1 1-1 2-1 4-2-2-1-3-3-2h-1c1-2 1-4 1-5s0-2-1-2c-2 0-3 1-4 0-2-2-3-5-3-7 1-1 2-1 4-2 2 0 4-1 7-2z" class="F"></path><path d="M623 478l1 1c-1 5-2 10-4 14 0 2-1 2-1 3-2 0-4 4-6 6 0 1-1 2-2 3h-1v-1-2-3c0-2 1-3 2-5v-1c1-2 1-3 2-4s2-1 2-2c-1-3-2-3-4-4h0l2-2h0c2-1 4-1 6-2l3-1z" class="C"></path><path d="M616 487c1 3-1 7-3 10l-3 5v-3c0-2 1-3 2-5v-1c1-2 1-3 2-4s2-1 2-2z" class="b"></path><path d="M623 478l1 1c-1 5-2 10-4 14 0-3 0-4-1-8l-1-1c-1-2-2-2-4-3h0c2-1 4-1 6-2l3-1z" class="p"></path><path d="M605 496c0-4 4-11 7-14v1h0c2 1 3 1 4 4 0 1-1 1-2 2s-1 2-2 4v1c-1 2-2 3-2 5v3 2c-1 1-1 2 0 4-1 0-1 1-2 2l1 1-2 3-3 2c1-1 0-3 0-5-1-5-1-10 0-15h1z" class="H"></path><path d="M604 496h1v1 2c-1 1-1 3-1 4 0 2 1 2 1 3 1 1 1 3 1 4-1 2 0 2 1 4h0l-3 2c1-1 0-3 0-5-1-5-1-10 0-15z" class="h"></path><path d="M609 489c0 1 1 1 0 3 0 2-1 5-1 8h1l1-1v3 2c-1 1-1 2 0 4-1 0-1 1-2 2l-2-3c-2-5 1-13 3-18z" class="K"></path><path d="M612 483c2 1 3 1 4 4 0 1-1 1-2 2s-1 2-2 4v1c-1 2-2 3-2 5l-1 1h-1c0-3 1-6 1-8 1-2 0-2 0-3 0-2 2-4 3-6z" class="s"></path><path d="M597 514c2-2 0-5 0-8 1 3 2 6 4 8l1 3h1c-9 9-25 15-38 15 1-1 1-2 1-3h2c1 0 2-1 4-1h0 1c-1-1-2-1-4-1 2-1 4-2 6-2 1 1 2 0 4-1l8-3c3-1 8-4 10-7z" class="n"></path><path d="M587 523h1v-1h1v1h1c1 0 1 0 2-1h-1l1-1c1 0 2 1 2 1-1 2-4 3-6 4-1 0-1 0-2 1h-2 0v-3h2 0c1-1 0-1 1-1z" class="q"></path><path d="M597 514c2-2 0-5 0-8 1 3 2 6 4 8l1 3h-2-1-1c-1 0-1 0-1 2v1c-1 0-1 0-2-1v1h0v1l-1 1s-1-1-2-1l-1 1h1c-1 1-1 1-2 1h-1v-1h-1v1h-1v-2c3-1 8-4 10-7z" class="m"></path><path d="M592 521l2-1c0-1 0-2 1-2 1-2 3-4 4-4 1 1 1 2 1 3h-1-1c-1 0-1 0-1 2v1c-1 0-1 0-2-1v1h0v1l-1 1s-1-1-2-1zm2-29l2 2v3 2l1 1h0 1c-1 2-1 4-1 6h0c0 3 2 6 0 8-2 3-7 6-10 7l-8 3c-1 0-2 0-2-1l1-1 3-2h-3v-3l1-3c1-1 2 0 2 0v-1-1h1l1 1h1v1l2-2c0-1 1-2 1-3h-3l1-1h3v-1l1-2c0-1 0-2 1-3l1-2 3-6v-2z" class="M"></path><path d="M594 492l2 2v3c-2 2-2 6-3 8l-2-2 2-1c-1-1-1-2-2-2l3-6v-2z" class="V"></path><path d="M596 499l1 1h0 1c-1 2-1 4-1 6h0c0 3 2 6 0 8v-2c-1-1-1-1-1-2l-2 1-2 2h0l3-8c0-2 0-4 1-5v-1z" class="H"></path><path d="M591 500c1 0 1 1 2 2l-2 1 2 2c-1 1-1 3-1 4-1 3-3 5-6 8 1-2 1-5 1-6 1-1 1-1 1-2v-1-1l1-2c0-1 0-2 1-3l1-2z" class="i"></path><path d="M591 500c1 0 1 1 2 2l-2 1 2 2c-1 1-1 3-1 4h-2v-7l1-2z" class="X"></path><path d="M592 513h0l2-2 2-1c0 1 0 1 1 2v2c-2 3-7 6-10 7l-8 3c-1 0-2 0-2-1l1-1 3-2 6-3 5-4z" class="B"></path><path d="M592 513h0l2-2 2-1c0 1 0 1 1 2-3 3-4 5-8 6-1 0-1-1-2-1l5-4z" class="F"></path><defs><linearGradient id="s" x1="545.174" y1="878.951" x2="535.107" y2="897.044" xlink:href="#B"><stop offset="0" stop-color="#b4b4b1"></stop><stop offset="1" stop-color="#e3e3e1"></stop></linearGradient></defs><path fill="url(#s)" d="M521 864c5 9 11 19 19 27 1-1 1-2 2-3l1 2 1 1 4 4c2 2 3 3 6 5 1 2 2 4 4 6 1 0 2 0 3 1l6 3 1 1c2 0 3 1 5 2l4 1s0 1 1 1l-1 1-1-1c-3 0-4-1-6-1-2-1-9-4-10-4v1l-1-1h-1c-5-1-8-4-12-8-10-7-19-17-25-28h0c0-1 0-3-1-4h-1l1-2c0-1 1-2 1-4h0z"></path><path d="M542 888l1 2 1 1 4 4c2 2 3 3 6 5 1 2 2 4 4 6-7-3-13-10-18-15 1-1 1-2 2-3z" class="O"></path><path d="M608 463c0-1 0-1 1-2h3c1-1 1-1 2-1h2l1-1v1l1 1c0 2 1 5 3 7 1 1 2 0 4 0 1 0 1 1 1 2s0 3-1 5l-1 4-1-1-3 1c-2 1-4 1-6 2h0l-2 2v-1c-3 3-7 10-7 14h-1c-1 5-1 10 0 15 0 2 1 4 0 5l-1 1h-1l-1-3c-2-2-3-5-4-8h0c0-2 0-4 1-6v-4c1-1 1-2 2-3h1c1-4 4-11 7-14 1-2 2-4 4-5 1-1 2-1 2-2l1-1 1 1c1 0 2 0 3-1h-1-2c-1-2-1-2 0-3l-1-1h-2v3h-2c-1-1-4 0-5 0h-1v-2h0c1-2 1-4 3-5z" class="L"></path><path d="M620 475h4c0 1 0 2-1 3h0l-3 1h-1v-1h1v-3z" class="E"></path><path d="M612 482c2-3 4-5 8-7v3h-1v1h1c-2 1-4 1-6 2h0l-2 2v-1z" class="F"></path><path d="M619 471l6-1c-5 3-9 6-13 10l-1 1h0c0-2 1-3 2-4v-1h-1-1l-3 3c1-2 2-4 4-5 1-1 2-1 2-2l1-1 1 1c1 0 2 0 3-1z" class="Q"></path><path d="M608 463c3 0 5-1 8-2l2 6h0v3h-1v-1c0-1 0 0-1-1l-1-1h-2v3h-2c-1-1-4 0-5 0h-1v-2h0c1-2 1-4 3-5z" class="W"></path><path d="M608 463c3 0 5-1 8-2l2 6c-1-1-2-1-3-1h-2v-1c-1-1-2-1-4 0-1 0-1 1-2 2v2h0c-1 0-1 0-1 1h-1v-2h0c1-2 1-4 3-5z" class="D"></path><path d="M608 479l3-3h1 1v1c-1 1-2 2-2 4h0c-3 5-5 9-7 15-1 5-1 10 0 15 0 2 1 4 0 5l-1 1h-1l-1-3c-2-2-3-5-4-8h0c0-2 0-4 1-6v-4c1-1 1-2 2-3h1c1-4 4-11 7-14z" class="l"></path><path d="M598 496c1-1 1-2 2-3h1c-1 4-3 12 0 16h0l1 1s0 1 1 2h0l1-1c0 2 1 4 0 5l-1 1h-1l-1-3c-2-2-3-5-4-8h0c0-2 0-4 1-6v-4z" class="E"></path><path d="M601 509l1 1s0 1 1 2h0l1-1c0 2 1 4 0 5l-1 1h-1l-1-3v-1c1-2 0-3 0-4z" class="J"></path><path d="M569 507c0 1-1 1-1 3l2 2v-1-1c1-1 2-2 3-4l1 1c-1 1-2 2-2 3h-1v2h2 3v3l1-2h2v1l-1 3v3h3l-3 2-1 1c0 1 1 1 2 1-2 1-3 2-4 1-2 0-4 1-6 2 2 0 3 0 4 1h-1 0c-2 0-3 1-4 1h-2c0 1 0 2-1 3h-8-5l1-11v-2-1-1c0-3 4-3 6-6h0 0l2-1c2 0 6-2 8-3z" class="p"></path><path d="M576 512v3c-2 1-5 3-6 5s-2 2-3 4v1l-3 1v-1l1-2s1-1 1-2c2-2 4-3 5-5l-2-2 4-2h3z" class="T"></path><path d="M576 515l1-2h2v1l-1 3v3h3l-3 2-7 2-4 1v-1c1-2 2-2 3-4s4-4 6-5z" class="o"></path><path d="M571 524c2-2 4-5 7-7v3h3l-3 2-7 2z" class="S"></path><path d="M569 507c0 1-1 1-1 3l2 2v-1-1c1-1 2-2 3-4l1 1c-1 1-2 2-2 3h-1v2h2l-4 2c-4 1-10 0-13 2-1 2-2 3-3 5v-2-1-1c0-3 4-3 6-6h0 0l2-1c2 0 6-2 8-3z" class="E"></path><path d="M571 524l7-2-1 1c0 1 1 1 2 1-2 1-3 2-4 1-2 0-4 1-6 2 2 0 3 0 4 1h-1 0c-2 0-3 1-4 1h-2c0 1 0 2-1 3h-8v-5h2c2-1 3-1 5-1l3-1 4-1z" class="L"></path><path d="M571 524l7-2-1 1c0 1 1 1 2 1-2 1-3 2-4 1-2 0-4 1-6 2v1c-1 0-1-1-1-1-1 0-2 0-3 1h0l-2-1-1 1h-3-1l1-1c2-1 3-1 5-1l3-1 4-1z" class="C"></path><path d="M594 315l6 3 1 1h-1l2 1c1 1 3 3 4 5v1l-1 1v-1 1h4v2h-1v1c0 2-1 3-1 5l1 1h-19-3c1 0 1 0 1-1l-2-1c0-1 0-1-1-2h0c0-1 1-1 1-2l-2-1v-1l-1 1c-1-4-4-8-7-10h0l7-2h1l1-1c1-1 0-1 1-1s2 1 3 0c2-1 3 1 6 0z" class="e"></path><path d="M600 331h1v1c-2 1-2 1-4 3h0c-2 0-4 0-6-1l2-3 1 1c1 1 1 1 3 1l3-2z" class="C"></path><path d="M601 332v1c3 1 5-2 7-3 0 2-1 3-1 5h-10c2-2 2-2 4-3z" class="V"></path><path d="M584 325h5l-3 5h1c1-1 1-2 2-3h0c0 2 0 2-1 4v1h1l2-1h0l-1 1c-1 1-1 3-1 4h-3c1 0 1 0 1-1l-2-1c0-1 0-1-1-2h0c0-1 1-1 1-2l-2-1v-1l1-3z" class="S"></path><path d="M594 327l4-2h1c1-1 1 0 2-1v3h1l1-1h2v1h4v2h-1v1c-2 1-4 4-7 3v-1-1h-1l-3 2c-2 0-2 0-3-1l-1-1c-1 0 0 0-1-1 1-1 2-1 2-2v-1z" class="h"></path><path d="M594 332c1-2 4-4 6-5 1 0 1 1 2 1-1 1-2 1-3 2 0 1 0 1 1 1l-3 2c-2 0-2 0-3-1z" class="H"></path><path d="M602 328l1 1h0 5v1c-2 1-4 4-7 3v-1-1h-1c-1 0-1 0-1-1 1-1 2-1 3-2z" class="D"></path><path d="M594 315l6 3 1 1h-1l2 1c1 1 3 3 4 5v1l-1 1v-1h-2l-1 1h-1v-3c-1 1-1 0-2 1h-1l-4 2c0-1 1-1 1-2h-3-3-5l-1 3-1 1c-1-4-4-8-7-10h0l7-2h1l1-1c1-1 0-1 1-1s2 1 3 0c2-1 3 1 6 0z" class="g"></path><path d="M594 315l6 3 1 1h-1l-3-1c-4-1-9-1-14-1l1-1c1-1 0-1 1-1s2 1 3 0c2-1 3 1 6 0z" class="V"></path><path d="M597 318l3 1 2 1c1 1 3 3 4 5v1l-1 1v-1h-2l-1 1h-1v-3c-1 1-1 0-2 1h-1l-4 2c0-1 1-1 1-2h-3-3-5l1-1c1-1 2-1 3-2h0 2c1 0 2 0 2-1h1v1c1-1 2-1 3 0h0 2 0l1-1-2-3z" class="N"></path><path d="M570 471l1 1 6 2c2 1 5 2 7 5-1 1-1 2-1 3 1 1 1 1 1 2s-1 3-1 5l-1 1 1 4h3 1v1s-1 1-1 2l-1 1c1 1 0 1 2 1 0 0 0 1 1 1l-1 2c1 1 1 2 2 3l-1 2v1h-3l-1 1h3c0 1-1 2-1 3l-2 2v-1h-1l-1-1h-1v1 1s-1-1-2 0v-1h-2l-1 2v-3h-3-2v-2h1c0-1 1-2 2-3l-1-1c-1 2-2 3-3 4v1 1l-2-2c0-2 1-2 1-3-2 1-6 3-8 3l1-1c3-1 6-3 8-5h-1v-2c-1-3 2-4 4-6l-1-1-1-1c0 1-1 1-1 2h-1v-1c-2-3-4-4-7-6l-3-2h0c1-1 1-2 1-3h0l2-1h0l-5-1-1-1c5-3 9-6 14-10z" class="P"></path><path d="M575 488h1c1 0 1 1 2 2l-1 1-2 1c0-1 0-1-1-2l1-2z" class="g"></path><path d="M562 483c1-1 2-1 4-1 1 1 1 1 2 1s2 1 3 1c2 0 3 1 4 2v1h-1-3c-1-1-1-2-2-3l-2 1h-1c-1-1-3-2-4-2h0z" class="K"></path><path d="M575 475c2 1 3 1 3 3 0 1-1 1-1 3 1 0 1 0 1 1l-1 1c-1 1-2 1-2 3-1-1-2-2-4-2-1 0-2-1-3-1l1-1c0-2 5-5 6-7z" class="Z"></path><path d="M570 471l1 1 6 2-2 1c-1 2-6 5-6 7l-1 1c-1 0-1 0-2-1-2 0-3 0-4 1l-5-1-1-1c5-3 9-6 14-10z" class="s"></path><path d="M562 483c1 0 3 1 4 2h1c4 4 7 8 10 13-2 2-4 5-7 6h-1v-2c-1-3 2-4 4-6l-1-1-1-1c0 1-1 1-1 2h-1v-1c-2-3-4-4-7-6l-3-2h0c1-1 1-2 1-3h0l2-1z" class="J"></path><path d="M562 483c1 0 3 1 4 2s1 3 1 4l2 1c0 1 0 1-1 2v-1c-1 0-1 0-2-1 0 0-1-1-1-2l-2-2-1 3-3-2h0c1-1 1-2 1-3h0l2-1z" class="Q"></path><path d="M583 482c1 1 1 1 1 2s-1 3-1 5l-1 1 1 4h3 1v1s-1 1-1 2l-1 1c1 1 0 1 2 1 0 0 0 1 1 1l-1 2c1 1 1 2 2 3l-1 2v1h-3l-1 1h3c0 1-1 2-1 3l-2 2v-1h-1l-1-1h-1v1 1s-1-1-2 0v-1h-2l-1 2v-3h-3-2v-2h1c0-1 1-2 2-3l-1-1c-1 2-2 3-3 4v1 1l-2-2c0-2 1-2 1-3l8-7c1-1 4-6 4-8-1 1-1 1-2 1 0-1 2-6 3-8 1-1 1-1 1-3z" class="X"></path><path d="M581 508c1 0 2 0 2 1v4l-1-1h-1v1 1s-1-1-2 0v-1h-2l-1 2v-3h0c2-1 4-3 5-4z" class="U"></path><path d="M582 490l1 4h3 1v1s-1 1-1 2l-1 1c1 1 0 1 2 1 0 0 0 1 1 1l-1 2-2 2v-2-1l-1-1s-1-1-1-2h0l1-1v-1h-1-1c0-2-1-4 0-6z" class="H"></path><path d="M587 502c1 1 1 2 2 3l-1 2v1h-3l-1 1h3c0 1-1 2-1 3l-2 2v-1h-1v-4c0-1-1-1-2-1l1-1v-2l2 1 1-1v-1l2-2z" class="R"></path><path d="M587 502c1 1 1 2 2 3l-1 2h-2v-2h-1v-1l2-2z" class="a"></path><path d="M646 309v3h7v1h5l2 1 7 4c1 0 1 1 2 1 2 2 5 5 6 7l1 2 2 6 1 1 2 1h-11-31v-1-4l1-10-1-3-1-4 2 2h0l1-1c2-1 4-5 5-6z" class="g"></path><path d="M654 331h-1c-1-1-1-2-3-3v2h-1v-2c0-1 1-1 1-2l1 1 2-1v1c1 1 1 0 2 1 0 1 0 1 1 2-1 1 0 1-2 1h0z" class="P"></path><path d="M650 326c4-1 6 0 9 2l4 4s-1 0-1 1l-6-3c-1-1-1-1-1-2-1-1-1 0-2-1v-1l-2 1-1-1z" class="R"></path><path d="M656 330l6 3c0-1 1-1 1-1h0 1c1 1 1 2 2 3h4v1h-31v-1h16 1c-1-3-1-3-2-4h0c2 0 1 0 2-1z" class="U"></path><path d="M646 309v3h7v1h5l-1 1h-5l-1 1 1 1h1c1 0 1 0 2 1 0 1-1 2-1 2 1 1 0 1 1 1 2 0 3 1 4 1v3h0l-1-1c-6-2-10-1-15 1l3-3c-1 0-2 0-3 1v-1l2-2h0l-3 1c-1 1-1 1-2 1l-1-3-1-4 2 2h0l1-1c2-1 4-5 5-6z" class="Q"></path><path d="M646 309v3h7v1h-7l-1 2 1 1c-2 1-3 1-4 3v1c-1 1-1 1-2 1l-1-3-1-4 2 2h0l1-1c2-1 4-5 5-6z" class="K"></path><path d="M658 313l2 1 7 4c1 0 1 1 2 1 2 2 5 5 6 7l1 2 2 6 1 1 2 1h-11v-1h-4c-1-1-1-2-2-3h-1 2v-1l-1-1c-2-2-4-4-5-6h0v-3c-1 0-2-1-4-1-1 0 0 0-1-1 0 0 1-1 1-2-1-1-1-1-2-1h-1l-1-1 1-1h5l1-1z" class="E"></path><path d="M659 324a19.81 19.81 0 0 1 11 11h-4c-1-1-1-2-2-3h-1 2v-1l-1-1c-2-2-4-4-5-6h0z" class="b"></path><path d="M679 335c-2 1-3 1-5 1-1-1-1-2-2-3v-1l-1-2-1-1c0-1-1-1-1-1-1-3-4-5-6-7-1-1-2-1-3-2v-1c7 0 9 9 14 13 1 1 3 2 4 3l1 1z" class="Q"></path><path d="M428 851l3-6h2c8 1 16 2 23 5 21 7 35 21 50 35 17 14 37 27 59 31v-1l-1-1-6-4h1l1 1v-1c1 0 8 3 10 4 2 0 3 1 6 1l1 1 1-1c-1 0-1-1-1-1 2 0 4 1 7 2 4 0 8 0 12 1 3-1 7-1 10-2-3 2-6 4-10 5-3 1-6 2-10 3-3 2-22-3-27-4-11-4-22-8-32-15-9-6-18-13-27-21-7-6-13-12-20-17-12-8-29-18-43-17-2 0-4 0-5 1-1 0-2 1-4 1h0z" class="s"></path><path d="M558 910h1l1 1v-1c1 0 8 3 10 4 2 0 3 1 6 1l1 1 1-1c-1 0-1-1-1-1 2 0 4 1 7 2 4 0 8 0 12 1-6 3-24 1-31-1v-1l-1-1-6-4z" class="J"></path><path d="M611 442l2 5c0 1 1 2 1 3v1c1 2 3 6 3 8l-1 1h-2c-1 0-1 0-2 1h-3c-1 1-1 1-1 2-2 1-2 3-3 5h0v2h1c1 0 4-1 5 0h2v-3h2l1 1c-1 1-1 1 0 3h2 1c-1 1-2 1-3 1l-1-1-1 1c0 1-1 1-2 2-2 1-3 3-4 5-3 3-6 10-7 14h-1c-1 1-1 2-2 3v4h-1 0l-1-1v-2-3l-2-2v2l-3 6-1 2c-1 1-1 2-1 3-1-1-1-2-2-3l1-2c-1 0-1-1-1-1-2 0-1 0-2-1l1-1c0-1 1-2 1-2v-1h-1-3l-1-4 1-1 5-8c0-1 1-2 1-3l3-7 2-7 2-7-1-1 1-1c2-4 3-5 6-9h1 1l1-2h4 1c1-1 1-1 1-2z" class="V"></path><path d="M595 479v-1c1-2 1-4 2-5 1-2 1-4 3-6h1c1 2 0 3 0 4l-2 2 1 1c-1 1-1 2-1 2h-1l-3 3z" class="D"></path><path d="M601 478l1-1 2-3h1c1 0 1 1 1 1 0 1 0 1 1 2l-1 1c-3 4-5 10-6 15-1 1-1 2-2 3v4h-1 0l-1-1v-2-3l-2-2c1-1 1 0 2-2l-1-1c1-2 1-2 3-3 0-1 0-2 1-3h0l2-5z" class="c"></path><path d="M594 492c1-1 1 0 2-2l-1-1c1-2 1-2 3-3-1 3-1 6 0 10v4h-1 0l-1-1v-2-3l-2-2z" class="G"></path><path d="M592 471h3l-5 12c1 1 1 1 2 1l1-1c0-1 0-2 1-3l1-1 3-3h1l-2 3 1 1c-1 1-1 1-1 2v1c1-1 3-5 3-5l1-1v1l-2 5h0c-1 1-1 2-1 3-2 1-2 1-3 3l1 1c-1 2-1 1-2 2v2l-3 6-1 2c-1 1-1 2-1 3-1-1-1-2-2-3l1-2c-1 0-1-1-1-1-2 0-1 0-2-1l1-1c0-1 1-2 1-2v-1h-1-3l-1-4 1-1 5-8c0-1 1-2 1-3l3-7z" class="I"></path><path d="M597 483c1-1 3-5 3-5l1-1v1l-2 5h0c-1 1-1 2-1 3-2 1-2 1-3 3l1 1c-1 2-1 1-2 2v2l-3 6-1 2c-1 1-1 2-1 3-1-1-1-2-2-3l1-2 5-9 4-8z" class="Z"></path><path d="M592 471h3l-5 12c0 2-2 5-3 6v1 1l-1 1c-1 0-1-1-2-1l1-3c1-1 1-2 2-3v-1c0-1 1-1 1-2v-1c0-1 1-2 1-3l3-7z" class="J"></path><path d="M611 442l2 5c0 1 1 2 1 3v1c1 2 3 6 3 8l-1 1h-2c-1 0-1 0-2 1h-3c-1 1-1 1-1 2-2 1-2 3-3 5 0-3 0-4 1-6-1 0-1 1-2 1l-2 2v-1-3l-1-1-1 1c0 2 0 2-1 3h-2l-1 4c0 1 0 2-1 3h-3l2-7 2-7-1-1 1-1c2-4 3-5 6-9h1 1l1-2h4 1c1-1 1-1 1-2z" class="N"></path><path d="M611 442l2 5c0 1 1 2 1 3v1l-1 1 1 2-1 1-1-1c0-1-1-2-1-3 1-1 2 0 2-2h-1c-1 1-2 1-3 1v-2h-1l1-1-1-1-1 1c-1 0-1 0-2-1v1l-1 1-2-2h1 1l1-2h4 1c1-1 1-1 1-2z" class="o"></path><path d="M596 457h0c1-1 2-2 2-3h1c1-1 2-3 3-4h1 4 0v5h1 1c0 1 0 1-1 1-1 2 0 2-1 4 0 0 0 2-1 2s-1 1-2 1l-2 2v-1-3l-1-1-1 1c0 2 0 2-1 3h-2l-1 4c0 1 0 2-1 3h-3l2-7 2-7z" class="X"></path><path d="M601 460c2-1 2-1 4-1v3l-1 1-2 2v-1-3l-1-1z" class="D"></path><path d="M594 464l3-3h1l-1 3-1 4c0 1 0 2-1 3h-3l2-7z" class="F"></path><path d="M450 400l1 5c1 4 1 8 1 12v20 68c0 10 0 22 1 32-3-2-5-4-7-5 1-2 0-4 0-6v-12h0c1-5 0-10 0-15v-33c0-1 0-2-1-3h-1 0c0 2 0 3-1 4v-5-1c1-2 1-5-1-7s-2-2-2-5l1-1-1-1c1-1 1-2 1-2 1-1 1-1 1-2 2-4-3-8-3-11l2 2h1 1v2c1 1 2 1 2 3h0v-1c2-5 1-12 1-18v-1c0-5 0-11-2-17l6-2z" class="d"></path><path d="M446 419v47c0-1 0-2-1-3h-1 0c0 2 0 3-1 4v-5-1c1-2 1-5-1-7s-2-2-2-5l1-1-1-1c1-1 1-2 1-2 1-1 1-1 1-2 2-4-3-8-3-11l2 2h1 1v2c1 1 2 1 2 3h0v-1c2-5 1-12 1-18v-1z" class="a"></path><path d="M439 432l2 2h1 1v2c1 2 2 4 2 7h-1c-1 1-1 1 0 2-1 1-1 2-2 3h1c-1 2-2 1-2 3h1 1v3h-1c-2-2-2-2-2-5l1-1-1-1c1-1 1-2 1-2 1-1 1-1 1-2 2-4-3-8-3-11zm62-59c4 3 6 9 6 14 1 12 0 26 0 38v75 23c0 3 0 7 1 10-2 2-5 3-7 5V373z" class="m"></path><path d="M627 295c1 1 1 2 1 3 1 1 2 2 3 4h1c0 2 1 3 2 4h-1v-1c-1-1-2-1-3-2h-2c-1 1-1 1-1 3 1 0 1 0 2 1 1 0 2 1 3 1 1 1 2 1 3 2l3 3v1h0l1 4 1 3-1 10v4 1h-8-3-4-6-2-3-5l-1-1c0-2 1-3 1-5v-1h1v-2h-4v-1 1l1-1v-1c-1-2-3-4-4-5l1-1c1 0 2 1 3 1 0 1 1 1 1 2v-2c0-4-3-9-5-12-1-1-3-2-4-3v-1h1v-2h1 1v-1c2-2 3-2 5-3 2 0 3-1 6-1v1h5c0 1 1 1 2 1 0-1 2-2 3-3h1l1 1 2-1 1-1z" class="M"></path><path d="M611 306l2-1v4c1 1 1 0 1 2h-1v-1c0-1 0 0-1-1l-1-1v-2z" class="C"></path><path d="M608 318l4-2v1c0 1 0 2 1 3l-1 1h-1l-2 1-1-4z" class="X"></path><path d="M618 306c0-2 0-5 2-6 1 0 2 1 3 1l-1 2v1c-1 0-3 1-4 2z" class="W"></path><path d="M625 304v-3c-1 0-1 0-2-1v-2h1l2-1 2 1c1 1 2 2 3 4h1c0 2 1 3 2 4h-1v-1c-1-1-2-1-3-2h-2l-1-1-2 2z" class="T"></path><path d="M622 304v-1c1 0 1 1 1 2 1 2 0 5 0 7-1 3-1 6-2 9-2 5-3 10-5 15h-3l-1-1c-1-1 0-3 0-4l2 1h1l1-1c2-5 3-10 4-16 0-1 1-3 1-4 0-3 1-5 1-7z" class="B"></path><path d="M618 306c1-1 3-2 4-2 0 2-1 4-1 7 0 1-1 3-1 4-1 6-2 11-4 16l-1 1h-1l-2-1 3-11c2-5 2-10 3-14z" class="Q"></path><path d="M615 320l1 3c0 3-1 4-2 8v1l-2-1 3-11z" class="Y"></path><path d="M600 302l3 1c1-1 1-1 1-3h2 1 0c1 0 1 0 2-1h1l1 2h0c1 0 2 0 2 1v3l-2 1v2l1 1v7h0l-4 2c-1-4-3-10-7-13-1-1-1-1-2-1v-2h1z" class="I"></path><path d="M612 316l-2-2v-1-1h-1c1-1 1-1 1-2h0l1-2 1 1v7z" class="H"></path><path d="M600 302l3 1c1-1 1-1 1-3h2 1 0c1 0 1 0 2-1h1l1 2h0c1 0 2 0 2 1v3l-2 1c-1-1-1-1 0-2 0-2 0-2-2-3-1 0-1 1-2 2 1 0 1 0 2 1v3c-1-1-1-1-1-2-1 0-2-1-2-2-1 0-1 1-1 1-1 0-1 0-2 1h0-2c-1-1-1-1-2-1v-2h1z" class="h"></path><path d="M625 304l2-2 1 1c-1 1-1 1-1 3 1 0 1 0 2 1 1 0 2 1 3 1 1 1 2 1 3 2l3 3v1h0l1 4 1 3-1 10v4 1h-8-3-4-6c0-1 1-1 1-2 1-1 0-1 1-2 0-1 0-2 1-3 1-8 4-16 4-25z" class="I"></path><path d="M622 328c1-1 1-2 2-3l1 1h2 3v-4-2c0-1-1-1-1-2h-1c1-1 2-1 3-1v-1c1-2 1-4 1-6l1 1 1 1h0v2l-1 1c0 4 0 10-1 14v3l-3-1v-3h-7z" class="E"></path><path d="M622 328h7v3l3 1-1 4h-3-4-6c0-1 1-1 1-2 1-1 0-1 1-2 0-1 0-2 1-3h0l1-1z" class="N"></path><path d="M629 331l3 1-1 4h-3l1-5z" class="F"></path><path d="M621 329h0c1 1 2 1 2 1l2-1 1 2-2 5h-6c0-1 1-1 1-2 1-1 0-1 1-2 0-1 0-2 1-3z" class="G"></path><path d="M635 310l3 3v1h0l1 4 1 3-1 10v4 1h-8l1-4v-3c1-4 1-10 1-14l1-1v-2h0l-1-1 2-1z" class="B"></path><path d="M635 320v-7c1-1 2 0 3 1h0l1 4c-1 1-3 1-4 2z" class="Y"></path><path d="M635 320c1-1 3-1 4-2l1 3-1 10h-1v-1h-1l-1 1h-1c1-4 0-7 0-11z" class="Q"></path><defs><linearGradient id="t" x1="467.208" y1="912.574" x2="525.518" y2="866.714" xlink:href="#B"><stop offset="0" stop-color="#4d4f4d"></stop><stop offset="1" stop-color="#7f7e7e"></stop></linearGradient></defs><path fill="url(#t)" d="M428 851h0c2 0 3-1 4-1 1-1 3-1 5-1 14-1 31 9 43 17 7 5 13 11 20 17 9 8 18 15 27 21 10 7 21 11 32 15v1c2 1 2 0 4 1h0c2 1 4 1 6 1v2h-1c-1-1 0 0-1 0-2 0-5 0-6-1h-1-2s-1 1 0 1c-2 0-4-1-6-1l1-1c-12-4-23-8-34-14-8-4-16-11-23-17-9-8-18-16-28-22l-9-6-14-5c-2 0-4-1-7 0h0c-5 0-11 0-16 1l2-4 4-4z"></path><path d="M459 863h0l-2-2h1 1l3 1c2 1 2 2 4 3v-1l-1-1c1-1 2-1 3 0v1 1h1 0c1-1 2-1 3-1h0l1 1c-1 1-2 0-3 1-1 0-2 2-2 3l-9-6z" class="j"></path><path d="M452 579c2 13 1 28 1 42v61 87 25c0 4 0 10-1 15h-1c0 3-2 5-4 7l-2-1-3 8-3-1v-1-1c-1-1-1-1-1-2v-8c1-5 5-8 9-11v-3-13-29-9-17-25-5-19-15c0-2 1-5-1-7h1v-40-11c0-6 0-12 1-19l2-1c1-1 1-2 1-3l1 1h0v-5z" class="J"></path><path d="M447 799c0 5 0 11-2 16l-3 8-3-1v-1-1c-1-1-1-1-1-2v-8c1-5 5-8 9-11z" class="p"></path><defs><linearGradient id="u" x1="500.969" y1="710.709" x2="515.517" y2="710.813" xlink:href="#B"><stop offset="0" stop-color="#565555"></stop><stop offset="1" stop-color="#828381"></stop></linearGradient></defs><path fill="url(#u)" d="M501 564c3 2 6 8 6 11 1 5 0 10 0 15v33 130 41c0 8 0 17 1 26s3 19 6 28h0v2 1c1 1 1 1 1 2-1 0-2 1-3 2l-4-9c0-2 0-2-1-3l-1-1c-2-6-4-14-4-21-1-13-1-25-1-38v-57-162z"></path><path d="M560 281l4-1h0l-1 2c2 1 2-1 3 0l1 1-1 1s-1 1-1 2l1 1-1 1h3 0l-2 2c1 1 3 1 4 2h2c1 0 3 1 4 0h2c3 0 5 1 7 1l1 2c-2 0-4 0-5-1-3-1-7 0-9 0l-1 1c2 1 6 0 8 1h2c4 0 7 2 10 3 1 0 1 0 1 1l1 1 2 1 3 2v1c1 1 3 2 4 3 2 3 5 8 5 12v2c0-1-1-1-1-2-1 0-2-1-3-1l-1 1-2-1h1l-1-1-6-3c-3 1-4-1-6 0-1 1-2 0-3 0s0 0-1 1l-1 1h-1l-7 2h0c3 2 6 6 7 10l1-1v1 1c-1 1-1 0-1 2v3h-1 0c-2 1-4 0-5 1h-18-106-23-14-5v-4h0-1-1l-2 1-1 1c-1 0-1 1-2 2h-2v-1c0-1 1-1 1-2v-1c-3 1-8 3-11 3 7-4 14-9 21-14 3-2 5-4 8-6s7-4 10-6h-3v-1l1-1c2-1 2 0 4-1h-1c1-1 3-1 4-1l-2-1c1-2 3-2 5-3h5l3-1 6-2c8-3 14-4 22-4l1-1h-5l1-1h0 1c2 0 3-1 4-2h0-5l1-1c0-1-1-2-1-3h5c-1-1-1-1-2-1l2-1h0v-1l-2-1c2 0 4 1 6 1 2 1 4 2 6 2s4 0 5 2l1-1s1 0 2 1l1-1c-1-1-1-1-1-2l7 3c0-2-2-3-2-4 1 0 3 2 3 2 3 2 5 3 8 4l8 4h6l7 2 8-2 5-2c4-2 8-5 12-7v-1c2 0 3-1 4-2z" class="s"></path><path d="M521 330l1-1h3v1l2 2h-1-2c-1 0-2-1-3-2z" class="K"></path><path d="M469 305h2 2c-1-2-1-2-2-2v-1c2 0 3 1 4 2h0c1 2 3 4 4 6-3-1-5-3-8-3l-2-2z" class="X"></path><path d="M524 295h3c-2 1-3 2-6 2-1 1-2 1-2 2s0 1 1 2h-1 0-3-3v-1h2l-2-2c2 1 2 1 2 2h2c0-1-1-1-1-2h0 1c1-1-1-1 0-2 1 0 3-1 4 0h1l2-1z" class="T"></path><path d="M488 309h-1c-1-1-1-1-1-2 1-1 2-1 3-2l3 3 1-1h2c0 1 1 2 2 2l1-2c1 1 2 2 2 3h-1-1l4 4-8-3c-1-1-1-1-2-1-1-1-2-2-4-2v1z" class="n"></path><path d="M535 326c2-1 4-3 6-3h1c1-1 2-1 3-1l-4 2v1c2 0 3-2 5-1l-1 1h0c-1 1-1 1-3 1l-1 1-1 1s-1 0-1 1c-2 1-4 0-5 0s-1-1-1-2c1 0 1 0 2-1z" class="g"></path><path d="M558 317l-3 4h1c1 0 1 0 2 1h3 5 0 0c-3 1-6 0-9 1-3 0-6 1-8 2h-4 0l1-1c-2-1-3 1-5 1v-1l4-2h2c3-2 7-4 11-5z" class="X"></path><path d="M561 328c3-1 8-1 10-1l7 1h2l1 1h1l1-1v1 1c-1 1-1 0-1 2v3h-1 0c-2 1-4 0-5 1l1-2 1-1 1 1h1c0-2-1-2-2-4h-4c-4-1-8-1-12-2h-1z" class="K"></path><path d="M473 298l-2-1h1c1 1 3 1 5 1 4 2 9 5 12 7-1 1-2 1-3 2 0 1 0 1 1 2h1c1 1 2 2 3 4l-8-5 4 6c-2-2-4-3-6-5l1-1c-1-1-2-2-3-4h1l-1-1 1-1c1 1 1 1 3 1-1-2-4-3-6-4l-4-1z" class="i"></path><path d="M488 296h2c1 0 2 1 3 0h1l2 2v2h0-2 0l1 1 8 8-7-4c1 1 1 2 2 2l-1 2c-1 0-2-1-2-2h0v-1c-3-2-6-6-9-7v-2c1 0 1 0 2 1h1 0c0-1-1-1-1-2h0z" class="H"></path><path d="M473 298l4 1c2 1 5 2 6 4-2 0-2 0-3-1l-1 1 1 1h-1c1 2 2 3 3 4l-1 1c-2-2-3-4-6-5-1-1-2-2-4-2v1c1 0 1 0 2 2h-2-2 0c-1 0-1-1-2-1-1-1-1 0-2-1 1 0 1 0 2-1h-1v-1l-1-1c2-2 5 0 8-2z" class="I"></path><path d="M473 298l4 1c-2 1-3 1-5 1-2 1-3 1-6 1l-1-1c2-2 5 0 8-2z" class="F"></path><path d="M561 324c4-1 7-1 11 0h1c2 0 3 1 5 2 0 1 1 2 2 2h-2l-7-1c-2 0-7 0-10 1 0 1-1 1-2 1v1l-1-1 1-1h-2c-2-1-3-1-4-2l-1-1 5-1c2 0 3-1 4 0z" class="H"></path><path d="M557 324c2 0 3-1 4 0l1 1h1v1l-2 1h-2c-1-1-1-2-2-3z" class="i"></path><path d="M552 325l5-1c1 1 1 2 2 3h2 10 0c-2 0-7 0-10 1 0 1-1 1-2 1v1l-1-1 1-1h-2c-2-1-3-1-4-2l-1-1z" class="M"></path><path d="M543 328h-1c1-1 0-1 1-1s2 0 3-1c1 0 1 0 2-1 2 0 3-1 4 0l1 1c1 1 2 1 4 2h2l-1 1c-1 1-2 1-3 2h3 1l1-1 1 1c-1 1-2 1-3 2-1-1-2-1-3-1-2 0-3 1-5 0h0c-2 1-3 1-4 1h-1-1c-1 0-2 1-2 1h-1-2 0l1-1c1 0 1 0 2-1h1v-1h-1l-1-1 3-1-1-1z" class="b"></path><path d="M543 328h-1c1-1 0-1 1-1s2 0 3-1c1 0 1 0 2-1 2 0 3-1 4 0l1 1c1 1 2 1 4 2h-1 0c-3 0-10-1-13 0z" class="k"></path><path d="M558 317h3c2 1 4 1 6 1l1-1h2l1 1 4 1h0c3 2 6 6 7 10h-1l-1-1c-1 0-2-1-2-2-2-1-3-2-5-2-2-1-4-1-7-2h0-5-3c-1-1-1-1-2-1h-1l3-4z" class="G"></path><path d="M558 317h3c2 1 4 1 6 1l1-1h2l1 1 4 1h0-1-3-12v1l-1-1v1 1h1c1 0 1 1 2 1h-3c-1-1-1-1-2-1h-1l3-4z" class="H"></path><path d="M556 284c0 1-1 2-1 3v1c-2 2-6 3-6 6l-2 2h1c2-1 3-2 5-2l-1 1c-3 1-6 4-8 6h0c1 1 1 0 1 1-2 1-4 3-4 4 1 0 2-1 3-2 2-2 5-3 7-3l-5 3c-3 1-6 5-10 6v2h-1c-1 0-1 1-2 2v-1c-1 1-1 1-2 1h0l1-1c1-1 0-1 1-1l1-1c1-1 2-1 2-2h0c1-1 1-2 1-3-1 2-3 4-4 5h-1c1-3 4-5 5-8h-1v-1c2-2 4-4 7-6 1-1 1-2 1-3h-5l5-2c4-2 8-5 12-7z" class="b"></path><path d="M544 291h1c1 1 2 0 3 0 0 1-2 3-3 4l-2 1c1-1 1-2 1-3h-5l5-2z" class="f"></path><path d="M428 319c2-2 5-3 8-4l1 1c0 1 0 2 1 3 1 2 1 7 1 10v6h1c3 0 9 0 12 1h-23v-1c1-1 2-2 3-4h0c0-1 0-1-1-1 0-4 0-7-2-10l-1-1z" class="N"></path><path d="M429 320l2-1h1c1 1 0 1 1 1l-1 1v-1h-1c0 2 0 3 1 4l2 2 1 1c1 1 2 3 3 4l1 3v1h1c3 0 9 0 12 1h-23v-1c1-1 2-2 3-4h0c0-1 0-1-1-1 0-4 0-7-2-10z" class="O"></path><path d="M440 335h-4l-2-2h0c1-2 2-2 4-2l1 3v1h1z" class="K"></path><defs><linearGradient id="v" x1="481.935" y1="311.389" x2="460.831" y2="289.841" xlink:href="#B"><stop offset="0" stop-color="#b7b5b0"></stop><stop offset="1" stop-color="#d6d5d1"></stop></linearGradient></defs><path fill="url(#v)" d="M452 298c8-3 14-4 22-4 1 1 2 1 4 1 1 0 1 0 2 1l6 3h0c3 1 6 5 9 7v1h0-2l-1 1-3-3c-3-2-8-5-12-7-2 0-4 0-5-1h-1l2 1c-3 2-6 0-8 2-1 0-3-1-4 0-3 0-5 1-8 1v-1h1v-1h-1l-1-1z"></path><path d="M560 281l4-1h0l-1 2c2 1 2-1 3 0l1 1-1 1s-1 1-1 2l1 1-1 1h3 0l-2 2c1 1 3 1 4 2l-4 1c-2 1-5 1-8 2h-1v-1c-5 2-8 6-12 8 0-1 0 0-1-1h0c2-2 5-5 8-6l1-1c-2 0-3 1-5 2h-1l2-2c0-3 4-4 6-6v-1c0-1 1-2 1-3v-1c2 0 3-1 4-2z" class="L"></path><path d="M557 294l4-2c-1-1-2 0-3 0-1-1-1-1-2-1l-1-1 1-1c1 0 1-1 2-1l1-1 1-1h1v-1h-1c1-1 2-1 3-2h3v-1l1 1-1 1s-1 1-1 2l1 1-1 1h3 0l-2 2c1 1 3 1 4 2l-4 1c-2 1-5 1-8 2h-1v-1z" class="V"></path><defs><linearGradient id="w" x1="453.496" y1="308.572" x2="449.367" y2="299.442" xlink:href="#B"><stop offset="0" stop-color="#b5b3b2"></stop><stop offset="1" stop-color="#d7d5cf"></stop></linearGradient></defs><path fill="url(#w)" d="M446 300l6-2 1 1h1v1h-1v1c3 0 5-1 8-1 1-1 3 0 4 0l1 1v1h1c-1 1-1 1-2 1 1 1 1 0 2 1 1 0 1 1 2 1h0l2 2h-6l-23 5v-1l-1-1 1-1h2l-1-1c1 0 2-1 3-1v-1c-1 0-2 0-2 1h-3l-2-2h-4l-2-1c1-2 3-2 5-3h5l3-1z"></path><path d="M444 309c2-1 4-3 7-2h0-1l-2 2-6 2-1-1 1-1h2z" class="B"></path><path d="M469 305h0l2 2h-6c-1 0-2-1-3-1l7-1z" class="i"></path><path d="M448 309c5-1 9-3 14-3 1 0 2 1 3 1l-23 5v-1l6-2z" class="n"></path><path d="M446 300l6-2 1 1h1v1h-1c-5 0-8 3-12 3h-1-3l1 1h1v1h-4l-2-1c1-2 3-2 5-3h5l3-1z" class="W"></path><path d="M578 292c3 0 5 1 7 1l1 2c-2 0-4 0-5-1-3-1-7 0-9 0l-1 1c2 1 6 0 8 1h2c4 0 7 2 10 3 1 0 1 0 1 1l1 1 2 1 3 2v1c0 1 0 1-2 2-1 0-2-1-3-2-1 0-2-1-4-2-1 0-3-1-4-2h-1c-1 0-2-1-3-1l-6-1c-5 1-10 1-14 3-2 1-3 2-5 3-2 0-7 4-8 3l7-6h-2c1 0 1-1 1-1l-7 4-1-1 5-3c-2 0-5 1-7 3-1 1-2 2-3 2 0-1 2-3 4-4 4-2 7-6 12-8v1h1c3-1 6-1 8-2l4-1h2c1 0 3 1 4 0h2z" class="X"></path><path d="M561 302l-1-2h-1v-1c1-1 2-1 3-2h0c3-1 6-1 9-1l-2 1v1h1c1 0 2 0 3 1h2 0c-5 1-10 1-14 3z" class="W"></path><path d="M570 292h2c-3 2-5 1-8 2l-2 1c-1 0-4 1-5 1-2 1-3 4-6 5-2 0-5 1-7 3-1 1-2 2-3 2 0-1 2-3 4-4 4-2 7-6 12-8v1h1c3-1 6-1 8-2l4-1z" class="M"></path><path d="M571 296c8 0 14 1 21 4l1 1 2 1 3 2v1c0 1 0 1-2 2-1 0-2-1-3-2-1 0-2-1-4-2-1 0-3-1-4-2h-1c-1 0-2-1-3-1l-6-1h0-2c-1-1-2-1-3-1h-1v-1l2-1z" class="c"></path><path d="M546 304l1 1 7-4s0 1-1 1h2l-7 6c1 1 6-3 8-3h0c-3 2-6 3-8 5v1c1 0 1 0 2-1 2-1 3-1 5-1h1v1h0c-2 2-4 4-6 5-4 2-8 5-12 8-1 0-3 1-3 3-1 1-1 1-2 1-2 2-4 3-6 3h-2v-1h-3l-1 1v-2s1 0 3-1c0-1 1-1 2-2v-1c0-1 0 0-1-1h0c-2 1-3 3-5 4-1 1-1 1-2 1l-1-1 1-1 1-1s1 0 1-1h1c0-1 1-2 2-2 2-1 3-3 4-4s1-2 3-3v1l1-1 2-1c1-1 1-2 2-2h1v-2c4-1 7-5 10-6z" class="g"></path><path d="M546 304l1 1 7-4s0 1-1 1h2l-7 6c-1 1-2 1-2 2-5 0-6 6-10 8h-3c3-3 7-5 8-8h0c-3 2-6 5-9 5h-1l2-1c1-1 1-2 2-2h1v-2c4-1 7-5 10-6z" class="o"></path><path d="M548 308c1 1 6-3 8-3h0c-3 2-6 3-8 5v1c1 0 1 0 2-1 2-1 3-1 5-1h1v1h0c-2 2-4 4-6 5-4 2-8 5-12 8-1 0-3 1-3 3-1 1-1 1-2 1-2 2-4 3-6 3h-2v-1c2-1 3-2 4-4 1-1 3-3 3-4-2 2-4 5-7 5 3-3 7-6 11-8s5-8 10-8c0-1 1-1 2-2z" class="j"></path><defs><linearGradient id="x" x1="535.561" y1="316.715" x2="545.836" y2="321.032" xlink:href="#B"><stop offset="0" stop-color="#141313"></stop><stop offset="1" stop-color="#2d2f30"></stop></linearGradient></defs><path fill="url(#x)" d="M555 309h1v1h0c-2 2-4 4-6 5-4 2-8 5-12 8-1 0-3 1-3 3-1 1-1 1-2 1-2 2-4 3-6 3 4-6 8-8 12-12 2-1 4-3 5-4h1c1-1 2-1 3-1h2l1-1c2 0 3-1 4-3z"></path><path d="M476 284h0v-1l-2-1c2 0 4 1 6 1 2 1 4 2 6 2s4 0 5 2l1-1s1 0 2 1l1-1c-1-1-1-1-1-2l7 3c0-2-2-3-2-4 1 0 3 2 3 2 3 2 5 3 8 4l8 4h6l7 2h-4-3l-2 1h-1c-1-1-3 0-4 0-1 1 1 1 0 2h-1 0c0 1 1 1 1 2h-2c0-1 0-1-2-2l2 2h-2v1h3l10 3c-8 1-16 2-23-1-2 0-4-1-5-1s-2-1-3-1l-1-1h0 2 0v-2l-2-2h-1c-1 1-2 0-3 0h-2 0c0 1 1 1 1 2h0-1c-1-1-1-1-2-1v2h0l-6-3c-1-1-1-1-2-1-2 0-3 0-4-1l1-1h-5l1-1h0 1c2 0 3-1 4-2h0-5l1-1c0-1-1-2-1-3h5c-1-1-1-1-2-1l2-1z" class="X"></path><path d="M505 299c3 1 6 3 8 3v1h-4c-2 0-2 0-4-1 0 0-1-1-2-1l1-1h1v-1z" class="I"></path><path d="M516 298c-1-1-4-3-5-4-3-1-6-3-8-4v-1l-2-1c2 0 3 1 4 1 4 3 9 5 14 6 1-1 1 0 2-1h0l3 1-2 1h-1c-1-1-3 0-4 0-1 1 1 1 0 2h-1 0z" class="P"></path><path d="M502 285c3 2 5 3 8 4l8 4h6l7 2h-4-3l-3-1h0c-1 1-1 0-2 1-5-1-10-3-14-6h1l-1-1c-2-1-2-2-3-3z" class="M"></path><path d="M487 287c2 1 4 1 6 2 4 1 7 1 11 4 1 0 1 1 2 1l3 1v1c0 1 2 2 2 3-2 0-3 0-4-1v-1c-1-1-8-3-11-4-2-2-3-2-5-3-1 1-2 1-3 1l-1-1h0c0-1-1-2 0-3z" class="D"></path><path d="M476 286h1c1 0 2 1 3 1h0v-1-1l2 1c2 0 3 0 5 1-1 1 0 2 0 3h0l1 1c1 0 2 0 3-1 2 1 3 1 5 3 3 1 10 3 11 4v1h-3 0l1 1v1h-1l-1 1c1 0 2 1 2 1-1 0-1 1-2 1-2 0-4-1-5-1s-2-1-3-1l-1-1h0 2 0v-2l-2-2h-1c-1 1-2 0-3 0h-2 0c0 1 1 1 1 2h0-1c-1-1-1-1-2-1v2h0l-6-3c-1-1-1-1-2-1-2 0-3 0-4-1l1-1h-5l1-1h0 1c2 0 3-1 4-2h0-5l1-1c0-1-1-2-1-3h5z" class="F"></path><path d="M490 296h2c-2-1-2 0-3-2h3 3c1 1 2 2 3 2 1 1 1 1 2 1-1 1-2 1-3 1h0-1l-2-2h-1c-1 1-2 0-3 0z" class="G"></path><path d="M483 291v-1l-1-1c2 0 3 1 5 1l1 1c1 0 2 0 3-1 2 1 3 1 5 3-1 0-4-1-6 0h0c-3 1-3-1-5-2h-2z" class="I"></path><path d="M496 298h1l6 3c1 0 2 1 2 1-1 0-1 1-2 1-2 0-4-1-5-1s-2-1-3-1l-1-1h0 2 0v-2z" class="C"></path><path d="M475 293c2 0 3 0 5 1h1c1 0 2 0 3 1l2-1c0 1 2 2 2 2h0c0 1 1 1 1 2h0-1c-1-1-1-1-2-1v2h0l-6-3c-1-1-1-1-2-1-2 0-3 0-4-1l1-1z" class="G"></path><path d="M476 286h1c1 0 2 1 3 1h0v-1-1l2 1c2 0 3 0 5 1-1 1 0 2 0 3h0c-2 0-3-1-5-1l1 1v1c-2-1-4-1-7-1h-5l1-1c0-1-1-2-1-3h5z" class="V"></path><defs><linearGradient id="y" x1="422.356" y1="327.594" x2="419.016" y2="309.506" xlink:href="#B"><stop offset="0" stop-color="#acaaac"></stop><stop offset="1" stop-color="#d1d0cb"></stop></linearGradient></defs><path fill="url(#y)" d="M435 305h4l2 2h3c0-1 1-1 2-1v1c-1 0-2 1-3 1l1 1h-2l-1 1 1 1v1l-6 2h1 3c2 2 2 4 2 7 0 0 1 1 1 2v2c-1-1-1-2-2-3l1-1h-1v-1h-1c-1-1-2-2-2-4h0-1l-1-1c-3 1-6 2-8 4l1 1c2 3 2 6 2 10 1 0 1 0 1 1h0c-1 2-2 3-3 4v1h-14-5v-4h0-1-1l-2 1-1 1c-1 0-1 1-2 2h-2v-1c0-1 1-1 1-2v-1c-3 1-8 3-11 3 7-4 14-9 21-14 3-2 5-4 8-6s7-4 10-6h-3v-1l1-1c2-1 2 0 4-1h-1c1-1 3-1 4-1z"></path><path d="M441 310l1 1v1l-6 2c-3 1-6 3-10 5-1-1-1 0-3-1 4-2 7-4 11-5 2-1 5-2 7-3z" class="X"></path><path d="M430 309l1-1v1c-4 5-9 7-14 10-2 1-2 1-3 2h-2c3-2 5-4 8-6s7-4 10-6z" class="G"></path><path d="M426 319c4-2 7-4 10-5h1 3c2 2 2 4 2 7 0 0 1 1 1 2v2c-1-1-1-2-2-3l1-1h-1v-1h-1c-1-1-2-2-2-4h0-1l-1-1c-3 1-6 2-8 4-2 1-4 2-6 4l-7 4h0l-2 2c-1 0-2 1-3 2v1h0-1-1l-2 1-1 1c-1 0-1 1-2 2h-2v-1c0-1 1-1 1-2v-1s3-2 4-2c2-2 4-4 6-5l11-7c2 1 2 0 3 1z" class="R"></path><path d="M406 330c2-2 4-4 6-5l11-7c2 1 2 0 3 1l-11 7c-3 1-5 3-9 4z" class="V"></path><path d="M422 323c2-2 4-3 6-4l1 1c2 3 2 6 2 10 1 0 1 0 1 1h0c-1 2-2 3-3 4v1h-14-5v-4-1c1-1 2-2 3-2l2-2h0l7-4z" class="j"></path><path d="M422 323c2-2 4-3 6-4l1 1c2 3 2 6 2 10-1 0-1 1-2 1 0-1-1-2-1-3-1-1-2-1-2-3h-1l-1 1h0v-2h-1l-1-1z" class="k"></path><path d="M422 323l1 1s-1 1-1 2v1c0 1-1 1-2 1 0 1 0 2-1 3s-3 1-4 1v4h-5v-4-1c1-1 2-2 3-2l2-2h0l7-4z" class="m"></path><path d="M415 329l4-3c0 1 1 1 1 2-1 1-2 2-4 2v1c-1-1-1-1-1-2z" class="e"></path><path d="M422 323l1 1s-1 1-1 2v1c0 1-1 1-2 1 0-1-1-1-1-2l-4 3v-2l7-4z" class="n"></path><path d="M575 299l6 1c1 0 2 1 3 1h1c1 1 3 2 4 2 2 1 3 2 4 2 1 1 2 2 3 2 2-1 2-1 2-2 1 1 3 2 4 3 2 3 5 8 5 12v2c0-1-1-1-1-2-1 0-2-1-3-1l-1 1-2-1h1l-1-1-6-3c-3 1-4-1-6 0-1 1-2 0-3 0s0 0-1 1l-1 1h-1l-7 2-4-1-1-1h-2l-1 1c-2 0-4 0-6-1h-3c-4 1-8 3-11 5h-2c-1 0-2 0-3 1h-1c-2 0-4 2-6 3 0-2 2-3 3-3 4-3 8-6 12-8 2-1 4-3 6-5h0v-1h-1c-2 0-3 0-5 1-1 1-1 1-2 1v-1c2-2 5-3 8-5h0c2-1 3-2 5-3 4-2 9-2 14-3z" class="m"></path><path d="M564 310c-1 0-7 1-8 1 3-2 6-3 9-4l-1 2v1z" class="h"></path><path d="M570 305c-2 0-5 1-8 1l1-1v-1c-2 1-3 1-4 1h-1c2-2 4-2 6-3 3-1 5-1 8-2l-2 2s3 1 4 1h2l-1 3-2-1-1-1s-1 0-2 1z" class="I"></path><path d="M570 305c1-1 2-1 2-1l1 1 2 1 11 2c3 1 7 3 10 4h1l1 1h-4l-1-1h-2-2c-3-1-6-2-8-2-3-1-8-2-11-1-2 1-4 1-6 1v-1l1-2 6-2h-1z" class="H"></path><path d="M575 310c2 0 4 0 7 1 4 1 8 1 12 4-3 1-4-1-6 0-1 1-2 0-3 0s0 0-1 1l-1 1h-1l-7 2-4-1-1-1h-2l-1 1c-2 0-4 0-6-1l14-3h-9l-1 1c-1-1-1-1-2-1h-1-8c5-4 15-3 21-4z" class="V"></path><path d="M579 315c2 0 2 0 3 2l-7 2-4-1c1-1 3-3 5-3h3z" class="G"></path><path d="M575 310c2 0 4 0 7 1 4 1 8 1 12 4-3 1-4-1-6 0-1 1-2 0-3 0s0 0-1 1l-1 1h-1c-1-2-1-2-3-2-2-3-6 0-9-2l2-2h2l1-1z" class="D"></path><path d="M572 300c3 0 5 1 8 1 1 0 0 0 1-1 1 0 2 1 3 1h1c1 1 3 2 4 2 2 1 3 2 4 2 1 1 2 2 3 2 2-1 2-1 2-2 1 1 3 2 4 3 2 3 5 8 5 12v2c0-1-1-1-1-2-1 0-2-1-3-1-1-1-2-2-3-2-3-2-6-3-9-5h2l1 1h4l-1-1h-1c-3-1-7-3-10-4l-11-2 1-3h-2c-1 0-4-1-4-1l2-2z" class="W"></path><path d="M598 305c1 1 3 2 4 3-1 1-1 1-1 2l1 1c-2 0-3-2-5-3l-1-1c2-1 2-1 2-2z" class="Y"></path><path d="M576 303c3 1 5 2 8 4 1 0 2 1 2 1l-11-2 1-3z" class="B"></path><path d="M698 457h0c1 6 0 11 3 17h1l3 6c-2 3 1 4 1 7-1 2-1 2-1 4v1 3l-4 6-6 9c1 1 1 1 1 2h1l2 1v1 1l1 2c-2 1-4-1-7 1 2 1 3 2 5 3 1 1 1 2 2 4h-1l2 3h-1l-1 1-5-1v2s1 0 1 1h0 0c1 2 3 2 3 4v4l3 1h1s1 0 1-1h1v1h1 1l-1 1h-1c-1 1-1 2-1 3l3 1-1 1c1 1 2 1 2 1v1h-1c1 1 2 2 2 4 0 5 3 8 5 12h0v5l1 1v4c-1 5-3 9-6 14-1 1-1 1-1 2l1 2c-2 2-4 6-6 8v-1c0-1 0 0-1-1l-1 1h-1c0 1-1 2-2 2-4 8-10 16-16 24-1 2-4 4-5 7l-6 6c-2 3-4 5-6 7-1 1-4 3-4 4l-4 4-2 1v-2l-1-2h-2l-1-2c0-1-2-2-3-2v1c1 1 1 1 2 1v2l-6 5-21-12 2 2h-3-2c-2-1-3-1-5-1h-3c-1 0-2 0-3-1l9-6 1-1c10-3 19 0 27 4-4-8-7-16-8-25-2-13-1-28-1-42v-67c0-10 1-23-2-33h1v-3c-1-2-1-3-1-5v-1l1 1h0l8 10c1 2 2 3 2 5l3 6v1l-3-2 3 6c-2 1-2 2-3 2 1 1 1 1 2 3h-1c1 1 2 2 3 2l2 2v1l-1 1v3h1c4 1 8 1 11 1 1 0 2-1 4-1 1-1 3-2 4-2v-6c-1 0-1-1-2-1 2-2 4-5 6-7 4-4 7-10 9-14 0 0 1 1 2 1s2 0 3 1c2 1 3 2 4 3l2-6c1-1 1-2 0-4h0v-2c0-1 1-1 1-2 1-1 1-2 3-3h0v-6z" class="s"></path><path d="M694 528c1-1 1-1 3-1 1 0 1-1 2-2l2 3h-1l-1 1-5-1z" class="g"></path><path d="M671 594h0c0-2 0-4 2-6 1-1 2-1 4-1l3 3v1l-4-1-1 1h-1c-1 0-1 1-1 1 1 1 1 2 1 2-2 2-2 0-3 3-1-1 0-2 0-3z" class="P"></path><path d="M648 632c-1 0-1 1-2 1l-1-1c0-2 0-2 1-4l-1-1h0c0-1 0-2 1-2 1-2 0-1 2-2 1 0 2-2 2-2 1-1 2-2 4-2l-1 1c-1 1-1 2-3 3h1c2 0 3-3 6-3-2 1-3 3-4 4l-2 1v1c-1 0-1 1-2 2v2h0c-1 0-1 1-2 1l1 1z" class="O"></path><path d="M645 493c1 1 1 1 2 3h-1c1 1 2 2 3 2l2 2v1l-1 1v3c-2 0-4-1-5-2 0-1 0-2 1-2v-2h-2-1v-1l1-1c-1-1 0-1 0-3-1 1-2 1-2 2h-1l1-1c1-2 1-2 3-2z" class="h"></path><path d="M659 595c-3-2-6-3-8-6-2-2-6-7-6-10l2 3c1 1 1 2 2 3h1 0c1 1 1 1 2 1h2c2 0 3 3 4 3 1 1 2 0 3 1v1 2 1h0c1 1 2 2 2 3-1 0-2-1-4-2z" class="i"></path><path d="M652 515c-1-1-3-3-3-4-1-1-1-2-2-3v-1l1-1c1 0 3 0 4 1 1 0 2 1 2 2s1 1 2 2h1v-1c2 1 3 2 3 4l1 1c-1 2-2 2-3 2h-1c-2 0-3-1-5-2z" class="e"></path><path d="M657 510c2 1 3 2 3 4l1 1c-1 2-2 2-3 2h-1c-2 0-3-1-5-2h1c0-1-1-2-1-2l-1-1h2c0-1 0-1 1-1l2 2h3l-2-2v-1z" class="M"></path><path d="M670 503c0 3 0 6-2 9-1 1-2 2-4 3h-3l-1-1c0-2-1-3-3-4 0-1 1-1 2-3h2l1-1c1 0 2-1 4-1 1-1 3-2 4-2z" class="H"></path><path d="M654 619l2-2c0-1 1-2 2-2v-1c0-1 0-1 1-2v2l1 1h0c1 0 3-2 4-2 3-3 7-5 9-8 2-2 3-5 5-7 1 1 1 2 2 4-2 3-5 5-7 8l-2 2h-1v-1c-1 0-4 3-5 4l-1 1h-2c-2 1-3 3-5 4-3 0-4 3-6 3h-1c2-1 2-2 3-3l1-1z" class="N"></path><path d="M635 473v-3c-1-2-1-3-1-5v-1l1 1h0l8 10c1 2 2 3 2 5l3 6v1l-3-2c0-1-2-4-3-4v3h0-1l1 1-1 1h-1-1c-2-3-3-9-4-13z" class="c"></path><path d="M639 479c-1-1-2-2-2-3v-1l-1-1h1l1 1c1 1 1 3 3 3v-2h0l2-1c1 2 2 3 2 5-1 0-2 0-3-1h-1 0v2h-1c0-1 0-1-1-1v-1z" class="J"></path><path d="M639 479v1c1 0 1 0 1 1h1v-2h0 1c1 1 2 1 3 1l3 6v1l-3-2c0-1-2-4-3-4v3h0-1l1 1-1 1c-1-1 0-1-1-2-2-2-1-2-1-5z" class="I"></path><path d="M664 522c-1 1-1 1-1 2l-1 1c0 1-1 2-1 3 0-1 1-1 2-1h0c0 2 0 1 1 3l4-1-8 7-1 1-2 1h0c-2 2-7 4-10 5 1-1 2-1 3-2-1-1-2-2-2-3h2 0l-1-1v-1l-1-1v-1l2-1v-1c1-1 3-2 4-3l2-1c2-2 3-3 5-4l2-2h1z" class="n"></path><path d="M657 538l-2-3 2-2v-1c-1-1-1-1-2-1l2-2 1 1 1 1c0 1-1 1-1 2h0v2l2 1-1 1-2 1z" class="h"></path><path d="M664 522c-1 1-1 1-1 2l-1 1c0 1-1 2-1 3 0-1 1-1 2-1h0c0 2 0 1 1 3l4-1-8 7-2-1v-2h0c0-1 1-1 1-2l-1-1-1-1c2-1 3-3 4-5l2-2h1z" class="H"></path><path d="M658 530l4-2v1c-1 1-1 2-3 2l-1-1z" class="C"></path><path d="M661 515h3c1 0 2 0 3 1v1l-1 2h0l-2 3h-1l-2 2c-2 1-3 2-5 4l-2 1c-1-1-1-1-2-1s-1 1-2 1-1-1-2-2c1 0 1-1 1-2l-2-1 1-1h1v-1h-1c1-2 0-2 2-3v-1l1-1c1 1 2 1 3 1h1l2-1h1c1 0 2 0 3-2z" class="M"></path><path d="M660 518c2 0 4 1 6 1l-2 3h-1-1-2c-1-1 0-2 0-4z" class="e"></path><path d="M661 515h3c1 0 2 0 3 1v1l-1 2h0c-2 0-4-1-6-1-1 0-3 0-4 1h-1v-1l2-1h1c1 0 2 0 3-2z" class="f"></path><path d="M622 643s-1 0-2-1c1 0 4 1 5 1s4 1 6 0h1v1h1c2-1 4-1 7-1 2 0 5 1 7 3v1c1 1 1 1 2 1v2l-6 5-21-12z" class="L"></path><path d="M640 643c2 0 5 1 7 3v1l-2-1h-1c1 1 2 1 2 2h-2l2 2h-1l-2-2c-3-1-3 2-5 2-1-2 0-1-1-3h-1v-1c1-1 2-1 3-3h1zm19-48c2 1 3 2 4 2 1 2 3 3 5 5h0 2l-2 4c0 1-2 1-3 1h-1l1-2h0c-1 0-1 0-2 1-1-1-2-2-2-3l3 1-1-1-4-3-8 13c-1 1-2 3-3 4v2l-1-1-1 1h-1l1-2h-1v-2l-1-1c2-4 4-7 7-10 2-3 3-6 6-9h1 1z" class="N"></path><path d="M680 596c1 0 2-1 3 0 2 0 3 0 5 1v1l-3 3c-1 2-2 3-3 4-1 0-2 2-3 3l-7 7c-5 5-11 9-16 13l-7 3-1 1-1-1c1 0 1-1 2-1h0v-2c1-1 1-2 2-2v-1l2-1c1-1 2-3 4-4s3-3 5-4h2l1-1c1-1 4-4 5-4v1h1l2-2c2-3 5-5 7-8-1-2-1-3-2-4l1-1 1-1z" class="M"></path><path d="M680 596c1 0 2-1 3 0 2 0 3 0 5 1-1 1-2 1-3 1h-2l-3 3v1c-1-2-1-3-2-4l1-1 1-1z" class="N"></path><path d="M678 598l1-1 1 2 2-2 1 1-3 3v1c-1-2-1-3-2-4z" class="j"></path><path d="M673 610h1c-2 2-4 4-6 5-1 3-5 6-8 7l1-1v-1h0v-1l3-3 1-1c1-1 4-4 5-4v1h1l2-2z" class="q"></path><path d="M674 581l1-1c1-1 2-1 3 0l1 1 1 1h3c2 1 3 1 4 2l1 1h0c1 3 2 5 5 6h1v1l-3 2c2 1 3 1 5 2h0 0c2-1 3 0 5 0l-1 1-1 2c0 1-1 2-2 2-1-1-1-1-2 0l-7 7-3 3-2 1c2-5 5-8 8-12-1 0-2-1-3 0l-2 2-1-1 3-3v-1c-2-1-3-1-5-1-1-1-2 0-3 0 0-1-1-2 0-3-2-1-4-1-5-2l1-1 4 1v-1l-3-3h1l-1-1v-1c-1-1-2-2-3-4z" class="H"></path><path d="M678 587c1 1 2 1 4 1l2-1c1 3 4 5 7 7-4 0-8-2-11-3v-1l-3-3h1z" class="R"></path><path d="M680 593h0c4 2 9 2 13 3-1 1-2 3-2 4-1 0-2-1-3 0l-2 2-1-1 3-3v-1c-2-1-3-1-5-1-1-1-2 0-3 0 0-1-1-2 0-3z" class="b"></path><path d="M674 581l1-1c1-1 2-1 3 0l1 1 1 1h3c2 1 3 1 4 2l1 1h0c1 3 2 5 5 6h1v1l-3 2h0c-3-2-6-4-7-7l-2 1c-2 0-3 0-4-1l-1-1v-1c-1-1-2-2-3-4z" class="W"></path><path d="M674 581l1-1c1-1 2-1 3 0l1 1 1 1c1 1 1 2 3 3h1v1 1l-2 1c-2 0-3 0-4-1l-1-1v-1c-1-1-2-2-3-4z" class="K"></path><path d="M667 578v-3-1h-1 0l1-1h1 0 2c0 1-1 2-1 2v1c1 1 2 1 3 1h1v4h1c1 2 2 3 3 4v1l1 1h-1c-2 0-3 0-4 1-2 2-2 4-2 6h0c0 1-1 2 0 3l-1 1 1 1-1 3h-2 0c-2-2-4-3-5-5 0-1-1-2-2-3h0v-1-2-1c-1-1-2 0-3-1-1 0-2-3-4-3h-2c-1 0-1 0-2-1h0c-1-1-3-4-2-5h0 1l1-1 1-1c2 2 2 2 4 3 1 0 1 1 2 1l1 1h0c2 2 4 1 6 1 2-1 3-1 4-3 0-1 0-1-1-2v-1z" class="V"></path><path d="M671 599l-2 1c-2-1-3-2-4-4h1c1 0 2 0 3 1l1 1 1 1zm0-5h-5l1-1c-1-2-1-3-1-4l-1-1c2-1 3-2 5-2 2-1 3-2 4-2 0 1 1 1 1 2h1 1l1 1h-1c-2 0-3 0-4 1-2 2-2 4-2 6h0z" class="B"></path><path d="M685 601l1 1 2-2c1-1 2 0 3 0-3 4-6 7-8 12-1 1-3 4-4 4h0v-1c0-1 0-1-1-1h0c-1 1-5 4-6 5-2 3-6 6-8 9-1 1-1 2-2 2v2c1 0 2 0 3 1l-5 5c-2 3-4 5-7 7l-1-1c-1 0-3-1-3-2s0-2-1-3v-1l1-1-2-1v-1h2c1-1 2-2 4-3 1 0 2-1 3-1v-1-2c5-4 11-8 16-13l7-7c1-1 2-3 3-3 1-1 2-2 3-4z" class="L"></path><path d="M656 630c1 0 2-1 3-1 0 2 0 3-2 4h0c-1 1-1 1 0 3l1 1v1l-2-2-1 1h-2-1v1h3c1 1 0 2 1 2 0 1-2 3-4 4-1 0-3-1-3-2s0-2-1-3v-1l1-1-2-1v-1h2c1-1 2-2 4-3 1 0 2-1 3-1v-1z" class="N"></path><path d="M665 562h3c1 1 4 5 5 6l1 2h3 1c-2 2-3 4-5 7h0-1c-1 0-2 0-3-1v-1s1-1 1-2h-2 0-1l-1 1h0 1v1 3 1c1 1 1 1 1 2-1 2-2 2-4 3-2 0-4 1-6-1h0l-1-1c-1 0-1-1-2-1-2-1-2-1-4-3l-1 1-1 1v-1c0-2 1-2 1-4l-1 1c0 1 0 1-1 2v-2l3-5h-1-1v-1c2 0 3-1 4-3h-1c-1 1-1 2-3 2h0l-2 2c0-1 1-2 1-3 1-1 1-2 1-4h0c1-1 1-1 2-1 3 0 5 0 8-1h6z" class="e"></path><path d="M667 579l-1 2h-1c-2 1-3 2-5 2-1-1-2-1-3-2-1-2-2-3-1-6 0-2 2-3 4-4h2 1l1 1c-1 1-1 1-1 2-1 0-1 1-1 1 0 1 0 1-1 2 1 1 1 2 2 3 1 0 2-1 3-1l1-1v1z" class="I"></path><path d="M665 562h3c1 1 4 5 5 6l1 2h3 1c-2 2-3 4-5 7h0-1c-1 0-2 0-3-1v-1s1-1 1-2h-2 0-1l-1 1h0 1v1 3l-1 1c-1 0-2 1-3 1-1-1-1-2-2-3 1-1 1-1 1-2 0 0 0-1 1-1 0-1 0-1 1-2l-1-1c-1-1-1-1-1-2-2 0-4 0-5-1 1-2 1-1 2-2v-4h6z" class="B"></path><path d="M709 560c1 1 1 2 1 3l1 1v3l1-2 1-1v5l1 1v4c-1 5-3 9-6 14-1 1-1 1-1 2l1 2c-2 2-4 6-6 8v-1c0-1 0 0-1-1l-1 1h-1l1-2 1-1c-2 0-3-1-5 0h0 0c-2-1-3-1-5-2l3-2v-1h-1c-3-1-4-3-5-6h0 4 3c0-1 1-2 2-3h0c0-1-1-1-1-2 1-1 1-3 2-4 1-4 3-7 4-10 1 0 3 1 4 0-1-1-1-2-2-3 1-1 1-2 2-2l1 2c0-1 1-2 2-3z" class="g"></path><path d="M694 591v-1h0l1-1v1c2-1 3-3 4-4l-1-1 1-1h1c1-1 2-2 2-3v-1-2l2 1v1c-1 2 0 4-1 5l-2 1s0 1-1 2c0 1-2 3-3 3-1 1-1 1-2 1h-1v-1z" class="m"></path><path d="M709 560c1 1 1 2 1 3l1 1v3 1l-1 1h1l1 1v2c-2 3-2 7-3 11v1c-2 3-7 6-10 7-1 1-2 1-4 1 1 0 1 0 2-1 1 0 3-2 3-3 1-1 1-2 1-2l2-1c1-1 0-3 1-5v-1c1-1 1-2 2-3s1-3 2-5c1-1 0-1 1-1l-1-1 1-2-1-1 1-1v-1l-2-1c0-1 1-2 2-3z" class="M"></path><path d="M713 564v5l1 1v4c-1 5-3 9-6 14-1 1-1 1-1 2l1 2c-2 2-4 6-6 8v-1c0-1 0 0-1-1l-1 1h-1l1-2 1-1c-2 0-3-1-5 0h0 0c-2-1-3-1-5-2l3-2h1c2 0 3 0 4-1 3-1 8-4 10-7v-1c1-4 1-8 3-11v-2l-1-1h-1l1-1v-1l1-2 1-1z" class="Y"></path><path d="M707 590l1 2c-2 2-4 6-6 8v-1c0-1 0 0-1-1l-1 1h-1l1-2 1-1c-2 0-3-1-5 0h0 0c4-1 8-3 11-6z" class="j"></path><path d="M683 612l2-1 3-3 7-7c1-1 1-1 2 0-4 8-10 16-16 24-1 2-4 4-5 7l-6 6c-2 3-4 5-6 7-1 1-4 3-4 4l-4 4-2 1v-2l-1-2h-2l-1-2 3-3c3-2 5-4 7-7l5-5c-1-1-2-1-3-1v-2c1 0 1-1 2-2 2-3 6-6 8-9 1-1 5-4 6-5h0c1 0 1 0 1 1v1h0c1 0 3-3 4-4z" class="B"></path><path d="M665 633l1-1c-1 2-1 3-2 4l1 2-3 3-2-3 5-5z" class="D"></path><path d="M656 653c-1-1-1-1-1-2-1-2 1-4 2-5l1-1-1-1h0l2-2 1 1-2 3c0 1-1 1 0 3 0-1 1-1 2-1v-1h0c1-1 2-2 4-2-1 1-4 3-4 4l-4 4z" class="H"></path><path d="M672 619c1-1 5-4 6-5h0c1 0 1 0 1 1v1h0l-8 10c-1 0-1 0-2 1l3-8z" class="Z"></path><path d="M672 619l-3 8c1-1 1-1 2-1l-5 6-1 1c-1-1-2-1-3-1v-2c1 0 1-1 2-2 2-3 6-6 8-9z" class="a"></path><path d="M653 645c3-2 5-4 7-7l2 3-2 2h0l-1-1-2 2h0l1 1-1 1c-1 1-3 3-2 5 0 1 0 1 1 2l-2 1v-2l-1-2h-2l-1-2 3-3z" class="C"></path><path d="M683 534l5-5c1-1 1 0 2-1h4v2s1 0 1 1h0 0c1 2 3 2 3 4v4l3 1h1s1 0 1-1h1v1h1 1l-1 1h-1c-1 1-1 2-1 3l3 1-1 1c1 1 2 1 2 1v1h-1c1 1 2 2 2 4 0 5 3 8 5 12h0l-1 1-1 2v-3l-1-1c0-1 0-2-1-3-1 1-2 2-2 3l-1-2c-1 0-1 1-2 2 1 1 1 2 2 3-1 1-3 0-4 0-1 3-3 6-4 10-1 1-1 3-2 4 0 1 1 1 1 2h0c-1 1-2 2-2 3h-3-4l-1-1c-1-1-2-1-4-2h-3l-1-1-1-1c-1-1-2-1-3 0l-1 1h-1v-4h0c2-3 3-5 5-7h-1-3l-1-2c-1-1-4-5-5-6h-3c2-1 3-1 5-1 2 2 2 2 3 2l1-1-1-2v-2c0-1-1-2-2-2s-1 0-2 1l-2-1c1-1 1-1 1-2-1 0 0 0-1-1l1-1h0v-1l-1-1 1-1c3-1 1-1 3-3l1-1c2-1 2-3 4-5l2-1 5-5z" class="f"></path><path d="M674 555l1-1h0-2v-1l1-1h8l1 1v1h-1c-1-1-2-1-3-1v1c1 0 3 1 5 1h0l3 4-1 1-1-1-2 1c-1-1-3-2-4-3-1 0-2-1-3-1s-1-1-2-1z" class="S"></path><path d="M678 539c1 1 2 2 3 2v1l1 1-2 1c-1 1-1 2-2 3h0l-2 2c-1 1-2 0-3 0-1-2-1-2-1-4 2-1 2-3 4-5l2-1z" class="O"></path><path d="M678 539c1 1 2 2 3 2v1l1 1-2 1-2-1c-1-1-2-1-2-3l2-1z" class="b"></path><path d="M682 543c3-1 5-1 8-1l1 3c-1 3-1 4 0 7 0 2 1 4 2 5l1 1c1 1 1 3 1 5l-1 5c-1-1-2-3-2-4-1 1-2 1-2 2l-2-2-1-1c-1-1-3-2-4-3l2-1 1 1 1-1-3-4h0c0-1-1-2 0-3v-1c0-1-1-1-1-2l-1-1h-2-1l-1-1c1-1 1-2 2-3l2-1z" class="p"></path><path d="M684 555c2 0 2 0 4 1h0c1 2 2 2 3 4h1l-1 1v1c1 1 1 0 0 1-1-1-2-3-4-4l-3-4z" class="f"></path><path d="M687 559c2 1 3 3 4 4l1 1c-1 1-2 1-2 2l-2-2-1-1c-1-1-3-2-4-3l2-1 1 1 1-1z" class="m"></path><path d="M675 557c-1-1-1 0-2-1h-1l1-1h1c1 0 1 1 2 1s2 1 3 1c1 1 3 2 4 3s3 2 4 3l1 1-2 2h-1c0 1 0 2-1 3v1l-3 2c-2 3-2 4-3 8-1-1-2-1-3 0l-1 1h-1v-4h0c2-3 3-5 5-7h-1-3l-1-2c-1-1-4-5-5-6h-3c2-1 3-1 5-1 2 2 2 2 3 2h2v-1-1-4z" class="P"></path><path d="M673 564h1c1 0 2 1 3 2l-1 2v1h-1c-1-2-1-3-2-5z" class="f"></path><path d="M675 557c-1-1-1 0-2-1h-1l1-1h1c1 0 1 1 2 1s2 1 3 1c1 1 3 2 4 3s3 2 4 3l1 1-2 2h-1c-2-1-2-1-3-2-1-2-1-3-2-4h-1-1c-1-2-2-2-3-3z" class="T"></path><path d="M682 564v-2c2 1 3 1 5 1l1 1-2 2h-1c-2-1-2-1-3-2z" class="f"></path><path d="M696 557h2c1 2 1 3 0 5l2-2h2 2l-2 6c-1 3-3 6-4 10-1 1-1 3-2 4 0 1 1 1 1 2h0c-1 1-2 2-2 3h-3-4l-1-1c-1-1-2-1-4-2h-3l-1-1-1-1c1-4 1-5 3-8l3-2v-1c1-1 1-2 1-3h1l2-2 2 2c0-1 1-1 2-2 0 1 1 3 2 4l1-5c0-2 0-4-1-5l2-1z" class="c"></path><path d="M691 571h0c1 0 1 0 2-1v1h-1c0 3 1 4-1 6h-1v-1c0-2-1-2-2-3l3-2z" class="d"></path><path d="M678 580c1-4 1-5 3-8 1 2 1 2 1 4h0c0 1-1 2 0 3 0 1 1 0 0 2h-3l-1-1z" class="B"></path><path d="M682 577l1-1c1-2 2-2 5-3 1 1 2 1 2 3-1 2-2 3-3 4-2 0-2 0-3-1l-2-2z" class="P"></path><path d="M688 564l2 2c0 1 1 3 1 4v1l-3 2c-3 1-4 1-5 3l-1 1v-1h0c0-2 0-2-1-4l3-2v-1c1-1 1-2 1-3h1l2-2z" class="E"></path><path d="M688 564l2 2c0 1 1 3 1 4-3-1-5-1-7 0v-1c1-1 1-2 1-3h1l2-2z" class="T"></path><path d="M696 557h2c1 2 1 3 0 5l2-2h2 2l-2 6c-1 3-3 6-4 10-1 1-1 3-2 4 0 1 1 1 1 2h0c-1 1-2 2-2 3h-3l2-2v-4l-3 3-1-1 1-1 1-1c1-2 1-3 2-4h1v-2l-1 2-1-1-1-3h1v-1c-1 1-1 1-2 1h0v-1c0-1-1-3-1-4s1-1 2-2c0 1 1 3 2 4l1-5c0-2 0-4-1-5l2-1z" class="G"></path><path d="M696 557h2c1 2 1 3 0 5l2-2h2l-1 2h1-2c-1 2-3 4-3 6l-1 1v-1c0-1 0-1-1-2h1c0-2 0-2-1-3 0-2 0-4-1-5l2-1z" class="M"></path><path d="M683 534l5-5c1-1 1 0 2-1h4v2s1 0 1 1h0 0c1 2 3 2 3 4v4l3 1h1s1 0 1-1h1v1h1 1l-1 1h-1c-1 1-1 2-1 3l3 1-1 1c1 1 2 1 2 1v1h-1c1 1 2 2 2 4 0 5 3 8 5 12h0l-1 1-1 2v-3l-1-1c0-1 0-2-1-3-1 1-2 2-2 3l-1-2c-1 0-1 1-2 2 1 1 1 2 2 3-1 1-3 0-4 0l2-6h-2-2l-2 2c1-2 1-3 0-5h-2l-2 1-1-1c-1-1-2-3-2-5-1-3-1-4 0-7l-1-3c-3 0-5 0-8 1l-1-1v-1c-1 0-2-1-3-2l5-5z" class="e"></path><path d="M698 543c1 0 1 0 2 1v2l-2 2h-2c0-1 0-2 1-3 0-1 0 0 1-1v-1z" class="V"></path><path d="M701 540h1v2l-1 2h-1c-1-1-1-1-2-1h0-2l1-2c1 0 3 0 4-1z" class="a"></path><path d="M691 545c1 2 2 9 4 11v-1c1-1 3-1 4-2l1 1v2l-1 1v-2h-1l-2 2-2 1-1-1c-1-1-2-3-2-5-1-3-1-4 0-7z" class="K"></path><path d="M681 542c2-1 3 0 4-1v-1h-1l-1-1h2v-1-1c0-1 0-2 1-3h0 2v-2h1v2l2 2v1c-1 1-1 1-2 1v1c1 0 3 0 4 1l-1 1h-3c-1 0-1 1-1 1h2c-3 0-5 0-8 1l-1-1z" class="S"></path><path d="M702 540s1 0 1-1h1v1h1 1l-1 1h-1c-1 1-1 2-1 3l3 1-1 1c1 1 2 1 2 1v1h-1l-2 1c0 1 0 3-1 4 0 1-1 2-1 2-1 1-1 2-1 3l-1 2-2 2c1-2 1-3 0-5h-2l2-2h1v2l1-1v-2l-1-1h1c1-1 1-4 1-4 0-1-1-1-1-3v-2h1l1-2v-2z" class="L"></path><path d="M703 544l3 1-1 1c1 1 2 1 2 1v1h-1l-2 1c0 1 0 3-1 4 0 1-1 2-1 2 0-3 0-7 1-11z" class="H"></path><path d="M702 555s1-1 1-2c1-1 1-3 1-4l2-1c1 1 2 2 2 4 0 5 3 8 5 12h0l-1 1-1 2v-3l-1-1c0-1 0-2-1-3-1 1-2 2-2 3l-1-2c-1 0-1 1-2 2 1 1 1 2 2 3-1 1-3 0-4 0l2-6h-2-2l1-2c0-1 0-2 1-3z" class="F"></path><path d="M704 560l1-6 4 6c-1 1-2 2-2 3l-1-2c-1 0-1 1-2 2 1 1 1 2 2 3-1 1-3 0-4 0l2-6z" class="b"></path><path d="M683 534l5-5c1-1 1 0 2-1h4v2s1 0 1 1h0 0c1 2 3 2 3 4v4l3 1c-1 1-3 1-4 1s-1 1-2 1v3c-3-2-1-5-2-8l-1-1c0-2 0-5 1-7-1 0-1 0-2 1v1l-2 1h-1v2h-2 0c-1 1-1 2-1 3v1 1h-2l1 1h1v1c-1 1-2 0-4 1v-1c-1 0-2-1-3-2l5-5z" class="L"></path><path d="M678 539l5-5c0 2-1 3-2 4v1 2c-1 0-2-1-3-2z" class="Z"></path><path d="M698 539c-1 1-2 1-3 1h0c-1-1-1-2-1-3l-1-3c0-2 0-2 1-4l1 1h0c1 2 3 2 3 4v4z" class="X"></path><path d="M698 457h0c1 6 0 11 3 17h1l3 6c-2 3 1 4 1 7-1 2-1 2-1 4v1 3l-4 6-6 9c1 1 1 1 1 2h1l2 1v1 1l1 2c-2 1-4-1-7 1-1 0-1 0-2 1-2 2-5 5-6 8 0 1-1 1-2 2-5 5-10 12-16 15h-2l-8 5c-1 2-5 3-7 4h0c-1-1-1 0 0-1v-1h-1c1-1 2-2 3-2-1-1-1-1-2-1h0-1c1-1 1-2 2-3 3-2 5-5 8-7v-1l1-1 8-7-4 1c-1-2-1-1-1-3h0c-1 0-2 0-2 1 0-1 1-2 1-3l1-1c0-1 0-1 1-2l2-3h0l1-2v-1c-1-1-2-1-3-1 2-1 3-2 4-3 2-3 2-6 2-9v-6c-1 0-1-1-2-1 2-2 4-5 6-7 4-4 7-10 9-14 0 0 1 1 2 1s2 0 3 1c2 1 3 2 4 3l2-6c1-1 1-2 0-4h0v-2c0-1 1-1 1-2 1-1 1-2 3-3h0v-6z" class="I"></path><path d="M689 513l-3 4c-1 2-3 5-5 7s-6 6-7 8h0l-4 4h0c0-1-1-1 0-1 0-1 2-3 2-3 3-4 6-7 8-10 3-4 5-7 9-9z" class="c"></path><path d="M688 505c4-2 6-9 9-12 2-2 3-3 4-5 1 0 2 0 3 1 0 1-1 2-2 3h-1c-2 1-4 3-5 6 0 1-2 3-3 4-1 3-2 6-5 8h0c0-1 0-1 1-2v-2h0 0l-2 1c0-1 1-1 1-2z" class="C"></path><path d="M671 526c3-3 7-7 10-11-2 5-7 9-10 12v1c-1 1-1 1-1 2s-2 3-3 5c-1 1-1 2-2 3v1c-1 1-1 1-1 2v1l6-6 4-4 1 2h-1l-5 5c-4 3-8 7-13 10h1c-1 2-5 3-7 4h0c-1-1-1 0 0-1v-1h-1c1-1 2-2 3-2-1-1-1-1-2-1h0-1c1-1 1-2 2-3 3-2 5-5 8-7v-1l1-1 8-7 3-3z" class="N"></path><path d="M664 542c-1 2-7 4-9 6 0-1 3-4 3-4 0-1-1-1-1-2 2-2 4-2 5-5s6-7 9-10v1c-1 1-1 1-1 2s-2 3-3 5c-1 1-1 2-2 3v1c-1 1-1 1-1 2v1z" class="C"></path><path d="M698 457h0c1 6 0 11 3 17h1l3 6c-2 3 1 4 1 7-1 2-1 2-1 4v1l-1-1c-2 4-7 9-8 13l-4 4 10-16c1-1 2-2 2-3-1-1-2-1-3-1-1 2-2 3-4 5-3 3-5 10-9 12 4-5 6-10 9-16l3-6c-1-2-1-3-1-4l-1-1c-1-2-2-3-4-4 1-1 1-2 0-4h0v-2c0-1 1-1 1-2 1-1 1-2 3-3h0v-6z" class="N"></path><path d="M698 457h0c1 6 0 11 3 17 1 2 1 4 2 6v4c-1 1-1 1-1 2h0-1l1-3v-1c-1-1-1-2-1-3l-1 4c-1-2-1-3-1-4l-1-1c-1-2-2-3-4-4 1-1 1-2 0-4h0v-2c0-1 1-1 1-2 1-1 1-2 3-3h0v-6z" class="a"></path><path d="M694 470l1-1c1 1 1 1 2 3s2 5 4 7l-1 4c-1-2-1-3-1-4l-1-1c-1-2-2-3-4-4 1-1 1-2 0-4z" class="e"></path><path d="M689 513h0c1-1 2-1 2-2l2-2h1l1 1h0c1 1 1 1 1 2h1l2 1v1 1l1 2c-2 1-4-1-7 1-1 0-1 0-2 1-2 2-5 5-6 8 0 1-1 1-2 2-5 5-10 12-16 15h-2l-8 5h-1c5-3 9-7 13-10l5-5h1l-1-2h0c1-2 5-6 7-8s4-5 5-7l3-4z" class="B"></path><path d="M695 510c1 1 1 1 1 2h1l2 1v1 1l1 2c-2 1-4-1-7 1-1 0-1 0-2 1-2 2-5 5-6 8 0 1-1 1-2 2-5 5-10 12-16 15h-2c4-4 9-7 13-12 7-6 12-14 17-22h0z" class="T"></path><path d="M697 512l2 1v1h0-1c-2 1-2 1-3 1l-1-1 2-2h1z" class="O"></path><path d="M674 489c4-4 7-10 9-14 0 0 1 1 2 1s2 0 3 1c2 1 3 2 4 3l2-6c2 1 3 2 4 4l1 1c0 1 0 2 1 4l-3 6c-3 6-5 11-9 16 0 1-1 1-1 2-2 2-6 6-6 8-3 4-7 8-10 11l-3 3-4 1c-1-2-1-1-1-3h0c-1 0-2 0-2 1 0-1 1-2 1-3l1-1c0-1 0-1 1-2l2-3h0l1-2v-1c-1-1-2-1-3-1 2-1 3-2 4-3 2-3 2-6 2-9v-6c-1 0-1-1-2-1 2-2 4-5 6-7z" class="E"></path><path d="M678 499c2-1 1-1 3-2 0-1 2-1 2-1l-4 7c-1-1-1-3-1-4z" class="U"></path><path d="M664 515c2-1 3-2 4-3l3 1-4 4v-1c-1-1-2-1-3-1z" class="R"></path><path d="M678 494h1l1-1c2-1 3-1 5-1 0 1-1 3-2 4 0 0-2 0-2 1-2 1-1 1-3 2v-5z" class="P"></path><path d="M685 486h4c-1 2-2 6-4 6s-3 0-5 1l-1 1h-1c1-1 1-2 1-2l3-1c0-2 0-3 1-4v-1h2z" class="h"></path><path d="M666 519c0 1 1 2 2 2h1c0 2-3 5-4 6 0 1-1 1-1 1h2c1-1 2-1 3-1v-1h2l-3 3-4 1c-1-2-1-1-1-3h0c-1 0-2 0-2 1 0-1 1-2 1-3l1-1c0-1 0-1 1-2l2-3h0z" class="B"></path><path d="M694 474c2 1 3 2 4 4l1 1c0 1 0 2 1 4l-3 6v-2l1-1c0-1-1-2 0-4h0l-1-1h-1l-2 3c-1 0-1 0-2 1v1h-3l3-6 2-6z" class="H"></path><path d="M674 489c4-4 7-10 9-14 0 0 1 1 2 1s2 0 3 1c2 1 3 2 4 3l-3 6h0-4-2v1c-1 1-1 2-1 4l-3 1s0 1-1 2v5c0 1 0 3 1 4l-3 4-1 1-4 5-3-1c2-3 2-6 2-9v-6c-1 0-1-1-2-1 2-2 4-5 6-7z" class="O"></path><path d="M688 480c0 1 0 1-1 2v3h-2v-1c-1-1-1-1-1-3 1-1 3-1 4-1z" class="g"></path><path d="M688 480v-3c2 1 3 2 4 3l-3 6h0-4v-1h2v-3c1-1 1-1 1-2z" class="P"></path><path d="M675 508l-1-17c1-1 2-2 3-2h0c3-2 4-2 6-3v1c-1 1-1 2-1 4l-3 1s0 1-1 2v5c0 1 0 3 1 4l-3 4-1 1z" class="X"></path><path d="M676 507c0-2-1-5 0-6l1-1c0-3-1-4-1-7h-1c1-2 1-2 3-2v3 5c0 1 0 3 1 4l-3 4z" class="H"></path><path d="M501 373l1-3v-1h6 20 67c13 0 26 0 38 1 19 1 35 7 48 22 3 3 6 8 8 12 2 3 3 7 5 11 1 3 3 7 3 11-1 1-1 3-2 4l1 5 1 11h0l1 11v6h0c-2 1-2 2-3 3 0 1-1 1-1 2v2h0c1 2 1 3 0 4l-2 6c-1-1-2-2-4-3-1-1-2-1-3-1s-2-1-2-1c-2 4-5 10-9 14-2 2-4 5-6 7 1 0 1 1 2 1v6c-1 0-3 1-4 2-2 0-3 1-4 1-3 0-7 0-11-1h-1v-3l1-1v-1l-2-2c-1 0-2-1-3-2h1c-1-2-1-2-2-3 1 0 1-1 3-2l-3-6 3 2v-1l-3-6c0-2-1-3-2-5l-8-10-5-9-1 1c-3 1-5 2-7 2-2 1-3 1-4 2l-1-1v-1c0-2-2-6-3-8v-1c0-1-1-2-1-3l-2-5c0 1 0 1-1 2h-1-4l-1 2h-1-1c-3 4-4 5-6 9l-1 1 1 1-2 7-2 7-3 7c0 1-1 2-1 3l-5 8c0-2 1-4 1-5s0-1-1-2c0-1 0-2 1-3-2-3-5-4-7-5l-6-2-1-1c-5 4-9 7-14 10l1 1 5 1h0l-2 1h0c0 1 0 2-1 3h0l3 2c3 2 5 3 7 6v1h1c0-1 1-1 1-2l1 1 1 1c-2 2-5 3-4 6v2h1c-2 2-5 4-8 5l-1 1-2 1h0 0c-2 3-6 3-6 6v1 1 2l-1 11-31 1c-4 0-8 0-12-1l-1 1c-1-3-1-7-1-10v-23-75c0-12 1-26 0-38 0-5-2-11-6-14z" class="s"></path><path d="M517 418l2 2c-1 1-2 1-3 2l-2-2 3-2z" class="b"></path><path d="M514 420c0-1-1-3-1-4h1l3 2-3 2z" class="K"></path><path d="M690 413l3 4c0 2 1 4 0 7l-3-9h-1c1-1 1-2 1-2z" class="P"></path><path d="M519 420c2 1 4 3 6 4l1 1c-1 1-1 1-2 1h-2c1-1 1-1 1-2l-1-1c-2 0-2 0-3 1l-3-2c1-1 2-1 3-2z" class="U"></path><path d="M519 441c1 0 2-1 2-1 0 1 0 2-1 3 0 2 0 2-1 3l-3-1-1 1c-1 1-1 1-2 1h0c1-3 4-4 6-6zm110-34c1-1 1-1 2-3 0-1 2-2 2-3h1c-1 1-1 2-2 3 0 1 0 2-1 3v2l-1 1h-1 1l-1 1c-2 0-2-1-4-1l4-3z" class="L"></path><path d="M515 491h0l3 4c0 1 0 1 1 1l1-1v1c1 1 2 2 2 3h-1l-1-1h-2 0-1v2h-1-1v-9z" class="Z"></path><path d="M549 391l1-1h2c1-1 1-1 3-1 0 1 1 3 0 4 0 2 0 3 1 4h-1l-2-2c-1-1-2-3-4-4z" class="p"></path><path d="M526 485l6 3h1l-1 1c-3 1-6 1-10 0l2-1v-1h-1v-1c1 0 1-1 3-1z" class="G"></path><path d="M689 404c2 3 3 7 5 11l-1 2-3-4c-1-3-1-5-3-6l2-3z" class="U"></path><path d="M519 441c-1 0-1-1-2-1l-7 5c4-4 7-8 13-9l1 1-1 2-2 1s-1 1-2 1z" class="o"></path><path d="M643 413h0c-1 2-2 4-3 5-1 4-3 7-5 11l-1-1v-4h1c2-4 5-7 8-11z" class="T"></path><path d="M520 410h2l1-1h3l1-1c1-1 3-1 4-2 1 0 3-1 4-1v-1l1 1-3 2c-4 3-9 5-14 4-1 0-1-1-2-1h1 0 2z" class="k"></path><path d="M693 417l1-2c1 3 3 7 3 11-1 1-1 3-2 4l-2-6c1-3 0-5 0-7z" class="N"></path><path d="M614 397c1-1 2-2 3-2v1l-2 3c1 1 2 1 3 1h1c1 2 2 2 5 2h1l1 1h0-1l-1 1-3 1c-1 0-2 0-3-1v1l-1-1-1-1h-1v-1-2-1s-1-1-1-2z" class="g"></path><path d="M519 424c1-1 1-1 3-1l1 1c0 1 0 1-1 2h2c1 0 1 0 2-1 2 1 5 3 6 5l1 2h0c-4 0-11-5-14-8z" class="S"></path><path d="M632 432c1 1 1 2 2 3 1-1 0-1 1-2s1-2 2-3l4-5s1-1 1-2l1-1 1-1h1c0 2-3 5-4 7h0c-1 1-1 2-2 2-1 1-1 2-2 4h1v1l-3 3v1l-2-4-1-3z" class="K"></path><path d="M536 405c2-2 3-4 6-4l4 3-2 2-1 2v1h-2s-1 0-2-1c0 0-1-1-1-3h0-1 0c-1 1-2 2-3 2h-1l3-2z" class="O"></path><path d="M543 408h-2c-1-1-2-2-2-3s1-1 2-2l3 3-1 2z" class="M"></path><path d="M581 392v1c0 2 0 2-1 4v1h0l1-1c0-1 1-2 1-3h1c0 1-1 3-1 4-2 2-2 5-4 7 0 1 0 1-1 1h0-2l-1 1c0 1-1 2-2 3l-3 1 3-4c1-1 2-2 2-3 3-4 5-8 7-12z" class="L"></path><path d="M534 413h0c0 1 1 1 1 2h-1v6l-1 7v4l-1-2c-1-2-4-4-6-5l-1-1c2 1 2 1 3 1 1 1 0 1 2 1h0c0-1-1-2-2-2 0-1 0-1-1-2 0 0-1-1 0-2 1 0 3 1 4 2h1 1v-1h-1c-2-1-2-1-2-2l1-1s1-1 2-1c1-1 0-3 1-4z" class="T"></path><path d="M651 408v-3c1 0 2-1 3-1v-1c0-1 0-1 1-1l2 1h2l1-1v1c1 0 2-1 3 0 1 0 1 1 3 1h2c-1 1-1 1-1 2h-2-1c0 1 0 1-1 1h-1l-1 1c-1 0-2 0-3 1h0-1l1-1c-1-1 0-1-1-2h1v-1c-1 0-2 0-3 1l1 1h0l-1-1c-2 0-2 1-4 2z" class="K"></path><path d="M581 392c0-1 0-1 1-1 2 0 3 0 4 1l1 1h1c1 2-1 6 1 7 0 2-1 2-1 3v1l-1 2-1 1c-1-1-2-2-2-3v-1c1 0 2-2 2-3s1-2 0-3h0l-1 1c-1 1 0 0-1 0h-2c0-1 1-3 1-4h-1c0 1-1 2-1 3l-1 1h0v-1c1-2 1-2 1-4v-1z" class="g"></path><path d="M538 399c3-1 3-1 6-1l3 3h-3c-1-1-1 0-2 0-3 0-4 2-6 4l-1-1v1c-1 0-3 1-4 1-1 1-3 1-4 2l-1 1h-3l-1 1h-2v-1h-1l2-2h3c5-2 9-4 14-8z" class="N"></path><path d="M686 435l1-14v-2h1c2 1 3 8 4 10l1 7-3-3h-2l-1-1v1l-1 2z" class="M"></path><path d="M687 433v-2h1c0-2 0-2 1-3h1v2l2-1 1 7-3-3h-2l-1-1v1z" class="i"></path><path d="M607 403l4-4c1-1 2-1 3-2 0 1 1 2 1 2v1 2 1h1l1 1 1 1c-2 1-3 1-3 3h-2v1c-1-1-1-1-2-1l-3-3h-1-3l2-3 1 1z" class="K"></path><path d="M607 403l4-4c1-1 2-1 3-2 0 1 1 2 1 2-2 1-3 2-5 3l-2 2c-1-1-1 0-1-1z" class="T"></path><path d="M589 400c2-1 2-3 4-5h0l1 1h-1l-1 1v1c1 1 1 2 0 3v1c1 0 3-1 4 0 0 1 1 1 1 2h1 1 1l2-1-3 6-2 5h-1v-2l2-2c0-1-1-2-1-3v-1c-2-1-3 0-5 0-2 1-4 0-5 0l1-2v-1c0-1 1-1 1-3z" class="K"></path><path d="M687 433v-1l1 1h2l3 3v6s0 3-1 3l-1 5c0-1-1-2-1-4 0-1 0-1-1-2h0c-1 0-2-1-4-1l1-4v-3-1l1-2z" class="D"></path><path d="M687 433v-1l1 1h2l3 3v6l-1-1v-3c0-1-1-1-2-1-1-2-2-1-4-1v-1l1-2z" class="C"></path><path d="M686 439l1-2c1 0 2 1 2 1 1 2 1 4 2 6v2l1-1h0l-1 5c0-1-1-2-1-4 0-1 0-1-1-2h0c-1 0-2-1-4-1l1-4z" class="G"></path><path d="M618 405v-1c1 1 2 1 3 1l3-1 1-1h1l1 1c1 0 1 0 2-1h1l-1 1v3l-4 3-1 1 1 1-3 3h0c0-1 1-2 1-3v-1h-1c-1-1-2-1-3 0-2 0-4-1-5 0 0-1 1-2 1-3 0-2 1-2 3-3z" class="R"></path><path d="M537 398l1 1c-5 4-9 6-14 8h-3l-2 2-1-1h-2c1-1 0-1 1-2h1v-1l-1 1-1-1c0-1 1-1 1-2s1-1 2-2c0 1 0 1 1 1 1-1 1-1 2-1 2-1 3 1 4 2l1-1h2c1 0 2-2 4-2 1 0 0 0 1-1l3-1z" class="K"></path><path d="M639 438v-2c1 0 2-1 2-2 1-1 2-2 3-4 2 1 3 1 4 1v-1l1 1-4 7-3 6-3 3-1-1c0-2-1-3-3-5v-2-1c1 1 2 1 3 2l1-2z" class="g"></path><path d="M639 438l1 1-2 4h1 2c1-1 1-2 1-3 1-2 1-1 3-2l-3 6-3 3-1-1c0-2-1-3-3-5v-2-1c1 1 2 1 3 2l1-2z" class="T"></path><path d="M649 431h2l1-1h2v1c0 2 0 3 1 5l-2 1v1 2c-1 3-2 5-4 8-1 0-1 0-2-1-1 0-1 0-2-1-1 0-2-1-3-2l3-6 4-7z" class="c"></path><path d="M649 431h2l1-1h2v1c0 2 0 3 1 5l-2 1v1 2c0-1-1-1-1-2l1-1c-1-1-1-1-1-2v-1h0c-2 3-5 5-6 7-1 1-2 1-2 2s1 2 1 3c-1 0-2-1-3-2l3-6 4-7z" class="F"></path><path d="M661 408l1-1h1c1 0 1 0 1-1h1 2c0-1 0-1 1-2 1 1 2 1 2 2 1 1 1 0 2 1v-1l-1-1h1l1 1h2l-1 2h1v2c1 2 2 3 2 5l1-1h-1c0-3 1-5 0-7h1c0 1 1 2 1 3l1 1c1 1 1 2 1 4h0c-1 1-1 1 0 2v-1h1v2l-1 1 1 1s0 1-1 1l-1-1v-1l-1-1 1-1-1-1-1 1v1c1 0 1 0 0 1-1 5 0 11-1 16h0c-1-1 0-8 0-10 0-3-1-5-1-8v-1c-1-1-2-2-4-2h0l-1-1 2-1v-1h-2l-1-1h-3-1l-1-1c-1 0-3 0-4-1z" class="O"></path><path d="M520 532c0-3 1-5 1-7h1v2h1c0-2 1-3 2-4l1-1h0c-1-1-3 0-5-1h-1c-1 0-1 0-2-1 1-1 5 0 7 0 0-2 0-3-1-4v-1h1l-1-1v-1c-1-2 0-3 0-4l-1-1c0-1 0-2-1-3-1 0-1 0-2-1h0 2l1 1h1 0 1c1 2 1 4 1 6 1 2 2 5 3 7l3 14h-7-5z" class="b"></path><path d="M602 403l1 1-1 2h1l1-1h3 1l3 3c1 0 1 0 2 1v-1h2c0 1-1 2-1 3-1 1-2 1-3 2s-1 3-3 3h-1l2-1-1-1h-1 0l-10 9c0-1 0-2 1-3v-1-1h1 1l1-2h-1v-1c-2 1-3 2-5 2h0c0-1 0-2 1-3h1l2-5 3-6z" class="L"></path><path d="M602 403l1 1-1 2h1l1-1h3l-2 2v2l-1 1c-2-1-2 0-3 0l-2-1 3-6z" class="Z"></path><path d="M597 414h3c1-1 2-3 3-2l-1 1 1 1 4-5 1 1-5 6c2-1 5-5 6-5-1 1-1 2-2 2v1l-10 9c0-1 0-2 1-3v-1-1h1 1l1-2h-1v-1c-2 1-3 2-5 2h0c0-1 0-2 1-3h1z" class="N"></path><path d="M514 469c3-1 4-3 7-3-1 1-1 2-2 4v1c-1 3-2 4-1 7v2l2-2c1 1 3 2 4 3l2 2c1 0 2 1 3 1h3c0 2 1 3 1 4h-1l-6-3c-2 0-2 1-3 1v1h1v1l-2 1c-1-1-3-2-4-3-3-2-5-4-5-8-1-3 0-6 1-9z" class="S"></path><path d="M520 478c1 1 3 2 4 3l2 2h-2c-2-1-4-1-6-3l2-2z" class="U"></path><path d="M518 486c0-1 0-3-1-4l1-1c1 1 2 3 4 3 1 0 2 1 4 1-2 0-2 1-3 1v1h1v1l-2 1c-1-1-3-2-4-3z" class="D"></path><defs><linearGradient id="z" x1="681.365" y1="430.255" x2="674.139" y2="442.054" xlink:href="#B"><stop offset="0" stop-color="#a1a09e"></stop><stop offset="1" stop-color="#c7c6c5"></stop></linearGradient></defs><path fill="url(#z)" d="M677 435c1-5 0-11 1-16 1-1 1-1 0-1v-1l1-1 1 1-1 1 1 1v1l1 1c1 0 1-1 1-1l-1-1 1-1 1 3v8c0 9-2 19-5 27-3 6-6 11-9 16 0-2 1-3 2-5l6-13c1-2 3-6 2-8h0l-1 1c-1 0-1-1-2-1h0c-1 2-1 2-2 3 1-2 2-4 2-7 1-2 2-5 1-7z"></path><path d="M625 410c2 0 2 1 4 1l1-1h-1 1l1-1-1 4v8h-2l-1 1-2 2c0 2 0 4-1 6v3l-1 1h-1l-2-1c-1-1-1-2-1-3l-1-1c0-2 0-3-1-5 1-1 1-2 1-4l1-1-1-1c-1 1-1 1-2 1 0 0 1-1 1-2h-2l1-2-1-1c-2 1 0 1-1 3-2 1-4 1-6 1v-2c2 0 2-2 3-3s2-1 3-2 3 0 5 0c1-1 2-1 3 0h1v1c0 1-1 2-1 3h0l3-3-1-1 1-1z" class="N"></path><path d="M619 411c1-1 2-1 3 0h1v1c-1 0-2 0-3 1h-2c-1 0-1 1-2 1v-1l3-1v-1z" class="j"></path><path d="M622 418c0-2 0-3 2-4l1 1h0v3l-1 1h0c-1 0-2-1-2-1z" class="M"></path><path d="M626 420l1-5h0c1-1 2-1 3-2v8h-2l-1 1-2 2 1-4z" class="X"></path><path d="M625 418v3l1-1-1 4c0 2 0 4-1 6v3c-1-1-1-2-2-3h1l-1-2-1 1-1-5 2-6s1 1 2 1h0l1-1z" class="n"></path><path d="M620 424l3-1v-1h1c1 2 0 6 0 8v3c-1-1-1-2-2-3h1l-1-2-1 1-1-5z" class="q"></path><path d="M524 437c2 0 3 2 5 4 1 1 2 2 2 4v1h-2l1 1c-1 1-1 1-2 1h-1c-1 3-2 5-2 8l-1 1v1l2 4 2 2h-4l-3 2c-3 0-4 2-7 3v-3l1-1-1-1v-1l-2-2h2c-1-2 0-3-1-4v-2h-1c1-2 0-1 1-3 0 0 2-1 2-2-2 0-3 1-5 1l5-3v-2l1-1 3 1c1-1 1-1 1-3 1-1 1-2 1-3l2-1 1-2z" class="o"></path><path d="M519 453c2 0 3-1 5 0v4 1l2 4 2 2h-4-3-1v-1h-2c1-1 1-1 1-2l2-1h-2v-3-1h1v-1h-2l1-2z" class="M"></path><path d="M524 458l2 4c-1 1-2 1-3 1 0-3 0-3 1-5z" class="e"></path><path d="M524 437c2 0 3 2 5 4 1 1 2 2 2 4v1h-2l1 1c-1 1-1 1-2 1h-1c-1 3-2 5-2 8l-1 1v-4c-2-1-3 0-5 0v-2-1h-2v-1c1 0 1-1 2-1h0 1l-1-1-2 1v-1l2-1c1-1 1-1 1-3 1-1 1-2 1-3l2-1 1-2z" class="S"></path><path d="M526 442l3 4-1 1v1h-1c-1 3-2 5-2 8l-1 1v-4-4c1-1 0-2 1-3 0-2 1-3 1-4z" class="O"></path><path d="M524 437c2 0 3 2 5 4 1 1 2 2 2 4v1h-2l1 1c-1 1-1 1-2 1v-1l1-1-3-4h1l-1-1c-1 0-1 0-2-1h0l-1-1 1-2z" class="N"></path><path d="M526 397h0c1-2 1-3 2-4h2l1-1-1-1c1-1 1-1 2-1v1 1l2 1s1 0 1-1l-1-2 1-1 1 1 2-1 1 1v-2h1v2c-1 0-1 1-1 2h1l1-1c1 0 1 0 2 1v1h1c0-1 0-2 1-3v1l1-1-2-1 1-1 3 3h1c2 1 3 3 4 4l2 2h1c-1-1-1-2-1-4l3 2-1 2 1 2v1h1v-2l1-1 1 1c2 2 3 5 3 8h0c1 1 1 2 2 4h-2-1-1c-3 0-5 0-7-1h-1c-3-1-6-3-8-5l-4-3c1 0 1-1 2 0h3l-3-3c-3 0-3 0-6 1l-1-1-3 1c-1 1 0 1-1 1-2 0-3 2-4 2h-2c-1-2-1-3-1-5z" class="T"></path><path d="M542 401c1 0 1-1 2 0h3c2 2 7 5 9 5h1c-2 1-2 2-2 3h-1c-3-1-6-3-8-5l-4-3z" class="M"></path><path d="M546 397l1-2h3 3l2 2c0 1 0 1-1 2h-1-1c0 1 2 3 3 4l-1 1-2-1 1-1-1-1h-1-2l2-1v-1h-3c-1 0-3-1-4-2l1-1 1 1z" class="N"></path><path d="M546 397l1-2h3c0 1-1 1-1 2h-3z" class="L"></path><path d="M526 397c1 1 2 1 3 1l1-1c2 0 2 0 3-1 1-2 1-2 3-3 1 0 1 1 2 2 0 1 0 2-1 3l-3 1c-1 1 0 1-1 1-2 0-3 2-4 2h-2c-1-2-1-3-1-5z" class="Z"></path><path d="M556 406v-2l1-1h1c0-1-1-1-2-2h2l1 1 1-1v-3h1c2 2 3 5 3 8h0c1 1 1 2 2 4h-2-1-1c-3 0-5 0-7-1 0-1 0-2 2-3h-1z" class="j"></path><path d="M557 406c2 0 5 0 6 2v2h-1c-3 0-5 0-7-1 0-1 0-2 2-3z" class="e"></path><path d="M529 441c3 1 2 1 3 4 0 1-1 2-1 4h1v1-9c1-2 0-5 1-7l1 12-1 43h-1l1-1c0-1-1-2-1-4h-3c-1 0-2-1-3-1l-2-2c-1-1-3-2-4-3l-2 2v-2c-1-3 0-4 1-7v-1c1-2 1-3 2-4l3-2h4l-2-2-2-4v-1l1-1c0-3 1-5 2-8h1c1 0 1 0 2-1l-1-1h2v-1c0-2-1-3-2-4z" class="f"></path><path d="M525 481l5-2v1l-1 2c2 1 2 0 3 2h-3c-1 0-2-1-3-1l-2-2h1 0z" class="a"></path><path d="M525 456c0-3 1-5 2-8h1c1 1 2 1 2 2-1 1 0 1-1 0v1 1c0 1-1 2-2 3v1 1l-1 1-1-2z" class="N"></path><path d="M524 464h4c2 2 3 3 3 6 0 2 0 3-2 4v1c-2 1-4 2-6 2l-1 1 3 3h0-1c-1-1-3-2-4-3l-2 2v-2c-1-3 0-4 1-7v-1c1-2 1-3 2-4l3-2z" class="G"></path><path d="M519 471c2-2 4-3 7-3h1l-1 1c-2 0-2 1-4 2-1 2-2 3-2 6v1l-2 2v-2c-1-3 0-4 1-7z" class="b"></path><path d="M522 471l1 1 2-1h0l-1 2v1h2 3v1c-2 1-4 2-6 2l-1 1 3 3h0-1c-1-1-3-2-4-3v-1c0-3 1-4 2-6z" class="S"></path><defs><linearGradient id="AA" x1="669.886" y1="464.879" x2="683.03" y2="474.559" xlink:href="#B"><stop offset="0" stop-color="#c7c5c2"></stop><stop offset="1" stop-color="#f0efeb"></stop></linearGradient></defs><path fill="url(#AA)" d="M685 443c2 0 3 1 4 1h0c1 1 1 1 1 2 0 2 1 3 1 4-2 8-5 17-8 25-2 4-5 10-9 14-2 2-4 5-6 7-1 1-2 2-4 3l-1-1c-1 0-2 1-3 1l-1-2c3-2 4-4 6-6l2-2v-1l-2 2-1-1 3-4c10-12 15-27 18-42z"></path><path d="M665 491c0 2-2 4-1 6 1 0 6-4 7-6 1-1 1-2 3-2h0c-2 2-4 5-6 7-1 1-2 2-4 3l-1-1c-1 0-2 1-3 1l-1-2c3-2 4-4 6-6z" class="J"></path><path d="M582 398h2c1 0 0 1 1 0l1-1h0c1 1 0 2 0 3s-1 3-2 3v1c-4 7-9 13-15 19-4 3-7 5-11 7l-6 3-1-1-1-1v-2-4-7c1 2 1 4 1 6 1 0 0 0 1-1v-1c0-4 1-7 1-11l1-2h1c2 1 4 1 7 1h1 1 2 0l1 1c1-2 2-3 5-4l-3 4 3-1c1-1 2-2 2-3l1-1h2 0c1 0 1 0 1-1 2-2 2-5 4-7z" class="O"></path><path d="M563 410h1l2 1v2c-2 2-4 4-6 5h-1v-4c-1-1-1-2-1-3l1-1c1 1 2 1 3 0h1z" class="g"></path><path d="M553 411c2 1 3 1 4 3 0 1 0 2-1 3 1 1 1 0 2 1 1 0 0 0 1 1-1 2-5 4-6 5l-1-1v-1c0-4 1-7 1-11z" class="R"></path><path d="M569 411l3-1c1-1 2-2 2-3l1-1h2 0c-1 2-2 5-4 6-2 3-5 6-7 8-3 3-7 5-10 7-1 1-3 2-5 3 0-1 0-2 1-3 2-3 5-5 8-7l9-9z" class="X"></path><path d="M573 412c-2 3-5 6-7 8l-1-1c0-1 0-2 1-3 1 0 2 0 3-1s3-2 4-3zm-22 20c7-6 15-10 22-17 0 0 2-2 3-2v-2c0-1 2-2 2-2 2-2 4-5 6-7v1 1c-4 7-9 13-15 19-4 3-7 5-11 7l-6 3-1-1z" class="C"></path><path d="M618 420c0 2 0 3-1 4 1 2 1 3 1 5l1 1c0 1 0 2 1 3l2 1h1l1-1v-3c1-2 1-4 1-6l2-2 1-1h2c0 4 1 8 2 11l1 3 2 4v2c2 2 3 3 3 5l1 1-3 4c-1 1-3 1-4 1-1 1-2 2-2 3l-1-1v-1h-1c-1 1-2 3-2 4-1-1-1-1-2-1 0 0 0 1-1 1 0-1 0-1-1-1v2h-1l-2-9h0-1c0 3 2 7 2 9l-3 2v-1c0-2-2-6-3-8v-1c0-1-1-2-1-3l-2-5c0 1 0 1-1 2h-1-4l-1 2h-1v-1c1-1 2-2 2-3h1l1-1c1-1 1-1 1-2l2-2v-3c-1 1-2 1-3 2 0 1-1 1-2 1l5-7 4-4 4-6z" class="n"></path><path d="M613 447v-1c1 0 1 0 2-1l-1-3h1 1v1l1 4c1 1 1 2 1 2h0c0 3 2 7 2 9l-3 2v-1c0-2-2-6-3-8v-1c0-1-1-2-1-3z" class="X"></path><path d="M618 420c0 2 0 3-1 4-1 2 0 5-2 6l-2 2v1 1h-2v1l1 2h0-1c-1 2 0 3 0 5 0 1 0 1-1 2h-1-4l-1 2h-1v-1c1-1 2-2 2-3h1l1-1c1-1 1-1 1-2l2-2v-3c-1 1-2 1-3 2 0 1-1 1-2 1l5-7 4-4 4-6z" class="a"></path><path d="M627 422l1-1h2c0 4 1 8 2 11l1 3 2 4v2c2 2 3 3 3 5l1 1-3 4c-1 1-3 1-4 1-1 1-2 2-2 3l-1-1v-1h-1c-1 1-2 3-2 4-1-1-1-1-2-1v-2l-1-1v-1l2 1c2-1 3-2 4-4l-2-1c0-1 1-1 1-2v-1c-2-1-1 0-1-2 0-1-4-3-5-4l1-1-1-1c-1 0-1 0-2 1 2 1 2 1 3 3v3l-1 1c-1 0-1-1-1-1-1-1-1-1-1-2h-1v-2-2l-1-3c1-1 0-1 1-1l1-1 2 1h1l1-1v-3c1-2 1-4 1-6l2-2z" class="C"></path><path d="M627 427h0c1 1 1 1 1 2v2l2 2h-1c-1 1 0 1-1 2l1 1c1 0 1 0 2-1 0 2 0 2-1 3h-1c-1 0-2 0-3 1h0-1l-1-1 1-2 1-1c1-1 0-3 1-5v-3z" class="B"></path><path d="M630 438l1 1 1-1c0-1 0-1 1-2v-1l2 4v2l-1 1s0 1-1 2h0v-1c-1 0-2 1-4 2 0-2-1-3-2-4 1-1 2-2 2-3h0 1z" class="I"></path><path d="M629 449h0l1-1 1 3 1 1 1-1v-2h0l1-1c1 1 0 1 1 1l1-1v-1-1h2l1 1-3 4c-1 1-3 1-4 1-1 1-2 2-2 3l-1-1v-1h-1c-1 1-2 3-2 4-1-1-1-1-2-1v-2l-1-1v-1l2 1c2-1 3-2 4-4z" class="M"></path><path d="M627 422l1-1h2c0 4 1 8 2 11l1 3v1c-1 1-1 1-1 2l-1 1-1-1c1-1 1-1 1-3-1 1-1 1-2 1l-1-1c1-1 0-1 1-2h1l-2-2v-2c0-1 0-1-1-2h0v-5z" class="D"></path><path d="M651 408c2-1 2-2 4-2l1 1h0l-1-1c1-1 2-1 3-1v1h-1c1 1 0 1 1 2l-1 1h1 0c1-1 2-1 3-1 1 1 3 1 4 1l1 1h1 3l1 1h2v1l-2 1 1 1h0c2 0 3 1 4 2v1c0 3 1 5 1 8 0 2-1 9 0 10h0c1 2 0 5-1 7 0 3-1 5-2 7l-2 4c0 1 0 2-1 3v-1-1-1c1-1 1-1 1-2l1-1c0-1 0-3 1-4l-1-1-1-2c1-1 1-1 1-2-2-2-3-2-5-4h-4c-1 0-2 2-2 2-1 2-2 3-3 4v-3l-1 1-1-1c-1 0-3-1-4-2v-1l2-1c-1-2-1-3-1-5v-1h-2l-1 1h-2l-1-1 1-1c0-2 1-5 2-7v-3l1-4c-1-3 0-5-1-7z" class="S"></path><g class="M"><path d="M665 424h2 1l-2-2c1 0 2-1 3-1l1 1h1v1l-2-1-1 1c1 0 1 1 2 1s1 0 2 1l1-1v1h-1v1h1l1-1h3c0 2-1 9 0 10h0c1 2 0 5-1 7 0 0 0-1-1-1 1-2 1-4 1-6-3-3-5-4-10-5 0-1-1-1-1-2l-2-1c1-1 2-1 3-1h1 0c-1-2-1-2-2-2z"></path><path d="M652 415c2 0 2 0 4-2v-1l1 1c0 1 0 1 1 1h0 2c-1 1-2 0-3 2h1v2s1 1 1 2c1 0 2-1 2-1 1 1 2 1 3 2h2v1h-1c0 1-1 1-2 2h2c1 0 1 0 2 2h0-1c-1 0-2 0-3 1l-2 1v-1c0-1-1-1-1-2h-1c0 1-1 2 0 3h-1 0l-2 3-1-2-1 2h0v-1h-2l-1 1h-2l-1-1 1-1c0-2 1-5 2-7v-3l1-4z"></path></g><path d="M651 419h3v2c3 0 2-3 5-1h0l-1 2c2 1 3 2 5 1v1h2c1 0 1 0 2 2h0-1c-1 0-2 0-3 1l-2 1v-1c0-1-1-1-1-2h-1c0 1-1 2 0 3h-1 0c-1-2-1-2-2-3v-3h-1c0 1 0 1-1 2v-2c-2-1-2 0-3 0v-3z" class="e"></path><path d="M651 422c1 0 1-1 3 0v2c1-1 1-1 1-2h1v3c1 1 1 1 2 3l-2 3-1-2-1 2h0v-1h-2l-1 1h-2l-1-1 1-1c0-2 1-5 2-7z" class="C"></path><path d="M649 429l2 1h0l1-1v-1s2-1 3-1v1 1l-1 2h0v-1h-2l-1 1h-2l-1-1 1-1z" class="I"></path><path d="M658 428h0 1c-1-1 0-2 0-3h1c0 1 1 1 1 2v1l2-1 2 1c0 1 1 1 1 2 5 1 7 2 10 5 0 2 0 4-1 6 1 0 1 1 1 1 0 3-1 5-2 7l-2 4c0 1 0 2-1 3v-1-1-1c1-1 1-1 1-2l1-1c0-1 0-3 1-4l-1-1-1-2c1-1 1-1 1-2-2-2-3-2-5-4h-4c-1 0-2 2-2 2-1 2-2 3-3 4v-3l-1 1-1-1c-1 0-3-1-4-2v-1l2-1c-1-2-1-3-1-5h0l1-2 1 2 2-3z" class="H"></path><path d="M655 436h1v-2l2 2 2-1c-1 1 0 1-1 2v1h2l1 1c-1 2-2 3-3 4v-3l-1 1-1-1c-1 0-3-1-4-2v-1l2-1z" class="I"></path><path d="M658 428h0 1c-1-1 0-2 0-3h1c0 1 1 1 1 2v1l2-1 2 1c0 1 1 1 1 2-1 0-2 1-3 2 0 0-1 1-2 1h-1 0v-2h-1s0 1-1 2v-2h-1v1l-1 1v-2l2-3z" class="X"></path><path d="M666 430c5 1 7 2 10 5 0 2 0 4-1 6 1 0 1 1 1 1 0 3-1 5-2 7l-2 4c0 1 0 2-1 3v-1-1-1c1-1 1-1 1-2l1-1c0-1 0-3 1-4l-1-1-1-2c1-1 1-1 1-2s1-2 1-3l-3-3h-1c-2-1-3 0-4-1-2 0-3-1-5-1 1 0 2-1 2-1 1-1 2-2 3-2z" class="V"></path><path d="M569 423c6-6 11-12 15-19 0 1 1 2 2 3l1-1c1 0 3 1 5 0 2 0 3-1 5 0v1c0 1 1 2 1 3l-2 2v2c-1 1-1 2-1 3-1 2-3 3-4 5s-2 3-3 5c-3 5-9 9-13 14-1 1-2 3-4 4l-6 5c-1 2-4 4-5 5l-1-1-4 3-2 2v-4c1-1 1-2 1-3-1 0-2 1-3 2v1l-1-4v-6-4c-1-2 0-8 0-10l1 1 1 1 6-3c4-2 7-4 11-7z" class="C"></path><path d="M579 429v1 1c2 0 2 0 3-1l1-1c2-1 3-4 6-5-3 3-5 6-8 9h0v-1c-3 1-10 12-12 11 1-2 3-3 4-4l3-4-1-1c-1 3-3 4-5 6l-1 1-3 2v1h-1 0l2-4c3-1 5-5 7-6 1-1 2-3 4-4h0l1-1h0z" class="e"></path><path d="M567 440l-2 4h0c-2 3-4 5-7 7l-2 2 1 1v1h-1c0 1-1 1-1 2l-2 2v-4c1-1 1-2 1-3l2-2h-2c1-1 4-4 6-5h1c2-2 4-4 6-5zm19-19c1 1 1 2 2 2l1-1c0-1 0-2-1-3v-1l2-1 1 2c1 1 0 2 0 3-1 2-2 3-3 5-3 5-9 9-13 14-1 1-2 3-4 4l-6 5c-1 2-4 4-5 5l-1-1c2-1 4-3 6-5 6-4 11-11 16-16h0c3-3 5-6 8-9-3 1-4 4-6 5l-1 1c-1 1-1 1-3 1v-1-1c1-1 2-1 3-2 1-2 3-4 4-6z" class="n"></path><path d="M574 432c1-1 2-1 4-2-2 1-3 3-4 4-2 1-4 5-7 6-2 1-4 3-6 5h-1c-2 1-5 4-6 5h2l-2 2c-1 0-2 1-3 2v1l-1-4v-6-4c-1-2 0-8 0-10l1 1 1 1 6-3 2 1v1c2 0 4 1 5 2h1c1 0 2-1 3-1 0 0 0-1 1-1 1-1 2 0 4 0z" class="p"></path><path d="M560 432c2 0 4 1 5 2h1c0 1-1 2-1 3l-2-1v2c-1 0-1 1-2 1h-1v-7z" class="S"></path><path d="M550 441c1 2 0 4 1 6h1l-1-1c0-2 0-3 1-5 1-1 2-2 4-2 1 0 1 1 2 2h0c-1 2-1 3-3 5l-1 1h1c1-1 3-3 5-2-2 1-5 4-6 5h2l-2 2c-1 0-2 1-3 2v1l-1-4v-6-4z" class="T"></path><path d="M555 446c-1-1-2-2-2-3v-1c2-1 3-1 5-1-1 2-1 3-3 5z" class="N"></path><path d="M574 432c1-1 2-1 4-2-2 1-3 3-4 4-2 1-4 5-7 6-2 1-4 3-6 5h-1c-2-1-4 1-5 2h-1l1-1c2-2 2-3 3-5h0l2-1v-1h1c1 0 1-1 2-1v-2l2 1c0-1 1-2 1-3 1 0 2-1 3-1 0 0 0-1 1-1 1-1 2 0 4 0z" class="m"></path><path d="M561 439l2 1c0 2 0 2-1 2h-1v-1c0-1 0 0-1-1v-1h1z" class="M"></path><path d="M569 433s0-1 1-1c1-1 2 0 4 0-3 3-5 5-8 7-1-1-2-1-3-1v-2l2 1c0-1 1-2 1-3 1 0 2-1 3-1z" class="i"></path><path d="M569 423c6-6 11-12 15-19 0 1 1 2 2 3l1-1c1 0 3 1 5 0 2 0 3-1 5 0v1c0 1 1 2 1 3l-2 2v2c-1 1-1 2-1 3-1 2-3 3-4 5 0-1 1-2 0-3l-1-2-2 1v1c1 1 1 2 1 3l-1 1c-1 0-1-1-2-2-1 2-3 4-4 6-1 1-2 1-3 2h0l-1 1h0c-2 1-3 1-4 2-2 0-3-1-4 0-1 0-1 1-1 1-1 0-2 1-3 1h-1c-1-1-3-2-5-2v-1l-2-1c4-2 7-4 11-7z" class="b"></path><path d="M586 421c1 0 1-1 1-2v-1-1l1-1 2-2c1 0 0 1 1 0h-2v-1s1-1 2-1c1-1 3-1 5 0v2c-1 1-1 2-1 3-1 2-3 3-4 5 0-1 1-2 0-3l-1-2-2 1v1c1 1 1 2 1 3l-1 1c-1 0-1-1-2-2z" class="k"></path><path d="M587 406c1 0 3 1 5 0 2 0 3-1 5 0v1c-2 0-4 1-6 1h-2c-1 0-1-1-2-1v1 1h0l-2 2h1 1l-1 2-1 1h0v2 2 1l1 1h-2c-1 0-2 1-3 1l1 1c-1 1-1 1-3 1l-2-2c-1-1 0-2 0-3l2-1c1 0 1 0 3 1h0v-1c1-2 1-3 1-4-1-2 1-3 1-4l2-2 1-1z" class="j"></path><path d="M569 423c6-6 11-12 15-19 0 1 1 2 2 3l-2 2c0 1-2 2-1 4 0 1 0 2-1 4v1h0c-2-1-2-1-3-1l-2 1c0 1-1 2 0 3l2 2c-1 1-3 2-4 3 0-1 1-2 1-3s0-2-1-3-3 2-4 3h-2z" class="O"></path><path d="M582 422h1v2c-1 1-4 4-4 5l-1 1h0c-2 1-3 1-4 2-2 0-3-1-4 0-1 0-1 1-1 1-2-1-3-1-5-2 3-2 7-4 11-5 1-1 3-2 4-3 2 0 2 0 3-1z" class="M"></path><path d="M569 423h2c1-1 3-4 4-3s1 2 1 3-1 2-1 3c-4 1-8 3-11 5 2 1 3 1 5 2-1 0-2 1-3 1h-1c-1-1-3-2-5-2v-1l-2-1c4-2 7-4 11-7z" class="f"></path><path d="M608 418c2 0 4 0 6-1 1-2-1-2 1-3l1 1-1 2h2c0 1-1 2-1 2 1 0 1 0 2-1l1 1-1 1-4 6-4 4-5 7c1 0 2 0 2-1 1-1 2-1 3-2v3l-2 2c0 1 0 1-1 2l-1 1h-1c0 1-1 2-2 3v1h-1c-3 4-4 5-6 9l-1 1 1 1-2 7-2 7-3 7c0 1-1 2-1 3l-5 8c0-2 1-4 1-5s0-1-1-2c0-1 0-2 1-3-2-3-5-4-7-5l-6-2-1-1c-5 4-9 7-14 10-1 1-2 1-3 2 0-1 1-2 2-3l1-1-2-1 2-2h0c-1-1-1-2-2-2h0c0-2 2-4 3-6l2-2-1-1c1-2 7-6 9-8h-1c-2 1-4 3-6 3l2-2c0-1 0-2 1-3 1-2 2-3 2-5l6-5c2-1 3-3 4-4 4-5 10-9 13-14 1-2 2-3 3-5s3-3 4-5h0c2 0 3-1 5-2v1h1l-1 2h-1-1v1 1c-1 1-1 2-1 3l10-9h0 1l1 1-2 1h1v2z" class="V"></path><path d="M585 457h0c-1 0-1 0-2-1l2-2c1 0 1-1 1-1l6-6h3l-5 6-5 4z" class="I"></path><path d="M590 453h3c0 2-1 3-2 5l-1 1-1-2c-2 1-9 10-11 9l-3 4c-1 1-3 0-4 2l-1-1 15-14 5-4z" class="R"></path><path d="M594 438c0 2-1 2 1 3l1-1h0c-1-1-1-1-1-2 2-1 4 0 5-1h1c-1 1-1 1-2 1-2 1-6 5-7 7 0 1-1 1-2 2-1-1-1-2-2-2l-1 1c-1 1-3 3-5 4-1 2-2 2-4 2 1-1 3-4 4-5v-1c1-1 2-1 2-2l3-3c3 0 5-2 7-3z" class="I"></path><path d="M605 437c1 0 2 0 2-1 1-1 2-1 3-2v3l-2 2c0 1 0 1-1 2l-1 1h-1c0 1-1 2-2 3v1h-1c-3 4-4 5-6 9l-1 1v1h-1c-1 2-2 4-2 6l-1-1h0v-4c1-2 2-3 2-5h-3l5-6c4-3 6-6 10-10z" class="K"></path><path d="M578 452c2 0 3 0 4-2 2-1 4-3 5-4l1-1c1 0 1 1 2 2-3 4-8 8-12 12-3 2-6 4-9 7-2 2-4 4-7 4 1-1 2-3 3-4 0-1 0-2 1-3h2v-1c2-4 7-7 10-10z" class="B"></path><path d="M557 468c3-2 6-5 8-7l15-14c1-1 2-3 3-3h1c0 1-1 1-2 2v1c-1 1-3 4-4 5-3 3-8 6-10 10v1h-2c-1 1-1 2-1 3-1 1-2 3-3 4 3 0 5-2 7-4h1c-3 3-5 6-9 7-2 1-3 3-5 3h0c-1-1-1-2-2-2h0c0-2 2-4 3-6z" class="q"></path><path d="M595 457v-1l1 1-2 7-2 7-3 7c0 1-1 2-1 3l-5 8c0-2 1-4 1-5s0-1-1-2c0-1 0-2 1-3-2-3-5-4-7-5l-6-2c1-2 3-1 4-2l3-4c2 1 9-8 11-9l1 2 1-1v4h0l1 1c0-2 1-4 2-6h1z" class="X"></path><path d="M581 471h2l1 1-1 1c-1 1-1 1-2 1v-1-2z" class="B"></path><path d="M595 457v-1l1 1-2 7-2 7-3 7c0 1-1 2-1 3l-5 8c0-2 1-4 1-5s0-1-1-2c0-1 0-2 1-3l1-1v-2l5-17 1-1v4h0l1 1c0-2 1-4 2-6h1z" class="U"></path><path d="M591 458v4h0l1 1c0-2 1-4 2-6h1c0 2-2 4-2 6-1 1-1 1-1 2-1 1-1 3-1 4-1 2-2 3-2 5-1 1-1 2-1 3-1 0-1 0-3-1l5-17 1-1z" class="P"></path><path d="M608 418c2 0 4 0 6-1 1-2-1-2 1-3l1 1-1 2h2c0 1-1 2-1 2 1 0 1 0 2-1l1 1-1 1-4 6-4 4h0c-2 2-5 4-6 6v1h-1l-1-1c2-2 3-4 5-5v-1c-2 1-2 0-3 0v2c-1 0-1 1-2 1h-5c-1 0-1 0-3 1v3 1c-2 1-4 3-7 3l-3 3h-1c-1 0-2 2-3 3l-15 14c-2 2-5 5-8 7l2-2-1-1c1-2 7-6 9-8h-1c-2 1-4 3-6 3l2-2c0-1 0-2 1-3 1-2 2-3 2-5l6-5c2-1 3-3 4-4 4-5 10-9 13-14 1-2 2-3 3-5s3-3 4-5h0c2 0 3-1 5-2v1h1l-1 2h-1-1v1 1c-1 1-1 2-1 3l10-9h0 1l1 1-2 1h1v2z" class="j"></path><path d="M596 429c2 0 3-1 5 1v2l1 1h-5c-1 0-1 0-3 1v3 1c-2 1-4 3-7 3l3-3c1-2 2-2 3-3l3-6z" class="h"></path><path d="M608 418c2 0 4 0 6-1 1-2-1-2 1-3l1 1-1 2h2c0 1-1 2-1 2 1 0 1 0 2-1l1 1-1 1-4 6-4 4h0c-2 2-5 4-6 6v1h-1l-1-1c2-2 3-4 5-5v-1c-2 1-2 0-3 0v2c-1 0-1 1-2 1l-1-1v-2c-2-2-3-1-5-1 1-1 3-3 5-4 2-2 4-5 7-7z" class="k"></path><path d="M604 430c0-1 1-1 1-2l-1-1v-1c2-2 5-4 8-5h1s1-1 2-1v1h-2c-1 1-1 1-2 1-1 1-2 1-2 3h0l2-1 1 2 1-1 1 1-4 4h0c-2 2-5 4-6 6v1h-1l-1-1c2-2 3-4 5-5v-1c-2 1-2 0-3 0z" class="M"></path><path d="M595 417h0c2 0 3-1 5-2v1h1l-1 2h-1-1v1 1c-1 1-1 2-1 3-2 3-5 7-6 10-1 1-2 2-2 3-7 7-14 15-22 21h-1c-2 1-4 3-6 3l2-2c0-1 0-2 1-3 1-2 2-3 2-5l6-5c2-1 3-3 4-4 4-5 10-9 13-14 1-2 2-3 3-5s3-3 4-5z" class="V"></path><path d="M595 417h0c2 0 3-1 5-2v1h1l-1 2h-1-1v1 1c-1 1-1 2-1 3-2 3-5 7-6 10-1 1-2 2-2 3l-1-2 2-2c0-2 1-3 2-5l2-4c-2 1-3 3-5 4h-1c1-2 2-3 3-5s3-3 4-5z" class="i"></path><path d="M577 441l11-10h0c0 1 0 1-1 2s-1 1-1 2c-1 2-3 3-4 5s-3 4-5 6l-8 8c-1 1-3 2-3 3-2 1-4 3-6 3l2-2c0-1 0-2 1-3 1-2 2-3 2-5l6-5c2-1 3-3 4-4h2z" class="G"></path><path d="M575 441h2v2c-1 2-5 5-6 6v-4c2-1 3-3 4-4z" class="D"></path><path d="M571 445v4c-3 1-5 3-7 5-1 2-1 3-2 4 0-1 0-2 1-3 1-2 2-3 2-5l6-5z" class="V"></path><path d="M662 439s1-2 2-2h4c2 2 3 2 5 4 0 1 0 1-1 2l1 2 1 1c-1 1-1 3-1 4l-1 1c0 1 0 1-1 2v1 1 1c1-1 1-2 1-3l2-4c1-1 1-1 2-3h0c1 0 1 1 2 1l1-1h0c1 2-1 6-2 8l-6 13c-1 2-2 3-2 5v1c-1 3-5 7-3 11v1h1l-3 4 1 1 2-2v1l-2 2c-2 2-3 4-6 6l1 2c1 0 2-1 3-1l1 1c2-1 3-2 4-3 1 0 1 1 2 1v6c-1 0-3 1-4 2-2 0-3 1-4 1-3 0-7 0-11-1h-1v-3l1-1v-1l-2-2c-1 0-2-1-3-2h1c-1-2-1-2-2-3 1 0 1-1 3-2l-3-6 3 2v-1l-3-6c0-2-1-3-2-5l-8-10-5-9-1 1c-3 1-5 2-7 2-2 1-3 1-4 2l-1-1 3-2c0-2-2-6-2-9h1 0l2 9h1v-2c1 0 1 0 1 1 1 0 1-1 1-1 1 0 1 0 2 1 0-1 1-3 2-4h1v1l1 1c0-1 1-2 2-3 1 0 3 0 4-1l3-4 3-3c1 1 2 2 3 2 1 1 1 1 2 1 1 1 1 1 2 1 2-3 3-5 4-8v-2c1 1 3 2 4 2l1 1 1-1v3c1-1 2-2 3-4z" class="l"></path><path d="M618 449h1 0l2 9h1v-2c1 0 1 0 1 1 1 0 1-1 1-1 1 0 1 0 2 1 0-1 1-3 2-4h1v1l1 1c2 1 3 3 5 5 4 6 8 12 12 19l4 11h-1c-1-1-1-2-2-4l-3-6c0-2-1-3-2-5l-8-10-5-9-1 1c-3 1-5 2-7 2-2 1-3 1-4 2l-1-1 3-2c0-2-2-6-2-9z" class="S"></path><path d="M678 447l1-1h0c1 2-1 6-2 8l-6 13c-1 2-2 3-2 5v1c-1 3-5 7-3 11v1h1l-3 4c-2 2-4 6-6 7-2-2-3-5-3-7v-2l-1-1v-1c0-1 0-2-1-3h0v-1c1 1 1 1 2 1 2-2 5-5 8-7 8-7 12-18 15-28z" class="M"></path><path d="M647 479h2v3h2c0 2 0 4 1 6h2v-2l1 1v2c0 2 1 5 3 7 2-1 4-5 6-7l1 1 2-2v1l-2 2c-2 2-3 4-6 6l1 2c1 0 2-1 3-1l1 1c2-1 3-2 4-3 1 0 1 1 2 1v6c-1 0-3 1-4 2-2 0-3 1-4 1-3 0-7 0-11-1h-1v-3l1-1v-1l-2-2c-1 0-2-1-3-2h1c-1-2-1-2-2-3 1 0 1-1 3-2l-3-6 3 2v-1c1 2 1 3 2 4h1l-4-11z" class="C"></path><path d="M650 505v-3l1-1 1 3v1h-1-1z" class="H"></path><path d="M648 491c1 2 1 3 2 5s2 3 4 5c1 0 1 0 1 1-1-1-2-1-4-2l-2-2c-1 0-2-1-3-2h1c-1-2-1-2-2-3 1 0 1-1 3-2z" class="P"></path><path d="M647 479h2v3h2c0 2 0 4 1 6h2v-2l1 1v2c0 2 1 5 3 7 2-1 4-5 6-7l1 1 2-2v1l-2 2c-2 2-3 4-6 6l1 2c1 0 2-1 3-1l1 1c-1 1-3 2-5 3h0-2v-3c-1-1-1-1-2-1-1-1-2-2-3-2 0-2 0-4-1-5v-1l-4-11z" class="I"></path><path d="M659 502v-1h-1c0-2 0-3 1-4l1 2c1 0 2-1 3-1l1 1c-1 1-3 2-5 3z" class="F"></path><path d="M662 439s1-2 2-2h4c2 2 3 2 5 4 0 1 0 1-1 2l1 2 1 1c-1 1-1 3-1 4l-1 1c0 1 0 1-1 2v1 1 1c-1 3-2 5-4 8-1 1-3 3-4 5s-6 5-8 7c-1 0-2 1-2 2h0l-1 1-2-6-2-2c0-1-1-2-2-3l-2-3c0-1-1-2-1-3h-1v-1c0-1 0-1-1-1-1-1-2-2-2-3h1c1-2 2-2 4-3h0l4-5-1-2c1 1 1 1 2 1 2-3 3-5 4-8v-2c1 1 3 2 4 2l1 1 1-1v3c1-1 2-2 3-4z" class="G"></path><path d="M646 468l1-1c1-2 2-3 4-4l1 1c-1 2-2 2-4 4l1 1s1 0 2-1l1-1c2 0 3 0 5-1v1c-1 1-2 2-2 3h0c-1 1-1 2-1 3l-1-1c0-1 0-1-1-2l-1 1 1 1c-1 1-1 1-2 1h0l-2-2c0-1-1-2-2-3zm-3-8l2-3c1-1 3-3 5-4h1l1 1 1 2h-1c-2 2-4 7-7 8v-1l-2-1h-1v-1c0-1 0-1 1-2v1z" class="J"></path><path d="M643 460l2-3c1-1 3-3 5-4h1l1 1c-1 3-5 5-7 7-1 0-1 0-2-1z" class="c"></path><path d="M665 441c1-1 2-1 3 0s2 2 3 4c0 0 0 1 1 1l1-1 1 1c-1 1-1 3-1 4l-1 1c0 1 0 1-1 2v1 1 1c-1 3-2 5-4 8-1 1-3 3-4 5s-6 5-8 7c-1 0-2 1-2 2h0l-1 1-2-6h0c1 0 1 0 2-1l-1-1 1-1c1 1 1 1 1 2l1 1c0-1 0-2 1-3h0c0-1 1-2 2-3v-1-1c0-1-1-2 0-3 1 0 1 2 2 2l2-1v1h1l1-1c1-1 1-2 2-3 1-2 2-3 3-5v-1c1-2 2-7 1-9 0-2-1-3-3-4h-1z" class="D"></path><path d="M665 441c1-1 2-1 3 0s2 2 3 4c0 0 0 1 1 1 0 4-4 12-7 16 0 0-1 1-2 1 1-1 1-2 2-3 1-2 2-3 3-5v-1c1-2 2-7 1-9 0-2-1-3-3-4h-1z" class="n"></path><path d="M662 439s1-2 2-2h4c2 2 3 2 5 4 0 1 0 1-1 2l1 2-1 1c-1 0-1-1-1-1-1-2-2-3-3-4s-2-1-3 0h1c2 1 3 2 3 4 1 2 0 7-1 9v1c-2 1-3 2-4 4-1 1-1 2-2 3l-2-1c-1 0-2-1-3-2v-1c-1-2-3-4-5-6l-1 1h-1c-2 1-4 3-5 4l-2 3v-1c-1 1-1 1-1 2 0-1 0-1-1-1-1-1-2-2-2-3h1c1-2 2-2 4-3h0l4-5-1-2c1 1 1 1 2 1 2-3 3-5 4-8v-2c1 1 3 2 4 2l1 1 1-1v3c1-1 2-2 3-4z" class="F"></path><path d="M662 442h0c1-1 2-1 3-1h1c2 1 3 2 3 4 1 2 0 7-1 9v1c-2 1-3 2-4 4-1 1-1 2-2 3l-2-1c3-1 4-4 5-7-1-2 0-3-1-6-1 0-2-1-3-2 1-1 1-3 1-4z" class="H"></path><path d="M662 442h0c1-1 2-1 3-1h1c-1 1-1 1-2 3v4c-1 0-2-1-3-2 1-1 1-3 1-4z" class="I"></path><path d="M662 439s1-2 2-2h4c2 2 3 2 5 4 0 1 0 1-1 2l1 2-1 1c-1 0-1-1-1-1-1-2-2-3-3-4s-2-1-3 0c-1 0-2 0-3 1h0c-2 0-2 1-3 3l4 5h-1l-3-3h-1c0 2 2 4 3 5h1v1c0 1-1 1-2 2h-1c-1-2-3-6-5-6l1-3c-1 0-1 0-1-1h-2c-1 1-2 3-3 5h0c-2 1-4 4-5 4h0l4-5-1-2c1 1 1 1 2 1 2-3 3-5 4-8v-2c1 1 3 2 4 2l1 1 1-1v3c1-1 2-2 3-4z" class="B"></path><path d="M546 404c2 2 5 4 8 5l-1 2c0 4-1 7-1 11v1c-1 1 0 1-1 1 0-2 0-4-1-6v7 4 2c0 2-1 8 0 10v4 6l1 4v-1c1-1 2-2 3-2 0 1 0 2-1 3v4l2-2 4-3 1 1c1-1 4-3 5-5 0 2-1 3-2 5-1 1-1 2-1 3l-2 2c2 0 4-2 6-3h1c-2 2-8 6-9 8l1 1-2 2c-1 2-3 4-3 6h0c1 0 1 1 2 2h0l-2 2 2 1-1 1c-1 1-2 2-2 3 1-1 2-1 3-2l1 1 5 1h0l-2 1h0c0 1 0 2-1 3h0l3 2c3 2 5 3 7 6v1h1c0-1 1-1 1-2l1 1 1 1c-2 2-5 3-4 6v2h1c-2 2-5 4-8 5l-1 1-2 1h0 0c-2 3-6 3-6 6v1 1 2l-1 11-31 1c-4 0-8 0-12-1h11 5 7l-3-14c-1-2-2-5-3-7 0-2 0-4-1-6v-5h0v-1c1-1 2-1 3-1v6h-1c0-2 1-4 0-5h-1c-1 2 0 5 1 7v1c1 2 1 5 2 7 1 1 2 1 4 1 1-2 0-5 0-7v-19l1-43-1-12v-2h0v-4l1-7v-6h1c0-1-1-1-1-2h0c-1-1 0-2 0-3v-1l1-1c3 0 3 0 5 2 2 0 2 0 3-1h0v-1l1-2 2-2z" class="G"></path><path d="M550 451l1 4v-1c1-1 2-2 3-2 0 1 0 2-1 3v4h0c-1 1-2 1-2 1-1 2 0 4 0 7h-1c-1-5 0-11 0-16z" class="P"></path><path d="M546 454c0 1 0 3-1 4h1c0 1 0 2-1 3v3s-1-1-1-2v-1-3h1v-1l-1 1-1 4v20h0c-1-2 0-5 0-7-1-5 0-9 0-14v-3c0-1 1-1 1-2-1-1-1-1-1-2h3z" class="I"></path><path d="M542 505h0v3l1-1v1c-1 5 0 9 0 14h-1v-5c-1 0-1-1-1-2-1-1 0-2 0-3 1-2 0-9 0-11 1-2 0-7 1-9v13z" class="F"></path><path d="M550 497c1 2 2 4 2 6h1l1-1 1 3 2-1h2c-2 2-4 4-5 6l-4 8v-21z" class="f"></path><path d="M550 494l1 1c1 0 2 1 2 2 1 1 0 0 2 0v1c1 1 3 0 4 0h1c2 0 3 0 4 1-1 1-3 4-5 5h-2l-2 1-1-3-1 1h-1c0-2-1-4-2-6v-3z" class="p"></path><path d="M533 428c1-1 1-2 2-2 1-1 1-1 2-1h1v-3h0c1-1 0-1 1-2v-3h1v2c0 2 1 4 1 6l1 1-1 2h0l-1-1v-2h0-1v1c0 3-2 6-3 8-1 0-1 0-1 1h1v1h0c-2 3-2 6-2 10l-1-12v-2h0v-4z" class="e"></path><path d="M569 495v1h1c0-1 1-1 1-2l1 1 1 1c-2 2-5 3-4 6v2h1c-2 2-5 4-8 5l-1 1-2 1h0l-4 1h-1l4-4c-2 1-3 1-4 2h0c1-2 3-4 5-6 2-1 4-4 5-5l1-2c1-1 2-1 4-2z" class="W"></path><path d="M558 508h0c2-1 3-2 5-3v1c0 1-1 2-1 3l-1 1-2 1h0l-4 1h-1l4-4z" class="c"></path><path d="M541 428h0l1 3c-1 2-1 6 0 7 1 0 1-1 2-2-1-1-1-1-1-2 2-1 2-2 3-4 1-1 2-1 4-1v2c0 2-1 8 0 10v4-1c-1-2 0-7-1-9h0c0-2-1-4 0-5h-1c-1 0-1 0-2 1 0 1 0 2 1 3-1 4-2 9-2 13l1 7h-3c0 1 0 1 1 2 0 1-1 1-1 2v3c0 5-1 9 0 14 0 2-1 5 0 7l-1 15c0 2 1 5 0 8v-13c1-7 0-14 0-21l-1-35c-1-1-1-1-1-2 1-1 1-5 1-6z" class="V"></path><path d="M543 454v-1-6h1v1l1 1v-2l1 7h-3z" class="D"></path><path d="M546 404c2 2 5 4 8 5l-1 2c0 4-1 7-1 11v1c-1 1 0 1-1 1 0-2 0-4-1-6v7 4c-2 0-3 0-4 1-1 2-1 3-3 4 0 1 0 1 1 2-1 1-1 2-2 2-1-1-1-5 0-7l-1-3 1-2-1-1c0-2-1-4-1-6v-2h-1v3c-1 1 0 1-1 2h0v3h-1c-1 0-1 0-2 1-1 0-1 1-2 2l1-7v-6h1c0-1-1-1-1-2h0c-1-1 0-2 0-3v-1l1-1c3 0 3 0 5 2 2 0 2 0 3-1h0v-1l1-2 2-2z" class="M"></path><path d="M534 410c2-1 2-2 4-1 1 1 2 2 4 2v1c-1 0-1 1-1 2h-1l-1-2-2 1h-3 0c-1-1 0-2 0-3z" class="S"></path><path d="M542 411c2-1 3-2 5-3 1 1 2 1 3 3v3h0c-1 0-2-1-3-1-1 1-1 1-2 1l-1 1h-1v-1h1c-1-1-1-2-2-3z" class="k"></path><path d="M546 404c2 2 5 4 8 5l-1 2c0 4-1 7-1 11v1c-1 1 0 1-1 1 0-2 0-4-1-6v-4-3c-1-2-2-2-3-3-2 1-3 2-5 3h0c-2 0-3-1-4-2-2-1-2 0-4 1v-1l1-1c3 0 3 0 5 2 2 0 2 0 3-1h0v-1l1-2 2-2z" class="f"></path><path d="M542 431l1-1v-1-2h0l1-1v1h1v-1h-1l2-2v-1l-1 1h-1v-1l3-1-2-2 1-1v-1-1c2 0 1 0 2 1h2v7 4c-2 0-3 0-4 1-1 2-1 3-3 4 0 1 0 1 1 2-1 1-1 2-2 2-1-1-1-5 0-7z" class="h"></path><path d="M546 430v-1-3l2-1h2v4c-2 0-3 0-4 1z" class="C"></path><path d="M565 450c0 2-1 3-2 5-1 1-1 2-1 3l-2 2c2 0 4-2 6-3h1c-2 2-8 6-9 8l1 1-2 2c-1 2-3 4-3 6h0c1 0 1 1 2 2h0l-2 2 2 1-1 1c-1 1-2 2-2 3 1-1 2-1 3-2l1 1 5 1h0l-2 1h0c0 1 0 2-1 3h0l3 2c3 2 5 3 7 6-2 1-3 1-4 2l-1 2c-1-1-2-1-4-1h-1c-1 0-3 1-4 0v-1c-2 0-1 1-2 0 0-1-1-2-2-2l-1-1v-27h1c0-3-1-5 0-7 0 0 1 0 2-1h0l2-2 4-3 1 1c1-1 4-3 5-5z" class="T"></path><path d="M557 482l5 1h0l-2 1h0c0 1 0 2-1 3h0c-3 0-4 1-6 3h0-1v-2c1-2 1-3 2-4l3-2z" class="F"></path><path d="M557 482l5 1h0l-2 1h0c-1 0-1 1-2 1-1 1-1 1-3 1l-1-2 3-2z" class="Y"></path><path d="M558 465l1 1-2 2c-1 2-3 4-3 6h0c1 0 1 1 2 2h0l-2 2c-1 0-1 1-2 2v2h-1c0-3-1-8 0-11 1 0 2-1 3-2h0l4-4z" class="K"></path><path d="M565 450c0 2-1 3-2 5-1 1-1 2-1 3l-2 2c2 0 4-2 6-3h1c-2 2-8 6-9 8l-4 4v-3c0-2 0-5 1-6 0-2 3-4 5-5 1-1 4-3 5-5z" class="X"></path><path d="M501 564l1-2c2-1 6-1 8-1h17l27 1c16 0 33-1 47 8 7 5 12 11 15 18 2 4 4 9 5 13-1 1-2 1-3 2h-1l1 4v9c-1 5-2 11-5 16-2 4-4 7-8 9v2l2 1 1-1c1 1 2 1 3 1h3c2 0 3 0 5 1h2 3l-2-2 21 12 6-5v-2c-1 0-1 0-2-1v-1c1 0 3 1 3 2l1 2h2l1 2v2l2-1 4-4c-1 2-2 3-4 5h0l1 2-2 3c0 1 0 1-1 2h1c1-1 2 0 3 0-1 2-3 3-4 5l-6 6-5 3 1 1c2 1 4 4 6 4l2 1-1 4 2-1 1 1 1 1c2 0 2 2 3 3l-2 2h-1l-2 2-2 1c-1-1-2-1-3-2l-5 5 1 3c-2 1-3 2-4 3l-2 2v1h-1-3 0c1 3 2 5 2 7-1 3-2 6-5 7l1 2-1 1c1 0 1 0 2 1s2 1 3 1c1 1 4 0 6 0 1 0 2 1 3 1s2 0 3 1h1c0 1-1 3-1 4v4h0 2c-2 2-4 2-4 5-4 2-8 4-12 7l-5 5 2 1 1-1h2 0 2c1 1 3 2 4 2l1-1c1 0 1 1 1 1l3 1h0c-1 2-1 3-2 4l-1 1h1l1 1c0 1 0 2 1 3l-1 1v2c-2 1-3 3-4 5h-1l-5 1-2 2-1 1v1c-2-1-2-1-4-1-1 1-1 1-1 2h0-2v2 1c0 1-1 1-2 2-1 2-2 2-3 2h-1l-1 1c0 1 1 2 1 2l-2 2-4-1c-1 1-1 2-3 2 0 1-1 2-1 3-1 1-1 3-2 4-2 1-4 4-7 4-1 0-1 0-2-1l-1-1-1 1 2 12h0c0-1 0-2 1-3l-1 17v1l-1-1c0 1-1 2-1 2h-1c0 3-2 6-3 9l1 2 1 1 1 1c-4 8-10 15-18 19-4 3-9 5-13 7h-2v-2h-1c-4 0-6 1-9 3h-1-1v1-2-1l-2 3c-1 1-2 2-3 4l-1 1-1 2h3l-1 3c-2 2-2 5-2 8-1 1-1 2-2 3-8-8-14-18-19-27l-7-16c-3-9-5-19-6-28s-1-18-1-26v-41-130-33c0-5 1-10 0-15 0-3-3-9-6-11z" class="s"></path><path d="M517 623h-2l-1-2 1-1 2-1v4z" class="T"></path><path d="M517 619h4v3c1 1 1 1 2 1h-6v-4z" class="a"></path><path d="M547 615v-1c1 0 2 0 3 1v12-7l-3-2h1c0-2 0-2-1-3z" class="e"></path><path d="M586 788l3-1c2 0 2 0 4-1v3 3l-2 1-3-4-2-1z" class="K"></path><path d="M593 786h0c1-1 1-1 3-2h1c1-2 5-3 7-4l-3 3c-1 1-2 1-2 2-2 1-3 1-4 2v1l-2 1v-3z" class="T"></path><path d="M560 838l8 2c0 1 0 2-1 3h-1 0c-2 0-2-1-3 0-1-1-2-1-4-1h0-1c-1-2 0-1-2-2 0 0-1-1-2-1 2 0 4 0 5 2h0 1 1s2 1 3 0v-1l-4-1v-1z" class="f"></path><path d="M521 619h6 2c-1 1-2 2-2 4h-3-1c-1 0-1 0-2-1v-3z" class="o"></path><path d="M527 619h2c-1 1-2 2-2 4h-3l1-3s1 0 1-1h1z" class="U"></path><path d="M542 601h-7c-1 0 0 0-1-1v-2-2h3c1 1 2 1 3 2l2 3z" class="S"></path><path d="M563 794h2c1-1 4 0 6 0v2h-2l-1 1h-9l-1 1c1-2 3-3 5-4z" class="M"></path><path d="M567 624h-6c-2 0-5-1-6-2h3v-1l-2-1h0l3-1h5l-2 1c1 2 1 3 2 4 1 0 2-1 3 0z" class="P"></path><path d="M570 737l3 1c1 1 2 1 3 2v1s1 0 2 1l-8 6 1-2c0-2 1-4 3-4l1-1-3-1s-1-1-1-2l-1-1z" class="f"></path><path d="M535 861l2-1v1c2 0 4 0 5 2v1 1h-1l-2 1c-2-1-6-3-7-5 1 0 2 0 2 1h1v-1z" class="N"></path><path d="M557 793c1-1 2-1 4-1l-1 1c1 1 2 0 4 0h0l-1 1h0c-2 1-4 2-5 4-1 1-4 2-4 2h-1l1-1 1-1v-1l-1-1 4-2-1-1z" class="f"></path><path d="M558 648h2v1l1 1-1 1-1 1v1 2c0 1 0 2-1 4 0 0 0 1 1 2v2l-3-3c0-3 1-7 2-11v-1z" class="L"></path><path d="M538 858v-1h2v1l2 1h0l-1 1c1 1 2 2 2 3v1c1 2 4 3 4 5-3-1-4-2-6-4h1v-1-1c-1-2-3-2-5-2v-1l-2 1-2-1v-1h2l2 1 1-1-1-1h1z" class="S"></path><path d="M521 675c0-1 0-1 1-2h1l1 2c2 2 4 1 7 2l-1 2-3 1h0c-1 0-2-1-2-1-2-1-2-2-4-2v-2z" class="I"></path><path d="M537 781c2-1 9-1 12 0v1l-1 1c-4 1-7 1-10 0-1-1-1-1-1-2z" class="d"></path><path d="M616 588c2 4 4 9 5 13-1 1-2 1-3 2h-1c0-4 0-7-1-10-1-2-1-3 0-5z" class="L"></path><path d="M606 602c1-1 1-1 2-1v-1c1 0 3-1 4-1h1v1c0 1-3 1-3 1 2 1 3 1 4 3h0l-2-1c-1 0-2 1-4 1l-1 1c-1 0-2 2-4 2v1c-1 1-3 2-4 3 1-2 2-3 3-4h0c1-1 1-1 2-1l-1-1v-1c1 0 2-1 2-2h1zm-38 129v4 1c-1 0-1 1-2 1 0 2-1 3-1 5h1 1l1 1-1 1h0c-2 1-4 2-5 3s-1 1-2 1c2-6 4-12 8-17z" class="g"></path><path d="M523 847h2c0 1 0 1 1 2 0 2 0 2-2 4l2-1 1 2-2 1c0 1 2 2 3 3 0 1 0 1-1 2l-1-2h0v-1l-1 1c0 2 1 4 1 6h-1c-2-5-2-11-2-17z" class="T"></path><path d="M542 859l5 4v-1l2-1 2 1c0 1-1 2-1 4l-1 1-2 3v-1c0-2-3-3-4-5v-1c0-1-1-2-2-3l1-1z" class="N"></path><path d="M547 863v-1l2-1 2 1c0 1-1 2-1 4l-3-3z" class="O"></path><path d="M561 650l1 1v2c0 1 1 1 1 2s-1 1-1 2l2 2h-1l1 2h-1l1 1c0 1 0 0-1 1l-1-1c-1 1-1 2-2 3l-1-2v-2c-1-1-1-2-1-2 1-2 1-3 1-4v-2-1l1-1 1-1z" class="j"></path><path d="M561 659h2l1 2h-1l1 1c0 1 0 0-1 1l-1-1-1-1-1-1 1-1z" class="L"></path><path d="M561 650l1 1v2c0 1 1 1 1 2s-1 1-1 2l2 2h-1-2l-1-8 1-1z" class="k"></path><path d="M541 790c1 0 2 0 4 1 1 0 1 0 2 1 0 1 0 2-1 3 0 1-1 1-2 1s-1 1-2 1c-2 0-3-1-4-2v-3h-1l1-1 3-1z" class="d"></path><path d="M537 596c3 0 7-1 9 0h1 3v4c-2 1-3 1-5 1h-3l-2-3c-1-1-2-1-3-2z" class="L"></path><path d="M606 778l2-2s1-1 2-1c1-3 3-4 5-6v-1c1 0 1-1 2-1h1c1-1 2 0 3-1 0 0 1-1 2-1 2-2 3-4 5-5 2 0 5-1 8-1l1 2h-1-1l-1 1h-2-2-2c-1 2-2 2-3 4h-1-1l-2 1h0c-1 1-2 1-4 2h-1l-2 2h-1c0 1-1 2-2 3l-2 3-3 1z" class="g"></path><path d="M540 806v-1l2-1c-1 0-2 0-2-1-1-2-1-3 0-6h1c2 1 4 0 5 1l-1 4c0 1 0 1-2 2 1 1 0 1 1 1h1l-1 1c-1 1-1 2-2 3h0c-1-1-1-2-2-3z" class="B"></path><path d="M557 793h-1v-1c2-1 4-2 6-2v1h4l1 1v-1h2 1c1 1 3 1 5 2s5 2 7 3c-1 1-1 1-2 1-1-1-1 0-2 0l-7-3c-2 0-5-1-6 0h-2 0l1-1h0c-2 0-3 1-4 0l1-1c-2 0-3 0-4 1z" class="Z"></path><path d="M553 821c0-1 1-3 0-4 0-1-1-1-1-2 1-1 2 0 3-1v-1h-3 0l-1-1c0-2 0-3 1-5l2-2h1v1h1v-2l1 1c0 2 0 2 1 3h0 2c-1 1-2 1-3 1 0-1-1-1-1-2l-1 1v1 1l-1-1c0 1 1 2 1 2 1 1 2 2 2 3h0c0 1-1 1 0 2l1 1c-1 1-1 2-2 3-1 0-2 0-3 1z" class="g"></path><path d="M542 877c-2 2-3 4-4 6-2-1-2-1-3-2v-1l-1-2c1-2 1-3 2-5v-1c-2 0-2 0-3-1v-1c1 1 2 1 4 1h1c2 1 3 3 5 4l-1 2z" class="U"></path><path d="M564 662h2v1l-2 1c0 1 0 1 1 2 1 0 2-1 3-1v1c1 0 0 0 1 1h-1c0 2 1 2 2 3v2c-2 0-2 0-3 2l1 1h-2v2l-1 1h0l-1-2h1v-2h0l1-1-3-3-3-5c1-1 1-2 2-3l1 1c1-1 1 0 1-1z" class="S"></path><path d="M568 667c0 2 1 2 2 3v2c-2 0-2 0-3 2l1 1h-2v2l-1 1h0l-1-2h1v-2c1 0 1-1 2-2-1-1-1-2-1-4l2-1z" class="M"></path><path d="M521 701h-1c-1-2 0-23 1-26v2 3h1c1 0 1 0 2 1l1 1c0 3-2 8-3 11h0c0 2 0 3-1 5v3z" class="e"></path><path d="M538 871c-2-2-4-1-6-3h-2v-1h1c1 1 1 1 2 1h3v1h3c-2-3-5-2-7-4 2 0 3 0 5 1h2l2-1c2 2 3 3 6 4v1c-1 1-2 2-3 4l-1 1c-2-1-3-3-5-4zm-5-255l1 4 1-1h2l-1 1v3 1 1 3h0c0 1-2 1-2 2v1 2 22c0 4 0 9-1 13v-13-32h-6c0-2 1-3 2-4h4v-3z" class="L"></path><path d="M535 619h2l-1 1v3 1 1 3h0c0 1-2 1-2 2v1 2l-1-8 1-5 1-1z" class="q"></path><path d="M535 619h2l-1 1c-1 2-1 3-3 5l1-5 1-1z" class="e"></path><path d="M606 778l3-1-5 8-2 2-3 2c-1 0-2 1-2 2-1 1-1 2-1 3-1 1 0 1-1 1v3c1 1 1 0 1 2l-1 1-1-4c-1-1-2-3-3-4l2-1v-3l2-1v-1c1-1 2-1 4-2 0-1 1-1 2-2l3-3 2-2z" class="U"></path><path d="M593 789l2-1v-1c1-1 2-1 4-2-1 1-2 2-2 3h-1l-1 2h0l-1 2 2 1c0 1 0 1-1 2v3c1 1 1 0 1 2l-1 1-1-4c-1-1-2-3-3-4l2-1v-3z" class="P"></path><path d="M557 805c1 0 0 0 1-1h2l2-2h3c3 0 5-1 8 1l-1 1h-3 0-1c-1 1-1 1 0 2h-1v9h-2v-7c-1-1-1-3-1-4h-1v5l-1 2-1-1c-1-1-2-1-4 0h-2v-1-1l1-1c0 1 1 1 1 2 1 0 2 0 3-1h-2 0c-1-1-1-1-1-3z" class="K"></path><path d="M642 754l3 1h0c-1 2-1 3-2 4-2 0-1-1-2-2-1 1-1 1-3 2 0-1 1-2 1-2 1 0 2 0 3-1l1 1 1-1h-5c-1 1-2 2-4 2-2-1-7-1-9 0-1 1-2 3-3 4-1 2-4 2-6 4 0 1-1 1-1 1-1 1-2 2-3 2h-1l-3 3v1c-1 0-3 1-4 1l7-7c3-2 6-3 8-5s4-4 6-5c5-2 11-2 16-3z" class="f"></path><defs><linearGradient id="AB" x1="543.511" y1="826.053" x2="539.665" y2="813.042" xlink:href="#B"><stop offset="0" stop-color="#919190"></stop><stop offset="1" stop-color="#b2afb0"></stop></linearGradient></defs><path fill="url(#AB)" d="M545 805v4c-1 6-1 13-2 19h0c-1 1-1 1-2 1l-1-2v-10-11c1 1 1 2 2 3h0c1-1 1-2 2-3l1-1z"></path><path d="M567 606c1 1 1 1 1 2l-1 1h2c0 1 0 1-1 3 1 0 1 0 2 1l-1 1h1l1-1v1l-1 1c-1 0-1 1-2 1h-6-3c-1-1-1-2 0-3l1-1-1-1v-1c1-2 3-3 5-4h2 1z" class="T"></path><path d="M521 677c2 0 2 1 4 2 0 0 1 1 2 1h0 3v1h0c0 2-1 5-1 6s1 0 0 1v2c-2 2-1 5-2 7-1 0-3 0-4-1v-1l-1-2h0c1-3 3-8 3-11l-1-1c-1-1-1-1-2-1h-1v-3z" class="V"></path><path d="M523 695c1-1 2 0 3 0 1 1 1 1 1 2-1 0-3 0-4-1v-1z" class="D"></path><path d="M565 789c2 0 4 0 6-1 1 0 1-1 2-1h1 0c2-1 6-1 8 0h0l-1 1c0 1 0 2 1 3 0 1 1 1 2 1h0l1 1-1 2 3 3h-1 0c-1-1-1-1-2-1l-2-1c-2-1-5-2-7-3s-4-1-5-2h-1-2v1l-1-1h-4v-1l3-1z" class="j"></path><path d="M565 789c2 0 4 0 6-1 1 0 1-1 2-1h1 0c2-1 6-1 8 0h-5c1 1 2 1 2 3l-3-1v1c1 1 2 1 3 1v1c-2 0-3-1-5-2-3 0-6 0-9-1z" class="O"></path><path d="M570 651c1 0 1 0 1-1h-1v-1h2c0 1 0 2 1 3-1 2-2 3-2 5v1h0l-1 3-1 4h1c-1 1 0 1-1 2-1-1 0-1-1-1v-1c-1 0-2 1-3 1-1-1-1-1-1-2l2-1v-1h-2l-1-1h1l-1-2h1l-2-2c0-1 1-1 1-2s-1-1-1-2v-2l2 1 1-1 1 2c0-1 0-2 1-3l3 1z" class="N"></path><path d="M570 651c1 0 1 0 1-1h-1v-1h2c0 1 0 2 1 3-1 2-2 3-2 5v1h0l-1 3-1-1 1-1h0v-1h-2l1-1v-1c-1 0-3 0-4-1 2-2 1-1 3-1 1 0 2-1 3-1l-1-1v-1z" class="m"></path><path d="M564 661c1-1 2-1 2-2h-1l1-2 1-1c0 1 0 1 1 1v1h2v1h0l-1 1 1 1-1 4h1c-1 1 0 1-1 2-1-1 0-1-1-1v-1c-1 0-2 1-3 1-1-1-1-1-1-2l2-1v-1h-2l-1-1h1z" class="k"></path><path d="M531 675l2 2c2 0 4-1 6 0v1 1c-2 3-3 4-3 7l-1 1v1l-1 6c-1 0-2 0-3-1-1 0-1-2-2-3h0v-2c1-1 0 0 0-1s1-4 1-6h0v-1h-3l3-1 1-2v-2z" class="B"></path><path d="M535 688c0-1-1-1-1-1-2-2-2-3-2-5 1-2 3-2 4-3v-1h3v1c-2 3-3 4-3 7l-1 1v1z" class="W"></path><path d="M538 768c3 0 5 0 7 1l2 1h0 0 1v1c-1 1-1 2-1 4h0v1l-2 1h1c1 0 2 0 3 1h0c-3 2-11 2-15 1l-1 1v-1h0c1-1 0 0 2-1l2-1h-1v-2c0-1-1-1-2-2l2-2 1-2h0l1-1z" class="Q"></path><path d="M538 768c3 0 5 0 7 1l2 1h0 0 1v1c-1 1-1 2-1 4h0c-1-2-1-3-3-4-2 0-4 0-5 1l-3 3c0-1-1-1-2-2l2-2 1-2h0l1-1z" class="f"></path><path d="M621 767h0l2-1h1l-1 1h0c-1 2-1 3-3 5-1 1-2 4-4 5h1c-2 3-4 7-6 9h-1v1l-2-1c1-1 1-2 1-3h-1 0c0-1-1-1-1-1-1 1 1 1-1 2l-2 1 5-8 2-3c1-1 2-2 2-3h1l2-2h1c2-1 3-1 4-2z" class="L"></path><path d="M611 774c1-1 2-2 2-3h1l2-2h1c2-1 3-1 4-2 0 1 0 2-1 3h-2l-1 1 1 1-1 1-1-1-3 3h-1l-1-1z" class="R"></path><path d="M609 783c1-2 3-6 5-7l1 1 1-1v1h1c-2 3-4 7-6 9h-1v1l-2-1c1-1 1-2 1-3z" class="e"></path><path d="M565 778c-1 0-1-1-2-1l1-1c1 1 1 1 3 1v-1c-2-1-2-3-2-4 1 0 2 1 3 2h0c0-1 0-1-1-2l-1-1h2l-1-1c0-1 1-2 1-3h3v4 4c1 0 1 1 2 1h3c-2-3-2-4-2-8h4c1 1 1 1 1 3h1v3h1c2-1 2-3 4-2l1 1c0 1 0 1 1 2-3 1-5 3-7 3h-1c-3 1-5 1-8 0-2 0-5-1-6 0z" class="P"></path><path d="M540 827l1 2c1 0 1 0 2-1h0v1c1 2 1 4 1 6s0 3 1 5c-1 1 0 3 0 3-1 1-1 2-1 4h1c0-1 1-1 1-2 0-2-1-7 0-8 1 0 3-1 3-1 1 0 2 0 2 1v2l-1 1-2-2c-1 1-1 0-1 1-1 2 0 4 0 6v8 1h-1c-2 0-3-1-5-1h-1c-1-1-2-1-2-3h0c-1-1-2-2-3-2v-1-1l1-1c1 0 2 0 3-1v2h1 1 2l1-2-1-1c-2-2-2-4-2-6v-6l-1-4z" class="K"></path><path d="M540 827l1 2c1 0 1 0 2-1h0v1 14c-2-2-2-4-2-6v-6l-1-4z" class="M"></path><path d="M536 845c1 0 2 0 3-1v2h1l1 1h3l1 2-1 1h-2c-1 1-1 1-1 3h-1c-1-1-2-1-2-3h0c-1-1-2-2-3-2v-1-1l1-1z" class="b"></path><path d="M520 729h-2c-2-2-6-1-8-3l9-6c2-1 5-4 7-6 0 1 0 2 2 2l3 1 2 2h-2l-3 3-2 2 1 1c0 1 0 2-1 3l-6 3v-2z" class="C"></path><path d="M528 722c0-1-1-2-1-2 0-1 1-2 2-2l2 1-3 3z" class="H"></path><path d="M526 724l1 1c0 1 0 2-1 3l-6 3v-2l6-5z" class="O"></path><path d="M524 781h2 0c0 1 1 1 1 2 0 0 0 1-1 1l1 1 1-1 1 1-1 1h1c0 1 0 1 1 2s2 3 4 3l-1 1h-1v2l1 1-2 2h1c1 1 1 2 2 3h-1-5c2 1 2 0 3 1 1 0 2 1 2 1 0 1 0 1-1 1h-1c-2 1-2 0-3 0v2h-1v1l-1-1h0l-1 1h-1v-2h2v-1h-2c-1-3 0-6 0-9v-1-2-2-3-4-1z" class="K"></path><path d="M604 785l2-1c2-1 0-1 1-2 0 0 1 0 1 1h0 1c0 1 0 2-1 3l2 1v-1h1l-2 5c0 1-1 2-1 3-1 1-1 3-2 4-2 1-4 4-7 4-1 0-1 0-2-1l-1-1c0-2 0-1-1-2v-3c1 0 0 0 1-1 0-1 0-2 1-3 0-1 1-2 2-2l3-2 2-2z" class="S"></path><path d="M595 795c1 0 0 0 1-1 0-1 0-2 1-3 0-1 1-2 2-2l3-2c-1 3-2 5-4 6v2c-2 0-2 0-3 2h1 1 1c0 1 1 1 2 1-1 0-2 1-3 2v1l-1-1c0-2 0-1-1-2v-3z" class="L"></path><path d="M608 786l2 1v-1h1l-2 5c0 1-1 2-1 3-1 1-1 3-2 4-2 1-4 4-7 4 0-1 0-1 1-1l1-1v-3h2c1-1 0-2 0-3l2-2h0l3-6z" class="q"></path><path d="M638 759c2-1 2-1 3-2 1 1 0 2 2 2l-1 1h1l1 1c0 1 0 2 1 3l-1 1v2c-2 1-3 3-4 5h-1l-5 1-2 2v-1-4h-1c-1 0-1 0-1 1h-2v-1l-2-2h-1v-2c1-2 2-2 3-4h2 2 2l1-1h1 1v-1h0l1-1z" class="U"></path><path d="M638 759c2-1 2-1 3-2 1 1 0 2 2 2l-1 1v1c-2 0-3 1-4 2v1l-1-1h0l-1-2h0 1v-1h0l1-1z" class="K"></path><path d="M644 765v2c-2 1-3 3-4 5h-1l-5 1-2 2v-1-4h1c0 1 0 0 1 1l1-1c0-1 1-2 2-2h1c1-1 1-2 0-3h2s1 1 1 2l3-2z" class="R"></path><path d="M644 765v2c-2 1-3 3-4 5h-1l-5 1c2-2 5-4 7-6l3-2z" class="l"></path><path d="M625 766v2h1l2 2v1h2c0-1 0-1 1-1h1v4 1l-1 1v1c-2-1-2-1-4-1-1 1-1 1-1 2h0-2v2 1c0 1-1 1-2 2-1 2-2 2-3 2h-1l-1 1c0 1 1 2 1 2l-2 2-4-1c-1 1-1 2-3 2l2-5c2-2 4-6 6-9h-1c2-1 3-4 4-5 2-2 2-3 3-5h0l1-1h1z" class="T"></path><path d="M611 786c2-2 4-6 6-9v1c1 1 1 1 1 2h1c0-1 0-1 2-2 1 1 1 1 2 1-1 1-1 1-1 2h1l1-1v1c0 1-1 1-2 2-1 2-2 2-3 2h-1l-1 1c0 1 1 2 1 2l-2 2-4-1c-1 1-1 2-3 2l2-5z" class="U"></path><path d="M530 848h5c1 0 2 1 3 2h0c0 2 1 2 2 3h1c2 0 3 1 5 1h1 1c3 0 3 0 5 1h8l-2 2c0 1 0 0 1 1h1c-1 0-2 0-3 1l-3 3c-2-1-5-1-7-2l-1-1v1l2 1-2 1v1l-5-4h0l-2-1v-1h-2v1h-1l1 1-1 1-2-1-1-1-1-1c-1-1-2-2-4-3h1 1l-1-1v-1c1 0 2-1 2-2h0l-2 1-1-1 1-2z" class="L"></path><path d="M538 858l-1-2h0l2 1 1-1c2 1 4 3 6 4h1l2 1-2 1v1l-5-4h0l-2-1v-1h-2v1z" class="P"></path><path d="M548 854c3 0 3 0 5 1h8l-2 2c0 1 0 0 1 1h1c-1 0-2 0-3 1-3 0-6-2-9-2h0c-1-1-1-2-1-3z" class="X"></path><path d="M597 605c3 0 7-2 9-3h-1c0 1-1 2-2 2v1l1 1c-1 0-1 0-2 1h0c-1 1-2 2-3 4v4c0 1-1 2-1 2h0l-1 1v5 3c-1-1-2-1-3-1h-2c-1-1-1 0-3 0v-4-1l-1-1c0-1-1-1-2-2l-1 1-1-2c1-1 1-1 3-1 1-1 0-1 0-3l2-2v1c0 1 0 1 1 2 1-2-1-3 0-5l2-2h1s2 0 2-1h1 1z" class="O"></path><path d="M589 611c0 1 0 1 1 2 1-2-1-3 0-5l2-2h1s2 0 2-1h1 1l-2 2s1 1 1 2h-1 0c-1-1-1-2-2-2l-1 1h0c1 1 0 1 1 2h-2v1s1 1 2 1v1h-2c-1 1-1 1-1 2l1 2h-1c0 1-1 2-1 3l-1-1c0-1-1-1-2-2l-1 1-1-2c1-1 1-1 3-1 1-1 0-1 0-3l2-2v1z" class="f"></path><path d="M589 620c0-1 1-2 1-3h1l-1-2c0-1 0-1 1-2h2l1 1-1 1h4c0 1 1 2 1 2l-1 1v5 3c-1-1-2-1-3-1h-2c-1-1-1 0-3 0v-4-1z" class="L"></path><path d="M593 615h4c0 1 1 2 1 2l-1 1v-1c-1 1-2 1-2 1-2 0-2 0-2-1v-2z" class="Z"></path><path d="M573 803c2 2 4 3 5 5h-1l-2-2c-1-1-2-1-4-1 0 0-1 1-2 1v2c1 1 0 1 0 2 1 0 2 1 3 1s1 1 2 1c1 1 1 0 2 1h1 1c0 2 1 3 1 5l-3 8v2h0 0-4-1v1c0 1 0 1-1 2s-1 1-2 0v-3l-1-8v-5-9h1c-1-1-1-1 0-2h1 0 3l1-1z" class="o"></path><path d="M567 820l3-1 1 1-1 2h0c1 0 1 0 2-1v1 1h-1c-1 1-1 1-2 1l1 1 1-1 1 1c-1 1-1 1-1 2l-1-1c-1 2-1 2-1 4h1v1c-1 1-1 1-2 0v-3l-1-8z" class="N"></path><path d="M564 639l6 1 9-1v2c2 2 0 6-1 9l-1-1c-1 0-2 0-3-1l-1 1h-1-2v1h1c0 1 0 1-1 1l-3-1c-1 1-1 2-1 3l-1-2-1 1-2-1-1-1-1-1v-1h-2l1-7 5-1h0v-1z" class="m"></path><path d="M566 641h6c-1 1-2 2-3 2 0 0-1 0-1 1l-1-1s0-1-1-2zm-3 8c1-1 1-2 1-3l1-1 3 2-2 1v1l1 1c-1 1-1 2-1 3l-1-2-2-2z" class="S"></path><path d="M568 647c1 0 2-1 3 0l2 2h-1-2v1h1c0 1 0 1-1 1l-3-1-1-1v-1l2-1z" class="L"></path><path d="M560 648l1-1-1-1c1-1 1-2 2-3h0v-2h4c1 1 1 2 1 2h-3v1l1 1h-2v4l2 2-1 1-2-1-1-1-1-1v-1z" class="j"></path><path d="M579 641c2 2 0 6-1 9l-1-1c-1 0-2 0-3-1l-2-3h-1l2-2v1h1 0c1-1 1-2 2-3h3z" class="n"></path><path d="M564 639l6 1 9-1v2h-3-4-6-4v2h0c-1 1-1 2-2 3l1 1-1 1h-2l1-7 5-1h0v-1z" class="R"></path><path d="M526 728c3 2 3 10 4 14l6 29-2 2h0-1c0-1 0-3-1-4l-3-13c-2-7-5-17-9-24v-1l6-3z" class="D"></path><path d="M568 616h5c2 1 3 0 5 0h5 1l1 2 1-1-1 1v1 2l-1 1 1 1c1 1 2 1 4 1l-1 1h-3v1c-1 0 0 0-1 1l-1-1h-1c0 1 1 1 2 2-1 1-2 1-4 1 1-1 1-2 0-3-2 0-3 2-5 1-1 1-1 2-2 3h-3v-1c1-1 1-1 1-2h-3l1 1c-1 2-3 1-4 2h-1c-1-1-2-1-3-1l-2-2c1-1 1 0 2-1 1 1 1 2 2 2h1l-1-1h1c1 0 0-1 1 0h1l1-1 1-1h1 2 0c1 0 1 0 2 1h1c1 0 1 0 2-1h2 1 1c-4-1-7 0-11-1h-1-1c-1-1-2 0-3 0-1-1-1-2-2-4l2-1h1l1-1h2 1 6 6l1 1v-2h-2-9-5l-4-1h6z" class="O"></path><path d="M565 619c2 0 12-1 13 0s1 0 1 1l2-1v1 2 1h0 1c-2 2-9 1-11 1h-1-2-1c-1-1-2 0-3 0-1-1-1-2-2-4l2-1h1z" class="Z"></path><path d="M570 630h3c1-1 1-2 2-3 2 1 3-1 5-1 1 1 1 2 0 3 0 1 0 2 1 3l-1 1h0c1 2 0 4 0 5h0l-1 1-9 1-6-1v1c-2 0-4-1-6-1l1-1-1-1c0-2 0-3 1-4l1-1v-1c1-1 1-1 1-2 1 0 2 0 3 1h1c1-1 3 0 4-2l-1-1h3c0 1 0 1-1 2v1z" class="S"></path><path d="M570 640v-3h2 0 0c1 0 1 1 2 1h0v-1l4-4v1 1h1c1 1 1 2 1 3l-1 1-9 1z" class="M"></path><path d="M561 629c1 0 2 0 3 1h1c1-1 3 0 4-2l-1-1h3c0 1 0 1-1 2v1 3l1 1h-2l-1 1c-1 0-1 0-2-1h0c-1 1-1 1-2 1l-1-1v2l-1 1 1 1-1 1c1 0 1 0 2-1v1 1c-2 0-4-1-6-1l1-1-1-1c0-2 0-3 1-4l1-1v-1c1-1 1-1 1-2z" class="N"></path><path d="M551 837c1 1 2 1 3 2h0c1 0 2 1 2 1 2 1 1 0 2 2h1 0c2 0 3 0 4 1 1-1 1 0 3 0l-2 2h-1c-1 1-1 2-2 3 1 0 1 0 2 1l1-1h0l-1 3c-1 1-1 2-2 4h0-8c-2-1-2-1-5-1h-1v-1-8c0-2-1-4 0-6 0-1 0 0 1-1l2 2 1-1v-2z" class="Z"></path><path d="M554 848l-1 1-1-1c0-2 0-3 1-5h2v4l-1 1z" class="N"></path><path d="M547 845c0-2-1-4 0-6 0-1 0 0 1-1l2 2 1-1v1 1l-2 2c0 1 1 1 0 2h0l-2-1h1-1v1z" class="b"></path><path d="M555 843h2c1 2 0 2 0 4l-1 2-1 1c-1 1-2 2-2 4l-1-2c-1-1-1-1 0-2 1 0 1 0 2-1v-1l1-1v-4z" class="L"></path><path d="M560 851c0-1 0-2 1-3 1 0 1 0 2 1l1-1h0l-1 3c-1 1-1 2-2 4h0-8v-1c0-2 1-3 2-4l1-1 1 1c1 1 1 1 3 1z" class="O"></path><path d="M556 849l1 1v3h-2v-1-2h0l1-1zm4 2c0-1 0-2 1-3 1 0 1 0 2 1l1-1h0l-1 3-2 2c-1-1-1-1-1-2z" class="f"></path><path d="M598 617h2c0-1 0-1 1-2s0 0 1 0c1-1 1-2 2-2 1-1 1-1 2-1-2 0-4 1-5 0l1-1c1-1 0 0 2-1l1-1c3 0 7 0 10 1v4l-3 1h0c0 3-1 4-1 7l-1 1h0c-1 0-1 1-1 2s-1 1-1 2h-4v2h1l1 1-2 2c-1-1-2-1-3-1-1-1-1-2-1-3h-2l-1-2v-3-5l1-1h0z" class="a"></path><path d="M601 618h0c1 0 2 1 3 1l1-1 1 2c-1 1-1 2-2 3h-1l-1 1c-1 0-2 1-3 1 1 1 1 2 1 3h-2l-1-2v-3h0c1-2 2-2 3-3 0-1 0-2 1-2z" class="o"></path><path d="M597 623h0c1-2 2-2 3-3l-1 5c1 1 1 2 1 3h-2l-1-2v-3z" class="K"></path><path d="M598 617h2c0-1 0-1 1-2s0 0 1 0c1-1 1-2 2-2 1-1 1-1 2-1-2 0-4 1-5 0l1-1c1-1 0 0 2-1l1-1c3 0 7 0 10 1v4l-3 1h0c0 3-1 4-1 7l-1 1h0c0-1-1-2-1-3l-1 1v2h-1v-3h0c1-1 1-1 3-1v-1-1h-2v1l-1-1 1-1h1v-3h-1v2l-2-1 1-1-1-1c-2 2-4 3-5 6-1 0-1 1-1 2-1 1-2 1-3 3h0v-5l1-1h0z" class="g"></path><path d="M523 827c-1-7-1-58 0-60v-1-4h1v3h1l1-1v2c-1 0-2 1-2 2v1 2 2c0 2-1 2 0 3v1c1 0 0 1 0 2v2h0v1 4 3 2 2 1c0 3-1 6 0 9h2v1h-2v2h1l1-1h0l1 1v-1h1v-2c1 0 1 1 3 0h1l2 1v1h-2c0 1 0 2 1 2h1l1 1c-1 1-1 1-3 1h-1v1h1c1 0 2 1 3 1l-1 1h-1v1c1 0 1 1 2 1v1h0c-2 0-2 0-3 1h1l2 2h0v1c-1 0-2 0-3 1l-1 1-1-1c-1 1-2 2-2 3-1 0-1 1-2 1h-1l-1 1s-1 1-1 2z" class="Z"></path><path d="M533 816l-2 1h-1c-1 0-3 0-3 1-2 1 0 4-1 5l-2-1c0-1 0 0 1-1v-1c0-1-1-2-1-3 1-2 1-1 0-3l2-3-2-1c0-1 1-1 2-1l-1-2 2-1v3 1c0 1 0 1 1 2 1 0 1 1 2 2l-1 1v1l2-1 1 1h1z" class="O"></path><path d="M551 668v2h2c1 0 3 0 4 1v2c1-1 1-1 1-2v-1l1-1 1 1h3l3 3-1 1h0v2h-1l-1 2c0 1 0 2-1 2h-1v4l1 5c-2 0-2-1-4-1l-1-3-2 2-1-5-1-1v-1-1h-3l-1-2h-1l-2 2v-1l-1-1v-1l-1 1c-1-1-2-1-3 0h-1-1c-2-1-4 0-6 0l-2-2v-2l1-1-1-1 1-1h0l1-1 1 1c-1 2-1 4-1 5h2 1 3 1v-6l1 1h0 0c2 0 3 0 4-1h0 3s1 1 2 1l1-2z" class="N"></path><path d="M561 676h0c1-1 1-2 2-3l2 1h0v2h-1l-1 2c0 1 0 2-1 2h-1c0-1 0-1 1-2v-1l-1-1z" class="C"></path><path d="M545 669h0v6h-3c-1-1-1-3-1-5h0c2 0 3 0 4-1z" class="B"></path><path d="M545 669h3s1 1 2 1h1v6c-2-1-4-2-6-1v-6z" class="I"></path><path d="M545 677c2 0 3-1 5 0h9l1 1h0c-1 2-1 2-4 2l-2 1v1l-1-1v-1-1h-3l-1-2h-1l-2 2v-1l-1-1z" class="B"></path><path d="M561 676l1 1v1c-1 1-1 1-1 2v4l1 5c-2 0-2-1-4-1l-1-3-2 2-1-5v-1l2-1c3 0 3 0 4-2h0l-1-1 2-1z" class="I"></path><path d="M554 682v-1l2-1c1 2 2 4 1 5l-2 2-1-5z" class="N"></path><path d="M591 734v-1l2-1c1 2 1 3 1 4 0 2 1 3 2 4-1 1-1 2-2 3h-1c-1 0-2 0-3-1-1 0-2 0-3 1v1h1c1 0 2 1 3 2v1l-2 1c-3 1-5 5-8 8l-6 3h-1c-3 0-5-2-7-4v-1l4 3c1-1 1-1 2-1-2-1-3-1-4-2-2-1-2-2-3-3 1-2 3-2 4-3l8-6c2-1 4-3 6-3h1l2-2-1-1h0 1l2 2 1-1c0-1 0-2 1-3h0z" class="k"></path><path d="M569 754c0-2 0-3 1-4l1 1c-1 1-1 1-1 2 2 0 2 1 3 1v-1-4h2c1 1 1 1 1 3 0 1-1 1-1 2v1l-2 1c-2-1-3-1-4-2z" class="i"></path><path d="M591 734v-1l2-1c1 2 1 3 1 4 0 2 1 3 2 4-1 1-1 2-2 3h-1c-1 0-2 0-3-1l-4-1 2-1h3v-2h-1-1l1-1c0-1 0-2 1-3h0z" class="q"></path><path d="M591 734c2 2 2 2 2 5v1l-1-1-1 1v-2h-1-1l1-1c0-1 0-2 1-3h0z" class="M"></path><path d="M593 739c0 1 1 3 0 4-1 0-2 0-3-1l-4-1 2-1h3l1-1 1 1v-1z" class="n"></path><path d="M571 757c4 0 5-1 8-2 1 0 3-2 4-3l-1-1 1-1c0 1 0 1 1 2l1-2c1-1 1-1 1-3h-1v-2c-1-1-3-1-4-1h-1v-1c2-2 3-2 6-2l4 1c-1 0-2 0-3 1v1h1c1 0 2 1 3 2v1l-2 1c-3 1-5 5-8 8l-6 3h-1c-3 0-5-2-7-4v-1l4 3z" class="i"></path><path d="M533 616c0-5 1-10 1-14 5 0 10-1 15 0 1 1 1 2 1 4v4 5c-1-1-2-1-3-1v1c1 1 1 1 1 3h-1v3c0 1 1 2 0 3l-1 1c-1-1-1-2-3-2 1 1 0 1 1 2h-2v11 1l-1 2v-10-5c-1 0-1-1-1-1v-1h-1l-3 3v-1-1-3l1-1h-2l-1 1-1-4h0z" class="M"></path><path d="M533 616c0-5 1-10 1-14 5 0 10-1 15 0 1 1 1 2 1 4v4c-2-1-3-1-4-3-2 1-3 2-5 2h-1c1-1 3-2 3-3h-1-2c0-1 1-1 1-1v-2c-1 1-1 1-2 1v-1l-1 1h-2c0 3-2 7-2 9 0 1 0 2-1 3z" class="S"></path><path d="M546 607v-1-1-1h1l1 1h-1v1h3v4c-2-1-3-1-4-3z" class="k"></path><path d="M535 619l1-1v-5h1c1-1 0-1 0-1 0-1 1-1 1-2h1c0 1 1 1 2 2v6h1c1-1 2 0 4 0v-1c-1 0-1-1-2-1v-1l2 1 1-1c1 1 1 1 1 3h-1v3c0 1 1 2 0 3l-1 1c-1-1-1-2-3-2 1 1 0 1 1 2h-2v11 1l-1 2v-10-5c-1 0-1-1-1-1v-1h-1l-3 3v-1-1-3l1-1h-2z" class="i"></path><path d="M537 619l1-1h0 2v2l-2 2h1l-3 3v-1-1-3l1-1z" class="C"></path><path d="M529 690h0c1 1 1 3 2 3 1 1 2 1 3 1 0 1-1 2 0 4h0c2 1 3 1 4 1l1 1h0v4 2h0 2v3c1 1 1 1 0 3h-2l-2 2-1-2v1l-2 1v4l-1 1-2-2-3-1c-2 0-2-1-2-2h0c-1 0-4 3-5 3-1-1-1-2 0-3l3-3v-5c-1-2-1-4-3-5v-3c1-2 1-3 1-5l1 2v1c1 1 3 1 4 1 1-2 0-5 2-7z" class="X"></path><path d="M523 696c1 1 3 1 4 1 0 2 0 5 1 7v2c-1-1-2-1-3-2v-1c1-1 1-4 1-6-1 1-2 1-3 1v-2z" class="H"></path><path d="M535 708v2c-1 0-2 0-4 1-1 2-1 3 0 6l-3-1c-2 0-2-1-2-2h0c2-1 3-2 5-4h0c1-1 2-1 3-1l1-1z" class="D"></path><path d="M539 706h0 2v3c1 1 1 1 0 3h-2l-2 2-1-2v1l-2 1v4l-1 1-2-2c-1-3-1-4 0-6 2-1 3-1 4-1h1l1-4v2l2-2z" class="I"></path><path d="M539 706h2v3c1 1 1 1 0 3h-2l-2 2-1-2v-1h1 2 0v-5z" class="F"></path><path d="M529 690h0c1 1 1 3 2 3 1 1 2 1 3 1 0 1-1 2 0 4h0c2 1 3 1 4 1l1 1h0v4 2l-2 2v-2l-1 4h-1v-2h-1c-2-1-5 1-6 1h-1l1-3v-2c-1-2-1-5-1-7 1-2 0-5 2-7z" class="Z"></path><path d="M529 690c1 1 1 3 2 3 1 1 2 1 3 1 0 1-1 2 0 4h-3c0 2 1 1 2 3v3c-1-1-2-1-3-1h-1-1c1-4 1-9 1-13z" class="I"></path><path d="M534 698h0c2 1 3 1 4 1l1 1h0v4 2l-2 2v-2-1c-1-1-3-1-4-1v-3c-1-2-2-1-2-3h3z" class="F"></path><defs><linearGradient id="AC" x1="538.534" y1="621.984" x2="537.765" y2="668.029" xlink:href="#B"><stop offset="0" stop-color="#a1a19f"></stop><stop offset="1" stop-color="#c1c0be"></stop></linearGradient></defs><path fill="url(#AC)" d="M539 622h1v1s0 1 1 1v5 10 20c-1 3-1 7-1 10v6h-1-3-1-2c0-1 0-3 1-5l-1-1v-1c1-4 1-9 1-13v-22-2-1c0-1 2-1 2-2h0v-3l3-3z"></path><path d="M536 628c1 3 1 6 1 9l-1 21c0 2 1 4 0 6h0c0 2 1 3 0 5l2-1v1h2 0v6h-1-3-1-2c0-1 0-3 1-5l-1-1v-1c1-4 1-9 1-13v-22-2-1c0-1 2-1 2-2z" class="H"></path><path d="M536 664c0 2 1 3 0 5l2-1v1h2 0v6h-1-3-1l-1-2 3-3-2-1c1-2 1-3 1-5z" class="G"></path><path d="M563 809v-5h1c0 1 0 3 1 4v7h2v5l1 8v3l-1 2-2 1h-1l-1-1c-2 0-4-1-6-1h-1 0l-1-1-1 1c2 1 3 1 4 2h-1c2 2 5 2 8 3h0c3 1 6 0 9-1 0 1-1 1-2 1-1 1-2 1-4 1v2h0l-8-2c-3-1-7-3-8-6l-3-6c-1 0-1 0-1-1h2v-2c1 0 2-1 3-2h0c1-1 2-1 3-1 1-1 1-2 2-3l-1-1c-1-1 0-1 0-2h0c0-1-1-2-2-3 0 0-1-1-1-2l1 1h2c2-1 3-1 4 0l1 1 1-2z" class="a"></path><path d="M566 827l2 1v3l-1 2-2 1h-1v-1l1-1c1-1 1-3 1-5z" class="K"></path><path d="M557 810c2-1 3-1 4 0l1 1 1-2v2 2 3h-2c1-2 0-2 0-4-2 0-3-1-4-2h0z" class="T"></path><path d="M565 815h2v5l1 8-2-1-1-1c1-1 0-9 0-11z" class="O"></path><path d="M561 826h0c1 1 0 1 1 2 0 1 0 2-1 3-1-1-1-1-3-1h-2v-4h-1v2l-1 1c-1-1-1-2-2-3l2-1h1c2-1 4 0 6 0v1z" class="k"></path><path d="M556 820c1-1 1-2 2-3l-1-1c-1-1 0-1 0-2h0c0-1-1-2-2-3 0 0-1-1-1-2l1 1h2 0c1 1 2 2 4 2 0 2 1 2 0 4v3-1l1 1-1 1 1 1v2 1l-1 2v-1-1c-2-2-4 1-6-1 1 0 1-1 2-1h1c1-1 1-1 1-2-1 0-1 1-3 1v-1z" class="j"></path><path d="M589 748c3 1 5 2 6 5h0c0 3 1 7 1 10-1 5-5 9-9 12-1-1-1-1-1-2l-1-1c-2-1-2 1-4 2h-1v-3h-1c0-2 0-2-1-3h-4l1-4c0-2 1-2 1-4l-1-1 6-3c3-3 5-7 8-8z" class="O"></path><path d="M578 765h0c0-1 0-2 1-3h0l1-1h0l1 1s1 1 1 2l-2 1h-2z" class="K"></path><path d="M581 762c2 0 3 2 5 3 0 0 1 1 1 2h0c0 1-1 1 0 3-2 0-2 1-4 1l1-1c0-1 0-2-1-3h-2-3-1c1-1 0-1 1-2h2l2-1c0-1-1-2-1-2z" class="P"></path><path d="M589 764c1 0 1-1 2-1v-1-1h1l2 2h2c-1 5-5 9-9 12-1-1-1-1-1-2l-1-1c-2-1-2 1-4 2l1-2c0-1-1-3-1-5h2c1 1 1 2 1 3l-1 1c2 0 2-1 4-1 0 0 0 1 1 1h1c1 0 1-1 2-1 0-2-1-3-1-4l-1-2z" class="R"></path><path d="M589 764c1 0 1-1 2-1v-1-1h1l2 2c0 1-1 1-2 3l1 1-1 1-2-2-1-2z" class="N"></path><path d="M589 748c3 1 5 2 6 5h0c0 3 1 7 1 10h-2l-2-2h-1v1 1c-1 0-1 1-2 1l-1-1c-1-3-3-4-5-6l-2-1c3-3 5-7 8-8z" class="a"></path><path d="M583 757c2 0 2 1 4 2h0c-1-2-2-3-3-4v-1c3 2 4 3 7 3h0v-1l3 3-2 2h0-1v1 1c-1 0-1 1-2 1l-1-1c-1-3-3-4-5-6z" class="o"></path><path d="M580 629c2 0 3 0 4-1-1-1-2-1-2-2h1l1 1c1-1 0-1 1-1v-1h3l1 1h0v4c-1-1-1-1-1-2-1 0-1 0-1 1h-2v4 7l-1 1c-1 0-2 1-2 2s0 2-1 3v1 3c-1 5-3 10-5 15 0 1 1 3 0 4s-1 2-2 4c0 1 0 1 1 2-2 2-3 3-4 5v1l-3 3-2 2v1l-1 1-1-4h-3v-4h1c1 0 1-1 1-2l1-2 1 2h0l1-1v-2h2l-1-1c1-2 1-2 3-2v-2c-1-1-2-1-2-3h1c1-1 0-1 1-2h-1l1-4 1-3h0v-1c0-2 1-3 2-5-1-1-1-2-1-3h1l1-1c1 1 2 1 3 1l1 1c1-3 3-7 1-9v-2l1-1h0c0-1 1-3 0-5h0l1-1c-1-1-1-2-1-3z" class="j"></path><path d="M574 648c1 1 2 1 3 1l1 1-6 16c0 1 0 1-1 2h1l-2 2c-1-1-2-1-2-3h1c1-1 0-1 1-2h-1l1-4 1-3h0v-1c0-2 1-3 2-5-1-1-1-2-1-3h1l1-1z" class="e"></path><path d="M573 652c0 1 1 1 2 2-1 2-3 3-4 4h0v-1c0-2 1-3 2-5z" class="n"></path><path d="M567 679l9-14c0 1 1 3 0 4s-1 2-2 4c0 1 0 1 1 2-2 2-3 3-4 5v1l-3 3-2 2v1l-1 1-1-4h-3v-4h1c1 0 1-1 1-2l1-2 1 2h0l1-1 2-1-1 3z" class="I"></path><path d="M566 677l2-1-1 3v4h0l4-3v1l-3 3-2 2v1l-1 1-1-4h-3v-4h1c1 0 1-1 1-2l1-2 1 2h0l1-1z" class="b"></path><path d="M561 680h1c1 0 1-1 1-2l1 6h-3v-4z" class="i"></path><path d="M571 794l7 3c4 3 7 6 9 12h0c0 2 1 4 0 6h-1v1c0 2-1 6-2 8-1 1-2 3-2 4h1 0c0-1 0-1 1-1 0 1 0 2-1 3v1l-3 6c-1 1-2 1-3 3l-1 1v-1c-2-1-3-1-5-1l-3 1v-2c2 0 3 0 4-1 1 0 2 0 2-1-3 1-6 2-9 1h0c-3-1-6-1-8-3h1c-1-1-2-1-4-2l1-1 1 1h0 1c2 0 4 1 6 1l1 1h1l2-1 1-2c1 1 1 1 2 0s1-1 1-2v-1h1 4 0 0v-2l3-8v4h0 1v-1-2l1-1h0c1-2 1-5 0-7v-1c0-2-1-4-2-5-2-2-4-4-5-6l-1-1-5-1 1-1h2v-2z" class="C"></path><path d="M571 839h0 1c3-1 6-3 7-5l3-3h1l-3 6c-1 1-2 1-3 3l-1 1v-1c-2-1-3-1-5-1z" class="P"></path><path d="M576 828h3c-2 2-5 4-7 5h-1c-2 0-4 0-6 1l2-1 1-2c1 1 1 1 2 0s1-1 1-2v-1h1 4 0 0z" class="U"></path><path d="M573 798c5 3 8 6 10 11 2 4 1 11-2 16 0 1-1 2-2 3h-3v-2l3-8v4h0 1v-1-2l1-1h0c1-2 1-5 0-7v-1c0-2-1-4-2-5-2-2-4-4-5-6l-1-1z" class="b"></path><path d="M579 818v4h0 1v-1-2l1-1h0c0 2 0 3-2 5v1c0 2-1 2-1 3h-1l-1-1 3-8z" class="g"></path><path d="M608 704l1 4v1c0 1 0 2-1 3-2 2-4 5-6 7h1 0l1 1-2 2-10 7v2l1 1h0l-2 1v1h0c-1 1-1 2-1 3l-1 1-2-2h-1 0l1 1-2 2h-1c-2 0-4 2-6 3-1-1-2-1-2-1v-1c-1-1-2-1-3-2l-3-1h0c1-2 2-2 3-3v-1c1-2 3-3 5-4 1-1 1-1 3-2h1c1-2 3-3 4-4 2-1 3-2 5-3s3-3 5-4c1-1 3-2 4-3l2-2c1 0 1-1 2-2h3c1-2 1-3 1-5z" class="O"></path><path d="M599 716c3-2 6-5 10-8v1c0 1 0 2-1 3-2 2-4 5-6 7l-1-1h-1c-1 0-1-1-1-2z" class="i"></path><path d="M599 716c0 1 0 2 1 2h1l1 1c-5 3-9 7-13 9l-6 3h-1-3-1c7-5 15-9 21-15z" class="h"></path><path d="M602 719h1 0l1 1-2 2-10 7v2l1 1h0l-2 1v1h0c-1 1-1 2-1 3l-1 1-2-2h-1 0l1 1-2 2h-1c-2 0-4 2-6 3-1-1-2-1-2-1v-1c-1-1-2-1-3-2l-3-1h0c1-2 2-2 3-3v-1l5-2h1 3 1l6-3c4-2 8-6 13-9z" class="L"></path><path d="M573 734v1c1 0 2 0 3-1v1 1 1h-1c-1 1-1 1-2 1l-3-1h0c1-2 2-2 3-3z" class="R"></path><path d="M592 729v2l1 1h0l-2 1v1h0c-1 1-1 2-1 3l-1 1-2-2h-1 0l1 1-2 2h-1c-2 0-4 2-6 3-1-1-2-1-2-1v-1c-1-1-2-1-3-2 1 0 1 0 2-1h1v2c2 0 4-1 6-3 1-1 2-1 3-1l7-6z" class="P"></path><path d="M596 740l1 1 1 2-1 1 1 1-1 1 1 1h1c1 0 1 1 1 2s0 1-1 2l1 2-1 1v2 2h1l1 1c0 2-1 5 0 7 0 0 2 1 2 2 0 0-1 2-1 3-1 1-3 3-5 4l1 1c1 0 2-1 3-2s1-2 2-3c0-2 1-3 3-4v1c0 1-1 1-1 2 2 0 5-5 7-6l1 1h0l-1 2-7 7c-6 4-11 5-17 7-6 1-11 2-17 1-3 0-6-1-8-3h1 0c3 1 6 2 10 2l-1-1h-2-1c-2-1-3-1-5-2 1-1 4 0 6 0 3 1 5 1 8 0h1c2 0 4-2 7-3 4-3 8-7 9-12 0-3-1-7-1-10h0c-1-3-3-4-6-5l2-1v-1c-1-1-2-2-3-2h-1v-1c1-1 2-1 3-1 1 1 2 1 3 1h1c1-1 1-2 2-3z" class="V"></path><path d="M596 740l1 1 1 2-1 1 1 1-1 1 1 1h1c1 0 1 1 1 2s0 1-1 2l1 2-1 1v2h0c-1 0-2 1-2 2v1c0 2 1 5 0 7h0l-1 3-1 2-3 3c-1 1 0 0-1 2-1 0-1 1-2 1l-1 1c-1 0-2 0-2 1h-1c-1-1-1-1-2 0h-4c-2 1-3 0-6 1h-2-1c-2-1-3-1-5-2 1-1 4 0 6 0 3 1 5 1 8 0h1c2 0 4-2 7-3 4-3 8-7 9-12 0-3-1-7-1-10h0c-1-3-3-4-6-5l2-1v-1c-1-1-2-2-3-2h-1v-1c1-1 2-1 3-1 1 1 2 1 3 1h1c1-1 1-2 2-3z" class="H"></path><path d="M596 740l1 1 1 2c-1 0-2 1-3 1l-1 1 1 1c1 1 2 2 2 4l-1 1v1c2 1 2 1 2 2h0c-1 1-1 1-1 2h-1v-1s-1-1-1-2h0c-1-3-3-4-6-5l2-1v-1c-1-1-2-2-3-2h-1v-1c1-1 2-1 3-1 1 1 2 1 3 1h1c1-1 1-2 2-3z" class="C"></path><defs><linearGradient id="AD" x1="558.877" y1="626.247" x2="535.191" y2="663.693" xlink:href="#B"><stop offset="0" stop-color="#999897"></stop><stop offset="1" stop-color="#c0c0be"></stop></linearGradient></defs><path fill="url(#AD)" d="M547 618l3 2v7l1 41-1 2c-1 0-2-1-2-1h-3 0c-1 1-2 1-4 1h0 0l-1-1c0-3 0-7 1-10v-20l1-2v-1-11h2c-1-1 0-1-1-2 2 0 2 1 3 2l1-1c1-1 0-2 0-3v-3z"></path><path d="M542 637v21c0 3-1 8 0 10 1 1 2 1 3 1-1 1-2 1-4 1h0 0l-1-1c0-3 0-7 1-10v-20l1-2z" class="H"></path><path d="M535 814h1l1 1c-1 1-2 1-3 1 0 0 1 1 2 1l1 1c-1 2-3 2-3 3h1 2c1 1 1 2 1 3h1c0-2 0-5 1-7v10l1 4v6c0 2 0 4 2 6l1 1-1 2h-2-1-1v-2c-1 1-2 1-3 1l-1 1v1 1h-5-2c-1-1 0-1-1-2 0-1 0-1-1-1s-1 1-2 1h-1v-19c0-1 1-2 1-2l1-1h1c1 0 1-1 2-1 0-1 1-2 2-3l1 1 1-1c1-1 2-1 3-1v-1h0l-2-2h-1c1-1 1-1 3-1h0v-1z" class="a"></path><path d="M528 848l2-2c-1 0-2-1-2-2l1-2h6c-1 1 0 1-1 1v1c1 0 1 1 2 1l-1 1v1 1h-5-2z" class="j"></path><path d="M541 831v6 4h-1l-3-2h-1-2c-1 0-1-1-2-1v-1h3v-1c-1 0-2-1-3-1 0-1 1-1 2-2h0c1 1 2 2 3 2s2-1 2-1c1-1 1-1 1-2l1-1z" class="K"></path><path d="M535 814h1l1 1c-1 1-2 1-3 1 0 0 1 1 2 1l1 1c-1 2-3 2-3 3h1 2c1 1 1 2 1 3h1c0-2 0-5 1-7v10l1 4-1 1c0 1 0 1-1 2 0 0-1 1-2 1s-2-1-3-2h0c-1 1-2 1-2 2l-2 2h-2v1h-1v-2c1-1 1-2 2-3h-1l1-2h-1v-1l1-1h-3l1-1c1-1 2-1 3-2l-1-1-1 1-1-1 4-3h-1l-2 1c0-1 1-2 2-3l1 1 1-1c1-1 2-1 3-1v-1h0l-2-2h-1c1-1 1-1 3-1h0v-1z" class="f"></path><path d="M528 823c0-1 1-2 2-3l1 1c2 1 4 0 6 2v1c-2 1-3 1-4 1v1c1 0 2 0 4 1v1l-1-1c-2 0-4 1-6 2l1 1h0c2-1 3-1 5 0l-1 1h-3-1v1l1 1c1 0 0-1 2 0-1 1-2 1-2 2l-2 2h-2v1h-1v-2c1-1 1-2 2-3h-1l1-2h-1v-1l1-1h-3l1-1c1-1 2-1 3-2l-1-1-1 1-1-1 4-3h-1l-2 1z" class="L"></path><path d="M598 737c1 0 1 0 3 1l1-1v1h0c-1 1-1 1-1 2h1l1-1 1-2c1 1 0 2 1 2l1 1v1c1 0 1 0 2-1l3-3h1v1c-1 1-2 3-3 4-1 2-2 4-2 6l-1 2c0 1 0 4 1 5v1l1 1c1-1 1-2 2-3h1v1c-1 1-2 2-3 4s-2 1 0 4c1 0 2-2 3-3 1-2 6-9 9-9l1 1 3-2c1 0 2-1 3-1l2-1c2 0 3-1 4-2l1 1-5 5 2 1 1-1h2 0 2c1 1 3 2 4 2l1-1c1 0 1 1 1 1-5 1-11 1-16 3-2 1-4 3-6 5s-5 3-8 5l1-2h0l-1-1c-2 1-5 6-7 6 0-1 1-1 1-2v-1c-2 1-3 2-3 4-1 1-1 2-2 3s-2 2-3 2l-1-1c2-1 4-3 5-4 0-1 1-3 1-3 0-1-2-2-2-2-1-2 0-5 0-7l-1-1h-1v-2-2l1-1-1-2c1-1 1-1 1-2s0-2-1-2h-1l-1-1 1-1-1-1 1-1-1-2c1-2 1-2 1-4z" class="D"></path><path d="M633 746l1 1-5 5-1 1c-1 1-2 2-3 2-2 1-3 2-5 4l-1 1c0-1 1-3 2-4h-1l1-1 2-2c1-2 2-2 4-3v-1l2-1c2 0 3-1 4-2z" class="F"></path><path d="M544 677l1-1v1l1 1v1l2-2h1l1 2h3v1 1l1 1 1 5 2-2 1 3c0 2 1 7-1 10v1 1 4l-1-1-2 1c-2 0-4-1-5 0-1 0-1 1-1 1h-1v-2c-2 0-3 1-4 1h-2v2h0-2 0v-2-4h0l-1-1c-1 0-2 0-4-1h0c-1-2 0-3 0-4l1-6v-1l1-1c0-3 1-4 3-7v-1-1h1 1c1-1 2-1 3 0z" class="L"></path><path d="M539 700c0-1-1-2 0-4h2l1-1 1 1v1c0 2 0 2 1 3l1-1v-3h6v2 2c-2 0-3 0-5 1h-2-1c-1-1-2-1-4-1h0z" class="B"></path><path d="M539 677h1 1c1-1 2-1 3 0l-2 2v1c-2 2-1 5 0 7v2c0 3 0 4 2 6h-1-1-3c-2-2 0-12 0-15v-1-1-1z" class="G"></path><path d="M544 677l1-1v1l1 1-1 3v9 4l-1 1c-2-2-2-3-2-6v-2c-1-2-2-5 0-7v-1l2-2z" class="F"></path><path d="M545 694l-1-1c0-1-1-3-1-4 0-2 1-2 2-3v-5 9 4z" class="I"></path><path d="M551 696h4c1 2 1 5 1 7l-2 1c-2 0-4-1-5 0-1 0-1 1-1 1h-1v-2c-2 0-3 1-4 1h-2v2h0-2 0v-2-4c2 0 3 0 4 1h1 2c2-1 3-1 5-1v-2-2z" class="E"></path><path d="M551 698c2 1 2 1 2 2v2 1h-2l-1-1h1v-2-2z" class="d"></path><path d="M546 679l2-2h1l1 2h3v1 1l1 1 1 5c0 2 0 5 1 7l-1 1c-1 0-3 0-4-1-1 0-1 0-1-1-1-1-1-3-1-4 0-3 0-5-1-8l-2-2z" class="F"></path><path d="M567 606v-1l-1-1c-2 1-3 0-4 1-1-1-1-1-1-3-1 1-2 1-2 1h-1c1-1 0-2 2-2 1 0 0 0 1-1-1-1-2 0-2-1 1-1 1 0 2-1 1 0 3-3 3-3l2 1h0v-1-1h2v1c-1 1-1 2-2 3h-1c-1 1-1 1-2 1l-1 1 1 1c3 0 2-2 5-1 1-1 1-2 2-3l2 2h1l-1-2h1v-1c-1 0-1 0-2 1l-1-2h1c1-1 1-1 2-1h1c1 1 2 1 3 3h0c2 0 3 1 4 3l1 1h1c-1-2 0-1-1-2-2-1-4-3-4-5l-2-2 1-1h2 1 0c0 1 0 0 1 1s1 1 1 3c1 0 2 1 2 2 1 1 2 1 3 2 1 0 1 1 1 1l2 2v-1l1-1c-1 0-2 0-3-1l-1-1 1-1h1l1 1 1-1h0c-1-1-1-1-2-1s-2-1-3-1c-1-1-1-1 0-2h4v-1c-1-1-1 0-1-1 1-1 1-1 2-1v2h2l1-1v-1h1v1 7h1 1c2 1 2 2 4 2l1-1 1 1c-1 1-2 1-3 1l-1 1c-4 2-5 4-9 3-2-1-3-2-5-3 0 1 0 2-1 2-1 1-3 1-4 1h-1c-3 0-4-1-6 1 1 0 1 0 2-1l1 1c1 0 1 0 2-1 1 0 4 1 5 0h1 1c2 0 3 0 4 1 1 3 1 3 0 5v-1l-2 2c0 2 1 2 0 3-2 0-2 0-3 1h-1-5c-2 0-3 1-5 0h-5c1 0 1-1 2-1l1-1v-1l-1 1h-1l1-1c-1-1-1-1-2-1 1-2 1-2 1-3h-2l1-1c0-1 0-1-1-2z" class="g"></path><path d="M574 601l2-1c0 1 0 1 1 1h1v2c1 0 1 0 2 1h0c-1 1-2 1-3 0h-2 0l-2 1c-2-1-2-1-3-2l1-1h3 1c-1-1-1 0-1-1z" class="a"></path><path d="M568 608l4-2h0c1 1 2 1 3 0 1 1 0 1 0 2 1-1 3-2 4-2h5l2 1 1-1c1 0 1 0 1 1 1 1 0 1 1 2v1l-2 2c0 2 1 2 0 3-2 0-2 0-3 1h-1-5c-2 0-3 1-5 0h-5c1 0 1-1 2-1l1-1v-1l-1 1h-1l1-1c-1-1-1-1-2-1 1-2 1-2 1-3h-2l1-1z" class="U"></path><path d="M593 693l6-2c2-1 4 0 6-1h0 5 2v1c-1 2-2 4-3 5l-1 7v1c0 2 0 3-1 5h-3c-1 1-1 2-2 2l-2 2c-1 1-3 2-4 3-2 1-3 3-5 4s-3 2-5 3c-1 1-3 2-4 4 1-2 1-2 2-3h0-2-1c-1 1-4 2-5 4-1 0-1 0-3-1-1 1-3 2-4 3l-1 1c-4 5-6 11-8 17-1 2-2 4-2 6l-1-1c0-1 1-3 2-4 2-6 4-13 8-18l1-1-1-1h1l1-1c1 0 1 0 1-1 1-1 2-2 2-3-1-1-2-1-3-2h-1l5-2 1-1c3-2 6-4 8-7l2-5h1c1-2 2-3 3-4s1-2 1-4l-2-1c-1 0-1 0-1-1l4-3h0c1 0 2-1 3-1z" class="K"></path><path d="M592 698c0-2 0-2 1-3l2 2 1-1c1 1 1 1 1 2h3l1 1 3-3c0 2 1 4 0 5l-1 1v1l-1 1v1 1h-1c-1 1-1 2-1 3l-1 2c-1 0-1 0-1-1-1 1-2 1-2 2l-2 2-5 5-2-1c3-3 4-6 6-10 1-1 1-2 2-4v-3c-1-1-1-1-1-2l-1-1h-1z" class="T"></path><path d="M593 693l6-2c2-1 4 0 6-1h0 5 2v1c-1 2-2 4-3 5l-1 7c-1-1-1-2-2-3l-3 3v-1l1-1c1-1 0-3 0-5l-3 3-1-1h-3c0-1 0-1-1-2l-1 1-2-2c-1 1-1 1-1 3v1l-2-1-1 1-2-1c-1 0-1 0-1-1l4-3h0c1 0 2-1 3-1z" class="K"></path><path d="M604 696h0l1-1v2h2c1 0 1 0 2-1l-1 7c-1-1-1-2-2-3l-3 3v-1l1-1c1-1 0-3 0-5z" class="O"></path><path d="M593 693l6-2c2-1 4 0 6-1h0 5c0 2-1 3-2 4h-1c-1 0-2 1-3 1-2 0-4 0-5 1l-1 1c-1-2-2-2-4-2l-1-2z" class="p"></path><path d="M592 698h1l1 1c0 1 0 1 1 2v3c-1 2-1 3-2 4-2 4-3 7-6 10-4 4-8 7-14 9-1 1-3 2-4 3l-1 1c-4 5-6 11-8 17-1 2-2 4-2 6l-1-1c0-1 1-3 2-4 2-6 4-13 8-18l1-1-1-1h1l1-1c1 0 1 0 1-1 1-1 2-2 2-3-1-1-2-1-3-2h-1l5-2 1-1c3-2 6-4 8-7l2-5h1c1-2 2-3 3-4s1-2 1-4l1-1 2 1v-1z" class="i"></path><path d="M587 712l1 1c0 2 0 2-1 3l-2 1v-1c0-1 1-2 2-4z" class="C"></path><path d="M582 712l2-5h1c1-2 2-3 3-4-1 5-4 9-7 13v-1h0v-1c1-1 1-1 1-2z" class="P"></path><path d="M582 712c0 1 0 1-1 2v1h0v1c-1 3-4 5-6 7-1-1-1-1-2-1l1-1-1-1 1-1c3-2 6-4 8-7z" class="j"></path><path d="M582 787c1 1 3 1 4 1l2 1 3 4c1 1 2 3 3 4l1 4 2 12h0c0-1 0-2 1-3l-1 17v1l-1-1c0 1-1 2-1 2h-1c0 3-2 6-3 9l1 2 1 1 1 1c-4 8-10 15-18 19-4 3-9 5-13 7h-2v-2h-1c-4 0-6 1-9 3h-1-1v1-2-1l1-1c0-2 1-3 1-4l-2-1-2-1v-1l1 1c2 1 5 1 7 2l3-3c1-1 2-1 3-1h-1c-1-1-1 0-1-1l2-2h0c1-2 1-3 2-4l1-3h0l-1 1c-1-1-1-1-2-1 1-1 1-2 2-3h1l2-2h0 1c1-1 1-2 1-3h0l3-1c2 0 3 0 5 1v1l1-1c1-2 2-2 3-3l3-6v-1c1-1 1-2 1-3-1 0-1 0-1 1h0-1c0-1 1-3 2-4 1-2 2-6 2-8v-1h1c1-2 0-4 0-6h0c-2-6-5-9-9-12 1 0 1-1 2 0 1 0 1 0 2-1l2 1c1 0 1 0 2 1h0 1l-3-3 1-2-1-1h0c-1 0-2 0-2-1-1-1-1-2-1-3l1-1z" class="i"></path><path d="M576 851c0 2-1 3-3 4-3 2-7 4-10 4v1l-1 1-1-1-1 1c-2 1-3 1-5 1l3-3c1-1 2-1 3-1 6-1 11-3 15-7z" class="j"></path><path d="M582 796l2 1c2 2 4 4 4 7 1 1 1 2 1 3 2 4 2 8 2 12-1-2-1-4-1-6s-1-5-2-7c0 3 1 6 0 10l-1-1c1-2 0-4 0-6h0c-2-6-5-9-9-12 1 0 1-1 2 0 1 0 1 0 2-1z" class="L"></path><defs><linearGradient id="AE" x1="583.909" y1="837.802" x2="589.596" y2="839.701" xlink:href="#B"><stop offset="0" stop-color="#8e8e8c"></stop><stop offset="1" stop-color="#acabab"></stop></linearGradient></defs><path fill="url(#AE)" d="M591 825c0-2 1-3 1-4 1-2 1-5 1-6l1-1v-1c2 3 0 5 1 8 0 2-1 2-1 3v4 1c0 3-2 6-3 9-3 8-7 14-14 19v-2c2-1 1-2 2-4 1-1 3-2 4-3l1-2c3-6 5-11 6-18 1 0 1-1 1-2v-1z"></path><defs><linearGradient id="AF" x1="571.275" y1="868.605" x2="570.183" y2="840.818" xlink:href="#B"><stop offset="0" stop-color="#0e0e0d"></stop><stop offset="1" stop-color="#424344"></stop></linearGradient></defs><path fill="url(#AF)" d="M577 857c7-5 11-11 14-19l1 2 1 1 1 1c-4 8-10 15-18 19-4 3-9 5-13 7h-2v-2h-1c-4 0-6 1-9 3h-1-1v1-2-1l1-1c0-2 1-3 1-4 10 2 18-1 26-5z"></path><path d="M582 787c1 1 3 1 4 1l2 1 3 4c1 1 2 3 3 4l1 4 2 12h0c0-1 0-2 1-3l-1 17v1l-1-1c0 1-1 2-1 2h-1v-1-4c0-1 1-1 1-3-1-3 1-5-1-8v1l-1 1c0 1 0 4-1 6 0 1-1 2-1 4v-6c0-4 0-8-2-12 0-1 0-2-1-3 0-3-2-5-4-7 1 0 1 0 2 1h0 1l-3-3 1-2-1-1h0c-1 0-2 0-2-1-1-1-1-2-1-3l1-1z" class="X"></path><path d="M582 787c1 1 3 1 4 1l2 1c-1 1-1 2-2 3h1c0 1 0 0 1 1-2 0-2-1-4-1h0c-1 0-2 0-2-1-1-1-1-2-1-3l1-1z" class="S"></path><path d="M585 793c1 1 2 2 3 4 0 1 0 1 1 2l1 2v1c0 1 0 4-1 5 0-1 0-2-1-3 0-3-2-5-4-7 1 0 1 0 2 1h0 1l-3-3 1-2z" class="M"></path><path d="M587 815c0 5 1 11-1 16s-3 10-6 14c-1 2-2 4-4 6-4 4-9 6-15 7h-1c-1-1-1 0-1-1l2-2h0c1-2 1-3 2-4l1-3h0l-1 1c-1-1-1-1-2-1 1-1 1-2 2-3h1l2-2h0 1c1-1 1-2 1-3h0l3-1c2 0 3 0 5 1v1l1-1c1-2 2-2 3-3l3-6v-1c1-1 1-2 1-3-1 0-1 0-1 1h0-1c0-1 1-3 2-4 1-2 2-6 2-8v-1h1z" class="I"></path><path d="M580 837c-1 3-3 6-5 9l-5 4c-3 2-5 4-9 5 1-2 1-3 2-4l1-3h0l-1 1c-1-1-1-1-2-1 1-1 1-2 2-3h1l2-2h0 1c1-1 1-2 1-3h0l3-1c2 0 3 0 5 1v1l1-1c1-2 2-2 3-3z" class="O"></path><path d="M570 849c-1-2-2-3-3-4 0-1 1-2 1-2 1 0 1 0 2 1-1 0-1 0-1 1 2 0 2 1 3 2l2-2 1 1-5 4v-1z" class="a"></path><path d="M580 837c-1 3-3 6-5 9l-1-1h-3v-1l2-2c1 0 1 0 2-1h1l1-1c1-2 2-2 3-3z" class="R"></path><path d="M564 848l2-1 1 1c0 1-2 1-2 2v1l2-1h2l1-1v1c-3 2-5 4-9 5 1-2 1-3 2-4l1-3z" class="b"></path><path d="M586 617c1 1 2 1 2 2l1 1v1 4c2 0 2-1 3 0h2c1 0 2 0 3 1l1 2h2c0 1 0 2 1 3 1 0 2 0 3 1l2-2-1-1h-1v-2h4l1 1c0 1-1 3-1 4v1c0 1 0 2-1 3h1c1-1 2-2 2-3l3-1c-2 4-4 7-8 9v2l2 1v3c1 1 1 0 1 1l2 2h0l1 7c-1 4-2 7-5 9-1 1-2 2-3 2h0l-5 2c0 1-2 1-3 2 2 0 9-3 10-3 0 1 0 2-2 3h-2 0c0 1 0 2-1 2s-4 2-5 2c-3 0-5 2-8 4-1 0-2 1-2 1h-2c-1 1 0 2-3 2l2-2c-1-1-2 0-4 0-3 3-7 7-10 9v-4l1-1-1-1 3-3v-1c1-2 2-3 4-5-1-1-1-1-1-2 1-2 1-3 2-4s0-3 0-4c2-5 4-10 5-15v-3-1c1-1 1-2 1-3s1-2 2-2l1-1v-7-4h2c0-1 0-1 1-1 0 1 0 1 1 2v-4h0l-1-1 1-1c-2 0-3 0-4-1l-1-1 1-1v-2-1l1-1z" class="m"></path><path d="M593 631h1c1 0 2 0 3 1v1h-2c-1 0-1 0-2 1l-1-1 1-2z" class="C"></path><path d="M593 645v-3l1-1c1 2 2 2 3 4h-4z" class="D"></path><path d="M586 617c1 1 2 1 2 2l1 1v1 4c2 0 2-1 3 0h2c1 0 2 0 3 1l1 2h2c0 1 0 2 1 3v5h-1v-2h-1v-1c0-1-1-1-1-2v-1c-2-1-4-3-6-3l-1 1-2-2h0l-1-1 1-1c-2 0-3 0-4-1l-1-1 1-1v-2-1l1-1z" class="b"></path><path d="M604 632l2-2-1-1h-1v-2h4l1 1c0 1-1 3-1 4v1c0 1 0 2-1 3h1c1-1 2-2 2-3l3-1c-2 4-4 7-8 9-1-1 1-1 0-3l-1 1c-1 0-2 1-3 1v-1c2-1 3-1 4-3 0-1-1-2-2-3h0v-1l1 1h1l-1-1z" class="P"></path><path d="M581 650s1-1 2-1c0-2 0-4 1-6 0 2 0 5 1 7v-10c1 5 2 10 0 15l-3 8-3 5v1l-2 2c-1 1-2 3-2 4-1-1-1-1-1-2 1-2 1-3 2-4s0-3 0-4c2-5 4-10 5-15z" class="F"></path><path d="M583 664c2-4 4-9 4-13 1-4 0-9 0-14l2 10c1-2 0-7 1-9l1 8h0 2v5c0 3-1 7-2 10-2 5-3 10-6 15-1 2-2 3-3 5-1-1-2 0-4 0-3 3-7 7-10 9v-4l1-1-1-1 3-3v-1c1-2 2-3 4-5 0-1 1-3 2-4l2-2v-1l3-5c1 0 1 0 1 1z" class="B"></path><path d="M591 646h2v5c0 3-1 7-2 10-2 5-3 10-6 15-1 2-2 3-3 5-1-1-2 0-4 0v-1h1c2-2 4-6 5-8 4-9 6-17 7-26z" class="Z"></path><path d="M582 663c1 0 1 0 1 1 0 2-1 4-2 6-2 4-5 7-6 10h0 3v1c-3 3-7 7-10 9v-4l1-1-1-1 3-3v-1c1-2 2-3 4-5 0-1 1-3 2-4l2-2v-1l3-5zm12-22h2v1h3c0-1 0-1 1-1l2 1-2 2h0l-2-1-1 1c0 1 0 1 2 1h0c1-1 2-1 3-1 1 1 1 1 1 2s1 2 1 3l2 1-1 1-2 2c0 1 1 2 2 3l1 1v-1l1-2h1l-1 2 4 1c-1 4-2 7-5 9-1 1-2 2-3 2h0l-5 2c0 1-2 1-3 2 2 0 9-3 10-3 0 1 0 2-2 3h-2 0c0 1 0 2-1 2s-4 2-5 2c-3 0-5 2-8 4-1 0-2 1-2 1h-2c-1 1 0 2-3 2l2-2c1-2 2-3 3-5 3-5 4-10 6-15 1-3 2-7 2-10v-5-1h4c-1-2-2-2-3-4z" class="C"></path><path d="M603 668l-1-1c0-1 0-1-1-2l2-3h1c0 3-1 4-1 6h0z" class="H"></path><path d="M603 646c0 1 1 2 1 3l2 1-1 1-2 2c0 1 1 2 2 3l1 1v-1l1-2h1l-1 2 4 1c-1 4-2 7-5 9v-4h-1c0-1 0-1-1-2-1-2-1-2 0-4-1-1-1-1-1-2v-1h-1l-1-2-1 1c-1-1-2-1-2-2l1-1c2 0 2 0 3 1l1 1c0-2-1-2-2-3 1-1 1-1 2-1v-1z" class="i"></path><path d="M593 645h4v1c0 1 1 1 1 2v1h-1l-1 1 1 1v1 2 4h-1v1c2 0 3-1 4 1 1 0 0 1 0 2l1 1c-1 1-2 1-3 3h0 2c0 2 0 2-1 3l-1 1c0 1-2 1-3 2 0-1-1-1-2-1 2-1 3-3 4-3h1v-1l-1 1-2-1v-1h1c-1-1-1-1-1-2l1-1h1 1c0-2-1-2-2-2 0 0-1 1-2 1v-2l-3 1c1-3 2-7 2-10v-5-1z" class="H"></path><path d="M591 661l3-1v2c1 0 2-1 2-1 1 0 2 0 2 2h-1-1l-1 1c0 1 0 1 1 2h-1v1l2 1 1-1v1h-1c-1 0-2 2-4 3 1 0 2 0 2 1 2 0 9-3 10-3 0 1 0 2-2 3h-2 0c0 1 0 2-1 2s-4 2-5 2c-3 0-5 2-8 4-1 0-2 1-2 1h-2c-1 1 0 2-3 2l2-2c1-2 2-3 3-5 3-5 4-10 6-15z" class="I"></path><path d="M631 707l1 1 2-2c1 3 2 5 2 7-1 3-2 6-5 7l1 2-1 1c1 0 1 0 2 1s2 1 3 1c1 1 4 0 6 0 1 0 2 1 3 1s2 0 3 1h1c0 1-1 3-1 4v4h0 2c-2 2-4 2-4 5-4 2-8 4-12 7l-1-1c-1 1-2 2-4 2l-2 1c-1 0-2 1-3 1l-3 2-1-1c-3 0-8 7-9 9-1 1-2 3-3 3-2-3-1-2 0-4s2-3 3-4v-1h-1c-1 1-1 2-2 3l-1-1v-1c-1-1-1-4-1-5l1-2c0-2 1-4 2-6 1-1 2-3 3-4v-1h-1l-3 3c-1 1-1 1-2 1v-1l-1-1c-1 0 0-1-1-2l-1 2-1 1h-1c0-1 0-1 1-2h0v-1l-1 1c-2-1-2-1-3-1 0 2 0 2-1 4l-1-1c-1-1-2-2-2-4 0-1 0-2-1-4h0l-1-1v-2l10-7 2-2-1-1h0-1c2-2 4-5 6-7 1-1 1-2 1-3 2 2 4 4 7 4 1 1 2 1 2 0 5 0 9-4 13-6h0z" class="F"></path><path d="M612 748l-1 2c-1 3 0-1-1 2h0c-1 1-1 1-2 1v-1c0-3 0-3 1-5l1 1h1 1z" class="W"></path><path d="M624 750l-2-1 1-1 1-1c1-1 3-1 4-1v1l1 1-2 1c-1 0-2 1-3 1z" class="Y"></path><path d="M621 743h1c-1 3-4 3-4 6l-4 4h0v-3c1 0 1-1 1-1 1-2 2-3 4-4 0-1 1-1 1-1l1-1z" class="E"></path><path d="M637 741v1c-1 2-2 2-4 4-1 1-2 2-4 2l-1-1v-1l9-5z" class="Q"></path><path d="M593 732l5-1 3 2v1c-2 1-2 1-3 3h0c0 2 0 2-1 4l-1-1c-1-1-2-2-2-4 0-1 0-2-1-4h0z" class="H"></path><path d="M648 735h2c-2 2-4 2-4 5-4 2-8 4-12 7l-1-1c2-2 3-2 4-4v-1c3 0 6-4 10-5l1-1z" class="Y"></path><path d="M602 722h0c-1 1-1 1-1 2 1 1 1 1 2 1 0 2-1 3-3 5-1 0-1 1-2 1l-5 1-1-1v-2l10-7z" class="O"></path><path d="M621 732h4v1h1c1 0 2-1 3-1v1 1c-1 0-2 1-2 2-1 0-2 1-3 2h-1c-1 0-2 1-3 2s-1 0-2 1c-2 1-5 4-6 6v1h-1l2-4h-1c-1-3 0-3 2-5 1-1 3-2 5-3 0-1 0-1 1-2v-1l1-1z" class="Y"></path><path d="M620 733l1 1-1 1c1 1 1 1 2 1h0-1c0 1-1 1-1 2l-2 1h0c-1 1-1 1-2 1l-3 3-1 1c-1-3 0-3 2-5 1-1 3-2 5-3 0-1 0-1 1-2v-1z" class="Q"></path><path d="M625 725c3 0 5 0 8-1 1 1 2 1 3 1 1 1 4 0 6 0 1 0 2 1 3 1s2 0 3 1h1c0 1-1 3-1 4v4h0l-1 1c-1-3 0-3-2-5-2 0-4 0-6 1l-5 1c-1 1-2 1-3 2l-4 1c0-1 1-2 2-2v-1-1c-1 0-2 1-3 1h-1v-1h-4 0c-1-1-1 0-2 0l-1 1c-1 0-2 0-3-1-1 1-3 2-4 3-1 0-2 1-3 1h-1l2-2c-1 0-1 0-1-1l2-3c5-1 9-4 14-4l1-1z" class="J"></path><path d="M610 730c5-1 9-4 14-4 2 2 3 0 5 2-1 1-1 1-2 1-3-1-4 0-7 1-2 0-3 1-5 2-1 1-3 2-4 3-1 0-2 1-3 1h-1l2-2c-1 0-1 0-1-1l2-3z" class="d"></path><path d="M631 707l1 1 2-2c1 3 2 5 2 7-1 3-2 6-5 7l1 2-1 1c1 0 1 0 2 1-3 1-5 1-8 1l-1 1c-5 0-9 3-14 4v-1l2-2-3-4-1 1-2-1-1-1c-1 1-1 2-2 3-1 0-1 0-2-1 0-1 0-1 1-2h0l2-2-1-1h0-1c2-2 4-5 6-7 1-1 1-2 1-3 2 2 4 4 7 4 1 1 2 1 2 0 5 0 9-4 13-6h0z" class="K"></path><path d="M602 719c2-2 4-5 6-7 0 2 0 3-1 5h1l1-1v1c-1 1-2 2-2 3 2-1 4-4 5-3h3c2 0 5 1 7 1-1 1-1 1-2 1-1 1 0 0-2 1h-2-2-1-2l-2-1v4h-1l-1-1-1 1-1-1c-1 1-1 2-2 3-1 0-1 0-2-1 0-1 0-1 1-2h0l2-2-1-1h0-1z" class="R"></path><path d="M631 707l1 1 2-2c1 3 2 5 2 7-1 3-2 6-5 7l-1-1c-1-1-4-1-5-2h1l-1-1 1-2-1-1v-1c2-1 3-1 4-2h2v-3h0z" class="p"></path><path d="M634 706c1 3 2 5 2 7-1 3-2 6-5 7l-1-1c-1-1-4-1-5-2h1c1 0 1 0 2-1l2 1 4-4c-1-2-1-4-2-5l2-2z" class="O"></path><path d="M625 717c1 1 4 1 5 2l1 1 1 2-1 1c1 0 1 0 2 1-3 1-5 1-8 1l-1 1c-5 0-9 3-14 4v-1l2-2-3-4-1 1-2-1 1-1 1 1h1v-4l2 1h2 1 2 2c2-1 1 0 2-1 1 0 1 0 2-1 0-1 2-1 3-1z" class="L"></path><path d="M612 727h0l1-1c2-1 3-1 5-2l1 1c1-1 2-2 3-2v-1l-1-1 1-1 1 1c1-1 2-1 3-1l1 1c-2 0-3 2-3 4h1l-1 1c-5 0-9 3-14 4v-1l2-2z" class="k"></path><path d="M541 706h0v-2h2c1 0 2-1 4-1v2h1s0-1 1-1l-1 4 1 1h2l5-1h2c0 1 0 2 1 3h0c3 4 5 8 9 11h1c1 1 2 1 3 2 0 1-1 2-2 3 0 1 0 1-1 1l-1 1h-1l1 1-1 1c-4 5-6 12-8 18-1 1-2 3-2 4l1 1-4 14c-1 1-1 4-2 5l-2-1h0l-2-1v-1h-1 0 0l-2-1c-2-1-4-1-7-1l-1 1h0l-1 2-6-29c-1-4-1-12-4-14 1-1 1-2 1-3l-1-1 2-2 3-3h2l1-1v-4l2-1v-1l1 2 2-2h2c1-2 1-2 0-3v-3z" class="G"></path><path d="M537 714l2-2h2v5 1h-1v-1l-3-3z" class="Y"></path><path d="M548 718c2 0 4 1 6 2h0v2h-6v-4z" class="b"></path><path d="M539 719c2-1 5-1 6 0 0 1 1 2 0 3-2 1-5 1-7-1v-1l1-1z" class="O"></path><defs><linearGradient id="AG" x1="554.572" y1="767.197" x2="549.859" y2="725.698" xlink:href="#B"><stop offset="0" stop-color="#181a18"></stop><stop offset="1" stop-color="#323132"></stop></linearGradient></defs><path fill="url(#AG)" d="M556 724h1l1 1v1l-1 2-9 42h-1 0 0l-2-1 1-1 5-30c1-3 2-8 2-11v-1l3-2z"></path><path d="M533 719l1-1v-4l2-1v6 2c-1 0-1 0-2 1-2 0-5 2-6 4h3c1 3 1 6 1 9l4 23c1 3 1 8 2 10l-1 1h0l-1 2-6-29c-1-4-1-12-4-14 1-1 1-2 1-3l-1-1 2-2 3-3h2z" class="a"></path><path d="M558 726c2 2 4 4 7 5h2c-4 5-6 12-8 18-1 1-2 3-2 4l1 1-4 14c-1 1-1 4-2 5l-2-1h0l-2-1v-1c1-2 8-37 9-42l1-2z" class="D"></path><path d="M558 726c2 2 4 4 7 5-2 2-4 4-4 7l-1 2c-1-2-1-5-1-7-1 1 0 3-1 4v-2-2h1v-1h0c0-2 0-2-2-4l1-2z" class="I"></path><path d="M565 731h2c-4 5-6 12-8 18-1 1-2 3-2 4l1 1-4 14c-1 1-1 4-2 5l-2-1h0c1-1 1-3 1-4l3-8 6-20 1-2c0-3 2-5 4-7z" class="j"></path><path d="M541 706h0v-2h2c1 0 2-1 4-1v2h1s0-1 1-1l-1 4 1 1h2l5-1h2c0 1 0 2 1 3h0c3 4 5 8 9 11h1c1 1 2 1 3 2 0 1-1 2-2 3 0 1 0 1-1 1l-1 1h-1l1 1-1 1h-2c-3-1-5-3-7-5v-1l-1-1h-1l-2-2v-2h0c-2-1-4-2-6-2v-1h-3-1l-1-1c-1 1-1 1-2 1v-5c1-2 1-2 0-3v-3z" class="G"></path><path d="M556 708h2c0 1 0 2 1 3h0l-1 1c0 1 0 1-1 2-2-1-2-1-4-1v1l-1-1c-1-1-1-2-1-4l5-1z" class="D"></path><path d="M554 720l1-2c-2-1-2-1-4-1v-1h4 1l2 2c2 1 5 5 7 7h0c0 2 1 3 1 4h1l1 1-1 1h-2c-3-1-5-3-7-5v-1l-1-1h-1l-2-2v-2h0z" class="h"></path><path d="M554 720c4 3 8 7 12 9h1l1 1-1 1h-2c-3-1-5-3-7-5v-1l-1-1h-1l-2-2v-2z" class="U"></path><path d="M541 706h0v-2h2c1 0 2-1 4-1v2h1s0-1 1-1l-1 4 1 1h2c0 2 0 3 1 4l1 1c1 0 3 1 4 1s1 1 2 1l-1 2-2-2h-1-4v1c2 0 2 0 4 1l-1 2c-2-1-4-2-6-2v-1h-3-1l-1-1c-1 1-1 1-2 1v-5c1-2 1-2 0-3v-3z" class="C"></path><path d="M545 713c1-1 2-1 2-2l1-1v6h-1c0-1-1-1-2-1v-2h0z" class="J"></path><path d="M541 706h0v-2h2c1 0 2-1 4-1v2h1s0-1 1-1l-1 4v2l-1 1c0 1-1 1-2 2 0-3 0-5-1-7 0 0-1 0-1 1l1 1-1 1h-2v-3z" class="G"></path><path d="M607 644l1-1c1 1 2 1 3 1h3c2 0 3 0 5 1h2 3l-2-2 21 12 6-5v-2c-1 0-1 0-2-1v-1c1 0 3 1 3 2l1 2h2l1 2v2l2-1 4-4c-1 2-2 3-4 5h0l1 2-2 3c0 1 0 1-1 2h1c1-1 2 0 3 0-1 2-3 3-4 5l-6 6-5 3 1 1c2 1 4 4 6 4l2 1-1 4 2-1 1 1 1 1c2 0 2 2 3 3l-2 2h-1l-2 2-2 1c-1-1-2-1-3-2l-5 5 1 3c-2 1-3 2-4 3l-2 2v1h-1-3 0l-2 2-1-1h0c-4 2-8 6-13 6 0 1-1 1-2 0-3 0-5-2-7-4v-1l-1-4v-1l1-7c1-1 2-3 3-5v-1h-2-5 0c-2 1-4 0-6 1l-6 2c-1 0-2 1-3 1h0l-4 3c0 1 0 1 1 1l2 1c0 2 0 3-1 4s-2 2-3 4h-1l-2 5c-2 3-5 5-8 7l-1 1-5 2c-4-3-6-7-9-11h0c-1-1-1-2-1-3h-2l-5 1h-2l-1-1 1-4c1-1 3 0 5 0l2-1 1 1v-4-1-1c2-3 1-8 1-10 2 0 2 1 4 1l-1-5h3l1 4 1-1v-1l2-2 1 1-1 1v4c3-2 7-6 10-9 2 0 3-1 4 0l-2 2c3 0 2-1 3-2h2s1-1 2-1c3-2 5-4 8-4 1 0 4-2 5-2s1-1 1-2h0 2c2-1 2-2 2-3-1 0-8 3-10 3 1-1 3-1 3-2l5-2h0c1 0 2-1 3-2 3-2 4-5 5-9l-1-7h0l-2-2c0-1 0 0-1-1v-3z" class="L"></path><path d="M610 676h2c0 1 1 2 0 3h-1l-10 2 1-1c1-1 2-2 4-3 1 0 3-1 4-1z" class="D"></path><path d="M585 683c3 0 6-2 9-3l12-3c-2 1-3 2-4 3l-1 1c-7 1-13 3-20 6 1-2 3-3 4-4z" class="F"></path><path d="M587 680h1l-3 3c-1 1-3 2-4 4-1 1-4 2-6 3-1 1-2 1-3 2l-1-1c3-2 6-6 9-8 3 0 2-1 3-2h2s1-1 2-1z" class="M"></path><path d="M592 684v1l-1 1c-2 1-3 2-5 2-4 2-6 6-10 7v1c-2 1-5 2-7 3v-1c1-3 6-4 8-6h0c-1 1-3 1-4 1 1-2 6-4 9-5 3-2 6-3 10-4z" class="h"></path><path d="M615 667c1 1 0 1 1 2l1-1h1c0 1 0 1-1 2-2 1-4 1-5 1 0 1-1 1-1 2v1l-1 2c-1 0-3 1-4 1l-12 3c-3 1-6 3-9 3l3-3h-1c3-2 5-4 8-4 1 0 4-2 5-2 2 0 3-1 5-1 4-1 7-3 10-6z" class="e"></path><path d="M615 667c1 1 0 1 1 2l1-1h1c0 1 0 1-1 2-2 1-4 1-5 1 0 1-1 1-1 2h0l-12 3c-1 1-2 1-2 1-2 0-3 1-5 1h0 1c0-1 1-2 2-2s4-2 5-2c2 0 3-1 5-1 4-1 7-3 10-6z" class="j"></path><path d="M628 671c1-1 1-1 3-2l1 1c-1 1-1 2-2 3-1 2-3 3-5 4-3 2-6 2-9 2-1 0-3 1-5 0h1c1-1 0-2 0-3h-2l1-2c1 0 2-1 3-1s2-1 3-1l3-1 2 1 2-2v1l2-1v1h2z" class="i"></path><path d="M628 671c1-1 1-1 3-2l1 1c-1 1-1 2-2 3-1 2-3 3-5 4v-1c1-1 1-2 2-3h1l1-1-1-1z" class="n"></path><path d="M614 673h1c2 1 3 1 5 1v-1c1 0 1-1 2 0s2 1 2 2v1h-1v2c-1-1-1 0-1-1h-1l-1 1-2-2-2 2v1h0c-1 0-3 1-5 0h1c1-1 0-2 0-3h-2l1-2c1 0 2-1 3-1z" class="C"></path><path d="M656 654l1 2-2 3c0 1 0 1-1 2h1c1-1 2 0 3 0-1 2-3 3-4 5l-6 6-5 3-6 5-4 2-1 2c-3 2-8 3-12 3h-1l-13 1v-1c-1-1-1-2-1-2l-1-1-1 1-1-1c-2 0-4 0-5 1-1 0-2-1-2 0-2 1-3 1-4 1l1-1v-1s1 0 2-1h1c2 0 7 0 9-2 2 0 3 0 4 1h1l1-1c1 0 4-1 5 0h0l1-1 1 1 5-1c1-1 1-1 2 0l1-1c1 0 1 1 2 1 0-2 0-2 1-3l1 1h0c2-1 3-2 5-3 1 0 1 0 1-1 2-1 3-2 4-3 1-3 4-4 6-6h3v-1c0-2 5-6 6-7h1l1-3z" class="k"></path><path d="M656 654l1 2-2 3c0 1 0 1-1 2h1c1-1 2 0 3 0-1 2-3 3-4 5l-6 6-5 3-6 5v-1c0-1 3-3 3-3l1-1c1 0 1-1 2-1h0l1-2 1-1c2-1 3-3 5-4l-1-1c-1 1-2 2-3 2 0-1 0-2-1-3h3v-1c0-2 5-6 6-7h1l1-3z" class="M"></path><path d="M602 684l1-1 1 1c1-1 1-1 2-1h8c1 0 2-1 4-1h1 0l1 1c1-1 2-1 3-2h0 2c1 0 3-1 5-1h0 1c2-1 3-3 4-3-1 2-3 4-5 5v1c2 0 5-3 7-4v1l-4 2-1 2c-3 2-8 3-12 3h-1l-13 1v-1c-1-1-1-2-1-2l-1-1-1 1-1-1z" class="H"></path><path d="M578 681c2 0 3-1 4 0l-2 2c-3 2-6 6-9 8-1 2-2 3-2 4 0 2-1 3-1 4h1c2-1 5-2 7-3v-1c4-1 6-5 10-7 2 0 3-1 5-2 1 0 2 0 4-1 0-1 1 0 2 0 1-1 3-1 5-1l1 1 1-1 1 1s0 1 1 2v1l13-1h1c4 0 9-1 12-3v3h-1c-1 0-2 1-2 2h0l-1 2-1 1-2-1h-3-1-1c-1 0-2-1-3-1l-1 1c-1 1-1 0-2-1h-2-2-5 0c-2 1-4 0-6 1l-6 2c-1 0-2 1-3 1h0l-4 3c0 1 0 1 1 1l-5 3c0 1-1 2-2 2l-2 2h-1l-1 1h-1v-1l-6 1-1 1c0-1 0-1-1-2h-3c1-1 1-1 1-2 0-2 2-3 2-4s-1-2-2-3c1-2 0-5 0-8l1-1v-1l2-2 1 1-1 1v4c3-2 7-6 10-9z" class="a"></path><path d="M569 695c-1 0-1 0-2-1 0-2 0-2 2-4h1l1 1c-1 2-2 3-2 4z" class="f"></path><path d="M591 686c1 0 2 0 4-1 0-1 1 0 2 0 1-1 3-1 5-1l1 1 1-1 1 1s0 1 1 2v1c-11 1-20 4-30 8v-1c4-1 6-5 10-7 2 0 3-1 5-2z" class="I"></path><path d="M568 704v-1h0l-1-1c1 0 1-1 2-1s1 0 2-1c2-1 2-1 5-1 0-1 1-2 3-2 1 0 0 0 1-1 1 0 2-1 3-1 2 1 3-1 6-2l1 1-4 3c0 1 0 1 1 1l-5 3c0 1-1 2-2 2l-2 2h-1l-1 1h-1v-1l-6 1-1 1c0-1 0-1-1-2l1-1z" class="k"></path><path d="M568 704l14-3c0 1-1 2-2 2l-2 2h-1l-1 1h-1v-1l-6 1-1 1c0-1 0-1-1-2l1-1z" class="O"></path><path d="M564 684l1 4c0 3 1 6 0 8 1 1 2 2 2 3s-2 2-2 4c0 1 0 1-1 2h3c1 1 1 1 1 2l1-1 6-1v1h1l1-1h1l2-2c1 0 2-1 2-2l5-3 2 1c0 2 0 3-1 4s-2 2-3 4h-1l-2 5c-2 3-5 5-8 7l-1 1-5 2c-4-3-6-7-9-11h0c-1-1-1-2-1-3h-2l-5 1h-2l-1-1 1-4c1-1 3 0 5 0l2-1 1 1v-4-1-1c2-3 1-8 1-10 2 0 2 1 4 1l-1-5h3z" class="p"></path><path d="M556 703l1 1v-4-1-1c1 1 2 2 2 3l1 2c-1 2-1 4-1 6v1 1c-1-1-1-2-1-3h-2c0-1 0-2-1-4h-1l2-1z" class="m"></path><path d="M549 704c1-1 3 0 5 0h1c1 2 1 3 1 4l-5 1h-2l-1-1 1-4z" class="K"></path><path d="M568 707l1-1 6-1v1h1l1-1h1c-1 4-2 7-5 10-1 0-2-1-2-2l-1-1v1c-2 1-2 0-3 0 0-3 0-3 2-5l-1-1z" class="R"></path><path d="M569 708l3 1h1c0-2 0-2 1-3 1 1 0 4 0 5-1 1-2 2-3 2l-1-1v1c-2 1-2 0-3 0 0-3 0-3 2-5z" class="N"></path><path d="M564 684l1 4c0 3 1 6 0 8l-5 7-1-2c0-1-1-2-2-3 2-3 1-8 1-10 2 0 2 1 4 1l-1-5h3z" class="H"></path><path d="M564 684l1 4c0 3 1 6 0 8l-5 7-1-2c1-1 2-2 2-3s1-1 2-1v-2l-1-1h0c1-2 1-4 0-5l-1-5h3z" class="n"></path><path d="M643 675l1 1c2 1 4 4 6 4l2 1-1 4 2-1 1 1 1 1c2 0 2 2 3 3l-2 2h-1l-2 2-2 1c-1-1-2-1-3-2l-5 5 1 3c-2 1-3 2-4 3l-2 2v1h-1-3 0l-2 2-1-1h0c-4 2-8 6-13 6 0 1-1 1-2 0-3 0-5-2-7-4v-1l-1-4v-1l1-7c1-1 2-3 3-5v-1h2c1 1 1 2 2 1l1-1c1 0 2 1 3 1h1 1 3l2 1 1-1 1-2h0c0-1 1-2 2-2h1v-3l1-2 4-2 6-5z" class="N"></path><path d="M635 687c1 0 1 0 3 1h1 2v1h-2l-1 2v1c-1 0-1 2-2 2v-4-1l-2-1 1-1z" class="P"></path><path d="M616 706h3 0c1-1 1-1 2-1h1c1 0 1-1 2-1l3-1c1-1 3-2 4-4v-1h1c0 3-4 6-6 8-1 0-3 2-4 4-1-2-3-1-4-1-2-1-2-1-3-3h1z" class="B"></path><path d="M626 706c3-1 5-3 7-5l2 2-4 4h0c-4 2-8 6-13 6 1 0 1-1 1-1v-1l3-1c1-2 3-4 4-4z" class="H"></path><path d="M643 691c2 0 4 0 5 1h0l-5 5 1 3c-2 1-3 2-4 3l-2 2v1h-1-3 0l-2 2-1-1 4-4 3-3h0-2c1-1 3-2 3-4h0l3-1c0-1 0-1-1-2v-1h1l1-1h0z" class="o"></path><path d="M643 691c2 0 4 0 5 1h0l-5 5c-1 1-2 2-3 2h0l2-3v-1c0-1 0-1-1-2v-1h1l1-1h0z" class="n"></path><path d="M648 683c1 1 1 1 3 2l2-1 1 1 1 1c2 0 2 2 3 3l-2 2h-1l-2 2-2 1c-1-1-2-1-3-2h0c-1-1-3-1-5-1h0c-2 1-4 0-5 0l1-2h2v-1c0-5 2-2 5-3l2-2z" class="R"></path><path d="M643 691h0c2-1 3-2 4-3v-1h2c1 0 1-2 2-2h3l1 1-6 5-1 1c-1-1-3-1-5-1z" class="m"></path><path d="M655 686c2 0 2 2 3 3l-2 2h-1l-2 2-2 1c-1-1-2-1-3-2h0l1-1 6-5z" class="Z"></path><path d="M649 691c2 0 3 0 4 2l-2 1c-1-1-2-1-3-2h0l1-1z" class="P"></path><path d="M655 686c2 0 2 2 3 3l-2 2-3-3h1l1-2z" class="K"></path><path d="M643 675l1 1c2 1 4 4 6 4l2 1-1 4c-2-1-2-1-3-2l-2 2c-3 1-5-2-5 3h-2-1c-2-1-2-1-3-1l-1 1c0 1-1 3-2 4l-2-1-1-2c0-1 1-2 2-2h1v-3l1-2 4-2 6-5z" class="g"></path><path d="M633 682v1 3 2l2-1-1 1c0 1-1 3-2 4l-2-1-1-2c0-1 1-2 2-2h1v-3l1-2z" class="K"></path><path d="M638 688v-1l1-1c1-1 1-2 2-3v-1h3c1 1 1 1 2 1h1 1l-2 2c-3 1-5-2-5 3h-2-1z" class="T"></path><path d="M629 689l1 2 2 1c0 2-1 3-3 4l-1 1h0c0 1-1 2-2 3s-3 3-4 3h-1-1 0v1l-1-1-3 3h-1c1 2 1 2 3 3 1 0 3-1 4 1l-3 1v1s0 1-1 1c0 1-1 1-2 0-3 0-5-2-7-4v-1l-1-4v-1l1-7c1-1 2-3 3-5v-1h2c1 1 1 2 2 1l1-1c1 0 2 1 3 1h1 1 3l2 1 1-1 1-2h0z" class="f"></path><path d="M614 696v1h1c1-1 1 0 2 0h2v-2l1 1v3h-2v1c-1 0-1 0-1-1-2 0-3 0-4 1v3c2 0 2 0 3 2v1h-1l-2-1c-2-3-3-5-3-9h4z" class="O"></path><path d="M612 691h1c0 1 0 2 1 3v2h-4c0 4 1 6 3 9l2 1c1 2 1 2 3 3 1 0 3-1 4 1l-3 1v1s0 1-1 1c0 1-1 1-2 0-3 0-5-2-7-4v-1l-1-4v-1l1-7c1-1 2-3 3-5z" class="F"></path><path d="M613 691c0 1 0 2 1 3v2h-4c1-1 2-3 3-5z" class="b"></path><path d="M629 689l1 2 2 1c0 2-1 3-3 4l-1 1h0c0 1-1 2-2 3v-2h-1v-3c-1 2-2 3-3 4h-1-1v-3l-1-1v2h-2c-1 0-1-1-2 0h-1v-1-2c-1-1-1-2-1-3h-1v-1h2c1 1 1 2 2 1l1-1c1 0 2 1 3 1h1 1 3l2 1 1-1 1-2h0z" class="O"></path><path d="M614 694h2 3c1 0 2 0 4 1l1-1 1 1c-1 2-2 3-3 4h-1-1v-3l-1-1v2h-2c-1 0-1-1-2 0h-1v-1-2z" class="g"></path><path d="M607 644l1-1c1 1 2 1 3 1h3c2 0 3 0 5 1h2 3l-2-2 21 12 6-5v-2c-1 0-1 0-2-1v-1c1 0 3 1 3 2l1 2h2l1 2v2l2-1 4-4c-1 2-2 3-4 5h0l-1 3h-1c-1 1-6 5-6 7v1h-3c-2 2-5 3-6 6l-2-1-3 3h-2-2c1-1 1-2 2-3l-1-1c-2 1-2 1-3 2h-2v-1l-2 1v-1l-2 2-2-1-3 1c-1 0-2 1-3 1s-2 1-3 1v-1c0-1 1-1 1-2 1 0 3 0 5-1 1-1 1-1 1-2h-1l-1 1c-1-1 0-1-1-2-3 3-6 5-10 6-2 0-3 1-5 1 1 0 1-1 1-2h0 2c2-1 2-2 2-3-1 0-8 3-10 3 1-1 3-1 3-2l5-2h0c1 0 2-1 3-2 3-2 4-5 5-9l-1-7h0l-2-2c0-1 0 0-1-1v-3z" class="L"></path><path d="M619 660l2-1 1 1v2h-1v1l-1-1c0-1-1-1-1-2z" class="S"></path><path d="M624 653h1c-1 1-1 1-1 3h2v-1l1 1v1c-1 1-1 2-2 2s-2-1-2-2l-1-1c-1 0-1 0-3 1v-2l2-2h2 1z" class="R"></path><g class="N"><path d="M612 654c2 0 3 1 4 1v-1c1 0 1-1 2-1v1 2 2h1l-1 2h0-2l1-1-1-1-1 1h-2c-1-2 0-4-1-5z"></path><path d="M622 660l1 1c0 2 0 2-1 3h0c-1 1-1 1-2 1v2h1l3-5c2 1 2 1 3 3-2 2-5 4-8 5l-2 2c-1 0-2 1-3 1s-2 1-3 1v-1c0-1 1-1 1-2 1 0 3 0 5-1 1-1 1-1 1-2h-1l-1 1c-1-1 0-1-1-2l2-1 1-1c1-1 2-2 3-2v-1h1v-2z"></path></g><path d="M625 653c1 1 3 2 5 2h0l2 2h1v2h0l1 3-2 1-5 4c-2 1-5 3-7 4l-3 1 2-2c3-1 6-3 8-5l2-1c0-1 1-1 1-3-2-1-2-2-3-3v-1-1l-1-1v1h-2c0-2 0-2 1-3z" class="b"></path><path d="M625 653c1 1 3 2 5 2h0l2 2h1v2h0-4v-2c-1 0-1 0-2-1l-1-1v1h-2c0-2 0-2 1-3z" class="O"></path><path d="M607 644l1-1c1 1 2 1 3 1h3c2 0 3 0 5 1l5 2v1l-3 2v1c1 1 1 1 2 1l1 1h-1-2l-2 2c0-1 0-1-1-2v-1h-1c1-1 2-1 2-2-2 0-3 1-4 2l-1-1-3 1v-2h-1 0l-2-2c0-1 0 0-1-1v-3z" class="U"></path><path d="M607 644l1-1c1 1 2 1 3 1h3v4l-1 1v1h0 2l-1 1-3 1v-2h-1 0l-2-2c0-1 0 0-1-1v-3z" class="N"></path><path d="M622 643l21 12c-2 1-3 2-4 3l-1 1-4 3-1-3h0v-2h-1l-2-2h0c-2 0-4-1-5-2h-1l-1-1c-1 0-1 0-2-1v-1l3-2v-1l-5-2h2 3l-2-2z" class="p"></path><path d="M630 655l3-2c1 0 1 1 2 2v1-1c1 0 2 0 2 1h1v3l-4 3-1-3h0v-2h-1l-2-2z" class="K"></path><path d="M610 650h1v2l1 2c1 1 0 3 1 5h2l1-1 1 1-1 1h2 0 1c0 1 1 1 1 2l1 1c-1 0-2 1-3 2l-1 1-2 1c-3 3-6 5-10 6-2 0-3 1-5 1 1 0 1-1 1-2h0 2c2-1 2-2 2-3-1 0-8 3-10 3 1-1 3-1 3-2l5-2h0c1 0 2-1 3-2 3-2 4-5 5-9l-1-7z" class="m"></path><path d="M611 664h2s1-1 2-1c0 1 0 1-1 2-2 3-6 6-9 8-2 0-3 1-5 1 1 0 1-1 1-2h0 2c2-1 2-2 2-3 2-2 4-3 6-5z" class="C"></path><path d="M610 650h1v2l1 2c1 1 0 3 1 5-1 2-1 3-2 5-2 2-4 3-6 5-1 0-8 3-10 3 1-1 3-1 3-2l5-2h0c1 0 2-1 3-2 3-2 4-5 5-9l-1-7z" class="b"></path><path d="M647 646c1 0 3 1 3 2l1 2h2l1 2v2l2-1 4-4c-1 2-2 3-4 5h0l-1 3h-1c-1 1-6 5-6 7v1h-3c-2 2-5 3-6 6l-2-1-3 3h-2-2c1-1 1-2 2-3l-1-1c-2 1-2 1-3 2h-2v-1l-2 1v-1l-2 2-2-1c2-1 5-3 7-4l5-4 2-1 4-3 1-1c1-1 2-2 4-3l6-5v-2c-1 0-1 0-2-1v-1z" class="k"></path><path d="M632 663l3 1v1l-1 2h-4v1l1 1c-2 0-2-1-4-2l5-4z" class="S"></path><path d="M638 659l1-1v2c2 1 2 0 3 2v2l-1-2-2 1h-2l-1-1c0 1 0 1 1 2l-2 1v-1l-3-1 2-1 4-3z" class="N"></path><path d="M646 654h3l1 1 2 1-7 6c-1-1-1-2-1-3l1-1c1 0 2-1 2-2-1 0-2 0-2 1l-1-1 1-1h1v-1z" class="m"></path><path d="M647 646c1 0 3 1 3 2l1 2h2l1 2v2l-2 2-2-1-1-1h-3v-1c1 0 2-1 3-2v-1-2c-1 0-1 0-2-1v-1z" class="n"></path><path d="M650 655l1-3h2 1v2l-2 2-2-1z" class="q"></path><path d="M660 649c-1 2-2 3-4 5h0l-1 3h-1c-1 1-6 5-6 7v1h-3c-2 2-5 3-6 6l-2-1-3 3h-2l8-7c2-1 4-3 5-4l7-6 2-2 2-1 4-4z" class="a"></path></svg>