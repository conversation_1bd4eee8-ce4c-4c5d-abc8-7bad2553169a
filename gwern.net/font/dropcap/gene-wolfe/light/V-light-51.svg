<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="110 65 809 852"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#252424}.C{fill:#c9c8c4}.D{fill:#2d2d2c}.E{fill:#a9a8a5}.F{fill:#191919}.G{fill:#82817f}.H{fill:#4d4d4c}.I{fill:#bebdba}.J{fill:#979693}.K{fill:#595a58}.L{fill:#8c8b89}.M{fill:#1e1e1e}.N{fill:#0f0f0e}.O{fill:#353534}.P{fill:#3f3f3f}.Q{fill:#797977}.R{fill:#141413}.S{fill:#d4d3ce}.T{fill:#020202}.U{fill:#5f5f5e}.V{fill:#f3f2ef}.W{fill:#3b3b3b}.X{fill:#616160}.Y{fill:#686867}.Z{fill:#eeedea}.a{fill:#b3b2b0}.b{fill:#e5e4e0}.c{fill:#deddda}.d{fill:#b7b6b3}.e{fill:#464646}.f{fill:#a2a19d}.g{fill:#737371}.h{fill:#fefefe}</style><path d="M379 765c1 1 1 1 1 2l-1 4-3 2-2-1c2-2 3-4 5-7zm-38-60c2 2 4 4 8 5h2 2v1c-2 1-3 1-5 1-4-1-6-3-8-6l1-1zm67 93l4 6 2 2c0 1 1 2 0 4-3-4-6-7-8-11v-1h2zm49 60l2-2c3 2 6 6 9 7h1v3l-12-8z" class="T"></path><path d="M382 750l2 1-1 1c0 1 1 3 0 5 0 3-2 7-3 10 0-1 0-1-1-2l3-15z" class="M"></path><path d="M400 846l1 2v1c1 2 2 4 4 5 2 2 6 2 8 1 2 0 4-1 6-2l2 1c-4 2-6 4-11 4-3 0-6-2-8-4s-3-5-2-8zm-80-176c-1 0-2 0-3 1-3-1-6-2-8-5-3-4-3-8-2-13 1-4 4-7 8-9v1c-3 2-6 5-7 9 0 4 1 9 3 12 2 2 3 3 6 3h2l1 1z" class="T"></path><path d="M679 661l1 1c0 1-1 1 0 2 0 2-1 4-1 6v2l1 1h0c1 1 2 2 4 2 5 2 11 3 11 10 0 3-1 7-3 10l-1-1c2-3 3-8 2-11v-2c-1-1-2-2-3-2-4-2-9-3-12-5-1-2-2-3-2-5 0-3 0-6 3-8zM416 826c1-2 0-6 1-8l1 2c0 6 1 15-4 20-1 1-3 2-3 3l-1-1-2-2c-2 0-4 1-5 2s-2 3-2 6l-1-2v-1c0-2 2-5 5-6 2-1 4 0 7 0h1c3-3 3-9 3-13z" class="N"></path><path d="M693 683c1 3 0 8-2 11l1 1c-2 1-3 3-5 3-2 1-3 1-4 0l-1-3 1-3v-1c2-2 5-2 7-3 1 0 1-1 1-2 1-1 1-1 2-1v-2z" class="T"></path><path d="M693 683c1 3 0 8-2 11v1c-2 1-3 2-4 2l-1 1h-1l-1-1v-1c2-1 4-3 5-5 0-1 1-2 1-3 1 0 1-1 1-2 1-1 1-1 2-1v-2z" class="I"></path><path d="M412 794l1-1c1 1 1 5 2 5 1 1 1 1 2 1l1-2v13 10l-1-2c-1 2 0 6-1 8 0-5 1-11-2-16 1-2 0-3 0-4l-2-2c1-4 0-7 0-10z" class="F"></path><path d="M412 794l1-1c1 1 1 5 2 5 1 1 1 1 2 1l-1 9h0l-2-2-2-2c1-4 0-7 0-10z" class="E"></path><path d="M401 848c0-3 1-5 2-6s3-2 5-2l2 2 1 1c0 1 1 1 1 2h1l1 1h1c-1 2-2 2-3 3-3 1-6 0-10 0h-1v-1z" class="b"></path><path d="M888 234c3 4 5 9 7 14-7-1-16-2-23 1l-1 7c5-2 10-3 15-2h0c-4 1-11 1-14 5l-3 6c0-3 1-8 1-11 1-4 0-9 0-13l2 2v4c3 0 8-1 11-1s7 0 11 1c-1-3-2-6-4-8 0-1-1-4-2-5h0zM333 696c0 1 0 2-1 3-2 0-5 0-7-2-3-2-4-4-5-8-1-3-1-6 1-9s6-3 10-3h0l-8 2c-2 2-2 3-2 5l-1 3c2 0 3 0 5 1 0 2 0 4 2 5h1c1 0 2-1 3-1 2 1 2 1 2 4z" class="T"></path><path d="M320 687c2 0 3 0 5 1 0 2 0 4 2 5l5 6c-2-1-5-1-7-3-3-2-4-5-5-9z" class="a"></path><path d="M357 786c-1-1-1-3-1-5 0-3 1-6 3-7 1-2 3-2 5-2 3 0 4 2 5 4l1 1c2-1 3-3 4-5l2 1c-2 2-5 5-6 8 1 1 1 1 3 1h-1c-2 1-3 1-5-1-2-1-3-4-4-6-1 0-2 0-3 1-2 2-2 6-1 8v2h-2z" class="M"></path><path d="M690 679c1 0 2 1 3 2v2 2c-1 0-1 0-2 1 0 1 0 2-1 2-2 1-5 1-7 3v1l-3-1h-1v-1l2-1-1-2h-2v-1c1 0 1-1 2-1v-1c0-1 7-2 8-3 0-1 1-2 2-2z" class="c"></path><path d="M253 564l1 1c0 1-2 10-1 11 2 2 5 4 8 4 2 0 3 0 5-1 1-1 2-3 3-4l7 4 1 1c-1 3-1 5 0 8 1 2 2 2 4 3 1-1 1-2 2-3 0 0 1-1 2-1l2 1v1h-1c-1 2-2 3-2 5-1 2-1 3-1 5 0-1 0-3-1-4-7-3-5-8-8-14-1-1-2-2-4-3-3 2-6 3-9 3-4 0-6-2-9-4-1-3 1-10 1-13z" class="B"></path><path d="M771 482l2-1 3 8c2 2 4 4 5 7-2 3-6 4-10 5 4 8 7 17 4 26-1 4-2 7-4 11l-3-8h1l2 3h0c4-5 4-13 3-19 0-6-3-12-8-16 5 1 9 0 14-2-1-1-2-3-4-5h-4l1-1 1-1c0-2-2-5-3-7zM598 816c0 5 0 19 4 22h1 0c3-1 5-1 7 0 3 2 4 5 5 8 0 2 0 5-2 8-2 2-5 3-8 3-4 1-8-1-12-4h4c2 2 4 2 6 3 4 0 6-1 9-3 1-3 2-5 1-8 0-3-2-4-4-5s-3 0-5 0v1c-2-1-2-1-3-2-5-5-5-15-5-23h2z" class="T"></path><path d="M813 412h1c0 4-1 8-2 12-10 0-22-1-29 6-2 2-4 4-5 7 0 1 1 2 1 3 5 3 11 2 16 5v1c-2-1-4-1-6-2-5 0-9-2-14-4 0-2 0-4 1-6h0v-1c5-7 16-10 24-11 3 0 7 1 11 1 1-3 1-7 2-11z" class="D"></path><path d="M763 496c-1-2-5-6-6-7h6c3 1 5 2 8 2h1 4c2 2 3 4 4 5-5 2-9 3-14 2h0c-1 0-3-1-3-2z" class="d"></path><path d="M890 239h0c2 2 3 5 4 8-4-1-8-1-11-1s-8 1-11 1v-4-3h13c2-1 3-1 5-1z" class="Z"></path><path d="M872 240h13c2-1 3-1 5-1l-1 2h-11c-2 0-3 0-5 1l-1 5v-4-3z" class="C"></path><defs><linearGradient id="A" x1="134.911" y1="263.554" x2="135.627" y2="271.356" xlink:href="#B"><stop offset="0" stop-color="#7b7a78"></stop><stop offset="1" stop-color="#999a93"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M126 264c4 0 10-3 13 1 2 2 2 5 2 8h-1c-2 0-3-1-5-2h-1v-1c-1 2 0 5 0 6 1 2 1 5 1 7v2c0 2 0 6 1 8 0 1 0 2-1 3v-1c-1-2 0-4-1-7-1-5 0-11-2-16v1l-1 1c-1-3-3-6-5-9v-1z"></path><path d="M200 403l1 1c-2 6-1 13 1 19 3-1 7-1 10-1 1-1 2-1 4-1h2 4l2 1h-1-3v1c1 0 1 0 2 1 4 2 14 7 16 12 0 2 0 4-1 5l-1 1h-2 0c-2 0-3 0-4 1h-2c-3 0-5 1-7 2v-1c4-2 9-2 13-4 1 0 1 0 1-1 0-3-2-6-5-8-5-5-13-7-20-7h-7c-1 0-1-1-2-2-2-6-2-12-1-19z" class="B"></path><path d="M212 422c1-1 2-1 4-1h2 4l2 1h-1-3v1c1 0 1 0 2 1l-10-2z" class="c"></path><path d="M899 257v-1c0-1 0-2 1-2v-1l1-1c1 1 3 3 4 5l2 5v4 1h0c-1 2-3 4-4 5h-1c-1 1-2 1-4 1l-1 1h-2v-1-1h1c1-2 1-3 2-4 1-4 1-8 1-11z" class="Z"></path><path d="M417 844v-1c3-1 5-4 7-6v-1h0c0 1 0 2-1 3-1 3-1 6 0 9l-1 1c1 0 2 1 2 1-1 1-2 3-3 4l-2-1c-2 1-4 2-6 2-2 1-6 1-8-1-2-1-3-3-4-5h1c4 0 7 1 10 0 1-1 2-1 3-3h-1l-1-1c1 0 2-1 4-1z" class="I"></path><path d="M419 853l3-4c1 0 2 1 2 1-1 1-2 3-3 4l-2-1z" class="N"></path><path d="M283 599c0-2 0-3 1-5 3 9 6 14 13 19l2 2 1 1c2 3 5 5 8 7 5 4 10 9 16 11h2v-2l-1-1v-4c1 3 2 6 4 7s4 0 6 0l5-2 1 2-18 7-8 4v-1c4-3 9-4 13-6-12-6-22-14-32-22-4-3-8-5-12-8-1-3-1-6-1-9z" class="R"></path><path d="M159 188h0c4 4 5 9 11 9 2 0 4-1 6-2h1v1c-2 1-3 1-5 2l-4 1c0 1 0 1 1 2-2 2-4 5-5 7-1 0-2-1-3-1h-2l1-1-2-1c0 1-1 1-2 2h-1 0l-1-1h-6l-6 7c-2 1-3 3-4 4 2-5 6-10 10-14 2-3 5-5 8-8 1-1 2-2 3-4 0-1-1-2-2-2l2-1z" class="M"></path><path d="M148 206c2-2 3-5 6-5l2-2 1 1-1 1c1-1 1-1 1-2l2-2 1 1-5 9h0l-1-1h-6z" class="E"></path><defs><linearGradient id="C" x1="164.202" y1="199.961" x2="161.401" y2="204.108" xlink:href="#B"><stop offset="0" stop-color="#605f5f"></stop><stop offset="1" stop-color="#767574"></stop></linearGradient></defs><path fill="url(#C)" d="M156 207c2-4 4-7 6-10 2 1 4 1 5 2h1c0 1 0 1 1 2-2 2-4 5-5 7-1 0-2-1-3-1h-2l1-1-2-1c0 1-1 1-2 2z"></path><path d="M157 189c-16 13-29 32-39 51h0c9-20 21-38 38-53-5-5-12-11-14-18-1-3-1-7 1-10 0-1 1-1 2-1l7 5c-2-12-1-25 1-37 1-4 3-8 6-10 6-4 13-6 20-4 2 0 3 0 4 2-3 1-4 3-6 5l1 4c1 3 0 7 1 11v1h-1c0-1-1-3-1-5-1 0-1 0-2-1h2v-4c-1-6-1-8 4-12-7-1-14-1-21 3-4 3-5 7-6 12-2 12-2 26-1 38-3-2-6-4-8-7l-1 1c-2 2-1 6-1 9 2 7 11 14 16 19l-2 1z" class="T"></path><path d="M181 194c2 0 4-1 6-1 2-1 4-3 7-3-2 3-3 8-6 10-1 2-2 5-3 7-1 1-1 2-2 3v-1l-19-1c1-2 3-5 5-7-1-1-1-1-1-2l4-1c2-1 3-1 5-2l4-2z" class="d"></path><path d="M181 194c2 0 4-1 6-1 2-1 4-3 7-3-2 3-3 8-6 10-2-1-5 0-7 0-4 1-8 1-12 1-1-1-1-1-1-2l4-1c2-1 3-1 5-2l4-2z" class="K"></path><path d="M805 383c20-7 36-19 52-33 5-4 11-10 16-13 4-3 9-2 14-1h0c4-9 3-19 5-28v1c0 6 0 12-1 19 0 3 0 6-2 9-3 1-10-1-13 0-2 0-4 2-5 4l-10 8c-11 10-23 19-36 26-3 2-6 4-10 6s-11 3-13 7v1c5 5 11 7 12 15v8h-1c0-4 0-8-2-12-2-6-8-7-13-9 0-2 0-4-1-6-4-5-14-7-20-8v-1l13 2c3 3 7 5 10 7 1 0 5-1 5-2zm-605 20c2-7 8-11 13-15-4-3-9-5-14-8-6-2-11-6-17-9l-24-18c-5-5-11-11-18-15-4-2-10 1-14-1-2-3-2-6-2-9-1-8-1-15-1-23 1 4 1 7 1 11 1 6 1 13 3 20 4-1 9-2 13 0s10 8 14 11c15 13 33 28 52 35h1v-1-1c0-2 0-3 2-4v-1-1h0c3 1 6 2 10 3h4 8 0c-4 1-10 2-13 6-1 1-2 4-2 6l1 1h0c-6 2-12 4-15 10l-1 4-1-1z" class="T"></path><path d="M209 374h0c3 1 6 2 10 3h4l-3 2c-2 2-5 4-6 6-2-1-6-2-8-3h1v-1-1c0-2 0-3 2-4v-1-1z" class="C"></path><path d="M851 114c4 2 8 3 9 7 2 4 2 8 3 12 1 10 2 20 1 30 2-1 4-3 6-5 2 0 2 0 3 2 2 3 1 7 0 10-2 7-8 13-13 18 11 11 21 22 29 35 2 2 4 6 4 8-1-1-1-2-2-3l-6-8c-4-6-21-30-27-30l-1 1c1 2 3 4 5 6l9 10c6 9 12 18 17 27h0c-7-11-14-21-23-31-1 1-2 2-4 2h-2l3 6c1 1 1 1 1 2h-1-1v-1h-1c-1 1-2 1-3 2 0 0-1 0-1 1l-2-2-4-6v-1h0c-1-1-2-2-3-4 1 0 0-1 0-2l3-1h-1 0c-3-1-4-2-6-3v-1c1 1 3 2 5 2 4 0 5-3 7-6 1-2 3-3 4-4 6-7 14-14 14-23 0-2-1-3-2-5-3 2-6 5-9 7 1-15 1-28-2-43l-1-3c-1-2-6-4-8-5v-1z" class="M"></path><g class="X"><path d="M850 199c1-1 2-1 3-1 1 1 3 4 4 5-3 1-6 1-7 3h0c-1-1-2-2-3-4 1 0 0-1 0-2l3-1z"></path><path d="M857 203v-1c-1-2-4-4-3-6l1-1c4 0 7 4 9 7 1 0 1 0 1 1-1 1-2 2-4 2h-2l3 6c-2-2-4-6-5-8z"></path></g><path d="M850 206c1-2 4-2 7-3h0c1 2 3 6 5 8 1 1 1 1 1 2h-1-1v-1h-1c-1 1-2 1-3 2 0 0-1 0-1 1l-2-2-4-6v-1z" class="J"></path><defs><linearGradient id="D" x1="861.353" y1="211.799" x2="884.937" y2="241.704" xlink:href="#B"><stop offset="0" stop-color="#c9c9c5"></stop><stop offset="1" stop-color="#edece8"></stop></linearGradient></defs><path fill="url(#D)" d="M865 203c9 10 16 20 23 31 1 1 2 4 2 5h0c-2 0-3 0-5 1h-13v3l-2-2c-1-9-4-19-8-28h1c0-1 0-1-1-2l-3-6h2c2 0 3-1 4-2z"></path><path d="M862 213h1c4 9 8 17 9 27v3l-2-2c-1-9-4-19-8-28zm-304-77c12-7 25-12 39-17 39-14 80-23 122-11 24 7 49 17 71 30 4 2 9 5 13 8 2 2 4 4 7 5 0 0 1-1 1-2l1-1 2-1c1 0 2 0 3 1h-1l1 1c0 1 1 2 2 3 1 0 4 3 4 4 3-1 7-4 10-4-8 5-16 8-26 8l3-1-1-1 1-1h1l1-1c-6-2-11-5-15-8h2l1 1h0c-1-1-1-1-2-3h0c-5-4-10-7-15-10-11-6-22-11-33-16l-26-9c-20-7-43-9-64-6-22 2-42 9-62 16-14 5-27 10-40 16v-1z" class="F"></path><path d="M798 146l9 6c2 1 5 2 6 4h-1c-6-2-11-5-15-8h2l1 1h0c-1-1-1-1-2-3h0z" class="b"></path><path d="M811 149l1-1 2-1c1 0 2 0 3 1h-1l1 1c0 1 1 2 2 3 1 0 4 3 4 4-5 0-9-2-12-5v-2z" class="Z"></path><defs><linearGradient id="E" x1="251.385" y1="450.114" x2="220.23" y2="458.149" xlink:href="#B"><stop offset="0" stop-color="#d1d0cb"></stop><stop offset="1" stop-color="#fafaf9"></stop></linearGradient></defs><path fill="url(#E)" d="M230 443c1-1 2-1 4-1h0 2l1-1 1 2c1 0 4 1 6 0h1 1c2 2 4 0 6 1l3 3s1-2 2-3l1 2 1 3-1 14v2c-3 3-7 5-10 7l-2-1c-2 0-3 0-5 1-3-2-7-4-10-7-2-2-3-5-4-7s-2-4-4-5-5 0-8-1v-1c1-3 3-5 6-7v1c2-1 4-2 7-2h2z"></path><path d="M244 464l1-2c1 0 2-1 3-1 1-1 1-1 2-1v-1l2-2 1 1v1c-2 1-2 2-3 3h0c-2 1-4 2-6 2z" class="C"></path><path d="M251 449l-1 2c-2 0-4 1-6 1h-5l-2-3h0c5 1 9 1 14 0z" class="b"></path><path d="M230 443c1-1 2-1 4-1h0 2l1-1 1 2c1 0 4 1 6 0h1 1c2 2 4 0 6 1l3 3c1 4 1 8 1 12-1 0-2 1-3 2-1 0-2 1-3 1 1-1 1-2 3-3v-1l-1-1 1-1c1-2 1-5 1-7h-3c-5 1-9 1-14 0l-9-6h2z" class="E"></path><path d="M230 443c1-1 2-1 4-1h0 2l1-1 1 2c1 0 4 1 6 0h1 1c2 2 4 0 6 1-4 0-7 2-11 2-4-1-7-2-11-3z" class="U"></path><path d="M221 444v1c-2 2-4 3-5 6 4 0 8 0 11 3 1 2 2 5 3 7 2 3 4 5 7 7 1-1 1-2 2-3s3-1 5-1 4-1 6-2h0c1 0 2-1 3-1 1-1 2-2 3-2 0-4 0-8-1-12 0 0 1-2 2-3l1 2 1 3-1 14v2c-3 3-7 5-10 7l-2-1c-2 0-3 0-5 1-3-2-7-4-10-7-2-2-3-5-4-7s-2-4-4-5-5 0-8-1v-1c1-3 3-5 6-7z" class="T"></path><path d="M250 462c1 0 2-1 3-1 1-1 2-2 3-2-1 2-1 6-3 8h-1-2-1c0-2 0-2 1-5h0z" class="J"></path><defs><linearGradient id="F" x1="248.541" y1="466.379" x2="238.981" y2="466.031" xlink:href="#B"><stop offset="0" stop-color="#a2a19f"></stop><stop offset="1" stop-color="#d1d0cd"></stop></linearGradient></defs><path fill="url(#F)" d="M244 464c2 0 4-1 6-2-1 3-1 3-1 5h1 2l-2 2c-5 1-9 1-13-1 1-1 1-2 2-3s3-1 5-1z"></path><path d="M559 855c0 1 0 1-1 2v1 1c0 1 0 2-1 3v3l8-5c2 0 4-2 5-3l1 1h-1l1 1c-5 5-12 8-18 12-8 5-17 11-23 18-3 2-5 5-7 8-2 2-6 9-8 10-6-5-9-12-14-17-10-10-21-16-32-24v-3l14 9h1v-3l1-1v-2c3 1 7 4 10 6l2 3 5 7h0 0c4 5 5 13 12 15 2 0 3 0 4-2 2-2 3-4 5-6l1-2c1-5 3-8 6-12-1-1 1-3 1-4 3-2 5-4 7-6 1-1 5-2 7-3 1 0 1 1 2 1 4-1 9-6 12-8z" class="R"></path><path d="M559 855c0 1 0 1-1 2v1 1c0 1 0 2-1 3v3l-10 7-1-8 1-1c4-1 9-6 12-8z" class="J"></path><defs><linearGradient id="G" x1="508.324" y1="897.634" x2="520.499" y2="885.889" xlink:href="#B"><stop offset="0" stop-color="#4e4e4d"></stop><stop offset="1" stop-color="#696865"></stop></linearGradient></defs><path fill="url(#G)" d="M502 882h0c4 5 5 13 12 15 2 0 3 0 4-2 2-2 3-4 5-6l1-2c0 1 0 3 1 4-3 5-6 11-10 15l-12-16c1-1 1-2 0-4 0-1-1-2-1-4h0z"></path><path d="M485 868v-2c3 1 7 4 10 6l2 3 5 7h0 0c0 2 1 3 1 4 1 2 1 3 0 4l-2-2-5-6-9-7c-3-2-2-3-2-7z" class="G"></path><path d="M495 872l2 3 1 5c1 2 2 3 3 4v4l-5-6 3 3c1 1 1 0 1 1 0-1 0-2-1-2 0-1-1-1-1-2l-3-3c-1-1 0-5 0-7z" class="Q"></path><path d="M497 875l5 7h0 0c0 2 1 3 1 4 1 2 1 3 0 4l-2-2v-4c-1-1-2-2-3-4l-1-5z" class="Y"></path><defs><linearGradient id="H" x1="527.127" y1="876.108" x2="542.212" y2="880.115" xlink:href="#B"><stop offset="0" stop-color="#737270"></stop><stop offset="1" stop-color="#969692"></stop></linearGradient></defs><path fill="url(#H)" d="M545 862c1 0 1 1 2 1l-1 1h-1c0 3 1 6 1 8l-21 19c-1-1-1-3-1-4 1-5 3-8 6-12-1-1 1-3 1-4 3-2 5-4 7-6 1-1 5-2 7-3z"></path><path d="M545 862c1 0 1 1 2 1l-1 1h-1c-6 4-10 7-15 11-1-1 1-3 1-4 3-2 5-4 7-6 1-1 5-2 7-3z" class="T"></path><path d="M764 433c1 0 2 1 4 1h0c2 1 4 3 6 3h0c0-2 1-2 2-3-1 2-1 4-1 6 5 2 9 4 14 4 2 1 4 1 6 2v-1c2 2 3 3 4 5v2c-2 1-6-1-9 1-2 2-3 6-4 9-4 5-10 8-15 11l2 8-2 1c-1-3-3-6-5-8-3-2-6-3-9-5-1-1-2-3-2-5-9-4-20-9-30-11v-1c10 3 19 7 28 11 0-3 0-7-1-9 0-2-2-3-3-4 2 0 3 1 5 2 1-1 1-4 2-6 0 1 0 1 1 2h0l3-3c0 2 0 3 2 4 1 1 2 1 2 2l1-1h0v-1l4-2-2-1v-1c-2-4-2-8-3-12z" class="N"></path><path d="M767 459c2 0 4-1 5 0-2 1-5 2-7 2-1 1-1 1-1 2v1c1 0 1 0 2 1h-2l-1 1 1 1 1 1-2 1c-1-1-3-1-4-2l-1-1h3c-1-1-1-1-1-2h2l-1-1v-2c2-1 4-1 6-1v-1z" class="I"></path><path d="M766 465l1-1c1 0 3 0 4-1h1c1 0 2-1 4-1 0 2-1 4 0 6-5 3-8 2-13 1l2-1-1-1-1-1 1-1h2z" class="C"></path><path d="M757 448l3-3c0 2 0 3 2 4 1 1 2 1 2 2l-3 1v1l1 1c1 0 3 1 4 1l1 1c-2 0-3 0-4 1 0 1 3 2 4 2v1c-2 0-4 0-6 1v2l-1-1c-1 0-2 0-2-1-2-2-2-5-2-8 0-1 1-2 1-3v-2z" class="E"></path><path d="M756 453l2 2h0v2h0c2 0 2 1 3 1h1l-3 3 1 1c-1 0-2 0-2-1-2-2-2-5-2-8z" class="f"></path><path d="M757 448l3-3c0 2 0 3 2 4 1 1 2 1 2 2l-3 1v1c-1-1-1 0-1-1 1-1 0-1 1-1-1-1-3-1-4-1v-2z" class="d"></path><defs><linearGradient id="I" x1="764.931" y1="465.846" x2="784.611" y2="457.429" xlink:href="#B"><stop offset="0" stop-color="#c7c6c3"></stop><stop offset="1" stop-color="#f8f7f3"></stop></linearGradient></defs><path fill="url(#I)" d="M772 459c5 0 10-4 14-4-2 6-3 10-9 13h-1c-1-2 0-4 0-6-2 0-3 1-4 1h-1c-1 1-3 1-4 1l-1 1c-1-1-1-1-2-1v-1c0-1 0-1 1-2 2 0 5-1 7-2z"></path><path d="M764 433c1 0 2 1 4 1h0c2 1 4 3 6 3h0c0-2 1-2 2-3-1 2-1 4-1 6 5 2 9 4 14 4 2 1 4 1 6 2l3 4c-5 0-7 1-11 4l-1 1c-4 0-9 4-14 4-1-1-3 0-5 0-1 0-4-1-4-2 1-1 2-1 4-1l-1-1c-1 0-3-1-4-1l-1-1v-1l3-1 1-1h0v-1l4-2-2-1v-1c-2-4-2-8-3-12z" class="Z"></path><path d="M765 450h0v-1l4-2 4 2h0l-1 1c4 0 8 1 11 0h1c-2 1-4 1-6 1-3 1-5 2-9 2-1-1-2-1-4 0v1c1 0 1 0 1 1-1 0-3-1-4-1l-1-1v-1l3-1 1-1z" class="C"></path><path d="M765 450l5 1v1h-4c-2 0-3 1-4 2l-1-1v-1l3-1 1-1z" class="I"></path><path d="M765 450h0v-1l4-2 4 2h0l-1 1-2 1-5-1z" class="c"></path><path d="M766 455c0-1 0-1-1-1v-1c2-1 3-1 4 0l1 1c1 0 3 1 4 2h1 1c3 1 7-2 11-2l-1 1c-4 0-9 4-14 4-1-1-3 0-5 0-1 0-4-1-4-2 1-1 2-1 4-1l-1-1z" class="S"></path><defs><linearGradient id="J" x1="774.053" y1="447.551" x2="773.888" y2="434.936" xlink:href="#B"><stop offset="0" stop-color="#a19f9c"></stop><stop offset="1" stop-color="#d3d1cd"></stop></linearGradient></defs><path fill="url(#J)" d="M764 433c1 0 2 1 4 1h0c2 1 4 3 6 3h0c0-2 1-2 2-3-1 2-1 4-1 6 5 2 9 4 14 4-2 1-5 3-8 3-2 2-5 2-8 2h0l-4-2-2-1v-1c-2-4-2-8-3-12z"></path><path d="M775 440c5 2 9 4 14 4-2 1-5 3-8 3h-1l5-2c-2 0-7 0-9-1-1-1-1-3-1-4z" class="L"></path><defs><linearGradient id="K" x1="456.042" y1="812.481" x2="408.624" y2="837.635" xlink:href="#B"><stop offset="0" stop-color="#d9d8d3"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#K)" d="M418 810h0l4-2v2c1 1 2 1 3 2 1 4 1 10 5 12 1 1 3 1 4 0 4-1 7-4 10-7 0 0 1-2 2-2h0c2-1 3-2 4-4 2 2 4 3 6 5 2 1 3 2 4 4l-11 18 1-1c1-1 2-2 2-3l2 1v3l1 1-3 4c-1 3-3 5-3 7l2 3c-1 2-3 4-4 5-2 0-4 0-6-1-6-4-6-12-8-18-3 3-6 8-9 11 0 0-1-1-2-1l1-1c-1-3-1-6 0-9 1-1 1-2 1-3h0v1c-2 2-4 5-7 6v1c-2 0-3 1-4 1h-1c0-1-1-1-1-2s2-2 3-3c5-5 4-14 4-20v-10z"></path><path d="M417 844c6-6 7-10 8-18l2 4c0 1 0 1 1 2 1 2 1 4 0 6v2c-1 3-2 4-3 6l-2 2c-1-3-1-6 0-9 1-1 1-2 1-3h0v1c-2 2-4 5-7 6v1z" class="e"></path><path d="M440 830v-1l1 1 2 13 6-5 1-1c1-1 2-2 2-3l2 1v3l1 1-3 4c-1 3-3 5-3 7l2 3c-1 2-3 4-4 5-2 0-4 0-6-1-6-4-6-12-8-18-3 3-6 8-9 11 0 0-1-1-2-1l1-1 2-2c1-2 2-3 3-6v-2c1-2 1-4 0-6-1-1-1-1-1-2l4 6c2-1 3-3 5-4 2 0 2-1 4-2z" class="M"></path><path d="M452 834l2 1v3l1 1-3 4c-1 3-3 5-3 7-1 2-1 4-3 5h0l-3-12 6-5 1-1c1-1 2-2 2-3z" class="I"></path><path d="M452 834l2 1v3l1 1-3 4v-2c0-2 1-3 0-4h-2c1-1 2-2 2-3z" class="E"></path><path d="M440 830l5 25c-3 0-5-1-7-4s-4-13-3-17l1-2c2 0 2-1 4-2z" class="h"></path><defs><linearGradient id="L" x1="168.671" y1="236.171" x2="139.289" y2="245.307" xlink:href="#B"><stop offset="0" stop-color="#a1a39d"></stop><stop offset="1" stop-color="#d3d0cb"></stop></linearGradient></defs><path fill="url(#L)" d="M156 207c1-1 2-1 2-2l2 1-1 1h2c1 0 2 1 3 1l-2 4c-8 19-12 37-7 58 1 2 3 5 5 7l-2 1c-1-2-2-3-3-5-3-2-6-3-10-4h-1l-1-2 1 1h1v-11c-1-1-1-1-2-1h0c-2-1-3-1-4-2h0 1 1c1 1 3 0 4 0v-3-1l-1-1c0-1 0-1-1-2h-7c-2 1-5 1-7 1s-5 1-6 0c0-2 1-3 1-5v-1c1-1 1-1 1-2s1-2 1-3c1-1 0-1 1-2l3-5c0-1-1 0 0-2 1 0 1-1 1-1l1-1c0-1 1-2 1-3l3-4c1-1 1-2 2-2 1-1 2-3 4-4l6-7h6l1 1h0 1z"></path><path d="M156 207c1-1 2-1 2-2l2 1-1 1h2c1 0 2 1 3 1l-2 4c-1-1-2-1-4-2h-2c-3 3-3 5-5 8-1 2-1 4-2 6l-1 3c0 2 0 4-1 6v3c0 1 0 0-1 1v5 5c0 1 0 2-1 3l-1-1c0-1 0-1-1-2h-7c-2 1-5 1-7 1s-5 1-6 0c0-2 1-3 1-5l1 1c-1 1-1 2-1 3h4c1-1 3-1 5-1h11l1-6v-3c0-2 0-3 1-4v-4l3-9 2-5c1-2 3-5 4-8h0 1z" class="J"></path><path d="M125 240c2 0 4 0 6-1h4c-1 0-1-1-2-1 3-1 8-1 11-1 0 1 0 2 1 3l-1 6h-11c-2 0-4 0-5 1h-4c0-1 0-2 1-3l-1-1v-1c1-1 1-1 1-2z" class="V"></path><path d="M133 223c1 0 2 0 3 2v2l-1 2h3 2l1-1c2 0 3 0 4 1h1v4c-1 1-1 2-1 4v3c-1-1-1-2-1-3-3 0-8 0-11 1 1 0 1 1 2 1h-4c-2 1-4 1-6 1 0-1 1-2 1-3 1-1 0-1 1-2l3-5c0-1-1 0 0-2 1 0 1-1 1-1l1-1c0-1 1-2 1-3z" class="b"></path><path d="M132 236h11 0-1c-3 1-7 1-9 1s-1 1-2 0l1-1z" class="C"></path><defs><linearGradient id="M" x1="151.373" y1="204.616" x2="137.758" y2="230.453" xlink:href="#B"><stop offset="0" stop-color="#c0c0bb"></stop><stop offset="1" stop-color="#ecebe7"></stop></linearGradient></defs><path fill="url(#M)" d="M148 206h6l1 1c-1 3-3 6-4 8l-2 5-3 9h-1c-1-1-2-1-4-1l-1 1h-2-3l1-2v-2c-1-2-2-2-3-2l3-4c1-1 1-2 2-2 1-1 2-3 4-4l6-7z"></path><path d="M318 106c11-3 22-5 34-4 11 0 22 2 32 5 23 5 45 13 67 22 9 4 19 8 29 13l4 3c0 2 0 4 1 6h0 0c3-2 6-2 9-2 3 1 5 1 6 4 1 2 0 3-1 5-4 4-9 5-15 6 3 3 7 6 10 10 10 12 16 27 21 41 2-15 10-30 20-42 3-3 7-6 9-10-4 1-9-1-12-3s-4-4-4-7c3-4 7-4 12-3 1 0 3 0 5-1 1-1 1-1 0-3 2-4 9-8 13-10v1c-3 3-7 5-11 8 6 2 13 4 19 3 0 1-3 2-4 3l-9 7c-13 10-22 21-29 36-4 8-8 16-9 26l1 2h0c-1 2 0 5 0 7-1 1-1 1-2 1-1-3-2-5-1-8l1-1-2-10c-6-20-18-37-34-51-5-4-10-8-15-11h-1c7 0 14-3 21-3h0l-9-6c-11-6-23-10-34-14-19-7-38-13-58-18-10-2-20-4-31-5-11 0-21 1-32 4l-1-1z" class="T"></path><path d="M485 151h2c3-1 8-1 11 1 1 0 1 1 1 2s0 2-1 2c-2 3-6 5-10 6-2 0-4 0-6-1s-4-3-6-4l-9-7 15-3 1 4 1 1 1-1h0zm62-4c2 0 4 1 5 1l10 2c-6 3-12 11-19 12-3 0-7-1-10-3-2-2-3-3-3-5v-1c4-4 11-2 15-2 2-1 2-2 2-4z" class="V"></path><path d="M553 791h1c1 5 3 9 5 14s4 10 8 14c0-1-1-3-1-4 1-3 3-4 5-6l3 3h1 0l6 8c2 0 2 0 3-1l1 1 1-1 1 1h0c3-4 3-8 4-12l1-1c2-1 2 0 4 1 0 0 0 1 1 2-1 2-1 4-1 6h0c0 8 0 18 5 23 1 1 1 1 3 2v-1c2 0 3-1 5 0s4 2 4 5c1 3 0 5-1 8-3 2-5 3-9 3-2-1-4-1-6-3h-4c-2-1-5-5-7-7-1-1-1-2-2-4l-1-1c-3 3-3 6-5 9-1 4-4 7-7 9l-1-1h1l-1-1c-1 1-3 3-5 3l-8 5v-3c1-1 1-2 1-3v-1-1c1-1 1-1 1-2 3-3 6-7 9-11h0c0-2 1-4 2-5 1-3 1-3 1-6-3-3-6-7-8-10v-1l-1-1c1-1 2-1 3-2-5-9-10-18-12-28z" class="F"></path><path d="M583 841c-1-2-1-2 0-3l1-1c1 1 1 1 1 2s0 2-1 3l-1-1z" class="N"></path><path d="M565 819c3 4 5 8 8 11 2 2 6 5 7 7l-1 3h-1l-7-7c-3-3-6-7-8-10v-1l-1-1c1-1 2-1 3-2z" class="a"></path><path d="M586 846v-1c1-1 1-4 0-5v-1-2c-1-1-1 0-1-1l1-1v-1c0-1 0-1 1-2 0-2 0-2 2-3h0v3c1 0 1 0 1 2v3c0 3 2 7 2 10 2 3 3 5 5 6h-4c-2-1-5-5-7-7z" class="B"></path><path d="M590 837c1 2 3 4 5 4 1 0 2 1 2 2 2 0 4 1 5 1h1l1 1c0 1 1 3 2 4h0c3 1 4 2 6 4-3 2-5 3-9 3-2-1-4-1-6-3-2-1-3-3-5-6 0-3-2-7-2-10z" class="C"></path><path d="M571 833l7 7c-2 1-2 1-3 2l3 3 1 1-1 2c-2 4-5 6-8 9-1 1-3 3-5 3l-8 5v-3c1-1 1-2 1-3v-1-1c1-1 1-1 1-2 3-3 6-7 9-11h0c0-2 1-4 2-5 1-3 1-3 1-6z" class="Q"></path><path d="M570 844l1 2c1-1 1-2 2-2 0 1 0 2 1 3 0 1 0 1-1 2h-1c-1 0-1 0-2 1l-3-2 3-4z" class="X"></path><path d="M570 844c1-2 2-3 3-4 1 1 2 1 2 2l3 3-3 5h0v-7l-1 1v3c-1-1-1-2-1-3-1 0-1 1-2 2l-1-2z" class="W"></path><path d="M571 833l7 7c-2 1-2 1-3 2 0-1-1-1-2-2-1 1-2 2-3 4l-3 4-2 5c-1 1 0 1-1 2 0-2 1-4 2-6 0-1 2-3 2-5h0c0-2 1-4 2-5 1-3 1-3 1-6z" class="M"></path><path d="M568 844c0 2-2 4-2 5-1 2-2 4-2 6-1 1-2 4-1 6h0l1-1h1l-8 5v-3c1-1 1-2 1-3v-1-1c1-1 1-1 1-2 3-3 6-7 9-11z" class="L"></path><path d="M567 819c0-1-1-3-1-4 1-3 3-4 5-6l3 3h1 0l6 8c2 0 2 0 3-1l1 1 1-1 1 1h0 2v5c-1 4-4 7-7 10-2 0-3-1-4-2-4-4-9-9-11-14z" class="Z"></path><path d="M584 819l1 1 1-1 1 1c-1 2-2 3-4 5l-2-5c2 0 2 0 3-1z" class="W"></path><path d="M591 808l1-1c2-1 2 0 4 1 0 0 0 1 1 2-1 2-1 4-1 6h0c0 8 0 18 5 23 1 1 1 1 3 2v-1c2 0 3-1 5 0s4 2 4 5c1 3 0 5-1 8-2-2-3-3-6-4h0c-1-1-2-3-2-4l-1-1h-1c-1 0-3-1-5-1 0-1-1-2-2-2l-3-3c-2-3-3-9-2-13h-1v-5h-2c3-4 3-8 4-12z" class="Z"></path><path d="M591 808l1-1c2-1 2 0 4 1 0 0 0 1 1 2-1 2-1 4-1 6v-1-3l-1-1-2-2c-1 1-1 1-1 2s-1 2-1 3v1h0c-1 3 0 6-1 10h-1v-5h-2c3-4 3-8 4-12z" class="c"></path><path d="M892 265h0c2-2 4-3 5-5v8l-1 4h-1v1 1l-1-1c-3 3-3 9-3 13 0 3 0 6 1 9 1 4 1 9 1 14h-1v-1c-2 9-1 19-5 28h0c-5-1-10-2-14 1-5 3-11 9-16 13-16 14-32 26-52 33 0 1-4 2-5 2-3-2-7-4-10-7 3-1 5-1 8-1l6-3h1l1-1c2 0 3-1 4-1l7-3 6-4 2-1c1-1 2-1 3-2s2-1 3-2l1-1c1 0 2-1 2-1l2-1 2-2c1 0 1-1 2-1 1-1 1-2 2-2 1-1 2-2 3-2 0 0 1-1 2-1 0-1 1-2 2-2 2-1 3-4 5-4 2-2 5-6 8-8 0-1 2-2 3-3 0-1 1-2 2-3s1-2 2-3h1c1-1 3-4 4-5 5-9 7-20 8-31 0-5 0-11 1-17 3-3 6-5 9-8z" class="V"></path><path d="M892 265h0c2-2 4-3 5-5v8h-5-1c-3 1-6 4-8 5 3-3 6-5 9-8z" class="c"></path><path d="M790 378c3-1 5-1 8-1l6-3h1 0c-1 1-2 2-3 2l-1 1h-1c-1 0-1 1-1 1h0c2 1 3 1 4 1l-1 2c1 1 1 1 1 2h1c0-1-1-1 0-2 1 0 0 0 1 1v1c0 1-4 2-5 2-3-2-7-4-10-7z" class="C"></path><path d="M276 522c6-3 9-5 15-4 2 0 3 1 4 2 3 2 6 3 9 5s7 7 11 8v1c-1 1-2 1-3 2s-1 3-2 4l-1 5v-5-4c0-2-1-3-2-3-2 4-4 7-5 11-2 1-2 3-2 5-1 1-2 2-2 4h0v-1h0-2l-2 8-1 2c-1 1-1 1-2 1l-1-1-1 1h-1c-1 2-1 4-1 6-1 6-2 12-2 18-1 0-2 1-2 1-1 1-1 2-2 3-2-1-3-1-4-3-1-3-1-5 0-8l-1-1-7-4c-1 1-2 3-3 4-2 1-3 1-5 1-3 0-6-2-8-4-1-1 1-10 1-11l-1-1c1-13 7-25 14-36-1-2-3-4-4-6l1-1 1 1c1 2 3 4 4 6l6-5 1-1z" class="V"></path><path d="M268 561l2-1h0c0 3-2 4-1 7h0c-1 1-1 2-2 2l-1-1c-1 1-2 1-2 1-1-1-1-2-2-2h0-1c0-2 1-3 1-4l1 1h1v3c1-1 1-2 2-2 1-1 1-2 2-4z" class="c"></path><path d="M268 561l-1-1h0c2-3 5-3 7-5 1-1 1-2 2-2l-1 3h0c0 2 0 2-1 4v2h-1c0 1 0 2-1 3h0c0-2 1-5 1-8-1 3-3 6-4 10-1-3 1-4 1-7h0l-2 1z" class="I"></path><path d="M273 545c3-5 9-9 13-14 1 1 2 2 2 3-2 1-4 3-6 5l1 1-2 3c-1 0-1 0-2-1-1 0-1-1-2 0-2 1-2 3-4 3z" class="X"></path><path d="M283 540h0l2 2c-2 1-3 3-4 5l2 2-1 1-1-1-1 2c-1-1-1-2-2-3l-3 1c-1 0-4 1-6 1v1h-1l2-2c0-1 2-3 3-4 2 0 2-2 4-3 1-1 1 0 2 0 1 1 1 1 2 1l2-3z" class="g"></path><path d="M270 549s1-1 2-1c2-3 3-1 6-2l2-1h0v4h0l1-2 2 2-1 1-1-1-1 2c-1-1-1-2-2-3l-3 1c-1 0-4 1-6 1v1h-1l2-2z" class="L"></path><path d="M296 530c1 1 2 1 3 1-2 1-4 3-6 5l-3 4c0 1-1 3-2 4v-2-1h-2l-1 1-2-2h0l-1-1c2-2 4-4 6-5l8-4z" class="B"></path><path d="M283 540c1-1 2-1 3-2 2 0 4-2 7-3v1h0c-2 1-3 3-3 4s-1 3-2 4v-2-1h-2l-1 1-2-2z" class="U"></path><path d="M285 542l1-1h2v1 2l-2 5-1 2c0 1-1 2-1 3l-2 6c-2 6-3 14-5 20h0l-1-1 8-28h0-1c-2 3-3 7-4 11 0-3 1-6 2-9 1-1 1-2 1-3l1-1-2-2c1-2 2-4 4-5z" class="G"></path><defs><linearGradient id="N" x1="269.226" y1="552.871" x2="264.378" y2="563.208" xlink:href="#B"><stop offset="0" stop-color="#a9a7a5"></stop><stop offset="1" stop-color="#d0d0ca"></stop></linearGradient></defs><path fill="url(#N)" d="M268 551h1v-1c2 0 5-1 6-1l1 1 1 1c0 1 0 2-1 2s-1 1-2 2c-2 2-5 2-7 5h0l1 1c-1 2-1 3-2 4-1 0-1 1-2 2v-3h-1l-1-1c2-4 3-8 6-12z"></path><path d="M275 549l3-1c1 1 1 2 2 3l1-2 1 1c0 1 0 2-1 3-1 3-2 6-2 9v2c0 1 0 1-1 2v1l-1 1h0v-2-2l1-2v-1-1h0l-1 1c-1 3-3 6-4 8h-1v-2c-1 1-1 0-1 2l-1-1c0-1 1-2 1-3h1 0c1-1 1-2 1-3h1v-2c1-2 1-2 1-4h0l1-3c1 0 1-1 1-2l-1-1-1-1z" class="c"></path><path d="M275 549l3-1c1 1 1 2 2 3l1-2 1 1c0 1 0 2-1 3h-1c-2 2-3 6-4 8v-5h0-1 0l1-3c1 0 1-1 1-2l-1-1-1-1z" class="J"></path><path d="M284 554c2 0 4-1 6 1l-1 4-1 2v2c-1 2-1 4-1 6-1 6-2 12-2 18-1 0-2 1-2 1-1 1-1 2-2 3-2-1-3-1-4-3-1-3-1-5 0-8h0c2-6 3-14 5-20l2-6z" class="V"></path><path d="M284 554c2 0 4-1 6 1l-1 4-1 2-1-2h1v-1-2h-3c-1 0-1 1-1 2 1-1 0-1 2-1l-1 1c-1 1-2 1-3 2l2-6z" class="C"></path><path d="M276 522c6-3 9-5 15-4 2 0 3 1 4 2 3 2 6 3 9 5s7 7 11 8v1c-1 1-2 1-3 2s-1 3-2 4l-1 5v-5-4c0-2-1-3-2-3-3-2-5-3-8-2-1 0-2 0-3-1l4-3c-5-3-9-7-15-6-5 1-10 5-14 9-3 3-7 5-9 9-5 8-6 17-8 26l-1-1c1-13 7-25 14-36-1-2-3-4-4-6l1-1 1 1c1 2 3 4 4 6l6-5 1-1z" class="N"></path><defs><linearGradient id="O" x1="298.781" y1="540.626" x2="286.072" y2="558.925" xlink:href="#B"><stop offset="0" stop-color="#787975"></stop><stop offset="1" stop-color="#c8c7c1"></stop></linearGradient></defs><path fill="url(#O)" d="M299 531c3-1 5 0 8 2-2 4-4 7-5 11-2 1-2 3-2 5-1 1-2 2-2 4h0v-1h0-2l-2 8-1 2c-1 1-1 1-2 1l-1-1-1 1h-1v-2l1-2 1-4c-2-2-4-1-6-1 0-1 1-2 1-3l1-2 2-5c1-1 2-3 2-4l3-4c2-2 4-4 6-5z"></path><path d="M286 549l4-1c1 2 1 6 0 9l1-1h0c0 2-1 2-2 3l1-4c-2-2-4-1-6-1 0-1 1-2 1-3l1-2z" class="E"></path><path d="M285 551c2 0 3 0 5 1v3c-2-2-4-1-6-1 0-1 1-2 1-3z" class="I"></path><defs><linearGradient id="P" x1="304.679" y1="533.158" x2="295.756" y2="541.551" xlink:href="#B"><stop offset="0" stop-color="#5f5f5e"></stop><stop offset="1" stop-color="#7e7e7b"></stop></linearGradient></defs><path fill="url(#P)" d="M299 531c3-1 5 0 8 2-2 4-4 7-5 11-2 1-2 3-2 5-1 1-2 2-2 4h0v-1h0-2l4-11-4-2-2-2-1-1c2-2 4-4 6-5z"></path><defs><linearGradient id="Q" x1="320.835" y1="587.622" x2="301.56" y2="619.269" xlink:href="#B"><stop offset="0" stop-color="#d9dad5"></stop><stop offset="1" stop-color="#fffffe"></stop></linearGradient></defs><path fill="url(#Q)" d="M307 533c1 0 2 1 2 3v4 5l1-5c2 1 3 2 5 4l1-1 1 1-1 2h1 1 1l-1 1c-1 3-2 8-2 12l-2 7h1v2 3l1-1c1 1 1 1 2 3 1 0 2 1 4 2v1l-2-1h-2v1 4 2 1l-1 1c1 1 2 2 2 3 2 3 3 6 3 9 1 3 2 5 2 8l-1-1v-1h-1l-1-1s1 1 1 2-1 0 0 1v2c0 1 0 1 1 2 0 1 0 1 1 2 0 2 1 3 2 4l3 9 2 4c1 2 2 5 4 7-2 0-4 1-6 0s-3-4-4-7v4l1 1v2h-2c-6-2-11-7-16-11-3-2-6-4-8-7l-1-1-2-2c-7-5-10-10-13-19 0-2 1-3 2-5h1v-1l-2-1c0-6 1-12 2-18 0-2 0-4 1-6h1l1-1 1 1c1 0 1 0 2-1l1-2 2-8h2 0v1h0c0-2 1-3 2-4 0-2 0-4 2-5 1-4 3-7 5-11z"></path><path d="M307 574l1 1c0 2 1 4 1 6l-1-1h-1l-1 7v-1l-1 1c0-2 0-4-1-6v1h0v-2c-1-2 0-3 1-5 0 2 0 3 1 5 1-1 1-4 1-6z" class="C"></path><defs><linearGradient id="R" x1="312.779" y1="573.726" x2="305.001" y2="582.522" xlink:href="#B"><stop offset="0" stop-color="#afafab"></stop><stop offset="1" stop-color="#d0d0cb"></stop></linearGradient></defs><path fill="url(#R)" d="M306 574c1-2 1-3 2-5 2 1 2 1 2 3 1 3 2 6 1 10h0c0 3 1 7 0 9h-1v-3-1 2 3c-2-3-2-8-1-11 0-2-1-4-1-6l-1-1h-1z"></path><defs><linearGradient id="S" x1="304.003" y1="569.885" x2="299.067" y2="577.881" xlink:href="#B"><stop offset="0" stop-color="#aaa9a3"></stop><stop offset="1" stop-color="#c8c8c2"></stop></linearGradient></defs><path fill="url(#S)" d="M299 571c1-1 1-1 2-1s3 0 5 1c0 2 0 0-1 2v1h1v-1 1h1c0 2 0 5-1 6-1-2-1-3-1-5-1 2-2 3-1 5v2h0c0 2-1 2-1 4l-1 2c0-2 0-5-1-7v-1-1h-3c0 2 1 4-1 7 0-5 1-11 2-15z"></path><path d="M311 591c1-2 0-6 0-9h0l1 6v-1h0c0 2 1 3 1 5 1 1 1 2 1 3l1 1 1 2v2c0 1 0 2 1 3 0 1 0 3 1 5l3 10c1 3 3 6 4 9v4l1 1h-2c-1-1-2-3-2-5-7-11-10-23-11-36z" class="I"></path><defs><linearGradient id="T" x1="320.674" y1="571.224" x2="314.026" y2="591.176" xlink:href="#B"><stop offset="0" stop-color="#80817c"></stop><stop offset="1" stop-color="#cac8c7"></stop></linearGradient></defs><path fill="url(#T)" d="M314 566h1v2 3l1-1c1 1 1 1 2 3 1 0 2 1 4 2v1l-2-1h-2v1 4 2 1l-1 1c1 1 2 2 2 3 2 3 3 6 3 9-1-1-2-1-2-2v-1c-1-1-3-1-4-1v6l-1-2-1-1c0-1 0-2-1-3 0-2-1-3-1-5h0v1l-1-6c1-4 0-7-1-10l3 4c0-4 1-7 1-10z"></path><path d="M310 572l3 4v6l1 1c0 4 0 8 1 12v1l-1-1c0-1 0-2-1-3 0-2-1-3-1-5h0v1l-1-6c1-4 0-7-1-10z" class="E"></path><defs><linearGradient id="U" x1="331.805" y1="605.304" x2="316.465" y2="619.446" xlink:href="#B"><stop offset="0" stop-color="#dadad3"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#U)" d="M316 598v-6c1 0 3 0 4 1v1c0 1 1 1 2 2 1 3 2 5 2 8l-1-1v-1h-1l-1-1s1 1 1 2-1 0 0 1v2c0 1 0 1 1 2 0 1 0 1 1 2 0 2 1 3 2 4l3 9 2 4c1 2 2 5 4 7-2 0-4 1-6 0s-3-4-4-7-3-6-4-9l-3-10c-1-2-1-4-1-5-1-1-1-2-1-3v-2z"></path><defs><linearGradient id="V" x1="314.802" y1="541.79" x2="302.501" y2="570.361" xlink:href="#B"><stop offset="0" stop-color="#545454"></stop><stop offset="1" stop-color="#aeaea7"></stop></linearGradient></defs><path fill="url(#V)" d="M307 533c1 0 2 1 2 3v4 5l1-5c2 1 3 2 5 4l1-1 1 1-1 2h1 1 1l-1 1c-1 3-2 8-2 12l-2 7c0 3-1 6-1 10l-3-4c0-2 0-2-2-3-1 2-1 3-2 5v-1 1h-1v-1c1-2 1 0 1-2-2-1-4-1-5-1s-1 0-2 1c0-4 2-9 1-13l-1-1v-2-2l2-4h-1c0-2 0-4 2-5 1-4 3-7 5-11z"></path><path d="M306 537l1-1h2v4 5h0c0 1 0 2-1 3-1-1 0-5 0-8 0-2 0-2-2-3z" class="U"></path><path d="M307 533c1 0 2 1 2 3v4-4h-2l-1 1c-2 2-3 5-4 7 1-4 3-7 5-11z" class="e"></path><defs><linearGradient id="W" x1="301.806" y1="574.465" x2="288.116" y2="580.067" xlink:href="#B"><stop offset="0" stop-color="#8a8c83"></stop><stop offset="1" stop-color="#b6b3af"></stop></linearGradient></defs><path fill="url(#W)" d="M300 549h1l-2 4v2 2l1 1c1 4-1 9-1 13-1 4-2 10-2 15 0 9 0 21 3 30l-1-1-2-2c-7-5-10-10-13-19 0-2 1-3 2-5h1v-1l-2-1c0-6 1-12 2-18 0-2 0-4 1-6h1l1-1 1 1c1 0 1 0 2-1l1-2 2-8h2 0v1h0c0-2 1-3 2-4z"></path><path d="M294 560h2v-1l-1 4c0 2-1 3-2 4v-5l1-2z" class="f"></path><path d="M291 563c1 0 1 0 2-1v5l-1 5c-1-3-1-6-1-9z" class="C"></path><path d="M296 552h2 0v1h0c0 1-1 6-2 6v1h-2l2-8z" class="J"></path><path d="M296 607v-1c1-2-1-5-1-7v-1c-1-6 0-12 1-18 0-3 0-7 1-9v4c0 3 0 6-1 9v6c0 6 0 12 2 18 0 2 0 4 1 6v1l-2-2c1-1-1-5-1-6z" class="C"></path><defs><linearGradient id="X" x1="303.496" y1="572.965" x2="281.733" y2="599.753" xlink:href="#B"><stop offset="0" stop-color="#cbcbc5"></stop><stop offset="1" stop-color="#fefdfd"></stop></linearGradient></defs><path fill="url(#X)" d="M288 563h1l1-1 1 1c0 3 0 6 1 9-1 12 1 23 4 35 0 1 2 5 1 6-7-5-10-10-13-19 0-2 1-3 2-5h1v-1l-2-1c0-6 1-12 2-18 0-2 0-4 1-6z"></path><path d="M759 378c1 0 3-1 5-1h1c2-1 5-1 8-1 1 1 2 1 3 1h1c6 1 16 3 20 8 1 2 1 4 1 6 5 2 11 3 13 9 2 4 2 8 2 12-1 4-1 8-2 11-4 0-8-1-11-1-8 1-19 4-24 11v1h0c-1 1-2 1-2 3h0c-2 0-4-2-6-3h0c-2 0-3-1-4-1l-2-2v-1l-1-3c-1-1-1-2-1-2l-1-2 1-1-1-1-2-1-1-1 2-1h-1c-1 0-2 1-3 1l-2-4c-1-1-1-1-2-1-1-1-1-5-2-6 4-2 8-3 13-4h-1c-3 1-8 1-12 2-1 1 0 0-1 0h-1c2-2 4-3 7-4 0-1 1-1 2-1l2-1c-2-3-4-5-6-7-1 0-6-5-6-5l-6-5c2 0 4 0 7-1v1l6-2 3-1h0l4-2z" class="V"></path><path d="M773 399c2 0 5 0 8 1l-1 1c2 0 2 0 3 1l-6-1c-1-1-2-2-4-2z" class="Z"></path><path d="M759 398c1 0 7 1 8 0l3 1h3c2 0 3 1 4 2l-9-1c-3-1-6-1-9-2z" class="c"></path><path d="M759 386c2-2 2-2 4-2l2 1c1 1 2 3 4 3h1c2 0 4 0 6-2v-1l1 1c-3 2-4 3-8 3-2 0-4-1-6-1l-1 1h0v1h-1c-1-2-2-2-2-4z" class="I"></path><path d="M759 394c-2-1-4-2-5-3s-1-1-1-2l-1-1c1 0 1-1 2-1h1v-1c1-1 1-1 3-2l1 3v-1c0 2 1 2 2 4h1v-1l3 2h1c3 1 7 1 10 1h0c-2 1-6 0-9 0-1 0-3-1-4-1h-2l-1-1-2 1h-3c1 1 2 1 2 2 1 0 1 0 2 1z" class="S"></path><path d="M755 380h0l4-2c-2 2-4 3-4 5 1-1 2-1 3-1h0c1-1 1-1 2-1l1-1c4 0 9 1 14 1 0-1 1-1 1 0h0c-3 2-8 1-12 1l8 2c-1 1-2 2-3 4-2 0-3-2-4-3l-2-1c-2 0-2 0-4 2v1l-1-3h0v-1l-4 1c-1-1-2-2-2-3l3-1z" class="b"></path><path d="M757 400l1 1h0c4 0 6 0 10-1l9 1 6 1c7 2 14 4 19 10v1c-3-2-5-4-8-6h-2c-1 1-3 0-5-1-2 0-4-1-7-1l-6-2-13 1h-1c-3 1-8 1-12 2-1 1 0 0-1 0h-1c2-2 4-3 7-4 0-1 1-1 2-1l2-1z" class="X"></path><path d="M774 403c7-1 14 1 20 4h-2c-1 1-3 0-5-1-2 0-4-1-7-1l-6-2zm-28-20l6-2c0 1 1 2 2 3l4-1v1h0c-2 1-2 1-3 2v1h-1c-1 0-1 1-2 1l1 1c0 1 0 1 1 2s3 2 5 3h1c7 2 15 2 22 4h0c-5-1-13-2-18-1h-3-3l1 1c3 1 6 1 9 2-4 1-6 1-10 1h0l-1-1c-2-3-4-5-6-7-1 0-6-5-6-5l-6-5c2 0 4 0 7-1v1z" class="C"></path><path d="M746 382v1 1l2 1h2c0 1-1 2-1 2 1 4 5 5 7 8h-1c-1-1-3-2-4-2s-6-5-6-5l-6-5c2 0 4 0 7-1z" class="L"></path><defs><linearGradient id="Y" x1="769.237" y1="412.427" x2="779.936" y2="427.499" xlink:href="#B"><stop offset="0" stop-color="#cccbc2"></stop><stop offset="1" stop-color="#f2efec"></stop></linearGradient></defs><path fill="url(#Y)" d="M759 416h1 1 9c3 0 8-1 12 0h2c2 1 5 1 7 3l2 1v1h-3 0c-2 0-2 0-3 1h-2l-4 1h-4c-3 0-6 1-9 1-1 0-1 0-2 1-1 0-2 0-3 1-1 0-1 0-2 1-1-1-1-2-1-2l-1-2 1-1-1-1-2-1-1-1 2-1h-1l-1-1 1-1h2z"></path><path d="M757 418l-1-1 1-1 1 1h1c1 0 1 0 2 1h6c-2 1-5 3-7 4l-1-1-2-1-1-1 2-1h-1z" class="I"></path><path d="M784 416c2 1 5 1 7 3h-2-1v1h-2c-4-1-8 0-12 0v-1c3-1 6-1 10-1h1l-1-1h-1l1-1z" class="C"></path><defs><linearGradient id="Z" x1="750.362" y1="407.395" x2="766.584" y2="412.766" xlink:href="#B"><stop offset="0" stop-color="#999895"></stop><stop offset="1" stop-color="#d2d1cd"></stop></linearGradient></defs><path fill="url(#Z)" d="M761 404l13-1 6 2h-6-3l-1-1-1 2h2 0c-2 1-3 1-4 2v1h11c0 1-1 1-1 2h1l-1 1h-2c-1 1-2 1-4 1-4 1-8 1-12 3h-2l-1 1 1 1c-1 0-2 1-3 1l-2-4c-1-1-1-1-2-1-1-1-1-5-2-6 4-2 8-3 13-4z"></path><path d="M793 420l1-1v1l4 1c1 0 1 0 2 1-8 1-19 4-24 11v1h0c-1 1-2 1-2 3h0c-2 0-4-2-6-3h0c-2 0-3-1-4-1l-2-2v-1l-1-3c1-1 1-1 2-1 1-1 2-1 3-1 1-1 1-1 2-1 3 0 6-1 9-1h4l4-1h2c1-1 1-1 3-1h0 3v-1z" class="S"></path><path d="M231 377c8-1 15-3 23-1l8 3c4 1 8 3 11 5-2 2-6 5-8 7l-3 3c-2 2-3 3-3 6v1l-1 1c1 1 2 1 3 2h0c-2 0-2 0-3 1l6 1c1 2 0 4 0 6 0 3 0 6-2 8v1c-1 0-1 0-2 1h0l-1 1c-2 2-4 4-5 7h0c-1 3-2 6-4 9-1 2-2 3-4 4h-1-1c-2 1-5 0-6 0l-1-2c1-1 1-3 1-5-2-5-12-10-16-12-1-1-1-1-2-1v-1h3 1l-2-1h-4-2c-2 0-3 0-4 1-3 0-7 0-10 1-2-6-3-13-1-19l1-4c3-6 9-8 15-10h0l-1-1c0-2 1-5 2-6 3-4 9-5 13-6z" class="V"></path><defs><linearGradient id="a" x1="243.401" y1="404.564" x2="242.099" y2="399.436" xlink:href="#B"><stop offset="0" stop-color="#696662"></stop><stop offset="1" stop-color="#838381"></stop></linearGradient></defs><path fill="url(#a)" d="M242 400c6 0 11 1 17 1l-1 1c1 1 2 1 3 2h0c-2 0-2 0-3 1-1 0-3-1-4-1-2 0-4-1-7-1-4-1-13-1-18 1-9 2-15 5-21 12h-1c0-1 1-2 1-2 8-10 22-13 34-14z"></path><path d="M246 388l17 1c0 1 0 1-1 1v1h3l-3 3c-2 2-3 3-3 6v1c-6 0-11-1-17-1 2 0 4 0 5-1l2-2c-6-1-12-1-17 0l-4 1c-5 1-11 3-15 6l-2 1h-1c9-6 18-8 29-9 2-1 5-1 8-2h0c2 1 3 0 5 1h1 1c1 1 2 0 3 0v-1c-3 0-7 0-9-1h-2-1c-2-1-5-1-8-1-2 0-5 0-7 1-5 1-10 2-15 4h-1c4-2 9-4 13-4l5-1c3 0 7 0 10-1 2 0 4 1 5 0l1-1h1c1 0 3 0 4-1h-1c-2 0-3 0-5-1h-1z" class="C"></path><path d="M262 394c-2-2-9-1-12-2 2-1 5 0 8-1 1-1 3-1 4-1v1h3l-3 3z" class="d"></path><defs><linearGradient id="b" x1="260.028" y1="410.662" x2="245.992" y2="410.844" xlink:href="#B"><stop offset="0" stop-color="#9c9a96"></stop><stop offset="1" stop-color="#cbc9c4"></stop></linearGradient></defs><path fill="url(#b)" d="M229 404c5-2 14-2 18-1 3 0 5 1 7 1 1 0 3 1 4 1l6 1c1 2 0 4 0 6 0 3 0 6-2 8v1c-1 0-1 0-2 1h0 0c-1-2-3-2-4-3h-2c-3-1-5-1-8-2-5 0-14-1-19 1l-9 3h-2c3-1 6-3 9-4 8-2 18-2 26 0h1c1-1 1-1 2 0l1-1c-2-1-3-2-5-2l-11-2c-1-1-4 0-5-1l1-1h8l-1-1h-3c-1 0-1 0-3-1h1c1 0 2-1 3 0h3c1 1 2 2 3 2l1-1-1-2c-2-3-8 0-11-1 0-1-1-1-1-2h-5z"></path><path d="M229 404c5-2 14-2 18-1l3 2h-2l-1 1h0c-3-2-10-3-13-2h-5z" class="C"></path><path d="M251 417c3 1 7 2 11 2l-3-6c-1 0 0 0-1-1 1-1 1-2 2-3h1v-1c-2 0-4-1-6-2h4c1 1 2 1 3 1 0 1 1 1 1 2v1h1v2c0 3 0 6-2 8v1c-1 0-1 0-2 1h0 0c-1-2-3-2-4-3h-2c-3-1-5-1-8-2-5 0-14-1-19 1l-9 3h-2c3-1 6-3 9-4 8-2 18-2 26 0z" class="J"></path><defs><linearGradient id="c" x1="248.221" y1="437.388" x2="224.617" y2="416.67" xlink:href="#B"><stop offset="0" stop-color="#bcbab6"></stop><stop offset="1" stop-color="#e4e4e3"></stop></linearGradient></defs><path fill="url(#c)" d="M227 418c5-2 14-1 19-1 3 1 5 1 8 2h2c1 1 3 1 4 3h0l-1 1c-2 2-4 4-5 7h0c-1 3-2 6-4 9-1 2-2 3-4 4h-1-1c-2 1-5 0-6 0l-1-2c1-1 1-3 1-5-2-5-12-10-16-12-1-1-1-1-2-1v-1h3 1l-2-1h-4l9-3z"></path><path d="M245 443v-1c1-1 1-2 3-2v-1c2-2 2-3 2-5 1-2 2-3 4-4-1 3-2 6-4 9-1 2-2 3-4 4h-1z" class="a"></path><path d="M254 419h2c1 1 3 1 4 3h0l-1 1c-2 2-4 4-5 7-2-2-5-2-8-3l8-1 1-1v-1l-1-1h2l-1-1-3-1 2-2z" class="f"></path><path d="M227 418c5-2 14-1 19-1 3 1 5 1 8 2l-2 2h-2c-1 0-2 1-3 1 1 1 3 2 5 2v1c-2 0-2 0-3 1h-3-3v1c1 0 3 1 4 1l1 1v2h-2l-1-1c-1 1-1 1-3 1-4-2-7-5-11-7-2-1-5-2-7-2l-2-1h-4l9-3z" class="b"></path><path d="M227 418c5-2 14-1 19-1 3 1 5 1 8 2l-2 2h-2l-2-1c-6 0-12-2-17-1h-2 0-1-1c-1 1-1 1-2 1-1 1-1 1-2 1l-1-1 5-1v-1z" class="C"></path><path d="M246 417c3 1 5 1 8 2l-2 2h-2l-2-1v-1l1 1h1l1-1-5-2z" class="d"></path><defs><linearGradient id="d" x1="253.497" y1="396.284" x2="226.135" y2="374.171" xlink:href="#B"><stop offset="0" stop-color="#d6d5cf"></stop><stop offset="1" stop-color="#fbf9f8"></stop></linearGradient></defs><path fill="url(#d)" d="M231 377c8-1 15-3 23-1l8 3c4 1 8 3 11 5-2 2-6 5-8 7h-3v-1c1 0 1 0 1-1l-17-1c-8 0-17 0-25 2h-4 0l-1-1c0-2 1-5 2-6 3-4 9-5 13-6z"></path><path d="M254 376l8 3c0 1 0 3-1 3-1 1-1 0-2 1-1 0-1 0-2 1-2 0-4 0-5 1-2 0-4-1-6-1v-1h4c1-1 2-1 3-1v1h-1 1c2 0 3-1 3-2h-1-1c-2 1-3 0-4-1h0c2-1 4 0 6-1 1 0 1 0 1-1-1 0-2-1-3-2z" class="C"></path><path d="M262 379c4 1 8 3 11 5-2 2-6 5-8 7h-3v-1c1 0 1 0 1-1l-17-1c-8 0-17 0-25 2h-4 0c2-1 5-1 7-2 5 0 10-1 15-1h12c3 1 9 2 12 1-1-1-1-1-2-1h0c-1 0-1-1-2-1h-2l-5-1c1-1 3-1 5-1 1-1 1-1 2-1 1-1 1 0 2-1 1 0 1-2 1-3z" class="E"></path><path d="M159 188c-5-5-14-12-16-19 0-3-1-7 1-9l1-1c2 3 5 5 8 7-1-12-1-26 1-38 1-5 2-9 6-12 7-4 14-4 21-3-5 4-5 6-4 12v4h-2c1 1 1 1 2 1 0 2 1 4 1 5 1 2 2 4 4 5-2 1-2 0-4 0-3-1-7-5-11-4-1 1-2 2-3 4h1 1l1 1 1-1c2 1 4 4 6 6 2 1 4 3 6 4h0c5 4 12 8 19 9 2 0 5 1 7 1 4-1 7-3 11-4 1 0 3 1 5 1l13 2c4 1 8 1 12 1 1 0 3 1 5 0 4 2 12 3 16 3h2c0 2 1 2 0 4l-6 5 2 1-2 3c-2 1-4 1-6 1-5-1-10-1-15-1-5 1-11 0-16 1l-3 7h-8c-1 1-3 1-5 1 0 0-2-1-3-1-7-4-9-2-13 4 0 1-1 2-1 2-3 0-5 2-7 3-2 0-4 1-6 1l-4 2v-1h-1c-2 1-4 2-6 2-6 0-7-5-11-9h0z" class="h"></path><path d="M258 177c4-1 4-3 6-5l2 1-2 3c-2 1-4 1-6 1z" class="M"></path><path d="M183 168c2 0 5 1 7 2 3 0 7 0 10 1s7 0 10 0h26c4-1 8 0 12-1l14-1h1l-5 1c-4 1-9 1-13 1l-26 1c-4 0-8 1-12 1-7-1-14-1-20-3-2-1-3-1-4-2z" class="c"></path><path d="M177 191l-6-7 4 1c6 3 14 6 20 3 0 1-1 2-1 2-3 0-5 2-7 3-2 0-4 1-6 1l-4 2v-1c1-2 0-2 0-4z" class="D"></path><path d="M177 191c2-1 4-1 6 0l1 1c-1 1-3 1-3 2l-4 2v-1c1-2 0-2 0-4z" class="W"></path><path d="M817 148h1c5 0 9-4 13-7 2-2 4-4 5-6 2-5 1-12 1-17l-5-5h1c6-3 12-1 18 1v1c2 1 7 3 8 5l1 3c3 15 3 28 2 43 3-2 6-5 9-7 1 2 2 3 2 5 0 9-8 16-14 23-1 1-3 2-4 4-2 3-3 6-7 6-2 0-4-1-5-2v1c2 1 3 2 6 3h0 1l-3 1c0 1 1 2 0 2h-1c-5 0-12 0-17 1h0l-2-3-2-3-1-2c-1-1-2-2-2-3-1-2-1-3-1-4l-3-1c0-1 0-1-1-2-3-4-7-3-12-1-1 1-2 2-3 2s-4-1-5-2c-1 0-7 1-7 0 0 0-1-1-1-2-2-2-3-4-6-4-7-3-18 1-25-2-3-2-7-5-10-7l-2-2v1c0-1 0-2 1-3 1-2 5-3 7-4s8-1 11-1l12-1c5-1 10-2 15-2l15 3c10 0 18-3 26-8-3 0-7 3-10 4 0-1-3-4-4-4-1-1-2-2-2-3l-1-1h1z" class="h"></path><path d="M746 168c0-1 0-2 1-3 1-2 5-3 7-4-1 2-1 2-3 3v1c2 2 5 2 7 2l15 2h-4-2l-1-1-8-1c-3 0-9-2-11 0h-1v1z" class="S"></path><path d="M821 188c4 2 7 3 12 2 4-1 9-4 13-6-3 2-5 4-7 7-3 2-10 3-14 2l-2-1h-1c-1-2-1-3-1-4z" class="D"></path><path d="M817 148h1c5 0 9-4 13-7 2-2 4-4 5-6 2-5 1-12 1-17l-5-5h1c6-3 12-1 18 1v1c-4-2-11-4-15-2h-1l5 3-2 1v8l13 2v-3l5 6h-1 0c-4-3-11-4-16-3h-1c0 6-1 10-5 15-3 2-5 4-8 5l-3 1c-2 1-3 1-5 1l-1-1h1z" class="T"></path><path d="M822 192h1l2 1c4 1 11 0 14-2 1 1 2 3 4 4v1c2 1 3 2 6 3h0 1l-3 1c0 1 1 2 0 2h-1c-5 0-12 0-17 1h0l-2-3-2-3-1-2c-1-1-2-2-2-3z" class="H"></path><path d="M822 192h1l2 1c4 1 11 0 14-2 1 1 2 3 4 4v1c-1-1-3-2-4-2-2-1-7 1-9 1s-5-1-7-2l1 2c-1-1-2-2-2-3z" class="e"></path><path d="M825 197c2 0 18 0 19 2l2 2v1c-5 0-12 0-17 1h0l-2-3-2-3z" class="X"></path><path d="M827 200l19 1v1c-5 0-12 0-17 1h0l-2-3zm-2-53c2 1 5 0 7-2 5-4 7-8 13-10l3 3c1 0 2 1 3 3v2h-1v-1c-1-1-1-2-2-2-2 0-5 5-6 6-2 2-4 4-7 5 0 0-1 1-2 1-3 0-7 3-10 4 0-1-3-4-4-4-1-1-2-2-2-3 2 0 3 0 5-1l3-1z" class="E"></path><path d="M319 107c11-3 21-4 32-4 11 1 21 3 31 5 20 5 39 11 58 18 11 4 23 8 34 14l9 6h0c-7 0-14 3-21 3h1c5 3 10 7 15 11 16 14 28 31 34 51l2 10-1 1c-1 3 0 5 1 8 1 0 1 0 2-1 0-2-1-5 0-7h0l-1-2c1-10 5-18 9-26 7-15 16-26 29-36l9-7c1-1 4-2 4-3-6 1-13-1-19-3 4-3 8-5 11-8 13-6 26-11 40-16 20-7 40-14 62-16 21-3 44-1 64 6l26 9c11 5 22 10 33 16 5 3 10 6 15 10h0c1 2 1 2 2 3h0l-1-1h-2c4 3 9 6 15 8l-1 1h-1l-1 1 1 1-3 1-15-3c-5 0-10 1-15 2l-12 1c-3 0-9 0-11 1s-6 2-7 4c-1 1-1 2-1 3v-1l2 2c3 2 7 5 10 7 7 3 18-1 25 2 3 0 4 2 6 4 0 1 1 2 1 2 0 1 6 0 7 0 1 1 4 2 5 2s2-1 3-2c5-2 9-3 12 1 1 1 1 1 1 2l3 1c0 1 0 2 1 4 0 1 1 2 2 3l1 2 2 3 2 3h0c5-1 12-1 17-1h1c1 2 2 3 3 4h0v1l4 6 2 2c0-1 1-1 1-1 1-1 2-1 3-2h1v1h1c4 9 7 19 8 28 0 4 1 9 0 13 0 3-1 8-1 11v6c2 0 5 0 6-1 3-2 4-5 7-6 2-1 3-1 5-1 1 0 3 0 4-1 3-1 6-3 8-5 0 3 0 7-1 11-1 1-1 2-2 4l1-4v-8c-1 2-3 3-5 5h0c-3 3-6 5-9 8-1 6-1 12-1 17-1 11-3 22-8 31-1 1-3 4-4 5h-1c-1 1-1 2-2 3s-2 2-2 3c-1 1-3 2-3 3-3 2-6 6-8 8-2 0-3 3-5 4-1 0-2 1-2 2-1 0-2 1-2 1-1 0-2 1-3 2-1 0-1 1-2 2-1 0-1 1-2 1l-2 2-2 1s-1 1-2 1l-1 1c-1 1-2 1-3 2s-2 1-3 2l-2 1-6 4-7 3c-1 0-2 1-4 1l-1 1h-1l-6 3c-3 0-5 0-8 1l-13-2v1h-1c-1 0-2 0-3-1-3 0-6 0-8 1h-1c-2 0-4 1-5 1l-4 2h0l-3 1-6 2v-1c-3 1-5 1-7 1l6 5s5 5 6 5c2 2 4 4 6 7l-2 1c-1 0-2 0-2 1-3 1-5 2-7 4h1c1 0 0 1 1 0 4-1 9-1 12-2h1c-5 1-9 2-13 4 1 1 1 5 2 6 1 0 1 0 2 1l2 4c1 0 2-1 3-1h1l-2 1 1 1 2 1 1 1-1 1 1 2s0 1 1 2l1 3v1l2 2c1 4 1 8 3 12v1l2 1-4 2v1h0l-1 1c0-1-1-1-2-2-2-1-2-2-2-4l-3 3h0c-1-1-1-1-1-2-1 2-1 5-2 6-2-1-3-2-5-2 1 1 3 2 3 4 1 2 1 6 1 9-9-4-18-8-28-11v1c10 2 21 7 30 11 0 2 1 4 2 5 3 2 6 3 9 5 2 2 4 5 5 8 1 2 3 5 3 7l-1 1-1 1h-1c-3 0-5-1-8-2h-6c1 1 5 5 6 7 0 1 2 2 3 2h0c5 4 8 10 8 16 1 6 1 14-3 19h0l-2-3h-1c-3-4-6-8-10-11-1-1-2-1-3-1l-1 1-8 10c3 3 5 6 7 10 5 8 6 17 7 27 1 2 2 6 1 9 0 2-3 4-4 5-2 1-4 1-7 1-2-1-5-2-7-1s-2 1-3 3c-1 3-2 8-4 10-1 1-2 1-4 1h0l-1 2c1 3 1 9-1 11-3 3-7 6-11 8l-25 18c-2 2-5 3-7 5 8 4 18 5 21 15 1 4 0 9-2 13-2 2-4 4-7 5-1 0-3 0-4-1l-4-3-11 6-1-1v-2c0-2 1-4 1-6-1-1 0-1 0-2l-1-1c-3 2-3 5-3 8 0 2 1 3 2 5 3 2 8 3 12 5-1 0-2 1-2 2-1 1-8 2-8 3v1c-1 0-1 1-2 1v1h2l1 2-2 1v1h1l3 1-1 3h-1c-1 1-2 2-4 3-1 5-2 10-7 13-3 1-6 1-9 0-2 0-3-1-5-2v3c0 3 0 6-2 9-1 1-3 3-6 3h-8c-1 6-3 11-4 16a104.13 104.13 0 0 0-5 12c2 4 3 7 4 10 2 5 5 12 10 14 2-1 3-3 6-4 2 0 4 0 5 1 3 3 3 6 3 9s-1 5-3 7c-3 3-8 4-12 4-7 0-12-4-17-8-1 2-2 4-2 6-1 3-3 12-5 13s-3 1-4 0c-1 0-2-1-3-2l-3-2c-1 0-3 2-4 4-2 2-7 6-8 8v4h-2 0c0-2 0-4 1-6-1-1-1-2-1-2-2-1-2-2-4-1l-1 1c-1 4-1 8-4 12h0l-1-1-1 1-1-1c-1 1-1 1-3 1l-6-8h0-1l-3-3c-2 2-4 3-5 6 0 1 1 3 1 4-4-4-6-9-8-14s-4-9-5-14h-1c2 10 7 19 12 28-1 1-2 1-3 2l1 1v1c2 3 5 7 8 10 0 3 0 3-1 6-1 1-2 3-2 5h0c-3 4-6 8-9 11-3 2-8 7-12 8-1 0-1-1-2-1-2 1-6 2-7 3-2 2-4 4-7 6 0 1-2 3-1 4-3 4-5 7-6 12l-1 2c-2 2-3 4-5 6-1 2-2 2-4 2-7-2-8-10-12-15h0 0l-5-7-2-3c-3-2-7-5-10-6v2l-1 1v3h-1l-14-9h-1c-3-1-6-5-9-7l-2 2-5-6-1 1-2-3c0-2 2-4 3-7l3-4-1-1v-3l-2-1c0 1-1 2-2 3l-1 1 11-18c-1-2-2-3-4-4-2-2-4-3-6-5-1 2-2 3-4 4h0c-1 0-2 2-2 2-3 3-6 6-10 7-1 1-3 1-4 0-4-2-4-8-5-12-1-1-2-1-3-2v-2l-4 2h0v-13l-1 2c-1 0-1 0-2-1-1 0-1-4-2-5l-1 1c0 3 1 6 0 10l-4-6h-2v1c-2 1-2 3-4 4-1 1-2 1-3 1h-2c-4-2-4-8-6-12 0-2-1-5-2-7l-4 3c-6 4-13 6-20 5-4-1-6-3-8-7h2v-2c-1-2-1-6 1-8 1-1 2-1 3-1 1 2 2 5 4 6 2 2 3 2 5 1h1c-2 0-2 0-3-1 1-3 4-6 6-8l3-2 1-4c1-3 3-7 3-10 1-2 0-4 0-5l1-1-2-1c-2-5-4-11-5-16l-3-12c-3 2-5 2-8 2-2 0-4-1-6-3-3-4-2-10-1-14-2 1-3 3-5 4h-1v-1h-2-2c-4-1-6-3-8-5l-1 1-5-10-1-1-1 1c0-3 0-3-2-4-1 0-2 1-3 1h-1c-2-1-2-3-2-5-2-1-3-1-5-1l1-3c0-2 0-3 2-5l8-2h0c1-1 2-2 2-3h0l-9-6-4 2-1-1h-2c-3 0-4-1-6-3-2-3-3-8-3-12 1-4 4-7 7-9l8-4 18-7-1-2-5 2c-2-2-3-5-4-7l-2-4-3-9c-1-1-2-2-2-4-1-1-1-1-1-2-1-1-1-1-1-2v-2c-1-1 0 0 0-1s-1-2-1-2l1 1h1v1l1 1c0-3-1-5-2-8 0-3-1-6-3-9 0-1-1-2-2-3l1-1v-1-2-4-1h2l2 1v-1c-2-1-3-2-4-2-1-2-1-2-2-3l-1 1v-3-2h-1l2-7c0-4 1-9 2-12l1-1h-1-1-1l1-2-1-1-1 1c-2-2-3-3-5-4 1-1 1-3 2-4s2-1 3-2v-1c-4-1-8-6-11-8s-6-3-9-5c-1-1-2-2-4-2-6-1-9 1-15 4l-1 1-6 5c-1-2-3-4-4-6l-1-1-1 1-5-5-3 1c-1 2-3 3-4 4-4 5-7 10-9 16-2-4-4-8-4-13-2-7-1-17 4-23l-1-2c-3 0-6-1-9-3v-2c1-2 2-4 4-5 2-4 6-14 5-18 2-1 3-1 5-1l2 1c3-2 7-4 10-7v-2l1-14-1-3-1-2c-1 1-2 3-2 3l-3-3c-2-1-4 1-6-1 2-1 3-2 4-4 2-3 3-6 4-9h0c1-3 3-5 5-7l1-1h0c1-1 1-1 2-1v-1c2-2 2-5 2-8 0-2 1-4 0-6l-6-1c1-1 1-1 3-1h0c-1-1-2-1-3-2l1-1v-1c0-3 1-4 3-6l3-3c2-2 6-5 8-7-3-2-7-4-11-5l-8-3c-8-2-15 0-23 1h0-8-4c-4-1-7-2-10-3h0c-1 0-2 0-3-1l-8-4-2-1-5-3c-2-1-3-2-4-2l-6-5-1-1c-2 0-1 1-2 0 0-1-1-1-1-2h-1l-1-1-3-3c-2 0-1 1-2 0l-4-4-3-3c-4-2-7-6-10-9 0 0-2-1-2-2l-1-1c-1-1-2-2-2-4l-2-2-3-3-3-6-1-2c0-1 0-1-1-2v-1-2h-1v-2c-1-1-1-1-1-2v-3c-1-2-1-2-1-3v-1-3c1-1 1-2 1-3-1-2-1-6-1-8v-2c0-2 0-5-1-7 0-1-1-4 0-6v1h1c2 1 3 2 5 2h1c0-3 0-6-2-8-3-4-9-1-13-1h0c-3-4-8-4-11-8 0-2 0-2 1-2h1v1h1c3 3 7 5 11 7h1c2-1 4-3 7-2 2 1 4 3 5 5l1 2 1 2h1c4 1 7 2 10 4 1 2 2 3 3 5l2-1c-2-2-4-5-5-7-5-21-1-39 7-58l2-4 19 1v1c1-1 1-2 2-3 1-2 2-5 3-7 3-2 4-7 6-10 0 0 1-1 1-2 4-6 6-8 13-4 1 0 3 1 3 1 2 0 4 0 5-1h8l3-7c5-1 11 0 16-1 5 0 10 0 15 1 2 0 4 0 6-1l2-3-2-1 6-5c1-2 0-2 0-4h-2c-4 0-12-1-16-3-2 1-4 0-5 0-4 0-8 0-12-1l-13-2c-2 0-4-1-5-1-4 1-7 3-11 4-2 0-5-1-7-1-7-1-14-5-19-9h0c-2-1-4-3-6-4-2-2-4-5-6-6l-1 1-1-1h-1-1c1-2 2-3 3-4 4-1 8 3 11 4 2 0 2 1 4 0-2-1-3-3-4-5h1c3 4 9 11 14 12 4 1 8-1 11 1 1 0 1 1 1 1v1c19-15 39-25 62-33l25-8c6-1 12-4 18-5 1-1 2-1 3 0 0 1 0 2 1 2h4l1 1z" class="T"></path><path d="M674 353c2 0 3 0 5 1l-4 1-1 1-1-1 1-2zM485 825l1-2c1 0 1 1 2 1 0 2-1 3-2 4l-1-1v-2z" class="B"></path><path d="M415 656l3-2c0 1-1 4-2 5h0-1v-3z" class="W"></path><path d="M756 219l6-1v1c-1 1-2 1-3 2-1-1-2-1-3-2z" class="F"></path><path d="M550 772c1 2 1 3 1 5 0 0-1 1-2 1v1l-1 1c0-3 1-5 2-8z" class="O"></path><path d="M535 279c1 1 3 2 4 2-1 1-1 1-1 2-1 1-1 2-2 2-1-2-1-4-1-6z" class="a"></path><path d="M188 261h6v1l-1 1c-1 0-2 0-3 1-1 0-1-1-2-1v-2z" class="M"></path><path d="M148 295l1 1 1 4-1 3-2-6 1-2z" class="Y"></path><path d="M532 264v11l-3-6c1 0 1 0 2-1 0-1 0-3 1-4z" class="J"></path><path d="M483 830c1-2 1-3 1-6l1 1v2l1 1c-1 2-2 4-3 5v-3z" class="O"></path><path d="M614 570c1-2 2-4 3-7l3 2c-1 1-2 2-4 3-1 1-1 1-1 2h-1z" class="P"></path><path d="M147 288c0 2 1 5 1 7l-1 2-2-5 2-4z" class="e"></path><path d="M603 335h0c0 2 0 4-1 5l-1 1-1-1v-3c1 0 2-1 3-2z" class="N"></path><path d="M599 599l3-8 2 8-2-2c-1 0-2 1-3 2z" class="M"></path><path d="M145 281l1 2 1 5-2 4-1-5c1-2 1-4 1-6z" class="P"></path><path d="M311 295c1 1 2 1 4 2 0 1 1 2 0 3-2 0-4 0-6-1 1 0 2-1 3-1s0 0 1-1c-1 0-1-1-2-2z" class="F"></path><path d="M291 244c1-1 1-1 3-1l1 5-2 1c0-1 0-1-1-2v-1c0-1 0-1-1-2z" class="R"></path><path d="M823 263c2 1 3 2 5 4v1h-2v2h1v-1h1 0c1 0 1-1 2-1-2 2-3 3-5 3-1-1 1-4 0-5 0-1-2-2-2-3z" class="F"></path><path d="M616 561v-1c-2-2-3-4-2-6 1 0 1 2 3 3l1 1 1 2h0-2l-1 1z" class="W"></path><path d="M333 365h5l2 7-3-2c0-1 0-1-1-2h1v-1c-2-1-4-1-5-2v-1l1 1z" class="D"></path><path d="M578 234c0 1 0 2 1 2v1c-1 2-3 5-4 7l-1-2 4-8zm38 327l1-1h2 0c1 0 3 2 4 3l1 2h-1c-3-1-5-2-7-4z" class="P"></path><path d="M504 377c-2-3-3-6-4-9h2l2 2v7z" class="J"></path><path d="M511 406l2 4s0 1 1 2v1l1 1h1v2h1c-1 1-3 1-5 0 0-3 0-6-1-10z" class="K"></path><path d="M249 219h0 1c1-2 2-3 4-4h3v1c-2 1-3 2-5 3-1 1-2 2-4 2 0 1 0 1-1 1v-2l2-1z" class="M"></path><path d="M200 337c3 1 6 3 9 3v1 1h1-1c-2 1-4 0-5-1l-5-3 1-1z" class="N"></path><path d="M150 300l5 8-1 3c-2-2-3-5-5-8l1-3z" class="Q"></path><path d="M581 218c1 2 1 3 0 5h0-2c-1-1-3-2-4-4 2 0 4 0 6-1z" class="b"></path><path d="M430 640c1 5 3 9 3 14h-1l-1-3c-1-4-2-7-1-11z" class="F"></path><path d="M411 584c1 3 3 7 2 10v1 1h-1-1v-8-4z" class="B"></path><path d="M444 214c-2-1-4-4-6-6l7 1v1c1 2 1 3 0 4h-1z" class="R"></path><path d="M444 214c0-1 0-2-1-3v-2h1l1 1c1 2 1 3 0 4h-1z" class="M"></path><path d="M590 638v-3c0-2 2-9 3-10l1 1c-1 2 0 4 0 6-1 1-2 2-2 3s1 2 0 3l-1-2-1 2z" class="O"></path><path d="M571 213c1 3 2 4 4 6 1 2 3 3 4 4h-3 0l-1 1c-2-2-4-6-6-9l2-2z" class="R"></path><path d="M438 273c1-2 1-2 2-3 3 0 7 4 8 6 1 1 1 1 1 2-4-1-6-5-11-5z" class="O"></path><path d="M333 173l5 3h-20l8-1-5-1h3 5c1 0 3 0 4-1z" class="D"></path><path d="M695 287c0-4 3-11 4-14 0 3 1 5 1 8-1 1-1 1-2 3l1 1c-1 1-2 1-3 1l-1 1z" class="B"></path><path d="M305 270c1 3 2 5 3 7 1 4 3 7 4 11h0c-2-2-3-6-4-9h-1v1c-1 1-1 2-2 3l-1-1c1-2 1-6 0-8l1-4z" class="R"></path><defs><linearGradient id="e" x1="665.884" y1="410.343" x2="669.839" y2="414.841" xlink:href="#B"><stop offset="0" stop-color="#474846"></stop><stop offset="1" stop-color="#605e5e"></stop></linearGradient></defs><path fill="url(#e)" d="M662 415h0c1-2 1-3 2-4 5 0 9 1 14 2-2 1-3 1-6 1s-7-1-10 1z"></path><path d="M448 391c3 1 6 2 8 4s3 3 4 5l-3-1c-2-1-7-3-8-5 0 0-1-2-1-3z" class="H"></path><path d="M528 274c2 5 3 10 5 15-1 0-1 1-2 1-1-2-2-5-3-7 0-3-1-6 0-9z" class="Y"></path><path d="M489 834c-3-3 0-4 1-7 0-3-1-7-1-9h0l3 12c-1 2 0 3-1 5-1 0-1 0-2-1h0z" class="B"></path><path d="M519 479l1 13-1 31c0 3 0 8 1 12v8h-1v-64z" class="Q"></path><path d="M555 199c1 4 0 8 1 12s2 7 2 12v2c-1 0-1-1-1-1-3-6-2-10-2-15v-10z" class="E"></path><defs><linearGradient id="f" x1="670.137" y1="381.293" x2="675.8" y2="378.641" xlink:href="#B"><stop offset="0" stop-color="#646364"></stop><stop offset="1" stop-color="#797a77"></stop></linearGradient></defs><path fill="url(#f)" d="M683 381c-6 0-12 1-17 3l-1 1c4-4 7-6 11-8l1 1-1 1c1 1 2 1 4 1h1c1 0 1 0 2 1z"></path><path d="M596 660c0-1 0-1-1-2-1-2-2-6-2-9 2 0 6 6 7 8 0 0-1 1-2 1s-1 1-2 2z" class="D"></path><defs><linearGradient id="g" x1="762.946" y1="209.623" x2="772.136" y2="213.692" xlink:href="#B"><stop offset="0" stop-color="#2a2b2b"></stop><stop offset="1" stop-color="#444443"></stop></linearGradient></defs><path fill="url(#g)" d="M760 208c5 1 9 1 13 5l3 4c-1 0-2-1-2-2h-1-1-1c-1 1-3-1-4-2v-1c-2-1-3-2-5-2h-1c1-1 1 0 1-1h1l-3-1z"></path><defs><linearGradient id="h" x1="682.03" y1="341.43" x2="688.667" y2="336.649" xlink:href="#B"><stop offset="0" stop-color="#494948"></stop><stop offset="1" stop-color="#646362"></stop></linearGradient></defs><path fill="url(#h)" d="M677 342c0-1 1-2 1-3 7-1 12-2 19 0-4 1-6 1-10 1-2 0-4 0-6 1l-4 1z"></path><path d="M683 325l1 2h0l3-1h1v1l-2 3c-1 2-3 4-6 5 0-2 2-7 3-10z" class="F"></path><path d="M453 233h0c-2-2-3-4-4-6 2 1 5 2 7 3 1 0 3 2 3 2l1-1c1 0 2 1 3 1h-1l1 1s1 1 1 2h0-1c-1-1-2-1-3-2h-1 0l-1 1-1-1c-1 0-3-1-4 0z" class="O"></path><path d="M547 784c1 2-1 4 0 6 0 4-2 8-2 13l-2-3c-1-3 3-13 4-16z" class="H"></path><path d="M184 264l1-1c1-1 4-5 4-6-1-2-2-3-2-6h0c3 3 4 5 8 6h-1l1 2c-2 1-2 1-4 1l-1-1h-1l-1 1v2c-1 1-2 1-3 2h-1z" class="R"></path><path d="M706 249h1c1 2 4 4 5 5-4 1-7 2-10 5h0 0c0-3 1-5 2-6l2-2v-2h0z" class="D"></path><path d="M463 232c2 0 3 0 4 1h0v1 1c0 1-1 1-1 1l1 2c-2 0-2-1-3-2l-1 1c-3-1-6-2-8-2-1 0-2-2-2-2 1-1 3 0 4 0l1 1 1-1h0 1c1 1 2 1 3 2h1 0c0-1-1-2-1-2l-1-1h1z" class="H"></path><path d="M260 223v-2c0-1 0-1 2-2 2 0 3 1 5 3v3l1 1v3h0v-1c0-1 0-2 1-4 0 1 0 4 1 4 0 1 1 1 1 2v2 1h0l-1-1h0c0 1-1 2 0 3h-1c-1-2-3-9-3-11v-1c-2-1-3-2-5-3v2l-1 1zm431 168l1 1c2 1 3 3 4 5-4-1-8-3-13-4h-7c-1 0-3 0-4-1 3-1 6-1 9-1l1 1h3 1 0 3c2 0 1 0 2-1z" class="R"></path><path d="M594 632c1 1 1 2 1 4h1v1c0 1 0 2 1 4v1l1 6c-3-3-6-7-8-10l1-2 1 2c1-1 0-2 0-3s1-2 2-3z" class="U"></path><path d="M594 632c1 1 1 2 1 4l-1 1c0 1 1 3 0 4l-2-2v-1c1-1 0-2 0-3s1-2 2-3z" class="H"></path><path d="M252 219v1h1c1-1 1-2 3-2 1 0 2 1 3 1 0 1 0 1-1 2l1 3h0l-2-1h0c-1 1-2 1-2 1h-1-1c-2-1-3-1-5-3 2 0 3-1 4-2z" class="F"></path><path d="M652 426c1-1 1-4 3-5 1-1 4-2 6-1 0 1-1 4-1 5l1 1c-1 0-1 1-2 1-2-1-5 0-7-1z" class="H"></path><path d="M410 695c1-2 2-4 4-4h3c0 1 0 10-1 12v7l-1-4v-1h-1v-4c0-2 1-5 0-8h-1c-1 1-2 1-3 1v1z" class="K"></path><path d="M448 742v-3h1v-10c0-2-1-4 0-6 1-1 0-3 1-5v13c1 0 1 0 1-1h1v3c0 3 1 7 0 10l-2-8c0 3-1 7-1 11l-1-4z" class="D"></path><defs><linearGradient id="i" x1="512.886" y1="410.303" x2="521.114" y2="404.197" xlink:href="#B"><stop offset="0" stop-color="#61625d"></stop><stop offset="1" stop-color="#78777a"></stop></linearGradient></defs><path fill="url(#i)" d="M518 396l2 4c-1 3-1 6-2 9 0 2 0 5-1 7h-1v-2h-1l-1-1v-1l1-1c2-4 1-10 3-15z"></path><path d="M184 329c3 2 6 4 9 5s5 2 7 3l-1 1c-1-1-4-1-6-2-4-2-10-3-14-6h4l1-1z" class="Y"></path><path d="M499 528c0 3 0 9 1 11 1 1 2 4 3 6l2 9-1-1 1 3v2l-3-6c-1-4-3-9-3-13-1-3 0-7 0-11z" class="W"></path><path d="M412 652l5-5c-1 6-7 9-10 14h1l1-1-5 5v-2c-1 1-2 1-3 1v-1c3-2 8-7 11-11z" class="F"></path><path d="M155 308c4 6 9 11 15 16 0-1-1-1-2 0h1v1c-6-3-11-8-15-14l1-3z" class="G"></path><path d="M610 300h1 3c1 1-1 5-2 6l-9 4c1 0 1-1 1-1l5-6c-1 0-2 0-3-1h-1c2-1 3-1 5-2z" class="X"></path><path d="M492 830c1 4 7 16 6 20h0c-1-2-3-6-5-7-1-1-1-3-2-4l-2-5h0c1 1 1 1 2 1 1-2 0-3 1-5z" class="O"></path><defs><linearGradient id="j" x1="592.497" y1="630.992" x2="598.503" y2="634.008" xlink:href="#B"><stop offset="0" stop-color="#414141"></stop><stop offset="1" stop-color="#62625f"></stop></linearGradient></defs><path fill="url(#j)" d="M594 632c0-2-1-4 0-6 1 2 3 5 4 6s2 1 2 3h0l-1 1 1 2v1h-1 0l-1 1c-1 1-1 1-1 2v-1c-1-2-1-3-1-4v-1h-1c0-2 0-3-1-4z"></path><path d="M575 371c4 1 5 3 8 5 0 1 1 2 1 3v1l-5 1-5-9h0l1-1z" class="B"></path><path d="M597 642c0-1 0-1 1-2l1-1h0v1h1c0 1 1 4 1 5 1 2 1 4 2 6s2 3 2 5l-2-2c-2-2-4-4-5-6l-1-6z" class="Y"></path><path d="M599 640h1c0 1 1 4 1 5 1 2 1 4 2 6s2 3 2 5l-2-2c0-2-2-4-3-6s-1-5-1-8z" class="U"></path><defs><linearGradient id="k" x1="409.524" y1="695.262" x2="409.978" y2="701.275" xlink:href="#B"><stop offset="0" stop-color="#6e6d6c"></stop><stop offset="1" stop-color="#888784"></stop></linearGradient></defs><path fill="url(#k)" d="M410 695v-1c1 0 2 0 3-1h1c1 3 0 6 0 8-3 0-5 1-8 1h-1c1-3 3-5 5-7z"></path><path d="M182 224l1 1c-2 4-2 8-3 12 0 3-1 11 1 13 0 2 0 3-1 4v1l1 2v3-1c-4-6-3-19-2-26l1-3 2-6z" class="J"></path><path d="M424 321l-2-5 23 11v1l-6-3h-1l-3-2-1 1-6-2c-2-1-3-2-4-1z" class="H"></path><path d="M428 322c2-1 3-1 5 0 1 0 1 1 2 1l-1 1-6-2z" class="e"></path><path d="M452 377c0-1 0-2 2-3l5 1v1c0 1 0 1-1 2s-1 1-1 3c-1 0-1 1-2 2v1c-2-1-3-2-5-3l2-4z" class="B"></path><path d="M452 377v1l1 1c0 1 2 3 2 4v1c-2-1-3-2-5-3l2-4z" class="D"></path><path d="M760 212h0c-6-1-13-1-18 2-3 1-7 4-10 4 9-5 18-10 28-10l3 1h-1c0 1 0 0-1 1h1c2 0 3 1 5 2v1l-2-1c-2-1-3-1-5 0z" class="B"></path><defs><linearGradient id="l" x1="368.245" y1="465.746" x2="362.05" y2="466.446" xlink:href="#B"><stop offset="0" stop-color="#3e3e3d"></stop><stop offset="1" stop-color="#585858"></stop></linearGradient></defs><path fill="url(#l)" d="M355 460c6 1 11 4 16 8-3 2-8 1-11 1 0-1 0-2 1-3v1-1h0c-1-1-2-2-2-3h0l-4-3z"></path><path d="M292 246v1c1 1 1 1 1 2l2-1 7 13h-2c-2 0-2-1-4 0h-3 0c1-1 2-2 3-2v-1l-1-3c-1-2 0-3-1-4-2 1-3 3-5 4v-1c1-1 2-2 2-4 0-1 1-2 1-4zm-18-17c0-1-1-1 0-2 0 1 1 2 1 4 1-1 1-3 2-4 1 1 2 2 2 4v1l-1 1h0l-1 2v1 2l1-1c-1 2-1 3-3 5v2h-1 0l-1-7v-2h0l1-6z" class="F"></path><path d="M273 237v-2h0l1-6c0 3-1 5 1 7v-2 1c1 0 1 0 1 1s0 1 1 2l1-1c-1 2-1 3-3 5v2h-1 0l-1-7z" class="M"></path><path d="M616 617l-2-1c-4-7-7-14-9-21-1-3-2-6-1-9l1 3h0c3 5 4 10 5 15 2 4 4 8 6 13z" class="P"></path><path d="M615 174c1-1 2-1 4-1v1c1 0 5 1 6 0 1 0 2-1 2-1 1 0 0 2 2 1 1 0 1 1 1 2h-8-17-4l2-3c2 1 4 1 6 1h5 1z" class="J"></path><path d="M603 173c2 1 4 1 6 1h5c-2 1-7 1-9 2h-4l2-3z" class="L"></path><path d="M499 489c1 8 1 17 1 25v8l1-1 1 15c0 3 1 6 1 9-1-2-2-5-3-6-1-2-1-8-1-11v-39z" class="U"></path><defs><linearGradient id="m" x1="677.729" y1="375.329" x2="686.015" y2="378.541" xlink:href="#B"><stop offset="0" stop-color="#81817e"></stop><stop offset="1" stop-color="#a09f9b"></stop></linearGradient></defs><path fill="url(#m)" d="M676 377h0c3-2 6-3 9-4 2 1 4 3 6 4-2 0-4 0-6 2 0 1 1 2 1 3l-3-1c-1-1-1-1-2-1h-1c-2 0-3 0-4-1l1-1-1-1z"></path><path d="M299 357l1 2c-1 1-2 1-3 1v1h-1c-2 1-4 3-6 4l-1 2c1-1 1-1 2-1 2-2 4-3 6-4l1-1c3-1 0 1 2-1h3l-6 3c-7 4-11 10-17 14l-2-1 11-11c1 0 3-2 3-2l6-4 1-2z" class="B"></path><path d="M686 392c-1 0-2-1-3-1h-1c-4-1-9-1-13 1-1 0-3 1-5 1l3-2c4-2 11-4 16-4 3 0 6 2 8 4-1 1 0 1-2 1h-3z" class="F"></path><path d="M305 283c1-1 1-2 2-3v1l4 11c-4 0-7-1-10-3l-1-1v-1c2-1 3-3 5-4z" class="H"></path><path d="M493 863l8-6 2 8c-1 1-2 1-2 3v5l-1-1c-1-3-4-6-7-9z" class="C"></path><path d="M656 435c1 2 2 4 4 6v1c1 1 1 2 2 3l-1 1c-5 0-12 0-16 3l-1 1v-1c0-1 1-3 1-3h1c3-2 6-3 10-4v-6-1z" class="N"></path><path d="M145 269c4 1 7 2 10 4l-2 2c-1 0-2-1-3-1v2c-1 0-2 0-2 1-2 1-2 4-2 6l-1-2c0 2 0 4-1 6v-5-1c0-2 1-2 0-4 0-2 0-5 1-7v-1z" class="B"></path><path d="M145 281v-8c2 0 4 1 5 1v2c-1 0-2 0-2 1-2 1-2 4-2 6l-1-2z" class="U"></path><path d="M522 593c0-1 0-2 1-3l1 2h0c0 1 0 2 1 3h0c1 1 1 2 1 3s0 1 1 1c-1 2-1 3-1 3-2 2-2 7-3 9l-1-6-2-3c1-2 1-5 1-7l1-2z" class="R"></path><path d="M524 592h0c0 1 0 2 1 3h0v8c-2-3-1-7-1-11z" class="B"></path><path d="M522 593c0 3 1 10 0 12l-2-3c1-2 1-5 1-7l1-2z" class="F"></path><path d="M539 807c1 0 2 1 2 2 1 3 0 10-1 12h-1v1h-1c-2 1-2 4-4 6l-1-1 6-20z" class="D"></path><path d="M578 397l8-4-2 10c-1 1-1 3-2 4 0 1-2 2-3 2v-2c-1-2-1-3-1-5l1-1c1-2 0-3-1-4z" class="G"></path><path d="M447 718c1 2 1 2 1 4v1 22c0 1-1 2 0 3 0-2-1-4 0-6l1 4c-1 9-2 17-5 26 0-3 0-4 1-6 0-1-1-1-1-2 2-10 3-19 3-29v-11-6z" class="N"></path><path d="M595 269c1 2 1 3 3 3h1c0 2-1 3-1 5h0l-3 3c-1 2-2 3-3 4h-3c-1-1-1-2-1-3 4-4 4-8 7-12z" class="D"></path><path d="M489 845c1-2 1-4 2-6 1 1 1 3 2 4 0 5-3 10-5 15l-1 1-2-1v-1-3-3h1 1l2-6z" class="R"></path><path d="M489 845c1 1 1 2 1 2 0 2-2 4-3 5 0 2-1 4 1 6l-1 1-2-1v-1-3-3h1 1l2-6z" class="B"></path><path d="M291 352c2 2 6 3 8 5l-1 2-6 4c-2-1-8-1-10-2 2 0 8 0 9 1h0v-1l-2-1-1-3v-1c1-2 2-2 3-4z" class="H"></path><path d="M317 330h2l1 2c-1 1-2 1-3 2l1 1h-1v1l-3 3-1-1c0-1 0 0 1-1 1 0 2-1 2-2-2 0-4 2-5 3l-3 1-2 2h-1 0 1 1 0v1h-1l-1 1c-1 0-2 3-3 3s-1 0-2 1l-1-1 3-3c1-1 2-1 3-3l1-1c-1-1-1-1-2-1l-1 1-1-1h0c0-1-1-2-1-2l1-1h2 1v1h2c3 0 8-2 10-4h-1l-1-1c1 0 2 0 2-1z" class="N"></path><defs><linearGradient id="n" x1="506.2" y1="404.057" x2="515.8" y2="390.443" xlink:href="#B"><stop offset="0" stop-color="#616463"></stop><stop offset="1" stop-color="#a39e9d"></stop></linearGradient></defs><path fill="url(#n)" d="M507 385l2 2 1-1v1l1 1c1 3 2 7 2 9 0 5 2 9 2 14l-1 1c-1-1-1-2-1-2l-2-4-4-21z"></path><path d="M244 213h0v2 1h0c1 0 1-1 1-2l3-3h1c1 1 1 2 0 3l-1 1h1l1-2h3v1l-4 5-2 1v2l-1-1c-1 1-1 1-2 1-1-1-1-2-2-2h-4c1-1 2-3 3-5 1-1 2-1 3-2z" class="B"></path><path d="M241 215l2 4v-1l2-1h0v2l1 1 1-1v1 2l-1-1c-1 1-1 1-2 1-1-1-1-2-2-2h-4c1-1 2-3 3-5z" class="P"></path><defs><linearGradient id="o" x1="768.102" y1="220.141" x2="777.795" y2="224.583" xlink:href="#B"><stop offset="0" stop-color="#4b4b4a"></stop><stop offset="1" stop-color="#717270"></stop></linearGradient></defs><path fill="url(#o)" d="M760 212c2-1 3-1 5 0l2 1c1 1 3 3 4 2h1 1 1c0 1 1 2 2 2 2 3 3 7 2 11l-1 2c-1 1-2 2-3 2s-2 1-2 1h-1l2-3h1c1-2 1-3 1-5l-1-1v-1c-3-6-8-9-14-11z"></path><path d="M454 247h0 1c0 6 1 10 2 16 0 3 0 6 1 9l-1 2h-1v-3c-1-5-3-10-5-15 0-2 1-3 1-4v-1c1-1 1-3 2-4z" class="R"></path><path d="M452 252c2 1 2 2 2 4 1 2 3 14 2 15-1-5-3-10-5-15 0-2 1-3 1-4z" class="D"></path><path d="M414 165l12 11h-1c-7 0-16 1-24 0 2-1 5 0 6-1l3-3c1-1 1 0 2 0 2 0 4-2 5-2-1-2-2-3-3-5h0z" class="J"></path><path d="M605 580l7-22c1 1 3 4 3 5s-1 3-1 3c-1 4-2 7-2 10-2 0-3 0-5 1 0 1-1 2-2 3z" class="D"></path><path d="M280 172c5 1 10 2 14 4h-30l2-3c5-1 9-1 14-1z" class="Y"></path><defs><linearGradient id="p" x1="414.503" y1="660.991" x2="403.851" y2="664.564" xlink:href="#B"><stop offset="0" stop-color="#454545"></stop><stop offset="1" stop-color="#787875"></stop></linearGradient></defs><path fill="url(#p)" d="M409 660c2 0 4-2 6-4v3h1 0c-1 3-3 5-5 7-3 2-5 4-7 7l-1-1v-2c1-1 1-1 1-2-1-1-2-2-3-4h0c1 0 2 0 3-1v2l5-5z"></path><path d="M618 620l1-1h1 1c0-2-1-3-1-5h2 0l1 1 4 7c-1-1-1-1-2-1 0 2 1 4 2 6 2 3 4 5 6 7 1 1 1 2 2 3-7-4-13-11-17-17z" class="L"></path><path d="M472 200c1 7 5 19 0 25-1 1-2 2-3 2-1-4 0-7 1-11l2-16z" class="E"></path><path d="M471 850c1 0 1 0 2 1h1l2 2 2 1h0c2 1 4 3 6 4h1l2 1c1 1 3 3 5 4h1c3 3 6 6 7 9 0 3 3 5 2 7-3-7-7-12-14-16-3-2-6-3-8-5-3-2-6-5-9-8z" class="H"></path><path d="M416 605l1-1 2 1 3 8c1 3 1 7 2 10h0c0 1 1 2 1 3v1c-2-1-1-2-2-3h-2c0 1-2 4-2 4l-1-2c1-1 1-3 1-4v-5c-1-1-1-3-2-4v-2c0-1-1-2-1-3v-1h-1l1-2z" class="B"></path><path d="M416 605c2 2 2 6 4 9 0 2 0 2-1 3-1-1-1-3-2-4v-2c0-1-1-2-1-3v-1h-1l1-2z" class="D"></path><defs><linearGradient id="q" x1="421.517" y1="618.697" x2="417.513" y2="622.276" xlink:href="#B"><stop offset="0" stop-color="#2b2a2b"></stop><stop offset="1" stop-color="#404240"></stop></linearGradient></defs><path fill="url(#q)" d="M420 614c0 1 1 2 1 3 1 4 2 4 0 7 0 1-2 4-2 4l-1-2c1-1 1-3 1-4v-5c1-1 1-1 1-3z"></path><path d="M462 838c-1-1-1-1-1-2 1-2 3-3 4-4s2-1 3-2c-1 3-1 5-1 7l1 1c0 2 1 3 1 5l3 4c0 1 1 1 1 2l5 5h0l-2-1-2-2h-1c-1-1-1-1-2-1l-3-3c-2-3-5-6-6-9z" class="W"></path><path d="M462 838c-1-1-1-1-1-2 1-2 3-3 4-4s2-1 3-2c-1 3-1 5-1 7l1 1c0 2 1 3 1 5l3 4h-1c-2-2-4-6-5-10h-1l-2-1-1 2z" class="F"></path><path d="M706 344c3 2 7 6 8 10 0 1 0 2-1 3-2 0-5-1-7-2 0-1 0-1-1-2 0-1-1-1-2-1h-1c1-2 2-3 2-5h-1c-1-1-1-1-1-2h1c2 0 2 0 3-1z" class="d"></path><defs><linearGradient id="r" x1="294.425" y1="255.072" x2="285.466" y2="260.036" xlink:href="#B"><stop offset="0" stop-color="#2d2d2d"></stop><stop offset="1" stop-color="#51514f"></stop></linearGradient></defs><path fill="url(#r)" d="M289 255c2-1 3-3 5-4 1 1 0 2 1 4l1 3v1c-1 0-2 1-3 2h-1v1 1h-6l-2-2-1-2 1-2 5-3v1z"></path><path d="M295 255l1 3v1l-2-2 1-2z" class="D"></path><path d="M289 254v1c0 3-1 4-4 5l-1 1-1-2 1-2 5-3z" class="N"></path><path d="M807 257v-1c0-2-3-5-4-7l6 6h0l1-1c1 1 2 3 4 5s5 4 7 6l2 2c0 1 0 1-1 2h0c-1-1-1-1-2-1h-1l2 1v1l-1 1-1-1c-1-1-3-2-4-4-1-1-1-2-2-3h-1l-1-1c-1-1-2-3-4-4v-1z" class="D"></path><path d="M820 268c-1-1-2-2-3-4h1l2 2 1-1 2 2c0 1 0 1-1 2h0c-1-1-1-1-2-1z" class="e"></path><path d="M333 387c4 0 12 2 15 5h0c-5-1-10-1-15-1-5 1-10 2-15 4 2-2 4-6 7-7h1c2-1 4-1 7-1z" class="R"></path><path d="M292 262v-1h1 0 3c2-1 2 0 4 0h2c1 2 2 4 2 6 0 1 1 2 1 3l-1 4v-2l-2-1c-1 1-2 1-3 3-1-1 0-1 0-2l2-1v-1c-2-1-2 0-4 0 1-1 1-1 1-2l-2 1c-1-3-2-4-4-6v-1z" class="D"></path><path d="M292 262h3 0c1 1 1 1 2 1s2 1 3 2l2 2h-1c-1 1-2 1-3 1l-2 1c-1-3-2-4-4-6v-1z" class="P"></path><path d="M302 314c3-1 7-1 9-1 3 0 6 1 9 2h1c0 2 1 3 1 5h-1c-3-1-8-1-11-1h-4-1l-3-2c1 0 2 0 3-1h0c-1-1-2-1-2-1l-1-1z" class="F"></path><path d="M678 382c4 0 7 0 11 2-7 2-13 3-20 5l-6 3v1h-1l2-5c4-3 8-5 14-6z" class="D"></path><defs><linearGradient id="s" x1="599.41" y1="659.448" x2="607.202" y2="666.189" xlink:href="#B"><stop offset="0" stop-color="#484848"></stop><stop offset="1" stop-color="#70716f"></stop></linearGradient></defs><path fill="url(#s)" d="M600 657c4 3 7 6 11 7h1l-1 1v3c0 2-1 4 0 6h0 0c-2-3-5-5-8-7l-7-7c1-1 1-2 2-2s2-1 2-1z"></path><path d="M310 326h1c1 1 0 1 2 2v-1l1 1 1 1h0c1 0 2 1 2 1 0 1-1 1-2 1l1 1h1c-2 2-7 4-10 4h-2v-1h-1c-1-1-1-1-2-1-1 1-3 3-4 5l-2 1c1-3 5-8 9-9 1-1 1-2 3-3l1-1h-1c0-1 1-1 2-1z" class="B"></path><path d="M310 326h1c1 1 0 1 2 2v1c-2 1-3 1-5 1-1 1-2 1-3 1 1-1 1-2 3-3l1-1h-1c0-1 1-1 2-1z" class="D"></path><defs><linearGradient id="t" x1="571.973" y1="207.379" x2="578.762" y2="216.301" xlink:href="#B"><stop offset="0" stop-color="#a8a7a6"></stop><stop offset="1" stop-color="#cfceca"></stop></linearGradient></defs><path fill="url(#t)" d="M571 213c-1-1-1-3-2-5l12 1v8 1c-2 1-4 1-6 1-2-2-3-3-4-6z"></path><path d="M387 511c3 7 5 14 7 21 2 6 4 11 5 16 1 1 1 3 1 5l-1 1c-1-6-3-11-6-17 0 3 0 5-2 7v-4c1-5 1-10-1-14-1-3-3-5-3-8v-1-6z" class="D"></path><path d="M578 334h-1v-1-1c3-5 27-15 33-17l-2 5c-2 0-4 1-6 2-8 3-18 7-24 12z" class="U"></path><path d="M739 172c4 0 10-1 13 1v3c-6 1-13 0-18 0h-16c7-2 14-3 21-4z" class="L"></path><path d="M455 247c1-1 1-3 2-4 4 6 5 16 4 23l-3 6c-1-3-1-6-1-9-1-6-2-10-2-16z" class="B"></path><path d="M337 375c1 1 3 2 4 3h1l3 7c0 1 1 3 1 4-3 0-14-4-16-6v-1-1c1 0 2 0 4-1h4c0-2 0-3-1-5z" class="W"></path><path d="M337 375c1 1 3 2 4 3h1l3 7c-1-1-2-2-3-2-4-2-9-2-12-1v-1c1 0 2 0 4-1h4c0-2 0-3-1-5z" class="D"></path><path d="M337 375c1 1 3 2 4 3 0 1-1 1-1 2h0-6 4c0-2 0-3-1-5z" class="K"></path><path d="M478 789c1 2 1 3 2 4 1 4 3 8 3 11s-4 6-6 9c0-3-1-6-1-9 0-2 0-5-1-7l-1 2h-1c1-1 1-2 1-3l2-3h0l1-1 1-3z" class="P"></path><path d="M478 789c1 2 1 3 2 4-1 2-1 4-1 6h0c0 2 0 5-1 6v-12l-1-1 1-3z" class="B"></path><path d="M479 799h0c0-2 0-4 1-6 1 4 3 8 3 11l-2 1c-1-2-1-4-2-5v-1z" class="D"></path><defs><linearGradient id="u" x1="284.139" y1="228.921" x2="272.434" y2="250.657" xlink:href="#B"><stop offset="0" stop-color="#0f0f0f"></stop><stop offset="1" stop-color="#383938"></stop></linearGradient></defs><path fill="url(#u)" d="M278 237v-1l1-3s0-1 1-1v-1c1-1 1-2 1-4h1c2 3 0 10-1 13v1c-1 1-1 3-2 5 0 1-1 3-1 5l-2 2s-1 1-2 1c-1-2-1-2 0-4 0-2-1-4 0-6h0 1v-2c2-2 2-3 3-5z"></path><defs><linearGradient id="v" x1="589.031" y1="385.473" x2="579.974" y2="385.521" xlink:href="#B"><stop offset="0" stop-color="#4a4b4a"></stop><stop offset="1" stop-color="#757473"></stop></linearGradient></defs><path fill="url(#v)" d="M584 380l7-2c-1 4-2 9-4 12l-6 3c-1-2-1-4-1-6v-4l1-1-2-1h0l5-1z"></path><defs><linearGradient id="w" x1="407.691" y1="562.056" x2="400.309" y2="575.944" xlink:href="#B"><stop offset="0" stop-color="#1f201f"></stop><stop offset="1" stop-color="#40403f"></stop></linearGradient></defs><path fill="url(#w)" d="M401 576c0-1-1-1-1-2v-2-1-1-1c-2-4 0-9 1-13l7 19v4l-1 1c0-1-1-2-2-2-1-1-2-2-4-2z"></path><defs><linearGradient id="x" x1="745.254" y1="233.116" x2="744.21" y2="248.881" xlink:href="#B"><stop offset="0" stop-color="#525251"></stop><stop offset="1" stop-color="#8c8c8a"></stop></linearGradient></defs><path fill="url(#x)" d="M744 233h2c-1 4-2 10 0 13 3 4 7 7 10 11l-3-1c-1 0-3-1-4-2-2 0-3-1-4 0v2h0l-2-2c0-2-1-4-1-6v-6c-1-3 1-6 2-9z"></path><path d="M743 254c1-1 1-2 1-3 2 0 3 1 5 2v1h0c-2 0-3-1-4 0v2h0l-2-2z" class="E"></path><defs><linearGradient id="y" x1="593.229" y1="668.137" x2="608.323" y2="672.88" xlink:href="#B"><stop offset="0" stop-color="#3f3e3e"></stop><stop offset="1" stop-color="#848582"></stop></linearGradient></defs><path fill="url(#y)" d="M600 674c-2-1-5-4-5-6-1-2-1-6-1-8a79.93 79.93 0 0 1 13 13c0 1 0 2 1 3l-2 2c0 1 0 2-1 2-1-1-4-4-5-6z"></path><path d="M287 289c3 0 6 1 8 0s3-1 5-1l1 1c2 3 7 5 10 6 1 1 1 2 2 2-1 1 0 1-1 1s-2 1-3 1l-4-1c-1-1-1-1-2-1h-1l-2-1c-2-1-3-1-5-3-1 0-2-1-3-2h0-3c-2 1-3 1-4 1v-2l1 1 1-2z" class="D"></path><path d="M580 277v-1c3-3 6-5 10-6 1 1 1 2 1 3-1 3-2 5-3 7v1c0 1 0 2 1 3h3l-1 4h-2c-3-1-7-8-9-11z" class="P"></path><defs><linearGradient id="z" x1="510.14" y1="584.767" x2="507.576" y2="552.454" xlink:href="#B"><stop offset="0" stop-color="#1e1f1f"></stop><stop offset="1" stop-color="#424242"></stop></linearGradient></defs><path fill="url(#z)" d="M505 554h1c0-1 1-1 1-2v-1h0c0 1 1 3 1 4v2 1c0 1 0 1 1 2v2c0 1 0 1 1 2v1l1 1v2h1 1v2l3 23-11-35v-2l-1-3 1 1z"></path><path d="M493 843c2 1 4 5 5 7 0 1 2 4 1 5-1 2-5 6-7 8-2-1-4-3-5-4l1-1c2-5 5-10 5-15z" class="I"></path><defs><linearGradient id="AA" x1="322.88" y1="323.092" x2="309.236" y2="324.257" xlink:href="#B"><stop offset="0" stop-color="#151515"></stop><stop offset="1" stop-color="#2f2f2f"></stop></linearGradient></defs><path fill="url(#AA)" d="M305 319h1l1 1c3 1 14 0 16 3 1 1 1 2 1 3-1 1-5 1-7 1l-3 1-1-1v1c-2-1-1-1-2-2h-1-3l-4-1c0-3 0-3 2-6h0z"></path><path d="M305 319h1l1 1c1 2 2 3 4 5h-1c-1 0-2 0-3 1l-4-1c0-3 0-3 2-6h0z" class="P"></path><path d="M454 724v-3-7 5c0-1 1-2 1-3v2l1 1v2c1 1 1 1 1 2l4 16c2 4 4 9 4 13 0 2-1 4-2 5-1-2 0-5-1-8s-2-7-3-10l1-1-2-1c-1 1 0 1 0 2h-1c-1-5-3-10-3-15z" class="F"></path><path d="M721 168s1-1 2-1h6c3 1 7 0 10 1h7v-1l2 2c-4 1-9 1-13 1-4 1-9 1-13 2-5 1-10 4-15 4l-10-1c4 0 9 0 13-1v-1c2-2 7-3 10-4l1-1z" class="H"></path><defs><linearGradient id="AB" x1="594.355" y1="362.262" x2="577.142" y2="363.696" xlink:href="#B"><stop offset="0" stop-color="#3f4040"></stop><stop offset="1" stop-color="#757573"></stop></linearGradient></defs><path fill="url(#AB)" d="M578 364c7-3 13-7 20-9l-1 1c0 2-1 6-2 7h-1c-6 2-11 4-17 7 1-2 1-4 1-6z"></path><path d="M594 363c-1-1 0-1-1-1s-1 0-1-1c1 0 2-1 3-2v-2c1-1 1 0 2-1 0 2-1 6-2 7h-1z" class="P"></path><defs><linearGradient id="AC" x1="335.884" y1="373.749" x2="325.593" y2="376.812" xlink:href="#B"><stop offset="0" stop-color="#5d5d5c"></stop><stop offset="1" stop-color="#91908d"></stop></linearGradient></defs><path fill="url(#AC)" d="M322 371h1c5 0 10 1 14 4 1 2 1 3 1 5h-4c-2 1-3 1-4 1-2 1-4 2-5 3l1-2-2-3c-1-2-3-5-2-8z"></path><path d="M322 371h1v1c1 0 1 0 2 1v1c0 1 1 3 1 5h0l-1-1-1 1h0c-1-2-3-5-2-8z" class="J"></path><defs><linearGradient id="AD" x1="521.584" y1="393.805" x2="526.661" y2="367.416" xlink:href="#B"><stop offset="0" stop-color="#727272"></stop><stop offset="1" stop-color="#8a8a86"></stop></linearGradient></defs><path fill="url(#AD)" d="M523 365h1c2 2 4 3 7 4-5 10-9 20-11 31l-2-4c2-3 3-10 3-13 1-6 2-12 2-18z"></path><path d="M220 192c2 4 0 11-1 16s-3 10-8 13l-1 1-6 2h-1l1-1s1-1 1-2h0l1-1c6-3 8-11 10-17l4-11z" class="M"></path><path d="M126 264h0c-3-4-8-4-11-8 0-2 0-2 1-2h1v1h1c3 3 7 5 11 7h1c2-1 4-3 7-2 2 1 4 3 5 5l1 2 1 2h1v1h-2c0-1-1-2-1-3h0c0 1-1 2 0 3v3 12s0 1 1 2c0 1-1 4 0 5 0 1 0 2 1 3v3h0c0 2 1 3 2 5 0 1-1 0 0 1 0 1 1 1 1 2l1 3c1 0 1 1 1 1l1 2c-9-10-9-26-9-39 0-3 0-6-2-8-3-4-9-1-13-1z" class="R"></path><path d="M548 504l6 2c-1 8-4 16-7 24 0 1 0 2-1 2-1-4-1-9-1-13v-4s1-1 1-2c2-3 2-5 2-9z" class="O"></path><path d="M467 233c5 3 10 6 13 10-1 0-3 0-4-1-1 0-3 0-4-1l-1 1 1 1c1 1 2 3 2 4-1-1-3-3-4-5-3 1-6 1-9 2l-1-2-5-7c2 0 5 1 8 2l1-1c1 1 1 2 3 2l-1-2s1 0 1-1v-1-1z" class="U"></path><path d="M469 854c2 1 14 10 14 11s0 3 1 4v3h-1l-14-9h-1v-1-6l1-2z" class="J"></path><defs><linearGradient id="AE" x1="406.002" y1="618.571" x2="390.519" y2="633.055" xlink:href="#B"><stop offset="0" stop-color="#5f5f5e"></stop><stop offset="1" stop-color="#a5a5a0"></stop></linearGradient></defs><path fill="url(#AE)" d="M381 638c4-4 9-7 13-10 4-4 7-8 10-12 2 2 2 3 2 6h-1 1c-2 3-5 6-7 8-4 3-9 6-12 9 0-2 0-2 1-4-1 1-3 1-4 2-1 0-2 0-3 1h0z"></path><defs><linearGradient id="AF" x1="750.205" y1="220.576" x2="752.209" y2="228.568" xlink:href="#B"><stop offset="0" stop-color="#1d1d1d"></stop><stop offset="1" stop-color="#40403f"></stop></linearGradient></defs><path fill="url(#AF)" d="M744 223v-1c1-1 1-1 3-2 1 0 3-1 5-1h4c1 1 2 1 3 2-5 3-9 5-12 11l-1 1h-2-1v-2l-1 1-1-1c0-1 0-3-1-4l4-4z"></path><path d="M744 231l2-1c0 1 0 1 1 2h0l-1 1h-2-1v-2h1z" class="H"></path><path d="M744 223l1 1-2 2h1c1 1 1 1 2 0v1l-2 4h-1l-1 1-1-1c0-1 0-3-1-4l4-4z" class="D"></path><path d="M341 412h9c0 3 0 7 1 10h1v2c-2 1-5 1-8 0h-4c1 0 2 0 4-1 0-2-2-2-3-4 0-1 0-3-1-4-2-1-5 0-7 0-1 0-2-1-3-2 4 0 8-1 11-1z" class="K"></path><path d="M343 414c1 0 2 0 3 1-1 1-1 2-2 3 1 1 3 3 5 4h2 1v2c-2 1-5 1-8 0 1 0 2 0 3-1-1-2-3-2-4-4v-5z" class="Y"></path><path d="M330 413c4 0 8-1 11-1h0c-1 1-1 1-2 1s-1 0-2 1c1 1 4 0 6 0v5c1 2 3 2 4 4-1 1-2 1-3 1h-4c1 0 2 0 4-1 0-2-2-2-3-4 0-1 0-3-1-4-2-1-5 0-7 0-1 0-2-1-3-2z" class="Q"></path><defs><linearGradient id="AG" x1="450.775" y1="405.501" x2="460.929" y2="406.634" xlink:href="#B"><stop offset="0" stop-color="#686867"></stop><stop offset="1" stop-color="#91908d"></stop></linearGradient></defs><path fill="url(#AG)" d="M455 410l-5-13c4 2 8 5 12 9l4 3h-2c0 4 0 7 1 11v1l-3-3-7-8z"></path><path d="M462 406l4 3h-2c0 4 0 7 1 11v1l-3-3c0-2 0-3-1-4s0-4 0-5l1 1h0v-4z" class="J"></path><defs><linearGradient id="AH" x1="681.994" y1="340.321" x2="701.186" y2="347.711" xlink:href="#B"><stop offset="0" stop-color="#615f60"></stop><stop offset="1" stop-color="#adada7"></stop></linearGradient></defs><path fill="url(#AH)" d="M697 339c3 1 6 3 9 5-1 1-1 1-3 1-2-1-6-2-8-2-1 0-2 1-3 2-3 0-5 0-8 1-2 0-3 1-6 1l-1 2h-2l2-7 4-1c2-1 4-1 6-1 4 0 6 0 10-1z"></path><path d="M677 342l4-1s-1 1-1 2v1c-1 1-2 2-2 3l-1 2h-2l2-7z" class="H"></path><path d="M473 799h1l1-2c1 2 1 5 1 7 0 3 1 6 1 9l-7 7-2 1-2-2c0-2 1-3 0-5 2-5 5-9 7-15h0z" class="K"></path><path d="M448 217c-1-2 0-6 0-9l12-1c0 2-1 4-1 5-1 4-3 8-6 10h-1c-2 1-3 1-5 1v-3c0-2 0-2 1-3z" class="I"></path><path d="M448 217h2c1 0 2 0 3-1s3-3 6-4c-1 4-3 8-6 10h-1c-2 1-3 1-5 1v-3c0-2 0-2 1-3z" class="C"></path><path d="M438 273c5 0 7 4 11 5 1 0 2 1 2 2-1 2-3 4-4 6-2 1-3 3-5 5l-1 2h0c0-3-3-7-4-10 0-1-1-2-2-4h0l2 1 1-1c2 1 4 3 6 5-2-3-6-6-6-10v-1z" class="H"></path><defs><linearGradient id="AI" x1="600.618" y1="640.316" x2="606.369" y2="641.264" xlink:href="#B"><stop offset="0" stop-color="#777575"></stop><stop offset="1" stop-color="#92948d"></stop></linearGradient></defs><path fill="url(#AI)" d="M599 639h1v-1l-1-2 1-1c1 1 1 2 3 3s6 6 8 8c-1 0-2 0-4-1 1 3 4 4 5 6h-1v1c-1 1-1 1-1 2h0v2l3 4 2 2h-1c-1-1-2-1-3-1s-3-2-3-2l-3-3c0-2-1-3-2-5s-1-4-2-6c0-1-1-4-1-5h-1v-1z"></path><path d="M601 645h1v-2h0l1-1h0c0 2 1 4 2 5s1 2 1 2c0 2 1 3 2 4s1 2 2 3l3 4 2 2h-1c-1-1-2-1-3-1s-3-2-3-2l-3-3c0-2-1-3-2-5s-1-4-2-6z" class="L"></path><path d="M601 645h1v-2h0l1-1h0c0 2 1 4 2 5l-1 2c1 2 2 4 3 7h0v1c1 0 1 1 1 2l-3-3c0-2-1-3-2-5s-1-4-2-6z" class="G"></path><path d="M391 544c2-2 2-4 2-7 3 6 5 11 6 17 0 1-1 3-2 3v4s-1 1-2 1c-2 1-3 2-5 1l1-1v-1c1-2 1-2 1-4-1-1-1-2 0-4h0c-1-1-2-1-2-2h-1l-1 1c-2 2-5 3-8 4l6-4c2-1 2-2 3-4l1-1c0-1 1-2 1-3z" class="P"></path><path d="M391 562c1 0 1 0 2-1h0c2-1 3-2 4-4v4s-1 1-2 1c-2 1-3 2-5 1l1-1z" class="D"></path><defs><linearGradient id="AJ" x1="487.348" y1="814.74" x2="480.143" y2="818.606" xlink:href="#B"><stop offset="0" stop-color="#181818"></stop><stop offset="1" stop-color="#383737"></stop></linearGradient></defs><path fill="url(#AJ)" d="M477 815c2-4 5-6 8-9 1 6 3 12 3 18-1 0-1-1-2-1l-1 2-1-1c0 3 0 4-1 6v3l-1 2-1 1-1-3h0l1 1v-1c0-3-1-6-1-10l-1-4c0-2-1-3-2-4z"></path><path d="M480 823v-1-3h1c1 3 1 5 2 8v3 3l-1 2-1 1-1-3h0l1 1v-1c0-3-1-6-1-10z" class="P"></path><defs><linearGradient id="AK" x1="528.211" y1="268.1" x2="526.412" y2="244.929" xlink:href="#B"><stop offset="0" stop-color="#979695"></stop><stop offset="1" stop-color="#d9d8d6"></stop></linearGradient></defs><path fill="url(#AK)" d="M529 243v3c1 2 0 3 1 4v1c1 4 2 9 2 13-1 1-1 3-1 4-1 1-1 1-2 1v-1c-2-4-5-9-5-13 0-1 1-3 1-4h-1-5l-1-2c0-1 0-2-1-3 1 1 1 0 1 1l1 1h1v-1c2-2 5-3 8-3h0l1-1z"></path><path d="M518 249h1c1 0 2-1 2-1 1-1 1-1 2-1l4 2-2 2h-1-5l-1-2z" class="C"></path><defs><linearGradient id="AL" x1="444.455" y1="386.169" x2="456.545" y2="387.331" xlink:href="#B"><stop offset="0" stop-color="#474746"></stop><stop offset="1" stop-color="#767574"></stop></linearGradient></defs><path fill="url(#AL)" d="M448 391l-5-12 7 2c2 1 3 2 5 3l2 1c0 1 3 2 3 3-1 2 1 7 1 9v1c1 1 1 2 1 4l-2-2c-1-2-2-3-4-5s-5-3-8-4z"></path><path d="M456 395l1-1c1 1 1 2 2 3 0 0 1 1 2 1 1 1 1 2 1 4l-2-2c-1-2-2-3-4-5z" class="Q"></path><path d="M457 385c0 1 3 2 3 3-1 2 1 7 1 9-2-1-2-3-2-5-1-2-2-4-3-5l1-2z" class="L"></path><defs><linearGradient id="AM" x1="378.196" y1="630.906" x2="367.993" y2="655.338" xlink:href="#B"><stop offset="0" stop-color="#bebdb8"></stop><stop offset="1" stop-color="#fbfaf7"></stop></linearGradient></defs><path fill="url(#AM)" d="M381 638h0c1-1 2-1 3-1 1-1 3-1 4-2-1 2-1 2-1 4-1 0-3 2-3 3-2 1-4 2-6 4-1 1-3 2-4 3-3 1-9 5-13 4-1-1-2-1-3-2l-1-1c9-2 15-8 24-12z"></path><defs><linearGradient id="AN" x1="289.324" y1="241.187" x2="282.854" y2="252.016" xlink:href="#B"><stop offset="0" stop-color="#181818"></stop><stop offset="1" stop-color="#3e3f3e"></stop></linearGradient></defs><path fill="url(#AN)" d="M281 241h1c1-3 2-5 5-7h1v4l-1 10 4-4c1 1 1 1 1 2 0 2-1 3-1 4 0 2-1 3-2 4l-5 3v-5c-1 0-1-1-2-1h-2c-1 1-2 1-3 2h-1l2-2c0-2 1-4 1-5 1-2 1-4 2-5z"></path><path d="M281 241h1c1-3 2-5 5-7h1v4c-2 1-4 4-6 5l-4 8c0-2 1-4 1-5 1-2 1-4 2-5z" class="R"></path><path d="M468 190l2 2c0 1 1 1 1 2 0 2 1 4 1 6l-2 16c-1 4-2 7-1 11h-1c-1-1-1-1-1-2l-1-4c1-6 1-12 0-18h0l-2 4-1 1c0-6 1-10 4-16l1-2z" class="I"></path><path d="M467 192c1 1 2 3 2 4 0 4-1 7-1 11 0 3 0 7-1 11v1 1 5l-1-4c1-6 1-12 0-18h0l-2 4-1 1c0-6 1-10 4-16z" class="a"></path><defs><linearGradient id="AO" x1="419.09" y1="663.479" x2="405.723" y2="677.847" xlink:href="#B"><stop offset="0" stop-color="#525251"></stop><stop offset="1" stop-color="#81827f"></stop></linearGradient></defs><path fill="url(#AO)" d="M412 667l7-7c0 2 1 8-1 10-1 2-4 5-6 7-2 1-4 4-6 6 0-1 0-1 1-1v-1c-1-1-1-1-1-2h0l-1-1h-1c-1 2-2 3-3 4h-1-1l5-9c2-3 4-5 7-7l1 1z"></path><path d="M404 673c2-3 4-5 7-7l1 1c-4 5-9 9-12 15h-1l5-9z" class="F"></path><defs><linearGradient id="AP" x1="430.152" y1="638.436" x2="425.909" y2="659.367" xlink:href="#B"><stop offset="0" stop-color="#161616"></stop><stop offset="1" stop-color="#454545"></stop></linearGradient></defs><path fill="url(#AP)" d="M422 653c-1-3 0-8 1-10 2-2 4-4 5-8h0 1l1 5c-1 4 0 7 1 11v3c0 2 1 5 1 7-2-1-3-2-5-2s-3 0-5-1v-5z"></path><path d="M482 835v1l2-1c1 1 2 3 2 5 1 3 0 7-1 11v3 3 1h-1c-2-1-4-3-6-4l-5-5c2-2 3-3 4-5h0l2-2 2-6 1-1z" class="C"></path><path d="M481 839l1-1c3 3 2 12 3 16v3c-1 0-1 0-1-1-3-2-3-13-3-17z" class="K"></path><path d="M482 835v1l2-1c1 1 2 3 2 5 1 3 0 7-1 11v3c-1-4 0-13-3-16l-1 1-2 3 2-6 1-1z" class="O"></path><defs><linearGradient id="AQ" x1="426.723" y1="631.142" x2="415.398" y2="637.22" xlink:href="#B"><stop offset="0" stop-color="#212120"></stop><stop offset="1" stop-color="#595959"></stop></linearGradient></defs><path fill="url(#AQ)" d="M424 623l3 10c-3 5-6 10-10 14v-1c1-2 1-3 0-4 0-2-1-3-2-4-2 0-2 1-4 2 0 1-1 1-2 1l3-4 5-6 2-3s2-3 2-4h2c1 1 0 2 2 3v-1c0-1-1-2-1-3h0z"></path><defs><linearGradient id="AR" x1="417.074" y1="645.214" x2="402.453" y2="646.731" xlink:href="#B"><stop offset="0" stop-color="#656664"></stop><stop offset="1" stop-color="#979795"></stop></linearGradient></defs><path fill="url(#AR)" d="M409 641c1 0 2 0 2-1 2-1 2-2 4-2 1 1 2 2 2 4 1 1 1 2 0 4v1h0l-5 5h-1l-2 2v-1l-5 4c0-1 0-2-1-2-1-2-1-2-1-4 0-1-1-1-2-2h0c1-1 2-2 3-2 2-2 4-5 6-6z"></path><path d="M672 360c0-1 0-2 1-2 1-2 3-2 5-2-1 3-1 6-1 9v1l2 1h0-1c0 1 0 1-1 2h5 1l-2 1h1c1 0 1 0 2 1-7 2-12 6-18 10l6-21z" class="O"></path><path d="M672 360c0-1 0-2 1-2 1-2 3-2 5-2-1 3-1 6-1 9v1l2 1h0c-3 0-5 1-7 1v-3-5z" class="U"></path><defs><linearGradient id="AS" x1="580.393" y1="404.644" x2="562.894" y2="409.629" xlink:href="#B"><stop offset="0" stop-color="#7d7d7c"></stop><stop offset="1" stop-color="#b7b6b0"></stop></linearGradient></defs><path fill="url(#AS)" d="M559 413c5-6 12-12 19-16 1 1 2 2 1 4l-1 1c0 2 0 3 1 5v2l-13 10v-1s0-1 1-1l1-1-5-5c-1 1-2 1-3 2h-1z"></path><path d="M552 238c2-3 6-6 10-7h1v2c1 0 1 0 2-1 1 1 1 2 2 3 1 0 1-1 2-1 2 0 5-2 7-3l-10 17c-2-2-6-4-8-7h0c-1 1-2 2-2 3v-1c0-2 2-5 3-7h1-2-1l-2 1c-1 1-2 1-3 1z" class="L"></path><defs><linearGradient id="AT" x1="806.046" y1="231.789" x2="806.557" y2="246.754" xlink:href="#B"><stop offset="0" stop-color="#7e7d7c"></stop><stop offset="1" stop-color="#9e9f9b"></stop></linearGradient></defs><path fill="url(#AT)" d="M810 220h3c1 3 3 6 3 9v1c1 5 1 14-2 19-4-5-11-2-16-6-1-2-2-4-2-6h1c3 1 7 3 10 2 2 0 4 0 5-2 2-2 2-6 2-9-1-4-1-5-4-8z"></path><path d="M449 746c0-4 1-8 1-11l2 8v3l1 1c0 7-1 16 1 23l1 1c-1 1-1 2-2 4 0-1 0-2-1-2h-2l-5 1-1-1v-1c3-9 4-17 5-26z" class="e"></path><path d="M451 753h1v13 7h-2l1-20z" class="g"></path><path d="M444 773h4c4-5 0-14 3-20l-1 20-5 1-1-1z" class="P"></path><path d="M463 208l1-1 2-4h0c1 6 1 12 0 18-1 2-2 6-4 7-3 0-5-1-7-3v-1l-2-2c3-2 5-6 6-10 0-1 1-3 1-5 1 1 2 1 3 1z" class="c"></path><path d="M460 207c1 1 2 1 3 1-1 6-5 11-8 16l-2-2c3-2 5-6 6-10 0-1 1-3 1-5z" class="F"></path><defs><linearGradient id="AU" x1="602.668" y1="701.902" x2="606.991" y2="704.865" xlink:href="#B"><stop offset="0" stop-color="#807f7d"></stop><stop offset="1" stop-color="#999995"></stop></linearGradient></defs><path fill="url(#AU)" d="M600 731l-1-27c-1-4-1-10 1-14h1c1 0 2 3 3 4l4 8c-2 1-2 1-3 3l2 2-1 2-1 1c-1-1-1-2-1-3h-1v2c0 1 0 1-1 3 0 4 1 9 0 14h0l-1 2c0 1-1 2-1 3z"></path><defs><linearGradient id="AV" x1="687.084" y1="373.353" x2="699.598" y2="380.565" xlink:href="#B"><stop offset="0" stop-color="#a7a5a2"></stop><stop offset="1" stop-color="#cfcfc9"></stop></linearGradient></defs><path fill="url(#AV)" d="M685 373c8-2 15-1 23 2h-1-2l-2-1c-1 3-2 5-4 8l-4 4c1 2 3 2 5 4h0v1l-1 1c-3-4-8-8-13-10 0-1-1-2-1-3 2-2 4-2 6-2-2-1-4-3-6-4z"></path><path d="M574 242l1 2 1 5h0c0 2 1 4 2 5 1 5-2 12-3 17v3c-1-1-1-2-2-3 0 0-1-1-1-2-2-1-3-3-3-5 0-1-1-2-1-4s1-4 1-6c2-4 3-8 5-12z" class="O"></path><path d="M570 266h1c1-4 2-6 2-10 0-1 0-1 1-1v6c0 1-1 3-1 3 0 1 1 1 1 2l-1 3-3-3h0z" class="B"></path><path d="M574 266c1-3 2-6 2-9 1 1 1 1 1 2-1 3-3 10-2 12v3c-1-1-1-2-2-3 0 0-1-1-1-2-2-1-3-3-3-5l1 2h0l3 3 1-3z" class="M"></path><defs><linearGradient id="AW" x1="531.058" y1="823.867" x2="535.442" y2="846.133" xlink:href="#B"><stop offset="0" stop-color="#393837"></stop><stop offset="1" stop-color="#6e6e6d"></stop></linearGradient></defs><path fill="url(#AW)" d="M533 827l1 1c2-2 2-5 4-6h1v-1h1c-1 3-2 4-1 7 1 2 1 3 1 5h0c0 4-2 7-4 10h0v1l-7 7c-2-1-2-1-2-3l6-21z"></path><path d="M539 828c1 2 1 3 1 5h0l-1 2s-1 0-1-1h-1c-1 1 0 1-1 1v1l-1-1 1-1h0c1-1 1-2 1-4l2-2z" class="H"></path><path d="M539 835l1-2c0 4-2 7-4 10h0l-3 3h-1c0-4 2-7 4-10 1 0 2 0 3-1h0z" class="U"></path><defs><linearGradient id="AX" x1="577.635" y1="391.888" x2="565.493" y2="392.11" xlink:href="#B"><stop offset="0" stop-color="#868582"></stop><stop offset="1" stop-color="#b6b5b1"></stop></linearGradient></defs><path fill="url(#AX)" d="M558 398c5-6 14-14 21-17l2 1-1 1v4c0 2 0 4 1 6l-3 1c-4 3-9 6-13 9 0 1-4 5-4 5l-1-1 7-7-1-3v-5c-3 2-5 4-7 6h-1z"></path><path d="M578 394c0-3 0-8 1-10l1 3c0 2 0 4 1 6l-3 1z" class="G"></path><defs><linearGradient id="AY" x1="722.005" y1="239.97" x2="729.715" y2="247.814" xlink:href="#B"><stop offset="0" stop-color="#2c2c2c"></stop><stop offset="1" stop-color="#5a5b59"></stop></linearGradient></defs><path fill="url(#AY)" d="M725 233c-1-2-2-5-1-6l2 1v2l1-2c0 4 1 8 1 13h1v2c1 2 2 5 1 7v3c-1 0-2-2-3-3l-1-1-1-1-1 1v1c1 2 2 3 1 5-2-3-4-8-5-12l-1-11-1-1 1-1c1 1 1 3 2 5 0-1 0-2-1-2l1-1v-3h1c0 1 1 2 1 4h2z"></path><path d="M723 233h2v5c-2-1-2-3-2-5z" class="F"></path><path d="M719 232v3l1 1 1 1s1 1 2 1c0 1 0 2-1 3h-1l-1 2-1-11z" class="B"></path><defs><linearGradient id="AZ" x1="710.262" y1="244.738" x2="715.705" y2="253.868" xlink:href="#B"><stop offset="0" stop-color="#1c1c1c"></stop><stop offset="1" stop-color="#4e4e4d"></stop></linearGradient></defs><path fill="url(#AZ)" d="M706 249l1-2 1-1c0 1 1 1 2 2v-2l4-9v1l1 3c1 2 2 3 2 4l1 1 1-1c0 1 0 2 1 3 0 1 1 2 2 4 1 1 2 3 3 4h0c1 1 3 4 4 5l1 1v1l-1 1c-1 0-2-1-3-2-2-2-3-3-5-4l-1 1c-1 0-2 0-4-1v-1l-6-6c-1-1-1-2-3-2h-1z"></path><path d="M730 262c-2 0-3-1-4-2-1-2-3-4-5-6s-3-6-6-7c-1-1-2-1-2-2v-1l-1-1 1-3h1l1 1c1 2 2 3 2 4l1 1 1-1c0 1 0 2 1 3 0 1 1 2 2 4 1 1 2 3 3 4h0c1 1 3 4 4 5l1 1z" class="F"></path><defs><linearGradient id="Aa" x1="521.263" y1="379.208" x2="512.833" y2="377.313" xlink:href="#B"><stop offset="0" stop-color="#a8a7a2"></stop><stop offset="1" stop-color="#c6c6c2"></stop></linearGradient></defs><path fill="url(#Aa)" d="M515 356c3 2 6 6 8 9 0 6-1 12-2 18 0 3-1 10-3 13-2 5-1 11-3 15 0-5-2-9-2-14 0 1 2 2 2 3 1 2 0 3 1 5v-5-2c1-4 1-10 1-14v-16c-1-3 0-5-2-7h1v-3l-1-1v-1z"></path><path d="M229 217l1-1c3-4 6-6 11-6h4c3 0 8-1 10 1h-5c-1-1-2-1-2-1-1 1-2 1-2 1l-1 1h0l-1 1c-1 1-2 1-3 2-1 2-2 4-3 5-1 3-1 4-1 7 1 3 4 6 7 7h0c0 1 0 1-1 1l-1-1c-2 0-2 0-3-1s-1 0-2-1-2-1-2-3c-1 0-1 0-2-1 0 1 0 1-1 2v-1-5c0-1 1-2 1-4l-1 1-2 5c-1-1 0-6 1-8-2 1-3 3-4 4l-3 1c1-2 3-4 5-6z" class="F"></path><path d="M235 229c0-1 0-1 1-2 2 3 3 5 6 7-2 0-2 0-3-1s-1 0-2-1-2-1-2-3z" class="K"></path><path d="M232 224l1 1c0-1 1-2 2-3 1-2 1-2 3-2-1 2-1 3-1 5-1 1-1 1-1 2-1 1-1 1-1 2-1 0-1 0-2-1 0 1 0 1-1 2v-1-5z" class="H"></path><path d="M229 217h0c2 0 4-1 5-1l1-1h0c0 1-1 2 0 3 2-2 3-4 5-5 1 2-1 5-2 7-2 0-2 0-3 2-1 1-2 2-2 3l-1-1c0-1 1-2 1-4l-1 1-2 5c-1-1 0-6 1-8-2 1-3 3-4 4l-3 1c1-2 3-4 5-6z" class="O"></path><path d="M539 807c1-1 1-2 2-2h0v1h0 0 2v3l1-2c1 1 1 1 2 1-1 3-1 5-2 8l1 1 1 2v2h0v2 3h-1c-1 0-1 0-1 1 1 2 2 4 3 7l3 4v1c-1-1-2-3-4-4v1 4l-1 1v-1l-1-4c-1 0 0 0-1 1 0 1-1 2-2 3l-1 1v2 2c-2 0-2-1-4-2 2-3 4-6 4-10h0c0-2 0-3-1-5-1-3 0-4 1-7 1-2 2-9 1-12 0-1-1-2-2-2z" class="N"></path><path d="M543 809l1-2c1 1 1 1 2 1-1 3-1 5-2 8l1 1 1 2-3-1v-9z" class="D"></path><path d="M543 818l3 1v2h0v2 3h-1c-1 0-1 0-1 1-1-3-1-6-1-9z" class="H"></path><defs><linearGradient id="Ab" x1="651.527" y1="429.457" x2="671.428" y2="439.51" xlink:href="#B"><stop offset="0" stop-color="#444"></stop><stop offset="1" stop-color="#70706f"></stop></linearGradient></defs><path fill="url(#Ab)" d="M670 425h10c-1 1-1 1-1 3h2c-7 0-14 0-20 1-2 0-4 1-5 2-1 2-1 2-1 4h1v1 6c-4 1-7 2-10 4h-1l6-16 1-4c2 1 5 0 7 1 1 0 1-1 2-1l-1-1h1 9z"></path><path d="M652 426c2 1 5 0 7 1-3 0-6 2-8 3l1-4z" class="P"></path><defs><linearGradient id="Ac" x1="407.791" y1="700.854" x2="407.334" y2="715.634" xlink:href="#B"><stop offset="0" stop-color="#848482"></stop><stop offset="1" stop-color="#bab9b5"></stop></linearGradient></defs><path fill="url(#Ac)" d="M405 702h1c3 0 5-1 8-1v4l-1 3v5l-3 1c0 2-1 10-2 12h-1c0-1 0-2 1-3l-1-1-1 1h-1v-1c-1-1-2-1-3-1-3 2-1 5-4 7 1-2 2-5 2-8v-1l1-2 4-15z"></path><defs><linearGradient id="Ad" x1="408.315" y1="706.989" x2="408.212" y2="718.511" xlink:href="#B"><stop offset="0" stop-color="#a4a49f"></stop><stop offset="1" stop-color="#cfcecb"></stop></linearGradient></defs><path fill="url(#Ad)" d="M401 717h4c5-1 2-8 5-12l1 1c0 2-1 5-1 8 0 2-1 10-2 12h-1c0-1 0-2 1-3l-1-1-1 1h-1v-1c-1-1-2-1-3-1-3 2-1 5-4 7 1-2 2-5 2-8v-1l1-2z"></path><path d="M400 719h5 3v1h-1-7v-1z" class="S"></path><defs><linearGradient id="Ae" x1="478.993" y1="766.227" x2="463.007" y2="785.273" xlink:href="#B"><stop offset="0" stop-color="#0c0b0b"></stop><stop offset="1" stop-color="#323232"></stop></linearGradient></defs><path fill="url(#Ae)" d="M465 764l4-10h0c0 5 9 35 9 35l-1 3-1 1h0l-2 3v-3-2-3l-1-1c-1-1-1-1-2-3v-1c-2-1-1 0-3 0-1 1-1 0-2 0l1-1c0-2 0-4 1-5 0-3 1-6-1-8v4l-2-1c0-1 1-3 0-4h0v-4z"></path><path d="M468 777h1c0-1 0-4 1-5v4h1 1v1c1 3 3 5 2 9l-1 1c-1-1-1-1-2-3v-1c-2-1-1 0-3 0-1 1-1 0-2 0l1-1c0-2 0-4 1-5z" class="D"></path><path d="M467 782c2-1 2-2 3-3h1c0 2 1 4 2 5v1l1 1-1 1c-1-1-1-1-2-3v-1c-2-1-1 0-3 0-1 1-1 0-2 0l1-1z" class="P"></path><defs><linearGradient id="Af" x1="541.348" y1="252.014" x2="522.152" y2="241.486" xlink:href="#B"><stop offset="0" stop-color="#a09f9c"></stop><stop offset="1" stop-color="#d4d3d1"></stop></linearGradient></defs><path fill="url(#Af)" d="M528 238v-1c1-2 1-6 1-8 1 1 1 2 2 2l1 1v1c0 1 1 3 1 5 1 1 2 2 2 3l3 3h0v1c1 3-2 20-3 24l-3-12c-1-2-1-4-2-6v-1c-1-1 0-2-1-4v-3l-1-5z"></path><defs><linearGradient id="Ag" x1="215.269" y1="235.41" x2="196.649" y2="238.374" xlink:href="#B"><stop offset="0" stop-color="#787878"></stop><stop offset="1" stop-color="#989895"></stop></linearGradient></defs><path fill="url(#Ag)" d="M199 253c-1-2-1-5-1-7-1-5-2-11-1-16 0-3 1-4 3-6 1 4 2 12 4 15 2 1 5 1 7 0 3 0 4-1 7-2v2c0 2 0 3-2 4-2 2-7 2-10 3-1 0-3 0-4 1s-2 4-3 6z"></path><path d="M295 293c2 2 3 2 5 3l2 1h1c1 0 1 0 2 1 0 1 1 1 2 1l5 2h4l2 7-7-2h-1 0c2 1 6 3 9 3 0 2 1 3 1 4l-14-3h-2l1-2h0v-3l-1-1c-2-1-8-2-10-1h-3c1-2 3-4 3-5 0-2 0-2-1-2l2-1v-2z" class="F"></path><path d="M310 306v-1c-2 0-2-1-4-1h0c0-1 1-1 2-1 2 0 3 1 5 2l-2 1h-1z" class="M"></path><path d="M295 293c2 2 3 2 5 3l2 1h1c1 0 1 0 2 1 0 1 1 1 2 1l5 2s1 0 0 1c-3 0-6-2-9-3h-6c-1-1-1-2-2-3v-1-2z" class="B"></path><path d="M580 415l-5 18c0-2 0-5-1-6s-2-1-2 0c-2 0-3 2-4 3-4 4-7 9-9 15v1l-1 1c-1-3-3-9-2-12s4-6 6-8c6-5 11-9 18-12z" class="W"></path><path d="M606 754l3 3c1 3 4 4 6 7a57.31 57.31 0 0 0 11 11l7 4h-1-2l-1 1h0c-1 1-1 1-1 2-1-1-2-1-2-1-1 2-2 3-2 5v1h-1c-2-2-4-6-6-9-3-5-7-11-10-16-1-3-1-5-1-8z" class="a"></path><defs><linearGradient id="Ah" x1="681.288" y1="350.981" x2="703.167" y2="346.03" xlink:href="#B"><stop offset="0" stop-color="#646464"></stop><stop offset="1" stop-color="#b5b4ac"></stop></linearGradient></defs><path fill="url(#Ah)" d="M678 347c3 0 4-1 6-1 3-1 5-1 8-1 1-1 2-2 3-2 2 0 6 1 8 2h-1c0 1 0 1 1 2h1c0 2-1 3-2 5h1c1 0 2 0 2 1 1 1 1 1 1 2-4-1-7-2-11-2h-11l-5 1c-2-1-3-1-5-1l1-4h2l1-2z"></path><path d="M685 349l1-1h1c1 0 2 0 3 1h0l-4 2c-2 0-3 0-5-1l4-1z" class="Q"></path><path d="M675 349h2c3 0 5-1 8 0l-4 1c2 1 3 1 5 1s7 0 8 1l1 1h-11l-5 1c-2-1-3-1-5-1l1-4z" class="W"></path><path d="M684 353h0v-1l1-1h-9c2-1 4-1 5-1 2 1 3 1 5 1s7 0 8 1l1 1h-11z" class="K"></path><defs><linearGradient id="Ai" x1="250.085" y1="223.662" x2="245.589" y2="233.459" xlink:href="#B"><stop offset="0" stop-color="#555553"></stop><stop offset="1" stop-color="#6f6f6d"></stop></linearGradient></defs><path fill="url(#Ai)" d="M238 220h4c1 0 1 1 2 2 1 0 1 0 2-1l1 1c1 0 1 0 1-1 2 2 3 2 5 3h1c2 2 5 5 5 9 0 1 0 2-1 3-4 0-7-1-10-2h-2-2 0c-3-1-6-4-7-7 0-3 0-4 1-7z"></path><path d="M528 238c-2-10-2-23 3-32h1v2 2h-1c0 4 0 7 2 10 1 3 2 7 4 9 3 5 7 10 3 15-1 1-1 0-2 0l-3-3c0-1-1-2-2-3 0-2-1-4-1-5v-1l-1-1c-1 0-1-1-2-2 0 2 0 6-1 8v1z" class="C"></path><path d="M598 728c1 5 1 10 2 14 2 8 5 16 8 23l10 18c2 3 5 6 4 9h-2c-2-2-4-5-5-7-9-12-13-24-17-38l-2-17c1 1 1 3 2 4v-2l-1-1c0-1 1-1 1-3z" class="I"></path><path d="M608 765l10 18v2 1l-2-2c-3-5-8-12-9-17 0-1 1-1 1-2z" class="E"></path><path d="M704 329l5 3c3 1 8 5 11 7l2 4c1 3 3 5 4 7l-1 1-1-1c0-1-1-1-1-1-2 1-6 1-7 0-2-1-2-2-3-3-2-1-4-3-5-4-2 0-2-1-3-2-2-1-5-3-6-4l1-1c1-1 1-1 2-1l-1-1c0-1 0-1 1-2h0l2-2z" class="B"></path><path d="M708 341c4 0 6 4 9 4v-1l-4-3 1-1 6 5 1 3h0l-5-1c-3-1-5-3-8-6z" class="O"></path><path d="M700 335c6 0 10 2 14 5h0l-1 1 4 3v1c-3 0-5-4-9-4l-3-1c-2-1-5-3-6-4l1-1z" class="D"></path><path d="M704 329l5 3c3 1 8 5 11 7l2 4v1c0 2 1 3 2 4h-2v-1c0-1-1-2-2-2h0l-6-5h0c-4-3-8-5-14-5 1-1 1-1 2-1l-1-1c0-1 0-1 1-2h0l2-2z" class="R"></path><defs><linearGradient id="Aj" x1="726.666" y1="252.409" x2="729.238" y2="257.095" xlink:href="#B"><stop offset="0" stop-color="#70706f"></stop><stop offset="1" stop-color="#868682"></stop></linearGradient></defs><path fill="url(#Aj)" d="M726 226v-2c0-1 2-3 3-4 1 1 1 2 1 3l3 2v2 1c1 5 1 11 2 17h0v1c2 3 3 5 5 8v1c-1-1-2-2-4-3l-1 1h0l-2-1c-1 2 0 3-1 4 0 2-2 3-3 5-1-1-3-4-4-5v-1c1-2 0-3-1-5v-1l1-1 1 1 1 1c1 1 2 3 3 3v-3c1-2 0-5-1-7v-2h-1c0-5-1-9-1-13l-1-2z"></path><path d="M726 226v-2c0-1 2-3 3-4 1 1 1 2 1 3-1 1-1 2-1 3 0 2 1 5 0 7l-1-5v-3l-1-1-1 2z" class="N"></path><defs><linearGradient id="Ak" x1="729.325" y1="234.657" x2="736.381" y2="243.089" xlink:href="#B"><stop offset="0" stop-color="#353434"></stop><stop offset="1" stop-color="#575857"></stop></linearGradient></defs><path fill="url(#Ak)" d="M729 226c0-1 0-2 1-3l3 2v2 1c1 5 1 11 2 17h0v1c0 1-1 1-1 3v3h-1c-1-2-1-4-2-6-1-5-2-8-2-13 1-2 0-5 0-7z"></path><path d="M729 226c0-1 0-2 1-3l3 2v2c-1 1-1 3-1 5h-1 0c0-3 0-4-2-6z" class="F"></path><path d="M729 226c2 2 2 3 2 6h-1c0 4 0 8 2 13l-1 1c-1-5-2-8-2-13 1-2 0-5 0-7z" class="B"></path><path d="M455 410l7 8 3 3 6 7h-1c1 2 2 4 2 5 3 6 0 13-2 18l-13-32h0c-1-3-1-6-2-9z" class="W"></path><path d="M455 410l7 8 3 3 6 7h-1c-1-1-4-6-6-6 0 0-2 0-2-1-1 0-3-2-4-3l-1 1h0c-1-3-1-6-2-9z" class="F"></path><defs><linearGradient id="Al" x1="322.29" y1="373.758" x2="309.185" y2="381.982" xlink:href="#B"><stop offset="0" stop-color="#9d9d98"></stop><stop offset="1" stop-color="#d0cfc9"></stop></linearGradient></defs><path fill="url(#Al)" d="M301 374c7-2 14-3 21-3-1 3 1 6 2 8l2 3-1 2c-4 2-6 4-9 7h-1l-1-1h-2l-1-1v-1h3 0c0-1-1-2-1-3-2-2-5-4-6-7-1-1-1-2-2-3l-4-1z"></path><defs><linearGradient id="Am" x1="795.141" y1="199.131" x2="814.897" y2="214.687" xlink:href="#B"><stop offset="0" stop-color="#494948"></stop><stop offset="1" stop-color="#898987"></stop></linearGradient></defs><path fill="url(#Am)" d="M795 186c3 1 4 3 7 4l1 1c3 4 6 7 8 11l1 1h-1l-1-2-1 2v2c1 1 1 3 1 4l3 11h-3c-1 0-2-1-2-1-4-4-6-12-9-17-1-3-5-8-4-11 0-1 0-1 1-1v-1h0c0-1-1-2-1-3z"></path><path d="M795 186c3 1 4 3 7 4-1 1-3 2-3 3v2l-1 1v-2c0-2 0-2-1-4l-1-1h0c0-1-1-2-1-3z" class="e"></path><path d="M803 191c3 4 6 7 8 11l1 1h-1l-1-2-1 2v2c1 1 1 3 1 4-1-1-1-2-2-3 0-2-2-5-3-7-1-1-1-3-1-4-1-1-1-2-1-4z" class="G"></path><path d="M467 824v1c0 1-1 1-2 2v1c-2 2-4 4-5 7h-1c-1 1-1 1-1 2 0 2-1 2 0 4l2 2 9 11-1 2v6 1c-3-1-6-5-9-7l-2 2-5-6-1 1-2-3c0-2 2-4 3-7l3-4c3-5 7-11 12-15z" class="Y"></path><path d="M454 844c1-1 2-2 4-3l2 2c-1 2 0 4 1 6l1 3v1l-1-1c0-2-2-4-3-5h0l-1-2-3-1z" class="O"></path><path d="M453 849h1l2 2c2 3 6 6 9 8l-1-6h1l2 1v2h1v6 1c-3-1-6-5-9-7-2-2-4-4-6-7z" class="L"></path><path d="M467 856h1v6h0c-1-2-1-4-1-6z" class="Q"></path><path d="M467 824v1c0 1-1 1-2 2v1c-2 2-4 4-5 7h-1c-1 1-1 1-1 2 0 2-1 2 0 4-2 1-3 2-4 3l3 1 1 2h0c-1 1-2 2-2 4l-2-2h-1c2 3 4 5 6 7l-2 2-5-6-1 1-2-3c0-2 2-4 3-7l3-4c3-5 7-11 12-15z" class="N"></path><path d="M454 844l3 1 1 2h0c-1 1-2 2-2 4l-2-2h-1v-1c0-2 0-2 1-4z" class="K"></path><path d="M381 499h0l2 2 4 10v6 1c0 3 2 5 3 8 2 4 2 9 1 14v4c0 1-1 2-1 3l-1 1c-1 2-1 3-3 4l-2-3c-2-1-2-1-4-3l4-7 2-3v-1h0c1-2 1-3 1-4v-1c-1-3-3-5-4-7 0-1 0-1 1 0h1 0c-1-2 0-2 0-3s-3-2-3-3v-1l-1-1c0-1 1-1 1-2v-2-1c-1-1-1-2-2-3 1 1 1 1 2 1l1-1h0c0-2 0-3-2-4l-2-2v-1l2-1z" class="O"></path><path d="M382 511v-1c-1-1-1-2-2-3 1 1 1 1 2 1l1-1h0c1 2 2 4 2 7h-1v1 2c-1 0-1-1-2-1l-1-1c0-1 1-1 1-2v-2z" class="H"></path><path d="M388 534l1-3h1v1c0 1 1 1 1 2 1 1 0 4 0 6v4c0 1-1 2-1 3l-1 1c-1 2-1 3-3 4l-2-3c-2-1-2-1-4-3l4-7 2-3 2-2z" class="Q"></path><path d="M388 534l1-3h1v1c0 1 1 1 1 2 1 1 0 4 0 6v4c0 1-1 2-1 3l-1 1-1-2h-1l-1-1c1-2 2-4 2-6h-3-1l2-3 2-2z" class="Y"></path><path d="M388 534l1-3h1v1c0 1 1 1 1 2 1 1 0 4 0 6v4c0 1-1 2-1 3 0-2-1-3-1-5s1-3 0-5c0-1 0-2-1-3z" class="K"></path><defs><linearGradient id="An" x1="605.753" y1="577.11" x2="620.533" y2="613.678" xlink:href="#B"><stop offset="0" stop-color="#363636"></stop><stop offset="1" stop-color="#878684"></stop></linearGradient></defs><path fill="url(#An)" d="M605 580c1-1 2-2 2-3 2-1 3-1 5-1-1 5-1 10 1 15v1c0 1 0 1 1 2v2c0 1 0 1 1 2 1 2 1 4 2 7l1 2c1 1 2 3 3 4 0 1 0 2 1 3h-2c0 2 1 3 1 5h-1-1l-1 1-2-3c-2-5-4-9-6-13-1-5-2-10-5-15h0v-2c-1-3 0-5 0-7z"></path><defs><linearGradient id="Ao" x1="628.45" y1="781.246" x2="655.928" y2="783.079" xlink:href="#B"><stop offset="0" stop-color="#b4b2b1"></stop><stop offset="1" stop-color="#e5e4e0"></stop></linearGradient></defs><path fill="url(#Ao)" d="M646 782h0c6-1 3-6 7-8 1 0 2 1 3 2s1 3 1 5c0 3-1 5-3 7-3 3-6 4-10 3-7 0-12-4-16-9 0-1 0-1 1-2h0l1-1h2 1l1 1c2 0 3 1 4 1l1 1h7z"></path><defs><linearGradient id="Ap" x1="264.804" y1="219.803" x2="266.041" y2="243.705" xlink:href="#B"><stop offset="0" stop-color="#0c0c0c"></stop><stop offset="1" stop-color="#494949"></stop></linearGradient></defs><path fill="url(#Ap)" d="M260 223l1-1v-2c2 1 3 2 5 3v1c0 2 2 9 3 11 0 1 1 2 1 4 0-1 0-1 1-1v-1h2l1 7c-1 2 0 4 0 6l-1-1h-2 0c0 3-1 5-3 7 0 1-1 1-1 2l-2-2 1-2c-1-1-1-1-2-1-1-1-2 0-4 0l-1 3h-2c2-6 8-10 7-17-1-4-2-12-5-15h0 1v-1z"></path><defs><linearGradient id="Aq" x1="269.674" y1="243.761" x2="264.798" y2="256.34" xlink:href="#B"><stop offset="0" stop-color="#4f4f4e"></stop><stop offset="1" stop-color="#81817e"></stop></linearGradient></defs><path fill="url(#Aq)" d="M270 239c0-1 0-1 1-1v-1h2l1 7c-1 2 0 4 0 6l-1-1h-2 0c0 3-1 5-3 7 0 1-1 1-1 2l-2-2 1-2c-1-1-1-1-2-1-1-1-2 0-4 0 2-2 4-4 5-7 0-1 1-2 2-3 1 0 2 1 3 1v-2-1c-1-1-1-1 0-2z"></path><path d="M270 239c0-1 0-1 1-1v-1h2l1 7c-1 2 0 4 0 6l-1-1c0-4-1-5-3-8-1-1-1-1 0-2z" class="W"></path><defs><linearGradient id="Ar" x1="502.513" y1="523.503" x2="507.468" y2="498.596" xlink:href="#B"><stop offset="0" stop-color="#7c7c79"></stop><stop offset="1" stop-color="#b0b0ac"></stop></linearGradient></defs><path fill="url(#Ar)" d="M499 487c1 0 0 0 1 1v10c1 2 4 3 5 4 1-2 3-2 4-3h1v-11c-1-3 0-4-1-6 0-1 1-2 1-3l1 41c0 1-1 2-1 3l-1 1h-1c-2 0-4 0-6-1 0-1 0-3-1-5h0-1v-4c0-8 0-17-1-25v-2z"></path><path d="M211 250l5-1 2 3h0v-2-1l2-2h2c-2 2-3 3-3 6 0 1 0 2 1 3 1 2 3 4 6 6v1l1 2-3-1-3-2c-3-1-6-4-8-5-1 1-3 3-4 3s-1 0-1 1c2 2 5 4 7 6l-8-6c-1-1 0-1-1-2l-2 2v1h-2-1c0 1-1 2-1 2v2c0 2-3 3-2 4l3-3 1 1h-1c0 1-1 2-1 3h0l1 2c-3 0-6-1-9-2-1-1-2-2-2-3l1-2c2-2 4-5 6-6 6-2 8-9 14-10z" class="O"></path><path d="M191 266l2 1 3-3v1h0c-1 1-1 2-2 4 0 1-1 1-2 2-1-1-2-2-2-3l1-2z" class="P"></path><path d="M208 261c-1-1-1-1-1-2 1-1 2-3 3-4 1 0 2 1 3 2-1 1-3 3-4 3s-1 0-1 1zm-14 8l4-4h0c-1 1-2 3-2 4 2-1 3-3 4-5v2c0 2-3 3-2 4l3-3 1 1h-1c0 1-1 2-1 3h0l1 2c-3 0-6-1-9-2 1-1 2-1 2-2z" class="H"></path><path d="M211 250l5-1 2 3h0v-2-1l2-2h2c-2 2-3 3-3 6 0 1 0 2 1 3 1 2 3 4 6 6v1l1 2-3-1-3-2h1c1 1 3 1 4 1-1-1-2-2-4-3-1-1-2-2-4-3h-1c0-1-1-2-2-3 0-2-1-2 0-4h-4z" class="B"></path><defs><linearGradient id="As" x1="604.376" y1="315.327" x2="581.525" y2="307.98" xlink:href="#B"><stop offset="0" stop-color="#6c6c6e"></stop><stop offset="1" stop-color="#9d9c96"></stop></linearGradient></defs><path fill="url(#As)" d="M591 308v1c2-2 5-3 7-4 2 0 3-1 5-2l2-1h1c1 1 2 1 3 1l-5 6s0 1-1 1c-1 1-2 1-3 1l-5 3-8 4v1l11-5c3-1 5-3 8-3l1-1c1 0 2-1 3-1h1v1l-6 3-17 6-8 4c-1 0-3 2-5 1l-2-14 1-1 1 1v4 1h0v2 1c1 0 1 1 1 2 2-1 3-5 4-6l9-6h0 2z"></path><path d="M595 314c1-2 2-3 4-4l1 1-5 3z" class="Q"></path><path d="M146 283c0-2 0-5 2-6 0-1 1-1 2-1v3l1-1c0 4 0 8 1 12v1l2 4c0 1 1 3 0 3 1 2 2 3 2 4l2 2c0 1 0 1 1 2v1c1 1 1 0 1 1 1 1 1 2 2 3h0l1 1 5 5c5 5 10 9 16 12l-1 1h-4c-4-1-7-3-10-5v-1h-1c1-1 2-1 2 0-6-5-11-10-15-16l-5-8-1-4-1-1c0-2-1-5-1-7l-1-5z" class="E"></path><path d="M162 311l1 1 5 5c5 5 10 9 16 12l-1 1h-4c-4-1-7-3-10-5v-1h-1c1-1 2-1 2 0 3 1 8 4 10 4-1-1-2-2-3-2-4-3-9-7-12-11-1-1-2-2-3-4z" class="g"></path><path d="M146 283c0-2 0-5 2-6 0-1 1-1 2-1v3l1-1c0 4 0 8 1 12v1l2 4c0 1 1 3 0 3h0l-3-9c-1-1 0-3-1-4v-4h-1c0 4 2 10 0 14v1l-1-1c0-2-1-5-1-7l-1-5z" class="L"></path><defs><linearGradient id="At" x1="478.512" y1="819.618" x2="477.494" y2="828.324" xlink:href="#B"><stop offset="0" stop-color="#6d6e6b"></stop><stop offset="1" stop-color="#898987"></stop></linearGradient></defs><path fill="url(#At)" d="M477 815c1 1 2 2 2 4l1 4c0 4 1 7 1 10v1l-1-1h0l1 3-2 6-2 2h0c-1 2-2 3-4 5 0-1-1-1-1-2l-3-4c0-2-1-3-1-5l-1-1c0-2 0-4 1-7s3-8 5-10l3-4 1-1z"></path><path d="M468 830c1-3 3-8 5-10l3-4c0 3 0 5-2 7-2 4-1 10-2 14l-3 6c0-2-1-3-1-5l-1-1c0-2 0-4 1-7z" class="X"></path><path d="M472 837l2-1c0-1 2-3 2-4s-1-3 0-5h0l3 5 1 1 1 3-2 6-2 2h0c-1 2-2 3-4 5 0-1-1-1-1-2l-3-4 3-6z" class="f"></path><path d="M479 832l1 1 1 3-2 6-2 2h0c-2 1-3 3-5 4 1-3 1-6 2-9 1-1 4-2 4-5l1-2z" class="d"></path><defs><linearGradient id="Au" x1="484.813" y1="524.449" x2="500.076" y2="498.387" xlink:href="#B"><stop offset="0" stop-color="#404042"></stop><stop offset="1" stop-color="#9d9c98"></stop></linearGradient></defs><path fill="url(#Au)" d="M488 489v3c1 0 1 0 2 1l6 3v-12c0-2 0-4 1-6v-1 59c-2-5-3-9-5-14-1-4-3-8-4-12-1-7 0-14 0-21z"></path><defs><linearGradient id="Av" x1="448.335" y1="730.855" x2="459.243" y2="768.413" xlink:href="#B"><stop offset="0" stop-color="#2f2f2f"></stop><stop offset="1" stop-color="#6a6a68"></stop></linearGradient></defs><path fill="url(#Av)" d="M452 733c0-2 0-4 1-6h0v2h0l1-5c0 5 2 10 3 15h1c0-1-1-1 0-2l2 1-1 1c1 3 2 7 3 10s0 6 1 8l-3 9c-2 2-3 4-5 5l-1-1c-2-7-1-16-1-23l-1-1v-3c1-3 0-7 0-10z"></path><path d="M457 739h1c0-1-1-1 0-2l2 1-1 1c1 3 2 7 3 10s0 6 1 8l-3 9c-1-3-1-6-1-9l-2-18z" class="O"></path><path d="M457 739h1c0-1-1-1 0-2l2 1-1 1v2 7c0 3 1 6 0 9l-2-18z" class="M"></path><defs><linearGradient id="Aw" x1="281.689" y1="253.822" x2="275.839" y2="265.123" xlink:href="#B"><stop offset="0" stop-color="#545453"></stop><stop offset="1" stop-color="#8e8d8b"></stop></linearGradient></defs><path fill="url(#Aw)" d="M273 249l1 1c-1 2-1 2 0 4 1 0 2-1 2-1h1c1-1 2-1 3-2h2c1 0 1 1 2 1v5l-1 2 1 2 2 2h6c2 2 3 3 4 6l-4 1-4 2c-2 1-3 3-5 5 0-2 0-3-1-4-2-2-3-2-5-2 0 1 0 2-1 3v-2c0-5-2-10-3-15 0-1 0-2-1-3-1 0-2 2-3 3l-1-1c2-2 3-4 3-7h0 2z"></path><path d="M273 249l1 1c-1 2-1 2 0 4 0 5 2 8 2 13l1 4c0 1 0 2-1 3v-2c0-5-2-10-3-15 0-1 0-2-1-3-1 0-2 2-3 3l-1-1c2-2 3-4 3-7h0 2z" class="K"></path><path d="M283 259l1 2 2 2h6c2 2 3 3 4 6l-4 1-4 2 1-3-2-1h0c-1-1-2-2-4-3-1 1-1 2-2 2 0-2 2-2 2-4 0-1-1-2 0-4z" class="Y"></path><path d="M283 265c2-1 4 0 6 0l1 1c0 1 0 1 1 1 0 1 1 2 1 3l-4 2 1-3-2-1h0c-1-1-2-2-4-3z" class="Q"></path><path d="M297 270c2 0 2-1 4 0v1l-2 1c0 1-1 1 0 2 1-2 2-2 3-3l2 1v2c1 2 1 6 0 8l1 1c-2 1-3 3-5 4v1c-2 0-3 0-5 1s-5 0-8 0c1-1 2-2 2-3h0c-2-3-4-3-7-3h-1c4-6 9-12 16-13z" class="O"></path><path d="M299 274c1-2 2-2 3-3l2 1v2c1 2 1 6 0 8s-3 4-5 4l-2-2c0-4 0-7 2-10z" class="U"></path><defs><linearGradient id="Ax" x1="220.642" y1="201.609" x2="200.849" y2="204.324" xlink:href="#B"><stop offset="0" stop-color="#474847"></stop><stop offset="1" stop-color="#8c8c89"></stop></linearGradient></defs><path fill="url(#Ax)" d="M216 184h8-1c-1 1-2 1-3 1-1 3-1 4 0 7l-4 11c-2 6-4 14-10 17l-1 1-3 1c0-2 1-4 2-6l4-11-1-1-1-3h-1c-2 1-3 4-5 6h0c2-4 4-7 7-11 2-2 4-5 6-7 1-1 2-2 3-4v-1z"></path><defs><linearGradient id="Ay" x1="449.428" y1="695.799" x2="436.814" y2="704.344" xlink:href="#B"><stop offset="0" stop-color="#242425"></stop><stop offset="1" stop-color="#575856"></stop></linearGradient></defs><path fill="url(#Ay)" d="M434 670h1v15 2 1l1-1v1l1-2h-1c0-1 1-2 1-3l1 1v2l1-1h0v-2h0c1 2 1 3 2 5l2 1v-3c-1-1-1-2-1-3h0c0-1-1-1-1-2l1-1h1c0-1-1-3-1-5h0c1 2 3 7 3 10 2 5 3 11 3 17 0 5-1 11-1 16v6l-1-4c-2-4-3-8-3-12h0v4c-1-1-2-1-2-2l-1-2c0-4-2-8-4-12h-1-1c-1-5 0-12 0-18v-8z"></path><path d="M442 680h1c0-1-1-3-1-5h0c1 2 3 7 3 10-1 2 0 7 0 10h-1v-6c0-3-1-6-2-9z" class="R"></path><path d="M756 240c-1-3-3-6-3-8 1-6 9-10 14-13 2 1 3 0 5 2 1 1 1 2 2 3l1 1c0 2 0 3-1 5h-1c-2 0-3 2-5 2l-1 3-2 6c-2 2-2 2-2 4-4-1-5-3-7-5z" class="H"></path><path d="M768 232c0-1 1-2 1-3v-2c0-2 1-4 3-6 1 1 1 2 2 3l1 1c0 2 0 3-1 5h-1c-2 0-3 2-5 2z" class="X"></path><path d="M756 240c1-2 2-2 4-3l7-7c-1 3-2 7-2 11-2 2-2 2-2 4-4-1-5-3-7-5z" class="U"></path><defs><linearGradient id="Az" x1="576.527" y1="654.271" x2="593.515" y2="668.522" xlink:href="#B"><stop offset="0" stop-color="#0c0c0c"></stop><stop offset="1" stop-color="#424242"></stop></linearGradient></defs><path fill="url(#Az)" d="M579 680c0-10 3-18 6-27l4-15 5 31c-1 0-1 1-2 1-1 1-3 3-3 5-2 1-3 1-5 2l-1 1-1-1c-1 2 0 3-2 5l-1-2z"></path><defs><linearGradient id="BA" x1="438.615" y1="245.1" x2="436.453" y2="274.219" xlink:href="#B"><stop offset="0" stop-color="#979690"></stop><stop offset="1" stop-color="#c5c4bf"></stop></linearGradient></defs><path fill="url(#BA)" d="M432 251l2-3v-1c1 0 1-1 2-1h6 2l2 2v4l1 4c-4 4-9 7-11 13-1 2-1 5-1 7-1 0-4-3-5-4-2-3-1-13-1-16l2-1c0-1 1-3 1-4z"></path><path d="M733 228l1-1c4-6 10-11 18-12 3-1 7-1 9 1v1c-3-1-6 0-9 2-2 0-4 1-5 1-2 1-2 1-3 2v1l-4 4c1 1 1 3 1 4l1 1 1-1v2h1c-1 3-3 6-2 9v6c0 2 1 4 1 6l2 2c-1 0-2-1-3-2l-1 2c-2 5-4 9-5 14h-1c-1-2 0-3 1-5s2-5 3-7l1-3v-1c-2-3-3-5-5-8v-1h0c-1-6-1-12-2-17z" class="M"></path><defs><linearGradient id="BB" x1="738.517" y1="233.422" x2="739.905" y2="251.622" xlink:href="#B"><stop offset="0" stop-color="#504f4f"></stop><stop offset="1" stop-color="#80817d"></stop></linearGradient></defs><path fill="url(#BB)" d="M740 227c1 1 1 3 1 4l1 1 1-1v2h1c-1 3-3 6-2 9v6c0 2 1 4 1 6l2 2c-1 0-2-1-3-2l-1 2c-2 5-4 9-5 14h-1c-1-2 0-3 1-5s2-5 3-7l1-3v-1c1-2 0-4-1-6-1-3-2-5-2-8-1-3 0-5 0-8l3-5z"></path><path d="M740 227c1 1 1 3 1 4l1 1 1-1v2c-1 1-2 3-3 3v-2c-1-1-2-1-3-2l3-5z" class="P"></path><defs><linearGradient id="BC" x1="687.623" y1="329.025" x2="699.108" y2="311.472" xlink:href="#B"><stop offset="0" stop-color="#171819"></stop><stop offset="1" stop-color="#3a3938"></stop></linearGradient></defs><path fill="url(#BC)" d="M703 307h2c1 2 2 3 2 5 2 2 3 2 5 2h-1c-1 1-2 3-3 5-1 1-1 2-1 3 0 0 1 0 1 1h-3c-1 0-1-1-1-1-4-1-9 2-11 3l-1 2-4-1h-1l-3 1h0l-1-2v-1l3-8c3-5 12-7 17-9z"></path><path d="M684 327c1-2 2-4 4-4l1-1c5-2 11-3 17-4l1 1c-7 3-14 1-20 7l-3 1h0z" class="T"></path><path d="M707 319h1c-1 1-1 2-1 3 0 0 1 0 1 1h-3c-1 0-1-1-1-1-4-1-9 2-11 3l-1 2-4-1h-1c6-6 13-4 20-7z" class="P"></path><path d="M686 316c0 1 0 1 1 1 3 0 6-2 9-2 3-1 8-1 11-3 2 2 3 2 5 2h-1c-1 1-2 3-3 5h-1l-1-1 1-1v-1c-2-1-2-2-5-1-3 0-7 1-10 2-2 1-4 2-7 3h0c0 2 0 3-2 4l3-8z" class="F"></path><path d="M703 307h2c1 2 2 3 2 5-3 2-8 2-11 3-3 0-6 2-9 2-1 0-1 0-1-1 3-5 12-7 17-9z" class="D"></path><defs><linearGradient id="BD" x1="429.336" y1="659.049" x2="424.16" y2="681.094" xlink:href="#B"><stop offset="0" stop-color="#404140"></stop><stop offset="1" stop-color="#767674"></stop></linearGradient></defs><path fill="url(#BD)" d="M431 651l1 3h1c1 3 1 5 1 7v9 8c-1 3-1 6-1 9-1-2-1-2-1-4 0-1 0-1-1-2h-3c0-1 0-2-1-2 0 1-1 2-1 3v-3h-1v-5c-1 3-1 5-1 8l-1-2c-1 0-2 0-3 1h0c-1 1-1 2-1 2 0-10 1-20 3-30v5c2 1 3 1 5 1s3 1 5 2c0-2-1-5-1-7v-3z"></path><path d="M431 651l1 3h1c1 3 1 5 1 7-2-2-2-4-3-7v-3z" class="B"></path><defs><linearGradient id="BE" x1="547.978" y1="555.723" x2="528.539" y2="533.217" xlink:href="#B"><stop offset="0" stop-color="#2c2c2b"></stop><stop offset="1" stop-color="#6d6c6c"></stop></linearGradient></defs><path fill="url(#BE)" d="M533 527l2-1c2-3 5-4 7-6 1 4 1 8 1 12s1 8 0 12c0 4-2 8-3 12l-5 16c-1-3 0-8 0-11l-2-33v-1z"></path><path d="M533 527l2-1c2-3 5-4 7-6v4-1c-2 1-3 2-5 3 0 1-2 2-2 2-1 1-1 0-2 0v-1z" class="g"></path><defs><linearGradient id="BF" x1="506.099" y1="562.75" x2="507.917" y2="523.404" xlink:href="#B"><stop offset="0" stop-color="#383738"></stop><stop offset="1" stop-color="#797a77"></stop></linearGradient></defs><path fill="url(#BF)" d="M500 514v4h1 0c1 2 1 4 1 5 2 1 4 1 6 1h1l1-1c0-1 1-2 1-3l2 50v-2h-1-1v-2l-1-1v-1c-1-1-1-1-1-2v-2c-1-1-1-1-1-2v-1-2c0-1-1-3-1-4h0v1c0 1-1 1-1 2h-1l-2-9c0-3-1-6-1-9l-1-15-1 1v-8z"></path><defs><linearGradient id="BG" x1="405.58" y1="680.418" x2="388.94" y2="683.096" xlink:href="#B"><stop offset="0" stop-color="#8d8d8a"></stop><stop offset="1" stop-color="#abaaa7"></stop></linearGradient></defs><path fill="url(#BG)" d="M396 667l1-1 1-1c1-1 1-1 3-2v1h0c1 2 2 3 3 4 0 1 0 1-1 2v2l1 1-5 9h1 1c1-1 2-2 3-4h1l1 1h0c0 1 0 1 1 2v1c-1 0-1 0-1 1l-7 13c-2 3-3 6-4 10-2-3-1-6-1-9 1 0 0 0 0-1-1 1-2 1-2 3h0c0-3 0-5-1-8 1-8 1-17 5-24z"></path><defs><linearGradient id="BH" x1="408.256" y1="689.113" x2="391.371" y2="692.556" xlink:href="#B"><stop offset="0" stop-color="#8b8b89"></stop><stop offset="1" stop-color="#a6a5a3"></stop></linearGradient></defs><path fill="url(#BH)" d="M400 682h1c1-1 2-2 3-4h1l1 1h0c0 1 0 1 1 2v1c-1 0-1 0-1 1l-7 13c-2 3-3 6-4 10-2-3-1-6-1-9 1 0 0 0 0-1 0-2 1-3 1-4l3-7 1-3h1z"></path><path d="M593 212l1-1c1-1 2-1 4-1 4 2 8 5 12 7l-4 4-6 12c-3 0-1-1-2-2 0-1-1-1-1-1v-1c-1 0-3 1-4 1v-2h-4 0c-1 1-1 2-2 2v-1c-2 0-3 0-4 1h0c-2 2-3 4-4 7v-1c-1 0-1-1-1-2 0-3 5-8 7-10 3-4 5-8 8-12z" class="D"></path><path d="M593 212l1-1c1-1 2-1 4-1l-1 2-3 3v-1-1l-1-1z" class="F"></path><path d="M583 230v-1c1-1 3-4 5-5 1 0 2-2 4-3h0l-3 5c-1 1-1 1 0 2-1 1-1 2-2 2v-1c-2 0-3 0-4 1h0z" class="W"></path><path d="M598 210c4 2 8 5 12 7l-4 4-3-1h0c1 0 1-1 2-2v-1c-3-1-3-1-5 0l1-1-1-1c-2 0-4 3-6 4 2-2 3-3 4-5l-1-2 1-2z" class="M"></path><path d="M589 228c0-1 1-2 2-3 2-2 3-5 7-6v1c-1 1-3 2-3 4l1-1c2-2 4-3 6-4l1 1h0c-2 1-3 2-4 4h0c-1 2-4 4-6 4h-4z" class="O"></path><defs><linearGradient id="BI" x1="601.55" y1="221.204" x2="597.232" y2="231.778" xlink:href="#B"><stop offset="0" stop-color="#2b2b2b"></stop><stop offset="1" stop-color="#575756"></stop></linearGradient></defs><path fill="url(#BI)" d="M593 228c2 0 5-2 6-4h0c1-2 2-3 4-4l3 1-6 12c-3 0-1-1-2-2 0-1-1-1-1-1v-1c-1 0-3 1-4 1v-2z"></path><defs><linearGradient id="BJ" x1="543.251" y1="786.048" x2="568.99" y2="814.54" xlink:href="#B"><stop offset="0" stop-color="#575755"></stop><stop offset="1" stop-color="#adacab"></stop></linearGradient></defs><path fill="url(#BJ)" d="M548 780l1-1v-1c1 0 2-1 2-1v1l2 13c2 10 7 19 12 28-1 1-2 1-3 2l1 1v1h-1l-7-8h-1-2c-1-1-2-3-3-4-1 0-2-2-3-3-1 0-1 0-2-1l-1 2v-3c1 0 2 0 3-1-1-1-1-1-1-2h0c0-5 2-9 2-13-1-2 1-4 0-6 0-1 0-3 1-4z"></path><path d="M548 780l1-1v-1c1 0 2-1 2-1v1l-1 1v1l-3 10c-1-2 1-4 0-6 0-1 0-3 1-4z" class="W"></path><path d="M545 803c4 4 6 8 10 12h-1-2c-1-1-2-3-3-4-1 0-2-2-3-3-1 0-1 0-2-1l-1 2v-3c1 0 2 0 3-1-1-1-1-1-1-2z" class="F"></path><defs><linearGradient id="BK" x1="593.423" y1="606.98" x2="614.099" y2="623.017" xlink:href="#B"><stop offset="0" stop-color="#252525"></stop><stop offset="1" stop-color="#7a7b78"></stop></linearGradient></defs><path fill="url(#BK)" d="M599 599c1-1 2-2 3-2l2 2c2 6 6 11 9 17l9 11c-1 1-2 1-3 1 0 1 1 2 2 3-3-1-4-3-6-4-1-2-2-3-3-4h-3v2c-1-1-3-3-3-5-2-1-2-3-3-5l-2-3c0 2-1 3-1 5l-2-1h-1c-2 3 0 8 1 11s3 6 5 9v2c-2-1-2-2-3-3h0c0-2-1-2-2-3l-3-6c0-2-2-4-2-6 0-1 1-4 2-5 1-6 2-11 4-16z"></path><path d="M598 616c0-2 0-5 1-6l2 2c0 2-1 3-1 5l-2-1z" class="H"></path><path d="M606 620c-1-1-1-3-2-4l-1-3h0c2 3 2 5 6 6l3 3v1h-3v2c-1-1-3-3-3-5z" class="Q"></path><defs><linearGradient id="BL" x1="616.1" y1="622.315" x2="618.426" y2="627.746" xlink:href="#B"><stop offset="0" stop-color="#7a7b78"></stop><stop offset="1" stop-color="#969591"></stop></linearGradient></defs><path fill="url(#BL)" d="M613 616l9 11c-1 1-2 1-3 1-2-1-4-2-4-4v-1l-1-2h0l1 1v-1c-1-1-2-3-2-5z"></path><defs><linearGradient id="BM" x1="416.775" y1="603.182" x2="402.225" y2="616.318" xlink:href="#B"><stop offset="0" stop-color="#2a2a2b"></stop><stop offset="1" stop-color="#646362"></stop></linearGradient></defs><path fill="url(#BM)" d="M413 597l1-2h1l4 10-2-1-1 1-1 2h1v1c0 1 1 2 1 3v2l-1 1h0l-2 4c-1 1-1 2-1 4l-1 2c-1-1-1-1-1-2v-3c-2 3-4 5-6 7-1 1-3 3-3 5-1 1-2 2-3 4 0 1-1 2-2 3s-2 3-3 4l-2 2c-1 1-2 2-3 2h0l2-3h-1l-1 1c-3 3-6 3-9 4-2 1-4 1-6 1 1-1 3-2 4-3 2-2 4-3 6-4 0-1 2-3 3-3 3-3 8-6 12-9 2-2 5-5 7-8h-1 1c0-3 0-4-2-6l9-19z"></path><path d="M414 608l1 1c0 2 0 3-2 5-1-1 0 0-1 0l-2 2h-1c1-2 2-3 3-5 1-1 2-2 2-3z" class="P"></path><path d="M413 597l1-2h1l4 10-2-1-1 1-1 2-1 1c0 1-1 2-2 3l2-5c1-2 1-2 1-4 0-1 0-1-1-2 0-1 0-2-1-3z" class="M"></path><path d="M415 607h1v1c0 1 1 2 1 3v2l-1 1h0l-2 4c-1 1-1 2-1 4l-1 2c-1-1-1-1-1-2v-3h0c0-2 1-3 2-5 2-2 2-3 2-5l-1-1 1-1z" class="K"></path><path d="M415 607h1v1c0 1 1 2 1 3v2l-1 1h0-1c-2 1-3 3-4 5 0-2 1-3 2-5 2-2 2-3 2-5l-1-1 1-1z" class="W"></path><defs><linearGradient id="BN" x1="393.193" y1="631.778" x2="386.485" y2="647.797" xlink:href="#B"><stop offset="0" stop-color="#b8b7b0"></stop><stop offset="1" stop-color="#dfdedc"></stop></linearGradient></defs><path fill="url(#BN)" d="M406 622l3-3c-3 6-8 11-13 16h1l5-4c-1 1-2 2-3 4 0 1-1 2-2 3s-2 3-3 4l-2 2c-1 1-2 2-3 2h0l2-3h-1l-1 1c-3 3-6 3-9 4-2 1-4 1-6 1 1-1 3-2 4-3 2-2 4-3 6-4 0-1 2-3 3-3 3-3 8-6 12-9 2-2 5-5 7-8z"></path><path d="M384 642h0c-1 3-5 4-7 6 4-1 8-3 12-4-3 3-6 3-9 4-2 1-4 1-6 1 1-1 3-2 4-3 2-2 4-3 6-4z" class="b"></path><defs><linearGradient id="BO" x1="359.895" y1="430.652" x2="331.284" y2="440.458" xlink:href="#B"><stop offset="0" stop-color="#303030"></stop><stop offset="1" stop-color="#605f5f"></stop></linearGradient></defs><path fill="url(#BO)" d="M352 422h0c1-1 1-1 1-2 1-1 1-1 3 0 2 3 4 7 5 11l5 17h0c-2-2-5-3-8-4-1-1 0-1-1-1l-1-4c-4 3-7 3-12 3 1-1 1-2 1-4l1-1v-2c0-1 1-1 1-2 1-2 1-4 0-5h-4c-5-1-11 0-16-1-2 0-3 0-5-1l18-2h4c3 1 6 1 8 0v-2z"></path><defs><linearGradient id="BP" x1="351.555" y1="438.754" x2="346.945" y2="439.746" xlink:href="#B"><stop offset="0" stop-color="#6e6d6d"></stop><stop offset="1" stop-color="#858483"></stop></linearGradient></defs><path fill="url(#BP)" d="M347 428c3 0 7 0 9 2v9c-4 3-7 3-12 3 1-1 1-2 1-4l1-1v-2c0-1 1-1 1-2 1-2 1-4 0-5z"></path><defs><linearGradient id="BQ" x1="327.712" y1="357.287" x2="298.72" y2="366.314" xlink:href="#B"><stop offset="0" stop-color="#737270"></stop><stop offset="1" stop-color="#c4c3c0"></stop></linearGradient></defs><path fill="url(#BQ)" d="M327 354h7l4 11h-5l-1-1h-3c-10 0-19 3-28 6l-4 1c1-1 2-2 3-2h1v-1c-1-2-3-3-4-5l6-3c8-4 15-6 24-6z"></path><defs><linearGradient id="BR" x1="332.522" y1="357.522" x2="322.516" y2="357.376" xlink:href="#B"><stop offset="0" stop-color="#525251"></stop><stop offset="1" stop-color="#7a7a79"></stop></linearGradient></defs><path fill="url(#BR)" d="M327 354h7l4 11h-5l-1-1h-3c0-2 0-3-1-4h-1-5c-2 0-3 1-4-1 2-1 4-1 6-1s3 0 4-1c0-2 0-2-1-3z"></path><path d="M328 360v-1c1 0 1 1 2 1 1 2 3 2 4 4l-1 1-1-1h-3c0-2 0-3-1-4z" class="Y"></path><defs><linearGradient id="BS" x1="503.642" y1="240.848" x2="489.358" y2="233.652" xlink:href="#B"><stop offset="0" stop-color="#b4b4b1"></stop><stop offset="1" stop-color="#d9d7d4"></stop></linearGradient></defs><path fill="url(#BS)" d="M499 210v3c1 0 1 1 1 2v1 2 1c1 2 1 4 1 6 0 4 0 8-1 13v3l-1 2v3c-1 1 0 2 0 3-1 1-1 1-1 2l-1 3-3 12c-1-2-1-5-2-7-2-6-1-10 0-16-1 1-2 1-3 1s-1 0-2-1c-3-4 6-15 8-19 2-5 3-10 4-14z"></path><path d="M502 368c6-3 8-8 13-12v1l1 1v3h-1c2 2 1 4 2 7v16c0 4 0 10-1 14v2 5c-1-2 0-3-1-5 0-1-2-2-2-3 0-2-1-6-2-9l-1-1v-1l-1 1-2-2-3-8v-7l-2-2z" class="S"></path><path d="M504 370c3 6 6 13 7 18l-1-1v-1l-1 1-2-2-3-8v-7z" class="E"></path><path d="M222 247c1 0 3-1 4 0 4 0 7 3 11 5 3 1 6 2 10 4 1 1 3 2 4 2l4 4c0 1 1 2 1 3v3l-1-1c-2-1-4-4-6-5l-1-1c-1 0-1 0-2 1 2 2 5 4 7 7 1 2 3 3 3 6-2-3-5-7-7-9-2 0-3-1-4-2l-2-2h-1c1 1 2 2 2 4l-4-4-3 1v-1c-1-1-4-3-5-3-1-1-2-1-3-2h-1c2 3 3 5 3 8l1 1-5-1-1-2v-1c-3-2-5-4-6-6-1-1-1-2-1-3 0-3 1-4 3-6z" class="a"></path><path d="M226 247c4 0 7 3 11 5 3 1 6 2 10 4-3 0-7 1-9-1h-1l-1-1c-1 0-2-1-3-1-2 0-1 0-2-1s-3-2-4-3c-1 0-1-1-1-2z" class="f"></path><path d="M226 255c2-1 2-1 4-1 6 1 14 7 19 12-2 0-3-1-4-2l-2-2h-1c1 1 2 2 2 4l-4-4-3 1v-1c-1-1-4-3-5-3-1-1-2-1-3-2h-1c2 3 3 5 3 8l1 1-5-1-1-2v-1c-3-2-5-4-6-6 2 0 4 0 6-1z" class="M"></path><path d="M229 257c4 0 7 1 10 3 1 1 2 1 3 2s2 2 2 4l-4-4-3 1v-1c-1-1-4-3-5-3-1-1-2-1-3-2z" class="H"></path><path d="M220 256c2 0 4 0 6-1v1c1 3 3 4 3 7v1l-3-1v-1c-3-2-5-4-6-6z" class="E"></path><defs><linearGradient id="BT" x1="556.483" y1="740.47" x2="571.832" y2="748.974" xlink:href="#B"><stop offset="0" stop-color="#181818"></stop><stop offset="1" stop-color="#444443"></stop></linearGradient></defs><path fill="url(#BT)" d="M556 753l9-28c1-4 1-9 2-13l3-9c0-2 1-2 1-3 1 1 0 1 0 2l-3 8v4c-1 1-1 0-1 1 0 2 0 3-1 4v10 4c-1 2-1 4-1 6l1 10c0 2 1 5 1 6l1 8 3 7v2h0c-2-1-3-1-5-1v1l-2 1-1-1c-1-2-1-4-2-6l-1 4-1 1-2-10-1 1c0-2-1-3-2-5 1-1 1-2 2-4z"></path><path d="M556 753c1 3 1 5 1 8l-1 1c0-2-1-3-2-5 1-1 1-2 2-4z" class="N"></path><path d="M561 766c-1-2-2-7-1-9 0 1 0 1 1 2h0 0c1-1 0-1 0-2v-1h1v1c1 1 2 1 2 2l1 1 1-1v1l1-1c-1-1-1-2-1-3l1-1 1 8 3 7v2h0c-2-1-3-1-5-1v1l-2 1-1-1c-1-2-1-4-2-6z" class="H"></path><path d="M567 755l1 8 3 7v2h0c-2-1-3-1-5-1h0c-1-2-2-4-2-5v-1l1 2c1 1 2 2 2 3h1c0-4-3-7-4-11l1 1 1-1v1l1-1c-1-1-1-2-1-3l1-1z" class="W"></path><defs><linearGradient id="BU" x1="331.534" y1="351.409" x2="325.223" y2="349.059" xlink:href="#B"><stop offset="0" stop-color="#3d3d3d"></stop><stop offset="1" stop-color="#5a5a59"></stop></linearGradient></defs><path fill="url(#BU)" d="M322 338c2 1 3 1 5 2l2 1 4 11c-2 1-7 1-9 0l-4 1-9 2-8 3-1-1c-1-2-2-3-1-6h0c3-6 10-9 15-11 2-1 4-1 6-2z"></path><path d="M320 353l1-1 2-1 1 1-4 1z" class="X"></path><path d="M316 340c2-1 4-1 6-2-1 2-1 3-2 4v1c1 2 2 2 4 3h0c-1 1-3 1-5 1v-1c0-1-2-2-2-3-1-1-1-2-1-3h0z" class="Q"></path><defs><linearGradient id="BV" x1="320.21" y1="356.411" x2="306.548" y2="348.14" xlink:href="#B"><stop offset="0" stop-color="#676768"></stop><stop offset="1" stop-color="#a8a7a4"></stop></linearGradient></defs><path fill="url(#BV)" d="M312 349l1 1c1 0 2 0 3-1 1 0 2 0 3-1 1 0 4 1 5 1h0c-1 0-2 1-3 2l-8 2h-1c-1 1-1 2-1 2l-8 3-1-1c-1-2-2-3-1-6v3h1v-1c2-1 4-1 6-2 1-1 3-1 4-2z"></path><defs><linearGradient id="BW" x1="315.656" y1="341.637" x2="302.616" y2="355.068" xlink:href="#B"><stop offset="0" stop-color="#8c8c8a"></stop><stop offset="1" stop-color="#bdbbba"></stop></linearGradient></defs><path fill="url(#BW)" d="M301 351h0c3-6 10-9 15-11h0c0 1 0 2 1 3 0 1 2 2 2 3v1c-2 1-5 1-7 2-1 1-3 1-4 2-2 1-4 1-6 2v1h-1v-3z"></path><defs><linearGradient id="BX" x1="200.602" y1="274.29" x2="229.569" y2="277.791" xlink:href="#B"><stop offset="0" stop-color="#6b6b69"></stop><stop offset="1" stop-color="#868683"></stop></linearGradient></defs><path fill="url(#BX)" d="M200 264s1-1 1-2h1 2v-1l2-2c1 1 0 1 1 2l8 6c0 1 1 2 2 3 2 1 4 3 6 5h1c2 2 4 5 7 7h1c1 2 2 2 2 3l1 1 1 1h0l2 5-2-2c-2-2-4-4-5-6l-1 1-4 4c2 2 3 2 6 2 0 1 0 1 1 1-2 0-4 0-6 1h-1l-2-2c0-1 0-2 1-2 0-2 3-3 3-5-1 1-3 3-5 4-6-7-12-12-22-15l-1-2h0c0-1 1-2 1-3h1l-1-1-3 3c-1-1 2-2 2-4v-2z"></path><path d="M200 264s1-1 1-2h1 2v-1l2-2c1 1 0 1 1 2v1c1 3 4 5 7 7h-1l-7-5-4 5-2 2h0c0-1 1-2 1-3h1l-1-1-3 3c-1-1 2-2 2-4v-2z" class="P"></path><defs><linearGradient id="BY" x1="407.84" y1="708.985" x2="388.66" y2="715.015" xlink:href="#B"><stop offset="0" stop-color="#4c4f4c"></stop><stop offset="1" stop-color="#807e7d"></stop></linearGradient></defs><path fill="url(#BY)" d="M412 677h0c2-1 4-4 6-5 0 2 0 4-1 5l-3 6-5 8c0 1-1 3-2 4-2 4-4 8-5 12-4 9-5 20-8 29l-2-13v-5h1v-2c0-3 0-5 1-6l1-4c1-4 2-7 4-10l7-13c2-2 4-5 6-6z"></path><path d="M412 677h0c2-1 4-4 6-5 0 2 0 4-1 5l-3 6-5 8c0 1-1 3-2 4-3 2-6 10-7 13-1 1-1 3-2 4 0-5 3-9 5-13v-1h0c-1-2 1-5 1-6 1-2 1-4 2-6-1 1-2 2-3 4l-2 3h0c-1 2-1 2-2 3l7-13c2-2 4-5 6-6z" class="P"></path><path d="M546 836v-1c2 1 3 3 4 4h1v1c2 3 4 5 7 7-2 3-5 5-8 8l-8 6-3 2h-1v-1c-3-2-7-7-9-11l7-7v-1h0c2 1 2 2 4 2v-2-2l1-1c1-1 2-2 2-3 1-1 0-1 1-1l1 4v1l1-1v-4z" class="M"></path><path d="M546 836v-1c2 1 3 3 4 4h1v1h-1c-1 1-1 3-1 5h0l-2 2c0-4 0-7-1-11z" class="f"></path><path d="M544 842c0-1 0-1 1-2v1c0 2 1 4 1 6-1 3 1 3-2 6h-1l-1-3c1-1 1-2 1-2 2-3 1-4 1-6z" class="O"></path><path d="M547 847l2-2c-1 3-2 6-1 9l2 1-8 6-3 2h-1v-1c1-2 2-3 4-4v1l3-3c3-3 2-5 2-9z" class="E"></path><path d="M540 845v-2-2l1-1c1-1 2-2 2-3 1-1 0-1 1-1l1 4c-1 1-1 1-1 2 0 2 1 3-1 6 0 0 0 1-1 2 0-1-1-1-1-2s-1-2-1-3z" class="B"></path><path d="M543 848l-1-2c0-2 0-4 1-5v-1l1 2c0 2 1 3-1 6z" class="M"></path><path d="M549 845h0c0-2 0-4 1-5h1c2 3 4 5 7 7-2 3-5 5-8 8l-2-1c-1-3 0-6 1-9zm-20 6l7-7 6 14c-2 1-3 2-4 4-3-2-7-7-9-11z" class="I"></path><defs><linearGradient id="BZ" x1="417.779" y1="305.631" x2="446.156" y2="313.116" xlink:href="#B"><stop offset="0" stop-color="#5a5a59"></stop><stop offset="1" stop-color="#94938f"></stop></linearGradient></defs><path fill="url(#BZ)" d="M413 292l3 1 2 1 9 6c2 1 3 1 4 3 5 2 9 8 14 12v1h1l6 3 1-1v1l1-2c0 2 1 6 0 8l-1-1v1h-2c-1 0-2-1-3-1l-9-4c-6-3-12-5-17-8h-1c-2-2-4-8-5-11v-2l-3-7z"></path><path d="M416 301c2 0 2 1 3 2 2 2 6 5 9 6h-1c-1 0-2 0-3-1-1 0-2 0-3-1v1c0 1 1 2 1 2v2h-1c-2-2-4-8-5-11z" class="e"></path><path d="M413 292l3 1 2 1 9 6c2 1 3 1 4 3-2 0-3-3-5-2-1 1 4 2 4 4h0c-1 0-2-1-3-1-2-1-3-2-4-4h-2v1l-2-1c-1 0-2-1-3-1l-3-7z" class="X"></path><path d="M413 292l3 1 2 1 9 6h0c-3-1-7-5-10-4 0 0 0 2 1 2 1 1 2 2 3 2v1l-2-1c-1 0-2-1-3-1l-3-7z" class="H"></path><defs><linearGradient id="Ba" x1="432.1" y1="320.271" x2="439.175" y2="312.563" xlink:href="#B"><stop offset="0" stop-color="#505251"></stop><stop offset="1" stop-color="#757571"></stop></linearGradient></defs><path fill="url(#Ba)" d="M422 312v-2s-1-1-1-2v-1c1 1 2 1 3 1 1 1 2 1 3 1h1c2 1 9 4 10 6h0c0 1 1 2 2 2h0c1 0 1 0 2 1s3 1 5 2l1-1-2-1c0-1 0-1-1-2h1l6 3 1-1v1l1-2c0 2 1 6 0 8l-1-1v1h-2c-1 0-2-1-3-1l-9-4c-6-3-12-5-17-8z"></path><path d="M445 316h1l6 3 1-1v1 5l-1-1c-1-1-4-2-5-3l1-1-2-1c0-1 0-1-1-2z" class="J"></path><defs><linearGradient id="Bb" x1="415.077" y1="586.324" x2="394.993" y2="612.231" xlink:href="#B"><stop offset="0" stop-color="#2c2c2d"></stop><stop offset="1" stop-color="#888884"></stop></linearGradient></defs><path fill="url(#Bb)" d="M408 575c2 3 2 6 3 9v4 8h1 1l-2 4c0 2-2 5-3 7l-6 10h-1l-1-2-4-3-2 3-6 9h-1l4-6 1-3c2-1 4-3 3-6l3-8c2-7 3-17 2-24h0l1-1c2 0 3 1 4 2 1 0 2 1 2 2l1-1v-4z"></path><path d="M411 588v8h1 1l-2 4c0 2-2 5-3 7 0-4 2-9 1-13v-2l1 2v-1-1c0-1 0-2 1-4z" class="W"></path><defs><linearGradient id="Bc" x1="408.375" y1="579.572" x2="392.702" y2="601.321" xlink:href="#B"><stop offset="0" stop-color="#3a3b3a"></stop><stop offset="1" stop-color="#81817f"></stop></linearGradient></defs><path fill="url(#Bc)" d="M401 576c2 0 3 1 4 2 1 0 2 1 2 2l1-1c0 2 0 3-1 4v3 2l-5 14c-1 2-2 3-3 5s-2 4-3 5l-2 3-6 9h-1l4-6 1-3c2-1 4-3 3-6l3-8c2-7 3-17 2-24h0l1-1z"></path><defs><linearGradient id="Bd" x1="434.294" y1="339.511" x2="444.736" y2="329.48" xlink:href="#B"><stop offset="0" stop-color="#666"></stop><stop offset="1" stop-color="#7c7b79"></stop></linearGradient></defs><path fill="url(#Bd)" d="M424 321c1-1 2 0 4 1l6 2 1-1 3 2h1l6 3v-1c3 1 5 2 7 5v3c2 5 3 9 4 14-2 0-3 0-4-1 0 0-1-1-2-1l-8-5-13-6-1-2c-2-4-3-9-4-13z"></path><path d="M428 334c4 1 11 4 13 7 1 0 1 1 1 1l-13-6-1-2z" class="X"></path><path d="M434 324l1-1 3 2h1l6 3v-1c3 1 5 2 7 5v3c-5-4-12-8-18-11z" class="W"></path><path d="M398 728c3-2 1-5 4-7 1 0 2 0 3 1v1h1l1-1 1 1c-1 1-1 2-1 3h1c-2 6-4 11-6 17l-2 5v1l-2 4-2 3c0 1-1 1-1 2-1 1-2 2-2 3-2 2-4 4-6 5l-2 2h0c-1 0-2 1-2 1l-3 1c-1 0-1 1-1 1l1-4c1-3 3-7 3-10 1-2 0-4 0-5 1 1 1 2 2 3 2-1 3-3 4-5 5-7 6-14 9-22z" class="V"></path><defs><linearGradient id="Be" x1="608.386" y1="329.051" x2="572.074" y2="339.813" xlink:href="#B"><stop offset="0" stop-color="#7b7b79"></stop><stop offset="1" stop-color="#a1a09b"></stop></linearGradient></defs><path fill="url(#Be)" d="M578 334c6-5 16-9 24-12 2-1 4-2 6-2 0 2-1 4-2 7l-3 8h0c-1 1-2 2-3 2-4 2-7 3-11 5l-1-1c-4 4-10 8-16 9 0-1 1-3 1-4h1c1-4 2-8 4-12z"></path><path d="M606 327l-3 8h0v-1c-3 0-3 0-5 1h-1l6-5h0c0-1 0-1-1 0h0v-1c2 0 3-1 4-2z" class="e"></path><path d="M597 335h1c2-1 2-1 5-1v1c-1 1-2 2-3 2-4 2-7 3-11 5l-1-1 9-6z" class="Y"></path><defs><linearGradient id="Bf" x1="209.777" y1="226.868" x2="184.613" y2="230.252" xlink:href="#B"><stop offset="0" stop-color="#989996"></stop><stop offset="1" stop-color="#c1bfba"></stop></linearGradient></defs><path fill="url(#Bf)" d="M200 207h0c2-2 3-5 5-6h1l1 3 1 1-4 11c-1 2-2 4-2 6l-2 2c-2 2-3 3-3 6-1 5 0 11 1 16 0 2 0 5 1 7-1 2-2 3-4 4-4-1-5-3-8-6v-1l-1-2c1-15 6-27 14-41z"></path><defs><linearGradient id="Bg" x1="235.602" y1="221.464" x2="240.532" y2="247.468" xlink:href="#B"><stop offset="0" stop-color="#51504f"></stop><stop offset="1" stop-color="#7e7e7c"></stop></linearGradient></defs><path fill="url(#Bg)" d="M227 222c1-1 2-3 4-4-1 2-2 7-1 8l2-5 1-1c0 2-1 3-1 4v5 1c1-1 1-1 1-2 1 1 1 1 2 1 0 2 1 2 2 3s1 0 2 1 1 1 3 1l1 1c1 0 1 0 1-1h2v1h2l2 2c0-1 0-1 2-1 1 0 3 1 4 2l-1 3c0 2-2 4-5 5-2 1-7 3-9 3-1 0-3-1-4-2-8-3-11-6-14-13 0-2-1-4-1-6 1-2 1-3 2-5l3-1z"></path><path d="M236 233c3 0 2 1 4 2 1 1 2 1 3 1l-1 1-1 2v-1c-1-2-2-3-4-4h-1v3h-1c0-2 0-2 1-4z" class="U"></path><path d="M227 222c1-1 2-3 4-4-1 2-2 7-1 8-1 2-1 3-1 4h-1c0-2 1-4 0-6h1l-1-1c-2 2-3 5-4 8l-1 3c0-2-1-4-1-6 1-2 1-3 2-5l3-1z" class="e"></path><path d="M224 223l3-1-3 5-1 1h-1c1-2 1-3 2-5z" class="P"></path><path d="M760 159c2 0 3 0 5 1-3 0-9 0-11 1s-6 2-7 4c-1 1-1 2-1 3h-7c-3-1-7 0-10-1h-6c-1 0-2 1-2 1l-1 1c-3 1-8 2-10 4v1c-4 1-9 1-13 1l10 1c-4 1-8 0-12 0s-8 0-12-1l-1 1-4-1s4-3 5-3 2-1 2-1c3-2 9-5 11-4h1v-1l2 1c11-4 22-7 34-5 3 0 8 3 11 2s5-2 8-3 5-1 8-2z" class="B"></path><path d="M697 171l1 1c3 0 5-2 7-2v1c-2 1-4 2-6 2v1h-8l6-3z" class="P"></path><path d="M705 171c6-1 11-3 16-3l-1 1c-3 1-8 2-10 4-4 1-8 1-11 1v-1c2 0 4-1 6-2z" class="U"></path><path d="M683 172c1 0 2-1 2-1 3-2 9-5 11-4h1v-1l2 1-16 8-1 1-4-1s4-3 5-3z" class="L"></path><path d="M728 163h2v1h-4l-1 1c2 0 3 0 5 1h-1c-8 0-15 3-24 4-2 0-4 2-7 2l-1-1c10-4 21-7 31-8z" class="H"></path><path d="M760 159c2 0 3 0 5 1-3 0-9 0-11 1s-6 2-7 4c-1 1-1 2-1 3h-7c-3-1-7 0-10-1h-6c-1 0-2 1-2 1-5 0-10 2-16 3v-1c9-1 16-4 24-4h1c-2-1-3-1-5-1l1-1h4v-1h-2 4l1-1c3 0 8 3 11 2s5-2 8-3 5-1 8-2z" class="D"></path><path d="M280 377c6-4 10-10 17-14 1 2 3 3 4 5v1h-1c-1 0-2 1-3 2l4-1c9-3 18-6 28-6h3v1c1 1 3 1 5 2v1h-1c1 1 1 1 1 2l3 2c1 2 2 3 2 5v1h-1c-1-1-3-2-4-3-4-3-9-4-14-4h-1c-7 0-14 1-21 3l-24 11v-2h-1c1-2 3-4 4-6z" class="N"></path><defs><linearGradient id="Bh" x1="333.483" y1="365.936" x2="324.617" y2="368.866" xlink:href="#B"><stop offset="0" stop-color="#333432"></stop><stop offset="1" stop-color="#545353"></stop></linearGradient></defs><path fill="url(#Bh)" d="M320 368c4-2 7-2 11-2 2 1 4 2 6 1v1h-1c1 1 1 1 1 2-6-1-11-2-17-2z"></path><defs><linearGradient id="Bi" x1="301.771" y1="367.438" x2="287.178" y2="380.444" xlink:href="#B"><stop offset="0" stop-color="#9f9f9a"></stop><stop offset="1" stop-color="#c8c5c8"></stop></linearGradient></defs><path fill="url(#Bi)" d="M297 371l4-1c2 0 3 0 4 1-1 0-1 1-1 1l-22 9 3-3c0-2 1-3 3-4l8-3h1z"></path><defs><linearGradient id="Bj" x1="316.666" y1="363.686" x2="310.334" y2="372.314" xlink:href="#B"><stop offset="0" stop-color="#6c6c6b"></stop><stop offset="1" stop-color="#939291"></stop></linearGradient></defs><path fill="url(#Bj)" d="M329 364h3v1c1 1 3 1 5 2-2 1-4 0-6-1-4 0-7 0-11 2-5 1-11 2-16 4 0 0 0-1 1-1-1-1-2-1-4-1 9-3 18-6 28-6z"></path><path d="M280 377c6-4 10-10 17-14 1 2 3 3 4 5v1h-1c-1 0-2 1-3 2h-1l-8 3c-2 1-3 2-3 4l-3 3c-1 1-4 1-5 2h-1c1-2 3-4 4-6z" class="C"></path><defs><linearGradient id="Bk" x1="417.633" y1="736.86" x2="387.166" y2="744.691" xlink:href="#B"><stop offset="0" stop-color="#b0b0ac"></stop><stop offset="1" stop-color="#edebe9"></stop></linearGradient></defs><path fill="url(#Bk)" d="M413 708l1-3h1v1l1 4v3h0v1h0l-1 2v7c-1 10-1 20-5 30-3 3-8 7-10 10 0 1-1 2-2 3l-2 2c-1 0-1 0-2 1v-3l-1-2c-1-1 0-2 0-3s1-2 2-3c0-1 1-1 1-2l2-3 2-4v-1l2-5c2-6 4-11 6-17 1-2 2-10 2-12l3-1v-5z"></path><path d="M413 708l1-3h1v1l1 4v3h0v1h0l-1 2v7c-2-3-2-7-2-10v-5z" class="L"></path><path d="M413 708l1-3h1v1l1 4v3h0v1h0l-1 2c0-2 0-3-1-5 0-1-1-2-1-3z" class="Q"></path><path d="M515 316c1 1 2 3 3 5 4 5 9 9 9 17 0 4-8 9-11 12h-1c-3-2-11-8-12-12 0-8 8-16 12-22z" class="V"></path><defs><linearGradient id="Bl" x1="773.666" y1="220.353" x2="778.22" y2="246.167" xlink:href="#B"><stop offset="0" stop-color="#6d6c6a"></stop><stop offset="1" stop-color="#8a8a87"></stop></linearGradient></defs><path fill="url(#Bl)" d="M778 230c0-2 1-4 1-5 0-4-2-8-4-11 1-1 1-1 2 0s4 0 5 1c1 0 4 7 5 8v1c1 1 4 1 4 2 1 3-1 9-3 11-3 6-8 10-14 11-3 1-8-2-11-3 0-2 0-2 2-4l2-6 1-3c2 0 3-2 5-2l-2 3h1s1-1 2-1 2-1 3-2h1z"></path><path d="M768 232c2 0 3-2 5-2l-2 3h1s1-1 2-1 2-1 3-2h1c-2 2-3 3-5 3-1 1-3 2-5 3l-1-1 1-3z" class="K"></path><defs><linearGradient id="Bm" x1="578.739" y1="679.92" x2="594.761" y2="701.08" xlink:href="#B"><stop offset="0" stop-color="#3b3b3a"></stop><stop offset="1" stop-color="#7c7d7a"></stop></linearGradient></defs><path fill="url(#Bm)" d="M594 669l2 30 1 3c-5 2-11 4-14 8h0l-1 1c1 2 1 4 0 6h0c-1-2 0-3-1-5 0 2 0 4-1 5h0c1-3 0-6 0-9 0-6 1-14 0-20l-1-8 1 2c2-2 1-3 2-5l1 1 1-1c2-1 3-1 5-2 0-2 2-4 3-5 1 0 1-1 2-1z"></path><path d="M582 711v-2l1-2c0-1 2-2 3-2l9-6v-1 1h1l1 3c-5 2-11 4-14 8h0l-1 1z" class="Q"></path><defs><linearGradient id="Bn" x1="357.799" y1="635.504" x2="362.825" y2="645.201" xlink:href="#B"><stop offset="0" stop-color="#8c8c88"></stop><stop offset="1" stop-color="#b9b8b6"></stop></linearGradient></defs><path fill="url(#Bn)" d="M395 609c1 3-1 5-3 6l-1 3-4 6h1l6-9 2-3 4 3 1 2h1a34.47 34.47 0 0 1-12 12c-8 7-18 14-27 18-6 2-12 3-18 5-4 2-7 4-10 6l-2 1-1 1h-2c-1 1-5 1-6 1 0-1 0-2 1-4s4-2 5-4c1-1 3-2 5-3 7-3 14-3 22-6 12-3 23-13 31-23 1-2 2-4 4-6l3-6z"></path><path d="M396 612l4 3 1 2h1a34.47 34.47 0 0 1-12 12l7-7v-3l-2 1c-1-2-1-3-1-5l2-3z" class="L"></path><defs><linearGradient id="Bo" x1="804.198" y1="227.414" x2="831.481" y2="232.032" xlink:href="#B"><stop offset="0" stop-color="#a7a6a1"></stop><stop offset="1" stop-color="#c5c5c0"></stop></linearGradient></defs><path fill="url(#Bo)" d="M810 209c0-1 0-3-1-4v-2l1-2 1 2h1l5 8c4 7 8 15 10 22 2 9 2 19 1 28-2-1-3-3-5-4l1-3h-1c0 1-1 1-2 2-3-2-5-4-7-7 3-5 3-14 2-19v-1c0-3-2-6-3-9l-3-11z"></path><defs><linearGradient id="Bp" x1="410.551" y1="633.023" x2="390.147" y2="642.053" xlink:href="#B"><stop offset="0" stop-color="#868682"></stop><stop offset="1" stop-color="#d9d8d3"></stop></linearGradient></defs><path fill="url(#Bp)" d="M417 613c1 1 1 3 2 4v5c0 1 0 3-1 4l1 2-2 3-5 6-3 4c-2 1-4 4-6 6-1 0-2 1-3 2h0c1 1 2 1 2 2 0 2 0 2 1 4 1 0 1 1 1 2l-2 1c-2 1-4 3-6 4 0-1-1-3-1-4-1-1-1-2-1-3l-3 1h0l-2 2c-1-1-1 0-2-1l1-1c1 0 4-2 5-3v-1c0-1 1-1 1-1l-1-2-1 1h0v1l-1-1h-1l1-1c1-2 1-3 1-5l2-2c1-1 2-3 3-4s2-2 2-3c1-2 2-3 3-4 0-2 2-4 3-5 2-2 4-4 6-7v3c0 1 0 1 1 2l1-2c0-2 0-3 1-4l2-4h0l1-1z"></path><path d="M397 652c1-2 2-2 3-3 1 1 2 1 2 2 0 2 0 2 1 4 1 0 1 1 1 2l-2 1c-2 1-4 3-6 4 0-1-1-3-1-4-1-1-1-2-1-3l-3 1c1-1 4-3 6-4z" class="a"></path><path d="M397 652c1-2 2-2 3-3 1 1 2 1 2 2 0 2 0 2 1 4 1 0 1 1 1 2l-2 1c-1-2-2-4-2-6v-1l-3 1z" class="E"></path><defs><linearGradient id="Bq" x1="415.838" y1="625.703" x2="409.497" y2="629.159" xlink:href="#B"><stop offset="0" stop-color="#5f5e5e"></stop><stop offset="1" stop-color="#787876"></stop></linearGradient></defs><path fill="url(#Bq)" d="M417 613c1 1 1 3 2 4v5c0 1 0 3-1 4l1 2-2 3-5 6v-3c-2-2-3-4-4-6 0-3 1-4 3-6 0 1 0 1 1 2l1-2c0-2 0-3 1-4l2-4h0l1-1z"></path><defs><linearGradient id="Br" x1="419.631" y1="619.077" x2="415.479" y2="622.337" xlink:href="#B"><stop offset="0" stop-color="#383838"></stop><stop offset="1" stop-color="#515150"></stop></linearGradient></defs><path fill="url(#Br)" d="M417 613c1 1 1 3 2 4v5c0 1 0 3-1 4l1 2-2 3c-1 0-1-1-1-1 3-4 0-4-1-7v-2c1-2 2-3 2-4v-1l-4 6c0-2 0-3 1-4l2-4h0l1-1z"></path><defs><linearGradient id="Bs" x1="554.033" y1="789.804" x2="558.89" y2="797.271" xlink:href="#B"><stop offset="0" stop-color="#6a6b69"></stop><stop offset="1" stop-color="#898886"></stop></linearGradient></defs><path fill="url(#Bs)" d="M554 757c1 2 2 3 2 5l1-1 2 10 1-1 1-4c1 2 1 4 2 6l1 1v1c-2 3-2 6-2 9 1 3 2 10 4 12 2 4 5 8 8 11l-2 2c1 1 2 2 3 4h-1l-3-3c-2 2-4 3-5 6 0 1 1 3 1 4-4-4-6-9-8-14s-4-9-5-14h-1l-2-13v-1c0-2 0-3-1-5l3-9 1-6z"></path><path d="M555 785c0 1 0 0 1 1 1-2 1-4 2-6l2 2 2 7c-2-1-4-1-6-1-1-1-1-1-1-3z" class="U"></path><path d="M550 772l3-9c1 3 0 7 1 9l1 4-1 1c-1-2 0-3-1-4-1 6 1 12 1 18h-1l-2-13v-1c0-2 0-3-1-5z" class="R"></path><defs><linearGradient id="Bt" x1="550.563" y1="766.517" x2="560.727" y2="772.658" xlink:href="#B"><stop offset="0" stop-color="#1d1d1d"></stop><stop offset="1" stop-color="#3d3d3c"></stop></linearGradient></defs><path fill="url(#Bt)" d="M554 757c1 2 2 3 2 5l4 20-2-2c-1 2-1 4-2 6-1-1-1 0-1-1v-1l-1-2v-5l1-1-1-4c-1-2 0-6-1-9l1-6z"></path><path d="M554 782h2v1 1h-1l-1-2z" class="W"></path><path d="M555 776c1 2 1 4 1 6h-2v-5l1-1z" class="D"></path><path d="M556 762l1-1 2 10 1-1 1-4c1 2 1 4 2 6l1 1v1c-2 3-2 6-2 9 1 3 2 10 4 12 2 4 5 8 8 11l-2 2-6-8c-1-4-3-7-4-11l-2-7-4-20z" class="T"></path><path d="M561 766c1 2 1 4 2 6l1 1v1c-2 3-2 6-2 9v-1c-1-4-2-8-3-11l1-1 1-4z" class="P"></path><defs><linearGradient id="Bu" x1="557.68" y1="794.464" x2="567.949" y2="812.614" xlink:href="#B"><stop offset="0" stop-color="#81817d"></stop><stop offset="1" stop-color="#dbdad7"></stop></linearGradient></defs><path fill="url(#Bu)" d="M559 805c-1-3-1-6-1-9v-1c0-1 1-1 2-1l2 2c1 1 2 2 4 3v1l6 8c1 1 2 2 3 4h-1l-3-3c-2 2-4 3-5 6 0 1 1 3 1 4-4-4-6-9-8-14z"></path><defs><linearGradient id="Bv" x1="428.488" y1="685.825" x2="423.05" y2="710.713" xlink:href="#B"><stop offset="0" stop-color="#82837f"></stop><stop offset="1" stop-color="#b3b2ad"></stop></linearGradient></defs><path fill="url(#Bv)" d="M424 682c0-3 0-5 1-8v5h1v3c0-1 1-2 1-3 1 0 1 1 1 2h3c1 1 1 1 1 2 0 2 0 2 1 4 0-3 0-6 1-9 0 6-1 13 0 18h1 1c2 4 4 8 4 12l-1-1c-1-1-1-2-1-4h0c-1-1-1-1-2-1l1 1-1 1h0l-1 1v1c-1 1 0 3 0 5l-1 10h-1v13c-1-3 0-6 0-9-1-5-1-9-1-14-2 0-4-1-5 0v3l1 1v2c-1 1-1 2-1 3h-1v-3c-1-3-1-5-1-8-2 0-4-1-6 1v8l1 1-1 3-1-10c0-2 1-4 0-5v3c0-2-1-3 0-5 0-1 0-1-1-2l2-15v-5s0-1 1-2h0c1-1 2-1 3-1l1 2z"></path><path d="M425 679h1v3 1h-1v-4z" class="g"></path><path d="M419 683s0-1 1-2h0c1-1 2-1 3-1l1 2h0c-1 2-1 3-1 4-1 1-3 1-4 1v1-5zm7-1c0-1 1-2 1-3 1 0 1 1 1 2h3c1 1 1 1 1 2 0 2 0 2 1 4l1 3-3-2h-2-1v-4l-1 1v-1h-1v5h0c-1-2-1-4-1-6h1v-1z" class="Q"></path><path d="M433 687c0-3 0-6 1-9 0 6-1 13 0 18h1 1c2 4 4 8 4 12l-1-1c-1-1-1-2-1-4h0c-1-1-1-1-2-1l1 1-1 1h0l-1 1v1c-1 1 0 3 0 5l-1 10h-1l1-31-1-3z" class="X"></path><defs><linearGradient id="Bw" x1="227.796" y1="313.93" x2="244.281" y2="307.084" xlink:href="#B"><stop offset="0" stop-color="#91908f"></stop><stop offset="1" stop-color="#b1b0ad"></stop></linearGradient></defs><path fill="url(#Bw)" d="M233 292c-1 0-1 0-1-1-3 0-4 0-6-2l4-4 1-1c1 2 3 4 5 6l2 2c0 1 0 0 1 1l3 7 1 1c0 1 1 2 1 3h0c2 1 2 3 3 5v-1s0-1 1-1l1 4c1 1 1 2 1 3l1 1 1-1v-1c1 1 1 2 1 4v1l1-1-10 17c-1 3-6 7-9 8h0l1-1 1-2c5-7 4-15 3-23l-3-5c-2-3-5-5-8-5-3-1-5 1-7 3l4-5v-1-1c2-3 1-5 0-9h1c2-1 4-1 6-1z"></path><path d="M227 293c2-1 4-1 6-1 1 1 2 2 3 4l-1 1h-1c-2 0-2 0-4-1v2c1 1 0 1 0 2-1 1-1 1-2 1l-1 3h-1v-1-1c2-3 1-5 0-9h1z" class="f"></path><path d="M227 293c2-1 4-1 6-1 1 1 2 2 3 4l-1 1h-1c-1-1-2-1-3-2s-2-2-4-2z" class="T"></path><path d="M227 304c0 1 3 1 5 2 0-2 0-2-1-3 1-2 2-3 2-5h1c1 1 2 1 3 2 2 0 2 2 3 4-1 2-1 4-2 6 0 2 2 3 2 6l-3-5c-2-3-5-5-8-5-3-1-5 1-7 3l4-5h1z" class="I"></path><path d="M244 304c2 1 2 3 3 5v-1s0-1 1-1l1 4c1 1 1 2 1 3l1 1 1-1v-1c1 1 1 2 1 4v1l1-1-10 17c0-2 2-4 2-6 2-8 1-16-2-24z" class="F"></path><path d="M247 309v-1s0-1 1-1l1 4c1 1 1 2 1 3l1 1c0 2-1 4-2 6l-2-12z" class="g"></path><defs><linearGradient id="Bx" x1="213.978" y1="263.626" x2="254.763" y2="300.652" xlink:href="#B"><stop offset="0" stop-color="#737370"></stop><stop offset="1" stop-color="#c5c4c1"></stop></linearGradient></defs><path fill="url(#Bx)" d="M213 257c2 1 5 4 8 5l3 2 3 1 5 1v2l3 2h1c0 1 1 2 2 3l1 2 2 3c1 1 1 2 2 3-1 1 0 1-1 1v1c1 1 2 3 3 3l2 2c1 3 3 6 4 9 0 1 1 2 1 4l-1 5c1 2 0 3-1 4l-1 1-1-4c-1 0-1 1-1 1v1c-1-2-1-4-3-5h0c0-1-1-2-1-3l-1-1-3-7c-1-1-1 0-1-1l-2-5h0l-1-1-1-1c0-1-1-1-2-3h-1c-3-2-5-5-7-7h-1c-2-2-4-4-6-5-1-1-2-2-2-3-2-2-5-4-7-6 0-1 0-1 1-1s3-2 4-3z"></path><path d="M224 264l3 1 5 1v2c-1 0-2-1-4-1l-9-3h5z" class="Q"></path><path d="M245 286l2 2c1 3 3 6 4 9 0 1 1 2 1 4l-1 5c1 2 0 3-1 4l-1 1-1-4c-1 0-1 1-1 1v1c-1-2-1-4-3-5h0c0-1-1-2-1-3l-1-1-3-7c-1-1-1 0-1-1l-2-5c1 1 2 1 2 2l1 1 1-1c4 6 5 13 9 18 1-4 0-6 0-10-1-3-2-4-3-6l-1-5z" class="f"></path><defs><linearGradient id="By" x1="211.213" y1="257.698" x2="227.497" y2="276.059" xlink:href="#B"><stop offset="0" stop-color="#555"></stop><stop offset="1" stop-color="#888884"></stop></linearGradient></defs><path fill="url(#By)" d="M213 257c2 1 5 4 8 5l3 2h-5c-1 0-2-1-3-1h-1 1c2 2 3 4 4 6 3 3 10 7 11 11 0 1 1 2 1 2h-1c-3-2-5-5-7-7h-1c-2-2-4-4-6-5-1-1-2-2-2-3-2-2-5-4-7-6 0-1 0-1 1-1s3-2 4-3z"></path><defs><linearGradient id="Bz" x1="398.235" y1="706.86" x2="374.336" y2="738.406" xlink:href="#B"><stop offset="0" stop-color="#9a9a96"></stop><stop offset="1" stop-color="#c5c3be"></stop></linearGradient></defs><path fill="url(#Bz)" d="M390 696l1-5c1 3 1 5 1 8h0c0-2 1-2 2-3 0 1 1 1 0 1 0 3-1 6 1 9l-1 4c-1 1-1 3-1 6v2h-1v-4l-1-1v11c1 5 3 11 1 16-1 4-3 7-5 10l-3-5c-4-6-6-14-7-21h1 1c2-3 2-8 2-12 0-3 1-5 1-7l-1-1c0-1 0-1 1-2s2-3 2-5l1-1 2 2v2l-1 1 1 1 4-6h-1z"></path><path d="M391 696v5h0c-1 0-1 0-2-1l-2 4c-1 2-2 5-2 7v-10l1-1h-2c-1 1-1 3-2 5l-1-1c0-1 0-1 1-2s2-3 2-5l1-1 2 2v2l-1 1 1 1 4-6z" class="G"></path><path d="M391 713v-6c1-1 0-3 1-4 0-3 0-4 2-6 0 3-1 6 1 9l-1 4c-1 1-1 3-1 6v2h-1v-4l-1-1z" class="J"></path><defs><linearGradient id="CA" x1="430.25" y1="352.68" x2="461.812" y2="364.769" xlink:href="#B"><stop offset="0" stop-color="#4b4b4a"></stop><stop offset="1" stop-color="#969693"></stop></linearGradient></defs><path fill="url(#CA)" d="M429 336l13 6 8 5c1 0 2 1 2 1 1 2 3 4 4 4 2 1 3 2 4 2l5 1 1 1 2 1c-1 2-2 3-4 4h-1c-1-1 0-1-1 0h0l5 4-1 1c-1-1-2-3-5-4 1 2 0 3-1 4v1h0c0 2 0 3-1 4l-1 2-6-4-12-6c-1 0-2-1-2-1-2-2-3-9-4-12l-3-7v-1c-1-2-1-4-2-6z"></path><path d="M456 356h-2c-2-1-3-3-3-4 1 1 2 2 4 2 2-1 3 0 5 0l5 1 1 1c-1 0-2 0-3 1-2 1-1 2-3 1l-2-1s-1-1-2-1z" class="e"></path><path d="M434 344c6 1 13 4 17 8h0c0 1 1 3 3 4h2l3 3h0c-5-2-9-5-14-8-3-3-8-4-11-7z" class="P"></path><defs><linearGradient id="CB" x1="436.386" y1="334.79" x2="448.194" y2="356.439" xlink:href="#B"><stop offset="0" stop-color="#141512"></stop><stop offset="1" stop-color="#39383a"></stop></linearGradient></defs><path fill="url(#CB)" d="M429 336l13 6 8 5c1 0 2 1 2 1 1 2 3 4 4 4 2 1 3 2 4 2-2 0-3-1-5 0-2 0-3-1-4-2h0c-4-4-11-7-17-8h0l-2-2-1 1v-1c-1-2-1-4-2-6z"></path><defs><linearGradient id="CC" x1="438.189" y1="361.319" x2="446.538" y2="357.796" xlink:href="#B"><stop offset="0" stop-color="#464746"></stop><stop offset="1" stop-color="#60605e"></stop></linearGradient></defs><path fill="url(#CC)" d="M434 350l14 10c1 1 2 2 3 2l1 1v1h-1c0 1 1 2 1 3v2l-12-6c-1 0-2-1-2-1-2-2-3-9-4-12z"></path><defs><linearGradient id="CD" x1="442.096" y1="702.367" x2="437.203" y2="770.299" xlink:href="#B"><stop offset="0" stop-color="#83837e"></stop><stop offset="1" stop-color="#a6a5a3"></stop></linearGradient></defs><path fill="url(#CD)" d="M434 721l1-10c0-2-1-4 0-5v-1l1-1h0l1-1-1-1c1 0 1 0 2 1h0c0 2 0 3 1 4l1 1 1 2c0 1 1 1 2 2v-4h0c0 4 1 8 3 12l1 4v11c0 10-1 19-3 29-1 3-2 7-4 10-3-12-4-23-5-35 0-6-1-12-1-18z"></path><path d="M441 710c0 1 1 1 2 2v-4h0c0 4 1 8 3 12l1 4v11c0-2 0-5-1-7l-1-1v-4c-1-4-3-9-4-13z" class="X"></path><defs><linearGradient id="CE" x1="465.194" y1="793.427" x2="454.417" y2="816.082" xlink:href="#B"><stop offset="0" stop-color="#6a6a68"></stop><stop offset="1" stop-color="#d5d4cf"></stop></linearGradient></defs><path fill="url(#CE)" d="M461 789l2-1v-2c2-1 2-3 3-4v1c1 0 1 1 2 0 2 0 1-1 3 0v1c1 2 1 2 2 3l1 1v3 2 3c0 1 0 2-1 3h0c-2 6-5 10-7 15 1 2 0 3 0 5l2 2 2-1-3 4c-5 4-9 10-12 15l-1-1v-3l-2-1c0 1-1 2-2 3l-1 1 11-18c-1-2-2-3-4-4-2-2-4-3-6-5-1 2-2 3-4 4h0c5-7 9-14 13-22l2-4z"></path><path d="M471 788v-3-1c1 2 1 2 2 3l1 1-1 1s-1 1-1 2l-1-3z" class="H"></path><path d="M474 788v3 2 3c0 1 0 2-1 3h0c-2 6-5 10-7 15l-14 20c0 1-1 2-2 3l-1 1 11-18c6-10 10-20 13-31l1-1z" class="B"></path><path d="M474 793v3c0 1 0 2-1 3h0l-1-1c0-2 1-3 2-5z" class="D"></path><defs><linearGradient id="CF" x1="466.974" y1="782.002" x2="464.787" y2="795.294" xlink:href="#B"><stop offset="0" stop-color="#464545"></stop><stop offset="1" stop-color="#757573"></stop></linearGradient></defs><path fill="url(#CF)" d="M461 789l2-1v-2c2-1 2-3 3-4v1c1 0 1 1 2 0 2 0 1-1 3 0v1 1 3l-1 9c-1-1 0-1-1-2h-2-2l-1 1-3-3h-2l2-4z"></path><defs><linearGradient id="CG" x1="468.131" y1="821.477" x2="451.236" y2="832.107" xlink:href="#B"><stop offset="0" stop-color="#646464"></stop><stop offset="1" stop-color="#9b9b96"></stop></linearGradient></defs><path fill="url(#CG)" d="M466 814c1 2 0 3 0 5l2 2 2-1-3 4c-5 4-9 10-12 15l-1-1v-3l-2-1 14-20z"></path><path d="M231 265c0-3-1-5-3-8h1c1 1 2 1 3 2 1 0 4 2 5 3v1l3-1 4 4c0-2-1-3-2-4h1l2 2c1 1 2 2 4 2 2 2 5 6 7 9 4 9 8 22 5 32-1 2-2 4-3 5-1 2-2 4-4 5h0l-1 1v-1c0-2 0-3-1-4v1l-1 1-1-1c0-1 0-2-1-3l1-1c1-1 2-2 1-4l1-5c0-2-1-3-1-4-1-3-3-6-4-9l-2-2c-1 0-2-2-3-3v-1c1 0 0 0 1-1-1-1-1-2-2-3l-2-3-1-2c-1-1-2-2-2-3h-1l-3-2v-2l-1-1z" class="B"></path><defs><linearGradient id="CH" x1="244.982" y1="262.778" x2="245.555" y2="278.22" xlink:href="#B"><stop offset="0" stop-color="#636162"></stop><stop offset="1" stop-color="#7e7f7c"></stop></linearGradient></defs><path fill="url(#CH)" d="M237 263l3-1 4 4c5 5 8 9 11 16-1-1-2-1-3-2-1-2-2-3-4-5-3-4-7-9-11-12z"></path><defs><linearGradient id="CI" x1="256.925" y1="282.813" x2="251.575" y2="293.187" xlink:href="#B"><stop offset="0" stop-color="#878787"></stop><stop offset="1" stop-color="#aeada7"></stop></linearGradient></defs><path fill="url(#CI)" d="M248 275c2 2 3 3 4 5 1 1 2 1 3 2 3 5 4 11 5 17l-1-1h-1 0c-1-2-2-5-3-7 1-3-4-9-5-11s-1-3-2-5z"></path><path d="M252 296h1c1-2 1-3 1-5h1c1 2 2 5 3 7h0 1l1 1c0 4 0 7-2 11v2c-1 2-2 4-4 5h0l-1 1v-1c0-2 0-3-1-4v1l-1 1-1-1c0-1 0-2-1-3l1-1c1-1 2-2 1-4l1-5c0-2-1-3-1-4l1-1z" class="a"></path><path d="M252 296c1 6 3 15 1 21 0-2 0-3-1-4v1l-1 1-1-1c0-1 0-2-1-3l1-1c1-1 2-2 1-4l1-5c0-2-1-3-1-4l1-1z" class="X"></path><path d="M252 301v13l-1 1-1-1c0-1 0-2-1-3l1-1c1-1 2-2 1-4l1-5z" class="J"></path><defs><linearGradient id="CJ" x1="243.41" y1="264.127" x2="243.035" y2="294.885" xlink:href="#B"><stop offset="0" stop-color="#6e6c6c"></stop><stop offset="1" stop-color="#b0b3ac"></stop></linearGradient></defs><path fill="url(#CJ)" d="M231 265c0-3-1-5-3-8h1c1 1 2 1 3 2 1 0 4 2 5 3v1c4 3 8 8 11 12 1 2 1 3 2 5s6 8 5 11h-1c0 2 0 3-1 5h-1l-1 1c-1-3-3-6-4-9l-2-2c-1 0-2-2-3-3v-1c1 0 0 0 1-1-1-1-1-2-2-3l-2-3-1-2c-1-1-2-2-2-3h-1l-3-2v-2l-1-1z"></path><path d="M244 278c2 1 3 1 4 2v1l1 1c0 2 1 4 1 6-3-3-4-7-6-10z" class="L"></path><path d="M231 265c0-3-1-5-3-8h1c1 1 2 1 3 2 1 0 4 2 5 3v1c1 3 3 4 4 7-3-2-6-4-8-7v1h-1l-1 1z" class="Y"></path><path d="M236 270c2 1 5 4 6 6v1l1 1h1c2 3 3 7 6 10 1 2 2 5 2 8l-1 1c-1-3-3-6-4-9l-2-2c-1 0-2-2-3-3v-1c1 0 0 0 1-1-1-1-1-2-2-3l-2-3-1-2c-1-1-2-2-2-3z" class="G"></path><path d="M241 278c3 2 5 6 6 10l-2-2c-1 0-2-2-3-3v-1c1 0 0 0 1-1-1-1-1-2-2-3z" class="E"></path><defs><linearGradient id="CK" x1="596.888" y1="245.303" x2="587.084" y2="265.745" xlink:href="#B"><stop offset="0" stop-color="#8c8c88"></stop><stop offset="1" stop-color="#bcbcb8"></stop></linearGradient></defs><path fill="url(#CK)" d="M589 228h4v2c1 0 3-1 4-1v1s1 0 1 1c1 1-1 2 2 2-2 9 0 18-1 26 0 3 1 6 0 9v4h-1c-2 0-2-1-3-3-4-6-10-9-15-13-1-2-2-4-4-6v-1l-1-5c1-2 3-5 4-7 1-3 2-5 4-7h0c1-1 2-1 4-1v1c1 0 1-1 2-2h0z"></path><defs><linearGradient id="CL" x1="601.449" y1="233.116" x2="589.561" y2="250.089" xlink:href="#B"><stop offset="0" stop-color="#545454"></stop><stop offset="1" stop-color="#8d8d8a"></stop></linearGradient></defs><path fill="url(#CL)" d="M593 230c1 0 3-1 4-1v1s1 0 1 1c1 1-1 2 2 2-2 9 0 18-1 26 0 3 1 6 0 9v-8l-1-1c-2-5-3-12-2-17v-1h0v1l-1 5c-1-2-1-2-1-3-2-1-4-2-6-2l-3 3c0-1 1-3 2-4h-2c0-2 1-3 2-5 1 0 2-1 2-2 1-2 2-3 4-4z"></path><defs><linearGradient id="CM" x1="580.904" y1="231.602" x2="584.845" y2="237.311" xlink:href="#B"><stop offset="0" stop-color="#444343"></stop><stop offset="1" stop-color="#5e5e5d"></stop></linearGradient></defs><path fill="url(#CM)" d="M589 228h4v2c-2 1-3 2-4 4 0 1-1 2-2 2-1 2-2 3-2 5h2c-1 1-2 3-2 4-1 3-1 5-4 8h0l-1 1v2c-1-2-2-4-4-6v-1l-1-5c1-2 3-5 4-7 1-3 2-5 4-7h0c1-1 2-1 4-1v1c1 0 1-1 2-2h0z"></path><defs><linearGradient id="CN" x1="581.67" y1="244.241" x2="582.347" y2="249.389" xlink:href="#B"><stop offset="0" stop-color="#7f7d7c"></stop><stop offset="1" stop-color="#92938f"></stop></linearGradient></defs><path fill="url(#CN)" d="M583 238h0c0-1 1-2 2-3v2 1s1-1 1-2h1c-1 2-2 3-2 5h2c-1 1-2 3-2 4-1 3-1 5-4 8h0l-1 1v2c-1-2-2-4-4-6 1-2 1-4 2-5h2 0c1-3 2-5 3-7z"></path><path d="M583 238h0c0-1 1-2 2-3v2 1c0 2-1 4-2 6h0v-6z" class="X"></path><defs><linearGradient id="CO" x1="567.61" y1="840.991" x2="546.676" y2="831.267" xlink:href="#B"><stop offset="0" stop-color="#080807"></stop><stop offset="1" stop-color="#272726"></stop></linearGradient></defs><path fill="url(#CO)" d="M546 808c1 1 2 3 3 3 1 1 2 3 3 4h2 1l7 8h1c2 3 5 7 8 10 0 3 0 3-1 6-1 1-2 3-2 5h0c-3 4-6 8-9 11-3 2-8 7-12 8-1 0-1-1-2-1-2 1-6 2-7 3-2 2-4 4-7 6l-1-1c2-2 5-4 7-7h1 1l3-2 8-6c3-3 6-5 8-8-3-2-5-4-7-7v-1h-1v-1l-3-4c-1-3-2-5-3-7 0-1 0-1 1-1h1v-3-2h0v-2l-1-2-1-1c1-3 1-5 2-8z"></path><path d="M559 825c2 2 7 7 8 10l-3 6v-5c-1-2-1-3-3-4 0-2-2-5-2-7z" class="e"></path><path d="M559 845h1l1 1c-3 4-6 7-9 10l-3 2c-2 2-5 2-7 3l8-6c3-3 6-5 8-8l1-2z" class="P"></path><path d="M542 861c2-1 5-1 7-3l3-2c-1 3-3 4-5 6h-2c-2 1-6 2-7 3-2 2-4 4-7 6l-1-1c2-2 5-4 7-7h1 1l3-2z" class="O"></path><path d="M557 822c0 1 2 2 2 3 0 2 2 5 2 7 2 1 2 2 3 4v5l-3 3-1 1h-1v-1c1-2 0-5 0-7l-1-8c-1-2-1-5-1-7z" class="K"></path><path d="M561 832c2 1 2 2 3 4v5l-3 3v-2c1-2 1-3 1-4l-1-6z" class="U"></path><path d="M546 808c1 1 2 3 3 3 1 1 2 3 3 4v1l2 1c1 2 2 3 3 5 0 2 0 5 1 7l1 8c0 2 1 5 0 7v1l-1 2c-3-2-5-4-7-7v-1h-1v-1l-3-4c-1-3-2-5-3-7 0-1 0-1 1-1h1v-3-2h0v-2l-1-2-1-1c1-3 1-5 2-8z" class="Y"></path><path d="M546 808c1 1 2 3 3 3 1 1 2 3 3 4v1c-1-1-2 0-3-2l-1-1c-1 1-1 2-1 3v1h-1-1l-1-1c1-3 1-5 2-8z" class="H"></path><defs><linearGradient id="CP" x1="553.034" y1="818.305" x2="551.714" y2="824.947" xlink:href="#B"><stop offset="0" stop-color="#6c6b6b"></stop><stop offset="1" stop-color="#888885"></stop></linearGradient></defs><path fill="url(#CP)" d="M554 817c1 2 2 3 3 5 0 2 0 5 1 7-1 0-1 0-1-1-1 0-2-1-3-1h-1v2h-1c-1 0-1-1-2-2v1c-1-1-2-3-2-4v-2h0v-3-1h1l1 1 1-1c1-1 2-1 3-1z"></path><path d="M550 828v-1c1 1 1 2 2 2h1v-2h1c1 0 2 1 3 1 0 1 0 1 1 1l1 8c0 2 1 5 0 7v1l-1 2c-3-2-5-4-7-7v-1h-1v-1l-3-4 2-6h1z" class="E"></path><path d="M549 828c1 2 2 3 3 4v3c0 1-1 1-2 2v1l-3-4 2-6z" class="L"></path><path d="M550 828v-1c1 1 1 2 2 2h1v-2h1c1 0 2 1 3 1 0 1 0 1 1 1l1 8c-1-2-3-3-3-5l-2-2h-2v2c-1-1-2-2-3-4h1z" class="J"></path><defs><linearGradient id="CQ" x1="415.833" y1="295.203" x2="451.251" y2="295.652" xlink:href="#B"><stop offset="0" stop-color="#71716d"></stop><stop offset="1" stop-color="#a9a9a4"></stop></linearGradient></defs><path fill="url(#CQ)" d="M422 277l6-3 9 9c1 3 4 7 4 10 2 5 5 7 9 10 2 0 5 0 7 1h-1c0 2-1 2 0 4h-1c-2 2-2 2-2 5l1 4-1 2v-1l-1 1-6-3h-1v-1c-5-4-9-10-14-12-1-2-2-2-4-3l-9-6-2-1-3-1-2-8c4-2 7-4 11-7z"></path><path d="M416 293c1-1 0-1 1-1h1l-1-1c-1-1-2-1-3-2 2-1 4 1 7 2h0l5 5c-2-1-6-3-8-3v1l-2-1z" class="Y"></path><path d="M451 311l2 7-1 1-6-3c1-1 2-2 2-3l1-1c1 1 1 3 2 4v-1-3-1z" class="f"></path><path d="M450 303c2 0 5 0 7 1h-1c0 2-1 2 0 4h-1c-2 2-2 2-2 5l1 4-1 2v-1l-2-7-1-8z" class="T"></path><path d="M413 292l-2-8c4-2 7-4 11-7 1 2 1 2 0 4v-1-1h-1c-2 2-4 3-6 4l9 8-9-6h0c2 3 4 4 6 6h0c-3-1-5-3-7-2 1 1 2 1 3 2l1 1h-1c-1 0 0 0-1 1l-3-1z" class="K"></path><defs><linearGradient id="CR" x1="444.839" y1="799.307" x2="427.632" y2="793.131" xlink:href="#B"><stop offset="0" stop-color="#a8a7a5"></stop><stop offset="1" stop-color="#d5d4d0"></stop></linearGradient></defs><path fill="url(#CR)" d="M433 721h1c0 6 1 12 1 18 1 12 2 23 5 35 2-3 3-7 4-10 0 1 1 1 1 2-1 2-1 3-1 6v1l1 1 5-1h2c1 0 1 1 1 2-2 9-3 18-9 25l-9 12h1v1c0 1-1 1-1 2 0 2 1 3 2 5v1c-2 1-3 2-5 3-1-1-2-1-3-2-1-2-2-4-2-7-2-8 0-16 0-25 1 1 2 0 4 0l-1 1h1v1c2-1 3-2 5-3 1-1 2-2 2-3h-1c2-4 2-5 1-9l-2-12-3-31v-13z"></path><path d="M433 721h1c0 6 1 12 1 18 1 12 2 23 5 35 2-3 3-7 4-10 0 1 1 1 1 2-1 2-1 3-1 6v1l1 1-1 1c0 2 2 3 2 5 2 4 0 10-3 13l-7 5v-1c4-3 9-7 9-12 1-3 0-5-1-7l-1-1h-1v1c-1 3-2 5-4 8h-1c2-4 2-5 1-9l-2-12-3-31v-13z" class="T"></path><defs><linearGradient id="CS" x1="691.493" y1="358.178" x2="730.723" y2="374.979" xlink:href="#B"><stop offset="0" stop-color="#a7a6a2"></stop><stop offset="1" stop-color="#eeede8"></stop></linearGradient></defs><path fill="url(#CS)" d="M678 356c7-2 14-2 21-1 4 0 8 1 12 3 6 2 13 9 18 14v1c3 2 7 6 8 10-2 1-3 0-5-1l-15-6-3-1c-6-2-12-4-18-5-4 0-8-1-12 1-1-1-1-1-2-1h-1l2-1h-1-5c1-1 1-1 1-2h1 0l-2-1v-1c0-3 0-6 1-9z"></path><defs><linearGradient id="CT" x1="717.387" y1="372.866" x2="734.82" y2="382.332" xlink:href="#B"><stop offset="0" stop-color="#b8b6b4"></stop><stop offset="1" stop-color="#e8e7e7"></stop></linearGradient></defs><path fill="url(#CT)" d="M718 372h1c1 1 2 1 3 1 2 1 3 1 4 2h3l1 1h1l-1-1c0-1 0-1-1-2 3 2 7 6 8 10-2 1-3 0-5-1l-15-6-1-3c1 0 2-1 2-1z"></path><defs><linearGradient id="CU" x1="686.985" y1="365.556" x2="703.915" y2="371.242" xlink:href="#B"><stop offset="0" stop-color="#4e4e4e"></stop><stop offset="1" stop-color="#7a7978"></stop></linearGradient></defs><path fill="url(#CU)" d="M691 366c9 1 18 3 26 6h1s-1 1-2 1l1 3-3-1c-6-2-12-4-18-5-4 0-8-1-12 1-1-1-1-1-2-1h-1l2-1h-1-5c1-1 1-1 1-2h1 0l12-1z"></path><path d="M683 369h1l1-1c2 0 3 1 5 1s4 0 6 1c-4 0-8-1-12 1-1-1-1-1-2-1h-1l2-1z" class="H"></path><path d="M714 375c-1-2-2-3-4-3h-1v-1c2 0 4 0 6 1h2 1s-1 1-2 1l1 3-3-1z" class="E"></path><defs><linearGradient id="CV" x1="677.214" y1="357.224" x2="690.971" y2="362.663" xlink:href="#B"><stop offset="0" stop-color="#6e6d6c"></stop><stop offset="1" stop-color="#a1a19e"></stop></linearGradient></defs><path fill="url(#CV)" d="M678 356c7-2 14-2 21-1h-1c-1 1-2 1-4 1-1 2-5 7-5 9h2v1l-12 1-2-1v-1c0-3 0-6 1-9z"></path><defs><linearGradient id="CW" x1="439.78" y1="212.984" x2="434.222" y2="238.986" xlink:href="#B"><stop offset="0" stop-color="#1a1a1a"></stop><stop offset="1" stop-color="#4e4e4e"></stop></linearGradient></defs><path fill="url(#CW)" d="M427 231l-1-2c-2-5-5-8-8-12 5-4 10-6 16-7l5 5c2 2 3 3 4 6-3-3-7-6-10-9v-1h-1c2 3 5 6 8 9 2 2 4 6 6 9l3 3c3 4 6 7 6 13l-1 2h0c-1 1-1 3-2 4l-1 2c-1 1-2 2-4 3l-1-4v-4l-2-2h-2-6c-1 0-1 1-2 1v1l-2 3c0 1-1 3-1 4l-2 1c1-7 2-14 0-20l-2-5z"></path><path d="M439 228c0 1 1 2 2 2v2h-2l-1-2 1-2z" class="H"></path><path d="M446 229l3 3c-3 0-3-1-5-3h0l2 1v-1z" class="O"></path><path d="M430 229v-1l2 1v-1l-2-2c1 0 2 0 2 1h1 1l-1-1v-1l6 3-1 2c-1-1-2-1-3-2-2 1-3 1-4 1h-1z" class="P"></path><defs><linearGradient id="CX" x1="450.406" y1="240.255" x2="444.875" y2="248.59" xlink:href="#B"><stop offset="0" stop-color="#515251"></stop><stop offset="1" stop-color="#82827e"></stop></linearGradient></defs><path fill="url(#CX)" d="M441 230c1 0 1 1 2 1v1c1 1 2 2 3 4l3 3c0 2 1 2 2 3 2 1 3 3 3 4v1c-1 1-1 3-2 4l-1 2c-1 1-2 2-4 3l-1-4v-4l-3-9c-1-3-2-5-4-7h2v-2z"></path><path d="M446 252c2 0 3-1 4 0l1 1c-1 1-2 2-4 3l-1-4z" class="L"></path><path d="M439 232h2c3 2 4 3 4 6v1h0v1 1h0l-1-1c0-1 0 0-1-1-1-3-2-5-4-7z" class="K"></path><defs><linearGradient id="CY" x1="434.662" y1="229.445" x2="437.613" y2="246.27" xlink:href="#B"><stop offset="0" stop-color="#4d4e4d"></stop><stop offset="1" stop-color="#8f8f8b"></stop></linearGradient></defs><path fill="url(#CY)" d="M430 229h1c1 0 2 0 4-1 1 1 2 1 3 2l1 2c2 2 3 4 4 7l3 9-2-2h-2-6c-1 0-1 1-2 1v1l-2 3c0 1-1 3-1 4l-2 1c1-7 2-14 0-20l-2-5 1-1v1l1-1 1-1z"></path><defs><linearGradient id="CZ" x1="427.642" y1="239.867" x2="435.302" y2="246.087" xlink:href="#B"><stop offset="0" stop-color="#5f605f"></stop><stop offset="1" stop-color="#8a8988"></stop></linearGradient></defs><path fill="url(#CZ)" d="M427 231l1-1v1l1-1v2c0 2 1 4 1 5 1-1 1 0 1-2 1 0 2-1 2-2l3 6h0l-1-1-1 2h0c-1 4-2 7-2 11 0 1-1 3-1 4l-2 1c1-7 2-14 0-20l-2-5z"></path><defs><linearGradient id="Ca" x1="526.484" y1="855.488" x2="503.822" y2="885.091" xlink:href="#B"><stop offset="0" stop-color="#d1d0cd"></stop><stop offset="1" stop-color="#fefdfb"></stop></linearGradient></defs><path fill="url(#Ca)" d="M525 854c2-2 2-3 2-6 0 2 0 2 2 3 2 4 6 9 9 11v1h-1c-2 3-5 5-7 7l1 1c0 1-2 3-1 4-3 4-5 7-6 12l-1 2c-2 2-3 4-5 6-1 2-2 2-4 2-7-2-8-10-12-15h0 0l1-1-1-2c1-2-2-4-2-7l1 1v-5c0-2 1-2 2-3 0 1 1 1 1 1h19c0-4 1-8 2-12z"></path><path d="M525 854c2-2 2-3 2-6 0 2 0 2 2 3 2 4 6 9 9 11v1h-1c-3-2-8-8-12-9z" class="F"></path><path d="M530 870l1 1c0 1-2 3-1 4-3 4-5 7-6 12l-1 2c-2 2-3 4-5 6-1 2-2 2-4 2-7-2-8-10-12-15h0 0l1-1-1-2c1-2-2-4-2-7l1 1 7 12c1 3 3 7 6 9 5-2 8-13 11-17 1-3 3-5 5-7z" class="N"></path><defs><linearGradient id="Cb" x1="703.238" y1="373.708" x2="741.697" y2="409.526" xlink:href="#B"><stop offset="0" stop-color="#d7d6cf"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#Cb)" d="M700 391v-1h0c-2-2-4-2-5-4l4-4c2-3 3-5 4-8l2 1h2 1l23 9 1-2c2 1 3 2 5 1 2 0 1-1 2-3l1-1c1 1 2 2 3 2h2l1 1c-3 1-5 1-7 1l6 5s5 5 6 5c2 2 4 4 6 7l-2 1c-1 0-2 0-2 1-1-1-2-1-3-1-4 0-10 0-14-2-2-1-5-2-7-2l-9-3c-1 0-2 1-2 0h-2c-1-1-1-1-2-1h-3c-2-1-2-1-3-1h-1-3v-1h-4z"></path><path d="M745 388s5 5 6 5c2 2 4 4 6 7l-2 1c-1 0-2 0-2 1-1-1-2-1-3-1l3-1v-1c-2-2-4-5-7-7 0-2-1-3-1-4z" class="D"></path><path d="M737 383c2 0 1-1 2-3l1-1c1 1 2 2 3 2h2l1 1c-3 1-5 1-7 1l6 5c0 1 1 2 1 4-4-4-9-6-15-8l1-2c2 1 3 2 5 1z" class="B"></path><defs><linearGradient id="Cc" x1="612.649" y1="286.199" x2="570.084" y2="307.889" xlink:href="#B"><stop offset="0" stop-color="#767774"></stop><stop offset="1" stop-color="#acaaa7"></stop></linearGradient></defs><path fill="url(#Cc)" d="M600 276c0-1 1-2 2-2 4 1 7 3 10 4 3 2 6 2 8 3l-4 13c0 1-1 4-2 5-1 0-3 1-4 1-2 1-3 1-5 2l-2 1c-2 1-3 2-5 2-2 1-5 2-7 4v-1h-2 0l-9 6c-1 1-2 5-4 6 0-1 0-2-1-2v-1-2h0c1-4 3-7 5-10l6-7c2-4 4-7 5-10l1-4c1-1 2-2 3-4l3-3 2-1z"></path><path d="M600 276h3c-2 1-4 3-6 5h-1c-1-1 0-1-1-1l3-3 2-1z" class="Y"></path><path d="M611 281c1 0 2 0 3 1h3c-1 1-2 2-3 2h0c-2 0-3 1-4 2h-1c1-2 2-4 2-5z" class="K"></path><defs><linearGradient id="Cd" x1="605.744" y1="296.442" x2="598.99" y2="306.22" xlink:href="#B"><stop offset="0" stop-color="#6b6a6b"></stop><stop offset="1" stop-color="#8c8c89"></stop></linearGradient></defs><path fill="url(#Cd)" d="M604 299c4-1 8-5 12-5 0 1-1 4-2 5-1 0-3 1-4 1-2 1-3 1-5 2l-2 1c-2 1-3 2-5 2-2 1-5 2-7 4v-1c1-1 2-1 2-2v-1l2-1c3-2 6-4 9-5z"></path><path d="M600 276c0-1 1-2 2-2 4 1 7 3 10 4 3 2 6 2 8 3l-4 13c-4 0-8 4-12 5 2-2 5-3 7-4l-1-1c-2 1-3 1-5 1 1-2 3-4 5-5 3-1 5-3 7-5v-1c-2 1-4 3-6 4s-3 2-4 3l-1-1c1-1 2-2 3-2l5-4c1 0 2-1 3-2h-3c-1-1-2-1-3-1l-8-5h-3z" class="H"></path><path d="M610 290l1 1c2-1 4-3 6-3-1 1-3 3-5 4 2 0 2-1 4 0-2 1-3 2-5 3l-1-1c-2 1-3 1-5 1 1-2 3-4 5-5z" class="Y"></path><defs><linearGradient id="Ce" x1="201.318" y1="228.498" x2="182.235" y2="226.923" xlink:href="#B"><stop offset="0" stop-color="#797876"></stop><stop offset="1" stop-color="#9b9b98"></stop></linearGradient></defs><path fill="url(#Ce)" d="M195 188c4-6 6-8 13-4 1 0 3 1 3 1 2 0 4 0 5-1v1c-1 2-2 3-3 4-2 2-4 5-6 7-3 4-5 7-7 11-8 14-13 26-14 41l1 2v1h0c0 3 1 4 2 6 0 1-3 5-4 6l-1 1c-1-2-2-3-3-4v-3l-1-2v-1c1-1 1-2 1-4-2-2-1-10-1-13 1-4 1-8 3-12l-1-1v-2-7c0-2 1-4 1-5 1-1 1-2 2-3 1-2 2-5 3-7 3-2 4-7 6-10 0 0 1-1 1-2z"></path><path d="M195 188c4-6 6-8 13-4 1 0 3 1 3 1 2 0 4 0 5-1v1c-1 2-2 3-3 4-2 2-4 5-6 7-3 4-5 7-7 11-8 14-13 26-14 41h0v-5-1c3-16 9-31 18-44l6-8 1-1c-2-1-5-3-7-3s-2 0-4 1c-3 4-5 8-8 11s-4 5-6 9h-1c1-2 2-5 3-7 3-2 4-7 6-10 0 0 1-1 1-2z" class="N"></path><path d="M200 187c2-1 2-1 4-1s5 2 7 3l-1 1-6 8v-2c-1-1-1-1-3-1v-1l-1 1c0 1-1 1-2 2s-2 3-3 5c-3 5-7 11-9 16-2 6-3 12-5 19v13c-2-2-1-10-1-13 1-4 1-8 3-12l-1-1v-2-7c0-2 1-4 1-5 1-1 1-2 2-3h1c2-4 3-6 6-9s5-7 8-11z" class="K"></path><path d="M200 187c2-1 2-1 4-1s5 2 7 3l-1 1-2-2h-1-3c-2 0-3 3-4 4 0-1 1-2 1-3l-1-2z" class="H"></path><path d="M193 200c0-1 1-2 2-2 0 1-1 3-2 4l-6 12c-2 4-3 7-4 11l-1-1v-2-7c0-2 1-4 1-5 1-1 1-2 2-3h1c2-4 3-6 6-9l1 2z" class="G"></path><path d="M186 207c2-4 3-6 6-9l1 2-7 11v-4z" class="W"></path><path d="M185 207h1v4l-4 11v-7c0-2 1-4 1-5 1-1 1-2 2-3z" class="B"></path><path d="M396 667c-4 7-4 16-5 24l-1 5h1l-4 6-1-1 1-1v-2l-2-2-1 1c0 2-1 4-2 5s-1 1-1 2l1 1c0 2-1 4-1 7 0 4 0 9-2 12h-1-1c1 7 3 15 7 21v5 1l-2-1c-2-5-4-11-5-16l-3-12c-3 2-5 2-8 2-2 0-4-1-6-3-3-4-2-10-1-14-2 1-3 3-5 4h-1v-1h-2c6-2 6-7 10-10l1 2h4c1-1 2-3 3-4v-1h1 2c6-6 10-12 13-20l11-10z" class="R"></path><path d="M376 697c1 1 1 2 1 3s0 3-1 5c0 3 1 8 0 10-2-4-3-9-3-14 0-1 2-3 3-4z" class="G"></path><path d="M376 697c1 1 1 2 1 3s0 3-1 5h-1-1l-1-4c0-1 2-3 3-4z" class="Y"></path><defs><linearGradient id="Cf" x1="389.911" y1="690.36" x2="384.637" y2="694.362" xlink:href="#B"><stop offset="0" stop-color="#5f605d"></stop><stop offset="1" stop-color="#7c7b78"></stop></linearGradient></defs><path fill="url(#Cf)" d="M385 695l5-10v10 1h1l-4 6-1-1 1-1v-2l-2-2-1 1 1-2z"></path><defs><linearGradient id="Cg" x1="382.428" y1="683.225" x2="383.162" y2="699.794" xlink:href="#B"><stop offset="0" stop-color="#3e3b3e"></stop><stop offset="1" stop-color="#70736d"></stop></linearGradient></defs><path fill="url(#Cg)" d="M376 697c3-3 6-7 8-10s4-6 6-8c-1 2-1 5-2 7-1 3-3 5-3 8v1l-1 2c0 2-1 4-2 5v-3c1-1 1-2 1-3h-1l-2 5-2 2-1-3c0-1 0-2-1-3z"></path><defs><linearGradient id="Ch" x1="375.91" y1="701.243" x2="382.796" y2="721.358" xlink:href="#B"><stop offset="0" stop-color="#858582"></stop><stop offset="1" stop-color="#a4a4a0"></stop></linearGradient></defs><path fill="url(#Ch)" d="M377 700l1 3 2-2 2-5h1c0 1 0 2-1 3v3c-1 1-1 1-1 2l1 1c0 2-1 4-1 7 0 4 0 9-2 12h-1-1l-1-9c1-2 0-7 0-10 1-2 1-4 1-5z"></path><path d="M370 706v-1-1c2 2 4 12 4 15 0 1-1 2-2 3-3 1-6 2-9 0-1 0-3-2-3-4-1-2-1-6 0-9h1v-1c1 0 1 1 1 1 3 0 6-2 8-3z" class="G"></path><path d="M370 706c0 1-1 2-2 3l-1 2h0c-1 1-2 3-2 4v4c-1-1-1-1-1-2l-1 1c-2-2-2-5-2-8v-1-1c1 0 1 1 1 1 3 0 6-2 8-3z" class="Q"></path><defs><linearGradient id="Ci" x1="539.423" y1="577.034" x2="517.095" y2="557.088" xlink:href="#B"><stop offset="0" stop-color="#2a2a2a"></stop><stop offset="1" stop-color="#5b5a5a"></stop></linearGradient></defs><path fill="url(#Ci)" d="M530 525h0c0-2 0-3 1-4l1 10 1 30h2c0 3-1 8 0 11-1 4-2 9-4 13l-3 12-1 2c-1 0-1 0-1-1s0-2-1-3h0c-1-1-1-2-1-3h0l-1-2c-1 1-1 2-1 3l-1 2c0 2 0 5-1 7-3-10-2-19-2-29l1-30h1v-8-6c1 0 2 0 3 1v1c2 1 2 1 3 0 3-1 3-3 4-6z"></path><path d="M533 561h2c0 3-1 8 0 11-1 4-2 9-4 13l-3 12-1 2c-1 0-1 0-1-1s0-2-1-3h0v-3c1 1 1 2 1 3h1c1-8 5-14 6-22 1-4 1-8 0-12z" class="F"></path><defs><linearGradient id="Cj" x1="516.366" y1="597.734" x2="523.759" y2="562.66" xlink:href="#B"><stop offset="0" stop-color="#141415"></stop><stop offset="1" stop-color="#4a4a4a"></stop></linearGradient></defs><path fill="url(#Cj)" d="M518 573c2-3 1-8 1-11 1 0 3 0 4 1 1 0 2 2 4 2v-5l1 9c-1 2 0 2 0 4 0 1 0 3-1 5 0 2 0 3-1 5-1-3 0-7 1-9h-1v4h0l-1-7v12l-1 9h0l-1-2c-1 1-1 2-1 3l-1 2c0 2 0 5-1 7-3-10-2-19-2-29z"></path><defs><linearGradient id="Ck" x1="529.404" y1="563.807" x2="520.677" y2="527.224" xlink:href="#B"><stop offset="0" stop-color="#414140"></stop><stop offset="1" stop-color="#81807f"></stop></linearGradient></defs><path fill="url(#Ck)" d="M530 525h0c0-2 0-3 1-4l1 10c-1 2-1 4-1 6v1 5c0 1-2 2-2 3-2 1-2 0-3 3 0 0 1 1 1 2v2 2l1 2v1 1h1c0 3 0 8-1 10l-1-9v5c-2 0-3-2-4-2-1-1-3-1-4-1 0 3 1 8-1 11l1-30h1v-8-6c1 0 2 0 3 1v1c2 1 2 1 3 0 3-1 3-3 4-6z"></path><path d="M797 184c1 1 4 2 5 2s2-1 3-2c5-2 9-3 12 1 1 1 1 1 1 2l3 1c0 1 0 2 1 4 0 1 1 2 2 3l1 2 2 3 2 3 3 7v1c-1 0-2-1-3-2 1 1 2 5 2 6 1 2 1 3 2 4 0 3 1 6 2 8l1-2c2 5 3 11 3 17 0 0-1 1-1 2-1 2 0 4-1 7 0 3-1 6-2 9 0-4 2-12 0-15 0 8 0 13-4 20l-3-4c1-9 1-19-1-28-2-7-6-15-10-22l-5-8-1-1c-2-4-5-7-8-11l-1-1c-3-1-4-3-7-4l2-2z" class="K"></path><path d="M828 235h1l1 3 1 16h-1l-2-19z" class="G"></path><path d="M833 229l1 10v5 7h-1l-1-20 1-2z" class="Y"></path><path d="M813 202c1 0 1 1 2 2s2 2 2 3h1 1l3 2c1 2 3 5 4 7 2 5 3 9 5 13 0 3 0 6-1 9l-1-3h-1c-2-9-5-17-10-24l-5-9z" class="E"></path><path d="M826 216c2 5 3 9 5 13 0 3 0 6-1 9l-1-3v-2c-1-1-1-3-1-4-1-3-1-4-1-7 0-1-1-4-1-6z" class="Q"></path><path d="M821 199c1 2 3 6 5 7 1 0 1 1 2 2v2c1 1 1 2 1 3 1 0 1 1 1 2l1 1v-1c1 2 1 3 2 4 0 3 1 6 2 8l1-2c2 5 3 11 3 17 0 0-1 1-1 2-1 2 0 4-1 7 0 3-1 6-2 9 0-4 2-12 0-15-1-1-1-4-1-6l-1-10-1 2v-2h-1c-2-4-3-8-5-13-1-2-3-5-4-7l-3-6v-2-1l3 4h1c-1-1-1-2-2-3v-1-1z" class="H"></path><path d="M836 225c2 5 3 11 3 17 0 0-1 1-1 2-1 2 0 4-1 7 0-8 0-16-2-24l1-2z" class="N"></path><path d="M821 199c1 2 3 6 5 7 0 1 0 2 1 4s3 6 3 8v2 1c1 1 1 1 1 2l1 1v2c1 1 1 2 1 3l-1 2v-2h-1c-2-4-3-8-5-13-1-2-3-5-4-7l-3-6v-2-1l3 4h1c-1-1-1-2-2-3v-1-1z" class="X"></path><defs><linearGradient id="Cl" x1="804.073" y1="181.357" x2="810.672" y2="192.621" xlink:href="#B"><stop offset="0" stop-color="#060506"></stop><stop offset="1" stop-color="#272727"></stop></linearGradient></defs><path fill="url(#Cl)" d="M797 184c1 1 4 2 5 2s2-1 3-2c5-2 9-3 12 1 1 1 1 1 1 2l3 1c0 1 0 2 1 4 0 1 1 2 2 3l1 2 2 3 2 3 3 7v1c-1 0-2-1-3-2 1 1 2 5 2 6v1l-1-1c0-1 0-2-1-2 0-1 0-2-1-3v-2c-1-1-1-2-2-2-2-1-4-5-5-7v1 1c1 1 1 2 2 3h-1l-3-4v1 2l3 6-3-2h-1-1c0-1-1-2-2-3s-1-2-2-2l5 9h-1l-5-8-1-1c-2-4-5-7-8-11l-1-1c-3-1-4-3-7-4l2-2z"></path><path d="M810 187c2-1 2-1 4-1l1 3c0 2 0 2 1 3v3l-4-5c0-2-1-2-2-3z" class="D"></path><path d="M816 195v-3c-1-1-1-1-1-3l2 4c1 2 2 4 4 6v1 1c1 1 1 2 2 3h-1l-3-4c0-2-3-3-3-5z" class="e"></path><path d="M818 187l3 1c0 1 0 2 1 4 0 1 1 2 2 3l1 2 2 3 2 3 3 7v1c-1 0-2-1-3-2-2-8-9-13-11-20-1-1 0-1 0-2z" class="R"></path><defs><linearGradient id="Cm" x1="809.883" y1="189.791" x2="809.12" y2="198.666" xlink:href="#B"><stop offset="0" stop-color="#5a5959"></stop><stop offset="1" stop-color="#757673"></stop></linearGradient></defs><path fill="url(#Cm)" d="M810 187c1 1 2 1 2 3l4 5c0 2 3 3 3 5v1 2l3 6-3-2h-1-1c0-1-1-2-2-3s-1-2-2-2c-1-1-3-3-3-4-2-3-6-6-7-9l1-1 6-1z"></path><path d="M819 207l-2-3v1l-1-2-1-1h0v-1c1 2 2 1 3 1l1 1 3 6-3-2z" class="G"></path><path d="M810 187c1 1 2 1 2 3l4 5c0 2 3 3 3 5v1 2l-1-1c0-1 0-1-1-1h0c-1 0-1-2-2-3-1-2-4-8-6-8-1-1-1 0-2-1s-1-1-3-1l6-1z" class="W"></path><path d="M812 190l4 5c0 2 3 3 3 5v1 2l-1-1c0-1 0-1-1-1h0c-1 0-1-2-2-3 0-2-1-5-3-7v-1z" class="K"></path><path d="M404 796c7-11 15-22 19-35h0l3 23 1 6c0 9-2 17 0 25 0 3 1 5 2 7 1 1 2 1 3 2 2-1 3-2 5-3v-1c-1-2-2-3-2-5 0-1 1-1 1-2v-1c1-1 3-3 4-5 5-5 8-9 11-16 2-6 3-13 6-19l3-1c1-1 2-3 3-4s1-2 2-3v4h0c1 1 0 3 0 4l2 1v-4c2 2 1 5 1 8-1 1-1 3-1 5l-1 1v-1c-1 1-1 3-3 4v2l-2 1-2 4c-4 8-8 15-13 22-1 0-2 2-2 2-3 3-6 6-10 7-1 1-3 1-4 0-4-2-4-8-5-12-1-1-2-1-3-2v-2l-4 2h0v-13l-1 2c-1 0-1 0-2-1-1 0-1-4-2-5l-1 1c0 3 1 6 0 10l-4-6h-2v1c-2 1-2 3-4 4v-1c2-2 2-3 2-6z" class="N"></path><path d="M465 768h0c1 1 0 3 0 4l2 1-1 3-1 1h-1l1-9zm-5 3h2c1 2 0 6 0 8v1c0-3-3-6-5-8l3-1z" class="B"></path><path d="M413 786c-1 3-1 5-1 8s1 6 0 10l-4-6c0-1-1-1 0-2l1-2 4-8z" class="J"></path><defs><linearGradient id="Cn" x1="463.075" y1="776.658" x2="466.925" y2="782.342" xlink:href="#B"><stop offset="0" stop-color="#2d2d2d"></stop><stop offset="1" stop-color="#4a4a4a"></stop></linearGradient></defs><path fill="url(#Cn)" d="M467 773v-4c2 2 1 5 1 8-1 1-1 3-1 5l-1 1v-1c-1 1-1 3-3 4v2l-2 1 3-12h1l1-1 1-3z"></path><defs><linearGradient id="Co" x1="462.244" y1="779.052" x2="450.416" y2="788.521" xlink:href="#B"><stop offset="0" stop-color="#343434"></stop><stop offset="1" stop-color="#605f5e"></stop></linearGradient></defs><path fill="url(#Co)" d="M457 772c2 2 5 5 5 8-2 5-3 13-7 18 0-1 0-2 1-3v-1c0-1-2-2-3-3h-1-1c2-6 3-13 6-19z"></path><defs><linearGradient id="Cp" x1="449.689" y1="789.971" x2="440.199" y2="820.495" xlink:href="#B"><stop offset="0" stop-color="#61615f"></stop><stop offset="1" stop-color="#bcbbb7"></stop></linearGradient></defs><path fill="url(#Cp)" d="M451 791h1 1c1 1 3 2 3 3v1c-1 1-1 2-1 3-3 8-11 18-18 23v-1c-1-2-2-3-2-5 0-1 1-1 1-2v-1c1-1 3-3 4-5 5-5 8-9 11-16z"></path><path d="M413 786c1-4 4-8 6-13 1-2 2-6 4-9l2 26c0 6-1 12-1 19l1 3c-1-1-2-1-3-2v-2l-4 2h0v-13l-1 2c-1 0-1 0-2-1-1 0-1-4-2-5l-1 1c0-3 0-5 1-8z" class="a"></path><path d="M418 797h1v-3l2-4c0 5 1 10 2 15v4h1c-1-2-1-4 0-6v6l1 3c-1-1-2-1-3-2v-2l-4 2h0v-13z" class="C"></path><path d="M639 486h1l6-2c4-1 6 0 9 2 1 0 2 1 2 2 1 2 1 6 0 8h0v1h-1l-1 1c0 1 1 1 1 2-1 2-3 4-5 7l-6 2c-2 0-8 2-9 4-2 1-4 3-6 5-2 1-3 3-4 5s-1 4-1 6 0 4 1 6c1 1 2 2 3 4h-1l5 9 6 6-2 2c-1 1-2 1-4 0h-1l-4-1-1 2c0 2-1 4-3 7l-1-1c-1-1-3-3-4-3l-1-2-1-1-1-2-2-3c0-2 1-3 1-5 1-3 2-7 3-11l11-34c0-1 1-3 1-4l3-10h1c1 0 4-2 5-2h0z" class="W"></path><path d="M645 496h1l3 1c-5 1-9 4-13 5-1 0-3 1-4 1v-1c4-3 8-5 13-6z" class="U"></path><path d="M617 548l1-1c1-4 2-7 2-11l2 10 1 3c-1 2-1 3-3 4l-1 1h0l-1 4-1-1-1-2-2-3c0-2 1-3 1-5l2 1z" class="K"></path><path d="M615 547l2 1c0 2-1 3-2 4h-1c0-2 1-3 1-5z" class="H"></path><path d="M616 555c1-1 1-1 1-3 1-1 2-2 2-3h2v-3h1l1 3c-1 2-1 3-3 4l-1 1h0l-1 4-1-1-1-2z" class="Q"></path><defs><linearGradient id="Cq" x1="621.125" y1="535.631" x2="629.103" y2="542.832" xlink:href="#B"><stop offset="0" stop-color="#656564"></stop><stop offset="1" stop-color="#898886"></stop></linearGradient></defs><path fill="url(#Cq)" d="M627 553l-1-1c-4-5-3-16-3-22l5 9h0l5 9c-2 1-5 3-5 5h-1z"></path><path d="M628 553c0-2 3-4 5-5l6 6-2 2c-1 1-2 1-4 0h-1l-4-1-1 2c0 2-1 4-3 7l-1-1c-1-1-3-3-4-3l-1-2 1-4h0l1-1c2-1 2-2 3-4 1 2 2 3 4 4h0 1z" class="G"></path><path d="M628 553c0-2 3-4 5-5l6 6-2 2c-1 1-2 1-4 0h-1 3l-3-1c-2 0-3-1-4-2h0z" class="C"></path><defs><linearGradient id="Cr" x1="625.151" y1="511.694" x2="655.469" y2="502.804" xlink:href="#B"><stop offset="0" stop-color="#5b5a59"></stop><stop offset="1" stop-color="#8a8b87"></stop></linearGradient></defs><path fill="url(#Cr)" d="M649 497h6 1l-1 1c0 1 1 1 1 2-1 2-3 4-5 7l-6 2c-2 0-8 2-9 4-2 1-4 3-6 5-2 1-3 3-4 5 0-2-1-3 0-4v-1c0-1 0-2 1-3v-2c0-1 0-1 1-2v-2c0-1 1-1 1-1 2-3 4-4 6-5l1-1c4-1 8-4 13-5z"></path><path d="M629 508h1c2-1 5-3 8-3-4 2-7 3-9 8h0c-1 2-1 4-2 5l1 1 1-1h1c-2 1-3 3-4 5 0-2-1-3 0-4v-1c0-1 0-2 1-3v-2c0-1 0-1 1-2v-2c0-1 1-1 1-1z" class="H"></path><defs><linearGradient id="Cs" x1="640.23" y1="498.506" x2="648.884" y2="482.699" xlink:href="#B"><stop offset="0" stop-color="#2e2e2e"></stop><stop offset="1" stop-color="#494948"></stop></linearGradient></defs><path fill="url(#Cs)" d="M639 486h1l6-2c4-1 6 0 9 2 1 0 2 1 2 2 1 2 1 6 0 8h0v1h-1-1-6l-3-1h-1c-1-1-1-1-2-1h-3c0 1-1 1-2 1s0 0-1 1c-4 1-5 3-8 5 0-1 1-3 1-4l3-10h1c1 0 4-2 5-2h0z"></path><path d="M655 486c1 0 2 1 2 2h-3-5v-1c2 0 5 0 6-1z" class="O"></path><path d="M646 496c3-1 7-1 10-1l1 1h-3l1 1h-6l-3-1z" class="K"></path><path d="M630 498l2-2 1-1 1-1 1 1-1 1h1 2l1-1h2c1-1 2-1 3-1v1h-3c0 1-1 1-2 1s0 0-1 1c-4 1-5 3-8 5 0-1 1-3 1-4z" class="D"></path><path d="M461 244c3-1 6-1 9-2 1 2 3 4 4 5v1c1 3 3 5 3 8 2 5 1 13 0 18 0 2-1 5-1 6l-5 17v4c-3 6-4 12-7 18l-6-9c-1-1-2-1-3-2h1c-1-2 0-2 0-4h1c-2-1-5-1-7-1l-2-6c-1-1-2 0-4 0-2-6 11-16 15-21 2-3 4-6 5-9 3-8 1-16-3-23z" class="V"></path><path d="M471 297v4c-3 6-4 12-7 18l-6-9c-1-1-2-1-3-2h1c-1-2 0-2 0-4h1 1c2 1 5 9 7 12l6-19z" class="N"></path><defs><linearGradient id="Ct" x1="615.668" y1="727.613" x2="626.344" y2="783.695" xlink:href="#B"><stop offset="0" stop-color="#c1c1bd"></stop><stop offset="1" stop-color="#f3f2f0"></stop></linearGradient></defs><path fill="url(#Ct)" d="M608 702c3 7 5 14 7 21 2 8 8 29 15 33h1c1 5 3 10 5 14l6 6 4 4v2h-7l-1-1c-1 0-2-1-4-1l-1-1-7-4a57.31 57.31 0 0 1-11-11c-2-3-5-4-6-7l-3-3c-5-6-6-15-6-23 0-1 1-2 1-3l1-2h0c1-5 0-10 0-14 1-2 1-2 1-3v-2h1c0 1 0 2 1 3l1-1 1-2-2-2c1-2 1-2 3-3z"></path><path d="M642 776l4 4v2h-7l-1-1c-1 0-2-1-4-1h3l1 1c1 0 0-1 1 0h1c1 0 1-2 2-3v-1-1z" class="Z"></path><defs><linearGradient id="Cu" x1="602.889" y1="715.156" x2="605.88" y2="726.903" xlink:href="#B"><stop offset="0" stop-color="#a3a19e"></stop><stop offset="1" stop-color="#c3c2bf"></stop></linearGradient></defs><path fill="url(#Cu)" d="M603 709v2l1 1 4 15v3 1 3l-1-1v-1-1-1l-1 1-1-5h-3c1-5 0-10 0-14 1-2 1-2 1-3z"></path><path d="M608 702c3 7 5 14 7 21 2 8 8 29 15 33h1c1 5 3 10 5 14h-1c-3-1-5-2-6-3l-2-1c-1-1-3-2-4-4-5-6-10-14-12-23l-3-9v-3l-4-15-1-1v-2-2h1c0 1 0 2 1 3l1-1 1-2-2-2c1-2 1-2 3-3z" class="V"></path><defs><linearGradient id="Cv" x1="604.292" y1="704.638" x2="610.552" y2="712.564" xlink:href="#B"><stop offset="0" stop-color="#91908d"></stop><stop offset="1" stop-color="#bcbbb9"></stop></linearGradient></defs><path fill="url(#Cv)" d="M608 702c3 7 5 14 7 21h-1c-1-1-1-3-2-5h-1v-1h-1c0-1 0 0 1-1h1l-1-1h-1l-1 1v1h-1 0-1-1c0 2 1 3 1 5 1 1 1 3 1 5l-4-15-1-1v-2-2h1c0 1 0 2 1 3l1-1 1-2-2-2c1-2 1-2 3-3z"></path><path d="M603 711l1-1 1 1h1 0l-1 1h-1l-1-1z" class="E"></path><defs><linearGradient id="Cw" x1="396.709" y1="781.609" x2="365.253" y2="766.513" xlink:href="#B"><stop offset="0" stop-color="#a1a19d"></stop><stop offset="1" stop-color="#efedeb"></stop></linearGradient></defs><path fill="url(#Cw)" d="M416 713c1-3 0-8 1-10 1 1 1 1 1 2-1 2 0 3 0 5v-3c1 1 0 3 0 5l1 10c-1 8 0 16-2 24-1 10-4 19-9 28-3 4-5 8-8 12-2 2-4 4-6 5l-1 1c1 3 1 9 4 11h1l1 1h-2c-4-2-4-8-6-12 0-2-1-5-2-7l-4 3c-6 4-13 6-20 5-4-1-6-3-8-7h2v-2c-1-2-1-6 1-8 1-1 2-1 3-1 1 2 2 5 4 6 2 2 3 2 5 1h1c-2 0-2 0-3-1 1-3 4-6 6-8l3-2s0-1 1-1l3-1s1-1 2-1h0l2-2c2-1 4-3 6-5 0 1-1 2 0 3l1 2v3c1-1 1-1 2-1l2-2c1-1 2-2 2-3 2-3 7-7 10-10 4-10 4-20 5-30v-7l1-2h0v-1z"></path><path d="M400 763c2-3 7-7 10-10-1 5-3 11-6 16l-1-2 1-3c1-2 1-4 2-6-1 2-3 4-5 5h-1z" class="J"></path><path d="M379 771s0-1 1-1l3-1s1-1 2-1h0l2-2c2-1 4-3 6-5 0 1-1 2 0 3l1 2v3c1-1 1-1 2-1l-6 6c-2 1-3 2-4 3h-1c-3 2-7 4-10 5h-2c-2 0-2 0-3-1 1-3 4-6 6-8l3-2z" class="b"></path><path d="M415 716l1-2c0 12 0 25-4 36-1 5-3 10-5 14 0 1-1 4-1 5 1 4-3 9-5 13-1 1-1 2-1 4-2 2-4 4-6 5l-1 1c1 3 1 9 4 11h1l1 1h-2c-4-2-4-8-6-12 0-2-1-5-2-7l-4 3c-6 4-13 6-20 5-4-1-6-3-8-7h2l2 3c3 2 6 3 9 3 9-1 14-6 20-12l2 10c4-7 9-14 12-21 3-5 5-11 6-16 4-10 4-20 5-30v-7z" class="T"></path><path d="M394 791c1-4 3-7 5-10 2-4 4-8 7-12 1 4-3 9-5 13-1 1-1 2-1 4-2 2-4 4-6 5z" class="E"></path><defs><linearGradient id="Cx" x1="406.514" y1="742.455" x2="417.699" y2="746.306" xlink:href="#B"><stop offset="0" stop-color="#a1a19d"></stop><stop offset="1" stop-color="#cdcbc8"></stop></linearGradient></defs><path fill="url(#Cx)" d="M416 713c1-3 0-8 1-10 1 1 1 1 1 2-1 2 0 3 0 5v-3c1 1 0 3 0 5l1 10c-1 8 0 16-2 24-1 10-4 19-9 28-3 4-5 8-8 12 0-2 0-3 1-4 2-4 6-9 5-13 0-1 1-4 1-5 2-4 4-9 5-14 4-11 4-24 4-36h0v-1z"></path><defs><linearGradient id="Cy" x1="473.255" y1="446.789" x2="493.065" y2="440.529" xlink:href="#B"><stop offset="0" stop-color="#a9a8a4"></stop><stop offset="1" stop-color="#e3e3dd"></stop></linearGradient></defs><path fill="url(#Cy)" d="M473 385h1c2 2 4 4 6 7l1 1 1 1c1 2 2 3 2 5h1c3 5 4 12 5 18v3c1 2 1 5 0 7l-1 1v5h1v2h3c1 1 2 3 2 5h-1c0-1 0-4-1-4-1 3 1 9 2 13 1 9 1 19 2 28v1c-1 2-1 4-1 6v12l-6-3c-1-1-1-1-2-1v-3c-1-22-4-43-17-61l-6-7v-1c-1-4-1-7-1-11h2l10 11c-2-6-6-10-10-15l-4-3c0-2 0-3-1-4v-1c0-2-2-7-1-9 1 1 3 2 4 3 11 9 19 22 26 34-1-9-2-18-6-26-3-5-7-9-11-14z"></path><path d="M460 388c1 1 3 2 4 3 0 2 1 4 0 6 0 3 1 6 2 8l-4-3c0-2 0-3-1-4v-1c0-2-2-7-1-9z" class="J"></path><defs><linearGradient id="Cz" x1="562.593" y1="716.535" x2="588.35" y2="761.492" xlink:href="#B"><stop offset="0" stop-color="#3e3e3d"></stop><stop offset="1" stop-color="#878886"></stop></linearGradient></defs><path fill="url(#Cz)" d="M580 688c1 6 0 14 0 20 0 3 1 6 0 9h0v15 16l-1 15c-1 5-3 11-3 16 1 1 1 1 1 3 2 4 5 11 9 14l1 1c3 4 3 8 2 14 0 1-1 3-1 4l-2 4-1 1-1-1c-1 1-1 1-3 1l-6-8h0c-1-2-2-3-3-4l2-2c-3-3-6-7-8-11-2-2-3-9-4-12 0-3 0-6 2-9v-1l2-1v-1c2 0 3 0 5 1h0v-2l-3-7-1-8c0-1-1-4-1-6l-1-10c0-2 0-4 1-6v-4-10l1 6v-1-2l1-1v-3c0-1 0-1 1-2v-2l1-2v-2l1-2c0-1 0-3 1-4l1 1c-1 1-1 0-1 1h1c0-1 0-1 1-2h0v6l1 1c1-2 1-2 3-3v1l1-1c1 1 0 1 0 3h1l-1-16h0v-1l1-6z"></path><defs><linearGradient id="DA" x1="559.736" y1="720.418" x2="579.264" y2="745.082" xlink:href="#B"><stop offset="0" stop-color="#323333"></stop><stop offset="1" stop-color="#6f6f6e"></stop></linearGradient></defs><path fill="url(#DA)" d="M568 718c0-1 0-1 1-2v-2l1-2v-2l1-2c0-1 0-3 1-4l1 1c-1 1-1 0-1 1h1c0-1 0-1 1-2h0v6c0 1-1 3-1 4v2 3l-1 1c0 1 1 2 0 3v1c0 1 0 2-1 3-1 3-1 6-2 9v7 8c0 2 1 3-1 6-1-5-1-11-2-16v-8-4-10l1 6v-1-2l1-1v-3z"></path><path d="M566 719l1 6v-1-2l1-1v-3c1 4 1 10-1 13-1 3 0 7-1 10v-8-4-10z" class="O"></path><path d="M568 757c1 6 4 11 6 16 3-13 4-25 5-39v-13c0-1 0-3 1-4h0v15 16l-1 15c-1 5-3 11-3 16h0c-1-1-1-1-1-2-2 1-3 3-3 5l-1 1h0c0 4 2 9 4 12 2 2 3 3 5 4v1c-4-3-7-6-8-11-3-6 0-9 0-15-1 2-3 4-4 6h0c1-2 2-3 3-5h0v-2c-2-1-3-1-5-1v-1c2 0 3 0 5 1h0v-2l-3-7-1-8c0-1-1-4-1-6l-1-10c0-2 0-4 1-6v8c1 5 1 11 2 16z" class="T"></path><defs><linearGradient id="DB" x1="566.87" y1="795.074" x2="589.163" y2="796.343" xlink:href="#B"><stop offset="0" stop-color="#a9a8a6"></stop><stop offset="1" stop-color="#cbcbc4"></stop></linearGradient></defs><path fill="url(#DB)" d="M566 772c2 0 3 0 5 1v2h0c-1 2-2 3-3 5h0c1-2 3-4 4-6 0 6-3 9 0 15 1 5 4 8 8 11v-1c-2-1-3-2-5-4-2-3-4-8-4-12h0l1-1c0-2 1-4 3-5 0 1 0 1 1 2h0c1 1 1 1 1 3 2 4 5 11 9 14l1 1c3 4 3 8 2 14 0 1-1 3-1 4l-2 4-1 1-1-1c-1 1-1 1-3 1l-6-8h0c-1-2-2-3-3-4l2-2c-3-3-6-7-8-11-2-2-3-9-4-12 0-3 0-6 2-9v-1l2-1z"></path><path d="M566 772c2 0 3 0 5 1v2h0c-1 2-2 3-3 5h0v2c-1 2-1 4-1 5 1 3 3 5 1 8 1 1 2 3 3 4 1 3 5 7 8 9 3 1 6 4 8 6l1 1-2 4-1 1-1-1c-1 1-1 1-3 1l-6-8h0c-1-2-2-3-3-4l2-2c-3-3-6-7-8-11-2-2-3-9-4-12 0-3 0-6 2-9v-1l2-1z" class="g"></path><path d="M566 772c2 0 3 0 5 1v2h0c-1 2-2 3-3 5-2 2-2 4-3 7h0c-1-4-2-9-1-13v-1l2-1z" class="G"></path><path d="M566 795c2 1 3 3 4 5 1 1 2 3 4 5 2 1 4 3 6 4 1 2 5 5 7 5l1 1-2 4-1 1-1-1c-1 1-1 1-3 1l-6-8h0c-1-2-2-3-3-4l2-2c-3-3-6-7-8-11z" class="E"></path><path d="M574 806l3 5h0-1l-1 1h0c-1-2-2-3-3-4l2-2z" class="R"></path><path d="M577 811c3 2 5 5 7 8-1 1-1 1-3 1l-6-8 1-1h1 0z" class="D"></path><defs><linearGradient id="DC" x1="397.599" y1="718.614" x2="428.573" y2="788.747" xlink:href="#B"><stop offset="0" stop-color="#cfcfca"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#DC)" d="M419 722l1-3-1-1v-8c2-2 4-1 6-1 0 3 0 5 1 8v3h1c0-1 0-2 1-3v-2l-1-1v-3c1-1 3 0 5 0 0 5 0 9 1 14 0 3-1 6 0 9l3 31 2 12c1 4 1 5-1 9h1c0 1-1 2-2 3-2 1-3 2-5 3v-1h-1l1-1c-2 0-3 1-4 0l-1-6-3-23h0c-4 13-12 24-19 35 0 3 0 4-2 6v1c-1 1-2 1-3 1l-1-1h-1c-3-2-3-8-4-11l1-1c2-1 4-3 6-5 3-4 5-8 8-12 5-9 8-18 9-28 2-8 1-16 2-24z"></path><path d="M398 803c2-2 4-4 6-7 0 3 0 4-2 6v1c-1 1-2 1-3 1l-1-1z" class="F"></path><path d="M436 765l2 12c1 4 1 5-1 9h1c0 1-1 2-2 3-2 1-3 2-5 3v-1h-1l1-1c-2 0-3 1-4 0l-1-6c1 1 1 2 1 3s0 1 1 2h0l1-1 1-2c0-1 0-1 1-1v-2c1-1 2-3 3-4s1-1 1-2 1-2 1-3v-1h0v-1c-1-2 0-4 0-6v-1z" class="c"></path><path d="M438 777c1 4 1 5-1 9h1c0 1-1 2-2 3-2 1-3 2-5 3v-1h-1l1-1c1-1 1-2 2-3h0c2-2 3-4 3-6 1-2 1-3 2-4z" class="I"></path><path d="M676 122h5c4 0 11-1 14 1h4l1 1c-2 0-4 1-6 1-5 0-11 2-17 4-13 4-26 9-38 15-11 7-22 14-32 22-3 2-5 4-8 7-1 1-2 3-4 3-3 1-6 0-9 0h-9l-6 1c9-20 32-35 51-43 2 0 4-1 6-1 3 0 7 0 11-1v2c1-1 3-2 4-2h1 0 3 0c2 0 4-1 5-2l9-3c4-1 7-3 11-3l4-2z" class="Z"></path><path d="M577 176l1-1h-1l1-1 3 1c1-1 3-3 4-5l3-3v1c-1 2-1 3-3 5h1 0c1-1 2-1 4-1v-1c1 0 2 0 2-1 2 0 3-1 5-2v1c-1 2-6 4-5 5v-1c2 0 3 0 5 1 1-1 1-1 2-1-1 1-2 3-4 3-3 1-6 0-9 0h-9z" class="I"></path><path d="M676 122h5c4 0 11-1 14 1h4l1 1c-2 0-4 1-6 1-5 0-11 2-17 4-4-3-10 0-15-1l-47 20v-1c0-1 1-2 2-2h1v-1h-2s2-1 3-1c3-2 5-4 8-5l12-4c1-1 3-2 4-2h1 0 3 0c2 0 4-1 5-2l9-3c4-1 7-3 11-3l4-2z" class="K"></path><path d="M662 128l8-2c4-1 8-2 12-2h5c2 0 5 0 7 1-5 0-11 2-17 4-4-3-10 0-15-1z" class="I"></path><path d="M643 132h1 0 3 0l-32 15c0-1 1-2 2-2h1v-1h-2s2-1 3-1c3-2 5-4 8-5l12-4c1-1 3-2 4-2z" class="d"></path><defs><linearGradient id="DD" x1="611.571" y1="160.539" x2="596.294" y2="146.454" xlink:href="#B"><stop offset="0" stop-color="#c4c3bd"></stop><stop offset="1" stop-color="#e6e5e3"></stop></linearGradient></defs><path fill="url(#DD)" d="M622 134c2 0 4-1 6-1 3 0 7 0 11-1v2l-12 4c-3 1-5 3-8 5-1 0-3 1-3 1h2v1h-1c-1 0-2 1-2 2v1c-5 3-11 5-16 8-3 2-4 3-6 5l-1 2c-1 1-1 2-2 2 0 1-1 2-2 3v-1l-3 3c-1 2-3 4-4 5l-3-1-1 1h1l-1 1-6 1c9-20 32-35 51-43z"></path><path d="M292 123c44-5 90-4 129 17l-1 1c8 4 16 9 22 15s11 13 15 21h-1l-12-1h-6c-3 0-4-2-5-3-1 0-1-1-2-2-3-3-6-6-10-8l-30-17-16-8h0l-1 1c-10-4-20-8-30-11-15-3-30-4-45-3l-6-1-1-1z" class="M"></path><defs><linearGradient id="DE" x1="333.603" y1="123.525" x2="361.278" y2="129.523" xlink:href="#B"><stop offset="0" stop-color="#8b8a89"></stop><stop offset="1" stop-color="#bdbcba"></stop></linearGradient></defs><path fill="url(#DE)" d="M375 138c-2-1-6-2-8-3-7-3-14-6-21-7-7-2-14-2-20-4l12-1 24 5v1l-1 1-1 1c3 2 7 3 11 5 2 1 3 0 4 2h0z"></path><path d="M338 123c7-2 11 0 18 0h2 1c6 0 13 2 19 3 2 1 5 1 7 2-1 1-1 1 0 2 0 0 2 1 3 1l1 2h0c1 1 1 2 0 3l13 5c5 2 13 5 18 8h-3l-7-4-6-2-5-2c-4-1-8-4-13-4l-24-9-24-5z" class="X"></path><path d="M385 130s2 1 3 1l1 2h0c1 1 1 2 0 3l-7-3c1 0 1-1 2-1h1l-1-1 1-1z" class="E"></path><defs><linearGradient id="DF" x1="367.726" y1="127.432" x2="383.971" y2="129.259" xlink:href="#B"><stop offset="0" stop-color="#7e7e7b"></stop><stop offset="1" stop-color="#9b9b99"></stop></linearGradient></defs><path fill="url(#DF)" d="M359 123c6 0 13 2 19 3 2 1 5 1 7 2-1 1-1 1 0 2l-1 1 1 1h-1c-1 0-1 1-2 1s-1 0-2-1h-2l-4-1c-2-1-3-2-6-2l-1-1c-2 0-4-1-6-1-1-1-1-1-2-1l1-1 8 2 1-1c-2 0-5-1-6-2-2 0-3 0-4-1z"></path><defs><linearGradient id="DG" x1="385.146" y1="126.306" x2="433.663" y2="151.645" xlink:href="#B"><stop offset="0" stop-color="#93928f"></stop><stop offset="1" stop-color="#cececb"></stop></linearGradient></defs><path fill="url(#DG)" d="M385 128c12 3 24 7 35 13 8 4 16 9 22 15-3 0-5-3-7-4h-1c-2 0-6-2-8-3 2 2 6 5 9 7v1c-5-3-10-6-15-8-5-3-13-6-18-8l-13-5c1-1 1-2 0-3h0l-1-2c-1 0-3-1-3-1-1-1-1-1 0-2z"></path><defs><linearGradient id="DH" x1="389.935" y1="145.306" x2="432.565" y2="142.194" xlink:href="#B"><stop offset="0" stop-color="#b7b9b1"></stop><stop offset="1" stop-color="#eeebeb"></stop></linearGradient></defs><path fill="url(#DH)" d="M388 131c8 2 15 6 23 10 5 2 10 4 15 8h0c2 2 6 5 9 7v1c-5-3-10-6-15-8-5-3-13-6-18-8l-13-5c1-1 1-2 0-3h0l-1-2z"></path><path d="M362 128l24 9c5 0 9 3 13 4l5 2 6 2 7 4h3c5 2 10 5 15 8v-1c-3-2-7-5-9-7 2 1 6 3 8 3h1c2 1 4 4 7 4 6 6 11 13 15 21h-1l-12-1h-6c-3 0-4-2-5-3-1 0-1-1-2-2-3-3-6-6-10-8l-30-17-16-8c-1-2-2-1-4-2-4-2-8-3-11-5l1-1 1-1v-1z" class="c"></path><path d="M433 173c1 0 2 0 3 1l1-1c1 0 2 0 3 1h3l1-1 2 2h-1l-1 1h-6c-3 0-4-2-5-3z" class="S"></path><path d="M362 128l24 9h-3c-5-1-10-4-15-4h-1v-1h0c2 0 4 0 6 1h2c-4-2-10-3-13-4v-1z" class="d"></path><path d="M386 137c5 0 9 3 13 4l5 2 6 2c0 2 1 3 3 3v1c0 1 0 1 2 1h0c0 1 1 1 1 1v1c-11-5-22-11-33-15h3z" class="J"></path><defs><linearGradient id="DI" x1="413.42" y1="146.529" x2="433.779" y2="161.195" xlink:href="#B"><stop offset="0" stop-color="#91918c"></stop><stop offset="1" stop-color="#b4b2ae"></stop></linearGradient></defs><path fill="url(#DI)" d="M410 145l7 4h3c5 2 10 5 15 8l3 3h-1c-1 1-2 1-3 2l-6-3c-4-2-8-4-12-7v-1s-1 0-1-1h0c-2 0-2 0-2-1v-1c-2 0-3-1-3-3z"></path><path d="M611 674h0c-1-2 0-4 0-6v-3l1-1c1 1 3 2 4 2l1 1c5 3 11 10 15 14 2 3 4 7 6 10 2 2 4 4 5 6h-1l-3-3-3-4h-1c0 1 3 5 4 6l4 3c1 0 0 0 1-1 1 1 1 2 2 2v1c1 1 1 2 2 4v1c1 1 1 1 2 3h0v-2c1 0 1 1 2 1 3 3 3 4 3 8v-4h1c0 3 0 6-2 9-1 1-3 3-6 3h-8c-1 6-3 11-4 16a104.13 104.13 0 0 0-5 12v4h-1c-7-4-13-25-15-33-2-7-4-14-7-21l-4-8 1-1-3-5c-1-3-2-5-3-7h-1c-1-2-1-5-1-7h3c1 2 4 5 5 6 1 0 1-1 1-2l2-2c-1-1-1-2-1-3l4 4h1l-1-3z" class="N"></path><path d="M621 675c2 1 3 4 4 6l-1 1v-2h-1v2c-1 0-1-1-1-1l-1-6z" class="B"></path><path d="M621 707v-1-2l1-1c1 2 1 5 1 7h-1 0c-1 9-1 16-2 24l-2-7c2-3 1-9 1-12 0-1 0-2 1-3h1v-1-4z" class="g"></path><defs><linearGradient id="DJ" x1="623.989" y1="692.644" x2="622.704" y2="701.457" xlink:href="#B"><stop offset="0" stop-color="#5f5f5d"></stop><stop offset="1" stop-color="#7c7b79"></stop></linearGradient></defs><path fill="url(#DJ)" d="M623 682v-2h1v2l1-1c1 2 2 3 3 5 0 1 1 2 2 2l-1 2c1 2 1 2 1 4l-1 1v2h-1l-1-1h-1l1 2-1 1-1 1 1 1c-1 0-2 2-2 4-1 1 0 4 0 5-1 2-1 3-1 4l-1-4h1c0-2 0-5-1-7l-1 1v2 1-4-11c2-3 3-7 2-10z"></path><path d="M625 700v-1-1l1-3h0v1l1 2-1 1-1 1z" class="U"></path><defs><linearGradient id="DK" x1="625.774" y1="681.861" x2="626.824" y2="691.465" xlink:href="#B"><stop offset="0" stop-color="#232423"></stop><stop offset="1" stop-color="#474747"></stop></linearGradient></defs><path fill="url(#DK)" d="M625 681c1 2 2 3 3 5 0 1 1 2 2 2l-1 2c1 2 1 2 1 4l-1 1v2h-1l-1-1v-2l-1-2h-1c0 1 0 1-1 1v-11l1-1z"></path><path d="M629 690c1 2 1 2 1 4l-1 1v2h-1l-1-1v-2h1c0-1 0-2 1-3v-1z" class="X"></path><path d="M630 688l9 11 3 3-2 6c-1-1-2-1-3 0l-1 2h-1v-1-1-3c-1 0-2 0-3 1v1c0 2-1 3-1 4s-1 1-1 2c-1-1-1-2-1-3-1-2-1-4-2-6 0-1-1-2-1-3l-1-1 1-1 1-1-1-2h1l1 1h1v-2l1-1c0-2 0-2-1-4l1-2z" class="g"></path><path d="M630 696l2-2c1 0 1 0 2 1v1c-1 1-1 2-1 3-1 0-1 0-1-1h-1v6c0 1 0 2-1 4v-12z" class="K"></path><path d="M631 704c1-1 2-1 3-3v-2c1 2 1 3 2 5 0 1 0 3-1 5v-1-3c-1 0-2 0-3 1v1c0 2-1 3-1 4s-1 1-1 2c-1-1-1-2-1-3l1-2c1-2 1-3 1-4z" class="G"></path><path d="M630 688l9 11c-1 1-2 1-2 2s0 1-1 1v-3c-1-1-1-2-2-3v-1c-1-1-1-1-2-1l-2 2v-2c0-2 0-2-1-4l1-2z" class="P"></path><defs><linearGradient id="DL" x1="627.711" y1="697.965" x2="628.16" y2="707.042" xlink:href="#B"><stop offset="0" stop-color="#6f6f6d"></stop><stop offset="1" stop-color="#898a85"></stop></linearGradient></defs><path fill="url(#DL)" d="M630 694v2 12l-1 2c-1-2-1-4-2-6 0-1-1-2-1-3l-1-1 1-1 1-1-1-2h1l1 1h1v-2l1-1z"></path><path d="M640 719c2-5 1-10 4-14v2l1-2h1l1 2 1-1c1 1 1 1 2 3h0v-2c1 0 1 1 2 1 3 3 3 4 3 8-1 2-2 5-4 6-3 1-7 1-10 0l-1-1v-2z" class="G"></path><path d="M640 719c2-5 1-10 4-14v2l1-2h1l1 2 1-1v5l-1 1v-4l-1 1-1 12h-1c-1-1-1-2-1-3 0 0-1 0-2 1h-1z" class="Q"></path><path d="M611 674h0c-1-2 0-4 0-6v-3l1-1c1 1 3 2 4 2l1 1 4 4v4l1 6s0 1 1 1c1 3 0 7-2 10v11 4 4h-1c0-4-2-8-2-12-2-8-4-15-6-22l-1-3z" class="f"></path><path d="M622 681s0 1 1 1c1 3 0 7-2 10l1-11z" class="W"></path><defs><linearGradient id="DM" x1="612.106" y1="691.694" x2="617.031" y2="725.641" xlink:href="#B"><stop offset="0" stop-color="#4e4e4d"></stop><stop offset="1" stop-color="#848582"></stop></linearGradient></defs><path fill="url(#DM)" d="M607 673l4 4h1l6 22c0 4 2 8 2 12h1v1h-1c-1 1-1 2-1 3 0 3 1 9-1 12l-7-23-6-11-3-5c-1-3-2-5-3-7h-1c-1-2-1-5-1-7h3c1 2 4 5 5 6 1 0 1-1 1-2l2-2c-1-1-1-2-1-3z"></path><defs><linearGradient id="DN" x1="604.384" y1="683.025" x2="617.976" y2="688.23" xlink:href="#B"><stop offset="0" stop-color="#81827f"></stop><stop offset="1" stop-color="#9d9d9a"></stop></linearGradient></defs><path fill="url(#DN)" d="M607 673l4 4c3 9 5 17 6 26h0v-1l-4-8c-2-5-5-9-8-14 1 0 1-1 1-2l2-2c-1-1-1-2-1-3z"></path><defs><linearGradient id="DO" x1="596.539" y1="687.73" x2="611.651" y2="689.998" xlink:href="#B"><stop offset="0" stop-color="#2e2f2f"></stop><stop offset="1" stop-color="#515150"></stop></linearGradient></defs><path fill="url(#DO)" d="M599 681h-1c-1-2-1-5-1-7h3c1 2 4 5 5 6 3 5 6 9 8 14-1 1 0 1-1 1l-1-3h-1v1h-1c1 4 2 7 2 11l-6-11-3-5c-1-3-2-5-3-7z"></path><path d="M599 681h1 1c1 1 2 3 3 5l-2 2c-1-3-2-5-3-7z" class="D"></path><defs><linearGradient id="DP" x1="626.094" y1="700.878" x2="631.292" y2="747.578" xlink:href="#B"><stop offset="0" stop-color="#93938f"></stop><stop offset="1" stop-color="#c6c6c0"></stop></linearGradient></defs><path fill="url(#DP)" d="M623 714c0-1 0-2 1-4 0-1-1-4 0-5 0-2 1-4 2-4 0 1 1 2 1 3 1 2 1 4 2 6 0 1 0 2 1 3 0-1 1-1 1-2s1-2 1-4v-1c1-1 2-1 3-1v3 1 1h1l1-2c1-1 2-1 3 0l-3 16h0c-1 10-4 18-9 26-3-4-6-9-7-14 0-4 1-8 1-11 1-4 1-8 1-11z"></path><path d="M635 710h1l1-2c1-1 2-1 3 0l-3 16h0c0-1 0-1-1-1v2-1c-2-3-2-11-1-14z" class="G"></path><path d="M582 711l1-1h0c3-4 9-6 14-8l1 20c0 2 1 5 0 6 0 2-1 2-1 3l1 1v2c-1-1-1-3-2-4l2 17c4 14 8 26 17 38 1 2 3 5 5 7h2c1 4-1 7-3 10-1 0-1 1-1 0-2 0-3-2-5-3h0l1 2-1 1-3-2c-1 0-3 2-4 4-2 2-7 6-8 8v4h-2 0c0-2 0-4 1-6-1-1-1-2-1-2-2-1-2-2-4-1l-1 1c-1 4-1 8-4 12h0l-1-1 2-4c0-1 1-3 1-4 1-6 1-10-2-14l-1-1c-4-3-7-10-9-14 0-2 0-2-1-3 0-5 2-11 3-16l1-15v-16-15c1-1 1-3 1-5 1 2 0 3 1 5h0c1-2 1-4 0-6z" class="V"></path><path d="M587 790c1-2 1-5 2-8 1-8 2-15 2-23l2 7-1 1v4 2c0 2-1 4-1 5 0 5-2 11-2 16 1 2 1 4 2 6v8c-1 4-1 8-4 12h0l-1-1 2-4c0-1 1-3 1-4 1-6 1-10-2-14v-7z" class="T"></path><path d="M576 779c0-5 2-11 3-16l1-15h1c0 3 0 6-1 9v4 1l1-2h1c-1 2-1 4-1 7 0 2 1 8-1 10l-1 1 1 2c0 1 0 1-1 2v1 1c2 3 4 9 7 10 0-2 0-2 1-4v7l-1-1c-4-3-7-10-9-14 0-2 0-2-1-3z" class="S"></path><path d="M593 766c3 9 9 18 15 25 1 3 4 5 5 8h0l1 2-1 1-3-2c-1 0-3 2-4 4-2 2-7 6-8 8v4h-2 0c0-2 0-4 1-6v-1l4-4 5-6-3-6c-3-5-5-9-8-12l-2-2h0l-2-1c0-1 1-3 1-5v-2-4l1-1z" class="R"></path><path d="M592 771v-2l1-1v1l6 12c2 3 4 6 5 10-5-4-8-13-12-18v-2z" class="g"></path><path d="M592 773c4 5 7 14 12 18 1 1 4 4 4 6 0 1-1 1-2 2l-3-6c-3-5-5-9-8-12l-2-2h0l-2-1c0-1 1-3 1-5z" class="L"></path><defs><linearGradient id="DQ" x1="601.101" y1="782.075" x2="590.657" y2="805.399" xlink:href="#B"><stop offset="0" stop-color="#979690"></stop><stop offset="1" stop-color="#b8b7b5"></stop></linearGradient></defs><path fill="url(#DQ)" d="M591 778l2 1h0l2 2c3 3 5 7 8 12l3 6-5 6-4 4v1c-1-1-1-2-1-2-2-1-2-2-4-1l-1 1v-8c-1-2-1-4-2-6 0-5 2-11 2-16z"></path><path d="M597 809v-2c1-4 1-8 0-12 1 2 3 5 3 7v2l1 1-4 4z" class="J"></path><defs><linearGradient id="DR" x1="586.223" y1="705.053" x2="595.772" y2="734.069" xlink:href="#B"><stop offset="0" stop-color="#7a7a78"></stop><stop offset="1" stop-color="#c9c9c3"></stop></linearGradient></defs><path fill="url(#DR)" d="M582 711l1-1h0c3-4 9-6 14-8l1 20c0 2 1 5 0 6 0 2-1 2-1 3l1 1v2c-1-1-1-3-2-4l2 17c-2-3-3-6-3-10h-1l-1 3c-1-3-1-5-1-8h0l-1 1-1 3v-5h0c-1 1-1 3-1 5-1 2 0 5-1 7v-4h-1c-1-1-1-3-1-3l-1-4h0c0 1 0 2-1 3v6c-1 2 0 5-1 8-1-1 0-10 0-12l-1 1c0 1 0 3-1 5v5h-1v-16-15c1-1 1-3 1-5 1 2 0 3 1 5h0c1-2 1-4 0-6z"></path><path d="M587 723l1 1c0 2 1 5 0 8h-1v-9z" class="I"></path><path d="M580 717c1-1 1-3 1-5 1 2 0 3 1 5l-2 15v-15z" class="L"></path><defs><linearGradient id="DS" x1="315.881" y1="174.041" x2="316.183" y2="155.566" xlink:href="#B"><stop offset="0" stop-color="#747473"></stop><stop offset="1" stop-color="#8f8e8a"></stop></linearGradient></defs><path fill="url(#DS)" d="M275 150c2-1 4-1 5-2l4 1c1 1 2 1 3 1 6-1 12-1 17-1h5c29 1 58 10 81 27h-42-4-6l-5-3c-1 1-3 1-4 1h-5-3l5 1-8 1c-5 0-10-1-15-1l-9-2c-5-1-11-2-17-3l-1 1h5c-1 1-4 0-5 1h4c-5 0-9 0-14 1l-2-1 6-5c1-2 0-2 0-4h-2c-4 0-12-1-16-3h4l5-3 2-1h0c1-2 10-5 13-5l-1-1z"></path><path d="M281 154l3 1-2 1-3 1v-1h0c1 0 2 0 3-1-5 1-13 2-18 4 1 1 2 2 3 2h0c-3 0-5 0-7-1l1-1c7-2 13-3 20-5z" class="Q"></path><path d="M309 162c13 1 24 7 35 14h-6l-5-3c-1-1-2-1-4-2-6-3-11-5-17-6-2-1-5-1-7-2h4v-1z" class="F"></path><path d="M270 163h0c3 1 6 0 9-1s7-1 11-1c6-1 13 0 19 1v1h-4c-9-1-24-3-32 3-1 1-1 2-2 4h6l-1 1h5c-1 1-4 0-5 1h4c-5 0-9 0-14 1l-2-1 6-5c1-2 0-2 0-4z" class="N"></path><path d="M277 170h-6c1-2 1-3 2-4 8-6 23-4 32-3 2 1 5 1 7 2 6 1 11 3 17 6 2 1 3 1 4 2-1 1-3 1-4 1h-5-3l5 1-8 1c-5 0-10-1-15-1l-9-2c-5-1-11-2-17-3z" class="K"></path><path d="M303 175v-1c-2-1-6-2-9-3h0c3 0 5 1 8 2 7 1 15 0 22 1h-3l5 1-8 1c-5 0-10-1-15-1z" class="P"></path><path d="M286 165l22 3h1l11 3h-8c-5 0-11-1-16-2-3-1-5-1-8-1l-2-1v-1-1z" class="X"></path><path d="M312 165c6 1 11 3 17 6 2 1 3 1 4 2-1 1-3 1-4 1-2-2-6-2-9-2h0c-2 1-5 0-8-1h8l-11-3-1-1h-1l1-1c1 0 1 1 2 1 1-1 1-1 2-1h0v-1h0z" class="H"></path><path d="M320 172v-1c3-1 6 1 9 0 2 1 3 1 4 2-1 1-3 1-4 1-2-2-6-2-9-2z" class="W"></path><defs><linearGradient id="DT" x1="272.762" y1="165.314" x2="311.751" y2="168.523" xlink:href="#B"><stop offset="0" stop-color="#3c3c3c"></stop><stop offset="1" stop-color="#535453"></stop></linearGradient></defs><path fill="url(#DT)" d="M277 170h-6c1-2 1-3 2-4 8-6 23-4 32-3 2 1 5 1 7 2h0v1h0c-1 0-1 0-2 1-1 0-1-1-2-1l-1 1h1l1 1h-1l-22-3h-4l2 1v1c-1 0-2 0-3 1 3 0 9 1 11 3 1 0 2 1 2 2-5-1-11-2-17-3z"></path><path d="M281 168c-1-1-1 0-2-1 1-1 2-1 3-2l2 1v1c-1 0-2 0-3 1zm-6-18c2-1 4-1 5-2l4 1c1 1 2 1 3 1 6-1 12-1 17-1h5c29 1 58 10 81 27h-42c7-1 14-1 21-1-1-2-4-3-5-5h0c-2-1-3 0-5-1h-1l-2-1h-1c-1-1-1 0-3 0-3-2-7-3-11-4h1l-1-1c1 0 1 0 2-1h-1c-5 0-12-2-17-3-2-1-3-1-5-1-1-1-1-1-2-1-6-1-12-1-18-1l-7 1h-7 0c1-1 1-1 2-1-1 0-2-1-3 0h-3l2-1-3-1c-7 2-13 3-20 5l-1 1h-4l5-3 2-1h0c1-2 10-5 13-5l-1-1z" class="H"></path><path d="M364 170c2 1 4 1 6 2 1 0 1 0 2-1 2 0 5 2 8 2v1h-7v1h-4c-1-2-4-3-5-5z" class="X"></path><path d="M307 151c3 1 11 1 14 2l22 4c2 0 2 0 3 1h-1-6c-4-1-7-2-10-3l-17-2c-3-1-6-1-9-1l-10 1h-5v-1l19-1z" class="U"></path><path d="M292 154c15-1 29 0 43 4l-1 1c-2 0-4 0-6-1h-1c-1-1-1-1-2-1h-1-2c-2-1-2-1-3-1h-2c-2-1-6-1-8-1-1 0-6-1-7 0-1 0-2 1-2 1l-7 1h-7 0c1-1 1-1 2-1-1 0-2-1-3 0h-3l2-1c3 0 6 0 8-1z" class="L"></path><path d="M275 150c2-1 4-1 5-2l4 1c1 1 2 1 3 1 6-1 12-1 17-1h5v1c3 0 7 1 10 1l3 1-1 1c-3-1-11-1-14-2l-19 1v1h5l-1 1h0c-2 1-5 1-8 1l-3-1c-7 2-13 3-20 5l-1 1h-4l5-3 2-1h0c1-2 10-5 13-5l-1-1z" class="O"></path><path d="M281 154h2c-3-2-9 2-12 1l1-1c5-1 10-2 16-2v1h5l-1 1h0c-2 1-5 1-8 1l-3-1z" class="K"></path><path d="M287 150c6-1 12-1 17-1h5v1c3 0 7 1 10 1l3 1-1 1c-3-1-11-1-14-2-1-1-2-1-3-1-3 0-6 0-9 1h-9l-1-1h2z" class="e"></path><path d="M300 156s1-1 2-1c1-1 6 0 7 0 2 0 6 0 8 1h2c1 0 1 0 3 1h2 1c1 0 1 0 2 1h1c2 1 4 1 6 1l1-1 10 1c7 2 15 5 22 8 3 1 7 2 10 4h-1c-2 0-3-1-5-1-1 0-2-1-3-1h-1c2 1 3 2 5 2-1 1-1 1-2 1-2-1-4-1-6-2h0c-2-1-3 0-5-1h-1l-2-1h-1c-1-1-1 0-3 0-3-2-7-3-11-4h1l-1-1c1 0 1 0 2-1h-1c-5 0-12-2-17-3-2-1-3-1-5-1-1-1-1-1-2-1-6-1-12-1-18-1z" class="G"></path><path d="M367 169c-2 0-4-1-6-2s-4-1-6-2l1-1 7 3h4c3 1 7 2 10 4h-1c-2 0-3-1-5-1-1 0-2-1-3-1h-1z" class="U"></path><path d="M191 271l1 1c5 2 10 3 14 4 8 3 16 12 19 20 1 2 1 5 0 7-2 3-5 6-7 9-6 8-10 18-9 28-3 0-6-2-9-3-2-1-4-2-7-3s-6-3-9-5c-6-3-11-7-16-12l-5-5-1-1h0c-1-1-1-2-2-3 0-1 0 0-1-1v-1c-1-1-1-1-1-2l-2-2c0-1-1-2-2-4 1 0 0-2 0-3l-2-4v-1c-1-4-1-8-1-12l-1 1v-3-2c1 0 2 1 3 1l2-2c1 2 2 3 3 5l2-1c4 7 6 12 7 20 0 2 0 4 1 6 0 2 2 3 3 5v1l2 1v-1h1c3-1 5-5 7-6 2 0 2 0 4-1 1 0 2 1 3 0h-1l2-2v1 1h1c-1-1-1-3 0-5v-5-1-2-1l1-1 7 2h2c0-2-2-4-3-6l-5-11-1-1z" class="I"></path><path d="M213 300c0-1 0-1 2-2v1l1 1v2c-1 3-4 5-5 7l-2-1c1-2 1-3 1-4 1-1 1-1 1-2 1-1 1 0 1-1l1-1z" class="E"></path><path d="M155 273c1 2 2 3 3 5 2 4 3 8 4 13v4l-1-4c0-4-1-7-2-10-2-3-5-3-7-4l-1 1-1 1v-3-2c1 0 2 1 3 1l2-2z" class="O"></path><path d="M158 278l2-1c4 7 6 12 7 20 0 2 0 4 1 6 0 2 2 3 3 5v1c-1-1-2-3-3-4 0 0-1-1-1-2-2 0-2 0-2-2h-1c0-2-1-2-1-4h0c-1-2-1-1-1-2v-4c-1-5-2-9-4-13z" class="a"></path><defs><linearGradient id="DU" x1="208.159" y1="295.529" x2="208.404" y2="310.97" xlink:href="#B"><stop offset="0" stop-color="#90908b"></stop><stop offset="1" stop-color="#b6b6b1"></stop></linearGradient></defs><path fill="url(#DU)" d="M201 305c0-2 0-2 1-2 0 2 1 3 2 4h0c0-3 0-3 1-6 0-1 0-2 1-2 2-2 3-4 6-4l1 1v4l-1 1c0 1 0 0-1 1 0 1 0 1-1 2 0 1 0 2-1 4l2 1-3 2c0 1 0 1-1 2l-2-2-1-1s-1-2-1-3h-1l-1 1h0v-3z"></path><defs><linearGradient id="DV" x1="207.013" y1="292.032" x2="201.856" y2="304.281" xlink:href="#B"><stop offset="0" stop-color="#7f7e7b"></stop><stop offset="1" stop-color="#9b9b96"></stop></linearGradient></defs><path fill="url(#DV)" d="M200 290h4c1 0 2 0 3 1 1 0 2 0 3 1h1c2 0 3 1 4 3l1 1v1 3l-1-1v-1c-2 1-2 1-2 2v-4l-1-1c-3 0-4 2-6 4-1 0-1 1-1 2-1 3-1 3-1 6h0c-1-1-2-2-2-4-1 0-1 0-1 2l-1-1c-1 2 0 6-1 8v-12c1 0 1 0 1-1-1-1-1-2-1-3 1-2 1-3 1-4v-2h0z"></path><defs><linearGradient id="DW" x1="196.78" y1="291.584" x2="192.565" y2="314.075" xlink:href="#B"><stop offset="0" stop-color="#878683"></stop><stop offset="1" stop-color="#adaea8"></stop></linearGradient></defs><path fill="url(#DW)" d="M191 287l7 2 2 1h0v2c0 1 0 2-1 4 0 1 0 2 1 3 0 1 0 1-1 1v12h0l1 7c-4-5-9-10-10-17-1-1-1-3 0-5v-5-1-2-1l1-1z"></path><path d="M474 247c0-1-1-3-2-4l-1-1 1-1c1 1 3 1 4 1 1 1 3 1 4 1 4 5 7 11 9 17l3-1c1 2 1 5 2 7 0 2 0 5-1 7 0 4 1 7 0 10l-1 1-2-2c0 8 2 17-1 25l1 4v-1c0-1 1-1 1-2l-3 13c1 1 1 2 2 3-1 1-1 2-1 3-1 5-1 9 0 13l1 3 1 2c-1 2-2 4-1 7v1 1h0c-1 1-1 1-2 1 2 3 4 7 7 9l-2 2c-2-6-6-10-10-15-1-2-4-6-6-7l-1 2c-1 0-1-1-1-2 0 2-1 3-2 4-1 2-4 3-6 3h0-1-1c-2 1-6 0-8-2h-1c-1-5-2-9-4-14v-3l2 1c-1-3-1-5 0-8 1-2 0-6 0-8l-1-4c0-3 0-3 2-5 1 1 2 1 3 2l6 9c3-6 4-12 7-18v-4l5-17c0-1 1-4 1-6 1-5 2-13 0-18 0-3-2-5-3-8v-1z" class="I"></path><path d="M492 259c1 2 1 5 2 7 0 2 0 5-1 7-1-4-2-9-4-13l3-1z" class="R"></path><path d="M489 307l1 4v-1c0-1 1-1 1-2l-3 13c0 2-1 4-2 6-1 1-2 1-3 2-1 0-2-1-3-1v-3l-1-1c-1-1 0-1 0-2 2 1 1 0 3 0 0 2 0 3 1 4h1c2-3 3-5 3-9 0-1-1-2-1-3 3-2 3-4 3-7z" class="D"></path><path d="M476 280c1 1 1 2 1 3v-1l2-3v4 2c0 1 0 2-1 3l-1 4c-1 0-1 0-1 1-1 2-2 7-4 8h-1v-4l5-17z" class="Q"></path><defs><linearGradient id="DX" x1="467.226" y1="308.283" x2="473.274" y2="312.717" xlink:href="#B"><stop offset="0" stop-color="#878685"></stop><stop offset="1" stop-color="#a9a8a4"></stop></linearGradient></defs><path fill="url(#DX)" d="M471 301h1c2-1 3-6 4-8 0-1 0-1 1-1 0 2-2 5-2 7l1 1c0 1-1 3-1 4-1 3-2 7-3 10 0 3 0 5-1 8l-2-2h0v-3c-1 2-2 6-4 9 0-2 1-3 1-5h-1l-1 4v-6c3-6 4-12 7-18z"></path><path d="M481 267h0l1 5c1 4 1 8 1 13 0 2 1 5 1 8-1 1-1 5-1 6l-2 1c-1 1-1 4-2 6h-1v2 1c0 2 1 3 0 5 0-2 0-3-1-5v4 2c-1-2-2-3-2-5 1-1 0-2 0-3 1-1 1-2 1-3 1-1 0-2 0-3 1-1 1-1 1-2 1-1 0-2 0-3l1-1c0-1 1-1 1-1v-4c0-1 0-2 1-3 1-2 0-4 0-5 1-1 1-2 0-3v-3h1v1l1 1c1-2 0-1 0-2v-3h-1l-1-1c1-1 1-3 1-5z" class="E"></path><path d="M475 344l-2-10c0-4 0-11 3-14 2 0 2 1 3 2 0 1-1 1 0 2l1 1v3c1 0 2 1 3 1 1-1 2-1 3-2 1-2 2-4 2-6 1 1 1 2 2 3-1 1-1 2-1 3-1 5-1 9 0 13l1 3 1 2c-1 2-2 4-1 7v1 1h0c-1 1-1 1-2 1 2 3 4 7 7 9l-2 2c-2-6-6-10-10-15-1-2-4-6-6-7l-1 2c-1 0-1-1-1-2z" class="R"></path><path d="M479 324l1 1v3c1 0 2 1 3 1 1-1 2-1 3-2l-1 2c-1 3-3 8-6 9v-14z" class="G"></path><defs><linearGradient id="DY" x1="480.777" y1="334.403" x2="493.789" y2="344.649" xlink:href="#B"><stop offset="0" stop-color="#92918d"></stop><stop offset="1" stop-color="#cbcbc5"></stop></linearGradient></defs><path fill="url(#DY)" d="M486 327c1-2 2-4 2-6 1 1 1 2 2 3-1 1-1 2-1 3-1 5-1 9 0 13l1 3 1 2c-1 2-2 4-1 7v1 1h0c-1 1-1 1-2 1-3-4-7-9-8-15l-1-2c3-1 5-6 6-9l1-2z"></path><path d="M485 329l1 1c0 3-2 6-4 8l-2 2-1-2c3-1 5-6 6-9z" class="J"></path><path d="M455 308c1 1 2 1 3 2l6 9v6l1-4h1c0 2-1 3-1 5v9c0 6 0 11 2 16h-1-1c-2 1-6 0-8-2h-1c-1-5-2-9-4-14v-3l2 1c-1-3-1-5 0-8 1-2 0-6 0-8l-1-4c0-3 0-3 2-5z" class="Z"></path><path d="M455 308c1 1 2 1 3 2l-5 3c0-3 0-3 2-5z" class="S"></path><path d="M452 332l2 1 3 16h-1c-1-5-2-9-4-14v-3z" class="F"></path><path d="M464 325l1-4h1c0 2-1 3-1 5v9c0 6 0 11 2 16h-1l-1-1c-2-9-2-16-1-25z" class="K"></path><path d="M164 208l19 1v1c0 1-1 3-1 5v7 2l-2 6-1 3c0-1 0-2-1-3-2 9-3 23 2 31 2 3 5 5 7 7 1 1 4 3 4 3l1 1 5 11c1 2 3 4 3 6h-2l-7-2-1 1v1 2 1 5c-1 2-1 4 0 5h-1v-1-1l-2 2h1c-1 1-2 0-3 0-2 1-2 1-4 1-2 1-4 5-7 6h-1v1l-2-1v-1c-1-2-3-3-3-5-1-2-1-4-1-6-1-8-3-13-7-20-2-2-4-5-5-7-5-21-1-39 7-58l2-4z" class="V"></path><path d="M182 215v7 2l-2 6-1 3c0-1 0-2-1-3 0-3 1-5 2-8 0-2 1-5 2-7z" class="M"></path><path d="M180 222v5 3l-1 3c0-1 0-2-1-3 0-3 1-5 2-8z" class="F"></path><defs><linearGradient id="DZ" x1="180.089" y1="288.278" x2="187.406" y2="299.223" xlink:href="#B"><stop offset="0" stop-color="#787777"></stop><stop offset="1" stop-color="#a3a39d"></stop></linearGradient></defs><path fill="url(#DZ)" d="M171 308l4-2c2-2 4-5 4-8h0c1-5 0-10 0-14l12 3-1 1v1 2 1 5c-1 2-1 4 0 5h-1v-1-1l-2 2h1c-1 1-2 0-3 0-2 1-2 1-4 1-2 1-4 5-7 6h-1v1l-2-1v-1z"></path><path d="M389 644l1-1h1l-2 3h0c1 0 2-1 3-2 0 2 0 3-1 5l-1 1h1l1 1v-1h0l1-1 1 2s-1 0-1 1v1c-1 1-4 3-5 3l-1 1c1 1 1 0 2 1l2-2h0l3-1c0 1 0 2 1 3 0 1 1 3 1 4 2-1 4-3 6-4l2-1 5-4v1l2-2h1c-3 4-8 9-11 11-2 1-2 1-3 2l-1 1-1 1-11 10c-3 8-7 14-13 20h-2-1v1c-1 1-2 3-3 4h-4l-1-2c-4 3-4 8-10 10h-2c-4-1-6-3-8-5l-1 1-5-10-1-1-1 1c0-3 0-3-2-4-1 0-2 1-3 1h-1c-2-1-2-3-2-5-2-1-3-1-5-1l1-3c0-2 0-3 2-5l8-2h0c1-1 2-2 2-3h0l1-1c0-1 1-3 1-4h-2v-2h1c1-1 2-1 4-2l-1-2-3-3 1-1c7-4 14-6 22-9l1 1c1 1 2 1 3 2 4 1 10-3 13-4 2 0 4 0 6-1 3-1 6-1 9-4z" class="h"></path><path d="M349 695l1 1c-2 1-4 2-6 2-2 1-4 1-5 1 0 2 2 4 2 6l-1 1-5-10h2c0 1 1 1 2 1 3 0 6 0 10-2z" class="N"></path><path d="M367 661l8-5h0c-6 5-11 9-14 16-4 8-4 17-11 24l-1-1-1-1c2-1 3-3 4-5 3-5 4-11 6-16l6-9 3-3z" class="B"></path><defs><linearGradient id="Da" x1="401.616" y1="660.017" x2="385.604" y2="672.759" xlink:href="#B"><stop offset="0" stop-color="#90908f"></stop><stop offset="1" stop-color="#bfbdbb"></stop></linearGradient></defs><path fill="url(#Da)" d="M404 657l5-4v1l2-2h1c-3 4-8 9-11 11-2 1-2 1-3 2l-1 1-1 1-11 10-1-1h2c0-2 1-2 1-3-1-1-3 0-5 0 3-4 10-8 14-11 2-1 4-3 6-4l2-1z"></path><path d="M382 673c2 0 4-1 5 0 0 1-1 1-1 3h-2l1 1c-3 8-7 14-13 20h-2l1-1v-1c0-4 3-9 5-12 2-4 4-7 6-10zm-19-11h0c1-1 2-1 4-1l-3 3-6 9c-2 5-3 11-6 16-1 2-2 4-4 5l1 1c-4 2-7 2-10 2-1 0-2 0-2-1h-2l-1-1-1 1c0-3 0-3-2-4-1 0-2 1-3 1h-1c-2-1-2-3-2-5-2-1-3-1-5-1l1-3c0-2 0-3 2-5l8-2h0c1-1 2-2 2-3h0l1-1c0-1 1-3 1-4h-2v-2h1c1-1 2-1 4-2l-1-2h2c8 2 16-1 24-1z" class="C"></path><path d="M352 689c-2 0-3 0-5-1v-1l-2 1v-1c0-1 0-1 1-2h0v-1c0-1 0-2 1-3h2v-1c2-3 5-6 8-9 1-2 3-4 4-6 1 0 2-1 3-1l-6 9c-2 5-3 11-6 16z" class="E"></path><path d="M320 687l1-3c0-2 0-3 2-5 3 3 6 3 9 4l1 1h1c2 1 4 0 6-1l1-1c1 0 1 0 2-1v1l1 1c-2 1-3 2-4 3-2 0-3 1-4 3h-1l-1 1c1 1 2 2 4 2h7c2-1 3-1 5-2 0 1-2 3-2 4l1 1c-4 2-7 2-10 2-1 0-2 0-2-1h-2l-1-1-1 1c0-3 0-3-2-4-1 0-2 1-3 1h-1c-2-1-2-3-2-5-2-1-3-1-5-1z" class="c"></path><path d="M325 688c2 1 4 2 6 4-1 0-2 1-3 1h-1c-2-1-2-3-2-5z" class="N"></path><path d="M337 663h2c8 2 16-1 24-1h-1c-4 3-8 4-12 4v1 1l-2 1v2c1 2 3 0 0 3h0c-1 2-1 4-2 6h-1c-1 0-2 1-2 1-1 1-1 1-2 1l-1 1c-2 1-4 2-6 1h-1l-1-1c-3-1-6-1-9-4l8-2h0c1-1 2-2 2-3h0l1-1c0-1 1-3 1-4h-2v-2h1c1-1 2-1 4-2l-1-2z" class="V"></path><path d="M333 667h1c1-1 2-1 4-2-1 4-1 8-5 11 0 0-1 1-2 1h0c1-1 2-2 2-3h0l1-1c0-1 1-3 1-4h-2v-2z" class="F"></path><defs><linearGradient id="Db" x1="475.531" y1="434.677" x2="513.963" y2="419.942" xlink:href="#B"><stop offset="0" stop-color="#bfbfba"></stop><stop offset="1" stop-color="#fefdf9"></stop></linearGradient></defs><path fill="url(#Db)" d="M476 346l1-2c2 1 5 5 6 7 4 5 8 9 10 15 15 27 16 57 17 87 0 9 1 18 0 26 0 1-1 2-1 3 1 2 0 3 1 6v11h-1c-1 1-3 1-4 3-1-1-4-2-5-4v-10c-1-1 0-1-1-1-1-16-1-31-5-47h1c0-2-1-4-2-5h-3v-2h-1v-5l1-1c1-2 1-5 0-7v-3c-1-6-2-13-5-18h-1c0-2-1-3-2-5l-1-1-1-1c-2-3-4-5-6-7h-1l-8-8c-3-1-5-2-7-4l1-2c1-1 1-2 1-4h0v-1c1-1 2-2 1-4 3 1 4 3 5 4l1-1-5-4h0c1-1 0-1 1 0h1c2-1 3-2 4-4l-2-1-1-1-5-1c-1 0-2-1-4-2-1 0-3-2-4-4 1 1 2 1 4 1h1c2 2 6 3 8 2h1 1 0c2 0 5-1 6-3 1-1 2-2 2-4 0 1 0 2 1 2z"></path><path d="M472 372c1 1 2 3 3 4h0 0-2c0 1 1 1 1 3h-1c-1-2-1-4-1-7z" class="c"></path><g class="I"><path d="M476 359v1l1 1v1l-1 1v1c1 0 1 1 1 2h0c-1 0-4-4-5-5l4-2z"></path><path d="M470 371c0-2-1-3-2-5 1 0 2 1 2 1 1-2 1-4 3-5 1 2 3 4 4 6h-1l-1-1c-1 1-1 1-2 3h0 1 0l-1 1c0 2 1 3 2 5-1-1-2-3-3-4l-2-1z"></path></g><path d="M468 357c1 2 3 3 5 5-2 1-2 3-3 5 0 0-1-1-2-1 1 2 2 3 2 5 0-1-2-3-2-3-1-1-1 0-2-1l-2-2c-1 2-1 4-1 6v1l-1 2 3 3h0c-3-1-5-2-7-4l1-2c1-1 1-2 1-4h0v-1c1-1 2-2 1-4 3 1 4 3 5 4l1-1-5-4h0c1-1 0-1 1 0h1c2-1 3-2 4-4z" class="E"></path><path d="M475 344c0 1 0 2 1 2v1c-1 3-4 5-6 7h-3c0 1 0 1 1 2l4 5c1 1 4 5 5 5 9 14 18 28 20 44h0c-4-13-9-26-17-38-1-1-2-4-3-4-1-2-3-4-4-6-2-2-4-3-5-5l-2-1-1-1-5-1c-1 0-2-1-4-2-1 0-3-2-4-4 1 1 2 1 4 1h1c2 2 6 3 8 2h1 1 0c2 0 5-1 6-3 1-1 2-2 2-4z" class="D"></path><path d="M476 346l1-2c2 1 5 5 6 7v1c-1 1-1 3 0 5v6h0c-1-2-2-2-3-4l-2-1c-1 0-1 0-2 1l-4 2-4-5c-1-1-1-1-1-2h3c2-2 5-4 6-7v-1z" class="E"></path><path d="M277 385l24-11 4 1c1 1 1 2 2 3 1 3 4 5 6 7 0 1 1 2 1 3h0-3v1l1 1h2l1 1c-1 6 0 12 2 17l1 2c2 2 4 3 8 4l4-1c1 1 2 2 3 2 2 0 5-1 7 0 1 1 1 3 1 4 1 2 3 2 3 4-2 1-3 1-4 1l-18 2-16 2c-6 2-13 4-19 6-3 1-7 2-10 4h0c-3 3-7 4-11 6-3 2-5 3-7 5l-1-3-1-2c-1 1-2 3-2 3l-3-3c-2-1-4 1-6-1 2-1 3-2 4-4 2-3 3-6 4-9h0c1-3 3-5 5-7l1-1h0c1-1 1-1 2-1v-1c2-2 2-5 2-8 0-2 1-4 0-6l-6-1c1-1 1-1 3-1h0c-1-1-2-1-3-2l1-1v-1c0-3 1-4 3-6l3-3c2-2 6-5 8-7h1l2-1h1v2z" class="V"></path><path d="M260 422h0c1-1 1-1 2-1s1 1 2 2c-3 1-4 3-6 5v-1c0-1 1-2 1-4l1-1z" class="D"></path><defs><linearGradient id="Dc" x1="258.393" y1="414.176" x2="274.121" y2="404.283" xlink:href="#B"><stop offset="0" stop-color="#0b0b0b"></stop><stop offset="1" stop-color="#2c2b29"></stop></linearGradient></defs><path fill="url(#Dc)" d="M262 401c2 1 4 2 6 2 6 0 10-5 15-6-5 4-11 7-15 12v1c0 4 0 9-3 12-1 1-1 0-1 1-1-1-1-2-2-2v-1c2-2 2-5 2-8 0-2 1-4 0-6-1-1-1-2-2-3l-1-1 1-1z"></path><path d="M259 423c0 2-1 3-1 4v1 1c-1 2-3 5-4 7l6 4c6-3 11-6 17-9 1 0 4-2 5-2-1 2-4 3-6 4-4 2-8 4-12 7-1 0-3 1-4 2s-2 3-2 4l-1-2c-1 1-2 3-2 3l-3-3c-2-1-4 1-6-1 2-1 3-2 4-4 2-3 3-6 4-9h0c1-3 3-5 5-7z" class="O"></path><path d="M246 443c2-1 3-2 4-4l6 3v1l1 1c-1 1-2 3-2 3l-3-3c-2-1-4 1-6-1z" class="B"></path><path d="M311 389l1 1h2l1 1c-1 6 0 12 2 17l1 2h-2-2v1h-5v1c-4 0-9 1-12 0h0c1 0 2 0 4-1h4l4-1v-1c-1 0-2 0-4 1-1 0-3-1-5 0h-7 0l20-3c-3-2-8-2-11-3h-3-1l-1-1c2-1 4-2 6-4h-6l3-2 6-3c-3 1-6 2-8 2 0-1 3-2 4-3s2 0 4-1h0c1 0 1 0 2-1h2 0-4c-2 0-2 1-3 0-1 0-1 0-2 1l-1-1h0c1-1 3-1 4-1 2-1 4-1 7-1z" class="S"></path><defs><linearGradient id="Dd" x1="281.273" y1="448.226" x2="283.989" y2="422.272" xlink:href="#B"><stop offset="0" stop-color="#b4b4b1"></stop><stop offset="1" stop-color="#e3e1e0"></stop></linearGradient></defs><path fill="url(#Dd)" d="M282 429c1 0 2-1 3-1l14-5 4-1c-1 1-1 2-2 3 2 1 4 2 5 3-6 2-13 4-19 6-3 1-7 2-10 4h0c-3 3-7 4-11 6-3 2-5 3-7 5l-1-3c0-1 1-3 2-4s3-2 4-2c4-3 8-5 12-7 2-1 5-2 6-4z"></path><defs><linearGradient id="De" x1="342.632" y1="426.473" x2="302.869" y2="416.528" xlink:href="#B"><stop offset="0" stop-color="#888885"></stop><stop offset="1" stop-color="#ecebe5"></stop></linearGradient></defs><path fill="url(#De)" d="M318 410c2 2 4 3 8 4l4-1c1 1 2 2 3 2 2 0 5-1 7 0 1 1 1 3 1 4 1 2 3 2 3 4-2 1-3 1-4 1l-18 2-16 2c-1-1-3-2-5-3 1-1 1-2 2-3l5-1c1-1 1-1 2-1s1 0 2-1h2l1-1h2 3l1-1h4c-1-1-2-1-2-2-3 0-4-2-7-2-2 2-4 2-6 3l-1-1c2 0 3 0 4-1l-6-1h0 7v-1h1-6v-1h5v-1h2 2z"></path><defs><linearGradient id="Df" x1="308.444" y1="378.358" x2="266.09" y2="405.584" xlink:href="#B"><stop offset="0" stop-color="#d5d5ce"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#Df)" d="M277 385l24-11 4 1c1 1 1 2 2 3 1 3 4 5 6 7 0 1 1 2 1 3h0-3v1c-3 0-5 0-7 1-1 0-3 0-4 1h0c-2 1-4 2-6 2h-1v1c-1 0-2 1-4 1-1 0-3 1-4 2h-2c-5 1-9 6-15 6-2 0-4-1-6-2l-1 1 1 1c1 1 1 2 2 3l-6-1c1-1 1-1 3-1h0c-1-1-2-1-3-2l1-1v-1c0-3 1-4 3-6l3-3c2-2 6-5 8-7h1l2-1h1v2z"></path><path d="M273 384h1l2-1h1v2c-5 2-9 6-12 10-1 1-2 3-3 4v1 1l-1 1 1 1c1 1 1 2 2 3l-6-1c1-1 1-1 3-1h0c-1-1-2-1-3-2l1-1v-1c0-3 1-4 3-6l3-3c2-2 6-5 8-7z" class="B"></path><path d="M552 238c1 0 2 0 3-1l2-1h1 2-1c-1 2-3 5-3 7v1c0-1 1-2 2-3h0c2 3 6 5 8 7-2 6-3 13 0 19 2 2 9 11 12 12 1 0 1 0 2 1l1 1h0c-1-1-1-1-1-2h-1l1-2c2 3 6 10 9 11h2c-1 3-3 6-5 10l-6 7c-2 3-4 6-5 10v-1-4l-1-1-1 1 2 14c2 8-1 15-2 22 0 1-1 3-1 4l-3 1h0c-3 1-4 1-7 0l-2-1c-1-1-2-2-3-4v-4-16l-3-6h-1l-1 2c0 1 0 2-1 4 1 1 1 2 1 3s0 1-1 2c0 1 0 1-1 2h-1c-1-2-2-6-3-8v-1-1c0-3-2-6-2-10h0l-2-2c-1-1-1-3-2-4-2-9-1-17-1-26-1 0-3-1-4-2 1-4 2-9 3-13 2-7 3-14 6-19 2-4 5-6 8-9z" class="V"></path><path d="M580 304h-1c1-2 1-5 2-7 1 1 2 1 3 2 1-1 1-2 2-3v2l-6 7v-1z" class="B"></path><path d="M580 277c2 3 6 10 9 11h2c-1 3-3 6-5 10v-2c-1-8-4-12-8-17 1 0 1 0 2 1l1 1h0c-1-1-1-1-1-2h-1l1-2z" class="R"></path><path d="M553 285l-1-8h0c1 5 2 11 4 16 1 4 3 8 5 12l3 12c4-5 5-8 6-13h1c3 1 6 1 9 0v1c-2 3-4 6-5 10v-1-4l-1-1-1 1h0c-1 3-4 5-6 8l-2 2v5c1 8 1 16-1 23-1-2 0-4 0-6 1-8 1-17-2-24 0-9-4-17-7-25-1-2-1-5-2-8z" class="M"></path><path d="M573 310l2 14c2 8-1 15-2 22 0 1-1 3-1 4l-3 1h0c-3 1-4 1-7 0l2-2v-1c2-7 2-15 1-23v-5l2-2c2-3 5-5 6-8h0z" class="Z"></path><path d="M552 238c1 0 2 0 3-1l2-1h1 2-1c-1 2-3 5-3 7v1c0 1-2 3-2 5-2 7-3 15-2 23v5h0l1 8c1 3 1 6 2 8 3 8 7 16 7 25 3 7 3 16 2 24 0 2-1 4 0 6v1l-2 2-2-1c-1-1-2-2-3-4v-4-16l-3-6h-1l-1 2c0 1 0 2-1 4 1 1 1 2 1 3s0 1-1 2c0 1 0 1-1 2h-1c-1-2-2-6-3-8v-1-1c0-3-2-6-2-10h0l-2-2c-1-1-1-3-2-4-2-9-1-17-1-26-1 0-3-1-4-2 1-4 2-9 3-13 2-7 3-14 6-19 2-4 5-6 8-9z" class="I"></path><path d="M542 311h2c1-1 0-2 0-4s-1-9-1-10c1 2 1 5 2 7v3c1 1 1 3 1 4 1 2 2 4 2 7 1 0 1 1 1 2s0 2 1 3c0 0 1 0 2-1 0 1 0 2-1 4 1 1 1 2 1 3s0 1-1 2c0 1 0 1-1 2h-1c-1-2-2-6-3-8v-1-1c0-3-2-6-2-10h0l-2-2z" class="E"></path><path d="M550 323s1 0 2-1c0 1 0 2-1 4h-3c0-2 1-2 2-3z" class="B"></path><defs><linearGradient id="Dg" x1="562.547" y1="297.768" x2="550.058" y2="341.894" xlink:href="#B"><stop offset="0" stop-color="#949491"></stop><stop offset="1" stop-color="#cccac6"></stop></linearGradient></defs><path fill="url(#Dg)" d="M549 274v-4h0c1 5 1 11 3 16 1 0 0 0 1-1 1 3 1 6 2 8 3 8 7 16 7 25 3 7 3 16 2 24 0 2-1 4 0 6v1l-2 2-2-1c-1-1-2-2-3-4v-4-16-5c-1-1-1-1-1-2 0-3-1-6-2-10 0-5-3-10-4-15-3-7-3-14-4-21l1-1v2s1 1 2 0z"></path><path d="M547 274s1 1 2 0l2 17h-1v-2l-1-1c-1-5-1-9-2-14z" class="a"></path><path d="M557 346l1-1 1-1c2-1 3-1 5-2 0 2-1 4 0 6v1l-2 2-2-1c-1-1-2-2-3-4z" class="C"></path><path d="M560 350c0-1 1-2 1-2 0-1 1-1 2-1 0 0 0 1 1 2l-2 2-2-1z" class="b"></path><path d="M549 274v-4h0c1 5 1 11 3 16 1 0 0 0 1-1 1 3 1 6 2 8 3 8 7 16 7 25-2-4-3-9-5-13s-4-8-6-13v-1l-2-17z" class="G"></path><path d="M306 428l16-2c2 1 3 1 5 1 5 1 11 0 16 1h4c1 1 1 3 0 5 0 1-1 1-1 2v2l-1 1c0 2 0 3-1 4-2 1-4 1-7 1h1 11l-10 4c-2 2-4 3-5 6 0 1 1 3 2 3l3 2-1 2c-2 0-5 0-7 1h-1l-1 1h-3 0c-2 1-4 0-5 1-1 0-2 0-3 1-2 0-3 0-4-1-1 0-2 0-3 1-3 1-6 1-8 2-6 1-11 3-16 5l-3 1-3 1c-2 1-5 2-6 3-1 3-8 5-11 7-6 2-10 6-16 8h1c1 0 2 1 3 1 0 0-1 1 0 1h-1l-4 4-1 1-4 4h0l-1-2c-3 0-6-1-9-3v-2c1-2 2-4 4-5 2-4 6-14 5-18 2-1 3-1 5-1l2 1c3-2 7-4 10-7v-2l1-14c2-2 4-3 7-5 4-2 8-3 11-6h0c3-2 7-3 10-4 6-2 13-4 19-6z" class="V"></path><path d="M241 472c2-1 3-1 5-1l2 1c-7 4-8 11-10 18 4 0 8 0 12-1s7-4 11-6 9-5 14-7c-1 3-8 5-11 7-6 2-10 6-16 8h-9-3c0 1-1 1-1 2h0l-3 3v-1c1-2 2-4 4-5 2-4 6-14 5-18z" class="N"></path><path d="M235 493h0c0-1 1-1 1-2h3 9 1c1 0 2 1 3 1 0 0-1 1 0 1h-1l-4 4-1 1-4 4h0l-1-2c-3 0-6-1-9-3v-2 1l3-3z" class="I"></path><path d="M232 495v1c4 2 9 2 14 2l-4 4h0l-1-2c-3 0-6-1-9-3v-2z" class="T"></path><path d="M235 493h0c0-1 1-1 1-2h3 9 1c1 0 2 1 3 1 0 0-1 1 0 1h-1l-4 4c-1-4-5-2-8-3v-2c-2 0-2 0-3 1h-1z" class="a"></path><path d="M332 444h6l1 3c-2 2-4 3-5 6 0 1 1 3 2 3l3 2-1 2c-2 0-5 0-7 1h-1l-1 1h-3 0c-2 1-4 0-5 1-1 0-2 0-3 1-2 0-3 0-4-1h2c1-1 2-1 3-1h4l1-1h3c1-1 1 0 2 0 0-1 0-1 1-1h3c1-1 1-1 2-1h0l-1-1c-1-1-2-1-4-1h-2c-1-1-1-1-2-1-5 1-10 2-15 2h1c3-1 6-2 9-2l-2-1-6 1h-2v-1h2l8-2c1 0 3 0 3-2-5 0-11 2-16 2 2-1 4-1 7-2l12-1c-3-1-6-1-8-1h-4l4-1c-3-1-6 1-9 0-1 0-2 0-2-1l14-2c4-1 7-1 10-1z" class="C"></path><path d="M323 454c1 0 1 1 2 1-1 1-3 1-4 1l-2-1h0l4-1z" class="c"></path><path d="M323 454c1-1 3-1 4-1 1 1 2 1 2 2h-4c-1 0-1-1-2-1z" class="S"></path><path d="M332 444h6l1 3c-2 2-4 3-5 6-1-3-1-6-2-9z" class="E"></path><path d="M322 445h4c-1 1-3 1-4 1l6 2h-9c-3-1-6 1-9 0-1 0-2 0-2-1l14-2z" class="c"></path><path d="M306 428l16-2c2 1 3 1 5 1 5 1 11 0 16 1h4c1 1 1 3 0 5 0 1-1 1-1 2v2l-1 1c0 2 0 3-1 4-2 1-4 1-7 1h1 11l-10 4-1-3h-6c-3 0-6 0-10 1l-14 2c-13 4-26 8-39 13-4 2-8 3-11 5v-2l1-14c2-2 4-3 7-5 4-2 8-3 11-6h0c3-2 7-3 10-4 6-2 13-4 19-6z" class="V"></path><path d="M306 428l16-2c2 1 3 1 5 1 5 1 11 0 16 1h-13-9c-11 1-20 3-31 6l-10 3s-2 1-3 1h0c3-2 7-3 10-4 6-2 13-4 19-6z" class="U"></path><path d="M321 428h9l3 1c0 2-1 4-2 5v1h1c0 1 0 1 1 1 0 2 1 4 1 6 0 0-1 0-1 1h-9c1-1 2-1 4-1 1 0 1 1 3 0v-1l-1-1v-1h-1c-2-1-2 0-3-1-3-2-11-1-14-1 2-1 4-1 5-1h2c2-1 3-1 5 0h1c-1-2-3-2-5-2 2-1 4-1 7-1 1-1 1-2 1-4h-6l-1-1z" class="C"></path><path d="M333 443c2 0 3-1 4 0h1 11l-10 4-1-3h-6c-3 0-6 0-10 1l-14 2c-13 4-26 8-39 13-4 2-8 3-11 5v-2c1 1 3-1 4-1 20-9 40-16 62-19h9z" class="G"></path><defs><linearGradient id="Dh" x1="345.669" y1="433.646" x2="331.879" y2="435.631" xlink:href="#B"><stop offset="0" stop-color="#8d8c88"></stop><stop offset="1" stop-color="#bfbeb9"></stop></linearGradient></defs><path fill="url(#Dh)" d="M330 428h13 4c1 1 1 3 0 5 0 1-1 1-1 2v2l-1 1c0 2 0 3-1 4-2 1-4 1-7 1-1-1-2 0-4 0 0-1 1-1 1-1 0-2-1-4-1-6-1 0-1 0-1-1h-1v-1c1-1 2-3 2-5l-3-1z"></path><path d="M380 546c2 2 2 2 4 3l2 3-6 4c3-1 6-2 8-4l1-1h1c0 1 1 1 2 2h0c-1 2-1 3 0 4 0 2 0 2-1 4v1l-1 1c2 1 3 0 5-1h2c1 1 1 1 1 2 0 3 2 7 2 10v3h0c1 7 0 17-2 24 0 1-3 8-3 8l-3 6c-2 2-3 4-4 6-8 10-19 20-31 23-8 3-15 3-22 6-2 1-4 2-5 3-1 2-4 2-5 4s-1 3-1 4c1 0 5 0 6-1h2l1-1 2-1h-1l1 1-1 1 3 3 1 2c-2 1-3 1-4 2h-1v2h2c0 1-1 3-1 4l-1 1-9-6-4 2-1-1h-2c-3 0-4-1-6-3-2-3-3-8-3-12 1-4 4-7 7-9l8-4 18-7c11-5 19-16 25-27l2-7 1-2c-1 0-1-1-2-1-4-5-7-13-6-19v-1c2-9 7-15 13-22 1-3 4-6 6-9z" class="Z"></path><path d="M380 584l1 2v1 1 2h-1l-1 1-3-3 1-1h2c1-1 1-1 1-2v-1z" class="b"></path><path d="M376 583h2l2-1h0v2 1c0 1 0 1-1 2h-2c-1-2-2-2-1-4z" class="S"></path><path d="M388 605l1 6 2 1 1 3c-2 2-3 4-4 6 0-2 0-2 1-4 0-1 1-1 1-3h0v-2h-2v1c-1-3-1-5 0-8zm-7-19c1-1 1-2 1-3h0c1-1 2-1 3-1 1 4 3 8 3 12-1-2-2-5-3-7v-2h-2v2 2c-1 0-1-1-2-2v-1z" class="C"></path><path d="M382 565l1 1h0c0 1-1 2-2 3 1 0 1 1 2 2h-1c-1-1-2-1-4-1h0-5c3-3 6-4 9-5z" class="P"></path><path d="M319 669l3-2c0-3-3-6-4-9 0-2 0-4 1-5s1-1 2-1c0 1 0 2 1 3 2 1 5-1 8-2-1 2-4 2-5 4s-2 2-2 4c1 4 2 6 4 7s3 2 5 2l1-1h2c0 1-1 3-1 4l-1 1-9-6-4 2-1-1z" class="N"></path><defs><linearGradient id="Di" x1="372.306" y1="576.917" x2="373.325" y2="586.864" xlink:href="#B"><stop offset="0" stop-color="#b7b6b2"></stop><stop offset="1" stop-color="#e1dfdb"></stop></linearGradient></defs><path fill="url(#Di)" d="M370 577c2-1 3-1 4-1 1 1 1 1 2 1v6c-1 2 0 2 1 4l-1 1-1-1c0 2 0 3-1 4h0l-1-1-3-3v4l-1-1v-4c0-3-1-6 0-8h1v-1z"></path><path d="M369 578h1c0 1 1 1 1 2-1 1-1 5-2 6h0c0-3-1-6 0-8z" class="d"></path><path d="M373 570h5c1 1 2 1 3 2h2v2l-1 1c-1 1-1 3-1 4s0 1-1 1v2h0l-2 1h-2v-6c-1 0-1 0-2-1-1 0-2 0-4 1v1h-1c1-3 2-5 4-8z" class="E"></path><path d="M376 577v1l1 1v1h1 2 0v2h0l-2 1h-2v-6z" class="d"></path><path d="M373 570h5c1 1 2 1 3 2h2v2l-1 1c-1 1-1 3-1 4s0 1-1 1h0v-4h-1l-1 1v-1-3l-1-1c-1 1-5 1-5 2-1 1-1 2-2 3v1h-1c1-3 2-5 4-8zm-48 87c-1 2-1 3-1 4 1 0 5 0 6-1h2l1-1 2-1h-1l1 1-1 1 3 3 1 2c-2 1-3 1-4 2h-1v2l-1 1c-2 0-3-1-5-2s-3-3-4-7c0-2 1-2 2-4z" class="L"></path><path d="M334 658l1 1-1 1 3 3 1 2c-2 1-3 1-4 2h-1v-5c0-1 0-2 1-4z" class="B"></path><path d="M369 598h1c2-1 3-2 5-3 2 0 2 1 3 2 1 5-4 13-7 17-3 6-9 12-15 16 2-2 5-5 6-7 3-3 6-6 8-10-1-1-3-2-4-3v-3l2-7 1-2z" class="M"></path><path d="M368 600l1 1c2 1 4 0 6 0-1 4-2 8-5 12-1-1-3-2-4-3v-3l2-7z" class="L"></path><path d="M380 546c2 2 2 2 4 3l2 3-6 4c3-1 6-2 8-4l1-1h1c0 1 1 1 2 2h0c-1 2-1 3 0 4 0 2 0 2-1 4v1l-1 1-7 3-1-1 1-1-2-1v-2h-1l-1-2 1-1 1 1v-1-1h0c-2 0-3 1-4 1h-1l1-2 1-1c1 0 1 0 2-1h0c-1-1-1-2-3-2l-3 3h0c1-3 4-6 6-9z" class="C"></path><defs><linearGradient id="Dj" x1="389.059" y1="575.03" x2="380.062" y2="610.494" xlink:href="#B"><stop offset="0" stop-color="#888884"></stop><stop offset="1" stop-color="#d4d3ce"></stop></linearGradient></defs><path fill="url(#Dj)" d="M382 575l1-1c2 0 2 0 4 2h0c0 1 0 0 1 1 2 1 3 6 3 9v9c0 2-1 4-2 6 0 2 0 3-1 4-1 3-1 5 0 8l-2 2-1-1-1 1v-2l-2 2h0c-1-1 0-1 0-1 3-7 5-13 6-20 0-4-2-8-3-12-1 0-2 0-3 1h0c0 1 0 2-1 3l-1-2v-2-2c1 0 1 0 1-1s0-3 1-4z"></path><path d="M380 580c1 0 1 0 1-1s0-3 1-4l3 7c-1 0-2 0-3 1h0c0 1 0 2-1 3l-1-2v-2-2z" class="a"></path><defs><linearGradient id="Dk" x1="355.721" y1="609.667" x2="354.86" y2="634.955" xlink:href="#B"><stop offset="0" stop-color="#989894"></stop><stop offset="1" stop-color="#cccbc8"></stop></linearGradient></defs><path fill="url(#Dk)" d="M366 607v3c1 1 3 2 4 3-2 4-5 7-8 10-1 2-4 5-6 7-2 3-5 4-8 6h-1l1-1c-3 0-6 2-8 3l-9 3c-1 0-2 1-2 0 1 0 2-1 3-1 0 0 1 0 1-1h2c1-1 1 0 2-1 1 0 1 0 2-1h3l1-1h1v-1c-4 0-5 1-8 2-4 2-8 4-13 4l18-7c11-5 19-16 25-27z"></path><defs><linearGradient id="Dl" x1="399.987" y1="581.208" x2="378.746" y2="598.559" xlink:href="#B"><stop offset="0" stop-color="#6e6f6b"></stop><stop offset="1" stop-color="#c4c3be"></stop></linearGradient></defs><path fill="url(#Dl)" d="M390 563c2 1 3 0 5-1h2c1 1 1 1 1 2 0 3 2 7 2 10v3h0c1 7 0 17-2 24 0 1-3 8-3 8l-3 6-1-3-2-1-1-6c1-1 1-2 1-4 1-2 2-4 2-6v-9c0-3-1-8-3-9-1-1-1 0-1-1h0c-2-2-2-2-4-2v-2h-2c-1-1-2-1-3-2h0c2 0 3 0 4 1h1c-1-1-1-2-2-2 1-1 2-2 2-3h0l7-3z"></path><path d="M383 572h1c1 1 3 2 3 3v1c-2-2-2-2-4-2v-2z" class="G"></path><path d="M390 563c2 1 3 0 5-1h2c1 1 1 1 1 2 0 3 2 7 2 10v3h0v3h-1c0-2-1-2-2-3 0 0-1 0-1 1-2-3-3-5-4-7-1-1-1-2-2-2v-2l-1-1-6 5c-1-1-1-2-2-2 1-1 2-2 2-3h0l7-3z" class="X"></path><path d="M391 566c1 0 2-1 4-1 1 0 2 0 3-1 0 3 2 7 2 10l-2-2h0c-1-1 0-1-1-1-1-2-2-3-3-4l-3-1z" class="U"></path><path d="M390 563c2 1 3 0 5-1h2c1 1 1 1 1 2-1 1-2 1-3 1-2 0-3 1-4 1h-2l-6 5c-1-1-1-2-2-2 1-1 2-2 2-3h0l7-3z" class="B"></path><path d="M601 612l2 3c1 2 1 4 3 5 0 2 2 4 3 5v-2h3c1 1 2 2 3 4 2 1 3 3 6 4-1-1-2-2-2-3 1 0 2 0 3-1 6 7 14 13 22 19l3 2 5 2c5 2 11 3 17 5 3 1 7 3 10 5v1c-3 2-3 5-3 8 0 2 1 3 2 5 3 2 8 3 12 5-1 0-2 1-2 2-1 1-8 2-8 3v1c-1 0-1 1-2 1v1h2l1 2-2 1v1h1l3 1-1 3h-1c-1 1-2 2-4 3-1 5-2 10-7 13-3 1-6 1-9 0-2 0-3-1-5-2v3h-1v4c0-4 0-5-3-8-1 0-1-1-2-1v2h0c-1-2-1-2-2-3v-1c-1-2-1-3-2-4v-1c-1 0-1-1-2-2-1 1 0 1-1 1l-4-3c-1-1-4-5-4-6h1l3 4 3 3h1c-1-2-3-4-5-6-2-3-4-7-6-10-4-4-10-11-15-14l-1-1-5-5c1 0 2 0 3 1h1l-2-2-3-4v-2h0c0-1 0-1 1-2v-1h1c-1-2-4-3-5-6 2 1 3 1 4 1-2-2-6-7-8-8v-2c-2-3-4-6-5-9s-3-8-1-11h1l2 1c0-2 1-3 1-5z" class="h"></path><path d="M644 646l3 2c-1 0-1 0-2 1-2 0-3-1-4-1l3-2z" class="b"></path><path d="M629 672c5 4 9 10 12 16 1 1 2 3 2 4v2 3c-1-2-3-4-5-6-2-3-4-7-6-10h3c-1-3-4-7-6-9z" class="I"></path><defs><linearGradient id="Dm" x1="616.012" y1="672.305" x2="633.459" y2="671.228" xlink:href="#B"><stop offset="0" stop-color="#b4b6b0"></stop><stop offset="1" stop-color="#dad6d6"></stop></linearGradient></defs><path fill="url(#Dm)" d="M611 661c1 0 2 0 3 1h1 2c4 3 9 6 12 10h0c2 2 5 6 6 9h-3c-4-4-10-11-15-14l-1-1-5-5z"></path><defs><linearGradient id="Dn" x1="607.012" y1="649.111" x2="618.523" y2="652.262" xlink:href="#B"><stop offset="0" stop-color="#a19f9c"></stop><stop offset="1" stop-color="#cdccc8"></stop></linearGradient></defs><path fill="url(#Dn)" d="M612 651c-1-2-4-3-5-6 2 1 3 1 4 1 1 1 2 1 3 2 1 0 1 1 2 2l2 1s1 1 2 1l3 3c1 0 2 1 2 1l2 1 1 1h0c-2 0-3-1-5-1v-1c-2-1-3-2-5-2v3 1h0c0 1 0 1-1 1 1 1 2 1 2 2-1 1-1 1-2 1h-2l-2-2-3-4v-2h0c0-1 0-1 1-2v-1h1z"></path><path d="M613 654v-2c1 1 1 2 2 3h0-2c-1 1 0 1 0 2l-2-2c1-1 0-1 1-1h1z" class="d"></path><path d="M610 656v-2h0c0-1 0-1 1-2v-1h1l1 3h-1c-1 0 0 0-1 1l2 2h0l2 2h-1l-1 1-3-4z" class="E"></path><path d="M613 657c1-1 2-1 3-2l1 1c0 1 0 0 1 2h0c0 1 0 1-1 1 1 1 2 1 2 2-1 1-1 1-2 1h-2l-2-2 1-1h1l-2-2z" class="C"></path><defs><linearGradient id="Do" x1="604.827" y1="640.032" x2="620.051" y2="643.261" xlink:href="#B"><stop offset="0" stop-color="#999994"></stop><stop offset="1" stop-color="#d6d5ce"></stop></linearGradient></defs><path fill="url(#Do)" d="M603 636c1 0 2 0 3 1h1l-1-1v-3l1-1c3 2 4 4 7 6 1 0 1 1 1 1h1c1 1 1 2 2 2l1 1 8 6c-2-1-3-1-4-2l-1-1h-1v-1c-2 2-2 4-2 5l2 3h1 0c1 1 0 1 1 1h0l1 2h1c1 0 1 1 2 2l-2-1s-1-1-2-1l-3-3c-1 0-2-1-2-1l-2-1c-1-1-1-2-2-2-1-1-2-1-3-2-2-2-6-7-8-8v-2z"></path><defs><linearGradient id="Dp" x1="595.831" y1="618.354" x2="606.534" y2="633.015" xlink:href="#B"><stop offset="0" stop-color="#4b4a4b"></stop><stop offset="1" stop-color="#908f8d"></stop></linearGradient></defs><path fill="url(#Dp)" d="M601 612l2 3c1 2 1 4 3 5 0 2 2 4 3 5l2 4-2-2c0 1 0 2 1 3v1h0l1 1c1 2 3 4 6 6 2 1 3 3 5 4l1 1 3 3c1 1 2 1 3 2v1l-2-1-8-6-1-1c-1 0-1-1-2-2h-1s0-1-1-1c-3-2-4-4-7-6l-1 1v3l1 1h-1c-1-1-2-1-3-1-2-3-4-6-5-9s-3-8-1-11h1l2 1c0-2 1-3 1-5z"></path><defs><linearGradient id="Dq" x1="601.806" y1="621.911" x2="608.053" y2="625.814" xlink:href="#B"><stop offset="0" stop-color="#6b6b6b"></stop><stop offset="1" stop-color="#8a8a86"></stop></linearGradient></defs><path fill="url(#Dq)" d="M601 612l2 3c1 2 1 4 3 5 0 2 2 4 3 5l2 4-2-2c0 1 0 2 1 3v1h0l1 1c1 2 3 4 6 6 2 1 3 3 5 4l1 1c-2 0-6-5-8-6-4-1-5-4-7-7-2-2-3-4-5-6l-2-4h0c-1-1-1-2-1-3 0-2 1-3 1-5z"></path><path d="M601 612l2 3c-1 2-1 2-1 3l1 1v1h-1-1 0c-1-1-1-2-1-3 0-2 1-3 1-5z" class="X"></path><defs><linearGradient id="Dr" x1="628.691" y1="626.148" x2="631.809" y2="647.352" xlink:href="#B"><stop offset="0" stop-color="#989996"></stop><stop offset="1" stop-color="#deddd7"></stop></linearGradient></defs><path fill="url(#Dr)" d="M612 623c1 1 2 2 3 4 2 1 3 3 6 4-1-1-2-2-2-3 1 0 2 0 3-1 6 7 14 13 22 19l-3 2-9-4c-2-1-5-3-7-4-1-1-2-1-3-1-3-1-7-5-8-7l-2-1h0l-1 1-1-1h0v-1c-1-1-1-2-1-3l2 2-2-4v-2h3z"></path><path d="M612 623c1 1 2 2 3 4 2 1 3 3 6 4v1h-1v1l-3-2-1 1 2 2v1l-4-4v1l-2-1h0l-1 1-1-1h0v-1c-1-1-1-2-1-3l2 2-2-4v-2h3z" class="f"></path><path d="M612 623c1 1 2 2 3 4h-3v1c1 1 1 0 1 1h0-2l-2-4v-2h3z" class="L"></path><path d="M643 694l1 1c3 4 5 6 9 8l-2-7c1 0 0 0 1 1 2 4 6 11 10 13 3 0 5 0 7-1 4-2 5-7 6-11-7 0-14-3-17-11-2-6-3-12-6-18-2-4-7-8-11-12h0l9 6c3 4 5 8 6 12 2 5 4 11 7 16 0-1 1-1 2-1s4 1 5 2c4 0 8-1 11 1v1 1c-1 1-2 2-4 3-1 5-2 10-7 13-3 1-6 1-9 0-2 0-3-1-5-2v3h-1v4c0-4 0-5-3-8-1 0-1-1-2-1v2h0c-1-2-1-2-2-3v-1c-1-2-1-3-2-4v-1c-1 0-1-1-2-2-1 1 0 1-1 1l-4-3c-1-1-4-5-4-6h1l3 4 3 3h1v-3z" class="R"></path><path d="M663 691c0-1 1-1 2-1s4 1 5 2c4 0 8-1 11 1v1c-3 2-5 3-9 2-4 0-7-2-9-5z" class="Z"></path><path d="M650 663l3 1c3 0 8 0 10 1h3c2 1 3 2 3 3 0 3 0 5-1 8 0 0-1 1-1 2 1 2 3 3 5 4v1c3 1 5 1 8 1v1c-1 0-1 1-2 1v1h2l1 2-2 1v1h1l3 1-1 3h-1v-1-1c-3-2-7-1-11-1-1-1-4-2-5-2s-2 0-2 1c-3-5-5-11-7-16-1-4-3-8-6-12z" class="I"></path><path d="M665 670v-1h1c1 1 1 3 1 4s1 2 1 3c0 0-1 1-1 2h-1l-1-1c-1-1-2-2-2-4 0-1 1-2 1-3h1z" class="b"></path><path d="M653 664c3 0 8 0 10 1h3c2 1 3 2 3 3 0 3 0 5-1 8 0-1-1-2-1-3s0-3-1-4h-1v1c-1-1-1-1-2-1h0v-1h4v-1c-5-2-9-1-14-3z" class="S"></path><path d="M632 556h1c2 1 3 1 4 0l2-2 3 6h0c6 5 8 11 9 18 1 6-1 14-4 19-1 1-1 1-2 1l-1 2c2 2 3 5 4 8 2 4 4 7 6 10-2 1-3 1-5 3l10 10c0 1 1 1 2 2l8 5h1 0c1-1 2-1 2-1l2 1c2 1 5 1 8 1l2-1c7 3 18 6 21 14 2 3 2 8 0 11-1 3-4 5-6 6-1 1-2 1-4 0v1l-4-3-11 6-1-1v-2c0-2 1-4 1-6-1-1 0-1 0-2l-1-1v-1c-3-2-7-4-10-5-6-2-12-3-17-5v-2c-6-3-12-7-17-11-1-1-1-2-2-3-2-2-4-4-6-7-1-2-2-4-2-6 1 0 1 0 2 1l-4-7-1-1h0c-1-1-1-2-1-3-1-1-2-3-3-4l-1-2c-1-3-1-5-2-7-1-1-1-1-1-2v-2c-1-1-1-1-1-2v-1c-1-4-1-8-1-12v-1c0-3 1-5 2-8h1c0-1 0-1 1-2 2-1 3-2 4-3l4 2v-1l-1-1h1l-1-2 1 1c2-3 3-5 3-7l1-2 4 1z" class="Z"></path><path d="M639 584v-8l3-2c0 2 1 3 2 4h0c1 4 1 8 0 12-1-1 0-5-1-7v-2l-1 1c-1 1-2 1-3 2z" class="I"></path><path d="M623 563l1 1c6 1 12 2 16 6h-7l-1 1h-1c-3-1-5-3-7-4v-1l-1-1h1l-1-2z" class="D"></path><defs><linearGradient id="Ds" x1="622.635" y1="604.229" x2="631.148" y2="609.146" xlink:href="#B"><stop offset="0" stop-color="#9b9a96"></stop><stop offset="1" stop-color="#c6c5c2"></stop></linearGradient></defs><path fill="url(#Ds)" d="M626 598c1 2 1 6 3 7 0 2 1 4 2 5 1 3 3 7 5 10h-1-1l-2-5c-1 0-1-1-1-1l-1-1-1 1c1 3 3 4 4 7-4-3-6-8-8-12v-1h0c-1-2-2-3-2-4v-1-1h1 0c1-1 2-2 2-3v-1z"></path><path d="M629 585s2 0 2-1c1 0 1-1 3-1v-1c1-1 2-1 3-1v-3l1 1c0 1 1 3 1 5h0c0 2 0 3-1 5h0l-1-3-1 1h0l-1-1c-1 1-1 2-1 3l-1-1c-1 0-2 1-3 2l-1 3c0 1 0 2 1 2v9c-1-1-1-1-1-2v-2c0-2 0-3-1-4-1 3 2 7 1 9-2-1-2-5-3-7v-10h1v1c1-1 1-3 2-4z" class="I"></path><defs><linearGradient id="Dt" x1="624.09" y1="614.843" x2="634.647" y2="625.932" xlink:href="#B"><stop offset="0" stop-color="#b3b2af"></stop><stop offset="1" stop-color="#e3e3e0"></stop></linearGradient></defs><path fill="url(#Dt)" d="M625 608h0v1c2 4 4 9 8 12 2 0 2 2 3 3 0 1 0 3-1 4l-1 1h1v1 1l-6-6-2-3-4-7-1-1c0-3 0-4 1-6h1 1z"></path><path d="M625 608h0v1 3c1 1 1 0 0 2l-1 1h-1l-1-1c0-3 0-4 1-6h1 1z" class="E"></path><path d="M631 571h1l1-1h7c2 2 4 5 4 8h0c-1-1-2-2-2-4l-3 2v8h0 0c0-2-1-4-1-5l-1-1v3c-1 0-2 0-3 1v1c-2 0-2 1-3 1 0 1-2 1-2 1 0-2 0-4 1-6l-1-2h0v-1-2c1-1 1-2 2-3z" class="J"></path><path d="M629 577c2-2 2-2 4-3 0-1 0-1 1-2 0 1 0 1 1 2l1-1c1-2 2-1 4-1v1c-2 0-2 1-4 2v2l-1-1c-2 1-3 2-3 3h-2l-1-2z" class="L"></path><path d="M631 571h1l1-1h7c2 2 4 5 4 8h0c-1-1-2-2-2-4l-2-1v-1c-2 0-3-1-4 1l-1 1c-1-1-1-1-1-2-1 1-1 1-1 2-2 1-2 1-4 3h0v-1-2c1-1 1-2 2-3z" class="Q"></path><path d="M681 651l11 5c0-2 1-3 2-5 1 1 2 2 2 4 1 3 0 6-2 8 0 1-1 2-1 3-1 1 1 3 2 3h0v1l-4-3-11 6-1-1v-2c0-2 1-4 1-6-1-1 0-1 0-2l-1-1v-1c2 1 3 2 5 4v-1h2c1 0 2-2 3-3 0-2-2-4-3-5l-1-1c-1-1-4-2-4-3z" class="F"></path><path d="M685 654c3 1 5 3 6 6 0 1-1 4-2 5-2 3-6 5-9 6l-1-1c0-2 1-4 1-6-1-1 0-1 0-2l-1-1v-1c2 1 3 2 5 4v-1h2c1 0 2-2 3-3 0-2-2-4-3-5l-1-1z" class="G"></path><path d="M659 631c-10-7-19-18-23-30 0-3-1-5 1-7 1 0 5 3 7 4h1l-1 2c2 2 3 5 4 8 2 4 4 7 6 10-2 1-3 1-5 3l10 10z" class="F"></path><path d="M640 605c-1-2-2-4-1-6 2 0 4 0 5 1 2 2 3 5 4 8-3 0-5-3-8-3z" class="K"></path><defs><linearGradient id="Du" x1="646.16" y1="607.245" x2="648.348" y2="619.3" xlink:href="#B"><stop offset="0" stop-color="#838381"></stop><stop offset="1" stop-color="#a9a8a5"></stop></linearGradient></defs><path fill="url(#Du)" d="M640 605c3 0 5 3 8 3 2 4 4 7 6 10-2 1-3 1-5 3-4-5-7-10-9-16z"></path><path d="M635 637c-1-1-1-2-2-3-2-2-4-4-6-7-1-2-2-4-2-6 1 0 1 0 2 1l2 3 6 6c5 6 13 11 21 13 7 2 14 3 21 5 1 0 3 1 4 2 0 1 3 2 4 3l1 1c1 1 3 3 3 5-1 1-2 3-3 3h-2v1c-2-2-3-3-5-4-3-2-7-4-10-5-6-2-12-3-17-5v-2c-6-3-12-7-17-11z" class="a"></path><path d="M652 648l13 4c5 1 10 2 14 5 2 2 3 4 5 6v1c-2-2-3-3-5-4-3-2-7-4-10-5-6-2-12-3-17-5v-2z" class="B"></path><defs><linearGradient id="Dv" x1="608.671" y1="581.976" x2="629.977" y2="592.772" xlink:href="#B"><stop offset="0" stop-color="#424241"></stop><stop offset="1" stop-color="#9c9c98"></stop></linearGradient></defs><path fill="url(#Dv)" d="M614 570h1c0-1 0-1 1-2 2-1 3-2 4-3l4 2c2 1 4 3 7 4-1 1-1 2-2 3v2 1h0l1 2c-1 2-1 4-1 6-1 1-1 3-2 4v-1h-1v10 1c0 1-1 2-2 3h0-1v1 1c0 1 1 2 2 4h-1-1c-1 2-1 3-1 6h0c-1-1-1-2-1-3-1-1-2-3-3-4l-1-2c-1-3-1-5-2-7-1-1-1-1-1-2v-2c-1-1-1-1-1-2v-1c-1-4-1-8-1-12v-1c0-3 1-5 2-8z"></path><path d="M339 458c4 1 8 1 12 1 1 0 3 0 4 1l4 3h0c0 1 1 2 2 3h0v1-1c-1 1-1 2-1 3 3 0 8 1 11-1 1 2 2 3 2 4l6 16c-1-1-1-1-2-1-4-1-11-4-13-8-3 3-5 4-9 6h-1l-1-1-20 2c-5 0-11 1-16 3-14 4-28 9-39 20-2 2-5 4-7 7l-1 1v-1l-1 1c2 2 4 5 6 6l-6 5c-1-2-3-4-4-6l-1-1-1 1-5-5-3 1c-1 2-3 3-4 4-4 5-7 10-9 16-2-4-4-8-4-13-2-7-1-17 4-23h0l4-4 1-1 4-4h1c-1 0 0-1 0-1-1 0-2-1-3-1h-1c6-2 10-6 16-8 3-2 10-4 11-7 1-1 4-2 6-3l3-1 3-1c5-2 10-4 16-5 2-1 5-1 8-2 1-1 2-1 3-1 1 1 2 1 4 1 1-1 2-1 3-1 1-1 3 0 5-1h0 3l1-1h1c2-1 5-1 7-1l1-2z" class="V"></path><path d="M260 513l6-9c1 1 1 1 0 2 0 2 1 4 2 6 1 1 2 3 3 4l-1 1v-1l-1 1c-1 0-3-3-4-4-2 0-3 1-5 1h1l-1-1z" class="M"></path><path d="M255 514c1-4 1-8 1-12 1 1 1 2 1 3 0 3 0 6 3 8l1 1h-1v1c1 2 3 4 4 6l-1 1-5-5-3 1c-1-1 0-2 0-4z" class="F"></path><path d="M254 491l7-2c2 0 3-1 5-1l1 1c-1 1-2 2-4 3-1 0-2-1-3 0-4 2-6 8-8 12 0-3 2-7 2-10-1-1-1 0-3-1h1c0-1 1-1 2-2z" class="S"></path><path d="M260 514c2 0 3-1 5-1 1 1 3 4 4 4 2 2 4 5 6 6l-6 5c-1-2-3-4-4-6l-1-1c-1-2-3-4-4-6v-1z" class="Q"></path><path d="M260 515h1l1 1c1 0 1 1 3 1 1 1 1 2 1 4l-1 1-1-1c-1-2-3-4-4-6z" class="J"></path><path d="M328 475h2 4l1 1h2-1l-1 1 1 1c-1 1-1 1-2 1l2 1h1c1 2 5 1 7 2h-4c-3 1-10 1-12 0s-4-1-6-1c-2 1-6 2-8 1l5-1c2-1 4-1 6-1l-1-1v-1h3v-1h-2 1-3 0-4c2-1 3-1 5-1h0c1-1 3-1 4-1z" class="c"></path><path d="M255 518c-1 2-3 3-4 4-4 5-7 10-9 16-2-4-4-8-4-13-2-7-1-17 4-23h0c-3 7-4 15-3 22v1c0 3 2 6 3 9 2-3 3-6 4-9 2-3 3-6 5-9l-3-6c2 1 4 2 7 4 0 2-1 3 0 4z" class="B"></path><defs><linearGradient id="Dw" x1="356.694" y1="475.253" x2="333.744" y2="480.145" xlink:href="#B"><stop offset="0" stop-color="#9d9c99"></stop><stop offset="1" stop-color="#e2e1d9"></stop></linearGradient></defs><path fill="url(#Dw)" d="M325 472c11-1 21-2 32-1v2h2 1 1l1-1h2 4 5l6 16c-1-1-1-1-2-1-4-1-11-4-13-8-3 3-5 4-9 6h-1l-1-1-20 2c2-2 3-2 6-2h3c2 0 3 0 4-1v-1h-2c-2-1-6 0-7-2h-1l-2-1c1 0 1 0 2-1l-1-1 1-1h1-2l-1-1h-4-2 0v-1h5c1-1 3 0 4 0 2-1 3-1 4-1l-1-1h-3c-2 1-4 0-7 0-1 1-3 1-4 0h-1z"></path><defs><linearGradient id="Dx" x1="362.282" y1="477.069" x2="355.12" y2="478.956" xlink:href="#B"><stop offset="0" stop-color="#797877"></stop><stop offset="1" stop-color="#9c9b96"></stop></linearGradient></defs><path fill="url(#Dx)" d="M362 472h2 4 1 1c1 1 1 1 1 2h-9l1 1 1-1 2 1c0 1 0 1-1 3l-1 1c-3 3-5 4-9 6h-1l-1-1h0c1 0 2-1 2-1l1-1 1 1 1-1-1-1h-1 0l-2-2 1-1h-1v-1c0-2-2-2-3-2h3v-1h-1l4-1h2 1 1l1-1z"></path><path d="M364 472h4 1c-2 2-3 1-5 1h0v-1z" class="g"></path><path d="M362 472h2v1l-4 1h0l-1 1c-1 0-1 0-2-1l2-1h1 1l1-1z" class="G"></path><defs><linearGradient id="Dy" x1="376.104" y1="478.948" x2="362.041" y2="481.031" xlink:href="#B"><stop offset="0" stop-color="#424141"></stop><stop offset="1" stop-color="#6b6a69"></stop></linearGradient></defs><path fill="url(#Dy)" d="M368 472h5l6 16c-1-1-1-1-2-1-4-1-11-4-13-8l1-1c1-2 1-2 1-3l-2-1-1 1-1-1h9c0-1 0-1-1-2h-1-1z"></path><defs><linearGradient id="Dz" x1="301.05" y1="482.483" x2="299.381" y2="465.024" xlink:href="#B"><stop offset="0" stop-color="#a0a09d"></stop><stop offset="1" stop-color="#ceccca"></stop></linearGradient></defs><path fill="url(#Dz)" d="M339 458c4 1 8 1 12 1 1 0 3 0 4 1l4 3h0c0 1 1 2 2 3h0v1-1c-1 1-1 2-1 3 3 0 8 1 11-1 1 2 2 3 2 4h-5-4-2l-1 1h-1-1-2v-2c-11-1-21 0-32 1-20 2-40 7-57 17l-3 3c-2 1-3 3-5 5v-1l3-4c2-1 3-2 4-3l-1-1c-2 0-3 1-5 1l-7 2c-1 1-2 1-2 2-1 0 0-1 0-1-1 0-2-1-3-1h-1c6-2 10-6 16-8 3-2 10-4 11-7 1-1 4-2 6-3l3-1 3-1c5-2 10-4 16-5 2-1 5-1 8-2 1-1 2-1 3-1 1 1 2 1 4 1 1-1 2-1 3-1 1-1 3 0 5-1h0 3l1-1h1c2-1 5-1 7-1l1-2z"></path><path d="M264 483h3l1-1h2 1c0-1 1-1 2-1v1 1h-2c-2 1-3 2-5 3-4 1-9 0-12 5-1 1-2 1-2 2-1 0 0-1 0-1-1 0-2-1-3-1h-1c6-2 10-6 16-8z" class="G"></path><defs><linearGradient id="EA" x1="359.97" y1="474.847" x2="357.03" y2="465.653" xlink:href="#B"><stop offset="0" stop-color="#2b292b"></stop><stop offset="1" stop-color="#4a4a49"></stop></linearGradient></defs><path fill="url(#EA)" d="M371 468c1 2 2 3 2 4h-5-4-2l-1 1h-1-1-2v-2c-11-1-21 0-32 1-20 2-40 7-57 17 1-1 2-2 4-3v-1c9-5 18-8 28-10s21-4 32-5l28-1c3 0 8 1 11-1z"></path><path d="M357 471l5 1-1 1h-1-1-2v-2z" class="L"></path><defs><linearGradient id="EB" x1="360.27" y1="461.446" x2="331.657" y2="466.597" xlink:href="#B"><stop offset="0" stop-color="#656563"></stop><stop offset="1" stop-color="#bebdb7"></stop></linearGradient></defs><path fill="url(#EB)" d="M339 458c4 1 8 1 12 1 1 0 3 0 4 1l4 3h0c0 1 1 2 2 3h0v1-1c-1 1-1 2-1 3l-28 1v-1c0-2-1-4 0-6v-1c-2 1-4 1-6 1v-1h0 3l1-1h1c2-1 5-1 7-1l1-2z"></path><path d="M499 243c1 1 2 3 2 4 1 1 2 1 3 3l2 1c1 0 2 1 3 0 2-1 2-2 2-3l2-1c0 1-1 3 0 5l1-2 1 1 2-1c1 4 2 7 4 11l4 8 3 5c-1 3 0 6 0 9 1 2 2 5 3 7l15 33v1 1c1 2 2 6 3 8h1c1-1 1-1 1-2 1-1 1-1 1-2 0 4 1 8-1 11 0 6-6 11-9 15-2 2-3 5-4 7-2 1-3 3-5 3-4 0-6-2-9-4l-6-8c4-3 9-7 12-11 0-3 0-6-1-9-1-5-5-9-8-13-2-3-4-6-6-8h0c-6 9-16 16-14 28 1 5 8 9 12 13-4 5-9 11-15 12-1 0-2 0-3-1-3-2-5-6-7-9 1 0 1 0 2-1h0v-1-1c-1-3 0-5 1-7l-1-2-1-3c-1-4-1-8 0-13 0-1 0-2 1-3-1-1-1-2-2-3l3-13c0 1-1 1-1 2v1l-1-4c3-8 1-17 1-25l2 2 1-1c1-3 0-6 0-10 1-2 1-5 1-7l3-12 1-3c0-1 0-1 1-2 0-1-1-2 0-3v-3z" class="S"></path><path d="M490 343l2 2 1-1v1c0 1 0 2 1 3s1 2 1 4h-1-2c-1-2-1-2-1-4l1-1-1-2-1-2z" class="b"></path><path d="M489 340h0c2-5 0-11 3-16l1 1c0 1 0 1-1 2v2h0c0 2 0 0-1 2 0 1 1 5 1 6 0 2 0 5 1 7l-1 1-2-2-1-3z" class="c"></path><path d="M504 295c3-5 5-10 7-15 1-5 3-8 3-13l2 6c2 4 4 8 5 12l10 25c2 4 5 9 7 14 1 2 2 4 2 7 1 2 1 7 2 8 0 2 0 3-1 4-1 0-1 0-1-1h-1c0-3 0-5-1-8-3-9-8-17-11-26l-12-27c-5 9-8 20-12 29-2 8-7 15-9 23 0 1 0 1-1 2h0c-1-3 0-8 1-11 1-2 2-3 2-5l-1 2-1-1 10-25z" class="V"></path><path d="M513 252l1-2 1 1v3h1c0 3 0 5-1 7 0 1 0 2 1 3v2 1c0 1 0 1 1 2 0 1 0 3-1 4l-2-6c0 5-2 8-3 13-2 5-4 10-7 15l-10 25c-2 2-3 5-5 7 0-1 0-2 1-3-1-1-1-2-2-3l3-13 12-34c3-7 9-15 10-22z" class="C"></path><path d="M500 295v2c1 3-2 9-3 12h1l2-6c1-2 2-4 2-6l1-2h1l-10 25c-2 2-3 5-5 7 0-1 0-2 1-3 2-4 4-9 5-13l1-3c2-4 2-9 4-13z" class="I"></path><defs><linearGradient id="EC" x1="512.887" y1="263.206" x2="507.27" y2="258.603" xlink:href="#B"><stop offset="0" stop-color="#8c8e8a"></stop><stop offset="1" stop-color="#b3aeae"></stop></linearGradient></defs><path fill="url(#EC)" d="M513 252l1-2 1 1v3l-1 3-10 26c-1-4 2-6 2-9h-1v1h-1l-1-1c3-7 9-15 10-22z"></path><path d="M503 274l1 1h1v-1h1c0 3-3 5-2 9-1 4-3 8-4 12-2 4-2 9-4 13l-1 3c-1 4-3 9-5 13-1-1-1-2-2-3l3-13 12-34z" class="L"></path><path d="M499 243c1 1 2 3 2 4 1 1 2 1 3 3l2 1c1 0 2 1 3 0 2-1 2-2 2-3l2-1c0 1-1 3 0 5-1 7-7 15-10 22l-12 34c0 1-1 1-1 2v1l-1-4c3-8 1-17 1-25l2 2 1-1c1-3 0-6 0-10 1-2 1-5 1-7l3-12 1-3c0-1 0-1 1-2 0-1-1-2 0-3v-3z" class="T"></path><defs><linearGradient id="ED" x1="502.382" y1="264.52" x2="494.118" y2="250.98" xlink:href="#B"><stop offset="0" stop-color="#9a9797"></stop><stop offset="1" stop-color="#b9b9b5"></stop></linearGradient></defs><path fill="url(#ED)" d="M499 243c1 1 2 3 2 4 1 1 2 1 3 3 1 3 2 7 1 10s-2 5-4 7l-4 8v-18l1-6c0-1 0-1 1-2 0-1-1-2 0-3v-3z"></path><path d="M501 267v-3h-1c0-3 0-7 1-9 0 0 0-1 1 0v5h1 2c-1 3-2 5-4 7z" class="E"></path><path d="M501 247c1 1 2 1 3 3 1 3 2 7 1 10h-2-1v-5c0-2-1-6-1-8z" class="I"></path><defs><linearGradient id="EE" x1="545.141" y1="297.624" x2="521.473" y2="301.601" xlink:href="#B"><stop offset="0" stop-color="#b6b5b3"></stop><stop offset="1" stop-color="#dcdcd7"></stop></linearGradient></defs><path fill="url(#EE)" d="M515 251l2-1c1 4 2 7 4 11l4 8 3 5c-1 3 0 6 0 9 1 2 2 5 3 7l15 33v1 1c1 2 2 6 3 8h1c1-1 1-1 1-2 1-1 1-1 1-2 0 4 1 8-1 11h0 0l-1 3s-1 1-1 2c-1 0-1 0-1 1s-1 1-2 2c0-1 0-1-1-1l1 2h-1-1c0-1-1-2-1-2-1-1-1-6-1-7v-1c-1-1-1-6-2-8 0-3-1-5-2-7-2-5-5-10-7-14l-10-25c-1-4-3-8-5-12 1-1 1-3 1-4-1-1-1-1-1-2v-1-2c-1-1-1-2-1-3 1-2 1-4 1-7h-1v-3z"></path><path d="M515 251l2-1c1 4 2 7 4 11h-1v3 1c-2-3-2-6-4-9h0l1-1-1-2v1h-1v-3z" class="I"></path><path d="M525 269l3 5c-1 3 0 6 0 9-2-3-3-6-4-9 1-2 1-3 1-5z" class="J"></path><path d="M521 261l4 8c0 2 0 3-1 5l-4-9v-1-3h1z" class="E"></path><defs><linearGradient id="EF" x1="707.565" y1="441.568" x2="722.107" y2="410.931" xlink:href="#B"><stop offset="0" stop-color="#afaeaa"></stop><stop offset="1" stop-color="#fffffd"></stop></linearGradient></defs><path fill="url(#EF)" d="M700 391h4v1h3 1c1 0 1 0 3 1h3c1 0 1 0 2 1h2c0 1 1 0 2 0l9 3c2 0 5 1 7 2 4 2 10 2 14 2 1 0 2 0 3 1-3 1-5 2-7 4h1c1 0 0 1 1 0 4-1 9-1 12-2h1c-5 1-9 2-13 4 1 1 1 5 2 6 1 0 1 0 2 1l2 4c1 0 2-1 3-1h1l-2 1 1 1 2 1 1 1-1 1 1 2s0 1 1 2l1 3v1l2 2c1 4 1 8 3 12v1l2 1-4 2v1h0l-1 1c0-1-1-1-2-2-2-1-2-2-2-4l-3 3h0c-1-1-1-1-1-2-1 2-1 5-2 6-2-1-3-2-5-2 1 1 3 2 3 4 1 2 1 6 1 9-9-4-18-8-28-11v1s-1 0-2-1h-2l-6-2h-2l-2-1h-3c-4-1-8-1-12-1-1-1-4-1-5-2l1-1v-1c-2-1-3 0-5 0-2-1-5-1-7-1-1 0-3 1-4 0s-3 0-4 0c-4 0-8 1-12-1v-1c-2-2-3-4-4-6h-1c0-2 0-2 1-4 1-1 3-2 5-2 6-1 13-1 20-1h-2c0-2 0-2 1-3h-10-9-1c0-1 1-4 1-5l1-5c3-2 7-1 10-1s4 0 6-1c1 0 5 0 5 1h3c5 0 7-2 10-5l1-1c3-5 4-10 2-16l1-1z"></path><path d="M680 425l14 2-1 1h-3-1l-2 1-6-1h-2c0-2 0-2 1-3z" class="G"></path><path d="M694 427c10 1 19 4 29 8l7 3c2 1 6 2 7 4-5-2-10-5-15-7-7-2-15-4-22-5l-13-1 2-1h1 3l1-1z" class="L"></path><path d="M678 413c1 0 5 0 5 1-2 1-4 1-6 1h-2-1-3c-1 1-1 2-1 4 1 0 1 0 3 1h2c-2 1-4 1-6 2 1 1 1 2 1 3h-9-1c0-1 1-4 1-5l1-5c3-2 7-1 10-1s4 0 6-1z" class="J"></path><path d="M662 415c3-2 7-1 10-1h-4c-1 2-1 4-2 6l2 1c-2 0-5 0-6 2-1 0-1 1-1 2h-1c0-1 1-4 1-5l1-5z" class="G"></path><path d="M700 391h4v1h3 1c1 0 1 0 3 1h3c1 0 1 0 2 1h2c0 1 1 0 2 0l9 3c2 0 5 1 7 2 4 2 10 2 14 2 1 0 2 0 3 1-3 1-5 2-7 4h1c1 0 0 1 1 0 4-1 9-1 12-2h1c-5 1-9 2-13 4 1 1 1 5 2 6 1 0 1 0 2 1l2 4c1 0 2-1 3-1h1l-2 1 1 1 2 1 1 1-1 1 1 2s0 1 1 2l1 3v1l2 2c1 4 1 8 3 12v1l2 1-4 2v1h0l-1 1c0-1-1-1-2-2-2-1-2-2-2-4l-3 3h0c-1-1-1-1-1-2-1-3-3-4-5-6-3-1-7-3-10-5 0-1-1-1-1-1-2-1-1-1-2-1l-2-1c-1-1-2-1-3-2-3 0-5-2-8-3-1 0 0 0-2-1h-1c-3-1-7-2-10-3h0 0c4 0 8 2 12 3 1 1 3 1 5 2 1 1 3 1 4 2 6 2 11 6 17 8h1c0-1-2-1-2-1-1-2 0-4 0-6 0-1 0-2-1-3s-1-2-1-3c-1 0-1-1-2-1v-2l-3-1h-1c-2 1-3 2-5 2l-4-2h0c1-1 2 0 3 0s3-1 4-1v-1l-1-1h-1-2c-2-1-4-2-5-3h-1c-4-2-9-2-12-4-2-1-5-1-6-1-2-1-5 0-7 0s-1-1-2-1c-1-1-4 0-6 0l1-1c3-5 4-10 2-16l1-1z" class="V"></path><path d="M720 394l9 3c2 0 5 1 7 2 4 2 10 2 14 2 1 0 2 0 3 1-3 1-5 2-7 4h1c1 0 0 1 1 0 4-1 9-1 12-2h1c-5 1-9 2-13 4 1 1 1 5 2 6 1 0 1 0 2 1l2 4c1 0 2-1 3-1h1l-2 1 1 1 2 1 1 1-1 1 1 2s0 1 1 2l1 3v1l2 2c1 4 1 8 3 12v1l2 1-4 2v1h0l-1 1c0-1-1-1-2-2-2-1-2-2-2-4l-3 3h0c-1-1-1-1-1-2-1-3-3-4-5-6-3-1-7-3-10-5l9 4c3-1 6-3 9-4-2-4-4-7-7-10-1-1-3-2-3-3-1 0-1-2-1-3-2-3-2-8-4-11-3-8-17-11-24-14z" class="R"></path><path d="M762 440c2 2 3 4 5 5v1l2 1-4 2v1h0l-1 1c0-1-1-1-2-2-2-1-2-2-2-4l-3 3h0c-1-1-1-1-1-2-1-3-3-4-5-6h1v1c2 1 3 2 4 3l1-1c0-1 0-1-1-1 0-1-1-1-1-1h1c1 0 2 0 3-1 0 1 0 1 1 0h2z" class="T"></path><path d="M760 445c3-1 5 0 7 1l2 1-4 2v1h0l-1 1c0-1-1-1-2-2-2-1-2-2-2-4z" class="C"></path><path d="M750 414c1 0 1 0 2 1l2 4c1 0 2-1 3-1h1l-2 1 1 1 2 1 1 1-1 1 1 2s0 1 1 2l1 3v1l2 2c1 4 1 8 3 12-2-1-3-3-5-5-1-3-2-7-4-10-3-6-7-10-8-16z" class="J"></path><path d="M661 429c6-1 13-1 20-1l6 1 13 1c7 1 15 3 22 5 5 2 10 5 15 7 1 1 2 1 3 1l6 4v1c1 0 2 2 3 2 1 1 3 2 3 4 1 2 1 6 1 9-9-4-18-8-28-11v1s-1 0-2-1h-2l-6-2h-2l-2-1h-3c-4-1-8-1-12-1-1-1-4-1-5-2l1-1v-1c-2-1-3 0-5 0-2-1-5-1-7-1-1 0-3 1-4 0s-3 0-4 0c-4 0-8 1-12-1v-1c-2-2-3-4-4-6h-1c0-2 0-2 1-4 1-1 3-2 5-2z" class="V"></path><path d="M692 444h3c1 1 1 1 2 1h2c2 1 4 1 6 1l3 1 1 1 11 3c2 0 4 0 5 1v1s-1 0-2-1h-2l-6-2h-2l-2-1h-3c-4-1-8-1-12-1-1-1-4-1-5-2l1-1v-1z" class="S"></path><defs><linearGradient id="EG" x1="662.269" y1="432.472" x2="686.718" y2="438.287" xlink:href="#B"><stop offset="0" stop-color="#9c9a97"></stop><stop offset="1" stop-color="#dad9d4"></stop></linearGradient></defs><path fill="url(#EG)" d="M661 429c6-1 13-1 20-1l6 1 13 1h-1c-2 1-3 1-5 0h-4c-1-1-4-1-5-1h-2c0 2 1 3 2 4 2 2 7 2 9 2h2l2 1c-3 2-14-1-16 2 3 1 8 1 11 1h2l1 1h1l-1 1c0-1-1-1-2-1h-2-2-1l-1-1h-5c-1 1-1 2-1 3h1c1 0 0 0 1 1h5c1 0 1 1 3 0 1 0 2 1 3 1h-3c-2-1-3 0-5 0-2-1-5-1-7-1-1 0-3 1-4 0s-3 0-4 0c-4 0-8 1-12-1v-1c-2-2-3-4-4-6h-1c0-2 0-2 1-4 1-1 3-2 5-2z"></path><path d="M656 435h-1c0-2 0-2 1-4 1-1 3-2 5-2 0 4-1 8-1 12-2-2-3-4-4-6z" class="Q"></path><defs><linearGradient id="EH" x1="321.263" y1="195.011" x2="303.989" y2="121" xlink:href="#B"><stop offset="0" stop-color="#adaca9"></stop><stop offset="1" stop-color="#fffffe"></stop></linearGradient></defs><path fill="url(#EH)" d="M269 127c7-2 16-4 23-4l1 1 6 1c15-1 30 0 45 3 10 3 20 7 30 11 12 6 24 13 34 21 2 2 5 3 6 5h0c1 2 2 3 3 5-1 0-3 2-5 2-1 0-1-1-2 0l-3 3c-1 1-4 0-6 1l-1-1v1c-3 0-5-1-7-2v2c-1 1-1 1-3 0h0c-23-17-52-26-81-27h-5c-5 0-11 0-17 1-1 0-2 0-3-1l-4-1c-1 1-3 1-5 2l1 1c-3 0-12 3-13 5h0l-2 1-5 3h-4c-2 1-4 0-5 0-4 0-8 0-12-1l-13-2c-2 0-4-1-5-1-4 1-7 3-11 4-2 0-5-1-7-1-7-1-14-5-19-9h0c-2-1-4-3-6-4-2-2-4-5-6-6l-1 1-1-1h-1-1c1-2 2-3 3-4 4-1 8 3 11 4 2 0 2 1 4 0-2-1-3-3-4-5h1c3 4 9 11 14 12 4 1 8-1 11 1 1 0 1 1 1 1-1 2-1 3-2 5 3-1 6-3 10-5l14-7c14-7 28-12 42-15z"></path><path d="M269 127c7-2 16-4 23-4l1 1 6 1-27 3c-1-1-2-1-3-1z" class="G"></path><path d="M215 155c1 0 2-1 3-1l1 1 1-1c1-1 2-1 4-2 0 0 0-1 1-1v-1c1 0 1-1 2-1 0 1 1 1 1 2h1v3c1 1 2 1 4 2h0l1 1h5c1-1 2-1 3-1h0l1-1h0 1c1-1 2-1 2-1 1-1 2-1 3-1l-10 4c-4 2-9 0-13-1-3 0-7-1-11-1z" class="h"></path><path d="M249 153c1 0 2 0 3-1l8-3c9-3 18-6 26-7h2 6c10-2 22-1 32 0 3 1 7 2 11 2 1 1 2 1 4 2-6-1-12-3-18-3-12-2-25-2-37 0-16 3-32 9-47 16 3 0 5 1 7 1h1 0c-4 0-8 0-12-1l-13-2c-2 0-4-1-5-1-4 1-7 3-11 4-2 0-5-1-7-1l2-2c1 0 2-1 3-1 4 1 8-1 11-1 4 0 8 1 11 1 4 1 9 3 13 1l10-4z" class="O"></path><path d="M269 127c1 0 2 0 3 1-19 5-36 10-53 19-5 3-9 6-15 9-1 0-2 1-3 1l-2 2c-7-1-14-5-19-9 3 0 5 2 8 3 2 1 7 4 10 2 2-1 4-3 5-6v-1l-8 1c-5 0-9-5-13-9-2-1-3-3-4-5h1c3 4 9 11 14 12 4 1 8-1 11 1 1 0 1 1 1 1-1 2-1 3-2 5 3-1 6-3 10-5l14-7c14-7 28-12 42-15z" class="N"></path><path d="M180 150h0c-2-1-4-3-6-4-2-2-4-5-6-6l-1 1-1-1h-1-1c1-2 2-3 3-4 4-1 8 3 11 4 2 0 2 1 4 0 4 4 8 9 13 9l8-1v1c-1 3-3 5-5 6-3 2-8-1-10-2-3-1-5-3-8-3z" class="a"></path><path d="M246 160c-2 0-4-1-7-1 15-7 31-13 47-16 12-2 25-2 37 0 6 0 12 2 18 3 1 1 3 1 5 1l15 6 5 1c2 1 4 3 6 3 1 1 2 2 3 2l8 5 7 4 2 2 3 2 5 4c-3 0-5-1-7-2v2c-1 1-1 1-3 0h0c-23-17-52-26-81-27h-5c-5 0-11 0-17 1-1 0-2 0-3-1l-4-1c-1 1-3 1-5 2l1 1c-3 0-12 3-13 5h0l-2 1-5 3h-4c-2 1-4 0-5 0h0-1z" class="Z"></path><path d="M246 160c10-3 19-9 29-10l1 1c-3 0-12 3-13 5h0l-2 1-5 3h-4c-2 1-4 0-5 0h0-1z" class="B"></path><path d="M280 148c36-8 75 2 106 21 2 1 5 3 7 5v2c-1 1-1 1-3 0h0c-23-17-52-26-81-27h-5c-5 0-11 0-17 1-1 0-2 0-3-1l-4-1z" class="F"></path><defs><linearGradient id="EI" x1="690.193" y1="192.961" x2="717.497" y2="140.171" xlink:href="#B"><stop offset="0" stop-color="#7b7b7e"></stop><stop offset="1" stop-color="#989791"></stop></linearGradient></defs><path fill="url(#EI)" d="M699 123c2-1 6 0 8 0l1 1c3 0 7 0 9 1h3v-1c1-1 2-1 4-1l11 2c13 2 26 6 38 11 5 2 10 4 14 7l7 3c1 1 2 2 3 2 4 3 9 6 15 8l-1 1h-1l-1 1 1 1-3 1-15-3c-5 0-10 1-15 2l-12 1c-2-1-3-1-5-1-3 1-5 1-8 2s-5 2-8 3-8-2-11-2c-12-2-23 1-34 5l-2-1v1h-1c-2-1-8 2-11 4 0 0-1 1-2 1s-5 3-5 3l4 1h-38c-3 0-9 1-12-1l-2 1c0-1 0-2-1-2-2 1-1-1-2-1 0 0-1 1-2 1-1 1-5 0-6 0v-1c-2 0-3 0-4 1h-1-5c-2 0-4 0-6-1 4-4 8-8 13-12 0-1 3-2 4-3 6-5 13-9 20-13l-1-1c12-6 25-11 38-15 6-2 12-4 17-4 2 0 4-1 6-1l-1-1z"></path><path d="M744 153h4l5 2c1 0 2 2 3 3-9-2-17-4-26-4h2c1-1 2-1 4-1 1 2 6 1 9 1v1l1-1-2-1z" class="P"></path><path d="M686 153c10-3 22-4 32-4 5 0 10 0 15 1 1 0 2 1 3 1 3 1 6 1 8 2l2 1-1 1v-1c-3 0-8 1-9-1-2 0-3 0-4 1h-2c-4 1-7 1-11 1l-14 2c-3 1-6 2-9 2 3-2 9-2 12-3s7-1 10-2h4v-1c-2 0-3 0-4 1h-5c-3 1-8 2-11 2v-1c1 0 3 0 4-1h1c3-1 6 0 8-1v-1-1h-13c-4 1-8 2-12 2h-4 0z" class="U"></path><path d="M690 153c4 0 8-1 12-2h13v1 1c-2 1-5 0-8 1h-1c-1 1-3 1-4 1v1c-9 3-18 5-26 9-3 1-7 2-10 4-2 1-5 3-7 3-2 1-4 2-7 3h18c2 0 5 1 6 0 3-1 4-2 7-3h0c-1 0-5 3-5 3l4 1h-38c-2-1-5 0-7-1 4-2 8-5 12-8 9-5 19-9 29-12 3-1 5-2 8-2h0 4z" class="X"></path><path d="M658 166h0c4-2 11-6 15-5l-14 7h-1l1-1h0c-1-1 0-1-1-1z" class="K"></path><path d="M690 153c4 0 8-1 12-2h13v1h-13c-7 1-15 3-22 5l-1-1c4-1 7-2 11-3z" class="P"></path><path d="M644 176c-2-1-5 0-7-1 4-2 8-5 12-8 9-5 19-9 29-12 3-1 5-2 8-2h0 4c-4 1-7 2-11 3l1 1c-3 2-5 3-7 4-4-1-11 3-15 5h0 0c-4 3-8 5-12 8h0 2l1-1c0-1 2-1 3-1h0c-1 1-1 1-2 1l-1 1c3 1 7-2 10-2-2 1-4 2-7 3h18c2 0 5 1 6 0 3-1 4-2 7-3h0c-1 0-5 3-5 3l4 1h-38z" class="H"></path><defs><linearGradient id="EJ" x1="699.974" y1="174.195" x2="700.062" y2="142.926" xlink:href="#B"><stop offset="0" stop-color="#c3c2be"></stop><stop offset="1" stop-color="#f6f5f5"></stop></linearGradient></defs><path fill="url(#EJ)" d="M693 144c1-1 2-1 4-1 3-1 7-1 10-1h10c14 0 27 3 40 8 6 2 12 6 18 8 1 1 1 0 2 1l-12 1c-2-1-3-1-5-1l-4-1c-1-1-2-3-3-3l-5-2h-4c-2-1-5-1-8-2-1 0-2-1-3-1-5-1-10-1-15-1-10 0-22 1-32 4-3 0-5 1-8 2-10 3-20 7-29 12-4 3-8 6-12 8 2 1 5 0 7 1-3 0-9 1-12-1l-2 1c0-1 0-2-1-2 3-4 7-7 11-10h0s1 0 1-1c2 0 3-1 4-2 4-2 9-5 14-7h2l7-3 9-4h4c1-1 0-1 2-1h2c1-1 1-1 2-1h2l1-1h3z"></path><path d="M632 175c5-4 11-8 16-11 27-16 60-22 90-16 6 1 13 3 19 5 3 2 7 4 11 5h7c1 1 1 0 2 1l-12 1c-2-1-3-1-5-1l-4-1c-1-1-2-3-3-3l-5-2h-4c-2-1-5-1-8-2-1 0-2-1-3-1-5-1-10-1-15-1-10 0-22 1-32 4-3 0-5 1-8 2-10 3-20 7-29 12-4 3-8 6-12 8 2 1 5 0 7 1-3 0-9 1-12-1z" class="B"></path><defs><linearGradient id="EK" x1="605.738" y1="162.469" x2="699.729" y2="138.909" xlink:href="#B"><stop offset="0" stop-color="#b0afa9"></stop><stop offset="1" stop-color="#f9f9f3"></stop></linearGradient></defs><path fill="url(#EK)" d="M699 123c2-1 6 0 8 0l1 1c3 0 7 0 9 1-8 0-16 0-24 1v1h-1c1 3 4 4 5 6 1 1 1 2 2 2v2 1c-1 0-3 2-3 4l1-1 2 1c-3 1-4 1-6 2h-3l-1 1h-2c-1 0-1 0-2 1h-2c-2 0-1 0-2 1h-4l-9 4-7 3h-2c-5 2-10 5-14 7-1 1-2 2-4 2 0 1-1 1-1 1h0c-4 3-8 6-11 10-2 1-1-1-2-1 0 0-1 1-2 1-1 1-5 0-6 0v-1c-2 0-3 0-4 1h-1-5c-2 0-4 0-6-1 4-4 8-8 13-12 0-1 3-2 4-3 6-5 13-9 20-13l-1-1c12-6 25-11 38-15 6-2 12-4 17-4 2 0 4-1 6-1l-1-1z"></path><path d="M610 171l3-2c0-1 0-1 1 0-2 1-3 3-4 4l8-5 1 1c-2 2-4 3-6 4l2 1h-1-5l1-3z" class="E"></path><path d="M603 173c4-4 8-8 13-12-1 2-8 8-8 9h1l1 1-1 3c-2 0-4 0-6-1z" class="J"></path><path d="M619 173h1 2l1-1 5-2 1 1c4-2 7-5 11-7-4 3-8 6-11 10-2 1-1-1-2-1 0 0-1 1-2 1-1 1-5 0-6 0v-1z" class="a"></path><path d="M699 123c2-1 6 0 8 0l1 1c3 0 7 0 9 1-8 0-16 0-24 1-4 1-9 2-14 3-13 4-26 9-39 16l-1-1c12-6 25-11 38-15 6-2 12-4 17-4 2 0 4-1 6-1l-1-1z" class="O"></path><path d="M724 123l11 2c13 2 26 6 38 11 5 2 10 4 14 7l7 3c1 1 2 2 3 2 4 3 9 6 15 8l-1 1h-1l-1 1 1 1-3 1-15-3c-5 0-10 1-15 2-1-1-1 0-2-1-6-2-12-6-18-8-13-5-26-8-40-8h-10c-3 0-7 0-10 1-2 0-3 0-4 1 2-1 3-1 6-2l-2-1-1 1c0-2 2-4 3-4v-1-2c-1 0-1-1-2-2-1-2-4-3-5-6h1v-1c8-1 16-1 24-1h3v-1c1-1 2-1 4-1z" class="h"></path><path d="M724 123l11 2-1 1-14-1v-1c1-1 2-1 4-1z" class="T"></path><path d="M735 125c13 2 26 6 38 11 5 2 10 4 14 7l7 3c1 1 2 2 3 2 4 3 9 6 15 8l-1 1h-1l-1 1 1 1-3 1-15-3c-5 0-10 1-15 2-1-1-1 0-2-1-6-2-12-6-18-8-13-5-26-8-40-8h-10c-3 0-7 0-10 1-2 0-3 0-4 1 2-1 3-1 6-2h0 5 15c2 0 5-1 8 0l11 1 20 6 14 6c1 1 4 2 5 3 1 0 3-1 3-1 3 0 6-1 9-1 7 0 13 2 20 2-22-16-48-28-75-32l1-1z" class="N"></path><defs><linearGradient id="EL" x1="564.419" y1="438.848" x2="515.35" y2="419.086" xlink:href="#B"><stop offset="0" stop-color="#b9b9b4"></stop><stop offset="1" stop-color="#fefdfa"></stop></linearGradient></defs><path fill="url(#EL)" d="M552 322l1-2h1l3 6v16 4c1 2 2 3 3 4l2 1c3 1 4 1 7 0h0l3-1c6-1 12-5 16-9l1 1c4-2 7-3 11-5v3l1 1 1-1-1 5-3 9v1c-7 2-13 6-20 9 0 2 0 4-1 6l-2 1-1 1h0l5 9h0c-7 3-16 11-21 17h1c2-2 4-4 7-6v5l1 3-7 7 1 1c-3 3-7 8-8 13 2-2 4-7 6-8h1c1-1 2-1 3-2l5 5-1 1c-1 0-1 1-1 1v1c-5 5-9 10-13 15-11 19-10 40-11 61l1 17c0 2 0 5-1 8-2 2-5 3-7 6l-2 1v1l2 33h-2l-1-30-1-10c-1 1-1 2-1 4h0c-1 3-1 5-4 6-1 1-1 1-3 0v-1c-1-1-2-1-3-1v6c-1-4-1-9-1-12l1-31-1-13c1-31-1-62 9-92 3-9 6-17 11-24l-1-1c1-2 2-5 4-7 3-4 9-9 9-15 2-3 1-7 1-11 0-1 0-2-1-3 1-2 1-3 1-4z"></path><path d="M552 368c-1-1 0-1-1-1l1-2v-1h-1l-1 1c-3 0-4 3-6 4l4-8 1-1h-1s-1 0-2 1c1-2 2-4 3-5h1 1c0 2-1 4-1 5h1 2v-1h1c1 0 1 0 2-1l1 1-4 4c2 0 2-2 3 0l-4 4z" class="C"></path><path d="M551 347c4 3 1 7 7 10v1l-1 2-1-1c-1 1-1 1-2 1h-1v1h-2-1c0-1 1-3 1-5h-1-1c1-1 1-1 1-2 1-2 0-3 0-4h-2l3-3z" class="I"></path><path d="M575 365l3-1c0 2 0 4-1 6l-2 1-1 1h0l-3 2-6 3h0c1-1 1-2 2-2h1c0-1 0-2-1-3-2-1-2 1-4 1 1-1 3-2 5-3 1-1 1-1 1-2-1 0-1 0-2-1h0l1-1h0v-1l1-1 1 1h3v1c1 0 1-1 2-1z" class="E"></path><path d="M575 365l3-1c0 2 0 4-1 6l-2 1-1 1h0l-3 2v-4-2l2-2c1 0 1-1 2-1z" class="J"></path><path d="M575 365l3-1c0 2 0 4-1 6l-2 1-1 1c1-2 1-5 1-7z" class="G"></path><path d="M553 345c1-1 1-1 2-1l1 1c1-1 1-2 1-3v4c1 2 2 3 3 4l2 1c3 1 4 1 7 0l-1 3h-5s1 2 2 2c0 1 1 0 1 1l-10 7c-1-2-1 0-3 0l4-4 1-2v-1c-6-3-3-7-7-10l2-2z" class="E"></path><path d="M553 345c1-1 1-1 2-1l1 1c1-1 1-2 1-3v4c1 2 2 3 3 4l2 1c3 1 4 1 7 0l-1 3h-5-2c-3-2-5-4-7-8 0-1 0-1-1-1z" class="D"></path><path d="M552 322l1-2h1l3 6v16c0 1 0 2-1 3l-1-1c-1 0-1 0-2 1l-2 2-3 3-9 13-1-1c1-2 2-5 4-7 3-4 9-9 9-15 2-3 1-7 1-11 0-1 0-2-1-3 1-2 1-3 1-4z" class="R"></path><path d="M588 341l1 1h0c-2 2-5 4-5 7h0l-3 2-16 8c-4 2-7 4-11 8-5 5-9 10-12 16-2 4-4 8-6 13 0 1-2 7-3 7 3-12 10-27 19-35l4-4 10-7c0-1-1 0-1-1-1 0-2-2-2-2h5l1-3h0l3-1c6-1 12-5 16-9z" class="W"></path><path d="M531 484c0-12 1-23 2-34 1-4 1-8 2-12h1l-3 56v33 1l2 33h-2l-1-30-1-10c-1 1-1 2-1 4h0-1 0c-1-1 0-6 1-8s0-5 0-7v-14c0-4 0-9 1-12z" class="T"></path><path d="M530 496c0-4 0-9 1-12v37c-1 1-1 2-1 4h0-1 0c-1-1 0-6 1-8s0-5 0-7v-14z" class="L"></path><defs><linearGradient id="EM" x1="541.903" y1="522.915" x2="532.316" y2="499.91" xlink:href="#B"><stop offset="0" stop-color="#777775"></stop><stop offset="1" stop-color="#b7b6b3"></stop></linearGradient></defs><path fill="url(#EM)" d="M533 494c1 2 1 3 1 5 2 0 5-2 7-2 0-1 0-1 1-2l1 17c0 2 0 5-1 8-2 2-5 3-7 6l-2 1v-33z"></path><defs><linearGradient id="EN" x1="523.628" y1="529.945" x2="525.254" y2="504.237" xlink:href="#B"><stop offset="0" stop-color="#848481"></stop><stop offset="1" stop-color="#b5b4b0"></stop></linearGradient></defs><path fill="url(#EN)" d="M520 492v-6h0c1 6 1 12 1 18h9v-8h0v14c0 2 1 5 0 7s-2 7-1 8h0 1c-1 3-1 5-4 6-1 1-1 1-3 0v-1c-1-1-2-1-3-1v6c-1-4-1-9-1-12l1-31z"></path><path d="M529 525h0 1c-1 3-1 5-4 6-1 1-1 1-3 0v-1c-1-1-2-1-3-1v-3c1 2 3 4 5 5 1-1 1-1 2-1 1-1 1-3 2-5z" class="G"></path><defs><linearGradient id="EO" x1="584.368" y1="346.147" x2="574.466" y2="364.255" xlink:href="#B"><stop offset="0" stop-color="#686865"></stop><stop offset="1" stop-color="#9b9b97"></stop></linearGradient></defs><path fill="url(#EO)" d="M600 337v3l1 1 1-1-1 5-3 9v1c-7 2-13 6-20 9l-3 1c-1 0-1 1-2 1v-1h-3l-1-1-1 1-3-1c-3 0-4 1-6 2-1 0-3 1-5 1 4-4 7-6 11-8l16-8 3-2h0c0-3 3-5 5-7h0c4-2 7-3 11-5z"></path><path d="M584 349h1l1 1c-1 1-2 1-3 2l-2-1 3-2z" class="U"></path><path d="M598 346c1 0 1 0 3-1l-3 9h-4 0c-1 1-2 2-3 2h-1l1-2v-2c-1 1-2 1-3 1l4-3c2 0 4-2 6-4z" class="H"></path><path d="M591 352c1-1 3-1 4-1h3c-1 2-4 2-4 3h0c-1 1-2 2-3 2h-1l1-2v-2z" class="Y"></path><path d="M601 341l1-1-1 5c-2 1-2 1-3 1-2 2-4 4-6 4-1 0-2 0-3-1l1-1h-2v-1l2-1c4-2 8-3 11-5z" class="U"></path><path d="M590 348c2 0 6-2 8-2-2 2-4 4-6 4-1 0-2 0-3-1l1-1z" class="Y"></path><path d="M600 337v3l1 1c-3 2-7 3-11 5l-2 1c-1 1-3 1-3 2h-1 0c0-3 3-5 5-7h0c4-2 7-3 11-5z" class="M"></path><path d="M574 372l5 9h0c-7 3-16 11-21 17-1 0-3 3-4 3-6 8-11 16-15 25v-6c2-19 12-31 26-43l6-3 3-2z" class="T"></path><path d="M319 107c11-3 21-4 32-4 11 1 21 3 31 5 20 5 39 11 58 18 11 4 23 8 34 14l9 6h0c-7 0-14 3-21 3h1c5 3 10 7 15 11 16 14 28 31 34 51l2 10-1 1c-1 3 0 5 1 8v2c-2 1-7-1-9-2l8 9-2 9c0 1 0 2-2 3-1 1-2 0-3 0l-2-1c-1-2-2-2-3-3 0-1-1-3-2-4l1-2v-3c1-5 1-9 1-13 0-2 0-4-1-6v-1-2-1c0-1 0-2-1-2v-3l-1-3c-1 1-1 4-1 5-1 4-3 11-5 14-2-1-4-1-6-2-2 0-3 1-4 2-2-2-2-5-3-7l-4-9c-2-5-1-8 1-13h-1l-1 1h-1c-2-2-2-5-2-8h-1v2l-2-2c-2-3-4-5-5-7-1-1-1-3-2-4-1-3-2-6-4-8-3-5-6-8-10-12-8-7-16-14-26-19-39-21-85-22-129-17-7 0-16 2-23 4-14 3-28 8-42 15l-14 7c-4 2-7 4-10 5 1-2 1-3 2-5v1c19-15 39-25 62-33l25-8c6-1 12-4 18-5 1-1 2-1 3 0 0 1 0 2 1 2h4l1 1z" class="V"></path><path d="M488 200c2 7 2 11 0 17-1-3 0-6 0-9v-8z" class="C"></path><path d="M463 165c4 2 7 5 10 9l1 5h-1c-2-1-3-2-4-4h0c0 1 0 1 1 2l1 5v7 1h-1c0-6-1-12-3-17-1-3-3-5-4-8h0z" class="a"></path><path d="M471 190v-1-7l-1-5c-1-1-1-1-1-2h0c1 2 2 3 4 4h1c0 2 1 4 1 6 1 3 1 6 2 8h0l-2 4-1 1h-1c-2-2-2-5-2-8z" class="S"></path><path d="M463 165l-1-2h0c10 6 18 14 23 26h-1c0-1-1-1-1-2-1 1-4 5-4 6l-3 4h-1l2-4h0c-1-2-1-5-2-8 0-2-1-4-1-6l-1-5c-3-4-6-7-10-9z" class="R"></path><path d="M473 174c4 4 5 6 7 10l-3 9h0c-1-2-1-5-2-8 0-2-1-4-1-6l-1-5z" class="f"></path><path d="M205 150c19-15 39-25 62-33l25-8c6-1 12-4 18-5 1-1 2-1 3 0 0 1 0 2 1 2h4l1 1-12 4c1-2 4-4 5-6-1-1-3 0-4 0-6 2-12 5-18 7-22 7-43 13-63 25-7 4-14 10-21 14h-1v-1z" class="M"></path><defs><linearGradient id="EP" x1="477.156" y1="193.955" x2="488.702" y2="221.445" xlink:href="#B"><stop offset="0" stop-color="#b7b7b2"></stop><stop offset="1" stop-color="#f4f1ec"></stop></linearGradient></defs><path fill="url(#EP)" d="M479 193c0-1 3-5 4-6 0 1 1 1 1 2h1c1 3 3 8 3 11v8c0 3-1 6 0 9-1 3-2 4-2 7-2 0-3 1-4 2-2-2-2-5-3-7l-4-9c-2-5-1-8 1-13l3-4z"></path><path d="M479 193c1 0 1 0 2 1v1c0 1-1 5 0 6 0 1 0 1 1 2 0 0-1 2-1 3l-1 10-1 3-4-9c-2-5-1-8 1-13l3-4z" class="S"></path><path d="M481 201c0 1 0 1 1 2 0 0-1 2-1 3l-1 10-1-2c0-3 0-6 1-9 0-1 0-2 1-4z" class="C"></path><path d="M558 137c13-6 26-11 40-16 20-7 40-14 62-16 21-3 44-1 64 6l26 9c11 5 22 10 33 16 5 3 10 6 15 10h0c1 2 1 2 2 3h0l-1-1h-2c-1 0-2-1-3-2l-7-3c-4-3-9-5-14-7-12-5-25-9-38-11l-11-2c-2 0-3 0-4 1v1h-3c-2-1-6-1-9-1l-1-1c-2 0-6-1-8 0h-4c-3-2-10-1-14-1h-5l-4 2c-4 0-7 2-11 3l-9 3c-1 1-3 2-5 2h0-3 0-1c-1 0-3 1-4 2v-2c-4 1-8 1-11 1-2 0-4 1-6 1 2-1 5-2 7-3l4-1v-1c-17 6-32 14-46 25-6 5-12 12-16 19-1 2-1 3-2 5-1 4-4 6-6 10-1 3 1 6 2 9 1 6 2 12 4 18 2 3 4 7 6 9-1 1-1 2-2 2-2 2-4 2-5 2-3-2-4-5-5-8l-1 3c0 1-1 1-2 3-1-1-1 0-2-1v-2c0-5-1-8-2-12s0-8-1-12l1-3h0c-1 1-1 2-2 2-2 0-3-1-4-2l-1 1c2 2 4 5 5 8h0c0 4-4 10-6 13-1 3-1 5-2 7-2 0-3-1-4-1s-3 1-4 2c-2-1-3-3-4-4-1-4-2-8-2-12v-2-2h-1c-5 9-5 22-3 32l1 5-1 1h0c-3 0-6 1-8 3v1h-1l-1-1c0-1 0 0-1-1h0l-1 1 1 3-2 1-1-1-1 2c-1-2 0-4 0-5l-2 1 2-9-8-9c2 1 7 3 9 2v-2c1 0 1 0 2-1 0-2-1-5 0-7h0l-1-2c1-10 5-18 9-26 7-15 16-26 29-36l9-7c1-1 4-2 4-3-6 1-13-1-19-3 4-3 8-5 11-8z" class="Z"></path><path d="M537 201c2 1 1 8 3 11h1v3h-2c-1-2-1-3-2-5h0v-9z" class="c"></path><path d="M670 112c9-1 18 0 27 1 2 0 5-1 7 0 2 0 4 0 5 1h0c-1-1-3-1-5-1h-36c-2 1-4 0-6 1l-10 1-15 4c-2 0-4 1-5 0h0c1-1 3-1 5-2l14-2 5-1 6-1 8-1z" class="C"></path><path d="M516 229v3c2 1 6-1 7-1l4-1c-4 2-8 5-10 8v8h0l-1 1 1 3-2 1-1-1-1 2c-1-2 0-4 0-5l-2 1 2-9-8-9c2 1 7 3 9 2v-2c1 0 1 0 2-1z" class="B"></path><path d="M513 247c0-2 0-5 1-7h0l2 7 1 3-2 1-1-1-1 2c-1-2 0-4 0-5z" class="f"></path><defs><linearGradient id="EQ" x1="553.691" y1="214.821" x2="565.492" y2="207.995" xlink:href="#B"><stop offset="0" stop-color="#abaaa8"></stop><stop offset="1" stop-color="#d2d1ca"></stop></linearGradient></defs><path fill="url(#EQ)" d="M557 185l1 1c0 3 0 7-1 10l-1 5c2-1 3-2 5-3v-3h0c1 3 1 5 1 8v5c0 3 0 7 1 10v2l-1 3c0 1-1 1-2 3-1-1-1 0-2-1v-2c0-5-1-8-2-12s0-8-1-12l1-3c1-3 1-7 1-11z"></path><path d="M545 192c0-1-1-1-1-1v-1c2-11 12-21 20-27-4 7-6 14-7 22h0c0 4 0 8-1 11h0c-1 1-1 2-2 2-2 0-3-1-4-2l-1 1c-1-2-2-4-4-5z" class="M"></path><path d="M548 189c1-5 3-9 5-13h1l1-1c-2 5-3 9-4 14v5c0 1-1 1-1 2h0l-1 1c-1-2-2-4-4-5l1-2c0-2 0-2 1-2l1 1z" class="f"></path><path d="M545 192l1-2c0-2 0-2 1-2l1 1c0 2 1 4 2 7h0l-1 1c-1-2-2-4-4-5z" class="N"></path><path d="M555 175l6-9c-2 6-4 12-4 18-1 4 0 9-1 12-1 1-1 2-2 2-2 0-3-1-4-2h0c0-1 1-1 1-2v-5c1-5 2-9 4-14z" class="b"></path><path d="M633 129c5-2 11-3 16-4 25-5 49-5 75-2-2 0-3 0-4 1v1h-3c-2-1-6-1-9-1l-1-1c-2 0-6-1-8 0h-4c-3-2-10-1-14-1h-5l-4 2c-4 0-7 2-11 3l-9 3c-1 1-3 2-5 2h0-3 0-1c-1 0-3 1-4 2v-2c-4 1-8 1-11 1-2 0-4 1-6 1 2-1 5-2 7-3l4-1v-1z" class="M"></path><path d="M676 122l-4 2c-4 0-7 2-11 3l-1-1c-1 1-4 2-5 1-2 0-2-1-3-1h-4c4-1 9-2 14-2l14-2z" class="L"></path><path d="M637 129l11-3h4c1 0 1 1 3 1 1 1 4 0 5-1l1 1-9 3c-1 1-3 2-5 2h0-3 0-1c-1 0-3 1-4 2v-2c-4 1-8 1-11 1-2 0-4 1-6 1 2-1 5-2 7-3l4-1 4-1z" class="C"></path><path d="M633 130l4-1c1 0 1 0 2 1h0c1-1 3-1 4-1-3 2-6 3-9 3-2 0-3 0-5-1l4-1z" class="d"></path><path d="M637 129l11-3h4c1 0 1 1 3 1 1 1 4 0 5-1l1 1-9 3c-1 1-3 2-5 2h0-3 0-1c1-1 3-1 5-2 2 0 3-1 5-2-4 0-7 1-10 1-1 0-3 0-4 1h0c-1-1-1-1-2-1z" class="E"></path><path d="M260 253c2 0 3-1 4 0 1 0 1 0 2 1l-1 2 2 2c0-1 1-1 1-2l1 1c1-1 2-3 3-3 1 1 1 2 1 3 1 5 3 10 3 15v2c1-1 1-2 1-3 2 0 3 0 5 2 1 1 1 2 1 4 2-2 3-4 5-5l4-2 4-1 2-1c0 1 0 1-1 2-7 1-12 7-16 13h1c3 0 5 0 7 3h0c0 1-1 2-2 3l-1 2-1-1v2c1 0 2 0 4-1h3 0c1 1 2 2 3 2v2l-2 1c1 0 1 0 1 2 0 1-2 3-3 5h3c2-1 8 0 10 1l1 1v3c-3 2-6 2-9 4h1l3-1 1 1c-1 0-2 1-3 2h1c1-1 1-1 3 0h0l1 1s1 0 2 1h0c-1 1-2 1-3 1l3 2h0c-2 3-2 3-2 6l4 1h3c-1 0-2 0-2 1h1l-1 1c-2 1-2 2-3 3-4 1-8 6-9 9-3 3-4 7-8 10l1 1 2 1c-1 2-2 2-3 4v1l1 3 2 1v1h0c-1-1-7-1-9-1 2 1 8 1 10 2 0 0-2 2-3 2l-11 11 2 1c-1 2-3 4-4 6l-2 1h-1c-3-2-7-4-11-5l-8-3c-8-2-15 0-23 1h0-8-4c-4-1-7-2-10-3h0c-1 0-2 0-3-1l-8-4-2-1-5-3c-2-1-3-2-4-2l-6-5-1-1c-2 0-1 1-2 0 0-1-1-1-1-2h-1l-1-1-3-3c-2 0-1 1-2 0l-4-4-3-3c-4-2-7-6-10-9 0 0-2-1-2-2l-1-1c-1-1-2-2-2-4l-2-2-3-3-3-6-1-2c0-1 0-1-1-2v-1-2h-1v-2c-1-1-1-1-1-2v-3c-1-2-1-2-1-3v-1-3c1-1 1-2 1-3-1-2-1-6-1-8v-2c0-2 0-5-1-7 0-1-1-4 0-6v1h1c2 1 3 2 5 2h1c0 13 0 29 9 39 2 5 7 10 11 13 10 10 22 18 36 22l10 3c10 1 19-1 26-7l2-1h0c3-1 8-5 9-8l10-17h0c2-1 3-3 4-5 1-1 2-3 3-5 3-10-1-23-5-32 0-3-2-4-3-6-2-3-5-5-7-7 1-1 1-1 2-1l1 1c2 1 4 4 6 5l1 1v-3c0-1-1-2-1-3 1-2 1-4 2-6h2l1-3z" class="V"></path><path d="M278 355c2 1 4 1 6 2 1 0 2-1 3-2l1 2 1 3c-3-1-7 0-11-1 1-1 2-1 4-1 0 0 1 1 2 1v-1c-1 0-2-1-4-1h0l-2-2z" class="I"></path><path d="M277 271c2 0 3 0 5 2 1 1 1 2 1 4l-6 8v-1c1-2 1-2 2-3 0 0 0-1 1-1v-1c0-1 1-1 1-2 0-2-1-2-3-3h0-2c1-1 1-2 1-3z" class="a"></path><path d="M194 355c2-1 7 1 9 2 3 1 6 1 9 2 6 1 13 1 19 0h5-2c-12 2-28 1-40-4z" class="d"></path><path d="M271 275c2-2 3-2 5-3v2h2c-1 1-1 2-2 3s-1 4-2 5h0v-1-3h1v-1h-1c-1 2-2 4-4 6l1-8z" class="I"></path><path d="M252 351h0c2-2 4-1 6-2l11-2h2 1l1-1c1 0 2 0 3-1l1-1h1c0-1 0-1 1-2 0-1 0 0 1-1v1c-1 1-1 3-2 3-1 1-3 2-4 3 1 0 2 0 4-1-1 0-1 0-1-1h3 0c1-1 2-1 3-1 0-1 0-2 1-3l1-1c-1 2-1 3-1 4-2 1-4 1-5 2 1 1 1 1 2 1-3 0-5 0-8 1h-3c-6 2-12 2-18 2z" class="c"></path><path d="M257 329c0 1-1 2-1 3l-2 2c0 1-1 2-1 3l-2 2-1 1c-1 3-5 6-7 8l-1-1c0-2 1-2 3-4 1-2 4-5 5-7 4-5 6-10 10-15l1 1c0 1-4 5-4 7z" class="b"></path><path d="M274 341c-1 0-3 1-3 1-2 1-2 2-4 2-1-1-1-2-1-3 0-8 7-19 12-25l-6 17c-1 2-1 4-1 6l2 2h1z" class="S"></path><path d="M298 268c0 1 0 1-1 2-7 1-12 7-16 13h1c3 0 5 0 7 3h0c0 1-1 2-2 3l-1 2-1-1 2-3c-1-2-1-2-2-2-2 0-3 0-5 2-8 8-8 20-13 30l-10 12c0-2 4-6 4-7h1c2-4 5-7 7-11l3-12c1-5 3-10 5-14l6-8c2-2 3-4 5-5l4-2 4-1 2-1z" class="M"></path><path d="M282 309c1-2 4-4 7-5 0-1 1-1 2-1h3c-2 4-4 4-7 7-1 1-2 3-2 4-2 2-5 5-6 7 0 1 0 3-1 4s-2 2-2 4c-1 2-2 3-2 5-1 2-1 4-1 7l-2-2c0-2 0-4 1-6l6-17c2-1 3-2 4-3l3-6h0l-1 1-2 1z" class="I"></path><defs><linearGradient id="ER" x1="301.623" y1="309.509" x2="294.826" y2="305.271" xlink:href="#B"><stop offset="0" stop-color="#7c7c7a"></stop><stop offset="1" stop-color="#979792"></stop></linearGradient></defs><path fill="url(#ER)" d="M294 303c2-1 8 0 10 1l1 1v3c-3 2-6 2-9 4-2-1-2-1-3 0-2 1-3 3-4 4-4 3-7 5-11 9 1-1 1-3 1-4 1-2 4-5 6-7 0-1 1-3 2-4 3-3 5-3 7-7z"></path><defs><linearGradient id="ES" x1="268.9" y1="356.482" x2="265.231" y2="346.378" xlink:href="#B"><stop offset="0" stop-color="#cfcecc"></stop><stop offset="1" stop-color="#f4f3ec"></stop></linearGradient></defs><path fill="url(#ES)" d="M281 348h5l1 1 1 1 1 1 2 1c-1 2-2 2-3 4v1l-1-2c-1 1-2 2-3 2-2-1-4-1-6-2l-2-1-1 1-7-1c0 1-2 1-3 1-7 0-13-1-19 1h-1 0c2-1 5-3 7-5h0c6 0 12 0 18-2h3c3-1 5-1 8-1z"></path><path d="M268 354v-1l7-1v1 2l-7-1z" class="C"></path><path d="M287 349l1 1 1 1 2 1c-1 2-2 2-3 4v1l-1-2c-1 1-2 2-3 2-2-1-4-1-6-2l-2-1-1 1v-2h4c2-1 4-1 6-1l2-3z" class="E"></path><path d="M287 349l1 1 1 1-1 2c-2 0-3 0-4-1v1l1 1h-1-5c-1-1-1-1-2 0h-1l-1 1v-2h4c2-1 4-1 6-1l2-3z" class="I"></path><defs><linearGradient id="ET" x1="289.484" y1="293.69" x2="264.862" y2="304.965" xlink:href="#B"><stop offset="0" stop-color="#c4c3c1"></stop><stop offset="1" stop-color="#efede8"></stop></linearGradient></defs><path fill="url(#ET)" d="M267 317c5-10 5-22 13-30 2-2 3-2 5-2 1 0 1 0 2 2l-2 3v2c1 0 2 0 4-1h3 0c1 1 2 2 3 2v2l-2 1c1 0 1 0 1 2 0 1-2 3-3 5-1 0-2 0-2 1-3 1-6 3-7 5-1 1-1 1-3 1v1l-6 3-2 2c-1 0-2 1-3 2l-1-1z"></path><path d="M285 292c1 0 2 0 4-1h3 0c1 1 2 2 3 2v2l-2 1-11 3c1-3 2-5 3-7z" class="N"></path><defs><linearGradient id="EU" x1="262.797" y1="262.882" x2="272.785" y2="285.185" xlink:href="#B"><stop offset="0" stop-color="#8f8f8a"></stop><stop offset="1" stop-color="#d4d4ce"></stop></linearGradient></defs><path fill="url(#EU)" d="M260 253c2 0 3-1 4 0 1 0 1 0 2 1l-1 2 2 2c0-1 1-1 1-2l1 1c1-1 2-3 3-3 1 1 1 2 1 3 1 5 3 10 3 15-2 1-3 1-5 3l-1 8-1 1h0c0 4-2 8-3 11 0-3 0-7-1-11v-1c-1-2-1-4-2-6v-1c0-1-1-3-1-4-1 1-1 1-2 1-2-1-2-1-3-3l-1-2v-3c0-1-1-2-1-3 1-2 1-4 2-6h2l1-3z"></path><path d="M268 256l1 1c1-1 2-3 3-3 1 1 1 2 1 3 1 5 3 10 3 15-2 1-3 1-5 3l-1 8-1 1v-3c2-7 2-15 2-22l-3 3v-1l-3 2c-1 2-1 6-2 7-1-2 0-4 1-6l2-4 1-2c0-1 1-1 1-2z" class="Q"></path><path d="M265 263c1-1 1-2 2-3s2-2 3-2l1 1-3 3v-1l-3 2z" class="G"></path><defs><linearGradient id="EV" x1="271.164" y1="261.284" x2="274.825" y2="269.861" xlink:href="#B"><stop offset="0" stop-color="#8b8b8b"></stop><stop offset="1" stop-color="#ababa6"></stop></linearGradient></defs><path fill="url(#EV)" d="M271 275l1-11c0-2 0-5 1-7 1 5 3 10 3 15-2 1-3 1-5 3z"></path><defs><linearGradient id="EW" x1="259.567" y1="257.496" x2="260.448" y2="271.973" xlink:href="#B"><stop offset="0" stop-color="#908f8b"></stop><stop offset="1" stop-color="#c9c9c3"></stop></linearGradient></defs><path fill="url(#EW)" d="M260 253c2 0 3-1 4 0 1 0 1 0 2 1l-1 2 2 2-1 2-2 4c-1 2-2 4-1 6 0 2 0 3 1 5 1 3 1 5 1 8-1-2-1-4-2-6v-1c0-1-1-3-1-4-1 1-1 1-2 1-2-1-2-1-3-3l-1-2v-3c0-1-1-2-1-3 1-2 1-4 2-6h2l1-3z"></path><path d="M260 253c2 0 3-1 4 0 1 0 1 0 2 1l-1 2 2 2-1 2-2 4-1-3c0-1 1-2 1-3-2 0-3-1-5 0v-2l1-3z" class="J"></path><path d="M266 260c-1-1-1-2-1-4l2 2-1 2z" class="G"></path><path d="M264 372l-4-6c1-2 2-4 3-5 5-3 10-2 15-2 4 1 8 0 11 1l2 1v1h0c-1-1-7-1-9-1 2 1 8 1 10 2 0 0-2 2-3 2l-11 11 2 1c-1 2-3 4-4 6l-2 1h-1c-3-2-7-4-11-5l-8-3c-8-2-15 0-23 1h0c4-2 10-2 15-3 5-4 12 0 18-2z" class="R"></path><path d="M264 372l7 6h-5-3c-3-1-7-2-10-3-2 0-5 0-7-1 5-4 12 0 18-2z" class="a"></path><path d="M266 368c-1-1-1-2-1-3 1-2 2-3 3-4 3-2 11 0 14 0 2 1 8 1 10 2 0 0-2 2-3 2l-11 11c-3 0-5-1-7-2-3-1-4-3-5-6z" class="E"></path><path d="M266 368c-1-1-1-2-1-3 1-2 2-3 3-4 3-2 11 0 14 0 2 1 8 1 10 2 0 0-2 2-3 2-2-1-5-1-8-1-2 0-6 0-9 1-1 0-1 2-2 2-1 1-3 0-4 1z" class="G"></path><defs><linearGradient id="EX" x1="306.315" y1="331.798" x2="290.46" y2="332.961" xlink:href="#B"><stop offset="0" stop-color="#4f4f50"></stop><stop offset="1" stop-color="#979692"></stop></linearGradient></defs><path fill="url(#EX)" d="M296 312h1l3-1 1 1c-1 0-2 1-3 2h1c1-1 1-1 3 0h0l1 1s1 0 2 1h0c-1 1-2 1-3 1l3 2h0c-2 3-2 3-2 6l4 1h3c-1 0-2 0-2 1h1l-1 1c-2 1-2 2-3 3-4 1-8 6-9 9-3 3-4 7-8 10l-1-1-1-1h-5c-1 0-1 0-2-1 1-1 3-1 5-2 0-1 0-2 1-4 1 0 1 0 1-1-1-2-2-3-4-4 0 2 1 2 1 4h-4c-2 0-3 0-5 1h-1c0-3 0-5 1-7 0-2 1-3 2-5 0-2 1-3 2-4 4-4 7-6 11-9 1-1 2-3 4-4 1-1 1-1 3 0z"></path><path d="M303 325l4 1h3c-1 0-2 0-2 1-7-1-11-1-16 3l-4 4-1-1c0-2 1-3 2-4l4-4h10z" class="F"></path><path d="M299 318c0-1 1-1 2-2l1 1 3 2h0c-2 3-2 3-2 6h-10l6-5-1-1 1-1z" class="H"></path><path d="M279 334v-3c2-1 2 0 4 0l4 2 1 1c2 2 4 4 5 6-1 3-4 3-5 7h-2c-1 0-2-1-3 0h0c1 0 2 1 3 1h-5c-1 0-1 0-2-1 1-1 3-1 5-2 0-1 0-2 1-4 1 0 1 0 1-1-1-2-2-3-4-4 0 2 1 2 1 4h-4l2-1 1-1-3-3v-1z" class="C"></path><path d="M279 334v-3c2-1 2 0 4 0v4h-1c-1 0-2-1-3-1z" class="d"></path><path d="M278 328c3-2 4-4 8-4 1 1 2 3 3 5-1 1-2 2-2 4l-4-2c-2 0-2-1-4 0v3 1l3 3-1 1-2 1c-2 0-3 0-5 1h-1c0-3 0-5 1-7 0-2 1-3 2-5h1v1l1-2z" class="D"></path><defs><linearGradient id="EY" x1="294.172" y1="321.091" x2="281.437" y2="323.103" xlink:href="#B"><stop offset="0" stop-color="#696868"></stop><stop offset="1" stop-color="#7d7d7a"></stop></linearGradient></defs><path fill="url(#EY)" d="M296 312h1l3-1 1 1c-1 0-2 1-3 2h1c1-1 1-1 3 0h0l1 1s1 0 2 1h0c-1 1-2 1-3 1l-1-1c-1 1-2 1-2 2l-1 1 1 1-6 5-4 4c-1-2-2-4-3-5-4 0-5 2-8 4l-1 2v-1h-1c0-2 1-3 2-4 4-4 7-6 11-9 1-1 2-3 4-4 1-1 1-1 3 0z"></path><path d="M294 319c1-1 2-2 4-2l1 1-1 1-2 2-2-2z" class="B"></path><path d="M296 312h1l3-1 1 1c-1 0-2 1-3 2h1c1-1 1-1 3 0h0l1 1s1 0 2 1h0c-1 1-2 1-3 1l-1-1c-1 1-2 1-2 2l-1-1c-2 0-3 1-4 2v-1h-2l2-2h1c-1-1-1-1-2-1s-1 1-2 1l-2 1c-4 3-7 5-11 9v2l-1 2v-1h-1c0-2 1-3 2-4 4-4 7-6 11-9 1-1 2-3 4-4 1-1 1-1 3 0z" class="e"></path><path d="M677 524c1 2 2 3 3 4 6 3 12 4 18 4 1 1 1 2 1 3h0c2 3 4 8 6 12h0c0-3-2-8-3-11l2-1c1 1 2 2 4 3 1 1 2 4 3 5 0-1 0-2-1-3v-1h1c1-1 3-1 4-2l2-1c1 1 2 1 3 2h1c1 4 4 6 6 10 3 7 5 13 6 20 1 4 2 8 3 11l1 2h1l1 1 1 1c-1 3-2 8-4 10-1 1-2 1-4 1h0l-1 2c1 3 1 9-1 11-3 3-7 6-11 8l-25 18c-2 2-5 3-7 5 8 4 18 5 21 15 1 4 0 9-2 13-2 2-4 4-7 5-1 0-3 0-4-1v-1c2 1 3 1 4 0 2-1 5-3 6-6 2-3 2-8 0-11-3-8-14-11-21-14l-2 1c-3 0-6 0-8-1l-2-1s-1 0-2 1h0-1l-8-5c-1-1-2-1-2-2l-10-10c2-2 3-2 5-3-2-3-4-6-6-10-1-3-2-6-4-8l1-2c1 0 1 0 2-1 3-5 5-13 4-19-1-7-3-13-9-18h0l-3-6-6-6-5-9h1c1 1 0 1 2 1l1-1 1-1 7-6c7-3 12-4 19-5 5 1 13 2 17 1l1-4z" class="T"></path><path d="M690 578h1l1 2-2 7-1-2v1c0 1-1 2-2 3l3-11z" class="L"></path><path d="M687 589c1-1 2-2 2-3v-1l1 2c-1 6-4 11-5 17l-2 1-3 3 7-19z" class="J"></path><path d="M680 608l3-3c-3 9-7 16-13 23l-1-1c2-3 4-6 6-10 2-3 3-6 5-9z" class="E"></path><defs><linearGradient id="EZ" x1="690.098" y1="552.97" x2="683.033" y2="565.876" xlink:href="#B"><stop offset="0" stop-color="#282929"></stop><stop offset="1" stop-color="#585856"></stop></linearGradient></defs><path fill="url(#EZ)" d="M684 551h3c0-1-1-2-1-3-1 0-1-1-1-1-1-1-1-2-2-3l-1-1c-1-1-2-2-4-3-3-3-6-8-11-8-1 0-2 0-2-1 7 0 13 6 18 11 5 6 7 14 7 21v7l-3-2-2 1h0v-1c1-3-2-12-3-16l2-1z"></path><defs><linearGradient id="Ea" x1="693.781" y1="579.078" x2="687.456" y2="601.988" xlink:href="#B"><stop offset="0" stop-color="#94938e"></stop><stop offset="1" stop-color="#cecdc7"></stop></linearGradient></defs><path fill="url(#Ea)" d="M692 580v-1c1 0 2 0 3 1v-1l1 1c-1 7-3 13-5 21l-2 3c0 1-1 2-1 3-1-1-1-1-1-2h0l-2-1c1-6 4-11 5-17l2-7z"></path><defs><linearGradient id="Eb" x1="683.514" y1="555.292" x2="678.676" y2="564.82" xlink:href="#B"><stop offset="0" stop-color="#3b3b3b"></stop><stop offset="1" stop-color="#5e5e5d"></stop></linearGradient></defs><path fill="url(#Eb)" d="M670 538l2 2h1c-2-2-4-3-5-5 2 0 4 1 6 3s-1-1 1 1c1 0 3 2 3 4h0c-1 0-1 0-2-1 0 1 0 2 1 3h1 1c0 1 1 2 2 3l-1 1-1-1v1c2 0 4 1 5 2l-2 1c1 4 4 13 3 16-1 1-1 2 0 4l1 1h-2v-1-5h-3v2c0-8-2-15-6-22-1-3-3-5-5-7v-2z"></path><path d="M678 545h1c0 1 1 2 2 3l-1 1-1-1v1c2 0 4 1 5 2l-2 1c1 4 4 13 3 16-1-5-1-10-4-13-1-2-2-3-2-4-1-1-1-2-2-3h1v-3z" class="D"></path><defs><linearGradient id="Ec" x1="676.467" y1="543.76" x2="672.154" y2="560.764" xlink:href="#B"><stop offset="0" stop-color="#1d1f1e"></stop><stop offset="1" stop-color="#3b3a3b"></stop></linearGradient></defs><path fill="url(#Ec)" d="M679 575c-1-8-1-15-4-22-4-10-11-15-20-19l1-1c4 2 7 4 11 6v-1c-3-2-6-3-9-5 0-1 1-1 1-1 2 0 3 0 4 1 2 2 5 3 7 5v2c2 2 4 4 5 7 4 7 6 14 6 22l-1 5-1 1z"></path><defs><linearGradient id="Ed" x1="665.964" y1="618.772" x2="665.557" y2="638.187" xlink:href="#B"><stop offset="0" stop-color="#adaba9"></stop><stop offset="1" stop-color="#cececc"></stop></linearGradient></defs><path fill="url(#Ed)" d="M654 618c3 4 6 7 9 10 6 5 14 8 21 10l-2 1c-3 0-6 0-8-1l-2-1s-1 0-2 1h0-1l-8-5c-1-1-2-1-2-2l-10-10c2-2 3-2 5-3z"></path><path d="M683 605l2-1 2 1h0c0 1 0 1 1 2l-10 25 1 2-5-2c-2 0-4-1-6-3 0-1 1-1 2-1 6-7 10-14 13-23z" class="S"></path><path d="M674 632c1-4 3-6 4-10 2-4 3-8 6-11h0c-1 1-1 1-1 3h0c0 2-1 3-1 5-1 4-3 8-5 12l1 1 1 2-5-2z" class="b"></path><defs><linearGradient id="Ee" x1="682.332" y1="552.843" x2="702.611" y2="571.484" xlink:href="#B"><stop offset="0" stop-color="#474748"></stop><stop offset="1" stop-color="#959590"></stop></linearGradient></defs><path fill="url(#Ee)" d="M685 536c1 1 2 1 3 2l1-1c3 2 4 4 4 7l1 1c0 2 0 4 1 5l3 14v2c1 1 1 2 1 3s0 3 1 4v3 2l-3-1c-1 1-1 1-1 2v1l-1-1v1c-1-1-2-1-3-1v1l-1-2h-1c0-3 1-7 1-10 1-5 1-11 0-17v-1c-1-3-2-7-4-10-1-1-1-2-2-4z"></path><path d="M691 568l2 2c0 2-1 4-1 7 0 1 0 0-1 1h-1c0-3 1-7 1-10z" class="Q"></path><path d="M685 536c1 1 2 1 3 2l1-1c3 2 4 4 4 7l1 1c-2-1-2-4-4-5 0 1 1 2 1 3h-1c-1-1-1-2-2-2l-1-1c-1-1-1-2-2-4z" class="D"></path><defs><linearGradient id="Ef" x1="695.896" y1="539.808" x2="710.314" y2="562.73" xlink:href="#B"><stop offset="0" stop-color="#424242"></stop><stop offset="1" stop-color="#9b9c96"></stop></linearGradient></defs><path fill="url(#Ef)" d="M685 536c0-1-1-1 0-2 1 0 2-1 3 0 2 1 3 3 5 4h1l-2-3c1 0 2 1 4 2 0-1 1-1 3-2 2 3 4 8 6 12h0c0-3-2-8-3-11l2-1c1 1 2 2 4 3 1 1 2 4 3 5l2 7c0 1-1 1-1 3 0 0 0 1 1 2h0c-1 2-1 4 0 6-1 0-3 1-4 1v-9c0 3 0 5-1 8v5c-1-1-1-2-2-3h-1 0c-2 1-3 1-5 1l-2 2v-2c-1-4-2-9-3-14-1-1-1-3-1-5l-1-1c0-3-1-5-4-7l-1 1c-1-1-2-1-3-2z"></path><path d="M694 538l-2-3c1 0 2 1 4 2v1c1 0 1 0 2 1l1-1v7h-1c-1-1-3-2-3-4v-2s0-1-1-1z" class="P"></path><defs><linearGradient id="Eg" x1="663.447" y1="586.658" x2="684.182" y2="604.713" xlink:href="#B"><stop offset="0" stop-color="#646363"></stop><stop offset="1" stop-color="#9fa09b"></stop></linearGradient></defs><path fill="url(#Eg)" d="M681 569v-2h3v5 1h2l-1-1c-1-2-1-3 0-4v1h0l2-1 3 2c-1 2-1 5-2 8 1 3-1 7-1 9-3 10-7 21-13 30-2 4-4 7-9 10-2-1-4-3-6-5 13-12 20-29 20-47l1-1 1-5z"></path><path d="M685 568v1h0l2-1 3 2c-1 2-1 5-2 8 0-1 0-2 1-2v-1c-1-1-1-2-1-3h1v-1l-3-1v2 1l-1-1c-1-2-1-3 0-4z" class="K"></path><defs><linearGradient id="Eh" x1="704.745" y1="593.328" x2="690.755" y2="610.672" xlink:href="#B"><stop offset="0" stop-color="#b5b3ad"></stop><stop offset="1" stop-color="#d1d3cf"></stop></linearGradient></defs><path fill="url(#Eh)" d="M698 566l2-2c2 0 3 0 5-1h0 1v3h-3v2c0 1 0 2-1 4v4 17c0 7-2 14-4 21l-6 12c0 1-1 2-1 3l-2 2-1 1-1-1c-1 1-2 3-4 3h-4l-1-2 10-25c0-1 1-2 1-3l2-3c2-8 4-14 5-21v-1c0-1 0-1 1-2l3 1v-2-3c-1-1-1-3-1-4s0-2-1-3z"></path><path d="M698 566l2-2c2 0 3 0 5-1h0 1v3h-3v2c0 1 0 2-1 4v4c-1 2 0 4-1 6v-1-3c-1-1-1-3-1-4v2-3c-1-1-1-3-1-4s0-2-1-3zm-2 13c0-1 0-1 1-2l3 1v7c-1 3 0 5-1 8v4l-3 14c-1 5-3 10-6 15 0 1-1 3-3 4 1-3 3-5 4-8 3-7 5-14 6-21 1-3 1-5 1-7v-4c1-4 1-6 1-10-1 0-2-1-3-1z" class="J"></path><defs><linearGradient id="Ei" x1="695.049" y1="580.648" x2="696.44" y2="594.25" xlink:href="#B"><stop offset="0" stop-color="#a3a39d"></stop><stop offset="1" stop-color="#cdcdc7"></stop></linearGradient></defs><path fill="url(#Ei)" d="M696 579c1 0 2 1 3 1 0 4 0 6-1 10v4c0 2 0 4-1 7 0-2-1-4 0-6v-1c-1 0-2 0-3 1v1c-2 1-2 3-3 5h0c2-8 4-14 5-21v-1z"></path><defs><linearGradient id="Ej" x1="681.347" y1="602.696" x2="695.617" y2="625.102" xlink:href="#B"><stop offset="0" stop-color="#deddd2"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#Ej)" d="M691 601h0c1-2 1-4 3-5v-1c1-1 2-1 3-1v1c-1 2 0 4 0 6-1 7-3 14-6 21-1 3-3 5-4 8v1c-1 1-2 3-4 3h-4l-1-2 10-25c0-1 1-2 1-3l2-3z"></path><defs><linearGradient id="Ek" x1="690.908" y1="592.332" x2="710.648" y2="617.101" xlink:href="#B"><stop offset="0" stop-color="#d8d5cf"></stop><stop offset="1" stop-color="#fffffd"></stop></linearGradient></defs><path fill="url(#Ek)" d="M711 543c0-1 0-2-1-3v-1h1c1-1 3-1 4-2l2-1c1 1 2 1 3 2h1c1 4 4 6 6 10 3 7 5 13 6 20 1 4 2 8 3 11l1 2h1l1 1 1 1c-1 3-2 8-4 10-1 1-2 1-4 1h0l-1 2c1 3 1 9-1 11-3 3-7 6-11 8h0-1 0l-1 1-2 1h-1c1 0 2-1 2-2v-1l-3 3c-4 4-9 7-14 11-4 2-7 6-12 6h0v-1l2-2 2-2c0-1 1-2 1-3l6-12c2-7 4-14 4-21v-17-4c1-2 1-3 1-4v-2h3v-3c1 1 1 2 2 3v-5c1-3 1-5 1-8v9c1 0 3-1 4-1-1-2-1-4 0-6h0c-1-1-1-2-1-2 0-2 1-2 1-3l-2-7z"></path><path d="M711 585v6 1l-1-6c0-1 0 0 1-1z" class="S"></path><defs><linearGradient id="El" x1="702.429" y1="566.203" x2="706.412" y2="583.604" xlink:href="#B"><stop offset="0" stop-color="#9d9d96"></stop><stop offset="1" stop-color="#cccbc6"></stop></linearGradient></defs><path fill="url(#El)" d="M706 563c1 1 1 2 2 3l-1 1 1 1v2 1c0 1 1 1 1 2v1 1c0 1 0 2 1 3 0 2 0 2 1 4v1 2c-1 1-1 0-1 1h-1v-3c-1 0-2 0-3 1v2c1 1 0 2 0 3-1-2-1-6-1-8l-1 10h0v-6c-2 1-1 5-2 8v-17-4c1-2 1-3 1-4v-2h3v-3z"></path><defs><linearGradient id="Em" x1="707.418" y1="562.673" x2="716.413" y2="578.641" xlink:href="#B"><stop offset="0" stop-color="#91928c"></stop><stop offset="1" stop-color="#cac9c5"></stop></linearGradient></defs><path fill="url(#Em)" d="M708 566v-5c1-3 1-5 1-8v9c1 0 3-1 4-1-1-2-1-4 0-6h0c0 3 1 5 1 8 1 4 1 8 2 12-1 5 0 11 0 17-2-3-1-8-1-12-2 1-2 1-3 2v1c1 2 0 5-1 7v1-6-2-1c-1-2-1-2-1-4-1-1-1-2-1-3v-1-1c0-1-1-1-1-2v-1-2l-1-1 1-1z"></path><path d="M713 555c-1-1-1-2-1-2 0-2 1-2 1-3 3 7 5 15 7 22v5c0 2 1 4 1 6l-1 4 1 2c-1 7-3 13-5 19 0 2-1 5-2 6v1c0-3 0-6 1-9 0-5 1-9 1-14 0-6-1-12 0-17-1-4-1-8-2-12 0-3-1-5-1-8z" class="E"></path><path d="M713 555c-1-1-1-2-1-2 0-2 1-2 1-3 3 7 5 15 7 22v5 11h-1c0-7 0-14-2-21l-1-4v11 1c-1-4-1-8-2-12 0-3-1-5-1-8z" class="G"></path><path d="M711 543c0-1 0-2-1-3v-1h1c1-1 3-1 4-2l2-1c1 1 2 1 3 2h1c1 4 4 6 6 10 3 7 5 13 6 20 1 4 2 8 3 11l1 2h1l1 1 1 1c-1 3-2 8-4 10-1 1-2 1-4 1h0l-1 2c1 3 1 9-1 11-3 3-7 6-11 8h0-1 0l-1 1-2 1h-1c1 0 2-1 2-2v-1l-3 3 1-2v-1c1-1 2-4 2-6 2-6 4-12 5-19l-1-2 1-4c0-2-1-4-1-6v-5c-2-7-4-15-7-22l-2-7z" class="V"></path><path d="M724 564c1 2 1 4 1 7 1 2 1 5 1 7-1-2-1-4-1-6 0-1-1-2-1-3h0l-1-1h0c0-1 0-1-1-1 0-1 0-2 1-3h0 1z" class="C"></path><path d="M728 593v-1l-1-2v-3c3-1 5 2 8 4 2-2 2-6 3-9h1l1 1c-1 3-2 8-4 10-1 1-2 1-4 1h0l-1 2v-3h-3z" class="O"></path><path d="M720 572c1-1 1-2 1-3 1-1 1-1 2-1v3l1-2c0 1 1 2 1 3 0 2 0 4 1 6v4h-1c-2 2-3 5-4 7l-1-2 1-4c0-2-1-4-1-6v-5z" class="Z"></path><path d="M724 569c0 1 1 2 1 3-2 2-1 6-2 9 0 2-2 4-3 6l1-4 1-1v-1c1-1 1-8 1-10l1-2z" class="c"></path><path d="M720 572c1-1 1-2 1-3 1-1 1-1 2-1v3c0 2 0 9-1 10v1l-1 1c0-2-1-4-1-6v-5z" class="S"></path><path d="M728 593h3v3c1 3 1 9-1 11-3 3-7 6-11 8h0-1 0l-1 1-2 1h-1c1 0 2-1 2-2v-1c2-3 7-5 9-7 4-4 3-10 3-14z" class="F"></path><defs><linearGradient id="En" x1="706.108" y1="542.982" x2="728.279" y2="561.867" xlink:href="#B"><stop offset="0" stop-color="#676764"></stop><stop offset="1" stop-color="#d4d3cf"></stop></linearGradient></defs><path fill="url(#En)" d="M711 543c0-1 0-2-1-3v-1h1c1-1 3-1 4-2l2-1c1 1 2 1 3 2h1c1 4 4 6 6 10 3 7 5 13 6 20v1c0-1 0-1-1-2h0c0-2 0-2-1-3 1-1 0-1 0-2v-1l-1-1c0-1 0-3-1-4h-3c0 3 3 8 3 11l-1 1-3-9-1-1c0-1 0-1-1-1 0 2 0 4 1 7h-1 0c-1 1-1 2-1 3 1 0 1 0 1 1h0l1 1h0l-1 2v-3c-1 0-1 0-2 1 0 1 0 2-1 3-2-7-4-15-7-22l-2-7z"></path><path d="M364 479c2 4 9 7 13 8 1 0 1 0 2 1 2 4 3 8 4 13l-2-2h0l-2 1v1l2 2c2 1 2 2 2 4h0l-1 1c-1 0-1 0-2-1 1 1 1 2 2 3v1 2c0 1-1 1-1 2l1 1v1c0 1 3 2 3 3s-1 1 0 3h0-1c-1-1-1-1-1 0 1 2 3 4 4 7v1c0 1 0 2-1 4h0v1l-2 3-4 7c-2 3-5 6-6 9-6 7-11 13-13 22v1c-1 6 2 14 6 19 1 0 1 1 2 1l-1 2-2 7c-6 11-14 22-25 27l-1-2-5 2c-2-2-3-5-4-7l-2-4-3-9c-1-1-2-2-2-4-1-1-1-1-1-2-1-1-1-1-1-2v-2c-1-1 0 0 0-1s-1-2-1-2l1 1h1v1l1 1c0-3-1-5-2-8 0-3-1-6-3-9 0-1-1-2-2-3l1-1v-1-2-4-1h2l2 1v-1c-2-1-3-2-4-2-1-2-1-2-2-3l-1 1v-3-2h-1l2-7c0-4 1-9 2-12l1-1h-1-1-1l1-2-1-1-1 1c-2-2-3-3-5-4 1-1 1-3 2-4s2-1 3-2v-1c-4-1-8-6-11-8s-6-3-9-5c-1-1-2-2-4-2-6-1-9 1-15 4l-1 1c-2-1-4-4-6-6l1-1v1l1-1c2-3 5-5 7-7 11-11 25-16 39-20 5-2 11-3 16-3l20-2 1 1h1c4-2 6-3 9-6z" class="T"></path><path d="M323 568c-1-5 0-11 0-15 1-3 2-7 5-10v1l-2 2v2 1l1-1h0l1 3c-1 1-1 2-1 4-1 2-2 5-2 7 0 3 0 6-1 8l-1 1v-3z" class="O"></path><path d="M323 568v-9l1-1v4h1c0 3 0 6-1 8l-1 1v-3z" class="U"></path><defs><linearGradient id="Eo" x1="328.398" y1="545.74" x2="329.939" y2="565.656" xlink:href="#B"><stop offset="0" stop-color="#2b2b2b"></stop><stop offset="1" stop-color="#626361"></stop></linearGradient></defs><path fill="url(#Eo)" d="M328 551v-2c0-2 2-4 4-5 0 1 1 2 1 3 0-1 1-2 1-2h1 1 0c-1 1-1 1-1 3h-1c-1 2 1 0 0 2h-1v2c-1 1-1 1-1 2l-1 1v2h-1v3c0 1 0 2-1 3v9h0v-1l-1-1v-4h-2v3l-1 1c-1 2-1 5 0 6 1 2 0 4 0 5-1-3-1-7-2-10l1-1c1-2 1-5 1-8 0-2 1-5 2-7 0-2 0-3 1-4z"></path><defs><linearGradient id="Ep" x1="343.062" y1="552.636" x2="327.702" y2="563.532" xlink:href="#B"><stop offset="0" stop-color="#252627"></stop><stop offset="1" stop-color="#5a5a58"></stop></linearGradient></defs><path fill="url(#Ep)" d="M336 545l3-2c0 1-1 1-1 2s1 0 0 2v3 1l1-1 1-3c1-1 1-2 2-3 1-2 4-4 5-5h1c-3 2-5 5-7 8v1c-5 7-6 16-6 24l-2-1c0-1 0-5-1-6s0-1-1-2h0v1 5c0 1 0 4-1 5l-1-2v-9c1-1 1-2 1-3v-3h1v-2l1-1c0-1 0-1 1-2v-2h1c1-2-1 0 0-2h1c0-2 0-2 1-3z"></path><defs><linearGradient id="Eq" x1="331.801" y1="547.305" x2="310.942" y2="563.899" xlink:href="#B"><stop offset="0" stop-color="#363738"></stop><stop offset="1" stop-color="#8a8a86"></stop></linearGradient></defs><path fill="url(#Eq)" d="M318 541c2-1 3-2 4-3s2 0 3 0c-3 8-4 16-4 25 0 3 1 5 1 8v4c-2-1-3-2-4-2-1-2-1-2-2-3l-1 1v-3-2h-1l2-7c0-4 1-9 2-12l1-1h-1-1c0-1 1-1 1-2l1-1c0-1 0-1-1-2z"></path><defs><linearGradient id="Er" x1="333.792" y1="590.12" x2="321.452" y2="600.862" xlink:href="#B"><stop offset="0" stop-color="#8a8b8c"></stop><stop offset="1" stop-color="#cdcdc6"></stop></linearGradient></defs><path fill="url(#Er)" d="M322 571c2 15 7 30 14 43l9 15h-2l-2-3h-1c-4-6-8-13-11-20-2-3-3-6-4-9h-1v-1 1c-1 2 1 7 2 8s1 1 1 2c1 1 1 1 0 2h0c-1-2-2-4-3-5 0-3-1-5-2-8 0-3-1-6-3-9 0-1-1-2-2-3l1-1v-1-2-4-1h2l2 1v-1-4z"></path><path d="M324 604c1 1 2 3 3 5h0c1-1 1-1 0-2 0-1 0-1-1-2s-3-6-2-8v-1 1h1c1 3 2 6 4 9 3 7 7 14 11 20h1l2 3h2v1l-5 2-5 2c-2-2-3-5-4-7l-2-4-3-9c-1-1-2-2-2-4-1-1-1-1-1-2-1-1-1-1-1-2v-2c-1-1 0 0 0-1s-1-2-1-2l1 1h1v1l1 1z" class="Z"></path><defs><linearGradient id="Es" x1="346.099" y1="588.621" x2="328.326" y2="601.699" xlink:href="#B"><stop offset="0" stop-color="#70716e"></stop><stop offset="1" stop-color="#a8a7a4"></stop></linearGradient></defs><path fill="url(#Es)" d="M331 569v-5-1h0c1 1 0 1 1 2s1 5 1 6l2 1c-1 14 2 28 10 39 2 4 5 7 8 10l-6 6c-2-2-5-4-7-7-6-8-10-19-13-29 0-3-2-7-2-10 0-1 1-3 0-5-1-1-1-4 0-6l1-1v-3h2v4l1 1v1h0l1 2c1-1 1-4 1-5z"></path><path d="M331 569v-5-1h0c1 1 0 1 1 2s1 5 1 6l-1 4v-5l-1 2h0v-3z" class="Y"></path><defs><linearGradient id="Et" x1="359.451" y1="514.993" x2="334.278" y2="525.528" xlink:href="#B"><stop offset="0" stop-color="#cfcfc8"></stop><stop offset="1" stop-color="#f6f5f2"></stop></linearGradient></defs><path fill="url(#Et)" d="M354 512h1 1 3 1c-1-1-4 0-4-1 1 0 3-1 5 0 1 1 3 1 5 1l2 1-3 2h2l4 1h1l2 1 3 2h0c2 1 4 2 6 4 1 2 3 4 4 7v1c0 1 0 2-1 4h0l-3 4-11-8c-7-2-13-3-20-4l-15 3c-1-2-1-5-2-7h-1-1c-2-1-3-3-5-4 1-1 1 0 2 0h1c0-1-2-1-2-3l3-1-1-1h3 2c2-1 3-1 5-1 4-1 8-1 13-1z"></path><path d="M358 514c2 0 5 0 7 1h2l4 1c-1 2-2 3-3 4l-4-2h-3c-1-1-3-2-4-3h0-1c-2 0-3-1-5-1h6 1z" class="I"></path><path d="M367 515l4 1c-1 2-2 3-3 4l-4-2 3-1h0v-2z" class="f"></path><path d="M354 512h1 1 3 1c-1-1-4 0-4-1 1 0 3-1 5 0 1 1 3 1 5 1l2 1-3 2c-2-1-5-1-7-1-7-1-13-1-19 0-2 0-5 1-7 1l-1-1h3 2c2-1 3-1 5-1 4-1 8-1 13-1z" class="G"></path><defs><linearGradient id="Eu" x1="373.413" y1="527.49" x2="359.235" y2="521.903" xlink:href="#B"><stop offset="0" stop-color="#9b9b97"></stop><stop offset="1" stop-color="#cacac4"></stop></linearGradient></defs><path fill="url(#Eu)" d="M361 518h3l4 2 3 1 2 1 2 1c0 2 0 3-1 4v1c-1 1-2 3-2 3-7-2-13-3-20-4h5 1l3-9z"></path><defs><linearGradient id="Ev" x1="384.433" y1="533.031" x2="373.054" y2="530.527" xlink:href="#B"><stop offset="0" stop-color="#656564"></stop><stop offset="1" stop-color="#91918d"></stop></linearGradient></defs><path fill="url(#Ev)" d="M371 516h1l2 1 3 2h0c2 1 4 2 6 4 1 2 3 4 4 7v1c0 1 0 2-1 4h0l-3 4-11-8s1-2 2-3v-1c1-1 1-2 1-4l-2-1-2-1-3-1c1-1 2-2 3-4z"></path><defs><linearGradient id="Ew" x1="386.408" y1="527.02" x2="377.676" y2="524.741" xlink:href="#B"><stop offset="0" stop-color="#5a5a5a"></stop><stop offset="1" stop-color="#72726f"></stop></linearGradient></defs><path fill="url(#Ew)" d="M371 516h1l2 1 3 2h0c2 1 4 2 6 4 1 2 3 4 4 7v1c0 1 0 2-1 4 0-3-1-4-3-6-3-2-5-4-8-6l-2-1-2-1-3-1c1-1 2-2 3-4z"></path><path d="M371 516h1l2 1 3 2h0l1 3h0c-2 0-4-1-5 0l-2-1-3-1c1-1 2-2 3-4z" class="G"></path><path d="M371 516h1l2 1c-1 1-1 2-1 3l-2 1-3-1c1-1 2-2 3-4z" class="J"></path><path d="M364 479c2 4 9 7 13 8 1 0 1 0 2 1 2 4 3 8 4 13l-2-2h0l-2 1v1l2 2c2 1 2 2 2 4h0l-1 1c-1 0-1 0-2-1 1 1 1 2 2 3v1 2c0 1-1 1-1 2l1 1v1c0 1 3 2 3 3s-1 1 0 3h0-1c-1-1-1-1-1 0-2-2-4-3-6-4h0l-3-2-2-1h-1l-4-1h-2l3-2-2-1c-2 0-4 0-5-1-2-1-4 0-5 0 0 1 3 0 4 1h-1-3-1-1c-5 0-9 0-13 1-2 0-3 0-5 1h-2-3l1 1-3 1c0 2 2 2 2 3h-1c-1 0-1-1-2 0h0l-2-2v1l-1-1c-1 0-1-1-1-1-1-2-1-2-2-3v1 1c1 3 3 4 4 7 2 1 6 4 6 6s0 2-2 3c0 1-1 3-2 4l-3 3c-1 0-2-1-3 0s-2 2-4 3c1 1 1 1 1 2l-1 1c0 1-1 1-1 2h-1l1-2-1-1-1 1c-2-2-3-3-5-4 1-1 1-3 2-4s2-1 3-2v-1c-4-1-8-6-11-8s-6-3-9-5c-1-1-2-2-4-2-6-1-9 1-15 4l-1 1c-2-1-4-4-6-6l1-1v1l1-1c2-3 5-5 7-7 11-11 25-16 39-20 5-2 11-3 16-3l20-2 1 1h1c4-2 6-3 9-6z" class="V"></path><path d="M336 496l-14 2c-1 1-2 1-3 1l15-4h3v-1c-5-2-10 0-15 0 5-1 10-3 15-3h1c1 0 2 1 3 0 1 0 1 0 2 1v1h0-2l2 1v1c-1 0-3 0-3 1h-4z" class="S"></path><path d="M317 489l1 2c-14 2-27 8-38 17-2 2-4 4-6 7-1 1-1 2-1 3 1 1 2 2 3 4l-1 1c-2-1-4-4-6-6l1-1v1l1-1c2-3 5-5 7-7 11-11 25-16 39-20z" class="F"></path><path d="M309 522l-3-3h-1c-1-1-3-1-5-2v-1l-4 2-1-1c1-1 2-1 3-2h2c1 0 2 1 3 1v1h1c-1-1-1-1-1-2 3-1 6-2 10-3l-3 3c-1 0-2 1-2 1l1 1h0c3-2 7-1 11-3h2v1c-1 1-2 1-4 2h0c-1 1-1 1-2 1h-1c-1 0-3 1-4 0h-1-2c0 1 1 2 1 3v1z" class="I"></path><path d="M322 515c1 3 3 4 4 7h-13l-1 1-1 1-2-2v-1c0-1-1-2-1-3h2 1c1 1 3 0 4 0h1c1 0 1 0 2-1h0c2-1 3-1 4-2z" class="C"></path><path d="M353 484l1 1v2l-1 2h-2-2l-9 1h1l1-1c0-1-1-1-2-2l-22 4-1-2c5-2 11-3 16-3l20-2z" class="L"></path><path d="M348 486c2 0 4 0 6 1l-1 2h-2-2l-1-3z" class="I"></path><path d="M340 487l8-1 1 3-9 1h1l1-1c0-1-1-1-2-2z" class="C"></path><path d="M330 531c0 1-1 3-2 4l-3 3c-1 0-2-1-3 0s-2 2-4 3c1 1 1 1 1 2l-1 1c0 1-1 1-1 2h-1l1-2-1-1-1 1c-2-2-3-3-5-4 1-1 1-3 2-4s2-1 3-2v-1c5 0 10-1 15-2z" class="R"></path><path d="M318 537h2c3 0 5-2 8-2l-3 3c-1 0-2-1-3 0s-2 2-4 3c0-1 1-2 0-4z" class="O"></path><path d="M312 536l3 1c1 0 1 1 2 2l1-2c1 2 0 3 0 4 1 1 1 1 1 2l-1 1c0 1-1 1-1 2h-1l1-2-1-1-1 1c-2-2-3-3-5-4 1-1 1-3 2-4z" class="H"></path><path d="M313 512l24-5-1 2h2l1 1h1l1 1h2 0 1 3 1c2 0 4 0 6 1-5 0-9 0-13 1-2 0-3 0-5 1h-2-3l1 1-3 1c0 2 2 2 2 3h-1c-1 0-1-1-2 0h0l-2-2v1l-1-1c-1 0-1-1-1-1-1-2-1-2-2-3v1h-2c-4 2-8 1-11 3h0l-1-1s1-1 2-1l3-3z" class="L"></path><defs><linearGradient id="Ex" x1="352.493" y1="491.483" x2="344.269" y2="499.76" xlink:href="#B"><stop offset="0" stop-color="#b1b0ae"></stop><stop offset="1" stop-color="#d2d2cb"></stop></linearGradient></defs><path fill="url(#Ex)" d="M349 489h2 2c-1 3-2 7 0 10 1 1 1 2 2 2l3 3-1 1h-11 1 2v-1h4 0 1l-1-2h-2c-6-1-12 1-17-1-4 0-9 2-13 3l8-3c1-1 2-1 4-1 1-1 2-1 3-1 1-1 3 0 5 0h-3v-1c-2-1-3 0-5 0-1 1-1 0-2 0 1 0 2 0 3-1h0c1 0 1 0 2-1h4c0-1 2-1 3-1v-1l-2-1h2 0v-1c-1-1-1-1-2-1-1 1-2 0-3 0h-1l3-1 9-1z"></path><path d="M329 501c6-1 13-3 19-1v1c-4 0-10-1-14 0-4 0-9 2-13 3l8-3z" class="C"></path><defs><linearGradient id="Ey" x1="381.81" y1="505.438" x2="357.206" y2="506.148" xlink:href="#B"><stop offset="0" stop-color="#5d5c5b"></stop><stop offset="1" stop-color="#8f8f8c"></stop></linearGradient></defs><path fill="url(#Ey)" d="M364 479c2 4 9 7 13 8 1 0 1 0 2 1 2 4 3 8 4 13l-2-2h0l-2 1v1l2 2c2 1 2 2 2 4h0l-1 1c-1 0-1 0-2-1 1 1 1 2 2 3v1 2c0 1-1 1-1 2l1 1v1c0 1 3 2 3 3s-1 1 0 3h0-1c-1-1-1-1-1 0-2-2-4-3-6-4h0l-3-2-2-1h-1l-4-1h-2l3-2-2-1c-2 0-4 0-5-1-2-1-4 0-5 0 0 1 3 0 4 1h-1-3-1-1c-2-1-4-1-6-1h-1-3-1 0-2l-1-1h-1l-1-1h-2l1-2 9-2h11l1-1-3-3c-1 0-1-1-2-2-2-3-1-7 0-10l1-2v-2h1c4-2 6-3 9-6z"></path><path d="M378 503c2 2 3 3 5 4l-1 1c-1 0-1 0-2-1 1 1 1 2 2 3v1c0-1-1-1-1-2h-1c-1-1-4-4-4-5 1 0 1 0 2-1z" class="K"></path><path d="M371 496c1 1 2 2 3 2l-2 2-6-2c-1-1-2-1-3-1 3-1 5-1 8-1z" class="U"></path><path d="M374 498l1 1c1 0 0 0 1 1 1 0 2 0 2 1h1l2 2c2 1 2 2 2 4h0c-2-1-3-2-5-4l-3-2-3-1 2-2z" class="P"></path><path d="M374 498l1 1c1 0 0 0 1 1 1 0 2 0 2 1h1l2 2c-2 0-4-2-6-2l-3-1 2-2z" class="X"></path><path d="M377 494c2 1 3 3 4 5h0l-2 1v1h-1c0-1-1-1-2-1-1-1 0-1-1-1l-1-1c-1 0-2-1-3-2l3-1h3v-1z" class="H"></path><path d="M377 494c2 1 3 3 4 5h0c-2-2-5-3-7-4h3v-1zm-23-9h1l2 1c-2 2-2 3-3 6v7c1 0 1 1 2 1h0l6 7c6 3 13 4 19 8l1 1c-2 0-4-2-6-3-3-1-7-3-12-4l-3-1c-2-1-3-2-5-2h-1l2-1 1-1-3-3c-1 0-1-1-2-2-2-3-1-7 0-10l1-2v-2z" class="D"></path><defs><linearGradient id="Ez" x1="382.385" y1="514.842" x2="370.728" y2="517.631" xlink:href="#B"><stop offset="0" stop-color="#3c3c3c"></stop><stop offset="1" stop-color="#595958"></stop></linearGradient></defs><path fill="url(#Ez)" d="M364 509c5 1 9 3 12 4 2 1 4 3 6 3v1c0 1 3 2 3 3s-1 1 0 3h0-1c-1-1-1-1-1 0-2-2-4-3-6-4h0l-3-2-2-1h-1l-4-1h-2l3-2-2-1c1 0 2 0 3 1v-1l-1-1c-1 0-2-1-3-1l-1-1z"></path><path d="M372 514c2 1 3 1 5 3v2l-3-2-2-1v-2z" class="K"></path><path d="M368 513c1 1 2 1 4 1v2h-1l-4-1h-2l3-2z" class="Q"></path><path d="M354 492l7-1v1c2 0 5-1 7-1h0 4l1 1-1 1h0c2 0 3 0 5 1h0v1h-3l-3 1c-3 0-5 0-8 1l-5 1c-2 1-2 0-2 2-1 0-1-1-2-1v-7z" class="e"></path><path d="M377 494h0v1h-3l-3 1c-3 0-5 0-8 1l-5 1-1-1h1 1c3-1 6-1 8-2h5c1 0 4-1 5-1z" class="O"></path><path d="M368 491h0 4l1 1-1 1h0c2 0 3 0 5 1-1 0-4 1-5 1l-1-1c-2-1-5 0-7 1l-1-1c2-1 3-1 5-2v-1z" class="W"></path><path d="M354 492l7-1v1c2 0 5-1 7-1v1c-2 1-3 1-5 2l1 1h-4-3l-2 2 1 1 1-1 1 1c-2 1-2 0-2 2-1 0-1-1-2-1v-7z" class="P"></path><path d="M368 491v1c-2 1-3 1-5 2h-7 0-1l1-1 1-1h2 2c2 0 5-1 7-1z" class="e"></path><defs><linearGradient id="FA" x1="368.659" y1="513.785" x2="339.051" y2="505.925" xlink:href="#B"><stop offset="0" stop-color="#70706d"></stop><stop offset="1" stop-color="#969593"></stop></linearGradient></defs><path fill="url(#FA)" d="M346 505h11l-2 1h1c2 0 3 1 5 2l3 1 1 1c1 0 2 1 3 1l1 1v1c-1-1-2-1-3-1-2 0-4 0-5-1-2-1-4 0-5 0 0 1 3 0 4 1h-1-3-1-1c-2-1-4-1-6-1h-1-3-1 0-2l-1-1h-1l-1-1h-2l1-2 9-2z"></path><defs><linearGradient id="FB" x1="375.913" y1="489.191" x2="355.427" y2="486.611" xlink:href="#B"><stop offset="0" stop-color="#313131"></stop><stop offset="1" stop-color="#515150"></stop></linearGradient></defs><path fill="url(#FB)" d="M364 479c2 4 9 7 13 8 1 0 1 0 2 1 2 4 3 8 4 13l-2-2c-1-2-2-4-4-5h0c-2-1-3-1-5-1h0l1-1-1-1h-4 0c-2 0-5 1-7 1v-1l-7 1c1-3 1-4 3-6l-2-1c4-2 6-3 9-6z"></path><path d="M377 489h1c-1 1-1 1-2 1v1c-1 0-3-1-4 0h-4 0c-2 0-5 1-7 1v-1h1c4 0 7-1 10-1 1 0 4 0 5-1z" class="D"></path><path d="M364 479c2 4 9 7 13 8 1 0 1 0 2 1 2 4 3 8 4 13l-2-2c-1-2-2-4-4-5h0c-2-1-3-1-5-1h0l1-1-1-1c1-1 3 0 4 0v-1c1 0 1 0 2-1h-1l-7-4c-2-1-3-2-4-2-4-1-6 1-9 3l-2-1c4-2 6-3 9-6z" class="B"></path><path d="M372 491c1-1 3 0 4 0l1 1h-2-2l-1-1z" class="O"></path><path d="M672 443c1 0 3-1 4 0s3 0 4 0c2 0 5 0 7 1 2 0 3-1 5 0v1l-1 1c1 1 4 1 5 2 4 0 8 0 12 1h3l2 1h2l6 2h2c1 1 2 1 2 1 10 2 21 7 30 11 0 2 1 4 2 5 3 2 6 3 9 5 2 2 4 5 5 8 1 2 3 5 3 7l-1 1-1 1h-1c-3 0-5-1-8-2h-6c1 1 5 5 6 7 0 1 2 2 3 2h0c5 4 8 10 8 16 1 6 1 14-3 19h0l-2-3h-1c-3-4-6-8-10-11-1-1-2-1-3-1l-1 1-8 10c3 3 5 6 7 10 5 8 6 17 7 27 1 2 2 6 1 9 0 2-3 4-4 5-2 1-4 1-7 1-2-1-5-2-7-1s-2 1-3 3l-1-1-1-1h-1l-1-2c-1-3-2-7-3-11-1-7-3-13-6-20-2-4-5-6-6-10h-1c-1-1-2-1-3-2l-2 1c-1 1-3 1-4 2h-1v1c1 1 1 2 1 3-1-1-2-4-3-5-2-1-3-2-4-3l-2 1c1 3 3 8 3 11h0c-2-4-4-9-6-12h0c0-1 0-2-1-3-6 0-12-1-18-4-1-1-2-2-3-4l-1 4c-4 1-12 0-17-1-7 1-12 2-19 5l-7 6-1 1-1 1c-2 0-1 0-2-1-1-2-2-3-3-4-1-2-1-4-1-6s0-4 1-6 2-4 4-5c2-2 4-4 6-5 1-2 7-4 9-4l6-2c2-3 4-5 5-7 0-1-1-1-1-2l1-1h1v-1h0c1-2 1-6 0-8 0-1-1-2-2-2-3-2-5-3-9-2l-6 2h-1 0c-1 0-4 2-5 2h-1l2-5c1-3 2-5 2-8v-1c1 0 1-1 2-2 1-2 3-3 5-4l6-6c5-3 15-3 22-3h0c3-2 4-3 6-5v-2c-1-3-7-6-10-8h4v-1z" class="V"></path><path d="M759 512v1c1 0 3 0 4-1l1-1c1 1 1 1 1 3-1 0-3 1-5 2h-1c0-2-1-2 0-4z" class="F"></path><path d="M760 516c2-1 4-2 5-2 1 3 2 7 2 10-2-3-4-6-7-8z" class="a"></path><path d="M680 480h-3l-2-1h0c0-1 0-1-1-2h6 0c1 1 3 1 4 1l10 3-13-1h-1z" class="C"></path><path d="M759 516h1c3 2 5 5 7 8l2 6h-1c-3-4-6-8-10-11 1-1 1-2 1-3z" class="N"></path><defs><linearGradient id="FC" x1="651.037" y1="476.539" x2="673.024" y2="476.018" xlink:href="#B"><stop offset="0" stop-color="#949390"></stop><stop offset="1" stop-color="#d4d3cd"></stop></linearGradient></defs><path fill="url(#FC)" d="M658 471c9 0 18 0 27 1l-2 1h-1c-2-1-4 0-6-1h-3v3c0 1-1 2-1 3 1 1 1 1 0 2l2 2 1-1v-1h5 1v1c1 0 2 1 3 1h0c0 1-1 1-2 1h-2-1c-2-1-5 0-8 0v-1h-4l-1 2c3 0 6-1 8 0 1 1 2 1 3 0l1 1h-1-12l-7-1h-3l-3-1c0-1-1-2 0-3v-3c-1-2-1-3-1-5l7-1z"></path><defs><linearGradient id="FD" x1="656.164" y1="479" x2="665.095" y2="484.196" xlink:href="#B"><stop offset="0" stop-color="#a9a7a4"></stop><stop offset="1" stop-color="#cbcac6"></stop></linearGradient></defs><path fill="url(#FD)" d="M658 484l-5-3c6-1 12-1 18 1h-4l-1 2c3 0 6-1 8 0 1 1 2 1 3 0l1 1h-1-12l-7-1z"></path><path d="M701 492h0l1-1c15 6 29 12 40 25l6-9-4-6c4 4 7 9 10 14 2-3 2-6 3-10 1 2 2 5 2 7-1 2 0 2 0 4 0 1 0 2-1 3-1-1-2-1-3-1l-1 1-8 10c3 3 5 6 7 10 5 8 6 17 7 27 1 2 2 6 1 9 0 2-3 4-4 5-2 1-4 1-7 1l1-1c3 0 6-1 8-4 1-1 1-2 1-4l-3-16c-1-6-3-13-7-18-4-6-11-10-16-14l1-1 1 1v-1-1h1 1l2-4c0-1 0-2-1-3-3-5-9-9-14-12-7-5-16-8-24-11z" class="T"></path><path d="M744 527l-4-3c1-4 6-10 9-13 1 2 2 3 4 5h0l-1 1 2 2-8 10-2-2z" class="G"></path><path d="M744 527c3-3 5-7 8-10l2 2-8 10-2-2z" class="F"></path><defs><linearGradient id="FE" x1="695.832" y1="478.753" x2="699.235" y2="464.723" xlink:href="#B"><stop offset="0" stop-color="#848584"></stop><stop offset="1" stop-color="#cbc9c4"></stop></linearGradient></defs><path fill="url(#FE)" d="M637 475v-1c1 0 1-1 2-2 1-2 3-3 5-4l6-6c5-3 15-3 22-3 2 1 4 1 7 2h1c1-1 1-1 2 0h4l4 1c2 0 4 0 6 1h2l2 1h1c2 0 3 0 4 1h0c13 4 25 9 37 15 4 3 9 6 13 9-3 1-7 0-10 0l3 4h0l-4-3c-16-13-40-15-59-18-9-1-18-1-27-1l-7 1c0 2 0 3 1 5v3c-1 1 0 2 0 3-1-1-2-1-2-2l-2-1h0c0 1 0 1 1 2l-10 4h0c-1 0-4 2-5 2h-1l2-5c1-3 2-5 2-8z"></path><defs><linearGradient id="FF" x1="645.951" y1="464" x2="651.603" y2="466.771" xlink:href="#B"><stop offset="0" stop-color="#5b5a5a"></stop><stop offset="1" stop-color="#737372"></stop></linearGradient></defs><path fill="url(#FF)" d="M637 475v-1c1 0 1-1 2-2 1-2 3-3 5-4l6-6c5-3 15-3 22-3 2 1 4 1 7 2-4 1-7 0-10 1v-1h1v-1h-1c-2 1-6 0-9 1h-4-2c0 2-2 6-2 7h1c1 0 2 0 3 1-3 0-7 0-10 1-1 0-3 0-5 1v1l-4 3z"></path><path d="M656 469c18 1 36 2 55 6 11 2 25 5 33 14v1c-16-13-40-15-59-18-9-1-18-1-27-1-4 0-8-1-11 0h-2l-4 1v-1c2-1 4-1 5-1 3-1 7-1 10-1z" class="W"></path><path d="M641 472l4-1h2c3-1 7 0 11 0l-7 1c0 2 0 3 1 5v3c-1 1 0 2 0 3-1-1-2-1-2-2l-2-1h0c0 1 0 1 1 2l-10 4h0c-1 0-4 2-5 2h-1l2-5c1-3 2-5 2-8l4-3z" class="X"></path><path d="M648 480v-3h-1l1-2h0l-1-1 1-1 3-1c0 2 0 3 1 5v3c-1 1 0 2 0 3-1-1-2-1-2-2l-2-1z" class="L"></path><path d="M635 483c2-1 3-2 4-2s0 0 1-1h1 2c2-1 3-1 5 0h0c0 1 0 1 1 2l-10 4h0c-1 0-4 2-5 2h-1l2-5z" class="Y"></path><path d="M721 518c2 0 6-1 8-1 1 0 3 2 4 2 1 1 3 2 4 3h-1v1 1l-1-1-1 1c5 4 12 8 16 14 4 5 6 12 7 18l3 16c0 2 0 3-1 4-2 3-5 4-8 4l-1 1c-2-1-5-2-7-1s-2 1-3 3l-1-1-1-1h-1l-1-2c-1-3-2-7-3-11-1-7-3-13-6-20-2-4-5-6-6-10h-1c-1-1-2-1-3-2l-2 1c-1 1-3 1-4 2h-1v1c1 1 1 2 1 3-1-1-2-4-3-5-2-1-3-2-4-3l-2 1c1 3 3 8 3 11h0c-2-4-4-9-6-12h0c0-1 0-2-1-3 6-2 9-6 14-9 2-2 5-3 8-4l1-1z" class="h"></path><path d="M737 579l7-4c2 1 2 3 4 4 0 0 2 1 3 1l-1 1c-2-1-5-2-7-1s-2 1-3 3l-1-1-1-1h-1l-1-2h1z" class="M"></path><path d="M733 550h1v-2c2 0 4 1 5 2 3 1 3 3 6 3l3 6c0 1 1 3 1 4-1 2 0 5 0 8-1-2-2-4-3-7h-1c0 2 1 4 0 6h-1-1l-1-2-1 1h-1v1c-1-1-1-3-1-4l-4-11c0-2-2-3-2-5z" class="S"></path><path d="M742 566h1 1c0 1 0 3 1 4h-1-1l-1-2v-2z" class="b"></path><path d="M739 563l1-1v1c1 1 1 0 2 1v2 2l-1 1h-1l-1-6z" class="Z"></path><path d="M737 556c0-1-1-2-1-3h1c1 1 2 1 3 2 2 0 4 1 5 3v1c1 1 1 2 2 3v-1h0l1-2c0 1 1 3 1 4-1 2 0 5 0 8-1-2-2-4-3-7v-1l-1-1c-1-2-2-3-4-5l2 7h-1v-1l-1-2c0-2-2-4-4-5z" class="I"></path><path d="M733 550h1v-2c2 0 4 1 5 2 3 1 3 3 6 3l3 6-1 2h0v1c-1-1-1-2-2-3v-1c-1-2-3-3-5-3-1-1-2-1-3-2h-1c0 1 1 2 1 3 0 2 1 4 2 7l1 6v1c-1-1-1-3-1-4l-4-11c0-2-2-3-2-5z" class="f"></path><path d="M721 538h0l-4-5c3 1 7 2 10 4l9 5c2 1 4 3 5 5l4 6c-3 0-3-2-6-3-1-1-3-2-5-2v2h-1c0 2 2 3 2 5l4 11-1-1c-1-1-1-2-2-4 0-1-1-1-1-2 0 2 1 4 1 6v5c0 1 0 0-1 1l2 8h-1c-1-3-2-7-3-11-1-7-3-13-6-20-2-4-5-6-6-10z" class="Q"></path><path d="M731 557c0-3-2-5-2-7 1-1 1-1 3-1l1 1c0 2 2 3 2 5l-1 1v3l-2-2h-1z" class="a"></path><path d="M731 557h1l2 2v-3l1-1 4 11-1-1c-1-1-1-2-2-4 0-1-1-1-1-2 0 2 1 4 1 6v5c0 1 0 0-1 1l-4-14z" class="b"></path><path d="M729 544c0-2-2-4-3-6l1-1 9 5c2 1 4 3 5 5h-2c-1-1-1-1-2-1 0-1-1-2-2-2h-4v-1l-1 1 2 2c-1 0-2-1-3-2z" class="X"></path><path d="M721 538h0l-4-5c3 1 7 2 10 4l-1 1c1 2 3 4 3 6-2-1-4-4-6-5 2 3 3 6 4 9h0c-2-4-5-6-6-10z" class="H"></path><defs><linearGradient id="FG" x1="708.651" y1="521.674" x2="730.318" y2="539.99" xlink:href="#B"><stop offset="0" stop-color="#000100"></stop><stop offset="1" stop-color="#252423"></stop></linearGradient></defs><path fill="url(#FG)" d="M721 518c2 0 6-1 8-1 1 0 3 2 4 2 1 1 3 2 4 3h-1v1 1l-1-1-1 1c-5-3-7-4-12-2-3 1-7 3-10 5l14 7c1-1 1 0 2-1 3 3 7 6 10 9 7 7 12 18 14 28h-1l-2-7c0-1-1-3-1-4l-3-6-4-6c-1-2-3-4-5-5l-9-5c-3-2-7-3-10-4l4 5h0-1c-1-1-2-1-3-2l-2 1c-1 1-3 1-4 2h-1v1c1 1 1 2 1 3-1-1-2-4-3-5-2-1-3-2-4-3l-2 1c1 3 3 8 3 11h0c-2-4-4-9-6-12h0c0-1 0-2-1-3 6-2 9-6 14-9 2-2 5-3 8-4l1-1z"></path><defs><linearGradient id="FH" x1="709.214" y1="531.894" x2="712.284" y2="538.101" xlink:href="#B"><stop offset="0" stop-color="#525251"></stop><stop offset="1" stop-color="#6f6f6e"></stop></linearGradient></defs><path fill="url(#FH)" d="M704 535c2-2 4-3 6-4 4 1 7 4 10 7-1-1-2-1-3-2l-2 1c-1 1-3 1-4 2h-1v1c1 1 1 2 1 3-1-1-2-4-3-5-2-1-3-2-4-3z"></path><path d="M672 443c1 0 3-1 4 0s3 0 4 0c2 0 5 0 7 1 2 0 3-1 5 0v1l-1 1c1 1 4 1 5 2 4 0 8 0 12 1h3l2 1h2l6 2h2c1 1 2 1 2 1 10 2 21 7 30 11 0 2 1 4 2 5 3 2 6 3 9 5 2 2 4 5 5 8 1 2 3 5 3 7l-1 1-1 1h-1c-3 0-5-1-8-2h-6c1 1 5 5 6 7l-8-7c-4-3-9-6-13-9-12-6-24-11-37-15h0c-1-1-2-1-4-1h-1l-2-1h-2c-2-1-4-1-6-1l-4-1h-4c-1-1-1-1-2 0h-1c-3-1-5-1-7-2h0c3-2 4-3 6-5v-2c-1-3-7-6-10-8h4v-1z" class="V"></path><path d="M678 454c2 0 4 0 7 1 1 1 3 1 4 1 4 1 9 2 12 2-2 1-2 0-4 0-4-1-10-1-14-2h-4c-1 0-3 2-4 2v1h0l2 1h1l2 1h-1c-3-1-5-1-7-2h0c3-2 4-3 6-5z" class="C"></path><path d="M705 465c8 2 16 5 24 8l18 9c5 3 9 5 15 6 2 0 3 1 5 2h6l-1 1h-1c-3 0-5-1-8-2h-6c1 1 5 5 6 7l-8-7c-4-3-9-6-13-9-12-6-24-11-37-15h0z" class="B"></path><defs><linearGradient id="FI" x1="675.344" y1="440.296" x2="692.922" y2="458.462" xlink:href="#B"><stop offset="0" stop-color="#b4b3af"></stop><stop offset="1" stop-color="#e8e7e2"></stop></linearGradient></defs><path fill="url(#FI)" d="M672 443c1 0 3-1 4 0s3 0 4 0c2 0 5 0 7 1 2 0 3-1 5 0v1l-1 1c1 1 4 1 5 2-4 0-8-1-11 0l15 3c1 0 3 0 4 1-1 1-4 0-6 0-4-1-8-2-12-2v1h1 3c5 2 10 2 15 4h1v1c-3 0-6-1-9 1h2s1 0 2 1c-3 0-8-1-12-2-1 0-3 0-4-1-3-1-5-1-7-1v-2c-1-3-7-6-10-8h4v-1z"></path><defs><linearGradient id="FJ" x1="665.002" y1="518.159" x2="729.107" y2="492.777" xlink:href="#B"><stop offset="0" stop-color="#dad9d6"></stop><stop offset="1" stop-color="#fffffe"></stop></linearGradient></defs><path fill="url(#FJ)" d="M648 480l2 1c0 1 1 1 2 2l3 1h3l7 1h12c8 1 17 3 24 6h1l-1 1h0c8 3 17 6 24 11 5 3 11 7 14 12 1 1 1 2 1 3l-2 4h-1c-1-1-3-2-4-3-1 0-3-2-4-2-2 0-6 1-8 1l-1 1c-3 1-6 2-8 4-5 3-8 7-14 9-6 0-12-1-18-4-1-1-2-2-3-4l-1 4c-4 1-12 0-17-1-7 1-12 2-19 5l-7 6-1 1-1 1c-2 0-1 0-2-1-1-2-2-3-3-4-1-2-1-4-1-6s0-4 1-6 2-4 4-5c2-2 4-4 6-5 1-2 7-4 9-4l6-2c2-3 4-5 5-7 0-1-1-1-1-2l1-1h1v-1h0c1-2 1-6 0-8 0-1-1-2-2-2-3-2-5-3-9-2l-6 2h-1l10-4c-1-1-1-1-1-2h0z"></path><path d="M668 498c1-1 3-1 4-2h1l1-1c1 0 1 0 3 1h5c1 1 2 1 3 1h0-9v1h1c-1 1-2 1-3 1v-1h-6z" class="b"></path><path d="M690 509c8 1 18 4 26 7 2 1 4 1 5 2l-1 1h0l-7-3c-1 1-2 1-3 1h0-2v3h0l-2-1c-1-1-2-3-3-4-5-2-10-2-14-5l1-1z" class="Y"></path><path d="M706 519c0-3-2-4-3-6 3 0 7 2 10 3-1 1-2 1-3 1h0-2v3h0l-2-1z" class="b"></path><path d="M655 484h3l7 1h12c8 1 17 3 24 6h1l-1 1h0l-25-4c-5-2-11 0-16-1l-1 1-1-1c-1-1-1-2-3-3z" class="L"></path><path d="M655 484h3l7 1 1 1c-2 0-5-1-6 1l-1 1-1-1c-1-1-1-2-3-3z" class="G"></path><path d="M660 487c5 1 11-1 16 1-3 0-6 0-9 1h-5c1 1 1 1 2 1h1 1l1-1 1 1c2 1 8 1 11 2h2c-4 0-8 0-11 1h-6-1c0-1 1-1 1-1-1-1-2-2-3-2v1c1 3 0 6 0 8v1l-1 1h4 0l-2-1c0-1 0-1 1-1 0-1 1-1 1-1h1 3 6l-1 1c-2 0-8-1-10 1 2 1 9 0 12 0 3 1 6 1 9 2-5 0-12-1-18-1-2 0-4 1-6 1l-1 1 2 1c-1 0-3 0-4 1 2 0 5-1 7 0-3 0-7 1-10 0 1-1 6-5 6-7v-1c1-3 0-6-1-9l1-1z" class="C"></path><path d="M648 480l2 1c0 1 1 1 2 2l3 1c2 1 2 2 3 3l1 1c1 3 2 6 1 9v1c0 2-5 6-6 7 3 1 7 0 10 0l26 4-1 1-18-3c-6-1-13-1-19 0l-6 3-1-1 6-2c2-3 4-5 5-7 0-1-1-1-1-2l1-1h1v-1h0c1-2 1-6 0-8 0-1-1-2-2-2-3-2-5-3-9-2l-6 2h-1l10-4c-1-1-1-1-1-2h0z" class="M"></path><path d="M652 507c6-1 13-1 19 0l18 3c4 3 9 3 14 5 1 1 2 3 3 4l2 1h0v-3h2 0c1 0 2 0 3-1l7 3h0c-3 1-6 2-8 4-5 3-8 7-14 9-6 0-12-1-18-4-1-1-2-2-3-4l-1 4c-4 1-12 0-17-1-7 1-12 2-19 5l-7 6-1 1-1 1c-2 0-1 0-2-1-1-2-2-3-3-4-1-2-1-4-1-6s0-4 1-6 2-4 4-5c2-2 4-4 6-5 1-2 7-4 9-4l1 1 6-3z" class="L"></path><path d="M648 515c7-1 13-1 20-1h1-5c-4 0-6 1-10 2-2 1-4 1-6 2v-1-2z" class="I"></path><path d="M668 514v-2h1 0 2l1 1h4c1 0 2 0 4 1h9c-1 1-1 2-2 3-5-2-13-2-18-3h-1z" class="J"></path><path d="M689 514l1-1c1 1 1 1 1 3-1 1-2 3-3 5l-3 3c-2 2-3 3-5 4-1-1-2-2-3-4l-1 4c-4 1-12 0-17-1h1l-1-1h-2c-1 0-1 0-2 1h0c1-2 2-4 2-6l1-3c2-2 3-3 6-4h5c5 1 13 1 18 3 1-1 1-2 2-3z" class="Z"></path><path d="M689 514l1-1c1 1 1 1 1 3-1 1-2 3-3 5l-3 3c-2 2-3 3-5 4-1-1-2-2-3-4l1-1c5-1 6-3 9-6 1-1 1-2 2-3z" class="B"></path><path d="M703 515c1 1 2 3 3 4l2 1h0v-3h2 0c1 0 2 0 3-1l7 3h0c-3 1-6 2-8 4-5 3-8 7-14 9-6 0-12-1-18-4 2-1 3-2 5-4l3-3c1-2 2-4 3-5l12-1z" class="Z"></path><path d="M703 515c1 1 2 3 3 4l-1 1h0l-5 5h-11-1c-1-1-2-1-3-1l3-3c1-2 2-4 3-5l12-1z" class="S"></path><path d="M703 515c1 1 2 3 3 4l-1 1h0l-1-1c-5 0-11 2-16 2 1-2 2-4 3-5l12-1z" class="C"></path><defs><linearGradient id="FK" x1="627.17" y1="527.873" x2="639.612" y2="529.675" xlink:href="#B"><stop offset="0" stop-color="#605f5e"></stop><stop offset="1" stop-color="#9e9d9a"></stop></linearGradient></defs><path fill="url(#FK)" d="M646 510l6-3c-2 3-4 5-7 8h0 0v1l3-1v2 1c2-1 4-1 6-2 4-1 6-2 10-2-3 1-4 2-6 4l-1 3c0 2-1 4-2 6h0c1-1 1-1 2-1h2l1 1h-1c-7 1-12 2-19 5l-7 6-1 1-1 1c-2 0-1 0-2-1-1-2-2-3-3-4-1-2-1-4-1-6s0-4 1-6 2-4 4-5c2-2 4-4 6-5 1-2 7-4 9-4l1 1z"></path><path d="M632 539v-1-5c1 1 1 3 1 5l-1 1z" class="G"></path><path d="M628 525h1c-1 4-3 6-3 10-1-2-1-4-1-6 1-2 2-3 3-4z" class="K"></path><path d="M632 518c2 0 3-1 4-2l3 1v1c-4 1-8 3-10 7h-1c0-3 2-5 4-7z" class="H"></path><path d="M636 513c-1 2-2 3-4 5s-4 4-4 7c-1 1-2 2-3 4 0-2 0-4 1-6s2-4 4-5c2-2 4-4 6-5z" class="O"></path><path d="M646 510l6-3c-2 3-4 5-7 8h0 0v1c-2 0-4 1-6 2v-1l-3-1c-1 1-2 2-4 2 2-2 3-3 4-5s7-4 9-4l1 1z" class="Y"></path><path d="M636 513c1-2 7-4 9-4l1 1c-2 2-4 4-7 5l-3 1c-1 1-2 2-4 2 2-2 3-3 4-5z" class="e"></path><defs><linearGradient id="FL" x1="640.213" y1="521.795" x2="656.886" y2="525.504" xlink:href="#B"><stop offset="0" stop-color="#a7a6a2"></stop><stop offset="1" stop-color="#dfdfdb"></stop></linearGradient></defs><path fill="url(#FL)" d="M648 517v1c2-1 4-1 6-2 4-1 6-2 10-2-3 1-4 2-6 4l-1 3c0 2-1 4-2 6h0c1-1 1-1 2-1h2l1 1h-1c-7 1-12 2-19 5h-1c1-2 2-4 4-5 1-1 2-1 2-2h0c-1-1-4 1-6 1v-2c1-1 0-1 0-2l2-2h2c0-1 1-1 2-1s2-1 3-2h0z"></path><defs><linearGradient id="FM" x1="782.955" y1="246.433" x2="820.18" y2="368.796" xlink:href="#B"><stop offset="0" stop-color="#d7d7d4"></stop><stop offset="1" stop-color="#fefdf9"></stop></linearGradient></defs><path fill="url(#FM)" d="M829 203h0c5-1 12-1 17-1h1c1 2 2 3 3 4h0v1l4 6 2 2c0-1 1-1 1-1 1-1 2-1 3-2h1v1h1c4 9 7 19 8 28 0 4 1 9 0 13 0 3-1 8-1 11v6c2 0 5 0 6-1 3-2 4-5 7-6 2-1 3-1 5-1 1 0 3 0 4-1 3-1 6-3 8-5 0 3 0 7-1 11-1 1-1 2-2 4l1-4v-8c-1 2-3 3-5 5h0c-3 3-6 5-9 8-1 6-1 12-1 17-1 11-3 22-8 31-1 1-3 4-4 5h-1c-1 1-1 2-2 3s-2 2-2 3c-1 1-3 2-3 3-3 2-6 6-8 8-2 0-3 3-5 4-1 0-2 1-2 2-1 0-2 1-2 1-1 0-2 1-3 2-1 0-1 1-2 2-1 0-1 1-2 1l-2 2-2 1s-1 1-2 1l-1 1c-1 1-2 1-3 2s-2 1-3 2l-2 1-6 4-7 3c-1 0-2 1-4 1l-1 1h-1l-6 3c-3 0-5 0-8 1l-13-2v1h-1c-1 0-2 0-3-1-3 0-6 0-8 1h-1c-2 0-4 1-5 1l-4 2h0l-3 1-6 2v-1l-1-1h-2c-1 0-2-1-3-2l-1 1c-1 2 0 3-2 3-1-4-5-8-8-10v-1l2-1-4-4-11-9 9-7h0l1-1c-1-2-3-4-4-7l-2-4c-3-2-8-6-11-7l-5-3c-4-2-8-2-12-2l1-2c2-1 7-4 11-3 0 0 0 1 1 1h3c0-1-1-1-1-1 0-1 0-2 1-3 1-2 2-4 3-5h1c-2 0-3 0-5-2 0-2-1-3-2-5h-2s-1 0-1-1l-15 6c1-3 2-7 3-10v-1l2-7 1-4h1l2 1c1 0 1 1 2 1h1v-2h0c-2 0-3-2-4-3l1-1c1 0 2 0 3-1l-1-1c1-2 1-2 2-3v1h1c-1-6-2-11 0-17 1-3 3-6 6-8 3-1 6-1 9 0v1c2 1 3 1 4 1l1-1c2 1 3 2 5 4 1 1 2 2 3 2l1-1v-1l-1-1c1-2 3-3 3-5 1-1 0-2 1-4l2 1h0l1-1c2 1 3 2 4 3l-1 3c-1 2-2 5-3 7s-2 3-1 5h1c1-5 3-9 5-14l1-2c1 1 2 2 3 2h0v-2c1-1 2 0 4 0 1 1 3 2 4 2l3 1c1 1 2 2 2 4 4-5 9-9 15-11v1l-4 2c-2 1-4 2-5 4h0l2-1c3-2 8-4 12-5 3-2 6-4 9-5 4 0 5 1 8 3h4s1 1 2 1c1 1 3 2 4 4l2 3v1c2 1 3 3 4 4l1 1h1c1 1 1 2 2 3 1 2 3 3 4 4l1 1 1-1v-1l-2-1h1c1 0 1 0 2 1h0c1-1 1-1 1-2 0-3-6-7-8-9v-1l8 6c0 1 2 2 2 3 1 1-1 4 0 5 2 0 3-1 5-3 1 0 3-2 3-2 7-8 7-15 6-24 0-6-1-12-3-17l-1 2c-1-2-2-5-2-8-1-1-1-2-2-4 0-1-1-5-2-6 1 1 2 2 3 2v-1l-3-7z"></path><path d="M741 256c2 4 1 8 0 11l-1 1c0-2 0-4 1-5 0-1 0-2-1-3-1 3-2 5-3 8 0 1 0 2-1 3v5h0c-1-3-2-4-1-6h1c1-5 3-9 5-14z" class="a"></path><path d="M720 303c1 0 2-1 3 0 2 1 2 2 3 3 3 2 10 4 12 8h-1c-3-2-7-4-10-6l-1 1-1-1h-1-2v-1h1 1v-1l-2-1c-2 0-4 0-6-1 2 0 3 0 4-1z" class="S"></path><path d="M728 298l6 3 1 2c-1 0-1 1-1 2v2c0-1-1-1-2-2-3-1-5-2-8-4l4-3z" class="c"></path><path d="M753 355c1-1 7 0 10 1l11 3h2c6 2 14 1 21 1 1-1 3 0 4 0l1-1c1 0 2 0 3 1h-2 0c-2 1-3 1-4 1h-1c-5 0-11 1-15 0-1-1-3 0-4-1h-1-3c-5-1-9-2-13-3-3-1-9 0-11-1l2-1z" class="Z"></path><path d="M709 307c0-1 0-1 1-1 1-2 4-2 6-2 2 1 4 1 6 1-1 1-3 1-4 1l-1 1c1 1 1 2 3 3v-1l-1-1h1c1 0 2 1 3 2 2 1 3 2 4 4-5-3-10-4-16-5-2 0-2-1-2-2z" class="E"></path><defs><linearGradient id="FN" x1="724.282" y1="318.688" x2="733.718" y2="320.812" xlink:href="#B"><stop offset="0" stop-color="#787775"></stop><stop offset="1" stop-color="#959591"></stop></linearGradient></defs><path fill="url(#FN)" d="M722 313c1 1 4 2 5 1 3 2 6 4 8 7l-1 2c1 1 1 3 1 4l-2-1-3-3c-1-1-2-3-5-4l-4-4 1-2z"></path><path d="M706 305h3l-1 1 1 1c0 1 0 2 2 2 6 1 11 2 16 5-1 1-4 0-5-1-3-1-7 0-10 1-2 0-3 0-5-2 0-2-1-3-2-5h-2s-1 0-1-1l4-1z" class="N"></path><path d="M766 256c3-2 8-4 12-5 3-2 6-4 9-5 4 0 5 1 8 3 1 3 1 5 0 8l-5-1-1-1c-2-1-3-2-4-1-5 1-10 3-14 6 1-1 1-2 2-3 1 0 3-2 4-2h2c1-1 2-1 3-2h1 1 1 2c1 0 2 1 3 2s3 0 5 0v-1l1-1c-1-2-1-3-3-4-1-1-2-2-4-2l-3 3c-4-1-5 0-8 2l-1 1c-1 0-2 1-4 1-1 0-4 2-6 2h-1z" class="d"></path><path d="M756 257c1 1 2 2 2 4 4-5 9-9 15-11v1l-4 2c-2 1-4 2-5 4h0c-1 2-3 4-4 5h-2l-1-2c-2-2-4-1-7-2h-2v1h0l2 4c1 1 1 1 1 2v1c1 1 1 1 1 3 1 1 1 1 0 2 0-1-1-2-1-3l-1-2c0-2-1-3-2-3 0-1 0-1-1-2 0-2-1-3-2-5v-2c1-1 2 0 4 0 1 1 3 2 4 2l3 1z" class="a"></path><path d="M874 276c0-2 0-3 1-4 2-2 5-5 7-6s8-1 10-1c-3 3-6 5-9 8-1 6-1 12-1 17-1 11-3 22-8 31-1 1-3 4-4 5h-1c1-2 3-5 5-8s3-7 4-11c3-11 3-23 4-35l-2 2c-4 2-4 5-5 9v-7h-1z" class="L"></path><path d="M748 375c2-2 4-4 5-6 0 3-1 3-2 5 1 0 0 0 1-1 1 0 1 0 2-1h0c3 1 5 1 7 0h11c-2 0-4 1-6 1l-2 2h1 2c2 0 8 0 9 1h1v1h-1c-1 0-2 0-3-1-3 0-6 0-8 1h-1c-2 0-4 1-5 1l-4 2h0l-3 1-6 2v-1l-1-1h-2c-1 0-2-1-3-2l5-2 3-2z" class="S"></path><path d="M740 379l5-2c2 2 7 3 10 3l-3 1-6 2v-1l-1-1h-2c-1 0-2-1-3-2z" class="X"></path><path d="M735 321l3 2 3 2c3 3 8 9 8 14 0 2-1 3-2 4h-1l-3 1c-2 0-4-1-6-2 0 0 1-1 2-1 0-1 0-1 1-1-1-5-2-9-5-13 0-1 0-3-1-4l1-2z" class="S"></path><path d="M735 321l3 2c0 3 0 5 1 8l1 3c1 3 4 6 6 9l-3 1c-2 0-4-1-6-2 0 0 1-1 2-1 0-1 0-1 1-1-1-5-2-9-5-13 0-1 0-3-1-4l1-2z" class="d"></path><defs><linearGradient id="FO" x1="751.453" y1="266.02" x2="738.578" y2="277.452" xlink:href="#B"><stop offset="0" stop-color="#989792"></stop><stop offset="1" stop-color="#d0d0c9"></stop></linearGradient></defs><path fill="url(#FO)" d="M741 256l1-2c1 1 2 2 3 2h0c1 2 2 3 2 5 1 1 1 1 1 2 1 0 2 1 2 3-1-1-2-1-2-3l-1 8c3 7 2 14 1 21-1-5-4-10-5-15-1-3-2-5-2-7-1-1-1-2-1-2l1-1c1-3 2-7 0-11z"></path><path d="M747 271c-1-3-2-8-3-12 0-1 0-1 1-1l2 3c1 1 1 1 1 2 1 0 2 1 2 3-1-1-2-1-2-3l-1 8z" class="d"></path><defs><linearGradient id="FP" x1="725.266" y1="257.414" x2="732.528" y2="268.629" xlink:href="#B"><stop offset="0" stop-color="#8a8a87"></stop><stop offset="1" stop-color="#bebdb5"></stop></linearGradient></defs><path fill="url(#FP)" d="M729 261c1-2 3-3 3-5 1-1 0-2 1-4l2 1h0l1-1c2 1 3 2 4 3l-1 3c-1 2-2 5-3 7s-2 3-1 5c-1 2 0 3 1 6h0v1h-1c-1-2-1-3-1-4l-1-1v-1c0-1 0-2-1-3h0c-1 1-2 1-2 1-2 1-3 1-4 2h0 0c3 2 5 4 5 7-5-5-9-9-16-11 1-1 1-1 3-2l2-4c-1-1-2-2-3-2h-1v-1c2 1 3 1 4 1l1-1c2 1 3 2 5 4 1 1 2 2 3 2l1-1v-1l-1-1z"></path><path d="M771 260c4-3 9-5 14-6 1-1 2 0 4 1h-1l-3 3c-1 2-3 4-3 7l-1-1v-1h-1l1-2c-3-1-5 1-8 2h-1c-9 7-15 20-16 31-1 6 0 11 1 15v1c-3-5-4-9-3-15 1-14 6-26 17-35z" class="N"></path><path d="M772 263c4-3 9-4 13-7 1 0 1-1 3-1l-3 3c-1 2-3 4-3 7l-1-1v-1h-1l1-2c-3-1-5 1-8 2h-1z" class="K"></path><defs><linearGradient id="FQ" x1="721.018" y1="266.685" x2="721.75" y2="286.508" xlink:href="#B"><stop offset="0" stop-color="#0e0d0e"></stop><stop offset="1" stop-color="#3d3e3d"></stop></linearGradient></defs><path fill="url(#FQ)" d="M722 285c0-2-2-4-3-6s-3-5-5-7c0-1-1-1-1 0-3 0-5 3-6 5v-3c0-2 1-5 2-6 2-2 4-2 6-1 7 2 11 6 16 11 3 5 7 9 9 15 3 7 4 16 8 23 1 3 4 6 6 9h0c-3-3-6-6-8-10-4-10-4-30-19-31h0c-1 0-1 0-2-1-1 1-2 1-3 2z"></path><defs><linearGradient id="FR" x1="700.287" y1="266.597" x2="723.06" y2="279.668" xlink:href="#B"><stop offset="0" stop-color="#565655"></stop><stop offset="1" stop-color="#8f8f8c"></stop></linearGradient></defs><path fill="url(#FR)" d="M701 282c-1-6-2-11 0-17 1-3 3-6 6-8 3-1 6-1 9 0v1 1h1c1 0 2 1 3 2l-2 4c-2 1-2 1-3 2-2-1-4-1-6 1-1 1-2 4-2 6v3c1-2 3-5 6-5 0-1 1-1 1 0 2 2 4 5 5 7s3 4 3 6c-3 3-5 4-9 5h0c-2 0-4 0-6-1-3-1-5-4-6-7z"></path><defs><linearGradient id="FS" x1="736.422" y1="363.171" x2="741.042" y2="375.293" xlink:href="#B"><stop offset="0" stop-color="#9b9a96"></stop><stop offset="1" stop-color="#bfbeb9"></stop></linearGradient></defs><path fill="url(#FS)" d="M727 367l-11-9 9-7c1 1 1 1 1 2v2 2 1c2 1 3 2 6 3l11-2c3 1 7 2 9 5 1 1 1 2 1 3v2c-1 2-3 4-5 6l-3 2-5 2-1 1c-1 2 0 3-2 3-1-4-5-8-8-10v-1l2-1-4-4z"></path><path d="M738 361c2 0 4 0 6 1 1 0 2 1 2 1-2 0-6 0-8 1v-2-1z" class="Q"></path><path d="M731 371c1 1 5 6 6 6 3 0 5-1 8-2h3l-3 2-5 2-1 1c-1 2 0 3-2 3-1-4-5-8-8-10v-1l2-1z" class="N"></path><path d="M726 358c2 1 3 2 6 3l11-2c3 1 7 2 9 5 1 1 1 2 1 3v2c-1 2-3 4-5 6h-3c1-2 3-3 4-5v-2c-1-2-1-3-3-5 0 0-1-1-2-1-2-1-4-1-6-1v1c-3-1-8 1-10-1-2-1-1-1-2-3z" class="D"></path><path d="M749 370v-3h1l1 1 1-2 1 1v2c-1 2-3 4-5 6h-3c1-2 3-3 4-5z" class="F"></path><defs><linearGradient id="FT" x1="717.496" y1="357.514" x2="729.04" y2="360.57" xlink:href="#B"><stop offset="0" stop-color="#6c6c6b"></stop><stop offset="1" stop-color="#848381"></stop></linearGradient></defs><path fill="url(#FT)" d="M727 367l-11-9 9-7c1 1 1 1 1 2v2 2 1c1 2 0 2 2 3 2 2 7 0 10 1l-2 1 1 2h0-1c-1-1-2-1-2-1-1 0-2 0-2 1h-1c-2 0-3 1-4 2z"></path><path d="M699 285l-1-1c1-2 1-2 2-3v1h1c1 3 3 6 6 7 2 1 4 1 6 1h0c4-1 6-2 9-5 1-1 2-1 3-2 1 1 1 1 2 1 2 7 4 12 7 17l-6-3-4 3c-1 0-3 0-4 1v1c-1 1-2 1-4 1s-5 0-6 2c-1 0-1 0-1 1l-1-1 1-1h-3l-4 1-15 6c1-3 2-7 3-10v-1l2-7 1-4h1l2 1c1 0 1 1 2 1h1v-2h0c-2 0-3-2-4-3l1-1c1 0 2 0 3-1z" class="M"></path><path d="M702 290h2l1 1-3 1h-3v-2h3z" class="N"></path><path d="M702 292l3-1c-1 2-3 3-5 3-1 1-2 1-3 1 1-1 1-2 3-2 0 0 1-1 2-1z" class="D"></path><path d="M709 298c3-1 5-1 7-1 0 1 0 2-1 3l-1 1-1-2c-1 0-2 0-4-1zm-10-13l-1-1c1-2 1-2 2-3v1l2 5-1 1-2-3z" class="O"></path><path d="M699 285l2 3v1l1 1h-3 0c-2 0-3-2-4-3l1-1c1 0 2 0 3-1z" class="D"></path><path d="M692 294c1 2 3 2 4 3v1l-1 1 1 1h-1c-2 0-3 1-5 1l2-7z" class="F"></path><path d="M720 302c-1-2-1-3-1-4l1-1c3 0 5 0 8 1l-4 3c-1 0-3 0-4 1z" class="C"></path><path d="M696 298c2-1 4-3 6-3 6-2 12-2 18-4 1 0 1 0 1-1 2 0 4 0 6 2l-1 1c-9 0-19 6-29 6l-1-1z" class="W"></path><defs><linearGradient id="FU" x1="693.065" y1="298.823" x2="706.206" y2="308.958" xlink:href="#B"><stop offset="0" stop-color="#262626"></stop><stop offset="1" stop-color="#4a4a4a"></stop></linearGradient></defs><path fill="url(#FU)" d="M709 298c2 1 3 1 4 1l1 2c-2 1-6 2-8 4l-4 1-15 6c1-3 2-7 3-10v-1c2 0 3-1 5-1 3 1 11-1 14-2z"></path><path d="M712 314c3-1 7-2 10-1l-1 2 4 4c3 1 4 3 5 4l3 3 2 1c3 4 4 8 5 13-1 0-1 0-1 1-1 0-2 1-2 1l-1-1h-1c-1-1-3-1-3-3-1-1-1-3-1-4l3-1c0-1-1-2-2-2 0-1-1-1-1-1-2 1-3 2-6 3l-1-1c-1 1-1 1-2 3 1 1 0 1 1 1l2 2c1 2 3 4 5 5l1 1c4 3 15 6 20 6l1-1c2 0 3 0 6 1h1c-1 1-1 2-2 2-1 1-4 1-5 2-6 0-12-1-17 1-1 0-2 0-3 1h1c1 0 2 1 3 2 2 0 3 0 5 1h2l-11 2c-3-1-4-2-6-3v-1-2-2c0-1 0-1-1-2h0l1-1c-1-2-3-4-4-7l-2-4c-3-2-8-6-11-7l-5-3c-4-2-8-2-12-2l1-2c2-1 7-4 11-3 0 0 0 1 1 1h3c0-1-1-1-1-1 0-1 0-2 1-3 1-2 2-4 3-5h1z" class="S"></path><path d="M726 350l3 3c3 1 9 0 12 0h2 0l-16 2v1l5 5c-3-1-4-2-6-3v-1-2-2c0-1 0-1-1-2h0l1-1z" class="C"></path><path d="M709 324l2-1 2 1s1-1 2-1l1-1h1l4 2 4 3c1 0 1 0 1-1l3-3h1l3 3 2 1c3 4 4 8 5 13-1 0-1 0-1 1-1 0-2 1-2 1l-1-1-3-3c-1-1-1-2 0-3l2-2h1c-1-2-3-3-5-4-2 0-3 2-5 3l-5-3c-3-2-5-3-7-4-2 0-3-1-5-1z" class="B"></path><path d="M729 323h1l3 3h-2c-2 0-3 1-5 0l3-3z" class="M"></path><defs><linearGradient id="FV" x1="696.03" y1="321.833" x2="708.277" y2="328.613" xlink:href="#B"><stop offset="0" stop-color="#494948"></stop><stop offset="1" stop-color="#757574"></stop></linearGradient></defs><path fill="url(#FV)" d="M692 327l1-2c2-1 7-4 11-3 0 0 0 1 1 1h3l1 1h0c2 0 3 1 5 1 2 1 4 2 7 4h-1l-3 3c-1 0-1 1-1 2 2 2 3 3 4 5-3-2-8-6-11-7l-5-3c-4-2-8-2-12-2z"></path><path d="M715 333c-2 0-3-1-4-2h0v-1c-1 0-1 0-2-1-1 0-1-1-1-1 1-1 3-1 4-2l-1 4c1 1 2 2 3 2 0 1 0 1 1 1z" class="G"></path><path d="M714 325c2 1 4 2 7 4h-1l-3 3c-1 0-1 1-1 2l-1-1c-1 0-1 0-1-1-1 0-2-1-3-2l1-4 2-1z" class="J"></path><path d="M712 314c3-1 7-2 10-1l-1 2 4 4c3 1 4 3 5 4h-1l-3 3c0 1 0 1-1 1l-4-3-4-2h-1l-1 1c-1 0-2 1-2 1l-2-1-2 1h0l-1-1c0-1-1-1-1-1 0-1 0-2 1-3 1-2 2-4 3-5h1z" class="U"></path><path d="M721 324c0-1 1-1 1-2 1-1 2-1 4-2l3 3-3 3c0 1 0 1-1 1l-4-3z" class="Q"></path><path d="M712 314c3-1 7-2 10-1l-1 2 4 4c-2-1-3-2-5-3-1 0-2 0-3 1h-4c0 1 3 3 4 4v1h0-1l-1 1c-1 0-2 1-2 1l-2-1-2 1h0l-1-1c0-1-1-1-1-1 0-1 0-2 1-3 1-2 2-4 3-5h1z" class="K"></path><path d="M712 314c3-1 7-2 10-1l-1 2 4 4c-2-1-3-2-5-3-1 0-1-1-2-1h-2c-1-1-2-1-3 0v1l-2-2h1z" class="H"></path><defs><linearGradient id="FW" x1="800.104" y1="279.21" x2="760.989" y2="290.124" xlink:href="#B"><stop offset="0" stop-color="#868685"></stop><stop offset="1" stop-color="#c1c0bb"></stop></linearGradient></defs><path fill="url(#FW)" d="M795 249h4s1 1 2 1c1 1 3 2 4 4l2 3v1c0 1-1 1-2 2h0l-6 6c-1 1 0 1-1 1l-16 15c0 1-2 3-3 3l-6 9h1l-1 3c-3 7-5 13-6 20l-2 4h-3l-5-11v-1c-1-4-2-9-1-15 1-11 7-24 16-31h1c3-1 5-3 8-2l-1 2h1v1l1 1c0-3 2-5 3-7l3-3h1l1 1 5 1c1-3 1-5 0-8z"></path><path d="M782 271c-1 0-1 1-1 2h1c-4 4-7 7-10 9 2-5 6-8 10-11z" class="J"></path><path d="M794 269c1 0 2-1 3-2h1l-16 15h0l-1 1h-1l1-2a57.31 57.31 0 0 1 11-11c1 1 0 1 1 0l1-1z" class="L"></path><path d="M766 292v5c-1 3-2 5-1 8h1c1-2 3-4 3-6 2-5 5-11 9-14h1l-6 9h1l-1 3c-3 7-5 13-6 20l-2 4h-3l-5-11v-1l4 7c0-5 1-11 1-17h0 1c1-3 2-5 3-7z" class="E"></path><path d="M773 294h1l-1 3c-3 7-5 13-6 20l-2 4h-3l-5-11v-1l4 7c0-5 1-11 1-17h0 1 0c-1 5-1 9 0 14h1l2-2c1-1 2-3 2-5 2-4 3-8 5-12z" class="B"></path><path d="M795 249h4s1 1 2 1c1 1 3 2 4 4l2 3v1c0 1-1 1-2 2h0l-6 6c-1 1 0 1-1 1h-1c-1 1-2 2-3 2 1-1 3-3 4-5h-3-1c0 1-1 1-1 1-3 1-5 2-7 3-2 0-3 1-4 3-4 3-8 6-10 11-2 3-4 5-5 8h0l-1-1c1-3 3-6 5-9v-1c1 0 1-1 2-2l1-2 2-2-1-1-1 1h-1c1-1 4-4 5-6h0c0-1 1-1 1-2v-1h1l1 1h1c0-3 2-5 3-7l3-3h1l1 1 5 1c1-3 1-5 0-8z" class="Y"></path><path d="M805 254l2 3v1c0 1-1 1-2 2h0c-1 0-4 2-4 2l1-2v-1c-4 1-6 2-9 3l2-2c3-2 7-4 10-6z" class="K"></path><path d="M795 249h4s1 1 2 1c1 1 3 2 4 4-3 2-7 4-10 6l-2 2c-3 2-6 3-9 4h-1v-1h-1c0-3 2-5 3-7l3-3h1l1 1 5 1c1-3 1-5 0-8z" class="J"></path><path d="M789 255l1 1v2c-2 0-3 1-4 2v-1l-1-1 3-3h1z" class="g"></path><path d="M790 256l5 1-1 2h-3l-1-1v-2z" class="f"></path><path d="M795 249h4s1 1 2 1l-2 1h0c-1 2-2 4-2 5-1 2-2 3-2 4l-2 2c-3 2-6 3-9 4h-1v-1c5-1 8-3 11-6l1-2c1-3 1-5 0-8z" class="R"></path><path d="M795 260c0-1 1-2 2-4 0-1 1-3 2-5h0l2-1c1 1 3 2 4 4-3 2-7 4-10 6z" class="W"></path><defs><linearGradient id="FX" x1="788.062" y1="269.373" x2="750.474" y2="301.409" xlink:href="#B"><stop offset="0" stop-color="#646564"></stop><stop offset="1" stop-color="#b9b8b5"></stop></linearGradient></defs><path fill="url(#FX)" d="M772 263h1c3-1 5-3 8-2l-1 2h1v1l1 1h-1l-1-1h-1v1c0 1-1 1-1 2h0c-1 2-4 5-5 6h1l1-1 1 1-2 2-1 2c-1 1-1 2-2 2v1c-2 3-4 6-5 9l1 1h0s-1 1-1 2c-1 2-2 4-3 7h-1 0c0 6-1 12-1 17l-4-7c-1-4-2-9-1-15 1-11 7-24 16-31z"></path><path d="M829 203h0c5-1 12-1 17-1h1c1 2 2 3 3 4h0v1l4 6 2 2c0-1 1-1 1-1 1-1 2-1 3-2h1v1h1c4 9 7 19 8 28 0 4 1 9 0 13 0 3-1 8-1 11v6c2 0 5 0 6-1 3-2 4-5 7-6 2-1 3-1 5-1 1 0 3 0 4-1 3-1 6-3 8-5 0 3 0 7-1 11-1 1-1 2-2 4l1-4v-8c-1 2-3 3-5 5h0c-2 0-8 0-10 1s-5 4-7 6c-1 1-1 2-1 4 0 5 0 10-1 16-2 14-9 26-19 35-15 13-34 25-54 23-9 0-19-6-25-13l-1-1c-5-5-8-10-12-15h3l2-4c1-7 3-13 6-20l1-3h-1l6-9c1 0 3-2 3-3l16-15c1 0 0 0 1-1l6-6h0c1-1 2-1 2-2 2 1 3 3 4 4l1 1h1c1 1 1 2 2 3 1 2 3 3 4 4l1 1 1-1v-1l-2-1h1c1 0 1 0 2 1h0c1-1 1-1 1-2 0-3-6-7-8-9v-1l8 6c0 1 2 2 2 3 1 1-1 4 0 5 2 0 3-1 5-3 1 0 3-2 3-2 7-8 7-15 6-24 0-6-1-12-3-17l-1 2c-1-2-2-5-2-8-1-1-1-2-2-4 0-1-1-5-2-6 1 1 2 2 3 2v-1l-3-7z" class="T"></path><path d="M867 275l4-3-1 9c-1-1-1-3-1-4h0l-1 1c0-2 0-2-1-3z" class="K"></path><path d="M864 299h1l1-2 1 1c-1 2-2 5-3 8h-3l3-7z" class="g"></path><path d="M861 306h3c-2 3-4 6-7 9l-1-1-3 3v-1c3-3 6-6 8-10z" class="Q"></path><path d="M853 316v1l3-3 1 1c-4 4-7 7-11 9h-3-4l4-2c4 0 7-3 10-6z" class="G"></path><path d="M839 324h4 3c-7 4-14 7-21 10 1-3 7-4 9-6 1-2 4-3 5-4z" class="U"></path><path d="M867 275c1 1 1 1 1 3l1-1h0c0 1 0 3 1 4-1 4-1 9-2 13 0 1-1 3-1 4l-1-1-1 2h-1c1-5 3-9 3-14 0-2 1-4 0-5l-1-3h-1s0 1-1 1-2-1-3-1c2 0 4-1 6-2z" class="X"></path><path d="M824 341h2v1c-5 4-17 5-24 5h-2l9-3c2 0 4-2 7-2 2 0 5-1 8-1z" class="B"></path><path d="M797 314c1 1 1 3 2 4h1c2 3 5 7 6 11v11c-1 1-2 1-3 2v1h-1s1-1 1-2c2-9-3-19-7-27h1z" class="O"></path><path d="M834 328c-2 2-8 3-9 6-2 2-6 3-8 4-5 2-9 4-14 5v-1c1-1 2-1 3-2l4-3 24-9z" class="B"></path><defs><linearGradient id="FY" x1="837.471" y1="290.114" x2="851.357" y2="317.052" xlink:href="#B"><stop offset="0" stop-color="#989592"></stop><stop offset="1" stop-color="#c7c7c1"></stop></linearGradient></defs><path fill="url(#FY)" d="M861 277c1 0 2 1 3 1s1-1 1-1h1l1 3c1 1 0 3 0 5 0 5-2 9-3 14l-3 7c-2 4-5 7-8 10s-6 6-10 6l-4 2c-1 1-4 2-5 4l-24 9c1-1 2-2 4-2 1 0 2-1 3-1 1-1 1-1 2-1 1-1 1 0 2-1h1 1l2-1c0-1-1-2-2-3-2 1-5 3-7 3l-1-1 13-7c2-1 3-2 5-3 3-2 5-5 7-7h1c0-1 1-2 1-3 1-1 2-2 2-3 1-1 1-2 2-3 1 0 1-1 2-2l1-3c2 0 2-6 4-7 2-3 3-9 5-13l3-2z"></path><path d="M843 322c4-4 8-9 12-14v1 1h0 0l3-3c2-3 4-6 5-10 0-1 0-2 1-3 0 0 0-1 1-2v-1-1c2-1 0-2 1-3 0-1 1-2 1-2 0 5-2 9-3 14l-3 7c-2 4-5 7-8 10s-6 6-10 6z" class="f"></path><defs><linearGradient id="FZ" x1="813.355" y1="290.074" x2="827.733" y2="330.042" xlink:href="#B"><stop offset="0" stop-color="#95948f"></stop><stop offset="1" stop-color="#deddd7"></stop></linearGradient></defs><path fill="url(#FZ)" d="M827 287h0c3 1 5 1 8-1l1-1c1 2 1 3 1 4 0 3-1 9 0 11s4 3 6 4h1 2c-1 1-1 2-2 3 0 1-1 2-2 3 0 1-1 2-1 3h-1c-2 2-4 5-7 7-2 1-3 2-5 3l-13 7 1 1c2 0 5-2 7-3 1 1 2 2 2 3l-2 1h-1-1c-1 1-1 0-2 1-1 0-1 0-2 1-1 0-2 1-3 1-2 0-3 1-4 2l-4 3v-11c-1-4-4-8-6-11l-2-4h2c0 1 0 2 1 3 1-2 2-2 3-3l1-2c1-1 1-1 2-3h-1c1-1 1-1 1-2s1-3 0-5c0-1-2-2-3-3-1 0-2-1-3-1v-1c-1-2 0-3 1-5 1-1 2-1 3-2h1c3 0 6 0 9-1 1-1 1 0 2 0 3 0 6-1 9-2h1z"></path><path d="M817 311c1 1 1 2 2 3 0 1-2 4-2 5-1 1-4 4-5 4 1-1 2-2 2-3 0-2 1-4 2-5h0c0-2 0-3 1-4z" class="d"></path><path d="M817 311c-1-2 0-5 0-7h1v3h1v-2l1-1v4 2 1l1-1 2 2c-2 2-4 5-6 7 0-1 2-4 2-5-1-1-1-2-2-3z" class="E"></path><defs><linearGradient id="Fa" x1="836.517" y1="292.461" x2="826.554" y2="301.308" xlink:href="#B"><stop offset="0" stop-color="#828382"></stop><stop offset="1" stop-color="#b5b3ab"></stop></linearGradient></defs><path fill="url(#Fa)" d="M827 287h0c3 1 5 1 8-1l1-1c1 2 1 3 1 4 0 3-1 9 0 11s4 3 6 4h1v2l-3-2h-1c-2-1-3-1-5-1-1 0-1 1-1 1 0 2-1 3-2 4h-1l1-2-1-2c-2 0-2 1-3 2v-3-1l-3 3v-3l1-1c1-1 0-3 0-5 1-1 1-1 1-2-1-2-1-4-1-6h1l-1-1h1z"></path><path d="M827 288c1 0 3 0 4 1h1v1l1 2c-1 3-3 6-4 9l-1 1-3 3v-3l1-1c1-1 0-3 0-5 1-1 1-1 1-2-1-2-1-4-1-6h1z" class="J"></path><path d="M832 290l1 2c-1 3-3 6-4 9h-1c-1-2 0-3 0-4 1-3 2-5 4-7zm-6-3l1 1h-1c0 2 0 4 1 6 0 1 0 1-1 2 0 2 1 4 0 5l-1 1v3 4c-1 1-1 2-2 3l-2-2-1 1v-1-2-4l-1 1v2h-1v-3h-1c0 2-1 5 0 7-1 1-1 2-1 4h0c0-4 0-8-1-12 0-2 1-5 0-6 0-1-1-2-1-3v-2c-1 0-4-1-5 0-2 0-4 3-6 4 1 1 3 2 4 4 1 1 1 2 1 2l1 1c0 2 0 2-1 4h-1c0-1 1-3 0-5 0-1-2-2-3-3-1 0-2-1-3-1v-1c-1-2 0-3 1-5 1-1 2-1 3-2h1c3 0 6 0 9-1 1-1 1 0 2 0 3 0 6-1 9-2z" class="L"></path><path d="M826 287l1 1h-1c-1 1-2 2-3 2-2 0-2 0-3 1 0 4 1 7 1 11-2-2-2-8-2-11h-3c-2-1-6 0-8 0h-1l-1 1h-2c-1 1-2 1-2 3v2l-1 1v-1c-1-2 0-3 1-5 1-1 2-1 3-2h1c3 0 6 0 9-1 1-1 1 0 2 0 3 0 6-1 9-2z" class="G"></path><defs><linearGradient id="Fb" x1="820.354" y1="291.165" x2="825.07" y2="309.586" xlink:href="#B"><stop offset="0" stop-color="#8b8a87"></stop><stop offset="1" stop-color="#a8aaa1"></stop></linearGradient></defs><path fill="url(#Fb)" d="M821 302c0-4-1-7-1-11 1-1 1-1 3-1 1 0 2-1 3-2 0 2 0 4 1 6 0 1 0 1-1 2 0 2 1 4 0 5l-1 1v3 4c-1 1-1 2-2 3l-2-2-1 1v-1-2h1v-6z"></path><defs><linearGradient id="Fc" x1="792.476" y1="305.628" x2="833.122" y2="274.182" xlink:href="#B"><stop offset="0" stop-color="#adaca7"></stop><stop offset="1" stop-color="#d7d6d0"></stop></linearGradient></defs><path fill="url(#Fc)" d="M823 267c0-3-6-7-8-9v-1l8 6c0 1 2 2 2 3 1 1-1 4 0 5 2 0 3-1 5-3 1 0 3-2 3-2l-4 13c-1 2-2 5-2 8h-1c-3 1-6 2-9 2-1 0-1-1-2 0-3 1-6 1-9 1h-1c-1 1-2 1-3 2-1 2-2 3-1 5v1c1 0 2 1 3 1 1 1 3 2 3 3 1 2 0 4 0 5s0 1-1 2h1c-1 2-1 2-2 3l-1 2c-1 1-2 1-3 3-1-1-1-2-1-3h-2l2 4h-1c-1-1-1-3-2-4h-1c-2-3-6-7-9-8-3 0-5 1-7 2-5 4-7 9-7 15-1 4 0 8 1 12h0v1c-5-5-8-10-12-15h3l2-4c1-7 3-13 6-20l1-3h-1l6-9c1 0 3-2 3-3l16-15c1 0 0 0 1-1l6-6h0c1-1 2-1 2-2 2 1 3 3 4 4l1 1h1c1 1 1 2 2 3 1 2 3 3 4 4l1 1 1-1v-1l-2-1h1c1 0 1 0 2 1h0c1-1 1-1 1-2z"></path><path d="M823 267c0-3-6-7-8-9v-1l8 6c0 1 2 2 2 3 1 1-1 4 0 5v1c-5 3-11 3-16 5-10 3-17 10-19 20-1 3-1 4 1 6l6 9 1 2 2 4h-1c-1-1-1-3-2-4-1-2-2-3-3-5-2-3-5-5-6-9l1-7c0-1 0-1-1-2 1 0 1-1 1-2 0-2-1-3-2-5l-1-1h1c2 1 2 1 3 2l2 4c3-5 7-9 13-12 2-3 7-5 11-5l3-1c-3-3-5-5-7-8h1c1 1 1 2 2 3 1 2 3 3 4 4l1 1 1-1v-1l-2-1h1c1 0 1 0 2 1h0c1-1 1-1 1-2z" class="B"></path><path d="M819 271l1 1c-1 2-8 3-11 4l-4 1c2-3 7-5 11-5l3-1z" class="e"></path><defs><linearGradient id="Fd" x1="812.109" y1="278.992" x2="788.657" y2="270.508" xlink:href="#B"><stop offset="0" stop-color="#636363"></stop><stop offset="1" stop-color="#91918e"></stop></linearGradient></defs><path fill="url(#Fd)" d="M807 258c2 1 3 3 4 4l1 1c2 3 4 5 7 8l-3 1c-4 0-9 2-11 5-6 3-10 7-13 12l-2-4c-1-1-1-1-3-2h-1l1 1-3 1-1-2c-3 4-6 7-9 11h-1l6-9c1 0 3-2 3-3l16-15c1 0 0 0 1-1l6-6h0c1-1 2-1 2-2z"></path><path d="M808 263l1-1 1 1c1 0 0 0 1-1l1 1c2 3 4 5 7 8l-3 1-2-2c-2 0-5 0-7 1-2 0-3 2-5 2 3-2 6-4 8-7v-1l-2-2z" class="K"></path><path d="M807 258c2 1 3 3 4 4-1 1 0 1-1 1l-1-1-1 1-6 4v-1c1-1 4-3 5-5-4 0-8 6-10 9-5 4-9 7-13 12l-1 1c-3 4-6 7-9 11h-1l6-9c1 0 3-2 3-3l16-15c1 0 0 0 1-1l6-6h0c1-1 2-1 2-2z" class="U"></path><defs><linearGradient id="Fe" x1="784.655" y1="318.084" x2="768.243" y2="302.604" xlink:href="#B"><stop offset="0" stop-color="#a3a3a2"></stop><stop offset="1" stop-color="#c6c5c0"></stop></linearGradient></defs><path fill="url(#Fe)" d="M783 283l1 2 3-1c1 2 2 3 2 5 0 1 0 2-1 2 1 1 1 1 1 2l-1 7c1 4 4 6 6 9 1 2 2 3 3 5h-1c-2-3-6-7-9-8-3 0-5 1-7 2-5 4-7 9-7 15-1 4 0 8 1 12h0v1c-5-5-8-10-12-15h3l2-4c1-7 3-13 6-20l1-3c3-4 6-7 9-11z"></path><path d="M762 321h3l2-4v8c1 4 4 7 7 10v1c-5-5-8-10-12-15z" class="F"></path><path d="M783 283l1 2 3-1c1 2 2 3 2 5 0 1 0 2-1 2 1 1 1 1 1 2l-1 7-1-3v-2-1l-1-1-4 2c-2 1-2 1-4 0l-1-1-1 1c-1 1-2 1-3 2l1-3c3-4 6-7 9-11z" class="f"></path><path d="M778 295c1-1 2-2 4-3h0v1 2c-2 1-2 1-4 0z" class="T"></path><path d="M782 292c1-1 2-1 4 0v1h0l-4 2v-2-1h0z" class="R"></path><path d="M829 203h0c5-1 12-1 17-1h1c1 2 2 3 3 4h0v1l4 6 2 2c0-1 1-1 1-1 1-1 2-1 3-2h1v1h1c4 9 7 19 8 28 0 4 1 9 0 13 0 3-1 8-1 11v6c-2 0-4 2-6 3s-4 2-6 4l1 1c-2 4-3 10-5 13-2 1-2 7-4 7l-1 3c-1 1-1 2-2 2h-2-1c-2-1-5-2-6-4s0-8 0-11c0-1 0-2-1-4l-1 1c-3 2-5 2-8 1h0c0-3 1-6 2-8l4-13c7-8 7-15 6-24 0-6-1-12-3-17l-1 2c-1-2-2-5-2-8-1-1-1-2-2-4 0-1-1-5-2-6 1 1 2 2 3 2v-1l-3-7z" class="V"></path><path d="M849 299c3-8 3-14 8-21l1 1c-2 4-3 10-5 13-2 1-2 7-4 7z" class="B"></path><path d="M829 209c1 1 2 2 3 2 2 4 4 10 4 14l-1 2c-1-2-2-5-2-8-1-1-1-2-2-4 0-1-1-5-2-6z" class="F"></path><path d="M829 203h0c5-1 12-1 17-1h1c1 2 2 3 3 4h0v1h0c-4 1-8 1-12 1-2 0-4 0-5-1-1 1-1 2-1 3l-3-7z" class="d"></path><defs><linearGradient id="Ff" x1="851.023" y1="239.444" x2="871.807" y2="243.25" xlink:href="#B"><stop offset="0" stop-color="#aaa9a3"></stop><stop offset="1" stop-color="#d6d5d2"></stop></linearGradient></defs><path fill="url(#Ff)" d="M854 213l2 2c0-1 1-1 1-1 1-1 2-1 3-2h1v1h1c4 9 7 19 8 28 0 4 1 9 0 13 0 3-1 8-1 11v6c-2 0-4 2-6 3 4-21 0-43-9-61z"></path><path d="M234 183h219c2 3 4 6 6 8l-1 2c-1 3-2 4-3 7v1c-3 0-6-1-9 0-5 0-10 1-14 2-14 4-27 15-34 28-4 8-5 18-3 27 1 4 3 9 4 13l7 20 28 80 24 69 26 79 26 75 14 39 75-260 19-61 9-32c4-10 7-20 7-30 0-12-5-23-14-31-8-8-19-15-30-17-6-1-11-1-16-1-1-4-2-7-4-10 3-2 5-5 6-8 6-1 13-1 18-1h42l143 1c2 3 5 6 7 9-2 3-3 6-4 9v1c-24-2-44 4-63 19-9 7-17 18-21 29-5 11-6 24-10 35l-21 70-86 288-41 141-15 47c-3 9-6 19-8 29-1 0-8 0-8-1-1 0-6-16-7-18l-15-48-48-147-37-110-58-172-27-82c-4-12-7-25-13-36-4-7-8-14-14-20-12-11-27-21-43-24-5-1-10-1-15-1-1-3-4-6-5-9l7-9z" class="h"></path><path d="M567 580c1 0 1 0 2 1 0 3 0 4-1 7h-1v-1l1-3c1-1 0-2-1-4z" class="Z"></path><path d="M385 377l1 1c-1 5-2 8-1 13 1 2 1 3 2 4l-1 1-2-2v-4c0-4 0-9 1-13z" class="C"></path><path d="M391 354c1 0 1 1 1 2 0 2 1 5 2 7h7c0-1 0-1 1-2 0 1 1 1 1 2-1 1-4 1-5 1h-7-1c0 1-1 1-1 1h-1-1l1-1c1 0 3-1 4-1v-3l-1-6zm276-148h1c1-1 2 0 3 1l2 1-1 1-1 1h-3-1c-1 0-1 0-2-1v-1c1-1 1-2 2-2zm-12 1c2-1 3-1 4 0s-1 2-1 4l-2 3h-1c-1-1-2-1-2-2l-1-1c-1-1-1-1-1-2 2-1 2-2 4-2z" class="b"></path><path d="M384 205c1 0 2-1 3 0 0 0-1 0-1 1h-5-18-52-3 0l76-1z" class="S"></path><path d="M515 782l9-26v1l-4 17c-1 3-3 5-3 8v1c-1 1-1 1 0 2 0 2-1 4-2 6-1 4-1 8-2 12v2h0l-1-3c-1 1-1 2-1 4h-1c1-5 4-11 4-15 0-3 1-6 2-9h-1z" class="b"></path><path d="M277 195h1c2 0 4 1 6 1 6 1 13 0 19 0h35 49 19 2c1 1 2 0 4 0h0l-3 2-3 2h-1l3-2v-1h-85c-13 0-26 1-38 0-2 0-4 0-5-1l-3-1z" class="E"></path><path d="M510 806h-1c1 3 2 6 2 9v1 1l2-2c0-1 0-2 1-3v-2l1-1c0-1 0-3 1-4v-2l3-10 1-4 1-3 3-8c0-1 1-3 1-4l1-4 1-2 1-3 1-3c0-1 1-3 1-4l1-3c1-3 2-7 3-11 1 1 1 1 1 2l-8 25-1 4-1 2-4 15c-1 1-1 2-1 4l-1 2-1 3-6 19-1-1-6-20v-3l-4-13-4-10c-1-2-1-2 0-3h0l2 6 6 20 1 1c1 2 1 5 2 8 0-4 0-8 2-11h0l5-12h1c-1 3-2 6-2 9 0 4-3 10-4 15z" class="S"></path><path d="M525 802v-1c0-1 1-2 2-3v-2-1l1-2 1-3c0-1 0-1 1-2v-1c0-1 0-1 1-2v-1h1c0 1 0 1-1 2v2l1-2v-1l1-2v-2c1 0 1-1 1-2s0 0 1-1l-1-1h1v-1c0-1 0 0 1-1v-3c1-1 1-2 1-3l1-2v-2l1-1c0-2 0-2 1-3l-1-1 1-1h0l1-1-1-1 1-1c0-1 0-2 1-3v-2c0-1 0-1 1-2h0v-1c0-1 1-2 1-2l1-1h0v2 1c0 1-1 2-1 3l-9 29-7 24-1 3-1 4-1 4-3 7v1l-1 4c-1-1-1-1-1-2h0c0-1 0-1-1-1 1-1 1-4 1-6 1-4 2-9 3-12 1-2 2-3 2-4z" class="Z"></path><defs><linearGradient id="Fg" x1="512.651" y1="824.118" x2="520.936" y2="827.959" xlink:href="#B"><stop offset="0" stop-color="#cdcdc5"></stop><stop offset="1" stop-color="#f7f6f3"></stop></linearGradient></defs><path fill="url(#Fg)" d="M515 819l23-69h0l-6 25-2 5v2l-1 2v3c-1 2-2 5-3 8s-2 6-2 9c0-1 1-2 1-2 0 1-1 2-2 4-1 3-2 8-3 12 0 2 0 5-1 6 1 0 1 0 1 1h0c0 1 0 1 1 2 0 1-1 2-1 3l-1 2c0 1 0 3-1 4h0c-1 3-3 6-4 9h0-1l-1 1-1-1 1-1v-1c-1 0-1-1-1-2v-1-1l2 1c1-2 1-9 2-11 0-4 2-7 2-10v-1l-2 1z"></path><path d="M511 839c-1-6-2-11-5-15 0-2-1-3-2-5v-2c-1-2-1-3-1-4h0c0-1 0-2-1-3v-1c-1-2-1-4-2-6-2-5-4-12-5-18l-7-24h0l11 32c3 3 4 9 5 13l6 15c1 0 1-1 1-2l1 1c0 1 0 3 1 4l2-5 2-1v1c0 3-2 6-2 10-1 2-1 9-2 11l-2-1z" class="S"></path><path d="M499 793c3 3 4 9 5 13l6 15c1 0 1-1 1-2l1 1c0 1 0 3 1 4-1 1-1 2-2 3l-12-34z" class="Z"></path></svg>