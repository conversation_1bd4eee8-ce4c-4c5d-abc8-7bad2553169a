<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="76 104 356 572"><!--oldViewBox="0 0 488 752"--><style>.B{fill:#2d2c2c}.C{fill:#1f1f1f}.D{fill:#3b3a3a}.E{fill:#111010}.F{fill:#494949}.G{fill:#6d6c6d}.H{fill:#656464}.I{fill:#585758}.J{fill:#bbbabb}.K{fill:#282728}.L{fill:#434343}.M{fill:#5e5e5d}.N{fill:#4d4d4d}.O{fill:#171717}.P{fill:#dbd9da}.Q{fill:#d8d7d7}.R{fill:#323132}.S{fill:#747374}.T{fill:#d5d3d4}.U{fill:#181717}.V{fill:#9a9899}.W{fill:#b4b3b3}.X{fill:#c2c1c2}.Y{fill:#cccbcb}.Z{fill:#878686}.a{fill:#929091}.b{fill:#a8a7a8}.c{fill:#a1a0a0}.d{fill:#383738}.e{fill:#525152}.f{fill:#afaeae}.g{fill:#7f7e7e}.h{fill:#403f3f}.i{fill:#343434}.j{fill:#0e0e0e}.k{fill:#7a7979}.l{fill:#8b8a8b}.m{fill:#deddde}.n{fill:#080808}.o{fill:#f4f2f3}</style><path d="M106 272h3v1l-1 1h-3v-1l1-1z" class="g"></path><path d="M279 435h0l1-1 1 1c1 1 1 2 1 3h0-3c1-1 0-1 0-3z" class="H"></path><path d="M210 136v-3c-1 0-2 0-3-1v-1c1 0 2-1 3-1 1 2 1 5 0 6h0z" class="N"></path><path d="M148 464c1 1 2 2 3 1 1 1 1 2 0 3-1-1-3-1-4-1 0-2 0-2 1-3z" class="B"></path><path d="M276 467l2 1v1l-6 2-1-1v-1l5-2z" class="F"></path><path d="M284 398h2c1 0 1 0 1 2v1h-1c-1 0-1 0-2-1s0-1 0-2z" class="h"></path><path d="M143 195c1 0 2 1 3 1 0-1 0-1 1-2 0 2 0 2 2 3v1l-1 2-5-5z" class="B"></path><path d="M154 594l1 1v1 5c-1 2-1 3-1 5-1-3-1-6-1-8h1v-4z" class="d"></path><path d="M155 596l1-1h1l-1 11v-1h0-1v-4-5z" class="K"></path><path d="M262 471l1 1-8 2c0-1 0-1-1-2l-1 1v-1l2-1 2 1c2 0 3 0 5-1z" class="B"></path><path d="M119 414h0v-1c1-1 2 0 2 0l1-1c1 0 1 1 1 1 0 2-1 3-2 3h-2c-1 1-1 1-2 1h0l2-2v-1z" class="R"></path><path d="M288 424c-1 1-1 2-1 3h-1c-1-2-2-3-3-4h0v-2h1c1 0 1 1 1 1l1 1h1l1 1z" class="I"></path><path d="M262 471c3-2 6-1 9-1l1 1-9 1-1-1h0z" class="d"></path><path d="M154 594l1-6h2v7h-1l-1 1v-1l-1-1z" class="B"></path><path d="M210 136h0c1 1 1 1 1 2s-1 2 1 2h-1v-2h1v8c0 1 0 1-1 1h-1v-2-9z" class="I"></path><path d="M257 51h0c0 1-3 2-4 3-2 1-4 3-5 5h-1 0v-2c-2-1-2-1-3-3l5 2 8-5z" class="j"></path><path d="M263 148l10-1c-2 1-2 1-3 2h0c-2 0-2 0-3 1h-1-2v-2h-1zM141 263c2-1 3-1 5-1v-1c1 1 2 1 3 1h0l1 1-2-1v1 2h-3-2-1l-1-2z" class="Y"></path><path d="M156 431c-2-1-3-1-4-2 0-2-1-2 0-3h1l-1 1h2 3v1l-1 1v2h0z" class="M"></path><path d="M361 387h0v22c-1-3 0-6-1-8h-1c0-1 0-2-1-3v-2h1c1 0 1 0 2-1-1-3 0-5 0-8z" class="I"></path><path d="M275 289l-1-1h0v-2-1l1-1c2 1 4 2 5 3-1 0-1 1-1 2h-1-2-1z" class="P"></path><path d="M288 331c1 1 1 1 1 2h0c0 1-1 1-1 2l1 1h0l2-1c1 0 1 0 2 1 0 1 1 1 1 2l-1-1-2 2h0-3v-4l-1-1h0s1 0 1-1v-2z" class="Y"></path><path d="M100 329v1h0 2c1 1 5-2 7-3v1c-2 2-4 4-7 6h0-1c0-1 1-1 0-2 0-1-2-1-3-1 1-1 1 0 2-2z" class="M"></path><path d="M418 194v12c0-1 0-3-1-3 0-2-3-1-4-3 1-1 2-2 4-3v-1l1-2z" class="G"></path><path d="M338 661c3 1 7 3 9 5-3-1-8-1-11-3h0v-2h2z" class="C"></path><path d="M151 373h1 0l1 4-1 1h-1c-3 1-8 0-12 0 1 0 2 0 3-1 2-1 5 0 7 0 1 0 2 0 3-1v-3h-1z" class="P"></path><path d="M109 327h0c1 0 2-1 3-2 2-2 6-6 9-6h0c0 1-1 2-2 3-3 1-7 2-8 5v1h-2v-1z" class="g"></path><path d="M212 624h-1c-1-1-2 0-4-1 2-3 0-3 0-5h1l2 1 2 1v2 2z" class="o"></path><path d="M202 100h1v3l2 3c1 2 3 3 4 4l1 2h0c0 1 1 2 1 2-1 0-1 0-1-1h-1-1-1c-1-5-5-8-5-13z" class="K"></path><path d="M357 324h1s1 0 1-1h0c0-1-1-2 0-3 0-1 1-2 1-2 1-1 1-2 1-2v7 4h-1c-1-1-2-1-3-2v-1h0z" class="k"></path><path d="M255 567v1h2v2c1 0 2 1 2 2s0 1-1 2c-1 0-1 0-2-1-1 1-1 1-1 2v-8z" class="T"></path><path d="M284 421c3 1 4 1 6 0 4-2 6-4 10-6h0c0 1-1 2-2 2-3 2-7 5-10 7l-1-1h-1l-1-1s0-1-1-1z" class="H"></path><path d="M144 330c0 2 0 2 1 2l-1 1c1 1 1 2 2 3l-2 2h-6-1c1-1 3-2 5-2 1-2 2-3 2-6z" class="a"></path><path d="M359 279c1-2 1-4 2-6v13c-1 1-1 2-1 3v-2s-1 0-2 1l1-9z" class="O"></path><path d="M151 480c0 1 1 2 2 3-1 1-2 1-4 2h0c-1 1-2 2-3 4v1l2 1-2-1-1-1h0v-1c0 1 0 0-1 0 2-3 4-5 7-8z" class="C"></path><path d="M84 344c1 0 1 0 2 1 0 2-1 4-2 6h-1c-1 0-1-1-2-2v-2c1-1 1-2 3-3z" class="N"></path><path d="M102 334c0 2-1 2-2 3h0c0-1 0-2-1-2-1-2-3-3-4-5h0c1 0 1 1 2 1 0-1 1-1 1-2h2c-1 2-1 1-2 2 1 0 3 0 3 1 1 1 0 1 0 2h1z" class="C"></path><path d="M259 499c2 1 5 2 6 5 1 1 1 2 1 3-1-1-2 0-3 0l-1-2c1-1 1-1 0-2-1 0-2-1-4-1h0l1-3z" class="h"></path><path d="M261 507l1-2 1 2c1 0 2-1 3 0 1 3 0 4-1 7-2-2-2-4-4-7z" class="E"></path><path d="M255 553l1 1 3-1v-1c1-1 2-1 2-1h0c1 0 1 0 2 1v1 1c3 0 6 0 8 1-2 1-8 0-11 0h-5v-2z" class="o"></path><path d="M279 296c2 3 6 8 6 11h-1c0 1 0 1-1 2h0v-2c0-1-1-2-1-2-1-1-1-2-2-3v-2l-2-2v-1c1 0 1-1 1-1z" class="l"></path><path d="M137 338h1 6l2-2 2 2c-1 0-2 1-3 2 0 1 0 1-1 2-2-2-5-2-8-3l1-1z" class="D"></path><path d="M152 372c1-2-1-8 0-11l1 1v2 1h1c0 2-1 2-1 3l2 2c0 1-1 2-1 3h-2 0-1-15c1-1 13-1 15-1h1z" class="m"></path><path d="M415 190c0-1 1-1 1-1 0-1 0-1 1-1l1 1v4 1l-1 2v1l-2-2c1-1 0-2 0-3l-2 2-2-2c2-1 2 0 3-1l1-1z" class="K"></path><path d="M415 190c0-1 1-1 1-1 0-1 0-1 1-1l1 1v4-4c-1 1-3 2-3 3l-2 2-2-2c2-1 2 0 3-1l1-1zM213 624h1c1 1 1 1 1 2l-1 1v1l-1 1v2s1 0 1 1-1 4-1 6v2 6h-1v-20c0 1 0 2 1 2v-1-3z" class="g"></path><path d="M276 256h1c2-1 3-3 5-1 1 1 1 3 2 4-1 0-1 1-1 2l-2-1h0-1c-1-1-1-1-1-2h-1-1-1v-2z" class="S"></path><path d="M129 408c1 0 1 1 1 1l2 3c-2 0-7 0-9 1 0 0 0-1-1-1l1-3h3 2l1-1z" class="E"></path><path d="M213 640h2v3h2v1h-1c-1 1-1 2-2 2v11c-1 1-1 1-2 1l-1-1v-1l1 1 1-1v-4c-1-2 0-3-1-5v-1h1v-6z" class="Q"></path><path d="M261 249v-8c0-1 0-2 1-3 0-1 0-2 1-3 0-3-1-6 0-9 0 3 1 5 1 8v3 1c-1 2-1 5-1 8v3h-1-1z" class="P"></path><path d="M210 610c1 0 2 0 2 1v1 5 3l-2-1-2-1c1-1 2-1 3-1h0l-3-3c-1 0-1 0-1-1 1-1 1-1 1-2 1 0 1-1 2-1h0z" class="T"></path><path d="M102 408h3c1 1 1 1 2 0v2l1 1c-1 0-1 1-2 2l-2 2c-1 2-1 5-2 6 1-4 1-9 0-13z" class="B"></path><path d="M105 408c1 1 1 1 2 0v2l1 1c-1 0-1 1-2 2-1 0-1-1-1-1h-1v-4h1z" class="C"></path><path d="M147 429h1c1 1 0 3 0 4-2 0-5-1-8-2-1 0-2-1-3-2h10z" class="N"></path><path d="M118 270h1c2 1 3 2 4 3 0 1-1 2-2 3s-1 1-2 1l-2-2c0-1-1-3 0-4 0-1 1-1 1-1z" class="S"></path><path d="M213 622c1 0 2-2 3-3v2 7c0 1 0 7-1 8h-1l-1 2c0-2 1-5 1-6s-1-1-1-1v-2l1-1v-1l1-1c0-1 0-1-1-2h-1v-1-1z" class="b"></path><path d="M260 635l2 2 1-1c-1 3-1 9-3 11h0c0-2 2-5 1-7l-2 1s-1-1-1 0h-3 0l5-6z" class="J"></path><defs><linearGradient id="A" x1="157.355" y1="610.63" x2="152.189" y2="608.432" xlink:href="#B"><stop offset="0" stop-color="#201e19"></stop><stop offset="1" stop-color="#2b2c37"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M154 606c0-2 0-3 1-5v4h1 0v1c0 5 1 11 0 15 0-1 0-4-1-5v1 3c-1-5-1-10-1-14z"></path><path d="M116 147h9 6c2 0 3 1 5 1h2c-2 1-3 1-4 2-1 0-3 0-4-1l2-2h-1-4v2c-2 1-6 0-8 0l-3-2z" class="Q"></path><path d="M262 395c1 1 1 1 3 1h0c-1 3-1 6-1 9 0 2 0 3-1 5v1 1h0c0-1 0-1-1-2v-3-7s0-1 1-1l-1-2v-1-1z" class="L"></path><path d="M360 327h1v18l-2-2v-4l1-1v-1c0-1-2-2-3-3v-3c1-1 2-2 3-4z" class="G"></path><path d="M262 414h1 1v1 15c0 2 0 5-1 7h0c0-2 0-4-1-6v-17z" class="h"></path><path d="M262 414h1 1v1c-1 1-1 6-1 8h0v-1 1c0-2 0-6-1-8v-1z" class="C"></path><path d="M282 438h0c2 1 5 0 7 0 2 1 3 2 5 2l-8 4c0-1 0-1-1-2s-1-1-3-1v-1-2h0 0z" class="g"></path><path d="M138 243h6v2c-1 1-2 2-4 2h0c0 1-1 2-2 3h0c-1-1-1-3-2-4s0-1 0-2 1-1 2-1z" class="e"></path><path d="M136 246c-1-1 0-1 0-2s1-1 2-1c1 1 1 1 1 2v1h-3z" class="L"></path><path d="M260 583c1 2 3 6 5 7h2c2-1 3 0 4 0l1 1h-7 0v1h-4v-1l-3-2h2c-2-1-2-1-3-2v-1l2 1h1v-4z" class="i"></path><path d="M260 589h3l1 1-2 1c1 1 2 1 3 0v1h-4v-1l-3-2h2z" class="C"></path><path d="M287 237l1-1 1 1c-1 1-1 2-1 2-1 2-4 3-6 4-3 2-6 7-9 8h-2v-1c4-2 6-5 10-8 1-1 3-2 4-4 1 0 1-1 2-1z" class="g"></path><path d="M271 590c0-1 0-2 1-3s2-1 3-2c1 0 1 1 2 1h0c0-1 2-2 3-3l9-8h0l-12 12c1 1 0 2 0 3l-1 1h-4l-1-1z" class="E"></path><path d="M119 149c2 0 6 1 8 0v-2h4 1l-2 2c1 1 3 1 4 1-1 0-2 0-3 1s-3 1-4 1-1 1-1 1c-2 0-3 0-4-1v-1c-1-1-2-2-3-2z" class="T"></path><path d="M249 245c1-1 1-1 3-1l1-1h1c0-1 0-2 1-3h0v12h0-3s0 1-1 2v-2h0l-1-1v-2-1c1-1-1-2-1-3z" class="K"></path><defs><linearGradient id="C" x1="142.021" y1="422.757" x2="132.262" y2="421.106" xlink:href="#B"><stop offset="0" stop-color="#676566"></stop><stop offset="1" stop-color="#7f7d7f"></stop></linearGradient></defs><path fill="url(#C)" d="M130 423h-7c2-1 6-1 8-1h0c0-2-3 0-5-2h16v1h0c1 1 3 1 5 1l-1 1h-10-6z"></path><path d="M272 591h4c-1 0-2 1-3 1 1 0 1 0 1 1h5 8 4c-1 1-3 1-4 1h-9-9-1-1v-1c-1-1-1-1-2-1v-1h0 7z" class="Q"></path><path d="M99 404v5h0c-1-1-2-1-4-1-3 0-7-1-11-1l-8-1c3 0 6-1 9-1 1 0 1 1 2 1h10 1v-1c-4 0-7 1-11 0l12-1z" class="E"></path><path d="M255 641h0 3c0-1 1 0 1 0-1 1-3 3-4 5 0 1-1 3-2 4-1 0-1 0-2-1-1 0-1 0-1-1h0c-1-1 0-3 0-4 1-1 3-2 5-3z" class="k"></path><path d="M250 644l1 1v-1h1 1v1h1c0 1 0 1-1 2v1h-2-1 0c-1-1 0-3 0-4z" class="l"></path><path d="M218 656h1c1 2 1 3 1 4v1s1 1 1 2v10 8c-1-2 0-5 0-8-1 0-1 0-1-1-2-1-3-5-3-7h1c1-1 0-6 0-9z" class="m"></path><path d="M288 629c-1-1-1-1-1-2l2-1c1-2 0-2 0-4l1-1 8 8c-2 1-3 1-5 1s-4-1-5-1z" class="N"></path><path d="M251 254c1-1 1-2 1-2h3 0 0 2c1 0 2 0 3 1h1 0v2 2c-1 0-1 1-2 1h0-4-2v-1h0c0-1-1-2-2-3z" class="o"></path><path d="M251 254c1-1 1-2 1-2h3 0 0v6h-2v-1h0c0-1-1-2-2-3z" class="R"></path><path d="M108 372c-2-1-5-2-7-2-6-1-13-1-19-1-2 0-5 0-6-1h24 5s0 1 1 1c0 1 2 1 3 1l-1 2z" class="K"></path><path d="M223 34l1-1 1 1h0c2 2 3 13 3 16l-2 3-1-1c0-1 0-1-1-2h0v-1-4c0-3 1-8 0-10 0-1 0-1-1-1z" class="B"></path><path d="M210 121l1 1h1c1 0 2-1 3 0v2 1 2 2c-1 1-2 2-3 2h0v4 1 2h-1v2h1c-2 0-1-1-1-2s0-1-1-2c1-1 1-4 0-6 0-3 1-7 0-9z" class="L"></path><path d="M212 122c1 0 2-1 3 0v2 1 2 2c-1 1-2 2-3 2h0v-9z" class="V"></path><path d="M115 408c1 0 1-1 1-1h2 0 4 1c1 0 2 1 3 1v1h-3l-1 3-1 1s-1-1-2 0v1h0l-1-1h-3l-1-1c0-1-1-1-2-1 1-1 1-1 1-2 1-1 1-1 2-1h0z" class="R"></path><path d="M122 410l1-1-1 3-1 1s-1-1-2 0v1h0l-1-1h-3l-1-1c1 0 1 0 2-1h0l-1-1h1 1v1h0 4l1-1z" class="D"></path><path d="M115 408c1 0 1-1 1-1h2 0 4 1c1 0 2 1 3 1v1h-3l-1 1c-2-1-3-1-4-1s-2 0-3-1h0z" class="M"></path><path d="M215 636c1-1 1-7 1-8v6h1v5h0 1v17c0 3 1 8 0 9h-1v-8-13-1h-2v-3h-2v-2l1-2h1z" class="G"></path><path d="M213 638l1-2h1c0 2 1 4 1 6l1 1h-2v-3h-2v-2z" class="J"></path><path d="M255 549v4 2h5c2 1 6 0 8 0 2 1 5 1 7 1v3c-1 0-1 1-2 1 0-1 0-2-1-2l-1-1h-16v4-3l-2-1-1-1 1-1c1 0 1-1 2-1v-2h-1l-2-1c1-1 2-1 3-2z" class="B"></path><path d="M100 368c2-1 3-1 5-2 1 0 2-1 3-1 1-1 4 0 6 0v2h-1c-1-1-3-1-4-1h0c-1 0-2 0-3 1 2 1 7 0 7 1 1 0 3 1 3 1-1 1-1 1-1 2v1h-1c-2 1-4 1-6 0l1-2c-1 0-3 0-3-1-1 0-1-1-1-1h-5z" class="e"></path><path d="M109 370c1 0 4 1 5 2h0c-2 1-4 1-6 0l1-2z" class="O"></path><defs><linearGradient id="D" x1="357.279" y1="298.818" x2="358.88" y2="310.77" xlink:href="#B"><stop offset="0" stop-color="#3c3c3d"></stop><stop offset="1" stop-color="#575757"></stop></linearGradient></defs><path fill="url(#D)" d="M360 289c0-1 0-2 1-3v25 1c-2-1-3-2-5-3-2-2 0-4 1-6 0-1 1-3 1-4l2-2c1-1 0-7 0-8z"></path><path d="M232 140v-1c-1-1 0-3-1-4v-2c1-1 0-1 0-1l1-1v-1c0-1-1-3-1-4s1-1 2-2v1c1 2 3 2 5 5-1 0-1 1-1 1-1 0-3 0-4-1h0v4c0 1 1 2 2 4 0 1-1 1 0 2v1s-1 0 0 1h0c0 1 0 3 1 4l-1 1h0 0c-1-1-2-2-2-3s0-1-1-1v-3z" class="C"></path><path d="M285 323c1 0 1 0 2-1l1 1v1h1 1 2v-1c1 0 1 0 1 1 1 1 3 0 5 0h0c1 0 1 0 2-1 3 1 8 0 11 1-6 1-14 1-19 3-1 1-3 1-3 2-1 1-1 1-1 2h-1c0-1 0-2 1-3l-2-1v-1-1l-1-2z" class="D"></path><path d="M286 325c1 0 1 0 2 1v2l-2-1v-1-1z" class="E"></path><path d="M281 306l2 1v2h0c1-1 1-1 1-2h1c0 2 1 5 3 7-1 2-1 3 0 4 0 1 0 1 1 1 0 1 0 1 1 2 2 2 7 2 10 2-1 1-1 1-2 1h0c-2 0-4 1-5 0 0-1 0-1-1-1v1h-2-1-1v-1-1c0-1-1-1-1-2l-1-1h0c-2-2-2-3-3-5v-1-1h-1l-2-1c1 0 1 0 2-1 0-1 0-2-1-4z" class="H"></path><path d="M283 312v-1h1l3 8v1l-1-1h0c-2-2-2-3-3-5v-1-1z" class="W"></path><path d="M155 620v-3-1c1 1 1 4 1 5v4 9 37h0c-1-2-1-6-1-7v-1c1-1 0-2-1-4 2-2 1-34 1-39z" class="n"></path><path d="M191 123c2-2 3-3 6-3 1 1 3 1 4 2 0 1-1 2-2 3l-1 1c0 2 1 3 0 4-1 0-3 1-4 1v-4l-1-1h-2c-1 0-1-1-1-2l1-1z" class="G"></path><path d="M199 124c-1 1-1 1-2 1s-2 0-3-1h0c1-1 2-1 3-1 0 0 1 1 2 1z" class="Z"></path><path d="M191 123c2 1 3 2 5 3h0 0v1h-2l-1-1h-2c-1 0-1-1-1-2l1-1z" class="c"></path><path d="M197 120c1 1 3 1 4 2 0 1-1 2-2 3v-1c-1 0-2-1-2-1h-2v-1l2-2z" class="F"></path><path d="M361 312v-1 5s0 1-1 2c0 0-1 1-1 2-1 1 0 2 0 3h0c0 1-1 1-1 1h-1l-3-3-3-3c1-1 1-2 2-3 1 0 1-1 1-1 2-1 3-1 5-1h1l1-1z" class="H"></path><path d="M351 318c1-1 1-2 2-3 1 0 1-1 1-1 2-1 3-1 5-1l-3 1c-1 1 1 5 0 6-1 0-2 0-2 1l-3-3z" class="e"></path><path d="M151 341h2l1 1v10c0 4 0 9-1 13v-1-2l-1-1c-1 3 1 9 0 11 0-3 0-7-1-11h-3 3c0-2-1-4 0-5h2v-1l-2-1v1h-1v-9-3c1-1 1-1 1-2z" class="T"></path><path d="M151 341h2l1 1-1 11h-1v-3h-1c-1 1 0 3 0 4h0v1h-1v-9-3c1-1 1-1 1-2z" class="P"></path><path d="M352 336c0-1 0-1 1-2 0-1 0-1 1-2s2-3 2-4l-2-1h0l2-2h1 0c1 1 2 1 3 2-1 2-2 3-3 4v3c1 1 3 2 3 3v1l-1 1v4l-4-4-3-3z" class="M"></path><path d="M357 325c1 1 2 1 3 2-1 2-2 3-3 4v-6h0z" class="d"></path><path d="M101 394l2 1v1c-1 4-1 8-1 12h0c1 4 1 9 0 13h0c-1-1-2-2-2-4v-2c0-1 0-2-1-3v-3h0v-5-2c0-1 0-3 1-4l1-4z" class="o"></path><path d="M99 402h1c0 1-1 3 0 4 0 1 1 2 1 3s0 5-1 6c0-1 0-2-1-3v-3h0v-5-2z" class="T"></path><path d="M263 249v-3c0-3 0-6 1-8v7 3l1 1h1v1l-1 1c0 2 1 3 2 4h1-1v5l-1 1c1 1 1 0 2 1-1 1-1 2-2 3h-1 0c1-1 1-3 1-4-1-1-2 0-3-1v-1h-3l-1-1c1 0 1-1 2-1v-2-2-4h1 1z" class="f"></path><path d="M261 249h1 1 1v2l-1 1c-1 0-1 1-1 2l1-1c1 2 0 5 0 6h-3l-1-1c1 0 1-1 2-1v-2-2-4z" class="N"></path><path d="M139 476c-1 0-2 0-3-1v-1c1-1 4-1 6-1l1-2c1-1 3 0 4 0h1c-1 1-1 1-2 1 0 1 0 1-1 1v1h0l1 1c1-1 2-2 3-2h1c-1 1-1 1-2 1v1h1c1 1 2 1 3 0l2 2 1 1v1h0 0l-1-1c-2 0-2 0-3 1h-1c-2 0-4 0-6-1-1-1-1-1-2-1s-2 0-3-1z" class="L"></path><path d="M139 476v-1h4s0 1 1 1l1 1-1 1c-1-1-1-1-2-1s-2 0-3-1z" class="D"></path><path d="M145 477v-1h2c0 1 1 1 2 2 0 0 1 0 2 1h-1c-2 0-4 0-6-1l1-1z" class="E"></path><path d="M263 260c1 1 2 0 3 1 0 1 0 3-1 4h0 1c1-1 1-2 2-3-1-1-1 0-2-1l1-1v-5h1c1 1 1 1 3 1l-1 2c0-1 0-1-1-2 0 2-1 4 0 5l1 1v1l-1 1v1 2 1 2c-1 1-1 1 0 2v1s0 1-1 1c0-1 0-1-1-2h0l-1 1h-2c0-1 0-1-1-2v-3-1-7z" class="a"></path><path d="M269 265v2 1 2c-1 1-1 1 0 2v1s0 1-1 1c0-1 0-1-1-2h0c-1 0-1-1-1-1v-1c1 1 1 1 2 1 0-2-2-2-2-4h1 2v-2z" class="k"></path><path d="M264 430v6c1 1 2 0 3 1v4c0 1 1 1 2 1l1 1v1 5-1l-1 1c0-1-1-2-1-3v1l-1 1c-1-1-2-1-2-2v-2c0-1 0-1-1-2v-3h-1v4h0-1l-1 1c-1 0-1 0-2-1v-2s-1-1-1-2h-1v-1l1-1 4-1v-5c1 2 1 4 1 6h0c1-2 1-5 1-7z" class="c"></path><path d="M269 442l1 1v1 5-1l-1 1c0-1-1-2-1-3s0-1-1-1h-1v-1c1-1 2-1 3-2z" class="d"></path><path d="M269 596l2 3 3 4 3 4c1 0 1 1 2 2h-1 0c-2 0-6-3-7-4s-1-1-2-1h-1-2v-1s0-1-1-1h-1c-1 0-1-1-1-1-1 0-1 1-2 1v-1h1c0-1 0-2 1-2v-2h2 3l1-1z" class="H"></path><path d="M264 600c1 1 1 1 2 0h1c1 1 3 1 4 1v2h2 1l3 4h-1c-1 0-1 0-2-1 0-1-1-1-2-2-2-1-3-2-4-2s-1 0-1-1h-2 0l-1-1z" class="I"></path><path d="M269 596l2 3 3 4h-1-2v-2c-1 0-3 0-4-1h-1c-1 1-1 1-2 0h0l-1-1v-2h2 3l1-1z" class="N"></path><path d="M269 596l2 3c0 1-1 1-1 1-1 0-1 0-2-1v-1l1-1h-1l1-1z" class="d"></path><path d="M265 597h3 1l-1 1v-1l-1 1s-1 1-1 2h-2l-1-1v-2h2z" class="F"></path><path d="M149 262l1-1c1 1 2 0 4 0v1h3c1 0 2 1 2 2h-1c-1 1-1 2-1 3-1-1-1-1-1-2h-2 0 1v1l-1 1-1-1v1c-1 0-2 1-3 1 0-1-1-1-1-1h-2v1 1l-1-1c-1 0-6 0-7 1h-3v-1h0c1 0 5-2 5-3v-2l1 2h1 2 3v-2-1l2 1-1-1z" class="R"></path><path d="M149 267v-1h1c1-1 2 0 3 1-1 0-2 1-3 1 0-1-1-1-1-1z" class="E"></path><path d="M149 262l1-1c1 1 2 0 4 0v1h3c1 0 2 1 2 2h-1c-1 1-1 2-1 3-1-1-1-1-1-2h-2 0c-2 0-8 1-9 0h3v-2-1l2 1-1-1z" class="b"></path><path d="M154 262h1c1 1 1 2 1 3h-2-1c0-1 0-2 1-3z" class="J"></path><path d="M154 262h3c1 0 2 1 2 2h-1c-1 1-1 2-1 3-1-1-1-1-1-2s0-2-1-3h-1 0z" class="a"></path><path d="M270 443c1-2 1-7 0-8 0-2 0-3 1-3h1v1c-1 2-1 4 0 5h0c1-1 2-2 2-4h3 1l1 1c0 2 1 2 0 3h0v2h-2v2h-1v1h0c-1 0-1 1-1 1v2c-1 0-2 0-3 1 0 1 0 1-1 2h-1v-5-1z" class="f"></path><path d="M277 434h1l1 1c0 2 1 2 0 3h0v2h-2v2h-1c0-1 1-3 1-5 0 0-1-1-1-2l1-1z" class="D"></path><path d="M270 444c2-1 2-1 2-2 1 0 1 0 1 1h0 1 0v-2h1v1l1 1h0c-1 0-1 1-1 1v2c-1 0-2 0-3 1 0 1 0 1-1 2h-1v-5z" class="e"></path><path d="M207 113h1 1 1c0 1 0 1 1 1 1 1 2 1 3 1h4v2l-1 1h1c1 1 1 2 1 3h-1v1h0c-1 1-1 3-1 4h-1c0-2 0-3-1-4s-2 0-3 0h-1l-1-1-1-1c-1 0-2 0-2-1-1-1 0-5 0-6z" class="M"></path><path d="M209 120v-1h1c2 1 4 1 6 1h2v1 1h0c-1 1-1 3-1 4h-1c0-2 0-3-1-4s-2 0-3 0h-1l-1-1-1-1z" class="C"></path><path d="M271 555l2-1c1-2 1-5 3-6 1 0 2 1 2 2s1 4 1 4c1 0 2 1 3 2h0c-1 1-4 2-4 3-1 1-3 4-4 4s-3-1-3-3v-3l1 1c1 0 1 1 1 2 1 0 1-1 2-1v-3c-2 0-5 0-7-1-2 0-6 1-8 0 3 0 9 1 11 0z" class="F"></path><path d="M154 417c1 1 1 2 2 2h0v2 1 1h0l1 1v2 1h-3-2l1-1s1 0 1-1h0 0c-2-1-3-1-5-1h-11-5l-3-1h6 10l1-1c-2 0-4 0-5-1h0v-1h8c1 0 3 0 4-1v-2z" class="D"></path><path d="M142 421h7 0c1 1 1 1 2 1v1h-5l1-1c-2 0-4 0-5-1h0z" class="I"></path><path d="M154 417c1 1 1 2 2 2h0v2 1 1h0v1c-1 0-2-1-2-2-1-1-4-1-5-1h-7v-1h8c1 0 3 0 4-1v-2z" class="G"></path><path d="M271 298h0l3 3h2c0-1 1-1 2-1v-1h0v-1l2 2v2c1 1 1 2 2 3 0 0 1 1 1 2l-2-1c1 2 1 3 1 4-1 1-1 1-2 1h-2c0-1-1-3 0-4-1 0-1-1-1-1h-1c-1 0-1 1-2 1l-2-2-1-2-1-2c0-1-1-1-2-2h2l1-1z" class="F"></path><path d="M271 303l2-1h1v1c-1 1-1 2-2 2l-1-2z" class="e"></path><path d="M274 303h1 0c0 1 1 2 2 2h2c0 1 0 1-1 2-1 0-1-1-1-1h-1c-1 0-1 1-2 1l-2-2c1 0 1-1 2-2z" class="B"></path><path d="M279 305v-1-1h1c0 1 1 2 1 3 1 2 1 3 1 4-1 1-1 1-2 1h-2c0-1-1-3 0-4s1-1 1-2z" class="T"></path><path d="M360 368h1v5 3 11h0c0-1 0-2-1-3-1 0-1 0-2 1h-2c1-1 1-2 2-2h1v-1c-2-1-4-2-5-4l-1 1h0v-1h0c0-2 0-2-1-3v-1h0 1c0-1 0-2 1-2l2-1c0-1 1-1 1-1l3-2z" class="F"></path><path d="M360 368v1c0 1-1 2-2 2h-2c0-1 1-1 1-1l3-2z" class="B"></path><path d="M356 371h2c-1 3-4 3-4 6v1l-1 1h0v-1h0c0-2 0-2-1-3v-1h0 1c0-1 0-2 1-2l2-1z" class="E"></path><path d="M361 376c0 2 0 4-1 6l-5-5c0-1 0-1 1-1 1-2 3-3 5-3v3z" class="O"></path><path d="M269 604c1 0 1 0 2 1s5 4 7 4h0 1c1 1 10 11 11 12l-1 1c0 2 1 2 0 4l-2 1c0 1 0 1 1 2h-1c-2-1-2-6-3-8h-2 0l-1-1h0v-1l2-2c-1-1-1-2-2-2-1 1-1 1-2 1-1-1 0-2 0-3h-1l-2-1v-2h-2c0-1 0-1-1-2 0-2-3-2-4-4z" class="G"></path><path d="M283 617v3l-1 1-1-1h0v-1l2-2z" class="F"></path><defs><linearGradient id="E" x1="350.778" y1="290.285" x2="359.718" y2="312.946" xlink:href="#B"><stop offset="0" stop-color="#0f1010"></stop><stop offset="1" stop-color="#474647"></stop></linearGradient></defs><path fill="url(#E)" d="M358 288c1-1 2-1 2-1v2c0 1 1 7 0 8l-2 2c0 1-1 3-1 4-1 2-3 4-1 6 2 1 3 2 5 3l-1 1h-1c-2 0-3 0-5 1 0 0 0 1-1 1-1 1-1 2-2 3l-1-1 1-4c3-8 5-17 7-25z"></path><path d="M152 453h1s1 0 1 1c1 2 0 6 1 7h1v1h-1v1c1 1 1 3 3 3h5-1-2v2l-1 1-1-2-1 1c1 1 1 2 1 3s0 2-1 3h0-1v1l-2 1v1l-2-2s-1-1-1-2v-2h-1c1-1 3 0 5-1v-1c-1-1-2-1-4-1 1-1 1-2 0-3-1 1-2 0-3-1l-1-1c1 0 3 1 4 0 1 0 2 0 3-1 0-1 0-1-1-2-1 0-2 0-3-1h1l1-1-1-2c1 0 1-1 1-1h1l-1-2z" class="E"></path><path d="M147 463c1 0 3 1 4 0 1 0 2 0 3 1l-1 1h-2c-1 1-2 0-3-1l-1-1z" class="f"></path><path d="M155 463c1 1 1 3 3 3h5-1-2v2l-1 1-1-2h0v-1h-3v-1-2z" class="L"></path><path d="M157 468c1 1 1 2 1 3s0 2-1 3h0-1v1l-2 1v1l-2-2s-1-1-1-2v-2h0 3l1 1c1-1 1-2 1-4h1z" class="j"></path><path d="M151 473c2 1 3 1 5 1v1l-2 1v1l-2-2s-1-1-1-2z" class="C"></path><path d="M124 398c2 1 6 0 9 1h14v2h-1v2c-1 1-3 1-4 1h-1c-1 0-1 1-2 1v-1c-2-1-4-1-5-1h-3l-2-2c-1 0-1 0-3 1h-1c-1-1-3-1-4-2 1 0 1 0 2-1v-1h1z" class="X"></path><path d="M146 401h-5c-3 0-7 1-10 1v-1h6s0-1-1-1c-2-1-8-1-10 0-1 0-2-1-2-1h0c3-1 6 0 9 0h14v2h-1z" class="S"></path><path d="M256 595h1l1 1c0 1 0 1-1 1 1 1 1 1 2 1 1-1 2-1 4-1v2c-1 0-1 1-1 2h-1v1c1 0 1-1 2-1 0 0 0 1 1 1h1c1 0 1 1 1 1v1h2 1c1 2 4 2 4 4 1 1 1 1 1 2h2v2l2 1h1c0 1-1 2 0 3 1 0 1 0 2-1 1 0 1 1 2 2l-2 2v1h0c-2 0-2-1-3-2v-2h0c-1-1-1-1 0-1l-1-1-3-3h0c-3-2-5-6-9-6h-4l-1-2c-2-1-3-1-4 0-1 0-3-3-4-4 1-1 1-2 1-3 0 1 1 2 1 4l1-1c1-1 0-2 1-4z" class="a"></path><path d="M278 616v1h2c1 1 1 1 1 2v1h0c-2 0-2-1-3-2v-2z" class="b"></path><path d="M256 595h1l1 1c0 1 0 1-1 1 1 1 1 1 2 1 1-1 2-1 4-1v2c-1 0-1 1-1 2h-1c-1-1-1-2-1-2h-2l-1-1c-1 0-1 1-2 2h-1l1-1c1-1 0-2 1-4z" class="N"></path><path d="M215 122c1 1 1 2 1 4h1c0-1 0-3 1-4v25h-1-3 0v-1c-1-2-1-2-1-4v-1h0c-1 1 0 3-1 4v1-8-2-1-4h0c1 0 2-1 3-2v-2-2-1-2z" class="K"></path><path d="M212 131h0c1 0 2-1 3-2v8c0 2 1 7 0 10h-1 0v-1c-1-2-1-2-1-4v-1h0c-1 1 0 3-1 4v1-8-2-1-4z" class="Z"></path><path d="M156 265c0 1 0 1 1 2v2h0c1 1 1 1 1 2l-1 1-1 1c0 1-1 1-2 2h0c-2 1-4 1-6 2 0-1 0-1-1-1l-1-1s-1-1-1-2c0 0-1 0-1-1v-1c1 0 2-2 3-2v-1-1h2s1 0 1 1c1 0 2-1 3-1v-1l1 1 1-1v-1h-1 0 2z" class="N"></path><path d="M147 268c1 1 2 1 3 2 1-1 2-1 2-2h1l-1 3v1h-1-1s-2 0-2 1c-1 0-1 0-1 1l-1 1s-1-1-1-2c0 0-1 0-1-1v-1c1 0 2-2 3-2v-1z" class="K"></path><path d="M150 272h-3c1-1 3-1 5-1v1h-1-1z" class="U"></path><path d="M152 272h1l2-2c0-1-1-1-1-2h1 0 1v2l-1 1v2h1c0 1-1 1-2 2h0c-2 1-4 1-6 2 0-1 0-1-1-1l-1-1 1-1c0-1 0-1 1-1 0-1 2-1 2-1h1 1 0z" class="M"></path><path d="M152 272h0c-1 2-2 2-4 2h-1c0-1 0-1 1-1 0-1 2-1 2-1h1 1z" class="B"></path><path d="M147 276c2 0 4-1 6-2l1 1c-2 1-4 1-6 2 0-1 0-1-1-1z" class="d"></path><path d="M147 232c-4 0-6-1-9-2l-2-11c0-1 1-3 0-5h0 0c1-1 1-3 0-4 0-1 0-2 1-3h1v1l-1 1v6c0 4 0 8 1 12 0 1 1 1 2 1l9-1h4 3v-1h1l1 1c0 2 0 6-1 8-1-1-1-2-2-3s-4-1-5-1h-3v1z" class="B"></path><path d="M156 227v-1h1l1 1c0 2 0 6-1 8-1-1-1-2-2-3s-4-1-5-1c2 0 5 0 7-1v-2l-1-1c-1 1-2 0-3 0h3z" class="i"></path><path d="M68 157h7v-1c0-1 1-1 1-2v-1h-2l1-1c6-3 14-3 21-4l-1 6c-5 0-8-1-13 3h-1 1c-3 0-7 0-9 1-2 0-4-1-5-1z" class="m"></path><path d="M99 546h1c0-1 1-2 3-3s6-2 8-2l13-4-2 3c-1 2-2 3-2 4l-2 3-19-1z" class="T"></path><path d="M285 325l1 1v1l2 1c-1 1-1 2-1 3h1v2c0 1-1 1-1 1h0l1 1v4h0-2v1h-1-6-2-2-4v-1h1v-1h0l1 1c1 0 1-1 2-2h0c1-2 1-3 2-5h0 0l2-1h3 0c1 0 2 0 3-1v-3l-1-1 1-1z" class="N"></path><path d="M280 334c1 0 1 0 1 1-1 1-2 2-4 3l-1-1 2-2 2-1z" class="K"></path><path d="M279 331h3l-1 2-1-1v1c-1 1-2 1-3 1v-2h0l2-1z" class="D"></path><path d="M285 325l1 1v1l2 1c-1 1-1 2-1 3h1v2c0 1-1 1-1 1h0l1 1v4h0-2-2-1v-1h1v-4-1l-1 1c-1 0-1-1-2-1l1-2h0c1 0 2 0 3-1v-3l-1-1 1-1z" class="R"></path><path d="M282 331h0c1 0 2 0 3 1h-1v1l-1 1c-1 0-1-1-2-1l1-2z" class="B"></path><path d="M284 333h1c1 2 1 4 2 5l1-1v-2 4h0-2-2-1v-1h1v-4-1z" class="c"></path><path d="M113 402c1-2 3-4 5-5s2 0 4 1l1-2h0 1 0v2h-1v1c-1 1-1 1-2 1 1 1 3 1 4 2h1c2-1 2-1 3-1l2 2c-1 1-2 1-2 2v1l-3 1h3v1l-1 1h-2v-1c-1 0-2-1-3-1h-1-4 0-2s0 1-1 1l-1-2v-4h-1 0z" class="k"></path><path d="M126 402c2-1 2-1 3-1l2 2c-1 1-2 1-2 2v1l-3 1c-1-1-2-1-4-1v-2h0v-2h2 1 1z" class="d"></path><path d="M126 402c2 0 2 0 3 1-1 1-5 1-7 1h0v-2h2 1 1z" class="Q"></path><path d="M114 402c1-2 2-3 4-4h1c1 1 2 7 2 8-1 0-2 1-3 1h0-2s0 1-1 1l-1-2v-4z" class="P"></path><path d="M323 651l15 10h-2v2h0l-22-7s-1-1-1-2 1-2 2-3l1 1c2-1 5 0 7-1z" class="n"></path><path d="M323 651l15 10h-2c-3 0-5-2-8-3-4-2-8-3-12-6 2-1 5 0 7-1z" class="D"></path><path d="M153 377h0v1 1h1c0-1 0 0 0 0h1c-1 1-1 1-2 1 0 1 0 1 1 2h-2-6c-5 1-11 0-17 0h-12 0c3-2 5-3 9-3 4-1 9-1 13-1s9 1 12 0h1l1-1z" class="a"></path><path d="M153 377h0v1 1h1c0-1 0 0 0 0h1c-1 1-1 1-2 1 0 1 0 1 1 2h-2-6 5v-2h-1v-1l1-1h1l1-1z" class="S"></path><path d="M117 382h0c3-2 5-3 9-3l1 1h2 3v1h-3-1-3v1h4-12z" class="Z"></path><path d="M101 394l1-1h1c1 1 1 1 1 2 1 2 5 3 6 5 1 1 2 1 3 2h0 1v4l1 2h0c-1 0-1 0-2 1 0 1 0 1-1 2-1-1-2 0-4 0l-1-1v-2c-1 1-1 1-2 0h-3 0c0-4 0-8 1-12v-1l-2-1z" class="I"></path><path d="M113 402h1v4l1 2h0c-1 0-1 0-2 1 0 1 0 1-1 2-1-1-2 0-4 0l-1-1v-2h1v-1s1-1 2-1c0 0 1-1 1-2v-2h2 0z" class="C"></path><path d="M114 406l1 2h0c-1 0-1 0-2 1l-4-1 1-1h4v-1z" class="F"></path><path d="M108 408l1-1v1l4 1c0 1 0 1-1 2-1-1-2 0-4 0l-1-1v-2h1z" class="U"></path><path d="M350 317l1 1 3 3 3 3h0v1h0-1l-2 2h0l2 1c0 1-1 3-2 4s-1 1-1 2c-1 1-1 1-1 2l-1-1h0c-2 0-3 0-4-1-1-2-2-3-3-4l1-2v-2l1-1 3-6 1-2z" class="N"></path><path d="M350 317l1 1 3 3 3 3h0v1c-2-1-3-3-4-3-1-1-1 0-2 0v-1h0l-1-1-1-1 1-2z" class="h"></path><path d="M345 328c2 1 3 3 5 5 0 1 0 1 1 2-2 0-3 0-4-1-1-2-2-3-3-4l1-2z" class="K"></path><path d="M273 271h0c1 1 1 1 2 1 0 1 0 2 1 3s1 2 1 3h0l-1 1h0 0v1h0l2 2c0 1 1 1 0 1 0 1-1 1-1 0h-1s0-1-1-1h-1v2h1 0l-1 1v1 2h0l1 1-1 1c-1-1-2-3-4-4s-4-3-6-4h1c0-1 1-1 2-1v-1c0-1 1-1 1-2 0-2-1-1-2-2v-3l1-1h0c1 1 1 1 1 2 1 0 1-1 1-1 1-1 3-1 4-2z" class="o"></path><path d="M345 309c4-6 7-13 10-20 1-3 1-5 2-7 1-1 2-2 2-3l-1 9c-2 8-4 17-7 25-1 0-1 0-2-1-2 0-3-2-4-3z" class="m"></path><path d="M166 146h0c1-1 1-2 3-3h0 2l2 2v1c1 1 4 0 6 1l3-1v4 3c0 1 1 4 0 5h-8 0-2v-3h0v-4-1h-4-2c0-1 1-2 1-2v-1l-1-1z" class="P"></path><path d="M174 158h2c0-3 0-7 1-9h1v1 3c1 0 1 0 1 1 1-1 1-1 1-2v-2h2v3c0 1 1 4 0 5h-8 0z" class="W"></path><path d="M178 149v1 3c1 0 1 0 1 1l2 1v1c-1 1-2 1-3 1v-8z" class="J"></path><path d="M166 146h0c1-1 1-2 3-3h0 2l2 2v1c1 1 4 0 6 1h0-2l-1 2c-1 2-3 1-3 4 0 0 1 1 2 1 0 1 0 1-1 2 0 1-1 1-2 1v-2-4-1h-4-2c0-1 1-2 1-2v-1l-1-1z" class="Q"></path><path d="M167 147c2 0 3-1 4 0v1 1l1 1h-4-2c0-1 1-2 1-2v-1z" class="P"></path><path d="M167 147c2 0 3-1 4 0v1c-1 0-1 0-2 1-1 0-1-1-1-1h-1v-1z" class="f"></path><path d="M151 480c1-1 2-1 4-1h0 2 3c0 2 0 3 1 4l1-1 1 1-1 3c-1 0-1 1-1 2-1 2-2 5-2 7h0v-3c-1 1-2 2-2 3-1-1-1-2-1-2v-1-2l-1 1h-1v-1h-1c-1 0-1 1-2 2l-1 1c0-1 0-1-1-2h-1l-2-1v-1c1-2 2-3 3-4h0c2-1 3-1 4-2-1-1-2-2-2-3z" class="n"></path><path d="M151 480c1-1 2-1 4-1h0 2l-2 2c0 1-1 1-2 2-1-1-2-2-2-3z" class="O"></path><path d="M148 491l-2-1v-1c1-2 2-3 3-4v3h2c0 1 0 1 1 2 0-1 1-1 1-2h3c-1 1 0 2-2 2h-1c-1 0-1 1-2 2l-1 1c0-1 0-1-1-2h-1z" class="U"></path><path d="M161 483l1-1 1 1-1 3c-1 0-1 1-1 2-1 2-2 5-2 7h0v-3c-1 1-2 2-2 3-1-1-1-2-1-2v-1-2l-1 1h-1v-1c2 0 1-1 2-2 1 1 1 2 2 2 0-2 2-3 2-6 0 0 0-1 1-1z" class="h"></path><path d="M156 492h0c1-1 1-1 2-1l1 1c-1 1-2 2-2 3-1-1-1-2-1-2v-1z" class="e"></path><path d="M293 264h0c3 1 6 0 9 0l11 1c-1 1-3 0-4 0-5 1-10 0-15 1-4 1-8 3-11 6 0 1 0 3-1 4 0 1-1 1-2 2h-2-1 0c0-1 0-2-1-3s-1-2-1-3c-1 0-1 0-2-1 1 0 1 0 1-1v-1h1v-1h0c2-2 3-1 5-2 0-1 0-1 1-1h3 1c2 0 6 0 8-1z" class="d"></path><path d="M275 268c2-2 3-1 5-2 0-1 0-1 1-1v1h5 0c-2 1-3 1-5 1s-4 1-6 1h0z" class="N"></path><path d="M274 269h1v1h1c1 1 0 3 1 3 1 1 1 1 2 1s2 1 2 1l1 1h0c0 1-1 1-2 2h-2-1 0c0-1 0-2-1-3s-1-2-1-3c-1 0-1 0-2-1 1 0 1 0 1-1v-1z" class="F"></path><path d="M414 153c1 0 3 1 4 1 0 1-1 1-1 2 0 2 1 4 0 5h0c1 1 1 2 1 3-1 2-1 5 0 7-1 2 0 4 0 6v4c-3-3-4-5-6-9l-1-5-1-1v-2l-1-1c0-1-1-2-1-3l-1-2h3v-1c2-1 3-2 4-4z" class="P"></path><path d="M410 166h1 2 2v1l-1 1s-1 0-1 1c0 0-1 1 0 2v-1s0-1 1-1h1c1 1 1 2 3 2-1 2 0 4 0 6v4c-3-3-4-5-6-9l-1-5-1-1zm-308-19h6 1 5 2l3 2c1 0 2 1 3 2v1c1 1 2 1 4 1-1 0-1 1-2 2v1 2h-3 0-10l-8-10-1-1z" class="Q"></path><path d="M109 147h5l1 2-1 1c-2-1-4-1-5-3z" class="l"></path><path d="M115 149c2 1 4 3 7 3 1 1 2 1 4 1-1 0-1 1-2 2-4-1-7-4-10-5l1-1z" class="g"></path><path d="M114 147h2l3 2c1 0 2 1 3 2v1c-3 0-5-2-7-3l-1-2z" class="V"></path><path d="M226 122c0-1-1-1-1-1v-1h2 0 3 1 0c-1 1-1 1-1 2l1 1h0c1-1 1-2 2-3 1 1 2 2 2 3v1h-2c-1 1-2 1-2 2s1 3 1 4v1l-1 1s1 0 0 1v2c1 1 0 3 1 4v1 3c1 0 1 0 1 1s1 2 2 3h0 0-4-2-3v-1-5-8-11z" class="j"></path><path d="M232 140v3c1 0 1 0 1 1s1 2 2 3h0 0-4 0l1-1c-1-1-1-2-2-4v-1h1 1v-1z" class="U"></path><path d="M226 122c1 1 0 5 1 7 0 2-1 4 1 5h0c1 3 1 8 0 11l-1 1c1 1 3 0 4 1h0-2-3v-1-5-8-11z" class="C"></path><path d="M291 339l2-2 1 1c1 1 6 1 8 1h-4v1l-4 4h1c-1 2-2 2-4 3-1 1-4 3-4 4-1 0-1 0-2 1 0-1-1-2-2-2v-1l-2-1c-1-1-1-1-2-1l-1-1c-1-1-1 0-2 0v-1c-1-1-1-1-1-2h-1v-1s0-1-1-1l2-1h2 2 6 1v-1h2 0 3 0z" class="a"></path><path d="M291 339h0 2v2h-1c-1-1-1-2-1-2z" class="k"></path><path d="M286 339h2 2l-1 1s0 1-1 0h-2v-1z" class="g"></path><path d="M284 342c2 0 7-1 9 1h1c-1 1 0 1-1 1l-2 2s0 1-1 1c-1-1-3-1-4-2v-1h-1c0-1-1-1-1-2z" class="W"></path><path d="M278 344s-1 0-1-1h1s1-1 2-1c1 1 1 1 2 0h2c0 1 1 1 1 2h1v1c1 1 3 1 4 2l-3 3c-1 0-1 0-2-1l-2-1-2-1-3-2v-1z" class="P"></path><path d="M283 348l1-2h1c1 1 1 1 0 2v1l-2-1z" class="m"></path><path d="M278 345c2-1 3-1 4-1 0 1 0 2-1 3l-3-2z" class="T"></path><path d="M278 344s-1 0-1-1h1s1-1 2-1c1 1 1 1 2 0h2c0 1 1 1 1 2h1v1c0-1-1-1-1-1-2 0-6-1-7 0z" class="J"></path><path d="M271 256c1-2 1-2 3-2h1c0 1 1 1 1 2v2h1 1 1c0 1 0 1 1 2h1 0l2 1c0-1 0-2 1-2 2 2 5 4 9 5-2 1-6 1-8 1h-1-3c-1 0-1 0-1 1-2 1-3 0-5 2h0v1h-1v1c0 1 0 1-1 1h0c-1 1-3 1-4 2v-1c-1-1-1-1 0-2v-2-1-2-1l1-1v-1l-1-1c-1-1 0-3 0-5 1 1 1 1 1 2l1-2z" class="G"></path><path d="M274 265l1 2v1h0v1h-1l-1-1h0c0-1 1-2 1-3z" class="M"></path><path d="M273 268l1 1v1c0 1 0 1-1 1h0l-2-1 2-2z" class="D"></path><path d="M270 263h1c0 2 0 3-1 4h-1v-2-1l1-1z" class="L"></path><path d="M269 268h0c1 0 1 1 1 2h1l2 1c-1 1-3 1-4 2v-1c-1-1-1-1 0-2v-2z" class="h"></path><path d="M271 256c1-2 1-2 3-2h1c-1 1-2 1-2 2h-1v4 1c-1 1-2 1-1 2h-1v-1l-1-1c-1-1 0-3 0-5 1 1 1 1 1 2l1-2z" class="I"></path><path d="M274 264l-1-1v-1l1-1c1-2 0-2 0-3l1-1v1c0 1 1 1 1 1v2c-1 0-1 0-1 1v1h4 1 0v-1-1l1-1 2 1c0-1 0-2 1-2 2 2 5 4 9 5-2 1-6 1-8 1h-1-3c-1 0-1 0-1 1-2 1-3 0-5 2v-1l-1-2h-1l1-1z" class="k"></path><path d="M274 264h2c2 0 5 0 8 1h-3c-1 0-1 0-1 1-2 1-3 0-5 2v-1l-1-2h-1l1-1zm10-5c2 2 5 4 9 5-2 1-6 1-8 1l5-1c-2-1-5-1-8-2 0 0-1-1-2-1l1-1 2 1c0-1 0-2 1-2z" class="e"></path><path d="M225 52l1 1c4 3 5 6 6 11v1c-1 1-2 1-3 1l-1-1h-2c-2 0-5 1-6 0h-2c-1 1-3 0-4 0v1l-1-1c1-3 2-8 4-11 2-2 5-1 7-2h1z" class="O"></path><path d="M221 55h2c2 0 3 1 4 2v3c0 1 1 4 1 5h-2c-2 0-5 1-6 0h2c0-2 0-7-1-8v-2z" class="S"></path><path d="M221 55h2c1 1 1 3 1 4 1 2 0 4 1 6h-3c0-2 0-7-1-8v-2z" class="b"></path><path fill="#fffefe" d="M214 65h0c1-2 1-5 2-6 0-2 1-3 2-4s1-1 2-1l1 1v2c1 1 1 6 1 8h-2-2c-1 1-3 0-4 0z"></path><path d="M220 54l1 1v2c1 1 1 6 1 8h-2-2c1-2 0-3 1-4 0-1 0-2 1-2v-5z" class="o"></path><path d="M151 580c2-2 8-1 10-1 3 0 6 1 9 1 1 0 6-1 7 0 0 1-2 1-3 1h-2c-1 1-2 2-3 2-1 2-2 2-4 3-2-1-4-2-6-2-1 0-1 0-1 1l-1 3h-2l-1 6v4h-1c0-2 0-3-1-5v4 8l-1 19v-32l-12 16h0l11-17c-1 1-2 3-3 3l2-3c1-1 4-8 4-10 0-1-1-1-2-1z" class="E"></path><path d="M117 382h12c6 0 12 1 17 0h6c1 0 2 0 3 1v1l-1 1c0 1 1 1 1 1 1 1 1 2 1 4-1-1-1-2-2-2-6-1-13-1-18-1-1 1-2 1-3 1h-5c-1-1-2-1-4-1-2-1-6-2-8-4h-1v1l2 1c-2 0-5-1-7-1l7-2h0z" class="O"></path><path d="M222 1h1l1 23c0 3 0 6 1 8l-1 1-1 1c1 0 1 0 1 1 1 2 0 7 0 10v4 1h0c1 1 1 1 1 2h-1v-1h-2-3-1c-1 0-1-1-1-1l1-11c1-2 1-4 2-6h1v-1h-1v-5l1-13c0-1 1-2 1-3V3 1z" class="J"></path><path d="M222 1h1l1 23c0 3 0 6 1 8l-1 1-1 1h-1c2-8 1-16 0-23V1z" class="E"></path><path d="M219 51l1-1c-1 0-1 0-2-1v-1c1-4 1-9 2-13 0-1 1-1 1-1h1v8c0 1 0 3-1 5h0c1 1 1 1 1 2l-1 1h0 1c0 1 1 1 2 0 1 1 1 1 1 2h-1v-1h-2-3z" class="P"></path><path d="M168 464h2 0c0 1 1 1 2 2v-1 3h0v5 3h0-2l1 2h-1c0-1 0-1-1-1 0 0-1 1-2 1l-1 1c-1 1-2 3-3 4l-1-1-1 1c-1-1-1-2-1-4h-3-2 0v-1l-1-1v-1l2-1v-1h1 0c1-1 1-2 1-3s0-2-1-3l1-1 1 2 1-1v-2h2 1c2 0 3-1 5-2z" class="K"></path><path d="M156 475c0 1 1 1 1 3h-2l-1-1v-1l2-1zm11-3v-1h0c1-1 2-1 3-1l1 1c-1 1-2 1-4 1z" class="O"></path><path d="M158 471c1 0 1 0 2 1 0 1 0 2-1 3h-1l-1-1h0 0c1-1 1-2 1-3z" class="U"></path><path d="M160 479h3c1 1 0 2 0 4l-1-1-1 1c-1-1-1-2-1-4z" class="O"></path><path d="M172 465v3h0v5-2-3c-2 0-5 0-7 1v-2h6l1-1v-1z" class="D"></path><path d="M171 471h1v2 3h0-2l1 2h-1c0-1 0-1-1-1 0 0-1 1-2 1v-2l-1-1v-1c0-1 0-1 1-2 2 0 3 0 4-1z" class="R"></path><path d="M170 476v-1c-1 0-1-1-1-2h2c1 1 0 2 1 3h-2z" class="C"></path><path d="M168 464h2 0c0 1 1 1 2 2l-1 1h-6v2h1v1h-1c-2 0-3 0-5 1v1c-1-1-1-1-2-1 0-1 0-2-1-3l1-1 1 2 1-1v-2h2 1c2 0 3-1 5-2z" class="O"></path><path d="M159 469l1-1v-2h2v1h2v1c-1 0-1 1-1 1h-4z" class="B"></path><path d="M158 467l1 2h4l-1 1h3c-2 0-3 0-5 1v1c-1-1-1-1-2-1 0-1 0-2-1-3l1-1z" class="C"></path><path d="M173 475h1v1h0c-1 1-1 1-2 1h0c1 2 1 2 1 4h-1v1h1v1c-1 1-1 2-1 3v6h1l-1 1h0v1l-1 1v2c-1 0-2 0-2 1h0-1c0-1 0-1-1-2 0-1 0 0-1-1 0 2 0 3-2 4h0c-1-1-3 0-5 0v-3-1h0c0-2 1-5 2-7 0-1 0-2 1-2l1-3c1-1 2-3 3-4l1-1c1 0 2-1 2-1 1 0 1 0 1 1h1l-1-2h2 0l1-1z" class="G"></path><path d="M162 489h1 3 1l-1 6-1-2-1-1c-1-1-2-1-3-1v-1-1h1z" class="X"></path><path d="M161 488l1 1h-1v1 1c1 0 2 0 3 1l1 1 1 2h0c0 2 0 3-2 4h0c-1-1-3 0-5 0v-3-1h0c0-2 1-5 2-7z" class="J"></path><path d="M161 491c1 0 2 0 3 1-1 0-1 1-2 1-1-1-1-1-1-2z" class="Q"></path><path d="M159 495c0 1 0 2 1 3h1l1-1c1-1 1 0 2-1v-2l1-1 1 2h0c0 2 0 3-2 4h0c-1-1-3 0-5 0v-3-1h0z" class="c"></path><path d="M173 475h1v1h0c-1 1-1 1-2 1h0c1 2 1 2 1 4h-1v1h1v1c-1 1-1 2-1 3v6h1l-1 1h0v1l-1 1h-1l1-4c0-1 0-2 1-3l-1-1-2 1c-1 0-5 1-6 0h0c-1-1-1-1-1-2l1-3c1-1 2-3 3-4l1-1c1 0 2-1 2-1 1 0 1 0 1 1h1l-1-2h2 0l1-1z" class="S"></path><path d="M173 475h1v1h0c-1 1-1 1-2 1h0c1 2 1 2 1 4h-1c-1 0-1 0-2 1h-1v1c0 1 1 1 1 2h0-2v1-3s0-1 1-1l-1-2-2-1 1-1c1 0 2-1 2-1 1 0 1 0 1 1h1l-1-2h2 0l1-1z" class="Z"></path><path d="M166 479l2 1 1 2c-1 0-1 1-1 1v3s0 1-1 2h-4c-1-1-1-1-1-2l1-3c1-1 2-3 3-4z" class="P"></path><path d="M279 438h3 0v2 1c2 0 2 0 3 1s1 1 1 2h-1v1c1 0 2 1 2 2s0 3-1 3c0 1-1 2-2 2-1 1-2 2-3 2-1 1-2 4-3 4h-2c-1 0-1-2-2-3h-3c0 1 0 1-1 2l2 2h-1c-1 0-1-1-2-1h-1v-3-1c1-1 1-1 1-2h-2v-1l2-1v-1l1-1v1h1c1-1 1-1 1-2 1-1 2-1 3-1v-2s0-1 1-1h0v-1h1v-2h2v-2h0z" class="L"></path><path d="M276 442h1v-2h2c0 2-1 3-1 4-1 1-1 0-1 1s1 1 3 2c0-1 1-1 2-1v1c-2 0-6 1-7-1v-2s0-1 1-1h0v-1z" class="F"></path><path d="M279 438h3 0v2 1c2 0 2 0 3 1s1 1 1 2h-1v1c-2 0-4-1-7-1 0-1 1-2 1-4v-2h0z" class="b"></path><path d="M270 449h1c1-1 1-1 1-2 1-1 2-1 3-1 1 2 5 1 7 1l1-1v1 1h-1-1v1c-1 0-1 1-1 1-2 1-4 1-6 1-1 0-2 1-3 1v2 1c0 1 0 1-1 2l2 2h-1c-1 0-1-1-2-1h-1v-3-1c1-1 1-1 1-2h-2v-1l2-1v-1l1-1v1z" class="B"></path><path d="M281 449v1c1 0 2 0 3 1v1c-1 1-2 2-3 2-1 1-2 4-3 4h-2c-1 0-1-2-2-3h-3v-1-2c1 0 2-1 3-1 2 0 4 0 6-1 0 0 0-1 1-1z" class="K"></path><path d="M277 453h0c1 1 1 1 1 2v1h-2v-1c1 0 1-1 1-2z" class="C"></path><path d="M402 147c5 1 11-1 16 0v2h0c0 2-1 4 0 5-1 0-3-1-4-1-1 2-2 3-4 4v1h-3 0-3-5c-1-1-1-2-2-2l-2-2c0-1-1-2-2-3l-4-4c2 1 5 0 7 1 2-1 4-1 6-1z" class="Q"></path><path d="M396 148c2-1 4-1 6-1v2h2l3 3c0 1-1 2-2 2s-2 0-3-1 0-3-1-4h-1-4v-1z" class="P"></path><path d="M407 158c1 0 1-1 2-1h1c0-1 1-2 1-3-1 0-2 0-3 1l-1-1c1 0 1 0 1-1l1-1c1 0 1-1 2-1h0c2-1 0-2 1-4 1 1 1 2 1 2 0 1 1 1 1 2l3-3 1 1h0c0 2-1 4 0 5-1 0-3-1-4-1-1 2-2 3-4 4v1h-3 0z" class="X"></path><path d="M418 149c0 2-1 4 0 5-1 0-3-1-4-1 1 0 1-1 2-2 0-1 1-1 2-2z" class="Q"></path><path d="M135 278v-3l-2-2v-5h0 3v1h3c1-1 6-1 7-1l1 1c-1 0-2 2-3 2v1c0 1 1 1 1 1 0 1 1 2 1 2l1 1c1 0 1 0 1 1l-4 1-1 1v1h2 1v1h1v1h0-1c-1 0-1 1-1 2v3 1 3c0 1 0 2-1 3v2c0 1 1 2 0 3 0 0 1 0 0 1v1c-4-2-1-10-3-13-2-1-3-2-4-3s-2-1-2-3l1-1c1 0 1 0 2 1v1h-2v1c1 0 2 1 3 1h1l-1-1v-1h2v-1c-1 0-2 0-2-1-1 0-2-1-2-1-1-1-2-1-2-2z" class="S"></path><path d="M144 272c0 1 1 1 1 1 0 1 1 2 1 2l1 1c1 0 1 0 1 1l-4 1h0c0-1-1-2-1-3 0 0 0-2 1-3z" class="C"></path><path d="M133 268h3v1h3 0v2 1h-1v-1h0c-1 0-3 0-3 1-1-1-1-3-2-4z" class="T"></path><path d="M135 278v-2c1 0 1 1 1 1 2 1 3 1 5 1h1c0 1-1 1-1 2h-1-3c-1-1-2-1-2-2z" class="B"></path><path d="M139 269c1-1 6-1 7-1l1 1c-1 0-2 2-3 2s-3 0-3 1 0 1-1 1v-3l-1-1h0z" class="V"></path><path d="M141 269h2 0v1l-2 1h0v-2z" class="f"></path><path d="M220 115c2-1 5 0 6 0s2 0 2 1-1 2-1 3v1h-2v1s1 0 1 1v11 8 5l-1-1c0-1-1-1-2-1 0 2 0 2-1 3h0-1c-1 1-2 1-3 0v-25h0v-1h1 0v1c1 0 1 0 2 1v-3h-1v-4-1z" class="M"></path><path d="M220 115h3c0 1 0 1-1 1h-2v-1z" class="l"></path><path d="M223 115h1 2v1s-1 0-1 1l1 1h-3-1l-1 1v-1l1-2c1 0 1 0 1-1z" class="Z"></path><path d="M223 125v-2h2v4c0 2 0 4 1 6v8 5l-1-1c0-1-1-1-2-1v-6c1-1 0-2 0-4v-9z" class="k"></path><path d="M223 125v-2h2v4c0 2 0 4 1 6v8 5l-1-1v-14c0-1 1-5 0-6h-2z" class="H"></path><defs><linearGradient id="F" x1="217.005" y1="131.255" x2="226.369" y2="134.88" xlink:href="#B"><stop offset="0" stop-color="#130e14"></stop><stop offset="1" stop-color="#1e211c"></stop></linearGradient></defs><path fill="url(#F)" d="M221 120c1 0 2-1 2 0h0v8 10h0v6c0 2 0 2-1 3h0-1c0-3-1-6 0-9v-12-3-3z"></path><path d="M219 121h0v1c1 0 1 0 2 1v3 12c-1 3 0 6 0 9h1-1c-1 1-2 1-3 0v-25h0v-1h1z" class="H"></path><path d="M218 122h1c0 4 0 23 1 25h0 1 1-1c-1 1-2 1-3 0v-25h0z" class="W"></path><path d="M262 455h6v3h1c1 0 1 1 2 1h1 1v3c1 0 1 1 1 2h0-1v-1h-1l-1-1h-1v-1h-2v2c1 0 1 1 1 2h3l1 1c1 0 3 0 4 1s15 0 18 1h3c-7 1-14 0-20 1v-1l-2-1-5 2v1c-3 0-6-1-9 1h0c-2 1-3 1-5 1l2-1h-1c-1-1-1-1-2-1v-1l-1-1v-1c1-1 1-1 0-2 0-1 0-1 1-1-1 0-1 0-2-1h-1c1-1 2-1 3-1 0-2 0-2-1-3v-1c0-1 0-1 1-2h3c1 0 2 1 2 2h1v-3z" class="b"></path><path d="M259 461c1 0 2-1 3-1l-1 1h4c-1 1-2 0-2 2l2 1c1 1 1 1 3 1v1c-1 0-2 0-3-1l-2-1h0v4c0 1-1 1-1 2h0v1h0c-2 1-3 1-5 1l2-1v-1c1 0 1 0 2-1 0-1 0-1 1-1l-1-1c0-1 1-2 0-2v-3c-1 0-1 0-2-1z" class="h"></path><path d="M263 468v1l2-2c3 0 9-1 11 0l-5 2v1c-3 0-6-1-9 1v-1h0c0-1 1-1 1-2z" class="H"></path><path d="M256 464c0-2 0-2 1-3h2c1 1 1 1 2 1v3c1 0 0 1 0 2l1 1c-1 0-1 0-1 1-1 1-1 1-2 1v1h-1c-1-1-1-1-2-1v-1l-1-1v-1c1-1 1-1 0-2 0-1 0-1 1-1h0z" class="V"></path><path d="M257 468c1 0 3 0 4 1-1 1-1 1-2 1v1h-1c-1-1-1-1-2-1 1-1 1-1 1-2z" class="I"></path><path d="M256 464h2c-1 2-1 3-1 4s0 1-1 2v-1l-1-1v-1c1-1 1-1 0-2 0-1 0-1 1-1h0z" class="F"></path><path d="M256 464c0-2 0-2 1-3h2c1 1 1 1 2 1v3h0c-1 0-3 0-3-1h-2z" class="T"></path><path d="M262 455h6v3h1c1 0 1 1 2 1h1 1v3c1 0 1 1 1 2h0-1v-1h-1l-1-1h-1v-1h-2v2 2c-1 0-2 0-3-1l-2-1c0-2 1-1 2-2h-4l1-1c-1 0-2 1-3 1h-2c-1 1-1 1-1 3h0c-1 0-1 0-2-1h-1c1-1 2-1 3-1 0-2 0-2-1-3v-1c0-1 0-1 1-2h3c1 0 2 1 2 2h1v-3z" class="P"></path><path d="M281 620l1 1h0 2c1 2 1 7 3 8h1c1 0 3 1 5 1s3 0 5-1l25 22c-2 1-5 0-7 1l-1-1c-3-2-7-3-9-5-3-2-8-5-11-8h-1c0-1-1-1-2-2l-7-6-3-2c1 0 1 0 1-1 0-2-1-4-2-7z" class="M"></path><path d="M295 634h0 2s1 1 2 1v1c0 1 1 2 2 3 1 2 5 4 5 7h0c-3-2-8-5-11-8h1l-3-4h2z" class="a"></path><path d="M281 620l1 1h0 2c1 2 1 7 3 8h1c1 0 3 1 5 1v3s1 1 2 1h-2l3 4h-1-1c0-1-1-1-2-2l-7-6-3-2c1 0 1 0 1-1 0-2-1-4-2-7z" class="J"></path><path d="M288 629c1 0 3 1 5 1v3s1 1 2 1h-2c-2-1-5-3-5-5z" class="g"></path><path d="M260 148l1-1 2 1h1v2h2 1c1-1 1-1 3-1h0c1-1 1-1 3-2h5c3 0 7 0 9 1h1l6-1v1h-3c-1 1-3 1-4 2-1 3-2 5-4 8h-4c-2 0-4 1-6 0-3 0-6 0-9-1-2-2-6-4-8-4-1-1-2-1-3-2 1-1 1-2 2-3s4 0 5 0z" class="a"></path><path d="M275 151c-1 0-1-1-2-2h1c1 0 2-1 4 0v2h-3z" class="f"></path><path d="M264 150h2 1c1-1 1-1 3-1-2 2-5 4-8 4h0v-1l2-2z" class="J"></path><path d="M278 155v-1h5c0-1 1 0 1 0l-1 3c-1 0-1-1-2-1v1h-1l-2-2z" class="T"></path><path d="M283 150h2v1c0 1-1 2-1 3h0s-1-1-1 0h-5v1l-3-4h3c1 1 1 1 3 1 0-1 1-1 2-2z" class="J"></path><path d="M278 147c3 0 7 0 9 1h1l6-1v1h-3c-1 1-3 1-4 2 0 0 0-1-1-1h-1v2-1h-2c-1 1-2 1-2 2-2 0-2 0-3-1v-2-2z" class="V"></path><path d="M281 152c-1-1-2-1-2-2l-1-1h1c1-1 3 0 4 1-1 1-2 1-2 2z" class="f"></path><path d="M266 153c1-1 2-2 4-2l1-1h1l1 1 4 5c1 0 2 1 2 2-2 0-4 1-6 0v-1c-1 0-2-1-3-1h-2v-2c-1 0-1-1-2-1z" class="X"></path><path d="M266 153c1-1 2-2 4-2l1-1h1l1 1h-2l-1 1v1c1 0 2 1 3 1l1 1h-1-1c-1 1-1 1-2 1h-2v-2c-1 0-1-1-2-1z" class="f"></path><path d="M268 154c1 0 3 0 4 1-1 1-1 1-2 1h-2v-2z" class="Q"></path><path d="M260 148l1-1 2 1h1v2l-2 2v1h0-1-1v1c1 0 2 1 3 0h1l2-1c1 0 1 1 2 1v2h2c1 0 2 1 3 1v1c-3 0-6 0-9-1-2-2-6-4-8-4-1-1-2-1-3-2 1-1 1-2 2-3s4 0 5 0z" class="W"></path><path d="M266 153c1 0 1 1 2 1v2h-1c-1-1-3-1-3-2l2-1z" class="X"></path><path d="M255 148c1-1 4 0 5 0-1 0-2 0-2 1-1 0-1 1-1 2v1l-1 1c-1-1-2-1-3-2 1-1 1-2 2-3z" class="Y"></path><path d="M255 435l1 1h1 0c0-1 0-1 1-2l1 1s0 1-1 1v1l-1 1v1h1c0 1 1 2 1 2v2c1 1 1 1 2 1l1-1h1 0v-4h1v3c1 1 1 1 1 2v2c0 1 1 1 2 2l1-1v-1c0 1 1 2 1 3v1l-2 1v1h2c0 1 0 1-1 2v1h-6v3h-1c0-1-1-2-2-2h-3c-1 1-1 1-1 2h-1c0-1-2-2-3-3-1-2-2-2-3-3 0-1 1-2 1-3 1-1 1-2 0-3v-3l1-1 1-1h0v-4c0-1 2 0 3-1l1-1z" class="F"></path><path d="M255 443l2 1v1c0 1-1 2-1 3-1 0-1-1-2-1v-1l1-3z" class="X"></path><path d="M265 454c-1-2-1-2-2-3h-1l3-2h1v1 2 1l-1 1z" class="K"></path><path d="M266 452l2 2v1h-6-2v-2h1 2c0 1 0 1 1 1h1l1-1v-1z" class="D"></path><path d="M257 439h1c0 1 1 2 1 2v2c1 1 1 1 2 1l1-1h1 0c1 0 1 3 1 4h-1-2c-1-1-2-2-3-2h-1v-1-4-1z" class="S"></path><path d="M256 448l3 1v2h2v1h-1l-2 2c0 1-1 1-2 2s-1 1-1 2h-1c0-1-2-2-3-3 1-1 2-1 2-2v-1c1 0 2 1 3 1v-1c0-1 0-1-1-2l1-2z" class="d"></path><path d="M258 454c-1-1-1-1-1-2h3l-2 2z" class="E"></path><path d="M250 442l1-1v5 1c1 0 2-1 3 0h0c1 0 1 1 2 1h0l-1 2c1 1 1 1 1 2v1c-1 0-2-1-3-1v1c0 1-1 1-2 2-1-2-2-2-3-3 0-1 1-2 1-3 1-1 1-2 0-3v-3l1-1z" class="I"></path><defs><linearGradient id="G" x1="248.105" y1="448.235" x2="251.553" y2="447.001" xlink:href="#B"><stop offset="0" stop-color="#232124"></stop><stop offset="1" stop-color="#3d3e3c"></stop></linearGradient></defs><path fill="url(#G)" d="M250 442l1-1v5c-1 1 0 5 0 7h1l1-1v1c0 1-1 1-2 2-1-2-2-2-3-3 0-1 1-2 1-3 1-1 1-2 0-3v-3l1-1z"></path><path d="M255 435l1 1h1 0c0-1 0-1 1-2l1 1s0 1-1 1v1l-1 1v1 1 4l-2-1-1 3v1h0c-1-1-2 0-3 0v-1-5h0v-4c0-1 2 0 3-1l1-1z" class="b"></path><path d="M254 439l1-1h2v1 1 4l-2-1c0-1 0-2-1-3v-1z" class="m"></path><path d="M254 439l1-1h2v1 1h-2l-1-1z" class="P"></path><path d="M251 441l1 1 1-1c1 1 1 4 1 5v1h0c-1-1-2 0-3 0v-1-5h0z" class="G"></path><path d="M268 304c1-1 1-1 1-2 0 0 0-1 1-1l1 2 1 2 2 2c1 0 1-1 2-1h1s0 1 1 1c-1 1 0 3 0 4h2l2 1h1v1 1c1 2 1 3 3 5h0l1 1c0 1 1 1 1 2v1l-1-1c-1 1-1 1-2 1l1 2v1l-1-1-1 1 1 1v3c-1 1-2 1-3 1h0-3l-2 1h0 0c-1 2-1 3-2 5h0c-1 1-1 2-2 2l-1-1c0-2 1-4 1-5 1-4 1-11 1-14 0-1-1-2-1-3l-1-4c-2-2-2-6-4-8z" class="F"></path><path d="M282 314c-2 0-2 1-3 0s-2-1-2-2l1-1h2l2 1h1v1c-1 0-1 1-1 1z" class="h"></path><path d="M282 314s0-1 1-1v1c1 2 1 3 3 5h0-2l-1 1c0-1-1-2-1-2-1 0-1 0-2-1h-1-1 0c0-1 1-2 2-2h1l1-1z" class="Z"></path><path d="M278 323v-3h1c1 0 1 1 2 1 0 2-2 1-1 3l1 1 1-1v-1-1l2 1h0c-1-1-1-2-1-3h1c1 1 1 2 1 3l1 2v1l-1-1c-2 0-1 0-2 1h-2-1c-1 1-1 0-2 0v-3h0z" class="K"></path><path d="M278 323v3c1 0 1 1 2 0h1 2c1-1 0-1 2-1l-1 1 1 1v3c-1 1-2 1-3 1h0-3l-2 1 1-9z" class="L"></path><path d="M279 331v-1c0-1 1-1 2-1v-2h0 1 1 2v3c-1 1-2 1-3 1h0-3z" class="X"></path><path d="M268 304c1-1 1-1 1-2 0 0 0-1 1-1l1 2 1 2 2 2c1 3 2 6 2 9l2 7h0l-1 9h0 0c-1 2-1 3-2 5h0c-1 1-1 2-2 2l-1-1c0-2 1-4 1-5 1-4 1-11 1-14 0-1-1-2-1-3l-1-4c-2-2-2-6-4-8z" class="P"></path><path d="M276 327l1 5c-1 2-1 3-2 5h0c-1-3 0-6 1-10z" class="c"></path><path d="M276 316l2 7h0l-1 9h0 0l-1-5c-1 0 0-1 0-1 0-2-1-2 0-3-1-1-1-2-1-3l1-1v2-5z" class="Z"></path><defs><linearGradient id="H" x1="269.815" y1="314.88" x2="275.827" y2="308.597" xlink:href="#B"><stop offset="0" stop-color="#868686"></stop><stop offset="1" stop-color="#acaaac"></stop></linearGradient></defs><path fill="url(#H)" d="M268 304c1-1 1-1 1-2 0 0 0-1 1-1l1 2 1 2 2 2c1 3 2 6 2 9v5-2l-1 1c0-2-1-6-2-8h-1c-2-2-2-6-4-8z"></path><path d="M161 443v2c2 1 4 1 6 2l3 2c-1 1-1 1-1 2v1c1 1 0 2 1 3h1 0 0c1 1 1 2 1 3h0v3 4 1c-1-1-2-1-2-2h0-2c-2 1-3 2-5 2h-5c-2 0-2-2-3-3v-1h1v-1h-1c-1-1 0-5-1-7 0-1-1-1-1-1h-1-2v-1h3v-3l-1-1 2-1h0l1-1h2v-1l1-1v1c1 0 1 1 2 1 1-1 1-2 1-3z" class="I"></path><path d="M154 447h0l1-1h2v-1c0 1 0 2 1 2l-1 2s-1 0-1-1c-1 0-1-1-2-1z" class="L"></path><path d="M161 443v2c2 1 4 1 6 2l3 2c-1 1-1 1-1 2v1c1 1 0 2 1 3h-1-2-2 0v-1s1 0 1-1h-1v-1c-1-1-2-3-2-4h-1v1l-1-1h-1c0 1 0 2-1 2h0c0-1-1-2 0-3h2 1s0-1-1-1h-1c1-1 1-2 1-3z" class="d"></path><path d="M167 455c0-1-1-2 0-3h1c0 1 1 2 1 3h-2z" class="C"></path><path d="M169 455h1 1 0 0c1 1 1 2 1 3h0v3 4 1c-1-1-2-1-2-2h0-2c-2 1-3 2-5 2h-5c-2 0-2-2-3-3v-1h1l1-1 1-2c1-1 3-2 5-3l2-1h2 2z" class="F"></path><g class="D"><path d="M163 466c1-2 3-3 4-4l2 2h-1c-2 1-3 2-5 2z"></path><path d="M169 455h1 1 0 0c1 1 1 2 1 3h0v3 4 1c-1-1-2-1-2-2h0-2 1l1-1-1-2c-1-1 0-1 0-2v-1h0c-1 0-1 0-1-1h-2c1-1 1-1 1-2h2z"></path></g><path d="M169 455h1 1v2h0c-1 0-1 0-2 1-1 0-1 0-1-1h-2c1-1 1-1 1-2h2z" class="G"></path><path d="M169 461v-2h1c0 1 1 2 2 2v4 1c-1-1-2-1-2-2h0-2 1l1-1-1-2z" class="K"></path><path d="M167 455c0 1 0 1-1 2l1 2h-1c-1 1-2 1-2 2-1 0-1 1-1 1-1 2-3 3-4 4h-1c-2 0-2-2-3-3v-1h1l1-1 1-2c1-1 3-2 5-3l2-1h2z" class="S"></path><path d="M157 461l2 1-2 2h0l-1-2 1-1z" class="W"></path><path d="M167 455c0 1 0 1-1 2v2h-1v-1c-1 0-1-1-1-1l-1-1 2-1h2z" class="a"></path><path d="M158 459c1-1 3-2 5-3l1 1-3 3-2 2-2-1 1-2z" class="W"></path><path d="M158 459l3 1-2 2-2-1 1-2z" class="c"></path><path d="M212 146v-1c1-1 0-3 1-4h0v1c0 2 0 2 1 4v1h0 3 1c1 1 2 1 3 0h1 0c1-1 1-1 1-3 1 0 2 0 2 1l1 1v1h3 2 4l1-1v1h3c2 0 3 0 4 1 2 0 5 0 8-1h1 1 0c1 1 1 1 2 1-1 1-1 2-2 3l-1-1h-2l1 1 1 2h-9l-2-1v1h-2c-2-1-6 0-8 0h-9 0c-3-1-7 0-10 0h-2v1h-3-2-6 0c2 0 1-1 1-2 1 0 1-1 1-1h2v-1c1-2 1-2 2-3s1-1 1-2l-1-1h2l3 1v2h1c1 0 1 0 1-1z" class="Y"></path><path d="M205 147c1 0 1 1 1 1 0 1-2 2-3 2 1-2 1-2 2-3z" class="Q"></path><path d="M210 147c1 0 2 0 3 1v1c-1 1-1 1-2 1-1-1-1-2-1-3zm-4 4h3c1 1 1 1 1 2h-1c-1-1-2 0-2 0l-1-1v-1z" class="m"></path><path d="M223 144c1 0 2 0 2 1l1 1v1h3c-2 0-5 1-7 0 1-1 1-1 1-3z" class="l"></path><path d="M233 149h0c1 0 4 1 4 2v1h-1-3c-1-1-1-1-1-2l1-1z" class="T"></path><path d="M212 146v-1c1-1 0-3 1-4h0v1c0 2 0 2 1 4v1h0 3c-1 1-2 1-3 1h-1c-1-1-2-1-3-1-2 1-2 0-3 0v-1-2l3 1v2h1c1 0 1 0 1-1z" class="J"></path><path d="M252 147h1 0c1 1 1 1 2 1-1 1-1 2-2 3l-1-1h-2c0 1-1 1-2 1s-2-1-3-1v-1c-1 1-1 1-2 1v-1h0c2 0 4-1 5 0 1 0 4-1 5-1v-1h-1z" class="T"></path><path d="M212 153v-1-1c2 0 2-1 4-1l1 1c1 0 1 0 2-1v1 1c1 0 1-1 2-2v-1l1-1h1v1h2c1-1 3 1 5-1l1 1v1h-1v1l1 1v1h-9 0c-3-1-7 0-10 0z" class="m"></path><path d="M150 231c1 0 4 0 5 1s1 2 2 3v1l1 1v3s1 1 1 2h0v1h4v2c0 1 1 3 0 4h0v2l-1 1 2 1c-1 1-2 2-1 3v3h-1c-1 0-2 1-2 2v2l-1 1c0-1-1-2-2-2h-3v-1c-2 0-3 1-4 0v-2c0-2-1-3 0-4v-1c0-1-1 0-1-1s1-1 1-2v-1c-1-2 0-4 0-6l-1-1v-7l6-3v-1c-3-1-6 0-8 0v-1h3z" class="c"></path><path d="M150 244s1 0 1 1v1c1 1 1 3 1 4h-2c-1-2 0-4 0-6z" class="I"></path><path d="M150 237h2c0 1 0 1-1 2h1c0 2 0 4-1 6 0-1-1-1-1-1l-1-1 1-3h1v-1h-1v-1-1z" class="N"></path><path d="M150 237l1-1c1 0 2 0 3 1 1 0 1 0 2 1 0 1 1 1 1 2l-1 1h-1c0-1 0-1-1-1l-2-1h-1c1-1 1-1 1-2h-2z" class="b"></path><path d="M150 250h2v5h0s1 0 1 1h-1c0 1 0 2 1 2h1c-1 1-2 2-3 2l-1-1v1 1h4c-2 0-3 1-4 0v-2c0-2-1-3 0-4v-1c0-1-1 0-1-1s1-1 1-2v-1z" class="M"></path><path d="M155 244l1-1h2l1-1v1h4v2c0 1 1 3 0 4h0v2l-1 1 2 1c-1 1-2 2-1 3v3h-1c-1 0-2 1-2 2v2l-1 1c0-1-1-2-2-2h-3v-1h-4v-1-1l1 1c1 0 2-1 3-2v-1c1-3 1-7 1-10v-2-1z" class="D"></path><path d="M155 244c2 0 3 0 4 1 1 0 1 1 1 1 0 1-1 1-1 1-1 1-1 1-2 1h0v-2c-1 0-1 0-2-1h0v-1z" class="B"></path><path d="M158 255h0 2v1 1c0 1 1 1 2 2-1 0-2 1-2 2v2l-1 1c0-1-1-2-2-2l1-1 1-1-1-1h0c0-1 0-2-1-2 0-1 0-1 1-2z" class="S"></path><path d="M158 255h0 2v1 1h-1l-1-1v-1z" class="g"></path><path d="M155 247c1 2 0 5 1 8v2 1c-1 1-1 2-1 3h3l-1 1h-3v-1h-4v-1-1l1 1c1 0 2-1 3-2v-1c1-3 1-7 1-10z" class="F"></path><path d="M155 244l1-1h2l1-1v1h4v2c0 1 1 3 0 4h0v2l-1 1 2 1c-1 1-2 2-1 3v3h-1c-1-1-2-1-2-2v-1-1h-2l1-1h-2c1-2 0-3 1-5h0l1 1h1v-4s0-1-1-1c-1-1-2-1-4-1z" class="G"></path><path d="M163 249h-1c0-1 0-2-1-2l1-2h1c0 1 1 3 0 4z" class="g"></path><path d="M162 252l2 1c-1 1-2 2-1 3v3h-1c-1-1-2-1-2-2v-1c1 0 1 0 2-1v-1c-1-1 0-1 0-2z" class="D"></path><path d="M260 281l1-1c1 1 2 1 3 2 2 1 4 3 6 4s3 3 4 4c2 2 4 4 5 6 0 0 0 1-1 1v1 1h0v1c-1 0-2 0-2 1h-2l-3-3h0l-1 1h-2c1 1 2 1 2 2-1 0-1 1-1 1 0 1 0 1-1 2l-2-2-1-1h-1l-1 1c-1-2-1-2-3-2l-4 6v-1h0c0 1-1 1-1 2l-1 1c0-1 0-1-1-1l3-6c1-1 1-2 1-3l1-2-1-1c0-1 1-1 2-2h3 0s0-1 1-1c1-1 1-1 1-2-2-1-2-1-3-3 0 1-1 2-1 3h-1c-1-1-2-2-3-2-2-2-4-3-6-3v-1c1 0 1-1 1-1 1 0 1-1 2-2 2 0 2 0 3 1 2 1 2-1 4-1z" class="M"></path><path d="M266 286c1 0 2 1 2 2l1 1h-3v-3z" class="N"></path><path d="M263 285h2l1 1v3c-1 0-1 0-2 1-2-1-2-1-3-3 1 0 1-1 2-2z" class="B"></path><path d="M260 281c1 1 2 2 3 2s1 0 2 1v1h-2l-1-1h-2v1c-1 0-3 0-4-1v-2c2 1 2-1 4-1z" class="c"></path><path d="M266 289h3v1h-1v1c1 0 2 1 2 1v3l-1 1c-1 1-2 1-3 1-2-1-3-2-4-4 0 0 0-1 1-1 1-1 1-1 1-2 1-1 1-1 2-1z" class="a"></path><path d="M250 285v-1c1 0 1-1 1-1 1 0 1-1 2-2 2 0 2 0 3 1v2c1 1 3 1 4 1v-1h2l1 1c-1 1-1 2-2 2 0 1-1 2-1 3h-1c-1-1-2-2-3-2-2-2-4-3-6-3z" class="W"></path><path d="M259 293h3 0c1 2 2 3 4 4 1 0 2 0 3-1l1-1h0v1 2l1-1v1l-1 1h-2c1 1 2 1 2 2-1 0-1 1-1 1 0 1 0 1-1 2l-2-2-1-1h-1l-1 1c-1-2-1-2-3-2l-4 6v-1h0c0 1-1 1-1 2l-1 1c0-1 0-1-1-1l3-6c1-1 1-2 1-3l1-2-1-1c0-1 1-1 2-2z" class="B"></path><path d="M259 293l1 1v1c0 1-1 1-1 1h-1l-1-1c0-1 1-1 2-2z" class="O"></path><path d="M270 298l1-1v1l-1 1h-2c1 1 2 1 2 2-1 0-1 1-1 1 0 1 0 1-1 2l-2-2c0-1-1-2 0-3h1c1-1 2-1 3-1z" class="S"></path><path d="M259 296h1s0-1 1-1c0 0 1 0 1 1v1h0c1 0 2 0 2 1l-1 1c-1 0-1-2-2-1l-1 2-4 6v-1h0c0 1-1 1-1 2l-1 1c0-1 0-1-1-1l3-6c1-1 1-2 1-3l1-2h1z" class="L"></path><path d="M147 399h5c0-1 1-1 2-1l1 1h2c1 2 0 3 2 4v2 3 6 6h0-2l-1 1v-2h0c-1 0-1-1-2-2v-2c0-1 0-2-1-2l-8-1h-11-2l-2-3s0-1-1-1v-1h-3l3-1v-1c0-1 1-1 2-2h3c1 0 3 0 5 1v1c1 0 1-1 2-1h1c1 0 3 0 4-1v-2h1v-2z" class="H"></path><path d="M131 403h3l1 1c0 1-1 1-1 2h-5v-1c0-1 1-1 2-2z" class="B"></path><path d="M154 403v4 1 1c-2-1-3 0-5 0-1-1-1-1-2 0v-2c1 0 5 0 6-1s1-2 1-3z" class="L"></path><path d="M147 409c1-1 1-1 2 0 2 0 3-1 5 0-1 1-2 1-4 1h-1 0c-1 0-2 0-2 1-2 0-3-1-5-1-1 0-2 1-3 0h-6v1l1 1h-2l-2-3h4c2-1 3 0 5 0s5-1 7 0h1z" class="B"></path><path d="M148 401c1-1 3-1 4-1h1l-2 2 1 1v1c-1 1-2 1-3 2-1 0-1-1-2-1l-1 1h-6c-1 0-1-1-2-1s-2 0-3-1l-1-1c1 0 3 0 5 1v1c1 0 1-1 2-1h1c1 0 3 0 4-1v-2h1 0 1z" class="a"></path><path d="M148 401h3v1c-3 2-4 3-8 2h-1c1 0 3 0 4-1v-2h1 0 1z" class="W"></path><path d="M155 399h2c1 2 0 3 2 4v2 3 6 6h0-2l-1 1v-2h0c-1 0-1-1-2-2v-2c0-1 0-2-1-2l-8-1h-11l-1-1v-1h6c1 1 2 0 3 0 2 0 3 1 5 1 0-1 1-1 2-1h0 1c2 0 3 0 4-1v-1-1-4c0-1 0-2 1-4z" class="C"></path><path d="M155 410l2 1v3c-1 1-2 1-3 1 0-1 0-2-1-2l-8-1h7 2s1 0 1-1v-1z" class="B"></path><path d="M155 399h2v12h0l-2-1c0-1 0-1 1-2l1-1s0-1-1-2h0c-1 1-2 2-2 3v-1-4c0-1 0-2 1-4z" class="i"></path><path d="M157 399c1 2 0 3 2 4v2 3 6 6h0-2l-1 1v-2h0c-1 0-1-1-2-2v-2c1 0 2 0 3-1v-3h0v-12z" class="S"></path><path d="M157 411h0v9l-1 1v-2h0c-1 0-1-1-2-2v-2c1 0 2 0 3-1v-3z" class="h"></path><path d="M257 298c0 1 0 2-1 3l-3 6c1 0 1 0 1 1l1-1c0-1 1-1 1-2h0v1l4-6c2 0 2 0 3 2l1-1h1l1 1 2 2c2 2 2 6 4 8l1 4-1-1v-1h-1 0-2c0-1-1-1-1-2v2h-2c0-1 0-1-1-2h0l-2 1c1 1 0 2 1 2l1 1-9 1-3 1c1 0 3 0 3 1h0c-2 1-3 1-5 2v1h-1l-1-2c0 2 0 3-1 4h-2v-1-2l-2-1-1 1v-4h0-1l1-2 2-1 3-3c1-1 1-2 2-3l1-1c1-3 4-7 6-9z" class="R"></path><path d="M265 303c1 2 2 3 2 5 0 1 0 2-1 2h0c0-2-1-3-2-5 0-1 0-1 1-2z" class="O"></path><path d="M264 301h1l1 1 2 2c2 2 2 6 4 8l1 4-1-1v-1h-1 0-2c0-1-1-1-1-2-1 0-1-1-1-2l1-1-1-1c0-2-1-3-2-5l-1-2z" class="L"></path><path d="M268 309c1 1 2 2 2 4h1v1h-2c0-1-1-1-1-2-1 0-1-1-1-2l1-1zm-8-9c2 0 2 0 3 2l1-1 1 2c-1 1-1 1-1 2h-2c-1 0-2 1-2 2h0c-2 1-2 1-3 2s-1 1-1 2l-1 1h-2c1-2 1-3 2-4 0-1 1-1 1-2l4-6z" class="O"></path><path d="M263 302l1-1 1 2c-1 1-1 1-1 2h-2c1-1 1-2 1-3z" class="C"></path><path d="M256 311l1 1 2-1v1c1-1 0-2 1-2h1v3h1v-2h0v-1h2 1v2l-2 1c1 1 0 2 1 2l1 1-9 1h-1c1 0 1-1 1-1h1v-1h-1c-1 1-2 1-3 1 0-1 1-3 2-4l1-1z" class="D"></path><path d="M257 298c0 1 0 2-1 3l-3 6c1 0 1 0 1 1l1-1c0-1 1-1 1-2h0v1c0 1-1 1-1 2-1 1-1 2-2 4h2c-1 1-2 3-2 4 1 0 2 0 3-1h1v1h-1s0 1-1 1h1l-3 1c1 0 3 0 3 1h0c-2 1-3 1-5 2v1h-1l-1-2c0 2 0 3-1 4h-2v-1-2l-2-1-1 1v-4h0-1l1-2 2-1 3-3c1-1 1-2 2-3l1-1c1-3 4-7 6-9z" class="P"></path><path d="M243 317c2 0 3 1 4 2-2 0-2 0-3 1l-1 1v-4h0z" class="I"></path><path d="M247 319l2 1c0 2 0 3-1 4h-2v-1-2l-2-1c1-1 1-1 3-1z" class="B"></path><path d="M253 307c1 0 1 0 1 1l1-1c0-1 1-1 1-2h0v1c0 1-1 1-1 2-1 1-1 2-2 4h2c-1 1-2 3-2 4 1 0 2 0 3-1h1v1h-1s0 1-1 1h1l-3 1c-1 1-2 1-3 0s-2-2-2-3l5-8z" class="N"></path><path d="M253 316v1c-1 0-1-1-2-2 1-1 1-2 2-3h2c-1 1-2 3-2 4z" class="B"></path><path d="M138 361h10 3c1 4 1 8 1 11h-1c-2 0-14 0-15 1h-1c-2 0-3 1-4 0h-2-2c-1-1-2-1-3-2h-3v1 1c-2 0-4-1-6-1v-1c0-1 0-1 1-2 0 0-2-1-3-1h4c3-1 6-4 8-5 0-1 1-1 2-2h5 0 1 5z" class="M"></path><path d="M127 368h3v1 1s1 0 1 1h-1c0 1 0 1 1 2h-2c0-2 0-3-1-4l-1-1z" class="e"></path><path d="M140 362h0l2 1c0-1 1-1 1-1h3s1 1 2 1l-2 2c-3 1-7 1-10 1h-2l-1-1h5 0c0-1 1-1 1-1 0-1 1-1 1-1v-1z" class="Y"></path><path d="M116 369l1 1c2 1 2-1 3-2 2 0 4 0 5 1l2-1 1 1c1 1 1 2 1 4h-2c-1-1-2-1-3-2h-3v1 1c-2 0-4-1-6-1v-1c0-1 0-1 1-2z" class="i"></path><path d="M125 363c0-1 1-1 2-2h5 0 1 5c0 1 1 1 2 1v1s-1 0-1 1c0 0-1 0-1 1h0-5v-1h-1s0 1-1 2c-1 0-2 0-2-1h-2c-1-1-1-1-2-1h-1l1-1h0z" class="Q"></path><path d="M125 363c1 0 2-1 2-1h4 1c0 1 0 1-1 1s-2 2-4 2c-1-1-1-1-2-1h-1l1-1z" class="P"></path><path d="M138 361h10 3c1 4 1 8 1 11h-1v-6l-4 4h-3-2c-3 1-6 0-8 1h-1v-3l1 1h7 3c1 0 1-1 2-1h0c1 0 3-2 3-3s0-2-1-3h-2-3s-1 0-1 1l-2-1h0c-1 0-2 0-2-1z" class="L"></path><path d="M133 368v3h1c2-1 5 0 8-1h2 3l4-4v6c-2 0-14 0-15 1h-1c-2 0-3 1-4 0s-1-1-1-2h1c1-1 1-2 1-3v-1l1 1h0z" class="B"></path><path d="M285 630l7 6c1 1 2 1 2 2h1c3 3 8 6 11 8 2 2 6 3 9 5-1 1-2 2-2 3s1 2 1 2l-14-5h0c-5-1-10-3-14-6-3-1-7-2-10-4 3 0 4 2 6 2v-2c-1 0-1-1 0-1h0-2c-1 0-2-1-3-1-2-1-1-2-3-3 1 0 3-1 4-2 2-2 4-3 7-4z" class="d"></path><path d="M278 634v2c-1 0-1 1-2 1 0 1 0 1 1 2-2-1-1-2-3-3 1 0 3-1 4-2z" class="B"></path><path d="M286 645c1 0 3 1 4 0h0c1 1 1 1 2 1 1 1 2 1 3 2h1 1 0s1 0 1 1c1 0 1 0 1 1l1 1c-5-1-10-3-14-6z" class="F"></path><path d="M292 636c1 1 2 1 2 2-1 0-1 0-2 1 0 1 0 1 1 1v1c-1 1 0 1 0 2h2l1-1 2 2h0c2 0 3 2 4 3l-2 2c0-1-1-3-2-4h-2-1l1-1h-1-2c-1 0-1-1-1-1v-1c-1-2-4 0-6 0v-1c1-1 2-1 3-1v1c1-1 1-1 2-1h0 0v-2-1l1-1z" class="B"></path><path d="M285 630l7 6-1 1v1 2h0 0c-1 0-1 0-2 1v-1c0-1 0-2-1-2 0 1-1 1-2 1-2-1-4-2-4-4l-1-1c-1 1-1 1-1 3-1-1-1-1-2-1v-2c2-2 4-3 7-4z" class="E"></path><path d="M294 638h1c3 3 8 6 11 8 2 2 6 3 9 5-1 1-2 2-2 3s1 2 1 2l-14-5v-2l2-2c-1-1-2-3-4-3h0l-2-2-1 1h-2c0-1-1-1 0-2v-1c-1 0-1 0-1-1 1-1 1-1 2-1z" class="O"></path><path d="M255 258h4 0l1 1h3v1 7 1 3c1 1 1 1 1 2h2v3c1 1 2 0 2 2 0 1-1 1-1 2v1c-1 0-2 0-2 1h-1c-1-1-2-1-3-2-1 0-2 0-2-1-1 0-2-1-3-1l-2-1h0c1-2 1-3 0-5v-1c-1-1-1-1-2-1-1 1-1 1-2 1h-2c-1-1-2-1-3-1h0l-1-2c-2 0-4 1-5 0s-1-2-1-4h0 0v-3c0-1 0-2 1-3h6 4 4 2z" class="L"></path><path d="M252 263h0v1 2c-1 0-2 0-2 1h0l-1-3c1 0 2 0 3-1z" class="Z"></path><path d="M245 270h2l1-1 1-1c1 0 2 1 4-1v1c1 1 1 2 1 3-1-1-1-1-2-1-1 1-1 1-2 1h-2c-1-1-2-1-3-1z" class="R"></path><path d="M238 264c1 1 1 1 2 1s2 0 3 1h2l1-2h2 0v3c-1 1-2 1-4 1 0-1-2-1-2-1-1 0-2 1-3 1-1-1-1-2-1-4h0 0z" class="g"></path><path d="M254 272h4c1 0 1 1 1 2h0l1 2-1 1c-1 0-2 0-3 1l-2-1h0c1-2 1-3 0-5z" class="P"></path><path d="M263 271c1 1 1 1 1 2h2v3c1 1 2 0 2 2 0 1-1 1-1 2v1c-1 0-2 0-2 1h-1c-1-1-2-1-3-2-1 0-2 0-2-1-1 0-2-1-3-1 1-1 2-1 3-1l1-1-1-2c1 0 2 1 3 1l1-4z" class="m"></path><path d="M263 271c1 1 1 1 1 2v1c0 2 1 4 1 7h-1c-1-1-3-4-4-5h0l-1-2c1 0 2 1 3 1l1-4z" class="G"></path><defs><linearGradient id="I" x1="257.88" y1="265.942" x2="258.673" y2="258.882" xlink:href="#B"><stop offset="0" stop-color="#363435"></stop><stop offset="1" stop-color="#585757"></stop></linearGradient></defs><path fill="url(#I)" d="M255 258h4 0l1 1h3v1 7h-2l-1 1c0 1 1 1 1 2h0-1l-1-1s-1 0-2-1l-1 1c0-1-1-1 0-2h1l2 1h0 0c-1-3-4-1-6-2v-1h1v-2h-1l-1 1v-1-1c0-1 1-2 1-3l-4-1h4 2z"></path><path d="M252 262h2v-1h1l1 1c2 0 2-1 3-1h1v2h1v1c-1 0-3-1-3-1-1 0-1 1-1 1 0 1-2 1-3 1v-2h-1l-1 1v-1-1z" class="S"></path><path d="M255 258h4v1h-1v1s1 0 1 1c-1 0-1 1-3 1l-1-1h-1v1h-2c0-1 1-2 1-3l-4-1h4 2z" class="M"></path><path d="M239 258h6 4l4 1c0 1-1 2-1 3v1h0c-1 1-2 1-3 1v-1l-1 1h0-2l-1 2h-2c-1-1-2-1-3-1s-1 0-2-1v-3c0-1 0-2 1-3z" class="a"></path><path d="M249 264c0-1 0-2 1-3h1l1 1v1c-1 1-2 1-3 1z" class="J"></path><path d="M243 260c1 0 3-1 4 0 0 1-1 1-1 2v1h2v1h-2c-1 0-1 0-1-1h-1c-1 0-1-1-2-2l1-1z" class="X"></path><path d="M238 261c1-1 2-1 3-2 0 1 1 1 2 1l-1 1c1 1 1 2 2 2h1c0 1 0 1 1 1l-1 2h-2c-1-1-2-1-3-1s-1 0-2-1v-3z" class="V"></path><path d="M150 201l2 2h0l3 3v1l3-3v23l-1-1h-1v1h-3-4l-9 1v-2h-1v-2c1-1 0-4 1-5v-2l1-5v-1c0-2 0-3 1-4l1-1c1-5 5-3 7-5z" class="c"></path><path d="M151 218h1l1-1c1 1 2 1 3 1v1c-1 1-3 1-4 2l-1-1c-1-1 0-1 0-2z" class="I"></path><path d="M150 213h1c1 1 0 1 0 2h1 0 1c1 0 2 1 3 2h-3l-1 1h-1 0c0-2 0-4-1-5z" class="H"></path><path d="M149 223c1 0 1 1 2 1h0c1-1 0-1 0-3h1c1 1 3 1 4 0v3c-1 1-1 0 0 2h-6c-1-1-1-2-1-3z" class="M"></path><path d="M144 223h3 2c0 1 0 2 1 3h6v1h-3-4l-9 1v-2h-1v-2c1 0 2 1 3 1 0-1 1-1 1-2h1z" class="V"></path><path d="M139 224c1 0 2 1 3 1 0-1 1-1 1-2h1c0 1 0 2 1 3-2 0-4-1-5 0h-1v-2z" class="Z"></path><path d="M140 217l1-5 2 1c0 1 0 2 1 2h4v1h0l-3 1h0c2 1 3 0 4 1 0 1 1 2 0 3h-1v-2h-1c-1 1-1 1-1 2h-4-1v-2h-1v-2z" class="W"></path><path d="M140 217c1 0 1 0 2 1 0 1 0 1 1 3h-2v-2h-1v-2z" class="P"></path><path d="M152 203l3 3v1l3-3v23l-1-1h-1v1-1c-1-2-1-1 0-2v-3-2-1c-1 0-2 0-3-1h3c-1-1-2-2-3-2h-1 0-1c0-1 1-1 0-2h-1 0c0-1 0-2 1-2 2-3 0-5 1-7v-1z" class="L"></path><path d="M155 213v-2c1-1 1-1 1-2h1c0 2 1 7 0 8l-1 1c-1 0-2 0-3-1h3c-1-1-2-2-3-2 1-1 2-1 3-1h0v-1h-1z" class="B"></path><path d="M152 203l3 3v1h1v2 1c-2 0-3 0-4 2v1h0 3 1v1h0c-1 0-2 0-3 1h-1 0-1c0-1 1-1 0-2h-1 0c0-1 0-2 1-2 2-3 0-5 1-7v-1z" class="I"></path><path d="M150 201l2 2h0v1c-1 2 1 4-1 7-1 0-1 1-1 2h0-7l-2-1v-1c0-2 0-3 1-4l1-1c1-5 5-3 7-5z" class="G"></path><path d="M145 210s0-1 1-1 0-2 1-3v2h2 0c1 1 1 2 0 3-1 0-3 0-4-1z" class="a"></path><path d="M150 201l2 2h0v1s-1 0-1 1 1 2 0 3l-1-1-1 1h0-2v-2c0-1 1-2 2-2l-1-1c-1 0-2 0-3 1l-2 2h0c1-5 5-3 7-5z" class="k"></path><path d="M147 206c0-1 1-2 2-2v4h-2v-2z" class="Z"></path><path d="M143 206h0l2-2c1-1 2-1 3-1l1 1c-1 0-2 1-2 2-1 1 0 3-1 3s-1 1-1 1h-1c0 1 0 1-1 2h0c-1 0-1-1-2-1 0-2 0-3 1-4l1-1z" class="c"></path><path d="M265 312h0c1 1 1 1 1 2h2v-2c0 1 1 1 1 2h2 0 1v1l1 1c0 1 1 2 1 3 0 3 0 10-1 14 0 1-1 3-1 5h0v1h-1-2v1c-1 0-2 0-3-1-1 0-1 0-2-1l-2-2c-1 0-2-2-4-3l-1-1c0-1-1-1-2-2 0-1 0-1-1-1l-1-2v-1c-1-2-1-3-2-5 2-1 3-1 5-2h0c0-1-2-1-3-1l3-1 9-1-1-1c-1 0 0-1-1-2l2-1z" class="D"></path><path d="M268 332h4c-1 1-1 2-1 2h-1 0-1l-1 2h0 1 1 0c1 1 1 1 2 1v1 1h-1-2-2v-3c0-2 1-2 1-4h0z" class="C"></path><path d="M264 332h3 1 0 0c0 2-1 2-1 4v3h2v1c-1 0-2 0-3-1-1 0-1 0-2-1l-2-2c2-1 1 0 3 0-1 0-1-1-2-1h0l1-2v-1h0z" class="R"></path><path d="M274 319c0 3 0 10-1 14v-3h-2c-2 0-4-1-5 0-1 0-1 1-1 2h-1 0v1l-1 2h0c1 0 1 1 2 1-2 0-1-1-3 0-1 0-2-2-4-3l-1-1c0-1-1-1-2-2 2 1 2 1 4 1v-1h-2c2 0 4-1 6-1v-1l1-1v2h2 3v-1l1 1h1c1 0 2 0 2-1l1-9z" class="L"></path><path d="M257 332l1-1c1 0 2 0 4 1 1 1-1 2 1 3h0c1 0 1 1 2 1-2 0-1-1-3 0-1 0-2-2-4-3l-1-1z" class="d"></path><path d="M272 315l1 1c0 1 1 2 1 3l-1 9c0 1-1 1-2 1h-1l-1-1v1h-3v-2h0c-1-1 0-2 0-4l-1-1h2v-3h-1c3-1 3-2 5-3l1-1z" class="C"></path><path d="M269 324v-1l1-1 1 2c0 2 0 2-1 5l-1-1v-4z" class="N"></path><path d="M271 317h1v3h0v1h-2-1-1l2-2c0-1 1-1 1-2z" class="j"></path><path d="M267 322l3-1 1 1v2l-1-2-1 1v1 4 1h-3v-2h0c-1-1 0-2 0-4l-1-1h2z" class="L"></path><path d="M266 327h2 0v-3h1v4 1h-3v-2z" class="E"></path><path d="M256 322h0 2v1h5l2-1 1 1c0 2-1 3 0 4h0v2h-2v-2l-1 1v1c-2 0-4 1-6 1h2v1c-2 0-2 0-4-1 0-1 0-1-1-1l-1-2c0-1 0-1 1-2 0-1-1-1-1-2 1-1 2-1 3-1z" class="N"></path><path d="M256 322h0 2v1h-2c0 1 0 1 1 2l-2 1h1c0 1-1 2-2 3l-1-2c0-1 0-1 1-2 0-1-1-1-1-2 1-1 2-1 3-1z" class="O"></path><path d="M263 323l2-1 1 1c0 2-1 3 0 4h0v2h-2v-2l-1 1v1c-2 0-4 1-6 1v-1c1-1 1-2 1-3h1v2h1v-2h1c0 1 0 2 1 2v-3h0l1-2z" class="R"></path><path d="M265 312h0c1 1 1 1 1 2h2v-2c0 1 1 1 1 2h2 0 1v1l-1 1c-2 1-2 2-5 3h1v3h-2l-2 1h-5v-1h-2 0c-1 0-2 0-3 1 0 1 1 1 1 2-1 1-1 1-1 2v-1c-1-2-1-3-2-5 2-1 3-1 5-2h0c0-1-2-1-3-1l3-1 9-1-1-1c-1 0 0-1-1-2l2-1z" class="H"></path><path d="M256 322c3-2 7-3 10-3h1v3h-2l-2 1h-5v-1h-2 0z" class="U"></path><path d="M265 312h0c1 1 1 1 1 2h2v-2c0 1 1 1 1 2h2 0 1v1l-1 1h-3s0 1-1 1h-2c-2 1-3 1-4 1-2 0-3 1-5 1h0c0-1-2-1-3-1l3-1 9-1-1-1c-1 0 0-1-1-2l2-1z" class="b"></path><path d="M265 312h0c1 1 1 1 1 2h2v-2c0 1 1 1 1 2h2 0l-6 2-1-1c-1 0 0-1-1-2l2-1z" class="C"></path><path d="M156 373c1 0 3 1 4 0 1 0 2 0 3 1h0c1 2 3 3 4 5h0l4 4h0v1l-1-1v1c-1 2-1 4-1 6 0 1-1 4 0 6v2l-1 1h0v2c-1 1-1 2-1 3 1 0 1 1 2 1l-2 1c-1-1-2-1-3-1h-3-2v-2c-2-1-1-2-2-4h-2l-1-1s1 0 1-1c-1 0-1-1-2-1h0c0-2 1-1 1-2 1-1-1-3 0-4v-2c1 0 1 1 2 2 0-2 0-3-1-4 0 0-1 0-1-1l1-1v-1c-1-1-2-1-3-1h2c-1-1-1-1-1-2 1 0 1 0 2-1h-1c0-2 1-2 1-3s-1-2-1-2h0c1 0 1-1 2-1z" class="S"></path><path d="M165 392l1 1c0 2 0 2-1 3h-1v-1c0-1 0-2 1-3z" class="F"></path><path d="M157 383h0c1 0 1 0 2 1 1-1 1-1 2-1v1c-1 1-2 1-2 2h-2v-3z" class="e"></path><path d="M167 395l2 1v2l-1 1h-2v-1c0-1 1-2 1-3z" class="K"></path><path d="M161 384h1c1-1 2-1 3-1l1 1c-2 1-3 1-4 2h-1-2 0c0-1 1-1 2-2z" class="V"></path><path d="M156 398v-1h1v-2h1c1 1 1 2 2 2h2c1 0 1 1 2 1h2v1h-4c-3 0-4 0-6-1z" class="D"></path><path d="M155 397l1 1c2 1 3 1 6 1h4 2 0v2c-1 1-1 2-1 3 1 0 1 1 2 1l-2 1c-1-1-2-1-3-1h-3-2v-2c-2-1-1-2-2-4h-2l-1-1s1 0 1-1z" class="M"></path><path d="M163 403s1-2 0-2c0-1 0-1-1-2h6v2l-2-1-1 1c0 1 0 1-1 2h-1z" class="Z"></path><path d="M155 397l1 1c2 1 3 1 6 1l-1 2h0l-1-1h-1v3c-2-1-1-2-2-4h-2l-1-1s1 0 1-1z" class="g"></path><path d="M163 403h1c1-1 1-1 1-2l1-1 2 1c-1 1-1 2-1 3 1 0 1 1 2 1l-2 1c-1-1-2-1-3-1l-1-1v-1z" class="H"></path><path d="M165 383c1 0 2 1 3 1h2c-1 2-1 4-1 6 0 1-1 4 0 6l-2-1c1 0 1-1 0-1 0-2-1-2-2-3-1 0-1 0-2 1v1 3h-1v-3-1-1l-1 1v2l-1-1v-2-1-2s1-1 1-2h1c1-1 2-1 4-2l-1-1z" class="d"></path><path d="M162 386l1 1c0 1 0 2-1 4h-2v-1-2s1-1 1-2h1z" class="K"></path><path d="M165 383c1 0 2 1 3 1v2c-1 1-4 1-5 1l-1-1c1-1 2-1 4-2l-1-1z" class="Z"></path><path d="M156 373c1 0 3 1 4 0 1 0 2 0 3 1h0c1 2 3 3 4 5h0l4 4h0v1l-1-1v1h-2c-1 0-2-1-3-1s-2 0-3 1h-1v-1c-1 0-1 0-2 1-1-1-1-1-2-1h0v-1l-1 1-1 1h0v-1c-1-1-2-1-3-1h2c-1-1-1-1-1-2 1 0 1 0 2-1h-1c0-2 1-2 1-3s-1-2-1-2h0c1 0 1-1 2-1z" class="R"></path><path d="M157 375s1-1 2 0c1 0 1 1 2 1v2c-1-1-2 0-3-1 0-1 0-1-1-2z" class="N"></path><path d="M167 379h0l4 4h0v1l-1-1v1h-2c-1 0-2-1-3-1s-2 0-3 1h-1v-1l1-2 1-1c1 0 2 0 3 1 1-1 1-1 1-2z" class="B"></path><path d="M163 380c1 0 2 0 3 1l1 1v1h-3c0-1 1-2 0-3h-1z" class="C"></path><path d="M167 379h0l4 4h0v1l-1-1c-1-1-2-1-3-1l-1-1c1-1 1-1 1-2z" class="L"></path><path d="M157 375c1 1 1 1 1 2 1 1 2 0 3 1l1 3-1 2c-1 0-1 0-2 1-1-1-1-1-2-1h0v-1l-1 1v-2c0-2 0-5 1-6z" class="M"></path><defs><linearGradient id="J" x1="151.326" y1="519.817" x2="151.801" y2="488.093" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#262525"></stop></linearGradient></defs><path fill="url(#J)" d="M144 488c1 0 1 1 1 0v1h0l1 1 2 1h1c1 1 1 1 1 2l1-1c1-1 1-2 2-2h1v1h1l1-1v2 1s0 1 1 2v1 1c-1 1-1 1 0 2h0v3 1l1 1v-1-1h1v1l1 5v2c0 1 1 2 1 3v5 2h-2c-1-1-3 0-5 0-1-1-3-2-4-3l-3-2c-3-3-7-10-7-15h0l1 2c1-1 1-5 1-7s1-5 2-7z"></path><path d="M155 505c1-1 1-1 1-2v6c0-1-1-1-1-2v-2z" class="E"></path><defs><linearGradient id="K" x1="151.756" y1="515.111" x2="151.922" y2="501.609" xlink:href="#B"><stop offset="0" stop-color="#070607"></stop><stop offset="1" stop-color="#262525"></stop></linearGradient></defs><path fill="url(#K)" d="M151 492c1-1 1-2 2-2h1v1h1l1-1v2 1s0 1 1 2v1 1c-1 1-1 1 0 2h0v3l-1 1c0 1 0 1-1 2v2c0 1 1 1 1 2v5c1 1 1 2 1 3v1h0l-1-1c0-2-2-5-3-6l-2-3c-1-1-2-4-3-5-1 0-1 0-1-1h0v-3h-1c-1-2 0-3 0-5 0 0 0-1 1-1h0v2l1-1c1-1 1-2 1-3 1 1 1 1 1 2l1-1z"></path><path d="M153 498h1v2 2h1c1-1 1-2 2-3h0v3l-1 1c0 1 0 1-1 2-1-1-1-2-1-3h-2v4c-1-1-2-3-2-5h1l2-3z" class="i"></path><path d="M151 492c1-1 1-2 2-2h1v1h1l1-1v2 1s0 1 1 2v1 1c-1 1-1 1 0 2-1 1-1 2-2 3h-1v-2-2h-1v-1c1-1 1-4 0-6l-2 1z" class="F"></path><g class="M"><path d="M154 498v-1l1-1c1 1 1 2 1 3-1 1-1 1-2 1v-2z"></path><path d="M155 491l1-1v2 1s0 1 1 2v1 1l-2-1c-1-2 0-3 0-5z"></path></g><path d="M149 491c1 1 1 1 1 2l1-1 2-1c1 2 1 5 0 6v1l-2 3h-1-1l-1 1v-1h0v-1c0-1-1-1-2-3v-3s0-1 1-1h0v2l1-1c1-1 1-2 1-3z" class="e"></path><path d="M151 494c1 0 1 1 2 1v1l-1 1-1 1-1-1 1-3z" class="H"></path><path d="M146 494s0-1 1-1h0v2l1-1v4h0c1 0 1-1 1-2h1v4l1 1h-1-1l-1 1v-1h0v-1c0-1-1-1-2-3v-3z" class="h"></path><path d="M144 301l2 2c1 1 1 2 2 2l1 2 3 3c2 2 4 4 5 6 1 0 2 1 2 2v2h1l1 1v8 9c0 1 0 2-1 3-1 0-2-1-3-2 0 0-1 0-1-1-1 0-1-1-1-2l-2 2c-1-1-1-2-2-3h-2c0-1-1-1-1-2-2-1-3-3-4-5l-1 1v-2s0-1-1-2h0c0-1-1-1-2-2l2-2v-11-2-4-1h0c1 0 2-1 2-2z" class="Y"></path><path d="M144 314c0-1 2-1 3-1v1 1h2v1c-1 1-1 0-2 0s-1-1-2-2h-1z" class="W"></path><path d="M142 308c1 0 3 2 4 2s3 1 3 1c0 1-1 2-2 2h0c-1 0-3 0-3 1h-1c-1-2-1-3-1-4v-2z" class="M"></path><path d="M152 310c2 2 4 4 5 6-2 1-3 1-4 2l-1 1c-1 1-2 1-3 1 1-1 1-3 3-5h0c-1-1 0-3 0-5z" class="X"></path><path d="M144 301l2 2c1 1 1 2 2 2l1 2-3 3c-1 0-3-2-4-2v-4-1h0c1 0 2-1 2-2z" class="T"></path><path d="M146 303c1 1 1 2 2 2l-1 1c-1 0-1 1-2 1s-1 0-1-1l2-3zm11 13c1 0 2 1 2 2v2h1l1 1v8 9c0 1 0 2-1 3-1 0-2-1-3-2 0 0-1 0-1-1-1 0-1-1-1-2l-2 2c-1-1-1-2-2-3h-2c0-1-1-1-1-2 1 0 1-1 2-1h0v-1c-2-1-2 0-4-1h0l4-2c0-1 0-2-1-2v-6c1 0 2 0 3-1l1-1c1-1 2-1 4-2z" class="J"></path><path d="M157 316c1 0 2 1 2 2v2h-1v-1h-1l-1 1c0 1 0 2-1 3h-1v-2c0-1-1-2-2-2l1-1c1-1 2-1 4-2z" class="f"></path><path d="M158 334h2c1 0 0-4 1-5v9c0 1 0 2-1 3-1 0-2-1-3-2 0 0-1 0-1-1-1 0-1-1-1-2l-2 2c-1-1-1-2-2-3h-2c1-1 2-1 2-2 1-1 0-2 1-3l1 1c1 0 1 0 1 1 2 0 3 1 4 2z" class="Y"></path><path d="M151 335s0-1 1-1h1 2 1c0 1 1 1 1 2h-2l-2 2c-1-1-1-2-2-3z" class="J"></path><path d="M158 334h2c1 0 0-4 1-5v9c0 1 0 2-1 3-1 0-2-1-3-2 0 0-1 0-1-1-1 0-1-1-1-2h2c1 1 2 1 3 1v-1c-1 0-1-1-2-1v-1z" class="b"></path><path d="M157 339h3v2c-1 0-2-1-3-2z" class="f"></path><path d="M129 452h4 9 8v1h2l1 2h-1s0 1-1 1l1 2-1 1h-1c1 1 2 1 3 1 1 1 1 1 1 2-1 1-2 1-3 1-1 1-3 0-4 0h-13c-1 0-3-1-5 0h0l1 1h0c-2 1-3 0-5-1v-1h-5c-1 1-1 2-1 2h-1v-1c-1-1-2 0-4-1l-1 2c-1-2-3-2-5-2l-6-2-1 1c-2 1-4-2-5-3h-5c1 0 7-1 8 0v1h1l1-1-1-1c2-1 3-2 5-3l1 1c-2 0-3 1-4 2h0c2 0 2 0 4-1 0 0 0 1 1 1s1 1 2 1v-4-1h2c3-1 5 0 7 1h0l2-2h2c1 1 2 1 3 1s3-1 4-1z" class="j"></path><path d="M111 453c3 0 4 1 7 1v2h0 2c2 0 4 1 5 1 1 1 2 1 2 2h0-7c-1 0-2 1-3 0h0c-2 1-5 0-6 0-1-1 0-3 0-4l-1-1 1-1z" class="T"></path><path d="M129 452h4l-1 3v1 3h-3 0 0v1h-1l-1-1h0c0-1-1-1-2-2-1 0-3-1-5-1h-2 0v-2c-3 0-4-1-7-1h0c3-1 5 0 7 1h0l2-2h2c1 1 2 1 3 1s3-1 4-1z" class="K"></path><path d="M118 454l2-2h2c1 1 2 1 3 1-1 1-1 2-2 2-2 0-3-1-5-1z" class="o"></path><path d="M129 452h4l-1 3v1l-1 1-1-1c0 1-1 1-2 1 0-1-1-1-1-2 0-2 1-2 2-3z" class="J"></path><path d="M133 452h9 8v1h2l1 2h-1s0 1-1 1l1 2-1 1h-1c-2 0-4-1-6 0-1 0-1 1-1 2l-1-1v-1h0l-1 1c-1 0-2 0-3-1v-2h0c-1 1-1 2-2 3h-1l-1-1c-1-1-1-1-1-3v-1h1v-1l-2 1 1-3z" class="i"></path><path d="M139 454v-1h2c1 1 1 2 1 2 0 1-1 1-1 2-1 0-1 0-2-1v-2h0z" class="P"></path><path d="M134 454v-1h3v2 2c-1 0-3 0-4-1v-1h1v-1z" class="f"></path><path d="M133 452h9 8v1h2l1 2h-1s0 1-1 1l1 2-1 1-1-1v-1c-1 0-1 0-2 1v-2l-2 2s-1-1-1-2c-1-1-1-2-3-2v1s0-1-1-2h-2v1h-1l-1 1v-2h-3v1l-2 1 1-3z" class="a"></path><path d="M174 568c1 0 2 0 2 1 2-1 3-1 4-1 1 1 2 0 3 0s3 1 4 1v1 1c1 1 1 2 3 2 0 1 0 0 0 0h1c0 1 0 2 1 2h1l1 1-1 1c0 1 1 1 1 1v2h1c0 1 0 1 1 1 0 1 1 2 1 2h3l1 2-1 1c1 2 3 2 4 4h-1c-3-2-5-4-7-6s-3-3-5-4-4-1-6-1-3 0-5 1v2l-1-1v2h-1 0c0-1 0-1-1-2-1 0-5 1-6 2-2 1-3 3-5 4l-1-1c2-1 3-1 4-3 1 0 2-1 3-2h2c1 0 3 0 3-1-1-1-6 0-7 0-3 0-6-1-9-1-2 0-8-1-10 1l-18-11h1c4-1 10-1 15-1 2 0 4 0 7 1 1 0 2-1 3 0h2c3-1 5-1 7 0 1-1 2-1 3-1l2 1 1-1z" class="D"></path><path d="M197 583h3l1 2-1 1-3-3z" class="B"></path><path d="M178 574h0 2c1 1 2 2 3 4h0-7c-2 1-4 0-6 0h-3s-1 0-1-1h5c3-1 4-2 7-3z" class="j"></path><path d="M183 573c0-1 1-1 1-1 1-1 1-2 2-2h1v1c1 1 1 2 3 2 0 1 0 0 0 0v2c0 1 1 2 1 3h-3-3-2 0c1-1 1-1 1-2v-1h-2v-2h1z" class="C"></path><path d="M185 578l1-1h1c1 0 1 0 1 1h-3z" class="U"></path><path d="M187 571c1 1 1 2 3 2 0 1 0 0 0 0v2h-2s-1 1-2 1v-1h-2v-1c1 0 1 1 1 1 1-1 2-3 2-4z" class="R"></path><path d="M183 573c0-1 1-1 1-1 1-1 1-2 2-2h1v1c0 1-1 3-2 4 0 0 0-1-1-1l-1-1z" class="D"></path><path d="M174 568c1 0 2 0 2 1 2-1 3-1 4-1 1 1 2 0 3 0s3 1 4 1v1h-1c-1 0-1 1-2 2 0 0-1 0-1 1h-1v2h2v1c0 1 0 1-1 2-1-2-2-3-3-4h-2 0v-1c1 0 1 1 2 1v-1c0-1-1-1-2-2-1 0-3 1-5 1h0c1-1 2-2 2-3l-1-1z" class="R"></path><path d="M168 569c1-1 2-1 3-1l2 1 1-1 1 1c0 1-1 2-2 3h0c2 0 4-1 5-1 1 1 2 1 2 2v1c-1 0-1-1-2-1v1c-3 1-4 2-7 3-1 0-1-1-1-1-1-1-1-1-2-1h0v-2l1-1v-2l-1-1z" class="h"></path><path d="M168 569c1-1 2-1 3-1l2 1c0 1-1 2-2 3l-3 3v-2l1-1v-2l-1-1z" class="K"></path><path d="M169 572v-2l1-1c1 1 1 2 1 3l-3 3v-2l1-1z" class="C"></path><defs><linearGradient id="L" x1="152.026" y1="567.808" x2="151.371" y2="576.759" xlink:href="#B"><stop offset="0" stop-color="#2b2a2a"></stop><stop offset="1" stop-color="#4c4c4d"></stop></linearGradient></defs><path fill="url(#L)" d="M133 569h1c4-1 10-1 15-1 2 0 4 0 7 1 1 0 2-1 3 0h2c3-1 5-1 7 0l1 1v2l-1 1v2h0c1 0 1 0 2 1 0 0 0 1 1 1h-5-10-6c3 2 9 1 13 1v1h-2c-2 0-8-1-10 1l-18-11z"></path><path d="M156 577l1-1h2c1-1 2 0 3 0l1-1h0-3 0v-1l1-1h2 1l-1-2h1l1 1h3v1 2h0c1 0 1 0 2 1 0 0 0 1 1 1h-5-10z" class="N"></path><path d="M168 572v1 2h0l-1 1h-2v-1-1l2 1 1-1v-2z" class="D"></path><defs><linearGradient id="M" x1="231.379" y1="88.739" x2="222.073" y2="87.831" xlink:href="#B"><stop offset="0" stop-color="#424041"></stop><stop offset="1" stop-color="#595959"></stop></linearGradient></defs><path fill="url(#M)" d="M218 65h2c1 1 4 0 6 0h2l1 1-1 1c1 6 0 12 1 17l2 1c-3 0-7-1-10 0h2c2 1 4 1 5 1s2 1 2 1h1v1l1 1v1l-1-1c-1 1-1 1-1 2 1 1 2 2 2 3l-2 2v-1c-1 1-1 2-2 3-1 0-2-1-2-2h0-1c-1-1-2-1-3-1l-3 3c0 1-1 1-1 2 0-1 0-1-1-1s-1 1-2 1c-1 2-2 3-4 4-1 0-2 1-3 0h-1-1c0-1-1-3-2-3 0 1 1 3 1 4v1l-2-3v-3h-1 0c-1-2 0-5 1-7 3-5 8-8 13-9v-6h0v-1h-2 0c0-1 1-1 2-1 1-1 0-7 1-9 0 0-1 0-1-1h-2v-1c1 0 3 1 4 0z"></path><path d="M219 87c1 1 1 1 3 1 0 0 0 1-1 1v1h-2c-1-1 0-2 0-3z" class="g"></path><path d="M214 86c2-1 4-1 6-1v1h-1v1c0 1-1 2 0 3h-1-2l1-1h1v-1c-1-1-1-2-2-2h-2z" class="Z"></path><path d="M218 65h2c1 1 4 0 6 0h2l1 1-1 1h-2-1l-1 1-2-2h-4c0 1 0 2-1 3h0v-2s-1 0-1-1h-2v-1c1 0 3 1 4 0z" class="E"></path><path d="M225 91h-1v-1h1l3 1c1 0 0-2 2-2h1c-1 1-1 1-1 2 1 1 2 2 2 3l-2 2v-1l-1-1c0-1-1-1-2-1s-1-1-2-2z" class="D"></path><path d="M221 90v-1h1c0 1 1 1 2 2v2h1v-2c1 1 1 2 2 2s2 0 2 1l1 1c-1 1-1 2-2 3-1 0-2-1-2-2h0-1c-1-1-2-1-3-1v-1h0l-1-1-1-1h1v-1-1z" class="G"></path><path d="M225 91c1 1 1 2 2 2s2 0 2 1h-1v2c-1 0-1-1-3-2l-1-1h1v-2z" class="M"></path><defs><linearGradient id="N" x1="231.034" y1="73.769" x2="221.992" y2="78.993" xlink:href="#B"><stop offset="0" stop-color="#1a1b1a"></stop><stop offset="1" stop-color="#333134"></stop></linearGradient></defs><path fill="url(#N)" d="M224 68l1-1h1 2c1 6 0 12 1 17l2 1c-3 0-7-1-10 0h2l-2 2v-1h0v-1h-3v-1c1 0 4 1 5 0s1-3 2-5v-1h0c0-2-1-4 0-5 0-2 0-4-1-5z"></path><path d="M214 86h2c1 0 1 1 2 2v1h-1l-1 1h2 1 2v1 1h-1l1 1 1 1h0v1l-3 3c0 1-1 1-1 2 0-1 0-1-1-1s-1 1-2 1c-1 2-2 3-4 4-1 0-2 1-3 0h-1-1c0-1-1-3-2-3 0 1 1 3 1 4v1l-2-3c0-3-1-7 1-10 1-3 5-6 8-6l2-1h0z" class="o"></path><path d="M214 86h2c1 0 1 1 2 2v1h-1l-1 1h2 1 2v1 1h-1-1l-2 1c-1-1-1-1-3-2-1 0-1 0-2 1v1l-1 1-1-3h2l2-2 1-1c-1 0-2 0-3 1v-1l1-1h2c1 0 1 0 1-1h-2 0z" class="f"></path><path d="M219 90h2v1 1h-3v-2h1z" class="X"></path><path d="M212 92c1-1 1-1 2-1 2 1 2 1 3 2l2-1h1l1 1 1 1h0v1l-3 3c0 1-1 1-1 2 0-1 0-1-1-1s-1 1-2 1c-1 2-2 3-4 4l-1-1s-2-1-2-2c-1-1-1-3-1-4l3-3h1l1-1v-1z" class="T"></path><path d="M212 92c1-1 1-1 2-1 2 1 2 1 3 2v1h-1c0 1-1 1 0 2l-1 1c-1-1-1-3-2-5h-1z" class="J"></path><path d="M217 93l2-1h1l1 1 1 1h0v1l-3 3c0 1-1 1-1 2 0-1 0-1-1-1l-2-2 1-1c-1-1 0-1 0-2h1v-1z" class="c"></path><path d="M217 94h1v2h-1-1c-1-1 0-1 0-2h1z" class="Q"></path><path d="M216 96h1v1h2v1c0 1-1 1-1 2 0-1 0-1-1-1l-2-2 1-1z" class="W"></path><path d="M210 94c2 1 3 2 4 3 0 2 0 3-2 5 0 1-1 1-1 1h-1s-2-1-2-2c-1-1-1-3-1-4l3-3z" class="d"></path><path d="M223 85h-2c3-1 7 0 10 0 4 2 9 5 10 9 2 4 2 7 0 11l-4 6 1 6c0 1 1 1 0 1-1 3 1 5-2 6h-1v-1c0-1-1-2-2-3-1 1-1 2-2 3h0l-1-1c0-1 0-1 1-2h0-1-3 0v-1c0-1 1-2 1-3s-1-1-2-1-4-1-6 0v1 4h1v3c-1-1-1-1-2-1v-1h0c0-1 0-2-1-3h-1l1-1v-2h-4c-1 0-2 0-3-1 0 0-1-1-1-2h0l-1-2c-1-1-3-2-4-4v-1c0-1-1-3-1-4 1 0 2 2 2 3h1 1c1 1 2 0 3 0 2-1 3-2 4-4 1 0 1-1 2-1s1 0 1 1c0-1 1-1 1-2l3-3c1 0 2 0 3 1h1 0c0 1 1 2 2 2 1-1 1-2 2-3v1l2-2c0-1-1-2-2-3 0-1 0-1 1-2l1 1v-1l-1-1v-1h-1s-1-1-2-1-3 0-5-1z" class="O"></path><path d="M228 111l-1-1c0-1 1-2 1-3l1-1c0-1 0-1 1-2h1v1h0v2c-1 0-2 0-3 1 1 0 1 1 2 1 0 1-1 1-2 2z" class="E"></path><path d="M231 87c1 1 3 3 3 5h0l1 1c2 1 3 1 4 4h-1v-1c-2-1-3-1-5-1v-1h-1c0-1-1-2-2-3 0-1 0-1 1-2l1 1v-1l-1-1v-1z" class="F"></path><path d="M239 97c0 2 0 3-1 5-1 1-2 3-4 3h-3v-1h-1c-1 1-1 1-1 2l-1 1c0 1-1 2-1 3l1 1c-1 0-2 1-3 1v1h0l-1-2h0c-1 1-1 1-2 1h-2c0-1 1-1 1-1 2-1 5-3 6-5 0-1 0-1 1-3 1 0 1-1 1-2 2 0 3 1 4 2l1-1c1 0 1 1 1 1 1 0 1-1 2-1 2-2 1-3 1-5h1z" class="d"></path><path d="M232 94h1v1c2 0 3 0 5 1v1c0 2 1 3-1 5-1 0-1 1-2 1 0 0 0-1-1-1l-1 1c-1-1-2-2-4-2 0 1 0 2-1 2h-2l2-2h-1c-1 1-2 1-3 2h-1s-1 0-2-1-1-2-2-4l3-3c1 0 2 0 3 1h1 0c0 1 1 2 2 2 1-1 1-2 2-3v1l2-2z" class="M"></path><path d="M222 95c1 0 2 0 3 1h1 0l-1 1c1 1 1 2 1 3-1 1-1 2-2 2h-1 0v1s-1 0-2-1-1-2-2-4l3-3z" class="R"></path><path d="M223 102c-1-1-1-1-1-2s0-2 1-3h2c1 1 1 2 1 3-1 1-1 2-2 2h-1z" class="P"></path><path d="M233 95c2 0 3 0 5 1v1c0 2 1 3-1 5-1 0-1 1-2 1 0 0 0-1-1-1s-2-1-2-2h0c-1-1-1-1-1-2 0-2 1-2 2-3z" class="U"></path><path d="M225 112l2 2h0c1-1 1-1 1-2h2v1-1h2l1-1s0-1 1-1l-1 1v1 1 1l4-1v1-3l1 6c0 1 1 1 0 1-1 3 1 5-2 6h-1v-1c0-1-1-2-2-3-1 1-1 2-2 3h0l-1-1c0-1 0-1 1-2h0-1-3 0v-1c0-1 1-2 1-3s-1-1-2-1-4-1-6 0v1 4h1v3c-1-1-1-1-2-1v-1h0c0-1 0-2-1-3h-1l1-1v-2h-4c-1 0-2 0-3-1 0 0-1-1-1-2 1 0 1 0 1 1h2 1c1 0 2-1 3-1l1 1 2-1h2c1 0 1 0 2-1h0l1 2h0v-1z" class="C"></path><path d="M210 112c1 0 1 0 1 1h2 1c1 0 2-1 3-1l1 1-1 2h-3c-1 0-2 0-3-1 0 0-1-1-1-2z" class="O"></path><path d="M219 98c1 2 1 3 2 4s2 1 2 1h1c1-1 2-1 3-2h1l-2 2h2c-1 2-1 2-1 3-1 2-4 4-6 5 0 0-1 0-1 1l-2 1-1-1c-1 0-2 1-3 1h-1-2c0-1 0-1-1-1h0l-1-2c-1-1-3-2-4-4v-1c0-1-1-3-1-4 1 0 2 2 2 3h1 1c1 1 2 0 3 0 2-1 3-2 4-4 1 0 1-1 2-1s1 0 1 1c0-1 1-1 1-2z" class="G"></path><path d="M219 98c1 2 1 3 2 4s2 1 2 1h1v1c-1 0-4 1-5 1 0 0-1-1-2-1 1-1 1-3 1-4s1-1 1-2z" class="V"></path><path d="M217 99c1 0 1 0 1 1s0 3-1 4c0 1-1 1-1 2l-2-1v1h-1l-1-1h1v-1c2 0 2-2 3-3l-1-1c1 0 1-1 2-1z" class="J"></path><path d="M209 110c1 0 1 1 1 1v-1-2h0l1-1c1 0 1 0 1 1 1 0 2 1 2 1l1 1-1 1v2h-1-2c0-1 0-1-1-1h0l-1-2z" class="I"></path><path d="M210 112l1-1v-1l1-1c1 2 1 2 1 4h-2c0-1 0-1-1-1h0z" class="d"></path><path d="M226 103h2c-1 2-1 2-1 3-1 2-4 4-6 5 0 0-1 0-1 1l-2 1-1-1c-1 0-2 1-3 1v-2l1-1-1-1c2-2 6-1 8-3h3c1-1 1-2 1-3z" class="N"></path><path d="M214 109c2-2 6-1 8-3v2c-2 2-5 2-7 2l-1-1zm-46 290l1-1v2 2c0 1 1 2 1 2h1c2 1 3 2 4 3 2 2 3 3 4 5 0 1 0 1-1 2v3h-2c0 1 1 1 1 2h0l-1 1h1v1h-2l1 2c0 1 0 2 1 3h-1v1 1 1c-1 0-2 0-2 1v1l-1 3v4 1c0 1 0 2 1 3l-2 2c1 1 2 2 3 2h-3v1c0 1-1 2-2 2l-3-2c-2-1-4-1-6-2v-2c0 1 0 2-1 3-1 0-1-1-2-1v-1l-1 1v1h-2l-1 1h0l-2 1c-1-1-2-1-3-3s0-6 0-8c0-1 0-2 1-2 1-1 2-1 3-1l3-3h0v-2l1-1v-1-1-2l-1-1h0v-1-1l1-1h2 0v-6-6-3h2 3c1 0 2 0 3 1l2-1c-1 0-1-1-2-1 0-1 0-2 1-3v-2h0z" class="H"></path><path d="M170 408l1-1 2 2c0 2 0 3-1 4h-1s0-1-1-2v-3z" class="c"></path><path d="M160 430c1 0 2 0 3-1h1c1 0 1 0 1 1v1 2c-1 1-1 1-2 1v-1-1c-1-1-1-1-3-1v-1z" class="G"></path><path d="M156 422c2 0 4 0 5 1s1 1 0 2h1v1l-1 1h-1v1l1 1h-1 0s-1 1-2 1l-1-2v-1-1-2l-1-1h0v-1z" class="e"></path><path d="M157 426h1 1 0l1-1 2 1-1 1h-1v1l1 1h-1 0s-1 1-2 1l-1-2v-1-1z" class="G"></path><path d="M166 429h0c1-1 0-2 1-3 0-2-1-2-1-4 1 0 2 0 2 1 2-1 2-1 4-1l1 1c-2 2-2 3-2 5h-1v2l-1 1v-2h0-3z" class="i"></path><path d="M168 424l1-1v1 2h-1v-2z" class="C"></path><path d="M169 431l1-1v-2h1v2c-1 3 0 7 0 10-3 0-3-3-5-3l1-1c0-2 0-5-1-7h3 0v2z" class="D"></path><path d="M166 429h3 0v2h-1c0 2 3 3 2 4-1 0-2 1-3 1h0c0-2 0-5-1-7z" class="E"></path><path d="M173 423h1v-1-1h1l1 2c0 1 0 2 1 3h-1v1 1 1c-1 0-2 0-2 1v1l-1 3v-2c0-1 0-1-1-1l-1-1v-2c0-2 0-3 2-5z" class="J"></path><path d="M176 423c0 1 0 2 1 3h-1-1-1v-1l2-2z" class="Q"></path><path d="M172 431c2-2 0-3 1-4s1-1 2-1h1v1 1 1c-1 0-2 0-2 1v1l-1 3v-2c0-1 0-1-1-1z" class="H"></path><path d="M169 416l1-1v3h1v2c-2 1-4 0-6 0h-6 0l2-2s-1-1 0-1c0-1 1-1 1-1h1v2c1 0 2-1 2-1h2l1-1h1z" class="C"></path><path d="M169 416l1-1v3h1v2c-2 1-4 0-6 0v-2h1 0v2h2v-1-3h1z" class="B"></path><path d="M169 416l1-1v3h1c-1 0-2 1-3 1v-3h1z" class="d"></path><path d="M168 399l1-1v2 2c0 1 1 2 1 2h1c2 1 3 2 4 3 2 2 3 3 4 5 0 1 0 1-1 2v3h-2c0 1 1 1 1 2h0l-1 1h0-1c-1-1-2 0-2 0h-1v-4h2l1-1h-1-1s-1-1-1-2h1c1-1 2-2 2-3l-2-1-2-2-1 1v-3h-1c-1 0-1-1-2-1 0-1 0-2 1-3v-2h0z" class="R"></path><path d="M171 430l1 1c1 0 1 0 1 1v2 4 1c0 1 0 2 1 3l-2 2c1 1 2 2 3 2h-3v1c0 1-1 2-2 2l-3-2c-2-1-4-1-6-2v-2c-1-1-1-2-1-3h1 2v-1h0v-2l1-1 1 1h1c2 0 2 3 5 3 0-3-1-7 0-10z" class="f"></path><path d="M164 436l1 1c0 1 0 2-1 3l-1-1h0v-2l1-1z" class="Q"></path><path d="M160 440h1v2l1 1c1 1 0 1 0 2h1 1c1-1 2-2 2-3l1 2c1 1 1 2 2 3h-2c-2-1-4-1-6-2v-2c-1-1-1-2-1-3z" class="a"></path><path d="M166 442h-1v-1c1 0 3-1 4-1 1 1 2 3 3 4s2 2 3 2h-3v1c0 1-1 2-2 2l-3-2h2c-1-1-1-2-2-3l-1-2z" class="D"></path><path d="M169 447c1 0 1-1 3-1v1c0 1-1 2-2 2l-3-2h2z" class="e"></path><path d="M159 405h2 3c1 0 2 0 3 1l2-1h1v3 3 4l-1 1h-1l-1 1h-2s-1 1-2 1v-2h-1s-1 0-1 1c-1 0 0 1 0 1l-2 2v-6-6-3z" class="h"></path><path d="M159 405h2l1 2c-1 1-2 1-3 1v-3z" class="K"></path><path d="M163 406h1c0 3 1 5 1 7s0 2-1 2c0-1 0-3-2-4h0c1-2 1-3 1-5z" class="e"></path><path d="M159 414c1-1 1-2 2-2h1 1 0c-1 1-1 1-1 2-1 1-1 1-2 1 1 1 1 1 2 1 0 0-1 0-1 1-1 0 0 1 0 1l-2 2v-6z" class="B"></path><path d="M169 405h1v3 3 4l-1 1h-1c-1-1 0-2 0-3 0 0-1 0-1-1v-1h2v-1c0-2 0-2-1-2-1-1-1-1-1-2l2-1z" class="E"></path><path d="M156 431h0v-2l1-1 1 2c1 0 2-1 2-1v1 1c2 0 2 0 3 1v1l-1 1-2 2h3 1l-1 1v2h0v1h-2-1c0 1 0 2 1 3 0 1 0 2-1 3-1 0-1-1-2-1v-1l-1 1v1h-2l-1 1h0l-2 1c-1-1-2-1-3-3s0-6 0-8c0-1 0-2 1-2 1-1 2-1 3-1l3-3z" class="I"></path><path d="M159 435h-1 0v-3h1c1 1 1 2 0 3z" class="G"></path><path d="M160 431c2 0 2 0 3 1v1l-1 1-2 1h-1c1-1 1-2 0-3 1 0 1 0 1-1z" class="i"></path><path d="M154 436l2-1 1 1-1 1v3l-1 1h-1c0-2 1-3 0-5h0z" class="J"></path><path d="M160 436h3 1l-1 1v2h-1 0-2c-1-1-1-1-1-2s1-1 1-1z" class="P"></path><path d="M150 445v-9c1 0 1-1 2-1 1 3 1 5 1 8l-2 2h-1z" class="c"></path><path d="M151 445c0 1 1 1 1 1 1-1 2-1 3-2v-2h2l2-2h1c0 1 0 2 1 3 0 1 0 2-1 3-1 0-1-1-2-1v-1l-1 1v1h-2l-1 1h0l-2 1c-1-1-2-1-3-3h1 1z" class="B"></path><path d="M345 309c1 1 2 3 4 3 1 1 1 1 2 1l-1 4-1 2-3 6-1 1v2l-1 2c-1 1-2 2-2 3l-4 7c-1 3-3 5-4 7s-2 5-4 6h-26-13-4v-2c0-1 3-3 4-4 2-1 3-1 4-3h-1l4-4v-1h4 15c2-1 4 0 6 0l5-6c2-4 6-7 8-10 3-4 7-9 9-14z" class="Q"></path><path d="M346 312c1 0 2 1 3 1l-1 1c-1 1-1 1-2 0v-2z" class="T"></path><path d="M341 325h0l2-2h0l1 2h0 2l-1 1h-3l-1-1z" class="Y"></path><path d="M341 325l1 1 2 1h-1c0 1-2 1-3 1s-1 0-1-1l2-2z" class="m"></path><path d="M291 347c1 0 1 1 2 1s2-1 2-1c1 1 1 1 2 1 0-1 1 0 1 0l-2 1c0-1-1-1-1-1-1 1-1 1-1 2h0-4 0-1l-1 1c1 1 2 0 2 1 3 2 9 1 12 1h2-13-4v-2c0-1 3-3 4-4z" class="X"></path><path d="M328 333c1 0 2 0 2 1v2l-1 1c1 1 1 2 0 3v3l-1 1c-1 0-3 0-3 1-1 0-1 0-1 1 0-1 0-1-1-2-1 1-3 1-4 2-2 0-3 1-4 1v-1h1 0-2c-1 0-1 1-2 1-1 1-10 1-10 0-1 0-1-1-2 0h0c-1 0-1 1-2 1 0 0-1-1-1 0-1 0-1 0-2-1 0 0-1 1-2 1s-1-1-2-1c2-1 3-1 4-3h-1l4-4v-1h4 15c2-1 4 0 6 0l5-6z" class="f"></path><path d="M323 344h1s1 0 1-1v-2c0-1 1-1 2-2l-1 2c1 0 1 1 2 1v2c-1 0-3 0-3 1-1 0-1 0-1 1 0-1 0-1-1-2z" class="J"></path><path d="M327 339c1 0 1-1 2-2 1 1 1 2 0 3v3l-1 1v-2c-1 0-1-1-2-1l1-2z" class="Y"></path><defs><linearGradient id="O" x1="311.062" y1="337.998" x2="311.798" y2="342.24" xlink:href="#B"><stop offset="0" stop-color="#6d6a6c"></stop><stop offset="1" stop-color="#7e7f7e"></stop></linearGradient></defs><path fill="url(#O)" d="M302 339h15 4 0c-1 2-2 3-4 4-1 1-2 1-4 1h-8c-3 0-7-1-10 0h-1l4-4v-1h4z"></path><path d="M227 251v1c0 1-1 5 0 6h4 0 3 5c-1 1-1 2-1 3v3h0 0c0 2 0 3 1 4s3 0 5 0l1 2h0c1 0 2 0 3 1h2c1 0 1 0 2-1 1 0 1 0 2 1v1c1 2 1 3 0 5h0l2 1c1 0 2 1 3 1 0 1 1 1 2 1l-1 1c-2 0-2 2-4 1-1-1-1-1-3-1-1 1-1 2-2 2 0 0 0 1-1 1v1c2 0 4 1 6 3 1 0 2 1 3 2h1c0-1 1-2 1-3 1 2 1 2 3 3 0 1 0 1-1 2-1 0-1 1-1 1h0-3c-1 1-2 1-2 2l1 1-1 2c-2 2-5 6-6 9l-1 1c-1 1-1 2-2 3l-3 3h0c-1-1-2-3-3-3v2h-1v-2-5-5-1-1h-2v-2c-1 0-1 0-1-1h0c-2 0-3 0-5 1-2 0-3 2-5 1l-1-1v-4h0v-20l-1-15 1-7z" class="C"></path><path d="M236 293h-1c-2 1-4 1-6 1h0v-1c2 0 3-1 4-1h2l1 1z" class="B"></path><path d="M232 285c1 0 3-1 4 0v2c-1 0-1 0-2 1l1 1-2 2c-1-1-1 0-1-1-1-1-1-3-1-4l1-1z" class="U"></path><path d="M233 274h8l1 1-1 1c-3 0-7 0-10 1 0 0-1 1-2 1h-1c-1-2-1-2-1-4h0 6z" class="n"></path><path d="M243 273l2-1h2 2c0 1 1 2 2 2h1v1h1l1 1c-1 0-1 1-1 1v1c-1 0-1 0-2-1h-2-6l-2-1h0l1-1-1-1h-8l1-1h5 4z" class="K"></path><path d="M243 273c2 0 4 0 6 1l-1 1h0l-9-1v-1h4z" class="L"></path><path d="M243 273l2-1h2 2c0 1 1 2 2 2h1v1h-3-1 0l1-1c-2-1-4-1-6-1z" class="B"></path><path d="M254 271v1c1 2 1 3 0 5h0l2 1c1 0 2 1 3 1 0 1 1 1 2 1l-1 1c-2 0-2 2-4 1-1-1-1-1-3-1-1 1-1 2-2 2 0 0 0 1-1 1v1h0c0 1 0 2-1 3l-1 1v-1l-1-2v-1h0c0-1 0-1 1-2h0v-1-3h1v-2h2c1 1 1 1 2 1v-1s0-1 1-1l-1-1h-1v-1c0-1 1-2 2-2v-1z" class="i"></path><path d="M254 277l2 1c1 0 2 1 3 1 0 1 1 1 2 1l-1 1c-2 0-2 2-4 1-1-1-1-1-3-1-1 1-1 2-2 2 0 0 0 1-1 1v1h0v-1c0-2-1-4 0-5s2 0 3 0c1-1 1-1 1-2z" class="a"></path><path d="M254 277l2 1c1 0 2 1 3 1l-1 1h-3 0l-2-1c1-1 1-1 1-2z" class="D"></path><path d="M250 284c0-2-1-4 0-5s2 0 3 0l2 1h0l-1 1h-1-3v3z" class="S"></path><path d="M227 251v1c0 1-1 5 0 6h4 0 3 5c-1 1-1 2-1 3v3h0 0c0 2 0 3 1 4s3 0 5 0l1 2h-6v1h-4v-1c-1 0-1 1-2 1h-1l1-1-1-1h-2c-2-1 0-3-2-4l-1 1v7l-1-15 1-7z" class="B"></path><path d="M236 268h-2c-1-1-3-1-4-1 0-1-1-2-1-2h-1c0-2 0-2 1-3 1 2 2 3 3 4v1h3l1 1zm3 3l-3-1h1c1-1 1-1 1-2v-4c0 2 0 3 1 4s3 0 5 0l1 2h-6v1z" class="D"></path><path d="M231 258h3c-1 1-2 1-3 2 1 2 2 4 2 5l2 2h-3v-1c-1-1-2-2-3-4 0-1-1-2 0-3l2-1h0z" class="N"></path><path d="M234 258h5c-1 1-1 2-1 3v3h0v-4h-1v4 3l-1 1-1-1-2-2c0-1-1-3-2-5 1-1 2-1 3-2z" class="M"></path><path d="M237 264s0 1-1 1h0c-2-1-2-1-2-3 0-1 0-1 1-2s1-1 2-1v1 4z" class="a"></path><path d="M241 276l2 1h6v2h-1v3 1h0c-1 1-1 1-1 2h0v1l1 2v1l1-1c1-1 1-2 1-3h0c2 0 4 1 6 3 1 0 2 1 3 2h1c0-1 1-2 1-3 1 2 1 2 3 3 0 1 0 1-1 2-1 0-1 1-1 1h0-3c-1 1-2 1-2 2l1 1-1 2c-2 2-5 6-6 9l-1 1c-1 1-1 2-2 3l-3 3h0c-1-1-2-3-3-3v2h-1v-2-5-5-1-1h-2v-2c-1 0-1 0-1-1h0v-3h-2l-1-1h1c1 0 1 0 2-1h-1v-2h-2l-1-1c1-1 1-1 2-1v-2c-1-1-3 0-4 0v-1c0-1 0-3 1-4 0 0 3-2 4-2 1-1 2-1 4-2z" class="i"></path><path d="M238 291h0c1 0 1-1 2-1h0 1c1 0 2 0 2 1h1c-2 1-5 1-7 1h-1c1 0 1 0 2-1z" class="B"></path><path d="M239 285h1c1 1 1 2 1 4h-1l-3-2c1 0 1-1 2-2z" class="K"></path><path d="M242 293h3s0 1 1 0h4c1 0 2 0 3 1v1 1h-2 0-1-6s-1 0-2-1v-2z" class="E"></path><path d="M250 293c1 0 2 0 3 1v1 1h-2v-1h-3v-1l2-1z" class="O"></path><path d="M250 285h0c2 0 4 1 6 3 1 0 2 1 3 2v1c-2 0-2-1-4-1v1h-1v-1c-2-1-6-1-7-1-2 1-3 1-4 1-1-1-1-1-1-2h2c1 1 2 1 4 0v1l1-1c1-1 1-2 1-3z" class="I"></path><path d="M261 287c1 2 1 2 3 3 0 1 0 1-1 2-1 0-1 1-1 1h0-3c-1 1-2 1-2 2l1 1-1 2c-2 2-5 6-6 9l-2-2v-1c1 0 2-1 2-2 2-3-1-3-1-6h1 0 2v-1-1l-1-2c1 0 1 0 2-1h0 1v-1c2 0 2 1 4 1v-1h1c0-1 1-2 1-3z" class="B"></path><path d="M261 287c1 2 1 2 3 3 0 1 0 1-1 2-1 0-1 1-1 1h0-3c-1 1-2 1-2 2l-2 2h0l-1-1v-1c1-1 1-2 0-3v-1h0 1v-1c2 0 2 1 4 1v-1h1c0-1 1-2 1-3z" class="N"></path><path d="M243 277h6v2h-1v3 1h0c-1 1-1 1-1 2h0v1l1 2c-2 1-3 1-4 0h-2c-1-1-1-2-1-3v-1h-5-2l3-3c1 0 2-1 2-2v-1c1-1 2-1 4-1z" class="H"></path><path d="M248 283c-1 1-3 0-4 1v-1-1h4v1z" class="a"></path><path d="M240 281h0c1 1 2 2 3 2v1h-4c0-1 1-2 1-3z" class="S"></path><path d="M237 281c2 0 3-2 5-3l-1 3h-1c-1 0-1 1-1 2-1 0-2 0-3 1h0-2l3-3z" class="N"></path><path d="M243 277h6v2h-1v-1h-6c-2 1-3 3-5 3 1 0 2-1 2-2v-1c1-1 2-1 4-1z" class="L"></path><path d="M241 285c1 0 5 0 6 1l1 2c-2 1-3 1-4 0h-2c-1-1-1-2-1-3z" class="T"></path><path d="M242 288v-1c1-1 1-1 2-1v2h-2z" class="Q"></path><path d="M238 293h4v2c1 1 2 1 2 1h6c0 3 3 3 1 6 0 1-1 2-2 2v1l2 2-1 1c-1 1-1 2-2 3l-3 3h0c-1-1-2-3-3-3v2h-1v-2-5-5-1-1h-2v-2c-1 0-1 0-1-1h0v-3z" class="N"></path><path d="M246 309v-2s1 0 1-1v-1-1c1 0 1 0 2 1l2 2-1 1c-1 1-1 2-2 3l-1-1-1-1z" class="I"></path><path d="M246 309l2-2c1 0 1 1 2 1-1 1-1 2-2 3l-1-1-1-1z" class="R"></path><path d="M238 293h4v2c1 1 2 1 2 1h6c0 3 3 3 1 6-1 0-1 0-2-1h1v-1h0-4c-1 1-1 1-1 2v1l-1-1h-1v1c0 1 0 1-1 1v-4h-1v-1h-2v-2c-1 0-1 0-1-1h0v-3z" class="D"></path><path d="M238 293h4v2c1 1 2 1 2 1h-6 0v-3z" class="K"></path><defs><linearGradient id="P" x1="134.361" y1="542.911" x2="131.453" y2="562.874" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#282728"></stop></linearGradient></defs><path fill="url(#P)" d="M121 562l-29-14c-3-1-7-2-10-4l17 2 19 1h18v1c1 0 1 1 1 1l1-1h4c4-1 9 0 12 0 5 0 12-1 17 0 1 1 1 2 3 2 1 1 2 2 3 4 0 1 2 3 2 4 0 0-1 0-1 1h0c1 1 1 1 1 2h-1-1l-1 1-2 2c2 2 9 1 12 1 1 0 2 0 3 1v1h1v2h-1-2c-1 0-3-1-4-1s-2 1-3 0c-1 0-2 0-4 1 0-1-1-1-2-1l-1 1-2-1c-1 0-2 0-3 1-2-1-4-1-7 0h-2c-1-1-2 0-3 0-3-1-5-1-7-1-5 0-11 0-15 1h-1l-2-1h0l2-2c0-1 0 0-1 0-4-1-7-2-11-4z"></path><path d="M166 558l1-2h0c1 1 1 1 1 2h-2zm-30-6c1-1 2 0 4 0h0l-2 2h-1-1-1 0s0-1 1-1l-1-1h1zm-12-1c1 0 1 0 2 1v1l-2 2c-1 1-1 1-2 0h0c1-1 1-1 1-2 1 0 1-1 0-1l1-1z" class="j"></path><path d="M137 556h0 0l2-3c1 0 2 0 2-1v-2l-1-1h1c0 1 1 1 1 2h2v-1h1 1c2 1 4 4 7 5 1 0 2 1 3 1s3 1 4 1c2 1 4 0 5 1h1 2c2 0 4 1 6 1l1 2c2-1 2-1 3-2 1 1 1 1 1 2h-1-1l-1 1-2 2c2 2 9 1 12 1 1 0 2 0 3 1v1h1v2h-1-2c-1 0-3-1-4-1s-2 1-3 0c-1 0-2 0-4 1 0-1-1-1-2-1l-1 1-2-1c-1 0-2 0-3 1-2-1-4-1-7 0h-2c-1-1-2 0-3 0-3-1-5-1-7-1-5 0-11 0-15 1h-1l-2-1h0l2-2c0-1 0 0-1 0-4-1-7-2-11-4 1 0 2-1 2-1l2-2h1 0 1v-1-1h0c1 1 2 1 3 1h2c1 0 1 1 2 2 0-1 0-1 1-1h1v-1c0-1 0-1 1-2z" class="B"></path><path d="M137 556h0 0l2-3c1 0 2 0 2-1v-2l-1-1h1c0 1 1 1 1 2v1c1 1 2 2 2 3v2h0c-1 0-1 0-2-1v1l1 1-1 1h-1 0v-2-4c-1 2-3 4-4 5h-1l1-2z" class="O"></path><path d="M133 564l-1-1c1-1 2-2 3-2l1-2v1h0l1 1c0-1 1-1 2-1h1 2 1l-2 2c-1 1-2 1-2 2h-2-4z" class="D"></path><path d="M121 562c1 0 2-1 2-1l2-2h1 0 1v-1-1h0c1 1 2 1 3 1h2c1 0 1 1 2 2 0-1 0-1 1-1h1 0l1 2-1-1h0v-1l-1 2c-1 0-2 1-3 2l1 1 1 1c2 0 4 0 5 1h0c-2 0-4 1-6 0 0-1 0 0-1 0-4-1-7-2-11-4z" class="i"></path><path d="M133 569l-2-1h0l2-2c2 1 4 0 6 0 9 1 18 1 27 1 8-1 16-1 23 0h1v2h-1-2c-1 0-3-1-4-1s-2 1-3 0c-1 0-2 0-4 1 0-1-1-1-2-1l-1 1-2-1c-1 0-2 0-3 1-2-1-4-1-7 0h-2c-1-1-2 0-3 0-3-1-5-1-7-1-5 0-11 0-15 1h-1z" class="j"></path><path d="M144 551v-1h1 1c2 1 4 4 7 5 1 0 2 1 3 1s3 1 4 1c2 1 4 0 5 1h1 2c2 0 4 1 6 1l1 2c2-1 2-1 3-2 1 1 1 1 1 2h-1-1l-1 1v-1h-2c-1 1-1 2-2 4h0c-2 1-5 0-8 0h-23l6-6v-1c-1 0-2 1-3 1v-2h0v-2c0-1-1-2-2-3v-1h2z" class="n"></path><path d="M144 551v-1h1 1c2 1 4 4 7 5 1 0 2 1 3 1s3 1 4 1c2 1 4 0 5 1h1 2c2 0 4 1 6 1l1 2c2-1 2-1 3-2 1 1 1 1 1 2h-1-1l-1 1v-1h-2 0c-1-1-3-2-5-2h-4-1l2 2v1c-1-1-2-1-3-2l1-1-6-1c-1 0-2 0-2-1-1 0-3-1-4-1-1-1-2-2-3-2v1s1 2 2 2c1 1 3 1 4 1-1 2-2 5-4 6l-2-2v2h-2v-1l2-2h0v-1l-1-1h-1v-1c-1 0-2 1-3 1v-2h0v-2c0-1-1-2-2-3v-1h2z" class="C"></path><path d="M149 560v-2h2v1c0 1 0 2-2 2h0v-1z" class="R"></path><path d="M144 551c1 1 3 4 4 6l-1 1c-1 0-2 1-3 1v-2h0v-2c0-1-1-2-2-3v-1h2z" class="n"></path><path d="M143 329l1-1c1 2 2 4 4 5 0 1 1 1 1 2h2c1 1 1 2 2 3l2-2c0 1 0 2 1 2 0 1 1 1 1 1 1 1 2 2 3 2 1-1 1-2 1-3 0 0 1 0 1 1-1 1 0 2 0 3l3 2-1 1c0 1-1 1-2 2v2h1l3-3 3-2h0v1c2 0 2 0 4-1v-2h0l2-1c1 0 1 0 2-1h0 1v2h3l1-1c1 1 1 2 2 3 1 0 2-1 4 0l2 1c1 1 4 3 6 3l1-1h0v4c0 1 0 4-1 5l1 2c0 1 0 1 1 1l-1 2h0c1 1 1 2 1 3 1 1 2 1 3 1-1 0-2 1-3 2l1 1c1 1 2 1 3 1l-1 1c0 1-1 3-1 3l-1 2-1 1-2 1v1h0-4c-2 0-5 1-7 0l1-1c-2-1-5 1-7 0l-2-1c-1 0-2 1-4 0h0l-1 1h-1v-1l-1-1v-1h-2-4-1 0c-1-1-2-1-3-1-1 1-3 0-4 0s-1 1-2 1h0s1 1 1 2-1 1-1 3c0 0 0-1 0 0h-1v-1-1h0l-1-4h2c0-1 1-2 1-3l-2-2c0-1 1-1 1-3h-1c1-4 1-9 1-13v-10l-1-1h-2v-2c-1-1-2-1-3-1l-2-2c-1-1-1-2-2-3l1-1c-1 0-1 0-1-2l-1-1z" class="S"></path><path d="M145 332c2 2 5 4 6 6v1c-1-1-2-1-3-1l-2-2c-1-1-1-2-2-3l1-1z" class="T"></path><path d="M155 370v-1c1-1 0-3 1-4v8c-1 0-1 1-2 1h0s1 1 1 2-1 1-1 3c0 0 0-1 0 0h-1v-1-1h0l-1-4h2c0-1 1-2 1-3z" class="H"></path><path d="M162 354c0-1-1-2 0-3h3l5 1h1l-1 2h-2-2-1c0-1 0-2-1-2h-1c0 1 0 1-1 2h0z" class="f"></path><path d="M155 336c0 1 0 2 1 2 0 1 1 1 1 1 1 1 2 2 3 2v2c1 1 0 3 0 4v1l-2-1v-1h-2c-1 0-1-1-1-2 0-2-1-4-2-6l2-2z" class="V"></path><path d="M155 344c0-1 1-1 1-1v-1l2 2s1 1 2 1c1-1 0-1 0-2 1 1 0 3 0 4v1l-2-1v-1h-2c-1 0-1-1-1-2z" class="a"></path><path d="M157 350l1 2h1v-1h1v4 4h0v6c1 1 0 1 0 1v3 4h0-3c-1 0-1-1-1-1v-5c0-3 0-15 1-17z" class="W"></path><path d="M163 352h1c1 0 1 1 1 2h1 2 2l1-2c0 1 0 2 1 2v1h-1v1h-1l-1 2c1 0 1 1 1 1 0 1 0 1 1 1l1 1c1 1 1 2 1 4v1 3 1 2 4l-1 1h-1v-1l-1-1c1-1 1-1 1-2h-2v-1c-1 1-1 0-2 1h0-5c-1-3 0-5 0-7v-2-5h0v-5h0c1-1 1-1 1-2z" class="Y"></path><path d="M163 352l1 1h0c1 2 1 2 2 3v1c-1 0-1 0-2-1v-1h-2v-1c1-1 1-1 1-2z" class="T"></path><path d="M171 352c0 1 0 2 1 2v1h-1v1h-1l-1 2c1 0 1 1 1 1 0 1 0 1 1 1-1 2-1 5-1 7h0c0 1 0 4-1 5s-1 0-2 1c0-1 0-2 1-3v-1c1-1 1-1 1-2s0-2-1-3h0c0-1 1-2 1-3l-1-1h-1l1-1v-2c0-2-1-2-2-3h2 2l1-2z" class="J"></path><path d="M171 352c0 1 0 2 1 2v1h-1v1h-1s0-1-1-1l1-1 1-2z" class="X"></path><path d="M171 360l1 1c1 1 1 2 1 4v1 3 1 2 4l-1 1h-1v-1l-1-1c1-1 1-1 1-2h-2v-1c1-1 1-4 1-5h0c0-2 0-5 1-7z" class="N"></path><path d="M171 369h1c0 1 1 2 0 3-1-1-1-1-1-2v-1z" class="L"></path><path d="M163 359h1 1v1c0 1 1 2 2 3 1 2 0 4-1 6l2 1c-1 1-1 2-1 3h0-5c-1-3 0-5 0-7v-2l1-1 1-1h-1c0-1 0-1-1-2l1-1z" class="m"></path><path d="M163 359h1c0 1 1 2 0 2l-1 1c0-1 0-1-1-2l1-1z" class="o"></path><path d="M163 363v1c1 0 1-1 2 0v1c0 1-2 0-2 1v1c1 0 1 0 1 1s-1 1-1 2 0 1 1 1c0 0 2-1 2-2l2 1c-1 1-1 2-1 3h0-5c-1-3 0-5 0-7v-2l1-1z" class="Q"></path><path d="M161 338s1 0 1 1c-1 1 0 2 0 3l3 2-1 1c0 1-1 1-2 2v2h1l3-3 3-2h0v1c2 0 2 0 4-1v-2h0l2-1c1 0 1 0 2-1h0 1v2h3l1-1c1 1 1 2 2 3 1 0 2-1 4 0l2 1c1 1 4 3 6 3l1-1h0v4c0 1 0 4-1 5h-2 0c-1 1-2 1-3 1-3 0-9-1-12 0v2s0 1 1 1c-1 1-1 1-2 1l1 1h-3l-1-1-1 1-1-1h-1l-1-1c-1 0-1 0-1-1 0 0 0-1-1-1l1-2h1v-1h1v-1c-1 0-1-1-1-2h-1l-5-1c-1 0-3-1-4 0v1h0c-1-1 0-1-1-2s-2-1-2-2h-1l-1 1c-1 0-1-1-1-1l1-1h2l2 1v-1c0-1 1-3 0-4v-2c1-1 1-2 1-3z" class="b"></path><path d="M169 345h1c0 1 1 1 1 2v1h-1c-2 0-2 0-3-1l2-2z" class="P"></path><path d="M175 345c1 0 2 1 2 1v2l-1-1c-2 1-2 1-4 1v-1c1-2 1-1 1-1 1 0 2-1 2-1z" class="Y"></path><path d="M175 341c1 0 1 0 2-1h0 1v2h3l1-1c1 1 1 2 2 3 1 0 2-1 4 0l2 1-1 1c1 1 1 1 0 2h-1c-1 0-2 1-3 1l-1-1-3-1-1 1h0-3v-2s-1-1-2-1l-1-1c0-1-1-1-1-2h0l2-1z" class="W"></path><path d="M175 341l1 2s-1 1-2 1c0-1-1-1-1-2h0l2-1z" class="Q"></path><path d="M188 344l2 1-1 1c1 1 1 1 0 2h-1-2c0-2 2-2 2-4z" class="X"></path><path d="M180 348h-1c-1-1 0-1 0-2 1-1 1-1 2-1h2 1v3l-3-1-1 1z" class="P"></path><path d="M175 341c1 0 1 0 2-1h0 1v2h3l1-1c1 1 1 2 2 3-2 0-5 0-7-1v-1l-1 1-1-2z" class="Y"></path><path d="M161 338s1 0 1 1c-1 1 0 2 0 3l3 2-1 1c0 1-1 1-2 2v2h1l3-3 1 1c-1 1-1 1-1 2 1 1 2 1 3 1h11 8 1c2-1 4 0 6 0 0-1 1-1 1-2l1-1h0v4c0 1 0 4-1 5h-2 0c-1 1-2 1-3 1v-1h1c0-1 1-1 1-2v-1l-1-1-1 1c-2 0-1 0-2-1h-2v-1c-4 0-9 1-13 1-1 0-2-1-4 0l-5-1c-1 0-3-1-4 0v1h0c-1-1 0-1-1-2s-2-1-2-2h-1l-1 1c-1 0-1-1-1-1l1-1h2l2 1v-1c0-1 1-3 0-4v-2c1-1 1-2 1-3z" class="M"></path><path d="M162 342l3 2-1 1c0 1-1 1-2 2v-5z" class="Y"></path><path d="M187 351h5 5c0 1 0 4-1 5 0-1 1-3 0-4h-4 0l-1 1c-2 0-1 0-2-1h-2v-1z" class="c"></path><path d="M192 352h0 4c1 1 0 3 0 4h-2 0c-1 1-2 1-3 1v-1h1c0-1 1-1 1-2v-1l-1-1z" class="W"></path><path d="M170 352c2-1 3 0 4 0 4 0 9-1 13-1v1h2c1 1 0 1 2 1l1-1 1 1v1c0 1-1 1-1 2h-1v1c-3 0-9-1-12 0v2s0 1 1 1c-1 1-1 1-2 1l1 1h-3l-1-1-1 1-1-1h-1l-1-1c-1 0-1 0-1-1 0 0 0-1-1-1l1-2h1v-1h1v-1c-1 0-1-1-1-2h-1z" class="V"></path><path d="M183 353c1 0 2 0 3-1v2c1 0 2 1 3 2h-3l-3-3z" class="W"></path><path d="M186 356h-6c0-1-1-1-1-2h-1v-1h5l3 3z" class="X"></path><path d="M187 352h2c1 1 0 1 2 1l1-1 1 1v1c0 1-1 1-1 2h-1-2c-1-1-2-2-3-2v-2h1z" class="P"></path><path d="M172 354c0-1 0-1 1-2 2 0 3 1 4 2v1h-2v1 1c2 0 3 1 4 0v2s0 1 1 1c-1 1-1 1-2 1l-1-1-1-2c-1-1-2-1-3-1l-1-2v-1z" class="J"></path><path d="M170 356h1v-1h1l1 2c1 0 2 0 3 1l1 2 1 1 1 1h-3l-1-1-1 1-1-1h-1l-1-1c-1 0-1 0-1-1 0 0 0-1-1-1l1-2z" class="T"></path><path d="M173 361v-3c1 0 2 2 2 3l-1 1-1-1z" class="W"></path><path d="M194 356h0 2l1 2c0 1 0 1 1 1l-1 2h0c1 1 1 2 1 3 1 1 2 1 3 1-1 0-2 1-3 2l1 1c1 1 2 1 3 1l-1 1c0 1-1 3-1 3l-1 2-1 1-2 1v1h0-4c-2 0-5 1-7 0l1-1c-2-1-5 1-7 0l-2-1c-1 0-2 1-4 0h0v-4-2-1-3-1c0-2 0-3-1-4h1l1 1 1-1 1 1h3l-1-1c1 0 1 0 2-1-1 0-1-1-1-1v-2c3-1 9 0 12 0 1 0 2 0 3-1z" class="Y"></path><path d="M194 367h1c1 1 1 2 1 3 1 0 1 0 2-1l2 1h1c0 1-1 3-1 3h-4v-1 1l-1-1v-2h0c0-1 0-2-1-3z" class="Q"></path><g class="J"><path d="M196 372h0c2 0 3-1 5-2 0 1-1 3-1 3h-4v-1z"></path><path d="M193 365c1 0 3-2 4-2 0 2 1 3 1 4l1 1c1 1 2 1 3 1l-1 1h-1l-2-1c-1 1-1 1-2 1 0-1 0-2-1-3h-1 0c0-1 0-1-1-2zm-20 1h3c0 1 1 2 1 3 1 1 2 1 3 1h1v2h-1-1c-1 0-1-2-3-2 0 0-1 1-2 1v1l2-1 1 1c0 1 0 1-1 1-1 1-2 1-3 2v1h0v-4-2-1-3z"></path></g><path d="M173 366h3c0 1 1 2 1 3-1 1-2 1-4 1v-1-3z" class="f"></path><path d="M177 376h0c1 0 1 0 2-1h1c0 1 0 1 1 1l1-1 1 1h4v-1l-2-1v-1c1-1 1-1 3-1 0 2 0 3 2 3l2 2 6-1-2 1v1h0-4c-2 0-5 1-7 0l1-1c-2-1-5 1-7 0l-2-1z" class="X"></path><path d="M192 377l6-1-2 1v1h0-4c-2 0-5 1-7 0l1-1c-2-1-5 1-7 0 1-1 4 0 5 0h8z" class="S"></path><path d="M176 366h2 5 5v1c0 1 1 1 2 1h0l1 1c-1 0-2 1-2 1-2 1-6-1-8 0h-1c-1 0-2 0-3-1 0-1-1-2-1-3z" class="a"></path><path d="M178 366h5 5v1h-1c-1 1-1 1-2 1s-1 1-1 1h-2v-1c-1-1-2 0-3 0-1-1-1-1-1-2z" class="b"></path><path d="M194 356h0 2l1 2c0 1 0 1 1 1l-1 2h0c1 1 1 2 1 3 1 1 2 1 3 1-1 0-2 1-3 2 0-1-1-2-1-4-1 0-3 2-4 2 1 1 1 1 1 2-1 1-3 1-3 2l-1-1h0c-1 0-2 0-2-1v-1h-5-5-2-3v-1c0-2 0-3-1-4h1l1 1 1-1 1 1h3l-1-1c1 0 1 0 2-1-1 0-1-1-1-1v-2c3-1 9 0 12 0 1 0 2 0 3-1z" class="X"></path><path d="M193 361h0 3l-2 2-1-2z" class="W"></path><path d="M193 364h-1c-1-2-1-2 0-3h1l1 2-1 1z" class="T"></path><path d="M194 356h0 2l1 2-1 2v-1h-2v-3z" class="f"></path><path d="M173 365h5c1-1 1-1 1-2 1 1 1 2 1 2h4c0 1 0 1-1 1h-5-2-3v-1z" class="k"></path><path d="M197 358c0 1 0 1 1 1l-1 2h0c1 1 1 2 1 3 1 1 2 1 3 1-1 0-2 1-3 2 0-1-1-2-1-4-1 0-3 2-4 2 1 1 1 1 1 2-1 1-3 1-3 2l-1-1h0c-1 0-2 0-2-1v-1h-5c1 0 1 0 1-1 3 0 7 1 9-1l1-1 2-2v-1l1-2z" class="Z"></path><path d="M193 365c1 1 1 1 1 2-1 1-3 1-3 2l-1-1h0c-1 0-2 0-2-1v-1h1c2 0 3 0 4-1z" class="c"></path><path d="M188 366h1l1 1v1c-1 0-2 0-2-1v-1z" class="W"></path><defs><linearGradient id="Q" x1="395.1" y1="249.688" x2="420.108" y2="228.252" xlink:href="#B"><stop offset="0" stop-color="#09090a"></stop><stop offset="1" stop-color="#3b3a3a"></stop></linearGradient></defs><path fill="url(#Q)" d="M413 194l2-2c0 1 1 2 0 3l2 2c-2 1-3 2-4 3 1 2 4 1 4 3 1 0 1 2 1 3v11 8 2 8 4 41c-1-4-3-8-5-12-9-18-23-33-37-48 0 0 1 0 2-1 1 0 3 0 4-1 1 0 1 0 2-1h0l1 1 1-1h2 0-1c2-2 5-4 7-6l1 1c-1 0-1 1-2 1v1h0l1-1 1 1c2-1 4-2 6-4l1-1h0c1 0 2-1 3-2l1-1c1-1 3-2 4-4-1 0-3 0-4-1l-1 1h-1l6-6 3-2z"></path><path d="M401 222l-2 2h0l-1-1 2-2 1 1z" class="U"></path><path d="M395 218l-3 1v-3h0l3 2h0z" class="B"></path><path d="M395 218l-1-3c1 0 1-1 2-1 2 0 3 0 4 1h0-1c-1-1-2-1-3 0-1 0-1 0-1 1s2 2 3 4h0l-1 1c-1 0-1-1-2-1v-2h0z" class="O"></path><path d="M398 220c-1-2-3-3-3-4s0-1 1-1c1-1 2-1 3 0v2c-1 1-1 2-1 3z" class="D"></path><path d="M413 232h2l3 3v4c-1-2-1-3-2-4h-2-1c-1-1-1-1-2-1v-1h2v-1z" class="h"></path><path d="M399 215h1 0l-1 1c1 0 1 1 2 1 0 1 0 2 1 3l-1 2h0l-1-1-2-1h0c0-1 0-2 1-3v-2z" class="L"></path><path d="M400 215l4 3h0c2 1 2 2 4 3 0 1 1 1 2 2l3 3-2 2v1h-1l-1-1c-3-2-5-4-8-6l1-2c-1-1-1-2-1-3-1 0-1-1-2-1l1-1z" class="e"></path><path d="M411 228c0-1-1-2-1-2-1-1-2-1-2-1 0-1 0-2-1-2h-1c-1-1-1-1-1-2-1-1-2-1-2-1h-1v-3l2 1h0c2 1 2 2 4 3 0 1 1 1 2 2l3 3-2 2z" class="M"></path><path d="M413 235s-1 1-2 1h-1v1c-3 3 2 9 1 13h-1l-3-13-1-3c-1 0-1 1-1 1h-1l2-2v1l1-1v-1l-1-1h-1l-1-1h-2l-1-1v-1c2 0 2-1 4-1l1 2h0 1l2-1 1 1h1 0v3h2v1h-2v1c1 0 1 0 2 1z" class="R"></path><g class="L"><path d="M409 228l1 1h1 0v3h-2c-1 0-1-1-2-2v-1l2-1z"></path><path d="M409 215v1c1-1 3-1 4-1v1c1 1 0 1 1 1h4v8 2 8l-3-3h-2-2v-3h0v-1l2-2-3-3c-1-1-2-1-2-2-2-1-2-2-4-3l2-1h0v-2h2 1z"></path></g><path d="M409 215v1c-1 1-2 1-3 1v-2h2 1z" class="d"></path><path d="M404 218l2-1c0 1 1 2 1 2h1l1-1h0c0 3 0 2 1 4 1 0 1 1 2 1h-2c-1-1-2-1-2-2-2-1-2-2-4-3z" class="R"></path><path d="M412 223l1-1c1 1 0 2 1 4h1c0-1 0-1 1-2v-1l1-1c1 1 1 2 1 3v2 3c-2 0-4-3-5-4l-3-3h2z" class="E"></path><path d="M413 226c1 1 3 4 5 4v-3 8l-3-3h-2-2v-3h0v-1l2-2z" class="H"></path><path d="M411 229c2 1 3 2 4 3h-2-2v-3z" class="I"></path><path d="M413 194l2-2c0 1 1 2 0 3l2 2c-2 1-3 2-4 3 1 2 4 1 4 3 1 0 1 2 1 3v11h-4c-1 0 0 0-1-1v-1c-1 0-3 0-4 1v-1h-1-2v2h0l-2 1h0l-4-3h0 0c1 0 1 0 2 1v-2l-1-1h-1c1-1 1-2 2-3 1 0 1 0 2-1h-2 0c1 0 2-1 3-2l1-1c1-1 3-2 4-4-1 0-3 0-4-1l-1 1h-1l6-6 3-2z" class="L"></path><path d="M410 204l2 1h0c0 1-1 1-2 1h-1-1l2-2z" class="I"></path><path d="M402 211l2-1h3c-1 1-1 1-2 1l1 1v2l2 1h-2l-1-1v-1c-1-1-1-1-2-1h-1v-1z" class="D"></path><path d="M401 213c0-1 1-1 1-2v1h1c1 0 1 0 2 1v1l1 1v2h0l-2 1h0l-4-3h0 0c1 0 1 0 2 1v-2l-1-1z" class="K"></path><path d="M401 213c0-1 1-1 1-2v1h1c1 0 1 0 2 1v1c0 1 0 1-1 2h0-1 0v-2-1h-2z" class="B"></path><path d="M415 195l2 2c-2 1-3 2-4 3l-2 3-1 1-2 2-4 4-2 1c0 1-1 1-1 2h-1c1-1 1-2 2-3 1 0 1 0 2-1h-2 0c1 0 2-1 3-2l1-1c1-1 3-2 4-4l3-5 2-2z" class="U"></path><path d="M413 194l2-2c0 1 1 2 0 3l-2 2-3 5c-1 0-3 0-4-1l-1 1h-1l6-6 3-2z" class="i"></path><path d="M406 201c1-1 5-4 6-4h1l-3 5c-1 0-3 0-4-1z" class="F"></path><path d="M413 200c1 2 4 1 4 3 1 0 1 2 1 3v11h-4c-1 0 0 0-1-1v-1c-1 0-3 0-4 1v-1-1l1-1c0-1-1-2-1-3 1-1 1-2 2-2s1 2 1 2l1 1c0-2-1-5 0-7h0 0l-2-1 2-3z" class="I"></path><path d="M410 213h4v1s-1 0-1 1c-1 0-3 0-4 1v-1-1l1-1z" class="B"></path><path d="M162 259h1 0c1 0 1 0 2 1v1h2c1 1 2 1 3 1s1 1 2 1l-1 1c-1 0-2 0-2 1s0 1 1 2 1 5 1 7l-1 1 1 1v2l1-1 1 1v2h1v1h5 1 0c2 0 2 1 3 2h2s0-2 1 0l1 2c2-1 4-3 6-4 1 0 2-1 3-2h2c0-1 1-2 1-3 1-1 1-2 1-3v-1c0-1 0-1 1-2h2 0v6 1c1 0 1-1 1-1 0 1 1 2 0 3v1 8 1 2h-1v1c0 1-1 1 0 1 0 2 0 3-1 4v-3c-1-1-3-1-5-1h-7 0-3c0-1-1-1-2-1-2 0-3 0-4 1v1 2 2h0-3l-1-1v-1-1l-2-1c-1-1-2-1-3-1l-1 2h-1l-1 3c-1 0-1-1-1-1h-1c-1 0-1 1-1 1l2 2v2h0c0 2-2 2-2 4-1 1-1 1-1 2-1 1-1 2-1 3s-1 2-2 3l1 1h1 0v1l-2 1v1l1 2-1 1h-1l-1-1h-1v-2c0-1-1-2-2-2-1-2-3-4-5-6l-3-3-1-2c-1 0-1-1-2-2l-2-2h0v-1c1-1 0-1 0-1 1-1 0-2 0-3v-2c1-1 1-2 1-3v-3-1-3c0-1 0-2 1-2h1 0v-1h-1v-1h-1-2v-1l1-1 4-1c2-1 4-1 6-2h0c1-1 2-1 2-2l1-1 1-1c0-1 0-1-1-2h0v-2c0-1 0-2 1-3h1l1-1v-2c0-1 1-2 2-2z" class="H"></path><path d="M159 281c1 0 2 0 2 1 1 1 1 3 1 4h-2v-1l-1-1v-3h0z" class="B"></path><path d="M157 272l1-1h1l1 1 1 1c0 1 0 1-1 1 1 1 1 1 1 2-1 1-3 1-4 1 0 0 1-1 1-2 1 0 1 0 1-1-1 0-1 0-1-1l-1-1z" class="c"></path><path d="M162 278c0 1 1 2 0 4v3l1 1c0 1-1 3-1 4l2 2v1l-2 2h2c1 2 0 3 1 4-1 1-1 2-2 2l1 1h-1-1v-2-3c1-2 0-6 0-8v-11z" class="V"></path><path d="M164 292l1-1c1 0 1 0 2 1 0 1 0 1-1 2l2 1-1 2c-1 0-1 1-1 1l2 2v2h-1l-2-1h0v1s0 1 1 1c0 1 0 1-1 2v-1c-1-1-1-1-1-2l-1-1c1 0 1-1 2-2-1-1 0-2-1-4h-2l2-2v-1z" class="Y"></path><path d="M162 259h1 0c-1 1-1 2-1 4h0c-1 2 0 5-1 6l-2 2h-1c0-1 0-1-1-2h0v-2c0-1 0-2 1-3h1l1-1v-2c0-1 1-2 2-2z" class="L"></path><path d="M158 264l1 1v2c1 1 2 1 2 2l-2 2h-1c0-1 0-1-1-2h0v-2c0-1 0-2 1-3z" class="E"></path><path d="M159 281v3l1 1v1h2c-1 2-1 3 0 5 0 1-1 5 0 6v3h-1c-1-1-1-2-3-2v-1h2v-1c-1-1-2-3-3-5l1-1v-1c-2-2-2-2-2-4h0c2-2 2-2 3-4z" class="K"></path><path d="M159 281v3l1 1v1l-2-1v4c-2-2-2-2-2-4h0c2-2 2-2 3-4z" class="N"></path><g class="W"><path d="M162 274s0 1 1 1c0 1 2 0 1 2v1l1 1v1c0 1 0 2 1 3-1 1-1 2-1 3l-1 1 1 1h1c0 1 0 1 1 1l1 1h1c0 1 1 2 2 2 0 1 0 2-1 3l-1 3c-1 0-1-1-1-1h-1l1-2-2-1c1-1 1-1 1-2-1-1-1-1-2-1l-1 1-2-2c0-1 1-3 1-4l-1-1v-3c1-2 0-3 0-4v-4z"></path><path d="M163 259c1 0 1 0 2 1v1h2c1 1 2 1 3 1s1 1 2 1l-1 1c-1 0-2 0-2 1s0 1 1 2 1 5 1 7l-1 1 1 1v5c0-1-1-2-1-3l-1 1s0 1-1 1v-1 3c-1 1-1 3-1 5-1 0-2-1-2-1 0-1 0-2 1-3-1-1-1-2-1-3v-1l-1-1v-1c1-2-1-1-1-2-1 0-1-1-1-1v-4-1-6h0c0-2 0-3 1-4z"></path></g><path d="M168 271c0 1 1 1 1 2l-2 1v-1c0-1 0-1 1-2z" class="b"></path><path d="M167 274l2-1c1 1 1 1 2 1l-1 1-1 1-2-2z" class="P"></path><path d="M167 261c1 1 2 1 3 1s1 1 2 1l-1 1c-1 0-2 0-2 1s0 1 1 2h-2-1c0-1 0-2 1-3 0-1 0-2-1-3z" class="a"></path><path d="M168 267h2c1 1 1 5 1 7-1 0-1 0-2-1 0-1-1-1-1-2h-1v-3s1 0 1-1h0z" class="J"></path><path d="M163 259c1 0 1 0 2 1v1c1 1 1 2 2 3v4l-2-2v-2l-1-1-1 1c0-1-1-1-1-1 0-2 0-3 1-4z" class="X"></path><path d="M162 270c0-1 1-1 1-1 0-1 0-3 1-3 1 2 1 4 2 7 0 0 0 1-1 1v1c1 1 1 1 2 1v1c0 1 0 1 1 2v3c-1 1-1 3-1 5-1 0-2-1-2-1 0-1 0-2 1-3-1-1-1-2-1-3v-1l-1-1v-1c1-2-1-1-1-2-1 0-1-1-1-1v-4z" class="P"></path><path d="M157 272l1 1c0 1 0 1 1 1 0 1 0 1-1 1 0 1-1 2-1 2v1 1c1 0 1 0 2-1h1l1 1-2 2h0c-1 2-1 2-3 4h0c0 2 0 2 2 4v1l-1 1v-1h-2c1 2 1 2 0 3h-2v1c-1-1-1-1-2-1l-1 1h0v-3h-2l-1 1c-1 0-2 0-2-1v-3-1-3c0-1 0-2 1-2h1 0v-1h-1v-1h-1-2v-1l1-1 4-1c2-1 4-1 6-2h0c1-1 2-1 2-2l1-1z" class="b"></path><path d="M153 287h-1 0c0-1 1-2 1-3h-1v-3h1 1c0 1 0 2-1 3 0 1 0 1 1 2h0l-1 1z" class="J"></path><path d="M154 275l1 4h-1 0c0-1-1-1-2-2l-2 2v1l-1-1c-1 0-2 0-3 1h-1-2v-1l1-1 4-1c2-1 4-1 6-2h0zm0 11v-2l2 1h0c0 2 0 2 2 4v1l-1 1v-1h-2c1 2 1 2 0 3h-2v1c-1-1-1-1-2-1v-1-4h2v-1l1-1h0z" class="V"></path><path d="M156 285c0 2 0 2 2 4v1l-1 1v-1h-2c1-1 0-3 0-4l1-1z" class="I"></path><path d="M153 287l1-1c0 2-1 4-1 6h-2v-4h2v-1z" class="c"></path><path d="M146 280c1-1 2-1 3-1l1 1v11h-2l-1 1c-1 0-2 0-2-1v-3-1-3c0-1 0-2 1-2h1 0v-1h-1v-1z" class="I"></path><path d="M145 287h1 2v1l-1 1c-1 0-1 0-2-1v-1z" class="H"></path><path d="M147 282l1-1h1 0c-1 1-1 2-3 2h0c0 1 0 1 1 1s1 0 2 1v1l-3 1h-1v-3c0-1 0-2 1-2h1 0z" class="R"></path><path d="M171 276v2l1-1 1 1v2h1v1h5 1 0c2 0 2 1 3 2h2s0-2 1 0l1 2-5 2-6 1v1c2 0 3-1 4-1h2l1 2c0 1-1 1-2 2 0 0-1 0-1 1v1l1-1v1 2 2h0-3l-1-1v-1-1l-2-1c-1-1-2-1-3-1l-1 2h-1c1-1 1-2 1-3-1 0-2-1-2-2h-1l-1-1c-1 0-1 0-1-1h-1l-1-1 1-1s1 1 2 1c0-2 0-4 1-5v-3 1c1 0 1-1 1-1l1-1c0 1 1 2 1 3v-5z" class="X"></path><path d="M169 286c1 0 1 0 2 1h0l-2 1v-1-1z" class="T"></path><path d="M171 276v2l1-1 1 1v2h1v1h-2v2s0 1 1 1l-1 1v1l2 2 1-1c1 0 1 0 1 1v1c-1 1-2 1-3 2v1c1 0 2 1 3 1 0 1 1 1 1 2l-2-1c-1-1-2-1-3-1l-1 2h-1c1-1 1-2 1-3-1 0-2-1-2-2 1 1 2 1 3 1v-3-2c-1-2-1-3-1-5v-5z" class="Z"></path><path d="M182 288l1 2c0 1-1 1-2 2 0 0-1 0-1 1v1l1-1v1 2 2h0-3l-1-1v-1-1c0-1-1-1-1-2-1 0-2-1-3-1v-1c1-1 2-1 3-2 2 0 3-1 4-1h2z" class="b"></path><path d="M176 293l1-1h1v1 2 1h1 0l1-1h0 1v-1 2 2h0-3l-1-1v-1-1c0-1-1-1-1-2z" class="W"></path><path d="M176 288c0-1 0-1-1-1l-1 1-2-2v-1l1-1c-1 0-1-1-1-1v-2h2 5 1 0c2 0 2 1 3 2h2s0-2 1 0l1 2-5 2-6 1z" class="X"></path><path d="M176 288c0-1 0-1-1-1l-1 1-2-2v-1l1-1c-1 0-1-1-1-1v-2h2 5 1v1c-2 1-4 0-6 1v1c1 1 2 1 2 2h1l1-1h2 1s0 1 1 2l-6 1z" class="W"></path><path d="M199 276c1-1 1-2 1-3v-1c0-1 0-1 1-2h2 0v6 1c1 0 1-1 1-1 0 1 1 2 0 3v1 8 1 2h-1v1c0 1-1 1 0 1 0 2 0 3-1 4v-3c-1-1-3-1-5-1h-7 0-3c0-1-1-1-2-1-2 0-3 0-4 1l-1 1v-1c0-1 1-1 1-1 1-1 2-1 2-2l-1-2h-2c-1 0-2 1-4 1v-1l6-1 5-2c2-1 4-3 6-4 1 0 2-1 3-2h2c0-1 1-2 1-3z" class="Q"></path><path d="M191 286c1-2 3-3 5-5 0 2 0 3-1 4h0c-1 2-3 3-4 3h-1v1c-1 0-1 0-1-1 1-1 1-2 2-2z" class="Y"></path><path d="M184 288l1-1 1-1h1 4c-1 0-1 1-2 2 0 1 0 1 1 1 0 1 3 2 4 2h1c1 1 2 0 3 0v1h5c0 1-1 1 0 1 0 2 0 3-1 4v-3c-1-1-3-1-5-1h-7 0-3c0-1-1-1-2-1-2 0-3 0-4 1l-1 1v-1c0-1 1-1 1-1 1-1 2-1 2-2 2 0 2 0 3-1l-2-1z" class="g"></path><path d="M184 288l1-1 1-1h1 4c-1 0-1 1-2 2 0 1 0 1 1 1 0 1 3 2 4 2h1c1 1 2 0 3 0v1h0c-3 0-8 0-10-1l1-2-1-1c-1 0-1 0-2 1l-2-1z" class="c"></path><path d="M199 276c1-1 1-2 1-3v-1c0-1 0-1 1-2h2 0v6 1c1 0 1-1 1-1 0 1 1 2 0 3v1 8 1 2h-1v-6-5l-1 1h-1v-2h2v-1h-1c-1 1-4 3-6 3h0c-2 2-4 3-5 5h-4-1l-1 1-1 1 2 1c-1 1-1 1-3 1l-1-2h-2c-1 0-2 1-4 1v-1l6-1 5-2c2-1 4-3 6-4 1 0 2-1 3-2h2c0-1 1-2 1-3z" class="G"></path><path d="M182 288h0c1-1 1-1 2 0l2 1c-1 1-1 1-3 1l-1-2z" class="W"></path><path d="M199 276c1-1 1-2 1-3v-1c0-1 0-1 1-2 1 2 1 1 1 3l-1 1s0 1 1 2l-2 2c-1 0-1 1-2 1 0-1 1-2 1-3z" class="V"></path><path d="M155 290h2v1c1 2 2 4 3 5v1h-2v1c2 0 2 1 3 2h1v2h1 1c0 1 0 1 1 2v1c1-1 1-1 1-2-1 0-1-1-1-1v-1h0l2 1h1 0c0 2-2 2-2 4-1 1-1 1-1 2-1 1-1 2-1 3s-1 2-2 3l1 1h1 0v1l-2 1v1l1 2-1 1h-1l-1-1h-1v-2c0-1-1-2-2-2-1-2-3-4-5-6l-3-3-1-2c-1 0-1-1-2-2l-2-2h0v-1c1-1 0-1 0-1 1-1 0-2 0-3v-2c1-1 1-2 1-3 0 1 1 1 2 1l1-1h2v3h0l1-1c1 0 1 0 2 1v-1h2c1-1 1-1 0-3z" class="M"></path><path d="M150 294h0v2 1-1h-4v-1c1 0 1-1 2-1h1 1z" class="G"></path><path d="M161 316l1 2h0l1 2-1 1h-1l-1-1 1-4z" class="F"></path><path d="M161 312v-1c1 2 1 2 1 3l1 1h1 0v1l-2 1v1h0l-1-2v-4z" class="N"></path><path d="M145 291c0 1 1 1 2 1l1-1h2v3h-1-1c-1 0-1 1-2 1v1h-2v-2c1-1 1-2 1-3z" class="H"></path><path d="M150 302h4v1c1 0 2 1 3 2l1 1c1 1 2 1 2 2v2 1l1 1-1 1c1 1 0 3 0 5v-1l-5-5c-1-2-2-4-3-5 0-2-1-4-1-5h-1z" class="a"></path><path d="M157 311h1c1-1 1-1 2 0l1 1-1 1c-1 0-2-1-3-2z" class="m"></path><path d="M155 308l-1-3s-1 0-1-1v-1h1c1 0 2 1 3 2v2c0 1 0 1-1 1h-1z" class="T"></path><path d="M157 305l1 1c1 1 2 1 2 2v2 1c-1-1-1-1-2 0h-1c-1-1-2-1-2-3h1c1 0 1 0 1-1v-2z" class="f"></path><path d="M157 305l1 1c1 1 2 1 2 2v1c-2 0-3 0-4-1 1 0 1 0 1-1v-2z" class="X"></path><path d="M155 290h2v1c1 2 2 4 3 5v1h-2v1c2 0 2 1 3 2h1v2h1 1c0 1 0 1 1 2v1c1-1 1-1 1-2-1 0-1-1-1-1v-1h0l2 1h1 0c0 2-2 2-2 4-1 1-1 1-1 2-1 1-1 2-1 3s-1 2-2 3c0-1 0-1-1-3v1h0l-1-1v-1-2c0-1-1-1-2-2l-1-1c-1-1-2-2-3-2v-1h-4v-5-1-2l1-1c1 0 1 0 2 1v-1h2c1-1 1-1 0-3z" class="g"></path><path d="M161 300h1v2c0 2 1 5 0 8h-2v-2c0-1-1-1-2-2v-1c1 1 1 1 2 1 0-1 1-2 0-3h-1l-1-2c1 0 1 0 2 1h0l1-1c-1 0-1 0-1-1h1z" class="i"></path><path d="M155 295l2 3v1l1 1v1l1 2h1c1 1 0 2 0 3-1 0-1 0-2-1v1l-1-1c-1-1-2-2-3-2v-1c1 0 1-1 2-1 0-2 0-4-1-6z" class="k"></path><path d="M155 290h2v1c1 2 2 4 3 5v1h-2v1c2 0 2 1 3 2h-1c0 1 0 1 1 1l-1 1h0c-1-1-1-1-2-1v-1l-1-1v-1l-2-3v-1h-2v-1h2c1-1 1-1 0-3z" class="S"></path><path d="M151 293c1 0 1 0 2 1h2v1c1 2 1 4 1 6-1 0-1 1-2 1h-4v-5-1-2l1-1z" class="V"></path><path d="M151 293c1 0 1 0 2 1v1c-1 1-2 1-3 1v-2l1-1z" class="J"></path><path d="M153 294h2v1c1 2 1 4 1 6-1 0-1 1-2 1 1-1 1-3 1-4-1-1-1-2-2-3v-1z" class="a"></path><defs><linearGradient id="R" x1="228.706" y1="615.414" x2="284.255" y2="635.245" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2a292a"></stop></linearGradient></defs><path fill="url(#R)" d="M242 594c1 0 1-2 1-3 0 1 0 2 1 2v2h0c0 1 1 1 1 1l1-1s2 0 2-1l1 2 3 3c1 1 3 4 4 4 1-1 2-1 4 0l1 2h4c4 0 6 4 9 6h0l3 3 1 1c-1 0-1 0 0 1h0v2c1 1 1 2 3 2h0 0c1 3 2 5 2 7 0 1 0 1-1 1l3 2c-3 1-5 2-7 4-1 1-3 2-4 2 2 1 1 2 3 3 1 0 2 1 3 1h2 0c-1 0-1 1 0 1v2c-2 0-3-2-6-2 0 0-1 0-2-1-1 0-2-1-4-1-1 0 0 1-1 2v-3c-1 0-4-2-5-2h-1l-1 1-2-2c-5-4-11-6-16-9-2-1-5-3-7-4 0 2 0 7-1 8 0 1 0 1-1 1s-1 1-2 0h-1l-1 22c-1-3-1-5-1-7l-1-1c-2 2-1 4-1 7-1-2-1-3-1-5v-5l1-14v-8h0l-2-2c-1 0-1-1-2-1v-1h1c1-1 1-2 2-3 0-1 1-1 1-1h1l-1-2h2v-1-1l1-1v-1h0c1 0 2-1 3-2v-2h1v1h2c1-1 1-2 2-3 0-2 1-3 2-4l1-2z"></path><path d="M258 621c1 0 2 1 2 1-1 1-1 1-2 1h0v-2z" class="E"></path><path d="M265 633h1 2l-1 2-1 1c0-1-1-2-1-3z" class="R"></path><path d="M258 619h1c1 0 2 1 2 2v1h-1s-1-1-2-1l1-1-1-1z" class="O"></path><path d="M267 627h0c0 1 1 2 1 3h2c-2 1-3 1-4 0v-1-2h1z" class="E"></path><path d="M248 616c1 0 1 0 2-1 0 2 1 3 0 4l-1 1-1-1v-3z" class="U"></path><path d="M254 616h1s1 2 2 2 1 0 2 1h-1l-1-1c-1 1-2 2-3 2l-1-1 1-2v-1z" class="K"></path><path d="M244 616c1-1 2-2 2-3h1v2l1 1v3h-1l-3-3z" class="j"></path><path d="M235 631v-2c0-1 0-2-1-3v-1c0-1 0-1 1-2h1l1-1c0 2 0 7-1 8 0 1 0 1-1 1z" class="O"></path><path d="M234 602h1v1h2l-3 7c-1 2-2 6-5 7l-1 1h-2c-1 0-1-1-2-1v-1h1c1-1 1-2 2-3 0-1 1-1 1-1h1l-1-2h2v-1-1l1-1v-1h0c1 0 2-1 3-2v-2z" class="E"></path><path d="M231 607c2-1 3-1 3-2h1c0 2-1 5-3 5v1l-1-1c-1 0-1-1-1-1v-1l1-1z" class="j"></path><path d="M230 609s0 1 1 1l1 1h0c-1 0-1 0-2 1h0l1 1c0 1-1 2-2 3 0 0-1 0-2 1l1 1h-2c-1 0-1-1-2-1v-1h1c1-1 1-2 2-3 0-1 1-1 1-1h1l-1-2h2v-1z" class="U"></path><path d="M241 605c1 0 2 1 2 2h1l3 3h0c1 1 3 2 3 4h1l-1 1c-1 1-1 1-2 1l-1-1v-2h-1c0 1-1 2-2 3h0c-2-2-5-3-7-6h0c0-2 1-4 3-4h0l1 1v-2z" class="C"></path><path d="M237 610c2 0 4-1 6 0h1c0 1 0 1 1 1v1c-1 0-1 1-1 1v3c-2-2-5-3-7-6z" class="O"></path><path d="M244 595c0 1 1 1 1 1l1-1s2 0 2-1l1 2 3 3c1 1 3 4 4 4 1-1 2-1 4 0l1 2h4c4 0 6 4 9 6h0l3 3 1 1c-1 0-1 0 0 1h0v2c1 1 1 2 3 2h0 0c1 3 2 5 2 7 0 1 0 1-1 1l3 2c-3 1-5 2-7 4-1 1-3 2-4 2h-2-1l1-1h2v-1l-1-1h-1c-2 0-2-2-2-3h-2c0-1-1-2-1-3h0c-1 0-2-1-2-1-1-2-2-3-4-4v-1c0-1-1-2-2-2-1-1-1-1-2-1s-2-2-2-2h-1c-1 0-2-1-3-2h-1c0-2-2-3-3-4h0l-3-3h-1c0-1-1-2-2-2l1-1v-1c0-1-1-2-2-3h-1c1-1 1-2 2-3h1 0c1 0 1-1 2-1v-1z" class="D"></path><path d="M269 620h1l1 1v2h-1c-1-1-1-1-1-3z" class="K"></path><path d="M257 608l3 3h-2l-1-1c-1-1 0-1 0-2z" class="C"></path><path d="M256 610h1l1 1h2c0 1 1 2 1 2h-3s0 1-1 1h-2l1-4z" class="B"></path><path d="M262 609l2 2 2 2 2 3h-1c0 2 1 2 2 3l-1 1h-3c0-1 0-2-1-2l-2 1h-1v-2-3l1-1v-4z" class="L"></path><path d="M263 613h1v1c1 1 1 1 1 2h-1c-1 0-1 0-2-1 0-1 0-1 1-2z" class="B"></path><path d="M266 613v-3-1c1 1 2 2 2 3 2 1 4 2 5 4l1 1v1h1l2-1 1 1c1 1 1 2 3 2h0 0c1 3 2 5 2 7 0 1 0 1-1 1-4-3-8-6-11-10l-3-2-2-3z" class="T"></path><path d="M266 613v-3-1c1 1 2 2 2 3v1c2 0 3 1 4 3h-1l-2-2h-1l2 2c0 1 1 1 1 2h0l-3-2-2-3z" class="Q"></path><path d="M256 603c1-1 2-1 4 0l1 2h4c4 0 6 4 9 6h0l3 3 1 1c-1 0-1 0 0 1h0v2l-1-1-2 1h-1v-1l-1-1c-1-2-3-3-5-4 0-1-1-2-2-3v1 3l-2-2-2-2-2-2-2-2-2-2z" class="V"></path><path d="M260 607c0-1 1-1 2-1l2 1v2 1 1l-2-2-2-2z" class="P"></path><path d="M256 603c1-1 2-1 4 0 1 2 0 2 2 3h0c-1 0-2 0-2 1l-2-2-2-2zm11 5c2 0 3 2 6 3 0 1 0 2 1 2 0 1 1 0 2 2 0 1-1 1 0 2l2-1h0v2l-1-1-2 1h-1c0-1 1-1 1-2 0-2-3-3-5-4-2-2-3-2-3-4z" class="J"></path><path d="M264 610l2-1-1-1 1-1 1 1c0 2 1 2 3 4 2 1 5 2 5 4 0 1-1 1-1 2v-1l-1-1c-1-2-3-3-5-4 0-1-1-2-2-3v1 3l-2-2v-1z" class="X"></path><path d="M244 595c0 1 1 1 1 1l1-1s2 0 2-1l1 2 3 3c1 1 3 4 4 4l2 2-2 2 1 1c0 1-1 1 0 2h-1l-1 4c0 1 0 1-1 1-2-1-3-3-5-4-1-2-2-3-4-4 0 0-1-3-2-3h-1v-1c0-1-1-2-2-3h-1c1-1 1-2 2-3h1 0c1 0 1-1 2-1v-1z" class="C"></path><path d="M242 604v-1c0-1-1-2-2-3h-1c1-1 1-2 2-3h1l-1 1c0 1-1 1-1 2h0 1s1 0 1 1h2c1 1 1 4 4 4 0 2 2 3 2 4l-1 2c-1-2-2-3-4-4 0 0-1-3-2-3h-1z" class="O"></path><path d="M244 595c0 1 1 1 1 1l1-1s2 0 2-1l1 2 3 3c1 1 3 4 4 4l2 2-2 2 1 1c0 1-1 1 0 2h-1-1c0-1 0-1-1-1 0-1-1-1-1-2 0-2-1-2-1-4h0c-1-1-1-1-2-1s-1-1-1-1h-1c-1 0-1-1-2-1l-1-1h-2 0c0-1 1-1 1-2h-2c1 0 1-1 2-1v-1z" class="K"></path><path d="M244 595c0 1 1 1 1 1l1-1s2 0 2-1l1 2-1 1h-1l-1-1-1 1c0 1 1 1 1 1h2l1 3h-1c-1 0-1-1-2-1l-1-1h-2 0c0-1 1-1 1-2h-2c1 0 1-1 2-1v-1z" class="U"></path><path d="M191 241v-1c4-4 3-8 4-13h0c0 2-1 5 1 7l-1 3c2-1 2 0 4 0v3l-1 2h0v2h1 1c1 1 2 2 3 2v4l-1 1c1 2 1 4 1 6v6 7h0-2c-1 1-1 1-1 2v1c0 1 0 2-1 3 0 1-1 2-1 3h-2c-1 1-2 2-3 2-2 1-4 3-6 4l-1-2c-1-2-1 0-1 0h-2c-1-1-1-2-3-2h0-1-5v-1h-1v-2l-1-1-1 1v-2l-1-1 1-1c0-2 0-6-1-7s-1-1-1-2 1-1 2-1l1-1c-1 0-1-1-2-1s-2 0-3-1h-2v-1c-1-1-1-1-2-1h0v-3c-1-1 0-2 1-3l-2-1 1-1v-2h0c1-1 0-3 0-4v-2h-4v-1h0c0-1-1-2-1-2v-3l-1-1c1 0 2 0 2-1h5 8v-6l2 1 1 1v3c1 0 1 0 1 1 0 0-1 0-1 1v1h1c1 0 2 1 2 1h1c0 1 1 2 2 2v-1l3-3c1-1 1-1 1-2h3l1-1 1 3h0 1 0c0 1 0 2-1 3 1 0 1 1 1 2z" class="D"></path><path d="M180 272v-2h0v-1c1 0 1 1 2 1 0 0 1 0 1 1l1-1v2c-1 0-1 1-1 2h-1v-1c-1 0-1 0-1-1h-1z" class="Z"></path><path d="M181 267l2-1v1c1 1 1 2 1 2h1c0 1 1 3 1 4v1h-3c0-1 0-2 1-2v-2l-1 1v-2l-2-2z" class="G"></path><path d="M185 243c0 2 0 2 1 4 1 1 1 2 1 4-1 1-1 3-1 4 1 1 1 1 1 2-2 3 1 7-3 9h-1l-2 1c-1 0-2 1-2 2v1h0-1v-2-1h1c2-1 4-2 6-4v-5-5-10z" class="g"></path><path d="M187 257l1 1h-1c1 1 1 2 2 3v1l2-1c0 1 0 1 1 2h1c-1 2-2 2-2 4l1 1h0v1 1h0c-1 0-1 1-1 2l-1 1v2h0-2v1l-1-1h-1v-1-1c0-1-1-3-1-4h-1s0-1-1-2v-1h1c4-2 1-6 3-9z" class="I"></path><path d="M183 266c1 0 1 0 2 1h1l1 1v1s-1 0-1-1l-1 1h-1s0-1-1-2v-1z" class="F"></path><path d="M187 258c1 1 1 2 2 3v2h-2c-1-2-1-4 0-5z" class="D"></path><path d="M187 268l1-1h2v1h-1l2 2v-1l1-1h0v1 1h0c-1 0-1 1-1 2l-1 1v2h0-2v1l-1-1h-1v-1-1c0-1-1-3-1-4l1-1c0 1 1 1 1 1v-1z" class="k"></path><path d="M186 273h1c1 1 1 1 1 2v1l-1-1h-1v-1-1z" class="V"></path><path d="M163 256h2c1 1 1 1 2 1 1-1 1-1 2-1v2h2c1 1 1 1 2 1 0 1 0 2 1 3 0 1-1 2 0 3v1l1-1v4h2v1h0c-2 0-2 0-2 2h0c1 0 2 0 3-1h1l1 1h0 1c0 1 0 1 1 1v1h1 3v1h1l1 1v2c0 1-3 3-5 3l-1-1c-1 0-1 1-2 1h0-1-5v-1h-1v-2l-1-1-1 1v-2l-1-1 1-1c0-2 0-6-1-7s-1-1-1-2 1-1 2-1l1-1c-1 0-1-1-2-1s-2 0-3-1h-2v-1c-1-1-1-1-2-1h0v-3z" class="H"></path><path d="M163 256h2c1 1 1 1 2 1 1-1 1-1 2-1v2h2c1 1 1 1 2 1 0 1 0 2 1 3 0 1-1 2 0 3 0 0 0 1-1 1v1 3l-2-1c0-1 1-1 1-2-1-1-1-2-1-3l1-1c-1 0-1-1-2-1s-2 0-3-1h-2v-1c-1-1-1-1-2-1h0v-3z" class="d"></path><path d="M163 256h2c1 1 1 1 2 1v3 1c1-1 1-2 2-2h1v3c-1 0-2 0-3-1h-2v-1c-1-1-1-1-2-1h0v-3z" class="G"></path><path d="M178 271h1l1 1h0 1c0 1 0 1 1 1v1h1 3v1h1l1 1v2c0 1-3 3-5 3l-1-1c-1 0-1 1-2 1h0-1-5v-1l1-1h-1c0-2 0-5 1-6 0 0 0 1 1 1v-1h1 1v-2z" class="S"></path><path d="M183 274h3v1h1c0 2 0 2-1 3h-1-1-1-1 0-2v-1l1-1h1v-2h0 1z" class="a"></path><path d="M186 275h1c0 2 0 2-1 3h-1-1-1c1-1 1-1 1-2 1 0 0 0-1-1h3z" class="M"></path><path d="M178 271h1l1 1h0 1c0 1 0 1 1 1v1h0v2h-1v-1h-3c-1 2 1 3-1 6h2-5v-1l1-1h-1c0-2 0-5 1-6 0 0 0 1 1 1v-1h1 1v-2z" class="k"></path><path d="M176 276h1c0 1 0 2-1 3h-1v-3h1z" class="M"></path><path d="M189 233l1 3h0c-2 3-4 5-5 7v10 5h-1c0 1 0 1-1 2h0-2c0 1-2 3-3 4l-2 1h-1l-1 1v-1c-1-1 0-2 0-3-1-1-1-2-1-3h0l2-1v-1h-2v-3-1 1h1c2-2 3-4 4-6l3-1v-2-1h1v-2l-1 1-2 1v-1l-1-1 3-2v-1l3-3c1-1 1-1 1-2h3l1-1z" class="Z"></path><path d="M174 262c0-1 2-1 3-1l1-1-1-1c0-1 1-2 1-3h1v2 1l2 1h0c0 1-2 3-3 4l-2 1h-1l-1 1v-1c-1-1 0-2 0-3z" class="V"></path><path d="M181 245h1 1v3c-1 1-1 2-1 4h1l1 1h1v5h-1c0 1 0 1-1 2h0-2 0v-5-2-6-2z" class="i"></path><path d="M184 253h1v5h-1c-1 0-2-1-2-2v-2c1 0 2 1 2 0v-1z" class="F"></path><path d="M189 233l1 3h0c-2 3-4 5-5 7v10h-1l-1-1h-1c0-2 0-3 1-4v-3h-1-1v-1h1v-2l-1 1-2 1v-1l-1-1 3-2v-1l3-3c1-1 1-1 1-2h3l1-1z" class="h"></path><path d="M185 234h3c-1 2-3 4-5 6-1 0-1 1-1 2l-1 1-2 1v-1l-1-1 3-2v-1l3-3c1-1 1-1 1-2z" class="f"></path><path d="M191 241v-1c4-4 3-8 4-13h0c0 2-1 5 1 7l-1 3c2-1 2 0 4 0v3l-1 2h0v1c-1 2 0 4-2 6h-1-1v1c1 0 1 0 2 1v2 3l-1 1h-1l1 2h0c1 1 1 2 0 4h1l-1 1c-1 0-2-1-2-1h-1c-1-1-1-1-1-2l-2 1v-1c-1-1-1-2-2-3h1l-1-1c0-1 0-1-1-2 0-1 0-3 1-4 0-2 0-3-1-4-1-2-1-2-1-4 1-2 3-4 5-7h1 0c0 1 0 2-1 3 1 0 1 1 1 2z" class="l"></path><path d="M191 252c-1-3-1-7 1-10l2 3-1 1-2 1v5zm4 7h0c1 1 1 2 0 4h1l-1 1c-1 0-2-1-2-1h-1c-1-1-1-1-1-2h0l-1-2h1 4z" class="D"></path><path d="M191 252v-5l2-1 1 2h0l1 1h-1v1c1 0 1 0 2 1v2 3l-1 1h-1c-1 1-3 1-4 1 1-2 1-4 1-6z" class="E"></path><path d="M195 257c-1-1-2-2-2-3l1-1h2v3l-1 1z" class="e"></path><path d="M195 237c2-1 2 0 4 0v3l-1 2h0v1c-1 2 0 4-2 6h-1l-1-1h0l-1-2 1-1-2-3v-1l3-4z" class="C"></path><path d="M195 237c2-1 2 0 4 0v3l-1 2-1-1h-1v-2h-1c0 1 0 2-1 3l-2-1 3-4z" class="E"></path><path d="M190 236h1 0c0 1 0 2-1 3 1 0 1 1 1 2-2 2-2 4-2 7v1 12c-1-1-1-2-2-3h1l-1-1c0-1 0-1-1-2 0-1 0-3 1-4 0-2 0-3-1-4-1-2-1-2-1-4 1-2 3-4 5-7z" class="F"></path><path d="M190 236h1 0c0 1 0 2-1 3-2 2-3 5-4 8-1-2-1-2-1-4 1-2 3-4 5-7zm8 6v2h1 1c1 1 2 2 3 2v4l-1 1c1 2 1 4 1 6v6 7h0-2c-1 1-1 1-1 2v1c0 1 0 2-1 3 0 1-1 2-1 3h-2c-1 1-2 2-3 2-2 1-4 3-6 4l-1-2c-1-2-1 0-1 0h-2c-1-1-1-2-3-2 1 0 1-1 2-1l1 1c2 0 5-2 5-3v-2-1h2 0v-2l1-1c0-1 0-2 1-2h0v-1-1h0l-1-1c0-2 1-2 2-4 0 0 1 1 2 1l1-1h-1c1-2 1-3 0-4h0l-1-2h1l1-1v-3-2c-1-1-1-1-2-1v-1h1 1c2-2 1-4 2-6v-1z" class="c"></path><path d="M196 263v2h1l2 2v2c-1 0-1 0-1-1h-1c-2-1-2-1-3-3h-1c-1 1-1 2-1 3h0l-1-1c0-2 1-2 2-4 0 0 1 1 2 1l1-1z" class="Z"></path><path d="M196 251c0 2-1 5 1 7h0 0c2 0 4 0 6-1v6 7h0v-1c-2-1-3-2-4-4 1-1 2-1 2-1 1-1 1-1 1-2v-1l-2 1h0c0-1 0-2-1-2 0-1-1 0-2 0h-1l1 1 1 1v1c-1 0-1-1-2-1v1h-1c1-2 1-3 0-4h0l-1-2h1l1-1v-3-2z" class="a"></path><path d="M199 265h1c1 0 1 0 2 1h0c1-1 1-1 1-2v-1 7h0v-1c-2-1-3-2-4-4z" class="l"></path><path d="M198 242v2h1 1c1 1 2 2 3 2v4l-1 1c1 2 1 4 1 6-2 1-4 1-6 1h0 0c-2-2-1-5-1-7-1-1-1-1-2-1v-1h1 1c2-2 1-4 2-6v-1z" class="b"></path><path d="M198 251l1 1v2h-1c-1-1-1-1 0-3z" class="c"></path><path d="M198 251l-1-1c1 0 1-1 2-1h1c0 1 0 2-1 3l-1-1z" class="Y"></path><path d="M200 249l2 2c1 2 1 4 1 6-2 1-4 1-6 1h0c0-1-1-1-1-2l2-1h1v-1-2c1-1 1-2 1-3z" class="X"></path><path d="M192 270h3c0-1-1-1-1-1h0-1c0-1 0-2 1-3v2h1c1 0 3 1 3 2 1 1 1 2 1 3 1 1 0 2 0 3s-1 2-1 3h-2c-1 1-2 2-3 2-2 1-4 3-6 4l-1-2c-1-2-1 0-1 0h-2c-1-1-1-2-3-2 1 0 1-1 2-1l1 1c2 0 5-2 5-3v-2-1h2 0v-2l1-1c0-1 0-2 1-2h0z" class="J"></path><path d="M172 229l2 1 1 1v3c1 0 1 0 1 1 0 0-1 0-1 1v1h1c1 0 2 1 2 1h1c0 1 1 2 2 2l-3 2 1 1v1l2-1 1-1v2h-1v1 2l-3 1c-1 2-2 4-4 6h-1v-1 1 3h2v1l-2 1h0c-1 0-1 0-2-1h-2v-2c-1 0-1 0-2 1-1 0-1 0-2-1h-2c-1-1 0-2 1-3l-2-1 1-1v-2h0c1-1 0-3 0-4v-2h-4v-1h0c0-1-1-2-1-2v-3l-1-1c1 0 2 0 2-1h5 8v-6z" class="S"></path><path d="M166 242v1c1 1 1 1 0 2l-1 1v-3l1-1z" class="l"></path><path d="M168 246l1-1h2c-1 2-2 2-3 3v-2z" class="L"></path><path d="M167 238h1c1 1 1 1 2 1h0l-1 1v3h-2l-1-1 1-1v-3z" class="N"></path><path d="M162 237h10l1 1c-1 0-2 0-3 1-1 0-1 0-2-1h-1-2v-1h-3z" class="D"></path><path d="M175 237h1v1s0 1-1 1c0 1 0 2 1 3v1h1v1l-2 2c-1 0-1 0-2-1 1-1 1-2 1-3s1-2 1-2l-1-1c0-1 0-1 1-2z" class="e"></path><path d="M170 239c1-1 2-1 3-1v5c-1 1-1 1-2 1s-1 0-1-1v-2-2h0z" class="H"></path><path d="M159 236l3 1h3v1h2v3l-1 1 1 1h-1v-1c-1-1-1-2-1-3-1-1-3 0-5 0v-1h-1v-2z" class="M"></path><path d="M159 236l3 1h3v1h-5-1v-2z" class="i"></path><path d="M176 237c1 0 2 1 2 1h1c0 1 1 2 2 2l-3 2-1 1h-1v-1c-1-1-1-2-1-3 1 0 1-1 1-1v-1zm-13 6h2v3 1-1h3v2l-1 1-3 4-2-1 1-1v-2h0c1-1 0-3 0-4v-2z" class="M"></path><path d="M163 249l2 1c0-1 0-1 1-1h1l-3 4-2-1 1-1v-2z" class="L"></path><path d="M158 237h0l1-1v2h1v1c2 0 4-1 5 0 0 1 0 2 1 3l-1 1h-2-4v-1h0c0-1-1-2-1-2v-3z" class="H"></path><path d="M159 242c2-1 3-1 5-1 0 1 0 1 1 2h-2-4v-1h0z" class="F"></path><path d="M170 249l1 1h1c1 1 0 3 1 4v3h2v1l-2 1h0c-1 0-1 0-2-1h-2v-2h1v-1l-1-1h1v-2c-1 0-1 1-2 2-1-1-1-1-2-1h0c0-2 3-3 4-4z" class="I"></path><path d="M171 258v-2h2v3h0c-1 0-1 0-2-1z" class="i"></path><path d="M179 244l2-1 1-1v2h-1v1 2l-3 1c-1 2-2 4-4 6h-1v-1 1c-1-1 0-3-1-4h-1l-1-1 1-2 2-2c1 1 1 1 2 1l2-2v-1l1-1 1 1v1z" class="P"></path><path d="M178 242l1 1v1c0 1 0 2-1 3-1-1-1-2-1-3v-1l1-1z" class="Q"></path><path d="M173 245c1 1 1 1 2 1-1 1-1 2-2 3l-2-2 2-2z" class="M"></path><path d="M171 247l2 2v4 1c-1-1 0-3-1-4h-1l-1-1 1-2z" class="h"></path><path d="M179 244l2-1 1-1v2h-1v1 2l-3 1v-1c1-1 1-2 1-3z" class="a"></path><path d="M180 582v-2c2-1 3-1 5-1s4 0 6 1 3 2 5 4 4 4 7 6h1c-1-2-3-2-4-4l1-1-1-2v-1l2-2 2 3h0 2c1 1 1 2 2 2 2 1 3 3 5 4 0-1-1-2-1-3h2v-1h0c2 0 2 0 4 1l2-1c0 1 1 1 2 2v1l2 2c0 1 1 2 2 3l2 1h-1c-1-1-2-1-2-2 0 2 0 2 2 3v1h1 1 0c0 1 1 2 1 2h0l1 2h0c-1 1-5 3-6 3 0 1 1 0 2 1l-1 2 1 1 1-1h1l2-2v1 1 1l-1 1v1 1h-2l1 2h-1s-1 0-1 1c-1 1-1 2-2 3h-1v1c1 0 1 1 2 1l2 2h0v8l-1 14v5c0 2 0 3 1 5-1 6-2 12-2 18v1h-1v-1-3l-1-1c0 2 0 4-1 5l-1 28c-1-2-1-5-1-7v-11-8-10c0-1-1-2-1-2v-1c0-1 0-2-1-4h-1v-17h-1 0v-5h-1v-6-7-2c-1 1-2 3-3 3v1 1 3 1c-1 0-1-1-1-2v-2-2-2-3-5-1c0-1-1-1-2-1l-13-7c-3-2-7-3-9-6h0c-2 1-5-1-6-2-2 0-3-1-5-2l-1 1h0v-2c-1-1-1 0-2 0-1-2-6-3-8-5 2-1 3-3 5-4 1-1 5-2 6-2 1 1 1 1 1 2h0 1v-2l1 1z" class="E"></path><path d="M184 581v3c-1 2-2 3-3 5 0-2 1-4 0-5l1-1 2-2z" class="L"></path><path d="M180 582v-2c2-1 3-1 5-1l-1 1v1l-2 2-1 1c-1-1-1-2-1-2z" class="j"></path><path d="M213 622v-3l1-2c-1 0-1-1-1-1v-3l1-1 3 3c-1 2-1 4-1 6v-2c-1 1-2 3-3 3z" class="V"></path><path d="M230 598l1 2h0c-1 1-5 3-6 3 0 1 1 0 2 1l-1 2 1 1 1-1h1l2-2v1 1 1l-1 1v1 1h-2l1 2h-1s-1 0-1 1c-1 1-1 2-2 3h-1v1s-1 0-1 1c0 3 1 7 0 11-1-1 0-8 0-9v-4c0-1 2-2 3-3v-1l-1-1h-2 0v-1c0-2-1-2-2-4 0-1 1-1 2-2h-4l1-1c1-1 3-1 4-1 3-1 4-2 6-4z" class="R"></path><path d="M227 609v-2h1c1 1 0 2 0 3v1h-1-1l1-1v-1z" class="D"></path><path d="M223 635v-1c1-2 1-4 1-6h0c0 4 1 8 1 12v7c0 1 1 2 1 3h0c0-1 0-2 1-3 0 2 0 3 1 5-1 6-2 12-2 18v1h-1v-1-3l-1-1c0 2 0 4-1 5v-36z" class="f"></path><path d="M224 666h0c0-2-1-8 0-9h1v3c0 3 0 7 1 10v1h-1v-1-3l-1-1z" class="Q"></path><path d="M226 650h0c0-1 0-2 1-3 0 2 0 3 1 5-1 6-2 12-2 18-1-3-1-7-1-10s0-7 1-10z" class="E"></path><path d="M223 618c0-1 1-1 1-1 1 0 1 1 2 1l2 2h0v8l-1 14v5c-1 1-1 2-1 3h0c0-1-1-2-1-3v-7c0-4-1-8-1-12h0c0 2 0 4-1 6v1-6c1-4 0-8 0-11z" class="F"></path><path d="M223 618c0-1 1-1 1-1 1 0 1 1 2 1l2 2h0c-1 1-1 1-2 1v2l-1 2v22-7c0-4-1-8-1-12h0c0 2 0 4-1 6v1-6c1-4 0-8 0-11z" class="a"></path><path d="M223 618c0-1 1-1 1-1 1 0 1 1 2 1l2 2h0c-1 1-1 1-2 1v2l-1 2v-4h0v-2s0-1-1-1h-1z" class="e"></path><path d="M217 615h0v-2h1l1 2h1 2v6h-1c0 1 0 1 1 2h0c-1 1 0 2 0 4s0 4-1 6c0 1 0 2 1 3h-1v4l1 12c0 5 0 9-1 14 0 2 1 5 0 7v-10c0-1-1-2-1-2v-1c0-1 0-2-1-4h-1v-17h-1 0v-5h-1v-6-7c0-2 0-4 1-6z" class="I"></path><path d="M219 615h1c1 1 1 5 0 6v3h0c-1-1-1-1-2-1v-1l-1-1 1-6h1z" class="c"></path><path d="M218 623c1 0 1 0 2 1h0c1 2 0 6 0 8 0 0-1 1-2 1h0v-9-1z" class="f"></path><path d="M218 623c1 0 1 0 2 1 0 1-1 1-1 1h-1v-1-1z" class="W"></path><defs><linearGradient id="S" x1="223.023" y1="644.7" x2="215.039" y2="649.636" xlink:href="#B"><stop offset="0" stop-color="#aeaeaf"></stop><stop offset="1" stop-color="#d4d1d3"></stop></linearGradient></defs><path fill="url(#S)" d="M218 639h0v-4l1-1h1 1c-1 1-1 1-2 1l2 2v12c0 5 1 9 0 14 0-1-1-2-1-2v-1c0-1 0-2-1-4h-1v-17z"></path><path d="M200 583v-1l2-2 2 3h0 2c1 1 1 2 2 2 2 1 3 3 5 4 0-1-1-2-1-3h2v-1h0c2 0 2 0 4 1l2-1c0 1 1 1 2 2v1l2 2c0 1 1 2 2 3l2 1h-1c-1-1-2-1-2-2 0 2 0 2 2 3v1h1 1 0c0 1 1 2 1 2h0c-2 2-3 3-6 4-1 0-3 0-4 1l-1 1h-2l-1-3c-1-1-2-1-3-2-2-1-3-1-4-2s-1-2-1-3l-5-4h1c-1-2-3-2-4-4l1-1-1-2z" class="C"></path><path d="M200 583v-1l2-2 2 3-2 2h-1l-1-2z" class="j"></path><path d="M203 590h1c6 4 12 7 19 7l1 1c-3 1-7 0-11-1l-5-3-5-4z" class="F"></path><path d="M204 583h2c1 1 1 2 2 2 2 1 3 3 5 4l1 1c1 0 1 0 2 1s2 1 4 2l2 1c1 1 2 1 2 1l3 1h1l1 1c-1 1-3 1-4 1h-1l-1-1c1 0 1 0 1-1-3-1-5-2-7-3s-3-3-5-3c-1-1-2-2-4-3-1-1-2-3-4-4z" class="D"></path><path d="M220 585c0 1 1 1 2 2v1l2 2c0 1 1 2 2 3l2 1h-1c-1-1-2-1-2-2 0 2 0 2 2 3v1l-3-1s-1 0-2-1l-2-1c-2-1-3-1-4-2s-1-1-2-1l-1-1c0-1-1-2-1-3h2v-1h0c2 0 2 0 4 1l2-1z" class="U"></path><path d="M220 585c0 1 1 1 2 2v1 2h-1l-1-1v-1l-2-2 2-1z" class="C"></path><path d="M214 585h0l2 1h0c1 2 3 4 4 5s1 1 2 1h1c1 0 1 0 1 1v2s-1 0-2-1l-2-1c-2-1-3-1-4-2s-1-1-2-1l-1-1c0-1-1-2-1-3h2v-1z" class="K"></path><path d="M212 586h2c1 1 1 1 1 2s0 1-1 2l-1-1c0-1-1-2-1-3z" class="U"></path><path d="M208 594l5 3c4 1 8 2 11 1h1c1 0 3 0 4-1l-1-1h1 0c0 1 1 2 1 2h0c-2 2-3 3-6 4-1 0-3 0-4 1l-1 1h-2l-1-3c-1-1-2-1-3-2-2-1-3-1-4-2s-1-2-1-3z" class="O"></path><path d="M208 594l5 3c1 1 0 1 1 1 1 1 2 1 3 2 1 0 3 0 3 1v1c-1 0-2 0-2-1h-1-1c-1-1-2-1-3-2-2-1-3-1-4-2s-1-2-1-3z" class="C"></path><path d="M182 146v-2l1-1 1 1v2c3 2 6 0 9 1h1c1 0 2 0 2 1l2-1h1 5c0-1 1-2 1-3l1 1c0 1 0 1-1 2s-1 1-2 3v1h-2s0 1-1 1c0 1 1 2-1 2h0 6 2 3v-1h2c3 0 7-1 10 0h0 0l-1 1c0 1 0 1 1 1 0 1 1 1 1 2h0c2 2 3 2 4 5l-1 5c1 3 1 5 0 8v18 5 2h-1c-1-1-2-1-4-2h-3l-1-1c-2 1-4 1-5 0s-1-1 0-2l-1-1c-1 1-2 1-4 1l-2 1c-1-1-1-1-2 0h-4-1c-1-1-1-2-2-3h1c0-2 0-4 2-6l-3-3-2 2c0 2-1 3-2 5h0v1l-3 1c-1-1-1-2-3-2h-1v3-4-7-3-5c0-4 1-9 0-13v-1c1-2 1-2 3-3h-1-4-1c1-1 0-4 0-5v-3-4z" class="P"></path><path d="M195 164c-1-1 0-1-1-2h-1-1v-1h3 0c2 1 3 1 4 2v1c-1 0-2-1-4 0z" class="X"></path><path d="M216 159h0c0 1-1 2-2 2 0 1-1 1-1 1l-2 2c-1-1-1-2-2-3l1-1h3v1c0-1 1-1 1-2h1 1 0zm-21 5v1c-2 0-3-1-4-1h-1l-2-2 1-1c2-1 4-1 6 0h-3v1h1 1c1 1 0 1 1 2z" class="Q"></path><path d="M192 178c1-2 2-1 4-1v-2h0c0-1 0-1-1-1v-1h0 1l1 1c0 1 0 2 1 2-1 1-1 1-1 2h1v1c-2 0-3 1-4 0-1 0-2-1-2-1z" class="m"></path><path d="M212 153c3 0 7-1 10 0h0 0l-1 1c0 1 0 1 1 1 0 1 1 1 1 2h0c-1-1-2-1-3-1 0 1-1 1-1 2h-1v-1l1-2v-1c-3 0-6 1-9 0h0v-1h2z" class="J"></path><path d="M226 167h-1c-1 0-1-1-2-1v-1c0-1-1-2-1-2-1-1-1-1-2-1-1-1-1-1-1-2v-1c1 1 2 1 3 2h0c2 1 3 1 5 1l-1 5z" class="o"></path><path d="M215 181v-2l1-1c0-1 0-1-1-2h-1c-1-1-1-2-2-3l1-1h1v2h1v-3h1c1 2 1 5 2 7 1 1 1 2 2 4h-1c-1-1-1-1-2-1l-1 1c1 1 1 2 2 3h1 1v1l-1 1s-1 1-1 2c-1 1-2 1-2 2-2 1-3 3-4 4l-1-1v-1c1 0 1-1 2-1 0-1 1-1 1-2 1-1 1-1 0-1 2-1 2-1 3-2s-1-2-2-4v-2z" class="Y"></path><path d="M198 176h2 0l-1-3h1c1 0 1 1 2 1l1-1h1v1l-1 1h-1c1 2 1 5 0 6 0 2-2 4-3 6l-3-3-2 2c0 2-1 3-2 5h0v1l-3 1c-1-1-1-2-3-2h-1v3-4-7-3-5c2 1 3 1 4 3 0 0 0 1 1 2l2-2s1 1 2 1c1 1 2 0 4 0v-1h-1c0-1 0-1 1-2z" class="Q"></path><path d="M190 183l1-2c0 1 1 1 2 2h0c-1 1-1 1-2 1s-1-1-1-1z" class="a"></path><path d="M193 183c1-1 2 0 4 0l-1 1-2 2h-1l-2-1v-1c1 0 1 0 2-1h0zm-8 0c1 0 2 0 3 1-1 1-1 2-2 2 1 1 2 1 3 2v2h-4v-7z" class="Y"></path><path d="M202 175c1 2 1 5 0 6 0 2-2 4-3 6l-3-3 1-1 5-8z" class="B"></path><path d="M190 183s0 1 1 1v1l2 1h1c0 2-1 3-2 5h0v1l-3 1c-1-1-1-2-3-2h-1v3-4h4v-2c1-1 1-1 1-2v-3z" class="b"></path><path d="M193 186h1c0 2-1 3-2 5l-1-1s-1-1-1-2c1-1 2-1 3-2z" class="X"></path><path d="M220 182h1 0l1-1v-3h1s1-1 2 0h0c2-1 0-2 1-3v18 5 2h-1c-1-1-2-1-4-2h-3l-1-1c-2 1-4 1-5 0s-1-1 0-2 2-3 4-4c0-1 1-1 2-2 0-1 1-2 1-2l1-1v-1h-1-1c-1-1-1-2-2-3l1-1c1 0 1 0 2 1h1z" class="T"></path><path d="M215 194h1 0l-1 2h-1v-1l1-1z" class="P"></path><path d="M217 197c1 0 4 1 5 0 1-2 0-3 0-4h-2 0 1c1-1 1-2 1-3 1 1 1 1 1 2h3v1h0v5 2h-1c-1-1-2-1-4-2h-3l-1-1z" class="X"></path><path d="M223 192h3v1h0v5 2h-1l1-1-1-1c-1-1-1-1-1-3h0c-1-1-1-2-1-3z" class="T"></path><path d="M223 192h3v1 1l-2 1c-1-1-1-2-1-3z" class="P"></path><path d="M220 182h1 0l1-1v-3h1s1-1 2 0h0c2-1 0-2 1-3v18h0v-1c0-2 1-8 0-10h-1c-2 0-3 2-4 2 1 2 2 4 1 6h-3c1-1 1-1 2-1v-1c0-1-1-1-2-1l1-1v-1h-1-1c-1-1-1-2-2-3l1-1c1 0 1 0 2 1h1z" class="Q"></path><path d="M182 146v-2l1-1 1 1v2c3 2 6 0 9 1h1c1 0 2 0 2 1l2-1h1 5c0-1 1-2 1-3l1 1c0 1 0 1-1 2s-1 1-2 3v1h-2s0 1-1 1c0 1 1 2-1 2h0 6 2l1 1h-1-1v1c2 0 3 0 4 1 2 0 4 0 5-1 1 1 1 2 1 3h0-1-1c0 1-1 1-1 2v-1-1l-4-1h-4c-4-1-7 0-10 0h-2-3-2-1-4-1c1-1 0-4 0-5v-3-4z" class="W"></path><path d="M188 153h1v1l-2 2h0l-2-2s2 0 3-1z" class="T"></path><path d="M199 147h5c-1 1-1 1-2 1l-1 1h-2v-2z" class="Y"></path><path d="M182 153h1v5h-1c1-1 0-4 0-5z" class="e"></path><path d="M199 157h-2-2c-1 0-2-1-3-1 3-2 4-2 7-2h0l-1 1h0l1 2z" class="Q"></path><path d="M199 154h6 2l1 1h-1-1v1c2 0 3 0 4 1-3 0-6-1-9 0h-2l-1-2h0l1-1z" class="T"></path><path d="M182 146v-2l1-1 1 1v2c3 2 6 0 9 1h1c1 0 2 0 2 1l2-1h1v2h-1c0 1-1 2-2 3h0c-1 0-1 1-2 1-1 1-2 1-3 1 0-1 0-1-1-2h-1c-1 1-1 0-2 0v-1s-1-1-1-2h-1v3h-1l-1 1h-1v-3-4z" class="X"></path><path d="M190 149c2 1 2 1 4 0 0 0 0 1 1 1h-1l-2 1c-1 0-2 0-2-1v-1z" class="W"></path><path d="M182 146v-2l1-1 1 1c-1 1 0 6 0 8l-1 1h-1v-3-4z" class="D"></path><path d="M204 174v2h3c0-1 0-1 1-2 1 0 1 1 2 1s1 0 1 1v2c0 2 1 2 3 4l1-1v2c1 2 3 3 2 4s-1 1-3 2c1 0 1 0 0 1 0 1-1 1-1 2-1 0-1 1-2 1v1c-1 1-2 1-4 1l-2 1c-1-1-1-1-2 0h-4-1c-1-1-1-2-2-3h1c0-2 0-4 2-6 1-2 3-4 3-6 1-1 1-4 0-6h1l1-1z" class="m"></path><path d="M204 185h0 4v1s-1 1-3 1c0-1-1-1-1-1v-1z" class="J"></path><path d="M202 175h1c0 1 0 2 1 3 1 0 1 1 1 1 1 1 1 1 1 2l-1-1v1c0 1-1 1-2 2l-1-2c1-1 1-4 0-6z" class="Y"></path><path d="M203 183c1-1 2-1 2-2v-1l1 1v1l-3 2c0 1 0 1 1 1v1l1 2c-1 1-1 1-2 0l-2-2v-2l1-1 1 1v-1z" class="c"></path><path d="M205 188h0c1 1 1 1 2 1 1 1 0 2 2 3v1c1 0 1-1 2-1l1-1 1 1c-1 0-1 1-2 1v1c-1 1-2 1-4 1h-1-1c0-1 0-1-1-2v-2c0-1-1-2-2-2v-1h0 1c1 1 1 1 2 0z" class="Q"></path><path d="M202 181l1 2v1l-1-1-1 1v2l2 2h-1 0v1c1 0 2 1 2 2v2c1 1 1 1 1 2h1 1l-2 1c-1-1-1-1-2 0h-4-1c-1-1-1-2-2-3h1c0-2 0-4 2-6 1-2 3-4 3-6z" class="W"></path><path d="M202 181l1 2v1l-1-1-1 1v2c-1 2-4 6-3 9 0 0 0 1 1 1h-1c-1-1-1-2-2-3h1c0-2 0-4 2-6 1-2 3-4 3-6z" class="l"></path><path d="M227 293h0v4l1 1c2 1 3-1 5-1 2-1 3-1 5-1h0c0 1 0 1 1 1v2h2v1 1 5 5 2h1v-2c1 0 2 2 3 3h0l-2 1-1 2h1 0v4l1-1 2 1v2 1h2c1-1 1-2 1-4l1 2h1v-1c1 2 1 3 2 5v1l1 2c1 0 1 0 1 1 1 1 2 1 2 2l1 1c2 1 3 3 4 3l2 2c1 1 1 1 2 1 1 1 2 1 3 1v-1h2v1h4l-2 1c1 0 1 1 1 1v1h1c0 1 0 1 1 2v1c1 0 1-1 2 0l1 1c1 0 1 0 2 1-1 0-2 1-2 1 0 1 0 2-1 3h0c-1 0-1 0-2 1h-2 0-9-8-6-1-10 0-1v3c0 1-1 1-1 1 1 1 1 2 0 3v1h-2l-3-1h0c-2-2-4-3-7-4h-4c-1-1-1-1-2-1s-2-1-3-2l-3 3-3-2-2 3v-1c-1-1-2-2-2-3-1 0-1 0-1-1l-1-1 3-3 1-3h1 0c0 1 0 1 1 1l1-2v-4h0v2l2 1 1 1 2-1 7-7 2-2h0 0c1-1 1-2 1-4v-10-2l-1-10c1-2 1-5 0-7v-2c1-2 0-4 0-6h1z" class="M"></path><path d="M258 336c1 0 4 1 5 3h-2c-1 0-2-1-4 0h-3v-1c1-1 1-1 2-1 1 1 1 1 2 1h0v-2z" class="B"></path><path d="M219 351s1 0 2 1c0-1 0-1 1-1s2 0 2 1h0c-1 1-2 1-3 1l-1 1h1 0c1-1 2-1 3-1l-2 1c1 1 1 1 2 1l2 1h-4c-1-1-1-1-2-1s-2-1-3-2h0l2-2z" class="E"></path><path d="M224 346l1-1 1 1v2c0 1-1 2-1 2l-1 2c0-1-1-1-2-1s-1 0-1 1c-1-1-2-1-2-1l3-3 2-2z" class="K"></path><path d="M224 346l1-1 1 1v2c0 1-1 2-1 2-1 0-1-1-2-1 1-1 1-2 1-3h0z" class="C"></path><path d="M240 343v3h0-1c-3 1-4 2-6 4h-1c-2 0-3 1-4 2h-3c1 0 1 0 1-1h0c0-1 1-1 2-1 1-1 1-2 2-2 2-1 3-2 5-3s3-1 4-2h1z" class="Z"></path><path d="M239 339h2v1c-1 1 0 2-2 2-2-1-3 0-5 0v2l-2-1c-1 2-2 3-4 4-1 0-1 0-2 1v-2l-1-1c1 0 1 0 2-1v-4 2h2l1-1h2c1-1 2-1 3-1l4-1z" class="F"></path><path d="M229 342l1-1h2c1-1 2-1 3-1h0c0 1-1 1-2 1v2h0c-1 0-3 0-4 1s-1 2-1 3c-1 0-1 0-2 1v-2l-1-1c1 0 1 0 2-1v-4 2h2z" class="d"></path><path d="M227 340v2h2c-1 1-1 2-2 3h0v-1-4z" class="B"></path><path d="M239 346h1c0 2 1 5 0 7h-1-8c-2 0-5 1-7 0v-1h1 3c1-1 2-2 4-2h1c2-2 3-3 6-4z" class="c"></path><path d="M233 350h2 4 0c1 0 1 0 1 1v1l-2-1-1 1c-1 0-2 0-3-1h0-3l1-1h1z" class="W"></path><path d="M233 350c2-2 3-3 6-4h0v2 1l-1 1c-1-1-2-1-3 0h-2z" class="V"></path><path d="M231 353h8v3c0 1-1 1-1 1 1 1 1 2 0 3v1h-2l-3-1h0c-2-2-4-3-7-4l-2-1c-1 0-1 0-2-1l2-1c1 1 6 0 7 0z" class="n"></path><path d="M224 336l2-2c1 2 1 3 1 5v1 4c-1 1-1 1-2 1l-1 1-2 2-3 3-2 2v-1c0-1 1-2 1-3v-1l1-1h-1l-1 1v-1-1l-2-2h0l2-1 7-7z" class="c"></path><path d="M219 347l1-1h2l-1 1 1 1-3 3-2 2v-1c0-1 1-2 1-3v-1l1-1z" class="W"></path><path d="M224 336l2-2c1 2 1 3 1 5l-1-3-3 3v1l-1 1c-1 0-1 0-2 1v1c0 1 0 1-1 2h-1l-1-2 7-7z" class="G"></path><path d="M212 340h0v2l2 1 1 1h0l2 2v1 1l1-1h1l-1 1v1c0 1-1 2-1 3v1h0l-3 3-3-2-2 3v-1c-1-1-2-2-2-3-1 0-1 0-1-1l-1-1 3-3 1-3h1 0c0 1 0 1 1 1l1-2v-4z" class="f"></path><path d="M209 345h1 0c0 1 0 1 1 1 0 2 0 2-1 3-1-1-1-1-2-1l1-3z" class="c"></path><path d="M215 344l2 2v1l-3 1h-1l2-4z" class="S"></path><path d="M209 353v-1c0-2 2-3 4-4h1c-2 2-3 4-5 5h0z" class="l"></path><path d="M217 347v1l1-1h1l-1 1v1c0 1-1 2-1 3v1h0l-3 3-3-2-2 3v-1-3h0c2-1 3-3 5-5l3-1z" class="V"></path><path d="M211 354l7-6v1c0 1-1 2-1 3v1h0l-3 3-3-2z" class="J"></path><path d="M269 339h2v1h4l-2 1c1 0 1 1 1 1v1h1c0 1 0 1 1 2v1c1 0 1-1 2 0l1 1c1 0 1 0 2 1-1 0-2 1-2 1 0 1 0 2-1 3h0c-1 0-1 0-2 1h-2 0-9-8-6-1-10 0c1-2 0-5 0-7h0v-3h1v-1h1 4c1 0 2-1 3-1l1 1h1l1-1v-1h3 2c1-1 3-1 4-1s2 0 3 1l2-1c1 1 2 1 3 1v-1z" class="Y"></path><path d="M266 343c2-2 4-3 7-2 1 0 1 1 1 1v1h1c0 1 0 1 1 2v1 1h-1c-1 0-2 0-3 1h-3c-1-1 0-1 0-2h0l-1-1c0 1-1 1-2 1v-1h-3l-1-1h0 0l4-1z" class="J"></path><path d="M266 343c2-2 4-3 7-2 1 0 1 1 1 1v1h1c0 1 0 1 1 2-2-1-5-3-7-1 0 0 0 1-1 1s-1-1-1-1h0c-2 0-2 0-4 1l-1-1h0 0l4-1z" class="f"></path><path d="M251 349h1 2v-1h0v-1h1l1 1h1c1-1 1-2 2-2h0v2c1 0 1 0 1 1 1 0 1 1 1 2h1c1 0 1-1 2-1h1c1 0 2 0 4 1 1 0 2-1 3-1h4c1 0 2-1 3-1 0 1 0 2-1 3h0c-1 0-1 0-2 1h-2 0-9-8c-1 0-2-1-3-1h-1c-2-1-2-2-2-3z" class="Q"></path><path d="M269 339h2v1h4l-2 1c-3-1-5 0-7 2l-4 1h0c-1 0-2 1-3 1v1h0c-1 0-1 1-2 2h-1l-1-1h-1v1h0v1h-2-1c0 1 0 2 2 3h1c1 0 2 1 3 1h-6-1-10 0c1-2 0-5 0-7h0v-3h1v-1h1 4c1 0 2-1 3-1l1 1h1l1-1v-1h3 2c1-1 3-1 4-1s2 0 3 1l2-1c1 1 2 1 3 1v-1z" class="S"></path><path d="M240 343h1v-1h1-1c0 1 1 2 1 3v1h-2v-3z" class="G"></path><path d="M242 345v-1c2 1 3 0 5-1h3l-2 1-1 1c-1 1-2 1-3 1h-2 0v-1z" class="a"></path><path d="M250 343c2 0 5-1 7 0-2 2-5 3-7 4h-1l-1-1c0-1 0-1-1-1l1-1 2-1z" class="b"></path><path d="M250 348c2-1 3-2 5-3s4-1 7-1h0c-1 0-2 1-3 1v1h0c-1 0-1 1-2 2h-1l-1-1h-1v1h0v1h-2-1c-1-1-3 0-4 1 1-1 2-2 3-2z" class="J"></path><path d="M269 339h2v1h4l-2 1c-3-1-5 0-7 2v-1-2h0-3c0 1 0 1-1 1l1 1v1h-1l-1-1h-2c-1 0-2 0-3-1h-1-1c0 1-1 0-2 0v-1h3 2c1-1 3-1 4-1s2 0 3 1l2-1c1 1 2 1 3 1v-1z" class="H"></path><path d="M247 345c1 0 1 0 1 1l1 1h1v1c-1 0-2 1-3 2 1-1 3-2 4-1 0 1 0 2 2 3h1c1 0 2 1 3 1h-6-1-10 0c1-2 0-5 0-7h0 2 0 2c1 0 2 0 3-1z" class="l"></path><path d="M244 348v-1h1 0c0 1 1 1 1 2-2 1-3 2-6 3l4-4z" class="J"></path><path d="M247 345c1 0 1 0 1 1l1 1c-1 1-2 1-3 2 0-1-1-1-1-2h0-1v1l-2-1v-1h0 2c1 0 2 0 3-1z" class="f"></path><path d="M247 350c1-1 3-2 4-1 0 1 0 2 2 3h1c1 0 2 1 3 1h-6-1c-2-1-5 0-7-1l4-2z" class="T"></path><path d="M227 293h0v4l1 1c2 1 3-1 5-1 2-1 3-1 5-1h0c0 1 0 1 1 1v2h2v1 1 5 5 2h1v-2c1 0 2 2 3 3h0l-2 1-1 2h1 0v4l1-1 2 1v2 1h2c1-1 1-2 1-4l1 2h1v-1c1 2 1 3 2 5v1l1 2c1 0 1 0 1 1 1 1 2 1 2 2l1 1c2 1 3 3 4 3l2 2c1 1 1 1 2 1l-2 1c-1-1-2-1-3-1h0 2c-1-2-4-3-5-3v2h0c-1 0-1 0-2-1-1 0-1 0-2 1v1c-1 1-2 0-3 0-2 0-3 0-5 1-1 0-2-1-3 0h-2v-1h-2l-4 1c-1 0-2 0-3 1h-2l-1 1h-2v-2-1c0-2 0-3-1-5h0 0c1-1 1-2 1-4v-10-2l-1-10c1-2 1-5 0-7v-2c1-2 0-4 0-6h1z" class="U"></path><path d="M236 336c-1 0-2 1-3 2h-1v-1l3-3c0 1 0 1 1 2z" class="B"></path><path d="M238 332c1 0 2-1 2 0l-1 1h0v1l-3 2c-1-1-1-1-1-2l2-2h0 1z" class="D"></path><path d="M237 332h-1c-1 0-2 1-3 2l-1-1-3 1h0v-1c1 0 2-1 3-1h1c1-1 2-1 2-2v-1c-1 1-2 1-3 1 0-1 0-1 1-1v-1l-1-1h1 1 1c0 1 0 2 1 3 0 1 1 1 2 2h-1 0z" class="K"></path><path d="M234 319h1l3-1v1h1l-1 2v3c0 1-1 1-1 2s1 1 1 2h1l1 1 1 1h0v1 1h-1c0-1-1 0-2 0-1-1-2-1-2-2-1-1-1-2-1-3h1v-2-2l1-2v-1h-1c-2 0-5 0-7-1v-1l5 1z" class="F"></path><path d="M240 329l1 1h0v1c-1 0-2 0-3-1h0c1-1 1-1 2-1z" class="H"></path><path d="M229 312h1l-2-2v-1h2c1 1 2 3 4 3h0v2c1 1 1 1 1 2v2l-1 1-5-1h0v-2c-1-1-1-2-1-4h1z" class="j"></path><path d="M229 312h1l-2-2v-1h2c1 1 2 3 4 3h0v2c1 1 1 1 1 2 0-1-1-1-2-2h0c-2 0-3-1-4-2z" class="O"></path><path d="M234 312v-1l2 1c2 1 5 3 7 3l-1 2h1 0v4h0l-1-1-2 2-2-1 1-2h-1v-1l-3 1h-1l1-1v-2c0-1 0-1-1-2v-2h0z" class="W"></path><path d="M239 319c1-1 2-1 4-2v4h0l-1-1-2 2-2-1 1-2z" class="D"></path><path d="M234 312l5 5-1 1-3 1h-1l1-1v-2c0-1 0-1-1-2v-2z" class="C"></path><path d="M227 293h0v4l1 1c2 1 3-1 5-1 2-1 3-1 5-1h0c0 1 0 1 1 1v2h2v1 1 5 5 2h1v-2c1 0 2 2 3 3h0l-2 1c-2 0-5-2-7-3l-2-1v1c-2 0-3-2-4-3h-2v1l2 2h-1-1l-1 2v3c1 1 0 3 0 3v-2l-1-10c1-2 1-5 0-7v-2c1-2 0-4 0-6h1z" class="B"></path><path d="M239 303l-1-1v-1h3v5 5c-1-1-2-1-2-3v-5h0z" class="h"></path><path d="M239 303h1c0 2 0 4-1 5v-5h0z" class="N"></path><path d="M235 302l1 1v1 2 1h1c0-1-1-3 0-4h1 0v6c0 1-1 1-1 1 0 1 0 2-1 2l-2-1v1c-2 0-3-2-4-3l1-1 1 1c2 0 2 1 3 0v-3h0v-4z" class="D"></path><path d="M227 293h0v4l1 1c2 1 3-1 5-1v6 3s0 1 1 1v-1-1-4h0c1 0 1 1 1 1v4h0v3c-1 1-1 0-3 0l-1-1-1 1h-2v1l2 2h-1-1l-1 2v3c1 1 0 3 0 3v-2l-1-10c1-2 1-5 0-7v-2c1-2 0-4 0-6h1z" class="E"></path><path d="M243 321l1-1 2 1v2 1h2c1-1 1-2 1-4l1 2h1v-1c1 2 1 3 2 5v1l1 2c1 0 1 0 1 1 1 1 2 1 2 2l1 1c2 1 3 3 4 3l2 2c1 1 1 1 2 1l-2 1c-1-1-2-1-3-1h0 2c-1-2-4-3-5-3v2h0c-1 0-1 0-2-1-1 0-1 0-2 1v1c-1 1-2 0-3 0-2 0-3 0-5 1-1 0-2-1-3 0h-2v-1h-2s1 0 2-1c0-1-1-1-1-2-1 0-2 1-2 1h-1v-1h0l2-2v-1h0l1-1h1v-1-1h0l-1-1-1-1h-1c0-1-1-1-1-2s1-1 1-2v-3l2 1 2-2 1 1h0z" class="I"></path><path d="M251 327l3 4c-2 0-3-1-4-1l-2-2h1 2v-1z" class="H"></path><path d="M249 320l1 2 1 5v1h-2v-1c0-2 0-2-1-3 1-1 1-2 1-4z" class="F"></path><path d="M250 322h1v-1c1 2 1 3 2 5v1l1 2c1 0 1 0 1 1 1 1 2 1 2 2l1 1v1c-2 0-3-3-4-3l-3-4-1-5z" class="f"></path><path d="M243 321l1-1 2 1v2 1h0v1c1 0 2 1 2 2h-1 0c0-1 0-1-1-1h0c-1 1-2 1-2 1h-1c0-1 0-3 1-4l-2-1h-2l2-2 1 1h0z" class="F"></path><path d="M240 322l2-2 1 1h1v2l-2-1h-2z" class="E"></path><path d="M238 321l2 1h2l2 1c-1 1-1 3-1 4h-1l1 2-2 1-1-1-1-1h-1c0-1-1-1-1-2s1-1 1-2v-3z" class="O"></path><path d="M242 322l2 1c-1 1-1 3-1 4h-1l1 2-2 1-1-1-1-1 1-1h1c0-1 0-2-1-3 0-1 1-1 2-2zm9 15h0l-6-6h1 2s0 1 1 1v-1h2c1 1 1 1 3 1l1 1c0 1 1 1 1 1 1 1 1 2 2 2v2h0c-1 0-1 0-2-1h-3c-1 1-1 0-2 0z" class="d"></path><path d="M249 331h2c1 1 1 1 3 1l1 1c0 1-1 1-1 1h-1c-2 0-3-1-4-3z" class="E"></path><path d="M253 337l-1-1 1-1c1 0 2 0 3-1h0c1 1 1 2 2 2v2h0c-1 0-1 0-2-1h-3z" class="O"></path><path d="M239 333c1 0 2-1 2-1 1 0 2 1 3 0 3 1 4 4 7 5 1 0 1 1 2 0h3c-1 0-1 0-2 1v1c-1 1-2 0-3 0-2 0-3 0-5 1-1 0-2-1-3 0h-2v-1h-2s1 0 2-1c0-1-1-1-1-2-1 0-2 1-2 1h-1v-1h0l2-2v-1z" class="C"></path><path d="M183 158h4 1c-2 1-2 1-3 3v1c1 4 0 9 0 13v5 3 7 4-3h1c2 0 2 1 3 2l3-1v-1h0c1-2 2-3 2-5l2-2 3 3c-2 2-2 4-2 6h-1s-1 0-2 1l1 1c0 1-1 1 0 2v3 1h1v1c-1 1-1 2-1 4v1c1 1 1 1 1 3h-1 0v17h0c-1 5 0 9-4 13v1c0-1 0-2-1-2 1-1 1-2 1-3h0-1 0l-1-3-1 1h-3c0 1 0 1-1 2l-3 3v1c-1 0-2-1-2-2h-1s-1-1-2-1h-1v-1c0-1 1-1 1-1 0-1 0-1-1-1v-3l-1-1-2-1v6h-8-5c0 1-1 1-2 1v-1c1-2 1-6 1-8v-23-7h-1l1-2h1 2c1 0 1 0 2 1h5v-2c1 0 3 0 4 1v-7-6c0-1 1-1 1-1h0c0-1 1-1 1-2v-2l1-1v-2-6c-1-1 0-3-1-5h-1v-1l1-1c0-1 0-1-1-2l1-1h8 1z" class="g"></path><path d="M185 194l1 1 1 1c1 0 2 1 2 2-1 1-2 1-3 1l-1-1v-4z" class="W"></path><path d="M181 211l1-1h2 0v2c0 1 0 1 1 1l2 2v4l-1-1v-2h-2 0l-2-1 1-1h0-1l-1-3h0z" class="H"></path><path d="M173 201h1c1 1 2 2 4 2v1l-1 1h-2l-1 1h0c1 1 3 1 4 0h8v1l-1 1h-1c-1 0-3 0-4 1-2-1-3-1-5 0 0-1-1-1-1-1h-1v-7z" class="D"></path><path d="M175 209c2-1 3-1 5 0h0c1 0 1 1 1 2h0l1 3h1 0l-1 1 2 1v1h-2v1h-1c-1-1-2-1-4-1h-2c-1 0-1-1-2-2 1-2 0-4 0-6h1 1z" class="G"></path><path d="M180 209c1 0 1 1 1 2h0c0 2 0 2-1 4v-1l-1-5h1z" class="Z"></path><path d="M175 209c2-1 3-1 5 0h0-1-1c-1 1 1 4-1 4-1 0-1 0-2-1 0-1 0-2-1-3h1z" class="k"></path><path d="M173 215c1-2 0-4 0-6h1c1 1 1 2 1 3l-1 1c0 1 1 2 1 2 2 1 6 1 7 0l2 1v1h-2v1h-1c-1-1-2-1-4-1h-2c-1 0-1-1-2-2z" class="R"></path><path d="M172 195v-7-6c0-1 1-1 1-1 1 1 0 3 0 5 0 4-1 9 0 13v2h0v7h1s1 0 1 1h-1-1c0 2 1 4 0 6 1 1 1 2 2 2l2 1-2 1h-1l-1 3h-1v-1-7-2-11-1l-2-2v-1c-1-1-1-1-2-1v-2c1 0 3 0 4 1z" class="S"></path><path d="M173 215c1 1 1 2 2 2l2 1-2 1h-1c-1-2-1-3-1-4z" class="I"></path><path d="M168 194c1 0 3 0 4 1v5l-2-2v-1c-1-1-1-1-2-1v-2z" class="h"></path><path d="M184 216h0 2v2l1 1v3 1 4 5c-1 0-1 0-2 2 0 1 0 1-1 2l-3 3v1c-1 0-2-1-2-2h-1s-1-1-2-1h-1v-1c0-1 1-1 1-1 0-1 0-1-1-1v-3l-1-1v-4l-1-4 1-3h1l2-1-2-1h2c2 0 3 0 4 1h1v-1h2v-1z" class="L"></path><path d="M177 218h2s1 1 1 2h0l-2 1c0-1-1-2-1-3z" class="K"></path><path d="M177 218h0c0 1 1 2 1 3h-2-1-1c0-1 0-1 1-2l2-1z" class="d"></path><path d="M176 227c1 1 3 1 5 1l1-1c0 1 1 1 1 2h0l-2 1c-1 0-4 0-6-1v-2h1z" class="V"></path><path d="M174 226c1 1 1 2 1 3h0c2 1 5 1 6 1 2 0 2-1 3 1v1h-1-1c-1 1-5 0-7-1l-1-1v-4z" class="D"></path><path d="M184 216h2v2l1 1v3 1 4 5c-1 0-1 0-2 2l-1-1c0-1 1-2 1-4v-8l-1-5z" class="S"></path><path d="M184 216h2v2l1 1v3 1c-2 1 0 3-2 5v-7l-1-5z" class="g"></path><path d="M174 219h1c-1 1-1 1-1 2h1 1 7l-1 4h-2v-2h-1c0 2 0 2-1 2-1 1-1 1-2 1v1h-1v2h0c0-1 0-2-1-3l-1-4 1-3z" class="l"></path><path d="M174 219h1c-1 1-1 1-1 2h1c1 2 0 4 0 6v2h0c0-1 0-2-1-3l-1-4 1-3z" class="F"></path><path d="M175 231c2 1 6 2 7 1h1v3l1 1-3 3v1c-1 0-2-1-2-2h-1s-1-1-2-1h-1v-1c0-1 1-1 1-1 0-1 0-1-1-1v-3z" class="Z"></path><path d="M181 239v-1c0-1 0-2-1-3v-1h0 2v2h1v-1l1 1-3 3z" class="G"></path><path d="M196 184l3 3c-2 2-2 4-2 6h-1s-1 0-2 1l1 1c0 1-1 1 0 2v3 1h1v1c-1 1-1 2-1 4v1c1 1 1 1 1 3h-1 0v17h0c-1 5 0 9-4 13v1c0-1 0-2-1-2 1-1 1-2 1-3h0-1 0l-1-3-1 1h-3c1-2 1-2 2-2v-5-4-1-3-4-9-3-2c0-1 1-1 2-1h0 1c0-1 0-1-1-2 0-1-1-2-2-2l-1-1-1-1h0v-3h1c2 0 2 1 3 2l3-1v-1h0c1-2 2-3 2-5l2-2z" class="I"></path><path d="M185 194v-3h1c2 0 2 1 3 2l3-1c0 2 0 3-1 4s-1 0-2 0-2-1-3-1h0l-1-1h0z" class="J"></path><path d="M186 195c1-2 1-2 2-2h2v2l-1 1c-1 0-2-1-3-1z" class="Q"></path><path d="M191 226v-21h1v6l1 14c0 4 0 8-2 11h0-1 0l-1-3v-3c1-1 1-4 1-5v-1l1 2z" class="V"></path><path d="M189 230c1-1 1-4 1-5v-1l1 2c0 3 0 7-1 10l-1-3v-3z" class="F"></path><path d="M187 215v-9-3-2c0-1 1-1 2-1h0v6 12c0 3 1 5 0 8v4 3l-1 1h-3c1-2 1-2 2-2v-5-4-1-3-4z" class="X"></path><path d="M187 227c1 1 1 3 1 5 1-1 0-3 0-4h0c0-1 1-1 1-2h0v4 3l-1 1h-3c1-2 1-2 2-2v-5z" class="J"></path><defs><linearGradient id="T" x1="203.203" y1="210.57" x2="182.35" y2="224.914" xlink:href="#B"><stop offset="0" stop-color="#161713"></stop><stop offset="1" stop-color="#514f54"></stop></linearGradient></defs><path fill="url(#T)" d="M196 184l3 3c-2 2-2 4-2 6h-1s-1 0-2 1l1 1c0 1-1 1 0 2v3 1h1v1c-1 1-1 2-1 4v1c1 1 1 1 1 3h-1 0v17h0c-1 5 0 9-4 13v1c0-1 0-2-1-2 1-1 1-2 1-3 2-3 2-7 2-11l-1-14c1-2 1-8 0-10l1-3-1-1v-6h0c1-2 2-3 2-5l2-2z"></path><path d="M193 198h1 0l-1-1v-4l1 1 1 1c0 1-1 1 0 2v3 1l-1-1h0c1-1 1-1 0-2v1l-2 2 1-3z" class="C"></path><path d="M196 184l3 3c-2 2-2 4-2 6h-1s-1 0-2 1l-1-1v4l1 1h0-1l-1-1v-6h0c1-2 2-3 2-5l2-2z" class="E"></path><path d="M183 158h4 1c-2 1-2 1-3 3v1c1 4 0 9 0 13v5 3 7 4h0v4s-1 2 0 2v3 1c0 1-1 1-2 1h-6l1-1v-1c-2 0-3-1-4-2h-1 0v-2c-1-4 0-9 0-13 0-2 1-4 0-5h0c0-1 1-1 1-2v-2l1-1v-2-6c-1-1 0-3-1-5h-1v-1l1-1c0-1 0-1-1-2l1-1h8 1z" class="D"></path><path d="M177 181h2 1l-1 3h0v1l-2 1-1-1v-3h0l1-1z" class="G"></path><path d="M175 174h1 2l1 1c0 1 0 2-1 3l-1 2v1l-1 1c-1 0-1-1-1-2v-4-2z" class="C"></path><path d="M178 174l1 1c0 1 0 2-1 3l-1 2v-3h0c0-2 0-2-1-3h2z" class="N"></path><path d="M178 178c2 1 3 0 4 1l1 1h0c-1 1-1 3-1 5h1l-1 2s0 1-1 1c-1 1-2 1-4 1h-1v-1-2c1 1 1 1 2 1h0l2-2-1-1 1-3h-1-2v-1l1-2z" class="B"></path><path d="M178 187h2c1 0 0-1 1-1v2c-1 1-2 1-4 1h-1v-1-2c1 1 1 1 2 1h0zm7-25c1 4 0 9 0 13v5c0 1-1 1-1 2s1 2-1 3h-1c0-2 0-4 1-5h0l-1-1c-1-1-2 0-4-1 1-1 1-2 1-3l-1-1h-2-1v-6h6 1c0 1 2 0 3-1v-5z" class="O"></path><path d="M178 174h0c0-1-1-2-1-2 0-1 0-2 1-2s1 0 1 1 1 2 1 3l1 1c1 1 1 2 1 3v1c-1-1-2 0-4-1 1-1 1-2 1-3l-1-1z" class="L"></path><path d="M185 162c1 4 0 9 0 13v5c0 1-1 1-1 2s1 2-1 3h-1c0-2 0-4 1-5h0l-1-1v-1c0-1 0-2-1-3 0-1 1-1 1-2h1s1 0 2-1v-5-5z" class="i"></path><path d="M183 158h4 1c-2 1-2 1-3 3v1 5c-1 1-3 2-3 1h-1-6c-1-1 0-3-1-5h-1v-1l1-1c0-1 0-1-1-2l1-1h8 1z" class="E"></path><path d="M183 185c2-1 1-2 1-3s1-1 1-2v3 7 4h0v4s-1 2 0 2v3 1c0 1-1 1-2 1h-6l1-1v-1c-2 0-3-1-4-2h-1 0v-2c1 1 2 2 3 1h0l-1-1v-3c1 0 2-1 3-3l-1-2h-2l1-2h1c2 0 3 0 4-1 1 0 1-1 1-1l1-2z" class="N"></path><path d="M181 199c1-1 1-2 1-2 1 1 1 2 1 4l-2-2z" class="M"></path><path d="M182 191h1v1 1h-2c0 1 0 1 1 2h-1c0-1-1-1-1-2s1-1 2-2z" class="B"></path><path d="M183 205l-1-1 1-1c0-1-1-1-2-1v-2h-1l1-1h0l2 2h1c0 1 1 1 1 2v1c0 1-1 1-2 1z" class="h"></path><path d="M182 187c1 0 2 1 3 2l-1 1h-1c-1 0-1 0-2 1h-3l-1-2c2 0 3 0 4-1 1 0 1-1 1-1z" class="i"></path><path d="M157 197l1-2h1 2c1 0 1 0 2 1h5c1 0 1 0 2 1v1l2 2v1 11 2 7 1h1l1 4v4l-2-1v6h-8-5c0 1-1 1-2 1v-1c1-2 1-6 1-8v-23-7h-1z" class="G"></path><path d="M168 212l1-1 2 2v1h-2c-1 0-1-2-1-2z" class="S"></path><path d="M159 215c1 1 2 1 3 1l1 1v2 2h-4v-6zm13-1v7h-1-4v-2-1-2c2 1 3 1 4 1v3c1-1 1-5 1-6z" class="M"></path><path d="M159 213v-8l1 3c0 1 2 1 2 1 1 0 1 1 2 1l1-1v1h1v1c-1 1-1 1-2 1 1 1 1 1 1 2s-2 1-3 1c-1-1 1-3-1-4v1s-1 1-2 1z" class="I"></path><path d="M170 198l2 2v1 11 2c0 1 0 5-1 6v-3-3-1l-2-2-1 1-1-1v-1-1l-1 1h-1v-1l-1 1c-1 0-1-1-2-1 0 0-2 0-2-1l-1-3s0-1 1-1h0l1 1c2 1 7 0 9 0v-5l-1-1 1-1z" class="F"></path><path d="M167 210h2l1-2 1 1v3h0v1l-2-2-1 1-1-1v-1z" class="H"></path><path d="M157 197l1-2h1 2c1 0 1 0 2 1h5c1 0 1 0 2 1v1l-1 1 1 1v5c-2 0-7 1-9 0l-1-1h0c-1 0-1 1-1 1v8 2 6 14c0 1-1 1-2 1v-1c1-2 1-6 1-8v-23-7h-1z" class="Z"></path><path d="M157 197l1-2h1 2c1 0 1 0 2 1-1 1-2 1-2 3h1c0 2-1 2-1 3 1 1 4 1 4 2-2 0-3 0-4-1l-1-1v-3h-1c-1 0-1-1-1-2h-1z" class="I"></path><path d="M157 197l1-2h1 2l-1 4h-1c-1 0-1-1-1-2h-1z" class="a"></path><path d="M163 221h4 4 1v1h1l1 4v4l-2-1v6h-8-5v-14h4z" class="F"></path><path d="M171 221h1v1c0 1 1 3 0 3l-1 1-1-1-1-1c0-1-1-1-2-1 0-1 1-1 1-2 3 1 1 1 3 2h0v-2z" class="L"></path><path d="M172 222h1l1 4v4l-2-1c0-1 0-1-1-2v-1l1-1c1 0 0-2 0-3z" class="H"></path><path d="M169 224l1 1c0 2 0 5-1 6v1c-1 0-2 0-2 1h2v1h-3-1c-1-1-2-2-2-3 1-2 1-1 2-2h0c0-1 0-3 1-4 1 0 2 0 3-1z" class="M"></path><path d="M167 225l1 1v5h1v1h-2c0-1 0-2-1-3h0c0-1 1-1 1-2v-2z" class="G"></path><path d="M169 224l1 1c0 2 0 5-1 6h-1v-5l-1-1h-1c1 0 2 0 3-1z" class="V"></path><path d="M165 234c-2 0-3 0-4-1-1-2-1-8 0-10h6c1 0 2 0 2 1-1 1-2 1-3 1-1 1-1 3-1 4h0c-1 1-1 0-2 2 0 1 1 2 2 3z" class="c"></path><path d="M181 293c1-1 2-1 4-1 1 0 2 0 2 1h3 0 7c1 1 1 2 1 3h0v3l-1 1c1 2 1 1 0 3 0 1 1 2 0 4l1 1v2 3 1 3h1 1c1 1 1 2 1 3l1 1h2 1v2l2 1c0-1 0-1 1-1h1c0 2 0 2-1 4 1 0 4-1 5 0v1h0 0c-1 2-1 4-1 6v1 1 4h0v4l-1 2c-1 0-1 0-1-1h0-1l-1 3-3 3 1 1-2 1-1 1-2 2-1 1c-1 1-2 1-2 2-1 0-1 0-1-1l-1-2c1-1 1-4 1-5v-4h0l-1 1c-2 0-5-2-6-3l-2-1c-2-1-3 0-4 0-1-1-1-2-2-3l-1 1h-3v-2h-1 0c-1 1-1 1-2 1l-2 1h0v2c-2 1-2 1-4 1v-1h0l-3 2-3 3h-1v-2c1-1 2-1 2-2l1-1-3-2c0-1-1-2 0-3 0-1-1-1-1-1v-9-8h1l1-1-1-2v-1l2-1v-1h0-1l-1-1c1-1 2-2 2-3s0-2 1-3c0-1 0-1 1-2 0-2 2-2 2-4h0v-2l-2-2s0-1 1-1h1s0 1 1 1l1-3h1l1-2c1 0 2 0 3 1l2 1v1 1l1 1h3 0v-2-2-1z" class="Q"></path><path d="M179 333s1 0 2 1v-1l1-1h1c1 0 3 1 3 0l2-1h1v1c-2 1-2 1-3 3 0 1 0 1 1 2-1 0-1 1-2 1s-1 1-2 2h-1 0v-4h-1-2v-1l1-1-1-1z" class="Y"></path><path d="M177 333h1 1l1 1-1 1v1h2 1v4 1l-1 1h-3v-2h-1 0c-1 1-1 1-2 1l-2 1v-2c2-1 2-1 3-2v1h1v-1-5z" class="f"></path><path d="M178 340c0-1 0-2 1-3s2-1 3-1v4 1l-1 1h-3v-2z" class="l"></path><path d="M182 322v2c2 0 2 0 4 1v-1l1 1c1 0 1 0 2 1-2 2-5 0-6 2h-1-1c0 1 1 2 1 3-2 0-2-2-3-1l-1 2v1h-1c-1 0-1 0-2-1l-1-1v-3h5c1 0 1 0 1-1h-1v-1c1-1 2-1 2-1 0-1 1-3 1-3z" class="P"></path><path d="M198 317h1 1c1 1 1 2 1 3l1 1h2 1v2l2 1c0-1 0-1 1-1h1c0 2 0 2-1 4 1 0 4-1 5 0v1h0 0c-1 2-1 4-1 6v1 1 4h0v4l-1 2c-1 0-1 0-1-1h0-1l-1 3-3 3 1 1-2 1-1 1-2 2-1 1c-1 1-2 1-2 2-1 0-1 0-1-1l-1-2c1-1 1-4 1-5v-4h0c-1-5 0-10 0-15v-5l-1-1c0-1 1-1 1-2s1-6 1-7z" class="S"></path><path d="M204 333l1-2s1-1 2-1l1 1c1 0 2-1 2-1h1c1 1 0 2 0 4h1v1h-2c0-2 0-2-1-3h-2c-1 1-1 1-1 2l-2-1z" class="Z"></path><path d="M202 328h5c-1 0-2 1-3 1h0v2h-1c-1-1-1-1-2 0h-1l1 2h0-2v1-2-1-3h3z" class="G"></path><path d="M199 326l1 1h6 2c1 0 4-1 5 0v1h0c-2 1-4 0-6 0h-5-4v-1l1-1z" class="K"></path><path d="M197 324c1 1 1 1 1 2h1l-1 1v1h4-3v3 1 2h1c0 1-1 1-1 1v1s0 1-1 2v3h0v2h-1v4h0c-1-5 0-10 0-15v-5l-1-1c0-1 1-1 1-2z" class="N"></path><path d="M206 334c0-1 0-1 1-2h2c1 1 1 1 1 3h2v1 4h0v4l-1 2c-1 0-1 0-1-1h0-1c0-1 1-1 1-1v-3l-2-2c-1-1-3-3-5-4 1 0 1-1 1-2l2 1z" class="g"></path><path d="M206 334c0-1 0-1 1-2h2c1 1 1 1 1 3h2v1 4h0l-1 1v-1-1c-1 0 0-2-1-3h-1c-1-1-2-1-3-1h-1l1-1z" class="S"></path><path d="M198 317h1 1c1 1 1 2 1 3l1 1h2 1v2l2 1c0-1 0-1 1-1h1c0 2 0 2-1 4h-2-6l-1-1h-1c0-1 0-1-1-2 0-1 1-6 1-7z" class="F"></path><path d="M202 321h2 1v2c-1 0-1 0-2 1-1 0-1-1-1-1h-1c1-1 1-2 1-2z" class="M"></path><path d="M199 322c2 1 2 1 2 3 0 1-1 1-1 2l-1-1h-1c0-2 1-2 1-4z" class="H"></path><path d="M198 317h1 1c1 1 1 2 1 3l-2 1v1h0c0 2-1 2-1 4 0-1 0-1-1-2 0-1 1-6 1-7z" class="M"></path><path d="M205 323l2 1c0-1 0-1 1-1h1c0 2 0 2-1 4h-2c-1-1-2-1-3-1h-1l1-2c1-1 1-1 2-1z" class="S"></path><path d="M201 339h-1c0-2 1-3 2-4h1c2 1 4 3 5 4l2 2v3s-1 0-1 1l-1 3-3 3 1 1-2 1-1 1-2 2-1 1c-1 1-2 1-2 2-1 0-1 0-1-1l-1-2c1-1 1-4 1-5v-4-4h1v-2h1 0c1 0 1-1 2-1v-1z" class="I"></path><path d="M201 339h1 1c0 1-1 2-1 3l-1-1v-1-1z" class="F"></path><path d="M201 339h-1c0-2 1-3 2-4h1c2 1 4 3 5 4l-1 1-1-1h-4-1z" class="l"></path><path d="M207 340l1-1 2 2v3s-1 0-1 1l-1 3-3 3h-1 0c0-1-1-1-1-1v-3c-2-1-3-2-4-3l-1-1v-2h1c1 1 1 2 1 2 1 1 2 1 3 2l1 1c0-1 1-1 2-2s1-2 1-3v-1z" class="Z"></path><path d="M207 340l1-1 2 2c-1 1-1 2-2 3 0-1 0-2-1-3v-1z" class="V"></path><path d="M210 341v3s-1 0-1 1l-1 3-3 3h-1 0c0-1-1-1-1-1v-3h0c2 0 4-2 5-3s1-2 2-3z" class="L"></path><path d="M197 347v-4h1l1 1c1 1 2 2 4 3v3s1 0 1 1h0 1l1 1-2 1-1 1-2 2-1 1c-1 1-2 1-2 2-1 0-1 0-1-1l-1-2c1-1 1-4 1-5v-4z" class="I"></path><path d="M199 344c1 1 2 2 4 3v3s1 0 1 1h0 1l1 1-2 1-1 1-1-1c0-1 0-2-1-3v-2c-1-1-1-2-2-2l-1 1h0c0-2 1-2 1-3z" class="N"></path><path d="M201 350c2 1 2 1 3 2v1l-1 1-1-1c0-1 0-2-1-3z" class="L"></path><path d="M197 347v-4h1l1 1c0 1-1 1-1 3h0c1 1 1 2 1 4h0c-1 1-1 2 0 3 0 1 1 1 2 2l-1 1c-1 1-2 1-2 2-1 0-1 0-1-1l-1-2c1-1 1-4 1-5v-4z" class="d"></path><path d="M170 312v1h0c-1 1-1 2-1 3h1 1 0 1c1-1 1-1 2-1h0l1 1v1c-1 0-1 1-1 2 1-1 2-1 2-1h1l-1 1 1 2c1-1 1-1 3-1 1 0 1 0 2 2 0 0-1 2-1 3 0 0-1 0-2 1v1h1c0 1 0 1-1 1h-5v3l1 1c1 1 1 1 2 1v5 1h-1v-1c-1 1-1 1-3 2v2h0v2c-2 1-2 1-4 1v-1h0l-3 2-3 3h-1v-2c1-1 2-1 2-2l1-1-3-2c0-1-1-2 0-3 0-1-1-1-1-1v-9-8h1l1-1-1-2v-1l2-1v-1c2 0 2-1 3-2 1 0 2-1 3-1z" class="J"></path><path d="M172 316c1-1 1-1 2-1h0l1 1v1c-1 0-1 1-1 2h0c0 1 0 1-1 2v-1h0l-3-3h1 1v-1z" class="c"></path><path d="M162 318v-1l2-1v7 3c0 1-1 1-2 2 0-1 1-3 0-4v-2-1l1-1-1-2z" class="a"></path><path d="M177 321c1-1 1-1 3-1 1 0 1 0 2 2 0 0-1 2-1 3 0 0-1 0-2 1-1 0-1 0-2-1v-4z" class="e"></path><path d="M161 321h1v1 2c1 1 0 3 0 4 0 2 2 3 2 5h0l-2-1c-1 1 0 5 0 7 0-1-1-1-1-1v-9-8z" class="N"></path><path d="M173 332v-1l-2-1v-2h2v-5s0-1 1-1l1 1-1 1v2h3c0 1 2 1 2 1h1c0 1 0 1-1 1h-5v3l1 1c1 1 1 1 2 1v5 1h-1v-1c-1 1-1 1-3 2 0-1 0-1 1-1v-1c-1 0-1 0-1-1l-2 2-1-2c-1 0-1-1-2-1l-1-1v-2c1 0 1-2 2-3 2 1 3 0 4 2z" class="c"></path><path d="M175 332c1 1 1 1 2 1v5 1h-1v-1c-1 0-2-1-2-1v-2h1c0 1 0 1 1 2v-1-1l-2-1c0-1 0-1 1-2z" class="J"></path><path d="M167 333c1 0 1-2 2-3 2 1 3 0 4 2l-3 1v1c1 0 2 1 2 2v1h-2c-1 0-1-1-2-1l-1-1v-2z" class="Y"></path><path d="M162 339c0-2-1-6 0-7l2 1h0c1 0 1 1 2 1l1-1v2l1 1c1 0 1 1 2 1l1 2 2-2c0 1 0 1 1 1v1c-1 0-1 0-1 1v2h0v2c-2 1-2 1-4 1v-1h0l-3 2-3 3h-1v-2c1-1 2-1 2-2l1-1-3-2c0-1-1-2 0-3z" class="T"></path><path d="M162 339c0-2-1-6 0-7l2 1c1 2 3 3 4 5-1 0-2 0-3 1v-2c-1-1-1-1-2-1-1 1-1 2 0 3s1 1 1 2l1 3-3-2c0-1-1-2 0-3z" class="f"></path><path d="M164 333c1 0 1 1 2 1l1-1v2l1 1c1 0 1 1 2 1l1 2 2-2c0 1 0 1 1 1v1c-1 0-1 0-1 1v2h0v2c-2 1-2 1-4 1v-1h0c-2-2-4-3-4-5 1-1 2-1 3-1-1-2-3-3-4-5h0z" class="S"></path><path d="M168 338h1c0 1 1 2 2 2v3c-1 1-1 1-2 1h0c-2-2-4-3-4-5 1-1 2-1 3-1z" class="X"></path><path d="M181 293c1-1 2-1 4-1 1 0 2 0 2 1h3 0 7c1 1 1 2 1 3h0v3l-1 1c1 2 1 1 0 3 0 1 1 2 0 4l1 1v2h0c-2 0-2 0-3-1h-1c0 1 0 1-1 2l-3-3c0-2 1-3-1-4h-2c-1-1-1-2-1-3-1 1-1 1-1 2h0c0 1 1 2 0 3l2 2v1 1c-1 0-1 1-1 2h1l-3 4c0 1 0 1 1 2v2h-1v-1h-1v-1l-1-1c0 1 0 1-1 2h0 0l-1-3s-1-1-2-1c-1-1-2-3-3-4v1h0c0 1 0 2-1 3h0c-1 0-1 0-2 1h-1 0-1-1c0-1 0-2 1-3h0v-1c-1 0-2 1-3 1-1 1-1 2-3 2h0-1l-1-1c1-1 2-2 2-3s0-2 1-3c0-1 0-1 1-2 0-2 2-2 2-4h0v-2l-2-2s0-1 1-1h1s0 1 1 1l1-3h1l1-2c1 0 2 0 3 1l2 1v1 1l1 1h3 0v-2-2-1z" class="m"></path><path d="M177 308c1 0 2 0 3 1v1l-1 1v-1c-1 0-2-1-3-1h1 0v-1z" class="T"></path><path d="M181 293c1-1 2-1 4-1 1 0 2 0 2 1h3c-1 0-2 0-3 1-2-1-2-1-4-1 0 1 0 2 1 2l-3 1v-2-1z" class="P"></path><path d="M181 298c1 2 1 5 1 7v1c-1 0-3 1-3 1-1-1-1-1-2-1h-1s-1 1-1 2h1 1v1h0-1l-1 1v1 1h0c0 1 0 2-1 3h0c-1-1-1-2-1-3h0v-1c0-1 1-1 1-2 1 0 1 0 1-1h-1c0-2 2-3 3-4l1-1 1-1c0-1 0-1 1-2l-1-1v1l-1-1h1l-1-1h3 0z" class="X"></path><path d="M179 302c1 0 1 0 2 1 0 1 0 2-1 2h-1l-1-2 1-1z" class="m"></path><path d="M171 295l1-2c1 0 2 0 3 1l2 1v1 1l1 1 1 1h-1l1 1v-1l1 1c-1 1-1 1-1 2l-1 1-1 1c-1 1-3 2-3 4h1c0 1 0 1-1 1 0 1-1 1-1 2v1h0c0 1 0 2 1 3-1 0-1 0-2 1h-1 0-1-1c0-1 0-2 1-3h0v-1c-1 0-2 1-3 1-1 1-1 2-3 2h0-1l-1-1c1-1 2-2 2-3s0-2 1-3c0-1 0-1 1-2 0-2 2-2 2-4h0v-2l-2-2s0-1 1-1h1s0 1 1 1l1-3h1z" class="P"></path><path d="M170 301l1-1v1 1h-2l1-1z" class="Q"></path><path d="M175 294l2 1v1 1h-1-1c-1-1-1-2 0-3z" class="J"></path><path d="M173 298h-1v-1c0-1 1-2 1-3l1 1v1l1 1h1v2l-1-1h-2z" class="T"></path><path d="M170 295h1l-1 1v2l1 1v1h-2v3 1c0 1-1 1-1 1 0 1-1 2-1 2h-1c-1 0-1 1-1 1 0-1 0-1 1-2 0-2 2-2 2-4h0v-2l-2-2s0-1 1-1h1s0 1 1 1l1-3z" class="b"></path><path d="M176 297h1l1 1 1 1h-1l1 1v-1l1 1c-1 1-1 1-1 2l-1 1-1 1h-1c-2 1-3 2-4 3 0 1-1 1-1 2-1-1-1-1-1-2h1l-1-1c0 1-1 1-1 1h-1c1-1 1-2 3-2v-2h0l1-1c1 1 1 2 2 2l1-1c0-1 0-2-1-3 0 0 0-1-1-1v-1h2l1 1v-2z" class="W"></path><path d="M176 297h1l1 1 1 1h-1-2 0v-2z" class="Q"></path><path d="M171 309c0-1 1-1 1-2 1-1 2-2 4-3h1c-1 1-3 2-3 4h1c0 1 0 1-1 1 0 1-1 1-1 2v1h0c0 1 0 2 1 3-1 0-1 0-2 1h-1 0-1-1c0-1 0-2 1-3h0v-1c-1 0-2 1-3 1-1 1-1 2-3 2h0-1l-1-1c1-1 2-2 2-3s0-2 1-3c0 0 0-1 1-1h1v1s1 0 1 1 1 1 1 1l2-1z" class="b"></path><path d="M164 311h2l1 1-3 3h-1l-1-1c1-1 2-2 2-3z" class="J"></path><path d="M170 312l1-2c1 1 1 0 1 1v2l-1 1v2h0-1-1c0-1 0-2 1-3h0v-1z" class="P"></path><path d="M165 308s0-1 1-1h1v1s1 0 1 1 1 1 1 1c0 1-1 1-2 2h0l-1-1h-2c0-1 0-2 1-3z" class="W"></path><path d="M164 374h4 2v1l1 1v1h1l1-1h0c2 1 3 0 4 0l2 1c2 1 5-1 7 0l-1 1c2 1 5 0 7 0h4 0l2 4c0 2 1 5 2 6h0v2l-1 1c1 1 1 2 2 3l2 4 4 5-2 2v1h1c0 1-1 1-1 1v1h0-1v2l-1 1v-1l-1 1v2c-1 1-2 1-2 3h0-2v-2c-1-1-1-3-1-5-1 3 0 4 0 7 0 1-1 2-1 4h0l-1 1c0 1 1 2 0 3v5 2l-2-1v4 1l1 1v1 1h0c1 1 1 1 1 2l1 1v6c0 2 1 5 0 7l2 1s0-1-1-2c1-1 1-1 2-1l2 2h1v3c0 1-1 2 0 2v3h-1 0-3-1c-3 1-7 0-8 2l-1 1 2 2s1 1 1 2-1 1-1 2v1l-1 1h-3l-1-1h0c-1-1-2 0-3-1-2 1-3 2-5 3-1 1-2 1-3 1h-1l-1 1v-3-5h0v-3-4-3h0c0-1 0-2-1-3h0 0-1c-1-1 0-2-1-3v-1c0-1 0-1 1-2 1 0 2-1 2-2v-1h3c-1 0-2-1-3-2l2-2c-1-1-1-2-1-3v-1-4l1-3v-1c0-1 1-1 2-1v-1-1-1h1c-1-1-1-2-1-3l-1-2h2v-1h-1l1-1h0c0-1-1-1-1-2h2v-3c1-1 1-1 1-2-1-2-2-3-4-5-1-1-2-2-4-3h-1s-1-1-1-2v-2-2-2c-1-2 0-5 0-6 0-2 0-4 1-6v-1l1 1v-1h0l-4-4h0c-1-2-3-3-4-5h1z" class="M"></path><path d="M187 402h3l-1 1h-1v4 7 1h-1 0v1c-1-2 0-5 0-7 0-1-1-2-1-4 0-1 1-2 1-3z" class="G"></path><path d="M175 405c2 1 3 2 4 4 1 1 2 3 3 4 0 1 0 2-1 3l-1-1h0c-1 0-1 0-2-1 1-1 1-1 1-2-1-2-2-3-4-5 1-1 0-1 0-2z" class="H"></path><path d="M179 412c1 1 2 2 1 3h0c-1 0-1 0-2-1 1-1 1-1 1-2z" class="D"></path><path d="M188 414v-7-4h1 0l2 2v9l-1-1c-1 0-1 1-2 1z" class="e"></path><path d="M181 395c1 0 2 1 2 2 1 1 1 2 2 3 1 0 2 0 2 1h1l1-1h1v1 1h-3c-1-1-1-1-2-1l-1 1h0c-1-1-1-1-1-2s-1-1-2-2h0v2l-1 1-2-2c0-1 1-1 2-2l1 1v-1s0-1-1-2h1z" class="F"></path><path d="M184 393c1 1 2 2 4 3l1 2c2 1 3 2 4 3v1c1 0 1 0 1 1-1-1-1-1-2-1h-1 0l-1-1v-1h-1l-1 1h-1c0-1-1-1-2-1-1-1-1-2-2-3 0-1-1-2-2-2l1-1-1-1h3z" class="G"></path><path d="M184 393c1 1 2 2 4 3l1 2c0 1-1 2-2 2l-2-2c0-1-1-1-1-2v-1s-1-1-2-1l-1-1h3z" class="F"></path><path d="M182 413l1 1v4c1 2 0 5 1 7l-2-1c-1 0-2 1-4 2h-1c-1-1-1-2-1-3l-1-2h2v-1h-1l1-1h0c0-1-1-1-1-2h2v-3c1 1 1 1 2 1h0l1 1c1-1 1-2 1-3z" class="e"></path><path d="M177 419h2c1 0 2 2 3 2v2 1c-1 0-2 1-4 2h-1c-1-1-1-2-1-3l-1-2h2v-1h-1l1-1z" class="S"></path><path d="M176 423h1c2-1 3-1 5 0v1c-1 0-2 1-4 2h-1c-1-1-1-2-1-3z" class="J"></path><path d="M188 414c1 0 1-1 2-1l1 1c0 2 1 5 0 7h0v6 5c1 2 1 3 0 4l-1-1v-1h-2c0 1 0 1-1 2v-4h1v-1-2h0c-1 0-1 0-2 1h-1l-1-1-1-3 1-1c-1-2 0-5-1-7l1-2v-1c1 1 1 2 1 3 1 2 1 3 1 4h1c-1-2 0-4 0-6v-1h0 1v-1z" class="H"></path><path d="M187 422h0c0 1 0 2-1 2v1-3h1z" class="I"></path><path d="M187 416v-1h0 1v6l-1 1h0c-1-2 0-4 0-6z" class="S"></path><path d="M183 418l1-2v-1c1 1 1 2 1 3v12l-1-1-1-3 1-1c-1-2 0-5-1-7z" class="l"></path><path d="M188 429h1 1l-1-1h-1v-1h2 0 1c-1-1 0-3-1-4h0c0-1 0-1 1-1v5h0v5c1 2 1 3 0 4l-1-1v-1h-2c0 1 0 1-1 2v-4h1v-1-2z" class="I"></path><path d="M188 431c1 1 2 1 2 2v1h-2c0 1 0 1-1 2v-4h1v-1z" class="G"></path><path d="M190 401l1 1h0 1c1 0 1 0 2 1h1c0 1 2 3 2 4v2c-1 3 0 4 0 7 0 1-1 2-1 4h0l-1 1c0 1 1 2 0 3v5 2l-2-1v4l-2-2v-5-6h0c1-2 0-5 0-7v-9l-2-2h0l1-1v-1z" class="S"></path><path d="M191 402h1c1 0 1 0 2 1h1c-1 1-2 2-3 2 0-1 0-2-1-3z" class="I"></path><path d="M191 421h1 1v3c-1 1 0 2 0 3s1 2 1 2l1 1h-2v4l-2-2v-5-6z" class="Z"></path><path d="M171 383h1c1 1 2 1 3 2v1c2 1 4 2 5 4 1 0 2 1 2 1l2 2h-3l1 1-1 1h-1c1 1 1 2 1 2v1l-1-1c-1 1-2 1-2 2l2 2s1 1 1 2v2c0 1 0 1 1 2h-1-1 0c-2 0-3-2-4-4 1-1 0-2 0-2h-1c-1 0-2-1-2-1h-1c1 1 1 2 2 2 0 1 1 2 1 3s1 1 0 2c-1-1-2-2-4-3h-1s-1-1-1-2v-2-2-2c-1-2 0-5 0-6 0-2 0-4 1-6v-1l1 1v-1z" class="G"></path><path d="M180 407c-1-1-2-2-2-4l1-1 2 1v2c0 1 0 1 1 2h-1-1z" class="e"></path><path d="M172 400c0-1-1-2-1-3h0l1-1-1-1c-1-1-1-4-1-5l1-2v5c1 2 3 4 3 5s-1 1-1 2h-1z" class="I"></path><path d="M171 383h1c1 1 2 1 3 2v1c2 1 4 2 5 4 1 0 2 1 2 1l2 2h-3l1 1-1 1h-1c1 1 1 2 1 2v1l-1-1c-1 1-2 1-2 2v1 1h0c-2-1-5-8-6-10v-4c0-1-1-1-1-1l1-1-1-1v-1z" class="M"></path><path d="M180 395l-4-5c0-1-1-1-1-2v-1h1l1 3h1c1 0 1 1 1 2 1 1 1 1 2 1l1 1-1 1h-1z" class="h"></path><path d="M171 383h1c1 1 2 1 3 2-1 1-2 1-2 3 0 1 4 5 5 6v1c-1 0-1 0-2-1s-1-3-2-4l-1 1c1 1 2 3 2 4 0 2 1 4 3 5v1h0c-2-1-5-8-6-10v-4c0-1-1-1-1-1l1-1-1-1v-1z" class="N"></path><path d="M182 424l2 1-1 1 1 3 1 1h1c1-1 1-1 2-1h0v2 1h-1v4l1 1-1 1h0v1 1 2c-1 0-2 0-3 1v3c0 2 0 6-2 7v-2c0-1 0-2 1-3l-1-1c-1 1-2 1-3 0 0 0-1 1-2 1l-2-2c-1 0-2-1-3-2l2-2c-1-1-1-2-1-3v-1-4l1-3v-1c0-1 1-1 2-1v-1-1-1h1 1c2-1 3-2 4-2z" class="B"></path><path d="M182 424l2 1-1 1s-1 0-1 1h0c0 2 0 2-1 3-1 0-1 0-1-1-1-1-1-2-2-3 2-1 3-2 4-2z" class="K"></path><path d="M180 429v-2l1-1 1 1c0 2 0 2-1 3-1 0-1 0-1-1z" class="U"></path><path d="M179 432c1 0 2-1 3 0v1s-1 1-1 2h0c2 2 0 5 3 6v2l-1 1c-2 0-7 0-8-1v-1c1 0 1-1 2-1h0 2l1-1v-4c0-1-1-3-1-4z" class="C"></path><path d="M185 430h1c1-1 1-1 2-1h0v2 1h-1v4l1 1-1 1h0v1 1 2c-1 0-2 0-3 1h0v-2c-1-1 0-3 0-4-1-1 0-7 0-8l1 1z" class="k"></path><path d="M186 437c0-1 0-1-1-2v-3c0-1 0-1 1-1l1 1v4l1 1-1 1-1-1z" class="N"></path><path d="M184 437v2h1v-2h1l1 1h0v1 1 2c-1 0-2 0-3 1h0v-2c-1-1 0-3 0-4z" class="I"></path><path d="M177 426h1c1 1 1 2 2 3-1 1-1 2-1 3s1 3 1 4v4l-1 1h-2l-1-1v-2c-1 0-1-1-1-1 1-2 1-4 1-6v-2-1-1-1h1z" class="N"></path><path d="M178 429c1 1 1 5 1 6s-1 1-1 2c-1-3 0-6 0-8z" class="I"></path><path d="M176 428c1 0 1-1 2-1v2c0 2-1 5 0 8 0 1-1 2 0 3l-1 1-1-1v-2c-1 0-1-1-1-1 1-2 1-4 1-6v-2-1z" class="M"></path><path d="M173 434l1-3v-1c0-1 1-1 2-1v2c0 2 0 4-1 6 0 0 0 1 1 1v2l1 1h0c-1 0-1 1-2 1v1c1 1 6 1 8 1l1-1h0v3c0 2 0 6-2 7v-2c0-1 0-2 1-3l-1-1c-1 1-2 1-3 0 0 0-1 1-2 1l-2-2c-1 0-2-1-3-2l2-2c-1-1-1-2-1-3v-1-4z" class="N"></path><path d="M164 374h4 2v1l1 1v1h1l1-1h0c2 1 3 0 4 0l2 1c2 1 5-1 7 0l-1 1c2 1 5 0 7 0h4 0l2 4c0 2 1 5 2 6h0v2l-1 1c1 1 1 2 2 3l2 4 4 5-2 2v1h1c0 1-1 1-1 1v1h0-1v2l-1 1v-1l-1 1v2c-1 1-2 1-2 3h0-2v-2c-1-1-1-3-1-5v-2c0-1-2-3-2-4h-1c0-1 0-1-1-1v-1c-1-1-2-2-4-3l-1-2c-2-1-3-2-4-3l-2-2s-1-1-2-1c-1-2-3-3-5-4v-1c-1-1-2-1-3-2h-1 0l-4-4h0c-1-2-3-3-4-5h1z" class="T"></path><path d="M193 393h2v1l-1 1-1-1v-1zm-6-2c2 0 2 1 3 2h0v1h-2v-1c-1-1-1-1-1-2z" class="P"></path><g class="f"><path d="M175 386c1-1 1-2 2-2l1 2h1c1 1 1 2 2 2 0 1 1 1 2 1h1v1h-1l-1 1s-1-1-2-1c-1-2-3-3-5-4z"></path><path d="M179 383h0c1-1 1 0 2 0h1c0-1 0-2 1-4v2c0 2 0 4 1 5-1 1-1 2-2 3v-3l-3-3z"></path></g><path d="M164 374h4 2v1l1 1v1h1l1-1h0c2 1 3 0 4 0l2 1c2 1 5-1 7 0l-1 1-2 3v-2c-1 2-1 3-1 4h-1c-1 0-1-1-2 0h0l-1 1v2l-1-2c-1 0-1 1-2 2v-1c-1-1-2-1-3-2h-1 0l-4-4h0c-1-2-3-3-4-5h1z" class="P"></path><path d="M167 379h1c1-1 4-1 5-1h3v1h-4c-1 1-1 2-1 4l-4-4zm5 4h1c0-1 0-2 1-3h1c0 1 0 2 1 2 1 1 2 0 1 2-1 0-1 1-2 2v-1c-1-1-2-1-3-2z" class="J"></path><path d="M171 376v1h1l1-1h0c2 1 3 0 4 0l2 1c2 1 5-1 7 0l-1 1-2 3v-2h0v-1c-1 0-2 1-3 0h-1c-1 0-2 0-3 1v-1h-3c-1 0-2 0-3-1l1-1z" class="G"></path><path d="M164 374h4 2v1l1 1-1 1c1 1 2 1 3 1-1 0-4 0-5 1h-1 0c-1-2-3-3-4-5h1z" class="V"></path><path d="M164 374h4 2v1 1c-1 0-1 1-2 1-1-1-3-2-4-3z" class="Y"></path><path d="M201 394l2 4 4 5-2 2v1h1c0 1-1 1-1 1v1h0-1v2l-1 1v-1l-1 1v2c-1 1-2 1-2 3h0-2v-2c-1-1-1-3-1-5v-2c0-1-2-3-2-4h-1c0-1 0-1-1-1v-1c-1-1-2-2-4-3l-1-2c1 1 2 1 3 2 1 0 1 0 2-1 1 0 2 1 3 1 0 1 0 2 1 2h2v-1c1-1 1-2 1-3h0l1-2z" class="P"></path><path d="M205 405v1h1c0 1-1 1-1 1-1 0-2 0-2-1l1-1h1 0z" class="J"></path><path d="M200 412v-3h1c1 0 1 1 2 1l-1 1-2 1z" class="W"></path><path d="M200 400h2c0 1 0 1 1 1l-1 2c-1 0-2-1-3-1v-1l1-1z" class="Y"></path><path d="M202 411v2c-1 1-2 1-2 3l-1-2v-1h0l1-1 2-1z" class="b"></path><path d="M191 432l2 2v1l1 1v1 1h0c1 1 1 1 1 2l1 1v6c0 2 1 5 0 7l2 1s0-1-1-2c1-1 1-1 2-1l2 2h1v3c0 1-1 2 0 2v3h-1 0-3-1c-3 1-7 0-8 2l-1 1 2 2s1 1 1 2-1 1-1 2v1l-1 1h-3l-1-1h0c-1-1-2 0-3-1-2 1-3 2-5 3-1 1-2 1-3 1h-1l-1 1v-3-5h0v-3-4-3h0c0-1 0-2-1-3h0 0-1c-1-1 0-2-1-3v-1c0-1 0-1 1-2 1 0 2-1 2-2v-1h3l2 2c1 0 2-1 2-1 1 1 2 1 3 0l1 1c-1 1-1 2-1 3v2c2-1 2-5 2-7v-3c1-1 2-1 3-1v-2-1-1h0l1-1-1-1c1-1 1-1 1-2h2v1l1 1c1-1 1-2 0-4z" class="H"></path><path d="M192 449h1c1 0 1 0 2 1-1 0-1 1-2 1l-1-2z" class="M"></path><path d="M190 442h3 0c0 1 0 2-1 2-2 0-2 0-3-1v-1h1z" class="a"></path><path d="M189 442v-1-1-4c1 0 2 1 2 1l1 1-1 1h0-1c1 1 1 1 1 2l-1 1h-1z" class="M"></path><path d="M191 432l2 2v1l1 1v1c-1 1-1 2-1 3l1 1h0c-1 0-1 1-1 1h-3l1-1c0-1 0-1-1-2h1 0l1-1-1-1v-1c1-1 1-2 0-4z" class="S"></path><path d="M195 440l1 1v6c0 2 1 5 0 7-1 0-1 1-1 2-1-1-2-1-3-2l1-1c1 0 1 1 2 1v-1-7c0-2-1-4 0-6z" class="F"></path><path d="M187 442h1c1 1 0 4 0 6-1 2 0 3-1 5v1h-1c0 1 0 1 1 1v1s-1 0-1 1c-1-1-1-1-2-1h-1l-8-1 1-1h0 2 2v-1-1l2 2v-1c2-1 2-5 2-7v-3c1-1 2-1 3-1z" class="V"></path><path d="M182 453c2-1 2-5 2-7 0 2 0 5 1 7l2 1h-1c0 1 0 1 1 1v1s-1 0-1 1c-1-1-1-1-2-1h-1l-8-1 1-1h0 2 2v-1-1l2 2v-1z" class="g"></path><path d="M187 456v1h2c0-2 1-3 0-4l1-2v-2-1c1 1 1 1 2 1l1 2v1h-2v1l1 1c1 1 2 1 3 2 0-1 0-2 1-2l2 1s0-1-1-2c1-1 1-1 2-1l2 2h1v3c0 1-1 2 0 2v3h-1 0-3-1c-3 1-7 0-8 2l-1 1 2 2s1 1 1 2-1 1-1 2v1l-1 1h-3l-1-1h0c-1-1-2 0-3-1h1 1c1-3 1-6 1-8l-1-1h1l-1-1c1-1 1-1 1-2-1 0-1 0-1-1v-2c1 0 1 0 2 1 0-1 1-1 1-1z" class="k"></path><path d="M195 460h-1c-1-1-2-2-2-4h0l2 2 1-1v3z" class="I"></path><path d="M187 468c1 1 1 3 3 4v-1 1l-1 1h-3l-1-1h0s1 0 2-1 0-2 0-3z" class="T"></path><path d="M188 465l2 2s1 1 1 2-1 1-1 2v1c-2-1-2-3-3-4v-1c1-1 1-1 1-2z" class="P"></path><path d="M195 456c0-1 0-2 1-2l2 1v1h0c-1 1-1 1-1 2h0 1v1l-2 2h-1v-1-3-1z" class="B"></path><path d="M199 452l2 2h1v3c0 1-1 2 0 2v3h-1 0-3-1c-1 0-1 0-2-1h1l2-2v-1h-1 0c0-1 0-1 1-2h0v-1s0-1-1-2c1-1 1-1 2-1z" class="M"></path><path d="M197 458c1-1 1-1 2-1v-2h1v4c1 1 0 2 0 2l1 1h-3-1c-1 0-1 0-2-1h1l2-2v-1h-1z" class="k"></path><path d="M198 458l1 1c0 1 0 2-1 3h0-1c-1 0-1 0-2-1h1l2-2v-1z" class="G"></path><path d="M172 447v-1h3l2 2c1 0 2-1 2-1 1 1 2 1 3 0l1 1c-1 1-1 2-1 3v2 1l-2-2v1 1h-2-2 0l-1 1 8 1h1v2c0 1 0 1 1 1 0 1 0 1-1 2l1 1h-1l1 1c0 2 0 5-1 8h-1-1c-2 1-3 2-5 3-1 1-2 1-3 1h-1l-1 1v-3-5h0v-3-4-3h0c0-1 0-2-1-3h0 0-1c-1-1 0-2-1-3v-1c0-1 0-1 1-2 1 0 2-1 2-2z" class="U"></path><path d="M180 463l4-1 1 1c0 2 0 5-1 8h-1c0-1-1-3-2-3h-3v-2l1-1 2 1c1 1 1 1 2 0v-3h-3z" class="i"></path><path d="M173 472h1 2c0-1 1-3 0-4v-2h2 0v2h3c1 0 2 2 2 3h-1c-2 1-3 2-5 3-1 1-2 1-3 1h-1v-3z" class="R"></path><path d="M171 455c1 0 1-1 1-2v-1c0-1 1-3 2-3l1 1c1 1 0 3 1 4l-1 1v5c0 1 0 1 1 1h0-2v1h-1c0 2 1 3 0 4v1c1 0 1-1 2-1v1c0 1-1 1-2 1v4 3l-1 1v-3-5h0v-3-4-3h0c0-1 0-2-1-3h0z" class="H"></path><path d="M176 461c-1 0-1 0-1-1v-5l8 1h1v2c0 1 0 1 1 1 0 1 0 1-1 2l1 1h-1l-4 1c-2-1-4-1-6-1v-1h2 0z" class="L"></path><path d="M176 461h8l1 1h-1l-4 1c-2-1-4-1-6-1v-1h2z" class="Z"></path><path d="M176 461c-1 0-1 0-1-1v-5l8 1c1 1 0 2-1 3l-1 1-1-1c-1 0-2 0-3 1 0 0-1 0-1 1z" class="O"></path><path d="M172 447v-1h3l2 2c1 0 2-1 2-1 1 1 2 1 3 0l1 1c-1 1-1 2-1 3v2 1l-2-2v1 1h-2-2 0c-1-1 0-3-1-4l-1-1c-1 0-2 2-2 3v1c0 1 0 2-1 2h0-1c-1-1 0-2-1-3v-1c0-1 0-1 1-2 1 0 2-1 2-2z" class="C"></path><path d="M176 454c0-1 1-2 1-3 1-1 2-2 4-3h1v3 2 1l-2-2v1 1h-2-2z" class="O"></path><path d="M148 147l1 1c3-1 6-1 8-1h3l1-1c0-1-1-1-1-1 0-1 0-1 1-1 2 0 1 1 3 2h2l1 1v1s-1 1-1 2h2 4v1 4h0v3h2 0l-1 1c1 1 1 1 1 2l-1 1v1h1c1 2 0 4 1 5v6 2l-1 1v2c0 1-1 1-1 2h0s-1 0-1 1v6 7c-1-1-3-1-4-1v2h-5c-1-1-1-1-2-1h-2-1l-1 2h1v7l-3 3v-1l-3-3h0l-2-2-2-1 1-2v-1c-2-1-2-1-2-3-1 1-1 1-1 2-1 0-2-1-3-1s-1-1-2-1l-1 1h-1v-1c0-3-2-3-4-5l-11-7c-17-11-35-18-55-23l-12-2h11c1 0 3 1 5 1 2-1 6-1 9-1h-1 1c5-4 8-3 13-3l1-6c2-1 4 0 6-1l1 1 8 10h10 0 3v-2-1c1-1 1-2 2-2 0 0 0-1 1-1s3 0 4-1 2-1 3-1c1-1 2-1 4-2 1-1 4-1 5-2 0 0 0-1 1-1 1-1 2-1 3 0s1 1 1 2z" class="E"></path><path d="M164 166v-3l1-1c0 1 0 1 1 2s1 1 2 1c-1 1-3 0-4 1h0z" class="i"></path><path d="M149 198l1 1c1 0 2 0 2 1v3l-2-2-2-1 1-2h0z" class="U"></path><path d="M164 166h0c1 0 2 1 2 2-1 0-1 0-2 1l-1 1-1-1c0-1-1-1-1-2h0c1 0 2 0 3-1z" class="B"></path><path d="M131 161h0c3-1 8 4 10 6l-1 1v-1h0l-3-3h-1v1h0 0l-1-1c-2-1-3-2-4-3z" class="j"></path><path d="M149 169c0 1 1 1 2 2v4c-1 0-1 1-1 2 0 2 0 4-1 5 1-1 1-3 0-4v-5-4z" class="O"></path><path d="M147 192h0c1 0 1 0 2-1v-2h-1v-1h1 0c0 1 0 1 1 2 0 1 0 6-1 8h0v-1c-2-1-2-1-2-3v-2h0z" class="M"></path><path d="M147 172c-1-3 0-6 0-9 0-1-1-2 0-3h0c2 2 1 5 1 7l1 2v4l-1 3h-1v-1l-1-3h1z" class="l"></path><path d="M147 172h0c2-1 0-3 1-5l1 2v4l-1 3h-1v-1l-1-3h1z" class="Z"></path><path d="M154 195c1 0 2 0 3 1v1h1v7l-3 3v-1c1-2 0-6-1-7v-4z" class="B"></path><path d="M153 186v-4l-2-2 1-1c1-1 1-2 1-2v-1c0-1-1-1 0-2 0-2 1-8 0-9h0 1v3h0v-3h1v1c0 2 1 2 2 3h0v1h-3v7 7c-1 1-1 1-1 2z" class="n"></path><path d="M147 175v1h1l1-3v5c1 1 1 3 0 4h-1v1c1 1 2 0 2 2 0 0-1 1 0 2l-1 1h-1v1h1v2c-1 1-1 1-2 1h0v-17z" class="G"></path><path d="M161 167c0 1 1 1 1 2l1 1-1 1h0c-1-1-1-1-2-1 0 0 0 1-1 1h-2 0 0v3h0c1 1 1 2 2 3l1-1s1 0 2 1c0-1 1-1 1-1v2l1 1s0 1 1 2h-2c-2 0-3 1-5 1h0l-4 2h0v-7-7h3v-1l4-2z" class="F"></path><path d="M154 177h2l-1-1h0c-1-1 0-3 0-4h0c1 0 1 1 1 1 0 2 2 3 2 4-1 1-2 1-2 2h1l-1 1h0l2 2h0l-4 2h0v-7z" class="b"></path><path d="M163 176v2l1 1s0 1 1 2h-2c-2 0-3 1-5 1h0 0l-2-2h0l1-1h-1c0-1 1-1 2-2l2 1h0l-1-1 1-1s1 0 2 1c0-1 1-1 1-1z" class="a"></path><path d="M158 177l2 1v2h-1l-2-1h-1c0-1 1-1 2-2z" class="X"></path><path d="M102 147l1 1v1s1 1 1 2c2 2 4 4 6 7-2 1-34 0-37 0 2-1 6-1 9-1h-1 1c5-4 8-3 13-3l1-6c2-1 4 0 6-1z" class="P"></path><path d="M142 180c1-1 2-2 4-3v-4-1l1 3v17h0v2c-1 1-1 1-1 2-1 0-2-1-3-1s-1-1-2-1l-1 1h-1v-1c0-3-2-3-4-5l-11-7c1-1 2 0 3 0l3 3c1-1 2-1 2-1 2-1 5-2 6-3h2v-1h0 2z" class="O"></path><path d="M142 180c1-1 2-2 4-3v-4-1l1 3v17h0l-2-1c-1 0-1 0-3 1 0 0-1 0-1-1s0-1 1-1h1c0-1 1-1 1-1 1 0 1-1 2-1v-2l-1-1-3 3c-1 1-2 1-2 1l-1-1 1-1h1c1 0 3-3 4-4l-2-1c-1-1-1-1-1-2h0z" class="C"></path><path d="M143 146c0 1 0 1 1 2s2 1 2 3v1l-1 1c1 1 1 1 2 1 0 0 1 1 1 2 1 1 1 1 1 2h-3-4-21 0 3v-2-1c1-1 1-2 2-2 0 0 0-1 1-1s3 0 4-1 2-1 3-1c1-1 2-1 4-2 1-1 4-1 5-2z" class="Y"></path><path d="M124 156c1 0 1 0 2-1 2-2 5-3 8-3h0c-1 1-3 1-3 1-1 1-1 1-1 2-1 1-1 1-1 2v1h8 5-21 0 3v-2z" class="J"></path><path d="M143 146c0 1 0 1 1 2-3 0-7 2-10 3v1c-3 0-6 1-8 3-1 1-1 1-2 1v-1c1-1 1-2 2-2 0 0 0-1 1-1s3 0 4-1 2-1 3-1c1-1 2-1 4-2 1-1 4-1 5-2z" class="l"></path><path d="M171 156c0-1-1-1-1-1h0v-1h1l1 1v3h2 0l-1 1c1 1 1 1 1 2l-1 1v1h1c1 2 0 4 1 5v6 2l-1 1v2c0 1-1 1-1 2l-1-1c-1 1-3 1-4 1h-3c-1-1-1-2-1-2l-1-1v-2s-1 0-1 1c-1-1-2-1-2-1l-1 1c-1-1-1-2-2-3h0v-3h0 0 2c1 0 1-1 1-1 1 0 1 0 2 1h0l1-1 1-1c1-1 1-1 2-1h1 3 1l1-1c0-2 0-7-1-9v-2z" class="H"></path><path d="M164 169c1-1 1-1 2-1h1l1 2h1l1 2h-2c-1-1-1-1-2 0h0c-2-1 0-1-1-2 0 0 0-1-1-1z" class="F"></path><path d="M171 156c0-1-1-1-1-1h0v-1h1l1 1v3h2 0l-1 1c1 1 1 1 1 2l-1 1v1h1c1 2 0 4 1 5v6 2-3h-2v-2h2v-3l-1 1v1l-1-1h-1c0 1-1 2-1 3v1-1c-1 0-1 0-1-1v1l-1-2h-1l-1-2h3 1l1-1c0-2 0-7-1-9v-2z" class="h"></path><path d="M170 168c0 1 0 2 1 2l1-1c0-1-1-1 0-2h1v2h-1c0 1-1 2-1 3v1-1c-1 0-1 0-1-1v1l-1-2h-1l-1-2h3z" class="N"></path><path d="M157 174h0v-3h0 0 2 1 0c2 0 2 0 3 1h1c1 1 1 1 1 2h4c1 0 1 0 2 1v4l1-1v-4h2c0 1-1 1-1 2v2c0 1-1 1-1 2-1 1-3 1-4 1h-3c-1-1-1-2-1-2l-1-1v-2s-1 0-1 1c-1-1-2-1-2-1l-1 1c-1-1-1-2-2-3z" class="S"></path><path d="M157 174h0v-3h0 0 2 1 0c2 0 2 0 3 1h1c1 1 1 1 1 2l-1-1-2 2-1 1c-1-1-1-2-2-2 0-1 0-1-1-1h0l-1 1z" class="G"></path><path d="M163 176l1-1 2 2c1 0 3-1 4-1v1h0c-1 0-2 1-3 2v1c1 0 3-2 3-3h1v3c-1 0-2 0-3 1h-3c-1-1-1-2-1-2l-1-1v-2z" class="c"></path><path d="M148 147l1 1c3-1 6-1 8-1h3l1-1c0-1-1-1-1-1 0-1 0-1 1-1 2 0 1 1 3 2h2l1 1v1s-1 1-1 2h2 4v1 4h0l-1-1h-1v1h0s1 0 1 1v2h-5-1-15l-1-3c0-1-1-1-1-1-1-2 0-5 0-7z" class="J"></path><path d="M157 152v-3h1l1 2-2 1z" class="T"></path><path d="M168 150h4v1 4h0l-1-1h-1v1h0s1 0 1 1v2h-5-1v-4l-1-1v-1c0-1-1-1-1 0-3 0-7 2-10 1h0c1-1 3-1 4-1l2-1c1 0 2 0 4-1v1c1 0 4-1 5-1z" class="G"></path><path d="M172 151v4h0l-1-1h-1v1h0s1 0 1 1v2h-5l1-1v-2h0l-3-3c3 0 6-1 8 1v-2z" class="J"></path><path d="M167 155h1c0 1 1 1 2 2h1v-1 2h-5l1-1v-2h0z" class="c"></path><path d="M172 180l1 1h0s-1 0-1 1v6 7c-1-1-3-1-4-1v2h-5c-1-1-1-1-2-1h-2-1l-1 2v-1c-1-1-2-1-3-1v-2c-1-2-1-4-1-7 0-1 0-1 1-2h0l4-2h0c2 0 3-1 5-1h2 3c1 0 3 0 4-1z" class="G"></path><path d="M155 191c1 2 1 3 2 5-1-1-2-1-3-1v-2-1l1-1zm4 4l-1-1v-1c2 1 3 1 5 1 0-1 1-1 1-1h1l1 1h0 0 2v2h-5c-1-1-1-1-2-1h-2z" class="L"></path><path d="M158 182h0l1 1c-1 0-1 1-1 1h-1c-1 0-2 1-2 1v6l-1 1v1c-1-2-1-4-1-7 0-1 0-1 1-2h0l4-2z" class="B"></path><path d="M162 184l1-2 1 2c1 0 1 0 2 1h-1c-1 2-1 4-1 6 0 0-1 1-2 1-1-1-1-2-2-3 0-2-2-2-2-4h0l1 1h1c1 0 2-1 2-2z" class="a"></path><path d="M162 184l1-2 1 2c1 0 1 0 2 1h-1c-2 1-4 1-6 1h1c1 0 2-1 2-2z" class="l"></path><path d="M172 180l1 1h0s-1 0-1 1v6 7c-1-1-3-1-4-1h-2 0 0l-1-1c0-1 0-2-1-2 0-2 0-4 1-6h1c-1-1-1-1-2-1l-1-2-1 2h0c-1-1-2-1-3-1l-1-1c2 0 3-1 5-1h2 3c1 0 3 0 4-1z" class="V"></path><path d="M168 182h2c1 1 0 1 0 2h-1-2-1l2-2z" class="k"></path><path d="M169 184h1c1 1 0 2 0 4l1 5v1h-5 0c1-1 1-2 2-2h2v-1c0-2-1-2-2-4 0-1 1-2 1-3z" class="Z"></path><path d="M172 180l1 1h0s-1 0-1 1v6 7c-1-1-3-1-4-1h-2 0 5v-1l-1-5c0-2 1-3 0-4 0-1 1-1 0-2h-2-5l-1 2h0c-1-1-2-1-3-1l-1-1c2 0 3-1 5-1h2 3c1 0 3 0 4-1z" class="N"></path><path d="M194 194c1-1 2-1 2-1 1 1 1 2 2 3h1 4c1-1 1-1 2 0l2-1c2 0 3 0 4-1l1 1c-1 1-1 1 0 2s3 1 5 0l1 1h3l-1 1v2h-1v1l-1 1 2 1v1 1c0 1-1 1-1 2v1c1 1 2 1 2 1v1 1c1 0 1 1 2 2 0 1 1 1 1 2v2c1 1 1 1 2 1 1 1 0 6 0 8v2 3c0 2-1 4 0 6s1 4 1 5l-1 8h1l-1 7 1 15v20h-1c0 2 1 4 0 6v2c1 2 1 5 0 7l1 10v2 10c0 2 0 3-1 4h0 0l-2 2-7 7-2 1-1-1-2-1v-2-4-1-1c0-2 0-4 1-6h0 0v-1c-1-1-4 0-5 0 1-2 1-2 1-4h-1c-1 0-1 0-1 1l-2-1v-2h-1-2l-1-1c0-1 0-2-1-3h-1-1v-3-1-3-2l-1-1c1-2 0-3 0-4 1-2 1-1 0-3l1-1v-3h0c0-1 0-2-1-3 2 0 4 0 5 1v3c1-1 1-2 1-4-1 0 0 0 0-1v-1h1v-2-1-8-1c1-1 0-2 0-3 0 0 0 1-1 1v-1-6-7-6c0-2 0-4-1-6l1-1v-4c-1 0-2-1-3-2h-1-1v-2h0l1-2v-3c-2 0-2-1-4 0l1-3c-2-2-1-5-1-7v-17h0 1c0-2 0-2-1-3v-1c0-2 0-3 1-4v-1h-1v-1-3c-1-1 0-1 0-2l-1-1z" class="T"></path><path d="M214 231h1v3l-2-2 1-1z" class="Q"></path><path d="M214 236h1v1 1l-1 1h-1c1-1 1-2 1-3z" class="P"></path><path d="M217 260h1l-1 1c0 1-2 2-3 4 0 0 0 2-1 2h-1l-1-1c1-2 2-1 3-2s1-3 3-4z" class="X"></path><path d="M219 258c0-2-1-2-2-3 0-1 1-1 1-2h1 1v-1l1 1c-1-1-1-2 0-3v-5c0 2 1 5 0 7-1 1 0 4 0 5-1 0-2 1-2 1z" class="P"></path><path d="M209 224l1-3c1 0 1-1 2-1-1 2-1 4-2 6 0 2 0 4-1 6v1c0 1-1 1-1 2v-4c0-2 0-4 1-7z" class="X"></path><path d="M222 235h1c1 2 2 2 2 4-1 4-2 7-2 11l-1 3-1-1c1-2 0-5 0-7 1-3 2-7 1-10z" class="D"></path><path d="M223 235l2-1 1-2c0 2-1 4 0 6s1 4 1 5l-1 8v-4h0-1c-1 1-1 2-2 3 0-4 1-7 2-11 0-2-1-2-2-4z" class="b"></path><path d="M223 250c1-1 1-2 2-3h1 0v4h1l-1 7h0c0-1 0-1-1-1h-2l-1 1v-5l1-3z" class="X"></path><path d="M211 218c1-1 2-1 3-1 0 0 1 1 1 2 1 1 1 2 1 4 1 3 4 8 6 9h1c1 0 1 0 2-1 1 0 1-1 1-2v3l-1 2-2 1h-1c-1 0-4-7-5-8s-1-3-2-4-1-2-1-3h-2c-1 0-1 1-2 1l-1 3v-3-2h1l1 1 1-2h-1z" class="W"></path><path d="M209 215c0 2-1 5 0 6v3c-1 3-1 5-1 7v4c-1 3-2 7-2 11 0 1 0 2-1 3v-3-8c0-2 0-3 1-5v-2c1-2 1-4 1-5s1-3 0-4c0-2 1-5 2-7z" class="S"></path><path d="M210 207l1 1c2 2 2 5 3 8v1l2-1c1 1 1 1 0 2 0 0-1 0-1 1 0-1-1-2-1-2-1 0-2 0-3 1h1l-1 2-1-1h-1v2c-1-1 0-4 0-6v-3l1-5z" class="b"></path><path d="M211 218h-1v-1l1-1c0-1 0-1 1-1l1 1h1v1l2-1c1 1 1 1 0 2 0 0-1 0-1 1 0-1-1-2-1-2-1 0-2 0-3 1z" class="J"></path><path d="M219 258s1-1 2-1c0-1-1-4 0-5l1 1v5l1-1h2c1 0 1 0 1 1h0l1 15v20h-1 0v-12-4-17c-2 1-3 1-4 3v3l-2-1s0-1-1-1l1-1v-2c0-2 0-2-1-2v-1z" class="f"></path><path d="M222 258l1-1h2c1 0 1 0 1 1v1l-3 1c-1-1-1-1-1-2z" class="Q"></path><path d="M206 246h0 2l1 2c0 1 0 3-1 4 0 1-1 1-1 1v1 3h0l2 1c-1 1-1 2-1 4-1 2 0 5-1 7 1 1 1 1 2 1v-2c1 0 2 1 2 1v2c-1 0-2 2-2 2-2 0-3 0-4-1v-8-15c1-1 1-2 1-3z" class="b"></path><path d="M206 246h2l1 2c0 1 0 3-1 4 0 1-1 1-1 1v1 3h0l2 1c-1 1-1 2-1 4-1 2 0 5-1 7 1 1 1 1 2 1h0v1c-1-1-2-1-3-1v-1c1-2 1-5 1-7v-3c-1-1 0-3 0-4-1-2-1-1 0-3v-2c0-1 0-2 1-3-1 0-1-1-2-1z" class="X"></path><path d="M211 194l1 1c-1 1-1 1 0 2s3 1 5 0l1 1c-2 0-4 0-6 1-1 1-1 3-1 4s0 2-1 3v1l-1 5v3c-1 2-2 5-2 7 1 1 0 3 0 4s0 3-1 5v2l-1-15c0-2-1-5-1-7 0-1 0-1 1-2l-1-1v-2l1-1v-6-2-1l2-1c2 0 3 0 4-1z" class="H"></path><path d="M209 203h1c1 1 0 2 0 3v1l-1 5s-1 0-1-1c1-2 1-6 1-8z" class="D"></path><path d="M205 205h1c0 1 1 1 0 2v2c0 1 1 3-1 4 0 2 1 4 0 5 0-2-1-5-1-7 0-1 0-1 1-2l-1-1v-2l1-1z" class="N"></path><path d="M211 194l1 1c-1 1-1 1 0 2s3 1 5 0l1 1c-2 0-4 0-6 1-1 1-1 3-1 4s0 2-1 3c0-1 1-2 0-3h-1 0l-1-2h1v1h1v-1l-1-2c-1-1-1 1-3 1 0-1 1-1 1-2v-1h-2v-1l2-1c2 0 3 0 4-1z" class="L"></path><defs><linearGradient id="U" x1="205.983" y1="242.561" x2="201.276" y2="242.883" xlink:href="#B"><stop offset="0" stop-color="#292925"></stop><stop offset="1" stop-color="#3a383d"></stop></linearGradient></defs><path fill="url(#U)" d="M203 210l1 1c0 2 1 5 1 7l1 15c-1 2-1 3-1 5v8 3 15 8c1 1 2 1 4 1-1 1-2 1-2 2l-3 1s0 1-1 1v-1-6-7-6c0-2 0-4-1-6l1-1v-4-5-4-9c0-2-1-4 0-7v-11z"></path><path d="M222 266v-3c1-2 2-2 4-3v17 4h-1-1c-2 2-3 4-5 5s-3 1-4 2l-1-1 3-9h0-1v-2l1-1v-2l1-1v-2c1-2 1-3 2-4v-1l2 1z" class="Q"></path><path d="M220 265l2 1-1 1c1 1 1 1 1 2-1 2-1 3 0 5 1 1 3 2 4 3v4h-1-1c1-1 1-1 1-3 0 0-1-1-2-1-1-1-1-1-2 0v-1c-2 0-3 1-4 2h0-1v-2l1-1v-2l1-1v-2c1-2 1-3 2-4v-1z" class="P"></path><path d="M220 266l1 1-3 6v1c-1 1-1 3-1 4h-1v-2l1-1v-2l1-1v-2c1-2 1-3 2-4z" class="f"></path><path d="M217 278c1-1 2-2 4-2v1c1-1 1-1 2 0 1 0 2 1 2 1 0 2 0 2-1 3-2 2-3 4-5 5s-3 1-4 2l-1-1 3-9z" class="m"></path><path d="M209 273s1-2 2-2v1c2 1 4 0 6 1v2l-1 1v2h1 0l-3 9c0 2-1 3-1 4l-1-1-1 2h-2c-1 0-2-1-3-1v-1c1-1 1-1 1-2l-1-1-2 1v-8-1c1-1 0-2 0-3l3-1c0-1 1-1 2-2z" class="J"></path><path d="M210 275h3v2h0c-1 0-2-1-3-2z" class="X"></path><path d="M213 275c2 1 2 1 3 1v2l-1 1-2-2v-2z" class="Y"></path><g class="T"><path d="M210 275v-1h0c3 0 4-1 6 0l1 1-1 1c-1 0-1 0-3-1h-3z"></path><path d="M204 276l3-1 1 1h0c1 1 2 1 3 1 1 1 2 1 2 2s0 1-1 1-1 0-2-1c-1 0-2 1-3 1h-2-1v-1c1-1 0-2 0-3z"></path></g><path d="M204 276l3-1 1 1h0v2c-1 0-1 0-2 1l-1 1h-1v-1c1-1 0-2 0-3z" class="X"></path><g class="Q"><path d="M206 287c0-1 0-3-1-4 1-2 3-2 4-3l1 1c1 1 3-1 4 1 0 1-1 1-2 2 1 1 2 0 2 2 0 1-1 1-1 1 0 1 0 1-1 2v1l-1 2h-2c-1 0-2-1-3-1v-1c1-1 1-1 1-2l-1-1z"></path><path d="M214 287l1 1c1-1 2-1 4-2s3-3 5-5h1 1v12h0c0 2 1 4 0 6l-1-1c-1 2-4 4-5 5l-4 3-1-1h-1l-1-2h0c-1-2 0-3-1-5h0v-1l-1 1-1-1v-2-1h0c-1-1-1-1-2-1l3-1h0l1-2 1 1c0-1 1-2 1-4z"></path></g><path d="M212 298h0 2c1 2-1 3 1 4-1 1-1 1-2 1-1-2 0-3-1-5h0z" class="W"></path><path d="M212 290l1 1v1h0c1 0 2 0 2-1 1 0 1 0 2-1h0c-2 2-4 3-6 5l1 2-1 1-1-1v-2-1h0c-1-1-1-1-2-1l3-1h0l1-2z" class="Z"></path><path d="M219 300c0 1 0 1 1 3l-4 3-1-1h-1l-1-2h0c1 0 1 0 2-1l4-2z" class="V"></path><path d="M214 287l1 1c1-1 2-1 4-2s3-3 5-5h1c-3 3-5 7-8 9h0 0c-1 1-1 1-2 1 0 1-1 1-2 1h0v-1c0-1 1-2 1-4z" class="T"></path><path d="M223 295c1-1 1-2 3-2h0c0 2 1 4 0 6l-1-1c-1 2-4 4-5 5-1-2-1-2-1-3v-1c1-1 3-2 4-4z" class="f"></path><path d="M223 295c1-1 1-2 3-2h0c0 2 1 4 0 6l-1-1v-1c-1 1-1 2-2 2l-1-1h1v-2-1z" class="J"></path><path d="M211 203c0-1 0-3 1-4 2-1 4-1 6-1h3l-1 1v2h-1v1l-1 1 2 1v1 1c0 1-1 1-1 2v1c1 1 2 1 2 1v1 1c1 0 1 1 2 2 0 1 1 1 1 2v2c1 1 1 1 2 1 1 1 0 6 0 8v2c0 1 0 2-1 2-1 1-1 1-2 1h-1c-2-1-5-6-6-9 0-2 0-3-1-4 0-1 1-1 1-1 1-1 1-1 0-2l-2 1v-1c-1-3-1-6-3-8l-1-1v-1c1-1 1-2 1-3z" class="Q"></path><path d="M217 204v-1l-2 1v-1l1-1h3l-1 1s0 1-1 1z" class="Y"></path><path d="M217 204c1 0 1-1 1-1l2 1v1c-1 1-2 1-3 0v-1z" class="J"></path><path d="M211 203c1 1 1 0 2 1 0 0 1 3 2 3-2 1-2-1-4 1l-1-1v-1c1-1 1-2 1-3z" class="P"></path><path d="M211 208c2-2 2 0 4-1 0 1 2 3 2 4v9h1c1-1 0-2 2-3l1-1 1 1-1 1c-1 1-1 4-1 5s1 1 1 1c0 2 1 3 2 4 0 2-1 3 0 4h-1c-2-1-5-6-6-9 0-2 0-3-1-4 0-1 1-1 1-1 1-1 1-1 0-2l-2 1v-1c-1-3-1-6-3-8z" class="Y"></path><path d="M206 287l1 1c0 1 0 1-1 2v1c1 0 2 1 3 1h2 0l-3 1c1 0 1 0 2 1h0v1 2l1 1 1-1v1h0c1 2 0 3 1 5h0l-1 1 1 1c-1 1-1 2-2 3 1 1 1 1 1 2-1 1-1 1-1 2 0 2 1 3 1 5-2 1-3 1-3 3v1h-4-1-2l-1-1c0-1 0-2-1-3h-1-1v-3-1-3-2l-1-1c1-2 0-3 0-4 1-2 1-1 0-3l1-1v-3h0c0-1 0-2-1-3 2 0 4 0 5 1v3c1-1 1-2 1-4-1 0 0 0 0-1v-1h1v-2-1l2-1z" class="Z"></path><path d="M198 311c1 0 1 0 2 1h1 0l1 2c0 1 0 2-1 2 0 1 0 2 1 3h0c1 0 1 0 1 1 1-1 1-2 1-3 1 1 0 2 0 4h0-2l-1-1c0-1 0-2-1-3h-1-1v-3-1-2z" class="k"></path><path d="M198 314l1 1 1-1v1s0 1 1 2h-1-1-1v-3z" class="H"></path><path d="M198 311c1 0 1 0 2 1h1 0c0 1 0 2-1 2l-1 1-1-1v-1-2z" class="I"></path><path d="M212 298c1 2 0 3 1 5h0l-1 1 1 1c-1 1-1 2-2 3 1 1 1 1 1 2-1 1-1 1-1 2 0 2 1 3 1 5-2 1-3 1-3 3v1h-4-1 0c1-1 1-1 2-1-1-1-1-1-1-2 1 1 1 1 2 1v-1c0-1 0-1 1-1v-1h0c1-1 1-2 1-3 0 0 0-1 1-2-1 0-1-1-1-2 1-1 1 0 2 0v-2-1c1-1 1-4 0-5v-2l1-1z" class="S"></path><path d="M206 287l1 1c0 1 0 1-1 2v1c1 0 2 1 3 1h2 0l-3 1c1 0 1 0 2 1h0v1 2l1 1 1-1v1h0l-1 1h-2c-1 1 0 1 0 2-1 2-2 2-2 4l-2 4c0-1 0-2-1-3 0 0 0-1-1-1 1-2 1-2 0-4 0-1 0-2 1-2 0-1 0-1 1-1l-1-1v-5-1-2-1l2-1z" class="V"></path><path d="M206 287l1 1c0 1 0 1-1 2v1c1 0 2 1 3 1h2 0l-3 1c1 0 1 0 2 1v1c-1 0-2 1-3 2h-1c0-1 0-1 1-2-1 0-1-1-1-1v-1-1h-2v-1-2-1l2-1z" class="b"></path><path d="M206 287l1 1c0 1 0 1-1 2v1c1 0 2 1 3 1-1 0-3 0-4-1l1-1c-1-1-1-1-2-1v-1l2-1zm-3 4h1v1 5l1 1c-1 0-1 0-1 1-1 0-1 1-1 2l-1 7c0 1 0 2-1 3v1h-1c-1-1-1-1-2-1v2-3-2l-1-1c1-2 0-3 0-4 1-2 1-1 0-3l1-1v-3h0c0-1 0-2-1-3 2 0 4 0 5 1v3c1-1 1-2 1-4-1 0 0 0 0-1v-1z" class="Y"></path><path d="M200 305c-1 0-1-1-1-2-1-1-1-2 0-3h1c1 0 1 1 2 2-1 1-1 2-2 3z" class="Q"></path><path d="M202 294v3h0v1h-3v-3c1 0 2-1 3-1z" class="m"></path><path d="M203 291h1v1 5l1 1c-1 0-1 0-1 1-1 0-1 1-1 2l-1 7c0 1 0 2-1 3v1h-1c-1-1-1-1-2-1 0-2 1-4 2-6 1-1 1-2 2-3v-5h0c1-1 1-2 1-4-1 0 0 0 0-1v-1z" class="H"></path><defs><linearGradient id="V" x1="194.821" y1="213.474" x2="199.245" y2="214.312" xlink:href="#B"><stop offset="0" stop-color="#333433"></stop><stop offset="1" stop-color="#504d4f"></stop></linearGradient></defs><path fill="url(#V)" d="M194 194c1-1 2-1 2-1 1 1 1 2 2 3h1 4c1-1 1-1 2 0v1 2 6l-1 1v2l1 1c-1 1-1 1-1 2l-1-1v11c-1 3 0 5 0 7v9 4 5c-1 0-2-1-3-2h-1-1v-2h0l1-2v-3c-2 0-2-1-4 0l1-3c-2-2-1-5-1-7v-17h0 1c0-2 0-2-1-3v-1c0-2 0-3 1-4v-1h-1v-1-3c-1-1 0-1 0-2l-1-1z"></path><path d="M196 228l1-3 1 1v5 1l1 5c-2 0-2-1-4 0l1-3v-6z" class="d"></path><path d="M198 220h3l1 1c0 1 0 1-1 2h0 1v1 1l-2-1v1h0v2c0 2 0 2-1 3l1 1c1 0 0 1 0 1h-2v-1-11z" class="a"></path><path d="M195 210h1c1 4 1 8 0 11 0 2 1 3 0 5v2 6c-2-2-1-5-1-7v-17z" class="V"></path><path d="M200 232s1-1 0-1l-1-1c1-1 1-1 1-3v-2h0c2 1 2 2 3 3v9h-3l-1-1c0-1 1-1 2-2l-1-1v-1z" class="b"></path><path d="M203 210h0v11c-1 3 0 5 0 7-1-1-1-2-3-3v-1l2 1v-1-1h-1 0c1-1 1-1 1-2l-1-1h-3l1-2c-1-1 0-3 0-5 0 1 0 2 1 2h1v-2c1-1 1-2 2-3z" class="g"></path><path d="M199 218l2-2 1 1c0 1-1 1-1 3h-3l1-2zm-1 14h2v1l1 1c-1 1-2 1-2 2l1 1h3v4 5c-1 0-2-1-3-2h-1-1v-2h0l1-2v-3l-1-5z" class="V"></path><path d="M199 240c1-1 0-1 1-2 1 0 2 1 2 1v1c-1 0-2 1-3 2v1l1 1h-1-1v-2h0l1-2z" class="f"></path><path d="M199 243l3-2h1v5c-1 0-2-1-3-2l-1-1z" class="X"></path><path d="M194 194c1-1 2-1 2-1 1 1 1 2 2 3h1 4c1-1 1-1 2 0v1 2 6l-1 1v2l1 1c-1 1-1 1-1 2l-1-1h0c-1 1-1 2-2 3v2h-1c-1 0-1-1-1-2-1-3-1-7-1-9 0-1-1-1-2-2v-1h-1v-1-3c-1-1 0-1 0-2l-1-1z" class="V"></path><path d="M203 204h1v2 2l1 1c-1 1-1 1-1 2l-1-1h0v-1-2-3z" class="L"></path><path d="M204 199h1v6l-1 1v-2h-1c0-2 0-3 1-5z" class="F"></path><path d="M194 194c1-1 2-1 2-1 1 1 1 2 2 3h1 4c1-1 1-1 2 0v1 2h-1-2l-2 1c-1-1 0-2-2-1h1c0 1-1 3-1 5 0-1-1-1-2-2v-1h-1v-1-3c-1-1 0-1 0-2l-1-1z" class="D"></path><path d="M195 200l2-1v-1s3 0 4-1h0l1 1v1l-2 1c-1-1 0-2-2-1h1c0 1-1 3-1 5 0-1-1-1-2-2v-1h-1v-1z" class="L"></path><path d="M225 298l1 1v2c1 2 1 5 0 7l1 10v2 10c0 2 0 3-1 4h0 0l-2 2-7 7-2 1-1-1-2-1v-2-4-1-1c0-2 0-4 1-6h0 0v-1c-1-1-4 0-5 0 1-2 1-2 1-4h-1c-1 0-1 0-1 1l-2-1v-2h4v-1c0-2 1-2 3-3 0-2-1-3-1-5 0-1 0-1 1-2 0-1 0-1-1-2 1-1 1-2 2-3l-1-1 1-1 1 2h1l1 1 4-3c1-1 4-3 5-5z" class="Q"></path><path d="M214 319c1 1 3 0 4-1l1 1v2c-1 0-3 0-4 1h0v-2s-1 0-1-1z" class="e"></path><path d="M219 321h2l-1 1-2 1-1 1-1-1-1-1c1-1 3-1 4-1z" class="Z"></path><path d="M213 303l1 2h1l1 1h0s0 1 1 2h-1 0c-1-1-1-1-2-1-1 2-1 8 0 11v1c0 1 1 1 1 1v2h0l1 1 1 1-2 1-2 3v-1c-1-1-4 0-5 0 1-2 1-2 1-4h-1c-1 0-1 0-1 1l-2-1v-2h4v-1c0-2 1-2 3-3 0-2-1-3-1-5 0-1 0-1 1-2 0-1 0-1-1-2 1-1 1-2 2-3l-1-1 1-1z" class="M"></path><path d="M209 321c1 0 2 0 2-1v-1l2-2 1 1v1c0 1 1 1 1 1v2h-3c-1 0-3 0-4 1-1 0-1 0-1 1l-2-1v-2h4z" class="L"></path><path d="M215 322h0l1 1 1 1-2 1-2 3v-1c-1-1-4 0-5 0 1-2 1-2 1-4h-1c1-1 3-1 4-1h3z" class="k"></path><path d="M208 323c1-1 3-1 4-1-1 1-1 0-2 2l1 2h2v1c-1-1-4 0-5 0 1-2 1-2 1-4h-1z" class="H"></path><path d="M218 318h4 5v2 10c0 2 0 3-1 4h0 0l-2 2-7 7-2 1-1-1-2-1v-2-4-1-1c0-2 0-4 1-6h0 0l2-3 2-1 1-1 2-1 1-1h-2v-2l-1-1z" class="Q"></path><path d="M226 334v-2c-1-1-2 0-2-2 1 0 2-1 2-1v5h0zm-8-11c0 1 1 1 1 2 1 0 1-1 2 1h0l1 1-1 2 1 1h-1-3-1v-1h3l-1-2 1-1-1-1h-1v1c-1 0-2 0-3-1l2-1 1-1z" class="P"></path><path d="M213 328v1c1 1 1 1 3 2h0v1c1 0 3 0 4 1v1h-3c-1-1-1-1-2 0-1 0-2 0-2 1h0l-1 1v-1-1c0-2 0-4 1-6z" class="Y"></path><path d="M213 329c1 1 1 1 3 2h0v1h-3v-3z" class="T"></path><path d="M214 343l1-1 1-1c1-1 1-1 1-3l1-1 2 2v-1c0-1 0-1-1-2l1-1c1 0 1 0 2 1h2l-7 7-2 1-1-1z" class="X"></path><path d="M218 318h4 5v2 10c0 2 0 3-1 4v-5-5h-2-1-1v-1l-2-1 1-1h-2v-2l-1-1z" class="H"></path><path d="M222 321c1-1 2-1 3-1h1v4h-2c0-2 1-2 1-3h0c-1-1-2 0-3 0z" class="J"></path><path d="M222 321c1 0 2-1 3 0h0c0 1-1 1-1 3h-1-1v-1l-2-1 1-1h1z" class="Y"></path><path d="M197 462h1 3 0c1 1 1 1 2 1l3 3v1-1h1v1c1 1 1 5 1 6v6c1 0 1-1 1-1h1v4 2l-2 1v2c1 2 1 1 1 3 1 4 0 9 1 13v-3l3 3v1h2c1 0 1-1 2-1h0c1 0 2 0 3 1l1 1c0 1 1 1 1 1v2c0 1 0 1 1 1v-1l1-1c1 0 1 1 1 1v1c1 0 1 0 1-1v9 1 3 4 1c1 3 0 7 0 10l1 24v1c1 2 2 3 4 5v1 2h-1c2 4 4 8 7 11-1 1-1 2-2 3 1 2 1 3 0 4l1 1c-1 1-2 1-2 2-1 0-1 1-2 2s-3 1-4 2l-2-1c-1-1-2-2-2-3l-2-2v-1c-1-1-2-1-2-2l-2 1c-2-1-2-1-4-1h0v1h-2c0 1 1 2 1 3-2-1-3-3-5-4-1 0-1-1-2-2h-2 0l-2-3-2 2v1h-3s-1-1-1-2c-1 0-1 0-1-1h-1v-2s-1 0-1-1l1-1-1-1h-1c-1 0-1-1-1-2h-1s0 1 0 0c-2 0-2-1-3-2v-1-1h2 1v-2h-1v-1c-1-1-2-1-3-1-3 0-10 1-12-1l2-2 1-1h1 1c0-1 0-1-1-2h0c0-1 1-1 1-1 0-1-2-3-2-4-1-2-2-3-3-4-2 0-2-1-3-2-5-1-12 0-17 0-3 0-8-1-12 0h-4l-1 1s0-1-1-1v-1h-18l2-3c0-1 1-2 2-4l2-3h1l4-1 4-2h1l4-2c5-2 10-4 15-4-1-2-1-5-1-7l-4-3v-1h2 0c1 1 3 2 4 3 2 0 4-1 5 0h2v-2-5c0-1-1-2-1-3v-2l-1-5v-1h-1v1 1l-1-1v-1-3h0c-1-1-1-1 0-2v-1-1c0-1 1-2 2-3v3 1 3c2 0 4-1 5 0h0c2-1 2-2 2-4 1 1 1 0 1 1 1 1 1 1 1 2h1 0c0-1 1-1 2-1v-2l1-1v-1h0l1-1h-1v-6c0-1 0-2 1-3v-1h-1v-1h1c0-2 0-2-1-4h0c1 0 1 0 2-1h0v-1c1 0 2 0 3-1 2-1 3-2 5-3 1 1 2 0 3 1h0l1 1h3l1-1v-1c0-1 1-1 1-2s-1-2-1-2l-2-2 1-1c1-2 5-1 8-2z" class="U"></path><path d="M184 535c2 0 2 0 3 1l-1 1h-2v-2z" class="C"></path><path d="M175 520c1 0 2 0 3 1v2c-1 0-3 0-4-1h1c1-1 1-2 0-2z" class="B"></path><path d="M169 527c0-1 1-1 2-1v3l-1 1-1 1v-4z" class="E"></path><path d="M191 570c1 1 2 3 3 4l-1 1h-1c-1 0-1-1-1-2l-1-1v-1l1-1z" class="N"></path><path d="M194 576c2 1 2 1 3 2 0 1 1 1 1 2 1 1 1 1 2 1v1h-1s-1-1-2-1c-1-2-1-3-3-3v2-2s-1 0-1-1l1-1z" class="C"></path><path d="M187 569h2l2 1-1 1v1l1 1h-1s0 1 0 0c-2 0-2-1-3-2v-1-1zm3-48v-1-3h0c1 0 1 1 2 1 1 1 1 0 2 0v1c-1 1-1 1-2 1 0 0-1 1-2 1z" class="B"></path><path d="M192 520c1 0 2 0 2 1l1 1s1 0 1 1l-2 1h0v-1l-2 1-1-1-1-2c1 0 2-1 2-1z" class="h"></path><path d="M187 523c1 0 1-1 2-2h1l1 2 1 1h-1v1 1 1l-1-1-1-1h-2v-2z" class="E"></path><path d="M185 494h1v5h-4-1v-1-2-1c0-1 2 0 3 0 0 0 0-1 1-1z" class="Z"></path><path d="M172 533l1 1c1 2 1 4 2 6-1 2 0 4-2 6-1 0-1 1-1 2-1-1-1-2-1-3l1-1h0c1 0 1-1 1-2l-1-1-1 1c1-2 2-3 2-4v-2c-1 0-1-1-1-1v-2z" class="K"></path><path d="M181 544v1h-1s-1-2-1-3-1-3-2-5v-1h0v-2h-1c0-1 0-2 1-3 0 1 1 2 1 4l1 2 3 3c0 1 0 2-1 2v2z" class="C"></path><path d="M169 531l1-1c1 1 1 2 1 3h1v2s0 1 1 1v2c0 1-1 2-2 4v1h-1c0-2-1-4-1-6v-3-3z" class="i"></path><path d="M171 533h1v2s0 1 1 1v2c-1 0-2-1-3-1 0-1 0-3 1-4z" class="C"></path><path d="M178 494v-1c-1-2-2-3-1-5h1c1 1 0 1 1 2 0-1 1-1 2-1h3c2 0 3 0 3 1v3l2 1-1 3v-1c0-2-1-2-2-3 0-1 0-1 1-2l-1-1c-1 0-4-1-5 0-1 0-1 0-1 1h1s1 0 1-1h0c2 0 3 0 4 1l-1 1h-3 0c-2 0-3 0-4 2z" class="E"></path><path d="M182 483c3 1 4 0 6 0 0 1 0 4 1 4v1s0 1-1 2c0 1 1 3 1 4l-2-1v-3c0-1-1-1-3-1h-3v-5l1-1h0z" class="b"></path><path d="M182 483c3 1 4 0 6 0 0 1 0 4 1 4v1s0 1-1 2c0 1 1 3 1 4l-2-1v-3c0-1-1-1-3-1h2c0-2 0-2-1-3 0-1 1-1 1-2-1 0-1 0-2 1l-2-2z" class="L"></path><path d="M174 483c1 1 1 1 2 1v1 2h4c1-1 1-2 1-3h0v5c-1 0-2 0-2 1-1-1 0-1-1-2h-1c-1 2 0 3 1 5v1h0c-2-1-3-1-4 0l-1 1v-1h-1v-1h0l1-1h-1v-6l2-3z" class="F"></path><path d="M175 525l1-1c1 1 1 2 2 2 2 0 3 1 4 1l2-2v-5c-1-1-2-6-2-8 2 0 2 0 3 1 1 2 0 4 2 5 0 1 1 1 0 2v3 2c-1 1-2 2-4 3h0-4 0c-2-1-3-2-4-3z" class="R"></path><path d="M185 513c1 2 0 4 2 5 0 1 1 1 0 2v3-2c-1 0-2 0-2-1s0-2 1-3c-1-1-1-2-2-2 0-1 1-1 1-2z" class="E"></path><path d="M177 531c-1-1-2-2-2-3s0-2-1-2h0l1-1c1 1 2 2 4 3h0 0c2 1 2 1 4 1h1v3c-1 0-1 0-1 1h1v1 1 2h-2v1c1 0 1 0 2 1 0 2 0 2-1 4h0c-1 0-1 1-2 1v-2c1 0 1-1 1-2l-3-3-1-2c0-2-1-3-1-4z" class="n"></path><path d="M188 496v1 1c1 0 1 1 0 2v3l1 1 3 3c1 0 2 0 2 1v1h2v1 3h0l1 3-1 1-2-2h-1c-1 1-2 1-3 1l-2-2c0-1-1-2-1-3-2-2-4 0-6-1v-1h1c0-1-1-2-1-3h-1 0v-1h1 0c0-1 1-2 2-2h2v-1h-3v-1h1 0 1l1 1 1-1h0c1 0 1 0 1 2l1-3s-1 0 0-1v-1c-1 0-1-1 0-1v-1z" class="C"></path><path d="M181 505l1-1c1 0 1-1 1-1 1 1 2 1 3 1 1 1 0 3 0 5h0c0-1 0-2-1-3h-1 0c-1-1-2-1-3-1z" class="B"></path><path d="M188 514h1 1 0 1l1-1c0-1 1-1 1-1h2c0 2-1 2-1 3h-1c-1 1-2 1-3 1l-2-2z" class="E"></path><path d="M181 505c1 0 2 0 3 1h0 1c1 1 1 2 1 3-2 1-3 1-5 1v-1h1c0-1-1-2-1-3v-1z" class="G"></path><path d="M181 505c1 0 2 0 3 1h0l-2 3c0-1-1-2-1-3v-1z" class="D"></path><path d="M188 503l1 1 3 3c1 0 2 0 2 1v1h2v1l-1-1-6 1v-1-2c-1-1-1-2-1-4z" class="F"></path><path d="M161 513l2 2v1 2l1-1c0 2 0 4-1 5 1 2 2 2 3 2h1 0c0-1 1-1 2-2h1v1h-1c-1 0-1 0-1 1l-1 1c-1 0-1 0-2 1l1 1h0c1-1 1-1 2-1l1 1v4 3 3h-2c-1-1-1-2-1-4s0-4-2-5h-1v-1c-2 1-4 1-6 2 0 0 0 1-1 1v-3c-1 0-2 1-3 1-1-2-1-5-1-7l-4-3v-1h2 0c1 1 3 2 4 3 2 0 4-1 5 0h2v-2-5z" class="j"></path><path d="M161 513l2 2v1 2 4h-1c0-1 0-3-1-4v-5zm-11 4c1 1 3 2 4 3l-1 1v1c1 1 1 1 2 1 0 1 1 1 1 2h1c0-1 2-1 3-1 1 1 2 1 2 2v1l-1-1c-1 0-2 0-2 1h-1v-1c-1 0-2 0-2 1-1 0-2 1-3 1-1-2-1-5-1-7l-4-3v-1h2 0z" class="C"></path><path d="M182 471c1 1 2 0 3 1h0l1 1h3v4h1c0 2 0 3 1 4h0 1l-1 1h-1c0 2 0 3 1 4l-1 2-1-1c-1 0-1-3-1-4-2 0-3 1-6 0h0l-1 1h0c0 1 0 2-1 3h-4v-2-1c-1 0-1 0-2-1l-2 3c0-1 0-2 1-3v-1h-1v-1h1c0-2 0-2-1-4h0c1 0 1 0 2-1h0v-1c1 0 2 0 3-1 2-1 3-2 5-3z" class="L"></path><path d="M186 480c-1 0-1-1-2-1h-1v-1h4v1c0 1-1 1-1 1z" class="B"></path><path d="M186 480s1 0 1-1v1l2 2-1 1c-2 0-3 1-6 0h0c1 0 1-1 2-2 0 1 0 1 1 1l1-2z" class="d"></path><path d="M174 476h4 0c-1 1-2 1-3 1-1 1 0 3 0 4s-1 1-1 2l-2 3c0-1 0-2 1-3v-1h-1v-1h1c0-2 0-2-1-4h0c1 0 1 0 2-1z" class="I"></path><path d="M186 473h3v4h1c0 2 0 3 1 4h0 1l-1 1h-1c0 2 0 3 1 4l-1 2-1-1c-1 0-1-3-1-4l1-1c-1-3 0-6-1-9h-2z" class="Z"></path><path d="M182 471c1 1 2 0 3 1h0 0c-2 1-2 2-3 4h-4-4 0v-1c1 0 2 0 3-1 2-1 3-2 5-3z" class="m"></path><path d="M178 480h-1c0-1 0-2 1-2l2 2v1l1 1v-1-1l3 1c-1 1-1 2-2 2l-1 1h0c0 1 0 2-1 3h-4v-2h2c0-1 0-2-1-2v-2l1-1z" class="B"></path><path d="M178 480c1 2 1 2 1 4l-1 1c0-1 0-2-1-2v-2l1-1z" class="F"></path><path d="M159 492v3 1 3c2 0 4-1 5 0h0c2-1 2-2 2-4 1 1 1 0 1 1 1 1 1 1 1 2h1 0c0-1 1-1 2-1v-2l1-1h1v1l1-1c1-1 2-1 4 0l-3 1c0 1 0 1 1 2s1 1 2 1c-1 1-1 2-1 3 1 1 1 2 2 2v1c0 1-1 1-1 1-1 1 0 3-1 3 0 1-1 1-1 1s1 1 1 2v1h0l1 1 1 1v1 1h0l-2 2v2c1 0 1 0 1 1-1-1-2-1-3-1 1 0 1 1 0 2h-1l-2-1c-1 0-2-1-3-1-2-1-4-3-5-5h-1l-2-2c0-1-1-2-1-3v-2l-1-5v-1h-1v1 1l-1-1v-1-3h0c-1-1-1-1 0-2v-1-1c0-1 1-2 2-3z" class="K"></path><path d="M173 495l1-1c1-1 2-1 4 0l-3 1h-1c-1 1-1 1-1 2 1 0 1 1 2 1h0l-1 1h0c0-1-1-1-1-1v-3z" class="R"></path><path d="M172 494h1v1 3s1 0 1 1c0 0-1-1-2 0l-1 1h0 0v-3h0v-2l1-1z" class="L"></path><path d="M171 500l1-1c1-1 2 0 2 0h0s-1 0-1 1c0 0 1 1 1 2s0 2-1 3v2c1 2 0 3 1 5h-1c-1 1-1 2-1 3v-2c0-3 1-5 0-8 0 0 1 0 1-1s-1-2-2-4z" class="D"></path><path d="M166 495c1 1 1 0 1 1 1 1 1 1 1 2h1 0c0-1 1-1 2-1h0v3h0 0c1 2 2 3 2 4s-1 1-1 1c-1 1-1 2-1 3h-1l-1-1 1-1c0-1 1-1 1-2l-1-2v-1-1c-2-1-4 0-6-1h0c2-1 2-2 2-4z" class="M"></path><path d="M170 508h1c0-1 0-2 1-3 1 3 0 5 0 8v2 1c2 1 3 2 3 4 1 0 1 1 0 2h-1l-2-1c-1 0-2-1-3-1l2-2c-2-2-1-4-2-6 0-1 1-1 1-2v-2z" class="N"></path><path d="M171 518c0 1 1 1 1 2v1c-1 0-2-1-3-1l2-2z" class="I"></path><path d="M172 516c2 1 3 2 3 4 1 0 1 1 0 2-2-2-2-4-3-6z" class="d"></path><defs><linearGradient id="W" x1="178.206" y1="513.236" x2="172.535" y2="514.143" xlink:href="#B"><stop offset="0" stop-color="#171616"></stop><stop offset="1" stop-color="#323233"></stop></linearGradient></defs><path fill="url(#W)" d="M178 505c-1 1 0 3-1 3 0 1-1 1-1 1s1 1 1 2v1h0l1 1 1 1v1 1h0l-2 2v2c1 0 1 0 1 1-1-1-2-1-3-1 0-2-1-3-3-4v-1c0-1 0-2 1-3h1c-1-2 0-3-1-5l1 1h2 1 0l-1-2c1 0 1-1 1-1h1z"></path><path d="M159 492v3 1 3c2 0 4-1 5 0 2 1 4 0 6 1v1 1l1 2c0 1-1 1-1 2l-1 1 1 1v2c0 1-1 1-1 2 1 2 0 4 2 6l-2 2c-2-1-4-3-5-5h-1l-2-2c0-1-1-2-1-3v-2l-1-5v-1h-1v1 1l-1-1v-1-3h0c-1-1-1-1 0-2v-1-1c0-1 1-2 2-3z" class="S"></path><path d="M166 515c0-1-1-2-1-3l1-2h2c1-1 1-2 1-3v-1h1l-1 1 1 1c-1 1-2 2-2 3s1 1 1 2h-1v-1c-1 1-1 0-1 1v2h-1z" class="G"></path><path d="M166 501h0v3l-1 5h-2c0 1 1 2 0 3-2-1-2-2-3-2v-2h2 0v-2h1l2-1v-1c1-1 1-2 1-3z" class="a"></path><path d="M166 515h1v-2c0-1 0 0 1-1v1h1c0-1-1-1-1-2s1-2 2-3v2c0 1-1 1-1 2 1 2 0 4 2 6l-2 2c-2-1-4-3-5-5h2z" class="H"></path><path d="M159 492v3 1 3c2 0 4-1 5 0 2 1 4 0 6 1v1 1c-1 1-1 2-2 2h-1-1v-3h0c0 1 0 2-1 3v1l-2 1h-1v2h0-2l-1-5v-1h-1v1 1l-1-1v-1-3h0c-1-1-1-1 0-2v-1-1c0-1 1-2 2-3z" class="b"></path><path d="M164 502c1 0 1-1 2-1 0 1 0 2-1 3v1l-2 1 1-1v-1c-1-1-2-1-2-2h2 0z" class="c"></path><path d="M164 500h2 1l-1 1h0c-1 0-1 1-2 1-1-1-3 0-4-1v-1h4z" class="J"></path><path d="M159 492v3 1 3c2 0 4-1 5 0 2 1 4 0 6 1v1 1c-1 1-1 2-2 2h-1-1v-3l1-1h-1-2-4c-1 1-1 2-1 3v-1h-1v1 1l-1-1v-1-3h0c-1-1-1-1 0-2v-1-1c0-1 1-2 2-3z" class="g"></path><path d="M167 504c0-2 1-3 2-3h1v1c-1 1-1 2-2 2h-1z" class="l"></path><path d="M159 492v3 1 6h0-1v1 1l-1-1v-1-3h0c-1-1-1-1 0-2v-1-1c0-1 1-2 2-3z" class="B"></path><path d="M157 499c0-1 0-1 1-2v2c0 1 0 2 1 3h0-1v1 1l-1-1v-1-3z" class="C"></path><path d="M182 547v-2c1 0 1 0 2-1 1 0 3-1 4-1v1c1 0 1 0 2-1h0l-2-1v-1c-1-1 1-2 1-3v-6c0-1-1-1-2-2v1h0v-5h2v3 1l3-3v-2h0c1 0 1 1 1 1h0l1 1 1 1h1l1 1h1c0 1-1 1-1 1 1 1 2 1 3 2v1c0 1 1 2 1 3v1h1v-1h0c1 1 1 1 2 1v1c-1 1-1 1 0 3v5l1 1-1 1c-1 0-1 1-1 2-1 1 0 3 0 4h0c-1 1-1 2-1 3h-1 1v2 3 1 2h0-1-7 0c0 1 0 3-1 3-1-1-1-2-2-3-1-4-4-8-5-11s-3-5-4-7z" class="n"></path><path d="M192 533h1c1 0 2 1 3 2l-1 1h-1l-2-2v-1z" class="U"></path><path d="M194 531l3-1c1 1 2 1 3 2v1h-1c-2 0-3-1-5-2z" class="N"></path><path d="M196 542v-2c1 0 0-2 0-2h1 1 2v1l-2 1c-1 1-1 2-2 3v-1z" class="U"></path><path d="M193 526h0l1 1 1 1h1l1 1h1c0 1-1 1-1 1l-3 1c0-2-1-3-1-5z" class="H"></path><path d="M202 546l2 2c-1 0-1 1-1 2h-3l-1-1 1-2 1 1c1-1 1-1 1-2z" class="U"></path><path d="M191 565c0-1 1-1 1-2 1-1 0-1 1-2h0l1 3v1h0c0 1 0 3-1 3-1-1-1-2-2-3z" class="E"></path><path d="M194 564h0c1-1 2 0 2-1h6v2h0-1-7v-1zm2-22v1l-3 6c-1 0-2 1-2 1 0 1 0 2-1 3 0 1-1 1-2 1h-1c2-2 3-5 4-7 2-1 3-3 5-5z" class="C"></path><path d="M201 537h1v-1h0c1 1 1 1 2 1v1c-1 1-1 1 0 3v5l1 1-1 1-2-2v-1l-1-2c1-1 0-3 1-5l-1-1z" class="E"></path><path d="M202 545c1 0 1 0 2 1l1 1-1 1-2-2v-1z" class="B"></path><path d="M197 462h1 3 0c1 1 1 1 2 1l3 3v1-1h1v1c1 1 1 5 1 6v6c1 0 1-1 1-1h1v4 2l-2 1v2c1 2 1 1 1 3-1 1-1 3-1 5l-1-1v3h-2c-1 0-3 1-5 1l-10 1c-1 0-1-1-2-1v-1l1-3c0-1-1-3-1-4 1-1 1-2 1-2v-1l1 1 1-2c-1-1-1-2-1-4h1l1-1h-1 0c-1-1-1-2-1-4h-1v-4l1-1v-1c0-1 1-1 1-2s-1-2-1-2l-2-2 1-1c1-2 5-1 8-2z" class="k"></path><path d="M191 495c2 0 5 0 6 1 0 0 1 1 0 2h-1v-1l-2-1c-1 0-2 0-2 1-1-1-1-1-1-2z" class="l"></path><path d="M193 492l1-1c0 1 0 2 1 2h2 1s1 1 1 2v1h-2c-1-1-4-1-6-1-1-1-1-1-1-2h3v-1z" class="G"></path><path d="M207 474s0-1 1-1v6c1 0 1-1 1-1h1v4 2l-2 1v2c1 2 1 1 1 3-1 1-1 3-1 5l-1-1v-12-8z" class="i"></path><path d="M208 479c1 0 1-1 1-1h1v4 2l-2 1v-6z" class="N"></path><path d="M202 488h5v2c-1 1-1 1-2 1-2 0-2 1-3 2 2 0 3 0 4 1v2 1c-2-1-3-1-5-1 0 1-1 1-2 0v-1c0-1-1-2-1-2 0-1 1-2 2-3l-1-1c1 0 2-1 3-1z" class="c"></path><path d="M200 490c1 0 1 1 2 0h1c1 0 1 0 2 1-2 0-2 1-3 2h-2c0 1 1 1 1 1-1 1-1 1-2 1 0-1-1-2-1-2 0-1 1-2 2-3z" class="a"></path><path d="M202 488h5v2c-1 1-1 1-2 1-1-1-1-1-2-1h-1c-1 1-1 0-2 0l-1-1c1 0 2-1 3-1z" class="c"></path><path d="M193 481h0c0 2 1 1 2 1v2c2-1 1-3 2-4h0 1c0 2 1 3 0 4l2 2 2 2c-1 0-2 1-3 1l1 1c-1 1-2 2-2 3h-1-2c-1 0-1-1-1-2l-1 1v1h-3c0-1 1-1 1-2-1-1-1-2-1-3l1-2c-1-1-1-2-1-4h1l1-1h1z" class="I"></path><path d="M191 486c-1-1-1-2-1-4h1v1c0 1 1 0 1 2l-1 1z" class="G"></path><path d="M198 487c0 1-1 2-2 2v-2h0c1-1 1-1 2-1h0l1 1h-1z" class="e"></path><path d="M191 491c1 0 1 0 1-1h1v2 1h-3c0-1 1-1 1-2z" class="L"></path><path d="M200 486l2 2c-1 0-2 1-3 1 0 0-1-1-1-2h1l1-1z" class="b"></path><path d="M193 490v-5h1 0c1 1 1 2 0 3v2h-1z" class="S"></path><path d="M203 473l1-1v1h2v1h1v8 4 2h-5l-2-2-2-2c1-1 0-2 0-4v-3-2c1 0 1-1 1-1h2v-1h0 2z" class="W"></path><path d="M198 475c1 0 1-1 1-1 0 2 1 3 2 5v1l1-1h1l2 3h-3l-2-1s-1 0-1-1 0-2-1-3v-2z" class="X"></path><path d="M206 474h1v8 4h-2c-1 0-1-1-2-2l1-1s1 0 2-1h-1l-2-3c1-1 2-1 3-1v-4z" class="Y"></path><path d="M203 473l1-1v1h2v1 4c-1 0-2 0-3 1h-1l-1 1v-1c-1-2-2-3-2-5h2v-1h0 2z" class="T"></path><path d="M203 473l1-1v1h2v1 4c-1 0-2 0-3 1h-1v-2h1c1 0 1 0 2 1l1-1v-4h-3 0z" class="Q"></path><path d="M197 462h1 3 0c1 1 1 1 2 1l3 3v1-1h1v1c1 1 1 5 1 6-1 0-1 1-1 1h-1v-1h-2v-1l-1 1h-2 0v1h-2s0 1-1 1v2 3h-1 0c-1 1 0 3-2 4v-2c-1 0-2 1-2-1h0-1-1 0c-1-1-1-2-1-4h-1v-4l1-1v-1c0-1 1-1 1-2s-1-2-1-2l-2-2 1-1c1-2 5-1 8-2z" class="o"></path><path d="M206 467v-1h1v1c1 1 1 5 1 6-1 0-1 1-1 1h-1v-1h-2v-1c1 0 1 0 2-1v-4h0z" class="L"></path><path d="M192 469h4l2 2c0 1-1 2 0 3h-2c0-1 0-1-1-1l-1 2h-1v-3h0c0-1-1-2-1-3z" class="P"></path><path d="M191 469h1c0 1 1 2 1 3h0v3h1l1-2c1 0 1 0 1 1h2c1 0 2-1 3-1v1h-2s0 1-1 1v2 3h-1 0c-1 1 0 3-2 4v-2c-1 0-2 1-2-1h0-1-1 0c-1-1-1-2-1-4h-1v-4l1-1v-1c0-1 1-1 1-2z" class="S"></path><path d="M191 469h1c0 1 1 2 1 3h0l-2 2h0-1l1 1h1l1 1s0 1-1 2c-1-1-1-1-2-1h-1v-4l1-1v-1c0-1 1-1 1-2z" class="J"></path><path d="M195 478h1c2 0 1-2 2-4v1 2 3h-1 0c-1 1 0 3-2 4v-2c-1 0-2 1-2-1h0-1-1 0c-1-1-1-2-1-4 1 0 1 0 2 1s2 1 2 2c1 0 0-1 0-2h1z" class="e"></path><path d="M190 477c1 0 1 0 2 1s2 1 2 2c1 0 0-1 0-2h1v3h-1l-1-1v1h-1-1 0c-1-1-1-2-1-4z" class="G"></path><defs><linearGradient id="X" x1="141.609" y1="536.665" x2="145.205" y2="551.695" xlink:href="#B"><stop offset="0" stop-color="#b8b6b6"></stop><stop offset="1" stop-color="#e9e8ea"></stop></linearGradient></defs><path fill="url(#X)" d="M156 527v3c1 0 1-1 1-1 2-1 4-1 6-2v1h1c2 1 2 3 2 5s0 3 1 4l1 1c0 2 1 2 1 4h-1v1 1l1 3h-33-18l2-3c0-1 1-2 2-4l2-3h1l4-1 4-2h1l4-2c5-2 10-4 15-4 1 0 2-1 3-1z"></path><path d="M139 541c1 1 1 1 2 3-2 1-2 1-4 0h-1l1-1 2-2z" class="W"></path><path d="M124 537h1v1c1 1 1 1 2 1 1 1 2 0 4 0v1h-4c-1 0-3 0-4 1l-1-1 2-3z" class="b"></path><path d="M163 541l5 3h-4l-2-1c-1 1-2 1-3 1-1-1-2-1-4-1l2-1 2-1c1 1 2 1 4 0z" class="Y"></path><path d="M139 541h0c1 0 2 0 3-1h0v1l1 1c1-1 2-1 3-1l1 1c1 0 2-1 3-1s2 1 3 1h4l-2 1v1h0c-2-1-3 0-5 0v-2c-1 0-1 1-2 1 0 0 0 1-1 1-1-1-1-1-2 0-1 0-2 0-2 1h-1c0-1 1-2 2-2v-1c-2 0-2 1-3 2-1-2-1-2-2-3z" class="V"></path><path d="M125 537l4-1c0 1-1 1 0 2h2l1 1h2l-1 2h1l2-1c1 0 1 1 1 2h0v1l-1 1s-1 0-2 1c-2-2-4 1-6 1v-1c1-1 3-2 4-4 1 0 1 0 0-1h-1v-1c-2 0-3 1-4 0-1 0-1 0-2-1v-1z" class="c"></path><path d="M153 536l3 1 2 2 5 2c-2 1-3 1-4 0l-2 1h-4c-1 0-2-1-3-1s-2 1-3 1l-1-1c-1 0-2 0-3 1l-1-1v-1h0c-1 1-2 1-3 1h0l-2 2v-1h0c0-1 0-2-1-2 1-1 2-2 3-2v2c2-1 3-1 4-1v1c2 0 3-1 4-1s2-1 2-1c1-1 1-1 3-1l1-1z" class="l"></path><path d="M153 536l3 1 2 2c-1 0-1-1-2-1h-4-1c1 1 2 1 3 2h-1-2-2v-2c1-1 1-1 3-1l1-1z" class="M"></path><path d="M134 534l4-2c0 1 0 1 1 2 2 1 4-1 5-1 3 0 8 1 9 2v1l-1 1c-2 0-2 0-3 1 0 0-1 1-2 1s-2 1-4 1v-1c-1 0-2 0-4 1v-2c-1 0-2 1-3 2l-2 1h-1l1-2h-2l-1-1h-2c-1-1 0-1 0-2l4-2h1z" class="H"></path><path d="M129 536l4-2c-1 1-1 2-1 3v1c1 0 2-1 2-1h1c0 1-1 2-1 2h-2l-1-1h-2c-1-1 0-1 0-2z" class="Z"></path><path d="M134 534l4-2c0 1 0 1 1 2 2 1 4-1 5-1 3 0 8 1 9 2v1l-1 1c-2 0-2 0-3 1 0 0-1 1-2 1l-2-1h1 0l1-2c-1 0-1-1-2-1s-2 1-2 1c-1 0-1-1-2-1 0 1-2 2-2 2l-1-1-2 1c0-1-1-1-1-1h-1v-2z" class="d"></path><path d="M147 536v1h1v-1-1h3 2 0v1l-1 1c-2 0-2 0-3 1 0 0-1 1-2 1l-2-1h1 0l1-2z" class="I"></path><path d="M156 527v3c1 0 1-1 1-1 2-1 4-1 6-2v1h1c2 1 2 3 2 5s0 3 1 4l1 1c0 2 1 2 1 4h-1v1 1h0l-5-3-5-2-2-2-3-1v-1c-1-1-6-2-9-2-1 0-3 2-5 1-1-1-1-1-1-2 5-2 10-4 15-4 1 0 2-1 3-1z" class="U"></path><path d="M155 534c-1 0-2-1-4-2h1c2-1 1 0 3 1h1c0-1 0-1 1-2 1 1 2 2 4 3h1-1c0 1-1 1-1 1-1 0-3-1-4-1h-1z" class="B"></path><path d="M161 534c0-2 1-3 2-3v1c1 0 2 0 3 1 0 2 0 3 1 4l1 1h-1c-2-1-2-1-3-2v-1h-2v-1h0-1z" class="R"></path><path d="M161 534c0-2 1-3 2-3v1h0l1 2h0-2 0-1z" class="E"></path><path d="M155 534h1c1 0 3 1 4 1 0 0 1 0 1-1h1 0v1h2v1c1 1 1 1 3 2h1c0 2 1 2 1 4h-1v1 1h0l-5-3-5-2-2-2h3v-1c-2 0-2 0-3-1l-1-1z" class="D"></path><path d="M168 542c-3-1-5-2-7-3 1 0 3 1 4 1h1c-2-2-4-3-6-3v-1c1 0 2 1 3 1v-2l1 1c1 1 1 1 3 2h1c0 2 1 2 1 4h-1z" class="I"></path><path d="M211 542v-1c1 0 1-1 1-2h0c1 0 1 0 2 1 0 2 0 3 2 4v2h0c0-1 1-1 1-2v1c3 6 5 12 8 17l2-2v1c1 2 2 3 4 5v1 2h-1c2 4 4 8 7 11-1 1-1 2-2 3 1 2 1 3 0 4l1 1c-1 1-2 1-2 2-1 0-1 1-2 2s-3 1-4 2l-2-1c-1-1-2-2-2-3l-2-2v-1c-1-1-2-1-2-2l-2 1c-2-1-2-1-4-1h0v1h-2c0 1 1 2 1 3-2-1-3-3-5-4-1 0-1-1-2-2h-2 0l-2-3c-2-1-6-5-7-7s-3-4-4-6c-2-2-3-4-4-7-1-1-1-2-2-3-1-3-2-6-3-8-1-1-1-2-1-3l1 1c1 2 3 4 4 7s4 7 5 11c1 1 1 2 2 3 1 0 1-2 1-3h0 7 1 0v-2-1l1-1h1v2h2v-1c-1-2 0-3 0-5 0-1 0-2-1-4 0-1 1-2 0-4h0 1c1 1 1 2 1 3l1-1 1-1h1l-1-1v-1-1s1 0 1-1c1-1 1-2 1-3v-1z" class="R"></path><path d="M206 582v-2h1l2 1c-1 1-1 1-3 1h0z" class="U"></path><path d="M196 571h2l1 1-1 1h-2v-1-1z" class="E"></path><path d="M209 577c0-1-1-3 0-4 1 1 1 1 1 2l1 1-1 1h-1zm-7-2h1v2 1h-1c-1 0-1-1-2-2l1-1h1z" class="C"></path><path d="M204 571l1-1c2 1 1 1 1 2s1 2 1 2v1h-1c0-1 0-2-1-3l-1-1z" class="U"></path><path d="M204 576v-1l1 1h0 2l1 1v2c-2 0-3 0-3 1-1-1-1-2-1-3v-1z" class="O"></path><path d="M202 565c1 1 0 3 0 5v1h1 1l1 1c1 1 1 2 1 3l-1 1-1-1v1-1l-1-1v1h-1c1-1 0-3 0-4h0v-4-2h0z" class="K"></path><path d="M214 585s-1 0-1-1c-1-1-1-1 0-2h1v-1c2-1 2 0 3 0l1 1h0l2 3-2 1c-2-1-2-1-4-1h0z" class="O"></path><path d="M211 576h1v2l1 1h-1l1 2h0-2v1c0 2 1 3 1 4s1 2 1 3c-2-1-3-3-5-4-1 0-1-1-2-2v-1h0c2 0 2 0 3-1h1l1-1-2-1v-2h1l1-1z" class="E"></path><path d="M211 576h1v2s0 1-1 1c-1-1 0-1-1-2l1-1z" class="D"></path><path d="M194 565h7 1v2 4h0c0 1 1 3 0 4h-1c0-1-1-1-1-1-1-1 0-2 0-2 0-1-1-1-1-2l-1 1h-2l-3-3c1 0 1-2 1-3h0z" class="O"></path><path d="M194 565h7 1v2c-2 0-4-1-6 0h-1 0c-1 0-1-1-1-2h0z" class="n"></path><path d="M219 578h-1c0-1-1-2-1-2 1-1 1-2 1-3v-2h0l-2 1v2h-1c0-3-2-5-4-7-1 1-1 3-2 4h-1c-1-2-1-2-1-4h0c-1 0-1 0-1 1h0-2v-2-1h2 1 0c2-1 5 0 7 0l1 1c-1 1-1 2-1 3 1 1 2 2 3 2 1-1 1-2 1-2 1 0 1 0 2 1 1 0 2 1 2 2h-2l-1 1 1 1v1 1 2h-1z" class="O"></path><path d="M206 563v-1c-1-2 0-3 0-5 0-1 0-2-1-4 0-1 1-2 0-4h0 1c1 1 1 2 1 3v2c1 3 0 6 0 8h1c2 1 1 1 3 1 1-1 1 0 2 0h1c1-1 1-2 1-3l2-1v1 1c0 1 1 2 2 3h0v2c1 2 1 2 1 4-1-1-1-1-2-1 0 0 0 1-1 2-1 0-2-1-3-2 0-1 0-2 1-3l-1-1c-2 0-5-1-7 0h0-1l1-1h0l-1-1z" class="B"></path><path d="M219 564h0v2 1c-1 0-1-1-2-2l2-1zm-8-22v-1c1 0 1-1 1-2h0c1 0 1 0 2 1 0 2 0 3 2 4v2h0c1 3 2 7 2 10v3h-1l-2 1c0 1 0 2-1 3h-1c-1 0-1-1-2 0-2 0-1 0-3-1h-1c0-2 1-5 0-8v-2l1-1 1-1h1l-1-1v-1-1s1 0 1-1c1-1 1-2 1-3v-1z" class="O"></path><path d="M212 552c1 1 2 1 3 2v1h-2v1c-2-1-2-1-2-2l1-2z" class="U"></path><path d="M209 547s1 0 1-1c1-1 1-2 1-3v-1h0c0 1 0 2 1 2-1 2 0 4 1 6v1c-1 0-2-1-3-1l-1-1v-1-1z" class="B"></path><path d="M211 563c0-1 0-2-1-2h0c0-1 0-2 1-3l-2-1v-2c0-1 0-2 2-3h1l-1 2c0 1 0 1 2 2-1 1-1 3 0 5l2-1c0 1 0 2-1 3h-1c-1 0-1-1-2 0z" class="C"></path><path d="M217 544v1c3 6 5 12 8 17l2-2v1c1 2 2 3 4 5v1 2h-1c2 4 4 8 7 11-1 1-1 2-2 3 1 2 1 3 0 4l1 1c-1 1-2 1-2 2-1 0-1 1-2 2s-3 1-4 2l-2-1c-1-1-2-2-2-3l-2-2v-1c-1-1-2-1-2-2l-2-3h0l-1-1v-2l2-1h1v-2-1-1l-1-1 1-1h2c0-1-1-2-2-2 0-2 0-2-1-4v-2h0c-1-1-2-2-2-3v-1-1h1v-3c0-3-1-7-2-10 0-1 1-1 1-2z" class="h"></path><path d="M220 582h4l1 1h-1c-1 0-2 0-3 1h-1v-1-1z" class="K"></path><path d="M235 586c-1-1-1-3-2-4h1l1 1c1 2 1 3 0 4v-1z" class="B"></path><path d="M224 585c0-1 1-1 1-2h3v-2l1 1h0v1c-1 0-2 1-3 1v2-1h-2z" class="R"></path><path d="M219 578h1c0 2 1 2 0 3-1 0-2 0-2 1l-1-1v-2l2-1z" class="C"></path><path d="M218 582l1 1s0-1 1-1h0v1 1h1c1-1 2-1 3-1l-1 2h-1v2c-1-1-2-1-2-2l-2-3z" class="d"></path><g class="K"><path d="M222 587v-2h1 1 2c-1 1-1 1-1 2 0 0 0 1 1 1-2 0-2 1-2 2l-2-2v-1z"></path><path d="M229 582h2v2c2 1 2 1 2 3h0c1 0 1-1 2-1v1l1 1c-1 1-2 1-2 2-1 0-1 1-2 2s-3 1-4 2l-2-1c-1-1-2-2-2-3s0-2 2-2c-1 0-1-1-1-1 0-1 0-1 1-2v1-2c1 0 2-1 3-1v-1h0z"></path></g><path d="M226 585v1h0l1 2-1 1v-1c-1 0-1-1-1-1 0-1 0-1 1-2z" class="U"></path><path d="M229 586c1 0 1 1 2 0l1 1v1h-4v-1l1-1z" class="i"></path><path d="M229 583s1 1 0 2v1h-3 0v-2c1 0 2-1 3-1z" class="D"></path><path d="M224 590c0-1 0-2 2-2v1c0 1 1 3 0 4-1-1-2-2-2-3z" class="R"></path><path d="M217 544v1c3 6 5 12 8 17l2-2v1c1 2 2 3 4 5v1 2h-1c2 4 4 8 7 11-1 1-1 2-2 3l-1-1v-3c0-1 0-1-1-1h0c-2 0-2 0-3 1h0v-2h-1 0l-1 1c0-1-1-1-1-2h-1-1-1s-1-1-1-2l1-1c-1-1-1-1-1-2v-1h-1c0-2 0-3-2-4 0 0 0-2-1-2h0 0c-1-1-2-2-2-3v-1-1h1v-3c0-3-1-7-2-10 0-1 1-1 1-2z" class="N"></path><path d="M218 556c1 2 1 5 1 8h0 0c-1-1-2-2-2-3v-1-1h1v-3z" class="E"></path><path d="M225 576l1-2s-1 0-1-1h0 0c-1-2-1-2-1-3h2l1 1c-1 1-1 1-2 1l1 1h2c0 1 1 2 2 3v1h-1 0l-1 1c0-1-1-1-1-2h-1-1z" class="d"></path><path d="M227 560v1c1 2 2 3 4 5v1 2h-1l-5-7 2-2z" class="J"></path><path d="M208 495c0-2 0-4 1-5 1 4 0 9 1 13v-3l3 3v1h2c1 0 1-1 2-1h0c1 0 2 0 3 1l1 1c0 1 1 1 1 1v2c0 1 0 1 1 1v-1l1-1c1 0 1 1 1 1v1c1 0 1 0 1-1v9 1 3 4 1c1 3 0 7 0 10l1 24-2 2c-3-5-5-11-8-17v-1c0 1-1 1-1 2h0v-2c-2-1-2-2-2-4-1-1-1-1-2-1h0c0 1 0 2-1 2v1 1c0 1 0 2-1 3 0 1-1 1-1 1v1 1l1 1h-1l-1 1-1 1c0-1 0-2-1-3h-1 0c1 2 0 3 0 4 1 2 1 3 1 4 0 2-1 3 0 5v1h-2v-2h-1l-1 1v-3-2h-1 1c0-1 0-2 1-3h0c0-1-1-3 0-4 0-1 0-2 1-2l1-1-1-1v-5c-1-2-1-2 0-3v-1c-1 0-1 0-2-1h0v1h-1v-1c0-1-1-2-1-3v-1c-1-1-2-1-3-2 0 0 1 0 1-1h-1l-1-1h-1l-1-1-1-1 1-1v-1h0l2-1c0-1-1-1-1-1l-1-1c0-1-1-1-2-1 1 0 1 0 2-1v-1l-1-3h1l2 2 1-1-1-3h0v-3-1h-2v-1c0-1-1-1-2-1l-3-3-1-1v-3c1-1 1-2 0-2 1 0 1 1 2 1l10-1c2 0 4-1 5-1h2v-3l1 1z" class="C"></path><path d="M203 554h0v-3l1 1v5 1h-1v-4h0z" class="B"></path><path d="M204 524c0-1 1-1 2-1v1l-1 2s-1 0-2-1v1-1l-1-1h1 1z" class="e"></path><path d="M204 524v-4h0v-1-3c1 0 2 0 2 1v1h0c1 1 0 4 0 6v-1c-1 0-2 0-2 1z" class="F"></path><path d="M203 525v1c0 3 0 3-2 5 0 1 0 1-1 1-1-1-2-1-3-2 0 0 1 0 1-1 1-1 2-1 4-1v-1-1l1-1z" class="G"></path><path d="M204 537v-2h0c0-1-1-1-1-2v-1c1 0 2 0 3 1v2h0c1 1 0 2 0 3h0c0 3-1 5 1 8l-1 1h-1l-1-1v-5c-1-2-1-2 0-3v-1z" class="j"></path><path d="M196 523c2-1 3-1 4 0 1 0 1 0 2 1l1 1-1 1v1 1c-2 0-3 0-4 1h-1l-1-1h-1l-1-1-1-1 1-1v-1h0l2-1z" class="S"></path><path d="M194 525v1h3 1c-1 1-2 1-2 2h-1l-1-1-1-1 1-1z" class="G"></path><path d="M196 523c2-1 3-1 4 0 1 0 1 0 2 1l1 1-1 1-2-1c0-1 0-1-1-1s-3 1-4 1h0l-1-1 2-1z" class="J"></path><path d="M200 525l2 1v1 1c-2 0-3 0-4 1h-1l-1-1c0-1 1-1 2-2l1 2v-1-1-1h1z" class="L"></path><path d="M198 526l1 2-1 1h-1l-1-1c0-1 1-1 2-2z" class="R"></path><path d="M208 495c0-2 0-4 1-5 1 4 0 9 1 13v5c2 2 0 10 1 11l1-1v6h-2v-2c-1 1-1 4-1 5s0 2-1 3v2c1 3 1 6 1 8-1-1-1-3-1-4-1-1 0-2 0-3-1 0-2-1-2-1v-3c-1 0 0 0 0-1 1 0 1-1 2-1h1l-2-2v-1h1c2-1 1-5 1-7h0c0-1 0-1-1-2 1-2 0-4 1-6h0 0c0-1 0-2 1-2h0v-2c0-1-1-1-2-2v-4l-1-1v-1-3l1 1z" class="K"></path><path d="M210 508c2 2 0 10 1 11l1-1v6h-2v-2-1-2-10-1z" class="G"></path><path d="M208 495c0-2 0-4 1-5 1 4 0 9 1 13v5 1c-1-2 0-5 0-7v1h-1c-1-3-1-6-1-8z" class="D"></path><path d="M210 500l3 3v1h2c1 0 1-1 2-1h0v1h-1c-1 2 0 5 0 8 0 1 0 2 1 4h-1v6 2l-2-1-1 1v-1l-1 1v-6l-1 1c-1-1 1-9-1-11v-5-3z" class="I"></path><path d="M213 523v-6h1v3s1 0 1 1l-1 2-1 1v-1z" class="H"></path><path d="M214 517l1-1v1l1-1v6 2l-2-1 1-2c0-1-1-1-1-1v-3z" class="F"></path><path d="M210 500l3 3v1 4l1 1v3l-1-1v-1h-1c0 2 1 4 0 6v2l-1 1c-1-1 1-9-1-11v-5-3z" class="S"></path><path d="M202 511l1 1h0v-1-1c1 0 2 0 3 1l1-1c0 2 0 3-1 4h-1 0l1 2v1c0-1-1-1-2-1v3 1h0v4h-1-1c-1-1-1-1-2-1-1-1-2-1-4 0 0-1-1-1-1-1l-1-1c0-1-1-1-2-1 1 0 1 0 2-1v-1l-1-3h1l2 2 1-1 2-1v-2-1h0 0c1-1 2-1 3-1z" class="I"></path><path d="M199 515c0 2-1 3-1 5h-3l-1 1c0-1-1-1-2-1 1 0 1 0 2-1v-1l-1-3h1l2 2 1-1 2-1z" class="F"></path><path d="M193 515h1l2 2h0v1c0 1-1 1-1 2l-1 1c0-1-1-1-2-1 1 0 1 0 2-1v-1l-1-3z" class="d"></path><path d="M202 511l1 1h0v-1-1c1 0 2 0 3 1l1-1c0 2 0 3-1 4h-1 0v1c-1 0-1 1-1 1-1-1-2-1-3-2v-1h-2v-1h0 0c1-1 2-1 3-1z" class="N"></path><path d="M207 497v1 9 3l-1 1c-1-1-2-1-3-1v1 1h0l-1-1c-1 0-2 0-3 1h0 0v1 2l-2 1-1-3h0v-3-1h-2v-1c0-1-1-1-2-1l-3-3-1-1v-3c1-1 1-2 0-2 1 0 1 1 2 1l10-1c2 0 4-1 5-1h2z" class="I"></path><path d="M189 504l1-1c1 0 2 1 3 2v1 1h-1l-3-3z" class="H"></path><path d="M196 509h2c1 0 1 0 1 1 0 0 1 1 0 2h0v1 2l-2 1-1-3h0v-3-1z" class="G"></path><path d="M199 512v1 2l-2 1-1-3 1 1 2-2z" class="D"></path><path d="M196 509h2c1 0 1 0 1 1-2 0-2 1-3 3v-3-1z" class="S"></path><path d="M207 497v1 9 3l-1 1c-1-1-2-1-3-1v1 1h0l-1-1c-1-3 1-3 2-5v-1l-1-1c-1 1-2 3-2 4l-1 1-1-4c0-1-2-1-3-1 0-2-1-2-1-4h1l1 1h2v-1-1h1v-1c2 0 4-1 5-1h2z" class="H"></path><path d="M200 499c1 0 1 0 2 1 1-1 2 0 3 0l-1 2c-1 1-2 1-4 3l-1-1c0-1 1-1 1-2v-1h-1v-1-1h1z" class="M"></path><path d="M200 499c1 0 1 0 2 1h-3v-1h1z" class="F"></path><path d="M207 497v1 9-2h-1c-1 0-1-1-2-1v-1-1l1-2c-1 0-2-1-3 0-1-1-1-1-2-1v-1c2 0 4-1 5-1h2z" class="e"></path><path d="M205 500l2-1v3l-3 1v-1l1-2z" class="S"></path><path d="M207 502v3h-1c-1 0-1-1-2-1v-1l3-1z" class="M"></path><path d="M217 503c1 0 2 0 3 1l1 1c0 1 1 1 1 1v2c0 1 0 1 1 1v-1l1-1c1 0 1 1 1 1v1c1 0 1 0 1-1v9 1 3 4 1c1 3 0 7 0 10l1 24-2 2c-3-5-5-11-8-17v-1c0 1-1 1-1 2h0v-2c-2-1-2-2-2-4-1-1-1-1-2-1h0c0 1 0 2-1 2v1 1c0 1 0 2-1 3 0 1-1 1-1 1v-2c-1-2 0-3 0-5s0-5-1-8v-2c1-1 1-2 1-3s0-4 1-5v2h2l1-1v1l1-1 2 1v-2-6h1c-1-2-1-3-1-4 0-3-1-6 0-8h1v-1z" class="B"></path><path d="M217 516s1 1 0 2v1c0 1 0 3-1 3h0v-6h1z" class="E"></path><path d="M208 532c2 0 3 0 4 2l2 2h1v-1 3h-1c-1-1-1-1-2-1h-1v3 2 1c0 1 0 2-1 3 0 1-1 1-1 1v-2c-1-2 0-3 0-5s0-5-1-8z" class="D"></path><path d="M209 527c0-1 0-4 1-5v2h2l1-1v1l1-1 2 1v1c-1 2-1 2-1 4h-1c1 1 1 2 1 2v4 1h-1l-2-2c-1-2-2-2-4-2v-2c1-1 1-2 1-3z" class="F"></path><path d="M214 523l2 1v1h-2-1v-1l1-1z" class="I"></path><path d="M209 527c0-1 0-4 1-5v2h2l1-1v1 1h1l-2 2v2 2l-1 1c-1-1-1-2-1-3l1-1c-1 0-1 0-2-1z" class="e"></path><path d="M220 512v2c0-1 1-1 2-2v2c-1 1-1 3 0 4v2h-1v2 2c-1 2-1 4-2 7h0 1c-1 2-1 3-2 5 1 0 1 1 1 1 0 1-1 1-1 2 1 1 1 0 1 2l-1 1h0c1 0 1 1 1 1 0 1-1 2-2 2v-1c0-7 0-15 1-22v-4-1-4c1 0 1-1 2-1z" class="V"></path><path d="M220 514c0-1 1-1 2-2v2c-1 1-1 3 0 4v2h-1v2 2c-1 2-1 4-2 7h0 1-2v-1c1-1 1-2 2-3v-4-3c0-1-1-2-1-3 0 0 0-1 1-1h0l-1-1 1-1z" class="X"></path><path d="M217 503c1 0 2 0 3 1l1 1c0 1 1 1 1 1v2c0 1 0 1 1 1v-1l1-1c1 0 1 1 1 1v1c1 0 1 0 1-1v9 1 3c-1 0-2 0-3-1h-1v-2c-1-1-1-3 0-4v-2c-1 1-2 1-2 2v-2c-1 0-1 1-2 1h-2v-1c0-3-1-6 0-8h1v-1z" class="Y"></path><path d="M222 511c1 0 2 1 2 1 1 0 1 1 2 1l-1 1c-1 0-1 0-2 1v2h3v1 3c-1 0-2 0-3-1h-1v-2c-1-1-1-3 0-4v-2c-1 1-2 1-2 2v-2c1-1 2-1 2-1z" class="W"></path><path d="M223 520l3-2v3c-1 0-2 0-3-1z" class="Q"></path><path d="M216 512c0-3-1-6 0-8h1c1 2 3 5 5 7 0 0-1 0-2 1-1 0-1 1-2 1h-2v-1z" class="C"></path><path d="M222 520h1c1 1 2 1 3 1v4 1c1 3 0 7 0 10l1 24-2 2c-3-5-5-11-8-17 1 0 2-1 2-2 0 0 0-1-1-1h0l1-1c0-2 0-1-1-2 0-1 1-1 1-2 0 0 0-1-1-1 1-2 1-3 2-5h-1 0c1-3 1-5 2-7v-2-2h1z" class="Y"></path><path d="M226 526h-1c-2 1-2 1-3 0 0-1 0-1 1-2l3 1v1z" class="F"></path><defs><linearGradient id="Y" x1="293.264" y1="407.529" x2="367.725" y2="376.603" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#252425"></stop></linearGradient></defs><path fill="url(#Y)" d="M342 333c0-1 1-2 2-3 1 1 2 2 3 4 1 1 2 1 4 1h0l1 1 3 3 4 4 2 2v20 3h-1l-3 2s-1 0-1 1l-2 1c-1 0-1 1-1 2h-1 0v1c1 1 1 1 1 3h0v1h0l1-1c1 2 3 3 5 4v1h-1c-1 0-1 1-2 2h2c1-1 1-1 2-1 1 1 1 2 1 3 0 3-1 5 0 8-1 1-1 1-2 1h-1v2c1 1 1 2 1 3h1c1 2 0 5 1 8v18 9 3c-1 0-6-12-7-14-5-10-12-19-19-27-5-6-10-10-15-15-2-2-5-4-7-5-1-1-3-1-5-1h-26c-3 0-6 0-8 1-1 2-2 6-3 8-2 4-4 6-6 10-2 0-2 0-3-1v1 1l1 2c-1 0-1 1-1 1-2 0-3 2-4 3l-1 1-2 1c0 2-1 2-2 3h-1 0-1c-1-1 0-2-2-2 0 0-1 0-2 1-1 0-2 0-2 1l-1 2-1 1c-3 3-9 5-12 5-2 0-5 0-7 1l-2-2h0l1-1v-1l1-1v-2s1 0 2-1h0-1l-2-1h0-1-2c1-1 1-1 1-2h-1-1v1l-1 1v-1h-1 0c-1 0-2 0-3-1l-1 2c0-1-1-1-2-2-1 1-2 1-3 1v-1h0-2-1v-1l2-2-4-5-2-4c-1-1-1-2-2-3l1-1v-2h0c-1-1-2-4-2-6l-2-4v-1l2-1 1-1 1-2s1-2 1-3l1-1c-1 0-2 0-3-1l-1-1c1-1 2-2 3-2-1 0-2 0-3-1 0-1 0-2-1-3h0l1-2c0-1 1-1 2-2l1-1 2-2 1-1 2-1c0 1 0 1 1 1 0 1 1 2 2 3v1l2-3 3 2 3-3c1 1 2 2 3 2s1 0 2 1h4c3 1 5 2 7 4h0l3 1h2v-1c1-1 1-2 0-3 0 0 1 0 1-1v-3h1 0 10 1 6 8 9 0 2c1-1 1-1 2-1h0c1-1 1-2 1-3 0 0 1-1 2-1l2 1v1c1 0 2 1 2 2 1-1 1-1 2-1v2h4 13 26c2-1 3-4 4-6s3-4 4-7l4-7z"></path><path d="M342 361h1v1h-2l1-1zm0 4h0c0 1-1 2-2 2-1 1-2 1-2 2l-1-1 5-3z" class="C"></path><path d="M351 353h1v3h-1-1l1-3z" class="d"></path><path d="M356 359h1c1 1 0 2 0 3-2 0-2 0-2-2l1-1z" class="L"></path><path d="M351 374h1v1c0 1-1 1-2 2l-2-2c1 0 2-1 3-1zm-9 11c1 1 2 1 3 1 1 1 2 1 2 2l-2 1h-1-2l1-1-2-2 1-1z" class="U"></path><path d="M316 365l1 1h1 0l2-1h1 1v2h-1 0c-2-1-5 1-6 2h-1c0-1 0-1 1-2 0-1 0-2 1-2zm-13 0h0 2c1 1 3 2 3 3-1 1-2 1-3 1l-3-1v-1h-1c0-1 1-2 2-2z" class="j"></path><path d="M301 367c-1 3-3 6-5 8h-1-1l3-4-1-1c5 0 3-4 6-6l1 1c-1 0-2 1-2 2z" class="C"></path><path d="M355 406s-1 1-2 1h-1v-3c-1 0-2-1-3-1 0 0-1 0-1-1s0-1 1-1l2 1v-2-1l1-1c0 1-1 2 0 3h0l3 1h0 0 2l-2 2v1 1z" class="B"></path><path d="M352 401l3 1c-1 1-1 2-3 2v-3z" class="E"></path><path d="M290 360h1c-2 2-3 2-5 3 0 1 1 1 0 2 0 0-1 0-1 1-1 0-1 0-1 1l-1-1c-1-1-2-2-2-4v-1c1 0 2-1 3 0 2 0 4-1 6-1z" class="j"></path><path d="M282 362h0l1 1h2l1 1-1 1s-1 0-1 1l-1-1c-1-1-1-1-1-3z" class="U"></path><path d="M282 368h1c3 1 5 0 8-1 0-1 2-1 3-1 0 0 1 0 1 1-1 0-1 0-1-1-2 0-4 1-5 2h1 1l-3 2c-2 1-3 0-4 1-1 0-1 1-2 1 0 1-1 1-2 1v1 2l-1 1c0-2 0-2-1-3v-1c1-1 2-2 2-3 1-1 2-1 2-2z" class="K"></path><path d="M348 375h-1v-1c0-1 1-2 2-3h1v-1c-1-1-2-1-2-2v-1h4v1c0 1 1 1 2 1v1l-1 1 1 1c-1 0-1 1-1 2h-1 0-1c-1 0-2 1-3 1z" class="B"></path><path d="M348 368h2 2l-1 1c-1 0-2 0-2-1h-1z" class="d"></path><path d="M351 374c0-2 1-3 3-4l-1 1 1 1c-1 0-1 1-1 2h-1 0-1z" class="D"></path><defs><linearGradient id="Z" x1="353.414" y1="411.371" x2="361.42" y2="407.975" xlink:href="#B"><stop offset="0" stop-color="#262627"></stop><stop offset="1" stop-color="#414040"></stop></linearGradient></defs><path fill="url(#Z)" d="M359 401h1c1 2 0 5 1 8v18 9c-1 0 0 0 0 0l-1-24-8 7c1-2 2-3 3-4-1-2-1-4-1-6h-1c1-3 6-4 7-6h-1l-4 3v-1-1l2-2 2-1h0z"></path><path d="M354 378c1 2 3 3 5 4v1h-1c-1 0-1 1-2 2h2c1-1 1-1 2-1 1 1 1 2 1 3 0 3-1 5 0 8-1 1-1 1-2 1h-1v2c1 1 1 2 1 3h0l-2 1h-2 0 0l-3-1h0c-1-1 0-2 0-3h1l-1-2c-2 0-2 0-3 1h-1l2-2c-2 0-2-1-3-3l1-1h2 0c-1-1-1-1-2-1v-1c1-1 1-1 1-2v-1c1-1 4 0 6 1 1 0 1 0 2-1h0c-1-1-4-2-6-2-1 0-1 0-2-1v-2l2-2s1 0 2-1v1h0l1-1z" class="R"></path><path d="M351 384c-1 0-1 0-2-1v-2l2-2v1c2 0 4 0 5 1l-1 1c-2 0-2-1-3 1 0 0-1 0-1 1z" class="K"></path><path d="M352 396v-2c1 0 1 1 2 0h0c0-1 1-1 1-2v-1l-1-1c-1 1-2 1-2 2l-1-1s1-1 2-1c0-1 1-1 2-1 1 1 2 3 3 4s1 2 1 3h-1v2c1 1 1 2 1 3h0l-2 1h-2 0 0l-3-1h0c-1-1 0-2 0-3h1l-1-2z" class="d"></path><path d="M355 402l1-2c1-1 2 0 2 1h1l-2 1h-2z" class="E"></path><path d="M342 333c0-1 1-2 2-3 1 1 2 2 3 4 1 1 2 1 4 1h0l1 1 3 3 4 4 2 2v20 3h-1l-3 2s-1 0-1 1l-2 1-1-1 1-1v-1s1 0 1-1h0c0-1 0-2-1-4h-3 0 0c1-1 1-1 2-1 1 1 1 1 2 3l1-1h1c2 0 2 0 3-1v-6h-1-2v-3h-1c-2 0-2 0-3-1 0-1 0-3-1-4h0c-2-2 0-2 1-4-1 0-2-1-3-1 0-1-1 0-1 0 0-1-1-1-1-1h-1v-1c-1-1-1-2-3-2-2-1-3-1-6-1l4-7z" class="L"></path><path d="M354 349h1 0v1l-1 1-1-1 1-1z" class="B"></path><path d="M344 337h1l1 1h0c-1 1-1 1-2 1v-1-1z" class="R"></path><path d="M342 333c0-1 1-2 2-3 1 1 2 2 3 4h-2l-3-1z" class="e"></path><path d="M357 370c0-1 0-1-1-1l1-1c1-1 2-3 4-3v3h-1l-3 2z" class="I"></path><path d="M281 348l2 1v1c1 0 2 1 2 2 1-1 1-1 2-1v2h4-5c-1 0-1 1-1 1-1 1-1 2-2 3s-3 3-3 5v1l-2 2c1 1 3 1 4 3h0c0 1-1 1-2 2 0 1-1 2-2 3v1c1 1 1 1 1 3l1-1v-2c0 1 0 2 1 3h2v-1h0c3 1 7 0 10 1h4 10 1-26c-3 0-6 0-8 1-1 2-2 6-3 8-2 4-4 6-6 10-2 0-2 0-3-1h0c0-2 0-3-2-4 1-1 1-1 1-2h-1c0-2 1-4 2-5-1-1-2-2-3-2l-1-1c0-1 1-2 0-3 1 0 1 0 2-1h0c0 1 0 1 1 2l1-1c1-3 4-6 6-10l3-3h0v-3h-1v-2h-1-1l-1-1c-1-1-1-1-2-1h-4s-1-1-1-2c-1-1 0-1 0-2l5-1h9 0 2c1-1 1-1 2-1h0c1-1 1-2 1-3 0 0 1-1 2-1z" class="C"></path><path d="M266 375h1c0 1 1 1 3 1h0l1 1h-1c-2 1-3 0-4 0h-1l1-2z" class="B"></path><path d="M277 369v-2-1h0c-1-1-2-3-4-4-1-1-2-3-4-4h0 0c4 2 7 6 10 9v1c-1 1-2 1-2 1z" class="K"></path><path d="M279 367l3 1h0c0 1-1 1-2 2 0 1-1 2-2 3 0-1 0-2-1-3-1 0-1 0-1-1h1s1 0 2-1v-1z" class="R"></path><path d="M281 348l2 1v1c1 0 2 1 2 2 1-1 1-1 2-1v2h4-5c-1 0-1 1-1 1-1 1-1 2-2 3s-3 3-3 5v1l-2 2c1 1 3 1 4 3l-3-1c-3-3-6-7-10-9-2-2-3-3-5-4h10v-1h0 2c1-1 1-1 2-1h0c1-1 1-2 1-3 0 0 1-1 2-1z" class="E"></path><path d="M278 365v-3c0-2 3-5 4-6l1 1c-1 1-3 3-3 5v1l-2 2z" class="O"></path><path d="M281 348l2 1v1c1 0 2 1 2 2 1-1 1-1 2-1v2h4-5c-1 0-1 1-1 1-1 1-1 2-2 3l-1-1 1-2h-1-8v-1h0 2c1-1 1-1 2-1h0c1-1 1-2 1-3 0 0 1-1 2-1z" class="b"></path><path d="M274 353h12c-1 0-1 1-1 1-1 1-1 2-2 3l-1-1 1-2h-1-8v-1h0z" class="U"></path><path d="M281 348l2 1c0 2-1 2-2 2 0 1-1 1-1 2h-4c1-1 1-1 2-1h0c1-1 1-2 1-3 0 0 1-1 2-1z" class="T"></path><defs><linearGradient id="a" x1="248.298" y1="372.023" x2="262.248" y2="355.104" xlink:href="#B"><stop offset="0" stop-color="#252425"></stop><stop offset="1" stop-color="#414040"></stop></linearGradient></defs><path fill="url(#a)" d="M240 353h0 10 1 6 8l-5 1c0 1-1 1 0 2 0 1 1 2 1 2h4c1 0 1 0 2 1l1 1h1 1v2h1v3h0l-3 3c-2 4-5 7-6 10l-1 1c-1-1-1-1-1-2h0c-1 1-1 1-2 1h-3c-1 1-1 2 0 4h-1-2v-1c1-1 1-1 1-2h0-1l1 1h-1l-1-1h-1l1 1h-2c0 1 1 1 1 2l-1 1v1h0l-3 1c0 1 0 2-1 3l-1-2c-1 1-2 1-3 1v-1h0v-1-9l-1-5v-1l1-1c0-1 0-2-1-2l1-1-2-2-2-2-1-1h2v-1c1-1 1-2 0-3 0 0 1 0 1-1v-3h1z"></path><path d="M257 368h1c1 1 1 1 1 2h-1c-1-1-1-1-1-2z" class="D"></path><path d="M243 368c1 0 2 1 3 1l-1 1c0 1 1 2 1 2h2v1h0-1v1c-1-1-2-2-3-2h-1c0-1-1-1-1-2s1-1 1-1v-1z" class="C"></path><path d="M253 375l-1-2h1c1 0 2 0 3 1 1 0 2 1 3 2l1 1c-1 1-1 1-2 1h-3-1c1-1 1-2 1-2l-2-1z" class="O"></path><path d="M258 363v-1s-1-1-1-2c1-1 1-1 2-1 1 1-1 1 0 2s2-1 3-1 1 0 2 1h4c0 1-1 2-2 2 0 1 0 1 1 2h-1c-1-1-2-3-4-3-1 0-2 1-3 2h-1v-1z" class="L"></path><path d="M240 362h3c0 1 1 2 2 2 1 1 2 4 3 6h0c-1 0-2 0-2-1-1 0-2-1-3-1v1s-1 0-1 1 1 1 1 2h-1c-1 0-2-1-2-1v-1l1-1c0-1 0-2-1-2l1-1-2-2 1-2z" class="j"></path><path d="M239 364c1 0 3 0 4 1v1l-1 1h0l1 1v1s-1 0-1 1 1 1 1 2h-1c-1 0-2-1-2-1v-1l1-1c0-1 0-2-1-2l1-1-2-2z" class="R"></path><path d="M240 353h0 10 1v1c0 1 1 2 1 3l-1 1h-3c0 1 0 2 1 2l-1 1h0-2v-1h-2c-2 1-2 1-4 1l-1 1h1l-1 2-2-2-1-1h2v-1c1-1 1-2 0-3 0 0 1 0 1-1v-3h1z" class="K"></path><path d="M246 360c-1 0-1-1-2-1v-1h1c1 1 1 1 2 1l1-1c0 1 0 2 1 2l-1 1h0-2v-1z" class="B"></path><path d="M250 353h1v1c-1 0-2 1-2 1v1l-2-2c-1-1 0 0-2 0-1 0-3-1-5-1h10z" class="d"></path><path d="M240 361c0-1-1-1-1-1 0-2 1-2 1-4l1 1h2c1 1 1 2 1 3-2 1-2 1-4 1z" class="j"></path><path d="M268 360h1 1v2h1v3h0l-3 3c-2 4-5 7-6 10l-1 1c-1-1-1-1-1-2v-1l2-1-1-1c-1 0-1-1 0-2h1c-1-2-1-3-1-4l-2-2v-2h-1-1l1-1v1h1c1-1 2-2 3-2 2 0 3 2 4 3h1c-1-1-1-1-1-2 1 0 2-1 2-2v-1z" class="D"></path><path d="M262 372h1v1c0 1-1 1-1 2l-1-1c-1 0-1-1 0-2h1z" class="K"></path><path d="M262 368h3v1 1l-1 1c0-1-1-1-2-2v-1z" class="B"></path><path d="M240 371s1 1 2 1h1 1c1 0 2 1 3 2v-1c1 0 1 0 2-1h1l2 2h0l1 1 2 1s0 1-1 2h1c-1 1-1 2 0 4h-1-2v-1c1-1 1-1 1-2h0-1l1 1h-1l-1-1h-1l1 1h-2c0 1 1 1 1 2l-1 1v1h0l-3 1c0 1 0 2-1 3l-1-2c-1 1-2 1-3 1v-1h0v-1-9l-1-5z" class="K"></path><path d="M243 379v-1l1-1 1 1v2h-1l-1-1zm3-2c-1-1-1-1 0-2h0 2c0 1 0 2-1 2h-1z" class="U"></path><path d="M243 384l-1-1v-3l1-1 1 1v3l-1 1z" class="i"></path><path d="M240 371s1 1 2 1h1 1c0 1 1 1 2 2h-1-2c-1 0-1-1-2-1v3l-1-5z" class="L"></path><path d="M246 377h1v1c1 0 3 0 3 1l1 1h-2c0 1 1 1 1 2l-1 1v1h0l-3 1c0 1 0 2-1 3l-1-2c-1 1-2 1-3 1v-1h0 2 1v-1h-2l1-1 1-1v-3h1v2c1 1 1 1 1 2h2v-2c-1-2-2-3-2-5h0z" class="C"></path><path d="M255 378h3c1 1 0 2 0 3l1 1c1 0 2 1 3 2-1 1-2 3-2 5h1c0 1 0 1-1 2 2 1 2 2 2 4h0v1 1l1 2c-1 0-1 1-1 1-2 0-3 2-4 3l-1 1-2 1c0 2-1 2-2 3h-1 0-1c-1-1 0-2-2-2 0 0-1 0-2 1-1 0-2 0-2 1l-1 2-1 1c-3 3-9 5-12 5-2 0-5 0-7 1l-2-2h0l1-1v-1l1-1v-2s1 0 2-1h0-1l-2-1c-1-1-1-1-2-1v-4h2v-3h1 4c1-1 1-1 1-2v-1c1-1 1-1 1-2 1 0 1 0 1-1l2 1h1 0 1c2-2 4-6 4-8 0-1 0-1 1-2h0 1v1h0v1c1 0 2 0 3-1l1 2c1-1 1-2 1-3l3-1h0v-1l1-1c0-1-1-1-1-2h2l-1-1h1l1 1h1l-1-1h1 0c0 1 0 1-1 2v1h2 1c-1-2-1-3 0-4z" class="K"></path><path d="M240 392h2c0 1 0 1-1 2 0 0 0 1-1 1v-3z" class="D"></path><path d="M238 401l1 1c1-1 1-3 3-4h0c0 1 1 1 0 2-1 0-2 1-3 3l1 1h-3l-1-1 2-2z" class="R"></path><path d="M230 404c0-1 1-1 1-1 1-1 1-2 2-3s2-1 2-2l-1-1h0l1-1 1 1h3c0-1 1-1 2-1l1 1h-1c-1 0-1 0-2 1h0c0 2-1 2-1 3l-2 2 1 1c-2 1-3 2-4 3l-1 1h0-3l2-1c0-1 0-2-1-3z" class="L"></path><path d="M236 403l1 1c-2 1-3 2-4 3l-1 1v-3c2-1 2-2 4-2z" class="K"></path><path d="M240 385h1v1h0c-1 4-3 8-6 10l-1 1h0l1 1c0 1-1 1-2 2s-1 2-2 3c0 0-1 0-1 1 1 1 1 2 1 3l-2 1v1h-3-1l-2-1c-1-1-1-1-2-1v-4h2v-3h1 4c1-1 1-1 1-2v-1c1-1 1-1 1-2 1 0 1 0 1-1l2 1h1 0 1c2-2 4-6 4-8 0-1 0-1 1-2h0z" class="N"></path><path d="M231 394l2 1-2 2h-1l-1 1v-1c1-1 1-1 1-2 1 0 1 0 1-1z" class="R"></path><path d="M230 404c1 1 1 2 1 3l-2 1v1c-1 0-2 0-3-1l1-1 3-3z" class="K"></path><path d="M224 400h4l-1 1c1 0 2 1 2 2h0c-1 2-3 3-4 5v1l-2-1c-1-1-1-1-2-1v-4h2v-3h1z" class="n"></path><path d="M224 400h4l-1 1c1 0 2 1 2 2h0c-2-1-4 0-6 0v-3h1z" class="L"></path><path d="M243 402c1 2 1 3 0 5v1l-2 3h2c-3 3-9 5-12 5-2 0-5 0-7 1l-2-2h0l1-1v-1l1-1v-2s1 0 2-1h0 3v-1h3 0l1-1c1-1 2-2 4-3h3l1 1c1-1 2-2 2-3z" class="H"></path><path d="M224 412c1 0 1 0 2-1v3h-2l-1-1 1-1zm17-5h1l1 1-2 3h-1-2v-2h2c1 0 1 0 2-1l-1-1z" class="S"></path><path d="M243 402c1 2 1 3 0 5v1l-1-1h-1c-2 1-3 2-5 2-1 0-1-1-1-1 2-1 4-1 6-3 1-1 2-2 2-3z" class="g"></path><path d="M237 404h3l1 1c-2 2-4 2-6 3h-1-2 0l1-1c1-1 2-2 4-3z" class="d"></path><path d="M232 408h2v2c-1 1-2 1-3 1h-1-4 0c-1 1-1 1-2 1v-2s1 0 2-1h0 3v-1h3z" class="k"></path><path d="M241 411h2c-3 3-9 5-12 5-2 0-5 0-7 1l-2-2h0l1-1v-1l1 1c0 1 0 1 1 1 4 1 12-2 15-4h1z" class="B"></path><defs><linearGradient id="b" x1="246.812" y1="389.685" x2="249.469" y2="399.138" xlink:href="#B"><stop offset="0" stop-color="#1b1b1a"></stop><stop offset="1" stop-color="#3a3839"></stop></linearGradient></defs><path fill="url(#b)" d="M255 378h3c1 1 0 2 0 3l1 1c1 0 2 1 3 2-1 1-2 3-2 5h1c0 1 0 1-1 2 2 1 2 2 2 4h0v1 1l1 2c-1 0-1 1-1 1-2 0-3 2-4 3l-1 1-2 1c0 2-1 2-2 3h-1 0-1c-1-1 0-2-2-2 0 0-1 0-2 1-1 0-2 0-2 1l-1 2-1 1h-2l2-3v-1c1-2 1-3 0-5v-1c0-2 1-4 1-6v-1h1v-1h-1c0-2 0-2 1-3 1 0 2 1 3 1h1c0-1 1-2 2-2v-1-1l-1-1v-4h0c0-1-1-1-1-2h2l-1-1h1l1 1h1l-1-1h1 0c0 1 0 1-1 2v1h2 1c-1-2-1-3 0-4z"></path><path d="M251 402h2l1-1-1-1c-1 0-1 0-2-1h1 1l1-1c1 1 3 2 4 3v1 1l-1 1c-1-1-1-1-1-2-1 1-2 1-2 2h-1s-1 0-1-1h-1 0v-1z" class="R"></path><path d="M256 395l2 1h0c1-1 1-1 1-2h0l1-1c1 1 1 2 2 3v1l1 2c-1 0-1 1-1 1-2 0-3 2-4 3v-1-1c-1-1-3-2-4-3l1-1h0c0-1 0-1 1-2z" class="D"></path><path d="M256 395l2 1h0c1-1 1-1 1-2h0l1-1c1 1 1 2 2 3v1 2h0l-1-1s0-1-1-1l-1-1-1 1h-3 0c0-1 0-1 1-2z" class="O"></path><path d="M244 395l1 1c1 1 2 1 2 2 1 1 3 1 3 2 1 0 1 1 1 2h0v1h0 1c0 1 1 1 1 1h1c0-1 1-1 2-2 0 1 0 1 1 2l-2 1c0 2-1 2-2 3h-1 0-1c-1-1 0-2-2-2 0 0-1 0-2 1-1 0-2 0-2 1l-1 2-1 1h-2l2-3v-1c1-2 1-3 0-5v-1c0-2 1-4 1-6z" class="F"></path><path d="M251 403h0 1c0 1 1 1 1 1h1c0 1 0 1-1 2-1-1-2-1-4-2l2-1z" class="B"></path><path d="M245 396c1 1 2 1 2 2 1 1 3 1 3 2 1 0 1 1 1 2h0v1l-2 1-1 1c-2 0-2 0-2-1s0-1-1-2h-1v-4l1-2z" class="H"></path><path d="M246 404c0-1 0-1 1-2-1 0-1-1-2-2 1 0 1-1 1-1h1c0 1 0 1 1 2l3 1h0v1l-2 1-1 1c-2 0-2 0-2-1z" class="S"></path><path d="M255 378h3c1 1 0 2 0 3l1 1c1 0 2 1 3 2-1 1-2 3-2 5h1c0 1 0 1-1 2 2 1 2 2 2 4h0v1c-1-1-1-2-2-3l-1 1h0c0 1 0 1-1 2h0l-2-1h-3 0v1h0-1c-2 0-2-1-3-2v-3c0-1 1-2 2-2v-1-1l-1-1v-4h0c0-1-1-1-1-2h2l-1-1h1l1 1h1l-1-1h1 0c0 1 0 1-1 2v1h2 1c-1-2-1-3 0-4z" class="i"></path><path d="M252 382v1c-1 0-1 0-1-1v-1h1v1z" class="h"></path><path d="M258 390c0-1 0-1 1-2h0l1 1c0 1 0 1-1 2h0l-1-1z" class="F"></path><path d="M251 387v-2-1c1 0 2 0 3 1v1h0s-1 0-1 1l1 2h-3 0 0v-1-1z" class="I"></path><path d="M255 378h3c1 1 0 2 0 3h0v5c-1-1-2-3-3-4h0c-1-2-1-3 0-4z" class="Q"></path><path d="M251 389h0 0 3v1c0 1 1 1 2 2h0-1c-1 1-1 2-2 3h0v1h0-1c-2 0-2-1-3-2v-3c0-1 1-2 2-2z" class="J"></path><path d="M251 389h0l1 2-1 1-2 2v-3c0-1 1-2 2-2z" class="F"></path><path d="M251 392c1 0 2 1 2 2v1 1h0-1c-2 0-2-1-3-2l2-2z" class="Q"></path><path d="M206 352c0 1 0 1 1 1 0 1 1 2 2 3v1l2-3 3 2 3-3c1 1 2 2 3 2s1 0 2 1h4c3 1 5 2 7 4h0l3 1 1 1 2 2 2 2-1 1c1 0 1 1 1 2l-1 1v1l1 5v9h-1 0c-1 1-1 1-1 2 0 2-2 6-4 8h-1 0-1l-2-1c0 1 0 1-1 1 0 1 0 1-1 2v1c0 1 0 1-1 2h-4-1v3h-2v4c1 0 1 0 2 1h0-1-2c1-1 1-1 1-2h-1-1v1l-1 1v-1h-1 0c-1 0-2 0-3-1l-1 2c0-1-1-1-2-2-1 1-2 1-3 1v-1h0-2-1v-1l2-2-4-5-2-4c-1-1-1-2-2-3l1-1v-2h0c-1-1-2-4-2-6l-2-4v-1l2-1 1-1 1-2s1-2 1-3l1-1c-1 0-2 0-3-1l-1-1c1-1 2-2 3-2-1 0-2 0-3-1 0-1 0-2-1-3h0l1-2c0-1 1-1 2-2l1-1 2-2 1-1 2-1z" class="H"></path><path d="M229 391c1 1 1 2 2 3 0 1 0 1-1 1 0 1 0 1-1 2 0-2 0-2-1-4l-1-1 2-1z" class="h"></path><path d="M204 371h0c2 2 3 3 5 4-2 0-2 0-4 1h0l-1-1v-4z" class="K"></path><path d="M225 388c1 1 2 2 4 3l-2 1 1 1v1h-1l-2-2-1-1h0l1-2v-1z" class="F"></path><path d="M227 382h4v5l-1 1c-1 0-2 0-2-1v-1h0l-2 1h0c0-3-2-2-2-4 1 0 1 0 2-1h1z" class="O"></path><path d="M215 383c0-1-1-2 0-3 1-2 2-2 4-2l3 3s0 1-1 2h0c-1 1-2 1-3 1-1 1-2 0-3-1z" class="W"></path><path d="M217 381c1-1 1-1 3 0v1l-1 1h-2l-1-1 1-1z" class="F"></path><path d="M235 381l3 1c1 1 1 2 2 3h0c-1 1-1 1-1 2 0 2-2 6-4 8h-1 0l-2-3h-1c-1-1-2-2-2-4h-2l1-1c0 1 1 1 2 1l1-1v-5c1 0 2 0 2-1h2z" class="K"></path><path d="M210 360c1 0 2 1 2 2h1l1-1h1v3 1h0v6 2 1 2c-1 1-1 1-3 1-1-2 0-1 0-3h-1v1c-1-1-3-1-3-3h0c1 0 2-1 2-1v-1c-1 0-2 1-2 1h-1-1l-3-3v-1-1c1 0 2-1 2-1l4-4 1-1z" class="U"></path><path d="M213 370h-1c0-1-1-1-2-2v-2h1l4 2-1 2h-1z" class="j"></path><path d="M213 362l1-1h1v3l-1 1h-3c-1 0-1 0-1-1 1 0 2-1 3-2z" class="n"></path><path d="M205 365h2 1c1 1 1 2 0 4h-1v-1c-2-1-2-1-3-1l-1-1c1 0 2-1 2-1z" class="O"></path><path d="M215 365h0v6 2 1 2c-1 1-1 1-3 1-1-2 0-1 0-3h-1v1c-1-1-3-1-3-3h0c1 0 2-1 2-1v-1c-1 0-2 1-2 1h-1-1l-3-3v-1-1l1 1c1 0 1 0 3 1v1c0 1 0 1 1 1h1c1 0 2 1 3 1v2h1v-3h1l1-2c-1-1-1-2 0-3z" class="B"></path><path d="M206 352c0 1 0 1 1 1 0 1 1 2 2 3v1l2-3 3 2-3 2-1 2-1 1-4 4s-1 1-2 1v1 1l3 3h-2 0l-1-2h-1c-1 0-2 0-3-1l-1-1c1-1 2-2 3-2-1 0-2 0-3-1 0-1 0-2-1-3h0l1-2c0-1 1-1 2-2l1-1 2-2 1-1 2-1z" class="V"></path><path d="M203 362h1c0 1-1 1-2 2v3s-1 1-2 1h-1l-1-1c1-1 2-2 3-2l2-3z" class="L"></path><path d="M200 357h1c1 0 1-1 1-2l1 1v1 2c-1 1-2 1-3 1v1h0c2 0 2 0 3 1l-2 3c-1 0-2 0-3-1 0-1 0-2-1-3h0l1-2c0-1 1-1 2-2z" class="W"></path><path d="M206 352c0 1 0 1 1 1 0 1 1 2 2 3v1l2-3 3 2-3 2-1 2-1 1c-1-1-2-1-2-2l-1 1v-1h0l1-1v-1c-1-1-1-2-2-3-1 1 0 2-1 3h-1v-1l-1-1c0 1 0 2-1 2h-1l1-1 2-2 1-1 2-1z" class="l"></path><path d="M211 354l3 2-3 2-1 2-1 1c-1-1-2-1-2-2l2-2 2-3z" class="W"></path><path d="M202 369h1l1 2v4l1 1h0c2-1 2-1 4-1 0 1 3 4 3 4v3l-1 1v1l-1 2c-1 1-3 2-3 3-1 1-2 1-3 1-2 0-3 0-4-2h0c-1-1-2-4-2-6l-2-4v-1l2-1 1-1 1-2s1-2 1-3l1-1z" class="U"></path><path d="M210 385l-1 1-2-2s1-1 0-2l-1 1h0v-1h-3v-1l1 1c2-1 3-1 5-1v1s1 1 1 2v1z" class="j"></path><path d="M198 382c1 0 2 0 2 1h2c1 0 1 1 2 1v1c-2 0-3-2-4 0v3c-1-1-2-4-2-6z" class="K"></path><path d="M202 369h1l1 2v4c-1-1-4 0-5 0l1-2s1-2 1-3l1-1z" class="E"></path><path d="M209 375c0 1 3 4 3 4v3l-1 1v1l-1 2v-1-1c0-1-1-2-1-2v-1c-2 0-3 0-5 1l-1-1v-2h-3s-1 0-1-1c1 0 1-1 2-2h1c1 0 0 1 1 1s1 0 2-1h0c2-1 2-1 4-1z" class="C"></path><path d="M231 365c2 2 4 3 5 5h1s1-1 2-1l1-2c1 0 1 1 1 2l-1 1v1l1 5v9h-1c-1-1-1-2-2-3l-3-1h-2c0 1-1 1-2 1h-4c-1 0-1-1-2-1h-2l-1 1v-1c1-2 0-2 0-4h1c3-3 7-6 8-10v-1-1z" class="E"></path><path d="M236 370h1c0 2 1 4 1 6l-2-1v-2-3z" class="D"></path><path d="M232 377l1-2h1c1 1 2 2 3 2v1c-2-1-3-1-5-1zm-9 0v1 1l3-1c0-1-1-1 1-3v1l1-1c-1 1 0 3-1 4h0c0 1-1 1-1 2h-1-2l-1 1v-1c1-2 0-2 0-4h1z" class="B"></path><path d="M227 379l1 1c1-1 1-2 2-3l1 1 2 3c0 1-1 1-2 1h-4c-1 0-1-1-2-1h1c0-1 1-1 1-2h0z" class="e"></path><path d="M239 369l1-2c1 0 1 1 1 2l-1 1v1l1 5v9h-1c-1-1-1-2-2-3l-3-1h-2l-2-3 1-1c2 0 3 0 5 1v-1h1v-1c0-2-1-4-1-6 0 0 1-1 2-1z" class="M"></path><defs><linearGradient id="c" x1="233.216" y1="380.56" x2="239.189" y2="376.717" xlink:href="#B"><stop offset="0" stop-color="#454445"></stop><stop offset="1" stop-color="#606060"></stop></linearGradient></defs><path fill="url(#c)" d="M232 377c2 0 3 0 5 1 1 0 2 0 3 1v1 1h-1 0c-1-1-2-1-3-1l-1 1h-2l-2-3 1-1z"></path><path d="M217 353c1 1 2 2 3 2s1 0 2 1h4c3 1 5 2 7 4h0l3 1 1 1 2 2 2 2-1 1-1 2c-1 0-2 1-2 1h-1c-1-2-3-3-5-5v1l-1 1c-2 2-5 7-8 9h-1l-1-1h-1c-1 0-1 1-2 0l-1-1h-1v-1-2-6h0v-1-3h-1l-1 1h-1c0-1-1-2-2-2l1-2 3-2 3-3z" class="e"></path><path d="M221 366c1 1 2 1 2 1v2l-2 1v-4zm-6 4c1-1 1-3 2-3l1-1h1c-1 1-1 1-1 2l1 1v1h0c-1-1-2-1-2 0h-2z" class="B"></path><path d="M221 364c2 0 3 1 4 3h0l-1 1-1-1s-1 0-2-1v-2z" class="K"></path><path d="M215 370h2 2v1h-1l-3 2v-2-1z" class="M"></path><path d="M218 371c0 1 0 1 1 2l1 1-1 1c-1 0-1 1-2 0l-1-1h-1v-1l3-2zm-8-11l1-2c1 1 2 1 2 2 1 0 1-1 2 0h1 2c1 1 3 1 5 1h0-3v1c-1 0-3 0-4 1l1 1 1-1 2 1v1c-2 1-4-1-5 0h0v-1-3h-1l-1 1h-1c0-1-1-2-2-2z" class="R"></path><path d="M223 361c3 1 5 2 8 4v1l-1 1-1-1c-1 0-2 0-3 1h0-1 0c-1-2-2-3-4-3l-1-2v-1h3z" class="j"></path><path d="M217 353c1 1 2 2 3 2s1 0 2 1c-1 0-2 1-2 2-1 1-1 2-2 2h-2-1c-1-1-1 0-2 0 0-1-1-1-2-2l3-2 3-3z" class="B"></path><path d="M225 367h1 0c1-1 2-1 3-1l1 1c-2 2-5 7-8 9l-1-1v-3c0-1-1-1 0-2l2-1v-2l1 1 1-1z" class="C"></path><path d="M224 368c1 1 1 1 1 3v1c-1 0-2 0-3-1 0 0 0-1 1-2v-2l1 1z" class="j"></path><path d="M218 360c1 0 1-1 2-2 0-1 1-2 2-2h4c3 1 5 2 7 4h0l3 1 1 1 2 2 2 2-1 1-1 2c-1 0-2 1-2 1h-1c-1-2-3-3-5-5-3-2-5-3-8-4h0c-2 0-4 0-5-1z" class="G"></path><path d="M237 362l2 2 2 2-1 1-1 2c-2-1-4-4-6-5h1 1l2-2z" class="V"></path><path d="M224 359c2-1 3 0 5 0v1h1c1 0 2 1 3 0h0l3 1 1 1-2 2h-1-1c-1-1-2-2-3-2h-1c-1-1-3-2-5-3z" class="c"></path><path d="M230 362h0c1-1 2-1 4-1v3h-1c-1-1-2-2-3-2z" class="b"></path><path d="M233 360h0l3 1 1 1-2 2h-1v-3s-1 0-1-1z" class="g"></path><path d="M218 360c1 0 1-1 2-2 0-1 1-2 2-2h4c3 1 5 2 7 4h0 0c-1 1-2 0-3 0h-1v-1c-2 0-3-1-5 0h-1-1l1 2h0c-2 0-4 0-5-1z" class="S"></path><path d="M212 382v1h1 0c0-1 0-2 1-2v1l1 1c1 1 2 2 3 1 1 0 2 0 3-1h0 1v2c0 1 2 2 3 3v1l-1 2h0l1 1 2 2h1v-1c1 2 1 2 1 4v1c0 1 0 1-1 2h-4-1v3h-2v4c1 0 1 0 2 1h0-1-2c1-1 1-1 1-2h-1-1v1l-1 1v-1h-1 0c-1 0-2 0-3-1l-1 2c0-1-1-1-2-2-1 1-2 1-3 1v-1h0-2-1v-1l2-2-4-5-2-4c-1-1-1-2-2-3l1-1v-2c1 2 2 2 4 2 1 0 2 0 3-1 0-1 2-2 3-3l1-2v-1l1-1z" class="R"></path><path d="M211 384c1 1 1 1 1 2h2l-2 3-2 2c-1-1-2-1-3-2 0-1 2-2 3-3l1-2z" class="L"></path><path d="M222 401v-3-1c1 0 1 0 1-1h1v-1h0c-1 0-2 0-2-1-1-1-1-2 0-3l1-1v-1h2l-1 2h0l1 1 2 2h1v-1c1 2 1 2 1 4v1c0 1 0 1-1 2h-4-1l-1 1z" class="E"></path><path d="M225 392c0 1-1 2-2 2v-1c0-1 0-1 1-2l1 1z" class="U"></path><path d="M228 393c1 2 1 2 1 4v1c0 1 0 1-1 2h-4 0c0-2 0-2 1-2l1-3 1-1h1v-1z" class="B"></path><path d="M227 394h1c-1 1-1 1-1 2s1 2 0 3h0-1v-4l1-1z" class="d"></path><path d="M215 389c1-1 1-1 2-1-1-1-1-1-1-2h4c1 1 1 1 1 3v4 3 5h1l1-1v3h-2v4c1 0 1 0 2 1h0-1-2c1-1 1-1 1-2h-1-1v1l-1 1v-1h-1 0c-1 0-2 0-3-1v-1l1-1c1-1 0-3 0-4 0-4 0-7 1-11h-1z" class="N"></path><path d="M215 389c1-1 1-1 2-1-1-1-1-1-1-2h4c1 1 1 1 1 3v4 3h-1l-1-1c-2-1 0-3-1-4h0v-1c1-1 1-2 1-3h0l-2 1v1 2 1s0 1-1 1v-3-1h-1z" class="I"></path><path d="M218 390c1 1 0 2 2 4 0-1 0-1 1-1v3h-1l-1-1c-2-1 0-3-1-4h0v-1z" class="H"></path><path d="M219 395l1 1h1v5h1l1-1v3h-2v4c1 0 1 0 2 1h0-1-2c1-1 1-1 1-2h-1c-1-1-1-1-2-1 0 0-1-1-1-2v-1h0v-3l1-1h-1 0c1-2 1-2 2-3z" class="F"></path><path d="M219 395l1 1h1v5 1c-2-1-3-2-4-3l1-1h-1 0c1-2 1-2 2-3z" class="I"></path><path d="M219 395l1 1v1c-1 1-1 1-2 1h-1 0c1-2 1-2 2-3z" class="D"></path><path d="M212 389c1 0 2 0 3-1v1h1c-1 4-1 7-1 11 0 1 1 3 0 4l-1 1v1l-1 2c0-1-1-1-2-2-1 1-2 1-3 1v-1h0-2-1v-1l2-2-4-5-2-4c-1-1-1-2-2-3l1-1v-2c1 2 2 2 4 2 1 0 2 0 3-1 1 1 2 1 3 2l2-2z" class="U"></path><path d="M207 403l4 3c-1 1-2 1-3 1v-1h0-2-1v-1l2-2z" class="b"></path><path d="M200 388c1 2 2 2 4 2 1 0 2 0 3-1 1 1 2 1 3 2 0 1-1 2-2 2v1l-1 3c-1 1-2 0-3 0l-1 1-2-4c-1-1-1-2-2-3l1-1v-2z" class="L"></path><path d="M200 390c1 1 3 2 4 4l-1 1c0 1 0 1 1 2l-1 1-2-4c-1-1-1-2-2-3l1-1z" class="D"></path><path d="M207 389c1 1 2 1 3 2 0 1-1 2-2 2v1l-1 3c-1 1-2 0-3 0-1-1-1-1-1-2l1-1c1-1 2-1 3-2h0l1-1-1-2z" class="g"></path><path d="M206 406h2 0v1c1 0 2 0 3-1 1 1 2 1 2 2l1-2c1 1 2 1 3 1h0 1v1l1-1v-1h1 1c0 1 0 1-1 2h2 1 0l2 1h1 0c-1 1-2 1-2 1v2l-1 1v1l-1 1h0l2 2c2-1 5-1 7-1 3 0 9-2 12-5l1-1 1-2c0-1 1-1 2-1 1-1 2-1 2-1 2 0 1 1 2 2h1 0v1h0 2c2 3 0 21 1 26l-1 1c-1 1-3 0-3 1v4h0l-1 1-1 1v3c1 1 1 2 0 3 0 1-1 2-1 3 1 1 2 1 3 3 1 1 3 2 3 3h1v1c1 1 1 1 1 3-1 0-2 0-3 1h1c1 1 1 1 2 1-1 0-1 0-1 1 1 1 1 1 0 2v1l1 1v1c1 0 1 0 2 1h1l-2 1-2-1-2 1v1l1-1c1 1 1 1 1 2v1 1 1h0v3c0 4-1 8-1 12h0c0 3 1 5 1 7h4l-1 3h0c2 0 3 1 4 1 1 1 1 1 0 2l-1 2c2 3 2 5 4 7-2 2-3 3-6 4-2 0-3 0-5-1v6c-1 1-3 0-4-1 1 2 2 3 4 4 0 1-2 2-1 3s2 0 2 2v18c-1 1-2 1-3 2l2 1h1v2c-1 0-1 1-2 1l-1 1 1 1 2 1v3 1h0 1l1 1h0-2v4 8l1 2 4 6v4h-1l-2-1v1c1 1 1 1 3 2h-2l3 2v1h4c1 0 1 0 2 1v1l2 2-1 1h-3-2c-2 0-3 0-4 1-1 0-1 0-2-1 1 0 1 0 1-1l-1-1h-1c-1 2 0 3-1 4l-1 1c0-2-1-3-1-4 0 1 0 2-1 3l-3-3-1-2c0 1-2 1-2 1l-1 1s-1 0-1-1h0v-2c-1 0-1-1-1-2 0 1 0 3-1 3l-1 2c-1 1-2 2-2 4-1 1-1 2-2 3h-2v-1h-1v2c-1 1-2 2-3 2h0v-1-1l-2 2h-1l-1 1-1-1 1-2c-1-1-2 0-2-1 1 0 5-2 6-3h0l-1-2h0s-1-1-1-2h0-1-1v-1c-2-1-2-1-2-3 0 1 1 1 2 2h1c1-1 3-1 4-2s1-2 2-2c0-1 1-1 2-2l-1-1c1-1 1-2 0-4 1-1 1-2 2-3-3-3-5-7-7-11h1v-2-1c-2-2-3-3-4-5v-1l-1-24c0-3 1-7 0-10v-1-4-3-1-9c0 1 0 1-1 1v-1s0-1-1-1l-1 1v1c-1 0-1 0-1-1v-2s-1 0-1-1l-1-1c-1-1-2-1-3-1h0c-1 0-1 1-2 1h-2v-1l-3-3v3c-1-4 0-9-1-13 0-2 0-1-1-3v-2l2-1v-2-4h-1s0 1-1 1v-6c0-1 0-5-1-6v-1h-1v1-1l-3-3c-1 0-1 0-2-1h1v-3c-1 0 0-1 0-2v-3h-1l-2-2c-1 0-1 0-2 1 1 1 1 2 1 2l-2-1c1-2 0-5 0-7v-6l-1-1c0-1 0-1-1-2h0v-1-1l-1-1v-1-4l2 1v-2-5c1-1 0-2 0-3l1-1h0c0-2 1-3 1-4 0-3-1-4 0-7 0 2 0 4 1 5v2h2 0c0-2 1-2 2-3v-2l1-1v1l1-1v-2h1 0v-1s1 0 1-1z" class="j"></path><path d="M252 508h1v1l-1 1h-1v-1l1-1z" class="U"></path><path d="M238 565h0c-1-1-2-1-3-2v-1s1 0 2 1h1v2z" class="K"></path><path d="M255 476v1h0v3c-1-1-2-1-3-2h0c1-1 2-1 2-1l1-1z" class="O"></path><path d="M229 478h1c0 1 1 1 1 2v1c-1 1-1 1-2 1v-1s0-1-1-1c0-1 0-1 1-2zm15-54l1 1c-1 1-2 1-3 1-1 1-2 2-4 2 0 1 1 2 1 3l-1-1c-2-1-2 0-3 0v-3c0 1 1 1 1 1 2 0 4-2 6-2 1 0 1-1 2-2z" class="C"></path><path d="M258 502c2 0 3 1 4 1 1 1 1 1 0 2l-1 2c-2-1-2-3-3-5z" class="O"></path><path d="M228 606l1-1 1-1h0 1c1-1 2-2 2-3h0c0-1 1-1 1-2 1-1 2-1 3-2h0l-4 4c0 1 1 1 1 1v2c-1 1-2 2-3 2h0v-1-1l-2 2h-1zm24-101l-3 3h-1c0-1 1-1 1-2h0v-1h-2l-2-2h-1 1l-1-1c-1 0-1 0-1-1 2 2 5 3 8 3h1l-1 1h1z" class="U"></path><path d="M235 583c1-1 1-2 2-3 1 1 1 1 1 2v1l1 1c0 1-2 3-3 4l-1-1c1-1 1-2 0-4z" class="d"></path><path d="M239 594v-1h2l-1-1c0-1 0-1 1-1v-1-2c2 1 3 1 4 2-1 1-1 2-1 3h1c0 1-1 1-1 2v-2c-1 0-1-1-1-2 0 1 0 3-1 3h-3z" class="U"></path><path d="M230 598h0c3-1 5-3 6-6s4-6 5-8c0 3-1 4-3 7s-4 6-7 9l-1-2h0z" class="E"></path><path d="M248 562v-1c0-3 0-3-1-5h-3c-1 1-1 1-1 2l-1 1v-2c1 0 1-1 2-1v-1c1 0 1 0 2 1v-1-2c1 0 2-1 3-2l1 1c-1 0-1 1-1 2h0c1 1 1 2 1 3v2 1c-1-1 0-2-1-3h-1l1 2v3h-1zm-8-142h1v2h0l-2-1v1c0 1 1 1 1 3-1 1-1 1-2 1l-1-1c-1-2-2-2-4-3 0 0 0-1-1-1l1-1c1 1 2 1 2 1h2 0c1-1 2-1 3-1zm1 113c0-1 1-1 1-1l1 1c-1 1-1 1-2 1-1 1-1 2-1 2v2h1l1-1c0-1 1-1 1-2 1 0 1-1 2-1v1c-1 2-3 3-4 5h0v1h0v-2l-3 3h-1l2-2v-1c0-1-1-1-2-1 0-1 1-2 2-3 0-1 1-2 2-2z" class="O"></path><path d="M249 562h0c1-1 2-3 4-3h1c-1 2-3 3-4 4-2 2-5 4-6 7h-1v-4c1-1 2-1 2-2l3-2h1z" class="E"></path><path d="M239 594h3l-1 2c-1 1-2 2-2 4-1 1-1 2-2 3h-2v-1h-1s-1 0-1-1l4-4h0c0-1 1-2 2-3z" class="B"></path><path d="M235 602l1-1c1-1 1 0 2 0l-1-1c1-2 3-3 4-4-1 1-2 2-2 4-1 1-1 2-2 3h-2v-1z" class="U"></path><path d="M256 577l4 6v4h-1l-2-1h0-2 0c-2-1-3-1-3-3h0l1-1c2 1 3 1 4 1v-1c-1-1-1-2-2-2h0v-2l1-1z" class="C"></path><path d="M252 497s1 0 1-1l-1-1c0-1 2-3 2-3 0 3 1 5 1 7h4l-1 3c-2 1-4 1-6 3h-1l1-1v-2s-1-1-1-2c-1 0-1-1-1-1l2-2z" class="B"></path><path d="M252 497s1 0 1-1l-1-1c0-1 2-3 2-3 0 3 1 5 1 7-1 1-1 2-2 2 0 0-1 0-1-1v-1l1-1-1-1z" class="U"></path><path d="M237 563l2-2-1-2c0-2 3-3 4-5 0 1-1 2-2 3l2 1h0l-1-1c-1 1-2 1-2 1v1h2c1 1 0 2 1 3 0 0 2 1 3 1v1c0 1-1 1-2 2v4c-1-1-1-1-2-1l-1-1v-1c0-1 0-2 1-3v-1h0c-1 0-2 1-3 2v-2h-1z" class="C"></path><path d="M241 564h1v2 2h-1-1v-1c0-1 0-2 1-3zm-11-123c0-1 1-1 2-2 1 2 0 3 0 5 1 2 0 6 0 9h-1l-1 1c0-1 0-1-1-2s0-6 0-8c-1-1-1-2-1-3h1 1z" class="E"></path><path d="M229 441h1v1c0 1 0 2-1 2v-3z" class="R"></path><path d="M244 424l2-1h-2-1c-1-1-1-2-1-3s1-2 1-3h2c1 0 2-2 4-2h1c0 2-1 4-1 6s-1 3-2 3l-2 1h0l-1-1z" class="O"></path><path d="M246 473c0-1 1-2 2-3 1 1 1 1 2 1 1 1 2 1 3 1v1l1-1c1 1 1 1 1 2v1l-1-1c-1-1-2-1-3-1 0 1 0 1-1 1 0 1-1 3-1 4 1 1 1 2 0 2l1 2h-1-2c-1-1-2-1-4-3v-1h-2 0c1-1 1-1 2-1v-1h1s1 0 1 1h2c0-1-1-1-1-2v-1l-1-1h1z" class="C"></path><path d="M246 473c0-1 1-2 2-3 1 1 1 1 2 1 1 1 2 1 3 1v1h-7z" class="N"></path><path d="M247 585c0-1 0-1-1-2l1-1s1 0 1-1c1 0 3 1 4 2 0 2 1 2 3 3h0 2 0v1c1 1 1 1 3 2h-2l3 2v1c-7-1-9-3-14-7z" class="K"></path><path d="M247 585l1-1h0c1 1 1 1 2 1l2 2v1c2-1 2 0 4 0v1l-1 1h1c1 0 1 0 2-1h0l3 2v1c-7-1-9-3-14-7z" class="D"></path><path d="M227 561c2 1 3 1 5 3 2 1 4 2 5 4h1c2 1 2 3 4 4 1 0 2 0 3 1l1 1c0 1 1 1 1 1 0 1-2 2-2 3h-2 0v-2l-1-1c-1 1-1 1-2 1l-1-1c1-1 1-2 1-3h-1l-1-1c0-1-1-1-1-2h-2c-1-2-2-3-4-3-2-2-3-3-4-5z" class="F"></path><path d="M241 588l3-3c1 0 1 0 1 1l2 2 3 3c1 0 1 1 2 1l1 1v1h-1v1h1 1l-1 1c0 1 0 2-1 3l-3-3-1-2c0 1-2 1-2 1l-1 1s-1 0-1-1h0c0-1 1-1 1-2h-1c0-1 0-2 1-3-1-1-2-1-4-2z" class="V"></path><path d="M245 590c0 1 2 4 3 4 0 1-2 1-2 1l-1 1s-1 0-1-1h0c0-1 1-1 1-2h-1c0-1 0-2 1-3z" class="C"></path><path d="M252 592c3 0 5 0 8 1 2 1 3 1 5 0l1 1s0-1 1-1v1l2 2-1 1h-3-2c-2 0-3 0-4 1-1 0-1 0-2-1 1 0 1 0 1-1l-1-1h-1c-1 2 0 3-1 4l-1 1c0-2-1-3-1-4l1-1h-1-1v-1h1v-1l-1-1z" class="R"></path><path d="M253 594h0c2 0 3-1 4-1v1l-1 1c-1 2 0 3-1 4l-1 1c0-2-1-3-1-4l1-1h-1-1v-1h1z" class="C"></path><path d="M257 595c1 0 3-1 4-1 0 0 0 1 1 1 1-1 1-1 2-1l1 3h-2c-2 0-3 0-4 1-1 0-1 0-2-1 1 0 1 0 1-1l-1-1z" class="D"></path><path d="M243 411l1-1 1-2c0-1 1-1 2-1 1-1 2-1 2-1 2 0 1 1 2 2h1 0v1h0c0 1-1 1-2 2 0 0-1 1-2 1-4 2-8 5-13 6-1 1-3 1-4 1h-1c1 0 1 0 2-1l1-1h1 2c2-1 3-2 5-2 0-1 1-1 1-1 1 0 3-2 4-3h0c-1 1-1 1-2 1h-1c-2 3-10 5-13 5h-4s1 0 1 1v1l-1-1-2-1c2-1 5-1 7-1 3 0 9-2 12-5z" class="C"></path><path d="M231 566c2 0 3 1 4 3h2c0 1 1 1 1 2l1 1h1c0 1 0 2-1 3l1 1c1 0 1 0 2-1l1 1v2h0 2l-3 3c-1 0-3 1-4 1 0-1 0-1-1-2-3-3-5-7-7-11h1v-2-1z" class="G"></path><path d="M235 569h2c0 1 1 1 1 2-1 0-1 1-2 0l-1-2z" class="I"></path><path d="M238 577l2-1c1 1 1 1 1 2l1 1c1 0 1 0 1-1h2l-3 3-1-1h-1c0-1-1-3-2-3z" class="g"></path><path d="M231 567c1 1 2 2 3 4s3 4 4 6c1 0 2 2 2 3h1l1 1c-1 0-3 1-4 1 0-1 0-1-1-2-3-3-5-7-7-11h1v-2z" class="b"></path><path d="M231 424l-2-2 1-1c2 2 3 4 5 6v3c1 0 1-1 3 0l1 1h0c1 1 2 2 2 4h1 1c1 0 1 1 2 1l1-1 1 1h0 7c-1 1-3 0-3 1v4h0l-1 1-1 1v3c1 1 1 2 0 3 0 1-1 2-1 3 1 1 2 1 3 3 1 1 3 2 3 3h1v1c1 1 1 1 1 3-1 0-2 0-3 1h1c1 1 1 1 2 1-1 0-1 0-1 1 1 1 1 1 0 2v1l1 1v1c1 0 1 0 2 1h1l-2 1-2-1-2 1c-1 0-2 0-3-1-1 0-1 0-2-1-1 1-2 2-2 3h-1c-1 2-2 2-3 2h-1c-3 1-2 5-5 6h0c0-1 0-2-1-2v-2h-2s-1 0-1 1l-1-1 1-1c-1-2-1-4-2-5h0-1c0-2-1-4 0-5 0-1 0-2 1-3v3-4c1-1 2-2 2-4 0-1 1-1 2-1 1 1 0 2 0 4l1-1c0-1-1-2-1-3v-1c-1 2-2 0-4 0v4-6l1-1h1c0-3 1-7 0-9 0-2 1-3 0-5-1-1 0-3 0-5v-1l-1-1 1-1c0-2-1-5-1-7z" class="B"></path><path d="M236 458h-1v-1c1-1 2-1 3-1 0 1 0 2 1 2h-2-1z" class="S"></path><path d="M232 460c0-1 0-2 1-2 1 1 1 3 1 4h-1c-1 0-1-1-1-2z" class="N"></path><path d="M236 433h1s1 0 2 1h0l-2 2v2c-1-2-1-4-1-5z" class="h"></path><path d="M233 477c1-2 1-5 1-7 0-1 0-3 1-4v4 1 8-2h-2z" class="C"></path><path d="M232 460c0 1 0 2 1 2h1v3c0 2 0 6-1 7v1l-1-1c-1-4-1-8 0-12z" class="F"></path><path d="M233 462h1v3h-2c0-1 0-2 1-3z" class="I"></path><path d="M236 458h1 2v1h-1v1h0 2 0c1 0 1 1 1 1v3 3h0v5h-1 0-1l-1 1h-1c-1 0-2-1-2-2v-1-4-5l1-2v-1z" class="M"></path><path d="M240 464h1v3h-1c-1-1-1-2 0-3z" class="Z"></path><path d="M240 460c1 0 1 1 1 1v3h-1c-1-1-1-2-1-3l1-1z" class="k"></path><path d="M235 466v-5 4l1 1 1-1v3c-1 1-1 1-1 2h-1v-4z" class="F"></path><path d="M236 459h1c1 1 0 3 0 4l1 1-1 1h-2v-4l1-2z" class="H"></path><path d="M236 470v1h2 0c0-1 0-2 1-3 1 1 1 2 1 3v1h-1l-1 1h-1c-1 0-2-1-2-2v-1h1z" class="e"></path><path d="M241 461l2 2v-2l1 1h-1c1 1 2 2 3 2 0 0 1 0 2 1v4 1h0c-1 1-2 2-2 3h-1c-1 2-2 2-3 2h-1c-3 1-2 5-5 6h0c0-1 0-2-1-2v-8c0 1 1 2 2 2h1l1-1h1 0 1v-5h0v-3-3z" class="D"></path><path d="M241 461l2 2v3c0 1 1 2 3 2v1 1c-1 0-2 0-3 1h0-1v-4h-1 0 0v-3-3z" class="I"></path><path d="M243 461l1 1h-1c1 1 2 2 3 2 0 0 1 0 2 1v4h-2v-1c-2 0-3-1-3-2v-3-2z" class="G"></path><path d="M231 424l-2-2 1-1c2 2 3 4 5 6v3 7h0l1 1h-1c0 1-1 1-1 2 1 0 1-1 3-1l1 1v1l-2 2v1h2 0c0 1-1 1-1 2h-1l1 1h1v3h0c-1 0-1-1-2-1l-1 1v2l-2 1v1h1 0l1 1-1 1h-1c0-1-1-2-2-3h1c0-3 1-7 0-9 0-2 1-3 0-5-1-1 0-3 0-5v-1l-1-1 1-1c0-2-1-5-1-7z" class="h"></path><path d="M232 438c1-1 1-1 3-1h0l-2 3h0c-1-1-1-1-1-2z" class="e"></path><path d="M235 441l1-1h1l1 1-2 2c0-1-1-1-1-2z" class="M"></path><path d="M235 441c0 1 1 1 1 2v1h2 0c0 1-1 1-1 2h-1c-1 1-1 2-2 2l-1-1c0-3 0-4 2-6zm-4-17l-2-2 1-1c2 2 3 4 5 6v3 7c-2 0-2 0-3 1 1-1 1-3 0-3v-8h0 1v-1c-1 0 0-1-1-1 0-1-1-1-1-1z" class="F"></path><path d="M245 436l1-1 1 1h0 7c-1 1-3 0-3 1v4h0l-1 1-1 1v3c1 1 1 2 0 3 0 1-1 2-1 3 0 0-1-1-2-1h0c0-1 0-1-2-1 0 1-1 2-2 2 0 1-1 2-1 2v2h-1v-11-7c-1-1 0-1 0-2h4v1l1-1z" class="g"></path><path d="M244 444c0-2 0-4-1-5v-1h1c0 1 0 2 1 3v1h0v3h-1v-1z" class="F"></path><path d="M240 438l2 2c1 1 1 1 0 2 0 2-1 2-1 4h1-1s-1 0-1-1v-7z" class="H"></path><path d="M245 441v-2c0-1 1-1 2-1l1-1h0l1 1s1 0 2-1v4h0l-1 1-1 1v3 1c-1 0-2 0-3-1l1-1c-1-1-1-2-2-3h0v-1z" class="c"></path><path d="M249 438s1 0 2-1v4h0l-1 1-1-4z" class="M"></path><path d="M247 445v-1-2-1c1 0 2 1 2 2v3 1c-1 0-2 0-3-1l1-1z" class="f"></path><path d="M245 442h0c1 1 1 2 2 3l-1 1c1 1 2 1 3 1v-1c1 1 1 2 0 3 0 1-1 2-1 3 0 0-1-1-2-1h0c0-1 0-1-2-1 0 1-1 2-2 2 0 1-1 2-1 2v2h-1v-11c0 1 1 1 1 1h1l2-2v1h1v-3h0z" class="e"></path><path d="M248 449h1c0 1-1 2-1 3 0 0-1-1-2-1h0c0-1 0-1-2-1l1-1h3z" class="L"></path><path d="M245 442c1 1 1 2 2 3l-1 1c1 1 2 1 3 1v-1c1 1 1 2 0 3h-1c-1 0-2-1-2-1v-2c-1-1-1-3-1-4z" class="G"></path><path d="M244 450c2 0 2 0 2 1h0c1 0 2 1 2 1 1 1 2 1 3 3 1 1 3 2 3 3h1v1c1 1 1 1 1 3-1 0-2 0-3 1h1c1 1 1 1 2 1-1 0-1 0-1 1 1 1 1 1 0 2v1l1 1v1c1 0 1 0 2 1h1l-2 1-2-1-2 1c-1 0-2 0-3-1-1 0-1 0-2-1h0v-1-4c-1-1-2-1-2-1-1 0-2-1-3-2h1l-1-1c-1 0-1 0-1-1h0l-1-2c0-1-1-1-2-1 0-1 0-1 1-1h1v-2s1-1 1-2c1 0 2-1 2-2z" class="K"></path><path d="M249 460c-1 0-2 1-3 0-1 0-2-1-2-2h1 0c1-1 3-1 4-1l1 1-1 2z" class="G"></path><path d="M244 450c2 0 2 0 2 1h0c0 1 1 2 1 4h-1-1v-2c-1 1-2 1-2 2v1s-1 1-1 2 1 0 0 2l-1-2c0-1-1-1-2-1 0-1 0-1 1-1h1v-2s1-1 1-2c1 0 2-1 2-2z" class="i"></path><path d="M246 451c1 0 2 1 2 1 1 1 2 1 3 3 1 1 3 2 3 3h1v1 2c-2 1-3 1-4 1l-1-1-1-1 1-2v-1c-1-2-2-1-3-2h0c0-2-1-3-1-4z" class="U"></path><path d="M255 459c1 1 1 1 1 3-1 0-2 0-3 1h1c1 1 1 1 2 1-1 0-1 0-1 1 1 1 1 1 0 2v1l1 1v1c1 0 1 0 2 1h1l-2 1-2-1-2 1c-1 0-2 0-3-1-1 0-1 0-2-1h0v-1-4c-1-1-2-1-2-1-1 0-2-1-3-2h1 7c1 0 2 0 4-1v-2z" class="H"></path><path d="M252 466h2 1v1 1l-1 1h-1c0-1 0-1-1-1h0v-2z" class="g"></path><path d="M255 468l1 1v1c1 0 1 0 2 1h1l-2 1-2-1-2 1c-1 0-2 0-3-1 1 0 3-1 4-2l1-1z" class="I"></path><path d="M256 469v1c1 0 1 0 2 1h1l-2 1-2-1v-1l1-1z" class="H"></path><path d="M248 465c0-1 1-1 1-1v2h1l1-1v1 3l-3 1v-1-4z" class="B"></path><path d="M255 459c1 1 1 1 1 3-1 0-2 0-3 1h1c1 1 1 1 2 1-1 0-1 0-1 1 1 1 1 1 0 2v-1h-1-2c-1-1-2-2-3-2 0 0-1 0-1 1-1-1-2-1-2-1-1 0-2-1-3-2h1 7c1 0 2 0 4-1v-2z" class="Z"></path><path d="M217 434v-2h1c1 0 2 1 3 2v1s0 1 1 1h1v-3h3v1h0v102c0-3 1-7 0-10v-1-4-3-1-9c0 1 0 1-1 1v-1s0-1-1-1l-1 1v1c-1 0-1 0-1-1v-2s-1 0-1-1l-1-1c-1-1-2-1-3-1h0c-1 0-1 1-2 1h-2v-1l-3-3v3c-1-4 0-9-1-13 0-2 0-1-1-3v-2l2-1v-2-4c1-3 0-8 0-11v-28c1-2 1-4 1-6h1v3h2l1 1s-1 1 0 1c0-1 0-1-1-3v-1c1 1 2 1 3 0h0z" class="V"></path><path d="M220 469c2-1 4-2 6-2v5l-2-1c-1-1 0-1-1-2l-2 2c-1 0-1-2-1-2z" class="Q"></path><path d="M222 436h1v-3h3v1 10l-1-1h-1v5c-1 0-1 0-1-1-1 0-1-1-1-3v-3-5z" class="P"></path><path d="M216 470c1-1 2-1 4-1 0 0 0 2 1 2l2-2c1 1 0 1 1 2l2 1c0 2 1 8 0 10l-1-1h-1l-1 1-1-1c-2 0-2 1-3 1h-1-2 0v-5-2c-1-2 0-3 0-5z" class="T"></path><path d="M210 439c1-2 1-4 1-6h1v3 2h2c0 1-1 1-1 2v5 15c0 3 0 6-1 9 1 1 1 2 1 2l2 1c1-1 1-1 1-2 0 2-1 3 0 5v2 5c-1-1-2-2-3-2v2c0 1 0 1 1 1l-1 1c-1 2-1 4-1 5h-1c-1-2 0-5-1-7v-4c1-3 0-8 0-11v-28z" class="J"></path><path d="M216 470c0 2-1 3 0 5v2c0 1 0 1-1 2h-1-1v-2-6l2 1c1-1 1-1 1-2z" class="b"></path><path d="M217 434v-2h1c1 0 2 1 3 2v1s0 1 1 1v5 3c0 2 0 3 1 3 0 1 0 1 1 1 0 1 0 2 1 3l1 1v14l-10 3v-15-1c0-1-1-1-1-2v-4-9c0-1 0-1-1-3v-1c1 1 2 1 3 0h0z" class="m"></path><path d="M217 457h2c1 1 1 2 1 3h-1c-1 0-1-1-2-2v-1z" class="P"></path><path d="M217 434v-2h1c1 0 2 1 3 2v1s0 1 1 1v5 3c-1-1-1-1-1-2-1 0-2 0-3 1v5l-1 1 1 2h1c0 1 0 1-1 0h0c-1 0-1 1-1 1h2v1c-1 0-2 0-3 1v-1c0-1-1-1-1-2v-4-9c0-1 0-1-1-3v-1c1 1 2 1 3 0h0z" class="T"></path><path d="M215 438c0-1 0-1-1-3v-1c1 1 2 1 3 0v5 5h0c1 2 0 7-1 9 0-1-1-1-1-2v-4-9z" class="I"></path><path d="M210 482c1 2 0 5 1 7h1c0-1 0-3 1-5l1-1c-1 0-1 0-1-1v-2c1 0 2 1 3 2h0 2 1c1 0 1-1 3-1l1 1 1-1h1v1l-6 3 1 1 6-4v26c0 1 0 1-1 1v-1s0-1-1-1l-1 1v1c-1 0-1 0-1-1v-2s-1 0-1-1l-1-1c-1-1-2-1-3-1h0c-1 0-1 1-2 1h-2v-1l-3-3v3c-1-4 0-9-1-13 0-2 0-1-1-3v-2l2-1v-2z" class="m"></path><path d="M217 487h-1v-3h1c1 0 1 0 2 1l-2 2h0z" class="T"></path><path d="M218 493c1-1 1 0 2 0 0 1 1 2 1 3v1 1h-1 0l-1-1v-3l-1-1z" class="Y"></path><path d="M210 482c1 2 0 5 1 7h1c0-1 0-3 1-5l1-1c-1 0-1 0-1-1v-2c1 0 2 1 3 2h0 0c0 2 0 3-1 4 0 1 0 1 1 2h1v-1h0c1 0 1 1 1 1 1 1 1 2 2 2l-1 1c-1-1-1-2-3-2 0 1 0 1-1 2l1 1h0c-1 2 0 7 0 10h1v1c-1 0-1 1-2 1h-2v-1l-3-3v3c-1-4 0-9-1-13 0-2 0-1-1-3v-2l2-1v-2z" class="V"></path><path d="M210 495c1 1 1 3 2 3h1v5l-3-3v-5z" class="Z"></path><path d="M208 485l2-1v11 5 3c-1-4 0-9-1-13 0-2 0-1-1-3v-2z" class="L"></path><path d="M213 498v-1c-1-2-1-3-1-4l2-1v-2c1 0 0-1 1-2v3l1 1h0c-1 2 0 7 0 10h1v1c-1 0-1 1-2 1h-2v-1-5z" class="g"></path><path d="M213 498h1c0 2 0 3 1 4h2v1c-1 0-1 1-2 1h-2v-1-5z" class="G"></path><path d="M206 406h2 0v1c1 0 2 0 3-1 1 1 2 1 2 2l1-2c1 1 2 1 3 1h0 1v1l1-1v-1h1 1c0 1 0 1-1 2h2 1 0l2 1h1 0c-1 1-2 1-2 1v2l-1 1v1l-1 1h0l2 2 2 1 1 1-1 8v7h0v-1h-3v3h-1c-1 0-1-1-1-1v-1c-1-1-2-2-3-2h-1v2h0c-1 1-2 1-3 0v1c1 2 1 2 1 3-1 0 0-1 0-1l-1-1h-2v-3h-1c0 2 0 4-1 6v28c0 3 1 8 0 11h-1s0 1-1 1v-6c0-1 0-5-1-6v-1h-1v1-1l-3-3c-1 0-1 0-2-1h1v-3c-1 0 0-1 0-2v-3h-1l-2-2c-1 0-1 0-2 1 1 1 1 2 1 2l-2-1c1-2 0-5 0-7v-6l-1-1c0-1 0-1-1-2h0v-1-1l-1-1v-1-4l2 1v-2-5c1-1 0-2 0-3l1-1h0c0-2 1-3 1-4 0-3-1-4 0-7 0 2 0 4 1 5v2h2 0c0-2 1-2 2-3v-2l1-1v1l1-1v-2h1 0v-1s1 0 1-1z" class="k"></path><path d="M199 450l1 1h2c-1 1-1 2-1 3l-2-2v-2z" class="a"></path><path d="M207 438h1v6h0l-1 1c-2 0-2-1-3 0h0v1 1h-1 0v-2-1c1-1 3-1 4-1v-2-3z" class="V"></path><path d="M202 457v-1c0-1 1-2 1-2v-4l1 1v2 1c0 2 1 1 2 2l1-1h1v4 1 6l-1 1v-1h-1v1-1l-3-3c-1 0-1 0-2-1h1v-3c-1 0 0-1 0-2z" class="Z"></path><path d="M206 459l-1 2h-1 0v-3-2h1v1h1v2z" class="G"></path><path d="M206 456l1-1h1v4 1 6l-1 1v-1h-1v1-1l-3-3 1-1 3 3c-1-2 0-4-1-6v-2h-1v-1h1z" class="I"></path><path d="M196 437c1 1 1 1 2 1h1 1c1-1 1-1 2-1v1 6 7h-2l-1-1v2c-1 0-1 0-2 1 1 1 1 2 1 2l-2-1c1-2 0-5 0-7v-6-4z" class="c"></path><path d="M196 437c1 1 1 1 2 1h1l1 1c0 1 0 1-1 1 0 1-1 2-2 2l1 1h0c1 1 0 3 0 3 1 2 0 3 1 4v2c-1 0-1 0-2 1 1 1 1 2 1 2l-2-1c1-2 0-5 0-7v-6-4z" class="Z"></path><path d="M208 423v5h1c1-2-1-4 1-5h1v1c0 2 0 4-1 6h1c0 2-1 6-1 9v28c0 3 1 8 0 11h-1s0 1-1 1v-6c0-1 0-5-1-6l1-1v-6-1c1-2 0-3 0-5v-10h0v-6h-1v3h0-3-1c0-1-1-1 0-2l-1-1v-1-2-1-1-1-4-2h1 1v-2c0-1 0-1 1-1 0 1 0 1 1 2 0-1 1-1 1-2h1z" class="G"></path><path d="M204 437l2 2h0c0-1 1-1 1-1v3h0-3v-4z" class="H"></path><path d="M202 428l2 1v1 7 4h-1c0-1-1-1 0-2l-1-1v-1-2-1-1-1-4z" class="Z"></path><path d="M202 434h1v5l-1-1v-1-2-1z" class="G"></path><path d="M208 438v-7l1-1v1 6c0 3 1 5 0 8-1 5 0 10 0 15h-1 0v-1c1-2 0-3 0-5v-10h0v-6z" class="b"></path><path d="M196 420c0-2 1-3 1-4 0-3-1-4 0-7 0 2 0 4 1 5v2h2 0c0-2 1-2 2-3v-2l1-1v1c1 1 2 3 3 5h0l2 7h-1c0 1-1 1-1 2-1-1-1-1-1-2-1 0-1 0-1 1v2h-1-1v2 4 1 1 1 2c-1 0-1 0-2 1h-1-1c-1 0-1 0-2-1v4l-1-1c0-1 0-1-1-2h0v-1-1l-1-1v-1-4l2 1v-2-5c1-1 0-2 0-3l1-1h0z" class="V"></path><path d="M199 434c2 0 2 0 3-1v1 1l-1 1h-4v-1c1 0 1-1 2-1h0z" class="b"></path><path d="M201 431l1 1v1c-1 1-1 1-3 1 0-1-1-1-1-2 1 0 2-1 3-1z" class="f"></path><path d="M196 420v13-4h0-1v-5c1-1 0-2 0-3l1-1z" class="G"></path><path d="M201 431c0-1-1-1-1-2l-2 1h0v-1-3h1 2l-2-2h3v2 2 4l-1-1z" class="b"></path><path d="M195 429h1 0v4 4 4l-1-1c0-1 0-1-1-2h0v-1-1l-1-1v-1-4l2 1v-2z" class="e"></path><path d="M193 430l2 1c0 2-1 3-1 5l-1-1v-1-4z" class="G"></path><path d="M196 420c0-2 1-3 1-4 0-3-1-4 0-7 0 2 0 4 1 5v2h2v1h0c1 0 1 1 2 1h0v2 1h-2v1h2 0c-1 1-2 1-4 1-1-1-1-1-2-3z" class="W"></path><path d="M203 410v1c1 1 2 3 3 5h0l2 7h-1c0 1-1 1-1 2-1-1-1-1-1-2-1 0-1 0-1 1v2h-1-1v-2-3-1-2h0c-1 0-1-1-2-1h0v-1h0c0-2 1-2 2-3v-2l1-1z" class="M"></path><path d="M200 416c0-2 1-2 2-3 0 1 1 4 0 5h0 0c-1 0-1-1-2-1h0v-1h0z" class="V"></path><path d="M206 416l2 7h-1c0 1-1 1-1 2-1-1-1-1-1-2-1 0-1 0-1 1v2h-1-1v-2-3-1l1 1c1-1 1-1 1-2h0c2 0 1 3 2 4h1v-1-1s0-1-1-1v-4z" class="H"></path><path d="M206 406h2 0v1c1 0 2 0 3-1 1 1 2 1 2 2l1-2c1 1 2 1 3 1h0 1v1l1-1v-1h1 1c0 1 0 1-1 2h2 1 0l2 1h1 0c-1 1-2 1-2 1v2l-1 1v1l-1 1h0l2 2 2 1 1 1-1 8v7h0v-1h-3v3h-1c-1 0-1-1-1-1v-1c-1-1-2-2-3-2h-1v2h0c-1 1-2 1-3 0v1c1 2 1 2 1 3-1 0 0-1 0-1l-1-1h-2v-3h-1c0 2 0 4-1 6 0-3 1-7 1-9h-1c1-2 1-4 1-6v-1h-1c-2 1 0 3-1 5h-1v-5l-2-7h0c-1-2-2-4-3-5l1-1v-2h1 0v-1s1 0 1-1z" class="c"></path><path d="M214 406c1 1 2 1 3 1h0 1v1 3l-5-3 1-2z" class="R"></path><path d="M217 418l-1 1 1 1c0 1-1 2-1 2h-1v-2h0v-1h-1c-1 0-2 0-3-1h2c1 0 1 0 1-1h3l1 1h-1z" class="f"></path><path d="M217 417v-3h1l1 1c1 0 2 0 3-1v1h0l-1 2h-1c-1 1 0 1-2 2h-1v-1h1l-1-1z" class="a"></path><path d="M220 406h1c0 1 0 1-1 2h2 1 0l2 1h1 0c-1 1-2 1-2 1v2l-1 1v1l-1 1v-1c-1 0-1-1-1-1l-2-2h-1v-3l1-1v-1h1z" class="M"></path><path d="M219 411c1 0 2 0 4-1v1l1 1-1 1v1l-1 1v-1c-1 0-1-1-1-1l-2-2z" class="C"></path><path d="M212 433v-3h0v-1-5h3l-1 1v2c2 1 2 2 3 3l-1 2 1 2h0c-1 1-2 1-3 0v1c1 2 1 2 1 3-1 0 0-1 0-1l-1-1h-2v-3z" class="l"></path><path d="M214 427c2 1 2 2 3 3l-1 2-1 1h-1v-3-3z" class="M"></path><defs><linearGradient id="d" x1="208.179" y1="416.373" x2="208.225" y2="406.48" xlink:href="#B"><stop offset="0" stop-color="#acabac"></stop><stop offset="1" stop-color="#cdcccc"></stop></linearGradient></defs><path fill="url(#d)" d="M206 406h2 0v1l1 2c1 0 1-1 2-1h1v3c1 2 1 4 1 6-1 0-2 0-3-1l-1-2c-1 0-1 0-2 1l-1 1c-1-2-2-4-3-5l1-1v-2h1 0v-1s1 0 1-1z"></path><path d="M217 425h0c0-2 1-3 2-4l1-1c0-1 0-2 1-3 1 1 1 0 2 0v2h1l2-1 1 1-1 8v-4c-1 0-1 1-1 1-1 0-1-1-2 0l-1 2v2h-2-3v-1-1-1z" class="f"></path><g class="J"><path d="M217 425c1-1 2-2 4-2h0v1c-1 1-1 2-1 3-1 1-2 1-3 1v-1-1-1z"></path><path d="M222 428v-2l1-2c1-1 1 0 2 0 0 0 0-1 1-1v4 7h0v-1h-3v3h-1c-1 0-1-1-1-1v-1c-1-1-2-2-3-2h-1v2l-1-2 1-2c-1-1-1-2-3-3v-2l1-1c1 1 1 2 2 3v1h3 2z"></path></g><path d="M215 424c1 1 1 2 2 3v1h3 2c0 1 1 1 1 2h-2v-1h-1v1h-3c-1-1-1-2-3-3v-2l1-1z" class="G"></path><path d="M372 148l12-1c1 0 4-1 5 0l4 4c1 1 2 2 2 3l2 2c1 0 1 1 2 2h5 3 0l1 2c0 1 1 2 1 3l1 1v2l1 1 1 5c2 4 3 6 6 9 0 2 0 1-1 3l1 2c0 1-1 1-1 2-1 0-1 0-1 1 0 0-1 0-1 1l-1 1c-1 1-1 0-3 1l2 2-3 2-6 6h1l1-1c1 1 3 1 4 1-1 2-3 3-4 4l-1 1c-1 1-2 2-3 2h0l-1 1c-2 2-4 3-6 4l-1-1-1 1h0v-1c1 0 1-1 2-1l-1-1c-2 2-5 4-7 6h1 0-2l-1 1-1-1h0c-1 1-1 1-2 1-1 1-3 1-4 1-1 1-2 1-2 1l-1-1c-10-7-19-15-30-21-3-2-6-3-8-4s-5-1-7-1h-16-60l1 42v5h0c-1 1-1 2-1 3h-1l-1 1c-2 0-2 0-3 1 0 1 2 2 1 3v1 2l1 1h0v2c1 1 2 2 2 3h0v1h-4-4-6-5-3 0-4c-1-1 0-5 0-6v-1h-1l1-8c0-1 0-3-1-5s0-4 0-6v-3-2c0-2 1-7 0-8-1 0-1 0-2-1v-2c0-1-1-1-1-2-1-1-1-2-2-2v-1-1s-1 0-2-1v-1c0-1 1-1 1-2v-1-1l-2-1 1-1v-1h1v-2l1-1c2 1 3 1 4 2h1v-2-5-18c1-3 1-5 0-8l1-5c-1-3-2-3-4-5h0c0-1-1-1-1-2-1 0-1 0-1-1l1-1h0 9c2 0 6-1 8 0h2v-1l2 1h9l-1-2-1-1h2l1 1c1 1 2 1 3 2 2 0 6 2 8 4 3 1 6 1 9 1 2 1 4 0 6 0h4c2-3 3-5 4-8 1-1 3-1 4-2h3v-1h16 3l1 1h1 0c1 1 1 1 2 1s1-1 2-2h0c1 1 1 1 2 0h0c6 1 12 1 18 1h10c3-1 7-1 11-1h0c3 1 6 0 8 0h2l2 1z" class="n"></path><path d="M253 169h1l-1 1c1 1 1 1 1 2h-2c0-2 0-2 1-3z" class="j"></path><path d="M340 177c1-1 1 0 2 0h1l-1 1-1 1c-1-1-1-1-1-2zm-90-17h0c2 1 3 2 3 4-1-1-3-2-4-3l1-1z" class="O"></path><path d="M381 188l-1 1h-1l-2-2-1 1v-1l1-1h2 0l2 2zm5 3c1 1 2 1 3 2-1 1-1 1-2 1-1-1-2-1-3-3h2z" class="B"></path><path d="M392 203c1 1 1 1 1 2v2l-1 1-1-1v-2l1-2z" class="E"></path><path d="M268 174l1 5v1l-1-1c-1 0-1-1-1-1v-3l1-1z" class="O"></path><path d="M380 178h0c0 1 1 1 2 2l-1 1c-1 1-3 0-5 0l4-3z" class="R"></path><path d="M262 158h2c0 3 0 5-1 8v3h-1 0c1-3 0-8 0-11z" class="C"></path><path d="M264 172l-1 1c-1 0 0 3 0 4-2 0-2-1-3-1h-1-2l2-2 2 1v-3h1 2z" class="U"></path><path d="M360 186h3s1 0 1 1c-2 0-4 1-6 1s-2 0-3-1c1-1 3 0 5-1h0z" class="C"></path><path d="M392 208h0l-1 1h-1c-1-1-1-1-2-1l-1-1c1-1 2-1 3-2l-1-1 1-1v-1h0c1 1 1 1 1 2v1h0v2l1 1z" class="B"></path><path d="M262 172c2-1 4-2 5-3 0-1 1-1 2-2h0v1c-1 2-1 4-1 6l-1 1c0-1 0-3-1-4-1 0-1 1-1 1h-1-2z" class="E"></path><path d="M253 175h0c1-1 4-5 5-6 0-3 1-5 1-7 1 1 1 3 1 4l-2 3c-1 3-1 5-4 7h-1v-1z" class="j"></path><path d="M253 176h1c0 2 1 2 2 3l2 1-3 3-3-3h0v-1c0-1 1-2 1-3z" class="i"></path><path d="M253 176h1c0 2 1 2 2 3-1 1-3 0-4 1h0v-1c0-1 1-2 1-3z" class="E"></path><path d="M242 241h1c1 1 1 1 1 2v1l-3-1v2 1h-1c-1 0-2 1-2 1l-1 1h-1-1c1-3 5-5 7-7z" class="C"></path><path d="M226 200v-2l1 18c0 2-1 4 0 7 0 1-1 3 0 4h-1c0-2 1-7 0-8-1 0-1 0-2-1v-2c0-1-1-1-1-2-1-1-1-2-2-2v-1-1c1 2 3 3 4 5h1 0v-9-6z" class="Y"></path><path d="M408 160c0 1 1 2 1 3-2 0-3 1-5 1-1 0-1 1-2 1s-2 0-2 1l-1 1c-1 0-1-1-1-2 1-1 4-3 6-4 1 0 2 1 3 0 0 0 1 0 1-1z" class="h"></path><path d="M378 176l1 1c-2 2-6 4-7 7h-2l-1 2h0 0c-1 1-3 2-4 3s-2 2-3 2h-1c2-2 5-3 6-5-1 0-2 0-3 1 0-1-1-1-1-1 2-1 5-2 6-3s3-1 4-2 4-3 5-5z" class="K"></path><path d="M250 251l1 1h0v2c1 1 2 2 2 3h0v1h-4-4-6-5-3c4-1 8 0 12-1v-1l-1-1 1-1h0c1 0 3 1 3 2h1c1 0 1 0 3-1 0 1 1 1 1 2h1c0-2-1-1-1-3-1 0-1-3-1-3z" class="E"></path><path d="M360 186h0c1-2 3-2 5-3l7-5c2 0 3-2 4-2h2-1 1c-1 2-4 4-5 5s-3 1-4 2-4 2-6 3h-3z" class="O"></path><path d="M351 160c-3 3-6 5-8 8-4 4-7 8-11 12h-1l3-4 6-7c1-1 2-2 2-3-1 0-1 1-2 1h-2v-1l3-3h0c1 0 2 1 2 1v1c1-1 1-1 2-1l2-3c2-1 2-1 4-1z" class="C"></path><path d="M227 223v2 1c1 0 2 2 2 3s1 2 0 3v1l2-2h1l1 1-1 1c-1 1-3 4-4 5l-1 1 1 1c-1 1-1 2-1 3 0-1 0-3-1-5s0-4 0-6v-3-2h1c-1-1 0-3 0-4z" class="U"></path><path d="M226 227h1c0 1-1 2 0 3h1c1 1 1 2 1 3h-1-1c-1 1-1 4-1 5-1-2 0-4 0-6v-3-2z" class="i"></path><path d="M228 233v1h1c1-1 2-1 3-2h0v1c-1 1-3 4-4 5l-1 1 1 1c-1 1-1 2-1 3 0-1 0-3-1-5 0-1 0-4 1-5h1z" class="E"></path><path d="M361 162l1 1h1c1-1 1-1 2-1l1-1c0-1 1-1 2-1h0c0 1-1 1-2 2l-17 11v-1c0-1 2-1 3-2 0-1 0-2 1-2l2-2c2-1 4-4 6-4z" class="O"></path><path d="M369 186c1 0 2 0 2-1h2v1h1l-1-1h1 0c0 2-1 1-1 3h1v1h2c0 1 0 1 1 2l1 1h1c1 1 2 2 2 3l-1 2h0c1 0 1-1 2-1l2 1c-1 1-2 3-3 3l-1 1 1 1-1 1c0-1 0-2-1-2-1 1-1 1-3 2h0l1-1 1-1h-1c-1-2-1-3-2-5-1-1-1-2-1-3h0c1 0 2 1 2 1 0 2 2 3 2 4h1v-1s-1 0-1-1h0c0-1-1-2-1-3v-1h0l-2-2h-2l1-1h-1s-1-1-1-2 1 0 0-1h0c-1 0-2 1-3 0h0 0z" class="E"></path><path d="M280 168c2 0 2 2 4 3 1 1 1 3 2 4h1 0l-1 2v1h2v2 1c-1-1-3-3-4-2-1 0-1 1-2 1h0c-2-3-3-6-2-9v-1-2z" class="U"></path><path d="M281 171c1 0 1 0 1 1l1 1-1 1c0-1-1-1-1-1l-1-1 1-1z" class="B"></path><path d="M221 198c2 1 3 1 4 2h1v6 9h0-1c-1-2-3-3-4-5 0 0-1 0-2-1v-1c0-1 1-1 1-2v-1-1l-2-1 1-1v-1h1v-2l1-1z" class="P"></path><path d="M242 238c-5-5-10-9-13-14 2 1 3 3 5 4l2-2c0-1 1-1 2-1 0-1 1 0 1 0v-1l1 1c3 1 5 4 7 6h1l-1 1c-1 1-2 2-2 3h-1l1 1c-1 0-1 0-2 1 1 2 2 3 4 4h-1 0c-1-1-3-2-4-3z" class="B"></path><path d="M243 231h4 1l-1 1h-2c-1 1-1 2-2 3-1 0-1 0-1-1h0l2-2s-1 0-1-1h0z" class="i"></path><path d="M236 231l-1-1c1-1 2-2 3-2l1 1h2c2 1 1 1 3 1l-1 1h0c0 1 1 1 1 1l-2 2h0c-2 1-2 0-3 0-1-1-2-3-3-3z" class="E"></path><path d="M239 234c1-1 1-2 2-2h1v2h0c-2 1-2 0-3 0z" class="U"></path><path d="M236 231l-1-1c1-1 2-2 3-2l1 1c1 0 1 0 2 1l-1 1h-1c-1 0-2-1-3 0z" class="O"></path><path d="M248 231l1 2c1 1 3 2 3 3h2l1-1v5h0c-1 1-1 2-1 3h-1l-1 1c-2 0-2 0-3 1 0-1-1-1-1-1h-1c-1 0-2 1-2 2s-1 1-2 2c1 1 1 1 2 1h0v2h1l-1-1 2-2v1c0 2-1 2-2 3l-1 1c-1-2-2-3-2-4l-1-2c1-1 2-1 3-1v-1h1c0-1 1-1 1-1s0-1 1-1c-1-1-3-2-3-3l-2-2c1 1 3 2 4 3h0 1c-2-1-3-2-4-4 1-1 1-1 2-1l-1-1h1c0-1 1-2 2-3l1-1z" class="E"></path><path d="M248 231l1 2c1 1 3 2 3 3h2c-1 2-1 3-1 4-1 1-2 2-3 2v-2h0c-1-1-3-1-4-2 0-1 0-1-1-2h0l-1-1h1c0-1 1-2 2-3l1-1z" class="R"></path><path d="M252 236h2c-1 2-1 3-1 4h-1s-1-1-1-2 1-1 1-2zm-4-5l1 2c0 1 0 1-1 2h-3 0c0-1 1-2 2-3l1-1z" class="h"></path><path d="M360 147c-1 4-4 7-7 11h11c-1 1-1 2-1 3 0 0-1 1-2 1-2 0-4 3-6 4l-2 2c-1 0-1 1-1 2h-4c-1-1-1-1-1-2v-1h0v-1c0-1 3-3 4-4s1-2 1-3l-13-1h0 4 9c3-3 6-7 8-11h0z" class="B"></path><path d="M347 167h1c0-1 0-1 1-1h1 5 0l-2 2c-1 0-1 1-1 2h-4c-1-1-1-1-1-2v-1h0z" class="C"></path><path d="M347 167h3v1h-3v-1z" class="O"></path><path d="M360 147c3 1 6 0 8 0v3c2 2 3 5 3 7v1h-7-11c3-4 6-7 7-11z" class="m"></path><path d="M339 148h10c3-1 7-1 11-1-2 4-5 8-8 11h-9c-1-1-2-2-3-4s0-3-2-4v-1c0-1 0-1 1-1z" class="Q"></path><path d="M395 201l1-1c1 2 3 3 5 5l1-1c1-1 1-2 2-2h1l1-1c1 1 3 1 4 1-1 2-3 3-4 4l-1 1c-1 1-2 2-3 2h0l-1 1c-2 2-4 3-6 4l-1-1-1 1h0v-1c1 0 1-1 2-1l-1-1c-2 2-5 4-7 6h1 0-2l-1 1-1-1h0c-1 1-1 1-2 1-1 1-3 1-4 1-1 1-2 1-2 1l-1-1c3-2 7-1 10-3v-1c-1-1-2-1-2-2l-1-2c1-1 2-1 3-2h0c0-1 1-1 1-2h0 0v1 1h2 0-1 0l-1 1h0 2c1 1 1 2 2 3h0l1-1v-1s1 0 1-1c1 0 1 0 1-1h0v-2-2c0-1 0-1-1-2h1 1l1-1v-1z" class="C"></path><path d="M402 205l1 1c1 0 1 1 2 1-1 1-2 2-3 2s-2 0-3-1l3-3z" class="R"></path><path d="M394 213h0l5-5c1 1 2 1 3 1h0l-1 1c-2 2-4 3-6 4l-1-1z" class="K"></path><path d="M406 201c1 1 3 1 4 1-1 2-3 3-4 4l-1 1c-1 0-1-1-2-1l-1-1 3-3 1-1z" class="h"></path><path d="M395 201l1-1c1 2 3 3 5 5h0c-2 1-2 2-3 3-1 0-2 1-3 2s-2 2-4 2v-1s1 0 1-1c1 0 1 0 1-1h0v-2-2c0-1 0-1-1-2h1 1l1-1v-1z" class="R"></path><path d="M395 202h1v1h-2 0l1-1zm1 3h1 1l-2 2h-1v-1l1-1z" class="B"></path><defs><linearGradient id="e" x1="381.47" y1="177.171" x2="401.74" y2="172.249" xlink:href="#B"><stop offset="0" stop-color="#2e2d2e"></stop><stop offset="1" stop-color="#474647"></stop></linearGradient></defs><path fill="url(#e)" d="M398 165c0 1 0 2 1 2l1-1 1 2h0c0 1 0 1 1 2h-1c1 0 1 0 2 1s0 2 1 2c0 1 0 2-1 3h1-2v1l-1-1h0c-2-1-3-1-4-1s-1 1-2 1h0-2l-1 1v1c-1 0-2 1-2 2h-2 0c-1 0-2 1-2 1l-2-1c0 1-1 1-1 2h0v-2c-1 0-1-1-1-2h-1-1 0c4-4 9-7 14-10 0-1 3-2 4-3z"></path><path d="M399 171c1 0 2 1 2 1v2l-1-1c-1-1-1-1-1-2z" class="R"></path><path d="M385 175h0c1 1 1 1 1 2l-1 1h-1c0-1 0-2 1-3z" class="B"></path><path d="M398 165c0 1 0 2 1 2l1-1 1 2h0c0 1 0 1 1 2h-1c1 0 1 0 2 1s0 2 1 2c0 1 0 2-1 3 0-1-1-2-1-3l-1-1s-1-1-2-1c-2 0-4 2-6 1v-1c0-1-1-1-1-1l2-2c0-1 3-2 4-3z" class="N"></path><path d="M250 150h2l1 1c1 1 2 1 3 2 2 0 6 2 8 4v1h-2-17-13c-2 0-4 1-6 0-1 0-2-1-3-1 0-1-1-1-1-2-1 0-1 0-1-1l1-1h0 9c2 0 6-1 8 0h2v-1l2 1h9l-1-2-1-1z" class="c"></path><path d="M222 153v1h6c3-1 7 0 10 0 0 1 0 3-1 4-3 1-7-1-11 0-1 0-2-1-3-1 0-1-1-1-1-2-1 0-1 0-1-1l1-1z" class="Q"></path><path d="M250 150h2l1 1c1 1 2 1 3 2 2 0 6 2 8 4v1h-2-17 0-6v-4c2 1 4 0 6 0 0 0 1 1 1 0 4 0 7 1 10 2v-1c0-1-4-3-5-4l-1-1z" class="X"></path><path d="M250 150h2l1 1c1 1 2 1 3 2 2 0 6 2 8 4v1h-2-17 0 9c2 0 5 0 7-1l-5-1v-1c0-1-4-3-5-4l-1-1z" class="M"></path><path d="M255 183c-1 1-2 2-2 3 0 2 0 4 1 6-6-7-13-12-18-20 1-1 2-5 1-7v-1l1-1h0v-1l4 4c4 3 7 7 11 9v1c0 1-1 2-1 3v1h0l3 3z" class="K"></path><path d="M252 180h-1l-1-1 2-2-2-2-6-6h-1c0 1 0 1-1 2-1-1 0-3 0-4v-1c4 3 7 7 11 9v1c0 1-1 2-1 3v1z" class="R"></path><path d="M315 148c1 1 1 1 2 1s1-1 2-2h0c1 1 1 1 2 0h0c6 1 12 1 18 1-1 0-1 0-1 1v1c2 1 1 2 2 4s2 3 3 4h-4-16-9 5v-1c0-1-1-2-2-2l-3-1c0-1 1-1 1-2-1 0-3 0-4-1 1 0 1 0 2-1s1-1 1-2h1 0z" class="J"></path><path d="M314 148h1 0c0 1 1 2 1 3l-1 1c-1 0-3 0-4-1 1 0 1 0 2-1s1-1 1-2z" class="T"></path><path d="M372 148l12-1c1 0 4-1 5 0l4 4c1 1 2 2 2 3l2 2c1 0 1 1 2 2h5c-3 1-7 0-10 0h-21l-1-1h-1c0-2-1-5-3-7v-3h2l2 1z" class="Y"></path><path d="M382 152c1 0 1 0 2 1h0-1-2v-1h1zm-12-5l2 1h0c1 0 3-1 4 0-1 1-1 1-2 1v1c1 0 2 1 4 0l1-1 1 1c0 1-1 1-1 1-2 1-6 3-7 4v1 1h-1c0-2-1-5-3-7v-3h2z" class="J"></path><path d="M370 147l2 1h0v1l1 1v1c-1 1-2 2-3 2v-6z" class="Q"></path><path d="M310 147h3l1 1c0 1 0 1-1 2s-1 1-2 1c1 1 3 1 4 1 0 1-1 1-1 2l3 1c1 0 2 1 2 2v1h-5-2 0-16-13c2-3 3-5 4-8 1-1 3-1 4-2h3v-1h16z" class="T"></path><path d="M294 148h4 0c-1 1-2 1-3 2h0c-1-1-2-1-4-2h3z" class="m"></path><path d="M308 151h3 0c1 1 3 1 4 1 0 1-1 1-1 2-1 0-2 0-3-1h-2l-2-2h1 0z" class="P"></path><path d="M308 151h3l-1 1c-1 0-1 0-2-1h0z" class="J"></path><path d="M310 147h3l1 1c0 1 0 1-1 2s-1 1-2 1h0-3c-1 0-2-1-3-1h0l-1-1h3 1c1 0 2 0 3-1h0l-1-1z" class="Q"></path><defs><linearGradient id="f" x1="381.638" y1="189.819" x2="409.667" y2="186.362" xlink:href="#B"><stop offset="0" stop-color="#282828"></stop><stop offset="1" stop-color="#515051"></stop></linearGradient></defs><path fill="url(#f)" d="M409 163l1 1v2l1 1 1 5c2 4 3 6 6 9 0 2 0 1-1 3l1 2c0 1-1 1-1 2-1 0-1 0-1 1 0 0-1 0-1 1l-1 1c-1 1-1 0-3 1l2 2-3 2-6 6c-1 0-1 1-2 2l-1 1c-2-2-4-3-5-5l-1 1v1l-1 1h-1l-2-2-1 1c-1 1-2 1-3 2l-1-1c1 0 2-1 3-1v-1c-2 1-4 2-5 3h-1c-1 0-1 1-2 0v-1c0 1 1 1 1 1 2-1 4-2 5-3h-2c-1 0-1 0-1-1 1-1 2-1 3-2h0c-1 0-2-1-3-1v-1h1 0c1-1 1 0 2-1 1 0 1-1 2-1h0v-1l-1-1-2-1h-2c-1 0-2 0-2 1v1h1l1 1h-2c-1-1-1-1-1-2l1-1c1 0 3 0 3-1v-1c-1-1-2 1-4 1 0 1-1 1-2 1h0 0v-1h1 1c0-1 1-1 2-1l-1-1h-1l-2-2c1 0 2 1 3 1 0-1 1-1 1-1h1 1c1 1 2 1 3 1h0v-1c-1 0-2 0-3-1h0c-1 0-2 1-4 1h0c0-1-1-2-1-2h0c0-1 0-1-1-1 2 0 2-1 4-1h0c0-1 1-1 1-2l2 1s1-1 2-1h0 2c0-1 1-2 2-2v-1l1-1h2 0c1 0 1-1 2-1s2 0 4 1h0l1 1v-1h2-1c1-1 1-2 1-3-1 0 0-1-1-2s-1-1-2-1h1c-1-1-1-1-1-2h0l-1-2c0-1 1-1 2-1s1-1 2-1c2 0 3-1 5-1z"></path><path d="M402 188c1-1 1-1 2-1l1 1c-1 0-2 1-2 1l-1-1z" class="L"></path><path d="M400 190v-2-1h0 1l1 1 1 1c-1 1-2 1-3 1z" class="N"></path><path d="M402 177v-1h2v4h-1c0-1-1-2-1-3zm-10 0l1-1h2 0c1 0 1-1 2-1s2 0 4 1h0c-1 1-3 1-3 2l-1 1-1-1s0-1-1-1l-1 1h-1l1-1h-2z" class="R"></path><path d="M383 182c0-1 1-1 1-2l2 1s1-1 2-1h0c0 1-1 2 0 2l3 3s0 1-1 1c-2-1-4-4-7-4zm12 19l-1 1-1-1c-1-1-1-1-1-2l1-1v-1l-1-1h0l1-1c1-1 1-1 2-1h0c1 0 2-1 3-1 0 0 0 1-1 2v1l-1 1-1 1c0 1 0 2 1 2l-1 1z" class="D"></path><path d="M409 163l1 1v2l1 1 1 5c2 4 3 6 6 9 0 2 0 1-1 3l1 2c0 1-1 1-1 2-1 0-1 0-1 1 0 0-1 0-1 1l-1 1c-1 1-1 0-3 1l2 2-3 2-6 6c-1 0-1 1-2 2l-1 1c-2-2-4-3-5-5-1 0-1-1-1-2l1-1 1-1v-1c1-1 1-2 1-2v-1c0-1 1-1 2-2 1 0 2 0 3-1 0 0 1-1 2-1 1-1 2-1 3-3 0 0 0-1-1-2h0l1-1 1 1s0-1 1-2h-1-2l-1-1c2-1 2 0 3-1v-1h-2v-1c-1-1-1-1-1-2s0-1 1-2l-1-1-2 1c-1 0 0-1-1-2s-1-1-2-1h1c-1-1-1-1-1-2h0l-1-2c0-1 1-1 2-1s1-1 2-1c2 0 3-1 5-1z" class="I"></path><path d="M401 200s-1-1-2-1c1-1 1-1 1-2 1 1 1 1 2 1v2h-1z" class="H"></path><path d="M402 198s1-1 2-1l1 1h-2v1h0v3c-1 0-1 0-2-1v-1h1v-2zm7-28h1v1 2l-1 2h-2v-1h1v-3-1h1z" class="e"></path><path d="M401 201c1 1 1 1 2 1v-3h0v2l1 1c-1 0-1 1-2 2-1 0-1-1-2-1s0-1-1-2h2z" class="H"></path><path d="M396 200c-1 0-1-1-1-2l1-1 1 1-1 1 1 1h0 3l-1 1c1 1 0 2 1 2s1 1 2 1l-1 1c-2-2-4-3-5-5z" class="N"></path><path d="M411 167l1 5c0 1 0 2-1 3v-1l-1 1h-1l1-2v-2-1h-1c0-1-1-1-1-1 0-1 1-2 3-2z" class="H"></path><path d="M410 164v2l1 1c-2 0-3 1-3 2-2 0-3-1-4-2l-1 1c1 1 1 1 1 2h-2v-3-1h0c1 0 3-1 4-1h3s1 0 1-1zm2 8c2 4 3 6 6 9 0 2 0 1-1 3l1 2c0 1-1 1-1 2-1 0-1 0-1 1 0 0-1 0-1 1h-1c1 0 1-1 1-1v-1c-1 0-1-1-1-2h2 0c1-2 0-2 0-2l1-1v-2l-1-1h-1-2c0-1-1-1-2-2l1-1c-1-1-1-2-1-2 1-1 1-2 1-3zm-6 21v-1h1v1l3 1c0 1-1 2 0 2l-6 6-1-1v-2-1h2 0v-1h0c-2 0-2-1-3-2l1-1s1 0 2 1h0l1-1v-1z" class="G"></path><path d="M406 193l-1-1v-1l3-4h4c0 1 1 2 2 2l1-1v1s0 1-1 1h1l-1 1c-1 1-1 0-3 1l2 2-3 2c-1 0 0-1 0-2l-3-1v-1h-1v1z" class="H"></path><path d="M407 192c1 0 1 0 1-1h1 1c1 1 2-1 4-1h1l-1 1c-1 1-1 0-3 1l2 2-3 2c-1 0 0-1 0-2l-3-1v-1z" class="S"></path></svg>