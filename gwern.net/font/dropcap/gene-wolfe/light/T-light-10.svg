<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="167 134 729 736"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#b0aead}.C{fill:#a9a8a7}.D{fill:#bbbab9}.E{fill:#393836}.F{fill:#9f9e9c}.G{fill:#2d2c2b}.H{fill:#b6b4b3}.I{fill:#5c5b59}.J{fill:#1a1918}.K{fill:#403f3e}.L{fill:#6b6a68}.M{fill:#908f8d}.N{fill:#cecccb}.O{fill:#c1bfbf}.P{fill:#343331}.Q{fill:#575654}.R{fill:#868483}.S{fill:#81807e}.T{fill:#a4a2a1}.U{fill:#969492}.V{fill:#787776}.W{fill:#c8c7c6}.X{fill:#262624}.Y{fill:#4f4e4c}.Z{fill:#716f6e}.a{fill:#c5c3c2}.b{fill:#454442}.c{fill:#232220}.d{fill:#4b4948}.e{fill:#dcdbda}.f{fill:#8b8a88}.g{fill:#d6d4d3}.h{fill:#555352}.i{fill:#9b9998}.j{fill:#30302e}.k{fill:#61605f}.l{fill:#676564}.m{fill:#7d7c7a}.n{fill:#494846}.o{fill:#e7e6e6}.p{fill:#3c3c39}.q{fill:#989794}.r{fill:#636360}.s{fill:#0e0e0d}.t{fill:#747371}.u{fill:#1e1e1c}.v{fill:#fefefd}.w{fill:#91908c}.x{fill:#42423f}.y{fill:#020202}</style><path d="M796 431l1 1-1 7-1-2v-1 1h-1c1-2 1-5 2-6z" class="s"></path><path d="M200 248v3c-3 7-2 15-2 23 0 4 0 9 1 13l-2 1c-3-12-3-29 3-40z" class="y"></path><path d="M199 287c2 12 6 22 13 32 3 5 6 9 12 10l2 1v1c-6 0-12 0-18-1l1-1 9 1c-11-11-19-27-21-42l2-1z" class="s"></path><path d="M187 347l-17-38 14 11c8 5 16 7 25 9l-1 1c-11-1-21-6-30-13-1-1-1-1-2-1h-1c0 3 2 6 3 9l7 15c1 1 2 4 2 5v2h0z" class="y"></path><path d="M794 437h1v-1 1l1 2c-5 22-15 44-31 61h0l-1-1c3-4 7-8 9-12 5-6 8-12 11-19 5-10 6-21 10-31z" class="J"></path><path d="M778 473c2-2 4-5 5-7l1-1v3c-3 7-6 13-11 19-2 4-6 8-9 12l-1 1h-1c-1 1-2 2-2 3h-1c0-1 1-2 1-3 1-1 2-2 2-4v-1c5-5 10-11 13-18l3-4z" class="B"></path><path d="M751 423c1 6 2 13 1 18v1l1 4c1 0 1 1 2 1h0v1l1 1c-1 1-2 2-2 3l1 3h0v1c-1 0-1 0-2 1l-2 2-3 15h0c0-1 0-1-1-2s0-4 0-5l2-14c1-9 1-18 1-26l1-1v-3z" class="c"></path><path d="M751 459l1-17 1 4c1 0 1 1 2 1h0v1l1 1c-1 1-2 2-2 3l1 3h0v1c-1 0-1 0-2 1l-2 2z" class="V"></path><defs><linearGradient id="A" x1="768.244" y1="491.88" x2="765.514" y2="477.875" xlink:href="#B"><stop offset="0" stop-color="#282827"></stop><stop offset="1" stop-color="#494746"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M776 470c1 0 2-1 3-1l-1 4-3 4c-3 7-8 13-13 18 0-1-1-2-2-3 2-2 3-4 5-5h1v-2-1h3-1l-1-2h-2v-1-1c-1 0-1-1-1-2v-1c1 1 1 1 2 1l-1-1 1-1 2-1 1-2 1-2 1 2 2-1h1l2-2z"></path><path d="M776 470c1 0 2-1 3-1l-1 4-3 4h-1c-1 0 0 0-1-1l-2 1v1c-1 0 0 0-1-1l-1 1h1c-1 1-1 1-2 1l-1-1 1-1h-3l1-1 2-1 1-2 1-2 1 2 2-1h1l2-2z" class="K"></path><path d="M770 471l1 2 2-1h1c-2 3-3 4-6 5h-3l1-1 2-1 1-2 1-2z" class="Y"></path><path d="M187 345l15 30c1 2 2 5 4 7 7 14 19 28 33 37-1 1-1 1-1 2l-5-3h0l12 32-1 1c-4-7-7-15-10-23-1-4-2-10-5-14-1-2-4-4-5-6l-9-9c-12-15-22-34-28-52h0v-2z" class="J"></path><path d="M836 247c7 14 9 31 4 46-2 5-4 10-7 14-3 3-6 6-8 10-3 4-5 9-7 14 4 0 8 0 11-1v1c-4 1-10 2-14 1l-1-1 4-8 1-1c0-2 1-2 1-4 2-2 3-3 3-5 7-7 12-14 15-23 5-13 3-28-3-41 1-1 1-2 1-2z" class="s"></path><path d="M200 251h0c1 3-1 6-1 9 0 8 4 17 9 23 1 1 1 3 3 4v1l-2 2c-1-1-1-1-1-2-1-1-2-3-3-3h0l-1-1c-2 4 2 13 4 17 1 3 2 6 4 9 0 1 2 3 2 4 0 2 1 4 0 5h-2c-7-10-11-20-13-32-1-4-1-9-1-13 0-8-1-16 2-23z" class="e"></path><path d="M187 345c0-1-1-4-2-5l-7-15c-1-3-3-6-3-9h1c3 7 9 11 12 18l4 7 10 21c0 2 3 9 5 10l1 3v1-1c-2 1-5 0-6 0l-15-30z" class="v"></path><defs><linearGradient id="C" x1="777.944" y1="457.179" x2="799.751" y2="434.563" xlink:href="#B"><stop offset="0" stop-color="#8d8c8a"></stop><stop offset="1" stop-color="#a7a6a6"></stop></linearGradient></defs><path fill="url(#C)" d="M794 429h2v2c-1 1-1 4-2 6-4 10-5 21-10 31v-3l-1 1c-1 2-3 5-5 7l1-4c-1 0-2 1-3 1l-2 2h-1v-3h1l-1-3h1l-1-2c0-2 2-3 3-4 0-2 1-2 0-3 1-2 2-3 3-5h1c1 0 1-1 2-2l-2-2c1-1 2-1 2-2h2v-2c1 0 2 0 3-1h-1-1v-1c0-1 1-2 1-3-1-1 0-4 0-6v2h1l1 1 1-1v-2 1 2h1l1-6c2 0 2 0 3-1z"></path><path d="M789 433v1 2h1c-1 6-3 13-5 18 0 2 0 3-1 4 0-2 0-4-1-6 2-1 0-4 2-5l1-1h-1l-1-2c1 0 2 0 3-1h-1-1v-1c0-1 1-2 1-3-1-1 0-4 0-6v2h1l1 1 1-1v-2z" class="E"></path><path d="M780 452c1 0 1-1 2-2l-2-2c1-1 2-1 2-2h2v-2l1 2h1l-1 1c-2 1 0 4-2 5 1 2 1 4 1 6-2 2-3 5-3 7l-2 4c-1 0-2 1-3 1l-2 2h-1v-3h1l-1-3h1l-1-2c0-2 2-3 3-4 0-2 1-2 0-3 1-2 2-3 3-5h1z" class="k"></path><path d="M776 470c0-1 0-2 1-3 0-1 0-1 1-2h3l-2 4c-1 0-2 1-3 1z" class="E"></path><path d="M779 452h1l-1 2h1l-1 1c-2 3-1 4-2 6-1 0-1 0-1-1 0-2 1-2 0-3 1-2 2-3 3-5z" class="L"></path><path d="M783 452c1 2 1 4 1 6-2 2-3 5-3 7h-3v-2l5-11z" class="G"></path><defs><linearGradient id="D" x1="814.073" y1="261.45" x2="827.042" y2="283.077" xlink:href="#B"><stop offset="0" stop-color="#1e1b1a"></stop><stop offset="1" stop-color="#383837"></stop></linearGradient></defs><path fill="url(#D)" d="M794 264c1 4 2 9 4 13 1 3 4 6 8 7 3 1 7 0 10-2 10-6 14-17 17-27 0-3 0-8 1-11 1 1 1 2 2 3 0 0 0 1-1 2 0 6 1 13 0 19-1 3-1 6-3 8h0l-1 1c-5 8-9 12-18 14h-8c-5-2-10-5-13-10h1c1 1 1 2 2 2h1v1l3 3h0c1 1 2 1 3 1l1 1h0l-4-4 1-1 1-1c0 1 1 1 2 1-2-1-3-3-5-5h0c-1-1-1-2-2-3 0-1-1-2-1-3l-1-2v-7z"></path><path d="M832 276c-1 0-1-1-1-1-1 1-3 4-5 5l6-9h1c-1-1-1-2 0-2l2-1c-1 3-1 6-3 8h0z" class="b"></path><defs><linearGradient id="E" x1="814.506" y1="305.539" x2="845.814" y2="259.704" xlink:href="#B"><stop offset="0" stop-color="#93908e"></stop><stop offset="1" stop-color="#d2d2d1"></stop></linearGradient></defs><path fill="url(#E)" d="M835 249c6 13 8 28 3 41-3 9-8 16-15 23l-5 3-1-1c2-1 4-2 5-5 3-2 4-4 5-7 1-2 1-2 0-4 0 0 1-2 1-3h-3l1-2c0-1 1-1 1-2-1-1-1-1-3-2 4-3 6-6 9-9h1v-1l-1-1h-1l-1-2 1-1h0c2-2 2-5 3-8 1-6 0-13 0-19z"></path><path d="M824 290c4-3 6-6 9-9h1c-1 5-3 11-6 15h-3l1-2c0-1 1-1 1-2-1-1-1-1-3-2z" class="J"></path><path d="M443 686l1-5v39h1l-1-33v-2-2c0-1 0-2 1-3h0c0 2-1 5 0 7 0 12 1 23 0 35s-3 23-5 34v1c-5 20-16 40-32 52-7 5-14 9-22 12-1 0-2 1-3 2l-29 4v7h87c7-1 14-1 22-2 2-1 8-2 10-2v1l-22 3h-8c-3 1-7 1-10 1h-25-4-51l1-8h-1c0 2 1 8-1 10v-12c21 0 43-6 59-20 22-21 30-51 32-81v-38z" class="y"></path><defs><linearGradient id="F" x1="212.14" y1="239.909" x2="235.525" y2="286.488" xlink:href="#B"><stop offset="0" stop-color="#1d1c1a"></stop><stop offset="1" stop-color="#3e3d3d"></stop></linearGradient></defs><path fill="url(#F)" d="M200 248c0-2 1-3 2-4l1 1c0 12 1 24 10 33l4 4v2c4 1 7 3 11 2 2-1 4-2 6-4 2-1 3-2 5-3v1h1c3-2 4-6 5-9 1-1 1-2 1-2 0-1 1 0 1 0 0-1 0-1 1-2v1l1 1 1-5 1 2 2 1c-3 9-9 17-19 22-5 2-11 3-17 1-4-1-6-4-9-7-5-6-9-15-9-23 0-3 2-6 1-9h0v-3z"></path><path d="M228 286l2 1c-1 1-2 2-3 2h-3c-4 0-8-3-10-6h0l3 1c4 1 7 3 11 2z" class="h"></path><path d="M233 228l4-2 1 1c2 2 3 2 5 4h1l3-1 1 1v2h0l2 2c0 1 1 2 0 3v1 1c-1 1-2 2-2 3 2 3 3 6 3 10h0 1l3 2v3 3l-1 1-1 5-2-1-1-2-1 5-1-1v-1c-1 1-1 1-1 2 0 0-1-1-1 0 0 0 0 1-1 2-1 3-2 7-5 9h-1v-1c-2 1-3 2-5 3-2 2-4 3-6 4-4 1-7-1-11-2v-2c3 2 6 3 9 2 5 0 9-3 12-6 5-7 5-17 4-24-1-10-4-18-9-26z" class="J"></path><path d="M243 245h1c2 5 3 12 2 18 0 3-1 5-1 8-1 3-2 7-5 9h-1v-1l1-1c7-10 5-22 3-33z" class="Y"></path><path d="M240 235c-1-2-2-3-2-4v-1s1-1 1 0c3 0 6 4 7 7l2-2h0 2c0 1 1 2 0 3v1 1c-1 1-2 2-2 3 2 3 3 6 3 10h0 1l3 2v3 3l-1 1-1 5-2-1-1-2-1 5-1-1v-1c-1 1-1 1-1 2 0 0-1-1-1 0 0 0 0 1-1 2 0-3 1-5 1-8 1-6 0-13-2-18h-1v-1l-3-9z" class="p"></path><path d="M252 255c0 2 0 3-1 5h-1l-1-2c1-2 1-2 3-3z" class="d"></path><g class="G"><path d="M250 264l1-1 2-1h1l-1 5-2-1-1-2zm1-11h1l3 2v3 3l-4-1c1-2 1-3 1-5l-1-2z"></path><path d="M240 235c-1-2-2-3-2-4v-1s1-1 1 0c3 0 6 4 7 7l2-2h0 2c0 1 1 2 0 3v1 1c-1 1-2 2-2 3 2 3 3 6 3 10l-1-3h-1c-1-1-1-1-1-3-1 0-1-1-2-2h0c-1-1-2-2-2-4h-1l1-2-1-1c0-2-2-3-3-3z"></path></g><path d="M246 237l2-2h0 2c0 1 1 2 0 3v1 1c-1 1-2 2-2 3-1 0-1-1-2-1v-4-1z" class="c"></path><path d="M188 334l2-1 1 1 2 2h1 1l2-2 3 1 6 3s2 0 2 1v1c2 1 1 1 2 3 0 2-1 1-2 3 0 1 1 4 2 5 2 2 3 3 6 3v6 1 1l1 1c-1 1-1 0-3 2v1c-1 1 0 2-1 3v1h-3c1 3 4 2 3 5-1 1-1 1-3 1-1-1-2-3-3-4-2-1-5-8-5-10l-10-21-4-7z" class="D"></path><path d="M203 348l1 2h-1v2c0 1 0 1 1 2l1 1h-1 0c-3-1-1 1-4-1h0v-2l1-2 2-2z" class="H"></path><path d="M208 342l2 1c0 2-1 1-2 3 0 1 1 4 2 5 2 2 3 3 6 3v6c-1 0-1 0-2-1h-1c1-1 0-1 1-2h0v-1l-1 1-1-1 1-1-1-1c-1 1-2 1-3 1l-1-1 1-1-1-1h-2-1v-1l-1-1-1-2h-1l1-1 1 1c0-2 1-3 1-4s2-2 3-2z" class="q"></path><path d="M188 334l2-1 1 1 2 2h1 1l2-2 3 1 6 3s2 0 2 1v1c2 1 1 1 2 3l-2-1c-1 0-3 1-3 2s-1 2-1 4l-1-1-1 1h1l-2 2-1 2-3-3h0c1 4 5 9 5 13h0l-10-21-4-7z" class="S"></path><path d="M188 334l2-1 1 1 2 2h1 1c1 0 1 0 2 1 0 1 1 2 1 3h-4l-1-1h0c0-1-1-2-2-3v1c0 1 1 2 2 3l-1 1-4-7z" class="Z"></path><path d="M197 349c-2-3-3-6-5-9 2 1 3 3 4 4l1 1h3 1 2 0l1-1h1c0 1-1 2-1 4l-1-1-1 1h1l-2 2-1 2-3-3h0z" class="F"></path><path d="M205 344c0 1-1 2-1 4l-1-1-1 1h1l-2 2c-1-1-2-1-2-2h0c2-1 3-1 4-3h0l1-1h1z" class="C"></path><path d="M197 334l3 1 6 3s2 0 2 1v1c2 1 1 1 2 3l-2-1c-1 0-3 1-3 2h-1l-1 1h0-2c0-1-1-1-1-2-1 0-1-2-1-2v-1l-1 1v-1c0-1-1-2-1-3-1-1-1-1-2-1l2-2z" class="I"></path><path d="M197 334l3 1c-1 1-2 1-3 2-1-1-1-1-2-1l2-2z" class="l"></path><path d="M203 340h3c0 1 0 0 1 1l1 1c-1 0-3 1-3 2h-1c-1 0 0 0-1-1 0-1 0-1 1-1v-1l-1-1z" class="V"></path><path d="M199 340v-1c2 0 3 1 4 1l1 1v1c-1 0-1 0-1 1 1 1 0 1 1 1l-1 1h0-2c0-1-1-1-1-2-1 0-1-2-1-2v-1z" class="f"></path><path d="M199 341c2 0 2 1 3 2l1 2h-2c0-1-1-1-1-2-1 0-1-2-1-2z" class="U"></path><path d="M208 339l14 4c1 0 4 1 5 1 3 1 8 3 11 3h2l-1 3-1 2c-1 2-2 5-3 7h-1v3c-1 1-1 2-1 3l-1 1-1 4c-1 0-1-1-2-2l-1 1c0 1-1 1-1 2l-1 1v-2l-2-1c0 1-1 3-2 4h-1-1c-1 0-1 1-2 1l-1-1 1-2-1-1c-1 0-2-1-3-1v-3-1c2-2 2-1 3-2l-1-1v-1-1-6c-3 0-4-1-6-3-1-1-2-4-2-5 1-2 2-1 2-3-1-2 0-2-2-3v-1z" class="G"></path><path d="M230 353c1-4-1-2-2-5 2 0 3 0 4 1h0c1-1 1 0 2-1v-1l1 1v2h0-2c0 1 1 2 2 3h-4-1z" class="Q"></path><path d="M211 345l2-1c1 1 3 1 4 2 0 3 0 4-2 5-1 1-2 1-3 1-1-1-2-2-2-4s0-2 1-3z" class="W"></path><path d="M217 356c0-3 2-7 3-9l3 1c2 1 4 1 6 3-1 2-2 4-4 5h-2c-1 0-3 0-4 1l2 2h-1-2l-1-3z" class="F"></path><path d="M223 356c-1-1-1-1-1-3l3-1v3c-1 1-1 1-2 1z" class="N"></path><path d="M223 348c2 1 4 1 6 3-1 2-2 4-4 5h-2 0c1 0 1 0 2-1v-3-1l-1-1c0 1-1 1-1 2l-1-1c-1-1-1-1-1-2l2-1z" class="D"></path><path d="M235 350l2-2 2 2-1 2c-1 2-2 5-3 7h-1v3c-1 1-1 2-1 3l-1 1c0-1-1-2-1-4v-2l-2 2-1-1v-2h-1c0 1-1 2-2 2-1 1-1 0-2 0l2-1v-1c1-1 1-2 2-3l2-1 1-2h1 4c-1-1-2-2-2-3h2 0z" class="l"></path><path d="M235 353h1v1h-3v1c1 1 1 0 1 2-1 1-2 2-3 2l-3-2-1-1 2-1 1-2h1 4z" class="I"></path><path d="M235 353h1v1h-3v1l-1 1c-1 0-2 0-3-1l1-2h1 4z" class="V"></path><path d="M217 356l1 3h2 1l-2-2c1-1 3-1 4-1h2c0 1 0 1-1 1-1 1-1-1-2 1v1 1l1 1c1 0 1 1 2 0 1 0 2-1 2-2h1v2l1 1 2-2v2c0 2 1 3 1 4l-1 4c-1 0-1-1-2-2l-1 1c0 1-1 1-1 2l-1 1v-2l-2-1c0 1-1 3-2 4h-1-1c-1 0-1 1-2 1l-1-1 1-2-1-1c-1 0-2-1-3-1v-3-1c2-2 2-1 3-2l-1-1v-1h2c-2-2-1-3-1-5z" class="q"></path><path d="M224 369c0-1 0-2 1-3l1 1v3l-2-1zm-7 1h2c2 0 1-1 3-1 0 2-1 2-1 4h-1c-1 0-1 1-2 1l-1-1 1-2-1-1z" class="T"></path><path d="M217 363l2 1c1 1 1 1 1 3 0 1-1 1-2 2-1 0-2-1-3-1 0-1 0-2-1-3 2-2 2-1 3-2z" class="H"></path><path d="M217 356l1 3h2 1l-2-2c1-1 3-1 4-1h2c0 1 0 1-1 1-1 1-1-1-2 1v1 1l1 1c1 0 1 1 2 0 1 0 2-1 2-2h1v2l1 1 2-2v2c0 2 1 3 1 4l-1 4c-1 0-1-1-2-2v-1l-1-1h1 1v-1l-1-1-2-2c-1 1-3 2-4 1-2-1-2-2-4-2v1l1 1-1 1-2-1-1-1v-1h2c-2-2-1-3-1-5z" class="R"></path><path d="M255 241l2 4v2 5c1 2 1 3 0 5s0 5-1 8c-1 1-1 3-1 5h1c-1 1-1 1-1 2h0c1-1 2-3 2-5v-1c1-2 1-5 2-8 0-2 1-2 3-3l-1 9c1-1 1-3 3-4l1 2c-1 1-1 2-1 4v1l-3 6-1 2c0 2-1 3-2 4h-1c0 2 0 2-1 4-2 1-3 3-5 5l-1 1c-1 2-3 4-5 6l-3 2c-2 0-5 1-7 2-1-1-2-1-3 0v-1l-1-1h-4c-2 1-6 0-8 0-7-2-11-7-14-12h0c1 0 2 2 3 3 0 1 0 1 1 2l2-2v-1c-2-1-2-3-3-4 3 3 5 6 9 7 6 2 12 1 17-1 10-5 16-13 19-22l1-5 1-1v-3-3l-3-2 2-1c1-1 1-5 1-7v-4z" class="a"></path><path d="M255 245c1 4 1 10 0 13v-3l-3-2 2-1c1-1 1-5 1-7z" class="P"></path><defs><linearGradient id="G" x1="235.247" y1="286.325" x2="254.434" y2="289.62" xlink:href="#B"><stop offset="0" stop-color="#161514"></stop><stop offset="1" stop-color="#2f2f2e"></stop></linearGradient></defs><path fill="url(#G)" d="M262 255l-1 9c1-1 1-3 3-4l1 2c-1 1-1 2-1 4v1l-3 6-1 2c0 2-1 3-2 4h-1c0 2 0 2-1 4-2 1-3 3-5 5l-1 1c-1 2-3 4-5 6l-3 2c-2 0-5 1-7 2-1-1-2-1-3 0v-1l-1-1c0-1 7-3 8-3 3-2 6-4 9-7 0-1 0-2 1-3l3-6c1-1 2-1 2-3 0-1 0-1 1-2v-1c1-1 2-3 2-5v-1c1-2 1-5 2-8 0-2 1-2 3-3z"></path><path d="M264 260l1 2c-1 1-1 2-1 4v1l-3 6h-1c-1 1-1 2-1 3l-2 2c-1-2 1-4 1-6 0-1 2-7 3-8s1-3 3-4z" class="X"></path><path d="M262 255l-1 9c-1 1-3 7-3 8-3 5-6 11-10 15 0-1 0-2 1-3l3-6c1-1 2-1 2-3 0-1 0-1 1-2v-1c1-1 2-3 2-5v-1c1-2 1-5 2-8 0-2 1-2 3-3z" class="O"></path><path d="M176 316c1 0 1 0 2 1 9 7 19 12 30 13 6 1 12 1 18 1l7-1c3 0 7-1 10-1 2 0 4 0 5 1l-5 2h2l1 3c0 1 0 1-1 1-3 1-2 3-6 3l-1 1c2 1 4 0 4 1v3l-1 2-1 1h-2c-3 0-8-2-11-3-1 0-4-1-5-1l-14-4c0-1-2-1-2-1l-6-3-3-1-2 2h-1-1l-2-2-1-1-2 1c-3-7-9-11-12-18z" class="g"></path><path d="M190 329l7 5-2 2h-1-1l-2-2-1-5z" class="I"></path><path d="M234 343c-3-3-7-2-9-5v-1c2 0 4 0 6-1h4c1-1 2-1 4-1-3 2-5 3-9 3v1c3 2 6 0 8 3l-1 1-3-1v1z" class="W"></path><path d="M176 316c1 0 1 0 2 1h-1l13 12 1 5-1-1-2 1c-3-7-9-11-12-18z" class="K"></path><path d="M239 335h2c1 0 2 0 3-1v-1h-3l-3 1h-1-3v-1l9-1h2l1 3c0 1 0 1-1 1-3 1-2 3-6 3l-1 1c2 1 4 0 4 1v3l-1 2-1 1h-2c-3 0-8-2-11-3-1 0-4-1-5-1 2-1 7-1 9 0l1 1c0-1 1-1 2-1v-1l3 1 1-1c-2-3-5-1-8-3v-1c4 0 6-1 9-3z" class="D"></path><path d="M227 344h6s2 1 3 0l1-1 1 1c0 1-1 1 0 3-3 0-8-2-11-3z" class="C"></path><path d="M443 343v1c1 2 1 4 1 5v-5l1 3v156 113 36 22 13c-1-2 0-5 0-7h0c-1 1-1 2-1 3v2 2l1 33h-1v-39l-1 5v-9-17-64-82-171z" class="s"></path><path d="M440 757v1c1 2 0 3 0 5l-4 9c1 1 1 0 2 1l1 1h1v1h2l1-4 1-1c0-1 0-2 1-3h1l-2 5h1c0-1 1-2 1-3l1-1 1 2c-2 3-2 6-4 9-1 2-2 5-4 7-2 3-5 7-6 10s-3 6-5 9l-1 1-2 2-3 3h0c-1 0-2 1-3 2h-3l-5 3-4 2-2 1c-1 0-3 1-4 2h-2-1c-1 0-1 1-2 2h-14c1-1 2-2 3-2 8-3 15-7 22-12 16-12 27-32 32-52z" class="o"></path><path d="M408 809v1c-1 2-3 2-4 3l-7 4 1 1 2-2h1c1-1 2-1 3-2h1l1-1c1-1 1-1 2-1h1c-4 4-11 7-17 9-2 0-4 1-5 0h-1c8-3 15-7 22-12z" class="W"></path><defs><linearGradient id="H" x1="406.762" y1="820.817" x2="405.76" y2="805.031" xlink:href="#B"><stop offset="0" stop-color="#202120"></stop><stop offset="1" stop-color="#534e4e"></stop></linearGradient></defs><path fill="url(#H)" d="M426 799c0 2 0 3-2 4-1 2-1 3-2 4 0 1-1 2-2 3l-3 3-5 3-4 2-2 1c-1 0-3 1-4 2h-2-1c-1 0-1 1-2 2h-14c1-1 2-2 3-2h1c1 1 3 0 5 0 6-2 13-5 17-9 7-3 12-8 17-13z"></path><path d="M444 772h1c0-1 1-2 1-3l1-1 1 2c-2 3-2 6-4 9-1 2-2 5-4 7-2 3-5 7-6 10s-3 6-5 9l-1 1-2 2-3 3h0c-1 0-2 1-3 2h-3l3-3c1-1 2-2 2-3 1-1 1-2 2-4 2-1 2-2 2-4 8-8 13-17 18-27z" class="I"></path><path d="M434 796c-1 3-3 6-5 9l-1 1-2 2-3 3h0c-1 0-2 1-3 2h-3l3-3c2 0 3-2 4-3l10-11z" class="B"></path><path d="M445 674l1-4v-7l3 1 1 1 1-1v-2c0-2 1-2 2-3v5 4l1 1-1 46-3 32c0 7-1 14-4 20h-1c-1 1-1 2-1 3l-1 1-1 4h-2v-1h-1l-1-1c-1-1-1 0-2-1l4-9c0-2 1-3 0-5v-1-1c2-11 4-22 5-34s0-23 0-35v-13z" class="g"></path><path d="M445 503c1 0 4-1 5-1 1 1 1 1 3 1 0-4-1-10 0-14v-1 50c0 8 0 17 1 25l-1 1 1 1v20l-1 19c0 3 1 8-1 10l-1 2h-1l-2 2h-2l-1-2V503z" class="v"></path><path d="M453 564l1 1v20l-1 19c0 3 1 8-1 10v-8h-1v-2c1-1 1-3 1-5v-2c0-6-1-12 0-18v-6l1 2v-11z" class="e"></path><path d="M207 372c1 1 2 3 3 4 2 0 2 0 3-1 1-3-2-2-3-5h3v-1c1-1 0-2 1-3v3c1 0 2 1 3 1l1 1-1 2 1 1c1 0 1-1 2-1h1 1c1-1 2-3 2-4l2 1v2l1-1c0-1 1-1 1-2l1-1c1 1 1 2 2 2v8c1 7 2 13 5 20l3 7 1 2 2 3c1 2 2 4 4 5l1-1h3c1 2 2 2 2 4l-1 1c1 2 2 2 3 3s3 3 3 4c1 1 2 1 3 2l2 2c-1 0 0 0-1 1-2-1-5-2-7-3l-7-4-8-5c-14-9-26-23-33-37-2-2-3-5-4-7 1 0 4 1 6 0v1-1l-1-3z" class="C"></path><path d="M228 378s0 1 1 1h0v1h-2c0-1 0-1 1-2z" class="H"></path><path d="M221 387l-1-1v-2l1-1c1 1 1 1 3 1l1-1-1-2h-2l1-1h0 2l1 1c0 1 1 1 1 2s1 1 1 2h0c-1 0-1 0-1-1-3 0-4 0-5 2l-1 1z" class="F"></path><path d="M221 387l1-1c2 2 4 5 4 7 1 1 0 1 0 2 1 1 1 1 1 2h-1c-1-1-1-2-2-3 0-2-1-3-2-4l-1-3z" class="U"></path><path d="M230 403l-2-4v-1h1l3 1c0 1 1 1 0 2 0 1 0 1 1 2h0 4c1 3 1 6 4 8l-1-4 2 3c1 2 2 4 4 5l2 3c-1 1-1 1-2 1-1-2-1-2-3-3-1 0-3-1-4-2v-1c-1-1-1-2-2-2-3-2-5-4-8-7l1-1z" class="D"></path><path d="M240 407l2 3c1 2 2 4 4 5l2 3c-1 1-1 1-2 1-1-2-1-2-3-3-1-1-2-3-2-5l-1-4z" class="T"></path><path d="M230 403l-2-4v-1h1l3 1c0 1 1 1 0 2 0 1 0 1 1 2h0l1 1c1 2 2 4 4 5-3-2-5-3-8-6z" class="i"></path><path d="M202 375c1 0 4 1 6 0v1-1l2 3c2 1 2 3 4 5l2 4 1 1c0 1 1 1 2 2h3c1 1 2 2 2 4 1 1 1 2 2 3l-1 1c1 2 2 4 4 6h0c3 3 5 5 8 7 1 0 1 1 2 2v1c1 1 3 2 4 2 2 1 2 1 3 3 1 0 1 0 2-1l-2-3 1-1h3c1 2 2 2 2 4l-1 1c1 2 2 2 3 3s3 3 3 4c1 1 2 1 3 2l2 2c-1 0 0 0-1 1-2-1-5-2-7-3l-7-4-8-5c-14-9-26-23-33-37-2-2-3-5-4-7z" class="a"></path><path d="M202 375c1 0 4 1 6 0v1-1l2 3 7 13h-1c-4-1-4-4-6-7-1-1-3-2-4-2-2-2-3-5-4-7z" class="g"></path><path d="M247 414h3c1 2 2 2 2 4l-1 1c1 2 2 2 3 3s3 3 3 4c1 1 2 1 3 2l2 2c-1 0 0 0-1 1-2-1-5-2-7-3l-7-4 2 1h1 4l-6-6-1 1-1-1c1 0 1 0 2-1l-2-3 1-1z" class="B"></path><path d="M247 414h3c1 2 2 2 2 4l-1 1c1 2 2 2 3 3s3 3 3 4c-3-3-6-5-9-8l-2-3 1-1z" class="p"></path><path d="M210 378c2 1 2 3 4 5l2 4 1 1c0 1 1 1 2 2h3c1 1 2 2 2 4 1 1 1 2 2 3l-1 1c1 2 2 4 4 6h0c3 3 5 5 8 7 1 0 1 1 2 2v1c-3-3-7-5-10-8l-12-15-7-13z" class="T"></path><path d="M747 472c1 1 1 1 1 2l-1 3 2 2-1 1c0 1 0 1 1 2h0c1-2 1-1 1-2l1-2c2-1 3-1 5-1-1 4-2 7-3 11-1 3-7 16-6 17l-1 3h1c1-1 1-2 2-2l2-2c0-2 0-4 1-6 1-1 1-2 2-4 2-1 2-1 5 0l1-2h0c1 1 2 2 2 3v1c0 2-1 3-2 4 0 1-1 2-1 3h1c0-1 1-2 2-3h1l1-1 1 1-3 5c-10 10-21 20-33 27-9 6-19 11-30 11h0c1-2 5-3 7-5 23-14 35-41 41-66z" class="y"></path><path d="M747 477l2 2-1 1c0 1 0 1 1 2l-2 2-1-1 1-6z" class="l"></path><path d="M723 528c3 1 4-2 6-2 1 0 2 1 3 1-4 3-8 4-12 5 1-1 2-3 3-4z" class="R"></path><path d="M735 509l1 1h1c-2 4-3 6-3 11-1 1-3 2-4 2h-1l2-3h-2c1-2 2-4 3-5 0-2 2-5 3-6z" class="L"></path><path d="M732 515l1 1c0 1-1 4-2 4h-2c1-2 2-4 3-5z" class="V"></path><path d="M738 508c2 0 3-1 5-1v1l-1 2-2 2c0 1 0 1 1 2l-1 1c0 1-1 1-1 2v1c-1 1-3 2-5 3 0-5 1-7 3-11l1-2z" class="E"></path><path d="M749 482c0 1 0 2-1 4v1c1 1 1 1 1 2v1c1 1 1 1 1 2v1l-1-1-1 1h-2c0 2-1 2-1 4-1 1-1 2-2 3l-2 3c1 1 2 1 3 2 0 1-1 2-1 2-2 0-3 1-5 1l-1 2h-1l-1-1c2-6 7-12 8-19 1-2 2-5 3-7l1 1 2-2h0z" class="h"></path><path d="M741 503c1 1 2 1 3 2 0 1-1 2-1 2-2 0-3 1-5 1l3-5z" class="c"></path><path d="M748 487c1 1 1 1 1 2v1c1 1 1 1 1 2v1l-1-1-1 1h-2-1l1-1v-1c1-1 1-2 2-4z" class="G"></path><path d="M750 480l1-2c2-1 3-1 5-1-1 4-2 7-3 11-1 3-7 16-6 17-2 3-3 5-5 7l-1 2c-1-1-1-1-1-2l2-2 1-2v-1s1-1 1-2c-1-1-2-1-3-2l2-3c1-1 1-2 2-3 0-2 1-2 1-4h2l1-1 1 1v-1c0-1 0-1-1-2v-1c0-1 0-1-1-2v-1c1-2 1-3 1-4 1-2 1-1 1-2z" class="d"></path><path d="M741 503l2-3 2 2c-1 1-1 2-1 3-1-1-2-1-3-2z" class="G"></path><path d="M746 493h2v1 2 1l-2 1v1l1 1v1l-2 1-2-2c1-1 1-2 2-3 0-2 1-2 1-4z" class="X"></path><path d="M750 480l1 1s0 1 1 2h0c0 1-1 2-2 2l1 1 1-1c0 2-1 3-3 4 0-1 0-1-1-2v-1c1-2 1-3 1-4 1-2 1-1 1-2z" class="E"></path><path d="M760 492h0c1 1 2 2 2 3v1c0 2-1 3-2 4 0 1-1 2-1 3h1c0-1 1-2 2-3h1c-8 11-20 20-31 27-1 0-2-1-3-1-2 0-3 3-6 2l6-8h2l-2 3h1c1 0 3-1 4-2 2-1 4-2 5-3v-1c0-1 1-1 1-2l1-1 1-2c2-2 3-4 5-7l-1 3h1c1-1 1-2 2-2l2-2c0-2 0-4 1-6 1-1 1-2 2-4 2-1 2-1 5 0l1-2z" class="U"></path><path d="M751 504c2-1 4-4 6-4-2 4-7 10-11 12 0-1 0-2 1-3s2-2 2-3l2-2z" class="d"></path><path d="M747 505l-1 3h1c1-1 1-2 2-2 0 1-1 2-2 3s-1 2-1 3c-2 2-4 4-7 6v-1c0-1 1-1 1-2l1-1 1-2c2-2 3-4 5-7z" class="y"></path><path d="M760 492h0c1 1 2 2 2 3-1 2-3 3-4 5h-1c-2 0-4 3-6 4 0-2 0-4 1-6 1-1 1-2 2-4 2-1 2-1 5 0l1-2z" class="P"></path><path d="M760 492h0c1 1 2 2 2 3-1 2-3 3-4 5l-1-1c1-2 2-3 2-5l1-2z" class="c"></path><path d="M454 585h1v5l2 1c0 8-1 16 1 23v29c0 2 0 4-1 7v10 4 2c0 1-1 2 0 4 0 2 0 5 2 7 1 0 1-1 3-1h2c-2 5-2 10 0 14h0 1v1l1 2c1 2 3 5 6 6h4v2c2 0 1-1 3-1l-1 1v4h0-1v4 1l-1-1s-1-1-2-1h0-1l-1 1c-1 0-1 0-2 1-1 0-1 1-2 1l-1-2c-1 0-1 0-2 1-1 0-2 1-3 1h-5 0v-5l-1 1v-7l-1 5v2l1 1v1c-1 1-1 3-1 5h0c-1 1-1 1-2 1l1-46-1-1v-4-5c-1 1-2 1-2 3v2l-1 1-1-1-3-1v7l-1 4v-22-36l1 2h2l2-2h1l1-2c2-2 1-7 1-10l1-19z" class="h"></path><path d="M458 643c0 2 0 4-1 7-1 2-1 5-2 7v-10c1-1 2-2 3-4z" class="i"></path><path d="M454 585h1v5l2 1c0 8-1 16 1 23v29c-1 2-2 3-3 4 0-8 1-17 0-25h0v-4c-1 1-1 4-1 6l-1-9v-11l1-19z" class="S"></path><path d="M454 585h1v5 21c0 3 1 8 0 11h0v-4c-1 1-1 4-1 6l-1-9v-11l1-19z" class="d"></path><path d="M455 657c1-2 1-5 2-7v10 4 2c0 1-1 2 0 4 0 2 0 5 2 7 1 0 1-1 3-1h2c-2 5-2 10 0 14h0v4l-1-2s-1 0-1 1h1v1c-1 0-2-1-3-1v1 1 1l-1 1c-1 1-1 3-1 5h1c0 1 1 2 0 3v1l2 3h0c1 0 1 1 2 1h2c-1 0-2 1-3 1h-5 0v-5l-1 1v-7c-1-1-1-17-1-19v-9-15z" class="F"></path><path d="M459 681h1v1l-2 1v2c1 0 1 1 2 1l-1 2v2c0 1-1 2-1 3h1l1-1v1 1 1 1l-1 1c-1 1-1 3-1 5h1c0 1 1 2 0 3v1l2 3h0c1 0 1 1 2 1h2c-1 0-2 1-3 1h-5 0v-5h1c0-2-1-3 0-5h0c-1-2-1-2-1-3-1-2 0-3 0-4-1-1-1-2-1-3l1-1-1-1c0-1 0-1 1-3h0l-1-1v-3c1 0 2 0 3-1z" class="B"></path><path d="M457 670c0 2 0 5 2 7 1 0 1-1 3-1h2c-2 5-2 10 0 14h0v4l-1-2s-1 0-1 1h1v1c-1 0-2-1-3-1v-1l-1 1h-1c0-1 1-2 1-3v-2l1-2c-1 0-1-1-2-1v-2l2-1v-1h-1c-1-1-2-1-2-2-1-3-1-7 0-9z" class="D"></path><path d="M465 690v1l1 2c1 2 3 5 6 6h4v2c2 0 1-1 3-1l-1 1v4h0-1v4 1l-1-1s-1-1-2-1h0-1l-1 1c-1 0-1 0-2 1-1 0-1 1-2 1l-1-2c-1 0-1 0-2 1h-2c-1 0-1-1-2-1h0l-2-3v-1c1-1 0-2 0-3h-1c0-2 0-4 1-5l1-1v-1-1-1c1 0 2 1 3 1v-1h-1c0-1 1-1 1-1l1 2v-4h1z" class="a"></path><path d="M460 695l2 1c0 1 0 2-1 3s-1 1-1 2v2h1l1-1h1c0 1 0 2-1 3h1v2l1 1c2 0 2-1 3-1s1 0 2 1c1-2 1-4 1-6 2 1 2 1 2 4 0 1 0 1 1 2l-1 1c-1 0-1 0-2 1-1 0-1 1-2 1l-1-2c-1 0-1 0-2 1h-2c-1 0-1-1-2-1h0l-2-3v-1c1-1 0-2 0-3h-1c0-2 0-4 1-5l1-1v-1z" class="H"></path><path d="M465 690v1l1 2c1 2 3 5 6 6h4v2c2 0 1-1 3-1l-1 1v4h0-1v4 1l-1-1s-1-1-2-1h0-1c-1-1-1-1-1-2 0-3 0-3-2-4h0l-1-2h-1 0c0-2-1-2-1-3l-3-3v-4h1z" class="B"></path><path d="M474 708l1-1v-1h-1v-1c1-1 1-2 3-2l1 1-1 1v4 1l-1-1s-1-1-2-1h0z" class="W"></path><path d="M452 614c2-2 1-7 1-10v11 18l1 28v8l-1-1v-4-5c-1 1-2 1-2 3v2l-1 1-1-1-3-1v7l-1 4v-22-36l1 2h2l2-2h1l1-2z" class="B"></path><path d="M449 637h1l1 7c1 6 0 12-1 18 0-2-1-4-1-6 0-4 0-9 1-13 0-1-1-1-1-2l1-2-1-2z" class="q"></path><path d="M829 330c5-1 11-3 15-6 3-1 6-4 9-6-11 19-17 40-28 58-4 7-9 14-15 20l-6 7s-2 1-2 2 0 3-1 3l-1 10c0 4-1 10-3 14l-1-1v-2h-2c-1 1-1 1-3 1l-1 6h-1v-2-1 2l-1 1-1-1h-1v-2c-1-1-1-2-1-4-1 0 0 0-1-1 0-1 0-2-1-4-2-1-2-3-4-5h0c0-1-1-2-2-2l-1-1-2-2h-1c0-2 2-2 3-3l-5 1v-3l2-2 1-1 4-2c2-1 3-2 5-4l1-1 1-1 1-1 1-4v-1l1-1c0-1 1-1 2-1l2-2 1-1 1-2 1-1c1 0 1 0 2 1 1 0 2-1 2-2s1-2 1-3h0c2-1 3-2 4-4 1-3 2-4 1-6 1-2 1-2 2-3h1c-1-1 0-1-1-2h0c0-1-1-2-2-3 2 0 3 1 5 2h4c1-1 2-2 2-4h1c0-2 0-2-1-3l1-2h-1l1-2v-1c-1 0-1-1-2-1 1-1 1-1 1-2 1-1 1 0 1-1l1 1c2-1 1-2 1-4h2 0c1 0 2 0 3-1h1 0v1 2h1c3-1 1-2 2-4h1l1-1c2 0 2 0 4 1l1-2c0-1 2-3 2-5l-1 1-3-1 1-2h-1l3-1c-2 0-3-1-4-1l-1-1h-2v-1z" class="y"></path><path d="M831 331l13-5-1 2h0c-3 2-5 4-7 5-2 0-3-1-4-1l-1-1z" class="o"></path><path d="M792 411c2-2 5-3 7-5v6l-1 9c-1 2-1 5-2 8h-2c-1 1-1 1-3 1l1-9h0v-1-9z" class="C"></path><path d="M792 421l2 2c-1 1-1 2-1 4 0 1 1 1 1 2-1 1-1 1-3 1l1-9h0z" class="F"></path><path d="M796 413c1-1 1-1 3-1l-1 9h-4c-2-2 0-1-1-4h0l1-1c1-2 1-2 2-3z" class="B"></path><path d="M796 413h2c0 1-2 3-3 3h-1c1-2 1-2 2-3z" class="D"></path><path d="M781 414c0-2 5 0 7-1 1 0 3-1 4-2v9 1h0l-1 9-1 6h-1v-2-1 2l-1 1-1-1h-1v-2c-1-1-1-2-1-4-1 0 0 0-1-1 0-1 0-2-1-4-2-1-2-3-4-5h0c0-1-1-2-2-2l-1-1-2-2h0c2 1 3 2 5 2l2-2z" class="S"></path><path d="M788 419c2 1 3 2 4 2l-1 9-1 6h-1v-2-1l-1-1c1-1 1-3 1-3v-3c-1-2 0-4-1-7z" class="G"></path><path d="M781 414c0-2 5 0 7-1 1 0 3-1 4-2v9 1h0c-1 0-2-1-4-2v-5c-3 0-5 0-7 1v-1z" class="X"></path><defs><linearGradient id="I" x1="777.214" y1="422.163" x2="788.255" y2="425.981" xlink:href="#B"><stop offset="0" stop-color="#1c1b1d"></stop><stop offset="1" stop-color="#383734"></stop></linearGradient></defs><path fill="url(#I)" d="M774 414h0c2 1 3 2 5 2l2-2v1c3 4 5 10 6 15 0 1 1 1 1 2l1 1v2l-1 1-1-1h-1v-2c-1-1-1-2-1-4-1 0 0 0-1-1 0-1 0-2-1-4-2-1-2-3-4-5h0c0-1-1-2-2-2l-1-1-2-2z"></path><path d="M844 326l4-2c-10 18-16 36-26 54h-3c0-1 0-2 1-4h0c1-5 5-10 7-15h-1c1-2 2-5 3-7 2-3 3-6 5-9l1-2c0-1 2-3 2-5l-1 1-3-1 1-2h-1l3-1c2-1 4-3 7-5h0l1-2z" class="W"></path><path d="M836 333c2-1 4-3 7-5l-16 31h-1c1-2 2-5 3-7 2-3 3-6 5-9l1-2c0-1 2-3 2-5l-1 1-3-1 1-2h-1l3-1z" class="I"></path><path d="M805 380h1 2l2 2v1c-1 1-1 0-1 2-1 1-1 2-2 3l-1 1h2c2-1 3-1 4-1h2v1l-1 1-4 5c-8 8-16 13-26 15l-7 1-5 1v-3l2-2 1-1 4-2c2-1 3-2 5-4l1-1 1-1 1-1 1-4v-1l1-1c0-1 1-1 2-1l2-2 1-1 1-2 1-1c1 0 1 0 2 1 1 0 2-1 2-2s1-2 1-3h0c1 1 2 1 3 2l2-2z" class="N"></path><path d="M806 389h2c2-1 3-1 4-1h2v1l-1 1-4 5v-1-1l-1-1c-2 0-3 1-5 2l-2-1c2-1 3-2 4-4h1z" class="H"></path><path d="M790 399v-1c1 1 2 1 3 2l-3 1c0 1-1 1-2 2-1 2-2 2-4 3-2 0-3 0-3 1l-2-1-1 1-1 1 1 1h1c1 0 2 0 3 1h1l-7 1-5 1v-3l2-2 1-1 4-2c2-1 3-2 5-4l1-1c0 1 0 2 1 2l1 1c2-1 2-2 4-3z" class="O"></path><path d="M790 399v-1c1 1 2 1 3 2l-3 1c0 1-1 1-2 2 0 0-3 1-4 1s0-1-2 0h-4c2-1 3-2 5-4l1-1c0 1 0 2 1 2l1 1c2-1 2-2 4-3z" class="U"></path><path d="M805 380h1 2l2 2v1c-1 1-1 0-1 2-1 1-1 2-2 3l-1 1h-1c-1 2-2 3-4 4l-5 4-3 3c-1-1-2-1-3-2v1c-2 1-2 2-4 3l-1-1c-1 0-1-1-1-2l1-1 1-1 1-4v-1l1-1c0-1 1-1 2-1l2-2 1-1 1-2 1-1c1 0 1 0 2 1 1 0 2-1 2-2s1-2 1-3h0c1 1 2 1 3 2l2-2z" class="m"></path><path d="M797 390h2v-2h1c1 1 1 1 1 3h-1l-3 3-1-1c0-1 0-2 1-3zm3-10c1 1 2 1 3 2l-1 2v2c-2 0-3 2-5 2 1-2 3-3 4-4l-1-1h-1c0-1 1-2 1-3h0z" class="L"></path><path d="M805 380h1 2l2 2v1c-1 1-1 0-1 2-1 1-1 2-2 3l-1 1h-1-1c-1-1-2-1-2-2v-1h2c0 1 0 1 1 1l1-1v-2l-1-1-2 1h-1l1-2 2-2z" class="R"></path><path d="M793 387l2 1v1l1 1h1c-1 1-1 2-1 3v3 1l-3 3c-1-1-2-1-3-2l2-2v-2h1v-2h-2l-1 1v-3l2-2 1-1z" class="f"></path><path d="M792 388c1 1 1 3 1 4h0-2l-1 1v-3l2-2z" class="U"></path><path d="M790 390v3l1-1h2v2h-1v2l-2 2v1c-2 1-2 2-4 3l-1-1c-1 0-1-1-1-2l1-1 1-1 1-4v-1l1-1c0-1 1-1 2-1z" class="F"></path><path d="M785 401c0-1 1-1 1-2h4c-2 1-2 2-4 3l-1-1z" class="C"></path><path d="M787 393l2 2-1 3h-1-2l1-1 1-4z" class="w"></path><path d="M790 390v3l-1 2-2-2v-1l1-1c0-1 1-1 2-1z" class="R"></path><path d="M825 347h1c3-1 1-2 2-4h1l1-1c2 0 2 0 4 1-2 3-3 6-5 9-1 2-2 5-3 7h1c-2 5-6 10-7 15h0c-1 2-1 3-1 4h3c-3 4-5 9-9 12l1-1v-1h-2c-1 0-2 0-4 1h-2l1-1c1-1 1-2 2-3 0-2 0-1 1-2v-1l-2-2h-2-1l-2 2c-1-1-2-1-3-2 2-1 3-2 4-4 1-3 2-4 1-6 1-2 1-2 2-3h1c-1-1 0-1-1-2h0c0-1-1-2-2-3 2 0 3 1 5 2h4c1-1 2-2 2-4h1c0-2 0-2-1-3l1-2h-1l1-2v-1c-1 0-1-1-2-1 1-1 1-1 1-2 1-1 1 0 1-1l1 1c2-1 1-2 1-4h2 0c1 0 2 0 3-1h1 0v1 2z" class="F"></path><path d="M810 375v2h-1-1v-2h2z" class="C"></path><path d="M816 357l1-2 1 1 1 2v3l-2 1c1 2 1 1 1 3h0c-1 0-1 1-2 1-1 1-2 1-3 1l-2 1-1 1v-2-1-1c1 1 1 0 2 1l1 1c0-1 1-2 1-3 1-1 2-2 2-4h1c0-2 0-2-1-3z" class="m"></path><path d="M810 375c1-2 1-4 1-5 2-1 2 0 3 0 0-1 2-2 3-2v1h0v2l-2 2c0 1 0 1 1 2l-1 1h-2v2h-1 0c-1-1-2-1-2-1v-2z" class="H"></path><path d="M807 388h1c2-1 4-4 5-6h1c1-1 3-2 3-4l-1-1c2-2 2-2 4-3-1 2-1 3-1 4h3c-3 4-5 9-9 12l1-1v-1h-2c-1 0-2 0-4 1h-2l1-1z" class="C"></path><path d="M805 362c2 0 3 1 5 2h4c0 1-1 2-1 3l-1-1c-1-1-1 0-2-1v1 1 2c-1 1-2 2-2 4 0 1-1 1-1 3h-1l2 2-2 2h-1l-2 2c-1-1-2-1-3-2 2-1 3-2 4-4 1-3 2-4 1-6 1-2 1-2 2-3h1c-1-1 0-1-1-2h0c0-1-1-2-2-3z" class="Z"></path><path d="M805 380c-1-1-1-1-1-2l2-2 2 2-2 2h-1z" class="V"></path><path d="M825 347h1c3-1 1-2 2-4h1l1-1c2 0 2 0 4 1-2 3-3 6-5 9-1 2-2 5-3 7h-1c-1 3-2 5-3 7h-1c0-2 1-3 2-5 0-1 1-2 2-2v-1c0-1 1-1 1-3h-1l-1 2s0 1-1 1c0 2 0 2-1 2h-1l2-3-1-1h-4l-1-1h-1l1-2v-1c-1 0-1-1-2-1 1-1 1-1 1-2 1-1 1 0 1-1l1 1c2-1 1-2 1-4h2 0c1 0 2 0 3-1h1 0v1 2z" class="C"></path><path d="M825 344h0v1h-1-1l-3 3h0c1 1 0 1 0 2h-1l-1-1c2-1 1-2 1-4h2 0c1 0 2 0 3-1h1z" class="i"></path><path d="M825 347h1c3-1 1-2 2-4h1l1-1c2 0 2 0 4 1-2 3-3 6-5 9 0-1 0 0-1-1 0 2 0 2-1 3-1-2 0-3 0-4-2 1-2 2-4 4v-1h-1c2-2 2-2 2-5 1 0 1 0 1-1z" class="F"></path><defs><linearGradient id="J" x1="746.997" y1="455.155" x2="764.715" y2="455.492" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#232221"></stop></linearGradient></defs><path fill="url(#J)" d="M748 398s1 0 2-1l1 1h3c-1-1-2-1-3-2l1-1h3l1 6 5 3h0l1 1 2 1h2l1-1-3-3 6 3 1 1v1l3-1-1 1-2 2v3l5-1c-1 1-3 1-3 3h1l2 2 1 1c1 0 2 1 2 2h0c2 2 2 4 4 5 1 2 1 3 1 4 1 1 0 1 1 1 0 2 0 3 1 4 0 2-1 5 0 6 0 1-1 2-1 3v1h1 1c-1 1-2 1-3 1v2h-2c0 1-1 1-2 2l2 2c-1 1-1 2-2 2h-1c-1 2-2 3-3 5 1 1 0 1 0 3-1 1-3 2-3 4l1 2h-1l1 3h-1v3l-2 1-1-2-1 2-1 2-2 1-1 1 1 1c-1 0-1 0-2-1v1c0 1 0 2 1 2v1 1h2l1 2h1-3v1 2h-1c-2 1-3 3-5 5h0l-1 2c-3-1-3-1-5 0-1 2-1 3-2 4-1 2-1 4-1 6l-2 2c-1 0-1 1-2 2h-1l1-3c-1-1 5-14 6-17 1-4 2-7 3-11-2 0-3 0-5 1l-1 2c0 1 0 0-1 2h0c-1-1-1-1-1-2l1-1-2-2 1-3h0l3-15 2-2c1-1 1-1 2-1v-1h0l-1-3c0-1 1-2 2-3l-1-1v-1h0c-1 0-1-1-2-1l-1-4v-1c1-5 0-12-1-18v3l-1 1-1-8 1 1v-7-1c0-4-1-9-2-14z"></path><path d="M764 455l1 1v1c0 1-1 1-1 2l-1 2v-2-2l1-2z" class="c"></path><path d="M759 453l1 1v4h-2v-3l1-2z" class="M"></path><path d="M765 453h3c-1 2-2 2-2 4h-1v-1l-1-1 1-2z" class="P"></path><path d="M765 446l2 2 1 1-2 1v1l-1 1-1-1c0-2 0-4 1-5z" class="G"></path><path d="M765 446c0-3 0-3 1-5 0 2 1 3 1 4h1 0l3 1-1 1-3 1-2-2z" class="C"></path><path d="M758 458h2l-2 9h-1v-7c1-1 1-1 1-2z" class="m"></path><path d="M767 440c2 0 3 0 5 1v2l-1 1h-1l-2 1h0-1c0-1-1-2-1-4l1-1z" class="H"></path><path d="M764 434l1-1c0-1-1-1 0-1h1c0 1 0 1-1 2l1 1c1 0 2 1 3 1l1 1h1v1c-1 0-1 1-3 1h-2l-1 1-1-1c0-2 1-3 0-4v-1z" class="I"></path><path d="M773 448l2 2-2 3c-1 0-1 0-2 1h-1l-2-1h-3l-1-1h1l1-1v-1l2-1c2 0 2 1 4 0l1-1z" class="c"></path><path d="M772 441h1l2 2c0 2 0 3-2 5l-1 1c-2 1-2 0-4 0l-1-1 3-1 1-1-3-1 2-1h1l1-1v-2z" class="h"></path><path d="M772 441h1l2 2c0 2 0 3-2 5l-1 1-1-1c0-1 1-1 2-2 0-1 1-1 1-2v-1h-2v-2z" class="Q"></path><path d="M761 438l-1 16-1-1-1 2-1-1v-3c0-1-1-2-1-2l-1-1v-1h1l1-1v-3l-1-1v-1c1-2 3-2 5-3z" class="U"></path><path d="M756 447c2-1 2-1 3 0l1 2-2 2c0-2-1-2-3-3v-1h1z" class="m"></path><path d="M755 448c2 1 3 1 3 3l1 2-1 2-1-1v-3c0-1-1-2-1-2l-1-1z" class="Z"></path><path d="M771 437c3 0 3 2 6 2v1l1 1c1 1 1 2 1 4-1 1-1 2 0 3l-2 1v1l1 1c-1 1-3 2-3 4l-2-2 2-3-2-2c2-2 2-3 2-5l-2-2h-1c-2-1-3-1-5-1l-1-1h2c2 0 2-1 3-1v-1z" class="k"></path><path d="M775 443h1c1 2 1 3 0 5 0 1 0 2-1 2l-2-2c2-2 2-3 2-5z" class="P"></path><path d="M771 438c1 0 1 1 3 1 1 1 1 2 2 3v1h-1l-2-2h-1c-2-1-3-1-5-1l-1-1h2c2 0 2-1 3-1z" class="b"></path><path d="M761 407h0l1 1 4 6h0c2 0 1 0 2 1h1c1 1 1 1 3 2l1 1c-2 2-2 2-2 4l-1 1-3-3-1 2c0 1 0 1 1 2 0 1-1 3-1 5 0 1-1 2-1 3-1 0 0 0 0 1l-1 1-1-2c1-1 1-3 1-5h-1c-1-2 1-2 1-4 0-1-1-3-1-4-1-2 0-4-1-6s-1-4-1-6z" class="E"></path><path d="M767 420h1v-1h-3c0-1 0-2 1-2v-2h2 1c1 1 1 1 3 2l1 1c-2 2-2 2-2 4l-1 1-3-3z" class="I"></path><path d="M764 402l6 3 1 1v1l3-1-1 1-2 2v3l5-1c-1 1-3 1-3 3h1l2 2 1 1c-1 1-2 2-2 3l-2-2-1-1c-2-1-2-1-3-2h-1c-1-1 0-1-2-1h0l-4-6-1-1h0v-3l1 1 2 1h2l1-1-3-3z" class="X"></path><path d="M769 415c1-1 1-1 2-1l2 2h3l1 1c-1 1-2 2-2 3l-2-2-1-1c-2-1-2-1-3-2z" class="Y"></path><path d="M771 407l3-1-1 1-2 2v3h-4c-2-1-2-1-2-3 2-2 3-1 5-2h1z" class="H"></path><path d="M761 465l2 1v2h1c1 3 0 4-1 6 0 1 0 2-1 3v1s0 1 1 1c0 1 1 2 2 2v1h2l1 2h1-3v1 2h-1c-2 1-3 3-5 5h0l-1 2c-3-1-3-1-5 0 0-1 0-2 1-3s1-2 1-3c1-2 1-6 2-8v-1c0-2 0-4 1-6l2-6v-2z" class="n"></path><path d="M763 479c0 1 1 2 2 2v1h2l1 2h1-3v1c-1 0-2 1-4 0h0l-1 1h-1v-1-1l2-2 1-3z" class="K"></path><path d="M763 479c0 1 1 2 2 2v1l-1 2h-1l-1-2 1-3z" class="n"></path><path d="M756 488c2 0 4 0 5 1 0 1-2 1-2 2l1 1-1 2c-3-1-3-1-5 0 0-1 0-2 1-3s1-2 1-3z" class="G"></path><path d="M761 465l2 1v2h1c1 3 0 4-1 6 0 1 0 2-1 3v1s0 1 1 1l-1 3-2 2c0-2-1-3 0-5l-1-1c1-1 1-1 1-3 1 0 2-2 3-2 0-1-2-2-2-2l1-1v-2l-1-1v-2z" class="E"></path><path d="M756 449s1 1 1 2v3l1 1v3c0 1 0 1-1 2v7h1l-1 5-1 5c-2 0-3 0-5 1l-1 2c0 1 0 0-1 2h0c-1-1-1-1-1-2l1-1-2-2 1-3h0l3-15 2-2c1-1 1-1 2-1v-1h0l-1-3c0-1 1-2 2-3z" class="f"></path><defs><linearGradient id="K" x1="752.92" y1="476.718" x2="754.669" y2="457.29" xlink:href="#B"><stop offset="0" stop-color="#585756"></stop><stop offset="1" stop-color="#71716f"></stop></linearGradient></defs><path fill="url(#K)" d="M757 454l1 1v3c0 1 0 1-1 2v7h1l-1 5-1 5c-2 0-3 0-5 1l-1 2c0 1 0 0-1 2h0c-1-1-1-1-1-2l1-1 2-5c2-3 2-4 3-7s1-6 2-9l1-4z"></path><path d="M757 467h1l-1 5c-2-1-2 0-2-2 1 0 1-1 1-2l1-1z" class="Z"></path><path d="M778 451l1 1c-1 2-2 3-3 5 1 1 0 1 0 3-1 1-3 2-3 4l1 2h-1l1 3h-1v3l-2 1-1-2-1 2-1 2-2 1-1 1 1 1c-1 0-1 0-2-1v1c0 1 0 2 1 2v1c-1 0-2-1-2-2-1 0-1-1-1-1v-1c1-1 1-2 1-3 1-2 2-3 1-6h-1v-2l-2-1v-1l2-2v-1l1-2c0-1 1-1 1-2h1c0-2 1-2 2-4l2 1h1c1-1 1-1 2-1l2 2c0-2 2-3 3-4z" class="h"></path><path d="M768 467c-1-2-1-2-1-4h1v1l2 2v1h-2z" class="d"></path><path d="M770 457c1 1 3 1 4 1v1h-3 0-2l-2 1h0c1-2 1-2 3-3z" class="I"></path><path d="M763 462h2v1c0 1-1 1-1 2v3h-1v-2l-2-1v-1l2-2z" class="j"></path><path d="M770 470c0-1 1-2 1-3v-2h2v1l1 3h-1v3l-2 1-1-2v-1z" class="b"></path><path d="M768 467h2v3 1l-1 2h-2l-2-2c0-2 1-3 3-4z" class="M"></path><path d="M768 453l2 1h1v2l-1 1c-2 1-2 1-3 3h0v1c-1 1-1 2-2 2v-1h-2v-1l1-2c0-1 1-1 1-2h1c0-2 1-2 2-4z" class="n"></path><path d="M764 459l3 2c-1 1-1 2-2 2v-1h-2v-1l1-2z" class="p"></path><path d="M777 417c1 0 2 1 2 2h0c2 2 2 4 4 5 1 2 1 3 1 4 1 1 0 1 1 1 0 2 0 3 1 4 0 2-1 5 0 6 0 1-1 2-1 3v1h1 1c-1 1-2 1-3 1v2h-2c0 1-1 1-2 2l2 2c-1 1-1 2-2 2h-1l-1-1-1-1v-1l2-1c-1-1-1-2 0-3 0-2 0-3-1-4l-1-1v-1c-3 0-3-2-6-2h-1l-1-1c-1 0-2-1-3-1l-1-1c1-1 1-1 1-2h-1c0-1 1-2 1-3 0-2 1-4 1-5-1-1-1-1-1-2l1-2 3 3 1-1c0-2 0-2 2-4l2 2c0-1 1-2 2-3z" class="r"></path><path d="M776 430c-1 0-2 0-3-1v-1c1 0 1-1 3-1l2 2v1h-2z" class="V"></path><path d="M778 429l1-1v-1h1l1 1c-1 0-1 0 0 1v2h-1c-1 1-1 2-2 3v-2h-3c0-1 0-1 1-2h2v-1z" class="I"></path><path d="M778 430l1 1c-1 1-1 0-1 1h-3c0-1 0-1 1-2h2z" class="Y"></path><path d="M777 417c1 0 2 1 2 2h0l-2 2c0 1 0 2-1 3h-1l-2 2c-1-1-2-1-3-2v-1l1-1c0-2 0-2 2-4l2 2c0-1 1-2 2-3z" class="b"></path><path d="M773 418l2 2c-1 1-1 2-2 2-1 1-1 1-2 0 0-2 0-2 2-4z" class="C"></path><path d="M778 434v1c0 1 1 1 2 2h2v-2c1-1 1-2 1-3 0-2 1-2 2-3 0 2 0 3 1 4 0 2-1 5 0 6 0 1-1 2-1 3v1h1 1c-1 1-2 1-3 1v2h-2c0 1-1 1-2 2l2 2c-1 1-1 2-2 2h-1l-1-1-1-1v-1l2-1c-1-1-1-2 0-3 0-2 0-3-1-4l-1-1v-1c-3 0-3-2-6-2h-1l-1-1c-1 0-2-1-3-1 0-1 1-2 2-3v-1c2 0 3-1 5-1v2h2 3v2z" class="Z"></path><path d="M769 436v-4l1-1 1 1v2c2 0 2 1 4 1l2 2c2 1 3 1 3 3v1l1 1v1 2h-2c0-2 0-3-1-4l-1-1v-1c-3 0-3-2-6-2h-1l-1-1z" class="S"></path><path d="M777 437c2 1 3 1 3 3v1c-2 0-2-1-3-3v-1z" class="f"></path><path d="M748 398s1 0 2-1l1 1h3c-1-1-2-1-3-2l1-1h3l1 6c1 4 2 7 2 11 2 8 3 18 3 26-2 1-4 1-5 3v1l1 1v3l-1 1h-1 0c-1 0-1-1-2-1l-1-4v-1c1-5 0-12-1-18v3l-1 1-1-8 1 1v-7-1c0-4-1-9-2-14z" class="F"></path><path d="M752 441l3 2v4c-1 0-1-1-2-1l-1-4v-1z" class="R"></path><path d="M754 412c1-1 1 0 1 0 1 1 2 2 2 3 1 1 1 1 1 2h-2c-1-2-1-3-2-5z" class="T"></path><path d="M750 412h0l1 11v3l-1 1-1-8 1 1v-7-1z" class="s"></path><path d="M748 398s1 0 2-1l1 1h3c-1-1-2-1-3-2l1-1h3l1 6c1 4 2 7 2 11h0c-1-1-1-2-2-3v-2-1c0-1 0-2-1-2h-2c-1 2 0 4 0 6h-1 0-1l-1 2h0c0-4-1-9-2-14z" class="M"></path><defs><linearGradient id="L" x1="412.496" y1="395.35" x2="466.57" y2="403.292" xlink:href="#B"><stop offset="0" stop-color="#c3c0c1"></stop><stop offset="1" stop-color="#e8e8e7"></stop></linearGradient></defs><path fill="url(#L)" d="M425 287l3-1c-1 2-1 3-2 4h1c0 1 0 2 1 3h0c2 1 4 2 5 2l5 4 1-1h0c3 3 6 7 8 10l1 1h1c0 3 0 4 2 6v1c-1 1 0 2 1 3 0 1 1 3 1 5 1 1 1 3 2 4 1 0 1 0 2 1 1 0 1 0 1 1s-1 2-1 3v1c-2 5-3 9-2 14v3l3 6c4 4 9 6 14 8-1 1-2 1-4 1l1 1h1c0 1 0 3 1 4v-1h2l1 2h-1c-1 1 0 1-1 2l2 3-1 1 2 1v1c-1 1-2 3-4 4v1c1 1 3 1 4 1l-7 6c-3 3-6 6-8 9-1 2-2 4-2 5l-2-1v3l-1 1 1 2c-2 4-1 8-1 12 0 2-1 4 0 5v1 24 2 1h-1v-5h0l-1 37v1c-1 4 0 10 0 14-2 0-2 0-3-1-1 0-4 1-5 1V347l-1-3v5c0-1 0-3-1-5v-1c0-6-1-11-2-17h0 1c-1-4-3-10-5-13-2-5-7-9-11-12l4-5-4-2h1c-1-2-3-2-5-2l-1-1 1-1c1-2 1-2 3-3z"></path><path d="M441 326h0 1c3 7 3 14 3 21l-1-3v5c0-1 0-3-1-5v-1c0-6-1-11-2-17z" class="y"></path><path d="M425 287l3-1c-1 2-1 3-2 4h1c0 1 0 2 1 3h0c2 1 4 2 5 2l5 4c5 6 11 15 12 23l-1 1h0v-3c-4-10-9-19-19-24l-4-2h1c-1-2-3-2-5-2l-1-1 1-1c1-2 1-2 3-3z" class="E"></path><path d="M425 287l3-1c-1 2-1 3-2 4l-1 1v-2c-2 1-2 1-3 1 1-2 1-2 3-3z" class="j"></path><path d="M439 298h0c3 3 6 7 8 10l1 1h1c0 3 0 4 2 6v1c-1 1 0 2 1 3 0 1 1 3 1 5 1 1 1 3 2 4 1 0 1 0 2 1 1 0 1 0 1 1s-1 2-1 3l-3 3h0c0-2-1-4-2-6l-2-8c-1-8-7-17-12-23l1-1z" class="m"></path><path d="M453 360c1 3 1 6 1 9v30c0 2-1 7 0 8l1 1h1l-1 1 1 2c-2 4-1 8-1 12 0 2-1 4 0 5v1 24 2 1h-1v-5h0c-1-1 0-3 0-4l-1-9v-69-9z" class="k"></path><path d="M449 323l1-1 2 8c1 2 2 4 2 6h0l3-3v1c-2 5-3 9-2 14l-1-3h-1v7 8 9s-1-1-1-2v-1s-1-1 0-1v-1c-1 0-1-1-1-1v-1c-1-2-1-4-1-7-1-3-1-5-1-8l1-1c0-2-2-3-2-5 1-1 0-2 0-3 1-1 1-5 1-6 1-1 0-7 0-9h0z" class="D"></path><path d="M449 323l1-1 2 8c1 2 2 4 2 6h0l3-3v1c-2 5-3 9-2 14l-1-3h-1v7c-1-3 0-6 0-9-1-7-2-14-4-20z" class="J"></path><path d="M453 352v-7h1l1 3v3l3 6c4 4 9 6 14 8-1 1-2 1-4 1l1 1h1c0 1 0 3 1 4v-1h2l1 2h-1c-1 1 0 1-1 2l2 3-1 1 2 1v1c-1 1-2 3-4 4v1c1 1 3 1 4 1l-7 6c-3 3-6 6-8 9-1 2-2 4-2 5l-2-1v3h-1l-1-1c-1-1 0-6 0-8v-30c0-3 0-6-1-9v-8z" class="D"></path><path d="M467 381h1v4l-2-1 1-3z" class="B"></path><path d="M465 373c2 1 2 1 3 3l-2 2c-1-1-1-2-1-3v-1-1z" class="W"></path><path d="M455 391l2 2v1c-1 1-1 0-1 2 1 1 1 0 2 1v3c1-1 1-2 2-3h0v4c-1 2-2 4-2 5l-2-1v-4l-1-10z" class="C"></path><path d="M460 397l1-1c1-1 1-2 2-3h1l-1-2c1-1 1-2 2-2 2-1 2-1 3-3l1-1 1 1-3 4 1 2c-3 3-6 6-8 9v-4z" class="F"></path><path d="M454 369v-4c0 1 0 2 1 4v-5l1-1c0 4-1 27-1 28l1 10v4 3h-1l-1-1c-1-1 0-6 0-8v-30z" class="b"></path><defs><linearGradient id="M" x1="451.129" y1="353.457" x2="472.852" y2="362.103" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2b2927"></stop></linearGradient></defs><path fill="url(#M)" d="M453 352v-7h1l1 3v3l3 6c4 4 9 6 14 8-1 1-2 1-4 1l1 1h1c0 1 0 3 1 4v-1h2l1 2h-1c-1 1 0 1-1 2v-1c-2 0-4-1-6-1l-1 1v1c-1-1-1-1-2-1v2 1l-1-1v-1c-2-1-1-8-3-11l-2-2c-1 0-1 1-1 2l-1 1v5c-1-2-1-3-1-4v4c0-3 0-6-1-9v-8z"></path><path d="M462 374v-3c-1-3-1-5-1-7 2 0 3 1 4 2h3l1 1h1c0 1 0 3 1 4v-1h2l1 2h-1c-1 1 0 1-1 2v-1c-2 0-4-1-6-1l-1 1v1c-1-1-1-1-2-1v2 1l-1-1v-1z" class="F"></path><path d="M465 366h3l1 1-2 2c-1 0-1 0-2-1v-2z" class="w"></path><path d="M467 369c1 0 2 1 3 2h2v1h-2c-1 0-3-1-4-1s-1 1-2 1v-2c0-1-1-1-1-2h2c1 1 1 1 2 1z" class="D"></path><path d="M623 292c-1 2-6 4-8 5-12 7-18 19-21 32-2 9-2 19-2 28v30 109 149l1 79c0 14 3 28 7 41 6 17 16 31 29 44h-2c-4-3-8-8-12-12l-1-2c-4-5-8-10-11-16-6-12-9-26-12-40l-1-11-1-12v-5h-2l-3 3h-1c1 1 1 1 2 1h-1c0 2 0 3-1 4v4c-1-2-1-5-1-7v2h-1c0-3 0-5-1-7v-3c1-2 0-4 0-5v-1-1-4-5l1-1v-2c0-4 0-8-1-12v-5c-1 1-2 1-2 2h-1c-1-1-1-1-3-1v-1l-2 3v-1l-2-4c-1-2-3-4-4-5l-3-3h1l4-4h0l2-3c-2-1-2-2-3-4 1 0 2-4 2-5 1-3 1-5 2-8h1l1 2v-2c2-1 3-2 4-4v-3-7c0-3 0-6-1-9l-1-1 1-2v-1c1 0 2 0 3 1h1v-9c0-1 0-3 1-4l-1-1c1-6 1-11 1-17v-8-2-11l-1-1-1-1c1 0 2-1 3-1h0l1 1v-4l2 2v1 2l1 1h0c6 0 1-14 3-17l1-41V387v-30c0-10-1-20 2-30 2-10 7-20 15-27 3-3 6-4 9-6 3-1 5-2 7-2z" class="J"></path><path d="M586 560h0c6 0 1-14 3-17l1 22-1-1-2-2-2-1 1-1z" class="B"></path><path d="M589 601v-11 30 4l-2 1v-1c-1-1-1-2-2-2l-1-1h-1v-7-2l4 2v1l2 1v-7-8z" class="R"></path><path d="M583 612l4 2v1l2 1v3c-1 1-2 1-3 1v-2h-1v-2l-2-2v-2z" class="M"></path><path d="M583 554l2 2v1 2l1 1-1 1 2 1 2 2 1 1-1 25v11 8 7l-2-1v-1l-4-2v-7c-1 0 0-6-2-8v2l-1-1c1-6 1-11 1-17v-8-2-11l-1-1-1-1c1 0 2-1 3-1h0l1 1v-4z" class="D"></path><path d="M583 587c-1-3 0-6 0-9v1s1 1 1 2 0 1 1 2v2c1 1 1 1 1 2-1 2-1 2-1 3l1 1v1 3 2l1 1c-1 1-1 1-1 2l3 1v8c-1-1-2-2-3-4h-1c1-1 1-1 1-2-2-3 0-5-2-8 0-1 0-2 1-4h0c-1-1 0-2-1-3l-1-1z" class="T"></path><path d="M583 587l1 1c1 1 0 2 1 3h0c-1 2-1 3-1 4 2 3 0 5 2 8 0 1 0 1-1 2h1c1 2 2 3 3 4v7l-2-1v-1l-4-2v-7h0v-4-9-5z" class="q"></path><path d="M579 558c1 0 2-1 3-1h0l1 1v20c0 3-1 6 0 9v5 9 4h0c-1 0 0-6-2-8v2l-1-1c1-6 1-11 1-17v-8-2-11l-1-1-1-1z" class="G"></path><path d="M581 599v-2c2 2 1 8 2 8v7 2 7h1l1 1c1 0 1 1 2 2v1l2-1v-4 7 37l1 37c0 5 0 10-1 15v-5h-2l-3 3h-1c1 1 1 1 2 1h-1c0 2 0 3-1 4v4c-1-2-1-5-1-7v2h-1c0-3 0-5-1-7v-3c1-2 0-4 0-5v-1-1-4-5l1-1v-2c0-4 0-8-1-12v-5c-1 1-2 1-2 2h-1c-1-1-1-1-3-1v-1l-2 3v-1l-2-4c-1-2-3-4-4-5l-3-3h1l4-4h0l2-3c-2-1-2-2-3-4 1 0 2-4 2-5 1-3 1-5 2-8h1l1 2v-2c2-1 3-2 4-4v-3-7c0-3 0-6-1-9l-1-1 1-2v-1c1 0 2 0 3 1h1v-9c0-1 0-3 1-4z" class="d"></path><path d="M585 688c2-1 1-3 4-2v6c-1-1-1-2-2-2l-1 1-1-3z" class="I"></path><path d="M583 636v7 6c1 2 1 2 1 4h1c1-1 1-2 3-3v1 3h-2l-2 3v1l-1 1c2 1 2-1 3 1h0c-1 1-1 1-1 2 1 1 0 1 1 1h0v3l1 2 1 1h0l-2 1c0 3-2 6 0 9-2 0-2 0-4-1 1-5 1-10 1-15 0-1 1-2 1-2l-1-1c-1-2 0-5 0-7 0-5-1-11 0-17z" class="p"></path><defs><linearGradient id="N" x1="583.281" y1="640.638" x2="590.119" y2="636.505" xlink:href="#B"><stop offset="0" stop-color="#525150"></stop><stop offset="1" stop-color="#6c6a68"></stop></linearGradient></defs><path fill="url(#N)" d="M587 625l2-1v-4 7 5c0 5 1 11-3 14l-1 1-1-1c0-1 0-1 1-2v-1h-2v-7-5-3l2-1h0l2-2z"></path><path d="M587 625l2-1v-4 7 5c-1 1-2 2-4 2 0-1 0-1 1-2l-1-1-1-1 2-1-1-2h0l2-2z" class="Z"></path><path d="M581 633v-6c1-1 1-2 1-3-1-2-1-3-1-5v2c1 2 0 5 1 8 0 0 1 1 1 2v5c-1 6 0 12 0 17 0 2-1 5 0 7l1 1s-1 1-1 2c0 5 0 10-1 15v11h0-1c0-4 0-8-1-12v-5-6-9-1l-1 1c-3-1-3-2-5-3l2-2 1-3h0c0-1 1-2 1-3 0-2 1-2 2-4v1c0-1 0-2 1-3v-7z" class="J"></path><path d="M580 642v1 10c0 1 1 3 0 4v-1l-1 1c-3-1-3-2-5-3l2-2 1-3h0c0-1 1-2 1-3 0-2 1-2 2-4z" class="x"></path><path d="M577 649c1 1 1 2 1 4v1c1 0 2 0 2 2l-1 1c-3-1-3-2-5-3l2-2 1-3z" class="Y"></path><path d="M574 654c2 1 2 2 5 3l1-1v1 9 6c-1 1-2 1-2 2h-1c-1-1-1-1-3-1v-1l-2 3v-1l-2-4c-1-2-3-4-4-5l-3-3h1l4-4 2 1 2-2 1-1v-1l1-1z" class="I"></path><path d="M568 662l2-1h1c0 1 0 1 1 1v-2h1v1l2 2h1v1c0 1 0 2 1 3v1c-2 1-3 1-4 1l-1-1 1-1-4-1 1-2-1-1c0 1-1 1-2 1v-1l1-1z" class="l"></path><path d="M574 654c2 1 2 2 5 3l1-1v1 9c-2-1-3-3-4-4s-2-2-3-2l1-1-2-2 1-1v-1l1-1z" class="Q"></path><path d="M573 656h2c1 1 1 4 1 6-1-1-2-2-3-2l1-1-2-2 1-1z" class="l"></path><path d="M568 658l2 1c-1 1-1 2-2 3l-1 1v1c1 0 2 0 2-1l1 1-1 2 4 1-1 1v1h0c1 1 2 2 2 3l-2 3v-1l-2-4c-1-2-3-4-4-5l-3-3h1l4-4z" class="P"></path><path d="M582 689l3-1 1 3 1-1c1 0 1 1 2 2v15 4h-2l-3 3h-1c1 1 1 1 2 1h-1c0 2 0 3-1 4v4c-1-2-1-5-1-7v2h-1c0-3 0-5-1-7v-3c1-2 0-4 0-5v-1-1-4-5l1-1v-2h1 0z" class="Q"></path><path d="M580 697l2-2v6l-2 2v-1-1-4z" class="G"></path><path d="M584 714c0-1-1-2-1-3h1c0-1 0-1 1-2l2 2-3 3z" class="L"></path><path d="M581 689h1v6l-2 2v-5l1-1v-2z" class="c"></path><path d="M585 708c2-1 3-1 4-1v4h-2l-2-2v-1z" class="t"></path><path d="M580 703l2-2v7c1 2 0 6 0 8v2h-1c0-3 0-5-1-7v-3c1-2 0-4 0-5z" class="c"></path><path d="M586 691l1-1c1 0 1 1 2 2v15c-1 0-2 0-4 1v-1h-1-1l2-1v-3l-1-1 3-1c0-1-1 0-1-1-1 0 0-1 0-1 0-1-1-1-1-1l1-2h-1v-2h0l1-3z" class="l"></path><path d="M581 599v-2c2 2 1 8 2 8v7 2 7h1l1 1c1 0 1 1 2 2v1l-2 2h0l-2 1v3c0-1-1-2-1-2-1-3 0-6-1-8v-2c0 2 0 3 1 5 0 1 0 2-1 3v6 7c-1 1-1 2-1 3v-1c-1 2-2 2-2 4 0 1-1 2-1 3h0l-1 3-2 2-1 1v1l-1 1-2 2-2-1h0l2-3c-2-1-2-2-3-4 1 0 2-4 2-5 1-3 1-5 2-8h1l1 2v-2c2-1 3-2 4-4v-3-7c0-3 0-6-1-9l-1-1 1-2v-1c1 0 2 0 3 1h1v-9c0-1 0-3 1-4z" class="u"></path><path d="M583 621h1l1 1c1 0 1 1 2 2v1l-2 2h0l-2 1v-1c0-1 1-2 0-3v-3z" class="V"></path><path d="M576 611c1 0 2 0 3 1h1v4 2 10c0 2 0 4 1 5v7c-1 1-1 2-1 3v-1h0c-1-1-1-2-1-3s0-3 1-4v-3l-1-2h0v2h-1v-2l-1 1v-7c0-3 0-6-1-9l-1-1 1-2v-1z" class="j"></path><path d="M579 619l1-1v10 1c-1-4-1-7-1-10z" class="n"></path><path d="M576 611c1 0 2 0 3 1h1v4 2l-1 1v-1c-1-1-2-2-2-3h-1l-1-1 1-2v-1z" class="K"></path><path d="M579 618c0-2 0-3 1-5v3 2l-1 1v-1z" class="Q"></path><path d="M577 631l1-1v2h1v-2h0l1 2v3c-1 1-1 3-1 4s0 2 1 3h0c-1 2-2 2-2 4 0 1-1 2-1 3h0l-1 3-2 2-1 1v1l-1 1-2 2-2-1h0l2-3c-2-1-2-2-3-4 1 0 2-4 2-5 1-3 1-5 2-8h1l1 2v-2c2-1 3-2 4-4v-3z" class="L"></path><path d="M573 638c2-1 3-2 4-4 0 2-1 7-1 9l-3 1c-1 2 1 1 1 3l-1-1-1 1h-1c1-3 1-5 2-7v-2z" class="R"></path><path d="M577 631l1-1v2h1v-2h0l1 2v3c-1 1-1 3-1 4s0 2 1 3h0c-1 2-2 2-2 4 0 1-1 2-1 3h0l-1 3-2 2-1 1v1l-1 1-2 2-2-1h0l2-3c0-1 1-1 2-2v-1l1-1c0-2 1-2 1-4 1-1 1-3 2-4 0-2 1-7 1-9v-3z" class="c"></path><path d="M570 655c0-1 1-1 2-2-1 1-1 2-1 3h0c1-1 1-1 2-1v1l-1 1-2 2-2-1h0l2-3z" class="J"></path><path d="M284 265l1 2 3 3c1 0 1 1 1 1v1c1 2 1 3 0 4l1 1h1v-2h1c1 1 1 2 1 4 1 3 2 7 4 10l1-1h2l2 1-1 1v2l-1 1c1 2 2 3 4 4l1 1c1 1 3 2 4 3l-3 1-8 4-2 1h-2c-3 1-7 4-9 4h-1-1c-2 1-2 1-4 1v1l-3 1v-1l1-1c-2-1-3 0-5 0l-1 2-1-1h-1c-1 1-1 2-3 3 0 1 0 1-1 1-2 1-1 0-2-1-1 0-2 0-2-1 0 2-1 2 1 4v1c-1 2-6 6-9 8h0c-2 0-3 1-5 2-1-1-3-1-5-1-3 0-7 1-10 1l-7 1v-1l-2-1c-6-1-9-5-12-10h2c1-1 0-3 0-5 0-1-2-3-2-4-2-3-3-6-4-9-2-4-6-13-4-17l1 1c3 5 7 10 14 12 2 0 6 1 8 0h4l1 1v1c1-1 2-1 3 0 2-1 5-2 7-2l3-2c2-2 4-4 5-6l1-1c2-2 3-4 5-5 1-2 1-2 1-4h1c1-1 2-2 2-4l1-2 3-6 1 1 2 1h2l4 5v-1h3 0l1 1c1-1 2-1 2-1 2-2 2-2 3-4l2-2v-2z" class="D"></path><path d="M218 312h2l1 1-1 1h0c-1 0-1 0-1 1h-2 0l1-3z" class="T"></path><path d="M252 319c1 0 1 1 2 1h1 1c1 0 1 0 2 1l-1 1-2 1v-1c-2 0-1 1-3 1v-4zm-29-4l1-1c1 1 2 1 3 2 0 1 0 1-1 1 2 2 2 2 4 2 0 1 0 1-2 2v1l-1-1c-1-1-2-1-2-2l1-1-3-3zm23 4h1c0 1 1 2 0 3h-1 0l-1 1h0 2v1h-2-3c-2 1-1 1-2 1l3-3c1-1 2-1 2-3h1z" class="W"></path><path d="M253 315h1 3 4c0 2-1 2 1 4h-2c-2 0-3-1-4-2h-3 0v-2z" class="r"></path><path d="M221 313l2 2 3 3-1 1c0 1 1 1 2 2h-3c-2-2-4-4-5-6 0-1 0-1 1-1h0l1-1z" class="C"></path><path d="M220 308h1l2 2c0-1 1-1 1-1l6 3h0c2 1 3 1 4 1 0 1 0 1-1 2h1c-1 2-2 3-1 5l-2 1v-1-1-2l2-3h-1c-2 0-4-1-6-1l-1-2h-5c1-1 1-2 0-3z" class="T"></path><path d="M214 314l5 7 1-1-3-5h2c1 2 3 4 5 6h3l1 1c1 1 2 2 4 3h1l1 1 2 2h2 1 2c2-1 2-1 4-1 1 1 2 0 3 0v1c-1 1-3 1-5 1-3 0-7 1-10 1l-7 1v-1l-2-1c-6-1-9-5-12-10h2c1-1 0-3 0-5z" class="R"></path><path d="M224 321h3l1 1c1 1 2 2 4 3h1l-1 1-1 1h-1l-2-2-4-4z" class="U"></path><path d="M214 314l5 7c1 3 3 4 5 6 1 0 2 1 3 2l2-1 1 1h0c-1 0-3 0-4 1l-2-1c-6-1-9-5-12-10h2c1-1 0-3 0-5z" class="W"></path><path d="M235 305c2 0 5 1 7 2h0l1 1 5 1 1 1v2h-1-3c2 1 2 1 3 1 2 1 4 1 5 2v2h-1l-1-1h-4c-2-1-1 0-3 0-1 0-3-2-4-2 1 3 2 5 0 7-1 1-2 2-4 2s-3-1-5-2l2-1c-1-2 0-3 1-5h-1c1-1 1-1 1-2-1 0-2 0-4-1h0v-1c-1 0-1-1-2-1s0 0-1-1h-1v-2c0-1 0-1 1-2 1 0 2 1 3 1h3l2-1h0z" class="f"></path><path d="M234 315c1 0 3 0 4 1s1 1 1 3h-1l-1 2c-1 0-2 0-3-1h-1c-1-2 0-3 1-5z" class="D"></path><path d="M238 319c-1-1-1-1-1-2l1-1c1 1 1 1 1 3h-1z" class="a"></path><path d="M231 307c4 1 7 3 11 5h3 0c2 1 2 1 3 1 2 1 4 1 5 2v2h-1l-1-1c-1-1-2-1-3-1-1-1-2-1-3-1s-2-1-3-1c-2 0-3-1-6-2-2-1-3-1-5-4z" class="Z"></path><path d="M235 305c2 0 5 1 7 2h0l1 1 5 1 1 1v2h-1-3 0-3c-4-2-7-4-11-5l-1-1h3l2-1h0z" class="b"></path><path d="M235 305c2 0 5 1 7 2h0c-1 1-1 2-2 2s-2-1-4-2c-1 0-2 0-3-1l2-1h0z" class="r"></path><path d="M242 307l1 1 5 1 1 1c-2 0-2 0-3 1-2 0-4-1-6-2 1 0 1-1 2-2z" class="Q"></path><path d="M208 301c-2-4-6-13-4-17l1 1c3 5 7 10 14 12 2 0 6 1 8 0h4l1 1v1c1-1 2-1 3 0l1 2h-2l2 1-1 1v2h0l-2 1h-3c-1 0-2-1-3-1-1 1-1 1-1 2v2h1c1 1 0 1 1 1s1 1 2 1v1l-6-3s-1 0-1 1l-2-2h-1c1 1 1 2 0 3h0c-1-1-2-1-2-2l-1 1-1 1 2 1-1 3h0l3 5-1 1-5-7c0-1-2-3-2-4-2-3-3-6-4-9z" class="c"></path><path d="M227 297h4l1 1v1c-2 0-3-1-5-2z" class="s"></path><path d="M225 302c-1 0-1-1-2-2 1-1 5 0 6 0h0l-2 2h-2z" class="R"></path><path d="M222 306c0-1-1-2-1-4 2 0 3 1 4 2 1 0 1 1 2 1-1 1-1 1-1 2v2c-1-1-2-1-4-3z" class="D"></path><path d="M232 299c1-1 2-1 3 0l1 2h-2l2 1-1 1v2h0l-2 1h-3c-1 0-2-1-3-1s-1-1-2-1c1-1 1-1 0-2h2l2-2h0 2l1-1z" class="q"></path><path d="M232 299c1-1 2-1 3 0l1 2h-2c-2 0-3 0-5-1h0 2l1-1z" class="P"></path><path d="M225 302h2l8 3-2 1h-3c-1 0-2-1-3-1s-1-1-2-1c1-1 1-1 0-2z" class="L"></path><path d="M210 300c0-2-1-3-1-5 1 0 2 2 4 3v1l-2-1c1 2 2 2 3 3h1l-1-2h4 0 0c-1 1-2 1-2 2s3 4 5 6c1 0 1 0 1-1 2 2 3 2 4 3h1c1 1 0 1 1 1s1 1 2 1v1l-6-3s-1 0-1 1l-2-2h-1c1 1 1 2 0 3h0c-1-1-2-1-2-2l-1 1-1 1 2 1-1 3h0l3 5-1 1-5-7c0-1-2-3-2-4-2-3-3-6-4-9l2-1z" class="Y"></path><path d="M213 305h1l1 1 1-1 1 1v1 1h3c1 1 1 2 0 3h0c-1-1-2-1-2-2l-1 1-1 1 2 1-1 3c-1-1-2-2-2-3h0c-1-2-1-5-2-7z" class="M"></path><path d="M210 300l1 3s1 1 1 2h1c1 2 1 5 2 7h0c0 1 1 2 2 3h0l3 5-1 1-5-7c0-1-2-3-2-4-2-3-3-6-4-9l2-1z" class="G"></path><path d="M242 297l3-2c2-2 4-4 5-6l3 1c0 2 0 2-1 3l1 1c0-1 1-2 1-2 0 2 0 4 1 7l2 2 1-2c1-1 1-1 1-2l4 2 3 1v1c0 1 0 1-1 2v1c0 1 1 1 2 2h0c1 0 1-1 1-1v-1l1-1c1 1 1 3 1 4 1-1 2-1 3-1l2 1v-1c0 1 1 1 1 1l-4 5-1 2-1-1h-1c-1 1-1 2-3 3 0 1 0 1-1 1-2 1-1 0-2-1-1 0-2 0-2-1h-4-3-1c-1-1-3-1-5-2-1 0-1 0-3-1h3 1v-2l-1-1-5-1-1-1h0c-2-1-5-2-7-2v-2l1-1-2-1h2l-1-2c2-1 5-2 7-2z" class="Z"></path><path d="M251 301l-1-1-2 2-1-1v-2l1-1 2 1c1 1 3 2 3 3-1 1-1 0-2 0v-1zm8-4l4 2 3 1v1c0 1 0 1-1 2v1c0 1 1 1 2 2h0-1c-1 0-2 0-3-1 0-1 0-1-1-1l-1 1-1-1c-2-1-2-2-3-3l1-2c1-1 1-1 1-2z" class="M"></path><path d="M242 297l3-2c2-2 4-4 5-6l3 1c0 2 0 2-1 3l1 1c0-1 1-2 1-2 0 2 0 4 1 7h-1-3-1v-1c2-1 2-1 3-2l-1-2v1c-1 0-1 0-1 1l-3-1c-1 1-1 1-1 3-1 0-2 0-2-1-1 1-1 2-1 2-1 1-1 2-2 3l-2-1c1-2 1-3 2-4z" class="I"></path><path d="M248 295c1-1 2-1 3-2l1 1v1c-1 0-1 0-1 1l-3-1z" class="L"></path><path d="M235 299c2-1 5-2 7-2-1 1-1 2-2 4l2 1c2 1 3 1 5 1l2 1 1-2c1 2 1 2 2 3 1 0 2-1 3 0 1 0 3 1 4 1 2 1 3 0 5 1v1h-3l-11-2-2-1-5 3-1-1h0c-2-1-5-2-7-2v-2l1-1-2-1h2l-1-2z" class="x"></path><g class="C"><path d="M236 302l12 3-5 3-1-1h0c-2-1-5-2-7-2v-2l1-1z"></path><path d="M248 305l2 1 11 2v1c4 0 6-1 9-2 1-1 2-1 3-1l2 1v-1c0 1 1 1 1 1l-4 5-1 2-1-1h-1c-1 1-1 2-3 3 0 1 0 1-1 1-2 1-1 0-2-1-1 0-2 0-2-1h-4-3-1c-1-1-3-1-5-2-1 0-1 0-3-1h3 1v-2l-1-1-5-1 5-3z"></path></g><path d="M248 305l2 1c-1 0-2 0-3 1 1 1 1 1 1 2l-5-1 5-3z" class="F"></path><path d="M249 310c3 0 7 2 10 3l7 3c0 1 0 1-1 1-2 1-1 0-2-1-1 0-2 0-2-1h-4-3-1c-1-1-3-1-5-2-1 0-1 0-3-1h3 1v-2z" class="Y"></path><path d="M270 307c1-1 2-1 3-1l2 1v-1c0 1 1 1 1 1l-4 5-1 2-1-1h-1c-1 1-1 2-3 3l-7-3c0-1 0-2 1-4h1c4 0 6-1 9-2z" class="F"></path><path d="M275 306c0 1 1 1 1 1l-4 5-1 2-1-1v-1c2-3 2-4 5-5v-1z" class="b"></path><path d="M261 309l1 1v1c3 0 5 0 7 2-1 1-1 2-3 3l-7-3c0-1 0-2 1-4h1z" class="H"></path><path d="M284 265l1 2 3 3c1 0 1 1 1 1v1c1 2 1 3 0 4l1 1h1v-2h1c1 1 1 2 1 4 1 3 2 7 4 10l1-1h2l2 1-1 1v2l-1 1c1 2 2 3 4 4l1 1c1 1 3 2 4 3l-3 1-8 4-2 1h-2c-3 1-7 4-9 4h-1-1c-2 1-2 1-4 1v1l-3 1v-1l1-1c-2-1-3 0-5 0l4-5s-1 0-1-1v1l-2-1c-1 0-2 0-3 1 0-1 0-3-1-4l-1 1v1s0 1-1 1h0c-1-1-2-1-2-2v-1c1-1 1-1 1-2v-1l-3-1-4-2c0 1 0 1-1 2l-1 2-2-2c-1-3-1-5-1-7 0 0-1 1-1 2l-1-1c1-1 1-1 1-3l-3-1 1-1c2-2 3-4 5-5 1-2 1-2 1-4h1c1-1 2-2 2-4l1-2 3-6 1 1 2 1h2l4 5v-1h3 0l1 1c1-1 2-1 2-1 2-2 2-2 3-4l2-2v-2z" class="r"></path><path d="M282 269l2-2 1 3-3 3-2 1h-3c1-1 2-1 2-1 2-2 2-2 3-4z" class="P"></path><path d="M282 273l1 1-1 2v2l-1 1v1 2c-1-1-1-2-2-3 0-1-1-2-2-3l-1-3 1 1h3l2-1z" class="Z"></path><path d="M281 298c1 1 1 2 1 4v2 1c0 1-1 1-2 2h0-4s-1 0-1-1h0c2-2 2-2 4-2v-2l2-4z" class="P"></path><path d="M276 307h4v1c1 1 1 3 3 3-2 1-2 1-4 1v1l-3 1v-1l1-1c-2-1-3 0-5 0l4-5z" class="B"></path><path d="M279 313c0-2-1-3-2-4l1-1h2c1 1 1 3 3 3-2 1-2 1-4 1v1z" class="D"></path><path d="M285 267l3 3c1 0 1 1 1 1v1c1 2 1 3 0 4l1 1v1h-1c-1 1-1 0-2 1h-1v-2c0-2-1-2-2-3 1-2 1-5 1-7z" class="S"></path><path d="M288 270c1 0 1 1 1 1v1c1 2 1 3 0 4l1 1v1h-1c-1 1-1 0-2 1 0-2 0-3 1-5v-4z" class="B"></path><path d="M284 274c1 1 2 1 2 3v2h1v2h-1v2l-1 1v3h0v5 2c-1 4 1 8 1 11h-1c-1-2-1-4-1-6 0-1 0-1-1-2v-19c0-1 0-2 1-4z" class="R"></path><path d="M284 274c1 1 2 1 2 3v2h1v2h-1v2l-1 1v3h0c-1-1-1-4 0-6v-1l-2-2c0-1 0-2 1-4z" class="M"></path><path d="M285 294l1 5v1l1 2c0 1 1 1 2 2v-1h1v1h1c2 0 3 0 5 1l1-1 1 2-2 1h-2c-3 1-7 4-9 4v-1c0-1-1-1-1-2-1-2-1-5-1-7v-4c1 1 1 1 1 2 0 2 0 4 1 6h1c0-3-2-7-1-11z" class="Z"></path><path d="M285 294l1 5v1l1 2c0 1 1 1 2 2v-1h1v1h1c2 0 3 0 5 1l-2 1c-1 0-1-1-2-1l-1 2h0l-1-1v-1c-2 1-1 2-2 3l-1-1c0-1-1-1-1-2 0-3-2-7-1-11z" class="M"></path><path d="M277 276c1 1 2 2 2 3 1 1 1 2 2 3v5c1 2 1 5 0 8v3h0l-2 4v2c-2 0-2 0-4 2h0v1l-2-1c-1 0-2 0-3 1 0-1 0-3-1-4l-1 1v1s0 1-1 1h0c-1-1-2-1-2-2v-1c1-1 1-1 1-2v-1h5c1-1 1-2 2-2 1-1 2-3 3-4h-1c2-3 2-6 2-9-1-1-1-3-1-4 0-3 0-3 1-5z" class="F"></path><path d="M277 276c1 1 2 2 2 3 1 1 1 2 2 3v5 2h-1v-3l-1-1c-1 1 0 3 0 4l-2 1v-5c-1-1-1-3-1-4 0-3 0-3 1-5z" class="B"></path><path d="M273 298l1 1c1 0 1 0 1-1l2-2v-1h0l1 1c-1 2-3 3-2 5 0 1 1 2 2 2l1-1v2c-2 0-2 0-4 2h0v1l-2-1c-1 0-2 0-3 1 0-1 0-3-1-4l-1 1v1s0 1-1 1h0c-1-1-2-1-2-2v-1c1-1 1-1 1-2v-1h5c1-1 1-2 2-2z" class="f"></path><path d="M269 303v-1h1c1-1 2-1 4-2l-2 4h1 1l1 2h0v1l-2-1c-1 0-2 0-3 1 0-1 0-3-1-4z" class="q"></path><path d="M291 275h1c1 1 1 2 1 4 1 3 2 7 4 10l1-1h2l2 1-1 1v2l-1 1c1 2 2 3 4 4l1 1c1 1 3 2 4 3l-3 1-8 4-1-2-1 1c-2-1-3-1-5-1h-1v-1h-1v1c-1-1-2-1-2-2l-1-2v-1l-1-5v-2-5h0v-3l1-1v-2h1v-2c1-1 1 0 2-1h1v-1h1v-2z" class="H"></path><path d="M296 292c2 3 3 5 5 7-2 0-2 0-4-1 0-1 0-1-1-2v-4z" class="f"></path><path d="M290 277h1c0 2 0 5-1 7h-1l-1-1-1-2v-2c1-1 1 0 2-1h1v-1z" class="O"></path><path d="M286 283v-2h1l1 2c1 4 2 8 4 12l-1 1h0c-1-3-3-6-4-9l-1-4z" class="S"></path><path d="M292 295c1 2 3 5 5 7 1 0 1 0 2 1l1 1 1-1h2l2-2 1 1-8 4-1-2-2-1c-1-3-3-5-4-7l1-1z" class="k"></path><path d="M291 296h0c1 2 3 4 4 7l2 1-1 1c-2-1-3-1-5-1h-1v-1l-2-3 1-1h0c0-1 0-1 1-2v-1h1z" class="q"></path><path d="M290 303l-2-3 1-1c2 3 3 3 6 4l2 1-1 1c-2-1-3-1-5-1h-1v-1z" class="H"></path><path d="M285 287h0v-3l1-1 1 4c1 3 3 6 4 9h-1v1c-1 1-1 1-1 2h0l-1 1 2 3h-1v1c-1-1-2-1-2-2l-1-2v-1l-1-5v-2-5z" class="F"></path><path d="M291 275h1c1 1 1 2 1 4 1 3 2 7 4 10l1-1h2l2 1-1 1v2l-1 1c1 2 2 3 4 4l1 1c1 1 3 2 4 3l-3 1-1-1-4-2c-2-2-3-4-5-7-2-1-3-5-4-8h0c-1-3 0-6-1-9z" class="Y"></path><path d="M297 289l1-1h2l2 1-1 1v2l-1 1-3-4z" class="C"></path><path d="M264 267l1 1 2 1h2l4 5v-1h3 0l1 3c-1 2-1 2-1 5 0 1 0 3 1 4 0 3 0 6-2 9h1c-1 1-2 3-3 4-1 0-1 1-2 2h-5l-3-1-4-2c0 1 0 1-1 2l-1 2-2-2c-1-3-1-5-1-7 0 0-1 1-1 2l-1-1c1-1 1-1 1-3l-3-1 1-1c2-2 3-4 5-5 1-2 1-2 1-4h1c1-1 2-2 2-4l1-2 3-6z" class="i"></path><path d="M258 279c0 2 1 2 3 4l-4 3-1-3c1-2 1-2 1-4h1z" class="Q"></path><path d="M250 289l1-1c2-2 3-4 5-5l1 3c-2 2-3 3-3 6 0 0-1 1-1 2l-1-1c1-1 1-1 1-3l-3-1z" class="Y"></path><path d="M266 282v3c-2 1-3 2-5 3 0 1-1 2-1 4 0 1 1 1 2 2h0l1 1-1 1-1-2-1 1 3 3v1l-4-2c0-1-1-1-1-2-1-2-1-5-1-6 2-4 6-5 9-7z" class="K"></path><path d="M264 267l1 1 2 1h0c1 1 1 1 1 3-1 0-2 0-3 2v2c-1 1-2 1-4 2l1 1h1v2l-2 2c-2-2-3-2-3-4 1-1 2-2 2-4l1-2 3-6z" class="k"></path><path d="M267 269c1 1 1 1 1 3-1 0-2 0-3 2v2c-1 1-2 1-4 2v-3c1-1 1-2 2-3h2v-2s1 0 2-1z" class="Z"></path><path d="M269 269l4 5v-1h3 0l1 3c-1 2-1 2-1 5l-1-1-2 1h0c-1 1-1 1-1 2h-2 0l-2-2v2 1l-2 1v-3c0-1 0-3 1-5v-1l-1-1-1 1v-2c1-2 2-2 3-2 0-2 0-2-1-3h0 2z" class="E"></path><path d="M273 274v-1h3 0l1 3c-1 2-1 2-1 5l-1-1c0-2-1-4-2-6z" class="U"></path><path d="M265 276v-2c1-2 2-2 3-2l3 6c-1-1-2-2-4-2l-1-1-1 1z" class="R"></path><path d="M267 276c2 0 3 1 4 2l1 2 1 1h0c-1 1-1 1-1 2h-2 0l-2-2v2 1l-2 1v-3c0-1 0-3 1-5v-1z" class="f"></path><path d="M269 280l3 1v-1l1 1h0c-1 1-1 1-1 2h-2 0l-1-3z" class="V"></path><path d="M267 277c0 2 0 2 2 3l1 3-2-2v2 1l-2 1v-3c0-1 0-3 1-5z" class="R"></path><path d="M275 280l1 1c0 1 0 3 1 4 0 3 0 6-2 9h1c-1 1-2 3-3 4-1 0-1 1-2 2h-5l-3-1v-1l-3-3 1-1 1 2 1-1-1-1h0c-1-1-2-1-2-2 0-2 1-3 1-4 2-1 3-2 5-3l2-1v-1-2l2 2h0 2c0-1 0-1 1-2h0l2-1z" class="U"></path><path d="M262 294h0c-1-1-2-1-2-2 0-2 1-3 1-4l1 1c2 1 0 0 1 0l3 3v2h-2c-1-1-1 0-2 0z" class="R"></path><path d="M268 284v-1-2l2 2h0c1 2 1 2 0 3v1l2 1v1 1h-5l-1-1c2-1 2-2 2-4v-1z" class="C"></path><path d="M262 294c1 0 1-1 2 0h2l3 1 1-1 2 1v-1h0l1 1 3-1c-1 1-2 3-3 4-1 0-1 1-2 2h-5l-3-1v-1l-3-3 1-1 1 2 1-1-1-1z" class="m"></path><path d="M272 294h0l1 1 3-1c-1 1-2 3-3 4-1 0-1 1-2 2h-5l-3-1v-1h1c3 1 5-1 8-3v-1z" class="Y"></path><path d="M275 280l1 1c0 1 0 3 1 4 0 3 0 6-2 9h1l-3 1-1-1h0l-1-1h0c0-2 0-2 1-3v-1-1l-2-1v-1c1-1 1-1 0-3h2c0-1 0-1 1-2h0l2-1z" class="n"></path><path d="M270 283h2c0-1 0-1 1-2l1 5c-1 0-2 1-2 1l-2-1c1-1 1-1 0-3z" class="S"></path><path d="M270 286l2 1s1-1 2-1c0 3 1 3-1 6 0 1 0 1-1 2h0l-1-1h0c0-2 0-2 1-3v-1-1l-2-1v-1z" class="f"></path><path d="M820 294l4-4c2 1 2 1 3 2 0 1-1 1-1 2l-1 2h3c0 1-1 3-1 3 1 2 1 2 0 4-1 3-2 5-5 7-1 3-3 4-5 5l1 1 5-3c0 2-1 3-3 5 0 2-1 2-1 4l-1 1-4 8 1 1c4 1 10 0 14-1h2l1 1c1 0 2 1 4 1l-3 1h1l-1 2 3 1 1-1c0 2-2 4-2 5l-1 2c-2-1-2-1-4-1l-1 1h-1c-1 2 1 3-2 4h-1v-2-1h0-1c-1 1-2 1-3 1h0-2c0 2 1 3-1 4l-1-1c0 1 0 0-1 1 0 1 0 1-1 2 1 0 1 1 2 1v1l-1 2h1l-1 2c1 1 1 1 1 3h-1c0 2-1 3-2 4h-4c-2-1-3-2-5-2 1 1 2 2 2 3-1 1-1 2-2 3h-1 0-1l1 1c-1 0-3 0-4-1h-1c1-1 1-1 1-2h-2v-2c-1 1-1 2-1 4l-1-5v-4l-1-1-2-1-1-2-1-1-1-1c0-1-1-1-1-2-2 0-2 0-3-1v-1-2c0-1 1-1 1-2v-1l-4-4h-1l-3-1-2-1-2 2-2-1v-2l-2-1-3-1-11-5-1-1-6-2-5-3c1-1 2-2 4-3h1v-1-1l2-2v-1-1c0-1-1-2-1-2v-1-1l5-5v-1c1 0 1 0 2-1h1l1-2 4-3 1 1 2 2v1h1l3-1c3-1 5 0 7-1 1 0 4 0 5 1l8 1v-1l1-2h2l3-1 2-2h12c3 0 5-1 7-3h2z" class="B"></path><path d="M782 325c3-1 5-2 6-4h2c-2 2-4 3-6 4l-1 1c0 1 0 1 1 1l1 1c2 0 3 0 4 1v1c-4 0-7-2-11-2-2-1-4 0-6-1l1-1h1 3 3v-1h2z" class="V"></path><path d="M796 339c-2-1-4-3-6-3-1 0-3 0-4-1h-1c0 1 1 2 1 4h0l-3-1c1-1 1-2 1-2l-3-3-4-2 1-1 2 1c2 0 5 2 7 2h5v2c1 1 2 0 4 1 0 1 1 1 2 2l2 2-1 1h-2v-2h-1z" class="W"></path><path d="M756 329h0c1-1 3-1 5-1 2 2 3 3 6 3 1 1 2 1 3 2 1-1 1-1 2-1l1 2 2-1 3 2c1 1 1 1 2 3l3 2h-1l-3-1-2-1-2 2-2-1v-2l-2-1-3-1-11-5-1-1z" class="F"></path><path d="M773 337l2-1c1 0 3 1 5 2l3 2h-1l-3-1-2-1-2 2-2-1v-2z" class="c"></path><path d="M792 333c0-1 1-1 1-2l7 1 3 1 1 1c1 1 1 0 2 0s2 1 2 1v1c-1 1-2 2-2 3h-1c-2 0-2 1-3 3l-2 2c0-1-1-2-1-3l1-1-2-2c-1-1-2-1-2-2-2-1-3 0-4-1v-2z" class="D"></path><path d="M800 340c0-1 1 0 1 0l3-3h1v2c-2 0-2 1-3 3l-2 2c0-1-1-2-1-3l1-1zm-12 5h3c0-2-2-3-3-5 0-1 0-2 1-3 1 0 1 1 2 1 2 1 2 2 3 4h0v2c0 1 1 2 2 2 0 1 0 1 1 2s1 2 1 4l-2 1-1 1c1 1 1 1 0 2v2l-2-1-1-2-1-1-1-1c0-1-1-1-1-2-2 0-2 0-3-1v-1-2c0-1 1-1 1-2h1z" class="C"></path><path d="M787 345h1l4 7c0 1 1 1 2 2h1c1 1 1 1 0 2v2l-2-1-1-2-1-1-1-1c0-1-1-1-1-2-2 0-2 0-3-1v-1-2c0-1 1-1 1-2z" class="X"></path><path d="M786 349h3v2c-2 0-2 0-3-1v-1z" class="J"></path><path d="M807 339l6-1v1l-2 1v1 1c-1 1-1 1-1 2v1l1 1v1l-2-1-1 2-4 1-1 1v-2h-3c-1-1-2 0-3 0-1-1-1-1-1-2-1 0-2-1-2-2v-2h0c0-1 1-1 1-1l1-1v-1h1v2h2c0 1 1 2 1 3l2-2c1-2 1-3 3-3h1 1z" class="T"></path><path d="M796 339h1v2h2c0 1 1 2 1 3h0l-1 2c-1 0-2-1-2-1l-3-3h0c0-1 1-1 1-1l1-1v-1z" class="D"></path><path d="M797 341h2c0 1 1 2 1 3h0-1c-1 0-1 0-2-1v-2z" class="N"></path><g class="X"><path d="M807 339l6-1v1l-2 1v1 1c-1 1-1 1-1 2v1l1 1v1l-2-1-1 2-4 1-1 1v-2l2-2c2-1 3-3 3-5l-1-2z"></path><path d="M797 348c1 0 2-1 3 0h3v2l1-1 4-1c1 2 2 3 1 4l-1 1c1 0 1 1 2 1l1 2c2 0 3 1 4 3l1 1v-1 1c0 2-1 3-2 4h-4c-2-1-3-2-5-2 1 1 2 2 2 3-1 1-1 2-2 3h-1 0-1l1 1c-1 0-3 0-4-1h-1c1-1 1-1 1-2h-2v-2c-1 1-1 2-1 4l-1-5v-4l-1-1v-2c1-1 1-1 0-2l1-1 2-1c0-2 0-3-1-4z"></path></g><path d="M800 348h3v2l1-1 4-1c1 2 2 3 1 4l-1 1c1 0 1 1 2 1l-3 2-1-1h-1c-2-1-3 0-4-1v-2-1c-1 0-1 0-2-1l1-2z" class="J"></path><path d="M803 350l1-1 4-1c1 2 2 3 1 4l-1 1c1 0 1 1 2 1l-3 2-1-1v-4h0l-2 1h1l-1 1c-1 0-1 0-1-1v-2z" class="x"></path><path d="M805 355h1l1 1 3-2 1 2c2 0 3 1 4 3l1 1v-1 1c0 2-1 3-2 4h-4c-2-1-3-2-5-2h0c-1-2-1-2-1-4h1l-2-2 2-1z" class="j"></path><path d="M816 360c-1 1-2 2-4 3-2-1-2-1-3-3 0-2 1-3 2-4 2 0 3 1 4 3l1 1z" class="B"></path><path d="M796 353l1 1 2-1c3 3 4 10 4 15l1 1c-1 0-3 0-4-1h-1c1-1 1-1 1-2h-2v-2c-1 1-1 2-1 4l-1-5v-4l-1-1v-2c1-1 1-1 0-2l1-1z" class="S"></path><path d="M829 331h2l1 1c1 0 2 1 4 1l-3 1h1l-1 2 3 1 1-1c0 2-2 4-2 5l-1 2c-2-1-2-1-4-1l-1 1h-1c-1 2 1 3-2 4h-1v-2-1h0-1c-1 1-2 1-3 1h0-2c0 2 1 3-1 4l-1-1c0 1 0 0-1 1 0 1 0 1-1 2 1 0 1 1 2 1v1l-1 2h1l-1 2c1 1 1 1 1 3h-1v-1 1l-1-1c-1-2-2-3-4-3l-1-2c-1 0-1-1-2-1l1-1c1-1 0-2-1-4l1-2 2 1v-1l-1-1v-1c0-1 0-1 1-2v-1-1l2-1v-1l-6 1h-1c0-1 1-2 2-3v-1s-1-1-2-1-1 1-2 0c1-1 3-2 5-2h1c1-1 1-1 2-1 1 1 1 1 2 1h1c4 1 10 0 14-1z" class="t"></path><path d="M827 335l6-1h1l-1 2c-1 0-2 0-3 1 0 1-1 1-1 2l-2-2h0v-2h0z" class="d"></path><path d="M819 337c2 0 5-1 8-2v2c-2 1-2 1-3 2l-1 1c-1 0-1 0-2 1h-1v-2-1h-1-1l1-1z" class="Y"></path><path d="M836 337l1-1c0 2-2 4-2 5l-1 2c-2-1-2-1-4-1l-1 1h-1c-1 2 1 3-2 4h-1v-2-1h1c0-1-1-2-1-2l1-1 1 1c3 0 7-4 9-5z" class="U"></path><path d="M813 338h0c2 0 3-1 5 0h1 1v1 2c-2 0-2 0-4 1l-1-2v1h0v2h-1v-1l-1 1c1 0 1 0 1 1h-1-3c0-1 0-1 1-2v-1-1l2-1v-1z" class="r"></path><path d="M816 357c-1-1-1-2-2-2-1-1-2-1-3-3 2-1 1-1 1-3 0 0 1-1 1-2 2-1 3-2 4-3 2 0 4 0 6-2h0l2 2h-1c-1 1-2 1-3 1h0-2c0 2 1 3-1 4l-1-1c0 1 0 0-1 1 0 1 0 1-1 2 1 0 1 1 2 1v1l-1 2h1l-1 2z" class="U"></path><path d="M829 331h2l1 1c1 0 2 1 4 1l-3 1-6 1h0c-3 1-6 2-8 2l-1 1c-2-1-3 0-5 0h0l-6 1h-1c0-1 1-2 2-3v-1s-1-1-2-1-1 1-2 0c1-1 3-2 5-2h1c1-1 1-1 2-1 1 1 1 1 2 1h1c4 1 10 0 14-1z" class="H"></path><path d="M809 334h1c1 0 2 1 3 2v1c-3 0-3 0-5-2l1-1z" class="O"></path><path d="M819 337l1-2c3-1 5-1 7 0h0c-3 1-6 2-8 2z" class="B"></path><path d="M760 302l4-3 1 1 2 2v1h1l3-1c3-1 5 0 7-1 1 0 4 0 5 1l-2 1c0 1 1 1 2 2l3 1 1 1 6 2v2h-1l-1 1v1h-1v4l-1 1-1 3c-1 2-3 3-6 4h-2v1h-3-3-1l-1 1-1 1-6-2-1 2c-2 0-1-1-3 0-2 0-4 0-5 1h0l-6-2-5-3c1-1 2-2 4-3h1v-1-1l2-2v-1-1c0-1-1-2-1-2v-1-1l5-5v-1c1 0 1 0 2-1h1l1-2z" class="q"></path><path d="M765 316l2 1c1 0 2 0 2 1h0l-2 1-2-2v-1z" class="F"></path><path d="M769 318l1 1c0 1 0 1-1 2h-4v-1h-1-1l2-3 2 2 2-1h0z" class="B"></path><path d="M764 312h1 1c1 0 2 0 3-1 1 0 3 0 4 1h0-3v3h0l-1-1c-2 0-3 1-4 2h-3c1-2 1-3 2-4z" class="V"></path><path d="M760 314c2-1 2-2 4-2-1 1-1 2-2 4v2l-1 2v3c-2-1-3-3-4-4 1-1 1-2 2-3 1 0 1-1 1-2z" class="k"></path><path d="M773 326c-1-1-2-1-3-1s0 0-1-1c0-1 0-1 1-3 1 1 1 1 2 1 0-1 1-2 1-4h0c2 0 2 1 4 2-2 1-2 2-2 4h1 0l2 1c1-1 1-1 2-1s2 1 2 1h-2v1h-3-3-1z" class="F"></path><path d="M752 317h1 2l1-1 3-3v1h1c0 1 0 2-1 2-1 1-1 2-2 3 1 1 2 3 4 4v1l-1 1-5-2-5-2v-1-1l2-2z" class="K"></path><path d="M752 317h1 2l1-1 3-3v1l-2 2v1c-1 1-3 0-3 3 0 1 2 1 1 3l-5-2v-1-1l2-2z" class="J"></path><path d="M750 321l5 2 5 2c1 0 1 0 2 1h3l-1 2c-2 0-1-1-3 0-2 0-4 0-5 1h0l-6-2-5-3c1-1 2-2 4-3h1z" class="C"></path><path d="M783 315c1 0 1 1 3 0h0l4 2-1 1-1 3c-1 2-3 3-6 4 0 0-1-1-2-1s-1 0-2 1l-2-1h0-1c0-2 0-3 2-4l1-2 1 1c1-1 2-3 4-4z" class="O"></path><path d="M786 315l4 2-1 1h-4l1-3h0z" class="a"></path><path d="M777 320l1-2 1 1c1 0 2 0 4 1l-7 2v2h0-1c0-2 0-3 2-4z" class="b"></path><path d="M783 315c1 0 1 1 3 0l-1 3-2 2c-2-1-3-1-4-1 1-1 2-3 4-4z" class="Y"></path><path d="M781 305c2 1 3 2 5 3l1-1 6 2v2h-1l-1 1v1h-1v4l-4-2h0c-2 1-2 0-3 0l-1-1c-1-1-1-1-2-1h-1-2l3-2v-3h-5v-1h3c1-1 2-1 3-2z" class="C"></path><path d="M787 307l6 2v2h-1l-1 1v1h-1c-1-2-2-4-4-5l1-1z" class="K"></path><path d="M780 308c2 1 5 2 5 4 1 1 1 2 1 3h0c-2 1-2 0-3 0l-1-1c-1-1-1-1-2-1h-1-2l3-2v-3z" class="E"></path><path d="M758 311c6-4 10-4 17-4v1h5v3l-3 2v-1l-2 1c-1 0 0 0-1-1h-1 0c-1-1-3-1-4-1-1 1-2 1-3 1h-1-1c-2 0-2 1-4 2h-1v-1l-3 3-1 1h-2-1v-1-1c0-1-1-2-1-2h1l3-3c1 0 2 1 3 1z" class="J"></path><path d="M755 310c1 0 2 1 3 1l-6 5v-1c0-1-1-2-1-2h1l3-3z" class="C"></path><path d="M775 308h5v3l-3 2v-1l-2 1c-1 0 0 0-1-1h-1 0l1-1c1-1 1-2 2-3h-1z" class="c"></path><path d="M760 302l4-3 1 1 2 2v1h1l3-1c3-1 5 0 7-1 1 0 4 0 5 1l-2 1c0 1 1 1 2 2l3 1 1 1-1 1c-2-1-3-2-5-3-1 1-2 1-3 2h-3c-7 0-11 0-17 4-1 0-2-1-3-1l-3 3h-1v-1-1l5-5v-1c1 0 1 0 2-1h1l1-2z" class="B"></path><path d="M778 301c1 0 4 0 5 1l-2 1c0 1 1 1 2 2l3 1 1 1-1 1c-2-1-3-2-5-3-1 0-3 0-4-1h0-2-1s-1 1-2 1 0 0-1-1v-1-1c3-1 5 0 7-1z" class="F"></path><path d="M760 302l4-3 1 1 2 2v1h-1l-1 2c-2 0-2-1-4 1l-1 1-1-1-2 2c0 1-1 1-2 2l-3 3h-1v-1-1l5-5v-1c1 0 1 0 2-1h1l1-2z" class="o"></path><path d="M820 294l4-4c2 1 2 1 3 2 0 1-1 1-1 2l-1 2h3c0 1-1 3-1 3 1 2 1 2 0 4-1 3-2 5-5 7-1 3-3 4-5 5l1 1 5-3c0 2-1 3-3 5 0 2-1 2-1 4l-1 1-4 8 1 1h-1c-1 0-1 0-2-1-1 0-1 0-2 1h-1c-2 0-4 1-5 2l-1-1-3-1-7-1h0l-2-1h-2v-1c-1-1-2-1-4-1l-1-1c-1 0-1 0-1-1l1-1c2-1 4-2 6-4h-2l1-3 1-1v-4h1v-1l1-1h1v-2l-6-2-1-1-3-1c-1-1-2-1-2-2l2-1 8 1v-1l1-2h2l3-1 2-2h12c3 0 5-1 7-3h2z" class="w"></path><path d="M784 327c1-1 2-1 4-1l-3 2-1-1z" class="S"></path><path d="M806 318l3 2c-1 0-2 0-3 1v1c-1 0-1-1-2-1v-1l2-2z" class="B"></path><path d="M791 326l3-3h1v1c-1 1-1 1-1 3l-1 1-2-2z" class="U"></path><path d="M788 326v-2 1 1h3l2 2c-1 0-3 1-4 1-1-1-2-1-4-1l3-2z" class="R"></path><path d="M814 311l3-2-1 1c0 2-2 3-4 5l-1 1h1c1 0 2 0 3-1 1 0 1-1 1-1h1c2-1 4-2 5-4-1 3-3 4-5 5-1 1-3 2-4 2h-1c-1 0-2 2-3 3l-3-2c1-1 3-2 4-3l4-4z" class="T"></path><path d="M827 299c1 2 1 2 0 4-1 3-2 5-5 7-1 2-3 3-5 4h-1s0 1-1 1c-1 1-2 1-3 1h-1l1-1c2-2 4-3 4-5l1-1 2-2c3-2 5-5 8-8z" class="H"></path><path d="M811 305v1h1c1-1 1-2 1-3v-1l2 4 1 1c1-1 1-1 2-1h0l1 1-2 2-3 2h-4c-1 1-3 2-5 2h0c-1 1-1 1-3 1v-1-1-1l-1-2h1c3-1 6-1 9-4z" class="b"></path><path d="M801 309h1l2 2v1l1 1c-1 1-1 1-3 1v-1-1-1l-1-2z" class="I"></path><path d="M818 306l1 1-2 2-3 2h-4c-1 1-3 2-5 2 1-1 2-3 4-4 1 0 2 1 4 0l-1-1 1-1h1l1-1 1 1c1-1 1-1 2-1h0z" class="h"></path><path d="M793 309l4 1c1 0 1-1 2-1h1 1l1 2v1 1 1l-1 1c-3 0-7 3-10 5l-1 1h-2l1-3 1-1v-4h1v-1l1-1h1v-2z" class="Y"></path><path d="M800 309h1l1 2v1 1 1l-1 1h-2c-1-1-1-1-1-2l1-1v-1-1l1-1z" class="d"></path><path d="M791 313h1c1 0 0 0 1-1h3c-1 1-2 1-3 2v2h0c-1 2-2 2-2 4l-1 1h-2l1-3 1-1v-4h1z" class="E"></path><path d="M820 294l4-4c2 1 2 1 3 2 0 1-1 1-1 2l-1 2h3c0 1-1 3-1 3-3 3-5 6-8 8l-1-1h0c-1 0-1 0-2 1l-1-1-2-4c1 0 2-1 3-1 1-1 1-2 2-2 1-1 2-2 2-3h1c-1-1-1-2-1-2z" class="p"></path><path d="M821 296h2l2 1-1 1h-1c0 1 0 1 1 2-1 0-1 0-2 1-2-1-3-2-4-2 1-1 2-2 2-3h1z" class="J"></path><path d="M820 294l4-4c2 1 2 1 3 2 0 1-1 1-1 2l-1-1c-1 0-2 1-3 2l1 1h-2c-1-1-1-2-1-2z" class="G"></path><path d="M816 301c1 0 3 0 4 1 0 1-1 2-2 4h0c-1 0-1 0-2 1l-1-1-2-4c1 0 2-1 3-1z" class="M"></path><path d="M818 316l5-3c0 2-1 3-3 5 0 2-1 2-1 4l-1 1-4 8 1 1h-1c-1 0-1 0-2-1-1 0-1 0-2 1h-1c-2 0-4 1-5 2l-1-1-3-1-7-1h0l-2-1h-2v-1c1 0 3-1 4-1l1-1c9-2 16-5 24-11z" class="E"></path><path d="M816 320c1-1 2-2 4-2 0 2-1 2-1 4l-1 1v-1l-6 6-2-2c1-1 5-4 6-5v-1z" class="X"></path><path d="M812 328l6-6v1l-4 8 1 1h-1c-1 0-1 0-2-1-1 0-1 0-2 1h-1c-2 0-4 1-5 2l-1-1-3-1h5v-1c3 0 5-1 7-3z" class="w"></path><path d="M818 316l5-3c0 2-1 3-3 5-2 0-3 1-4 2l-6 4c-2 1-4 2-7 3l-1 1c-1 1-3 1-4 2h-4-2-1-2v-1c1 0 3-1 4-1l1-1c9-2 16-5 24-11z" class="u"></path><path d="M820 294s0 1 1 2h-1c0 1-1 2-2 3-1 0-1 1-2 2-1 0-2 1-3 1v1c0 1 0 2-1 3h-1v-1c-3 3-6 3-9 4h-1-1-1c-1 0-1 1-2 1l-4-1-6-2-1-1-3-1c-1-1-2-1-2-2l2-1 8 1v-1l1-2h2l3-1 2-2h12c3 0 5-1 7-3h2z" class="j"></path><path d="M791 303v-1l1-2h2v2h0c1 0 2 1 2 1l2-2s1 0 1 1v1h1 1l1-2h0c1 1 2 0 4 1 0 0 2 0 3 1-6 1-13 1-18 0z" class="p"></path><path d="M806 302c0-1 0-1 1-1l1 1v-1l1 1c2-1 3-3 5-4 2 0 4-1 6-2 0 1-1 2-2 3-1 0-1 1-2 2-1 0-2 1-3 1v1c0 1 0 2-1 3h-1v-1l-2-2c-1-1-3-1-3-1z" class="X"></path><path d="M783 302l8 1c5 1 12 1 18 0l2 2c-3 3-6 3-9 4h-1-1-1c-1 0-1 1-2 1l-4-1-6-2-1-1-3-1c-1-1-2-1-2-2l2-1z" class="C"></path><path d="M587 711h2v5l1 12 1 11c3 14 6 28 12 40 3 6 7 11 11 16h-3c-2 0-3 0-5 1 2 1 3 2 4 3v1-1h-2l-1 1 2 2 3 3c1 1 2 2 2 3-1 1-2 1-3 1-1 1-3 3-3 4-1-1-1 0-1-1l-2 1v1c1 0 1 1 1 2h-1l-4 1v-1c-1 0-1-1-3 0-1-1-2-1-3 0h-4c-1 0-1 1-3 1h-1l-2-2-1-1c-2 1-2 0-4 0l-1-1c-1 0-1 1-2 1l-1-1h-2v2c-1-1-2-1-3-1-2-1-3 0-4-1-1 0 0-1-2-1-2-1-3-1-4-2h-3l-1-1-1-4 1 1v-4l-3-3h-1v-1l-2-4c-2 1-2 1-2 3h0l-2-1c1-1 1-1 1-2l-1-1c0-1 0-1 1-2-1 0-1 0-2-2 1 1 0 1 2 1v-2-1-1h1c1 0 2-1 2-2h2l1 2c0-2 1-2 2-3v-1c-2-2-2-4-3-6l-1-2 1-2c0-5 1-7 3-11l-1-2c0-1 0-1 1-2l1 1 2-2-1-1c1-1 2-1 4-1 1 0 1-1 2-1l-2-3h3l1 1c2-1 3-2 5-3h1l4-4v-2c1-1 2-3 2-4 1 1 2 2 2 3v1h1l1 1v2l1-1v-4l1-1c1-5 0-10-1-16v-4c1-1 1-2 1-4h1c-1 0-1 0-2-1h1l3-3z" class="d"></path><path d="M562 776h1c1 1 2 1 3 1h1l-1 2-1-1-1 1c-1 0-1 0-2-1v-2z" class="I"></path><path d="M566 787l2 1c1 0 1-1 2-1h2c1 0 2-2 3-1v1l-1 2c-1 0-3 1-4 2h-4l-2-1v-1l2-2z" class="G"></path><path d="M574 789l2-1c0-2 1-2 2-4h1v3s-2 1-2 2l-3 3c-2 1-3 1-5 2l-1-1 2-2c1-1 3-2 4-2z" class="I"></path><path d="M577 789l1 1c-1 1-1 2-2 3v1 2l2 2h0c-1 1-1 1-1 2l-1 3h-2c-1-1-2-2-3-2l-2-1 1-1c0-2 0-2 1-4l1-1c1 0 1-1 2-2l3-3z" class="X"></path><path d="M573 796h1c1 1 2 1 2 3l-2 1h0c-2-1-2-1-2-2l1-2z" class="m"></path><path d="M571 775h3c1 1 1 2 2 3 0 1 0 2-1 2-1 1-1 1-1 2v1c0 1 0 2-1 3-1-1-1-1-2-1l-1 1h-1-2c-1-1-1-2-3-3 0-1 0-1 1-2v1-1h0l-1-1h2v-1l1-2 1-1c1-1 1-1 3-1z" class="h"></path><path d="M569 782v-1c0-1 0-2 1-3v1c0 1 1 1 1 1-1 2-1 2-2 2h0z" class="L"></path><path d="M571 775c-1 1-1 2-1 3h0c-1 1-1 2-1 3v1l-1 1h-2v-3-1l1-2 1-1c1-1 1-1 3-1z" class="k"></path><path d="M574 768l6-2-2 2 1 1c1 2 3 2 4 4l2 2h0c1 0 1 0 1 1-1 0-2 1-3 1-1 1-2 2-3 4h0l-1 1h-1c1-1 1-2 1-3-1 0-1 0-2-1h-1c-1-1-1-2-2-3 0-1 0-1-1-2 1-1 1-1 1-2h0l1-1v-2h-4 3z" class="G"></path><path d="M574 768l6-2-2 2v2c-1 1-1 0-2 1 0-1-1-2-1-3h-4 3z" class="j"></path><path d="M578 772l1 1c1 1 1 1 1 3l-1 1h-1c-1 0-2-1-2-2 0-2 0-2 2-3z" class="m"></path><path d="M561 760c2 1 2 2 3 3s1 2 2 3v1s0 1 1 1v1c1 1 1 0 2 1s2 2 3 2l2-1c0 1 0 1-1 2 1 1 1 1 1 2h-3c-2 0-2 0-3 1l-1 1h-1c-1 0-2 0-3-1h-1l-2 1 1 2v1l-2-2h-2c0-1 0-1-1-2h0l1-2-1-1v-1l-1-1 2-1-1-2c1-1 2-2 2-3l2-2c0-1 1-2 1-3z" class="K"></path><path d="M560 763l1 1h2c-1 1-1 2-3 2h-1l-1-1 2-2z" class="c"></path><path d="M561 760c2 1 2 2 3 3l-1 1h-2l-1-1c0-1 1-2 1-3z" class="j"></path><path d="M558 765l1 1-1 1h1 1c1 1 0 2 0 3v6 1l1 2v1l-2-2h-2c0-1 0-1-1-2h0l1-2-1-1v-1l-1-1 2-1-1-2c1-1 2-2 2-3z" class="G"></path><path d="M556 773l1-1c1 1 1 1 2 1v-1c0-1-1-1-1-2 1-1 1 0 2 0v6 1l1 2v1l-2-2h-2c0-1 0-1-1-2h0l1-2-1-1z" class="E"></path><path d="M568 776c0-1 0-1-1-2-2 0-2 1-4 1h-1c-1 0-1 0-2-1 1-1 1-1 2-1v-2s1-1 2-1c0-1-1-1-1-2h1l1 1 2-1v1c1 1 1 0 2 1s2 2 3 2l2-1c0 1 0 1-1 2 1 1 1 1 1 2h-3c-2 0-2 0-3 1z" class="d"></path><path d="M557 778h2l2 2v1l-1 1 2 2c2 1 2 2 4 3l-2 2v1l2 1h4l-2 2 1 1c-1 1-3 1-5 1-2-1-3-1-5-2l-4-1h0c-1 1-1 1-2 1l-1 1h-1c-2 1-2 1-2 3h0l-2-1c1-1 1-1 1-2l-1-1c0-1 0-1 1-2-1 0-1 0-2-2 1 1 0 1 2 1v-2-1-1h1c1 0 2-1 2-2h2l1 2c0-2 1-2 2-3v-1c1 0 1-1 2-1 0-2-1-2-1-3z" class="L"></path><path d="M566 791h4l-2 2h-4v-1h2v-1z" class="Q"></path><path d="M551 784h2l1 2c2 1 4 2 6 4v2h-1v-1c-1 0-1 0-2-1l-2 2h0c-3-2-4-3-6-6 1 0 2-1 2-2z" class="k"></path><path d="M548 786h1c2 3 3 4 6 6-1 1-1 1-2 1l-1 1h-1c-2 1-2 1-2 3h0l-2-1c1-1 1-1 1-2l-1-1c0-1 0-1 1-2-1 0-1 0-2-2 1 1 0 1 2 1v-2-1-1z" class="J"></path><path d="M557 778h2l2 2v1l-1 1 2 2c2 1 2 2 4 3l-2 2v1 1c-2 0-2 0-3-1h-1c-2-2-4-3-6-4 0-2 1-2 2-3v-1c1 0 1-1 2-1 0-2-1-2-1-3z" class="R"></path><path d="M562 784c2 1 2 2 4 3l-2 2c-2-1-3-1-5-3 1-1 2-1 3-2z" class="X"></path><path d="M557 778h2l2 2v1l-1 1 2 2c-1 1-2 1-3 2l-1-1-2-2v-1c1 0 1-1 2-1 0-2-1-2-1-3z" class="G"></path><path d="M558 785v-1l2-2 2 2c-1 1-2 1-3 2l-1-1z" class="u"></path><path d="M555 792h0l4 1c2 1 3 1 5 2 2 0 4 0 5-1 2-1 3-1 5-2-1 1-1 2-2 2l-1 1c-1 2-1 2-1 4l-1 1 2 1c1 0 2 1 3 2h2l1-3 2 1v1l2-1 2 2 3 2 1 1h3 1c1 1 0 1 1 1h0c0 1-1 1-2 2v1l-1 1-1-1v-2c-1 0-2 1-2 2-1 1-2 1-4 2-1 0-1 1-1 2h-1l-1-1c-1 0-1 1-2 1l-1-1h-2v2c-1-1-2-1-3-1-2-1-3 0-4-1-1 0 0-1-2-1-2-1-3-1-4-2h-3l-1-1-1-4 1 1v-4l-3-3h-1v-1l-2-4h1l1-1c1 0 1 0 2-1z" class="K"></path><path d="M564 797h1l1 2h1c-1 1-2 1-3 2v-2c-1-1-1-1-1-2v1l1-1z" class="E"></path><path d="M569 794c2-1 3-1 5-2-1 1-1 2-2 2l-1 1c-1 0-4 1-6 2h-1v-2c2 0 4 0 5-1z" class="j"></path><path d="M557 802c1 1 2 2 3 4h1c1 1 3 2 4 3l1 1-1 1h-1c-1-1-1-2-2-2h-2l-3-3v-4z" class="c"></path><path d="M556 805l1 1 3 3h2c1 0 1 1 2 2h1l1-1c2 1 5 2 7 2l1 1h0v2c-1-1-2-1-3-1-2-1-3 0-4-1-1 0 0-1-2-1-2-1-3-1-4-2h-3l-1-1-1-4z" class="G"></path><path d="M555 792h0l4 1c2 1 3 1 5 2v2l-1 1v-1c-2-1-2 0-3 0l-2 2-1-1h-1c-1 0-2 0-2 1h-1v-1l-2-4h1l1-1c1 0 1 0 2-1z" class="c"></path><path d="M577 800l2 1v1l2-1 2 2 3 2 1 1h3 1c1 1 0 1 1 1h0c0 1-1 1-2 2v1l-1 1-1-1v-2c-1 0-2 1-2 2-1 1-2 1-4 2v-1l-1-1v-1c-1 0-1 1-2 2l-1-1-2 1-1-1h-2c-1 1-1 0-1 1h-1c0-2 0-3 1-4h1 0-1v-2h-1 0c1-2 1-2 3-2h2l1-3z" class="Q"></path><path d="M582 811v-2-1c1 0 1-1 3-2 1 2 1 3 1 4-1 1-2 1-4 2v-1z" class="I"></path><path d="M577 800l2 1v1l2-1c0 1-1 2-2 3l1 1v1c-2 0-2 0-3-1l1-1v-1h-2 0l1-3z" class="k"></path><path d="M586 776c1-1 2-1 3-1l1 1h0c0 1 0 1 1 2v2c2 2 2 3 4 3v1h-1v2c2 1 3 2 4 3l1 1h-1v2c2 1 2 1 3 4 2 1 4 2 5 4v2h3l3 3c1 1 2 2 2 3-1 1-2 1-3 1-1 1-3 3-3 4-1-1-1 0-1-1l-2 1v1c1 0 1 1 1 2h-1l-4 1v-1c-1 0-1-1-3 0-1-1-2-1-3 0h-4c-1 0-1 1-3 1h-1l-2-2-1-1c-2 1-2 0-4 0h1c0-1 0-2 1-2 2-1 3-1 4-2 0-1 1-2 2-2v2l1 1 1-1v-1c1-1 2-1 2-2h0c-1 0 0 0-1-1h-1-3l-1-1-3-2 1-3h0v-1l-1 1-2-1h0c0-1-1-2-1-3h-1c1-2 1-3 2-4h0c-1-1-1-2-2-2 1-1 2-2 2-3s-1-1 0-2c-1-1-1-2-1-3v-1h0c1-2 2-3 3-4 1 0 2-1 3-1z" class="Q"></path><path d="M587 791l-1-3 1-1h2v2l-1 1-1 1z" class="n"></path><path d="M588 790l1 1c-1 1-2 0-3 2l1 1-1 1c-1-1-1-2-2-3l1-1h2l1-1z" class="k"></path><path d="M592 796l2 1h1l1 1-1 1c-1 1-1 1-1 3-1-1-2-1-3-1 0-2 1-3 1-5z" class="P"></path><path d="M581 792l1 1v2c1 1 2 1 3 1v1c-1 0-2 0-2-1-2 1-1 2-2 3h0c0-1-1-2-1-3h-1c1-2 1-3 2-4z" class="r"></path><path d="M591 801c1 0 2 0 3 1-1 1-1 2-3 3l-2-1-1 1h-2l-3-2 1-3c1 1 2 1 3 1 0 1 0 1 1 2 2 0 2-1 3-2z" class="u"></path><g class="d"><path d="M586 776c1-1 2-1 3-1l1 1h0c0 1 0 1 1 2v2c2 2 2 3 4 3v1h-1v2c2 1 3 2 4 3l1 1h-1v2l-1 1c-1-1-1-1-2-1h-2c0-1-1-2-2-3h0c1 0 2-1 2-2-1 0-1 0-2 1-1-1-1-2-2-3l-1 1-1-1c1-1 1-2 1-4 0-1 0-1 1-2l-1-1c-1 0-2 1-3 1l-1 1h2v1l-1 2c0 1-1 1-1 2v1 2 1l-1-1c0-1-1-2-1-3l1-1v-3h-2-1c1-2 2-3 3-4 1 0 2-1 3-1z"></path><path d="M592 796v-1l-2-1v-2-1l1-2c1 1 2 2 2 3h2c1 0 1 0 2 1l1-1c2 1 2 1 3 4 2 1 4 2 5 4v2h3l3 3c1 1 2 2 2 3-1 1-2 1-3 1-1 1-3 3-3 4-1-1-1 0-1-1h-1v-1h-1l1-2-1-1h-1c0-1 0-1-1-2 0-1 0-2-1-2l-4-6h-2l-1-1h-1l-2-1z"></path></g><path d="M605 808l2-1v1l2 1v1c-1 0-2 1-2 2h-1v-1h-1l1-2-1-1z" class="E"></path><path d="M592 796v-1l-2-1v-2-1l1-2c1 1 2 2 2 3h2c1 0 1 0 2 1l1-1c2 1 2 1 3 4-1 0-1 0-2 1 2 1 2 0 3 1 0 1 1 1 2 2l1 1h-1c-1 0-1 2-2 3l-4-6h-2l-1-1h-1l-2-1z" class="h"></path><path d="M595 797c0-1 1-1 2-2 0 1 1 2 1 3h-2l-1-1z" class="d"></path><path d="M592 796v-1l-2-1v-2-1l1-2c1 1 2 2 2 3l-1 1v1c2 1 3 1 5 0v1c-1 1-2 1-2 2h-1l-2-1z" class="K"></path><path d="M596 798h2l4 6c1 0 1 1 1 2 1 1 1 1 1 2h1l1 1-1 2h1v1h1l-2 1v1c1 0 1 1 1 2h-1l-4 1v-1c-1 0-1-1-3 0-1-1-2-1-3 0h-4c-1 0-1 1-3 1h-1l-2-2-1-1c-2 1-2 0-4 0h1c0-1 0-2 1-2 2-1 3-1 4-2 0-1 1-2 2-2v2l1 1 1-1v-1c1-1 2-1 2-2h0c-1 0 0 0-1-1h-1-3l-1-1h2l1-1 2 1c2-1 2-2 3-3 0-2 0-2 1-3l1-1z" class="J"></path><path d="M598 807h2c0 2-1 2-3 4-1 1-2 1-3 1 0-1 1-2 2-4l2-1z" class="j"></path><path d="M596 798h2l4 6c1 0 1 1 1 2l-3-3c0-1 0-1-1-2v1c0 1 1 1 1 2v1c-1 0-1-1-2-2 0-1 0-1-1-2l-2-2 1-1zm8 10h1l1 1-1 2h1v1h1l-2 1v1c1 0 1 1 1 2h-1l-4 1v-1l1-1c-1-1-1-2-2-3l1-1h3v-3z" class="X"></path><path d="M595 799l2 2c1 1 1 1 1 2 1 1 1 2 2 2v2h-2l-2 1c-1 2-2 3-2 4-2 2-4 2-6 2-1 0-2 1-3 1l-1-1c-2 1-2 0-4 0h1c0-1 0-2 1-2 2-1 3-1 4-2 0-1 1-2 2-2v2l1 1 1-1v-1c1-1 2-1 2-2h0c-1 0 0 0-1-1h-1-3l-1-1h2l1-1 2 1c2-1 2-2 3-3 0-2 0-2 1-3z" class="n"></path><path d="M597 801c1 1 1 1 1 2 1 1 1 2 2 2v2h-2l-2-1h-1c1-2 1-3 2-5z" class="E"></path><path d="M582 812v2h0c2 0 3-2 5-2l2 1v-1c1 0 2 0 2-1l1-1c2 0 3-1 4-2-1 2-2 3-2 4-2 2-4 2-6 2-1 0-2 1-3 1l-1-1c-2 1-2 0-4 0h1c0-1 0-2 1-2z" class="p"></path><path d="M587 711h2v5l1 12 1 11c3 14 6 28 12 40 3 6 7 11 11 16h-3c-2 0-3 0-5 1 2 1 3 2 4 3v1-1h-2l-1 1 2 2h-3v-2c-1-2-3-3-5-4-1-3-1-3-3-4v-2h1l-1-1c-1-1-2-2-4-3v-2h1v-1c-2 0-2-1-4-3v-2c-1-1-1-1-1-2h0l-1-1c-1 0-2 0-3 1 0-1 0-1-1-1h0l-2-2c-1-2-3-2-4-4l-1-1 2-2-6 2h-3 4v2l-1 1h0l-2 1c-1 0-2-1-3-2s-1 0-2-1v-1c-1 0-1-1-1-1v-1c-1-1-1-2-2-3s-1-2-3-3c0 1-1 2-1 3l-2 2c0 1-1 2-2 3l1 2-2 1 1 1v1l1 1-1 2h0c1 1 1 1 1 2s1 1 1 3c-1 0-1 1-2 1-2-2-2-4-3-6l-1-2 1-2c0-5 1-7 3-11l-1-2c0-1 0-1 1-2l1 1 2-2-1-1c1-1 2-1 4-1 1 0 1-1 2-1l-2-3h3l1 1c2-1 3-2 5-3h1l4-4v-2c1-1 2-3 2-4 1 1 2 2 2 3v1h1l1 1v2l1-1v-4l1-1c1-5 0-10-1-16v-4c1-1 1-2 1-4h1c-1 0-1 0-2-1h1l3-3z" class="C"></path><path d="M589 766l3 8-1-1h0-1c0-1 0-2-1-3h-2c1-1 1-2 1-4h1z" class="G"></path><path d="M586 750l3 16h-1c-1 0-2-1-3-1l2-2v-1l-1-4c-1 0-2 1-3 1v-2-3h0c1-2 1-2 0-2 1-2 2-2 3-2z" class="X"></path><path d="M583 754c2 1 2 2 3 4-1 0-2 1-3 1v-2-3h0z" class="n"></path><path d="M583 754v3 2c1 0 2-1 3-1l1 4v1l-2 2c1 0 2 1 3 1 0 2 0 3-1 4-1 0-1-1-2-1v1h-1c-2 0-2 0-3-1h-2l-1-1 2-2-6 2c0-1 0-2-1-3l1-1c1 0 1-1 2-2v-1l1 1 1 1 2-1c1-1 1-1 2-1l-1-2h-3l-2-2c1 0 3-1 3-1 2-1 3-1 4-2z" class="I"></path><path d="M583 754v3l-1-1c-2 1-1 1-1 2-1 0-3 1-3 1l-2-2c1 0 3-1 3-1 2-1 3-1 4-2z" class="Q"></path><path d="M580 766l1-2h1l1 1 1-1c0-1 0 0 1-1h2l-2 2c1 0 2 1 3 1 0 2 0 3-1 4-1 0-1-1-2-1v1h-1c-2 0-2 0-3-1h-2l-1-1 2-2h0z" class="E"></path><path d="M580 766h3c0 2-2 1 0 3h2v1h-1c-2 0-2 0-3-1h-2l-1-1 2-2h0z" class="c"></path><path d="M585 769c0-1-1-3-1-4l1-1v1c1 0 2 1 3 1 0 2 0 3-1 4-1 0-1-1-2-1z" class="n"></path><path d="M576 757l2 2h3l1 2c-1 0-1 0-2 1l-2 1-1-1-1-1v1c-1 1-1 2-2 2l-1 1c1 1 1 2 1 3h-3 4v2l-1 1h0l-2 1c-1 0-2-1-3-2s-1 0-2-1v-1c-1 0-1-1-1-1v-1c-1-1-1-2-2-3s-1-2-3-3h6l5-2 4-1z" class="l"></path><path d="M570 768h0l1-2 2-1c1 1 1 2 1 3h-3-1zm-3-8l5-2-1 4 1 1v1c-1-1-2-1-3-2-1 1-2 2-2 3l-1-1c0-2 1-3 1-4z" class="Q"></path><path d="M561 760h6c0 1-1 2-1 4l1 1 2 2c0 1 0 1 1 1h1 4v2l-1 1h0l-2 1c-1 0-2-1-3-2s-1 0-2-1v-1c-1 0-1-1-1-1v-1c-1-1-1-2-2-3s-1-2-3-3z" class="c"></path><path d="M587 711h2v5l1 12 1 11h0c-1-2-1-4-2-5h-2v-1c0-1 0-2 1-3 0-1 0-2-1-2 1-1 1-2 1-3-1 1-1 0-1 2-1 0-1 1-1 2v2c0 1 0 3 1 4-1 1-1 2-2 3v3 2h0l-1-4c1-5 0-10-1-16v-4c1-1 1-2 1-4h1c-1 0-1 0-2-1h1l3-3z" class="f"></path><path d="M587 711h2v5l1 12c-1-2-1-4-1-7 0-1 0-2-1-3l-1-1c-1-1-2-1-3-2h1c-1 0-1 0-2-1h1l3-3z" class="S"></path><path d="M579 769h2c1 1 1 1 3 1h1v-1c1 0 1 1 2 1h2c1 1 1 2 1 3h1 0l1 1c4 8 8 15 14 22 2 1 3 2 4 3v1-1h-2l-1 1 2 2h-3v-2c-1-2-3-3-5-4-1-3-1-3-3-4v-2h1l-1-1c-1-1-2-2-4-3v-2h1v-1c-2 0-2-1-4-3v-2c-1-1-1-1-1-2h0l-1-1c-1 0-2 0-3 1 0-1 0-1-1-1h0l-2-2c-1-2-3-2-4-4z" class="E"></path><path d="M584 770h1l3 3v1h-1c-2-1-3-2-3-4z" class="b"></path><path d="M576 742c1-1 2-3 2-4 1 1 2 2 2 3v1h1l1 1v2l1-1v-4l1-1 1 4h0l1 7c-1 0-2 0-3 2 1 0 1 0 0 2h0c-1 1-2 1-4 2 0 0-2 1-3 1l-4 1-5 2h-6c0 1-1 2-1 3l-2 2c0 1-1 2-2 3l1 2-2 1 1 1v1l1 1-1 2h0c1 1 1 1 1 2s1 1 1 3c-1 0-1 1-2 1-2-2-2-4-3-6l-1-2 1-2c0-5 1-7 3-11l-1-2c0-1 0-1 1-2l1 1 2-2-1-1c1-1 2-1 4-1 1 0 1-1 2-1l-2-3h3l1 1c2-1 3-2 5-3h1l4-4v-2z" class="s"></path><path d="M558 755c1-1 2-1 4-1 1 0 1-1 2-1l1 1 2-1 1 1v1h1l2-2h1v1l-1 1-7 1h-3c-2 1-4 4-5 5l-1-2c0-1 0-1 1-2l1 1 2-2-1-1z" class="S"></path><path d="M576 742c1-1 2-3 2-4 1 1 2 2 2 3v1h1l1 1v2l1-1v-4l1-1 1 4h0l1 7c-1 0-2 0-3 2 1 0 1 0 0 2h0c-1 1-2 1-4 2-1-2-1-2-3-2-1 0-3 1-5 1l1-1v-1h-1l-2 2h-1v-1l-1-1-2 1-1-1-2-3h3l1 1c2-1 3-2 5-3h1l4-4v-2z" class="i"></path><path d="M566 751c2-1 3-2 5-3-1 2-1 3-2 4-1 0-2-1-3-1z" class="w"></path><path d="M580 742h1c0 2-1 3-1 4-1 0-1 1-2 0 0-1 0-2 2-4z" class="C"></path><path d="M576 742c1-1 2-3 2-4 1 1 2 2 2 3v1c-2 2-2 3-2 4-2 1-4 1-6 2l4-4v-2z" class="M"></path><path d="M584 739l1 4h0l1 7c-1 0-2 0-3 2 1 0 1 0 0 2h0c-1 1-2 1-4 2-1-2-1-2-3-2 2-1 4-2 5-4 2-2 2-3 2-6v-4l1-1z" class="J"></path><path d="M744 324h1l5 3 6 2 1 1 11 5 3 1 2 1v2l2 1 2-2 2 1 3 1h1l4 4v1c0 1-1 1-1 2v2 1c1 1 1 1 3 1 0 1 1 1 1 2l1 1 1 1 1 2 2 1 1 1v4l1 5c0-2 0-3 1-4v2h2c0 1 0 1-1 2h1c1 1 3 1 4 1l-1-1h1 0 1c1-1 1-2 2-3h0c1 1 0 1 1 2h-1c-1 1-1 1-2 3 1 2 0 3-1 6-1 2-2 3-4 4h0c0 1-1 2-1 3s-1 2-2 2c-1-1-1-1-2-1l-1 1-1 2-1 1-2 2c-1 0-2 0-2 1l-1 1v1l-1 4-1 1-1 1-1 1c-2 2-3 3-5 4l-4 2-3 1v-1l-1-1-6-3 3 3-1 1h-2l-2-1-1-1h0l-5-3-1-6h-3l-1 1c1 1 2 1 3 2h-3l-1-1c-1 1-2 1-2 1l-7-20 2-2v-2l2-2h0c0-2-1-3-2-4 0-2 0-1-1-2v-1-1c-1-4-3-7-5-11l-3-5-12-18 1-1 3 3 1-3c1 1 1 1 2 1h0l-1-1 5-1 6 6 2 3h4c-2-4-2-7-2-11 0-1 0-1 1-2z" class="k"></path><path d="M776 380c2 0 2 0 4 2v1l-1 1-2-2h0c0-1-1-1-1-2zm-10-4c1-1 2-1 3 0h1v1h-1l-2 2-2-2 1-1h0z" class="L"></path><path d="M781 376l2 2c-1 1 0 2-1 3-1-1-2-1-2-1l-1-1v-1l2-2z" class="G"></path><path d="M777 369h1c1 2 1 4 2 6l1 1-2 2h0c0-2-1-2-2-4v-5z" class="E"></path><path d="M766 376v-2h2v-1h-2l-2-2c0-2 0-2 2-2l1 2c1 1 2 3 3 3h1c-1 1-1 1-1 2h-1c-1-1-2-1-3 0z" class="Z"></path><path d="M785 389v-1l3 2v1l-1 1v1l-1 4-2-1v-2-2-1c0-1 0-1 1-2z" class="P"></path><path d="M785 389v-1l3 2v1l-1 1h-2v-3zm-33-21l2-1c0-1 0-1 1-2 0 0 1 1 2 1h0 1c1 0 3-1 4 0h5l-1 2v-1h-1c-2 0-2 0-3 1v2c-1-1-2-2-3-2h0l1 2-2 2c-1-1-2-1-3-2l1-2c-1 0-1 0-2 1l-2-1z" class="X"></path><path d="M760 376l1-1 4 2 2 2 4 2c3 2 7 5 10 8l-2 2c-4-4-9-7-13-11l-6-4z" class="C"></path><path d="M783 378l3 2c-1 0-1 1-1 2h3l1 1v2l2 2c1-1 1-1 1-2h2l-1 2-1 1-2 2c-1 0-2 0-2 1v-1l-3-2c0-1-2-3-3-4h0v-1c1-2 1-3 1-5z" class="G"></path><path d="M785 382h3l1 1v2c-1 1-1 1-2 1 0-1-2-3-2-4z" class="Z"></path><path d="M789 385l2 2c1-1 1-1 1-2h2l-1 2-1 1-2 2c-1 0-2 0-2 1v-1l-1-4c1 0 1 0 2-1z" class="S"></path><path d="M767 371l3-3c1 1 1 2 3 2l1-1h1v1h0c-1 0-2 1-2 1v1l1-1 2 2c0 1 0 2 1 3h-2v2c1 1 1 1 1 2s1 1 1 2l-1 1c-1-1-2-1-2-3l-1-1h-3v-2-1c0-1 0-1 1-2h-1c-1 0-2-2-3-3z" class="m"></path><path d="M751 361l5 1c4 1 9 1 13 1h8v1 1c-3 1-6 1-10 1h-5c-1-1-3 0-4 0h-1 0c-1 0-2-1-2-1-1 1-1 1-1 2l-2 1-3-3h4v1c1-1 0-1 1-2-2 0-2-1-3-3z" class="J"></path><path d="M737 353h2c2 4 6 6 10 7l2 1c1 2 1 3 3 3-1 1 0 1-1 2v-1h-4l-1-1v1c1 2 2 3 3 5 1 0 3 1 4 2s4 3 5 4l6 4-2 2h-1v-2h-2v4h-1 0c-1-1-2-2-3-2h0l-1 3-1 1-2-1c-1-1-2-2-3-4l-1-3c-2-4-4-11-7-14-1-4-3-7-5-11z" class="s"></path><path d="M751 370c1 0 3 1 4 2v3h3v1 1l-2 1v1c1 1 1 1 1 2-2-1-2-2-3-3 1 0 1 0 1-1 1-1 1-1 2-1h0-1-3 1v-3c-1-1-2-2-2-3h-1z" class="J"></path><path d="M755 372c1 1 4 3 5 4l6 4-2 2h-1v-2h-2 0c-1-1-1-1-1-2l-4 1v-1l2-1v-1-1h-3v-3z" class="P"></path><path d="M750 381h3v-1l-1-1 1-1h1c1 1 1 2 3 3 0-1 0-1-1-2l4-1c0 1 0 1 1 2h0v4h-1 0c-1-1-2-2-3-2h0l-1 3-1 1-2-1c-1-1-2-2-3-4z" class="X"></path><path d="M761 380h2v2h1l2-2c4 4 9 7 13 11l2-2 2 2v2l1 1v2l2 1-1 1-1 1-1 1c-2 2-3 3-5 4l-4 2-3 1v-1l2-1c2-1 3-1 5-3l-1-1c-2-1-3-2-6-3-2-1-5-2-7-4l-2-2-1-1c-2-1-4-3-6-5l1-1 1-3h0c1 0 2 1 3 2h0 1v-4z" class="K"></path><path d="M762 392c1 0 2 0 2-2h1l1 1h0c-1 1-1 1-1 2l-1 1-2-2z" class="E"></path><path d="M771 398c1 0 0-2 0-3-1 0-2 0-3-1-1 0-1-1-1-2l1-1s0 1 1 1c1 1 0 1 1 2l2-1v1h3s0 1 1 1c-1 0-1 0-2 1 0 1 1 2 3 3h0 1v2 1l-1-1c-2-1-3-2-6-3z" class="P"></path><path d="M755 386l1-1 1-3h0c1 0 2 1 3 2h0 1l1-1 1 1v1h2l-3 1v1l2 1-1 1-1 1-1 1c-2-1-4-3-6-5z" class="E"></path><path d="M761 380h2v2h1l2-2c4 4 9 7 13 11l2-2 2 2v2l1 1v2l2 1-1 1-1 1-1 1c-2 2-3 3-5 4l-4 2-3 1v-1l2-1c2-1 3-1 5-3v-1-2h-1 0c-2-1-3-2-3-3 1-1 1-1 2-1 1 1 0 2 1 3l1-1s0-1 1-1v-1c0-1-1-1-2-1-1-1-2-1-2-2-2-1-4-2-5-4l-1-1c-1-1-3-2-4-2h-2v-1l-1-1-1 1v-4z" class="G"></path><path d="M783 396h1l2 1-1 1-1 1-1 1c-1-1-1-2 0-4z" class="E"></path><path d="M781 389l2 2v2l1 1v2h-1c-1-2-3-3-4-5l2-2z" class="T"></path><path d="M742 364c3 3 5 10 7 14l1 3c1 2 2 3 3 4l2 1c2 2 4 4 6 5l1 1 2 2c2 2 5 3 7 4 3 1 4 2 6 3l1 1c-2 2-3 2-5 3l-2 1-1-1-6-3 3 3-1 1h-2l-2-1-1-1h0l-5-3-1-6h-3l-1 1c1 1 2 1 3 2h-3l-1-1c-1 1-2 1-2 1l-7-20 2-2v-2l2-2h0c0-2-1-3-2-4 0-2 0-1-1-2v-1-1z" class="C"></path><path d="M763 396c1 0 2 1 3 2v1h-1c-1 0-2-1-2-2v-1zm7 9v-4h-2v-1l1-1 1 1c1 0 2 0 3 1s2 0 4 0l1 1c-2 2-3 2-5 3l-2 1-1-1z" class="H"></path><path d="M755 395c0-1-1-3-1-4h1c2 4 6 7 9 11h0l3 3-1 1h-2l-2-1-1-1h0l-5-3-1-6z" class="y"></path><path d="M742 364c3 3 5 10 7 14-1 0-1 0-2-1l-1 1c1 2 1 3 2 5 1 1 2 4 2 6 1 1 0 0 1 2 0 0 0 1 1 2v2l-1 1c1 1 2 1 3 2h-3l-1-1c-1 1-2 1-2 1l-7-20 2-2v-2l2-2h0c0-2-1-3-2-4 0-2 0-1-1-2v-1-1z" class="F"></path><path d="M789 351c0 1 1 1 1 2l1 1 1 1 1 2 2 1 1 1v4l1 5c0-2 0-3 1-4v2h2c0 1 0 1-1 2h1c1 1 3 1 4 1l-1-1h1 0 1c1-1 1-2 2-3h0c1 1 0 1 1 2h-1c-1 1-1 1-2 3 1 2 0 3-1 6-1 2-2 3-4 4h0c0 1-1 2-1 3s-1 2-2 2c-1-1-1-1-2-1l-1 1h-2c0 1 0 1-1 2l-2-2v-2l-1-1h-3c0-1 0-2 1-2l-3-2h0l-2-2-1-1c-1-2-1-4-2-6h-1c0-1-1-1 0-2v-2-1-1h-8c3-1 6-1 9-2h-4c2-1 3-2 5-3h1 1l-1-1 1-1 4-1 4-4z" class="H"></path><path d="M790 373l1 1c0 1 0 2-1 3l-3-1c1-2 2-2 3-3z" class="N"></path><path d="M792 378c1 0 1-1 2-1h1c-1-1-1-1 0-2l2-1 1 2c-1 0-2 1-3 1v1l-1 2-1-1-1-1z" class="O"></path><path d="M792 378l1 1 1 1 1-2v-1 4h-4l1-2v-1z" class="C"></path><path d="M797 374h1 1l2 2c-2 2-3 3-6 5v-4c1 0 2-1 3-1l-1-2h0 0z" class="T"></path><path d="M800 368c1 1 3 1 4 1-1 3-2 5-3 7l-2-2h-1-1v-2c2 0 2-1 3-2h1l-1-2z" class="U"></path><path d="M780 375h3l1 1c1 0 1 0 2-1 0 0 1 0 1 1l3 1-2 4-2-1-3-2h0l-2-2-1-1z" class="C"></path><path d="M779 368h2c1 0 1-1 2-2 1 0 3 0 4 1 1 0 1 1 2 1l-1 3v1l2 1c-1 1-2 1-3 3 0-1-1-1-1-1-1 1-1 1-2 1l-1-1h-3c-1-2-1-4-2-6l1-1z" class="a"></path><path d="M779 368h2c1 0 1-1 2-2 1 0 3 0 4 1-2 1-4 2-4 4v2 2h-3c-1-2-1-4-2-6l1-1z" class="H"></path><path d="M803 368h1 0 1c1-1 1-2 2-3h0c1 1 0 1 1 2h-1c-1 1-1 1-2 3 1 2 0 3-1 6-1 2-2 3-4 4h0c0 1-1 2-1 3s-1 2-2 2c-1-1-1-1-2-1l-1 1h-2c0 1 0 1-1 2l-2-2v-2l-1-1h-3c0-1 0-2 1-2l2 1h3 4c3-2 4-3 6-5 1-2 2-4 3-7l-1-1z" class="P"></path><path d="M800 380c0 1-1 2-1 3s-1 2-2 2c-1-1-1-1-2-1l-1 1h-2c0 1 0 1-1 2l-2-2v-2h4 1c2-1 4-1 6-3z" class="r"></path><path d="M789 351c0 1 1 1 1 2l1 1 1 1 1 2 2 1 1 1v4l1 5c-1 2-2 4-4 5-1 0-2 0-2 1l-1-1-2-1v-1l1-3c-1 0-1-1-2-1-1-1-3-1-4-1-1 1-1 2-2 2h-2l-1 1h-1c0-1-1-1 0-2v-2-1-1h-8c3-1 6-1 9-2h-4c2-1 3-2 5-3h1 1l-1-1 1-1 4-1 4-4z" class="X"></path><path d="M784 363h-4c1-2 3-2 5-3l1 1-1 2h-1 0z" class="u"></path><path d="M789 351c0 1 1 1 1 2l1 1c-2 2-3 2-5 3 0-1-1-1-1-2l4-4z" class="V"></path><path d="M791 354l1 1 1 2 2 1 1 1-2 2c-1 0-1-1-2-2h-2l-1 1-1 1h-1l-1-1c1-2 2-2 3-3s1-1 2-3z" class="J"></path><path d="M781 356l4-1c0 1 1 1 1 2-3 2-5 3-8 4h-4c2-1 3-2 5-3h1 1l-1-1 1-1z" class="R"></path><path d="M790 361l2 1v1h-3l-1 1 1 1 2 1-2 2c-1 0-1-1-2-1-1-1-3-1-4-1-1 1-1 2-2 2h-2l-1 1h-1c0-1-1-1 0-2v-2-1c1 0 1 0 3-1l3 1 1-1h0c2 0 2 0 3-1 1 0 2 0 3-1z" class="k"></path><path d="M780 363l3 1-4 3 1-4z" class="P"></path><path d="M777 364c1 0 1 0 3-1l-1 4v1l-1 1h-1c0-1-1-1 0-2v-2-1zm15-5c1 1 1 2 2 2l2-2v4l1 5c-1 2-2 4-4 5-1 0-2 0-2 1l-1-1-2-1v-1l1-3 2-2-2-1-1-1 1-1h3v-1l-2-1h0l2-2z" class="G"></path><path d="M788 364h3c1 1 1 1 1 2l1 1c-1 2-1 2-2 3l-2 1c0 1 0 0-1 1v-1l1-3 2-2-2-1-1-1z" class="I"></path><path d="M792 359c1 1 1 2 2 2l-1 1c1 1 2 2 2 3v2h-2 0l-1-1c0-1 0-1-1-2h-3l1-1h3v-1l-2-1h0l2-2z" class="c"></path><path d="M744 324h1l5 3 6 2 1 1 11 5 3 1 2 1v2l2 1 2-2 2 1 3 1h1l4 4v1c0 1-1 1-1 2v2 1c1 1 1 1 3 1l-4 4-4 1-1 1 1 1h-1-1c-2 1-3 2-5 3h4c-3 1-6 1-9 2-4 0-9 0-13-1l-5-1-2-1c-4-1-8-3-10-7h-2l-3-5-12-18 1-1 3 3 1-3c1 1 1 1 2 1h0l-1-1 5-1 6 6 2 3h4c-2-4-2-7-2-11 0-1 0-1 1-2z" class="M"></path><path d="M739 342c3 3 10 10 14 11 3 0 4 1 6 3h-2c-2-1-3-2-5-1l-4-2c-3-2-5-4-8-6 0-2-1-2-2-3 0-1 1-2 1-2z" class="Z"></path><path d="M723 329l3 3 1-3c1 1 1 1 2 1h0c1 1 2 1 3 3l-1 1 3 3 5 5s-1 1-1 2c1 1 2 1 2 3 3 2 5 4 8 6 0 1 0 2-1 3-3-1-5-2-7-4l-1 1h-2l-3-5-12-18 1-1z" class="s"></path><path d="M734 348c3 1 4 2 6 4h0l-1 1h-2l-3-5z" class="J"></path><path d="M726 332l1-3c1 1 1 1 2 1h0c1 1 2 1 3 3l-1 1 3 3 5 5s-1 1-1 2c1 1 2 1 2 3-5-4-9-9-13-14l-1-1z" class="m"></path><path d="M726 332l1-3c1 1 1 1 2 1h0c1 1 2 1 3 3l-1 1 3 3c-2-1-4-3-6-5h0l-1 1-1-1z" class="Z"></path><path d="M740 352c2 2 4 3 7 4 1-1 1-2 1-3l4 2c2-1 3 0 5 1h2 5c1 0 1 1 2 1v1c1-1 0-1 1-1h1v-1c1-1 4-1 6-1 1-1 2-1 2-2l1 2h0c0 1-1 2-1 3l4-1 1 1h-1-1c-2 1-3 2-5 3h4c-3 1-6 1-9 2-4 0-9 0-13-1l-5-1-2-1c-4-1-8-3-10-7l1-1z" class="L"></path><path d="M748 353l4 2c2 1 3 2 5 4-4-1-7-2-10-3 1-1 1-2 1-3z" class="u"></path><path d="M780 357l1 1h-1-1c-2 1-3 2-5 3-2-1-3 0-4 0h-1-1c-2 0-9 1-11-1v-1c3 1 5 1 8 0 4 0 8 0 11-1l4-1z" class="i"></path><path d="M752 355c2-1 3 0 5 1h2 5c1 0 1 1 2 1v1l-1 1c-3 1-5 1-8 0h0c-2-2-3-3-5-4z" class="l"></path><path d="M768 356c1-1 4-1 6-1 1-1 2-1 2-2l1 2h0c0 1-1 2-1 3-3 1-7 1-11 1l1-1c1-1 0-1 1-1h1v-1z" class="I"></path><path d="M743 326c2 1 2 1 3 3l1 3c1 4 2 6 5 8 2 2 4 3 6 3l1 1c1 0 2 0 3 1h0l2 2h1l-1 1h1v2h1-1c0 1-1 1-2 1 0 1 0 0 1 1 0 1 0 2 1 3l3 1v1h-1c-1 0 0 0-1 1v-1c-1 0-1-1-2-1h-5c-2-2-3-3-6-3v-3c-1 0-1 0-1-1v-1h0c-4-3-7-7-11-11h4c-2-4-2-7-2-11z" class="F"></path><path d="M758 343l1 1c1 0 2 0 3 1h0c-1 1-1 1-1 3-1 0-2-1-3-1v-4z" class="M"></path><path d="M745 337c3 4 6 7 11 10 1 0 1 1 2 2l-2-1-1 1h0l-3-1c-4-3-7-7-11-11h4z" class="K"></path><path d="M753 353v-3c-1 0-1 0-1-1v-1h0l3 1h0l1-1 2 1h2c2 1 3 1 5 1h1-1c0 1-1 1-2 1 0 1 0 0 1 1 0 1 0 2 1 3l3 1v1h-1c-1 0 0 0-1 1v-1c-1 0-1-1-2-1h-5c-2-2-3-3-6-3z" class="U"></path><path d="M755 349h0l1-1 2 1h2l-1 3-4-3z" class="E"></path><path d="M760 349c2 1 3 1 5 1h1-1c0 1-1 1-2 1 0 1 0 0 1 1 0 1 0 2 1 3l-6-3 1-3z" class="X"></path><path d="M744 324h1l5 3 6 2 1 1 11 5 3 1 2 1v2l2 1-4 2-7 2h-5l-1-1c-2 0-4-1-6-3-3-2-4-4-5-8l-1-3c-1-2-1-2-3-3 0-1 0-1 1-2z" class="G"></path><path d="M750 327l6 2 1 1c-2 2-2 1-4 1-1 1-2 1-2 1l-2-2c0-1 0-2 1-3z" class="X"></path><path d="M760 340c-2-1-4-1-5-3 1-1 0-2 0-3 1-1 1-1 2-1 1 1 2 1 2 2v1h2 1c1 1 1 2 2 3-2 1-2 1-4 1z" class="q"></path><path d="M747 332l4 1v1c1 1 2 1 2 3h0v2c2 2 3 3 6 3 1 1 2 1 4 2h1-5l-1-1c-2 0-4-1-6-3-3-2-4-4-5-8z" class="E"></path><path d="M751 334c1 1 2 1 2 3h0c-1 0-2 0-3-1-1 0-1 0-1-1 1 0 1-1 2-1z" class="u"></path><path d="M768 335l3 1 2 1v2l2 1-4 2-7 2h-1c-2-1-3-1-4-2 0-1 1-1 1-2 2 0 2 0 4-1 0-2 2-3 4-4z" class="X"></path><path d="M771 336l2 1v2l2 1-4 2v-3-3z" class="P"></path><path d="M775 340l2-2 2 1 3 1h1l4 4v1c0 1-1 1-1 2v2 1c1 1 1 1 3 1l-4 4-4 1-1 1-4 1c0-1 1-2 1-3h0l-1-2c0 1-1 1-2 2-2 0-5 0-6 1l-3-1c-1-1-1-2-1-3-1-1-1 0-1-1 1 0 2 0 2-1h1-1v-2h-1l1-1h-1l-2-2h0c-1-1-2-1-3-1h5l7-2 4-2z" class="K"></path><path d="M781 345c2 0 3 0 4 1-1 1-1 1-1 2-1 1-4 0-4 1s0 1-1 2c-1-1-1-2-2-2l4-4z" class="P"></path><path d="M783 340l4 4-2 2c-1-1-2-1-4-1l-1-2 2-3h1z" class="s"></path><path d="M777 349c1 0 1 1 2 2h1 2c0 1-1 1-1 2-1 1-1 2 0 3l-1 1-4 1c0-1 1-2 1-3h0l-1-2h2v-1l-1-1-1 1-1-1 2-2z" class="h"></path><path d="M780 343l1 2-4 4-2 2 1 1 1-1 1 1v1h-2c0 1-1 1-2 2-2 0-5 0-6 1l-3-1c-1-1-1-2-1-3-1-1-1 0-1-1 1 0 2 0 2-1h1c2-1 4-1 5-1 3-2 5-3 7-5l2-1z" class="J"></path><path d="M775 340l2-2 2 1 3 1-2 3-2 1c-2 2-4 3-7 5-1 0-3 0-5 1h-1v-2h-1l1-1h-1l-2-2h0c-1-1-2-1-3-1h5l7-2 4-2z" class="S"></path><path d="M779 339l3 1-2 3-2 1c0-1-1-2-2-2l-2 1v-1h2c0-1 1-1 1-2 1-1 1-1 2-1z" class="f"></path><path d="M774 343l2-1c1 0 2 1 2 2-2 2-4 3-7 5l-1-2c1-1 1-2 2-2v-1c1 0 2 1 2 1v-2z" class="F"></path><path d="M765 347c3-1 4-3 7-3v1c-1 0-1 1-2 2l1 2c-1 0-3 0-5 1h-1v-2h-1l1-1z" class="B"></path><defs><linearGradient id="O" x1="418.163" y1="775.532" x2="470.651" y2="838.719" xlink:href="#B"><stop offset="0" stop-color="#cac8c7"></stop><stop offset="1" stop-color="#fbfbfa"></stop></linearGradient></defs><path fill="url(#O)" d="M508 739l2-1-1 14 1 1v4c-1 3 0 5-1 7v3h1l1-3 1-1h0c1-2 1-3 2-4 2 2 2 5 2 8 0-1 0-2 1-3l-2 23-1 8-1 4c-1 2-2 4-3 5-3 4-5 8-9 11-8 8-18 11-28 15-2 0-8 1-10 2-8 1-15 1-22 2h-87v-7l29-4h14c1-1 1-2 2-2h1 2c1-1 3-2 4-2l2-1 4-2 5-3h3c1-1 2-2 3-2h0l-2 3h1c1 1 2 1 4 1h2 1 4l3-1 1-1 3 2h4c2-1 2-2 4-2 1-1 2-1 3-2h1 2c0-1 0 0 1-1 2-1 3-2 5-3 1 0 2-2 4-3h2 0 1c1-2 4-3 5-5l2-3h2c1-1 2-3 3-4v-2c6-5 8-12 9-21h0l-1-5h1 1c1-1 0-1 0-2h2c0-1-1-1-1-2s1 0 0-1v-3l1-1c2-2 3-2 6-2v-1c0-1 0-2 1-3 1 0 2-2 2-2 2-3 4-7 8-8h0z"></path><defs><linearGradient id="P" x1="408.527" y1="828.347" x2="422.974" y2="814.628" xlink:href="#B"><stop offset="0" stop-color="#6d6b69"></stop><stop offset="1" stop-color="#9f9d9e"></stop></linearGradient></defs><path fill="url(#P)" d="M408 818l4-2v3l7 1 6 1h6 8c1 0 5-1 6 0l1 1-49 1c1-1 1-2 2-2h1 2c1-1 3-2 4-2l2-1z"></path><path d="M406 819l2-1c0 1 1 2 2 3h0-3c-1-1-1-1-1-2z" class="m"></path><path d="M408 818l4-2v3l7 1c-1 1-1 1-1 2-3 0-5-1-8-1-1-1-2-2-2-3z" class="f"></path><defs><linearGradient id="Q" x1="430.127" y1="828.676" x2="444.204" y2="806.095" xlink:href="#B"><stop offset="0" stop-color="#a8a6a7"></stop><stop offset="1" stop-color="#cecdca"></stop></linearGradient></defs><path fill="url(#Q)" d="M417 813h3c1-1 2-2 3-2h0l-2 3h1c1 1 2 1 4 1h2 1 4l3-1 1-1 3 2h4l-1 1 1 1v-1h2c2 0 2 0 3-1h1v2c0 1-2 1-3 1v1c1 0 3 0 4-1h1c1-1 2-1 3-1 1-1 1-1 2-1 0 0 1 0 2 1h0c1 1 1 0 2 0h1l1 1h-2-2l-2 3-11 1-1-1c-1-1-5 0-6 0h-8-6l-6-1-7-1v-3l5-3z"></path><path d="M430 818l1 1v2h-6c1-1 1-1 1-2l4-1z" class="B"></path><path d="M430 818h1c1 1 2 1 3 1h1 3l2 1 2-1h1c-2 1-3 2-4 2h-8v-2l-1-1z" class="D"></path><path d="M508 739l2-1-1 14c-2 17-2 34-13 48-3 4-6 7-10 9-2 3-7 5-9 6l-5 2c-5 1-10 2-15 4l2-3h2 2l-1-1h-1c-1 0-1 1-2 0h0c-1-1-2-1-2-1-1 0-1 0-2 1-1 0-2 0-3 1h-1c-1 1-3 1-4 1v-1c1 0 3 0 3-1v-2h-1c-1 1-1 1-3 1h-2v1l-1-1 1-1c2-1 2-2 4-2 1-1 2-1 3-2h1 2c0-1 0 0 1-1 2-1 3-2 5-3 1 0 2-2 4-3h2 0 1c1-2 4-3 5-5l2-3h2c1-1 2-3 3-4v-2c6-5 8-12 9-21h0l-1-5h1 1c1-1 0-1 0-2h2c0-1-1-1-1-2s1 0 0-1v-3l1-1c2-2 3-2 6-2v-1c0-1 0-2 1-3 1 0 2-2 2-2 2-3 4-7 8-8h0z" class="W"></path><path d="M481 803l-1-2c0-2 1-2 2-4 1 1 2 1 2 1l-3 5z" class="o"></path><path d="M484 798l1-1h1c0 3 1 4-1 7h-3l-1-1h0l3-5z" class="D"></path><path d="M461 817l6-3 8-4c2-1 4-3 6-3v1c-1 2-2 2-3 3l-2 1c-3 2-6 3-10 4l-4 1h-1z" class="B"></path><path d="M466 808l9-5c-1 1-1 2-3 3v1c-2 2-5 3-8 5 0 1-1 1-2 2l-1-1-4 3c-1 0-1 0-2 1-1 0-2 0-3 1h-1c-1 1-3 1-4 1v-1c1 0 3 0 3-1v-2c5-1 11-3 16-7z" class="D"></path><defs><linearGradient id="R" x1="478.336" y1="793.376" x2="467.086" y2="808.203" xlink:href="#B"><stop offset="0" stop-color="#5c5b58"></stop><stop offset="1" stop-color="#787675"></stop></linearGradient></defs><path fill="url(#R)" d="M479 792h0l1 2c1 0 1 0 2-1v2l-7 8h0l-9 5h-1c0-1 1-2 2-3v-1c1-2 4-3 5-5l2-3h2c1-1 2-3 3-4z"></path><path d="M466 804h1v1c-1 1-2 2-2 3h1c-5 4-11 6-16 7h-1c-1 1-1 1-3 1h-2v1l-1-1 1-1c2-1 2-2 4-2 1-1 2-1 3-2h1 2c0-1 0 0 1-1 2-1 3-2 5-3 1 0 2-2 4-3h2 0z" class="D"></path><path d="M466 804h1v1c-1 1-2 2-2 3h1c-5 4-11 6-16 7h-1c0-1 1-1 2-1l6-3c3-2 6-4 9-7h0z" class="m"></path><path d="M491 762s1 1 2 1h1l-1 1v1h1 2v4l1 2c0 1 0 2-2 3 0 1-1 1-2 1v1c0 1 0 1-1 2-1 2-1 5-3 7-2 4-3 7-6 9l-1 1v-2c-1 1-1 1-2 1l-1-2h0v-2c6-5 8-12 9-21h0l-1-5h1 1c1-1 0-1 0-2h2z" class="S"></path><path d="M491 762s1 1 2 1h1l-1 1h-1c0 1 0 2-2 3 0 1-1 1-2 2h0l-1-5h1 1c1-1 0-1 0-2h2z" class="Q"></path><path d="M488 769l2 1c0 3-2 6-2 9-1 2 0 4-1 6-1 3-3 6-4 9l-1 1v-2c-1 1-1 1-2 1l-1-2h0v-2c6-5 8-12 9-21z" class="I"></path><path d="M479 792c2 0 2-1 3-1v2c-1 1-1 1-2 1l-1-2z" class="d"></path><path d="M508 739l2-1-1 14c-2 17-2 34-13 48-3 4-6 7-10 9l1-1c2-1 5-5 6-6 0-2 2-3 3-5 0-1 0-1 1-2l-2 2h-1c2-2 3-4 4-6 1-1 1-2 1-3 2-3 2-5 3-8 0-2 2-8 1-10v-2c0-1 0 0-1-1h-2c-3 0-2-3-4-3v1h-2-1v-1l1-1h-1c-1 0-2-1-2-1 0-1-1-1-1-2s1 0 0-1v-3l1-1c2-2 3-2 6-2v-1c0-1 0-2 1-3 1 0 2-2 2-2 2-3 4-7 8-8h0z" class="D"></path><path d="M502 750h1v4h-1c-1-2-1-2 0-4z" class="g"></path><path d="M502 761h1l1 1c0 2 0 2-1 4h-3c1-2 2-3 2-5z" class="N"></path><path d="M498 749c1 0 2-2 2-2 2-3 4-7 8-8v2c-1 1-1 3-1 5l-1-2h-1l-1 1c0 1-2 4-4 5h0c-1-1-1-1-2-1z" class="C"></path><path d="M497 753c2 1 3 2 3 4v3c-2 2-4 3-6 3h-1c-1 0-2-1-2-1 0-1-1-1-1-2s1 0 0-1v-3l1-1c2-2 3-2 6-2z" class="b"></path><path d="M494 754h3l2 4c-1 2-2 2-3 3h-2c-1-1-1-1-2-3 0-2 0-2 2-4z" class="W"></path><path d="M508 739l2-1-1 14c-2 17-2 34-13 48v-1c5-6 7-13 9-21 0-1-1-3-1-4 1-3 0-5 1-8v-7c1-3 0-6 1-9v-1c1-1 1-2 1-3 0-2 0-4 1-5v-2h0z" class="h"></path><path d="M507 746c0-2 0-4 1-5 0 7 1 14-1 20v-2h-1v-9-1c1-1 1-2 1-3z" class="T"></path><path d="M506 750v9h1v2c0 4 0 8-1 12l-1 5c0-1-1-3-1-4 1-3 0-5 1-8v-7c1-3 0-6 1-9z" class="B"></path><path d="M564 646l1 1c2 1 3 0 4-1 0 1-1 5-2 5 1 2 1 3 3 4l-2 3h0l-4 4h-1l3 3c1 1 3 3 4 5l2 4v1l2-3v1c2 0 2 0 3 1h1c0-1 1-1 2-2v5c1 4 1 8 1 12v2l-1 1v5 4 1 1c0 1 1 3 0 5v3c1 2 1 4 1 7h1v-2c0 2 0 5 1 7 1 6 2 11 1 16l-1 1v4l-1 1v-2l-1-1h-1v-1c0-1-1-2-2-3 0 1-1 3-2 4v2l-4 4h-1c-2 1-3 2-5 3l-1-1h-3l2 3c-1 0-1 1-2 1-2 0-3 0-4 1l1 1-2 2-1-1c-1 1-1 1-1 2l-1-1v-2-1c-1-1 0-1 0-2h0-1-1c-3-1-7-3-10-6-3-2-6-5-8-9-2-2-3-4-4-6-5-10-5-22-4-34 0-3 0-7 1-11h3 0l2-3c-1-1-1-2-2-3 0-1 2-3 3-4l3-4c2-2 3-4 6-7l1-1h0c2-3 5-5 7-7l1-1 8-5 1-1 1-1 1-1h1l1-3h0z" class="V"></path><path d="M564 662l-1-1c1-1 1-2 1-2v-1c0-1 1-2 2-2 0 1 1 1 2 2h0l-4 4z" class="m"></path><path d="M550 670c3 0 5-1 7-1l-1 2v2h-1l-2 1c-1 0-1-1-1-1 0-1-1-2-2-3z" class="J"></path><path d="M530 707c0-2 0-4 1-6v8l1 6v1l-3-3v-5h0l1-1z" class="b"></path><path d="M533 709l1 3c0 1 1 2 1 3v1h0c0 2-1 2-1 3h-1c0-1 0-2-1-3v-1l-1-6 2 1v-1z" class="L"></path><path d="M545 674l5-4c1 1 2 2 2 3 0 0 0 1 1 1-2 1-3 1-4 1l-2 1-2-2z" class="P"></path><path d="M543 665c2 0 3 1 4 0 1 0 1-1 2-1 0 0 1 1 1 2l1 1c-1 1-2 2-4 2h-1l-2-1-1 1v1 1h-1v-5l1-1h0z" class="L"></path><path d="M557 669c3 0 5 1 6 3s2 3 2 5h-2-1v-1l-3-3h-3v-2l1-2z" class="c"></path><path d="M567 674l1-1c1 0 2 1 3 1h1v1l1 6c-1-1-1-1-2-1-1 2-2 3-3 4 0-2 0-2-1-3h0l1-2-1-1v-4z" class="T"></path><path d="M545 674l2 2v2h-1 0c0 2 0 2-1 3v1h0-2c-1 0-1-1-1-2h-1c-1 2-1 2-1 4l-2-3c2-3 4-5 6-7h1z" class="P"></path><path d="M545 674l2 2v2h-1l-1-1-1-3h1z" class="p"></path><path d="M546 678c0 2 0 2-1 3v1h0-2c-1 0-1-1-1-2 1 0 3-1 4-2z" class="Q"></path><path d="M566 665c1 1 3 3 4 5l2 4h-1c-1 0-2-1-3-1l-1 1c-1-2-3-3-3-5v-1l-5-1c1-1 2-1 4-1 1 0 1-1 3-1z" class="F"></path><path d="M567 674c-1-2-3-3-3-5l3 3h1 0c1-1 2-1 2-2l2 4h-1c-1 0-2-1-3-1l-1 1z" class="M"></path><path d="M542 666v5h1v-1-1l1-1 2 1h1c-1 1-1 2-2 3-2 1-4 4-6 5 0 0 0-1-1-1v-1l-2-2c2-2 3-4 6-7z" class="n"></path><path d="M557 662c1-1 1-2 3-2 1 1 1 2 3 2l3 3c-2 0-2 1-3 1-2 0-3 0-4 1h-4 0l-2 2c0-1-1-1-1-2v-1s1-1 2-1 1 0 2-1l1-2z" class="w"></path><path d="M557 662h3l1 1-1 1c-2 1-3 1-4 0l1-2z" class="T"></path><path d="M533 677l3-4 2 2v1c1 0 1 1 1 1-1 2-2 3-3 5-1 4-3 7-4 11 0-3 0-3 1-5l-1-2 1-1c1-1 0-2 0-2h-1v1c-1-1-1-2-2-3 0-1 2-3 3-4z" class="K"></path><path d="M564 646l1 1c2 1 3 0 4-1 0 1-1 5-2 5v1c-1 1-1 1-1 2h-2c-1 1-1 3-3 3-1 1-2 1-3 1l-3 3h0l-2 1v-1c-2 0-1 0-2 1v1 1l-3-2 2-2h2 1v-1l-2-2 8-5 1-1 1-1 1-1h1l1-3h0z" class="S"></path><path d="M555 661l-2-1v-2c1 0 2-1 3-1v1c0 1 0 1-1 2v1h0zm7-12c1 2 1 2 1 4l-1 1h-1l-1-3 1-1 1-1z" class="M"></path><path d="M556 673h3l3 3v1h1 2c1 2 1 4 0 7h0v2c-2 2-4 3-6 5h-5v-2l1-1-1-2c1-2 1-3 3-4h0c0-1 0-2-1-3-1 1 0 1-1 1h-1-1c-2-1-1-1-2-2 0-1 0-1-1-1l-2 1h0-1v-2l2-1c1 0 2 0 4-1l2-1h1z" class="b"></path><path d="M556 677h1v2h0c1 0 2 1 2 1 0 2 1 3 0 3s-1 0-2-1h0c0-1 0-2-1-3-1 1 0 1-1 1h-1c1-1 1-1 1-2l1-1z" class="k"></path><path d="M565 677c1 2 1 4 0 7h0c-1 1-1 0-1 1l-1 1v-2h-1l-1-1h-1v-2l3 1v-2c1-2 0-2 0-3h2z" class="G"></path><path d="M556 673h3l3 3v1l-2 1-1 2s-1-1-2-1h0v-2h-1c1-1 1-1 2-1v-1l-1-1c-1 0-1 0-2-1h1z" class="b"></path><path d="M562 676v1l-2 1-1 2s-1-1-2-1h0v-2h0c2 0 3 0 5-1zm-9-2l2-1c1 1 1 1 2 1l1 1v1c-1 0-1 0-2 1l-1 1c0 1 0 1-1 2h-1c-2-1-1-1-2-2 0-1 0-1-1-1l-2 1h0-1v-2l2-1c1 0 2 0 4-1z" class="Y"></path><path d="M551 678h4c0 1 0 1-1 2h-1c-2-1-1-1-2-2z" class="L"></path><path d="M562 684h1v2l1-1c0-1 0 0 1-1v2c-2 2-4 3-6 5h-5v-2l1-1h1l3-3h2 0l1-1z" class="P"></path><path d="M547 678h1 0l2-1c1 0 1 0 1 1 1 1 0 1 2 2h1 1c1 0 0 0 1-1 1 1 1 2 1 3h0c-2 1-2 2-3 4l1 2-1 1v2h5l-2 2h-1c-1 1-1 0-2 1v3h1c1 0 1 1 2 1-3 0-6-1-8-3h-1 0c-1-2-2-2-4-2h-1c0-1 0-2 1-4l1-1v-2l-2-1c0-1 1-2 2-3h0v-1c1-1 1-1 1-3h0 1z" class="I"></path><path d="M548 681l2 2h0v2l1 1h-3l-1-2v-1l1-2z" class="k"></path><path d="M549 688l2-1c1 1 2 1 3 2v2c-2-1-3-1-5-1v-2z" class="E"></path><path d="M545 682c1-1 2-2 4-2h0l-1 1-1 2v1l-2 2-2-1c0-1 1-2 2-3h0z" class="d"></path><path d="M545 686l2-2 1 2c0 1 0 2 1 2v2c2 0 3 0 5 1h5l-2 2h-1c-1 1-1 0-2 1v3h1c1 0 1 1 2 1-3 0-6-1-8-3h-1 0c-1-2-2-2-4-2h-1c0-1 0-2 1-4l1-1v-2z" class="M"></path><path d="M545 688h2l1 1v1c-2 0-2 1-4 1v-2l1-1z" class="i"></path><path d="M545 686l2-2 1 2c0 1 0 2 1 2v2l-1-1-1-1h-2v-2z" class="K"></path><path d="M549 695c0-1 1-2 2-3 2 1 3 1 5 1-1 1-1 0-2 1v3h1c1 0 1 1 2 1-3 0-6-1-8-3z" class="T"></path><path d="M540 684c0-2 0-2 1-4h1c0 1 0 2 1 2h2c-1 1-2 2-2 3l2 1v2l-1 1c-1 2-1 3-1 4h1c2 0 3 0 4 2h0v1l1 1 1 1c-1 2-1 3-1 4v1l-1 1-1 1v1h1c1 0 2 0 3 1l-2 1c-1-1-1-1-3 0h-1c-1 1-3 1-4 1h-2l-2 2v1c0 1-1 2-2 4v-1c0-1-1-2-1-3l-1-3c0-6-1-11 1-17v-1c1-4 2-7 4-10l2 3z" class="u"></path><path d="M534 712c0-2 0-3 1-4h0v-1l1-1c2 0 3 0 5 1v1l-2 1-2 2v1c0 1-1 2-2 4v-1c0-1-1-2-1-3z" class="J"></path><path d="M540 684c0-2 0-2 1-4h1c0 1 0 2 1 2h2c-1 1-2 2-2 3l2 1v2l-1 1c-1 2-1 3-1 4h1v2l-4 2v-2c-1-2-1-2-1-4s-1-2 1-4l-1-1 1-1v-1z" class="E"></path><path d="M544 693c2 0 3 0 4 2h0v1l1 1 1 1c-1 2-1 3-1 4v1l-1 1-1 1v1h1c1 0 2 0 3 1l-2 1c-1-1-1-1-3 0h-1c-2-1-4-2-5-4v-1c-1-1-1-3 0-5v-1l4-2v-2z" class="M"></path><path d="M540 704v-1c-1-1-1-3 0-5l1 3h1l1-1h1v1l-3 3h-1z" class="R"></path><path d="M544 701l2 2h1 2l-1 1-1 1v1h1c1 0 2 0 3 1l-2 1c-1-1-1-1-3 0h-1c-2-1-4-2-5-4h1l3-3z" class="V"></path><path d="M540 704h1 1c1 1 2 1 3 0l1 2v2h-1c-2-1-4-2-5-4z" class="M"></path><path d="M544 693c2 0 3 0 4 2h0v1l1 1 1 1c-1 2-1 3-1 4v1h-2v-1c-1-1-2-1-2-2v-2l-1 1v-1-2-1-2z" class="Z"></path><path d="M544 696c1 1 2 1 2 2h-1l-1 1v-1-2z" class="T"></path><path d="M547 702l2-3-1-1 1-1 1 1c-1 2-1 3-1 4v1h-2v-1z" class="R"></path><path d="M532 684v-1h1s1 1 0 2l-1 1 1 2c-1 2-1 2-1 5-1 2-1 5-1 8-1 2-1 4-1 6l-1 1h0v5l3 3c1 1 1 2 1 3h1c0-1 1-1 1-3h0c1-2 2-3 2-4v-1l2-2h2c1 0 3 0 4-1h1c2-1 2-1 3 0l-1 1c1 1 1 1 1 3l-1 1c-1 1-3 2-3 4h0v2l-1 1h-1-2-1v2 1 3 3 1h-3v2 1l-1 1 1 1-1 1-2 1v1c-2-2-3-4-4-6-5-10-5-22-4-34 0-3 0-7 1-11h3 0l2-3z" class="I"></path><path d="M538 726c1 1 1 2 2 3v1h-3c-1-1-1-1-1-3l2-1z" class="n"></path><path d="M527 708v-1h-1c-1-1 0-2 0-3s1-2 1-2h1l1 2-1 1h-1l-1 1 1 1v1z" class="k"></path><path d="M532 719c1 1 1 1 2 1l2-1v3l1 1-1 1-1-1v-2l-3 1c-1 0-1 0-2-1l2-2z" class="d"></path><path d="M536 719h0c1 1 0 1 1 2l2 2h1v3 3c-1-1-1-2-2-3-1 0-1-1-2-2l1-1-1-1v-3z" class="J"></path><path d="M534 737v-1c0-1-1-2-1-3l1-2-2-1v-3l1 1c1 2 2 3 4 4v1l-1 1 1 1-1 1-2 1z" class="Z"></path><path d="M527 708v-1l-1-1 1-1h1l2 2-1 1h0v5l1 5h-1c-2-2 0-2-1-4l-1 1-1-1h0c1-1 1-3 1-4-1-1 0-2 0-2z" class="L"></path><path d="M539 709h2c-1 2-1 3-2 3h-2c0 3 0 4 1 7l1 1c0 1 0 2 1 2v1h-1l-2-2c-1-1 0-1-1-2h0l-2 1c-1 0-1 0-2-1-1 0-1 0-2-1l-1-5 3 3c1 1 1 2 1 3h1c0-1 1-1 1-3h0c1-2 2-3 2-4v-1l2-2z" class="Y"></path><path d="M532 684v-1h1s1 1 0 2l-1 1 1 2c-1 2-1 2-1 5-1 2-1 5-1 8-1 2-1 4-1 6l-2-2 1-1c0-2 0-2-1-3l1-2-1-1v-1c1-1 1-2 1-3v-1-1l1-5 2-3z" class="P"></path><path d="M546 708c2-1 2-1 3 0l-1 1c1 1 1 1 1 3l-1 1c-1 1-3 2-3 4h0v2l-1 1h-1-2-1v2c-1 0-1-1-1-2l-1-1c-1-3-1-4-1-7h2c1 0 1-1 2-3 1 0 3 0 4-1h1z" class="Z"></path><path d="M548 709c1 1 1 1 1 3l-1 1c-1 1-3 2-3 4h0v2l-1 1h-1-2-1c0-1 0-1 1-2v-1l1-2c1-3 4-4 6-6z" class="u"></path><path d="M541 720l1-2h1l1 1-1 1h-2z" class="J"></path><path d="M574 673c2 0 2 0 3 1h1c0-1 1-1 2-2v5c1 4 1 8 1 12v2l-1 1v5 4 1 1c0 1 1 3 0 5v3c1 2 1 4 1 7l-1 1v-1l-2 1c-1-1-2-2-4-3-3-2-5-3-9-3-2-1-4 0-6 0v-1h-1-3v1l-1-1c-2 1-1 0-3 0-1 0-1 1-3 1l1-1c0-2 0-2-1-3l1-1 2-1c-1-1-2-1-3-1h-1v-1l1-1 1-1v-1c0-1 0-2 1-4l-1-1-1-1v-1h1c2 2 5 3 8 3-1 0-1-1-2-1h-1v-3c1-1 1 0 2-1h1l2-2c2-2 4-3 6-5l2-2h0 1c1-1 2-2 3-4 1 0 1 0 2 1l-1-6 2-3v1z" class="p"></path><path d="M550 698c2 1 5 1 7 2h2l1 1c-2 0-2 0-4-1l-2 1-1 1 1 1h0l2-1-3 3h0l-2 2c-1-1-2-1-3-1h-1v-1l1-1 1-1v-1c0-1 0-2 1-4z" class="L"></path><path d="M574 673c2 0 2 0 3 1h1c0-1 1-1 2-2v5c1 4 1 8 1 12v2l-1 1c-1 0 0 0-1-1h-3l-1-1h-1v3l-3 1c1-2 1-2 2-3v-1l2-3v-3h0v-2-1c0-1 1-1 0-2v-1c0-2 0-3-1-5z" class="h"></path><path d="M580 677c1 4 1 8 1 12v2c-2-1-2-1-2-3-1 0-1 0-1-1-1-2 0-2 1-4h0v-1c1-2 1-3 1-5z" class="d"></path><path d="M568 684c1-1 2-2 3-4 1 0 1 0 2 1v1c0 1-1 4 0 4l-1 2c-1 2-2 4-3 5-2 2-5 5-7 5-2 1-3 0-5 0-1 0-1-1-2-1h-1v-3c1-1 1 0 2-1h1l2-2c2-2 4-3 6-5l2-2h0 1z" class="C"></path><path d="M573 682c0 1-1 4 0 4l-3-1v-1c1-1 2-1 3-2z" class="q"></path><path d="M569 693l-1-2c0-1 0-2 1-3 0-1 0-1 1-1l2 1c-1 2-2 4-3 5z" class="F"></path><path d="M567 684l1 1c0 1 0 1-1 2v2h-1c0 1 0 0-1 1s-3 2-5 2c-1 1-2 1-3 1l2-2c2-2 4-3 6-5l2-2z" class="q"></path><path d="M554 694h0 3c2-1 3 0 5 0 1 2 0 2 0 4-2 1-3 0-5 0-1 0-1-1-2-1h-1v-3z" class="B"></path><path d="M571 694l3-1v-3h1l1 1h3c1 1 0 1 1 1v5 4c-1-1-2-1-3-3h-2 0c-1-1-1-2-2-2l-1 1v2h-1c-1 0-2 1-2 2l-1 2c-1-1-1-2-3-2v1l-2 2h0l-3 2c0 1 0 1 1 2l1 1v1c0 1-1 0-1 1-1 1-1 1-2 1h-1-3v1l-1-1c-2 1-1 0-3 0-1 0-1 1-3 1l1-1c0-2 0-2-1-3l1-1 2-1 2-2 1 3c1-2 3-3 5-3l2-2c1 0 1 0 2-1l1-2h2l2-2v-1c2-1 3-1 3-3z" class="I"></path><path d="M555 712v-1h-1l1-1v-1c0-2 1-2 2-2l-1 2v1c2 0 1-2 2 0v2h-3z" class="h"></path><path d="M576 691h3c1 1 0 1 1 1v5 4c-1-1-2-1-3-3-1 0-1 0-1-1v-1l2 1c0-1 0-1 1-2l-3-4zm-22 17c1-2 3-3 5-3-1 0-1 1-2 2-1 0-2 0-2 2v1l-1 1h1v1 1l-1-1c-2 1-1 0-3 0-1 0-1 1-3 1l1-1c0-2 0-2-1-3l1-1 2-1 2-2 1 3z" class="n"></path><path d="M551 707l2-2 1 3c-1 2-3 2-5 4 0-2 0-2-1-3l1-1 2-1z" class="X"></path><path d="M568 703l1-2c0-1 1-2 2-2h1v-2l1-1c1 0 1 1 2 2h0 2c1 2 2 2 3 3v1 1c0 1 1 3 0 5v3c1 2 1 4 1 7l-1 1v-1l-2 1c-1-1-2-2-4-3-3-2-5-3-9-3-2-1-4 0-6 0v-1c1 0 1 0 2-1 0-1 1 0 1-1v-1l-1-1c-1-1-1-1-1-2l3-2h0l2-2v-1c2 0 2 1 3 2z" class="l"></path><path d="M568 703l2 1v3h-2v-4zm6-1c-1-1-1-1-1-2 1 0 2-1 3 0l1 2h-1-2z" class="Z"></path><path d="M565 704c1 0 1 1 2 1-1 2-1 2-3 4l-1-1c1-1 1-1 1-2l1-2z" class="t"></path><path d="M577 698c1 2 2 2 3 3v1c-1 0-1 0-1 1l-3 2-2-2v-1h2 1l-1-2 1-2z" class="I"></path><path d="M576 705l3-2c0-1 0-1 1-1v1c0 1 1 3 0 5v3c1 2 1 4 1 7l-1 1v-1l-2 1c-1-1-2-2-4-3-3-2-5-3-9-3-2-1-4 0-6 0v-1c1 0 1 0 2-1 0-1 1 0 1-1 3 2 5 1 7 2 1-1 1-2 2-2l2-2c-1 0-2-1-2-1 0-1 1-1 2-2 1 1 2 1 3 2l-1 2c1 0 1 1 2 0l1-1h0c0-2-1-2-2-3z" class="Y"></path><path d="M559 712c1 0 1 0 2-1 0-1 1 0 1-1 3 2 5 1 7 2h3 1l1 2h1 1c1 2 2 3 4 4l-2 1c-1-1-2-2-4-3-3-2-5-3-9-3-2-1-4 0-6 0v-1z" class="b"></path><path d="M571 710l2-2c-1 0-2-1-2-1 0-1 1-1 2-2 1 1 2 1 3 2l-1 2c1 0 1 1 2 0l1-1v2l2 1c0 2 0 2-1 3l-2 1-1-3c-1 0-2 1-3 0s-1-1-1-2h-1z" class="I"></path><path d="M558 712h1v1c2 0 4-1 6 0 4 0 6 1 9 3 2 1 3 2 4 3l2-1v1l1-1h1v-2c0 2 0 5 1 7 1 6 2 11 1 16l-1 1v4l-1 1v-2l-1-1h-1v-1c0-1-1-2-2-3 0 1-1 3-2 4v2l-4 4h-1c-2 1-3 2-5 3l-1-1h-3l2 3c-1 0-1 1-2 1-2 0-3 0-4 1l1 1-2 2-1-1c-1 1-1 1-1 2l-1-1v-2-1c-1-1 0-1 0-2h0-1-1c-3-1-7-3-10-6-3-2-6-5-8-9v-1l2-1 1-1-1-1 1-1v-1-2h3v-1-3-3-1-2h1 2 1l1-1v-2h0c0-2 2-3 3-4 2 0 2-1 3-1 2 0 1 1 3 0l1 1v-1h3z" class="X"></path><path d="M573 734h1l1-1v3h0c-1 1-2 3-2 4-3 0-3 1-4-1 1 1 1 1 2 1v-2-1-2h0l2-1z" class="b"></path><path d="M549 736v-2c1 0 1 1 2 1l2 1c0 1 1 3 1 4h1c-1 0 0 1-2 0l-1 1c-2-1-2-3-3-5z" class="J"></path><path d="M561 738c1 0 1 0 2-1h0 1l2 2c0-1 1-1 1-2l4 1v2c-1 0-1 0-2-1 1 2 1 1 4 1-1 1-2 2-2 1h-2c-1 1-1 2-1 3l-1 1c-1-1-1-1-1-2-1 1-4 1-5 1 0 0-1-1-2-1h-1l1-1h1v-1l-1-1h0l2-2z" class="E"></path><path d="M561 738c1 0 1 0 2-1h0 1l2 2c0-1 1-1 1-2l4 1v2c-1 0-1 0-2-1l-1-1c0 1 0 3-2 3-1 0-2 0-2-1l-2 1-1-1-1 1-1-1h0l2-2z" class="h"></path><path d="M554 729c2 1 1 2 3 2l1-2 1 1v1h1c2 1 2 1 2 2l1 1h0l3 2c-1 1-1 1-2 1h-1 0c-1 1-1 1-2 1l-2 2h0l-1-1v-1-1c-2 0-3 0-5-1h0l-2-1c1-1 1-2 1-3 0-2 1-2 2-3z" class="d"></path><path d="M562 733l1 1h0l3 2c-1 1-1 1-2 1h-1 0c-1 1-1 1-2 1l1-2v-1h-2v-1l2-1z" class="I"></path><path d="M554 729c2 1 1 2 3 2 0 1-1 3 0 4 0-1 1-2 2-3v1 1c-1 1-1 2-1 3-2 0-3 0-5-1h0l-2-1c1-1 1-2 1-3 0-2 1-2 2-3z" class="j"></path><path d="M552 732c1 0 2 0 3 1l-1 1v1l-1 1h0l-2-1c1-1 1-2 1-3z" class="J"></path><path d="M563 723c1 0 3-1 4 0l4 1-1 2c1 1 2 1 3 1 1 2 1 3 1 4 1 1 1 1 1 2l-1 1h-1l-2 1h0v2 1l-4-1c0 1-1 1-1 2l-2-2c1 0 1 0 2-1l-3-2h0l-1-1c0-1 0-1-2-2h-1v-1l-1-1 1-2h1v-2l3-2z" class="k"></path><path d="M568 732l1-1h1v3h0c1 0 1 1 1 1h0v2 1l-4-1c0-1 0-2 1-3h0v-2z" class="l"></path><path d="M565 732h3v2h0c-1 1-1 2-1 3s-1 1-1 2l-2-2c1 0 1 0 2-1l-3-2v-2h2z" class="R"></path><path d="M565 725c1 0 4 0 5 1s2 1 3 1c1 2 1 3 1 4 1 1 1 1 1 2l-1 1h-1l-2-1c1-1 1-1 2-1l-1-3h-2l-1 1h-1l-1-2h1l-1-1c0-1-1-1-2-2z" class="d"></path><path d="M563 723c1 0 3-1 4 0l4 1-1 2c-1-1-4-1-5-1 1 1 2 1 2 2l1 1h-1l-1 1-1 1v2h-2v2h0l-1-1c0-1 0-1-2-2h-1v-1l-1-1 1-2h1v-2l3-2z" class="K"></path><path d="M565 725c1 1 2 1 2 2l1 1h-1l-1 1-1 1v2h-2v2h0l-1-1c0-1 0-1-2-2l3-2v-1h-1v-1c1 0 2-1 3-2z" class="L"></path><path d="M567 727l1 1h-1l-1 1h-1l1-2h1z" class="Z"></path><path d="M565 713c4 0 6 1 9 3 2 1 3 2 4 3l2-1v1l1-1h1v-2c0 2 0 5 1 7 1 6 2 11 1 16l-1 1v4l-1 1v-2l-1-1h-1v-1c0-1-1-2-2-3 1-5 0-9-3-14l-2 2v1c-1 0-2 0-3-1l1-2 1-1-1-2c-2-1-3-1-5-2h0-5v-2c1-1 1-1 2-1h2l1-1-1-2z" class="F"></path><path d="M571 721c1 1 3 2 4 3l-2 2v1c-1 0-2 0-3-1l1-2 1-1-1-2h0z" class="G"></path><path d="M571 718c2 0 3 1 4 2h1l1 1c1 0 1 1 1 2 1 1 0 2 0 3l-1 1v-3-1l-6-4v-1z" class="B"></path><path d="M582 716c0 2 0 5 1 7 1 6 2 11 1 16l-1 1v-1-5c-1-6-1-11-5-15l2-1v1l1-1h1v-2z" class="G"></path><path d="M565 713c4 0 6 1 9 3 1 1 2 3 2 4h-1c-1-1-2-2-4-2v1 2h0c-2-1-3-1-5-2h0-5v-2c1-1 1-1 2-1h2l1-1-1-2z" class="w"></path><path d="M563 716h2l-1 2c-1 0-2-1-3-1 1-1 1-1 2-1z" class="q"></path><path d="M566 719c0-1 1-2 1-3h3 1 0v1h-1l1 1v1 2h0c-2-1-3-1-5-2z" class="i"></path><defs><linearGradient id="S" x1="542.541" y1="725.763" x2="551.287" y2="711.631" xlink:href="#B"><stop offset="0" stop-color="#0f0e0d"></stop><stop offset="1" stop-color="#2d2d2b"></stop></linearGradient></defs><path fill="url(#S)" d="M558 712h1v1c2 0 4-1 6 0l1 2-1 1h-2c-1 0-1 0-2 1v2h5 0c2 1 3 1 5 2l1 2-1 1-4-1c-1-1-3 0-4 0l-3 2v2h-1l-1 2-1 2c-2 0-1-1-3-2-1 1-2 1-2 3 0 1 0 2-1 3-1 0-1-1-2-1 0-2 0-4 1-5l-1-1-4-2-1 2-1-1-2 1v2h-1v-1-3-3-1-2h1 2 1l1-1v-2h0c0-2 2-3 3-4 2 0 2-1 3-1 2 0 1 1 3 0l1 1v-1h3z"></path><path d="M540 726l1-1h2v1 1l-2 1v2h-1v-1-3z" class="J"></path><path d="M545 726s0-1 1-1v-2l1-1c2-4 5-6 9-8-1 2-2 3-3 4 0 0-1 0-1 1-1 1-2 2-3 2v1h3 3c-2 2-4 4-5 7l-1-1-4-2z" class="L"></path><path d="M559 713c2 0 4-1 6 0l1 2-1 1h-2c-1 0-1 0-2 1v2h5c-5 0-7 1-11 3h-3-3v-1c1 0 2-1 3-2 0-1 1-1 1-1 1-1 2-2 3-4l3-1z" class="R"></path><path d="M559 713c2 0 4-1 6 0l1 2-1 1h-2c-1 0-1 0-2 1l-1-1c-2 0-2 1-3 2h-2 0-2c1-1 2-2 3-4l3-1z" class="M"></path><path d="M565 713l1 2-1 1h-2l1-2 1-1z" class="T"></path><path d="M555 722c4-2 6-3 11-3h0c2 1 3 1 5 2l1 2-1 1-4-1c-1-1-3 0-4 0l-3 2v2h-1l-1 2-1 2c-2 0-1-1-3-2-1 1-2 1-2 3 0 1 0 2-1 3-1 0-1-1-2-1 0-2 0-4 1-5 1-3 3-5 5-7z" class="s"></path><path d="M557 725c2-1 3-2 6-2l-3 2v2h-1l-2-2z" class="P"></path><path d="M554 729l3-4 2 2-1 2-1 2c-2 0-1-1-3-2z" class="G"></path><path d="M545 726l4 2 1 1c-1 1-1 3-1 5v2c1 2 1 4 3 5v1c4 3 8 6 13 5 4 0 8-2 11-5v2l-4 4h-1c-2 1-3 2-5 3l-1-1h-3l2 3c-1 0-1 1-2 1-2 0-3 0-4 1l1 1-2 2-1-1c-1 1-1 1-1 2l-1-1v-2-1c-1-1 0-1 0-2h0-1-1c-3-1-7-3-10-6-3-2-6-5-8-9v-1l2-1 1-1-1-1 1-1v-1-2h3 1v-2l2-1 1 1 1-2z" class="V"></path><path d="M553 747h2c-1 1-1 1-1 2l5 1v-1h0c1 0 1 0 2-1v1 1c1 1 0 1 1 0l2 3c-1 0-1 1-2 1-2 0-3 0-4 1l1 1-2 2-1-1c-1 1-1 1-1 2l-1-1v-2-1c-1-1 0-1 0-2h0-1-1c0-3-2-2-3-3 0-1 1-1 1-2 0 0 2 0 3-1z" class="l"></path><path d="M559 750v-1h0c1 0 1 0 2-1v1 1c1 1 0 1 1 0l2 3c-1 0-1 1-2 1-2 0-3 0-4 1l-1 1-1-1 2-2c-2 0-1 0-2-1 2-1 2-1 3-2z" class="R"></path><path d="M545 736h2l-1 2h1l2-2c1 2 1 4 3 5v1l-2 1-1-1c-1 0-1 1-2 2h0l2 2v-2h1l3 3c-1 1-3 1-3 1 0 1-1 1-1 2 1 1 3 0 3 3-3-1-7-3-10-6v-4h-1l1-2c-1-1-2-1-2-2l1-1 1 1c1-1 2-1 3-3h0z" class="Z"></path><path d="M545 726l4 2 1 1c-1 1-1 3-1 5v2l-2 2h-1l1-2h-2 0c-1 2-2 2-3 3l-1-1-1 1c0 1 1 1 2 2l-1 2h1v4c-3-2-6-5-8-9v-1l2-1 1-1-1-1 1-1v-1-2h3 1v-2l2-1 1 1 1-2z" class="Y"></path><path d="M541 730v-2l2-1 1 1v2c-1 1-1 2-2 3l-1-3z" class="s"></path><path d="M537 735h2l-1 1h-1l1 1v1c0 1 0 1 1 2h1c0 1 0 1-1 2l2 1h1v4c-3-2-6-5-8-9v-1l2-1 1-1z" class="k"></path><path d="M545 726l4 2 1 1c-1 1-1 3-1 5v2l-2 2h-1l1-2h-2l-1-1-1 1c-1 0-1 0-2 1l-1-1c1-1 1-2 3-2 1-2 1-2 1-4v-2l1-2z" class="I"></path><path d="M456 700v7l1-1v5h0 5c1 0 2-1 3-1 1-1 1-1 2-1l1 2c1 0 1-1 2-1 1-1 1-1 2-1l1-1h1 0c1 0 2 1 2 1l1 1v-1-4h1v2c1 2 4 3 4 5h-3c2 2 5 3 6 6 1-1 2-2 3-2v2h1 1c1-1 2-2 2-3v-1-1c0-2-2-3-3-5l1-1h3c1 0 3 2 4 3h0c0 3 1 3 1 5h1c0 1 1 2 1 3l2 1c1 0 1 0 2-2v-2c0-2 1-3 2-3 1 1 1 3 1 5 0 5-2 9-4 13v1c0 2-1 3 0 4 1 0 2 0 3-1v-1c1-1 1-1 1-2h0l1 5v3h0c-4 1-6 5-8 8 0 0-1 2-2 2-1 1-1 2-1 3v1c-3 0-4 0-6 2l-1 1v3c1 1 0 0 0 1s1 1 1 2h-2c0 1 1 1 0 2h-1-1l1 5h0c-1 9-3 16-9 21v2c-1 1-2 3-3 4h-2l-2 3c-1 2-4 3-5 5h-1 0-2c-2 1-3 3-4 3-2 1-3 2-5 3-1 1-1 0-1 1h-2-1c-1 1-2 1-3 2-2 0-2 1-4 2h-4l-3-2-1 1-3 1h-4-1-2c-2 0-3 0-4-1h-1l2-3 3-3 2-2 1-1c2-3 4-6 5-9s4-7 6-10c2-2 3-5 4-7 2-3 2-6 4-9l-1-2-1 1c0 1-1 2-1 3h-1l2-5c3-6 4-13 4-20l3-32c1 0 1 0 2-1h0c0-2 0-4 1-5v-1l-1-1v-2l1-5z" class="v"></path><path d="M481 759h1c1 1 1 1 1 3h-2l-1-1 1-2z" class="o"></path><path d="M470 794c3-1 6-2 9-4v2c-1 1-2 3-3 4h-2v-2l-1 1h-2l-1-1z" class="S"></path><path d="M467 757h1 1c1 1 3 1 5 2h2c2 3 3 5 4 9h-2v-2c0-1-1-2-1-3-2 0-2 0-3-1h0v-1h-1c-2-3-3-2-6-4z" class="r"></path><path d="M482 742c3-4 3-6 4-11 1 2 1 5 1 6s-1 2-1 2c-1 2 0 2-2 4v1c-4 1-4 5-9 4 2-1 4-3 6-5l1-1z" class="W"></path><path d="M474 762h0c1 1 1 1 3 1 0 1 1 2 1 3v2h2c1 4 0 10-2 13-2 5-7 5-10 8h-2v1l-2-1c1-1 1-1 2-1l1-1h1c2-1 3-1 4-2h1l1-1c1 0 3-3 3-4h0c1-3 0-6 2-9l-1-1c0-2-2-2-3-3-1-2-1-3-1-5z" class="F"></path><path d="M477 719l1 1c3 1 6 3 7 6 1 2 1 4 1 5-1 5-1 7-4 11 0-3 1-4 2-6 0-3 0-6-1-8-1-1-1-1-1-2-2-2-5-2-7-3h-1l1-1v-1h1l1-2z" class="L"></path><path d="M477 719l1 1 1 3h-2-2-1l1-1v-1h1l1-2z" class="Y"></path><path d="M457 751l2 1c5 4 12 2 17 7h-2c-2-1-4-1-5-2h-1-1c-1 0-1-1-2-1h-2c-2-2-7-2-9-2-2 2-3 6-5 8 1-4 1-7 4-11 2 0 1 2 3 2 1-1 1-1 1-2z" class="Q"></path><path d="M449 762c2-2 3-6 5-8 2 0 7 0 9 2h2c1 0 1 1 2 1 3 2 4 1 6 4-3 0-5-2-8-3h-1c-2 0-7-1-9 0-1 0-1 1-2 2h2v1c-1 2-2 3-3 4h0c-1 1-3 2-3 3s0 1-1 2l-1-2c1-2 1-4 2-6z" class="m"></path><defs><linearGradient id="T" x1="460.068" y1="727.803" x2="474.322" y2="721.76" xlink:href="#B"><stop offset="0" stop-color="#2f2d2d"></stop><stop offset="1" stop-color="#4e4e4a"></stop></linearGradient></defs><path fill="url(#T)" d="M461 728c1-3 3-4 5-6 3-2 7-3 11-3l-1 2h-1v1l-1 1-1 1v1c-2 1-3 1-3 4l-1 1h-3v-1c-1 1-2 0-2 2l-2 2h-2l-1 1 2-6z"></path><path d="M473 724v1c-2 1-3 1-3 4l-1 1h-3v-1l3-3c1-2 2-2 4-2z" class="C"></path><path d="M456 700v7l1-1v5h0 5c1 0 2-1 3-1 1-1 1-1 2-1l1 2c1 0 1-1 2-1 1-1 1-1 2-1l1-1h1 0c1 0 2 1 2 1l1 1v-1-4h1v2c1 2 4 3 4 5h-3c-4-1-7-1-11 0h0c-4 2-7 4-10 7h0 0c-1 0-1 1-2 1l-1-1c-1-1 0-3 0-5h0c0-2 0-4 1-5v-1l-1-1v-2l1-5z" class="U"></path><path d="M477 705h1v2c1 2 4 3 4 5h-3c-4-1-7-1-11 0h0c-3 0-4 1-6 2l-2-2-2 1-1-2h5c1 0 2-1 3-1 1-1 1-1 2-1l1 2c1 0 1-1 2-1 1-1 1-1 2-1l1-1h1 0c1 0 2 1 2 1l1 1v-1-4z" class="C"></path><path d="M453 715c1 0 1 0 2-1 0 2-1 4 0 5l1 1c1 0 1-1 2-1h0 0c0 2 0 3 1 4 0 1-1 3-1 4 0 2-1 3-1 5 0 3 0 6-1 9 1 3 1 7 3 11l-2-1c0 1 0 1-1 2-2 0-1-2-3-2-3 4-3 7-4 11-1 2-1 4-2 6l-1 1c0 1-1 2-1 3h-1l2-5c3-6 4-13 4-20l3-32z" class="G"></path><path d="M458 719h0c0 2 0 3 1 4 0 1-1 3-1 4h0-4c1-3 2-5 4-8z" class="O"></path><path d="M454 727h4c-2 5-3 11-4 16-1-5-1-11 0-16z" class="B"></path><path d="M458 727h0c0 2-1 3-1 5 0 3 0 6-1 9 1 3 1 7 3 11l-2-1c-1-2-2-5-3-8 1-5 2-11 4-16z" class="N"></path><path d="M455 760h-2c1-1 1-2 2-2 2-1 7 0 9 0h1c3 1 5 3 8 3h1v1c0 2 0 3 1 5 1 1 3 1 3 3l-2-1c-1 0-1 1-2 1h-1v-2h-3l-1 1c-1 1-2 0-3 0-1 1-3 3-4 3v1h-1c-2 0-3 1-4 0v-1h2c-2-1-2-2-3-3l-4-2v-2h0c1-1 2-2 3-4v-1z" class="H"></path><path d="M455 760h1l2 1s1-1 1-2c1 0 2 1 3 1 2 1 3 2 5 3s2 1 3 3h0c-1 0-1 0-2-1h0l-2 1c0-1 0-1-1-2-2-2-3-2-6-2l-3 2h-1v-3-1z" class="O"></path><path d="M460 763h1c1 0 2 1 3 2v4c-1 0-2 1-3 1h-2c-2-2-2-1-2-4l3-3z" class="v"></path><path d="M455 760h-2c1-1 1-2 2-2 2-1 7 0 9 0h1c3 1 5 3 8 3h1v1c0 2 0 3 1 5h-2c-1-1-4-3-5-4h-1c-2-1-3-2-5-3-1 0-2-1-3-1 0 1-1 2-1 2l-2-1h-1z" class="C"></path><path d="M474 723h1c2 1 5 1 7 3 0 1 0 1 1 2 1 2 1 5 1 8-1 2-2 3-2 6l-1 1c-2 2-4 4-6 5-4 1-6 1-9 0l-3-3-2-3c-1-3-2-5-2-8l1-1h2l2-2c0-2 1-1 2-2v1h3l1-1c0-3 1-3 3-4v-1l1-1z" class="D"></path><path d="M483 728c1 2 1 5 1 8-1 2-2 3-2 6l-1 1h-1c-2 1-3 1-5 1l2-2c1 0 2 0 4-1 1-1 0-1-1-2 0-2 1-3 2-4v-2c1-2 1-3 1-5z" class="F"></path><path d="M466 741l4 3h5c2 0 3 0 5-1h1c-2 2-4 4-6 5-4 1-6 1-9 0l-3-3h3v-4z" class="Y"></path><path d="M466 741l4 3c1 2 1 2 1 3l-5 1-3-3h3v-4z" class="b"></path><path d="M462 733l2-2c0-2 1-1 2-2v1 1c-1 1-1 2-2 3 1 2 1 4 2 7h0v4h-3l-2-3c-1-3-2-5-2-8l1-1h2z" class="E"></path><path d="M459 734l1-1h2v3c0 2-1 2 0 4h1 1v1 1h-3c-1-3-2-5-2-8z" class="G"></path><path d="M473 725c2 0 3-1 5 0 1 1 1 2 2 3h0c-1 1-1 2-1 3-1 0-2 1-3 1v1 1c1-1 2-1 2-1 1 0 1 0 2 1 0 1-1 1-1 2l-1 1-1-1-1 1 1 2h-1c0 1-1 2-1 3h-1-1-3v-1l2-1v-1c-2 0-2 1-3 0s-1-2-1-3l-2-1c0-1 1-1 2-2-1-1-1-1-1-2h2v-1l1-1c0-3 1-3 3-4z" class="N"></path><path d="M490 707h3c1 0 3 2 4 3h0c0 3 1 3 1 5h1c0 1 1 2 1 3l2 1c1 0 1 0 2-2v-2c0-2 1-3 2-3 1 1 1 3 1 5 0 5-2 9-4 13v1c0 2-1 3 0 4 1 0 2 0 3-1v-1c1-1 1-1 1-2h0l1 5v3h0c-4 1-6 5-8 8 0 0-1 2-2 2-1 1-1 2-1 3v1c-3 0-4 0-6 2l-1 1v3c1 1 0 0 0 1s1 1 1 2h-2c0 1 1 1 0 2h-1-1c-1-4-3-7-6-10h-1l-1-1-2-1c-1 0-3-2-4-2s-1 1-2 1v-1h-2c-4-1-7-3-9-7-1-2-2-8-1-11v-1c0-2 0-2 1-4l1 1-2 6c0 3 1 5 2 8l2 3 3 3c3 1 5 1 9 0 5 1 5-3 9-4-1 1 0 1 1 2l1-1c0-1 1-2 2-3 0-1 0 0 1-1 0-1 1-2 1-3h1c1-3 1-5 1-8-1-5-4-9-7-12 1-1 2-2 3-2v2h1 1c1-1 2-2 2-3v-1-1c0-2-2-3-3-5l1-1z" class="g"></path><path d="M490 707h3c1 0 3 2 4 3h0c0 3 1 3 1 5l1 2c0 3 1 8 0 10h-1 0l-2-1v1h-1c1-6 1-10-3-14 0-2-2-3-3-5l1-1z" class="v"></path><path d="M492 746l1 1-1 1s-1 1-1 2h0v2c-1 1-1 2-2 3l1 1v3c1 1 0 0 0 1s1 1 1 2h-2c0 1 1 1 0 2h-1-1c-1-4-3-7-6-10l5-3c2-2 4-3 6-5z" class="I"></path><path d="M492 746l1 1-1 1s-1 1-1 2h0v2c-1 1-1 2-2 3-1 0-2 1-2 1-1 0-2-1-3-1 2-2 2 0 4-1 0-1-1-2-2-3 2-2 4-3 6-5z" class="Y"></path><path d="M492 713c4 4 4 8 3 14l-1 3c0 1 0 3-1 3l-1-3c-1-5-4-9-7-12 1-1 2-2 3-2v2h1 1c1-1 2-2 2-3v-1-1z" class="R"></path><path d="M492 715c1 1 2 2 2 4l-1 1v-1h-2c-1 0-1 0-2 1-1-1-1-1-1-2h1 1c1-1 2-2 2-3z" class="T"></path><path d="M494 719c0 3 0 6-1 10 0-1 0-1-1-2 0 0 0-1-1-2 0-1-1-2 0-3l1-1 1-2v1l1-1z" class="w"></path><path d="M492 713c4 4 4 8 3 14l-1 3-1-1c1-4 1-7 1-10 0-2-1-3-2-4v-1-1z" class="L"></path><path d="M506 734v-1c1-1 1-1 1-2h0l1 5v3h0c-4 1-6 5-8 8 0 0-1 2-2 2-1 1-1 2-1 3v1c-3 0-4 0-6 2l-1 1-1-1c1-1 1-2 2-3v-2h0c0-1 1-2 1-2l1-1-1-1s4-6 5-6l6-10v1c0 2-1 3 0 4 1 0 2 0 3-1z" class="R"></path><path d="M497 740l2 2c0 1-1 1 0 2-1 2-3 5-5 5-1 1-1 2-1 2l-2 1v-2h0c0-1 1-2 1-2l1-1-1-1s4-6 5-6z" class="I"></path><path d="M497 740l6-10v1c0 2-1 3 0 4 1 0 2 0 3-1v2l-2 1v2h-1v1c-1 2-3 3-4 4-1-1 0-1 0-2l-2-2z" class="Q"></path><path d="M452 765v2l4 2c1 1 1 2 3 3h-2v1c1 1 2 0 4 0h1v-1c1 0 3-2 4-3 1 0 2 1 3 0l1-1h3v2h1c1 0 1-1 2-1l2 1 1 1c-2 3-1 6-2 9h0c0 1-2 4-3 4l-1 1h-1c-1 1-2 1-4 2h-1l-1 1c-1 0-1 0-2 1l2 1v-1h2c-1 1-2 1-2 3l2 2h2l1 1h2l1-1v2l-2 3c-1 2-4 3-5 5h-1 0-2c-2 1-3 3-4 3-2 1-3 2-5 3-1 1-1 0-1 1h-2-1c-1 1-2 1-3 2-2 0-2 1-4 2h-4l-3-2-1 1-3 1h-4-1-2c-2 0-3 0-4-1h-1l2-3 3-3 2-2 1-1c2-3 4-6 5-9s4-7 6-10c2-2 3-5 4-7 2-3 2-6 4-9 1-1 1-1 1-2s2-2 3-3z" class="a"></path><path d="M452 767l4 2c-1 0-2 0-3 1h0c-1 0-2 0-2 1l1-4z" class="D"></path><path d="M454 797c0 2 1 3 1 4l-2 1v1c-1-2-1-2-1-3 0-2 1-2 2-3z" class="o"></path><path d="M454 797c2 0 2-1 4 0 0 1 1 1 1 2h-2c-1 1-1 2-2 2 0-1-1-2-1-4z" class="g"></path><path d="M466 804v-1c1-3 3-3 6-4-1 2-4 3-5 5h-1zm1-32h1v1c0 1 0 2-1 3h-4-1v-1l1-1 2 1 2-3z" class="B"></path><path d="M459 799v3c-1 0-1 1-2 1h-4v-1l2-1c1 0 1-1 2-2h2z" class="v"></path><path d="M473 795c-2 1-4 1-7 1-2-1-2-2-3-4l1-1c1 2 2 3 4 3h2l1 1h2z" class="w"></path><path d="M452 765v2l-1 4c-2 2-2 3-4 5-1 1-1 2-1 3-1 1-2 2-2 4l-3 3-1 2c0 2-2 2-2 4-2 1-1 3-3 4h-1c1-3 4-7 6-10 2-2 3-5 4-7 2-3 2-6 4-9 1-1 1-1 1-2s2-2 3-3z" class="U"></path><path d="M436 814c-1 0-2-1-2-2-2 0-2 1-4 1v-2c2-1 2 0 4 0v-2h-1-3l1-1c0-1 0-1 1-2 1-2 1-4 3-6 1-1 1-1 1-2l1-1 1-1c0-1 0-2 1-3s3-2 3-4c1-2 2-3 3-5s2-3 3-5v1l1-1c1-3 2-5 4-8v5c-1 0-1 0-2 1h2v-1l2 2c1-1 1-3 2-3h1c-1 2-1 3-1 4v1h-1l-1-1h0l-1 2c1 1 2 2 2 3s0 3-1 3l-1 1c1 0 2 1 3 2s2 3 4 4c0 1 0 2-1 3v-1c-1 0-1 1-1 0l-2-1c-2 0-3 0-4 1s-3 3-3 5v1h-1l-2-2h-1l-2 4h1l2-1c2 0 2 1 4 0h0c1 1 3 2 4 2h2c1 0 2 1 3 1v1c-2 1-3 2-5 3-1 1-1 0-1 1h-2-1c-1 1-2 1-3 2-2 0-2 1-4 2h-4l-3-2-1 1z" class="e"></path><path d="M437 813v-2l-1-1c0-1-1-1-2-2 0-2 0-3 2-5l1 1-1 1c-1 2 0 3 1 5h2 0c2-1 2 0 3 1 1 0 2 1 2 1l2-1c1-1 2-1 3-2h0l1-1-1-1v-1h1c2 1 4 0 5-1h2c1 0 2 1 3 1v1c-2 1-3 2-5 3-1 1-1 0-1 1h-2-1c-1 1-2 1-3 2-2 0-2 1-4 2h-4l-3-2z" class="N"></path><defs><linearGradient id="U" x1="518.302" y1="842.764" x2="519.904" y2="800.725" xlink:href="#B"><stop offset="0" stop-color="#080808"></stop><stop offset="1" stop-color="#2e2d2a"></stop></linearGradient></defs><path fill="url(#U)" d="M614 795l1 2c4 4 8 9 12 12h2l14 8c3 1 7 3 11 4 9 2 17 3 26 4h5c1 2 1 10 1 12H353h-1c2-2 1-8 1-10h1l-1 8h51 4 25c3 0 7 0 10-1h8l22-3v-1c10-4 20-7 28-15 4-3 6-7 9-11 1-1 2-3 3-5l2-2c0-1 1-1 2-1v1 1c-1 0-2 1-2 2l1 2c3 4 5 8 8 11s7 7 10 9c0-2 0-3 1-4l1 1 1-2c-1-1-1-1-1-2l-1-1h2c2 0 2 1 3 2 2 1 3 1 4 1l1-1h0 3l1 1h0c1 0 2 0 2 1h1 1 0v-1c-1 0-1-1-1-1-2-1-2-2-2-3 0 1 1 2 2 2 1 1 2 1 2 2l2-1h0c0-1 1-2 2-3-1 0-2-1-3-1v-1l3-1h3c1 1 2 1 4 2 2 0 1 1 2 1 1 1 2 0 4 1 1 0 2 0 3 1v-2h2l1 1c1 0 1-1 2-1l1 1c2 0 2 1 4 0l1 1 2 2h1c2 0 2-1 3-1h4c1-1 2-1 3 0 2-1 2 0 3 0v1l4-1h1c0-1 0-2-1-2v-1l2-1c0 1 0 0 1 1 0-1 2-3 3-4 1 0 2 0 3-1 0-1-1-2-2-3l-3-3-2-2 1-1h2v1-1c-1-1-2-2-4-3 2-1 3-1 5-1h3z"></path><path d="M502 832h0-2c-1 0-2 1-3 1s-1 1-2 1-3-1-3-2c-1 0-1-1-1-2l4-3h1c-1 2-2 2-2 4 1 1 1 1 2 1 2-1 4-1 6 0z" class="X"></path><path d="M515 826c1-1 0-1 1-2h3 1l-1 3h1 0c0-1 0-1 1-2l3 3v1h1l2-3 1 1c0 1 1 1 2 1 1-1 0 0 0-2 2 0 2 1 4 1v-3l8 3v2h0c-1-2-2-2-3-2l-1 1h0l-1-1c-1 1-2 1-3 1-2 0-2-1-3-1 0 2 0 2-2 3-2-1-1-3-3-2v2h-1c-4-2-5-1-9 0h-2c-1 1-1 1-2 1l-1-1 1-1c1 0 1-2 1-3l2-1v1z" class="K"></path><path d="M515 826c1-1 0-1 1-2h3 1l-1 3h1 0c0-1 0-1 1-2l3 3h-2-3-3-1l1-1-1-1z" class="Q"></path><path d="M523 814l8 8h1c1 0 2 1 2 2v3c-2 0-2-1-4-1 0 2 1 1 0 2-1 0-2 0-2-1l-1-1-2 3h-1v-1l-3-3c-1 1-1 1-1 2h0-1l1-3h0c0-1-1-1-1-2s0-1 1-2l1 2v-1-1l1-1c1 0 1 1 2 1-1-1-1-1-1-2l-1-1c0-1 1-2 1-3z" class="Y"></path><path d="M531 822h1l-1 1c-1 0-2 1-3 1-1 1-1 1-2 1s-1-1-1-2h4c0-1 1-1 2-1h0z" class="Q"></path><path d="M523 814l8 8h0c-1 0-2 0-2 1h-4c-1 0-1-1-1-1 1 0 1 0 1-1v-1h-1c-1-1-1-1-1-2l-1-1c0-1 1-2 1-3z" class="d"></path><path d="M513 799l2-2c0-1 1-1 2-1v1 1c-1 0-2 1-2 2l1 2c3 4 5 8 8 11s7 7 10 9c11 5 21 8 32 11-2 0-5 0-7-1l-3-1h-1c-2-1-3-1-5-1h0 0c-2 0-4 0-6-1h-2v-2l-8-3c0-1-1-2-2-2h-1l-8-8c-1 0 0 0-1-1l-3-3h-4-1l-1 1c-1-1-1-1 0-2v-3l1-1c2 0 2 0 3 1h0c0-1-1-2-1-3h-1v-1c0-1-1-2-1-2l-3 5v4h-1v-5c1-1 2-3 3-5z" class="u"></path><path d="M515 810h4l3 3c1 1 0 1 1 1 0 1-1 2-1 3l1 1c0 1 0 1 1 2-1 0-1-1-2-1l-1 1v1 1l-1-2c-1 1-1 1-1 2s1 1 1 2h0-1-3c-1 1 0 1-1 2v-1l-2 1c0 1 0 3-1 3l-1 1 1 1c1 0 1 0 2-1h2s1 1 2 1l1 1c1 0 3 0 5-1h2c1-1 2-1 3-1l1 1c1 0 2 0 3 1 0-1 0-1 1-1 2-1 4 0 6 0h1c-3 1-5 0-9 2h0c-2-1-4-1-6-1v1h-1c-2-1-4 0-5 0-2 1-3 0-5 1-2 0-7 1-9 0l-1-1h1v-1h-4c-2-1-4-1-6 0-1 0-1 0-2-1 0-2 1-2 2-4 1 0 2-1 2-2 3-1 3-1 5-3v-1h2c0-1 1-1 2-2h2v-2h1c1-1 1-2 2-2h1c-1-1-1-1-1-2h2c0-1-1-1-1-2l1-1h1z" class="j"></path><path d="M513 826c0 1 0 3-1 3l-1 1c1-1 1-1 1-2-1 1-2 1-3 2l1-2c1-1 1-1 1-2h0 2z" class="E"></path><path d="M511 822l-1-1h-2v-1h4c1 1 2 2 4 3h-1c-1-1-3 0-4-1z" class="K"></path><path d="M505 826c0 1 0 1 1 1 0 1 0 1 1 1l4-2h0c0 1 0 1-1 2l-1 2-2 1-2-1c1 0 1 0 2-1l-2-2v-1z" class="p"></path><path d="M505 830c-2 1-4 1-5 1v-3c1-1 1-1 2-1 1-1 2-2 4-3v-1h3l-1 3h-3 0v1l2 2c-1 1-1 1-2 1z" class="b"></path><path d="M516 823l1-1 2-3c0-1-1-1 0-3 0 0 1 0 1 1v1l2 1-1 1v1 1l-1-2c-1 1-1 1-1 2s1 1 1 2h0-1-3c-1 1 0 1-1 2v-1l-2 1h-2l-4 2c-1 0-1 0-1-1-1 0-1 0-1-1h0 3l1-3 2-1c1 1 3 0 4 1h1z" class="n"></path><path d="M513 826l-2-1v-1l2-1c1 1 2 1 2 2l-2 1z" class="I"></path><path d="M515 810h4l3 3c1 1 0 1 1 1 0 1-1 2-1 3l1 1c0 1 0 1 1 2-1 0-1-1-2-1l-2-1v-1c0-1-1-1-1-1-1 2 0 2 0 3l-2 3-1 1c-2-1-3-2-4-3h1c1-1 1-2 1-4l2-1c1-1 2-1 3-2l-1-1c-1-1-1-1-2-1l-1-1z" class="E"></path><path d="M588 822l4 1h6 2c2 0 4 1 5 1h32c1 0 1 1 2 1l-2 3h-1 1c-3 0-5 0-6 1 0 1 0 1 1 1l2 1c0 1 1 2 2 3l-33-1c-8 0-18 1-27 0 7-1 15 0 22 0v-2-1l1-1-2-2h-1c-2 1-3 0-5 0v-3h-8-1c2-1 4-1 6-1v-1z" class="I"></path><path d="M623 828c1-1 1-1 3-2v1h0v2h-3v-1z" class="d"></path><path d="M588 822l4 1h6 2c2 0 4 1 5 1h-14-8-1c2-1 4-1 6-1v-1z" class="J"></path><path d="M608 828c2 0 4 0 6-1 0-1 0-1 1-1 2 1 3 1 4 0h1c1 1 2 1 3 2v1h3 0c1 1 1 3 2 3l1-1c1-1 2-1 3-1l2 1c0 1 1 2 2 3l-33-1h-4c1-2 2-2 3-3l2 1h0l-2-2 1-1h2 1 2z" class="l"></path><path d="M608 828c2 0 4 0 6-1 0-1 0-1 1-1 2 1 3 1 4 0h1c1 1 2 1 3 2v1h3 0c-3 1-6 1-9 0h-1-3l-1-1-1 1-4 1-1-1 2-1z" class="h"></path><defs><linearGradient id="V" x1="661" y1="821.288" x2="655.536" y2="838.879" xlink:href="#B"><stop offset="0" stop-color="#413e3d"></stop><stop offset="1" stop-color="#6b6c6b"></stop></linearGradient></defs><path fill="url(#V)" d="M645 823c0-2-2-3-4-4 0-1-1-2-2-2v-1c1 1 2 1 3 1h1c3 1 7 3 11 4 9 2 17 3 26 4l2 2v7h-46c-1-1-2-2-2-3l-2-1c-1 0-1 0-1-1 1-1 3-1 6-1h-1 1l2-3c-1 0-1-1-2-1h8l1-1h-1z"></path><path d="M640 827h3c0 1 0 2 1 2-1 1-1 1-2 1-1 1-2 1-3 2h-1v-1l3-2v-1c-1 0-1 0-1-1z" class="I"></path><path d="M639 825v1h3l1 1h0-3c-2 1-5 3-6 4l-2-1c-1 0-1 0-1-1 1-1 3-1 6-1h-1 1l2-3z" class="h"></path><defs><linearGradient id="W" x1="638.386" y1="814.973" x2="678.758" y2="830.137" xlink:href="#B"><stop offset="0" stop-color="#070505"></stop><stop offset="1" stop-color="#3d3c3c"></stop></linearGradient></defs><path fill="url(#W)" d="M645 823c0-2-2-3-4-4 0-1-1-2-2-2v-1c1 1 2 1 3 1h1c3 1 7 3 11 4 9 2 17 3 26 4l2 2v1l-1-1h-5c-2 1-5 0-7 2-1 1-2 0-3 0l-2 2c-1-1-1 0-2-1l1-2v-1c-5-3-12-2-18-3l1-1h-1z"></path><defs><linearGradient id="X" x1="643.846" y1="809.29" x2="592.708" y2="813.594" xlink:href="#B"><stop offset="0" stop-color="#040403"></stop><stop offset="1" stop-color="#252423"></stop></linearGradient></defs><path fill="url(#X)" d="M614 795l1 2c4 4 8 9 12 12h2l14 8h-1c-1 0-2 0-3-1v1c1 0 2 1 2 2 2 1 4 2 4 4h1l-1 1h-8-32c-1 0-3-1-5-1h-2-6l-4-1c2 0 3-1 4-2 0 0 0-1-1-1h-1c0-1-1-1-2-2 2 0 2-1 3-1h4c1-1 2-1 3 0 2-1 2 0 3 0v1l4-1h1c0-1 0-2-1-2v-1l2-1c0 1 0 0 1 1 0-1 2-3 3-4 1 0 2 0 3-1 0-1-1-2-2-3l-3-3-2-2 1-1h2v1-1c-1-1-2-2-4-3 2-1 3-1 5-1h3z"></path><path d="M623 814l8 5v1l-3-1c-1 0-1 1-2 1-2 1-4 0-6 1l1-2h1c1-2 1-3 1-5z" class="c"></path><defs><linearGradient id="Y" x1="623.989" y1="812.184" x2="616.417" y2="798.648" xlink:href="#B"><stop offset="0" stop-color="#5a5856"></stop><stop offset="1" stop-color="#888687"></stop></linearGradient></defs><path fill="url(#Y)" d="M614 795l1 2c4 4 8 9 12 12h2l14 8h-1c-1 0-2 0-3-1v1c1 0 2 1 2 2 2 1 4 2 4 4-5-1-9-5-13-8l-13-9-9-6v-1c-1-1-2-2-4-3 2-1 3-1 5-1h3z"></path><path d="M614 795l1 2c0 1 1 2 1 2l-3 1h0c-1-1-2-1-3-1-1-1-2-2-4-3 2-1 3-1 5-1h3z" class="M"></path><path d="M612 805c3 2 6 6 10 8l1 1c0 2 0 3-1 5h-1l-1 2c-3 0-8 1-12 0h-4c-1 0-3 1-3 1-1 0-1-1-2 0h-3l-4 1-4-1c2 0 3-1 4-2 0 0 0-1-1-1h-1c0-1-1-1-2-2 2 0 2-1 3-1h4c1-1 2-1 3 0 2-1 2 0 3 0v1l4-1h1c0-1 0-2-1-2v-1l2-1c0 1 0 0 1 1 0-1 2-3 3-4 1 0 2 0 3-1 0-1-1-2-2-3z" class="P"></path><path d="M614 815c0 1 0 2 1 4l-1 1h0l-1-1c1-2 0-3 1-4z" class="E"></path><path d="M591 816h4c1-1 2-1 3 0-1 1-2 2-3 2l-4-2z" class="G"></path><path d="M622 813l1 1c0 2 0 3-1 5h-1 0-2l-1-1c1-1 3-1 4-2v-3z" class="J"></path><path d="M558 810h3c1 1 2 1 4 2 2 0 1 1 2 1 1 1 2 0 4 1 1 0 2 0 3 1v-2h2l1 1c1 0 1-1 2-1l1 1c2 0 2 1 4 0l1 1 2 2h1c1 1 2 1 2 2h1c1 0 1 1 1 1-1 1-2 2-4 2v1c-2 0-4 0-6 1h1 8v3c2 0 3 1 5 0h1l2 2-1 1v1 2c-7 0-15-1-22 0h-10c-11-3-21-6-32-11 0-2 0-3 1-4l1 1 1-2c-1-1-1-1-1-2l-1-1h2c2 0 2 1 3 2 2 1 3 1 4 1l1-1h0 3l1 1h0c1 0 2 0 2 1h1 1 0v-1c-1 0-1-1-1-1-2-1-2-2-2-3 0 1 1 2 2 2 1 1 2 1 2 2l2-1h0c0-1 1-2 2-3-1 0-2-1-3-1v-1l3-1z" class="Z"></path><path d="M582 830c2-1 4-1 6-2l2 2h0l1-1h0c2 0 6 1 7 1v1h0c-2 0-4-1-6 1l-1-1c-1 0-3-1-4-1h0v-1c-1 1-3 1-5 1z" class="k"></path><path d="M583 824h8v3c2 0 3 1 5 0h1l2 2-1 1c-1 0-5-1-7-1h0l-1 1h0l-2-2c-2 1-4 1-6 2h-2c0-2 0-2 1-3l1-1v-1h0l1-1z" class="h"></path><path d="M582 825h0 7v1l-1 1-2-1c-1 1-2 1-4 0v-1z" class="L"></path><path d="M582 826c2 1 3 1 4 0l2 1-1 1h1c-2 1-4 1-6 2h-2c0-2 0-2 1-3l1-1z" class="I"></path><path d="M566 823h4c1 0 4 1 5 1l7 1v1l-1 1c-1 1-1 1-1 3h-1 0l1-1-1-1h-1c-3 0-4 0-6-1h-1c-1 0-3 1-4 1l-1-1v-1l-1-1 1-2z" class="l"></path><path d="M581 827c-2-1-7-1-9-2h-1-2 0-1l1-1 1-1c1 0 4 1 5 1l7 1v1l-1 1z" class="t"></path><defs><linearGradient id="Z" x1="543.353" y1="825.073" x2="554.398" y2="815.151" xlink:href="#B"><stop offset="0" stop-color="#5a5957"></stop><stop offset="1" stop-color="#71706e"></stop></linearGradient></defs><path fill="url(#Z)" d="M536 819l1-2c-1-1-1-1-1-2l-1-1h2c2 0 2 1 3 2 2 1 3 1 4 1l1-1 5 2h1l12 4v1 1l-2 2c-1 1-3 0-3 0-1 0-2-1-3-1-4-1-9-2-13-4-2-1-3-2-6-2z"></path><path d="M537 814c2 0 2 1 3 2v1h-2l-1-3z" class="k"></path><path d="M550 818h1l-2 3h-3l-1-1 1-1h1l1-1h2z" class="Z"></path><path d="M558 810h3c1 1 2 1 4 2 2 0 1 1 2 1 1 1 2 0 4 1h-2l-2 1s-1 0-1-1c-2 1-2 1-2 3h1c2 1 4 3 6 3h1c1 0 2 1 3 1 2 0 1-1 3 1h0c-2 1-2 1-3 2-1 0-4-1-5-1h-4l-3-1-12-4h-1l-5-2h0 3l1 1h0c1 0 2 0 2 1h1 1 0v-1c-1 0-1-1-1-1-2-1-2-2-2-3 0 1 1 2 2 2 1 1 2 1 2 2l2-1h0c0-1 1-2 2-3-1 0-2-1-3-1v-1l3-1z" class="d"></path><path d="M558 810h3c1 1 2 1 4 2-1 0-2 0-3 1-2 0-3 0-3 1v1c1 0 2 0 3 2v1h2c1 1 2 2 2 3-2 0-5-1-6-2 0-1 0-1-1-2-1 0-2 0-3-1h0c0-1 1-2 2-3-1 0-2-1-3-1v-1l3-1z" class="x"></path><path d="M550 813c0 1 1 2 2 2 1 1 2 1 2 2l2-1h0 0c1 1 2 1 3 1 1 1 1 1 1 2 1 1 4 2 6 2l4 1h8 0c-2 1-2 1-3 2-1 0-4-1-5-1h-4l-3-1-12-4h-1l-5-2h0 3l1 1h0c1 0 2 0 2 1h1 1 0v-1c-1 0-1-1-1-1-2-1-2-2-2-3z" class="G"></path><path d="M574 813h2l1 1c1 0 1-1 2-1l1 1c2 0 2 1 4 0l1 1 2 2h1c1 1 2 1 2 2h1c1 0 1 1 1 1-1 1-2 2-4 2v1c-2 0-4 0-6 1h1l-1 1h0l-7-1c1-1 1-1 3-2h0c-2-2-1-1-3-1-1 0-2-1-3-1h-1c-2 0-4-2-6-3h-1c0-2 0-2 2-3 0 1 1 1 1 1l2-1h2c1 0 2 0 3 1v-2z" class="h"></path><path d="M571 814c1 0 2 0 3 1 1 0 2 1 3 1-1 0-3 1-4 1s-2 0-3-1l1-2z" class="d"></path><path d="M574 813h2l1 1c1 0 1-1 2-1l1 1c2 0 2 1 4 0l1 1 2 2h-5l-5-1c-1 0-2-1-3-1v-2z" class="X"></path><path d="M582 817h5 1c1 1 2 1 2 2h1c1 0 1 1 1 1-1 1-2 2-4 2v1c-2 0-4 0-6 1h1l-1 1h0l-7-1c1-1 1-1 3-2h0 4v-1c1-2 1-3 0-4z" class="n"></path><path d="M578 822l10 1c-2 0-4 0-6 1h1l-1 1h0l-7-1c1-1 1-1 3-2z" class="c"></path><path d="M474 591c1 1 2 1 3 2 0 1 0 2 1 2 1 1 1 2 1 3l1-1c1 0 1-1 2-2h1l3 4c3 3 5 5 8 7 2 0 3 1 5 2 1 1 2 1 3 1-2-1-4-2-5-4l-1-1c2 1 7 5 10 5 1 1 2 2 4 2v4c2 2 4 3 6 4v1c1 4 1 9 0 13 0 6 1 13 0 20l1 96v15c-1 1-1 2-1 3 0-3 0-6-2-8-1 1-1 2-2 4h0l-1 1-1 3h-1v-3c1-2 0-4 1-7v-4l-1-1 1-14-2 1v-3l-1-5h0c0 1 0 1-1 2v1c-1 1-2 1-3 1-1-1 0-2 0-4v-1c2-4 4-8 4-13 0-2 0-4-1-5-1 0-2 1-2 3v2c-1 2-1 2-2 2l-2-1c0-1-1-2-1-3h-1c0-2-1-2-1-5h0c-1-1-3-3-4-3h-3l-1 1c1 2 3 3 3 5v1 1c0 1-1 2-2 3h-1-1v-2c-1 0-2 1-3 2-1-3-4-4-6-6h3c0-2-3-3-4-5v-2h0v-4l1-1c-2 0-1 1-3 1v-2h-4c-3-1-5-4-6-6l-1-2v-1h-1 0c-2-4-2-9 0-14h-2c-2 0-2 1-3 1-2-2-2-5-2-7-1-2 0-3 0-4v-2-4-10c1-3 1-5 1-7v-29l1 4c0 2-1 2 1 4v4-1s0-1 1-2v-6c0-1 1-2 2-3h1c0-1 1-2 0-2v-3c1 0 2-1 3-1h1c0-2 1-3 2-5 1-1 1-2 1-4h0c2-1 2-2 3-3l-2-3h0-1l-1-1h3l1-1z" class="g"></path><path d="M481 618l-1 1-1-2h1c1-1 1-1 2-1v1l-1 1z" class="v"></path><path d="M482 617l1 1 1 3c-2-1-2-1-3-2v-1l1-1z" class="W"></path><path d="M498 620l3 3v3c-2-2-2-3-3-6z" class="N"></path><path d="M488 612h1c0 2 0 3 2 4 0 1 0 0 1 1 0 1 0 1 1 1l1 1-1 2s1 1 1 2l-1 1h-1l-2-2v-3h1l-1-1c-2-1-2-1-3-2 0-2 0-3 1-4z" class="a"></path><path d="M503 637c1 1 2 3 2 4s0 1 1 2v4l1 1c0 2-1 3-2 5-1 1 0 0-1 2l1 1-1 2v-7l-1-1-1-2c0-1 0-1-1-2l1-1s1 1 2 1v-5c0-1 0-2-1-3h0v-1z" class="O"></path><path d="M484 603c1 1 2 2 3 2h0c1 1 1 2 2 2 2 1 3 3 5 4 0 0 1 2 2 2 1 1 1 0 2 1l-1 2c1 1 1 0 2 1l1 2 1 1h0v3l-3-3h-1c0-1-1-2-2-2l-1-1v-1c-2-2-3-5-5-7-2 0-2 0-3-1h-1l-2 2-2 2c-1 3-4 4-4 7h4c1 1 1 1 3 2l1 1 1-1 1-3h0c1 1 1 2 1 3s1 2 1 3v2l2 1c1-1 2-2 4-3 0 1 0 4 1 5s1 1 2 1v1c-1 1-1 1 0 3 0 0 0 1 1 1h1c-2 1-3 1-5 1l-1-1c1-1 1 0 2 0l-2-2c1-1 0-1 1-1l-1-1c0-2 0-2-1-3h-1c-1 2 1 3 0 5 0 1-1 2-2 3v-1-1-1c-1-1 0-2 0-4h-1c0-2-1-4-3-5h-1c0-1 0-1-1-1-1-1-3-2-4-2h-1-2c-2 0-2 0-4 2 0 1 0 1 1 3l-1 1-1 1-2 2c0-4 2-9 4-12l3-4c1-2 1-3 2-4h1v-3c1-1 1 0 2 0 1-1 2-3 2-4z" class="a"></path><path d="M488 648l1-1v-3l1-1 3 3c1-1 1-1 3-1l1 1c-2 1-2 0-4 0-1 0-2 1-3 2 0 2 1 4 3 6 1 0 1 0 2-1 1 2 2 2 2 4v1l1 1-1 1 1 1v-1c1-1 2-1 3-1 1-1 1-2 2-2v-3s0-1 1-2v-1 7l1-2-1-1c1-2 0-1 1-2 1-2 2-3 2-5 1 4 0 9 0 13 0 1-1 2-1 4s1 5-1 6l-2 2c-1 0-1 0-2 1l-2-1-1-1c-3-3-5-6-6-10l-1-2c0-2 0-3-1-4l-1-2c-1-2-1-4-1-6z" class="W"></path><path d="M490 656h2c2 3 2 6 4 9h0-1c0 1 1 2 1 3h1v1l1 1v2c-3-3-5-6-6-10l-1-2c0-2 0-3-1-4z" class="O"></path><path d="M507 648c1 4 0 9 0 13 0 1-1 2-1 4s1 5-1 6v-1c1-1 1-3 1-4l-1 1h0c-1-1-1-3-1-4-2 0-1 1-3 2v-1c0-1 1-1 2-2s1-3 1-4l1-2-1-1c1-2 0-1 1-2 1-2 2-3 2-5z" class="D"></path><path d="M485 601v-1l1-1c3 3 5 5 8 7 2 0 3 1 5 2 1 1 2 1 3 1-2-1-4-2-5-4l-1-1c2 1 7 5 10 5 1 1 2 2 4 2v4c2 2 4 3 6 4v1c-1 0-2-1-4-2h-1-1v4 2h-1v-2l-4-1 2 2v3c0 4 1 7 0 11v2 1c-1 2 0 5-1 7v-4c-1-1-1-1-1-2s-1-3-2-4c0-2 0-7-1-9-1-1-1-1-1-2v-3-3h0l-1-1-1-2c-1-1-1 0-2-1l1-2c-1-1-1 0-2-1-1 0-2-2-2-2-2-1-3-3-5-4-1 0-1-1-2-2h0c-1 0-2-1-3-2l1-2z" class="H"></path><path d="M485 601v-1l1-1c3 3 5 5 8 7 2 0 3 1 5 2 1 1 2 1 3 1-2-1-4-2-5-4l-1-1c2 1 7 5 10 5 1 1 2 2 4 2v4c2 2 4 3 6 4v1c-1 0-2-1-4-2h-1-1v4 2h-1v-2l-4-1 1-1c-1-1-2-1-2-2l-1-1c-1-1-3-3-5-4l-9-9c-2-1-2-2-4-3z" class="Q"></path><path d="M496 604c2 1 7 5 10 5 1 1 2 2 4 2v4c-2-2-4-2-6-3-3-2-7-3-10-6 2 0 3 1 5 2 1 1 2 1 3 1-2-1-4-2-5-4l-1-1z" class="W"></path><path d="M474 626c-1-2-1-2-1-3 2-2 2-2 4-2h2 1c1 0 3 1 4 2 1 0 1 0 1 1h1c2 1 3 3 3 5h1c0 2-1 3 0 4v1 1 1l-2 2c0 2 1 1 1 3l-2 1c-1 3 0 4 1 6 0 2 0 4 1 6l1 2c1 1 1 2 1 4l1 2c-2-2-3-4-4-6l2 7h-1l-1-3-1-3h-2l-2 1c-1 1-2 2-3 2l-3-5c-2-1-4-3-5-5 0-1 0 0-1-1s-1-3-2-4v-1c-1-1-1-2-1-3l1-1c0-1 0-2 1-3l2 2 2-2v-1c1 0 2-1 3-2l-2-3 2-1-1-1s0-1-1-2h-1-1l1-1z" class="b"></path><path d="M478 645c0-1 1-1 2-2v-1h2c0 1-1 3-2 3l-1 1v2 1c1 2 2 3 3 5 1 1 2 1 3 1h1l2 1 2 7h-1l-1-3-1-3h-2v-1h-2v1c-1-1-2-2-2-3h-1c0-2-1-2-2-2v-7z" class="V"></path><path d="M490 629c0 2-1 3 0 4v1 1 1l-2 2c0 2 1 1 1 3l-2 1c-2 1-4 2-5 4v2 4 2c-1-2-2-3-3-5v-1-2l1-1c1 0 2-2 2-3h0l1-1c4-3 6-6 7-12z" class="C"></path><path d="M486 624c2 1 3 3 3 5h1c-1 6-3 9-7 12l-1 1h0-2v1c-1 1-2 1-2 2l-1-2 1-3-2-2c0-1 1-1 1-2l2-1 1 1h1v3h2c2-2 4-4 5-7v-1c1-3 0-4-2-7z" class="Z"></path><path d="M477 636c2 1 2 2 3 3l-2 1-2-2c0-1 1-1 1-2z" class="d"></path><path d="M482 648v-2c1-2 3-3 5-4-1 3 0 4 1 6 0 2 0 4 1 6l1 2c1 1 1 2 1 4l1 2c-2-2-3-4-4-6l-2-1h-1c-1 0-2 0-3-1v-2-4z" class="a"></path><path d="M482 648h1 0c1 2 1 2 3 3l1 1h-3-2v-4z" class="W"></path><path d="M482 652h2 1l2 1 2 1 1 2c1 1 1 2 1 4l1 2c-2-2-3-4-4-6l-2-1h-1c-1 0-2 0-3-1v-2z" class="H"></path><path d="M474 637v-1c1 2 1 2 1 3l1-1 2 2-1 3h0v1 5 2h-1-1l2 4c-2-1-4-3-5-5 0-1 0 0-1-1s-1-3-2-4v-1c-1-1-1-2-1-3l1-1c0-1 0-2 1-3l2 2 2-2z" class="N"></path><path d="M474 637v-1c1 2 1 2 1 3l-1 4-3-3 1-1 2-2z" class="O"></path><path d="M476 638l2 2-1 3h0v1 5 2h-1-1c-1-3-1-5-1-8l1-4 1-1z" class="E"></path><path d="M474 626c-1-2-1-2-1-3 2-2 2-2 4-2h2 1c1 0 3 1 4 2 1 0 1 0 1 1h1c2 3 3 4 2 7v1c-1 3-3 5-5 7h-2v-3h-1l-1-1-2 1c0 1-1 1-1 2l-1 1c0-1 0-1-1-3 1 0 2-1 3-2l-2-3 2-1-1-1s0-1-1-2h-1-1l1-1z" class="C"></path><path d="M477 634l2-2c1 0 1 1 2 1l1 1-2 2-1-1-2 1c0 1-1 1-1 2l-1 1c0-1 0-1-1-3 1 0 2-1 3-2z" class="H"></path><path d="M477 625c2 0 4 0 6 1 2 2 3 4 3 6h0c0 1-1 2-1 3-1 1-1 1-3 2l1-1c1-2-1-4-2-6v-1c-2 0-3 0-4 1l-1-1s0-1-1-2h-1-1l1-1 3-1z" class="r"></path><path d="M474 626l3-1c0 1-1 2 1 3 1 0 1 0 3 1-2 0-3 0-4 1l-1-1s0-1-1-2h-1-1l1-1z" class="I"></path><path d="M474 626c-1-2-1-2-1-3 2-2 2-2 4-2h2 1c1 0 3 1 4 2 1 0 1 0 1 1h1c2 3 3 4 2 7v1h-2c0-2-1-4-3-6-2-1-4-1-6-1l-3 1z" class="o"></path><path d="M474 591c1 1 2 1 3 2 0 1 0 2 1 2 1 1 1 2 1 3l1-1c1 0 1-1 2-2h1l3 4-1 1v1l-1 2c0 1-1 3-2 4-1 0-1-1-2 0v3h-1c-1 1-1 2-2 4l-3 4c-2 3-4 8-4 12l2-2 1-1h1 1c1 1 1 2 1 2l1 1-2 1 2 3c-1 1-2 2-3 2v1l-2 2-2-2c-1 1-1 2-1 3l-1 1c0 1 0 2 1 3v1c1 1 1 3 2 4s1 0 1 1c1 2 3 4 5 5l3 5-2 1h1l-3 2c-2 1-4 2-5 3l-1 1c-2 3-4 5-5 9-1 5-2 9 0 14h-1 0c-2-4-2-9 0-14h-2c-2 0-2 1-3 1-2-2-2-5-2-7-1-2 0-3 0-4v-2-4-10c1-3 1-5 1-7v-29l1 4c0 2-1 2 1 4v4-1s0-1 1-2v-6c0-1 1-2 2-3h1c0-1 1-2 0-2v-3c1 0 2-1 3-1h1c0-2 1-3 2-5 1-1 1-2 1-4h0c2-1 2-2 3-3l-2-3h0-1l-1-1h3l1-1z" class="r"></path><path d="M476 663c-3-2-7-6-8-9-5-8-8-17-6-26 1 11 5 24 14 31l2 2h1l-3 2z" class="D"></path><path d="M474 591c1 1 2 1 3 2 0 1 0 2 1 2 1 1 1 2 1 3l-1 1c-8 7-14 15-17 25v-1-6c0-1 1-2 2-3h1c0-1 1-2 0-2v-3c1 0 2-1 3-1h1c0-2 1-3 2-5 1-1 1-2 1-4h0c2-1 2-2 3-3l-2-3h0-1l-1-1h3l1-1z" class="a"></path><path d="M474 591c1 1 2 1 3 2 0 1 0 2 1 2 1 1 1 2 1 3l-1 1c-1 0-2 0-2-1-2-1 0-2 0-4-1 0-2-1-2-1l-1-1 1-1z" class="g"></path><path d="M458 614l1 4c0 2-1 2 1 4v4-1s0-1 1-2v1c-2 9-2 19 3 27 2 4 5 6 7 9-2 0-3 0-5-1 0-2-2-4-3-6h0v-1l-1-2c0-1 0-1-1-2l-1 1s0 1-1 2v1h0v2 2 1h2v1l-2 1-2 1v-10c1-3 1-5 1-7v-29z" class="H"></path><path d="M466 618c1 0 2 1 3 1-2 8-3 14-1 22 0 1 0 2 1 3v1c1 1 1 3 2 4s1 0 1 1c1 2 3 4 5 5l3 5-2 1-2-2c-9-7-13-20-14-31 0-1 1-3 1-3l3-7z" class="g"></path><path d="M459 659l2-1v-1h-2v-1-2-2h0v-1c1-1 1-2 1-2l1-1c1 1 1 1 1 2l1 2v1h0c1 2 3 4 3 6 2 1 3 1 5 1l2 3c0 2-4 3-5 5-2 3-3 5-4 8h-2c-2 0-2 1-3 1-2-2-2-5-2-7-1-2 0-3 0-4v-2-4l2-1z" class="a"></path><path d="M462 666l1-1 1 1v2c-1 1-1 1-2 1l-1-1 1-1v-1z" class="H"></path><path d="M459 664c1 1 2 1 3 2v1c-1 0-2 0-3 1h-1 0v-3l1-1z" class="W"></path><path d="M457 660l2-1c0 1 0 3 1 4l-1 1-1 1-1 1v-2-4z" class="D"></path><path d="M463 665h0c-1-2 0-5 0-6l1-1h1c1 1 0 2 0 3 1 2 1 2 0 4 0 0-1 0-1 1l-1-1z" class="N"></path><path d="M483 595l3 4-1 1v1l-1 2c0 1-1 3-2 4-1 0-1-1-2 0v3h-1c-1 1-1 2-2 4l-3 4c-2 3-4 8-4 12l2-2 1-1h1 1c1 1 1 2 1 2l1 1-2 1 2 3c-1 1-2 2-3 2v1l-2 2-2-2c-1 1-1 2-1 3l-1 1c-2-8-1-14 1-22-1 0-2-1-3-1 3-8 8-15 15-20l-1-1c1 0 1-1 2-2h1z" class="h"></path><path d="M473 627h1 1c1 1 1 2 1 2l1 1-2 1-3 3c0-3 1-4 0-6l1-1z" class="l"></path><path d="M475 631l2 3c-1 1-2 2-3 2v1l-2 2-2-2v-1l2-2 3-3z" class="N"></path><path d="M470 636c1 0 2 0 4 1l-2 2-2-2v-1z" class="e"></path><path d="M474 618l-1-1v-1-2l2-1h1c-1-1-1-1-1-2l1-2 3 1 1-3v3h-1c-1 1-1 2-2 4l-3 4z" class="Z"></path><path d="M481 598h2l1 1h0c-6 6-11 12-14 19l-1 1c-1 0-2-1-3-1 3-8 8-15 15-20z" class="O"></path><defs><linearGradient id="a" x1="543.58" y1="682.335" x2="481.818" y2="714.822" xlink:href="#B"><stop offset="0" stop-color="#cccdc8"></stop><stop offset="1" stop-color="#f2eff3"></stop></linearGradient></defs><path fill="url(#a)" d="M510 622v-4h1 1c2 1 3 2 4 2 1 4 1 9 0 13 0 6 1 13 0 20l1 96v15c-1 1-1 2-1 3 0-3 0-6-2-8-1 1-1 2-2 4h0l-1 1-1 3h-1v-3c1-2 0-4 1-7v-4l-1-1 1-14-2 1v-3l-1-5h0c0 1 0 1-1 2v1c-1 1-2 1-3 1-1-1 0-2 0-4v-1c2-4 4-8 4-13 0-2 0-4-1-5-1 0-2 1-2 3v2c-1 2-1 2-2 2l-2-1c0-1-1-2-1-3h-1c0-2-1-2-1-5h0c-1-1-3-3-4-3h-3l-1 1c1 2 3 3 3 5v1 1c0 1-1 2-2 3h-1-1v-2c-1 0-2 1-3 2-1-3-4-4-6-6h3c0-2-3-3-4-5v-2h0v-4l1-1c-2 0-1 1-3 1v-2h-4c-3-1-5-4-6-6l-1-2v-1c-2-5-1-9 0-14 1-4 3-6 5-9l1-1c1-1 3-2 5-3l3-2h-1l2-1c1 0 2-1 3-2l2-1h2l1 3 1 3h1l-2-7c1 2 2 4 4 6 1 4 3 7 6 10l1 1 2 1c1-1 1-1 2-1l2-2c2-1 1-4 1-6s1-3 1-4c0-4 1-9 0-13l-1-1c1-2 0-5 1-7v-1-2c1-4 0-7 0-11v-3l-2-2 4 1v2h1v-2z"></path><path d="M510 733v5l-2 1v-3c1-1 1-2 2-3z" class="d"></path><path d="M510 718v-5c0 3 0 5 2 6 0 1 0 1 1 1 1 2 1 3 1 5l1 1c0 1 0 5-1 6s-2 1-3 2h0c0-3 0-7-1-9v-7z" class="o"></path><path d="M509 646h2l-1 39c0-1 0-2-1-3-1-2 0-6 0-9v-13-14z" class="m"></path><path d="M510 622v-4h1 1c2 1 3 2 4 2 1 4 1 9 0 13 0 6 1 13 0 20l-1-1h-1 1v-24l-2 2v1h1v2 1-2l-1-1c-2 1-2 2-3 3l1-11-1-1z" class="O"></path><path d="M516 627l-3 2h-1c0-1 0-2 1-3 0-1 2-1 3-1v2z" class="N"></path><path d="M510 622v-4h1 1c2 1 3 2 4 2 1 4 1 9 0 13v-6-2-3l-1-1c-1 1-2 1-3 1l-1 1-1-1z" class="U"></path><path d="M505 621l4 1v2h1v-2l1 1-1 11 1 12h-2v14 13h-1 0c-1-2-1-2-1-4v-2-2-4c0-4 1-9 0-13l-1-1c1-2 0-5 1-7v-1-2c1-4 0-7 0-11v-3l-2-2z" class="F"></path><path d="M505 621l4 1v2h1v-2l1 1-1 11 1 12h-2v-7-14l-2 1v-3l-2-2z" class="t"></path><path d="M488 656c1 2 2 4 4 6 1 4 3 7 6 10l1 1 2 1c1-1 1-1 2-1l2-2c2-1 1-4 1-6s1-3 1-4v4 2 2c0 2 0 2 1 4h0 1c0 3-1 7 0 9 1 1 1 2 1 3v22c0 4-1 7 0 11v7 8c-1 1-1 2-2 3l-1-5h0c0 1 0 1-1 2v1c-1 1-2 1-3 1-1-1 0-2 0-4v-1c2-4 4-8 4-13 0-2 0-4-1-5-1 0-2 1-2 3v2c-1 2-1 2-2 2l-2-1c0-1-1-2-1-3h-1c0-2-1-2-1-5l1 1 1-1c0-3 2-8 3-11 1-2 1-5 0-7v-2c1-2-4-10-4-13-3-4-7-9-9-14h1l-2-7z" class="p"></path><path d="M498 677c5 6 8 14 9 22l-2-4v-2h0c-1-1-2-1-3-1v-2c1-2-4-10-4-13z" class="B"></path><path d="M488 656c1 2 2 4 4 6 1 4 3 7 6 10l1 1 2 1c1-1 1-1 2-1l2-2c2-1 1-4 1-6s1-3 1-4v4 2 2c0 2 0 2 1 4h0 1c0 3-1 7 0 9l-1 11-1-4c-4-10-13-17-17-26l-2-7z" class="M"></path><path d="M507 661v4 2 2c0 2 0 2 1 4h0c-2 3-1 6-2 9 0 0-1 0-1-1-2-3-4-5-6-8l2 1c1-1 1-1 2-1l2-2c2-1 1-4 1-6s1-3 1-4z" class="C"></path><path d="M502 692c1 0 2 0 3 1h0v2l2 4c1 5 1 13 0 18 0-2 0-4-1-5-1 0-2 1-2 3v2c-1 2-1 2-2 2l-2-1c0-1-1-2-1-3h-1c0-2-1-2-1-5l1 1 1-1c0-3 2-8 3-11 1-2 1-5 0-7z" class="D"></path><path d="M502 708c-1-1-1-2 0-3l1-1 1 1c0 1 0 2-1 3h-1z" class="g"></path><path d="M502 708h1c-1 3-2 7-1 10h1v-3c0-1 1-3 1-4h1l1 1c-1 0-2 1-2 3v2c-1 2-1 2-2 2l-2-1c0-1-1-2-1-3h-1c0-2-1-2-1-5l1 1 1-1h2l1-2z" class="N"></path><path d="M485 657h2l1 3 1 3c2 5 6 10 9 14 0 3 5 11 4 13v2c1 2 1 5 0 7-1 3-3 8-3 11l-1 1-1-1h0c-1-1-3-3-4-3h-3l-1 1c1 2 3 3 3 5v1 1c0 1-1 2-2 3h-1-1v-2c-1 0-2 1-3 2-1-3-4-4-6-6h3c0-2-3-3-4-5v-2h0v-4l1-1c-2 0-1 1-3 1v-2h-4c-3-1-5-4-6-6l-1-2v-1c-2-5-1-9 0-14 1-4 3-6 5-9l1-1c1-1 3-2 5-3l3-2h-1l2-1c1 0 2-1 3-2l2-1z" class="O"></path><path d="M481 673c5 0 9 4 12 8 1 2 3 4 4 6s1 3 2 4l-2 2h0c-1-1-2-2-2-3 0-2 0-3-1-4v-1c-1-1-1-2-2-3l-2-2-2-2c-2-1-4-3-6-3h0c-3 0-5 1-7 1l6-3z" class="i"></path><path d="M473 687c0-2 0-5 1-7l3-3h2s1-1 2-1l1 1 1 2h0l-2 1-1 1 1 1 2 1v1h-1v2h-1l-1-1c-1 1-1 0-1 1-1 1-1 2-2 3h-2 0c-1-1-1-2-2-2z" class="g"></path><path d="M473 687c0-2 0-5 1-7l3-3h2s1-1 2-1l1 1c-2 1-3 1-4 2l-1 2s0 1-1 1-2-1-2 1c-1 2 0 2 1 3v3c-1-1-1-2-2-2z" class="W"></path><path d="M481 682l3-1v-1c1 1 2 1 2 2l1 1c2 0 2-1 3 1s1 3 1 5c-2 1-3 1-4 3h0l1 1c-1 1-2 2-3 2-2 0-3 1-5 3v-4c1 0 2 1 3 0v-3h0c1-1 0-1 0-2v-1c-1 2-1 2-2 3h-2c-2-1-2 0-4-2h2c1-1 1-2 2-3 0-1 0 0 1-1l1 1h1v-2h1v-1l-2-1z" class="N"></path><path d="M487 683l1 1c-1 1-2 1-4 2v-1c1-1 1-1 1-2v-1h1l1 1z" class="g"></path><path d="M497 687c1 1 2 1 3 2 1 2 1 3 2 5v5c-1 3-3 8-3 11l-1 1-1-1h0c0-2-1-4-1-5-1-3-1-5-3-7v-1l-3-2c0-1-1-1-1-1v-1c1 0 2 1 2 1 1 0 2-1 3-1l1 1h1l1-1h0l2-2c-1-1-1-2-2-4z" class="L"></path><path d="M497 693l2-2c1 2 2 4 2 6-1 1-2 1-3 1v1c0-1 0-2-1-3v-3z" class="f"></path><path d="M497 693h0v3c1 1 1 2 1 3s1 2 1 3l-1 1c0-2-1-3-2-4-2-2-3-4-6-4 0-1-1-1-1-1v-1c1 0 2 1 2 1 1 0 2-1 3-1l1 1h1l1-1z" class="C"></path><path d="M485 695c1 0 2-1 3-2l1 2-2 2 1 1c1 1 1 1 2 1 1-1 2-1 3-2v1c2 2 2 4 3 7 0 1 1 3 1 5-1-1-3-3-4-3h-3l-1 1c1 2 3 3 3 5v1 1c0 1-1 2-2 3h-1-1v-2c-1 0-2 1-3 2-1-3-4-4-6-6h3c0-2-3-3-4-5v-2h0v-4l1-1c-2 0-1 1-3 1v-2l4-1c2-2 3-3 5-3z" class="M"></path><path d="M483 707c3 0 3 0 5 1h1l-2 2h-1l-2-2h-1v-1z" class="Z"></path><path d="M482 700h0c2 0 2 1 3 3l-1 1h-2c-1-1-1-1-1-2l1-2z" class="W"></path><path d="M485 695c1 0 2-1 3-2l1 2-2 2 1 1c1 1 1 1 2 1-1 1-1 1-1 2-1 0-2 0-3-1 0 0-1-1-1-2v-3z" class="g"></path><path d="M485 713h-1v-2h-1c-2-2-4-4-4-6l1-1 3 3v1h1l2 2c0 1-1 2-1 3z" class="F"></path><path d="M493 697v1c2 2 2 4 3 7l-2 1-1-1c-1 0-1-1-2-1v-1l-2-2c0-1 0-1 1-2s2-1 3-2z" class="W"></path><path d="M493 697v1c-1 1-1 2-2 4v1l-2-2c0-1 0-1 1-2s2-1 3-2z" class="e"></path><path d="M489 708s0-1 1-1l-1 1c1 2 3 3 3 5v1 1c0 1-1 2-2 3h-1-1v-2l-2-3h-1c0-1 1-2 1-3h1l2-2z" class="i"></path><path d="M476 672c2 1 3 1 5 1l-6 3c-2 2-3 4-3 7 0 1 0 2 1 4 1 0 1 1 2 2h0c2 2 2 1 4 2h2c1-1 1-1 2-3v1c0 1 1 1 0 2h0v3c-1 1-2 0-3 0v4l-4 1h-4c-3-1-5-4-6-6l-1-2v-1c-2-5-1-9 0-14l6-3c1-1 3-1 4-1h1z" class="e"></path><path d="M476 672c2 1 3 1 5 1l-6 3c-2 2-3 4-3 7 0 1 0 2 1 4 1 0 1 1 2 2h0c2 2 2 1 4 2h2c-1 1-2 1-4 2-2 0-3-1-5-3s-3-7-2-10c0-4 2-6 5-8h1z" class="h"></path><defs><linearGradient id="b" x1="490.892" y1="693.623" x2="478.907" y2="662.785" xlink:href="#B"><stop offset="0" stop-color="#bab8b7"></stop><stop offset="1" stop-color="#e9e8e8"></stop></linearGradient></defs><path fill="url(#b)" d="M485 657h2l1 3 1 3c2 5 6 10 9 14 0 3 5 11 4 13v2c1 2 1 5 0 7v-5c-1-2-1-3-2-5-1-1-2-1-3-2-1-2-3-4-4-6-3-4-7-8-12-8-2 0-3 0-5-1h-1c-1 0-3 0-4 1l-6 3c1-4 3-6 5-9l1-1c1-1 3-2 5-3l3-2h-1l2-1c1 0 2-1 3-2l2-1z"></path><path d="M485 657h2l1 3c-2 0-3 0-5 1v-3l2-1z" class="g"></path><path d="M471 666h4v1 2c-2 1-2 1-3 1h-1-2v1l2 2-6 3c1-4 3-6 5-9l1-1z" class="N"></path><path d="M471 666h4v1 2c-2-1-3 0-5-2l1-1z" class="e"></path><path d="M476 672c5-1 7-1 11 1 6 3 11 10 13 16-1-1-2-1-3-2-1-2-3-4-4-6-3-4-7-8-12-8-2 0-3 0-5-1z" class="Y"></path><path d="M480 660c1 0 2-1 3-2v3c1 2 1 4 2 6v1h0-1c-1-1-2-1-4-1h-2c-1 1-2 1-3 0v-1h-4c1-1 3-2 5-3l3-2h-1l2-1z" class="o"></path><path d="M480 667v-1c1-2 1-2 3-4v1l-1 1 3 3v1h0-1c-1-1-2-1-4-1z" class="H"></path><path d="M552 595h0c1-1 2-1 2-1h1l1 2 1-2c0 1 1 2 2 2h0l3 2c0 2 0 2-1 4l3 3 4 4 2 2 1 1 2-1c1 1 1 1 3 1l-1 2 1 1c1 3 1 6 1 9v7 3c-1 2-2 3-4 4v2l-1-2h-1c-1 3-1 5-2 8-1 1-2 2-4 1l-1-1h0l-1 3h-1l-1 1-1 1-1 1-8 5-1 1c-2 2-5 4-7 7h0l-1 1c-3 3-4 5-6 7l-3 4c-1 1-3 3-3 4 1 1 1 2 2 3l-2 3h0-3c-1 4-1 8-1 11-1 12-1 24 4 34 1 2 2 4 4 6 2 4 5 7 8 9 3 3 7 5 10 6h1 1 0c0 1-1 1 0 2v1 2l1 1 1 2c-2 4-3 6-3 11l-1 2 1 2c1 2 1 4 3 6v1c-1 1-2 1-2 3l-1-2h-2c0 1-1 2-2 2h-1v1 1 2c-2 0-1 0-2-1 1 2 1 2 2 2-1 1-1 1-1 2l1 1c0 1 0 1-1 2l2 1h0c0-2 0-2 2-3l2 4v1h1l3 3v4l-1-1 1 4 1 1-3 1v1c1 0 2 1 3 1-1 1-2 2-2 3h0l-2 1c0-1-1-1-2-2-1 0-2-1-2-2 0 1 0 2 2 3 0 0 0 1 1 1v1h0-1-1c0-1-1-1-2-1h0l-1-1h-3 0l-1 1c-1 0-2 0-4-1-1-1-1-2-3-2h-2l1 1c0 1 0 1 1 2l-1 2-1-1c-1 1-1 2-1 4-3-2-7-6-10-9s-5-7-8-11l-1-2c0-1 1-2 2-2v-1-1c-1 0-2 0-2 1l-2 2 1-4 1-8 2-23v-15l-1-96c1-7 0-14 0-20 1-4 1-9 0-13v-1l1-2h1 1l6-3c11-4 19-10 27-19z" class="s"></path><path d="M526 771c-1 0-2 0-3-1v-1l2-1c1 1 1 1 1 3h0zm-1-8v-1l-2-3h3l1 1-2 3z" class="J"></path><path d="M535 769h2v1 2h-2l-1-1 1-2z" class="r"></path><path d="M526 656c0-2-1-4 1-6 1 2 2 1 2 3l-2 2-1 1z" class="c"></path><path d="M526 656l1-1c1 1 1 1 1 2l-1 1 1 2c-1 0-1 0-2 1v-5z" class="G"></path><path d="M520 686l2 2h1c-1 3-2 5-2 7l-1-9z" class="X"></path><path d="M527 760l1 1 2-1c0-2-1-1-2-2l1-1c1 1 2 1 2 3l-1 1c-1 1-3 2-5 3v-1l2-3z" class="u"></path><path d="M526 756l3-1v1 1l-1 1c1 1 2 0 2 2l-2 1-1-1-1-1v-3z" class="G"></path><path d="M531 618c0-1 1-2 2-3h2l1-1v1c2-2 4-2 6-3v1c-2 0-3 1-5 2-1 0-2 1-3 2l-3 1zm-1 19l-3-1v-1l1-1-2-1 1-1 2-1v2l3 1-2 2v1z" class="J"></path><path d="M528 649h1l1 1c0 2-1 3 0 5 0 0-1 1-1 2h-1c0-1 0-1-1-2l2-2c0-2-1-1-2-3l1-1h0z" class="j"></path><path d="M527 687l3-6c1 1 1 2 2 3l-2 3h0-3z" class="Y"></path><path d="M520 682c1-1 2-1 3-2 0 2 0 3-1 5v2 1l-2-2v-4z" class="P"></path><path d="M531 618l3-1-1 2v3l-1 1-2 1-1-2c1-2 1-2 2-3v-1z" class="G"></path><path d="M529 622c1-2 1-2 2-3h0c1 1 1 0 1 1v1c-1 0-2 1-3 1z" class="J"></path><path d="M529 740h1c2 1 2 2 3 4h0-1-1c-1 1-1 1-2 1l-2-2 2-3z" class="u"></path><g class="G"><path d="M525 673c1 1 1 2 2 2l1 1c0 1 0 1 1 2-1 2-2 4-3 5v-2c0-3-1-5-1-8zm19 104l-1-2h0l-2 2c-1 0-2 0-3-1h0c1-1 2-2 2-3v-1h1l2 2v-1h1v4z"></path><path d="M525 673v-2c0-1-1-2 0-4v-1h1v-1c0-1 0-1-1-2h0l1-2 2 2c-1 2-1 2 0 3h0c-2 2-2 2-2 4h0l1 1-2 2z"></path></g><path d="M542 612l1-1c2-1 3-3 4-4v-2c1-1 1-2 2-3l3 1c-1 1-2 2-2 3-1 1-2 1-3 2v1h-1l-4 4v-1z" class="u"></path><path d="M528 657h1l-1 1 1 1v1l2-1 1 1v1c-2 1-3 3-4 5-1-1-1-1 0-3l-2-2h0c1-1 1-1 2-1l-1-2 1-1z" class="P"></path><path d="M526 772l1 2-1 2 2 2v4 3l-2-3v-1-1c0-3-1-4-2-7v-1h2 0z" class="J"></path><path d="M543 759l2 1v1c0 2-2 3-3 4l-1 1c-1 1-2 1-3 0h3v-1l-1 1-1-1v-3-1c1 1 0 0 2 1 1-1 1-1 2-3z" class="K"></path><path d="M537 758c1 1 2 2 2 3v1 3l1 1 1-1v1h-3c-2-1-3-2-5-4h1l1-2h1 1v-2z" class="I"></path><path d="M541 664c1 0 2 0 2 1l-1 1c-3 3-4 5-6 7l-3 4v-2-1c1 0 2-1 2-2 1-1 1-1 1-2l-1 1c-1 2-2 2-4 3v1l2-3 4-5 1-1v-1c1-1 2-1 3-1z" class="u"></path><path d="M526 756l-1 2c-2-2-1-2-1-4 0-1 0-2 1-3l-1-1c0-1-1-6 0-8 2 2 1 4 2 6l-1 1h1 1v1h0v2l1 1h0c1-1 1 0 2-1 1 1 0 2 0 4h-1v-1l-3 1z" class="J"></path><path d="M525 673l2-2-1-1h0c0-2 0-2 2-4 1 3 2 5 5 6l-2 3-2 3c-1-1-1-1-1-2l-1-1c-1 0-1-1-2-2h0z" class="P"></path><path d="M532 623h0c0 1-1 2-1 2 0 1 1 2 0 3v2l2 1 1-1 2-1v3l-2 1h-1l-1 1-3-1v-2l-1-3c-1 0-1-1-1-1l3-3 2-1z" class="J"></path><path d="M532 807c3 3 9 8 13 9h0l-1 1c-1 0-2 0-4-1-1-1-1-2-3-2h-2c1-1 1-1 1-2l-2 1c-1-1-1-1-3-2h-1v-1l2-1v-2z" class="h"></path><path d="M540 805l1 1 1-1 1 1c-1 1-1 2-1 3l1-1 2 1 1-1h1v1c0 1 1 2 2 3h1v1c0 1 0 2 2 3 0 0 0 1 1 1v1l-1-1c-2 0-2-1-2-2l-2 1c-2-1-5-3-5-5l-1-1-2-1v-1-3z" class="c"></path><path d="M532 634l1-1h1l2-1v2l-3 4h0c-1 1-1 2-1 3-1 2-1 2-3 3 0 2-1 3-1 5h0l-1-1v-1c1-3 0-7 3-10v-1l2-2z" class="J"></path><path d="M532 634l1-1h1l2-1v2l-3 4h0v-1l1-2c-2 0-2 1-4 1l2-2z" class="c"></path><path d="M532 661h1 1v1h0l-2 1v1c1 2 0 1 1 2v1c1-1 1-1 2-1h1l1 1-4 5c-3-1-4-3-5-6h0c1-2 2-4 4-5z" class="K"></path><path d="M540 750c2 2 5 4 7 6 0 2 0 3-2 5v-1l-2-1c-1 2-1 2-2 3-2-1-1 0-2-1 0-1-1-2-2-3 0-2 1-3 1-4h2c1 0 1 0 2-1-2-1-1 0-3-1 0 0 0-1 1-2z" class="I"></path><path d="M538 754h2v1c1 1 2 1 3 2v2h0c-1 2-1 2-2 3-2-1-1 0-2-1 0-1-1-2-2-3 0-2 1-3 1-4z" class="j"></path><path d="M532 757v-3c1-3 2-4 4-7l4 3c-1 1-1 2-1 2 2 1 1 0 3 1-1 1-1 1-2 1h-2c0 1-1 2-1 4v2h-1-1l-1 2h-1v-2l-1-1v-2z" class="Q"></path><path d="M532 757c0 1 0 0 1 1l4-4c0-1 0 0 1-1v1c0 1-1 2-1 4v2h-1-1l-1 2h-1v-2l-1-1v-2z" class="P"></path><path d="M537 615c2 0 3 1 3 2v1h-1l-2 3c1 0 1 1 2 2 0 1-1 1-2 1l1 1s1 1 2 1v1h-1 0c-2 0-2 1-3 2l-2 1-1 1-2-1v-2c1-1 0-2 0-3 0 0 1-1 1-2h0l1-1v-3l1-2c1-1 2-2 3-2z" class="j"></path><path d="M533 622l2-1h1c0 2 0 3-1 5l1 1-1 1h-3v1c1 1 1 1 2 1l-1 1-2-1v-2c1-1 0-2 0-3 0 0 1-1 1-2h0l1-1z" class="X"></path><path d="M520 682v-5-1-15c-1-1-1-1-1-2 1-2 0-3 0-5 0-1 0-2 1-3 0-1 0-1-1-2l1-1c0-2 0-3 1-4h0c-1 4 1 9 0 13v1c1 3-1 7 0 10s0 6 0 9h1v-2c0-2 1-8 0-10h-1l1-1v-4-2-5h0v-1-12-4c0 2 0 3-1 4 0-1-1-3 0-4v-1c0-1 0-2-1-2l1-1v-2c-1-1-1-2-1-2 1-1 1-1 2-1v-3c-1-1-1-1-2-1v-1c1 0 1-1 2-2h0 1v60h0c-1 1-2 1-3 2z" class="j"></path><path d="M517 749v5 12c0 2 0 4 1 5 1 3 1 8 2 12l1 2v2 1l1 3c0 1-2 1-2 1-1 1 0 2-2 3h0l-1 1c-1 0-2 0-2 1l-2 2 1-4 1-8 2-23v-15z" class="P"></path><path d="M515 787c1 1 2 3 2 5-2 1-2 2-3 3l1-8z" class="p"></path><path d="M518 795v-3c1-2 2-3 3-4l1 3c0 1-2 1-2 1-1 1 0 2-2 3z" class="b"></path><path d="M525 764c2-1 4-2 5-3 0 2 1 3 1 4s1 1 1 2l-1 2h-1v1 3c1 0 1 0 2 1 0 0 1 1 1 2 1-1 1-3 1-4v3c0 1 0 1-1 2l1 1h1 1l1 1h1v-1c1-1 2-1 3-1l1 2v-1h0c1 1 1 0 0 2v2h-1l-1 1h-1-4c-1 0-2 0-3-1l1-2c-1-1-1-1-2-1v2h-1v-1c0-2 0-3 1-4 1 0 1-1 1-1s-1 0-1 1l-1-1c-1 1-1 1-2 3l-2-2 1-2-1-2v-1h0c0-2 0-2-1-3h0c1-1 0-2 0-4z" class="c"></path><path d="M552 602h1l1 1c1 1 0 1 1 1 2 1 3 3 4 5v-1h1v1c2 2 0 0 2 1l1 1v1l-1 2v1c0-1 0-1-1-1l-2 1-2-1v1h-2v2h0l-1-1h-1c-1 1-1 1-2 1h-2c-2 1 0 1-1 2-1 0-1 0-2-1h0v-1h-1c-1 0-1 0-2-1l-1 1 1 1h-3v-1c0-1-1-2-3-2 2-1 3-2 5-2l4-4h1v-1c1-1 2-1 3-2 0-1 1-2 2-3v-1z" class="K"></path><path d="M552 603v-1c1 1 1 2 2 2-2 1-3 2-4 3s-1 2-1 3c-1 0-2-1-3-1h1v-1c1-1 2-1 3-2 0-1 1-2 2-3z" class="j"></path><path d="M552 602h1l1 1c1 1 0 1 1 1 2 1 3 3 4 5v-1h1v1c2 2 0 0 2 1l1 1v1l-1 2c0-1 0-1-1-2-1 0-1-1-2-2h-1v-2c-1-2-2-3-4-4-1 0-1-1-2-2z" class="X"></path><path d="M546 617v-1l-1-2 1-2c1-1 1-1 2-1 0 1 1 3 2 4l1 1v1h-2c-2 1 0 1-1 2-1 0-1 0-2-1h0v-1z" class="d"></path><path d="M552 610l1-1v-1-1c1 1 1 1 1 2l3 3-2 1 2 1v1h-2v2h0l-1-1h-1c-1 1-1 1-2 1v-1l-1-1c-1-1-2-3-2-4h1c1-1 2-1 3-1z" class="h"></path><path d="M549 611c1-1 2-1 3-1s1 1 1 2c-2 0-2 0-3-1h-1z" class="I"></path><path d="M553 753h1 0c0 1-1 1 0 2v1 2l1 1 1 2c-2 4-3 6-3 11l-1 2 1 2c1 2 1 4 3 6v1c-1 1-2 1-2 3l-1-2h-2c0 1-1 2-2 2h-1c0-1 0-1-1-2s-1-2-2-3c0-2-1-3-1-4v-4h-1c1-2 1-4 2-6l4-8c1-2 4-3 4-6z" class="h"></path><path d="M549 774l-1-2h1 2l-2 2z" class="k"></path><path d="M545 767h3v4h-1l-1-1c-1 1-1 2-2 3h-1c1-2 1-4 2-6z" class="x"></path><path d="M553 776c1 2 1 4 3 6v1c-1 1-2 1-2 3l-1-2h-2 0c1-1 1-2 2-2v-1h-2v-1c1-1 1-2 2-4z" class="Z"></path><path d="M551 772c1 1 1 1 1 2l1 2c-1 2-1 3-2 4l-1-1-1 1h-1l1-1c0-1 1-2 1-3-1 0-1-1-2-1l1-1 2-2z" class="l"></path><path d="M545 767l4-8c1 1 3 1 3 3h0c-1 0-1 1-2 1h0v2 1c-1 0-1 0-2 1h0-3z" class="d"></path><path d="M518 795c2-1 1-2 2-3 0 0 2 0 2-1 2 4 5 9 8 13l2 3v2l-2 1v1h1c2 1 2 1 3 2l2-1c0 1 0 1-1 2l1 1c0 1 0 1 1 2l-1 2-1-1c-1 1-1 2-1 4-3-2-7-6-10-9s-5-7-8-11l-1-2c0-1 1-2 2-2v-1-1l1-1h0z" class="Y"></path><path d="M521 797h1l1 1c-1 1-1 2-2 3-1-1-1-2-2-3 1-1 1 0 2-1z" class="l"></path><path d="M524 800c1 0 1 0 2 1l-1 1 1 1v1 1h-3l-2-2 1-1 2 1v-1l-1-1 1-1z" class="r"></path><path d="M517 796l1-1 2 1c0 1-1 1-1 2-1 1-1 2-1 3v1l-1-1-1 1-1-2c0-1 1-2 2-2v-1-1z" class="b"></path><path d="M530 804l2 3v2l-2 1v1h1v2h0l-1-1-2 1-1-1c1 0 1 0 1-1l-1-1c1-1 0-2 1-4h0l-1-1c1-1 2-1 3-1z" class="I"></path><path d="M524 813h0v-2-1c0-1 0-1 2-1l1 1-1 1 1 1 1 1 2-1 1 1h0v-2c2 1 2 1 3 2l2-1c0 1 0 1-1 2l1 1c0 1 0 1 1 2l-1 2-1-1c-1 1-1 2-1 4-3-2-7-6-10-9z" class="k"></path><path d="M528 778c1-2 1-2 2-3l1 1c0-1 1-1 1-1s0 1-1 1c-1 1-1 2-1 4v1h1v-2c1 0 1 0 2 1l-1 2c1 1 2 1 3 1h4 1l1-1h1l2 2c0 1-1 1 0 2s2 2 2 3c1 2 1 2 2 2-1 1-1 1-1 2l1 1c0 1 0 1-1 2l2 1h0c0-2 0-2 2-3l2 4v1h1l3 3v4l-1-1 1 4 1 1-3 1v1c1 0 2 1 3 1-1 1-2 2-2 3h0l-2 1c0-1-1-1-2-2-1 0-2-1-2-2v-1h-1c-1-1-2-2-2-3v-1h-1l-1 1-2-1-1 1c0-1 0-2 1-3l-1-1-1 1-1-1v3c-1 0-2-1-3-1v-1c0-3 0-4-2-6v-1c-1-1-1-3-2-3-1-2-1-2-1-3-3-1-2-5-4-7v-1-3-4z" class="G"></path><path d="M530 784l3 2v1h0l-2-1h-1v-2z" class="P"></path><path d="M529 783c1-1 2-1 3 0l2 2c0 1 0 1-1 1l-3-2-1-1z" class="X"></path><path d="M538 790h-1c-1-2-1-3 0-4 0-1 1-1 2-2v1l-1 1v3 1z" class="c"></path><path d="M542 782l2 2c0 1-1 1 0 2s2 2 2 3c1 2 1 2 2 2-1 1-1 1-1 2l1 1h-3v1l-1-1v-1l1-1h1 0l-2-1-1-1c-1-1 0-1 0-3-1-1-2-1-3-2l2-2v-1z" class="X"></path><path d="M528 782l1 1h0l1 1v2 2h2c1 2 0 2 0 4 1 0 1-1 3-1v1l-1 1 1 1 1 1v1 1c1 0 1 0 2 1l-1 1h0 1c1 0 2 0 4-1v1c0 1-1 1-1 2h-1-1l-1 1c2 0 2 1 3 2l-1 1v3c-1 0-2-1-3-1v-1c0-3 0-4-2-6v-1c-1-1-1-3-2-3-1-2-1-2-1-3-3-1-2-5-4-7v-1-3z" class="u"></path><path d="M538 789l2-1c1 1 1 2 2 3h2l2 1h0-1l-1 1v1l1 1v-1h3c0 1 0 1-1 2l2 1h0l1 2-1 1v1l1 1h-4c-1 0-1 1-1 1l2 2h-1s0 1-1 1l1 1 1-1v1h1l-1 1h-1l-1 1-2-1-1 1c0-1 0-2 1-3l-1-1-1 1-1-1 1-1c-1-1-1-2-3-2l1-1h1 1 1c1 0 1-1 2-1s1 0 1-1c0 0-1-1-2-1l-1-1 1-1-3-3 1-1c-1-1-2-1-3-2v-1z" class="E"></path><path d="M549 801c-1-1-2-1-3-2v-1h1l2 2v1z" class="K"></path><path d="M546 805h-1l-1 1v1l-1-1v-2s1 0 2-1l2 2h-1z" class="X"></path><path d="M549 797c0-2 0-2 2-3l2 4v1h1l3 3v4l-1-1 1 4 1 1-3 1v1c1 0 2 1 3 1-1 1-2 2-2 3h0l-2 1c0-1-1-1-2-2-1 0-2-1-2-2v-1h-1c-1-1-2-2-2-3v-1l1-1h-1v-1l-1 1-1-1c1 0 1-1 1-1h1l-2-2s0-1 1-1h4l-1-1v-1l1-1-1-2z" class="K"></path><path d="M545 803c2 0 3 0 4 1l1 1-1 1c-1 0-1-1-1-1h-1l-2-2z" class="P"></path><path d="M550 799c2 2 2 2 4 2 0 1 0 2 1 3l1 1h0c-1 1-1 1-3 1h-2c0-2 0-2 1-3v-1h0-2l-1-1v-1l1-1z" class="G"></path><path d="M549 797c0-2 0-2 2-3l2 4v1h1l3 3v4l-1-1h0l-1-1c-1-1-1-2-1-3-2 0-2 0-4-2l-1-2z" class="s"></path><path d="M549 812l-1-3c1-2 1-1 3-2 0 1 0 2 1 3 0-1 1-1 1-2h1l1 2 2-1 1 1-3 1v1c1 0 2 1 3 1-1 1-2 2-2 3h0l-2 1c0-1-1-1-2-2-1 0-2-1-2-2v-1h-1z" class="P"></path><path d="M556 596l1-2c0 1 1 2 2 2h0l3 2c0 2 0 2-1 4l3 3 4 4 2 2 1 1 2-1c1 1 1 1 3 1l-1 2 1 1c1 3 1 6 1 9v7 3c-1 2-2 3-4 4v2l-1-2h-1c-1 3-1 5-2 8-1 1-2 2-4 1l-1-1h0l-1 3h-1l-1 1-1 1-1 1-8 5-1 1c-2 2-5 4-7 7h0c0-1-1-1-2-1s-2 0-3 1v1l-1 1-1-1h-1c-1 0-1 0-2 1v-1c-1-1 0 0-1-2v-1l2-1h0v-1h-1-1v-1l-1-1-2 1v-1l-1-1 1-1c0-1 1-2 1-2-1-2 0-3 0-5l-1-1h-1c0-2 1-3 1-5 2-1 2-1 3-3 0-1 0-2 1-3h0l3-4v-2-3c1-1 1-2 3-2h0 1v-1c-1 0-2-1-2-1l-1-1c1 0 2 0 2-1-1-1-1-2-2-2l2-3h1 3l-1-1 1-1c1 1 1 1 2 1h1v1h0c1 1 1 1 2 1 1-1-1-1 1-2h2c1 0 1 0 2-1h1l1 1h0v-2h2v-1l2 1 2-1c1 0 1 0 1 1v-1l1-2v-1l-1-1c-2-1 0 1-2-1v-1c-2-3-4-5-5-7-1-1-1-2-2-2v-1c1-1 0-1 1-1h1l1-1z" class="E"></path><path d="M539 636h2v1c0 1 0 1-1 1s-1 0-1-1v-1z" class="G"></path><path d="M539 637l-2 1-1-1 1-1h2v1z" class="x"></path><path d="M539 618h1 3l-1 1-1 1h-1l-1-2z" class="n"></path><path d="M528 649c0-2 1-3 1-5 1 1 2 2 2 3l-2 2h-1z" class="X"></path><path d="M536 629c1-1 1-2 3-2h0v4h0v2l-3 1v-2-3z" class="G"></path><path d="M540 647c0-1 0-1 1-2h-2c-1-2 0-1 1-2v-4h2v1 2c-1 1-1 0-1 2l1 1 1 2-1 1-2-1z" class="b"></path><path d="M545 639l1-1v-1c-1 1-2 1-3 1v-1c2-1 3-1 4-3h1c1 0 2 3 3 4h-1-1l-2 2-2-1z" class="Y"></path><path d="M530 650c1-1 2-2 4-3l1-1c0 1 1 1 2 1-2 2-2 0-3 3h0c-2 1-2 2-3 3l1 1s1-1 2-1h1l-1 1c1 2 2 0 2 2l-2 2v1c-1 1 0 1-2 1h0l-1-1-2 1v-1l-1-1 1-1c0-1 1-2 1-2-1-2 0-3 0-5z" class="K"></path><path d="M557 614l2 1 2-1c1 0 1 0 1 1h-1c-1 1-2 1-3 1s-1 1-2 1v1h1c-3 2-5 4-7 7l-1-1c-1 0-1-1-2-1-1 1-1 1-1 3h2v2l-1 1c0-1 0-1-1-2l-1 1c-2 0-3 0-5-1l2-1h1 1l-1-2v-2-1l2-1v-3h1v1h0c1 1 1 1 2 1 1-1-1-1 1-2h2c1 0 1 0 2-1h1l1 1h0v-2h2v-1z" class="b"></path><path d="M551 638l1 1 1 1h0 1 3 0c1 1 0 1 1 2h0c1 1 1 1 1 3h-1l1 1h0c1 2 1 3 2 4l-1 1-1 1-8 5-1 1c-2 2-5 4-7 7h0c0-1-1-1-2-1s-2 0-3 1v1l-1 1-1-1h-1c-1 0-1 0-2 1v-1c-1-1 0 0-1-2v-1l2-1h0v-1h-1-1v-1h0c2 0 1 0 2-1v-1l2-2c0-2-1 0-2-2l1-1 1-1c0-1 0 0-1-1h0l1-2 2 1v-2h1l1-1 2 1 1-1-1-2-1-1c0-2 0-1 1-2v-2l1-1h2l2 1 2-2h1 1z" class="Y"></path><path d="M537 657h0c1 0 2 1 2 2h-1l-2 1h0c0-1 0-1 1-2v-1z" class="Q"></path><path d="M542 650c2-1 2-1 4-1l1 1-2 2h-2l-1-2z" class="r"></path><path d="M558 646h1c1 2 1 3 2 4l-1 1-1 1-1-1c0-1 0 0-1-1v-1h1 1c0-1-1-2-1-3z" class="P"></path><path d="M538 666c0-1-1-1-1-2-1-1-1 0-1-1h1l2-1 2 1v1c-1 0-2 0-3 1v1z" class="b"></path><path d="M541 662v-1l1-1v-1h2l-1-1c0-1 1-2 2-3 0 1 0 0 1 1h0 1v2c-2 1-4 3-6 4z" class="K"></path><path d="M539 648c0 1 1 2 2 3h0c-1 2-1 2-3 2-1-1-1-2-2-4l2 1v-2h1z" class="p"></path><path d="M547 656h0c1 0 1 0 1 1l2 1c-2 2-5 4-7 7h0c0-1-1-1-2-1v-1-1c2-1 4-3 6-4v-2z" class="P"></path><path d="M547 656v-1c2-2 3-2 4-2 1 1 1 1 1 2l1-1c0-1 0-2 1-3 1 0 1 0 3 1l1-1 1 1-8 5-1 1-2-1c0-1 0-1-1-1h0z" class="p"></path><path d="M551 638l1 1 1 1h0 1 3 0c1 1 0 1 1 2h0c1 1 1 1 1 3h-1l1 1h0-1-2v-1h-1v3 1c-1 1-2 1-3 1-1 1-1 1-1 2h-1l-2-3 3-1v-2-1c1-1 1-1 1-2l-1-2h-1v-3h1z" class="K"></path><path d="M553 640h1 3 0c1 1 0 1 1 2h0-3-1l-1-2z" class="P"></path><path d="M545 639l2 1 2-2h1v3h1l1 2c0 1 0 1-1 2v1 2l-3 1-1 1-1-1c-2 0-2 0-4 1v-2l1-1-1-2-1-1c0-2 0-1 1-2v-2l1-1h2z" class="h"></path><path d="M545 639l2 1 2-2h1v3h0-3c-1 1-2 1-3 1h-1l2-2v-1zm-1 5c2-1 3-2 6-1l1 2v1h-2c-2-1-3-2-5-1v-1z" class="k"></path><path d="M542 645l2-1v1c2-1 3 0 5 1h2v2l-3 1-1 1-1-1c-2 0-2 0-4 1v-2l1-1-1-2z" class="I"></path><path d="M544 645c2-1 3 0 5 1-1 0-2 1-3 2l-2-1v-2z" class="L"></path><path d="M556 596l1-2c0 1 1 2 2 2h0l3 2c0 2 0 2-1 4l3 3 4 4 2 2 1 1 2-1c1 1 1 1 3 1l-1 2 1 1c1 3 1 6 1 9v7 3c-1 2-2 3-4 4v2l-1-2h-1c-1 3-1 5-2 8-1 1-2 2-4 1l-1-1h0l-1 3h-1l-1 1c-1-1-1-2-2-4h0l-1-1h1c0-2 0-2-1-3h0c-1-1 0-1-1-2h0-3-1 0l-1-1c1-2 1-2 2-3h0c-2-2-3-3-4-5s-1-4 0-6c2-3 4-5 7-7h-1v-1c1 0 1-1 2-1s2 0 3-1h1v-1l1-2v-1l-1-1c-2-1 0 1-2-1v-1c-2-3-4-5-5-7-1-1-1-2-2-2v-1c1-1 0-1 1-1h1l1-1z" class="C"></path><path d="M556 623c1-1 1-1 2-1s2 0 3-1l3 3h-4l-3 1c1-1 1-1 1-2h-2z" class="L"></path><path d="M564 624l2 1c0 1-1 2-1 3l-4-1h-1c-1-1 0-2 0-3h4z" class="U"></path><path d="M556 623h2c0 1 0 1-1 2l3-1c0 1-1 2 0 3h1c0 2 0 2 1 3v2c-2 0-3 0-3 2l-1 2-1 1c-2-3-5-6-4-9 0-3 1-4 3-5z" class="h"></path><path d="M557 625l3-1c0 1-1 2 0 3h1c0 2 0 2 1 3v2l-1-1h-3-2v-3h0c0-2 0-2 1-3z" class="p"></path><path d="M557 625l3-1c0 1-1 2 0 3-2 0-3 1-4 1h0c0-2 0-2 1-3z" class="S"></path><path d="M561 627l4 1c1 2 3 2 3 5h1v1 4h1 1c-1 3-1 5-2 8-1 1-2 2-4 1l-1-1h0l-1 3h-1l-1 1c-1-1-1-2-2-4h0l-1-1h1c0-2 0-2-1-3h0c-1-1 0-1-1-2h0-3-1 0l-1-1c1-2 1-2 2-3l2 1h1l1-1 1-2c0-2 1-2 3-2v-2c-1-1-1-1-1-3z" class="X"></path><path d="M565 634l3 2c-1 1-2 3-3 4h0c-1 0-1-1-1-1-1-1-1-2-1-3l2-2z" class="T"></path><path d="M561 627l4 1c1 2 3 2 3 5h1v1c-1 1-1 1-1 2l-3-2h0 2v-2c-1 0-2 0-2 1h-1l-1-1c1 0 1-1 1-2l-1-1-1 1c-1-1-1-1-1-3z" class="P"></path><path d="M558 642l2-1 2 2h2 1v2l-1 1h0l-1 3h-1l-1 1c-1-1-1-2-2-4h0l-1-1h1c0-2 0-2-1-3z" class="b"></path><path d="M563 649l-2-2v-1l1-1 2 1-1 3z" class="j"></path><path d="M569 634v4h1 1c-1 3-1 5-2 8-1 1-2 2-4 1l-1-1 1-1v-2c1-2 1-2 0-3h0c1-1 2-3 3-4 0-1 0-1 1-2z" class="M"></path><path d="M565 640c1 1 1 1 1 2 1 1 1 2 2 3h-1-2v-2c1-2 1-2 0-3h0z" class="R"></path><path d="M556 596l1-2c0 1 1 2 2 2h0l3 2c0 2 0 2-1 4l3 3 4 4 2 2 1 1 2-1c1 1 1 1 3 1l-1 2 1 1c1 3 1 6 1 9v7 3c-1 2-2 3-4 4v2l-1-2h-1-1-1v-4-1h-1c0-3-2-3-3-5 0-1 1-2 1-3 1 1 2 2 4 2l-1-1c-1-2-2-5-4-6-2-2-5-2-8-2h-1v-1c1 0 1-1 2-1s2 0 3-1h1v-1l1-2v-1l-1-1c-2-1 0 1-2-1v-1c-2-3-4-5-5-7-1-1-1-2-2-2v-1c1-1 0-1 1-1h1l1-1z" class="T"></path><path d="M571 616l4 9-1 1s-1-1-2-1l1-2-2-2c-1-2-1-3 0-5z" class="B"></path><path d="M555 601a30.44 30.44 0 0 1 8 8v2l-1-1c-2-1 0 1-2-1v-1c-2-3-4-5-5-7z" class="j"></path><path d="M571 625c1 4 3 9 2 13v2l-1-2h-1v-4-2-1c0-2-1-3-1-4l-1-1 2-1z" class="m"></path><path d="M556 596l1-2c0 1 1 2 2 2h0l3 2c0 2 0 2-1 4-2-2-5-4-8-4 1-1 0-1 1-1h1l1-1z" class="G"></path><path d="M571 612l2-1c1 1 1 1 3 1l-1 2 1 1c1 3 1 6 1 9l-6-12z" class="Y"></path><path d="M566 625c1 1 2 2 4 2 0 1 1 2 1 4v1 2 4h-1-1v-4-1h-1c0-3-2-3-3-5 0-1 1-2 1-3z" class="F"></path><path d="M563 609c2 3 4 6 6 10 1 2 1 4 2 6l-2 1c-1-2-2-5-4-6-2-2-5-2-8-2h-1v-1c1 0 1-1 2-1s2 0 3-1h1v-1l1-2v-1-2z" class="E"></path><path d="M563 612c1 1 1 2 1 3v1h-1c-1 0-1-1-1-1v-1l1-2z" class="G"></path><defs><linearGradient id="c" x1="628.936" y1="422.642" x2="565.419" y2="432.103" xlink:href="#B"><stop offset="0" stop-color="#9f9e9c"></stop><stop offset="1" stop-color="#bcbaba"></stop></linearGradient></defs><path fill="url(#c)" d="M606 292c-1 1-1 2-1 3l2-1 1-1 2-1h1v-1h1c1-1 2 0 4-1h4 4l-1 2c-2 0-4 1-7 2-3 2-6 3-9 6-8 7-13 17-15 27-3 10-2 20-2 30v30 115l-1 41c-2 3 3 17-3 17h0l-1-1v-2-1l-2-2v-13c-1 2-1 3-1 4l-1-2-1-5v-3l-2 1c0-1-1-2-2-2l-1-1v1l-2-1-3-3c-1-3-3-6-6-8-1-1-2-2-3-4-4-4-8-6-12-9-2-1-3-2-5-2l-4-2h-2c-5-1-9-2-13 2l-2 1v-3c1-2 1-4 1-6v-2-1l-1-3v-6-2-3-3h-2v-1c0-1 0 0-1-1 0-2 1-4 1-6v-3l2-2-1-10v-29-11c-1-3 0-5 0-8l-1 1v2-16c0-3 0-7 1-10h1 0v-1h-1c-1-1 0-2-1-3l-1-1c-4 0 1 1-2 0h0v-2-5h0c2-1 2-1 4 0v-8-1-1c2-2 2-2 5-3l11-1h1l3 5 5 1c0 1 0 2 1 2h0 2c1-1 1-1 1-3l-1-2h2l1-1h2s0-1 1-1l-1-2h-1l1-1 1-2h5 1l3-1 6-1v-1c3-1 5-2 7-4l2-2v-1-1c0-1 1-2 1-2 0-4-2-7-5-9 1-1 2-1 2-3l-1-1-1-1-3-2 2-3h1l1 1 2-1c1 1 1 0 2 1 1-1 2-1 4-2v-1l-1-1 1-1v-3c1-1 1-1 1-2l-2-1-1-2h0l1-2v-1l3-2h0c1 1 1 2 1 4 1-2 2-3 2-5 0-1 1-1 2-2 2-1 2-2 3-4 1-1 3-1 4-2 1-2 2-2 4-3 1 0 1-1 2-2h1z"></path><path d="M582 417l2 2c1 1 0 1 1 3v3s1 2 0 3c0-1-1-1-2-1v-8h0l-1-2z" class="B"></path><path d="M580 361c1-1 2-1 3-1v-5h0v18h0l-1-3v-1c-1-1-1-2-1-3v-4l-1-1z" class="G"></path><path d="M586 447h0v6l1-1v5h-1l-1 2h1 1v9h-1v-3c1-1 0-3 0-5h0-1c-1 0-2 0-2-2l1-2c1-2 2-3 1-5l1-4z" class="C"></path><path d="M587 323v8 4c-1 2-1 3-2 6-1 1 0 5-1 7v4l-1-3 1-4c0-1 0-2-1-2 1-3 1-5 1-8 1-4 1-8 3-12z" class="M"></path><path d="M583 419h0v8c1 0 2 0 2 1v3c0 1 1 2 1 3 0 2-1 3 1 5-1 1-1 3-1 4 0 0 1 1 1 2s-1 1-1 2h0v-2l-2-2h0c1-1 1-1 1-3l-2-1c0-2 1-1 2-3l-3-1c0-5 0-11 1-16z" class="i"></path><path d="M583 458c0 2 1 2 2 2 0 1-1 2-1 2v15c1 2-2 5 0 6v1c0 1-1 1-2 2v-16-7-1c0-1 1-3 1-4z" class="T"></path><path d="M583 458c0 2 1 2 2 2 0 1-1 2-1 2-2 3-1 5-2 8v-7-1c0-1 1-3 1-4z" class="F"></path><path d="M581 513c1-1 1-1 1 0l1 28c-1 2-1 3-1 4l-1-2-1-5 1-6v-7-12z" class="j"></path><path d="M582 369v1l1 3h0v17 11c-1-4 0-8-1-11 0 1-1 2-1 3v-1-10h-1l-1-4c-1 0-1 0-1-1l1-1v-2c1 0 2 0 3 1 0-1-1-2-1-3h0c0-1 0-2 1-3z" class="P"></path><path d="M579 374c1 0 2 0 3 1-1 2-1 4-1 7h-1l-1-4c-1 0-1 0-1-1l1-1v-2z" class="L"></path><path d="M583 349l1 3-1 3h0v5c-1 0-2 0-3 1s-3 2-5 3h-1c-2-1-3-1-4-2l6-3c3-3 6-6 7-11v1z" class="s"></path><path d="M580 361l1 1v4c0 1 0 2 1 3-1 1-1 2-1 3h0c0 1 1 2 1 3-1-1-2-1-3-1v2c-2 0-2-1-3-2v-1c-1 0-1-1-2-1s-1 1-3 0l1-1c-1-1-1-2-2-2l3-1c1-1 2-2 2-3v-1c2-1 4-2 5-3z" class="V"></path><path d="M576 373v-1c1 0 1 0 2 1l1 1v2c-2 0-2-1-3-2v-1z" class="S"></path><path d="M575 365l2 2c1 0 2 0 3 1l-1 1h0c-1 0-3 0-3-1l-1-1-1 2-1-1c1-1 2-2 2-3z" class="L"></path><path d="M580 361l1 1v4c0 1 0 2 1 3-1 1-1 2-1 3h0c0 1 1 2 1 3-1-1-2-1-3-1v-2-3l1-1c-1-1-2-1-3-1l-2-2v-1c2-1 4-2 5-3z" class="I"></path><path d="M580 361l1 1v4l-2-2-1 1h0c0 1-1 1-1 2l-2-2v-1c2-1 4-2 5-3z" class="Q"></path><path d="M582 435l3 1c-1 2-2 1-2 3l2 1c0 2 0 2-1 3h0l2 2v2l-1 4c1 2 0 3-1 5l-1 2c0 1-1 3-1 4v1l-2-1v-8-1-7-1-6c1-1 1-2 1-3l1 3v-4z" class="S"></path><path d="M580 439c1-1 1-2 1-3l1 3v11 2l-2 2v-1-7-1-6z" class="p"></path><path d="M582 450h1 1l1 1c1 2 0 3-1 5l-1 2c0 1-1 3-1 4v1l-2-1v-8l2-2v-2z" class="w"></path><path d="M580 454l2-2v10 1l-2-1v-8z" class="n"></path><path d="M580 462l2 1v7 16 27c0-1 0-1-1 0v-2-2-1-8-8-1-7h-1c0-1-1-1-2-2l-1-4v-3l3-3v-1-9z" class="E"></path><path d="M577 475l3-3c1 2 1 3 1 5v7h-1c0-1-1-1-2-2l-1-4v-3z" class="L"></path><path d="M578 482l-1-4v-3c2 1 3 1 3 2 1 2-1 4-2 5z" class="t"></path><defs><linearGradient id="d" x1="600.396" y1="305.251" x2="588.046" y2="300.494" xlink:href="#B"><stop offset="0" stop-color="#262526"></stop><stop offset="1" stop-color="#494945"></stop></linearGradient></defs><path fill="url(#d)" d="M606 292c-1 1-1 2-1 3l2-1 1-1 2-1h1v-1h1c1-1 2 0 4-1h4 4l-1 2c-2 0-4 1-7 2-3 0-4 0-7 1h-2c-8 3-15 12-18 20l-2 4v-1l-1 1h0c-1 1-1 2-1 2l-1-1 1-1v-3c1-1 1-1 1-2l-2-1-1-2h0l1-2v-1l3-2h0c1 1 1 2 1 4 1-2 2-3 2-5 0-1 1-1 2-2 2-1 2-2 3-4 1-1 3-1 4-2 1-2 2-2 4-3 1 0 1-1 2-2h1z"></path><path d="M587 306h0c1 1 1 2 1 4h-1c-1 0-1 0-1-1-1 0-2 1-3 2l1-2v-1l3-2z" class="h"></path><path d="M620 290h4l-1 2c-2 0-4 1-7 2-3 0-4 0-7 1h-2c4-3 9-4 13-5z" class="o"></path><path d="M585 321s0-1 1-2h0l1-1v1 4c-2 4-2 8-3 12 0 3 0 5-1 8 1 0 1 1 1 2l-1 4v-1c-1 5-4 8-7 11v-1c0-2 1-4 2-5l-1-1c1-2 1-3 1-4l2-2v-1-1c0-1 1-2 1-2 0-4-2-7-5-9 1-1 2-1 2-3l-1-1-1-1-3-2 2-3h1l1 1 2-1c1 1 1 0 2 1 1-1 2-1 4-2v-1z" class="j"></path><path d="M581 327l1-1h2c0 1-1 2-1 4v1h0c-2-1-1-2-2-4z" class="b"></path><path d="M578 330c4 5 5 10 5 15l-1-1c-1-2 0-3 0-4h0c0 2 0 4-2 5v1-1-1c0-1 1-2 1-2 0-4-2-7-5-9 1-1 2-1 2-3z" class="F"></path><path d="M577 324l2-1c1 1 1 0 2 1 1-1 2-1 4-2l-1 4h-2l-1 1h-2l-2 2-1-1-3-2 2-3h1l1 1z" class="Y"></path><path d="M573 326l2-3h1l1 1c0 1 0 2-1 3v1l-3-2z" class="E"></path><path d="M580 346v-1c2-1 2-3 2-5h0c0 1-1 2 0 4l1 1v3c-1 5-4 8-7 11v-1c0-2 1-4 2-5l-1-1c1-2 1-3 1-4l2-2z" class="T"></path><path d="M571 449h3v-1c0-1 1-1 2-1 0-1 1-2 2-2h1 1v1 7 1 8 9 1l-3 3v3l1 4c-1 1-3 1-4 1s-2-1-3-2l-1-1h0v-2l-3 1-2-1v-3l-4-1-2-2 1-1-2-2 1-1 2-1-1-1v-1l1-1-1-1 1-3c0-2 1-4 1-6 1-1 2-2 2-3 1 1 1 1 2 1l2-2h1c1-1 1-2 2-2v1z" class="S"></path><path d="M567 474l1 1c1 1 2 1 2 3l-3 1-2-1v-3l1 1 1-2z" class="k"></path><path d="M568 457c2 0 2 0 3-2l-1-1h0c0-2 1-2 3-3l-2 5 1 2-1 1 1 1v2h0v1l-2 1c1 2 3 2 1 5h1c1 0 1 1 1 1-1 2-3 3-5 5l-1-1 1-1h0l1-1c1-2 1-4 1-5l-1-1c1-1 1-1 0-2 1-1 1-2 1-2 0-1 0-1-1-2 0 0 0-1-1-1v-2z" class="t"></path><path d="M571 449h3v-1c0-1 1-1 2-1 0-1 1-2 2-2h1 1v1h-2l-1 1v1c-1 1-1 2-1 3-1 0-2-1-3 0h0c-2 1-3 1-3 3h0l1 1c-1 2-1 2-3 2h0l-2-1v-2h0v-2l2-2h1c1-1 1-2 2-2v1z" class="L"></path><path d="M566 452l2-2h1c1-1 1-2 2-2v1h0c-1 2-2 3-2 5h-3 0v-2z" class="d"></path><path d="M577 448v-1l1-1h2v7 1 8 9-3c-1-1-1-1-1-2l-1-1v-2l1-1-1-1h-1l-2 3 1 1v1h-1l-2-2c1-3 3-3 5-5l-1-1 1-2c-1-1-1-2-1-4h1c0-2 0-2-1-4z" class="t"></path><path d="M564 451c1 1 1 1 2 1v2h0v2l2 1h0v2c1 0 1 1 1 1 1 1 1 1 1 2 0 0 0 1-1 2 1 1 1 1 0 2l1 1c0 1 0 3-1 5l-1 1h0l-1 1-1 2-1-1-4-1-2-2 1-1-2-2 1-1 2-1-1-1v-1l1-1-1-1 1-3c0-2 1-4 1-6 1-1 2-2 2-3z" class="I"></path><path d="M566 454h0v2c-1 1-1 3-1 4 1 1 1 1 1 3l-1 1-2-2c0-3 1-5 3-8z" class="Q"></path><path d="M564 451c1 1 1 1 2 1v2c-2 3-3 5-3 8h-1l-1-2c0-2 1-4 1-6 1-1 2-2 2-3z" class="E"></path><path d="M561 467l-1-1v-1l1-1-1-1 1-3 1 2h1l2 2 2 5c-2 2-3 4-6 5l-2-2 1-1-2-2 1-1 2-1z" class="p"></path><path d="M561 467l2-1c0 1 1 1 1 2-1 2-1 2-2 3h-2l-2-2 1-1 2-1z" class="U"></path><path d="M578 348c0 1 0 2-1 4l1 1c-1 1-2 3-2 5v1l-6 3c1 1 2 1 4 2h1v1c0 1-1 2-2 3l-3 1c1 0 1 1 2 2l-1 1c2 1 2 0 3 0s1 1 2 1v1c1 1 1 2 3 2l-1 1c0 1 0 1 1 1l1 4h1v10 1 13c-1 0-3-3-3-4l-2 1-1-1c-1-2-2-4-4-6 1-1 2-2 2-3l-1-2h-1c0-2 0-3-1-5 0-1-1-1-1-1-1-1-1-1-2-1l-3-3-2-2v-1c0-1-1-2-2-3l-1-2c-1 1-1 1-2 1v1l-2 1-2-1 1-1-1-1-2-2h0c-1-2-2-2-3-3v-1h0 2c1-1 1-1 1-3l-1-2h2l1-1h2s0-1 1-1l-1-2h-1l1-1 1-2h5 1l3-1 6-1v-1c3-1 5-2 7-4z" class="t"></path><path d="M568 376v-1l-1-1h3 1v3h0l-1 1c0 1 0 1-1 2-1-1-1-3-1-4z" class="S"></path><path d="M580 382h1v10c-1 0-1 0-2-1 1-1 1-1 1-2h-1l-1 1-1-1 1-1 1-1c0-1 0-1-1-2 1-2 1-2 2-3z" class="r"></path><path d="M573 393c1 1 1 2 2 3l-1 1h2c1 1 1 3 2 5l-2 1-1-1c-1-2-2-4-4-6 1-1 2-2 2-3z" class="d"></path><path d="M562 378c2 1 3 3 5 3h3v1c1 1 1 0 2 1l-1 2c1 1 1 1 1 2h0c0 1 0 3-1 4 0-2 0-3-1-5 0-1-1-1-1-1-1-1-1-1-2-1l-3-3-2-2v-1z" class="Q"></path><path d="M568 369l1 1h1v-1c1 0 1 1 2 2l-1 1c2 1 2 0 3 0s1 1 2 1v1h-2c-1 0-1 1-1 1-1 0-2 0-2-1h-1-3l1 1v1c0 1 0 3 1 4s1 0 1 1h-3c-2 0-3-2-5-3 0-1-1-2-2-3l-1-2c-1-1-1-2-2-3h6c2 0 4 0 5-1z" class="l"></path><path d="M565 375l2 2v1h-2l-1-1 1-2z" class="t"></path><path d="M563 370l1 2c-2 1-1 1-2 2 0 0-2 0-2 1l-1-2c-1-1-1-2-2-3h6z" class="h"></path><path d="M568 369l1 1h1v-1c1 0 1 1 2 2l-1 1c2 1 2 0 3 0s1 1 2 1v1h-2c-1 0-1 1-1 1-1 0-2 0-2-1h-1-3l1 1v1l-1 1-2-2v-1h-2v-1l2-1 3-3z" class="L"></path><path d="M565 363c1 0 3-1 5-1 1 1 2 1 4 2h1v1c0 1-1 2-2 3l-3 1v1h-1l-1-1c-1 1-3 1-5 1h-6c1 1 1 2 2 3-1 1-1 1-2 1v1l-2 1-2-1 1-1-1-1-2-2h0c-1-2-2-2-3-3v-1h0 2c1-1 1-1 1-3l-1-2h2c4 2 8 2 12 1h1z" class="J"></path><path d="M553 369l2 3-2 1-2-2c1 0 2-1 2-2z" class="V"></path><path d="M553 373l2-1 2 2v1l-2 1-2-1 1-1-1-1z" class="S"></path><path d="M551 364l2 5c0 1-1 2-2 2h0c-1-2-2-2-3-3v-1h0 2c1-1 1-1 1-3z" class="R"></path><path d="M574 364h1v1c0 1-1 2-2 3l-3 1v1h-1l-1-1c-1 1-3 1-5 1h-6c-1-1-1-2-1-3 1 0 1 1 2 1v1h2c2-1 3-1 5-1 1 0 1 0 2-1 1 0 1 1 3 1l4-4z" class="n"></path><path d="M565 363c1 0 3-1 5-1 1 1 2 1 4 2l-4 4c-2 0-2-1-3-1-1 1-1 1-2 1 0-2 0-3-1-5h1z" class="c"></path><path d="M578 348c0 1 0 2-1 4l1 1c-1 1-2 3-2 5v1l-6 3c-2 0-4 1-5 1h-1c-4 1-8 1-12-1l1-1h2s0-1 1-1l-1-2h-1l1-1 1-2h5 1l3-1 6-1v-1c3-1 5-2 7-4z" class="B"></path><path d="M562 355l3-1v2h0c-1 0-1 0-2 1-1 0-3 1-4 1s-1-1-2-1h-2l1-2h5 1z" class="i"></path><path d="M578 348c0 1 0 2-1 4 0 0-3 2-3 3-3 0-6 2-9 1v-2l6-1v-1c3-1 5-2 7-4z" class="M"></path><path d="M577 352l1 1c-1 1-2 3-2 5v1l-6 3c-2 0-4 1-5 1h-4l-2-2h0l1-1h2s2 0 3-1c3-1 6-1 9-4 0-1 3-3 3-3z" class="D"></path><path d="M581 393c0-1 1-2 1-3 1 3 0 7 1 11l-1 16 1 2c-1 5-1 11-1 16v4l-1-3c0 1 0 2-1 3v6h-1-1c-1 0-2 1-2 2-1 0-2 0-2 1v1h-3v-1c-1 0-1 1-2 2h-1l-2 2c-1 0-1 0-2-1l-2-1c-1 0-2 0-3-1v-1c-1-1-1-2-1-3l-2-2v-7h-1c-2-2-1-2-1-4 0-1 0-1 2-2 1 0 2 0 3-1h5v-3c1-1 1-2 1-2l2-2 1 1 3-3c1 0 1-1 2 0h1l1-3v-1c0-3 0-5-1-8-1-2-3-5-4-7 2 0 2 1 4 2 1 0 1 0 1-1l1 1 2-1c0 1 2 4 3 4v-13z" class="G"></path><path d="M580 418l-1 7-1-3v-4h2zm-6 17l1-2c-1-1-1-1-1-2 2 0 2-1 4 0-1 3-3 6-4 9l-2-1c0-2 1-3 2-4z" class="R"></path><path d="M578 422l1 3c0 2 0 4-1 6-2-1-2 0-4 0 0 1 0 1 1 2l-1 2v-1l-1-1c-1 1-1 2-2 4-1-2 0-2-1-3h-3l1-1 1-1h3c0-3 1-1 2-2s0-3 2-4v-2c1-1 1 0 2-1v-1z" class="M"></path><path d="M572 447c1-2 3-4 5-6 0-1 0-4 1-5l1-1 1-6v7 3 6h-1-1c-1 0-2 1-2 2-1 0-2 0-2 1v1h-3v-1l1-1z" class="r"></path><path d="M575 416l3 2v4 1c-1 1-1 0-2 1v2c-2 1-1 3-2 4s-2-1-2 2h-3l3-5-2-3-2-1 3-3c1 0 1-1 2 0h1l1-3v-1z" class="S"></path><path d="M571 420c1 0 1-1 2 0h1l-2 7-2-3-2-1 3-3z" class="u"></path><defs><linearGradient id="e" x1="576.207" y1="416.611" x2="575.419" y2="403.912" xlink:href="#B"><stop offset="0" stop-color="#908d8d"></stop><stop offset="1" stop-color="#a8a8a7"></stop></linearGradient></defs><path fill="url(#e)" d="M570 401c2 0 2 1 4 2 1 0 1 0 1-1l1 1 2-1c0 1 2 4 3 4v2s0 1 1 1c0 1-2 3-3 4l1 5h-2l-3-2c0-3 0-5-1-8-1-2-3-5-4-7z"></path><path d="M578 402c0 1 2 4 3 4v2s0 1 1 1c0 1-2 3-3 4l-3-10 2-1zm-11 20l1 1 2 1 2 3-3 5-1 1-1 1h3c1 1 0 1 1 3 1-2 1-3 2-4l1 1v1c-1 1-2 2-2 4l2 1-4 4 2 1v2l-1 1c-1 0-1 1-2 2h-1l-2 2c-1 0-1 0-2-1l-2-1c-1 0-2 0-3-1v-1c-1-1-1-2-1-3l-2-2v-7h-1c-2-2-1-2-1-4 0-1 0-1 2-2 1 0 2 0 3-1h5v-3c1-1 1-2 1-2l2-2z" class="K"></path><path d="M564 426c1-1 1-2 1-2l4 1v1c-1 1-1 1-3 1h0c-1-1-1-1-2-1z" class="I"></path><path d="M557 434s-1-1-2-1l1-1c2-2 4-2 7-1-1 1-1 1-2 1l-1 1c-1 0-2 1-3 1z" class="C"></path><path d="M570 424l2 3-3 5-1 1c-1 0-1 0-2-1 1-1 1-1 0-2h0l1-1 2 1v-1l1-5z" class="G"></path><path d="M563 431c1 0 2 0 3 1h0l-1 3h1l1-1h3c1 1 0 1 1 3 1-2 1-3 2-4l1 1v1c-1 1-2 2-2 4h-3 0c-2 0-3 0-5 1h0c-1 0-1 0-2 1v2c0 1-1 2-2 2h-1v-1c-1-2 0-5 0-7-1-1-1-2-2-3 1 0 2-1 3-1l1-1c1 0 1 0 2-1z" class="B"></path><path d="M559 444c0-2 1-2 2-4l-1-1v-1l1-1 1 1v3 2c0 1-1 2-2 2h-1v-1z" class="W"></path><path d="M563 431c1 0 2 0 3 1h0l-1 3h1l1-1h3c1 1 0 1 1 3 1-2 1-3 2-4l1 1v1c-1 1-2 2-2 4h-3l2-1c0-1-3-1-4-1-3 0-3-2-5-3h-1c-2 1-2 2-2 3-1-1-1-2-2-3 1 0 2-1 3-1l1-1c1 0 1 0 2-1z" class="i"></path><path d="M572 439l2 1-4 4 2 1v2l-1 1c-1 0-1 1-2 2h-1l-2 2c-1 0-1 0-2-1l-2-1c-1 0-2 0-3-1v-1l1-1h0c-1-1-1-1-1-2h1c1 0 2-1 2-2v-2c1-1 1-1 2-1h0c2-1 3-1 5-1h0 3z" class="w"></path><path d="M560 447c2 1 3 1 5 1-2 1-2 1-3 2-1 0-2 0-3-1v-1l1-1z" class="G"></path><path d="M570 444l2 1v2l-1 1c-1 0-1 1-2 2h-1l-2 2c-1 0-1 0-2-1l-2-1c1-1 1-1 3-2h0c2-1 4-2 5-4z" class="c"></path><path d="M562 441c1-1 1-1 2-1h0c2-1 3-1 5-1-1 1-2 1-3 2v1l1-1c1 0 2-1 3-1v1h0c-2 1-3 4-4 5h-2-1c-1 0-2 1-3 1-1-1-1-1-1-2h1c1 0 2-1 2-2v-2z" class="C"></path><path d="M565 478l2 1 3-1v2h0l1 1c1 1 2 2 3 2s3 0 4-1c1 1 2 1 2 2h1v7 1 8 8 1 2 2 12 7l-1 6v-3l-2 1c0-1-1-2-2-2l-1-1v1l-2-1-3-3c-1-3-3-6-6-8-1-1-2-2-3-4-4-4-8-6-12-9-2-1-3-2-5-2l-4-2h-2c-5-1-9-2-13 2l-2 1v-3c1-2 1-4 1-6v-2-1l-1-3v-6-2-3l9 4h0c1 1 3 2 4 2v-1c1 1 2 1 3 1l2-2 3-3c1-1 1-1 1-2h1c1 1 1 2 3 3h1v1h2l-1 1c1 0 2 1 3 0h1s1 0 1 1c0 0 1 1 2 1v-1h2c-1-1-1-1-1-2s1-1 1-2c2-2 3-4 5-5z" class="f"></path><path d="M573 497h2v2h-2v-2z" class="V"></path><path d="M560 488l1 1c1 0 1 1 1 2l1 1 2-2c0-1 0-1-1-1 2-1 3-1 4-1l1 1-1 1v1l-1 1h0c-2 2-4 1-6 2l-2-2 1-1v-3z" class="M"></path><path d="M569 511h-1c0-1 0-2-1-2h-1c0-2 0-2 1-3-1-1-1 0-1-1v-1l-4-2 1-1 2-1c0-1-1-2-1-4 1-1 1-1 2-1 1 1 1 2 1 4h-1v2h1l1 1h0c2 0 4-1 5 1h-1c-1 0-1 0-2 1s-2 2-2 3l2 3h-1v1z" class="i"></path><path d="M565 478l2 1 3 4-1 1-3 3c-1 0-1 1-2 0-2 0-3-3-4-4 2-2 3-4 5-5z" class="Q"></path><path d="M564 480h2c1 1 1 1 1 2 0 2 0 2-2 4h-1c-1-1-1-2-2-3 1-1 1-2 2-3z" class="U"></path><path d="M570 478v2h0l1 1c1 1 2 2 3 2s3 0 4-1c1 1 2 1 2 2h1v7 1 8 8l-2-2c-1 0-1-1-2-1h0-1v-3h1v-4h0c0-2 0-2-2-3h-1c1-2 2-4 2-5 0-2-1-3-2-4h-2v-1c0-1-2-2-2-2l-3-4 3-1z" class="V"></path><path d="M574 486c2 1 3 3 4 5l1 1-1 1-3 2h-1c1-2 2-4 2-5 0-2-1-3-2-4z" class="m"></path><path d="M580 484h1v7 1 8l-2-1h-1l2-2c0-2 0-1-1-3 0-1 1-1 1-2s-1-2-1-3c0-2 0-3 1-5z" class="l"></path><defs><linearGradient id="f" x1="584.88" y1="519.707" x2="566.753" y2="509.647" xlink:href="#B"><stop offset="0" stop-color="#605e5e"></stop><stop offset="1" stop-color="#8f8d8b"></stop></linearGradient></defs><path fill="url(#f)" d="M573 503h0c1 1 1 0 1 1l1-1 1-1v3h1 0c1 0 1 1 2 1l2 2v1 2 2 12 7l-3-5c-2-2-3-4-5-6v-2c0-1-2-2-3-3v-2h0c-1-1-1-2-1-3v-1h1l-2-3c0-1 1-2 2-3s1-1 2-1h1z"></path><path d="M579 515l-1 2c-1-1-2-2-2-4l1-1c1 1 2 2 2 3z" class="Z"></path><path d="M577 505h0c1 0 1 1 2 1l2 2v1 2l-3 1c-1-1-2-2-2-3l1-4z" class="l"></path><path d="M576 509l2 1c1-1 2-1 3-1v2l-3 1c-1-1-2-2-2-3z" class="Z"></path><path d="M581 511v2 12l-2-2v-8c0-1-1-2-2-3h1l3-1z" class="r"></path><path d="M546 481c1 1 1 2 3 3h1v1h2l-1 1c1 0 2 1 3 0h1s1 0 1 1c0 0 1 1 2 1v-1h2v1h0v3l-1 1c-1 0-2-1-3-1l-1 2v1h2c1 0 1 0 2 1l1 3c2 0 2 2 4 1v1h-2c-1 1-1 2-2 3l1 2h0v2l-4-5-2 1 1 2h0-2 0l-3-1c0-1 1-2 1-2-1-3-3-4-4-6-2-1-3-3-5-4s-4-3-7-4v-1c1 1 2 1 3 1l2-2 3-3c1-1 1-1 1-2h1z" class="m"></path><path d="M557 494c1 0 1 0 2 1l-1 1h-1-1l1-2z" class="t"></path><path d="M551 486c1 0 2 1 3 0h1s1 0 1 1c0 0 1 1 2 1-1 1 0 1-1 2-1-1-2-1-3-2-1 0-1 1-2 1-1-1-1-2-1-3z" class="w"></path><path d="M549 496c2 1 2 0 4 1 0 0 0 2 1 2l2 1c0 1 0 2 1 2l-2 1 1 2h0-2 0l-3-1c0-1 1-2 1-2-1-3-3-4-4-6h1z" class="Q"></path><path d="M546 481c1 1 1 2 3 3h1v1c-2 0-3-1-5 1v1h1v2h1v3l2 1c0 1 1 1 0 3h-1c-2-1-3-3-5-4s-4-3-7-4v-1c1 1 2 1 3 1l2-2 3-3c1-1 1-1 1-2h1z" class="n"></path><path d="M546 481c1 1 1 2 3 3h1v1c-2 0-3-1-5 1v1h1v2h1v3h-2v-3l-3-1v-2c1 0 1-1 2-1v-2c1-1 1-1 1-2h1z" class="L"></path><path d="M554 505h0 2 0l-1-2 2-1 4 5 5 5 3-1c0 1 0 2 1 3h0v2c1 1 3 2 3 3v2c2 2 3 4 5 6l3 5-1 6v-3l-2 1c0-1-1-2-2-2l-1-1v1l-2-1-3-3c-1-3-3-6-6-8-1-1-2-2-3-4-4-4-8-6-12-9-2-1-3-2-5-2l-4-2c4-1 7 3 11 2 1 1 3 2 5 4v-3l-2-3z" class="C"></path><path d="M578 527l3 5-1 6v-3l-3-7 1-1z" class="E"></path><path d="M561 518c6 4 10 9 14 15v1l-2-1-3-3c-1-3-3-6-6-8-1-1-2-2-3-4zm-7-13h0 2 0l-1-2 2-1 4 5 5 5 3-1c0 1 0 2 1 3h0v2c1 1 3 2 3 3v2c2 2 3 4 5 6l-1 1c-5-7-10-12-16-17l-2 1c-1 0-2-1-3-1v-3l-2-3z" class="d"></path><path d="M566 512l3-1c0 1 0 2 1 3h0v2l-4-4z" class="S"></path><path d="M556 508l5 3-2 1c-1 0-2-1-3-1v-3z" class="D"></path><path d="M523 482l9 4h0c1 1 3 2 4 2 3 1 5 3 7 4s3 3 5 4c1 2 3 3 4 6 0 0-1 1-1 2l3 1 2 3v3c-2-2-4-3-5-4-4 1-7-3-11-2h-2c-5-1-9-2-13 2l-2 1v-3c1-2 1-4 1-6v-2-1l-1-3v-6-2-3z" class="S"></path><path d="M533 491c2 1 3 1 5 3v1c1 1 2 1 3 3-1 1-1 1-1 2-1 0-1 0-3-1h0 0c-1-1-1-3-1-4s0 0-1-1l-2-3z" class="Y"></path><path d="M540 503l1-2h2 1l-1-1c0-1-1-1-1-2l1-1c4 1 5 5 8 7l3 1 2 3v3c-2-2-4-3-5-4-4-2-7-3-11-4z" class="O"></path><path d="M524 497c3 0 6 1 8 1v2c1 0 2 1 4 1v1l4 1c4 1 7 2 11 4-4 1-7-3-11-2h-2c-5-1-9-2-13 2l-2 1v-3c1-2 1-4 1-6v-2z" class="Z"></path><path d="M523 482l9 4h0l-1 1h0c1 1 1 2 1 2l1 1v1l2 3c1 1 1 0 1 1s0 3 1 4h0l-5-1c-2 0-5-1-8-1v-1l-1-3v-6-2-3z" class="u"></path><path d="M527 488c2 1 2 1 3 3l1 1-2 2h0-2c1-3 1-3 0-6z" class="G"></path><path d="M523 482l9 4h0l-1 1h0c1 1 1 2 1 2l1 1v1l2 3c-2 0-2 0-4-2l-1-1c-1-2-1-2-3-3h-1v-1-1c-2 0-2 0-3 1v-2-3z" class="E"></path><path d="M529 494h0l2-2c2 2 2 2 4 2 1 1 1 0 1 1s0 3 1 4h0l-5-1c-2 0-5-1-8-1v-1h1c1 0 1-1 2-1s1 0 2-1zm9-135h1l3 5 5 1c0 1 0 2 1 2v1c1 1 2 1 3 3h0l2 2 1 1-1 1 2 1 2-1v-1c1 0 1 0 2-1l1 2c1 1 2 2 2 3v1l2 2 3 3c1 0 1 0 2 1 0 0 1 0 1 1 1 2 1 3 1 5h1l1 2c0 1-1 2-2 3 2 2 3 4 4 6 0 1 0 1-1 1-2-1-2-2-4-2 1 2 3 5 4 7 1 3 1 5 1 8v1l-1 3h-1c-1-1-1 0-2 0l-3 3-1-1-2 2s0 1-1 2v3h-5c-1 1-2 1-3 1-2 1-2 1-2 2 0 2-1 2 1 4h1v7l2 2c0 1 0 2 1 3v1c1 1 2 1 3 1l2 1c0 1-1 2-2 3 0 2-1 4-1 6l-1 3 1 1-1 1v1l1 1-2 1-1 1 2 2-1 1 2 2 4 1v3c-2 1-3 3-5 5 0 1-1 1-1 2s0 1 1 2h-2v1c-1 0-2-1-2-1 0-1-1-1-1-1h-1c-1 1-2 0-3 0l1-1h-2v-1h-1c-2-1-2-2-3-3h-1c0 1 0 1-1 2l-3 3-2 2c-1 0-2 0-3-1v1c-1 0-3-1-4-2h0l-9-4v-3h-2v-1c0-1 0 0-1-1 0-2 1-4 1-6v-3l2-2-1-10v-29-11c-1-3 0-5 0-8l-1 1v2-16c0-3 0-7 1-10h1 0v-1h-1c-1-1 0-2-1-3l-1-1c-4 0 1 1-2 0h0v-2-5h0c2-1 2-1 4 0v-8-1-1c2-2 2-2 5-3l11-1z" class="X"></path><path d="M536 387v-1c2 1 2 2 3 2h1c2 0 4 1 6 1v3c-1 1-1 1-3 1v-1h-2c-1 1-1 1-2 1h-1l2-1-1-1h-1v-1h-1l-1-1v-2z" class="Q"></path><path d="M528 391c1-1 2-3 3-4l1 1h1v-1l1-1 2 1v2l1 1-2 1c-1 0-1-1-2-1l-1 4h2v1c-1 1-3 2-4 3l-1-1c-1 1-1 1-2 1l-1-1c0-1 1-2 0-3h0l1-1c0-1 1-1 1-2z" class="Y"></path><path d="M527 393c0-1 1-1 1-2 1 1 0 4 1 4v2c-1 1-1 1-2 1l-1-1c0-1 1-2 0-3h0l1-1z" class="j"></path><path d="M546 389c1 1 3 1 3 2v1c1 1 1 2 1 3v1c0 1 1 1 2 2-1 2-2 3-3 5h2-2-1-1v-1l-2-1v1c-1 1-1 2-2 2 0 1-1 1-1 2-1 0-1 1-3 1h0l-1 1h-2v-1c0-1 0-2-1-2-1-2-1-3-2-4 0-1 0-1-1-2 0 1-1 1-2 1h0v-2c1-1 3-2 4-3v-1h-2l1-4c1 0 1 1 2 1l2-1h1v1h1l1 1-2 1h1c1 0 1 0 2-1h2v1c2 0 2 0 3-1v-3z" class="S"></path><path d="M541 401v-1h4l-1 2c-1 0-1 0-2-1h-1z" class="V"></path><path d="M535 405l2-2c-1-2-1-3-2-5 0-1 0-1 1-2v1c1 0 1 0 2 1 0 1 0 1-1 2l2 1c1-1 2-2 4-3h0 1l1 2h0-4v1c-1 1-3 1-4 3h1l1-1v1l-3 3c0-1 0-2-1-2z" class="Z"></path><path d="M541 392h2v1 2h2 1c0 2-1 2-2 3h-1 0c-2 1-3 2-4 3l-2-1c1-1 1-1 1-2-1-1-1-1-2-1v-1c2-2 2-2 6-2l-1-2z" class="m"></path><path d="M543 398l-1-1c-1 1-1 0-1 1h-1v-2l2-2 1 1h2 1c0 2-1 2-2 3h-1 0z" class="S"></path><path d="M545 395h1c0 2-1 2-2 3h-1v-1c0-1 1-1 2-2z" class="f"></path><path d="M546 389c1 1 3 1 3 2v1c1 1 1 2 1 3v1c0 1 1 1 2 2-1 2-2 3-3 5h2-2-1-1v-1c1-1 2-2 3-4l-1-1-1 1h-1l-1 1-1 1-1-2c1-1 2-1 2-3h-1-2v-2c2 0 2 0 3-1v-3z" class="L"></path><path d="M546 389c1 1 3 1 3 2v1l-1 1-2-1v-3z" class="Y"></path><path d="M546 395l1 1v1 1l-1 1-1 1-1-2c1-1 2-1 2-3z" class="t"></path><path d="M537 390h1v1h1l1 1-2 1h1c1 0 1 0 2-1l1 2c-4 0-4 0-6 2-1 1-1 1-1 2 1 2 1 3 2 5l-2 2c-1-2-1-3-2-4 0-1 0-1-1-2 0 1-1 1-2 1h0v-2c1-1 3-2 4-3v-1h-2l1-4c1 0 1 1 2 1l2-1z" class="k"></path><path d="M534 376h2l2 1h4l7 2 1 1 12 7 3 3v2c0 1 2 1 1 2-1 0-1 0-2-1-1 2-2 3-2 4h0l-3-3c-1-2-3-3-5-5l-23-7-2-1 1-1c1-1 3-1 4-1l-2-1c0-1 1-1 2-2z" class="C"></path><path d="M541 380c2-1 5 0 8 1 1 0 1 1 2 1s1 0 2 1 2 2 4 2v2h-1c-5-2-10-5-15-7z" class="e"></path><path d="M550 380l12 7 3 3v2c0 1 2 1 1 2-1 0-1 0-2-1-1 2-2 3-2 4h0l-3-3 1-4-4-3h1v-2c-2 0-3-1-4-2s-1-1-2-1-1-1-2-1l1-1z" class="o"></path><path d="M560 390c2 1 3 2 4 3-1 2-2 3-2 4h0l-3-3 1-4zm-26-14h2l2 1h4l7 2 1 1-1 1c-3-1-6-2-8-1h-1-1-3l5 2c4 1 7 3 9 4s3 1 4 3h0l-23-7-2-1 1-1c1-1 3-1 4-1l-2-1c0-1 1-1 2-2z" class="H"></path><path d="M534 376h2l2 1h4c-2 1-4 2-6 2l-1 1h0l-1-1-2-1c0-1 1-1 2-2z" class="q"></path><defs><linearGradient id="g" x1="543.709" y1="359.48" x2="545.124" y2="385.904" xlink:href="#B"><stop offset="0" stop-color="#060504"></stop><stop offset="1" stop-color="#2b2928"></stop></linearGradient></defs><path fill="url(#g)" d="M538 359h1l3 5 5 1c0 1 0 2 1 2v1c1 1 2 1 3 3h0l2 2 1 1-1 1 2 1 2-1v-1c1 0 1 0 2-1l1 2c1 1 2 2 2 3v1l2 2 3 3c1 0 1 0 2 1 0 0 1 0 1 1 1 2 1 3 1 5h1c-1 0-2 0-3-1l-3 1v-1h-1l-3-3-12-7-1-1-7-2h-4l-2-1h-2c-1 1-2 1-2 2l2 1c-1 0-3 0-4 1l-1 1h-5c-1 1-1 2-1 3h-1c-1-1 0-2-1-3l-1-1c-4 0 1 1-2 0h0v-2-5h0c2-1 2-1 4 0v-8-1-1c2-2 2-2 5-3l11-1z"></path><path d="M542 364l5 1c0 1 0 2 1 2v1l-2 2c-1-2-3-4-4-6z" class="C"></path><path d="M548 368c1 1 2 1 3 3h0l2 2 1 1-1 1-1 2c-2-2-4-4-6-7l2-2z" class="F"></path><path d="M562 387l1-1c1-1 1-1 2 0h1c1 0 2 0 3 1l1-1c1 2 1 3 1 5h1c-1 0-2 0-3-1l-3 1v-1h-1l-3-3z" class="b"></path><path d="M566 386c2 2 2 3 3 4l-3 1v-1-4z" class="G"></path><path d="M562 387l1-1c1-1 1-1 2 0h1v4h-1l-3-3z" class="p"></path><path d="M559 373l1 2c1 1 2 2 2 3v1l2 2c-1 0-2 0-2 1-3 0-7-4-10-5l1-2 2 1 2-1v-1c1 0 1 0 2-1z" class="M"></path><path d="M558 378c2-1 2-1 3-1l1 2 2 2c-1 0-2 0-2 1l-4-4z" class="X"></path><path d="M559 373l1 2c1 1 2 2 2 3v1l-1-2c-1 0-1 0-3 1 0-1-1-2-1-3v-1c1 0 1 0 2-1z" class="G"></path><path d="M531 365c2 1 3 1 5 0l1-1h1l2 3 2 3 4 6 3 3-7-2h-4l-2-1h-2c-1 1-2 1-2 2l2 1c-1 0-3 0-4 1l-1 1h-5c-1 1-1 2-1 3h-1c-1-1 0-2-1-3l-1-1c-4 0 1 1-2 0h0v-2-5h0c2-1 2-1 4 0v-8-1c2 1 3 2 5 1h3 1z" class="J"></path><path d="M534 374l2 1v1h-2 0-3l3-2z" class="G"></path><path d="M528 372c-2-1-3-2-4-3h0v-2c1-1 1-1 3-1l-1 1h0l-2 1 1 1h1l2 1v2z" class="c"></path><path d="M531 365c2 1 3 1 5 0l1-1h1l2 3-3 2c-1 0-3 1-4 2l-2 1c-1-1-2 0-3 0v-2l-2-1h-1l-1-1 2-1h0c1 1 1 1 2 1l3-3z" class="j"></path><path d="M540 367l2 3 4 6 3 3-7-2h-4l-2-1v-1l-2-1c-1-1-2-2-3-2l2-1c1-1 3-2 4-2l3-2z" class="n"></path><path d="M540 367l2 3c-2 0-3 0-5-1l3-2z" class="E"></path><path d="M537 372l2 2-1 3-2-1v-1c0-1 1-2 1-3z" class="c"></path><path d="M531 372l2-1c2 0 3 0 4 1 0 1-1 2-1 3l-2-1c-1-1-2-2-3-2z" class="p"></path><path d="M539 374l2-1v1h0v1c2 0 3 0 5 1h0l3 3-7-2h-4l1-3z" class="P"></path><path d="M541 375c2 0 3 0 5 1l-2 1c-1 0-1 0-3-1v-1z" class="G"></path><path d="M518 373h0c2-1 2-1 4 0 3 2 6 2 9 3h3 0c-1 1-2 1-2 2l2 1c-1 0-3 0-4 1l-1 1h-5c-1 1-1 2-1 3h-1c-1-1 0-2-1-3l-1-1c-4 0 1 1-2 0h0v-2-5z" class="R"></path><path d="M518 378c1 0 2-1 4-1h1l2 1c2 1 4 1 6 0h1l2 1c-1 0-3 0-4 1l-1 1h-5c-1 1-1 2-1 3h-1c-1-1 0-2-1-3l-1-1c-4 0 1 1-2 0h0v-2z" class="F"></path><path d="M565 390h1v1l3-1c1 1 2 1 3 1l1 2c0 1-1 2-2 3 2 2 3 4 4 6 0 1 0 1-1 1-2-1-2-2-4-2 1 2 3 5 4 7 1 3 1 5 1 8v1l-1 3h-1c-1-1-1 0-2 0l-3 3-1-1-2 2s0 1-1 2v3h-5c0-1-2-3-3-3h0l2-2h0l-4-3c-1-1-1 0-2 0h-1l-1-1c-1-1-1-2-2-3v-2l1-1c-1 0-1 0-2-1h1 1c1-1 1-2 2-2h1c0-1 1-1 2-2-1-1-2-2-2-3l1-2h-1v-2h-1v1h-2c1-2 2-3 3-5-1-1-2-1-2-2v-1c0-1 0-2-1-3v-1h0c2 1 2 1 4 1v1c1 0 1 1 2 1h0c1 1 0 1 1 1 1 1 2 2 2 3v1l2 1 2-3h0c0-1 1-2 2-4 1 1 1 1 2 1 1-1-1-1-1-2v-2z" class="Q"></path><path d="M563 407l1 1c1 0 1 0 2 1l2 2 1 2-3 1h-1 0c-2-1-1-5-2-7z" class="C"></path><path d="M552 402l2 1 2-1s1 1 1 2h1c0 1 0 1 1 2h0l-1 1c-1 0-2 0-3 1l-1 1c-1-1-2-2-2-3l1-2h-1v-2z" class="l"></path><path d="M562 397h0l1 5v3h-1-1v2h-2c1-1 1-2 1-2l-1-2c-1-1-1-2-2-3l1-1 2 1 2-3z" class="P"></path><path d="M558 399l2 1 1 2c-1 0-1 1-2 1-1-1-1-2-2-3l1-1z" class="K"></path><path d="M567 402l1 2v7l-2-2c-1-1-1-1-2-1l-1-1v-2h0v-3c1 0 2 0 3 1h1v-1z" class="B"></path><path d="M563 402c1 0 2 0 3 1v1l-3 1h0v-3z" class="F"></path><path d="M567 402l-1-3 1-1c1 0 2 2 3 3 1 2 3 5 4 7-3 2 0 5-4 5h-1l-1-2v-7l-1-2z" class="I"></path><path d="M568 404c1 3 2 5 2 8v1h-1l-1-2v-7z" class="U"></path><path d="M559 422h2c1-1 1-2 1-3l1 1h0c0 1 0 2 1 2h3 0l-2 2s0 1-1 2v3h-5c0-1-2-3-3-3h0l2-2h0l-4-3c2-1 3-1 4-1l1 2z" class="L"></path><path d="M554 421c2-1 3-1 4-1l1 2h1c0 2 0 2 1 3l-1 1c-1 0-2-1-2-2h0l-4-3z" class="Z"></path><path d="M570 413c4 0 1-3 4-5 1 3 1 5 1 8v1l-1 3h-1c-1-1-1 0-2 0l-1-1c-1 0 0 0-1 1h0l-2-1h0c0-1-1-2-1-3h-1v-2h1l3-1h1z" class="X"></path><path d="M570 419c1 0 1-1 2-2v-1c2 0 2 0 3 1l-1 3h-1c-1-1-1 0-2 0l-1-1zm-5-5h1c2 0 3 0 4 1 0 1 0 2-1 3l-2 1h0c0-1-1-2-1-3h-1v-2z" class="J"></path><path d="M565 390h1v1l3-1c1 1 2 1 3 1l1 2c0 1-1 2-2 3 2 2 3 4 4 6 0 1 0 1-1 1-2-1-2-2-4-2-1-1-2-3-3-3l-1 1 1 3v1h-1c-1-1-2-1-3-1l-1-5c0-1 1-2 2-4 1 1 1 1 2 1 1-1-1-1-1-2v-2z" class="D"></path><path d="M569 390c1 1 2 1 3 1l1 2c0 1-1 2-2 3l-5-5 3-1z" class="E"></path><path d="M555 408c2 0 2 1 4 0v1c1 1 2 2 2 3s-1 1-1 2v1l-1 1 1 1v2c-1 0-1 0-2 1-1 0-2 0-4 1-1-1-1 0-2 0h-1l-1-1c-1-1-1-2-2-3v-2l1-1c-1 0-1 0-2-1h1 1c1-1 1-2 2-2h1c0-1 1-1 2-2l1-1z" class="l"></path><path d="M548 413h1c1-1 1-2 2-2v3h0-2c-1 0-1 0-2-1h1z" class="L"></path><path d="M548 415c2 2 3 3 5 3-1 1-2 2-2 3l-1-1c-1-1-1-2-2-3v-2z" class="V"></path><path d="M555 408c2 0 2 1 4 0v1c0 1-1 1-1 2-1-1-2-1-3-1l-2 2-1-1c0-1 1-1 2-2l1-1z" class="K"></path><path d="M559 409c1 1 2 2 2 3s-1 1-1 2v1l-1 1c-1 1-2 1-3 1l-1-1c1 0 1 0 2-1s1-2 1-4c0-1 1-1 1-2z" class="E"></path><path d="M523 384c0-1 0-2 1-3h5l2 1h0v2h1-1c-2 1-3 2-4 4 0 0-1 2-1 3l1 1v1l-1 1h0c1 1 0 2 0 3l1 1c1 0 1 0 2-1l1 1v2h0c1 0 2 0 2-1 1 1 1 1 1 2 1 1 1 2 2 4 1 0 1 1 1 2v1h2l1-1h0c2 0 2-1 3-1 0-1 1-1 1-2 1 0 1-1 2-2v-1l2 1v1h1 1 2v-1h1v2h1l-1 2c0 1 1 2 2 3-1 1-2 1-2 2h-1c-1 0-1 1-2 2h-1-1c1 1 1 1 2 1l-1 1v2c1 1 1 2 2 3l1 1h1c1 0 1-1 2 0l4 3h0l-2 2h0c1 0 3 2 3 3-1 1-2 1-3 1-2 1-2 1-2 2 0 2-1 2 1 4v1h-1l-1 1h-1c-2 0-2 1-3 2v2h-1-1l1-1-2-2h-1c-1 1-2 0-3 0l1 3-1 1c-2-2 0-3-2-4h-1l-1 2c0 1 0 2-1 3h-1c1 1 1 1 2 1l1 1-1 1h-2v1l-1 3c0 1 0 1 1 0v2h0c-2 1-2 1-3 2v2l-1-1c-1 1-1 0-2 2h0v1 1c0 2-1 3 0 6h0l-1-1v-3c1-1 0-2 0-3-1-1 0-1-1-2 0-1-2-1-2-1l-1 1-3-1v-29-11c-1-3 0-5 0-8l-1 1v2-16c0-3 0-7 1-10h1 0v-1z" class="s"></path><path d="M521 411v-16c0-3 0-7 1-10h1l-1 31c-1-3 0-5 0-8l-1 1v2z" class="q"></path><path d="M527 417h1c0 2-1 5 0 6v4c0 1 1 1 1 2l1 2h-1c-1 2-1 3-1 4v1 3 1c-1 2 0 4 0 6v1l1 1c-1 1-1 1-1 2l2 3v2h2 0v1c-1 1-1 0-2 2h0v1 1c0 2-1 3 0 6h0l-1-1v-3c1-1 0-2 0-3-1-1 0-1-1-2 0-1-2-1-2-1v-2c-1-1 0-4 0-6v-12-5h1l-1-1c0-2 0-2 1-4h0c-2-3 0-6 0-9z" class="j"></path><path d="M529 397l1 1v2h0c1 0 2 0 2-1 1 1 1 1 1 2 1 1 1 2 2 4 1 0 1 1 1 2v1h-3c0 1-1 1-2 2h0c-2 2-2 3-1 6-1-1-1-2-2-4l-1 2c1 1 1 2 1 3h-1l-1-1v-1-2-1-4h1l-1-1v-1-3-1c0-1 0-2 1-4 1 0 1 0 2-1z" class="E"></path><path d="M530 400h0c1 0 2 0 2-1 1 1 1 1 1 2l-1 1h-2v4l-1 1c-1-1-1-2-1-3l1-2v-2h1z" class="p"></path><path d="M532 402l1-1c1 1 1 2 2 4 1 0 1 1 1 2v1h-3c0 1-1 1-2 2h0c-2 2-2 3-1 6-1-1-1-2-2-4l1-1h-1c-1-1 0-2 0-3l1-1 1-1v-4h2z" class="Y"></path><path d="M532 402l1-1c1 1 1 2 2 4 1 0 1 1 1 2v1h-3c0 1-1 1-2 2v-1c0-1 1-2 2-2h0v-1c-1 0-1-1-2-1 0-1 1-2 1-3z" class="L"></path><path d="M533 430v2c0 1-2 1-1 3v3l1 1c-1 2-2 2-1 4l2-1v1 1c0 1 1 1 2 2v1 1l-1 3c0 1 0 1 1 0v2h0c-2 1-2 1-3 2v2l-1-1v-1h0-2v-2l-2-3c0-1 0-1 1-2l-1-1v-1c0-2-1-4 0-6v-1-3-1c0-1 0-2 1-4h1v-1h3z" class="n"></path><path d="M530 453v-4h1c1 1 1 1 2 3l-1 3h-2v-2zm2-10l2-1v1 1c0 1 1 1 2 2v1 1c-1 0-1-1-2-1l-1 2-1-1c-1 0-1 0-2-1 1-1 2-2 2-4h0z" class="Q"></path><path d="M529 431v1c1 0 1 1 2 1 0 3-1 5-2 7 0 2 0 4-1 6h0c0-2-1-4 0-6v-1-3-1c0-1 0-2 1-4z" class="K"></path><path d="M530 416c-1-3-1-4 1-6h0c1 1 1 1 1 2l-2 1v2l1 1 2-2v2c0 1 0 1-1 2 0 1 1 2 0 3l1 1 1-1h1l1 2v1l-1 1 1 1h1v1l-1 1h0 1 1c0 2 0 3-1 5h0c1 1 2 1 3 1v1c0 2 0 2 2 4h0l1 3-1 1c-2-2 0-3-2-4h-1l-1 2c0 1 0 2-1 3h-1c1 1 1 1 2 1l1 1-1 1h-2v-1c-1-1-2-1-2-2v-1-1l-2 1c-1-2 0-2 1-4l-1-1v-3c-1-2 1-2 1-3v-2h-3v1l-1-2c0-1-1-1-1-2v-4c-1-1 0-4 0-6 0-1 0-2-1-3l1-2c1 2 1 3 2 4z" class="l"></path><path d="M536 440l-2-1v-2l2-1v4zm0 6c0-1 0 0-1-1v-3c2-1 1 1 2 1v-1-1h1c0 1 0 2-1 3h-1c1 1 1 1 2 1l1 1-1 1h-2v-1z" class="Z"></path><path d="M537 433h0c1 1 2 1 3 1v1c0 2 0 2 2 4h0l1 3-1 1c-2-2 0-3-2-4h-1-1c-1 0-1 1-2 1v-4l1-1v-2z" class="V"></path><path d="M528 417c0-1 0-2-1-3l1-2c1 2 1 3 2 4l1 3h0c-1 1-1 2-1 3 1 0 1 0 2 1h0l-1 3 2 2v2h-3v1l-1-2c0-1-1-1-1-2v-4c-1-1 0-4 0-6z" class="K"></path><path d="M530 430v-1c0-2 0-4 2-6l-1 3 2 2v2h-3z" class="Y"></path><path d="M536 408h2l1-1h0c2 0 2-1 3-1 0-1 1-1 1-2 1 0 1-1 2-2v-1l2 1v1h1 1 2v-1h1v2h1l-1 2c0 1 1 2 2 3-1 1-2 1-2 2h-1c-1 0-1 1-2 2h-1-1c1 1 1 1 2 1l-1 1v2c-1-1-2-2-4-2v-2 1c-2 1-2 1-2 3-1 1-2 1-4 2h0-1l-2 2h-1l-1 1-1-1c1-1 0-2 0-3 1-1 1-1 1-2v-2l-2 2-1-1v-2l2-1c0-1 0-1-1-2h0 0c1-1 2-1 2-2h3z" class="t"></path><path d="M537 411h2v2c0 1-1 1-1 1-1 0-1-1-1-2h-2l2-1z" class="L"></path><path d="M535 412l-1 1v-1h-1c1-1 2-2 4-3v2l-2 1z" class="l"></path><path d="M542 409c0-2 0-2 1-3 2 0 1 2 3 1 1-1 2-1 2-3h1l1 1v1h0 1c0 1-1 2-1 3l-2-1-3 3-1-1 1-1v-1l-3 1z" class="m"></path><path d="M542 409l3-1v1l-1 1 1 1 3-3 2 1-2 4h-1c1 1 1 1 2 1l-1 1v2c-1-1-2-2-4-2v-2 1c-2 1-2 1-2 3-1 1-2 1-4 2 0-1-1-1-1-2 1-2 2-2 3-3l-1-1v-2l1-1 2-1z" class="M"></path><path d="M539 411l1-1c1 1 2 1 2 3l-2 1-1-1v-2z" class="S"></path><path d="M544 413l1-1h1l1 1h0c1 1 1 1 2 1l-1 1v2c-1-1-2-2-4-2v-2z" class="R"></path><path d="M544 413v2c2 0 3 1 4 2s1 2 2 3l1 1h1c1 0 1-1 2 0l4 3h0l-2 2h0c1 0 3 2 3 3-1 1-2 1-3 1-2 1-2 1-2 2 0 2-1 2 1 4v1h-1l-1 1h-1c-2 0-2 1-3 2v2h-1-1l1-1-2-2h-1c-1 1-2 0-3 0h0c-2-2-2-2-2-4v-1c-1 0-2 0-3-1h0c1-2 1-3 1-5h-1-1 0l1-1v-1h-1l-1-1 1-1v-1l-1-2 2-2h1 0c2-1 3-1 4-2 0-2 0-2 2-3v-1z" class="M"></path><path d="M546 427h1c1 2 0 3 0 5h0c-2-1-2-1-2-2 1-1 1-2 1-3z" class="i"></path><path d="M546 427c-1 1-1 1-2 1h-1c1-1 1-1 1-2h1v-1c-1 0-1 0-2-1l1-1v-3h2c0 2 0 2 1 4l-1 2v1z" class="S"></path><path d="M546 420h2l1 1c0 1-1 1-1 2l1 1h0c1 2 2 0 4 1-1 1-1 2-2 2-2 1-2 3-4 5 0-2 1-3 0-5h-1v-1l1-2c-1-2-1-2-1-4z" class="R"></path><path d="M544 413v2c0 3-1 3-2 5l-2-1v1 1c1 0 1 1 2 2l-1 2-1 1-1 1 2 1v2h1l1-1 1 1c0 1-1 1-1 2v2s-2 1-2 2l1 1v2c-2-2-2-2-2-4v-1c-1 0-2 0-3-1h0c1-2 1-3 1-5h-1-1 0l1-1v-1h-1l-1-1 1-1v-1l-1-2 2-2h1 0c2-1 3-1 4-2 0-2 0-2 2-3v-1z" class="S"></path><path d="M535 421l2-2 2 2c-1 1-2 1-3 2h0l-1-2z" class="M"></path><path d="M552 421c1 0 1-1 2 0l4 3h0l-2 2h0c1 0 3 2 3 3-1 1-2 1-3 1-2 1-2 1-2 2 0 2-1 2 1 4v1h-1-1-2c-1 0-2 1-3 1v-1l2-1c0-1-1-2-1-3-1 1-1 0-1 0l-1-1h0c2-2 2-4 4-5 1 0 1-1 2-2-2-1-3 1-4-1h0l3-3z" class="S"></path><path d="M555 436h-3l-2-2v-2c1-1 2-3 4-4h1v-2l1 1v3c-2 1-2 1-2 2 0 2-1 2 1 4z" class="Z"></path><path d="M555 436h1v7l2 2c0 1 0 2 1 3v1c1 1 2 1 3 1l2 1c0 1-1 2-2 3 0 2-1 4-1 6l-1 3 1 1-1 1v1l1 1-2 1-1 1 2 2-1 1 2 2 4 1v3c-2 1-3 3-5 5 0 1-1 1-1 2s0 1 1 2h-2v1c-1 0-2-1-2-1 0-1-1-1-1-1h-1c-1 1-2 0-3 0l1-1h-2v-1h-1c-2-1-2-2-3-3h-1c0 1 0 1-1 2l-3 3-2 2c-1 0-2 0-3-1v1c-1 0-3-1-4-2h0l-9-4v-3h-2v-1c0-1 0 0-1-1 0-2 1-4 1-6v-3l2-2-1-10 3 1 1-1s2 0 2 1c1 1 0 1 1 2 0 1 1 2 0 3v3l1 1h0c-1-3 0-4 0-6v-1-1h0c1-2 1-1 2-2l1 1v-2c1-1 1-1 3-2h0v-2c-1 1-1 1-1 0l1-3v-1h2l1-1-1-1c-1 0-1 0-2-1h1c1-1 1-2 1-3l1-2h1c2 1 0 2 2 4l1-1-1-3c1 0 2 1 3 0h1l2 2-1 1h1 1v-2c1-1 1-2 3-2h1l1-1h1v-1z" class="Q"></path><path d="M538 482c-1 1-1 0-2 0h0l1-2 1 1v1z" class="r"></path><path d="M530 477c2-2 2-2 2-4 1 2 1 3 2 4l1 1v3h-1-1v-2l-3-2h0z" class="n"></path><path d="M533 457v-2c1-1 1-1 3-2-1 1-1 3-2 5h1c0-1 1-1 1-2v-1c1 0 2 0 2 1l1 2h0l-4 2v1c1 1 0 3-1 5v-3-1l-1 1v1l-1 1c-1-1 0-3 0-4l1-1v-3h0z" class="k"></path><path d="M530 466h0c-1-3 0-4 0-6v-1-1h0c1-2 1-1 2-2l1 1h0l-2 3c-1 1 0 4 0 6h0c0 2 1 3 1 4s1 2 0 3c0 2 0 2-2 4 0-2 1-2 1-3-1-1-2-2-2-3v-1-2l1-2z" class="K"></path><path d="M539 446v1h0v2l2 1 1-1 2 2c-2 1-5 1-7 3h1v2c0-1-1-1-2-1v1c0 1-1 1-1 2h-1c1-2 1-4 2-5h0v-2c-1 1-1 1-1 0l1-3v-1h2l1-1z" class="Z"></path><path d="M539 446v1h0v2c-1 1-2 1-3 2h0c-1 1-1 1-1 0l1-3v-1h2l1-1z" class="r"></path><path d="M537 467h3c2 3 3 4 2 8v2h0l-1-1c-1 1-1 2-2 3 0 1 0 1-1 1 0-2 0-2-2-3v-1c1-1 2-2 2-3-1-1-1-1-2-1h-1l1-2h0l-1-1 2-1v-1z" class="l"></path><path d="M539 469h0c1 1 1 1 1 2l-1 1c0-1-1-1-2-1v-1l2-1z" class="m"></path><path d="M544 451h0v2c1 1 1 1 2 3 0 1 1 2 1 3l-1 1c0 2-1 2-2 3-2 2-3 2-4 4h-3v1l-2 1c0-1 0-2-1-3 1-2 2-4 1-5v-1l4-2h0l-1-2v-2h-1c2-2 5-2 7-3z" class="t"></path><path d="M539 458l1-1h1v2h1c0-1 0-2 1-2l1 1 1 2v1c-2 0-2-1-3 0v2c-1 0-1 1-2 1v-2c-1-1-1-2-2-2l1-2h0z" class="S"></path><g class="V"><path d="M534 466c1-2 2-4 1-5v-1l4-2-1 2c1 0 1 1 2 2v2c-1 0-2 0-3 2v1 1l-2 1c0-1 0-2-1-3z"></path><path d="M544 451h0v2c1 1 1 1 2 3-1 1-2 1-2 2l-1-1c-1 0-1 1-1 2h-1v-2h-1l-1 1-1-2v-2h-1c2-2 5-2 7-3z"></path></g><path d="M546 460l1 5v2c0 1 1 2 1 3 1 2 1 3 2 5-1 1-1 3-3 4l-1 2h-1c0 1 0 1-1 2l-3 3-2 2c-1 0-2 0-3-1v1c-1 0-3-1-4-2h1c-1-1-1-2-2-3 0-2 0-4-1-6l3 2v2h1 1c0 1-2 1-1 2l3 1 1-2v-1-1c1 0 1 0 1-1 1-1 1-2 2-3l1 1h0v-2c1-4 0-5-2-8 1-2 2-2 4-4 1-1 2-1 2-3z" class="K"></path><path d="M536 487c1-2 1-1 2-2s1-3 1-3c1 0 2 0 3 1-1 0-2 0-2 2l1 1-2 2c-1 0-2 0-3-1z" class="L"></path><path d="M548 470c1 2 1 3 2 5-1 1-1 3-3 4l-1 2h-1c0 1 0 1-1 2l-3 3-1-1c0-2 1-2 2-2-1-1-2-1-3-1 2-2 2-3 3-5h0 1c1 0 1 0 2-1s1-1 2-1v-1l1-4z" class="S"></path><path d="M548 470c1 2 1 3 2 5-1 1-1 3-3 4l-1 2h-1v-1l1-1c1-2 1-2 1-4v-1l1-4z" class="B"></path><path d="M546 460l1 5v2c0 1 1 2 1 3l-1 4v1c-1 0-1 0-2 1s-1 1-2 1h-1v-2c1-4 0-5-2-8 1-2 2-2 4-4 1-1 2-1 2-3z" class="F"></path><path d="M547 474c-1-1-1-3-1-4-1-1-1-2-2-3h2 1c0 1 1 2 1 3l-1 4z" class="q"></path><path d="M526 456s2 0 2 1c1 1 0 1 1 2 0 1 1 2 0 3v3l1 1-1 2v2 1c0 1 1 2 2 3 0 1-1 1-1 3h0c1 2 1 4 1 6 1 1 1 2 2 3h-1 0l-9-4v-3h-2v-1c0-1 0 0-1-1 0-2 1-4 1-6v-3l2-2-1-10 3 1 1-1z" class="s"></path><path d="M526 467c1 0 2 0 3 1v2c-1 2-1 3-1 6h0c-1 0-1-1-2-1l1-4c-2-1-2-1-4-1v-1c1-1 2-1 2-1l1-1z" class="u"></path><path d="M529 470v1c0 1 1 2 2 3 0 1-1 1-1 3h0c1 2 1 4 1 6l-2-1c-1 0-2-2-2-3s0-2 1-3h0c0-3 0-4 1-6zm-3-14s2 0 2 1c1 1 0 1 1 2 0 1 1 2 0 3v3l1 1-1 2c-1-1-2-1-3-1l-1 1s-1 0-2 1v1 8 1h-2v-1c0-1 0 0-1-1 0-2 1-4 1-6v-3l2-2-1-10 3 1 1-1z" class="X"></path><path d="M521 468l2-2-1 5h-1v-3z" class="t"></path><path d="M521 471h1c1 2 0 5 1 7v1h-2v-1c0-1 0 0-1-1 0-2 1-4 1-6z" class="r"></path><path d="M523 469c0-3-1-8 1-11 1 0 1 0 2 1v2 3h1l1 1c-2 0-1 1-2 2l-1 1s-1 0-2 1z" class="s"></path><path d="M555 436h1v7l2 2c0 1 0 2 1 3v1c1 1 2 1 3 1l2 1c0 1-1 2-2 3 0 2-1 4-1 6l-1 3 1 1-1 1v1l1 1-2 1-1 1 2 2-1 1 2 2 4 1v3c-2 1-3 3-5 5 0 1-1 1-1 2s0 1 1 2h-2v1c-1 0-2-1-2-1 0-1-1-1-1-1h-1c-1 1-2 0-3 0l1-1h-2v-1h-1c-2-1-2-2-3-3l1-2c2-1 2-3 3-4-1-2-1-3-2-5 0-1-1-2-1-3v-2l-1-5 1-1c0-1-1-2-1-3-1-2-1-2-2-3v-2h0l-2-2-1 1-2-1v-2h0v-1l-1-1c-1 0-1 0-2-1h1c1-1 1-2 1-3l1-2h1c2 1 0 2 2 4l1-1-1-3c1 0 2 1 3 0h1l2 2-1 1h1 1v-2c1-1 1-2 3-2h1l1-1h1v-1z" class="V"></path><path d="M559 449c1 1 2 1 3 1l2 1c0 1-1 2-2 3l-1-1c-1 0-1 0-3 1h0v-2c-1 0-1-1-2-2l1-1 1 1 1-1z" class="I"></path><path d="M558 462c-1 0-1-1-1-2 1 0 2-1 2-1l-1-2c1-1 1-2 1-2h1c1 0 2-1 2-1 0 2-1 4-1 6l-1 3 1 1-1 1v1l1 1-2 1c-1 0-1 0-2-1 2-1 2-1 2-3l-1-1v-1z" class="r"></path><path d="M547 459l1-2v2l2-1 1 1c1 1 2 1 3 1l1 1c-1 1-2 1-3 2v1l1 1h1v-2h1l3-1v1c-1 2-3 3-5 4l-2-2h-1v1 1c-1 0-2-1-3-2l-1-5 1-1z" class="R"></path><path d="M547 459l1-2v2c1 2 1 4 2 6v1 1c-1 0-2-1-3-2l-1-5 1-1z" class="O"></path><path d="M555 436h1v7l2 2c0 1 0 2 1 3v1l-1 1-1-1 1-1c-1 0-2 0-3 1h0c-2 0-2 0-3 1v1h2v1 1c-2 0-2 0-2 1h-1 0v-4c1-1 2-1 2-2l-1-3 1-2s1 0 1-1v-1h-1v-3l1-1h1v-1z" class="L"></path><path d="M542 439c1 0 2 1 3 0h1l2 2-1 1h1 1v-2c1-1 1-2 3-2h1v3h1v1c0 1-1 1-1 1l-1 2 1 3c0 1-1 1-2 2l-2 2-1 4v1l-1 2c0-1-1-2-1-3-1-2-1-2-2-3v-2h0l-2-2-1 1-2-1v-2h0v-1l-1-1c-1 0-1 0-2-1h1c1-1 1-2 1-3l1-2h1c2 1 0 2 2 4l1-1-1-3z" class="U"></path><path d="M549 452c-1-1-2-1-3-1v-1c1 0 1 0 2-1 1-3 3-2 4-4l1 3c0 1-1 1-2 2l-2 2z" class="f"></path><path d="M542 439c1 0 2 1 3 0h1l2 2-1 1h1 1v-2c1-1 1-2 3-2h1v3h1v1c0 1-1 1-1 1-1 0-2-1-3-1l-1 1c0 1 0 1-1 2 0 0-1 1-1 2-1 1-2 1-3 1 0-2 3-2 2-4l-1-1v-1c-2 2-4 4-3 6v1l-1 1-2-1v-2h0v-1l-1-1c-1 0-1 0-2-1h1c1-1 1-2 1-3l1-2h1c2 1 0 2 2 4l1-1-1-3z" class="S"></path><path d="M558 463l1 1c0 2 0 2-2 3 1 1 1 1 2 1l-1 1 2 2-1 1 2 2 4 1v3c-2 1-3 3-5 5 0 1-1 1-1 2s0 1 1 2h-2v1c-1 0-2-1-2-1 0-1-1-1-1-1h-1c-1 1-2 0-3 0l1-1h-2v-1h-1c-2-1-2-2-3-3l1-2c2-1 2-3 3-4-1-2-1-3-2-5 0-1-1-2-1-3v-2c1 1 2 2 3 2v-1-1h1l2 2c2-1 4-2 5-4z" class="R"></path><path d="M559 476h1c1 0 1 1 2 2h0l-1 1c-1 0-1 0-1-1l-2-1 1-1z" class="U"></path><g class="m"><path d="M547 479l1 1 1-2c1 1 2 1 3 2h0l-2 1 2 1-2 2h-1c-2-1-2-2-3-3l1-2z"></path><path d="M558 463l1 1c0 2 0 2-2 3 1 1 1 1 2 1l-1 1 2 2-1 1c-1-1-2 0-2-2 0 0-1-1-1-2l-2 2 1 1 1 1-1 2h1c1 0 2 1 3 2h0l-1 1-3-2h-1c0 1 0 1-1 2h-2l-2 1-1 2-1-1c2-1 2-3 3-4-1-2-1-3-2-5 0-1-1-2-1-3v-2c1 1 2 2 3 2v-1-1h1l2 2c2-1 4-2 5-4z"></path></g><path d="M550 465h1l2 2 1 1-1 1v3 1l-2 4-2 1-1 2-1-1c2-1 2-3 3-4-1-2-1-3-2-5 0-1-1-2-1-3v-2c1 1 2 2 3 2v-1-1z" class="L"></path><path d="M547 465c1 1 2 2 3 2v-1c1 3 2 5 0 9-1-2-1-3-2-5 0-1-1-2-1-3v-2z" class="H"></path><path d="M510 282c1-2 0-7 2-10l1 3 2 2s1 0 2 1v3 1 7 9c2 3 2 4 2 7 1-1 1-1 1-3h1c0 1 1 2 1 3h0c0 2 0 4 1 6v4 7 18 1 5c-1 2-1 4-2 5 0 1 0 1 1 2l1-1c1 1 1 2 1 3v1l-1 1v1h1c1 1 2 1 3 2-3 1-3 1-5 3v1 1 8c-2-1-2-1-4 0h0v5 2h0c3 1-2 0 2 0l1 1c1 1 0 2 1 3h1v1h0-1c-1 3-1 7-1 10v16-2l1-1c0 3-1 5 0 8v11 29l1 10-2 2v3c0 2-1 4-1 6 1 1 1 0 1 1v1h2v3 3 2 6l1 3v1 2c0 2 0 4-1 6v3l2-1c4-4 8-3 13-2h2l4 2c2 0 3 1 5 2 4 3 8 5 12 9 1 2 2 3 3 4 3 2 5 5 6 8l3 3 2 1v-1l1 1c1 0 2 1 2 2l2-1v3l1 5 1 2c0-1 0-2 1-4v13 4l-1-1h0c-1 0-2 1-3 1l1 1 1 1v11 2 8c0 6 0 11-1 17l1 1c-1 1-1 3-1 4v9h-1c-1-1-2-1-3-1v1c-2 0-2 0-3-1l-2 1-1-1-2-2-4-4-3-3c1-2 1-2 1-4l-3-2h0c-1 0-2-1-2-2l-1 2-1-2h-1s-1 0-2 1h0c-8 9-16 15-27 19l-6 3h-1-1l-1 2c-2-1-4-2-6-4v-4c-2 0-3-1-4-2-3 0-8-4-10-5l1 1c1 2 3 3 5 4-1 0-2 0-3-1-2-1-3-2-5-2-3-2-5-4-8-7l-3-4h-1c-1 1-1 2-2 2l-1 1c0-1 0-2-1-3-1 0-1-1-1-2-1-1-2-1-3-2l-1 1h-3l1 1h1 0l2 3c-1 1-1 2-3 3h0c0 2 0 3-1 4-1 2-2 3-2 5h-1c-1 0-2 1-3 1v3c1 0 0 1 0 2h-1c-1 1-2 2-2 3v6c-1 1-1 2-1 2v1-4c-2-2-1-2-1-4l-1-4c-2-7-1-15-1-23l-2-1v-5h-1v-20l-1-1 1-1c-1-8-1-17-1-25v-50l1-37h0v5h1v-1-2-24-1c-1-1 0-3 0-5 0-4-1-8 1-12l-1-2 1-1v-3l2 1c0-1 1-3 2-5 2-3 5-6 8-9l7-6c-1 0-3 0-4-1v-1c2-1 3-3 4-4v-1l-2-1 1-1-2-3c1-1 0-1 1-2h1l-1-2h-2v1c-1-1-1-3-1-4h-1l-1-1c2 0 3 0 4-1-5-2-10-4-14-8l-3-6v-3c-1-5 0-9 2-14 2-3 4-5 7-7s7-3 10-4l5-1c0-1 0-1 1-2v-1c2-3 3-9 2-12-1-2-1-4-1-6l1-1c0 1 0 2 1 3v1c-1 1-1 1-1 3h2v1l1 1c1 1 1 2 1 3l1 1 2-2v-1h0c2-1 6-5 8-7l2-2h0c2 0 4-1 5-2v-2h1c0-1 1-2 1-3 1 0 1-1 1-1l-1-2 1-1v-1l-2-1v-1l2 1v-1h3c-1-1 0-2-1-4h0l1-1z" class="o"></path><path d="M516 372l2 1v5 2l-2 1v-9z" class="V"></path><path d="M506 373l4-1v3c-1 1-2 1-4 1l-1-2h0c-1 1-1 1-2 1h-2c1-1 4-2 5-2z" class="D"></path><path d="M516 450h1c3 3 0 7 3 10v2l-1-2h-2c-1 1 0 2 0 3h0l-1 3h0v-16z" class="l"></path><path d="M517 303c2 7-2 38 0 41l1-1v-3l1 1v5 3h-1v3 8l-2-1 1-56z" class="Z"></path><path d="M511 281c0-2 0-3 1-4 1 1 3 2 5 4v1 7 9 4c-1-4 0-9-1-12 0-2 0-4-1-5v-1c-1 1-2 2-2 3 0 3-2 5-2 8v-14z" class="O"></path><path d="M511 281c0-2 0-3 1-4 1 1 3 2 5 4v1 7l-1-7c-2-1-2-1-5-1z" class="U"></path><path d="M516 359l2 1h1c1 1 1 2 3 3h0v1 1 8c-2-1-2-1-4 0h0l-2-1 1-5-1-8z" class="h"></path><path d="M517 367h1l1-1 1 2-1 1c0 1 0 1 1 2 1 0 1 0 1-1l-1-2c1-1 1-1 1-2l1-1v8c-2-1-2-1-4 0h0l-2-1 1-5z" class="I"></path><path d="M518 352v-3h1l2 2c0 1 0 1 1 2l1-1c1 1 1 2 1 3v1l-1 1v1h1c1 1 2 1 3 2-3 1-3 1-5 3h0c-2-1-2-2-3-3h-1v-8z" class="H"></path><path d="M521 359l-1-1c0-1 0-1 1-2l1 2c0 1 0 1-1 1z" class="a"></path><path d="M518 352c1 1 2 2 3 4-1 1-1 1-1 2l1 1c-1 0-2 1-2 1h-1v-8z" class="D"></path><path d="M511 595c1 1 0 8 0 10 1 0 1 0 1 1l-1 2c2 0 2-1 4-1h0c0-1 1-2 0-3v-10c1-4 0-7 0-11 1-1 1-1 0-1v-4c1-1 1-3 1-4h0v29c0 4 0 9 1 14l-1 2c-2-1-4-2-6-4v-4c-2 0-3-1-4-2h0 3v-4c0-2 0-3-1-5l1-1v-3c1-3 0-5 2-8v7z" class="v"></path><path d="M511 588v7c0 5 0 11-1 16-2 0-3-1-4-2h0 3v-4c0-2 0-3-1-5l1-1v-3c1-3 0-5 2-8z" class="Q"></path><path d="M509 533h1c0 2-1 5 0 8v33c1 5 1 10 1 14-2 3-1 5-2 8 0-3 1-10 0-12l-1-1c1-1 1-2 1-3l-2-1c0-1-1-2-1-3 1-2 0-4 1-6v-2-2-1-4h0 1l-1-2 1-2 1-5c-2-2-2-3-2-6v-1-7h1v-3h1v-2z" class="V"></path><path d="M508 535h1c0 6 1 12 0 17-2-2-2-3-2-6v-1-7h1v-3z" class="T"></path><path d="M511 381v9 16 26l-2 1v-3h0l-1-1-1-1v-3c1-4 0-8 0-13 0-1 0-3-1-4v-1l1-1c0-3 0-7-1-9 1-1 1-1 1-2v-1-4-2-3-2c1 0 2 1 3 1l1-3z" class="M"></path><path d="M511 381v9 16h0c-1 2-1 5-1 8h0c0-3 1-6 0-9s0-8-1-10c0-4 0-4-2-7v-3-2c1 0 2 1 3 1l1-3z" class="Z"></path><path d="M507 388c2 3 2 3 2 7v12 14 9h0 0l-1-1-1-1v-3c1-4 0-8 0-13 0-1 0-3-1-4v-1l1-1c0-3 0-7-1-9 1-1 1-1 1-2v-1-4-2z" class="C"></path><path d="M510 282c1-2 0-7 2-10l1 3 2 2s1 0 2 1v3c-2-2-4-3-5-4-1 1-1 2-1 4v14l-1 17-1 1c1-2 1-4 0-6l-3 1-1-1h0l-2 1c0-1-2-2-2-2h-2c-1 0-2 0-3-1l-5 6c-1 0-1 0-2-1h0c2-1 6-5 8-7l2-2h0c2 0 4-1 5-2v-2h1c0-1 1-2 1-3 1 0 1-1 1-1l-1-2 1-1v-1l-2-1v-1l2 1v-1h3c-1-1 0-2-1-4h0l1-1z" class="l"></path><path d="M505 287l2 1v-1h3l-1 18-1 1v-3c-2-3 0-3 0-6-2 0-3 1-4 2v-2h1c0-1 1-2 1-3 1 0 1-1 1-1l-1-2 1-1v-1l-2-1v-1z" class="f"></path><path d="M504 299c1-1 2-2 4-2 0 3-2 3 0 6v3c-1 0-2 0-3 1h0l-2 1c0-1-2-2-2-2h-2c-1 0-2 0-3-1l-5 6c-1 0-1 0-2-1h0c2-1 6-5 8-7l2-2h0c2 0 4-1 5-2z" class="D"></path><path d="M502 302l1-1c1 1 1 1 1 2 1 1 1 2 1 3v1l-2 1c0-1-2-2-2-2h-2c-1 0-2 0-3-1h0c1 0 2 0 2-1l2-2h2 0z" class="a"></path><path d="M502 302h0c2 2 2 3 2 5-2-1-2-2-3-3l1-2z" class="N"></path><path d="M517 298c2 3 2 4 2 7 1-1 1-1 1-3h1c0 1 1 2 1 3h0c0 2 0 4 1 6v4 7 18 1 5c-1 2-1 4-2 5l-2-2v-3-5l-1-1v3l-1 1c-2-3 2-34 0-41v-1-4z" class="h"></path><path d="M519 335h-1v-14c0-3-1-7 1-10h0v16c0 2 1 7 0 8z" class="L"></path><path d="M519 305c1-1 1-1 1-3h1c0 1 1 2 1 3h0c0 2 0 4 1 6v4 7h-1v4c0 1-1 1-1 2l-2-1v-16c1-2 0-4 0-6z" class="n"></path><path d="M521 328c0-1 1-1 1-2v-4h1v18 1 5c-1 2-1 4-2 5l-2-2v-3-5l1-1-1-5c1-1 0-6 0-8l2 1z" class="r"></path><path d="M519 327l2 1 1 1v2l-2 1c0 1 0 1 1 2s1 2 1 3l-2 3c1 2 1 4 1 6l-1 2c0-1-1-1-1-2v-5l1-1-1-5c1-1 0-6 0-8z" class="E"></path><path d="M507 439l-1-5c1-2 0-6 1-9v3l1 1 1 1h0v3l2-1v36l-1 8s-1-1-2-1c1-2 1-2 1-4-2 1-2 0-3 2 0 2 0 2-1 3l-3 3c1-2 1-3 1-5-1 2-2 3-4 3 1-1 1-2 2-4 0-2 1-3 2-4h0c2-2 1-6 1-8h0v-2c1-2 0-3 0-4l3 1v-3h-1v-2c0-1-1-2-2-3h0l-1-1 1-1-1-7h1 3z" class="q"></path><path d="M509 445c1 2 1 4 1 6l-1 1-2-2c1-1 1-3 1-5h1z" class="C"></path><path d="M509 471v-1c-1 0-1 0-2-1h0l3-3 1 2-1 8s-1-1-2-1c1-2 1-2 1-4z" class="m"></path><path d="M507 453c1 1 1 2 1 3s1 2 2 4c-1 2-1 3-3 5h-1v-3s1-1 1-2l-1-1c0-1 1-2 1-3v-3z" class="T"></path><path d="M503 439h1 3c0 2-1 2 0 4l1 2h0c0 2 0 4-1 5l-2-3-1 1h0l-1-1 1-1-1-7z" class="O"></path><path d="M507 439l-1-5c1-2 0-6 1-9v3l1 1 1 1h0v3h0v2 4l-1 1h0l2 2-1 2v1h-1 0l-1-2c-1-2 0-2 0-4z" class="H"></path><path d="M504 455l3 1c0 1-1 2-1 3l1 1c0 1-1 2-1 2v3c0 2 0 4-1 6-1 1-1 2-2 3-1 2-2 3-4 3 1-1 1-2 2-4 0-2 1-3 2-4h0c2-2 1-6 1-8h0v-2c1-2 0-3 0-4z" class="D"></path><defs><linearGradient id="h" x1="511.158" y1="498.133" x2="525.632" y2="493.26" xlink:href="#B"><stop offset="0" stop-color="#6d6c6b"></stop><stop offset="1" stop-color="#8a8885"></stop></linearGradient></defs><path fill="url(#h)" d="M517 463h0c0-1-1-2 0-3h2l1 2-1 1h1c0 1 0 3 1 4v1 3c0 2-1 4-1 6 1 1 1 0 1 1v1h2v3 3 2 6l1 3v1 2c0 2 0 4-1 6v3l-1 4 1 2v6 3l-2-2c0-2 0-2-1-3l-1-1h-1v3c-2-1-1-2-2-3v-51h0l1-3z"></path><path d="M519 485h-1c-1-2 0-2 0-3 1-1 1 0 3 0l-2 3zm2 0h2v2c-1 1-1 1-3 1 0-1 0-2 1-3z" class="M"></path><path d="M521 479h2v3 3h-2-2l2-3s1-1 1-2l-1-1z" class="S"></path><path d="M517 463h0c0-1-1-2 0-3h2l1 2-1 1h1c0 1 0 3 1 4v1 3c0 2-1 4-1 6 1 1 1 0 1 1-1-1-3-1-4-2h0v-1l-1-9 1-3z" class="h"></path><path d="M517 463l2 1v3c-1 1 0 4 1 6l-2 2h-1l-1-9 1-3z" class="d"></path><path d="M523 487v6l1 3v1 2c0 2 0 4-1 6v3l-1 4 1 2v6 3l-2-2c0-2 0-2-1-3l-1-1h-1 0c-1-3 1-3 2-5l-2-2v-3c1 0 2-1 2-2v-1l-1-1h-1c0-2 0-4 1-6h0v-1-2c0-1 0-1-1-2v-3h1l1-1c2 0 2 0 3-1z" class="U"></path><path d="M523 487v6h0c-1 0-2 0-3-1-1 0-1-2-1-3l1-1c2 0 2 0 3-1z" class="S"></path><path d="M520 518v-2h1v-2l1-1v-1l1 2v6 3l-2-2c0-2 0-2-1-3zm-2-138h0c3 1-2 0 2 0l1 1c1 1 0 2 1 3h1v1h0-1c-1 3-1 7-1 10v16-2l1-1c0 3-1 5 0 8v11 29l1 10-2 2v-1c-1-1-1-3-1-4h-1l1-1v-2c-3-3 0-7-3-10h-1v-41c0-9-1-19 0-28h0l2-1z" class="R"></path><path d="M517 450l1-1 1 1c0 2 0 2 2 4 0 4 1 9 0 13-1-1-1-3-1-4h-1l1-1v-2c-3-3 0-7-3-10z" class="V"></path><path d="M521 411v-2l1-1c0 3-1 5 0 8v11 10c-1-2-1-5-1-6l-1-12 1-8z" class="F"></path><path d="M518 380h0c3 1-2 0 2 0l1 1c1 1 0 2 1 3h1v1h0-1c-1 3-1 7-1 10v16l-1 8c0 2 0 4-2 5 0-2-1-2-1-3-1-2-1-6-1-8 0-10 1-21 0-32h0 0l2-1z" class="I"></path><path d="M502 479l3-3c1-1 1-1 1-3 1-2 1-1 3-2 0 2 0 2-1 4 1 0 2 1 2 1 0 8 1 17-1 25v1h0c-2 1-4 2-5 2h-1c-1 1-2 1-3 2-2 0-2 0-4 1h0l2 2c-2 0-3 0-5-1v-1h-2-1l-1-1h-1c-1 0-2 1-3 1-3 0-5 3-8 3h-1l2-3h-1c-1 0-1 0-2 1h-1l3-6v-1c1-2 3-3 4-5 1-1 2-3 3-4 2-3 3-8 4-11h1l1-5 1 1c-1 2 0 2 0 4h1c1 0 2-1 3-2l4-2c2 0 3-1 4-3 0 2 0 3-1 5z" class="w"></path><path d="M501 486l8-4c0 2-1 6 0 8v1l-1-1-1 2s1 1 1 2-1 2-2 3v-2-2-2c-2 0-2-1-4-1v-2c-2 1-3 2-5 2h-1c2-2 4-3 5-4z" class="a"></path><path d="M501 486l8-4c0 2-1 6 0 8v1l-1-1h1l-1-1h-2c-1 0 1-2 0-4l-3 2-2-1z" class="F"></path><path d="M494 504c4-3 9-4 14-6 1 1 1 3 1 4-2 1-4 2-5 2h-1c-1 1-2 1-3 2-2 0-2 0-4 1h0l2 2c-2 0-3 0-5-1v-1h-2-1l-1-1 5-2z" class="e"></path><path d="M494 504h6v-2c1-1 1-1 2 0l1 2c-1 1-2 1-3 2-2 0-2 0-4 1h0l2 2c-2 0-3 0-5-1v-1h-2-1l-1-1 5-2z" class="v"></path><path d="M501 483c2-1 6-4 8-4v3l-8 4c-1 1-3 2-5 4h0l-9 6c-2 1-3 2-5 2l-1-1c5-5 14-11 20-14z" class="e"></path><path d="M499 477c2 0 3-1 4-3 0 2 0 3-1 5h-1l1 1v1h-1v2c-6 3-15 9-20 14l-4 5v-1c1-2 3-3 4-5 1-1 2-3 3-4 2-3 3-8 4-11h1l1-5 1 1c-1 2 0 2 0 4h1c1 0 2-1 3-2l4-2z" class="t"></path><path d="M499 477c2 0 3-1 4-3 0 2 0 3-1 5h-1l1 1v1h-1c-2 1-2 1-3 2h-1l-1-1c-1 1-1 1-1 2l-2 2h-1l1-1v-1c-2 0-2 1-4 2h0v-5l1-5 1 1c-1 2 0 2 0 4h1c1 0 2-1 3-2l4-2z" class="M"></path><path d="M499 477c2 0 3-1 4-3 0 2 0 3-1 5h-1l1 1v1c-2 0-2-1-3-2 0 1-1 1-1 1-1 1-2 1-2 1l-1 1c-1 0-2-1-3-1 1 0 2-1 3-2l4-2z" class="B"></path><path d="M496 490h1c2 0 3-1 5-2v2c2 0 2 1 4 1v2 2 2l-5 3c-5 1-9 4-13 6-1 0-2 1-3 1-3 0-5 3-8 3h-1l2-3h-1c-1 0-1 0-2 1h-1l3-6 4-5 1 1c2 0 3-1 5-2l9-6h0z" class="o"></path><path d="M496 490h1c2 0 3-1 5-2v2c2 0 2 1 4 1v2 2 2l-5 3v-2l3-2v-2c-1-1-1-1-1-2l-2-2v-1 1c-1 1-2 1-3 2h0c-1 0-1 0-2 1-5 1-8 4-12 7l-3 3-3 4h-1c-1 0-1 0-2 1h-1l3-6 4-5 1 1c2 0 3-1 5-2l9-6h0zm-9-61c1-1 2-1 3-2 0 1-1 2-1 3v1h2l1 1v1c1 1 3 1 4 2v3l1 1h1c0 1-1 2-1 3v1 1l1-1 1-1c2 2 0 4 5 4l-1 1 1 1h0c1 1 2 2 2 3v2h1v3l-3-1c0 1 1 2 0 4v2h0c0 2 1 6-1 8h0c-1 1-2 2-2 4-1 2-1 3-2 4l-4 2c-1 1-2 2-3 2h-1c0-2-1-2 0-4l-1-1c-1-3 1-6-1-9 1-2 0-6-1-8 1-2 1-3 2-6l-1-1-3-1-1-1c-2 1-3 0-4 0v1l-1 1c-1 1-1 1-2 1 0-1-1-3-1-4l-1-1v-1-6c0-1 2-2 2-3l1-1 3-2c-1 0-1 0-1-1v-1c3 0 5-2 6-4z" class="g"></path><path d="M490 452c0-2 1-2 1-3v-1c1 1 1 2 1 3l-2 1z" class="e"></path><path d="M504 455v-1c0-1 1-2 2-3v2h1v3l-3-1z" class="B"></path><path d="M496 435v3l1 1h1c0 1-1 2-1 3-1-1-2-1-3-2v-2c0-1 1-2 2-3z" class="N"></path><path d="M487 429c1-1 2-1 3-2 0 1-1 2-1 3v1h2l1 1-1 1h-3c1 1 2 2 3 2l-1 2c-1 1-1 2-1 2-1 1-1 2-1 3s0 3 1 4c-2 1-3-1-5 2h-1l-1-1 1-1c-2-1-3-2-4-2l-1 1h1v1 1c0 1 0 2 1 3h0 1 0v1l-1 1c-1 1-1 1-2 1 0-1-1-3-1-4l-1-1v-1-6c0-1 2-2 2-3l1-1 3-2c-1 0-1 0-1-1v-1c3 0 5-2 6-4z" class="a"></path><path d="M487 429c1-1 2-1 3-2 0 1-1 2-1 3l-3 3v1l-3 3-3 3-2-1v3c0 1 0 1-1 3v1c0 1 1 2 1 3 1 1 1 2 2 3h0c-1 1-1 1-2 1 0-1-1-3-1-4l-1-1v-1-6c0-1 2-2 2-3l1-1 3-2c-1 0-1 0-1-1v-1c3 0 5-2 6-4z" class="B"></path><path d="M495 452c1-1 2-1 2-2 0-3-3-5-5-7h1c2 1 4 3 6 5v2h1l2-1c0 1 1 1 0 2v3l1 1c1 1 1 4 1 5l-1 1h1c0 2 1 6-1 8h0c-1 1-2 2-2 4-1 2-1 3-2 4l-4 2c-1 1-2 2-3 2h-1c0-2-1-2 0-4l-1-1c-1-3 1-6-1-9 1-2 0-6-1-8 1-2 1-3 2-6l-1-1h1l2-1 3 1z" class="v"></path><path d="M491 458c1-1 1-1 0-2 1-1 3-2 4-2-1 0-1 1-2 2v2h0l1-1 2 1h0c0 1-1 2-1 3l-2-2s-1-1-2-1z" class="o"></path><path d="M494 471c0 1 0 1 1 3h0c0 1 1 2 1 2 1-1 1-1 1-2-1-1-1-1-1-3h-1l1-1c1 0 1 0 2 1v3h1l2-1c-1 2-1 3-2 4l-4 2-2-1c0-2-1-3-1-5l2-2z" class="N"></path><path d="M495 452c1-1 2-1 2-2 0-3-3-5-5-7h1c2 1 4 3 6 5v2c-1 1-3 2-4 4h0c-1 0-3 1-4 2 1 1 1 1 0 2l-1 1s0 1 1 1v2l2 2-1 2h2c1 2 0 3 0 5h0l-2 2c0 2 1 3 1 5l2 1c-1 1-2 2-3 2h-1c0-2-1-2 0-4l-1-1c-1-3 1-6-1-9 1-2 0-6-1-8 1-2 1-3 2-6l-1-1h1l2-1 3 1z" class="D"></path><path d="M489 452h1l2-1 3 1c-2 1-3 2-4 2h-1v-1l-1-1z" class="N"></path><path d="M496 390h2c2-1 3-1 5-1h1 0c2-1 2 0 3 1v4 1c0 1 0 1-1 2 1 2 1 6 1 9l-1 1v1c1 1 1 3 1 4 0 5 1 9 0 13-1 3 0 7-1 9l1 5h-3-1l1 7c-5 0-3-2-5-4l-1 1-1 1v-1-1c0-1 1-2 1-3h-1l-1-1v-3c-1-1-3-1-4-2v-1l-1-1h-2v-1c0-1 1-2 1-3-1 1-2 1-3 2l-1-1 2-2c-1 0-2-1-3-2l2-7v-2l-1 1c-1-1-1-1-1-2-1 0-2-2-3-2h-3 0v-2l1-2c-1-1-1-1 0-3l-1-1 1-1c1-1 3-1 4-2h1c0-1 1-1 1-1h1l1 1 1-1-1-2-3-1h-1 0c1-1 1-2 2-3h2l2-2 3-1h1l1-1h1z" class="v"></path><g class="N"><path d="M496 390h2c2-1 3-1 5-1h1 0c2-1 2 0 3 1v4c-1-1-2-1-4-2v-1-1h-3s0 1-1 1c-1 1-3 3-4 3-1 1-1 1-1 2 0-1 0-2-1-3v-1-1h1l1-1h1z"></path><path d="M490 392l3-1v1l-2 2-1 4 1 1c1 1 2 2 2 3v1c1 1 1 1 2 1v1l-1 1c-1 1-1 0-1 1-1 1-1 1-2 1v-4c-1-2-1-3-2-4l-1-2-3-1h-1 0c1-1 1-2 2-3h2l2-2z"></path></g><path d="M490 392l3-1v1l-2 2h-3v1 1c1 0 1 0 2 1-1 1-1 0-2 1h0l-3-1h-1 0c1-1 1-2 2-3h2l2-2z" class="O"></path><path d="M487 415l1-1c1 2 1 3 1 4 1 2 0 2 1 4 1 0 2-1 3-1v1c-1 2-2 3-2 5 0 1 1 2 2 3h0l-1 3v-1l-1-1h-2v-1c0-1 1-2 1-3-1 1-2 1-3 2l-1-1 2-2c-1 0-2-1-3-2l2-7v-2z" class="N"></path><path d="M489 400c1 1 1 2 2 4v4l-1 1v1c0 1-1 3-2 4l-1 1-1 1c-1-1-1-1-1-2-1 0-2-2-3-2h-3 0v-2l1-2c-1-1-1-1 0-3l-1-1 1-1c1-1 3-1 4-2h1c0-1 1-1 1-1h1l1 1 1-1z" class="C"></path><path d="M485 401h0c2 1 2 2 2 4l-1 1h-1c-1-1-1-1-2-1 0-2 1-2 2-4z" class="N"></path><path d="M489 400c1 1 1 2 2 4-2 3-3 5-7 7-2-1-3-2-4-3s-1-1 0-3c1 1 3 1 5 2v1h1l3-3c0-2-1-3-1-4l1-1z" class="m"></path><path d="M491 404v4l-1 1v1c0 1-1 3-2 4l-1 1-1 1c-1-1-1-1-1-2-1 0-2-2-3-2h-3 0v-2l1-2c1 1 2 2 4 3 4-2 5-4 7-7z" class="D"></path><path d="M485 414v-1c2-1 3-2 5-3 0 1-1 3-2 4l-1 1-1 1c-1-1-1-1-1-2z" class="e"></path><path d="M503 392c2 1 3 1 4 2v1c0 1 0 1-1 2 1 2 1 6 1 9l-1 1v1c1 1 1 3 1 4 0 5 1 9 0 13-1 3 0 7-1 9l1 5h-3-1l-1-1c1-2-1-4-2-6-2-1-2 1-4 1l-1-1 2-2c1 0 2 0 3-2h0 1l1 1-1 1 1 1 2-1v-2c-1 0-1-1-1-1l-1-1c1-1 1-1 2 0h1v-1l-1-1 1-1c-1-2-1-4-2-5v-4-3-2-1c1-2 1 0 1-2h0l1-1v-1c1-1 1-4 1-5-1-1-2-2-3-2 0-1 0-2 1-3h1l1 1c-1-1-1-1-2-1v-1l-1-1z" class="g"></path><defs><linearGradient id="i" x1="452.681" y1="424.053" x2="509.444" y2="384.951" xlink:href="#B"><stop offset="0" stop-color="#bdbbbb"></stop><stop offset="1" stop-color="#edecec"></stop></linearGradient></defs><path fill="url(#i)" d="M502 360h5 3c1 2 0 5 1 7l-1 5-4 1c-1 0-4 1-5 2v2c2 1 4 0 5 3 1 0 3-1 4 0l1 1-1 3c-1 0-2-1-3-1v2 3 2c-1-1-1-2-3-1h0-1c-2 0-3 0-5 1h-2-1l-1 1h-1l-3 1-2 2h-2c-1 1-1 2-2 3h0 1l3 1 1 2-1 1-1-1h-1s-1 0-1 1h-1c-1 1-3 1-4 2l-1 1 1 1c-1 2-1 2 0 3l-1 2v2h0 3c1 0 2 2 3 2 0 1 0 1 1 2l1-1v2l-2 7c1 1 2 2 3 2l-2 2 1 1c-1 2-3 4-6 4v1c0 1 0 1 1 1l-3 2-1 1c0 1-2 2-2 3v6 1l-2 3 3 6 1 3v1l-3-3-1-1c-1-2-2-2-2-3-1-3-2-4-4-5-1 0-2-1-2-1v-1c-1-1-2-2-2-4l-1-1h0c-1-1-2-3-3-3-2-6-2-11-3-17h0c-2-2 0-8 0-11h-1l-1-2 1-1v-3l2 1c0-1 1-3 2-5 2-3 5-6 8-9l7-6c-1 0-3 0-4-1v-1c2-1 3-3 4-4v-1l1-2h2c3 2 5-3 8-4h2 1 1c2-2 3-3 5-3 1-2 2-5 3-7l-2 1h-1c0-2 1-2 2-3s4-1 5-1z"></path><path d="M462 429l1-1c1 2 2 4 2 7h0 0-1c-1-2-1-4-2-6z" class="b"></path><path d="M471 398v-2c1-2 1-2 2-3h4l-6 5z" class="W"></path><path d="M464 425l2 4h0l-1 5v1c0-3-1-5-2-7l1-3z" class="n"></path><path d="M467 403h1l3-3v3h-1c-1 2-1 3-1 4l-2 3v3c0 4-1 9 1 12v2l-2 1v1h0l-2-4-1 3-1 1-1-5c-1-7 2-15 6-21z" class="X"></path><path d="M467 410v3c0 4-1 9 1 12v2l-2 1v1h0l-2-4c-1-6 0-10 3-15z" class="m"></path><path d="M489 373h1c2-2 3-3 5-3-1 2-2 4-4 5-3 2-4 3-5 7-12 6-23 12-27 26-1 5-2 9-2 14h0c-2-2 0-8 0-11h-1l-1-2 1-1v-3l2 1c0-1 1-3 2-5 2-3 5-6 8-9l7-6c-1 0-3 0-4-1v-1c2-1 3-3 4-4v-1l1-2h2c3 2 5-3 8-4h2 1z" class="I"></path><path d="M456 405l2 1c0 2 0 3-1 5h-1l-1-2 1-1v-3z" class="i"></path><path d="M478 377c3 2 5-3 8-4h2c-3 5-8 9-13 13-1 0-3 0-4-1v-1c2-1 3-3 4-4v-1l1-2h2z" class="N"></path><path d="M502 360h5 3c1 2 0 5 1 7l-1 5-4 1c-1 0-4 1-5 2-5 1-10 4-15 7 1-4 2-5 5-7 2-1 3-3 4-5s2-5 3-7l-2 1h-1c0-2 1-2 2-3s4-1 5-1z" class="M"></path><path d="M502 367l1 1v2l-1 1h-3c-1-1-1-1-1-2l4-2z" class="D"></path><path d="M509 367h2l-1 5-4 1-1-2 1-1 3 1v-1-2-1z" class="L"></path><path d="M502 367h0l-2-2v-1h3 1l2-1v1c0 1-1 1-2 3h0c1 0 2 1 3 2-1 1 0 1-1 1l-1 1-2-1v-2l-1-1z" class="T"></path><path d="M503 370l2 1 1 2c-1 0-4 1-5 2-5 1-10 4-15 7 1-4 2-5 5-7 1 1 0 1 1 1 2 0 6-2 8-3h0 2v-2l1-1z" class="V"></path><path d="M502 360h5 3c1 2 0 5 1 7h-2c0-2 0-3-1-4-1 0-2 0-4-1-2 0-4 1-6 1l-2 1h-1c0-2 1-2 2-3s4-1 5-1z" class="E"></path><path d="M506 380c1 0 3-1 4 0l1 1-1 3c-1 0-2-1-3-1v2 3 2c-1-1-1-2-3-1h0-1c-2 0-3 0-5 1h-2-1l-1 1h-1l-3 1-2 2h-2c-1 1-1 2-2 3h0 1l3 1 1 2-1 1-1-1h-1s-1 0-1 1h-1c-1 1-3 1-4 2l-1 1c-1 1-1 2-1 3l-2 2h-4v-1h-1l-4 5v-3l2-3c0-1 0-2 1-4h1v-3l-3 3h-1c1-2 3-4 4-5l6-5c8-6 18-11 29-13z" class="j"></path><path d="M486 391s5-2 6-3c0 2-1 3-2 4l-2 2h-2l-3 1c1-1 2-2 3-4z" class="f"></path><path d="M473 402v-1c2-4 9-9 13-10-1 2-2 3-3 4-3 1-7 5-10 7z" class="I"></path><path d="M492 388c4-2 10-4 15-5v2 3 2c-1-1-1-2-3-1h0-1c-2 0-3 0-5 1h-2-1l-1 1h-1l-3 1c1-1 2-2 2-4z" class="q"></path><path d="M496 390v-1-1c2-1 4-2 6-2l1 1c1 0 1 0 1-1h1l2-1v3 2c-1-1-1-2-3-1h0-1c-2 0-3 0-5 1h-2zm-13 5l3-1c-1 1-1 2-2 3h0 1l3 1 1 2-1 1-1-1h-1s-1 0-1 1h-1c-1 1-3 1-4 2l-1 1c-1 1-1 2-1 3l-2 2h-4v-1h-1l-4 5v-3l2-3 4-5c3-2 7-6 10-7z" class="B"></path><path d="M480 399c-1 1-1 2-1 2-2 2-3 3-4 5 1 0 2 1 3 1l-2 2h-4l1-1c0-3 4-7 7-9z" class="n"></path><path d="M480 399c1-1 3-2 5-2l3 1 1 2-1 1-1-1h-1s-1 0-1 1h-1c-1 1-3 1-4 2l-1 1c-1 1-1 2-1 3-1 0-2-1-3-1 1-2 2-3 4-5 0 0 0-1 1-2z" class="L"></path><path d="M480 403v-3c1 0 2-1 4-1 1 0 2 0 3 1h-1s-1 0-1 1h-1c-1 1-3 1-4 2z" class="m"></path><path d="M478 407c0-1 0-2 1-3l1 1c-1 2-1 2 0 3l-1 2v2h0 3c1 0 2 2 3 2 0 1 0 1 1 2l1-1v2l-2 7c1 1 2 2 3 2l-2 2 1 1c-1 2-3 4-6 4v1c0 1 0 1 1 1l-3 2-1 1c0 1-2 2-2 3v6 1l-2 3 3 6 1 3v1l-3-3-1-1c-1-2-2-2-2-3-1-3-2-4-4-5-1 0-2-1-2-1v-1c-1-1-2-2-2-4l-1-1h0v-5l2-1v-1h0 0v-1l1-5v-1l2-1v-2c-2-3-1-8-1-12l4-5h1v1h4l2-2z" class="C"></path><path d="M472 418s0 1 1 2v-1c1 1 2 1 2 2-1 1-2 2-3 2v-5z" class="R"></path><path d="M478 407c0-1 0-2 1-3l1 1c-1 2-1 2 0 3l-1 2-3 2v-3l2-2z" class="V"></path><path d="M472 408v1h4v3c-2 2-2 5-3 7v1c-1-1-1-2-1-2v-2c-1-2 0-5 0-8z" class="Z"></path><path d="M472 424l1-1h2c1-1 1-1 2-1l1 1h3l1 1c-1 1-2 2-3 2 1 1 2 2 3 2s2 0 3-1v2c-1 1-1 0-2 1-4-2-7-2-11-1h0c-1-2-1-2-2-3 0-1 1-2 2-3v1z" class="i"></path><path d="M472 423v1c1 1 1 1 2 1l1 1-2 2-1 1c-1-2-1-2-2-3 0-1 1-2 2-3z" class="F"></path><path d="M467 413l4-5h1c0 3-1 6 0 8v2 5h0c-1 1-2 2-2 3l-2-1c-2-3-1-8-1-12z" class="D"></path><path d="M472 418h-1-3c1-2 1-2 2-3l2 1v2z" class="N"></path><path d="M479 412h0 3c1 0 2 2 3 2 0 1 0 1 1 2l1-1v2l-2 7c1 1 2 2 3 2l-2 2 1 1c-1 2-3 4-6 4h-2c1-1 1 0 2 0 1-2 1-2 2-3s1 0 2-1v-2h-1c-1-1-2-2-2-3v-1h-1c-1-1-1-1-2-1-2 0-2-1-3-3v-1c0-1 1-1 2-2l-1-1v-2l2-1z" class="O"></path><path d="M479 412h0 3c1 0 2 2 3 2 0 1 0 1 1 2l-1 2-1-1v-1c-2 1-2 1-3 2h0c-2 0-3 1-5 1v-1c0-1 1-1 2-2l-1-1v-2l2-1z" class="W"></path><path d="M479 412h0v2l2 1v1h-3l-1-1v-2l2-1z" class="a"></path><path d="M468 425l2 1c1 1 1 1 2 3h0c4-1 7-1 11 1-1 1-1 1-2 3-1 0-1-1-2 0h2v1c0 1 0 1 1 1l-3 2-1 1c0 1-2 2-2 3v6 1l-2 3 3 6 1 3v1l-3-3-1-1c-1-2-2-2-2-3-1-3-2-4-4-5-1 0-2-1-2-1v-1c-1-1-2-2-2-4l-1-1h0v-5l2-1v-1h0 0v-1l1-5v-1l2-1v-2z" class="e"></path><path d="M471 432h0l1 1v1h-2c0-1 0-1 1-2z" class="o"></path><path d="M469 431l1 4c-2 1-3 0-5 0h0v-1c2-1 3-2 4-3z" class="g"></path><path d="M463 442v-5l2-1s-1 2-1 3c0 0 1 0 1 1 0 2 0 2 1 4v3c-1-1-2-2-2-4l-1-1h0z" class="N"></path><path d="M468 425l2 1c1 1 1 1 2 3h0l-3 2c-1 1-2 2-4 3l1-5v-1l2-1v-2z" class="M"></path><path d="M479 433h2v1c0 1 0 1 1 1l-3 2-1 1c0 1-2 2-2 3v6 1l-2 3c-3-4-4-7-3-12 2-3 3-4 7-6h1z" class="F"></path><path d="M471 439c2-3 3-4 7-6-1 2-2 2-3 4h1c-1 2-1 4-2 6-1-3-1-2-3-4z" class="r"></path><path d="M471 439c2 2 2 1 3 4l1 3h0-2v1l1 1 1-1h1v1l-2 3c-3-4-4-7-3-12z" class="k"></path><path d="M456 411h1c0 3-2 9 0 11h0c1 6 1 11 3 17 1 0 2 2 3 3h0l1 1c0 2 1 3 2 4v1s1 1 2 1c2 1 3 2 4 5 0 1 1 1 2 3l1 1 3 3v-1l-1-3-3-6 2-3 1 1c0 1 1 3 1 4 1 0 1 0 2-1l1-1v-1c1 0 2 1 4 0l1 1 3 1 1 1c-1 3-1 4-2 6 1 2 2 6 1 8 2 3 0 6 1 9l-1 5h-1c-1 3-2 8-4 11-1 1-2 3-3 4-1 2-3 3-4 5v1l-3 6h1c1-1 1-1 2-1h1l-2 3c-2 1-4 6-6 8-1 0-2 1-3 1-4 6-11 12-12 19v5c-1 2-1 4-1 6v14c-1-8-1-17-1-25v-50l1-37h0v5h1v-1-2-24-1c-1-1 0-3 0-5 0-4-1-8 1-12z" class="g"></path><path d="M463 495h2c1 1 0 1 0 3h-1l-2-1s-1-1-1-2h2z" class="M"></path><path d="M482 487c1 1 1 2 2 3v2c-1 1-2 3-3 4l1-9z" class="O"></path><path d="M466 470s0-1 1-1c0-1 1-1 1-2s0-1 1-2l1 1-1 1c0 1-2 4-2 4 0 1 1 3 1 3 0 2 2 3 2 5-2-1-4-4-5-5 0-1 1-2 1-4z" class="a"></path><path d="M457 484l1-2c2-2 4-5 5-7l1 1c0 1 0 2 1 3v2h-1l-2-1-1 1v1l-4 2z" class="W"></path><path d="M457 493c1 2 1 3 1 5-1 1-1 5-1 7l1 1v5c1 2 1 6 0 7h0c1 0 2 1 2 2h1 0c2-1 3-1 4-1l-4 5-2 1h-1v-1c0-3-1-3-2-5l1-2v-3-1c0-3 1-7-1-9 1-4-1-8 1-11z" class="a"></path><path d="M485 472c0 1 0 2 1 3s1 2 2 3v3c-1 3-2 8-4 11v-2c-1-1-1-2-2-3 0-5 2-10 3-15z" class="W"></path><path d="M486 475c1 1 1 2 2 3v3c-1 3-2 8-4 11v-2c1-5 2-9 2-15z" class="C"></path><path d="M457 467c0 2 0 4 1 6 0 1-1 1 0 2l1 2v1l4-3v-1c1-1 2-2 3-4 0 2-1 3-1 4l-2 1c-1 2-3 5-5 7l-1 2c0 2-1 3 0 5 0 2 1 3 0 4-2 3 0 7-1 11 2 2 1 6 1 9v1 3l-1 2h-1c1-5 0-10 0-15l1-9v-4c-1-1 0-3 0-4h0c-1-2-1-9 0-11h0l-1-1v-2c0-2 0-3 1-4h0l1-2z" class="F"></path><path d="M457 467c0 2 0 4 1 6 0 1-1 1 0 2l1 2v1l-2 3h0v-3-2s0-1-1-2v-5h0l1-2z" class="D"></path><path d="M464 457l1-1c1 1 2 1 3 2 2 2 1 5 4 5v1l1 2c0 1 0 1 1 2h-1l-1 1h0l-1-1v1h-1l1-1-2-1 1-1-1-1c-1 1-1 1-1 2s-1 1-1 2c-1 0-1 1-1 1-1 2-2 3-3 4v1l-4 3v-1l-1-2c-1-1 0-1 0-2-1-2-1-4-1-6v-3c1 0 2-1 3 0h1l1-1c1-2 2-3 2-6z" class="N"></path><path d="M462 463v1c-1 1-3 0-4 1 0 1 0 1 1 2 0 0 2 2 2 3v2c0 1 1 2 2 3l-4 3v-1l-1-2c-1-1 0-1 0-2-1-2-1-4-1-6v-3c1 0 2-1 3 0h1l1-1z" class="a"></path><path d="M458 475l1-1h1c0 2 0 2-1 3l-1-2z" class="W"></path><path d="M476 448l1 1c0 1 1 3 1 4 1 0 1 0 2-1l1-1v-1c1 0 2 1 4 0l1 1 3 1 1 1c-1 3-1 4-2 6 1 2 2 6 1 8 2 3 0 6 1 9l-1 5h-1v-3c-1-1-1-2-2-3s-1-2-1-3c0-2-1-4-2-6-1-3-4-5-5-8l-1-1-3-6 2-3z" class="m"></path><path d="M481 450c1 0 2 1 4 0l1 1 3 1 1 1c-1 3-1 4-2 6l-1-1c-1-1-1-2-3-3-1-1-1-1-1-2l-2-2v-1z" class="T"></path><path d="M481 450c1 0 2 1 4 0l1 1v1c1 0 1 1 2 2-1 1-2 1-3 1l-2-2-2-2v-1zm-3 8l1-2c1 0 3-1 5 0 0 1 1 1 2 2 0 1 1 3 2 5l1 5v3l-1 7c-1-1-1-2-2-3s-1-2-1-3c0-2-1-4-2-6-1-3-4-5-5-8z" class="D"></path><path d="M489 471l-2-1c-1-1-1-1-1-3v-1l3 2v3zm-3-13c0 1 1 3 2 5 0 1-1 1-1 1l-2-1-1-1c-1-1-1-1-1-3l3-1z" class="N"></path><path d="M454 451h0v5h1v-1c1 2 0 6 1 8v1 5h0c-1 1-1 2-1 4v2l1 1h0c-1 2-1 9 0 11h0c0 1-1 3 0 4v4l-1 9c0 5 1 10 0 15h1c1 2 2 2 2 5v1h1l2-1 4-5c5-6 8-13 12-18v1l-3 6h1c1-1 1-1 2-1h1l-2 3c-2 1-4 6-6 8-1 0-2 1-3 1-4 6-11 12-12 19v5c-1 2-1 4-1 6v14c-1-8-1-17-1-25v-50l1-37z" class="Z"></path><path d="M458 524v1h1l2-1-4 6-1-1c1-1 1-2 1-4 0-1 0 0 1-1z" class="H"></path><path d="M455 519h1c1 2 2 2 2 5-1 1-1 0-1 1 0 2 0 3-1 4l1 1-1 1h-1c1-4 0-8 0-12z" class="M"></path><path d="M474 508h1c1-1 1-1 2-1h1l-2 3c-2 1-4 6-6 8-1 0-2 1-3 1l7-11z" class="D"></path><path d="M456 411h1c0 3-2 9 0 11h0c1 6 1 11 3 17 1 0 2 2 3 3h0l1 1c0 2 1 3 2 4v1s1 1 2 1c2 1 3 2 4 5 0 1 1 1 2 3l1 1c1 3 3 5 4 7 3 6 3 11 2 17v3h-1c-1-2 0-4-1-7 0-1-1-2-2-4-1 0-1 0-2 1h-1v-2c1 0 1 0 1-1 0-2 0-3-1-4s-1-1-1-2l-1-2v-1c-3 0-2-3-4-5-1-1-2-1-3-2l-1 1c0 3-1 4-2 6l-1 1h-1c-1-1-2 0-3 0v3l-1 2v-5-1c-1-2 0-6-1-8v-2-24-1c-1-1 0-3 0-5 0-4-1-8 1-12z" class="M"></path><defs><linearGradient id="j" x1="454.586" y1="440.412" x2="463.31" y2="441.217" xlink:href="#B"><stop offset="0" stop-color="#9c9a99"></stop><stop offset="1" stop-color="#b9b7b5"></stop></linearGradient></defs><path fill="url(#j)" d="M455 429c1 1 1 2 2 3v3c1 1 1 2 1 3l1 3 2 3c0 2 1 4 2 5l1 2c-1 1 0 1-1 2-2-1-2 0-3-1v-2h-1v-1l-2-2v1c0 2 0 3-2 5v-24z"></path><path d="M457 447l1-1h0 1s1 1 1 2-1 1-1 1l-2-2z" class="O"></path><path d="M455 453c2-2 2-3 2-5v-1l2 2v1h1v2c1 1 1 0 3 1 1-1 0-1 1-2 1 2 2 3 4 5 1 2 3 4 5 7h-1c-3 0-2-3-4-5-1-1-2-1-3-2l-1 1c0 3-1 4-2 6l-1 1h-1c-1-1-2 0-3 0v3l-1 2v-5-1c-1-2 0-6-1-8v-2z" class="B"></path><path d="M457 457h2v1c0 2 1 3 0 4h-1-1v-4-1z" class="O"></path><path d="M459 450h1v2c1 1 1 0 3 1 1-1 0-1 1-2 1 2 2 3 4 5 1 2 3 4 5 7h-1c-3 0-2-3-4-5-1-1-2-1-3-2l-1 1c0-1-1-1-1-2-1 0-1 2-1 3v1c-1-1-1-1-2-1 0-1-1-2-2-2v-1l1-1c-1 0-1 0-2-1l1-1v-1l1-1z" class="D"></path><path d="M460 439c1 0 2 2 3 3h0l1 1c0 2 1 3 2 4v1s1 1 2 1c2 1 3 2 4 5 0 1 1 1 2 3l1 1c1 3 3 5 4 7 3 6 3 11 2 17v3h-1c-1-2 0-4-1-7 0-1-1-2-2-4-1 0-1 0-2 1h-1v-2c1 0 1 0 1-1 0-2 0-3-1-4s-1-1-1-2l-1-2v-1h1 0c2 3 5 7 6 11 0 1 0 1 1 2h0c0-3-1-7-2-9l-1-1c-5-9-15-17-17-27z" class="O"></path><path d="M481 301l1-1c0 1 0 2 1 3v1c-1 1-1 1-1 3h2v1l1 1c1 1 1 2 1 3l1 1 2-2v-1c1 1 1 1 2 1l5-6c1 1 2 1 3 1h2s2 1 2 2l2-1h0l1 1 3-1c1 2 1 4 0 6l1-1v3 19 8 11 2c0 1-1 1-2 1s-1 0-2 2l1 2h-5c-1 0-4 0-5 1s-2 1-2 3h1l2-1c-1 2-2 5-3 7-2 0-3 1-5 3h-1-1-2c-3 1-5 6-8 4h-2l-1 2-2-1 1-1-2-3c1-1 0-1 1-2h1l-1-2h-2v1c-1-1-1-3-1-4h-1l-1-1c2 0 3 0 4-1-5-2-10-4-14-8l-3-6v-3c-1-5 0-9 2-14 2-3 4-5 7-7s7-3 10-4l5-1c0-1 0-1 1-2v-1c2-3 3-9 2-12-1-2-1-4-1-6z" class="V"></path><path d="M464 347h1c2 4 3 5 6 7 1 0 1 0 1 1h0c-2 0-4-1-6-2l-4-4c1-1 2-1 2-2z" class="d"></path><path d="M486 345c1-1 2-1 3-1-1 3-1 5-3 8-1 1-2 1-4 2 1-2 2-3 3-5h-1l-1-1c2-1 3-2 3-3z" class="D"></path><path d="M471 352c-3-2-2-3-4-6h0v-3-1l1-1v2l2 1 2 4c0 1-1 3-1 4z" class="H"></path><path d="M463 342l1-2c1-3 4-5 7-6l-1 2c-1 2-3 3-4 5v3h-1l-1 1 2 1-1 1h-1c-1-1-1-2-1-3v-2z" class="l"></path><path d="M491 346c0 3 0 4-2 7v1c-1 1-2 1-3 2-1 0-2 0-3 2h-1 0-3c-1-1-1 0-1-1-1-1-2-1-3-1h-2c4 0 8 0 12-2 3-2 4-5 6-8z" class="N"></path><path d="M474 337c2 0 3-1 5 0l2 2h1c1-1 1-1 3-1 0 1 0 2-1 3l1 1c1 1 1 1 1 3 0 1-1 2-3 3l1 1h1c-1 2-2 3-3 5-4 0-7 0-11-2 0-1 1-3 1-4l-2-4-2-1v-2l4-4 1-1 1 1z" class="a"></path><path d="M482 339c1-1 1-1 3-1 0 1 0 2-1 3l-2 2v1h-1c1-2 1-3 1-5z" class="H"></path><path d="M482 344v-1l2-2 1 1c1 1 1 1 1 3 0 1-1 2-3 3l1 1c-3 1-3 1-5 1l-1-1h2v-1-2l-1-1h0l2-1h1z" class="i"></path><path d="M482 344v-1l2-2 1 1c0 1 0 3-1 4l-2-2h0z" class="a"></path><path d="M474 337c2 0 3-1 5 0l2 2c-1 1-1 2-2 2-1 1-2 3-3 4v1h0l2-1v1c0 1-1 1-2 2-1 0-3 1-4 0l-2-4-2-1v-2l4-4 1-1 1 1z" class="g"></path><path d="M473 336l1 1v2l1 2h0-2c-2 1-2 2-3 3l-2-1v-2l4-4 1-1z" class="O"></path><defs><linearGradient id="k" x1="454.568" y1="342.664" x2="481.576" y2="323.811" xlink:href="#B"><stop offset="0" stop-color="#bbb8b8"></stop><stop offset="1" stop-color="#e8e7e7"></stop></linearGradient></defs><path fill="url(#k)" d="M474 323c3 0 7 0 10 1h1v3c1 1 1 2 2 3h1-2c0 2 1 2 2 3l-1 1v1l3 6v4h-1v-3 2c-1 0-2 0-3 1 0-2 0-2-1-3l-1-1c1-1 1-2 1-3-2 0-2 0-3 1h-1l-2-2c-2-1-3 0-5 0l-1-1-1 1c-1-1-1-1-2-1l1-2c-3 1-6 3-7 6l-1 2-3-2v1c-2 1-2 1-3 3l-1 1v5l-1 1v-3c-1-5 0-9 2-14 2-3 4-5 7-7s7-3 10-4z"></path><path d="M474 323c3 0 7 0 10 1h1v3h-3c0-1-1-2-2-2h-5c-2 0-2 0-4 1h-3-1c-1 1-2 1-3 1 3-2 7-3 10-4z" class="o"></path><path d="M484 324h1v3h-3c1-1 1 0 2-1-1-1-1-1-2-1l2-1z" class="g"></path><defs><linearGradient id="l" x1="462.364" y1="333.485" x2="477.462" y2="337.219" xlink:href="#B"><stop offset="0" stop-color="#171616"></stop><stop offset="1" stop-color="#32322e"></stop></linearGradient></defs><path fill="url(#l)" d="M460 340l1-3c2-3 5-6 8-7 4-1 9-1 13 1-2 1-3 1-5 2h0c-1 0-2 1-2 1l-2 2-1 1c-1-1-1-1-2-1l1-2c-3 1-6 3-7 6l-1 2-3-2z"></path><path d="M471 334c2-2 4-2 6-1h0c-1 0-2 1-2 1l-2 2-1 1c-1-1-1-1-2-1l1-2z" class="Z"></path><path d="M482 331c2 2 4 3 5 4l3 6v4h-1v-3 2c-1 0-2 0-3 1 0-2 0-2-1-3l-1-1c1-1 1-2 1-3-2 0-2 0-3 1h-1l-2-2c-2-1-3 0-5 0l-1-1 2-2s1-1 2-1h0c2-1 3-1 5-2z" class="E"></path><path d="M486 338c1 1 2 2 3 4v2c-1 0-2 0-3 1 0-2 0-2-1-3l-1-1c1-1 1-2 1-3h1z" class="B"></path><path d="M477 333c1 0 2 0 4 1 0 0 1 0 2 1 0 1 1 1 1 1h1c1 0 1 1 1 2h-1c-2 0-2 0-3 1h-1l-2-2c-2-1-3 0-5 0l-1-1 2-2s1-1 2-1z" class="f"></path><path d="M477 333c1 0 2 0 4 1h-2l-1 1c-1 0-3 0-3-1 0 0 1-1 2-1z" class="S"></path><path d="M484 336h1c1 0 1 1 1 2h-1c-2 0-2 0-3 1h-1l-2-2c2-1 3-1 5-1z" class="q"></path><path d="M460 341v-1l3 2v2c0 1 0 2 1 3 0 1-1 1-2 2l4 4c2 1 4 2 6 2l1 1h2c1 0 2 0 3 1 0 1 0 0 1 1h3 0 1c1-2 2-2 3-2 1-1 2-1 3-2l-2 5 1 1c2-1 3-4 5-6l-1 1v3c0 1-1 1-1 2v2h0c-1 2-2 4-1 6h0c1 0 1-1 1-2l2-2h1 1 1l2-1c-1 2-2 5-3 7-2 0-3 1-5 3h-1-1-2c-3 1-5 6-8 4h-2l-1 2-2-1 1-1-2-3c1-1 0-1 1-2h1l-1-2h-2v1c-1-1-1-3-1-4h-1l-1-1c2 0 3 0 4-1-5-2-10-4-14-8l-3-6 1-1v-5l1-1c1-2 1-2 3-3z" class="e"></path><path d="M494 364h1 1l2-1c-1 2-2 5-3 7-2 0-3 1-5 3h-1c2-3 4-6 5-9z" class="x"></path><path d="M483 366l1-1c0 1 1 2 0 3s-2 2-3 2c0 1 0 2 1 3 0 1 0 1-1 1l-3 3h-2c1-1 1-2 1-3l1-1c1-2 1-3 3-4 1-1 1-2 2-3h0z" class="a"></path><path d="M455 351l1-1v-5l1-1c0 2 0 4 1 6h0v1c1 1 4 2 4 4h-1c-1 0-2 1-3 2l-3-6z" class="D"></path><path d="M460 341v-1l3 2v2c0 1 0 2 1 3 0 1-1 1-2 2l4 4c-2 0-5-3-7-4l-1 1h0c-1-2-1-4-1-6 1-2 1-2 3-3z" class="W"></path><path d="M460 345l3-1c0 1 0 2 1 3 0 1-1 1-2 2-1-1-1-2-2-4z" class="G"></path><path d="M460 341v-1l3 2v2l-3 1v-4z" class="X"></path><path d="M472 365c3 0 5 1 8 0l3-1 1 1-1 1h0c-1 1-1 2-2 3-2 1-2 2-3 4l-1 1c0 1 0 2-1 3l-1 2-2-1 1-1-2-3c1-1 0-1 1-2h1l-1-2h-2v1c-1-1-1-3-1-4h-1l-1-1c2 0 3 0 4-1z" class="f"></path><path d="M472 365c3 0 5 1 8 0l3-1 1 1-1 1c-3 1-5 1-9 2-1 0-3 0-4-1h-1l-1-1c2 0 3 0 4-1z" class="P"></path><path d="M481 301l1-1c0 1 0 2 1 3v1c-1 1-1 1-1 3h2v1l1 1c1 1 1 2 1 3l1 1 2-2v-1c1 1 1 1 2 1l5-6c1 1 2 1 3 1h2s2 1 2 2l2-1h0l1 1 3-1c1 2 1 4 0 6l1-1v3 19 8 11 2c0 1-1 1-2 1s-1 0-2 2l1 2h-5c-1 0-4 0-5 1s-2 1-2 3h-1-1l-2 2c0 1 0 2-1 2h0c-1-2 0-4 1-6h0v-2c0-1 1-1 1-2v-3l1-1c-2 2-3 5-5 6l-1-1 2-5v-1c2-3 2-4 2-7 0-2-1-3-1-5l-3-6v-1l1-1c-1-1-2-1-2-3h2-1c-1-1-1-2-2-3v-3h-1c-3-1-7-1-10-1l5-1c0-1 0-1 1-2v-1c2-3 3-9 2-12-1-2-1-4-1-6z" class="l"></path><path d="M506 342c1 1 2 1 3 1v4h-1 0c-1-1-3-3-3-4l1-1z" class="T"></path><path d="M505 350h5v-8 11h0l-6 1h-1l2-4z" class="e"></path><path d="M506 318l1-1h2v5c-1 3 0 8 0 12v3c-1 0-2 1-2 1l-1-1v-2c1-1 1-2 2-3v-4c-1-1-1-1-1-2v-1-2c0-2 0-3-1-5z" class="T"></path><path d="M497 335c2 0 3 0 4 1 1 0 1 1 2 2l1 1v-1c0-1 0 0-1-1l1-1 2-1v2l1 1s0 1 1 1v-1h1v3c-2 1-4-1-5 0 0 1 1 1 1 1h1l-1 1c0 1 2 3 3 4 0 0-1 1-2 1-1 1-1 1-2 1l-1-1h0c-2 1-3 1-4 1h-1v-7-2h1c-1-1-1-2-1-3l-1-2z" class="U"></path><path d="M498 342c1 2 1 2 3 3h1l1-1c1 0 1 1 2 2h0c1 1 1 2 1 2-1 1-1 1-2 1l-1-1h0c-2 1-3 1-4 1h-1v-7z" class="C"></path><path d="M503 315h1 2c1 1 1 0 1 1 1 0 1 1 2 1h-2l-1 1c1 2 1 3 1 5v2 1c0 1 0 1 1 2v4c-1 1-1 2-2 3l-2 1-1 1c1 1 1 0 1 1v1l-1-1c-1-1-1-2-2-2-1-1-2-1-4-1-1-4-3-6-6-8l2-2c3-4 6-8 10-10z" class="r"></path><path d="M499 329l-1 3h0l-3-3c0-2 1-3 1-4 1-2 3-2 4-3 0 1-1 2-1 3-1 1-1 2-1 4h0 1z" class="f"></path><path d="M502 328h2c2 1 2 1 2 3-1 1-1 2-2 3h-1c-2-1-2-2-3-3 1-2 1-2 2-3z" class="N"></path><path d="M506 318c1 2 1 3 1 5v2h-3c-2 1-3 2-5 3v1h-1 0c0-2 0-3 1-4 0-1 1-2 1-3l3-3 3-1z" class="D"></path><path d="M504 325v-1l1-1h2v2h-3z" class="e"></path><path d="M481 301l1-1c0 1 0 2 1 3v1c-1 1-1 1-1 3h2v1l1 1c1 1 1 2 1 3l1 1 2-2v-1c1 1 1 1 2 1l5-6c1 1 2 1 3 1h2s2 1 2 2l2-1h0l1 1 3-1c1 2 1 4 0 6l1-1v3c0 2 0 5-1 7v-5c-1 0-1-1-2-1 0-1 0 0-1-1h-2-1c-4 2-7 6-10 10l-2 2-3-1c0-1-2-1-3-2h-1c-3-1-7-1-10-1l5-1c0-1 0-1 1-2v-1c2-3 3-9 2-12-1-2-1-4-1-6z" class="q"></path><path d="M486 322l-1 1-4-2v-1l1-1c0 2 0 2 1 3l2-3 1 1v2z" class="H"></path><path d="M484 308l1 1c1 1 1 2 1 3l1 1 2-2v-1c1 1 1 1 2 1-1 3-3 5-5 7l1 3-1 1v-2l-1-1-2 3c-1-1-1-1-1-3l2-2c1-3-1-6 0-9z" class="O"></path><path d="M496 305c1 1 2 1 3 1h2s2 1 2 2l-6 5h-1c-3 2-6 5-9 8l-1-3c2-2 4-4 5-7l5-6zm10 3l3-1c1 2 1 4 0 6l1-1v3c0 2 0 5-1 7v-5c-1 0-1-1-2-1 0-1 0 0-1-1h-2-1c-4 2-7 6-10 10h-2c-1-1-2 0-3-1 0-3 2-4 4-6l3-3 1-1 6-4 1-1 2-1h1z" class="W"></path><path d="M505 308l2 3v1h-2l-2-3 2-1z" class="e"></path><path d="M502 310l1-1 2 3c-1 1-2 1-3 1v-3z" class="g"></path><path d="M495 315l2 2-3 3h-1l-1-2 3-3z" class="e"></path><path d="M509 313l1-1v3c0 2 0 5-1 7v-5c-1 0-1-1-2-1 0-1 0 0-1-1h-2-1c2-1 4-2 6-2z" class="K"></path><path d="M496 314l6-4v3c0 1-1 2-1 3h-3l-1 1-2-2 1-1z" class="e"></path><path d="M495 315l1-1 2 2-1 1-2-2z" class="g"></path><path d="M485 324c1 1 3 1 3 2l3 1c3 2 5 4 6 8l1 2c0 1 0 2 1 3h-1v2 7 2h4s2 0 3-1l-2 4h1l6-1h0v2c0 1-1 1-2 1s-1 0-2 2l1 2h-5c-1 0-4 0-5 1s-2 1-2 3h-1-1l-2 2c0 1 0 2-1 2h0c-1-2 0-4 1-6h0v-2c0-1 1-1 1-2v-3l1-1c-2 2-3 5-5 6l-1-1 2-5v-1c2-3 2-4 2-7 0-2-1-3-1-5l-3-6v-1l1-1c-1-1-2-1-2-3h2-1c-1-1-1-2-2-3v-3z" class="o"></path><path d="M502 351s2 0 3-1l-2 4h1l6-1h0v2c0 1-1 1-2 1s-1 0-2 2l1 2h-5c1-1 2-2 2-3v-1h-1c-2 0-3 0-4-1h0l3-3v-1z" class="g"></path><path d="M503 356h0 5c-1 0-1 0-2 2l1 2h-5c1-1 2-2 2-3v-1h-1zm-18-32c1 1 3 1 3 2l3 1c3 2 5 4 6 8l1 2c-2 0-3-1-4-1v-2c-1-2-1-2-2-2h-1c-1 0-2-1-3-2h-1c-1-1-1-2-2-3v-3z" class="e"></path><path d="M485 324c1 1 3 1 3 2s0 3-1 4c-1-1-1-2-2-3v-3z" class="o"></path><path d="M509 502h0c1 3 2 5 1 7h1c1 5 0 12 0 16v1l-1 4v11c-1-3 0-6 0-8h-1v2h-1v3h-1v7 1c0 3 0 4 2 6l-1 5-1 2 1 2h-1 0v4 1 2 2c-1 2 0 4-1 6 0 1 1 2 1 3l2 1c0 1 0 2-1 3l1 1c1 2 0 9 0 12v3l-1 1c1 2 1 3 1 5v4h-3 0c-3 0-8-4-10-5l1 1c1 2 3 3 5 4-1 0-2 0-3-1-2-1-3-2-5-2-3-2-5-4-8-7l-3-4h-1c-1 1-1 2-2 2l-1 1c0-1 0-2-1-3-1 0-1-1-1-2-1-1-2-1-3-2l-1 1h-3l1 1h1 0l2 3c-1 1-1 2-3 3h0c0 2 0 3-1 4-1 2-2 3-2 5h-1c-1 0-2 1-3 1v3c1 0 0 1 0 2h-1c-1 1-2 2-2 3v6c-1 1-1 2-1 2v1-4c-2-2-1-2-1-4l-1-4c-2-7-1-15-1-23l-2-1v-5h-1v-20l-1-1 1-1v-14c0-2 0-4 1-6v-5c1-7 8-13 12-19 1 0 2-1 3-1 2-2 4-7 6-8h1c3 0 5-3 8-3 1 0 2-1 3-1h1l1 1h1 2v1c2 1 3 1 5 1l-2-2h0c2-1 2-1 4-1 1-1 2-1 3-2h1c1 0 3-1 5-2z" class="e"></path><path d="M480 569h3c0 3 1 3 0 6h-1c0-1-1-1-2-2 0-2-1-2 0-4z" class="o"></path><path d="M480 555c1 2 2 4 2 6h-2c0 1 1 2 2 4h0c-1 1-1 2-1 3h-1l-3-9c1 0 2-1 2-2h1v-2z" class="L"></path><path d="M482 565h0c-1-2-2-3-2-4h2c2 1 3 3 4 5 1 0 1 0 2-1 1 1 2 2 2 4l-1 1h-1c-2-2-2-2-4-3-1 0-2-2-2-2z" class="k"></path><path d="M496 604c-8-7-14-16-17-26h0l8 13 3 1h1l1-1 3 4 2 1 7 5 4 2v1l1 1v4h-3 0c-3 0-8-4-10-5z" class="i"></path><path d="M505 602c1 1 1 1 1 3l-2 2c-1 0-4-1-5-2l2-2c1 0 1 0 2 1h1l1-2z" class="B"></path><path d="M495 595l2 1 7 5 1 1-1 2h-1c-1-1-1-1-2-1l-2 2-5-4c1 0 3 1 5 0l-4-4v-2z" class="D"></path><path d="M492 591l3 4v2l4 4c-2 1-4 0-5 0-3-3-6-6-7-10l3 1h1l1-1z" class="W"></path><path d="M460 535c1 1 1 2 3 2l-2 3c1 0 2-1 3-1h1c0-1 1-2 1-3h4c0 1 0 2 1 2v1c0 4-1 9-1 13s0 7 1 11c1 12 5 22 12 32h-1c-1 0-1-1-2-1v-1c-3-3-5-7-6-11h-1c-3-6-4-15-4-21l-1-4c0-3 0-7 1-10v-6c-1-1-2 0-2-2-1 0-2 0-3 1v1l-2 2h0l-1 1-2 4h-1v3l-1 1v-1-2h-3c0-2 0-4 1-6 1-3 3-6 5-8z" class="T"></path><path d="M460 535c1 1 1 2 3 2l-2 3c-2 3-3 6-4 9h-3c0-2 0-4 1-6 1-3 3-6 5-8z" class="P"></path><path d="M462 543h0l2-2v-1c1-1 2-1 3-1 0 2 1 1 2 2v6c-1 3-1 7-1 10l1 4h-1v-3l-1-1c-3 2-5 4-7 5v1c-1 0-2 1-2 2h0c-1-2-1-3 0-4-1-3-1-6-1-9l1-1v-3h1l2-4 1-1z" class="D"></path><path d="M462 543c2 1 2 2 3 4l-2 1c-1-1-1-3-2-4l1-1z" class="O"></path><path d="M457 552l1-1v-3h1v1c0 1 0 2-1 3v5l1 1c1-1 1-1 1-2l1-1c0-1 0-2 1-2 1-1 1-1 2-1l-2 4c0 1-1 2-2 3v3 1c-1 0-2 1-2 2h0c-1-2-1-3 0-4-1-3-1-6-1-9z" class="B"></path><path d="M464 552h1l2-2c0-1 0-2 1-2 0 1 0 1-1 2h1c0 2-1 5 0 7l1 4h-1v-3l-1-1c-3 2-5 4-7 5v-3c1-1 2-2 2-3l2-4z" class="O"></path><path d="M485 514c3-1 5-2 9-2l5 5v1 1 2h-1l-1-1h-3v-2l-1-1c-3 0-4 1-6 3-4 4-6 7-7 13h0c-1 3 0 5 0 7v14 1 2h-1c0 1-1 2-2 2-2-10-2-22 1-33l1-3-1-1h0l-1-1c1-1 2-2 2-3l1-1 5-3z" class="Q"></path><path d="M485 514c3-1 5-2 9-2-1 2-1 2-2 3l-8 3c-2 1-4 3-6 4l-1-1c1-1 2-2 2-3l1-1 5-3z" class="G"></path><path d="M494 512l5 5v1 1 2h-1l-1-1h-3v-2l-1-1c-3 0-4 1-6 3-4 4-6 7-7 13h0c-1 3 0 5 0 7v14c-3-4 1-11-1-14-1-2 0-2 0-3 0-3 1-6 0-8 0-2 0-2 2-3v-1-1l1-1c0-1 1-2 2-2 0-1 1-2 1-2l4-2c1-1 2-1 3-2h0c1-1 1-1 2-3z" class="V"></path><path d="M494 512l5 5v1 1h-1c-1-1-1-1-1-2h-1-1c-1-1-2-1-3-2h0c1-1 1-1 2-3z" class="E"></path><path d="M485 507c1 0 2-1 3-1h1l1 1h1 2v1c2 1 3 1 5 1l2 1 1 3-1 1c0 1 0 2-1 3l-5-5c-4 0-6 1-9 2l-5 3-1 1c0 1-1 2-2 3l1 1h0l1 1-1 3-2-1c-2 2-4 11-5 14v-1c-1 0-1-1-1-2h-4c0 1-1 2-1 3h-1c-1 0-2 1-3 1l2-3c-2 0-2-1-3-2-2 2-4 5-5 8v-5c1-7 8-13 12-19 1 0 2-1 3-1 2-2 4-7 6-8h1c3 0 5-3 8-3z" class="W"></path><path d="M476 510h1c3 0 5-3 8-3l-2 2-3 2c-2 1-7 7-10 7 2-2 4-7 6-8z" class="e"></path><path d="M493 508c2 1 3 1 5 1l2 1 1 3-1 1c0 1 0 2-1 3l-5-5c-4 0-6 1-9 2l-1-1 1-1c2-2 5-3 7-3l1-1z" class="v"></path><path d="M493 508c2 1 3 1 5 1l2 1 1 3-1 1c-2-2-5-2-7-4l-1-1 1-1z" class="o"></path><path d="M480 517l-1 1c0 1-1 2-2 3l1 1h0l1 1-1 3-2-1c-2 2-4 11-5 14v-1c-1 0-1-1-1-2h-4c0 1-1 2-1 3h-1c-1 0-2 1-3 1l2-3c-2 0-2-1-3-2 5-7 12-13 20-18z" class="E"></path><path d="M478 522l1 1-1 3-2-1c-2 2-4 11-5 14v-1c-1 0-1-1-1-2h-4c0 1-1 2-1 3h-1c-1 0-2 1-3 1l2-3h0c1-1 4-3 4-4 1-1 0-1 1-2 0 0 1-1 2-1h2 0l-1-2c1-1 1 0 2-1 0 0 1 0 1-1 1-1 3-2 4-4z" class="M"></path><path d="M505 558c1 0 1 1 2 1l1 2h-1 0v4 1 2 2c-1 2 0 4-1 6 0 1 1 2 1 3l2 1c0 1 0 2-1 3l1 1c1 2 0 9 0 12v3l-1 1c1 2 1 3 1 5l-1-1v-1l-4-2-7-5-2-1-3-4-2-2c-2-1-3-4-4-6s-2-4-2-6c2-2 2-2 5-3-1 0-2-1-2-1v-2l1-1h1l1-1c0-2-1-3-2-4l-1-2v-3c1 1 2 2 3 2s1 0 2 1l2 1 1-1 2 2v-1h1l-1 3 1 1h1l-1-2c0-1 0-1 1-1 2 0 2 0 3 1l2-2v-2l2-1c1 0 0 1 1-1-1 0-1-1-2-1v-1z" class="a"></path><path d="M494 571c-1-1-1-1-1-2h1 1c0 2 1 2 2 3v1 1c-1 1-2 2-2 3l1 1v2c-1 1-1 2-1 3h-1v1l-2-1v-1h1c2-2 1-8 1-11z" class="D"></path><path d="M489 574c2 0 3-1 5-3 0 3 1 9-1 11h-1v1l2 1c0 1 0 2-1 3l1 1v1c-1 0-2 0-3-1l-1 1c-2-1-3-4-4-6s-2-4-2-6c2-2 2-2 5-3z" class="o"></path><path d="M486 583c1 0 2 0 2 1 2 1 3 2 5 3l1 1v1c-1 0-2 0-3-1l-1 1c-2-1-3-4-4-6z" class="a"></path><path d="M495 583v2c0 2 0 3 1 5l2-1s0 1 1 1v1c0-1 1-1 1-1s1 1 2 1h2v-1c1 1 1 1 2 1 0-2-2-3-1-6h0c0-2 0-2 1-2l-1-2v-1-3-3-5c-1-1-1-1-2-1v-2c1 0 1-1 2-2s1-2 2-3v4 1 2 2c-1 2 0 4-1 6 0 1 1 2 1 3v1 3 1c-1 1-1 2-1 3v1l1-1 1 1c-1 2-1 3-2 5l-1-1h-1c-2 0-2 0-3-1-1 1-1 3-1 4v1h-1l-2-1v1l-2-1-3-4-2-2 1-1c1 1 2 1 3 1v-1l-1-1c1-1 1-2 1-3v-1h1z" class="B"></path><path d="M507 579l2 1c0 1 0 2-1 3l1 1c1 2 0 9 0 12v3l-1 1c1 2 1 3 1 5l-1-1v-1l-4-2-7-5v-1l2 1h1v-1c0-1 0-3 1-4 1 1 1 1 3 1h1l1 1c1-2 1-3 2-5l-1-1-1 1v-1c0-1 0-2 1-3v-1-3-1z" class="M"></path><path d="M500 596v-1c0-1 0-3 1-4 1 1 1 1 3 1 0 2 0 3 1 5 1 1 2 1 2 3h0c-2 0-2 1-3 0s-1-2-1-3h0l-2 1h0v-1-2l-1 1z" class="C"></path><path d="M457 549v2 1c0 3 0 6 1 9-1 1-1 2 0 4h0c0-1 1-2 2-2v-1c2-1 4-3 7-5l1 1v3h1c0 6 1 15 4 21h1c1 4 3 8 6 11v1c1 0 1 1 2 1-1 1-1 2-2 2l-1 1c0-1 0-2-1-3-1 0-1-1-1-2-1-1-2-1-3-2l-1 1h-3l1 1h1 0l2 3c-1 1-1 2-3 3h0c0 2 0 3-1 4-1 2-2 3-2 5h-1c-1 0-2 1-3 1v3c1 0 0 1 0 2h-1c-1 1-2 2-2 3v6c-1 1-1 2-1 2v1-4c-2-2-1-2-1-4l-1-4c-2-7-1-15-1-23l-2-1v-5h-1v-20l-1-1 1-1v-14h3z" class="e"></path><path d="M465 577l1-2c1 1 2 1 2 2-1 1-1 1-2 0h-1z" class="W"></path><path d="M465 577h1c-1 1-2 1-2 2-1 1 0 1-1 3-1-1-1-1-1-3l3-2z" class="o"></path><path d="M459 568c2 0 3-1 4 0-1 1-2 2-2 3s1 2 2 3v1h-2v-2h-1c-1 2-1 3-1 5v-9-1z" class="a"></path><g class="N"><path d="M460 562c2-1 4-3 7-5l1 1v3h1c0 6 1 15 4 21h1c1 4 3 8 6 11v1c1 0 1 1 2 1-1 1-1 2-2 2l-1 1c0-1 0-2-1-3-1 0-1-1-1-2-1-1-2-1-3-2v-2-1c-1-1-1-2-1-4v-1c-1 0-1-1-2-1-1 1-1 1-1 2 0 0 0 1-1 1v1 2c-1 0-2 1-2 1l-2-1c-1-1 0-2 0-4 0 0 1 0 2-1h0c2-1 3-1 4-3-1-1 0-2-1-3s-1-2-1-3h0c0-1-1-2-2-3l-1-1c1-1 1-2 1-3l-1-1c-1-1-1-2-1-3l-1 1v4h-1c-1-1-2 0-4 0 1-2 1-3 1-5v-1z"></path><path d="M457 549v2 1c0 3 0 6 1 9-1 1-1 2 0 4h0c0-1 1-2 2-2 0 2 0 3-1 5v1 9c1 1 1 3 0 4v1l1 1 2-1 1 1-2 2c0 2 3 3 3 5-2 0-2-1-4 0 1 1 1 2 2 3l-2-1v1c1 1 1 2 1 3 2 2 3 1 5 2 1-1 2-2 3-2l1-1v1c0 1 0 3-1 4l1 2c-1 2-2 3-2 5h-1c-1 0-2 1-3 1v3c1 0 0 1 0 2h-1c-1 1-2 2-2 3v6c-1 1-1 2-1 2v1-4c-2-2-1-2-1-4l-1-4c-2-7-1-15-1-23l-2-1v-5h-1v-20l-1-1 1-1v-14h3z"></path></g><path d="M458 565c0-1 1-2 2-2 0 2 0 3-1 5v1 9c1 1 1 3 0 4v1c-1 2 0 4 0 6l1 1-1 1c0 1 0 3 1 5l-1 2v2 1 1c0 3 0 6 1 10 0 3-1 7 0 10-2-2-1-2-1-4l-1-4c-2-7-1-15-1-23v-5-7c0-5 0-9 1-14z" class="D"></path><path d="M457 549v2 1c0 3 0 6 1 9-1 1-1 2 0 4h0c-1 5-1 9-1 14v7 5l-2-1v-5h-1v-20l-1-1 1-1v-14h3z" class="R"></path><path d="M454 565c1 0 1 0 2-1v6c0 1 0 2-1 3v7 5h-1v-20z" class="I"></path><path d="M457 549v2c-1 1-1 1-1 2 1 2 0 4 0 7 0 1 0 1-1 2h1v2c-1 1-1 1-2 1l-1-1 1-1v-14h3z" class="d"></path><path d="M509 502h0c1 3 2 5 1 7h1c1 5 0 12 0 16v1l-1 4v11c-1-3 0-6 0-8h-1v2h-1v3h-1v7 1c0 3 0 4 2 6l-1 5-1 2c-1 0-1-1-2-1v1c1 0 1 1 2 1-1 2 0 1-1 1l-2 1v2l-2 2c-1-1-1-1-3-1-1 0-1 0-1 1l1 2h-1l-1-1 1-3h-1v1l-2-2-1 1-2-1c-1-1-1-1-2-1s-2-1-3-2v3l1 2c-1 1-1 1-2 1-1-2-2-4-4-5 0-2-1-4-2-6v-1-14c0-2-1-4 0-7h0c1-6 3-9 7-13 2-2 3-3 6-3l1 1v2h3l1 1h1v-2-1-1c1-1 1-2 1-3l1-1-1-3-2-1-2-2h0c2-1 2-1 4-1 1-1 2-1 3-2h1c1 0 3-1 5-2z" class="N"></path><path d="M486 525h0c1 1 1 1 1 2-1 0-1 0-2-1l1-1z" class="g"></path><path d="M483 541h0c2 1 2 1 4 1l1-2 1 1v2c-1 1-1 0-2 0h-1-1l-2-2z" class="O"></path><path d="M487 532h1s1 1 2 1h1l1 3-1 1h-1c-1-2-1-2-3-3v-2z" class="g"></path><path d="M480 533c0 2 1 5 1 7 1 2 0 3 1 4l2-1v1c1 1 1 1 2 3l-1 2c-1-1-2-3-3-2v6c-1 2 1 6 2 7l1 2h1l1 1 1 2c-1 1-1 1-2 1-1-2-2-4-4-5 0-2-1-4-2-6v-1-14c0-2-1-4 0-7z" class="F"></path><path d="M506 555c0-1-1-1-2-2l-1-1c-1-1 0 0-1 1h-3c-2-2-3-2-3-4 0-1 1-3 2-4s2-2 4-2c1 1 1 1 3 1 0 0 1 1 2 1h0v1c0 3 0 4 2 6l-1 5-1 2c-1 0-1-1-2-1l1-3z" class="O"></path><path d="M499 546c1-1 2-1 4 0v2c-1 0-2 0-3-1l-1-1z" class="e"></path><path d="M499 546l1 1c1 1 2 1 3 1l-2 3c-1 0-2 1-3 0s-1-2-1-3l2-2z" class="o"></path><path d="M507 546c0 3 0 4 2 6l-1 5-1 2c-1 0-1-1-2-1l1-3v-3c1-2 1-4 1-6z" class="H"></path><path d="M509 502h0c1 3 2 5 1 7h1c1 5 0 12 0 16v1l-1 4v11c-1-3 0-6 0-8h-1v2h-1v3h-1c0-3-2-2-3-4h-2 0c-1 0-1 0-2 1s-2 1-4 1h0l-2-1c1-1 1-1 1-3 0-1-1-1-1-2s1-2 1-2c0-2 0-2-2-3h0-2c0-2 2-1 1-3-1 0-2-1-4-1l-2 2-2 2v1c0 1 0 1-1 2-1 2-2 5-2 7 0 1 1 4 2 6l2 2h1 1c0 2 0 2-1 4-1-2-1-2-2-3v-1l-2 1c-1-1 0-2-1-4 0-2-1-5-1-7h0c1-6 3-9 7-13 2-2 3-3 6-3l1 1v2h3l1 1h1v-2-1-1c1-1 1-2 1-3l1-1-1-3-2-1-2-2h0c2-1 2-1 4-1 1-1 2-1 3-2h1c1 0 3-1 5-2z" class="B"></path><path d="M508 530c0 1 1 2 1 3v2h-1l-1-1c0-2 0-2 1-4z" class="M"></path><path d="M487 520c2-2 3-3 6-3l1 1v2h3l1 1h1c0 2 0 2-1 3h-1c-2-1-3-1-3-3l-1-1v-1h0l-6 1z" class="U"></path><path d="M503 514c1 2 1 3 3 3 1 1 1 1 1 3l-1 1-1 1c1 1 1 2 1 3-2 3-4 5-7 8h-2 0l2-4c2-3 3-5 4-9v-6z" class="K"></path><path d="M506 521c2 1 2 1 4 1v4 4 11c-1-3 0-6 0-8h-1c0-1-1-2-1-3l-1-1c-1 0-1 1-2 1l-1 1c-1 0-1 1-2 1l-3 1c3-3 5-5 7-8 0-1 0-2-1-3l1-1z" class="S"></path><path d="M506 521c2 1 2 1 4 1v4h-1c-1-1-2-1-3-1 0-1 0-2-1-3l1-1z" class="k"></path><path d="M509 502h0c1 3 2 5 1 7h1c1 5 0 12 0 16v1l-1 4v-4-4c-2 0-2 0-4-1l1-1c0-2 0-2-1-3-2 0-2-1-3-3v6c-1 1-2 2-3 2 0-1 0-2-1-3v-1-1c1-1 1-2 1-3l1-1-1-3-2-1-2-2h0c2-1 2-1 4-1 1-1 2-1 3-2h1c1 0 3-1 5-2z" class="N"></path><path d="M500 510c1-1 1-1 3 0l1 1c-1 1-1 1-2 1l-1 1-1-3z" class="g"></path><path d="M503 514l1-1c1-1 1 0 2 0 1 1 2 1 2 2l-2 2c-2 0-2-1-3-3z" class="E"></path><path d="M506 517l2-2c1 3 2 4 2 7-2 0-2 0-4-1l1-1c0-2 0-2-1-3z" class="Y"></path><path d="M509 502h0c1 3 2 5 1 7h1l-1 7c-1-1-2-2-3-4-1-1 0-2-1-3l-6-3c1-1 2-1 3-2h1c1 0 3-1 5-2z" class="F"></path><path d="M509 502h0c1 3 2 5 1 7-1 0-3-3-5-4l-1-1c1 0 3-1 5-2z" class="R"></path><path d="M523 508l2-1c4-4 8-3 13-2h2l4 2c2 0 3 1 5 2 4 3 8 5 12 9 1 2 2 3 3 4 3 2 5 5 6 8l3 3 2 1v-1l1 1c1 0 2 1 2 2l2-1v3l1 5 1 2c0-1 0-2 1-4v13 4l-1-1h0c-1 0-2 1-3 1l1 1 1 1v11 2 8c0 6 0 11-1 17l1 1c-1 1-1 3-1 4v9h-1c-1-1-2-1-3-1v1c-2 0-2 0-3-1l-2 1-1-1-2-2-4-4-3-3c1-2 1-2 1-4l-3-2h0c-1 0-2-1-2-2l-1 2-1-2h-1s-1 0-2 1h0c-8 9-16 15-27 19l-6 3h-1-1c-1-5-1-10-1-14v-29l1-20-1-14v-10-4-9c1 1 0 2 2 3v-3h1l1 1c1 1 1 1 1 3l2 2v-3-6l-1-2 1-4z" class="s"></path><path d="M526 517c0-1 1-2 3-3v2c-1 1-2 1-3 1z" class="J"></path><path d="M537 515v3l-2 2-1 1-2-1v1h-1v-1c-1-1-1-2 0-3h1c2-1 3-1 5-2z" class="u"></path><path d="M531 521v-1c-1-1-1-2 0-3h1l2 2-1 1h-1v1h-1z" class="J"></path><path d="M529 546l-1-1c-1 0-1 0-2-1 0-1 1-2 1-4s-1-3-1-6h1v2l1 1h0c1-1 1-1 2-1-1 1-1 2-1 3h0c1 1 1 2 1 3v1l-1 5v-2z" class="X"></path><path d="M522 601c1-4 0-10 0-14 0-2 0-4 1-6 0 8 0 16 1 24l-1 1-2 2c1-3 1-5 1-7z" class="Z"></path><path d="M538 505h2l4 2v1l2 1v1c-1 0-2-1-4-1v1c-1-1-1-1-2-1-2 0-4 0-6 2l-2-1v-2c1 0 1 1 1 1 2-1 4 0 5-2v-2z" class="J"></path><path d="M523 514c1-1 1-1 2 0h1v-1c1-2 3-5 6-5v2l2 1-5 3c-2 1-3 2-3 3l-3 3v-6z" class="c"></path><path d="M531 521h1c-1 1-1 2-1 3 1 1 2 1 3 1l1-1h0c0 1 0 1-1 2h-1-2-1 0l-1 2 1 2h0v5 1c-1 0-1 0-2 1h0l-1-1v-2l1-1c-1-3-1-5 0-8 0-2 1-3 3-4z" class="u"></path><path d="M530 530v5l-2-2c0-1 1-1 2-3z" class="c"></path><path d="M530 530l2-2c1-1 1-1 2-1h3c-1 2-2 3-2 5v2l-1 1v1l-1 1-2-1v1c0 2 2 2 1 4l-2 1c0-1 0-2-1-3h0c0-1 0-2 1-3v-1-5h0z" class="p"></path><path d="M534 527h3c-1 2-2 3-2 5v2l-1 1h0-1c-1 0-2-1-2-2l1-1v-1l2-4h0z" class="Q"></path><path d="M530 542l2-1c1-2-1-2-1-4v-1l2 1 1-1c-1 2-1 2-1 4v1c0 2 1 2 3 3h0c0 1-1 1-1 2v1l1 1c0 2-1 1-1 2-1 1-1 2-2 3l-3-3-1-1v-1l1-5v-1z" class="h"></path><path d="M530 543h2l1 2h-1c0 1-1 1-1 3l1 1 3-2 1 1c0 2-1 1-1 2-1 1-1 2-2 3l-3-3-1-1v-1l1-5z" class="b"></path><path d="M541 519h1c-1 1-1 2-1 4-1 2-1 1-2 2-1 0-1 2-2 2h-3c-1 0-1 0-2 1l-2 2-1-2 1-2h0 1 2 1c1-1 1-1 1-2h0l-1 1c-1 0-2 0-3-1 0-1 0-2 1-3v-1l2 1 1-1h1 4l1-1z" class="J"></path><path d="M541 519h1c-1 1-1 2-1 4-1 2-1 1-2 2-1 0-1 2-2 2h-3c1 0 1-1 2-1v-2c1-1 0-2 0-4h4l1-1z" class="K"></path><path d="M523 508l2-1c4-4 8-3 13-2v2c-1 2-3 1-5 2 0 0 0-1-1-1-3 0-5 3-6 5v1h-1c-1-1-1-1-2 0l-1-2 1-4z" class="s"></path><path d="M528 577c-3-3-1-3-1-7-1-4-1-9-1-14 0-1 1-3 1-4-1-1-1-1-1-2 0-2 1-3 3-4v2 1l1 1v7c-1 1 0 2-1 3v3 5l-1 2 2 1-1 1h0c-1 2-1 3-1 5z" class="J"></path><path d="M529 549l1 1v7c-1 1 0 2-1 3v3 5l-1 2h-1c-1-4 1-9 1-14-1-1-1-2 0-3 0-2 1-2 1-4z" class="c"></path><path d="M529 572c0 1 0 2 1 3l-1 1 1 2c1 1 2 3 3 4h2 1l-2 2 1 4h1v4h1v2h-1-2c-1 2-1 2-1 4v1l-3 3v-2c0 1-1 2-3 3 1-1 1-2 2-3h0c0-1-1-2-1-3v-2c1-1 1-1 1-2v-1l1-1-2-2s1-2 1-3c1-1 1 0 2-1l-2-2h0v-1c0-2 0-2-2-3v-1l1-1c0-2 0-3 1-5z" class="X"></path><path d="M529 572c0 1 0 2 1 3l-1 1 1 2c1 1 2 3 3 4h-2c-1-1-1-1-2-3-1 0-1 0-2-1l1-1c0-2 0-3 1-5z" class="G"></path><path d="M533 584h1l1 4h1v4h1v2h-1-2c-1 2-1 2-1 4v1l-3 3v-2c2-2 2-2 2-4-1-1-1-1-1-2l1-2-1-1v-1l1-2c-1-1-1-1-1-2l2-2z" class="E"></path><path d="M534 594v-2c-1 0-1 0-2-1l1-2c0 1 0 2 1 2s1 0 2 1h1v2h-1-2z" class="d"></path><path d="M533 584h1l1 4h1v4c-1-1-1-1-2-1s-1-1-1-2v-5z" class="h"></path><path d="M516 517c1 1 0 2 2 3v-3h1l1 1c1 1 1 1 1 3l2 2v4 53h-1c0-1-1-11-1-13 0-3-1-7-1-11v-2h0-2c-1-1-1-2-1-3v3l-1-14v-10-4-9z" class="V"></path><path d="M518 529l1-1c1 1 1 2 2 4h0v10c-2-1-2-2-2-4l-3 2v-10l2-1z" class="K"></path><path d="M518 529l1-1c1 1 1 2 2 4h0c-1 1-1 2-1 3l-1-1-1-1c-1-1 0-3 0-4z" class="X"></path><path d="M516 540l3-2c0 2 0 3 2 4v6 1 2c0 2 0 4-1 5v-2h0-2c-1-1-1-2-1-3v3l-1-14z" class="k"></path><path d="M516 517c1 1 0 2 2 3v-3h1l1 1c1 1 1 1 1 3l2 2v4 1c-1 1-1 2-1 4h-1c-1-2-1-3-2-4l-1 1-2 1v-4-9z" class="S"></path><path d="M518 517h1l1 1c1 1 1 1 1 3 0 1 0 2-1 3l-1 2c-2-2-1-4-1-6v-3z" class="B"></path><defs><linearGradient id="m" x1="523.509" y1="578.693" x2="512.294" y2="580.514" xlink:href="#B"><stop offset="0" stop-color="#36372f"></stop><stop offset="1" stop-color="#59555b"></stop></linearGradient></defs><path fill="url(#m)" d="M517 554v-3c0 1 0 2 1 3h2 0v2c0 4 1 8 1 11 0 2 1 12 1 13h1v1c-1 2-1 4-1 6 0 4 1 10 0 14 0 2 0 4-1 7h-2c-1 1-1 1-1 2v5 2h-1c-1-5-1-10-1-14v-29l1-20z"></path><path d="M521 567c0 2 1 12 1 13h1v1c-1 2-1 4-1 6 0 4 1 10 0 14-1 1-1 2-2 4-1-3 0-5-1-7v-7-13h0l1 3h1v-1-4-9z" class="K"></path><path d="M538 545l2-1c1 0 2 0 3 1 2 2 4 2 6 3 1 1 1 1 3 1l1-2 1 1c1 1 0 1 1 1h0l3 2-2 2-2 2c-1 1-1 1-1 2-1 0-1 0-2-1s-2-1-3-1c-2 0-2 1-3 2h0c0 3 0 4 1 6h0l1 2c-1 0-1-1-2-1l-2 2 1 3v2c0 3 0 4-1 7h-1 0l-2 2-2 1v1h-1-1-1-2c-1-1-2-3-3-4l-1-2 1-1c-1-1-1-2-1-3h0l1-1-2-1 1-2v-5-3c1-1 0-2 1-3v-7l3 3c1-1 1-2 2-3 0-1 1 0 1-2l-1-1v-1c0-1 1-1 1-2l2 1z" class="n"></path><path d="M535 567h0l2 2-1 1h-2c1-1 1-1 1-2v-1zm-2-1h-1c-1-2-1-2-1-4l1-1 2 3s-1 1-1 2z" class="Q"></path><path d="M538 581l-1-1-1-1v-2c0-1 0-1 1-2 0 1 0 1 1 2 0 1 1 2 1 3h1l-2 1z" class="P"></path><path d="M530 578c2-1 3 0 4 0v1c0 1 0 1 1 1l2 2h-1-1-2c-1-1-2-3-3-4z" class="K"></path><path d="M529 563c1 2 1 3 1 5s1 2 1 3c-1 1-1 2-1 4 1 1 2 1 4 1h1v1l-1 1c-1 0-2-1-4 0l-1-2 1-1c-1-1-1-2-1-3h0l1-1-2-1 1-2v-5z" class="E"></path><path d="M534 564l1-1c1-1 2-1 3-2h2v-2-1l1 1c1 0 1 0 2 1h1l-4 4h-3c-1 1-2 2-3 2h-1c0-1 1-2 1-2z" class="k"></path><path d="M542 578c0-1-1-1-2-1 0-2 0-2-1-4h-2 0c1 0 1 0 2-1l-1-1h2 4c0 3 0 4-1 7h-1 0z" class="h"></path><path d="M532 557c0-1 2-2 2-2h2c0-1 0-2 1-3 1 1 1 1 2 3v1h1 2c0 1-1 1-1 2h-1v1 2h-2c-1 1-2 1-3 2l-1 1-2-3 2-2v-1h-1-1-1l1-1z" class="L"></path><path d="M538 545l2-1c1 0 2 0 3 1 2 2 4 2 6 3 1 1 1 1 3 1l1-2 1 1c1 1 0 1 1 1h0l3 2-2 2-2 2c-1 1-1 1-1 2-1 0-1 0-2-1s-2-1-3-1c-2 0-2 1-3 2h0-1c-1 1-2 1-3 1 0-1 1-1 1-2h-2-1v-1c-1-2-1-2-2-3-1 1-1 2-1 3h-2s-2 1-2 2c0-2 1-3 1-4 1-1 1-2 2-3 0-1 1 0 1-2l-1-1v-1c0-1 1-1 1-2l2 1z" class="I"></path><path d="M555 549h0l3 2-2 2-2 2c-1 1-1 1-1 2-1 0-1 0-2-1s-2-1-3-1c-2 0-2 1-3 2h0-1v-2c0-3 2-2 4-3 0 1 1 1 2 2l3-3 2-2z" class="P"></path><path d="M555 549h0l3 2-2 2c-1-1-2-1-3-2l2-2z" class="J"></path><path d="M538 545l2-1c1 0 2 0 3 1 2 2 4 2 6 3-1 1-1 1-1 2l-2-2-2 1c-1 1-2 2-4 3l-3-3c0-2 0-3 1-4z" class="E"></path><path d="M540 546l1-1 1 1c1 2 0 2-1 3-1 0-1 1-2 0 0-2 0-2 1-3z" class="i"></path><path d="M544 507c2 0 3 1 5 2 4 3 8 5 12 9 1 2 2 3 3 4 3 2 5 5 6 8h0l-6-3 2 6 1 7c-1 1-1 1-2 1l-1-1c1-1 1-1 1-2l-1-1-1 1-1-1v-1h-2l1 1c0 5 0 9-3 14l-3-2h0c-1 0 0 0-1-1l-1-1-1 2c-2 0-2 0-3-1-2-1-4-1-6-3-1-1-2-1-3-1l-2 1-2-1h0c-2-1-3-1-3-3v-1c0-2 0-2 1-4v-1l1-1v-2c0-2 1-3 2-5 1 0 1-2 2-2 1-1 1 0 2-2 0-2 0-3 1-4h-1l-1 1h-4-1l2-2v-3h1v-1c1-1 1-2 3-3 0 1 1 1 2 1s1-1 2-1h1v-1-1l-2-1v-1z" class="r"></path><path d="M553 525l1 1h2c0 1 0 2-1 4 0-1 0-1-1-2h0c-1-1-1-2-1-3zm0 12v-1l2 2h1l2 1c-1 2-1 3-1 4l-1-2v-1c-1 1-1 1-2 1-1-1 0-2-1-4z" class="n"></path><path d="M540 538c2 0 3 0 4 1v2h1l-1 1v2h-1c-1-1-2-1-2-2-1-2 0-2-1-3v-1z" class="S"></path><path d="M548 525l1 2h1c0-2 0-3 1-4h0c1 1 0 1 0 2l1 3v3l-2 1-1-1c-1-1-1-3-2-4l1-2z" class="V"></path><path d="M549 531h1l1-3h1v3l-2 1-1-1z" class="S"></path><path d="M547 541l3-3v4c1 1 1 1 3 1 0 2 0 1-1 2s-1 2-3 2h0c-2-1-3-1-4-3h-1v-2l1-1 1 1h0l1 1h1l-1-1v-1z" class="L"></path><path d="M547 541l3-3v4l-1 1 1 1v1c-2 0-2-1-4-1v-2h0l1 1h1l-1-1v-1z" class="V"></path><path d="M542 519l2 1c2-2 1-2 2-4h3c2 1 3 3 3 5h0l3 1h0c1 1 1 2 1 3v1h-2l-1-1h-1v-2l-1-1c-1-1-1-3-1-4l-1-1c0 1-1 1-1 3v2c-1-1-2-2-3-2-2 0-3 1-4 3 0-2 0-3 1-4z" class="Q"></path><path d="M555 522c1 1 1 2 1 3v1h-2l-1-1v-1l2-1v-1z" class="x"></path><path d="M560 536l1 1c0 5 0 9-3 14l-3-2c1-1 1-2 1-3-1 0-1-1-2-1v-1c1 0 1 0 2 1h1v-2c0-1 0-2 1-4l1-1 1-2z" class="P"></path><path d="M542 530v3c1 1 1 1 1 3-1 0-2 1-3 1v1 1h0c-1 0 0 1 0 2-3 0-2 2-4 2 0-1-1-2-2-4l3-3h0 2l1-1h1c0-1-2-3-3-4l1-2c1 0 2 0 3 1z" class="m"></path><path d="M542 530c0-2 0-3 1-4 1 0 1 0 2-1s1 0 3-1v1l-1 2c1 1 1 3 2 4l1 1 2-1 1 3-1 1v2l-2 1h0l-3 3v1l1 1h-1l-1-1h0l-1-1h-1v-2c-1-1-2-1-4-1v-1c1 0 2-1 3-1 0-2 0-2-1-3v-3z" class="M"></path><path d="M547 527c1 1 1 3 2 4l-1 1h-2-1c-1 0-2-1-2-2v-1h1l1 1h1c-1-1 0-1-1-2l2-1zm5 4l1 3-1 1v2l-2 1h0l-3 3c0-2-2-3-2-5 1-1 2-1 3-1l2-2v-1l2-1z" class="R"></path><path d="M552 531l1 3-1 1v2l-2 1v-2h-2v-1l2-2v-1l2-1z" class="Z"></path><path d="M552 531l1 3-1 1h-1l-1-2v-1l2-1z" class="t"></path><path d="M557 524l1 2 1-2h1l1 2h1l1-1 1 2 2 6 1 7c-1 1-1 1-2 1l-1-1c1-1 1-1 1-2l-1-1-1 1-1-1v-1h-2l-1 2-1 1-2-1h-1l-2-2v1h-1v-2l1-1 1-3h2c0-1 0-1-1-1 1-2 1-3 1-4v-1l1 1v-2z" class="K"></path><path d="M553 534l1-3h2-1c0 2 1 2 0 3l-2 2v1h-1v-2l1-1z" class="Q"></path><path d="M558 526l1-2h1l1 2h1c-1 2-1 3-2 4l-1-1-1 1c0 1 1 2 1 2l-2 3h1c0 1 0 2 1 3l-1 1-2-1 1-3c0-3 0-6 1-9z" class="M"></path><path d="M563 525l1 2 2 6c-2 0-2 0-4 1h0l-3-2s-1-1-1-2l1-1 1 1c1-1 1-2 2-4l1-1z" class="U"></path><path d="M559 532l3 2h0c2-1 2-1 4-1l1 7c-1 1-1 1-2 1l-1-1c1-1 1-1 1-2l-1-1-1 1-1-1v-1h-2l-1 2c-1-1-1-2-1-3h-1l2-3z" class="C"></path><path d="M544 507c2 0 3 1 5 2 4 3 8 5 12 9 1 2 2 3 3 4 3 2 5 5 6 8h0l-6-3-1-2-1 1h-1l-1-2h-1l-1 2-1-2v2l-1-1c0-1 0-2-1-3h0l-3-1h0c0-2-1-4-3-5h-3c-1 2 0 2-2 4l-2-1h-1l-1 1h-4-1l2-2v-3h1v-1c1-1 1-2 3-3 0 1 1 1 2 1s1-1 2-1h1v-1-1l-2-1v-1z" class="X"></path><path d="M556 518c2 2 5 4 7 7l-1 1h-1l-1-2h-1l-1 2-1-2c0-2-1-4-1-6z" class="S"></path><path d="M546 513c2 0 3 1 5 2 2 0 4 1 5 3 0 2 1 4 1 6v2l-1-1c0-1 0-2-1-3h0l-3-1h0c0-2-1-4-3-5-1 0-2-2-3-3z" class="K"></path><path d="M551 515c2 0 4 1 5 3 0 2 1 4 1 6v2l-1-1c0-1 0-2-1-3h0c-1-2-1-3-1-4v-1c-2 0-2-1-3-2h0z" class="G"></path><path d="M538 514l4-1h4c1 1 2 3 3 3h-3c-1 2 0 2-2 4l-2-1h-1l-1 1h-4-1l2-2v-3h1v-1z" class="h"></path><path d="M538 514l4-1c-1 2-2 2-3 4h-1c-1-1-1-1 0-2v-1z" class="K"></path><path d="M538 515c-1 1-1 1 0 2h1l2 2-1 1h-4-1l2-2v-3h1z" class="j"></path><path d="M564 527l6 3h0l3 3 2 1v-1l1 1c1 0 2 1 2 2l2-1v3l1 5 1 2c0-1 0-2 1-4v13 4l-1-1h0c-1 0-2 1-3 1l1 1 1 1v11 2 8c0 6 0 11-1 17l1 1c-1 1-1 3-1 4v9h-1c-1-1-2-1-3-1v1c-2 0-2 0-3-1l-2 1-1-1-2-2-4-4-3-3c1-2 1-2 1-4l-3-2h0c-1 0-2-1-2-2l-1 2-1-2h-1s-1 0-2 1h0c-8 9-16 15-27 19l-6 3h-1v-2-5c0-1 0-1 1-2h2l2-2 1-1h2l1-2c2-1 3-2 3-3v2l3-3v-1c0-2 0-2 1-4h2 1v-2h-1v-4h-1l-1-4 2-2h1 1v-1l2-1 2-2h0 1c1-3 1-4 1-7v-2l-1-3 2-2c1 0 1 1 2 1l-1-2h0c-1-2-1-3-1-6h0c1-1 1-2 3-2 1 0 2 0 3 1s1 1 2 1c0-1 0-1 1-2l2-2 2-2c3-5 3-9 3-14l-1-1h2v1l1 1 1-1 1 1c0 1 0 1-1 2l1 1c1 0 1 0 2-1l-1-7-2-6z" class="T"></path><path d="M521 608l2-2 1-1v4l5-1c-1 2-1 3-3 4l-3-1c-1 1-1 1-1 2 1 1 2 1 3 1l-6 3h-1v-2-5c0-1 0-1 1-2h2z" class="M"></path><path d="M521 608l2-2 1-1v4h-1c-1 1-1 1-1 2-2 0-2-1-4-1 0-1 0-1 1-2h2z" class="S"></path><path d="M556 578c1-2 2-5 3-6h1l-3 8c-4 7-8 13-13 19-1-1-1-1-1-2l1-2 2-2 2-3c-1-1-2-1-4-1l2-3 2 1 2-4c2-2 2-4 4-5h2z" class="d"></path><path d="M546 586l2 1 2-4c2-2 2-4 4-5h2l-8 12c-1-1-2-1-4-1l2-3z" class="i"></path><path d="M534 594h2c1 1 1 2 1 3v1l1 1 2 1 3-3c0 1 0 1 1 2-5 4-10 7-15 9l-5 1v-4h2l1-2c2-1 3-2 3-3v2l3-3v-1c0-2 0-2 1-4z" class="P"></path><path d="M533 599l1 2-2 2h1 1 1c-1 1-2 2-4 2l-1-3 3-3z" class="Y"></path><path d="M535 599l2-1 1 1 2 1c-2 1-3 2-5 3h-1c0-2 1-3 1-4z" class="I"></path><path d="M534 594h2c1 1 1 2 1 3v1l-2 1c0 1-1 2-1 4h-1-1l2-2-1-2v-1c0-2 0-2 1-4z" class="b"></path><path d="M534 594h2c1 1 1 2 1 3v1l-2 1v-2h0l-1-1v-2z" class="Y"></path><path d="M544 569l1 2v1l2 1 1-1 1-1h1v1 3l-1 1-3 6 1 1c-1 1-2 1-2 2l1 1-2 3c2 0 3 0 4 1l-2 3-2 2-1 2-3 3-2-1-1-1v-1c0-1 0-2-1-3h1v-2h-1v-4h-1l-1-4 2-2h1 1v-1l2-1 2-2h0 1c1-3 1-4 1-7v-2z" class="R"></path><path d="M546 593c-1-1-3-1-3-3l1-1c2 0 3 0 4 1l-2 3z" class="U"></path><path d="M538 582v1l1-1c1 0 2-1 3-2v1c0 1 0 3-2 4l-2 1v1h1v1c0 1-1 2-2 2l1 2c0 1 0 1-1 2v-2h-1v-4h-1l-1-4 2-2h1 1z" class="I"></path><path d="M538 586c2 1 3 1 3 2l-1 1c0 1 1 3 2 4 0 1-1 1 0 2h2 0l-1 2-3 3-2-1-1-1v-1c0-1 0-2-1-3h1c1-1 1-1 1-2l-1-2c1 0 2-1 2-2v-1h-1v-1z" class="Z"></path><path d="M538 592c1 1 1 1 1 3v2l-1 2-1-1v-1c0-1 0-2-1-3h1c1-1 1-1 1-2z" class="L"></path><path d="M544 569l1 2v1l2 1 1-1 1-1h1v1 3l-1 1-3 6 1 1c-1 1-2 1-2 2l-1 2-1-1v-1c1-1 1-2 2-3-1 0-1 0-2-1 1-1 2-1 2-3l-1-1-1 2-1-1h1c1-3 1-4 1-7v-2z" class="F"></path><path d="M549 571h1v1 3l-1 1c-1 1-2 1-3 2l1-1c1-2 1-2 1-4v-1l1-1z" class="q"></path><path d="M567 547v-1c1-2 1-3 1-5l1-1c0 2-1 4 0 6v11c0 1 0 1 1 3h1v1l-1 2c0 1 1 2 1 2l-1 2v2l-1 2 1 1-2 2c-1 3-2 4-2 7l-1 1c-2 1-2 3-3 4l-5 8-1 2-1-2h-1s-1 0-2 1h0c4-7 9-14 11-22 1-4 2-7 2-11l1-9 1-6z" class="u"></path><path d="M570 560h1v1l-1 2c0 1 1 2 1 2l-1 2v2l-1 2 1 1-2 2c-1 3-2 4-2 7l-1 1c0-3 2-6 2-9 1-3 0-5 1-7 0-1 1-2 0-3h0c0-2 1-2 2-3z" class="b"></path><path d="M564 527l6 3h0l3 3 2 1v-1l1 1c1 0 2 1 2 2l2-1v3l1 5 1 2c0-1 0-2 1-4v13 4l-1-1h0c-1 0-2 1-3 1l1 1c-1 0-2 1-3 1v2h-2 0l-2-2-1 1v4h-1s-1-1-1-2l1-2v-1h-1c-1-2-1-2-1-3v-11c-1-2 0-4 0-6l-1 1c0 2 0 3-1 5v1-7l-1-7-2-6z" class="Y"></path><path d="M569 546l3 2-2 2c1 0 1 1 1 2l-1 1c1 1 2 0 1 2l-2 2v-11z" class="b"></path><path d="M564 527l6 3h0l3 3c0 1-1 1-1 2h0l-2 1c0 1-1 2-2 3 1 0 1 1 2 1l-1 6c-1-2 0-4 0-6l-1 1c0 2 0 3-1 5v1-7l-1-7-2-6z" class="j"></path><path d="M570 536c0-1 0-1-1-1h-1c0-2 0-2 1-3h1c0 1 1 2 2 3h0l-2 1z" class="n"></path><path d="M573 533l2 1v-1l1 1c1 0 2 1 2 2l2-1v3l1 5 1 2c0-1 0-2 1-4v13 4l-1-1h0c-1 0-2 1-3 1h-1v-3h1c-1-2-1-2-1-4h1l-1-2c0-1-1-2-1-3v-2c0-1 0-2-1-3l-1 1-1-1c-1-1 0-2 0-3l-1-1-1 1h-2l1-1h2 0l-1-2h0c0-1 1-1 1-2z" class="p"></path><path d="M575 533l1 1c1 0 2 1 2 2l2-1v3l1 5c-2 0-4-7-6-9v-1z" class="i"></path><path d="M560 536h2v1l1 1 1-1 1 1c0 1 0 1-1 2l1 1c1 0 1 0 2-1v7l-1 6-1 9-2-1c-1 1-1 2-1 3l-1 2h-1 0c-1 1 0 1-1 2v-2h-1v1 2c-1 2-2 3-3 5-1 1-1 3-1 4-2 1-2 3-4 5l-2 4-2-1-1-1c0-1 1-1 2-2l-1-1 3-6 1-1v-3-1h-1l-1 1-1 1-2-1v-1l-1-2-1-3 2-2c1 0 1 1 2 1l-1-2h0c-1-2-1-3-1-6h0c1-1 1-2 3-2 1 0 2 0 3 1s1 1 2 1c0-1 0-1 1-2l2-2 2-2c3-5 3-9 3-14l-1-1z" class="B"></path><path d="M545 572v-2c1 0 2-1 3 0 0 0 0 1 1 1l-1 1-1 1-2-1z" class="O"></path><path d="M560 536h2v1l1 1 1-1 1 1c0 1 0 1-1 2l1 1c1 0 1 0 2-1v7l-1 6v-7h-2v-3h-1l-1 1h0v2c0 1 0 3-1 4l-1 1v1l-1 1h0c-2 1-3 2-5 2l2-2 2-2c3-5 3-9 3-14l-1-1z" class="F"></path><path d="M550 572h0c1-1 2-3 2-4 1-1 1-2 1-3s0-1-1-2l1-1-2-2 2-1v1l1 1 1 1 1-1c-1-1-1-1-1-2l3-3 1-1v2h0l1-1v1c1 1 0 4 1 5v1 2 1h-1 0c-1 1 0 1-1 2v-2h-1v1 2c-1 2-2 3-3 5-1 1-1 3-1 4-2 1-2 3-4 5l-2 4-2-1-1-1c0-1 1-1 2-2l-1-1 3-6 1-1v-3z" class="D"></path><path d="M580 559l1 1v11 2 8c0 6 0 11-1 17l1 1c-1 1-1 3-1 4v9h-1c-1-1-2-1-3-1v1c-2 0-2 0-3-1l-2 1-1-1-2-2-4-4-3-3c1-2 1-2 1-4l-3-2h0c-1 0-2-1-2-2l5-8c1-1 1-3 3-4l1-1c0-3 1-4 2-7l2-2-1-1 1-2v-2l1-2h1v-4l1-1 2 2h0 2v-2c1 0 2-1 3-1z" class="L"></path><path d="M568 587c2 0 3-1 4 0l-1 1c-1 0-2 1-3 0v-1z" class="Q"></path><path d="M573 590h2l-1 1c-1 1-1 2-3 2 0-1-1-1-1-2 1-1 2 0 3-1z" class="I"></path><path d="M566 588c1 0 1 0 1 1v1h0c1 1 1 2 2 3h-1l-2-2-2-1 1-1h1v-1z" class="Z"></path><path d="M569 593l1 2v2 2h-2v-1c1-1 1-1 0-2v-3h1z" class="V"></path><path d="M562 598v-2 1c1 1 0 2 2 3l1 1h1l-2 4-3-3c1-2 1-2 1-4z" class="E"></path><path d="M570 581h2c1 1 1 2 1 3h3c0 2 0 2-1 4h-1c-1-2-1-3-2-4-2-1-1 0-2-1v-2z" class="Q"></path><path d="M576 593h0l-1 1 1 2h-1c-1 1-2 2-2 4h0c-1 0-2-1-2-2l1-1c-1-1-1-1 0-2 1 0 2-2 4-2z" class="k"></path><path d="M580 573h1v8c-1 1-1 2-2 2h0l-1-1-2-2c1 0 2-1 2-2s-1-1-2-2c0-1 1-2 1-2 1-1 2 0 2 0l1-1z" class="h"></path><path d="M569 578v3c-1 1-3 0-3 2v1 1 3 1h-1l-1 1 2 1v1c-1 1-2 1-2 2h-1c0-1 0-1-1-2v-3l1-1v-2h2v-4l1-1h1c0-1 1-2 2-3z" class="r"></path><path d="M557 594l5-8c1-1 1-3 3-4v4h-2v2l-1 1v3c0 1-1 1-1 2s-1 2-2 2h0c-1 0-2-1-2-2z" class="b"></path><path d="M579 583c1 0 1-1 2-2 0 6 0 11-1 17h-1-1c-1-1 0-2 0-4l-2-1h0c1-2 1-2 0-4l1-1v1c1 1 2 0 3 0v-2c-1 1-1 1-2 1h-1c0-3 0-3 2-5z" class="Y"></path><path d="M580 559l1 1v11 2h-1l-1 1s-1-1-2 0c0 0-1 1-1 2h-1 0c-1 2-3 2-3 5h-2c0-2 1-4 0-5h-1v2c-1 1-2 2-2 3h-1c0-3 1-4 2-7l2-2-1-1 1-2v-2l1-2h1v-4l1-1 2 2h0 2v-2c1 0 2-1 3-1z" class="I"></path><path d="M580 573c0-1 0 0-1-1-1 0-2-1-2-2h1 1l1-2 1 3v2h-1z" class="n"></path><path d="M580 559l1 1v11l-1-3v-2c-1 0-1 1-2 2l-2-1c1-1 1-3 1-4v-1-2c1 0 2-1 3-1z" class="x"></path><path d="M576 593l2 1c0 2-1 3 0 4h1 1l1 1c-1 1-1 3-1 4v9h-1c-1-1-2-1-3-1v1c-2 0-2 0-3-1l-2 1-1-1-2-2-4-4 2-4h-1l2-1c1 0 2 2 3 2h2 1c2 0 2-1 3-2v-1l-3 1c0-2 1-3 2-4h1l-1-2 1-1z" class="Q"></path><path d="M570 611l2-2h1v2l-2 1-1-1z" class="K"></path><path d="M576 611l-1-1c1-1 1-2 2-3 2-1 3-2 3-4v9h-1c-1-1-2-1-3-1z" class="Y"></path><path d="M566 601c1 1 2 2 2 3 2 1 2 1 3 3-1 1-2 1-3 2l-4-4 2-4z" class="n"></path><path d="M499 230c1 1 3 4 6 4 1 0 2 1 3 1l3 1c2 0 4 0 5 1h2v1h2 1c1 0 1 0 2-1l2 3c1 0 2 0 3-1h2v1c0 1 0 1-1 2l1 2h0c1 1 2 1 2 3l-2 1 1 1h1v2 1c1 0 1 1 1 2l-1 1h1 2c1 0 2-1 3-1 3 1 4-2 7-1l-2 2v1h3c3-1 8-1 12-2 35-1 68 11 99 27 2 1 4 1 6 2h2c4 1 10 5 12 8h1c2 1 3 2 5 3l2 2c1 0 1 0 1-1-1-1-3-3-5-4s-5-3-8-5v-3c2 2 5 3 7 4l10 9v-1c-1-2-3-5-5-7l-3-3c0-2-1-4-1-5h-1c1 0 2 0 3 1h0l3 3c3 6 10 12 15 17v-1l1-1 2 3c1 0 1 0 2-1l2 3c1 1 2 4 3 4 2 1 2 2 3 3l2 2 1 1c1 0 2-1 3-2l5 7c2 3 5 6 8 9l-5 1 1 1h0c-1 0-1 0-2-1l-1 3-3-3-1 1 12 18 3 5c2 4 4 7 5 11v1 1c1 1 1 0 1 2 1 1 2 2 2 4h0l-2 2v2l-2 2 7 20c1 5 2 10 2 14v1 7l-1-1c-3-29-18-59-37-80-6-8-13-15-21-21l-18-12c-15-8-32-15-49-16h-4-4c-2 1-3 0-4 1h-1v1h-1l-2 1-1 1-2 1c0-1 0-2 1-3h-1c-1 1-1 2-2 2-2 1-3 1-4 3-1 1-3 1-4 2-1 2-1 3-3 4-1 1-2 1-2 2 0 2-1 3-2 5 0-2 0-3-1-4h0l-3 2v1l-1 2h0l1 2 2 1c0 1 0 1-1 2v3l-1 1 1 1v1c-2 1-3 1-4 2-1-1-1 0-2-1l-2 1-1-1h-1l-2 3 3 2 1 1 1 1c0 2-1 2-2 3 3 2 5 5 5 9 0 0-1 1-1 2v1 1l-2 2c-2 2-4 3-7 4v1l-6 1-3 1h-1-5l-1 2-1 1h1l1 2c-1 0-1 1-1 1h-2l-1 1h-2l1 2c0 2 0 2-1 3h-2 0c-1 0-1-1-1-2l-5-1-3-5h-1l-11 1c-1-1-2-1-3-2h-1v-1l1-1v-1c0-1 0-2-1-3l-1 1c-1-1-1-1-1-2 1-1 1-3 2-5v-5-1-18-7-4c-1-2-1-4-1-6h0c0-1-1-2-1-3h-1c0 2 0 2-1 3 0-3 0-4-2-7v-9-7-1-3c-1-1-2-1-2-1l-2-2-1-3c-2 3-1 8-2 10l-1 1h0c1 2 0 3 1 4h-3v1l-2-1v1l2 1v1l-1 1 1 2s0 1-1 1c0 1-1 2-1 3h-1v2c-1 1-3 2-5 2h0l-2 2c-2 2-6 6-8 7h0v1l-2 2-1-1c0-1 0-2-1-3l-1-1v-1h-2c0-2 0-2 1-3v-1c-1-1-1-2-1-3l-1 1c0 2 0 4 1 6 1 3 0 9-2 12v1c-1 1-1 1-1 2l-5 1c-3 1-7 2-10 4s-5 4-7 7v-1c0-1 1-2 1-3s0-1-1-1c-1-1-1-1-2-1-1-1-1-3-2-4 0-2-1-4-1-5-1-1-2-2-1-3v-1c-2-2-2-3-2-6h-1l-1-1c-2-3-5-7-8-10h0l-1 1-5-4c-1 0-3-1-5-2h0c-1-1-1-2-1-3h-1c1-1 1-2 2-4l-3 1c-2 1-2 1-3 3l-1 1 1 1c2 0 4 0 5 2h-1l4 2-4 5c4 3 9 7 11 12 2 3 4 9 5 13h-1 0c-2-5-4-10-8-14-20 1-40 12-54 26-9 10-17 21-24 32l-8 15-9 23v1c-5 19-8 38-9 57 0 7 0 14 2 21 2 8 6 15 10 22 5 8 10 15 17 21 3 2 6 4 9 7-6-1-11-3-16-4-10-2-20-4-30-8-15-6-29-16-41-27-9-8-17-17-25-27-4-6-8-13-11-20l1-1-12-32h0l5 3c0-1 0-1 1-2l8 5 7 4c2 1 5 2 7 3 1-1 0-1 1-1l-2-2c-1-1-2-1-3-2 0-1-2-3-3-4s-2-1-3-3l1-1c0-2-1-2-2-4h-3l-1 1c-2-1-3-3-4-5l-2-3-1-2-3-7c-3-7-4-13-5-20v-8l1-4 1-1c0-1 0-2 1-3v-3h1c1-2 2-5 3-7l1-2 1-3 1-1 1-2v-3c0-1-2 0-4-1l1-1c4 0 3-2 6-3 1 0 1 0 1-1l-1-3h-2l5-2c2-1 3-2 5-2h0c3-2 8-6 9-8v-1c-2-2-1-2-1-4 0 1 1 1 2 1 1 1 0 2 2 1 1 0 1 0 1-1 2-1 2-2 3-3h1l1 1 1-2c2 0 3-1 5 0l-1 1v1l3-1v-1c2 0 2 0 4-1h1 1c2 0 6-3 9-4h2l3 1c0-1 0-1 1-1v1 1h1c-2 1-3 1-4 2-2 1-2 1-4 1-1 2-2 3-3 5l-5 2c1 0 2 1 3 1v-1c0 1 0 2 1 3l4 7 1 1c1 2 1 3 2 5v1c1 5 0 7-3 10l-1 1h0l-2 1c1 1 1 2 2 3l2-1 1 1c2 1 2-1 4 1h-1l1 1v1h1l3 3 3-1c0 1 1 1 1 3l-1 2c2 1 2 0 4 0l1-1 1 1-1 2h1c1 0 2-2 3-2h2l1-1s1 0 2-1h0l2-3 3-5 1-2c1-2 1-3 2-4l2-2 10-15c1-1 2-3 3-4l4-6c0 2 0 2 2 3v1h1c1 0 2-1 2-2 2-2 3-2 5-2 23-26 51-43 83-56 1 0 4-2 5-2l12-3 8-1 1-3h0c2 0 2 0 3 1h1l1-1-1-1v-6h4 1l2-1h1 0 3c2-1 1 0 3 0v-2l1-1h0c1-2 0-5 0-8l-1-5 1 1c1 1 2 1 2 3h1l1-1 2 2 2-1h2v-1c1-1 2-2 4-2z" class="y"></path><path d="M336 366c1 1 2 1 2 2-1 1-1 1-2 1l-2 2c0-1 1-2 1-3 1 0 1-1 1-2z" class="J"></path><path d="M554 270c1-1 1-1 2 0l1 1-2 2-1-1v-2z" class="u"></path><path d="M599 269l2 1 1 1c-1 1-1 1-1 2-2-1-2-2-3-3h-1l2-1zM330 405h-1-1c0-2-1-2-2-3l-1-1c3-2 2 0 4 0v-1l1-1h0c0 1-1 3-1 4v1l1 1zm224-135v2l-4 2-2-2c1 0 2-1 2-1 2-1 2-1 4-1z" class="J"></path><path d="M344 335c2-4 5-8 8-11-1 2-1 3-2 4v1 1h0l-2 2v1c-1 1-3 1-4 2z" class="s"></path><path d="M586 268c2 0 4 0 5-1l1 1h1v2c1 0 3 0 4 1h0c1 1 2 1 2 2v1l-1-2c-2-1-3 0-5 0l-1-1v-1l-2-1c-2 0-2 0-4-1z" class="J"></path><path d="M533 255h2l1 2c1 0 2 0 3 1l-8 3c2-2 2-3 2-6z" class="r"></path><path d="M358 329v-2h1l3-3c0-2 2-3 3-4l1-1c0 1 0 1 1 1v1c-1 0-1 1-1 1-1 2-3 3-4 4s-2 2-4 3z" class="J"></path><path d="M535 255c1 0 2-1 3-1 3 1 4-2 7-1l-2 2v1h3l-7 2c-1-1-2-1-3-1l-1-2z" class="t"></path><path d="M331 395h1l-1 3 2 2c2-1 2-2 3-4 0 2-1 4-2 5l-1 1-3 3-1-1v-1c0-1 1-3 1-4h0c0-2 0-3 1-4z" class="c"></path><path d="M526 300v2l2 1v1l2 2-2 1h-1l-2-1c0 2-1 3-1 4l-1 1c-1-2-1-4-1-6l1 4v-2c1-1 1-1 2-1v-1l-1-1c0-2 1-3 2-4z" class="J"></path><path d="M528 304l2 2-2 1h-1l-2-1c1 0 2-1 3-2z" class="X"></path><path d="M579 266c2-1 2 0 3 0 2 0 4-1 6-1 1 0 2 1 3 1 2 0 2 1 4 1 1 1 2 1 4 2l-2 1v-1c-1-1-2-1-4-1h0-1l-1-1c-1 1-3 1-5 1h0c-1-1-2-1-4-1-1 0-2-1-3-1z" class="s"></path><path d="M375 313l3-2v-1c0-1 0-2 1-3h0l-1-1h1l2-1c0-1 0 0 1-1l1 1h1v-2-1l2 2c-1 1-1 2-2 3h-1c0 1-1 2-2 3 0 1-2 3-3 4 0-1-2-1-3-1z" class="J"></path><path d="M557 271l2-2c1 0 1 0 2-1l1-1 1 1 4-1c1 0 2 0 3 1h5c1 0 2 0 4 1l-1 2h-1 0c-1-1-1-1 0-2l-1-1c-1 1-1 1-2 1 0 1-1 1-2 1-1 1-1 0-2 0h-3-1-1c-1-1-1-1-2 0-2 0-3 1-4 2l-2-1z" class="u"></path><path d="M336 389h1c1 0 1 0 2-1 0 2-1 3-1 5 0 1-1 2-2 3-1 2-1 3-3 4l-2-2 1-3c1-2 1-3 2-5 1-1 1-1 2-1z" class="G"></path><path d="M504 267l4 3c2 0 2-1 4-1v1l1 1c2 1 3 3 4 5v6-1-3c-1-1-2-1-2-1l-2-2-1-3c-2 3-1 8-2 10l-1-4c0-2-1-6-2-6-1-1-3-2-4-3h0 0l1-2z" class="J"></path><path d="M605 284l1-1 2 1 1 1 1 1v1h-2c-2 0-3 1-5 1v1h1 3l1-1h1l1 2c-1 1-2 1-4 2h-1c-1 1-1 2-2 2v-1-1h-1l1-1c1 1 1 1 2 1l1-1-1-1h-4v1l-2-1 1-2v-2l-1-1h-1c2 0 3-1 4-1l2 1 1-1z" class="G"></path><path d="M605 284l1-1 2 1 1 1v1h-3 0-2c-2 1-3 2-4 2v-2l-1-1h-1c2 0 3-1 4-1l2 1 1-1z" class="E"></path><path d="M600 286l2-1 2 1c-2 1-3 2-4 2v-2z" class="x"></path><path d="M348 348c2-1 4-3 6-4-1 3-3 5-4 7-1 3-2 5-4 6-1 1-2 3-4 3l-1 2v2c-1 1-2 2-3 4 0-1-1-1-2-2l12-18z" class="u"></path><path d="M490 264h0l2-2 1 2h2v-2h1l2 2h1c1 0 2 0 3 1h0c0 1 0 1 1 2h1l-1 2h0c-2 0-5 0-6 1h-3c-1 0 0 0-1-1h-1c-1 0-2 0-2-1h-1-2l-1-2c1 0 2-1 3-1l1-1z" class="s"></path><path d="M497 267h0c1 0 2 0 2-1 1 1 1 1 1 2l3 1h0c-2 0-5 0-6 1h-3v-1c1-1 2-1 3-2z" class="j"></path><path d="M490 264c3 1 4 2 7 3-1 1-2 1-3 2v1c-1 0 0 0-1-1h-1c-1 0-2 0-2-1h-1-2l-1-2c1 0 2-1 3-1l1-1z" class="p"></path><path d="M468 263c1-1 2-2 4-2 1 0 1 0 2 1l1-1v-1c1 1 2 1 2 2 2 0 3 0 5 1 1 0 3 0 5-1h1c0 1 0 1 1 2v1c-1 0-2 1-3 1l1 2-1 2-2-3-1-1-1-1c-1 2-1 2-2 3-2 0-4-1-5 0v-3h0-4c-1 1-2 1-3 1v-3h0z" class="J"></path><path d="M468 263h0c1 1 2 1 3 2-1 1-2 1-3 1v-3z" class="c"></path><path d="M483 266h3l1 2-1 2-2-3-1-1z" class="b"></path><path d="M475 265l2 1h1c0-1 1-1 2-1l2-1h0l-1 1v1l1-1c-1 2-1 2-2 3-2 0-4-1-5 0v-3z" class="K"></path><path d="M334 371l2-2c0 2 0 3-1 4l1 1c1 1 1 2 3 2l-1 1h0l-2 1-2 2 1 1v-1c2-2 4 0 5-2s2-2 3-3v-1h2c-1 2-3 4-4 5 0 2-2 5-4 6l-2 2c1 0 1 1 2 2h-1c-1 0-1 0-2 1-1 2-1 3-2 5h-1l1-2c0-1 0-1 1-2v-1l1-2c0-1 0-1-1-2 0 1-1 1-1 1h-1v-3h-1c0-1-1-2-1-2l-1 1v-1l1-1 2 2h1l-1-1v-3l2-2 1-5v-1z" class="u"></path><path d="M335 381v-1l2 2v1l-2 1c-1-1-1-1-1-2l1-1z" class="s"></path><path d="M579 266c1 0 2 1 3 1 2 0 3 0 4 1h0c2 1 2 1 4 1l2 1v1l1 1 1 1h0 4c-1 1-2 1-3 2s-2 1-2 2v1h1l1 1v1l1 1h-1-1c-1 0-1 1-2 0v-1h-2c-1-1-1-1-1-2-1-2 0-3-1-5l-1 1s-1 0-2-1h0l-2 1-1-1-1-1c-1-1-2-1-3 0l-1-1h1l1-2c-2-1-3-1-4-1h-5l2-1c2 0 5 0 7-1z" class="j"></path><path d="M583 269h1v1h1 1c-1 1-1 2-2 2l-2-1 1-2z" class="G"></path><path d="M586 268h0c2 1 2 1 4 1l2 1c-2 0-3 1-4 0l-1 1-1-1h-1-1v-1h-1v-1h3z" class="u"></path><path d="M579 266c1 0 2 1 3 1 2 0 3 0 4 1h-3v1l-1 2-3-2c-2-1-3-1-4-1h-5l2-1c2 0 5 0 7-1zm-161 19v-1l2-1-1-1c0-1 1-2 2-2 1-1 1 0 2 0l1-1v-1l2 1h0c0-2 0-3 1-5 1-1 2-1 3-2 2 0 2 0 3-1s1-1 2-1l2 1-2 1v1c1-1 2-2 3-1h1v-1h2c2-1 4 0 6-2 1 1 1 1 2 1 1-1 2-1 4-2 0-1 0-1 1-2 1 0 3 1 4 0 2 0 3-1 4-1h2v-1l1-2c1 0 2 1 3 1v3c-2 0-2 1-4 0-1 0-2 0-3 1h-1 0c-2 0-4 0-6 1 0 1-1 1-2 2-1 0-3 1-4 1l-1-1c-2 1-3 2-4 3h-1c-1 0-2 0-3 1h-2c0 1-1 2-2 3 0 0 0 1-1 1-1-1-2 0-3 0-2 1-3 2-5 3-1 0-2 1-3 2-2 0-3 0-4 2h-1z" class="J"></path><path d="M485 248h4 1c0 1 2 2 3 2l4 1c2 1 5 2 7 2 0 1 0 2-1 4l1 4c-5-2-10-4-16-6l-6-1c0-2 2-1 4-2v-1h-2c1-2 1-2 1-3z" class="M"></path><path d="M493 252c1 0 2 0 4 1l1 1c2 0 3 1 5 2l-2 1c-2-1-4-1-6-2l-1-1h-2-1l2-2z" class="C"></path><path d="M490 248c0 1 2 2 3 2l4 1c2 1 5 2 7 2 0 1 0 2-1 4v-1c-2-1-3-2-5-2l-1-1c-2-1-3-1-4-1l-2-1h0 1c-2 0-2 0-2-1v-2z" class="f"></path><path d="M485 248h4 1v2c0 1 0 1 2 1h-1 0l2 1-2 2h0-2l-1 1-6-1c0-2 2-1 4-2v-1h-2c1-2 1-2 1-3z" class="m"></path><path d="M491 251l2 1-2 2h0-2v-2l2-1z" class="F"></path><path d="M485 242h0v3l-1 2 1 1c0 1 0 1-1 3h2v1c-2 1-4 0-4 2l-1 1h-5l-11 1 1-3h0c2 0 2 0 3 1h1l1-1-1-1v-6h4 1l2-1h1 0 3c2-1 1 0 3 0v-2l1-1z" class="w"></path><path d="M475 246l2-1c1 2 1 2 0 3s-2 1-2 2l-1-1c1-1 1-2 1-3z" class="F"></path><path d="M485 242h0v3l-1 2 1 1c0 1 0 1-1 3h2v1c-2 1-4 0-4 2l-1 1h-5c1-1 1-2 2-2 1-1 1-1 1-2l-1-1c0-2 0-2 1-4l-1-1h0 3c2-1 1 0 3 0v-2l1-1z" class="f"></path><path d="M481 255v-1c0-1 0-2-1-3l1-1c0 1 2 1 3 1h2v1c-2 1-4 0-4 2l-1 1z" class="t"></path><path d="M386 304c1-1 1-1 2-1h1c0-1 0-1 1-2 1 0 1-1 2-1l1-1h1c1-1 3-2 4-3v-1l1-1c1 1 1 1 2 1l3-3h0c2 1 3 1 3 3 1 1 0 1 1 2-1 1-2 2-2 3l-1 1h-2l-1-2h-2v1l2 2c-1 0-3 1-4 1h-1l-3 3h-1l-2 2h0c1 1 1 1 1 2h2c-1 0-2 1-2 1-1 0-2 1-3 2h-2l-1 1c-1 0-1-1-3 1v1h-2l-1-1h0l-2-1c1-1 3-3 3-4 1-1 2-2 2-3h1c1-1 1-2 2-3z" class="u"></path><path d="M384 309c0-1 0-1 1-2h4c-2 2-2 2-5 2z" class="X"></path><path d="M389 307l1-1h1 0 2l-2 2h0c1 1 1 1 1 2h2c-1 0-2 1-2 1-1 0-2 1-3 2h-2l-1 1c-1 0-1-1-3 1v1h-2l-1-1h0l-2-1c1-1 3-3 3-4 1 0 2 0 3-1 3 0 3 0 5-2z" class="j"></path><path d="M391 308h0c1 1 1 1 1 2h2c-1 0-2 1-2 1-1 0-2 1-3 2h-2c1-1 1-2 2-2s1 0 2-1l-1-1 1-1z" class="K"></path><path d="M435 277c1-1 2-2 2-3h2c1-1 2-1 3-1h1c1-1 2-2 4-3l1 1c1 0 3-1 4-1 1-1 2-1 2-2 2-1 4-1 6-1h0l1 1h-2c-1 1-1 1-2 1l2 2h1 3c-1 1-1 2-2 2v2c-1 0-2 1-3 1h-1c-1 1-1 1-1 2v3h-1 0v-2c-1 0-3 1-4 2h-2l-1 1-2 1v-2-1h-3 0l-1-1h-1c-1 0-2 1-2 1l-1-1-4 1v-2c1 0 1-1 1-1z" class="G"></path><path d="M440 276c2-1 3-1 4-2 1 0 2-2 3-2l2 1 2-2v2h-2l-6 3h-3z" class="E"></path><path d="M449 273c2 1 2 1 3 3-1 0-2-1-2 0-2 1-2 1-4 1l-3 3-1-1h-1c-1 0-2 1-2 1l-1-1-4 1v-2c1 0 1-1 1-1h2 0l3-1h3l6-3z" class="d"></path><path d="M435 277h2 0l1 1h2l1 1c-1 0-2 1-2 1l-1-1-4 1v-2c1 0 1-1 1-1z" class="K"></path><path d="M454 272c1-1 1-2 3-3l2 2h1 3c-1 1-1 2-2 2v2c-1 0-2 1-3 1h-1c-1 1-1 1-1 2v3h-1 0v-2c-1 0-3 1-4 2h-2l-1 1-2 1v-2-1h-3 0l3-3c2 0 2 0 4-1 0-1 1 0 2 0-1-2-1-2-3-3h2v-2c1 0 2 0 3 1z" class="L"></path><path d="M446 280c1 0 3-2 4-2s2-1 4-1c0 0 1 1 1 2-1 0-3 1-4 2h-2l-1 1-2 1v-2-1z" class="R"></path><path d="M454 272c1-1 1-2 3-3l2 2h1v3 1c-1-1-1-2-1-3h-1c-1 2-1 2-2 2h-1l-3 2c-1-2-1-2-3-3h2v-2c1 0 2 0 3 1z" class="d"></path><path d="M451 271c1 0 2 0 3 1l-2 1h2l1 1-3 2c-1-2-1-2-3-3h2v-2z" class="n"></path><path d="M593 272c2 0 3-1 5 0l1 2v-1c1 1 2 1 3 1h2 1l1-1 1 1v1l-1 1h2c1 0 1-1 2-1 0 1 1 1 1 1l2 2v-1c1 2 2 3 4 4h4c0 1 1 2 1 3h-1c0-1 0-1-1-2-1 0-2 0-3 1-2 0-4 1-6 1 0 0-2-1-3 0l-2-1-1 1-1 1-2-1c-1 0-2 1-4 1s-4 1-6 1v2h-1-1c0-1 0-1-1-1 0 0 0 1-1 2l-1 1c-1-1-2-2-3-4 1 0 2-1 3-1s3-1 4-1c0-1-1-2-2-3l1-1h2v1c1 1 1 0 2 0h1 1l-1-1v-1l-1-1h-1v-1c0-1 1-1 2-2s2-1 3-2h-4 0l-1-1z" class="c"></path><path d="M590 280h2v1c1 1 1 0 2 0h1 1 0 1 1s1 0 2-1h2l1 1c-1 1-3 1-4 2l-2-1v1c-1 1-4 1-6 1 0-1-1-2-2-3l1-1z" class="E"></path><path d="M591 284c2 0 5 0 6-1v-1l2 1c1 0 1 1 2 1h4l-1 1-2-1c-1 0-2 1-4 1s-4 1-6 1v2h-1-1c0-1 0-1-1-1 0 0 0 1-1 2l-1 1c-1-1-2-2-3-4 1 0 2-1 3-1s3-1 4-1z" class="j"></path><path d="M599 273c1 1 2 1 3 1h2 1l1-1 1 1v1l-1 1h2c1 0 1-1 2-1 0 1 1 1 1 1l2 2v-1c1 2 2 3 4 4h4c0 1 1 2 1 3h-1c0-1 0-1-1-2-1 0-2 0-3 1h-7c-1 0-3 0-4-1 1-3 5-1 7-2l-2-2c-3 0-4-2-7 0h-1c1-2 2-2 3-3h-2c-2 1-2 1-4 0-1 0-1-1-1-1v-1z" class="s"></path><path d="M431 278c1 0 2-1 3 0v2l4-1 1 1s1-1 2-1h1l1 1h0 3v1 2l2-1-3 3c-1 0-1 0-1 1h-1-2c-1 2-1 1 0 3l-1 1c-2 1-2 1-3 3 0 1-1 2-1 2l3 3-1 1-5-4c-1 0-3-1-5-2h0c-1-1-1-2-1-3h-1c1-1 1-2 2-4l-3 1 1-2-2-1-1-1c1-1 2-2 3-2 2-1 3-2 5-3z" class="d"></path><path d="M429 285h3l-1 1c-1 1-1 3-2 4h-2-1c1-1 1-2 2-4l1-1z" class="K"></path><path d="M434 280l4-1 1 1-1 1c-3 0-3 0-5 2 0 1 0 1-1 2h-3l-2-2c1-1 1-1 2-1l3-1 2-1z" class="X"></path><path d="M433 289l3 1c-1 1-2 1-2 2l1 3h1l3 3-1 1-5-4-1-1v-1l-1-1c0-1 0-1-1-2h1 2v-1z" class="L"></path><path d="M431 278c1 0 2-1 3 0v2l-2 1-3 1c-1 0-1 0-2 1l2 2-1 1-3 1 1-2-2-1-1-1c1-1 2-2 3-2 2-1 3-2 5-3z" class="j"></path><path d="M431 278c1 0 2-1 3 0v2l-2 1h-1v-3z" class="b"></path><path d="M427 283l2 2-1 1-3 1 1-2v-2h1z" class="c"></path><path d="M441 279h1l1 1h0 3v1 2l2-1-3 3c-1 0-1 0-1 1h-1-2c-1 2-1 1 0 3l-1 1c-2 1-2 1-3 3 0 1-1 2-1 2h-1l-1-3c0-1 1-1 2-2l-3-1v-1l1-1c-1-1-1-1-1-2 2-1 4-1 5-2v-2l1-1s1-1 2-1z" class="V"></path><path d="M441 284c2-1 3-3 5-3v2c-1 1-3 1-5 1z" class="U"></path><path d="M438 288l2 2c-2 1-2 1-3 3l-1-2 1-1c0-1 0-1 1-2z" class="R"></path><path d="M446 283l2-1-3 3c-1 0-1 0-1 1h-1-2c-1 2-1 1 0 3l-1 1-2-2c1-2 2-3 3-4 2 0 4 0 5-1z" class="q"></path><path d="M441 279h1l1 1h0l-1 1-1 1c-2 0-2 1-3 2-1 2-2 2-3 3l-1 1h-1l1-1c-1-1-1-1-1-2 2-1 4-1 5-2v-2l1-1s1-1 2-1z" class="I"></path><path d="M471 265h4 0v3c1-1 3 0 5 0 1-1 1-1 2-3l1 1 1 1 2 3-2 2v1h-2v-1c-1 1-1 1-2 1l-1 1c1 2 2 3 3 4 0 0 0 1-1 1l-1-1-1 1h-2v2h-2c-1-1-2-1-2-1l-2-1-1-1v-3l-2-1-1 1h-1 0c-1 0-2 1-2 2l-1-1c-1 0-1 0-2 1l-1-1h-3 1c1 0 2-1 3-1v-2c1 0 1-1 2-2h-3-1l-2-2c1 0 1 0 2-1h2l-1-1h1c1-1 2-1 3-1 2 1 2 0 4 0 1 0 2 0 3-1z" class="m"></path><path d="M469 270l2 1 2 2h0l-1 1c-2-1-2-1-3-2h-1l-1 2h-1l-2-2c1-1 1 0 1-1 1-1 3-1 4-1z" class="L"></path><path d="M470 275l1-1c1 1 1 1 1 2 0 0 1 1 2 1h2c1 1 1 1 1 2v2h-2c-1-1-2-1-2-1l-2-1-1-1v-3z" class="F"></path><path d="M471 265h4 0v3c-1 0-2 0-3 1v1l2 2s-1 0-1 1l-2-2-2-1c-1 0-3 0-4 1 0 1 0 0-1 1l-1-1h-3-1l-2-2c1 0 1 0 2-1h2l-1-1h1c1-1 2-1 3-1 2 1 2 0 4 0 1 0 2 0 3-1z" class="P"></path><path d="M461 268c2 1 3 1 5 1l1-1c1 0 2 1 2 2-1 0-3 0-4 1 0 1 0 0-1 1l-1-1h-3-1l-2-2c1 0 1 0 2-1h2z" class="b"></path><path d="M482 265l1 1 1 1 2 3-2 2v1h-2v-1c-1 1-1 1-2 1l-1 1c1 2 2 3 3 4 0 0 0 1-1 1l-1-1c0-1-2-2-3-2l-3-3h-1 0c0-1 1-1 1-1l-2-2v-1c1-1 2-1 3-1 1-1 3 0 5 0 1-1 1-1 2-3z" class="Y"></path><path d="M481 270c1-1 1-2 2-3h1c0 2-1 2-2 4l-1-1z" class="h"></path><path d="M484 267l2 3-2 2-2-1c1-2 2-2 2-4z" class="d"></path><path d="M481 270l1 1 2 1v1h-2v-1c-1 1-1 1-2 1l-1 1c1 2 2 3 3 4 0 0 0 1-1 1l-1-1c0-1-2-2-3-2l-3-3 3-3 2 2c0-1 1-2 2-2z" class="k"></path><path d="M347 318c0 2 0 2 2 3v1h1c1 0 2-1 2-2 2-2 3-2 5-2l-5 6h0c-3 3-6 7-8 11l-14 24c0-1-1-1-1 0v-7c1-1 1-2 0-2-1-1-1-1-3-1 1-2 1-3 2-4l2-2 10-15c1-1 2-3 3-4l4-6z" class="B"></path><path d="M330 343l1-1c1-1 2-1 5-2-1 2-4 7-6 8-1-1-2-1-2-3l2-2z" class="W"></path><path d="M340 328l2 2c-1 2-3 4-4 7-1 1-2 2-2 3-3 1-4 1-5 2l-1 1 10-15z" class="D"></path><path d="M457 276h3l1 1c1-1 1-1 2-1l1 1c0-1 1-2 2-2h0 1l1-1 2 1v3l1 1 2 1s1 0 2 1h2v-2h2l1-1 1 1c1 2 2 3 2 5 0 1 1 3 0 4s-2 1-3 2l-1 1v3c-1-1-1-1-1-2l-2 1-2-2h0c-2-2-2-2-4-2h0l-1-1c-3-2-6-3-9-5-1 0-2 2-3 2-2 0-1-2-2 0l-1 1h-2v-1l-2 2c-1 1-2 1-4 2h0v-1l-1-2v-1c2 0 3-1 5-2h3c-1 0-1-1-2-2 1-1 3-2 4-2v2h0 1v-3c0-1 0-1 1-2z" class="B"></path><path d="M477 279h2l1-1 1 1c1 2 2 3 2 5h-1c-2 1-2 2-2 4h-1v-4c-1-1-2-2-2-3v-2z" class="q"></path><path d="M457 276h3l1 1c1-1 1-1 2-1l1 1c0-1 1-2 2-2h0 1l1-1 2 1v3l1 1c-2 0-3 0-4-1l-1-1c-1 0-2 0-2 1h-1c-1 0-2 0-3 1h-1c-1 0-2 1-2 2v1h-1-1l-2 1c-1 0-1-1-2-2 1-1 3-2 4-2v2h0 1v-3c0-1 0-1 1-2z" class="U"></path><path d="M470 289l-1-1c0-1 0-1-1-2h0c-1-1-2-1-4-2-1-1-2-1-2-2 1-2 2-2 4-2 1 1 2 1 3 1h1 1c1 0 2 0 3 1 1 0 1 1 1 2h0c2 0 2 0 3 1v1h-1c1 2 1 4 1 6l-2 1-2-2h0c-2-2-2-2-4-2h0z" class="D"></path><path d="M475 290s0-1-1-1l-1-1-1-1h1 2c1 2 1 2 0 3z" class="O"></path><path d="M475 287l2-1c1 2 1 4 1 6l-2 1-2-2 1-1c1-1 1-1 0-3z" class="F"></path><defs><linearGradient id="n" x1="722.156" y1="354.245" x2="734.164" y2="341.558" xlink:href="#B"><stop offset="0" stop-color="#a09f9d"></stop><stop offset="1" stop-color="#d4d1d2"></stop></linearGradient></defs><path fill="url(#n)" d="M720 338v-3c0-2-3-5-4-7v-2c-2-1-2-3-3-5l4 5h1l4 4 12 18 3 5c2 4 4 7 5 11v1 1c1 1 1 0 1 2 1 1 2 2 2 4h0l-2 2v2l-2 2-5-11-16-29z"></path><path d="M736 367h4c1 1 2 1 2 1 1 1 1 2 1 3v3 2l-2 2-5-11z" class="M"></path><path d="M489 268h1c0 1 1 1 2 1h1c1 1 0 1 1 1h3c1-1 4-1 6-1h0c1 1 3 2 4 3 1 0 2 4 2 6l1 4-1 1h0c1 2 0 3 1 4h-3v1l-2-1 1-2-2-2c-1 1-2 1-3 1v-2h-1-3l-1-3v-1 1l-1 1h-1-1c0 1-1 1-1 2l-1-1h0l-1-1c-1 0-2 2-3 2 0 1-1 0-1 1l-1 1v4 1l-1 4h-1v-5c1-1 0-3 0-4 0-2-1-3-2-5 1 0 1-1 1-1-1-1-2-2-3-4l1-1c1 0 1 0 2-1v1h2v-1l2-2 1-2h2z" class="S"></path><path d="M505 282c2 0 2 1 4 2v1c-2-1-2 0-3 0l-2-2 1-1z" class="i"></path><path d="M496 275c3 1 5 1 7 2l1 1c1 1 1 2 1 4l-1 1c-1 1-2 1-3 1v-2h1c1-1 2-1 2-3h-2l-1-1h-2c-1 0-2-1-3-3z" class="U"></path><path d="M494 275h2 0c1 2 2 3 3 3h2l1 1h2c0 2-1 2-2 3h-1-1-3l-1-3v-1 1l-1 1h-1l-1-2h-1l1-2 1-1h0z" class="F"></path><path d="M494 275h0l2 4-1 1h-1l-1-2h-1l1-2 1-1z" class="q"></path><path d="M502 279h2c0 2-1 2-2 3h-1-1-2 0v-1c1-1 2-2 4-2z" class="R"></path><path d="M497 270c1-1 4-1 6-1h0c1 1 3 2 4 3 1 0 2 4 2 6h-2l-1-1v-1-1h-1c-1 0-3-1-4-2h-1-3v-2-1z" class="d"></path><path d="M497 271l2-1c1 0 1 1 2 2h0l-4 1v-2z" class="p"></path><path d="M489 268h1c0 1 1 1 2 1h1c1 1 0 1 1 1h3v1 2h3 1l-4 1-1-1-1-1h-1v3l-1 1-1 2h1l1 2h-1c0 1-1 1-1 2l-1-1h0l-1-1c-1 0-2 2-3 2 0 1-1 0-1 1l-1 1v4 1l-1 4h-1v-5c1-1 0-3 0-4 0-2-1-3-2-5 1 0 1-1 1-1-1-1-2-2-3-4l1-1c1 0 1 0 2-1v1h2v-1l2-2 1-2h2z" class="t"></path><path d="M484 273v2 1c-1 0-1 0-1 1-1-2-1-2-1-4h2zm7-1h3v3c-3 0-2-1-4-2l1-1z" class="L"></path><path d="M487 268h2l2 2v2l-1 1h-1c-2-1-3-1-5-1l2-2 1-2z" class="I"></path><path d="M489 268h1c0 1 1 1 2 1h1c1 1 0 1 1 1h3v1 2h3 1l-4 1-1-1-1-1h-1-3v-2l-2-2z" class="Q"></path><path d="M483 284h1v-4l1-1c0-1 0-1-1-2v-1c2 0 2 0 3 1h1v-2c2 0 3 0 5 1l-1 2h1l1 2h-1c0 1-1 1-1 2l-1-1h0l-1-1c-1 0-2 2-3 2 0 1-1 0-1 1l-1 1v4 1l-1 4h-1v-5c1-1 0-3 0-4z" class="M"></path><path d="M485 284v-2c1-1 1-2 3-2h0c1-2 1-2 1-3l1-1 1 2h1 1l1 2h-1c0 1-1 1-1 2l-1-1h0l-1-1c-1 0-2 2-3 2 0 1-1 0-1 1l-1 1z" class="T"></path><path d="M448 282l1-1h2c1 1 1 2 2 2h-3c-2 1-3 2-5 2v1l1 2v1h0c2-1 3-1 4-2l2-2v1h2l1-1c1-2 0 0 2 0 1 0 2-2 3-2 3 2 6 3 9 5l-2 1v4l1 1c0 1 1 3 2 4l1 2-1 1v3h-2c0-1-1-2-1-2-1-1-2 0-3-1l1-4c-1-1-1-2-2-2h-2l-1-1c-2 0-3 1-5 1l-1 1h-2l-2 2v1h2l-1 1h-1c-1 0-1 1-1 2l2 2h2v1l-2 3-1-1-2 2-1-1c-2-3-5-7-8-10h0l-3-3s1-1 1-2c1-2 1-2 3-3l1-1c-1-2-1-1 0-3h2 1c0-1 0-1 1-1l3-3z" class="O"></path><path d="M446 295l3-2c1 0 1 1 2 2v1c-2 1-2 1-4 1l-1-1v-1z" class="N"></path><path d="M443 295c1 0 2-1 3-1h0v1 1l-1 1c1 1 1 1 2 1 0 1 1 1 1 2s0 1-1 2c-1-1-1-1-1-2l-1 2-4-4 1-1h1 1l-1-2z" class="H"></path><path d="M448 282l1-1h2c1 1 1 2 2 2h-3c-2 1-3 2-5 2v1l1 2v1c-1 0-2 0-2 1-1 2-3 2-2 4l1 1 1 2h-1-1l-1 1 4 4 2 2h-1l2-1c0 1 1 1 2 2-1 1-1 0-2 0v1l2 1-2 2-1-1c-2-3-5-7-8-10h0l-3-3s1-1 1-2c1-2 1-2 3-3l1-1c-1-2-1-1 0-3h2 1c0-1 0-1 1-1l3-3z" class="T"></path><path d="M441 289h2c-1 3-4 3-4 5l1 2c0 1 0 1-1 2h0l-3-3s1-1 1-2c1-2 1-2 3-3l1-1z" class="U"></path><path d="M454 294c-1 0-1 0-2-1v-1l1-1h4l3-2h0v-2h1l4 4h0l2 2 1 1c0 1 1 3 2 4l1 2-1 1v3h-2c0-1-1-2-1-2-1-1-2 0-3-1l1-4c-1-1-1-2-2-2h-2l-1-1c-2 0-3 1-5 1l-1-1z" class="H"></path><path d="M468 304v-2-5c-1 0-1-1-2-2 0-1 0-1-1-2h0v-2l2 2 1 1c0 1 1 3 2 4l1 2-1 1v3h-2z" class="C"></path><path d="M454 294c-1 0-1 0-2-1v-1l1-1h4l3-2h0v-2h1v3c1 0 0 0 1 1 1 0 2 1 2 1l-1 1v-1l-1 1h-2l-1-1-5 2z" class="W"></path><path d="M375 313c1 0 3 0 3 1l2 1h0l1 1c-1 0-1 1-2 1 0 1-1 2-1 2l1 1v2c-1 0-1 0-2 1h-2c-1 0-2 1-3 2h-1s1 2 0 2v1c-1 0 0 1 0 1-1 0-1 0-2 1-1 0-1 1-2 1v1 1h0l-3 2-2 2c1 1 2 1 3 1l1-1h1v1l-2 3-1 1h0s0 1-1 2v1l2 3c-2 1-2 3-4 4h0l-2 2h0c-2 0-3 1-4 1v-3h0l-1-1-2-1-2 1c1-2 3-4 4-7-2 1-4 3-6 4-1 0-2-1-3-1l2-2c1-1 0-2 1-3h2 0c0-1 1-2 1-2 2-2 2-2 2-3 1-1 1-1 1-2l-2-2 1-1h2 0v-1-1l1-1 1 1-1 2c1 0 1 0 2-1v-2c2-1 3-2 4-3s3-2 4-4c0 0 0-1 1-1h1c1-1 2-2 4-2 0-1 1-1 2-2v-2l1-2z" class="J"></path><path d="M359 340l3-3c1 1 2 1 3 1l1-1h1v1l-2 3-1 1h0s0 1-1 2v1l-1 1-2-1v-2l-2 1c-1-1-1-2-2-2l3-2z" class="b"></path><path d="M356 342l3-2c1 1 2 1 3 2l-2 1-2 1c-1-1-1-2-2-2z" class="p"></path><path d="M362 342h2s0 1-1 2v1l-1 1-2-1v-2l2-1z" class="Y"></path><path d="M354 344l2-2c1 0 1 1 2 2l2-1v2l-3 5-2 2h0l-1-1-2-1-2 1c1-2 3-4 4-7z" class="b"></path><path d="M354 344l2-2c1 0 1 1 2 2-1 1-2 1-2 2-1 1-2 2-2 3l-2 1-2 1c1-2 3-4 4-7z" class="P"></path><path d="M363 345l2 3c-2 1-2 3-4 4h0l-2 2h0c-2 0-3 1-4 1v-3l2-2 3-5 2 1 1-1z" class="L"></path><path d="M360 345l2 1c-1 1-2 2-2 4l-1 1-2-1 3-5z" class="k"></path><path d="M375 313c1 0 3 0 3 1l2 1h0l1 1c-1 0-1 1-2 1 0 1-1 2-1 2l1 1v2c-1 0-1 0-2 1h-2c-1 0-2 1-3 2h-1s1 2 0 2v1c-1 0 0 1 0 1-1 0-1 0-2 1-1 0-1 1-2 1v1l-5 2-1 1c-1 1-2 1-3 2l-6 5-1-1c1-1 5-4 6-6v-2s1 0 2-1l-1-1v-2c2-1 3-2 4-3s3-2 4-4c0 0 0-1 1-1h1c1-1 2-2 4-2 0-1 1-1 2-2v-2l1-2z" class="c"></path><path d="M366 327c0-1 1-2 2-4h2 0v-1c1-1 2-1 3-1l-2 2h0c1 1 1 0 1 1v1h-1l-3 1c-1 1-1 1-2 1z" class="j"></path><path d="M380 315h0l1 1c-1 0-1 1-2 1 0 1-1 2-1 2l1 1v2c-1 0-1 0-2 1h-2c-1 0-2 1-3 2v-1c0-1 0 0-1-1h0l2-2c1-1 3-3 3-4 2 0 3 0 4-2z" class="E"></path><path d="M378 319l1 1v2c-1 0-1 0-2 1h-2c-1 0-2 1-3 2v-1c1-1 2-2 4-2h0v-1l2-2z" class="b"></path><path d="M366 327c1 0 1 0 2-1l3-1s1 2 0 2v1c-1 0 0 1 0 1-1 0-1 0-2 1-1 0-1 1-2 1v1l-5 2h-1c1-1 1-1 1-2s2-1 2-2c1-1 1-2 2-3z" class="p"></path><path d="M455 295c2 0 3-1 5-1l1 1h2c1 0 1 1 2 2l-1 4c1 1 2 0 3 1 0 0 1 1 1 2v1c-1 1-2 0-2 2v3h0l2-1v3h0c0 2 1 3 1 4v2h-1c-1 0-2 1-3 1v3c-1 1-1 1-2 1v-2l-1 1-1 1c0 2-1 3-2 4-2 0-1-1-3-1-1 0-1-1-3-2 0-2-1-4-1-5-1-1-2-2-1-3v-1c-2-2-2-3-2-6h-1l2-2 1 1 2-3v-1h-2l-2-2c0-1 0-2 1-2h1l1-1h-2v-1l2-2h2l1-1z" class="W"></path><path d="M469 316l-2-1-1 1v1l-2-3 1-2h3c0 2 1 3 1 4z" class="D"></path><path d="M458 324c2-2-2-3-2-5h1c1 1 3 2 3 3l1 2v-1c0 2-1 3-2 4l-1-3zm0-16v-4c1-1 1-3 2-4l-2-2h0c1-1 1-1 2-1l3 3c-2 2-3 3-3 6 0 1-1 1-2 2z" class="O"></path><path d="M449 309c1 1 2 1 2 3v1c1 1 2 1 2 2 0 2 1 7 3 8l2 1 1 3c-2 0-1-1-3-1-1 0-1-1-3-2 0-2-1-4-1-5-1-1-2-2-1-3v-1c-2-2-2-3-2-6z" class="H"></path><path d="M462 322l-1-1c-1-1-2-2-2-4l-2-3c-1-1-2-1-3-1h-1v-1l-1-1c1-1 2 0 3-1v-1h-1l-2-2 2-1h3c0 1 1 1 1 2h0c0 1-1 2 0 3v1 1l2-1 1 1h-1c0 2-1 2 0 3l2 3 1-1c1 0 1 1 2 1v3c-1 1-1 1-2 1v-2l-1 1z" class="O"></path><defs><linearGradient id="o" x1="349.95" y1="383.911" x2="343.525" y2="365.919" xlink:href="#B"><stop offset="0" stop-color="#2c2a2c"></stop><stop offset="1" stop-color="#4b4c45"></stop></linearGradient></defs><path fill="url(#o)" d="M350 351l2-1 2 1 1 1h0v3c1 0 2-1 4-1h0l2-2h0l2 2c-1 1-2 3-4 5-2 4-13 22-12 25v1l-9 23v1h-1v-1l1-1v-1h0v-1l1-1v-1-1c1-1 1-2 1-3l1-1v-2l1-2c1-1 0-1 1-1l2-5 1-1v-2-1l-1-1h-1c-2 2-4 5-4 7l-2 2v1c0-2 1-3 1-5-1 1-1 1-2 1h-1 1c-1-1-1-2-2-2l2-2c2-1 4-4 4-6 1-1 3-3 4-5h-2v1c-1 1-2 1-3 3s-3 0-5 2v1l-1-1 2-2 2-1h0l1-1c-2 0-2-1-3-2l-1-1c1-1 1-2 1-4 1 0 1 0 2-1 1-2 2-3 3-4v-2l1-2c2 0 3-2 4-3 2-1 3-3 4-6z"></path><path d="M343 374l-1-1s-1 0-2-1h1c2-1 2-1 4-1l1 1v1c-1 0 0 1 0 1v1 1l-1 2h0c0 2-1 2-2 3h-1l-1-2c1-1 3-3 4-5h-2z" class="E"></path><path d="M348 365v-1c2-1 2-2 1-3l1-2c1 1 1 2 2 2h4l-2 1v2c-1 2-3 3-4 4l1 2-3 2c-1-1-1-1-1-2v-1l2-2-1-2z" class="d"></path><path d="M355 355c1 0 2-1 4-1h0l2-2h0l2 2c-1 1-2 3-4 5 0 0-2 1-2 2h-1-4c-1 0-1-1-2-2h1l1-1h1c1 0 1 0 2-1-1-1-1 0-1-1l1-1z" class="Q"></path><path d="M350 351l2-1 2 1 1 1h0v3l-1 1c0 1 0 0 1 1-1 1-1 1-2 1h-1l-1 1h-1l-1 2c1 1 1 2-1 3v1 1 1l-2 1v4l-1-1c0-1-1-2-2-3l-2 1c2-2 5-4 5-7v-1l1-1c0-1-1-2-1-3 2-1 3-3 4-6z" class="K"></path><path d="M355 352h0v3l-1 1c0 1 0 0 1 1-1 1-1 1-2 1l-1-2c0-2 1-3 3-4z" class="l"></path><path d="M338 368c1-2 2-3 3-4v-2l1-2c2 0 3-2 4-3 0 1 1 2 1 3l-1 1v1c0 3-3 5-5 7l2-1c1 1 2 2 2 3-2 0-2 0-4 1h-1c1 1 2 1 2 1l1 1v1c-1 1-2 1-3 3s-3 0-5 2v1l-1-1 2-2 2-1h0l1-1c-2 0-2-1-3-2l-1-1c1-1 1-2 1-4 1 0 1 0 2-1z" class="G"></path><path d="M469 288l1 1h0c2 0 2 0 4 2h0l2 2 2-1c0 1 0 1 1 2v-3l1-1c1-1 2-1 3-2v5h1v1c-1 2-2 4-2 6l-1 1c0 2 0 4 1 6 1 3 0 9-2 12v1c-1 1-1 1-1 2l-5 1c-3 1-7 2-10 4s-5 4-7 7v-1c0-1 1-2 1-3s0-1-1-1c-1-1-1-1-2-1-1-1-1-3-2-4 2 1 2 2 3 2 2 0 1 1 3 1 1-1 2-2 2-4l1-1 1-1v2c1 0 1 0 2-1v-3c1 0 2-1 3-1h1v-2c0-1-1-2-1-4h0v-3l-2 1h0v-3c0-2 1-1 2-2v-1h2v-3l1-1-1-2c-1-1-2-3-2-4l-1-1v-4l2-1z" class="t"></path><path d="M471 316h1v2l2-2 1 3c1 1 1 1 2 1 0 1 0 0-1 1-2 0-3 1-4 0l-1-2v-3z" class="T"></path><path d="M469 316c1 3 1 3 0 5 0 1 0 1-1 1h-3v-3c1 0 2-1 3-1h1v-2z" class="B"></path><path d="M462 322l1-1v2c1 0 1 0 2-1h3l-10 8c0-1 0-1-1-1-1-1-1-1-2-1-1-1-1-3-2-4 2 1 2 2 3 2 2 0 1 1 3 1 1-1 2-2 2-4l1-1z" class="C"></path><path d="M475 306c1 1 2 1 3 2 0 1 0 0 1 1h1v-2l-2-1c0-2 0-3 2-4h0c1 5 1 13-2 17l-1 1c-1 0-1 0-2-1l-1-3-2 2v-2h-1l-2-3c2-1 2-1 4-1 1 1 1 2 2 3l1-1v-5l-1-3z" class="N"></path><path d="M471 300c1 2 3 4 4 6l1 3v5l-1 1c-1-1-1-2-2-3-2 0-2 0-4 1 0-1 0-1-1-1v-3l-2 1h0v-3c0-2 1-1 2-2v-1h2v-3l1-1z" class="H"></path><path d="M471 303h0l2 2 1 2v3 1l-1-1h-2s0 1-1 1h0v-3c0-2 1-3 1-5z" class="W"></path><path d="M469 288l1 1h0c2 0 2 0 4 2h0l2 2 2-1c0 1 0 1 1 2v-3l1-1c1-1 2-1 3-2v5h1v1c-1 2-2 4-2 6l-1 1-1 1h0c-2 1-2 2-2 4l2 1v2h-1c-1-1-1 0-1-1-1-1-2-1-3-2-1-2-3-4-4-6l-1-2c-1-1-2-3-2-4l-1-1v-4l2-1z" class="e"></path><path d="M468 294c1 0 2 0 3 2l-1 2c-1-1-2-3-2-4z" class="o"></path><path d="M480 290c1-1 2-1 3-2v5h1v1c-1 2-2 4-2 6l-1 1-1 1h0c-1-2-2-4-3-5l-7-8c2 0 2 0 4 2h0l2 2 2-1c0 1 0 1 1 2v-3l1-1z" class="M"></path><path d="M480 290c1-1 2-1 3-2v5h1v1h-2v1l-2-5z" class="H"></path><defs><linearGradient id="p" x1="489.581" y1="285.882" x2="496.454" y2="307.285" xlink:href="#B"><stop offset="0" stop-color="#c0bfbc"></stop><stop offset="1" stop-color="#e3e2e2"></stop></linearGradient></defs><path fill="url(#p)" d="M496 279v-1 1l1 3h3 1v2c1 0 2 0 3-1l2 2-1 2v1l2 1v1l-1 1 1 2s0 1-1 1c0 1-1 2-1 3h-1v2c-1 1-3 2-5 2h0l-2 2c-2 2-6 6-8 7h0v1l-2 2-1-1c0-1 0-2-1-3l-1-1v-1h-2c0-2 0-2 1-3v-1c-1-1-1-2-1-3 0-2 1-4 2-6v-1l1-4v-1-4l1-1c0-1 1 0 1-1 1 0 2-2 3-2l1 1h0l1 1c0-1 1-1 1-2h1 1l1-1z"></path><path d="M498 292c-1 2-1 2-2 3l-1-1 2-3 1 1z" class="a"></path><path d="M497 291c0-1 1-2 2-3l1 1v1c-1 1-1 2-2 2l-1-1z" class="D"></path><path d="M500 297h1v2l-2 2-2 2c-2 2-6 6-8 7 1-1 3-3 4-5s4-5 6-7c1 0 1-1 1-1z" class="o"></path><path d="M483 304l2-2-1 3h1c1 0 1 1 2 2 0 2 0 3 2 4l-2 2-1-1c0-1 0-2-1-3l-1-1v-1h-2c0-2 0-2 1-3z" class="N"></path><path d="M500 289h2c1 2 2 2 2 5 0 0 0 1-1 2v1c-1 1-1 2-2 2v-2h-1v-3c1-2 1-2 0-4v-1z" class="a"></path><path d="M485 300c-1 0-1 0-2-1 1-1 1-1 2-1s1-1 2-1c1-1 1-1 2-1 1-1 2 0 3-2 0 2 0 2-1 4h0 1 1l-3 3-1 1c-2-1-1-3-4-2z" class="W"></path><path d="M496 279v-1 1l1 3h3 1v2c1 0 2 0 3-1l2 2-1 2v1l2 1v1l-1 1 1 2s0 1-1 1c0 1-1 2-1 3h-1v2c-1 1-3 2-5 2h0l2-2c1 0 1-1 2-2v-1c1-1 1-2 1-2 0-3-1-3-2-5v-1-1h-1c-1-1-1-1-2-3h-1l-2 1h1v1h-1l-3-3h-1c-1-1-1 0-2 0l-1 1c0 2-3 3-4 4v-4l1-1c0-1 1 0 1-1 1 0 2-2 3-2l1 1h0l1 1c0-1 1-1 1-2h1 1l1-1z" class="B"></path><path d="M485 288c1-1 4-2 4-4l1-1c1 0 1-1 2 0h1l3 3h-2-1l1 1v4c-1 1-2 2-2 3-1 2-2 1-3 2-1 0-1 0-2 1-1 0-1 1-2 1s-1 0-2 1c1 1 1 1 2 1l1 2h-1l-2 2v-1c-1-1-1-2-1-3 0-2 1-4 2-6v-1l1-4v-1z" class="a"></path><path d="M485 289l2 1v2c-1 0-2 1-3 1l1-4z" class="H"></path><defs><linearGradient id="q" x1="343.615" y1="509.665" x2="307.383" y2="521.965" xlink:href="#B"><stop offset="0" stop-color="#6e6d6c"></stop><stop offset="1" stop-color="#9f9e9d"></stop></linearGradient></defs><path fill="url(#q)" d="M330 495c7 12 14 24 23 34-10-2-20-4-30-7-8-2-16-6-23-11l4 1h0c1-1 1-2 1-3l-1-1-1 1v-3c2 2 4 3 7 5v-1c-1-2-2-3-4-4-1 0-1 0-2-1v-1l1 1c2-1 3-1 3-2l1-1 1 2v2l1-2h0c3 0 5-2 7-2l1-1h3v-1c0-1-1-3-2-4 3 1 7 2 9 1 1-1 1-1 1-2z"></path><path d="M310 511v-1l3 1h1c3 1 5 3 8 4v-3h2 0c0 1 1 2 2 2 2 1 3 2 4 4v2c-7-2-14-6-20-9z" class="K"></path><path d="M322 512h2 0c0 1 1 2 2 2l-1 2c-1 0-2 0-3-1v-3z" class="h"></path><path d="M324 506h0l-1-2 1-1c3 4 6 9 10 12s7 5 10 9c-3-1-6-3-9-3l-5-1v-2c-1-2-2-3-4-4-1 0-2-1-2-2h0-2l-2-3h1v-1c2 0 2-1 3-2z" class="u"></path><path d="M321 508c2 0 2-1 3-2h1v4l1 1h0c1 0 2 1 3 2h-2l-3-1h0-2l-2-3h1v-1z" class="d"></path><path d="M324 512l3 1h2c2 2 5 5 6 8l-5-1v-2c-1-2-2-3-4-4-1 0-2-1-2-2z" class="j"></path><path d="M322 501v-1l2 3-1 1 1 2h0c-1 1-1 2-3 2v1h-1l2 3v3c-3-1-5-3-8-4h-1l-3-1c-1-2-2-3-4-4-1 0-1 0-2-1v-1l1 1c2-1 3-1 3-2l1-1 1 2v2l1-2h0c3 0 5-2 7-2l1-1h3z" class="l"></path><path d="M322 501v-1l2 3-1 1 1 2h0c-1 1-1 2-3 2v1h-1c-1 1-1 1-1 3h-1v-1-1-1c1-1 1-2 1-3l-1-1c1-2 3-2 3-3l-2-1h3z" class="Q"></path><path d="M322 501v-1l2 3-1 1 1 2h0c-1 1-1 2-3 2 0-1-1-1-1-1 1-1 1-2 2-3s0-1 0-3z" class="K"></path><path d="M682 285c0-2-1-4-1-5h-1c1 0 2 0 3 1h0l3 3c3 6 10 12 15 17v-1l1-1 2 3c1 0 1 0 2-1l2 3c1 1 2 4 3 4 2 1 2 2 3 3l2 2 1 1c1 0 2-1 3-2l5 7c2 3 5 6 8 9l-5 1 1 1h0c-1 0-1 0-2-1l-1 3-3-3-1 1-4-4h-1l-4-5c1 2 1 4 3 5v2c1 2 4 5 4 7v3l-5-6-12-15c-1 0-11-10-13-12-10-9-22-16-33-24 2 1 4 1 6 2h2c4 1 10 5 12 8h1c2 1 3 2 5 3l2 2c1 0 1 0 1-1-1-1-3-3-5-4s-5-3-8-5v-3c2 2 5 3 7 4l10 9v-1c-1-2-3-5-5-7l-3-3z" class="B"></path><path d="M703 317c4 2 8 4 10 9h1c1 2 1 4 1 6l-12-15z" class="w"></path><defs><linearGradient id="r" x1="694.468" y1="299.038" x2="697.53" y2="295.962" xlink:href="#B"><stop offset="0" stop-color="#1b1b1a"></stop><stop offset="1" stop-color="#353433"></stop></linearGradient></defs><path fill="url(#r)" d="M682 285c0-2-1-4-1-5h-1c1 0 2 0 3 1h0l3 3c3 6 10 12 15 17v-1l1-1 2 3c2 3 3 5 5 8 1 2 3 4 4 6l2 4c1 0 1 1 1 1 2 3 5 5 7 8l-1 1-4-4c0-2-3-4-4-6-8-8-15-16-24-24v-1c-1-2-3-5-5-7l-3-3z"></path><path d="M704 302c1 0 1 0 2-1l2 3c1 1 2 4 3 4 2 1 2 2 3 3l2 2 1 1c1 0 2-1 3-2l5 7c2 3 5 6 8 9l-5 1 1 1h0c-1 0-1 0-2-1l-1 3-3-3c-2-3-5-5-7-8 0 0 0-1-1-1l-2-4c-1-2-3-4-4-6-2-3-3-5-5-8z" class="V"></path><path d="M709 310l1-1c2 2 4 5 6 7l1-1 2 2c0 2 2 3 3 4 1 2 3 3 4 5 0 1 1 2 2 3l1 1h0c-1 0-1 0-2-1l-1 3-3-3c-2-3-5-5-7-8 0 0 0-1-1-1l-2-4c-1-2-3-4-4-6z" class="k"></path><path d="M499 230c1 1 3 4 6 4 1 0 2 1 3 1l3 1c2 0 4 0 5 1h2v1h2 1c1 0 1 0 2-1l2 3c1 0 2 0 3-1h2v1c0 1 0 1-1 2l1 2h0c1 1 2 1 2 3l-2 1 1 1h1v2 1c1 0 1 1 1 2l-1 1h1c0 3 0 4-2 6-4 3-9 6-13 10h0c-4-4-8-6-11-9h-2l-1-1-1-4c1-2 1-3 1-4-2 0-5-1-7-2l-4-1c-1 0-3-1-3-2h-1-4l-1-1 1-2v-3c1-2 0-5 0-8l-1-5 1 1c1 1 2 1 2 3h1l1-1 2 2 2-1h2v-1c1-1 2-2 4-2z" class="M"></path><path d="M514 256c2 2 3 2 6 3l-1 1h0c-2-1-4-1-5-1h-1l-1-2c1 0 1-1 2-1z" class="S"></path><path d="M521 254l2 1-1 2-2 2c-3-1-4-1-6-3h0 1 4l1-1 1-1z" class="E"></path><path d="M525 249c0 1 1 2 2 2l1 1-1 1-1 1c-1 2-3 3-4 3l1-2-2-1v-4h1 2l1 1v-2z" class="n"></path><path d="M521 250h1 2l1 1c-1 2-1 3-2 4l-2-1v-4z" class="X"></path><path d="M516 248c1-1 1-1 2 0 2 0 3 1 3 2v4l-1 1-1 1h-4c0-1-1-2-1-3-1-2 0-3 1-5h1z" class="N"></path><path d="M516 248c1-1 1-1 2 0 2 0 3 1 3 2v4l-1 1c0-1 0 0-1-1-1-2 0-4-1-5l-2-1z" class="C"></path><path d="M509 243c0 2 0 3 1 4l2-1c1 0 2-1 3-1v2 1c-1 2-2 3-1 5 0 1 1 2 1 3h-1 0c-1 0-1 1-2 1l1 2-2 1h-1c-1-1-2-1-2-2h-1c-1 2 0 2 0 3v1h-2l-1-1-1-4c1-2 1-3 1-4l1-3c1-2 2-4 4-7z" class="U"></path><path d="M509 252l1-1c1 0 1 0 3 1 0 2 0 2 1 4h0c-1 0-1 1-2 1h0-1v2c-2-2-2-5-2-7z" class="f"></path><path d="M504 253l1-3h3l-1 2c-2 4-2 5-2 10l-1-1-1-4c1-2 1-3 1-4z" class="Z"></path><path d="M509 243c0 2 0 3 1 4l2-1c1 0 2-1 3-1v2 1c-1 2-2 3-1 5 0 1 1 2 1 3h-1c-1-2-1-2-1-4-2-1-2-1-3-1l-1 1h-2l1-2h-3c1-2 2-4 4-7z" class="b"></path><path d="M508 250c2-2 2-2 5-2 0 1-1 3 0 4-2-1-2-1-3-1l-1 1h-2l1-2z" class="V"></path><path d="M523 262c2-2 3-5 6-6h0v-2c0-1 1-1 2-2 0 1 1 2 1 3h1c0 3 0 4-2 6-4 3-9 6-13 10h0c-4-4-8-6-11-9v-1c0-1-1-1 0-3h1c0 1 1 1 2 2h1l2-1h1l6 3c2-1 2-1 3 0z" class="a"></path><path d="M513 259h1l6 3c2-1 2-1 3 0l-1 1c-1 1-3 2-4 2s-1-2-1-2c-1 1-1 1-2 1-2-1-3-2-4-4l2-1z" class="B"></path><path d="M516 237h2v1h2 1c1 0 1 0 2-1l2 3c1 0 2 0 3-1h2v1c0 1 0 1-1 2l1 2h0c1 1 2 1 2 3l-2 1h0-1l-1-2h-1c0 2 1 4 1 6l-1-1c-1 0-2-1-2-2v2l-1-1h-2-1c0-1-1-2-3-2-1-1-1-1-2 0h-1v-1-2c-1 0-2 1-3 1l-2 1c-1-1-1-2-1-4l2-1v-1h4v-1h-2v-1h1c1 0 2 0 3-1l-1-1z" class="I"></path><path d="M515 240h9c2 1 4 3 5 6 1 0 1 1 1 2h-1l-1-2h-1-1c0-1 0-1-1-2l-2-3h-8v-1z" class="R"></path><path d="M528 239h2v1c0 1 0 1-1 2l1 2h0c1 1 2 1 2 3l-2 1h0c0-1 0-2-1-2-1-3-3-5-5-6h1c1 0 2 0 3-1z" class="T"></path><path d="M516 237h2v1h2 1c1 0 1 0 2-1l2 3h-1-9-2v-1h1c1 0 2 0 3-1l-1-1z" class="e"></path><path d="M511 242h6l1 1h3c1 1 2 3 3 5h1v1 2l-1-1h-2-1c0-1-1-2-3-2-1-1-1-1-2 0h-1v-1-2c-1 0-2 1-3 1l-2 1c-1-1-1-2-1-4l2-1z" class="G"></path><path d="M515 245c2-1 3-1 4 0 2 0 3 2 5 3v2h-2-1c0-1-1-2-3-2-1-1-1-1-2 0h-1v-1-2z" class="E"></path><path d="M499 230c1 1 3 4 6 4 1 0 2 1 3 1l3 1c2 0 4 0 5 1l1 1c-1 1-2 1-3 1h-1v1h2v1h-4v1l-2 1c-2 3-3 5-4 7l-1 3c-2 0-5-1-7-2l-4-1c-1 0-3-1-3-2h-1-4l-1-1 1-2v-3c1-2 0-5 0-8l-1-5 1 1c1 1 2 1 2 3h1l1-1 2 2 2-1h2v-1c1-1 2-2 4-2z" class="H"></path><path d="M493 250l5-4h1v1l4 2-1 1-1 1h0c-1 1-3 0-4 0l-4-1zm10-10c1 0 2 1 3 1s2-1 3-2v-1c2 0 3 0 5 1h-1v1h2v1h-4c-2 0-2 0-4 1-1 1-1 0-3 1l1 1h1v1c-1 0-2 1-3 1h-1c0-1 1-3 0-4h0l1-2z" class="a"></path><path d="M499 230c1 1 3 4 6 4 1 0 2 1 3 1l3 1c2 0 4 0 5 1l1 1c-1 1-2 1-3 1-2-1-3-1-5-1v1c-1 1-2 2-3 2s-2-1-3-1l1-1h-2c0-1-1-2-2-3h-2l-1-3h-2v-1c1-1 2-2 4-2z" class="B"></path><path d="M497 233h1c1 0 1 1 2 2v1h-2l-1-3z" class="U"></path><path d="M500 235c2 0 3 2 4 2l1 2h-1-2c0-1-1-2-2-3v-1zm-16-6l1 1c1 1 2 1 2 3h1l1-1 2 2 2-1h2 2l1 3-1 1c1 1 2 2 1 3 0 2-1 3-2 4-2 0-2 0-4-1-1 2-1 3-3 4h-1-1c-1 0-1 0-2-1v-1-3c1-2 0-5 0-8l-1-5z" class="O"></path><path d="M487 234h0c1 1 1 2 2 3l-1 1c-1 0-1 0-2-1v-1l1-2z" class="g"></path><path d="M492 243l-2-2v-3c0-1 1-2 3-3 2 0 2 1 4 2 1 1 2 2 1 3 0 2-1 3-2 4-2 0-2 0-4-1z" class="d"></path><path d="M493 237h1l1 1-1 2h1 0l1 1-1 1s-1 0-1 1c-2-2-2-3-3-4l2-2z" class="F"></path><path d="M567 270h3c1 0 1 1 2 0 1 0 2 0 2-1 1 0 1 0 2-1l1 1c-1 1-1 1 0 2h0l1 1c1-1 2-1 3 0l1 1 1 1 2-1h0c1 1 2 1 2 1l1-1c1 2 0 3 1 5 0 1 0 1 1 2l-1 1c1 1 2 2 2 3-1 0-3 1-4 1s-2 1-3 1c-1 1-3 2-5 3h-1l-1 1-1-1c-1 0-3 1-3 1h-2c-1 0-1 1-2 2h0-3c-1-1-2-1-3-2v-1c-1 1-1 3-1 4l-5-1-1-1-5-1c-2 1-2 0-3 2v1h-1c-1 1-2 1-2 1l-1-1-2-1-1 1v2l-2 1-2 2c0 1 0 0 1 1h0l3-1v1c-1 1-1 2-2 3h0v3h-2v1l1 1-1 1c0-1-1-1-1-2h-2l-2 2-4-1 2-1-2-2v-1l-2-1v-2-2h-2c1-1 1-3 2-4h0v-1c0-1 0-2 1-3v-1-1c1-3 2-4 4-5v-1c3-2 6-3 8-5 1-1 2-2 3-2 2 0 3-1 4-2l2-1 2 2 4-2 1 1 2-2h0l2 1c1-1 2-2 4-2 1-1 1-1 2 0h1 1z" class="J"></path><path d="M548 272l2 2-2 1h-1l-1-2 2-1z" class="s"></path><path d="M530 293h1c0 1 1 2 2 2h1 1c-2 1-2 2-3 3-2 0-2 0-2-1v-3-1z" class="P"></path><path d="M530 293c-1-1-1-1-2-1h0c1-1 0-1 1-1 3-1 5-5 8-7 0 1 1 1 2 1h0v1h-1c-2 0-3 1-4 2-1 2-1 3-2 4l-1 1h-1z" class="X"></path><path d="M540 285h3l-3 3c-1 1-1 1-1 2l1 1c-1 0-1 1-3 1v1c-1 1-1 2-2 2h-1-1c-1 0-2-1-2-2l1-1c1-1 1-2 2-4 1-1 2-2 4-2h1v-1h1z" class="j"></path><path d="M539 290l1 1c-1 0-1 1-3 1v1c-1 1-1 2-2 2h-1-1c-1 0-2-1-2-2l1-1 1-1c2-1 4-1 6-1z" class="K"></path><path d="M557 271h0l2 1-3 2h-1l-1 1h0l2 1v-1h2l1-1h1l-1 2h2l1 1-4 2h-2l-1 1h-2c-1 0-2 1-2 2l-2-1h0-2v2l-2 1h-1c-1 1-1 0-1 1h-3c1-1 2-2 3-2l2-2c1 0 0 0 1-1h1 0 3l2-2h-1c-1 1-4 2-5 2l-1-1 3-3v-1l2-1 4-2 1 1 2-2z" class="G"></path><path d="M549 281h2c1-1 2-3 3-4h2l2 2h-2l-1 1h-2c-1 0-2 1-2 2l-2-1h0z" class="j"></path><path d="M537 293l1 1 3-1v2l-2 1-2 2c0 1 0 0 1 1h0l3-1v1c-1 1-1 2-2 3h0v3h-2v1l1 1-1 1c0-1-1-1-1-2h-2l-2 2-4-1 2-1-2-2v-1-2c1-2 2-2 3-3h1c1-1 1-2 3-3 1 0 1-1 2-2z" class="h"></path><path d="M538 299h0v1c0 1 0 1-1 2h-1l2-3z" class="k"></path><path d="M532 298c1-1 1-2 3-3-1 2-1 4-2 5h-2v-2h1z" class="E"></path><path d="M531 300h2c0 1 0 2 2 3h1v1h-1-3l-2-1 1-3z" class="K"></path><path d="M532 304h3l-1 2-2 2-4-1 2-1h1c0-1 0-1 1-2h0z" class="J"></path><path d="M528 303v-2c1-2 2-2 3-3v2l-1 3 2 1h0c-1 1-1 1-1 2h-1l-2-2v-1z" class="P"></path><path d="M567 270h3c1 0 1 1 2 0 1 0 2 0 2-1 1 0 1 0 2-1l1 1c-1 1-1 1 0 2-3 0-4 1-5 3-1 1-1 2-1 2h-2c-1 1-1 2-2 4l-2 2-1-1-3 1-3-1h-1l-1 1-1-1c-1 1-3 1-4 1 0-1 1-2 2-2h2l1-1h2l4-2-1-1h-2l1-2h-1l-1 1h-2v1l-2-1h0l1-1h1l3-2c1-1 2-2 4-2 1-1 1-1 2 0h1 1z" class="E"></path><path d="M563 270c1-1 1-1 2 0h1s-1 1-1 2l-1 1-1-1 1-2h-1z" class="X"></path><path d="M567 270h3c1 0 1 1 2 0 1 0 2 0 2-1 1 0 1 0 2-1l1 1c-1 1-1 1 0 2-3 0-4 1-5 3-1 1-1 2-1 2h-2-2c0 1-1 1-2 1h-1l2-2v-3h3l1-1c-1 0-2-1-3-1z" class="G"></path><path d="M570 271l1 2c-1 1-3 2-5 2v-3h3l1-1z" class="K"></path><path d="M551 282c1 0 3 0 4-1l1 1 1-1h1l3 1-3 1v2h2v1h1 1c1 1 0 2 1 3-1 1-1 3-1 4l-5-1-1-1-5-1c-2 1-2 0-3 2v1h-1c-1 1-2 1-2 1l-1-1-2-1-1 1-3 1-1-1v-1c2 0 2-1 3-1l-1-1c0-1 0-1 1-2l3-3c0-1 0 0 1-1h1l2-1v-2h2 0l2 1z" class="E"></path><path d="M556 289l-1-1 1-2h1l1 1-2 2z" class="G"></path><path d="M558 287h2l1 1-4 4-1-1v-2l2-2z" class="X"></path><path d="M551 282c1 0 3 0 4-1l1 1 1-1h1l3 1-3 1h-3c-1 2-1 2-2 3v-1c-1-1-2-2-3-2 0 0-1 0-1 1h-1v-2l1-1 2 1z" class="K"></path><path d="M577 271h0l1 1c1-1 2-1 3 0l1 1 1 1 2-1h0c1 1 2 1 2 1l1-1c1 2 0 3 1 5 0 1 0 1 1 2l-1 1c1 1 2 2 2 3-1 0-3 1-4 1s-2 1-3 1c-1 1-3 2-5 3h-1l-1 1-1-1c-1 0-3 1-3 1h-2c-1 0-1 1-2 2h0-3c-1-1-2-1-3-2v-1c-1-1 0-2-1-3h-1-1v-1h-2v-2l3-1 3-1 1 1 2-2c1-2 1-3 2-4h2s0-1 1-2c1-2 2-3 5-3z" class="I"></path><path d="M578 288h-1v-3h3v1l-2 2z" class="l"></path><path d="M589 281h0c1 1 2 2 2 3-1 0-3 1-4 1s-2 1-3 1c-1 1-3 2-5 3h-1v-1l2-2v1h1c1-1 1-1 2-1h1l1-1-1-1v-1c2 0 2 0 3-1l2-1z" class="h"></path><path d="M571 276v3c-2 1-2 2-3 4h-1l-4 2v1h2l-1 2c1 0 2 1 2 2h-3v-1c-1-1 0-2-1-3h-1-1v-1h-2v-2l3-1 3-1 1 1 2-2c1-2 1-3 2-4h2z" class="n"></path><path d="M561 282l3-1 1 1c-1 1-2 2-4 2l-1 1h-2v-2l3-1z" class="j"></path><path d="M574 282h1 1c1-1 3-2 4-4l1 2v2h3l-1 1v1c-1 0-1 1-2 1l-1-1h0l1-2h-1c-1 0-2 1-3 1h-2l-1 1c1 0 1 1 1 1-1 1-1 1-1 2v1l-1 1h-2c0-1 0-1 1-2h-1-1-2-1v-1l1-1-1-2h1c1-2 1-3 3-4h0c1 2 1 3 0 5v2h1s1-1 1-2c0 0 1-1 1-2z" class="Y"></path><path d="M577 271h0l1 1c1-1 2-1 3 0l1 1 1 1 2-1h0c1 1 2 1 2 1l1-1c1 2 0 3 1 5 0 1 0 1 1 2l-1 1h0l-2 1c-1 1-1 1-3 1l1-1h-1-3v-2l-1-2c-1 2-3 3-4 4h-1-1c0 1-1 2-1 2 0 1-1 2-1 2h-1v-2c1-2 1-3 0-5h0v-3s0-1 1-2c1-2 2-3 5-3z" class="x"></path><path d="M575 277c0-1 1-2 1-2 2 0 2 0 3 1h-1l-3 1zm-4-1s0-1 1-2c0 1 0 2 1 3v1l-2 1h0v-3zm16 6l-2-3h1 0c2 0 2 1 3 2l-2 1zm-5-9l1 1 2-1h0c1 1 2 1 2 1 0 1-1 1-2 2v-1c-2-1-2 1-4 1l-1-1 2-2z" class="E"></path><path d="M578 276l1 1c-1 1-2 2-3 2h0c-1 1-2 2-2 3s-1 2-1 2c0 1-1 2-1 2h-1v-2c1-2 1-3 0-5l2-1h0l2-1 3-1z" class="d"></path><path d="M588 289c1-1 1-2 1-2 1 0 1 0 1 1h1 1v-2c2 0 4-1 6-1h1l1 1v2l-1 2 2 1v-1h4l1 1-1 1c-1 0-1 0-2-1l-1 1h1v1 1c-2 1-3 1-4 3-1 1-3 1-4 2-1 2-1 3-3 4-1 1-2 1-2 2 0 2-1 3-2 5 0-2 0-3-1-4h0l-3 2v1l-1 2h0l1 2 2 1c0 1 0 1-1 2v3l-1 1 1 1v1c-2 1-3 1-4 2-1-1-1 0-2-1l-2 1-1-1h-1l-2 3-5-3-4-2-1-1-1-1c1-1 1-2 1-3h-1c-1 2-2 2-3 4h-1c-1 0-1 1-2 1s-1-1-1-2c-1 0-2 1-3 1l-1-1-2-2c0-1-2-3-3-3 1-1 1-2 2-2l-1-2-2 1c-1-1-1-2-1-3-1 0-2 0-3 1l-1-1v-2l-1-1v-3h0c1-1 1-2 2-3v-1l-3 1h0c-1-1-1 0-1-1l2-2 2-1v-2l1-1 2 1 1 1s1 0 2-1h1v-1c1-2 1-1 3-2l5 1 1 1 5 1c0-1 0-3 1-4v1c1 1 2 1 3 2h3 0c1-1 1-2 2-2h2s2-1 3-1l1 1 1-1h1c2-1 4-2 5-3 1 2 2 3 3 4l1-1z" class="Y"></path><path d="M578 294c1 0 1 0 2 1l-1 2h-1l-1-1 1-2z" class="k"></path><path d="M578 289h1v2h2c-2 1-2 1-3 3h0c-1-1-1-1-1-2-1-1 0-1 0-2l1-1z" class="x"></path><path d="M591 294l2 2h0c-1 1-1 1-1 3h0l-1 1c-1-1-2-1-3-2 2-1 2-2 3-3h0v-1z" class="Q"></path><path d="M579 289c2-1 4-2 5-3 1 2 2 3 3 4l1-1v1 1h0c1 1 1 2 1 3h1l1-1v1 1c-3 1-3 0-6 0h-1-1c-1-1-2-1-2-1 0-1 0-2 1-3h0-1-2v-2z" class="b"></path><path d="M579 289c2-1 4-2 5-3 1 2 2 3 3 4h-2c-1 0-2 0-3 1h0-1-2v-2z" class="E"></path><path d="M580 299c0-1 0-2 1-2s1 0 2 1l2-1c1 1 1 1 2 1h0l-1 1s-1 0-1 1v1c2 0 4 0 6-1-1 1-3 4-4 4h-2l-1 1c2 0 2 0 3 1l-3 2v1l-1 2h0c-1 0 0 0-1-1-1 0-2-1-4-2v-2c0-1 1 0 2 0v-7z" class="I"></path><path d="M584 305c2 0 2 0 3 1l-3 2c-1-1-1-1-1-2l1-1z" class="n"></path><path d="M569 296c1-1 2-3 4-3 1 1 1 3 2 4l1 1 4 1v7c-1 0-2-1-2 0v2c-1 2-3 4-4 4l-1 1v-1l1-1c-1-1-1-1-3-2 0-2 1-4 1-6v-2-2l-1-1-2 2-1-1v-1l1-2h0z" class="K"></path><path d="M576 298l4 1v7c-1 0-2-1-2 0l-2-1h1 1v-1-1c1-1 0-1 0-2h-2 0-1l1-3z" class="Q"></path><path d="M569 296c1-1 2-3 4-3 1 1 1 3 2 4 0 2-1 3-1 4l2 1 1 1-1 1v2h-1c-3-2-2-2-3-5v-2l-1-1-2 2-1-1v-1l1-2h0z" class="X"></path><path d="M588 289c1-1 1-2 1-2 1 0 1 0 1 1h1 1v-2c2 0 4-1 6-1h1l1 1v2l-1 2 2 1v-1h4l1 1-1 1c-1 0-1 0-2-1l-1 1h1v1 1c-2 1-3 1-4 3-1 1-3 1-4 2-1 2-1 3-3 4-1-1-1-1-1-2l2-1c1-3 5-4 5-6v-1c-2 1-3 1-4 1l-1 2-2-2v-1l-1 1h-1c0-1 0-2-1-3h0v-1-1z" class="K"></path><path d="M590 289h1l1 1c1-1 1-1 1-2 1 0 1 1 2 2l-1 1h0c-1 0-2 0-3 1 0-1 0-2-1-3z" class="n"></path><path d="M599 285l1 1v2l-1 2c-2 0-2 0-3-1l1-1h1v-2l1-1z" class="b"></path><path d="M590 289c1 1 1 2 1 3v1l-1 1h-1c0-1 0-2-1-3h0v-1c1 0 1-1 2-1z" class="Q"></path><path d="M563 289v1c1 1 2 1 3 2l1 1c1 0 1 1 2 2v1h0l-1 2v1l1 1 2-2 1 1v2 2c-1 1-1 2-3 2v-1c-1 0-3-1-5-1v2c-1-1-1-2-2-3 0-1 0-1-1-2s-2-1-4-1c1-1 1-1 1-2s0-2-1-4h-2c-2 0-3-1-5-2l1-1 5 1 1 1 5 1c0-1 0-3 1-4z" class="U"></path><path d="M567 293c1 0 1 1 2 2v1h0l-1 2v1l-3-4 2-2z" class="b"></path><path d="M563 289v1c1 1 2 1 3 2l1 1-2 2-3-2c0-1 0-3 1-4z" class="G"></path><path d="M557 293c2 1 4 1 5 3l1 1c0 1 0 0 1 1v3 2 2c-1-1-1-2-2-3 0-1 0-1-1-2s-2-1-4-1c1-1 1-1 1-2s0-2-1-4z" class="h"></path><path d="M572 303c0 2-1 4-1 6 2 1 2 1 3 2l-1 1v1 2l1 1-2 2v1h0c0 1-1 1-1 1l-1 1-2 2-4-2-1-1-1-1c1-1 1-2 1-3 2-2 1-6 2-8s1-2 2-3c1 0 2 0 2-1v1c2 0 2-1 3-2z" class="H"></path><path d="M568 312v-1c0-1 0-1 1-2l2 1v1c-1 1-2 1-3 1z" class="F"></path><path d="M571 311c0 3-1 4-2 6-1 0-1 0-1-1v-2-2c1 0 2 0 3-1z" class="C"></path><path d="M571 309c2 1 2 1 3 2l-1 1v1 2l1 1-2 2v1h0c0 1-1 1-1 1v-1l-1-1h-1v-1c1-2 2-3 2-6v-1-1z" class="P"></path><path d="M563 316c2-2 1-6 2-8s1-2 2-3c1 3 1 4 0 7l-1 1c0 1 1 2 2 3 0 1 0 1 1 1v1h1l1 1v1l-1 1-2 2-4-2-1-1-1-1c1-1 1-2 1-3z" class="U"></path><path d="M569 318h1l1 1v1l-1 1-2 2-4-2-1-1v-1h3l3-1z" class="j"></path><path d="M569 318h1l1 1v1l-1 1c-2-1-3-1-4-2l3-1z" class="c"></path><path d="M574 312c1 0 3-2 4-4 2 1 3 2 4 2 1 1 0 1 1 1l1 2 2 1c0 1 0 1-1 2v3l-1 1 1 1v1c-2 1-3 1-4 2-1-1-1 0-2-1l-2 1-1-1h-1l-2 3-5-3 2-2 1-1s1 0 1-1h0v-1l2-2-1-1v-2l1-1z" class="I"></path><path d="M576 317l1-2c1 0 1 0 3 1 0 1 0 2 1 3h-1l-1 1c-2-1-2-2-3-3z" class="t"></path><path d="M574 312c1 0 3-2 4-4 2 1 3 2 4 2 1 1 0 1 1 1l1 2c-1 0-3-2-5-3-1 0-2 0-2 1l1 2-1 1c-1 0-2-1-3-2z" class="Q"></path><path d="M576 317h0c1 1 1 2 3 3l1-1h1l-1 1c2 0 2-2 4-1v1l1 1v1c-2 1-3 1-4 2-1-1-1 0-2-1l-2 1-1-1h0c0-1 0-4-1-5h0l1-1z" class="l"></path><path d="M573 315h3v2l-1 1h0c1 1 1 4 1 5h0-1l-2 3-5-3 2-2 1-1s1 0 1-1h0v-1l2-2-1-1z" class="Y"></path><path d="M557 299c2 0 3 0 4 1s1 1 1 2c1 1 1 2 2 3v-2c2 0 4 1 5 1 0 1-1 1-2 1-1 1-1 1-2 3s0 6-2 8h-1c-1 2-2 2-3 4h-1c-1 0-1 1-2 1s-1-1-1-2c-1 0-2 1-3 1l-1-1-2-2c0-1-2-3-3-3 1-1 1-2 2-2h1c2-1 3-2 4-3l-1-2 1-1c0-1 0-2 1-3s1-2 2-3h0l1-1z" class="M"></path><path d="M553 306c0-1 0-2 1-3l2 4c-1 0-2 0-3-1z" class="P"></path><path d="M564 303c2 0 4 1 5 1 0 1-1 1-2 1-1 1-1 1-2 3s0 6-2 8h-1-1-3v-1c2 0 3 0 4-1 1-2 1-6 2-9v-2z" class="i"></path><path d="M557 299c2 0 3 0 4 1s1 1 1 2l-1 2 1 2h-1c-1 0-2-1-3-1l-2-1v-4l1-1z" class="T"></path><path d="M552 307l1-1c1 1 2 1 3 1 1 1 2 2 3 4-1 0-1 2-1 2 0 2-1 3-3 4v1 1c-1 0-2 1-3 1l-1-1-2-2c0-1-2-3-3-3 1-1 1-2 2-2h1c2-1 3-2 4-3l-1-2z" class="j"></path><path d="M554 313c1-1 0-1 2-1l1 1c-1 2-1 1-2 2l-1-2z" class="J"></path><path d="M554 313l1 2-1 1 1 2v1c-1 0-2 1-3 1l-1-1c0-2 2-4 3-6z" class="c"></path><g class="n"><path d="M552 307l1-1c1 1 2 1 3 1 1 1 2 2 3 4-1 0-1 2-1 2v-2c-1-1-1-1-2-1l-3 3c-2 0-2 0-4-1 2-1 3-2 4-3l-1-2z"></path><path d="M547 293h1v-1c1-2 1-1 3-2l-1 1c2 1 3 2 5 2h2c1 2 1 3 1 4s0 1-1 2l-1 1h0c-1 1-1 2-2 3s-1 2-1 3l-1 1 1 2c-1 1-2 2-4 3h-1l-1-2-2 1c-1-1-1-2-1-3-1 0-2 0-3 1l-1-1v-2l-1-1v-3h0c1-1 1-2 2-3v-1l-3 1h0c-1-1-1 0-1-1l2-2 2-1v-2l1-1 2 1 1 1s1 0 2-1z"></path></g><path d="M545 294s1 0 2-1l-1 5h-1c-1-2-1-2 0-4zm5-3c2 1 3 2 5 2v1c0 1-1 1-2 2-2 0-2 0-4-1 0-1 1-2 1-4z" class="K"></path><path d="M552 299l1-2c1 0 2 0 3-1s1 0 2 0v1c0 1 0 1-1 2l-1 1h0c-1 0-1 1-2 1s-1 0-2-1v-1z" class="p"></path><path d="M541 298c1-1 1-1 2-1 1 1 1 0 1 1l1 1 2-2c1 0 2 0 4 1l1 1v1h-2c-2-1-4 0-6 0l-1 1c-1 0-2-1-2-2v-1z" class="k"></path><path d="M541 299c0 1 1 2 2 2l1-1c2 0 4-1 6 0h2c1 1 1 1 2 1s1-1 2-1c-1 1-1 2-2 3s-1 2-1 3l-1 1 1 2c-1 1-2 2-4 3h-1l-1-2-2 1c-1-1-1-2-1-3-1 0-2 0-3 1l-1-1v-2l-1-1v-3h0c1-1 1-2 2-3z" class="L"></path><path d="M545 304c1-1 2-1 3-1l1 1c-1 1-2 1-3 2l-1 1-1-1 1-2z" class="R"></path><path d="M546 306c1-1 2-1 3-2v1c0 1 1 2 1 3l-1 1h0c-1-1-1-1-1-3h-2z" class="V"></path><path d="M545 304v-1h-1l-1-1c2-1 2-1 3-1l1 1c1 1 2-1 2 2h0l-1-1c-1 0-2 0-3 1z" class="m"></path><path d="M539 302c2 1 2 1 3 3 0 1-1 1-1 2h1c2 1 1 0 2 1-1 0-2 0-3 1l-1-1v-2l-1-1v-3z" class="I"></path><path d="M550 300h2c1 1 1 1 2 1s1-1 2-1c-1 1-1 2-2 3s-1 2-1 3l-1 1c-1-1-1-2-3-2v-1h0 2v-1h-1v-3z" class="Q"></path><path d="M418 285h1c1-2 2-2 4-2l1 1 2 1-1 2c-2 1-2 1-3 3l-1 1 1 1c2 0 4 0 5 2h-1l4 2-4 5c4 3 9 7 11 12 2 3 4 9 5 13h-1 0c-2-5-4-10-8-14-20 1-40 12-54 26-9 10-17 21-24 32l-8 15v-1c-1-3 10-21 12-25 2-2 3-4 4-5l-2-2c2-1 2-3 4-4l-2-3v-1c1-1 1-2 1-2h0l1-1 2-3v-1h-1l-1 1c-1 0-2 0-3-1l2-2 3-2h0v-1-1c1 0 1-1 2-1 1-1 1-1 2-1 0 0-1-1 0-1v-1c1 0 0-2 0-2h1c1-1 2-2 3-2h2c1-1 1-1 2-1v-2l-1-1s1-1 1-2c1 0 1-1 2-1h2v-1c2-2 2-1 3-1l1-1h2c1-1 2-2 3-2 0 0 1-1 2-1h-2c0-1 0-1-1-2h0l2-2h1l3-3h1c1 0 3-1 4-1l-2-2v-1h2l1 2h2l1-1c0-1 1-2 2-3-1-1 0-1-1-2 0-2-1-2-3-3 2-1 2-1 4-1l1-1c1-1 1-2 3-2 1-1 2-2 3-2s2-1 3-1z" class="J"></path><path d="M386 325l2-1h1 1l2 1c-2 1-4 3-5 4l-3-3 2-1z" class="I"></path><path d="M364 342l2 2 4-1 3-1-10 12-2-2c2-1 2-3 4-4l-2-3v-1c1-1 1-2 1-2h0z" class="k"></path><path d="M363 344l3 1c-1 1-1 2-1 3l-2-3v-1z" class="V"></path><path d="M384 326l3 3c-5 4-10 8-14 13l-3 1-4 1-2-2 1-1 2-3v-1h-1l-1 1c-1 0-2 0-3-1l2-2 3-2h0v-1-1c1 0 1-1 2-1 1-1 1-1 2-1l2-1 1 1h3l1-1 1-1c1 1 1 1 0 1l1 1 4-3z" class="l"></path><path d="M367 338l2 1-1 1s-1 1-1 2c-1 0-1-1-2-1l2-3z" class="r"></path><path d="M378 328l1-1c1 1 1 1 0 1l1 1 1 2h-1l-1-1c-2 1-3 2-4 2l-3 1c2-1 3-2 5-4l1-1z" class="Q"></path><path d="M373 328l1 1h3c-2 2-3 3-5 4l-1 2h0c-1-1-1-1-2-1v2 2 1h0l-2-1v-1h-1l-1 1c-1 0-2 0-3-1l2-2 3-2h0v-1-1c1 0 1-1 2-1 1-1 1-1 2-1l2-1z" class="Y"></path><path d="M373 328l1 1c-2 1-5 4-6 5l-1 1h-3l3-2h0v-1-1c1 0 1-1 2-1 1-1 1-1 2-1l2-1z" class="G"></path><defs><linearGradient id="s" x1="395.263" y1="325.065" x2="400.162" y2="311.724" xlink:href="#B"><stop offset="0" stop-color="#484748"></stop><stop offset="1" stop-color="#656462"></stop></linearGradient></defs><path fill="url(#s)" d="M389 313l-1 1v1c1 0 1 0 2-1l2 1c1 0 2-1 3-1 1-1 0-2 1-2l1 1 1 1c2-1 2-1 3-1 1-1 2-1 3-1v1h2v4c-5 2-10 5-14 8l-2-1h-1-1l-2 1-2 1-4 3-1-1c1 0 1 0 0-1l-1 1-1 1h-3l-1-1-2 1s-1-1 0-1v-1c1 0 0-2 0-2h1c1-1 2-2 3-2h2c1-1 1-1 2-1v-2l-1-1s1-1 1-2c1 0 1-1 2-1h2v-1c2-2 2-1 3-1l1-1h2z"></path><path d="M389 313l-1 1v1c1 0 1 0 2-1l2 1c1 0 2-1 3-1 1-1 0-2 1-2l1 1c-1 2-1 2-3 2l-1 1c-1 0-2 0-2 1l-1-1-1 1v1h1l2-1c0 1 1 1 1 2s0 1-1 2c-1 0-3 0-4 1-2-1-2-1-4-1v-1-1c-2 0-2 0-3 1 0 1 0 1-2 2h0v-2l-1-1s1-1 1-2c1 0 1-1 2-1h2v-1c2-2 2-1 3-1l1-1h2z" class="Q"></path><path d="M381 316h2v-1c2-2 2-1 3-1 0 1-1 2-1 3 1 1 2-1 2 1l-3 2v-1c-2 0-2 0-3 1 0 1 0 1-2 2h0v-2l-1-1s1-1 1-2c1 0 1-1 2-1z" class="n"></path><path d="M379 320c1-1 2-1 4-2v-1l1 1v1c-2 0-2 0-3 1 0 1 0 1-2 2h0v-2z" class="E"></path><path d="M379 322h0c2-1 2-1 2-2 1-1 1-1 3-1v1 1c2 0 2 0 4 1 1-1 3-1 4-1-1 1-1 2-2 3h-1-1l-2 1-2 1-4 3-1-1c1 0 1 0 0-1l-1 1-1 1h-3l-1-1-2 1s-1-1 0-1v-1c1 0 0-2 0-2h1c1-1 2-2 3-2h2c1-1 1-1 2-1z" class="Y"></path><path d="M373 328c1-1 2-1 2-2 2 1 2 1 3 2l-1 1h-3l-1-1z" class="P"></path><path d="M384 321c2 0 2 0 4 1-1 1-2 1-3 2l-1-1h-1-1l2-2z" class="I"></path><path d="M388 322c1-1 3-1 4-1-1 1-1 2-2 3h-1-1l-2 1-1-1c1-1 2-1 3-2z" class="K"></path><path d="M402 302c1 0 1 0 1 1l1 1c1-1 1-1 2-1h1c1 0 1-1 2-1h3c1 1 2 1 4 1h0s1 1 2 1h1v1h-1v2h2c2 2 3 2 6 2l2 1c-8 1-15 3-22 7v-4h-2v-1c-1 0-2 0-3 1-1 0-1 0-3 1l-1-1-1-1c-1 0 0 1-1 2-1 0-2 1-3 1l-2-1c-1 1-1 1-2 1v-1l1-1c1-1 2-2 3-2 0 0 1-1 2-1h-2c0-1 0-1-1-2h0l2-2h1l3-3h1c1 0 3-1 4-1z" class="I"></path><path d="M412 302c1 1 2 1 4 1h0s1 1 2 1h1v1h-1 0c-2 1-4 0-5 0-1 1-2 0-2 0 0-1 0-2 1-3z" class="b"></path><path d="M404 312c1-1 1-2 3-3l1 1 1-1-1-1 1-1c1 1 1 1 2 1h2 0c0 1 0 1-1 2h-2c-1 1 0 2-2 2h-1l-1 1h-2v-1z" class="l"></path><path d="M403 303l1 1c1-1 1-1 2-1h1c-1 1-1 3-3 4l-1-1h-1c-2 2-5 2-7 3h0c0-1 1-1 1-2 1-3 3-1 5-2l2-2z" class="Y"></path><path d="M402 302c1 0 1 0 1 1l-2 2c-2 1-4-1-5 2 0 1-1 1-1 2l-1 1h-2c0-1 0-1-1-2h0l2-2h1l3-3h1c1 0 3-1 4-1z" class="p"></path><path d="M418 285h1c1-2 2-2 4-2l1 1 2 1-1 2c-2 1-2 1-3 3l-1 1 1 1c2 0 4 0 5 2h-1l4 2-4 5c4 3 9 7 11 12v1 1c1 1 1 1 1 2-1-1-2-4-4-4-1-1-1-1-2-3-2 0-3 1-4 0l-2-1c-3 0-4 0-6-2h-2v-2h1v-1h-1c-1 0-2-1-2-1h0c-2 0-3 0-4-1h-3c-1 0-1 1-2 1h-1c-1 0-1 0-2 1l-1-1c0-1 0-1-1-1l-2-2v-1h2l1 2h2l1-1c0-1 1-2 2-3-1-1 0-1-1-2 0-2-1-2-3-3 2-1 2-1 4-1l1-1c1-1 1-2 3-2 1-1 2-2 3-2s2-1 3-1z" class="s"></path><path d="M415 294h3 8l4 2-4 5c-4-3-8-4-13-6h0l2-1z" class="H"></path><path d="M418 302l2-1c4 1 8 6 12 9-2 0-3 1-4 0l-2-1c-3 0-4 0-6-2h-2v-2h1v-1h-1c-1 0-2-1-2-1l2-1z" class="K"></path><path d="M420 307l1-1-1-1c1 0 1 0 2 1l4 3c-3 0-4 0-6-2z" class="h"></path><path d="M408 297l2 1 1-1c1 0 4 0 5 1 0 1 1 1 1 1l2 1-1 1v1l-2 1h0c-2 0-3 0-4-1h-3c-1 0-1 1-2 1h-1c-1 0-1 0-2 1l-1-1c0-1 0-1-1-1l-2-2v-1h2l1 2h2l1-1c0-1 1-2 2-3z" class="P"></path><path d="M411 297c1 0 4 0 5 1 0 1 1 1 1 1-2 0-2 0-3 2-2-1-2-2-3-4z" class="c"></path><path d="M408 297l2 1v2c-1 2-3 1-5 1l1-1c0-1 1-2 2-3z" class="G"></path><path d="M418 285h1c1-2 2-2 4-2l1 1 2 1-1 2c-2 1-2 1-3 3l-1 1 1 1c2 0 4 0 5 2h-1-8-3v-1h-1-4v1l-1-1v-1c1 0 1 0 2-1 1 0 2-1 3-2l-2-1c1-1 2-2 3-2s2-1 3-1z" class="u"></path><path d="M424 284l2 1-1 2c-2 1-2 1-3 3l-1 1 1 1c2 0 4 0 5 2h-1-8-3v-1c2-1 3-1 5-2h0l-1-2-1 2-1-1h-1c2-2 6-4 8-6z" class="J"></path><path d="M326 349c2 0 2 0 3 1 1 0 1 1 0 2v7c0-1 1-1 1 0-9 19-14 38-15 60-2 17-1 35 5 51l2 6 8 19c0 1 0 1-1 2-2 1-6 0-9-1 1 1 2 3 2 4v1h-3l-1 1c-2 0-4 2-7 2h0l-1 2v-2l-1-2h-1 0l-1 1h-1l-1-1c1-2 2-4 2-7v-1l1-1v-1c-1-1 0-2 0-3-1-1-1 0-1-1v-2h-1v-1c-1-3-2-4-3-5l-3-1h-4l2-3c-1-1-1-1-1-2h-2v-1l5-4h5 0v-1l1-2v-3c-1 0-1-1-1-1v-1c-1-3-1-6-2-9l-1-13c-1-5 0-10 0-15 0-2-1-4 0-5s0-4 1-5l1 2c1-13 4-27 9-39l7-18h0l2-3 3-5 1-2z" class="N"></path><path d="M322 476l8 19c0 1 0 1-1 2-2 1-6 0-9-1-1-3-2-5-4-8l-4-10c2 2 2 4 4 6 2 1 2 2 3 4l1 2c1 2 1 4 4 4 1 1 2 1 4 0h0l-1-1v-3c-1-2-3-4-3-6-1-2-1-3-2-4v-3-1z" class="H"></path><path d="M305 461c-1-3-1-6-2-9l-1-13c-1-5 0-10 0-15 0-2-1-4 0-5s0-4 1-5l1 2h0c0 12-1 22 0 34 1 3 1 6 2 9 0 2 2 5 1 7v1l2 6-2-1h-1-3v-1l2-1v-1-1l1-2v-3c-1 0-1-1-1-1v-1z" class="S"></path><path d="M309 473l-2-6v-1l5 12h0l4 10c2 3 3 5 4 8 1 1 2 3 2 4v1h-3l-1 1c-2 0-4 2-7 2h0l-1 2v-2l-1-2h-1 0l-1 1h-1l-1-1c1-2 2-4 2-7v-1l1-1v-1c-1-1 0-2 0-3-1-1-1 0-1-1v-2h-1v-1c-1-3-2-4-3-5l-3-1h-4l2-3c-1-1-1-1-1-2h-2v-1l5-4h5 0v1l-2 1v1h3 1l2 1z" class="Q"></path><path d="M310 477c2 3 2 5 3 8l-1 1-2-2h1c-1-1 0-1-1-2h-1c0-2 0-3 1-5z" class="Z"></path><path d="M303 480c2 0 3 0 4 2 0 0 1 1 1 2h2l2 2 1-1h1c0 2 1 3 1 4l-1 1v1s1 1 1 2h-5 0c-1-1-1-2-1-3l-1-1c-1-1-1 0-1-1v-2h-1v-1c-1-3-2-4-3-5z" class="m"></path><path d="M313 485h1c0 2 1 3 1 4l-1 1v1s1 1 1 2h-5 0c-1-1-1-2-1-3 2 0 2 0 3-1 0-1-1-2-1-2h-1l-1-1h3l1-1z" class="l"></path><path d="M309 490c2 0 2 0 3-1l1 1c0 2-2 2-3 3h0c-1-1-1-2-1-3z" class="Z"></path><path d="M295 474v-1l5-4h5 0v1l-2 1v1h3 1l2 1v2l-1 1c1 1 1 0 2 1-1 2-1 3-1 5h1c1 1 0 1 1 2h-1-2c0-1-1-2-1-2-1-2-2-2-4-2l-3-1h-4l2-3c-1-1-1-1-1-2h-2z" class="T"></path><path d="M298 476c2 0 2 1 4 0v2l-2 1h-4l2-3z" class="q"></path><path d="M307 472l2 1v2l-1 1c1 1 1 0 2 1-1 2-1 3-1 5h1c1 1 0 1 1 2h-1-2c0-1-1-2-1-2-1-2-2-2-4-2l-3-1 2-1v-2h1c0 1 0 1 1 2h1c0-2 1-3 2-6z" class="f"></path><path d="M318 499h2 0c0-2-1-2-2-4s-2-4-2-7c2 3 3 5 4 8 1 1 2 3 2 4v1h-3l-1 1c-2 0-4 2-7 2h0l-1 2v-2l-1-2h-1 0l-1 1h-1l-1-1c1-2 2-4 2-7v-1l1-1v-1c-1-1 0-2 0-3l1 1c0 1 0 2 1 3h0 5l1 2 2 2v2z" class="Y"></path><path d="M311 494c2 2 2 1 2 4l-1 1c-2 0-2 0-3-1 0-2 0-3 2-4z" class="B"></path><path d="M316 495l2 2v2c-1 0-2 0-2 1v1l-1 1c-2 0-2 1-3 0h0c1-2 2-3 3-4l1-3z" class="L"></path><path d="M315 493l1 2-1 3v-2h-1s0 1-1 1v1c0-3 0-2-2-4l-1-1h0 5z" class="I"></path><path d="M534 306h2c0 1 1 1 1 2l1-1-1-1v-1h2l1 1v2l1 1c1-1 2-1 3-1 0 1 0 2 1 3l2-1 1 2c-1 0-1 1-2 2 1 0 3 2 3 3l2 2 1 1c1 0 2-1 3-1 0 1 0 2 1 2s1-1 2-1h1c1-2 2-2 3-4h1c0 1 0 2-1 3l1 1 1 1 4 2 5 3 3 2 1 1 1 1c0 2-1 2-2 3 3 2 5 5 5 9 0 0-1 1-1 2v1 1l-2 2c-2 2-4 3-7 4v1l-6 1-3 1h-1-5l-1 2-1 1h1l1 2c-1 0-1 1-1 1h-2l-1 1h-2l1 2c0 2 0 2-1 3h-2 0c-1 0-1-1-1-2l-5-1-3-5h-1l-11 1c-1-1-2-1-3-2h-1v-1l1-1v-1c0-1 0-2-1-3l-1 1c-1-1-1-1-1-2 1-1 1-3 2-5v-5-1-18-7-4l1-1c0-1 1-2 1-4l2 1h1l4 1 2-2z" class="q"></path><path d="M544 329l2 1c-1 1-1 2-2 3 0-1-1-1-1-2s1-1 1-2z" class="S"></path><path d="M544 329c1-2 1-1 3-1 0 0 2-2 3-2 0 1 1 1 1 2l-1 1c-1 0 0 0-1 1v1c0 1-1 1-1 2-1-1-1-2-2-3l-2-1z" class="f"></path><path d="M534 306h2c0 1 1 1 1 2l1-1-1-1v-1h2l1 1v2l1 1c1-1 2-1 3-1 0 1 0 2 1 3l2-1 1 2c-1 0-1 1-2 2-2-3-7-4-11-5-1-1-2-1-3-1h0l2-2z" class="K"></path><path d="M546 330c1 1 1 2 2 3 0-1 1-1 1-2 2 0 2 0 3 2l-2 3h0l-2 3-2-1c1 1 1 1 1 3h-1l-1-1h0l-1 4c-2 1-2 0-3 0v-1c0-1 1-1 1-2-1-2-1-1 0-2 1 0 1 0 1-1v-1c-1 0-2 0-3-1 1-2 2 0 3-1 0 0 1-1 1-2 1-1 1-2 2-3z" class="V"></path><path d="M547 334c1 1 2 1 3 2h0l-2 3-2-1v-2l1-1v-1z" class="f"></path><path d="M549 331c2 0 2 0 3 2l-2 3c-1-1-2-1-3-2h0l1-1c0-1 1-1 1-2z" class="U"></path><path d="M546 338l2 1c0 4-1 7 1 11l2 3c-1 1-2 2-3 2h-1 0c-1-1-1-2-2-3 0-2-1-3-1-4l-1-1-1 2v1c-2-1-2-2-2-4l-1-1c1-1 0-1 1-2h0c0-1 0-1-1-2h0 3c0 1-1 1-1 2v1c1 0 1 1 3 0l1-4h0l1 1h1c0-2 0-2-1-3zm-21-32l2 1h1l4 1h0v1l2 1h3 2l1 2h-1c0 1-1 1-1 1v1h0c-1 1-1 1-1 2h1l1 1h1c0-1 0-1 1-1v1 1l2 2 1-1v2c1 0 2 0 4 1h0c-1 1-2 1-4 1-2-2-3-4-5-4-1 0-3-1-4-2l-7-2c-1 1-2 0-4 0h-1v-4l1-1c0-1 1-2 1-4z" class="R"></path><path d="M525 306l2 1h1l4 1h0v1l2 1h-1c-1 0-1 1-2 1l-1 2 2 2c-2 0-2-1-4 0-1 1-2 0-4 0h-1v-4l1-1c0-1 1-2 1-4z" class="b"></path><path d="M525 306l2 1c-1 1-1 1-1 2l1 4c-1 0-2 0-2 1-1 1-1 0-1 1h-1v-4l1-1c0-1 1-2 1-4z" class="G"></path><path d="M562 316h1c0 1 0 2-1 3l1 1 1 1 4 2 5 3 3 2 1 1 1 1c0 2-1 2-2 3-1-2-3-3-5-4-5-1-9-2-13 0-2 1-4 2-6 4-1-2-1-2-3-2v-1c1-1 0-1 1-1l1-1c0-1-1-1-1-2l-1-1 1-1v-1l-1-1 1-2c-1-1-1-2-1-3l2 2 1 1c1 0 2-1 3-1 0 1 0 2 1 2s1-1 2-1h1c1-2 2-2 3-4z" class="T"></path><path d="M562 324h2c1 1 1 1 2 1v1l-1 1h-1c-1-1-1-2-2-3z" class="H"></path><path d="M562 316h1c0 1 0 2-1 3l1 1 1 1c-1 1-1 2-3 3h1 0c1 1 1 2 2 3h1-4c-1 1-3 1-4 1-1-1-1-1-1-2 1-1 1-1 3-2h0l-1-1h-2v-2c1 0 1-1 2-1h1c1-2 2-2 3-4z" class="B"></path><path d="M538 342l1-1c1 1 1 1 1 2h0c-1 1 0 1-1 2l1 1c0 2 0 3 2 4v-1l1-2 1 1c0 1 1 2 1 4 1 1 1 2 2 3h0 1c1 0 2-1 3-2 4 1 7 2 11 2h-1-5l-1 2-1 1h1l1 2c-1 0-1 1-1 1h-2l-1 1h-2l1 2c0 2 0 2-1 3h-2 0c-1 0-1-1-1-2l-5-1-3-5h-1l-11 1c-1-1-2-1-3-2h-1v-1l1-1v-1c0-1 0-2-1-3l-1 1c-1-1-1-1-1-2 1-1 1-3 2-5v3h3c1 0 2-1 3-1h7c1-2 1-4 2-6z" class="f"></path><path d="M538 355c1-1 1-2 3-3 1 1 1 1 2 1s1-1 2-1h0c1 1 1 2 2 3h0c-2 0-4 0-5 2v-2c-2 1-2 1-4 0z" class="i"></path><path d="M524 358h1c4-1 7-2 11-2-1 1-1 2 0 3h2l-11 1c-1-1-2-1-3-2z" class="C"></path><path d="M538 355c2 1 2 1 4 0v2c1 1 1 2 2 3-1 2-1 1-2 3 3 1 3-1 5 2l-5-1-3-5h-1-2c-1-1-1-2 0-3l2-1z" class="H"></path><path d="M538 342l1-1c1 1 1 1 1 2h0c-1 1 0 1-1 2l1 1c-1 1-2 2-1 3v1c-2 2-4 3-7 3l-1 1v1h-1c-1 0-1 0-2 1h-4v-1c0-1 0-2-1-3l-1 1c-1-1-1-1-1-2 1-1 1-3 2-5v3h3c1 0 2-1 3-1h7c1-2 1-4 2-6z" class="i"></path><path d="M526 349c1 0 2-1 3-1h7c-1 1-1 2-2 2l-1 1h-1l-1-1-1 1c-1 1-4 0-6 1-1-1-1-2-1-3h3z" class="l"></path><path d="M551 353c4 1 7 2 11 2h-1-5l-1 2-1 1h1l1 2c-1 0-1 1-1 1h-2l-1 1h-2l1 2c0 2 0 2-1 3h-2 0c-1 0-1-1-1-2-2-3-2-1-5-2 1-2 1-1 2-3-1-1-1-2-2-3 1-2 3-2 5-2h1c1 0 2-1 3-2z" class="M"></path><path d="M550 362v-1c-2-1-2-1-4 0l-1-1 1-1h2c1-1 1-2 3-2 1 1 2 2 3 2l1-1 1 2c-1 0-1 1-1 1h-2l-1 1h-2z" class="T"></path><path d="M523 315h1c2 0 3 1 4 0l7 2c1 1 3 2 4 2 2 2 4 4 5 7l-4 7c-1 3-2 6-2 9-1 2-1 4-2 6h-7c-1 0-2 1-3 1h-3v-3-5-1-18-7z" class="G"></path><path d="M536 321c1 0 2 0 3 1 1 0 1 0 1 1-1 0-3 0-4-1v-1zm2 6h1c1 1 1 2 1 3-2 1-3 1-5 1v-1l3-3z" class="J"></path><path d="M533 325h1c1 0 3 1 4 2l-3 3-3-3 1-2z" class="S"></path><path d="M529 321c1-1 2-1 3-1l2 1-1 1 2 1h0c-1 1-1 1-2 1v1l-1 2h-1c-1-1-3-1-4-1v-2c0-2 1-2 2-3z" class="u"></path><path d="M529 321c1-1 2-1 3-1-1 2-1 4-3 5h-1l1-4z" class="X"></path><path d="M523 315h1c2 0 3 1 4 0l7 2c0 2-1 3-1 4l-2-1c-1 0-2 0-3 1s-2 1-2 3v2c0 2-1 3-1 4l2 2-1 1c-1 1-1 2-1 2 0 1 1 1 2 2l-3 3h-2v-18-7z" class="s"></path><path d="M526 335c1 0 1 0 2-1h3v-2h2c1 0 1 0 2 1l1 1c1-1 2-1 4-1-1 3-2 6-2 9-1 2-1 4-2 6h-7c-1 0-2 1-3 1h-3v-3-5-1h2l3-3c-1-1-2-1-2-2z" class="c"></path><path d="M532 343c0 1 0 1-1 2l-1-1c0-1 0-1 1-2h1v1z" class="j"></path><path d="M532 342v-1h-1l1-1c1 0 1 0 2-1v-1l1-1c1 1 2 2 2 4l-2 2h-2 0-1v-1z" class="J"></path><path d="M523 341c2 1 3 1 4 3l-1 3h0v2h-3v-3-5z" class="s"></path><path d="M552 333c2-2 4-3 6-4 4-2 8-1 13 0 2 1 4 2 5 4 3 2 5 5 5 9 0 0-1 1-1 2v1 1l-2 2c-2 2-4 3-7 4v1l-6 1-3 1c-4 0-7-1-11-2l-2-3c-2-4-1-7-1-11l2-3h0l2-3z" class="E"></path><path d="M554 342h1c1-1 0-1 1-2h1v1c0 2 1 2 2 3v2c0 1-1 1-2 2-2-1-2-2-3-4h-1l1-2z" class="Y"></path><path d="M550 336c0 1 0 2 1 3h0l-1 2c0 1 0 2-1 3 1 2 3 2 3 4l-1 2h-2c-2-4-1-7-1-11l2-3z" class="c"></path><path d="M556 334c1 0 2 1 4 2 0 2-1 3-1 4l-2 1v-1h-1c-1 1 0 1-1 2h-1l-2-1v-2c1-2 3-4 4-5z" class="b"></path><path d="M552 333c2-2 4-3 6-4 4-2 8-1 13 0v3 2l-2-2c-2 0-4-1-6-1h-1l-6 3c-1 1-3 3-4 5h-1 0c-1-1-1-2-1-3h0l2-3z" class="J"></path><path d="M567 345l1-1 1 1h2l2 1h2c0 1-1 1-1 1l-1 2-1-1-3 2c0 1 0 1 2 2v1l-6 1-3 1c-4 0-7-1-11-2l-2-3h2 0c1 1 1 0 2 0s3 2 3 3l1-1c-1-1-1-2-1-3h1l1 1v1c4 0 3-1 5-4h0l-1-1c1 0 1-1 1-2 2 1 2 2 4 1z" class="j"></path><path d="M567 345l1-1 1 1h2l2 1h2c0 1-1 1-1 1v-1h-2l-1 1c-2 0-2 0-4-2zm-18 5h2 0c1 1 1 0 2 0s3 2 3 3l1-1h1c1-1 2-1 3 0h0c2 0 3-1 5-2 1-1 2 0 3 0 0 1 0 1 2 2v1l-6 1-3 1c-4 0-7-1-11-2l-2-3z" class="G"></path><path d="M571 329c2 1 4 2 5 4 3 2 5 5 5 9 0 0-1 1-1 2v1 1l-2 2c-2 2-4 3-7 4v1-1c-2-1-2-1-2-2l3-2 1 1 1-2s1 0 1-1h-2l-2-1h-2l-1-1-1 1c-2 1-2 0-4-1l-3-3c0-1 1-3 2-3h1l1-3-1-1 1-1h4l1-1 2 2v-2-3z" class="c"></path><path d="M571 332c1 0 2 0 3 1l-1 1c-1 1-1 0-2 0v-2z" class="s"></path><path d="M569 350l3-2v2s0 1-1 2v1-1c-2-1-2-1-2-2z" class="J"></path><path d="M574 339h1c1 0 2 1 2 2l-1 1-2 1-1-1c0-2 0-2 1-3z" class="F"></path><path d="M567 343h-2c-1-1-1-2-2-3v-1c1-2 2-2 4-2 1 2 1 3 0 6z" class="M"></path><path d="M567 337h0c1 0 2 0 3-1l1 1v1l-2 2h1l1 1h1l-1 1-1 1 1 2h-2l-1-1c0-1 0 0-1-1 1-3 1-4 0-6z" class="P"></path><path d="M296 307l3 1c0-1 0-1 1-1v1 1h1c-2 1-3 1-4 2-2 1-2 1-4 1-1 2-2 3-3 5l-5 2c1 0 2 1 3 1v-1c0 1 0 2 1 3l4 7 1 1c1 2 1 3 2 5v1c1 5 0 7-3 10l-1 1h0l-2 1c1 1 1 2 2 3l2-1 1 1c2 1 2-1 4 1h-1l1 1v1h1l3 3 3-1c0 1 1 1 1 3l-1 2c2 1 2 0 4 0l1-1 1 1-1 2h1c1 0 2-2 3-2h2l1-1s1 0 2-1l-7 18c-5 12-8 26-9 39l-1-2c-1 1 0 4-1 5s0 3 0 5c0 5-1 10 0 15l1 13c1 3 1 6 2 9v1s0 1 1 1v3l-1 2v1h0-5l-5 4v1h2c0 1 0 1 1 2l-2 3h4l3 1c1 1 2 2 3 5v1h1v2c0 1 0 0 1 1 0 1-1 2 0 3v1l-1 1v1c0 3-1 5-2 7l1 1h1l1-1h0 1l-1 1c0 1-1 1-3 2l-1-1v1c1 1 1 1 2 1 2 1 3 2 4 4v1c-3-2-5-3-7-5v3l1-1 1 1c0 1 0 2-1 3h0l-4-1-8-6c-2-1-4-3-6-5-4-2-8-6-11-10-13-12-23-25-30-40l-12-32h0l5 3c0-1 0-1 1-2l8 5 7 4c2 1 5 2 7 3 1-1 0-1 1-1l-2-2c-1-1-2-1-3-2 0-1-2-3-3-4s-2-1-3-3l1-1c0-2-1-2-2-4h-3l-1 1c-2-1-3-3-4-5l-2-3-1-2-3-7c-3-7-4-13-5-20v-8l1-4 1-1c0-1 0-2 1-3v-3h1c1-2 2-5 3-7l1-2 1-3 1-1 1-2v-3c0-1-2 0-4-1l1-1c4 0 3-2 6-3 1 0 1 0 1-1l-1-3h-2l5-2c2-1 3-2 5-2h0c3-2 8-6 9-8v-1c-2-2-1-2-1-4 0 1 1 1 2 1 1 1 0 2 2 1 1 0 1 0 1-1 2-1 2-2 3-3h1l1 1 1-2c2 0 3-1 5 0l-1 1v1l3-1v-1c2 0 2 0 4-1h1 1c2 0 6-3 9-4h2z" class="B"></path><path d="M290 431l-1-1 2-2h0c1 1 2 1 3 1h-1c-1 1-1 1-2 1h0l-1 1z" class="C"></path><path d="M291 430c1 1 2 1 3 2 0 1-1 1-1 2l-3-2v-1l1-1z" class="a"></path><path d="M293 423l1-2c1 1 0 1 2 1l1 1s-2 4-2 5h-1v-1-1c0-1 0-2-1-3z" class="T"></path><path d="M293 423c1 1 1 2 1 3v1 1 1c-1 0-2 0-3-1h0c0-2 1-3 2-5z" class="S"></path><path d="M291 428c1-1 1-1 3-1v1 1c-1 0-2 0-3-1z" class="V"></path><path d="M300 426c1 3 0 5 0 8 0 2 1 5 1 7v3l-1-2v-2-2c-1 0-1-1-1-2 0-2 0-2-2-3 1-1 0-1 1-1h1v-1c0-2 0-3 1-5zm-11 22c-1-1-1-2-1-3v-2c1-2 3-3 5-3 1 0 4 0 5 1l1 1c-1 1-2 1-2 2l-1 1c-1 0-1-1-2-1 0 0 1-2 0-2 0-1-1-1-1-1-2 1-3 2-4 3v2h1c0 1-1 1-1 2z" class="C"></path><path d="M294 409c2-2 4-2 5-4s1-2 3-3c-1 1-2 2-2 4v1c0 1 0 2-1 3-2-1-2 0-4 0v1c-1 1-2 3-2 5 0 1-1 2-2 3 0 1 0 1-1 2-2 3-3 10-3 13l-2 2c0 2-1 3-2 4v-2l2-2-1-1v-1c1-1 1-1 1-2v-1h1v-2-3c1-1 1-1 1-2 0-2 2-3 2-5s0-1 1-2c1-3 2-6 4-8z" class="U"></path><path d="M302 402c1-1 0-1 1-3l2-1c-1 5-1 11-2 16-1 1 0 4-1 5s0 3 0 5c0 5-1 10 0 15l1 13c1 3 1 6 2 9l-1 1h-1c1 2 2 2 1 4h0l-2-1c-1 1-2 1-3 1v-1l-2-1v-1c0-2-1-2-2-3s0-1-1-2c0-1-2-2-3-3 0-1 0-1-1-1-1-2-3-3-3-5h0l-1-1c0-2 0-5 1-6v-1c0-1 0-1 1-2 0 0 1-1 1-2v-2h2 2 2l2 2-4 3c-2 0-4 1-5 3v2c0 1 0 2 1 3 0 1 1 1 1 3 1 0 1 0 2 1h1c1-2 1-3 3-4l1 1-1 1v3l1 1v1l1 3 1-1v1c0 1 1 3 0 4v1h1c1 0 2-1 2-2 0-2 0-5-1-6l-2 1v-1l2-1c0-3-1-7 0-10v-3c0-2-1-5-1-7 0-3 1-5 0-8h-1c1-2 1-2 0-4-1-1-2-1-3-1v-2c0-1-1-2-1-2v-1l-1-1-1 1c0-2 1-4 2-5v-1c2 0 2-1 4 0 1-1 1-2 1-3v-1c0-2 1-3 2-4z" class="F"></path><path d="M296 411h1c0 1 1 2 0 3h-1c-1-2 0-2 0-3z" class="B"></path><path d="M297 455l1 3h-1l-2-1h0l2-2z" class="H"></path><path d="M295 411h1c0 1-1 1 0 3h1l-2 3v-1l-1-1-1 1c0-2 1-4 2-5z" class="T"></path><path d="M286 426v3 2h-1v1c0 1 0 1-1 2v1l1 1-2 2v2c-1 6-1 10 3 15 1 1 2 1 3 1l1 2h2 2c1 1 0 1 1 2s2 1 2 3v1l2 1v1c1 0 2 0 3-1l2 1h0c1-2 0-2-1-4h1l1-1v1s0 1 1 1v3l-1 2v1h0-5l-5 4v1h2c0 1 0 1 1 2l-2 3c-1 1-2 1-3 2l-1 3c0 1 1 2 1 3v2c-1 0-2 0-3-1h-1c-1-1-2-1-3-2s-2-3-2-4v-2c-1-2-1-3-1-5 0-1 0-2 1-2-2-1-2-2-3-3l1-3-2-2c1-2 4-3 5-6-5-5-7-9-7-16l2-11 1-4 1-3c1 0 1 0 2 1v1l1 1c0-1 0-2 1-2z" class="f"></path><path d="M286 481l2 2 1-1c1-1 3-1 4-1l-1 3-1 2c-1-1-2-1-3-1h0c-2-1-2-3-2-4z" class="F"></path><path d="M282 467c1 1 2 1 2 2v1l2 2 1 1h3v1c-1 0-4 1-5 1v-3l-1 1c-2-1-2-2-3-3l1-3z" class="L"></path><path d="M292 461h1c2 2 3 4 3 7 0 2-2 4-4 4l-1 1h-1-3l-1-1c-1-1-1-2 0-4 1 1 2 2 4 2h2c1-1 2-2 2-4s-1-3-2-5z" class="K"></path><path d="M286 468c1 1 2 2 4 2l1 2c-1 1-3 0-4 1l-1-1c-1-1-1-2 0-4z" class="G"></path><path d="M286 468c-1-2-1-4 0-5 2-2 3-2 6-2h0c1 2 2 3 2 5s-1 3-2 4h-2c-2 0-3-1-4-2z" class="B"></path><path d="M289 462c1 0 1 0 2 1 0 1 1 1 1 2l-1 2h-1c-1-2-1-3-1-5z" class="g"></path><path d="M295 474h2c0 1 0 1 1 2l-2 3c-1 1-2 1-3 2-1 0-3 0-4 1l-1 1-2-2c0-1 0-3-1-4v-1h2 2c2-1 4-1 5-1l1-1z" class="C"></path><path d="M286 478h1c1 1 1 1 2 0h3l-1 1c-1 1-1 1-2 1-2 0-2-1-3-2z" class="O"></path><path d="M282 425c1 0 1 0 2 1v1l1 1c-2 4-4 10-4 15s-1 10 4 14l3 3c2 0 2 0 4 1-3 0-4 0-6 2-1 1-1 3 0 5-1 2-1 3 0 4l-2-2v-1c0-1-1-1-2-2l-2-2c1-2 4-3 5-6-5-5-7-9-7-16l2-11 1-4 1-3z" class="E"></path><path d="M280 465l2 2-1 3c1 1 1 2 3 3-1 0-1 1-1 2 0 2 0 3 1 5v2c0 1 1 3 2 4s2 1 3 2h1c1 1 2 1 3 1v-2c0-1-1-2-1-3l1-3c1-1 2-1 3-2h4l3 1c1 1 2 2 3 5v1h1v2c0 1 0 0 1 1 0 1-1 2 0 3v1l-1 1v1c0 3-1 5-2 7l1 1h1l1-1h0 1l-1 1c0 1-1 1-3 2l-1-1v1c1 1 1 1 2 1 2 1 3 2 4 4v1c-3-2-5-3-7-5v3l1-1 1 1c0 1 0 2-1 3h0l-4-1-8-6c-2-1-4-3-6-5h1v-2c-1-1-2-2-2-3v-1c-4-6-7-12-8-19l1-4c0-1 0-2 1-4l1-2z" class="L"></path><path d="M281 470c1 1 1 2 3 3-1 0-1 1-1 2-2 2-1 5-1 7-1-2-1-4-1-7-1-2-1-3 0-5z" class="I"></path><path d="M280 465l2 2-1 3c-1 2-1 3 0 5-1-2-1-2-3-4 0-1 0-2 1-4l1-2z" class="X"></path><path d="M290 494c1 0 2 0 4 1h4c1 1 1 1 1 2 1 0 2 0 2 1 1 1 2 2 4 2h0c-2 1-5 0-6-1-1 1-1 1-2 1s-2-1-3-2c-1 0-4-3-4-4z" class="k"></path><path d="M307 495v-1 1c0 3-1 5-2 7l1 1h1l1-1h0 1l-1 1c0 1-1 1-3 2l-1-1v1c1 1 1 1 2 1 2 1 3 2 4 4v1c-3-2-5-3-7-5l-10-7 1-1c1 1 2 2 3 2s1 0 2-1c1 1 4 2 6 1h0c0-1-1-3 0-4 0-1 1-1 2-1z" class="Q"></path><path d="M306 485v1h1v2c0 1 0 0 1 1 0 1-1 2 0 3v1l-1 1v1c-1-1-2-1-3-1v-2-1l-1 1v2 1 1l-1-1c-1-1-2-2-2-3h-4c-1 1-1 2-2 3-1-2 1-3 0-5h-1c-1 1-1 2-2 2-2-2-4-4-5-6 1 1 2 1 3 2h1c1 1 2 1 3 1v-2c2 2 4 4 6 4 1 0 3-1 4-2 2-1 2-2 3-4z" class="V"></path><path d="M306 485v1h1v2c0 1 0 0 1 1 0 1-1 2 0 3v1c-1 0-2-1-3-2l-2-2c2-1 2-2 3-4z" class="L"></path><path d="M278 471c2 2 2 2 3 4 0 3 0 5 1 7 2 5 4 9 8 12h0c0 1 3 4 4 4l-1 1-5-3-3-2c-4-6-7-12-8-19l1-4z" class="J"></path><path d="M285 494l3 2 5 3 10 7v3l1-1 1 1c0 1 0 2-1 3h0l-4-1-8-6c-2-1-4-3-6-5h1v-2c-1-1-2-2-2-3v-1z" class="B"></path><path d="M285 494l3 2c0 4 3 6 4 9-2-1-4-3-6-5h1v-2c-1-1-2-2-2-3v-1z" class="T"></path><path d="M296 479h4l3 1c1 1 2 2 3 5-1 2-1 3-3 4-1 1-3 2-4 2-2 0-4-2-6-4 0-1-1-2-1-3l1-3c1-1 2-1 3-2z" class="G"></path><path d="M297 480h2c1 0 3 0 4 1 1 2 1 3 1 5-1 1-1 2-3 3-1 0-3 0-4-1-2-1-2-2-2-4s1-3 2-4z" class="R"></path><path d="M312 376l1 1c-5 12-8 26-9 39l-1-2c1-5 1-11 2-16l-2 1c-1 2 0 2-1 3-2 1-2 1-3 3s-3 2-5 4-3 5-4 8c-1 1-1 0-1 2s-2 3-2 5c0 1 0 1-1 2-1 0-1 1-1 2l-1-1v-1c-1-1-1-1-2-1l-1 3c-1-1-2-1-3-3h0l-1-1h-3 0v-1c0-1 1-1 2-2 0 0 0-1-1-2h0v2c-1-1-1-1-2-1-1-1-1-2-2-3h0l3-2v-2l2-1-1-1c0-2 1-2 2-3l2-2c2-1 3-2 5-3v-1c-1 0-2-1-2-1h-1l5-3h-2c0-2 0-2 1-4l14-11 1 1c2 0 4-3 6-4h1c1-1 1-1 3-1l2-3z" class="C"></path><path d="M276 421l1 3h-3 0v-1c0-1 1-1 2-2z" class="F"></path><path d="M291 408v1c1-1 2-2 4-3l-1 3c-2 2-3 5-4 8-1 1-1 0-1 2s-2 3-2 5c0 1 0 1-1 2-1 0-1 1-1 2l-1-1v-1c-1-1-1-1-2-1 0-1 2-3 2-4 2-4 4-9 7-13z" class="b"></path><path d="M299 393l1 1-4 5h0c-2 0-3 2-4 4-1 1-1 2-1 3l-3 3c-1 1-1 2-1 3h-1c0 1-1 1-1 2v2l-3 1v3h-1c-1 1-1 1-2 1v2h-1v-4c-1 0-1 0-2-1l3-1v-1c1-1 2-2 3-4h1l-1-2 2-2c1 0 2-1 3-2 2-2 3-3 4-5l1-1c2-1 4-2 5-5l2-2z" class="U"></path><path d="M282 412l1 1c0 1-1 2-1 3h0c-1 1-1 1-2 1h-1v-1c1-1 2-2 3-4z" class="T"></path><path d="M299 393c1-2 6-4 7-6 1-1 1-1 2-1v1h0c-1 4-1 8-3 11l-2 1c-1 2 0 2-1 3-2 1-2 1-3 3s-3 2-5 4l1-3c-2 1-3 2-4 3v-1c1-3 3-6 5-9h0l4-5-1-1z" class="S"></path><path d="M299 393c1-2 6-4 7-6 1-1 1-1 2-1v1h0c-1 1-2 2-2 3-2 3-4 4-6 7s-3 7-5 9c-2 1-3 2-4 3v-1c1-3 3-6 5-9h0l4-5-1-1z" class="E"></path><path d="M284 403c5-2 9-6 13-8-1 3-3 4-5 5l-1 1c-1 2-2 3-4 5-1 1-2 2-3 2l-2 2 1 2h-1c-1 2-2 3-3 4v1l-3 1-1 1h0v2c-1-1-1-1-2-1-1-1-1-2-2-3h0l3-2v-2l2-1-1-1c0-2 1-2 2-3l2-2c2-1 3-2 5-3z" class="f"></path><path d="M273 420v-3c1-1 1-1 3-1-1 1-1 2-1 3v2c-1-1-1-1-2-1z" class="t"></path><path d="M279 416l-1-1v-1l2-2h2c-1 2-2 3-3 4z" class="q"></path><path d="M284 403c5-2 9-6 13-8-1 3-3 4-5 5l-1 1c-3 2-8 3-10 6v1h1c-1 1-2 1-3 1 0 0-1-1-2-1l2-2c2-1 3-2 5-3z" class="m"></path><defs><linearGradient id="t" x1="296.276" y1="378.967" x2="296.226" y2="401.033" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#313231"></stop></linearGradient></defs><path fill="url(#t)" d="M312 376l1 1c-5 12-8 26-9 39l-1-2c1-5 1-11 2-16 2-3 2-7 3-11h0v-1c-1 0-1 0-2 1-1 2-6 4-7 6l-2 2c-4 2-8 6-13 8v-1c-1 0-2-1-2-1h-1l5-3h-2c0-2 0-2 1-4l14-11 1 1c2 0 4-3 6-4h1c1-1 1-1 3-1l2-3z"></path><path d="M299 383l1 1c2 0 4-3 6-4h1c1-1 1-1 3-1-7 7-16 15-24 19h-2c0-2 0-2 1-4l14-11z" class="W"></path><path d="M238 385c2 1 4 3 6 5v1h-1c0 1 0 2 1 2 1 2 2 3 3 4s2 1 3 2c5 2 10 4 15 5 2 0 4-1 6 0 2 0 4-1 6-2l4-1h1s1 1 2 1v1c-2 1-3 2-5 3l-2 2c-1 1-2 1-2 3l1 1-2 1v2l-3 2h0c1 1 1 2 2 3 1 0 1 0 2 1v-2h0c1 1 1 2 1 2-1 1-2 1-2 2v1h0 3l1 1h0c1 2 2 2 3 3l-1 4-4-1c-1 2-1 2-1 4v1l-2 1v1h-4v1c-1-1-2-1-3-2-1 0 0 0-1-1h-1c-2 0-3 0-4-1 1-1 1-1 2-1h1l1-2-3-1c1-1 0-1 1-1l-2-2c-1-1-2-1-3-2 0-1-2-3-3-4s-2-1-3-3l1-1c0-2-1-2-2-4h-3l-1 1c-2-1-3-3-4-5l-2-3-1-2-3-7c2-2 1-2 2-4l-1-4h1c1-1 1-1 1-2s-1-2-1-3h0z" class="K"></path><path d="M245 409c1-1 2-2 3-2l1 4h-2l-2-2z" class="l"></path><path d="M249 411l2 2-1 1h-3v-3h2z" class="I"></path><path d="M249 402l1 1-1 3-4-2c1-1 3-1 4-2z" class="S"></path><path d="M242 402l3 1v1l4 2-1 1c-1 0-2 1-3 2l-1-2-1-3-1-2z" class="I"></path><path d="M244 407l1 2 2 2v3l-1 1c-2-1-3-3-4-5 1 0 1-1 2-2v-1z" class="E"></path><path d="M251 406h2c1 0 2 1 2 2 0 2 0 2-2 3 0 0-1 0-1 1l-2-2c-1-1 0-2 0-3l1-1z" class="D"></path><path d="M241 400c0-2 1-4 3-4l2 2 1-1c1 1 2 1 3 2v1 1c-1 0-1 0-1 1h0c-1 1-3 1-4 2v-1l-3-1-1-2z" class="l"></path><path d="M245 403c-1-1-2-2-2-3 1-1 1-1 2-1 2-1 3 0 5 1v1c-1 0-1 0-1 1h0c-1 1-3 1-4 2v-1z" class="Z"></path><path d="M269 408c1 1 3 1 4 1 0 2 1 3 1 4v2l-3 2h0l-2 2h-1v-1l-1-1c-2-1-2-2-4-3 2 0 3 0 6-1l1-2c0-1-1-2-1-3z" class="L"></path><path d="M269 408c1 1 3 1 4 1-1 2-1 3-1 4l-2 1-1-1 1-2c0-1-1-2-1-3z" class="I"></path><path d="M238 394l1 1c0 1 1 3 2 5l1 2 1 2 1 3v1c-1 1-1 2-2 2l-2-3-1-2-3-7c2-2 1-2 2-4z" class="X"></path><path d="M244 408c-2-1-2-1-3-3v-1l1 1 1-1 1 3v1z" class="G"></path><path d="M238 385c2 1 4 3 6 5v1h-1c0 1 0 2 1 2 1 2 2 3 3 4l-1 1-2-2c-2 0-3 2-3 4-1-2-2-4-2-5l-1-1-1-4h1c1-1 1-1 1-2s-1-2-1-3h0z" class="c"></path><path d="M238 394l-1-4h1l4 4c-1 0-2 0-3 1l-1-1z" class="d"></path><path d="M239 395c1-1 2-1 3-1l2 1v1c-2 0-3 2-3 4-1-2-2-4-2-5z" class="Q"></path><path d="M281 401h1s1 1 2 1v1c-2 1-3 2-5 3l-2 2c-1 1-2 1-2 3l1 1-2 1c0-1-1-2-1-4-1 0-3 0-4-1-2-1-3-2-5-3h3c1-1 3 0 4-1 2 0 4-1 6-2l4-1z" class="P"></path><path d="M284 403c-2 1-3 2-5 3h-2l-2 1h-2c-1-1-3-1-4-1 3-2 5 0 8-1 1-1 2-1 4-2h3z" class="k"></path><path d="M269 408c-2-1-3-2-5-3h3l2 1c1 0 3 0 4 1h2l2-1h2l-2 2c-1 1-2 1-2 3l1 1-2 1c0-1-1-2-1-4-1 0-3 0-4-1z" class="Z"></path><path d="M251 413h3c1-1 2-3 4-4h1v-2h-1l-1 1c-1-2-1-2-1-4h-1v-1h0c1 0 3 2 4 2s1 0 3 1l-1 1c2 1 3 1 5 1 1 1 1 1 1 3-1 1-2 2-3 2-2 0-2-1-3-2h-1-1 0c-1 1-1 1-1 2s-1 4-2 5h-3v1l1 1v1 1c-1-1-2-1-3-3l1-1c0-2-1-2-2-4l1-1z" class="r"></path><path d="M254 422v-1-1l-1-1v-1h3c1-1 2-4 2-5s0-1 1-2h0l1 2 3 1c2 1 2 2 4 3l1 1v1h1l2-2c1 1 1 2 2 3 1 0 1 0 2 1v-2h0c1 1 1 2 1 2-1 1-2 1-2 2v1h0 3l1 1h0c1 2 2 2 3 3l-1 4-4-1c-1 2-1 2-1 4v1l-2 1v1h-4v1c-1-1-2-1-3-2-1 0 0 0-1-1h-1c-2 0-3 0-4-1 1-1 1-1 2-1h1l1-2-3-1c1-1 0-1 1-1l-2-2c-1-1-2-1-3-2 0-1-2-3-3-4z" class="f"></path><path d="M257 419v-1l1-1c1 0 2 1 4 1l1 1-1 1h0c-1 0-3-1-5-1z" class="D"></path><path d="M265 418h1v1c-1 1-1 2-1 4l-1 1-1-1c0-1 0-2-1-3l1-1-1-1h3z" class="B"></path><path d="M260 413l3 1c2 1 2 2 4 3h-2-6v-1l1-1v-2z" class="V"></path><path d="M257 419c2 0 4 1 5 1-2 2 0 4-1 6h-2v-3c0-1-1-2-1-4h-1z" class="C"></path><path d="M265 417h2l1 1v1h1l2-2c1 1 1 2 2 3 1 0 1 0 2 1v-2h0c1 1 1 2 1 2-1 1-2 1-2 2v1 4h-1v1c-1 0-3 1-4 1h0-3c0-1-1-2-2-2v-1c-1-1-1-2 0-3l1-1c0-2 0-3 1-4v-1h-1v-1z" class="K"></path><path d="M267 427l-1-2c1-1 1-1 3-2 2 0 2 0 3 1 0 1 0 2-1 2l-1 2h-2l-1-1z" class="H"></path><path d="M267 427v-1c1 0 1-1 2-1s1 0 1 1h1l-1 2h-2l-1-1z" class="T"></path><path d="M265 417h2l1 1v1h1l2-2c1 1 1 2 2 3 1 0 1 0 2 1v-2h0c1 1 1 2 1 2-1 1-2 1-2 2-1-1-2-1-3-2-2 0-4 1-6 2 0-2 0-3 1-4v-1h-1v-1z" class="w"></path><path d="M277 424l1 1h0c1 2 2 2 3 3l-1 4-4-1c-1 2-1 2-1 4v1l-2 1v1h-4v1c-1-1-2-1-3-2-1 0 0 0-1-1h-1c-2 0-3 0-4-1 1-1 1-1 2-1h1l1-2h1l1-1 2 1c1 1 1 1 2 1h0c1-1 2-2 3-2l-1-1c-2 1-1 1-3 0h0c1 0 3-1 4-1v-1h1v-4h0 3z" class="Q"></path><path d="M269 438c1-1 3-2 3-3 1-1 1-2 1-3v5 1h-4z" class="R"></path><path d="M277 424l1 1h0c1 2 2 2 3 3l-1 4-4-1c-1 2-1 2-1 4v1l-2 1v-5h0c1-3 1-5 1-8h3z" class="w"></path><path d="M278 425c1 2 2 2 3 3l-1 4-4-1 1-2c0-2 0-2 1-4z" class="D"></path><path d="M312 363c1 0 2-2 3-2h2l1-1s1 0 2-1l-7 18-1-1-2 3c-2 0-2 0-3 1h-1c-2 1-4 4-6 4l-1-1-14 11c-1 2-1 2-1 4h2l-5 3-4 1c-2 1-4 2-6 2-2-1-4 0-6 0-5-1-10-3-15-5-1-1-2-1-3-2s-2-2-3-4c2 0 2 0 3 2 1-1 1-2 2-3h1 3v-1c2 1 5 0 7-1v-1c2 0 3 0 4-2l3-1v-1c2-2 4-4 4-8h1l-1-3c2 0 3-1 5 0l1-1h1c0 1 0 3-1 3 1 2 1 4 1 5l1 1 3 1 4-2c1-1 2-1 3-2 2-1 4-2 6-4l4-3c2-3 5-6 6-10l1-1c2 1 2 0 4 0l1-1 1 1-1 2h1z" class="Y"></path><path d="M279 390h3c-2 2-5 3-8 4l-1 1-3-1c1-1 2-1 2-1 3-1 4-2 7-3z" class="M"></path><path d="M301 379l3-2c-1 3-3 4-5 6l-14 11-4 2 3-4 5-4c2-4 5-7 9-9h3z" class="T"></path><path d="M298 379h3c-4 4-8 8-12 9 2-4 5-7 9-9z" class="Q"></path><path d="M310 361l1-1 1 1-1 2h1v1l-1 1h0 0c-1 2-2 3-3 4s-1 3-1 4l-3 4-3 2h-3 0v-1l4-4-1-1c-1 1-2 2-2 3-1 0-1 0-2 1l-2-2 4-3c2-3 5-6 6-10l1-1c2 1 2 0 4 0z" class="G"></path><path d="M299 372v1c-1 1-2 1-2 3l1-1v-1c1-1 2-1 4-1h1v-1l1-1c0 2 0 3-1 5-1 1-3 2-5 3v-1l4-4-1-1c-1 1-2 2-2 3-1 0-1 0-2 1l-2-2 4-3z" class="K"></path><path d="M310 361l1-1 1 1-1 2h1v1l-1 1h0 0c-1 2-2 3-3 4s-1 3-1 4l-3 4-3 2h-3 0c2-1 4-2 5-3 1-2 1-3 1-5 2-3 5-6 6-10z" class="b"></path><path d="M312 363c1 0 2-2 3-2h2l1-1s1 0 2-1l-7 18-1-1-2 3c-2 0-2 0-3 1h-1c-2 1-4 4-6 4l-1-1c2-2 4-3 5-6l3-4c0-1 0-3 1-4s2-2 3-4h0 0l1-1v-1z" class="o"></path><path d="M311 365h0l1-1c1 0 2 0 2 1l-2 3-1-3z" class="I"></path><path d="M311 365l1 3-5 5c0-1 0-3 1-4s2-2 3-4h0z" class="Q"></path><path d="M312 363c1 0 2-2 3-2h2l1-1s1 0 2-1l-7 18-1-1c2-4 4-9 5-13v-1l-3 3c0-1-1-1-2-1v-1z" class="h"></path><path d="M271 374c2 0 3-1 5 0l1-1h1c0 1 0 3-1 3 1 2 1 4 1 5l1 1 3 1 4-2h1c1 0 2-1 3 0v3c-2 2-5 4-8 6h-3c-3 1-4 2-7 3 0 0-1 0-2 1v1c-1-1-3-1-4-1h0-2c1-2 1-2 3-3v-1c1-1 1-1 2-3l-3 1 1-2v-1c2-2 4-4 4-8h1l-1-3z" class="C"></path><path d="M266 394c2-1 4-1 5-2 0-1 0-2 1-3 0-1 0-1 1-1l1 2c0 2-1 0-2 2v1s-1 0-2 1v1c-1-1-3-1-4-1z" class="F"></path><path d="M271 374c2 0 3-1 5 0l1-1h1c0 1 0 3-1 3-1 3 0 5-1 8v-7h-2c-1-1-1-1-2 0l-1-3z" class="R"></path><path d="M272 377c1-1 1-1 2 0h-1v1l2 1v1h-2v1 1c-2 1-3 3-4 5l-3 1 1-2v-1c2-2 4-4 4-8h1z" class="M"></path><path d="M276 384c1-3 0-5 1-8 1 2 1 4 1 5l1 1 3 1 4-2h1c1 0 2-1 3 0v3c-2 2-5 4-8 6h-3l1-1c1-1 2-1 3-2-1-1-1-1-2-1l-1 1c-2 0-2 0-4-1l1-1-1-1z" class="i"></path><path d="M276 384c1-3 0-5 1-8 1 2 1 4 1 5l1 1 3 1 4-2h1c-3 2-7 4-10 4l-1-1z" class="b"></path><path d="M264 387l3-1-1 2 3-1c-1 2-1 2-2 3v1c-2 1-2 1-3 3h2 0c1 0 3 0 4 1v-1l3 1 1-1 1 2 9-4-3 4 4-2c-1 2-1 2-1 4h2l-5 3-4 1c-2 1-4 2-6 2-2-1-4 0-6 0-5-1-10-3-15-5-1-1-2-1-3-2s-2-2-3-4c2 0 2 0 3 2 1-1 1-2 2-3h1 3v-1c2 1 5 0 7-1v-1c2 0 3 0 4-2z" class="T"></path><path d="M284 392l-3 4-1 1c-4 0-7 2-11 1l6-2c2-1 6-3 9-4z" class="q"></path><path d="M281 396l4-2c-1 2-1 2-1 4h2l-5 3-4 1h-1c-1 0-1-1-1-1l1-2c1 0 2-1 4-2h0l1-1z" class="H"></path><path d="M273 395l1-1 1 2-6 2h-1c-4 1-8 1-12 0-3-1-5-2-7-3h1c1 0 3 0 5 1 1 0 2-1 3-1h0c4 2 12 1 15 0z" class="n"></path><path d="M264 387l3-1-1 2 3-1c-1 2-1 2-2 3v1c-2 1-2 1-3 3h2 0c1 0 3 0 4 1v-1l3 1c-3 1-11 2-15 0h0c-1 0-2 1-3 1-2-1-4-1-5-1h-1-2c1-1 1-2 2-3h1 3v-1c2 1 5 0 7-1v-1c2 0 3 0 4-2z" class="m"></path><path d="M260 390l-3 3-2-1-1 1 1 2h-1v-1c-1 0-3-1-4-2h3v-1c2 1 5 0 7-1z" class="l"></path><path d="M266 388l3-1c-1 2-1 2-2 3v1c-2 1-2 1-3 3-1 0-2 1-3 1l-2-2c1-1 2-2 3-2 2-1 3-2 4-3z" class="i"></path><path d="M245 351l2-3 1 2c5 3 10 5 13 9l1 2 1 3 1 1 1 3c0 1 0 2 1 3h0l3 2-1-4c2 0 2 0 2 1l1 4 1 3h-1c0 4-2 6-4 8v1l-3 1c-1 2-2 2-4 2v1c-2 1-5 2-7 1v1h-3-1c-1 1-1 2-2 3-1-2-1-2-3-2-1 0-1-1-1-2h1v-1c-2-2-4-4-6-5h0c0 1 1 2 1 3s0 1-1 2h-1l1 4c-1 2 0 2-2 4-3-7-4-13-5-20v-8l1-4 1-1c0-1 0-2 1-3v-3h1c1-2 2-5 3-7l1 2 1-2c1 0 2 1 3 1h0l2-2z" class="X"></path><path d="M246 360c0-2 1-4 2-6l5 3c-2 0-2 0-3-1v1c0 1-1 2-1 3h-3 0z" class="L"></path><path d="M246 360h0 3c0-1 1-2 1-3v-1c1 1 1 1 3 1 1 0 2 1 3 2l-2 2c-1-1-1-1-3 0h0c-2 1-2 1-2 2s0 2-1 3c-1-1-2-2-3-2v-3l1-1z" class="S"></path><path d="M245 364v-3c2 1 3 1 4 2 0 1 0 2-1 3-1-1-2-2-3-2z" class="Z"></path><path d="M245 364c1 0 2 1 3 2l-2 2c1 1 2 1 3 1h1 1v2c0 1-2 1-3 2v2h-2l-1 1v2c-1-2-1-4-1-6l1-8z" class="V"></path><path d="M256 359h1c1 2 2 3 3 5l1 1h1l1-1 1 1 1 3c-1 2-1 3-2 5 0 1 0 2-1 3l-1 2-2-2c1-1 2-2 2-3h-1l-1 1c-1-1-1-2-2-3h-2-1-3v-2h-1-1c-1 0-2 0-3-1l2-2c1-1 1-2 1-3s0-1 2-2h0c2-1 2-1 3 0l2-2z" class="R"></path><path d="M257 359c1 2 2 3 3 5h-1c-1 0-2 0-2-1s0 0-1-2c0 0 1-1 1-2z" class="t"></path><path d="M263 364l1 1 1 3c-1 2-1 3-2 5 0 1 0 2-1 3l-1-1v-1c1-1 1-2 0-4l1-1c0-2 0-3-1-4h1l1-1z" class="K"></path><path d="M251 361h0c2 1 3 2 4 3s1 2 2 3c2 0 3-1 4 0-1 0-1 1-2 1h-2v1 1l-1-1-1 2h-1-3v-2h-1-1c-1 0-2 0-3-1l2-2c1-1 1-2 1-3s0-1 2-2z" class="i"></path><path d="M253 368v-2h1c1 1 1 2 2 3l-1 2h-1c0-1 0-2-1-3z" class="m"></path><path d="M250 365c1 1 1 1 2 3h1c1 1 1 2 1 3h-3v-2h-1v-4z" class="f"></path><path d="M249 363c0-1 0-1 2-2v3c0 1-1 1-1 1v4h-1c-1 0-2 0-3-1l2-2c1-1 1-2 1-3z" class="T"></path><path d="M255 371h2c1 1 1 2 2 3l1-1h1c0 1-1 2-2 3l2 2-1 2s-1 1-2 1c-2 2-2 2-2 5h0v1h0l-3 1c-1 0-1 0-2 1s-2 1-4 1v-1h-1c-2-1-3-1-3-2v-2-1c1-1 1-2 2-3l2 1c0-1-1-3-2-4v-2l1-1h2v-2c1-1 3-1 3-2h3 1z" class="C"></path><path d="M243 385c2 1 3 2 4 4h-1c-2-1-3-1-3-2v-2z" class="N"></path><path d="M248 373l2 1 1 1c-1 2-1 2-2 3-1-1-1-1-1-3v-2z" class="H"></path><path d="M245 376l4 3c1 2-1 2 1 4l-1 1-2-2c0-1-1-3-2-4v-2z" class="F"></path><path d="M259 376l2 2-1 2s-1 1-2 1l-2 1c-2 0-3 0-5-1-1-1-1-1-1-2 2 0 2 0 4-1v1c2 0 3 0 4-1 0-1 0-1 1-2h0z" class="q"></path><path d="M255 371h2c1 1 1 2 2 3l1-1h1c0 1-1 2-2 3h0c-1 1-1 1-1 2-1 1-2 1-4 1v-1l-3-2v-1l-1-1-2-1c1-1 3-1 3-2h3 1z" class="E"></path><path d="M255 371h2c1 1 1 2 2 3-2 1-2 1-3 0-1 0-2-1-2-1-2 0-3 0-3 2v1-1l-1-1-2-1c1-1 3-1 3-2h3 1z" class="F"></path><path d="M265 368c0 1 0 2 1 3h0l3 2-1-4c2 0 2 0 2 1l1 4 1 3h-1c0 4-2 6-4 8v1l-3 1c-1 2-2 2-4 2v1c-2 1-5 2-7 1v1h-3-1c-1 1-1 2-2 3-1-2-1-2-3-2-1 0-1-1-1-2h1v-1h3c2 0 3 0 4-1s1-1 2-1l3-1h0v-1h0c0-3 0-3 2-5 1 0 2-1 2-1l1-2 1-2c1-1 1-2 1-3 1-2 1-3 2-5z" class="Y"></path><path d="M258 386h1v2 1l-3-2h0v-1h2z" class="Q"></path><path d="M263 381h2v1c-2 1-4 3-6 4h-1c1-2 3-4 5-5z" class="M"></path><path d="M268 377c1 0 2 1 3 0 0 4-2 6-4 8v1l-3 1h-1l3-3c0-1 0-1-1-2v-1c1-1 2-3 3-4z" class="p"></path><path d="M265 368c0 1 0 2 1 3h0l3 2-1-4c2 0 2 0 2 1l1 4 1 3h-1c-1 1-2 0-3 0-1 1-2 3-3 4h-2c-2 1-4 3-5 5h-2 0c0-3 0-3 2-5 1 0 2-1 2-1l1-2 1-2c1-1 1-2 1-3 1-2 1-3 2-5z" class="T"></path><path d="M269 374l-3 1c-1-1-1-1-1-2l1-2 3 2v1z" class="W"></path><path d="M268 369c2 0 2 0 2 1l1 4 1 3h-1c-1 1-2 0-3 0v-1l1-2v-1l-1-4z" class="K"></path><path d="M268 376l3-1v2c-1 1-2 0-3 0v-1z" class="P"></path><path d="M260 380l5-4s1 1 1 2h-2c0 2 0 2-1 3-2 1-4 3-5 5h-2 0c0-3 0-3 2-5 1 0 2-1 2-1z" class="B"></path><path d="M238 352l1 2 1-2c1 0 2 1 3 1h0l2-2-3 10c-1 4-1 7-1 11h3c0 2 0 4 1 6 1 1 2 3 2 4l-2-1c-1 1-1 2-2 3v1 2c0 1 1 1 3 2h1v1h-3c-2-2-4-4-6-5h0c0 1 1 2 1 3s0 1-1 2h-1l1 4c-1 2 0 2-2 4-3-7-4-13-5-20v-8l1-4 1-1c0-1 0-2 1-3v-3h1c1-2 2-5 3-7z" class="D"></path><path d="M232 366l1-1c0-1 0-2 1-3v-3h1c-1 4-1 8-1 12h-1c-2 1-1 5-2 7v-8l1-4z" class="E"></path><path d="M239 354l1-2c1 0 2 1 3 1-2 2-3 5-4 8-2 2-1 7-2 10 1 1 2 3 2 5v4 1h-1c0-3-2-5-2-8v-1c0-1-1-2 0-3v-1-4h1l-1-1c0-2 1-3 2-4 0-1 1-1 1-2v-2h1l-1-1z" class="N"></path><path d="M231 378c1-2 0-6 2-7h1c1 5 2 9 4 14h0c0 1 1 2 1 3s0 1-1 2h-1l1 4c-1 2 0 2-2 4-3-7-4-13-5-20z" class="J"></path><path d="M243 353h0l2-2-3 10c-1 4-1 7-1 11h3c0 2 0 4 1 6 1 1 2 3 2 4l-2-1c-1 1-1 2-2 3v1 2c-2-2-3-4-4-6v-1-4c0-2-1-4-2-5 1-3 0-8 2-10 1-3 2-6 4-8z" class="H"></path><path d="M239 376l3 7s0 1 1 1v1 2c-2-2-3-4-4-6v-1-4z" class="e"></path><path d="M241 372h3c0 2 0 4 1 6 1 1 2 3 2 4l-2-1c-2-3-4-5-4-9z" class="E"></path><path d="M245 450l-12-32h0l5 3c0-1 0-1 1-2l8 5 7 4c2 1 5 2 7 3l3 1-1 2h-1c-1 0-1 0-2 1 1 1 2 1 4 1h1c1 1 0 1 1 1 1 1 2 1 3 2v-1h4v-1l2-1v-1c0-2 0-2 1-4l4 1-2 11c0 7 2 11 7 16-1 3-4 4-5 6l-1 2c-1 2-1 3-1 4l-1 4c1 7 4 13 8 19v1c0 1 1 2 2 3v2h-1c-4-2-8-6-11-10-13-12-23-25-30-40z" class="M"></path><path d="M271 466c1 0 1 1 2 1v2c-1 0-2-1-3-2l1-1zm-9-8c2 0 3 2 4 3l1 1h0v-1l1-2c0 2 1 3 3 4h2l1 1v-1 2h-2-2l-1 1h0l-1-1c-2-2-5-4-6-7z" class="F"></path><path d="M259 457l2 2 7 10c1 1 2 2 3 4h1c1-1 1 0 1-1h0c1 1 0 1 1 1 0 4 0 7 1 11h0l-9-12 1-1c-1-3-4-7-6-10l-2-4z" class="t"></path><path d="M276 445c0 1 0 1 1 1 0-1 1-2 1-3 0 7 2 11 7 16-1 3-4 4-5 6l-1 2c-1 2-1 3-1 4l-1 4h0-1c-1 4 0 6 0 10h0l-1-1h0c-1-4-1-7-1-11 0-2 1-7 2-8 2-4 2-8 2-12v-1h-1v2c-1 2-1 5-2 8l-2-2 1-2v-7h0c0-1 2-1 3-2l-1-1h-1l1-3z" class="C"></path><path d="M279 467l-1-2c-1-1 0-3 1-5 0-1 0-1 2-1 1 1 2 0 4 0-1 3-4 4-5 6l-1 2z" class="D"></path><path d="M257 445c2 0 4-2 6-2v1c1 1 1 2 2 2h0l2 1-1 1-1 1c1 0 1 1 2 1 0 1 0 2 1 3l1 2h0c1 2 1 2 3 3h2l-1 2 2 2-1 1v1l-1-1h-2c-2-1-3-2-3-4l-1 2v1h0l-1-1c-1-1-2-3-4-3h0c-3-3-5-7-6-10l1-1v-2z" class="H"></path><path d="M268 459c-1 0-3-2-3-3l1-1c-1-2-1-1-3-2v-1h1c2 0 4 2 5 3h0c1 2 1 2 3 3h2l-1 2 2 2-1 1v1l-1-1h-2c-2-1-3-2-3-4z" class="a"></path><path d="M257 445c2 0 4-2 6-2v1c1 1 1 2 2 2-1 0-2 1-3 2l-1 1c1 3 1 4 3 5v1c-1 1-1 1-1 2 1 1 1 2 2 2 1 1 1 1 1 2-1-1-2-3-4-3h0c-3-3-5-7-6-10l1-1v-2z" class="C"></path><path d="M257 445c2 0 4-2 6-2v1 2c-1 0-2 0-3 1l-1 1c-1 0-1-1-2-1v-2zm19-14l4 1-2 11c0 1-1 2-1 3-1 0-1 0-1-1l-1 3h1l1 1c-1 1-3 1-3 2h0v7h-2c-2-1-2-1-3-3h0l-1-2c-1-1-1-2-1-3-1 0-1-1-2-1l1-1 1-1-2-1h1c2-2 0-2 1-3 0-1 2-1 2-2 1-1 1-1 1-2 1 0 1 0 2 1h0l1-2v-1l2-1v-1c0-2 0-2 1-4z" class="B"></path><path d="M270 452c0 1 1 2 0 4l-1-1h0l-1-2 2-1z" class="C"></path><path d="M275 448h-2-1c1-1 1-1 1-2s0-1 1-2l2 1-1 3zm-2-11l2-1c-1 2-1 3-1 5-1 0-2 0-3 1 0 1 0 2-1 3h0l1 1c-1 2-3 1-2 3l1 3-2 1c-1-1-1-2-1-3-1 0-1-1-2-1l1-1 1-1-2-1h1c2-2 0-2 1-3 0-1 2-1 2-2 1-1 1-1 1-2 1 0 1 0 2 1h0l1-2v-1z" class="F"></path><path d="M239 419l8 5 7 4c2 1 5 2 7 3l3 1-1 2h-1c-1 0-1 0-2 1 1 1 2 1 4 1h1c1 1 0 1 1 1 1 1 2 1 3 2v-1h4l-1 2h0c-1-1-1-1-2-1 0 1 0 1-1 2 0 1-2 1-2 2-1 1 1 1-1 3h-1 0c-1 0-1-1-2-2v-1c-2 0-4 2-6 2v2l-1 1c1 3 3 7 6 10l-1 1-2-2 2 4c2 3 5 7 6 10l-1 1c-5-6-8-13-12-20l-4-9-1-2v-2l-4-10c-1-4-3-5-6-7l-1-1c0-1 0-1 1-2z" class="l"></path><path d="M245 429c1 0 2-1 3 0 2 1 1 0 2 1 1 0 1 1 2 1l-3 2v1l1 1v1l-1 1c0 1 2 1 0 2l-4-10zm9-1c2 1 5 2 7 3l3 1-1 2h-1c-1 0-1 0-2 1-1-1-2-1-3-2h-2c-1-1-1 0-2 0l-1-1 2-1v-3z" class="P"></path><path d="M259 457c-2-3-4-7-5-10-1-1-2-4-2-6 0-1 1-1 1-1 0-2-1-3 0-5h1l1-1h1l1 2c-1 1-2 1-3 2l-1 2h1v4c1 1 1 2 2 4 1 3 3 7 6 10l-1 1-2-2z" class="m"></path><path d="M239 419l8 5 7 4v3l-2 1v-1c-1 0-1-1-2-1-1-1 0 0-2-1-1-1-2 0-3 0-1-4-3-5-6-7l-1-1c0-1 0-1 1-2z" class="u"></path><path d="M257 436v1c2-1 3-1 5 0 0 1 1 2 2 2h1v-1c1-1 1 0 1-1 1 1 2 1 3 2v-1h4l-1 2h0c-1-1-1-1-2-1 0 1 0 1-1 2 0 1-2 1-2 2-1 1 1 1-1 3h-1 0c-1 0-1-1-2-2v-1c-2 0-4 2-6 2v2l-1 1c-1-2-1-3-2-4v-4h-1l1-2c1-1 2-1 3-2z" class="M"></path><path d="M263 441c1 1 2 2 2 4v1h0c-1 0-1-1-2-2v-1-2z" class="F"></path><path d="M257 436v1h2l-3 3-2-2h0c1-1 2-1 3-2z" class="i"></path><path d="M254 444c1-1 1-1 0-2h0l3-1v4 2l-1 1c-1-2-1-3-2-4z" class="F"></path><path d="M257 441l2-1h2c1 1 1 1 2 1v2c-2 0-4 2-6 2v-4z" class="C"></path><defs><linearGradient id="u" x1="266.607" y1="455.71" x2="251.752" y2="462.587" xlink:href="#B"><stop offset="0" stop-color="#aeacac"></stop><stop offset="1" stop-color="#dbdad9"></stop></linearGradient></defs><path fill="url(#u)" d="M245 450l-12-32h0l5 3 1 1c3 2 5 3 6 7l4 10v2l1 2 4 9c4 7 7 14 12 20l9 12 1 1h0c0-4-1-6 0-10h1 0c1 7 4 13 8 19v1c0 1 1 2 2 3v2h-1c-4-2-8-6-11-10-13-12-23-25-30-40z"></path><path d="M254 452c0 1 1 2 0 4-1-1-1-1-1-2l-1-2c-1-1-1-1-1-2l-1-3c-1-1-1-3-2-4h1 1l4 9z" class="N"></path><path d="M239 422c3 2 5 3 6 7l4 10v2l-2-1c0-1-1-1-1-2s0-2-1-3-1-3-2-5c0-1-2-2-2-3-1-2-2-3-2-5zm57-115l3 1c0-1 0-1 1-1v1 1h1c-2 1-3 1-4 2-2 1-2 1-4 1-1 2-2 3-3 5l-5 2c1 0 2 1 3 1v-1c0 1 0 2 1 3l4 7 1 1c1 2 1 3 2 5v1c1 5 0 7-3 10l-1 1h0l-2 1c1 1 1 2 2 3l2-1 1 1c2 1 2-1 4 1h-1l1 1v1h1l3 3 3-1c0 1 1 1 1 3l-1 2-1 1c-1 4-4 7-6 10l-4 3c-2 2-4 3-6 4-1 1-2 1-3 2l-4 2-3-1-1-1c0-1 0-3-1-5 1 0 1-2 1-3h-1l-1 1c-2-1-3 0-5 0l-1-4c0-1 0-1-2-1l1 4-3-2h0c-1-1-1-2-1-3l-1-3-1-1-1-3-1-2c-3-4-8-6-13-9l-1-2-2 3-2 2h0c-1 0-2-1-3-1l-1 2-1-2 1-2 1-3 1-1 1-2v-3c0-1-2 0-4-1l1-1c4 0 3-2 6-3 1 0 1 0 1-1l-1-3h-2l5-2c2-1 3-2 5-2h0c3-2 8-6 9-8v-1c-2-2-1-2-1-4 0 1 1 1 2 1 1 1 0 2 2 1 1 0 1 0 1-1 2-1 2-2 3-3h1l1 1 1-2c2 0 3-1 5 0l-1 1v1l3-1v-1c2 0 2 0 4-1h1 1c2 0 6-3 9-4h2z" class="D"></path><path d="M279 360h-2c-2-1-5-2-6-4v-1l5 1 13 2-3 3-7-1z" class="c"></path><path d="M259 348c2 0 4 1 5 2 2 2 5 1 6 3 0 2-1 1-2 2l-1-1c-2-1-3-2-5-2v1l2 1v1c-2 0-3-1-4-2-2-1-3 1-5-1 0-2-1-2 0-4h4z" class="a"></path><path d="M251 346h1c2 1 3 1 5 1 1 0 2 1 2 1h-4c-1 2 0 2 0 4 2 2 3 0 5 1 1 1 2 2 4 2 2 1 4 3 6 5v1c-1-1-2-2-4-3-1 0-2-2-4-2l-1 3c-3-4-8-6-13-9l3-4z" class="C"></path><path d="M261 359l1-3c2 0 3 2 4 2 2 1 3 2 4 3 0 2-1 2 0 4-1 1-1 1-2 1l-1 1c1 0 1 1 1 2l1 4-3-2h0c-1-1-1-2-1-3l-1-3-1-1-1-3-1-2z" class="O"></path><path d="M262 361h1c1 0 1 0 2-1 1 1 1 1 1 2l-1 1s-1 1-1 2l-1-1-1-3z" class="C"></path><path d="M257 343l2-2v-1h1c1 0 1 1 2 1h1 0 4c1 0 1 1 2 1h1l3 2v1c1 1 3 2 4 2h-1c-1 1 0 1-1 1 2 1 3 2 4 3-1 1-1 1-2 1l-1-1c-3 1-8-1-11-2l-1 1c-1-1-3-2-5-2 0 0-1-1-2-1-2 0-3 0-5-1h-1c1-1 3-1 4-2-1-2-1-1 0-2l1-1 1 2z" class="I"></path><path d="M252 346c2 0 5 0 6-1 2 1 3 1 4 2v1c2 1 2-1 3 1l-1 1c-1-1-3-2-5-2 0 0-1-1-2-1-2 0-3 0-5-1z" class="V"></path><path d="M257 343l2-2v-1h1c1 0 1 1 2 1h1 0 4c1 0 1 1 2 1h1l3 2v1c1 1 3 2 4 2h-1c-1 1 0 1-1 1-6-1-13-2-18-5z" class="a"></path><path d="M263 341h4c1 0 1 1 2 1h1l3 2v1c-1 1-1 1-2 1v-1c-3 2-4-1-7-1h-1c-1-1-1-2 0-3h0z" class="N"></path><defs><linearGradient id="v" x1="291.538" y1="360.755" x2="282.528" y2="343.772" xlink:href="#B"><stop offset="0" stop-color="#a19f9f"></stop><stop offset="1" stop-color="#c2c1bf"></stop></linearGradient></defs><path fill="url(#v)" d="M271 337l1-1c2 4 5 5 8 7l3 3 2 2h5c1 1 1 2 2 3l2-1 1 1c2 1 2-1 4 1h-1l1 1v1h1l3 3c-3 0-6 1-9 1h-5l-13-2c1-2 1-2 0-3l1-1c1 0 1 0 2-1-1-1-2-2-4-3 1 0 0 0 1-1h1c-1 0-3-1-4-2v-1l-3-2c0-1-1-2-2-3v-1l1-1c1-1 1 0 2 0z"></path><path d="M294 350l1 1c2 1 2-1 4 1h-1-1c-1 1-2 1-3 1h-2c-2 1-3 1-5 1l-1-1 1-1c2 0 3 0 4-1h1 0l2-1z" class="O"></path><path d="M285 348h5c1 1 1 2 2 3h0-5c-3 0-4-1-5-3h0 3z" class="a"></path><path d="M271 337l1-1c2 4 5 5 8 7l3 3 2 2h-3c-1-2-4-2-6-3l-3-1-3-2c0-1-1-2-2-3v-1l1-1c1-1 1 0 2 0z" class="C"></path><path d="M270 342c0-1-1-2-2-3v-1l1-1c1-1 1 0 2 0l-1 3c2 2 3 2 6 4h0v1h0l-3-1-3-2z" class="F"></path><path d="M303 357l3-1c0 1 1 1 1 3l-1 2-1 1c-1 4-4 7-6 10l-4 3c-2 2-4 3-6 4-1 1-2 1-3 2l-4 2-3-1-1-1c0-1 0-3-1-5 1 0 1-2 1-3h-1l-1 1c-2-1-3 0-5 0l-1-4c0-1 0-1-2-1 0-1 0-2-1-2l1-1c1 0 1 0 2-1-1-2 0-2 0-4v-1c3 2 7 6 11 6 1 1 1 0 1 0l2-1h2l1-1c1 0 1 0 2-1l-1-1c-1 0-3 0-4 1h-1l-1-1c-1-1-2-2-3-2l7 1 3-3h5c3 0 6-1 9-1z" class="E"></path><path d="M290 360l8-1c-1 1-2 3-4 4l-1-1c-1-1-2-1-3-2z" class="U"></path><path d="M288 362h1v1h2l1 1c-3 2-6 3-10 2l2-1h2l1-1c1 0 1 0 2-1l-1-1z" class="B"></path><path d="M279 360l7 1 4-1c1 1 2 1 3 2l1 1-2 1-1-1h-2v-1h-1c-1 0-3 0-4 1h-1l-1-1c-1-1-2-2-3-2zm4 11l9-3c4-2 7-5 10-8 2-1 3-2 5-1l-1 2-1 1c-2 0-2 0-4 2-1 1-2 3-3 4l-1-2c-2 1-4 3-6 4l-2 1h-1c-2 0-4 0-6 1h-3c1-1 3-1 4-1z" class="C"></path><path d="M270 361c1 1 3 3 4 5 1 1 2 3 4 4l5 1c-1 0-3 0-4 1l-1 1h-1l-1 1c-2-1-3 0-5 0l-1-4c0-1 0-1-2-1 0-1 0-2-1-2l1-1c1 0 1 0 2-1-1-2 0-2 0-4z" class="U"></path><path d="M270 365h1v1c0 1 1 2 2 2l1 1v3c-1-1-1 0-2-1 0 0-1 0-2-1 0-1 0-1-2-1 0-1 0-2-1-2l1-1c1 0 1 0 2-1z" class="H"></path><path d="M298 368c1-1 2-3 3-4 2-2 2-2 4-2-1 4-4 7-6 10l-4 3c-2 2-4 3-6 4-1 1-2 1-3 2l-4 2-3-1-1-1c0-1 0-3-1-5 1 0 1-2 1-3l1-1h3c2-1 4-1 6-1h1l2-1c2-1 4-3 6-4l1 2z" class="O"></path><path d="M291 370c2-1 4-3 6-4l1 2c-2 2-4 4-6 5l-1 2c-2 0-2 0-3-1l2-1v-2h-1l2-1z" class="D"></path><path d="M291 370h2v1c-1 1-2 1-3 2v-2h-1l2-1z" class="O"></path><path d="M279 372h3c2-1 4-1 6-1h1 1v2l-2 1c-1 0-1 2-3 2-1 1-1 0-2 1 0 3-1 3-1 6l-3-1-1-1c0-1 0-3-1-5 1 0 1-2 1-3l1-1z" class="N"></path><path d="M279 372h3c2-1 4-1 6-1-1 2-1 2-3 2s-4 0-6 1l2 2h-1c-1 1-1 3-2 5 0-1 0-3-1-5 1 0 1-2 1-3l1-1z" class="O"></path><path d="M288 320v-1c0 1 0 2 1 3l4 7 1 1c1 2 1 3 2 5v1c1 5 0 7-3 10l-1 1h0l-2 1h-5l-2-2-3-3c-3-2-6-3-8-7l-1 1c-1 0-1-1-2 0l-1 1v1c1 1 2 2 2 3h-1c-1 0-1-1-2-1h-4 0-1c-1 0-1-1-2-1h-1v1l-2 2-1-2c1-1 1-2 2-3 2-2 3-3 6-5 2-1 3-3 5-4l1 1 1-1 1-1 2-1c1-1 1-1 3-2s2-1 5 0c0-1 1-1 2-2h0c1 0 2 0 3-1l1-2z" class="M"></path><path d="M283 346v-2c1-1 2-1 3-1v1c3 0 3-3 5-5l1 2v1l2 1v1l-2 1v2h0l-2 1h-5l-2-2zm-6-21c2 1 5 4 8 4 0 1 1 1 1 2 1 1 2 2 3 2-1 2-1 4-1 5v1c-1-1-1-2-1-4 0-1 0 0-1-1v-1l-2-2h-1v2l-2 1c-2-2-6-5-9-5h-1l1-1 2-1c1-1 1-1 3-2z" class="F"></path><path d="M288 320v-1c0 1 0 2 1 3l4 7 1 1c1 2 1 3 2 5v1c1 5 0 7-3 10l-1 1v-2l2-1v-1l-2-1v-1l1-1v-4c-1-1-3-3-4-3s-2-1-3-2c0-1-1-1-1-2-3 0-6-3-8-4 2-1 2-1 5 0 0-1 1-1 2-2h0c1 0 2 0 3-1l1-2z" class="S"></path><path d="M293 329l1 1c1 2 1 3 2 5h-2c-1-1-2-1-2-3h0c-1-1-2-1-2-2 1 0 2-1 3-1z" class="Q"></path><path d="M288 320v-1c0 1 0 2 1 3l4 7c-1 0-2 1-3 1l-1-2c-2-1-3-3-5-5h0c1 0 2 0 3-1l1-2z" class="P"></path><path d="M264 333c2-1 3-3 5-4l1 1 1-1h1c3 0 7 3 9 5 3 2 4 3 4 6 0 1-1 2-2 2h-1c1-2 2-3 2-5-1 0-2 1-3 2v3l-1 1c-3-2-6-3-8-7l-1 1c-1 0-1-1-2 0l-1 1v1c1 1 2 2 2 3h-1c-1 0-1-1-2-1h-4 0-1c-1 0-1-1-2-1h-1v1l-2 2-1-2c1-1 1-2 2-3 2-2 3-3 6-5z" class="M"></path><path d="M258 338h3c1-2 0-2 2-2l2 2c-1 1-1 1-2 3h0-1c-1 0-1-1-2-1h-1v1l-2 2-1-2c1-1 1-2 2-3z" class="B"></path><path d="M265 338v-1c0-1 1-2 2-3 0 1 0 1 1 1l3-2 1 1 2-1 1 1c1 0 1 1 2 2h1 1 1c1 2 1 2 0 4h-1-1l1-1v-1c-1 0-2 0-3-1-1 0-1-1-1-2h-1l-2 1-1 1c-1 0-1-1-2 0l-1 1v1c1 1 2 2 2 3h-1c-1 0-1-1-2-1h-4c1-2 1-2 2-3z" class="D"></path><path d="M272 329c3 0 7 3 9 5 3 2 4 3 4 6 0 1-1 2-2 2h-1c1-2 2-3 2-5-1 0-2 1-3 2v3l-1 1c-3-2-6-3-8-7l2-1h1c0 1 0 2 1 2 1 1 2 1 3 1v1l-1 1h1 1c1-2 1-2 0-4h-1v-1l-1-1c-2-2-3-2-5-3h-2l1-2z" class="p"></path><path d="M296 307l3 1c0-1 0-1 1-1v1 1h1c-2 1-3 1-4 2-2 1-2 1-4 1-1 2-2 3-3 5l-5 2c1 0 2 1 3 1l-1 2c-1 1-2 1-3 1h0c-1 1-2 1-2 2-3-1-3-1-5 0s-2 1-3 2l-2 1-1 1-1 1-1-1c-2 1-3 3-5 4-3 2-4 3-6 5-1 1-1 2-2 3l-1 1c-1 1-1 0 0 2-1 1-3 1-4 2l-3 4-1-2-2 3-2 2h0c-1 0-2-1-3-1l-1 2-1-2 1-2 1-3 1-1 1-2v-3c0-1-2 0-4-1l1-1c4 0 3-2 6-3 1 0 1 0 1-1l-1-3h-2l5-2c2-1 3-2 5-2h0c3-2 8-6 9-8v-1c-2-2-1-2-1-4 0 1 1 1 2 1 1 1 0 2 2 1 1 0 1 0 1-1 2-1 2-2 3-3h1l1 1 1-2c2 0 3-1 5 0l-1 1v1l3-1v-1c2 0 2 0 4-1h1 1c2 0 6-3 9-4h2z" class="o"></path><path d="M246 345l-2-1c1-1 1-1 3-2v1l-1 2zm35-28v-1l1-1h4c-2 2-2 2-5 2z" class="e"></path><path d="M269 319h2v1l-2 2h0-2 0c1-1 1-2 2-2v-1z" class="a"></path><path d="M257 331l2-1v1c-1 2-1 1-2 1-2 1-5 5-7 6-1 1-1 1-2 1v-1c3-2 5-7 9-7z" class="e"></path><path d="M272 312c2 0 3-1 5 0l-1 1v1c-1 2-3 4-4 4h-4 0c-1 1-2 1-4 2l7-6 1-2z" class="a"></path><path d="M247 343h3l-3 5-2 3-2 2h0c-1 0-2-1-3-1l-1 2-1-2 1-2 1-3 1-1 1 1c2 0 2 0 4-2l1-2z" class="O"></path><path d="M279 321l1 1c1 0 3 0 4 1h0c-1 1-2 1-2 2-3-1-3-1-5 0s-2 1-3 2l-2 1-1 1-1 1-1-1c-2 1-3 3-5 4 0-1 0-2-1-3h0c5-3 11-7 16-9z" class="p"></path><path d="M250 343a79.93 79.93 0 0 1 13-13c1 1 1 2 1 3-3 2-4 3-6 5-1 1-1 2-2 3l-1 1c-1 1-1 0 0 2-1 1-3 1-4 2l-3 4-1-2 3-5z" class="G"></path><path d="M270 313l1 1-7 6c-8 6-16 15-22 24v-3c0-1-2 0-4-1l1-1c4 0 3-2 6-3 1 0 1 0 1-1l-1-3h-2l5-2c2-1 3-2 5-2h0c3-2 8-6 9-8v-1c-2-2-1-2-1-4 0 1 1 1 2 1 1 1 0 2 2 1 1 0 1 0 1-1 2-1 2-2 3-3h1z" class="I"></path><path d="M245 332l6-1-9 10c0-1-2 0-4-1l1-1c4 0 3-2 6-3 1 0 1 0 1-1l-1-3z" class="B"></path><path d="M296 307l3 1c0-1 0-1 1-1v1 1h1c-2 1-3 1-4 2-2 1-2 1-4 1-1 2-2 3-3 5l-5 2c1 0 2 1 3 1l-1 2c-1 1-2 1-3 1-1-1-3-1-4-1l-1-1h0l1-1c0-1 1-1 1-3 3 0 3 0 5-2h0l2 1c1-1 1-2 2-3l-1-1c-2 0-4 1-5 2s-1 0-2 0c1-1 1-1 2-1s1-1 1-2h-1 1c2 0 6-3 9-4h2z" class="N"></path><path d="M279 321l6-2c1 0 2 1 3 1l-1 2c-1 1-2 1-3 1-1-1-3-1-4-1l-1-1h0z" class="J"></path><path d="M296 307l3 1c0-1 0-1 1-1v1 1h1c-2 1-3 1-4 2-2 1-2 1-4 1-1 1-1 1-2 0v-1c2-2 2-1 4-2l-1-2h2z" class="e"></path><path d="M233 228c-15-22-36-32-61-37l-6-1-1-11h205l56-1c14 0 28 1 42-1 12-1 25-4 36-9 3-1 7-3 9-6 3-3 4-8 5-13 0 1 1 2 1 2 5 12 15 17 26 22 7 2 15 4 22 5 11 1 23 0 34 0h59l130 1c25 0 49-1 73 0l1 10-15 3c-17 5-34 16-44 31-8 13-11 26-11 41v7l1 2c0 1 1 2 1 3 1 1 1 2 2 3h0c2 2 3 4 5 5-1 0-2 0-2-1l-1 1-1 1 4 4h0l-1-1c-1 0-2 0-3-1h0l-3-3v-1h-1c-1 0-1-1-2-2h-1c3 5 8 8 13 10h8c9-2 13-6 18-14l1 2h1l1 1v1h-1c-3 3-5 6-9 9l-4 4h-2c-2 2-4 3-7 3h-12l-2 2-3 1h-2l-1 2v1l-8-1c-1-1-4-1-5-1-2 1-4 0-7 1l-3 1h-1v-1l-2-2-1-1-4 3-1 2h-1c-1 1-1 1-2 1v1l-5 5v1 1s1 1 1 2v1 1l-2 2v1 1h-1c-2 1-3 2-4 3h-1c-1 1-1 1-1 2 0 4 0 7 2 11h-4l-2-3-6-6c-3-3-6-6-8-9l-5-7c-1 1-2 2-3 2l-1-1-2-2c-1-1-1-2-3-3-1 0-2-3-3-4l-2-3c-1 1-1 1-2 1l-2-3-1 1v1c-5-5-12-11-15-17l-3-3h0c-1-1-2-1-3-1h1c0 1 1 3 1 5l3 3c2 2 4 5 5 7v1l-10-9c-2-1-5-2-7-4v3c3 2 6 4 8 5s4 3 5 4c0 1 0 1-1 1l-2-2c-2-1-3-2-5-3h-1c-2-3-8-7-12-8h-2c-2-1-4-1-6-2-31-16-64-28-99-27-4 1-9 1-12 2h-3v-1l2-2c-3-1-4 2-7 1-1 0-2 1-3 1h-2-1l1-1c0-1 0-2-1-2v-1-2h-1l-1-1 2-1c0-2-1-2-2-3h0l-1-2c1-1 1-1 1-2v-1h-2c-1 1-2 1-3 1l-2-3c-1 1-1 1-2 1h-1-2v-1h-2c-1-1-3-1-5-1l-3-1c-1 0-2-1-3-1-3 0-5-3-6-4-2 0-3 1-4 2v1h-2l-2 1-2-2-1 1h-1c0-2-1-2-2-3l-1-1 1 5c0 3 1 6 0 8h0l-1 1v2c-2 0-1-1-3 0h-3 0-1l-2 1h-1-4v6l1 1-1 1h-1c-1-1-1-1-3-1h0l-1 3-8 1-12 3c-1 0-4 2-5 2-32 13-60 30-83 56-2 0-3 0-5 2 0 1-1 2-2 2h-1v-1c-2-1-2-1-2-3l-4 6c-1 1-2 3-3 4l-10 15-2 2c-1 1-1 2-2 4l-1 2-3 5-2 3h0c-1 1-2 1-2 1l-1 1h-2c-1 0-2 2-3 2h-1l1-2-1-1-1 1c-2 0-2 1-4 0l1-2c0-2-1-2-1-3l-3 1-3-3h-1v-1l-1-1h1c-2-2-2 0-4-1l-1-1-2 1c-1-1-1-2-2-3l2-1h0l1-1c3-3 4-5 3-10v-1c-1-2-1-3-2-5l-1-1-4-7c-1-1-1-2-1-3v1c-1 0-2-1-3-1l5-2c1-2 2-3 3-5 2 0 2 0 4-1 1-1 2-1 4-2h-1v-1-1c-1 0-1 0-1 1l-3-1 2-1 8-4 3-1c-1-1-3-2-4-3l-1-1c-2-1-3-2-4-4l1-1v-2l1-1-2-1h-2l-1 1c-2-3-3-7-4-10 0-2 0-3-1-4h-1v2h-1l-1-1c1-1 1-2 0-4v-1s0-1-1-1l-3-3-1-2v2l-2 2c-1 2-1 2-3 4 0 0-1 0-2 1l-1-1h0-3v1l-4-5h-2l-2-1-1-1v-1c0-2 0-3 1-4l-1-2c-2 1-2 3-3 4l1-9c-2 1-3 1-3 3-1 3-1 6-2 8v1c0 2-1 4-2 5h0c0-1 0-1 1-2h-1c0-2 0-4 1-5 1-3 0-6 1-8s1-3 0-5v-5-2l-2-4v4c0 2 0 6-1 7l-2 1h-1 0c0-4-1-7-3-10 0-1 1-2 2-3v-1-1c1-1 0-2 0-3l-2-2h0v-2l-1-1-3 1h-1c-2-2-3-2-5-4l-1-1-4 2z" class="y"></path><path d="M857 181h3c1 2 1 4 1 5-1 1-3 1-4 1h-1v-1c0-2 0-3 1-5z" class="M"></path><path d="M252 232c1 2 2 7 3 9v4c0 2 0 6-1 7l-2 1h-1 0c0-4-1-7-3-10 0-1 1-2 2-3 0 1-1 2-1 3l1 2v-2h0l1-1v-1-1-1c0-1 0-1 1-2l1 2v1-1-2l-1-1v-1h0v-2-1z" class="J"></path><path d="M252 232c1 2 2 7 3 9v4c0 2 0 6-1 7 0-1 0-2-1-4h-1l1-2h0l-2-2v-2l2-2v-1-2l-1-1v-1h0v-2-1z" class="c"></path><path d="M798 194c3-2 7-3 11-4 2-1 5 0 7-1 1-1 2-1 3-1l-7 5c-5 3-10 7-15 12-7 9-12 18-15 30 0-4 2-9 1-13l-1-7c-1 2-5 7-5 9 0 1 0 0-1 1l-1 3v-1l-1-1c1-1 1-2 1-3 2-2 3-4 4-7v-4h0c1-2 1-4 1-6l-1-4 2-2c1 1 1 1 1 2 0 2 0 3 1 4 1-1 2-1 3-2 2-4 8-8 11-11l1 1z" class="F"></path><path d="M783 213c1 0 1 0 2 1v1h-2v-1-1z" class="B"></path><path d="M797 193l1 1c-4 4-9 7-13 12-2 3-4 6-6 10v-4h0c1-2 1-4 1-6l-1-4 2-2c1 1 1 1 1 2 0 2 0 3 1 4 1-1 2-1 3-2 2-4 8-8 11-11z" class="u"></path><path d="M797 205h-2v1c-1 1-1 2-3 3h-1c0-1 0-1 1-2 0-1 1-1 1-2l1-1c1-3 8-10 11-11v-1c3-1 5-1 8-1l-1 1v1c-5 3-10 7-15 12z" class="H"></path><defs><linearGradient id="w" x1="854.794" y1="176.623" x2="796.347" y2="189.734" xlink:href="#B"><stop offset="0" stop-color="#92908f"></stop><stop offset="1" stop-color="#c1c1c0"></stop></linearGradient></defs><path fill="url(#w)" d="M750 181h107c-1 2-1 3-1 5v1h1-36-14-53c1-1 3-1 5-1 1-1 2 0 3 0 1 1 3 0 4 0h10 1 2 0 5 3 9 4 0c-1-2-1-2-2-3h-2c-3-2-9-1-12-1h-22c-3 0-6 0-9-1h-3z"></path><path d="M487 176c8-3 25-7 30-16 1-2 1-4 1-6 2 2 3 4 4 6 7 8 20 14 30 17l3 1c-2 0-5 1-7 0-4-1-10-1-14-1h-1l1 1h0-2v-1c-3-1-8-1-12-1-3 0-6 0-9 1h-6-9c-3 1-7 1-10 1-2-1-3 0-4 0 2 0 3-1 4-2h1z" class="O"></path><path d="M521 169c1-1 2 0 4 0-1 0-1 0-2 1h0c-2 0-3 0-4 1-5 2-11 4-16 5h-4-1l1-1c1 0 2 0 4-1 2 0 3 0 5-1 4-1 8-3 13-4z" class="C"></path><defs><linearGradient id="x" x1="577.414" y1="164.233" x2="615.831" y2="203.01" xlink:href="#B"><stop offset="0" stop-color="#bcbab9"></stop><stop offset="1" stop-color="#e4e4e4"></stop></linearGradient></defs><path fill="url(#x)" d="M534 178h0l-1-1h1c4 0 10 0 14 1 2 1 5 0 7 0 10 2 20 2 31 2l30 1h10c1 0 3-1 4 0h5c0 1 0 2-1 3l-1 3c4 0 9 0 13-1 2 0 8 0 10 1h-26l-43 1c-3 0-4 1-6 3l-1-1h-2-2-8c-1 0-1 1-2 1h-5-13v-1c-2 0-4-1-6-1h-1-3c3 0 5 0 7-1h1-5c2 0 4 0 6-1h3 8c2-1 3 0 4-1 0-2 0-2-1-4-2-1-3-1-4-1s-1 0-2-1h-6c-3-1-6-1-9-1-2-1-4-1-6-1z"></path><path d="M546 188h41c-3 0-4 1-6 3l-1-1h-2-2-8c-1 0-1 1-2 1h-5-13v-1c-2 0-4-1-6-1h-1-3c3 0 5 0 7-1h1z" class="c"></path><defs><linearGradient id="y" x1="234.435" y1="223.82" x2="251.37" y2="215.213" xlink:href="#B"><stop offset="0" stop-color="#bfbdbc"></stop><stop offset="1" stop-color="#e9e8e7"></stop></linearGradient></defs><path fill="url(#y)" d="M217 188h21v3h4 1 6c-1 1-1 2-2 2l-1 1c0 2 1 3 1 4 0 2 0 3-1 4l3 3-1 1 1 2-1 1-4-4c0 1 1 2 2 3s1 2 2 3l3 3h-1c2 2 3 4 5 7 0 1 1 3 2 4 1 2 4 7 3 9v4h1l1 9v8c-2 1-3 1-3 3-1 3-1 6-2 8v1c0 2-1 4-2 5h0c0-1 0-1 1-2h-1c0-2 0-4 1-5 1-3 0-6 1-8s1-3 0-5v-5-2l-2-4c-1-2-2-7-3-9-6-16-18-30-33-39-2-2-5-2-7-4h1v-1h4z"></path><path d="M260 238h1l1 9v8c-2 1-3 1-3 3-1 3-1 6-2 8v1c0 2-1 4-2 5h0c0-1 0-1 1-2h-1c0-2 0-4 1-5 1-3 0-6 1-8s1-3 0-5h1 0l1-1c0-3 0-7-1-10h1v2c1 2 0 4 1 6v4h1v-3-1c-1-3 0-4-1-6v-5z" class="o"></path><defs><linearGradient id="z" x1="217.584" y1="188.011" x2="237.012" y2="191.345" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#242322"></stop></linearGradient></defs><path fill="url(#z)" d="M217 188h21v3h4 1 6c-1 1-1 2-2 2l-1 1c0 2 1 3 1 4 0 2 0 3-1 4l3 3-1 1 1 2-1 1-4-4v-1c-5-5-13-12-20-14-4 0-7 0-10-1h-1v-1h4z"></path><path d="M238 197l-1-1c-2-1-3-2-4-5l1-1 3 2 1-1h4c-2 1-4 1-6 2 1 2 2 2 2 4z" class="K"></path><path d="M242 191h1 6c-1 1-1 2-2 2l-1 1c0 2 1 3 1 4 0 2 0 3-1 4l3 3-1 1c-3-3-6-7-10-9 0-2-1-2-2-4 2-1 4-1 6-2z" class="l"></path><path d="M246 199v1c-2 0-3-1-4-3l1-1 1 1 2 2z" class="M"></path><path d="M244 197v-1c-1 0-2-1-2 0h-3v-1c1 0 2-1 3-1h1c0-1 1-1 1-2 2 0 2 0 3 1l-1 1c0 2 1 3 1 4l-1 1-2-2z" class="R"></path><path d="M630 181h120 3c3 1 6 1 9 1h22c3 0 9-1 12 1h2c1 1 1 1 2 3h0-4-9-3-5 0-2-1-10c-1 0-3 1-4 0-1 0-2-1-3 0-2 0-4 0-5 1h-6-13-57-22c-2-1-8-1-10-1-4 1-9 1-13 1l1-3c1-1 1-2 1-3h-5z" class="v"></path><path d="M455 180l32-4h-1c-1 1-2 2-4 2 1 0 2-1 4 0 3 0 7 0 10-1h9 6c3-1 6-1 9-1 4 0 9 0 12 1v1h2c2 0 4 0 6 1 3 0 6 0 9 1h6c1 1 1 1 2 1s2 0 4 1c1 2 1 2 1 4-1 1-2 0-4 1h-8-3c-2 1-4 1-6 1l-10-1h-37-8l-26 1-19-1c-5-1-11 0-17 0-2 2-5 3-8 3h0c-5 1-8 4-12 6l-5 6-5 5h-1c1 1 1 3 1 4v1l-4 5h2v2c-2 2-6 8-6 10l-2 3-4 6h-1c0-1 1-2 2-4h-1-1v1c-2 1-3 2-4 3-3 4-6 7-8 12l-1 1h0l-2 3c0-1-1-2-1-3l-1 1-1-1c-1 1-1 2-2 2l-2 2c-1-2 0-4 0-5h-2v-3-2c-1-2-1-3-1-5 1-1 2-3 2-5h0c1-3 2-6 4-8l1 1 1-1c1-1 1-1 1-2l1-1c3-2 6-6 8-8l-1-3c1 0 1-1 2-1v-1-2h0v-3l1-2v-1l1-2c-1-2 0-4-2-6v1c-2 0-2-1-4 1l-1-2-3 1h0c-1-2-2-3-3-4s-1-1 0-2l-2-1c-3 1-7 1-10 1v-1h-4c-1 1-1 0-2 0-2 0-2 0-4 1l-5 1h-1c-1-1-3 0-4 0-6 0-12-1-17 0h-7-6-2l-4-2c-3 0-7 1-10 0-3 0-6 0-9 1l-6 3-2-2h0-9-3-6-6-1-4v-3h-21c-4-1-9-1-13-1h-37v-6l288-1z" class="o"></path><path d="M282 187l12 1v1c-3 0-7 1-10 0l-2-2z" class="G"></path><path d="M346 187h12l2 2c-3 1-7 1-10 1v-1h-4c-1 1-1 0-2 0l2-2z" class="Y"></path><path d="M403 187c7-1 15 0 21 0-2 2-5 3-8 3h0c-5 1-8 4-12 6v-2c1-1 2-1 2-2h-8l-4 4-1-1 4-4h1c1-1 2-2 2-4h3z" class="I"></path><path d="M400 187h3l4 1c-1 1-2 1-3 1l-6 3-4 4-1-1 4-4h1c1-1 2-2 2-4z" class="s"></path><path d="M404 189c3 0 9-1 12 1-5 1-8 4-12 6v-2c1-1 2-1 2-2h-8l6-3z" class="i"></path><path d="M455 180l32-4h-1c-1 1-2 2-4 2 1 0 2-1 4 0 3 0 7 0 10-1h9 6c3-1 6-1 9-1 4 0 9 0 12 1v1c-16-1-32 1-49 2l-15 1c-2 1-4 1-6 1s-3-1-4-1c-1-1-2-1-3-1z" class="f"></path><path d="M238 188l44-1 2 2c-3 0-6 0-9 1l-6 3-2-2h0-9-3-6-6-1-4v-3z" class="c"></path><path d="M294 188l52-1-2 2c-2 0-2 0-4 1l-5 1h-1c-1-1-3 0-4 0-6 0-12-1-17 0h-7-6-2l-4-2v-1z" class="J"></path><defs><linearGradient id="AA" x1="388.942" y1="198.001" x2="370.058" y2="178.999" xlink:href="#B"><stop offset="0" stop-color="#191817"></stop><stop offset="1" stop-color="#373332"></stop></linearGradient></defs><path fill="url(#AA)" d="M358 187h42c0 2-1 3-2 4h-1-8-3c-2 0-9-1-12 0 0 1-1 1-2 2 0 1 1 1 1 2v1c-2 0-2-1-4 1l-1-2-3 1h0c-1-2-2-3-3-4s-1-1 0-2l-2-1-2-2z"></path><path d="M362 190c2 1 5 2 6 5h0l-3 1h0c-1-2-2-3-3-4s-1-1 0-2z" class="i"></path><path d="M373 195c0-1-1-1-1-2 1-1 2-1 2-2 3-1 10 0 12 0h3 8l-4 4-3 3c-3 3-6 6-9 8l-7 7-2 3-1-3c1 0 1-1 2-1v-1-2h0v-3l1-2v-1l1-2c-1-2 0-4-2-6z" class="T"></path><path d="M380 197h1l1 2-3 2c-1-1-1-1-1-2 0-2 1-1 2-2z" class="B"></path><path d="M378 204h0c3-1 6-6 8-6l1 1c-1 1-2 1-3 2-2 0-3 2-4 3l1 2-7 7-2 3-1-3c1 0 1-1 2-1v-1-2h0l4-3 1-2z" class="H"></path><path d="M373 209l4-3c0 3-3 4-3 7l-2 3-1-3c1 0 1-1 2-1v-1-2h0z" class="B"></path><path d="M373 195c0-1-1-1-1-2 1-1 2-1 2-2 3-1 10 0 12 0h3 8l-4 4-3 3-1-1c0-1 0-2 1-3v-1h-5-3c0 1-1 1-1 1-1 0-2 0-3 1 0 0-1 0-2 1h1v3l-1 1 1 1v1l-1 1c0 1 1 1 2 1l-1 2-4 3v-3l1-2v-1l1-2c-1-2 0-4-2-6z" class="q"></path><path d="M398 192h8c0 1-1 1-2 2v2l-5 6-5 5h-1c1 1 1 3 1 4v1l-4 5h2v2c-2 2-6 8-6 10l-2 3-4 6h-1c0-1 1-2 2-4h-1-1v1c-2 1-3 2-4 3-3 4-6 7-8 12l-1 1h0l-2 3c0-1-1-2-1-3l-1 1-1-1c-1 1-1 2-2 2l-2 2c-1-2 0-4 0-5h-2v-3-2c-1-2-1-3-1-5 1-1 2-3 2-5h0c1-3 2-6 4-8l1 1 1-1c1-1 1-1 1-2l1-1c3-2 6-6 8-8l2-3 7-7c3-2 6-5 9-8l3-3 1 1 4-4z" class="e"></path><path d="M393 207c1 1 1 3 1 4v1h-2l-2-1 3-4z" class="u"></path><path d="M366 231l8-10c3-5 8-12 13-16v3c0 1-2 2-2 3l-8 10c-2 2-4 5-5 7-1 1-2 2-3 4l-1 1-2-2z" class="H"></path><path d="M368 233l-2 2c-1 1-1 2-1 3-3 1-4 4-5 6l-1 1c0 1 0 0-1 1 0 1 0 1-1 2 1 1 1 1 2 1 1-1 2-3 4-5 1-2 2-3 3-4h1v-1-2c2-3 4-5 6-7 1-2 2-4 4-6h0c0 1-1 2-1 3l1-1 2-2 1-2 2-2 1-2 1-1c1-1 1-2 2-2v-1h2l-7 8c-1 2-2 3-3 5 0 1 1 1 0 3 0 1-1 1-1 2-2 2-4 5-6 7-1 1-1 2-2 4-2 2-3 3-4 5 0 1 1 1 2 2l-1 1h0l-2 3c0-1-1-2-1-3l-1 1-1-1c-1 1-1 2-2 2l-2 2c-1-2 0-4 0-5 0-2 0-2-1-4 0-1 1-1 1-2h0 1c2-3 4-6 6-10l2-3 2 2z" class="W"></path><path d="M378 227c0 1 1 1 0 3 0 1-1 1-1 2-2 2-4 5-6 7-1 1-1 2-2 4-2 2-3 3-4 5 0 1 1 1 2 2l-1 1h0l-2 3c0-1-1-2-1-3l-1 1-1-1c-1 1-1 2-2 2l19-26z" class="G"></path><path d="M390 211l2 1h2l-4 5h2v2c-2 2-6 8-6 10l-2 3-4 6h-1c0-1 1-2 2-4h-1-1v1c-2 1-3 2-4 3-3 4-6 7-8 12-1-1-2-1-2-2 1-2 2-3 4-5 1-2 1-3 2-4 2-2 4-5 6-7 0-1 1-1 1-2 1-2 0-2 0-3 1-2 2-3 3-5l7-8 2-3z" class="J"></path><path d="M392 212h2l-4 5-7 10c-1 0-1 0-1-1l3-6c0-1 1-2 2-4l2-1 3-3z" class="s"></path><path d="M390 217h2v2c-2 2-6 8-6 10l-2 3-4 6h-1c0-1 1-2 2-4h-1-1c0-1 0 0 1-1l3-6 7-10z" class="K"></path><path d="M390 198l3-3 1 1c-1 1-5 4-5 6-1 0 0 1 0 2-1 0-2 1-2 1-5 4-10 11-13 16l-8 10-2 3c-2 4-4 7-6 10h-1 0c0 1-1 1-1 2 1 2 1 2 1 4h-2v-3-2c-1-2-1-3-1-5 1-1 2-3 2-5h0c1-3 2-6 4-8l1 1 1-1c1-1 1-1 1-2l1-1c3-2 6-6 8-8l2-3 7-7c3-2 6-5 9-8z" class="F"></path><path d="M362 227l1 1h1l1-2h2c-1 1-2 2-2 3-1 1-1 0-1 2-1 0-2 2-3 2h0c-1 3-3 6-5 8 0 2 0 2 1 3h0c0 1-1 1-1 2 1 2 1 2 1 4h-2v-3-2c-1-2-1-3-1-5 1-1 2-3 2-5h0c1-3 2-6 4-8l1 1 1-1z" class="f"></path><path d="M390 198l3-3 1 1c-1 1-5 4-5 6-1 0 0 1 0 2-1 0-2 1-2 1-5 4-10 11-13 16l-8 10-2 3h-1l10-14c4-5 7-9 9-14-3 3-5 7-8 10-2 2-4 4-5 6v1c-1 1-2 2-2 3h-2l-1 2h-1l-1-1c1-1 1-1 1-2l1-1c3-2 6-6 8-8l2-3 7-7c3-2 6-5 9-8z" class="V"></path><path d="M424 187c6 0 12-1 17 0l19 1 26-1h8 37l10 1h5-1c-2 1-4 1-7 1h3 1c2 0 4 1 6 1v1l-1 1c-1-1-3-1-4-1h-1c1 2 2 4 2 6l-2-2c-1 0-2 1-3 1l1 2h0v3c1 2 2 4 2 6l1 1s0 1 1 2c0 0 0 1 1 1l3-2v-1c1-2 2-2 3-3-1 1-2 2-2 4h-1v2c-1 1-1 2-2 2l-2 1v2c-1 1-1 1 0 2v3l1 1c-1 1-1 0-1 1-1 0-1 2-1 3 0 0 0 1 1 1l-3 3-1 1c0 1-3 3-3 4l-7 3v1h-2c-1 1-2 1-3 1l-2-3c-1 1-1 1-2 1h-1-2v-1h-2c-1-1-3-1-5-1l-3-1c-1 0-2-1-3-1-3 0-5-3-6-4-2 0-3 1-4 2v1h-2l-2 1-2-2-1 1h-1c0-2-1-2-2-3l-1-1s0-1-1-2h0c0-2 0-4-1-6l-3-3-1-3-1 2 1 4-1 1h0l-1 2h-3c1 2 2 4 2 5v2h-1c-1-1 0-1-1-1s-1 0-2-1v-2l-3-3c-1-2-2-5-4-7v-1l-1 2-1-1-1-1-2-1h-1l-1 3c-2-1-4-1-6-2l1 2h-5c0-1-1-1-1-2h-1v-1c-2-1-3-1-5-2l-1-1h0c-1 0-2 0-2-1-2 0-3 0-4 1-1 0-2 0-3-1h-3 0l-3 1c-2 1-4 1-7 2v1h-3c-2 1-5 5-7 7 0 1-1 1-2 1h-2c0-1 0-1 1-1-1-2-1-2-3-4l1-1-1-1 4-3h-2v-1h-2c-2 1-5 4-6 6l-3 1v-2h-2l4-5v-1c0-1 0-3-1-4h1l5-5 5-6c4-2 7-5 12-6h0c3 0 6-1 8-3z" class="J"></path><path d="M399 202h2c-1 2-3 3-4 5h-3l5-5z" class="u"></path><path d="M458 191c4 0 6-1 9 2l3 3h-1c-1 1-2 1-3 2l-2-2c-1-2-1-3-3-3h-1l-2-2z" class="F"></path><path d="M432 194c3-1 6 0 9-1 4-2 7 2 10 1l1-1 3 3c-1 1-1 2-3 2l-3-1h0c-3-2-6-2-9-3h-8z" class="G"></path><path d="M452 193l3 3c-1 1-1 2-3 2l-3-1h1c1-1 1-1 2-1l-1-2 1-1zm42-6h37l-1 2c-3 0-7-1-10 0 0 1-2 1-2 1-3 0-6 0-8-1h-5l-3 1h-7l2-1c1-1 3 0 5 0v-1h7c3 1 7 0 11 0h0-19c-2 0-4 1-6 0l-1-1z" class="c"></path><path d="M470 196c0-2-2-4-3-5h6c3-1 8-1 11 0h-1c-1 0-1 1-2 2-1 0-2-1-3-1-1 1-1 2-2 2l-1 1c1 1 1 2 2 2-1 1-1 1-2 1v1l-1 2 1 1v1l-2-2c-1-2-3-3-4-5h1z" class="L"></path><path d="M474 201c-1-2-1-3-3-4v-2h2l1-2h1l1 1-1 1c1 1 1 2 2 2-1 1-1 1-2 1v1l-1 2z" class="F"></path><path d="M432 194h8l1 2c-1 0-2 0-3 1-8-1-14 0-22 3-2 1-5 2-7 4-3 1-7 5-9 5 9-9 19-14 32-15z" class="N"></path><path d="M409 204h5 2l-7 3-6 5h-2c-2 1-5 4-6 6l-3 1v-2h-2l4-5v-1l4-1c1 0 1-1 2-1 2 0 6-4 9-5z" class="I"></path><path d="M394 211l4-1-6 7h-2l4-5v-1z" class="G"></path><path d="M424 187c6 0 12-1 17 0-2 1-3 1-5 1-3 2-5 2-9 2-5 0-10 3-14 5s-8 3-12 7h-2l5-6c4-2 7-5 12-6h0c3 0 6-1 8-3z" class="K"></path><path d="M427 190l2-2c2-1 5 0 7 0-3 2-5 2-9 2z" class="G"></path><path d="M409 204c2-2 5-3 7-4 8-3 14-4 22-3h0-5v1c1 0 4 0 4 1v1l8 2-1 1c-3-1-7-1-10-2-6 0-12 1-18 3h-2-5z" class="L"></path><path d="M414 204c7-4 15-5 23-4l8 2-1 1c-3-1-7-1-10-2-6 0-12 1-18 3h-2z" class="E"></path><defs><linearGradient id="AB" x1="448.471" y1="196.177" x2="439.269" y2="203.141" xlink:href="#B"><stop offset="0" stop-color="#454740"></stop><stop offset="1" stop-color="#5c565c"></stop></linearGradient></defs><path fill="url(#AB)" d="M440 194c3 1 6 1 9 3h0l3 1 2 2c1 1 4 2 4 4v1l-4-1v2c-3 0-6-3-9-4l-8-2v-1c0-1-3-1-4-1v-1h5 0c1-1 2-1 3-1l-1-2z"></path><path d="M437 199c6 1 11 3 17 5v2c-3 0-6-3-9-4l-8-2v-1z" class="S"></path><path d="M440 194c3 1 6 1 9 3h0l3 1 2 2-1 1c0-1-1-1-2-1l-1-1h-2c-3-1-7-2-10-2h0c1-1 2-1 3-1l-1-2z" class="H"></path><path d="M455 191h3l2 2h1c2 0 2 1 3 3l2 2c1-1 2-1 3-2 1 2 3 3 4 5l2 2v1l-2 4h-2c0 1 1 2 1 3h1l1 1h1 1c0 1 0 1 1 2l1 1h0l-1 2 1 4-1 1h0l-1 2h-3c1 2 2 4 2 5v2h-1c-1-1 0-1-1-1s-1 0-2-1v-2l-3-3c-1-2-2-5-4-7v-1l-1 2-1-1-1-1-2-1h-1l-2-1h0l-3-3-4-2 1-1v-2l-6-3 1-1c3 1 6 4 9 4v-2l4 1v-1c0-2-3-3-4-4l-2-2c2 0 2-1 3-2l-3-3-1-2h4z" class="W"></path><path d="M471 219h0c1 1 2 2 2 3v-1l1 1h3 0l-1 2h-3l-2-5z" class="O"></path><path d="M455 191h3l2 2h1c2 0 2 1 3 3l-1-1h0c-2 1-2 1-4 0l-3-3-1-1z" class="B"></path><path d="M455 196l7 8-3 3-1-2v-1c0-2-3-3-4-4l-2-2c2 0 2-1 3-2z" class="P"></path><path d="M466 198c1-1 2-1 3-2 1 2 3 3 4 5l2 2v1l-2 4h-2c0 1 1 2 1 3h1l1 1h1 1c0 1 0 1 1 2l1 1h0l-1 2h-1c-1 0-2 0-3-1s-2-2-2-4c0-1-1-2-2-3h0l-3-3c-1-2-1-3-2-4 0-1-1-2-1-3 1-1 2 0 3 0v-1z" class="a"></path><path d="M466 198c1-1 2-1 3-2 1 2 3 3 4 5l-1 1s-1 1 0 1c0 2 1 1 1 3h-1c-2-2-3-4-5-5l-1-1-1 1h1c1 2 2 3 3 5l1 1-1 2-3-3c-1-2-1-3-2-4 0-1-1-2-1-3 1-1 2 0 3 0v-1z" class="H"></path><path d="M445 202c3 1 6 4 9 4v-2l4 1 1 2 3-3c2 3 4 5 5 9 1 1 3 3 3 5l1 1 2 5c1 2 2 4 2 5v2h-1c-1-1 0-1-1-1s-1 0-2-1v-2l-3-3c-1-2-2-5-4-7v-1l-1 2-1-1-1-1-2-1h-1l-2-1h0l-3-3-4-2 1-1v-2l-6-3 1-1z" class="Y"></path><path d="M455 209l3 2c0 1-1 2-2 3h0l-3-3c1 0 2-1 2-2z" class="D"></path><path d="M458 211c2 2 4 4 6 5l-1 2-1-1-1-1-2-1h-1l-2-1c1-1 2-2 2-3z" class="B"></path><path d="M450 206l5 3c0 1-1 2-2 2l-4-2 1-1v-2z" class="W"></path><path d="M454 204l4 1 1 2c2 1 5 3 6 6h-1l-4-4-2-2-1 1c-2 0-1-1-3-2v-2z" class="m"></path><path d="M462 204c2 3 4 5 5 9h-2c-1-3-4-5-6-6l3-3z" class="K"></path><path d="M464 217l1-2h2c1 2 1 2 1 4 1 0 1 0 2-1l1 1 2 5c1 2 2 4 2 5v2h-1c-1-1 0-1-1-1s-1 0-2-1v-2l-3-3c-1-2-2-5-4-7z" class="G"></path><path d="M416 204c6-2 12-3 18-3 3 1 7 1 10 2l6 3v2l-1 1 4 2 3 3h0l2 1-1 3c-2-1-4-1-6-2l1 2h-5c0-1-1-1-1-2h-1v-1c-2-1-3-1-5-2l-1-1h0c-1 0-2 0-2-1-2 0-3 0-4 1-1 0-2 0-3-1h-3 0l-3 1c-2 1-4 1-7 2v1h-3c-2 1-5 5-7 7 0 1-1 1-2 1h-2c0-1 0-1 1-1-1-2-1-2-3-4l1-1-1-1 4-3h-2v-1l6-5 7-3z" class="I"></path><path d="M420 206l2 2h1c-2 1-5 2-7 3h0l-1-2 1-1c1-1 3-1 4-2z" class="T"></path><path d="M420 206h2c1 0 2-1 3-1l7-1c1 0 1 1 3 2h0 1c-5 1-9 1-13 2h-1l-2-2z" class="O"></path><path d="M434 201c3 1 7 1 10 2l6 3v2l-1 1c-5-1-8-3-13-3h-1c-1-2-2-2-2-3l1-2z" class="g"></path><path d="M416 204c6-2 12-3 18-3l-1 2c0 1 1 1 2 3h0c-2-1-2-2-3-2l-7 1c-1 0-2 1-3 1h-2c-1 1-3 1-4 2h-5l-2 1v-2l7-3z" class="W"></path><path d="M425 209l4-1v1h3c1 0 2-1 3-1h0 5l1 1c1 0 6 2 7 2s1-1 3 0v1c0 1 1 1 2 2h3 0l2 1-1 3c-2-1-4-1-6-2l1 2h-5c0-1-1-1-1-2h-1v-1c-2-1-3-1-5-2l-1-1h0c-1 0-2 0-2-1-2 0-3 0-4 1-1 0-2 0-3-1h-3 0l-2-2z" class="Z"></path><path d="M446 216h5l1 2h-5c0-1-1-1-1-2z" class="H"></path><path d="M403 212l6-5v2l2-1h5l-1 1 1 2h0c1 1 1 1 3 2 1-2 4-3 6-4l2 2-3 1c-2 1-4 1-7 2v1h-3c-2 1-5 5-7 7 0 1-1 1-2 1h-2c0-1 0-1 1-1-1-2-1-2-3-4l1-1-1-1 4-3h-2v-1z" class="m"></path><path d="M412 210c1-1 2-1 3-1l1 2c-1 1-2 1-3 1l-1-2h0z" class="R"></path><path d="M407 215v1 2h1c0-1 1-2 1-4h2v2l-3 3h-2v1c0 1 0 1 1 2 0 1-1 1-2 1h0v-2-1c0-1-1-2-1-2l-1-1c1 0 4-1 4-2z" class="w"></path><path d="M403 212l6-5v2l2-1h5l-1 1c-1 0-2 0-3 1s-2 1-4 2c0-1-1-1-1-1l-2 2h0-2v-1z" class="C"></path><path d="M405 213h1c1 0 2 0 3-1h1c-1 1-1 3-3 3 0 1-3 2-4 2l1 1s1 1 1 2v1 2h0-2c0-1 0-1 1-1-1-2-1-2-3-4l1-1-1-1 4-3h0z" class="r"></path><path d="M531 187l10 1h5-1c-2 1-4 1-7 1h3 1c2 0 4 1 6 1v1l-1 1c-1-1-3-1-4-1h-1c1 2 2 4 2 6l-2-2c-1 0-2 1-3 1l1 2h0v3l-2-1-2-2-1 1v-1-2h0-8c-1 0-1 1-2 2h-2-6c-3 0-5 1-8 2h-1l-8 6c-1 2-3 3-4 5l-1 2c-1 3-2 7-1 10 1 2 3 5 5 7-2 0-3 1-4 2v1h-2l-2 1-2-2-1 1h-1c0-2-1-2-2-3l-1-1s0-1-1-2h0c0-2 0-4-1-6l-3-3-1-3h0l-1-1c-1-1-1-1-1-2h-1-1l-1-1h-1c0-1-1-2-1-3h2l2-4v-1-1l-1-1 1-2v-1c1 0 1 0 2-1-1 0-1-1-2-2l1-1c1 0 1-1 2-2 1 0 2 1 3 1 1-1 1-2 2-2h1 1 0l3-1c1 1 5 0 7 0h7l3-1h5c2 1 5 1 8 1 0 0 2 0 2-1 3-1 7 0 10 0l1-2z" class="B"></path><path d="M502 190l3-1 2 3c-2 2-5 3-8 5-2 1-5 3-8 3v1l-1-1h-1c-1-1-3-3-3-4 0-3 0-4 2-6 1 1 5 0 7 0h7z" class="a"></path><path d="M487 195v-2c1-1 2-1 4-1l1 1-1 4h-1-2l-1-2z" class="N"></path><path d="M487 195l1-1c1 0 1 0 2 1v2h-2l-1-2z" class="v"></path><path d="M502 190l3-1 2 3c-2 2-5 3-8 5-2 1-5 3-8 3v1l-1-1c1-1 1-2 1-3l1-4c1-1 1-1 3-2l2 3h0c0-1 0-1 1-2v1l1 2h1 0l1-2c1 0 2-1 3-1l-2-2z" class="G"></path><path d="M497 194h0v2l-2 2c-2-1-2-1-3-2 1-2 2 0 4-1 0 0 0-1 1-1z" class="c"></path><path d="M503 198c1 0 2 0 4-1h2l1 1-1 2h-1l-8 6c-1 2-3 3-4 5l-1 2h-2l-1-1-2-1c-1-1-3-1-5-1l-2-1c-2-1-3-2-5-4v-2s1 1 2 1c0-1 0-1 1-2v-1-2h2c1 2 1 3 3 4h0v1h3c1 1 2 0 3 0l1 1c1 0 2-1 3-1l1-2h2v-1c0-1 1-1 2-2s1-1 2-1z" class="N"></path><path d="M481 201c2 0 3 1 3 2v3c-1 0-1-1-1-1-1-1-1-2-2-3v-1zm22-3c1 0 2 0 4-1h2l1 1-1 2h-1 0c-3-1-5 2-8 3l-1-1 1-1c1-1 2-1 2-2l1-1z" class="g"></path><path d="M478 205v-2s1 1 2 1c0-1 0-1 1-2 1 1 1 2 2 3 0 0 0 1 1 1v2h2c1-1 0-2 1-2v1c1 1 1 2 2 3h2l1 2-2-1c-1-1-3-1-5-1l-2-1c-2-1-3-2-5-4z" class="D"></path><path d="M491 210l4-4c2 0 2-1 4-2 1 0 0 0 1 1h0v1c-1 2-3 3-4 5l-1 2h-2l-1-1-1-2zm40-23l10 1h5-1c-2 1-4 1-7 1h3 1c2 0 4 1 6 1v1l-1 1c-1-1-3-1-4-1h-1c1 2 2 4 2 6l-2-2c-1 0-2 1-3 1l1 2h0v3l-2-1-2-2-1 1v-1-2h0-8c-1 0-1 1-2 2h-2-6c-3 0-5 1-8 2l1-2-1-1h-2c-2 1-3 1-4 1s-1 0-2 1l-2-2c3-2 6-3 8-5l-2-3h5c2 1 5 1 8 1 0 0 2 0 2-1 3-1 7 0 10 0l1-2z" class="O"></path><path d="M523 198v-1h-3-1c1-2 2-2 4-2v-1c1-1 1-1 2-1v2l2 1c-1 0-1 1-2 2h-2z" class="g"></path><path d="M510 198c1 0 1 0 3-1l-1-1h-1l-1-1h-3 2v-1-1h-1c1-1 4-1 6-1 1-1 2 0 4 0v1l-1 1h-1v1l1 3c-3 0-5 1-8 2l1-2z" class="N"></path><path d="M516 194l-1-1h3l-1 1h-1z" class="g"></path><path d="M531 187l10 1h5-1c-2 1-4 1-7 1h3 1c2 0 4 1 6 1v1l-1 1c-1-1-3-1-4-1h-1c1 2 2 4 2 6l-2-2c-1 0-2 1-3 1l1 2h0v3l-2-1-2-2-1 1v-1-2h0v-1h-5l-1-1c0-2 2-2 3-4-4-1-8 0-12-1 3-1 7 0 10 0l1-2z" class="G"></path><path d="M535 195c0-1 0-1 1-2-1-1-1-2-2-3 1 1 2 1 3 1h1c2 0 3 2 4 3v1c-1 0-2 1-3 1l1 2h0v3l-2-1-2-2-1 1v-1-2h0v-1z" class="g"></path><path d="M542 194v1c-1 0-2 1-3 1l1 2-3-1c2-2 2-3 5-3z" class="e"></path><path d="M537 197l3 1h0v3l-2-1-2-2 1-1z" class="o"></path><path d="M475 202c1 1 2 3 3 3 2 2 3 3 5 4l2 1c2 0 4 0 5 1l2 1 1 1h2c-1 3-2 7-1 10 1 2 3 5 5 7-2 0-3 1-4 2v1h-2l-2 1-2-2-1 1h-1c0-2-1-2-2-3l-1-1s0-1-1-2h0c0-2 0-4-1-6l-3-3-1-3h0l-1-1c-1-1-1-1-1-2h-1-1l-1-1h-1c0-1-1-2-1-3h2l2-4v-1-1z" class="T"></path><path d="M490 212c0 1 1 2 0 3h1c0 1 1 2 1 3v1c-1 1 0 3 1 4s1 2 1 3c1 1 2 2 2 3-1 0-1 0-2-1l-1-3h-2c-1-1-2-4-3-3l-1 1c-1-2-3-4-4-6 1-1 1-1 2-1h1l1 3c1 1 2 2 4 2h0c0-1-1-2-1-3v-1-5z" class="S"></path><path d="M475 202c1 1 2 3 3 3 2 2 3 3 5 4l2 1c2 0 4 0 5 1v1 5 1c0 1 1 2 1 3h0c-2 0-3-1-4-2l-1-3h-1c-1 0-1 0-2 1l-3-5h0l-3-6-2-2v-1-1z" class="r"></path><path d="M483 209l2 1 1 3 1 1h1c0 1 1 2 1 3h0 0c-1 0-2-1-3-1h-1c-1 0-1 0-2 1l-3-5c1 0 1 0 2-1h1c0 1 0 1 1 2h1v-1c-1-1-1-2-2-3z" class="n"></path><path d="M475 202c1 1 2 3 3 3 2 2 3 3 5 4 1 1 1 2 2 3v1h-1c-1-1-1-1-1-2h-1c-1 1-1 1-2 1h0l-3-6-2-2v-1-1z" class="K"></path><path d="M475 204l2 2 3 6h0l3 5c1 2 3 4 4 6 2 2 3 4 5 6l3 3v1h-2l-2 1-2-2-1 1h-1c0-2-1-2-2-3l-1-1s0-1-1-2h0c0-2 0-4-1-6l-3-3-1-3h0l-1-1c-1-1-1-1-1-2h-1-1l-1-1h-1c0-1-1-2-1-3h2l2-4z" class="N"></path><path d="M475 204l2 2 3 6-1-1c-2 0-3 0-5-1l-1-2 2-4z" class="O"></path><path d="M475 204l2 2c-1 0-1 1-2 2 0 1 1 1-1 2l-1-2 2-4z" class="H"></path><path d="M482 221h1l1 2h0c1 1 1 3 1 4l2 3c2 0 3 0 5 2l1 1-2 1-2-2-1 1h-1c0-2-1-2-2-3l-1-1s0-1-1-2h0c0-2 0-4-1-6z" class="e"></path><path d="M527 196h8 0v2 1l1-1 2 2 2 1c1 2 2 4 2 6l1 1s0 1 1 2c0 0 0 1 1 1l3-2v-1c1-2 2-2 3-3-1 1-2 2-2 4h-1v2c-1 1-1 2-2 2l-2 1v2c-1 1-1 1 0 2v3l1 1c-1 1-1 0-1 1-1 0-1 2-1 3 0 0 0 1 1 1l-3 3-1 1c0 1-3 3-3 4l-7 3v1h-2c-1 1-2 1-3 1l-2-3c-1 1-1 1-2 1h-1-2v-1h-2c-1-1-3-1-5-1l-3-1c-1 0-2-1-3-1-3 0-5-3-6-4-2-2-4-5-5-7-1-3 0-7 1-10l1-2c1-2 3-3 4-5l8-6h1c3-1 5-2 8-2h6 2c1-1 1-2 2-2z" class="a"></path><path d="M520 224h0l1-1 2 3c0 2-1 2-2 3l-2-2 1-1v-2zm-1-14l1-1h1v-2c1 0 1 0 2 1 1 0 2-1 3 0l-3 3c-1 0-3 0-4-1z" class="e"></path><path d="M509 210l2-2c4-1 6-4 11-4v1h-1c-1 1-1 1-2 1-2 0-3 1-4 3l-3 3c-1 1-2 3-4 4v1h-1l1 2-1 2c-1 1 0 1-1 1-1 1-2 1-2 1l-1-2 1-7h2c1-1 1-2 2-2l1-1v-1z" class="F"></path><path d="M526 229v-1l2-2c1 1 1 1 2 1l1 1h3c-1 2-3 3-5 5-1 0-1 1-2 1l1 1c-1 1-2 1-3 1l-2 1c-1 1-1 1-2 1h-1-2v-1h0c0-2 0-2-1-3s-1-1-2-1c1 0 3 0 4-1h1c1 0 2 0 3-1l3-2z" class="N"></path><path d="M526 229h2l1 1c-2 2-3 2-6 3l-1-1 1-1 3-2z" class="v"></path><path d="M520 232c1 0 2 0 3-1l-1 1 1 1 2 1c-2 2-5 2-7 3 0-2 0-2-1-3s-1-1-2-1c1 0 3 0 4-1h1z" class="e"></path><path d="M507 221h2l3 4h1l2 1v2c1 1 1 1 2 1l1 1h2v2h-1c-1 1-3 1-4 1l-4-1h-1l-2-1-1-1v-1c-2-2-3-3-3-6 0 0 1 0 2-1 1 0 0 0 1-1z" class="H"></path><path d="M511 232c2-1 2-1 2-2 1 0 1 1 2 2 0-1 1-1 2-1h0c1 1 1 1 2 1-1 1-3 1-4 1l-4-1z" class="O"></path><path d="M507 221h2l3 4v2l-1 1-1-1c-2 1-1 2-2 4l-1-1v-1c-2-2-3-3-3-6 0 0 1 0 2-1 1 0 0 0 1-1z" class="B"></path><path d="M519 210c1 1 3 1 4 1 2 0 3 1 4 1 1 2 2 6 1 9 0 1 0 1-1 1l-1-1c-1 2-1 1-1 2s-1 2-2 3l-2-3 2-1v-1l-1-1c-1-1 0-2 0-3-1 0-2 0-4-1-1-1-2-2-2-3v-1c1-1 2-1 3-2z" class="o"></path><path d="M531 203l3 3 2 2-2 2c0 1 0 1 1 2h1l1 1-2 2c-1 2 1 2-1 4h0-1-1c0 1-1 1-2 1v-1c1-1 1-2 1-2l-1-1c0-2-1-3 0-5 0 0-1-1-1-2-2 0-2-1-4-2l-1-1h-2v-1-1h4c1 0 2 0 3 1 1 0 1-1 2-2z" class="B"></path><path d="M531 203l3 3c-1 1-1 1-3 1-1 0-1-1-2-2 1 0 1-1 2-2z" class="a"></path><path d="M530 220c1 0 2 0 2-1h1 1 0c2-2 0-2 1-4l2-2-1-1h-1c-1-1-1-1-1-2l2-2c1 3 2 5 2 8 0 1 1 2 1 3 0 3-1 6-2 8-1 1-2 3-3 3-2 2-4 4-6 5l-1-1c1 0 1-1 2-1 2-2 4-3 5-5h-3l1-2v-1h-2 0v-5z" class="D"></path><path d="M535 221l2-2 1 1c0 1 0 3-1 4-1-1 0-1-1-2l-1-1z" class="e"></path><path d="M532 225c0-2 0-4 2-5l1 1 1 1c1 1 0 1 1 2l-2 4h-1-3l1-2v-1z" class="N"></path><path d="M532 226l1-1c1 1 2 2 2 3h-1-3l1-2z" class="e"></path><path d="M509 221v-4c1-1 2-2 4-3h0c0-1 1-1 2-2h1v1c0 1 1 2 2 3 2 1 3 1 4 1 0 1-1 2 0 3l1 1v1l-2 1-1 1h0c-2 0-3 1-5 0-1 0-1 1-2 1h-1l-3-4z" class="v"></path><path d="M515 216h1v2h-1l-1-1 1-1z" class="o"></path><path d="M509 221v-4c1-1 2-2 4-3h0c0-1 1-1 2-2h1v1h0c-1 1-1 2-2 2-1 1-2 3-3 4l4 5c-1 0-1 1-2 1h-1l-3-4z" class="N"></path><path d="M539 219c2-2 2-3 2-5h0c0-1 0-1 1-2h1c1 1 0 2 1 4-1 1-1 1 0 2v3l1 1c-1 1-1 0-1 1-1 0-1 2-1 3 0 0 0 1 1 1l-3 3-1 1c0 1-3 3-3 4l-7 3v1h-2c-1 1-2 1-3 1l-2-3 2-1c1 0 2 0 3-1 2-1 4-3 6-5 1 0 2-2 3-3 1-2 2-5 2-8h0z" class="v"></path><path d="M534 230c0 2 0 3-2 4-1 1-3 1-4 3 1 1 1 1 2 1v1h-2c-1 1-2 1-3 1l-2-3 2-1c1 0 2 0 3-1 2-1 4-3 6-5z" class="e"></path><path d="M523 237c2 0 4 1 5 2h0c-1 1-2 1-3 1l-2-3z" class="o"></path><path d="M539 219c2-2 2-3 2-5h0c0-1 0-1 1-2h1c1 1 0 2 1 4-1 1-1 1 0 2v3l1 1c-1 1-1 0-1 1-1 0-1 2-1 3 0 0 0 1 1 1l-3 3-1 1c0-2 0-1 1-3 0-2 1-1 1-2v-1l-1-1c-1 2-2 2-4 3h0c1-2 2-5 2-8h0z" class="e"></path><path d="M527 196h8 0v2 1l1-1 2 2 2 1c1 2 2 4 2 6l1 1s0 1 1 2c0 0 0 1 1 1l3-2v-1c1-2 2-2 3-3-1 1-2 2-2 4h-1v2c-1 1-1 2-2 2l-2 1v2c-1-2 0-3-1-4h-1c-1 1-1 1-1 2h0c0 2 0 3-2 5h0c0-1-1-2-1-3 0-3-1-5-2-8l-2-2-3-3c-2-1-2-2-3-2v-1l-1-2h-2c1-1 1-2 2-2z" class="v"></path><path d="M527 198c4 2 8 4 10 8 1 1 1 2 2 3 0 3 1 5 1 8l-1 2h0c0-1-1-2-1-3 0-3-1-5-2-8l-2-2-3-3c-2-1-2-2-3-2v-1l-1-2z" class="Q"></path><path d="M538 200l2 1c1 2 2 4 2 6l1 1s0 1 1 2c0 0 0 1 1 1l3-2v-1c1-2 2-2 3-3-1 1-2 2-2 4h-1v2c-1 1-1 2-2 2l-2 1v2c-1-2 0-3-1-4h-1c-1 1-1 1-1 2h0c0 2 0 3-2 5l1-2c0-3-1-5-1-8 1 0 1 1 2 1l1-1c-1-1-1-2-1-3l-1-1c-1-1-2-4-2-5z" class="N"></path><path d="M517 198h6 2 2l1 2v1c1 0 1 1 3 2-1 1-1 2-2 2-1-1-2-1-3-1h-4c-5 0-7 3-11 4l-2 2v1l-1 1c-1 0-1 1-2 2h-2l-1 7 1 2c0 3 1 4 3 6v1l1 1 2 1h1l4 1c1 0 1 0 2 1s1 1 1 3h0-2c-1-1-3-1-5-1l-3-1c-1 0-2-1-3-1-3 0-5-3-6-4-2-2-4-5-5-7-1-3 0-7 1-10l1-2c1-2 3-3 4-5l8-6h1c3-1 5-2 8-2z" class="K"></path><path d="M500 214c0-3 3-5 5-7h3c0 2-1 2-2 3s-1 2-1 3l-1 1-1 7 1 2c0 3 1 4 3 6v1c-3-2-4-5-6-8h0c-1-3-1-6-1-8z" class="P"></path><path d="M509 210v-1c0-2 1-2 1-3l-2-2c-1 0-1 0-1-1l1-1h2c2-2 5-2 9-2 3 0 6 0 9 1 1 0 1 1 3 2-1 1-1 2-2 2-1-1-2-1-3-1h-4c-5 0-7 3-11 4l-2 2z" class="n"></path><path d="M519 200c3 0 6 0 9 1 1 0 1 1 3 2-1 1-1 2-2 2-1-1-2-1-3-1-2-1-5-2-7-2h-1l1-2z" class="o"></path><path d="M496 211l2 3 1-2 1 2c0 2 0 5 1 8h0c2 3 3 6 6 8l1 1 2 1h1l4 1c1 0 1 0 2 1s1 1 1 3h0-2c-1-1-3-1-5-1l-3-1c-1 0-2-1-3-1-3 0-5-3-6-4-2-2-4-5-5-7-1-3 0-7 1-10l1-2z" class="E"></path><path d="M498 214l1-2 1 2c0 2 0 5 1 8h-1l-1-1-1-1c-1 1-1 3-1 4 0-2-1-5-1-7l2-3z" class="C"></path><path d="M497 224c0-1 0-3 1-4l1 1 1 1h1 0l-1 1c0 1 0 2 1 3 0 0 0 1 1 2l-1 1c1 1 2 1 3 2 1 0 3 2 4 3v1c-1 0-2-1-3-1-2-1-4-2-5-4h0l-3-6z" class="H"></path><path d="M508 235v-1c-1-1-3-3-4-3-1-1-2-1-3-2l1-1c-1-1-1-2-1-2-1-1-1-2-1-3l1-1c2 3 3 6 6 8l1 1 2 1h1l4 1c1 0 1 0 2 1s1 1 1 3h0-2c-1-1-3-1-5-1l-3-1z" class="o"></path><path d="M510 232h1l4 1c1 0 1 0 2 1s1 1 1 3h0-2c-1-1-3-1-5-1l1-1c-1-1-2-1-3-2h1l-1-1h1z" class="v"></path><path d="M807 187h14l-2 1c-1 0-2 0-3 1-2 1-5 0-7 1-4 1-8 2-11 4l-1-1c-3 3-9 7-11 11-1 1-2 1-3 2-1-1-1-2-1-4 0-1 0-1-1-2l-2 2 1 4c0 2 0 4-1 6h0v4c-1 3-2 5-4 7-2 1-4 2-5 4h0-4v1l-1-1-2-1h-4-3l-2 1v1h0c-4-1-7-4-9-7l-1 1h-1l-1-2c-1 1-2 3-3 4l1 1v1 1l-2-1h0c-1 0-2-1-2-1h-1v2l2 2-6 1c-2 1-2 3-4 4v1c-1 0-2 0-3-1l-2 2c0 2-1 2-2 3-1 0-2 0-2 1h-1l-1 2 1 1-1 2 1 1c1 0 1 1 1 2l1 1-3 1h-1-5l-2-1v2l2 1-2 2-1 1-1-1-1-1-2 1-1-1c-1-1-2-2-2-4s1-4 2-6l-1-2-1 1-1-1h-1l-2-1c0 1-1 1-2 2 0-1 0-1-1-1-1-1-1-1-1-2l-1 1-1 3c0 2 0 4-1 5v2h-1c-3-2-6-6-8-9l-3-3-7-10c-2-2-3-4-5-6 0-1-2-3-3-4v-2l2-2v-1l-1-1v-1-6h0l1-5c1-2 2-3 3-4 4-5 9-6 15-6-2 0-3-1-5-2h0v1h-1 0c0-1 1-1 2-2h57 13 6 53z" class="J"></path><path d="M781 200h-1l1-1c-2-1-1-2-2-3h-2c-1-1-2-2-2-3-1-1-3-2-4-3 1-1 1-1 2 0 2 0 6 0 8 1h6c-1 1-2 0-3 0-1 1-1 1-2 1h-1l-3 2 1 1 2 2c1 1 1 1 2 1v1c0 1 0 1 1 2h0v1c1 0 1 1 2 2-1 1-2 1-3 2-1-1-1-2-1-4 0-1 0-1-1-2z" class="k"></path><path d="M741 192c8-3 21-5 29-1l1 1h-1-4v1c1 2 3 4 3 6l1 1c-1 0-2 1-4 1h0-1v-2h0c0-1 1-2 1-3l-2-2c-3-2-5-2-9-1-2-1-3-2-5-1l-4 1h-1v-1h-4z" class="T"></path><path d="M766 196l3 3 1 1c-1 0-2 1-4 1h0-1v-2h0c0-1 1-2 1-3z" class="E"></path><path d="M807 187h14l-2 1c-1 0-2 0-3 1-2 1-5 0-7 1-4 1-8 2-11 4l-1-1 3-1c1 0 1-1 1-1h0c0-1 0-1-1-1h-6v-1l-1 1c-2 0-10 0-12-1v-1h1c7 0 17 0 24-1h1z" class="s"></path><path d="M787 191h1 2 2c2 0 4 0 6 1l1-1 1 1-3 1c-3 3-9 7-11 11-1-1-1-2-2-2v-1h0c-1-1-1-1-1-2v-1c-1 0-1 0-2-1l-2-2-1-1 3-2h1c1 0 1 0 2-1 1 0 2 1 3 0z" class="R"></path><path d="M787 191h1 2 2c2 0 4 0 6 1h-3l-1 1h-3-1c-1 1 0 2-2 2h0c-1-1-3-2-5-2l-1 1h-1l1-2c1 0 1 0 2-1 1 0 2 1 3 0z" class="t"></path><path d="M746 193l4-1c2-1 3 0 5 1 4-1 6-1 9 1l2 2c0 1-1 2-1 3h0c-1-1-2-2-3-2-2-1-4-1-6-1-2 1-3 1-5 1-1 0-2-1-3 0-3 1-6 5-9 5h0l-1 1h-5c0-1 1-2 1-2 4-3 7-5 12-8z" class="G"></path><path d="M746 193l4-1c2-1 3 0 5 1-6 1-13 4-16 9l-1 1h-5c0-1 1-2 1-2 4-3 7-5 12-8z" class="D"></path><path d="M694 190c4 2 10 0 14 0 8 0 16 0 24-1 3 0 6 1 9 1 0 0 3-1 3 0-1 0-1 1-2 1l-3 1v1c-1 0-1 0-2 1-3 0-5 1-7 2v1l-3 2-2-1c-2 2-4 2-7 2l-1 1c-1 2-2 3-3 5h0-1v-2c-2-1-3-1-6-1v1h-1-3-1c-1-2-2-2-3-3v-1c-1-1-2-2-2-3v-1c-1 0-2-1-3-2v-4z" class="F"></path><path d="M707 203l-1-1 1-2h1l2 1 1-1 2 1v-1c1 1 3 1 4 1-1 2-2 3-3 5h0-1v-2c-2-1-3-1-6-1z" class="Z"></path><path d="M710 199c-2 0-6 2-8 0-1-1-2-3-3-5v-1c1 1 2 1 2 1h2c1-1 2-1 3-1 0-1 1-1 2-1h0c3 1 6 0 9 0h4l1-1 2 1s1 0 2-1h1c1 1 2 1 3 0l2 1h-1c-1 1-2 1-2 1h-4c-2 1-4 3-5 5-2 1-4 1-6 1l-1-1-2 1h-1z" class="O"></path><path d="M710 199v-2c-2-1-2 0-3-1l-2 1h-1v-1c2-1 3-1 5-1s4-2 5-2 2 1 2 2c-1 0-1 1-2 2h-3v2h-1z" class="N"></path><path d="M771 192c3 3 5 5 7 9l1 1 1 4c0 2 0 4-1 6h0v4c-1 3-2 5-4 7-2 1-4 2-5 4h0-4v1l-1-1-2-1h-4-3l-2 1v1h0c-4-1-7-4-9-7l-1 1h-1l-1-2c-1 1-2 3-3 4l1 1v1 1l-2-1h0c-1 0-2-1-2-1h-1 0c-2-3-3-6-3-9 1-6 2-9 6-13l1-1h0c3 0 6-4 9-5 1-1 2 0 3 0 2 0 3 0 5-1 2 0 4 0 6 1 1 0 2 1 3 2v2h1 0c2 0 3-1 4-1l-1-1c0-2-2-4-3-6v-1h4 1z" class="L"></path><path d="M756 196c2 0 4 0 6 1 1 0 2 1 3 2v2h0c-3-2-6-4-9-5z" class="h"></path><path d="M746 220l2 1 1 1c1 0 1-1 2-1 1 1 2 1 3 2l-2 2c-3-1-4-2-6-5z" class="o"></path><path d="M755 198h0c1 1 1 0 2 1v1c1-1 1-1 2-1v2c2 0 3 1 3 3h-1l-1-1-1-1c0-1 0 0-1-1h-1c0 1 1 1 1 2h-3v-3c-1-1 0-1 0-2z" class="m"></path><path d="M765 201h0 1l2 3 1 3c0 1 0 2-1 2 0 1-1 1-1 1l-1-1 1-1c-1-1-1-2-1-3-1-1-2-1-2-2l1-2z" class="I"></path><path d="M756 215l2 1c2 0 3-1 5-2v-4l1-1c1 1 0 3 0 4s0 2-1 3c-3 3-5 2-9 2-1-1-1-2-1-3h2v-1l1 1z" class="K"></path><path d="M761 207c0 2 1 3 1 4-1 1-1 3-2 3l-4 1-1-1v1h-2v-2l2-4h1c2 1 3 1 4 1h0l1-3z" class="B"></path><path d="M761 207c0 2 1 3 1 4-1 1-1 3-2 3l-4 1-1-1h4l1-1-1-2h-1l-1 1-1-1v-2c2 1 3 1 4 1h0l1-3z" class="C"></path><path d="M743 202h3c2 0 4-1 5-2l1-2c1 1 1 1 2 3-1 1-1 2-2 3h-1c-1 0-3 2-4 3h-1l1-1-1-1-2 1-1-1-4 2h-1c2-2 4-3 5-5z" class="m"></path><path d="M748 207c1-1 3-2 5-3 1 0 2 1 3 0 2 1 3 2 5 3l-1 3h0c-1 0-2 0-4-1h-1l-2 4v2c0 1 0 2 1 3s2 2 4 3v2h2c-1 1-1 2-1 3h-3l-4-1 2-2c-1-1-2-1-3-2-1 0-1 1-2 1l-1-1-2-1c-1-1-1-3-2-5v-1-1c1-2 2-4 4-6z" class="D"></path><path d="M754 223l2 2 1-1-1-1-1-1c-1 0-2-1-2-2h1c1 0 2 1 3 1h1v2h2c-1 1-1 2-1 3h-3l-4-1 2-2z" class="O"></path><path d="M748 207h1c1 0 3-1 4-2h1l1 2-2 1c0 2 0 3-2 4v1c1 2 1 3 1 5-1 0-2-1-3-2v1c0 2 0 2 2 4-1 0-1 1-2 1l-1-1-2-1c-1-1-1-3-2-5v-1-1c1-2 2-4 4-6z" class="e"></path><path d="M744 215c2 1 2 2 3 4l1 1v1l-2-1c-1-1-1-3-2-5z" class="g"></path><path d="M748 207h1c1 0 3-1 4-2h1l1 2-2 1h0l-3 3v2c-1 1-3 1-5 2l-1-1v-1c1-2 2-4 4-6z" class="o"></path><path d="M739 202h0c3 0 6-4 9-5 1-1 2 0 3 0-3 2-6 3-8 5-1 2-3 3-5 5h1l4-2 1 1 2-1 1 1-1 1c-2 2-2 4-3 6v1c0 2 0 4 1 6l1 1h0l-1 1h-1l-1-2c-1 1-2 3-3 4l1 1v1 1l-2-1h0c-1 0-2-1-2-1h-1 0c-2-3-3-6-3-9 1-6 2-9 6-13l1-1z" class="C"></path><path d="M739 224v-2c0-1-1-2-1-3v-3c0-1 0-1 1-2h1l1 1c-1 2-1 2-1 4l2 1c-1 1-2 3-3 4z" class="a"></path><path d="M740 214h3c0 2 0 4 1 6l1 1h0l-1 1h-1l-1-2-2-1c0-2 0-2 1-4l-1-1z" class="D"></path><path d="M738 207h1l4-2 1 1 2-1 1 1-1 1c-2 2-2 4-3 6v-3h0l-1-1-1-1c-2 2-2 4-3 6l-1 1v9c-1-3-2-5-2-8v-2-1l3-6z" class="M"></path><path d="M739 202h0c3 0 6-4 9-5 1-1 2 0 3 0-3 2-6 3-8 5-1 2-3 3-5 5l-3 6v1 2c0 3 1 5 2 8 0 0 0 1 1 2h0c-1 0-2-1-2-1h-1 0c-2-3-3-6-3-9 1-6 2-9 6-13l1-1z" class="P"></path><path d="M771 192c3 3 5 5 7 9l1 1 1 4c0 2 0 4-1 6h0v4c-1 3-2 5-4 7-2 1-4 2-5 4h0-4v1l-1-1-2-1h-4c0-1 0-2 1-3h-2v-2c-2-1-3-2-4-3 4 0 6 1 9-2h2l4-6-1-1c1 0 1-1 1-2l-1-3-2-3h0c2 0 3-1 4-1l-1-1c0-2-2-4-3-6v-1h4 1z" class="O"></path><path d="M767 224c1-2 4-5 6-7l1 3c-2 2-4 4-7 4z" class="U"></path><path d="M778 201l1 1 1 4-1 2c-1 0-1 0-2-1l-2 2 1-5 1-1h1v-2z" class="H"></path><path d="M776 204h-2c-1-1-1-2-2-4 0-1-1-2-2-3l-1-1v-1h1c1 0 4 2 5 3v1c0 1 1 3 2 4h0l-1 1z" class="N"></path><path d="M766 201h0c2 0 3-1 4-1 2 4 3 8 1 12 0-1-1-1-2-2l-1-1c1 0 1-1 1-2l-1-3-2-3z" class="j"></path><path d="M780 206c0 2 0 4-1 6h0c-1 3-3 6-5 8l-1-3c2-2 3-4 2-8l2-2c1 1 1 1 2 1l1-2z" class="f"></path><defs><linearGradient id="AC" x1="774.345" y1="223.362" x2="769.336" y2="221.216" xlink:href="#B"><stop offset="0" stop-color="#201f1f"></stop><stop offset="1" stop-color="#393a38"></stop></linearGradient></defs><path fill="url(#AC)" d="M774 220c2-2 4-5 5-8v4c-1 3-2 5-4 7-2 1-4 2-5 4h0-4v1l-1-1-2-1 3-1 1-1c3 0 5-2 7-4z"></path><path d="M769 210c1 1 2 1 2 2 0 3-2 5-4 7 0 1 0 2-1 3h-2c0 2 1 2 2 3l-3 1h-4c0-1 0-2 1-3h-2v-2c-2-1-3-2-4-3 4 0 6 1 9-2h2l4-6z" class="Y"></path><path d="M769 210c1 1 2 1 2 2 0 3-2 5-4 7h-1v-1h0c1-2 2-2 2-3v-1l-2 2h-1l4-6z" class="E"></path><path d="M766 219h1c0 1 0 2-1 3h-2c0 2 1 2 2 3l-3 1h-4c0-1 0-2 1-3h-2v-2c4 0 5 0 8-2z" class="H"></path><path d="M739 193l2-1h4v1h1c-5 3-8 5-12 8 0 0-1 1-1 2h5c-4 4-5 7-6 13 0 3 1 6 3 9h0v2l2 2-6 1c-2 1-2 3-4 4v1c-1 0-2 0-3-1l-2 2c0 2-1 2-2 3-1 0-2 0-2 1h-1l-1 2 1 1-1 2 1 1c1 0 1 1 1 2l1 1-3 1h-1-5l-2-1v2l2 1-2 2-1 1-1-1-1-1-2 1-1-1c-1-1-2-2-2-4s1-4 2-6l-1-2-1 1-1-1h-1l-2-1c0 1-1 1-2 2 0-1 0-1-1-1-1-1-1-1-1-2l2-4c5-10 13-19 19-28l1-1h0c1-2 2-3 3-5l1-1c3 0 5 0 7-2l2 1 3-2v-1c2-1 4-2 7-2 1-1 1-1 2-1z" class="e"></path><path d="M718 200l1 3h2l2-2h1l1-1 1 1c-2 1-3 2-4 3l-5 4c0-1 0-1-1-1h-1l-1-1c1-2 2-3 3-5l1-1z" class="W"></path><path d="M714 206l1 1h1c1 0 1 0 1 1l-6 6h-1c-1 3-3 5-5 7-1 2-2 3-4 5-1 1-1 2-1 3-1 1-1 1-2 1 0 2-1 3-1 4-1 1-1 1-1 2h1c0 1 0 1-1 1l-2-2c5-10 13-19 19-28l1-1h0z" class="O"></path><path d="M697 236c2-2 4-6 6-8 3-3 6-8 7-11l2-2c2-2 5-5 8-5-3 2-5 5-8 7-1 2-2 3-3 4l1 1 3-3 1 1-2 3c-1 1-1 2-1 4-1 1-3 2-4 4l-2 2-2 4-2 4-1 1-1-1h-1l-2-1c0 1-1 1-2 2 0-1 0-1-1-1-1-1-1-1-1-2l2-4 2 2c1 0 1 0 1-1z" class="H"></path><path d="M696 240c1-1 1-2 3-2v3h-1l-2-1z" class="N"></path><path d="M709 221l1 1 3-3 1 1-2 3c-1 1-1 2-1 4-1 1-3 2-4 4l-1-2c1-1 1-2 2-2v-1c1-1 3-2 3-3h-1l-4 5c0-3 2-5 3-7z" class="i"></path><path d="M739 193l2-1h4v1h1c-5 3-8 5-12 8 0 0-1 1-1 2h5c-4 4-5 7-6 13l-4 4v3h0 0c-1-1-1-2-2-2h-1l-2 1h-1-2v1 1h-1v-1c-1 0-2-1-2-2-1 0-2-1-3-1l-1-1-3 3-1-1c1-1 2-2 3-4 3-2 5-5 8-7l5-5 2-2 6-3-3-3v-1c2-1 4-2 7-2 1-1 1-1 2-1z" class="V"></path><path d="M725 209c1 2 1 3 1 5h-1 0c-1-2 0-3 0-5z" class="f"></path><path d="M727 203l1 3h0l-2 1v1l-1 1v-4l2-2z" class="L"></path><path d="M722 220v-3-1c2-1 2-1 4 0-1 2-1 2-1 4v1l-2 1h-1v-2z" class="S"></path><path d="M739 193l2-1h4v1c-4 2-8 4-12 7l-3-3v-1c2-1 4-2 7-2 1-1 1-1 2-1z" class="N"></path><path d="M733 203h5c-4 4-5 7-6 13l-4 4v3h0 0c-1-1-1-2-2-2h-1v-1c0-2 0-2 1-4h1v-4c1-1 2-3 2-4 1-2 3-3 4-5h0z" class="O"></path><path d="M727 212c0 3 0 6 1 8v3h0 0c-1-1-1-2-2-2h-1v-1c0-2 0-2 1-4h1v-4z" class="T"></path><path d="M720 210l5-5v4h0c0 2-1 3 0 5h0c-1 1-1 1-2 1s-1 0-2-1c0 1-3 3-3 4 2 0 3 1 4 2v2h-2v1 1h-1v-1c-1 0-2-1-2-2-1 0-2-1-3-1l-1-1-3 3-1-1c1-1 2-2 3-4 3-2 5-5 8-7z" class="Z"></path><path d="M718 213c2-2 2-2 4-3h1c-1 2-3 3-4 4l1 1 1-2v1c0 1-3 3-3 4l-1 1h-2c0-2 3-4 3-6z" class="S"></path><path d="M718 213c0 2-3 4-3 6h2l1-1c2 0 3 1 4 2v2h-2v1 1h-1v-1c-1 0-2-1-2-2-1 0-2-1-3-1l-1-1-3 3-1-1c1-1 2-2 3-4l1 1c2-1 4-3 5-5z" class="U"></path><path d="M718 218c2 0 3 1 4 2v2h-2v1c-1-2-2-3-3-4l1-1z" class="K"></path><path d="M728 220l4-4c0 3 1 6 3 9h0v2l2 2-6 1c-2 1-2 3-4 4v1c-1 0-2 0-3-1l-2 2c0 2-1 2-2 3-1 0-2 0-2 1h-1l-1 2 1 1-1 2 1 1c1 0 1 1 1 2l1 1-3 1h-1-5l-2-1v2l2 1-2 2-1 1-1-1-1-1-2 1-1-1c-1-1-2-2-2-4s1-4 2-6l-1-2 2-4 2-4 2-2c1-2 3-3 4-4 0-2 0-3 1-4l2-3c1 0 2 1 3 1 0 1 1 2 2 2v1h1v-1-1h2 1l2-1h1c1 0 1 1 2 2h0 0v-3z" class="r"></path><path d="M712 233l3-3c0-1 0-2 1-3h1l1 1 2 1v2c-1 1-2 1-3 1l-1 1-1-1h-1c-1 1-1 1-2 1z" class="Q"></path><path d="M702 243l2 4 3 6 1 1-1 1-1-1-1-1-2 1-1-1c-1-1-2-2-2-4s1-4 2-6z" class="W"></path><path d="M714 220c1 0 2 1 3 1 0 1 1 2 2 2v1h1v1h-1l-2 2h-1c-1 1-1 2-1 3l-3 3c-3 2-3 4-5 6l-2 2c0 2 0 2-1 4v1 1l-2-4-1-2 2-4 2-4 2-2c1-2 3-3 4-4 0-2 0-3 1-4l2-3z" class="P"></path><path d="M705 233c2 0 3 0 5-2v-1c0-1 1-1 2-1 1 1 1 1 1 2h-1c-1 1-2 1-3 2v1c-1 0-1 1-2 2h0c-1-1-3 0-4 1l2-4z" class="c"></path><path d="M714 220c1 0 2 1 3 1 0 1 1 2 2 2v1c-4 0-5 0-8 3 0-2 0-3 1-4l2-3z" class="D"></path><path d="M703 237c1-1 3-2 4-1h0c-1 2-2 3-2 5s0 2-1 4v1 1l-2-4-1-2 2-4z" class="J"></path><path d="M728 220l4-4c0 3 1 6 3 9h0v2l2 2-6 1c-3 0-5 0-8-2 0 1 1 1 2 3-2 1-2 1-4 1l-1-1v-2l-2-1-1-1 2-2h1v-1-1-1h2 1l2-1h1c1 0 1 1 2 2h0 0v-3z" class="E"></path><path d="M719 228c1 0 2 1 4 1-1-1-1-1-1-2h1v1c0 1 1 1 2 3-2 1-2 1-4 1l-1-1v-2l-2-1h1z" class="n"></path><path d="M722 222h1l2 3c-1 0-4 1-4 1l-2 2h-1l-1-1 2-2h1v-1-1-1h2z" class="X"></path><g class="H"><path d="M725 221h1c1 0 1 1 2 2h0v1c1 2 1 2 0 4-1 0-2-2-3-3l-2-3 2-1z"></path><path d="M728 220l4-4c0 3 1 6 3 9h0v2l-3 1h-4c1-2 1-2 0-4v-1h0v-3z"></path></g><path d="M728 223l2-1v1c0 1 0 2 1 3l1 1v1h-4c1-2 1-2 0-4v-1h0z" class="o"></path><path d="M708 249l-1-1v-2h-1c0-1 1-1 1-2 1-2 2-3 3-5l3-3c1-1 1-1 2-1l1-2 1 1h0c1 0 1-1 2-1 1-1 2 0 3 0s1 0 2-1c1 0 2 1 3 2v1c-1 0-2 0-3-1l-2 2c0 2-1 2-2 3-1 0-2 0-2 1h-1l-1 2 1 1-1 2 1 1c1 0 1 1 1 2l1 1-3 1h-1-5l-2-1z" class="f"></path><path d="M717 240v-2h-2v-1l1-1 2 1 2-2c1-1 2 0 3-1h1l-2 2c0 2-1 2-2 3-1 0-2 0-2 1h-1 0z" class="D"></path><path d="M715 250c-2-1-4-1-6-2l-1-1c0-2 1-2 2-3v-2c1-1 2-3 4-4v1l1 1h1 1 0l-1 2 1 1-1 2 1 1c1 0 1 1 1 2l1 1-3 1h-1z" class="B"></path><path d="M686 191c2 0 3 0 5-1h3v4c1 1 2 2 3 2v1c0 1 1 2 2 3v1c1 1 2 1 3 3h1 3 1v-1c3 0 4 0 6 1v2h1l-1 1c-6 9-14 18-19 28l-2 4-1 1-1 3c0 2 0 4-1 5v2h-1c-3-2-6-6-8-9l-3-3-7-10c-2-2-3-4-5-6 0-1-2-3-3-4v-2l2-2v-1l-1-1v-1-6h0l1-5c1-2 2-3 3-4 4-5 9-6 15-6l4 1z" class="E"></path><path d="M669 215c1 2 2 5 3 7-1 0-2 1-3 1l-4-6 2-1c1 0 2 0 2-1z" class="B"></path><path d="M670 206c1-1 1-2 1-2l1-2c1-2 2-3 4-4s4-2 6-2l-7 7v1l-1 1h-2l-2 1z" class="Z"></path><path d="M663 205h1c1-1 1-2 1-3h0 1l1 1v-1c-1 0-1-1-2-1l1-1c2 0 2 0 3 1-2 5-1 9 0 14 0 1-1 1-2 1l-2 1-1-4-1-1v-1-6z" class="D"></path><path d="M670 206l2-1h2l-1 2c1 2 1 2 3 3-1 1-1 1-3 2 1 0 2 1 3 1l2-2c1 0 1 0 2 2v1c-1 1-1 1-1 3l1 2h-1v1c0 1 0 2 1 3h-2c0 1-1 2-2 2h0c-2-3-4-7-5-11-1-3-1-5-1-8z" class="M"></path><path d="M679 219c-1 0-2-1-3 0h-1v-1l1-1 1-1c-1-1-1-1-1-2 2-2 2-1 4-1v1c-1 1-1 1-1 3l1 2h-1z" class="D"></path><path d="M670 206l2-1h2l-1 2c1 2 1 2 3 3-1 1-1 1-3 2h0-1l3 3h-1c-1 0-2-1-3-1-1-3-1-5-1-8z" class="V"></path><path d="M686 191c2 0 3 0 5-1h3v4c1 1 2 2 3 2v1c0 1 1 2 2 3v1h-2c-1 2 0 4-1 6-1 1-1 2-2 4v-3c0-1 0-1-1-2 1-1 1-1 1-2-1-1-2-2-2-3s1-1 1-2v-1h-3-1l-1-2c-3-2-5-3-9-3-3 1-6 2-8 5l-1 2h-1v1c-1-1-1-1-3-1l-1 1c1 0 1 1 2 1v1l-1-1h-1 0c0 1 0 2-1 3h-1 0l1-5c1-2 2-3 3-4 4-5 9-6 15-6l4 1z" class="H"></path><path d="M686 191c2 0 3 0 5-1h3v4c1 1 2 2 3 2v1c0 1 1 2 2 3v1h-2c-1 2 0 4-1 6 0-3 0-5-1-8-2-4-5-6-9-8z" class="E"></path><path d="M688 196l1 2h1 3v1c0 1-1 1-1 2s1 2 2 3c0 1 0 1-1 2 1 1 1 1 1 2v3c0 1-1 2-2 3h2v1l-2 1c0 1-1 2-2 3h-1-4l-2-2-3-3v-1c-1-2-1-2-2-2l-2 2c-1 0-2-1-3-1 2-1 2-1 3-2-2-1-2-1-3-3l1-2 1-1v-1l7-7h0c2 0 2 0 4 1 1 0 1 0 2-1z" class="q"></path><path d="M675 204h1l1 2c0 1 0 0-1 1v2 1c-2-1-2-1-3-3l1-2 1-1z" class="H"></path><path d="M688 196l1 2c1 2 2 4 1 7-1 0-2 1-3 1 1-1 0-3 0-4-1-3-2-4-5-6 2 0 2 0 4 1 1 0 1 0 2-1z" class="Q"></path><path d="M678 208c0-2 0-4-1-6 2-2 2-2 4-3l2 1c1 1 2 1 2 2 1 1 1 2 0 3h-1l-2 3h-1c-1-1-2 0-3 0z" class="V"></path><path d="M683 200c1 1 2 1 2 2 1 1 1 2 0 3h-1c-1-1-2-2-4-3h0v-1l3-1z" class="D"></path><path d="M685 205c0 2-1 3-3 4-1 2 0 2 0 4 1 2 1 3 3 4 3 1 5-1 7-3h2v1l-2 1c0 1-1 2-2 3h-1-4l-2-2-3-3v-1c-1-2-1-2-2-2l-2 2c-1 0-2-1-3-1 2-1 2-1 3-2v-1l2-1c1 0 2-1 3 0h1l2-3h1z" class="I"></path><path d="M689 198h1 3v1c0 1-1 1-1 2s1 2 2 3c0 1 0 1-1 2 1 1 1 1 1 2v3c0 1-1 2-2 3-2 2-4 4-7 3-2-1-2-2-3-4 0-2-1-2 0-4 2 0 3 0 5-1l1 1 1-1 1-3c1-3 0-5-1-7z" class="a"></path><path d="M689 208c2 0 2 0 3 1v1h-1c-1 1-2 1-3 2v1h-1l-1-2 2-2 1-1z" class="g"></path><path d="M696 207c1-2 0-4 1-6h2c1 1 2 1 3 3h1 3 1v-1c3 0 4 0 6 1v2h1l-1 1c-6 9-14 18-19 28l-2 4-1 1-1 3c0 2 0 4-1 5v2h-1c-3-2-6-6-8-9l-3-3-7-10c-2-2-3-4-5-6 0-1-2-3-3-4v-2l2-2v-1l1 4 4 6c1 0 2-1 3-1l3 4 1-1h0c1 0 2-1 2-2h2c-1-1-1-2-1-3v-1h1l-1-2c0-2 0-2 1-3l3 3 2 2h4 1c1-1 2-2 2-3l2-1v-1h-2c1-1 2-2 2-3 1-2 1-3 2-4z" class="Z"></path><path d="M692 223h3 0l2 1 1 1c-1 1 0 1-1 2l-1-2c-1 1-1 0-2 1 0-2-2-1-4-3h2z" class="R"></path><path d="M684 229l1 1c0 1 1 1 2 2 0 1 1 2 2 2-1 1-2 1-3 2l-5-5c1 0 2-1 3-2z" class="M"></path><path d="M687 236v1c1 1 2 1 3 2h0c1 0 1 0 1 1l-1 3c-2-1-3-2-4-4l-2-2 3-1z" class="Y"></path><path d="M696 218h1l2-2 1 1c0 3-2 4-3 7l-2-1h0c1-1 1-1 1-2l-2-1 2-2h0z" class="V"></path><path d="M678 223l1 2 1 1c0-2 0-2 1-3 1 0 2 1 3 2l1 1h-2 0l-1 1c0 1 1 2 2 2-1 1-2 2-3 2-2-2-3-4-5-6 1 0 2-1 2-2z" class="T"></path><path d="M694 215l2 3-2 2 2 1c0 1 0 1-1 2h-3-2c0-2 0-3-1-4h1c1-1 2-2 2-3l2-1z" class="M"></path><path d="M694 215l2 3-2 2h-1c0-2 0-2-1-4l2-1z" class="T"></path><g class="q"><path d="M690 219l2 2h1l-1 2h-2c0-2 0-3-1-4h1z"></path><path d="M686 226h2l2 2c0-1-1-2-1-3s1-1 1-2c2 2 4 1 4 3s0 2-1 3v1 1c-1 0-2 1-2 2v1h-1c-1-1-1-2-2-2l-1-1c0-2 0 0 1-2l-1-1-1-2z"></path></g><path d="M680 214l3 3 2 2h4c1 1 1 2 1 4 0 1-1 1-1 2s1 2 1 3l-2-2h-2-1l-1-1c-1-1-2-2-3-2-1 1-1 1-1 3l-1-1-1-2h2c-1-1-1-2-1-3v-1h1l-1-2c0-2 0-2 1-3z" class="B"></path><path d="M683 217l2 2h0c0 1 0 1-1 2h-1c-2 0-2-1-3-2l1-1h2v-1z" class="F"></path><path d="M696 207c1-2 0-4 1-6h2c1 1 2 1 3 3h1l-3 2-1 2c0 1 0 2 1 3l1 1c0 1 1 2 1 3v3h-1l-1-1-1-1-2 2h-1 0l-2-3v-1h-2c1-1 2-2 2-3 1-2 1-3 2-4z" class="m"></path><path d="M696 212h1v4l-1 2h0l-2-3v-1l2-2z" class="M"></path><path d="M701 212c0 1 1 2 1 3v3h-1l-1-1-1-1-2 2h-1l1-2 2-1h1l1-2v-1z" class="Q"></path><path d="M696 207c1-2 0-4 1-6h2c1 1 2 1 3 3h1l-3 2h-2c-1 1-1 2-1 4l-1 2h0l-2 2h-2c1-1 2-2 2-3 1-2 1-3 2-4z" class="j"></path><path d="M707 203c3 0 4 0 6 1v2h1l-1 1c-3 1-4 5-7 7 0 1-1 1-1 1h-3c0-1-1-2-1-3l-1-1c-1-1-1-2-1-3l1-2 3-2h3 1v-1z" class="H"></path><path d="M706 205c2-1 2-1 4-1l1 1c0 1-1 2-2 2h-2l-1-2z" class="e"></path><path d="M703 204h3v1l1 2h2c-1 2-1 3-3 4l-2-1c-1 0-1 1-2 1h-2c-1-1-1-2-1-3l1-2 3-2z" class="o"></path><path d="M702 211c0-1-1-2-1-3l2-1c0-1 1-1 2-2 1 1 1 2 2 3l-1 1h-2v1c-1 0-1 1-2 1z" class="v"></path><path d="M664 213l1 4 4 6c1 0 2-1 3-1l3 4 1-1h0c2 2 3 4 5 6l5 5h1l-3 1 2 2c1 2 2 3 4 4 0 2 0 4-1 5v2h-1c-3-2-6-6-8-9l-3-3-7-10c-2-2-3-4-5-6 0-1-2-3-3-4v-2l2-2v-1z" class="x"></path><path d="M686 239c1 2 2 3 4 4 0 2 0 4-1 5v-1c-3-2-5-4-7-7h2 1l1-1z" class="S"></path><path d="M672 222l3 4 1 1v1c1 2 3 6 3 8-4-4-7-8-10-13 1 0 2-1 3-1z" class="F"></path><path d="M676 225h0c2 2 3 4 5 6l5 5h1l-3 1 2 2-1 1h-1-2l-3-4c0-2-2-6-3-8v-1l-1-1 1-1z" class="R"></path><path d="M676 225h0c2 2 3 4 5 6l5 5h1l-3 1c-2-3-5-7-8-9v-1l-1-1 1-1z" class="n"></path><defs><linearGradient id="AD" x1="403.818" y1="315.121" x2="397.41" y2="256.464" xlink:href="#B"><stop offset="0" stop-color="#b4b2b2"></stop><stop offset="1" stop-color="#f3f2f1"></stop></linearGradient></defs><path fill="url(#AD)" d="M427 211h0 3c1 1 2 1 3 1 1-1 2-1 4-1 0 1 1 1 2 1h0l1 1c2 1 3 1 5 2v1h1c0 1 1 1 1 2h5l-1-2c2 1 4 1 6 2l1-3h1l2 1 1 1 1 1 1-2v1c2 2 3 5 4 7l3 3v2c1 1 1 1 2 1s0 0 1 1h1v-2c0-1-1-3-2-5h3l1-2h0l1-1-1-4 1-2 1 3 3 3c1 2 1 4 1 6h0c1 1 1 2 1 2l1 5c0 3 1 6 0 8h0l-1 1v2c-2 0-1-1-3 0h-3 0-1l-2 1h-1-4v6l1 1-1 1h-1c-1-1-1-1-3-1h0l-1 3-8 1-12 3c-1 0-4 2-5 2-32 13-60 30-83 56-2 0-3 0-5 2 0 1-1 2-2 2h-1v-1c-2-1-2-1-2-3l-4 6h-2l-1 2-2-2v-1l1-1c1-3 1-5 2-8l2-5c-1-2-1-2-2-5v-1-1h1c0-2 0-4 1-5l-1-1c-1-2-3-3-4-5l-12-8-1-1-1-1v-1c-2-2-3-3-4-5 0-1-2-2-3-3v-1c-1 0 0-1 0-2l-1-1 2-1 1 1 2-2c-1-2-1-4-2-6h1l1 3 1-1c0 1 2 2 2 1h2c1-1 1-1 3-1h0l7-6 5-4c2 0 3 1 4 1 1-1 1-2 1-3h0v-2l2-3 1 3h2 0c0-1 0-1 1-3 0 2 1 4 2 5 0 1 0 1 1 2v-2h2c0 1-1 3 0 5l2-2c1 0 1-1 2-2l1 1 1-1c0 1 1 2 1 3l2-3h0l1-1c2-5 5-8 8-12 1-1 2-2 4-3v-1h1 1c-1 2-2 3-2 4h1l4-6 2-3c0-2 4-8 6-10l3-1c1-2 4-5 6-6h2v1h2l-4 3 1 1-1 1c2 2 2 2 3 4-1 0-1 0-1 1h2c1 0 2 0 2-1 2-2 5-6 7-7h3v-1c3-1 5-1 7-2l3-1z"></path><path d="M434 256v2c-2 0-6 3-7 3h-1v-1c-1 1-2 2-3 2h-2-1l-1 1h-2l17-7z" class="o"></path><path d="M415 257c0-1 1-1 2-1 1-1 2-2 4-2h2c1-1 2-2 2-4l1-1c0-1 2 0 3 0 1 1 2 2 3 2s2 1 2 1c3-1 9-3 11-2-2 1-6 1-7 4-1 0-2 1-4 2l-17 7h0v-1l-5 2c-1 0-2 1-3 1v-1h1v-1c1-1 2-3 3-4 1 0 2-1 3-1l-1-1z" class="W"></path><path d="M412 264l-1-1c3-3 7-4 10-5 4-2 7-3 12-4-4 2-8 4-11 5-1 1-4 1-5 2v1l-5 2z" class="N"></path><path d="M415 257c0-1 1-1 2-1 1-1 2-2 4-2h2c1-1 2-2 2-4l1-1c0-1 2 0 3 0 1 1 2 2 3 2s2 1 2 1c-2 1-3 1-5 1l-1 1-9 2c-1 1-2 2-3 2l-1-1z" class="B"></path><defs><linearGradient id="AE" x1="468.216" y1="251.58" x2="447.152" y2="253.347" xlink:href="#B"><stop offset="0" stop-color="#9c9a98"></stop><stop offset="1" stop-color="#d2d1d0"></stop></linearGradient></defs><path fill="url(#AE)" d="M458 244c1 0 1 1 2 0 1 0 3 0 4-1h4v2h-1c-2 0-3 1-5 1s-3 1-4 1l-1 1h2l11-2v6l1 1-1 1h-1c-1-1-1-1-3-1h0l-1 3-8 1-12 3c-1 0-4 2-5 2l-1-1c1-1 2-1 4-1v-2h2c0-1 0 0 1-1 2-2 4-4 6-5h1v-1c-3 0-8 2-10 2h-1l-4 1c1-3 5-3 7-4 2 0 4-1 5-1 2-1 5-1 7-2l-1-1h-3c0-1 1-1 3-1 1-1 1-1 1-2l1 1z"></path><path d="M466 253v-1c0-1 0-2 1-2l3 2 1 1-1 1h-1c-1-1-1-1-3-1z" class="C"></path><path d="M457 257c1-1 2-2 4-2 1-1 1-1 2-1 1-1 2-1 3-1l-1 3-8 1z" class="T"></path><path d="M458 244c1 0 1 1 2 0 1 0 3 0 4-1h4v2h-1c-2 0-3 1-5 1s-3 1-4 1l-1 1h2c-5 1-12 2-17 5l-4 1c1-3 5-3 7-4 2 0 4-1 5-1 2-1 5-1 7-2l-1-1h-3c0-1 1-1 3-1 1-1 1-1 1-2l1 1z" class="l"></path><path d="M477 217l1-2 1 3 3 3c1 2 1 4 1 6h0c1 1 1 2 1 2l1 5c0 3 1 6 0 8h0l-1 1v2c-2 0-1-1-3 0h-3 0-1l-2 1h-1-4l-11 2h-2l1-1c1 0 2-1 4-1s3-1 5-1h1v-2h-4c-1 1-3 1-4 1-1 1-1 0-2 0l1-1c1-1 1-1 3-2h0l1-1-2-3h0-2v-1h2 1l1-1h2l1-1 1-1v-1h1l1 1 1-2 1-2c1 1 1 1 2 1s0 0 1 1h1v-2c0-1-1-3-2-5h3l1-2h0l1-1-1-4z" class="Q"></path><path d="M469 233l1-2h0c1 1 3 1 4 1v1c-1 1-2 2-3 2h0l-2-2z" class="Z"></path><path d="M474 246l-1-1c0-1 1 0 2-1l1-2h2c0 1-1 2 0 3h0-1l-2 1h-1z" class="x"></path><path d="M475 229l2 1c0 1 1 1 1 2l-1 1h0c1 1 1 2 1 3l-1 1c-1-1-2-2-2-3-1-2-1-3-2-4 1 0 0 0 1 1h1v-2z" class="J"></path><path d="M471 235h0l1 1 2 1h0l-1 2v2c-1 1-2 2-3 2l-1-1c-1 0-1 0-2-1h0v-5c0-1 0 0 1-1h3z" class="L"></path><path d="M471 235h0l1 1c-2 1-2 2-3 3v1h-1v-3l-1-1c0-1 0 0 1-1h3z" class="V"></path><path d="M478 232c1 2 2 4 2 6l2 3c0 1 1 2 1 2h1v2c-2 0-1-1-3 0h-3c-1-1 0-2 0-3v-2c0-1-1-2-1-3l1-1c0-1 0-2-1-3h0l1-1z" class="j"></path><path d="M466 234l1-1v-1h1l1 1 2 2h-3c-1 1-1 0-1 1v5h0c1 1 1 1 2 1l-1 1h-4c-1 1-3 1-4 1-1 1-1 0-2 0l1-1c1-1 1-1 3-2h0l1-1-2-3h0-2v-1h2 1l1-1h2l1-1z" class="R"></path><defs><linearGradient id="AF" x1="477.158" y1="238.205" x2="482.342" y2="228.295" xlink:href="#B"><stop offset="0" stop-color="#0f1114"></stop><stop offset="1" stop-color="#333028"></stop></linearGradient></defs><path fill="url(#AF)" d="M477 217l1-2 1 3 3 3c1 2 1 4 1 6h0c1 1 1 2 1 2l1 5c0 3 1 6 0 8h0l-1 1h-1s-1-1-1-2l-2-3c0-2-1-4-2-6 0-1-1-1-1-2l-2-1c0-1-1-3-2-5h3l1-2h0l1-1-1-4z"></path><path d="M473 224h3c0 2 0 4 1 6l-2-1c0-1-1-3-2-5z" class="H"></path><path d="M479 226h2l1 1-1 2h1c2 1 2 4 3 5 0 3 1 6 0 8h0c-2-5-4-11-6-16z" class="C"></path><path d="M477 217l1-2 1 3 3 3c1 2 1 4 1 6h0c1 1 1 2 1 2l1 5c-1-1-1-4-3-5h-1l1-2-1-1h-2c-1-1-1-2-2-4h0l1-1-1-4z" class="D"></path><path d="M477 217l1-2 1 3c0 1 0 3 1 4v3l1 1h-2c-1-1-1-2-2-4h0l1-1-1-4z" class="B"></path><path d="M410 254l2-1h0c1 1 2 1 2 0 1 0 1-1 2-1h1 0 1c-1 2-2 2-3 3-1 0-1 1-1 2h1l1 1c-1 0-2 1-3 1-1 1-2 3-3 4v1h-1v1c1 0 2-1 3-1l5-2v1c-11 5-19 11-29 17l-1 1c-1 1-1 1-2 1l-10 8v-3c-3-7-3-17-2-24h1l2-2c0-1 0-2 1-3l1 1c0 2-1 3-1 5h-1v1 4 2 1l-1 1 1 1c1-1 2-2 3-2 2-2 5-3 8-5l1 1 9-6c2-2 3-3 4-5l3-3 1 1c2 0 3-1 5-2v1z" class="b"></path><path d="M374 266h1v1l-1 1h-1 0l1-2z" class="E"></path><path d="M387 272c-2 1-5 2-7 3v1l-1-1c-1 1-2 1-3 1v-1c2-1 3-2 4-3 3 1 4 1 7 0z" class="f"></path><path d="M380 272c2-1 4-2 5-3l2 2h2l-2 1c-3 1-4 1-7 0z" class="U"></path><path d="M396 269c4-4 11-8 17-10-1 1-2 3-3 4v1h-1v1c-4 3-8 5-13 5v-1z" class="D"></path><path d="M410 254l2-1h0c1 1 2 1 2 0 1 0 1-1 2-1h1 0 1c-1 2-2 2-3 3-1 0-1 1-1 2-3 2-25 14-25 14h-2l-2-2 3-1 9-6c2-2 3-3 4-5l3-3 1 1c2 0 3-1 5-2v1z" class="N"></path><path d="M404 254l1 1c2 0 3-1 5-2v1c-1 1-3 2-5 4-3 1-5 3-8 4 2-2 3-3 4-5l3-3z" class="C"></path><path d="M412 264l5-2v1c-11 5-19 11-29 17l-1 1c-1 1-1 1-2 1-2 0-6 4-8 5-1-2-1-4-2-6v-1l1-1c0-1 1-1 2-2 2-1 5-1 7-2 4-2 7-4 11-6v1c5 0 9-2 13-5 1 0 2-1 3-1z" class="T"></path><path d="M376 279h3 0c2-1 4-2 6-2l1-1 1 1c-1 1-2 2-4 2-2 1-4 4-6 4v-2c-1 0-1 0-2-1l1-1zm82-64h1l2 1 1 1 1 1 1-2v1c2 2 3 5 4 7l3 3v2l-1 2-1 2-1-1h-1v1l-1 1-1 1h-2l-1 1h-1-2v1h2 0l2 3-1 1h0c-2 1-2 1-3 2l-1 1-1-1c0 1 0 1-1 2-2 0-3 0-3 1h3l1 1c-2 1-5 1-7 2-1 0-3 1-5 1-2-1-8 1-11 2 0 0-1-1-2-1s-2-1-3-2c-1-2-2-4-2-6 0-5 1-8 3-12 2-3 3-5 5-7l3-2 1 1 1-1h1l2 1 4-3c-1 0-1-1-1-1v-1h1 5l-1-2c2 1 4 1 6 2l1-3z" class="B"></path><path d="M434 244h-2v1h-1v-4h1l1 1 1 2z" class="D"></path><path d="M441 239l1-2h1l1 3c-1 1-2 0-4 1 0-2 0-1 1-2z" class="T"></path><path d="M456 233l3 3v1h2 0v3l-2-1c-1 1 0 2-1 3-2 0-2 0-2-1l2-2c0-1-2-2-2-4-1-1 0-1 0-2z" class="a"></path><path d="M438 238l3 1c-1 1-1 0-1 2h1 3c1 2 0 2 1 4v1h-1c-1-1-2-1-3-2l-2 2c-2 0-2-1-3-2l-1-1v-3l1-1c1 0 1 0 2-1h0z" class="D"></path><path d="M435 243c1-1 1-1 3-2 1 0 1 1 2 2l1 1-2 2c-2 0-2-1-3-2l-1-1z" class="L"></path><path d="M438 222l1 1 1-1h1c-1 1-1 2-2 3h-1l-1 2-2 2h0c0 2-2 2-2 4l-2 1v1l-2 2 1 1h-1c-1 3-1 6 0 8s2 3 4 4c1 0 5 1 6 0l1-1c1 0 3-1 4-1s1-1 2-1l1 1 2-2h3 1 3l1 1c-2 1-5 1-7 2-1 0-3 1-5 1-2-1-8 1-11 2 0 0-1-1-2-1s-2-1-3-2c-1-2-2-4-2-6 0-5 1-8 3-12 2-3 3-5 5-7l3-2z" class="t"></path><path d="M438 222l1 1 1-1h1c-1 1-1 2-2 3h-1l-3 1v-2l3-2z" class="L"></path><path d="M468 224l3 3v2l-1 2-1 2-1-1h-1v1l-1 1-1 1h-2l-1 1h-1-2l-3-3c0 1-1 1 0 2 0 3-1 4-2 6l-1 1c-2 0-3 0-5-1-1-1-1-1-1-2h0l-1-3h0c1-1 1-3 3-3l1-1-1-1 1-1h5l3 1c0-1 1-1 2-1l1 1c1 0 2-1 2-2l3-1 2-4z" class="n"></path><path d="M471 227v2l-1 2-1 2-1-1h-1v1l-1 1h-1l-2-2c2 0 2 0 4-1 0-1 0-1 1-2s2-2 3-2z" class="V"></path><path d="M456 233l1-2h1l1 1h0 4l2 2h1l-1 1h-2l-1 1h-1-2l-3-3z" class="x"></path><path d="M459 232h4l2 2h1l-1 1h-2l-1 1c-1-1-2-3-3-4z" class="L"></path><path d="M450 232c2 1 3 1 4 3 1 1 1 2 0 3s-2 2-3 2c-2 1-3 0-4-1l-1-3h0c1-1 1-3 3-3l1-1z" class="g"></path><path d="M449 233h1v2l-1 2-3-1c1-1 1-3 3-3z" class="e"></path><path d="M458 215h1l2 1 1 1 1 1 1-2v1c2 2 3 5 4 7l-2 4-3 1c0 1-1 2-2 2l-1-1c-1 0-2 0-2 1l-3-1h-5l-1 1 1 1-1 1c-2 0-2 2-3 3h0l-1-1v1c-1 0-2 1-4 1-1 0-2 0-3 1h0c-1 1-1 1-2 1l-1 1v3l1 1-1 1-1-1-1-2v-5l1-1 1-1c0-1 1-3 2-4l-2-2 2-2 1-2h1c1-1 1-2 2-3l2 1 4-3c-1 0-1-1-1-1v-1h1 5l-1-2c2 1 4 1 6 2l1-3z" class="W"></path><path d="M435 235h1l1 1c0 1-1 2-2 3h0l-1-1v-2l1-1zm5-7s2 0 2-1c1-1 0-1 2-2 1 0 2 1 3 2h0l-2 2c0 1 1 1 0 3l-1 1c-1 0-2 1-3 1v2h-1c0-1 0-2 1-3l-1-1v1h-2c0-1 0-1-1-2l3-3z" class="g"></path><path d="M447 220l1 1 1 1h0c2 0 3 1 4 1 0 1 1 2 1 3 1 1 2 0 4 1l-3 3h-5c-1-1-1-1-1-2l2-1h0l-2-2c-2-1-3-1-5-1-1 0-2 1-2 2l-2 2-3 3h0l-2-2 2-2 1-2h1c1-1 1-2 2-3l2 1 4-3z" class="T"></path><path d="M437 227h1c1-1 2-1 4-1l-2 2-3 3h0l-2-2 2-2z" class="D"></path><path d="M458 215h1l2 1 1 1 1 1 1-2v1c2 2 3 5 4 7l-2 4-3 1c0 1-1 2-2 2l-1-1c-1 0-2 0-2 1l-3-1 3-3c-2-1-3 0-4-1 0-1-1-2-1-3-1 0-2-1-4-1h0l-1-1-1-1c-1 0-1-1-1-1v-1h1 5l-1-2c2 1 4 1 6 2l1-3z" class="R"></path><path d="M446 218l6 1 1 1-1 1-2-1c-1 1-1 1-2 1l-1-1c-1 0-1-1-1-1v-1z" class="d"></path><path d="M463 224l2 2c0 1 0 1 1 2l-3 1c-1 0-1 0-2-1h-1v-1h1v1l1-2 1-2z" class="B"></path><path d="M451 216c2 1 4 1 6 2h0c1 1 1 2 2 2 1 2 2 4 2 5h-2c-2-2-3-4-5-5l1-1-2-1h-1l-1-2z" class="C"></path><path d="M458 215h1l2 1 1 1 1 1 1-2v1c2 2 3 5 4 7l-2 4c-1-1-1-1-1-2l-2-2-1 2-1-1c0-1-1-3-2-5-1 0-1-1-2-2h0l1-3z" class="U"></path><path d="M458 215h1l4 5c-2 1-1 0-2-1-1 0-3 0-4-1h0l1-3z" class="h"></path><path d="M457 218c1 1 3 1 4 1 1 1 0 2 2 1 1 1 1 1 1 2l-1 1v1l-1 2-1-1c0-1-1-3-2-5-1 0-1-1-2-2z" class="L"></path><path d="M427 211h0 3c1 1 2 1 3 1 1-1 2-1 4-1 0 1 1 1 2 1h0l1 1c2 1 3 1 5 2v1h1c0 1 1 1 1 2h-1v1s0 1 1 1l-4 3-2-1h-1l-1 1-1-1-3 2c-2 2-3 4-5 7-2 4-3 7-3 12 0 2 1 4 2 6-1 0-3-1-3 0l-1 1c0 2-1 3-2 4h-2c-2 0-3 1-4 2-1 0-2 0-2 1h-1c0-1 0-2 1-2 1-1 2-1 3-3h-1 0-1c-1 0-1 1-2 1 0 1-1 1-2 0h0l-2 1v-1c-2 1-3 2-5 2l-1-1 1-2c1-1 1-2 1-3l1-1c1-2 0-4-1-6l-1-2-2-4v-2l-1-1 2-2c0-1-1-2-1-3 1-1 1-1 1-3 0-1 1-1 1-2 1 0 2 0 2-1 2-2 5-6 7-7h3v-1c3-1 5-1 7-2l3-1z" class="a"></path><path d="M412 243v-4l2 1c0 1 1 1 1 2l-3 1zm2-18v-1c1 1 2 1 2 1h1v3c-2-1-2-2-3-3z" class="D"></path><path d="M421 222h1c1 0 2 0 3 1l-1 1c-1 0-2 0-3-1v-1z" class="g"></path><path d="M417 246c1 0 1 0 2 1v1l-1 1-1 1-1-1c0-1 1-1 1-2v-1z" class="N"></path><path d="M408 237l1 1h-1c0 1 0 1-1 2 1 0 2 1 2 2h-3l-1-2 3-3z" class="D"></path><path d="M438 222c2-1 3-2 5-3h3s0 1 1 1l-4 3-2-1h-1l-1 1-1-1z" class="l"></path><path d="M406 242h3v1c0 2 1 3 1 4 0 2-1 2-2 3v1l-3 1c1-1 1-2 1-3l1-1c1-2 0-4-1-6z" class="C"></path><path d="M417 225c3 0 5 1 7 1l1 1h2 1l1 1c1-1 1-2 2-3l1 1s-1 1-1 2l-2 2-3 6v2l-1-1h0v-1l1-2v-1c1-1 1-2 1-3-1-1-1-1-3 0-1-1-1-1-2-1-1-1-3-1-4-1h-1v-3z" class="H"></path><path d="M427 211h0 3c1 1 2 1 3 1 1-1 2-1 4-1 0 1 1 1 2 1h0l1 1c2 1 3 1 5 2v1l-1 1c-1 0 0 0-1 1h-2c-3 1-5 4-7 6l-1-1 4-4c-2-2-4 0-4-3h-1l-1 2h-1v-1c-1-2-1-2-2-3-1 0-1 0-2 1h-3-1c-1 1 0 1-2 1-1 0-1 1-2 2h0c-1 2-2 3-2 4-2 0-2 0-3 1 0-1-1-3-1-3 1-3 4-3 5-5h0v-1c3-1 5-1 7-2l3-1z" class="D"></path><path d="M407 222c2-2 5-6 7-7h3 0c-1 2-4 2-5 5 0 0 1 2 1 3h-1c-1 1-2 2-3 4h1c1-1 1-1 2 0h0l2-2c1 1 1 2 3 3h1c1 0 3 0 4 1 1 0 1 0 2 1 2-1 2-1 3 0 0 1 0 2-1 3v1s-1 1-2 1v-1l-2 1v2l1 1s-2 4-3 4c-1 1-2 2-4 2l-1-1c1 0 1-1 1-1 1 0 3 0 3-1-1-1-2 0-3 0h-2v-1c-1 0-1-1-1-2l-2-2c-1 0-2-1-3-1v2l-3 3-2-4v-2l-1-1 2-2c0-1-1-2-1-3 1-1 1-1 1-3 0-1 1-1 1-2 1 0 2 0 2-1z" class="N"></path><path d="M409 233c3 0 3 0 5 2l-1 1c-2-1-3-1-4-3z" class="O"></path><path d="M418 228c1 0 3 0 4 1 1 0 1 0 2 1l-1 1h0c-3 0-4-2-5-3z" class="a"></path><path d="M416 234v-1l2 1c1 1 1 1 2 1l1 1-2 1c-1-1-3-1-4-2l1-1z" class="B"></path><path d="M407 222c2-2 5-6 7-7h3 0c-1 2-4 2-5 5 0 0 1 2 1 3h-1c-1 1-2 2-3 4 1 3 1 3 4 5l3 2-1 1h-1c-2-2-2-2-5-2h-2l1 2v2l-3 3-2-4v-2l-1-1 2-2c0-1-1-2-1-3 1-1 1-1 1-3 0-1 1-1 1-2 1 0 2 0 2-1z" class="C"></path><path d="M407 233v-1-1l1-1c0-3-2-1-3-3 1-2 2-1 3-3v-1l1-1c1 0 2 0 3 1-1 1-2 2-3 4 1 3 1 3 4 5l3 2-1 1h-1c-2-2-2-2-5-2h-2z" class="D"></path><path d="M403 212v1h2l-4 3 1 1-1 1c2 2 2 2 3 4-1 0-1 0-1 1h2c0 1-1 1-1 2 0 2 0 2-1 3 0 1 1 2 1 3l-2 2 1 1v2l2 4 1 2c1 2 2 4 1 6l-1 1c0 1 0 2-1 3l-1 2-3 3c-1 2-2 3-4 5l-9 6-1-1c-3 2-6 3-8 5-1 0-2 1-3 2l-1-1 1-1v-1-2-4-1h1c0-2 1-3 1-5l-1-1c-1 1-1 2-1 3-1-1-1-2-1-4 0-1 0-1-1-2 0-2 0-3 1-5s1-4 2-7c0-1 1-3 1-4v-2s0-1 1-2v-1h1 1c-1 2-2 3-2 4h1l4-6 2-3c0-2 4-8 6-10l3-1c1-2 4-5 6-6h2z" class="V"></path><path d="M394 231l1-1h1c0 2 1 2 2 4l1 1v1h-6l-2 1c-1 0-1-1-1-2l3-3c1 0 1 0 1-1z" class="I"></path><path d="M399 236h1 0c3 2 5 5 6 7-1 1-1 2-2 2l-1-1-1-1-1-1c-2-2-3-2-6-3h0c-2-1-1-1-2-3h6z" class="g"></path><path d="M391 237l2-1c1 2 0 2 2 3h0c3 1 4 1 6 3l1 1c-1 1-2 1-2 2l-1-1h-1l-2-2h-1-1c-1-1-1 0-2 0l-1-1c-1 1-2 1-3 0v-1-1l3-2z" class="m"></path><path d="M396 242l1-1h2v3h-1l-2-2z" class="L"></path><path d="M391 237l2-1c1 2 0 2 2 3h0c-1 2-3 1-4 2s-2 1-3 0v-1-1l3-2z" class="W"></path><path d="M387 241l1-1v1c1 1 2 1 3 0l1 1c1 0 1-1 2 0h1 1l2 2c-2-1-4-1-6 0-1 0-2 1-3 2 0 0 0 1-1 1l-1 3h-3v1h-2l1-3c1-2 3-5 4-7z" class="f"></path><path d="M401 216l1 1-1 1c-1 2-2 3-4 5-1 2-5 5-6 8h3c0 1 0 1-1 1l-3 3c0 1 0 2 1 2l-3 2v1l-1 1-2-3 1-3 2-1-1-1c3-6 9-10 12-15l2-2z" class="b"></path><path d="M388 234l1 2c0 1-1 2-1 3v1l-1 1-2-3 1-3 2-1z" class="E"></path><path d="M401 218c2 2 2 2 3 4-1 0-1 0-1 1h2c0 1-1 1-1 2 0 2 0 2-1 3 0 1 1 2 1 3l-2 2 1 1v2l-3-1c0-2 0-3-1-4l1-1v-2l1-1c1 0 2 0 3-1v-1l-1-1c-1 1-2 1-3 1-2 1-1 1-1 3 0 0-1 0-1 1-1 0-2 0-3-1 0-2 1-1 2-3v-2c2-2 3-3 4-5z" class="M"></path><path d="M403 212v1h2l-4 3-2 2c-3 5-9 9-12 15l1 1-2 1v-1c-1 1-2 1-2 2v-4l2-3c0-2 4-8 6-10l3-1c1-2 4-5 6-6h2z" class="R"></path><path d="M403 212v1h2l-4 3-2 2c0-2 1-4 2-6h2z" class="F"></path><path d="M392 219l3-1c0 3-4 5-5 8h-1l-1 4c-1 0-2-1-2-1 0-2 4-8 6-10z" class="L"></path><path d="M406 243c0 6-4 9-7 14-2 2-4 4-7 6-1 1-5 3-5 4-3 2-6 3-8 5-1-2-1-3-1-4l9-8h1l2-1h0l2-2h0 1c0-1 1-2 2-2 1-2 0-5 2-7l2-1c0 1 1 1 1 3v1l1 1v-2h0v-1c0-1-1-2-1-3v-1c0-1 1-1 2-2l1 1 1 1c1 0 1-1 2-2z" class="H"></path><path d="M402 243l1 1c0 3 0 4-2 6h0v-1c0-1-1-2-1-3v-1c0-1 1-1 2-2z" class="r"></path><path d="M392 257h1c0-1 1-2 2-2 1-2 0-5 2-7 1 2 1 3 1 5 0 1-1 3-2 4v1c-3 3-9 7-13 8 3-3 8-5 9-9z" class="N"></path><path d="M392 244c2-1 4-1 6 0h1l1 1v1c0 1 1 2 1 3v1h0v2l-1-1v-1c0-2-1-2-1-3l-2 1c-2 2-1 5-2 7-1 0-2 1-2 2h-1 0l-2 2h0l-2 1h-1c-2 0-3 1-4 2-2-1-1-1-2-2s-1-1-1-2c1-2 2-3 2-4l-1-1v-1l1-1h2v-1h3l1-3c1 0 1-1 1-1 1-1 2-2 3-2z" class="i"></path><path d="M392 244h5v1l-6 9-1-2c1-2 2-3 3-4v-1c-2-1-3 0-5 0 1 0 1-1 1-1 1-1 2-2 3-2z" class="H"></path><path d="M388 247c2 0 3-1 5 0v1c-1 1-2 2-3 4l1 2c-3 2-7 6-10 6-1-1-1-1-1-2 1-2 2-3 2-4l-1-1v-1l1-1h2v-1h3l1-3z" class="O"></path><path d="M382 251h2v-1h3l-2 4-1 2c-2 2-3 0-3 4-1-1-1-1-1-2 1-2 2-3 2-4l-1-1v-1l1-1z" class="F"></path><path d="M379 234h1 1c-1 2-2 3-2 4h1l4-6v4c0-1 1-1 2-2v1l-1 3 2 3c-1 2-3 5-4 7l-1 3-1 1v1l1 1c0 1-1 2-2 4 0 1 0 1 1 2s0 1 2 2c1-1 2-2 4-2l-9 8c0 1 0 2 1 4-1 0-2 1-3 2l-1-1 1-1v-1-2-4-1h1c0-2 1-3 1-5l-1-1c-1 1-1 2-1 3-1-1-1-2-1-4 0-1 0-1-1-2 0-2 0-3 1-5s1-4 2-7c0-1 1-3 1-4v-2s0-1 1-2v-1z" class="I"></path><path d="M383 262c-1 1-2 1-3 2l-1-2c-1-1 1-2 1-4h0 0c0 1 0 1 1 2s0 1 2 2z" class="M"></path><path d="M384 236c0-1 1-1 2-2v1l-1 3 2 3c-1 2-3 5-4 7l-1 3-1 1-1 1-1 1v1l-1-1h0c0 2 0 3-1 4h-1v-2c1-3 1-6 2-9l3-6 3-5z" class="U"></path><path d="M378 254c3-5 4-10 7-16l2 3c-1 2-3 5-4 7l-1 3-1 1-1 1-1 1v1l-1-1z" class="K"></path><path d="M379 235c-1 1-1 2-1 2v2c0 1-1 3-1 4-1 3-1 5-2 7s-1 3-1 5c1 1 1 1 1 2 0 2 0 3 1 4l-2 2h-1c-1 7-1 17 2 24v3l-11 10-7 7c-4 3-8 7-10 11l-4 6h-2l-1 2-2-2v-1l1-1c1-3 1-5 2-8l1 1c2-4 3-7 6-10 1-2 1-3 2-4l2-9c2-11 4-22 3-33l1-3h1v-1l2-2c1 0 1-1 2-2l1 1 1-1c0 1 1 2 1 3l2-3h0l1-1c2-5 5-8 8-12 1-1 2-2 4-3z" class="F"></path><path d="M370 285v2c1 1 0 2 0 3l1 1v1c-2-1-3-1-3-2 0-2 0-3 2-5z" class="w"></path><path d="M362 286h1c0-1 1-3 1-4 1 0 3-1 4-1v1l-1 2-1 1v1c0 2 0 2-1 5v1c0 1-1 1-1 2h-1v-2c-1-1-2 0-3 0 0-2 1-4 2-6z" class="M"></path><path d="M370 279c1 3 1 5 2 8 0 1 0 2 2 3 1-1 1-2 1-3v3l-11 10-7 7-1-1 15-14v-1l-1-1c0-1 1-2 0-3v-2l-2-1c1-2 1-3 2-5z" class="V"></path><defs><linearGradient id="AG" x1="365.055" y1="273.455" x2="368.798" y2="280.047" xlink:href="#B"><stop offset="0" stop-color="#6f6e6c"></stop><stop offset="1" stop-color="#868483"></stop></linearGradient></defs><path fill="url(#AG)" d="M368 271h-1c1-2 1-4 3-6-1 5-1 10 0 14-1 2-1 3-2 5h-1l1-2v-1c-1 0-3 1-4 1 0 1-1 3-1 4h-1s2-7 2-8l1-4v-1c1-1 2-1 3-2z"></path><path d="M365 273c1-1 2-1 3-2-1 3-1 5-4 7l1-4v-1z" class="L"></path><path d="M378 237v2c0 1-1 3-1 4-1 3-1 5-2 7s-1 3-1 5c1 1 1 1 1 2 0 2 0 3 1 4l-2 2h-1c-1 7-1 17 2 24 0 1 0 2-1 3-2-1-2-2-2-3-1-3-1-5-2-8-1-4-1-9 0-14v-5c1-6 3-12 5-18l3-5z" class="D"></path><path d="M374 255c1 1 1 1 1 2 0 2 0 3 1 4l-2 2h-1c0-3 0-5 1-8z" class="d"></path><path d="M379 235c-1 1-1 2-1 2l-3 5c-2 6-4 12-5 18v5c-2 2-2 4-3 6h1c-1 1-2 1-3 2 0-2 0-4 1-5 1-2 1-3 1-5v-6l-1-1c0-2 1-3 0-5h0l1-1c2-5 5-8 8-12 1-1 2-2 4-3z" class="k"></path><path d="M379 235c-1 1-1 2-1 2l-3 5c-1 1-2 1-3 2v2c-1 2-2 6-3 8v3h-1v2l-1 4v-6l-1-1c0-2 1-3 0-5h0l1-1c2-5 5-8 8-12 1-1 2-2 4-3z" class="p"></path><path d="M355 296c1 1 1 2 1 3-1 2-3 5-3 8-1 1-2 2-2 3h0l5-4 1 1c-4 3-8 7-10 11l-4 6h-2l-1 2-2-2v-1l1-1c1-3 1-5 2-8l1 1c2-4 3-7 6-10 1-2 1-3 2-4h0 1l1-2v1h1l2-4z" class="X"></path><path d="M350 301h0 1l1-2v1h1c-1 3-3 6-5 9v-4c1-2 1-3 2-4z" class="L"></path><path d="M341 314l1 1c2-4 3-7 6-10v4l-7 15-1 2-2-2v-1l1-1c1-3 1-5 2-8z" class="B"></path><path d="M359 253c1 0 1-1 2-2l1 1 1-1c0 1 1 2 1 3l2-3c1 2 0 3 0 5l1 1v6c0 2 0 3-1 5-1 1-1 3-1 5v1l-1 4c0 1-2 8-2 8-1 2-2 4-2 6l-3 6-1 1c0-1 0-2-1-3l-2 4h-1v-1l-1 2h-1 0l2-9c2-11 4-22 3-33l1-3h1v-1l2-2z" class="F"></path><path d="M352 292h1l2-4c0 4-2 8-3 11l-1 2h-1 0l2-9z" class="Q"></path><path d="M366 256l1 1v6c0 2 0 3-1 5-1 1-1 3-1 5v1c-1 0-2 0-2 1v3l-1 1-1-1 3-18c1-1 1-3 2-4z" class="j"></path><path d="M361 278l1 1 1-1v-3c0-1 1-1 2-1l-1 4c0 1-2 8-2 8-1 2-2 4-2 6l-3 6-1 1c0-1 0-2-1-3l5-13 1-5z" class="E"></path><defs><linearGradient id="AH" x1="350.751" y1="267.95" x2="362.039" y2="274.159" xlink:href="#B"><stop offset="0" stop-color="#222122"></stop><stop offset="1" stop-color="#41423d"></stop></linearGradient></defs><path fill="url(#AH)" d="M359 253c1 0 1-1 2-2l1 1 1-1c0 1 1 2 1 3l2-3c1 2 0 3 0 5-1 1-1 3-2 4l-2 2h0v2c-1 1-1 3-1 4-1 1-1 1-1 2v1c0 1-1 1-2 3l-3 14-2 4h-1c2-11 4-22 3-33l1-3h1v-1l2-2z"></path><path d="M358 273v-1-7c0-1-1-3 0-3 0-1 0-1 1-2 0-2 0-3 1-4s2-1 4-1c-3 6-5 11-6 18z" class="P"></path><path d="M366 251c1 2 0 3 0 5-1 1-1 3-2 4l-2 2h0v2c-1 1-1 3-1 4-1 1-1 1-1 2v1c0 1-1 1-2 3v-1c1-7 3-12 6-18v-1l2-3z" class="M"></path><path d="M348 245l1 3h2 0c0-1 0-1 1-3 0 2 1 4 2 5 0 1 0 1 1 2v-2h2c0 1-1 3 0 5v1h-1l-1 3c1 11-1 22-3 33l-2 9c-1 1-1 2-2 4-3 3-4 6-6 10l-1-1 2-5c-1-2-1-2-2-5v-1-1h1c0-2 0-4 1-5l-1-1c-1-2-3-3-4-5l-12-8-1-1-1-1v-1c-2-2-3-3-4-5 0-1-2-2-3-3v-1c-1 0 0-1 0-2l-1-1 2-1 1 1 2-2c-1-2-1-4-2-6h1l1 3 1-1c0 1 2 2 2 1h2c1-1 1-1 3-1h0l7-6 5-4c2 0 3 1 4 1 1-1 1-2 1-3h0v-2l2-3z" class="W"></path><path d="M344 291l2-1 1 1c-1 1 0 2-2 2l-1-1v-1z" class="e"></path><path d="M357 250c0 1-1 3 0 5v1h-1l-1 3v-7-2h2z" class="S"></path><path d="M344 294v9c0 2-1 4-1 6-1-2-1-2-2-5v-1-1h1c0-2 0-4 1-5h0l1-3z" class="C"></path><path d="M349 248h2 0l1 2-2 2v5c1 2 1 4 1 6 0 3 1 8 0 11v1c-1 3 0 6-1 8l-1 1h0c-1-1 0-4 0-5l1-7v-4h-1v1c-1 3-2 5-3 8v-3c0-1 1-1 1-2v-1c-2-3 1-5 1-8l1-5v-6-4z" class="B"></path><path d="M348 245l1 3v4 6l-1 5c0 3-3 5-1 8v1c0 1-1 1-1 2v3c-1 5-2 9-2 14v1h0v2l-1 3h0l-1-1v-2c0-2 1-3 0-4 0-1 0-1 1-2v-6-2c-1 0-2-2-3-4l1 1 1-1c-1-1 0-1-1-1h-1l1-2-1-2h-1v-1l1-1 2-1v-3l-1-1c1-2 2-3 3-5 0-1 1-2 2-2h-1v-1l-1-1 1-2c1-1 1-2 1-3h0v-2l2-3z" class="S"></path><path d="M348 245l1 3v4c0 1-1 2-1 3v-4l-2-1h0v-2l2-3z" class="r"></path><path d="M345 267h1c0 2-2 3-3 4h-3-1v-1l1-1 2-1 2-1h1z" class="E"></path><path d="M343 282h1v3 7 2l-1 3h0l-1-1v-2c0-2 1-3 0-4 0-1 0-1 1-2v-6z" class="Y"></path><path d="M346 257l1 1v3 4c-1 1-2 1-2 2h-1l-2 1v-3l-1-1c1-2 2-3 3-5 0-1 1-2 2-2z" class="C"></path><path d="M344 267h0c0-2 0-2 1-3h1l1 1c-1 1-2 1-2 2h-1z" class="D"></path><path d="M346 257l1 1v3c-1 1-3 0-4 2l-1 2-1-1c1-2 2-3 3-5 0-1 1-2 2-2z" class="H"></path><path d="M341 252c2 0 3 1 4 1l-1 2 1 1v1h1c-1 0-2 1-2 2-1 2-2 3-3 5l1 1v3l-2 1-1 1v1h1l1 2-1 2h1c1 0 0 0 1 1l-1 1-1-1h0l-2-3-3 2-1-2c-1-3-2-5-1-9l-2 2c-1 0-1 0-2 1h0-1c0-1 0-3 1-4v-1l7-6 5-4z" class="b"></path><path d="M344 255l1 1v1h1c-1 0-2 1-2 2-2 1-5 3-7 3l5-5 2-2z" class="M"></path><path d="M334 263c1 5 1 7 4 10l-3 2-1-2c-1-3-2-5-1-9l1-1z" class="C"></path><path d="M337 262c2 0 5-2 7-3-1 2-2 3-3 5l1 1v3l-2 1-2-1c-1 0-2-2-2-3s1-2 1-3z" class="F"></path><path d="M341 264l1 1v3l-2 1-2-1c0-1 2-3 3-4z" class="a"></path><path d="M341 252c2 0 3 1 4 1l-1 2-2 2c-3 0-5 3-8 6h0l-1 1-2 2c-1 0-1 0-2 1h0-1c0-1 0-3 1-4v-1l7-6 5-4z" class="G"></path><path d="M321 266c-1-2-1-4-2-6h1l1 3 1-1c0 1 2 2 2 1h2c1-1 1-1 3-1h0v1c-1 1-1 3-1 4h1 0c1-1 1-1 2-1l2-2c-1 4 0 6 1 9l1 2 3-2 2 3h0c1 2 2 4 3 4v2 6c-1 1-1 1-1 2 1 1 0 2 0 4v2c-1-2-3-3-4-5l-12-8-1-1-1-1v-1c-2-2-3-3-4-5 0-1-2-2-3-3v-1c-1 0 0-1 0-2l-1-1 2-1 1 1 2-2z" class="k"></path><path d="M339 288h1v-1l1-1 2 2c-1 1-1 1-1 2 1 1 0 2 0 4l-2-2c-2-2-2-2-1-4z" class="m"></path><path d="M334 273l1 2c0 1 1 2 1 2 1 3 1 3 1 6h-1v-2c-1 0-4-2-4-4s1-2 0-4h2z" class="d"></path><path d="M329 263c-1 1-1 3-1 4h1 0c1-1 1-1 2-1l2-2c-1 4 0 6 1 9h-2-1c0 1 0 1-1 2l-4-5c0-3 1-5 3-7z" class="x"></path><path d="M338 273l2 3h0c1 2 2 4 3 4v2 6l-2-2-1 1v1h-1l-1-2-1-1v-2c0-3 0-3-1-6 0 0-1-1-1-2l3-2z" class="D"></path><path d="M338 273l2 3c-2 0-3 0-4 1 0 0-1-1-1-2l3-2z" class="i"></path><path d="M338 286c0-3 1-3 3-5h1v4l-1 1-1 1v1h-1l-1-2z" class="a"></path><path d="M321 266c-1-2-1-4-2-6h1l1 3 1-1c0 1 2 2 2 1h2c1-1 1-1 3-1h0v1c-2 2-3 4-3 7l-1 1 1 1c1 2 4 4 5 7h-2c-2-2-2-3-4-5-1-2-2-4-3-5 0-1-1-2-1-3z" class="K"></path><path d="M322 262c0 1 2 2 2 1h2c1-1 1-1 3-1h0v1c-2 2-3 4-3 7l-1 1c-1-3-3-5-4-8l1-1z" class="H"></path><path d="M321 266c0 1 1 2 1 3 1 1 2 3 3 5 0 0-1 0-1 1v1c1 1 2 2 2 3l5 4v1h-1 0l-2-1-4-3c-2-2-3-3-4-5 0-1-2-2-3-3v-1c-1 0 0-1 0-2l-1-1 2-1 1 1 2-2z" class="t"></path><path d="M318 267l1 1 1 1 1 1c1 2 0 3 2 5h0-3c0-1-2-2-3-3v-1c-1 0 0-1 0-2l-1-1 2-1z" class="R"></path><path d="M320 269l1 1h0v2c-1 1-2 0-4 0v-1c1 0 2-1 3-2z" class="U"></path><path d="M318 267l1 1 1 1c-1 1-2 2-3 2s0-1 0-2l-1-1 2-1zm457-39l1-3c1-1 1 0 1-1 0-2 4-7 5-9l1 7c1 4-1 9-1 13l-1 10c0 11 1 27 11 36h0c3 5 8 8 13 10h8c9-2 13-6 18-14l1 2h1l1 1v1h-1c-3 3-5 6-9 9l-4 4h-2c-2 2-4 3-7 3h-12l-2 2-3 1h-2l-1 2v1l-8-1c-1-1-4-1-5-1-2 1-4 0-7 1l-3 1h-1v-1l-2-2-1-1-4 3-1 2h-1c-1 1-1 1-2 1v1l-5 5v1 1s1 1 1 2v1 1l-2 2v1 1h-1c-2 1-3 2-4 3h-1c-1 1-1 1-1 2 0 4 0 7 2 11h-4l-2-3-6-6c-3-3-6-6-8-9l-5-7c-1 1-2 2-3 2l-1-1-2-2c-1-1-1-2-3-3-1 0-2-3-3-4l-2-3c-1 1-1 1-2 1l-2-3-1 1v1c-5-5-12-11-15-17l-3-3h0c0-1-1-3-2-4-1-2-4-5-5-8-1-1-1-3-2-4-1-4-4-7-6-11l4 1v-1h0 3l-1-3c-1-2-2-4-4-6-1-2-1-3 0-5l2 4 2-1 2 2h0s1 0 1-1v-6l3 3c2 3 5 7 8 9h1v-2c1-1 1-3 1-5l1-3 1-1c0 1 0 1 1 2 1 0 1 0 1 1 1-1 2-1 2-2l2 1h1l1 1 1-1 1 2c-1 2-2 4-2 6s1 3 2 4l1 1 2-1 1 1 1 1 1-1 2-2-2-1v-2l2 1h5 1l3-1-1-1c0-1 0-2-1-2l-1-1 1-2-1-1 1-2h1c0-1 1-1 2-1 1-1 2-1 2-3l2-2c1 1 2 1 3 1v-1c2-1 2-3 4-4l6-1-2-2v-2h1s1 1 2 1h0l2 1v-1-1l-1-1c1-1 2-3 3-4l1 2h1l1-1c2 3 5 6 9 7h0v-1l2-1h3 4l2 1 1 1v-1h4 0c1-2 3-3 5-4 0 1 0 2-1 3l1 1v1z" class="C"></path><path d="M693 270l2-2 1 2v2c0 1 1 1 0 2v-1h-1l-2-1v-2z" class="i"></path><path d="M701 263v2l-2 1c0 1 0 2-1 2h-1v-2c1-1 1-1 1-2v-1h3z" class="D"></path><path d="M688 264l1-1 1 3c0 2 2 2 3 4v2h-2v1h0l-1-2c0-2 0-2-2-3h0v-4z" class="R"></path><path d="M700 249c0 2 1 3 2 4l1 1c-1 0-2 1-2 2-1 1-1 1 0 2v1l2-2c1 1 1 0 2 1l1 2-1 1-1-1c-1 0-2 0-3 1v2h-3c1-3 0-5 1-7 0-2 0-5 1-7z" class="O"></path><path d="M705 258l1 2-1 1-1-1c-1 0-2 0-3 1 0-1 1-2 1-2 1-1 2-1 3-1z" class="H"></path><path d="M701 273c-1-1-1-4 0-6 1-1 1-3 2-4h1 0c1 1 2 1 2 2l2 1c0 1-1 2-2 3l4-1c0 1 1 1 1 2l-2 1c1 1 2 1 3 3h0l-1 1-2 3h0l-1 3-2 2v-2c-1 1-2 2-3 2l-1-4-1-6z" class="G"></path><path d="M702 272h1l2 1-1 1h-1l-1-2z" class="u"></path><path d="M701 273l1-1 1 2h1l1 2 1-2 1 1c0 1-1 2-1 3l-1-2c-2 0-2 0-3 1v2l-1-6z" class="X"></path><path d="M702 279v-2c1-1 1-1 3-1l1 2v1 2c-1 1-2 2-3 2l-1-4z" class="u"></path><path d="M704 263h0c1 1 2 1 2 2l2 1c0 1-1 2-2 3l-4 3c1-3 2-6 2-8v-1z" class="H"></path><path d="M708 274l1-3c1 1 2 1 3 3h0l-1 1-2 3h0l-1 3-2 2v-2-2-1c0-1 1-2 1-3l1-1z" class="M"></path><path d="M708 274l1-3c1 1 2 1 3 3h0l-1 1c-1 0-2-1-3-1z" class="C"></path><path d="M706 279c1-1 1-2 3-2v1l-1 3-2 2v-2-2z" class="i"></path><path d="M692 239c0 1 0 1 1 2 1 0 1 0 1 1 1-1 2-1 2-2l2 1h1l1 1c-1 2-1 3-1 5l-1 3-1 1v5c-1 2 0 4-1 6v1c-1 1 0 2-1 3h-1-1v-2-4h-2l-1-1v-5c2-1 2-2 2-4-1 1-2 1-3 3l-1-1 1-1v-1-2c1-1 1-3 1-5l1-3 1-1z" class="D"></path><path d="M692 239c0 1 0 1 1 2 1 0 1 0 1 1 1-1 2-1 2-2l2 1h1l1 1c-1 2-1 3-1 5l-1 3c-1-1-1-2-1-3v-1c-1 1-2 0-2 0l-1-1c-1 1-2 1-2 3v2c-1 1-2 1-3 3l-1-1 1-1v-1-2c1-1 1-3 1-5l1-3 1-1z" class="O"></path><path d="M692 239c0 1 0 1 1 2 1 0 1 0 1 1v1c-1 0-2 0-2-1l-1 1v1c2 1 1 1 3 0v1c-1 1-2 1-2 3v2c-1 1-2 1-3 3l-1-1 1-1v-1-2c1-1 1-3 1-5l1-3 1-1z" class="F"></path><path d="M677 238l3 3c2 3 5 7 8 9h1v1l-1 1 1 1c1-2 2-2 3-3 0 2 0 3-2 4v5l-1 1c1 2 2 0 2 3-1 1-1 1-1 3l-1-3-1 1c-1-1-1-2-2-3h-1c-1-1-2-1-3-2 0-1-1-1-2-1-1 1 0 2-1 4 0-1-1-1-1-1 0-1-1-3-1-3 0-2-1-3-2-4l-1-3c-1-2-2-4-4-6-1-2-1-3 0-5l2 4 2-1 2 2h0s1 0 1-1v-6z" class="M"></path><path d="M670 240l2 4 10 10h-4-1c0-1 1-3-1-5l-2 2c-1-2-2-4-4-6-1-2-1-3 0-5z" class="X"></path><path d="M674 251l2-2c2 2 1 4 1 5h1 4c3 3 6 4 7 8v1l-1 1c-1-1-1-2-2-3h-1c-1-1-2-1-3-2 0-1-1-1-2-1-1 1 0 2-1 4 0-1-1-1-1-1 0-1-1-3-1-3 0-2-1-3-2-4l-1-3z" class="J"></path><path d="M677 238l3 3c2 3 5 7 8 9h1v1l-1 1 1 1c1-2 2-2 3-3 0 2 0 3-2 4-1 0-2 0-3-1h0-1c-3-1-6-4-8-6h-2v-2s1 0 1-1v-6z" class="B"></path><path d="M680 241c2 3 5 7 8 9h1v1h-4 0l-2-2c-2-2-4-4-3-8z" class="M"></path><path d="M672 254h0 3c1 1 2 2 2 4 0 0 1 2 1 3 0 0 1 0 1 1 1-2 0-3 1-4 1 0 2 0 2 1 1 1 2 1 3 2h1c1 1 1 2 2 3v4h0c2 1 2 1 2 3l1 2h0v-1h2l2 1h1v1l1 3c0 1 0 1 1 2 0 1 1 3 1 4s1 2 1 3c-1 0-1 0-1-1l-1-1c-1 1-1 0-1 1l1 1c0 1 1 2 1 3 0 0 1 1 1 2h0c1 2 1 3 3 4h-3l2 4-1 1v1c-5-5-12-11-15-17l-3-3h0c0-1-1-3-2-4-1-2-4-5-5-8-1-1-1-3-2-4-1-4-4-7-6-11l4 1v-1z" class="G"></path><path d="M688 268c2 1 2 1 2 3l1 2h0c0 2 1 5 1 7 0 1-1 1-1 3l2 2-1 1h-1v-2h-1c-1-1-2-2-2-3s-1-1-1-1c0-1 0-2 1-2h2c0-1 0-3-1-4l-1-6z" class="J"></path><path d="M686 261c1 1 1 2 2 3v4h0l1 6c1 1 1 3 1 4h-2l-3-5-1-1-2-3c1-2 1-2 1-3 0-2 2-4 3-5z" class="C"></path><path d="M691 273v-1h2l2 1h1v1l1 3c0 1 0 1 1 2 0 1 1 3 1 4s1 2 1 3c-1 0-1 0-1-1l-1-1c-1 1-1 0-1 1l1 1c0 1 1 2 1 3 0 0 1 1 1 2h0c1 2 1 3 3 4h-3c-2-4-6-9-7-14l-1-1c0-2-1-5-1-7z" class="f"></path><path d="M691 273v-1h2l2 1h1c-1 1-1 1-1 2l-1-1-1 2s0 1 1 2v2c0 1 0 0-1 1l-1-1c0-2-1-5-1-7z" class="U"></path><path d="M672 254h0 3c1 1 2 2 2 4 0 0 1 2 1 3 0 0 1 0 1 1 1-2 0-3 1-4 1 0 2 0 2 1 1 1 2 1 3 2h1c-1 1-3 3-3 5 0 1 0 1-1 3l2 3 1 1-2 3c2 3 4 7 7 10l11 14v1c-5-5-12-11-15-17l-3-3h0c0-1-1-3-2-4-1-2-4-5-5-8-1-1-1-3-2-4-1-4-4-7-6-11l4 1v-1z" class="k"></path><path d="M675 258c1 3 1 5 2 7-1-1-2-3-3-3l-1-1v-1l2-2z" class="I"></path><path d="M684 272l1 1-2 3c-1-1-2-2-2-3l3-1z" class="K"></path><path d="M675 254c1 1 2 2 2 4 0 0 1 2 1 3 0 0 1 0 1 1l2-1v1h-1l-2 1c0 2 1 3 1 5l-1 1c0-2-1-2-1-4-1-2-1-4-2-7-1-1-2-2-3-4h3z" class="E"></path><path d="M679 262c1-2 0-3 1-4 1 0 2 0 2 1 1 1 2 1 3 2h1c-1 1-3 3-3 5 0 1 0 1-1 3l2 3-3 1-3-4 1-1c0-2-1-3-1-5l2-1h1v-1l-2 1z" class="P"></path><path d="M679 268c0-2-1-3-1-5l2-1c0 2 1 4 2 6h-3 0z" class="J"></path><path d="M708 249l2 1h5 1l3-1c2 0 3 2 5 3l2 2h0l1 2c0 1 1 2 2 3s2 3 3 5c0-1 0-1 1-2 0-1 1-1 2-2h2l1 1-1 1c-1 3-2 6-4 8l-2 3-5 5-2 1-6 6h-1c-2 2-4 4-6 5l-2 2-1-1c-1-1-1-1-1-2-1-2-1-3-1-4-1-1 0-1 0-2l2-2 1-3h0l2-3 1-1h0c-1-2-2-2-3-3l2-1c0-1-1-1-1-2l-4 1c1-1 2-2 2-3l-2-1c0-1-1-1-2-2l1-2 1-1-1-2c-1-1-1 0-2-1l-2 2v-1c-1-1-1-1 0-2 0-1 1-2 2-2l2-1 1 1 1 1 1-1 2-2-2-1v-2z" class="g"></path><path d="M701 256c1 1 2 1 3 1l1-1c2 1 4 1 6 0v1c-1 1-2 1-3 1l-1 2v1l2 1-3 3c0-1-1-1-2-2l1-2 1-1-1-2c-1-1-1 0-2-1l-2 2v-1c-1-1-1-1 0-2z" class="W"></path><path d="M720 253c1 1 3 2 3 3h1v5h1v1l-2 2v3l-1-2c-2-1-3-2-4-3l-4-1h-4c1-1 2-1 3-2-1-1-1-1-1-2h1l1-1c1 0 2 0 3 1 2-1 2-2 3-4z" class="v"></path><path d="M720 253c1 1 3 2 3 3h1v5c-2-1-2-2-4-2l-3-2c2-1 2-2 3-4z" class="O"></path><path d="M720 259c1-1 1-2 2-3h1 1v5c-2-1-2-2-4-2z" class="N"></path><path d="M713 259c2 0 4 0 5 1 1 0 1 0 2 1h2v1l1 2v3l-1-2c-2-1-3-2-4-3l-4-1h-4c1-1 2-1 3-2z" class="W"></path><path d="M718 262c2 0 3-1 4 0l1 2v3l-1-2c-2-1-3-2-4-3z" class="H"></path><path d="M710 261h4l4 1c1 1 2 2 4 3l1 2 1 2h1 1c0 1-1 2-1 2l-1 1-1 2h-2v1c-1 0-2 1-2 1v1c0 1-1 0-1 0h0c-1-1-1-2-2-2 0 0-1 0-1 1h0l-1-1c-1-1-1-1-2-1h0c-1-2-2-2-3-3l2-1c0-1-1-1-1-2l-4 1c1-1 2-2 2-3l-2-1 3-3 1-1z" class="Y"></path><path d="M722 265l1 2 1 2h1 1c0 1-1 2-1 2l-1 1-1 2h-2c1-3 1-4 0-7l1-2z" class="B"></path><path d="M714 261l4 1c1 1 2 2 4 3l-1 2s-1-1-2-1l2 2-1 1-2-2c-1-2-2-2-3-3 0-1 0-1 1-2l-2-1z" class="m"></path><path d="M710 261h4l2 1c-1 1-1 1-1 2 1 1 2 1 3 3l-3-1c0 2 0 4-1 5l-2 3c-1-2-2-2-3-3l2-1c0-1-1-1-1-2l-4 1c1-1 2-2 2-3l-2-1 3-3 1-1z" class="l"></path><path d="M711 270l1-1c1 0 1 1 2 2l-2 3c-1-2-2-2-3-3l2-1z" class="F"></path><path d="M715 264c1 1 2 1 3 3l-3-1c-1 0-3 0-5 1h-1v-1c2-2 2-2 6-2z" class="P"></path><path d="M721 274h2l1-2 1 1h1l1 1-4 4h1v1l-6 6h-1c-2 2-4 4-6 5l-2 2-1-1c-1-1-1-1-1-2-1-2-1-3-1-4-1-1 0-1 0-2l2-2 1-3h0l2-3 1-1c1 0 1 0 2 1l1 1h0c0-1 1-1 1-1 1 0 1 1 2 2h0s1 1 1 0v-1s1-1 2-1v-1z" class="I"></path><path d="M721 274h2l1-2 1 1c-1 2-3 4-4 5h-1l1-3v-1z" class="i"></path><path d="M715 276c0-1 1-1 1-1 1 0 1 1 2 2h0s1 1 1 0v-1s1-1 2-1l-1 3h1c-1 1-2 1-3 2l-1 1h-2c-1-2 0-3 0-5z" class="f"></path><path d="M712 274c1 0 1 0 2 1l1 1h0c0 2-1 3 0 5h-2 0l-1 1-1-1h-1-2l1-3h0l2-3 1-1z" class="K"></path><path d="M715 276h0c0 2-1 3 0 5h-2 0l-2-2v-1l3-2h1z" class="k"></path><path d="M708 281h2 1l1 1 1-1h0l2 1c1 1 1 2 2 3-2 2-4 4-6 5l-2 2-1-1c-1-1-1-1-1-2-1-2-1-3-1-4-1-1 0-1 0-2l2-2z" class="G"></path><path d="M715 282c1 1 1 2 2 3-2 2-4 4-6 5v-1c1-1 1-2 2-3s1-2 2-4z" class="P"></path><path d="M708 281h2 1l1 1v2h-1l-2-2-1 1c-1 2 0 3 1 5 0 1 0 2-1 3-1-1-1-1-1-2-1-2-1-3-1-4-1-1 0-1 0-2l2-2z" class="r"></path><path d="M708 249l2 1h5 1l3-1c2 0 3 2 5 3l2 2h0l1 2c0 1 1 2 2 3s2 3 3 5c0-1 0-1 1-2 0-1 1-1 2-2h2l1 1-1 1c-1 3-2 6-4 8l-2 3-5 5-2 1v-1h-1l4-4-1-1h-1l-1-1 1-1s1-1 1-2h-1-1l-1-2v-3l2-2v-1h-1v-5h-1c0-1-2-2-3-3-4-2-6-1-10-1l-2-1v-2z" class="f"></path><path d="M731 269l2-2v-1s1-1 1-2v-1c1-1 2-1 3-1-1 3-2 6-4 8l-2 3v-4z" class="w"></path><path d="M725 271h3c0-1 0-1 1-1 1-1 1-1 2-1v4l-5 5-2 1v-1h-1l4-4-1-1h-1l-1-1 1-1z" class="m"></path><path d="M708 249l2 1h5 1c5 2 9 5 12 9v2c-1-1-1-1-1-2l-3-3h-1c0-1-2-2-3-3-4-2-6-1-10-1l-2-1v-2z" class="Z"></path><path d="M724 256l3 3c0 1 0 1 1 2v1 1c-1 2-1 4-2 6h-1-1l-1-2v-3l2-2v-1h-1v-5z" class="H"></path><path d="M725 261h2v2c-1 0-1 1-1 2s0 2-1 4h-1l-1-2v-3l2-2v-1z" class="O"></path><path d="M731 273l2-3 1 2-6 7 1 1h0c3-1 4-3 7-4l2-2c0-1 1-1 2-2-1 4-4 5-6 8v1l-1 2h0l1 1v2l1 1c0 1-1 2 0 4l1 1 2 2c-1 0-2 0-3 1 0 1-1 1 0 2v1 3l-1 1 1 1h2v6l2 2 1 1h1c-1 1-2 1-3 2l-1-1c-1 1-1 2-1 3v2l2 2c2 2 4 3 6 4-1 1-1 1-1 2 0 4 0 7 2 11h-4l-2-3-6-6c-3-3-6-6-8-9l-5-7c-1 1-2 2-3 2l-1-1-2-2c-1-1-1-2-3-3-1 0-2-3-3-4l-2-3c-1 1-1 1-2 1l-2-3-2-4h3c-2-1-2-2-3-4h0c0-1-1-2-1-2 0-1-1-2-1-3l-1-1c0-1 0 0 1-1l1 1c0 1 0 1 1 1l1 1h0c1 0 2 1 3 1h1l-2-5c1 0 2-1 3-2v2c0 1-1 1 0 2 0 1 0 2 1 4 0 1 0 1 1 2l1 1 2-2c2-1 4-3 6-5h1l6-6 2-1 5-5z" class="S"></path><path d="M727 305v3h-1l-2-1 1-2 1-1 1 1zm-10-10c2 1 3 2 4 3-1 1-1 1-2 1l-3-2 1-2z" class="M"></path><path d="M714 292l-1-1 1-1h2l1 4v1h-2 0l-1-3z" class="D"></path><path d="M716 290l1-2 1 1c0 1 1 2 1 3v1h0l-2 1-1-4z" class="C"></path><path d="M729 307l1 1v2c1 1 2 1 3 2 1 0 0 0 1 1l1 1-1 1-7-6 2-2z" class="U"></path><path d="M719 293h1l3 5v2l-2-2c-1-1-2-2-4-3h0v-1l2-1z" class="c"></path><path d="M725 310l9 7-1 2-2-1h-1l-5-6v-2z" class="P"></path><path d="M718 285v2l-1 1-1 2h-2l-1 1 1 1-1 1v3 1l-1-1-1-1-2-3 2-2c2-1 4-3 6-5h1z" class="F"></path><path d="M718 302l1 1 6 7v2l5 6-2 1-2-2-1 1h0v1l-5-7c-1-1-2-2-3-4h-1l-1-3h1c1-1 1-2 2-3z" class="X"></path><path d="M718 302l1 1c-1 1-1 2-1 4 0 1 0 0-1 1h-1l-1-3h1c1-1 1-2 2-3z" class="j"></path><path d="M726 317c-1-2-3-4-3-6 1 0 1 0 2 1l5 6-2 1-2-2z" class="s"></path><path d="M723 293l3 1h0v2l2 2c1 0 2 1 3 2h1l1-1 2-1v3l-1 1 1 1h2v6l2 2 1 1h1c-1 1-2 1-3 2l-1-1c-1 0 0 0-1-1-2-1-4-2-5-4h-1l-1-1-2-2-1-1h0c-1-1-1-1-2-1l-1-3v-2l-3-5c1 0 2 1 3 0z" class="r"></path><path d="M731 308h1l2 1v-1-1l1-1c1 1 1 2 2 3l2 2 1 1h1c-1 1-2 1-3 2l-1-1c-1 0 0 0-1-1-2-1-4-2-5-4z" class="P"></path><path d="M723 293l3 1h0v2l2 2c1 0 2 1 3 2h1l1-1c0 2 0 2-1 4 1 1 1 2 1 4-2 0-2-2-3-2l-1-1v-1h-1c-2-2-4-3-5-5l-3-5c1 0 2 1 3 0z" class="V"></path><path d="M725 319v-1h0l1-1 2 2 2-1h1l2 1 1-2 2 1 2 2c2 2 4 3 6 4-1 1-1 1-1 2 0 4 0 7 2 11h-4l-2-3-6-6c-3-3-6-6-8-9z" class="j"></path><path d="M730 318h1c0 1 0 2 1 2l-1 3c-1-1-2-3-3-4l2-1z" class="X"></path><path d="M738 326h0c1 1 1 1 1 2 1 0 1 1 2 1v1c0 1 0 0-1 1h-1c-2-1-3-2-3-3 0-2 1-2 2-2z" class="s"></path><path d="M732 320c1 2 2 3 4 4l2 2c-1 0-2 0-2 2-2-2-4-3-5-5l1-3z" class="J"></path><path d="M734 317l2 1 2 2c0 1-1 2-1 3l-1-1v2c-2-1-3-2-4-4-1 0-1-1-1-2l2 1 1-2z" class="G"></path><path d="M738 320c2 2 4 3 6 4-1 1-1 1-1 2 0 4 0 7 2 11h-4l-2-3 1-2h2l-1-2v-1c-1 0-1-1-2-1 0-1 0-1-1-2h0l-2-2v-2l1 1c0-1 1-2 1-3z" class="X"></path><path d="M736 324v-2l1 1h0c1 1 2 1 3 1l1-1v1 2s1 1 0 2v1c-1 0-1-1-2-1 0-1 0-1-1-2h0l-2-2z" class="Q"></path><path d="M703 283c1 0 2-1 3-2v2c0 1-1 1 0 2 0 1 0 2 1 4 0 1 0 1 1 2l1 1 2 3 1 1 1 1h1l2 3c1 0 1 1 2 2-1 1-1 2-2 3h-1l1 3h1c1 2 2 3 3 4-1 1-2 2-3 2l-1-1-2-2c-1-1-1-2-3-3-1 0-2-3-3-4l-2-3c-1 1-1 1-2 1l-2-3-2-4h3c-2-1-2-2-3-4h0c0-1-1-2-1-2 0-1-1-2-1-3l-1-1c0-1 0 0 1-1l1 1c0 1 0 1 1 1l1 1h0c1 0 2 1 3 1h1l-2-5z" class="M"></path><path d="M716 300c1 0 1 1 2 2-1 1-1 2-2 3h-1l-3-4c1 0 3 0 4-1z" class="G"></path><path d="M716 308h1c1 2 2 3 3 4-1 1-2 2-3 2l-1-1c0-2-1-3-1-4s0-1 1-1h0z" class="R"></path><path d="M700 291c2 0 2 1 3 2s2 3 4 4h0l2 4c0 1 1 1 2 2v2c0 1 2 1 1 2l-1-1c-1-1-2-2-3-2l-2-3c-1 1-1 1-2 1l-2-3-2-4h3c-2-1-2-2-3-4z" class="C"></path><path d="M700 295h3l3 6c-1 1-1 1-2 1l-2-3-2-4z" class="R"></path><path d="M703 283c1 0 2-1 3-2v2c0 1-1 1 0 2 0 1 0 2 1 4 0 1 0 1 1 2l1 1 2 3 1 1 1 1h1l2 3c-1 1-3 1-4 1l-1-2-1-1v1c-2-1-2-2-3-3v1h0c-2-1-3-3-4-4s-1-2-3-2h0c0-1-1-2-1-2 0-1-1-2-1-3l-1-1c0-1 0 0 1-1l1 1c0 1 0 1 1 1l1 1h0c1 0 2 1 3 1h1l-2-5z" class="i"></path><path d="M703 283c1 0 2-1 3-2v2c0 1-1 1 0 2 0 1 0 2 1 4 0 1 0 1 1 2l1 1 2 3 1 1 1 1h1l2 3c-1 1-3 1-4 1l-1-2-1-1-5-10-2-5z" class="X"></path><path d="M714 297l2 3c-1 1-3 1-4 1l-1-2 1-2h2z" class="J"></path><path d="M731 273l2-3 1 2-6 7 1 1h0c3-1 4-3 7-4l2-2c0-1 1-1 2-2-1 4-4 5-6 8v1l-1 2h0l1 1v2l1 1c0 1-1 2 0 4l1 1 2 2c-1 0-2 0-3 1 0 1-1 1 0 2v1l-2 1-1 1h-1c-1-1-2-2-3-2l-2-2v-2h0l-3-1c-1 1-2 0-3 0h-1 0v-1c0-1-1-2-1-3l-1-1 1-1v-2l6-6 2-1 5-5z" class="D"></path><path d="M721 285c1-1 1-2 3-3 0 1-1 1 0 2 0 1-1 1-2 2l-1-1z" class="B"></path><path d="M718 289l2-1h2 3l-6 5v-1c0-1-1-2-1-3z" class="q"></path><path d="M726 278l2 1c-2 2-2 2-4 3h0c-2 1-2 2-3 3s-2 2-3 2v-2l6-6 2-1z" class="C"></path><path d="M734 280v1l-1 2h0l1 1v2c0 1-1 1-2 1h0c-2 1-3 1-4 1h0c-2 0-2 0-2 2l1 1h-1c-1 0-1 0-2 1l-1 1c-1 1-2 0-3 0h-1 0l6-5 9-8z" class="E"></path><path d="M728 288c2-2 3-4 5-5l1 1v2c0 1-1 1-2 1h0c-2 1-3 1-4 1z" class="m"></path><path d="M734 286l1 1c0 1-1 2 0 4l1 1 2 2c-1 0-2 0-3 1 0 1-1 1 0 2v1l-2 1-1 1h-1c-1-1-2-2-3-2l-2-2v-2h0l-3-1 1-1c1-1 1-1 2-1h1l-1-1c0-2 0-2 2-2h0c1 0 2 0 4-1h0c1 0 2 0 2-1z" class="f"></path><path d="M723 293l1-1c1-1 1-1 2-1h1l-1-1c0-2 0-2 2-2v3h0c1 1 1 1 1 2v1h-2v1l1 1v2l-2-2v-2h0l-3-1z" class="S"></path><path d="M734 286l1 1c0 1-1 2 0 4l1 1 2 2c-1 0-2 0-3 1 0 1-1 1 0 2v1l-2 1-1 1c0-2 0-3 1-4v-1c0-1-1-1-1-2h0v-6c1 0 2 0 2-1z" class="R"></path><path d="M754 256v2c0 1-1 1 0 2 0 0 1 0 2-1 2 3 3 4 3 8 0 1 0 1-1 2h0l2 2-2 2 2 1v-1l1 1h-1l-1 1-1 1 1 2c-1 1-1 2-2 2v1c2-1 3-1 4-1l1 2c1 1 1 2 2 3h0v1c-1 2-1 2-1 4l1 1c-1 2-1 4-3 6h0c-1 0-2 1-3 0l1 2c0 1 0 1 1 3l-1 2h-1c-1 1-1 1-2 1v1l-5 5v1 1s1 1 1 2v1 1l-2 2v1 1h-1c-2 1-3 2-4 3h-1c-2-1-4-2-6-4l-2-2v-2c0-1 0-2 1-3l1 1c1-1 2-1 3-2h-1l-1-1-2-2v-6h-2l-1-1 1-1v-3-1c-1-1 0-1 0-2 1-1 2-1 3-1l-2-2-1-1c-1-2 0-3 0-4l-1-1v-2l-1-1h0l1-2v-1c2-3 5-4 6-8 1 0 2-2 3-3l2-3c1-2 3-3 5-5l4-5z" class="K"></path><path d="M740 312c0-2 1-3 1-4l1-1 2 5-2-1-1 1h-1z" class="X"></path><path d="M757 268l1 1h0l-3 3-4 3-1-4h1c1-1 2-1 3-1 2-1 2-1 3-2z" class="j"></path><path d="M755 272l3-3 2 2-2 2-1 2v1c-1 1-1 1-1 2-1 1-3 4-3 5v1 1c-1 0-1 0-1-1-1 1-2 1-2 2v1c0 1-1 1-1 2l-1 2-1 1c-1 0-2 1-3 2l1-4c1-2 2-4 3-5l1-1 2-4v-1h1c2-2 3-5 3-7z" class="V"></path><path d="M741 286c0-1 1-1 2-3 1-1 2-1 3-1-1 1-1 3-1 4 0 2-1 4-2 6-1 3-3 7-3 10 0 2 1 1 0 2-1 2 1 6-1 7l-2-2v-6h-2l-1-1 1-1v-3-1c-1-1 0-1 0-2 1-1 2-1 3-1l-2-2s0-1 1-1c0-2 0-4-1-5v-1l1-1 2 2h2z" class="C"></path><path d="M738 294h1c-1 3-1 6-2 9h-2l-1-1 1-1v-3-1c-1-1 0-1 0-2 1-1 2-1 3-1z" class="Y"></path><path d="M735 295h2c0 1 0 1-1 2h-1c-1-1 0-1 0-2z" class="x"></path><path d="M736 292s0-1 1-1c0-2 0-4-1-5v-1l1-1 2 2h2l1 1c-2 2-3 4-3 7h-1l-2-2z" class="b"></path><path d="M758 273l2 1v-1l1 1h-1l-1 1-1 1 1 2c-1 1-1 2-2 2v1c0 1-1 2-2 3l1 1h-2c-1 2-1 4-2 5l-1-1c-1 1-1 2-2 3 0 1 0 3 1 4-1 1-1 2-1 3 1 1 1 2 1 3v1c-1 0-2 1-3 2l1 1-2 2h-1c-1-1-2-2-3-2v-1-1-1c0-2 0-3 1-5 0-2 0-3 1-4s2-2 3-2l1-1 1-2c0-1 1-1 1-2v-1c0-1 1-1 2-2 0 1 0 1 1 1v-1-1c0-1 2-4 3-5 0-1 0-1 1-2v-1l1-2z" class="F"></path><path d="M746 295l1 3c0 1 1 1 1 2h0c-2-1-2-1-4 0 0-1 0-2-1-2h1c1-1 2-2 2-3z" class="f"></path><g class="S"><path d="M742 304v-1c0-2 0-3 1-5 1 0 1 1 1 2v3l-2 1z"></path><path d="M744 294c1-1 2-2 3-2 0 1 0 2-1 3 0 1-1 2-2 3h-1c0-2 0-3 1-4z"></path></g><path d="M754 256v2c0 1-1 1 0 2 0 0 1 0 2-1 2 3 3 4 3 8 0 1 0 1-1 2l-1-1c-1 1-1 1-3 2-1 0-2 0-3 1h-1l1 4-5 7c-1 0-2 0-3 1-1 2-2 2-2 3h-2l-2-2-1 1v1c1 1 1 3 1 5-1 0-1 1-1 1l-1-1c-1-2 0-3 0-4l-1-1v-2l-1-1h0l1-2v-1c2-3 5-4 6-8 1 0 2-2 3-3l2-3c1-2 3-3 5-5l4-5z" class="W"></path><path d="M750 264c0 1-1 1 0 2l1-1h1v2l2 3c-1 0-2 0-3 1h-1 0l-3-5c1 0 2-1 3-2z" class="x"></path><path d="M745 266c1-2 3-3 5-5v3c-1 1-2 2-3 2h-1c-2 2-4 5-6 8-1 2-3 6-5 7 0 1 1 2 2 3l-1 1v1c1 1 1 3 1 5-1 0-1 1-1 1l-1-1c-1-2 0-3 0-4l-1-1v-2l-1-1h0l1-2v-1c2-3 5-4 6-8 1 0 2-2 3-3l2-3z" class="Z"></path><path d="M754 256v2c0 1-1 1 0 2 0 0 1 0 2-1 2 3 3 4 3 8 0 1 0 1-1 2l-1-1c-1 1-1 1-3 2l-2-3v-2h-1l-1 1c-1-1 0-1 0-2v-3l4-5z" class="p"></path><path d="M752 265c1-1 2-2 3-2 2 0 1 0 3 1 0 1 0 2-1 4-1 1-1 1-3 2l-2-3v-2z" class="W"></path><path d="M757 281c2-1 3-1 4-1l1 2c1 1 1 2 2 3h0v1c-1 2-1 2-1 4l1 1c-1 2-1 4-3 6h0c-1 0-2 1-3 0l1 2c0 1 0 1 1 3l-1 2h-1c-1 1-1 1-2 1v1l-5 5v1 1s1 1 1 2v1 1l-2 2v1 1h-1c-2 1-3 2-4 3h-1c-2-1-4-2-6-4l-2-2v-2c0-1 0-2 1-3l1 1c1-1 2-1 3-2l1-1 2 1-2-5v-1c1 0 2 1 3 2h1l2-2-1-1c1-1 2-2 3-2v-1c0-1 0-2-1-3 0-1 0-2 1-3-1-1-1-3-1-4 1-1 1-2 2-3l1 1c1-1 1-3 2-5h2l-1-1c1-1 2-2 2-3z" class="k"></path><path d="M750 311s0 1 1 1v1s1 1 1 2v1 1l-2 2c-1 0-2 0-3-1v-1l2-4c0-1 0-1 1-2z" class="T"></path><path d="M754 290c1 2 3 3 4 6-2 1-2 2-4 2-2-1-2-2-3-4 0-2 0-2 1-3l2-1z" class="o"></path><path d="M755 299l3-2 1 2c0 1 0 1 1 3l-1 2h-1c-1 1-1 1-2 1v1l-5 5v1c-1 0-1-1-1-1 1-2 2-4 3-7 1-2 1-4 2-5z" class="B"></path><path d="M755 299l3-2 1 2c0 1 0 1 1 3l-1 2-1-1s-1 0-2-1c0 0 0-2-1-3z" class="W"></path><path d="M750 296v1c0 1 0 1 1 1 0 2 1 3 1 5h-1c-1 1 0 1-1 1 0 1 1 1 1 2l-4 4 1 2c-1 0-1 1-2 1h0c-1 2-1 2-2 3-1-1 0-2 0-4l-2-5v-1c1 0 2 1 3 2h1l2-2-1-1c1-1 2-2 3-2v-1c0-1 0-2-1-3 0-1 0-2 1-3z" class="V"></path><path d="M757 281c2-1 3-1 4-1l1 2c1 1 1 2 2 3h0v1l-2 1v1c0 1-1 2-2 3h-1l-2-2c-1 0-2 0-3 1l-2 1v-1c1-1 1-3 2-5h2l-1-1c1-1 2-2 2-3z" class="H"></path><path d="M741 312l1-1 2 1c0 2-1 3 0 4v2l5 3c-2 1-3 2-4 3h-1c-2-1-4-2-6-4l-2-2v-2c0-1 0-2 1-3l1 1c1-1 2-1 3-2z" class="U"></path><path d="M741 312l1-1 2 1c0 2-1 3 0 4v2c-1-2-4-3-6-4 1-1 2-1 3-2z" class="J"></path><path d="M745 221c2 3 5 6 9 7h0v-1l2-1h3 4l2 1c-1 0-2 0-2 1s1 2 1 4h0v1h2 1l1 2h0l-1 3 2-2 2 2h0c0 1-1 2 0 3l-2 5c-1 0-2-1-3 0-1 0-5 5-5 6l-1 2-1 1-1 2c-1 1-2 1-2 2-1 1-2 1-2 1-1-1 0-1 0-2v-2l-4 5c-2 2-4 3-5 5l-2 3c-1 1-2 3-3 3-1 1-2 1-2 2l-2 2c-3 1-4 3-7 4h0l-1-1 6-7-1-2c2-2 3-5 4-8l1-1-1-1h-2c-1 1-2 1-2 2-1 1-1 1-1 2-1-2-2-4-3-5s-2-2-2-3l-1-2h0l-2-2c-2-1-3-3-5-3l-1-1c0-1 0-2-1-2l-1-1 1-2-1-1 1-2h1c0-1 1-1 2-1 1-1 2-1 2-3l2-2c1 1 2 1 3 1v-1c2-1 2-3 4-4l6-1-2-2v-2h1s1 1 2 1h0l2 1v-1-1l-1-1c1-1 2-3 3-4l1 2h1l1-1z" class="g"></path><path d="M729 241c1 0 1 1 2 1h3v1l-1 1h-1c-1 0-1 0-2 1l-1-1v-3z" class="O"></path><path d="M724 234c1 1 2 1 3 1l1 1h0-4v2c-1 1-2 1-4 1 1-1 2-1 2-3l2-2z" class="W"></path><path d="M717 240h1c0 2 3 3 3 5 1 2-1 4 2 5h1l1 2h1v-1-1l2 3-2 1-2-2c-2-1-3-3-5-3l-1-1c0-1 0-2-1-2l-1-1 1-2-1-1 1-2zm20-4l2-2h1c2 0 4 0 6 1h2l1 3 2 3h0 1v-1-1c0 1 1 2 0 2 0 1-1 2-2 2l-3 1-1 1h-1c-1 1-1 2-2 3h-1c-1-1-1-1-2 0h-6 0c-1 1-1 2-1 4 0 1 1 2 2 4h-3l-1-1h1c-1-1-1-1-1-2-1-2 0-2 1-3 0-2 0-2 1-4h2c1-1 0-2 1-3h1c-1 1-1 1-1 2l1 1v-2l1 1c1 1 1 1 2 1h0c1-2 3-2 5-3v-2-4c-2-1-2-2-4-2-1 1-1 2-1 3-1 0-1 1-2 0v-1l-1-1z" class="a"></path><path d="M751 241c-2 0-2 0-4-1l2-2 2 3z" class="H"></path><path d="M735 225h1s1 1 2 1h0l2 1h0c1 1 2 1 3 2 1 0 1 1 1 2h-1c-1 0-1 1-2 1h5v3c-2-1-4-1-6-1h-1l-2 2h-3v1c0 1-1 1-2 1s-1-1-2-1l-2-1h0l-1-1v-1c2-1 2-3 4-4l6-1-2-2v-2z" class="L"></path><path d="M735 225h1s1 1 2 1l1 1-2 2-2-2v-2z" class="j"></path><path d="M741 232h5v3c-2-1-4-1-6-1h-1l-2 2h-3v1c0 1-1 1-2 1s-1-1-2-1l-2-1h0c1-1 2-1 3-2 0 1 0 1 1 1l2-1s0-1 1-1l1 1c1-1 2-1 3-1s1 0 2-1z" class="D"></path><path d="M745 221c2 3 5 6 9 7h0v-1l2-1h3 4l2 1c-1 0-2 0-2 1s1 2 1 4h0v1c-2 2-5 6-7 8l-1-1h-1c-1 1-1 3-2 3s-1 1-2 1h-1v-1c1 0 2-1 2-2 1 0 0-1 0-2v1 1h-1 0l-2-3-1-3h-2v-3h-5c1 0 1-1 2-1h1c0-1 0-2-1-2-1-1-2-1-3-2h0v-1-1l-1-1c1-1 2-3 3-4l1 2h1l1-1z" class="B"></path><path d="M748 230c0-1-1-2 0-3h1c0 1 0 2 1 3h-2z" class="D"></path><path d="M748 230h2l1 1h1 2l1 1v5l-3 2v1 1h-1 0l-2-3-1-3 2-1c-1-2-1-1-3-2l1-2z" class="N"></path><path d="M759 226h4l2 1c-1 0-2 0-2 1s1 2 1 4h0v1c-2 2-5 6-7 8l-1-1h0c2-2 3-3 4-5 0-1-1-1-1-2h-2-1c2-1 2-1 2-3-1 1-1 1-3 0v-2h-1v-1l2-1h3z" class="U"></path><path d="M759 226h4l2 1c-1 0-2 0-2 1h-2 0c-2 1-4 0-6 0h-1v-1l2-1h3z" class="P"></path><path d="M764 233h2 1l1 2h0l-1 3 2-2 2 2h0c0 1-1 2 0 3l-2 5c-1 0-2-1-3 0-1 0-5 5-5 6l-1 2-1 1-1 2c-1 1-2 1-2 2-1 1-2 1-2 1-1-1 0-1 0-2v-2l-4 5c-2 2-4 3-5 5l-2 3c-1 1-2 3-3 3-1 1-2 1-2 2l-2 2c-3 1-4 3-7 4h0l-1-1 6-7-1-2c2-2 3-5 4-8l1-1-1-1h-2c-1 1-2 1-2 2-1 1-1 1-1 2-1-2-2-4-3-5s-2-2-2-3l-1-2h0l2-1c1 1 2 2 3 2l1 1h3c-1-2-2-3-2-4 0-2 0-3 1-4h0 6c1-1 1-1 2 0h1c1-1 1-2 2-3h1l1-1 3-1v1h1c1 0 1-1 2-1s1-2 2-3h1l1 1c2-2 5-6 7-8z" class="v"></path><path d="M747 254h2v2h-2v-2zm-7 2h1c0 1 1 2 1 2l-1 2-2-2 1-2z" class="o"></path><path d="M740 253h1l-1 1h1v1l-2 1-1-1c0-1 1-2 2-2z" class="N"></path><path d="M745 263v-2c0-2 1-2 3-3v1l-3 4z" class="o"></path><path d="M736 276l1-2c0-2 1-2 2-4v-1h4c-1 1-2 3-3 3-1 1-2 1-2 2l-2 2zm28-43h2l-6 7h-1l-2 1h0c2-2 5-6 7-8zm-26 28c1 1 1 2 1 3h0c-1 1-1 2-1 3 0 2-3 3-4 5l-1-2c2-2 3-5 4-8l1-1h0z" class="g"></path><path d="M759 248c0-1 1-2 1-4 1-2 3-3 5-5 1-2 1-3 3-4l-1 3-8 10zm-11 11c1 0 1-1 2-2v-1h1c0-1 0-2 1-2l1-1v-1l5-2-4 6-4 5c-2 2-4 3-5 5l-1-1c0-1 1-1 1-2l3-4z" class="e"></path><path d="M769 236l2 2h0c0 1-1 2 0 3l-2 5c-1 0-2-1-3 0-1 0-5 5-5 6l-1 2-1 1-1 2c-1 1-2 1-2 2-1 1-2 1-2 1-1-1 0-1 0-2v-2l4-6 1-2 8-10 2-2z" class="G"></path><path d="M750 243v1h1c1 0 1-1 2-1s1-2 2-3h1l1 1h0c-1 1-4 5-4 5-2 3-4 6-6 8-2 0-4-1-6-1h-1l-4-1h0c1 3 2 6 2 9h0l-1-1h-2c-1 1-2 1-2 2-1 1-1 1-1 2-1-2-2-4-3-5s-2-2-2-3l-1-2h0l2-1c1 1 2 2 3 2l1 1h3c-1-2-2-3-2-4 0-2 0-3 1-4h0 6c1-1 1-1 2 0h1c1-1 1-2 2-3h1l1-1 3-1z" class="i"></path><path d="M750 243v1h1c1 0 1-1 2-1s1-2 2-3h1l1 1h0c-1 1-4 5-4 5h-3c0-1 1-1-1-1h0l-2-1c-1 2 0 3-1 4 0 1-2 3-2 3h-3-1v-1c1-1 1 0 2-1 1 0 1-1 2-1 2-1 2-1 2-3l1-1 3-1z" class="T"></path><path d="M731 255l1 1h3c-1-2-2-3-2-4 0-2 0-3 1-4h0 6c1-1 1-1 2 0h1c1-1 1-2 2-3h1c0 2 0 2-2 3-1 0-1 1-2 1-1 1-1 0-2 1v1c-2-1-4-1-5-1-1 2-1 2-1 4l1 1c0 2 0 3-1 4h-3v-1c-2-1-3-3-5-4h0l2-1c1 1 2 2 3 2z" class="B"></path><path d="M775 228l1-3c1-1 1 0 1-1 0-2 4-7 5-9l1 7c1 4-1 9-1 13l-1 10c0 11 1 27 11 36h0c3 5 8 8 13 10h8c9-2 13-6 18-14l1 2h1l1 1v1h-1c-3 3-5 6-9 9l-4 4h-2c-2 2-4 3-7 3h-12l-2 2-3 1h-2l-1 2v1l-8-1c-1-1-4-1-5-1-2 1-4 0-7 1l-3 1h-1v-1l-2-2-1-1-4 3c-1-2-1-2-1-3l-1-2c1 1 2 0 3 0h0c2-2 2-4 3-6l-1-1c0-2 0-2 1-4v-1h0c-1-1-1-2-2-3l-1-2c-1 0-2 0-4 1v-1c1 0 1-1 2-2l-1-2 1-1 1-1h1l-1-1v1l-2-1 2-2-2-2h0c1-1 1-1 1-2 0-4-1-5-3-8 0-1 1-1 2-2l1-2 1-1 1-2c0-1 4-6 5-6 1-1 2 0 3 0l2-5c-1-1 0-2 0-3h0l-2-2-2 2 1-3h0l-1-2h-1-2v-1h0c0-2-1-3-1-4s1-1 2-1l1 1v-1h4 0c1-2 3-3 5-4 0 1 0 2-1 3l1 1v1z" class="F"></path><path d="M778 267c2 4 5 9 8 13h-1c-2-2-3-4-5-7l-1-1h-1v-3l-1-1 1-1z" class="h"></path><path d="M776 263c0 1 1 2 2 4l-1 1 1 1v3h1l1 1-1 1v3c-1-1-1-2-2-3l-2 1v-5l1-1c-1-2-1-4 0-6z" class="Q"></path><path d="M786 280c4 3 6 5 10 8 1 0 1 0 2 1h-2c-2-1-4-3-6-4-1 0-2 1-3 1-1-1-3-2-3-4h1v-2h1z" class="V"></path><path d="M774 275v-3c0-1 0-1 1-2v5l2-1c1 1 1 2 2 3-1 2-2 4-2 6 0 1 0 1-1 2l-1-1c-1 1-1 1-2 1v-2l1-8z" class="K"></path><path d="M775 275l2-1c1 1 1 2 2 3-1 2-2 4-2 6l-2-1-1-1c1-2 1-3 2-4l-1-2z" class="d"></path><path d="M831 277l1 2h1l1 1v1h-1c-3 3-5 6-9 9l-4 4h-2c-2 2-4 3-7 3h-12l-2 2h-4v-3c-2-1-3-1-3-2-2-2-4-2-5-5l1-1-1-1 2-1c1 0 2-1 3-1 2 1 4 3 6 4h2l1 1h-1c1 1 3 2 6 2h4c2-1 3 0 5-1 9-2 13-6 18-14z" class="B"></path><path d="M793 296l6 1-2 2h-4v-3z" class="b"></path><path d="M811 297c0-1 0-2 1-3h6c-2 2-4 3-7 3z" class="T"></path><path d="M787 286c1 0 2-1 3-1 2 1 4 3 6 4h2l1 1h-1-2v2c-4 0-6-3-10-4h0l-1-1 2-1z" class="U"></path><path d="M780 273c2 3 3 5 5 7v2h-1c0 2 2 3 3 4l-2 1 1 1-1 1c1 3 3 3 5 5 0 1 1 1 3 2v3h4l-3 1h-2l-1 2v1l-8-1c-1-1-4-1-5-1-2 1-4 0-7 1l-3 1h-1v-1l-2-2-1-1-4 3c-1-2-1-2-1-3l-1-2c1 1 2 0 3 0h0c2-2 2-4 3-6s1-4 2-5h1l2-1c-1-1-1-1-1-2s1-2 1-3h0l1-1 1 1-1 2h0c1 1 1 1 2 1h1 0v2c1 0 1 0 2-1l1 1c1-1 1-1 1-2 0-2 1-4 2-6v-3l1-1z" class="f"></path><path d="M779 298v-3h3l-3 3z" class="t"></path><path d="M782 295c2 1 2 1 4 3-2 0-4 0-6 1h-1v-1l3-3z" class="m"></path><path d="M785 287h0l1 1-1 1c1 3 3 3 5 5h-2c-3-2-6-5-9-7l3 1c1 0 1 0 3-1z" class="C"></path><path d="M788 294h2c0 1 1 1 3 2v3c-2 0-4-1-6-1l1-2-1-2h1z" class="Y"></path><path d="M779 287c-1 0-1-1-1-2 0-2 0-3 1-4h1c1 2 2 3 4 5l1 1c-2 1-2 1-3 1l-3-1z" class="B"></path><path d="M780 299c2-1 4-1 6-1h1c2 0 4 1 6 1h4l-3 1h-2l-1 2v1l-8-1c-1-1-4-1-5-1s-1-1-2-2h3 1z" class="E"></path><path d="M773 283v2c1 0 1 0 2-1l1 1v1 2l-1 1h0c0 2 0 2-1 4v1l-1 1v1c0 1-1 2-1 3 1 0 2-1 3-1h1l3 1h1-1-3c1 1 1 2 2 2-2 1-4 0-7 1l-3 1h-1v-1c4-4 5-13 6-19h0z" class="b"></path><path d="M769 285c-1-1-1-1-1-2s1-2 1-3h0l1-1 1 1-1 2h0c1 1 1 1 2 1h1c-1 6-2 15-6 19l-2-2-1-1-4 3c-1-2-1-2-1-3l-1-2c1 1 2 0 3 0h0c2-2 2-4 3-6s1-4 2-5h1l2-1z" class="B"></path><path d="M768 289l1 1c0 1 0 1 1 2v1l-2 2v-6z" class="H"></path><path d="M769 285l1 1c-1 1-1 2-2 3h0v6h-1v1l-2-2h0l2-5v-3l2-1z" class="W"></path><path d="M766 286h1v3l-2 5h0c0 1-1 3-2 3h-2c2-2 2-4 3-6s1-4 2-5z" class="B"></path><path d="M765 294l2 2c0 2-1 2-1 3l-1 1-1-1-4 3c-1-2-1-2-1-3l-1-2c1 1 2 0 3 0h0 2c1 0 2-2 2-3z" class="D"></path><path d="M765 294l2 2c0 2-1 2-1 3l-2-1c-1 1-1 0-2 1-1-1-1-1-1-2h0 2c1 0 2-2 2-3z" class="g"></path><path d="M775 228l1-3c1-1 1 0 1-1 0-2 4-7 5-9l1 7c1 4-1 9-1 13l-1 10c0 1 0 3-1 4 0 2-3 3-3 5 0 1 1 1 1 2 1 3 3 6 2 9v-1h-1c-1-1-2-1-3-2v1c-1 2-1 4 0 6l-1 1c-1 1-1 1-1 2v3l-1 8h0-1c-1 0-1 0-2-1h0l1-2-1-1-1 1h0c0 1-1 2-1 3s0 1 1 2l-2 1h-1c-1 1-1 3-2 5l-1-1c0-2 0-2 1-4v-1h0c-1-1-1-2-2-3l-1-2c-1 0-2 0-4 1v-1c1 0 1-1 2-2l-1-2 1-1 1-1h1l-1-1v1l-2-1 2-2-2-2h0c1-1 1-1 1-2 0-4-1-5-3-8 0-1 1-1 2-2l1-2 1-1 1-2c0-1 4-6 5-6 1-1 2 0 3 0l2-5c-1-1 0-2 0-3h0l-2-2-2 2 1-3h0l-1-2h-1-2v-1h0c0-2-1-3-1-4s1-1 2-1l1 1v-1h4 0c1-2 3-3 5-4 0 1 0 2-1 3l1 1v1z" class="C"></path><path d="M771 241c0 1 1 2 1 4h0c1 1 1 2 1 4v-4-3h0l2 14c0 2 0 4 1 6v1c-1 2-1 4 0 6l-1 1c-1 1-1 1-1 2v3l-2-24-1-1c0-2-1-3-2-4h0l2-5z" class="n"></path><path d="M771 241c0 1 1 2 1 4v6l-1-1c0-2-1-3-2-4h0l2-5z" class="U"></path><path d="M770 227c1-2 3-3 5-4 0 1 0 2-1 3l1 1v1c-1 4-1 9-2 14h0v3 4c0-2 0-3-1-4h0c0-2-1-3-1-4-1-1 0-2 0-3h0l-2-2-2 2 1-3h0l-1-2h-1-2v-1h0c0-2-1-3-1-4s1-1 2-1l1 1v-1h4 0z" class="p"></path><path d="M770 227c1-2 3-3 5-4 0 1 0 2-1 3l1 1-2 4c-1 2-3 4-4 5l-2 2 1-3h0l2-4 2-1v-3h-2z" class="B"></path><path d="M765 227l1 1v-1h4 0 2v3l-2 1-2 4-1-2h-1-2v-1h0c0-2-1-3-1-4s1-1 2-1z" class="N"></path><path d="M767 233c0-1 1-2 2-3h1v1l-2 4-1-2z" class="e"></path><path d="M765 227l1 1v-1h4l-6 5h0c0-2-1-3-1-4s1-1 2-1z" class="t"></path><path d="M775 228l1-3c1-1 1 0 1-1 0-2 4-7 5-9l1 7c1 4-1 9-1 13l-1 10c0 1 0 3-1 4 0 2-3 3-3 5 0 1 1 1 1 2 1 3 3 6 2 9v-1h-1c-1-1-2-1-3-2-1-2-1-4-1-6l-2-14c1-5 1-10 2-14z" class="B"></path><path d="M760 254l1-2c0-1 4-6 5-6 1-1 2 0 3 0h0c-1 7 0 13 0 20 0 4-1 8-2 12 0 3 0 5-1 8-1 1-1 3-2 5l-1-1c0-2 0-2 1-4v-1h0c-1-1-1-2-2-3l-1-2c-1 0-2 0-4 1v-1c1 0 1-1 2-2l-1-2 1-1 1-1h1l-1-1v1l-2-1 2-2-2-2h0c1-1 1-1 1-2 0-4-1-5-3-8 0-1 1-1 2-2l1-2 1-1z" class="I"></path><path d="M765 270h2l-1 8c0 2-1 4-2 7h0c-1-1-1-2-2-3l1-1v-2l2-1v-2c-1-1-2-1-2-3l1-1v-1l1-1z" class="M"></path><path d="M763 270l1 1v1l-1 1c0 2 1 2 2 3v2l-2 1v2l-1 1-1-2c-1 0-2 0-4 1v-1c1 0 1-1 2-2l-1-2 1-1 1-1h1l-1-1c1-2 1-2 2-3h1z" class="B"></path><path d="M762 270h0c0-1-1-2-1-3 1-1 1-2 1-3s0-3 1-4c2 0 3 1 4 1v9h-2l-1 1-1-1h-1z" class="i"></path><path d="M763 270v-2c1 0 1 0 2 1v1l-1 1-1-1z" class="F"></path><path d="M760 254h1c1-1 3-2 4-4h1c1 3 1 5 1 8v2 1c-1 0-2-1-4-1-1 1-1 3-1 4s0 2-1 3c0 1 1 2 1 3h0c-1 1-1 1-2 3v1l-2-1 2-2-2-2h0c1-1 1-1 1-2 0-4-1-5-3-8 0-1 1-1 2-2l1-2 1-1z" class="f"></path><path d="M767 260c-1-1-2-2-3-2s-1 1-2 1l1-3h1l2-3c0 2 1 3 1 5v2z" class="U"></path><path d="M656 187h22c-1 1-2 1-2 2h0 1v-1h0c2 1 3 2 5 2-6 0-11 1-15 6-1 1-2 2-3 4l-1 5h0v6 1l1 1v1l-2 2v2c1 1 3 3 3 4 2 2 3 4 5 6l7 10v6c0 1-1 1-1 1h0l-2-2-2 1-2-4c-1 2-1 3 0 5 2 2 3 4 4 6l1 3h-3 0v1l-4-1c2 4 5 7 6 11 1 1 1 3 2 4 1 3 4 6 5 8 1 1 2 3 2 4-1-1-2-1-3-1h1c0 1 1 3 1 5l3 3c2 2 4 5 5 7v1l-10-9c-2-1-5-2-7-4v3c3 2 6 4 8 5s4 3 5 4c0 1 0 1-1 1l-2-2c-2-1-3-2-5-3h-1c-2-3-8-7-12-8h-2c-2-1-4-1-6-2-31-16-64-28-99-27-4 1-9 1-12 2h-3v-1l2-2c-3-1-4 2-7 1-1 0-2 1-3 1h-2-1l1-1c0-1 0-2-1-2v-1-2h-1l-1-1 2-1c0-2-1-2-2-3h0l-1-2c1-1 1-1 1-2v-1-1l7-3c0-1 3-3 3-4l1-1 3-3c-1 0-1-1-1-1 0-1 0-3 1-3 0-1 0 0 1-1l-1-1v-3c-1-1-1-1 0-2v-2l2-1c1 0 1-1 2-2v-2h1c0-2 1-3 2-4-1 1-2 1-3 3v1l-3 2c-1 0-1-1-1-1-1-1-1-2-1-2l-1-1c0-2-1-4-2-6v-3h0l-1-2c1 0 2-1 3-1l2 2c0-2-1-4-2-6h1c1 0 3 0 4 1l1-1h13 5c1 0 1-1 2-1h8 2 2l1 1c2-2 3-3 6-3l43-1h26z" class="M"></path><path d="M575 224c0 1 1 2 2 3l-3 2h0c-1 1-3 2-3 4l-1 1-1 3 1 1-1 1h0c-1 0-2 0-2 1h-1c2-6 4-11 9-16z" class="P"></path><path d="M576 214h1c1-1 4-4 6-5h3c-4 3-8 8-12 10l-1 1-4 2c2-3 3-6 7-8z" class="X"></path><path d="M593 210l4-1c1 0 2 1 2 2 1 0 1 0 1 1l1 1c0 1 0 2-1 3l-1 1s0 1 1 1l-1 1-4-2h-1c-4-1-8-1-12 0h-1c2-2 9-5 12-7z" class="E"></path><path d="M600 212l1 1c0 1 0 2-1 3l-1 1s0 1 1 1l-1 1-4-2h2c-1-1-1-1-3-1l-2-1-1-1 9-2z" class="I"></path><path d="M562 239l1-2 2-6c2-3 2-4 4-6h0l-3 8c-2 1-2 4-2 6v2c1-1 1-1 2-1h1c0-1 1-1 2-1h0l1-1-1-1 1-3 1-1c0-2 2-3 3-4l3 3-1 3 1 2h-1 0c-2-1-2-1-3-1v1c0 1 1 2 2 2v1l-2 1c0 1 1 1 1 2v1h-5c-2 0-4 0-6-1-1-1-1-2-1-4z" class="L"></path><path d="M582 217c4-1 8-1 12 0h-1-2l-2 1c2 0 4 0 5 1h1c1 0 0 0 1 1l1 1h-1l-1 3-1 1-2-1h-4c1 1 1 2 2 3h0c-1 1-1 1 0 1-2 1-4 1-6 0h0l-2-1 2-3h-2 0c-2 1-3 1-4 1l-1 2c-1-1-2-2-2-3h0l-1-1c1-1 2-2 3-2 2-1 3-2 5-4z" class="d"></path><path d="M593 220l3 1-1 3-1 1-2-1c0-1 1-2 1-4z" class="I"></path><path d="M589 218c2 0 4 0 5 1h1c1 0 0 0 1 1l1 1h-1l-3-1-7-1c1 0 2-1 3-1z" class="U"></path><path d="M584 224l1 1 1-1 1 1 1-1c1 1 1 2 2 3h0c-1 1-1 1 0 1-2 1-4 1-6 0h0l-2-1 2-3z" class="m"></path><path d="M582 217c4-1 8-1 12 0h-1-2l-2 1c-1 0-2 1-3 1-4 1-7 2-10 4l-1 1-1-1c1-1 2-2 3-2 2-1 3-2 5-4z" class="F"></path><path d="M572 213l2-1 2 2c-4 2-5 5-7 8l4-2c-1 2-2 3-4 5s-2 3-4 6l-2 6-1 2c-1 2-1 3-2 4v1 1l-5 1c-1-2-1-2-1-4l-1-1c0-8 3-16 6-23l2-2 1-2c1 2 0 7 2 8h1c2-3 4-5 5-8l2-1h0z" class="y"></path><path d="M569 222l4-2c-1 2-2 3-4 5s-2 3-4 6l-2 6-1 2c-1 2-1 3-2 4v1 1l-5 1c-1-2-1-2-1-4l1 1 1-1c0-1 0-2 1-3v-1-2h1c1 1 1 1 2 1v-1c-1-1-1-1-1-2 1-1 1-2 2-2v1 2h0l1-1c2-4 4-8 7-12z" class="G"></path><path d="M554 242l1 1 1-1c0-1 0-2 1-3v-1l1 1v1c1 1 1 1 1 2l-1 2h2v1l-5 1c-1-2-1-2-1-4z" class="P"></path><path d="M577 227l1-2c1 0 2 0 4-1h0 2l-2 3 2 1h0c2 1 4 1 6 0 1 1 1 2 2 3 1 2 2 2 3 3v1 1h1 0c0 1 0 2 1 2v1c-1-1-2-1-3-1v1c0 1 0 1-1 2v2-2l-1 1c0 1 1 2 1 4h-2c-1 0-2-1-2-1h-4c-2-1-2-1-4 0-2-1-4-1-7-1v-1c0-1-1-1-1-2l2-1v-1c-1 0-2-1-2-2v-1c1 0 1 0 3 1h0 1l-1-2 1-3-3-3h0l3-2z" class="M"></path><path d="M585 233c0-2 1-2 2-4 1 1 1 2 1 3-1 1-1 1-2 1h-1zm-3-6l2 1h0c-1 1-2 3-3 4v2l-1 1-1-1-1 1s-1 1-1 2l-1-2 1-3 2 1v-1l-1-1c0-1 0-2 1-3l1 2 2-3z" class="R"></path><path d="M577 227l1-2c1 0 2 0 4-1h0 2l-2 3-2 3-1-2c-1 1-1 2-1 3l1 1v1l-2-1-3-3h0l3-2z" class="l"></path><path d="M586 233v2c2-1 3-3 4-3l1 1c0 1-1 1-1 2 0 2 2 2 0 4l-1 1v1l-1-1-3-2c0-1-1-2-2-3 1 0 1 0 2-1v-1h1z" class="C"></path><path d="M585 238l3-1v1l1 2h0v1l-1-1-3-2z" class="q"></path><path d="M575 239c-1 0-2-1-2-2v-1c1 0 1 0 3 1h0 1c1 1 1 0 2 1l-1 1 2 2c1-1 1-1 3-2 1 0 1 2 2 3h1c0-1 1-1 2-2l1 1c-1 1-1 1-1 2 2 0 2-2 3-3h1c0-1 0-2 1-3l1 1v1c0 1 0 1-1 2v2-2l-1 1c0 1 1 2 1 4h-2c-1 0-2-1-2-1h-4c-2-1-2-1-4 0-2-1-4-1-7-1v-1c0-1-1-1-1-2l2-1v-1z" class="S"></path><path d="M575 239c1 1 1 3 2 3s2 0 3-1v1c1 1 2 1 4 1h1c1 1 1 1 0 2-2-1-2-1-4 0-2-1-4-1-7-1v-1c0-1-1-1-1-2l2-1v-1z" class="V"></path><path d="M596 221h1c3 1 7 5 9 8l1 1h0l2 5 1 1v5c0 2 0 4 1 5l-3 3c-2 1-4 0-6 0h-1c-4-2-6-3-8-6v-2c1-1 1-1 1-2v-1c1 0 2 0 3 1v-1c-1 0-1-1-1-2h0-1v-1-1c-1-1-2-1-3-3-1-1-1-2-2-3-1 0-1 0 0-1h0c-1-1-1-2-2-3h4l2 1 1-1 1-3z" class="B"></path><path d="M607 230h0l2 5 1 1v5c-1 3-2 5-5 7h0l1-1c1-1 1-2 1-4 1-1 1-1 1-2l-1-1c0-2 0-3 1-4h0c-1-2-2-3-2-5l1-1z" class="L"></path><path d="M603 231h1v-1h1l1 1c0 2 1 3 2 5h0c-1 1-1 2-1 4h-1-1l-1-2v-1c0-2 0-2-2-2-1 0-2 1-3 1h-1-2-1v-1-1l1-1c2-1 3-2 5-3h0l2 1z" class="R"></path><path d="M598 236c2-1 2-4 5-4l1 1-1 1h0l-1 1h0c-1 0-2 1-3 1h-1z" class="U"></path><path d="M602 235h0l1-1h0c1 1 1 1 2 1 1 1 1 1 2 1h0 1c-1 1-1 2-1 4h-1-1l-1-2v-1c0-2 0-2-2-2z" class="i"></path><path d="M606 240l1 1c-2 1-3 3-4 5v1h-1c-1-1-2-1-3-1l-3-3c0-1-1-1-2-2 2-2 2 0 4-1l1-1c1-1 3-2 5-1l1 2h1z" class="O"></path><path d="M596 221h1c3 1 7 5 9 8l1 1-1 1-1-1h-1v1h-1l-2-1h0c-2 1-3 2-5 3l-1 1c-1-1-2-1-3-3-1-1-1-2-2-3-1 0-1 0 0-1h0c-1-1-1-2-2-3h4l2 1 1-1 1-3z" class="V"></path><path d="M595 224c3 0 3 0 5 2l-4 1-2-1v-1l1-1z" class="h"></path><path d="M600 226l3 4v1l-2-1h0c-2 1-3 2-5 3l-1-1c1-1 2-2 2-3l-1-2 4-1z" class="Q"></path><path d="M588 224h4l2 1v1l2 1 1 2c0 1-1 2-2 3l1 1-1 1c-1-1-2-1-3-3-1-1-1-2-2-3-1 0-1 0 0-1h0c-1-1-1-2-2-3z" class="Z"></path><path d="M594 226l2 1 1 2c0 1-1 2-2 3-1-1-2-1-2-2-1-1-1-2 0-4h1z" class="W"></path><path d="M605 213h0c1 0 1 0 2-1 1 0 2 0 3 1s3 0 5 0c1 1 1 2 3 2h2l2 2h1c0 1-1 1-1 2 1 1 2 1 4 1h0c-1 2 0 2-2 2 0 1-1 1-1 2-1 2-1 3-3 4h-2v2l-1 1-1 1c0 1-1 4 0 5l1 1c0 1 0 3-1 4h0v2l-2-2 1-2c-1-1-1-1-2-1v-5c-2 0-2 0-4 1l-2-5h0l-1-1c-2-3-6-7-9-8l-1-1c-1-1 0-1-1-1h-1c-1-1-3-1-5-1l2-1h2 1 1l4 2 1-1c-1 0-1-1-1-1l1-1c1-1 1-2 1-3l2-1 2 1z" class="R"></path><path d="M611 223c-1-1-2-1-3-2s-2-2-3-2v-1h-1c1-1 2-1 4-1l3 6z" class="U"></path><path d="M607 230c0-1 0-1 1-2v-1-1c2 3 4 5 5 8h0c-2 0-2 0-4 1l-2-5z" class="W"></path><path d="M603 212l2 1-2 1h0c1 1 0 1 0 2l1 1c0 1-1 3-1 4l-4-2 1-1c-1 0-1-1-1-1l1-1c1-1 1-2 1-3l2-1z" class="t"></path><path d="M589 218l2-1h2 1 1l4 2 4 2c2 2 4 3 5 5v1 1c-1 1-1 1-1 2h0l-1-1c-2-3-6-7-9-8l-1-1c-1-1 0-1-1-1h-1c-1-1-3-1-5-1z" class="a"></path><path d="M608 217l1-2h1c3 1 4-1 7 2-2 0-5 0-7-1l-1 1h1c1 1 2 2 2 3v1c1 0 2 1 2 1 0 1 0 2 1 3l1 1c0 1 0 1-1 2 1 1 2 1 3 0v2l-1 1-1 1c0 1-1 4 0 5l1 1c0 1 0 3-1 4h0v2l-2-2 1-2c-1-1-1-1-2-1v-5h0l1-3-1-1v-1l-1-1c1-1 1-1 1-2-1-2-1-2-2-3l-3-6z" class="C"></path><path d="M614 231c1 3 1 6 1 9-1-1-1-1-2-1v-5h0l1-3z" class="f"></path><path d="M618 228c-1 1-2 1-3 0 1-1 1-1 1-2l-1-1c-1-1-1-2-1-3 0 0-1-1-2-1v-1c0-1-1-2-2-3h-1l1-1c2 1 5 1 7 1h1c2-1 2-1 4 0h1c0 1-1 1-1 2 1 1 2 1 4 1h0c-1 2 0 2-2 2 0 1-1 1-1 2-1 2-1 3-3 4h-2z" class="D"></path><path d="M622 219c1 1 2 1 4 1h0c-1 2 0 2-2 2 0 1-1 1-1 2-1 2-1 3-3 4h-2l-1-1c1 0 1-1 2-1 0-1 1 0 2-1l-1-1-2 1v-1c1-2 1-3 3-3v-2h1z" class="W"></path><path d="M554 218l1-1c0 1 1 1 2 1h0 2c-3 7-6 15-6 23l1 1c0 2 0 2 1 4v1 1c0 1 0 1-1 2h-1c-1 1-3 1-5 2-1 1-2 1-3 1-3-1-4 2-7 1-1 0-2 1-3 1h-2-1l1-1c0-1 0-2-1-2v-1-2h-1l-1-1 2-1c0-2-1-2-2-3h0l-1-2c1-1 1-1 1-2v-1-1l7-3c0-1 3-3 3-4l1-1 3-3s1-1 1-2l2-2h0v2h2 0l2-1h1v-2l2-2v-2z" class="H"></path><path d="M550 232v2c-1 0-2 1-3 0v-1l3-1z" class="a"></path><path d="M547 238c2 0 2 0 3 1 0 1 0 1-1 2l-1-1h-2l1-2zm-12 6h3v1l-1 2h-1l-1-2v-1z" class="N"></path><path d="M542 236c1 1 3 2 3 3 0 2 0 2-1 4h-5c0-1-1-1-1-1-1-1-1-2-1-3 2-1 4-3 5-3z" class="J"></path><path d="M532 247h2l1 1 2 1h0c1-1 1-1 2-1l1-2h0 4c1 0 1-1 2-1 0-1 0-1 1-1s1-1 2-2h1l2 2h-1l-1 1-1 3h-1l-1-1c-1 0-1 0-1 1h-1c-2 0-3 0-4 1-2 1-3 1-5 1l-1 2c-1-1-1-2-2-3h-1-1l-1-1 2-1z" class="a"></path><path d="M555 248c0 1 0 1-1 2h-1c-1 1-3 1-5 2-1 1-2 1-3 1-3-1-4 2-7 1-1 0-2 1-3 1h-2-1l1-1 9-3 13-3z" class="K"></path><path d="M544 227s1-1 1-2l2-2h0v2h2 0l2-1v1c0 1-1 2-1 3-1 1-2 2-2 4l-1 1v1l-4 1c-1 0-1 0-1 1-1 0-3 2-5 3h0c-1 1-1 0-1 1-1 1-2 1-2 2h-1l4-7c0-1 3-3 3-4l1-1 3-3z" class="F"></path><path d="M549 225h0l2-1v1c0 1-1 2-1 3-1 1-2 2-2 4l-1-1h0l-2-2h-1c1-1 0-1 2-1h1l2-3z" class="B"></path><path d="M554 218l1-1c0 1 1 1 2 1h0 2c-3 7-6 15-6 23v-1c-1 0-2 0-3-1s-1-1-3-1v-3c1 0 2 1 3 2l1-1c0-1 0-1-1-2v-2l-3 1 1-1c0-2 1-3 2-4 0-1 1-2 1-3v-1h1v-2l2-2v-2z" class="T"></path><path d="M554 218l1-1c0 1 1 1 2 1-1 2-1 3-2 4l-1-2v-2z" class="N"></path><path d="M551 224h1v-2l2-2 1 2-1 1h0c1 1 1 1 1 2l-2 2c0 1 0 1 1 2-1 1-1 2-2 3l-1-1c-1 1-1 0-1 1l-3 1 1-1c0-2 1-3 2-4 0-1 1-2 1-3v-1z" class="D"></path><path d="M548 191h13 5c1 0 1-1 2-1h8 2 2l1 1s-1 1-2 1c-2 3-4 6-5 9l-2 2 3 1c-1 2-2 2-2 4l-1 1c0 1 0 1 1 2h-1 0v2h0l-2 1c-1 3-3 5-5 8h-1c-2-1-1-6-2-8l-1 2-2 2h-2 0c-1 0-2 0-2-1l-1 1v2l-2 2v2h-1l-2 1h0-2v-2h0l-2 2c0 1-1 2-1 2-1 0-1-1-1-1 0-1 0-3 1-3 0-1 0 0 1-1l-1-1v-3c-1-1-1-1 0-2v-2l2-1c1 0 1-1 2-2v-2h1c0-2 1-3 2-4-1 1-2 1-3 3v1l-3 2c-1 0-1-1-1-1-1-1-1-2-1-2l-1-1c0-2-1-4-2-6v-3h0l-1-2c1 0 2-1 3-1l2 2c0-2-1-4-2-6h1c1 0 3 0 4 1l1-1z" class="O"></path><path d="M553 206l3-3 1 3-1 3-1 1h-5l3-4z" class="x"></path><path d="M553 206l3-3 1 3-1 3c-2-1-2-1-3-3z" class="G"></path><path d="M550 210h5l-1 3-5 8c-1 0-1 1-2 2h0c1-2 1-3 1-5 0-3 1-5 2-8z" class="E"></path><path d="M554 213v1c0 1 1 1 1 2s-2 0-1 2h0v2l-2 2v2h-1l-2 1h0-2v-2c1-1 1-2 2-2l5-8z" class="C"></path><path d="M548 191h13 5c1 0 1-1 2-1h8 2 2l1 1s-1 1-2 1c-1-1-2 0-4 0h-9c-3 4-5 9-8 13l-1 1-1-3 2-2c2-3 4-5 6-9-2 1-4 1-5 0-4-1-9 0-12 0l1-1z" class="E"></path><path d="M544 197c2 0 2 0 3-1l-1-2h1 0l1 1 2-1h5l1 1h1 2v3c-1 1-3 2-3 3v1h-1c-2 0-3 1-4 3h0c-1 1-2 1-3 3v1l-3 2c-1 0-1-1-1-1-1-1-1-2-1-2l-1-1c0-2-1-4-2-6v-3h0l-1-2c1 0 2-1 3-1l2 2z" class="g"></path><path d="M540 198l1 1c2 1 4 1 7 0v-1l-1-1 1-1c0 1 1 1 1 1 0 1 1 2 2 3v1h-3c-1 1-2 1-3 1h-2v1c0 1 0 1 1 2v2h-2c0-2-1-4-2-6v-3z" class="v"></path><path d="M558 205c3-4 5-9 8-13h9c2 0 3-1 4 0-2 3-4 6-5 9l-2 2 3 1c-1 2-2 2-2 4l-1 1c0 1 0 1 1 2h-1 0v2h0l-2 1c-1 3-3 5-5 8h-1c-2-1-1-6-2-8l-1 2-2 2h-2 0c-1 0-2 0-2-1l-1 1h0c-1-2 1-1 1-2s-1-1-1-2v-1l1-3 1-1 1-3 1-1z" class="N"></path><path d="M566 216l2-5c2 1 3 1 4 2l-2 1c-1 1-3 2-4 2z" class="c"></path><path d="M566 216c1 0 3-1 4-2-1 3-3 5-5 8h-1l2-6zm6-13l3 1c-1 2-2 2-2 4l-1 1c0 1 0 1 1 2h-1 0v2h0c-1-1-2-1-4-2l4-8z" class="J"></path><path d="M561 207c1-2 1-4 3-6 1-2 1-4 3-4 2-1 2-1 3 0 0 2-1 3-2 5-2 3-4 7-4 10h-2c-2-2-1-3-1-5z" class="o"></path><path d="M557 206l1-1c1 1 1 2 3 2h0c0 2-1 3 1 5h2v1l-1 1h-1l-1 2-2 2h-2 0c-1 0-2 0-2-1l-1 1h0c-1-2 1-1 1-2s-1-1-1-2v-1l1-3 1-1 1-3z" class="D"></path><path d="M561 207h0c0 2-1 3 1 5h2v1l-1 1h-1l-1 2c-2-1-2-1-2-3 0-1 0-1 1-3 0-1 0-2 1-3z" class="g"></path><defs><linearGradient id="AI" x1="620.694" y1="222.821" x2="641.269" y2="212.706" xlink:href="#B"><stop offset="0" stop-color="#aba9a8"></stop><stop offset="1" stop-color="#dbdad9"></stop></linearGradient></defs><path fill="url(#AI)" d="M619 198l-2-2h-1l1-1h2c1 3 8 4 11 5 2 1 5 3 6 5 1 3 3 4 5 7 1 1 2 3 3 4 1 2 3 3 3 5l2 2v4c1 1 2 3 3 4 0 3 1 5 2 8l-2 2 1 1h-2c-1-2-2-5-4-7h-2v-2l-3 1-1-1s-2-1-3-1v-1c-1-1-2-1-2-1-2-1-2-2-4-2v-1-2c0-1-1-1-2-1l-2 2-1-3 1-1v-1l-2-1h0c-2 0-3 0-4-1 0-1 1-1 1-2h-1l-2-2h-2c-2 0-2-1-3-2-2 0-4 1-5 0s-2-1-3-1c-1 1-1 1-2 1h0l-2-1-2 1-1-1c0-1 0-1-1-1 0-1-1-2-2-2l-4 1h-1v-1c1-2 3-3 4-4h1l-8 2c1-1 3-2 4-3 0 0 0-2 1-2l-1-1 3-1-1-1 7-2c6-1 11-1 17 1z"></path><path d="M636 216c7 8 12 16 16 25l1 1h-2c-1-2-2-5-4-7-3-7-9-12-14-18 1-1 2-1 3-1z" class="Z"></path><path d="M619 198l-2-2h-1l1-1h2c1 3 8 4 11 5 2 1 5 3 6 5 1 3 3 4 5 7 1 1 2 3 3 4 1 2 3 3 3 5l2 2v4c-2-4-5-7-7-11-7-8-13-14-23-18z" class="E"></path><defs><linearGradient id="AJ" x1="594.435" y1="199.014" x2="611.447" y2="202.741" xlink:href="#B"><stop offset="0" stop-color="#151414"></stop><stop offset="1" stop-color="#3b3a39"></stop></linearGradient></defs><path fill="url(#AJ)" d="M596 200c6-1 11-3 16-1l-1 1 1 3-1 1h-9l-5 1-8 2c1-1 3-2 4-3 0 0 0-2 1-2l-1-1 3-1z"></path><path d="M594 202s1 1 2 1 1-1 3 0c1 0 2 0 3 1l-5 1-8 2c1-1 3-2 4-3 0 0 0-2 1-2z" class="J"></path><defs><linearGradient id="AK" x1="609.742" y1="204.021" x2="635.557" y2="211.201" xlink:href="#B"><stop offset="0" stop-color="#363735"></stop><stop offset="1" stop-color="#6b6867"></stop></linearGradient></defs><path fill="url(#AK)" d="M612 199c10 3 18 9 24 17-1 0-2 0-3 1-4-4-9-10-15-11-2-1-5-2-7-2l1-1-1-3 1-1z"></path><path d="M602 204h9c2 0 5 1 7 2 0 2 0 3-1 5l-2-1-8-1v1c-1 2-2 1-4 2l-2 1-1-1c0-1 0-1-1-1 0-1-1-2-2-2l-4 1h-1v-1c1-2 3-3 4-4h1l5-1z" class="D"></path><path d="M618 206c0 2 0 3-1 5l-2-1 1-4h2z" class="a"></path><path d="M597 209h0 10v1c-1 2-2 1-4 2l-2 1-1-1c0-1 0-1-1-1 0-1-1-2-2-2z" class="d"></path><path d="M618 206c6 1 11 7 15 11 5 6 11 11 14 18h-2v-2l-3 1-1-1s-2-1-3-1v-1c-1-1-2-1-2-1-2-1-2-2-4-2v-1-2c0-1-1-1-2-1l-2 2-1-3 1-1v-1l-2-1h0c-2 0-3 0-4-1 0-1 1-1 1-2h-1l-2-2h-2c-2 0-2-1-3-2-2 0-4 1-5 0s-2-1-3-1c-1 1-1 1-2 1h0l-2-1c2-1 3 0 4-2v-1l8 1 2 1c1-2 1-3 1-5z" class="I"></path><path d="M623 217c3 1 5 3 7 5v1 1l-2 2-1-3 1-1v-1l-2-1h0c-2 0-3 0-4-1 0-1 1-1 1-2z" class="q"></path><path d="M630 223c3 1 4 2 6 4v1l1 1c1 1 2 2 3 2h2c0 1 0 1-1 2 0 0-2-1-3-1v-1c-1-1-2-1-2-1-2-1-2-2-4-2v-1-2c0-1-1-1-2-1v-1z" class="t"></path><path d="M618 206c6 1 11 7 15 11 5 6 11 11 14 18h-2v-2l-3 1-1-1c1-1 1-1 1-2l1-1c-2-3-6-8-10-11-2-2-12-9-16-8 1-2 1-3 1-5z" class="D"></path><path d="M643 230l2 3-3 1-1-1c1-1 1-1 1-2l1-1z" class="r"></path><defs><linearGradient id="AL" x1="616.467" y1="176.116" x2="628.054" y2="205.762" xlink:href="#B"><stop offset="0" stop-color="#100c0b"></stop><stop offset="1" stop-color="#272828"></stop></linearGradient></defs><path fill="url(#AL)" d="M656 187h22c-1 1-2 1-2 2h0 1v-1h0c2 1 3 2 5 2-6 0-11 1-15 6-1 1-2 2-3 4l-1 5h0v6 1l1 1v1l-2 2v2c-6-8-11-15-19-22l-3-2-5-3c-2 1-3 1-5 3v1h1c1 1 1 1 1 2h-1c1 1 1 2 1 3l-1-1-1 1c-3-1-10-2-11-5h-2l-1 1h1l2 2c-6-2-11-2-17-1l-7 2 1 1-3 1 1 1c-1 0-1 2-1 2-1 1-3 2-4 3s-2 2-3 2h-3c-2 1-5 4-6 5h-1l-2-2-2 1v-2h0 1c-1-1-1-1-1-2l1-1c0-2 1-2 2-4l-3-1 2-2c1-3 3-6 5-9 1 0 2-1 2-1 2-2 3-3 6-3l43-1h26z"></path><path d="M572 203l2-2v2h2s0 1-1 1l-3-1z" class="J"></path><path d="M640 194c1-1 1-3 2-4 2 1 5 0 7 1-1 0-2 2-4 1h-1v2l-1 1v1l-3-2z" class="P"></path><path d="M577 202l2-2c1-2 2-3 4-5l2-2c4-3 9-4 14-3-2 1-4 2-7 2v1h-3v-1c-3 2-5 4-7 7v2h2l-2 2c-1 0-2-1-4 0h0l-1-1z" class="Y"></path><path d="M656 187h22c-1 1-2 1-2 2h0 1v-1h0c2 1 3 2 5 2-6 0-11 1-15 6-1 1-2 2-3 4l-2-4 4-5 1 1c2-1 1-1 2-3l2 1h0l1-1h3v-1c-4-1-9 0-13 0h-23c-2 0-7 0-9-1h26z" class="p"></path><path d="M599 190c2-1 4 0 6 0h14 5c-1 1-10 0-12 1h-4l-1 2h2c-2 0-4 1-5 1s-4 1-5 1l-3 1-12 5h-2v-2c2-3 4-5 7-7v1h3v-1c3 0 5-1 7-2z" class="l"></path><path d="M604 194c-1-1-1 0-1-1l-1-1 1-1h5l-1 2h2c-2 0-4 1-5 1z" class="t"></path><path d="M584 201h-2v-2c2-3 4-5 7-7v1 2l-1 1h-1l2 2h1c1-2 1-2 3-3v1h3l-12 5z" class="I"></path><path d="M596 196l3-1 1 1h0 0c1 0 1 1 2 1l-7 2 1 1-3 1 1 1c-1 0-1 2-1 2-1 1-3 2-4 3s-2 2-3 2h-3c-2 1-5 4-6 5h-1l-2-2-2 1v-2h0 1c-1-1-1-1-1-2l1-1c0-2 1-2 2-4 1 0 1-1 1-1l1-1 1 1h0c2-1 3 0 4 0l2-2 12-5z" class="s"></path><path d="M576 203l1-1 1 1h0c2-1 3 0 4 0-1 0-2 1-2 2-2 0-3 1-4 1v1l-2 2-1 2c-1-1-1-1-1-2l1-1c0-2 1-2 2-4 1 0 1-1 1-1z" class="P"></path><path d="M596 196l3-1 1 1h0 0c1 0 1 1 2 1l-7 2 1 1-3 1c-1 0-1 0-2 1h-1c-3 2-6 5-9 5h-1v-2c0-1 1-2 2-2l2-2 12-5z" class="J"></path><path d="M581 207v-1c0-1 1-1 2-1h1c0-1 1-2 2-2l6-3c1-1 1-1 3-1l1 1-3 1c-1 0-1 0-2 1h-1c-3 2-6 5-9 5z" class="X"></path><path d="M624 190c3 0 6-1 9 0l2 1c-2 1-3 1-5 3v1h1c1 1 1 1 1 2h-1c1 1 1 2 1 3l-1-1-1 1c-3-1-10-2-11-5h-2l-1 1h1l2 2c-6-2-11-2-17-1-1 0-1-1-2-1h0 0l-1-1c1 0 4-1 5-1s3-1 5-1h-2l1-2h4c2-1 11 0 12-1z" class="c"></path><path d="M619 195c5 1 8 2 12 4l-1 1c-3-1-10-2-11-5z" class="Q"></path><defs><linearGradient id="AM" x1="612.866" y1="188.231" x2="618.973" y2="196.389" xlink:href="#B"><stop offset="0" stop-color="#807e7e"></stop><stop offset="1" stop-color="#9c9997"></stop></linearGradient></defs><path fill="url(#AM)" d="M624 190c3 0 6-1 9 0l2 1c-2 1-3 1-5 3v1h1c1 1 1 1 1 2h-1s-1-1-2-1c-7-2-13-3-20-3h-2l1-2h4c2-1 11 0 12-1z"></path><path d="M633 190l2 1c-2 1-3 1-5 3v1h1c1 1 1 1 1 2h-1s-1-1-2-1-1-2-2-2c-1-1-1 0-3-1l2-2h2 0 2c1-1 2-1 3-1z" class="C"></path><path d="M649 191l2-1v1h4 2 4c2-1 3 0 5 0l-4 5 2 4-1 5h0v6 1l1 1v1l-2 2v2c-6-8-11-15-19-22v-1l1-1v-2h1c2 1 3-1 4-1z" class="t"></path><path d="M661 212v-2-4h1c0 2 1 3 1 4v1 1c-1 1 0 2-1 3h-1v-3z" class="Q"></path><path d="M654 196h0c1 1 1 2 2 3l-1 1-1 1h1l2 2-1 1h-1l-1-1c-1-2-3-3-5-5 1-1 1 0 2-1 1 1 1 1 2 1v-1l1-1z" class="R"></path><path d="M663 205c-1 0-1 0-2-1 0-2 0-2 1-3l-1-2c-1-1-1 1-3 1 0-1 0-2-1-3v-1h0 1 4l2 4-1 5z" class="Y"></path><path d="M661 191c2-1 3 0 5 0l-4 5h-4v-1-2l3-2h0z" class="C"></path><path d="M658 195v-2l3-2 1 2v1l-4 1z" class="W"></path><path d="M649 191l2-1v1c0 1 2 2 2 4h-2v-2h-1l-1 1h-3c0 1 2 2 2 2 0 1-1 1-1 2 0 0 7 7 8 7h0c2 1 5 5 6 7v3h1c1-1 0-2 1-3l1 1v1l-2 2v2c-6-8-11-15-19-22v-1l1-1v-2h1c2 1 3-1 4-1z" class="d"></path><path d="M635 191l5 3 3 2c8 7 13 14 19 22 1 1 3 3 3 4 2 2 3 4 5 6l7 10v6c0 1-1 1-1 1h0l-2-2-2 1-2-4c-1 2-1 3 0 5 2 2 3 4 4 6l1 3h-3 0v1l-4-1c2 4 5 7 6 11 1 1 1 3 2 4 1 3 4 6 5 8 1 1 2 3 2 4-1-1-2-1-3-1h1c0 1 1 3 1 5l3 3c2 2 4 5 5 7v1l-10-9c-2-1-5-2-7-4-2-1-5-3-7-4-5-3-9-6-14-8v-2h2 0c1-1 2-2 4-3-2-1-3-2-4-2l1-1 1-2c-1-1-2-2-1-4h-1v-1c1-2 1-3 0-5l1-1c1 1 1 2 1 2h2l-1-2h1l-1-2-2-3-2-3-1-1 2-2c-1-3-2-5-2-8-1-1-2-3-3-4v-4l-2-2c0-2-2-3-3-5-1-1-2-3-3-4-2-3-4-4-5-7-1-2-4-4-6-5l1-1 1 1c0-1 0-2-1-3h1c0-1 0-1-1-2h-1v-1c2-2 3-2 5-3z" class="v"></path><path d="M665 222c2 2 3 4 5 6 1 3 0 5 2 7 0 1-1 2-2 2l-1-1c-1 0-2 0-3-1l-3-5-5-7c1 1 1 1 2 1v1c1-1 2-1 4-1 1 1 0 3 0 5 1 1 4 3 5 5 0 1 0 1 1 2h1c-1-1-1-2-1-3v-1c-1-1-2-2-3-2v-1h0 2l-1-1h-1v-2l-2-3v-1z" class="N"></path><path d="M663 230c3 2 5 2 6 6-1 0-2 0-3-1l-3-5z" class="O"></path><path d="M670 228l7 10v6c0 1-1 1-1 1h0l-2-2-2 1-2-4c0-1-3-4-4-5 1 1 2 1 3 1l1 1c1 0 2-1 2-2-2-2-1-4-2-7z" class="H"></path><path d="M674 243h0c-1-2-2-3-3-3v-2h2l1-1s1 1 2 1c1 2-1 4 0 6v1l-2-2z" class="D"></path><path d="M632 197c11 8 18 16 26 26l5 7 3 5c1 1 4 4 4 5-1 2-1 3 0 5h-2c-4-7-8-14-13-20h-2c-5-7-10-14-17-20-1-2-4-4-6-5l1-1 1 1c0-1 0-2-1-3h1z" class="E"></path><path d="M631 199l1 1c3 2 7 4 10 7 5 4 9 12 13 18h-2c-5-7-10-14-17-20-1-2-4-4-6-5l1-1z" class="Z"></path><path d="M636 205c7 6 12 13 17 20h2c5 6 9 13 13 20h2c2 2 3 4 4 6l1 3h-3 0v1l-4-1c2 4 5 7 6 11h0c0 2 2 6 2 7-1 0-1 0-2-1v-1c0-1-1-2-2-3l-14-25c0 2-1 3 0 5l-1 1-2-3-2-3-1-1 2-2c-1-3-2-5-2-8-1-1-2-3-3-4v-4l-2-2c0-2-2-3-3-5-1-1-2-3-3-4-2-3-4-4-5-7z" class="G"></path><path d="M652 231c2 4 4 7 6 11 0 2-1 3 0 5l-1 1-2-3-2-3-1-1 2-2c-1-3-2-5-2-8z" class="D"></path><path d="M655 225c5 6 9 13 13 20h2c2 2 3 4 4 6l1 3h-3 0v1l-4-1c-5-9-9-20-15-29h2z" class="L"></path><path d="M668 245h2c2 2 3 4 4 6l1 3h-3 0c-2-3-3-6-4-9z" class="K"></path><path d="M658 242l14 25c1 1 2 2 2 3v1c1 1 1 1 2 1 0-1-2-5-2-7h0c1 1 1 3 2 4 1 3 4 6 5 8 1 1 2 3 2 4-1-1-2-1-3-1h1c0 1 1 3 1 5l3 3c2 2 4 5 5 7v1l-10-9c-2-1-5-2-7-4-2-1-5-3-7-4-5-3-9-6-14-8v-2h2 0c1-1 2-2 4-3-2-1-3-2-4-2l1-1 1-2c-1-1-2-2-1-4h-1v-1c1-2 1-3 0-5l1-1c1 1 1 2 1 2h2l-1-2h1l-1-2 1-1c-1-2 0-3 0-5z" class="C"></path><path d="M658 252l-1-2h1l7 13h-3v-1c-1-1-1-2-2-3h0 0c-1-3-1-5-2-7z" class="E"></path><path d="M674 265c1 1 1 3 2 4 1 3 4 6 5 8 1 1 2 3 2 4-1-1-2-1-3-1h1c0 1 1 3 1 5-4-6-8-12-10-18 1 1 2 2 2 3v1c1 1 1 1 2 1 0-1-2-5-2-7h0z" class="b"></path><path d="M654 251l1-1c1 1 1 2 1 2h2c1 2 1 4 2 7h0 0c1 1 1 2 2 3v1h0c-1 1-1 2-2 4h-1l-1-2v1c-2-1-3-2-4-2l1-1 1-2c-1-1-2-2-1-4h-1v-1c1-2 1-3 0-5z" class="F"></path><path d="M660 259h0c1 1 1 2 2 3v1h0c-1 1-1 2-2 4h-1l-1-2c1-2 1-4 2-6z" class="P"></path><path d="M654 251l1-1c1 1 1 2 1 2l2 5-1 1c-1 1-1 2-1 3-1-1-2-2-1-4h-1v-1c1-2 1-3 0-5z" class="h"></path><path d="M665 263c5 9 12 17 18 25h2c2 2 4 5 5 7v1l-10-9c-1-1-2-2-3-4h-1-1c-2-2 0-3-1-4s-3-3-3-4c-1-3-2-3-4-5v-1c-1-2-1-3-2-4h-2l1-1-2-1h0 3z" class="d"></path><path d="M662 263l2 1-1 1h2c1 1 1 2 2 4v1c2 2 3 2 4 5 0 1 2 3 3 4s-1 2 1 4h1 1c1 2 2 3 3 4-2-1-5-2-7-4-2-1-5-3-7-4-5-3-9-6-14-8v-2h2 0c1-1 2-2 4-3v-1l1 2h1c1-2 1-3 2-4z" class="l"></path><path d="M658 265l1 2-1 1 1 1-1 1h-1-2l-1-1h0c1-1 2-2 4-3v-1z" class="E"></path><path d="M666 274h-1l-1 1c0-1-1-2-2-3l1-1c2 0 3 0 4 1 0 1 1 1 2 2l1 1h1l-1 1h0c-2-1-3-1-4-2zm-4-11l2 1-1 1h2c1 1 1 2 2 4-2 1-2 1-4 1-2-1-2-2-3-3 1-2 1-3 2-4z" class="L"></path><path d="M652 269h2l1 1c2 2 4 3 7 4 3 2 7 6 11 7h0c-2-3-5-4-8-6l1-1c1 1 2 1 4 2h0l1-1c0 1 2 3 3 4s-1 2 1 4h1 1c1 2 2 3 3 4-2-1-5-2-7-4-2-1-5-3-7-4-5-3-9-6-14-8v-2z" class="I"></path><defs><linearGradient id="AN" x1="611.364" y1="273.482" x2="618.228" y2="256.603" xlink:href="#B"><stop offset="0" stop-color="#aaa8a7"></stop><stop offset="1" stop-color="#e7e6e5"></stop></linearGradient></defs><path fill="url(#AN)" d="M626 220l2 1v1l-1 1 1 3 2-2c1 0 2 0 2 1v2 1c2 0 2 1 4 2 0 0 1 0 2 1v1c1 0 3 1 3 1l1 1 3-1v2h2c2 2 3 5 4 7h2l2 3 2 3 1 2h-1l1 2h-2s0-1-1-2l-1 1c1 2 1 3 0 5v1h1c-1 2 0 3 1 4l-1 2-1 1c1 0 2 1 4 2-2 1-3 2-4 3h0-2v2c5 2 9 5 14 8 2 1 5 3 7 4v3c3 2 6 4 8 5s4 3 5 4c0 1 0 1-1 1l-2-2c-2-1-3-2-5-3h-1c-2-3-8-7-12-8h-2c-2-1-4-1-6-2-31-16-64-28-99-27-4 1-9 1-12 2h-3v-1l2-2c1 0 2 0 3-1 2-1 4-1 5-2h1c1-1 1-1 1-2v-1-1l5-1v-1-1c1-1 1-2 2-4 0 2 0 3 1 4 2 1 4 1 6 1h5c3 0 5 0 7 1 2-1 2-1 4 0h4s1 1 2 1h2c0-2-1-3-1-4l1-1v2c2 3 4 4 8 6h1c2 0 4 1 6 0l3-3c-1-1-1-3-1-5v-5l-1-1c2-1 2-1 4-1v5c1 0 1 0 2 1l-1 2 2 2v-2h0c1-1 1-3 1-4l-1-1c-1-1 0-4 0-5l1-1 1-1v-2h2c2-1 2-2 3-4 0-1 1-1 1-2 2 0 1 0 2-2z"></path><path d="M571 245c7 1 15 2 23 4h0c-3 0-8-1-12-2-1 0-1 0-2 1h0c-2-1-6 0-8 1-1 1-2 1-3 1-1 1-1 1-2 0v-1l2-1 1 1 1 1v-2l1-1h-1v-2z" class="H"></path><defs><linearGradient id="AO" x1="546.824" y1="247.517" x2="560.835" y2="252.54" xlink:href="#B"><stop offset="0" stop-color="#93908e"></stop><stop offset="1" stop-color="#acacab"></stop></linearGradient></defs><path fill="url(#AO)" d="M560 245h11v2l-1 1h0v-2h-4c-2 2-4 4-6 5v1c0 1 0 0-1 1h-2l1 1c-4 1-9 1-12 2h-3v-1l2-2c1 0 2 0 3-1 2-1 4-1 5-2h1c1-1 1-1 1-2v-1-1l5-1z"></path><path d="M638 264h1c1 1 3 2 5 3l8 4c5 2 9 5 14 8 2 1 5 3 7 4v3l-1-1c-3-1-4-2-6-3h0l-2-1c-1 0-1 0-1-1-3-2-6-4-9-5-5-2-9-4-12-6-1-1-3-2-3-2-1-1-1-2-1-3z" class="O"></path><path d="M616 244v-2h0c1-1 1-3 1-4l-1-1c-1-1 0-4 0-5l1-1 1 2v2c1 1 1 2 1 3s-1 2-1 2l2 2c0 2-2 1-1 2s1 1 1 2 1 2 1 3h1 0l1 1h1c0 2 2 3 2 5h0c-1 1-2 1-3 1l8 3h1v1c2 1 6 2 7 4h-1c-15-6-29-12-44-15h0c-8-2-16-3-23-4h-11v-1-1c1-1 1-2 2-4 0 2 0 3 1 4 2 1 4 1 6 1h5c3 0 5 0 7 1 2-1 2-1 4 0h4s1 1 2 1h2c0-2-1-3-1-4l1-1v2c2 3 4 4 8 6h1c2 0 4 1 6 0l3-3c-1-1-1-3-1-5v-5l-1-1c2-1 2-1 4-1v5c1 0 1 0 2 1l-1 2 2 2z" class="n"></path><path d="M613 239c1 0 1 0 2 1l-1 2v5l-1 3c0 1 1 1 2 2l-1 1h-2l-3-2 1-1c2-2 3-4 3-6h-1l1-5z" class="R"></path><path d="M609 235c2-1 2-1 4-1v5l-1 5-1 2c-1-1-1-3-1-5v-5l-1-1z" class="O"></path><path d="M609 235c2-1 2-1 4-1l-1 2c-1 0-1 1-2 0l-1-1z" class="g"></path><path d="M614 242l2 2c-1 1-1 1-1 2v1c1 1 1 2 1 3h1c2-1 2-1 3 0l2 2h1v-2h1c0 2 2 3 2 5h0c-1 1-2 1-3 1s-2-1-3-1-2 0-2-1c-2 0-2-1-3-2h0c-1-1-2-1-2-2l1-3v-5z" class="F"></path><path d="M615 252h4c1 0 1 1 2 1 1 1 3 2 5 2h0c-1 1-2 1-3 1s-2-1-3-1-2 0-2-1c-2 0-2-1-3-2z" class="V"></path><g class="B"><path d="M616 244v-2h0c1-1 1-3 1-4l-1-1c-1-1 0-4 0-5l1-1 1 2v2c1 1 1 2 1 3s-1 2-1 2l2 2c0 2-2 1-1 2s1 1 1 2 1 2 1 3h1 0l1 1v2h-1l-2-2c-1-1-1-1-3 0h-1c0-1 0-2-1-3v-1c0-1 0-1 1-2z"></path><path d="M626 220l2 1v1l-1 1 1 3 2-2c1 0 2 0 2 1v2 1c2 0 2 1 4 2 0 0 1 0 2 1v1c1 0 3 1 3 1l1 1 3-1v2h2c2 2 3 5 4 7h2l2 3 2 3 1 2h-1l1 2h-2s0-1-1-2l-1 1c1 2 1 3 0 5v1h1c-1 2 0 3 1 4l-1 2-1 1c1 0 2 1 4 2-2 1-3 2-4 3h0-2v2l-8-4c-2-1-4-2-5-3-1-2-5-3-7-4v-1h-1l-8-3c1 0 2 0 3-1h0c0-2-2-3-2-5h-1l-1-1h0-1c0-1-1-2-1-3s0-1-1-2 1 0 1-2l-2-2s1-1 1-2 0-2-1-3v-2l-1-2 1-1v-2h2c2-1 2-2 3-4 0-1 1-1 1-2 2 0 1 0 2-2z"></path></g><path d="M638 237l2 1v1c-1 1-1 2 0 4h0l-2 1c-1-2-1-3-1-4l-2-1-2 1v-1l1-1h2l2-1z" class="N"></path><path d="M633 240l2-1 2 1c0 1 0 2 1 4h-2l-1-1h-1c-1 1-3 2-4 2h0v-1c1-1 1-1 1-3l2-1z" class="H"></path><path d="M630 245c1 0 3-1 4-2h1l1 1c-2 1-4 3-6 5l1 1v1l3 2 1 2h-1-3-1c0-1-1-3-2-4 1-2 0-4 2-6z" class="N"></path><path d="M628 244h1l1 1h0c-2 2-1 4-2 6 1 1 2 3 2 4h0l2 4h-1l-8-3c1 0 2 0 3-1h0c0-2-2-3-2-5l3-3v-2l1-1z" class="M"></path><path d="M626 255c1 1 1 1 2 1 0-2-1-3-1-5 0-1 0-2 1-3v3h0c1 1 2 3 2 4h0l2 4h-1l-8-3c1 0 2 0 3-1z" class="l"></path><path d="M626 220l2 1v1l-1 1 1 3-4 3c0 1 1 2 1 3s0 1-1 2l2 1c0 2 1 2 2 3h0c1 1 1 1 2 0 2-1 2-3 5-3v-1h1c1 1 1 2 2 3l-2 1h-2l-1 1v1l-2 1c0 2 0 2-1 3v1l-1-1h-1l-1 1v2l-3 3h-1l-1-1h0-1c0-1-1-2-1-3s0-1-1-2 1 0 1-2l-2-2s1-1 1-2 0-2-1-3v-2l-1-2 1-1v-2h2c2-1 2-2 3-4 0-1 1-1 1-2 2 0 1 0 2-2z" class="a"></path><path d="M626 242c2-1 2-1 3-2l2 1c0 2 0 2-1 3v1l-1-1h-1l-1-1h-1-1l1-1zm-2-8c0-1-2-2-2-4l1-1c1-3 3-4 4-6l1 3-4 3c0 1 1 2 1 3s0 1-1 2z" class="B"></path><path d="M619 238l1 1h0l2 2c1-2 0-4 1-6 1 2 2 2 2 4 0 1-1 1 0 2l1 1-1 1h1 1l1 1-1 1v2l-3 3h-1l-1-1h0-1c0-1-1-2-1-3s0-1-1-2 1 0 1-2l-2-2s1-1 1-2z" class="W"></path><path d="M625 241l1 1-1 1h1 1l1 1-1 1v2l-3 3h-1l-1-1c2-3 3-5 3-8z" class="D"></path><path d="M630 224c1 0 2 0 2 1v2 1c2 0 2 1 4 2 0 0 1 0 2 1v1c1 0 3 1 3 1l1 1 3-1v2h2c2 2 3 5 4 7v2l-1 1h-2c-1-1-2 0-4-1 0 1 0 1-1 2-1 2-1 4-1 6v3h-1l-2 1c-3 1-5 0-8-1h3 1l-1-2-3-2c1-1 1-1 1-2h0c2-1 2-1 4-1h3v-2l2-1c0-1 0-1 1-1 1-1 1-1 1-2s-1-1-1-2c-1-1 0-1-1-2h0l1-1c-2-2-4-3-5-4s-2-1-4-2l-1 2c-1-1-2-1-3-1l-2 1-1 2-2-1c1-1 1-1 1-2s-1-2-1-3l4-3 2-2z" class="U"></path><path d="M639 253c0-1 1-1 2-2v1h1v3h-1l-2-2z" class="F"></path><path d="M632 228v2c-2 0-2-1-3-1v-1c1-2 1-1 3-1v1z" class="e"></path><path d="M625 232h2c-1-1 0-1-1-2h1l2 2-2 1-1 2-2-1c1-1 1-1 1-2z" class="T"></path><path d="M639 253l2 2-2 1c-3 1-5 0-8-1h3 1l-1-2c2 1 3 1 5 0z" class="O"></path><path d="M645 233v2h2c2 2 3 5 4 7v2l-1 1h-2c-1-1-2 0-4-1 1-1 1-2 2-3h-2v-1c0-2 0-1-1-2-1-2 0-2-1-4l3-1z" class="V"></path><path d="M645 235h2c2 2 3 5 4 7v2l-1 1h-2c0-3 0-7-2-9l-1-1h0z" class="U"></path><path d="M651 242h2l2 3 2 3 1 2h-1l1 2h-2s0-1-1-2l-1 1c1 2 1 3 0 5v1h1c-1 2 0 3 1 4l-1 2-1 1c1 0 2 1 4 2-2 1-3 2-4 3h0-2v2l-8-4c-2-1-4-2-5-3-1-2-5-3-7-4v-1l-2-4h0 1c3 1 5 2 8 1l2-1h1v-3c0-2 0-4 1-6 1-1 1-1 1-2 2 1 3 0 4 1h2l1-1v-2z" class="L"></path><path d="M632 259l-2-4h0 1c3 1 5 2 8 1 0 1 1 3 2 4h-2l-1-1c-1 0-1 0-2 1h-1v-2h0l-3 2v-1z" class="Q"></path><path d="M651 242h2l2 3c-1 1-1 1-2 1l-1 1h0l-1-1v1l-2 2h-1c-1-2 0-3-1-4-1 0-1 0-1 1-1 0-1 1-1 2l-1 1c0 3 0 3-2 6v-3c0-2 0-4 1-6 1-1 1-1 1-2 2 1 3 0 4 1h2l1-1v-2z" class="S"></path><path d="M651 242h2l2 3c-1 1-1 1-2 1l-1 1h0l-1-1v1l-1-1c2 0 2 0 3-1l-1-1h-1v-2z" class="V"></path><path d="M655 245l2 3 1 2h-1l1 2h-2s0-1-1-2l-1 1c1 2 1 3 0 5v1c-2 1-3 0-5 0l1-2 1-5 1-3h0l1-1c1 0 1 0 2-1z" class="D"></path><path d="M651 250c1 1 1 2 2 4v1l-2 1-1-1 1-5z" class="e"></path><path d="M655 245l2 3 1 2h-1l1 2h-2s0-1-1-2l-1 1c0-2-1-3-2-4l1-1c1 0 1 0 2-1z" class="S"></path><path d="M636 260c1 0 2 0 2 1 1 1 1 1 2 1l1-1c2-1 3-1 5-1h1l1-1c-1 0-1-1-2-1 0-1 0-1 2-2v1h1c2 0 3 1 5 0h1c-1 2 0 3 1 4l-1 2-1 1c1 0 2 1 4 2-2 1-3 2-4 3h0-2v2l-8-4c-2-1-4-2-5-3-1-2-5-3-7-4l3-2h0v2h1z" class="V"></path><path d="M648 257h1c2 0 3 1 5 0h1c-1 2 0 3 1 4l-1 2h-5 0c1-1 2-1 3-2h-2l-3-3v-1z" class="n"></path><path d="M645 266l-2-1c-1-1-2-1-2-2h2c1-1 1-1 2-1l1 1h4 5l-1 1c1 0 2 1 4 2-2 1-3 2-4 3h0-2c-2 0-3-1-3-2l-4-1z" class="U"></path><path d="M645 266c1-1 2-1 3-1v-1h1l2 1-2 2-4-1z" class="T"></path><path d="M654 264c1 0 2 1 4 2-2 1-3 2-4 3l-1-1c-1-2 0-3 1-4z" class="C"></path><path d="M344 189c1 0 1 1 2 0h4v1c3 0 7 0 10-1l2 1c-1 1-1 1 0 2s2 2 3 4h0l3-1 1 2c2-2 2-1 4-1v-1c2 2 1 4 2 6l-1 2v1l-1 2v3h0v2 1c-1 0-1 1-2 1l1 3c-2 2-5 6-8 8l-1 1c0 1 0 1-1 2l-1 1-1-1c-2 2-3 5-4 8h0c0 2-1 4-2 5 0 2 0 3 1 5v2 3 2c-1-1-1-1-1-2-1-1-2-3-2-5-1 2-1 2-1 3h0-2l-1-3-2 3v2h0c0 1 0 2-1 3-1 0-2-1-4-1l-5 4-7 6h0c-2 0-2 0-3 1h-2c0 1-2 0-2-1l-1 1-1-3h-1c1 2 1 4 2 6l-2 2-1-1-2 1 1 1c0 1-1 2 0 2v1c1 1 3 2 3 3 1 2 2 3 4 5v1l1 1 1 1 12 8c1 2 3 3 4 5l1 1c-1 1-1 3-1 5h-1v1 1c1 3 1 3 2 5l-2 5c-1 3-1 5-2 8l-1 1v1l2 2 1-2h2c-1 1-2 3-3 4l-10 15-2 2c-1 1-1 2-2 4l-1 2-3 5-2 3h0c-1 1-2 1-2 1l-1 1h-2c-1 0-2 2-3 2h-1l1-2-1-1-1 1c-2 0-2 1-4 0l1-2c0-2-1-2-1-3l-3 1-3-3h-1v-1l-1-1h1c-2-2-2 0-4-1l-1-1-2 1c-1-1-1-2-2-3l2-1h0l1-1c3-3 4-5 3-10v-1c-1-2-1-3-2-5l-1-1-4-7c-1-1-1-2-1-3v1c-1 0-2-1-3-1l5-2c1-2 2-3 3-5 2 0 2 0 4-1 1-1 2-1 4-2h-1v-1-1c-1 0-1 0-1 1l-3-1 2-1 8-4 3-1c-1-1-3-2-4-3l-1-1c-2-1-3-2-4-4l1-1v-2l1-1-2-1h-2l-1 1c-2-3-3-7-4-10 0-2 0-3-1-4h-1v2h-1l-1-1c1-1 1-2 0-4v-1s0-1-1-1l-3-3-1-2v2l-2 2c-1 2-1 2-3 4 0 0-1 0-2 1l-1-1h0-3v1l-4-5h-2l-2-1-1-1v-1c0-2 0-3 1-4l-1-2c-2 1-2 3-3 4l1-9v-8l-1-9h-1v-4c1-2-2-7-3-9-1-1-2-3-2-4-2-3-3-5-5-7h1l-3-3c-1-1-1-2-2-3s-2-2-2-3l4 4 1-1-1-2 1-1-3-3c1-1 1-2 1-4 0-1-1-2-1-4l1-1c1 0 1-1 2-2h6 3 9 0l2 2 6-3c3-1 6-1 9-1 3 1 7 0 10 0l4 2h2 6 7c5-1 11 0 17 0 1 0 3-1 4 0h1l5-1c2-1 2-1 4-1z" class="g"></path><path d="M318 208l1-2 2 1v1c1 1 0 1 1 2h-1c-1 0-1 0-2-1l-1-1h0z" class="o"></path><path d="M313 199l8 3h0c-2 0-3 1-5 0h-2-1v-3z" class="O"></path><path d="M324 221c0-1-1-1-1-2h0l2-1v-2c-2 0-3-1-4-2v-1c1 0 1 1 2 1h2l2 1v2 1 1c-2 0-2 0-3 2z" class="a"></path><path d="M316 202l5 5-2-1-1 2h-1c-1 1-1 1-2 1v-1c-1-1-1-1-1-2s-1-2-1-3l3-1z" class="e"></path><path d="M321 202h9c-1 0-2 1-4 2 0 1 0 1 1 3 0 1 1 2 1 4-3-3-5-6-7-9h0z" class="Q"></path><path d="M329 213l4 3v1 2h-1-3-2v-1-1-2c2 0 2 0 3-1l-1-1h0z" class="N"></path><path d="M329 213l1-2 1 1 1-1 1 1v1c1 1 0 1 1 1 1-1 1 0 2-1h1v3l1 1c0-1 1-1 1-2s0 0 1-1h3c-1 2-1 3-1 5v3h1v1h-1l-1-1v1l-4-4c-1-1-1-2-3-3h-1l-4-3z" class="O"></path><path d="M342 222l-2-1v-3h1l1 1v3z" class="N"></path><path d="M333 216h1c2 1 2 2 3 3l4 4v-1l1 1h1v-1h-1v-3h2c1 1 1 2 0 4 1 2 2 3 3 6l1 1c1 1 1 2 1 3-1-1-1-2-2-3l-1 1c-2-1-2-2-3-4h0l-2-3-2 1h0l-1-1h-1-1c-1 0-1 1-2 1l-2-1v-1l-2-2 2-2h1v-2-1z" class="H"></path><path d="M333 222l3 2c-1 0-1 1-2 1l-2-1v-1l1-1z" class="q"></path><path d="M333 217l3 4h-1c-1 0-1 0-2 1l-1 1-2-2 2-2h1v-2z" class="O"></path><path d="M298 191h2 6c2 1 3 1 6 1-1 1-1 1 0 3h1 2v1l-1 1c1 0 2 1 3 1 2 0 4 0 7 1l1 1c1-1 2-1 4 0h1l5-3h1v1h0c-1 2-2 3-2 5-2 0-2 0-4-1h-9l-8-3c-5-2-9-4-14-7l-1-1z" class="d"></path><path d="M300 191h6c2 1 3 1 6 1-1 1-1 1 0 3h1 2v1l-1 1h0c-4 0-9-3-12-5l-2-1z" class="M"></path><path d="M284 189c3 1 7 0 10 0l4 2 1 1v1c2 2 4 3 6 5 5 4 9 10 12 16h0c1 1 1 2 1 3-2 0-2-2-3-3-2-6-6-11-11-15-2 0-2 0-3 2l-4-4h-1c-1-1-2-1-3-2h-1c-3-1-6-1-9-1h-1c-2-1-3-1-4-2-1 0-2 1-2 1h-1c-3 0-5 1-8 2l2-2 6-3c3-1 6-1 9-1z" class="U"></path><path d="M297 197l1-1c2 0 4 1 6 3-2 0-2 0-3 2l-4-4z" class="H"></path><path d="M275 190l2 1c2 0 4 0 6 1h3c1 0 2 0 2 1h0c-2 0-4 0-5 1h-1c-2-1-3-1-4-2-1 0-2 1-2 1h-1c-3 0-5 1-8 2l2-2 6-3z" class="B"></path><path d="M306 191h7c5-1 11 0 17 0 1 0 3-1 4 0h1l5-1v2c-2 1-4 2-5 5l-5 3h-1c-2-1-3-1-4 0l-1-1c-3-1-5-1-7-1-1 0-2-1-3-1l1-1v-1h-2-1c-1-2-1-2 0-3-3 0-4 0-6-1z" class="F"></path><path d="M315 195v-1c1 0 2 1 3 1 1-1 0-2 2-1v1l-1 2-1-1c-1 1-2 0-3 0v-1z" class="B"></path><path d="M324 198l-1-2h-1-1v-1c1-1 2-1 3 0h4c1 1 1 1 2 3h-1-1-4z" class="T"></path><path d="M306 191h7c5-1 11 0 17 0 1 0 3-1 4 0h1l5-1v2c-2 1-4 2-5 5l-5 3h-1c-2-1-3-1-4 0l-1-1c-3-1-5-1-7-1-1 0-2-1-3-1l1-1c1 0 2 1 3 0l1 1c1 0 2 1 3 1h2 4 1 1v-1c1 0 1-1 3-1l2-2c-1-1-2-1-2-2-2 0-4 1-6 0-2 0-4 0-5 1h-1l-1-1h-4l-2 1c-1 0-1-1-2-1-3 0-4 0-6-1z" class="L"></path><path d="M301 201c1-2 1-2 3-2 5 4 9 9 11 15v1l-2-2-1 2 2 3 1 1c1 1 3 3 5 3h0l1 1c-1 1-2 1-3 1s-1 0-2-1c0 1-1 1-1 2h-2c2 0 2 0 3 1l-4 1-5 1v-2-6l-2-1-1-3v-1c1-1 1-2 1-3-1-2-1-3-3-5h0v-1l3 2v-1c-1-2-2-4-4-6z" class="e"></path><path d="M302 206l3 2v-1l1 1 3 3c0 1-1 1-2 2s-1 3-1 4c1 1 1 1 1 2v1l-2-1-1-3v-1c1-1 1-2 1-3-1-2-1-3-3-5h0v-1z" class="h"></path><path d="M313 225h0-2c-1 0-2-1-3-2 0-1 0-1 1-2h1c1 1 2 1 3 2l1-1c-2-2-4-3-6-5l1-1c2 0 3 1 5 2h0l1 1c1 1 3 3 5 3h0l1 1c-1 1-2 1-3 1s-1 0-2-1c0 1-1 1-1 2h-2z" class="o"></path><path d="M339 199h1c1-1 2-3 3-5l1 2-2 2 2 2c-1 2 0 2 0 4v1h-1c1 1 1 1 1 2 1 2 1 2 1 4l2 2v1h1v1c1 2 2 2 2 4 1 1 2 1 3 1l1 1c-1 1-2 1-1 2h0c1 2 1 7 3 7l1-1 3-5 1-1h1l-2 4c-2 2-3 5-4 8h0c0 2-1 4-2 5 0 2 0 3 1 5v2 3 2c-1-1-1-1-1-2-1-1-2-3-2-5l-2-2c1-1 1-1 1-2v-1c-2-2-3-4-2-7 0-1 0-2-1-3l-1-1c-1-3-2-4-3-6 1-2 1-3 0-4h-2c0-2 0-3 1-5h-3l-1-3c0-2-1-3-1-4-1-3 0-5 1-8z" class="G"></path><path d="M339 211h1 0v-1-8c0 4 1 7 3 11v1h-3l-1-3z" class="C"></path><path d="M339 199h1c1-1 2-3 3-5l1 2-2 2-2 4v8 1h0-1c0-2-1-3-1-4-1-3 0-5 1-8z" class="B"></path><path d="M344 207c1 2 1 2 1 4l2 2v1h1v1c1 2 2 2 2 4 1 1 2 1 3 1l1 1c-1 1-2 1-1 2h0c1 2 1 7 3 7l1-1 3-5 1-1h1l-2 4c-2 2-3 5-4 8h0c-1 0-2-3-3-3v-3c-1-1 0-1 0-2l-1-1c-2-1-2-3-3-5 0-2-1-2-2-4-2-2-1-2-1-4l-1-1c0-2-1-3-1-5z" class="h"></path><path d="M343 213c3 5 5 9 7 14 1 4 3 8 4 13 0 2 0 3 1 5v2 3 2c-1-1-1-1-1-2-1-1-2-3-2-5l-2-2c1-1 1-1 1-2v-1c-2-2-3-4-2-7 0-1 0-2-1-3l-1-1c-1-3-2-4-3-6 1-2 1-3 0-4h-2c0-2 0-3 1-5v-1z" class="F"></path><path d="M344 189c1 0 1 1 2 0h4v1c3 0 7 0 10-1l2 1c-1 1-1 1 0 2s2 2 3 4h0l3-1 1 2c2-2 2-1 4-1v-1c2 2 1 4 2 6l-1 2v1l-1 2v3h0v2 1c-1 0-1 1-2 1l1 3c-2 2-5 6-8 8l-1 1c0 1 0 1-1 2l-1 1-1-1 2-4h-1l-1 1-3 5-1 1c-2 0-2-5-3-7h0c-1-1 0-1 1-2l-1-1c-1 0-2 0-3-1 0-2-1-2-2-4v-1h-1v-1l-2-2c0-2 0-2-1-4 0-1 0-1-1-2h1v-1c0-2-1-2 0-4l-2-2 2-2-1-2c-1 2-2 4-3 5h-1v-1h-3v-1h-1c1-3 3-4 5-5v-2c2-1 2-1 4-1z" class="C"></path><path d="M360 224h-3 0l1-3h3v2h0l-1 1zm-2-8l4 1c0 1 0 1-1 2h-2l-1 1-2-1h0l-1-1h1l2-2z" class="D"></path><path d="M356 218c0-1-1-4-1-5l-1-1v-1h2 1c1 0 2-1 3 0h0c0 1-1 2-1 3v1l-1 1-2 2zm-6-4c2 0 2-1 3 0 1 2 1 3 1 4l2 1h0c0 1 1 2 0 3v1 6h1l-1 1c-2 0-2-5-3-7h0c-1-1 0-1 1-2l-1-1v-2-1h-2c-1-1-1-2-1-3z" class="q"></path><path d="M373 195c2 2 1 4 2 6l-1 2v1l-1 2c-2 2-4 3-6 5v1l-2 1-1-1c2-2 4-4 6-7v-1l1-3-2-4c2-2 2-1 4-1v-1z" class="P"></path><path d="M373 196v4c-1 0-1 1-2 1l-2-4c2-2 2-1 4-1z" class="X"></path><path d="M365 196l3-1 1 2 2 4-1 3h-3c-1 1-2 2-2 3-2 0-2 0-3 2l1 1h0-2-2l3-3-1-1v-3h1l1-2-1-2c1-2 1-2 3-3h0z" class="H"></path><path d="M363 201c1 0 2 0 3 1-1 2-2 3-4 5l-1-1v-3h1l1-2z" class="Y"></path><path d="M365 196h0c1 3 1 3 1 6-1-1-2-1-3-1l-1-2c1-2 1-2 3-3z" class="Q"></path><path d="M367 212v-1c2-2 4-3 6-5v3h0v2 1c-1 0-1 1-2 1l1 3c-2 2-5 6-8 8l-1 1c0 1 0 1-1 2l-1 1-1-1 2-4h-1 0c2-4 5-7 7-10l-1-1z" class="F"></path><path d="M367 212v-1c2-2 4-3 6-5v3h0c-3 3-5 7-8 10 0 1-2 4-3 4h-1 0c2-4 5-7 7-10l-1-1z" class="r"></path><path d="M344 189c1 0 1 1 2 0h4v1c3 0 7 0 10-1l2 1c-1 1-1 1 0 2s2 2 3 4c-2 1-2 1-3 3l1 2-1 2h-1l-1-1-1-2h-1l-1 3c-1 1 0 2 0 4h-1l-1-1h-1l-1 1h-4l-1-1h0l-1-2v3s1 0 1 1v1l1 1c0 2 0 2 1 4 0 1 0 2 1 3h2v1 2c-1 0-2 0-3-1 0-2-1-2-2-4v-1h-1v-1l-2-2c0-2 0-2-1-4 0-1 0-1-1-2h1v-1c0-2-1-2 0-4l-2-2 2-2-1-2c-1 2-2 4-3 5h-1v-1h-3v-1h-1c1-3 3-4 5-5v-2c2-1 2-1 4-1z" class="t"></path><path d="M350 199c1-1 3-2 4-2s1 1 2 1h3l1 1h-2c-2 0-3 0-4 1l-1 1s1 1 1 2l-1 1 2 2h-1l-1 1h-4l-1-1h0l-1-2h0c1-2 2-3 3-5z" class="U"></path><path d="M350 199c1 2 2 3 3 5l-1 1c-1 1-2 1-3 2l-1-1h0l-1-2h0c1-2 2-3 3-5z" class="B"></path><path d="M344 205c1-2 2-3 2-6h0c2 0 2-1 2-1l1-1c2-1 4-1 6-1l1 2c-1 0-1-1-2-1s-3 1-4 2c-1 2-2 3-3 5h0v3s1 0 1 1v1l1 1c0 2 0 2 1 4 0 1 0 2 1 3h2v1 2c-1 0-2 0-3-1 0-2-1-2-2-4v-1h-1v-1l-2-2c0-2 0-2-1-4 0-1 0-1-1-2h1z" class="R"></path><path d="M344 189c1 0 1 1 2 0h4v1c3 0 7 0 10-1l2 1c-1 1-1 1 0 2s2 2 3 4c-2 1-2 1-3 3l1 2-1 2h-1l-1-1 1-1c0-1 1-1 1-2-1-2-2-3-4-4h-2l-1-1h-3c-2 0-4 1-6 3l-2 3-2-2 2-2-1-2c-1 2-2 4-3 5h-1v-1h-3v-1h-1c1-3 3-4 5-5v-2c2-1 2-1 4-1z" class="E"></path><path d="M343 192h6 1l-6 4-1-2c0-1 0-1-2-1 1-1 2-1 2-1z" class="C"></path><path d="M360 189l2 1c-1 1-1 1 0 2-4-1-6-1-10 0-1 0-2 1-2 0h-1-6c3-1 5-1 7-2 3 0 7 0 10-1z" class="w"></path><path d="M344 189c1 0 1 1 2 0h4v1c-2 1-4 1-7 2 0 0-1 0-2 1 2 0 2 0 2 1-1 2-2 4-3 5h-1v-1h-3v-1h-1c1-3 3-4 5-5v-2c2-1 2-1 4-1z" class="Q"></path><path d="M341 193c2 0 2 0 2 1-1 2-2 4-3 5h-1v-1h-3v-1c2-2 4-3 5-4z" class="O"></path><path d="M329 219h3l-2 2 2 2v1l2 1c1 0 1-1 2-1h1 1l1 1h0l2-1 2 3h0c1 2 1 3 3 4l1-1c1 1 1 2 2 3-1 3 0 5 2 7v1c0 1 0 1-1 2l2 2c-1 2-1 2-1 3h0-2l-1-3-2 3v2h0c0 1 0 2-1 3-1 0-2-1-4-1l-5 4-7 6h0c-2 0-2 0-3 1h-2c0 1-2 0-2-1l-1 1-1-3h-1c1 2 1 4 2 6l-2 2-1-1v-3h-2-2-1 0l-2-2c-1-1-1-1-2-3 0-3-3-3-3-6l-2-6c0-1-1-1-1-1v-1-3-4h0v-1h0v-1c1-1 0-2 1-3h2c1-1 2-1 3-1h1s1-1 3-1c0 1 1 1 2 2 0-1 0-1 1-1h4 1v-1c-2-1-6-1-8-1-1 0-2 1-2 1-1-1-1-1-2-1-1-1-2-1-2-1v-1l5-1 4-1c3 0 6 0 8 1 2-1 1-1 1-2 1-1 2-1 2-2l-1-1-2-1c1-2 1-2 3-2h2z" class="a"></path><path d="M328 225h1v2h-1l-1-1 1-1z" class="N"></path><path d="M324 221c1-2 1-2 3-2 0 2 0 2-1 3l-2-1z" class="D"></path><path d="M329 219h3l-2 2 2 2v1c-1 0-3 0-4-1 0-2 0-2 1-4z" class="B"></path><path d="M334 228l2 2c1 2 2 2 4 3v2c-2 0-2 0-3-1l-1-1c-1 0-2-1-3-2 0-1 0-2 1-3z" class="g"></path><path d="M336 224h1 1l1 1h0l2-1 2 3h0l-2 1c1 1 1 2 2 2-2 0-3-1-5-1h0-2v1l-2-2h0c-2 0-3-1-4-2h1 3c1 1 1 1 2 1v-1l-2-1c1 0 1-1 2-1z" class="B"></path><path d="M336 229h2 0c2 0 3 1 5 1-1 0-1-1-2-2l2-1c1 2 1 3 3 4l1-1c1 1 1 2 2 3-1 3 0 5 2 7v1c0 1 0 1-1 2l2 2c-1 2-1 2-1 3h0-2l-1-3-5-5c-1 0-1-1-3-1l-2-1 2-2h2 1-3v-1-2c-2-1-3-1-4-3v-1z" class="D"></path><path d="M346 236c1 1 2 2 2 3-1 1 0 2 0 4-1 0-1 0-2-1v-6z" class="H"></path><path d="M348 239c1 1 2 2 2 4l2 2c-1 2-1 2-1 3h0l-1-3c-1-2-1-2-2-2 0-2-1-3 0-4z" class="O"></path><path d="M343 236h1l1 2v1c0 1-1 1-2 1s-1-1-3-1l-2-1 2-2h2 1z" class="e"></path><path d="M336 229h2 0c2 0 3 1 5 1-1 0-1-1-2-2l2-1c1 2 1 3 3 4l1-1c1 1 1 2 2 3-1 3 0 5 2 7v1c0 1 0 1-1 2 0-2-1-3-2-4 0-1-1-2-2-3-2-3-6-5-10-7z" class="U"></path><path d="M316 226c3 0 6 0 8 1 5 2 11 6 14 10 0 1-1 1-1 2v1l1 3c0 2-3 4-4 5-1 0 0 0-1-1h0c1-2 1-2 2-3h1v-2h-1c-3-2-4-4-7-5 0-1-1-2-1-3h-1l-1-1h-1c-1 0-2-1-3-1v-1c-2-1-6-1-8-1-1 0-2 1-2 1-1-1-1-1-2-1-1-1-2-1-2-1v-1l5-1 4-1z" class="L"></path><path d="M321 231c2 1 4 1 6 2 0 0 1 1 2 1 3 1 6 4 8 6l1 3c0 2-3 4-4 5-1 0 0 0-1-1h0c1-2 1-2 2-3h1v-2h-1c-3-2-4-4-7-5 0-1-1-2-1-3h-1l-1-1h-1c-1 0-2-1-3-1v-1z" class="i"></path><path d="M316 226c3 0 6 0 8 1 5 2 11 6 14 10 0 1-1 1-1 2-2-2-3-3-6-5-1-1-2-2-2-3h-1c-2 0-3 0-5-1-1-1-1 0-2 0l-1-1c-2-1-5 0-6 0l-2-2 4-1z" class="Q"></path><path d="M338 237v1l2 1c2 0 2 1 3 1l5 5-2 3v2h0c0 1 0 2-1 3-1 0-2-1-4-1l-5 4-2-2c0-1 0-1-1-2-1 1-1 1-2 1h-1c-1 1-2 1-2 1h-1c-1 1-3 1-4 1-2-1-3-1-4-3l-1-3h1c1 1 2 2 2 3h2c1 0 1 1 2 1 2-1 2-1 3-1s5-3 6-4 4-3 4-5l-1-3v-1c0-1 1-1 1-2z" class="H"></path><path d="M345 247l1 1v2h0c0 1 0 2-1 3-1 0-2-1-4-1l4-5z" class="P"></path><path d="M334 249l7-6 2 1c0 1-1 2-2 3l-1 1c-1 0-1 0-2 1h-4z" class="C"></path><path d="M338 237v1l2 1c2 0 2 1 3 1l5 5-2 3-1-1c0-1 0-2-1-4l-1 1-2-1-7 6c-2 2-4 3-6 5h-1c-1 1-3 1-4 1-2-1-3-1-4-3l-1-3h1c1 1 2 2 2 3h2c1 0 1 1 2 1 2-1 2-1 3-1s5-3 6-4 4-3 4-5l-1-3v-1c0-1 1-1 1-2z" class="b"></path><path d="M318 237c2-1 1-2 3-4l1 2h2c1 1 2 1 4 2 3 1 4 3 7 5h1v2h-1c-1 1-1 1-2 3h0c1 1 0 1 1 1-1 1-5 4-6 4s-1 0-3 1c-1 0-1-1-2-1h-2c0-1-1-2-2-3h-1l1 3v1c-1 1-1 1-1 2h0-2v-1c-1-2 0-2 0-4l-1-2v-4l1-3v-2l1-1 1-1z" class="N"></path><path d="M318 237l1 1c-1 2-2 2-3 3v-2l1-1 1-1z" class="g"></path><path d="M318 241c1-1 1-1 3-2 1 0 2 0 3 1l-1 1v1h-3c-1 0-1 0-2-1z" class="e"></path><path d="M324 240l1 1v1h-1c-2 0-2 1-3 1-2 2-2 3-3 4v2l1 3v1c-1 1-1 1-1 2h0-2v-1c-1-2 0-2 0-4l-1-2v-4l3-3c1 1 1 1 2 1h3v-1l1-1z" class="g"></path><path d="M335 242h1v2h-1c-1 1-1 1-2 3h0c1 1 0 1 1 1-1 1-5 4-6 4s-1 0-3 1c-1 0-1-1-2-1h-2c0-1-1-2-2-3h-1v-2c1-1 1-2 3-4l1 1 1 1-2 1 3 4c2 0 1-1 2-2h2c1 0 2-3 3-4s2-1 3-2h1z" class="C"></path><path d="M303 242v-4h0v-1h0v-1c1-1 0-2 1-3h2c1-1 2-1 3-1h1s1-1 3-1c0 1 1 1 2 2 0-1 0-1 1-1h4 1c1 0 2 1 3 1h1l1 1h1c0 1 1 2 1 3-2-1-3-1-4-2h-2l-1-2c-2 2-1 3-3 4l-1 1-1 1v2l-1 3v4l1 2c0 2-1 2 0 4v1h2 0c0-1 0-1 1-2v-1c1 2 2 2 4 3 1 0 3 0 4-1h1s1 0 2-1h1c1 0 1 0 2-1 1 1 1 1 1 2l2 2-7 6h0c-2 0-2 0-3 1h-2c0 1-2 0-2-1l-1 1-1-3h-1c1 2 1 4 2 6l-2 2-1-1v-3h-2-2-1 0l-2-2c-1-1-1-1-2-3 0-3-3-3-3-6l-2-6c0-1-1-1-1-1v-1-3z" class="D"></path><path d="M312 247c-1-1-1-2-1-2 1-2 1-2 2-2v1h0v2l-1 1z" class="W"></path><path d="M303 245l1-1s1 0 2-1c0 1 1 2 1 3v2c2 4 3 7 5 10v-2h2 0l1 4-1 1h1c0 2 0 2-1 3h-1 0l-2-2c-1-1-1-1-2-3 0-3-3-3-3-6l-2-6c0-1-1-1-1-1v-1z" class="C"></path><path d="M313 239c-1-1-1-1-1-2s-1-2-1-3h0v-1h3 4c0 1-2 3-2 4l1 1-1 1v2l-1 3v4l1 2c0 2-1 2 0 4v1h2 0c0-1 0-1 1-2v-1c1 2 2 2 4 3 1 0 3 0 4-1h1s1 0 2-1h1c1 0 1 0 2-1 1 1 1 1 1 2l2 2-7 6h0c-2 0-2 0-3 1h-2c0 1-2 0-2-1l-1 1-1-3h-1c1 2 1 4 2 6l-2 2-1-1v-3h-2-2c1-1 1-1 1-3s1-6 0-7l-2-2 1-1h0l-1-2v-1h0v-1h-1l1-1v-2h0v-1-2c0-1-1-1 0-2z" class="O"></path><path d="M313 239l2 4c-1 1-1 0-2 1v-1-2c0-1-1-1 0-2z" class="B"></path><path d="M315 261l1 1 1-1c0-1 0-2 1-4h0c1 1 1 2 0 3v4h-2-2c1-1 1-1 1-3z" class="H"></path><path d="M320 257c2 1 3 1 4 3h0l-2 2-1 1-1-3c1-1 0-2 0-3z" class="B"></path><path d="M318 257l1-1 1 1c0 1 1 2 0 3h-1c1 2 1 4 2 6l-2 2-1-1v-3-4c1-1 1-2 0-3z" class="R"></path><path d="M330 253h1c1 0 1 0 2-1 1 1 1 1 1 2l2 2-7 6h0c-2 0-2 0-3 1h-2c0 1-2 0-2-1l2-2h1c2 0 2-1 4-3 0-1 0-2 1-3v-1z" class="N"></path><path d="M258 191h9 0l2 2-2 2c3-1 5-2 8-2h1s1-1 2-1c1 1 2 1 4 2h1c3 0 6 0 9 1h1c1 1 2 1 3 2h1l4 4c2 2 3 4 4 6v1l-3-2v1h0c2 2 2 3 3 5 0 1 0 2-1 3v1l1 3 2 1v6 2 1s1 0 2 1c1 0 1 0 2 1 0 0 1-1 2-1 2 0 6 0 8 1v1h-1-4c-1 0-1 0-1 1-1-1-2-1-2-2-2 0-3 1-3 1h-1c-1 0-2 0-3 1h-2c-1 1 0 2-1 3v1h0v1h0v4c0-1-1-1-1-2v-2l-1-1v-1h-1c-2 1-2 1-3 2-1 2-3 2-4 3-1 0-2 1-2 1l-2 1h-4c0-1-1-1-2-2-2-1-4-3-7-5 0 0-1 2-1 3l-12-14v1h1v-1-1l-3-3c1-2 0-1 1-2h0c-1-2-1-4-2-6l-1-4c0 1-1 2-1 3-1 0-2-1-3-1h-1c-2-2-3-4-5-6l-3-3c1-1 1-2 1-4 0-1-1-2-1-4l1-1c1 0 1-1 2-2h6 3z" class="g"></path><path d="M270 214l2 2-1 2-1 1-2-2c1-1 1-2 2-3zm-1 8h0c2-1 2-1 3-2h0c2 1 2 2 4 2l3-1c1 1 1 1 2 1h-3l-1 1v1 2h-2c-2-1-3-1-5-2l-1-2z" class="W"></path><path d="M277 220c3 0 4-1 6-2 1 1 1 1 1 2 2 1 2-1 4 1-3 4-6 4-11 5v-2-1l1-1h3v1h1v-2c-1-1-3-1-5-1z" class="D"></path><path d="M267 195c3-1 5-2 8-2h1s1-1 2-1c1 1 2 1 4 2-3 1-6 1-9 3v-1h1v-1c-1 0-4 0-5 1-1 0-3 3-4 4-1 0-1 1-1 2l-1 1c-1 3 0 6-1 9-1-2-1-3-1-5 0-5 2-9 6-12z" class="O"></path><path d="M268 205l1-3h2v1c0 1 0 2-1 4h-1c1 2 2 4 2 6h2l1 1 2 2c2 1 3 1 4 1 2-1 3-1 5-1l-2 2c-2 1-3 2-6 2-2-1-4-2-5-4l-2-2c-2-3-2-5-2-9z" class="d"></path><path d="M268 205l1-3h2v1l-2 2h-1z" class="x"></path><path d="M265 219l4 3 1 2c2 1 3 1 5 2h2c5-1 8-1 11-5h0l1 1c-2 1-3 4-6 4l-2 2h-3c-2 0-3 0-5 1h-2 0l-2-1c-1-3-4-6-6-8v-1h2z" class="n"></path><path d="M270 224c2 1 3 1 5 2h0c-2 1-3 1-5 1-1-2 0-2 0-3zm-10-22c1-1 0-1 1-1v-2c2-3 4-5 6-8l2 2-2 2c-4 3-6 7-6 12 0 2 0 3 1 5 0 2 2 5 3 7h-2v1c2 2 5 5 6 8l2 1c1 1 2 3 3 4v1h1l1 2s-1 2-1 3l-12-14v1h1v-1-1l-3-3c1-2 0-1 1-2h0c-1-2-1-4-2-6l-1-4c0-2 1-5 1-7z" class="K"></path><path d="M284 203l2 1c2 1 1 0 3 1 0 0 0 1 1 2v2c1 1 2 2 2 3v4c-1 1-1 2-1 2 0 1-1 2-1 3l-1 1-1-1h0c-2-2-2 0-4-1 0-1 0-1-1-2l2-2c-2 0-3 0-5 1-1 0-2 0-4-1l-2-2-1-1h-2c0-2-1-4-2-6h1c0 1 1 1 2 1s2-1 3-3l1 1c1 0 0 0 1-1h1c1-1 3-1 4-1h1l1-1z" class="f"></path><path d="M279 205c2 1 2 0 4 2 0 2 0 2-1 2l-2 1-1-1v-4z" class="g"></path><path d="M280 217c1-1 2-2 2-3 1-1 1-2 2-4l3 1v3l-2 2c-2 0-3 0-5 1z" class="I"></path><path d="M274 214v-2c0-1 0-2 2-3l1 1h1l2 4v1c-1 1-2 1-4 1l-2-2z" class="e"></path><path d="M278 205h1v4 1c-2-1-2-1-3-1-2 1-2 2-2 3v2l-1-1h-2c0-2-1-4-2-6h1c0 1 1 1 2 1s2-1 3-3l1 1c1 0 0 0 1-1h1z" class="L"></path><path d="M287 211h0l-1-1c0-2-1-3-2-4h-1l1-1h1c1 1 3 3 4 5l1-1c1 1 2 2 2 3v4c-1 1-1 2-1 2 0 1-1 2-1 3l-1 1-1-1h0c-2-2-2 0-4-1 0-1 0-1-1-2l2-2 2-2v-3z" class="U"></path><path d="M287 214h1v1l1-1c1 1 1 2 1 3s-1 3-2 4h0c-2-2-2 0-4-1 0-1 0-1-1-2l2-2 2-2z" class="F"></path><path d="M258 191h9 0c-2 3-4 5-6 8v2c-1 0 0 0-1 1 0 2-1 5-1 7 0 1-1 2-1 3-1 0-2-1-3-1h-1c-2-2-3-4-5-6l-3-3c1-1 1-2 1-4 0-1-1-2-1-4l1-1c1 0 1-1 2-2h6 3z" class="M"></path><path d="M252 205c1-1 2-1 4-1 0 1 1 1 2 2l-1 1 1 1-1 1c-2 0-1-1-3-1l-1-1-1-2z" class="D"></path><path d="M254 197l1-1 1 2c-1 2-4 2-4 4 1 1 2 1 4 1v1c-2 0-3 0-4 1h0l-1-1v-2c-1-1-1-1-1-2l4-2h1l-1-1z" class="T"></path><path d="M258 191h9 0c-2 3-4 5-6 8v2c-1 0 0 0-1 1l-1 1c-1 1-1 1-2 0h-1c-2 0-3 0-4-1 0-2 3-2 4-4l-1-2-1 1c-1-2-2-2-2-4 1-1 2-1 3-2h0 3z" class="i"></path><path d="M261 192l1 3c-1 1-1 1-2 1l-2-1c1-1 2-1 3-1v-2z" class="C"></path><path d="M255 191h3v1h1 2v2c-1 0-2 0-3 1-1-1-1-2-3-2v1 2l-1 1c-1-2-2-2-2-4 1-1 2-1 3-2h0z" class="B"></path><path d="M255 194c1 1 2 1 3 2v3c0 1 0 1 1 2h0v1l-1 1h-1-1c-2 0-3 0-4-1 0-2 3-2 4-4l-1-2v-2z" class="H"></path><path d="M283 194c3 0 6 0 9 1h1c1 1 2 1 3 2h1l4 4c2 2 3 4 4 6v1l-3-2v1l-2-2h-1-1l-1-1c-1-1-2 0-3 0-1-1-2-1-3-2-1 0-1 0-1-1h-1l-1 2 1 1v1c-2-1-1 0-3-1l-2-1-1 1h-1c-1 0-3 0-4 1h-1c-1 1 0 1-1 1l-1-1c-1 2-2 3-3 3s-2 0-2-1c1-2 1-3 1-4v-1h-2c1-2 3-4 4-5 3-2 6-2 9-3h1z" class="M"></path><path d="M275 205c0-1-1-1-1-2 1-1 1-1 1-2 1 0 1 1 2 1h3v1c-2 1-3 0-4 2h0 1c-1 1 0 1-1 1l-1-1z" class="S"></path><path d="M293 195c1 1 2 1 3 2l-1 3-2-2c-1 0-2 0-3 1v-2h-4v-1h6l1-1z" class="p"></path><path d="M284 203c-1-1-1-2 0-3l1 1c2-1 3 0 4 0l-1 2 1 1v1c-2-1-1 0-3-1l-2-1z" class="C"></path><path d="M296 197h1l4 4c2 2 3 4 4 6v1l-3-2h0c-2-2-4-5-7-6l1-3z" class="n"></path><path d="M283 194c3 0 6 0 9 1h1l-1 1h-6v1c-1 0-3-1-3 0-1 0-2 1-2 1-1 0-1-1-3 0h0c-1 0-3 0-3 1-1 1-3 2-4 3h-2c1-2 3-4 4-5 3-2 6-2 9-3h1z" class="E"></path><path d="M289 201h1c0 1 0 1 1 1 1 1 2 1 3 2 1 0 2-1 3 0l1 1h1 1l2 2h0c2 2 2 3 3 5 0 1 0 2-1 3v1l1 3 2 1v6 2 1s1 0 2 1c1 0 1 0 2 1 0 0 1-1 2-1 2 0 6 0 8 1v1h-1-4c-1 0-1 0-1 1-1-1-2-1-2-2-2 0-3 1-3 1h-1c-1 0-2 0-3 1h-2c-1 1 0 2-1 3v1h0v1h0v4c0-1-1-1-1-2v-2l-1-1v-1h-1c-2 1-2 1-3 2-1 2-3 2-4 3-1 0-2 1-2 1l-2 1h-4c0-1-1-1-2-2-2-1-4-3-7-5l-1-2h-1v-1c-1-1-2-3-3-4h0 2c2-1 3-1 5-1h3l2-2c3 0 4-3 6-4l1-1c0-1 1-2 1-3 0 0 0-1 1-2v-4c0-1-1-2-2-3v-2c-1-1-1-2-1-2v-1l-1-1 1-2z" class="O"></path><path d="M283 236l1 1c2 1 4 1 6 0 0-1 0-1 1-2h1c1 1 1 1 1 3-2 1-3 2-5 2-1 1-2 0-3 0l-1-1-1-3z" class="D"></path><path d="M290 207l2-1 1 1c0 3-1 5 0 8 0 1-1 2-1 3h0l2 2h1v-1c0-1 0-1-1-2 1 0 2 0 2 1 1 1 1 1 1 2l-1 1c0 1-1 2-1 3h-3c-1 1-1 2-3 3 0-1 1-3 2-4h0c0-1-1-1-1-2h0c0-1 1-2 1-3 0 0 0-1 1-2v-4c0-1-1-2-2-3v-2z" class="B"></path><path d="M289 201h1c0 1 0 1 1 1 1 1 2 1 3 2 1 0 2-1 3 0l1 1h1 1l2 2h0v1c0 1 1 4 1 5-1 1-2 3-2 4-1-2 0-6-1-8h-2l-1 1v-1c0-2 0-2 1-3h-1c-1 1-2 1-3 1h-1l-1-1-2 1c-1-1-1-2-1-2v-1l-1-1 1-2z" class="D"></path><path d="M294 204c1 0 2-1 3 0l1 1h1 1l2 2h0v1c0 1 1 4 1 5-1 1-2 3-2 4-1-2 0-6-1-8h-2 0v-1l2-2h0c-2-1-2-1-4 0h0l-2-2z" class="C"></path><path d="M302 207c2 2 2 3 3 5 0 1 0 2-1 3v1l1 3 2 1v6c-2 1-3 2-5 3-1 0-1 1-2 1l-3 1h0c0-1 0-1 1-2 0-1 1-2 1-4 0-1 1-1 2-3 0-1 0-1-1-2v-1l2-1-1-1c0-1 1-3 2-4 0-1-1-4-1-5v-1z" class="i"></path><path d="M298 229h1c1-1 1-1 2-1 3-1 1-2 3-4 0-2-1-2-1-3l1-2-1-1 1-2 1 3 2 1v6c-2 1-3 2-5 3-1 0-1 1-2 1l-3 1h0c0-1 0-1 1-2z" class="I"></path><path d="M307 226v2 1s1 0 2 1c1 0 1 0 2 1 0 0 1-1 2-1 2 0 6 0 8 1v1h-1-4c-1 0-1 0-1 1-1-1-2-1-2-2-2 0-3 1-3 1h-1c-1 0-2 0-3 1h-2c-1 1 0 2-1 3v1h0v1h0v4c0-1-1-1-1-2v-2l-1-1v-1h-1c-2 1-2 1-3 2-1 2-3 2-4 3-1 0-2 1-2 1-1-1-2-1-3-2 2 0 3-1 5-2 0-2 0-2-1-3l2-1h2l1-1v-2h0l3-1c1 0 1-1 2-1 2-1 3-2 5-3z" class="C"></path><path d="M297 231h0l3-1c1 0 1 0 2 2-2 1-4 3-6 4l-1 1c-1-1-1-1-1-2v-1h2l1-1v-2z" class="B"></path><path d="M290 221h0c0 1 1 1 1 2h0c-1 1-2 3-2 4v1s-1 0-1 1c-1 1 0 2-1 4h-1l-1-1h-1l-1 1-1 2 1 1 1 3 1 1c1 0 2 1 3 0 1 1 2 1 3 2l-2 1h-4c0-1-1-1-2-2-2-1-4-3-7-5l-1-2h-1v-1c-1-1-2-3-3-4h0 2c2-1 3-1 5-1h3l2-2c3 0 4-3 6-4l1-1z" class="F"></path><path d="M271 229h0 2c2-1 3-1 5-1v2c-1 0-3 0-3 1 1 1 0 1 1 1v1c-1 1-1 0-1 1h-1v-1c-1-1-2-3-3-4z" class="L"></path><path d="M284 239c-1 0-2 0-3-1v-2h-1c-1-1-1-1-1-3 1 0 1-1 2-1h2v-1c1-1 2-1 3-2v-1l2 1c-1 1 0 2-1 4h-1l-1-1h-1l-1 1-1 2 1 1 1 3z" class="B"></path><path d="M249 205c2 2 3 4 5 6h1c1 0 2 1 3 1 0-1 1-2 1-3l1 4c1 2 1 4 2 6h0c-1 1 0 0-1 2l3 3v1 1h-1v-1l12 14c0-1 1-3 1-3 3 2 5 4 7 5 1 1 2 1 2 2h4l2-1s1-1 2-1c1-1 3-1 4-3 1-1 1-1 3-2h1v1l1 1v2c0 1 1 1 1 2v3 1s1 0 1 1l2 6c0 3 3 3 3 6 1 2 1 2 2 3l2 2h0 1 2 2v3l-2 1 1 1c0 1-1 2 0 2v1c1 1 3 2 3 3 1 2 2 3 4 5v1l1 1 1 1 12 8c1 2 3 3 4 5l1 1c-1 1-1 3-1 5h-1v1 1c1 3 1 3 2 5l-2 5c-1 3-1 5-2 8l-1 1v1l2 2 1-2h2c-1 1-2 3-3 4l-10 15-2 2c-1 1-1 2-2 4l-1 2-3 5-2 3h0c-1 1-2 1-2 1l-1 1h-2c-1 0-2 2-3 2h-1l1-2-1-1-1 1c-2 0-2 1-4 0l1-2c0-2-1-2-1-3l-3 1-3-3h-1v-1l-1-1h1c-2-2-2 0-4-1l-1-1-2 1c-1-1-1-2-2-3l2-1h0l1-1c3-3 4-5 3-10v-1c-1-2-1-3-2-5l-1-1-4-7c-1-1-1-2-1-3v1c-1 0-2-1-3-1l5-2c1-2 2-3 3-5 2 0 2 0 4-1 1-1 2-1 4-2h-1v-1-1c-1 0-1 0-1 1l-3-1 2-1 8-4 3-1c-1-1-3-2-4-3l-1-1c-2-1-3-2-4-4l1-1v-2l1-1-2-1h-2l-1 1c-2-3-3-7-4-10 0-2 0-3-1-4h-1v2h-1l-1-1c1-1 1-2 0-4v-1s0-1-1-1l-3-3-1-2v2l-2 2c-1 2-1 2-3 4 0 0-1 0-2 1l-1-1h0-3v1l-4-5h-2l-2-1-1-1v-1c0-2 0-3 1-4l-1-2c-2 1-2 3-3 4l1-9v-8l-1-9h-1v-4c1-2-2-7-3-9-1-1-2-3-2-4-2-3-3-5-5-7h1l-3-3c-1-1-1-2-2-3s-2-2-2-3l4 4 1-1-1-2 1-1z" class="e"></path><path d="M300 271c0-1 0-1 1-2l1-1 3 2c-1 0-2 0-3 1h-2 0zm-5-21h1v1c0 1-1 1-1 2v1h-1-1c0-2 1-3 2-4z" class="v"></path><path d="M279 255c1 1 3 2 4 3l-1 2 1 1h0c-2-1-4-4-4-6z" class="W"></path><path d="M297 238h2c1 0 1 0 1 1-1 0-1 0-2 1 0 2-1 3 0 5h-2c-2 1-3 1-5 1l-1 3h-1l1-3c0-1-1-1-1-2v-1l2-1s1-1 2-1c1-1 3-1 4-3z" class="d"></path><path d="M291 246l6-6h1c0 2-1 3 0 5h-2c-2 1-3 1-5 1z" class="W"></path><path d="M276 236c3 2 5 4 7 5 1 1 2 1 2 2h4v1c0 1 1 1 1 2l-1 3c-6-3-10-6-14-10 0-1 1-3 1-3z" class="Y"></path><path d="M271 247l8 8c0 2 2 5 4 6h0l2 1v1c0 1 0 1 1 1l4 4v2 1l-1 1v-1s0-1-1-1l-3-3-1-2-6-7-3 1-3-2-2-2c1-1 1-2 2-3v-1c0-1-1-2-1-4z" class="D"></path><path d="M272 252l6 6-3 1-3-2-2-2c1-1 1-2 2-3z" class="c"></path><path d="M249 205c2 2 3 4 5 6h1c1 0 2 1 3 1 0-1 1-2 1-3l1 4c1 2 1 4 2 6h0c-1 1 0 0-1 2l3 3v1 1h-1v-1c-1 0-3-3-4-3-4-4-7-9-11-13l1-1-1-2 1-1z" class="h"></path><path d="M259 209l1 4c0 1 0 2-1 3l-5-5h1c1 0 2 1 3 1 0-1 1-2 1-3z" class="R"></path><path d="M257 225c3 2 6 7 8 10 4 5 8 9 12 13 2 1 8 5 9 7-3 0-6-3-8-5l-8-7h0c0 2 1 3 1 4 0 2 1 3 1 4l-4-5c-2-2-4-7-7-8h-1v-4c1-2-2-7-3-9z" class="H"></path><g class="J"><path d="M261 238c3 1 5 6 7 8l4 5v1c-1 1-1 2-2 3l-1-1c-1 1-1 1-3 2h-1c0 1 0 1 1 2h-1-1c-1 1-1 0 0 1v1c-2 1-2 3-3 4l1-9v-8l-1-9z"></path><path d="M268 246l4 5v1c-1 1-1 2-2 3l-1-1-2-3c1 0 1-1 2-1l-2-1c0-1 1-2 1-3z"></path></g><path d="M262 247l1 1h2l1 1 1 2 2 3c-1 1-1 1-3 2h-1c0 1 0 1 1 2h-1-1c-1 1-1 0 0 1v1c-2 1-2 3-3 4l1-9v-8z" class="G"></path><path d="M266 256v-1l-1-1c0-2 0-3 1-5l1 2 2 3c-1 1-1 1-3 2z" class="Q"></path><path d="M286 264h1c1-1 0-2 0-3l1-1 1 1h1c-1-2-1-3-1-4 1-1 2-1 4-1 0 1 0 1 2 2-1 2-1 3 0 5 1 3 2 5 4 7l1 1h0l-1 1 1 2c-1 1-1 2-1 4l1 1-1 1-1-1h-2v1l-3-1c0-2 0-3-1-4h-1v2h-1l-1-1c1-1 1-2 0-4v-1 1l1-1v-1-2l-4-4z" class="N"></path><path d="M299 270h-2-1v-1c-3-1-3-2-5-4 0-1 0-3 1-4h1l1 1-2 1 1 1 2-1c1 3 2 5 4 7z" class="a"></path><path d="M269 254l1 1 2 2 3 2 3-1 6 7v2l-2 2c-1 2-1 2-3 4 0 0-1 0-2 1l-1-1h0-3v1l-4-5h-2l-2-1-1-1v-1c0-2 0-3 1-4l-1-2v-1c-1-1-1 0 0-1h1 1c-1-1-1-1-1-2h1c2-1 2-1 3-2z" class="f"></path><path d="M267 266l2 3h-2l-2-1 2-2z" class="G"></path><path d="M265 262l2 4-2 2-1-1v-1c0-2 0-3 1-4z" class="E"></path><path d="M269 259c2-1 2-1 3-2l3 2v1l-1 1c-1-1-1-1-2-1-2 0-2-1-3-1z" class="L"></path><path d="M269 254l1 1 2 2c-1 1-1 1-3 2l-1-1h-1l2 3-1 1c-2 0-2-2-3-3h-1c-1-1-1 0 0-1h1 1c-1-1-1-1-1-2h1c2-1 2-1 3-2z" class="I"></path><path d="M274 261l1-1 2 2-1 1h0l-3 6v1c-1-2-5-5-5-7l2-1 1 1c2-1 2-1 3-2z" class="L"></path><path d="M274 261l1-1 2 2-1 1h0c-1 1-2 1-2 2-1 0-1 1-2 0l1-2h-2c2-1 2-1 3-2z" class="R"></path><path d="M278 258l6 7v2l-2 2c-1 2-1 2-3 4 0 0-1 0-2 1l-1-1h0l-3-3v-1l3-6h0l1-1-2-2v-1l3-1z" class="p"></path><path d="M279 273c0-1-1-1-2-2-1 0-2-1-2-3 0 0 0-1 1-1 0-1 2-2 3-2 2 1 2 2 3 4-1 2-1 2-3 4zm18-35c1-1 1-1 3-2h1v1l1 1v2c0 1 1 1 1 2v3 1s1 0 1 1l2 6c0 3 3 3 3 6 1 2 1 2 2 3l2 2h0 1 2 2v3l-2 1 1 1c0 1-1 2 0 2v1c1 1 3 2 3 3 1 2 2 3 4 5v1l1 1 1 1 12 8c-1 2-1 4-3 5l-2-1v5l-3 3-2 3-4 4-1 1c-3 1-4 4-7 5l-3 2h-1l-4 1c0 1-2 1-3 1h-1s-2 1-2 2l-1-1h-2c0 1 0 2-1 3l-6-4c-1-1-2-1-4-1v1c-1 0-2-1-3-1l5-2c1-2 2-3 3-5 2 0 2 0 4-1 1-1 2-1 4-2h-1v-1-1c-1 0-1 0-1 1l-3-1 2-1 8-4 3-1c-1-1-3-2-4-3l-1-1c-2-1-3-2-4-4l1-1v-2l1-1-2-1h-2l-1 1c-2-3-3-7-4-10l3 1v-1h2l1 1 1-1-1-1c0-2 0-3 1-4l-1-2 1-1c2 3 5 5 7 7 1 1 3 1 4 1 0 1 0 1 1 1h2l2 1c1-1 2-1 2-2l-2-1c-1 0-1-1-2-1l-1-2c-2-2-6-4-8-6-1-1-1-2-2-4-1-1-2-4-3-7h0v-4-1c-2-2-2-6-2-8-1-2 0-3 0-5 1-1 1-1 2-1 0-1 0-1-1-1h-2z" class="a"></path><path d="M320 284c-1 0 0 0-1-1l1-1h2c-1 1-1 2-2 2z" class="e"></path><path d="M321 287c2 0 3 1 5 2-1 1-2 1-3 2h-2c0-1 0-2-1-3l1-1z" class="K"></path><path d="M300 253v-1l1 1v-2l1 1c0 1 0 1 1 0l2 5v3l-1 2-4-8v-1z" class="j"></path><path d="M305 257c1 1 1 2 2 3 1 2 2 4 4 6 0 1 0 2-1 3h0c-3-2-5-5-6-7l1-2v-3z" class="E"></path><path d="M311 266c2 2 3 4 6 5v1c1 1 3 2 3 3 1 2 2 3 4 5v1c-5-4-10-7-14-12h0c1-1 1-2 1-3z" class="K"></path><path d="M297 238c1-1 1-1 3-2h1v1l1 1v2c0 1 1 1 1 2v3 1s1 0 1 1l2 6c0 3 3 3 3 6 1 2 1 2 2 3l2 2h0 1 2 2v3l-2 1 1 1c0 1-1 2 0 2-3-1-4-3-6-5s-3-4-4-6c-1-1-1-2-2-3l-2-5c-1 1-1 1-1 0l-1-1v2l-1-1v1c-2-2-2-6-2-8-1-2 0-3 0-5 1-1 1-1 2-1 0-1 0-1-1-1h-2z" class="f"></path><path d="M313 264h1 2c0 1-1 3 0 4h-1c-1-1-2-3-2-4z" class="T"></path><path d="M316 264h2v3l-2 1h0c-1-1 0-3 0-4z" class="B"></path><path d="M300 239c0 2 0 4 1 6 0 2 2 6 2 7-1 1-1 1-1 0l-1-1v2l-1-1v1c-2-2-2-6-2-8-1-2 0-3 0-5 1-1 1-1 2-1z" class="E"></path><path d="M325 282l1 1 12 8c-1 2-1 4-3 5l-2-1-2-1h0l-1-1c-2 1-3 3-4 4l-1 1c-3 0-4 2-7 2v2h-3c1-1 3-2 3-4v-1h0l6-3-1-2h1l-1-1c1-1 2-1 3-2l1 1c0-1 0-2-1-2-2-3-3-4-6-4 1 0 1-1 2-2 0 1 0 0 1 1l2-1z" class="O"></path><path d="M326 289l1 1c0 1 1 1 2 2l-5 2-1-2h1l-1-1c1-1 2-1 3-2z" class="b"></path><path d="M325 282l1 1c0 2 1 3 2 4 1 2 1 4 3 4l1-1h2v1l-2 2-3-1c-1-1-2-1-2-2s0-2-1-2c-2-3-3-4-6-4 1 0 1-1 2-2 0 1 0 0 1 1l2-1z" class="N"></path><path d="M300 274l-1-2 1-1c2 3 5 5 7 7 2 1 3 2 5 3l9 6-1 1c1 1 1 2 1 3h2l1 1h-1l1 2-6 3h0l-9 4c-1-1-3-2-4-3l-1-1c-2-1-3-2-4-4l1-1v-2l1-1-2-1h-2l-1 1c-2-3-3-7-4-10l3 1v-1h2l1 1 1-1-1-1c0-2 0-3 1-4z" class="t"></path><path d="M306 283c2 0 4-1 6-2l9 6-1 1h-1l-1-1c-1 0-2-1-3-2-3-1-2 0-4 0s-2-1-3-1c-2 1-4 1-5 2h-1-3c-1 0-2-1-2-2h2c2 0 5-1 7-1z" class="n"></path><path d="M308 291l-3-3h-1v-1c1-1 3-2 4-2l1 2h4 5l1 1h1c1 1 1 2 1 3h2l1 1h-1v1c-1 0-1 0-2 1l-2-2-1 1-2-1c-1 0-2 1-3 2h0c-1-1-1-1-1-3h-4z" class="i"></path><path d="M313 287h5l1 1h1c1 1 1 2 1 3l-6-1c-1-1-2-2-2-3z" class="S"></path><path d="M300 274l-1-2 1-1c2 3 5 5 7 7 2 1 3 2 5 3-2 1-4 2-6 2s-5 1-7 1h-2c0 1 1 2 2 2l1 2h-2l-1 1c-2-3-3-7-4-10l3 1v-1h2l1 1 1-1-1-1c0-2 0-3 1-4z" class="U"></path><path d="M300 274c1 1 2 1 3 2-1 1-1 1-1 2h1 1v1c-1 1-1 1-2 0l-2 1 1 3h-1c-1-1-2-1-3-1h-1c1-1 1-2 2-3l1 1 1-1-1-1c0-2 0-3 1-4z" class="O"></path><path d="M300 274l-1-2 1-1c2 3 5 5 7 7 2 1 3 2 5 3-2 1-4 2-6 2h-4l1-2c0-1 0-1-1-2 1 1 1 1 2 0v-1h-1-1c0-1 0-1 1-2-1-1-2-1-3-2zm2 15l1 2 1 1 2-1h2 4c0 2 0 2 1 3h0c1-1 2-2 3-2l2 1 1-1 2 2c1-1 1-1 2-1v-1l1 2-6 3h0l-9 4c-1-1-3-2-4-3l-1-1c-2-1-3-2-4-4l1-1v-2l1-1z" class="H"></path><path d="M303 291l1 1 3 3c-2-1-3-1-5-2 1-1 1-1 1-2h0z" class="T"></path><path d="M304 292l2-1c1 1 3 1 4 3 1 0 1 1 2 1h-5l-3-3z" class="i"></path><path d="M308 291h4c0 2 0 2 1 3h0c1-1 2-2 3-2l2 1 1-1 2 2c1-1 1-1 2-1v-1l1 2-6 3h0l-9 4c-1-1-3-2-4-3 1 0 2 1 3 1 3 1 4 0 6-1 1-1 1-1 1-2h-1l-2-1c-1 0-1-1-2-1-1-2-3-2-4-3h2z" class="R"></path><path d="M326 297c1-1 2-3 4-4l1 1h0l2 1v5l-3 3-2 3-4 4-1 1c-3 1-4 4-7 5l-3 2h-1l-4 1c0 1-2 1-3 1h-1s-2 1-2 2l-1-1h-2c0 1 0 2-1 3l-6-4c-1-1-2-1-4-1v1c-1 0-2-1-3-1l5-2c1-2 2-3 3-5 2 0 2 0 4-1 1-1 2-1 4-2h-1v-1-1c-1 0-1 0-1 1l-3-1 2-1 8-4 3-1 9-4v1c0 2-2 3-3 4h3v-2c3 0 4-2 7-2l1-1z" class="F"></path><path d="M324 305l4-4c0 1 1 1 2 2l-2 3-1-2-3 1z" class="P"></path><path d="M331 294l2 1v5l-3 3c-1-1-2-1-2-2 2-2 3-4 3-7z" class="G"></path><path d="M324 305l3-1 1 2-4 4-1 1c-3 1-4 4-7 5l-3 2h-1l-4 1c0 1-2 1-3 1h-1s-2 1-2 2l-1-1h-2c0 1 0 2-1 3l-6-4c-1-1-2-1-4-1v1c-1 0-2-1-3-1l5-2 6-3-2 3 1 1h1c1 1 3 0 4-1l1 1c9-1 17-7 23-13z" class="c"></path><path d="M292 320h3 5l5-2c1 1 2 1 3 1 0 1-2 1-3 1h-1s-2 1-2 2l-1-1h-2c0 1 0 2-1 3l-6-4z" class="E"></path><defs><linearGradient id="AP" x1="305.895" y1="315.987" x2="308.019" y2="303.998" xlink:href="#B"><stop offset="0" stop-color="#2c2e2a"></stop><stop offset="1" stop-color="#4d4949"></stop></linearGradient></defs><path fill="url(#AP)" d="M326 297v3h-1c-1 0-1 1-2 2-5 7-18 10-26 15l-2 1-1-1 2-3-6 3c1-2 2-3 3-5 2 0 2 0 4-1 1-1 2-1 4-2h-1v-1-1c-1 0-1 0-1 1l-3-1 2-1 8-4 3-1 9-4v1c0 2-2 3-3 4h3v-2c3 0 4-2 7-2l1-1z"></path><path d="M315 302h3v-2c3 0 4-2 7-2-2 2-4 3-7 4-2 1-5 3-7 5l-15 7-6 3c1-2 2-3 3-5 2 0 2 0 4-1 1-1 2-1 4-2l14-7z" class="T"></path><path d="M318 297v1c0 2-2 3-3 4l-14 7h-1v-1-1c-1 0-1 0-1 1l-3-1 2-1 8-4 3-1 9-4z" class="o"></path><path d="M338 291c1 2 3 3 4 5l1 1c-1 1-1 3-1 5h-1v1 1c1 3 1 3 2 5l-2 5c-1 3-1 5-2 8l-1 1v1l2 2 1-2h2c-1 1-2 3-3 4l-10 15-2 2c-1 1-1 2-2 4l-1 2-3 5-2 3h0c-1 1-2 1-2 1l-1 1h-2c-1 0-2 2-3 2h-1l1-2-1-1-1 1c-2 0-2 1-4 0l1-2c0-2-1-2-1-3l-3 1-3-3h-1v-1l-1-1h1c-2-2-2 0-4-1l-1-1-2 1c-1-1-1-2-2-3l2-1h0l1-1c3-3 4-5 3-10v-1c-1-2-1-3-2-5l-1-1-4-7c-1-1-1-2-1-3 2 0 3 0 4 1l6 4c1-1 1-2 1-3h2l1 1c0-1 2-2 2-2h1c1 0 3 0 3-1l4-1h1l3-2c3-1 4-4 7-5l1-1 4-4 2-3 3-3v-5l2 1c2-1 2-3 3-5z" class="C"></path><path d="M323 347c1-2 2-3 3-5l2 1c-1 1-1 2-2 3l-3 1z" class="R"></path><path d="M305 351c-1-1-2-1-2-2v-2l1-1v2h1c1 0 2 1 3 2v1h-3z" class="D"></path><path d="M294 330c0-1-1-3 0-4h0v-1-1l2 1s1 0 1 1 1 1 1 2c1 1 1 2 2 3 0 1 1 3 2 5v5c0 2-1 4-2 5v1c-2 1-3 2-4 3l-1 1-1-1-2 1c-1-1-1-2-2-3l2-1h0l1-1c3-3 4-5 3-10v-1c-1-2-1-3-2-5z" class="e"></path><path d="M294 330c0-1-1-3 0-4h0v-1-1l2 1h-1c0 1 1 2 2 3l1 4c-1 2-1 3-2 4v-1c-1-2-1-3-2-5z" class="H"></path><path d="M298 332c0 2 2 6 2 7s-1 4-2 5l-1 1c0 1-1 2-1 3v2l-1 1-1-1-2 1c-1-1-1-2-2-3l2-1h0l1-1c3-3 4-5 3-10 1-1 1-2 2-4z" class="a"></path><path d="M292 347c1 1 2 1 2 3l-2 1c-1-1-1-2-2-3l2-1z" class="N"></path><path d="M323 347l3-1-2 4 1 1-3 5-2 3h0c-1 1-2 1-2 1l-1 1h-2c-1 0-2 2-3 2h-1l1-2-1-1-1 1c-2 0-2 1-4 0l1-2c0-2-1-2-1-3l-3 1-3-3h-1v-1c2-1 4-1 6-2h3l2-1c3-1 7-1 10-3h3z" class="U"></path><path d="M323 347l3-1-2 4c-5 3-10 4-16 6l-1-1c0-2 1-2 3-3 0 1 0 1 1 1 0-1 1-1 2-2h3l5-2c2-1 1-1 2-2z" class="F"></path><path d="M324 350l1 1-3 5-2 3h0c-1 1-2 1-2 1l-1 1h-2c-1 0-2 2-3 2h-1l1-2-1-1-1 1c-2 0-2 1-4 0l1-2c0-2-1-2-1-3h2c6-2 11-3 16-6z" class="J"></path><path d="M311 360c0-1 1-2 2-3h2l3 1 1-2h1 2l-2 3h0c-1 1-2 1-2 1l-1 1h-2c-1 0-2 2-3 2h-1l1-2-1-1z" class="p"></path><path d="M313 318l2 2v2 2c2 2 3 3 5 4h2v1h0c1 1 1 1 2 1l1-1c1 0 2 0 4 1h0c0 1 0 2 1 3l-2 3c1 1 1 2 2 3h-3l-1 1h1c-2 1-2 2-3 2l-1-1 1 2c-1 1-2 1-3 1v1h-2c-1 0-1 0-2 1h-2 0c-2 2-5 0-7 1-2 0-2 0-2-1h1c0-2 0-3-1-4v-3h0c-2-6-4-10-8-15 1-1 1-2 1-3h2l1 1c0-1 2-2 2-2h1c1 0 3 0 3-1l4-1h1z" class="M"></path><path d="M310 332c0-2-1-2-2-4 1-1 1-2 2-2l1 1c1 0 2 1 3 2l-4 2v1z" class="i"></path><path d="M311 327c1 0 2 1 3 2l-4 2c0-2 0-3 1-4z" class="D"></path><path d="M308 335c1 4 1 7 3 10v1c-1 0-2 0-2-1l-2 1h0c0-2 0-3-1-4v-3h0 1c1-1 0-2 1-4z" class="d"></path><path d="M313 318l2 2v2 2 1c-1 1-1 1-2 1v-1c-2-1-3-1-5 0h0-2c0 1 0 1 1 2v2l1 1c-1 1-1 1-1 2 1 1 1 2 1 3h0c-1 2 0 3-1 4h-1c-2-6-4-10-8-15 1-1 1-2 1-3h2l1 1c0-1 2-2 2-2h1c1 0 3 0 3-1l4-1h1z" class="S"></path><path d="M312 321h2l1 1v2 1c-1 1-1 1-2 1v-1c0-2 0-3-1-4z" class="V"></path><path d="M313 318l2 2v2l-1-1h-2c-1 1-2 1-4 1 2-2 3-1 4-4h1z" class="L"></path><path d="M308 319l4-1c-1 3-2 2-4 4-1 1-2 1-3 1l-3-1c0-1 2-2 2-2h1c1 0 3 0 3-1z" class="k"></path><path d="M298 324c1-1 1-2 1-3h2l1 1 3 1-1 1 1 1c0 1 0 2-1 2 2 3 1 6 4 8h0c-1 2 0 3-1 4h-1c-2-6-4-10-8-15z" class="K"></path><path d="M301 321l1 1 3 1-1 1 1 1c0 1 0 2-1 2h0c-1-1-2-2-2-3h0c-1-1-2-1-2-2l1-1z" class="Z"></path><path d="M315 324c2 2 3 3 5 4h2v1h0c1 1 1 1 2 1l1-1c1 0 2 0 4 1h0c0 1 0 2 1 3l-2 3c1 1 1 2 2 3h-3l-1 1h1c-2 1-2 2-3 2l-1-1c-3-1-3 2-5 2-1 1-2 0-2 0h-1c-2-1-4-5-4-7-1-2 0-2-1-3v-1-1l4-2v1h1v-2c1-1 0-2 0-3v-1z" class="H"></path><path d="M322 332c0 2 0 3-1 4h-1c-1-1-1-2-1-3 1-1 1-1 3-1h0z" class="W"></path><path d="M315 324c2 2 3 3 5 4h2v1h0c1 1 1 1 2 1-1 0-1 1-2 2h0l-1-2-2 1c-2-1-2-2-4-3 1-1 0-2 0-3v-1z" class="q"></path><path d="M314 329v1h1l-1 1h0c-1 0-1 0-2 1v1c2 0 2-1 3 0v1h-1l-2 2c0 1 1 2 2 3s2 2 3 2 2-1 3-1c0-1 1-1 1-1l1-1 2 1c0-1 1-2 1-3 1-1 2-2 4-3v-1h-4c1 0 2-1 3-1l1-1h0c0 1 0 2 1 3l-2 3c1 1 1 2 2 3h-3l-1 1h1c-2 1-2 2-3 2l-1-1c-3-1-3 2-5 2-1 1-2 0-2 0h-1c-2-1-4-5-4-7-1-2 0-2-1-3v-1-1l4-2z" class="B"></path><path d="M328 336c1 1 1 2 2 3h-3l-1 1h1c-2 1-2 2-3 2v-1c1-2 3-3 4-5z" class="f"></path><path d="M338 291c1 2 3 3 4 5l1 1c-1 1-1 3-1 5h-1v1 1c1 3 1 3 2 5l-2 5c-1 3-1 5-2 8l-1 1v1l2 2 1-2h2c-1 1-2 3-3 4l-10 15-2 2c-1 1-1 2-2 4l-1 2-1-1 2-4c1-1 1-2 2-3l-2-1c1-1 1-1 1-2h-1l1-1h3c-1-1-1-2-2-3l2-3c-1-1-1-2-1-3h0c-2-1-3-1-4-1l-1 1c-1 0-1 0-2-1h0v-1h-2c-2-1-3-2-5-4v-2-2l-2-2 3-2c3-1 4-4 7-5l1-1 4-4 2-3 3-3v-5l2 1c2-1 2-3 3-5z" class="R"></path><path d="M331 308h2c1 1 1 2 1 4h0c-1 0-2-1-3-1h0-5v-1c1-1 3-1 5-2z" class="O"></path><path d="M337 302h2l1 2c1-1 1 0 1-1v1l-3 11c0-1-1-2-1-3l1-1v-2l-2-1v-1h1c1 0 1-1 2-1h0c1-1-1-2-1-3l-1-1z" class="Q"></path><path d="M333 300l4-1 1 1-1 1c-1 0-1 1-1 1h-1c-1 1-1 2-1 3-1 1-3 1-4 1v1 1h1c-2 1-4 1-5 2v1h0l-2-1 4-4 2-3 3-3z" class="L"></path><path d="M338 291c1 2 3 3 4 5l1 1c-1 1-1 3-1 5h-1v1c0 1 0 0-1 1l-1-2h-2-1s0-1 1-1l1-1-1-1-4 1v-5l2 1c2-1 2-3 3-5z" class="B"></path><path d="M333 295l2 1 6 6v1c0 1 0 0-1 1l-1-2h-2-1s0-1 1-1l1-1-1-1-4 1v-5z" class="d"></path><path d="M323 311l1-1 2 1h0 5l1 2 3 1 1-1v-2h2l-1 1c0 1 1 2 1 3l-2 8c-1 2-1 4-2 5h0l-1 3h-1v-1-1c1-2 3-6 3-8-1 0-2 0-3-1 0-2-1-3-2-5-3-2-3-3-7-3v-1z" class="r"></path><path d="M332 313l3 1c0 1 0 1 1 2-1 1-1 2-2 3l-1-1c0-2 0-3-1-4h-1l1-1z" class="X"></path><path d="M330 315c1 2 2 3 2 5 1 1 2 1 3 1 0 2-2 6-3 8v1c-1 1-1 2-2 3-1-1-1-2-1-3h0c-2-1-3-1-4-1l-1 1c-1 0-1 0-2-1h0v-1h-2c2-1 4-1 5-2 2-2 2-4 2-6h1 1v-4l1-1z" class="M"></path><path d="M325 329c1 0 2-1 3-2l2-2h1v2l1 2v1c-1 1-1 2-2 3-1-1-1-2-1-3h0c-2-1-3-1-4-1z" class="T"></path><path d="M331 327l1 2v1c-1 1-1 2-2 3-1-1-1-2-1-3 1-1 2-1 2-3z" class="q"></path><path d="M341 304c1 3 1 3 2 5l-2 5c-1 3-1 5-2 8l-1 1v1l2 2 1-2h2c-1 1-2 3-3 4l-10 15-2 2c-1 1-1 2-2 4l-1 2-1-1 2-4c1-1 1-2 2-3l-2-1c1-1 1-1 1-2h-1l1-1h3c-1-1-1-2-2-3l2-3c1-1 1-2 2-3v1h1l1-3h0c1-1 1-3 2-5l2-8 3-11z" class="S"></path><path d="M330 339l1-1c0 2-2 4-3 5l-2-1c1-1 1-1 1-2h-1l1-1h3z" class="U"></path><path d="M334 328h0c1-1 1-3 2-5 1 1 1 2 2 3v1c-1 1-2 3-2 4-1 2-2 4-4 6v-1h-1l2-2c1-2 2-2 2-4l-1-2z" class="f"></path><path d="M341 304c1 3 1 3 2 5l-2 5c-1 3-1 5-2 8l-1 1v1l2 2-4 5c0-1 1-3 2-4v-1c-1-1-1-2-2-3l2-8 3-11z" class="F"></path><path d="M316 316c3-1 4-4 7-5v1c4 0 4 1 7 3l-1 1v4h-1-1c0 2 0 4-2 6-1 1-3 1-5 2-2-1-3-2-5-4v-2-2l-2-2 3-2z" class="h"></path><path d="M316 316l1 1h-1c0 1 0 2-1 3l-2-2 3-2z" class="k"></path><path d="M317 322v-1c0-1 1-2 2-3l1 1c0 1 0 3-1 4-1 0-1 0-2-1z" class="B"></path><path d="M324 318h0l2 2-1 2s-1 1-2 1l-2-1c1-2 2-3 3-4z" class="P"></path><path d="M320 319c1 1 1 2 1 3l2 1c1 0 2-1 2-1l1 1c-1 1-2 2-4 2s-3 0-5-2v-1c1 1 1 1 2 1 1-1 1-3 1-4z" class="i"></path><path d="M323 312c4 0 4 1 7 3l-1 1v4h-1-1-1l-2-2h0c-1-1-2-1-2-2 0-2 0-3 1-4z" class="F"></path><path d="M322 316l3-3h1c0 1 1 2 2 3v1h-1c-1 0-2 1-3 1h0c-1-1-2-1-2-2z" class="C"></path></svg>