<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="146 118 752 824"><!--oldViewBox="0 0 1022 1024"--><style>.B{fill:#040302}.C{fill:#32302f}.D{fill:#1c1a19}.E{fill:#696766}.F{fill:#a3a2a2}.G{fill:#1a1917}.H{fill:#949293}.I{fill:#bab8b8}.J{fill:#383735}.K{fill:#d4d3d3}.L{fill:#bab9b9}.M{fill:#090807}.N{fill:#7b7979}.O{fill:#858383}.P{fill:#252322}.Q{fill:#f0efef}.R{fill:#100f0e}.S{fill:#5f5e5d}.T{fill:#4a4847}.U{fill:#0e0b05}</style><path d="M484 165l-2 1c-1 0-2 0-3-1-1-2-1-2-1-4h1l2 2c1 1 2 1 3 2z" class="M"></path><path d="M321 248h1c1 1 2 1 2 3l-2 2h-1c-1 0-2-1-2-2 0-2 0-2 2-3z" class="B"></path><path d="M402 591c-4-2-7-4-10-7 4 1 8 3 12 5v2h-2z" class="M"></path><path d="M385 351h1c2 1 2 1 3 3l-2 2c-1 1-1 1-3 1l-1-2c0-2 0-2 2-4zm-1-198l2-1 2 2-1 1c0 2 0 2-1 3h-2l-1-1c-1-1-1-2-1-2 1-1 1-2 2-2z" class="Q"></path><path d="M700 246h1c1 1 2 1 2 3s0 2-1 3h-2c-1 0-1-1-2-1 0-2 1-3 2-5z" class="D"></path><path d="M401 527c4-3 8-5 13-7l1 1-3 1v1l-9 5c-1 0-1 0-2-1z" class="E"></path><path d="M565 152c1 2 1 2 1 5 0 6 2 10 5 16-1-1-3-2-4-3-3-6-3-12-2-18z" class="B"></path><path d="M401 519c5-4 9-9 14-14-3 6-7 11-12 16l-2-2z" class="C"></path><path d="M414 196c2 2 2 2 2 4 1 5 4 10 8 14l-1 1h0v-1c-2-1-3-1-5-2h-1-1-1l4-4c-2-4-4-7-5-12z" class="D"></path><path d="M441 557h0c2 0 3 0 4 1s2 1 2 3l-2 2h-2c-2 0-2-1-4-3 0-2 1-2 2-3z" class="Q"></path><path d="M382 512c4-2 8-1 12-3h0 1 2v1h0c-4 1-7 2-10 3-3 0-5 1-8 1h-1l2-2h1 1z" class="G"></path><path d="M632 205c2 4 3 8 2 12 0 4-2 8-6 10v1h-1c6-8 5-14 5-23zm-267 24h1c3 6 7 11 12 16-5-2-9-5-12-9-1-2-2-4-1-7zm78-92c6 1 13 4 16 10h0c-2-1-3-2-4-3h0c1 1 2 2 2 3v1c-5-5-9-7-16-10 1 0 1-1 2-1z" class="D"></path><path d="M392 524c3-2 6-3 9-5l2 2c-6 5-14 9-22 11h-1v-1h1c5-2 9-3 13-6h-2v-1z" class="J"></path><path d="M481 163c1-3 2-6 0-8-1-2-2-2-3-2s-2 0-3-1h0v-1c0-1 1-2 1-3 1 1 0 3 1 3 2 0 5 1 6 2 2 2 2 5 2 8 0 1-1 2-1 4-1-1-2-1-3-2z" class="B"></path><path d="M465 141h2v1c3 1 4 2 5 4v1c0 1-1 2-2 2l-1 1c-1-1-1-1-2-1v-1c-1 0-2-1-2-1l-1-1c0-1-1-1-1-2l2-3z" class="Q"></path><path d="M421 371c2 1 8 2 10 1 0-2 1-4 2-6h0l1 3c-1 0-1 1-1 1v1h0c1 2 2 3 3 4h0c2 1 3 2 4 2l2 2v1l-7-3c-1-1-2-2-4-3h-1c-3-1-6-1-10-1h0c1 0 2-1 3-1h-3l1-1z" class="G"></path><path d="M457 148v-1c0-1-1-2-2-3h0c1 1 2 2 4 3h0c3 4 3 8 3 13-1 5-4 8-7 11-2 1-4 2-6 2h0c4-3 8-5 10-10s1-10-2-15z" class="M"></path><path d="M686 526c1 0 2 1 3 1l-33 13 7-5c3-3 11-4 15-5 3-1 6-3 8-4zm-364-69c2 1 4 4 6 6 3 4 6 7 10 9 2 1 5 3 8 2 4-1 8-3 13-1 1 0 1 0 2 1-6-3-12 4-18 2-10-2-16-12-21-19z" class="B"></path><path d="M392 525h2c-4 3-8 4-13 6h-1v1h1c-4 1-9 2-13 2h-7c-1 0-1 0-2-1h2c6-2 11-2 17-3 5-1 9-3 14-5z" class="N"></path><path d="M642 239h0c-3 4-9 5-13 6-2 1-4 2-6 2s-2 0-2-2c0-1 1-3 3-4 3-1 4 1 6 2 4 0 9-4 11-7v1c0 1-1 2-2 3 1-1 2-1 2-2l1 1z" class="B"></path><path d="M625 242c1 0 1 0 2 1v1l-2 2c-1 1-1 1-2 0l-1-1c1-1 1-2 3-3z" class="Q"></path><path d="M419 342h3 0c5 3 10 4 15 7h1c4 3 9 7 12 11l5 6-1 1c-6-8-14-14-23-19l-12-6zm127-189h2l2 2c1 1 1 2 0 3-1 3-2 6-5 7h-1c-1 0-2 0-3-1-1-2-1-4 0-6s3-4 5-5z" class="B"></path><path d="M546 155h2c1 2 1 3 0 4 0 2-1 3-3 4l-1-1c-1-1-1-2-1-3 0-2 1-3 3-4z" class="Q"></path><path d="M440 347c-3-2-7-5-9-8h0c8 5 17 13 23 21v-3c0-3 1-7 4-9-1 2-1 5-1 7-1 5 1 9 2 14h-1c-3-4-5-8-8-11s-6-5-8-8v-1l-2-2z" class="B"></path><defs><linearGradient id="A" x1="410.361" y1="366.413" x2="406.109" y2="375.652" xlink:href="#B"><stop offset="0" stop-color="#181516"></stop><stop offset="1" stop-color="#433f3e"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M405 374h-1c-5-1-10-2-15-5l22 1c3 1 7 1 10 1l-1 1h3c-1 0-2 1-3 1h0 0-2-1l1 1c-2 1-5 0-7 0-2 1-4 1-6 1v-1z"></path><path d="M413 530h0c1 0 2-1 3-1 3-1 7-1 11-1 11 0 19 3 29 8l-1 1c-8-4-18-7-27-6h-10c-2 1-4 2-6 2v-1l1-2z" class="B"></path><path d="M418 455c-1 2-3 4-4 6-2 6 1 13-1 19-1 2-3 4-4 6 2-7 3-13 2-20-4-1-8-1-11-3l1-1c1 1 2 1 3 1 4 0 7-2 9-5l3-4 2 1z" class="D"></path><path d="M579 223l12-3c10-5 17-14 20-25 0-3 0-6 1-8v-1c0 9-1 14-5 21 4 0 5 1 9 3-2 0-4-1-5-1-3 1-7 6-9 7-6 4-15 7-21 8h-2v-1z" class="C"></path><path d="M375 451c12-1 22-4 30-14l3-4c-3 10-13 17-22 22-4 2-8 3-12 4l-1-1c5-1 10-3 15-6l-11 2-3-1h3 3c1-1 2-1 3-1l2-1h1l3-1c-2 0-3 0-5 1h-1-8z" class="R"></path><path d="M422 342c2 0 3 0 4 1h1l5 2h1c1 1 2 1 3 2l6 3c2 3 5 5 8 8s5 7 8 11h1c2 1 3 9 4 11v-36c1 9 1 18 1 27-1 4 0 10-2 13-2-6-5-12-8-17l1-1-5-6c-3-4-8-8-12-11h-1c-5-3-10-4-15-7z" class="S"></path><path d="M360 336h2l10-3c1-1 2-1 4-1 1 1 2 1 3 1-6 2-17 5-21 11v1c-1 1-2 2-2 4v1l-4 4h-1c-1-3 3-5 4-8l-2 1h-1c1-1 2-3 4-4h0v-2c-2 2-4 4-7 5 2-3 6-6 8-7h1v-1c0-1 1-1 2-2z" class="P"></path><defs><linearGradient id="C" x1="572.956" y1="544.342" x2="579.186" y2="536.478" xlink:href="#B"><stop offset="0" stop-color="#4b4945"></stop><stop offset="1" stop-color="#626060"></stop></linearGradient></defs><path fill="url(#C)" d="M568 541c4-1 7-2 11-2 5-1 10 1 15 1 2 1 4 0 6 0l1 1c-2 1-2 1-4 1 6 5 12 9 13 17l-1 1h0c-1-7-6-12-12-15-2-2-4-3-7-3h0c-7-1-13-1-20 1h-1c-1-1-1-1-1-2z"></path><path d="M565 152c0-3 1-5 2-7 3-5 8-7 13-8l4-1c2-2 0-4 2-5h0v1 3c2 1 5 1 7 0h1c1-2 1-3 3-4h1l1 1c-1 0-2 0-2 2 0 1 0 1 2 2 1 1 3 1 5 1h0l1-1 3 1c-1 1-2 1-3 2-3 1-6-2-10-2-1-1-3 0-4 0-9 1-17 2-22 10-1 3-2 5-3 8v2c0-3 0-3-1-5z" class="P"></path><path d="M416 454c5-4 9-5 16-4 4 0 9 3 11 6 5 6 6 15 5 22s-5 13-9 19l-1 1v-2c0-1 1-1 2-2 3-4 5-9 6-14 2-6 1-15-3-21-3-4-6-6-11-7s-9 0-14 3l-2-1z" class="B"></path><path d="M675 527c4-2 9-3 13-5s7-5 11-7v3l-13 8c-2 1-5 3-8 4-4 1-12 2-15 5-4 0-8 0-12-1 3-2 10-1 13-2l1-1c0-1 7-2 8-2l2-2z" class="S"></path><path d="M378 205s-2 5-2 6c-1 4-1 9 1 13 3 10 10 14 19 19 1-1 2-2 4-3 1-1 3 0 4 0 2 1 2 2 3 3-1 2-2 2-3 3-5 1-8-1-12-3-8-4-14-11-17-20-2-6-1-12 3-18z" class="B"></path><path d="M401 241h1c1 0 1 1 2 2v1l-1 1c-1 0-1 1-2 0-1 0-2 0-3-1 1-2 2-2 3-3z" class="Q"></path><path d="M620 530c-4-3-8-6-13-8-3-2-5-2-8-3 4-1 11 2 14 4 2 0 3 0 5 1l6 4h0l2 2c6 6 11 13 12 22-1-3-2-5-4-7-2-4-5-6-7-9-2-2-4-5-7-6z" class="B"></path><path d="M613 523c2 0 3 0 5 1l6 4h0l2 2h-1v1c-3-1-5-3-8-5-2-1-3-2-4-3z" class="J"></path><path d="M441 138c-7-1-13 1-20-2-1-1-1 0-2 1-4 3-6 8-11 9-4 1-6 0-9-2v-1c-3 2-5 3-7 6h-1 0c1-2 3-4 6-6l2-1c0-2 0-3 1-5s3-4 5-5 3-1 5 0h0-2c-2 0-4 1-5 3-3 2-3 4-2 7 0 1 0 1 1 2 2 1 3 1 5 0 7-1 7-8 12-10 4-1 6 3 9 2 0-1 0-3 1-3 0-2 1-2 2-2-1 1-1 2-2 4 1 0 1 1 2 1 2 0 4 0 6-1 1-2 2-4 2-6 0-1-1-2-2-3v-1h0c1 1 2 1 3 3 1 1 0 4-1 5v1l-1 1h0c2 1 3 1 5 2-1 0-1 1-2 1zm113 21h0l-2 5c-2 6-1 13 2 18 2 5 5 8 10 9 12 4 22-1 27-12 0-1 1-4 2-4v1c-1 3-2 5-3 8-2 4-6 8-11 10-7 1-14 1-20-3-5-3-8-8-9-14-2-7 0-13 4-18z" class="B"></path><defs><linearGradient id="D" x1="346.617" y1="377.922" x2="355.939" y2="379.789" xlink:href="#B"><stop offset="0" stop-color="#1b1a18"></stop><stop offset="1" stop-color="#444243"></stop></linearGradient></defs><path fill="url(#D)" d="M360 358l1 1c2-2 5-7 8-8v1c-2 2-5 5-6 8v1c-9 10-16 20-16 34l-1 1v2h0l-1-1h0c0-2-1-5 0-7v-3-1c1-10 9-20 15-28z"></path><path d="M465 153h0c3 1 6 5 7 7 3 6 5 13 3 20-1 5-5 9-10 11-5 4-13 4-19 2s-9-6-12-11v-3c1 2 2 5 4 7 3 4 7 6 12 6 6 1 13 0 17-5 5-3 7-8 7-13 0-9-4-15-9-21z" class="B"></path><path d="M308 474c7 6 12 10 21 13 10 4 18 5 28 4-1 1-3 2-5 2-12 2-24 0-34-7-2-1-4-3-7-5h0-1c-1-3-2-4-2-7z" class="D"></path><path d="M636 329c12 0 22 4 34 7 8 2 17 3 25 3-7 2-16 3-23 2l-7-2c-2-1-3-1-5-1-7-3-14-6-22-7-1 0-1-1-2-2z" class="R"></path><defs><linearGradient id="E" x1="322.398" y1="428.586" x2="339.602" y2="430.414" xlink:href="#B"><stop offset="0" stop-color="#0d0c0c"></stop><stop offset="1" stop-color="#2d2926"></stop></linearGradient></defs><path fill="url(#E)" d="M324 381h1c0 4-2 8-2 12-1 12 2 26 7 36 2 7 6 12 12 17 1 0 0 0 1 1v1c1 1 2 2 3 4v5h-1c-3-8-10-11-14-19-7-11-9-25-9-38 0-4 0-8 1-11 0-3 1-5 1-8z"></path><path d="M345 386v1 3c-1 2 0 5 0 7h0l1 1h0v-2l1-1 2 8c0-1 2-3 3-4 1 0 3-1 5 0v1l-1 1v3c1 8 9 18 15 22 2 2 5 4 8 6-7-2-12-6-16-11s-9-12-8-18v-5c-2 2-4 4-6 7 1 2 2 3 3 4s1 1 1 3c-1 0-2-2-3-3 0 1 0 1 1 2l6 9c3 3 7 6 10 9-8-3-14-10-18-17-5-8-7-16-4-26zm340-32l-15-12h1l44 24-1 2v1h0c-1 0-2 0-3-1l-2-1c-2 0-3-1-4-1l-9-5c-2-1-3-1-4-2l-2-2c-2-1-3-2-5-3z" class="P"></path><defs><linearGradient id="F" x1="647.097" y1="236.949" x2="638.954" y2="212.702" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#302d2d"></stop></linearGradient></defs><path fill="url(#F)" d="M641 236l1-1c4-6 6-11 5-19 0-2-1-4-2-6-3-1-6-2-8-4v-5c0-2 2-3 3-4 1 0 3-1 5-1v-3c-1-2-3-3-4-4l1-9c-1-1-3-3-5-4-4-3-8-3-7-9v-2l1-1c0-1 2-3 4-4h1 0 2 1c2 1 5 3 6 5l1 2-1 1-2-3-3-3c-3 0-5-1-7 1s-2 3-2 5c0 1 0 3 1 3 1 1 3 1 4 2 3 1 8 5 8 8s-2 5-1 8c2 1 3 1 5 0l1 1-3 2v5c0 1 2 1 1 2h-1c-2-1-3-1-5-1-2 1-2 2-3 3 0 2 0 4 1 5s3 2 5 2c1-1 1-2 2-3 2 1 3 8 3 11 2 7 0 14-4 19-1 2-2 3-3 4l-1-1c0 1-1 1-2 2 1-1 2-2 2-3v-1z"></path><path d="M452 502l4-11c0 7 1 13-4 19-3 3-7 5-11 7 7 1 12 3 18 6l-1 2c-3-2-6-4-10-5-10-3-23-3-33 1l-1-1c5-2 11-4 17-5l6-1c6-2 12-7 15-12z" class="B"></path><path d="M358 334c14-6 29-7 42-1 5 2 9 4 14 6 4 1 7 1 10 2s5 2 7 2c3 0 6 4 9 4l2 2v1l-6-3c-1-1-2-1-3-2h-1l-5-2h-1c-1-1-2-1-4-1h0-3 0 0c-2-1-6-2-9-3-6-2-13-5-20-6-4-1-7 0-11 0-1 0-2 0-3-1-2 0-3 0-4 1l-10 3h-2 0-2-1c0 1-1 1-1 1h-1c-1 1-2 2-3 2l-1 1h-2l-1 1h-1c0-1 1-1 1-1l4-2c1-1 2-1 3-2 1 0 2-1 3-1v-1z" class="G"></path><path d="M432 543l-1-1c-6 3-12 8-15 15l-1 3c0-6 4-11 7-15 2-1 3-2 5-3 1-1 3-1 5-2h-7l-1-1c2-1 5-1 7-1 6 0 12-2 18 0l6 2c4 2 9 4 12 7a57.31 57.31 0 0 1 11 11c1 2 2 4 4 6 1 2 4 6 4 8-4-5-6-10-11-15-1-2-4-3-6-4l-1-1h0c-2-4-7-6-11-8-1-1-3 0-5-1-5-1-10-3-15-2h-1c0 1-1 1-1 1-1 0-1 0-2 1h-1z" class="P"></path><path d="M349 346c3-1 5-3 7-5v2h0c-2 1-3 3-4 4h1l-7 8c-6 8-11 19-11 29-1 4 0 8 0 11-3-4-6-10-5-15 2-14 8-26 19-34z" class="R"></path><path d="M324 522c2 0 2 0 4 1l8 3v1c1 0 2 1 4 1l2 1 3 1 1 1c1 0 2 1 3 1l2 1 2 1v-1h0c0-1-1-1-2-1l-1-1-3-1c-1 0-2-1-3-1l-2-1c-1-1-2-1-2-1l-3-1c-1-1-2-1-3-3h0c5 2 9 4 14 5 15 3 29 0 44-4v1c-5 2-9 4-14 5-6 1-11 1-17 3h-2c1 1 1 1 2 1 4 2 7 4 11 7-3-1-5-2-8-2-9-3-19-6-28-10-4-2-8-4-12-7z" class="M"></path><defs><linearGradient id="G" x1="291.519" y1="392.041" x2="296.678" y2="394.555" xlink:href="#B"><stop offset="0" stop-color="#080608"></stop><stop offset="1" stop-color="#2b2a27"></stop></linearGradient></defs><path fill="url(#G)" d="M310 372l2-2h0c-1 3-3 6-5 9-6 11-11 23-19 33h-1c-5 3-10 5-15 8 3-3 6-5 8-8 11-13 18-28 29-41l1 1z"></path><path d="M310 372l2-2h0c-1 3-3 6-5 9-6 11-11 23-19 33h-1l3-6c-2 3-5 5-7 7h-1c3-3 5-6 8-9 7-10 13-21 20-32z" class="L"></path><path d="M379 333c4 0 7-1 11 0 7 1 14 4 20 6 3 1 7 2 9 3h0l-1 1-18-6c-2 0-5-1-8-1-11 3-18 5-26 13v1c0 1-1 1-2 2l-1-1 1-1-1-1-4 4v-1c1-3 2-5 4-7v-1c-2 2-5 4-7 6v-1c0-2 1-3 2-4v-1c4-6 15-9 21-11z" class="F"></path><path d="M363 344c2-2 5-4 8-5 2-1 4-1 6 0-5 2-11 5-14 10l-4 4v-1c1-3 2-5 4-7v-1z" class="J"></path><path d="M356 349c3-3 5-6 9-9 8-5 19-7 28-5-6 1-11 2-16 4-2-1-4-1-6 0-3 1-6 3-8 5s-5 4-7 6v-1zm78 20c5 5 11 7 18 11 4 2 8 5 12 8h0c1 1 0 1 1 1 2 1 5 6 7 9h0c2 3 3 6 3 8l-5-5-1 1c-1-1-2-3-2-4l-1 1h0c-1 2 1 1 0 4-4-5-9-9-14-14-1-4-8-6-10-9h0v-1l-2-2c-1 0-2-1-4-2h0c-1-1-2-2-3-4h0v-1s0-1 1-1z" class="D"></path><path d="M442 380c2-1 3-1 5 0 6 4 14 10 20 16 1 1 3 3 3 5l-1 1c-1-1-2-3-2-4l-1 1h0c-1 2 1 1 0 4-4-5-9-9-14-14-1-4-8-6-10-9z" class="O"></path><path d="M595 624c0-2 1-3 1-4 1-1 3-2 4-2 7 0 15 3 20 9l1 2 3 4c-4-2-7-4-10-5h-1c-8-2-16 0-23 4-4 3-8 6-11 10-3 2-6 5-8 7l-1-1 3-5c2-2 4-5 6-8h1c2-1 3-2 3-3l4-4 2-2 3-1c1 0 2-1 3-1h0z" class="B"></path><path d="M595 624c0-2 1-3 1-4 1-1 3-2 4-2 7 0 15 3 20 9l1 2h-1-1c-1-1-3-3-5-4l-2-1h-2c-1-1-1-1-2-1-2-1-7 0-9 0h-3l-1 1z" class="G"></path><path d="M329 422c9 14 17 26 34 31l3 1h0 11l11-2c-5 3-10 5-15 6l1 1-6 1c-8 0-17-3-22-8-1-2-2-3-3-4v-1c-1-1 0-1-1-1-6-5-10-10-12-17l1-1c-1-2-1-4-2-6z" class="N"></path><path d="M346 452c-1-2-2-3-3-4v-1c2 2 4 4 6 5 6 6 16 6 24 6h0l1 1-6 1c-8 0-17-3-22-8z" class="B"></path><path d="M329 422c9 14 17 26 34 31l3 1h0v1h1c2 1 4 0 6 1l-1 1c-2 0-7 0-9-1-1 0-2 1-3 0h0c-1 0-1 0-2-1l-2-1-2-1c-1 0-2 0-2-1l-1-1c-4-1-5-5-9-7-3-1-5-5-7-7-1-3-3-6-4-9-1-2-1-4-2-6zm250 220h0c1 0 2-1 3-1 8-2 17-1 24 3h1-1c-6-1-11 0-17 1-9 5-14 13-22 19h-1l-2 2-1 1h-1c-1-1-1 0-1-1-2 1-4 5-6 6-3 3-4 6-6 9l-1-2c6-12 17-23 29-31h0c-7 1-9 8-16 10 2-2 5-4 7-6 1-1 1-1 1-2l1-2 1 1c2-2 5-5 8-7z" class="E"></path><path d="M555 672c6-10 14-17 23-23 1-1 3-2 4-2 2-1 5-2 7-2-9 5-14 13-22 19h-1l-2 2-1 1h-1c-1-1-1 0-1-1-2 1-4 5-6 6z" class="D"></path><path d="M556 231h0c-1-6-3-11-6-16l-1-1c7 5 14 7 22 9 3 1 6 1 8 2l1 1c1 3 3 6 5 9-2-2-5-4-8-5-4-2-9-3-13-4-2-1-5-2-7-2 0 1 0 3 1 4 3 5 11 8 16 11h1c3-1 6-2 9-1l1 1h-1l-1 1c1 2 3 3 5 5 0-1 1-2 1-3 1-1 3-1 4 0 1 0 1 0 2 1v1h-2c0-1-1-1-2-1v3c2 1 5 1 7 0 1-1 2-2 3-4 2 2 4 4 6 5h1l1 1h0c-1 1-2 1-4 1 0 0-1-1-2-1-1-1-2 0-4 0-3 0-6 1-9-1-1 0-3-1-4-2-2-1-6-4-8-4-1 0-3 1-5 0-6-1-13-6-17-10z" class="M"></path><defs><linearGradient id="H" x1="375.897" y1="505.996" x2="330.174" y2="463.413" xlink:href="#B"><stop offset="0" stop-color="#010000"></stop><stop offset="1" stop-color="#232424"></stop></linearGradient></defs><path fill="url(#H)" d="M394 482h1c0 1 1 1 0 3-1 0-1 1-2 2s-1 2-1 4l-8 8c-12 7-25 10-39 8l-5-1-8-3-15-9c-3-1-5-4-7-6 1-1 1-1 1-3l1-1c2 3 5 5 8 7 10 9 27 14 41 12h1 3c12-3 22-11 29-21z"></path><path d="M405 374v1c2 0 4 0 6-1 2 0 5 1 7 0l-1-1h1 2 0c4 0 7 0 10 1h1c2 1 3 2 4 3l7 3h0c2 3 9 5 10 9-4-2-8-5-13-7-8-3-18-2-26-1h-1c-10 2-22 8-28 16l-3 5v-2c0-4 6-13 9-16 4-4 10-8 15-10z" class="L"></path><path d="M405 374v1c2 0 4 0 6-1 2 0 5 1 7 0l-1-1h1 2 0c4 0 7 0 10 1h1c2 1 3 2 4 3h-1c-2 0-4 0-5-1-7 0-14-1-20 0-4 2-8 3-12 5-2 1-5 3-7 3 4-4 10-8 15-10zm-39-24v-1c8-8 15-10 26-13 3 0 6 1 8 1l18 6c5 3 8 6 10 12 1 2 2 6 1 8h0v-1c-2-5-5-10-10-13h-2l-2-1-14-3-1 1 1 2c-1 1-3 2-4 3-2 0-3 0-4-1-1-2-1-2-1-4 1-1 1-2 3-2 4-2 19 3 24 5-8-6-17-10-26-9-14 3-21 11-30 21v-1c1-3 4-6 6-8v-1c-3 1-6 6-8 8l-1-1 3-3c-1-1-2-2-4-2l4-4 1 1-1 1 1 1c1-1 2-1 2-2z" class="P"></path><path d="M395 345c2 0 3-1 5 0v1c-1 2-2 3-3 4-2 0-2 0-3-1l-1-1c1-2 1-2 2-3zm-29 5c3-3 7-6 11-7l7-3c1 0 2-1 3 0h-2l-1 1c-8 3-16 8-21 14-1-1-2-2-4-2l4-4 1 1-1 1 1 1c1-1 2-1 2-2z" class="Q"></path><path d="M685 354c2 1 3 2 5 3l2 2c1 1 2 1 4 2l9 5c1 0 2 1 4 1l2 1c1 1 2 1 3 1h0v-1l1-2c8 6 16 9 25 12-13-2-26-4-38-9 3 3 7 7 9 11 10 13 18 27 30 39l-11-6 1-1-1-1c-5-1-9-9-11-13l-1 1 1 8-2-2h0c-1-3-3-7-4-10-7-15-15-30-28-41z" class="D"></path><path d="M404 589c12 5 24 8 33 18l-4-21c6 14 16 24 22 38l3 9c1 2 1 7 3 9 1 1 1 2 1 3l-1 1c-2-3-3-7-5-11l-12-24c-2-2-4-6-6-8v1h-1v2c1 1 1 2 1 4l1 2c1 0 1 1 1 2v1c-5-5-10-10-15-13-9-5-18-8-28-6-2 1-5 2-6 4l-3 1c1 1 0 1 1 1-5 2-15 4-17 10h-1c0 1-1 2-2 2h-1l1-1c5-8 15-14 23-18 2-2 5-3 7-3l3-1h2v-2z" class="P"></path><path d="M723 475l4-22c1 6 4 13 9 17l1 1-1 2c-2-3-5-7-7-10 0 19-8 42-22 56-4 4-8 7-11 10-3 2-6 5-10 6 5-4 11-10 14-15-3 3-7 5-11 7-1 0-2-1-3-1l13-8 4-3c3-2 4-5 7-6 2-3 3-6 4-9 1-4 2-7 3-10h0l-2 2v-1c0-1 1-3 1-3l5-13h2z" class="G"></path><path d="M715 491c0-1 1-3 1-3l5-13h2l-3 12-3 9c-3 7-5 13-10 20-2 2-3 5-6 6h0l9-13c2-3 3-6 4-9 1-4 2-7 3-10h0l-2 2v-1z" class="S"></path><path d="M319 373c2-2 4-6 6-7-1 3-3 6-3 10 1 0 1 1 2 1v4c0 3-1 5-1 8l-2-2c-2 3-5 7-5 11v1c-2 12-1 27 6 37l8 7c-4-1-8-3-11-6h0c-1-2-4-5-5-7s-2-4-3-7l-1-1c-2-3-2-7-2-10-1-7 0-14 2-22l-2-1c2-6 6-12 9-17 1 0 2 1 2 1z" class="M"></path><path d="M321 387c2-3 1-8 1-11 1 0 1 1 2 1v4c0 3-1 5-1 8l-2-2z" class="K"></path><path d="M317 372c1 0 2 1 2 1-4 7-6 14-8 23l-1-2c1-2 1-4 1-5v-1c-1 0-1 1-1 2l-2-1c2-6 6-12 9-17z" class="I"></path><path d="M310 390c0-1 0-2 1-2v1c0 1 0 3-1 5l1 2c0 3-1 6-1 9v14c1 1 1 3 1 4l-1-1c-2-3-2-7-2-10-1-7 0-14 2-22z" class="E"></path><path d="M719 407l-1-8 1-1c2 4 6 12 11 13l1 1-1 1-5-3c8 22 10 45 14 67-1-1-2-3-3-4l1-2-1-1c-5-4-8-11-9-17l-4 22h-2c4-11 5-20 4-32 1-6-1-13-2-20h-2c0-1-1-4-1-5l-3-13 2 2z" class="B"></path><path d="M717 405l2 2 5 16h-1-2c0-1-1-4-1-5l-3-13z" class="E"></path><defs><linearGradient id="I" x1="726.135" y1="452.267" x2="733.365" y2="455.233" xlink:href="#B"><stop offset="0" stop-color="#838383"></stop><stop offset="1" stop-color="#a7a6a5"></stop></linearGradient></defs><path fill="url(#I)" d="M729 444c2 8 2 17 6 25l1 1c-5-4-8-11-9-17 1-2 0-5 1-7 0-1 1-1 1-2z"></path><path d="M724 423c2 6 4 14 5 21 0 1-1 1-1 2-1 2 0 5-1 7l-4 22h-2c4-11 5-20 4-32 1-6-1-13-2-20h1z" class="N"></path><path d="M438 498l2 2c1-1 1-1 1-2 1-3 3-5 4-8 3-5 5-9 6-15 2-8 1-19-3-26-4-5-10-10-16-11-3-1-5-1-7 0-1 1-2 3-3 4 0 3 1 4 2 7-1 0-2-1-2-2-1-2-1-4-1-5 0-2 2-4 4-6 2-1 4-1 7-1 0-1 0-1-1-2 0-2 0-3 1-5 1-1 3-2 4-2h2 0-1l-3 3c-1 2-1 4 0 6 2 1 5 3 7 4 9 6 14 17 13 28 0 11-4 21-10 29h0c2 1 3 2 4 4s1 2 0 3h2 0c1-1 2-1 2-1-3 5-9 10-15 12l-6 1c1-1 1-1 2-1 1-1 1-2 2-2l4-2 3-1-1-1-1 1c-1 0-2-1-3-1l-1-1c-5 3-9 5-15 7l15-21c3-5 4-12 2-17-1-4-3-7-6-9-2-1-6-2-9-1-2 1-4 3-5 5-1 3-2 6-1 9 1 2 3 4 6 5h0-1c-2 0-4-2-5-4-2-3-2-5-2-8 1-3 3-6 6-8 3-1 5-2 9-1 3 1 6 4 8 8 3 6 2 13 0 20v2h1 1c-1 1-2 1-2 2v2h0z" class="B"></path><path d="M438 492v2h1 1c-1 1-2 1-2 2v2h0-2l-1 1c0 1 0 3-1 4 0 0-1 1-1 2-2 4-5 5-9 6 5-6 10-13 14-19zm-2 15c3-1 5-4 6-7 0-1 0-1 1-2h2c1 1 1 1 2 3-1 2-2 3-4 4l1 1h0c2-1 4-4 4-6 1 2 1 2 0 3h2 0c1-1 2-1 2-1-3 5-9 10-15 12l-6 1c1-1 1-1 2-1 1-1 1-2 2-2l4-2 3-1-1-1-1 1c-1 0-2-1-3-1l-1-1z" class="Q"></path><defs><linearGradient id="J" x1="295.626" y1="442.764" x2="322.822" y2="438.297" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2c2927"></stop></linearGradient></defs><path fill="url(#J)" d="M299 400v1c1-1 2-3 2-4l1 1-1 1 1 1c-1 4-3 8-3 12-2 17 1 37 11 51 5 6 10 12 17 16 3 2 6 3 8 5-1 0-3 0-4-1l-1 1h-4l-1-1-1 1 1 1h2v1c1 0 1 0 2 1-9-3-14-7-21-13-6-7-9-14-12-23l-1-4c0-2 0-4-1-6v-4c-1-2-1-4-1-6h1c1-2 0-5 0-8l1-2-2-2c1-6 3-13 6-19z"></path><path d="M308 474c-6-7-9-14-12-23l-1-4c0-2 0-4-1-6v-4c-1-2-1-4-1-6h1v8c1 1 1 3 1 4 3 13 10 28 21 36 4 3 10 4 15 4l-1 1h-4l-1-1-1 1 1 1h2v1c1 0 1 0 2 1-9-3-14-7-21-13z" class="L"></path><path d="M327 360l2-2c3-3 6-7 9-9 4-2 8-6 12-8-2 3-6 5-8 7s-3 5-5 6c-5 7-5 15-8 22h1c0 1-1 3-1 4-4 7 0 31 3 39 5 12 15 24 28 29 5 2 10 2 15 3h8 1c2-1 3-1 5-1l-3 1h-1l-2 1c-1 0-2 0-3 1h-3-3l3 1h-11 0l-3-1c-17-5-25-17-34-31 1 2 1 4 2 6l-1 1c-5-10-8-24-7-36 0-4 2-8 2-12h-1v-4c-1 0-1-1-2-1 0-4 2-7 3-10h0c1-2 2-4 2-6z" class="D"></path><defs><linearGradient id="K" x1="330.783" y1="366.633" x2="323.217" y2="366.867" xlink:href="#B"><stop offset="0" stop-color="#b2afb4"></stop><stop offset="1" stop-color="#d0cecb"></stop></linearGradient></defs><path fill="url(#K)" d="M327 360l2-2c3-3 6-7 9-9 4-2 8-6 12-8-2 3-6 5-8 7s-3 5-5 6c-5 7-5 15-8 22l-1 2h0v-4l5-16c1-3 5-5 6-8v-1c-3 3-6 7-8 10-2 5-3 11-4 16-1 3-1 6-2 9-2 13 0 26 4 38 1 2 1 4 2 6l-1 1c-5-10-8-24-7-36 0-4 2-8 2-12h-1v-4c-1 0-1-1-2-1 0-4 2-7 3-10h0c1-2 2-4 2-6z"></path><path d="M603 603h0c9-5 19-7 29-4h3 1c0 1 1 1 2 2l7 4c1 1 2 2 3 2l2-2c6 7 12 14 12 24v3h0c0 3-1 5-2 7 1-6 2-11-3-17-6-7-15-13-24-14-10-1-19 1-27 6-2 1-4 3-6 4-1 0-3 1-4 2 0 1-1 2-1 4h0c-1 0-2 1-3 1l-3 1-2 2-4 4c0 1-1 2-3 3h-1c1-2 3-4 5-6 4-6 8-11 13-16l-1-1 8-5c0-2-1-3-1-4z" class="R"></path><path d="M603 603h0c9-5 19-7 29-4h3 1c0 1 1 1 2 2l7 4c1 1 2 2 3 2l2 2c-1 0-6-3-7-4-16-7-33-1-46 8l-1-1 8-5c0-2-1-3-1-4z" class="K"></path><path d="M603 603h0c9-5 19-7 29-4h3 1c0 1 1 1 2 2-4 0-7-1-10-1-9 0-17 3-24 7 0-2-1-3-1-4z" class="J"></path><path d="M295 475c4 7 8 15 13 21 7 7 17 11 26 14 13 5 28 5 41 1h4 3v1h-1-1l-2 2h1c3 0 5-1 8-1-3 2-6 3-9 4l-2 1 4 4c-7-2-13 0-20 0-20-1-38-9-53-22 4 6 8 11 14 15l9 6c1 0 4 1 4 2h0c1 2 2 2 3 3l3 1s1 0 2 1l2 1c1 0 2 1 3 1l3 1 1 1c1 0 2 0 2 1h0v1l-2-1-2-1c-1 0-2-1-3-1l-1-1-3-1-2-1c-2 0-3-1-4-1v-1l-8-3c-2-1-2-1-4-1h-1c-15-8-23-29-28-44v-3z" class="G"></path><path d="M379 511h3v1h-1-1l-2 2h1c3 0 5-1 8-1-3 2-6 3-9 4 0 0-1-2-2-2h-3c1 0 2-1 3-1v-1-1l3-1z" class="D"></path><path d="M415 521c10-4 23-4 33-1 4 1 7 3 10 5 8 5 14 13 19 21 2 2 3 5 4 7l1 1v4l-1-2c-1-2-2-4-4-7l-4-1c0-2-4-5-6-7-3-2-8-3-11-5-10-5-18-8-29-8-4 0-8 0-11 1-1 0-2 1-3 1h0c-1-1-1-1-2-1l-2 1-6 2c-4 1-9 5-11 9-1 2-3 4-4 6-1 1-1 4-3 5 0-2 0-3 1-5 0-1 0-3 1-4 3-6 9-12 14-16 1 1 1 1 2 1l9-5v-1l3-1z" class="I"></path><path d="M412 523c12-5 27-4 39 1 5 2 11 6 14 11-3-1-4-4-8-3-13-8-26-11-41-7h-2c-2 1-4 1-6 2s-3 2-5 3c-2 0-3 0-4 1-3 2-7 6-9 9-1 1-2 3-3 3 3-6 9-12 14-16 1 1 1 1 2 1l9-5z" class="P"></path><path d="M415 521c10-4 23-4 33-1 4 1 7 3 10 5 8 5 14 13 19 21 2 2 3 5 4 7l1 1v4l-1-2c-1-2-2-4-4-7l-4-1c0-2-4-5-6-7 0-1-8-8-10-9 4-1 5 2 8 3-3-5-9-9-14-11-12-5-27-6-39-1v-1l3-1z" class="F"></path><path d="M457 532c4-1 5 2 8 3l2 2c2 5 8 7 10 12h0l-4-1c0-2-4-5-6-7 0-1-8-8-10-9z" class="T"></path><path d="M295 475c-2-4-2-8-3-11 1 2 2 5 3 7 2 5 5 10 8 14h1l2 2 1-1v1c2 3 5 5 8 7h2l15 9 8 3 5 1c14 2 27-1 39-8l8-8c0-2 0-3 1-4s1-2 2-2c1-2 0-2 0-3h-1c2-5 3-8 4-13 0 10 1 20-3 29-2 3-4 6-5 9 1 1 4 0 4 1-4 1-9 1-12 3h-3-4c-13 4-28 4-41-1-9-3-19-7-26-14-5-6-9-14-13-21z" class="M"></path><path d="M332 503l8 3c-1 0 0 0-1 1v1h0c-4-1-8-1-12-3l1-1c2 0 3 0 4-1z" class="O"></path><path d="M392 491h0c-6 10-14 15-25 18-9 1-19 0-28-1h0v-1c1-1 0-1 1-1l5 1c14 2 27-1 39-8l8-8z" class="I"></path><path d="M295 475c-2-4-2-8-3-11 1 2 2 5 3 7 2 5 5 10 8 14h1l2 2 1-1v1c2 3 5 5 8 7h2l15 9c-1 1-2 1-4 1l-1 1h-2c14 7 31 9 46 5 9-3 16-8 21-16 1-2 1-3 2-4 0-2 2-5 3-7-2 9-7 19-15 24l-7 4c-13 4-28 4-41-1-9-3-19-7-26-14-5-6-9-14-13-21z" class="K"></path><path d="M304 485l2 2 1-1v1c2 3 5 5 8 7h2l15 9c-1 1-2 1-4 1l-1 1h-2c-3-1-15-10-17-13-1-2-4-4-5-7h1z" class="S"></path><path d="M381 402l3-5c6-8 18-14 28-16h1c8-1 18-2 26 1 5 2 9 5 13 7 5 5 10 9 14 14 3 4 7 8 9 13 3 4 5 9 7 13-2-1-3-3-4-5-2-2-5-6-8-8-2-1-5-4-7-4l-1-1c-5-1-10-2-15-2-4 0-9-1-13 0-2 1-4 2-7 3l2-2c10-5 21-5 31-3-9-8-19-16-31-19-12-2-25 0-35 6-6 3-11 7-13 13v3c-1-3 0-6 0-8z" class="U"></path><path d="M293 370l44-26c-6 0-11 0-16-2 13-1 24-4 37-8v1c-1 0-2 1-3 1-1 1-2 1-3 2l-4 2s-1 0-1 1h1l1-1h2l1-1c1 0 2-1 3-2h1s1 0 1-1h1 2 0c-1 1-2 1-2 2-12 6-22 18-26 31l-2 7h-1c3-7 3-15 8-22 2-1 3-4 5-6s6-4 8-7c-4 2-8 6-12 8-3 2-6 6-9 9l-2 2c0 2-1 4-2 6h0c-2 1-4 5-6 7 0 0-1-1-2-1-3 5-7 11-9 17l2 1c-2 8-3 15-2 22 0 3 0 7 2 10l1 1c1 3 2 5 3 7s4 5 5 7h0c-2 0-4-2-5-4-3-2-4-6-6-9-2-2-4-5-4-7-2-5-2-12-2-17h0 0l-1-1 1-1-1-1c0 1-1 3-2 4v-1c1-4 4-8 6-11 5-9 9-18 15-27v-1l3-3c-7 2-11 9-17 12-9 4-20 6-29 9l15-8 1-1z" class="B"></path><path d="M317 372c1-3 8-10 10-12 0 2-1 4-2 6h0c-2 1-4 5-6 7 0 0-1-1-2-1z" class="F"></path><path d="M308 389l2 1c-2 8-3 15-2 22 0 3 0 7 2 10l1 1c1 3 2 5 3 7s4 5 5 7c-5-4-7-8-10-13-5-9-4-25-1-35z" class="Q"></path><path d="M293 370c2 0 3 0 4-1 1 0 2-1 3-2h2l5-3 2-1c1 0 1 0 1-1 2 0 3-1 4-2 1 0 1-1 2-1l2-1c1 0 1-1 2-1 1-1 1-1 2-1 1-1 2-1 2-2l5-3c1-1 2-1 3-2l4-3 1 1c-1 1-3 2-4 3h-1l-2 2c-1 0-1 1-2 1-2 2 1-1-1 1l-2 2h-1c0 1 0 2-1 2-7 2-11 9-17 12-9 4-20 6-29 9l15-8 1-1z" class="R"></path><path d="M535 186c3 3 8 5 11 8 9 7 16 17 27 19 8 1 15 0 22-4 6-5 10-14 11-22 2-13 0-26-9-37h1c9 8 11 20 13 32 0 1 1 3 1 4v1c-1 2-1 5-1 8-3 11-10 20-20 25l-12 3h-1c-2-1-4-1-5-2v-1s-4 0-4-1h-3 0l1 1c-2 0-3 0-4-1-16-5-26-16-33-30v-1c1 1 2 1 2 2 1 1 1 2 2 3 1 0 1 1 2 1l1-1c-1-2-3-4-4-6l2-1z" class="M"></path><defs><linearGradient id="L" x1="572.274" y1="215.429" x2="613.568" y2="198.929" xlink:href="#B"><stop offset="0" stop-color="#acabae"></stop><stop offset="1" stop-color="#dad9d8"></stop></linearGradient></defs><path fill="url(#L)" d="M611 182c0 1 1 3 1 4v1c-1 2-1 5-1 8-3 11-10 20-20 25l-12 3h-1c-2-1-4-1-5-2v-1s-4 0-4-1h-3 0l1 1c-2 0-3 0-4-1-16-5-26-16-33-30v-1c1 1 2 1 2 2 1 1 1 2 2 3 1 0 1 1 2 1l1-1c8 12 19 21 33 23 7 2 19 0 25-4 10-7 14-19 16-30z"></path><path d="M530 189v-1c1 1 2 1 2 2 1 1 1 2 2 3 1 0 1 1 2 1 6 11 16 18 29 22 9 3 16 3 26 0-6 4-11 5-18 4 0 0-4 0-4-1h-3 0l1 1c-2 0-3 0-4-1-16-5-26-16-33-30z" class="G"></path><path d="M389 602c6 0 12-1 18 0 8 1 17 6 23 11l6 4h1c6 5 9 11 14 18l-1 2 5 11h-1s-1-1-2-1l-1-1h-1c-1-1-2-1-3-2-1 0-3-1-4-1h-1c-1-1-1-1-2-1-1-1-3 0-4 0h-3l-1-1c6-2 12-2 18 1-6-6-13-13-22-14-6-2-14-2-20 0-3 2-7 4-10 6 7-10 13-15 25-17-10-6-21-11-33-9-9 1-18 7-23 15-4 6-3 10-2 17-1-2-2-5-2-7-2-7 1-13 5-19h1c1 0 2-1 2-2h1c2-6 12-8 17-10z" class="M"></path><path d="M430 613l6 4h1c6 5 9 11 14 18l-1 2c-6-9-13-16-21-23l1-1z" class="I"></path><path d="M389 602c6 0 12-1 18 0 8 1 17 6 23 11l-1 1c-6-5-16-9-24-10-13-2-23 1-34 8h1c2-6 12-8 17-10z" class="L"></path><path d="M585 597l6-8c1 0 1-2 2-2-2 4-3 8-4 12-1 3-1 6-1 9 2-3 5-7 8-9s9-3 13-5c8-3 18-7 26-11-3 3-6 6-10 9 8 2 19 7 25 13l-2 2c-1 0-2-1-3-2l-7-4c-1-1-2-1-2-2h-1-3c-10-3-20-1-29 4h0c0 1 1 2 1 4l-8 5 1 1c-5 5-9 10-13 16-2 2-4 4-5 6-2 3-4 6-6 8l-3 5-1 2-2 2c-1-1-2-1-2-2l3-9-1-1c4-15 8-31 18-43z" class="D"></path><path d="M580 622l5-5c9-12 20-20 35-22 6 0 10 1 16 4h-1-3c-10-3-20-1-29 4h0c-9 4-16 12-22 20l-1-1z" class="K"></path><path d="M585 602c0 2-1 4-2 6-2 4-4 9-5 13 0 3-2 5-3 7-2 4-4 8-5 12h0c2-3 4-6 5-9s4-7 5-9l1 1c-2 2-5 7-5 10-1 1-1 2-1 3-1 3-4 4-2 7l-3 5-1 2-2 2c-1-1-2-1-2-2l3-9c1-6 5-11 7-16 3-8 6-16 10-23z" class="I"></path><path d="M585 597l1 1c0 1-1 3-1 4-4 7-7 15-10 23-2 5-6 10-7 16l-1-1c4-15 8-31 18-43z" class="G"></path><path d="M603 603c0 1 1 2 1 4l-8 5 1 1c-5 5-9 10-13 16-2 2-4 4-5 6-2 3-4 6-6 8-2-3 1-4 2-7 0-1 0-2 1-3 0-3 3-8 5-10 6-8 13-16 22-20z" class="C"></path><path d="M596 612l1 1c-5 5-9 10-13 16-2 2-4 4-5 6-2 3-4 6-6 8-2-3 1-4 2-7 0-1 0-2 1-3v1h0c3-5 6-9 10-13 3-3 6-6 10-9z" class="F"></path><path d="M391 600c1-2 4-3 6-4 10-2 19 1 28 6 5 3 10 8 15 13v-1c0-1 0-2-1-2l-1-2c0-2 0-3-1-4v-2h1v-1c2 2 4 6 6 8l12 24c2 4 3 8 5 11 1 2 1 4 2 6 1 5 9 12 13 15 2 2 4 6 5 8-2-8-6-16-8-24-1-6-1-11-1-16 1 1 1 2 1 4 2 14 6 26 11 39 3 9 8 16 10 25l2 8v2l-1 1-1-2c0-5-3-10-5-15l-1 1-1-4c-1-2-2-3-3-4l-1-3v-1c-1-1-1-2-2-3l-3-5c-1-1-1-2-2-3l-1-2-1-1-3-3h-1c1 1 1 2 2 4l2 2c-1 0-1 0-2-1l-6-7v1c0 1 1 2 2 3 1 2 4 4 5 6l-1 1c-4-5-7-9-11-13-2-1-3-2-5-3l-1 1h0c-3-6-7-11-12-15-8-6-14-5-23-4 4-1 8-3 12-3l1 1h3c1 0 3-1 4 0 1 0 1 0 2 1h1c1 0 3 1 4 1 1 1 2 1 3 2h1l1 1c1 0 2 1 2 1h1l-5-11 1-2c-5-7-8-13-14-18h-1l-6-4c-6-5-15-10-23-11-6-1-12 0-18 0-1 0 0 0-1-1l3-1z" class="L"></path><path d="M440 615v-1c0-1 0-2-1-2l-1-2c0-2 0-3-1-4v-2h1c2 4 5 8 7 13 5 9 10 18 13 27h0c-2-3-5-6-6-10 0-2-2-4-3-6-3-5-6-9-9-13zm16 40c8 4 17 13 22 20 2 3 3 6 5 8 2 5 5 9 6 14h0l-1 1-1-4c-1-2-2-3-3-4l-1-3v-1c-1-1-1-2-2-3l-3-5c-1-1-1-2-2-3l-1-2-1-1-3-3h-1 0c-2-2-4-5-6-6h-1l-1-1c-2-1-4-3-5-4s-1-2-1-3z" class="G"></path><path d="M391 600c12-4 21-3 32 3 7 4 15 10 19 16 2 3 4 6 5 9 1 2 3 4 4 6h0v1c-5-7-8-13-14-18h-1l-6-4c-6-5-15-10-23-11-6-1-12 0-18 0-1 0 0 0-1-1l3-1z" class="R"></path><path d="M455 663c-3-6-7-11-12-15-8-6-14-5-23-4 4-1 8-3 12-3l1 1h3c1 0 3-1 4 0 1 0 1 0 2 1h1c1 0 3 1 4 1 1 1 2 1 3 2h1l1 1c1 0 2 1 2 1h1l4 4c2 4 6 6 9 10h0c-4-3-8-6-13-9-2-1-4-3-6-3h0l7 5c0 1 0 2 1 3s3 3 5 4l1 1h1c2 1 4 4 6 6h0c1 1 1 2 2 4l2 2c-1 0-1 0-2-1l-6-7v1c0 1 1 2 2 3 1 2 4 4 5 6l-1 1c-4-5-7-9-11-13-2-1-3-2-5-3l-1 1h0z" class="B"></path><defs><linearGradient id="M" x1="460.558" y1="662.626" x2="492.332" y2="739.891" xlink:href="#B"><stop offset="0" stop-color="#040100"></stop><stop offset="1" stop-color="#292726"></stop></linearGradient></defs><path fill="url(#M)" d="M455 663l1-1c2 1 3 2 5 3 4 4 7 8 11 13l1-1c-1-2-4-4-5-6-1-1-2-2-2-3v-1l6 7c1 1 1 1 2 1l-2-2c-1-2-1-3-2-4h1l3 3 1 1 1 2c1 1 1 2 2 3l3 5c1 1 1 2 2 3v1l1 3c1 1 2 2 3 4l1 4 1-1c2 5 5 10 5 15l1 2 1-1v-2h2l3 28c0 2 0 4 1 7-1 1-1 2-1 4 0 1 0 0-1 1 0 3 1 3-1 5v2h-1l-2 16-1 1c-1 2-1 3-1 5h0 0v-6l-2-2c1-10 1-20 0-30-2-19-12-37-27-49h-1l-14-7c2 0 5 1 7 1 2 1 5 3 7 3-2-10-5-18-9-27h0z"></path><path d="M484 700c1 1 2 2 3 4h0c5 9 9 19 10 29 1 8 1 17 1 25l-2 16-1 1c1-9 1-18 1-27 0-7 0-14-2-21-1-9-6-19-10-27z" class="H"></path><path d="M465 693c2-1 3 1 5 2 16 15 23 32 25 53 0 9 0 17-1 26l-2-2c1-10 1-20 0-30-2-19-12-37-27-49z" class="O"></path><path d="M484 700l-1-1h0c1 0 2 0 2 1 5 3 5 9 9 12l1 2 1-1v-2h2l3 28c0 2 0 4 1 7-1 1-1 2-1 4 0 1 0 0-1 1 0 3 1 3-1 5v2h-1c0-8 0-17-1-25-1-10-5-20-10-29h0c-1-2-2-3-3-4z" class="G"></path><path d="M499 740c0-1 1-2 1-3 0 0 0 2 1 2 0 2 0 4 1 7-1 1-1 2-1 4 0 1 0 0-1 1 0 3 1 3-1 5v-16z" class="I"></path><defs><linearGradient id="N" x1="496.337" y1="715.436" x2="500.131" y2="735.284" xlink:href="#B"><stop offset="0" stop-color="#a4a4a3"></stop><stop offset="1" stop-color="#c3c2c3"></stop></linearGradient></defs><path fill="url(#N)" d="M496 711h2l3 28c-1 0-1-2-1-2 0 1-1 2-1 3l-3-16c0-3-1-7-1-10l1-1v-2z"></path><path d="M455 663l1-1c2 1 3 2 5 3 4 4 7 8 11 13l1-1c-1-2-4-4-5-6-1-1-2-2-2-3v-1l6 7c1 1 1 1 2 1l-2-2c-1-2-1-3-2-4h1l3 3 1 1 1 2c1 1 1 2 2 3l3 5c1 1 1 2 2 3v1l1 3c1 1 2 2 3 4l1 4 1-1c2 5 5 10 5 15-4-3-4-9-9-12-4-8-9-15-15-23-4-6-9-10-15-14z" class="C"></path><path d="M472 678l1-1c-1-2-4-4-5-6-1-1-2-2-2-3v-1l6 7c1 1 1 1 2 1l2 2 3 6c1 0 1 1 2 2l4 8c1 3 2 5 4 7h-1c-2-2-3-5-5-7-1-2-2-4-4-6-2-3-5-6-7-9z" class="G"></path><defs><linearGradient id="O" x1="273.455" y1="451.376" x2="332.219" y2="473.258" xlink:href="#B"><stop offset="0" stop-color="#120f0c"></stop><stop offset="1" stop-color="#28292a"></stop></linearGradient></defs><path fill="url(#O)" d="M309 371c2-2 8-9 11-10v1c-6 9-10 18-15 27-2 3-5 7-6 11-3 6-5 13-6 19l2 2-1 2c0 3 1 6 0 8h-1c0 2 0 4 1 6v4c1 2 1 4 1 6l1 4c3 9 6 16 12 23 0 3 1 4 2 7h1l1 3-1 1c0 2 0 2-1 3 2 2 4 5 7 6h-2c-3-2-6-4-8-7v-1l-1 1-2-2h-1c-3-4-6-9-8-14-1-2-2-5-3-7 1 3 1 7 3 11v3 1c0 3 2 6 3 9 4 11 10 24 20 32 5 5 12 8 17 12-5-1-10-4-15-7-13-9-24-26-29-41-2-8-3-16-5-24-1 1-1 3-1 5-1 4-2 9-4 13 1-6 2-13 4-18 1-3 1-6 1-9h-1l-4 20c0-20 0-40 7-59 8-10 13-22 19-33 2-3 4-6 5-9h0l-2 2-1-1z"></path><path d="M304 485c-2-3-4-6-5-9-8-15-11-32-9-49 0-5 1-11 3-16 4-12 11-25 18-36 1-3 2-6 4-8 1-1 3-5 5-5-6 9-10 18-15 27-2 3-5 7-6 11-3 6-5 13-6 19l2 2-1 2c0 3 1 6 0 8h-1c0 2 0 4 1 6v4c1 2 1 4 1 6l1 4c3 9 6 16 12 23 0 3 1 4 2 7h1l1 3-1 1c0 2 0 2-1 3 2 2 4 5 7 6h-2c-3-2-6-4-8-7v-1l-1 1-2-2z" class="F"></path><path d="M305 481c-12-20-14-39-12-62l2 2-1 2c0 3 1 6 0 8h-1c0 2 0 4 1 6v4c1 2 1 4 1 6l1 4c3 9 6 16 12 23 0 3 1 4 2 7h1l1 3-1 1c0 2 0 2-1 3l-5-7z" class="B"></path><path d="M305 481v-1c1 0 1 0 1-1 1 1 1 2 2 3l3 3c0 2 0 2-1 3l-5-7z" class="C"></path><path d="M603 525c4 2 9 3 13 6-1 1-1 1-1 2v1l-1-1c-5 0-10-2-15-3h-1c-11-1-24 3-33 9l-3 3c2 1 4-1 6-1 0 1 0 1 1 2h1c7-2 13-2 20-1 5 2 9 6 12 11-7-5-13-9-23-8-6 1-12 4-17 8l1 48c2-5 5-11 6-16 0-3-2-6-1-9 0 0 1-1 2-1s2 0 3 1l1 1c1-1 2-1 3-2 0-2 0-4-1-5l-3-3c-1-2-1-3 1-5 0 1 1 1 0 2v2 1l2 1h1c0-2-1-3-1-4v-1c-1-2-3-2-5-2-1 1-2 1-3 2v4 2h-1-1c0-3 0-6 2-8s3-2 6-1 6 3 7 6c0 4-1 9-4 12-2 3-5 5-7 8-1 4-2 7-4 11-1 2-2 5-3 7-1 3 0 6 0 9l1 18c0 5 1 9 1 13v4c-1 1-2 1-3 2l-2-2h-2c0-2 1-4 0-6s-1-3-1-6v-2c-1 1-1 3-1 5-1 3-1 7-2 11-2 4-4 7-6 11 0-3 2-7 2-11l3-16c0-1 0-2-1-3v-9l2-18v-27h1c1-2 1-4 1-5h-1c-1 0-1 1-1 2-2 1-4 4-6 5-1 1-1 2-2 2-1 1-2 2-3 2l-2 2c-1 0-2 1-2 2l-2 1h0l-2 2v1c0-3 1-6 3-8 2-5 5-10 5-16l-5 11-4 1 2-6c1-3 2-6 3-8 2-4 6-7 8-10 5-6 9-13 15-18 3-3 7-5 11-7 9-4 17-5 27-4 1 0 2 0 3-1z" class="M"></path><path d="M569 576c2 0 1-1 2 0s1 1 0 2v5h-1c-1-3-2-5-1-7zm34-51c4 2 9 3 13 6-1 1-1 1-1 2v1l-1-1c-1-3-11-6-14-7 1 0 2 0 3-1z" class="Q"></path><path d="M562 542c2 1 4-1 6-1 0 1 0 1 1 2h1l-17 11-3 2h-1l4-3c0-1 1-1 2-2l12-6c-2-1-4 0-6 0s-3 3-5 3h0c2-2 3-4 5-5l1-1z" class="J"></path><path d="M552 631v-9l2-18v-27h1c0 18 1 38-2 57 0-1 0-2-1-3z" class="S"></path><path d="M456 536c3 2 8 3 11 5 2 2 6 5 6 7l4 1c2 3 3 5 4 7l1 2c1 2 2 3 3 5l3 6c1 2 2 5 3 7 1 3 3 6 4 9 3 7 4 13 6 20 1 3 2 7 2 10 1 2 1 5 1 7h2c1 2 1 6 1 8 1 0 1 0 1-1v-1l1-6c0 2 0 4 1 5 1 2 0 6 0 9v10c1 6 0 12 0 18 0 4 1 9 1 12-1 1-1 2-2 3v-1c0 4 1 7 0 11l-1-6c-1-4-3-7-4-11v-1c1 0 1 0 1-1 0-2-1-4-1-6-2-9-6-17-11-24-6-7-14-13-18-22h-1l1 12h0l-1 2c-1 2-1 5-1 7 0-2 0-3-1-4 0 5 0 10 1 16 2 8 6 16 8 24-1-2-3-6-5-8-4-3-12-10-13-15-1-2-1-4-2-6l1-1c0-1 0-2-1-3l1-29c0-6-4-13-6-19-1-2-2-6-3-8s-2-2-3-3c-4-4-6-8-6-14 0-2 1-6 3-8 3-2 5-2 8-2 1 1 2 1 2 2 2 2 1 5 1 7-3 3-5-1-8 0-2 1-1 4-2 6v2c0 3 5 7 7 9 0 0 0-1-1-1l-3-6 1-1h3c1 0 2 1 3 2 0 2-1 5-2 7h-1c2 7 5 14 7 21l2-31c0-8 0-16-1-24-3-2-6-4-10-5-11-3-21-1-30 4 3-3 6-6 9-8h1c1-1 1-1 2-1 0 0 1 0 1-1h1c5-1 10 1 15 2 2 1 4 0 5 1 4 2 9 4 11 8h0l1 1c2 1 5 2 6 4 5 5 7 10 11 15 0-2-3-6-4-8-2-2-3-4-4-6a57.31 57.31 0 0 0-11-11v-2c-1 0-2-2-3-2l-9-6 1-1z" class="R"></path><path d="M454 578c1 0 1 0 2 1 1 2 0 2 0 4h-1l-2-3 1-2z" class="Q"></path><path d="M463 652c4 1 8 7 10 10 1 2 2 3 3 5-4-3-12-10-13-15z" class="T"></path><path d="M451 561c2 0 2-1 4 0 1 1 2 2 1 4 0 2 0 2-1 3-1-1-2-1-3-1h-2v-1h0c2-3 2-1 4-1v-2l-1-1-2 1c-1 0-2 1-3 2 0 2-1 6-1 8 1 1 1 1 0 2v-2c-1-3-1-5 0-8 0-2 2-3 4-4z" class="Q"></path><path d="M497 602l4 15v3h0c-2-2-3-4-4-6v-1c-1-1-1-2-1-3-1-1-1-1-1-2l-2-2v-1l1-1 1 1 2-3z" class="D"></path><path d="M493 587l1-1c3 3 7 24 8 29h1c1 2 1 5 1 7 0 3 1 7 1 10 1 3 2 4 0 7h-1c-1-3-3-8-2-11 0-2-1-5-1-8v-3l-4-15-1-4c0-4-2-7-3-11z" class="S"></path><defs><linearGradient id="P" x1="463.73" y1="606.469" x2="480.788" y2="617.543" xlink:href="#B"><stop offset="0" stop-color="#414144"></stop><stop offset="1" stop-color="#63635f"></stop></linearGradient></defs><path fill="url(#P)" d="M472 635v-68 1c0 12 0 26 1 38 1 5 0 10 3 13 2 2 4 5 6 7l13 13-2 1c-6-7-14-13-18-22h-1l1 12h0l-1 2c-1 2-1 5-1 7 0-2 0-3-1-4z"></path><path d="M456 536c3 2 8 3 11 5 2 2 6 5 6 7l4 1c2 3 3 5 4 7l1 2c1 2 2 3 3 5l3 6c1 2 2 5 3 7 1 3 3 6 4 9 3 7 4 13 6 20 1 3 2 7 2 10h-1c-1-5-5-26-8-29l-1 1c-2-3-3-7-6-11-1-3-4-5-6-8-2-4-4-7-7-10s-7-4-10-7c1-1 3 1 4 1l1 1c2 1 5 2 6 4 5 5 7 10 11 15 0-2-3-6-4-8-2-2-3-4-4-6a57.31 57.31 0 0 0-11-11v-2c-1 0-2-2-3-2l-9-6 1-1z" class="E"></path><path d="M456 536c3 2 8 3 11 5 2 2 6 5 6 7l4 1c2 3 3 5 4 7l1 2c1 2 2 3 3 5h-1c0-1-1-2-2-3-1 1 0 1 0 3h0c-1-2-1-2-1-4-5-4-9-10-14-14-1 0-2-2-3-2l-9-6 1-1z" class="D"></path><path d="M473 548l4 1c2 3 3 5 4 7-4-2-6-4-8-8z" class="C"></path><path d="M495 639c3 3 5 7 7 12 3 8 5 16 6 25 0-5 0-10-1-15 0-6-2-11-3-17-4-14-7-28-17-39-4-4-8-7-12-11l3 2c11 6 21 20 24 32-1 3 1 8 2 11h1c2-3 1-4 0-7 0-3-1-7-1-10h2c1 2 1 6 1 8 1 0 1 0 1-1v-1l1-6c0 2 0 4 1 5 1 2 0 6 0 9v10c1 6 0 12 0 18 0 4 1 9 1 12-1 1-1 2-2 3v-1c0 4 1 7 0 11l-1-6c-1-4-3-7-4-11v-1c1 0 1 0 1-1 0-2-1-4-1-6-2-9-6-17-11-24l2-1z" class="N"></path><path d="M504 639h1l3 11v-3l1 2 1-1v17c-1-4-2-8-3-13-2-4-2-8-3-13z" class="T"></path><path d="M506 622c1 2 1 6 1 8 1 0 1 0 1-1v-1l1-6c0 2 0 4 1 5 1 2 0 6 0 9v10 2l-1 1-1-2v3l-3-11c2-3 1-4 0-7 0-3-1-7-1-10h2z" class="E"></path><path d="M509 622c0 2 0 4 1 5 1 2 0 6 0 9v10 2l-1 1-1-2c1-6 0-11-1-17 1 0 1 0 1-1v-1l1-6z" class="C"></path><path d="M427 412c3-1 5-2 7-3 4-1 9 0 13 0 5 0 10 1 15 2l1 1c2 0 5 3 7 4 3 2 6 6 8 8 1 2 2 4 4 5 1 1 2 1 2 2l1 2c1 0 1 1 2 2l1-1c4 7 7 15 9 22l11 40c0-8-1-16-3-24 0-2 0-6-1-8l1-2v-1c0-2-1-5-1-7-1-5 0-11 0-16l1-1 4 31 2 35-1 4v1c-1 3 0 7-1 11l-1-1v3c1 3 1 6 1 9 1 2 2 4 2 6v16 4c0 3-1 5-1 8v-1c0-1-1-2-1-3-1 0-1 0-1 1l2 16v3c0 2 0 4-1 6-1-1-1-2-2-3 0 2 0 3 1 5 1 4 1 8 2 12v2c1 6 1 13 1 20 0 4 0 9-1 14 0-3 1-7 0-9-1-1-1-3-1-5l-1 6v1c0 1 0 1-1 1 0-2 0-6-1-8h-2c0-2 0-5-1-7 0-3-1-7-2-10-2-7-3-13-6-20-1-3-3-6-4-9-1-2-2-5-3-7l-3-6c-1-2-2-3-3-5v-4l-1-1c-1-2-2-5-4-7-5-8-11-16-19-21l1-2 5 3-1-104c-1-2-3-2-5-3-12-5-25-6-38-1l5-5c1-1 1-1 2-1z" class="U"></path><path d="M476 516l1 1v10 4l-1 1c0-6-1-11 0-16z" class="L"></path><path d="M477 527c1 2 1 3 1 5v3c1 2 1 4 1 5s0 1 1 2v2h-2c-1-4-2-8-2-12l1-1v-4z" class="K"></path><path d="M474 489l2 1 2 12c-1 2-1 4-1 6l-1-3-1-1-1-15z" class="C"></path><path d="M504 518c0-1 0-2-1-3-1-2-1-4-2-6l-3-6-1-1c0-1-1-1-1-2l-1-1-2-2c-1 0-2-1-2-2v-1c3 2 6 5 7 8l1 1c2 2 3 4 4 7v1c1 1 1 0 1 2 0 1 1 2 1 4 1 1 1 1 1 3 1 0 1 1 2 1 1 3 1 6 1 9v1c-2-4-3-9-5-13z" class="G"></path><path d="M474 489v-54c1 1 1 1 1 2s0 2-1 4v12l2 37-2-1z" class="T"></path><path d="M508 561c-2-5-3-10-4-15-3-8-8-14-12-22 7 8 13 17 17 27 0 1 1 3 2 4v1c0 3-1 5-1 8v-1c0-1-1-2-1-3-1 0-1 0-1 1z" class="H"></path><path d="M509 551v-2-3c0-11-3-23-10-31-2-3-5-5-8-8h1c4 1 9 8 12 11 2 4 3 9 5 13v-1c1 2 2 4 2 6v16 4-1c-1-1-2-3-2-4z" class="N"></path><defs><linearGradient id="Q" x1="448.096" y1="416.937" x2="435.904" y2="406.063" xlink:href="#B"><stop offset="0" stop-color="#bebdc0"></stop><stop offset="1" stop-color="#e1dfe1"></stop></linearGradient></defs><path fill="url(#Q)" d="M427 412c3-1 5-2 7-3 4-1 9 0 13 0 6 2 12 4 18 7 1 1 3 2 4 3l-1 1c-10-5-20-9-32-8-3 0-7 1-10 1h-1c1-1 1-1 2-1z"></path><path d="M478 502c1 4 2 8 2 12h-1c-1 1 0 5 0 7 1 5 1 10 2 15v3c0 1 0 1 1 2v2 2l1 1c0 3 2 6 1 9 0-1-1-2-1-3l-3-8v-2c-1-1-1-1-1-2s0-3-1-5v-3c0-2 0-3-1-5v-10l-1-1c0-2 0-6 1-8 0-2 0-4 1-6z" class="O"></path><path d="M484 555c1-3-1-6-1-9l-1-1v-2-2c-1-1-1-1-1-2v-3c-1-5-1-10-2-15 0-2-1-6 0-7h1l8 43h0l-1 1v-1 3 1h0c-1 0-1-1-2-2 0-2-1-2-1-4z" class="F"></path><defs><linearGradient id="R" x1="497.01" y1="472.971" x2="495.49" y2="487.529" xlink:href="#B"><stop offset="0" stop-color="#7b797a"></stop><stop offset="1" stop-color="#9e9e9c"></stop></linearGradient></defs><path fill="url(#R)" d="M500 489c-3-5-7-9-11-13-4-3-11-6-13-10 6 3 12 7 17 10v1c-1-4-4-9-7-12-1-2-3-4-4-7 1 1 3 3 4 5 5 5 9 11 13 17h1l-1 1h0l1 2c0 1 0 1 1 2v1c0 2 0 2-1 3z"></path><path d="M509 622c0-12 0-24-4-36-1-5-4-10-6-15h1c2 3 3 7 6 11-3-9-6-16-9-24 6 6 9 17 10 25 0 2 0 3 1 5 1 4 1 8 2 12v2c1 6 1 13 1 20 0 4 0 9-1 14 0-3 1-7 0-9-1-1-1-3-1-5z" class="E"></path><path d="M480 544l3 8c0 1 1 2 1 3 0 2 1 2 1 4 1 1 1 2 2 2h0v-1-3 1l1-1h0c3 10 7 20 10 30 3 11 6 23 8 35h-2c0-2 0-5-1-7 0-3-1-7-2-10-2-7-3-13-6-20-1-3-3-6-4-9-1-2-2-5-3-7l-3-6c-1-2-2-3-3-5v-4l-1-1-3-9h2z" class="I"></path><path d="M447 409c5 0 10 1 15 2l1 1c2 0 5 3 7 4 3 2 6 6 8 8 1 2 2 4 4 5 1 1 2 1 2 2l1 2c1 0 1 1 2 2l1-1c4 7 7 15 9 22l-1 3c-1-4-3-8-5-12-1-2-3-5-4-7-5-8-11-15-19-20l1-1c-1-1-3-2-4-3-6-3-12-5-18-7z" class="H"></path><defs><linearGradient id="S" x1="490.74" y1="488.345" x2="516.059" y2="498.943" xlink:href="#B"><stop offset="0" stop-color="#828381"></stop><stop offset="1" stop-color="#b8b5b6"></stop></linearGradient></defs><path fill="url(#S)" d="M500 480l-3-9c-2-6-5-12-9-18-3-6-7-12-11-18l-1-1c-1-1-3-2-4-3l1-1h0l1 1c3 2 5 5 7 8l7 10c2 2 3 3 3 5l1-1c0-2-1-3-1-6 2 4 4 8 5 12l1-3 11 40c0-8-1-16-3-24 0-2 0-6-1-8l1-2v-1c0-2-1-5-1-7-1-5 0-11 0-16l1-1 4 31 2 35-1 4v1c-1 3 0 7-1 11l-1-1c-1-10-4-19-8-29 1-1 1-1 1-3v-1c-1-1-1-1-1-2l-1-2h0l1-1z"></path><path d="M491 447c2 4 4 8 5 12s2 7 3 11c-3-7-7-13-11-21 2 2 3 3 3 5l1-1c0-2-1-3-1-6z" class="C"></path><path d="M504 454c-1-5 0-11 0-16l1-1 4 31 2 35-1 4c1-5 0-11 0-15-1-10-3-20-5-30v-1c0-2-1-5-1-7zm-90-276l1-2c1-13 6-24 16-32h1l-5 6c-7 9-10 21-8 33v1c1 6 3 12 7 17 3 5 9 10 16 11h0 2c7 1 17 0 23-5 5-4 8-11 11-16 5-7 9-13 12-21 3-5 5-11 7-17 4-9 7-18 9-27 4-13 5-27 7-40 3 27 7 54 19 79 3 6 7 12 11 18 3 4 5 9 7 14-3-4-6-6-10-9-8-5-13-13-16-22l-2-6-1 2h0c1 2 2 5 3 7 0 2 2 5 3 7l8 10-2 1c1 2 3 4 4 6l-1 1c-1 0-1-1-2-1-1-1-1-2-2-3 0-1-1-1-2-2v1c7 14 17 25 33 30 1 1 2 1 4 1l-1-1h0 3c0 1 4 1 4 1v1c1 1 3 1 5 2h1v1h2l-2 1c-2-1-5-1-8-2-8-2-15-4-22-9l1 1c3 5 5 10 6 16h0l-1 1c-1 1-1 2-2 3v-1c0-3 0-4-2-6-10-11-22-18-29-32-4-6-5-12-8-18 1 20 9 40 25 54 11 11 27 18 42 22 19 4 40 4 60 4 10 0 22 0 32-1 6 0 15-3 19-8 2-2 3-6 3-9-1-1-2-3-4-4s-6-1-8-1c-2 1-3 2-3 3-1 2-1 3 0 5v1c-2-2-2-3-2-4 0-2 1-4 2-5 3-2 5-2 8-2 3 1 5 2 7 5 2 2 2 5 1 7-1 5-4 8-8 11l-9 3c37 0 74 0 110-4 16-1 31-3 46-7 11-3 22-7 33-11l22-8-37 26c-2-1-3-1-5 0-1 0-2 1-4 2l-3 1-3 1-3 1c-1 1 1 0-1 1h-1l-1 1v1c-1 1-2 1-3 1-5 2-11 5-16 9-3 0-5 1-8 1 0 0-2 0-2 1-6 1-11 1-16 1h-22l-89-1-18-1c-8 0-15 0-22 2l-16 3-25 1h-16l-7-1-3-1h3c-2 0-3-1-5-2h0l-16-3c-13-2-25-9-32-19-2-2-3-3-3-5l-1-2-1-21c-2-3 1-9-2-12v8 1l-1 17c0 3 0 6-1 9-2 6-6 11-9 14-8 8-18 11-28 14-8 2-14 4-22 4-4 1-9 0-13 0-2 0-5 1-6 1-1-1-1-1-2-1-1-1-2-1-3-2-3-1-8 0-11 0-8 0-16 0-25-2l-9-2c-2 0-5-1-7-1-4-1-8-2-11-3-6-1-13 0-19 0 5-2 12 0 17 0l2-2c3 0 5 1 7 1l-4-2-6-3-2-2c-1-1-2-2-3-2v-1c-3-1-5 0-8-1h0-3v-1l-1 1c-2-1-3-1-5-2-5-1-9-4-11-9-2-2-2-5-1-7 1-3 4-6 6-7 3-1 7-1 9 0s3 2 4 4v5h-1c0-1 0-3-1-5 0-1-1-2-2-2-3-1-6-1-8 0-3 2-5 4-5 7-1 2 0 4 1 6 4 5 12 6 18 7 15 2 29 2 44 2 31 0 65-1 90-21 12-10 18-21 23-35 3-7 6-15 7-23-6 16-13 30-26 42-5 4-10 7-14 12-1 1-1 0-3 1l-12 6c-6 3-11 4-17 6-3 1-5 2-8 1l1-1h1c0-2-1-3-1-5h2c1-1 3-1 4 0l1 3 10-5c8-4 17-9 23-17l-2 1c-5 4-10 5-16 6-3 0-7 1-10 2-2 1-4 2-6 4l1-8-17-12 1-1c-4-4-7-9-8-14 0-2 0-2-2-4h0c-2-3 0-13 0-18z" class="B"></path><path d="M567 276l17 2-1 1c-1 0-2 0-4 1l-7-1-3-1h3c-2 0-3-1-5-2z" class="H"></path><path d="M427 274l18-2 2 2-17 2c-1-1-2-1-3-1v-1z" class="N"></path><path d="M397 273c7 1 14 2 21 1h9v1c1 0 2 0 3 1-10 1-22 0-33-1v-2z" class="S"></path><path d="M571 271v-1c1 0 2 1 3 0-2 0-2 0-3-1h-1v-1c3 0 6 1 9 1 1 2 3 2 5 2h13c2 0 4 0 6 1-10 1-23 2-32-1z" class="O"></path><path d="M512 223l-1-12c1-2 0-6 1-7 2 2 1 4 2 7v4c2 8 3 15 7 21l-2 1c-1-2-2-4-2-6-1-1-2-4-2-5h-1c-2-3 1-9-2-12v8 1z" class="T"></path><path d="M568 264l29 4 1 1c-5 0-11 0-16 1l-3-1c-3 0-6-1-9-1-2-1-2-1-3-2l1-2z" class="H"></path><path d="M584 278c17 1 34 0 51-3l1 1-16 3-25 1h-16c2-1 3-1 4-1l1-1z" class="O"></path><path d="M640 267l14-2c2-1 8-3 10-2-6 2-12 3-19 4l-20 4c-3 0-5-1-8-1h0l-2-1c-2-1-7 1-9-1h13 5c6 0 11 0 16-1z" class="N"></path><defs><linearGradient id="T" x1="515.097" y1="139.374" x2="507.903" y2="145.126" xlink:href="#B"><stop offset="0" stop-color="#636260"></stop><stop offset="1" stop-color="#797778"></stop></linearGradient></defs><path fill="url(#T)" d="M500 186c0-2 0-3 1-5 4-8 7-19 9-28 1-6 1-13 2-19 0-4 0-9 1-13 1 7 1 17 0 24-3 13-5 24-10 35-1 2-1 4-3 6z"></path><path d="M597 268h9c2 2 7 0 9 1l2 1h0c3 0 5 1 8 1-6 1-11 2-17 2-1 0-4 0-5-1-2-1-4-1-6-1h-13c-2 0-4 0-5-2l3 1c5-1 11-1 16-1l-1-1z" class="C"></path><path d="M597 268h9c2 2 7 0 9 1l2 1h0c-11 0-24 1-35 0 5-1 11-1 16-1l-1-1z" class="O"></path><path d="M359 263l9 4h1c2 1 3 2 5 2l4 1c1 1 1 1 2 1 5 1 10 1 14 2h3v2c-2 0-4-1-5-1h-5c-1-1-3 0-5 0v1c-2 0-5-1-7-1-4-1-8-2-11-3-6-1-13 0-19 0 5-2 12 0 17 0l2-2c3 0 5 1 7 1l-4-2-6-3-2-2z" class="D"></path><path d="M619 268l1-1h7c-3-1-6 0-8-1-3-1-5 0-8 0-3-1-5 0-8-1h-2-5c-2-1-5 0-7-1h-3 0l-1-1v-1l1 1c2-1 3-1 5 0h5l8 1h9 3 22c1-1 2 0 4 0v-1h4c1-1 2 0 3 0l-1 1h0c-1 1-2 1-3 1h-1-1c-1 0-2 0-3 1v1c-5 1-10 1-16 1h-5z" class="G"></path><path d="M513 145c1 10-1 17-4 26-1 5-3 11-7 16 0 1-1 2-2 3l-1 2-3 7c-1 1-2 3-4 4l-1 2-1 1c-3 5-9 8-12 13l-1 1-1 1-1 3-1 3c-1 1-1 1-1 2-1-1-1-2-1-3l1-1 1-1v-1-2l3-3 5-6c3-2 5-5 8-8 1-2 2-4 4-5v-1l2-4c1-1 2-3 3-5 0-1 1-2 1-3h0c2-2 2-4 3-6 5-11 7-22 10-35z" class="J"></path><path d="M462 276l22-6c5-2 12-6 16-10v1c-6 8-16 11-26 14-7 2-14 4-21 5-1 0-1 0-2 1h0c-4 1-9 0-13 0-2 0-5 1-6 1-1-1-1-1-2-1-1-1-2-1-3-2 4 0 8 0 12-1 6-1 13-3 19-3 1 1 2 1 3 1h1z" class="N"></path><path d="M439 278c6-1 13-3 19-3 1 1 2 1 3 1h1c-8 2-15 4-23 2z" class="C"></path><path d="M414 196h0c-2-3 0-13 0-18 1 11 5 22 13 30 4 3 8 5 13 7 7 3 14 6 22 5h6 0c-2 0-2 0-3 1h-3c-4 1-7 0-11 0-11-2-21-7-28-15 0 2 4 6 6 8 7 7 14 10 24 11h0c-3 0-7 1-10 0-6-1-14-7-19-11-4-4-7-9-8-14 0-2 0-2-2-4z" class="K"></path><defs><linearGradient id="U" x1="476.123" y1="250.639" x2="480.153" y2="264.833" xlink:href="#B"><stop offset="0" stop-color="#737271"></stop><stop offset="1" stop-color="#9c9a9a"></stop></linearGradient></defs><path fill="url(#U)" d="M445 272c21-3 42-12 55-29 5-8 8-17 10-26 1 1 1 1 1 2v1c-1 1-1 5-2 7-5 16-15 30-29 38-10 5-22 7-33 9l-2-2z"></path><defs><linearGradient id="V" x1="545.805" y1="237.636" x2="550.135" y2="272.871" xlink:href="#B"><stop offset="0" stop-color="#4f4e4c"></stop><stop offset="1" stop-color="#8c8b8b"></stop></linearGradient></defs><path fill="url(#V)" d="M521 236c11 17 29 24 47 28l-1 2c1 1 1 1 3 2v1h1c1 1 1 1 3 1-1 1-2 0-3 0v1c-5-1-10-2-14-4-11-3-23-9-30-18-3-4-5-8-8-12l2-1z"></path><path d="M516 152l1-1c0-1 1-5 0-6h0c2 1 2 2 2 3v2l1 1v1c0 1 0 2 1 3v2c0 1 1 2 1 3l-1 2h0c1 2 2 5 3 7 0 2 2 5 3 7l8 10-2 1c1 2 3 4 4 6l-1 1c-1 0-1-1-2-1-1-1-1-2-2-3 0-1-1-1-2-2v1c7 14 17 25 33 30 1 1 2 1 4 1l-1-1h0 3c0 1 4 1 4 1v1c1 1 3 1 5 2h1v1h2l-2 1c-2-1-5-1-8-2-8-2-15-4-22-9l1 1c3 5 5 10 6 16h0l-1 1v-1-2c-1-4-4-11-7-15l-12-12c-4-5-7-11-10-16-6-13-11-25-11-39h0c0 2 0 3 1 5z" class="H"></path><defs><linearGradient id="W" x1="523.094" y1="154.231" x2="517.126" y2="170.533" xlink:href="#B"><stop offset="0" stop-color="#13110b"></stop><stop offset="1" stop-color="#2e2c2b"></stop></linearGradient></defs><path fill="url(#W)" d="M516 152l1-1c0-1 1-5 0-6h0c2 1 2 2 2 3v2l1 1v1c0 1 0 2 1 3v2c0 1 1 2 1 3l-1 2h0c1 2 2 5 3 7 0 2 2 5 3 7l8 10-2 1c1 2 3 4 4 6l-1 1c-1 0-1-1-2-1-1-1-1-2-2-3 0-1-1-1-2-2v1c-1-1-1-2-2-3l-4-9h-1c-3-8-6-16-7-25z"></path><path d="M527 176l8 10-2 1c1 2 3 4 4 6l-1 1c-1 0-1-1-2-1-1-1-1-2-2-3 0-1-1-1-2-2v1c-1-1-1-2-2-3l-4-9 1-1 3 6v-1c0-1-1-3-1-5h0z" class="C"></path><path d="M832 262v1c-1 1-2 1-3 1-5 2-11 5-16 9-3 0-5 1-8 1 0 0-2 0-2 1-6 1-11 1-16 1h-22l-89-1-18-1c-8 0-15 0-22 2l-1-1c4-1 8-3 13-4h16 24l84-1c14 0 27 0 41-2 6-2 13-4 19-6z" class="F"></path><path d="M832 262l1-1h1c2-1 0 0 1-1l3-1 3-1 3-1c2-1 3-2 4-2 2-1 3-1 5 0-10 8-20 17-29 26-18 19-31 40-41 64-4 12-8 25-10 38-3 29 1 62 8 91-6-11-9-22-13-34l-10-29c-4-10-7-21-12-30-9-17-25-32-42-40-7-4-15-7-24-9-17-5-36-7-55-5-7 0-15 2-23 2-13 1-26 0-39 0v35c0 2-1 8 0 10l2 1c-1 1-2 3-2 4-1 4 0 9-1 13l-11 16c-1 2-3 5-4 8-1 0-1 1-1 2-1 0-1 1-2 2v3l-3 4c0 3-1 6-2 9-1 1-2 2-2 3s1 2 1 2l-14 34-2-2c-2 9-4 18-4 28 0 3-1 6 0 9l-1 1v-28c0-7 0-14 2-20 1-4 2-8 4-12l3-9h-1c-2 3-3 8-5 12h0c-1 2-1 6-2 8l-1-1c-1-2 0-7 0-9v-2-5c1-1 0-4 0-5l-2-2v-1c-1-3 1-7 1-11 0-1 0-3 1-4l1-20c2-14 9-33 20-43 5-4 10-7 15-11-4 2-9 4-13 7-4 2-7 5-10 8 3-9 8-17 13-25 3-4 6-7 9-11-14 11-21 25-28 41-2 4-4 10-6 14-1-7-1-15 0-22l-1-2c-1-3-1-16 0-18v-1c0-2 0-3-1-4-1-5-1-15 0-20l1-3c-1-3-1-7-1-9l1-2c3-3 8-4 11-6 11-3 21-4 31-5h23 13 7c2-1 11-1 14-1 0 0-2-1-3-1h10 11 15c1-1 10 0 13-1 2 0 4 0 7-1h-7c1 0 3 0 4-1h4c1-1 1-1 3-2h0-1c-3-1-7 0-10-1h-1 0-11l-34-1h0c2 0 4 0 6-1l-2-1 16-3c7-2 14-2 22-2l18 1 89 1h22c5 0 10 0 16-1 0-1 2-1 2-1 3 0 5-1 8-1 5-4 11-7 16-9 1 0 2 0 3-1v-1z" class="R"></path><path d="M649 288h5-1c-3 1-9 0-11 2h0c3 0 5 0 8 1l3 1-16-1-3-3h15z" class="J"></path><path d="M766 326h0c2 0 2 0 3 1 0 0-3 0-3 1-2 0-3 1-5 2 1 2 3 3 5 3s2-2 4-3c0 1 1 1 2 1 0 1 0 1 1 2 1 0 1 0 1-1s1-2 1-3c-1-1-1-1-2-1-1-1-2-1-3-2l4 1c1 1 1 2 1 3-1 3-2 4-5 6-3-2-7-3-10-5 0-1 0-1 1-1 0-2 3-3 5-4z" class="C"></path><path d="M755 334c6 7 11 16 14 25v1c-2-3-3-6-5-8-2-4-4-7-7-10l1-2h-1c-1-3-2-3-2-6z" class="S"></path><path d="M637 291l16 1h6c1 0 2 1 3 1 4 1 9 1 13 3l-2 1-16-3h-9c-3-1-7-2-10-3h-1 0z" class="O"></path><path d="M622 280l38-1c-4 2-6 2-10 3l-34-1h0c2 0 4 0 6-1z" class="S"></path><defs><linearGradient id="X" x1="672.759" y1="283.67" x2="671.128" y2="276.857" xlink:href="#B"><stop offset="0" stop-color="#4e4a4b"></stop><stop offset="1" stop-color="#6d6d6c"></stop></linearGradient></defs><path fill="url(#X)" d="M660 279c6 0 28-2 33 2 2 0 5 0 7 1h-39 0-11c4-1 6-1 10-3z"></path><path d="M803 275c0-1 2-1 2-1 3 0 5-1 8-1-4 3-9 6-13 10-4 5-7 12-12 17-1 1-2 2-4 2 5-5 7-11 11-17 2-4 5-6 8-10z" class="E"></path><path d="M745 313h1c1-1 3 0 5 1 3 1 8 2 11 4 2 1 4 2 5 4l-1 1c0 1-1 2-2 2-1 1-2 2-2 3h-2l-2-2c-4-2-7-5-10-8l1-1c2 1 3 3 5 5v-1l-1-2c0-1 0-2 1-3-2-1-4-2-6-2l-3-1z" class="C"></path><path d="M757 318h3c3 2 4 3 4 6l-1 1c-1 1-2 1-3 1h-1c-1-3-3-5-3-7l1-1z" class="J"></path><path d="M582 290h13 7c1 1 2 2 4 1l21 6c9 4 19 8 25 16l2 3c-2-2-4-5-7-7-15-11-36-15-55-17h0c-3 0-8 0-10-2z" class="O"></path><path d="M522 473c3-11 7-20 11-31 3-5 5-10 8-15 0 3-1 6-2 9-1 1-2 2-2 3s1 2 1 2l-14 34-2-2z" class="F"></path><defs><linearGradient id="Y" x1="694.295" y1="291.663" x2="690.353" y2="282.192" xlink:href="#B"><stop offset="0" stop-color="#837f85"></stop><stop offset="1" stop-color="#adaea9"></stop></linearGradient></defs><path fill="url(#Y)" d="M666 285l48 1h-4c1 1 2 1 3 1l1 1h9c-8 1-17 0-25 0h-44-5c1-1 10 0 13-1 2 0 4 0 7-1h-7c1 0 3 0 4-1z"></path><path d="M675 296c14 3 27 7 40 12 3 1 7 3 9 4l2 2-1 1h-1-3c-4-1-9-5-14-6-11-5-22-9-34-12l2-1z" class="F"></path><defs><linearGradient id="Z" x1="552.334" y1="302.317" x2="538.008" y2="321.566" xlink:href="#B"><stop offset="0" stop-color="#898888"></stop><stop offset="1" stop-color="#a8a8a9"></stop></linearGradient></defs><path fill="url(#Z)" d="M529 309l6-3c12-2 28 3 38 8 5 1 9 4 13 6-13-4-25-10-39-11-5 0-10 0-14 2l-5 3-1-1c0-1 0-1 1-3h-1l2-1z"></path><path d="M517 340v1c1 0 1 0 1-1 3-8 9-18 17-22 4-2 8-3 12-2-1 0-1 0-2 1-7 1-12 5-16 10-4 6-8 14-9 22-1 3-1 7-2 11l-1-2c-1-3-1-16 0-18z" class="O"></path><path d="M616 289h4l14 2c1 0 1 1 2 1l12 3c1 1 3 1 4 2h-1c-3 0-6-1-9-2 0 1 3 2 4 2 3 1 9 3 12 6-1 0-2 0-2-1-12-5-25-6-37-9-5 0-9-2-13-2-2 1-3 0-4-1 2-1 11-1 14-1z" class="E"></path><defs><linearGradient id="a" x1="752.735" y1="328.443" x2="723.325" y2="319.264" xlink:href="#B"><stop offset="0" stop-color="#6f6d6d"></stop><stop offset="1" stop-color="#9e9e9e"></stop></linearGradient></defs><path fill="url(#a)" d="M724 312c12 5 23 12 31 22 0 3 1 3 2 6h1l-1 2c-10-12-23-20-36-27h3 1l1-1-2-2z"></path><defs><linearGradient id="b" x1="516.447" y1="411.196" x2="530.896" y2="427.939" xlink:href="#B"><stop offset="0" stop-color="#83817f"></stop><stop offset="1" stop-color="#b5b6b8"></stop></linearGradient></defs><path fill="url(#b)" d="M517 422c1-2 3-8 4-10 5-13 9-27 18-39-2 5-5 10-7 14-2 5-4 10-6 16-1 2-2 5-2 7l7-13c2-5 5-12 9-15-6 11-10 22-15 34-2 7-5 14-6 21 1 2 2 4 2 6l-1 1v10h0c-1 2-1 6-2 8l-1-1c-1-2 0-7 0-9v-2-5c1-1 0-4 0-5l-2-2v-1c-1-3 1-7 1-11 0-1 0-3 1-4z"></path><path d="M519 437c1 2 2 4 2 6l-1 1v10h0c-1 2-1 6-2 8l-1-1c1-8 1-16 2-24z" class="M"></path><path d="M714 286l33-1h23 13c1 1 1 0 1 1v2c-1 3-5 7-8 8s-9-2-12-3v-1c-13-4-27-4-41-4h-9l-1-1c-1 0-2 0-3-1h4z" class="C"></path><path d="M713 287c14 0 29-1 43 1 2 1 6 2 8 3v1c-13-4-27-4-41-4h-9l-1-1z" class="N"></path><defs><linearGradient id="c" x1="728.97" y1="271.682" x2="723.53" y2="296.318" xlink:href="#B"><stop offset="0" stop-color="#c4c9c6"></stop><stop offset="1" stop-color="#fff8fb"></stop></linearGradient></defs><path fill="url(#c)" d="M661 282h39 64 14c3 1 6 0 8 1l1 1c-1 2-2 3-3 4v-2c0-1 0 0-1-1h-13-23l-33 1-48-1h4c1-1 1-1 3-2h0-1c-3-1-7 0-10-1h-1z"></path><path d="M623 288h11l3 3h0 1c3 1 7 2 10 3h9l-4 1h0c9 1 17 3 25 6 6 1 12 4 18 7 20 8 38 17 52 34l9 12h-1v1c-10-15-24-28-39-37-8-4-16-7-23-10-10-4-19-7-30-9-4-1-8-2-12-2 4 2 9 2 14 4 4 1 8 3 11 4 5 1 9 2 13 4 13 5 26 13 37 22 3 3 8 7 11 12 1 2 3 3 5 5l3 5v1l-2-2-4-5c-4-5-9-10-13-14-4-3-8-6-11-9-8-5-17-9-26-13l-22-8c-3-1-6-1-10-2-2-1-4-2-7-3v-1h1c-1-1-3-1-4-2l-12-3c-1 0-1-1-2-1l-14-2h-4s-2-1-3-1h10z" class="T"></path><path d="M613 288h10l1 1h3-7-4s-2-1-3-1z" class="C"></path><defs><linearGradient id="d" x1="739.475" y1="309.903" x2="742.638" y2="299.94" xlink:href="#B"><stop offset="0" stop-color="#6f6e6e"></stop><stop offset="1" stop-color="#908f90"></stop></linearGradient></defs><path fill="url(#d)" d="M659 292c11-2 23 0 33 1 20 2 39 4 58 9 11 3 21 6 31 12l3 3c1 1 1 1 1 3 0 1-1 6-3 7-1 0-1 0-2-1-4-3-7-7-11-10-5-4-11-7-18-9-10-4-20-6-31-8-19-3-39-8-58-6-1 0-2-1-3-1z"></path><path d="M780 326v-1c0-6-6-11-10-15 3 1 8 3 11 5 1 1 2 2 3 2h0c1 1 1 1 1 3 0 1-1 6-3 7-1 0-1 0-2-1z" class="C"></path><defs><linearGradient id="e" x1="553.239" y1="332.071" x2="552.201" y2="291.033" xlink:href="#B"><stop offset="0" stop-color="#908e8e"></stop><stop offset="1" stop-color="#b5b3b4"></stop></linearGradient></defs><path fill="url(#e)" d="M559 290h23c2 2 7 2 10 2l9 3v1c-3 0-4-1-6-2h-9c-14-1-30 2-44 7-4 2-9 4-12 7l-1 1-2 1h1c-1 2-1 2-1 3l1 1c-6 5-8 10-10 17 0 3 0 5-1 8 0-2 0-3-1-4-1-5-1-15 0-20l1-3c-1-3-1-7-1-9l1-2c3-3 8-4 11-6 11-3 21-4 31-5z"></path><path d="M517 312c1-5 2-7 6-9 5-3 10-4 16-6 1 0 4-1 5-1-9 4-18 8-24 17-1 2-2 5-3 7l-1-5 1-3z" class="B"></path><path d="M512 223v-1-8c3 3 0 9 2 12l1 21 1 2c0 2 1 3 3 5 7 10 19 17 32 19l16 3h0c2 1 3 2 5 2h-3l3 1 7 1h16l25-1 2 1c-2 1-4 1-6 1h0l34 1h11 0 1c3 1 7 0 10 1h1 0c-2 1-2 1-3 2h-4c-1 1-3 1-4 1h7c-3 1-5 1-7 1-3 1-12 0-13 1h-15-11-10c1 0 3 1 3 1-3 0-12 0-14 1h-7-13-23c-10 1-20 2-31 5-3 2-8 3-11 6l-1 2c0 2 0 6 1 9l-1 3c-1 5-1 15 0 20 1 1 1 2 1 4v1c-1 2-1 15 0 18l1 2c-1 7-1 15 0 22 2-4 4-10 6-14 7-16 14-30 28-41-3 4-6 7-9 11-5 8-10 16-13 25 3-3 6-6 10-8 4-3 9-5 13-7-5 4-10 7-15 11-11 10-18 29-20 43l-1 20c-1 1-1 3-1 4 0 4-2 8-1 11v1c0 1 0 2-1 3h0c-1 5 0 10-2 14 0 3 0 5-1 7h0c0-1-1-3 0-5 0-1 0-2 1-3v-2l-3 3c0 1 0 2 1 3 0 2 0 8-1 10l-4-31-1 1c0 5-1 11 0 16 0 2 1 5 1 7v1l-1 2c1 2 1 6 1 8 2 8 3 16 3 24l-11-40c-2-7-5-15-9-22l-1 1c-1-1-1-2-2-2l-1-2c0-1-1-1-2-2-2-4-4-9-7-13-2-5-6-9-9-13 1-3-1-2 0-4h0l1-1c0 1 1 3 2 4l1-1 5 5c0-2-1-5-3-8h0c-2-3-5-8-7-9-1 0 0 0-1-1h0l-2-4c2-3 1-9 2-13 0-9 0-18-1-27v-15c-14 1-30 2-45 0-13-1-26-3-40-3-17 0-35 3-51 7-23 7-44 23-57 44-5 8-8 18-11 27l-12 34c-4 12-8 24-14 35 0-3 1-5 2-8l2-14c4-24 7-49 4-73-1-10-2-20-5-29-11-37-36-67-66-90-14-11-29-19-42-30 6 2 11 4 16 6 12 5 24 9 36 12 43 11 87 11 131 12h23c3 0 7 1 9 0h1l1-1v1h3 0c3 1 5 0 8 1v1c1 0 2 1 3 2l2 2 6 3 4 2c-2 0-4-1-7-1l-2 2c-5 0-12-2-17 0 6 0 13-1 19 0 3 1 7 2 11 3 2 0 5 1 7 1l9 2c9 2 17 2 25 2 3 0 8-1 11 0 1 1 2 1 3 2 1 0 1 0 2 1 1 0 4-1 6-1 4 0 9 1 13 0 8 0 14-2 22-4 10-3 20-6 28-14 3-3 7-8 9-14 1-3 1-6 1-9l1-17z" class="R"></path><path d="M380 279c3 0 6 0 9 1 2 0 3 0 4 1l1 1h-6c-1-1-5 0-6-1l-2-2z" class="T"></path><path d="M343 265c6 0 15-1 21 3l-1 1h0l-1-1c-4-1-9-1-14-1l1-1h-1c-2 0-3 0-5-1h0z" class="C"></path><path d="M504 464c-1-4-2-7-3-11 0-2 0-4 1-6 1 1 2 5 2 7s1 5 1 7v1l-1 2z" class="D"></path><path d="M307 294l19-2h4v1l-21 3h-1 2c1-1 2-1 3-1s1-1 2-1l1 1c1-1 2-1 3-1 2 0 2-1 3-1 2 0 1 1 2 0h-4c-4 1-9 2-13 1h0z" class="F"></path><path d="M326 292l25-2 2 1v1h-11c-4 0-8 2-12 1v-1h-4z" class="H"></path><path d="M493 309l1-1 6 3c2 1 3 2 4 4s3 2 5 3l1 3h-1l-2-2c-1-1-1-1-2-1l1 2v1c-4-5-7-9-13-12z" class="F"></path><path d="M491 363l-14-7c-3-2-6-3-9-6l22 9-2 1c1 0 0 0 1 1 1 0 1 1 2 2z" class="H"></path><path d="M393 289h23v1c-1 1 0 1-1 1h-1l-8 1 1-1c-5-1-11 1-15 0l1-2z" class="N"></path><path d="M363 287l20 1-17 3c-4 0-9 1-13 1v-1l-2-1h6c3 0 7-1 11-2h-9 0l4-1z" class="E"></path><path d="M406 294h2c-3 2-9 1-12 4h1l1-1h1 0 2c-12 5-22 10-32 18v-1l4-4c9-8 20-12 31-16h2z" class="H"></path><defs><linearGradient id="f" x1="246.939" y1="371.092" x2="255.794" y2="354.986" xlink:href="#B"><stop offset="0" stop-color="#4e4e4d"></stop><stop offset="1" stop-color="#747372"></stop></linearGradient></defs><path fill="url(#f)" d="M256 353c0 2-2 6-2 8l-4 14c0 2-1 6-2 8 0-9 2-17 4-26 1-5 2-10 5-14h0c0 3-4 9-3 11h1l1-1h0z"></path><path d="M506 321v-1l-1-2c1 0 1 0 2 1l2 2h1l1 2c1 1 1 4 1 6v17c-2-2-2-3-3-5 0-7 0-13-3-20z" class="L"></path><path d="M378 295h1c1 0 6 0 7-1v-1l4-1h12l-1 1c2 0 4 0 5 1h-2c-5-1-11 0-16 1-18 4-37 9-53 20l-4 4v-1c7-7 16-12 26-16 0 0 3-1 4-2 6-2 11-3 17-5z" class="N"></path><path d="M343 278l22 1c5 0 10 1 15 0l2 2c1 1 5 0 6 1h-17-27 0l-1-1h3v-1l-4-1h1v-1z" class="E"></path><path d="M343 279l21 2c-6 1-13 1-20 1l-1-1h3v-1l-4-1h1z" class="G"></path><path d="M493 309c-2-1-5-2-7-2-15-3-27 4-38 11 4-4 9-8 15-11 10-6 21-5 32-1h1c1 2 3 3 4 5l-6-3-1 1z" class="O"></path><defs><linearGradient id="g" x1="473.057" y1="419.135" x2="482.1" y2="416.948" xlink:href="#B"><stop offset="0" stop-color="#898887"></stop><stop offset="1" stop-color="#a4a2a3"></stop></linearGradient></defs><path fill="url(#g)" d="M466 403c1-3-1-2 0-4h0l1-1c0 1 1 3 2 4l1-1 5 5 13 28-1 1c-1-1-1-2-2-2l-1-2c0-1-1-1-2-2-2-4-4-9-7-13-2-5-6-9-9-13z"></path><defs><linearGradient id="h" x1="237.533" y1="314.729" x2="253.94" y2="317.432" xlink:href="#B"><stop offset="0" stop-color="#4b4a48"></stop><stop offset="1" stop-color="#7f7e7e"></stop></linearGradient></defs><path fill="url(#h)" d="M258 304v1c-1 1-3 2-4 2 1 2 1 2 1 4 1 0 2-1 2-1h1 0c-3 2-5 4-8 6s-9 10-13 10c-1-1-1-1-1-2-1-1-1-3 0-4 1-2 3-3 4-5 6-5 11-8 18-11z"></path><path d="M248 282h0c4-1 8 0 12 0 11 0 23 1 34 0h6l2-2h0c-1 0-1-1-2 0h-4 0c5-1 9-1 14-1 11 0 22 1 33-1v1h-1l4 1v1h-3l1 1h0-96z" class="J"></path><path d="M380 292c-5 0-9 1-14 2-4 1-9 1-13 2-14 3-28 7-41 13-19 9-36 22-47 41-2 2-4 6-5 9 0 1 0 1-1 2 0-2 1-4 2-6 4-8 8-15 14-21 17-20 43-31 69-37 12-3 23-6 36-7v1 1z" class="N"></path><path d="M307 294h0c4 1 9 0 13-1h4c-1 1 0 0-2 0-1 0-1 1-3 1-1 0-2 0-3 1l-1-1c-1 0-1 1-2 1s-2 0-3 1h-2 1c-7 2-13 3-20 4-11 2-21 6-31 10h-1s-1 1-2 1c0-2 0-2-1-4 1 0 3-1 4-2v-1c10-3 21-6 31-7l18-3z" class="H"></path><defs><linearGradient id="i" x1="506.617" y1="327.344" x2="493.69" y2="357.787" xlink:href="#B"><stop offset="0" stop-color="#7e7d7d"></stop><stop offset="1" stop-color="#afaead"></stop></linearGradient></defs><path fill="url(#i)" d="M509 370c0-14-2-32-12-42-4-5-13-11-20-11h0 0 3c11 0 21 8 26 18 1 1 1 4 3 5v1c1 2 1 3 3 5v20c1 2 1 4 0 6v-1l-1 5v-4 2h-1l-1-2v-2z"></path><path d="M509 370l1-4c0 1 0 1 1 2v4 2h-1l-1-2v-2z" class="I"></path><path d="M265 315h1 0c-2 1-4 1-5 2v1h2c2 1 2 2 3 4l-2 1c0 1-1 2-2 3-4 4-7 7-12 10-1 1-2 1-2 1-3-2-4-4-5-7l14-12h1c2-2 4-3 7-3z" class="C"></path><path d="M490 292c5 1 9 2 13 4 2 2 4 3 6 4h0c1 1 1 1 1 2v11l1 10-1-2-1-3c-2-1-4-1-5-3s-2-3-4-4c-1-2-3-3-4-5h-1 1c1-1 2-1 3-1 2 1 3 2 5 2-2-2-5-4-7-5l3 1v-1c-2-1-3-1-4-3h0-1c0-2 0-3-1-5 0-1-1-1-2-1h-1c-1 0-1 0-2-1h-1 2z" class="D"></path><path d="M505 311c1 2 3 5 4 7-2-1-4-1-5-3l1-4z" class="O"></path><path d="M496 306c3 1 6 2 9 5l-1 4c-1-2-2-3-4-4-1-2-3-3-4-5z" class="E"></path><path d="M490 292c5 1 9 2 13 4 2 2 4 3 6 4h0c1 1 1 1 1 2v11l-1-4c-2-5-9-8-13-10h-1c0-2 0-3-1-5 0-1-1-1-2-1h-1c-1 0-1 0-2-1h-1 2z" class="K"></path><defs><linearGradient id="j" x1="488.455" y1="379.806" x2="510.553" y2="379.298" xlink:href="#B"><stop offset="0" stop-color="#a3a3a3"></stop><stop offset="1" stop-color="#cbc9ca"></stop></linearGradient></defs><path fill="url(#j)" d="M490 359l3 2c1-1 0-2 0-3-2-6-5-11-8-16 3 3 6 7 8 11l10 17c1 2 4 9 6 10 1-2 1-6 0-8l1 2h1v-2 4l1 19v12l1 5v3c-1 0-1 0 0 1 0 1 0 3-1 5v-8c-1-7-3-14-5-20-1-4-3-7-4-10-3-8-6-15-12-20-1-1-1-2-2-2-1-1 0-1-1-1l2-1z"></path><path d="M380 290c4-1 8-1 13-1l-1 2c4 1 10-1 15 0l-1 1h-4-12l-4 1v1c-1 1-6 1-7 1h-1c-6 2-11 3-17 5-1 1-4 2-4 2-2-1-2-1-4-1s-3 0-4 1c-1 0-2-1-3-1-3 1-7 2-11 3s-9 3-13 5c-20 8-35 21-46 39l-10 18c0-2 3-8 4-10 6-12 14-24 24-32 12-11 28-18 43-22 5-2 10-4 15-5 10-2 20-2 29-5h-1v-1-1z" class="E"></path><path d="M346 301c4-1 10-3 15-4 4 0 8 0 13-1 1-1 3-1 4-1-6 2-11 3-17 5-1 1-4 2-4 2-2-1-2-1-4-1s-3 0-4 1c-1 0-2-1-3-1z" class="D"></path><defs><linearGradient id="k" x1="281.89" y1="331.903" x2="273.704" y2="321.756" xlink:href="#B"><stop offset="0" stop-color="#7a7979"></stop><stop offset="1" stop-color="#afadad"></stop></linearGradient></defs><path fill="url(#k)" d="M257 343c1-3 4-6 5-9 8-11 19-20 31-25 5-3 11-4 16-6 14-5 29-10 43-10-3 2-7 2-11 3l-14 3c-24 7-50 20-64 41-3 4-5 8-7 13h0l-1 1h-1c-1-2 3-8 3-11h0z"></path><defs><linearGradient id="l" x1="512.752" y1="413.374" x2="492.688" y2="442.924" xlink:href="#B"><stop offset="0" stop-color="#7e7b7d"></stop><stop offset="1" stop-color="#afb1af"></stop></linearGradient></defs><path fill="url(#l)" d="M505 401v-2c0-1-1-2-1-3 1-3 0-6 0-8h1v2c1 1 1 1 1 2v1h1c2 6 4 13 5 20v8 13c1 6 0 12 0 18l-3 3c0 1 0 2 1 3 0 2 0 8-1 10l-4-31c0-3-1-6-2-9-2-9-5-17-8-26l-5-12c2 2 3 5 5 7 6 12 13 26 14 40 1 1 1 2 1 3 1-1 0-7 0-8l-2-14c-1-5-3-11-3-17z"></path><path d="M505 401v-2c0-1-1-2-1-3 1-3 0-6 0-8h1v2c1 1 1 1 1 2v1h1c2 6 4 13 5 20v8 13c-1-1 0-4-1-5 0-3 0-7-1-9-1-7-3-13-5-19z" class="B"></path><defs><linearGradient id="m" x1="435.667" y1="277.239" x2="455.949" y2="316.946" xlink:href="#B"><stop offset="0" stop-color="#878587"></stop><stop offset="1" stop-color="#bab9b8"></stop></linearGradient></defs><path fill="url(#m)" d="M416 289h42c11 0 22 0 32 3h-2 1c1 1 1 1 2 1h1c1 0 2 0 2 1 1 2 1 3 1 5-11-5-22-7-35-6-16 2-29 11-43 21 5-6 12-11 19-15 3-3 7-5 11-7h0 0c-8 0-15 0-23 1s-15 2-23 4h-2 0-1l-1 1h-1c3-3 9-2 12-4h-2c-1-1-3-1-5-1l1-1h4l8-1h1c1 0 0 0 1-1v-1z"></path><defs><linearGradient id="n" x1="306.284" y1="276.372" x2="311.6" y2="296.682" xlink:href="#B"><stop offset="0" stop-color="#524f51"></stop><stop offset="1" stop-color="#969592"></stop></linearGradient></defs><path fill="url(#n)" d="M226 284l32 2 61-1 21 1c4 0 8-1 11 0v1c2 1 6 0 8 1h0-55c-15 0-32-1-47 2-4 1-8 3-12 4-2 2-5 3-7 4l-2-1c-4-2-9-9-10-13z"></path><path d="M226 284l32 2h0 8v1h-4c-2 1-4 1-5 1l-5 2h0c-3 0-5 2-7 3v1c-2 2-5 3-7 4l-2-1c-4-2-9-9-10-13z" class="J"></path><defs><linearGradient id="o" x1="280.151" y1="266.01" x2="279.355" y2="277.803" xlink:href="#B"><stop offset="0" stop-color="#868485"></stop><stop offset="1" stop-color="#bebdbd"></stop></linearGradient></defs><path fill="url(#o)" d="M388 278c-2-1-4-1-5-1l-10-2c-12-3-26-1-38-1l-81 1c-13 0-27 2-40-1-7-2-15-6-21-9-10-5-19-10-28-17l1-1 2 2c10 8 22 14 34 17 11 2 23 3 34 4l57 1h52c6 0 13-1 19 0 3 1 7 2 11 3 2 0 5 1 7 1l9 2-3 1z"></path><path d="M168 249c1 0 2-1 3-1 2 0 5 3 7 4l6 3h1c2 1 4 2 5 2s3 1 4 1 1 1 2 1c2 1 4 1 5 1 1 1 2 0 3 1h1c1 0 2 0 3 1h1c3 0 5 0 8 1 2 0 4-1 7 0s7 0 10 0c1 0 1 0 2 1 3 0 7-1 11-1 0 0 1 1 2 1h33 3c-1 1-4 0-6 0h-14c-3 0-6 0-9 1h18c7-1 15 0 22 0h47 0c2 1 3 1 5 1h1l-1 1c5 0 10 0 14 1l1 1h0 1l-2 2c-5 0-12-2-17 0h-52l-57-1c-11-1-23-2-34-4-12-3-24-9-34-17z" class="B"></path><path d="M224 265c-11 1-22-2-32-6-5-1-10-3-14-6h0l12 5c13 4 25 5 39 6-2 0-4 0-5 1h0z" class="I"></path><path d="M229 264l21 1h6 18c-1 1-4 1-5 1h-11l-34-1h0c1-1 3-1 5-1z" class="K"></path><path d="M274 265c7-1 15 0 22 0h47 0c2 1 3 1 5 1h1l-1 1h-60c-10 0-20 0-30-1h11c1 0 4 0 5-1z" class="J"></path><defs><linearGradient id="p" x1="405.912" y1="388.759" x2="487.153" y2="201.76" xlink:href="#B"><stop offset="0" stop-color="#c9c8c8"></stop><stop offset="1" stop-color="#ecebeb"></stop></linearGradient></defs><path fill="url(#p)" d="M512 223v-1-8c3 3 0 9 2 12l1 21 1 2c0 2 1 3 3 5 7 10 19 17 32 19l16 3h0c2 1 3 2 5 2h-3l3 1 7 1h16l25-1 2 1c-2 1-4 1-6 1h0l34 1h11 0 1c3 1 7 0 10 1h1 0c-2 1-2 1-3 2h-4c-1 1-3 1-4 1h7c-3 1-5 1-7 1-3 1-12 0-13 1h-15-11-10c1 0 3 1 3 1-3 0-12 0-14 1h-7-13-23c-10 1-20 2-31 5-3 2-8 3-11 6l-1 2c0 2 0 6 1 9l-1 3c-1 5-1 15 0 20 1 1 1 2 1 4v1c-1 2-1 15 0 18l1 2c-1 7-1 15 0 22 2-4 4-10 6-14 7-16 14-30 28-41-3 4-6 7-9 11-5 8-10 16-13 25 3-3 6-6 10-8 4-3 9-5 13-7-5 4-10 7-15 11-11 10-18 29-20 43l-1 20c-1 1-1 3-1 4 0 4-2 8-1 11v1c0 1 0 2-1 3h0c-1 5 0 10-2 14 0 3 0 5-1 7h0c0-1-1-3 0-5 0-1 0-2 1-3v-2c0-6 1-12 0-18v-13c1-2 1-4 1-5-1-1-1-1 0-1v-3l-1-5v-12l-1-19 1-5v1c1-2 1-4 0-6v-20-17c0-2 0-5-1-6l-1-10v-11c0-1 0-1-1-2h0c-2-1-4-2-6-4 1-2 1-5 1-8H391h-8l-20-1-4 1c-2-1-6 0-8-1v-1c-3-1-7 0-11 0l-21-1-61 1-32-2v-1l22-1h96 27 17 6l-1-1c-1-1-2-1-4-1l1-1-2-1 3-1c9 2 17 2 25 2 3 0 8-1 11 0 1 1 2 1 3 2 1 0 1 0 2 1 1 0 4-1 6-1 4 0 9 1 13 0 8 0 14-2 22-4 10-3 20-6 28-14 3-3 7-8 9-14 1-3 1-6 1-9l1-17z"></path><path d="M515 251l3 6c0 2 1 5-1 7l-1-2h0l-1 1c-1 0 0-7 0-8v-4z" class="K"></path><path d="M360 286c3 0 6-1 8 1h10c2 0 3-1 5 0 2 0 5 0 8 1h-8l-20-1-3-1z" class="L"></path><path d="M319 285h24c6 0 12 0 17 1l3 1-4 1c-2-1-6 0-8-1v-1c-3-1-7 0-11 0l-21-1z" class="I"></path><path d="M511 376l1-5v1l1 27c0 4 1 9 0 13l-1-5v-12l-1-19z" class="E"></path><path d="M504 288l4 1c2 2 1 8 1 11h0c-2-1-4-2-6-4 1-2 1-5 1-8z" class="B"></path><path d="M565 288h34 14c1 0 3 1 3 1-3 0-12 0-14 1h-7-13-23c4-1 9-1 13-1l2-1h-9z" class="T"></path><path d="M565 288h34l1 1h2-2-28l2-1h-9z" class="C"></path><path d="M595 280l25-1 2 1c-2 1-4 1-6 1h0-50l-1-2h7l7 1h16z" class="T"></path><path d="M595 280l25-1 2 1c-2 1-4 1-6 1h-24l3-1z" class="G"></path><path d="M391 277c9 2 17 2 25 2 3 0 8-1 11 0 1 1 2 1 3 2 1 0 1 0 2 1h-26-12l-1-1c-1-1-2-1-4-1l1-1-2-1 3-1z" class="E"></path><path d="M390 279c4 0 7 1 11 1 3 0 8-1 12 0 0 0 0 1 1 1-2 1-5 0-8 1h-12l-1-1c-1-1-2-1-4-1l1-1z" class="C"></path><path d="M515 263l1-1h0l1 2c5 8 14 12 22 14 3 1 8 1 11 2h-1c-10 2-22 2-32-1-1-5-1-10-2-16zm-5-3v19c-13 3-28 2-41 1h0 0l17-2c12-3 18-8 24-18zm6 43l1-14 48-1h9l-2 1c-4 0-9 0-13 1-10 1-20 2-31 5-3 2-8 3-11 6l-1 2z" class="M"></path><path d="M516 249c0 2 1 3 3 5 7 10 19 17 32 19l16 3h0c2 1 3 2 5 2h-3l3 1h-7l1 2-9-1c-2 1-5 1-7 1v-1c-3-1-8-1-11-2-8-2-17-6-22-14 2-2 1-5 1-7l-3-6 1-2z" class="O"></path><path d="M520 260h1c2 2 4 5 6 7 8 5 17 7 26 9 4 1 8 3 12 3l1 2-9-1c-14-4-28-7-37-20z" class="B"></path><path d="M518 257c0 1 2 3 2 3 9 13 23 16 37 20-2 1-5 1-7 1v-1c-3-1-8-1-11-2-8-2-17-6-22-14 2-2 1-5 1-7z" class="F"></path><path fill="#fff" d="M563 374c2-6 7-11 7-17l1-1c0-3 0-6-1-10h0 0l2 3c3 6 0 11-3 17 2-3 4-6 5-8 4-5 9-11 13-15 8-7 19-9 29-12 6-1 13-2 20-2 1 1 1 2 2 2 8 1 15 4 22 7 2 0 3 0 5 1l7 2-1 1h-1l15 12c13 11 21 26 28 41 1 3 3 7 4 10h0l3 13c0 1 1 4 1 5h2c1 7 3 14 2 20 1 12 0 21-4 32l-5 13s-1 2-1 3v1l2-2h0c-1 3-2 6-3 10-1 3-2 6-4 9-3 1-4 4-7 6l-4 3v-3c-4 2-7 5-11 7s-9 3-13 5l-2 2c-1 0-8 1-8 2l-1 1c-3 1-10 0-13 2-1 0-4 0-5-1-5-1-10-3-15-5-3-1-5 0-7 0h0l-6-4c-2-1-3-1-5-1-3-2-10-5-14-4 3 1 5 1 8 3 5 2 9 5 13 8 3 1 5 4 7 6l-1 1c-3-3-6-5-10-6-4-3-9-4-13-6-1 1-2 1-3 1-10-1-18 0-27 4-4 2-8 4-11 7-6 5-10 12-15 18-2 3-6 6-8 10-1 2-2 5-3 8l-2 6c-3 7-5 16-10 22v-1h-1l1-2c1-5 2-9 4-14l-1-1-1 2c-1-1-1-2-1-4l-1-3 1-9c1-1 1-4 2-6l3-13-3 3-1-2c-2 5-5 11-7 16-1 2-1 5-2 8l-1-1h0v-2c-3-5-1-12 0-18 1-2 1-5 2-8 1-2 0-4 0-6-3-5 0-14 0-18-1-2 0-9 0-12-1-3 0-6 0-9 0-10 2-19 4-28l2 2 14-34s-1-1-1-2 1-2 2-3c1-3 2-6 2-9l3-4v-3c1-1 1-2 2-2 0-1 0-2 1-2 1-3 3-6 4-8l11-16c1-4 0-9 1-13 0-1 1-3 2-4l-2-1z"></path><path d="M593 433v-1c-1-3-2-4-4-6l-2-2h3c2 1 3 2 4 3 1 3 1 6 0 8v-1-1l-1 2v-2z" class="M"></path><path d="M636 352h0 4c1 1 2 1 3 3l-1 1v2l-1 1c-1-1-2-1-3-2s-2-3-2-5z" class="Q"></path><path d="M603 370c8-1 16-2 24-2-2 3-6 1-9 3h-4-4-2c-3 0-3 1-5-1z" class="B"></path><path d="M627 368c3 0 8 0 10 2 1 1 1 1 1 2h0c-4-1-6-1-9 0-1-2-1-2-3-2l-1 1h-1-3c-2 1-2 1-3 1h-3c-1 1-3 1-4 1l-1-2h4 4c3-2 7 0 9-3z" class="P"></path><path d="M574 405c10 0 19 1 25 8 2 2 2 3 3 5-1 0-1-1-2-1v-2h-1c-4-6-15-7-22-7l-3-3z" class="B"></path><path d="M609 457c1 1 3 1 3 3v3 5c1 5 1 11 3 15v1h-1l-2-2v-3c0-3 0-7-2-10-1-4-1-8-1-12z" class="P"></path><path d="M606 449c7 5 7 13 16 15h0c-1 1-3 2-4 1-2 0-4-1-6-2v-3c0-2-2-2-3-3-1-2-2-4-4-5v-2l1-1zm65-48l1-1v-8c2 9 2 17-3 24-2 5-6 8-11 9 7-5 12-10 12-19l1-5z" class="D"></path><path d="M706 466v2l-4 5c-3 4-7 7-11 9h-6c1-2 6-5 8-5 5-3 9-7 13-11z" class="B"></path><path d="M674 451l1-1h2l1-1c2-1 3-2 5-4 0-1 1-2 2-2 2-2 3-5 6-6-3 6-9 11-9 19h-1c-1-1 0-3 0-5-2 2-4 3-7 4-1-1 0-2 0-4z" class="P"></path><path d="M563 379v9c2-2 4-4 7-6 7-6 14-11 21-18l-3 6-2 1c-5 0-10 9-14 13-3 3-7 5-10 8 1-4 0-9 1-13z" class="C"></path><path d="M715 492l2-2h0c-1 3-2 6-3 10-1 3-2 6-4 9-3 1-4 4-7 6l-4 3v-3c8-6 13-14 16-23z" class="J"></path><path d="M699 454h0c-4 9-11 17-19 22v-1c-1-1-2-2-3-2-3 0-6 2-8 4v1h-1l-1-2 3-2c1-1 5-3 8-3 1 0 2 1 3 1 8-4 13-11 18-18z" class="B"></path><path d="M658 459c-5 0-10 0-16-2-10-4-19-10-24-19l5 5c6 6 14 6 22 6-2 1-4 1-6 1 1 1 5 0 6 1h-7l-6-1c3 2 7 2 10 3v1h-2 0l5 2c1 1 2 1 3 1h0l3 1c3 0 6-1 7 1z" class="M"></path><path d="M621 521c-4-4-9-10-11-16 5 6 10 12 16 16l6 3-1 2-3-1 3 3c-3-1-5 0-7 0h0l-6-4-1-2c2 0 3 0 4-1z" class="C"></path><path d="M621 521l7 4 3 3c-3-1-5 0-7 0h0l-6-4-1-2c2 0 3 0 4-1z" class="K"></path><defs><linearGradient id="q" x1="606.25" y1="372.272" x2="632.325" y2="381.237" xlink:href="#B"><stop offset="0" stop-color="#0d0b06"></stop><stop offset="1" stop-color="#333132"></stop></linearGradient></defs><path fill="url(#q)" d="M610 371l1 2c1 0 3 0 4-1h3c1 0 1 0 3-1h3 1l1-1c2 0 2 0 3 2l-10 4c5 4 12 9 14 14l-1 2c-2-3-5-5-7-8-2-1-3-3-5-4-3-2-8-4-12-6l-2-1 2-2h2z"></path><path d="M632 524c15 5 28 6 43 3l-2 2c-1 0-8 1-8 2l-1 1c-3 1-10 0-13 2-1 0-4 0-5-1-5-1-10-3-15-5l-3-3 3 1 1-2z" class="B"></path><path d="M628 525l3 1c11 4 22 6 34 5l-1 1c-3 1-10 0-13 2-1 0-4 0-5-1-5-1-10-3-15-5l-3-3z" class="O"></path><path d="M570 519l7-4-11-5c1-1 3 0 4 0l10 5c-5-5-10-8-10-15v-9c2 9 6 17 14 21 5 3 12 3 17 4s9 3 14 5h-3c-12-5-26-7-39-2h-3z" class="G"></path><defs><linearGradient id="r" x1="665.676" y1="370.179" x2="655.637" y2="377.822" xlink:href="#B"><stop offset="0" stop-color="#2e2a29"></stop><stop offset="1" stop-color="#4e4e51"></stop></linearGradient></defs><path fill="url(#r)" d="M644 349h4l4 4v-2c8 9 14 20 17 31 1 3 3 7 3 10v8l-1 1c-4-20-12-38-27-52z"></path><path d="M573 519c13-5 27-3 39 2h3c1 0 1 0 2 1l1 2c-2-1-3-1-5-1-3-2-10-5-14-4 3 1 5 1 8 3 5 2 9 5 13 8-2 0-5-2-7-3-3-2-6-4-10-5-9-3-21-3-30 1-4 1-7 3-10 6 0-1 0-1-1-2l2-1-1-3c2-2 4-3 7-4h3z" class="I"></path><path d="M563 523c2-2 4-3 7-4h3l-9 7-1-3zm91-185h-1c-2-2-6-3-9-4 11 2 21 7 29 15 7 6 9 12 12 20h1l1 1 3 11-1 1v2c-1 3-4 7-7 9h-1l2-3c3-7 0-18-2-25-3-7-8-15-14-20-4-3-9-5-13-7z" class="C"></path><path d="M685 369h1l1 1 3 11-1 1v-1 1c-1 2-1 4-3 5 1-6 0-12-1-18z" class="L"></path><path d="M686 432c2 0 2-1 3 0l-1 3h-1c0 1-1 1-2 2h0c-1 3-7 9-9 10l-3 1v1h1c0 1 0 1-1 2h0c1 0 0 1 1 0 0 2-1 3 0 4-5 2-11 4-16 4-1-2-4-1-7-1l-3-1h0c-1 0-2 0-3-1l-5-2h0 2v-1c2 1 5 1 7 1 12 0 24-8 32-16l5-6z" class="C"></path><path d="M651 458l1-2h3 4v-1h2l3-1s1 0 2-1l4-2h3 0c1 0 0 1 1 0 0 2-1 3 0 4-5 2-11 4-16 4-1-2-4-1-7-1z" class="J"></path><defs><linearGradient id="s" x1="585.108" y1="517.65" x2="592.249" y2="533.04" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2c2d2d"></stop></linearGradient></defs><path fill="url(#s)" d="M573 523c9-4 21-4 30-1 4 1 7 3 10 5 2 1 5 3 7 3 3 1 5 4 7 6l-1 1c-3-3-6-5-10-6-4-3-9-4-13-6-1 1-2 1-3 1-10-1-18 0-27 4 0-1-1-1-1-2l-1 1c-2 1-2 1-5 1 2-1 4-2 5-3l-1-1v1l-1-1c1-1 2-1 3-2l1-1z"></path><defs><linearGradient id="t" x1="581.547" y1="523.156" x2="584.655" y2="529.259" xlink:href="#B"><stop offset="0" stop-color="#9b9a9b"></stop><stop offset="1" stop-color="#ccc"></stop></linearGradient></defs><path fill="url(#t)" d="M571 527c11-4 20-5 32-2-1 1-2 1-3 1-10-1-18 0-27 4 0-1-1-1-1-2l-1 1c-2 1-2 1-5 1 2-1 4-2 5-3z"></path><defs><linearGradient id="u" x1="683.731" y1="400.085" x2="702.005" y2="427.265" xlink:href="#B"><stop offset="0" stop-color="#010205"></stop><stop offset="1" stop-color="#33312f"></stop></linearGradient></defs><path fill="url(#u)" d="M695 385l1-1v1c1 2 2 3 3 4v1l-2-2v1c0 1 0 2 1 2h0c1 5 2 10 2 15 0 10-4 21-9 30v1c-3 1-4 4-6 6-1 0-2 1-2 2-2 2-3 3-5 4l-1 1h-2l-1 1c-1 1 0 0-1 0h0c1-1 1-1 1-2h-1v-1l3-1c2-1 8-7 9-10h0c1-1 2-1 2-2h1l1-3c-1-1-1 0-3 0 8-14 12-31 9-47z"></path><path d="M610 336c6-3 14-3 21-2 7 2 12 3 19 6 4 2 10 5 12 9h-1c-4-3-10-9-16-9 4 2 7 3 9 6l-7-3v1h1c3 2 6 5 8 9-2-2-4-4-6-5 0 1 1 2 2 3v2l-4-4h-4l-4-4c-7-4-14-5-23-5-5-1-9-1-14 1-4 1-7 3-11 5l-1-1c6-5 12-7 19-9z" class="D"></path><path d="M626 337c1-2 3-1 5-1s6 1 7 2c1 0 1 0 1 1v1l-1-1c-2-1-4 0-6-1-2 0-4-1-6-1z" class="S"></path><path d="M610 336c6-3 14-3 21-2v2c-2 0-4-1-5 1-5-1-12 1-16-1z" class="T"></path><path d="M640 345v-2c-1 0-2-1-4-2l1-1c1 1 2 1 3 2v-1h0c2 1 3 2 4 3s5 3 6 4c0 1 1 2 2 3v2l-4-4h-4l-4-4z" class="C"></path><path d="M687 370c3 2 7 15 7 19 1 15-3 34-13 46l-2 1 1 1 1-1v2c-8 8-20 16-32 16-2 0-5 0-7-1-3-1-7-1-10-3l6 1h7c-1-1-5 0-6-1 2 0 4 0 6-1s4-1 7-1c14-4 25-12 33-25 7-12 9-29 5-42l-3-11z" class="D"></path><path d="M638 451h2c15 2 28-6 39-15l1 1 1-1v2c-8 8-20 16-32 16-2 0-5 0-7-1-3-1-7-1-10-3l6 1z" class="I"></path><path d="M563 374c2-6 7-11 7-17l1-1c0-3 0-6-1-10h0 0l2 3c3 6 0 11-3 17 2-3 4-6 5-8 4-5 9-11 13-15 8-7 19-9 29-12 6-1 13-2 20-2 1 1 1 2 2 2 8 1 15 4 22 7 2 0 3 0 5 1h-1-2c2 1 3 2 5 3 2 2 4 4 7 6l-1 1c-8-8-18-13-29-15 3 1 7 2 9 4h1 0c-2 0-4-1-5-1-3-1-5-2-7-2h-2l8 3h0c1 1 2 1 2 2-7-3-12-4-19-6-7-1-15-1-21 2-7 2-13 4-19 9l1 1h2c3-2 6-2 9-1h-1 0c-10 1-20 9-26 15l-4 6c3-1 6-2 10-2l-9 3c-1 0-2 1-3 1-2 2-4 5-5 7l-2-1z" class="P"></path><path d="M591 345c-4 2-7 6-11 9-1 1-3 5-5 5 2-3 5-7 8-9 13-13 29-18 46-19h9c8 1 15 4 22 7 2 0 3 0 5 1h-1-2c2 1 3 2 5 3 2 2 4 4 7 6l-1 1c-8-8-18-13-29-15 3 1 7 2 9 4h1 0c-2 0-4-1-5-1-3-1-5-2-7-2h-2l8 3h0c1 1 2 1 2 2-7-3-12-4-19-6-7-1-15-1-21 2-7 2-13 4-19 9z" class="I"></path><path d="M674 348c3 3 7 6 10 10 4 4 7 10 10 14 3 5 6 9 9 14 0 2 2 6 3 6l5 19c1 1 1 2 1 4-1 2-1 4-1 6-2 4-5 8-8 11 3-7 4-15 3-23-1-5-4-16-7-18h-1 0c-1 0-1-1-1-2v-1l2 2v-1c-1-1-2-2-3-4v-1l-1 1c3 16-1 33-9 47l-5 6v-2l-1 1-1-1 2-1c10-12 14-31 13-46 0-4-4-17-7-19l-1-1h-1c-3-8-5-14-12-20l1-1z" class="K"></path><path d="M695 385c-2-4-2-8-4-11 0-2-1-4-2-6 3 4 6 8 8 12 4 7 8 14 11 23 0 2 2 6 1 8v10l2-9v-1c1 1 1 2 1 4-1 2-1 4-1 6-2 4-5 8-8 11 3-7 4-15 3-23-1-5-4-16-7-18h-1 0c-1 0-1-1-1-2v-1l2 2v-1c-1-1-2-2-3-4v-1l-1 1z" class="C"></path><path d="M593 433v2l1-2v1 1h1 1c2 0 4 0 6 2 2 1 2 3 3 5 0 1 0 3-1 4h-1v-1c0-2 0-4-2-6-1-1-2-1-4-1-6 1-14 7-17 12-5 7-7 17-5 26 1 4 3 8 5 12 1 2 3 4 5 6-5-11-9-21-5-33 1-5 5-8 9-10 5-2 9-3 13-4l4 2-1 1v2c-4-2-8-2-12 0-5 1-9 4-11 9-4 8-3 17 1 25 3 7 7 12 12 17 2 3 4 7 6 10 0-4-2-6-4-10l-8-15h1l15 25c-9-1-18-1-24-9-2-2-3-4-3-6s0-2 2-4c1 0 2 1 4 1h0c-3-3-6-6-8-10-6-12-5-29 5-40 2-2 3-3 6-5 1-1 4-2 5-4 0 0 0-2 1-3z" class="B"></path><path d="M584 495c5 4 11 10 15 16-7 0-12-1-16-6-2-2-4-5-4-8 1-1 0-1 2-1l1 1 1 2c0-1-1-2 0-2 0-1 1 0 1-2h0z" class="K"></path><path d="M586 371l2-1s-1 2-1 3c5-1 10-3 16-3 2 2 2 1 5 1l-2 2 2 1c4 2 9 4 12 6 2 1 3 3 5 4 2 3 5 5 7 8l1-2c4 6 7 12 8 19-2-2-2-5-4-7-6-9-16-15-27-16-12-2-22 2-32 9l-14 12-10 9c-2 2-5 4-6 7h2c3-1 4-4 7-5l-1 1-7 6c-5 5-8 11-11 16 0 0-1-1-1-2s1-2 2-3c1-3 2-6 2-9l3-4v-3c1-1 1-2 2-2 0-1 0-2 1-2 1-3 3-6 4-8l11-16c3-3 7-5 10-8 4-4 9-13 14-13z" class="G"></path><path d="M586 371l2-1s-1 2-1 3c5-1 10-3 16-3 2 2 2 1 5 1l-2 2 2 1c4 2 9 4 12 6 2 1 3 3 5 4 2 3 5 5 7 8l5 8c-1-1-2-3-3-4-3-4-6-8-11-10-1-1-2-1-3-2-11-4-20-7-31-4l-1-1h-6l-6 4 10-12z" class="H"></path><path d="M588 379c9-4 16-3 25 1 1 0 3 1 5 2l1 1h1v1c-11-4-20-7-31-4l-1-1z" class="I"></path><defs><linearGradient id="v" x1="598.781" y1="367.632" x2="582.948" y2="380.413" xlink:href="#B"><stop offset="0" stop-color="#11100f"></stop><stop offset="1" stop-color="#333030"></stop></linearGradient></defs><path fill="url(#v)" d="M586 371l2-1s-1 2-1 3c5-1 10-3 16-3 2 2 2 1 5 1l-2 2 2 1c-8-1-17-1-24 3-1 1-1 2-2 2l-6 4 10-12z"></path><path d="M562 392c3-3 7-5 10-8 4-4 9-13 14-13l-10 12 6-4h6l1 1c-4 1-8 2-11 5h2l5-2v1c-1 1-3 2-5 3-3 2-5 4-8 7l-4 4c-1 2-3 3-4 4v1c-1 1-1 2-2 3-2 2-4 3-5 5l-1 1c-1 1-2 2-2 4-2 2-5 4-6 7h2c3-1 4-4 7-5l-1 1-7 6c-5 5-8 11-11 16 0 0-1-1-1-2s1-2 2-3c1-3 2-6 2-9l3-4v-3c1-1 1-2 2-2 0-1 0-2 1-2 1-3 3-6 4-8l11-16z" class="C"></path><path d="M544 423c0-1 1-1 1-2 2-3 4-5 5-8 2-2 3-4 5-5-4 6-7 12-10 19 1-1 2-3 3-4h2c3-1 4-4 7-5l-1 1-7 6c-5 5-8 11-11 16 0 0-1-1-1-2s1-2 2-3c1-3 2-6 2-9l3-4z" class="K"></path><path d="M562 392c3-3 7-5 10-8 4-4 9-13 14-13l-10 12 6-4h6l1 1c-4 1-8 2-11 5-7 3-14 10-18 16-1 2-3 6-5 7s-3 3-5 5c-1 3-3 5-5 8 0 1-1 1-1 2v-3c1-1 1-2 2-2 0-1 0-2 1-2 1-3 3-6 4-8l11-16z" class="F"></path><path d="M674 348c-3-2-5-4-7-6-2-1-3-2-5-3h2 1l7 2-1 1h-1l15 12c13 11 21 26 28 41 1 3 3 7 4 10h0l3 13c0 1 1 4 1 5h2c1 7 3 14 2 20 1 12 0 21-4 32l-5 13s-1 2-1 3c-7 12-16 21-29 25-6 2-12 3-17 3-6 1-11 0-17 1-2 0-5 2-8 2l7-7c-7-1-12-3-18-6-2-2-4-2-6-2h0v-1c2-1 3-2 3-4 0-1-1-2-1-3l-4-9h0c-1-3-1-5 0-8v-3l1-1v3c2 6 9 11 14 14 12 6 28 7 40 3s21-13 26-24c-7 7-13 14-23 17l21-17-2-1 4-5v-2c1-2 3-4 4-7 1-2 1-3 1-4-3 9-11 15-18 21 2-6 7-11 10-16 6-9 8-19 9-30 0-5 1-10 0-15 0-2 0-3-1-4l-5-19c-1 0-3-4-3-6-3-5-6-9-9-14-3-4-6-10-10-14-3-4-7-7-10-10z" class="M"></path><path d="M711 455v-1l4-12h0c1 1 0 2 0 3l-2 7v3c1 3-2 9-4 12-1 2-3 5-5 7l-2-1 4-5v-2c1-2 3-4 4-7 1-2 1-3 1-4z" class="T"></path><defs><linearGradient id="w" x1="680.367" y1="508.564" x2="677.239" y2="501.543" xlink:href="#B"><stop offset="0" stop-color="#aba9aa"></stop><stop offset="1" stop-color="#c9c7c8"></stop></linearGradient></defs><path fill="url(#w)" d="M698 494l1 1c-3 4-6 7-9 10-1 1-4 2-6 3v1h-1c-3 0-7 1-9 1-9 1-16 0-24-2l1-1c3 0 6 1 9 1 15 1 27-4 38-14z"></path><path d="M625 482c2 8 6 15 13 19 4 3 9 4 13 6l-1 1c8 2 15 3 24 2 2 0 6-1 9-1l-2 1c0 1 0 1 1 2-7 2-15 4-22 2h-1c-7-2-14-4-21-9-1 1-3 0-4 0-3 0-4 1-7 1 2-1 3-2 3-4 0-1-1-2-1-3l-4-9h0c-1-3-1-5 0-8z" class="L"></path><path d="M625 490c1 0 1 1 2 2 2 5 6 10 11 13-1 1-3 0-4 0-3 0-4 1-7 1 2-1 3-2 3-4 0-1-1-2-1-3l-4-9z" class="G"></path><path d="M681 510c-6 2-13 3-19 2-8-1-18-3-24-9 4 1 8 3 12 5 8 2 15 3 24 2 2 0 6-1 9-1l-2 1z" class="M"></path><path d="M674 348c-3-2-5-4-7-6-2-1-3-2-5-3h2 1l7 2-1 1h-1l15 12c13 11 21 26 28 41 1 3 3 7 4 10h0l3 13-1 1h0v7c-1-4-1-9-2-12-2-8-4-16-8-22v-1l-2-2v-1l-3-5-4-7c0-1-1-1-1-2l-1-2h-1-1-1c0 2 5 8 7 10 1 3 3 6 4 9v1c-1 0-3-4-3-6-3-5-6-9-9-14-3-4-6-10-10-14-3-4-7-7-10-10z" class="L"></path><path d="M674 348c-3-2-5-4-7-6-2-1-3-2-5-3h2c5 2 8 6 13 10 4 4 9 8 13 12 4 3 7 8 9 12 3 5 7 10 9 16 1 1 1 2 1 3v-1l-2-2v-1l-3-5-4-7c0-1-1-1-1-2l-1-2h-1-1-1c0 2 5 8 7 10 1 3 3 6 4 9v1c-1 0-3-4-3-6-3-5-6-9-9-14-3-4-6-10-10-14-3-4-7-7-10-10z" class="D"></path><defs><linearGradient id="x" x1="719.983" y1="470.13" x2="695.381" y2="454.197" xlink:href="#B"><stop offset="0" stop-color="#939191"></stop><stop offset="1" stop-color="#cfcece"></stop></linearGradient></defs><path fill="url(#x)" d="M719 426v-7h0l1-1c0 1 1 4 1 5h2c1 7 3 14 2 20l-1 1h0l-1 1c-1 21-9 42-25 57-5 4-10 8-16 10-1-1-1-1-1-2l2-1h1v-1c2-1 5-2 6-3 3-3 6-6 9-10l-1-1 3-3c9-12 18-29 19-44 1-7 0-15-1-21z"></path><path d="M721 423h2c1 7 3 14 2 20l-1 1h0l-1 1c0-8 0-15-2-22z" class="G"></path><path d="M723 445l1-1h0l1-1c1 12 0 21-4 32l-5 13s-1 2-1 3c-7 12-16 21-29 25-6 2-12 3-17 3-6 1-11 0-17 1-2 0-5 2-8 2l7-7c-7-1-12-3-18-6-2-2-4-2-6-2h0v-1c3 0 4-1 7-1 1 0 3 1 4 0 7 5 14 7 21 9h1c7 2 15 0 22-2 6-2 11-6 16-10 16-15 24-36 25-57z" class="R"></path><path d="M565 408l9-3 3 3c7 0 18 1 22 7h1v2c-11-5-21-4-31 0-2 0-4 1-6 3v103l1 3-2 1c1 1 1 1 1 2 3-3 6-5 10-6l-1 1c-1 1-2 1-3 2l1 1v-1l1 1c-1 1-3 2-5 3 3 0 3 0 5-1l1-1c0 1 1 1 1 2-4 2-8 4-11 7-6 5-10 12-15 18-2 3-6 6-8 10-1 2-2 5-3 8l-2 6c-3 7-5 16-10 22v-1h-1l1-2c1-5 2-9 4-14l-1-1-1 2c-1-1-1-2-1-4l-1-3 1-9c1-1 1-4 2-6l3-13-3 3-1-2c-2 5-5 11-7 16-1 2-1 5-2 8l-1-1h0v-2c-3-5-1-12 0-18 1-2 1-5 2-8 1-2 0-4 0-6-3-5 0-14 0-18-1-2 0-9 0-12-1-3 0-6 0-9 0-10 2-19 4-28l2 2 14-34c3-5 6-11 11-16l7-6 1-1c-3 1-4 4-7 5h-2c1-3 4-5 6-7l10-9 1 1z" class="U"></path><path d="M561 416c-1 2-2 2-3 4h2 0c-2 1-4 2-5 4l-5 5-1-1 4-4h1v-1l-4 3-1-1 7-6 5-3z" class="B"></path><path d="M526 551c0-2 2-4 3-6 3-5 7-10 11-15-4 7-8 13-10 20l-3 3-1-2z" class="H"></path><defs><linearGradient id="y" x1="520.685" y1="549.13" x2="514.202" y2="558.794" xlink:href="#B"><stop offset="0" stop-color="#858383"></stop><stop offset="1" stop-color="#9f9e9d"></stop></linearGradient></defs><path fill="url(#y)" d="M530 522l5-6h0c0 2-1 3-2 5l-6 9c-6 13-10 29-11 44v-2c-3-5-1-12 0-18 1-2 1-5 2-8 1-2 0-4 0-6s0-5 2-7l1-1h1c1-3 3-8 6-9 0-1 1-1 2-1z"></path><path d="M521 532h1c1-3 3-8 6-9 0-1 1-1 2-1-1 2-3 5-4 7-2 4-4 9-5 13l-2 4h0-1c1-2 0-4 0-6s0-5 2-7l1-1z" class="J"></path><path d="M518 540c0-2 0-5 2-7 0 1 0 3 1 4 0 2-1 3 0 5l-2 4h0-1c1-2 0-4 0-6z" class="S"></path><defs><linearGradient id="z" x1="571.321" y1="404.945" x2="585.003" y2="415.288" xlink:href="#B"><stop offset="0" stop-color="#918d8c"></stop><stop offset="1" stop-color="#bdbec0"></stop></linearGradient></defs><path fill="url(#z)" d="M565 408l9-3 3 3c7 0 18 1 22 7h1c-3-1-5-2-7-3-6-2-15-1-21 0-4 1-7 2-11 4h0l-5 3 1-1c-3 1-4 4-7 5h-2c1-3 4-5 6-7l10-9 1 1z"></path><path d="M565 408l9-3 3 3h-1c-5 0-9 1-14 3l3-3z" class="D"></path><path d="M527 583c4-11 9-21 12-32 2-4 3-9 4-14 2-5 3-10 4-15 2-11 2-23 3-34l2-27 1-1v4c-1 3-1 6-1 9v17c1 11 2 22 2 33 0 5 0 9-1 14l-1 2-1 1h-1l1-4c0-1 1-2 1-2v-2h0c-1 0-1 0-1 1l-1 1v2c0 1 0 1-1 2v-3-1-1h0c-1 1-1 2-1 3h-1v2l-2 5c0 1 0 1-1 2h0l-1-1h0c0 2-1 5-2 8l-1 2c0 2-2 5-2 6-2 8-7 16-10 24l-1-1z" class="I"></path><path d="M573 523l-1 1c-1 1-2 1-3 2l1 1v-1l1 1c-1 1-3 2-5 3 3 0 3 0 5-1l1-1c0 1 1 1 1 2-4 2-8 4-11 7-6 5-10 12-15 18-2 3-6 6-8 10-1 2-2 5-3 8l-2 6c-3 7-5 16-10 22v-1h-1l1-2c1-5 2-9 4-14 3-8 8-16 10-24 0-1 2-4 2-6l1-2c1-3 2-6 2-8h0l1 1h0c1-1 1-1 1-2l2-5v-2h1c0-1 0-2 1-3h0v1 1 3c1-1 1-1 1-2v-2l1-1c0-1 0-1 1-1h0v2s-1 1-1 2l-1 4h1l1-1 10-12c1 1 1 1 1 2 3-3 6-5 10-6z" class="F"></path><path d="M538 560l1 2c-2 4-4 8-5 12l-6 17c-1 2-2 5-4 7 1-5 2-9 4-14 3-8 8-16 10-24z" class="R"></path><defs><linearGradient id="AA" x1="569.778" y1="530.128" x2="556.188" y2="532.69" xlink:href="#B"><stop offset="0" stop-color="#0a0b0a"></stop><stop offset="1" stop-color="#3d3b3a"></stop></linearGradient></defs><path fill="url(#AA)" d="M573 523l-1 1c-1 1-2 1-3 2l1 1v-1l1 1c-1 1-3 2-5 3-6 5-11 11-15 17-1 2-4 6-5 7h-1-1c0-3 2-5 3-7s2-3 3-5l1-2 1-1 10-12c1 1 1 1 1 2 3-3 6-5 10-6z"></path><path d="M562 527c1 1 1 1 1 2-2 2-5 4-6 7-3 3-5 7-7 11-2 2-4 5-5 7h-1c0-3 2-5 3-7s2-3 3-5l1-2 1-1 10-12zm-13-102l1 1 4-3v1h-1l-4 4 1 1 5-5c0 2-3 4-4 6-3 3-5 6-7 9-7 11-13 24-17 36 1 1 2 1 3 1 5-7 9-16 15-23-1 5-5 10-8 14-10 17-18 33-18 54v-1c3-7 8-15 12-22l1-1c0 2-2 4-3 6-3 5-9 14-8 21 4-8 6-15 13-22 1-1 2-3 3-3l1-1c0 2-6 8-7 9-5 7-8 16-10 25l-1 1c-2 2-2 5-2 7-3-5 0-14 0-18-1-2 0-9 0-12-1-3 0-6 0-9 0-10 2-19 4-28l2 2 14-34c3-5 6-11 11-16z" class="L"></path><path d="M527 475c1 1 2 1 3 1-2 4-4 7-6 10 0-4 1-7 3-11z" class="P"></path><path d="M549 425l1 1 4-3v1h-1l-4 4 1 1c-11 14-18 29-25 46-2 4-2 9-3 13 0 1 0 3-1 4 0-5 1-11 3-17l14-34c3-5 6-11 11-16z" class="R"></path><path d="M515 438l2 2c0 1 1 4 0 5v5 2c0 2-1 7 0 9l1 1c1-2 1-6 2-8h0c2-4 3-9 5-12h1l-3 9c-2 4-3 8-4 12-2 6-2 13-2 20v28l1-1c0 3-1 10 0 12 0 4-3 13 0 18 0 2 1 4 0 6-1 3-1 6-2 8-1 6-3 13 0 18v2h0l1 1c1-3 1-6 2-8 2-5 5-11 7-16l1 2 3-3-3 13c-1 2-1 5-2 6l-1 9 1 3c0 2 0 3 1 4l1-2 1 1c-2 5-3 9-4 14l-1 2h1v1c5-6 7-15 10-22l4-1 5-11c0 6-3 11-5 16-2 2-3 5-3 8v-1l2-2h0l2-1c0-1 1-2 2-2l2-2c1 0 2-1 3-2 1 0 1-1 2-2 2-1 4-4 6-5 0-1 0-2 1-2h1c0 1 0 3-1 5h-1v27l-2 18v9c1 1 1 2 1 3l-3 16c0 4-2 8-2 11 2-4 4-7 6-11 1-4 1-8 2-11 0-2 0-4 1-5v2c0 3 0 4 1 6s0 4 0 6h2l2 2c1-1 2-1 3-2v-4c1-1 1-3 2-4l1 1-3 9c0 1 1 1 2 2l2-2c0 1 0 1-1 2-2 2-5 4-7 6 7-2 9-9 16-10h0c-12 8-23 19-29 31l1 2c2-3 3-6 6-9 2-1 4-5 6-6 0 1 0 0 1 1h1l1-1 2-2h1 1 0c3-3 6-4 10-5-7 9-13 20-16 31 3-2 6-3 10-4h0c-3 2-7 4-9 6v58c0 23-1 48 4 71 2 7 6 15 9 22 4 8 9 17 16 23 13 14 31 22 50 23 14 0 26-5 36-15 17-16 17-40 18-62 4 21 3 46-9 64-11 14-26 23-43 26-8 1-16 2-24 2l-27-3c-25-2-61-1-78 22-1 1-1 2-2 4v1c-6-12-16-18-28-22-16-6-36-6-52-5l-27 2c-19 1-40-2-56-15-5-5-11-11-14-18-6-12-8-26-8-39 0-6 0-11 1-16 2 21 2 45 20 61 10 9 24 13 38 12 20-1 37-10 50-25 11-13 19-30 23-46 5-23 4-48 4-72v-51-1h1c15 12 25 30 27 49 1 10 1 20 0 30l2 2v6h0 0c0-2 0-3 1-5l1-1 2-16h1v-2c2-2 1-2 1-5 1-1 1 0 1-1 0-2 0-3 1-4-1-3-1-5-1-7l-3-28h-2l-2-8c-2-9-7-16-10-25-5-13-9-25-11-39 0-2 0-5 1-7l1-2h0l-1-12h1c4 9 12 15 18 22 5 7 9 15 11 24 0 2 1 4 1 6 0 1 0 1-1 1v1c1 4 3 7 4 11l1 6c1-4 0-7 0-11v1c1-1 1-2 2-3 0-3-1-8-1-12 0-6 1-12 0-18v-10c1-5 1-10 1-14 0-7 0-14-1-20v-2c-1-4-1-8-2-12-1-2-1-3-1-5 1 1 1 2 2 3 1-2 1-4 1-6v-3l-2-16c0-1 0-1 1-1 0 1 1 2 1 3v1c0-3 1-5 1-8v-4-16c0-2-1-4-2-6 0-3 0-6-1-9v-3l1 1c1-4 0-8 1-11v-1l1-4-2-35c1-2 1-8 1-10-1-1-1-2-1-3l3-3v2c-1 1-1 2-1 3-1 2 0 4 0 5h0c1-2 1-4 1-7 2-4 1-9 2-14h0c1-1 1-2 1-3z" class="U"></path><path d="M492 772l2 2v6h-1l-1-1v-7z" class="E"></path><path d="M524 725c0-1 1-2 1-3 2 2 1 5 1 8h-1c0 2 1 3 0 5h-1v-10z" class="B"></path><path d="M568 880c-1-1-2-3-3-4v-1-1h0c3 2 5 4 8 6h-1c-1 0-2 0-3-1l-1 1z" class="P"></path><defs><linearGradient id="AB" x1="530.269" y1="729.783" x2="525.731" y2="732.717" xlink:href="#B"><stop offset="0" stop-color="#7f817d"></stop><stop offset="1" stop-color="#989799"></stop></linearGradient></defs><path fill="url(#AB)" d="M527 721l2 8c0 2 0 5 1 7l-1 6c-1-2-1-3-1-4h-1v-17z"></path><path d="M548 877c1 1 3 3 3 5-3 3-5 5-9 7 1-3 3-5 4-7l2-5z" class="J"></path><path d="M527 738h1c0 1 0 2 1 4l-2 17h-2l2-21z" class="F"></path><path d="M535 783l3 24c1 2 1 4 2 6s2 5 2 7c-1 0-1-2-2-3-2-5-3-10-4-16 0-1-1-4-1-5-1-3-1-6-1-9l1-1v-3z" class="O"></path><path d="M498 758h1c1 2 3 8 2 10-1 1-2 2-2 4 0 4-1 8-3 12v-10l2-16z" class="S"></path><path d="M542 712c2-4 14-24 17-25-2 4-4 8-6 11-1 2-3 4-3 5v1c-1 2-3 4-4 5l-4 9v-6z" class="T"></path><defs><linearGradient id="AC" x1="493.763" y1="792.204" x2="487.751" y2="789.303" xlink:href="#B"><stop offset="0" stop-color="#494748"></stop><stop offset="1" stop-color="#5f615d"></stop></linearGradient></defs><path fill="url(#AC)" d="M487 809c0-2 1-4 1-7l2-9 2-14 1 1h1 0 0c0 10-2 20-7 29z"></path><path d="M502 746v-1c1 7 1 13 1 19l-2 19s-1-1-1-2l1-13c1-2-1-8-2-10v-2c2-2 1-2 1-5 1-1 1 0 1-1 0-2 0-3 1-4zm50-115c1 1 1 2 1 3l-3 16c0 4-2 8-2 11 0 2-2 4-3 6h-1c-1 1-1 1-2 1 4-12 8-24 10-37z" class="H"></path><defs><linearGradient id="AD" x1="547.517" y1="878.535" x2="536.676" y2="881.289" xlink:href="#B"><stop offset="0" stop-color="#454342"></stop><stop offset="1" stop-color="#6c6967"></stop></linearGradient></defs><path fill="url(#AD)" d="M544 870l4 7-2 5c-1 2-3 4-4 7l-6 3c1-4 1-6 4-9v-1c3-2 3-8 4-12z"></path><path d="M568 880l1-1c1 1 2 1 3 1h1c4 3 9 6 14 8l6 3c-3 0-7-1-10-2-4 0-8 1-12 0l2-4-5-5z" class="C"></path><path d="M549 862c3 3 6 7 9 11 3 3 6 5 7 9-1 2-1 3-3 3l-5 1s0-1 1-2c-1-1-1-2-1-3s1-3 1-4-2-3-3-4c-2-3-5-7-6-11z" class="H"></path><path d="M539 807v-4l-1-10c0-3-1-5-1-7s1-2 2-3c1 21 3 42 20 57 5 5 11 9 16 15h0c-5-5-11-9-16-13-10-10-16-21-20-35z" class="E"></path><path d="M500 781c0 1 1 2 1 2-3 16-6 32-13 46-1-1-1-2-2-3 7-14 11-29 14-45z" class="F"></path><path d="M534 688h1v1 2c0 1 0 1-1 2 0 1 0 2-1 3l1 2-1 6 1 2-2 15c-1 5-1 10-2 15-1-2-1-5-1-7l-2-8 7-33z" class="H"></path><path d="M529 729l2-15v2c0 1-1 4 0 5v-3-1-1c1-1 1-1 1-3v-1-1c0-3 0-5 1-7l1 2-2 15c-1 5-1 10-2 15-1-2-1-5-1-7zm-1 67c1-1 1-1 2-1 0 2 0 3 1 5 1 4 2 8 3 11 3 9 7 16 11 24v6c1 1 1 1 0 2-9-14-14-31-17-47z" class="F"></path><path d="M482 859v-2c0-3 3-7 4-9l16-41c0 15-10 41-20 52z" class="J"></path><path d="M550 704l6-8c1-2 3-5 5-6-3 6-8 11-12 17-7 12-12 29-12 43-1 2-1 4-1 5h-1c-1-5 1-12 2-17l-1-1 2-12-1-1c1-2 1-4 2-6l3-6v6l4-9c1-1 3-3 4-5z" class="E"></path><path d="M542 712v6c-3 6-4 13-5 20l-1-1 2-12-1-1c1-2 1-4 2-6l3-6z" class="J"></path><path d="M489 889l-6-3c-2-1-3-2-3-4-1-3 2-8 4-10h0c-1 1-1 2-1 3h0l2-2 1 1-1 2 4 4v2c0 1 1 2 2 3l6 4c1 1 2 3 2 4l-10-4z" class="N"></path><path d="M484 884c-2-1-3-2-3-4 0-1 1-2 2-3 0 2 1 4 1 6v1z" class="E"></path><defs><linearGradient id="AE" x1="485.522" y1="887.405" x2="494.242" y2="883.81" xlink:href="#B"><stop offset="0" stop-color="#5d5c5a"></stop><stop offset="1" stop-color="#8b898a"></stop></linearGradient></defs><path fill="url(#AE)" d="M483 877c3 1 4 5 7 7 0 1 0 1 1 1l6 4c1 1 2 3 2 4l-10-4v-1c0-1-4-3-5-4v-1c0-2-1-4-1-6z"></path><path d="M495 775l1-1v10c-3 18-9 37-22 51-6 7-14 13-21 18-2 0-3 1-5 2 4-4 9-7 13-10 13-10 20-22 26-36 5-9 7-19 7-29 0-2 0-3 1-5z" class="E"></path><path d="M519 663c1-4 3-9 4-13 1-2 2-4 2-7v-2c2-6 6-13 8-19 3-5 5-11 9-16l-14 33c1-2 3-5 4-7 3-5 9-10 14-13-3 4-7 7-10 10-6 7-9 16-11 25 0 2-1 5-1 7h0v3h-1v2 1l-2 2h0c-1-2-1-4-2-6z" class="F"></path><path d="M537 750c0 4-2 10 0 13 0 1-1 1-1 2 1-1 1 0 1-1v-3c1-2 1-3 1-5 1-4 2-9 4-13 2-5 3-10 6-15l1 1c-3 5-5 10-7 15-2 7-3 14-3 21v18c-1 1-2 1-2 3s1 4 1 7l1 10v4h-1l-3-24v-4-15c0-4-1-7-1-11 0-5 1-11 2-16l1 1c-1 5-3 12-2 17h1c0-1 0-3 1-5z" class="C"></path><defs><linearGradient id="AF" x1="533.564" y1="655.347" x2="518.012" y2="672.721" xlink:href="#B"><stop offset="0" stop-color="#919092"></stop><stop offset="1" stop-color="#b7b6b5"></stop></linearGradient></defs><path fill="url(#AF)" d="M524 661l11-13c4-6 8-11 13-16v1c-5 6-10 13-14 20-3 4-6 8-8 12-5 11-7 22-8 34-1-2-1-2-1-4h0c0-1 0-3 1-4v-3c0-1 0-2 1-3v-2-2c1-1 1-1 1-2v-2c1-1 1 0 1-1l-3-3v6c-1 4-1 8-2 12 0 3 1 4-1 6h-1c0-2 0-4 1-6l1-18c0-1 0-2 1-3s2-5 2-7c1 2 1 4 2 6h0l2-2v-1-2h1v-3z"></path><path d="M555 672c2-1 4-5 6-6 0 1 0 0 1 1h1l1-1 2-2h1 1c-2 3-5 5-7 8l-1 1-3 5-3 3v1l-2 3c-2 2-3 6-4 8 0 2-1 4-2 6-1 0-1 1-1 1l-2 5-1 1-1 4c-1 1-1 2-1 2l-2 6h-1 0v-1c0-1 0-1 1-2v-3-1c0-3 2-6 2-8l9-22c2-3 3-6 6-9z" class="J"></path><defs><linearGradient id="AG" x1="521.59" y1="701.982" x2="513.91" y2="712.018" xlink:href="#B"><stop offset="0" stop-color="#afabad"></stop><stop offset="1" stop-color="#c8c8c7"></stop></linearGradient></defs><path fill="url(#AG)" d="M517 700h1c4-7 6-15 10-22-1 8-6 15-8 22 6-8 8-17 13-25-2 5-4 10-5 15-2 7-2 13-4 20l-1-1-4 24c0-1 0-3-1-4l-1 1v-5h-1v-9c0 3 0 5-1 8h0v-2c1-7 1-14 2-22z"></path><defs><linearGradient id="AH" x1="526.793" y1="712.485" x2="514.707" y2="716.015" xlink:href="#B"><stop offset="0" stop-color="#1b1c16"></stop><stop offset="1" stop-color="#2d2a2c"></stop></linearGradient></defs><path fill="url(#AH)" d="M517 725c0-12 2-22 9-32l-3 16-4 24c0-1 0-3-1-4l-1 1v-5z"></path><path d="M537 724l1 1-2 12c-1 5-2 11-2 16 0 4 1 7 1 11v15 4 3l-1 1c0 3 0 6 1 9 0 1 1 4 1 5h-1 0v-3-1h-1-1v-2-1l-1-4c-1-1-1-1-1-2v-2c1 1 1 2 2 3v2-4c0-2 0-4-1-6v-6-1-4l-1-1v-3c-1-1-1-4-1-6-1 2 0 6 0 9 0 2-1 4 0 6s0 4 0 6c1 2 1 3 0 4v1 6c0 2 2 5 1 7v1c-1-2-1-3-1-5-1-5-2-11-1-15l1-21c0-8 1-17 3-26 1 1 1 1 1 2 1-3 1-8 3-11z" class="D"></path><defs><linearGradient id="AI" x1="535.033" y1="766.226" x2="531.467" y2="766.774" xlink:href="#B"><stop offset="0" stop-color="#7e7c7d"></stop><stop offset="1" stop-color="#949392"></stop></linearGradient></defs><path fill="url(#AI)" d="M537 724l1 1-2 12c-1 5-2 11-2 16 0 4 1 7 1 11v15 4 3l-1 1-1-5c-1-16-1-31 1-47 1-3 1-8 3-11z"></path><path d="M533 782c2-3 0-5 1-8 0 1 0 3 1 4v1 4 3l-1 1-1-5z" class="H"></path><defs><linearGradient id="AJ" x1="551.828" y1="839.716" x2="527.406" y2="842.818" xlink:href="#B"><stop offset="0" stop-color="#9b9b9a"></stop><stop offset="1" stop-color="#b7b6b6"></stop></linearGradient></defs><path fill="url(#AJ)" d="M522 806c1-2 0-3 2-5 2 18 7 34 17 49 2 4 5 8 8 11v1c1 4 4 8 6 11 1 1 3 3 3 4s-1 3-1 4c-4-7-9-13-13-19-10-14-20-32-21-49l-1-7z"></path><path d="M548 679l1 2-9 22c0 2-2 5-2 8v1 3c-1 1-1 1-1 2v1h0 1 1c-1 2-1 4-2 6-2 3-2 8-3 11 0-1 0-1-1-2-2 9-3 18-3 26l-1 21c-1 4 0 10 1 15-1 0-1 0-2 1h0c-2-12-3-25-3-37h2c0 4-1 11 1 15v-6-12l3-23c1-5 1-9 2-13 3-14 9-29 15-41z" class="H"></path><path d="M538 711v1 3c-1 1-1 1-1 2v1h0 1 1c-1 2-1 4-2 6-2 3-2 8-3 11 0-1 0-1-1-2 0-2 1-4 1-6 1-6 2-11 4-16z" class="G"></path><defs><linearGradient id="AK" x1="567.116" y1="869.836" x2="575.539" y2="853.339" xlink:href="#B"><stop offset="0" stop-color="#858584"></stop><stop offset="1" stop-color="#a2a0a1"></stop></linearGradient></defs><path fill="url(#AK)" d="M545 835c5 7 10 15 16 21 8 9 19 17 30 24 3 2 7 4 11 6 1 1 5 2 6 3v1c-3-1-7-3-10-4-20-8-38-22-51-40l-2-3c1-1 1-1 0-2v-6z"></path><path d="M569 650c0 1 0 1-1 2-2 2-5 4-7 6 7-2 9-9 16-10h0c-12 8-23 19-29 31s-12 27-15 41c-1 4-1 8-2 13l-3 23v12 6c-2-4-1-11-1-15l2-17 1-6c1-5 1-10 2-15l2-15c1-7 3-13 5-19 2-3 4-7 5-11h1 1c3-6 8-12 12-17 3-3 6-5 9-7l2-2z" class="D"></path><defs><linearGradient id="AL" x1="453.052" y1="888.249" x2="463.241" y2="822.875" xlink:href="#B"><stop offset="0" stop-color="#7e7d7d"></stop><stop offset="1" stop-color="#979595"></stop></linearGradient></defs><path fill="url(#AL)" d="M488 829l-6 11c-14 23-38 43-65 50l12-6c23-14 44-34 57-58 1 1 1 2 2 3z"></path><defs><linearGradient id="AM" x1="473.278" y1="844.285" x2="495.225" y2="849.831" xlink:href="#B"><stop offset="0" stop-color="#848483"></stop><stop offset="1" stop-color="#aca9aa"></stop></linearGradient></defs><path fill="url(#AM)" d="M504 790v14 4c0 1 1 1 1 2 0 6-1 12-3 17-4 14-12 27-20 39v1c-3 3-10 10-11 14v4 1c-3-1-6-2-8-4 1-3 5-7 7-10l12-13c10-11 20-37 20-52v-7c0-1 1-1 1-1v-3c0-2 1-4 1-6z"></path><path d="M548 661c2-4 4-7 6-11 1-4 1-8 2-11 0-2 0-4 1-5v2c0 3 0 4 1 6s0 4 0 6h2l2 2c1-1 2-1 3-2v-4c1-1 1-3 2-4l1 1-3 9c0 1 1 1 2 2-3 2-6 4-9 7-4 5-9 11-12 17h-1-1c-1 4-3 8-5 11-2 6-4 12-5 19l-1-2 1-6-1-2c1-1 1-2 1-3 1-1 1-1 1-2v-2-1h-1c2-7 5-13 8-20 1 0 1 0 2-1h1c1-2 3-4 3-6z" class="I"></path><path d="M557 636c0 3 0 4 1 6s0 4 0 6h2l-2 3h-1-1v2c-1 0-2 2-3 3h0c2-4 3-9 3-14 0-2 0-4 1-6z" class="P"></path><path d="M567 640l1 1-3 9c-4 3-7 6-10 10-2 2-4 6-6 7 2-3 8-11 7-14v-2h1 1l2-3 2 2c1-1 2-1 3-2v-4c1-1 1-3 2-4z" class="C"></path><path d="M549 667c2-1 4-5 6-7 3-4 6-7 10-10 0 1 1 1 2 2-3 2-6 4-9 7-4 5-9 11-12 17h-1-1l1-1c0-2 2-7 4-8z" class="H"></path><path d="M542 668c1 0 1 0 2-1h1c-1 3-3 6-4 10-3 7-5 14-7 21l-1-2c1-1 1-2 1-3 1-1 1-1 1-2v-2-1h-1c2-7 5-13 8-20z" class="N"></path><path d="M523 709l1 1-2 12c1 2 1 2 2 3v10l-1 51 1 15c-2 2-1 3-2 5l1 7c0-1-1-2-1-3h0l-1 2h0l-2 5-1-36v-21l1-27 4-24z" class="E"></path><path d="M522 806v-20h1l1 15c-2 2-1 3-2 5z" class="F"></path><path d="M518 781l1 3c1-1 1-9 1-11v-1c1 13 0 27 1 40l-2 5-1-36z" class="L"></path><path d="M523 709l1 1-2 12c0 5-1 11-2 16v34 1c0 2 0 10-1 11l-1-3v-21l1-27 4-24z" class="K"></path><path d="M503 743l-2-47v-2c1 2 1 4 2 5v1c2 2 0 5 1 7 1 1 1 2 1 3v6-1-2h1v-1l1-1v-1c-1 0 0-1-1-2v-1-1c1 1 2 2 2 3 0 2 0 7-1 8 1 6 1 11 2 17v10c1 4 1 8 0 11l-1 1v17h0l-1 25c0 8 0 15-2 22-2 16-8 29-16 43-2 2-3 6-5 8-1-1 0-1 0-2s1-1 1-2h0c1-1 1-1 1-2 1-1 2-2 2-3s0 0 1-1v-1l2-2c0-2-1 0 0-2 1-1 1-2 2-2v-1-1c1 0 1 0 1-1 1-2 3-4 3-6h0c1-2 0-2 1-3 0-2 1-2 2-4h0v-3c-3 9-8 18-13 26-1 1-3 4-5 5h0c8-12 16-25 20-39 2-5 3-11 3-17 0-1-1-1-1-2v-4-14-5c1-6 1-11 1-17h-2v-4c0-6 0-12-1-19v-1c1 0 1 1 1 2v-3z" class="G"></path><path d="M505 768v-3 18l1-1c0 9 0 19-1 28 0-1-1-1-1-2v-4-14-5c1-6 1-11 1-17z" class="L"></path><path d="M508 773v-17c-1-6-3-35-1-39 1 6 1 11 2 17v10c1 4 1 8 0 11l-1 1v17h0z" class="I"></path><path d="M503 743l-2-47v-2c1 2 1 4 2 5v1c0 7 1 14 2 22v20l1 28c0 4 1 9 0 12l-1 1v-18 3h-2v-4c0-6 0-12-1-19v-1c1 0 1 1 1 2v-3z" class="K"></path><path d="M502 745v-1c1 0 1 1 1 2v-3l2 22v3h-2v-4c0-6 0-12-1-19z" class="B"></path><path d="M521 812h0c2 18 8 35 17 50l6 8c-1 4-1 10-4 12v1c-3 3-3 5-4 9-2 0-3 1-5 2-8 5-13 9-17 17-3-8-8-13-15-18 0-1-1-3-2-4l-6-4c-1-1-2-2-2-3v-2l-4-4 1-2-1-1-2 2h0c0-1 0-2 1-3h0v-1c2-2 3-6 5-8 8-14 14-27 16-43h2l-3 22-4 14c-2 6-4 10-3 16 1 4 3 8 6 11l7 8h0c0-1 1-2 1-3l1 1c1-2 1-6 1-8 0-4 0-5 3-7v21l9-9c3-3 6-7 7-12 1-10-5-21-8-30-3-8-5-18-5-26v-1l2-5z" class="F"></path><path d="M531 894c1-1 1-2 2-2l1-1h-1l1-2h-1l1-1c1-2 2-2 2-4h0l-3 3c0-2 4-6 5-8 1-1 1-3 2-5v9c-3 3-3 5-4 9-2 0-3 1-5 2z" class="N"></path><path d="M538 862l6 8c-1 4-1 10-4 12v1-9c1-4-1-8-2-12z" class="E"></path><path d="M475 630h0l-1-12h1c4 9 12 15 18 22 5 7 9 15 11 24 0 2 1 4 1 6 0 1 0 1-1 1v1c1 4 3 7 4 11 0 2 0 3-1 5l3 19 1 32h-1c0 1 0 3-1 5v-10c-1-6-1-11-2-17 1-1 1-6 1-8 0-1-1-2-2-3v1 1c1 1 0 2 1 2v1l-1 1v1h-1v2 1-6c0-1 0-2-1-3-1-2 1-5-1-7v-1c-1-1-1-3-2-5v2l2 47v3c0-1 0-2-1-2v1 1c-1-3-1-5-1-7l-3-28h-2l-2-8c-2-9-7-16-10-25-5-13-9-25-11-39 0-2 0-5 1-7l1-2z" class="U"></path><path d="M502 673l2-1c1 4 3 7 4 11 0 2 0 3-1 5l-5-15z" class="I"></path><path d="M493 687c2 8 5 16 5 24h-2l-2-8c2-3-2-11-2-14l1-2z" class="E"></path><path d="M502 673c-4-11-11-19-16-29 1 1 1 0 1 1 7 8 14 17 17 26v1l-2 1z" class="F"></path><path d="M475 630c2 10 6 19 9 29 4 9 7 19 9 28l-1 2c0 3 4 11 2 14-2-9-7-16-10-25-5-13-9-25-11-39 0-2 0-5 1-7l1-2z" class="O"></path><path d="M482 663c4 8 7 17 10 26 0 3 4 11 2 14-2-9-7-16-10-25l1-1c0-2-3-8-3-10v-4z" class="J"></path><path d="M473 639c0-2 0-5 1-7 1 11 5 21 8 31v4c0 2 3 8 3 10l-1 1c-5-13-9-25-11-39z" class="G"></path><path d="M515 438l2 2c0 1 1 4 0 5v5 2c0 2-1 7 0 9l1 1c1-2 1-6 2-8h0c2-4 3-9 5-12h1l-3 9c-2 4-3 8-4 12-2 6-2 13-2 20v28l1-1c0 3-1 10 0 12 0 4-3 13 0 18 0 2 1 4 0 6-1 3-1 6-2 8-1 6-3 13 0 18v2h0l1 1c1-3 1-6 2-8 2-5 5-11 7-16l1 2 3-3-3 13c-1 2-1 5-2 6l-1 9 1 3c0 2 0 3 1 4l1-2 1 1c-2 5-3 9-4 14l-1 2h1v1c5-6 7-15 10-22l4-1 5-11c0 6-3 11-5 16-2 2-3 5-3 8-7 12-9 24-11 38l-5 23v3l-2 15c-1 1-1 2-1 3l-1 18c-1 2-1 4-1 6h1c2-2 1-3 1-6 1-4 1-8 2-12v-6l3 3c0 1 0 0-1 1v2c0 1 0 1-1 2v2 2c-1 1-1 2-1 3v3c-1 1-1 3-1 4h0c0 2 0 2 1 4l-1 1c-1 8-1 15-2 22v2h0c1-3 1-5 1-8v9h1v5l1-1c1 1 1 3 1 4l-1 27v21l1 36v1c0 8 2 18 5 26 3 9 9 20 8 30-1 5-4 9-7 12l-9 9v-21c-3 2-3 3-3 7 0 2 0 6-1 8l-1-1c0 1-1 2-1 3h0l-7-8c-3-3-5-7-6-11-1-6 1-10 3-16l4-14 3-22h-2c2-7 2-14 2-22l1-25h0v-17l1-1c1-3 1-7 0-11 1-2 1-4 1-5h1l-1-32-3-19c1-2 1-3 1-5l1 6c1-4 0-7 0-11v1c1-1 1-2 2-3 0-3-1-8-1-12 0-6 1-12 0-18v-10c1-5 1-10 1-14 0-7 0-14-1-20v-2c-1-4-1-8-2-12-1-2-1-3-1-5 1 1 1 2 2 3 1-2 1-4 1-6v-3l-2-16c0-1 0-1 1-1 0 1 1 2 1 3v1c0-3 1-5 1-8v-4-16c0-2-1-4-2-6 0-3 0-6-1-9v-3l1 1c1-4 0-8 1-11v-1l1-4-2-35c1-2 1-8 1-10-1-1-1-2-1-3l3-3v2c-1 1-1 2-1 3-1 2 0 4 0 5h0c1-2 1-4 1-7 2-4 1-9 2-14h0c1-1 1-2 1-3z" class="K"></path><path d="M510 580c1 7 0 15 0 22v-2c-1-4-1-8-2-12-1-2-1-3-1-5 1 1 1 2 2 3 1-2 1-4 1-6z" class="P"></path><path d="M509 689c1-4 0-7 0-11v1c1-1 1-2 2-3v17l-1 5-1-9z" class="E"></path><path d="M508 683l1 6 1 9 1-5v12l-1 2-3-19c1-2 1-3 1-5zm3-131c1 6 0 12 0 17 0 2 1 5 0 6 0-1 0-2-1-3v1 4l-2-16c0-1 0-1 1-1 0 1 1 2 1 3v1c0-3 1-5 1-8v-4z" class="F"></path><path d="M510 507l1-4v33c0-2-1-4-2-6 0-3 0-6-1-9v-3l1 1c1-4 0-8 1-11v-1z" class="H"></path><path d="M514 649l1 2c1-2 2-5 2-7 0 2 0 7 1 9l1 2-2 15c-1 1-1 2-1 3h0c0-3 0-5-1-7 0-3 1-7 0-9-1-3-1-5-1-8z" class="L"></path><path d="M517 730l1-1c1 1 1 3 1 4l-1 27c-2 3 1 15 0 18-2-2-1-6-1-9v-39z" class="D"></path><path d="M517 511l1-1c0 3-1 10 0 12 0 4-3 13 0 18 0 2 1 4 0 6-1 3-1 6-2 8l1-43z" class="H"></path><path d="M526 551l1 2c-3 6-3 13-5 19l-3 9v-11c0 2-1 4-2 6v3c0 1-1 1-1 1h0c-1-1 0-4 0-6l1 1c1-3 1-6 2-8 2-5 5-11 7-16z" class="F"></path><path d="M514 649l1-16c2-8 1-16 2-24l1 1v1 14l1-1c0 5 1 10 0 15 0 1 0 2-1 3 0 2-1 5 0 7 1 1 1 2 1 3v3l-1-2c-1-2-1-7-1-9 0 2-1 5-2 7l-1-2z" class="I"></path><path d="M530 550l-3 13c-1 2-1 5-2 6l-1 9 1 3c0 2 0 3 1 4l1-2 1 1c-2 5-3 9-4 14l-1 2h1v1c0 1-1 3 0 4 0 1 0 2-1 3v1c0 2-1 3-2 4 0-1-1-2-1-3 1-2 1-3 0-5h0v-2c-1 6-1 14-1 21l-1 1v-14-1l-1-1v-9c0-6 1-13 2-19l3-9c2-6 2-13 5-19l3-3z" class="N"></path><path d="M518 610v-1c1-5 0-10 1-15 1-6 2-11 3-16 0-2 0-3 1-4 0-1 0-2 1-3v-2h0 1l-1 9-3 19c-1 1-1 4-1 6-1 6-1 14-1 21l-1 1v-14-1z" class="K"></path><path d="M524 578l1 3c0 2 0 3 1 4l1-2 1 1c-2 5-3 9-4 14l-1 2h1v1c0 1-1 3 0 4 0 1 0 2-1 3v1c0 2-1 3-2 4 0-1-1-2-1-3 1-2 1-3 0-5h0v-2c0-2 0-5 1-6l3-19z" class="R"></path><path d="M526 585l1-2 1 1c-2 5-3 9-4 14l-1 2h1v1c0 1-1 3 0 4 0 1 0 2-1 3v1c0 2-1 3-2 4 0-1-1-2-1-3 1-2 1-3 0-5l6-20z" class="L"></path><path d="M538 578l5-11c0 6-3 11-5 16-2 2-3 5-3 8-7 12-9 24-11 38l-5 23c0-1 0-2-1-3-1-2 0-5 0-7 1-1 1-2 1-3 1-5 0-10 0-15 0-7 0-15 1-21v2h0c1 2 1 3 0 5 0 1 1 2 1 3 1-1 2-2 2-4v-1c1-1 1-2 1-3-1-1 0-3 0-4 5-6 7-15 10-22l4-1z" class="F"></path><path d="M524 605h1v4 1l-1 3h0v2c-1 1-1 3-1 4l-1 1v2h-1c0-3-1-7 0-9 1-1 2-2 2-4v-1c1-1 1-2 1-3h0z" class="I"></path><path d="M524 601c5-6 7-15 10-22l4-1c-3 7-6 12-9 18-2 3-4 6-5 9h0c-1-1 0-3 0-4z" class="C"></path><defs><linearGradient id="AN" x1="501.372" y1="832.381" x2="514.472" y2="833.826" xlink:href="#B"><stop offset="0" stop-color="#090807"></stop><stop offset="1" stop-color="#343434"></stop></linearGradient></defs><path fill="url(#AN)" d="M509 744c1-2 1-4 1-5h1l-1 129 2-2c0 4-2 13 0 15h1c0 2 0 6-1 8l-1-1c0 1-1 2-1 3h0l-7-8c-3-3-5-7-6-11-1-6 1-10 3-16l4-14 3-22h-2c2-7 2-14 2-22l1-25h0v-17l1-1c1-3 1-7 0-11z"></path><path d="M508 773v-17l1-1v20 2l-1 1v-5z" class="K"></path><path d="M510 868l2-2c0 4-2 13 0 15h1c0 2 0 6-1 8l-1-1c0 1-1 2-1 3-1-4 0-19 0-23z" class="H"></path><path d="M508 773h0v5l1-1v-2l-2 45h-2c2-7 2-14 2-22l1-25z" class="L"></path><path d="M517 769c0 3-1 7 1 9 1-3-2-15 0-18v21l1 36v1c0 8 2 18 5 26 3 9 9 20 8 30-1 5-4 9-7 12l-9 9v-21c-3 2-3 3-3 7h-1c-2-2 0-11 0-15l1-7v-1c-1-6 0-14 0-20v-18c0-2 0-5 1-7l1-1v-1c0-4 0-8 1-11l1-31z" class="G"></path><path d="M513 859h0l1-1c0 2 0 3 1 5v-1c0-4 0-8 1-12v-1 17c0 3 1 6 0 8-3 2-3 3-3 7h-1c-2-2 0-11 0-15l1-7z" class="E"></path><defs><linearGradient id="AO" x1="524.89" y1="853.686" x2="504.61" y2="820.314" xlink:href="#B"><stop offset="0" stop-color="#969193"></stop><stop offset="1" stop-color="#bbbcba"></stop></linearGradient></defs><path fill="url(#AO)" d="M513 859v-1c-1-6 0-14 0-20v-18c0-2 0-5 1-7l1-1v-1c0-4 0-8 1-11v49 1c-1 4-1 8-1 12v1c-1-2-1-3-1-5l-1 1h0z"></path></svg>