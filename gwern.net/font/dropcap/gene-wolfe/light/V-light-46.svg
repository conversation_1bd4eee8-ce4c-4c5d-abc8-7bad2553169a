<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="96 95 854 836"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#dfd9d6}.C{fill:#cac1be}.D{fill:#d6cdc7}.E{fill:#ebe5e2}.F{fill:#ece8e4}.G{fill:#e0d4cf}.H{fill:#f4f0ed}.I{fill:#39404a}.J{fill:#c73231}.K{fill:#c2b8b3}.L{fill:#a19c9c}.M{fill:#15181a}.N{fill:#b8aea8}.O{fill:#7d7b80}.P{fill:#c1b9b4}.Q{fill:#981e1e}.R{fill:#cec9c5}.S{fill:#f2e6e5}.T{fill:#ebdbd9}.U{fill:#d0cccc}.V{fill:#050607}.W{fill:#470809}.X{fill:#7f1718}.Y{fill:#520e0f}.Z{fill:#a41d1e}.a{fill:#bc2a2a}.b{fill:#807c7c}.c{fill:#4d0d0f}.d{fill:#131719}.e{fill:#d03a3b}.f{fill:#3a3f47}.g{fill:#2f353b}.h{fill:#1e2227}.i{fill:#881c1c}.j{fill:#aca8a9}.k{fill:#530c0c}.l{fill:#bab6b6}.m{fill:#821618}.n{fill:#7c1414}.o{fill:#958c88}.p{fill:#525a68}.q{fill:#771515}.r{fill:#252b30}.s{fill:#090b0c}.t{fill:#afaaa7}.u{fill:#a9292a}.v{fill:#af1d1d}.w{fill:#c32e2f}.x{fill:#630e0f}.y{fill:#aea59f}.z{fill:#1b2024}.AA{fill:#390b0a}.AB{fill:#5b1413}.AC{fill:#ca3333}.AD{fill:#23282b}.AE{fill:#444a57}.AF{fill:#2d343b}.AG{fill:#8b1214}.AH{fill:#861616}.AI{fill:#ad2d2e}.AJ{fill:#cc2f2e}.AK{fill:#908885}.AL{fill:#5d6471}.AM{fill:#d64e4d}.AN{fill:#2b0909}.AO{fill:#6c0e0f}.AP{fill:#9e958f}.AQ{fill:#8e8f94}.AR{fill:#370b0a}.AS{fill:#3b0809}.AT{fill:#f8f6f6}.AU{fill:#6e717a}.AV{fill:#ebd4d2}.AW{fill:#d04648}.AX{fill:#a79d94}.AY{fill:#858992}.AZ{fill:#696f7b}.Aa{fill:#323942}.Ab{fill:#4e5563}.Ac{fill:#49505c}.Ad{fill:#d23b3b}.Ae{fill:#5d606b}.Af{fill:#e6c7c6}.Ag{fill:#626978}.Ah{fill:#f0d7d8}.Ai{fill:#585a60}.Aj{fill:#0d0e0f}.Ak{fill:#dea1a2}.Al{fill:#310404}.Am{fill:#e56c6c}.An{fill:#e3bfc1}.Ao{fill:#ddb3b3}.Ap{fill:#efc6c6}.Aq{fill:#d8a6a8}.Ar{fill:#d48789}</style><path d="M221 535c-1 1-1 1-1 2l1 1h-1c-1 0-3-1-4-2l5-1z" class="R"></path><path d="M219 598c0 1-1 2-1 2-2 1-2 1-4 0 1-1 3-1 5-2z" class="D"></path><path d="M750 654l1 2 1 3-3-2 1-3z" class="y"></path><path d="M706 152c1 0 2 0 3 1-1 1-5 1-7 2 0-1 1-1 1-1 1-1 2-2 3-2zm149 129l7 1c-1 1-2 1-4 1-2-1-3-1-4-2h1z" class="R"></path><path d="M207 407h4 0l3 2v1c-2-1-5-2-7-2v-1z" class="N"></path><path d="M853 284c2 0 5-1 7 0-2 0-5 1-7 2 0-1-1-1-2-2h2z" class="y"></path><path d="M812 537l8 1c-3 1-7 1-10 1v-1l2-1z" class="P"></path><path d="M158 320h4l3 2-1 1-7-1h0l-1-1 2-1z" class="K"></path><path d="M745 652l5 2-1 3c-2-2-3-3-5-3l1-2z" class="AX"></path><path d="M288 747v1c-1 1-1 1-1 3h0l-1 1-5 2 7-7z" class="C"></path><path d="M98 229v3c0 2-1 4-2 6 0 0 0-1-1-2 0-1 2-5 3-7z" class="r"></path><path d="M862 282l6 1c-2 0-5 1-8 1-2-1-5 0-7 0 1-1 3-1 5-1s3 0 4-1z" class="C"></path><path d="M165 322h2l6 2 3 2h-1c-1 0-2 0-2-1l-1 1-8-3 1-1z" class="N"></path><path d="M852 353l13 5c-3 1-3 1-6 0 0-1-1-1-2-2l-4-1c0-1-1-2-1-2zm-48 182c3 1 5 1 8 2l-2 1h-4-1c-1-1-2-1-3-2 1 0 2-1 2-1z" class="D"></path><path d="M452 100l4 5-2 2v1l-2-2c0-2-1-4 0-6z" class="S"></path><path d="M221 539c-5-1-9-1-13 0h-1s0-1 1-1l8-2c1 1 3 2 4 2l1 1z" class="D"></path><path d="M322 760c0-1 0 0 1-1v-1c1 0 1 0 2 1-1 3-1 5-3 8h-1 0l1-7z" class="B"></path><path d="M855 320l3 1h3v1l-9 2c-1-1-2-1-3-1l6-3z" class="C"></path><path d="M204 485l2 1c-2 1-3 1-4 2-3 1-6 0-9 1h-4l15-4z" class="P"></path><path d="M856 315c4 2 8 4 12 5-2 1-4 1-6 0l-1 1h-3l-3-1 7-1c-2-1-4-2-5-3l-1-1z" class="R"></path><path d="M507 905c2 2 2 3 3 5 0 1 0 2 1 3l1 1v1 1l1 1c1-1 1-3 1-4h0c0 2 0 4 1 6l-1 2c-1-1-2-2-2-4l-5-12z" class="d"></path><path d="M177 306c-1 2-1 4-1 5-4 3-8 5-12 7l-1-1h1l1-1c5-2 8-5 12-10z" class="P"></path><path d="M615 791l2 1 1 3 3 12-6-11v-2-3z" class="y"></path><path d="M615 791l2 1 1 3-3-1v-3z" class="j"></path><path d="M320 155h0c-2-1-5-1-6-1 2-3 9-2 12-3 3 0 5-1 7-1v1c-3 1-6 1-8 1-1 0-2 0-3 1-1 0-1 0-2 1v1z" class="B"></path><path d="M800 595c3 2 7 3 10 5 3 1 5 1 7 3l-13-2h3l-3-2h1l-1-1c-2-1-3-2-4-3z" class="l"></path><path d="M281 754l5-2c-1 2-3 3-5 5l-11 8h0l11-11z" class="K"></path><path d="M868 320l13 3h1c-7 1-14-1-21-1v-1l1-1c2 1 4 1 6 0z" class="P"></path><path d="M715 673v-1c1-2 1-3 2-4l2 1c0 5 0 9 1 14l1 4h0l-2-5c0-1 0-2-1-3 0-2 0-4-1-5l-2-1z" class="L"></path><path d="M365 729c3 0 5 0 8 2h-5c-1 1-2 1-2 2l1 1c1 3 1 5 1 8h-1c0-5-1-8-4-12h1l1-1z" class="y"></path><path d="M104 219v2c-1 1-2 2-2 3l4-4h1l-9 12v-3c0-3 4-8 6-10z" class="g"></path><path d="M157 322c-4 0-8 0-12 1h-5c5-2 10-2 15-4 3 0 6-1 8-2l1 1h-1c-2 1-4 1-5 2l-2 1 1 1h0z" class="D"></path><path d="M95 236c1 1 1 2 1 2 0 3-1 8 0 10h0c1 2 2 3 2 5 1 2 1 3 1 5-4-7-6-13-4-22z" class="s"></path><path d="M333 733h1c-5 7-8 14-9 22h0v-5l3-13c1-1 3-2 3-3l2-1z" class="N"></path><path d="M388 745h1c1 0 1 1 2 1l2 2h1c2 1 3 2 4 3l4 8-1 2c-4-6-7-11-13-16z" class="AK"></path><path d="M700 750c0 2 1 5 1 7 0 1 1 3 2 5h0l1 6c-4-5-5-9-6-16h1 1v-2z" class="G"></path><path d="M751 656c4 2 7 5 9 8 1 3 2 5 4 7l-1 2c-3-6-6-10-11-14l-1-3z" class="t"></path><path d="M655 751c0-1 0-2-1-2l1-1 3 3 4 2 3 8c1 1 2 2 2 3-4-3-8-9-12-13z" class="y"></path><path d="M450 107v-2c0-3 0-6-1-9 1 1 2 3 3 4-1 2 0 4 0 6-1 3-1 8-2 11l-2-2 2-4-2-1 2-3z" class="P"></path><path d="M448 110l2-3c0 1 1 2 0 3v1l-2-1z" class="D"></path><path d="M667 94c0 1 0 1 1 2-1 14 1 24 7 37-1-1-2-2-2-3 0-2-2-5-3-7-3-9-5-20-3-29z" class="AX"></path><path d="M732 747c3 1 4 2 6 4l3 3c1 0 1-1 2-1l11 12h0c-7-6-15-12-23-17h1v-1z" class="AK"></path><path d="M214 409l3 1c8 6 11 12 13 21l-1 1v-2-1l-1-1v4h0l-1-3c-2-9-5-14-13-19v-1z" class="y"></path><path d="M320 744l2 1 1 2v1c1 1 1 2 2 2v5h0v4c-1-1-1-1-2-1v1c-1 1-1 0-1 1v-7l-2-5v-2-2z" class="G"></path><path d="M320 744l2 1 1 2v1h-1v5l-2-5v-2-2z" class="D"></path><path d="M631 749c1-1 0-1 1-1 2-1 3-2 5-2-6 3-10 8-13 14-2-4 1-6 2-10v-1l2-1c1 1 1 1 3 1z" class="b"></path><path d="M891 207c4-2 9-4 12-7 0-1 1-1 2-2v2l-1 2c-1 1-1 0-2 1 0 2-1 4-2 4-3 1-4 3-6 4v-1l2-2c-2 0-3 1-4 1l1-1 2-2c-1 1-1 1-3 1h-1z" class="AD"></path><path d="M452 106l2 2c0 3 1 6 1 10h-1v1c-2 1-3 1-5 1l1-3c1-3 1-8 2-11z" class="H"></path><path d="M219 598c1 0 2-1 3-1v1h-1c1 0 2 0 3 1l-1 1h0v1h0c1 1 2 2 2 3l1 2c0 2 0 2-1 3h-1v-2c0-2 0-3-2-4-3-2-11-1-16 0 3-1 5-2 8-3 2 1 2 1 4 0 0 0 1-1 1-2z" class="C"></path><path d="M219 598c1 0 2-1 3-1v1h-1c1 0 2 0 3 1l-1 1h0v1h0c1 1 2 2 2 3-2-2-3-3-7-4 0 0 1-1 1-2z" class="G"></path><path d="M812 484c3 0 5 0 8 1 1 0 1-1 2-1h1 0l10 3c-4 1-10 0-14 0h-7c-2-1-6-1-8-1 1 0 2 0 3-1h0l5-1z" class="t"></path><path d="M802 615c-1-1-4-4-5-6s-1-4 0-7c3-3 4-3 7-3l3 2h-3c-3 1-4 2-5 4s-1 4 0 5c2 4 7 8 11 11h-3c-1-2-3-4-5-6z" class="AX"></path><path d="M764 671l3 6c3 9-1 17 2 26 1 3 2 5 5 7 1 1 3 3 3 4h0c-5-4-7-7-9-13-1-2-1-5-1-8 0-8-1-13-4-20l1-2z" class="K"></path><path d="M291 649c1 0 3 0 5 1 1 0 3 2 5 2v1c-1 1-1 2-2 2-2-1-4-2-7-3-5-2-9 0-14 3 2-2 4-3 7-5 2 0 4-1 6-1z" class="o"></path><path d="M179 277c1 0 2 1 4 1-2 1-4 2-6 2s-4 1-6 2c-2 0-3 1-4 1l1 1h2c4 3 6 5 8 9v2c-1-1-1-3-2-4-2-3-6-6-9-7h-1c-3 0-6-1-9-1l-1-1c9 0 15 0 23-5z" class="P"></path><path d="M221 538c3 1 5 1 8 3l5 4c2 1 2 2 4 2 3 0 6-1 8-1 2 2 4 1 6 2 1 0 3 4 4 4-1 0-2 0-2-1-3-1-9-4-12-4v1h-1v1c-2 1-2 1-4 0-5-5-9-9-16-10l-1-1h1z" class="AP"></path><path d="M807 621h3c6 5 11 11 17 17l-1 1v-1l-12-9c0-1-5-4-5-4 0-2-1-2-2-4h0z" class="K"></path><path d="M706 742c2-1 4-2 7-2-3 2-5 2-7 5-1 1-1 2-1 4-2 4-3 8-2 13-1-2-2-4-2-5 0-2-1-5-1-7l1-3h1c1-1 3-3 4-5z" class="N"></path><path d="M448 110l2 1-2 4c-2 4-4 6-9 7h0c-3-2-5 0-8-1-2-1-3-1-4-2l4 1c2 1 5 0 7 0 4-1 8-6 10-10z" class="B"></path><path d="M544 112l11 3c7-2 12-6 16-12-1 2-2 4-2 5l-1 1c-1 3-3 5-5 7l-4 1-8-2c-2-1-5-2-8-3h1z" class="D"></path><defs><linearGradient id="A" x1="188.335" y1="267.677" x2="185.523" y2="277.837" xlink:href="#B"><stop offset="0" stop-color="#999392"></stop><stop offset="1" stop-color="#b9b1ae"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M179 277c5-3 8-7 11-11v1l2 2v-1l1 2-1 1-3 3h0l-4 3-2 1c-2 0-3-1-4-1z"></path><path d="M241 549c1 2 1 3 2 5h0c2 8 0 18-4 25h0c-1-1 1-6 2-7 1-8 0-16-4-23 2 1 2 1 4 0z" class="N"></path><path d="M849 323c1 0 2 0 3 1h0c-4 2-8 6-9 10-1 5 0 9 3 14-2-1-3-3-3-4l-1-1-1-2c-2-4-1-7 0-10 2-4 5-6 8-8z" class="y"></path><path d="M849 323c1 0 2 0 3 1h0c-2 0-3 1-4 2-2 1-4 3-7 5 2-4 5-6 8-8z" class="P"></path><path d="M255 702h0 1v-1-2c1-3 1-6 1-9v-4-2c0-2 1-4 2-6 0-2 1-4 2-6l1-1c1-1 1-2 2-2h0c0 1-1 2-1 3-1 1-2 3-3 5v2c-1 1-1 2-1 3s0 2-1 2v1 11c-1 2 0 4-1 6-2 5-4 8-7 11h-1c0-1 0 0 1-1 2-3 3-7 4-10h1z" class="E"></path><path d="M722 689l6 6v1h-1c-2 0-4 1-5 2-1 2-2 3-2 4v1c-1 3-1 7-1 11l-1-4c0-2-1-6 0-8v-1l-1-1c1-1 0-3 1-5l2-2h2c1 1 2 1 3 1-1-2-2-3-2-4l-1-1z" class="C"></path><path d="M228 432h0v-4l1 1v1 2l1-1c2 11 0 21-4 31-1 2-2 5-4 7 0 1 0 1-1 1 6-12 7-24 7-38z" class="K"></path><path d="M808 389l4-6c2-4 5-7 8-10h1c1-1 2-2 4-2-7 6-17 14-18 24 0 3 0 5 2 7 4 4 13 4 18 4h8 1c-3 1-8 1-12 2h-6c0-1-1-1-1-1h-2c-1-2-3-2-5-3l-2-2c-1-1-2-3-2-4v-1c0-3 1-5 2-8z" class="y"></path><path d="M224 610v1c0 2-2 4-3 6l-8 7h1c2-1 3-3 4-4 3-2 7-4 10-5h1c-2 2-5 3-7 5l-2 2h0c-1 1-6 3-6 4-6 3-10 7-15 11h0c7-10 18-16 24-26l1-1z" class="C"></path><path d="M307 674c1 0 1 0 1 1 2 3 2 7 3 10v1 3c-1 1-2 1-3 2h-5l-1 2c-1 0-1 0-1 1h-3l4-3c5-5 5-11 5-17z" class="K"></path><path d="M306 687c0-1 1-2 1-2 2 0 3 0 4 1v3c-1 1-2 1-3 2h-5c1-2 2-3 3-4z" class="T"></path><path d="M306 687c1 1 2 1 3 2l-1 1v1h-5c1-2 2-3 3-4zm384-538c5 2 10 3 16 3-1 0-2 1-3 2 0 0-1 0-1 1h0c0 1 0 1-1 1 0 1 0 1-1 2v1 3l1 1h1c-3 1-9-1-11-3h0c1 0 2 0 3 1h3 0c0-2-1-4-2-5v-1h3c-2-1-2-3-4-4-2 0-2 0-3-1l-1-1z" class="B"></path><path d="M172 326l1-1c0 1 1 1 2 1h1c3 3 6 6 7 10 0 3-1 7-2 10-1 1-2 1-3 2l-3 4c-2 1-3 1-5 2 3-2 5-4 7-6 2-3 3-6 3-10-1-6-4-9-8-12z" class="P"></path><path d="M304 663l3 3c2 1 3 5 3 7h1c1 2 1 4 1 6 1 7 1 15-1 21-1-2 0-5 0-7v-1-3-3-1c-1-3-1-7-3-10 0-1 0-1-1-1l-1-5c0-2-1-4-2-6z" class="y"></path><path d="M304 663l3 3c2 1 3 5 3 7h1c1 2 1 4 1 6v2h-1l-1-7c-1-2-2-3-4-5 0-2-1-4-2-6z" class="AP"></path><path d="M673 77c2-2 4-3 5-4-6 7-9 14-10 23-1-1-1-1-1-2v-4l2-5-6 7h0l-1 1-2-1c0-3 2-3 4-5l-1-1c2-4 7-7 10-9z" class="L"></path><path d="M664 87l1-2 2 1c-2 1-4 4-4 6l-1 1-2-1c0-3 2-3 4-5z" class="AT"></path><path d="M663 86c2-4 7-7 10-9-1 3-4 6-6 9h0l-2-1-1 2-1-1z" class="G"></path><path d="M311 740c3 0 6 1 9 4v2 2c-1-1-3-3-5-4h0c-7-3-14 1-20 3-3 2-6 4-9 5l1-1h0c0-2 0-2 1-3l2-2 1 2c3-3 6-4 10-6l7-2h1 2z" class="y"></path><path d="M288 748l2-2 1 2-4 3h0c0-2 0-2 1-3z" class="T"></path><path d="M311 740c3 0 6 1 9 4v2l-3-3c-2-1-2-1-4-1h0l1-1c-2 0-3 0-5-1h2z" class="t"></path><path d="M786 546h5l1 1c-6 6-9 12-9 21 1 10 7 18 14 25l1 1c-1 0-1 0-1-1-9-5-13-16-15-24-1-3-1-6-1-9h0c0-2 1-4 2-7l1-1c0-1 0-2 1-2l1-1v-1-2z" class="o"></path><path d="M455 782c0 1 1 3 1 3l5 13c-4 2-6 6-9 10l-6 14h0c0-1 0-2 1-3l4-14 1-2 2-2v-1c1-2 1-3 1-5 1-2 1-3 1-4l-1-4v-5z" class="AQ"></path><path d="M455 782c0 1 1 3 1 3 0 4 1 8 0 12 0 3-4 8-4 11l-6 14h0c0-1 0-2 1-3l4-14 1-2 2-2v-1c1-2 1-3 1-5 1-2 1-3 1-4l-1-4v-5z" class="L"></path><path d="M718 736l6 5 8 6v1h-1c-6-2-13-6-20-4-3 1-4 3-6 5 0-2 0-3 1-4 2-3 4-3 7-5 2-1 4 0 6-2l-1-1v-1z" class="O"></path><path d="M718 736l6 5h-1c-1 0-2 0-4-1h-1l-1 1h-1c-4 1-6 3-10 4 2-3 4-3 7-5 2-1 4 0 6-2l-1-1v-1z" class="L"></path><path d="M828 365h1 4 0l-3 2c-1 0-1 1-2 2s-2 1-3 2h0c-2 0-3 1-4 2h-1c-3 3-6 6-8 10l-4 6v-2c1-1 1-3 3-4l1-1c0-2 2-3 2-4h-1-2c-1 0-3 1-4 1 2-2 5-6 8-7 2-1 4-2 6-4l7-3z" class="B"></path><path d="M806 538h4v1c-7 1-13 3-18 8l-1-1h-5-10l-2-1c0-1 0-1 1-2h3l1-2h1 0c2 0 3 0 4 1 2 1 5 2 7 1v-1c2 0 3-1 5-2h0l3-1 3-1c2 1 3 0 4 0z" class="L"></path><path d="M776 546l-2-1c0-1 0-1 1-2h3l1-2h1 0c3 2 7 3 10 5h1-5-10z" class="AT"></path><path d="M851 358h0c3-1 5-1 8 0s3 1 6 0c3 1 10 3 12 5-15-4-27-4-42 2-2 1-4 3-7 4 1-1 1-2 2-2l3-2h0-4-1c1-1 3-2 4-2l19-5h0z" class="K"></path><path d="M375 731h2c2 0 7 4 8 5l3 3 2-1 6 9c1 2 1 3 2 4-1-1-2-2-4-3h-1l-2-2c-1 0-1-1-2-1h-1c-3-2-6-2-9-3h-1l1-2h1l-1-1 1-1c1 0 1 0 1-1s1-1 1-1c-2-2-4-3-7-5z" class="b"></path><path d="M381 737c0-1 1-1 1-1 3 2 6 4 8 6l-1 1-1-1-1-1c-2-2-4-2-7-3 1 0 1 0 1-1z" class="K"></path><path d="M379 740h1l-1-1 1-1c3 1 5 1 7 3l1 1 1 1 1-1c2 2 3 4 4 6h-1l-2-2c-1 0-1-1-2-1h-1c-3-2-6-2-9-3h-1l1-2z" class="AQ"></path><path d="M379 740h1l-1-1 1-1c3 1 5 1 7 3l1 1c-2 1-4 0-6-1-1 0-2 0-3-1z" class="j"></path><path d="M456 105c2 2 3 5 6 6v1c1 1 2 1 3 1l1 1c5 2 11 0 16-1h4c-1 1-1 2-2 3l-1-1c-2-1-5 0-8 1l-21 3v-1h1c0-4-1-7-1-10v-1l2-2z" class="G"></path><path d="M458 113c1 1 2 1 3 2h2c1 0 1 1 2 1h1c-2 1-4 1-6 1-2-1-2-3-2-4z" class="E"></path><path d="M456 105c2 2 3 5 6 6v1c1 1 2 1 3 1l1 1 1 2h-1-1c-1 0-1-1-2-1h-2c-1-1-2-1-3-2-2-1-3-4-4-6l2-2z" class="H"></path><path d="M606 769l2 2c-2 1-3 2-5 3s-5 3-6 5c-7 3-11 10-14 17-1 2-2 4-2 6-1-5 3-11 5-15v-2-1c-1 1-2 2-3 2h0l-1-1 10-8 3-2 3-3c1-1 2-1 4-1 1 0 3-2 4-2z" class="Ai"></path><path d="M598 772c1-1 2-1 4-1-4 3-7 5-10 8s-4 5-6 8v-2-1c-1 1-2 2-3 2h0l-1-1 10-8 3-2 3-3z" class="N"></path><path d="M785 200c4-1 8-1 12-2 6-2 12-6 18-6-5 2-9 5-13 8-4 2-6 5-10 6h-1c-1 1-4 4-6 4l-7-1h0l-1-1 9-3 1-1s1-1 2-1h0l1-1h1c1-1 2-1 4-2-3-1-6 1-9 0h-1z" class="U"></path><path d="M787 204c2 0 6-3 8-2-1 1-1 2-2 3l-2 1c-1 1-4 4-6 4l-7-1h0l-1-1 9-3 1-1z" class="AT"></path><path d="M438 782l1 1h1l3 2c2 5 4 10 7 14 0 2 0 4 1 6l-4 14c-1-4-1-10-1-15 0-7-5-15-9-20l1-2z" class="E"></path><path d="M438 782l1 1c4 5 6 10 7 17h0v4h0c0-7-5-15-9-20l1-2z" class="b"></path><path d="M166 356v1h0l13-1 15 2h0l3 1h-2l1 1c-1 0-3 0-4 1l-1-1h-7l-1-1h-3l-1 1 1 1h0c-10-1-18 0-27 2-3 0-5 1-7 1 1-1 4-2 6-3l14-5z" class="C"></path><path d="M104 219c0-1 3-3 3-4 4-4 7-8 10-12 1-2 2-3 3-5l1 1-1 2 1 2 2 7h-1l-1 2h-2c-3 3-7 6-10 6l-2 2h-1l-4 4c0-1 1-2 2-3v-2z" class="AD"></path><path d="M109 218l11-12v-6 1l1 2 2 7h-1l-1 2h-2c-3 3-7 6-10 6z" class="AY"></path><path d="M119 212h0c0-1 1-3 1-4 1-2 0-3 1-5l2 7h-1l-1 2h-2z" class="U"></path><path d="M741 169c2-1 4-1 7-1-2 2-5 3-7 6l-1 1-2 3c-1 3-1 7 0 9 3 6 6 7 11 10-3 0-6-3-9-4 0 0-2-2-3-2l-3-3c-2-3-5-7-4-10h0c2 0 6-4 7-5s2-1 2-2h0l2-2z" class="C"></path><path d="M741 169c2-1 4-1 7-1-2 2-5 3-7 6l-1 1h-1c-1 1-1 2-2 4v1c-1 1-1 2-1 3s0 1-1 2l-2-6 4-6c1-1 2-1 2-2h0l2-2z" class="R"></path><defs><linearGradient id="C" x1="720.632" y1="655.171" x2="739.843" y2="663.388" xlink:href="#B"><stop offset="0" stop-color="#817977"></stop><stop offset="1" stop-color="#a9a097"></stop></linearGradient></defs><path fill="url(#C)" d="M734 646l4 2 1 1 6 3-1 2c-5-2-9-3-14-1-3 1-6 5-8 8l-2 4-2 2h-3l2-4-1-1v-2c1-1 2-1 2-2h1 1l4-4-2-1 1-1c1-2 2-3 4-3 1-1 3-1 4-1v-1c1 0 2 0 3-1z"></path><path d="M716 660c1-1 2-1 2-2h1 1l-3 5-1-1v-2zm18-14l4 2 1 1h-6l-2-2c1 0 2 0 3-1z" class="R"></path><path d="M731 647l2 2c-4 1-7 2-9 5l-2-1 1-1c1-2 2-3 4-3 1-1 3-1 4-1v-1z" class="F"></path><path d="M410 790c0-4 2-9 2-13 1-5 0-10 1-14v4l1 1h1l1 1h0v1 1c0 1 1 1 1 2l1 1h1l2 2c0 1-2 2-2 4h-1v1c-1 1-1 2-2 3h0l-1 2c-3 4-4 7-6 12-1 2-2 5-4 7l2-7v-8h2 1z" class="P"></path><path d="M414 774v-1h1c0 2 0 3-1 5v-1c-1-1 0-2 0-3z" class="C"></path><path d="M410 790c0 2 0 3-1 4 0 2-1 4-2 4v-8h2 1z" class="U"></path><path d="M416 769v1 1c0 1 1 1 1 2l1 1h1l2 2c0 1-2 2-2 4h-1v1c-1 1-1 2-2 3h0l-1 2h-1c1-1 1-2 1-3h0c1-3 4-4 5-7-2-1-3-2-4-4l-1 1h-1v1h-1v-3h2l1-2z" class="N"></path><path d="M715 673l2 1c1 1 1 3 1 5 1 1 1 2 1 3l2 5h0c1 1 1 1 1 2l1 1c0 1 1 2 2 4-1 0-2 0-3-1h-2l-2 2c-1 2 0 4-1 5l1 1v1c-1 2 0 6 0 8-2-2-3-4-4-8-1-9 0-19 1-29z" class="R"></path><path d="M719 682l2 5h0c1 1 1 1 1 2l1 1c0 1 1 2 2 4-1 0-2 0-3-1s-3-2-4-2h-1c0-1-1-1-1-3l1-1-1-1 1-1 1 3h1c-1-2-1-4 0-6zm123-379c-1-1-1-2-1-3v-6-1c2-3 3-7 6-9 1 0 1 0 2-1l-3-1h-2c-3-1-6-2-7-4l1-1c5 2 10 4 16 4 1 1 2 1 4 2-2 0-4 0-5 1h-2c1 1 2 1 2 2-4 2-6 5-7 9l-1 2h0c0 4 1 8 3 11l4 4h0c-4-1-9-7-10-9z" class="B"></path><path d="M842 303l4 4v-1c-1-1-1-3-2-4v-1c0-3-1-7 1-11 1-3 3-5 6-6 1 1 2 1 2 2-4 2-6 5-7 9l-1 2h0c0 4 1 8 3 11l4 4h0c-4-1-9-7-10-9z" class="AX"></path><defs><linearGradient id="D" x1="674.275" y1="717.237" x2="688.415" y2="751.909" xlink:href="#B"><stop offset="0" stop-color="#86817f"></stop><stop offset="1" stop-color="#aaa3a0"></stop></linearGradient></defs><path fill="url(#D)" d="M670 720l1-1 3 1 5 1c7 4 13 10 17 16l1 2c1 1 1 2 1 4 1 1 1 3 1 5v4h-1l-2-9c-3-7-8-14-15-17-5-2-10-1-16 1v-1h-2l1-2 1-1-2-1 5-1v-1h2z"></path><path d="M668 721l5 1h-1c-2 0-5 1-7 1l-2-1 5-1z" class="b"></path><path d="M807 379c1 0 3-1 4-1h2 1c0 1-2 2-2 4l-1 1c-2 1-2 3-3 4v2c-1 3-2 5-2 8v1c0 1 1 3 2 4-2 1-4 0-5-1-3-1-5-5-6-7v-1c0-2 1-4 3-5 1-2 2-3 3-5 2-1 3-3 4-4z" class="E"></path><path d="M803 486h1c2 0 6 0 8 1-10 2-19 5-25 14-2 2-3 5-4 8l-1-1c1-1 1-1 1-2 1-1 1-2 1-3-3 2-3 6-5 8-1 2-1 3-2 5l-1 4c1-11 6-19 13-27l-1-2-2 1-1-2-1-1h0v-2h1 5 7l6-1z" class="L"></path><path d="M784 487h1 5 7c-2 2-6 4-9 4l-2 1-1-2-1-1h0v-2z" class="N"></path><path d="M784 487h1c1 2 1 2 0 3l-1-1h0v-2z" class="o"></path><path d="M834 283l1-1c-1-2 0-4 0-6-1-1-1-1 0-2l1 1 2 2h0l-1 1c1 2 4 3 7 4h2l3 1c-1 1-1 1-2 1-3 2-4 6-6 9v1 6c0 1 0 2 1 3-2-1-4-4-5-6l-2-3c0-1-1-2-1-3l-1-1c1-1 1-2 0-3 0-2 1-2 1-4h0z" class="D"></path><path d="M834 283l1-1c-1-2 0-4 0-6-1-1-1-1 0-2l1 1h-1v1c1 1 1 3 1 4 1 1 3 2 3 3h-3v1c1 1 1 1 3 1l-1 1h-2v5l1 1c1 1 1 2 1 4l-1 1-2-3c0-1-1-2-1-3l-1-1c1-1 1-2 0-3 0-2 1-2 1-4h0z" class="l"></path><path d="M834 283c1 1 1 2 2 4h-1c0 2 1 5 0 7 0-1-1-2-1-3l-1-1c1-1 1-2 0-3 0-2 1-2 1-4zm-42 329l15 9c1 2 2 2 2 4-5-2-9-4-13-5h-1c-7-2-17 0-23 4-5 3-9 8-10 13v2h0c-2 12 5 22 9 33 1 4 2 9 1 14v-1h-1c0-3 0-7-1-10-1-9-6-16-9-24-1-4-1-8-1-12h0c1-5 2-9 5-12 5-6 12-8 19-9h11 0 3-2v-1h2c-2-2-4-3-6-5z" class="N"></path><path d="M798 617c3 1 6 3 8 5-3-1-7-2-10-3-1 0 0 0-1-1h0 3-2v-1h2z" class="G"></path><path d="M308 691c1-1 2-1 3-2v3 1c0 2-1 5 0 7 0 12-7 24-13 34v-1l3-6c4-8 6-17 3-26-2-3-3-4-6-5-1 0-1 0-1-1l1-1h3c0-1 0-1 1-1l1-2h5z" class="N"></path><path d="M304 700c-1-2-2-3-2-5 2 0 6-1 7 0l-2 13c-1-3-1-6-3-8z" class="B"></path><path d="M308 691c1-1 2-1 3-2v3 1l-2 2c-1-1-5 0-7 0 0 2 1 3 2 5v1c-2-3-3-4-6-5-1 0-1 0-1-1l1-1h3c0-1 0-1 1-1l1-2h5z" class="D"></path><path d="M308 691c1-1 2-1 3-2v3c-2 0-6 1-7 2h-3c0-1 0-1 1-1l1-2h5z" class="l"></path><path d="M222 620c9-3 19-4 28 0 6 2 11 6 13 12 7 16-5 31-8 46 0 2-1 4-1 6v3c-1-3 0-7 1-11 2-9 7-17 8-26 0-5 0-9-1-14-2-6-6-10-11-13-10-5-21-4-31-1h0l2-2z" class="N"></path><defs><linearGradient id="E" x1="598.918" y1="784.819" x2="612.461" y2="782.852" xlink:href="#B"><stop offset="0" stop-color="#6f6b6c"></stop><stop offset="1" stop-color="#908e8e"></stop></linearGradient></defs><path fill="url(#E)" d="M608 768h1l1 2c1 0 2 1 3 1h2l-1 8c-1 2-1 5 0 8l1 4v3 2l-1-2c-5-8-9-13-17-15 1-2 4-4 6-5s3-2 5-3l-2-2 2-1h0z"></path><path d="M606 777l2 1c-1 1-1 1-2 1h-1v-1l1-1z" class="b"></path><path d="M608 768h1l1 2c1 0 2 1 3 1h2l-1 8c-1 2-1 5 0 8-3-3-1-4-2-6-1-1-1-2-1-3l-1-2h-1c0 1 0 1-1 2l-2-1-1 1v1h0c-1-1-2-1-2-2l1-1c2-2 3-3 4-5h0l-2-2 2-1h0z" class="o"></path><path d="M608 768h1l-1 3h0l-2-2 2-1h0z" class="b"></path><path d="M604 776c1-1 2-1 3-1h-1l1-1 2 2c-1 0-2 0-3 1h0l-1 1v1h0c-1-1-2-1-2-2l1-1z" class="AK"></path><path d="M194 269h3l1 1c0 1 1 2 1 3-1 1-2 0-2 2h1c1 1 1 2 1 4h1 0c-1 0-1-1-2-1h-1v1 4h1c0 1 0 1-1 2l-1 3c-1-1-1-1-1-2v-1-1c0-1-1-2-2-2-1-1-1 0-2-1-2 0-3 1-5 2l-5 1h-12 1-2l-1-1c1 0 2-1 4-1 2-1 4-2 6-2s4-1 6-2l2-1 4-3h0l3-3 1-1 1-1z" class="D"></path><path d="M189 274c2 0 3-1 4 0l2-1 1 1c-1 2-1 2-1 4h1 1v1 4h1c0 1 0 1-1 2l-1 3c-1-1-1-1-1-2v-1l1-1c0-2 0-3-1-4s-1-1-2 0h-1l-1-1c1-1 1-2 2-3-1-1-2-2-4-2h0z" class="C"></path><path d="M194 269h3l1 1c0 1 1 2 1 3-1 1-2 0-2 2h1c1 1 1 2 1 4h1 0c-1 0-1-1-2-1h-1-1-1c0-2 0-2 1-4l-1-1-2 1c-1-1-2 0-4 0l3-3 1-1 1-1z" class="j"></path><path d="M192 271h2v1h1 1l1 1-1 1h0l-1-1-2 1c-1-1-2 0-4 0l3-3z" class="l"></path><path d="M185 277h0 5l2-1c0 1-1 2-2 3s-2 1-3 1c-2 1-3 1-5 2l-13 1v1h1-2l-1-1c1 0 2-1 4-1 2-1 4-2 6-2s4-1 6-2l2-1z" class="G"></path><path d="M573 99l1-2c1 2 0 5 0 9h0c1 3 2 5 4 7 0 2 0 3 1 4v1c1 1 3 3 4 5 2 1 2 2 4 3l-1 1c-1-1-4-2-6-3-1-1-1-2-2-4l-1 2-1-1h-3v1l-7-2 2-1-3-1c-1 0-1 0-2-2 2-2 4-4 5-7l1-1c0-1 1-3 2-5h0c0-1 1-3 2-4z" class="H"></path><path d="M570 110c2 2 0 8 3 11v1l-7-2 2-1c1-2 1-6 2-9z" class="S"></path><path d="M568 109l2 1c-1 3-1 7-2 9l-3-1c-1 0-1 0-2-2 2-2 4-4 5-7z" class="AT"></path><path d="M573 99l1-2c1 2 0 5 0 9h0c1 3 2 5 4 7 0 2 0 3 1 4v1c1 1 3 3 4 5 2 1 2 2 4 3l-1 1c-1-1-4-2-6-3-1-1-1-2-2-4-4-7-5-13-5-21z" class="N"></path><path d="M415 768c2-1 2-2 5-2 4 0 10 5 14 7l1 1c2 4 4 7 7 9l1 2-3-2h-1l-1-1-1 2c-3-3-6-4-10-5-2 0-3 0-5 1l-6 4h0c1-1 1-2 2-3v-1h1c0-2 2-3 2-4l-2-2h-1l-1-1c0-1-1-1-1-2v-1-1h0l-1-1z" class="U"></path><path d="M427 779v-1h-1l1-2c0-1 0-1-1-2l-1-1c1 0 2 0 3 1 4 2 8 5 10 8l-1 2c-3-3-6-4-10-5z" class="O"></path><path d="M416 769h0c4 1 9 2 12 5-1-1-2-1-3-1l1 1c1 1 1 1 1 2l-1 2h1v1c-2 0-3 0-5 1l-6 4h0c1-1 1-2 2-3v-1h1c0-2 2-3 2-4l-2-2h-1l-1-1c0-1-1-1-1-2v-1-1z" class="t"></path><path d="M416 769h0c4 1 9 2 12 5-1-1-2-1-3-1l1 1c1 1 1 1 1 2l-1 2h1v1c-2 0-3 0-5 1 1-2 2-2 3-4l-2-2c-2-1-4-3-7-4v-1z" class="b"></path><path d="M253 210h-1c0-1 0-1-1-1l-10-6c-7-4-15-7-22-10h0c4 1 7 2 10 3 8 2 17 4 25 4 10 1 21 0 29-8 3-3 5-7 5-11 0-6-4-10-8-14l5 2h1c12 4 21 2 32-3-3 3-8 5-11 5l-1 1c-8 2-16 0-24-4 4 5 9 9 9 15-1 5-4 9-7 12-10 8-26 8-38 6h-4l-1 1c4 2 8 5 12 6l2 2h-2z" class="K"></path><path d="M373 731h2c3 2 5 3 7 5 0 0-1 0-1 1s0 1-1 1l-1 1 1 1h-1l-1 2h1c0 1-18 20-20 22 3-4 5-10 6-15 1-2 1-4 2-7h1c0-3 0-5-1-8l-1-1c0-1 1-1 2-2h5z" class="D"></path><path d="M369 734h0l1 1h0c2 1 6 1 7 3h0l-1 1c-2 1-3 2-4 2-2-1-2-2-3-3-1-2-1-3 0-4z" class="G"></path><path d="M373 731h2c3 2 5 3 7 5 0 0-1 0-1 1-3-1-6-4-9-4h-3l-1-1-1 2-1-1c0-1 1-1 2-2h5z" class="l"></path><defs><linearGradient id="F" x1="351.729" y1="720.698" x2="352.114" y2="729.992" xlink:href="#B"><stop offset="0" stop-color="#787474"></stop><stop offset="1" stop-color="#b0a8a4"></stop></linearGradient></defs><path fill="url(#F)" d="M333 732l2-3c9-8 18-9 30-9h-1l6 1h0l-1 2h2l4 2 1 2 4 2c4 2 7 6 10 9l-2 1-3-3c-1-1-6-5-8-5h-2-2c-3-2-5-2-8-2l-1 1h-1c-5-4-9-6-15-5s-10 4-14 8h-1v-1z"></path><path d="M364 720l6 1h0l-1 2h2l-2 2c-3-1-6-1-10-2-1-1-4 1-6 0 3 0 8 0 10-1 0-1 0-1 1-2z" class="b"></path><path d="M364 720l6 1h0l-1 2c-2-1-4-1-6-1 0-1 0-1 1-2z" class="AU"></path><path d="M359 723c4 1 7 1 10 2l2-2 4 2 1 2 4 2c4 2 7 6 10 9l-2 1-3-3c-1-1-6-5-8-5h-2-2c-3-2-5-2-8-2l-1-1h2l-1-1c-1 0-3-1-5-2-1-1-1-1-2-1h-1l2-1z" class="y"></path><path d="M365 727c1-1 2-1 3-1l1 1v1l-4-1z" class="j"></path><path d="M369 725l2-2 4 2 1 2h-1c-2-1-4-1-6-2z" class="O"></path><path d="M375 727h1l4 2c4 2 7 6 10 9l-2 1-3-3c-1-1-6-5-8-5h-2-2c-3-2-5-2-8-2l-1-1h2l-1-1h0l4 1c2 1 5 2 8 2v-1l-2-2z" class="AQ"></path><path d="M311 670v-3l1 1v3c0 1 1 1 1 2 2 2 2 5 3 8v11c-1 11-4 23-7 34-3 2-5 5-6 9 1-1 2-2 4-3l-10 9c-2 2-4 4-7 5l-2 2v-1l10-14v1c6-10 13-22 13-34 2-6 2-14 1-21 0-2 0-4-1-6v-3z" class="F"></path><path d="M303 735c1-1 2-2 4-3l-10 9c-2 2-4 4-7 5l-2 2v-1l10-14v1h0l-7 11 12-10z" class="K"></path><path d="M663 726h2v1c-3 2-6 5-7 9-1 3 0 7 1 10 1 2 2 5 3 7l-4-2-3-3-1 1c1 0 1 1 1 2l-5-5v-1c-1 0-2-1-3-2h-1-4c-1 0-2 1-3 1h-1-1c0-2 0-2 1-3v-1h1c7-7 15-10 24-14z" class="N"></path><path d="M648 741l-1-2c1-2 3-4 5-5 1 0 1 0 2 1 0 2 0 4 1 6h0l1 3c-1 1-2 1-3 1l-3-3-2-1z" class="C"></path><path d="M663 726h2v1c-3 2-6 5-7 9-1 3 0 7 1 10h0l-1-1v-1-2c-1-4 0-7 0-11l-1-1c-5 2-8 4-12 7h-1l-2 2c-1 2-2 2-4 2v-1h1c7-7 15-10 24-14z" class="b"></path><path d="M639 740h0c2-2 3-2 5-3l-2 2c-1 2-2 2-4 2v-1h1z" class="o"></path><path d="M638 741c2 0 3 0 4-2 1 1 2 1 3 1h2l1 1 2 1 3 3c1 0 2 0 3-1l-1-3c1 2 1 3 3 4l1 1h0c1 2 2 5 3 7l-4-2-3-3-1 1c1 0 1 1 1 2l-5-5v-1c-1 0-2-1-3-2h-1-4c-1 0-2 1-3 1h-1-1c0-2 0-2 1-3z" class="t"></path><path d="M655 741c1 2 1 3 3 4l1 1h0c1 2 2 5 3 7l-4-2v-1c-1-2-3-3-5-5 1 0 2 0 3-1l-1-3z" class="P"></path><path d="M583 786h0c1 0 2-1 3-2v1 2c-2 4-6 10-5 15 0 6-2 12-1 19v3s-2-5-2-6l-3-5c-3-8-6-11-14-14l2-7 1 1h1 2c1 0 1-1 2-1l1-1h0 3l2 2 3 1h0c2-2 3-5 5-8z" class="R"></path><path d="M570 791h3l2 2 3 1h0l-1 4c2 2 1 6 0 9-1-3-1-7-3-9v1l-1-1h0c-1-1-2-5-3-7z" class="K"></path><path d="M575 793l3 1h0l-1 4-1-1-1-4z" class="j"></path><defs><linearGradient id="G" x1="562.242" y1="801.437" x2="574.408" y2="800.581" xlink:href="#B"><stop offset="0" stop-color="#707072"></stop><stop offset="1" stop-color="#999594"></stop></linearGradient></defs><path fill="url(#G)" d="M570 791h0c1 2 2 6 3 7 1 5 3 11 4 16 1 1 1 3 1 4l-3-5c-3-8-6-11-14-14l2-7 1 1h1 2c1 0 1-1 2-1l1-1z"></path><path d="M630 741v-1c2-5 7-8 11-11 2-1 3-3 5-3 4-2 7-3 11-4l1-1c1 0 2 0 3 1h2l2 1-1 1-1 2c-9 4-17 7-24 14h-1v1c-1 1-1 1-1 3h1 1c1 0 2-1 3-1h4 1c1 1 2 2 3 2v1l-2-1c-4-2-8-1-11 1-2 0-3 1-5 2-1 0 0 0-1 1-2 0-2 0-3-1l-2 1c0-3 2-6 4-8z" class="L"></path><path d="M653 726l6-1c-3 2-6 3-10 4 1-2 2-2 4-3z" class="AK"></path><path d="M661 722h2l2 1-1 1c-1 0-3 1-5 1l-6 1v-1l8-3z" class="O"></path><defs><linearGradient id="H" x1="629.284" y1="742.379" x2="633.811" y2="744.961" xlink:href="#B"><stop offset="0" stop-color="#777271"></stop><stop offset="1" stop-color="#888889"></stop></linearGradient></defs><path fill="url(#H)" d="M635 737l1 1 3-2c-1 2-3 3-4 5h1l1 1-1 2c-2 0-3 0-4 2-1 1-1 1-1 3-2 0-2 0-3-1l-2 1c0-3 2-6 4-8 0-1 2-1 2-2l1-1h1c0-1 0-1 1-1z"></path><path d="M630 741v-1c2-5 7-8 11-11 2-1 3-3 5-3 4-2 7-3 11-4l1-1c1 0 2 0 3 1l-8 3v1c-2 1-3 1-4 3-3 3-7 4-10 7l-3 2-1-1c-1 0-1 0-1 1h-1l-1 1c0 1-2 1-2 2z" class="AU"></path><path d="M635 737l6-6c4 0 7-5 12-6v1c-2 1-3 1-4 3-3 3-7 4-10 7l-3 2-1-1z" class="b"></path><defs><linearGradient id="I" x1="577.157" y1="777.112" x2="580.724" y2="786.72" xlink:href="#B"><stop offset="0" stop-color="#626060"></stop><stop offset="1" stop-color="#7a797c"></stop></linearGradient></defs><path fill="url(#I)" d="M598 772l-3 3-3 2-10 8 1 1c-2 3-3 6-5 8h0l-3-1-2-2h-3 0l-1 1c-1 0-1 1-2 1h-2-1l-1-1 4-9c1 0 2-1 3-2l7-4c4-2 8-3 12-4h1c3 0 5-1 8-1z"></path><path d="M568 788l-1-1v-1c3-1 8-4 11-4h0l1-1c4-4 10-5 16-6l-3 2c-3 1-6 2-8 3s-5 2-6 3c-1 0-2 2-3 2-1 2-4 2-6 3h-1z" class="b"></path><path d="M569 788c2-1 5-1 6-3 1 0 2-2 3-2 1-1 4-2 6-3s5-2 8-3l-10 8 1 1c-2 3-3 6-5 8h0l-3-1-2-2h-3 0c0-1-1-2-2-3h1z" class="o"></path><path d="M569 788l2 1c1-1 1-1 2-1 2 0 1 1 2 2 0 1 1 1 1 2 1-1 1-2 2-3l4-4 1 1c-2 3-3 6-5 8h0l-3-1-2-2h-3 0c0-1-1-2-2-3h1z" class="L"></path><defs><linearGradient id="J" x1="832.845" y1="258.141" x2="835.099" y2="268.624" xlink:href="#B"><stop offset="0" stop-color="#7c7472"></stop><stop offset="1" stop-color="#96908d"></stop></linearGradient></defs><path fill="url(#J)" d="M830 256c1-1 4-2 5-3v2c2 8 4 13 9 19 4 3 7 5 11 7h-1c-6 0-11-2-16-4h0l-2-2-1-1c-1 1-1 1 0 2 0 2-1 4 0 6l-1 1h0c0 2-1 2-1 4 1 1 1 2 0 3l-1-4v-1l-2-3v-3l-1-3v-3-4h-1 0l-2 4v-3c0-1-1-3-1-3 1-2 1-3 2-4 0-1 0-2 1-2v-1-1h-1l3-3z"></path><path d="M831 264c0 4 0 8 1 12-1 3 0 6 0 9l-2-3v-3l-1-3v-3-4h0 1l1-5z" class="O"></path><path d="M832 276l1-1c0-1-1-2 0-4v-3h2l1 1h2c1 1 3 2 4 4l-5-2h-3c-1 4-1 8 0 12h0c0 2-1 2-1 4 1 1 1 2 0 3l-1-4v-1c0-3-1-6 0-9z" class="L"></path><defs><linearGradient id="K" x1="827.177" y1="261.31" x2="830.323" y2="265.69" xlink:href="#B"><stop offset="0" stop-color="#85827f"></stop><stop offset="1" stop-color="#9e9897"></stop></linearGradient></defs><path fill="url(#K)" d="M830 256c1-1 4-2 5-3v2c-2 2-3 3-3 6-1 1-1 2-1 3l-1 5h-1 0-1 0l-2 4v-3c0-1-1-3-1-3 1-2 1-3 2-4 0-1 0-2 1-2v-1-1h-1l3-3z"></path><path d="M830 256c1-1 4-2 5-3v2c-2 2-3 3-3 6-1 1-1 2-1 3l-1 5h-1c1-3 1-6 1-8 1-2 1-3 0-5z" class="Ai"></path><path d="M834 283c-1-4-1-8 0-12h3l5 2c0 1 1 1 2 1h0c4 3 7 5 11 7h-1c-6 0-11-2-16-4h0l-2-2-1-1c-1 1-1 1 0 2 0 2-1 4 0 6l-1 1z" class="N"></path><path d="M673 130c0 1 1 2 2 3h0c1 1 1 2 1 3l2 2h0l2 3c1 1 2 2 4 3l6 5 1 1c1 1 1 1 3 1 2 1 2 3 4 4h-3v1c1 1 2 3 2 5h0-3c-1-1-2-1-3-1h0c-5-2-9-4-13-8-2-2-3-4-6-6h0c-4-3-8-4-13-6l1-1h0l-3-6v-1l4 5c2 2 6 3 8 5l3 3 1-2h0v-1l1-1-3-5c1-2 1-4 2-6z" class="H"></path><path d="M657 132l4 5c2 2 6 3 8 5l3 3c3 2 7 4 8 8 1 0 2 1 2 2h1c2 2 5 3 8 5h0c-5-2-9-4-13-8-2-2-3-4-6-6h0c-4-3-8-4-13-6l1-1h0l-3-6v-1z" class="R"></path><path d="M186 283c2-1 3-2 5-2 1 1 1 0 2 1 1 0 2 1 2 2v1 1c0 1 0 1 1 2l-2 3h0c-6 8-11 14-18 20 0-1 0-3 1-5v-2c1-3 1-6 1-9v-2c-2-4-4-6-8-9h-1 12l5-1z" class="E"></path><path d="M185 301c0-2 1-4 2-6v1h1c0-1 1-2 1-3h1c1-2 1-3 2-4-2 5-4 8-7 12z" class="B"></path><path d="M186 283c2-1 3-2 5-2 1 1 1 0 2 1 1 0 2 1 2 2v1 1c0 1 0 1 1 2l-2 3h0c-6 8-11 14-18 20 0-1 0-3 1-5v-2c1-3 1-6 1-9v-2l1 5c1 1 0 1 0 2l-1 7h0c2-2 5-4 7-6 3-4 5-7 7-12h0l1-1h-1l1-5c-3-1-4-1-7 0z" class="T"></path><path d="M195 286c0 1 0 1 1 2l-2 3-1-2 2-3z" class="D"></path><path d="M786 402c1-3 2-6 4-9h3c-1 2-2 3-3 6l2-2c1-1 1-2 3-3l1 1 1-1c1 2 3 6 6 7 1 1 3 2 5 1l2 2c2 1 4 1 5 3h2s1 0 1 1l-10 2c-2 0-4 0-6 1-3 1-5 3-7 5l-1-1h-1c-1 1-2 1-3 2v-2c-1 1-1 1-2 1 0-1 0-1-1-2h0v-1l3-2v-2-1h-1c-2-1-3-3-3-6z" class="F"></path><path d="M798 408h4v1l-2 1-2-2z" class="AP"></path><path d="M802 408c1 0 3-1 3-1h10 2s1 0 1 1l-10 2c-2 0-4 0-6 1v-2-1z" class="L"></path><defs><linearGradient id="L" x1="789.132" y1="409.999" x2="796.868" y2="412.501" xlink:href="#B"><stop offset="0" stop-color="#877f7d"></stop><stop offset="1" stop-color="#9f948a"></stop></linearGradient></defs><path fill="url(#L)" d="M790 411l8-3 2 2 2-1v2c-3 1-5 3-7 5l-1-1h-1c-1 1-2 1-3 2v-2c-1 1-1 1-2 1 0-1 0-1-1-2h0v-1l3-2z"></path><path d="M802 409v2c-3 1-5 3-7 5l-1-1h-1c-1 1-2 1-3 2v-2c1 0 3-2 4-2 2-2 4-2 6-3l2-1z" class="C"></path><path d="M786 402c1-3 2-6 4-9h3c-1 2-2 3-3 6l2-2c1-1 1-2 3-3l1 1 1-1c1 2 3 6 6 7 1 1 3 2 5 1l2 2h-1c-2 0-3-1-5-2-3-1-5-2-7-4l-1-2c-2 0-2 1-3 3l-1 7v1c0 1-1 2-2 2v-1h-1c-2-1-3-3-3-6z" class="R"></path><path d="M786 402c1-3 2-6 4-9h3c-1 2-2 3-3 6-1 2-1 3-1 6 1 1 1 2 1 3h-1c-2-1-3-3-3-6z" class="AP"></path><path d="M715 667h3l2-2-1 4-2-1c-1 1-1 2-2 4v1c-1 10-2 20-1 29 1 4 2 6 4 8l1 4c1 5 2 8 4 13l2 2 5 9c1 1 2 2 3 4 1 1 2 3 2 4l-5-4c-2-5-6-8-9-12-6-7-10-17-12-25-2-7-1-15-1-21l1-5c0-2 1-4 2-6h2 0l2-6h0z" class="F"></path><path d="M711 673h2c0 1-1 5-1 6l-1-1c0-1 0-1 1-2l-1-1c0 1-1 2-1 4 0 1-1 3-2 5l1-5c0-2 1-4 2-6z" class="E"></path><path d="M714 702c1 4 2 6 4 8l1 4c1 5 2 8 4 13-3-2-6-10-7-14l-3-9 1-2z" class="y"></path><defs><linearGradient id="M" x1="720.278" y1="683.048" x2="707.722" y2="687.452" xlink:href="#B"><stop offset="0" stop-color="#928e88"></stop><stop offset="1" stop-color="#b6a79f"></stop></linearGradient></defs><path fill="url(#M)" d="M715 667h3l2-2-1 4-2-1c-1 1-1 2-2 4v1c-1 10-2 20-1 29l-1 2c-2-9-2-17-1-25 0-1 1-5 1-6h0l2-6h0z"></path><path d="M578 113c2 3 5 5 8 6 5 1 10 1 14-1 6-4 6-11 7-17 1 4 1 7 4 11 2 3 6 5 10 5 9 0 19-9 26-14v1c-2 2-3 4-6 6l-1 1c-4 4-9 8-14 10v-1c-4-2-7-2-10-4-3-1-6-4-8-6l-1-3c0 4 0 8 1 11 1 1 0 1 1 2 0 2-1 4-3 6l-3 3-2 1 5 1v2l-13-4-7-2 1-1c-2-1-2-2-4-3-1-2-3-4-4-5v-1c-1-1-1-2-1-4z" class="T"></path><path d="M579 118l1-1 2 1c4 3 10 4 15 2h0c2-1 5-2 6-4 1-1 1-2 3-3 0 1 0 2 1 3l-1 3c-2 2-3 3-6 4h-1c-4 2-11 0-16 0-1-2-3-4-4-5z" class="H"></path><path d="M607 116v2l2 2c0 2-1 4-3 6l-3 3-2 1 5 1v2l-13-4-7-2 1-1c-2-1-2-2-4-3 5 0 12 2 16 0h1c3-1 4-2 6-4l1-3z" class="E"></path><path d="M607 116v2l2 2c0 2-1 4-3 6l-3 3-2 1 5 1v2l-13-4-7-2 1-1 9 2h2 1c2 0 2-1 4-2h-1l2-2h0c1-2 1-3 2-5l1-3z" class="B"></path><path d="M607 118l2 2c0 2-1 4-3 6l-3 3-2 1 5 1v2l-13-4h2c3 0 4 0 7-1 1 0 3-3 4-3v-1c1-2 1-4 1-6z" class="R"></path><path d="M779 511c2-2 2-6 5-8 0 1 0 2-1 3 0 1 0 1-1 2l1 1c0 3-1 6-1 9v1c1 3 2 5 4 8h1c1 1 2 2 4 3 4 3 9 4 13 5 0 0-1 1-2 1 1 1 2 1 3 2h1c-1 0-2 1-4 0l-3 1-3 1h0c-2 1-3 2-5 2v1c-2 1-5 0-7-1-1-1-2-1-4-1h0c-3-3-5-6-6-10 1-1 1-2 1-3v-4c1-1 1-3 1-4l1-4c1-2 1-3 2-5z" class="K"></path><path d="M783 534c-1-1-2-3-3-4h0c-1-3-2-4-1-6v-1c0-3 0-5 2-7v3 1h0l-1-2c0 2 0 4 1 6v4c1 2 5 4 7 6h-5z" class="G"></path><path d="M779 511c0 3-1 6-1 8-1 2-1 4-1 6-1 3 1 7 2 10h0c1 0 1 0 1-1h1l2 1 1 1c-1 1-1 2-1 3s1 2 1 3c-1-1-2-1-4-1h0c-3-3-5-6-6-10 1-1 1-2 1-3v-4c1-1 1-3 1-4l1-4c1-2 1-3 2-5z" class="j"></path><path d="M779 535c1 0 1 0 1-1h1l2 1 1 1c-1 1-1 2-1 3-2-1-2-2-4-4z" class="R"></path><path d="M788 534c-2-2-6-4-7-6v-4c-1-2-1-4-1-6l1 2h0c1 2 1 3 2 4v1c1 1 2 1 3 2h1c1 1 2 2 4 3 4 3 9 4 13 5 0 0-1 1-2 1h-10l-4-2z" class="E"></path><path d="M783 534h5l4 2h10c1 1 2 1 3 2h1c-1 0-2 1-4 0l-3 1-3 1h0c-2 1-3 2-5 2v1c-2 1-5 0-7-1 0-1-1-2-1-3s0-2 1-3l-1-1v-1z" class="B"></path><path d="M783 539c1 0 4 2 5 2s3-1 4-1h0c2-1 3 0 4 0h0c-2 1-3 2-5 2v1c-2 1-5 0-7-1 0-1-1-2-1-3zm0-5h5l4 2h10c1 1 2 1 3 2h1c-1 0-2 1-4 0l-3 1c-1-1-3-1-4-1-4 0-7 0-11-2l-1-1v-1z" class="C"></path><path d="M795 538l7-1 1 1h-1l-3 1c-1-1-3-1-4-1z" class="U"></path><path d="M609 120c-1-1 0-1-1-2-1-3-1-7-1-11l1 3c2 2 5 5 8 6 3 2 6 2 10 4v1c5-2 10-6 14-10h0c1 0 2 0 3-1 0 1 0 1-1 2l-2 2v1c-1 1-2 3-3 5l-1 1c-1 1-1 0-1 2-1 1-4 3-4 5v6c0 1 1 2 2 2-1 1-2 1-4 1-2-1-3-1-5-1l2 1h-1c-1-1-1-1-2-1l-6-2-5-2h-4l-2-1-5-1 2-1 3-3c2-2 3-4 3-6z" class="H"></path><path d="M631 134c-1-1-2-3-3-5 1-1 2-3 2-4l3-3c1-2 0 0 1-1l1-2 5-5v1c-1 1-2 3-3 5l-1 1c-1 1-1 0-1 2-1 1-4 3-4 5v6z" class="U"></path><path d="M230 496l2-1c1 1 2 1 3 2h0c3 2 5 4 6 7l1 1s1 1 1 2l1 1 3 9v2c1 2 1 3 2 4 0 2 0 6-1 8v2 1l-1 1 1 1-1 1-1 1c-1 2-3 5-6 6-1 1-3 1-4 0-1 0-1 0-2 1l-5-4c-3-2-5-2-8-3l-1-1c0-1 0-1 1-2 2 0 4-1 6-1l2-1c2-1 3-2 4-3l1-1c3-2 4-6 5-9 1-2 1-5 1-7-1-1-1-2-1-3v-1l-1-2-1-1c-1-4-4-7-7-10z" class="B"></path><path d="M229 541h3 1c1 1 2 1 3 2h0c2 0 4 1 5-1l3-2h0c0-1 1-2 2-2-1 2-3 5-6 6-1 1-3 1-4 0-1 0-1 0-2 1l-5-4z" class="R"></path><defs><linearGradient id="N" x1="240.039" y1="496.584" x2="234.442" y2="535.498" xlink:href="#B"><stop offset="0" stop-color="#d8d0cb"></stop><stop offset="1" stop-color="#f9f7f4"></stop></linearGradient></defs><path fill="url(#N)" d="M230 496l2-1c1 1 2 1 3 2h0c3 2 5 4 6 7l1 1s1 1 1 2l1 1h0c-1 3-1 5 0 7 1 4 1 9 1 13l-1 1h0c0 2 0 3-1 4s-2 1-3 1c-5 1-11 2-16 2v-1h1l2-1h0l2-1c2-1 3-2 4-3l1-1c3-2 4-6 5-9 1-2 1-5 1-7-1-1-1-2-1-3v-1l-1-2-1-1c-1-4-4-7-7-10z"></path><path d="M681 715c1 0 2-1 4-1 0 0 1 0 1 1l1-1h2l29 22v1l1 1c-2 2-4 1-6 2-3 0-5 1-7 2-1 2-3 4-4 5h-1l-1 3v2h-1v-4c0-2 0-4-1-5 0-2 0-3-1-4l-1-2c-4-6-10-12-17-16l-5-1 2-1h1l-1-1 1-1h2l1-1h1v-1z" class="y"></path><path d="M698 727l-1-1c-1-1-2-2-4-3 1-1 1-1 1-2l4 5h1l-1 1zm1 2c2 3 1 5 1 8v3l-1 3c0-1 0-2 1-4h0l-1-1 1-1c-2-2-1-6-1-8z" class="l"></path><path d="M700 740l1 4v2 1l-1 3v2h-1v-4-5l1-3z" class="AT"></path><path d="M680 716l8 5 1-1c1 2 1 3 2 4s0 1 1 2 1 1 1 2c1 3 5 8 4 11l-1-2c-4-6-10-12-17-16l-5-1 2-1h1l-1-1 1-1h2l1-1z" class="C"></path><path d="M680 716l8 5 1-1c1 2 1 3 2 4s0 1 1 2 1 1 1 2c-3-3-4-5-7-8-2-1-4-1-5-2h-1l1 1c-1 0-3 0-3 1l1 1-5-1 2-1h1l-1-1 1-1h2l1-1z" class="N"></path><path d="M699 726c3 3 6 4 10 7h0c1 1 1 1 1 2 2 2 5 1 7 2h1l1 1c-2 2-4 1-6 2-3 0-5 1-7 2-1 2-3 4-4 5h-1v-1-2l-1-4v-3c0-3 1-5-1-8l-1-2 1-1z" class="C"></path><g class="B"><path d="M705 740l2-1c2-1 3-2 6-2h2 2 1l1 1c-2 2-4 1-6 2-3 0-5 1-7 2l-1-1v-1z"></path><path d="M700 737c1-2 1-4 2-5 1 1 2 1 3 2v6 1l1 1c-1 2-3 4-4 5h-1v-1-2l-1-4v-3z"></path></g><path d="M702 747c-1-3 2-5 2-8 0-2 0-3 1-5v6 1l1 1c-1 2-3 4-4 5z" class="R"></path><defs><linearGradient id="O" x1="656.537" y1="110.258" x2="681.438" y2="119.417" xlink:href="#B"><stop offset="0" stop-color="#ccc5c1"></stop><stop offset="1" stop-color="#f7f5f0"></stop></linearGradient></defs><path fill="url(#O)" d="M654 116c1-6 5-12 7-17 2-3 3-8 6-10l-1 1h1v4c-2 9 0 20 3 29 1 2 3 5 3 7-1 2-1 4-2 6l3 5-1 1v1h0l-1 2-3-3c-2-2-6-3-8-5l-4-5-2-2v-3c-1-4-1-7-1-11z"></path><path d="M658 123c1 0 2 2 3 3 0 1 0 2 1 3l1 1h-1c-1-1-1-2-2-3l-2-4z" class="U"></path><path d="M664 111c1 1 1 1 1 2v1 6h-1-1c1-2 1-5 1-7h0v-2z" class="G"></path><path d="M663 92h0l6-7-2 5h-1l1-1c-3 2-4 7-6 10-2 5-6 11-7 17 0 4 0 7 1 11v3l2 2v1l3 6h0l-1 1c5 2 9 3 13 6h-4c-2 0-4 0-6-1-3 0-5-2-9-2 1 1 2 1 2 2-5-2-10-6-13-10 0-2-1-3-1-4l1-1c-2-3 0-8 1-11 2-5 5-9 8-14 2-2 3-5 5-7 1-2 3-4 4-6l2 1 1-1z" class="H"></path><path d="M660 92l2 1c-2 2-3 5-5 8 0-1 1-2 1-3l1-2c-1 1-2 1-3 2h0c1-2 3-4 4-6z" class="E"></path><defs><linearGradient id="P" x1="650.033" y1="118.712" x2="660.692" y2="120.614" xlink:href="#B"><stop offset="0" stop-color="#a69b92"></stop><stop offset="1" stop-color="#c5c0bd"></stop></linearGradient></defs><path fill="url(#P)" d="M663 92h0l6-7-2 5h-1l1-1c-3 2-4 7-6 10-2 5-6 11-7 17 0 4 0 7 1 11v3l2 2v1l3 6h0l-1 1c-4-1-8-3-10-7-4-9 5-23 8-32h0c2-3 3-6 5-8l1-1z"></path><path d="M655 130v4h-2v-2-1-3c1-4 0-8 1-12 0 4 0 7 1 11v3z" class="P"></path><path d="M348 706c2 0 4 0 6 1 4 1 7 2 11 5 0 0 3 2 4 2l9 9h-2c1 1 2 1 2 2h-3l-4-2h-2l1-2h0l-6-1h1c-12 0-21 1-30 9l-2 3v1l-2 1c0 1-2 2-3 3l4-16-1-1c1-1 2-2 3-4l-1 1-1-1 6-5c2-1 3-2 5-3h1c1-1 2-1 3-1l1-1z" class="l"></path><path d="M334 716l1-1 1 1c-1 1-2 4-4 5l-1-1c1-1 2-2 3-4z" class="D"></path><path d="M344 708c1-1 2-1 3-1l1 1h1c2 0 6 1 8 3-3 0-6-2-9-1-1 1-2 1-3 1v-1-1l-1-1z" class="y"></path><path d="M348 706c2 0 4 0 6 1 4 1 7 2 11 5 0 0 3 2 4 2l9 9h-2c1 1 2 1 2 2h-3l-4-2h-2l1-2h0 1l-10-8-4-2c-2-2-6-3-8-3h-1l-1-1 1-1z" class="o"></path><path d="M370 721c2 1 4 2 6 2 1 1 2 1 2 2h-3l-4-2h-2l1-2z" class="Ae"></path><path d="M348 706c2 0 4 0 6 1h-1c1 2 4 2 6 4 1 0 1 1 2 2l-4-2c-2-2-6-3-8-3h-1l-1-1 1-1z" class="AP"></path><path d="M333 732h-2v1l-1-1c1-2 1-4 2-6l1-1c1-3 2-5 4-6l1-1c1-1 2-2 3-2l6-3c1 0 4-1 5-1l6 3c1 1 2 1 4 2v1c3 0 5 1 8 2l-1 1-1-1h-3c-12 0-21 1-30 9l-2 3z" class="C"></path><path d="M776 485l3 2 2 2 1 1 2-1 1 1 1 2 2-1 1 2c-7 8-12 16-13 27 0 1 0 3-1 4v4c0 1 0 2-1 3 1 4 3 7 6 10h-1l-1 2h-3c-1 1-1 1-1 2l2 1-7 1-2 2-1 1v1l-2 1v-5c0-2 2-3 1-5-1-3 0-5 1-8h2 0v-6c-1-5 1-8 1-12 1-3 1-6 2-8 1-6 4-11 3-17h0c1-2 1-1 3-2l-1-2v-2z" class="K"></path><path d="M776 485l3 2 2 2-1 1c-2 0-2 0-3-1l-1-2v-2z" class="L"></path><path d="M768 542c-1-2-1-3-2-4l1-1c1 1 2 2 3 4l2 2c-1-1-1-1-2-1v2c0 1-1 1-1 3l-2 2-1 1-1-1c1-1 1-3 1-4 1-1 1-2 1-3h1z" class="G"></path><path d="M768 542s1 0 1 2l-1 1c-1 1-1 1-2 3l1 1-1 1-1-1c1-1 1-3 1-4 1-1 1-2 1-3h1z" class="R"></path><path d="M775 499c0 2 0 4-1 6v3l1-1v-2h1 0c-3 9-4 16-2 26 1 4 3 7 6 10h-1l-1 2h-3c-1 1-1 1-1 2l2 1-7 1c0-2 1-2 1-3v-2c1 0 1 0 2 1l-2-2c2-1 2-1 3-1l1-1c-1-2-2-5-3-7l-1-1c-1-2 0-5 0-7 0-8 2-17 5-25z" class="B"></path><path d="M784 489l1 1 1 2 2-1 1 2c-7 8-12 16-13 27 0 1 0 3-1 4v4c0 1 0 2-1 3-2-10-1-17 2-26h0-1v2l-1 1v-3c1-2 1-4 1-6 1-4 4-7 7-9l2-1z" class="O"></path><path d="M784 489l1 1 1 2c-4 4-7 7-10 13h0-1v2l-1 1v-3c1-2 1-4 1-6 1-4 4-7 7-9l2-1z" class="F"></path><defs><linearGradient id="Q" x1="182.305" y1="356.119" x2="226.287" y2="393.576" xlink:href="#B"><stop offset="0" stop-color="#ccc2ba"></stop><stop offset="1" stop-color="#f7f3f0"></stop></linearGradient></defs><path fill="url(#Q)" d="M180 361h0l-1-1 1-1h3l1 1h7l1 1c1-1 3-1 4-1l12 3-1 1c3 2 9 6 12 9l1 3c1 0 2 3 3 4h1c2 2 6 7 6 10 0 6-2 8-6 11h0c-1 1-1 0-2 1-2 1-6 2-8 1 1-1 3-1 4-2 1-6-1-10-3-15h0l-5-6h0c-1-2-3-3-4-4-8-8-16-12-26-15z"></path><path d="M216 374c2 2 5 4 7 6h1c1 2 2 4 3 7 1 2 2 4 1 6 0 3-2 5-4 6-1 1-2 1-2 1 1-1 3-3 4-5v-4c0-1-1-2-1-3s0-2-1-2v-1c-3-4-6-7-8-11z" class="G"></path><path d="M208 367c-2-1-5-3-7-3l-4-1v-1c1 0 1 0 3 1v-1c3 0 5 1 7 2 3 2 9 6 12 9l1 3c1 0 2 3 3 4-2-2-5-4-7-6l-8-7z" class="D"></path><path d="M208 367c2 1 3 1 4 2l1 1c2 1 4 4 7 6 1 0 2 3 3 4-2-2-5-4-7-6l-8-7z" class="T"></path><path d="M224 380c2 2 6 7 6 10 0 6-2 8-6 11h0c-1 1-1 0-2 1-2 1-6 2-8 1 1-1 3-1 4-2 1-6-1-10-3-15 2 1 3 5 4 8v1c1 1 1 4 1 5h2s1 0 2-1c2-1 4-3 4-6 1-2 0-4-1-6-1-3-2-5-3-7z" class="D"></path><path d="M217 486c2-2 7-1 9 0h1 0c4 0 9 0 14 1h4c0 2 1 3 1 5 3 7 6 14 7 21 1 4 1 7 1 10 1 3 1 7 1 10v4c-1 1-1 1-1 2h2c0 1 1 2 1 3s0 1 1 2v1l1 1v1c0 1 0 2 1 3v1h-1c-4-4-7-5-13-5-2 0-5 1-8 1-2 0-2-1-4-2 1-1 1-1 2-1 1 1 3 1 4 0 3-1 5-4 6-6l1-1 1-1-1-1 1-1v-1-2c1-2 1-6 1-8-1-1-1-2-2-4v-2l-3-9-1-1c0-1-1-2-1-2l-1-1c-1-3-3-5-6-7h0c-1-1-2-1-3-2l-2 1h0c-2-2-5-4-8-5l-2-1c-6-2-12-2-18-2 1-1 2-1 4-2h10 1z" class="F"></path><path d="M227 486c4 0 9 0 14 1h4c0 2 1 3 1 5 3 7 6 14 7 21 1 4 1 7 1 10 1 3 1 7 1 10v4c-1 1-1 1-1 2-2 2-6 4-9 5 2-3 4-4 6-8 3-7 2-14 1-21-1-3-1-6-2-9-4-7-7-14-15-17h0c-2-1-4-1-5-2h-1 0l-3-1h1z" class="T"></path><path d="M227 486c4 0 9 0 14 1h-8c3 1 5 2 8 4 0 1 1 2 2 2l-2-3h0c4 3 8 10 9 16-4-7-7-14-15-17h0c-2-1-4-1-5-2h-1 0l-3-1h1z" class="B"></path><path d="M206 486h10 1c11 2 20 6 27 16 2 3 4 7 5 11 1 5 2 11 1 16v1c-1 2-1 4-2 6l-1-1 1-1v-1-2c1-2 1-6 1-8-1-1-1-2-2-4v-2l-3-9-1-1c0-1-1-2-1-2l-1-1c-1-3-3-5-6-7h0c-1-1-2-1-3-2l-2 1h0c-2-2-5-4-8-5l-2-1c-6-2-12-2-18-2 1-1 2-1 4-2z" class="K"></path><path d="M220 490l1-1c-2 0-3-1-4-1h-1l1-1 7 2v1l-2 1-2-1z" class="R"></path><path d="M224 489c2 0 3 0 5 1l1 1c1 0 1 1 2 1l1 1c1 0 2 1 3 2l5 5c0 1 1 2 2 3l1 2 1 1v1c1 2 2 3 2 4 0 2 1 3 1 5l1 2v5c-1-1-1-2-2-4v-2l-3-9-1-1c0-1-1-2-1-2l-1-1c-1-3-3-5-6-7h0c-1-1-2-1-3-2l-2 1h0c-2-2-5-4-8-5l2-1v-1z" class="D"></path><path d="M430 754h1l4 1 1 1v1c3 1 4 4 7 6 0 0 1 1 1 2 1 0 1 0 2 1h0c1 2 2 4 5 6l3 7 1 3v5l1 4c0 1 0 2-1 4 0 2 0 3-1 5v1l-2 2-1 2c-1-2-1-4-1-6-3-4-5-9-7-14l-1-2c-3-2-5-5-7-9l-1-1c-4-2-10-7-14-7-3 0-3 1-5 2h-1l-1-1v-4l1-2c2-3 4-5 8-6h0l1-1h7z" class="j"></path><path d="M413 763l1-2 2 1c-1 1-2 2-2 4l-1 1v-4z" class="AQ"></path><path d="M442 772c1 1 2 1 3 2 1 0 1 1 2 1 1 1 2 1 3 2l-1 1h-2c0-1-1-2-1-2-2-1-3-2-4-3v-1z" class="P"></path><path d="M430 754h1v1c-2 0-3 0-5 1l-1 1c-2 1-3 2-4 2-2 1-3 1-5 3l-2-1c2-3 4-5 8-6h0l1-1h7z" class="b"></path><path d="M431 754l4 1 1 1v1c3 1 4 4 7 6 0 0 1 1 1 2 1 0 1 0 2 1h0c1 2 2 4 5 6l3 7c-4-1-7-6-10-9l-3-3c-1-2-2-6-4-7s-3-3-5-4l-1-1v-1z" class="AQ"></path><path d="M431 754l4 1 1 1v1c-2-1-3-1-4-1l-1-1v-1z" class="o"></path><defs><linearGradient id="R" x1="451.783" y1="780.61" x2="442.866" y2="794.937" xlink:href="#B"><stop offset="0" stop-color="#b2adab"></stop><stop offset="1" stop-color="#d7d0cf"></stop></linearGradient></defs><path fill="url(#R)" d="M442 778c2 1 4 1 7 1h1 0c1 0 1 0 2 1 0 2 1 4 2 5 0 3 0 7-1 10l-1 8-1 2c-1-2-1-4-1-6-3-4-5-9-7-14l-1-2v-2l-1-2 1-1z"></path><path d="M414 766c3-2 6-3 8-5h4v-2c2-1 3-2 5-1 1 1 1 1 2 3v2l6 6c1 1 2 1 3 2v1 1c1 1 2 2 4 3 0 0 1 1 1 2l-5-1v1l-1 1 1 2v2c-3-2-5-5-7-9l-1-1c-4-2-10-7-14-7-3 0-3 1-5 2h-1l-1-1 1-1z" class="K"></path><path d="M435 774c1 0 2 0 3 1s1 0 2 1h1 1c-2-2-4-3-7-3v-1c4 0 7 3 11 4 0 0 1 1 1 2l-5-1v1l-1 1 1 2v2c-3-2-5-5-7-9z" class="R"></path><path d="M442 781l-6-5 6 1v1l-1 1 1 2z" class="K"></path><path d="M352 685l1-1c1 0 3 0 4 1s3 2 4 3v1c-1 1-1 0-3 0-3 1-7 5-9 8-3 5-9 9-11 14l-6 5 1 1 1-1c-1 2-2 3-3 4l1 1-4 16-3 13c-1 0-1-1-2-2v-1l-1-2-2-1c-3-3-6-4-9-4h-2-1l-7 2c-4 2-7 3-10 6l-1-2c3-1 5-3 7-5l10-9h0l12-9c1-1 1-2 2-2 1-2 3-4 4-5 1-3 3-5 5-7 1-1 2-3 4-4 2-3 5-5 6-8 3-4 8-8 11-12 0-1 0 0 1-1v1z" class="H"></path><path d="M340 697c3-4 8-8 11-12 0-1 0 0 1-1v1c-1 4-3 7-6 10-5 7-10 13-17 19-2 2-5 5-8 7 1-2 3-4 4-5 1-3 3-5 5-7 1-1 2-3 4-4 2-3 5-5 6-8z" class="D"></path><path d="M301 742c5-7 12-11 20-17 2-1 4-3 6-5-1 3-2 5-4 6l1 2c-1 2-3 3-4 5-3 3-5 5-9 7h0-2-1l-7 2z" class="G"></path><path d="M308 740c2-2 4-3 6-5 3-3 5-7 9-9l1 2c-1 2-3 3-4 5-3 3-5 5-9 7h0-2-1z" class="R"></path><path d="M332 716l1 1 1-1c-1 2-2 3-3 4l1 1-4 16-3 13c-1 0-1-1-2-2v-1l-1-2-2-1c-3-3-6-4-9-4h0c4-2 6-4 9-7 1-2 3-3 4-5l-1-2c2-1 3-3 4-6l5-4z" class="E"></path><path d="M322 745v-4l1-3c0-3 3-5 3-8h0c1 1 1 1 1 2-1 3-2 6-4 10v5l-1-2z" class="B"></path><path d="M332 716l1 1 1-1c-1 2-2 3-3 4l-7 8-1-2c2-1 3-3 4-6l5-4z" class="K"></path><path d="M299 633l2 2c0 2 1 3 1 4v1s1 1 1 2h0l1 2c0 2 0 4 1 5v4l-1 1c1 1 1 2 2 3s2 3 3 5c2 2 4 3 5 5l3 3 4 8c2 2 3 4 3 7 0 1 0 1 1 2h0v4h1c1 0 2 0 3 1l1-1-1-2h1c1 1 2 2 2 3l1 1v3l1 1v1 2l-1 1v1 1 1l3-3c0-1 2-3 3-4h1c-1 3-4 5-6 8-2 1-3 3-4 4-2 2-4 4-5 7-1 1-3 3-4 5-1 0-1 1-2 2l-12 9h0c-2 1-3 2-4 3 1-4 3-7 6-9 3-11 6-23 7-34v-11c-1-3-1-6-3-8 0-1-1-1-1-2v-3l-1-1v3 3h-1c0-2-1-6-3-7l-3-3c-1-3-3-5-5-8 1 0 1-1 2-2v-1h1l1 1v-1c0-2-1-2-1-3v-3l-1-1h1c-1-1-1-1-1-2v-1-3c-1-2-2-4-2-6z" class="G"></path><path d="M301 653c5 3 7 9 9 14 0 1 0 3 1 3v3h-1c0-2-1-6-3-7l-3-3c-1-3-3-5-5-8 1 0 1-1 2-2z" class="O"></path><path d="M321 678c2 2 3 4 3 7 0 1 0 1 1 2h0v4h1c1 0 2 0 3 1l1-1c2 3 3 6 2 9v3c0 1-1 2-1 3h-1v-2c0-1 0-1-1-2 0-2 0-3-1-5l-1-1c0-1-1-2-2-3l-1 1c-2-3 1-7-1-10-1-2-2-4-2-6z" class="D"></path><path d="M330 691c2 3 3 6 2 9v3h-1l1-1c-1-2-1-3-1-4h0c-1-1-2-2-2-3-1-2-2-3-4-4h1c1 0 2 0 3 1l1-1z" class="N"></path><path d="M312 668c2 3 4 6 4 10h1 0c0 1 2 2 2 4 1 2 1 4 1 6 2 6 0 14 0 20v1c-1 0-1 1-1 1-1 1-1 2-1 2-2 5-5 10-9 14 3-11 6-23 7-34v-11c-1-3-1-6-3-8 0-1-1-1-1-2v-3z" class="E"></path><defs><linearGradient id="S" x1="568.085" y1="768.697" x2="609.202" y2="746.096" xlink:href="#B"><stop offset="0" stop-color="#787778"></stop><stop offset="1" stop-color="#989594"></stop></linearGradient></defs><path fill="url(#S)" d="M609 740l1 4c1 1 2 2 2 4l1 4v1l-2 2-2 1h0-3c0 2 2 4 2 5 0 2 0 3 1 5l-1 2h0l-2 1c-1 0-3 2-4 2-2 0-3 0-4 1-3 0-5 1-8 1h-1c-4 1-8 2-12 4l-7 4c-1 1-2 2-3 2l1-4c2-1 4-3 5-5h0-2s0-1 1-2c0 0 0-1 1-1h0v-1-1c1-2 1-4 1-6 0-1 2-3 2-5l1 1 7-6 17-12h2v1c2 0 2 0 3-1h2 0l1-1z"></path><path d="M610 748v3c-1 1-2 2-3 2 1-1 2-3 3-5z" class="AQ"></path><path d="M598 755c1-1 2-3 4-3 1-1 1 0 2 0v2l2 1h0-2-1c-2-1-3 0-5 0z" class="L"></path><path d="M609 740l1 4c1 1 2 2 2 4h-1 0c0 1 0 2-1 3v-3c-1-3-1-5-2-7l1-1z" class="K"></path><path d="M610 751c1-1 1-2 1-3h0 1l1 4v1l-2 2-2 1h0-3v-1h0l1-2c1 0 2-1 3-2zm-26 2l17-12h2v1c-5 3-11 7-16 11-3 3-6 5-9 7l-1-1 7-6z" class="R"></path><path d="M573 774h0 1c1-1 3-2 4-4l-1-1c1-1 1-1 1-2s0-1-1-1c0-1 0-2 1-3 1 0 2-1 3-2h1l-1 2h0 2c1 0 0 0 1-1h2l1 2c-3 3-6 5-8 8-1 1-1 3-2 4v1l-7 4c-1 1-2 2-3 2l1-4c2-1 4-3 5-5z" class="b"></path><path d="M570 781v-1c3-3 6-5 9-8-1 1-1 3-2 4v1l-7 4z" class="N"></path><path d="M598 755c2 0 3-1 5 0h1 2v1c0 2 2 4 2 5 0 2 0 3 1 5l-1 2h0l-2 1c-1 0-3 2-4 2-2 0-3 0-4 1-3 0-5 1-8 1h-1c-4 1-8 2-12 4v-1c1-1 1-3 2-4 2-3 5-5 8-8l1-1c3-3 6-6 10-8z" class="P"></path><path d="M607 767l1 1-2 1c-1 0-3 2-4 2-2 0-3 0-4 1-3 0-5 1-8 1v-2c3 0 6-1 10-2 2 0 5-1 7-2z" class="F"></path><path d="M598 755c2 0 3-1 5 0h1 2v1c0 2 2 4 2 5 0 2 0 3 1 5l-1 2h0l-1-1h-1c1 0 1 0 2-1l-1-1v-1h-3l-1-1c-1 0-1 0-2-1 2-1 3-2 3-3l-2-2c-6 0-8 4-13 6h-1c3-3 6-6 10-8z" class="j"></path><path d="M223 367l3 2v1l-2-3v-2c-2-3-4-8-6-11v-1c-1-3 0-9 0-13v-3c2-2 1-10 1-12v-1c0-2 0-2 1-4 0 3 1 6 1 9 0 4-1 8-2 12 0 2 0 4 1 5-1 2-1 3 0 5 0 0 1 1 1 2l9 12c0 1 0 3 1 5h1l2 1 3-1 2 2 4 5-1 1c1 1 2 1 2 2s1 2 1 3l-1 1 1 4h0l1 4-2-1c0 2 1 6 0 7l-2 3c-1 1 0 1-1 2h0c-3 3-7 4-11 5l-2-1h-2c-2 0-3 0-5 1-3 0-6-1-10-1h0-4v1c-7 0-15-1-22-2 3-1 5 0 8 0 5 0 13 0 18-2h2l1-1c2 1 6 0 8-1 1-1 1 0 2-1h0c4-3 6-5 6-11 0-3-4-8-6-10h-1c-1-1-2-4-3-4l-1-3c-3-3-9-7-12-9l1-1c6 3 13 6 18 12h2l-4-6-1-1v-1z" class="P"></path><path d="M207 407v-1c2-1 2 0 3 0h0l1 1h-4z" class="D"></path><path d="M233 386c1 2 1 3 1 4h-1l-1-2 1-2z" class="G"></path><path d="M231 381c1 2 2 3 2 5l-1 2-1-2v-5z" class="B"></path><path d="M221 353l9 12c0 1 0 3 1 5 1 1 1 3 2 4l2 7 1 2v7l-1 3h0v-4c1-7-3-14-7-20 0-1-1-2-2-4s-2-5-3-7-2-3-2-5z" class="o"></path><path d="M208 363c6 3 13 6 18 12l5 6v5l-12-13c-3-3-9-7-12-9l1-1z" class="F"></path><path d="M236 390c0-1 0-2 1-3h0v1l1 1v4 4c-3 3-5 7-9 8v1h-1c-1 0-1 0-2 1-2 0-3 0-5 1-3 0-6-1-10-1h0l-1-1h0c4-1 8-1 12-3 5-2 10-5 13-10l1-3z" class="H"></path><path d="M231 370h1l2 1 3-1 2 2 4 5-1 1c1 1 2 1 2 2s1 2 1 3l-1 1 1 4h0l1 4-2-1c0 2 1 6 0 7l-2 3c-1 1 0 1-1 2h0c-3 3-7 4-11 5l-2-1h-2c1-1 1-1 2-1h1v-1c4-1 6-5 9-8v-4-4l-1-1v-1h0c-1 1-1 2-1 3v-7l-1-2-2-7c-1-1-1-3-2-4z" class="D"></path><path d="M236 383c3 3 6 8 6 12 0 2 0 3-1 4v-1c-1-1-1-1-1-3h0c-2 0-1-3-1-5 0-1-1-1-1-1l-1-1v-1h0c-1 1-1 2-1 3v-7z" class="E"></path><path d="M238 389s1 0 1 1c0 2-1 5 1 5h0c0 2 0 2 1 3v1c-1 1-2 2-2 4h-1l-1 1c-1 0-2 1-4 1-1 1-3 1-5 2h-2c1-1 1-1 2-1h1v-1c4-1 6-5 9-8v-4-4z" class="F"></path><path d="M231 370h1l2 1 3-1 2 2 4 5-1 1c1 1 2 1 2 2s1 2 1 3l-1 1 1 4h0l1 4-2-1c0 2 1 6 0 7 0-5-1-10-4-15-2-2-3-2-5-2l-2-7c-1-1-1-3-2-4z" class="N"></path><path d="M231 370h1l2 1 3 4c2 2 4 5 5 8-4-3-5-8-9-9-1-1-1-3-2-4z" class="y"></path><path d="M237 370l2 2 4 5-1 1c1 1 2 1 2 2s1 2 1 3l-1 1 1 4h0l1 4-2-1c0-3-1-6-2-8-1-3-3-6-5-8l-3-4 3-1z" class="o"></path><path d="M244 380c0 1 1 2 1 3l-1 1c-1-2-1-3 0-4z" class="AX"></path><path d="M237 370l2 2 4 5-1 1c-2 0-3-2-5-3l-3-4 3-1z" class="AK"></path><path d="M197 278h1c1 0 1 1 2 1h0 2c1 1 1 2 2 3l1 2-1 1-1 4h2v-1c0-1 1-2 1-3 2 2 2 4 4 5 1 3 2 5 4 8l1 2-1 1c1 2 1 3 1 5 1 1 2 2 2 3 1 2 1 4 2 6l1 4s-1-1-2-1v1c-1 1-1 2-3 2h-3-6-1l-9-1h-1c-4 0-7-1-11 0h0c-2 0-4 0-6 1h0c-3 0-9-1-11 1h-2l-3-2h-4c1-1 3-1 5-2h1c4-2 8-4 12-7 7-6 12-12 18-20h0l2-3 1-3c1-1 1-1 1-2h-1v-4-1z" class="H"></path><path d="M175 315l5 1h-2-4 0l1-1z" class="F"></path><path d="M197 285l2 2c0 1-1 3-1 4l-3 6h0c0-2 1-4 1-5-1-1-1-1-2-1h0l2-3 1-3zm-35 35h22 0c-2 0-4 0-6 1h0c-3 0-9-1-11 1h-2l-3-2z" class="G"></path><path d="M197 278h1c1 0 1 1 2 1h0 2c1 1 1 2 2 3l1 2-1 1-1 4h0v2h-1l-1 3v-2l-2-1-1 3h0v-1-1-1c0-1 1-3 1-4l-2-2c1-1 1-1 1-2h-1v-4-1z" class="P"></path><path d="M203 289c0-2-1-2-1-3h-1-1c0-2 0-1 1-2h1 1l1 1-1 4h0z" class="l"></path><path d="M199 291c0-1 1-2 1-3h1l1 3-1 3v-2l-2-1z" class="D"></path><path d="M197 278h1c1 0 1 1 2 1h0 2c1 1 1 2 2 3l1 2-1 1-1-1h-1-1l-1-3h0l-2 2h-1v-4-1z" class="t"></path><path d="M198 306h2v1c1 2 2 3 4 5 5 3 9 4 14 6v1c-1 1-1 2-3 2h-3-6-1l-3-3s-1 0-1-1c-3-3-3-7-3-11z" class="F"></path><path d="M203 289h2v-1c0-1 1-2 1-3 2 2 2 4 4 5 1 3 2 5 4 8l1 2-1 1c1 2 1 3 1 5 1 1 2 2 2 3 1 2 1 4 2 6l1 4s-1-1-2-1c-5-2-9-3-14-6-2-2-3-3-4-5v-1h-2c1-3 1-5 2-8 0-1 1-3 1-4l1-3h1v-2h0z" class="D"></path><path d="M212 302l1 2v2c-1-1-2-2-2-3v-1h1z" class="C"></path><path d="M213 304l2 2c1 1 2 2 2 3v5c-2-3-2-6-4-8v-2z" class="N"></path><path d="M217 309c1 2 1 4 2 6l1 4s-1-1-2-1c-5-2-9-3-14-6v-1c4 3 8 4 12 5h1v-2-5z" class="t"></path><path d="M203 289h2v-1c0-1 1-2 1-3 2 2 2 4 4 5 1 3 2 5 4 8l1 2-1 1c1 2 1 3 1 5l-2-2-1-2h-1 0c-1-1-1-1-1-2l-1 1c-1-2-1-2-2-3l1-1h-1l-1 1v-2h-1c-2 1-2 2-2 3-1 2-1 3-1 5v1c-1 2 1 4 2 6v1c-2-2-3-3-4-5v-1h-2c1-3 1-5 2-8 0-1 1-3 1-4l1-3h1v-2h0z" class="N"></path><path d="M203 291h1c0 2-1 3-2 5-1 1-1 2-1 3l-1-1c0-1 1-3 1-4l1-3h1zm3-1c4 3 5 6 7 10l1 1c1 2 1 3 1 5l-2-2-1-2-1-2c-1-2-1-3-3-3 0-3-1-5-2-7z" class="y"></path><path d="M203 289h2v-1c0-1 1-2 1-3 2 2 2 4 4 5 1 3 2 5 4 8l1 2-1 1-1-1c-2-4-3-7-7-10v-1l-2 2h-1v-2h0z" class="AX"></path><path d="M663 86l1 1c-2 2-4 2-4 5-1 2-3 4-4 6-2 2-3 5-5 7-3 5-6 9-8 14-1 3-3 8-1 11l-1 1c0 1 1 2 1 4 3 4 8 8 13 10l46 29c-2 0-2 0-3-1h-1c-1-1-2-1-3-1 2 2 4 4 7 5l3 2c-1 1-1 1-2 1l1 1v1l4 3 3 3 2 2 5 4 1 1c1 0 1 1 2 1 0 1 1 1 2 1l1 1h1c1 1 2 1 3 2s0 1 1 1l3 3c-2 0-3-1-5-2h0 0c1 1 1 2 2 3h-1-3 0l-4-4-1-1h-1c-2-1-3-4-4-5-1 0-2 0-2-1-1-1-1-1-2-1 0-1-1-2-2-2h0l-1-1h-1c-1 0-30-21-34-24l-25-15c-9-5-18-10-28-13-4-2-9-4-13-5v-2l2 1h4l5 2 6 2c1 0 1 0 2 1h1l-2-1c2 0 3 0 5 1 2 0 3 0 4-1-1 0-2-1-2-2v-6c0-2 3-4 4-5 0-2 0-1 1-2l1-1c1-2 2-4 3-5v-1l2-2c1-1 1-1 1-2-1 1-2 1-3 1h0l1-1c3-2 4-4 6-6v-1c1-2 3-4 5-5l11-12z" class="R"></path><path d="M694 177c1 0 1 1 2 2v1l-3-2 1-1z" class="U"></path><path d="M643 119c-1 3-3 8-1 11l-1 1c0 1 1 2 1 4l-2-4c-1-1-1-3-1-5l1-1c0-2 1-4 3-6z" class="F"></path><path d="M606 131l2 1c3 2 8 3 13 5 4 2 9 4 14 6h1l1 1c1 0 2 0 3 1h-1-1v1c1 0 2 1 3 1 1 1 2 1 3 2 1 0 2 1 3 1v1h0c-9-5-18-10-28-13-4-2-9-4-13-5v-2z" class="K"></path><path d="M703 182c-1 0-2-2-2-2l-21-15c-3-2-7-5-11-7h-1 2v-1c6 3 12 7 18 11 2 1 4 3 6 3v1c2 2 4 4 7 5l3 2c-1 1-1 1-2 1l1 1v1z" class="U"></path><path d="M802 411c2-1 4-1 6-1-6 3-10 7-12 13-1 3-1 6-2 9h0c-1 12 1 23 6 33h0c1 2 2 3 3 5 1 1 3 2 3 3 2 3 7 6 9 7l8 4h-1c-1 0-1 1-2 1-3-1-5-1-8-1l-5 1h0c-1 1-2 1-3 1h-1l-6 1h-7-5-1c0-1-1-1-2-1v-1l-1 1c-1-2-1-4-1-5v-3-7-9-9l1-1 1-1 1-8-1-1c0-3 0-5 1-8 0-2 0-4 1-5v-3-1c1 0 1-1 1-2 1-2 3-4 5-6 1-1 2-1 3-2h1l1 1c2-2 4-4 7-5z" class="F"></path><path d="M789 468c1 1 2 3 3 4l2 2-1 1-3-3-2-3 1-1z" class="G"></path><path d="M791 485l21-1-5 1h0c-1 1-2 1-3 1h-1l-6 1h-7v-1l1-1z" class="C"></path><path d="M790 486h7c2-1 4-1 6-1v1l-6 1h-7v-1z" class="D"></path><path d="M785 472h0c0-1 1-3 1-4l1-1 1 2 2 3h-2c-1 2 0 3-1 5l3 3v1h-1c-1 0-1 1-1 1-1 1-2 1-3 2l1 1c-2 0-2 0-3-1 0-1 1-2 2-3 0-2 1-4 0-5v-4z" class="B"></path><path d="M791 435v1c0 1 0 2-1 3-1 2-1 7 0 9h0c1 1 0 2 0 4h1l-1 1v4c0 2 0 6 1 9v3 1l1 2c-1-1-2-3-3-4l-1-3c-1-6-1-11 0-17 1-5 1-9 3-13z" class="E"></path><path d="M790 430l3-3h1c-1 6-1 10-2 15v-4c-1-2-1-1-1-2 1-1 1-2 1-3 0 0-1 1-1 2-2 4-2 8-3 13-1 6-1 11 0 17l1 3-1 1-1-2-1 1c0 1-1 3-1 4h0v4c1 1 0 3 0 5-1 1-2 2-2 3 1 1 1 1 3 1h5l-1 1v1h-5-1c0-1-1-1-2-1v-1l-1 1c-1-2-1-4-1-5h1v-1l1-1v-3h0v-1l1-1v-7-4c1-1 1-2 2-4v-9c1-6 3-14 5-20z" class="P"></path><path d="M788 465l1 3-1 1-1-2-1 1c0 1-1 3-1 4h0c0-2-1-5 0-7l2 1 1-1z" class="C"></path><path d="M802 411c2-1 4-1 6-1-6 3-10 7-12 13-1 3-1 6-2 9h0c-1 12 1 23 6 33-1-1-2-3-3-4-3-6-4-13-5-19 1-5 1-9 2-15h-1l-3 3c-2 6-4 14-5 20v9c-1 2-1 3-2 4v4 7l-1 1v1h0v3l-1 1v1h-1v-3-7-9-9l1-1 1-1 1-8-1-1c0-3 0-5 1-8 0-2 0-4 1-5v-3-1c1 0 1-1 1-2 1-2 3-4 5-6 1-1 2-1 3-2h1l1 1c2-2 4-4 7-5z" class="O"></path><path d="M793 424c1-2 1-4 3-5l1 1-2 3v-1h0l-2 2z" class="AX"></path><path d="M793 424l2-2h0v1l-1 4h-1l-3 3 3-6z" class="y"></path><path d="M782 451c0 3 0 6-1 8l-1 3v-9l1-1 1-1z" class="AP"></path><path d="M790 417c1-1 2-1 3-2h1l1 1c-7 8-10 17-12 27l-1-1c0-3 0-5 1-8 0-2 0-4 1-5v-3-1c1 0 1-1 1-2 1-2 3-4 5-6z" class="R"></path><defs><linearGradient id="T" x1="398.671" y1="742.519" x2="386.684" y2="755.063" xlink:href="#B"><stop offset="0" stop-color="#605d5f"></stop><stop offset="1" stop-color="#948e89"></stop></linearGradient></defs><path fill="url(#T)" d="M369 714h0c-1-1-3-2-4-4 1 1 4 3 6 3 3 1 6 4 9 6 2 1 6 2 8 4l3 3 1-2c1 1 3 1 5 3l18 11v-2c-2-2-6-3-8-6h0 1v-1h0v-2-1-1c2 1 3 2 5 3 1 0 2 1 3 2 0 1 1 2 2 2l9 6c1 0 2 0 3 1l-1 1c1 1 1 2 2 3s1 2 1 3h1c1 2 2 3 4 4 0 1 1 1 0 2-1-1-4-3-6-3l1 1v2l3 2v1l-4-1h-1-7l-1 1h0c-4 1-6 3-8 6l-1 2c-1 4 0 9-1 14 0 4-2 9-2 13h-1-2l1-3c-1-10-3-17-7-26l1-2-4-8c-1-1-1-2-2-4l-6-9c-3-3-6-7-10-9l-4-2-1-2h3c0-1-1-1-2-2h2l-9-9z"></path><path d="M369 714h0c-1-1-3-2-4-4 1 1 4 3 6 3 3 1 6 4 9 6 2 1 6 2 8 4 1 1 1 2 1 3v1c5 5 8 9 12 15 1 2 3 6 3 8s1 4 0 5c-6-13-14-24-26-32l-9-9z" class="F"></path><path d="M380 719c2 1 6 2 8 4 1 1 1 2 1 3v1l-9-8z" class="B"></path><path d="M388 723l3 3 8 8c2 2 4 4 5 6l2-1v1h0v-1h1v-1c1 2 2 3 2 4l1 1v1 1l3 4h0c2 1 3 2 5 2v1c1 0 2 0 3 1l1 2c-4 1-6 3-8 6l-1 2c-1 4 0 9-1 14 0 4-2 9-2 13h-1-2l1-3h0c1-3 0-10 0-14h0l-3-14-1-4c1-1 0-3 0-5s-2-6-3-8c-4-6-7-10-12-15v-1c0-1 0-2-1-3z" class="F"></path><path d="M408 773l1-1s0-2-1-2c0-3-1-5 0-8l1 1 1 7c0 5 0 10-1 14v6h-2l1-3h0c1-3 0-10 0-14h0z" class="D"></path><path d="M407 750c-1-1-1-1-1-2v-3c1 1 1 0 2 1v1l1-2v-1h1v1l3 4h0c2 1 3 2 5 2v1c-1 1-2 1-2 1l-1 1c-1 1-2 1-3 1-1-2-2-5-3-7h-1l-1 2z" class="AT"></path><path d="M410 745l3 4h0c2 1 3 2 5 2v1c-1 1-2 1-2 1h-3c0-2 0-3-1-4-1-2-1-3-2-4z" class="R"></path><path d="M388 723l3 3 8 8c2 2 4 4 5 6l2-1v1h0v-1h1v-1c1 2 2 3 2 4l1 1v1h-1v1l-1 2v-1c-1-1-1 0-2-1v3c0 1 0 1 1 2 1 5 2 9 2 13l-1-1c-1 3 0 5 0 8 1 0 1 2 1 2l-1 1-3-14-1-4c1-1 0-3 0-5s-2-6-3-8c-4-6-7-10-12-15v-1c0-1 0-2-1-3z" class="U"></path><path d="M404 750c1 3 1 5 2 8l-1 1-1-4c1-1 0-3 0-5z" class="B"></path><path d="M399 734c2 2 4 4 5 6l2-1v1h0v-1h1c-1 2 0 4 0 5-2-1-3-2-4-4l-3-3c0-1-1-2-1-3z" class="C"></path><path d="M392 724c1 1 3 1 5 3l18 11v-2c-2-2-6-3-8-6h0 1v-1h0v-2-1-1c2 1 3 2 5 3 1 0 2 1 3 2 0 1 1 2 2 2l9 6c1 0 2 0 3 1l-1 1c1 1 1 2 2 3s1 2 1 3h1c1 2 2 3 4 4 0 1 1 1 0 2-1-1-4-3-6-3l1 1v2l3 2v1l-4-1h-1-7l-1 1h0l-1-2c-1-1-2-1-3-1v-1c-2 0-3-1-5-2h0l-3-4v-1-1l-1-1c0-1-1-2-2-4v1h-1v1h0v-1l-2 1c-1-2-3-4-5-6l-8-8 1-2z" class="K"></path><path d="M413 749l1-1 1 1c1 0 1-1 3-1 0-1 4 2 5 2h2c0 1 1 1 2 1h-9c-2 0-3-1-5-2z" class="C"></path><path d="M418 751h9c2 1 2 1 3 3h-7l-1 1h0l-1-2c-1-1-2-1-3-1v-1z" class="B"></path><path d="M397 727l18 11c2 1 3 2 5 3l3 2c2 1 5 3 8 4h1l1-1c1 2 2 3 4 4 0 1 1 1 0 2-1-1-4-3-6-3l1 1v2c-3-1-4-2-7-4h1v-1c-2-1-3-2-5-3-1 0-2-1-2-1l-1-1c-2-1-3-2-4-3-2-1-3-1-4-2s-2-2-3-2c-1-1-3-1-4-3h0c-1-2-4-3-5-4-1 0-1-1-1-1z" class="P"></path><path d="M431 749c-1 0-2 0-3-1l-1-1-1-1-1-1c-1 0-2-1-3-1-1-1 0-1-1-1l-1-1v-1l3 2c2 1 5 3 8 4h1l1-1c1 2 2 3 4 4 0 1 1 1 0 2-1-1-4-3-6-3z" class="L"></path><defs><linearGradient id="U" x1="426.528" y1="732.045" x2="409.715" y2="736.812" xlink:href="#B"><stop offset="0" stop-color="#928e8c"></stop><stop offset="1" stop-color="#c6c3c3"></stop></linearGradient></defs><path fill="url(#U)" d="M407 730h0 1v-1h0v-2-1-1c2 1 3 2 5 3 1 0 2 1 3 2 0 1 1 2 2 2l9 6c1 0 2 0 3 1l-1 1c1 1 1 2 2 3s1 2 1 3h1l-1 1h-1c-3-1-6-3-8-4l-3-2c-2-1-3-2-5-3v-2c-2-2-6-3-8-6z"></path><path d="M415 736c3 1 4 3 7 4 0 1 1 2 1 3l-3-2c-2-1-3-2-5-3v-2z" class="G"></path><path d="M422 740c2 1 4 3 7 4v-1h2c1 1 1 2 1 3h1l-1 1h-1c-3-1-6-3-8-4 0-1-1-2-1-3z" class="K"></path><path d="M429 744v-1h2c1 1 1 2 1 3h1l-1 1-3-3z" class="o"></path><path d="M776 546h10v2 1l-1 1c-1 0-1 1-1 2l-1 1c-1 3-2 5-2 7h0c0 3 0 6 1 9 2 8 6 19 15 24 0 1 0 1 1 1h1l1 1h0c1 1 2 2 4 3l1 1h-1c-3 0-4 0-7 3-1 3-1 5 0 7s4 5 5 6c2 2 4 4 5 6h0l-15-9h0c-4 0-10-4-13-6-5-4-8-9-10-14l-3-8c-2-5-3-9-3-14h0v-5l-1-1v-1c0-1 0-2-1-4v-1c0-1 1-2 1-4v-2h0c1-2 1-4 2-5v5l2-1v-1l1-1 2-2 7-1z" class="F"></path><path d="M804 598c-5 0-11-1-15-3v-1c3 1 6 2 10 2v-2l1 1h0c1 1 2 2 4 3z" class="R"></path><path d="M773 566h1 0l1 1h0v1h1 0l1 2 2-4h0c-1 5-2 9-2 14-1 1-1 3-1 5 1 2 3 4 4 6v1c-2-2-4-5-5-7s-2-3-3-5l1-1v-2l1-1h0v-1-2l-1-4v-3z" class="B"></path><path d="M773 566h1 0l1 1h0v1h1 0l1 2c-2 4-3 10-2 15h0c-1-2-2-3-3-5l1-1v-2l1-1h0v-1-2l-1-4v-3z" class="U"></path><path d="M773 569l1 1c1 1 0 2 0 3l-1-4z" class="F"></path><path d="M775 558c1-1 3-4 5-5h3c-1 3-2 5-2 7h0l-1 1c-1 1-2 3-1 5l-2 4-1-2h0-1v-1h0l-1-1h0-1v-2h1v-1c0-2 1-3 1-5z" class="R"></path><path d="M774 553c1 1 2 1 3 2l2-3h5l-1 1h-3c-2 1-4 4-5 5 0 2-1 3-1 5v1h-1v2 3l1 4v2 1h0l-1 1v2l-1 1-1-6h-1l-1-4c1-1 1-2 1-2-1-4 0-8 2-10 0-2 1-3 2-4v-1z" class="P"></path><path d="M771 574v-1-1-1-1c2-2 0-3 1-5v-3-1c0-1 2-2 3-3 0 2-1 3-1 5v1h-1v2 3l1 4v2 1h0l-1 1v2l-1 1-1-6z" class="K"></path><path d="M776 546h10v2 1l-1 1c-1 0-1 1-1 2h-5l-2 3c-1-1-2-1-3-2v1c-1 1-2 2-2 4-2 2-3 6-2 10 0 0 0 1-1 2-1-3-1-7-2-8h-2c0 2 0 3-1 4v4h-1 0v-5l-1-1v-1c0-1 0-2-1-4v-1c0-1 1-2 1-4v-2h0c1-2 1-4 2-5v5l2-1v-1l1-1 2-2 7-1z" class="AP"></path><path d="M767 558c0-1 1-1 1-2h2c-1 1-1 2-2 3l-1-1z" class="O"></path><path d="M786 548v1l-1 1c-1 0-1 1-1 2h-5l-2 3c-1-1-2-1-3-2 0 0 1-1 1-2h2c0 1 1 1 2 1 1-2 3-2 5-3l2-1z" class="j"></path><path d="M766 551v2c3 0 3-1 5-2h2l-2 3s-1 1-1 2h-2c0 1-1 1-1 2v-1l-1-1h-2v-1-1-2l2-1z" class="AU"></path><path d="M764 554h4v1c1-1 1 0 1-1h2s-1 1-1 2h-2c0 1-1 1-1 2v-1l-1-1h-2v-1-1z" class="b"></path><path d="M762 552c1-2 1-4 2-5v5 2 1 1h2l1 1c-2 1-2 1-3 3v6 4h-1 0v-5l-1-1v-1c0-1 0-2-1-4v-1c0-1 1-2 1-4v-2h0z" class="l"></path><path d="M764 555v1h2l1 1c-2 1-2 1-3 3v6 4h-1 0v-5c-1-4-1-7 1-10z" class="o"></path><defs><linearGradient id="V" x1="771.305" y1="546.239" x2="777.912" y2="552.973" xlink:href="#B"><stop offset="0" stop-color="#666464"></stop><stop offset="1" stop-color="#8b8582"></stop></linearGradient></defs><path fill="url(#V)" d="M776 546h10v2l-2 1h-5c-2-1-4 1-6 2h0-2c-2 1-2 2-5 2v-2-1l1-1 2-2 7-1z"></path><path d="M764 566c1-1 1-2 1-4h2c1 1 1 5 2 8l1 4h1l1 6c1 2 2 3 3 5s3 5 5 7c3 5 8 8 12 13 2 3 3 5 6 8v-1l1 1c1 1 1 2 3 3v-1c2 2 4 4 5 6h0l-15-9h0c-4 0-10-4-13-6-5-4-8-9-10-14l-3-8c-2-5-3-9-3-14h1v-4z" class="H"></path><path d="M764 566c1-1 1-2 1-4h2c1 1 1 5 2 8l1 4v-1c-1-1-1 0-1-2l-1-4c0 3 1 5 1 8v6c1 8 6 16 12 21l8 8h1l2 2c-4 0-10-4-13-6-5-4-8-9-10-14l-3-8c-2-5-3-9-3-14h1v-4z" class="B"></path><path d="M791 206h1c0 1-4 3-4 3l50 1h16 6c4 2 9 0 13 1h5c-2 2-12 4-15 5l-22 3c-4 0-8 0-12 1-1 1-2 0-4 1l-65 5c-4 0-11 1-15 0v-1l-1-1 2-2h-1l1-2c2-2 4-3 6-5l2-2-1-2h1c1 1 1 1 1 2h2c2-1 4 0 5 0l-2-1v-1c3 0 6 0 8-2h10 0l7 1c2 0 5-3 6-4z" class="V"></path><path d="M752 215c1 1 3 1 4 1 3 0 6-2 9 0-7 1-14 2-19 6h-1l1-2c2-2 4-3 6-5z" class="t"></path><path d="M753 211h1c1 1 1 1 1 2h2c2-1 4 0 5 0h5c2 1 7 0 8 1v1c1 0 1 0 2 1h-4c-2 0-6 1-8 0-3-2-6 0-9 0-1 0-3 0-4-1l2-2-1-2z" class="AY"></path><path d="M762 213h5c2 1 7 0 8 1v1c1 0 1 0 2 1h-4c-2-2-5-1-8-1s-5-1-8-2c2-1 4 0 5 0z" class="b"></path><path d="M791 206h1c0 1-4 3-4 3l50 1h16 6c4 2 9 0 13 1l-73 4-23 1c-1-1-1-1-2-1v-1c-1-1-6 0-8-1h-5l-2-1v-1c3 0 6 0 8-2h10 0l7 1c2 0 5-3 6-4z" class="AE"></path><path d="M767 213l36 1-3 1-23 1c-1-1-1-1-2-1v-1c-1-1-6 0-8-1z" class="O"></path><path d="M673 692l1-1c1 2 3 3 4 5 2 1 6 5 7 7v1h1l1-2c3 4 6 8 9 10 2 3 6 6 7 8 4 3 8 7 12 10 3 0 4 2 7 3 1 1 2 2 2 4 1 2 4 3 6 5l5 4c0-1-1-3-2-4 4 3 7 8 10 11-1 0-1 1-2 1l-3-3c-2-2-3-3-6-4l-8-6-6-5-29-22h-2l-1 1c0-1-1-1-1-1-2 0-3 1-4 1v1h-1l-1 1h-2l-1 1 1 1h-1l-2 1-3-1-1 1h-2v1l-5 1h-2c-1-1-2-1-3-1l-1 1c-4 1-7 2-11 4-2 0-3 2-5 3-4 3-9 6-11 11v1c-2 2-4 5-4 8v1c-1 4-4 6-2 10l-3 6c-2 7-4 12-4 19v7l-2-1-1-4c-1-3-1-6 0-8l1-8h-2c-1 0-2-1-3-1l-1-2h-1l1-2c-1-2-1-3-1-5 0-1-2-3-2-5h3 0l2-1 2-2v-1l-1-4c0-2-1-3-2-4l-1-4 1-2c2 0 3-2 5-2 1-1 2-2 4-3 1-2 3-2 4-4l8-5v-1l4-2c3-2 6-2 9-3l3-1c1-1 3-2 5-2l9-5 6-3 6-3s1 0 2-1v-2c2 1 2 2 4 3l9 8 27 21h0c-5-5-25-19-26-23l-16-18z" class="B"></path><path d="M647 717c1-1 3-2 5-2-2 2-4 3-7 4v1l2-3z" class="U"></path><path d="M615 775c1 3 0 7 2 10v7l-2-1-1-4c-1-3-1-6 0-8v3h1v-6-1z" class="N"></path><defs><linearGradient id="W" x1="670.979" y1="709.068" x2="676.455" y2="713.023" xlink:href="#B"><stop offset="0" stop-color="#726b6c"></stop><stop offset="1" stop-color="#87837e"></stop></linearGradient></defs><path fill="url(#W)" d="M666 713c2-1 3-2 4-3 4-2 7-2 11-1-1 1-2 1-3 2-2 0-3 1-5 2h-1c-1 0-1 0-2 1h-1l-1-1-1 1-1-1z"></path><path d="M666 713l1 1 1-1 1 1h1c1-1 1-1 2-1-2 2-3 2-4 4-1 0-2 1-2 2h-1c-3 0-4 0-7-1 3-1 6-3 8-5z" class="O"></path><defs><linearGradient id="X" x1="619.445" y1="771.851" x2="614.022" y2="777.65" xlink:href="#B"><stop offset="0" stop-color="#8a8682"></stop><stop offset="1" stop-color="#a6a2a0"></stop></linearGradient></defs><path fill="url(#X)" d="M615 775c1-3 2-7 2-11l1 1c0 2-1 4-1 5h1c1-1 1-2 2-4h1c-2 7-4 12-4 19-2-3-1-7-2-10z"></path><path d="M681 709l2 1 2 1c-2 1-4 1-6 1s-5 2-7 2v3l-1 1v-1l-2 1v1h-3c0-1 1-2 2-2 1-2 2-2 4-4h1c2-1 3-2 5-2 1-1 2-1 3-2z" class="o"></path><path d="M681 709l2 1c-3 2-7 2-10 3 2-1 3-2 5-2 1-1 2-1 3-2z" class="O"></path><path d="M715 730c3 0 4 2 7 3 1 1 2 2 2 4 1 2 4 3 6 5l5 4-1 1-19-16v-1z" class="C"></path><path d="M685 711l2 1 2 2h-2l-1 1c0-1-1-1-1-1-2 0-3 1-4 1v1h-1l-1 1h-2l-1 1 1 1h-1l-2 1-3-1-1 1-1-1v-1l2-1v1l1-1v-3c2 0 5-2 7-2s4 0 6-1z" class="AP"></path><path d="M687 712l2 2h-2l-1 1c0-1-1-1-1-1-2 0-3 1-4 1-1-1-2-1-3-1-1 1-2 1-3 1l2-1c1-1 2-1 3-1 2 0 5 0 7-1z" class="L"></path><path d="M675 715c1 0 2 0 3-1 1 0 2 0 3 1v1h-1l-1 1h-2l-1 1 1 1h-1l-2 1-3-1c1-1 3-1 4-2l-1-1 1-1zm-2-23l1-1c1 2 3 3 4 5 2 1 6 5 7 7v1h1l1-2c3 4 6 8 9 10 2 3 6 6 7 8 4 3 8 7 12 10v1l-17-14c-3-2-6-5-9-7l-16-18z" class="j"></path><path d="M613 753l2 2c0 1 1 3 0 4v12h-2c-1 0-2-1-3-1l-1-2h-1l1-2c-1-2-1-3-1-5 0-1-2-3-2-5h3 0l2-1 2-2z" class="O"></path><path d="M609 758c2 0 3 0 4 1l1 1-1 3h-2-1c0-2 0-3-1-5h0z" class="Ae"></path><path d="M613 753l2 2c0 1 1 3 0 4h0l-1 1h0l-1-1c-1-1-2-1-4-1v-2h0l2-1 2-2z" class="K"></path><path d="M609 756c2 0 4 0 5 2 0 0 0 1 1 1l-1 1h0l-1-1c-1-1-2-1-4-1v-2h0z" class="AU"></path><path d="M615 759h0v12h-2c-1 0-2-1-3-1l-1-2h-1l1-2c-1-2-1-3-1-5 1 2 1 3 1 4v1h2c0-1 0-1 1-2h1c1-2 1-2 1-4l1-1z" class="AK"></path><defs><linearGradient id="Y" x1="631.668" y1="733.955" x2="643.272" y2="746.36" xlink:href="#B"><stop offset="0" stop-color="#585556"></stop><stop offset="1" stop-color="#7c7977"></stop></linearGradient></defs><path fill="url(#Y)" d="M617 764c0-5 4-14 6-18 5-10 13-18 22-23 4-2 9-4 13-5 3 1 4 1 7 1h1 3l1 1h-2v1l-5 1h-2c-1-1-2-1-3-1l-1 1c-4 1-7 2-11 4-2 0-3 2-5 3-4 3-9 6-11 11v1c-2 2-4 5-4 8v1c-1 4-4 6-2 10l-3 6h-1c-1 2-1 3-2 4h-1c0-1 1-3 1-5l-1-1z"></path><defs><linearGradient id="Z" x1="612.5" y1="734.214" x2="629.362" y2="740.114" xlink:href="#B"><stop offset="0" stop-color="#8a8788"></stop><stop offset="1" stop-color="#cfc8c5"></stop></linearGradient></defs><path fill="url(#Z)" d="M644 718l3-1-2 3c-2 0-4 1-4 3h-1c-1 1-1 1-2 1-2 1-5 5-7 7 0 0-1 1-1 2-1 1-4 2-5 4-4 5-7 12-10 18l-2-2v-1l-1-4c0-2-1-3-2-4l-1-4 1-2c2 0 3-2 5-2 1-1 2-2 4-3 1-2 3-2 4-4l8-5v-1l4-2c3-2 6-2 9-3z"></path><path d="M609 740l1-2v4c1 0 2 1 3 2h-3l-1-4z" class="AQ"></path><path d="M610 744h3c1 3 0 5 0 8l-1-4c0-2-1-3-2-4z" class="L"></path><path d="M632 724c1 0 2-1 3-1s1-1 2-1c1-1 2-1 3-1v1c-2 1-3 2-5 3v-1h0-3z" class="C"></path><path d="M644 718l3-1-2 3c-2 0-4 1-4 3l-1-1v-1c-1 0-2 0-3 1-1 0-1 1-2 1s-2 1-3 1h-1v-1l4-2c3-2 6-2 9-3z" class="R"></path><path d="M218 318c1 0 2 1 2 1v1c-1 2-1 2-1 4v1c0 2 1 10-1 12v3c0 4-1 10 0 13v1c2 3 4 8 6 11v2l2 3v-1l-3-2v1l1 1 4 6h-2c-5-6-12-9-18-12l-12-3-1-1h2l-3-1h0l-15-2-13 1h0v-1c1-1 3-2 4-2 2-1 3-1 5-2l3-4c1-1 2-1 3-2 1-3 2-7 2-10-1-4-4-7-7-10l-3-2-6-2c2-2 8-1 11-1h0c2-1 4-1 6-1h0c4-1 7 0 11 0h1l9 1h1 6 3c2 0 2-1 3-2v-1z" class="E"></path><path d="M185 349c-1 2-4 3-6 4 1 1 5 0 5 1-2 0-6 1-8 0l9-5z" class="T"></path><path d="M204 356c4 1 11 2 13 6h1c1 1 1 2 2 3l-1 1c-1-1-2-1-3-2l1-1c-3-4-8-5-13-7zm-20-36c7 1 16 4 22 8 3 2 6 5 7 9-2-3-5-7-8-8-9-4-17-8-27-8h0c2-1 4-1 6-1z" class="D"></path><path d="M211 328c3 2 4 8 4 11l1 11c0 6 5 10 7 16v1 1l1 1-3-2v-3h-1c0-1 0-1-1-2v-1c-1 0-1-1-2-2 0 0-1-1-1-2h0c-1-2-1-4-1-6-1-1 0-2 0-3-1-2-1-3-1-5 0-3 1-6-1-9-1-2-1-4-2-6z" class="B"></path><path d="M218 318c1 0 2 1 2 1v1c-1 2-1 2-1 4v1c0 2 1 10-1 12v3c0 4-1 10 0 13v1c2 3 4 8 6 11v2l2 3v-1l-3-2v-1c-2-6-7-10-7-16l-1-11c0-3-1-9-4-11-3-3-8-6-12-6-2-1-2-1-3-2l9 1h1 6 3c2 0 2-1 3-2v-1z" class="D"></path><path d="M173 324l-2-2h5l1 1h0 2l-1-1c2 0 2 0 4 1h3c1 0 3 0 5 1 1 0 3 0 4 1s1 1 2 1c2 0 3 1 5 2l1 1c1 0 1 0 1 1-1 3-3 5-6 7-3 4-7 8-10 11-1 1-1 0-2 1l-9 5h-2 0l1 1 1 1 2-1h4c8-1 15 0 22 1 5 2 10 3 13 7l-1 1c1 1 2 1 3 2l2 1 3 2 4 6h-2c-5-6-12-9-18-12l-12-3-1-1h2l-3-1h0l-15-2-13 1h0v-1c1-1 3-2 4-2 2-1 3-1 5-2l3-4c1-1 2-1 3-2 1-3 2-7 2-10-1-4-4-7-7-10l-3-2z" class="F"></path><path d="M175 352l-1 1c-1 1-2 2-4 3h9l-13 1h0v-1c1-1 3-2 4-2 2-1 3-1 5-2z" class="B"></path><path d="M194 358c8 1 15 3 22 6 1 1 2 1 3 2l2 1 3 2 4 6h-2c-5-6-12-9-18-12l-12-3-1-1h2l-3-1z" class="N"></path><path d="M819 279c1-1 1-5 2-7 0-1 1-3 1-4l3-1s1 2 1 3v3l2-4h0 1v4 3l1 3v3l2 3v1l1 4 1 1c0 1 1 2 1 3l2 3c1 2 3 5 5 6 1 2 6 8 10 9h0c2 1 3 2 4 3h0l1 1c1 1 3 2 5 3l-7 1-6 3c-3 2-6 4-8 8-1 3-2 6 0 10l1 2 1 1c0 1 1 3 3 4s3 3 5 4l1 1s1 1 1 2h0c-14-5-25-12-32-24h-1c0-1 0-1-1-2 0-1-1-2-2-2-2 0-4 4-6 6v2l-3 2-1 1 1 1-1 1c1 1 2 2 2 3h-1c-2-1-3-2-3-3l-2-4c-2-6-3-9-3-15l1 2 1-1c-1-1-1-2-1-3l1-1h2 2l3-1h0 1l7-1h4l-1-11c1-2 1-3 1-5v1-2-2-7l-1-3 1-1h0l-1-3-1-1-1-2 1-1z" class="F"></path><path d="M845 318h6v1h-3c-3 1-5 1-8 1v-1h1c1 0 2-1 4-1zm-9 8c1 0 0 0 1 1h0l1-1h0c-1 2-1 3-2 5-1 5 1 8 3 12 1 1 1 1 0 2-1-2-3-4-3-5v-1c-1-1-1-1-1-2v-1c0-2-1-4 0-6v-1c1-1 1-2 1-3zm-5-33c0-3-1-5 1-7l1 4 1 1c-1-1-2-1-2-3v5l1 1c0 1 0 1 1 2 0 2 1 4 2 5 1 3 3 6 4 9-5-4-7-11-9-17z" class="B"></path><path d="M831 301l-2-14c-1-3-1-6 1-8v3l2 3v1c-2 2-1 4-1 7 2 6 4 13 9 17 1 0 2 1 3 1l4 2v1h0l-6-3c-3-2-6-4-8-7l-2-3z" class="R"></path><path d="M830 282l2 3v1c-2 2-1 4-1 7v-1c-1-3-2-7-1-10z" class="F"></path><path d="M819 279c1-1 1-5 2-7 0-1 1-3 1-4l3-1s1 2 1 3v3l2-4h0 1v4 3l1 3c-2 2-2 5-1 8l2 14 2 3c-3-3-6-6-7-9l-1-1-2-3-2-5h0l-1-3-1-1-1-2 1-1z" class="P"></path><path d="M831 301h0c-3-4-4-10-4-15 1-3 0-6 1-9l1-1 1 3c-2 2-2 5-1 8l2 14z" class="C"></path><path d="M826 273l2-4h0 1v4c-2 2-3 4-3 7 0 1 0 2-1 3v5c0 2 1 4 1 7l-1-1-2-3 1-1c-1-3-1-6 0-9 1-2 1-6 2-8z" class="j"></path><path d="M824 277v-1c0-1 1-1 1-2 0-2 0-3 1-4v3c-1 2-1 6-2 8-1 3-1 6 0 9l-1 1-2-5h0l-1-3c1-1 1-2 1-3l2-4h0c1 0 1 0 1 1z" class="L"></path><path d="M820 283c1-1 1-2 1-3l2-4h0c1 0 1 0 1 1-1 3-2 5-2 9h-1l-1-3z" class="AP"></path><path d="M819 279c1-1 1-5 2-7 0-1 1-3 1-4l3-1s1 2 1 3c-1 1-1 2-1 4 0 1-1 1-1 2v1c0-1 0-1-1-1h0l-2 4c0 1 0 2-1 3l-1-1-1-2 1-1z" class="AK"></path><defs><linearGradient id="a" x1="819.363" y1="311.5" x2="844.441" y2="299.77" xlink:href="#B"><stop offset="0" stop-color="#cfc9c5"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#a)" d="M821 290l-1-3 1-1 2 5 2 3 1 1c1 3 4 6 7 9 2 3 5 5 8 7l6 3 2 2c1 0 2 1 4 1h-2v1h-6c-1-1-2-1-3-1h0c-2-1-2-1-3-1-2 2-3 0-5 2-1 0-2 0-3-1-3 0-7-1-10-1l-1-11c1-2 1-3 1-5v1-2-2-7z"></path><path d="M821 290l-1-3 1-1 2 5 2 3c-2-2-2-2-3-4 0 2 1 3 1 6v6 1c-1-1-1-2-2-2v-2-2-7z" class="U"></path><path d="M821 316c3 0 7 1 10 1 1 1 2 1 3 1 2-2 3 0 5-2 1 0 1 0 3 1h0c1 0 2 0 3 1-2 0-3 1-4 1h-1v1c-3 1-6 2-9 2-1 0-1 1-2 1l-5 3h-1-2c-1 1 0 2-1 4l1 1h-1c0-1 0-1-1-2 0-1-1-2-2-2-2 0-4 4-6 6v2l-3 2-1 1 1 1-1 1c1 1 2 2 2 3h-1c-2-1-3-2-3-3l-2-4c-2-6-3-9-3-15l1 2 1-1c-1-1-1-2-1-3l1-1h2 2l3-1h0 1l7-1h4z" class="L"></path><path d="M805 324c1 2 2 5 1 7 0 1-1 2-1 3l-1-2c1-2 1-5 1-8z" class="AQ"></path><path d="M802 322l2 1 1 1c0 3 0 6-1 8l-3-9 1-1z" class="o"></path><path d="M802 318h2c0 1 1 1 2 1 1 1 2 1 3 2l-1 1 1 1c0 1 1 1 1 3 0 0 1 1 1 2l-4 5h0v-1c0-2 1-6 0-8 0 0-1-1-2-1v-1c0-2-2-3-4-3l1-1z" class="N"></path><path d="M800 321l1 2 3 9 1 2c0 2 1 4 2 6 1 1 2 2 2 3h-1c-2-1-3-2-3-3l-2-4c-2-6-3-9-3-15z" class="AT"></path><path d="M806 319c3 1 6 2 9 1 2-1 4 0 6 1h-1l1 1c-4 1-7 3-10 6 0-1-1-2-1-2 0-2-1-2-1-3l-1-1 1-1c-1-1-2-1-3-2z" class="C"></path><path d="M821 316c3 0 7 1 10 1 1 1 2 1 3 1 2-2 3 0 5-2 1 0 1 0 3 1h0c1 0 2 0 3 1-2 0-3 1-4 1h-1v1c-3 1-6 2-9 2-1 0-1 1-2 1l-5 3h-1-2c-1 1 0 2-1 4-1-2-1-3-1-5l1-1c0-1 0-1 1-1 1-1 1-1 2-1v-1l-2 1-1-1h1c-2-1-4-2-6-1-3 1-6 0-9-1-1 0-2 0-2-1h2l3-1h0 1l7-1h4z" class="R"></path><path d="M823 321h4l1 1h-1c-1 0-2 1-2 1l-6 2 1-1c0-1 0-1 1-1 1-1 1-1 2-1v-1z" class="D"></path><path d="M821 316c3 0 7 1 10 1 1 1 2 1 3 1 2-2 3 0 5-2 1 0 1 0 3 1h0c1 0 2 0 3 1-2 0-3 1-4 1h-1-5c-4 0-8-1-11 0h-2c-3 0-10 0-13-2h1l7-1h4z" class="F"></path><defs><linearGradient id="b" x1="718.529" y1="203.654" x2="726.693" y2="154.927" xlink:href="#B"><stop offset="0" stop-color="#c9c4c1"></stop><stop offset="1" stop-color="#fefbf8"></stop></linearGradient></defs><path fill="url(#b)" d="M655 145c0-1-1-1-2-2 4 0 6 2 9 2 2 1 4 1 6 1h4 0c3 2 4 4 6 6 4 4 8 6 13 8 2 2 8 4 11 3l2 1 7 3c1 0 2 0 2 1 5 1 8 1 12 2 6 0 11-1 16-1l-2 2h0c0 1-1 1-2 2s-5 5-7 5h0c-1 3 2 7 4 10l3 3c1 0 3 2 3 2 3 1 6 4 9 4h1c2 0 5 1 7 2 5 1 10 1 15 1h10 3 1c3 1 6-1 9 0-2 1-3 1-4 2h-1l-1 1h0c-1 0-2 1-2 1l-1 1-9 3 1 1h0 0-10c-4-1-8 0-12 0h-16 0-2l-10 1-3-4h-1l-18-16h1l1 1h0c1 0 2 1 2 2 1 0 1 0 2 1 0 1 1 1 2 1 1 1 2 4 4 5h1l1 1 4 4h0 3 1c-1-1-1-2-2-3h0 0c2 1 3 2 5 2l-3-3c-1 0 0 0-1-1s-2-1-3-2h-1l-1-1c-1 0-2 0-2-1-1 0-1-1-2-1l-1-1-5-4-2-2-3-3-4-3v-1l-1-1c1 0 1 0 2-1l-3-2c-3-1-5-3-7-5 1 0 2 0 3 1h1c1 1 1 1 3 1l-46-29z"></path><path d="M737 191c1 0 3 2 3 2 1 1 1 2 2 3h0-1l-3-2-1-3z" class="F"></path><path d="M739 171h0c0 1-1 1-2 2s-5 5-7 5h0v-1c1-3 6-5 9-6z" class="N"></path><path d="M738 199h1c2 1 8 4 10 3 1 0 1 0 3 1h1c1 0 1 0 2 1h2c3 1 5 1 8 1h0c-5 0-10 0-15-1-4-2-8-3-12-5z" class="U"></path><path d="M706 190h1l1 1h0c1 0 2 1 2 2 1 0 1 0 2 1 0 1 1 1 2 1 1 1 2 4 4 5h1l1 1 4 4h0 3 1c-1-1-1-2-2-3h0 0c2 1 3 2 5 2 2 2 4 3 7 3v1h7c1-1 1 0 2 0h3c1-1 3-1 4-1h2c1 0 1-1 2 0 3 1 9-1 13 0h2v1h-6-9 0c3 1 8 0 12 0 2 0 4 0 6 1h2-10c-4-1-8 0-12 0h-16 0-2l-10 1-3-4h-1l-18-16z" class="P"></path><path d="M725 206h0c2 0 4 0 6 1 2 2 5 2 8 2v-1l1 1h0-2l-10 1-3-4z" class="t"></path><defs><linearGradient id="c" x1="800.414" y1="346.971" x2="831.856" y2="377.654" xlink:href="#B"><stop offset="0" stop-color="#c1bab7"></stop><stop offset="1" stop-color="#fffbf7"></stop></linearGradient></defs><path fill="url(#c)" d="M807 340l1-1-1-1 1-1 3-2v-2c2-2 4-6 6-6 1 0 2 1 2 2 1 1 1 1 1 2h1c7 12 18 19 32 24h0l4 1c1 1 2 1 2 2-3-1-5-1-8 0h0 0l-19 5c-1 0-3 1-4 2l-7 3c-2 2-4 3-6 4-3 1-6 5-8 7-1 1-2 3-4 4-1 2-2 3-3 5-2 1-3 3-3 5v1l-1 1-1-1c-2 1-2 2-3 3l-2 2c1-3 2-4 3-6h-3c-2 3-3 6-4 9 0 3 1 5 3 6h1v1 2l-3 2v1l-2-2c0-1-1-1-2-2-1 0-1-1-2-1-1-1-1-2-1-4l-1 1v1c0-1-1-2-1-3 0 1-1 1-2 2l1-5c1-2 1-4 2-6l1-3v-1c0-1 0-2 1-3l-1-1h1 2c0-1 1-3 1-4l5-8 13-22 5-8 2-2c0-1-1-2-2-3z"></path><path d="M831 344c6 5 13 9 21 11h1 0l4 1c1 1 2 1 2 2-3-1-5-1-8 0h0l-12 2c1-1 2-2 3-2s0 1 2 0h1c1 0 1-1 3-1 1 1 1 0 2 0h3c-2-2-6-2-8-3-3-2-6-4-9-5v1c-1 0-1-1-2-2-2 0-2-1-3-2v-2z" class="B"></path><path d="M851 358h0l-19 5c-1 0-3 1-4 2l-7 3c-3 1-6 3-9 4 2-3 5-3 7-5-1 0-3 1-5 1h0c7-4 17-6 25-8l12-2z" class="AP"></path><path d="M814 368h0c2 0 4-1 5-1-2 2-5 2-7 5 3-1 6-3 9-4-2 2-4 3-6 4-3 1-6 5-8 7-1 1-2 3-4 4-1 2-2 3-3 5-2 1-3 3-3 5v1l-1 1-1-1c-2 1-2 2-3 3l-2 2c1-3 2-4 3-6h-3l2-4c2-4 5-7 8-11l4-3c2-3 7-6 10-7z" class="N"></path><path d="M792 389h1l1-1 3-3-3 8h-1-3l2-4z" class="AK"></path><path d="M794 393c1-1 1-1 2-3 1 0 1-1 2-1l1-2c1-2 2-3 4-4-1 2-2 3-3 5-2 1-3 3-3 5v1l-1 1-1-1c-2 1-2 2-3 3l-2 2c1-3 2-4 3-6h1z" class="C"></path><path d="M800 378l4-3h0c0 2-3 4-4 5 2 0 3-2 4-3v1l-7 7-3 3-1 1h-1c2-4 5-7 8-11z" class="O"></path><path d="M814 368h0c2 0 4-1 5-1-2 2-5 2-7 5l-4 3c-2 1-3 2-4 3v-1c-1 1-2 3-4 3 1-1 4-3 4-5h0c2-3 7-6 10-7z" class="o"></path><path d="M807 340l1-1-1-1 1-1 3-2v-2c2-2 4-6 6-6 1 0 2 1 2 2 1 1 1 1 1 2h1c7 12 18 19 32 24h-1c-8-2-15-6-21-11l-4-3c-2-1-3-3-5-5-1-2-2-2-2-4-1-1-1-2-2-2s-2 1-3 2v1c-1 2-2 2-2 5v1c-1 1-1 3-2 4 1 2 2 3 1 5s-2 3-2 6c0 2-1 3-2 5 0 1-1 3-2 4 0 3-2 6-4 8l-2 1h1c1 0 1 0 2-1h1l2-3v1c-1 1-4 4-5 6-2 3-6 3-6 8 1-1 1-2 2-3l2-2h1c-3 4-6 7-8 11l-2 4c-2 3-3 6-4 9 0 3 1 5 3 6h1v1 2l-3 2v1l-2-2c0-1-1-1-2-2-1 0-1-1-2-1-1-1-1-2-1-4l-1 1v1c0-1-1-2-1-3 0 1-1 1-2 2l1-5c1-2 1-4 2-6l1-3v-1c0-1 0-2 1-3l-1-1h1 2c0-1 1-3 1-4l5-8 13-22 5-8 2-2c0-1-1-2-2-3z" class="j"></path><path d="M811 343c1 2 2 3 1 5s-2 3-2 6c0 2-1 3-2 5 0 1-1 3-2 4 0 3-2 6-4 8l-2 1c0 2-1 3-2 4-2 1-3 3-4 4 1-3 3-6 4-9 2-3 3-7 5-10 1 0 1-1 1-1 1-2 1-2 2-3 0-2 3-6 4-8h0c0-2 0-3 1-5v-1z" class="P"></path><path d="M807 345h1c0 5-5 13-7 18l-1 2c-1 2-2 5-4 8-1 2-3 4-3 7-1 2-3 4-4 6-2 3-3 7-5 10-2 2-4 3-4 6v1c-1 1-1 2-1 3v1c0-1-1-2-1-3 0 1-1 1-2 2l1-5c1-2 1-4 2-6 2 0 2-1 3-3 3-2 5-5 6-8 0-2 2-4 2-5s1-2 2-3c3-5 6-10 8-15 2-3 3-4 3-7h0l-1-1 5-8z" class="L"></path><path d="M782 392c3-2 5-5 6-8 0-2 2-4 2-5s1-2 2-3l-2 5v2l-1 1v1c-1 1-2 2-3 4-3 5-6 10-8 15 0 1-1 1-2 2l1-5c1-2 1-4 2-6 2 0 2-1 3-3z" class="AK"></path><defs><linearGradient id="d" x1="787.927" y1="371.225" x2="795.723" y2="373.825" xlink:href="#B"><stop offset="0" stop-color="#7b7474"></stop><stop offset="1" stop-color="#988d87"></stop></linearGradient></defs><path fill="url(#d)" d="M802 353l1 1h0c0 3-1 4-3 7-2 5-5 10-8 15-1 1-2 2-2 3s-2 3-2 5c-1 3-3 6-6 8-1 2-1 3-3 3l1-3v-1c0-1 0-2 1-3l-1-1h1 2c0-1 1-3 1-4l5-8 13-22z"></path><path d="M781 388l1 2c-1 1-1 1-1 2h1c-1 2-1 3-3 3l1-3v-1c0-1 0-2 1-3z" class="b"></path><path d="M800 372h1c1 0 1 0 2-1h1l2-3v1c-1 1-4 4-5 6-2 3-6 3-6 8 1-1 1-2 2-3l2-2h1c-3 4-6 7-8 11l-2 4c-2 3-3 6-4 9 0 3 1 5 3 6h1v1 2l-3 2v1l-2-2c0-1-1-1-2-2-1 0-1-1-2-1-1-1-1-2-1-4 1-1 1-3 2-5 2-2 5-5 6-8l3-6c0-2 2-4 3-6 1-1 2-3 4-4 1-1 2-2 2-4z" class="R"></path><path d="M781 409c0-4 3-7 5-10v2 1c0 3 1 5 3 6h1v1 2l-3 2v1l-2-2c0-1-1-1-2-2-1 0-1-1-2-1z" class="B"></path><path d="M789 408h1v1 2l-3 2c1-1 1-3 2-5z" class="G"></path><path d="M244 384l1-1 3 5c0 2 1 4 2 7 0 2 1 4 1 7v7l-1 1h0c-3-1-6-1-8-1l-1 1h1c2 3 3 5 3 10-1-2-2-3-4-4 1 4 5 6 6 10 1 5 0 11 0 16v21c0 8-1 16 1 24-1 1-2 0-3 0h-4c-5-1-10-1-14-1h0-1c-2-1-7-2-9 0h-1-10l-2-1 5-3 2-1a30.44 30.44 0 0 0 8-8c1-1 1-2 2-3 1 0 1 0 1-1 2-2 3-5 4-7 4-10 6-20 4-31-2-9-5-15-13-21l-3-1-3-2c4 0 7 1 10 1 2-1 3-1 5-1h2l2 1c4-1 8-2 11-5h0c1-1 0-1 1-2l2-3c1-1 0-5 0-7l2 1-1-4h0l-1-4z" class="H"></path><path d="M235 472h0c0 2 0 2-1 4h0l-2 2v-1-2l3-3z" class="F"></path><path d="M209 482v2 1l1-1c2 0 4 0 6-1 4-1 7-1 11-3v1c-2 2-7 2-10 3-1 0-2 1-4 1h1l2 1h-10l-2-1 5-3zm35-98l1-1 3 5c0 2 1 4 2 7 0 2 1 4 1 7v7l-1 1h0c-3-1-6-1-8-1l-1 1c-1 0-3 0-4-1 0-1 0-1 1-1 2 0 5 1 7-1 1-1 1-2 0-3 1-1 1-1 1-2v-2-2c-1 1-1 3-2 4h0v1h-3 0c1-1 0-1 1-2l2-3c1-1 0-5 0-7l2 1-1-4h0l-1-4z" class="B"></path><path d="M244 391l2 1v1c0 5-1 7-5 10 1-1 0-1 1-2l2-3c1-1 0-5 0-7z" class="AX"></path><path d="M244 384l1-1 3 5c0 2 1 4 2 7 0 2 1 4 1 7v7l-1 1h0v-1c0-3-2-14-4-16v-1l-1-4h0l-1-4z" class="P"></path><path d="M244 384l1-1 3 5c0 2 1 4 2 7-2-3-2-5-4-7h-1 0l-1-4z" class="N"></path><path d="M226 407h2l2 1c-1 0-2 0-3 1 4 1 8 2 11 5 0 1 0 2 1 3h0c1 5-1 10-1 14 0 3 2 5 3 8 2 8 3 16 1 24-1 3-2 6-4 9 1-4 2-7 2-11v-6c-1-2 0-4-1-6l-1-8-2-5v-1c-1-1-2-3-2-4s0-3 1-5c0-4 0-10-4-13-2 0-4-1-6-1l-7-3-1 1-3-1-3-2c4 0 7 1 10 1 2-1 3-1 5-1z" class="D"></path><path d="M226 407h2l2 1c-1 0-2 0-3 1l-6-1c2-1 3-1 5-1z" class="E"></path><path d="M276 601v1h2v2h1c1 1 2 1 3 3l4 3c1 2 1 3 2 5 0 3-2 4 1 7v-1l1 1 1 2h0c1 0 1 1 2 1v1c2 2 4 2 6 4 5 6 7 16 8 24l-1 3c-1-1-1-2-2-3l1-1v-4c-1-1-1-3-1-5l-1-2h0c0-1-1-2-1-2v-1c0-1-1-2-1-4l-2-2c0 2 1 4 2 6v3 1c0 1 0 1 1 2h-1l1 1v3c0 1 1 1 1 3v1l-1-1h-1c-2 0-4-2-5-2-2-1-4-1-5-1-2 0-4 1-6 1-3 2-5 3-7 5-1 1-1 1-2 1-4 4-7 8-11 12l-1 1c-1 0-1 1-2 2l-1 1c-1 2-2 4-2 6-1 2-2 4-2 6v2 4c0 3 0 6-1 9v2 1h-1 0-1c1-5 0-10 0-15v-3c0-2 1-4 1-6 3-15 15-30 8-46-2-6-7-10-13-12-9-4-19-3-28 0 2-2 5-3 7-5 1 0 1 0 3-1 1 0 2-1 4-2s6-2 9-2c1-1 3-1 5-2h0c2-1 4-2 5-2l12-3c3 0 6-1 9-2z" class="H"></path><path d="M254 684c1 6 2 12 1 18h-1c1-5 0-10 0-15v-3z" class="F"></path><path d="M286 644h1l3-1v1l-2 4c-1 0-2 1-3 2-3 2-5 3-7 5-1 1-1 1-2 1-4 4-7 8-11 12 0-1 0-2 1-3h1c1-4 5-5 7-9 0-1 2-3 3-4 3-3 6-6 9-8z" class="D"></path><path d="M287 643v-1c0-1 1-1 2-2-1-1 0-1-1-1h-1l1-1v-2h0-1v-2c-1-2-1-2-1-4h0l-1-2c0 4 0 7-2 11v1l-1 1v-1-1c-1 1-1 1-2 1 1-1 2-3 2-4v-1c0-1 1-2 1-3l-1-3-3-2c-2 0-3-2-5-3h-3 0c2-1 4-1 6-1h2l5 1h1v2 1c2 1 5 4 5 6s1 5 0 7l-3 3z" class="E"></path><path d="M279 623l5 1h1v2 1l-1-1h0-1v3l1 3-1-1v-3h0c-1-1-1-2-1-2-2-2-3-2-5-2v-1h2z" class="N"></path><path d="M253 609c2-2 6-2 8-2 1 1 0 1 1 1 3 0 6 1 9 1 3 1 5 3 8 3h0c1 2 3 4 4 6h-3l2 3 1 1v-1c1 1 2 1 3 2-1 1-1 1-2 1l-5-1h3v-1h0c-2 0-3-2-4-3-2-1-2-2-2-3l-3-3h-1c-2-2-4-2-6-3v-1c-3 0-6 0-9 1h-1 0c-4 1-8 1-12 2h0 1l1-1c1-1 1 0 2-1s2-1 4-1h1z" class="E"></path><path d="M253 609c2-2 6-2 8-2 1 1 0 1 1 1-3 1-6 1-10 1h0 1z" class="B"></path><path d="M271 609c3 1 5 3 8 3h0c1 2 3 4 4 6h-3l-2-3-3-3-4-3z" class="P"></path><path d="M285 624s1 0 2 1c1 0 3 3 4 4 3 4 6 8 5 13-1 3-3 5-5 6v1c-2 0-4 1-6 1 1-1 2-2 3-2l2-4v-1l-3 1h-1l1-1 3-3c1-2 0-5 0-7s-3-5-5-6v-1-2z" class="AX"></path><path d="M290 633c1 2 2 3 2 5l1 1c0 1 1 3 0 4-1 2-3 3-5 5l2-4v-1l-3 1h-1l1-1 3-3c1-2 0-5 0-7z" class="N"></path><path d="M285 624s1 0 2 1c1 0 3 3 4 4 3 4 6 8 5 13h-1v-4h-1c-1-2-1-3-2-4 0-1-1-2-1-2-1-1-2-2-2-3-1-2-2-2-4-3v-2z" class="AK"></path><path d="M291 629c1-1 2-1 4 0l4 4c0 2 1 4 2 6v3 1c0 1 0 1 1 2h-1l1 1v3c0 1 1 1 1 3v1l-1-1h-1c-2 0-4-2-5-2-2-1-4-1-5-1v-1c2-1 4-3 5-6 1-5-2-9-5-13z" class="F"></path><path d="M276 601v1l-1 2v1h1c2 1 4 3 5 4l1 1c0 1 1 1 1 2v1c1 0 1 0 1 1v1-1l-3-3h-1l-1 1h0c-3 0-5-2-8-3-3 0-6-1-9-1-1 0 0 0-1-1-2 0-6 0-8 2h-3v-1h0 0c2-1 4-2 5-2l12-3c3 0 6-1 9-2z" class="E"></path><path d="M261 607c4 0 7 0 11 1h1c2 1 4 2 6 4-3 0-5-2-8-3-3 0-6-1-9-1-1 0 0 0-1-1z" class="K"></path><path d="M276 602h2v2h1c1 1 2 1 3 3l4 3c1 2 1 3 2 5 0 3-2 4 1 7v-1l1 1 1 2h0c1 0 1 1 2 1v1c2 2 4 2 6 4 5 6 7 16 8 24l-1 3c-1-1-1-2-2-3l1-1v-4c-1-1-1-3-1-5l-1-2h0c0-1-1-2-1-2v-1c0-1-1-2-1-4l-2-2-4-4c-2-1-3-1-4 0-1-1-3-4-4-4-1-1-2-1-2-1h-1c1 0 1 0 2-1-1-1-2-1-3-2v1l-1-1-2-3h3c-1-2-3-4-4-6l1-1h1l3 3v1-1c0-1 0-1-1-1v-1c0-1-1-1-1-2l-1-1c-1-1-3-3-5-4h-1v-1l1-2z" class="D"></path><path d="M280 618h3l1 1c-1 1-1 1-1 2h-1l-2-3zm2-11l4 3c1 2 1 3 2 5 0 3-2 4 1 7l-2 1c0-1-1-1-1-2-1-2-1-3-2-5h-1 1 1 1c-1-4-3-6-4-9z" class="K"></path><path d="M256 539l1-1c2 2 2 5 4 8 1 5 2 9 4 14 3 5 7 9 8 15 1 2 3 4 3 6v1c1 5 4 10 5 15l1 2c-1 1-1 1-3 1l-1 2h-2v-1c-3 1-6 2-9 2l-12 3c-1 0-3 1-5 2h0c-2 1-4 1-5 2-3 0-7 1-9 2s-3 2-4 2c-2 1-2 1-3 1h-1c-3 1-7 3-10 5-1 1-2 3-4 4h-1l8-7c1-2 3-4 3-6v-1-1h1c1-1 1-1 1-3l-1-2c0-1-1-2-2-3h0v-1h0l1-1c-1-1-2-1-3-1h1v-1l4-2h0l3-3c1 0 1-1 2-1 0-2 1-2 2-4l4-4c0-2 1-3 2-4h0c4-7 6-17 4-25h0c-1-2-1-3-2-5v-1h1v-1c3 0 9 3 12 4 0 1 1 1 2 1-1 0-3-4-4-4-2-1-4 0-6-2 6 0 9 1 13 5h1v-1c-1-1-1-2-1-3v-1l-1-1v-1c-1-1-1-1-1-2s-1-2-1-3z" class="H"></path><path d="M222 597l4-2v2h2c-1 1-3 1-4 2-1-1-2-1-3-1h1v-1z" class="F"></path><path d="M226 606l1 1v3c0 1-2 4-3 5h-1l-2 2c1-2 3-4 3-6v-1-1h1c1-1 1-1 1-3z" class="S"></path><path d="M228 597c2-1 4-2 7-1v1l-2 2c-4 1-6 1-9 1h-1 0l1-1c1-1 3-1 4-2z" class="T"></path><path d="M224 600c3 0 5 0 9-1-3 4-4 7-6 11v-3l-1-1-1-2c0-1-1-2-2-3h0v-1h1z" class="G"></path><path d="M223 600h1c1 1 3 1 4 3 0 1 0 3-1 4l-1-1-1-2c0-1-1-2-2-3h0v-1z" class="F"></path><path d="M252 603h1 0l-1 1h2 1 2l-1 1h1 1c1-1 1 0 2-1h1 1l1-1h1 3l-12 3c-1 0-3 1-5 2h0c-2 1-4 1-5 2-3 0-7 1-9 2s-3 2-4 2c-2 1-2 1-3 1 0-1 1-2 2-2 7-3 15-6 21-10zm-6-51c2-1 5 0 7 2l2 1 1 1c4 3 5 6 6 11v1 7c-1 1 0 1 0 2-1 4-5 12-7 15 0-1 3-6 3-8 2-4 1-8 1-12l1-1v-5l-1-1c0-1 0-2-1-3-1-3-3-4-5-7h-1l1 2v1h-1c-1-2-2-3-3-5 0 4 3 7 2 10v-1c-1-1-1-1-1-2v-1c-1-1-1-3-2-4l-1-2v-1h-1z" class="B"></path><path d="M246 546c6 0 9 1 13 5 2 2 3 5 4 7 0 1 0 3 1 5 0 3 0 6-1 9l-1 5c0-1-1-1 0-2v-7-1c-1-5-2-8-6-11l-1-1-2-1c-2-2-5-3-7-2h-2-1v1 1h0c-1-2-1-3-2-5v-1h1v-1c3 0 9 3 12 4 0 1 1 1 2 1-1 0-3-4-4-4-2-1-4 0-6-2z" class="D"></path><path d="M246 546c6 0 9 1 13 5 2 2 3 5 4 7 0 1 0 3 1 5 0 0 0 2-1 2v-1c-2-5-4-8-7-12-1 0-3-4-4-4-2-1-4 0-6-2z" class="AK"></path><defs><linearGradient id="e" x1="274.898" y1="579.341" x2="249.86" y2="581.749" xlink:href="#B"><stop offset="0" stop-color="#d2c7be"></stop><stop offset="1" stop-color="#f4efeb"></stop></linearGradient></defs><path fill="url(#e)" d="M256 539l1-1c2 2 2 5 4 8 1 5 2 9 4 14 3 5 7 9 8 15 1 2 3 4 3 6v1c1 5 4 10 5 15l1 2c-1 1-1 1-3 1l-1 2h-2v-1c-3 1-6 2-9 2h-3-1l-1 1h-1-1c-1 1-1 0-2 1h-1-1l1-1h-2-1-2l1-1h0-1 0l1-1v-1c3-2 5-4 7-7v-1h1v-1c1-2 1-3 2-5 1-4 3-8 3-13-1-1-1-2-1-3v-1l-1 1-1 1c1-3 1-6 1-9-1-2-1-4-1-5-1-2-2-5-4-7h1v-1c-1-1-1-2-1-3v-1l-1-1v-1c-1-1-1-1-1-2s-1-2-1-3z"></path><path d="M276 601c2-1 2-1 3-1l-1 2h-2v-1zm-7-25c-1-2-1-5-1-7 4 5 6 10 7 17-1-1-1-1-1-2l-1-1c-1-3-2-6-3-8 0-2 0-2-1-3v4z" class="B"></path><path d="M269 576v-4c1 1 1 1 1 3 1 2 2 5 3 8l1 1c0 1 0 1 1 2 2 2 3 4 3 7l3 7c-2-1-3-3-4-5-2-3-5-6-6-10-1-3-2-6-2-9z" class="E"></path><path d="M759 574c1-2 1-9 3-10l1 1v5h0c0 5 1 9 3 14l3 8c2 5 5 10 10 14 3 2 9 6 13 6h0c2 2 4 3 6 5h-2v1h2-3 0-11c-7 1-14 3-19 9-3 3-4 7-5 12h0c0 4 0 8 1 12 3 8 8 15 9 24 1 3 1 7 1 10h1v1 5c-1 1 0 2-1 4v7l1 1h0c0 3 2 5 2 7-3-2-4-4-5-7-3-9 1-17-2-26l-3-6c-2-2-3-4-4-7-2-3-5-6-9-8l-1-2-5-2-6-3-1-1-4-2c-1 1-2 1-3 1v1c-1 0-3 0-4 1-2 0-3 1-4 3 0-2 0-3 1-5h0l-1-1v-1-1c0-6 3-7 5-12 1-2 3-4 5-6h1c1-2 2-2 2-4v-1h1 2v-2c1-1 1-2 1-2v-3h1c1-4 3-7 6-10 1-1 1-1 1-2l-1-1-3-2-1-1v-1h1c1 1 2 1 3 2l2-2-1-1c0-2 1-3 1-4v-1l1-1c0-3 0-3 2-5h1c0-3 3-4 4-7 1-1 1-3 2-4z" class="F"></path><path d="M736 621h1 2v1c0 1 1 1 0 2 0 1-1 1-2 1v1h-1v-1l-2 1c1-2 2-2 2-4v-1z" class="R"></path><path d="M736 622l2 1c0 1-1 2-2 2l-2 1c1-2 2-2 2-4z" class="y"></path><path d="M760 664c3 3 7 8 7 12v1l-3-6c-2-2-3-4-4-7z" class="T"></path><path d="M748 602c1 0 2 1 4 1h0 1c0 1 0 1-1 1l-1 1h-1c-1 1-2 2-3 2-3 4-5 7-6 12 0 1-1 2-2 3v-1-2c1-1 1-2 1-2v-3h1c1-4 3-7 6-10 1-1 1-1 1-2z" class="C"></path><path d="M734 626l2-1v1c-2 2-4 5-6 7-1 1-2 3-3 4-1 2-1 4-2 5 3 2 6 3 9 4-1 1-2 1-3 1v1c-1 0-3 0-4 1-2 0-3 1-4 3 0-2 0-3 1-5h0l-1-1v-1-1c0-6 3-7 5-12 1-2 3-4 5-6h1z" class="K"></path><path d="M738 627h1v-1h1c2-3 4-3 8-3-2 1-3 2-3 4h-2l-1 1h2v2c-2 1-2 2-2 4-1 1-1 3-1 4l-1 1v1h-1c0 1-1 3 0 4v1c1 2 1 2 3 3l-1 1c-1-1-3-1-4-3l-3-3c-1-1-1-3-1-5 1-4 3-8 5-11z" class="D"></path><path d="M733 638h1 1c1-2 1-4 2-6s2-3 5-4c-2 3-6 8-6 12 0 2 1 4 1 6l-3-3c-1-1-1-3-1-5z" class="AX"></path><path d="M738 627h1v-1h1c2-3 4-3 8-3-2 1-3 2-3 4h-2l-1 1h0c-3 1-4 2-5 4s-1 4-2 6h-1-1c1-4 3-8 5-11z" class="AP"></path><path d="M742 621c2-5 4-9 9-13 5-3 9-3 15-2 6 0 12 3 18 5v1h-3c-2-1-3-1-5-1-4-1-9-3-13-2h-1c-1 0-2 0-3 1s-1 0-2 1c-1 0-1 1-2 1-1 1-2 2-3 2v1h-1c-2 1-4 5-6 7l10 1-3 1c-1 1-2 1-3 1l-1 1h0 0v-2-1c-4 0-6 0-8 3h-1v1h-1 0l3-3v-1l1-1v-1z" class="R"></path><path d="M742 621c2-5 4-9 9-13 5-3 9-3 15-2h-3l1 1h-3c-2 0-1 0-2 1h-2c-6 2-8 6-11 12l-1 1c-1 1-2 1-3 0z" class="N"></path><defs><linearGradient id="f" x1="751.012" y1="593.63" x2="784.693" y2="590.575" xlink:href="#B"><stop offset="0" stop-color="#d8d2d0"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#f)" d="M759 574c1-2 1-9 3-10l1 1v5h0c0 5 1 9 3 14l3 8c2 5 5 10 10 14h0c2 2 5 3 8 5-1 0-2-1-2-1l-9-3h0v-1h0-2c-3-1-8-2-10-3l-1-2h-2-1l-1 1c-2 0-5-1-7 0v1h0c-2 0-3-1-4-1l-1-1-3-2-1-1v-1h1c1 1 2 1 3 2l2-2-1-1c0-2 1-3 1-4v-1l1-1c0-3 0-3 2-5h1c0-3 3-4 4-7 1-1 1-3 2-4z"></path><path d="M761 592c0 1-1 1-1 2-2 0-4 2-5 2l-2-2c3-1 6-2 8-2z" class="C"></path><path d="M761 592c1-2 2-2 3-2v1l-1 1c-1 1-2 3-2 4v1h-1v-3c0-1 1-1 1-2z" class="E"></path><path d="M752 585h1v2l-1-1c-1 1-1 3-1 5l1 1c0 1-1 1-2 2v1l-2 1c0-2 1-3 1-4v-1l1-1c0-3 0-3 2-5z" class="R"></path><path d="M759 574c1-2 1-9 3-10l1 1v5h-1c0 3-1 6 0 8v1 1c1 2 0 5 0 7v-1l-1 2-1-1h1l-1-1c-1-2 0-6-1-7v-5z" class="E"></path><path d="M755 596c1 0 3-2 5-2v3h1l1 2h-1l-1-1h-2c-1-1-2 0-3 0v1c1 1 3 1 5 2l-1 1c-2 0-5-1-7 0v1h0c-2 0-3-1-4-1l-1-1-3-2-1-1v-1h1c1 1 2 1 3 2l2-2-1-1 2-1 3-1 2 2z" class="G"></path><path d="M748 600c1-1 1-2 2-3 1 0 3-1 4-1l-1 1s-1 1-1 2h-1c-1 0-1 1-2 1h-1z" class="C"></path><path d="M750 595l3-1 2 2h-1c-1 0-3 1-4 1-1 1-1 2-2 3l-1-1 2-2-1-1 2-1z" class="l"></path><path d="M748 626h0l1-1c1 0 2 0 3-1l3-1-10-1c2-2 4-6 6-7h1v-1c1 0 2-1 3-2 1 0 1-1 2-1 1-1 1 0 2-1s2-1 3-1h1c4-1 9 1 13 2 2 0 3 0 5 1h3v-1c2 1 5 1 7 3l1-2h0c2 2 4 3 6 5h-2v1h2-3 0-11c-7 1-14 3-19 9-3 3-4 7-5 12h0c0 4 0 8 1 12 3 8 8 15 9 24-1-1-1-2-2-2v-1l-5-7-7-11c-1 0-1-1-2-2-2-4-4-7-6-11 0-1-1-2-1-3-1-1-1-3-2-4l-1-4v-2h-2l1-1h2c0-2 1-3 3-4v1 2h0z" class="B"></path><path d="M748 626h0c-2 4 0 6-1 9v3c-1-1-1-3-2-4 1-2 1-5 1-7l2-1zm8 28c1 1 2 1 2 3h1c-1-2 0-1-1-2s-1-2-1-3c2 4 6 9 6 13l-7-11z" class="D"></path><path d="M784 611c2 1 5 1 7 3l2 1c-2 1-3 0-5-1s-4-1-7-2h3v-1z" class="U"></path><path d="M748 623v1 2l-2 1c0 2 0 5-1 7l-1-4v-2h-2l1-1h2c0-2 1-3 3-4z" class="C"></path><path d="M704 636c1-2 4-3 5-5s0-1 1-2c2-2 3-4 5-7l2-2 2-4 2-2c1-2 2-4 3-5v-1-1c1 0 1-1 1-1 1-3 2-6 5-8l6-3h1c1-2 1-3 2-5v5c1 1 2 1 3 2h1v1l1 1 3 2 1 1c0 1 0 1-1 2-3 3-5 6-6 10h-1v3s0 1-1 2v2h-2-1v1c0 2-1 2-2 4h-1c-2 2-4 4-5 6-2 5-5 6-5 12v1 1l1 1h0c-1 2-1 3-1 5l-1 1 2 1-4 4h-1-1c0 1-1 1-2 2v2l1 1-2 4h0l-2 6h0-2c-1 2-2 4-2 6l-1 5c0 6-1 14 1 21 2 8 6 18 12 25 3 4 7 7 9 12-2-2-5-3-6-5 0-2-1-3-2-4-3-1-4-3-7-3-4-3-8-7-12-10-1-2-5-5-7-8-3-2-6-6-9-10l-1 2h-1v-1c-1-2-5-6-7-7-1-2-3-3-4-5l-1 1c-2-1-5-3-6-4-2-2-2-3-3-6 1-1 2-2 3-4s3-4 5-6c1-1 3-2 3-3l2-3c7-6 18-13 20-22 0-1 0-1 1-2h0c2-3 4-4 6-6z" class="F"></path><path d="M722 632v1 2h0c0 2-2 4-2 6-1 3-1 6-1 9v-1h-1c-1-6 1-12 4-17z" class="O"></path><path d="M704 636h0c2-1 3-3 5-4v1l1-1v1c-1 2-4 4-5 6-2 0-4 1-6 3h-1c2-3 4-4 6-6z" class="G"></path><path d="M732 622h4c0 2-1 2-2 4h-1l-4 1-2 3-5 5h0v-2-1c0-1 1-2 2-3 2-4 4-6 8-7z" class="b"></path><path d="M732 622h4c0 2-1 2-2 4h-1l-4 1 4-4-1-1z" class="o"></path><path d="M687 702l-3-3c-1-2-1-2-2-3 0-2 0-4 1-6 1 0 1 0 1-1l-3 1-1-2-1 1h-1-1-1v-1c-3 0-4-2-7-2v-1h-1l1-1c1 1 1 1 2 1s2 0 3 1h1 0l1-1h2l2-1v1l-1 1h1l2 3v-1l2-1s1 0 1-1c1 0 1 0 2 1 0 1-1 2-2 3 0 1-1 2-2 4l3 3v-3l2 5 6 9c1 1 2 3 2 4-3-2-6-6-9-10z" class="C"></path><path d="M743 598l1 1 3 2 1 1c0 1 0 1-1 2-3 3-5 6-6 10h-1v3s0 1-1 2v2h-2-1c-1-1-2-3-3-4 0-2-1-4 0-6 1-6 4-9 9-11l1-2z" class="L"></path><path d="M744 599l3 2c0 1 0 2-1 2-1 1-1 1-2 3l-1 1c-2 2-3 4-4 6h-1c1-5 3-6 7-10-1-1-1-2-1-4z" class="y"></path><path d="M747 601l1 1c0 1 0 1-1 2-3 3-5 6-6 10h-1v3s0 1-1 2v2h-2c2-1 0-6 1-8h1c1-2 2-4 4-6l1-1c1-2 1-2 2-3 1 0 1-1 1-2z" class="l"></path><path d="M733 617c0-2-1-4 0-6 1-6 4-9 9-11 1 0 1 1 1 2s0 1-1 2h-1c-2 1-4 4-4 6v4c-3 1 0 0-1 1-2 0-2 1-3 2z" class="o"></path><path d="M729 627l4-1c-2 2-4 4-5 6-2 5-5 6-5 12v1 1l1 1h0c-1 2-1 3-1 5l-1 1 2 1-4 4h-1-1c0 1-1 1-2 2-2 1-4 3-6 5-1 2-3 4-4 6l-1 1c-1 2-2 5-4 7l1 1v2h-1c-1 2-1 2-1 4l-2 1h0c-2-1-2-1-2-4v-2c1-11 9-20 17-26 2-1 5-2 6-3v-3 1c0-3 0-6 1-9 0-2 2-4 2-6l5-5 2-3z" class="L"></path><path d="M722 635l5-5c-4 7-7 11-6 19 1 1 0 2-1 3h0l1 2-3 3c-2-2-2-2-5-2 2-1 5-2 6-3v-3 1c0-3 0-6 1-9 0-2 2-4 2-6z" class="AK"></path><defs><linearGradient id="g" x1="703.119" y1="665.389" x2="708.35" y2="670.591" xlink:href="#B"><stop offset="0" stop-color="#837875"></stop><stop offset="1" stop-color="#9f948e"></stop></linearGradient></defs><path fill="url(#g)" d="M713 655c3 0 3 0 5 2-5 2-8 5-11 10l-1 1h0c-1 2-3 3-4 5v1c-1 2-1 3-1 4v1c-1 1-2 2-2 3l-2 2-1-1v-2c1-11 9-20 17-26z"></path><defs><linearGradient id="h" x1="681.652" y1="698.05" x2="686.299" y2="637.187" xlink:href="#B"><stop offset="0" stop-color="#b6b0ac"></stop><stop offset="1" stop-color="#eae3de"></stop></linearGradient></defs><path fill="url(#h)" d="M698 642h1c2-2 4-3 6-3-1 1-2 3-3 4-1 2-1 5-3 7l-12 12c-3 2-5 5-7 8-2 4-6 8-9 13h0-1l-1 1-1 1h1v1c3 0 4 2 7 2v1h1 1 1l1-1 1 2 3-1c0 1 0 1-1 1-1 2-1 4-1 6 1 1 1 1 2 3l3 3-1 2h-1v-1c-1-2-5-6-7-7-1-2-3-3-4-5l-1 1c-2-1-5-3-6-4-2-2-2-3-3-6 1-1 2-2 3-4s3-4 5-6c1-1 3-2 3-3l2-3c7-6 18-13 20-22 0-1 0-1 1-2h0z"></path><path d="M670 683c0-1 2-3 2-4 2-3 4-7 7-10v1h1c-2 4-6 8-9 13h0-1z" class="R"></path><path d="M698 687l2-1c0-2 0-2 1-4h1v-2l-1-1c2-2 3-5 4-7l1-1c1-2 3-4 4-6 2-2 4-4 6-5v2l1 1-2 4h0l-2 6h0-2c-1 2-2 4-2 6l-1 5c0 6-1 14 1 21 2 8 6 18 12 25 3 4 7 7 9 12-2-2-5-3-6-5 0-2-1-3-2-4-3-1-4-3-7-3-4-3-8-7-12-10-1-2-5-5-7-8 0-1-1-3-2-4l-6-9-2-5c-1-1-1-2 0-2 1-1 1-2 2-2 3 0 4-2 6-4l-1-1-1-1 1-1 1 1v3c-2 2-5 4-4 7 0 3 1 3 2 5h1v-2-3c1-2 2-3 4-4 1-1 1-1 1-3z" class="U"></path><path d="M711 673l1-1v-1-1c1-2 1-2 2-3h1l-2 6h0-2z" class="B"></path><path d="M697 690c0 2 0 3-1 5h0c0 3 0 4-1 6h0l-1 1c-1-3-1-6-1-8 1-2 2-3 4-4z" class="N"></path><path d="M697 695h1v13h-1c0-1-1-1-1-2-1-1-1-3-2-4l1-1h0c1-2 1-3 1-6h1z" class="D"></path><path d="M696 695h1v7c-1 1-1 3-1 4-1-1-1-3-2-4l1-1h0c1-2 1-3 1-6z" class="C"></path><path d="M698 687l2-1c0-2 0-2 1-4h1v-2l-1-1c2-2 3-5 4-7l1-1c1-2 3-4 4-6 2-2 4-4 6-5v2c-3 3-5 5-7 9-1 1-2 3-2 5-2 3-4 5-4 9l-1 1v1-1c-1 1-3 4-4 6v3h0-1-1 0c1-2 1-3 1-5 1-1 1-1 1-3z" class="l"></path><path d="M350 133c3-8 6-14 7-22 1-5 1-11 0-15-1-11-4-16-11-23v-1c2 2 5 4 7 6 4 2 6 5 9 8h0l5 5c3 5 8 9 13 13l4 4c7 5 15 10 25 9 3-1 6-3 8-6 1-2 2-4 2-5v1l1-1c1 1 0 3 1 5h0v3 1c-2 2-2 3-2 6h0v1h-1c0 3-2 4-4 6-2 1-6 2-8 3l-7 2c-10 5-20 8-29 13-2 1-4 2-5 4-1 1-2 1-3 2-2 1-4 3-6 3-3 3-6 4-9 6l-9 7-20 12c-6 5-14 9-17 16l-1 1c-3 4-4 8-8 12h-12v1h-14-11l-2-2c-4-1-8-4-12-6l1-1h4c12 2 28 2 38-6 3-3 6-7 7-12 0-6-5-10-9-15 8 4 16 6 24 4l1-1c3 0 8-2 11-5l1-1c3-2 3-3 4-6-1 0-1-1-1-2s-1-2-2-2v-1c1-1 1-1 2-1 1-1 2-1 3-1 2 0 5 0 8-1v-1l3-2c1 0 2-1 3-2h0c3-2 6-6 8-9h1l1-1c0-1 0-2 1-3h0z" class="F"></path><path d="M335 152c-1 1-2 1-3 2h-1c-2 1-6 1-8 0 1-1 4 0 6-1l6-1z" class="B"></path><path d="M333 150l3-2v1c-2 1-3 2-5 3h4 1-1l-6 1c-2 0-3 0-4-1 2 0 5 0 8-1v-1z" class="E"></path><path d="M358 87c1 0 2 1 3 2v1h-1c0 1 1 2 2 4l3 9 1 7c1 1 0 3 1 4 1 4 0 8-1 12 0 2 0 4-1 7l-1-1c1-4 2-7 2-11 1-12-3-24-8-34z" class="H"></path><path d="M350 133c3-8 6-14 7-22 1-5 1-11 0-15-1-11-4-16-11-23v-1c2 2 5 4 7 6v1c1 1 1 1 1 3 1 0 4 5 4 5 5 10 9 22 8 34 0 4-1 7-2 11v-1h0c1-9 1-16 0-24l-1-5c-1-3-2-6-3-8s-1-3-2-4v3c1 5 1 11 0 17s-2 12-5 18c-1 1-1 4-3 5z" class="D"></path><path d="M266 210l2-1c2-1 4 0 6 0 2-1 5-1 7-1 3 1 7 1 9 0h-4c-1 0-1 0-2-1h7l2-3c0-1 0-1 1-2 0-1 1-3 2-4 2-1 3-3 4-4l1-1c1 0 1-1 2-2h-1l2-2v1l2-2c3-1 5-4 8-6l11-7 2-2h1 0l1-1c1 0 1 0 2-1h1 0l2-2c1 0 2-1 3-2l3-2c-1-1-7 4-9 4l16-9c3-2 5-4 9-5-3 3-6 4-9 6l-9 7-20 12c-6 5-14 9-17 16l-1 1c-3 4-4 8-8 12h-12v1h-14z" class="G"></path><defs><linearGradient id="i" x1="415.887" y1="97.203" x2="356.747" y2="122.957" xlink:href="#B"><stop offset="0" stop-color="#dcd4ce"></stop><stop offset="1" stop-color="#fcfaf7"></stop></linearGradient></defs><path fill="url(#i)" d="M362 86l5 5c3 5 8 9 13 13l4 4c7 5 15 10 25 9 3-1 6-3 8-6 1-2 2-4 2-5v1l1-1c1 1 0 3 1 5h0v3 1c-2 2-2 3-2 6h0v1h-1c0 3-2 4-4 6-2 1-6 2-8 3l-7 2c-3 0-5 1-7 2h0c-1 0-2 1-3 1h0l-2 1c-1 1-1 0-1 1h-1 0l-6 3c-2 0-3 1-4 2h-1l-2 2h-1c-1 0-1 1-2 1l-1 1h0-1c2-2 5-3 7-6l3-9c1-2 1-7 0-10-1-2-1-4-2-7s-5-9-5-12c0-2 0-2-1-3l-3-4c0-1-1-2-1-2l-1-1c0-1-1-2-1-3-1-1-1-2-2-2l1-2z"></path><path d="M406 131c1-1 1-1 3-2 0-1 5-3 6-4 3-1 1-1 2-3h1c0 3-2 4-4 6-2 1-6 2-8 3z" class="G"></path><path d="M123 210c0 1 1 2 1 3 1 2 6 3 8 4l2 1 2 1c1 0 2 0 3 1v-2l3 1v1c4 0 7 3 11 4l15 7c3 1 5-1 8 1h0c2 1 3 1 5 2l4 1h0l1 1s1 0 2 1v-1h0c2 0 3 1 5 1h1c1 1 2 1 3 2 3 1 6 3 9 3l9 5c1 0 3 2 4 2 4 0 19 10 22 12 3-1 3-1 6 0 1 1 0 1 1 1s3 2 4 2c2 1 4 3 6 4 4 4 8 7 13 10l1-1-1-1h1l1-2 4 4c0 1 1 2 1 3 1 1 2 3 4 5v1l2-2 1 1v-1c0 1 1 1 1 1 1 2 0 3 2 4v-1c0 1 0 1 1 3l-1 11c-1 2-1 5-2 7 1 5 1 10 1 15l-1 4h2l-1 8-1 8c-1 5-3 11-3 16h-1v1c0 3 0 8-1 10-1-1-1-2-2-3-1-4-5-10-7-14-11-21-24-42-39-61l-1 1c-1 0-2-2-3-3-3-1-5-3-7-5h-1v-2c0-1 0-1-1-2l-3-3c-2 0-2 0-3-1s-2-2-2-3c-1-1-2-1-3-2h0c0 1 1 2 1 3l-6-6c0 1 1 6 2 7v2c2 3 3 7 4 10-2-1-2-3-4-5 0 1-1 2-1 3v1h-2l1-4 1-1-1-2c-1-1-1-2-2-3h-2-1c0-2 0-3-1-4h-1c0-2 1-1 2-2 0-1-1-2-1-3l-1-1h-3l-1 1-1-2v1l-2-2v-1c1-4 2-7 3-10-2-3-6-4-8-6-14-9-31-18-48-21-9-1-22 0-29 5s-9 11-10 19c0-2-1-3-2-5h0c-1-2 0-7 0-10 1-2 2-4 2-6l9-12 2-2c3 0 7-3 10-6h2l1-2h1z" class="V"></path><path d="M206 248l-1-1v-1l1-1 4 3h-4z" class="h"></path><path d="M216 269c-5-4-10-8-15-13 6 3 12 8 17 12h-2v1z" class="AD"></path><path d="M176 232c2 1 3 1 5 2l4 1h0-1-2c-1-1-2-1-3-1 1 1 2 1 3 2 1 0 0-1 2 0 2 2 6 4 9 5l6 3h-1l-6-2-15-8c-1 0-1-1-1-2z" class="M"></path><path d="M286 329h2l-1 8-1 8c-1 5-3 11-3 16h-1l4-32z" class="B"></path><path d="M252 296c3 4 8 10 9 15l3 3 7 9c1 1 2 2 2 4h0c-2-2-16-17-16-19 0-1-2-4-3-6 0-1-1-1-1-2-1-1-1-2-1-4z" class="AD"></path><path d="M288 289c0 1 0 1 1 3l-1 11c-1 2-1 5-2 7-1-7-3-12-7-18 3 1 5 3 6 5h1c0-1 0-2-1-4h1c1-1 1-2 2-3v-1z" class="U"></path><path d="M136 219c1 0 2 0 3 1v-2l3 1v1c4 0 7 3 11 4l15 7-3 1-6-3c-5-2-9-4-14-6-4-2-7-2-10-4h1z" class="h"></path><path d="M139 218l3 1v1h-3v-2z" class="M"></path><path d="M168 231c3 1 5-1 8 1h0c0 1 0 2 1 2l15 8c-1 0-1 0-2 1h0-1v1l-21-11-3-1 3-1z" class="I"></path><path d="M168 231c3 1 5-1 8 1h0c0 1 0 2 1 2-1 1-1 0-1 1 1 1 5 2 5 4-1-1-2-1-3-2l-6-3h-1l-3-1h0l-3-1 3-1z" class="AD"></path><path d="M123 210c0 1 1 2 1 3 1 2 6 3 8 4l2 1 2 1h-1c-8-2-14-4-22-1-8 4-13 11-15 19l-1 3v1c-1 2-1 5-1 7h0c-1-2 0-7 0-10 1-2 2-4 2-6l9-12 2-2c3 0 7-3 10-6h2l1-2h1z" class="B"></path><path d="M264 285c3 0 3 1 6 3 0 1 0 1 1 1 2 3 4 6 5 10h1c1 2 1 4 2 6 1 4 2 8 2 13l-3-5-2-4c-1-2-2-3-2-4-1-1-2-3-3-4v-1-1c-1-2-3-3-3-5-1-1-1-2-2-3-1-2-1-3-1-4l-1-2z" class="AD"></path><path d="M264 285c3 0 3 1 6 3 0 1 0 1 1 1 2 3 4 6 5 10h-1c-1 0-2-1-2-1-1-2-3-3-3-5-2-3-3-4-5-6l-1-2zm-72-43l6 2 2 2c3 1 5 3 8 5 1 1 2 1 3 2v2c1 1 2 1 3 2l9 6 3 2c2 2 6 4 8 7l1 1c2 0 2 1 3 2 2 1 2 2 3 3h1c2 3 6 4 7 7-3-1-6-5-9-7-1-1-3-2-4-3-2-1-3-2-4-3l-43-28v-1h1 0c1-1 1-1 2-1z" class="f"></path><path d="M200 246c3 1 5 3 8 5 1 1 2 1 3 2v2l-5-2-1-1-4-2c-1 0-2-1-2-2h-1v-1l4 2v-1l-2-2z" class="g"></path><defs><linearGradient id="j" x1="227.538" y1="269.487" x2="225.478" y2="280.925" xlink:href="#B"><stop offset="0" stop-color="#242b34"></stop><stop offset="1" stop-color="#40484e"></stop></linearGradient></defs><path fill="url(#j)" d="M216 269v-1h2l20 14c6 4 10 9 14 14 0 2 0 3 1 4 0 1 1 1 1 2 1 2 3 5 3 6-6-9-15-18-24-25-5-5-12-9-17-14z"></path><path d="M238 282c6 4 10 9 14 14 0 2 0 3 1 4 0 1 1 1 1 2-3-2-6-6-8-9-1-1-2-3-3-4-2-3-3-5-5-7z" class="g"></path><path d="M193 256l6 5c9 7 17 14 25 23 3 3 6 6 8 10h1l-1 1c-1 0-2-2-3-3-3-1-5-3-7-5h-1v-2c0-1 0-1-1-2l-3-3c-2 0-2 0-3-1s-2-2-2-3c-1-1-2-1-3-2h0c0 1 1 2 1 3l-6-6c0 1 1 6 2 7v2c2 3 3 7 4 10-2-1-2-3-4-5 0 1-1 2-1 3v1h-2l1-4 1-1-1-2c-1-1-1-2-2-3h-2-1c0-2 0-3-1-4h-1c0-2 1-1 2-2 0-1-1-2-1-3l-1-1h-3l-1 1-1-2v1l-2-2v-1c1-4 2-7 3-10z" class="b"></path><path d="M200 266c1 1 1 2 1 3h-1l-1-1v-1-1h1z" class="o"></path><defs><linearGradient id="k" x1="193.422" y1="260.844" x2="194.461" y2="267" xlink:href="#B"><stop offset="0" stop-color="#7e7775"></stop><stop offset="1" stop-color="#958f8e"></stop></linearGradient></defs><path fill="url(#k)" d="M193 256l6 5h-5-1c3 1 5 1 7 3v2h-1v1 1l-1 2-1-1h-3l-1 1-1-2v1l-2-2v-1c1-4 2-7 3-10z"></path><path d="M194 268l-1-1h0l4-2h0c-1 1-1 2-2 2l-1 1z" class="AQ"></path><path d="M195 267c1 0 1-1 2-2l2 1v1h-1l-1 1-2-1z" class="AP"></path><path d="M195 267l2 1 1-1h1v1l-1 2-1-1h-3v-1l1-1z" class="L"></path><path d="M201 269c0 2 2 4 3 5 0-2-1-5-1-7h1c5 3 8 8 12 12l3 2c3 1 4 3 6 6 2 2 4 5 7 7h1l-1 1c-1 0-2-2-3-3-3-1-5-3-7-5h-1v-2c0-1 0-1-1-2l-3-3c-2 0-2 0-3-1s-2-2-2-3c-1-1-2-1-3-2h0c0 1 1 2 1 3l-6-6c0 1 1 6 2 7v2c2 3 3 7 4 10-2-1-2-3-4-5 0 1-1 2-1 3v1h-2l1-4 1-1-1-2c-1-1-1-2-2-3h-2-1c0-2 0-3-1-4h-1c0-2 1-1 2-2 0-1-1-2-1-3l1-2 1 1h1z" class="AK"></path><path d="M199 268l1 1c0 2 0 3 1 5l2 2c1 1 2 2 2 3v1h1c2 3 3 7 4 10-2-1-2-3-4-5 0 1-1 2-1 3v1h-2l1-4 1-1-1-2c-1-1-1-2-2-3h-2-1c0-2 0-3-1-4h-1c0-2 1-1 2-2 0-1-1-2-1-3l1-2z" class="AP"></path><path d="M200 279h-1c0-2 0-3-1-4h-1c0-2 1-1 2-2v1c1 1 1 2 1 3l1 1h1c1 1 2 1 2 3h1v3l-1-2c-1-1-1-2-2-3h-2z" class="L"></path><path d="M219 249c4 0 19 10 22 12 3-1 3-1 6 0 1 1 0 1 1 1s3 2 4 2c2 1 4 3 6 4 4 4 8 7 13 10l1-1-1-1h1l1-2 4 4c0 1 1 2 1 3 1 1 2 3 4 5v1l2-2 1 1v-1c0 1 1 1 1 1 1 2 0 3 2 4-1 1-1 2-2 3h-1c1 2 1 3 1 4h-1c-1-2-3-4-6-5l-5-4h-3v1c-1 0-1 0-1-1-3-2-3-3-6-3h-1c-1-1-2-3-3-5-1-1-1-2-1-4-1 0-3-2-4-2l-7-6c-10-7-19-14-29-19z" class="l"></path><path d="M246 265c2 0 3 0 4 1s2 1 3 1h1c1 1 2 2 2 4-4-1-7-4-10-6z" class="O"></path><path d="M241 261c3-1 3-1 6 0 1 1 0 1 1 1s3 2 4 2c0 1 1 2 2 3h-1c-1 0-2 0-3-1s-2-1-4-1c-2-1-3-3-5-4z" class="AZ"></path><path d="M259 276l15 12h-3v1c-1 0-1 0-1-1-3-2-3-3-6-3h-1c-1-1-2-3-3-5-1-1-1-2-1-4z" class="g"></path><path d="M273 284c2 1 4 1 5 3 1 1 1 1 2 1v-1l2-1v1l2-2 1 1v-1c0 1 1 1 1 1 1 2 0 3 2 4-1 1-1 2-2 3h-1c-1 0-1 0-2-1l-1-1h-2l-1-1c-2-1-4-4-6-6z" class="j"></path><path d="M284 285l1 1v5c-1-1-2-3-3-4l2-2z" class="AY"></path><path d="M285 286v-1c0 1 1 1 1 1 1 2 0 3 2 4-1 1-1 2-2 3 0-1-1-2-1-2v-5z" class="l"></path><path d="M252 264c2 1 4 3 6 4 4 4 8 7 13 10l1-1-1-1h1l1-2 4 4c0 1 1 2 1 3 1 1 2 3 4 5l-2 1v1c-1 0-1 0-2-1-1-2-3-2-5-3h-1l-6-6c-3-2-7-5-10-7 0-2-1-3-2-4s-2-2-2-3z" class="AL"></path><path d="M266 278c2 1 3 1 5 2 1 1 2 2 4 2v-1c1 1 1 2 1 3h-1-3l-6-6z" class="O"></path><path d="M273 274l4 4c0 1 1 2 1 3 1 1 2 3 4 5l-2 1v1c-1 0-1 0-2-1-1-2-3-2-5-3h-1 3 1c0-1 0-2-1-3l-3-5 1-2z" class="b"></path><path d="M278 281c1 1 2 3 4 5l-2 1v1c-1 0-1 0-2-1 1-1-1-5 0-6z" class="L"></path><path d="M199 244c1 0 2 1 3 2 2 0 3 2 4 2v1c1 0 2 1 2 1v-1c-1 0-2-1-2-1h4c6 2 11 6 16 9 7 5 14 10 19 17l2 1c3 2 10 6 11 10 1 0 2 1 3 2l1 1c1 1 2 4 4 5 2 4 4 7 6 10 0 1-1 2-1 3 1 3 4 4 4 7v1c1 1 1 1 1 2h0c1 2 0 4 1 6 1 1 1 3 1 4l2 6v1h1v2 1l-1-1v-1s0-1-1-1v-1l-1-4-1-3-3-5c-3-6-6-11-9-16l-2-2-6-8c-1-1-2-2-3-4-1-1-5-4-5-5-1-3-5-4-7-7h-1c-1-1-1-2-3-3-1-1-1-2-3-2l-1-1c-2-3-6-5-8-7l-3-2-9-6c-1-1-2-1-3-2v-2c-1-1-2-1-3-2-3-2-5-4-8-5l-2-2h1z" class="z"></path><path d="M247 275c1 3 3 5 5 7-5-2-9-8-14-11-1 0-1-1-2-1l1-1 1 1c3 1 5 3 7 4l2 1z" class="r"></path><path d="M518 93h1l1 5c0 2-1 4-1 5l-1 5c-1 3-1 5-2 7-1 3-3 5-4 7-2 2-3 4-4 6l-3 3v1c-2 1-4 2-5 3-3 2-5 5-8 5 1 0 2 0 3 1 2 1 2 1 3 3h-2c-7-1-14 4-19 9-1 1-3 2-3 4h0 1l-7 1c-1 1-2 1-2 2-1 1-1 2-1 3l1 1-2 1c1 1 1 1 3 2 0 1 1 1 1 2l-1 1h0c-2 1-3 1-5 2l-9 2h-4v-1h-1c-1 0-1 1-2 1h-2c-1-1-2-1-4-1-1 0-1 0-2-1l1-2-2 1c-1 1-2 2-4 1l-2 3-1-2h0l-1-1 1-3-2 3c0 1-1 1-1 2l-1-1s-1 1-1 2l-2 9-1-1v-1-1c-4 2-9 7-13 8l-3 3h-1l-5 4-8 9c-2 2-3 4-6 4h-1-1-8-20-9-48-7c4-4 5-8 8-12l1-1c3-7 11-11 17-16l20-12 9-7c3-2 6-3 9-6 2 0 4-2 6-3 1-1 2-1 3-2 1-2 3-3 5-4 9-5 19-8 29-13l7-2c2-1 6-2 8-3 2-2 4-3 4-6h1v-1h0c0-3 0-4 2-6v-1-3h0c-1-2 0-4-1-5l-1 1v-1-2h1c1 2 1 5 2 8 0 1 0 2 1 3s2 3 4 3v1c1 1 2 1 4 2 3 1 5-1 8 1h0c5-1 7-3 9-7l2 2-1 3c2 0 3 0 5-1l21-3c3-1 6-2 8-1l1 1c1-1 1-2 2-3h-4l3-1c3-2 4-2 5-5v-1h2v1l3 3c8-4 14-7 20-14l3-3z" class="F"></path><path d="M450 135h0v-1h2v-1c2 0 3-1 5-1 1-1 3-1 4-2 2-1 5 0 7 0 1-1 3-2 5-3 1-1 2-2 3-1-2 1-5 3-7 4-6 3-13 3-18 5h-1z" class="G"></path><path d="M384 157h1v1h0c-3 3-8 5-12 7-5 3-9 7-14 9l-1 1-1-1 27-17z" class="B"></path><path d="M419 152h1l2 2c-1 3-4 4-5 5l-1 1h1c1 0 3-1 5-2-1 1-2 2-4 3-1 0-2 1-3 2s-3 1-4 3c-3 1-5 2-8 1 3-2 8-4 10-7l2-1c1-1 4-2 4-3 0-2-1-2 0-3v-1z" class="P"></path><path d="M419 152c-3 1-7 3-10 5-2 1-5 5-7 5 1-1 5-5 7-6h0c2-1 3-3 5-3l1-1v-1h-1c1-2 3-3 4-4h3c1-1 2-1 3-1l1-1 2 1v-1l1 1 1-1h1v-1h1 1 1v-1l1 1 1-1h1l1-1 1 1v-1h1l1-1 1 1 1-1c2 0 2-1 4-1l1 1c-10 2-22 5-31 10l1 1h1c1-1 2 0 4 0h-2-1z" class="R"></path><path d="M486 113c0 1 0 1-1 2l-1 2h1l-1 1c-1 1-1 2-2 4 0 1-1 2-2 4h0c-2 4-9 7-14 8-2 1-4 2-7 2l-2 1-1 1-9 3-1-1h1 1v-1h2-2l1-1h1s1-1 3-1h-3-1l-4 2-1-1c-2 0-3 1-5 2h-3c-1 1-3 1-4 2-3 1-4 2-7 2 0 1-1 0-2 1h-2c1-2 3-1 4-2 2 0 3-1 5-2 0 0 1-1 3-1h1c1-1 1-1 3-2l1 1 1-1h1c1-1 3-2 4-2v1c2 0 4-1 6-2h1c5-2 12-2 18-5 2-1 5-3 7-4l2-1c0-1 1-1 2-1h0l1-1c1-1 1-1 1-2l-1-1 1-2c-1 1-2 1-3 1l5-3c1-1 1-2 2-3z" class="T"></path><path d="M453 137c1 0 2 0 3-1 4-2 9-3 13-4 1-1 1-1 2-1h1l1-1 3-2 2-1s1-1 2-1c-2 4-9 7-14 8-2 1-4 2-7 2l-2 1-1 1-9 3-1-1h1 1v-1h2-2l1-1h1s1-1 3-1zm-50 30c3 1 5 0 8-1-5 5-12 7-18 12-5 4-9 8-14 12-3 3-7 5-11 8-2 2-5 6-7 8l-2-1c-1 2-2 3-3 4h-9 5l3-3c2-3 5-6 8-9 4-3 9-5 13-9 3-2 6-5 9-8 5-5 12-9 18-13z" class="C"></path><path d="M480 126c2-1 3-1 4-1-1 1-2 2-2 4 1 0 3-1 4 0h0 1c2-1 4-3 6-3-2 1-3 1-4 2l-1 1-6 3h-1c-1 1-2 1-3 2l-9 4-33 9c-5 1-9 2-13 5h-1c-2 0-3-1-4 0h-1l-1-1c9-5 21-8 31-10l9-3 1-1 2-1c3 0 5-1 7-2 5-1 12-4 14-8h0z" class="H"></path><path d="M480 126c2-1 3-1 4-1-1 1-2 2-2 4 1 0 3-1 4 0-9 4-20 8-30 9l1-1 2-1c3 0 5-1 7-2 5-1 12-4 14-8h0z" class="l"></path><defs><linearGradient id="l" x1="511.882" y1="106.764" x2="497.05" y2="104.118" xlink:href="#B"><stop offset="0" stop-color="#8c827d"></stop><stop offset="1" stop-color="#c0b6ac"></stop></linearGradient></defs><path fill="url(#l)" d="M515 96c0 1 0 2-1 3v2h1c-1 6-7 13-11 17l-7 6c-2 1-3 2-5 4h0l-9 5-5 1c1-1 2-1 3-2h1l6-3 1-1c1-1 2-1 4-2-2 0-4 2-6 3h-1 0c-1-1-3 0-4 0 0-2 1-3 2-4-1 0-2 0-4 1 1-2 2-3 2-4 1-2 1-3 2-4l1-1h-1l1-2c1-1 1-1 1-2h-4l3-1c3-2 4-2 5-5v-1h2v1l3 3c8-4 14-7 20-14z"></path><path d="M499 111h2c1 1 1 0 1 1l-1 1-2-2z" class="C"></path><path d="M496 112l3-1 2 2-2 2h-1v-1c-1-1-2-1-2-2z" class="T"></path><path d="M501 113l1-1 1 1c0 2-2 5-4 6v-4l2-2z" class="K"></path><path d="M496 111v1c0 1 1 1 2 2v1h1v4l-3 2c-1 1-3 2-4 2l1-1c1-1 3-2 4-4 0-1 0-2-1-3l-2-3 2-1z" class="C"></path><path d="M514 101h1c-1 6-7 13-11 17l-7 6c-2 1-3 2-5 4h0l-9 5-5 1c1-1 2-1 3-2h1l6-3 1-1c1-1 2-1 4-2-2 0-4 2-6 3h-1l3-3h1c10-4 20-15 24-25z" class="E"></path><path d="M482 113l3-1c3-2 4-2 5-5v-1h2v1l3 3 1 1-2 1 2 3c1 1 1 2 1 3-1 2-3 3-4 4l-1 1-3 3-3 3h0c-1-1-3 0-4 0 0-2 1-3 2-4-1 0-2 0-4 1 1-2 2-3 2-4 1-2 1-3 2-4l1-1h-1l1-2c1-1 1-1 1-2h-4z" class="G"></path><path d="M485 124l5-11h1c-1 4-3 8-5 12l-1-1z" class="E"></path><path d="M492 111c1 0 1 0 2 1l2 3c1 1 1 2 1 3-1 2-3 3-4 4l-4 3h-1 0l2-5c1-1 1-3 2-4v-5z" class="D"></path><path d="M492 111c1 0 1 0 2 1l2 3c0 1-1 2-2 4h-1c0-1-1-2-1-3v-5z" class="H"></path><path d="M482 113l3-1c3-2 4-2 5-5v-1h2v1l3 3 1 1-2 1c-1-1-1-1-2-1-1 1-1 1-1 2h-1l-5 11-1 1c-1 0-2 0-4 1 1-2 2-3 2-4 1-2 1-3 2-4l1-1h-1l1-2c1-1 1-1 1-2h-4z" class="P"></path><path d="M492 106v1l3 3 1 1-2 1c-1-1-1-1-2-1-1 1-1 1-1 2h-1c0-2 0-4 2-7z" class="D"></path><path d="M492 107l3 3 1 1-2 1c-1-2-1-3-2-5z" class="K"></path><defs><linearGradient id="m" x1="406.505" y1="190.545" x2="380.23" y2="174.333" xlink:href="#B"><stop offset="0" stop-color="#c7c1bf"></stop><stop offset="1" stop-color="#ebe6e2"></stop></linearGradient></defs><path fill="url(#m)" d="M422 152h1l1 1-1 1c1 2 3 2 3 4 1 1 0 4 0 5l1 2h-2c0 1 1 2 1 3v5s-1 1-1 2l-2 9-1-1v-1-1c-4 2-9 7-13 8l-3 3h-1l-5 4-8 9c-2 2-3 4-6 4h-1-1-8-20c1-1 2-2 3-4l2 1c2-2 5-6 7-8 4-3 8-5 11-8 5-4 9-8 14-12 6-5 13-7 18-12 1-2 3-2 4-3s2-2 3-2c2-1 3-2 4-3-2 1-4 2-5 2h-1l1-1c1-1 4-2 5-5l-2-2h2z"></path><path d="M422 152h1l1 1-1 1c1 2 3 2 3 4 1 1 0 4 0 5l1 2h-2c-1 1-2 1-3 2 0 1-1 2-2 2l-4 2 1-2c1-1 3-2 4-3 0-1 0-2 1-2l2-1h-1l1-2v-3h-1-1c-2 1-4 2-5 2h-1l1-1c1-1 4-2 5-5l-2-2h2z" class="j"></path><path d="M382 200l5-5c0 3-2 4-4 7 0 0 0 2-1 2-1 1-1 1-1 2l-1 1c-2 0-3 0-4 1v1h-20c1-1 2-2 3-4l2 1v1h1 10c1 0 2-1 3-1 3-2 5-4 7-6z" class="D"></path><path d="M382 200l5-5c0 3-2 4-4 7 0 0 0 2-1 2-1 1-1 1-1 2l-1 1c-2 0-3 0-4 1h-1c0-1 0-1 1-1 1-1 0-1 1-1 1-3 4-3 5-6z" class="C"></path><path d="M387 195l3-2c2-3 6-5 8-8v-1c1-1 2-1 3-3s4-4 6-6c3-2 6-5 10-6l-1 2 4-2c-1 2-2 3-4 5-1 0-2 1-3 2h0c-1 1-2 1-3 1l-3 3-3 2-2 1a57.31 57.31 0 0 0-11 11c-2 2-3 4-5 6l-1 1s-1 1-2 1c2-3 4-4 4-7z" class="K"></path><path d="M402 183l1-2c2-1 4-4 6-5l1 1-3 3-3 2-2 1z" class="N"></path><path d="M416 171l4-2c-1 2-2 3-4 5-1 0-2 1-3 2h0c-1 1-2 1-3 1l-1-1 7-5z" class="L"></path><defs><linearGradient id="n" x1="397.474" y1="183.567" x2="404.036" y2="197.517" xlink:href="#B"><stop offset="0" stop-color="#a19c9b"></stop><stop offset="1" stop-color="#c1b9b6"></stop></linearGradient></defs><path fill="url(#n)" d="M420 169c1 0 2-1 2-2 1-1 2-1 3-2 0 1 1 2 1 3v5s-1 1-1 2l-2 9-1-1v-1-1c-4 2-9 7-13 8l-3 3h-1l-5 4-8 9c-2 2-3 4-6 4h-1-1-8v-1c1-1 2-1 4-1l1-1c0-1 0-1 1-2 1 0 1-2 1-2 1 0 2-1 2-1l1-1c2-2 3-4 5-6a57.31 57.31 0 0 1 11-11l2-1 3-2 3-3c1 0 2 0 3-1h0c1-1 2-2 3-2 2-2 3-3 4-5z"></path><path d="M404 182l3-2h0c0 1 0 1 1 1l-3 2-1-1z" class="L"></path><path d="M416 181l1-4c0-1 1-2 3-2-1 2-1 3-3 5l-1 1z" class="o"></path><path d="M413 176h0c1-1 2-2 3-2h2c-2 2-3 3-5 4v-1-1z" class="AK"></path><path d="M380 207h2c1-1 3-1 5-1v-1c1-1 2-1 3-1-2 2-4 3-5 5h-1-8v-1c1-1 2-1 4-1z" class="N"></path><path d="M420 169c1 0 2-1 2-2 1-1 2-1 3-2 0 1 1 2 1 3v5s-1 1-1 2l-2 9-1-1v-1-1c-4 2-9 7-13 8l-3 3h-1l-5 4-8 9c-2 2-3 4-6 4h-1c1-2 3-3 5-5 8-8 16-17 26-23l1-1c2-2 2-3 3-5l-1-1h-1-2c2-2 3-3 4-5z" class="O"></path><path d="M409 189c3-3 5-5 8-7 2-1 5-3 6-4l1-3h1l-2 9-1-1v-1-1c-4 2-9 7-13 8z" class="M"></path><path fill="#fff" d="M419 106v-2h1c1 2 1 5 2 8 0 1 0 2 1 3s2 3 4 3v1c1 1 2 1 4 2 3 1 5-1 8 1h0c5-1 7-3 9-7l2 2-1 3c2 0 3 0 5-1l21-3c3-1 6-2 8-1l1 1-5 3c-1 0-2 0-3 1-9 3-19 3-28 5-3 0-5 1-8 2l-22 6c-26 8-50 23-73 37-15 10-32 19-42 34-1 1-3 2-4 3v2h-7c4-4 5-8 8-12l1-1c3-7 11-11 17-16l20-12 9-7c3-2 6-3 9-6 2 0 4-2 6-3 1-1 2-1 3-2 1-2 3-3 5-4 9-5 19-8 29-13l7-2c2-1 6-2 8-3 2-2 4-3 4-6h1v-1h0c0-3 0-4 2-6v-1-3h0c-1-2 0-4-1-5l-1 1v-1z"></path><path d="M419 106v-2h1c1 2 1 5 2 8 0 1 0 2 1 3s2 3 4 3v1c1 1 2 1 4 2 3 1 5-1 8 1h0c5-1 7-3 9-7l2 2-1 3c-4 1-9 2-14 4-16 4-33 9-49 16l-14 6c-2 1-4 3-7 4 1-2 3-3 5-4 9-5 19-8 29-13l7-2c2-1 6-2 8-3 2-2 4-3 4-6h1v-1h0c0-3 0-4 2-6v-1-3h0c-1-2 0-4-1-5l-1 1v-1z" class="C"></path><path d="M422 112c0 1 0 2 1 3s2 3 4 3v1c1 1 2 1 4 2 3 1 5-1 8 1l-7 2-13 3c3-5 3-10 3-15z" class="H"></path><path d="M518 93h1l1 5c0 2-1 4-1 5l-1 5c-1 3-1 5-2 7-1 3-3 5-4 7-2 2-3 4-4 6l-3 3v1c-2 1-4 2-5 3-3 2-5 5-8 5 1 0 2 0 3 1 2 1 2 1 3 3h-2c-7-1-14 4-19 9-1 1-3 2-3 4h0 1l-7 1c-1 1-2 1-2 2-1 1-1 2-1 3l1 1-2 1c1 1 1 1 3 2 0 1 1 1 1 2l-1 1h0c-2 1-3 1-5 2l-9 2h-4v-1h-1c-1 0-1 1-2 1h-2c-1-1-2-1-4-1-1 0-1 0-2-1l1-2-2 1c-1 1-2 2-4 1l-2 3-1-2h0l-1-1 1-3-2 3c0 1-1 1-1 2l-1-1v-5c0-1-1-2-1-3h2l-1-2c0-1 1-4 0-5 0-2-2-2-3-4l1-1-1-1c4-3 8-4 13-5l33-9 9-4 5-1 9-5h0c2-2 3-3 5-4l7-6c4-4 10-11 11-17h-1v-2c1-1 1-2 1-3l3-3z" class="AT"></path><path d="M471 146l6-4 1 1c-3 3-6 5-9 7-2-1-2-1-3 0h-2l7-4z" class="P"></path><path d="M464 150h2c1-1 1-1 3 0-6 4-12 7-17 12l-4 2 2-3 1-1-1-1 6-3c3-2 5-4 8-6z" class="D"></path><path d="M448 164l4-2-2 3-1 1c-1 0-1 1-2 2l1 1 3-3h2l-3 5-1 2h-1c-1 0-1 1-2 1h-2c-1-1-2-1-4-1-1 0-1 0-2-1l1-2c3-1 6-4 9-6z" class="B"></path><path d="M492 140c1 0 2 0 3 1 2 1 2 1 3 3h-2c-7-1-14 4-19 9-1 1-3 2-3 4h0 1l-7 1c-2 0-4 1-6 1 2-3 6-5 8-7 7-5 13-9 22-12z" class="o"></path><path d="M495 141c2 1 2 1 3 3h-2c-7-1-14 4-19 9-1 1-3 2-3 4h0-3v-1c2-1 3-3 5-4l1-1h0l2-2c2-2 10-6 13-6h1v-1c1 0 1 0 2-1z" class="AX"></path><path d="M462 159c2 0 4-1 6-1-1 1-2 1-2 2-1 1-1 2-1 3l1 1-2 1c1 1 1 1 3 2 0 1 1 1 1 2l-1 1h0c-2 1-3 1-5 2l-9 2h-4v-1l1-2 3-5h1c2-3 5-5 8-7z" class="AH"></path><path d="M458 168c2 0 3-1 4-2v-2l1-1 1 2c1 1 1 1 3 2 0 1 1 1 1 2l-1 1h-2v-1-1h-4c-1 1-2 1-3 1h-1l1-1z" class="X"></path><path d="M454 166h2c1-1 1-2 2-1s0 2 0 3l-1 1c-2 1-3 1-5 3-1 0-1 0-2-1l3-5h1z" class="AN"></path><path d="M462 159c2 0 4-1 6-1-1 1-2 1-2 2-1 1-1 2-1 3l1 1-2 1-1-2-1 1v2c-1 1-2 2-4 2 0-1 1-2 0-3s-1 0-2 1h-2c2-3 5-5 8-7z" class="AR"></path><path d="M518 93c-2 5-2 10-4 15-6 18-21 25-36 35l-1-1c4-2 6-4 10-6h-1-2l2-1-1-1c2-1 3-2 5-3 1-1 2-2 2-3h0c2-2 3-3 5-4l7-6c4-4 10-11 11-17h-1v-2c1-1 1-2 1-3l3-3z" class="AP"></path><path d="M504 118c-1 3-3 5-5 8-1 2-3 4-5 5 1-2 2-3 4-5h-1v-2l7-6z" class="l"></path><path d="M492 128h0c2-2 3-3 5-4v2h1c-2 2-3 3-4 5-2 2-5 4-7 5h-1-2l2-1-1-1c2-1 3-2 5-3 1-1 2-2 2-3z" class="C"></path><path d="M492 128c0 1-1 2-2 3-2 1-3 2-5 3l1 1-2 1h2 1c-4 2-6 4-10 6l-6 4-7 4c-3 2-5 4-8 6l-6 3 1 1-1 1-2 3c-3 2-6 5-9 6l-2 1c-1 1-2 2-4 1l-2 3-1-2h0l-1-1 1-3-2 3c0 1-1 1-1 2l-1-1v-5c0-1-1-2-1-3h2l-1-2c0-1 1-4 0-5 0-2-2-2-3-4l1-1-1-1c4-3 8-4 13-5l33-9 9-4 5-1 9-5z" class="N"></path><path d="M477 140v2l-6 4h0v-1l-1-1h0l7-4z" class="S"></path><path d="M485 134l1 1-2 1h2 1c-4 2-6 4-10 6v-2l8-6z" class="G"></path><path d="M470 144l1 1v1h0l-7 4c-3 2-5 4-8 6l-6 3 1 1-1 1-2 3c-3 2-6 5-9 6l-2 1c-1 1-2 2-4 1l-2 3-1-2h0c2-1 4-3 5-5 11-10 23-17 35-24z" class="H"></path><path d="M433 172c2-1 4-3 5-4 0 1 0 2-1 3h0c-1 1-2 2-4 1z" class="l"></path><path d="M450 159l1 1-1 1-2 3c-3 2-6 5-9 6l-2 1h0c1-1 1-2 1-3 1-1 2-1 3-2 3-2 6-5 9-7z" class="K"></path><path d="M478 134l5-1c-4 3-8 5-12 7-5 3-9 5-13 8-6 4-11 10-17 13-1 1-3 3-5 4l-2 2 1 1c-1 2-3 4-5 5l-1-1 1-3 2-2c3-5 8-9 12-12l5-4c2-2 5-3 7-5 4-2 9-5 13-8l9-4z" class="C"></path><path d="M432 167c1-1 2-1 3-2l2-1c1-1 2-2 4-3-1 1-3 3-5 4l-2 2 1 1c-1 2-3 4-5 5l-1-1 1-3 2-2z" class="K"></path><defs><linearGradient id="o" x1="439.214" y1="144.352" x2="445.789" y2="159.153" xlink:href="#B"><stop offset="0" stop-color="#d5cac3"></stop><stop offset="1" stop-color="#f8f5f3"></stop></linearGradient></defs><path fill="url(#o)" d="M423 152c4-3 8-4 13-5l33-9c-4 3-9 6-13 8-2 2-5 3-7 5l-5 4c-4 3-9 7-12 12l-2 2-2 3c0 1-1 1-1 2l-1-1v-5c0-1-1-2-1-3h2l-1-2c0-1 1-4 0-5 0-2-2-2-3-4l1-1-1-1z"></path><path d="M424 153c2 0 4 1 5 3s1 5 0 7c0 1-1 1-2 2l-1-2c0-1 1-4 0-5 0-2-2-2-3-4l1-1z" class="AX"></path><path d="M426 168h1c1 0 2-1 3-2 2-3 6-8 9-9l1-1c2-1 2-1 4-1-4 3-9 7-12 12l-2 2-2 3c0 1-1 1-1 2l-1-1v-5z" class="AT"></path><path d="M519 93l6 7 1 1 2 2 3 2 3 2 10 5h-1l8 3 8 2 4-1c1 2 1 2 2 2l3 1-2 1 7 2v-1h3l1 1 1-2c1 2 1 3 2 4 2 1 5 2 6 3l7 2 13 4c4 1 9 3 13 5 10 3 19 8 28 13l25 15c4 3 33 24 34 24l18 16h1l3 4 10-1h2 0 16c4 0 8-1 12 0-2 2-5 2-8 2v1l2 1c-1 0-3-1-5 0h-2c0-1 0-1-1-2h-1l1 2-2 2c-2 2-4 3-6 5l-1 2-2 2c-5 2-12-1-16-1-3-1-6-1-9-1v2h7v1l13 2-12 3c-2 0-4 2-6 2-3 1-5 1-7 2-6 0-11 0-16-2v-1l-3-1c0 1-1 0-1 0v-1c-1 0-2-1-3-1-4-1-7-1-11-2l-1-1-3-1h0l-3-1-7-2c-2 0-6-1-8-2 1 0 2-1 4 0h3 0 3 2s-1-1-2-1h0-3c-1 0-3 0-4-1h-1-7l-2-1v-1c-2-1-6 0-9-1-1-1-3-1-5-2h4c-5-1-11 0-16 0-1-1-6-1-8-1h-22c-6 0-12 0-18-1v-1h2v-2l3 1 2-1c2 0 2 0 3-1l-2-2c0-1-1-2-1-4v-2-1h0c0-2-1-4-1-6v-1l1-1 2 1s1-1 2-1 1 1 3 0c2 1 3 2 4 4 1 0 1-1 2-1s1 0 2-1v-2c-1-1-1-2-1-3-1-2-2-3-3-4l-1-1c0-1-1-2-2-2l-3-3-3-1c-1 1-2 2-2 3-1 1-2 1-2 2l-2-4v-2c-1-1-2-3-3-5-3 0-4-3-7-3l-2-2v-1l1-1h0c-1-1-3-1-4-1v-1c-1-3-10-9-13-11l-1-1c-2-1-3-2-5-2h-2-1c-1 0-2 0-3 1l-1 1v-1h-1c1-2 1-2 1-4h-1l1-3 2-2v-1c-1 0-3 0-4 1l-6-2c3-1 5-1 8-1v-1c-2 0-4-1-6-1h-1l2-1c-2-1-4-2-5-3l-2-3-2-2h0l-4 1v-2l-1-1-1-8c1-1 0-4 0-6l1-5c0-1 1-3 1-5l-1-5z" class="F"></path><path d="M664 188h1c5 4 10 8 14 12 1 1 2 2 2 3-3-1-5-4-8-6-3-3-6-6-10-9h1z" class="D"></path><path d="M612 159h1c1 2 2 3 4 3 1 1 2 2 3 4v2 1c0 1 4 4 5 5-2-1-3-1-4-2v-1l-5-3c0-2-1-4-1-5-2-2-2-2-3-4z" class="C"></path><path d="M724 206v1l-2-1c-2-2-4-5-7-7-2-1-3-3-5-4l-16-12-5-3-1-1c-6-5-14-11-21-15l-2-1-15-9c-6-3-12-8-18-10l-16-7-14-4-5-2-14-5c-2-1-4-1-6-2l-19-6-3-1c-1-1-2-1-3-1h-1-1-4l1-1h4l8 2 4-1c1 2 1 2 2 2l3 1-2 1 7 2v-1h3l1 1 1-2c1 2 1 3 2 4 2 1 5 2 6 3l7 2 13 4c4 1 9 3 13 5 10 3 19 8 28 13l25 15c4 3 33 24 34 24l18 16z" class="H"></path><path d="M559 117l4-1c1 2 1 2 2 2l3 1-2 1-7-3z" class="F"></path><path d="M578 120c1 2 1 3 2 4l-7-2v-1h3l1 1 1-2z" class="E"></path><path d="M525 100l1 1 2 2 3 2 3 2 10 5h-1l-3-1c0 1 0 3 1 4 3 4 2 9 7 12v1l2 2h5 0c2 0 4 2 5 2 3 0 5 1 7 2h1c2 0 4 2 6 2l1 1h1c1 0 2 1 3 1 1 1 2 1 4 2 1 0 3 1 4 1 1 1 1 1 2 1h0c1 0 1 1 2 1h0c2 1 4 1 5 2 1 0 2 1 3 2 1 0 2 0 3 1 4 1 8 4 12 5 2 1 3 2 5 3l10 6c2 1 3 2 5 3h0l5 3c-2 0-2 0-3-1s-2-1-3-1c-2-1-3-2-5-4-5-3-12-6-18-9-1 0-2-1-3-1l-6-3c-3-2-7-3-10-5l-32-9c-2-1-3-2-5-1-2-1-4-2-5-3l-2-1c-1 0-5-3-6-3-5-4-11-10-14-15l-1-1-1-2h1 0l1-1c-1-1-1-2-2-2v-1-1-4z" class="B"></path><path d="M541 127l1-2c1 1 1 0 2 1 1 2 3 3 5 5h0l-2-1c-1 0-5-3-6-3z" class="D"></path><defs><linearGradient id="p" x1="527.209" y1="116.051" x2="539.916" y2="116.784" xlink:href="#B"><stop offset="0" stop-color="#998d86"></stop><stop offset="1" stop-color="#c6bdb4"></stop></linearGradient></defs><path fill="url(#p)" d="M525 100l1 1 2 2 3 2 3 2 10 5h-1l-3-1c-1 0-2-1-3-1 0 1 3 6 4 8 0 2 3 7 3 8-1-1-1 0-2-1l-1 2c-5-4-11-10-14-15l-1-1-1-2h1 0l1-1c-1-1-1-2-2-2v-1-1-4z"></path><path d="M537 114c1 2 1 4 1 6l-2-2c0-1 1-3 1-4z" class="K"></path><path d="M525 100l1 1c0 2 0 3 1 4v1c0 2 1 3 1 5l-1 1-1-1-1-2h1 0l1-1c-1-1-1-2-2-2v-1-1-4z" class="AK"></path><path d="M528 103l3 2c1 1 1 1 1 2h1v1l-1 1h0v1l-3-2c-1-1-1-4-1-5z" class="AX"></path><path d="M534 107l10 5h-1l-3-1c-1 0-2-1-3-1 0 1 3 6 4 8l-1 1v3l-2-2c0-2 0-4-1-6 0 0-1-1-1-2v-1l-2-4z" class="P"></path><defs><linearGradient id="q" x1="565.948" y1="161.117" x2="572.61" y2="142.723" xlink:href="#B"><stop offset="0" stop-color="#baaca3"></stop><stop offset="1" stop-color="#eae4de"></stop></linearGradient></defs><path fill="url(#q)" d="M519 93l6 7v4 1 1c1 0 1 1 2 2l-1 1h0-1l1 2 1 1c3 5 9 11 14 15 1 0 5 3 6 3l2 1c1 1 3 2 5 3 2-1 3 0 5 1 10 3 21 6 32 9 3 2 7 3 10 5 1 3 6 4 8 5 1 1 2 3 3 4v1c1 2 1 2 3 4 0 1 1 3 1 5h0c-1-1-2-2-3-2l-1-1-2-2c-1-1 0-2-1-3h-2v1l-1-1c-1-2-2-3-2-5h-2 0l-2 1h-3-3v2c0 1 1 2 1 2 0 2 0 4 2 5 0 2 1 3 2 4 0 2 1 4 1 6 0 1 0 2-1 3l-1 2c1 2 1 3 3 5v2l-3 2c-1-1-1-2-1-3-1-2-2-3-3-4l-1-1c0-1-1-2-2-2l-3-3-3-1c-1 1-2 2-2 3-1 1-2 1-2 2l-2-4v-2c-1-1-2-3-3-5-3 0-4-3-7-3l-2-2v-1l1-1h0c-1-1-3-1-4-1v-1c-1-3-10-9-13-11l-1-1c-2-1-3-2-5-2h-2-1c-1 0-2 0-3 1l-1 1v-1h-1c1-2 1-2 1-4h-1l1-3 2-2v-1c-1 0-3 0-4 1l-6-2c3-1 5-1 8-1v-1c-2 0-4-1-6-1h-1l2-1c-2-1-4-2-5-3l-2-3-2-2h0l-4 1v-2l-1-1-1-8c1-1 0-4 0-6l1-5c0-1 1-3 1-5l-1-5z"></path><path d="M580 156c1 0 1 0 2 1l3 1h0c2 1 2 1 2 3l-3-1c-1-1-2-1-3-2s-1-1-1-2z" class="D"></path><path d="M562 146l1 1c1 0 3 1 5 1h0c2 1 4 2 5 3v1c-4 0-8-3-11-6z" class="G"></path><path d="M547 130l2 1c1 1 3 2 5 3 9 4 19 7 29 11 5 2 11 3 16 6l2 1c1 1 3 2 3 3h0-2c-3 0-6-2-8-3-15-6-31-10-45-19-1-1-2-1-2-3z" class="H"></path><path d="M599 151l2 1c1 1 3 2 3 3h0-2c-3 0-6-2-8-3 2 0 3 0 4 1h3c-1 0-1-1-2-2z" class="F"></path><path d="M554 134c2-1 3 0 5 1 10 3 21 6 32 9 3 2 7 3 10 5 1 3 6 4 8 5 1 1 2 3 3 4v1c1 2 1 2 3 4 0 1 1 3 1 5h0c-1-1-2-2-3-2l-1-1-2-2c-1-1 0-2-1-3h-2v1l-1-1c-1-2-2-3-2-5h0c0-1-2-2-3-3l-2-1c-5-3-11-4-16-6-10-4-20-7-29-11z" class="P"></path><path d="M601 152c3 0 4 1 6 2l-1 2-2-1c0-1-2-2-3-3z" class="l"></path><path d="M604 155h0l2 1c1 1 2 3 3 3 2 1 2 2 3 3 1 0 2 1 2 2 0 2 1 2 2 4h0c-1-1-2-2-3-2l-1-1-2-2c-1-1 0-2-1-3h-2v1l-1-1c-1-2-2-3-2-5z" class="j"></path><path d="M527 118v-2l-2-5h1l1 1c3 5 9 11 14 15 1 0 5 3 6 3 0 2 1 2 2 3h-2c-1-1-2-1-3-1h-1l2 3c1 1 3 2 4 3 4 3 8 5 12 8h1c3 3 7 6 11 6 0 1 2 1 2 2h1l-1 1c-1-1 0-1-1-1-3 0-3-1-5-2-1 0-3-2-4-2h-2c-5-3-10-8-15-11-3-2-5-4-8-5v-1l-2-1c-4-1-6-4-8-7-2-2-3-4-4-8l1 1z" class="D"></path><path d="M527 118v-2l-2-5h1l1 1c3 5 9 11 14 15 1 0 5 3 6 3 0 2 1 2 2 3h-2c-1-1-2-1-3-1-2-1-4-2-5-3-5-3-9-7-12-11z" class="F"></path><path d="M519 93l6 7v4 1 1c1 0 1 1 2 2l-1 1h0-1l1 2h-1l2 5v2l-1-1c1 4 2 6 4 8 2 3 4 6 8 7l2 1v1l-7-2c-2-1-4-2-5-3l-2-3-2-2h0l-4 1v-2l-1-1-1-8c1-1 0-4 0-6l1-5c0-1 1-3 1-5l-1-5z" class="K"></path><path d="M523 119c1 1 2 2 2 4 1 2 2 3 3 5v1l-2-3-2-2h0l-4 1v-2-1-2c1 0 2 1 3 1l1 1c-1-1-1-1-1-2v-1z" class="y"></path><path d="M520 122c1 0 2-1 3 0s1 1 1 2l-4 1v-2-1z" class="N"></path><path d="M519 103l3 7c0 3 0 5 1 9h0v1c0 1 0 1 1 2l-1-1c-1 0-2-1-3-1v2 1l-1-1-1-8c1-1 0-4 0-6l1-5z" class="AX"></path><path d="M519 93l6 7v4 1 1c1 0 1 1 2 2l-1 1h0-1l1 2h-1l2 5v2l-1-1c-1-2-3-5-4-7l-3-7c0-1 1-3 1-5l-1-5z" class="B"></path><path d="M519 93l6 7v4 1 1c1 0 1 1 2 2l-1 1h0-1c-2-3-4-7-5-11l-1-5z" class="b"></path><path d="M533 132l7 2c3 1 5 3 8 5 5 3 10 8 15 11 9 8 20 13 29 20h1l3 2h1l-1-1 1-1h0 1l1-1c0 2 1 4 1 6 0 1 0 2-1 3l-1 2c1 2 1 3 3 5v2l-3 2c-1-1-1-2-1-3-1-2-2-3-3-4l-1-1c0-1-1-2-2-2l-3-3-3-1c-1 1-2 2-2 3-1 1-2 1-2 2l-2-4v-2c-1-1-2-3-3-5-3 0-4-3-7-3l-2-2v-1l1-1h0c-1-1-3-1-4-1v-1c-1-3-10-9-13-11l-1-1c-2-1-3-2-5-2h-2-1c-1 0-2 0-3 1l-1 1v-1h-1c1-2 1-2 1-4h-1l1-3 2-2v-1c-1 0-3 0-4 1l-6-2c3-1 5-1 8-1v-1c-2 0-4-1-6-1h-1l2-1z" class="F"></path><path d="M592 170h1l3 2h1l-1-1 1-1h0 1l1-1c0 2 1 4 1 6 0 1 0 2-1 3-2-3-4-5-6-8h-1z" class="K"></path><path d="M530 136c3-1 5-1 8-1 4 1 7 3 10 5-1 1-3 1-4 1s-2-1-2-1h-1l-1-2v-1c-1 0-3 0-4 1l-6-2z" class="AP"></path><path d="M541 140h1s1 1 2 1 3 0 4-1c8 6 15 13 22 20l2 1-1 2-3-1h0v-1c-2-1-3-3-4-4-4-3-7-7-11-10-2-1-4-2-6-4h-1c-1 0-2 0-3-1-1 0-1-1-2-2z" class="y"></path><path d="M538 140l2-2 1 2c1 1 1 2 2 2 1 1 2 1 3 1h1c2 2 4 3 6 4 4 3 7 7 11 10 1 1 2 3 4 4v1c-1-1-3-1-4-1v-1c-1-3-10-9-13-11l-1-1c-2-1-3-2-5-2h-2-1c-1 0-2 0-3 1l-1 1v-1h-1c1-2 1-2 1-4h-1l1-3z" class="N"></path><path d="M585 175l1-1c0-1-2-4-3-4-2-2-3-3-4-5l3 2h1c2 1 5 3 7 5 1 1 1 0 2 1l5 5 1 2c1 2 1 3 3 5v2l-3 2c-1-1-1-2-1-3-1-2-2-3-3-4l-1-1c0-1-1-2-2-2l-3-3-3-1z" class="C"></path><path d="M588 176h2 0c1 2 3 3 4 4v-3h0v1c1 0 1 0 1 1v-1h1c0 1 0 0 1 0l1 2c1 2 1 3 3 5v2l-3 2c-1-1-1-2-1-3-1-2-2-3-3-4l-1-1c0-1-1-2-2-2l-3-3z" class="N"></path><path d="M570 160c2 0 3 0 5 1v1l8 5h0-1l-3-2c1 2 2 3 4 5 1 0 3 3 3 4l-1 1c-1 1-2 2-2 3-1 1-2 1-2 2l-2-4v-2c-1-1-2-3-3-5-3 0-4-3-7-3l-2-2v-1l1-1 3 1 1-2-2-1z" class="U"></path><path d="M570 160c2 0 3 0 5 1v1c1 1 1 1 1 2h0l-1 1c-1-1-1-2-3-3v-1l-2-1z" class="B"></path><path d="M568 162l3 1c2 2 4 4 5 6-3 0-4-3-7-3l-2-2v-1l1-1z" class="AS"></path><path d="M602 155h2c0 2 1 3 2 5l1 1v-1h2c1 1 0 2 1 3l2 2 1 1c1 0 2 1 3 2h0l5 3v1c1 1 2 1 4 2 1 0 1 1 2 1 2 2 5 4 7 6l14 11c6 5 11 11 18 15 1 1 2 1 3 1l1 1c2 0 5-1 7 0l51 1 10-1h2 0 16c4 0 8-1 12 0-2 2-5 2-8 2v1l2 1c-1 0-3-1-5 0h-2c0-1 0-1-1-2h-1l1 2-2 2c-2 2-4 3-6 5l-1 2-2 2c-5 2-12-1-16-1-3-1-6-1-9-1v2h7v1l13 2-12 3c-2 0-4 2-6 2-3 1-5 1-7 2-6 0-11 0-16-2v-1l-3-1c0 1-1 0-1 0v-1c-1 0-2-1-3-1-4-1-7-1-11-2l-1-1-3-1h0l-3-1-7-2c-2 0-6-1-8-2 1 0 2-1 4 0h3 0 3 2s-1-1-2-1h0-3c-1 0-3 0-4-1h-1-7l-2-1v-1c-2-1-6 0-9-1-1-1-3-1-5-2h4c-5-1-11 0-16 0-1-1-6-1-8-1h-22c-6 0-12 0-18-1v-1h2v-2l3 1 2-1c2 0 2 0 3-1l-2-2c0-1-1-2-1-4v-2-1h0c0-2-1-4-1-6v-1l1-1 2 1s1-1 2-1 1 1 3 0c2 1 3 2 4 4 1 0 1-1 2-1s1 0 2-1v-2l3-2v-2c-2-2-2-3-3-5l1-2c1-1 1-2 1-3 0-2-1-4-1-6-1-1-2-2-2-4-2-1-2-3-2-5 0 0-1-1-1-2v-2h3 3l2-1h0z" class="I"></path><path d="M578 207l3 1h1v1h-4v-2z" class="Aj"></path><path d="M586 206h0 1l1-1 2 2v1l5 1h7 4c1 0 2 1 3 1l-27-1v-1h-1l2-1c2 0 2 0 3-1z" class="V"></path><path d="M586 206h0 1l1-1 2 2v1h-8-1l2-1c2 0 2 0 3-1z" class="Aq"></path><path d="M631 198l7 7 4 4 3 1c-2 1-11 1-14 0-7-1-15 0-22 0-1 0-2-1-3-1h0 4c1 1 2 0 3 0 2-1 2 0 3 0 2 0 3 0 5-1l1 1c1 0 2 0 3-1h1l1 1h1l1-1c0-2 0-2-2-3v-1-1l1-1v-1l2-1 1 1v-3z" class="Al"></path><path d="M627 203l1-1v-1l2-1 1 1c1 1 1 2 2 4l-1 2h0c-1 0-1-1-2-2 0 0-1 0-1-1h-2v-1z" class="AA"></path><path d="M631 198l7 7 4 4h-7c-2-1-2-1-2-2l1-1-1-1c-1-2-1-3-2-4v-3z" class="W"></path><path d="M740 209h16c4 0 8-1 12 0-2 2-5 2-8 2v1l2 1c-1 0-3-1-5 0h-2c0-1 0-1-1-2h-1c-10 2-21 1-31 1h-46c-2 0-5 0-7-1h0 73c-2-1-3-1-4-2h2 0z" class="AZ"></path><path d="M738 209h2l9 1c0 1-2 1-3 1h-4c-2-1-3-1-4-2z" class="AE"></path><path d="M616 193c1 1 3 3 3 2l2 2h3c1 1 1 1 1 3 1 1 2 2 2 3v1 1c2 1 2 1 2 3l-1 1h-1l-1-1h-1c-1 1-2 1-3 1l-1-1c-2 1-3 1-5 1-1 0-1-1-3 0-1 0-2 1-3 0v-3l2-1c1-1 1-2 1-4 0-1 0-1 1-2v-5l2-1z" class="c"></path><path d="M616 193c1 1 3 3 3 2l2 2-1 1h0c-1-1-2-1-3-1-1-1-2-1-3-3l2-1z" class="W"></path><path d="M614 199v1c0 1 0 1 1 2v1h1 1 0l2 2v1l-3 3c-1 0-1-1-3 0-1 0-2 1-3 0v-3l2-1c1-1 1-2 1-4 0-1 0-1 1-2z" class="m"></path><path d="M621 197h3c1 1 1 1 1 3 1 1 2 2 2 3v1 1c2 1 2 1 2 3l-1 1h-1l-1-1h-1c0-1-1-1-1-2h-1l-1-1h-1v-3l-1 1h-1c0-2 0-2 1-4v-1l1-1z" class="Y"></path><path d="M626 208c0-1-1-3-1-3-1-1-2-1-2-2v-1l2 1h0l-1-2 1-1c1 1 2 2 2 3v1 1c2 1 2 1 2 3l-1 1h-1l-1-1z" class="Z"></path><path d="M753 211l1 2-2 2c-2 2-4 3-6 5h-5-2v-2c1-2 4-2 6-3-2 0-7-1-9 1h0l-19 1c-5 1-10 1-14 1l-39-1h-5-7l-2-1v-1c-2-1-6 0-9-1-1-1-3-1-5-2h4 24 12 46c10 0 21 1 31-1z" class="Aj"></path><path d="M650 215c6 1 14 0 20 1-2 0-4 0-6 1h-5-7l-2-1v-1z" class="f"></path><path d="M698 216h38 0l-19 1h-15c-1 0-3 0-4-1z" class="AZ"></path><path d="M670 216h19 9c1 1 3 1 4 1h15c-5 1-10 1-14 1l-39-1c2-1 4-1 6-1z" class="Ac"></path><path d="M602 181c1 1 2 2 4 3v1 1l2 2 1 1h1l2 1h0c0 1 0 2 1 3h3 0l-2 1v5c-1 1-1 1-1 2 0 2 0 3-1 4l-2 1v3h-4 0-4-7l-5-1v-1l-2-2-1 1h-1 0l-2-2c0-1-1-2-1-4v-2-1h0c0-2-1-4-1-6v-1l1-1 2 1s1-1 2-1 1 1 3 0c2 1 3 2 4 4 1 0 1-1 2-1s1 0 2-1v-2l3-2v-2c0-1 0-2 1-3h0v-1z" class="u"></path><path d="M598 191l1 1 1 4h-1c-1 0-2 2-3 1h-2v-1-3c1 0 1-1 2-1s1 0 2-1z" class="AI"></path><path d="M593 202h2v2l2 2v2c-1 0-1 1-2 1l-5-1v-1c0-1 0-1 1-2 0-1 1-2 2-3z" class="Ar"></path><path d="M604 205c0-1 1-1 2-1l-1-1 1-1 3 3 1 1v3h-4 0-4c-1 0-3 0-4-1l1-1h0 1l1 1h1l1-2c0 1 0 1 1 2v-3z" class="U"></path><path d="M609 205l1 1v3h-4c1-1 0-1 1-2l1-1 1-1z" class="R"></path><path d="M582 190l1-1 2 1s1-1 2-1 1 1 3 0c2 1 3 2 4 4v3 1c-1 1-1 1-1 3v2c-1 1-2 2-2 3-1 1-1 1-1 2l-2-2-1 1h-1 0l-2-2c0-1-1-2-1-4v-2-1h0c0-2-1-4-1-6v-1z" class="Af"></path><path d="M586 202v-1l3 3v1c-1-1-1 0-2-1 0-1 0-1-1-2z" class="T"></path><path d="M586 202c1 1 1 1 1 2 1 1 1 0 2 1h-1l-1 1h-1 0l-2-2 2-2z" class="G"></path><path d="M585 192h3v1l-2 2c-1 1 1 2-1 3h-1l1-6z" class="C"></path><path d="M589 197c2 1 2 1 3 3l-1 1h-1c-1 0-1-1-2-1s-1 0-2-1c1-1 2-1 3-2z" class="AV"></path><path d="M582 190l1-1 2 1s1-1 2-1 1 1 3 0c2 1 3 2 4 4v3h-1c-1-2-2-3-3-5-2-1-3 0-5 1l-1 6-1-1h0c0-2-1-4-1-6v-1z" class="Ak"></path><path d="M602 181c1 1 2 2 4 3v1 1l2 2 1 1h1l2 1h0c0 1 0 2 1 3h3 0l-2 1v5c-1 1-1 1-1 2 0 2 0 3-1 4l-2 1-1-1-3-3-1 1 1 1c-1 0-2 0-2 1h-1l1-1-1-1h0v-2c-1 0-2-1-3-2v1l-2-1 1-3h1l-1-4-1-1v-2l3-2v-2c0-1 0-2 1-3h0v-1z" class="Q"></path><path d="M602 192c0 2 0 3 1 5v3c-1 0-2-1-3-1v1l-2-1 1-3h1 0l1-1c1-1 1-2 1-3z" class="a"></path><path d="M609 194l2-1v1l2 2v1c-1 1-1 3-1 4-1 0-1 0-1-1l-3-3-1 1v3h0c-2-1-1 0-2-1 1-1 1-2 1-3v-3c1 1 1 1 2 1l1-1z" class="Y"></path><path d="M602 189l2 1 1 1h0c1 1 1 1 1 2 1 0 2 1 3 1l-1 1c-1 0-1 0-2-1v3c0 1 0 2-1 3-1-1 0-2-1-3h-1c-1-2-1-3-1-5v-3z" class="u"></path><path d="M602 189l2 1 1 1c0 2-1 3-2 4l1 2h-1c-1-2-1-3-1-5v-3z" class="AB"></path><path d="M604 187v-1h2l2 2 1 1h1c0 1 1 3 1 4l-2 1c-1 0-2-1-3-1 0-1 0-1-1-2h0l-1-1 1-2-1-1z" class="c"></path><path d="M604 187v-1h2l2 2c0 1 0 1-1 2l-2-1v-1l-1-1z" class="AR"></path><path d="M602 181c1 1 2 2 4 3v1 1h-2v1l1 1-1 2-2-1v3c0 1 0 2-1 3l-1 1h0l-1-4-1-1v-2l3-2v-2c0-1 0-2 1-3h0v-1z" class="K"></path><path d="M599 192l2 1v2l-1 1h0l-1-4z" class="Af"></path><path d="M602 186l2 1 1 1-1 2-2-1v-3z" class="Y"></path><path d="M602 181c1 1 2 2 4 3v1 1h-2v1l-2-1v-4-1z" class="AS"></path><path d="M736 216c2-2 7-1 9-1-2 1-5 1-6 3v2h2 5l-1 2-2 2c-5 2-12-1-16-1-3-1-6-1-9-1v2h7v1l13 2-12 3c-2 0-4 2-6 2-3 1-5 1-7 2-6 0-11 0-16-2v-1l-3-1c0 1-1 0-1 0v-1c-1 0-2-1-3-1-4-1-7-1-11-2l-1-1-3-1h0l-3-1-7-2c-2 0-6-1-8-2 1 0 2-1 4 0h3 0 3 2s-1-1-2-1h0-3c-1 0-3 0-4-1h-1 5l39 1c4 0 9 0 14-1l19-1h0z" class="h"></path><path d="M665 221l1-1c4 1 8 0 12 1-1 1-2 1-2 1l-4 1-7-2z" class="I"></path><path d="M678 221l8 1h0l-2 1h0v1h4l-1 1-1 1-11-2-3-1 4-1s1 0 2-1z" class="AF"></path><path d="M678 221l8 1h0l-2 1h0v1l-8-2s1 0 2-1z" class="p"></path><path d="M707 221h19c3 0 6-1 9 0s5 1 8 3c-5 2-12-1-16-1-3-1-6-1-9-1v2h-1c-1-1-2-1-3-1v-1c-2-1-4-1-7-1z" class="I"></path><path d="M714 222h4v2h-1c-1-1-2-1-3-1v-1z" class="AZ"></path><path d="M686 222l21-1c3 0 5 0 7 1v1c1 0 2 0 3 1l-7 1 1 1h-11-2-3-4 0l-4-1 1-1h-4v-1h0l2-1h0z" class="AE"></path><path d="M697 223h6c-2 1-3 0-4 1l-2 1h-1c-1-1-1 0-1-2h2z" class="AU"></path><path d="M686 222c4 0 8 0 11 1h-2c0 2 0 1 1 2h1l1 1h-3-4 0l-4-1 1-1h-4v-1h0l2-1z" class="AL"></path><path d="M688 224l8 1h1l1 1h-3-4 0l-4-1 1-1z" class="g"></path><path d="M703 223h11c1 0 2 0 3 1l-7 1 1 1h-11-2l-1-1 2-1c1-1 2 0 4-1z" class="O"></path><path d="M699 224c3 1 7 1 11 1l1 1h-11-2l-1-1 2-1z" class="Ai"></path><defs><linearGradient id="r" x1="701.159" y1="236.109" x2="715.341" y2="226.391" xlink:href="#B"><stop offset="0" stop-color="#2a272d"></stop><stop offset="1" stop-color="#444e55"></stop></linearGradient></defs><path fill="url(#r)" d="M675 224l11 2 1-1 4 1h0 4 3 2 11l-1-1 7-1h1 7v1l13 2-12 3c-2 0-4 2-6 2-3 1-5 1-7 2-6 0-11 0-16-2v-1l-3-1c0 1-1 0-1 0v-1c-1 0-2-1-3-1-4-1-7-1-11-2l-1-1-3-1h0z"></path><path d="M686 226l1-1 4 1h0c1 1 2 1 3 2h0-2c-2-1-4-1-6-2z" class="I"></path><path d="M691 226h4 1c1 1 1 1 2 1v1 2l-4-2h0c-1-1-2-1-3-2z" class="Ac"></path><path d="M695 226h3 2c3 2 5 3 9 3h1 1 2 3l1 1h-3c-6 0-10 1-16 0v-2-1c-1 0-1 0-2-1h-1z" class="AZ"></path><path d="M718 224h7v1l13 2-12 3c-2 0-4 2-6 2-2-2-4-2-6-2h3l-1-1h-3-2-1-1c-4 0-6-1-9-3h11l-1-1 7-1h1z" class="AY"></path><path d="M718 224h7v1h-6l-8 1-1-1 7-1h1z" class="Ae"></path><path d="M725 225l13 2-12 3c-2 0-4 2-6 2-2-2-4-2-6-2h3l-1-1h-3c2-1 3-1 5-1 3-1 5 0 8-2h0l-7-1h6z" class="O"></path><path d="M717 230c2-1 7-2 9 0-2 0-4 2-6 2-2-2-4-2-6-2h3z" class="Ac"></path><defs><linearGradient id="s" x1="641.012" y1="172.772" x2="633.211" y2="201.895" xlink:href="#B"><stop offset="0" stop-color="#bbb1ae"></stop><stop offset="1" stop-color="#e7e0d9"></stop></linearGradient></defs><path fill="url(#s)" d="M602 155h2c0 2 1 3 2 5l1 1v-1h2c1 1 0 2 1 3l2 2 1 1c1 0 2 1 3 2h0l5 3v1c1 1 2 1 4 2 1 0 1 1 2 1 2 2 5 4 7 6l14 11c6 5 11 11 18 15 1 1 2 1 3 1l1 1c2 0 5-1 7 0h-14l-18 1-3-1-4-4-7-7v3l-1-1-2 1v1l-1 1c0-1-1-2-2-3 0-2 0-2-1-3h-3l-2-2c0 1-2-1-3-2h0-3c-1-1-1-2-1-3h0l-2-1h-1l-1-1-2-2v-1-1c-2-1-3-2-4-3v1h0c-1 1-1 2-1 3-2-2-2-3-3-5l1-2c1-1 1-2 1-3 0-2-1-4-1-6-1-1-2-2-2-4-2-1-2-3-2-5 0 0-1-1-1-2v-2h3 3l2-1h0z"></path><path d="M616 175c2 2 3 3 4 5h1l-5-3v-2z" class="C"></path><path d="M610 169h1 2c1 0 3 2 4 3h0c-2-1-3-2-5-2 0 1 2 3 4 4v1 2l-3-2-2-2-1-4z" class="N"></path><defs><linearGradient id="t" x1="623.511" y1="196.667" x2="638.714" y2="186.07" xlink:href="#B"><stop offset="0" stop-color="#ac9f96"></stop><stop offset="1" stop-color="#d7cfc9"></stop></linearGradient></defs><path fill="url(#t)" d="M627 192c-3-3-7-6-10-8s-5-3-7-5c-1-1-1-2-1-4h1 0v-1c1 0 2 0 2 1h1l3 2 5 3c4 3 9 6 12 10 3 2 4 5 6 7s5 3 7 5h0l-1-2c2 1 3 4 5 5h3c1 0 3 2 4 3h4c1 1 1 1 2 1l-18 1-3-1-4-4c0-4-2-5-5-7-2-2-3-4-6-6z"></path><path d="M627 192l1-2c1 0 1 0 2 1 0 1 1 1 2 1v1l2 3h-1l1 1-1 1c-2-2-3-4-6-6z" class="K"></path><path d="M602 155h2c0 2 1 3 2 5l1 1v-1h2c1 1 0 2 1 3l2 2c0 2-1 2-3 3l1 1 1 4 2 2h-1c0-1-1-1-2-1v1h0-1c0 2 0 3 1 4 2 2 4 3 7 5s7 5 10 8c3 2 4 4 6 6 3 2 5 3 5 7l-7-7v3l-1-1-2 1v1l-1 1c0-1-1-2-2-3 0-2 0-2-1-3h-3l-2-2c0 1-2-1-3-2h0-3c-1-1-1-2-1-3h0l-2-1h-1l-1-1-2-2v-1-1c-2-1-3-2-4-3v1h0c-1 1-1 2-1 3-2-2-2-3-3-5l1-2c1-1 1-2 1-3 0-2-1-4-1-6-1-1-2-2-2-4-2-1-2-3-2-5 0 0-1-1-1-2v-2h3 3l2-1h0z" class="o"></path><path d="M600 175l1-1 1 8c-1 1-1 2-1 3-2-2-2-3-3-5l1-2c1-1 1-2 1-3z" class="P"></path><path d="M602 155h2c0 2 1 3 2 5l1 1v-1h2c1 1 0 2 1 3l2 2c0 2-1 2-3 3l1 1 1 4c-2-2-4-4-5-6v-1l-2-3 1-1c0-1-1-2-1-2h-1c-1-2-1-3-1-5h0z" class="L"></path><path d="M599 169c-1-1-2-2-2-4-2-1-2-3-2-5 0 0-1-1-1-2v-2h3 3 1c0 2-1 3-1 4 0 2 1 4 0 6l2 2-1 6-1 1c0-2-1-4-1-6z" class="E"></path><path d="M597 156h3 1c0 2-1 3-1 4 0 2 1 4 0 6-1 0-3-1-3-3-1-2-1-5 0-7z" class="b"></path><path d="M602 181v-3h1c11 5 19 12 28 20v3l-1-1-2 1v1l-1 1c0-1-1-2-2-3 0-2 0-2-1-3h-3l-2-2c0 1-2-1-3-2h0-3c-1-1-1-2-1-3h0l-2-1h-1l-1-1-2-2v-1-1c-2-1-3-2-4-3z" class="AN"></path><path d="M606 185l6 3c0 1 1 2 3 2h1c2 2 3 3 4 5h-1c0 1-2-1-3-2h0-3c-1-1-1-2-1-3h0l-2-1h-1l-1-1-2-2v-1z" class="k"></path><path d="M905 200c7 10 16 18 21 29 3 8 4 16 1 25l-2 3c0-8 0-15-7-21-6-6-16-7-24-7-18 1-34 9-49 18-3 2-7 4-10 6-1 1-4 2-5 3l-3 3h1v1 1c-1 0-1 1-1 2-1 1-1 2-2 4l-3 1c0 1-1 3-1 4-1 2-1 6-2 7l-1 1c-1 1-1 2-2 5v-2l-1-1v-4c1-1 0-2 0-3v-1c-2 2-4 5-5 8l-2 1-6 6c-2 1-2 3-4 4-1 2-1 3-2 4s-2 1-2 2l-1 1c-1 1-1 2-1 2-2 3-4 5-5 7s-2 3-3 5c-3 2-4 5-6 9 0 1-1 2-1 3h-1s-1 1 0 2l-1 1v1c-2 0-2 0-2 1s0 1-1 2c0 2-2 4-2 6 1 1 1 1 1 2v3c-1 1-1 2-1 3l-2 2c-1 1 0 2-1 3s-2 1-2 3-2 3-3 5v1l-1 1v-2c-1 1-1 2-1 2-1 3-3 5-4 7l-1 2v1c-1 1-1 2-2 3v1 3c-1 1-1 1-3 2v2 1c-1 0-1 1-2 2l-2 1v1c-1 2-2 4-4 5l-1 1v1c0 1 0 1-1 2 0 2 1 6 0 7-1-1-1-3-1-4v-25l1-19v-2h-2v9c-1-4-1-10-1-14l-3-30h-1c-1-1-1-4-1-6l-2-6h0c-1-4-4-10-6-13-1-1-1-1-1-2-1-1-1-1-1-2-2-3-8-7-11-8-2-1-2 0-4-1h0c2-1 3-2 5-3 1-2 4-3 5-6-1-3-3-6-6-9-1-2-3-3-4-5h0c-2-5-10-10-14-12-3-2-8-5-9-8-1-2-2-3-2-4 1-1 5-1 7-1 4 1 8 2 12 4v-1-1h3 0c2-1 6-1 9-1 2-1 4-1 7-2 2 0 4-2 6-2l12-3-13-2v-1h-7v-2c3 0 6 0 9 1 4 0 11 3 16 1l2-2h1l-2 2 1 1v1c4 1 11 0 15 0l65-5c2-1 3 0 4-1 4-1 8-1 12-1l22-3c3-1 13-3 15-5h-5c-4-1-9 1-13-1 10 0 19-1 29-3h2 1c2 0 2 0 3-1l-2 2-1 1c1 0 2-1 4-1l-2 2v1c2-1 3-3 6-4 1 0 2-2 2-4 1-1 1 0 2-1l1-2z" class="V"></path><path d="M740 338l1 2h0c1-2 1-4 2-5 0-1 0-2 1-3 1 2-1 6-2 8-1 5 1 11-2 16v-2-16z" class="M"></path><path d="M770 313l1 1c-6 6-10 12-14 19-2 3-5 6-6 9l-1 2h-1c2-4 4-7 6-10 2-4 4-7 7-11l7-9 1-1z" class="Aj"></path><path d="M900 207c1 0 2-2 2-4 1-1 1 0 2-1l-3 11-1 2 2 1c8 1 15 5 20 11 1 1 2 3 3 4l2 6c0 2 1 7 0 8 0-8-3-16-10-21-5-5-12-8-19-7 0-1 1-2 1-3l1-2v-1c1-1 1-1 1-2l-1-1v-1z" class="U"></path><path d="M762 300c1-1 2-2 4-3 0 0 1 0 2-1v-1c0-1 1-1 3-1l-2 2c-1 1-1 1-1 2-1 0-1 1-2 1-1 2 0 0-1 1l-3 5c-1 1-1 1-1 2-1 0-1 1-2 1l-1 1v2l-6 7c0 1-1 2-2 3 0-2 1-2 1-3 1-1 1-1 1-2 0-2 1-3 1-5h-2l1-2c3-4 5-6 9-9h1z" class="h"></path><path d="M752 309c3-4 5-6 9-9h1c-3 3-7 8-9 11h-2l1-2z" class="s"></path><defs><linearGradient id="u" x1="879.497" y1="212.694" x2="891.994" y2="222.938" xlink:href="#B"><stop offset="0" stop-color="#aaaab1"></stop><stop offset="1" stop-color="#d7d8dc"></stop></linearGradient></defs><path fill="url(#u)" d="M894 211c2-1 3-3 6-4v1l1 1c0 1 0 1-1 2v1l-1 2c0 1-1 2-1 3l-29 10v-1-1c3-2 8-4 11-6 1 0 6-2 7-3 2-3 4-3 7-5z"></path><path d="M894 211c2-1 3-3 6-4v1c-4 5-7 7-13 8 2-3 4-3 7-5z" class="Ag"></path><path d="M827 259h1v1 1c-1 0-1 1-1 2-1 1-1 2-2 4l-3 1c0 1-1 3-1 4-1 2-1 6-2 7l-1 1c-1 1-1 2-2 5v-2l-1-1v-4c1-1 0-2 0-3v-1c-2 2-4 5-5 8l-2 1h-1 0-1c2-2 2-2 2-4l2-1h1c0-1 1-2 1-3 2-1 2-1 2-4-3 3-7 8-10 9 4-6 10-11 15-15 1-1 3-3 5-4l3-2z" class="O"></path><path d="M824 261c-1 3-3 4-4 6s-2 4-2 6c-1-1-1-3 0-4 0-2 1-3 1-4 1-1 3-3 5-4z" class="Ai"></path><path d="M812 275c1 0 2 0 2-2 1-1 1-2 2-3h1v5l1 1 1 3-1 1c-1 1-1 2-2 5v-2l-1-1v-4c1-1 0-2 0-3v-1c-2 2-4 5-5 8l-2 1h-1 0-1c2-2 2-2 2-4l2-1h1c0-1 1-2 1-3z" class="b"></path><path d="M816 283v-2c0-2 0-4 2-5l1 3-1 1c-1 1-1 2-2 5v-2z" class="L"></path><path d="M905 200c7 10 16 18 21 29l-1 2c-1-1-2-3-3-4-5-6-12-10-20-11l-2-1 1-2 3-11 1-2z" class="d"></path><path d="M901 213l1 1c2-1 3-3 4-5v-1h1l2 2h-1c-1 2 0 1-1 2 0 1-1 3-2 3l-3 1-2-1 1-2z" class="f"></path><path d="M909 210l7 8c3 3 5 5 6 9-5-6-12-10-20-11l3-1c1 0 2-2 2-3 1-1 0 0 1-2h1z" class="Ai"></path><path d="M800 259c1 0 3-2 4-3 5-3 10-5 15-8 3-1 7-4 10-4h1c1-1 2-1 3-1h1l-4 3c-1 1-2 2-4 3-1 0-2 1-3 2s-2 2-4 2c-1 1-1 2-2 2l-1 1c-1 0-2 1-3 2l-12 8-2 1-2 2-2 1c-1 1 0 1-1 1l-2 2c-1 1 1 0-1 0l-3 3c-2 1-3 4-6 5-1 1-5 5-6 7s-4 3-5 6h0c-2 0-3 0-3 1v1c-1 1-2 1-2 1-2 1-3 2-4 3h-1c-4 3-6 5-9 9v-3c2-3 5-6 6-9l-3 1 2-4c1-3 4-5 7-8l1-1 3-4 1-3 3-3 15-12c0 1-1 1-2 2v2h3l1-1c1-1-1 1 1-1l4-2c0-1 1-1 1-1l1-1c1-1 1-1 3-2h1z" class="M"></path><path d="M779 274l1 1c-8 7-15 14-22 22l-3 1 2-4c1-3 4-5 7-8l1-1 3-4c2 0 3-1 4-1l7-6z" class="Ac"></path><path d="M768 281c2 0 3-1 4-1 0 1-3 3-3 4l-4 1 3-4z" class="f"></path><path d="M765 285l4-1c-3 3-8 9-12 10 1-3 4-5 7-8l1-1z" class="AE"></path><path d="M800 259c1 0 3-2 4-3 5-3 10-5 15-8 3-1 7-4 10-4h1c1-1 2-1 3-1h1l-4 3c-1 1-2 2-4 3-1 0-2 1-3 2s-2 2-4 2h-1c0-1 0-1 1-1-2 0-6 3-8 3-11 5-21 12-31 20l-1-1-7 6c-1 0-2 1-4 1l1-3 3-3 15-12c0 1-1 1-2 2v2h3l1-1c1-1-1 1 1-1l4-2c0-1 1-1 1-1l1-1c1-1 1-1 3-2h1z" class="f"></path><path d="M811 255c3-2 7-4 10-6 3-1 6-3 9-3-1 1-2 2-4 3-1 0-2 1-3 2s-2 2-4 2h-1c0-1 0-1 1-1-2 0-6 3-8 3z" class="Aj"></path><path d="M787 263c0 1-1 1-2 2v2h3l1-1c1-1-1 1 1-1l4-2c0-1 1-1 1-1l1-1c1-1 1-1 3-2h1c-5 5-11 7-16 11-2 2-3 3-5 4l-7 6c-1 0-2 1-4 1l1-3 3-3 15-12z" class="AF"></path><defs><linearGradient id="v" x1="768.641" y1="322.127" x2="781.662" y2="329.469" xlink:href="#B"><stop offset="0" stop-color="#666362"></stop><stop offset="1" stop-color="#8c8786"></stop></linearGradient></defs><path fill="url(#v)" d="M804 280c3-1 7-6 10-9 0 3 0 3-2 4 0 1-1 2-1 3h-1l-2 1c0 2 0 2-2 4h1 0 1l-6 6c-2 1-2 3-4 4-1 2-1 3-2 4s-2 1-2 2l-1 1c-1 1-1 2-1 2-2 3-4 5-5 7s-2 3-3 5c-3 2-4 5-6 9 0 1-1 2-1 3h-1s-1 1 0 2l-1 1v1c-2 0-2 0-2 1s0 1-1 2c0 2-2 4-2 6 1 1 1 1 1 2v3c-1 1-1 2-1 3l-2 2c-1 1 0 2-1 3s-2 1-2 3-2 3-3 5v1l-1 1v-2c-1 1-1 2-1 2-1 3-3 5-4 7l-1 2v1c-1 1-1 2-2 3v1 3c-1 1-1 1-3 2v2 1c-1 0-1 1-2 2l-2 1v1c-1 2-2 4-4 5l-1 1v1c0 1 0 1-1 2 0 2 1 6 0 7-1-1-1-3-1-4v-25l1-1c3-8 8-17 13-25 0-2 3-6 4-8 1-1 3-4 4-6 7-13 16-24 25-35 6-7 11-14 18-20z"></path><path d="M792 302c-1 1-2 2-3 2 1-1 1-2 2-3 1-2 1-3 3-3 1-1 1-2 2-3 0-1 1-1 2-2-1 2-1 3-2 4s-2 1-2 2l-1 1c-1 1-1 2-1 2z" class="b"></path><path d="M753 349c0-2 3-6 4-8-1 7-4 15-8 20 0-2 1-3 1-4 1-1 1-2 2-3v-1c1-2 1-2 1-4z" class="Ai"></path><defs><linearGradient id="w" x1="739.132" y1="376.466" x2="746.974" y2="379.863" xlink:href="#B"><stop offset="0" stop-color="#565556"></stop><stop offset="1" stop-color="#706d6d"></stop></linearGradient></defs><path fill="url(#w)" d="M739 375l1-1c3-8 8-17 13-25 0 2 0 2-1 4v1c-1 1-1 2-2 3 0 1-1 2-1 4-1 5 0 9-3 13 0 2 0 5-1 8l1 1 1-1h0c0 1-1 2-1 3h0v2 1c-1 2-2 4-4 5l-1 1v1c0 1 0 1-1 2 0 2 1 6 0 7-1-1-1-3-1-4v-25z"></path><path d="M742 393c0-2 1-5 2-7l1-1v-3l1 1 1-1h0c0 1-1 2-1 3h0v2 1c-1 2-2 4-4 5z" class="O"></path><path d="M891 207h1c2 0 2 0 3-1l-2 2-1 1c1 0 2-1 4-1l-2 2v1c-3 2-5 2-7 5-1 1-6 3-7 3-3 2-8 4-11 6v1 1l-35 16h-1c-1 0-2 0-3 1h-1c-3 0-7 3-10 4-5 3-10 5-15 8-1 1-3 3-4 3h-1c-2 1-2 1-3 2l-1 1s-1 0-1 1l-4 2-1 1-1 1h-3v-2c1-1 2-1 2-2l-15 12h0c1-3 4-6 4-9 1-1 4-3 5-4-2 0-4 3-6 4-2 0-4 0-5 1h-1l3-3 3-3h0c2-1 3-3 4-5-1 0-2 0-3 1h0-3l-2 1v-1c1-1 3-2 4-3l1-2h0c1-1 2-2 3-2h1v-1h-1c-1 0-2 0-2-1h-2 0l2-2c1-1 6-3 8-3l1-1h2v-1h3c1-1 2-1 3-2h0l4-2h2c2-1 5-3 7-3 1-1 1-1 2-1l1-1c1 0 2 0 2-1h2l1-1c0-1 0-1 1-1h1c1-1 1-1 2-1 1-1 3-2 4-2 2-1 4-2 6-2h0l1-1c1 0 0 0 1-1h-2-3-1v-1c2-1 3 0 4-1 4-1 8-1 12-1l22-3c3-1 13-3 15-5h-5c-4-1-9 1-13-1 10 0 19-1 29-3h2z" class="s"></path><path d="M819 233c1-2 2-4 4-4 1 0 1 0 3-1 1-1 3-2 5-2-1 2-5 3-7 4l-2 1-1 1c-1 0-1 0-2 1z" class="M"></path><path d="M799 243c1 0 1 0 2 1-3 1-6 3-9 5-1 1-1 1-2 1l-1-1c1-2 9-5 10-6z" class="g"></path><path d="M818 232l1 1-6 3-12 8c-1-1-1-1-2-1 5-5 14-7 19-11z" class="r"></path><path d="M892 209c1 0 2-1 4-1l-2 2v1c-3 2-5 2-7 5-1 1-6 3-7 3h-1c-1 0-3 1-3 1l-9 3c-1 0-2 0-2 1l-1-1c10-3 19-8 28-14z" class="p"></path><path d="M889 207c-1 2-3 3-5 4h0c-4 2-7 3-11 4l-19 3-1 1h-6c-3 1-6 0-8 1-4 1-9 1-13 2h-1v-1c2-1 3 0 4-1 4-1 8-1 12-1l22-3c3-1 13-3 15-5h-5c-4-1-9 1-13-1 10 0 19-1 29-3z" class="h"></path><path d="M789 249l1 1c-2 2-3 4-5 6 3-1 5-3 7-4v-1h1 1c1-1 2-1 2-1h1l-4 3-7 5c-1 1-4 4-5 4-2 0-4 3-6 4-2 0-4 0-5 1h-1l3-3 3-3h0c2-1 3-3 4-5-1 0-2 0-3 1h0-3l-2 1v-1c1-1 3-2 4-3h1c1 0 3-1 5-1 0-1 0 0 1 0l7-4z" class="AF"></path><path d="M775 254h1c1 0 3-1 5-1 0-1 0 0 1 0-1 1-2 1-3 1-2 1-5 2-6 3l-2 1v-1c1-1 3-2 4-3z" class="h"></path><path d="M779 256l2 1 6-5-7 8v1c2-1 4-3 6-4v1c-1 1-4 4-5 4-2 0-4 3-6 4-2 0-4 0-5 1h-1l3-3 3-3h0c2-1 3-3 4-5z" class="I"></path><defs><linearGradient id="x" x1="796.643" y1="230.882" x2="798.391" y2="252.116" xlink:href="#B"><stop offset="0" stop-color="#020000"></stop><stop offset="1" stop-color="#1a222b"></stop></linearGradient></defs><path fill="url(#x)" d="M809 233c4 0 8-3 13-4h-1l-3 3c-5 4-14 6-19 11-1 1-9 4-10 6l-7 4c-1 0-1-1-1 0-2 0-4 1-5 1h-1l1-2h0c1-1 2-2 3-2h1v-1h-1c-1 0-2 0-2-1h-2 0l2-2c1-1 6-3 8-3l1-1h2v-1h3c1-1 2-1 3-2h0l4-2h2c2-1 5-3 7-3 1-1 1-1 2-1z"></path><path d="M865 224c0-1 1-1 2-1l9-3s2-1 3-1h1c-3 2-8 4-11 6v1 1l-35 16h-1c-1 0-2 0-3 1h-1c-3 0-7 3-10 4-5 3-10 5-15 8-1 1-3 3-4 3h-1c-2 1-2 1-3 2l-1 1s-1 0-1 1l-4 2-1 1-1 1h-3v-2c1-1 2-1 2-2l2-2 26-17c5-2 10-5 14-6 6-3 12-6 18-8 5-2 11-5 17-7l1 1z" class="d"></path><path d="M819 245c1 0 1 0 2 1h-1l-2 1v-1l1-1z" class="M"></path><path d="M829 238c6-3 12-6 18-8 5-2 11-5 17-7l1 1-26 11h-1c-2 1-6 3-9 3z" class="V"></path><path d="M869 225v1 1l-35 16h-1l36-18z" class="L"></path><path d="M761 250l21-7h3c-2 0-7 2-8 3l-2 2h0 2c0 1 1 1 2 1h1v1h-1c-1 0-2 1-3 2h0l-1 2c-1 1-3 2-4 3v1l2-1h3 0c1-1 2-1 3-1-1 2-2 4-4 5h0l-3 3-3 3h1c1-1 3-1 5-1 2-1 4-4 6-4-1 1-4 3-5 4 0 3-3 6-4 9h0l-3 3-1 3-3 4-1 1c-3 3-6 5-7 8l-2 4 3-1c-1 3-4 6-6 9v3l-1 2h2c0 2-1 3-1 5 0 1 0 1-1 2 0 1-1 1-1 3 0 1-1 1-1 3l-2 3c-1 1-1 2-1 2l-1 1-1 2c-1 1-1 2-1 3-1 1-1 3-2 5h0l-1-2v16h-2v9c-1-4-1-10-1-14l-3-30h-1c-1-1-1-4-1-6l-2-6h0c-1-4-4-10-6-13-1-1-1-1-1-2-1-1-1-1-1-2-2-3-8-7-11-8-2-1-2 0-4-1h0c2-1 3-2 5-3 1 0 2-1 3-1 1-1 2-2 4-2 1 0 2 0 4 1h0c3 1 4 4 5 6l1 2v-3c0-2 0-4-1-7 1 1 2 3 3 3l1-6 1-1 1-4v3l1-1h0c1 0 2-1 3-1l-2-4h1l3-2c-2-2-4-2-6-2 4 0 7-1 11-3 1 0 3-1 5-1l11-5z" class="Aj"></path><path d="M734 278h1c1 1 1 3 1 5 0 1 1 2 1 4 1 0 1 1 2 2l1 1c0 2-1 3-2 5l-1-1c0-1-1-2-1-4-1-4-1-8-2-12z" class="U"></path><path d="M734 266v3 2 5l-1-1v-3c-1 1-1 2-1 4v2 1 1 4c-1 1-2 2-2 3-1-1-1-2-1-3v-3c0-2 0-4-1-7 1 1 2 3 3 3l1-6 1-1 1-4z" class="d"></path><path d="M730 287c0-1 1-2 2-3 0 2-1 4 0 5 0 1 0 2 1 4v2c-2 3-1 5-1 8-1-1-1-1-1-2v-1c0-1-1-2-1-2v-1l-2-6v-1c2-1 2-2 2-3z" class="M"></path><path d="M739 313c1 1 1 2 1 4v1l1-1v6l-1 15v16h-2c0-6 1-13 1-19l-1-19 1-1v-2z" class="AQ"></path><path d="M744 292v1c0 2-1 4-2 6 1 2 1 2 3 3l-1 2c-1 1-2 2-3 2v3c0 2 0 4-1 7v1c0-2 0-3-1-4v2l-1 1-1-22 1 1 1 1 1-1h0l1-1h2c0-1 0-1 1-2z" class="t"></path><path d="M739 303c1 1 0 2 1 3h1v3c0 2 0 4-1 7v1c0-2 0-3-1-4v-10z" class="b"></path><path d="M744 292v1c0 2-1 4-2 6l-1 2-1-1c0-1 0-3 2-5 0-1 0 0 1-1h0c0-1 0-1 1-2z" class="O"></path><path d="M741 301l1-2c1 2 1 2 3 3l-1 2c-1 1-2 2-3 2h-1c-1-1 0-2-1-3l1-3 1 1z" class="t"></path><path d="M740 300l1 1c0 2 0 4-1 5-1-1 0-2-1-3l1-3z" class="AU"></path><path d="M723 291l1-3h1l1 2c1 2 2 5 2 7h0c1 1 1 2 2 3h1v1c0 1 0 1 1 2 0-3-1-5 1-8 1 5 0 11 1 15 1 2 1 7 0 9h-1c-1-1-1-4-1-6l-2-6h0c-1-4-4-10-6-13-1-1-1-1-1-2v-1z" class="I"></path><path d="M719 275c1 0 2 0 4 1h0c3 1 4 4 5 6l1 2c0 1 0 2 1 3 0 1 0 2-2 3v1l2 6v1s1 1 1 2h-1c-1-1-1-2-2-3h0c0-2-1-5-2-7l-1-2h-1l-1 3v1c-1-1-1-1-1-2-2-3-8-7-11-8-2-1-2 0-4-1h0c2-1 3-2 5-3 1 0 2-1 3-1 1-1 2-2 4-2z" class="Aa"></path><path d="M723 291c0-2 0-3-1-4v-1l1-1c1 0 1 1 1 2l1 1h-1l-1 3z" class="g"></path><path d="M719 275l-1 2 1 1h-1c-2 1-3 1-5 2h-2c0-1 0-1 1-2 1 0 2-1 3-1 1-1 2-2 4-2z" class="h"></path><path d="M719 275c1 0 2 0 4 1h0c3 1 4 4 5 6l1 2c0 1 0 2 1 3 0 1 0 2-2 3v1c-1-4-3-7-4-10h0c-2-2-3-2-5-3l-1-1 1-2z" class="s"></path><defs><linearGradient id="y" x1="739.035" y1="305.543" x2="773.463" y2="303.426" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#212c34"></stop></linearGradient></defs><path fill="url(#y)" d="M775 266c2-1 4-4 6-4-1 1-4 3-5 4 0 3-3 6-4 9h0l-3 3-1 3-3 4-1 1c-3 3-6 5-7 8l-2 4 3-1c-1 3-4 6-6 9v3l-1 2h2c0 2-1 3-1 5 0 1 0 1-1 2 0 1-1 1-1 3 0 1-1 1-1 3l-2 3c-1 1-1 2-1 2l-1 1-1 2c-1 1-1 2-1 3-1 1-1 3-2 5h0l-1-2 1-15v-6l-1 1v-1-1c1-3 1-5 1-7v-3c1 0 2-1 3-2l1-2c-2-1-2-1-3-3 1-2 2-4 2-6h1c1-3 5-6 7-9 1-1 3-3 4-3l3-2c4-2 7-5 10-8 2-2 4-4 6-5z"></path><path d="M746 314l3-1c-1 2-1 3-2 4h-1v-3z" class="M"></path><path d="M741 306c1 0 2-1 3-2l-1 8-2-3v-3z" class="L"></path><path d="M741 309l2 3-2 11v-6l-1 1v-1-1c1-3 1-5 1-7z" class="AY"></path><path d="M752 306v3l-1 2s-2 1-2 2l-3 1v-2c2-1 4-4 6-6z" class="h"></path><path d="M755 298l3-1c-1 3-4 6-6 9-2 2-4 5-6 6 1-3 2-7 4-10 1-1 3-3 5-4z" class="f"></path><path d="M766 279l2-1h1 0l-1 3-3 4-1 1c-3 3-6 5-7 8l-2 4c-2 1-4 3-5 4 0-2 2-3 3-6 1-4 4-6 7-9 1-2 3-3 4-4 0-2 1-3 2-4z" class="Aa"></path><path d="M766 279l2-1h1 0l-1 3-3 4-1 1c0-3 3-5 2-7z" class="h"></path><path d="M775 266c2-1 4-4 6-4-1 1-4 3-5 4-5 4-9 8-14 13-4 4-10 8-13 13-2 2-3 5-4 7v3c-2-1-2-1-3-3 1-2 2-4 2-6h1c1-3 5-6 7-9 1-1 3-3 4-3l3-2c4-2 7-5 10-8 2-2 4-4 6-5z" class="U"></path><path d="M744 293h1v3 3 3c-2-1-2-1-3-3 1-2 2-4 2-6z" class="l"></path><path d="M761 250l21-7h3c-2 0-7 2-8 3l-2 2h0 2c0 1 1 1 2 1h1v1h-1c-1 0-2 1-3 2h0l-1 2c-1 1-3 2-4 3v1l2-1h3 0c1-1 2-1 3-1-1 2-2 4-4 5h0l-3 3-3 3h1c1-1 3-1 5-1-2 1-4 3-6 5-3 3-6 6-10 8l-3 2c-1 0-3 2-4 3-2 3-6 6-7 9h-1v-1c-1 1-1 1-1 2h-2l-1 1h0l-1 1-1-1c1-2 2-3 2-5l-1-1c-1-1-1-2-2-2 0-2-1-3-1-4 0-2 0-4-1-5h-1v-2-5-2l1-1h0c1 0 2-1 3-1l-2-4h1l3-2c-2-2-4-2-6-2 4 0 7-1 11-3 1 0 3-1 5-1l11-5z" class="O"></path><path d="M745 275v1l1-1 2-1c-1 3-3 5-5 6h-1c1-2 2-4 3-5zm22-14h1c1-1 1-1 3 0l-6 4h-1c-1 0-1 0-1 1l-6 3c-2 1-3 2-5 3h-2c2-2 5-5 8-6 1 0 2-1 3-2l6-3z" class="p"></path><defs><linearGradient id="z" x1="777.624" y1="258.852" x2="764.876" y2="263.148" xlink:href="#B"><stop offset="0" stop-color="#282e35"></stop><stop offset="1" stop-color="#3d4251"></stop></linearGradient></defs><path fill="url(#z)" d="M776 257h0c1-1 2-1 3-1-1 2-2 4-4 5h0c-3 1-6 4-9 5l-1-1 6-4c-2-1-2-1-3 0h-1v-1h1c1-1 2-1 3-2l2-1h3z"></path><path d="M773 257h3c-2 1-3 2-5 4-2-1-2-1-3 0h-1v-1h1c1-1 2-1 3-2l2-1z" class="AE"></path><path d="M759 264h2c-1 1-2 2-3 2-3 1-6 4-8 6 0 1-1 2-2 2l-2 1-1 1v-1l3-6 3-3h2c1 0 4-2 5-2h0 1z" class="Ac"></path><path d="M751 266h2c1 0 4-2 5-2h0c-1 1-2 2-4 3l-1 1c-1 1-2 2-4 2l-1-1 3-3z" class="f"></path><defs><linearGradient id="AA" x1="763.796" y1="265.26" x2="761.533" y2="276.605" xlink:href="#B"><stop offset="0" stop-color="#444d59"></stop><stop offset="1" stop-color="#595b6e"></stop></linearGradient></defs><path fill="url(#AA)" d="M765 265l1 1c3-1 6-4 9-5l-3 3-3 3h1c1-1 3-1 5-1-2 1-4 3-6 5-3 3-6 6-10 8h0v-2h-1l1-3-1-2c1-3 4-5 6-7h1z"></path><path d="M769 267h1c1-1 3-1 5-1-2 1-4 3-6 5l-2-1-1 1c-1 1-2 1-3 1 0-2 4-4 6-5z" class="AE"></path><defs><linearGradient id="AB" x1="766.272" y1="248.162" x2="764.18" y2="267.278" xlink:href="#B"><stop offset="0" stop-color="#0e1613"></stop><stop offset="1" stop-color="#2c2f3d"></stop></linearGradient></defs><path fill="url(#AB)" d="M779 249h1v1h-1c-1 0-2 1-3 2h0l-1 2c-1 1-3 2-4 3v1c-1 1-2 1-3 2h-1v1l-6 3h-2-1 0c-1 0-4 2-5 2h-2l5-6c4-1 7-4 10-7l3-1 6-2 4-1z"></path><path d="M770 256v-1l6-3-1 2c-1 1-3 2-4 3l-1-1z" class="r"></path><path d="M770 256l1 1v1c-1 1-2 1-3 2h-1v-1h1-1c-1 0-2 1-3 1h-1c1-1 2-2 4-2 1-1 2-1 3-2z" class="g"></path><path d="M763 260h1c1 0 2-1 3-1h1-1v1 1l-6 3h-2c1-2 2-3 4-4z" class="f"></path><path d="M765 253h1c-3 3-6 6-10 7l-5 6-3 3-3 6c-1 1-2 3-3 5s-1 3-2 5l-1 4c-1-1-1-2-2-2 0-2-1-3-1-4 1-1 1-3 1-4 2-2 3-3 4-5 4-6 8-14 15-16 1 0 2-1 2-2 2 0 6-2 7-3z" class="AY"></path><path d="M737 287c2-2 2-6 3-9 1-1 2-3 3-5 2-1 3-3 5-5 1-3 5-7 8-8l-5 6-3 3-3 6c-1 1-2 3-3 5s-1 3-2 5l-1 4c-1-1-1-2-2-2z" class="l"></path><path d="M753 258c2-1 4-1 5-2 0 1-1 2-2 2-7 2-11 10-15 16-1 2-2 3-4 5 0 1 0 3-1 4 0-2 0-4-1-5h-1v-2-5-2l1-1h0c1 0 2-1 3-1h0c1-1 3-2 4-3 2-1 3-2 5-3 0 0 2-1 3-1 1-1 2-1 3-2z" class="Ab"></path><path d="M747 261s2-1 3-1l-13 13c0 1 0 1-1 2v1h-1v-1-3l-1-1h0v-2l1-1h0c1 0 2-1 3-1h0c1-1 3-2 4-3 2-1 3-2 5-3z" class="f"></path><path d="M738 267h0c1-1 3-2 4-3 2-1 3-2 5-3-3 3-6 5-9 8 0 1-1 2-2 3h-1v-1-3c1 0 2-1 3-1z" class="V"></path><path d="M758 272l1 2-1 3h1v2h0l-3 2c-1 0-3 2-4 3-2 3-6 6-7 9h-1v-1c-1 1-1 1-1 2h-2l-1 1h0l-1 1-1-1c1-2 2-3 2-5l-1-1 1-4c5-1 7-8 11-9 1-1 2-1 3-2h1 1l2-2z" class="AY"></path><path d="M746 284c1-3 3-5 6-7l1 2-5 6v-2l-2 1z" class="t"></path><path d="M746 284l2-1v2l-4 7c-1 1-1 1-1 2h-2l-1 1h0l-1 1-1-1c1-2 2-3 2-5 1 0 2-2 4-3l2-3z" class="U"></path><path d="M740 290c1 0 2-2 4-3l-3 7-1 1h0l-1 1-1-1c1-2 2-3 2-5z" class="j"></path><defs><linearGradient id="AC" x1="758.193" y1="277.33" x2="752.336" y2="277.669" xlink:href="#B"><stop offset="0" stop-color="#5e6273"></stop><stop offset="1" stop-color="#777d87"></stop></linearGradient></defs><path fill="url(#AC)" d="M758 272l1 2-1 3h1v2h0l-3 2c-1 0-3 2-4 3-2 3-6 6-7 9h-1v-1l4-7 5-6c0-1 1-1 1-2l1-3h1l2-2z"></path><path d="M761 250l21-7h3c-2 0-7 2-8 3l-2 2h0 2c0 1 1 1 2 1l-4 1-6 2-3 1h-1c-1 1-5 3-7 3-1 1-3 1-5 2-1 1-2 1-3 2-1 0-3 1-3 1-2 1-3 2-5 3-1 1-3 2-4 3h0l-2-4h1l3-2c-2-2-4-2-6-2 4 0 7-1 11-3 1 0 3-1 5-1l11-5z" class="B"></path><path d="M768 249l9-3-2 2h0 2c0 1 1 1 2 1l-4 1c-1-1-1-1-2-1l-1 1c-2 0-2 0-4-1z" class="z"></path><path d="M768 249c2 1 2 1 4 1l1-1c1 0 1 0 2 1l-6 2-3 1h-1c-1-1-1-1-1-2l4-2z" class="r"></path><path d="M756 254l8-3c0 1 0 1 1 2-1 1-5 3-7 3-1 1-3 1-5 2 2-1 3-2 3-4z" class="g"></path><path d="M756 254c0 2-1 3-3 4-1 1-2 1-3 2-1 0-3 1-3 1-2 1-3 2-5 3-1 1-3 2-4 3h0l-2-4h1l3-2 16-7z" class="M"></path><defs><linearGradient id="AD" x1="772.529" y1="217.91" x2="770.165" y2="251.427" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#262a2f"></stop></linearGradient></defs><path fill="url(#AD)" d="M746 222l-2 2 1 1v1c4 1 11 0 15 0l65-5v1h1 3 2c-1 1 0 1-1 1l-1 1h0c-2 0-4 1-6 2-1 0-3 1-4 2-1 0-1 0-2 1h-1c-1 0-1 0-1 1l-1 1h-2c0 1-1 1-2 1l-1 1c-1 0-1 0-2 1-2 0-5 2-7 3h-2l-4 2h0c-1 1-2 1-3 2h-3v1h-2l-1 1h-3l-21 7-11 5c-2 0-4 1-5 1-4 2-7 3-11 3 2 0 4 0 6 2l-3 2h-1l2 4c-1 0-2 1-3 1h0l-1 1v-3l-1 4-1 1-1 6c-1 0-2-2-3-3 1 3 1 5 1 7v3l-1-2c-1-2-2-5-5-6h0c-2-1-3-1-4-1-2 0-3 1-4 2-1 0-2 1-3 1 1-2 4-3 5-6-1-3-3-6-6-9-1-2-3-3-4-5h0c-2-5-10-10-14-12-3-2-8-5-9-8-1-2-2-3-2-4 1-1 5-1 7-1 4 1 8 2 12 4v-1-1h3 0c2-1 6-1 9-1 2-1 4-1 7-2 2 0 4-2 6-2l12-3-13-2v-1h-7v-2c3 0 6 0 9 1 4 0 11 3 16 1l2-2h1z"></path><path d="M785 234h0l2-1 5-1h2c1 0 1 0 2 1h1c1 0 1 0 2 1h-1-2c-1-1-1-1-3-1-1 2-1 2-3 3h0l-2-1h-2l-1-1z" class="s"></path><path d="M793 233c-1 2-1 2-3 3h0l-2-1c2-1 3-1 5-2z" class="z"></path><path d="M760 231c8-1 16-1 24-1h-1l-18 1h1c2 0 2 0 4 1h2l1-1s1 0 2 1 1 1 1 3v1h-1l-9 3h0l1-2c0-1-1-2-1-2l-3-3h0l-3-1z" class="r"></path><path d="M763 232h5v1h-1l1 1-2 1-3-3zm8 1c1-1 2 0 3 0h0l-2 1c0 1 1 1 1 2-1 0-2 0-3-1l-1-1h2v-1z" class="g"></path><path d="M768 233h3v1h-2l1 1c1 1 2 1 3 1l2-1v1l-9 3h0l1-2c0-1-1-2-1-2l2-1-1-1h1z" class="f"></path><path d="M738 227h10l-2 1h0c-8 0-15 2-23 5l-4 2h-2-3l-1 1-3 3-1 4c-2-3-5-4-8-6v-1-1h3 0c2-1 6-1 9-1 2-1 4-1 7-2 2 0 4-2 6-2l12-3z" class="B"></path><path d="M704 235c3 0 8-1 10 0l-1 1-3 3-1 4c-2-3-5-4-8-6v-1-1h3 0z" class="j"></path><path d="M712 236h1l-3 3h-1c-2 0-3-1-5-2 3 0 6 1 8-1z" class="L"></path><path d="M704 235c3 0 8-1 10 0l-1 1h-1-6c-2 0-3 1-5 0v-1h3 0z" class="AY"></path><path d="M778 235h0l1-1h3c1-1 2 0 3 0l1 1h2l2 1-11 4c-3 1-6 3-10 4l-6 2c-2 1-4 1-5 2s-2 1-3 1l2-3 1-1v-1c1 0 1 0 2-1-1 0-1-1-1-1l2-1c2-1 3-1 4-2h1 0l9-3h1l2-1z" class="g"></path><path d="M786 235h2l2 1-11 4s1 0 2-1l1-1s1 0 2-1l-6 1 8-3z" class="V"></path><path d="M776 236l2-1-2 3h1c-4 2-7 2-10 3l-1-2 9-3h1z" class="M"></path><path d="M778 235h0l1-1h3c1-1 2 0 3 0l1 1c-2 1-5 2-8 3h-1-1l2-3z" class="d"></path><path d="M766 239h0l1 2-2 1v1h-1l1 1c2-1 2-1 4 0l-6 2c-2 1-4 1-5 2s-2 1-3 1l2-3 1-1v-1c1 0 1 0 2-1-1 0-1-1-1-1l2-1c2-1 3-1 4-2h1z" class="I"></path><path d="M760 243h2c0 1-2 2-3 2h-1v-1c1 0 1 0 2-1z" class="AE"></path><path d="M766 239h0l1 2-2 1c-1 0-2 0-3 1h-2c-1 0-1-1-1-1l2-1c2-1 3-1 4-2h1z" class="h"></path><path d="M746 222l-2 2 1 1v1c4 1 11 0 15 0l65-5v1c-10 2-19 2-29 3l-41 2c-2 1-7 1-9 1h0l2-1h-10l-13-2v-1h-7v-2c3 0 6 0 9 1 4 0 11 3 16 1l2-2h1z" class="AL"></path><path d="M720 245h-1-3v-2c1-2 4-4 6-6 2-1 3-2 5-2l3-1c4-2 7-1 11-1l15-1-6 2c-1 1-3 1-5 2h-1l-2 1-8 3c-4 1-7 4-12 4h0l-2 1z" class="AD"></path><path d="M737 235c2-1 4-1 6-1l-1 1c1 1 1 1 2 1l-2 1-8 3c-4 1-7 4-12 4v-1h-1c-1 0-2-1-2-2l1-1h1c3 0 4-2 7-2l9-3z" class="AF"></path><path d="M737 235c-3 3-8 4-11 5-1 1-3 1-4 1l-1-1c3 0 4-2 7-2l9-3z" class="AE"></path><path d="M756 232c1 0 3 0 4-1l3 1h0l3 3s1 1 1 2l-1 2h-1c-1 1-2 1-4 2l-2 1s0 1 1 1c-1 1-1 1-2 1v1l-1 1-2 3c1 0 2 0 3-1s3-1 5-2c-2 1-2 1-2 3v1l-11 5c-2 0-4 1-5 1h-1-2c-3 0-4 1-7 1-1 0-2 0-3-1l1-1h0v-1h-4l-1-1h-3l-1 1-2-1c-1-1-2-1-4-1l-3-2c1-1 2-1 2-2l3-3 2-1h0c5 0 8-3 12-4l8-3 2-1h1c2-1 4-1 5-2l6-2z" class="Ac"></path><path d="M725 253l-1-2c-1-1 0-1-1 0h-1c-2-1-2-1-3-2 1-2 2-3 3-4v3 1c1 1 2 1 3 1l2 2h3 3v1h-5-3z" class="I"></path><path d="M720 245l2-1 1 1v1l-1-1c-1 1-2 2-3 4 1 1 1 1 3 2h1c1-1 0-1 1 0l1 2-1 1-2-1c-1-1-2-1-4-1l-3-2c1-1 2-1 2-2l3-3z" class="g"></path><path d="M745 246l2 1c-2 1-2 1-3 3l1 1c-1 1-2 1-4 2l-1 1c-1 0-1 1-2 1l-2-1h-1v-1c2-1 3-3 6-4h1l-1-1v-1c2 0 2 0 4-1z" class="AL"></path><path d="M736 254c2-1 5-3 7-3-1 0-2 1-2 2l-1 1c-1 0-1 1-2 1l-2-1z" class="I"></path><path d="M737 244c1 0 1 0 2 1h3c1-1 2-1 3-1l-3 2-1 1v1c-1 1-3 1-5 2h-4 0-3v-1c3-1 6-3 8-5z" class="Ag"></path><path d="M732 250v-1c1-1 2-1 4-2l1 1c0 1 0 1-1 2h-4z" class="AU"></path><path d="M722 244c5 0 8-3 12-4l-2 3-3 2c-1 0-2 1-2 2 1 0 1 1 2 1v1 1h-2-2c-1 0-2 0-3-1v-1-3l1 1v-1l-1-1h0z" class="p"></path><path d="M727 250l-1-1v-1l-2-1v-1c2 0 3-1 5-1-1 0-2 1-2 2 1 0 1 1 2 1v1 1h-2z" class="AZ"></path><path d="M747 241c1-1 2-1 3-2 3 2 7 0 10 1h1v1l-2 1s0 1 1 1c-1 1-1 1-2 1-2 1-4 1-6 1h0-1-2c-1 0-1 1-2 1v1l-2-1c-2 1-2 1-4 1l1-1 3-2c-1 0-2 0-3 1h-3c2-2 5-3 8-4z" class="p"></path><path d="M755 243c2-1 2-1 4-1 0 0 0 1 1 1-1 1-1 1-2 1-2 1-4 1-6 1h0v-1c1-1 1-2 2-2l1 1z" class="f"></path><path d="M754 242l1 1c-1 1-1 1-3 1 1-1 1-2 2-2z" class="Ab"></path><path d="M750 242h1c1-1 2-1 3 0-1 0-1 1-2 2v1h-1-2c-1 0-1 1-2 1v1l-2-1c-2 1-2 1-4 1l1-1 3-2c2 0 4-1 5-2z" class="Ae"></path><path d="M750 242v1c-1 0-2 1-3 2l-2 1c-2 1-2 1-4 1l1-1 3-2c2 0 4-1 5-2z" class="AU"></path><path d="M742 237h7l2 2h-1c-1 1-2 1-3 2-3 1-6 2-8 4-1-1-1-1-2-1-2 2-5 4-8 5v-1c-1 0-1-1-2-1 0-1 1-2 2-2l3-2 2-3 8-3z" class="O"></path><path d="M744 239h2v1c0 1 0 0 1 1-3 1-6 2-8 4-1-1-1-1-2-1 1 0 2-1 3-2 1 0 2-2 4-2h0v-1z" class="AU"></path><path d="M742 237h7l2 2h-1c-1 1-2 1-3 2-1-1-1 0-1-1v-1h-2c-3 0-4 1-6 1-3 1-4 3-6 3l2-3 8-3z" class="AL"></path><path d="M756 232c1 0 3 0 4-1l3 1h0l3 3s1 1 1 2l-1 2h-1c-1 1-2 1-4 2v-1h-1c-3-1-7 1-10-1h1l-2-2h-7l2-1h1c2-1 4-1 5-2l6-2z" class="AE"></path><path d="M745 236h1 9v3h0l-1-1h-1c-1 1-2 1-2 1l-2-2h-7l2-1h1z" class="p"></path><path d="M756 232c1 0 3 0 4-1l3 1h0l3 3s1 1 1 2l-1 2h-1c-1 1-2 1-4 2v-1h0v-1h0l-2-1h-1v-1l3-2h0-1c-1 0-2-1-3 0-1 0-2-1-3-1h-4l6-2z" class="f"></path><path d="M756 232c1 0 3 0 4-1l3 1c-1 1-2 2-3 2 0 0-1 0-2-1v1h-4-4l6-2z" class="g"></path><path d="M766 235s1 1 1 2l-1 2h-1c-1 1-2 1-4 2v-1h0v-1h0l-2-1c1-1 2-1 3-1l2-2h0 2z" class="I"></path><path d="M761 240c1-1 2-2 4-2v1c-1 1-2 1-4 2v-1h0z" class="Ai"></path><path d="M758 244v1l-1 1-2 3c1 0 2 0 3-1s3-1 5-2c-2 1-2 1-2 3v1l-11 5c-2 0-4 1-5 1h-1-2c-3 0-4 1-7 1-1 0-2 0-3-1l1-1h0l2-1h1l2 1c1 0 1-1 2-1l1-1c2-1 3-1 4-2l-1-1c1-2 1-2 3-3v-1c1 0 1-1 2-1h2 1 0c2 0 4 0 6-1z" class="AF"></path><path d="M758 244v1l-1 1-2 3-10 4h-4c2-1 3-1 4-2l-1-1c1-2 1-2 3-3v-1c1 0 1-1 2-1h2 1 0c2 0 4 0 6-1z" class="Ab"></path><path d="M758 244v1l-1 1-6 2v-1c0-1 1-1 1-2 2 0 4 0 6-1z" class="p"></path><path d="M747 247v-1c1 0 1-1 2-1h2 1 0c0 1-1 1-1 2v1c-2 1-3 2-5 2 0 0-1 0-1 1l-1-1c1-2 1-2 3-3z" class="Ab"></path><path d="M751 247v1c-2 1-3 2-5 2 0 0-1 0-1 1l-1-1c1-1 2-2 3-2l4-1z" class="Ag"></path><path d="M682 234c1-1 5-1 7-1 4 1 8 2 12 4 3 2 6 3 8 6l1-4 3-3 1-1h3c-2 2-5 4-5 7s0 4 2 6c0 1 1 1 1 2l3 2c2 0 3 0 4 1l2 1 1-1h3l1 1h4v1h0l-1 1c1 1 2 1 3 1 3 0 4-1 7-1h2 1c-4 2-7 3-11 3 2 0 4 0 6 2l-3 2h-1l2 4c-1 0-2 1-3 1h0l-1 1v-3l-1 4-1 1-1 6c-1 0-2-2-3-3 1 3 1 5 1 7v3l-1-2c-1-2-2-5-5-6h0c-2-1-3-1-4-1-2 0-3 1-4 2-1 0-2 1-3 1 1-2 4-3 5-6-1-3-3-6-6-9-1-2-3-3-4-5h0c-2-5-10-10-14-12-3-2-8-5-9-8-1-2-2-3-2-4z" class="s"></path><path d="M696 240c3 1 10 4 11 7h0c0 2 0 1-1 2l5 5h-1-1c-1-2-3-4-4-5l-9-9z" class="M"></path><path d="M686 237c4 1 7 2 10 3l9 9c1 1 3 3 4 5h-1 1c2 1 2 2 3 4l-4-3c-4-3-7-7-11-10-2-1-3-2-5-3-1 0-2-1-3-2h0l-3-3z" class="AD"></path><path d="M714 235h3c-2 2-5 4-5 7s0 4 2 6c0 1 1 1 1 2l3 2c2 0 3 0 4 1l2 1 1-1h3l1 1h4v1h0l-1 1c1 1 2 1 3 1 3 0 4-1 7-1h2 1c-4 2-7 3-11 3 2 0 4 0 6 2l-3 2h-1l2 4c-1 0-2 1-3 1h0l-1 1v-3l-1-1-2-2c-2 0-3 1-5 2 1-1 1-2 1-2 0-1-1-2-2-3-4-4-8-7-12-11-2-2-3-4-4-6l1-4 3-3 1-1z" class="AT"></path><path d="M733 265v-3l1-1c1 1 1 1 2 1l1 1h-1l2 4c-1 0-2 1-3 1h0l-1 1v-3l-1-1z" class="U"></path><path d="M735 268c0-2 0-3 1-5l2 4c-1 0-2 1-3 1h0z" class="r"></path><path d="M718 252c2 0 3 0 4 1l2 1 1-1h3l1 1h4v1h0l-1 1c1 1 2 1 3 1 3 0 4-1 7-1h2 1c-4 2-7 3-11 3-7-1-11-2-16-7z" class="I"></path><path d="M725 253h3l1 1c-2 1-2 1-5 0l1-1z" class="AE"></path><path d="M682 234c1-1 5-1 7-1h-4c-1 1-1 2-1 3 1 0 1 1 2 1l3 3h0c1 1 2 2 3 2 2 1 3 2 5 3 4 3 7 7 11 10l4 3c3 3 8 8 9 12 1 0 3-3 4-4l1 1c-1 1-3 2-4 4h3c0 1 2 3 3 3 1 3 1 5 1 7v3l-1-2c-1-2-2-5-5-6h0c-2-1-3-1-4-1-2 0-3 1-4 2-1 0-2 1-3 1 1-2 4-3 5-6-1-3-3-6-6-9-1-2-3-3-4-5h0c-2-5-10-10-14-12-3-2-8-5-9-8-1-2-2-3-2-4z" class="AF"></path><defs><linearGradient id="AE" x1="681.415" y1="549.738" x2="778.357" y2="594.124" xlink:href="#B"><stop offset="0" stop-color="#cac3c0"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#AE)" d="M810 282c1-3 3-6 5-8v1c0 1 1 2 0 3v4l1 1v2c1-3 1-4 2-5l1 2 1 1 1 3h0l-1 1 1 3v7 2 2-1c0 2 0 3-1 5l1 11h-4l-7 1h-1 0l-3 1h-2-2l-1 1c0 1 0 2 1 3l-1 1-1-2c0 6 1 9 3 15l2 4c0 1 1 2 3 3h1l-2 2-5 8-13 22-5 8c0 1-1 3-1 4h-2-1l1 1c-1 1-1 2-1 3v1l-1 3c-1 2-1 4-2 6l-1 5c1-1 2-1 2-2 0 1 1 2 1 3v-1l1-1c0 2 0 3 1 4 1 0 1 1 2 1 1 1 2 1 2 2l2 2h0c1 1 1 1 1 2 1 0 1 0 2-1v2c-2 2-4 4-5 6 0 1 0 2-1 2v1 3c-1 1-1 3-1 5-1 3-1 5-1 8l1 1-1 8-1 1-1 1v9 9 7 3c0 1 0 3 1 5l1-1v1c1 0 2 0 2 1v2h0l-2 1-1-1-2-2-3-2v2l1 2c-2 1-2 0-3 2h0c1 6-2 11-3 17-1 2-1 5-2 8 0 4-2 7-1 12v6h0-2c-1 3-2 5-1 8 1 2-1 3-1 5-1 1-1 3-2 5h0v2c0 2-1 3-1 4v1c1 2 1 3 1 4v1c-2 1-2 8-3 10-1 1-1 3-2 4-1 3-4 4-4 7h-1c-2 2-2 2-2 5l-1 1v1c0 1-1 2-1 4l1 1-2 2c-1-1-2-1-3-2h-1-1c-1-1-2-1-3-2v-5c-1 2-1 3-2 5h-1l-6 3c-3 2-4 5-5 8 0 0 0 1-1 1v1 1c-1 1-2 3-3 5l-2 2-2 4-2 2c-2 3-3 5-5 7-1 1 0 0-1 2s-4 3-5 5c-2 2-4 3-6 6h0c-1 1-1 1-1 2-2 9-13 16-20 22l-2 3c0 1-2 2-3 3-2 2-4 4-5 6s-2 3-3 4c1 3 1 4 3 6 1 1 4 3 6 4l16 18c1 4 21 18 26 23h0l-27-21-9-8c-2-1-2-2-4-3v2c-1 1-2 1-2 1l-6 3-6 3-9 5c-2 0-4 1-5 2l-3 1c-3 1-6 1-9 3l-4 2v1l-8 5c-1 2-3 2-4 4-2 1-3 2-4 3-2 0-3 2-5 2l-1 2-1 1h0-2c-1 1-1 1-3 1v-1h-2l-17 12-7 6-1-1 5-13 1-1c0 1 1 1 2 1l1-2c-1-1 0-2 0-4v-2c0-4 2-7 3-10l2-1c3-5 3-10 8-14 1-1 2-1 2-3l-1-1c-1 1-2 1-3 1v-1l1-3 2-1v2h2c1-2 2-3 4-4l1-1v-1c2 0 2-1 3-2h-1l13-9c3-2 6-5 9-7 4-3 8-5 10-10 5-8 12-13 18-20 4-5 8-11 12-16l1-1c1 1 1 1 2 1l1-2c2-3 7-7 9-10l10-12c17-20 27-45 35-70l4-13c1-2 1-4 2-6h0c1-1 1-2 1-3v-1c0-1 0-1 1-2v-3c1-1 1-3 2-5h0l3-4c2-5 5-10 9-13 1-1 1-4 2-5h-1v-1h1c-1-2-4-3-5-6l-4-7-1-2c-1-3-3-7-3-10s1-7 1-10c1-3 1-7 1-10l-1-20c0-2-1-6-2-8 1-4-1-9-1-12l1-1h1c0 1 0 3 1 4 1-1 0-5 0-7 1-1 1-1 1-2v-1l1-1c2-1 3-3 4-5v-1l2-1c1-1 1-2 2-2v-1-2c2-1 2-1 3-2v-3-1c1-1 1-2 2-3v-1l1-2c1-2 3-4 4-7 0 0 0-1 1-2v2l1-1v-1c1-2 3-3 3-5s1-2 2-3 0-2 1-3l2-2c0-1 0-2 1-3v-3c0-1 0-1-1-2 0-2 2-4 2-6 1-1 1-1 1-2s0-1 2-1v-1l1-1c-1-1 0-2 0-2h1c0-1 1-2 1-3 2-4 3-7 6-9 1-2 2-3 3-5s3-4 5-7c0 0 0-1 1-2l1-1c0-1 1-1 2-2s1-2 2-4c2-1 2-3 4-4l6-6 2-1z"></path><path d="M717 594c1-1 1-3 2-4l1 1-2 3h-1z" class="F"></path><path d="M674 650l1 1h0v1c-1 1-2 3-4 3v-2l-2 1h-1c-1-1-1-1-2-1 1-1 1 0 3 0h0c3-1 3-1 5-3z" class="AT"></path><path d="M620 698h2c-1 1-2 3-4 4s-2 2-4 3h-1 0 0l-2-1c2-1 4-2 5-4l4-2z" class="N"></path><path d="M754 488l6 9h-7-2c1-1 3-1 4-1v-1c-1-2-1-4-1-6v-1z" class="T"></path><path d="M749 420h1c1-1 1-3 1-4 0 2 1 4 0 6-1 3 0 7-1 10l-1-4v-4c-1 1-1 0-1 1 0-1-1-3 0-5h1z" class="D"></path><path d="M641 684c1-1 1-2 2-3l1-1c1-1 2-3 2-4 1-2 1-2 2-3v-1l1-1h0v1c-1 1-1 3-1 4 0 4-3 7-7 9v-1z" class="U"></path><path d="M743 468l2 1v-4c2 2 2 12 2 15l-4-7-1-2 1-2h0v-1z" class="R"></path><path d="M677 666l-2 3c0 1-2 2-3 3-2 2-4 4-5 6s-2 3-3 4l1-3c0-6 8-10 12-13z" class="U"></path><path d="M717 594h1l-7 15c-3 6-8 13-12 18h-1-1c3-3 5-7 7-11 5-7 9-14 13-22z" class="S"></path><path d="M741 441h1c0 2 0 4-1 6v5c0 1 0 1-1 2 0 2 1 4 0 6v1 2c1 1 1 2 1 3v-1c1-1 1 0 1-1v-2h0c-1-2 0-4-1-5 0-1 0-2 1-3l-1-2 1-1v-1l1 1h0c0 1 0 2 1 2 0 1 0 2-1 3 0 3 1 6 0 9v3 1h0l-1 2c-1-3-3-7-3-10s1-7 1-10c1-3 1-7 1-10z" class="K"></path><path d="M759 397l-1-2v-1c0-2 0-3 1-5l2 4c0 1-1 2 0 4h0c-1 2-1 5 0 6 0 2-1 4-1 6v2h0v2c0 1 0 2 1 3 0 2 0 4 1 7v2c0 2 1 4 1 6h0c1 2 0 2 0 3 0-3-1-6-2-9v-3l-1-1v-1-2-1l-1-2c-1-1 0-6 0-8h0v-2h0v-1c1-2 1-6 0-7z" class="D"></path><path d="M643 678c-1 4-7 4-7 8h1c1-1 1 0 2-1h0c1-1 1-1 2-1v1c-6 3-13 8-19 13h-2-1v-2c2-2 6-4 8-6 4-2 8-5 11-8 1 0 2-1 3-2l2-2z" class="j"></path><path d="M751 497h2v1c-1 1-1 2-2 3 0-1 1-2 1-2v-1c-2 0-3 2-4 4-2 3-4 6-5 10s-3 8-4 12h0c-1 1-1 2-1 2-2 3-2 8-3 11v1 1-3c0-1 0-1 1-2v-3c0-1 0-2 1-3 0-3-1-6 0-8 0-1 0-2 1-3 1-2 2-6 3-9h2 0l1-3v-1c1 0 2-1 2-1l2-3 3-3z" class="U"></path><path d="M752 487c1 0 1 1 2 1v1c0 2 0 4 1 6v1c-1 0-3 0-4 1l-3 3-2 3s-1 1-2 1v1l-1 3h0-2l-3 9v-1h-1v-1-2l1-1v-3l3-4c2-5 5-10 9-13 1-1 1-4 2-5z" class="C"></path><defs><linearGradient id="AF" x1="615.376" y1="704.175" x2="615.624" y2="687.326" xlink:href="#B"><stop offset="0" stop-color="#a7a19e"></stop><stop offset="1" stop-color="#cfcccc"></stop></linearGradient></defs><path fill="url(#AF)" d="M635 681c2 0 4-1 6-1-1 1-2 2-3 2-3 3-7 6-11 8-2 2-6 4-8 6v2h1l-4 2c-1 2-3 3-5 4h0l-1-2c-1 1-4 3-6 4h-1-2c1-2 2-3 4-4l1-1v-1c2 0 2-1 3-2h-1l13-9c3-2 6-5 9-7v1h-1s-1 1-1 2c1-1 1-1 2-1l4-2 1-1z"></path><path d="M635 681c2 0 4-1 6-1-1 1-2 2-3 2-3 0-4 1-7 2l3-2 1-1z" class="B"></path><path d="M619 696v2h1l-4 2c-1 2-3 3-5 4h0l-1-2c2-2 7-4 9-6z" class="t"></path><path d="M621 689c3-2 6-5 9-7v1h-1s-1 1-1 2c1-1 1-1 2-1l4-2-3 2c-4 2-7 5-11 8-3 2-7 3-11 6h0 0-1l13-9z" class="AT"></path><path d="M630 682c4-3 8-5 10-10 5-8 12-13 18-20 4-5 8-11 12-16l1-1c1 1 1 1 2 1l1-2c-2 5-5 9-8 13-2 3-3 6-5 8-6 5-12 8-16 16l-1 4-1 1v2h0l-2 2c-2 0-4 1-6 1l-1 1-4 2c-1 0-1 0-2 1 0-1 1-2 1-2h1v-1z" class="H"></path><path d="M635 681c2-2 5-3 7-6l3-4-1 4-1 1v2h0l-2 2c-2 0-4 1-6 1z" class="F"></path><defs><linearGradient id="AG" x1="596.529" y1="711.989" x2="606.718" y2="716.572" xlink:href="#B"><stop offset="0" stop-color="#908e8f"></stop><stop offset="1" stop-color="#b7b3b3"></stop></linearGradient></defs><path fill="url(#AG)" d="M610 702l1 2h0l2 1-3 3v4l1-2 1 1-2 1 1 1h-1c-1 1-4 4-5 4h0l-5 4c-2 1-4 3-6 4h0l-2 2v1l-2 1v-3c3-5 3-10 8-14 1-1 2-1 2-3l-1-1c-1 1-2 1-3 1v-1l1-3 2-1v2h2 2 1c2-1 5-3 6-4z"></path><path d="M592 728h-1v-1c0-2 1-3 2-4l1 2-2 2v1z" class="AQ"></path><path d="M594 725l3-3c1-1 1-2 3-3v2c-2 1-4 3-6 4z" class="t"></path><path d="M610 702l1 2c-2 2-4 3-6 4-1 1-1 1-1 2-1 1-2 2-4 2v1h-2v-1c1-1 2-1 2-3l-1-1c-1 1-2 1-3 1v-1l1-3 2-1v2h2 2 1c2-1 5-3 6-4z" class="AQ"></path><defs><linearGradient id="AH" x1="586.192" y1="729.658" x2="605.742" y2="732.831" xlink:href="#B"><stop offset="0" stop-color="#858384"></stop><stop offset="1" stop-color="#b8b3b0"></stop></linearGradient></defs><path fill="url(#AH)" d="M610 713l1 2c-2 2-4 4-6 5l-2 1 1 2c1-2 1-2 3-3h1 0v1 1l-1 1c-1 1-1 3-1 5h1 1l1-1v-1h2c0-1 1-1 1-1h1c1-1 1-2 2-2h1c-3 3-8 5-10 7-1 1-7 4-7 5l-6 3-8 5c-1-1 0-2 0-4v-2c0-4 2-7 3-10l2-1v3l2-1v-1l2-2h0c2-1 4-3 6-4l5-4h0c1 0 4-3 5-4z"></path><path d="M610 713l1 2c-2 2-4 4-6 5l-2 1 1 2c-2 1-4 2-5 3h-1c0-2 3-4 5-5l-1-1c-3 3-6 5-10 7l2-2h0c2-1 4-3 6-4l5-4h0c1 0 4-3 5-4z" class="N"></path><defs><linearGradient id="AI" x1="735.609" y1="408.874" x2="749.393" y2="414.334" xlink:href="#B"><stop offset="0" stop-color="#747578"></stop><stop offset="1" stop-color="#bfb6b2"></stop></linearGradient></defs><path fill="url(#AI)" d="M746 388c0 1 0 2 1 3v1c-1 5 0 13 0 19 1 3 0 6 1 9-1 2 0 4 0 5 0 2 1 5 0 6h0c0-1 0-3-1-4v-7h0v1c-1 2-2 3-3 5-1 3-1 5-2 8-1 2-1 4 0 7h-1l-1-20c0-2-1-6-2-8 1-4-1-9-1-12l1-1h1c0 1 0 3 1 4 1-1 0-5 0-7 1-1 1-1 1-2v-1l1-1c2-1 3-3 4-5z"></path><path d="M743 397h1l-1 2h0v-2zm-4 8v1h2l-1 1-1 1v-3z" class="b"></path><path d="M770 482l4 2h0l2 1v2l1 2c-2 1-2 0-3 2h0c1 6-2 11-3 17-1 2-1 5-2 8 0 4-2 7-1 12v6h0-2c-1 3-2 5-1 8 1 2-1 3-1 5-1 1-1 3-2 5h0v2c0 2-1 3-1 4v1c1 2 1 3 1 4v1c-2 1-2 8-3 10-1 1-1 3-2 4-1 3-4 4-4 7h-1c-2 2-2 2-2 5l-1 1v1c0 1-1 2-1 4l1 1-2 2c-1-1-2-1-3-2h-1-1c-1-1-2-1-3-2v-5c2-12 6-20 13-29 2-3 5-6 6-9 3-7 4-16 5-24l7-42v-4z" class="o"></path><path d="M770 482l4 2h0c-1 2-1 2-3 3h-1v-1-4z" class="b"></path><path d="M744 584c-1-1 0-1 0-2 1-3 4-9 6-11l3-3 1-1c0-1 1-1 1-2s0 0 1-1c0-1 0-1 1-2l5-10v2l-1 1c-1 3-3 8-4 12l-1-1c-3 3-5 7-8 11-1 2-4 5-4 7h0z" class="L"></path><path d="M762 552c0-3 1-6 1-9-1-1-1-1 0-2v1-4c1-1 1-1 1-2v-3l1-1v-3c1-1 1 0 1-1v-1c0-3 0-6 1-9 0-2 1-6 2-8 0-4 1-10 3-13v-1l-1-1c0-1 0-1 1-2h1v-3c-1-1-1-1 0-2l3-1 1 2c-2 1-2 0-3 2h0c1 6-2 11-3 17-1 2-1 5-2 8 0 4-2 7-1 12v6h0-2c-1 3-2 5-1 8 1 2-1 3-1 5-1 1-1 3-2 5zm-18 32c0-2 3-5 4-7 3-4 5-8 8-11l1 1c1-4 3-9 4-12l1-1c0 2-1 3-1 4v1c1 2 1 3 1 4v1c-2 1-2 8-3 10-1 1-1 3-2 4-1 3-4 4-4 7h-1c-2 2-2 2-2 5l-1 1v1c0 1-1 2-1 4l1 1-2 2c-1-1-2-1-3-2h-1-1c-1-1-2-1-3-2 1-1 1-2 2-4 1-1 2-5 3-7h0z" class="y"></path><path d="M746 583c1 2 1 3 1 5v1c-1 2-2 4-4 6v-2c-1-4 1-7 3-10z" class="AP"></path><path d="M744 584l2-2v1c-2 3-4 6-3 10v2l1 2h-1-1c-1-1-2-1-3-2 1-1 1-2 2-4 1-1 2-5 3-7h0z" class="U"></path><path d="M739 595c1-1 1-2 2-4 0 2 1 4 1 6-1-1-2-1-3-2z" class="C"></path><path d="M759 565c0 1 0 2-1 2 0 2-1 3-1 5-1 3-5 5-4 9h-1c-1 1-1 1-1 2l1 2c-2 2-2 2-2 5l-1 1v1c0 1-1 2-1 4l-1-1v-1c0-1 0-1 1-2v-3l1-1v-3l1-1v-2-1c1-2 1-2 2-2v-1l-1-1c3-4 5-8 8-12z" class="P"></path><path d="M761 559c1 2 1 3 1 4v1c-2 1-2 8-3 10-1 1-1 3-2 4-1 3-4 4-4 7h-1l-1-2c0-1 0-1 1-2h1c-1-4 3-6 4-9 0-2 1-3 1-5 1 0 1-1 1-2v-1c1-2 2-3 2-5z" class="K"></path><path d="M780 391v1l-1 3c-1 2-1 4-2 6l-1 5c1-1 2-1 2-2 0 1 1 2 1 3v-1l1-1c0 2 0 3 1 4 1 0 1 1 2 1 1 1 2 1 2 2l2 2h0c1 1 1 1 1 2 1 0 1 0 2-1v2c-2 2-4 4-5 6 0 1 0 2-1 2v1 3c-1 1-1 3-1 5-1 3-1 5-1 8l1 1-1 8-1 1-1 1v9 9 7 3c0 1 0 3 1 5l1-1v1c1 0 2 0 2 1v2h0l-2 1-1-1-2-2-3-2-2-1h0l-4-2c0-2 1-6 1-8 1-4 1-7 2-10 0-9 0-19-1-28-2-9-5-18-6-27 0-3 0-6 1-10 3-1 4-3 7-5 0-1 1-1 2-1 1-1 2-1 3-2h1z" class="R"></path><path d="M776 485v-1c1-2 1-4 2-6 0-2 0-6 1-8l1 1v7h-1c-1 1-2 3-2 4 0 2 1 4 2 5l-3-2z" class="U"></path><path d="M779 487c-1-1-2-3-2-5 0-1 1-3 2-4h1v3c0 1 0 3 1 5l1-1v1c1 0 2 0 2 1v2h0l-2 1-1-1-2-2z" class="B"></path><path d="M781 486l1-1v1c1 0 2 0 2 1v2c-2-1-2-2-3-3z" class="O"></path><path d="M774 412l1 1h0c0 2 2 3 3 4h0c1 1 1 2 1 4v1 3c-1 2-1 5-1 7-2-1-1-2-2-3-1-3-1-5-1-7-1-4-2-6-1-10z" class="B"></path><path d="M778 417c1 1 1 2 1 4v1 3c-2-2-2-3-3-5l2-3z" class="C"></path><path d="M776 406c1-1 2-1 2-2 0 1 1 2 1 3v-1l1-1c0 2 0 3 1 4 1 0 1 1 2 1 1 1 2 1 2 2l2 2h0c-3 3-6 5-8 8v-1c0-2 0-3-1-4h0c-1-1-3-2-3-4h0l-1-1v-2h1l1 1c-1-1 0-3 0-5z" class="U"></path><path d="M779 413h1c2 0 2 0 3 1v1l-2 1c-1-1-1-2-2-3z" class="B"></path><path d="M779 406l1-1c0 2 0 3 1 4 1 0 1 1 2 1 1 1 2 1 2 2h-1c-2 0-3-1-6 0 1-2 1-3 1-5v-1z" class="C"></path><path d="M776 406c1-1 2-1 2-2 0 1 1 2 1 3 0 2 0 3-1 5-1 0-1 0-2-1s0-3 0-5z" class="AP"></path><path d="M787 414c1 1 1 1 1 2 1 0 1 0 2-1v2c-2 2-4 4-5 6 0 1 0 2-1 2v1 3c-1 1-1 3-1 5-1 3-1 5-1 8l1 1-1 8-1 1-1 1c-1-6-3-14-2-20v-1c0-2 0-5 1-7v-3c2-3 5-5 8-8z" class="AK"></path><path d="M781 452l-1-7c0-4 1-7 1-10 1-4 1-7 3-10v1 3c-1 1-1 3-1 5-1 3-1 5-1 8l1 1-1 8-1 1z" class="l"></path><path d="M787 414c1 1 1 1 1 2-4 6-7 11-10 17h0v-1c0-2 0-5 1-7v-3c2-3 5-5 8-8z" class="O"></path><defs><linearGradient id="AJ" x1="768.752" y1="442.076" x2="774.627" y2="441.627" xlink:href="#B"><stop offset="0" stop-color="#8c807c"></stop><stop offset="1" stop-color="#a59c97"></stop></linearGradient></defs><path fill="url(#AJ)" d="M780 391v1l-1 3c-1 2-1 4-2 6l-2 3c0 2-1 3-2 4-1 3-1 7-1 10l1 1c1 3 0 5 1 7l2 23c1 9 2 20-1 29h-1v6l-4-2c0-2 1-6 1-8 1-4 1-7 2-10 0-9 0-19-1-28-2-9-5-18-6-27 0-3 0-6 1-10 3-1 4-3 7-5 0-1 1-1 2-1 1-1 2-1 3-2h1z"></path><path d="M780 391v1l-1 3c-1 2-1 4-2 6l-2 3c0-2 1-4 1-6 1-2 2-3 3-5v-2h1z" class="C"></path><path d="M771 474h1c0 2-1 3 0 4h0c0-2 0-3 1-4 0 0 0-1 1-1v3 2 6l-4-2c0-2 1-6 1-8zm-4-75c3-1 4-3 7-5 0-1 1-1 2-1 1-1 2-1 3-2v2c-1 2-2 3-3 5-1 1-2 2-3 4l-1 1-2 6c-1 1-1 2-1 3h0c0-2-1-3 0-5l-1-1c0 1-1 2-2 3 0-3 0-6 1-10z" class="AK"></path><path d="M767 399c3-1 4-3 7-5 0-1 1-1 2-1 1-1 2-1 3-2v2c-3 1-8 6-10 8 0 1-1 4-1 5s-1 2-2 3c0-3 0-6 1-10z" class="b"></path><defs><linearGradient id="AK" x1="617.352" y1="717.774" x2="627.088" y2="728.937" xlink:href="#B"><stop offset="0" stop-color="#737273"></stop><stop offset="1" stop-color="#b6b2b1"></stop></linearGradient></defs><path fill="url(#AK)" d="M616 723c6-3 12-8 18-12 7-5 13-11 20-17 2-2 5-5 6-7l2-2c0 2 0 3 1 4v1c2 2 5 3 7 5l2 2 3 4v2c-1 1-2 1-2 1l-6 3-6 3-9 5c-2 0-4 1-5 2l-3 1c-3 1-6 1-9 3l-4 2v1l-8 5c-1 2-3 2-4 4-2 1-3 2-4 3-2 0-3 2-5 2l-1 2-1 1h0-2c-1 1-1 1-3 1v-1h-2l-17 12-7 6-1-1 5-13 1-1c0 1 1 1 2 1l1-2 8-5 6-3c0-1 6-4 7-5 2-2 7-4 10-7z"></path><path d="M606 730c-1 2-2 3-3 4l-2 2-3 2c1-2 1-2 1-3s6-4 7-5z" class="b"></path><path d="M628 723c-1 1-3 2-5 3v-1c-2 0-3 0-5 1v-1l6-3 4-1v2z" class="N"></path><path d="M599 735c0 1 0 1-1 3l-6 5h-1l2-3v-1-1l6-3z" class="O"></path><path d="M634 718l3-2 1 2c-1 0-2 1-3 2-2 1-5 2-7 3v-2l6-3z" class="R"></path><path d="M593 738v1c-2 2-5 4-6 6s-4 4-5 5c0-1 1-3 2-4h0-2l-1-1 1-1c0 1 1 1 2 1l1-2 8-5z" class="Ae"></path><path d="M576 758l5-13 1 1h2 0c-1 1-2 3-2 4v1c-1 1-1 2-2 3v1c2-1 3-2 4-4v2l-7 6-1-1z" class="Ai"></path><path d="M648 704c2 0 3-1 4-1v-1c1 0 2 0 3-1v-1c2 2 2 0 3 1l-1 1h-1l-7 4c-2 1-4 4-5 5l-7 5-3 2v-2c1-3 5-5 7-7 3-2 5-3 7-5z" class="N"></path><path d="M655 697c1-1 1-2 3-2 0-1 0 0 1-1h1c1 0 2-1 3 0h1v-1l1 1v1 1l2-1-1-1h1l-4-3c-1 0-1 1-1 1 0-1-1-1 0-2h1c2 2 5 3 7 5l2 2 3 4v2c-1 1-2 1-2 1l-6 3c-1-3 1-2 1-5v-1h0v-2c-1-1-1-2-2-2s-2-1-2-2h-1c-1-1-1-1-3-1v1c-2 0-3 1-5 2z" class="AQ"></path><path d="M671 703h-1v-2h-1c0-2 0-3 1-4v-1-1l2 2v2c-1 2-2 2-1 4z" class="o"></path><path d="M672 697l3 4v2c-1 1-2 1-2 1v-1h-2c-1-2 0-2 1-4v-2z" class="AP"></path><path d="M672 699c1 1 1 2 1 3v1h-2c-1-2 0-2 1-4z" class="O"></path><path d="M631 723v1l-8 5c-1 2-3 2-4 4-2 1-3 2-4 3-2 0-3 2-5 2l-1 2-1 1h0-2c-1 1-1 1-3 1v-1h-2c10-7 19-13 30-18z" class="U"></path><path d="M603 741l4-3 1 3h-2c-1 1-1 1-3 1v-1z" class="L"></path><path d="M623 729c-1 2-3 2-4 4-2 1-3 2-4 3-2 0-3 2-5 2l-1 2-1 1h0l-1-3c5-3 10-7 16-9z" class="t"></path><path d="M655 697c2-1 3-2 5-2v-1c2 0 2 0 3 1h1c0 1 1 2 2 2s1 1 2 2v2h0v1c0 3-2 2-1 5l-6 3-9 5c-2 0-4 1-5 2l-3 1c1-3 5-4 7-6l10-5c3-2 5-4 6-7-1-1-1-2-3-2l-6 3c-1-1-1 1-3-1v1c-1 1-2 1-3 1v1c-1 0-2 1-4 1 2-1 3-2 4-4l3-3z" class="j"></path><path d="M655 697c2-1 3-2 5-2v-1c2 0 2 0 3 1-1 0-1 0-2 1s-2 2-4 2c-2 1-3 2-5 2l3-3z" class="L"></path><path d="M658 701l6-3c2 0 2 1 3 2-1 3-3 5-6 7l-10 5c-2 2-6 3-7 6-3 1-6 1-9 3v-1c1-1 2-2 3-2l-1-2 7-5c1-1 3-4 5-5l7-4h1l1-1z" class="B"></path><path d="M647 713c1-1 3-1 4-1-2 2-6 3-7 6-3 1-6 1-9 3v-1c1-1 2-2 3-2l9-5z" class="N"></path><path d="M658 701l6-3c2 0 2 1 3 2-1 3-3 5-6 7l-10 5c-1 0-3 0-4 1l8-5c1 0 2-1 2-1l2-2c3-1 5-3 7-6l-6 3-2 1-1 1c-1 0-2 1-3 1-4 2-7 4-10 6 1-1 3-4 5-5l7-4h1l1-1z" class="l"></path><defs><linearGradient id="AL" x1="767.056" y1="343.136" x2="796.219" y2="358.601" xlink:href="#B"><stop offset="0" stop-color="#8c8684"></stop><stop offset="1" stop-color="#f1e8e2"></stop></linearGradient></defs><path fill="url(#AL)" d="M810 282c1-3 3-6 5-8v1c0 1 1 2 0 3v4l1 1v2c1-3 1-4 2-5l1 2 1 1 1 3h0l-1 1 1 3v7 2 2-1c0 2 0 3-1 5l1 11h-4l-7 1h-1 0l-3 1h-2-2l-1 1c0 1 0 2 1 3l-1 1-1-2c0 6 1 9 3 15l2 4c0 1 1 2 3 3h1l-2 2-5 8-13 22-5 8c0 1-1 3-1 4h-2-1l1 1c-1 1-1 2-1 3h-1c-1 1-2 1-3 2-1 0-2 0-2 1-3 2-4 4-7 5v-3h-1l2-2h0l1-1c0-1 2-3 3-3l1-1c1 0 1-1 2-1h1c2-1 4-1 6-2l-1-1c1-1 1-1 1-2l-2 1-7 3h-1l-3 2h-1c-1 1-1 1-1 2h-1c-1 3-2 5-2 8l-1 2v4h-1c0-1 0-3 1-4v-4-2c-1-1-1-2-1-3-1 2 0 3-1 5-1-2 0-3 0-4l-2-4c-1 2-1 3-1 5v1l1 2-1-1c0 2-1 3 0 5 0 3-1 5 0 7-1 2-1 4-2 6v2l-1 1v-7l1-1c0-1 0 0 1-1l-1-1v-1c-1 2-2 3-3 3h-1l-1 1c1 2 0 3 1 5l-1 1c0 1 0 3-1 4h-1-1c-1-3 0-6-1-9 0-6-1-14 0-19v-1c-1-1-1-2-1-3v-1l2-1c1-1 1-2 2-2v-1-2c2-1 2-1 3-2v-3-1c1-1 1-2 2-3v-1l1-2c1-2 3-4 4-7 0 0 0-1 1-2v2l1-1v-1c1-2 3-3 3-5s1-2 2-3 0-2 1-3l2-2c0-1 0-2 1-3v-3c0-1 0-1-1-2 0-2 2-4 2-6 1-1 1-1 1-2s0-1 2-1v-1l1-1c-1-1 0-2 0-2h1c0-1 1-2 1-3 2-4 3-7 6-9 1-2 2-3 3-5s3-4 5-7c0 0 0-1 1-2l1-1c0-1 1-1 2-2s1-2 2-4c2-1 2-3 4-4l6-6 2-1z"></path><path d="M746 387l2-1-1 5c-1-1-1-2-1-3v-1z" class="b"></path><path d="M749 393c0 1 0 2 1 3v-1c0-2 0 0 1-2 0-1-1-2 0-3 1 1 1 0 0 2v3 4c-1 0-1 0-2 1 1-1 0-2 0-3v-4z" class="AX"></path><path d="M810 282c1-3 3-6 5-8v1c0 1 1 2 0 3s-1 4-3 5c-2 2-4 5-4 8h0c-1 1-2 2-3 2-1 1-2 1-3 1h0l1-1c1 0 2-1 3-2 0-2 0-3 1-4l4-5h-1z" class="o"></path><defs><linearGradient id="AM" x1="745.494" y1="389.574" x2="751.006" y2="418.426" xlink:href="#B"><stop offset="0" stop-color="#9b9590"></stop><stop offset="1" stop-color="#cbc4c0"></stop></linearGradient></defs><path fill="url(#AM)" d="M747 392l1-1v-1l1-1v4 4c0 1 1 2 0 3v4l1 1c0 2 0 4-1 6s0 6 0 9h-1c-1-3 0-6-1-9 0-6-1-14 0-19z"></path><path d="M812 283c2-1 2-4 3-5v4s0 3-1 3c0 5-1 9-3 13h0c-2 1-3 2-5 3v1-2-1c1-2 1-3 2-4 1-2 1-2 2-3v-3l1-1h0l1-1c1-2 0-3 0-4z" class="L"></path><path d="M806 301c2-5 5-8 6-13l1-2h0l1-1c0 5-1 9-3 13h0c-2 1-3 2-5 3z" class="P"></path><path d="M759 389l7-11c2-4 3-9 6-13 0 2-6 15-8 18h0c1-1 1 0 1-1 3-3 4-8 6-11 0-1 0-2 1-2v-2h1 0c-2 5-4 12-8 17l1 2-5 7-2-4z" class="G"></path><path d="M814 285c1 0 1-3 1-3l1 1v2c0 2 1 5 0 7 0 6-4 11-6 16-2 4-4 6-4 10h-2-2l-1 1c0 1 0 2 1 3l-1 1-1-2c0-2 0-3 1-5 0-2-1-2 0-4h1c1-3 4-7 4-10v-1c2-1 3-2 5-3h0c2-4 3-8 3-13z" class="o"></path><path d="M801 316c1-1 1-2 2-3h1l-2 5-1 1c0 1 0 2 1 3l-1 1-1-2c0-2 0-3 1-5z" class="G"></path><path d="M806 301c2-1 3-2 5-3-2 4-4 11-7 15h-1c-1 1-1 2-2 3 0-2-1-2 0-4h1c1-3 4-7 4-10v-1z" class="C"></path><path d="M803 336l2 4c0 1 1 2 3 3h1l-2 2-5 8-13 22-5 8c0 1-1 3-1 4h-2-1l1 1c-1 1-1 2-1 3h-1c-1 1-2 1-3 2-1 0-2 0-2 1-3 2-4 4-7 5v-3h-1l2-2h0l1-1c0-1 2-3 3-3l1-1c1 0 1-1 2-1h1c2-1 4-1 6-2l-1-1c1-1 1-1 1-2l-2 1h-1v-1l1-1h1c2-1 1-1 3-1l2-3c1-3 3-6 5-8v-2c1-1 2-2 2-4-1-5 3-13 5-17 2-3 3-4 5-4l1-1c1-2 0-2-1-4v-2z" class="E"></path><path d="M767 396l2-2c2-4 7-6 11-7l1 1c-1 1-1 2-1 3h-1c-1 1-2 1-3 2-1 0-2 0-2 1-3 2-4 4-7 5v-3z" class="Ae"></path><path d="M816 285c1-3 1-4 2-5l1 2 1 1 1 3h0l-1 1 1 3v7 2 2-1c0 2 0 3-1 5l1 11h-4l-7 1h-1 0l-3 1c0-4 2-6 4-10 2-5 6-10 6-16 1-2 0-5 0-7z" class="P"></path><path d="M810 317c2-2 1-4 2-6h0l1-1v-1c1-1 1-2 2-3 0-1 1-1 1-2v-1c1-2 1-3 2-5 0 4 0 8-2 12 0 0 1 1 0 2v3l1 1-7 1z" class="C"></path><path d="M816 285c1-3 1-4 2-5l1 2c-1 1-1 1-2 3 0 5 1 13-2 17 0 1 0 1-1 2l-3 6c-1 2-2 5-2 7l-3 1c0-4 2-6 4-10 2-5 6-10 6-16 1-2 0-5 0-7z" class="j"></path><path d="M818 298v-11c1 1 1 3 1 4l2-1v7 2 2-1c0 2 0 3-1 5l1 11h-4l-1-1v-3c1-1 0-2 0-2 2-4 2-8 2-12z" class="B"></path><path d="M821 290v7 2 2-1c0 2 0 3-1 5l-1-14 2-1z" class="C"></path><defs><linearGradient id="AN" x1="334.192" y1="498.434" x2="252.039" y2="534.127" xlink:href="#B"><stop offset="0" stop-color="#d3cecb"></stop><stop offset="1" stop-color="#fffffb"></stop></linearGradient></defs><path fill="url(#AN)" d="M206 278c-1-1-2-6-2-7l6 6c0-1-1-2-1-3h0c1 1 2 1 3 2 0 1 1 2 2 3s1 1 3 1l3 3c1 1 1 1 1 2v2h1c2 2 4 4 7 5 1 1 2 3 3 3l1-1c15 19 28 40 39 61 2 4 6 10 7 14 1 1 1 2 2 3 1-2 1-7 1-10v-1h1l-3 20v6-1l1-1c1 0 1-1 1-2l2 2v3c0 2 1 2 2 4v2c0 2 1 3 2 4v3h1v-3h1c1-1 2-1 3-2l11 22c2 4 4 9 5 13h0v10l2-2c1 0 1 1 1 2l3 7h2c0 1 0 1 1 2v1l1 3c1 1 1 3 2 5l2 6 3 7-1 1v9 2l-1 1 2 2v-1l1-1v4h0c1 4 1 7 0 10v1c0 2 0 4 1 6v3 1c0 2 1 4 1 6 0 1 0 1 1 2s1 2 2 4l-2-1v1c1 4 3 9 5 13l-1-1h-3c-1 2 0 3 0 5l2 4c2 2 3 5 6 8h0c-1 1-1 2-1 3 1 2 3 4 4 5v1l3 7v1c1 2 3 4 5 6l3 2c1 1 1 2 1 2v2h-2l1 2 2 3h-3c1 1 3 3 3 4l3 5c1 2 2 3 2 4l6 9 3 6c2 2 6 6 6 10 5 8 14 15 21 22 2 3 5 7 5 11l5 12 18 42 8 18-1 2c2 2 2 5 3 7v1 3h0c-1-1-2-1-4-2-1-1-2-1-3-1l-9-6c-1 0-2-1-2-2-1-1-2-2-3-2-2-1-3-2-5-3v1 1 2h0v1h-1 0c2 3 6 4 8 6v2l-18-11c-2-2-4-2-5-3l-1 2-3-3c-2-2-6-3-8-4-3-2-6-5-9-6-2 0-5-2-6-3 1 2 3 3 4 4h0c-1 0-4-2-4-2-4-3-7-4-11-5-2-1-4-1-6-1l-1 1c-1 0-2 0-3 1h-1c-2 1-3 2-5 3 2-5 8-9 11-14 2-3 6-7 9-8 2 0 2 1 3 0v-1c-1-1-3-2-4-3s-3-1-4-1l-1 1v-1c-1 1-1 0-1 1-3 4-8 8-11 12h-1c-1 1-3 3-3 4l-3 3v-1-1-1l1-1v-2-1l-1-1v-3l-1-1c0-1-1-2-2-3h-1l1 2-1 1c-1-1-2-1-3-1h-1v-4h0c-1-1-1-1-1-2 0-3-1-5-3-7l-4-8-3-3c-1-2-3-3-5-5-1-2-2-4-3-5l1-3c-1-8-3-18-8-24-2-2-4-2-6-4v-1c-1 0-1-1-2-1h0l-1-2-1-1v1c-3-3-1-4-1-7-1-2-1-3-2-5l-4-3c-1-2-2-2-3-3h-1v-2l1-2c2 0 2 0 3-1l-1-2c-1-5-4-10-5-15v-1c0-2-2-4-3-6-1-6-5-10-8-15-2-5-3-9-4-14-2-3-2-6-4-8l-1 1h-2c0-1 0-1 1-2v-4c0-3 0-7-1-10 0-3 0-6-1-10-1-7-4-14-7-21 0-2-1-3-1-5 1 0 2 1 3 0-2-8-1-16-1-24v-21c0-5 1-11 0-16-1-4-5-6-6-10 2 1 3 2 4 4 0-5-1-7-3-10h-1l1-1c2 0 5 0 8 1h0l1-1v-7c0-3-1-5-1-7-1-3-2-5-2-7l-3-5c0-1-1-2-1-3s-1-1-2-2l1-1-4-5-2-2-3 1-2-1h-1c-1-2-1-4-1-5l-9-12c0-1-1-2-1-2-1-2-1-3 0-5-1-1-1-3-1-5 1-4 2-8 2-12 0-3-1-6-1-9v-1l-1-4c-1-2-1-4-2-6 0-1-1-2-2-3 0-2 0-3-1-5l1-1-1-2c-2-3-3-5-4-8s-2-7-4-10v-2z"></path><path d="M381 666c1 0 1 0 2 1v1c-1 0-1 0-2-1v-1zm-16-6h1l1 1-1 3h-1v-4z" class="B"></path><path d="M342 666c1 2 3 3 3 5l-2-1-1-4zm24-14l1-1v1c0 2 1 3 1 4l-1 1-1-5z" class="F"></path><path d="M230 365c3 2 5 4 7 5l-3 1-2-1h-1c-1-2-1-4-1-5z" class="L"></path><path d="M334 607l2 2v-1c1 1 2 2 3 4h-1v-1l-1-1c0 2 0 2 1 3h0c0 1 1 2 1 3 1 3 3 5 4 8 0 1 1 2 1 4-1-1-9-18-10-21z" class="H"></path><path d="M339 612h1v1c1 1 2 1 2 2 0 2 1 2 2 3v-1l1 1h-1l1 1h0c0 1 1 1 1 2l1 1v3h-1c-1-1-2-4-3-6s-2-3-2-4c-1-1-2-2-2-3z" class="D"></path><path d="M270 475c-1-7-1-14-1-21h0v4c1-1 0-5 1-5l1 22h-1z" class="G"></path><path d="M270 475h1l3 8c3 4 6 7 8 11l-2 1c-2-5-6-9-8-14v-1l-1-1-1-4z" class="P"></path><path d="M309 478h1l1 3 1 1c1 1 1 2 1 3-1 2-1 5-2 7l-2-14z" class="AR"></path><path d="M206 278c1 1 3 2 4 4 0 0 0 1 1 2l-1 2h-1l1 1v2c1 1 1 1 1 2h0l1 1v1c1 2 2 3 2 5-2-3-3-5-4-8s-2-7-4-10v-2z" class="P"></path><path d="M269 483c1 0 1 0 1 1l1 1h0v1 2l4 14h-1v-1c-1-1 0 0-1-2h0v-2c-1 1 0 1 0 2v3l-4-19z" class="F"></path><path d="M365 660v-1c-1-2 0-4 0-6v-1c-2-3-3-5-4-7h-1l1-2c1 0 1 1 2 2v1l1 2h0l2 2v1 1l1 5v4h0l-1-1h-1z" class="E"></path><path d="M278 571c2 4 6 10 7 14v1c-2-1-4-2-5-3 0-2-1-3-1-4-1-1-1-1-1-2v-2c-1-1-1-2 0-4z" class="AK"></path><path d="M279 518h0c4 7 4 17 7 25l-1 1c0-1 0-2-1-3 0 2 1 4 1 6 0-1-1-3-1-4l-3-11c-1-2-2-4-1-5v1l1 1v2 1c1 0 0 0 1 1v1-2l-1-5-1-4c0-1 0-1-1-2v-3z" class="E"></path><path d="M283 600c-1-2-1-5-1-7v-1c-1-3-2-5-3-7l1-1c2 2 5 3 6 7 0 3-1 6-3 9z" class="O"></path><path d="M293 622c2 0 4 0 6 2 5 5 10 16 11 22-2-1-3-7-4-10-3-4-6-8-10-10l-3-3v-1z" class="b"></path><path d="M311 492c1-2 1-5 2-7l1 3h0v2l1-1v1l1 1-1 2h0c0 2 0 2 1 3l-2 3v2 3h-1l-1-6-1-6z" class="Z"></path><path d="M314 488h0v2l1-1v1l1 1-1 2c-1-1-1-2-2-3l1-2z" class="u"></path><path d="M312 498c1-2 0-5 1-7v7l1 1v2 3h-1l-1-6z" class="c"></path><path d="M311 492c1-2 1-5 2-7l1 3-1 2v1c-1 2 0 5-1 7l-1-6z" class="AS"></path><path d="M366 687c1 1 2 2 2 4l4 4 1 1h0c2 1 8 7 9 9l1 1c2 1 3 3 4 5l-3-2c-1-1-2-2-3-2l-11-10c-1-2-2-3-3-5 0-2-1-3-1-5z" class="E"></path><path d="M399 698c3 1 6 3 8 6l3 3 2 3v5c1 1 1 1 1 2l2 2c0 1 1 1 1 2 1 4 3 7 2 11-1 0-2-1-2-2h1c0-1-1-3-1-4v-1s-1-1-1-2v-1h0l-1-1h1l-1-2c-2-1-2-2-3-3-2-1-3-2-4-3 2 0 2 0 3 1h0l1-1-1-1h1v-2c-1 0-1-1-1-2-1 0-1 0-1-1l-1-1-1 3-1-1c0-3-2-4-4-6-1-1-2-2-2-3l-1-1z" class="K"></path><defs><linearGradient id="AO" x1="289.397" y1="608.903" x2="282.688" y2="609.095" xlink:href="#B"><stop offset="0" stop-color="#7d7673"></stop><stop offset="1" stop-color="#978a85"></stop></linearGradient></defs><path fill="url(#AO)" d="M283 600c4 4 8 9 8 14 0 3-1 5-2 7v1c-3-3-1-4-1-7-1-2-1-3-2-5 0-1-1-2-1-3l-3-3c0-2 0-2 1-4z"></path><path d="M365 664h1c5 1 9 3 12 7v1 1c2 4 8 9 8 14-2-1-4-4-5-6-2-3-6-9-8-11l-8-5v-1z" class="R"></path><path d="M214 301l1-1c3 6 6 13 7 20 1 10 0 17-2 26-1-1-1-3-1-5 1-4 2-8 2-12 0-3-1-6-1-9v-1l-1-4c-1-2-1-4-2-6 0-1-1-2-2-3 0-2 0-3-1-5z" class="o"></path><path d="M287 538c1-5-2-10 0-15 2 16 7 31 13 46h-1s0-1-1-2c-4-10-9-18-11-29z" class="G"></path><path d="M300 569l5 8c0 1 2 2 2 3 0 2 0 4 1 6l14 32h-1l-1-3-1-1c0-1-1-3-2-4v-1 1h-1l-12-27c0-2-2-4-2-6-1-2-1-3-2-5 0-1 0-2-1-3h1z" class="B"></path><path d="M300 599c1 1 3 8 4 10l4 7c7 14 14 29 25 40-1 1-1 1-2 1l-3-3-3-5c-1-1-1-2-2-3l-4-5c-8-13-15-27-21-41h1 0l1-1z" class="F"></path><path d="M251 486c1-6 0-12 1-18 0 21 4 42 8 63l5 19v1c-1-1-2-3-3-4l-3-16-1-3v-3c-1-5-3-10-4-15 0-5-1-10-2-15-1-3-2-4-1-7v-2z" class="AP"></path><path d="M313 589c3 2 4 5 6 8 11 14 22 30 24 47v7 2h-1c1-3-1-5-1-8v-1l-3-6-3-13c-1-2-1-3-2-5v-1h0l-3-4c-1-3-2-5-4-7l-11-16-2-3z" class="T"></path><path d="M347 586h1c1 1 3 3 5 4v-1h2l3 5c1 2 2 3 2 4l6 9 3 6c2 2 6 6 6 10-4-3-6-7-9-11-1-1-6-7-7-8l-12-18z" class="AN"></path><path d="M245 375c-1-2-2-3-3-5h1c1 1 2 2 3 4 0 2 1 3 2 5 0 1 1 1 1 2l1 1 1 1c0 2 1 2 1 4h-1c0 1 1 1 1 2s1 2 1 4l1 1c0 1 0 3 1 4 0 1 0 2 1 3 0 2-1 3 0 5 0 2 1 10 0 12 0 1 0 3-1 5v5 2h-1v-3l1-9-1-1c-1-2 0-6 0-8l-3-14c0-1-1-2-1-4 0-1-1-2-2-3l-3-5c0-1-1-2-1-3s-1-1-2-2l1-1-4-5c2 1 3 1 4 2 0 0 1 1 2 1z" class="F"></path><path d="M252 391c1 3 2 6 2 9-1-1-2-3-3-5 0-1-1-2-1-4h2z" class="O"></path><path d="M239 372c2 1 3 1 4 2 0 0 1 1 2 1 2 3 4 6 4 10l-6-8-4-5z" class="E"></path><path d="M251 395c1 2 2 4 3 5 2 6 1 12 1 18l-1-1c-1-2 0-6 0-8l-3-14z" class="AK"></path><path d="M243 377l6 8 3 6h-2c0-1-1-2-2-3l-3-5c0-1-1-2-1-3s-1-1-2-2l1-1z" class="b"></path><path d="M271 479l1 1v1c2 5 6 9 8 14l2-1 3 16 1 9c0 1 1 2 1 4-2 5 1 10 0 15-5-11-3-23-6-35-1-4-3-7-5-11-1-2-3-4-4-7h0v-1l-1-4v-1z" class="B"></path><path d="M284 510h1l1 9h0c-2-3-2-6-2-9z" class="G"></path><path d="M280 495l2-1 3 16h-1c0-6-2-10-4-15z" class="D"></path><defs><linearGradient id="AP" x1="392.499" y1="674.234" x2="380.2" y2="682.693" xlink:href="#B"><stop offset="0" stop-color="#cfcaca"></stop><stop offset="1" stop-color="#fefbf7"></stop></linearGradient></defs><path fill="url(#AP)" d="M368 656c1 3 2 4 3 6 4 2 7 4 10 8 1 2 3 4 5 6a53.56 53.56 0 0 0 15 15l9 6c-2 1-2 1-4 1-4-3-9-6-13-10-6-4-10-11-15-16v-1c-3-4-7-6-12-7l1-3h0v-4l1-1z"></path><path d="M369 697h1l11 10c1 0 2 1 3 2l3 2c2 1 5 3 7 4 1 1 2 2 3 2l2 2s0 1 1 1c3 1 6 3 8 5v1 1 2h0v1h-1 0l-2-1-21-15h1l-3-3c-1-1-2-2-3-4l-7-6c-1-1-3-2-3-4z" class="R"></path><path d="M385 714c1 1 2 1 3 2s1 0 2 1 1 1 3 2l1 1 3 2 2 1c2 1 4 2 5 4v1h0c1 0 1 0 1 1h0l-21-15h1z" class="P"></path><path d="M381 707c1 0 2 1 3 2l3 2c2 1 5 3 7 4 1 1 2 2 3 2l2 2h-1l-1 1c-6-4-12-7-16-13z" class="B"></path><path d="M248 388c1 1 2 2 2 3 0 2 1 3 1 4l3 14c0 2-1 6 0 8l1 1-1 9c-1 8-2 16-2 24v12 5c-1 6 0 12-1 18-1-4-1-7-1-11-1-14-1-27 1-41 0-3 2-7 1-10 1-2 0-8 0-10-1-4 0-8-1-12 0-3-1-5-1-7-1-3-2-5-2-7z" class="L"></path><defs><linearGradient id="AQ" x1="414.819" y1="697.618" x2="401.795" y2="706.75" xlink:href="#B"><stop offset="0" stop-color="#797779"></stop><stop offset="1" stop-color="#beb9b6"></stop></linearGradient></defs><path fill="url(#AQ)" d="M378 672c5 5 9 12 15 16 4 4 9 7 13 10l9 5c2 2 4 3 5 4s2 2 4 2v1l8 18-1 2c-1 0-3-8-4-10s-2-3-4-5c-1-1-3-3-6-4-2-1-4-3-7-4l-3-3c-2-3-5-5-8-6-2-2-4-3-6-5-3-2-5-4-7-6 0-5-6-10-8-14v-1z"></path><path d="M344 628v1c1 0 1 0 1 1h1c0 2 0 3 1 4v1c1 0 1 1 1 1l3 9-1 1v2 2c-1 1-1 1-3 1v-6-1-2l-1-3v-1c0-1-1-2-1-2l-3-10c-1-3-3-7-5-10-1-2-3-5-4-7l-8-10c-12-19-21-39-26-60-2-5-3-11-4-17l1-1v1c1 6 3 12 5 17 5 16 10 32 18 46 3 4 5 8 8 11v1c2 3 4 8 7 10h0c1 3 9 20 10 21z" class="E"></path><path d="M261 546c0-5-3-8-4-13-2-11-3-21-5-31-1-4-5-10-4-14h3c-1 3 0 4 1 7 1 5 2 10 2 15 1 5 3 10 4 15v3l1 3 3 16c1 1 2 3 3 4v-1c2 4 3 7 5 10 1 3 5 6 6 9l2 2c-1 2-1 3 0 4v2c0 1 0 1 1 2 0 1 1 2 1 4h0-1l1 1-1 1c1 2 2 4 3 7v1c0 2 0 5 1 7h0c-1 2-1 2-1 4l3 3c0 1 1 2 1 3l-4-3c-1-2-2-2-3-3h-1v-2l1-2c2 0 2 0 3-1l-1-2c-1-5-4-10-5-15v-1c0-2-2-4-3-6-1-6-5-10-8-15-2-5-3-9-4-14z" class="t"></path><path d="M262 547c1 1 2 3 3 4v-1c2 4 3 7 5 10 1 3 5 6 6 9l2 2c-1 2-1 3 0 4v2c0 1 0 1 1 2 0 1 1 2 1 4h0c-3-2-3-6-5-8l-5-10-3-6c-2-4-4-7-5-12z" class="AX"></path><path d="M278 577c-1-1-1-2-1-3-1-1-2-3-2-4l1-1 2 2c-1 2-1 3 0 4v2z" class="o"></path><path d="M290 622h3v1l3 3c4 2 7 6 10 10 1 3 2 9 4 10v7c0 1 1 2 1 3 7 7 13 13 16 22l1 1c0 2 0 4-1 6-1 1-1 1-1 2l3 2 1 2-1 1c-1-1-2-1-3-1h-1v-4h0c-1-1-1-1-1-2 0-3-1-5-3-7l-4-8-3-3c-1-2-3-3-5-5-1-2-2-4-3-5l1-3c-1-8-3-18-8-24-2-2-4-2-6-4v-1c-1 0-1-1-2-1h0l-1-2z" class="AX"></path><path d="M290 622h3v1l3 3c4 2 7 6 10 10 1 3 2 9 4 10v7h0c-1-1-1-3-2-4-1-4-1-8-3-12s-5-7-8-10h0c-2-1-2-2-4-2-1 0-1-1-2-1h0l-1-2z" class="o"></path><defs><linearGradient id="AR" x1="323.022" y1="666.229" x2="319.908" y2="673.945" xlink:href="#B"><stop offset="0" stop-color="#7e7473"></stop><stop offset="1" stop-color="#9a9086"></stop></linearGradient></defs><path fill="url(#AR)" d="M327 678v1c-1 0-1-1-2-2-3-2-5-5-6-7l-8-13v-1c7 7 13 13 16 22z"></path><defs><linearGradient id="AS" x1="428.413" y1="723.474" x2="415.655" y2="727.66" xlink:href="#B"><stop offset="0" stop-color="#918f8f"></stop><stop offset="1" stop-color="#c4bdbb"></stop></linearGradient></defs><path fill="url(#AS)" d="M410 707c3 1 5 3 7 4 3 1 5 3 6 4 2 2 3 3 4 5s3 10 4 10c2 2 2 5 3 7v1 3h0c-1-1-2-1-4-2-1-1-2-1-3-1l-9-6c1-4-1-7-2-11 0-1-1-1-1-2l-2-2c0-1 0-1-1-2v-5l-2-3z"></path><path d="M322 618l-14-32c-1-2-1-4-1-6 0 1 1 2 2 3v1l1 2c1 0 1 1 2 2l1 1h0l2 3 11 16c0 2 2 4 3 6 0 1 1 2 2 3v1c0 1 0 2 1 3 1 2 2 5 2 7s0 3 1 4v3l3 7c0 2 0 3 1 4v1c0-1-1-1-1-1-1-2-2-3-3-5-1-1-1-2-2-3l-6-9c-2-4-3-8-5-11z" class="H"></path><path d="M315 592l11 16c0 2 2 4 3 6 0 1 1 2 2 3v1c0 1 0 2 1 3 1 2 2 5 2 7s0 3 1 4v3c-1-3-3-7-3-10 0 0-1-1-1-2v-1c-3-9-10-17-15-25v-1c-1-1-1-2-1-4z" class="F"></path><path d="M251 402c1 4 0 8 1 12 0 2 1 8 0 10 1 3-1 7-1 10-2 14-2 27-1 41 0 4 0 7 1 11v2h-3c-1 4 3 10 4 14 2 10 3 20 5 31 1 5 4 8 4 13-2-3-2-6-4-8l-1 1h-2c0-1 0-1 1-2v-4c0-3 0-7-1-10 0-3 0-6-1-10-1-7-4-14-7-21 0-2-1-3-1-5 1 0 2 1 3 0-2-8-1-16-1-24v-21c0-5 1-11 0-16-1-4-5-6-6-10 2 1 3 2 4 4 0-5-1-7-3-10h-1l1-1c2 0 5 0 8 1h0l1-1v-7z" class="K"></path><path d="M246 410c1 1 2 1 4 2h1v3 14c-1-1-3-3-3-5l1-1c0-1-1-1 0-2v-1c0-5-1-6-3-10z" class="C"></path><path d="M242 410c1 0 2 1 4 0 2 4 3 5 3 10v1c-1 1 0 1 0 2l-1 1-3-4c0-5-1-7-3-10z" class="S"></path><path d="M316 496l1 1v1h2v1c1 0 1 0 2 1l1 1c1 1 3 2 3 4-1 1-1 1-2 1v1l1 1h0 2v2c1 0 1 0 2-1v1c0 2 1 4 1 6 0 1 0 1 1 2s1 2 2 4l-2-1v1c1 4 3 9 5 13l-1-1h-3c-1 2 0 3 0 5h-1c-1-3-3-4-5-6l-1-1c-1-1-2-3-3-5h-2l-6-22v-1h1v-3-2l2-3z" class="AR"></path><path d="M326 519h2c0 1 0 0 1 1 0 1 0 1 1 2 1 4 3 9 5 13l-1-1h-3c-1-1-2-1-3-2v-1h3c1-1-1-2-1-3v-2l-2-3h-1c-2 0-3-1-5 0h0l-2-1 1-2c1 1 1 1 1 2h1l1-1-1-1v-1h3z" class="W"></path><path d="M326 519h2c0 1 0 0 1 1h-1-1l-1 1h-2l-1-1v-1h3z" class="Y"></path><path d="M324 508h2v2c1 0 1 0 2-1v1c0 2 1 4 1 6 0 1 0 1 1 2s1 2 2 4l-2-1v1c-1-1-1-1-1-2-1-1-1 0-1-1h-2-3v1l1 1-1 1h-1c0-1 0-1-1-2 0-1-1-2-1-3l1-2c-1-1-1-1-1-2l-1-1c1-1 2-1 3-2 1 0 1-1 2-2h0z" class="i"></path><path d="M324 508h0c1 2 1 4 2 6 1 1 1 2 1 3-1-1-1 0-3 0 0-1 0-1-1-1h-1c0-2 1-2 2-3v-1l-2-2c1 0 1-1 2-2z" class="Y"></path><path d="M319 512c1-1 2-1 3-2l2 2v1c-1 1-2 1-2 3h1c1 0 1 0 1 1l1 1 1 1h-3v1l1 1-1 1h-1c0-1 0-1-1-2 0-1-1-2-1-3l1-2c-1-1-1-1-1-2l-1-1z" class="AB"></path><path d="M316 496l1 1v1h2v1c1 0 1 0 2 1l1 1c1 1 3 2 3 4-1 1-1 1-2 1v1l1 1c-1 1-1 2-2 2-1 1-2 1-3 2l1 1c0 1 0 1 1 2l-1 2c0 1 1 2 1 3l-1 2 2 1h0c2-1 3 0 5 0v1c-2 1-3 1-5 0v2c0 1 1 1 2 2l-1 1c0 1 1 1 1 2v1c-1-1-2-3-3-5h-2l-6-22v-1h1v-3-2l2-3z" class="AS"></path><path d="M317 506c2 0 4 1 6 1l1 1c-1 1-1 2-2 2-1 1-2 1-3 2-1 0-1 0-3-1l-1-5h2z" class="AN"></path><path d="M316 511c2 1 2 1 3 1l1 1c0 1 0 1 1 2l-1 2c0 1 1 2 1 3l-1 2 2 1h0c2-1 3 0 5 0v1c-2 1-3 1-5 0v2c0 1 1 1 2 2l-1 1c0 1 1 1 1 2v1c-1-1-2-3-3-5-1-1-1-3-2-5v-1c-1-3-2-6-3-10z" class="Al"></path><path d="M316 496l1 1v1h2v1c1 0 1 0 2 1l1 1c1 1 3 2 3 4-1 1-1 1-2 1v1c-2 0-4-1-6-1l-2-1h-2v-1h1v-3-2l2-3z" class="AA"></path><path d="M314 501l2-1 1 1-3 3v-3z" class="AN"></path><path d="M315 505c1-1 2-1 4-1 0 0 1 0 1 1l3 1v1c-2 0-4-1-6-1l-2-1z" class="i"></path><defs><linearGradient id="AT" x1="302.873" y1="416.728" x2="291.836" y2="422.66" xlink:href="#B"><stop offset="0" stop-color="#a9a6a5"></stop><stop offset="1" stop-color="#c9c3c0"></stop></linearGradient></defs><path fill="url(#AT)" d="M290 398c1-1 2-1 3-2l11 22c2 4 4 9 5 13h0v10l-1 19c-1-2-1-6-1-8v-5c0-2 0-5-1-7v-1c1 1 1 3 1 5v-5l1-1c0-1 0-1-1-2-1 0-1-1-2-1v3 1l-1 11c-1 3 0 6-1 8 0-2-1-5 0-7v-1-3c0-2 0-2 1-3l-2-1v-1-3c-1-1-1-2-1-3h0v-3c-1-1-1-1-1-2v-2h-1v-1 4 1 2c2 2 0 8 1 11v2c0 1 0 1-1 2v1h-1v-21h0l-1-1v-2-1c-1-1-1-2-2-4h0v-1-1l1 1v1s1 1 1 2c1 1 1 1 1 2v-2l-1-1v-2c-1-1 0-1-1-2h0l-1-2c0-1 0 0-1-1s-1-2-2-3l-1-1v-1l-2-3v1c-1 1 0 1-1 2v2 2l-2 2c1-7 3-13 4-19z"></path><defs><linearGradient id="AU" x1="289.769" y1="400.117" x2="276.28" y2="405.364" xlink:href="#B"><stop offset="0" stop-color="#948f8f"></stop><stop offset="1" stop-color="#c8c2bd"></stop></linearGradient></defs><path fill="url(#AU)" d="M282 361h1l-3 20v6-1l1-1c1 0 1-1 1-2l2 2v3c0 2 1 2 2 4v2c0 2 1 3 2 4v3h1v-3h1c-1 6-3 12-4 19-2 8-2 16-3 24-1-2-1-4 0-5v-1-3-2c0-1 0-1 1-2v-2-2l1-1-2-1v-1c-1 0-1-2-2-3v3c0 1 0 3-1 4v1l1 1v7h-1c0-2 1-4 0-5 0 1-1 2 0 4h0-1 0c-1-2-1-4-1-6 2-1 0-8 1-10h1v-3c1-1 0-3 0-4 1-1 1-1 1-2h0-1-1l1-1c-1-1-1-2-1-3 0 3 0 5-1 7h0v-3-1h0v-1h0-1v-1l-1-1 1-1c-1-1-1-1-1-2v-4-2c-1-1-2-2-2-3s1-3 0-3c0-1 0-1-1-2 1-1 1-1 1-2-1-1-1-2-1-2l-2-4h0 0c1 1 2 1 2 3 1 1 1 2 3 3 0 0 0 1 1 2v-5-1-2c1-1 1-3 1-4 1-1 0-2 0-3h1v-3c1 1 1 2 2 3 1-2 1-7 1-10v-1z"></path><defs><linearGradient id="AV" x1="331.018" y1="563.513" x2="341.188" y2="555.712" xlink:href="#B"><stop offset="0" stop-color="#200705"></stop><stop offset="1" stop-color="#590f0e"></stop></linearGradient></defs><path fill="url(#AV)" d="M319 527h2c1 2 2 4 3 5l1 1c2 2 4 3 5 6h1l2 4c2 2 3 5 6 8h0c-1 1-1 2-1 3 1 2 3 4 4 5v1l3 7v1c1 2 3 4 5 6l3 2c1 1 1 2 1 2v2h-2l1 2 2 3h-3c1 1 3 3 3 4h-2v1c-2-1-4-3-5-4h-1 0c-12-19-21-38-28-59z"></path><path d="M329 542l-4-8v-1c2 2 4 3 5 6-1 1-1 1-1 3z" class="v"></path><path d="M330 539h1l2 4-1 1 1 4c-2-2-3-4-4-6 0-2 0-2 1-3z" class="u"></path><path d="M333 548l-1-4 1-1c2 2 3 5 6 8h0c-1 1-1 2-1 3l-2 1-3-7z" class="AJ"></path><path d="M345 567v1 1c1 4 3 7 5 11 1 1 2 2 3 2l2 3h-3c-4-3-6-8-8-13-1-1-1-1-1-2v-2l-1-1h3z" class="Q"></path><path d="M336 555l2-1c1 2 3 4 4 5v1l3 7h-3c-3-4-4-7-6-12z" class="e"></path><path d="M345 568c1 2 3 4 5 6l3 2c1 1 1 2 1 2v2h-2l1 2c-1 0-2-1-3-2-2-4-4-7-5-11v-1z" class="AJ"></path><path d="M350 574l3 2c1 1 1 2 1 2v2h-2 0c-1-2-2-4-2-6zm-41-133l2-2c1 0 1 1 1 2l3 7h2c0 1 0 1 1 2v1l1 3c1 1 1 3 2 5l2 6 3 7-1 1v9 2l-1 1 2 2v-1l1-1v4h0c1 4 1 7 0 10v1c0 2 0 4 1 6v3c-1 1-1 1-2 1v-2h-2 0l-1-1v-1c1 0 1 0 2-1 0-2-2-3-3-4l-1-1c-1-1-1-1-2-1v-1h-2v-1l-1-1c-1-1-1-1-1-3h0l1-2-1-1v-1l-1 1v-2h0l-1-3c0-1 0-2-1-3l-1-1-1-3h-1l-1-18 1-19z" class="Z"></path><path d="M315 476c0 1 0 0 1 1l2-2-2 5h-1c-1-2-1-2 0-4z" class="W"></path><path d="M318 481l4-11v5c-1 2-2 3-3 5v2l-1-1z" class="AA"></path><path d="M315 466l2-2 2 1c0 3 0 7-1 9 0-1 0-3-1-4l-2-4h0z" class="c"></path><path d="M315 466l2 4c1 1 1 3 1 4v1l-2 2c-1-1-1 0-1-1v-1-1c-1-3-1-5 0-8z" class="AS"></path><path d="M323 465l3 7-1 1-3 2v-5c0-2 0-4 1-5z" class="AN"></path><path d="M313 473l2 2v1c-1 2-1 2 0 4h1v2l1 2-2 6v-1l-1 1v-2h0l-1-3c0-1 0-2-1-3l1-9z" class="k"></path><path d="M316 482l1 2-2 6v-1l-1 1v-2l2-6z" class="J"></path><path d="M325 473v9l-3-1c-1 1-1 1-1 2 0 0-1 0-2-1h0v-2c1-2 2-3 3-5l3-2z" class="Al"></path><path d="M317 484l1-3 1 1h0c1 1 2 1 2 1 0-1 0-1 1-2l3 1v2l-1 1c0 3 0 5-1 8l-1-2h1v-2l1-1h-1l-1 1h-1l-1-1h-2c-1 2-2 4-3 5h0l1-2-1-1 2-6z" class="Y"></path><path d="M316 491c1-2 1-2 1-4l2-2c1 1 1 2 1 3h-2c-1 2-2 4-3 5h0l1-2z" class="W"></path><path d="M320 488l1 1h1l1-1h1l-1 1v2h-1l1 2c1 1 1 0 1 1s0 2 1 3c-2 1-3 2-4 3-1-1-1-1-2-1v-1h-2v-1l-1-1c-1-1-1-1-1-3 1-1 2-3 3-5h2z" class="c"></path><path d="M318 490c1 1 1 2 2 2h1l-1-1v-1l1 1v2l1 1v2l-1-1c-1-2-3-3-3-5z" class="W"></path><path d="M323 493c1-3 1-5 1-8l2 2v-1l1-1v4h0c1 4 1 7 0 10v1c0 2 0 4 1 6v3c-1 1-1 1-2 1v-2h-2 0l-1-1v-1c1 0 1 0 2-1 0-2-2-3-3-4l-1-1c1-1 2-2 4-3-1-1-1-2-1-3s0 0-1-1z" class="q"></path><path d="M326 487v-1l1-1v4h0v5 1h-1v-1-7z" class="Z"></path><path d="M322 501v-1h2c1-1 2-1 2-2l-1-1 1-1h1v3 1c0 2 0 4 1 6v3c-1 1-1 1-2 1v-2h-2 0l-1-1v-1c1 0 1 0 2-1 0-2-2-3-3-4z" class="v"></path><path d="M309 441l2-2c1 0 1 1 1 2l3 7h2c0 1 0 1 1 2v1l1 3c1 1 1 3 2 5-1 2-2 4-2 6h0l-2-1-2 2h0c-1 3-1 5 0 8v1l-2-2-1 9-1-1-1-3h-1l-1-18 1-19z" class="AA"></path><path d="M313 464l2 2h0c-1 3-1 5 0 8v1l-2-2c-1-3 0-6 0-9z" class="W"></path><path d="M314 459c-1-2-2-5-1-8l4-1h1v1l1 3c1 1 1 3 2 5-1 2-2 4-2 6h0l-2-1-2 2-2-2 1-4v-1z" class="AB"></path><path d="M314 459l1-3 1 1 2 1-2 2h-2v-1z" class="n"></path><path d="M319 454c1 1 1 3 2 5-1 2-2 4-2 6 0-2-1-4-1-5h0l1-3-1-1c0-1 0-1 1-2z" class="AG"></path><path d="M314 459c-1-2-2-5-1-8l4-1h1v1l1 3c-1 1-1 1-1 2l1 1-1 1-2-1-1-1-1 3z" class="Al"></path><path d="M315 456l3-5 1 3c-1 1-1 1-1 2l1 1-1 1-2-1-1-1z" class="i"></path><defs><linearGradient id="AW" x1="409.702" y1="667.665" x2="390.89" y2="673.307" xlink:href="#B"><stop offset="0" stop-color="#888c92"></stop><stop offset="1" stop-color="#cac3bf"></stop></linearGradient></defs><path fill="url(#AW)" d="M366 612c3 4 5 8 9 11 5 8 14 15 21 22 2 3 5 7 5 11l5 12 18 42v-1c-2 0-3-1-4-2s-3-2-5-4l-9-5c2 0 2 0 4-1l-9-6a53.56 53.56 0 0 1-15-15s2 1 2 2l5 5c0 2 2 3 4 4h0l2 2h0v-3c1-1 1-1 1-2l-6-5v-1l-3-3c-1-1-2-2-2-3-1-1-1-2-2-3s-2-2-2-4h1v1c1 2 0 1 1 2l3 3c0 1 0 0 1 1 0 2 1 3 3 4v-1h0v-2c-1-1 1-3 1-5v-1c-1-1-1-3-2-4l-3-7c-1-1-4-7-6-7l-1-1c0-1 0-3-1-4 0-1 0-1-1-2l1-1c1-1 1-3 3-4h0c-1-4-7-9-10-11h0-1c-1-2-4-5-5-7-2-2-3-4-3-7z"></path><path d="M415 701c2 1 4 2 6 5v1h-1c-1-1-3-2-5-4h0c1 0 1 0 2 1h1 0l-3-3h0z" class="N"></path><path d="M401 656l5 12c-3-2-3-5-6-8h0 1l1 1h0v-1c-1-2-1-3-1-4z" class="L"></path><path d="M410 697c1 1 4 3 5 4h0l3 3h0-1c-1-1-1-1-2-1h0l-9-5c2 0 2 0 4-1z" class="P"></path><path d="M326 608c2 2 3 4 4 7l3 4h0v1c1 2 1 3 2 5l3 13 3 6v1c0 3 2 5 1 8h1c-1 4-2 8-1 12v1h0l1 4 2 1c2 2 4 4 6 5l1 1c2 1 3 1 6 1 1 1 2 1 3 2 2 1 4 5 5 7h0c0 2 1 3 1 5 1 2 2 3 3 5h-1c0 2 2 3 3 4l7 6c1 2 2 3 3 4l3 3h-1l21 15 2 1c2 3 6 4 8 6v2l-18-11c-2-2-4-2-5-3l-1 2-3-3c-2-2-6-3-8-4-3-2-6-5-9-6-2 0-5-2-6-3 1 2 3 3 4 4h0c-1 0-4-2-4-2-4-3-7-4-11-5-2-1-4-1-6-1l-1 1c-1 0-2 0-3 1h-1c-2 1-3 2-5 3 2-5 8-9 11-14 2-3 6-7 9-8 2 0 2 1 3 0v-1c-1-1-3-2-4-3s-3-1-4-1l-1 1v-1c-1 1-1 0-1 1-3 4-8 8-11 12h-1l1-1c1-2 2-3 4-4v-1h-1l1-2c0-1 0-1 1-2l1 1c-1 0-1 1-1 2 1-1 2-4 3-6 1 0 1 0 1-1 0-3-7-9-8-12v-1-1l-1-1v-2l-1-1v-2c1-2 1-3 1-5l-1-1v-3l-1-1c1-4-2-6-3-9h0 0v-1-1-1c1 2 2 3 3 5 0 0 1 0 1 1v-1c-1-1-1-2-1-4l-3-7v-3c-1-1-1-2-1-4s-1-5-2-7c-1-1-1-2-1-3v-1c-1-1-2-2-2-3-1-2-3-4-3-6z" class="B"></path><path d="M338 642c1 2 3 4 2 6v1l-1-1v-1-1c-1-1-1-2-1-4z" class="D"></path><path d="M343 708c0-2 1-3 3-4h0c0 1-1 2-1 3 0-1 1-1 2-1h0 1l-1 1c-1 0-2 0-3 1h-1z" class="F"></path><path d="M343 670l2 1c2 2 4 4 6 5l1 1c2 1 3 1 6 1l-2 1h-1c-2 1-4 0-5-1-3-2-6-5-7-8z" class="E"></path><path d="M361 690c1 1 2 3 1 5h0l-2-1c-2 1-3 2-4 4-1 1-1 3-1 4v2c-2 0-3-1-4-2-1-2-1-3 0-4 1-4 7-7 10-8z" class="o"></path><path d="M361 690l1-2c3 2 5 7 7 9 0 2 2 3 3 4l7 6c1 2 2 3 3 4l3 3h-1l-16-13c-2-2-4-4-6-5v1l1 2h-2-2l-1 1h1c0 1 0 1 1 2h-1c-1 0-1 1-1 2l1 1v1l-4-2v-2c0-1 0-3 1-4 1-2 2-3 4-4l2 1h0c1-2 0-4-1-5z" class="l"></path><path d="M363 699l-1-2v-1c2 1 4 3 6 5l16 13 21 15 2 1c2 3 6 4 8 6v2l-18-11c-2-2-4-2-5-3l-1 2-3-3c-2-2-6-3-8-4-3-2-6-5-9-6h0c-4-3-9-4-12-7v-1l-1-1c0-1 0-2 1-2h1c-1-1-1-1-1-2h-1l1-1h2 2z" class="B"></path><path d="M363 699c2 1 4 4 5 5 2 1 3 3 4 5v1l20 14-1 2-3-3c-2-2-6-3-8-4-3-2-6-5-9-6h0c-4-3-9-4-12-7v-1l-1-1c0-1 0-2 1-2h1c-1-1-1-1-1-2h-1l1-1h2 2z" class="C"></path><path d="M372 710l-3-2c-2-2-6-4-7-7 2-1 4 3 6 3 2 1 3 3 4 5v1z" class="G"></path><defs><linearGradient id="AX" x1="254.711" y1="326.821" x2="237.2" y2="338.089" xlink:href="#B"><stop offset="0" stop-color="#878585"></stop><stop offset="1" stop-color="#d1cac6"></stop></linearGradient></defs><path fill="url(#AX)" d="M206 278c-1-1-2-6-2-7l6 6c0-1-1-2-1-3h0c1 1 2 1 3 2 0 1 1 2 2 3s1 1 3 1l3 3c1 1 1 1 1 2v2h1c2 2 4 4 7 5 1 1 2 3 3 3l1-1c15 19 28 40 39 61 2 4 6 10 7 14v3h-1c0 1 1 2 0 3 0 1 0 3-1 4v2 1 5c-1-1-1-2-1-2-2-1-2-2-3-3 0-2-1-2-2-3h0 0l2 4s0 1 1 2c0 1 0 1-1 2 1 1 1 1 1 2 1 0 0 2 0 3s1 2 2 3v2l-2-1h0 0l-1-1h0v-1l1-1-1-3c-1-2 0-2-1-2 0-1-3-2-3-4-1-1-1 0-2-1h0c0-1 0-1-1-2v1l-1-2h0v-3c-1-1-1-1-1-2s-1-1-1-2l-4-12-1-1-1-1v-1l-1-1v-1h0c0-1-3-7-3-8v-1l-1-1-3-6c-1-1-1-2-2-2-1-2-1-3-2-4l-1-3c-1 0-1-1-2-2l-2-3c-2-3-4-5-5-8l-1-1c-2-1-2-2-3-4s-4-3-5-5l-1-1c-1-1-2-2-2-4l-1-1c-1-1-1-1-1 0l2 4c0 3 3 8 2 11-1-5-4-15-7-18-1-1-1 0-1-1l-1-1c0-1-1-1-1-2h0c-1-1-1-2-2-3 0-2 0-2-1-4h0l-2-2 1-2c-1-1-1-2-1-2-1-2-3-3-4-4z"></path><path d="M273 382v-3-1-1-1c1 1 1 2 2 3v2c1 1 1 2 1 4-2-1-2-2-3-3z" class="j"></path><path d="M235 318v-1c0-2-1-4-3-5v-1h0c3 3 5 6 7 9 0 1 0 1 1 2l1 1 1 2h-1l-1 1c-2-3-4-5-5-8z" class="K"></path><path d="M206 278c-1-1-2-6-2-7l6 6c2 2-1 3 3 5l3 4c1 2 2 3 3 5h0-1c-2-3-5-6-7-9v2c-1-1-1-2-1-2-1-2-3-3-4-4z" class="L"></path><path d="M567 202l2-1c3 2 5 5 9 6v2h-2v1c6 1 12 1 18 1h22c2 0 7 0 8 1 5 0 11-1 16 0h-4c2 1 4 1 5 2 3 1 7 0 9 1v1l2 1h7 1c1 1 3 1 4 1h3 0c1 0 2 1 2 1h-2-3 0-3c-2-1-3 0-4 0 2 1 6 2 8 2l7 2 3 1h0l3 1 1 1c4 1 7 1 11 2 1 0 2 1 3 1v1s1 1 1 0l3 1v1c5 2 10 2 16 2-3 0-7 0-9 1h0-3v1 1c-4-2-8-3-12-4-2 0-6 0-7 1 0 1 1 2 2 4 1 3 6 6 9 8 4 2 12 7 14 12h0c1 2 3 3 4 5 3 3 5 6 6 9-1 3-4 4-5 6-2 1-3 2-5 3h0c2 1 2 0 4 1 3 1 9 5 11 8 0 1 0 1 1 2 0 1 0 1 1 2 2 3 5 9 6 13h0l2 6c0 2 0 5 1 6h1l3 30c0 4 0 10 1 14v-9h2v2l-1 19v25h-1l-1 1c0 3 2 8 1 12 1 2 2 6 2 8l1 20c0 3 0 7-1 10 0 3-1 7-1 10s2 7 3 10l1 2 4 7c1 3 4 4 5 6h-1v1h1c-1 1-1 4-2 5-4 3-7 8-9 13l-3 4h0c-1 2-1 4-2 5v3c-1 1-1 1-1 2v1c0 1 0 2-1 3h0c-1 2-1 4-2 6l-4 13c-8 25-18 50-35 70l-10 12c-2 3-7 7-9 10l-1 2c-1 0-1 0-2-1l-1 1c-4 5-8 11-12 16-6 7-13 12-18 20-2 5-6 7-10 10-3 2-6 5-9 7l-13 9h1c-1 1-1 2-3 2v1l-1 1c-2 1-3 2-4 4h-2v-2l-2 1-1 3v1c1 0 2 0 3-1l1 1c0 2-1 2-2 3-5 4-5 9-8 14l-2 1c-1 3-3 6-3 10v2c0 2-1 3 0 4l-1 2c-1 0-2 0-2-1l-1 1-5 13c0 2-2 4-2 5 0 2 0 4-1 6v1 1h0c-1 0-1 1-1 1-1 1-1 2-1 2h2 0c-1 2-3 4-5 5l-1 4-4 9-2 7-46 120c-1-2-1-4-1-6v-15l-1 4-1-53c0-3 0-5 1-7 1-1 1-4 1-5 0-8 1-16 3-23l5-8h-1s-1 1-2 1v1h-3c5-4 8-9 11-15l1-4 8-21c1-1 2-3 2-5l20-54c0-1 0-2 1-3h0v-1-1l1-1v-1c0-1 0-1 1-2v-1l1-1v-1l-2 2h0c0-1 0-2 1-3 1-2 2-5 2-7v-4h-1l-1 5h0c-1 3-2 5-4 7 0-1 1-3 2-4h-2c-1-1-1-2-1-4l-1 1-1 1h0l-1-1 7-17c2-5 5-9 7-14-1 1-2 1-3 2-1 2-2 3-2 5-1 1 0 1-1 3h0c-2 1-2 2-2 4-1 0 0 0-1-1h-1v1 1 1h-2v1c0 1 0 1-1 2l-1-1h-2v-1h-1c-2-1-2-1-2-2l-1-1h1 1l-1-1 1-1-1-1c1-2 1-1 2-2l-1-1c-1 1-1 2-1 2l-1 2s-1 1-1 2h0v3h-1l-1-1 1-1v-3c1-1 2-3 2-5 0 0 0-1 1-1l1-1c0-1 1-1 1-2v-2-1-1c1-1 1-1 1-2h1v-1c0-1 0-1 1-2v-2l-1-1v-1c-1-1-1-1-1-2v-1l20-49h-1l7-18 4-8 20-50c1-1 1-2 1-2 0-1-1-1-1-2 0-3-1-5-3-7l-1-2c0-2 0-2 1-3v-1l1-2c1-1 3-2 4-3 0 0 1 0 2-1 1 0 3-1 5-1v-1c1-1 1-3 1-4 1-1 2-2 2-3l3-7-1-2h3v-3c2-2 3-6 3-8l4-7 1-6v-1l1-2c1-1 1-3 1-5 1-2 2-4 3-5v-1c-1-1-1-1-2-3 1-2 0-4 0-6v-1c2-1 3-1 5-1l1-1h0c1-1 2-5 3-7l7-26h1l2-8c1-1 1-2 1-3l4-20 1-2c-1 0-1-1-2-2l1-5v-4c1-6 1-12 1-18v-2-2-10c-1-2-2-3-3-4l-3-13h-1v1 1-1l-2-2h-2l-2-2-1 1-1-1v-1c0-1 0-2-1-2h-1l-1-1c1-1 1-1 1-2v-1h-1-1-1-1v-1c-1-1-2-1-2-2h-3l-1-2h-1-1c-2 1-2 1-4 1 0-1 0-2-1-3l2-2v-1c-1 0-1-1-2-2 1-1 1-2 1-3l2-2v-1c-1 0-1 0-2-1 0-1-1-2-2-2l-2-2h0-1-1-2l-1-3h-1v1c-2 0-2-1-3-1l-2 4-1-4s1-1 1-2-1-1-1-2v-1l1-2h1l-1-1 1-1v-2c1 1 1 1 2 1 0-1 1-1 2-2l2-2c1-1 2-1 3-2 0-1 0-1-1-2 1-1 1-1 1-2v-1l2-1h0c-3-2-5-5-8-7-2-2-5-6-8-5h-2s0-1 1-2h0c-1-2-3-3-5-4-5-2-10-5-15-6l-9-3c-7-2-14-2-22-2l6-5c4-3 8-6 11-10z" class="s"></path><path d="M711 373h2l1 1-3 3s0-1-1-2l1-2z" class="AF"></path><path d="M734 382c1 1 0 2 0 4l-1 1-1-2 2-3z" class="Ae"></path><path d="M646 585l2-5 1 1v2c-2 1-2 2-3 2z" class="n"></path><path d="M698 403h1l-3 8v-3l2-5z" class="r"></path><path d="M699 341c-1-3 0-6 1-10v4-1c0-1 1-1 1-2l-2 8v1z" class="z"></path><path d="M679 273h2c1 1 0 2 0 3s0 1 1 1h0c-1 1-1 2-2 2l-1-2v-3-1zm39 60c2-3 4-6 7-8-2 3-3 6-5 8h0l-1-1-1 1z" class="d"></path><path d="M610 674c0 1-1 2-1 4v1c0 1-1 2-1 3s0 1-1 1l-2 2 5-11z" class="AY"></path><path d="M693 336c0-3-1-8 0-11 0 1 1 3 1 4 1 2 0 4 0 6l-1 1zm25-3l1-1 1 1-6 9c1-4 2-6 4-9z" class="M"></path><path d="M656 224l6 1-3 2h1-1l-6-1 3-2z" class="AY"></path><path d="M652 223l4 1-3 2c-2 0-4-1-6-1 1-1 3-1 5-2z" class="AZ"></path><path d="M645 222l7 1c-2 1-4 1-5 2-2 0-4-1-6-1 1-1 3-1 4-2z" class="p"></path><path d="M585 737v2c0 2-1 3 0 4l-1 2c-1 0-2 0-2-1l3-7z" class="b"></path><path d="M692 297v-1h1l1 1c0 2 0 3-1 4 0 2 0 2-1 3-1 0-1 0-2-1 0 0-1-1-1-2l2 1h0c1-1 1-3 1-5z" class="AD"></path><path d="M658 243h2c2 1 10 4 11 6h-1c-1-1-1-1-2-1 0-1-2-2-3-2h-1-1c-2 0-3-2-5-3z" class="M"></path><path d="M678 509l3 4-3 3-1-1h-1l2-6z" class="Q"></path><path d="M680 264h1c1 0 3-1 4-2l4-2h0c-2 2-5 3-7 6h0c-1 1-3 1-4 1v-1c1 0 1-1 2-2z" class="z"></path><path d="M678 266h0-2l-2 1c-1 1-1 0-2 1h0c-1 1-2 1-2 3-1 0-1 1-1 1h-1v-2c2-3 8-5 12-6-1 1-1 2-2 2z" class="d"></path><path d="M657 249l-1-2v-1l6 3 2 2-4-1-1 1-2-2z" class="h"></path><path d="M657 249c1 0 2 0 3 1l-1 1-2-2z" class="g"></path><path d="M706 362h1v1l-1 2-1 2v1h1v-1-1l2-2v1l-1 1-2 5c0 1 0 1-1 2l-1-2c0-3 2-7 3-9z" class="Aa"></path><path d="M681 504c0-3 1-5 2-8l1 3c1 1 1 2 1 3-1 0-1 0-2 2v1l-2-1h0z" class="c"></path><path d="M684 499c1 1 1 2 1 3-1 0-1 0-2 2v1l-2-1 2-2c0-1 1-2 1-3z" class="AB"></path><path d="M574 763c0 2 0 4-1 6v1 1h0c-1 0-1 1-1 1-1 1-1 2-1 2h2 0c-1 2-3 4-5 5l6-16z" class="Ae"></path><path d="M702 331c1 2 1 4 1 7h0c-2 1-1 1-2 2h-2l2-8 1-1z" class="AF"></path><path d="M713 316l2-3c0 1 1 1 1 1 1 2 0 4 0 6 1 1 0 1 1 2l1-2 1 1c-1 1-1 1-1 2h-2l-1-3c0-1 0-1-1-2 0-1-1-1-1-1v-1z" class="d"></path><path d="M703 371l1 2v5l-6 11-1-1c3-6 4-11 6-17z" class="f"></path><path d="M695 286h1v-1l1-2h-1v-2l1 1v3 1c0 2 1 1 2 3 0 1-2 10-3 11-1 2-1 4-2 5 0 1 0 2-1 3 0-2 0-1 1-2v-3c0-2 1-3 2-4 0-1 0-3 1-5 1-1 1-3 0-4s0-1-1-1h0v1h-1v-4z" class="M"></path><path d="M706 362l7-13v-1c1-3 5-7 7-9 1 1 0 2 1 3v1c0 1 1-1-1 0 0 1 0 1-1 1h0 0c0-1 1-2 0-3h0c-1 1-1 2-2 3l-1 1v1l1-1c0 2-1 2-1 3v1c-2 1-3 2-4 4l-2 4c-1 1-2 3-3 5h-1z" class="h"></path><path d="M696 411c-1 5-5 9-7 13v1-1l-1-1c1-1 1-1 1-2 1-4 4-9 7-13v3z" class="z"></path><path d="M678 267c1 0 3 0 4-1-2 3-2 4-3 7v1-2l-2-1c-2 2-1 7-2 10l-1-4v1l-1 3h0v-2c1-4 2-9 5-12z" class="I"></path><path d="M725 352l6-8v1c0 1 1 3 0 4 0 2-3 6-5 8l-3 3 3-7-1-1z" class="z"></path><path d="M657 252c-1 1-1 1-2 1-3-2-5-5-8-7l-6-6c2 1 4 2 5 3 2 1 3 3 5 3 0-1-1-2-1-3 1 1 3 2 3 4v1l4 4z" class="M"></path><path d="M601 220l-8-2c-1 0-2-1-3-1h1 5l37 6 6 1h-3c-1-1-2 0-3 0-1-1-2-1-3-1h-3c-1-1-1-1-2-1h-3c-2-1-3-1-4-1h-3c-1-1-3-1-4-1h-3c-2-1-4-1-5-1-1-1-3 0-5 0h0 1l2 1h0 0z" class="V"></path><path d="M662 249c3 0 5 2 8 4 0 0 1 1 1 2l1 1h1 0c-2 1-6 3-8 2l-3-2 4-1 2 1-1-1c-1-1-2-2-3-4l-2-2z" class="z"></path><path d="M664 251c1 0 1 0 2 1 2 1 3 2 4 2l-2 2h0l-1-1c-1-1-2-2-3-4z" class="V"></path><path d="M660 264h1c7 0 14-3 20-4h0-1c-4 2-10 4-13 8v1l-1 1-1-2 1-1v-1c-1 0-1 0-2 1v1l-1-1c0-2 1-1 1-3h-3-1z" class="j"></path><path d="M647 516c1-1 2-3 2-5h1c0 3-1 8 1 10s5 2 6 3h0c-4-1-6-1-9 0 0-1 1-3 0-4-1 0-2 0-3 1v-1c0-1 1-3 2-4z" class="U"></path><path d="M732 355h0c-1 4-4 8-6 11l-11 16v-1c2-6 7-12 11-17 2-3 3-6 6-9z" class="z"></path><path d="M596 708v1c1 0 2 0 3-1l1 1c0 2-1 2-2 3-5 4-5 9-8 14l-2 1 8-19z" class="b"></path><path d="M725 377v1c-2 4-5 7-7 10-3 4-5 9-8 13-1 3-3 6-4 9v-1-1c0-2 1-4 2-6l7-12c2-3 4-5 6-7l4-6z" class="d"></path><path d="M627 632l2 2c0 1 0 1-1 2l2 1c-2 1-3 3-3 5l-6 5c1-5 4-10 6-15z" class="X"></path><path d="M704 451h0v6c1-1 1-1 1-2l1 2h2c-2 1-3 3-3 5l-3 2c-1-1-2-1-2-2h0-2-1l2-4h2 2c1-3 1-5 1-7z" class="AO"></path><path d="M704 417l1 1v1c-1 0-1 1-1 2l1-1v-1h1c-1 2-3 4-3 6-1 3-4 5-5 8 0 1-1 2-1 3l-1 2h0-1c-1 0-1-1-1-1v-2c1-1 2-3 3-4 0-1 1-2 1-2 1-4 3-5 5-8l1-4z" class="M"></path><path d="M709 436l1-1v2 2c0 5-4 11-5 16 0 1 0 1-1 2v-6h0c-1-3 4-12 5-15z" class="n"></path><path d="M667 275c2 1 3 2 4 4h2v2h0v1c0 1-1 1-1 1l3 6 1 1v1c0 1 1 1 1 2l1 1 1 2v1c1 0 1 1 2 2v1l2 2 3 6h0 0l-12-20c-3-4-6-8-7-13z" class="h"></path><path d="M671 279h2v2 1l-1-1-1-2z" class="z"></path><path d="M650 243l-2-2h0c3 1 6 3 8 5v1l1 2 2 2 1-1 4 1c1 2 2 3 3 4l1 1-2-1-4 1c-2-1-3-2-5-4l-4-4v-1c0-2-2-3-3-4z" class="V"></path><path d="M660 250l4 1c1 2 2 3 3 4l1 1-2-1c-3-1-5-2-7-4l1-1z" class="I"></path><path d="M675 281c1-3 0-8 2-10l2 1v2 3 2 3l-1 1v4c1 1 1 2 1 3-1 0-1 0-2-1 0-1-1-3-1-4-1-1-1-1-1-2h0l-1-2h1z" class="Aa"></path><path d="M678 287h-1v-2l-1-1 1-1h1v4z" class="f"></path><path d="M698 403c3-5 6-11 7-16l-2 3h0c-1 3-3 10-5 11 1-6 4-12 7-18v1l2-3h0l1-2c0-1 0-1 1-2h0c1-1 0-1 1-2 1 1 1 2 1 2-4 9-7 18-12 26h-1z" class="g"></path><path d="M725 352l1 1-3 7c-1 3-3 4-5 7-1 2-3 4-4 7l-1-1h-2c-1-1 0 0 0-2l1 1c1-2 3-4 4-6 3-5 6-10 9-14z" class="I"></path><path d="M700 407h1c-4 9-9 16-12 25-4 8-5 17-9 25v-1l-1 1v-1l3-5c-1-1-1-2 0-3 3-6 4-13 7-19 2-6 7-12 9-18v-1c1-1 1-2 2-3z" class="d"></path><path d="M670 231c2-1 4 0 6 0s4 2 6 3c0 1 1 2 2 4 1 3 6 6 9 8 4 2 12 7 14 12-7-6-13-10-20-15l-7-7c-3-2-7-4-10-5z" class="U"></path><path d="M627 632l9-22v1c0 1-1 2-1 3l1 3c0 2-1 4 0 6l-1 1-5 10 1 1-1 2-2-1c1-1 1-1 1-2l-2-2z" class="m"></path><path d="M647 506c1-4 4-10 6-13v2l1 1-4 15h-1c0 2-1 4-2 5s-2 3-2 4l-2 1-1-1c3-4 4-8 6-12 0-1 0-1-1-2z" class="AZ"></path><path d="M653 495l1 1-4 15h-1c0 2-1 4-2 5l6-21z" class="l"></path><path d="M676 434h1c0-2 1-3 2-4 0 1 0 2 1 2-1 3-2 5-3 7l-4 11c-2 5-3 10-6 15 0 1-1 2-2 2l3-9 4-13c1-4 2-8 4-11z" class="g"></path><path d="M708 457c1-2 2-4 4-5l1 1-3 4-1 4h1l-2 2h0c-1 0-2 0-3 1-2 3-3 4-6 5h-2c-1 1-2 2-4 2l1-3 3-5v-1h1 2 0c0 1 1 1 2 2l3-2c0-2 1-4 3-5z" class="J"></path><path d="M697 463c1 1 2 2 2 3-1 1-3 2-5 2l3-5z" class="Q"></path><path d="M697 462h1 2 0c0 1 1 1 2 2l-3 2c0-1-1-2-2-3v-1z" class="q"></path><path d="M697 469c5-4 9-7 13-12l-1 4h1l-2 2h0c-1 0-2 0-3 1-2 3-3 4-6 5h-2z" class="c"></path><path d="M704 378c0-1 0-1 1-2l1-2 4-8c0-1 1-2 1-2l1-2c0-1 1-2 1-3 2-2 2-3 4-5-1 3-2 5-4 7-1 2-2 3-3 5-3 5-5 11-7 16-2 4-4 7-6 11l-4 12-5 12c-1 1-2 2-2 4l-1 3v1c0 1 0 1-1 2v-1c-1-1 0-2-1-3l6-18h0v2c0 1 0 2-1 3s0-1 0 1l-1 2h0v1c-1 1-1 2-1 3 0 0 0 1-1 2h0c1 0 1 0 1-1s0-1 1-2l1-2c1-4 3-7 4-11l1-1c0-1 0-2 1-2v-2h1v-1-1-1c1-1 1-1 1-2l1-1v-1c-1-1-1-1-1-2l1-1 1 1 6-11z" class="d"></path><path d="M538 763c1 2 0 3 0 5v2l-8 22c-2 5-5 10-8 14h-1s-1 1-2 1v1h-3c5-4 8-9 11-15l1-4 8-21c1-1 2-3 2-5z" class="U"></path><path d="M514 837v48c0 4-1 10 0 13l-1 4-1-53c0-3 0-5 1-7 1-1 1-4 1-5z" class="AL"></path><path d="M607 589l3-5 1 1-19 46-1-2 11-28h0v-1-1h0c1-1 1-1 1-2 0-2 1-4 2-6 1-1 1 0 1-1l1-1h0z" class="l"></path><path d="M603 597c0-2 1-4 2-6 1-1 1 0 1-1l1-1h0l-5 12h0v-1-1h0c1-1 1-1 1-2z" class="p"></path><path d="M717 354l2-2 5-8v1l-14 25c-4 7-7 14-10 22-3 7-6 13-9 20-1 2-3 6-5 9 0-2 1-3 2-4l5-12 4-12c2-4 4-7 6-11 2-5 4-11 7-16 1-2 2-3 3-5 2-2 3-4 4-7z" class="AL"></path><path d="M685 502l2 2 3 1v1l2 1v3c-1 1-1 2-2 3h0l-2 2-4-1h-1l-2-1-3-4c0-1 1-2 2-3l1-2h0l2 1v-1c1-2 1-2 2-2z" class="Z"></path><path d="M681 504h0l2 1h0v4l-1 1h0c-2-1-2-2-2-4l1-2z" class="J"></path><path d="M685 502l2 2 3 1v1s-1 1-1 2c-1 1 0 2-3 3h0c0-1 0-2 1-3l-2-2h0l-2-1h0v-1c1-2 1-2 2-2z" class="c"></path><path d="M685 502l2 2c-1 0-1 0-2 1v1h0l-2-1h0v-1c1-2 1-2 2-2z" class="Y"></path><path d="M678 509c0-1 1-2 2-3 0 2 0 3 2 4h0c2 1 2 1 4 1h0c3-1 2-2 3-3 0-1 1-2 1-2l2 1v3c-1 1-1 2-2 3h0l-2 2-4-1h-1l-2-1-3-4z" class="AN"></path><path d="M645 521c1-1 2-1 3-1 1 1 0 3 0 4-14 11-22 26-29 41l-8 20-1-1c6-16 14-32 23-47l4-5c-1-1-1-2-1-4 1-1 1-2 2-3 2-1 4-2 5-2h1l1-2z" class="B"></path><path d="M638 525c2-1 4-2 5-2h1c-1 4-5 7-7 9-1-1-1-2-1-4 1-1 1-2 2-3z" class="AF"></path><path d="M697 469h2c3-1 4-2 6-5 1-1 2-1 3-1-1 2-1 4-1 6v6l-1 1h-1c-1 1-3 2-4 2l-1-1-1 1c-1 0-1 0-2 1-3 0-4 1-7 1h0l3-9c2 0 3-1 4-2z" class="Al"></path><path d="M694 472c1 0 2 0 3 1l-1 1h0c-1 1-2 1-3 0l1-2z" class="AN"></path><path d="M707 469v6l-1 1c0-2-1-4-1-5l2-2z" class="AR"></path><path d="M713 428c0 2 0 4 1 6 0 5 1 10 1 15v11c-2 0-2 1-4 2h0l-1-1h-1l1-4 3-4-1-1c-2 1-3 3-4 5h-2l-1-2c1-5 5-11 5-16v-2-2l-1 1v-1c0-2 2-6 4-7z" class="AN"></path><path d="M733 319h1l3 30c0 4 0 10 1 14v-9h2v2l-1 19v25h-1v-5l-1-3-1 2-1-3v-1h-1l-1 1v-4l1-1c0-2 1-3 0-4l1-2h0v-1c1-3 2-4 2-7l-2-33c-1-7-1-14-2-20z" class="AU"></path><path d="M737 372v6c2 4 1 12 1 17l-1-3-1 2-1-3v-1h-1l-1 1v-4l1-1c0-2 1-3 0-4l1-2h0v-1c1-3 2-4 2-7z" class="Ab"></path><path d="M735 391h1c0-1 0-1 1-2v3l-1 2-1-3z" class="Ai"></path><path d="M733 387l1-1 1 4h-1l-1 1v-4z" class="O"></path><path d="M737 372v6c0 2 0 3-1 5v3l-1-1v-5h0v-1c1-3 2-4 2-7z" class="f"></path><defs><linearGradient id="AY" x1="705.754" y1="314.998" x2="705.746" y2="334.503" xlink:href="#B"><stop offset="0" stop-color="#090c0d"></stop><stop offset="1" stop-color="#1d272d"></stop></linearGradient></defs><path fill="url(#AY)" d="M707 320c0-2 2-4 3-5v-1h1v1l2 1v1s1 0 1 1c1 1 1 1 1 2l-1 2s-1 2-1 3l-1 1-1 3-3 6c-1 0-2 2-3 3 0 2-1 4-2 5h-1c0-2 0-3 1-5h0c0-3 0-5-1-7l-1 1c0 1-1 1-1 2v1-4l3-8c1 0 2-1 3-1 0-1 0-1 1-2z"></path><path d="M707 320h1c0 1 0 2-1 3v-3z" class="d"></path><path d="M703 330l3-5v4c-1 0-1 0 0 1-1 1-1 1-1 2 0-1 0-2-1-3l-1 1z" class="h"></path><path d="M710 325c1 1 1 1 2 1l-1 3c-1 0-2-1-2-1 0-1 0-2 1-3z" class="f"></path><path d="M713 317s1 0 1 1c1 1 1 1 1 2l-1 2s-1 2-1 3l-1 1c-1 0-1 0-2-1h0v-1-3c0-1 2-3 3-4z" class="z"></path><path d="M714 318c1 1 1 1 1 2l-1 2-1-1c1-2 1-2 1-3z" class="h"></path><path d="M713 321l1 1s-1 2-1 3l-1 1c-1 0-1 0-2-1h0v-1l1-1c1-1 2-1 2-2z" class="AF"></path><path d="M710 325h3l-1 1c-1 0-1 0-2-1h0z" class="g"></path><path d="M709 328s1 1 2 1l-3 6c-1 0-2 2-3 3 0 2-1 4-2 5h-1c0-2 0-3 1-5h0c0-3 0-5-1-7l1-1 1-1c1 1 1 2 1 3 0-1 0-1 1-2l1 1 2-3z" class="r"></path><path d="M709 328s1 1 2 1l-3 6c-1 0-2 2-3 3 1-2 1-4 2-7l2-3z" class="I"></path><path d="M681 513l2 1h1l1 3-1 1v3h-1l1 1v1l-1 1h1v2l-2 4c0 3-1 6-1 9l-1 3h-3l-1-1c1 0 1-1 1-2h0v-1c-1-2-4-2-6-3l-3-2c1 0 2-1 3-1v-2-1l3-10s1-3 2-4h1l1 1 3-3z" class="W"></path><path d="M674 519h1c1 1 1 3 1 4h-1c0 1 0 1-1 2h1v3 1h-2c1 1 1 1 1 2v1l-3-3 3-10z" class="X"></path><path d="M681 513l2 1h1l1 3-1 1v3h-1l1 1v1l-1 1-1 1-1-1-1 1c-2 0-2 0-3-1-1-3 0-6 0-9l1 1 3-3z" class="Al"></path><path d="M684 522l-2 1v-1c-1-1-1-2-1-4 1 1 2 2 2 3l1 1z" class="AN"></path><path d="M681 513l2 1h1l1 3-1 1c-2 0-3-1-5-1l-1-1 3-3z" class="W"></path><path d="M681 513l2 1v2h-1l-3 1-1-1 3-3z" class="q"></path><path d="M678 528v-1c1-1 2-1 3-1 0 0 1 0 1 1v3c0 3-1 6-1 9l-1 3h-3l-1-1c1 0 1-1 1-2h0v-1c-1-2-4-2-6-3l-3-2c1 0 2-1 3-1v-2-1l3 3 1 1 2 1c3-2 0-4 1-6z" class="AN"></path><path d="M678 528v-1c1-1 2-1 3-1 0 0 1 0 1 1v3c0 3-1 6-1 9l-1 3h-3l-1-1c1 0 1-1 1-2h0v-1h2c1-1 0-3 1-5 1-1 1-2 1-3v-1-2c-1 0-2 0-3 1z" class="AR"></path><path d="M677 539c2 1 2 0 4 0l-1 3h-3l-1-1c1 0 1-1 1-2z" class="c"></path><path d="M649 253c4 3 7 9 11 11h1 3c0 2-1 1-1 3l1 1v-1c1-1 1-1 2-1v1l-1 1 1 2 1-1h1l-1 2v4c1 5 4 9 7 13l12 20h0v1c2 3 4 6 6 8 2 0 3-2 3-3 3-4 4-9 5-13 0-1 0 0 1-1-1 5-3 12-5 16-1 3-3 5-4 9 4-8 10-14 17-19l-4 4c-4 5-9 9-12 15-1 3 0 8 0 11l1 5-1 7v-1c-1-8-1-18-4-26-2-7-5-13-9-18l-13-24c-3-4-3-9-6-12-2 0-4 1-6 2 1-1 2-2 2-4-1-4-5-9-8-12z" class="l"></path><path d="M608 225c1-1 2-1 3-1l6 3 8 5 6 4 1 1h1l12 12c1 1 2 3 4 4 3 3 7 8 8 12 0 2-1 3-2 4h-1c0 1 0 2-1 2l-2-3c0-1 0-2-1-2l-5-8-1-1-1-2-1-1-2-2-1-1c0-1-1-2-2-3-2-4-7-8-11-11h0l-1-1c-2-1-3-2-5-3l-1-1-4-3c-1 0-1-1-2-1-1-1-1-1-2-1v-1c-1 0-2 0-3-1z" class="V"></path><path d="M668 533l3 2c2 1 5 1 6 3v1h0c0 1 0 2-1 2l1 1h3c-2 4-3 8-6 11l-4 2h0l-1 1-1-1c-2 1-3 2-5 3h0c-2 1-4 0-6-1l1-2 1-2 2-3c0-1 1-2 1-3l3-8c1-3 1-5 3-6z" class="Y"></path><path d="M670 546c2 0 2 0 3 1-1 1-2 2-4 2 1-1 1-2 1-3z" class="AJ"></path><path d="M661 550c0-1 1-2 1-3 1 0 3 1 4 2 0 1 1 1 3 2-1 1-1 2-2 2l-2 1c-1-1-2-1-3-2l-1-2z" class="AA"></path><path d="M662 552c0-1 1-1 1-2h1c1 1 3 1 4 1h1c-1 1-1 2-2 2l-2 1c-1-1-2-1-3-2z" class="AR"></path><path d="M661 550l1 2c1 1 2 1 3 2l3 1c-2 1-3 2-5 3h0c-2 1-4 0-6-1l1-2 1-2 2-3z" class="m"></path><path d="M659 553c2 0 2 1 4 1-1 2-1 2-2 3v-1c-1-1-2-1-3-1l1-2z" class="w"></path><path d="M668 533l3 2c2 1 5 1 6 3v1h0c0 1 0 2-1 2 0 1 0 3-1 4 0 1-1 2-2 2-1-1-1-1-3-1 0 1 0 2-1 3h-1-2c-1-1-3-2-4-2l3-8c1-3 1-5 3-6z" class="AB"></path><path d="M668 544c2-1 3-1 5 0l2 1c0 1-1 2-2 2-1-1-1-1-3-1-1-1-2-1-3-1l1-1z" class="i"></path><path d="M665 539l1 1-1 1 1 1 1 2h1l-1 1c1 0 2 0 3 1 0 1 0 2-1 3h-1-2c-1-1-3-2-4-2l3-8z" class="n"></path><path d="M667 545c1 0 2 0 3 1 0 1 0 2-1 3h-1c-1-1-2-2-2-3l1-1z" class="AM"></path><path d="M668 533l3 2c2 1 5 1 6 3v1c-2-1-3-1-4-1l-1 1 1 1-1 1c-2 0-4 0-6-1l-1-1c1-3 1-5 3-6z" class="W"></path><path d="M685 275l1-1 2 2v2h2c0 1 1 1 1 3v1 2 1 1h0l-1 1h0l1 1c0 1 1 2 1 2s0-2 1-2c0-1 1-1 2-2v4h1v6l-1 2h-1v-1l-1-1h-1v1c0 2 0 4-1 5h0l-2-1c1-1 1-1 0-2v3 1 1c-1 1-1 1-2 1l-3-3-2-5-2-2v-1h1c-1-2-1-3-2-4 0-1 0-2-1-3v-4l1-1v-3-2l1 2c1 0 1-1 2-2h0c1 0 2-1 3-2z" class="r"></path><path d="M679 282l1 1v1l-2-1 1-1z" class="I"></path><path d="M685 275v2l-2 2-1-2h0c1 0 2-1 3-2z" class="M"></path><path d="M691 288c0 1 1 2 1 2v5h-1v-3-4z" class="z"></path><path d="M679 277l1 2c1 0 1-1 2-2l1 2-3 3c-1-1-1-2-1-3v-2z" class="V"></path><path d="M691 302v-2c0-1-1-1-1-2l-1-1 1-1 2 1c0 2 0 4-1 5h0z" class="AF"></path><path d="M682 292l-1-3c1 0 1-1 2-1v-2-1c0-1 0-1 1-1v-1l2 1c-1 2-1 4 0 6l-1 1v2 2s0 1 1 1h-2c0-2 0-2-2-4z" class="Aa"></path><path d="M682 292c2 2 2 2 2 4h2v1l2-1c1 1 0 2 0 4h0l-2 2h-2l-2-5-2-2v-1h1 1v-2z" class="g"></path><path d="M692 510l1-2 1 1h0c-1 1-1 2-1 4 0 1 0 3 1 4v2c1 2 1 3 1 5l1 2c1 2 1 3 2 4l-3 1c1 1 1 2 2 4-1 1-9 7-10 7l-2 6c-1 0-1-1-1-2v-1l-4 2c-1 2-3 4-4 6h-2c3-3 4-7 6-11l1-3c0-3 1-6 1-9l2-4v-2h-1l1-1v-1l-1-1h1v-3l1-1-1-3 4 1 2-2h0c1-1 1-2 2-3z" class="Z"></path><path d="M688 515l2-2c-1 2-2 4-4 6l-2 5h-1l1-1v-1l-1-1h1v-3l1-1-1-3 4 1z" class="c"></path><path d="M688 515l2-2c-1 2-2 4-4 6v-3l-1-1h3z" class="W"></path><path d="M687 525c1-1 2-2 2-3h0-1v-1c0-1 0-1 1-2l3 3 2-3c1 2 1 3 1 5l1 2c1 2 1 3 2 4l-3 1c-2 1-3 1-4 2h-1c-2 1-3 2-5 3 1-2 4-4 5-5 1 0 1 0 2-1h1 0 1c-1-2-2-2-3-2h0-3c-1-1-1-1-1-2v-1z" class="i"></path><path d="M687 525c2-1 2-1 4-1v4h0-3c-1-1-1-1-1-2v-1z" class="AB"></path><path d="M684 526l1 1h0v2s1 1 2 0h1c0 2-1 1 0 3 1-1 2-3 3-4h0c1 0 2 0 3 2h-1 0-1c-1 1-1 1-2 1-1 1-4 3-5 5 0 1-2 2-2 4-1 1-2 2-2 4l-1 3c-1 2-3 4-4 6h-2c3-3 4-7 6-11l1-3c0-3 1-6 1-9l2-4z" class="J"></path><path d="M685 536c2-1 3-2 5-3h1c1-1 2-1 4-2 1 1 1 2 2 4-1 1-9 7-10 7l-2 6c-1 0-1-1-1-2v-1l-4 2 1-3c0-2 1-3 2-4 0-2 2-3 2-4z" class="W"></path><path d="M683 540h0c0 1 1 1 0 2v2c1 0 1 0 2-1 1 0 1 0 2-1l-2 6c-1 0-1-1-1-2v-1l-4 2 1-3c0-2 1-3 2-4z" class="c"></path><defs><linearGradient id="AZ" x1="557.757" y1="709.774" x2="561.243" y2="711.226" xlink:href="#B"><stop offset="0" stop-color="#a3a5af"></stop><stop offset="1" stop-color="#c4c5c6"></stop></linearGradient></defs><path fill="url(#AZ)" d="M577 663v1c2-2 3-8 5-11h0v2 1l-44 114v-2c0-2 1-3 0-5l20-54c0-1 0-2 1-3h0v-1-1l1-1v-1c0-1 0-1 1-2v-1l1-1v-1l-2 2h0c0-1 0-2 1-3 1-2 2-5 2-7 2-5 4-10 7-14l1-3h0v-1c0-1 0-1 1-2s1-3 2-5l1-2c1-2 2-3 4-4l-2 5z"></path><path d="M571 672c1 0 1-2 2-2 0-2 0-2 1-3l1-1-4 11-1-2 1-3z" class="I"></path><path d="M574 664l1-2c1-2 2-3 4-4l-2 5-2 3-1 1c-1 1-1 1-1 3-1 0-1 2-2 2h0v-1c0-1 0-1 1-2s1-3 2-5z" class="Ab"></path><path d="M570 675l1 2-8 20a104.13 104.13 0 0 1-5 12c0-1 0-2 1-3h0v-1-1l1-1v-1c0-1 0-1 1-2v-1l1-1v-1l-2 2h0c0-1 0-2 1-3 1-2 2-5 2-7 2-5 4-10 7-14z" class="f"></path><path d="M670 555l4-2h2l-2 2c-3 2-5 3-6 6h0c-4 4-6 11-7 17-1 2-2 6-1 8v1c-1 1-2 2-4 3-1 1-3 3-5 4h-3c-2 0-4 2-5 3h-2c1-4 2-9 5-12 1 0 1-1 3-2v-2l-1-1 4-11c0-1 1-3 2-4 0-2 2-5 3-8 2 1 4 2 6 1h0c2-1 3-2 5-3l1 1 1-1h0z" class="AA"></path><path d="M648 590h0l1 1-2 1h-1v-1l2-1z" class="AS"></path><path d="M649 581c0-1 1-1 1-2s1-1 1-1l1 4c-1 1-2 1-3 1v-2z" class="m"></path><path d="M654 565l1-1 1 1v2c-1 1-1 2-1 3v2l-1 1c-1-2 0-2-2-4 0-1 1-3 2-4z" class="k"></path><path d="M652 569c2 2 1 2 2 4-1 1-1 2-2 4 0 1 0 0-1 1 0 0-1 0-1 1s-1 1-1 2l-1-1 4-11z" class="X"></path><path d="M657 581h2v1l-1 1v2 2c-1 1-1 2-2 3s-3 3-5 4h-3c2-2 4-3 5-5v-2-1c0-1 1-2 1-3h0l1-1 2-1z" class="AC"></path><path d="M657 581h2v1l-1 1v2 2h-3v-1l1-1-1-2v-1l2-1z" class="AM"></path><path d="M670 555l4-2h2l-2 2c-3 2-5 3-6 6h0c-4 4-6 11-7 17-1 2-2 6-1 8v1c-1 1-2 2-4 3 1-1 1-2 2-3v-2-2l1-1v-1h-2 0c2-2 1-4 2-6 1 0 1 0 1-1s0-2 1-3v-2-2l1-1c0-2 0-3 1-4 1-2 2-2 2-3v-1h-2c2-1 3-2 5-3l1 1 1-1h0z" class="w"></path><path d="M668 555l1 1 1-1h0c-3 4-6 7-7 11-1 2-2 3-2 5v-2-2l1-1c0-2 0-3 1-4 1-2 2-2 2-3v-1h-2c2-1 3-2 5-3z" class="k"></path><path d="M680 547l4-2v1c0 1 0 2 1 2-2 5-5 8-7 13l1 1-1 3 2-2c0 2 0 3 1 5l-1 3v1c-1 2-1 3-2 5-1 1-2 3-2 5h-1c-1 2-2 3-3 5l-3 2c-2 2-4 4-7 5 1-3 4-4 5-6v-1l1-1-1-2c-1 3-3 4-5 6-1 0-1 0-1-1l-1-2v-1c-1-2 0-6 1-8 1-6 3-13 7-17h0c1-3 3-4 6-6l2-2c1-2 3-4 4-6z" class="AR"></path><path d="M680 547l4-2v1c0 1 0 2 1 2-2 5-5 8-7 13l-2 1v-1c0-1 0-1 1-2 0-1 1-1 1-2l1-1c1-2 1-4 2-5-1 0-2 2-3 2 0 1-1 1-1 1-2 1-2 1-3 1l2-2c1-2 3-4 4-6z" class="W"></path><path d="M661 578c1-6 3-13 7-17v1l-3 6c0 2 0 4-1 5l1 1 3-3c2-2 3-6 6-7v1l-2 3-1 3-2 5c-1 1-1 3-1 4l-1 2v2c-1 3-3 4-5 6-1 0-1 0-1-1l-1-2v-1c-1-2 0-6 1-8z" class="c"></path><path d="M661 578c1-6 3-13 7-17v1l-3 6c0 2 0 4-1 5l1 1 3-3c-1 2-2 4-2 6l-1 1 1 1v1 1c0 2-1 4-3 5-1 0-1 0-2-2 0-1 0-2 1-3v-1l-1-1v-1z" class="W"></path><path d="M678 561l1 1-1 3 2-2c0 2 0 3 1 5l-1 3v1c-1 2-1 3-2 5-1 1-2 3-2 5h-1c-1 2-2 3-3 5l-3 2c-2 2-4 4-7 5 1-3 4-4 5-6v-1l1-1-1-2v-2l1-2c0-1 0-3 1-4l2-5 1-3 2-3v-1l2-2 2-1z" class="X"></path><path d="M671 584v-3c0-1 2-1 3-2v1c-1 1-1 2-1 3l-2 1z" class="x"></path><path d="M673 583v1h0l1-1c0-1 1-1 1-1-1 2-2 3-3 5l-3-2h1l1-1 2-1z" class="q"></path><path d="M674 565h2c1 0 1 1 1 1l-3 4h-1c-1-1-1-1-1-2l2-3z" class="AW"></path><path d="M678 561l1 1-1 3-1 1s0-1-1-1h-2v-1l2-2 2-1zm-7 10l1-3c0 1 0 1 1 2h1l-2 6c-1 1-1 2-2 3l-1-3 2-5z" class="J"></path><path d="M671 571v4l1 1c-1 1-1 2-2 3l-1-3 2-5z" class="e"></path><path d="M667 584v-2l1-2c0-1 0-3 1-4l1 3c0 2-1 4-1 6l3 2-3 2c-2 2-4 4-7 5 1-3 4-4 5-6v-1l1-1-1-2z" class="w"></path><path d="M706 476l1-1c0 1 0 1 1 2h3 3l-1 4v3l-2 1-1 2-1 3c-1 1-1 1-2 1-1 1-1 1-1 2-1 2-2 3-2 5l-1 1h-2l-1 2c0 1-1 2-2 3h-1v3l-2-1c0 1-1 2-1 3l-1-1-1 2v-3l-2-1v-1l-3-1-2-2c0-1 0-2-1-3l-1-3 2-5 1-3 3-5 1-3h0c3 0 4-1 7-1 1-1 1-1 2-1l1-1 1 1c1 0 3-1 4-2h1z" class="Al"></path><path d="M706 476l1-1c0 1 0 1 1 2-1 1-2 1-2 2l-1-3h1z" class="c"></path><path d="M711 477h3l-1 4h-2c1-2 1-2 0-4z" class="x"></path><path d="M705 489c1-1 0-2 0-3 0 0 1-1 2-1 0-2 0-1 1-2v1 1c0 1 0 3-1 4h-2z" class="AR"></path><path d="M707 489c1-1 1-3 1-4v1l2 1-1 3c-1 1-1 1-2 1l-1-1 1-1z" class="Q"></path><path d="M698 486h1l1 1v1l-2 4h-1l-1-2c0-2 1-2 2-4z" class="AA"></path><path d="M705 489h2l-1 1c-1 1-2 2-4 2-1 1-2 2-3 4h0l-3 1 3-4c0-1 1-3 3-3s1 0 3-1z" class="AS"></path><path d="M711 481h2v3l-2 1-1 2-2-1v-1-1-1c1-1 2-2 3-2z" class="m"></path><path d="M708 484c1-1 1-1 3-1v2l-1 2-2-1v-1-1z" class="v"></path><path d="M701 478c1 0 3-1 4-2l1 3-2 3-1 1h-3v-1c-1 0-2-1-2-2l2-1 1-1z" class="AH"></path><path d="M700 479l3 1v2h0l-3 1v-1c-1 0-2-1-2-2l2-1z" class="AC"></path><path d="M706 490l1 1c-1 1-1 1-1 2-1 2-2 3-2 5l-1 1h-2l-1 2c0 1-1 2-2 3-1-1-1-2-1-3h2c0-1 1-2 1-4 0-1 0-1-1-1 1-2 2-3 3-4 2 0 3-1 4-2z" class="Z"></path><path d="M706 490l1 1c-1 1-1 1-1 2-1 2-2 3-2 5l-1 1h-2l1-1c1-2 0-4 0-6 2 0 3-1 4-2z" class="n"></path><path d="M690 480h0c3 0 4-1 7-1 1-1 1-1 2-1l1-1 1 1-1 1-2 1c0 1 1 2 2 2-1 1-3 0-4 0h-1l-1 4h-2c-1 0-3-2-3-3l1-3z" class="W"></path><path d="M686 488h3c1 1 1 1 2 1v1l1 2h0c1 2 2 4 4 5h0l3-1h0c1 0 1 0 1 1 0 2-1 3-1 4h-2c0 1 0 2 1 3h-1v3l-2-1c0 1-1 2-1 3l-1-1-1 2v-3l-2-1v-1l-3-1-2-2c0-1 0-2-1-3l-1-3 2-5 1-3z" class="J"></path><path d="M695 506h0l2-2v3l-2-1z" class="Z"></path><path d="M693 501h1v1l-1 6-1 2v-3l-2-1v-1-2h3v-2z" class="W"></path><path d="M692 492c1 2 2 4 4 5h0l3-1c-2 2-3 4-5 6v-1h-1l-1-3h1c0-2 0-2-1-4v-2z" class="x"></path><path d="M686 499c1 0 2-1 3-1h3l1 3v2h-3v2l-3-1-2-2c0-1 0-2-1-3h2z" class="c"></path><path d="M684 499h2c1 1 2 2 4 3v1 2l-3-1-2-2c0-1 0-2-1-3z" class="v"></path><path d="M686 488h3c1 1 1 1 2 1v1l1 2h0v2c1 2 1 2 1 4h-1-3c-1 0-2 1-3 1h-2l-1-3 2-5 1-3z" class="n"></path><path d="M690 495l2 3h-3l-1-1v-3l2 1z" class="Y"></path><path d="M686 488h3c1 1 1 1 2 1v1h0-1v5l-2-1c0-1-1-2-1-4l-2 1 1-3z" class="AA"></path><path d="M685 491l2-1c0 2 1 3 1 4v3l1 1c-1 0-2 1-3 1h-2l-1-3 2-5z" class="k"></path><path d="M715 320l1 3h2l-1 1-10 27-15 44c-1 3-3 7-3 10h0c0-2-1-3 0-5v-3h-1l3-15 2-35v1l1-7v5c1 1 1 2 2 3h1-1l2-2v-2l1-4v-1h2c1-1 0-1 2-2-1 2-1 3-1 5h1c1-1 2-3 2-5 1-1 2-3 3-3l3-6 1-3 1-1c0-1 1-3 1-3l1-2z" class="O"></path><path d="M693 380v-4c1 1 1 3 1 5l1 1c-1 4-3 11-5 13l-1 2h-1l3-15h0l2-2z" class="L"></path><path d="M693 380v-4c1 1 1 3 1 5l1 1c-1 4-3 11-5 13 1-5 2-9 3-15z" class="Aa"></path><defs><linearGradient id="Aa" x1="718.207" y1="324.006" x2="705.504" y2="337.727" xlink:href="#B"><stop offset="0" stop-color="#171c1b"></stop><stop offset="1" stop-color="#323a44"></stop></linearGradient></defs><path fill="url(#Aa)" d="M715 320l1 3h2l-1 1c-1 1-1 2-2 3l-6 16c-1-2-1-3-2-4l1-4 3-6 1-3 1-1c0-1 1-3 1-3l1-2z"></path><path d="M699 355c1 1 1 1 1 2l-1 3v1 1h2l-6 20-1-1c0-2 0-4-1-5l3-15 1-1 1-1 1-4z" class="g"></path><path d="M705 338c1-1 2-3 3-3l-1 4c1 1 1 2 2 4l-4 8-1 4-3 7h-2v-1-1l1-3c0-1 0-1-1-2 1-2 3-8 5-9l-1-1v-2c1-1 2-3 2-5z" class="AL"></path><path d="M699 355c1-2 3-8 5-9-1 4-2 7-4 11 0-1 0-1-1-2z" class="Aa"></path><path d="M707 339c1 1 1 2 2 4l-4 8-1 4-3 7h-2v-1-1l1 1h0l5-13c1-2 2-3 2-5h-1l-1 1h-1c1-2 2-3 3-5z" class="I"></path><path d="M699 340h2c1-1 0-1 2-2-1 2-1 3-1 5h1v2l1 1c-2 1-4 7-5 9l-1 4-1 1-1 1-3 15v4l-2 2h0l2-35v1l1-7v5c1 1 1 2 2 3h1-1l2-2v-2l1-4v-1z" class="j"></path><path d="M694 346c1 1 1 2 2 3h1-1l2-2c-1 2-1 4-2 6v3c-1 3-1 7-2 9-1-2 0-4 0-6v-13z" class="AD"></path><path d="M699 340h2c1-1 0-1 2-2-1 2-1 3-1 5h1v2l1 1c-2 1-4 7-5 9l-1 4-1 1-1 1v-3h0v-2-3c1-2 1-4 2-6v-2l1-4v-1z" class="Ac"></path><path d="M699 340h2c1-1 0-1 2-2-1 2-1 3-1 5h1v2s-1-1-2-1v1 1 1c0 1-1 1-1 1h-1v-1c1-1 1-1 1-2h-1-1l1-4v-1z" class="I"></path><path d="M662 590c2-2 4-3 5-6l1 2-1 1v1c-1 2-4 3-5 6 3-1 5-3 7-5 2 1 2 0 3 2l-5 7c-5 9-14 18-21 26l-2 2c-3 2-5 5-8 8l-4-1-1 2-1-1 5-10 1-1c-1-2 0-4 0-6l-1-3c0-1 1-2 1-3v-1c0-4 3-9 5-13h2c1-1 3-3 5-3h3c2-1 4-3 5-4 2-1 3-2 4-3l1 2c0 1 0 1 1 1z" class="AN"></path><path d="M636 617v-1l1-3c0 1 0 2 1 4v-1c0-1 1-1 2-1-1 3-3 5-4 8-1-2 0-4 0-6z" class="u"></path><path d="M643 605l1 3-4 7c-1 0-2 0-2 1v1c-1-2-1-3-1-4l1-2 1 1 3-6 1-1z" class="a"></path><path d="M645 603c4-3 7-6 12-7-4 4-9 7-13 12l-1-3 2-2z" class="v"></path><path d="M662 590c2-2 4-3 5-6l1 2-1 1v1c-1 2-4 3-5 6-1 0-3 2-5 2-5 1-8 4-12 7l-1-1c2-2 4-4 7-5v-2-1c2-1 4-3 5-4 2-1 3-2 4-3l1 2c0 1 0 1 1 1z" class="J"></path><path d="M660 587l1 2c0 1 0 1 1 1-4 3-7 5-11 7v-2-1c2-1 4-3 5-4 2-1 3-2 4-3z" class="X"></path><path d="M643 597c1-1 3-3 5-3h3v1 2c-3 1-5 3-7 5l1 1-2 2-1 1-3 6-1-1-1 2-1 3v1l-1-3c0-1 1-2 1-3v-1c0-4 3-9 5-13h2z" class="Z"></path><path d="M638 611c0-1 1-3 2-4 1-2 2-3 4-5l1 1-2 2-1 1-3 6-1-1z" class="AJ"></path><path d="M643 597c1-1 3-3 5-3h3v1l-10 7c1-1 2-3 2-5z" class="a"></path><path d="M632 633c3-2 4-5 6-7 2-3 5-5 7-7 4-5 8-10 13-13 2-2 5-3 7-5 0-1 1-2 2-3-5 9-14 18-21 26l-2 2c-3 2-5 5-8 8l-4-1z" class="w"></path><defs><linearGradient id="Ab" x1="583.387" y1="646.51" x2="574.214" y2="641.81" xlink:href="#B"><stop offset="0" stop-color="#4a5161"></stop><stop offset="1" stop-color="#6e7582"></stop></linearGradient></defs><path fill="url(#Ab)" d="M603 597c0 1 0 1-1 2h0v1 1h0l-11 28 1 2-6 16c-2 3-3 6-4 9v-1-2h0c-2 3-3 9-5 11v-1l2-5c-2 1-3 2-4 4l-1 2c-1 2-1 4-2 5s-1 1-1 2v1h0l-1 3c-3 4-5 9-7 14v-4h-1l-1 5h0c-1 3-2 5-4 7 0-1 1-3 2-4h-2c-1-1-1-2-1-4l-1 1-1 1h0l-1-1 7-17c2-5 5-9 7-14v-1l23-48c0 1 1 2 0 4v1c-1 1-2 2-1 3 0-1 1-3 2-4l2-3c0 2 0 3-1 5v1l1-2c0-1 1-2 1-3 2-4 3-7 5-11l4-4z"></path><path d="M563 681v-1l1-1 2-5h0l-1-1c0-2 1-3 2-5h1c0-1 0-2 1-3l1 1c1 0 2-1 2-1-3 4-6 11-9 16z" class="AL"></path><path d="M572 665l1-1h1c-1 2-1 4-2 5s-1 1-1 2v1h0l-1 3c-3 4-5 9-7 14v-4h-1l1-4c3-5 6-12 9-16z" class="p"></path><path d="M579 658c1-1 2-5 2-7l10-22 1 2-6 16c-2 3-3 6-4 9v-1-2h0c-2 3-3 9-5 11v-1l2-5z" class="t"></path><defs><linearGradient id="Ac" x1="623.262" y1="569.039" x2="604.707" y2="554.526" xlink:href="#B"><stop offset="0" stop-color="#545b69"></stop><stop offset="1" stop-color="#7b818f"></stop></linearGradient></defs><path fill="url(#Ac)" d="M633 524l2-1c1 1 0 2 2 2h1c-1 1-1 2-2 3 0 2 0 3 1 4l-4 5c-9 15-17 31-23 47l-3 5h0l-1 1c0 1 0 0-1 1-1 2-2 4-2 6l-4 4c-2 4-3 7-5 11 0 1-1 2-1 3l-1 2v-1c1-2 1-3 1-5l-2 3c-1 1-2 3-2 4-1-1 0-2 1-3v-1c1-2 0-3 0-4l15-38 2-6c1-1 1-2 1-3 1-5 4-9 4-14 0-1 1-2 1-3 1-1 1 0 1-1 1-3 1-4 3-6h1c0-2 2-4 2-7 1 0 3-2 3-2l4-5h3l3-1z"></path><path d="M612 561v1h1l-1 2h0-1l1-3zm15-36h3c-1 2-2 3-3 4l-2 2-1-1v1c-1 1-1 2-2 3l1-4 4-5z" class="O"></path><path d="M633 537l-1-1c0-3 2-6 4-8 0 2 0 3 1 4l-4 5z" class="AL"></path><path d="M620 532c1 0 3-2 3-2l-1 4-3 3c0 1 1 1 0 2l-1 1h-1c-3 3-1 7-5 9 0-1 1-2 1-3 1-1 1 0 1-1 1-3 1-4 3-6h1c0-2 2-4 2-7z" class="AU"></path><path d="M633 524l2-1c1 1 0 2 2 2h-1c-2 1-3 3-5 4-1-1-1-1-2-1h0l-2 2v-1c1-1 2-2 3-4l3-1z" class="AQ"></path><path d="M630 525l3-1c-1 2-1 3-3 4h-1 0l-2 2v-1c1-1 2-2 3-4z" class="AY"></path><path d="M605 572v1 2h1 0c-2 3-3 6-4 9 1 7-3 15-6 21l-3 6h0l-2 3c-1 1-2 3-2 4-1-1 0-2 1-3v-1c1-2 0-3 0-4l15-38z" class="O"></path><path d="M591 614c0-3 2-5 4-8 0-2 0-4 1-6 0-2 1-3 2-5 1-4 2-7 4-11 1 7-3 15-6 21l-3 6h0l-2 3z" class="AU"></path><defs><linearGradient id="Ad" x1="675.711" y1="539.541" x2="699.22" y2="550.145" xlink:href="#B"><stop offset="0" stop-color="#c4bebd"></stop><stop offset="1" stop-color="#f9f5f3"></stop></linearGradient></defs><path fill="url(#Ad)" d="M715 449l1 2h0 1v-3h1v3c1 1 0 1 1 2 1 14-1 28-3 42-2 9-3 18-6 27-3 11-7 21-11 31-3 7-7 14-10 21-5 8-10 16-15 23-4 5-8 11-12 16s-8 9-12 13c-2 2-3 5-6 6v1c-1 1-1 2-2 3-2-1-2-2-3-4 2-1 4-3 5-5v-1l2-2c7-8 16-17 21-26l5-7c-1-2-1-1-3-2l3-2c1-2 2-3 3-5h1c0-2 1-4 2-5 1-2 1-3 2-5v-1l1-3c-1-2-1-3-1-5l-2 2 1-3-1-1c2-5 5-8 7-13l2-6c1 0 9-6 10-7-1-2-1-3-2-4l3-1c-1-1-1-2-2-4l-1-2c0-2 0-3-1-5v-2c-1-1-1-3-1-4 0-2 0-3 1-4h0c0-1 1-2 1-3l2 1v-3h1c1-1 2-2 2-3l1-2h2l1-1c0-2 1-3 2-5 0-1 0-1 1-2 1 0 1 0 2-1l1-3 1-2 2-1v-3l1-4h-3-3c-1-1-1-1-1-2v-6c0-2 0-4 1-6h0l2-2 1 1h0c2-1 2-2 4-2v-11z"></path><path d="M708 503l2-2-3 16-2-2c1-1 1-1 1-2-1-1-1-2-1-3v-3h2l1-3v-1z" class="AG"></path><path d="M707 507c0 2 0 4-1 6-1-1-1-2-1-3v-3h2z" class="x"></path><path d="M713 484l-3 17-2 2c1-1 1-3 2-5v-6l-1-1v-1l1-3 1-2 2-1z" class="AB"></path><path d="M707 491c1 0 1 0 2-1v1l1 1v6c-1 2-1 4-2 5v1h-1v-3-4c-1-1 0-1 0-3l-1-1c0-1 0-1 1-2z" class="W"></path><path d="M707 491c1 0 1 0 2-1v1c0 1-1 3-2 3l-1-1c0-1 0-1 1-2z" class="AG"></path><path d="M706 493l1 1c0 2-1 2 0 3v4 3h1l-1 3h-2-1v-3h-2l1-1-1-1c0-1 0-1 1-3l1-1c0-2 1-3 2-5z" class="v"></path><path d="M703 499l1-1c0 3 0 3-1 5l-1-1c0-1 0-1 1-3z" class="AG"></path><path d="M704 507c0-2 1-4 2-5 0-2 0-3 1-5v4 3h1l-1 3h-2-1zm-26 70l1 1c1-1 2-1 3-1-2 4-5 8-8 12 0 0-1 2-2 2-1-2-1-1-3-2l3-2c1-2 2-3 3-5h1c0-2 1-4 2-5z" class="c"></path><path d="M698 504c1-1 2-2 2-3l1-2h2c-1 2-1 2-1 3l1 1-1 1h2v3h1v3c0 1 0 2 1 3 0 1 0 1-1 2l-3-3-5-4v-1-3h1z" class="x"></path><path d="M698 504c1-1 2-2 2-3l1-2h2c-1 2-1 2-1 3l1 1-1 1-1 2v-1h-3l-1 3v-1-3h1z" class="AH"></path><path d="M702 504h2v3h1v3c0 1 0 2 1 3 0 1 0 1-1 2l-3-3 1-1c-1 0-1-1-1-1l-1-1v-1-2l1-2z" class="n"></path><path d="M702 504h2v3h1v3l-2-1h0l-2-1v-2l1-2z" class="Z"></path><path d="M701 533l1 1c-5 13-10 25-17 37l-3 6c-1 0-2 0-3 1l-1-1c1-2 1-3 2-5v-1l1-3 2-1v1l1 1c3-2 3-5 5-8 2-6 5-11 7-17v-1c1-1 1-1 1-3h0l1-1h1v-2c1-1 1-2 2-4z" class="AA"></path><path d="M711 462h0c2-1 2-2 4-2v1c0 4-1 9-1 14v2h-3-3c-1-1-1-1-1-2v-6c0-2 0-4 1-6h0l2-2 1 1z" class="AS"></path><path d="M710 461l1 1v3l-1 3c-1-1-1-2-1-3v-1l-1-1 2-2z" class="W"></path><path d="M710 472c1 0 2-1 3-2v-4c-1-1-1-1 0-2v-2h0l2-1c0 4-1 9-1 14l-1-1-3-2z" class="AA"></path><path d="M708 463l1 1v1c0 1 0 2 1 3v3 1l3 2 1 1v2h-3-3c-1-1-1-1-1-2v-6c0-2 0-4 1-6h0z" class="n"></path><path d="M713 474c-2 1-2 2-3 2s-2-1-2-1c-1-2 0-5 0-6l2 2v1l3 2z" class="v"></path><path d="M695 506l2 1v1l5 4 3 3 2 2-1 3-1 4-2 6-1 4-1-1v-1h-1l2-2h-1l-3 1v-1c-1-1-1-2-2-4l-1-2c0-2 0-3-1-5v-2c-1-1-1-3-1-4 0-2 0-3 1-4h0c0-1 1-2 1-3z" class="AS"></path><path d="M697 511l6 6-1 1v1l-1 2-1 1v-1l-2-3c0-1 0-2 1-3 0-1-1-3-2-4z" class="W"></path><path d="M695 506l2 1v1l5 4 3 3 2 2-1 3-3-3-6-6c0-1-2-1-3-2h0c0-1 1-2 1-3z" class="AJ"></path><path d="M703 517l3 3-1 4-2 6-1 4-1-1v-1h-1l2-2h-1l-3 1v-1c-1-1-1-2-2-4h3c1-1 0-2 1-4l1-1 1-2v-1l1-1z" class="i"></path><path d="M703 517l3 3-1 4c-1-3-1-3-3-5v-1l1-1z" class="X"></path><path d="M700 522l1-1c0 3 0 4 1 6v2l1 1-1 4-1-1v-1h-1l2-2h-1l-3 1v-1c-1-1-1-2-2-4h3c1-1 0-2 1-4z" class="c"></path><path d="M698 531l3-1h1l-2 2h1v1c-1 2-1 3-2 4v2h-1l-1 1h0c0 2 0 2-1 3v1c-2 6-5 11-7 17-2 3-2 6-5 8l-1-1v-1l-2 1c-1-2-1-3-1-5l-2 2 1-3-1-1c2-5 5-8 7-13l2-6c1 0 9-6 10-7-1-2-1-3-2-4l3-1v1z" class="AR"></path><path d="M698 530v1c1 1 1 2 2 3-1 2-3 3-4 5-1 1-1 1-2 1v-2l3-2v-1c-1-2-1-3-2-4l3-1z" class="k"></path><path d="M686 559c-1 2-3 5-3 8l-2 1c-1-2-1-3-1-5l3-3c1-1 2-1 3-1z" class="AB"></path><path d="M697 535v1l-3 2v2c-1 1-1 2-1 4-1 1-1 1-1 2-1 1-2 3-3 4 0 3-3 6-3 9-1 0-2 0-3 1l-3 3-2 2 1-3-1-1c2-5 5-8 7-13l2-6c1 0 9-6 10-7z" class="AH"></path><path d="M683 560c0-1 0-2 1-3v-1c2-2 3-4 5-6 0 3-3 6-3 9-1 0-2 0-3 1z" class="AO"></path><path d="M697 535v1l-3 2c-2 2-3 3-4 5s-3 5-4 7l-5 9c0 1-1 2-2 3l-1-1c2-5 5-8 7-13l2-6c1 0 9-6 10-7z" class="Ad"></path><defs><linearGradient id="Ae" x1="696.925" y1="328.041" x2="642.992" y2="360.858" xlink:href="#B"><stop offset="0" stop-color="#020101"></stop><stop offset="1" stop-color="#1b2226"></stop></linearGradient></defs><path fill="url(#Ae)" d="M655 269c2-1 4-2 6-2 3 3 3 8 6 12l13 24c4 5 7 11 9 18 3 8 3 18 4 26l-2 35-3 15h1v3c-1 2 0 3 0 5l-6 18-3 9c-1 0-1-1-1-2-1 1-2 2-2 4h-1c-2 3-3 7-4 11l-4 13-1-1 1-2c0-3 1-7 2-10 0-1 1-2 1-3v-1c1-5 3-9 4-13l2-12c0-2 0-5 1-6l3-19c2-18 0-35-5-53-2-8-5-16-7-24v-1h-1c-1-3-1-8-3-11l-7-19c-2-3-4-7-5-11v-1c1 0 1-1 1-2h1z"></path><path d="M688 397h1v3c-1 2 0 3 0 5l-6 18-3 9c-1 0-1-1-1-2 2-2 2-6 3-9l6-24z" class="AY"></path><defs><linearGradient id="Af" x1="684.302" y1="309.304" x2="663.605" y2="325.007" xlink:href="#B"><stop offset="0" stop-color="#111316"></stop><stop offset="1" stop-color="#2c373e"></stop></linearGradient></defs><path fill="url(#Af)" d="M653 272c2 2 3 6 5 8h0c0-1 1-1 1-2l1 1c1 1 2 2 2 3 1 1 1 2 2 3s2 1 3 1c2 2 2 5 4 8 5 10 12 18 13 30 2 4 2 10 3 14s2 7 2 11 0 8-1 12c0 1 0 4-1 5v7c-1-6 1-12-3-16-1-1-1-4-1-6l-2-11-9-28c-1-3-2-7-3-10-1 0-1-1-2-2h0c1 3 2 6 2 9v4h-1c-1-3-1-8-3-11l-7-19c-2-3-4-7-5-11z"></path><path d="M682 340v-6h1v4 1l-1 1h0z" class="V"></path><path d="M658 280h0c0-1 1-1 1-2l1 1h0c-1 1 0 2-1 3h0l1 2c-1-1-2-2-2-4z" class="M"></path><path d="M669 302c0-1 0-1 1-2 1 2 1 3 1 5l1 1 1 2h-1c0 2 0 2 1 3l-1 1c-1-3-2-7-3-10z" class="V"></path><path d="M669 309l-4-10c0-2-1-4-2-5 0-2-1-4-1-5h0 1c1 1 1 3 2 5 0 2 2 4 2 6h0c1 3 2 6 2 9z" class="d"></path><path d="M684 324c2 4 2 10 3 14s2 7 2 11h-1c-1-3-1-7-2-10l-1-1c0-3 0-6-1-9v-5h0z" class="M"></path><path d="M681 340h1 0c0 3 1 7 2 10s3 6 4 10v1c0 1 0 4-1 5v7c-1-6 1-12-3-16-1-1-1-4-1-6l-2-11z" class="I"></path><path d="M669 309c0-3-1-6-2-9h0c1 1 1 2 2 2 1 3 2 7 3 10l9 28 2 11c0 2 0 5 1 6 4 4 2 10 3 16 0 16-3 31-6 46 0 1-1 3 0 4v-1c1 0 0 0 1-1-1 3-1 7-3 9-1 1-2 2-2 4h-1c-2 3-3 7-4 11l-4 13-1-1 1-2c0-3 1-7 2-10 0-1 1-2 1-3v-1c1-5 3-9 4-13l2-12c0-2 0-5 1-6l3-19c2-18 0-35-5-53-2-8-5-16-7-24v-1-4z" class="s"></path><path d="M678 410h1l-3 16v-1l2-4v-1l1-2v-2c0-1 0-2 1-3v-1-2h1c-2 11-6 21-9 32h-1v-1c1-5 3-9 4-13l2-12c0-2 0-5 1-6z" class="M"></path><path d="M684 357c4 4 2 10 3 16 0 16-3 31-6 46 0 1-1 3 0 4v-1c1 0 0 0 1-1-1 3-1 7-3 9-1 1-2 2-2 4h-1c-2 3-3 7-4 11l-4 13-1-1 1-2c0-3 1-7 2-10 0-1 1-2 1-3h1c3-11 7-21 9-32 4-17 5-35 3-53z" class="O"></path><path d="M681 419c0 1-1 3 0 4v-1c1 0 0 0 1-1-1 3-1 7-3 9-1 1-2 2-2 4h-1l5-15z" class="V"></path><defs><linearGradient id="Ag" x1="678.627" y1="221.235" x2="544.758" y2="460.412" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#293036"></stop></linearGradient></defs><path fill="url(#Ag)" d="M567 202l2-1c3 2 5 5 9 6v2h-2v1c6 1 12 1 18 1h22c2 0 7 0 8 1 5 0 11-1 16 0h-4c2 1 4 1 5 2 3 1 7 0 9 1v1l2 1h7 1c1 1 3 1 4 1h3 0c1 0 2 1 2 1h-2-3 0-3c-2-1-3 0-4 0 2 1 6 2 8 2l7 2 3 1h0l3 1 1 1c4 1 7 1 11 2 1 0 2 1 3 1v1s1 1 1 0l3 1v1c5 2 10 2 16 2-3 0-7 0-9 1h0-3v1 1c-4-2-8-3-12-4-2 0-6 0-7 1-2-1-4-3-6-3s-4-1-6 0l-11-4h1-1l3-2-6-1-4-1-7-1c-1 1-3 1-4 2h-2l-6-1-37-6h-5-1c1 0 2 1 3 1l8 2 1 2 6 3c1 1 2 1 3 1v1c1 0 1 0 2 1 1 0 1 1 2 1l4 3 1 1c2 1 3 2 5 3l1 1h0c4 3 9 7 11 11 1 1 2 2 2 3l1 1 2 2 1 1 1 2 1 1 5 8c1 0 1 1 1 2l2 3v1c1 4 3 8 5 11l7 19c2 3 2 8 3 11h1v1c2 8 5 16 7 24 5 18 7 35 5 53l-3 19c-1 1-1 4-1 6l-2 12c-1 4-3 8-4 13v1c0 1-1 2-1 3-1 3-2 7-2 10l-1 2 1 1-3 9c-1 2-1 4-2 6l-9 23-1-1v-2c-2 3-5 9-6 13 1 1 1 1 1 2-2 4-3 8-6 12l1 1 2-1v1l-1 2h-1c-1 0-3 1-5 2h-1c-2 0-1-1-2-2l-2 1-3 1h-3l2-2h0c1-1 1-2 2-4 6-4 8-11 12-17l3-6 1-2v-1c3-2 3-4 4-7 2-6 5-12 6-18 3-3 4-9 5-13 0-2 0-3 1-5h0l-1-2 2-9c1-1 1-5 1-7 1-1 1-2 1-3 1-1 1-1 1-2v-5c1 0 1-1 0-1-1 1-1 2-2 4v-1-2c1-1 1-1 1-2s1-2 0-3h0v-1h0l1-1v-2-3-1c0-2 1-2 1-4h2 1v-8l1-1c1-2 2-2 2-4h0v-1c1 0 1 0 1-1v-1c1-3 2-9 1-12-2-4-1-7-1-11 0-3-1-5 0-7-1-9-4-17-7-26v9h0c1 1 1 2 0 3 0 1 0 1 1 2 1 3 0 6-1 9-1-3-1-5-1-8 1-3-1-4-1-7s-2-5-2-8l1-1h-1c0-1-1-1-2-1l-1-1h-1-1v-2 2l-2 1h0 0-3v-10c-1-2-2-3-3-4l-3-13h-1v1 1-1l-2-2h-2l-2-2-1 1-1-1v-1c0-1 0-2-1-2h-1l-1-1c1-1 1-1 1-2v-1h-1-1-1-1v-1c-1-1-2-1-2-2h-3l-1-2h-1-1c-2 1-2 1-4 1 0-1 0-2-1-3l2-2v-1c-1 0-1-1-2-2 1-1 1-2 1-3l2-2v-1c-1 0-1 0-2-1 0-1-1-2-2-2l-2-2h0-1-1-2l-1-3h-1v1c-2 0-2-1-3-1l-2 4-1-4s1-1 1-2-1-1-1-2v-1l1-2h1l-1-1 1-1v-2c1 1 1 1 2 1 0-1 1-1 2-2l2-2c1-1 2-1 3-2 0-1 0-1-1-2 1-1 1-1 1-2v-1l2-1h0c-3-2-5-5-8-7-2-2-5-6-8-5h-2s0-1 1-2h0c-1-2-3-3-5-4-5-2-10-5-15-6l-9-3c-7-2-14-2-22-2l6-5c4-3 8-6 11-10z"></path><path d="M657 290c1 3 4 7 4 11-1-1-1-1-1-2-1-1-2-3-3-4v-5z" class="M"></path><path d="M557 212c2-1 5-4 8-6-1 1-1 2-1 3l1-1h1v2c-4 0-7 3-11 3l2-1z" class="B"></path><path d="M634 221l11 1c-1 1-3 1-4 2h-2l-6-1 1-2z" class="Ai"></path><path d="M602 222l-10-3c-1-1-2-2-4-2-4-1-9-1-13-3 2 0 4 0 5 1 3 0 6 1 9 1h1c2 0 4 0 6 1h0-5-1c1 0 2 1 3 1l8 2 1 2z" class="Aj"></path><path d="M674 392v-1c1 0 1 0 1-1v-1c1-3 2-9 1-12-2-4-1-7-1-11 0-3-1-5 0-7l1 5c1 7 0 14 0 21 0 2 1 6 0 8l-1 2c0-1 0-2-1-3z" class="M"></path><path d="M596 217l38 4-1 2-37-6h0z" class="Ac"></path><path d="M645 292l3 9v3h-1v1 1-1l-2-2h-2l-2-2-1 1-1-1v-1l1-1 2-1c2-2 3-3 3-6z" class="AG"></path><path d="M674 392c1 1 1 2 1 3v4c0 1 0 2-1 3 0 2 0 5-1 7 0 2-1 3-1 4 0 2 0 2-1 3 0 1 0 2-1 3v-4l1-1v-1c1-2 0-4 0-6 0-1 1-1 1-2 1-2 0-6-1-8l1-1c1-2 2-2 2-4h0z" class="g"></path><path d="M644 288c0 1 1 4 1 4 0 3-1 4-3 6l-2 1-1 1c0-1 0-2-1-2h-1l-1-1c1-1 1-1 1-2v-1-5l1-1c1 0 2-1 3 0h2 1z" class="W"></path><path d="M637 294v-5l1-1v1h1c1 1 0 3 0 5 0 0 0 1-1 2 1 1 0 1 1 1 0 1 1 1 1 2l-1 1c0-1 0-2-1-2h-1l-1-1c1-1 1-1 1-2v-1z" class="Y"></path><path d="M643 273l-6-12c2 2 4 5 6 7 1 1 2 2 3 4 2 2 3 5 5 7 2 3 4 7 6 11v5l-1-1c-1-3-2-5-3-7l-2-2v-1c0-1-1-3-2-4v1c-2-3-3-6-5-9l-1 1z" class="z"></path><path d="M663 465c1 3 0 5 0 8l-9 23-1-1v-2l10-28z" class="L"></path><path d="M567 202l2-1c3 2 5 5 9 6v2h-2v1c-3 0-7 1-10 0v-2h-1l-1 1c0-1 0-2 1-3-3 2-6 5-8 6h-1c4-3 8-6 11-10z" class="d"></path><path d="M565 206c1-1 2-3 4-3l1 3c-1 1-2 2-4 2h-1l-1 1c0-1 0-2 1-3z" class="AT"></path><path d="M570 206c2 2 3 3 6 3v1c-3 0-7 1-10 0v-2c2 0 3-1 4-2z" class="Ab"></path><path d="M594 211h22c2 0 7 0 8 1 5 0 11-1 16 0h-4c2 1 4 1 5 2 3 1 7 0 9 1v1l2 1c-7 0-14-2-21-3l-17-1h-5c-1 0-2-1-2-1h-3c-4 0-7 0-10-1z" class="M"></path><path d="M624 212c5 0 11-1 16 0h-4c2 1 4 1 5 2-2 0-4-1-7-1s-7 0-10-1z" class="d"></path><path d="M671 397c1 2 2 6 1 8 0 1-1 1-1 2 0 2 1 4 0 6v1l-1 1v4l-2 12c0 3 0 5-2 8-1 5-2 11-4 16 0-2 0-3 1-5h0l-1-2 2-9c1-1 1-5 1-7 1-1 1-2 1-3 1-1 1-1 1-2v-5c1 0 1-1 0-1-1 1-1 2-2 4v-1-2c1-1 1-1 1-2s1-2 0-3h0v-1h0l1-1v-2-3-1c0-2 1-2 1-4h2 1v-8z" class="I"></path><path d="M671 397c1 2 2 6 1 8 0 1-1 1-1 2 0 2 1 4 0 6v1l-1 1v4l-2 12c0 3 0 5-2 8v-4l1-1v-3l1-1c-1-1-1-1-1-2l1-1v-4c0-3 1-5 1-8 1-1 0 0 1-2v-8h1v-8z" class="Aa"></path><path d="M662 225l16 4h4 2 0c1 0 3 0 4 1h3c1 1 2 1 3 1v-1l3 1v1c5 2 10 2 16 2-3 0-7 0-9 1h0-3v1 1c-4-2-8-3-12-4-2 0-6 0-7 1-2-1-4-3-6-3s-4-1-6 0l-11-4h1-1l3-2z" class="L"></path><path d="M678 229h4 2 0c1 0 3 0 4 1h3c1 1 2 1 3 1v-1l3 1v1l-19-3z" class="h"></path><path d="M669 313v1c2 8 5 16 7 24 5 18 7 35 5 53l-3 19c-1 1-1 4-1 6l-2 12c-1 4-3 8-4 13v1c0 1-1 2-1 3-1 3-2 7-2 10l-1 2 1 1-3 9c-1 2-1 4-2 6 0-3 1-5 0-8l14-54 1-5c0-1 0-3 1-4v-4c1-5 1-10 1-14 3-24-6-48-12-71h1z" class="AQ"></path><path d="M643 502l3-6v1c0 2-1 4-2 6l-3 9c0 1-1 1-1 2h3l1-1v-1l1-2h0v-1l1-2 1-1c1 1 1 1 1 2-2 4-3 8-6 12l1 1 2-1v1l-1 2h-1c-1 0-3 1-5 2h-1c-2 0-1-1-2-2l-2 1-3 1h-3l2-2h0c1-1 1-2 2-4 6-4 8-11 12-17z" class="p"></path><path d="M629 523c2 0 4-1 5-2h4 1l-4 2-2 1-3 1h-3l2-2z" class="AZ"></path><path d="M647 506c1 1 1 1 1 2-2 4-3 8-6 12l1 1 2-1v1l-1 2h-1c-1 0-3 1-5 2h-1c-2 0-1-1-2-2l4-2c4-3 7-10 8-15z" class="j"></path><path d="M643 273l1-1c2 3 3 6 5 9v-1c1 1 2 3 2 4v1l2 2c1 2 2 4 3 7l1 1c1 1 2 3 3 4 0 1 0 1 1 2l3 12c0 1 1 5 1 6 2 4 3 10 3 14v9h0c1 1 1 2 0 3 0 1 0 1 1 2 1 3 0 6-1 9-1-3-1-5-1-8 1-3-1-4-1-7s-2-5-2-8l1-1h-1c0-1-1-1-2-1l2-2c2-2-2-6-3-9 0-1 0-3-1-5 0 0 1-1 1-2s-1-2-1-3c-1-1-1-1 0-2-1-1-1-2-2-3 0-2 0-4-1-5l-1-1v-1l-2-2c-1-2-1-4-3-5v-1l-1-2c0-2-1-3-2-5s-1-3-2-5v1c0 2 2 4 2 7 0 1 0 2 1 3h0v1c-1-1-2-5-2-7-2-3-3-7-4-10z" class="V"></path><path d="M665 319c2 4 3 10 3 14v9h0v-1-2c-1-3-1-6-1-9h-1l1-1c0-1-1-1-1-2v-3c-1-2-1-3-1-5z" class="M"></path><path d="M649 290v-1h0c-1-1-1-2-1-3 0-3-2-5-2-7v-1c1 2 1 3 2 5s2 3 2 5l1 2v1c2 1 2 3 3 5l2 2v1l1 1c1 1 1 3 1 5 1 1 1 2 2 3-1 1-1 1 0 2 0 1 1 2 1 3s-1 2-1 2c1 2 1 4 1 5 1 3 5 7 3 9l-2 2-1-1h-1-1v-2 2l-2 1h0 0-3v-10c-1-11-3-21-5-31z" class="Aa"></path><path d="M660 315c1 2 1 4 1 5 1 3 5 7 3 9l-2 2-1-1v-4l-1-1-2 1c0-2 1-1 1-3v-3c1-1 1-3 1-4v-1z" class="AF"></path><path d="M634 267c2 3 4 6 5 10h0l4 9c0 1 1 1 1 2h-1-2c-1-1-2 0-3 0l-1 1v5h-1-1-1-1v-1c-1-1-2-1-2-2h-3l-1-2h-1-1c-2 1-2 1-4 1 0-1 0-2-1-3l2-2v-1c-1 0-1-1-2-2 1-1 1-2 1-3l2-2v-1h1v-1c2-1 3-2 3-3l1-1v-1h2 3c0-1 0-2 1-3z" class="a"></path><path d="M631 290h1l1-1h1v2c-1 1 0 0 0 1l-1 1c-1-1-2-1-2-2v-1z" class="u"></path><path d="M631 285v5 1h-3l-1-2 1-1h1v-1c0-1 1-1 2-2z" class="Q"></path><path d="M626 281c2 0 3 0 4 2 0 0 0 2 1 2-1 1-2 1-2 2-1-1-2-1-4-1 0-1 0-1 1-2v-1h-1l1-2z" class="i"></path><path d="M620 282c1-1 1-2 1-3l2-2v5l1-1c0-1 0-1 1-2l1 2-1 2h1v1c-1 1-1 1-1 2-1-1 0-1-1 0h0l-1-1v-1h-1c-1 0-1-1-2-2z" class="e"></path><path d="M622 284h1v1l1 1h0c1-1 0-1 1 0 2 0 3 0 4 1v1h-1l-1 1h-1-1c-2 1-2 1-4 1 0-1 0-2-1-3l2-2v-1z" class="J"></path><path d="M632 280l1 1c1 2 2 3 4 4h0v1c2 0 3 1 5 1l1-1c0 1 1 1 1 2h-1-2c-1-1-2 0-3 0l-1 1v5h-1c-1-2-1-4-1-7-1-2-2-5-3-7z" class="AB"></path><path d="M639 277l4 9-1 1c-2 0-3-1-5-1v-1h0c-2-1-3-2-4-4l3-1h0l3-3z" class="Q"></path><path d="M633 281l3-1h0 1c1 1 1 1 2 1l1 1c-1 1-1 2-2 2l-1 1h0c-2-1-3-2-4-4z" class="AR"></path><path d="M634 267c2 3 4 6 5 10h0l-3 3h0l-3 1-1-1-1-3h-1-1c-1-1-1-1-2-1v-2h1c1-1 0-2 0-3v-1h2 3c0-1 0-2 1-3z" class="AG"></path><path d="M634 276h3 0l1 1h1 0l-3 3h0c0-2 0-2-2-4z" class="AH"></path><path d="M631 277c1-1 2-1 3-1 2 2 2 2 2 4l-3 1-1-1-1-3z" class="k"></path><path d="M630 270h3c0 2 1 3 1 5v1c-1 0-2 0-3 1h-1-1c-1-1-1-1-2-1v-2h1c1-1 0-2 0-3v-1h2z" class="Y"></path><path d="M618 246l1 1 3 3c2 2 3 4 5 6l2 2c0 1 1 2 1 3 1 1 1 2 2 3s1 2 2 3c-1 1-1 2-1 3h-3-2v1l-1 1c0 1-1 2-3 3v1h-1c-1 0-1 0-2-1 0-1-1-2-2-2l-2-2h0-1-1-2l-1-3h-1v1c-2 0-2-1-3-1l-2 4-1-4s1-1 1-2-1-1-1-2v-1l1-2h1l-1-1 1-1v-2c1 1 1 1 2 1 0-1 1-1 2-2l2-2c1-1 2-1 3-2 0-1 0-1-1-2 1-1 1-1 1-2v-1l2-1z" class="w"></path><path d="M620 261v-2c1 1 1 1 1 2l-1 1v-1z" class="e"></path><path d="M621 269l2-2c1 2 1 3 2 4v-1l1-1v2l1 1c0 1-1 2-3 3v1c-1-1 0-1-1-2s-1-1-1-2c0-2 0-2-1-3z" class="AW"></path><path d="M622 250c2 2 3 4 5 6l-2 2-1 1v1l-1-1c-1 0-1-1-1-2l-2-2v-1c0-2-1-2 0-4l1 1 1-1z" class="AB"></path><path d="M607 259c1 0 1 0 2 1v2h1l1-1c1-2 2-2 3-2 0 1 0 2-1 2s-1 1-2 1c-1 2-2 3-2 5l-1 1-2 4-1-4s1-1 1-2-1-1-1-2v-1l1-2h1l-1-1 1-1z" class="J"></path><path d="M623 259l1 1v-1l1-1 2-2 2 2c0 1 1 2 1 3l-1 1c-1 2-1 3-3 4 0 0 0 1-1 2-1-2-2-3-2-4v-2h-1l1-3z" class="AH"></path><path d="M629 258c0 1 1 2 1 3l-1 1c-1 2-1 3-3 4v-4-1c1-2 1-2 3-3z" class="k"></path><path d="M629 262l1-1c1 1 1 2 2 3s1 2 2 3c-1 1-1 2-1 3h-3-2v1l-1 1-1-1v-2l-1-1c1-1 1-2 1-2 2-1 2-2 3-4z" class="w"></path><path d="M626 266c2-1 2-2 3-4 0 2-1 3-2 5 0 2 1 2-1 4v-2l-1-1c1-1 1-2 1-2z" class="n"></path><path d="M632 264c1 1 1 2 2 3-1 1-1 2-1 3h-3l1-1-1-1c1-2 1-3 2-4z" class="AH"></path><path d="M618 246l1 1c0 1 0 3-1 4h0c0 2 0 4 1 6 0 1 0 2 1 4v1l-1 1-1-1c-1-1-1-2-1-3v-1h-2-1c-1-1-2-1-3-2l2-2c1-1 2-1 3-2 0-1 0-1-1-2 1-1 1-1 1-2v-1l2-1z" class="i"></path><path d="M614 259c1 1 2 2 2 3s1 1 1 2c2 1 3 3 4 5 1 1 1 1 1 3 0 1 0 1 1 2s0 1 1 2h-1c-1 0-1 0-2-1 0-1-1-2-2-2l-2-2h0-1-1-2l-1-3h-1v1c-2 0-2-1-3-1l1-1c0-2 1-3 2-5 1 0 1-1 2-1s1-1 1-2z" class="Aq"></path><path d="M612 268l1 1h1 4c-1 1-1 1-1 2h-1-1-2l-1-3z" class="Ah"></path><path d="M611 262c1 1 2 2 3 1l1-1v2c1 1 1 1 1 2h1v1h0c-2 0-2-2-4-2v2c0 1 1 1 1 2h-1l-1-1h-1v1c-2 0-2-1-3-1l1-1c0-2 1-3 2-5z" class="Ap"></path><defs><linearGradient id="Ah" x1="666.885" y1="551.478" x2="715.223" y2="574.424" xlink:href="#B"><stop offset="0" stop-color="#aba5a3"></stop><stop offset="1" stop-color="#fffffc"></stop></linearGradient></defs><path fill="url(#Ah)" d="M726 396l6-11 1 2v4l1-1h1v1l1 3 1-2 1 3v5l-1 1c0 3 2 8 1 12 1 2 2 6 2 8l1 20c0 3 0 7-1 10 0 3-1 7-1 10s2 7 3 10l1 2 4 7c1 3 4 4 5 6h-1v1h1c-1 1-1 4-2 5-4 3-7 8-9 13l-3 4h0c-1 2-1 4-2 5v3c-1 1-1 1-1 2v1c0 1 0 2-1 3h0c-1 2-1 4-2 6l-4 13c-8 25-18 50-35 70l-10 12c-2 3-7 7-9 10l-1 2c-1 0-1 0-2-1l-1 1c-4 5-8 11-12 16-6 7-13 12-18 20-2 5-6 7-10 10-3 2-6 5-9 7l-13 9h1c-1 1-1 2-3 2v1l-1 1c-2 1-3 2-4 4h-2v-2l-2 1 8-20 2-2c1 0 1 0 1-1s1-2 1-3v-1c0-2 1-3 1-4s1-4 1-5l4-8c2-4 3-10 6-14h0l6-5c0-2 1-4 3-5l1-2 1-2 4 1c3-3 5-6 8-8v1c-1 2-3 4-5 5 1 2 1 3 3 4 1-1 1-2 2-3v-1c3-1 4-4 6-6 4-4 8-8 12-13s8-11 12-16c5-7 10-15 15-23 3-7 7-14 10-21 4-10 8-20 11-31 3-9 4-18 6-27 2-14 4-28 3-42-1-1 0-1-1-2v-3h-1v3h-1 0l-1-2c0-5-1-10-1-15-1-2-1-4-1-6-1-3 0-4 1-7l12-25z"></path><path d="M739 491l1 1c0 1-1 1-1 2-1-1 0-2 0-3z" class="E"></path><path d="M738 505v4c-1 2-1 4-2 5v3c-1 1-1 1-1 2v1c0 1 0 2-1 3h0c1-6 2-12 4-18z" class="l"></path><path d="M732 429c1 1 0 4 1 6v5c0 1 1 2 0 3 0 2 0 3-1 5h0v-1l-1-1v-6c1-3 1-8 1-11z" class="K"></path><path fill="#fff" d="M671 635l20-23h0 2l-10 12c-2 3-7 7-9 10l-1 2c-1 0-1 0-2-1z"></path><path d="M739 481v-3-8h0l3 6v1c1 2 3 4 4 7v1h-1c-2 1-3 2-4 4l-1 3-1-1v-10z" class="B"></path><path d="M751 487h1c-1 1-1 4-2 5-4 3-7 8-9 13l-3 4h0v-4c0-2 1-5 2-7 2-5 5-9 11-11z" class="y"></path><path d="M737 419v-2l1-4h0c1 2 2 6 2 8l1 20c0 3 0 7-1 10 0 3-1 7-1 10s2 7 3 10l1 2h0c-1-1-2-3-2-4l-1-1v-1c-1 3 2 6 2 9l-3-6h0v8 3h0l-1-11h0c0 3-1 5-1 8v9h-1c0-2-1-6 0-9 0-2 1-8 0-10h0l1-4-1-1c1-1 1-3 1-5l1-12h0-1c0-1 0-3-1-4v-4h1l1 1h0v-7-3c-1-3-1-7-1-10z" class="E"></path><path d="M738 432c1 4 0 9 0 14h0-1c0-1 0-3-1-4v-4h1l1 1h0v-7zm-1-13v-2l1-4h0c1 2 2 6 2 8h-1v-2l-1 3v7c-1-3-1-7-1-10z" class="K"></path><defs><linearGradient id="Ai" x1="720.268" y1="396.189" x2="747.947" y2="428.059" xlink:href="#B"><stop offset="0" stop-color="#5f5d60"></stop><stop offset="1" stop-color="#b9b4ae"></stop></linearGradient></defs><path fill="url(#Ai)" d="M726 396l6-11 1 2v4l1-1h1v1l1 3 1-2 1 3v5l-1 1c0 3 2 8 1 12h0l-1 4v2c0 3 0 7 1 10v3 7h0l-1-1h-1v-1l-3-20-4-21h-3z"></path><defs><linearGradient id="Aj" x1="729.317" y1="395.93" x2="741.183" y2="405.07" xlink:href="#B"><stop offset="0" stop-color="#8c8886"></stop><stop offset="1" stop-color="#b4b2b2"></stop></linearGradient></defs><path fill="url(#Aj)" d="M733 391l1-1h1v1l1 3 1-2 1 3v5l-1 1c0 3 2 8 1 12h0l-1 4v2l-4-23v-5z"></path><path d="M737 392l1 3v5l-1 1-1-7 1-2z" class="AL"></path><defs><linearGradient id="Ak" x1="603.409" y1="684.988" x2="621.24" y2="687.418" xlink:href="#B"><stop offset="0" stop-color="#88898b"></stop><stop offset="1" stop-color="#b5aeac"></stop></linearGradient></defs><path fill="url(#Ak)" d="M609 679h1c1-1 1-2 2-3h1c0-1 0-2 1-3 1-2 3-3 4-4 0 1-1 2-1 4 0 1-1 2-1 4 1 0 2 0 3-1h1v-1h1l1-1c1-1 1-1 2-1l1 2c-2 1-3 1-4 2v1h1c1 0 1 0 2-1 1 0 2-1 3-1h0c1-2 0 0 2-1v-1c1-1 1-1 2-1s2 0 2-2c1 0 1-1 1-1 0 1 0 2-1 3 0 0-1 1-2 1-1 1-3 2-4 3-3 2-8 4-10 6v1l1 1h0c1-1 1-1 2-1l1-1c1-1 0 0 2-1v-1l1 1c0 1-2 2-3 4l-1-1c-2 0-2 1-4 1-1 1 0 3-1 5 1 0 2-1 3-1l1-1h1l1-1v1l-13 9h1c-1 1-1 2-3 2v1l-1 1c-2 1-3 2-4 4h-2v-2l-2 1 8-20 2-2c1 0 1 0 1-1s1-2 1-3z"></path><path d="M599 704c3-2 6-5 9-6h1c-1 1-1 2-3 2v1l-1 1c-2 1-3 2-4 4h-2v-2z" class="N"></path><defs><linearGradient id="Al" x1="608.724" y1="655.595" x2="641.296" y2="655.92" xlink:href="#B"><stop offset="0" stop-color="#89888d"></stop><stop offset="1" stop-color="#cac8c7"></stop></linearGradient></defs><path fill="url(#Al)" d="M636 634c3-3 5-6 8-8v1c-1 2-3 4-5 5 1 2 1 3 3 4h0c-5 5-11 7-14 13l1 1 1 1c-1 0-1 0-2 1s0 1-1 1c0 3-1 6-2 8 2-1 4-3 5-5 1-1 2-1 2-2h1c-1 1-1 2-2 3-3 3-6 6-9 8-1 1-3 4-4 4-1 1-3 2-4 4-1 1-1 2-1 3h-1c-1 1-1 2-2 3h-1v-1c0-2 1-3 1-4s1-4 1-5l4-8c2-4 3-10 6-14h0l6-5c0-2 1-4 3-5l1-2 1-2 4 1z"></path><path d="M615 661l1 1c-1 2-2 4-3 7v1h-1l-1-1 4-8z" class="AY"></path><path d="M615 661c2-4 3-10 6-14v4h-1c-1 2-3 7-3 9v1l-1-1v2l-1-1z" class="AQ"></path><path d="M631 635l1-2 4 1c-3 3-6 6-9 8 0-2 1-4 3-5l1-2z" class="AC"></path><defs><linearGradient id="Am" x1="716.198" y1="417.586" x2="730.98" y2="423.665" xlink:href="#B"><stop offset="0" stop-color="#7e7d7d"></stop><stop offset="1" stop-color="#ada7a6"></stop></linearGradient></defs><path fill="url(#Am)" d="M726 396h3l4 21-3 3h0c1 4 0 6-1 9h-2l-1-1c-1 1-1 2-2 3v3 1c-1 1-1 2-1 3 1 1 1 3 1 4l-1 1c0 1 1 2 1 3l-1 1c-1 1 0 1-1 0-1 0-1 0-2 1l-1 1v4c-1-1 0-1-1-2v-3h-1v3h-1 0l-1-2c0-5-1-10-1-15-1-2-1-4-1-6-1-3 0-4 1-7l12-25z"></path><path d="M723 438v3h-1c0-1 1-2 0-2v-1l1-1-1-1c0-2 0-3 1-5v-3-4h0l1 2c0 1-1 2 0 3h0v2 3 1c-1 1-1 2-1 3z" class="L"></path><path d="M718 441c1 2 1 4 3 5h2l-1-1c0-1 0-2 1-3h1l-1 1c0 1 1 2 1 3l-1 1c-1 1 0 1-1 0-1 0-1 0-2 1l-1 1c0-3-1-5-1-8z" class="j"></path><defs><linearGradient id="An" x1="713.639" y1="422.818" x2="719.278" y2="446.968" xlink:href="#B"><stop offset="0" stop-color="#928f8f"></stop><stop offset="1" stop-color="#c1bab8"></stop></linearGradient></defs><path fill="url(#An)" d="M713 428c-1-3 0-4 1-7l2 1c1 2 1 4 1 6 1 4 2 9 1 13 0 3 1 5 1 8v4c-1-1 0-1-1-2v-3h-1v3h-1 0l-1-2c0-5-1-10-1-15-1-2-1-4-1-6z"></path><defs><linearGradient id="Ao" x1="592.47" y1="517.056" x2="627.964" y2="534.928" xlink:href="#B"><stop offset="0" stop-color="#3d4450"></stop><stop offset="1" stop-color="#5c6473"></stop></linearGradient></defs><path fill="url(#Ao)" d="M654 331h3 0 0l2-1v-2 2h1 1l1 1c1 0 2 0 2 1h1l-1 1c0 3 2 5 2 8s2 4 1 7c0 3 0 5 1 8 1-3 2-6 1-9-1-1-1-1-1-2 1-1 1-2 0-3h0v-9c3 9 6 17 7 26-1 2 0 4 0 7 0 4-1 7 1 11 1 3 0 9-1 12v1c0 1 0 1-1 1v1h0c0 2-1 2-2 4l-1 1v8h-1-2c0 2-1 2-1 4v1 3 2l-1 1h0v1h0c1 1 0 2 0 3s0 1-1 2v2 1c1-2 1-3 2-4 1 0 1 1 0 1v5c0 1 0 1-1 2 0 1 0 2-1 3 0 2 0 6-1 7l-2 9 1 2h0c-1 2-1 3-1 5-1 4-2 10-5 13-1 6-4 12-6 18-1 3-1 5-4 7v1l-1 2-3 6c-4 6-6 13-12 17-1 2-1 3-2 4h0l-2 2-4 5s-2 2-3 2c0 3-2 5-2 7h-1c-2 2-2 3-3 6 0 1 0 0-1 1 0 1-1 2-1 3 0 5-3 9-4 14 0 1 0 2-1 3l-2 6-15 38-23 48v1c-1 1-2 1-3 2-1 2-2 3-2 5-1 1 0 1-1 3h0c-2 1-2 2-2 4-1 0 0 0-1-1h-1v1 1 1h-2v1c0 1 0 1-1 2l-1-1h-2v-1h-1c-2-1-2-1-2-2l-1-1h1 1l-1-1 1-1-1-1c1-2 1-1 2-2l-1-1c-1 1-1 2-1 2l-1 2s-1 1-1 2h0v3h-1l-1-1 1-1v-3c1-1 2-3 2-5 0 0 0-1 1-1l1-1c0-1 1-1 1-2v-2-1-1c1-1 1-1 1-2h1v-1c0-1 0-1 1-2v-2l-1-1v-1c-1-1-1-1-1-2v-1l20-49h-1l7-18 4-8 20-50c1-1 1-2 1-2 0-1-1-1-1-2 0-3-1-5-3-7l-1-2c0-2 0-2 1-3v-1l1-2c1-1 3-2 4-3 0 0 1 0 2-1 1 0 3-1 5-1v-1c1-1 1-3 1-4 1-1 2-2 2-3l3-7-1-2h3v-3c2-2 3-6 3-8l4-7 1-6v-1l1-2c1-1 1-3 1-5 1-2 2-4 3-5v-1c-1-1-1-1-2-3 1-2 0-4 0-6v-1c2-1 3-1 5-1l1-1h0c1-1 2-5 3-7l7-26h1l2-8c1-1 1-2 1-3l4-20 1-2c-1 0-1-1-2-2l1-5v-4c1-6 1-12 1-18v-2-2z"></path><path d="M658 451c0 3-1 4-2 7 0-3 1-5 2-7z" class="Ae"></path><path d="M619 489l2-2 1 1c0 2 0 2-1 4-1-1-2-1-2-2v-1z" class="p"></path><path d="M562 660h1l1-1 2-2 1 1v1c-1 1-2 1-3 2l-2-1z" class="Ai"></path><path d="M660 405c1 0 0 0 1 1 0 3-2 5-3 8h0v-2h-1c1-1 1-1 1-2l1-1c-1-1 0-3 1-4z" class="p"></path><path d="M582 601c1 0 0 2 0 2l-4 7h-1c2-3 3-7 5-10v1z" class="AL"></path><path d="M667 377h1l-1 8-1 1h0c0-1-1-2 0-3v-2c0-2 0-3 1-4z" class="I"></path><path d="M663 436c0-1 1-1 1-2h0 0l-1-1c1-1 1-1 1-2l-1-1h1v-1l1-1 1 1c0 1 0 2-1 3 0 2 0 6-1 7h-1v-3z" class="p"></path><path d="M663 403c-1 1 0 1-1 1l3-11c0-2 0-4 2-5-1 2-1 4-2 7l-2 8z" class="Ab"></path><path d="M595 564c-1 3-4 6-5 10v1h-1l-1-1c1-2 2-5 3-6l2-4h2z" class="AE"></path><path d="M613 546v-2h0c0-1 1-2 2-3v-1c1-1 1-3 2-4v-1l1-1 2-2c0 3-2 5-2 7h-1c-2 2-2 3-3 6 0 1 0 0-1 1z" class="Ag"></path><path d="M561 669l-1-1c0 1-1 1-1 1-1-1-2-1-2-1 0-1 1-1 1-2v-1l1-1c1-2 2-2 3-4l2 1c-1 2-2 3-2 5-1 1 0 1-1 3h0z" class="Ac"></path><path d="M624 477c0-1 1-1 1-2 0-2 0-2 1-3h0v4l-1 2h1c0-1 0-1 1-1l1 2-4 12v-3-3c1-2 2-2 2-4v-1-1h-1c-1 0-1-1-1-2z" class="p"></path><path d="M638 502c0 3-2 5-3 7l-2 5c0 3-2 3-2 5-1 2-1 3-2 4-1-2 1-2 1-3l-2 1h-1l2-2v-2l1-1h1c1-1 1 0 1-1v-1c0-1 1-2 1-3s1-2 2-3h0v-1-1c0-1 2-3 3-4z" class="AZ"></path><path d="M593 564l4-11 1 1 1 1-3 6v1h0c1-1 2-3 2-5 1 0 0 0 1-1l1-1v-1c0-1 1-2 1-3s1-2 2-4h0l-1 2c-1 5-3 9-5 13-1 1-2 1-2 2h-2z" class="I"></path><path d="M635 494v2h1 0c1-1 1-2 2-3s0-3 2-4c0 1-1 1-1 3h0c0 2-1 3-2 4l-3 6c0 1-1 2-1 2l-1 1c-2 0-2 0-2-2v-1l1-1c0-2 1-3 2-4s1-2 2-3z" class="Ag"></path><path d="M632 469h0v1c-2 4-2 8-4 12-1 2-1 3-2 5v1c0 2-1 3-1 4h0c1-1 1-2 2-3v2c-1 1-1 2-1 3l-3 9-1-1c0-1 1-1 1-2h0c1-2 1-1 0-2l-2 4h0-1c2-2 3-8 4-11l4-12 4-10z" class="I"></path><path d="M628 496l1 1 1-1c0 1-1 2-2 4l-1-1v-1c0 2-1 3 0 5-1 2-2 4-4 5-2 2-5 8-6 11l-10 21c-1 3-2 6-4 9h-1l1-2 13-28c1-2 2-5 4-7 0-1 1-1 1-3 0-1 1-3 2-4s2-2 2-3c1-2 1-4 3-6z" class="Ae"></path><path d="M637 475l1 1c-1 2-1 3-1 5 0 3-3 6-3 8l1 1c0 1-2 3-2 4s-1 1-1 2c0 2-1 3-2 5h0v2c0 1-1 1-2 2h0c-1-2 1-1 0-3l1-1v-1h0c1-1 2-3 2-4v-2c-1 0-1 0-1-1h0c-1 2-1 2-2 3 1-1 1-3 1-5 1-1 3-3 3-5 0-1 0 0 1-1v-1c2-3 3-5 3-8l1-1z" class="Ag"></path><path d="M638 476h0c0-2 1-3 1-5h1v-1-1c0-1 0 0 1-1v-2-1c1-1 1-2 1-3l1-1v-1-1c1 0 1-1 1-2h0v-1c0-1 0-1 1-2v1s0 1-1 2c0 3-1 6-2 9-1 2-1 4-2 6s-1 5-1 7l1 1c1-2 1-4 2-5v-2h1l1 1-1 1c-2 3-2 6-4 10v1c0 1-1 2-2 3 0 2 0 3-2 5-1 1-1 2-2 3l-1-1c0-1 1-1 1-2s2-3 2-4l-1-1c0-2 3-5 3-8 0-2 0-3 1-5z" class="Ae"></path><path d="M602 549h1c1 4-1 6-2 9-1 5-4 8-6 12-1 3-1 5-2 8-3 5-6 11-8 17l-3 6v-1l15-38c2-4 4-8 5-13z" class="O"></path><path d="M639 459c2-3 3-7 4-11v5c-1 1-2 3-2 5 0 1 1 1 0 1v3h0v1l-2 2c0 2-1 2-1 3v1 2c-1 1-1 2-1 3v1l-1 1c0 3-1 5-3 8v1c-1 1-1 0-1 1 0 2-2 4-3 5 0 2 0 4-1 5-2 2-2 4-3 6 0 1-1 2-2 3v-2l3-9c1-1 1-2 2-3 1-3 2-5 3-8 1-1 0-3 1-4 0-1 0 0 1-1 0-2 1-4 2-6 0-1 1-3 2-4v-2h0c1-3 1-5 2-7z" class="p"></path><path d="M633 484l-1-2c1-2 2-5 4-6 0 3-1 5-3 8z" class="AL"></path><path d="M614 494v1l1-1c0-1 1-2 2-4v-1l1-1 1 1v1c-1 1-1 2-1 4 0 3-2 6-3 9 0 2-1 2-2 3l1 2h0c-2 3-3 6-6 8 0 2-1 3-2 4v-3h-2c1-2 2-4 2-6 1-1 2-3 2-4 1-2 2-3 2-4 1-3 2-6 4-9z" class="I"></path><path d="M606 511h2c1 2 0 3 0 5s-1 3-2 4v-3h-2c1-2 2-4 2-6z" class="f"></path><path d="M615 480h3v3c-1 1-1 3-2 4 0 2-2 5-2 7-2 3-3 6-4 9 0 1-1 2-2 4 0 1-1 3-2 4 0 2-1 4-2 6v1l-2 1c0-1-1-1-1-2 0-3-1-5-3-7l-1-2c0-2 0-2 1-3v-1l1-2c1-1 3-2 4-3 0 0 1 0 2-1 1 0 3-1 5-1v-1c1-1 1-3 1-4 1-1 2-2 2-3l3-7-1-2z" class="AB"></path><path d="M605 498c1 0 3-1 5-1l-2 7-2-2 1-1-2-3z" class="F"></path><path d="M606 502l2 2c-1 3-3 8-5 11 0-2 0-5 1-7 2-2 0-3 0-5l2-1z" class="S"></path><path d="M599 502c1-1 3-2 4-3 0 0 1 0 2-1l2 3-1 1-2 1c0 2 2 3 0 5-1 2-1 5-1 7l-1 4c0-1-1-1-1-2 0-3-1-5-3-7l-1-2c0-2 0-2 1-3v-1l1-2z" class="AT"></path><path d="M597 508c2 0 3 0 4-1l1 1c-1 1-2 1-4 2l-1-2z" class="F"></path><path d="M599 502c1-1 3-2 4-3 0 0 1 0 2-1l2 3-1 1-2 1h-1c0 1 0 1-1 2-1-1-1 0-1-1h-1c-1 1-1 1-2 1v-1l1-2z" class="E"></path><path d="M604 517h2v3l-15 38c-2 4-4 7-6 11s-3 8-5 13c-2 3-4 6-6 10-2 1-2 4-3 5h-1l7-18 4-8 20-50c1-1 1-2 1-2l2-1v-1z" class="g"></path><defs><linearGradient id="Ap" x1="677.702" y1="361.655" x2="661.106" y2="367.849" xlink:href="#B"><stop offset="0" stop-color="#181b1f"></stop><stop offset="1" stop-color="#2a3439"></stop></linearGradient></defs><path fill="url(#Ap)" d="M668 333c3 9 6 17 7 26-1 2 0 4 0 7 0 4-1 7 1 11 1 3 0 9-1 12v1c0 1 0 1-1 1v1h0l-2-1v-4-1l-1 1c0 1 0 1-1 2-2 0-2-1-2-3 0 1-4 16-4 17h1 0v-1c0-1 0-3 1-4v-1h1v2c-1 1-1 2-1 2 0 1 0 2-1 3l-1 1v5h-1v-2h0v-5l2-8c1-3 1-5 2-7v-3l1-8c2-6 0-14 0-21 1-3 2-6 1-9-1-1-1-1-1-2 1-1 1-2 0-3h0v-9z"></path><defs><linearGradient id="Aq" x1="656.455" y1="477.082" x2="639.977" y2="474.323" xlink:href="#B"><stop offset="0" stop-color="#5b6272"></stop><stop offset="1" stop-color="#717884"></stop></linearGradient></defs><path fill="url(#Aq)" d="M663 436v3h1l-2 9 1 2h0c-1 2-1 3-1 5-1 4-2 10-5 13-1 6-4 12-6 18-1 3-1 5-4 7v1l-1 2-3 6c-4 6-6 13-12 17 0-2 2-2 2-5l2-5c1-2 3-4 3-7 1-1 1-3 2-4 0-2 1-3 2-5l3-7h0c0-1 1-2 1-3 0-3 3-6 4-9l6-16c1-3 2-4 2-7 1-2 2-4 2-7 1-3 2-5 3-8z"></path><path d="M649 486l-1-1v-1c0-1 0-2 1-3 0-1 0-2 2-3l-2 8z" class="AU"></path><path d="M643 502l-1-2c1-1 1-2 1-3v-1c1 1 0 2 0 3l2-3c0-1 1-1 2-2l-1 2-3 6z" class="Ag"></path><path d="M662 448l1 2h0c-1 2-1 3-1 5-1 4-2 10-5 13v-8h1c1-2 3-7 3-10 0-1 0-1 1-2z" class="Ac"></path><path d="M657 460v8c-1 6-4 12-6 18-1 3-1 5-4 7l2-7 2-8c3-5 3-10 5-14 1-1 1-2 1-4z" class="p"></path><defs><linearGradient id="Ar" x1="661.212" y1="412.564" x2="631.496" y2="410.176" xlink:href="#B"><stop offset="0" stop-color="#273036"></stop><stop offset="1" stop-color="#474d58"></stop></linearGradient></defs><path fill="url(#Ar)" d="M654 331h3 0 0l2-1v-2 2h1 1l1 1c1 0 2 0 2 1h1l-1 1c0 3 2 5 2 8s2 4 1 7c0 3 0 5 1 8 0 7 2 15 0 21h-1v-2h0-1-1v2h0l-1-2c0 3 0 6-1 8 0 1 0 2-1 3l-6 22v6 1l-1 1c-1 1 0 0 0 2-1 2-3 3-3 5 0 1-1 2-1 3-1 0-1 1-1 2s-1 1-1 2v1c-1 1-1 1-1 2-1 2-1 4-2 6s-1 3-2 5v2c-1 1-1 1-1 2-1 4-2 8-4 11-1 2-1 4-2 7h0v2c-1 1-2 3-2 4-1 2-2 4-2 6-1 1-1 0-1 1-1 1 0 3-1 4-1 3-2 5-3 8-1 1-1 2-2 3 0-1 0-2 1-3v-2c-1 1-1 2-2 3h0c0-1 1-2 1-4v-1c1-2 1-3 2-5 2-4 2-8 4-12v-1h0l-4 10-1-2c-1 0-1 0-1 1h-1l1-2v-4h0c-1 1-1 1-1 3 0 1-1 1-1 2-1 0-1 0-1-1-1 0-1 1-1 1 0 1-1 2-2 3h0v1c-1 1-1 1-1 2l-1 1v1c0 1 0 2-1 3l-1-1c1-1 1-3 2-4v-3-3c2-2 3-6 3-8l4-7 1-6v-1l1-2c1-1 1-3 1-5 1-2 2-4 3-5v-1c-1-1-1-1-2-3 1-2 0-4 0-6v-1c2-1 3-1 5-1l1-1h0c1-1 2-5 3-7l7-26h1l2-8c1-1 1-2 1-3l4-20 1-2c-1 0-1-1-2-2l1-5v-4c1-6 1-12 1-18v-2-2z"></path><path d="M657 358h1c1 2 0 3 0 5h0 0-1v-5z" class="f"></path><path d="M647 418h2c0 2-1 5-3 7l1-7z" class="I"></path><path d="M653 357v-1h1c0 2 1 7 0 8-1 0-1-1-2-2l1-5z" class="Ac"></path><path d="M648 389c1-1 1-2 1-3l4-20c1 3 1 9 0 11-1 1-1 0-1 2v2l-1 1h0c-1 2-1 5-2 7h-1z" class="AE"></path><path d="M639 459c0-2 0-3 1-5v1c0-1 0-1 1-2v-1l1-2v-1l1-1v-2c-1-3 2-8 3-10l1-4 1-3v-1l1-2v-1c0-1 0-1 1-2 0-2 1-3 1-5l3-3 2-7v6 1l-1 1c-1 1 0 0 0 2-1 2-3 3-3 5 0 1-1 2-1 3-1 0-1 1-1 2s-1 1-1 2v1c-1 1-1 1-1 2-1 2-1 4-2 6s-1 3-2 5v2c-1 1-1 1-1 2-1 4-2 8-4 11z" class="I"></path><path d="M667 348c0 3 0 5 1 8 0 7 2 15 0 21h-1v-2h0-1-1v2h0l-1-2c0 3 0 6-1 8 0 1 0 2-1 3l3-19c0-3 0-6 1-9h0l-1-2 1-1v-1-1c0-2 1-4 1-5z" class="Aa"></path><path d="M648 389h1v2l1-1c0 1 1 1 1 2s-1 2-1 4v2 1 3 3l1 1v1c-1 2-1 4-2 7v4h-2l-1 7c0 2 0 4-1 5l-13 39-4 10-1-2c-1 0-1 0-1 1h-1l1-2v-4h0c-1 1-1 1-1 3 0 1-1 1-1 2-1 0-1 0-1-1-1 0-1 1-1 1 0 1-1 2-2 3h0v1c-1 1-1 1-1 2l-1 1v1c0 1 0 2-1 3l-1-1c1-1 1-3 2-4v-3-3c2-2 3-6 3-8l4-7 1-6v-1l1-2c1-1 1-3 1-5 1-2 2-4 3-5v-1c-1-1-1-1-2-3 1-2 0-4 0-6v-1c2-1 3-1 5-1l1-1h0c1-1 2-5 3-7l7-26h1l2-8z" class="Ac"></path><path d="M645 397h1c0 2-1 4-1 5v1 2 2c1 1 1 2 1 3l-2 5s0 1-1 2c0 1-1 2-1 4-1 2-2 4-2 6-1 1-1 2-2 4-2 2-3 4-3 6l-4 12-1 3v1c-1 1-1 1-1 2l-1-1c0-1 1-3 1-5 1-2 3-4 2-6v-1c-1-1-1-1-2-3 1-2 0-4 0-6v-1c2-1 3-1 5-1l1-1h0c1-1 2-5 3-7l7-26z" class="Ae"></path><path d="M645 402v1 2 2c1 1 1 2 1 3l-2 5s0 1-1 2v-3h1l-1-1v1l-1-1c1-3 2-7 3-10v-1z" class="Ab"></path><path d="M629 432c2-1 3-1 5-1v5h-1c-1 2-1 3-1 5l-1 1c-1-1-1-1-2-3 1-2 0-4 0-6v-1z" class="Q"></path><path d="M642 428c1-4 2-8 4-11l1 1-1 7c0 2 0 4-1 5l-13 39-4 10-1-2c-1 0-1 0-1 1h-1l1-2v-4h0c1-2 1-4 2-6s2-4 3-5c1-2 1-4 2-6 1-3 1-7 3-10 3-5 5-12 6-17z" class="AL"></path><path d="M642 428c1-4 2-8 4-11l1 1-1 7c0 2 0 4-1 5 0-2 1-6 0-7-1 1-2 3-3 5z" class="p"></path><path d="M516 115c1-2 1-4 2-7 0 2 1 5 0 6l1 8 1 1v2l4-1h0l2 2 2 3c1 1 3 2 5 3l-2 1h1c2 0 4 1 6 1v1c-3 0-5 0-8 1l6 2c1-1 3-1 4-1v1l-2 2-1 3h1c0 2 0 2-1 4h1v1l1-1c1-1 2-1 3-1h1 2c2 0 3 1 5 2l1 1c3 2 12 8 13 11v1c1 0 3 0 4 1h0l-1 1v1l2 2c3 0 4 3 7 3 1 2 2 4 3 5v2l2 4c0-1 1-1 2-2 0-1 1-2 2-3l3 1 3 3c1 0 2 1 2 2l1 1c1 1 2 2 3 4 0 1 0 2 1 3v2c-1 1-1 1-2 1s-1 1-2 1c-1-2-2-3-4-4-2 1-2 0-3 0s-2 1-2 1l-2-1-1 1v1c0 2 1 4 1 6h0v1 2c0 2 1 3 1 4l2 2c-1 1-1 1-3 1l-2 1-3-1c-4-1-6-4-9-6l-2 1c-3 4-7 7-11 10l-6 5c8 0 15 0 22 2l9 3c5 1 10 4 15 6 2 1 4 2 5 4h0c-1 1-1 2-1 2h2c3-1 6 3 8 5 3 2 5 5 8 7h0l-2 1v1c0 1 0 1-1 2 1 1 1 1 1 2-1 1-2 1-3 2l-2 2c-1 1-2 1-2 2-1 0-1 0-2-1v2l-1 1 1 1h-1l-1 2v1c0 1 1 1 1 2s-1 2-1 2l1 4 2-4c1 0 1 1 3 1v-1h1l1 3h2 1 1 0l2 2c1 0 2 1 2 2 1 1 1 1 2 1v1l-2 2c0 1 0 2-1 3 1 1 1 2 2 2v1l-2 2c1 1 1 2 1 3 2 0 2 0 4-1h1 1l1 2h3c0 1 1 1 2 2v1h1 1 1 1v1c0 1 0 1-1 2l1 1h1c1 0 1 1 1 2v1l1 1 1-1 2 2h2l2 2v1-1-1h1l3 13c1 1 2 2 3 4v10 2 2c0 6 0 12-1 18v4l-1 5c1 1 1 2 2 2l-1 2-4 20c0 1 0 2-1 3l-2 8h-1l-7 26c-1 2-2 6-3 7h0l-1 1c-2 0-3 0-5 1v1c0 2 1 4 0 6 1 2 1 2 2 3v1c-1 1-2 3-3 5 0 2 0 4-1 5l-1 2v1l-1 6-4 7c0 2-1 6-3 8v3h-3l1 2-3 7c0 1-1 2-2 3 0 1 0 3-1 4v1c-2 0-4 1-5 1-1 1-2 1-2 1-1 1-3 2-4 3l-1 2v1c-1 1-1 1-1 3l1 2c2 2 3 4 3 7 0 1 1 1 1 2 0 0 0 1-1 2l-20 50-4 8-7 18-11 25-4 10c-1 3-3 6-4 9l-2 4-2 1v2s-1 1-2 1c0 1-1 2-1 3l-1 1 2 2-1 3-3 6-1 1c-1-1-2-1-3-2v-2l-1-1h4l-3-3c-1 0-2 0-3-1h0l-3-3-3-3-2 2c0-2-1-3-2-4l-1-1v-2l-8-7c-1-1-1-2-1-2l-2-1-4-7-1-1h-2-1c-1-2-4-3-5-5-1-1-2-2-2-3l2-2-1-1-2-3v-1l-2 1h-1l-2-3-2-5-1-4c-1-1-2-6-3-7v-1l-4-12s1 0 2-1l1 1h2l-5-5h0c-1-1-1-1-2-1l1-2c1 0 2 0 3-1-2-2-3-3-4-5l-1-1 1-1h1c0-2-1-3-1-5v-1c0-1-1-2-1-3l1-3c1-2 2-2 2-4v-1c0-1 0-2 1-3h-1 0c-2-2-3-4-4-6-1 1-2 2-2 3v1l-1 1c0-1-1-1-1-2h-2 0l-1-2h-1l-1 2c-1 0-1 1-2 2v1 1c-1-2-2-5-2-7-1-2-2-3-2-5l-1-2c-2-1-2-4-3-6 0-1 0-1-1-2v-4h0l1-1v-2h0l1-2v-5-2h-1c-1 1-1 0-1 0l-2-2h1l-1-2v-1h-1c-2 0-3 0-5-1l-6-16-2-6-3-7h0l-1-7-1-2v-1c-1-1-2-3-3-5l-5-13 2 2h2c-1-1 0-3 0-4h0c2-1 0-4 2-6 0-2 0-3-2-5l2-2-1-1 1-1-1-1v1l-1-2-1-1v-2-2c-1 0-1-1-1-2s0-1-1-2v-3h-1c-2-1-3-5-3-7-1-1-1-2-1-2-2 0-2 0-3 1l-1 1h0l-2-1v-1h2l-1-1v-1h-2c-1-1-1-1-1-2v-2c1-1 1-2 1-3-2-2-3-3-3-6l1-1h-2c-1-1-1-2-1-3l-1 1h-1l-1-6-2-9-3-1-2-9c1-1 1-2 1-3l-1-7v-1-1l-1-5v-1-1h3v-9-2-2-3s1-3 1-4l1-1c0-1 0-2 1-3 1 0 1-1 1-2l1-1c1-1 2-1 2-2l1 1v-3h2l1-1-2-2c1-1 1-2 1-3v-1h-1l-1-1c1 0 2-1 3-2v-1c1-1 1-2 1-3-1-1-2-3-2-4s1-2 1-2v-1c1-2 0-2 0-4 2 0 2-1 4-2h2v-1c1-2 3-5 6-7l1 1 2 2 2-1-1-1c-1-2 1-2 2-4-1 0-1 0-2-1h-1c0-2-1-4-1-5v-5c1-2 2-4 3-5l3-3v-4h-1l-1-1c2-2 3-2 3-4l2 1v-2c1 1 2 1 3 1l1-1v-3l2-2v-2l-1-6c2-1 4-2 5-3 4-3 8-4 12-6 11-5 24-7 36-8 2 0 1 0 2 1l-6 6c-2 3-1 7 0 10 0 2 1 3 2 5 0-2-1-4-1-6-1-2-1-4-1-6 2-3 5-5 7-7 1-2-1-3-1-5v-2h-1c-2-1-2-2-2-3v-1l2-2 4-3c-1-1-1-2-1-3v-2c0-1 0-2-1-3v-1l1-1-1-1-1 1-1-1c-1 0-1 0-2-1-1-2-1-3-2-4l-1-1-2-4c0 1-2 1-3 1h-1c-1 0-1 0-2 1l-1-3-1 2c-1-2-2-3-4-4l-1-2c-1-1-3-2-4-3l-3-2h0l-3-1h0l1-1c0-1-1-1-1-2-2-1-2-1-3-2l2-1-1-1c0-1 0-2 1-3 0-1 1-1 2-2l7-1h-1 0c0-2 2-3 3-4 5-5 12-10 19-9h2c-1-2-1-2-3-3-1-1-2-1-3-1 3 0 5-3 8-5 1-1 3-2 5-3v-1l3-3c1-2 2-4 4-6 1-2 3-4 4-7z" class="AT"></path><path d="M596 355h1v1l-1-1h0zm-12 184h1c1 0 1 1 1 2h0l-2-2z" class="E"></path><path d="M438 344h1l1 1h-3 0-1l2-1z" class="F"></path><path d="M458 258c2-1 3 0 4 1h0c-2 0-3 0-4-1zm-6 171h0 0l-2 2v-1h0-3c1-1 3-1 5-1zm141-18c2 0 1 0 2 1 0 0-1 0-1 1v1h-1c0-1-1-1 0-3zm-79 153v-3l1-1h0c1 2 0 4-1 6v-2z" class="H"></path><path d="M499 555l1 1h1c0 1 1 2 2 3-1 0-1 0-1-1-1 0-1-1-2-1h0v2l-1-2-1-1 1-1z" class="T"></path><path d="M602 391c-1-2-2-4-2-6h0 0c1 2 3 3 3 6h-1z" class="H"></path><path d="M461 239h1c1 1 1 3 1 4-1 1 0 1-1 1l-1-5z" class="D"></path><path d="M596 290h0l1 2c-1 1-1 2-2 3h-1-1c1-1 1-2 2-2 0-1 0-2 1-3z" class="F"></path><path d="M596 494h-1l-1-3h0c1-1 2-2 3-2l-1 5z" class="H"></path><path d="M442 328c1 0 1 0 2 1 1 0 3 0 4 1l1-1h0c-1 1-2 1-3 2l-3-1s-1-1-2-1l1-1zm56 222l1 1v1c-2 0-2 1-3 2h-2c-1 0-1 0-1-1h1c1-1 2-1 3-2l1-1z" class="E"></path><path d="M478 473h1l-1 3c-1 0-2 1-2 1-1 1-1 1-2 1v-1c1 0 1-1 2-1 1-1 1-2 2-3z" class="D"></path><path d="M466 484l2-1c1 1 2 1 4 3-1 0-2 0-3-1h-3v-1z" class="H"></path><path d="M449 273h2c1 0 1 0 2 1-1 1-3 1-4 1-1 1-1 1-2 1v-1h-1c1-1 2-1 3-2zm93 328h1 1c-2 1-2 2-3 3v2 1l1 1v1l-2 1v-1l1-2h-1v-1c0-2 0-4 2-5z" class="E"></path><path d="M595 426l1-1 1 1h1c0-1 0 0 1-1v1l2 3-1 1-1-1c-2-1-3-2-4-3z" class="AJ"></path><path d="M485 551c0-1 0 0 1-1v1h3l1-1c1 1 0 2 1 4h-3l-3-3h0z" class="E"></path><path d="M436 273v-1c1 0 2-1 3-1h0c1-1 1-1 2-1 1 2 1 3 2 4h-2l-1-1h-2l-1 1-1-1z" class="H"></path><path d="M467 464c1 1 2 2 2 3l-1 1c-1 0-2 0-3 2-1-1-1-3-1-4l1-1h2v-1zm70 150l2 5h0l-2 1h0c-1-1-3-1-4-2l1-1h0l3-3z" class="D"></path><path d="M534 617h2l1 1v2c-1-1-3-1-4-2l1-1z" class="E"></path><path d="M460 227c-1 0-1 0-2-1h0v-1l1-1c2 4 4 7 7 9l1 1h-1c-1 0-2 0-3 1l-1 1v-1c1-1 1-2 1-4l-3-4z" class="Ap"></path><path d="M519 317h1 0c1 1 0 1 1 1 0 2 1 3 0 5l-1 1v-1c-1 0-2-1-3-3h1c1-1 1-2 1-3z" class="AC"></path><path d="M590 287v1h1l3-3v-1c2 1 3 1 4 2l-1 2h-1l-1-2-2 2c-2 0-3 1-5 3v2l-1 1-1-1c0-2 2-3 3-4 0-1 0-1 1-2z" class="D"></path><path d="M596 421c2 2 4 3 7 4 0 0 0 1 1 2-2 0-3 0-5-1v-1c-1 1-1 0-1 1h-1l-1-1-1 1v-1l-1-2h2v-2z" class="AW"></path><path d="M576 236h-2v2h-1v-2h-3l1-1v-1l-3-2c-1 0-2-1-3-1v-1c-1 0-2 0-3-1h1 1 1c2 1 7 4 8 5v1h1c1-1 1-1 2 0v1z" class="E"></path><path d="M435 433c3 0 4 0 6-1l1 3c1 0 3 0 5-1v1s1 0 0 1l1 1-1 1h-1c-1-1-1-2-3-2h-4c-1-1-2-2-4-3z" class="C"></path><path d="M514 564v2c0 2-1 3 0 4v1c-3 2-2 5-4 8-1 1-2 0-3 0 1 0 2-1 2-1 0-1-1-3-1-4 1 0 1 1 2 1l1-2v-2l2-1v-2c0-2 0-2 1-4z" class="E"></path><path d="M554 627h0c1-1 2-2 2-3l-1-1c0-2 0-3-1-5h0v-1l3 2c-1 0-1 1-1 2h0l1 1h2l-4 10-1-1c1-2 0-3 0-4z" class="S"></path><path d="M572 219l9 3 2 3v6h0c-1-1-1-1-1-2v-3c0-1 0-1-1-1v-1c-2-1-3-1-4-1s-2-1-3-1l-1-2-1-1z" class="E"></path><path d="M581 234h2c1 1 1 1 1 2v1l2 2 1-1v1h-1c-1 1-1 2-1 4l-1-1c-1-1 0-1 0-3l-2-1-1 1h-2c0-1 2-2 2-3h-3 0-2v-1h1l4-1z" class="D"></path><path d="M529 605h0v2l3 1 1 5c1 0 1 1 3 1 0-1 0-1 1-1h0v1l-3 3h0-2c-1-2 0-1 0-2s0-1-1-2l-1-3c-1-2-2-3-1-5z" class="S"></path><path d="M467 507c0-1 2-1 2-2l1-1 1 1v2c-1 0-2 1-2 1 0 1 1 2 2 2h1l1-3 2 1v-1l1-1c1 1 1 1 1 2h0c-2 1-2 2-3 3h0c-2 1-3 0-5 0-2-1-2-2-2-4z" class="H"></path><path d="M430 392c2 0 3 0 4 1-1 1-2 3-2 5 0 0 1 0 1 1 1 0 2 1 2 2l-1 1h0c-1 1-2 1-3 2 0-2 0-2-1-4v-5l1-1v-1l-1-1z" class="AV"></path><path d="M514 327l-1-1c-1-1 0-2 0-3 2 0 2-1 3-2h0l1-1c1 2 2 3 3 3v1h0c-1 1-2 1-3 1-1 3-1 6-1 9-1-3-1-6-1-8l-1 1z" class="J"></path><path d="M448 229c4-3 8-4 12-6l-1 1-1 1v1h0c1 1 1 1 2 1h-1-2c-1 0-2 1-3 2h1l-1 1-1 1-1-1h-1l1 3-1 1v-2l-1-1v-1h0l-2-1z" class="B"></path><path d="M581 239l1-1 2 1c0 2-1 2 0 3l1 1 1-1 2-2c0-1 0-1 1-2l2 2v1l-2-2-1 2 1 4h-1l-2 2-1-1h-1l-2-1v-1l1-1v-1-2-1h-2z" class="H"></path><path d="M433 421l-1-1 1-1h1c1 2 2 3 4 3h1v1l-1 1h-2 1l-1 2-2 2-1-1v1l-1-2-1-1v-2l2-2z" class="G"></path><path d="M433 421l1 1c0 1 1 3 0 4l-1 1v1l-1-2-1-1v-2l2-2z" class="S"></path><path d="M436 426l1 1c1 0 1 1 2 2 2 1 1-1 3 1l-1 2c-2 1-3 1-6 1l-1-3-1-1 1-1 2-2z" class="T"></path><path d="M481 545l3-3h1c1 0 1 1 2 1h1l1 2c1 1 5 1 6 3-1 0-2 0-3-1h-1c-1-1-3-2-4-3h-1c-1 1-1 2-1 3h2v1l-1 2c-1 1-1 0-1 1-1 0-2-1-2-1 0-2 0-3-1-5h-1z" class="D"></path><path d="M536 208h0c1 2 2 4 4 5-2 1-4 0-5 2s0 6 0 8h0c-1 0-2 0-3 1l1-1c1-3 0-5 1-7 0-2 0-3-1-5 1-1 1-1 1-2h0c1-1 1-1 2-1z" class="J"></path><path d="M613 376h2c1 1 1 3 2 5 0 1 1 1 2 2l-1 1h0l-1 2-1 1h-2v-1l-2-1v-1l3-2c0-2-1-1-1-3-1 0-1-1-1-2h-1v-1h1z" class="Ah"></path><path d="M617 381c0 1 1 1 2 2l-1 1h0l-1 2-1 1h-2v-1l1-2c0-1 1-2 2-3z" class="e"></path><path d="M615 384l2 2-1 1h-2v-1l1-2z" class="Ad"></path><path d="M596 503l2 1v1c-1 1-1 1-1 3l1 2c2 2 3 4 3 7 0 1 1 1 1 2 0 0 0 1-1 2l-2 2-2-1c0-1 1-1 2-2h1l-1-1v-1h2c-1-2-2-3-3-5s-2-3-3-5l1-2v-3z" class="e"></path><path d="M469 496l1-1v-3h1v1c0 1 1 2 1 2 1 1 1 1 2 1l2-1c0 1 0 1 1 2 0 0 0 1 1 2v4h-1c-2-2 1-5-4-6v1h-3l1 2h-1-1l-1-1c-1-1-2-1-2-2h4l-1-1z" class="S"></path><path d="M432 279c1 2 3 4 5 6h0c4 3 9 3 14 6h0l-5-1c-2-1-5-1-7-1h0c-3 0-7-6-9-9l2-1z" class="Am"></path><path d="M431 404c1-1 2-1 3-2h0l1-1v2h-2v1l1 1h0c2 1 4 0 5 0h2 0v1h-1c-1 2-1 3-3 5-2 0-2 0-3-1s-1-1 0-2v-1c-2 0-2-1-3-2v-1z" class="H"></path><path d="M597 273l1 1 2 1c-2 1-5 4-6 6 0 1 1 1 1 2 1 1 2 1 3 2h1l-1 1c-1-1-2-1-4-2v1l-3 3h-1v-1l3-3-1-1h0c-1-1 0-1 0-2v-1c1-2 2-3 3-4s1-2 2-3z" class="e"></path><path d="M593 504h1c0-1 1-1 2-2v1 3l-1 2h-2c-2 2-6 4-6 7 0 1 1 2 1 3l-1-1c-2-1-8 1-11 1l8-2c3-2 6-9 9-12z" class="AM"></path><path d="M602 392c2-1 4-1 6-1 1 2 3 3 3 5l1 1v1c0 3 0 7-2 10 0 1-1 1-2 2h-1l1-2h0l1-1h0 1v-1-1c-1-1 0-3 0-4-1-1-1-2-1-3l1-1v-1-1l-2-2h-1c-1-1-2 0-3 0 0 1 1 3 0 4-1-1-1-1-1-2-1-2-1-2-1-3z" class="E"></path><path d="M594 423c-2-2-9 3-13 2-2-3-6-6-7-8l8 7c4-1 7-3 10-5 1 0 1 0 2-1l2 3v2h-2z" class="e"></path><path d="M430 360c2 0 3-1 5-2l2 2c-2 2-3 3-4 5l1 2h0c-1-1-3-2-4-3-1 0-1 1-2 0 0-1 0-1-1-1-1-1-3-1-4-1v-3h1v1h1l2 1 2-2 1 1z" class="AJ"></path><path d="M599 285h-1c-1-1-2-1-3-2 0-1-1-1-1-2 1-2 4-5 6-6 1 2 2 5 3 8l-4 2h0z" class="E"></path><path d="M437 319c2 2 3 5 4 8l1 1-1 1c-1-2-2-5-4-7h-2c-2 1-3 4-4 6h-2-1l-1 1v-1-1h1c1-1 1-2 1-3-1 0-1 0-2-1v-2h2 1c1-1 2-1 3-1l4-1z" class="Ad"></path><path d="M605 292l3 2c-1 2-1 3-1 5l-1 1c0 1-1 2-1 3-1 2-1 2-2 3l-2 2h-1c0-1-1-1-1-1h-4l2-2h2c1 0 1-1 2-2 2-3 3-7 4-11z" class="AJ"></path><path d="M525 544v-9c0-2-1-3-1-5-1-5 0-11 2-15v1c-1 3-1 7-1 10 0 2 1 5 2 7 1 3 1 9 1 12-1 0-2-1-3-1z" class="e"></path><path d="M462 476l1 2c1 1 1 2 2 3v2 1c-2 1-1 1-2 2v1c-1 1-2 1-4 1 0-1 0-2-1-3h-1l2-2c-1-1-1-1-1-3 1-1 1-2 2-2l2-2z" class="H"></path><path d="M462 476l1 2c1 1 1 2 2 3l-2 1-1-2-3 3c-1-1-1-1-1-3 1-1 1-2 2-2l2-2z" class="AM"></path><path d="M448 437s1 0 2 1h3 0c-1 2-1 3-2 5 1 1 1 2 1 2h-1l1 2-2 2c-1-1-1-1-3 0v-1-1c0-2 0-2-1-4l-2-2c-1-1-1-1-1-2l1-1h2 1l1-1z" class="E"></path><path d="M447 447h1c1 0 2-1 3-2l1 2-2 2c-1-1-1-1-3 0v-1-1z" class="H"></path><path d="M608 363h-1c0-1-1-2-2-3h1s1 1 2 1 1 1 1 2h1c0-2 2-2 2-3v-4-1-2-1l-2-2c-1 0-1 0-2-1s-2 0-3 0c-1-1-1-1-2-1l-1 1h1v1 1c0 1 0 1-1 1l-1-1c-1-1-1-2-2-3v-1c1-1 1 0 2 0l1-1c1 0 3 0 4 1h0l2-1 1 1 1 1c0 1 1 0 3 0 0 1 0 1 1 1h1c-1 1-1 1-2 1l-1 1c1 1 2 2 2 4 0 0-1 0-1 1v4c0 1-1 1-1 2v1l-1 1h0l-1 1h0c-1-1-2-1-2-2z" class="S"></path><path d="M577 579c-4-1-9-1-12 2-2 2-3 4-5 5-1 2-2 3-4 3-1 1-3 1-4 0h2c4-1 6-6 9-9 2-2 5-3 8-4s5-1 7-3v-7l3 5-4 8z" class="e"></path><path d="M430 405h1c1 1 1 2 3 2v1c-1 1-1 1 0 2s1 1 3 1c2-2 2-3 3-5h1c-1 3-1 4-3 6-1 1-2 1-3 1l-1 2s1 1 1 2l-2 2-1 1 1 1-2 2v-2c-1 0-1-1-1-2s0-1-1-2v-3h-1c2-2 2-2 2-5v-4z" class="Ad"></path><path d="M430 419c0-1 0-1 1-2 0-3 2-3 4-4l-1 2s1 1 1 2l-2 2-1 1 1 1-2 2v-2c-1 0-1-1-1-2z" class="T"></path><path d="M512 308l3 2v1 1c-1 1 0 3 0 5v-1h1v-2l1 1v3c1-1 1-1 2-1 0 1 0 2-1 3h-1l-1 1h0c-1 1-1 2-3 2 0 1-1 2 0 3l1 1 1 1c-1 2-3 3-4 4 0-3-1-7 0-10 0-3 1-5 1-8 1-2 0-4 0-6z" class="e"></path><path d="M512 308l3 2v1 1c-1 1 0 3 0 5v-1h1c0 1 0 1-1 2v3l-1 1-1-1c0-1 0-1 1-2v-3h0c-1 0-1 1-2 1 0 2 1 5-1 6v-1c0-3 1-5 1-8 1-2 0-4 0-6z" class="AI"></path><path d="M429 308c1 0 0 0 2 1h0c2 0 2 0 3 1v2c-1 1-1 0-1 1s0 2-1 3h1l-1 1c0 1 1 1 1 2h4l-4 1c-1 0-2 0-3 1h-1-2c-1-2-1-3-2-5v-1l1-1v-2l1-1 2-3z" class="F"></path><path d="M428 312l3-2h1l-1 2-1 3h-1c0-1-1-2-1-3z" class="E"></path><path d="M431 312l1 1c0 2 0 4-1 5-1 0-2 1-2 2l-1-1c0-1 0-2 1-3l1-1 1-3z" class="S"></path><path d="M426 312l1-1 1 1c0 1 1 2 1 3h1l-1 1c-1 1-1 2-1 3l1 1v1h-2c-1-2-1-3-2-5v-1l1-1v-2z" class="H"></path><path d="M435 358c2-1 3-3 4-5l3 3h1c1-2 2-4 3-5l-1 5c3 0 7-3 9-2 3 2 5 6 8 8 1 1 3 1 4 1-1 1 0 0-2 0-6 0-8-13-16-5-2 2-2 3-3 5 0 0-1-1-1-2-1-1-2-2-4-2-1 0-2 1-3 1l-2-2z" class="AC"></path><path d="M514 327l1-1c0 2 0 5 1 8 0 5 0 11 1 17v1c-3-2-3-7-4-10v-1c0-1-1-2-2-3v-6c1-1 3-2 4-4l-1-1z" class="a"></path><path d="M525 544c1 0 2 1 3 1 0 3 0 7-1 10 0 3-1 5-2 8-2 1-4 2-5 4l-1 1c0-3 1-7 2-9 1-3 3-5 3-7 1-3 1-6 1-8z" class="Ad"></path><path d="M598 483c-1 0-1 0-2-1l2-1s0-1 1-1h1v-1c-1-1-1-2-2-4v-2h-1c1-1 2-2 2-4 0-1-1-2-2-2l1-1 2 2h1l1 1v1h-1c-1 2-1 3-1 4l2 2c2 0 2 0 3-1v-3-1h-1c-1-1-1-1-1-2 0 0 1 0 2-1l1-1c0 1 0 2-2 3h1l1 1c1 1 1 3 0 4v1l-1 3-1 4v1c-2 0-3 0-4 1h0l-2-2z" class="E"></path><path d="M598 483c0-1 1-1 2-2l1 1s1 0 2 1h0l1-1c-1-1-1-2 0-3v-2h0l2-1-1 3-1 4v1c-2 0-3 0-4 1h0l-2-2z" class="S"></path><path d="M599 426c2 1 3 1 5 1 0 1 1 2 1 3h0v3c1 1 2 1 3 1l-2 4-1 1c-2 0-3 1-3 3-1 2 0 5 0 7 1 0 1 1 2 1v1l-1 1-2-2c0-1 0-2-1-3h-1v-1c0-3 0-5 1-8 1-1 0-6-1-7l1-1 1-1-2-3z" class="AM"></path><path d="M601 429h1 2l1 1-1 3-3-3v1h-2l1-1 1-1z" class="w"></path><path d="M599 426c2 1 3 1 5 1 0 1 1 2 1 3h0l-1-1h-2-1l-2-3z" class="J"></path><path d="M605 430h0v3c1 1 2 1 3 1l-2 4c-1-1-1-2-2-3h-1c1-1 1-1 1-2h0l1-3h0z" class="v"></path><path d="M518 297h0l1-1 1-1 1-1v8l-1 7 1 9c-1 0 0 0-1-1h0-1c-1 0-1 0-2 1v-3l-1-1v-11h-1l2-3v-3h0 1z" class="a"></path><path d="M518 297h0l1-1 1-1 1-1v8l-1 7v-1-4l-1-1h0v-3h0c-1 0-2 0-2 2v1c-1 3 1 8 0 12l-1-1v-11h-1l2-3v-3h0 1z" class="AJ"></path><path d="M425 380h3 1l1 1-1 2h1s1 0 1 1l1 1c-1 1-3 0-4 1 1 1 1 1 3 1v2c-1 1 0 1 0 3l1-1h1c1-2 2-1 4-2-1 0-1-1-2-1h1 1l1-1v-1-1c-1-1-2-2-4-3h2v-3h-1c-1 1-1 1-2 0 0 0 0-1 1-2s2-1 3-1v1h0v4 1c1 1 1 1 2 3 1 1 2 1 3 2s2 1 3 2l-1 1v-1c-2 0-3-1-4-1s-2 1-3 2c0 1-1 2 0 3v2l-1 1v1c-1-1-1-2-1-3s-1-1-1-1c-1-1-2-1-4-1l1 1c-2 0-2-1-3-1-1-3-2-5-4-8 1 0 1-1 2-1v-1l1-1c0-1-1-1-2-1z" class="S"></path><path d="M424 384c1 0 1-1 2-1v-1l1-1 1 1-1 2h0 1 0l-1 1v1l2 2h1v3 1h0l1 1c-2 0-2-1-3-1-1-3-2-5-4-8z" class="G"></path><path d="M426 391l2 1c1 0 1 1 3 1v1l-1 1v5c1 2 1 2 1 4v1h-1v4c0 3 0 3-2 5-2-1-3-5-3-7-1-1-1-2-1-2v-2-3c1-1 1-2 2-3h-1c1-1 1-2 2-3 0-1 0-2-1-3z" class="Af"></path><path d="M424 403c1-1 2-3 3-3h1l-1-1 1-1c1 1 1 1 1 2v1c0 1 0 2 1 3-1 0-2-1-2-1h-2l-1 1-1 1v-2z" class="C"></path><path d="M430 400c1 2 1 2 1 4v1h-1v4c0 3 0 3-2 5-2-1-3-5-3-7-1-1-1-2-1-2l1-1 1-1h2s1 1 2 1c-1-1-1-2-1-3v-1h1z" class="Ao"></path><path d="M430 409l-3-1-1-2 1-1h3v4z" class="E"></path><path d="M508 223l2 2v1 5h0l-1 1v2h1c1 0 1 1 1 2l2 5c-1 2-2 3-2 4 1 3 1 6 1 9v3 2l-1 1c-1 1 0 2-1 3 0-6 1-12 0-17-1-3-3-6-4-8v-5-8l2-2h0z" class="Ad"></path><path d="M508 223l2 2v1 5h0l-1 1v2l-1 3h-1c0-1 0-2-1-3v-1-8l2-2h0z" class="AM"></path><path d="M508 223l2 2v1 5h0l-1 1c-1-3-2-6-1-9h0z" class="AI"></path><path d="M598 313c-1-1-2-2-3-2-2-3-5-1-8-1-1-1-2-2-3-2-2 0-3 2-4 4h-1 0c0-1 1-2 1-3-2 1-5 2-7 2-1 0-2-1-2-1h1c5 0 7-3 11-4 1 0 3 1 3 1 2 0 4 0 5-1 2 1 3 1 4 1h4s1 0 1 1v2l3 1 1 1v1l-1 1 1 1c0 2-1 2 1 3l-1 1-2 1v3l-1-2h-1l1-1h0c0-3-2-6-3-7z" class="a"></path><path d="M600 310l3 1 1 1v1l-1 1v-1l-3-3z" class="AV"></path><path d="M598 313v-1l-1-2 1-1c1 1 1 1 1 3h1c1 0 1 1 1 1l1 1 1-1v1l1 1c0 2-1 2 1 3l-1 1-2 1v3l-1-2h-1l1-1h0c0-3-2-6-3-7z" class="AJ"></path><path d="M431 295h2c1 2 1 2 1 4v1l1 1h0l1-1v4c1 2 3 4 4 6l-1 1-1-1v-1h-1l-1-1h-1-3c-1 0-1 0-1 1h0c-2-1-1-1-2-1h-1c-3-2-3-5-3-7 2-3 3-4 6-6z" class="D"></path><path d="M427 328v1l1-1h1 2l1 1c1 1 2 2 2 3v2 2 1l-1-1s-1-1-1-2l-1-1c0-1 0-1-1 0-1 2 0 2-1 4 0 1-2 1-2 2-1 1-1 0-1 1 1 1 1 2 1 3s1 2 1 2l2-1c0-1 0-2 1-2 0-1 1-2 1-2l-1-1-1-1h0v-1l3 3v1c-1 0-2 0-2 1-1 2 1 4-1 6l1 1c-1 0-1 0-2 1l-1 1c1 0 1 1 2 1h1c1 1 1 2 0 4h0c-1 1-1 2-1 4l-1-1s-1 0-1-1v-2c-1-3-1-5-2-7h1c0-1 1-2 1-3-2-1-3-3-4-5 0-1 0-2 1-3 0-1 1-2 1-3h0l-1-2c-1-1 0-3 1-5h1z" class="AV"></path><path d="M428 332v5c-1 0-2 1-3 1 0-1 1-2 1-3l2-3z" class="Af"></path><path d="M427 328v1l1-1h1 2l1 1c-2 1-3 2-4 3l-2 3h0l-1-2c-1-1 0-3 1-5h1z" class="AC"></path><path d="M432 443c2-1 0-4 2-6 0-2 0-3-2-5l2-2 1 3c2 1 3 2 4 3h4c2 0 2 1 3 2h-2l-1 1c0 1 0 1 1 2l2 2c1 2 1 2 1 4v1 1l-1 1c0 1 1 1 0 2h-1c0-1-1-2-2-3h0c-3-2-3-2-3-5 0-1-1-1-1-2s-1-2-1-3l-1 1v3h-1v1h-3l-1-1z" class="AJ"></path><path d="M443 436c2 0 2 1 3 2h-2l-1 1c0 1 0 1 1 2l2 2c1 2 1 2 1 4v1c-2 0-3-1-4-2-2-2-3-6-2-8l2-1v-1z" class="C"></path><path d="M443 436c2 0 2 1 3 2h-2l-1 1c0 1 0 1 1 2l2 2-2 2-1-1v-1l1-1-2-2h0c1-1 1-2 1-3v-1z" class="T"></path><path d="M599 447h1c1 1 1 2 1 3l2 2 1-1v-1c-1 0-1-1-2-1 0-2-1-5 0-7 0-2 1-3 3-3l-3 5 3 6c1 4 1 9 6 11h0c-1 1-1 1-1 2v1 2 1 2l2 2-1 1-1-1h-2-1-1l-1-1h-1c2-1 2-2 2-3v-1l-1-1c-2-2-3-5-4-7 0-2 0-2 1-3-1-3-2-6-3-8z" class="AC"></path><path d="M601 458c2 1 3 3 5 4 1 1 1 2 2 4 0 1 0 3-2 4v1l-1-1h-1c2-1 2-2 2-3v-1l-1-1c-2-2-3-5-4-7z" class="D"></path><path d="M531 208c1 1 2 2 2 3 1 2 1 3 1 5-1 2 0 4-1 7l-1 1c-1 1-2 1-3 2h-1c-1 1 0 1-1 1-2 0-3-1-4-2-1-4 0-6 1-9l3-5c1-1 2-2 4-3z" class="E"></path><path d="M614 354c2-2 3-2 4-2 2-1 3-1 5 0h0l-1 3c-2 1-2 2-3 4 0 2 0 2 1 3l-2 2h-1v2c1 2 0 3 0 6-1 2-2 2-4 4h0-1v1c-1 0-1 1-2 1l-1 1h-1 0 0c0-1 0-2 1-2l1-1c1-1 1-1 2-1l1-1v-2h0c-1-1-1-2-1-3h0l-2-2c-1 0-1 1-2 0l1-1c-1-1-1-2-1-3 0 1 1 1 2 2h0l1-1h0l1-1v-1c0-1 1-1 1-2v-4h1v-2z" class="H"></path><path d="M614 354c2-2 3-2 4-2 2-1 3-1 5 0h-1l-2 2-1 2h-1c0-1 0-1-1-2h-3z" class="Ah"></path><path d="M423 362c1 0 3 0 4 1 1 0 1 0 1 1 1 1 1 0 2 0 1 1 3 2 4 3h0c1 1 2 1 2 2v1c-1 0-1 0-2-1s-1-1-2-1h-1-2v1h-2 0c1 1 1 2 1 3l-3 2h-2v-2l-1 2v1l1 2c1 0 1 1 1 1v1l-1 1v1s1 1 2 1h0v-2c1 0 2 0 2 1l-1 1v1c-1 0-1 1-2 1l-1-1h-2 0-1-1c-1-1-1-3-1-4l1-1v-1l-2-1-1 1h-1l-1-1c1-2 1-3 2-5l1-2 2 2 1 1v-1h1l3-3c0-3-2-2-3-3l-1-1 3-2z" class="H"></path><path d="M416 371l1-2 2 2 1 1c-1 2-3 3-3 4l-1 1h-1l-1-1c1-2 1-3 2-5z" class="w"></path><path d="M611 412c1 0 2 0 2 1h1c1 1 1 1 1 3 1 1 1 2 2 3h0c1 1 2 1 2 2-1 1-2 1-2 2v2h0l-2 4c-1 1-2 1-3 2-2 0-3 1-4 3-1 0-2 0-3-1v-3h0c0-1-1-2-1-3-1-1-1-2-1-2 0-1 0-1 1-1 2-1 4-2 6-4 0-1 1-2 0-3 0-2 0-3 1-4v-1z" class="e"></path><path d="M614 413c1 1 1 1 1 3h-1c0-1 0-1-1-1s0 0-1-1l1-1h1z" class="AI"></path><path d="M615 416c1 1 1 2 2 3h0c-1 0-2 0-3 1l-1-1c1-1 1-1 1-2v-1h1z" class="AJ"></path><path d="M614 420c1-1 2-1 3-1 1 1 2 1 2 2-1 1-2 1-2 2v2h0c-1 0-1-1-2-2h0c-1 0-1 0-2 1l-1-2 1-1c1 0 0 0 1 1h1 0l1-1-2-1z" class="J"></path><path d="M615 423c1 1 1 2 2 2l-2 4c-1 1-2 1-3 2-1-2-1-4-1-5l1-1c0 1 1 2 1 3h1v-1c0-2 0-2 1-4z" class="AI"></path><path d="M611 426c0 1 0 3 1 5-2 0-3 1-4 3-1 0-2 0-3-1v-3l1 1c1 0 2-1 3-2 0-2 0-2 2-3h0z" class="AC"></path><path d="M546 616c1 0 3 1 3 2v1h1c0 1 0 1 1 2l-1 1h1 1v1 1h-1c-1 0-1 1-2 1l-1 1c0 1 0 1-1 1s-2 0-3 1c0-2-1-3-1-5-2 1-2 2-3 4l-2 2h0c-1 1-2 1-3 1l-2-2c-1-1-1-2-3-3 1-1 1 0 1-1l1-1v-1-3l1-1c1 1 3 1 4 2h0l2-1h0c3-2 5-1 7-3z" class="S"></path><path d="M539 619c3-2 5-1 7-3 0 2-1 2-2 3h-2c-2 0-1 1-3 0h0z" class="T"></path><path d="M541 623l3-2c0 1 1 3 1 5 1 0 2-1 3-1v1c0 1 0 1-1 1s-2 0-3 1c0-2-1-3-1-5h-2z" class="E"></path><path d="M533 618c1 1 3 1 4 2h0l1 6h1s1 0 1-2c0 0 0-1 1-1h2c-2 1-2 2-3 4l-2 2h0c-1 1-2 1-3 1l-2-2c-1-1-1-2-3-3 1-1 1 0 1-1l1-1v-1-3l1-1z" class="G"></path><path d="M532 622h2 0l-1 1c0 2 2 4 4 5l1 1c-1 1-2 1-3 1l-2-2c-1-1-1-2-3-3 1-1 1 0 1-1l1-1v-1z" class="AM"></path><path d="M452 445c3 3 5 4 7 6 1 1 2 1 3 1-1 2-1 4-3 5v-4h-1c-1 1-1 1-1 3l-2 2c2 1 2 2 4 2v1c0 1-1 2-2 3l-2-1c-2-1-2-1-3 0-1-1-1-1-1-2h-1l-1-1h0c-2 0-3-1-5-2l-1-3v-2l2-1h1c1-1 0-1 0-2l1-1c2-1 2-1 3 0l2-2-1-2h1z" class="AW"></path><path d="M450 459c1 1 2 2 4 2h1c1 0 2 0 4-1v1c0 1-1 2-2 3l-2-1c-2-1-2-1-3 0-1-1-1-1-1-2h-1l-1-1 1-1z" class="a"></path><path d="M447 449c2-1 2-1 3 0s1 4 1 5l-5-2c1-1 0-1 0-2l1-1z" class="Ad"></path><path d="M457 456h-1-1c0-2-1-3-2-5 0 0 0-2-1-3l1-1 1 1-1 2 1 1h1l1-1v1c1 0 1 0 2 1h1v-1c1 1 2 1 3 1-1 2-1 4-3 5v-4h-1c-1 1-1 1-1 3z" class="Ap"></path><path d="M445 452h1l5 2v2s1 1 0 2l-1 1-1 1h0c-2 0-3-1-5-2l-1-3v-2l2-1z" class="Ah"></path><path d="M445 452h1l5 2v2s-1 0-1 1c-1-1-2-1-2-2l-1-1c-1 1-1 1-1 2-1 0-2-1-3-1v-2l2-1z" class="T"></path><path d="M467 460l1-2c2-2 6-3 8-4h2c1 0 1-2 2-2 1-3 2-8 4-10h1v1c-1 2-2 4-2 5-1 2-2 4-3 5h1v2c-1 1-2 1-3 1l-2-1v1c-3 2-8 3-9 8h0v1h-2l-1 1c0 1 0 3 1 4v1c1 0 1 0 2 1h-2c-1 2-1 4-2 6l-1-2h0c1-3 1-5 0-8-1-2-3-3-5-4 1-1 2-2 2-3v-1c-2 0-2-1-4-2l2-2c0-2 0-2 1-3h1v4c2-1 2-3 3-5h1v-1h1v1 1l1 1v2c2 1 2-1 2 2v2z" class="e"></path><path d="M462 452h1v-1h1v1 1l1 1v2c2 1 2-1 2 2v2c-1 0-2 0-2 1-1 1-1 1-2 0v-3l2-1v-1l-3 2c0 2 0 2-1 4-1 0-1 0-2-1v-1c-2 0-2-1-4-2l2-2c0-2 0-2 1-3h1v4c2-1 2-3 3-5z" class="T"></path><path d="M457 456c0-2 0-2 1-3h1v4l-2 2h1c1 0 1 0 2-1v-1h1l1 1c0 2 0 2-1 4-1 0-1 0-2-1v-1c-2 0-2-1-4-2l2-2z" class="H"></path><path d="M448 229l2 1h0v1l1 1v2l1-1v1c1 1 1 2 2 3-1 0-2 1-2 2v1 1h-1c0 2-2 4-3 5s-1 1-1 3v2 1 1h0l1 1v1c-1 0-2 1-2 2l2 2h-1l-1 1v-1l-1-1c-1-1-2-2-2-4 0-1-1-1-1-2l-1-6 1-1v-3l2-2v-2l-1-6c2-1 4-2 5-3z" class="T"></path><path d="M448 229l2 1h0v1l1 1v2h-1-1c0 1-1 1-1 2l-1 1v1c-1 0-2 2-3 2v-2l-1-6c2-1 4-2 5-3z" class="Ah"></path><path d="M436 443h1v-3l1-1c0 1 1 2 1 3s1 1 1 2c0 3 0 3 3 5h0c1 1 2 2 2 3l-2 1v2l1 3v1h-1l-1 1c0 1 0 1-1 2l2 2-1 1c0-1-1-1-1-2h-1l1 2h-2-1l-2-1v-1c-1-1-2-3-3-5l-5-13 2 2h2c-1-1 0-3 0-4h0l1 1h3v-1z" class="X"></path><path d="M433 449c0 1 1 2 1 3l-1 1h-1l-1-3 2-1z" class="AB"></path><path d="M437 461l2 4h-1l-2-1v-1s1-1 1-2z" class="a"></path><path d="M428 445l2 2h2l1 2-2 1 1 3c2 3 3 6 5 8 0 1-1 2-1 2-1-1-2-3-3-5l-5-13z" class="Q"></path><path d="M430 447h2l1 2-2 1c0-1-1-2-1-3z" class="x"></path><path d="M436 443h1v-3l1-1c0 1 1 2 1 3s1 1 1 2c0 3 0 3 3 5h-1-2v1l-1 1v3c0-1-1-1-2-2-1-2-2-2-2-5v-1c1 1 1 2 1 3h1l-1-2h0v-4z" class="e"></path><path d="M443 449h0c1 1 2 2 2 3l-2 1v2l1 3v1h-1l-1 1c0 1 0 1-1 2-1-2-3-4-4-7h1l1 1h1l-1-2v-3l1-1v-1h2 1z" class="w"></path><path d="M439 454v-3l1-1c1 1 1 2 1 4 0 1 1 1 1 2h-2l-1-2z" class="J"></path><path d="M443 449h0c1 1 2 2 2 3l-2 1-2 1c0-2 0-3-1-4v-1h2 1z" class="a"></path><path d="M603 391c3-1 4-2 6-3l1-1v2h2 0l3 3 1-1 2 2 3 1s1 0 2-1c1 2 1 2 1 3h1v-3c1 3 0 4 0 7h0c1 1 1 0 2 0 0 1-1 2-2 3-2 0-4 0-5 2l-1-1c-3 2-4 6-4 8l-1 1h-1c0-1-1-1-2-1v-2h-3c1-1 2-1 2-2 2-3 2-7 2-10v-1l-1-1c0-2-2-3-3-5-2 0-4 0-6 1h-3l-1-1h4 1z" class="w"></path><path d="M614 404c1 0 1 1 1 1v1h-2c0-1 1-1 1-2z" class="v"></path><path d="M616 391l2 2 3 1h0c-1 1-1 1-1 2v3h0l-3 2c-1 1-1 2-2 2v-6c-1-2-1-2 0-4v-1l1-1z" class="An"></path><path d="M617 394c1 2 2 3 3 5l-3 2h-1c0-2 0-5 1-7z" class="C"></path><path d="M616 391l2 2 3 1h0c-1 1-1 1-1 2v3h0c-1-2-2-3-3-5l-2-1v-1l1-1z" class="J"></path><path d="M621 394s1 0 2-1c1 2 1 2 1 3h1v-3c1 3 0 4 0 7h0c1 1 1 0 2 0 0 1-1 2-2 3-2 0-4 0-5 2l-1-1h0l2-4-1-1v-3c0-1 0-1 1-2h0z" class="Aq"></path><path d="M621 394l1 1v5h-1l-1-1v-3c0-1 0-1 1-2zm-19-71v-3l2-1 6 3 3 1v5h2c-1 1-2 3-1 4 0 1 2 3 3 4v1c-1 1-1 1-1 2h1 1v1h0l-1 1h-1l-1 1c-3-2-5-4-8-6l-6 6h-2c-1 2-2 2-2 4-1 1-2 1-3 2 0-1 1-2 1-3 0-2 4-3 5-4s2-3 3-4c1-2 0-3 0-4l-1-2c0-1 1-1 1-2l-1-1v-5z" class="e"></path><path d="M610 329l1-1c1 1 1 1 2 1-1 1-1 2-3 3h-3v-2h3v-1z" class="T"></path><path d="M602 323v-3l2-1 6 3 3 1v5 1c-1 0-1 0-2-1l-1 1v1h-3v2c-1 1-2 0-3 0h0l-1 1-1-2c0-1 1-1 1-2l-1-1v-5z" class="E"></path><path d="M604 332v-2c1-2 1-3 1-4v-1h1c2 0 3 3 4 4v1h-3v2c-1 1-2 0-3 0h0z" class="AT"></path><path d="M604 332c1 0 1-1 2-2h1v2c-1 1-2 0-3 0z" class="B"></path><path d="M421 284c1-2 3-5 6-7l1 1 2 2c2 3 6 9 9 9h0l-1 2v2l-2 1c0 2 0 3 2 4 1 0 2 0 3-1v1c0 1-1 1-2 2-1-1-2-1-3-2v2l-1 1h0l-1-1v-1c0-2 0-2-1-4h-2c-2 0-3 1-5 1-2-1-2-1-4-3l-1-2v-6-1z" class="T"></path><path d="M426 290h2l-1 2-1-1v1-2z" class="AV"></path><path d="M421 284c1-2 3-5 6-7l1 1h-2v1c0 2-1 2-2 4 0 1 0 1 1 2v1l1-1h1l1-1c1 1 1 1 1 3h0v1c0 1 0 2-1 2h-2v2c-1 1-1 1-1 2s0 1 1 2c-2-1-2-1-4-3l-1-2v-6-1z" class="C"></path><path d="M427 285l1-1v1h-1z" class="B"></path><path d="M421 284c1 1 2 1 3 2 0 1 0 1-1 1h1c1 1 1 2 2 3v2c-1 1-1 1-1 2s0 1 1 2c-2-1-2-1-4-3l-1-2v-6-1z" class="Af"></path><path d="M605 479l4-1c1 2 1 4 1 6l-2 3c-1-1-2-1-2-2l-1 1 2 2c1 0 2 1 2 2l2-1h0v3c0 1 0 3-1 4v1c-2 0-4 1-5 1-1 1-2 1-2 1-1 1-3 2-4 3l-1 2-2-1v-1c-1 1-2 1-2 2h-1v-4c-1-2-3-3-5-4l6 3c1-1 1-3 2-5l1-5 3-4h0c1-1 2-1 4-1v-1l1-4z" class="J"></path><path d="M593 500c1 1 3 1 3 2-1 1-2 1-2 2h-1v-4z" class="e"></path><path d="M601 486c1-1 1-1 3-1v-1l2 1-1 1 2 2-2-1h-1-1l-2-1z" class="AC"></path><path d="M601 486l2 1c1 2 1 2 1 4 0 1 0 1-1 2 0 3-2 4-4 6l-1-1c-1-1-1-3-1-4 1-3 2-6 4-8z" class="H"></path><path d="M603 493c-1 0-1 0-2-1v-3h1l1 2h1c0 1 0 1-1 2z" class="S"></path><path d="M603 487h1 1l2 1c1 0 2 1 2 2l2-1h0v3c0 1 0 3-1 4v1c-2 0-4 1-5 1-1 1-2 1-2 1-1 1-3 2-4 3v-3c2-2 4-3 4-6 1-1 1-1 1-2 0-2 0-2-1-4z" class="Ad"></path><path d="M609 490l2-1h0v3c0 1 0 3-1 4l-2-2 1-1v-3z" class="w"></path><path d="M511 338c1 1 2 2 2 3v1c1 3 1 8 4 10v-1l1 5c1 4 0 7 0 10l-1 17c0 3 2 9 1 12-1 1-1 1-3 2-3-3-2-8-2-12s-1-8 0-12c0-5 1-9 0-13-1-8-3-14-2-22z" class="AC"></path><path d="M524 603c1-1 1-1 3-1v1h2v2c-1 2 0 3 1 5l1 3c1 1 1 1 1 2s-1 0 0 2h2l-1 1-1 1v3 1l-1 1c0 1 0 0-1 1v1l-1-1 1-1-3-3c-1-1-3-2-3-3-1-2 1 1-1-2l-1 1h-1l-1-1v2l-1-1c0-1-2-3-3-4h-1v-1l1-1-3-2c0-1-1-1-1-2-1 0-1-1-2-2l1-1 1-1h1l3 2v-2l1 1h2c1-1 1-1 3 0l2-1z" class="e"></path><path d="M527 615c1 1 1 1 2 3v1h1 1 1v3 1l-1 1c0 1 0 0-1 1v1l-1-1 1-1-3-3h1v-1l-2-3 1-2z" class="AW"></path><path d="M518 607c1 1 1 1 2 1l1 1 2 1 1-1c1 0 1 1 2 2l-3 3v-1c0-1-1-1-1-2l-1 1c0 1 1 2 2 2v2c1-1 1 0 2-1v1h1l1-1-1 2 2 3v1h-1c-1-1-3-2-3-3-1-2 1 1-1-2l-1 1h-1l-1-1-1-2h1 0 1c-1-1-2-2-3-2v-1l-2-1 2-2v-1z" class="w"></path><path d="M518 612v-1l-2-1 2-2c0 1 1 1 2 2h1 0v1h-1c-1 0-1 0-2 1z" class="Q"></path><path d="M524 603c1-1 1-1 3-1v1h2v2c-1 2 0 3 1 5l1 3-2-1c-1 1-1 1-2 1l-1-2c-1-1-1-2-2-2l-1 1-2-1v-2h0c1 0 1-1 2-1l1-2v-1z" class="J"></path><path d="M526 610l2-2v1c1 1 1 1 2 1l1 3-2-1c-1-1-2-2-3-2z" class="AM"></path><path d="M524 604c0 2 1 4 2 6 1 0 2 1 3 2-1 1-1 1-2 1l-1-2c-1-1-1-2-2-2l-1 1-2-1v-2h0c1 0 1-1 2-1l1-2z" class="Am"></path><path d="M513 603l3 2v-2l1 1h2c1-1 1-1 3 0l2-1v1l-1 2c-1 0-1 1-2 1h0v2l-1-1c-1 0-1 0-2-1v1l-2 2 2 1v1c1 0 2 1 3 2h-1 0-1l1 2v2l-1-1c0-1-2-3-3-4h-1v-1l1-1-3-2c0-1-1-1-1-2-1 0-1-1-2-2l1-1 1-1h1z" class="v"></path><path d="M518 607c0-1 0-1 1-2 1 2 1 0 2 0s1 1 2 1c-1 0-1 1-2 1h0v2l-1-1c-1 0-1 0-2-1z" class="e"></path><path d="M513 603l3 2v1l-1 2-1-1c0-1-1-1-2 0-1 0-1-1-2-2l1-1 1-1h1z" class="Z"></path><path d="M415 287c2 0 2-1 4-2h2v6l1 2c2 2 2 2 4 3 2 0 3-1 5-1-3 2-4 3-6 6 0 2 0 5 3 7h1l-2 3-1 1c-1 0-3-1-4-2h0c-1 0-1 0-2-1v1l2 2-1 1-1-1h-3 0c-1-1-1-2-1-3 0 1 0 1-1 2l-2-2c1-1 1-2 1-3v-1h-1l-1-1c1 0 2-1 3-2v-1c1-1 1-2 1-3-1-1-2-3-2-4s1-2 1-2v-1c1-2 0-2 0-4z" class="AI"></path><path d="M418 302l1 1v3c-1-1-1-3-1-4z" class="Q"></path><path d="M418 302v-2l1-1c1 2 1 3 2 4h-2l-1-1z" class="a"></path><path d="M421 291h0l1 2-2 2-1 1v-3l2-2z" class="v"></path><path d="M415 287c2 0 2-1 4-2h2v6h0l-2-1-2 2-1-1v1h-1v-1c1-2 0-2 0-4z" class="Q"></path><path d="M421 303c1 2 1 3 1 6v1h0c-1 0-1 0-2-1v1l2 2-1 1-1-1h-3 0v-1c1-1 1-2 1-2 1-1 1-1 1-3v-3h2z" class="v"></path><path d="M415 292h1c0 1 0 2 1 4 0 2 0 2-1 3 0 1 1 2 1 3l-1 1v1c-1 1 0 3 0 5h0c0 1 0 1-1 2l-2-2c1-1 1-2 1-3v-1h-1l-1-1c1 0 2-1 3-2v-1c1-1 1-2 1-3-1-1-2-3-2-4s1-2 1-2z" class="m"></path><path d="M422 293c2 2 2 2 4 3 2 0 3-1 5-1-3 2-4 3-6 6 0 2 0 5 3 7h1l-2 3-1 1c-1 0-3-1-4-2v-1c0-3 0-4-1-6-1-1-1-2-2-4v-1l1-1c0 1 0 1 1 1l-1-3 2-2zm100 287l2 1v4c1 0 1 0 2-1s1-2 2-2h2 1 0 1c0 2-2 8-3 9-1 3 0 6 1 9l2 2-3 3h0v-2h-2v-1c-2 0-2 0-3 1l-2 1c-2-1-2-1-3 0-1-2-1-1 0-2h1c-2-2-3-4-4-6v-1c-1-1-1-2-1-3 1-1 1-2 3-3 1-1 1-2 2-3 0-1 0-2 1-2v-1h-1v-1l2-2z" class="AM"></path><path d="M521 599l1 2h1 0l1-1h0l1 1v-2h-1c1-1 1-1 1-2h1l1 1-1 2c1 1 1 1 2 1l1 1v1h-2v-1c-2 0-2 0-3 1l-2 1c-2-1-2-1-3 0-1-2-1-1 0-2h1l1-3z" class="AJ"></path><path d="M515 592c1-1 1-2 3-3l2 2c2 3 2 5 1 8l-1 3c-2-2-3-4-4-6v-1c-1-1-1-2-1-3z" class="Ah"></path><path d="M515 592c1-1 1-2 3-3l2 2-1 2v1 2c-2 0-2 0-3-1s-1-2-1-3z" class="C"></path><path d="M524 585c1 0 1 0 2-1s1-2 2-2h2 1 0 1c0 2-2 8-3 9h-3v2h-1l-1-2v-3h0c-1-1-1-1-1-2l1-1z" class="E"></path><path d="M531 582h1c0 2-2 8-3 9h-3c0-1 0-1-1-2h0v-1c1 1 1 1 2 1l3-3c1-2 1-2 1-4z" class="Am"></path><path d="M627 400h2 0l1 1c-1 1-1 3-2 4h1l-1 2h1 0 1l2-3v6c-1 1-2 4-2 6h0v2h0v1c-2 0-2 0-4 1v1c-3 1-6 2-9 4v-2c0-1 1-1 2-2 0-1-1-1-2-2h0c-1-1-1-2-2-3 0-2 0-2-1-3l1-1c0-2 1-6 4-8l1 1c1-2 3-2 5-2 1-1 2-2 2-3z" class="w"></path><path d="M630 410h2c-1 1-2 4-2 6v-2l-1-1c0-1 0-2 1-3z" class="J"></path><path d="M632 404v6h-2-1-1l-1 1c0-3 0-1 2-2l1-2 2-3z" class="e"></path><path d="M624 419c1-1 2-2 3-2l2-2 1 1v2h0v1c-2 0-2 0-4 1v1l-2-2z" class="Q"></path><path d="M628 405h1l-1 2h1 0 1l-1 2c-2 1-2-1-2 2l-1 2h-1l-1-3-1-1h-1v-2l1-1 3 3v-3-1h1 0 1z" class="Am"></path><path d="M627 400h2 0l1 1c-1 1-1 3-2 4h-1 0-1v1 3l-3-3h0-3v-1h0c1-2 3-2 5-2 1-1 2-2 2-3z" class="a"></path><path d="M614 413l1-1c0-2 1-6 4-8l1 1h0v1h3 0l-1 1v2h1l1 1v1l1 2c0 2 0 3-1 4l-2 2v1h1 1v-1l2 2c-3 1-6 2-9 4v-2c0-1 1-1 2-2 0-1-1-1-2-2h0c-1-1-1-2-2-3 0-2 0-2-1-3z" class="An"></path><path d="M618 412l2 2-1 1c-1 1-1 1-2 1-1-2 0-2 1-4z" class="H"></path><path d="M620 405v1h3 0l-1 1v2h1l1 1v1l1 2h-1c-1-1-2-1-3-1s-1 0-1-1c-1-2-1-4 0-6z" class="e"></path><path d="M621 412c1 0 2 0 3 1h1c0 2 0 3-1 4l-2 2v1h1 1v-1l2 2c-3 1-6 2-9 4v-2c0-1 1-1 2-2 0-1-1-1-2-2h0c3-1 3-2 5-3l-1-4z" class="u"></path><path d="M622 416v1l-1 1v2l-1 1h-1c0-1-1-1-2-2h0c3-1 3-2 5-3z" class="w"></path><path d="M620 461c1-1 2-1 3-1h1c1-1 1-2 2-4l-1 6-4 7c0 2-1 6-3 8v3h-3l1 2-3 7c0 1-1 2-2 3v-3h0l-2 1c0-1-1-2-2-2l-2-2 1-1c0 1 1 1 2 2l2-3c0-2 0-4-1-6l-4 1 1-3v-1c1-1 1-3 0-4h1 1 2l1 1 1-1-2-2v-2-1-2-1c1 1 2 1 3 1l1 1c2-1 4-2 6-4z" class="a"></path><path d="M625 462l-4 7h-2c0-1-1-2-1-2l7-5z" class="Ao"></path><path d="M613 489v-1c-1-2-2-3-2-4 1-1 1-2 1-3h0c1-2 1-2 3-2v1l1 2-3 7z" class="AT"></path><path fill="#b77475" d="M618 467s1 1 1 2h2c0 2-1 6-3 8 0-1 0-1-1-2h-1c0-2-1-3 0-4l-1-2c1-1 2-1 3-2z"></path><path d="M618 467s1 1 1 2l1 1c-1 1-3 1-4 1h0l-1-2c1-1 2-1 3-2z" class="An"></path><path d="M521 233h2c-2 5-1 11-1 17-1 4 3 6 0 10 0 1 1 3 1 4 0 3 0 6-1 9-2 3-1 6-1 9v12l-1 1-1 1-1 1h0-1 0l-1-20h1v-19h0l1-15c1-1 2-1 2-3v-1c1-2 1-4 1-6z" class="a"></path><path d="M522 260c0 1 1 3 1 4l-2 2-1-1v-1c0-2 1-3 2-4z" class="J"></path><path d="M516 277h1v-19h0l1 39h-1 0l-1-20z" class="C"></path><path d="M415 377h1l1-1 2 1v1l-1 1c0 1 0 3 1 4h1 1 0 2l1 1c2 3 3 5 4 8l-2-1c1 1 1 2 1 3-1 1-1 2-2 3h1c-1 1-1 2-2 3v3 2c-2 0-2 0-3 1l-1 1h0l-2-1v-1h2l-1-1v-1h-2c-1-1-1-1-1-2v-2c1-1 1-2 1-3-2-2-3-3-3-6l1-1h-2c-1-1-1-2-1-3l-1 1h-1l-1-6 1-1c0-1 0-1 1-1l2-1 1-1h1z" class="AC"></path><path d="M417 396c0 2 1 3 0 4h2v-1c1 0 2-1 2-2l1 1c0 2-2 3-3 6v-1h-2c-1-1-1-1-1-2v-2c1-1 1-2 1-3z" class="Q"></path><path d="M422 398l2 2v3 2c-2 0-2 0-3 1l-1 1h0l-2-1v-1h2l-1-1c1-3 3-4 3-6z" class="u"></path><path d="M413 378l1-1 1 5c1 1 1 1 1 2 0 2-1 3-2 4l-1 1c-1-1-1-2-1-3l-1 1h-1l-1-6 1-1c0-1 0-1 1-1l2-1z" class="x"></path><path d="M413 378l1-1 1 5c1 1 1 1 1 2-1 0-2-1-3 0-2-2-2-3-2-5h0l2-1z" class="i"></path><path d="M413 378l1-1 1 5c-1-1-2-2-4-3h0l2-1z" class="m"></path><path d="M422 395c-1-1-2-2-2-3l-1-1c-1-2-1-3-1-5 1-1 2-2 4-2l3 3c1 1 1 2 1 4 1 1 1 2 1 3-1 1-1 2-2 3-1-1-2-1-3-2z" class="G"></path><path d="M425 387c1 1 1 2 1 4 1 1 1 2 1 3-1 1-1 2-2 3-1-1-2-1-3-2 0-2 0-2-1-3l1-1h1c2-1 0 0 1-1 0-1 1-2 1-3z" class="T"></path><path d="M519 568l1-1c1-2 3-3 5-4-1 3-1 6 0 10 2-1 3-2 4-3v1l-1 2c1 3 3 6 4 9h-1 0-1-2c-1 0-1 1-2 2s-1 1-2 1v-4l-2-1-2 2v1h1v1c-1 0-1 1-1 2l-1-1c-1 0-1 1-2 1s-1 0-1-1v-1l-1-1-2 2-1 1h-1-2c-2-1-3-3-4-4s-2-1-3-2v-1c1 1 1 1 2 0v-1h1v1h2c1 0 2 1 3 0 2-3 1-6 4-8 3-1 3-1 5-3z" class="J"></path><path d="M504 578h1v1l1 1c1 1 1 2 1 3l1 1h1v-2l2-1v2l-1 1h0l2 2h-1-2c-2-1-3-3-4-4s-2-1-3-2v-1c1 1 1 1 2 0v-1z" class="Ar"></path><path d="M524 581c0-2 1-4 1-5l1-1c2 2 4 5 5 7h-1-2c-1 0-1 1-2 2s-1 1-2 1v-4zm-9-9c2-1 2-1 3 0s2 3 2 4c0 2 0 3 1 3l1 1-2 2v1h1v1c-1 0-1 1-1 2l-1-1c-1 0-1 1-2 1s-1 0-1-1v-1l-1-1c-1-1-2-2-2-4v-3c0-2 0-3 2-4z" class="H"></path><path d="M513 579v-3c0-2 0-3 2-4 0 1 1 3 0 4l-2 3z" class="C"></path><path d="M520 576c0 2 0 3 1 3l1 1-2 2v1h1v1c-1 0-1 1-1 2l-1-1c-1 0-1 1-2 1s-1 0-1-1v-1s1 0 2-1c2-2 1-4 2-7z" class="Ad"></path><path d="M581 222c5 1 10 4 15 6 2 1 4 2 5 4h0c-1 1-1 2-1 2h2c1 1 1 2 1 4h0v2c1 1 1 0 1 1h-2c-1 1 0 2-2 2v4c-1 0-1 0-2 1v-1c-1-2-2-1-3-1-2-1-2-2-3-3h-2l-1 2-1-4 1-2 2 2v-1l-2-2c-1 1-1 1-1 2l-2 2-1 1c0-2 0-3 1-4h1v-1l-1 1-2-2v-1c0-1 0-1-1-2h-2c1-1 1-2 2-3h0v-6l-2-3z" class="Ak"></path><path d="M586 231c2-1 4-1 6 0l2 1h0-2c-1 1-2 4-4 5-1 0-3-1-4 0v-1c2 0 2 0 4-1 0-2-1-3-2-4z" class="Ah"></path><path d="M585 230l-1-1c1-3 1-3 3-4h1v2 1l2-1 2 2v2c-2-1-4-1-6 0l-1-1z" class="AV"></path><path d="M585 230l1 1c1 1 2 2 2 4-2 1-2 1-4 1 0-1 0-1-1-2h-2c1-1 1-2 2-3h0l2-1z" class="H"></path><path d="M590 243c1-1 2-1 2-2l2 1h0v-1h-1l4-4 1 1 1-1 1 1h1l2 2c1 1 1 0 1 1h-2c-1 1 0 2-2 2v4c-1 0-1 0-2 1v-1c-1-2-2-1-3-1-2-1-2-2-3-3h-2z" class="T"></path><path d="M598 238l1-1 1 1h1l2 2c1 1 1 0 1 1h-2c-1 1 0 2-2 2-1-1-1-1-3 0v2c-1 0-1 0-1-1s-1-1-1-1v-1c1-1 2-1 4-1h0c1-2 0-2-1-3z" class="G"></path><defs><linearGradient id="As" x1="431.475" y1="261.906" x2="441.04" y2="268.876" xlink:href="#B"><stop offset="0" stop-color="#ebcfcf"></stop><stop offset="1" stop-color="#f4f2f1"></stop></linearGradient></defs><path fill="url(#As)" d="M436 246l2 1v-2c1 1 2 1 3 1l1 6c0 1 1 1 1 2 0 2 1 3 2 4l1 1v1l1-1c1 1 2 1 3 2l-1 1c-1 0-1-1-3-1h0-2v1h1v2 3h-1c0 1-3 3-3 3-1 0-1 0-2 1h0c-1 0-2 1-3 1v1l-2 2c-1 0-1 1-2 2 0 1 0 1 1 2h0l1 1c0 1 0 1 1 2h1l1 1h0v2c-2-2-4-4-5-6l-1-1c-1-2 1-2 2-4-1 0-1 0-2-1h-1c0-2-1-4-1-5v-5c1-2 2-4 3-5l3-3v-4h-1l-1-1c2-2 3-2 3-4z"></path><path d="M442 259h1c1 2 1 4 1 6-2-2-2-4-2-6z" class="S"></path><path d="M429 263c1-2 2-4 3-5 0 3 0 6-1 8l-2 2v-5z" class="AV"></path><path d="M437 253c1-1 1-2 2-2s2 0 3 1c0 1 1 1 1 2 0 2 1 3 2 4l1 1v1c-1-1-2-1-3-1h-1 0c-1-2-3-4-5-4v-1h1l-1-1z" class="e"></path><path d="M436 246l2 1v-2c1 1 2 1 3 1l1 6c-1-1-2-1-3-1s-1 1-2 2l1 1h-1v1h-2v-4h-1l-1-1c2-2 3-2 3-4z" class="AC"></path><path d="M437 253l1 1h-1v-1h0z" class="AJ"></path><path d="M634 379h1v1l-2 2v1 2h1v1c1 1 1 2 2 2 0 2 0 3 1 5 1 1 2 2 2 3l-1 1h-1c-2 1-2 2-3 3l-2 2v2l-2 3h-1 0-1l1-2h-1c1-1 1-3 2-4l-1-1h0-2c-1 0-1 1-2 0h0c0-3 1-4 0-7v3h-1c0-1 0-1-1-3-1 1-2 1-2 1l-3-1-2-2-1 1-3-3c1 0 2-1 2-2h2l1-1 1-2h0l1-1 2-1c0-1 1-1 1-1 1 0 3-1 4-1l2 2 3 1 1-2c1-1 1-2 2-2z" class="G"></path><path d="M626 383h0l1 1-2 2h-2c1-2 2-2 3-3z" class="H"></path><path d="M620 384l2-1 1 1-1 1v3c-1 1-1 1-1 3 2 1 2 0 4 1v1 3h-1c0-1 0-1-1-3-1 1-2 1-2 1l-3-1v-2l2-2c0-2-1-3 0-5z" class="T"></path><path d="M618 384h2c-1 2 0 3 0 5l-2 2v2l-2-2-1 1-3-3c1 0 2-1 2-2h2l1-1 1-2h0z" class="C"></path><path d="M618 384h2c-1 2 0 3 0 5l-2 2v-2l1-3-1-2h0z" class="AV"></path><path d="M614 387h2v4l-1 1-3-3c1 0 2-1 2-2z" class="AJ"></path><path d="M628 382l3 1 2 2c0 2 1 3 0 5h0-3c0 1 0 0 1 1 0 1-1 1-2 1l-1-2h-1c0-2 1-3 2-4 0-1 0-2-1-4z" class="a"></path><path d="M633 385c0 2 1 3 0 5h0v-1c-1-1-2-1-3 0h0v-2c0-1 1-1 3-2z" class="u"></path><path d="M628 382l3 1 2 2c-2 1-3 1-3 2l-1-1c0-1 0-2-1-4z" class="AI"></path><path d="M634 379h1v1l-2 2v1 2h1v1c1 1 1 2 2 2 0 2 0 3 1 5 1 1 2 2 2 3l-1 1h-1c-2 1-2 2-3 3l-2 2v2l-2 3h-1 0-1l1-2h-1c1-1 1-3 2-4l-1-1h0-2c-1 0-1 1-2 0h0c0-3 1-4 0-7v-1l1-1c0 1 1 1 1 1l1-2 1 2c1 0 2 0 2-1-1-1-1 0-1-1h3 0c1-2 0-3 0-5l-2-2 1-2c1-1 1-2 2-2z" class="Z"></path><path d="M631 391c-1-1-1 0-1-1h3 0l1 1 1 2c-1 1-2 2-3 2l-2-1 2-2-1-1z" class="X"></path><path d="M628 390l1 2c1 0 2 0 2-1l1 1-2 2-1-1-2 1v2c-1 1-1 2-1 3l1 1c-1 0-1 1-2 0h0c0-3 1-4 0-7v-1l1-1c0 1 1 1 1 1l1-2z" class="Am"></path><path d="M634 379h1v1l-2 2v1 2h1v1c1 1 1 2 2 2 0 2 0 3 1 5 1 1 2 2 2 3l-1 1h-1c-2 1-2 2-3 3l-2 2v2l-2 3h-1 0-1l1-2h-1c1-1 1-3 2-4 2-4 6-5 6-9l-2-1-1-1c1-2 0-3 0-5l-2-2 1-2c1-1 1-2 2-2z" class="AW"></path><path d="M620 362c-1-1-1-1-1-3 1 0 3 0 4 1h1c1 1 1 2 2 2l1 1c1 1 1 1 2 1l1 1h2l1 1h3l2-1h2v3c0 1 0 1-1 3v1h1c-1 2-2 4-3 5s-2 1-3 1v1c-1 0-1 1-2 2l-1 2-3-1-2-2c-1 0-3 1-4 1 0 0-1 0-1 1l-2 1c-1-1-2-1-2-2-1-2-1-4-2-5h-2 0c2-2 3-2 4-4 0-3 1-4 0-6v-2h1l2-2z" class="AV"></path><path d="M619 370h2v1l-1 1h-1l-1-2h1zm1-8h0c1 1 1 1 1 2-1 0-1 1 0 2l-1 1-3-1v-2h1l2-2z" class="S"></path><path d="M618 372c1 0 2 0 2 1v3l-1 1-2-1c0-2 1-3 1-4z" class="C"></path><path d="M617 366l3 1c0 1-1 2-1 3h-1v2c0 1-1 2-1 4l2 1c2 0 2 0 4-1 1 0 2 0 2-1l2 1v3c-1 0-1 1-1 1-1 0-3 1-4 1 0 0-1 0-1 1l-2 1c-1-1-2-1-2-2-1-2-1-4-2-5h-2 0c2-2 3-2 4-4 0-3 1-4 0-6z" class="AC"></path><path d="M617 376l2 1c2 0 2 0 4-1-1 1-1 3-1 4v1s-1 0-1 1l-1-3h-2l-1-1v-2z" class="Ad"></path><path d="M625 375l2 1v3c-1 0-1 1-1 1-1 0-3 1-4 1v-1c0-1 0-3 1-4 1 0 2 0 2-1z" class="AC"></path><path d="M630 365h2l1 1h3l2-1h2v3c0 1 0 1-1 3v1h1c-1 2-2 4-3 5s-2 1-3 1v1c-1 0-1 1-2 2l-1 2-3-1-2-2s0-1 1-1v-3l-2-1h0c1 0 1-1 2-1 1-1 2-3 2-5v-2c1-1 1-1 1-2z" class="Ao"></path><path d="M627 376l5-5c0-1-1-3 0-4h1c0 1-1 2-1 3l1 1v1 1c0 1 0 1-1 2v1h0c1 0 0 0 1 1-2 1-3 1-4 1l-1-1-1-1z" class="e"></path><path d="M627 376l1 1 1 1c1 0 2 0 4-1l1 1v1c-1 0-1 1-2 2l-1 2-3-1-2-2s0-1 1-1v-3z" class="J"></path><path d="M629 378c1 0 2 0 4-1l1 1v1c-1 0-1 1-2 2-2-1-2-2-3-3z" class="Am"></path><path d="M638 365h2v3c0 1 0 1-1 3v1h1c-1 2-2 4-3 5s-2 1-3 1l-1-1c-1-1 0-1-1-1h0v-1c1-1 1-1 1-2v-1-1l-1-1c0-1 1-2 1-3h0v-1h3l2-1z" class="w"></path><path d="M638 365h2v3c0 1 0 1-1 3v1h1c-1 2-2 4-3 5l-2-1 1-2h0l1-1c1-3 1-5 1-8z" class="X"></path><path d="M633 367l3 3-2 2c0 1 0 2 1 3v1l2 1c-1 1-2 1-3 1l-1-1c-1-1 0-1-1-1h0v-1c1-1 1-1 1-2v-1-1l-1-1c0-1 1-2 1-3h0z" class="n"></path><path d="M510 231c2 2 3 3 3 6 1 1 1 1 1 2v1l1 16 1 21 1 20v3l-2 3h1v11 2h-1v1c0-2-1-4 0-5v-1-1l-3-2c0-4-1-7-2-11-1-3-1-5-1-8v-1-12c1-3 1-6 1-9v-4c1-1 0-2 1-3l1-1v-2-3c0-3 0-6-1-9 0-1 1-2 2-4l-2-5c0-1 0-2-1-2h-1v-2l1-1z" class="Z"></path><path d="M510 231c2 2 3 3 3 6 1 1 1 1 1 2v1l1 16c0 1 0 1-1 2-1-4 0-11 0-15h-1v-2l-2-5c0-1 0-2-1-2h-1v-2l1-1z" class="Q"></path><path d="M509 289c1 0 2 0 2 2v3h1v-2h1c0 3-2 5 0 8h1c-1-1-1-2-1-3l1-1c0 1 1 2 1 4 0 3-1 6 0 10l-3-2c0-4-1-7-2-11-1-3-1-5-1-8z" class="AW"></path><path d="M510 267h1c0-1 0 0 1-1l1 1c0 1 0 3 1 5v7l-1 1v1c0 1 0 1-1 2 1 1 1 2 1 4 0 1 0 1-1 1h0v-4c-1 1-1 1-1 2v1c-1 0-2 1-2 1v-12c1-3 1-6 1-9z" class="e"></path><path d="M615 342c3 1 5 2 8 2h0 8c0 1 0 1 1 2h0v-3h2c2 1 2 1 3 2l1 2h0c1 0 1 0 2 1h2 1v2l-1 2v1c1 1 1 2 0 3v3 2c-1 0-2 0-3-1 0 1 0 3-1 4 1 0 1 0 2 1h-2l-2 1h-3l-1-1h-2l-1-1c-1 0-1 0-2-1l-1-1c-1 0-1-1-2-2h-1c-1-1-3-1-4-1 1-2 1-3 3-4l1-3h0c-2-1-3-1-5 0-1 0-2 0-4 2v2h-1c0-1 1-1 1-1 0-2-1-3-2-4l1-1c1 0 1 0 2-1 1 0 1-1 2-1l-1-1c-1 0 0-1-1-1 1-1 0-3 0-4z" class="AC"></path><path d="M632 346h0l1 2h0l-4 3-2-2 1-2h2 1 0l1-1z" class="Z"></path><path d="M642 353c1 1 1 2 0 3v3 2c-1 0-2 0-3-1l3-7z" class="n"></path><path d="M630 358c0-1 1-2 2-3 1 2 1 3 3 4v2c-1 0-1 0-2 1l-2-3h0l-1-1z" class="Q"></path><path d="M631 359h4v2c-1 0-1 0-2 1l-2-3h0z" class="m"></path><path d="M638 347h0c1 0 1 0 2 1h2 1c-1 1-1 2-2 3h0l-1 1c-1 1-2 1-2 3h-1c1-3 1-5 1-8z" class="e"></path><path d="M633 362c1-1 1-1 2-1l1 2 2 1h0c1 0 1 0 2 1h-2l-2 1h-3l-1-1c1-1 1-1 1-3z" class="q"></path><path d="M636 363l2 1h0c1 0 1 0 2 1h-2l-2 1v-3z" class="e"></path><path d="M615 342c3 1 5 2 8 2v3h-2v2l-1 1-1-1h-1c-2 1-2 2-4 1h-1c1 0 1 0 2-1 1 0 1-1 2-1l-1-1c-1 0 0-1-1-1 1-1 0-3 0-4z" class="Af"></path><path d="M620 346v-1h1v2l-2 1 1-2h0z" class="Ah"></path><path d="M632 346v-3h2c2 1 2 1 3 2l1 2c0 3 0 5-1 8l-1 1c-2 0-2-1-3-2 2-3 1-3 0-6l-1-2z" class="c"></path><path d="M623 352h2v1c1 1 0 1 1 1l4 4 1 1h0l2 3c0 2 0 2-1 3h-2l-1-1c-1 0-1 0-2-1l-1-1c-1 0-1-1-2-2h-1c-1-1-3-1-4-1 1-2 1-3 3-4l1-3z" class="G"></path><path d="M623 352h2v1c1 1 0 1 1 1l-1 2h0v3h-1l-2-2v-1-1l1-3z" class="T"></path><path d="M629 364c0-1 0-1-2-2l2-2h1l1-1 2 3c0 2 0 2-1 3h-2l-1-1z" class="Ak"></path><path d="M537 177h5l2 2c2 0 2 0 3-2h1 1l2 2c0 2 0 5 1 7 0 4-1 6-2 8l-1 1c-3 1-5 2-8 4-1 2-3 3-5 6h0v3c-1 0-1 0-2 1h0c0 1 0 1-1 2 0-1-1-2-2-3-2 1-3 2-4 3-1-1-1-2-1-3v-2h0c-1-2 0-3 0-4 0-2 1-3 2-5l1-3v-2l1-2h0c1 0 1 0 1-1 1-2 0-5 1-7 0-1 1-3 2-4l3-1z" class="D"></path><path d="M535 193l1 1c-1 1-1 1-1 2l2 2-3 3c0-1 0-3-1-4v-2l2-2z" class="H"></path><path d="M546 180l3 2v2l-3-1c-1 0-2 3-3 5 0 1 0 1-1 2v1l-1 1 1-1c-1-2-1-3-1-4 1-2 1-2 1-4h2l2-3z" class="S"></path><path d="M547 177h1 1l2 2c0 2 0 5 1 7 0 4-1 6-2 8l-1 1c-3 1-5 2-8 4h0c2-2 7-5 9-8 1-2 0-5-1-7v-2l-3-2h-1l-1-1c2 0 2 0 3-2z" class="AM"></path><path d="M547 177h1c1 3 1 3 1 5l-3-2h-1l-1-1c2 0 2 0 3-2z" class="AW"></path><path d="M530 190h0c1 0 1 0 1-1h1v1l2 1v1l1 1-2 2v2c1 1 1 3 1 4l1 2c2-1 4-3 6-4h0c-1 2-3 3-5 6h0v3c-1 0-1 0-2 1h0c0 1 0 1-1 2 0-1-1-2-2-3-2 1-3 2-4 3-1-1-1-2-1-3v-2h0c-1-2 0-3 0-4 0-2 1-3 2-5l1-3v-2l1-2z" class="AJ"></path><path d="M531 204h1v1l1 1h-1c1 1 1 1 2 1l2-2v3c-1 0-1 0-2 1h0c0 1 0 1-1 2 0-1-1-2-2-3l1-1c0-1-1-2-1-3z" class="AC"></path><path d="M526 208h2c0-2 0-5 2-6v2h1c0 1 1 2 1 3l-1 1c-2 1-3 2-4 3-1-1-1-2-1-3zm0-2c-1-2 0-3 0-4 0-2 1-3 2-5h2 0l2 3-1 1h-1v-2c-2 2-2 4-4 7z" class="Q"></path><path d="M530 190h0c1 0 1 0 1-1h1v1l2 1v1l1 1-2 2v2h-1-2 0-2l1-3v-2l1-2z" class="J"></path><path d="M529 194l3 3h-2 0-2l1-3z" class="Z"></path><g class="AV"><path d="M532 190l2 1v1l1 1-2 2-1-5z"></path><path d="M537 177h5l2 2 1 1h1l-2 3h-2c0 2 0 2-1 4 0 1 0 2 1 4l-1 1c0 1-1 2-2 3h0v-2c-2-2-2-2-4-2l-1 1v-1l-2-1v-1h-1c1-2 0-5 1-7 0-1 1-3 2-4l3-1z"></path></g><path d="M545 180h1l-2 3h-2c0-1-1-2-1-2v-1h1l2 1 1-1z" class="G"></path><path d="M532 189c0-1 1-3 2-4s2-4 5-5l1 2-2-1v1c0 1-1 2-2 3v1c-2 1-2 2-3 3l1 2-2-1v-1z" class="C"></path><path d="M534 191l-1-2c1-1 1-2 3-3v-1c1-1 2-2 2-3v-1l2 1c1 2 1 1 0 3l1 2c0 1 0 2 1 4l-1 1c0 1-1 2-2 3h0v-2c-2-2-2-2-4-2l-1 1v-1z" class="S"></path><path d="M541 192l-1 1v-2c0-1-1-1-1-2s0-3 1-4l1 2c0 1 0 2 1 4l-1 1z" class="B"></path><path d="M482 545c1 2 1 3 1 5 0 0 1 1 2 1h0l3 3h3v1l1-1h2l1 2c1 0 3-1 4-1l-1 1 1 1 1 2v1l1-1h0c1 1 0 2-1 3v1 1c0 1-1 2-1 2v1 1 2 1h1c0-2 1-5 3-6h1v3h0v1h0l1 1v-1c1 1 1 2 1 3h-1l-1-1v1l-1-1c0 1 0 1 1 1 0 1 1 1 1 2v1c1 0 1 1 1 2h1 1-1v-3l1-1 2 2c-1 0-1-1-2-1 0 1 1 3 1 4 0 0-1 1-2 1h-2v-1h-1v1c-1 1-1 1-2 0v1h-1c0 1 1 2 2 3h-1v-1c-1-1-3-2-4-3h0-2l1 1h-3l-1-2c0-1-1-2-2-3-1 0-2-1-3-1h-1-1c-1 0-1 0-2-1l-1-1c-2-2-3-3-4-5l-1-1 1-1h1c0-2-1-3-1-5v-1c0-1-1-2-1-3l1-3c1-2 2-2 2-4v-1c0-1 0-2 1-3z" class="S"></path><path d="M488 554h3l-1 1h-2v-1z" class="H"></path><path d="M503 573c2 2 2 2 2 5h-1l-1-1h-2v-2l2-2z" class="AV"></path><path d="M497 568c1 1 0 2 1 4l2 1c1-1 1-4 2-5l1 1h0c-1 2-1 3 0 4l-2 2v-1h-2l-3-2 1-1v-3h0z" class="Af"></path><path d="M487 561h4c1 0 0-2 1-2 0-1 1 0 1-2l-1-1 1-1c0 1 2 2 3 2l-1 1c-1 1-2 2-2 4-2 1-3 1-5 2 0-1-1-2-1-3z" class="T"></path><path d="M494 569h1v-1c1-1 2-1 2-2v2h0v3l-1 1 3 2h2v1 2h2l1 1v1c-1 1-1 1-2 0v1h-1c0 1 1 2 2 3h-1v-1c-1-1-3-2-4-3h0c-1-1-1-2-1-3-1-1-2-1-3-2v-1c0-1-1-1-1-2s0-1 1-2z" class="Am"></path><path d="M482 545c1 2 1 3 1 5 0 0 1 1 2 1h0l3 3v1c0 2 0 2-1 4h0l-1 2h1c0 1 1 2 1 3 1 2 2 3 4 4h0c1 1 1 1 2 1-1 1-1 1-1 2s1 1 1 2v1c1 1 2 1 3 2 0 1 0 2 1 3h-2l1 1h-3l-1-2c0-1-1-2-2-3-1 0-2-1-3-1h-1-1c-1 0-1 0-2-1l-1-1c-2-2-3-3-4-5l-1-1 1-1h1c0-2-1-3-1-5v-1c0-1-1-2-1-3l1-3c1-2 2-2 2-4v-1c0-1 0-2 1-3z" class="e"></path><path d="M483 566l-1 1h-1v-6c-1-1-1-1-1-2s0-1 1-2c1 1 2 2 2 4-1 1-1 2-1 4h1v1z" class="X"></path><path d="M483 550s1 1 2 1h0c0 2-1 3-1 5v2l-1-1c-1-1-2-3-2-5 1 0 1-1 2-1v-1z" class="Ak"></path><path d="M485 551l3 3v1c0 2 0 2-1 4h0v-1h-1l-1 1-1-1v-2c0-2 1-3 1-5z" class="AV"></path><path d="M488 570l1 1c1 0 2 0 2 1 2 3 5 4 5 7l1 1h-3l-1-2c0-1-1-2-2-3-1 0-2-1-3-1h-1v-1c0-1 0-2 1-3z" class="a"></path><path d="M488 570l1 1c1 1 1 2 2 3v1c-1 0-2-1-3-1h-1v-1c0-1 0-2 1-3z" class="X"></path><path d="M483 565h-1c0-2 0-3 1-4 2 3 3 6 5 9-1 1-1 2-1 3l-3-1-1-1c-1-1-1-3 0-5v-1z" class="m"></path><path d="M483 565h1v3c1 1 1 2 2 3-1 1-1 0-2 1l-1-1c-1-1-1-3 0-5v-1z" class="q"></path><path d="M415 311c1-1 1-1 1-2 0 1 0 2 1 3h0 3l1 1 1-1-2-2v-1c1 1 1 1 2 1h0c1 1 3 2 4 2v2l-1 1v1c1 2 1 3 2 5v2c1 1 1 1 2 1 0 1 0 2-1 3h-1v1h-1c-1 2-2 4-1 5l1 2h0c0 1-1 2-1 3-1 1-1 2-1 3 1 2 2 4 4 5 0 1-1 2-1 3h-1l-2-1-1-1c0-1-1-2-1-4l-2-2v1h-1c-1 0-2 0-3 1-1-2-3-2-3-4 1 0 1 0 2-1v-1-3-1-1c-1-1 0-1 0-2v-1l-1 1c-1 1-1 1-2 3l-3 3-2-2h-1-2v-2-2-3s1-3 1-4l1-1c0-1 0-2 1-3 1 0 1-1 1-2l1-1c1-1 2-1 2-2l1 1v-3h2l1-1z" class="w"></path><path d="M422 329l2 1v1h-5 0l1-1c1 0 1-1 2-1z" class="AM"></path><path d="M422 318c1 2 2 4 0 5v1h-1c-1-1 0-1-1-1l-1-1c1-2 2-2 3-4z" class="X"></path><path d="M409 316c1-1 2-1 2-2l1 1c1 1 2 1 2 2l-1 1-1 1-3-3z" class="AG"></path><path d="M419 331h5l-2 5c-2 0-1 0-2-1h-1v-4z" class="Q"></path><path d="M415 333l1-1-1-1 2-1 2 1h0v4h-1c-1 0-2-1-3-1v-1z" class="n"></path><path d="M426 328h-1v-2c-1-1-1-1-1-2l1-1h1 1c1 1 1 1 2 1 0 1 0 2-1 3h-1v1h-1z" class="a"></path><path d="M419 325l2-1c1 1 2 2 2 4h0l-1 1c-1 0-1 1-2 1l-2-2-1-1c1-2 1-2 2-2z" class="i"></path><path d="M419 325l2 1v1c-1 1-2 1-3 1l-1-1c1-2 1-2 2-2z" class="q"></path><path d="M415 311c1-1 1-1 1-2 0 1 0 2 1 3h0v3h0-1l-2 2c0-1-1-1-2-2v-3h2l1-1z" class="i"></path><path d="M412 312h2l-1 2h0c1 1 2 1 3 0v1l-2 2c0-1-1-1-2-2v-3z" class="n"></path><path d="M422 312l1 1v4c-1 0-1 1-1 1-1 2-2 2-3 4l1 1v1h-1-2c0-2 0-3 1-4s2-1 2-2c1 0 1 0 1-1h-2 0l-2-2v-3h3l1 1 1-1z" class="m"></path><path d="M415 334c1 0 2 1 3 1h1v3h1l1-1 1 1v1h-2v2 1h-1c-1 0-2 0-3 1-1-2-3-2-3-4 1 0 1 0 2-1v-1-3z" class="AH"></path><path d="M411 322l1-1v-2c1 0 2-1 3 0l1 1c0 2 0 5-1 7l-1 1c-1 0 0 0-1 1h-1v-3h1l-1-1h-1v2l-1 1h-1l1-1v-1c0-2 0-3 1-4z" class="X"></path><path d="M408 317l3 5c-1 1-1 2-1 4v1l-1 1h1l1-1v-2h1l1 1h-1v3h1l1 1c-1 1-1 1-2 3l-3 3-2-2h-1-2v-2-2-3s1-3 1-4l1-1c0-1 0-2 1-3 1 0 1-1 1-2z" class="AO"></path><path d="M407 330c1-3 0-4 0-7l2-2 1 1v4 1l-1 1h1l-1 3v1c-1-1-2-1-2-2z" class="Y"></path><path d="M410 328l1-1v-2h1l1 1h-1v3h1l1 1c-1 1-1 1-2 3l-3 3-2-2h-1-2v-2-2-3c1 1 2 1 3 3 0 1 1 1 2 2v-1l1-3z" class="AG"></path><path d="M461 504l1 2v2c2-1 2-1 3-2l2 1c0 2 0 3 2 4 2 0 3 1 5 0h0c1-1 1-2 3-3h0c2 1 3 0 4-1l4 1 1 1h-2c-1-1-3 0-4 0l-1 1c-2 1-3 2-3 3l1 1c1 0 1 1 2 1 0 1 0 2-1 3v1h0 0c2 0 2 0 3 2-1 1-1 1-3 2-1-1-1-1-2-1 0 1 0 1-1 2 0 1 1 1 0 2l1 1h1c1-2 1-3 2-4l1 1v3 1h1 2c0-1 1-1 2-1h0l-2 2v1c-2 0-3 0-4 1s-1 2-2 3c0 2 1 3 0 5-1 1-2 2-2 3v1l-1 1c0-1-1-1-1-2h-2 0l-1-2h-1l-1 2c-1 0-1 1-2 2v1 1c-1-2-2-5-2-7-1-2-2-3-2-5l-1-2c-2-1-2-4-3-6 0-1 0-1-1-2v-4h0l1-1v-2h0l1-2v-5-2-2c0-1 1-2 2-2z" class="E"></path><path d="M467 518c1 0 2 1 3 1l1 1h-1v5h2l1-1c0-2-1-3 1-4 0 1 1 1 1 2 0 2-1 5-1 7h-2 0c-2-1-3-2-4-5-1-2-1-4-1-6z" class="T"></path><path d="M469 531c-1-1-3-1-4-2-1-2-3-3-4-4 0-2-1-3 0-5h1v1c0 2 0 2 1 4 0 0 1 2 3 2h0v-1l1-1h0c1 0 1 0 1-1 1 3 2 4 4 5h0 2c1 1 2 1 3 0v-1s1-1 1-2h1l-1 1v1l1 2c1-1 3-1 4-1v1c-2 0-3 0-4 1s-1 2-2 3c-1 0-2-1-3-1v-1h-3l1-1c-1-1-2-1-3 0z" class="Ad"></path><path d="M461 504l1 2v2c2-1 2-1 3-2l2 1c0 2 0 3 2 4 2 0 3 1 5 0h0c1-1 1-2 3-3l-1 1c0 1-1 2-1 4h0c-2 0-3 0-4 1l-4 3v1c0 2 0 4 1 6 0 1 0 1-1 1h0l-1 1v1h0c-2 0-3-2-3-2-1-2-1-2-1-4v-1h0c-2-1-2-2-4-3l1-2v-5-2-2c0-1 1-2 2-2z" class="AC"></path><path d="M460 515c1-2 1-2 2-3 1 1 1 3 1 5l-1-1c-1-1-1 0-2-1h0z" class="i"></path><path d="M460 515h0c1 1 1 0 2 1l1 1 1 1v1l-1 2h-1v-1h0c-2-1-2-2-4-3l1-2h1z" class="Y"></path><path d="M461 504l1 2v2 4c-1 1-1 1-2 3h-1v-5-2-2c0-1 1-2 2-2z" class="m"></path><path d="M458 517h0c2 1 2 2 4 3h0-1c-1 2 0 3 0 5 1 1 3 2 4 4 1 1 3 1 4 2 1-1 2-1 3 0l-1 1h3v1c1 0 2 1 3 1 0 2 1 3 0 5-1 1-2 2-2 3v1l-1 1c0-1-1-1-1-2h-2 0l-1-2h-1l-1 2c-1 0-1 1-2 2v1 1c-1-2-2-5-2-7-1-2-2-3-2-5l-1-2c-2-1-2-4-3-6 0-1 0-1-1-2v-4h0l1-1v-2z" class="n"></path><path d="M457 520c1 2 2 3 3 6 0 1 3 2 4 4l-1 1h-2v1c-2-1-2-4-3-6 0-1 0-1-1-2v-4z" class="AR"></path><path d="M462 534c1 1 2 2 3 2l1-1c1 1 0 1 0 2s1 2 2 3h1l-1-2c1 1 1 1 2 1 1 1 1 1 2 1l1 2h-2 0l-1-2h-1l-1 2c-1 0-1 1-2 2v1 1c-1-2-2-5-2-7-1-2-2-3-2-5z" class="AB"></path><path d="M469 531c1-1 2-1 3 0l-1 1h3v1c1 0 2 1 3 1 0 2 1 3 0 5-1 1-2 2-2 3v1l-1 1c0-1-1-1-1-2l-1-2c-1-2-2-3-3-4l-2-3c1-1 1-2 2-2z" class="a"></path><path d="M469 536v-1c1-1 1-1 0-3h1l3 3v4h2c-1 1 0 0-1 2l1 1v1l-1 1c0-1-1-1-1-2l-1-2c-1-2-2-3-3-4z" class="AW"></path><path d="M602 234c3-1 6 3 8 5 3 2 5 5 8 7h0l-2 1v1c0 1 0 1-1 2 1 1 1 1 1 2-1 1-2 1-3 2l-2 2c-1 1-2 1-2 2-1 0-1 0-2-1v2l-1 1 1 1h-1l-1 2v1c-1 2-3 4-5 6l-3 3c-1 1-1 2-2 3l-1-2-1-2c1-1 1-1 1-2s0-3-1-4c-1-2-1-3-1-5 0-1 0-1-1-2-2 0-4 2-6 3l1-2h0l2-2h2v-1l1 1h0l-1-1 1-1c-1-2-1-2-3-2v-1h-3 0c-1-1-1-2 0-3h0l-1-1-2 2 1 1v1c0 1 0 1-1 2l-2-2h-1c-2 1-3 2-4 4 1 1 1 3 1 4v2 2h-1c0-2 0-4-1-6v-1c-1-1-1-1 0-2l2-2 1-1c2-1 2-2 4-3h1c0-1 1-2 2-4h1l1 1 2-2h1l1-2h2c1 1 1 2 3 3 1 0 2-1 3 1v1c1-1 1-1 2-1v-4c2 0 1-1 2-2h2c0-1 0 0-1-1v-2h0c0-2 0-3-1-4z" class="S"></path><path d="M584 246h1l1 1 2-2v3c-1 1 0 3 0 4-1 0-1-1-2 0-1-2 0-1 0-2s-1-2-1-2c-1 0-1 0-1 1-1 0-1 1-2 1 0-1 1-2 2-4z" class="Ah"></path><path d="M602 234c3-1 6 3 8 5 3 2 5 5 8 7h0l-2 1v1c0 1 0 1-1 2 1 1 1 1 1 2-1 1-2 1-3 2l-2 2c-1 1-2 1-2 2-1 0-1 0-2-1v2l-1 1h-3c-1-1-2-2-4-3h-1-2c-1 0 0 0-1-1l1-1h1v-1s0-1 1-1c0-2 0-2 1-3l-1-2c1-1 1-1 2-1v-4c2 0 1-1 2-2h2c0-1 0 0-1-1v-2h0c0-2 0-3-1-4z" class="T"></path><path d="M604 241v3h2l1 1v1c-1 0-2-1-3 0v-1c-1 0-1 0-1-1-2 1-2 3-3 5l-1 1-1-2c1-1 1-1 2-1v-4c2 0 1-1 2-2h2z" class="Ah"></path><path d="M603 249c2-1 3-2 4-1l1 1c0 2 1 3 0 5v2c-1-1-2-1-2-2-1 0-1 0-2-1l1-2h0-1-1v-2z" class="AV"></path><path d="M612 242c0 1 1 1 1 3-1 1-1 2-2 4h-1-2l-1-1c-1-1-2 0-4 1 0-1 1-2 1-3 1-1 2 0 3 0v-1l1 1 1-1c1-1 1-2 2-3h1z" class="Ap"></path><path d="M602 234c3-1 6 3 8 5 0 2 0 2 2 3h-1c-1 1-1 2-2 3l-1 1-1-1-1-1h-2v-3c0-1 0 0-1-1v-2h0c0-2 0-3-1-4z" class="Ao"></path><path d="M610 239c3 2 5 5 8 7h0l-2 1v1c0 1 0 1-1 2 1 1 1 1 1 2-1 1-2 1-3 2l-2 2c-1 1-2 1-2 2-1 0-1 0-2-1l1-1v-2c1-2 0-3 0-5h2 1c1-2 1-3 2-4 0-2-1-2-1-3-2-1-2-1-2-3z" class="AC"></path><path d="M608 249h2v3l1 1h0l1 1 3-4c1 1 1 1 1 2-1 1-2 1-3 2l-2 2c-1 1-2 1-2 2-1 0-1 0-2-1l1-1v-2c1-2 0-3 0-5z" class="a"></path><path d="M414 330l1-1v1c0 1-1 1 0 2v1 1 3 1c-1 1-1 1-2 1 0 2 2 2 3 4 1-1 2-1 3-1h1v-1l2 2c0 2 1 3 1 4l1 1 2 1c1 2 1 4 2 7v2c0 1 1 1 1 1l-2 2-2-1h-1v-1h-1v3l-3 2 1 1c1 1 3 0 3 3l-3 3h-1v1l-1-1-2-2-1 2c-1 2-1 3-2 5l1 1h-1l-1 1-2 1c-1 0-1 0-1 1l-1 1-2-9-3-1-2-9c1-1 1-2 1-3l-1-7v-1-1l-1-5v-1-1h3v-9h2 1l2 2 3-3c1-2 1-2 2-3z" class="AW"></path><path d="M420 341l2 2-2 3-1-1c1-1 1-1 1-2v-1-1z" class="X"></path><path d="M422 343c0 2 1 3 1 4h0-1l-1 2h-1v-3h0l2-3z" class="v"></path><path d="M423 347l1 1c-1 3-1 4-1 7l1 1 1 1v1 2h-1v-1h-1v3l-3 2c0-1 0 0-1-1v-1c0-1 1-2 2-3v-1c0-1 0-1 1-2v-1-3c0-2 1-3 1-5h0z" class="u"></path><path d="M424 356l1 1v1 2h-1v-1h-1-1v-1h1l1-2z" class="Z"></path><path d="M419 342c-1 2-2 4-2 6 1 1 1 1 2 1l1 1c-1 1-3 1-4 1l-1 2c0-1-1-2-1-2v-4-3l2-1c1-1 2-1 3-1z" class="n"></path><path d="M424 348l2 1c1 2 1 4 2 7v2c0 1 1 1 1 1l-2 2-2-1v-2-1l-1-1-1-1c0-3 0-4 1-7z" class="J"></path><path d="M410 355l1-1c2-1 2-1 3-1 2 1 2 3 4 4l-1 1v2l-2 1h0l-2 2-1-2h0l-2-3v-3z" class="Ak"></path><path d="M412 361c0-1 1-1 0-2v-3l2-1h0c1 1 1 3 1 5l-1 1h-2 0z" class="An"></path><path d="M410 358l2 3h0l1 2 2-2 3 4h-1v2 2l-1 2c-1 2-1 3-2 5l1 1h-1l-1 1-2 1c-1 0-1 0-1 1l-1 1-2-9-3-1-2-9c1-1 1-2 1-3h0l1 2h1l1 1h1v-1-1-1l1 1h1 1v-2z" class="m"></path><path d="M408 372v-5c1 0 1 0 1 1 2 2 1 4 1 6l-2-2zm2-14l2 3h0l1 2 2-2 3 4h-1v2 2l-1 2h-2c-1 0-1-1-2-1v-1h1v-1c-2-1-3-2-3-4-1-1 0 1 0-1h-1-1v-3h1 1v-2z" class="AI"></path><path d="M403 359h0l1 2h1c0 2 1 4 1 5s0 1 1 2v2c1 1 1 1 1 2l2 2h2l-1 1 3 1 1 1h-1l-1 1-2 1c-1 0-1 0-1 1l-1 1-2-9-3-1-2-9c1-1 1-2 1-3z" class="Z"></path><path d="M413 378c-2-1-2-1-3-3h1l3 1 1 1h-1l-1 1z" class="q"></path><path d="M403 359h0l1 2c0 4 1 8 3 11l-3-1-2-9c1-1 1-2 1-3z" class="s"></path><path d="M414 330l1-1v1c0 1-1 1 0 2v1 1 3 1c-1 1-1 1-2 1 0 2 2 2 3 4l-2 1v3 4s1 1 1 2h-1c-1 0-1 0-3 1l-1 1v3 2h-1-1l-1-1v1 1 1h-1l-1-1h-1l-1-2h0l-1-7v-1-1l-1-5v-1-1h3v-9h2 1l2 2 3-3c1-2 1-2 2-3z" class="X"></path><path d="M415 332v1 1 3 1c-1-1-2-2-2-3s1-2 2-3z" class="q"></path><path d="M411 346c1 1 1 2 1 4l2 1s1 1 1 2h-1c-1 0-1 0-3 1l-1 1v-1c0-2 0-2-1-3 1-1 1-1 1-2 1-1 0-2 1-3z" class="i"></path><path d="M406 334h1l2 2v1h-1l1 1 1 1v3l1 1-1 1c1 1 0 1 1 2-1 1 0 2-1 3-1-2-1-3-2-5h-3l-1-1v-9h2z" class="n"></path><path d="M406 334h1l2 2v1h-1l1 1-1 3h0-1c-1-1-1-2-2-3 0-1 1-3 1-4z" class="Y"></path><path d="M404 343l1 1h3c1 2 1 3 2 5 0 1 0 1-1 2 1 1 1 1 1 3v1 3 2h-1-1l-1-1v1 1 1h-1l-1-1h-1l-1-2h0l-1-7v-1-1l-1-5v-1-1h3z" class="x"></path><path d="M401 344c2 2 3 1 4 3-1 2-1 2-3 3l-1-5v-1z" class="AI"></path><path d="M406 347c1 0 2 0 3 1v3c1 1 1 1 1 3l-2-2h0c-1 0 0 0-1-1h-1c1-2 0-3 0-4z" class="n"></path><path d="M406 347c0 1 1 2 0 4h0v3l-4-2v-1-1c2-1 2-1 3-3h1z" class="Y"></path><path d="M402 351h3 1v3l-4-2v-1z" class="c"></path><path d="M406 351h1c1 1 0 1 1 1h0l2 2v1 3 2h-1-1l-1-1v1 1 1h-1l-1-1h-1l-1-2h0l-1-7 4 2v-3h0z" class="AH"></path><path d="M406 351h1c1 1 0 1 1 1h0l-1 3 1 1c-1 1-1 1-3 2 1-1 1-2 1-3v-1-3h0z" class="q"></path><path d="M402 352l4 2v1c0 1 0 2-1 3l-1-1c0 1 0 1-1 2h0l-1-7z" class="i"></path><path d="M444 458c2 1 3 2 5 2h0l1 1h1c0 1 0 1 1 2 1-1 1-1 3 0l2 1c2 1 4 2 5 4 1 3 1 5 0 8h0l-2 2c-1 0-1 1-2 2 0 2 0 2 1 3l-2 2h1c1 1 1 2 1 3 1 2 1 4 2 6 1 1 1 0 2 0 1-1 1-2 1-4 1 1 2 2 2 3l-1 1c1 1 2 1 4 2l1 1h-4-2c0 1 0 1 1 2-1 1 0 1-1 1-1 1 0 1 0 2 1 0 1 1 2 2 0 1-1 2-1 2-1 1-1 1-3 2v-2l-1-2c-1 0-2 1-2 2v2h-1c-1 1-1 0-1 0l-2-2h1l-1-2v-1h-1c-2 0-3 0-5-1l-6-16-2-6-3-7h0l-1-7-1-2 2 1h1 2l-1-2h1c0 1 1 1 1 2l1-1-2-2c1-1 1-1 1-2l1-1h1v-1z" class="Ad"></path><path d="M449 481l1-5 1 1c0 2 1 4 0 6 0-1-1-2-2-2z" class="AM"></path><path d="M458 480l-1-1h-1v2c-1-1 0-1-1-2 0-1-1-2-1-2v-1c-1-2-2-5-3-7h-1v-1h2v2h2 0c1 2 1 3 2 4l-1 2c2 1 3 1 4 1l1 1c-1 0-1 1-2 2z" class="Ap"></path><path d="M452 463c1-1 1-1 3 0l2 1c2 1 4 2 5 4 1 3 1 5 0 8h0l-2 2-1-1c-1 0-2 0-4-1l1-2c-1-1-1-2-2-4h0-2v-2-1l-1-1 1-3z" class="S"></path><path d="M459 477c1-2 0-4 0-6 2 1 1 2 2 4l1 1h0l-2 2-1-1z" class="T"></path><path d="M455 463l2 1c2 1 4 2 5 4-1 1-1 1-2 1 0-1 0-2-1-2l-1-1v-1l-2 1c0-1-1-2-1-3z" class="C"></path><path d="M436 464l2 1h1 2l1 1 3 2v1 2c1 0 2 1 2 2v2 1c1 0 1 1 1 2l1 1v2c1 0 2 1 2 2l1 1h-1c-1 0-2-1-4-1l-1-1c0-1 0-1-1-1 0-1-1-2-2-2h-1c-1 0-1 1-1 1l-3-7h0l-1-7-1-2z" class="x"></path><path d="M436 464l2 1h1 2l1 1v1c0 1 0 2 1 2h-2v1c2 3 4 4 4 7h-1l-2-3-2 1-2-2h0l-1-7-1-2z" class="AH"></path><path d="M437 466h2v2c0 2 2 4 2 5l1 1-2 1-2-2h0l-1-7z" class="AO"></path><path d="M441 480s0-1 1-1h1c1 0 2 1 2 2 1 0 1 0 1 1l1 1c2 0 3 1 4 1v1c1 1 3 2 3 3 1 1 0 2 1 4v1h1l1 1c0 1-1 2 0 3h1 0v-2c1 0 1 1 2 2h0l1 1 1-1v-1l1 1-1 2h1l1 1c-1 1 0 1 0 2 1 0 1 1 2 2 0 1-1 2-1 2-1 1-1 1-3 2v-2l-1-2c-1 0-2 1-2 2v2h-1c-1 1-1 0-1 0l-2-2h1l-1-2v-1h-1c-2 0-3 0-5-1l-6-16-2-6z" class="AG"></path><path d="M451 500l1 1-1 1h-1 0l1-2z" class="i"></path><path d="M452 494h1v2c1 1 1 1 2 1h1 1 1l-1 1v1s-1 1-2 1l-1-1-1 1-1-1c-1-2-1-2 0-5zm-2-6c2 1 2 1 2 3v2l-1-1h-1l-1 1c0 1 0 0 1 1v1h-1l-1 1c0-1-1-2-1-2 1-1 1-3 2-3l1-1v-2z" class="x"></path><path d="M447 483c2 0 3 1 4 1v1 1h-1l-4 4-1-1v-1c2 0 2 0 3-1l-1-1h0-1c0-2 1-2 1-3z" class="Y"></path><path d="M451 485c1 1 3 2 3 3 1 1 0 2 1 4v1h1l1 1c0 1-1 2 0 3h-1-1c-1 0-1 0-2-1v-2h-1l-1-2 1 1v-2c0-2 0-2-2-3v-2h1v-1z" class="AO"></path><path d="M458 497h0v-2c1 0 1 1 2 2h0l1 1 1-1v-1l1 1-1 2h1l1 1c-1 1 0 1 0 2 1 0 1 1 2 2 0 1-1 2-1 2-1 1-1 1-3 2v-2l-1-2c-1 0-2 1-2 2v2h-1c-1 1-1 0-1 0l-2-2h1 1v-3h0v-1h-1-3l1-1 1-1h0c1 0 2-1 2-1v-1l1-1z" class="Z"></path><path d="M459 503s1 0 2 1c-1 0-2 1-2 2-1-2-1-2 0-3z" class="AG"></path><path d="M461 498l1-1v-1l1 1-1 2h1l1 1c-1 1 0 1 0 2 1 0 1 1 2 2 0 1-1 2-1 2-1 1-1 1-3 2v-2l-1-2c-1-1-2-1-2-1 0-2 1-2 2-3v-2z" class="e"></path><path d="M461 500c0 2 0 3 2 4l-1 2-1-2c-1-1-2-1-2-1 0-2 1-2 2-3z" class="X"></path><path d="M630 418c1-1 1-1 2-1l3 4c0 1 0 2 1 3-1 2-1 4-1 6h0l-1 1c-2 0-3 0-5 1v1c0 2 1 4 0 6 1 2 1 2 2 3v1c-1 1-2 3-3 5 0 2 0 4-1 5l-1 2v1c-1 2-1 3-2 4h-1c-1 0-2 0-3 1-2 2-4 3-6 4l-1-1c-1 0-2 0-3-1 0-1 0-1 1-2h0c-5-2-5-7-6-11l-3-6 3-5 1-1 2-4c1-2 2-3 4-3 1-1 2-1 3-2l2-4h0c3-2 6-3 9-4v-1c2-1 2-1 4-1v-1h0z" class="T"></path><path d="M626 420l1 1v1c0 3 0 3-2 5l-1-1 1-1-2 2-3 3c-1-1-1-2-1-3l-1-1-1 2c-1 0-1 1-2 1l2-4h0c3-2 6-3 9-4v-1z" class="An"></path><path d="M623 427v-1c1-2 2-3 4-4 0 3 0 3-2 5l-1-1 1-1-2 2z" class="Af"></path><path d="M630 418c1-1 1-1 2-1l3 4c0 1 0 2 1 3-1 2-1 4-1 6h0l-1 1c-2 0-3 0-5 1v1c0 2 1 4 0 6 1 2 1 2 2 3v1c-1 1-2 3-3 5 0 2 0 4-1 5l-1 2v1c-1 2-1 3-2 4h-1c-1 0-2 0-3 1-2 2-4 3-6 4l-1-1c-1 0-2 0-3-1 0-1 0-1 1-2h0c1-1 1-1 2-1l1 1c0 1 0 1 1 2l1-3h0v2h2c1-1 2-2 2-3h-1v-1c0-1 1-2 2-2v-1-1l1-1c-1-1-1-2-1-3l1-1c0-2-1-3-2-4 0-1 0-2 1-3h3 1c0-2 0-5-1-6v-1c-1-1-1-3 0-4-1-1-1-1-1-2l1-1h1c-1-1 0-1-1-2h0l1 1c2-2 2-2 2-5v-1l-1-1c2-1 2-1 4-1v-1h0z" class="Ah"></path><path d="M611 461c1 1 1 1 2 3-1 0-2 0-3-1 0-1 0-1 1-2z" class="AJ"></path><path d="M626 438c1 0 2 1 3 2v1c-1 1-2 1-3 2h0v-2-3z" class="Af"></path><path d="M626 443h0c0 1 0 2-1 3 0 2 0 2-1 4l-1 1v-6c1-1 1-1 3-2z" class="AW"></path><path d="M625 430c1 1 2 1 3 2l1 1c0 2 1 4 0 6v1c-1-1-2-2-3-2v-3c0-1-1-1-1-2-1-1 0-2 0-3z" class="Ap"></path><path d="M629 439c1 2 1 2 2 3v1c-1 1-2 3-3 5 0 2 0 4-1 5l-1 2v1c-1 2-1 3-2 4h-1c-1 0-2 0-3 1 2-3 3-7 3-10l1-1c1-2 1-2 1-4 1-1 1-2 1-3 1-1 2-1 3-2v-1-1z" class="Ad"></path><path d="M626 455s-1-1-2-1c1-2 1-2 3-2v1l-1 2z" class="AM"></path><path d="M630 418c1-1 1-1 2-1l3 4c0 1 0 2 1 3-1 2-1 4-1 6h0l-1 1c-2 0-3 0-5 1v1l-1-1c-1-1-2-1-3-2l-1-1h0c1 0 1-1 2-1h0l-1-1c2-2 2-2 2-5v-1l-1-1c2-1 2-1 4-1v-1h0z" class="v"></path><path d="M631 419l1 2h0-1c0 1 1 2 2 3v3h-1l-2-1c1-1-1-2-1-2v-3l2-2z" class="Am"></path><path d="M630 419h1l-2 2v3s2 1 1 2c-1 2-1 2-1 4h3l1-1s1 1 2 1l-1 1c-2 0-3 0-5 1v1l-1-1c-1-1-2-1-3-2l-1-1h0c1 0 1-1 2-1h0l-1-1c2-2 2-2 2-5v-1l-1-1c2-1 2-1 4-1z" class="AI"></path><path d="M627 421c1 2 1 4 1 6l-2 1-1-1c2-2 2-2 2-5v-1z" class="Ao"></path><path d="M628 427c0 2 0 3 1 5v1l-1-1c-1-1-2-1-3-2l-1-1h0c1 0 1-1 2-1h0l2-1z" class="An"></path><path d="M483 572l1 1c1 1 1 1 2 1h1 1c1 0 2 1 3 1 1 1 2 2 2 3l1 2h3l-1-1h2 0c1 1 3 2 4 3v1h1c-1-1-2-2-2-3h1c1 1 2 1 3 2s2 3 4 4h2 1l1-1 2-2 1 1v1c0 1 0 1 1 1s1-1 2-1l1 1c-1 1-1 2-2 3-2 1-2 2-3 3 0 1 0 2 1 3v1c1 2 2 4 4 6h-1c-1 1-1 0 0 2h-2l-1-1v2l-3-2h-1l-1 1-1 1c1 1 1 2 2 2 0 1 1 1 1 2l-1 2c0-1 0-1-1-2l-1 1-3-2-1 1-3-2-1 1-1-1-1 1 1 1-1 1h-1l1 4c0 1-1 1-2 2l-2-3v-1l-2 1h-1l-2-3-2-5-1-4c-1-1-2-6-3-7v-1l-4-12s1 0 2-1l1 1h2l-5-5h0c-1-1-1-1-2-1l1-2c1 0 2 0 3-1z" class="q"></path><path d="M496 600c1 1 2 1 1 2l-1 1-3-3h0 3z" class="c"></path><path d="M493 600l3 3 2 3-2 1-2-3h-1-1c0-1-1-2 0-3l1-1z" class="Q"></path><path d="M485 593c0-1 0-1-1-2h0c1-1 1-1 2-1 0 1 0 2 1 3v-1h0c2 2 5 4 7 5l2 3h-3 0c-2-2-3-2-4-4 0-1 0-1-1-2h0v1h-1l-1-1h-1v-1z" class="W"></path><path d="M493 604h1l2 3c1 1 1 1 2 1s1 1 2 2h-1l1 4c0 1-1 1-2 2l-2-3v-1h-1c0-3-1-4-2-6v-2z" class="Y"></path><path d="M496 613l3-3 1 4c0 1-1 1-2 2l-2-3z" class="k"></path><path d="M481 581s1 0 2-1l1 1h2c1 1 1 2 2 3 0 1 0 3 1 4v3h-1l-1 1h0v1c-1-1-1-2-1-3-1 0-1 0-2 1h0c1 1 1 1 1 2l-4-12z" class="AA"></path><path d="M486 590c0-1 0-1-1-2l1-1 3 1v3h-1l-1 1h0v1c-1-1-1-2-1-3z" class="AS"></path><path d="M485 594h1l1 1h1v-1h0c1 1 1 1 1 2 1 2 2 2 4 4h0 0l-1 1c-1 1 0 2 0 3h1v2c1 2 2 3 2 6h1l-2 1h-1l-2-3-2-5-1-4c-1-1-2-6-3-7z" class="AH"></path><path d="M493 604v2 3h-1l-1-2 1-3h1z" class="AG"></path><path d="M489 602l2-1c0-2-2-2-2-3v-2c1 2 2 2 4 4h0 0l-1 1c-1 1 0 2 0 3l-1 3c-1-2-1-3-2-5z" class="AO"></path><path d="M485 594h1l1 1h1v-1h0c1 1 1 1 1 2v2c0 1 2 1 2 3l-2 1-1-1c-1-1-2-6-3-7z" class="x"></path><path d="M483 572l1 1c1 1 1 1 2 1h1 1c1 0 2 1 3 1 1 1 2 2 2 3l1 2c1 2 2 5 2 7h0v1 1c0 1 0 2 1 3l-1 1c0 1 0 2-1 2-4-1-3-5-4-7l-1-1c0-1-1-2-2-3s-1-2-2-3l-5-5h0c-1-1-1-1-2-1l1-2c1 0 2 0 3-1z" class="Q"></path><path d="M490 582l1-2 1 2v3c-1-1-2-2-2-3z" class="Z"></path><path d="M485 578h2 1c0 1 0 1 1 1h1l1 1-1 2-2-1-3-3z" class="u"></path><path d="M493 586c1 0 2 0 3 1h0v1 1c0 1 0 2 1 3l-1 1h-1v1h-1c0-3-1-5-1-8z" class="a"></path><path d="M483 572l1 1c1 1 1 1 2 1h1 1c1 0 2 1 3 1 1 1 2 2 2 3l1 2c1 2 2 5 2 7-1-1-2-1-3-1l-1-1v-3l-1-2-1-1h-1c-1 0-1 0-1-1h-1-2l-4-2h0c-1-1-1-1-2-1l1-2c1 0 2 0 3-1z" class="AI"></path><path d="M483 572l1 1v2h-2l-1 1c-1-1-1-1-2-1l1-2c1 0 2 0 3-1z" class="Z"></path><path d="M488 574c1 0 2 1 3 1 1 1 2 2 2 3l1 2c1 2 2 5 2 7-1-1-2-1-3-1l-1-1v-3c1 0 0 0 1 1 0 1 0 0 1 1v-1c0-1 0-2-1-2-1-1-1-1-1-2h0l-1-1h-1v-3h-1l-1-1z" class="AC"></path><path d="M496 593l1-1c-1-1-1-2-1-3v-1c2 1 4 3 6 4 1 0 1 0 2 1l2 1c0 1 0 2 1 3l2 1c2 2 2 4 3 5l-1 1-1 1c1 1 1 2 2 2 0 1 1 1 1 2l-1 2c0-1 0-1-1-2l-1 1-3-2-1 1-3-2h-1c-1-1-2-1-3-2h0l1-1c-1-3-2-6-5-9 1 0 1-1 1-2z" class="m"></path><path d="M509 602l2 2-1 1c0 1 0 1-1 1l-1-1c0-2 0-2 1-3z" class="q"></path><path d="M509 598c2 2 2 4 3 5l-1 1-2-2h-1c0-2 1-3 1-4z" class="Q"></path><path d="M499 605l3-1 1 2 2-2c2 0 2 2 3 3l-1 1-1 1-3-2h-1c-1-1-2-1-3-2z" class="AW"></path><path d="M496 593l1-1c-1-1-1-2-1-3v-1c2 1 4 3 6 4 1 0 1 0 2 1l2 1c0 1 0 2 1 3v4 2h-1c-1-1 0-1-1-1s-1-1-2-1v-5c-1 0-1 0-1 1l-1-1-2-3c0 3 1 6 2 9v1l-1 1c-1-3-2-6-5-9 1 0 1-1 1-2z" class="e"></path><path d="M506 594c0 1 0 2 1 3v4h0l-3-2v-3l2-2z" class="AG"></path><path d="M504 596l2 2-1 1h-1v-3z" class="m"></path><path d="M498 579h0c1 1 3 2 4 3v1h1c-1-1-2-2-2-3h1c1 1 2 1 3 2s2 3 4 4h2 1l1-1 2-2 1 1v1c0 1 0 1 1 1s1-1 2-1l1 1c-1 1-1 2-2 3-2 1-2 2-3 3 0 1 0 2 1 3v1c1 2 2 4 4 6h-1c-1 1-1 0 0 2h-2l-1-1v2l-3-2h-1c-1-1-1-3-3-5l-2-1c-1-1-1-2-1-3l-2-1c-1-1-1-1-2-1-2-1-4-3-6-4v-1h0c0-2-1-5-2-7h3l-1-1h2z" class="w"></path><path d="M510 598c-2-2-3-5-5-7l2-3v1h2l1 2v1c0 1 0 2 1 3-1 1-1 1 0 2l-1 1z" class="X"></path><path d="M507 589h2l1 2v1h-1c-1-1-2-1-2-3z" class="AI"></path><path d="M511 595s1 1 2 1h1l1 1 1-1c1 2 2 4 4 6h-1c-1 1-1 0 0 2h-2l-1-1v2l-3-2v-2c0-1-2-2-3-3l1-1c-1-1-1-1 0-2z" class="AW"></path><path d="M511 595s1 1 2 1c1 2 1 3 1 5l1 1h2l-1 1v2l-3-2v-2c0-1-2-2-3-3l1-1c-1-1-1-1 0-2z" class="Q"></path><path d="M515 583l1 1v1c0 1 0 1 1 1s1-1 2-1l1 1c-1 1-1 2-2 3-2 1-2 2-3 3 0 1 0 2 1 3v1l-1 1-1-1h-1c-1 0-2-1-2-1-1-1-1-2-1-3v-1l-1-2v-3h2 1l1-1 2-2z" class="AJ"></path><path d="M509 586h2c1 1 1 1 2 1v1c-1 1-1 0-2 2v2l1 1 1-2c1 1 1 1 2 1 0 1 0 2 1 3v1l-1 1-1-1h-1c-1 0-2-1-2-1-1-1-1-2-1-3v-1l-1-2v-3z" class="Ad"></path><path d="M509 586h2c1 1 1 1 2 1-2 1-2 2-3 4l-1-2v-3z" class="AM"></path><path d="M498 579h0c1 1 3 2 4 3v1h1c-1-1-2-2-2-3h1c1 1 2 1 3 2s2 3 4 4v3h-2v-1l-1-1-3 1 1 3h-1l-1 1c-2-1-4-3-6-4v-1h0c0-2-1-5-2-7h3l-1-1h2z" class="AC"></path><path d="M497 580c1 2 2 3 3 5l3 3 1 3h-1l-1 1c-2-1-4-3-6-4v-1h0c0-2-1-5-2-7h3z" class="X"></path><path d="M496 587l2-1c1 1 3 2 3 3 1 0 1 1 2 2l-1 1c-2-1-4-3-6-4v-1z" class="k"></path><path d="M560 166v-2l1-1 1 2h3l1 1h0l2 1 1-1c3 0 4 3 7 3 1 2 2 4 3 5v2l2 4c0-1 1-1 2-2 0-1 1-2 2-3l3 1 3 3c1 0 2 1 2 2l1 1c1 1 2 2 3 4 0 1 0 2 1 3v2c-1 1-1 1-2 1s-1 1-2 1c-1-2-2-3-4-4-2 1-2 0-3 0s-2 1-2 1l-2-1-1 1v1c0 2 1 4 1 6h0v1 2c0 2 1 3 1 4l2 2c-1 1-1 1-3 1l-2 1-3-1c-4-1-6-4-9-6l-2 1v-1c1-2 1-2 1-3l-1-1h-1 2v-4l1-1h0c0-2 1-2 1-3-1-2-1-3-2-4h0c0-1 0-1-1-2s0-1-1-2l-1 1-1-1h-2 0c0 1 1 1 2 2h1c1 1 2 2 2 3v1l-3-3h-1c-1-1-2-1-3-2l1 1-2 2v-1l1-1h-2c-1-1-1-1-1-2l-2 1h0l-2-1h-1v2l1 1-1 2c-1-2-1-5-1-7l-2-2 2-1 2-1h0c1-1 2-3 3-4v-2-2h1 2l1-1z" class="Aq"></path><path d="M579 176l2 4v2h1c0 1-1 1-2 2l-1-3c0-2 0-3-1-4l1-1z" class="Ar"></path><path d="M563 169l4 1c3 1 9 3 11 6v1c1 1 1 2 1 4l-3-2v-1c0-1 0-1-1-2h-1l-2-1v-1c-2 0-3-1-5-1v-1h-1-1 0-1c-1 0-3 1-4 0h0l3-3z" class="Ak"></path><path d="M560 166v-2l1-1 1 2h3l1 1h0l2 1 1-1c3 0 4 3 7 3 1 2 2 4 3 5v2l-1 1v-1c-2-3-8-5-11-6l-4-1h-1l-1-1v-2h-1z" class="q"></path><path d="M561 168c2 0 3 0 5-1 0 1 1 2 2 3v-2h2c1 1 0 1 1 1 2 0 3 2 5 3 1 1 2 2 3 2v2l-1 1v-1c-2-3-8-5-11-6l-4-1h-1l-1-1z" class="Z"></path><path d="M560 166h1v2l1 1h1l-3 3h0c1 1 3 0 4 0l1 1c2 0 2 0 4 1v1l1 2-2 2-1-1-1 2-1 1-1-1-2-1h-1-2 0l-2-1h-3-1v-3h0c1-1 2-3 3-4v-2-2h1 2l1-1z" class="Ao"></path><path d="M567 177c0-1 0 0 1-1l1 1v-2l1 2-2 2-1-1h0v-1z" class="N"></path><path d="M557 178h3c0-1 0-1 1-2 0-1 0 0 1-1 1 1 2 1 2 2h3v1h0l-1 2-1 1-1-1-2-1h-1-2 0l-2-1z" class="Af"></path><path d="M564 180l1-2h2 0l-1 2-1 1-1-1z" class="T"></path><path d="M560 166h1v2l1 1h1l-3 3h0l-3 3c-1 1-2 2-3 2v1h-1v-3h0c1-1 2-3 3-4v-2-2h1 2l1-1z" class="J"></path><path d="M556 167h1c1 1 1 1 2 3l-1 1c-2 1-3 3-5 4 1-1 2-3 3-4v-2-2z" class="u"></path><path d="M560 166h1v2l1 1h1l-3 3-2-1 1-1c-1-2-1-2-2-3h2l1-1z" class="AI"></path><path d="M585 175l3 1 3 3c1 0 2 1 2 2l1 1c1 1 2 2 3 4 0 1 0 2 1 3v2c-1 1-1 1-2 1s-1 1-2 1c-1-2-2-3-4-4-2 1-2 0-3 0s-2 1-2 1l-2-1-1 1c-1-1-1-2-1-4l1 2c2 0 4 0 6-1l1-1-2-1v-1c-2-2-3-3-4-5v-1c0-1 1-2 2-3z" class="a"></path><path d="M587 184h1v-1c1 0 1 0 2 2l-1 1-2-1v-1z" class="Am"></path><path d="M591 183l-1-1v-1h3l1 1-3 1z" class="AH"></path><path d="M585 175l3 1 3 3c-3 1-5 0-8 0v-1c0-1 1-2 2-3z" class="i"></path><path d="M594 182c1 1 2 2 3 4 0 1 0 2 1 3v2c-1 1-1 1-2 1-1-1-1-1-2-1v-2c-1-1-2-1-2-2l1-1c-1-1 0-1-1-1l-1-2 3-1z" class="Q"></path><path d="M553 175v3h1 3l2 1h0 2 1l2 1 1 1 1-1 1-2 1 1 2-2c2 1 3 2 5 2 0 1-1 1-1 2 1 0 1 1 1 2v1l1-1 2 1v1c-1 1-1 1 0 1l-2 2h2v1h-1c-1 0-1 1-1 1h-2l-1 1h0c-2 1-3 3-5 4l3-6c0-2-2-3-3-4h0c0-1 0-1-1-2s0-1-1-2l-1 1-1-1h-2 0c0 1 1 1 2 2h1c1 1 2 2 2 3v1l-3-3h-1c-1-1-2-1-3-2l1 1-2 2v-1l1-1h-2c-1-1-1-1-1-2l-2 1h0l-2-1h-1v2l1 1-1 2c-1-2-1-5-1-7l-2-2 2-1 2-1z" class="AV"></path><path d="M553 175v3l-2 1-2-2 2-1 2-1z" class="AI"></path><path d="M570 177c2 1 3 2 5 2 0 1-1 1-1 2-1 1-2 1-3 1l-1-1c-1 0-2 0-4-1l1-2 1 1 2-2z" class="P"></path><path d="M570 181v-1l1-1h0l1 1c-1 1-1 1-2 1z" class="K"></path><path d="M568 185c1 1 3 2 3 4l-3 6c2-1 3-3 5-4h0 4 5c0 2 1 4 1 6h0v1 2c0 2 1 3 1 4l2 2c-1 1-1 1-3 1l-2 1-3-1c-4-1-6-4-9-6l-2 1v-1c1-2 1-2 1-3l-1-1h-1 2v-4l1-1h0c0-2 1-2 1-3-1-2-1-3-2-4z" class="D"></path><path d="M577 191h5c0 2 1 4 1 6-1-1-2-2-2-3-1-2-2-2-4-3z" class="S"></path><path d="M652 334v4l2-3c0 6 0 12-1 18v4l-1 5c1 1 1 2 2 2l-1 2-4 20c0 1 0 2-1 3l-2 8h-1l-7 26c-1 2-2 6-3 7 0-2 0-4 1-6-1-1-1-2-1-3l-3-4c-1 0-1 0-2 1v-2h0c0-2 1-5 2-6v-6-2l2-2c1-1 1-2 3-3h1l1-1c0-1-1-2-2-3-1-2-1-3-1-5-1 0-1-1-2-2v-1h-1v-2-1l2-2v-1h-1v-1c1 0 2 0 3-1s2-3 3-5h-1v-1c1-2 1-2 1-3v-3c-1-1-1-1-2-1 1-1 1-3 1-4 1 1 2 1 3 1v-2-3c1-1 1-2 0-3v-1l1-2v-2h-1-2c-1-1-1-1-2-1h0l-1-2v-1c1-1 2-1 2-3l1 1c1-1 1-1 3-1 1-2 5-2 7-3l1-1 1-3z" class="Aj"></path><path d="M636 414c1 0 2-1 3-1l-3 11c-1-1-1-2-1-3l1-7z" class="J"></path><path d="M644 386v6 2 3c0 2-2 4-2 6h0c-1-1-1-2-1-3-1-1 0-1 0-2l-1-2v-1l1-1c1 0 1 0 2-1v-2l1-5z" class="k"></path><path d="M652 362c1 1 1 2 2 2l-1 2-4 20c0 1 0 2-1 3l-2 8h-1l7-35z" class="p"></path><path d="M642 385h0c1-2 2-3 3-4 0 2 0 4-1 5l-1 5v2c-1 1-1 1-2 1l-1 1v1h-1c0-1-1-2-2-3-1-2-1-3-1-5h2l4-3z" class="AH"></path><path d="M639 392c-1-1-1-1-1-2l1-1 2 2-2 1z" class="k"></path><path d="M641 391h1 1v2c-1 1-1 1-2 1l-1 1c0-1 0-1-1-3l2-1z" class="AB"></path><path d="M651 343l1-1c0 7-1 14-3 21v1c-1 0-2 0-2-2h-2c-1-1-1-2-1-2l-1-1h-1v-3c1-1 1-2 0-3v-1l1-2 1 3c1 0 1 0 2-1l1-1 1-1v-2-1-2c1 0 2-1 3-2z" class="AG"></path><path d="M648 345c1 0 2-1 3-2l-1 6h0l-1-1-1-1v-2z" class="k"></path><path d="M642 356h1c1-1 1-1 2-1 1 1 0 4 1 5 0 1 2 2 3 3v1c-1 0-2 0-2-2h-2c-1-1-1-2-1-2l-1-1h-1v-3z" class="a"></path><path d="M648 347l1 1 1 1h0v5c0 1 0 2-1 3l-2 1-1-1v-2l-2-2c1 0 1 0 2-1l1-1 1-1v-2-1z" class="Y"></path><path d="M648 347l1 1 1 1c-1 1 0 1-1 0l-1-1v-1z" class="c"></path><path d="M652 334v4c0 1-1 1 0 3v1l-1 1c-1 1-2 2-3 2v2 1 2l-1 1-1 1c-1 1-1 1-2 1l-1-3v-2h-1-2c-1-1-1-1-2-1h0l-1-2v-1c1-1 2-1 2-3l1 1c1-1 1-1 3-1 1-2 5-2 7-3l1-1 1-3z" class="k"></path><path d="M646 348l2 2-1 1-1 1v-4z" class="x"></path><path d="M646 348h0v-1c0-1 1-2 2-3v1 2 1 2l-2-2z" class="AH"></path><path d="M639 341l1 1c0 2 1 4 2 6h-2c-1-1-1-1-2-1h0l-1-2v-1c1-1 2-1 2-3z" class="J"></path><path d="M652 334v4c0 1-1 1 0 3v1l-1 1c-1 1-2 2-3 2v-1h-2c-1 0-2 1-2 2h-1l-1-1c0-2 0-2 1-4s5-2 7-3l1-1 1-3z" class="Al"></path><path d="M646 344l6-3v1l-1 1c-1 1-2 2-3 2v-1h-2z" class="W"></path><path d="M639 396h1l1 2c0 1-1 1 0 2 0 1 0 2 1 3h0c-1 4-1 7-3 10-1 0-2 1-3 1l-1 7-3-4c-1 0-1 0-2 1v-2h0c0-2 1-5 2-6v-6-2l2-2c1-1 1-2 3-3h1l1-1z" class="x"></path><path d="M640 407s-1 1-2 1c0 2 0 2-1 3l-1-1c-1-1-1-3-1-4 2-2 3-1 5-1v2z" class="AA"></path><path d="M639 396h1l1 2c0 1-1 1 0 2 0 1 0 2 1 3l-2 1-1-1h-2-1-1v-1l-1-1v-1c1-1 1-2 3-3h1l1-1z" class="w"></path><path d="M634 400h1l2-2c1 1 0 1 1 1 0 1 1 1 2 2v1l-1 1h-2-1-1v-1l-1-1v-1z" class="AO"></path><path d="M632 402l2 3v5c1 2 2 2 4 2 1-1 1-1 1-2l1-2v-1-2-1l2-1h0c-1 4-1 7-3 10-1 0-2 1-3 1l-1 7-3-4c-1 0-1 0-2 1v-2h0c0-2 1-5 2-6v-6-2z" class="AG"></path><path d="M632 417c2-2 2-3 4-4v1l-1 7-3-4z" class="AW"></path><path d="M642 359h1l1 1s0 1 1 2h2c0 2 1 2 2 2l-1 5-1 5c0 2 0 4-2 6v1c-1 1-2 2-3 4h0l-4 3h-2c-1 0-1-1-2-2v-1h-1v-2-1l2-2v-1h-1v-1c1 0 2 0 3-1s2-3 3-5h-1v-1c1-2 1-2 1-3v-3c-1-1-1-1-2-1 1-1 1-3 1-4 1 1 2 1 3 1v-2z" class="Y"></path><path d="M643 367l3-1c2 1 2 1 2 3l-1 5-4-7z" class="w"></path><path d="M642 359h1l1 1s0 1 1 2h2c0 2 1 2 2 2l-1 5c0-2 0-2-2-3l-3 1v-2l-1-4v-2z" class="Z"></path><path d="M642 359h1v1 1c1 1 1 1 1 3l-1 1-1-4v-2z" class="u"></path><path d="M643 371c1 1 1 1 1 3 1 2 1 4 1 6v1c-1 1-2 2-3 4h0 0c-2-2-1-5-1-7l1-1c0-3 1-3 1-6z" class="i"></path><g class="Z"><path d="M643 371c1 1 1 1 1 3 0 1 0 2 1 4-2 0-2-1-3-1 0-3 1-3 1-6z"></path><path d="M640 372l1-1h2c0 3-1 3-1 6l-1 1c0 2-1 5 1 7h0l-4 3h-2c-1 0-1-1-2-2v-1h-1v-2-1l2-2v-1h-1v-1c1 0 2 0 3-1s2-3 3-5z"></path></g><path d="M636 382c1 2 1 4 2 6h-2c-1 0-1-1-2-2v-1h-1l3-3z" class="q"></path><path d="M640 372l1-1h2c0 3-1 3-1 6l-1 1-3 1c-1 1-1 2-1 3h-1l-3 3v-2-1l2-2v-1h-1v-1c1 0 2 0 3-1s2-3 3-5z" class="J"></path><path d="M640 302l1-1 2 2h2l2 2v1-1-1h1l3 13c1 1 2 2 3 4v10 2 2l-2 3v-4l-1 3-1 1c-2 1-6 1-7 3-2 0-2 0-3 1l-1-1c0 2-1 2-2 3v1c-1-1-1-1-3-2h-2v3h0c-1-1-1-1-1-2h-8 0c-3 0-5-1-8-2h0l1-1h1l1-1h0v-1h-1-1c0-1 0-1 1-2v-1c-1-1-3-3-3-4-1-1 0-3 1-4s1-1 1-2c1-1 1-1 2-1 1-1 2-2 2-3l2 1h1l1-1h4c0-1-1-2-1-2v-1l2-1h1c1-1 1-1 2-1h0l1-1h1c0 1 1 1 1 1v-1-4l1-2 2-1h0 0c0-2 0-4 1-6l1-1z" class="AO"></path><path d="M645 309l3 1v1 3h-1c-1-1-1-2-2-3h-1l1-2z" class="Y"></path><path d="M639 311l4 3c1 0 2 1 2 3l3 3h1v1c1 1 0 1 1 1v1c-2 1-2 1-3 1 0-2-1-3-2-4l-1 2-1-1h0l-1-3v-1c-1-1-1-2-3-3h0v-3z" class="J"></path><path d="M642 318c1 0 3 1 3 2l-1 2-1-1h0l-1-3z" class="n"></path><path d="M635 312l1-2 2-1h0c1 1 1 1 1 2v3h0c2 1 2 2 3 3v1l1 3c-2 0-2 1-4 0v-3l-3-2h-1v-4z" class="i"></path><path d="M635 312l1-2 2-1c0 2-1 3 0 5l-2 2h0-1v-4z" class="u"></path><path d="M639 318v-2c1 0 1 1 2 1h1v1l1 3c-2 0-2 1-4 0v-3z" class="Y"></path><path d="M647 306v-1-1h1l3 13c0 2 1 7-1 9l-1 2-1 1c-1-1-1-2-2-3h-1c1-1 1-2 2-2s1 0 3-1v-1c-1 0 0 0-1-1v-1c1 0 0 0 1-1l-2-2c1-2 1-4 0-6v-1c0-2 0-2-1-4z" class="Z"></path><path d="M646 326c2-1 3 0 4 0l-1 2-1 1c-1-1-1-2-2-3z" class="AG"></path><path d="M651 317c1 1 2 2 3 4v10 2 2l-2 3v-4l-1-1v-2c-1-1-2-1-3-2l1-1 1-2c2-2 1-7 1-9z" class="V"></path><path d="M648 329l1-1h1c0 1 1 1 1 1 1 1 1 1 1 3l2 1v2l-2 3v-4l-1-1v-2c-1-1-2-1-3-2z" class="s"></path><path d="M639 321c2 1 2 0 4 0h0l1 1 1-2c1 1 2 2 2 4-1 0-1 1-2 2s-2 2-3 4c-1 1-1 1 0 2l-2 1h-1 0l1-2v-2l-2-2v-1l1-5z" class="x"></path><path d="M640 328v-3h1l1 2h0l-2 1z" class="c"></path><path d="M644 322v4h-1v-1-4l1 1z" class="X"></path><path d="M639 321c2 1 2 0 4 0h0v4h-2-1v3 1l-2-2v-1l1-5z" class="AO"></path><path d="M640 302l1-1 2 2h2l2 2v1c1 2 1 2 1 4l-3-1-1 2c-2 0-3-1-4-1-1-3-1-4-1-6v-1l1-1z" class="W"></path><path d="M640 302l1-1 2 2h2l2 2v1c1 2 1 2 1 4l-3-1 2-2-1-1-1-1c-1 0-2 0-4-1h-2v-1l1-1z" class="x"></path><path d="M645 326h1c1 1 1 2 2 3s2 1 3 2v2l1 1-1 3-1 1c-3 0-4-1-6-3l1-1c0-1-1-2-1-3h0-1l-1-1c1-2 2-3 3-4z" class="J"></path><path d="M644 331c0-2 0-2 2-2 0 2 0 3 2 5v1l-1 1-1-2h-1c0-1-1-2-1-3h0z" class="AW"></path><path d="M648 334c1-1 1-2 3-3v2l1 1-1 3-1 1c-3 0-4-1-6-3l1-1h1l1 2 1-1v-1z" class="J"></path><path d="M648 334c1-1 1-2 3-3v2l-2 3h-2l1-1v-1z" class="a"></path><path d="M632 317l1-1h1c0 1 1 1 1 1v-1h1l3 2v3l-1 5v1l2 2v2l-1-1c0 1 0 1-2 1-1 1-1 1-2 1h0-1c-1 1-1 1-2 3 0-1 0-2-1-3 0-1-1-1-1-2v-2h0v-1l1-1h1l-1-2c1 0 1 0 2-1h-1c-1-2-3-1-4-1 0-1-1-2-1-2v-1l2-1h1c1-1 1-1 2-1h0z" class="u"></path><path d="M635 332v-1c1-1 2-2 3-2l1 1c0 1 0 1-2 1-1 1-1 1-2 1z" class="AM"></path><path d="M632 317l1-1h1c0 1 1 1 1 1v-1h1l3 2v3l-1 5v1c-3-3-3 0-5 0-1-2 1-2 2-4v-1c0-3-1-3-3-5z" class="w"></path><path d="M636 316l3 2v3l-1 5v-4l-1-3-2-2v-1h1z" class="v"></path><path d="M632 317l1-1h1c0 1 1 1 1 1l2 2c0 1 0 3-1 4-1 0 0 0-1-1 0-3-1-3-3-5z" class="AW"></path><path d="M642 332c-1-1-1-1 0-2l1 1h1 0c0 1 1 2 1 3l-1 1c2 2 3 3 6 3-2 1-6 1-7 3-2 0-2 0-3 1l-1-1c0 2-1 2-2 3v1c-1-1-1-1-3-2h-2v3h0c-1-1-1-1-1-2h-8v-1l2-3v-2c2-2 2-3 3-5l2-3c0 1 1 1 1 2 1 1 1 2 1 3 1-2 1-2 2-3h1 0c1 0 1 0 2-1 2 0 2 0 2-1l1 1-1 2h0 1l2-1z" class="w"></path><path d="M642 332c-1-1-1-1 0-2l1 1h1 0c0 3 0 3-2 5v-4z" class="AG"></path><path d="M633 338l1-1c1 1 1 1 2 1-1 2-1 3-2 4h0c-1-1-1-2-1-4z" class="AW"></path><path d="M642 332v4c-1 0-2 1-3 1l-2-2 2-2h0 1l2-1z" class="k"></path><path d="M644 335c2 2 3 3 6 3-2 1-6 1-7 3-2 0-2 0-3 1l-1-1c0-1 0-2 2-2 0 1 0 0 1 1 0-1 1-1 1-2s0-2 1-2v-1z" class="Z"></path><path d="M632 335c1-2 1-2 2-3h1c0 3 1 5 3 7l-1 1h-1v-2c-1 0-1 0-2-1l-1 1-2 2-1 1h0l-1-1c2-1 3-3 3-5z" class="Q"></path><path d="M628 333l2-3c0 1 1 1 1 2 1 1 1 2 1 3 0 2-1 4-3 5l1 1h0l1-1 2-2c0 2 0 3 1 4h0v1h-2v3h0c-1-1-1-1-1-2h-8v-1l2-3v-2c2-2 2-3 3-5z" class="a"></path><path d="M625 338c2 1 0 3 2 4h7 0v1h-2v3h0c-1-1-1-1-1-2h-8v-1l2-3v-2z" class="Q"></path><path d="M628 322c1 0 3-1 4 1h1c-1 1-1 1-2 1l1 2h-1l-1 1v1h0v2l-2 3c-1 2-1 3-3 5v2l-2 3v1h0c-3 0-5-1-8-2h0l1-1h1l1-1h0v-1h-1-1c0-1 0-1 1-2v-1c-1-1-3-3-3-4-1-1 0-3 1-4s1-1 1-2c1-1 1-1 2-1 1-1 2-2 2-3l2 1h1l1-1h4z" class="An"></path><path d="M616 326c1-1 1-1 2-1 1 1 1 2 1 3h0l1 2c0 1-1 2-1 3v1h-1l-1-1 1-5-1-2h-1z" class="D"></path><path d="M617 337l2-1c0 1 0 2 1 3 0 0 1 0 1 1 1 0 1 1 1 1h1c0-1-1-3 1-4l1 1v2l-2 3v1h0c-3 0-5-1-8-2h0l1-1h1l1-1h0v-1h-1-1c0-1 0-1 1-2z" class="J"></path><path d="M628 322c1 0 3-1 4 1h1c-1 1-1 1-2 1l1 2h-1l-1 1v1h0v2l-2 3-1-1c-1 1-2 2-2 3h0-1l-1-1-1-1h-1v-3h-1l-1-2h0c0-1 0-2-1-3 1-1 2-2 2-3l2 1h1l1-1h4z" class="G"></path><path d="M623 323l1-1 2 3-1 1h-1-2 0v-3h1z" class="AV"></path><path d="M622 326h2 1l3 3c-1 0-1 1-1 1l-1-1c-1-1-2-1-3-1l-1-1v-1z" class="H"></path><path d="M622 326h2l1 1c-1 1-1 1-2 1l-1-1v-1z" class="E"></path><path d="M620 322l2 1v3h0v1l1 1c1 0 2 0 3 1l-1 1c-1 0 0 0-1 1h-1v-1h-2-1l-1-2h0c0-1 0-2-1-3 1-1 2-2 2-3z" class="C"></path><path d="M620 322l2 1v3c-1 1-2 1-3 2h0c0-1 0-2-1-3 1-1 2-2 2-3z" class="T"></path><path d="M500 610l1-1-1-1 1-1 1 1 1-1 3 2 1-1 3 2 1-1c1 1 1 1 1 2l1-2 3 2-1 1v1h1c1 1 3 3 3 4l1 1v-2l1 1h1l1-1c2 3 0 0 1 2 0 1 2 2 3 3l3 3-1 1 1 1v-1c2 1 2 2 3 3l2 2c1 0 2 0 3-1h0l2-2c1-2 1-3 3-4 0 2 1 3 1 5 2 2 3 2 5 3v1h2c1-2 1-2 1-4l2-1c0 1 1 2 0 4l1 1c-1 3-3 6-4 9l-2 4-2 1v2s-1 1-2 1c0 1-1 2-1 3l-1 1 2 2-1 3-3 6-1 1c-1-1-2-1-3-2v-2l-1-1h4l-3-3c-1 0-2 0-3-1h0l-3-3-3-3-2 2c0-2-1-3-2-4l-1-1v-2l-8-7c-1-1-1-2-1-2l-2-1-4-7-1-1h-2-1c-1-2-4-3-5-5-1-1-2-2-2-3l2-2-1-1c1-1 2-1 2-2l-1-4h1z" class="Q"></path><path d="M518 634l2-2h1v1c0 1-1 2-1 3-1 0-1-1-2-2z" class="e"></path><path d="M529 639l2-1c1 0 1 1 2 2v1h-2l-2-2z" class="Am"></path><path d="M526 646c0-2 0-1 1-2h1l2-2c1 1 2 1 3 2l-2 1v-1c-1 0-2 1-4 1l-1 1h0z" class="AG"></path><path d="M523 618l1 1h0v3 2h-3l2-6z" class="Y"></path><path d="M531 641c-3 0-3 0-5 2l-1-2c-1 1-1 1-2 1v-1c2-1 2-2 5-2h1 0l2 2z" class="w"></path><path d="M518 626l2 2 1 4h-1l-2 2-1-1 1-1-1-2 1-1h0c-2-1-2-1-3-2h0c2 0 2 0 3-1z" class="AC"></path><path d="M528 650c-1-1-2-2-3-4h1 0l2 2h3 0c1 2 1 3 1 4l-1 1-3-3z" class="e"></path><path d="M526 646l1-1c2 0 3-1 4-1v1l2-1c0 1 1 1 1 2h0c-1 1-1 2-2 2h-1 0-3l-2-2z" class="X"></path><path d="M511 626v-2l1-1 1 2v3l3 2c-1 1-2 2-4 3h0l-2-4c0-1 0-2 1-3z" class="W"></path><path d="M504 627v-2l1-1 2 2c1-1 2-1 3-2l1 2c-1 1-1 2-1 3l2 4h0l2 1h1v1l-1 1-2-1-4-7-1-1h-2-1z" class="e"></path><path d="M521 624h3l-1 1v1c1 1 1 1 1 2 0 2 1 3 2 6l3 2c-2 1-2 1-3 1h-2l-1-1c-1-1-1-1-1-2l-1-1v-1l-1-4s1-1 1-2c-1-1 0-1 0-2z" class="k"></path><path d="M522 627h1v2c-1 1 0 1-1 0v-2z" class="W"></path><path d="M525 625c1-1 0-2 2-3v3c1 0 1 0 1 1 0 2 1 2 2 4l3 3h0c-1 1-2 1-3 1h-4c-1-3-2-4-2-6 0-1 0-1-1-2v-1h2z" class="u"></path><path d="M523 625h2c0 2 0 2 1 3 1 3 2 4 4 6h-4c-1-3-2-4-2-6 0-1 0-1-1-2v-1z" class="n"></path><path d="M536 643h0l-1-1v-1l1-1v-1c-1 0 0 0-1-1l1-2c1 1 1 2 2 3l-1 1c1 1 1 1 2 1l1 1h1v1c0 1 1 2 2 4v1l2 1c0 1-1 2-1 3l-1 1 2 2-1 3-3 6-1 1c-1-1-2-1-3-2v-2l-1-1h4l-3-3c-1 0-2 0-3-1h0l-3-3 1-1c0-1 0-2-1-4h1c1 0 1-1 2-2h0l2-3z" class="a"></path><path d="M542 652v-1c0-2 1-2 1-3l2 1c0 1-1 2-1 3l-1 1-1-1z" class="J"></path><path d="M536 643c1 1 1 2 2 3h0c0 1 0 1-1 2h0 0l-1-1-2-1h0l2-3z" class="m"></path><path d="M536 660h4l1 1-1 2 1 1-1 1c-1-1-2-1-3-2v-2l-1-1z" class="AB"></path><path d="M531 648h1c1 0 1-1 2-2 1 2 1 3 1 5-1 1-1 1-3 1 0-1 0-2-1-4z" class="m"></path><path d="M536 643h0l-1-1v-1l1-1v-1c-1 0 0 0-1-1l1-2c1 1 1 2 2 3l-1 1c1 1 1 1 2 1l1 1c0 1 0 1-1 2l1 1 1 1-1 1v-1h-2 0c-1-1-1-2-2-3z" class="v"></path><path d="M544 658c-1 0-2-1-3-1s0-1-1-2l-2 2-1-1c-1-1-1-2-1-3 3-1 3-1 6-1l1 1 2 2-1 3z" class="c"></path><path d="M540 627c1-2 1-3 3-4 0 2 1 3 1 5 2 2 3 2 5 3v1h2c1-2 1-2 1-4l2-1c0 1 1 2 0 4l1 1c-1 3-3 6-4 9l-2 4-2 1v2s-1 1-2 1l-2-1v-1c-1-2-2-3-2-4v-1h-1l-1-1c-1 0-1 0-2-1l1-1c-1-1-1-2-2-3h1c1 0 2 1 2 2h1 1c-1-1-2-2-3-4-1 1-1 1-2 1-1-2-1-2-1-5 1 0 2 0 3-1h0l2-2z" class="AW"></path><path d="M540 627c1-2 1-3 3-4 0 2 1 3 1 5 2 2 3 2 5 3v1h-1l-3-2c-2 2-2 2-3 5v-2l-1-1c0-1 0-1 1-2h0l1-1c-1 0-2 0-2-1l-1-1z" class="AM"></path><path d="M542 635c1-3 1-3 3-5l3 2 2 1c-1 2-3 3-4 4-2 0-3-1-4-2z" class="G"></path><path d="M539 641v-1h4 3l1 1h2 2l-2 4-2 1v2s-1 1-2 1l-2-1v-1c-1-2-2-3-2-4v-1h-1l-1-1z" class="Z"></path><path d="M549 641h2l-2 4-2 1v-3l2-2z" class="Q"></path><path d="M500 610l1-1-1-1 1-1 1 1 1-1 3 2 1-1 3 2 1-1c1 1 1 1 1 2l1-2 3 2-1 1v1h1c1 1 3 3 3 4l1 1v-2l1 1h1l1 1-2 6c0 1-1 1 0 2 0 1-1 2-1 2l-2-2c-1 1-1 1-3 1-1-1-1-2-2-2l-1-2-1 1v2l-1-2c-1 1-2 1-3 2l-2-2-1 1v2c-1-2-4-3-5-5-1-1-2-2-2-3l2-2-1-1c1-1 2-1 2-2l-1-4h1z" class="AO"></path><path d="M506 609l1-1 3 2-1 3c-1 0-3-2-4-3l1-1z" class="Y"></path><path d="M506 614c1 0 1 2 1 2 1 1 2 0 2 1s1 2 1 3l-1 1-2-3c-1-1-1-1-3-1 0-1 2-2 2-3z" class="Z"></path><path d="M513 613s1 0 2-1v1c0 2 1 4 1 7v1h-1-1v1h-1v-4h1c-1-2 0-1 0-3-1 0-1-1-1-2z" class="X"></path><path d="M510 610l1-1c1 1 1 1 1 2l1-2 3 2-1 1c-1 1-2 1-2 1-1 1 0 1-1 1 0 1 0 1-1 1 0-1-1-1-2-2h0l1-3z" class="n"></path><path d="M510 620h1c1 1 1 1 1 2s1 1 2 2c0-1 0-1-1-2h1v-1h1 1l1 1 1 1v1c0 1 0 0 1 1l-1 1c-1 1-1 1-3 1-1-1-1-2-2-2l-1-2-1 1v2l-1-2c0-1 0-1-1-2v-1l1-1z" class="a"></path><path d="M515 613h1c1 1 3 3 3 4l1 1v-2l1 1h1l1 1-2 6c0 1-1 1 0 2 0 1-1 2-1 2l-2-2 1-1c-1-1-1 0-1-1v-1l-1-1-1-1v-1c0-3-1-5-1-7z" class="u"></path><path d="M521 617h1l1 1-2 6c0 1-1 1 0 2 0 1-1 2-1 2l-2-2 1-1c-1-1-1 0-1-1v-1l-1-1-1-1h3v1h1c0-2 0-3 1-5z" class="J"></path><path d="M500 610l1-1-1-1 1-1 1 1c0 1 1 2 2 3l2 3c0 1-2 2-2 3 2 0 2 0 3 1l2 3v1c1 1 1 1 1 2-1 1-2 1-3 2l-2-2-1 1v2c-1-2-4-3-5-5-1-1-2-2-2-3l2-2-1-1c1-1 2-1 2-2l-1-4h1z" class="AO"></path><path d="M504 617c2 0 2 0 3 1l2 3v1l-1 1v1h-1c0-1-1-1-2-2v-2h0l-1-3z" class="k"></path><path d="M497 619l2-2c1 3 3 6 6 7l-1 1v2c-1-2-4-3-5-5-1-1-2-2-2-3z" class="w"></path><path d="M500 610l1-1-1-1 1-1 1 1c0 1 1 2 2 3l2 3c0 1-2 2-2 3l1 3-1 1h0l-1-2c-1-1 0-1 0-2-1-2-2-3-3-3l-1-4h1z" class="J"></path><path d="M597 273l3-3c2-2 4-4 5-6 0 1 1 1 1 2s-1 2-1 2l1 4 2-4c1 0 1 1 3 1v-1h1l1 3h2 1 1 0l2 2c1 0 2 1 2 2 1 1 1 1 2 1v1l-2 2c0 1 0 2-1 3 1 1 1 2 2 2v1l-2 2c1 1 1 2 1 3 2 0 2 0 4-1h1 1l1 2h3c0 1 1 1 2 2v1h1 1 1 1v1c0 1 0 1-1 2l1 1h1c1 0 1 1 1 2v1l1 1-1 1c-1 2-1 4-1 6h0 0l-2 1-1 2v4 1s-1 0-1-1h-1l-1 1h0c-1 0-1 0-2 1h-1l-2 1v1s1 1 1 2h-4l-1 1h-1l-2-1c0 1-1 2-2 3-1 0-1 0-2 1 0 1 0 1-1 2h-2v-5l-3-1-6-3 1-1c-2-1-1-1-1-3l-1-1 1-1v-1l-1-1-3-1v-2h1l2-2c1-1 1-1 2-3 0-1 1-2 1-3l1-1c0-2 0-3 1-5l-3-2c-1-2-1-4-1-6-1-1-3-1-5-1l4-2-3-8-2-1-1-1z" class="T"></path><path d="M611 304c1 0 1 1 2 1h1v1 1h-1c-1-1-2-2-2-3z" class="S"></path><path d="M611 286h1l1 1h-1-3v-1h2z" class="AV"></path><path d="M607 286h2v1c1 1 1 2 2 4l-2 2h-1c1-3 0-4-1-7z" class="G"></path><path d="M614 281h1 1l2 1c-1 2-1 3-3 5h0-2l-1-1 1-1 2-2-1-2z" class="Af"></path><path d="M608 294h0v1c0 1 1 2 1 2 1 1 1 1 2 1 1 1 3 1 4 2 0 1 1 1 1 1l1 1h-2c-1 0-2-1-2-2h-2c-2-1-2-1-4-1v2l-1-1 1-1c0-2 0-3 1-5z" class="Ah"></path><path d="M610 281v-1c0-1 1-1 1-2 0-2-1-3 1-5l1 4 1 4h0l1 2-2 2-1 1h-1l1-4-1 1-1-2z" class="G"></path><path d="M612 282h1v3l-1 1h-1l1-4z" class="T"></path><path d="M604 286l2-2h0l1 2c1 3 2 4 1 7v1h0l-3-2c-1-2-1-4-1-6z" class="J"></path><path d="M603 283h1c0-1 0-1 1-2l-2-1 1-1c1 0 1 0 2 1s2 1 4 1l1 2 1-1-1 4h-2-2l-1-2h0l-2 2c-1-1-3-1-5-1l4-2z" class="AM"></path><path d="M611 283l1-1-1 4h-2-2l-1-2c2-1 3-1 5-1z" class="Ah"></path><path d="M611 269v-1h1l1 3-2 2h1c-2 2-1 3-1 5 0 1-1 1-1 2v1c-2 0-3 0-4-1h0l-1-6 1-1v-1l2-4c1 0 1 1 3 1z" class="C"></path><path d="M608 273h1l1 1-2 2c-1-1-1-1-1-2l1-1z" class="S"></path><path d="M611 269v-1h1l1 3-2 2c-1-1 0-3 0-4z" class="AV"></path><path d="M606 273h2l-1 1c0 1 0 1 1 2s1 2 0 3l-2 1-1-6 1-1z" class="Ah"></path><path d="M597 273l3-3c2-2 4-4 5-6 0 1 1 1 1 2s-1 2-1 2l1 4v1l-1 1 1 6h0c-1-1-1-1-2-1l-1 1 2 1c-1 1-1 1-1 2h-1l-3-8-2-1-1-1z" class="AM"></path><path d="M605 268l1 4v1l-1 1c0-1-1-1-1-1h-4l5-5z" class="a"></path><path d="M604 273s1 0 1 1l1 6h0c-1-1-1-1-2-1l-1 1 2 1c-1 1-1 1-1 2h-1l-3-8-2-1c1 0 3 0 4 1l2 1h0v-2-1z" class="Ad"></path><path d="M616 309v-1h3l1-2 1 3-1 2-1 4-2 2h-7l-2-1c-1 0-3 0-4-1l-1-1 1-1v-1l-1-1-3-1v-2h1l2-2c0 1 1 1 2 2s1 1 1 3h3c0 1-1 0-2 1l-1 1c0 1 0 1 1 1l3 1c0-1 1-1 1-1 0-1 3 0 4 0 0-1 1-1 2-2v-2l-1-1h0z" class="H"></path><path d="M610 317c1-1 1-2 2-3h3l1 2h1v1h-7z" class="S"></path><path d="M616 309v-1h3l1-2 1 3-1 2-1 4-2 2v-1c0-2 1-5 1-7h-2z" class="C"></path><path d="M601 308l2-2c0 1 1 1 2 2s1 1 1 3l-1 2c0 1 0 1 2 2h2l-1 1c-1 0-3 0-4-1l-1-1 1-1v-1l-1-1-3-1v-2h1z" class="B"></path><path d="M601 308l2-2c0 1 1 1 2 2l-1 1v1s-1 0-1 1l-3-1v-2h1z" class="S"></path><path d="M601 308l2-2c0 1 1 1 2 2l-1 1-3-1z" class="C"></path><path d="M615 271h1 1 0l2 2c1 0 2 1 2 2 1 1 1 1 2 1v1l-2 2c0 1 0 2-1 3 1 1 1 2 2 2v1l-2 2c1 1 1 2 1 3h0c-2 0-2 2-4 1v-1c1-1 1-3 1-4 1-2 1-2 0-4 1-1 0-2 0-3v-1l-2 1h-1c0-1-1-1-2-2l-1-4h-1l2-2h2z" class="Af"></path><path d="M615 271h1v1h-2l1-1z" class="Ao"></path><path d="M617 271l2 2v1 1h-1c-1-1-1-2-1-4z" class="G"></path><path d="M619 273c1 0 2 1 2 2v2h-1l-1-3v-1z" class="Ap"></path><path d="M621 275c1 1 1 1 2 1v1l-2 2c0 1 0 2-1 3 0-1-1-1-1-2v-1c0-1 0-1 1-2h1v-2z" class="Ao"></path><path d="M621 290c2 0 2 0 4-1h1 1l1 2c1 2 2 5 2 8 0 1 0 2-1 3h0c-1 2-1 2-3 2h0l-1 1c-2 0-3 1-4 2v2h0l-1-3v-2h2 1c-1-2-1-2-1-3s0-1 1-2c-1 0-2-1-2-2l1-1c0-1 0-1 1-2-1-1-2-2-2-4h0z" class="AV"></path><path d="M621 290c2 0 2 0 4-1h1c-1 1-1 1-2 3h1l-1 1v-1l-1 1v1c-1-1-2-2-2-4h0z" class="AM"></path><path d="M620 311l2 2v1h-1l1 1v4c0 1-1 1-1 2 0 0 1 0 1 1l1 1h-1l-2-1c0 1-1 2-2 3-1 0-1 0-2 1 0 1 0 1-1 2h-2v-5l-3-1-6-3 1-1c-2-1-1-1-1-3 1 1 3 1 4 1l2 1h7l2-2 1-4z" class="e"></path><path d="M610 322l1-1c1-1 1 0 2 0h2v1 2c-1 0-1 0-2-1l-3-1z" class="J"></path><path d="M615 321v-2h3v1h-1l2 2-1 1c-1 0-1 0-3-1v-1z" class="v"></path><path d="M613 323c1 1 1 1 2 1v-2c2 1 2 1 3 1l1-1h1c0 1-1 2-2 3-1 0-1 0-2 1 0 1 0 1-1 2h-2v-5z" class="w"></path><path d="M628 291h3c0 1 1 1 2 2v1h1 1 1 1v1c0 1 0 1-1 2l1 1h1c1 0 1 1 1 2v1l1 1-1 1c-1 2-1 4-1 6h0 0l-2 1-1 2v4 1s-1 0-1-1h-1l-1 1h0c-1 0-1 0-2 1h-1l-2 1v1s1 1 1 2h-4l-1 1-1-1c0-1-1-1-1-1 0-1 1-1 1-2v-4l-1-1h1v-1l-2-2 1-2h0v-2c1-1 2-2 4-2l1-1h0c2 0 2 0 3-2h0c1-1 1-2 1-3 0-3-1-6-2-8z" class="AC"></path><path d="M629 302c2 0 3-1 4 0 0 1 0 2-1 3-1 0-2-2-3-3h0z" class="Q"></path><path d="M625 305l1-1c2 3 4 3 6 6-2 0-2 0-3-1h-1v-1h-3v-3z" class="J"></path><path d="M629 309c1 1 1 1 3 1 1 2 1 2 0 4h0v3c-1 0-1 0-2 1h-1c1-3 0-4-1-6l1-3z" class="Q"></path><path d="M621 309v-2c1-1 2-2 4-2v3h3v1h1l-1 3c-1-1-1-1-2 0-1-1-1-1-1-2l-3 3-2-2 1-2h0z" class="AC"></path><path d="M628 309h1l-1 3c-1-1-1-1-2 0-1-1-1-1-1-2 1-1 1-1 3-1z" class="X"></path><path d="M621 309h0c1 0 1 1 2 1l1-1s0 1 1 1l-3 3-2-2 1-2z" class="Ad"></path><path d="M625 310c0 1 0 1 1 2 1-1 1-1 2 0 1 2 2 3 1 6l-2 1v1s1 1 1 2h-4l-1 1-1-1c0-1-1-1-1-1 0-1 1-1 1-2v-4l-1-1h1v-1l3-3z" class="J"></path><path d="M626 312c0 1 0 2-1 3l2 2v2 1h-2l-2-2 3-6z" class="Aq"></path><path d="M626 312c1-1 1-1 2 0 1 2 2 3 1 6l-2 1v-2l-2-2c1-1 1-2 1-3h0z" class="Ak"></path><path d="M628 291h3c0 1 1 1 2 2v1h1 1 1 1v1c0 1 0 1-1 2l1 1h1c1 0 1 1 1 2v1l1 1-1 1c-1 2-1 4-1 6h0 0l-2 1-1 2c-1-1-2-2-2-4v-2-1h2v-1l-2-2c-1-1-2 0-4 0 1-1 1-2 1-3 0-3-1-6-2-8z" class="J"></path><path d="M635 305h1v2l-1 1c0-1-1-1-1-1l1-2z" class="v"></path><path d="M637 298h1c1 0 1 1 1 2v1l1 1-1 1c-1 2-1 4-1 6-1-1-1-2-1-3v-3h-1v-2h-1c1-1 1-1 2-1v-2z" class="i"></path><path d="M516 115c1-2 1-4 2-7 0 2 1 5 0 6l1 8 1 1v2l4-1h0l2 2 2 3c1 1 3 2 5 3l-2 1h1c2 0 4 1 6 1v1c-3 0-5 0-8 1l6 2c1-1 3-1 4-1v1l-2 2-1 3h1c0 2 0 2-1 4h1v1l1-1c1-1 2-1 3-1h1 2c2 0 3 1 5 2l1 1c3 2 12 8 13 11v1c1 0 3 0 4 1h0l-1 1v1l2 2-1 1-2-1h0l-1-1h-3l-1-2-1 1v2l-1 1h-2-1v2 2c-1 1-2 3-3 4h0l-2 1-2 1h-1-1c-1 2-1 2-3 2l-2-2h-5l-3 1c-1 1-2 3-2 4-1 2 0 5-1 7 0 1 0 1-1 1h0l-1 2v2l-1 3c-1 2-2 3-2 5 0 1-1 2 0 4h0v2c0 1 0 2 1 3l-3 5c-1 3-2 5-1 9 1 1 2 2 4 2-2 1-3 3-4 6h-2c0 2 0 4-1 6v1c0 2-1 2-2 3l-1 15h0v19h-1l-1-21-1-16v-1c0-1 0-1-1-2 0-3-1-4-3-6h0v-5-1l-2-2h0l-2 2c0-3-1-6-3-9-2-2-2-4-2-7-1-1 0-1-1-1-2 0-1 0-2-1h-3l2-2 4-3c-1-1-1-2-1-3v-2c0-1 0-2-1-3v-1l1-1-1-1-1 1-1-1c-1 0-1 0-2-1-1-2-1-3-2-4l-1-1-2-4c0 1-2 1-3 1h-1c-1 0-1 0-2 1l-1-3-1 2c-1-2-2-3-4-4l-1-2c-1-1-3-2-4-3l-3-2h0l-3-1h0l1-1c0-1-1-1-1-2-2-1-2-1-3-2l2-1-1-1c0-1 0-2 1-3 0-1 1-1 2-2l7-1h-1 0c0-2 2-3 3-4 5-5 12-10 19-9h2c-1-2-1-2-3-3-1-1-2-1-3-1 3 0 5-3 8-5 1-1 3-2 5-3v-1l3-3c1-2 2-4 4-6 1-2 3-4 4-7z" class="H"></path><path d="M506 146c0-2 2-3 4-4-2 3-2 6-2 9-1-2-1-3-2-5z" class="G"></path><path d="M524 132l2-1c1 0 3 1 5 1-3 0-6 3-8 2v-1h1v-1z" class="F"></path><path d="M519 195c0-4 0-7 1-11l1 1c0 2 0 6-1 7v3h-1z" class="t"></path><path d="M505 142c4-1 7-1 10-2-1 1-4 2-5 2-2 1-4 2-4 4v1c-1-1-2-3-2-4l1-1z" class="P"></path><path d="M527 143l1-1c1-1 3-2 4-2l1 1c0 1-1 2-1 2l-2 4-1-1h-1l1-1s0-1 1-2c0 0 0-1 1-1h-1l-3 1z" class="S"></path><path d="M526 126l2 3c1 1 3 2 5 3l-2 1v-1c-2 0-4-1-5-1l-2 1c0-2 0-4 2-6z" class="G"></path><path d="M519 231v1h2v1c0 2 0 4-1 6v1c0 2-1 2-2 3l1-12z" class="AI"></path><path d="M520 125l4-1h0l2 2c-2 2-2 4-2 6v1h-1l-1-3c-1-1-2-3-2-5z" class="T"></path><path d="M520 125l4-1h0c-2 2-1 4-2 6-1-1-2-3-2-5z" class="P"></path><path d="M519 195h1c0 2 0 5 1 7v2 6h-2l-1-4 1-11z" class="y"></path><path d="M518 206l1 4h2l-1 4c-1 6-1 12-1 18v-1l-1-1c-2-4 0-19 0-24z" class="t"></path><path d="M513 216c1 7 2 16 1 24v-1c0-1 0-1-1-2 0-3-1-4-3-6h0v-5-1l1-3 1-1c1-1 1-2 1-3v-2h0z" class="q"></path><path d="M510 226c1 2 2 2 2 3l-2 2v-5z" class="AO"></path><path d="M523 210l1-1c1-2 1-2 2-3v2c0 1 0 2 1 3l-3 5c-1 3-2 5-1 9 1 1 2 2 4 2-2 1-3 3-4 6h-2v-1h-2c0-6 0-12 1-18l1 1c2-2 2-3 2-5z" class="a"></path><path d="M523 225c1 1 2 2 4 2-2 1-3 3-4 6h-2v-1l1-4c0-1 1-2 1-3z" class="v"></path><path d="M522 142c1-4 4-5 8-6l6 2c1-1 3-1 4-1v1l-2 2h-3l-2 1-1-1c-1 0-3 1-4 2l-1 1h0c-2 4-3 8-6 11l-1 2c0 2 0 2-1 3 0-2 0-3 1-5 0-4 0-8 2-12z" class="D"></path><path d="M521 154c0-2 1-3 1-4 1-3 2-5 5-7-2 4-3 8-6 11z" class="G"></path><path d="M522 142c1-4 4-5 8-6l6 2c-5 0-8 1-12 5-1 0-2-1-2-1z" class="AX"></path><defs><linearGradient id="At" x1="521.703" y1="157.735" x2="540.797" y2="152.265" xlink:href="#B"><stop offset="0" stop-color="#a0958d"></stop><stop offset="1" stop-color="#cbbcb4"></stop></linearGradient></defs><path fill="url(#At)" d="M523 170c2-9 8-19 14-27h1c0 2 0 2-1 4h1v1l1-1c1-1 2-1 3-1-3 2-4 3-5 6l-2 5c0 2-1 3-2 5h1 0l2 1-2 2c-1 1-2 1-3 2h0-1-1v-2l-1 1c-2 1-3 2-4 4h-1z"></path><path d="M535 157c0 2-1 3-2 5h1 0l2 1-2 2c-1 1-2 1-3 2h0-1-1v-2c1-1 2-2 3-4s2-3 3-4z" class="P"></path><path d="M511 163c1 2 2 7 3 10s0 8 1 11v9c0 2-1 5-2 7v6h0l-2-2c0-2-1-3-2-5v-1c-1-1-1-1-2-1l-1 1-1-1h0c1-3 1-5 2-7l1-3 1-1h1v-3l-1-4v-2l-1-3v-1c0-1 1-1 1-2 1 0 1-1 2-1v-5c-1-1-1-2 0-2z" class="S"></path><path d="M510 183c1 7 3 15 3 23l-2-2c0-2-1-3-2-5v-1c-1-1-1-1-2-1l-1 1-1-1h0c1-3 1-5 2-7l1-3 1-1h1v-3z" class="AB"></path><path d="M507 190c1 2 2 5 2 7v1c-1-1-1-1-2-1l-1 1-1-1h0c1-3 1-5 2-7z" class="n"></path><path d="M503 194h1c0 1 0 2 1 3h0l1 1 1-1c1 0 1 0 2 1v1c1 2 2 3 2 5l2 2h0v10h0v2c0 1 0 2-1 3l-1 1-1 3-2-2h0l-2 2c0-3-1-6-3-9-2-2-2-4-2-7-1-1 0-1-1-1-2 0-1 0-2-1h-3l2-2 4-3c-1-1-1-2-1-3h3 0v-5z" class="Am"></path><path d="M501 209c1-2 1-3 1-5 0 3 1 5 1 7v3 2c-2-2-2-4-2-7z" class="S"></path><path d="M510 216l1 1 2 1c0 1 0 2-1 3l-1 1-1 3-2-2v-2l-1-1 1-1c0 1 1 1 2 2v-4-1z" class="u"></path><path d="M510 216l1 1 2 1c0 1 0 2-1 3l-1 1-1-5v-1z" class="i"></path><path d="M511 217l2 1c0 1 0 2-1 3 0-2-1-3-1-4z" class="AG"></path><path d="M505 203l1 1c0 1 0 1 1 2l1-1v4h0v4 1l-3-1v-10z" class="u"></path><path d="M501 202h1v2c0 2 0 3-1 5-1-1 0-1-1-1-2 0-1 0-2-1h-3l2-2 4-3z" class="E"></path><path d="M509 199c1 2 2 3 2 5l2 2h0v10h0v2l-2-1-1-1v-7l-1-2c1-3 0-5 0-8z" class="x"></path><path d="M510 209c1 2 2 3 2 5 0 1 0 1 1 2v2l-2-1-1-1v-7z" class="X"></path><path d="M503 194h1c0 1 0 2 1 3h0l1 1 1-1c1 0 1 0 2 1v1c0 3 1 5 0 8l1 2h-2v-4l-1 1c-1-1-1-1-1-2l-1-1c-1 0-1 1-2 2v-3-3h0v-5z" class="J"></path><path d="M503 194h1c0 1 0 2 1 3h0l1 1h1 1c-1 1-1 2-2 3l1 1c-1 0-2-1-3-1 0-1-1-2-1-2v-5z" class="Z"></path><path d="M528 166l1-1v2h1 1 0 3c0 1-1 2-2 3 1 0 2 0 3-1l-1 4c1 1 1 1 1 2-2 1-3 1-4 3h3c-1 1-2 3-2 4-1 2 0 5-1 7 0 1 0 1-1 1h0l-1 2v2l-1 3c-1 2-2 3-2 5 0 1-1 2 0 4h0c-1 1-1 1-2 3l-1 1c0 2 0 3-2 5l-1-1 1-4v-6-2c-1-2-1-5-1-7v-3c1-1 1-5 1-7l-1-1c0-4 1-10 3-14h1c1-2 2-3 4-4z" class="AN"></path><path d="M535 169l-1 4c1 1 1 1 1 2-2 1-3 1-4 3v1c-1 0-2 1-3 2l1-2 3-9c1 0 2 0 3-1z" class="c"></path><path d="M531 178h3c-1 1-2 3-2 4-1 2 0 5-1 7 0 1 0 1-1 1h0l-1 2h-3c1-4 1-8 2-11 1-1 2-2 3-2v-1z" class="m"></path><path d="M531 178h3c-1 1-2 3-2 4-1 2 0 5-1 7 0 1 0 1-1 1h0c0-1 0-2-1-3v-1c0-1 1-2 1-3 0-2 1-3 1-4v-1z" class="a"></path><path d="M526 192h3v2l-1 3c-1 2-2 3-2 5 0 1-1 2 0 4h0c-1 1-1 1-2 3l-1 1c0 2 0 3-2 5l-1-1 1-4v-6-2-4 1c0 2 0 3 2 4 1 0 1-1 2-2 0-3 0-5 1-8v-1z" class="AO"></path><path d="M521 204c2 0 2-1 3 0 0 1-2 4-1 5v1c0 2 0 3-2 5l-1-1 1-4v-6z" class="m"></path><path d="M528 166l1-1v2h1 1c-7 8-9 21-10 31v4c-1-2-1-5-1-7v-3c1-1 1-5 1-7l-1-1c0-4 1-10 3-14h1c1-2 2-3 4-4z" class="N"></path><path d="M523 170h1c1-2 2-3 4-4-1 1-2 3-3 4-2 3-4 11-4 15l-1-1c0-4 1-10 3-14z" class="y"></path><path d="M516 115c1-2 1-4 2-7 0 2 1 5 0 6l1 8-1 1c0 4 1 7 2 11v1c-3 4-13 0-15 7h0l-1 1c0 1 1 3 2 4v-1c1 2 1 3 2 5v3l3 9c-1 0-1 1 0 2v5c-1 0-1 1-2 1 0-1-1-2-2-4l-9-23c-1-2-1-2-3-3-1-1-2-1-3-1 3 0 5-3 8-5 1-1 3-2 5-3v-1l3-3c1-2 2-4 4-6 1-2 3-4 4-7z" class="D"></path><path d="M518 123h0c-1-3-1-6-1-9h1l1 8-1 1z" class="S"></path><path d="M505 132h0 0c0 1-1 1-1 1-2 3-2 7-3 10-1-1-1-1-1-2-1-2-1-4 0-6 1-1 3-2 5-3z" class="K"></path><path d="M505 132v-1l3-3c1-2 2-4 4-6 1-2 3-4 4-7 0 4 0 7-2 10s-2 4-6 5c-1 1-2 1-3 2h0z" class="B"></path><path d="M505 142l-1-1 1-2c0-2 0-3 2-5 0 0 1 0 1-1 1-1 3-2 4-3l4-4 1 1c0 2 1 5 3 7h0v1c-3 4-13 0-15 7z" class="S"></path><path d="M492 140c3 0 5-3 8-5-1 2-1 4 0 6 0 1 0 1 1 2 2 5 5 11 6 17l2 1v-2c0-2 0-3-1-5l3 9c-1 0-1 1 0 2v5c-1 0-1 1-2 1 0-1-1-2-2-4l-9-23c-1-2-1-2-3-3-1-1-2-1-3-1z" class="F"></path><path d="M508 154l3 9c-1 0-1 1 0 2v5c-1-4-2-7-4-10l2 1v-2c0-2 0-3-1-5zm34-8h1 2c2 0 3 1 5 2l1 1c3 2 12 8 13 11v1c1 0 3 0 4 1h0l-1 1v1l2 2-1 1-2-1h0l-1-1h-3l-1-2-1 1v2l-1 1h-2-1v2 2c-1 1-2 3-3 4h0l-2 1-2 1h-1-1c-1 2-1 2-3 2l-2-2h-5l-3 1h-3c1-2 2-2 4-3 0-1 0-1-1-2l1-4c-1 1-2 1-3 1 1-1 2-2 2-3h-3c1-1 2-1 3-2l2-2-2-1h0-1c1-2 2-3 2-5l2-5c1-3 2-4 5-6z" class="G"></path><path d="M536 163l5-2c-1 1-1 2-2 3l-1 1h-4l2-2z" class="Y"></path><path d="M547 160h0l2 1h-3v1l-7 2c1-1 1-2 2-3s4-1 6-1z" class="q"></path><path d="M546 162v1c-1 1-3 2-5 2v1l-1 1h-2v-2l1-1 7-2z" class="W"></path><path d="M538 165v2c-1 1-2 1-3 2s-2 1-3 1c1-1 2-2 2-3h-3c1-1 2-1 3-2h4z" class="AR"></path><path d="M542 146h1 2c2 0 3 1 5 2l1 1c3 2 12 8 13 11v1l-4-1 1-1-3-3-2-1-2-2c-4-2-6-5-10-5l-1 1c-2 0-2 0-3 1v1l-2 2c-2 2-3 7-4 9h0-1c1-2 2-3 2-5l2-5c1-3 2-4 5-6z" class="D"></path><path d="M551 159l9 1 4 1c1 0 3 0 4 1h0l-1 1v1l2 2-1 1-2-1h0l-1-1h-3l-1-2-1 1v2l-1 1h-2-1c0-2-1-3-2-4l-1 1-2-1h-1v1c-1-1-1-1-2-1h-2v-1-1h3l-2-1 4-1z" class="Y"></path><path d="M549 161h1 0v2 1c-1-1-1-1-2-1h-2v-1-1h3zm2 2l2-2c2 0 2 0 3 2 1 0 1 1 1 2l1-1h1c0 1 0 1 1 2l-1 1h-2-1c0-2-1-3-2-4l-1 1-2-1z" class="i"></path><path d="M547 160c-1-1-3-1-3-1h-1c-1 0-2 0-3 1h-3v-1c1-1 1-2 2-4 0-1 1-3 2-4s1-1 2 0h1c4 1 7 3 10 5 1 0 0 0 1 1s3 1 4 2h-1-7l-4 1h0z" class="D"></path><path d="M551 163l2 1 1-1c1 1 2 2 2 4v2 2c-1 1-2 3-3 4h0l-2 1-2 1h-1-1c-1 2-1 2-3 2l-2-2h-5l-3 1h-3c1-2 2-2 4-3 0-1 0-1-1-2l1-4c1-1 2-1 3-2h2l1-1v-1c2 0 4-1 5-2h2c1 0 1 0 2 1v-1h1z" class="AM"></path><path d="M551 163l2 1 1-1c1 1 2 2 2 4v2c-1-1-2-2-2-4l-1 1-2-1c-1 1-2 2-4 3v-2l-1-1h1l1-2c1 0 1 0 2 1v-1h1z" class="a"></path><path d="M548 163c1 0 1 0 2 1-1 1-1 1-3 1l1-2z" class="u"></path><path d="M546 163h2l-1 2h-1c0 2-1 3-2 5l-1 2-1-2-3-1 1-2 1-1v-1c2 0 4-1 5-2z" class="q"></path><path d="M541 166h2l-1 2 2 2-1 2-1-2-3-1 1-2 1-1z" class="AI"></path><path d="M543 174h2c0-1 1-1 1-2v-1c1-1 1-1 3-1l1 1s0 1-1 2h0l1 1c1-1 2-3 2-4 1-1 0-1 1-2v1 2h3c-1 1-2 3-3 4h0l-2 1-2 1h-1-1c-1 2-1 2-3 2l-2-2s0-1 1-2v-1z" class="Ar"></path><path fill="#b77475" d="M553 171h3c-1 1-2 3-3 4h0l-2 1c0-2 1-3 2-5z"></path><path d="M543 175c1 1 2 2 4 2-1 2-1 2-3 2l-2-2s0-1 1-2z" class="J"></path><path d="M540 167l-1 2 3 1 1 2v2 1c-1 1-1 2-1 2h-5l-3 1h-3c1-2 2-2 4-3 0-1 0-1-1-2l1-4c1-1 2-1 3-2h2z" class="u"></path><path d="M539 169l3 1c0 2-1 2-2 4-2 1-2 1-4 0 1-1 1 0 0-1v-1l3-3z" class="a"></path><path d="M540 167l-1 2-3 3v1c1 1 1 0 0 1h0v1h1v2l-3 1h-3c1-2 2-2 4-3 0-1 0-1-1-2l1-4c1-1 2-1 3-2h2z" class="i"></path><path d="M498 144l9 23c1 2 2 3 2 4s-1 1-1 2v1l1 3v2l1 4v3h-1l-1 1-1 3c-1 2-1 4-2 7-1-1-1-2-1-3h-1v5h0-3v-2c0-1 0-2-1-3v-1l1-1-1-1-1 1-1-1c-1 0-1 0-2-1-1-2-1-3-2-4l-1-1-2-4c0 1-2 1-3 1h-1c-1 0-1 0-2 1l-1-3-1 2c-1-2-2-3-4-4l-1-2c-1-1-3-2-4-3l-3-2h0l-3-1h0l1-1c0-1-1-1-1-2-2-1-2-1-3-2l2-1-1-1c0-1 0-2 1-3 0-1 1-1 2-2l7-1h-1 0c0-2 2-3 3-4 5-5 12-10 19-9h2z" class="AN"></path><path d="M507 167c1 2 2 3 2 4s-1 1-1 2v1l-1-4v-3z" class="B"></path><path d="M466 164l1 1c1 1 2 1 3 1s2-2 2-2c0-1 0-1-1-1l-1-1 1-1s1 1 2 1l1-1c2 0 2 1 4 2-2 0-2 0-3 1v2l-3 1h-1-4c-2-1-2-1-3-2l2-1z" class="W"></path><path d="M472 167l3-1v3l-1 1 1 1-1 1-1 1-3-2h0l-3-1h0l1-1c0-1-1-1-1-2h4 1z" class="AH"></path><path d="M467 167h4l-1 1 1 2c-1 0-1 1-1 1l-3-1h0l1-1c0-1-1-1-1-2z" class="n"></path><path d="M472 167l3-1v3l-1 1 1 1-1 1h-1v-2l-1-1 1-1-1-1z" class="m"></path><path d="M478 163c1 0 2 1 4 0 0 1 1 1 2 1 2-1 3 0 5 0l-1 1h3 0l-3 3h0-2c-2-1-3-1-4-1l-1 1c-2 0-2-1-3-2v-1h0c-1-1-2-1-3-1 1-1 1-1 3-1z" class="k"></path><path d="M478 165l2-1 4 1c-1 0-2 2-2 2l-1 1c-2 0-2-1-3-2v-1z" class="X"></path><path d="M488 165h3 0l-3 3h0-2c-2-1-3-1-4-1 0 0 1-2 2-2 2 1 2 1 4 1v-1z" class="i"></path><path d="M489 164h0c1 0 1 0 2-1l2 2c0 1 0 1-1 2 2 1 3 1 5 1 1 1 3 2 3 2 1 2 1 4 3 5h2c1 1 1 2 3 3l1-1v2 1h0l-2 1h-1l-1-1-2 1h0l-1-4c-1-1-2-4-3-5l-3-3h-4c-1 0-1 0-1-1h0-3l3-3h0-3l1-1z" class="W"></path><path d="M502 177l2 1 1 2-2 1h0l-1-4z" class="Y"></path><path d="M475 164c1 0 2 0 3 1h0v1c1 1 1 2 3 2 1 0 2 1 4 1l-1 1v1l2 1c-2 1-2 1-4 1-1 1-2 1-3 2 0 1-1 1-2 1-1-1-3-2-4-3l1-1 1-1-1-1 1-1v-3-2z" class="Q"></path><path d="M475 164c1 0 2 0 3 1h0v1c1 1 1 2 3 2 1 0 2 1 4 1l-1 1v1c-1-1-2-1-3-1-2 0-1 1-2 3h-1-1l1-1-1-1-2-2v-3-2z" class="n"></path><path d="M475 164c1 0 2 0 3 1h0v1l-1 1h0c1 1 0 3 0 4l-2-2v-3-2z" class="q"></path><path d="M482 167c1 0 2 0 4 1h2 0 3 0c0 1 0 1 1 1h4l3 3c1 1 2 4 3 5l1 4h-1v2l-1-2v-1c0-1-1-2-1-3-1 2-2 3-2 4l-1 2c0-1-1-2-2-3 1-1 1-1 0-3s-3-3-4-4v-1h-5l-2-1v-1l1-1c-2 0-3-1-4-1l1-1z" class="X"></path><path d="M491 173c3 0 6 1 8 4h0 1c-1 2-2 3-2 4l-1 2c0-1-1-2-2-3 1-1 1-1 0-3s-3-3-4-4z" class="Z"></path><path d="M482 167c1 0 2 0 4 1h2 0 3 0c0 1 0 1 1 1h4l3 3c-4-2-7-2-11-1v1l-1-1-3-1 1-1c-2 0-3-1-4-1l1-1z" class="k"></path><path d="M500 177c0 1 1 2 1 3v1l1 2v-2h1 0l2-1 1 1h1l2-1h0v-1l1 4v3h-1l-1 1-1 3c-1 2-1 4-2 7-1-1-1-2-1-3h-1v5h0-3v-2c0-1 0-2-1-3v-1l1-1-1-1-1 1-1-1c1-2 2-3 1-6l-1-2 1-2c0-1 1-2 2-4z" class="Q"></path><path d="M500 177c0 1 1 2 1 3v1l-1 1-1 2-1-1v-2c0-1 1-2 2-4z" class="m"></path><path d="M503 187l2 1h-1v5c1-2 2-4 2-6h2l-1 3c-1 2-1 4-2 7-1-1-1-2-1-3h-1c-1-1 0-2-2-4v-1c2 0 2-1 2-2z" class="AI"></path><path d="M498 185h2v1 1h3c0 1 0 2-2 2v1c2 2 1 3 2 4v5h0-3v-2c0-1 0-2-1-3v-1l1-1-1-1-1 1-1-1c1-2 2-3 1-6z" class="a"></path><path d="M501 190c2 2 1 3 2 4v5h0-3v-2c0-1 0-2-1-3v-1l1-1c1 1 1 3 2 4 0-2 0-1-1-3v-3z" class="Ak"></path><path d="M509 179l1 4v3h-1l-1 1h-2-1v-2c-1 0-1 1-2 1h-2c1-1 1-1 1-3v-2h1 0l2-1 1 1h1l2-1h0v-1z" class="i"></path><path d="M505 180l1 1c-1 1-1 2-2 3l-1-1-1-2h1 0l2-1z" class="AB"></path><path d="M509 179l1 4v3h-1v-1c-1-1-1-3-2-4l2-1h0v-1z" class="c"></path><path d="M486 172h5v1c1 1 3 2 4 4s1 2 0 3c1 1 2 2 2 3l1 2c1 3 0 4-1 6-1 0-1 0-2-1-1-2-1-3-2-4l-1-1-2-4c0 1-2 1-3 1h-1c-1 0-1 0-2 1l-1-3-1 2c-1-2-2-3-4-4l-1-2c1 0 2 0 2-1 1-1 2-1 3-2 2 0 2 0 4-1z" class="u"></path><path d="M494 180c-2-1-2-1-4-1 0-2 0-2 1-3 1 1 2 1 3 1h1c1 2 1 2 0 3h0-1z" class="Q"></path><path d="M494 180h1 0c1 1 2 2 2 3l1 2c1 3 0 4-1 6-1 0-1 0-2-1-1-2-1-3-2-4-1-2-2-4-1-6h2z" class="An"></path><path d="M495 180c1 1 2 2 2 3l1 2h-2c-1 0-1-1-2-2h1v-3h0z" class="Ak"></path><path d="M482 173l2 1c1 0 2 1 3 1 1 2 1 2 1 4 0 1 2 2 2 2 0 1-2 1-3 1h-1c-1 0-1 0-2 1l-1-3-1 2c-1-2-2-3-4-4l-1-2c1 0 2 0 2-1 1-1 2-1 3-2z" class="T"></path><path d="M482 173l2 1-3 1c0 2 1 4 2 5l-1 2c-1-2-2-3-4-4l-1-2c1 0 2 0 2-1 1-1 2-1 3-2z" class="J"></path><path d="M498 144l9 23v3l-1-1c-1-2-3-3-5-4l-5-3-2-1-5-2c-3-1-6-2-9-2h-3-2-1 0c0-2 2-3 3-4 5-5 12-10 19-9h2z" class="K"></path><path d="M496 162c1 0 1-1 2-1-1-3-1-5 0-8 2 4-2 6 3 9v3l-5-3z" class="P"></path><path d="M477 157v-2c4-3 8-6 12-7 3-1 3-1 6 0 1 1 3 3 3 5-1 3-1 5 0 8-1 0-1 1-2 1l-2-1c1-1 1-1 1-2 0-2 1-2 1-4 1-1 0-2-1-3l-1-1c-1-1-2-2-3-2-3 0-9 3-11 6v2h-3z" class="B"></path><path d="M480 157v-2c2-3 8-6 11-6 1 0 2 1 3 2l1 1c1 1 2 2 1 3 0 2-1 2-1 4 0 1 0 1-1 2l-5-2c-3-1-6-2-9-2z" class="F"></path><path d="M494 151l1 1c1 1 2 2 1 3 0 2-1 2-1 4 0 1 0 1-1 2l-5-2h4c1 1 0 1 1 0 1 0 1-1 1-2h0v-1-2-1l-1-1v-1z" class="S"></path><defs><linearGradient id="Au" x1="438.014" y1="192.79" x2="245.467" y2="694.72" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2d353d"></stop></linearGradient></defs><path fill="url(#Au)" d="M430 169l-1 3 1 1h0l1 2 2-3c2 1 3 0 4-1l2-1-1 2c1 1 1 1 2 1 2 0 3 0 4 1h2c1 0 1-1 2-1h1v1h4l9-2c2-1 3-1 5-2l3 1h0l3 2c1 1 3 2 4 3l1 2c2 1 3 2 4 4l1-2 1 3c1-1 1-1 2-1h1c1 0 3 0 3-1l2 4 1 1c1 1 1 2 2 4 1 1 1 1 2 1l1 1 1-1 1 1-1 1v1c1 1 1 2 1 3v2c0 1 0 2 1 3l-4 3-2 2v1c0 1 0 2 2 3h1v2c0 2 2 3 1 5-2 2-5 4-7 7 0 2 0 4 1 6 0 2 1 4 1 6-1-2-2-3-2-5-1-3-2-7 0-10l6-6c-1-1 0-1-2-1-12 1-25 3-36 8-4 2-8 3-12 6-1 1-3 2-5 3l1 6v2l-2 2v3l-1 1c-1 0-2 0-3-1v2l-2-1c0 2-1 2-3 4l1 1h1v4l-3 3c-1 1-2 3-3 5v5c0 1 1 3 1 5h1c1 1 1 1 2 1-1 2-3 2-2 4l1 1-2 1-2-2-1-1c-3 2-5 5-6 7v1h-2c-2 1-2 2-4 2 0 2 1 2 0 4v1s-1 1-1 2 1 3 2 4c0 1 0 2-1 3v1c-1 1-2 2-3 2l1 1h1v1c0 1 0 2-1 3l2 2-1 1h-2v3l-1-1c0 1-1 1-2 2l-1 1c0 1 0 2-1 2-1 1-1 2-1 3l-1 1c0 1-1 4-1 4v3 2 2 9h-3v1 1l1 5v1 1l1 7c0 1 0 2-1 3l2 9 3 1 2 9 1 6h1l1-1c0 1 0 2 1 3h2l-1 1c0 3 1 4 3 6 0 1 0 2-1 3v2c0 1 0 1 1 2h2v1l1 1h-2v1l2 1h0l1-1c1-1 1-1 3-1 0 0 0 1 1 2 0 2 1 6 3 7h1v3c1 1 1 1 1 2s0 2 1 2v2 2l1 1 1 2v-1l1 1-1 1 1 1-2 2c2 2 2 3 2 5-2 2 0 5-2 6h0c0 1-1 3 0 4h-2l-2-2 5 13c1 2 2 4 3 5v1l1 2 1 7h0l3 7 2 6 6 16c2 1 3 1 5 1h1v1l1 2h-1l2 2s0 1 1 0h1v2 5l-1 2h0v2l-1 1h0v4c1 1 1 1 1 2 1 2 1 5 3 6l1 2c0 2 1 3 2 5 0 2 1 5 2 7v-1-1c1-1 1-2 2-2l1-2h1l1 2h0 2c0 1 1 1 1 2l1-1v-1c0-1 1-2 2-3 1 2 2 4 4 6h0 1c-1 1-1 2-1 3v1c0 2-1 2-2 4l-1 3c0 1 1 2 1 3v1c0 2 1 3 1 5h-1l-1 1 1 1c1 2 2 3 4 5-1 1-2 1-3 1l-1 2c1 0 1 0 2 1h0l5 5h-2l-1-1c-1 1-2 1-2 1l4 12v1c1 1 2 6 3 7l1 4 2 5 2 3h1l2-1v1l2 3 1 1-2 2c0 1 1 2 2 3 1 2 4 3 5 5h1 2l1 1 4 7 2 1s0 1 1 2l8 7v2l1 1c1 1 2 2 2 4l2-2 3 3 3 3h0c1 1 2 1 3 1l3 3h-4l1 1v2c1 1 2 1 3 2l1-1 3-6 1-3-2-2 1-1c0-1 1-2 1-3 1 0 2-1 2-1v-2l2-1 2-4c1-3 3-6 4-9l4-10 11-25h1l-20 49v1c0 1 0 1 1 2v1l1 1v2c-1 1-1 1-1 2v1h-1c0 1 0 1-1 2v1 1 2c0 1-1 1-1 2l-1 1c-1 0-1 1-1 1 0 2-1 4-2 5v3l-1 1 1 1h1v-3h0c0-1 1-2 1-2l1-2s0-1 1-2l1 1c-1 1-1 0-2 2l1 1-1 1 1 1h-1-1l1 1c0 1 0 1 2 2h1v1h2l1 1c1-1 1-1 1-2v-1h2v-1-1-1h1c1 1 0 1 1 1 0-2 0-3 2-4h0c1-2 0-2 1-3 0-2 1-3 2-5 1-1 2-1 3-2-2 5-5 9-7 14l-7 17 1 1h0l1-1 1-1c0 2 0 3 1 4h2c-1 1-2 3-2 4 2-2 3-4 4-7h0l1-5h1v4c0 2-1 5-2 7-1 1-1 2-1 3h0l2-2v1l-1 1v1c-1 1-1 1-1 2v1l-1 1v1 1h0c-1 1-1 2-1 3l-20 54c0 2-1 4-2 5l-8 21-1 4c-3 6-6 11-11 15h3v-1c1 0 2-1 2-1h1l-5 8c-2 7-3 15-3 23 0 1 0 4-1 5-1 2-1 4-1 7l1 53 1-4v15h0c0 1 0 3-1 4l-1-1v-1-1l-1-1c-1-1-1-2-1-3-1-2-1-3-3-5l-12-27-21-50c-4-10-8-20-13-30l-5-13s-1-2-1-3l-1-3-3-7c-3-2-4-4-5-6h0c-1-1-1-1-2-1 0-1-1-2-1-2-3-2-4-5-7-6v-1l-1-1v-1l-3-2v-2l-1-1c2 0 5 2 6 3 1-1 0-1 0-2-2-1-3-2-4-4h-1c0-1 0-2-1-3s-1-2-2-3l1-1c2 1 3 1 4 2h0v-3-1c-1-2-1-5-3-7l1-2-8-18-18-42-5-12c0-4-3-8-5-11-7-7-16-14-21-22 0-4-4-8-6-10l-3-6-6-9c0-1-1-2-2-4l-3-5c0-1-2-3-3-4h3l-2-3-1-2h2v-2s0-1-1-2l-3-2c-2-2-4-4-5-6v-1l-3-7v-1c-1-1-3-3-4-5 0-1 0-2 1-3h0c-3-3-4-6-6-8l-2-4c0-2-1-3 0-5h3l1 1c-2-4-4-9-5-13v-1l2 1c-1-2-1-3-2-4s-1-1-1-2c0-2-1-4-1-6v-1-3c-1-2-1-4-1-6v-1c1-3 1-6 0-10h0v-4l-1 1v1l-2-2 1-1v-2-9l1-1-3-7-2-6c-1-2-1-4-2-5l-1-3v-1c-1-1-1-1-1-2h-2l-3-7c0-1 0-2-1-2l-2 2v-10h0c-1-4-3-9-5-13l-11-22c-1 1-2 1-3 2h-1v3h-1v-3c-1-1-2-2-2-4v-2c-1-2-2-2-2-4v-3l-2-2c0 1 0 2-1 2l-1 1v1-6l3-20c0-5 2-11 3-16l1-8 1-8h-2l1-4c0-5 0-10-1-15 1-2 1-5 2-7l1-11c-1-2-1-2-1-3v1c-2-1-1-2-2-4 0 0-1 0-1-1v1l-1-1-2 2v-1c-2-2-3-4-4-5 0-1-1-2-1-3l-4-4-1 2h-1l1 1-1 1c-5-3-9-6-13-10-2-1-4-3-6-4-1 0-3-2-4-2s0 0-1-1c-3-1-3-1-6 0-3-2-18-12-22-12-1 0-3-2-4-2l-9-5c-3 0-6-2-9-3-1-1-2-1-3-2h-1c-2 0-3-1-5-1h0v1c-1-1-2-1-2-1l-1-1h0l-4-1c-2-1-3-1-5-2h0c-3-2-5 0-8-1l-15-7c-4-1-7-4-11-4v-1l-3-1v2c-1-1-2-1-3-1l-2-1-2-1c-2-1-7-2-8-4 0-1-1-2-1-3l-2-7-1-2 1-2c5 7 15 9 22 10 18 2 37 1 56 1h54 2 11 14v-1h12 7 48 9 20 8 1 1c3 0 4-2 6-4l8-9 5-4h1l3-3c4-1 9-6 13-8v1 1l1 1 2-9c0-1 1-2 1-2l1 1c0-1 1-1 1-2l2-3z"></path><path d="M427 512c1 1 0 1 0 2v-2z" class="z"></path><path d="M312 328l2-2 1 2c-1 0-1 1-2 1l-1-1z" class="V"></path><path d="M385 313h1c1 2 1 4 0 7 0-2 0-2-1-3v-4z" class="M"></path><path d="M398 460c1 0 1 0 1 1v1c0 1 0 0-1 1v1c0-1 0-2-1-2v-1l1-1z" class="V"></path><path d="M391 345c1 1 1 1 1 2v-1c1 2 1 3 1 5-1-2-2-3-2-6z" class="d"></path><path d="M312 328c0-2-1-4-1-5v-1l3 4-2 2zm12 46c-1-1-2-3-3-4h1v-1c1 0 2 3 2 4v1z" class="z"></path><path d="M465 585h1c0 2 1 3 2 4h-2v-1c-1-1-1-2-1-3z" class="V"></path><path d="M338 368h0c0-2 0-2-1-3v-1-1h0c1 2 2 5 3 8-1-1-2-1-2-3z" class="r"></path><path d="M419 524h1c1 0 1 0 2 1-1 1-1 2-1 4-1-1-1-2-2-2v-1-2h0z" class="AD"></path><path d="M292 254l7 1-1 1h-5l-1-2z" class="t"></path><path d="M303 343h2v1 3h0-1c-1-1-1-2-1-4z" class="s"></path><path d="M369 244v1c-1 1-3 2-5 2l-1-1c2-1 4-2 6-2z" class="d"></path><path d="M392 431h1l2 2c0 1 0 0 1 2l-1 1-1 1c-1-2-1-4-2-6zm-64-152h2c1 1 2 2 3 2v1l1 2c-1-1-2-2-3-2h-1c0-1-1-2-2-3z" class="M"></path><path d="M369 244c3-2 5-4 8-4-2 2-5 4-8 5v-1z" class="s"></path><path d="M134 214l5 4v2c-1-1-2-1-3-1l-2-1v-1l2 1c-1-1-2-2-2-3v-1z" class="d"></path><path d="M404 281v2l1 2v2l-1-1-2 1h0l2-6z" class="AO"></path><path d="M402 390c1 1 1 3 2 5v2 2 1c1 1 1 1 1 3-1-1-1-2-1-2-1-2-1-4-2-6 0-2-1-3 0-5z" class="M"></path><path d="M454 582l2 8h0l-1-2c-1 1-1 2-1 2h0c0-1-1-3-2-4v-1h1l1-3z" class="g"></path><path d="M404 411c0 1 3 7 3 8-1 2 0 2 0 3v1l-1-3-1 1-1-4c1-2 0-4 0-6z" class="M"></path><path d="M313 329c1 0 1-1 2-1 1 3 3 5 4 8l-4-3c-1-1-1-3-2-4z" class="AD"></path><path d="M337 282c1 0 2-1 3-2v1l-1 2h0v1 2c0 1 1 2 1 3v2c0-1-1-1-1-2h0c-1-1-1-1-1-2v2-1l-1-1v-5z" class="d"></path><path d="M186 222h3 2l10 1h-1c-1 1-2 1-4 1h2v1l-3-1h-4c-1-1-1-1-2-1l-3-1z" class="V"></path><path d="M387 240v-1c2 0 3-1 4-2s2-1 4-1c0 1 0 1-1 1l-4 4c0-1 0-1-1-1l-2 1v-1z" class="s"></path><path d="M419 462h1c1 1 1 2 2 4v2c1 1 0 2 1 3 1 2 1 2 1 4-1-2-5-8-4-10h0c-1-2-1-2-1-3z" class="d"></path><path d="M466 620c2 2 3 3 4 5s1 3 1 5h0c1 1 1 2 1 3-3-4-4-8-6-13z" class="AD"></path><path d="M427 565c0-1-1-2-1-3l-1-1v-2c-1-1 0-1-1-1l-1-5h0v-2-1c0 1 0 1 1 2v1l1 2 1 2c0 2 1 5 3 7l-2 1z" class="z"></path><path d="M486 670v-1l3 3c0 1 1 3 1 5-1 2 0 3 1 5h-1c-2-3 0-6-2-7h0c-2-2-2-2-2-5z" class="I"></path><path d="M399 220c1 0 2 0 3-1h6c2-1 3-1 5-1-1 1-2 1-4 1v1h2 2c-3 1-8 2-11 3v-1c1-1 1-1 2-1v-1h-5 0z" class="s"></path><path d="M411 437h0c3 2 2 3 3 6l1 1v1 1 1h0c0 1 0 2 1 2v3c-1-1-1-1-1-2v-1c-1-1-1-2-1-3-2 0-1 0-2-1l1-1c-1-1-1-2-2-3 0-1-1-2-1-2l1-1v-1z" class="d"></path><path d="M412 544v-1c0-2 0-3 1-4l4 12-1 2h0c-1-2-2-5-3-7v-1-1h-1zm71 117l2 2c1 0 1 0 2 1v2 1 1c1 1 1 2 2 4l-3-3v1h0l-2-2v-1c-1-2-1-4-1-6z" class="r"></path><path d="M363 246l1 1-6 3c-1 1-2 2-3 2-2 1-4 2-5 3l-1-1c4-3 10-5 14-8z" class="z"></path><path d="M424 248l3 2 2 2c-2 1-3 2-5 2h0l-1 1-3-2c1 0 1-1 2-1h1v-3l1-1z" class="AB"></path><path d="M427 565l2-1c0 1 1 2 1 3 1 1 1 2 1 3l1 1v1c1 1 1 1 1 2 0 2 1 3 2 4v1 1l-1 1v-1c-1-1-1-1-1-2h-1v-2l-1-2c-1-1-1-1-1-2l-1-1v-1h1l-1-1c0-2-1-3-2-4zm23-45l4 4c1 1 1 1 1 2h1l1 3c0 1 0 2 1 3 0 0 0 1 1 2l-1 1v-1l-1-1h-1v-2h-1 0c-1 0-1-1-1-1l-4-10z" class="h"></path><path d="M387 346h1 1c0 1 0 1 1 2l-1 1 1 2v6c1 1 1 1 1 3v1 1h0v2c-1 1 0 0 0 2v3h-1v-2c-1-1-1-1-1-2 1-1 1-3 1-4h-1l-1-1h0l1-1v-1c-1-1 0-4 0-6-1-1-1-3-1-4s0-1-1-2z" class="V"></path><path d="M491 632c-1-1-1-2-1-2l-3-4h0c-1-2-1-1-1-2h0c-1-1-2-3-2-4l-1-1-3-6h0c-1-2-2-3-1-5l1 1-1 1 2 2v1l1 1c1 2 1 4 3 6v1c1 1 2 1 2 2l2 2v1l1 2 1 4z" class="M"></path><path d="M408 272c0-1 1-1 1-2 1 1 2 3 3 5h0v2l-2 2v1l-3-3v1l-2-1 3-5z" class="Y"></path><path d="M407 277l1-2c1 0 1 1 2 2v2 1l-3-3z" class="AR"></path><path d="M408 272c0-1 1-1 1-2 1 1 2 3 3 5l-1 1h0l-1-1v-2l-2-1z" class="AA"></path><path d="M352 281l-1-1h0c-1-3-2-4-5-5h-1l1 1 1 1v2h-1v1c1 1 0 2 0 3h-1v-4c1-1 1-1 1-3l-1 1c-1 0 0 0-1 1v1h0v-1-1h-1c0-2 1-2 2-4h3c1 1 3 6 4 7l3 3v-4c1 3 0 6 0 9-1-2-1-5-3-7z" class="M"></path><path d="M402 287l2-1 1 1v1c-1 1-1 2-1 4l1 2s-1 1-1 2-1 2-1 4h-1v-2-3h-1l-2 2 3-10h0z" class="x"></path><path d="M402 287l2-1 1 1v1c-1 1-1 2-1 4l1 2s-1 1-1 2l-1-1s-1-1-1-2v-6z" class="AA"></path><path d="M134 214l-1-1v-1c2-1 3 1 5 2l8 3c2 1 3 2 4 2h-1c-1 0-2 0-3-1h-1c-1 1-2 1-3 1l-3-1-5-4z" class="h"></path><path d="M372 331h1c2 3 3 6 7 9-1 0-2 0-3 1-3 3-5 8-7 12v-4h1c1-2 3-6 3-9v-1c0-2-1-5-2-8z" class="j"></path><path d="M350 255c1-1 3-2 5-3 1 0 2-1 3-2v1h0c-2 1 0 0-1 1l-1 1h-1 4c0-1 1-1 2-1h1l-1 1-1 1h-2c-2 0-4 0-6 1-2 2-4 3-7 4h0c-1 0-2 0-3-1v-1c1-1 2-1 3-2l4-1 1 1z" class="s"></path><path d="M345 255l4-1 1 1c-2 1-3 1-4 1l-1-1z" class="M"></path><path d="M305 269l1-1 5 4 3 2 4 1c-7 3-12 7-15 14h0c0-3 5-9 8-12h-3l1-2 2-1-6-5z" class="B"></path><path d="M311 274l1 2s0 1-1 1h-3l1-2 2-1z" class="f"></path><path d="M340 280v-1l1-1h0v-1c1 1 1 2 2 2v1c1 1 1 2 2 4h-1s-1 0-1-1l-1 1c0 2 0 4 1 6 2 2 0 4 1 6l1 1v2l2 2h-1l-1-1-1 1h-1c0-2 0-2 1-3-1-1-1-1-2-1v-1-1c0-1 0-2 1-3-1-1-1-2-2-2v-1-1c0-1 0-1-1-2v-2-1h1l-1-1v-1-1zm189 473c0 4 0 7-1 11h1c0 2-1 3-2 5h0c-1 1-1 0-1 1v2c-1 1 0 2-1 3h-1v-3c0-1 1-1 1-2l1-6h-1v-5l2-2h0c1-1 1-3 2-4z" class="h"></path><path d="M525 759l2-2-1 7h-1v-5z" class="I"></path><path d="M551 710c1-2 3-4 5-7-2 4-4 9-6 13h0c-2 1-3 2-3 5-1 1-4 4-4 6h-1c-1 2-1 3-2 5l-3 3h-1l1-1c1-2 0-1 1-2s2-3 2-4l-1-1 1-1 1 1c0-1 1-2 1-2 1-3 4-4 4-6l-2 2h-1l4-3v-1c2-2 3-4 4-7z" class="V"></path><path d="M412 544h1v1 1c1 2 2 5 3 7h0l1-2 3 9-2 3h0c-2-2-3-5-3-7-1-2-1-3-1-4-1-2-2-4-1-5h0l-1-2h0v-1z" class="AD"></path><path d="M416 553l1-2 3 9-2 3h0v-1-2h-1l1-2c-1-1-1-3-2-5z" class="AF"></path><path d="M370 317c0-1 1-5 1-6 2-7 3-17 7-22-3 13-7 23-6 37v1h-2 0v-1c-1-3-1-6 0-9z" class="L"></path><path d="M547 721c0-3 1-4 3-5l-21 59h-1l1-2h0c0-2 1-3 1-4h0l1-2c0-3 2-5 3-8v-3l3-8c2-5 4-9 6-13l2-7c0-1 1-3 1-4s1-1 1-2v-1h0z" class="M"></path><path d="M380 253l12-10v1l-11 10c-4 5-8 9-14 11l-1-1c2 0 3-1 4-2l10-9z" class="f"></path><path d="M420 253l3 2v1l-1 1h1v2h0c0 2-1 3 0 4-1 1-2 0-3 0l-1 2c-1-1-1-1 0-2l-1-1h-1c0-1 0 0-1-1l-2 1 1-2 2-2c1-2 2-4 3-5z" class="q"></path><path d="M422 257h1v2h-1c-1 0-2 0-3-1l2-1h1z" class="c"></path><path d="M418 262v-1c1-1 2 0 3 0l-1 2-1 2c-1-1-1-1 0-2l-1-1z" class="J"></path><path d="M420 253l3 2v1l-1 1h-1-1l-1-1-2 2c1-2 2-4 3-5z" class="w"></path><path d="M564 661c1-1 2-1 3-2-2 5-5 9-7 14l-7 17-1 1v2 1c-1 1-1 2-2 3 0-1-1-1 0-2 0-1 1-2 1-4h0l1-2h0v-1c1-2 0-1 1-2v-1l1-3s0-1 1-1c0-1 1-2 1-3l1-1 1-2 1-2c0-2 0-3 2-4h0c1-2 0-2 1-3 0-2 1-3 2-5z" class="p"></path><path d="M376 265h1c9-8 17-18 27-25h0l-25 27c-2 0-2-1-3 0 0 1-1 1-2 1l2-3z" class="AL"></path><path d="M387 240v1l2-1c1 0 1 0 1 1l-7 4-1 1h-1-1l-4 3h-2c-1 1-2 1-2 1-1 1-1 0-2 1l3-3v-1-1c3-1 7-5 10-5 1 0 3-2 4-1z" class="d"></path><path d="M381 243h1 2l-2 1c-1 1-1 1-2 1v-1l1-1z" class="s"></path><path d="M330 368l-29-42v-1l17 21v1c0 4 5 7 6 11 1 1 2 3 3 4 2 1 2 4 4 6h-1z" class="p"></path><path d="M485 606c0-1-1-1-1-2v-1-1-1c-1-1-1-1-1-2l1-2 3 6 2 2 2 5h0l-2-2v-1 2 3c2 4 3 8 4 12h0-1c0-1-1-1-1-2v-1c-2-3-3-6-4-9 0-1 0-2-1-3 0-1-1-2-1-3z" class="d"></path><path d="M485 606c0-1-1-1-1-2v-1-1-1c-1-1-1-1-1-2l1-2 3 6c0 2 1 6 1 8 0-1-1-4-3-5z" class="h"></path><path d="M405 277l2 1v-1l3 3h0c0 1 1 1 1 2s0 1-1 2c0 2 0 4-1 6l-1-1c-1 2-1 3-1 4 0-2 0-3-2-5v-1-2l-1-2v-2c0-1 1-4 1-4z" class="i"></path><path d="M405 277l2 1c1 2 2 4 1 7 0 1-1 2-1 2h-1l-1-2-1-2v-2c0-1 1-4 1-4z" class="AN"></path><path d="M525 731h1v13c0 1 0 3 1 5v4 4h0l-2 2v5h1l-1 6h-1v2h0l-1-2v-1-2c1-1 0-1 1-1v-1-1c1-2 0-4 0-6h-1v-1l1-2v-11l1-3v-10z" class="r"></path><path d="M526 764l-1 6h-1c0-2 0-4 1-6h1z" class="AF"></path><path d="M525 731h1v13c0 1 0 3 1 5v4 4h0l-2 2v-18-10z" class="AE"></path><path d="M345 259c3-1 5-2 7-4 2-1 4-1 6-1v1 1l1 1-1 1h4v-1l2-1c1 1 1 1 2 1l-2 2-8 1h1l-3 1 2 1c-4-1-7-2-11-3z" class="Aa"></path><path d="M358 255v1c-1 1-2 2-4 2h-2l1-1c2-1 3-2 5-2z" class="r"></path><path d="M345 259c3-1 5-2 7-4 2-1 4-1 6-1v1c-2 0-3 1-5 2l-1 1h-1v1c2 0 3 1 5 1h1l-3 1 2 1c-4-1-7-2-11-3z" class="M"></path><path d="M435 578c1 1 2 2 2 4 0 1 1 1 1 2l1 4 3 8 1 1v1l1 2v1l-1 1v-2h-1v-2-1c-1-1-2-2-2-3v-1 4c2 2 3 5 4 7l2 5-2-1-2-4c-1-2 0-1-1-2 0-2-1-4-2-6h0c0-1-1-3-1-3 0-1-1-2-1-3v-1-1l-1-1c-1-1-2-3-2-5 0-1-1-2-1-4 0 1 0 1 1 2v1l1-1v-1-1z" class="r"></path><path d="M404 411c-1-1-1-2-2-3v-1-3c-1-1-1-2-1-3v-2c-1-1-1-3-1-4v-2c-1-1-1-2-1-3v-2c-1-1-1-2-1-3v-3c-1-1-1-1-1-2v-3c-1-1-1 0-1-1v-2c-1-1-1-2-1-3v-2-3c-1-1-1-1-1-2h0c1 0 1 1 2 1l-1-2v-3c-1-1-1-2-1-3-1-2-1-4-2-6 0-1-1-2-1-3-1-1 0-3 0-4-1-1-1-2-1-3-1-1 0-2 0-4 0 3 1 6 1 8 0 3 1 4 2 6 2 6 4 14 4 20 0 2 0 1 1 2v8 1c1 7 2 14 4 20 0 3 2 6 2 9z" class="z"></path><path d="M203 233c-5-2-10-2-15-4l-12-4c-8-2-16-6-24-9h0c1-1 8 2 10 3l3 1h1c1 1 2 1 3 2l2 1c2 1 4 1 6 1l29 8-3 1z" class="I"></path><path d="M456 526l1-1 1 1c1 2 1 5 3 6l1 2c0 2 1 3 2 5 0 2 1 5 2 7 0 3 2 5 3 8h-1c-1-1-1-1-1-2l-1 1 1 1c1 2 1 3 1 4-1-2-2-5-3-7 0-1-1-2-1-3-1-2-1-4-2-5l-2-5-1-4c-1-1-1-2-1-2-1-1-1-2-1-3l-1-3z" class="Aj"></path><path d="M405 288c2 2 2 3 2 5 0-1 0-2 1-4l1 1v3c0 1 0 2-1 4v3h0c-1 2-1 4-2 6l1 1h-2v-2h-1-1 0l1-1-2-2v-2h1c0-2 1-3 1-4s1-2 1-2l-1-2c0-2 0-3 1-4z" class="k"></path><path d="M407 293c0-1 0-2 1-4l1 1v3c0 1 0 2-1 4v3h0c-1 2-1 4-2 6l1 1h-2v-2c0-1 0-2 1-3v-2c1-2 1-2 1-3v-4z" class="e"></path><path d="M375 390v1 9c1 1 1 4 1 6 1 1 0 2 0 4 0 1 1 2 1 3v2c0-1 0-1 1-1h0v2c0 1 0 2 1 3v1 3h1v2c0 3 0 1-1 3-1 1 0 5 0 6v3c1 1 1 3 1 4v1 1l-1 1h0v-3-4c-1-1-1-1-1-2s-1-3 0-4v-4-3c-1-1-1-2-1-3v-3c-1-1-1-2-1-3v-3c-1-2-1-3-1-4-1-2 0-5 0-6-1-2-1-8-1-9v-2l1-1z" class="s"></path><path d="M328 279c1 1 2 2 2 3h1c1 0 2 1 3 2l1 2h0v-1l1-1v-2h1v5l1 1v1l2 4c1 1 1 1 1 2l1 1v4 1l-1-4-1 1h-1c-1-2-2-4-4-5-1-2-3-7-4-8s-3-4-3-6z" class="z"></path><path d="M424 530h1 0c1 1 0 1 1 2s0 0 0 1c1 2 2 3 2 4 1 1 1 3 1 4l3 6 5 12c2 4 4 10 7 14l2 4-1 1-1-2v-1c-1 0-1-1-1-2h-1c0-1 0-1-1-2v-1l-2-3v-1c0-1 0-1-1-2v-2h-1c0-1 0-2-1-3h0c-1-2-2-5-3-6l-2-2c0-1-1-1-1-2-1-2-1-4-3-5v-1-1c-1-1-1-4-1-5l1-1c-1 0-1 0-1-1-1-1-2-2-3-4h1v-1z" class="AF"></path><path d="M414 262l2-1c1 1 1 0 1 1h1l1 1c-1 1-1 1 0 2h0v3c-1 1-1 2-2 3v2l-2-2-1 2-1 1-1 1h0c-1-2-2-4-3-5l1-2c1-1 1-2 2-4 0-1 1-1 2-2z" class="AA"></path><path d="M410 268h2c0 1 1 1 1 2s-1 2 0 4l-1 1h0c-1-2-2-4-3-5l1-2z" class="AH"></path><path d="M414 262l2-1c1 1 1 0 1 1v1c0 1-1 3-1 4v-1-1h-2l-2-1c0-1 1-1 2-2z" class="W"></path><path d="M418 262l1 1c-1 1-1 1 0 2h0v3c-1 1-1 2-2 3v2l-2-2c0-2 0-3 1-4 0-1 1-3 1-4v-1h1z" class="Q"></path><path d="M418 262l1 1c-1 1-1 1 0 2h0c-1 1-1 1-2 0v-2-1h1z" class="e"></path><path d="M392 205v1 1c1 1 2 1 2 1h1l1 1v1h23l-2 1-47 1c4-2 7-2 11-2l3-1h1 1c3 0 4-2 6-4z" class="AY"></path><path d="M392 205v1 1c1 1 2 1 2 1h1l1 1v1h-15l3-1h1 1c3 0 4-2 6-4z" class="Aj"></path><path d="M418 563l2-3c3 8 5 15 9 22 1 2 3 4 3 6v1c1 1 1 1 1 2l2 2v2c1 1 1 2 2 3 0 1 1 2 1 3-1 0-1-1-1-2l-9-15c0-1-1-1-2-1 0-1-1-3-2-5-2-4-4-9-6-15z" class="Aa"></path><path d="M361 292l3-8 1-3 1 2c-2 7-6 13-8 20-3 7-6 14-9 20-2-2-3-4-4-6l-1-3c0-4-2-8-3-11 0-2-1-4-2-5h1l1-1 1 4v2s1 1 1 2 1 3 1 4c0 3 1 6 2 9h0c1 1 1 2 2 3 1-1 1 0 1-1l1-2c1-1 0-1 1-2v-1c1-1 2-4 3-5v-2-3c1 0 2-2 2-3 2-4 4-7 5-10z" class="AD"></path><path d="M452 585l-1-3c0-1-1-2-1-4h-1v-2c-1-2-2-4-2-5l-6-15-7-19v-1c1 1 1 1 1 2h0l1 1v2s1 1 1 2v1l1 1c0 2 1 4 2 6v1l1 1v2l1 1 1 2v1c1 1 1 3 2 4v-2l-1-2-3-8-2-7v-1c-1-1-1-1-1-2l16 41-1 3h-1z" class="f"></path><path d="M374 268c1 0 2 0 2-1 1-1 1 0 3 0-6 6-10 11-10 19h-1l-1-1-1-2-1-2v-5c-1-2-3-3-5-4 1 0 2 0 3 1h1c4-1 7-2 10-5z" class="Ag"></path><path d="M406 215h7 0l-1 1c2 0 2 0 3-1v1c2 0 5 0 7-1l15-1h1c-2 1-4 1-6 1l-12 2c-2 0-5 0-7 1-2 0-3 0-5 1h-6c-1 1-2 1-3 1-2 0-4 0-5 1l-1-1-1-1h3 3l1-1h2c1 0 2 0 2-1h-5c-1 1-2 0-3 1h-2-1v1l-2-1c3-1 6-2 8-2 3 0 5 0 8-1z" class="h"></path><path d="M376 249l4-3h1l-5 5c-3 1-5 4-7 6h-3c-1 0-1 0-2-1l-2 1v1h-4l1-1-1-1v-1-1h2l1-1 1-1c3 0 8-4 11-5v1l-3 3c1-1 1 0 2-1 0 0 1 0 2-1h2z" class="AD"></path><path d="M363 254c1 0 2-1 3-1v1c-1 1-3 2-4 3h0v1h-4l1-1-1-1v-1-1h2l1-1 2 1z" class="I"></path><path d="M361 253l2 1-1 1c-1-1-1-1-2-1l1-1z" class="r"></path><path d="M376 249l4-3h1l-5 5c-3 1-5 4-7 6h-3c-1 0-1 0-2-1 5-2 8-4 12-7z" class="I"></path><path d="M532 699c1-9 5-19 9-28l10-25v1l-1 2v1c-1 3-2 3-1 6 1 1 1 3 0 4s0 1-1 1l-1 1c-1 2-2 2-2 4-1 1-1 2-1 3h0c-1 2-1 3-1 4l-1 3h-1v2l-1 1h0c-1 1 0 1-1 1v3h0c0 1-1 2-1 2-1 1-1 0-1 2l-1 1c-1 0-1 1-1 2v1l-1 3c-1 1-1 2-1 3v3l-1-1z" class="f"></path><path d="M430 253v-1s1-1 1-2c0 0 2 1 3 1h1v4l-3 3c-1 1-2 3-3 5-1 0-2 1-3 1l-1 1v-1c-1 0-2-1-2-1-1-1 0-2 0-4h0v-2h-1l1-1v-1l1-1h0c2 0 3-1 5-2l1 1z" class="AI"></path><path d="M426 257l2 1c0 1 0 2-1 3 0-1-1-2-2-3l1-1z" class="n"></path><path d="M429 254h1c1-1 2-1 3-2v1c-1 1-1 1-2 1-1 1-2 3-3 4l-2-1c1-2 1-2 3-3z" class="AH"></path><path d="M429 252l1 1-1 1c-2 1-2 1-3 3l-1 1c-1-1-1-2-1-4h0c2 0 3-1 5-2z" class="e"></path><path d="M423 255l1-1c0 2 0 3 1 4s2 2 2 3l-1 3-1 1v-1c-1 0-2-1-2-1-1-1 0-2 0-4h0v-2h-1l1-1v-1z" class="AG"></path><path d="M423 259c2 2 2 3 2 5-1 0-2-1-2-1-1-1 0-2 0-4z" class="AI"></path><path d="M369 257c2-2 4-5 7-6v1h-1c0 1-1 1-1 2h0l-2 3h1l2-3 1 1h1v-1c1 0 2-1 3-1l-10 9c-1 1-2 2-4 2l1 1c-2 1-3 1-5 1v-1l3-1h0c-3 0-7 0-9-2l-2-1 3-1h-1l8-1 2-2h3z" class="z"></path><path d="M366 257h3c-1 1-2 2-3 2h-2l2-2z" class="g"></path><path d="M365 408l1 1v-4c1 1-1 5 1 6v-4l1 13 11 56v4c0 1 0 1 1 2v2l-12-53-1-4-1-10-1-9z" class="AZ"></path><path d="M365 408l1 1v-4c1 1-1 5 1 6v-4l1 13v1 4 2-2-1c0-1-1-2-1-2 0-2 0-3-1-4v-1l-1-9z" class="O"></path><path d="M331 285c1 1 3 6 4 8 2 1 3 3 4 5 1 1 2 3 2 5 1 3 3 7 3 11l1 3c1 2 2 4 4 6v1c-1 1-2 1-2 3h0c-1-1-1-2-2-3h0c-1-1-2-1-2-2 0-2-1-4-1-6l-11-31z" class="b"></path><path d="M335 293c2 1 3 3 4 5 1 1 2 3 2 5-1-1-1-2-2-3v-1 3l-4-9z" class="AF"></path><path d="M345 317c1 2 2 4 4 6v1c-1 1-2 1-2 3h0c-1-1-1-2-2-3 0-2-1-5 0-7z" class="t"></path><path d="M339 302v-3 1c1 1 1 2 2 3 1 3 3 7 3 11h-1c-2-2-4-9-4-12z" class="I"></path><path d="M485 728c1 1 1 1 2 3 1 1 2 1 2 3v1 1h1v2c1 1 3 2 3 4 1 0 0 0 1 2l1 3h1c0-1-1-2-1-2v-2h0l-1-1 1-1c0 1 1 2 1 3s1 3 2 4c0 1 1 3 1 4 2 2 4 4 4 6v1c1 1 1 1 1 3 1 2 4 3 5 6v3c0-1-1-2-1-3h0c-1-1-1-2-2-3l-1-1v3l1 2v1l1 1c0 1 1 2 1 4h0c1 1 1 3 2 4s2 2 2 3v1 1l-1 4-1-2h1v-2h0c0-2 0-3-1-4s-1-2-2-2c0-2-1-2-2-3v-2c0-1 0-2-1-2 0-1 0-2-1-3v-2l-1-1c-1-1-1-2-1-3s0-1-1-2c-2-3-3-7-5-10s-4-8-6-11c-2-4-4-8-5-11z" class="f"></path><path d="M426 583c1 0 2 0 2 1l9 15c0 1 0 2 1 2 0 1 1 1 1 3l2 4v1c1 1 1 1 1 2l1 1c0 1 1 3 0 4 1 1 2 2 2 4l1 1c0 1 1 2 1 3-1-1-3-4-4-5v-1c-1-1-2-1-2-1-2-1-3-6-4-7l-6-17h0l-3-5-2-5z" class="AE"></path><path d="M426 583c1 0 2 0 2 1l9 15c0 1 0 2 1 2 0 1 1 1 1 3l2 4v1c1 1 1 1 1 2l1 1c0 1 1 3 0 4h0c-2-3-3-8-5-11 0-2-2-3-3-4-2-4-1-6-4-8h0l-3-5-2-5z" class="I"></path><path d="M415 271l2 2c0 1-1 1-1 2 1 0 2-1 2-2 2 1 2 1 3 3-1 1-1 2-1 3l-1 1-1 1c-2 1-2 1-3 2h-1v2h1v2c0 2 1 2 0 4l-3-3v5l-1-1-1 1h-1v-3c1-2 1-4 1-6 1-1 1-1 1-2s-1-1-1-2h0v-1l2-2v-2l1-1 1-1 1-2z" class="AG"></path><path d="M412 275l1-1c1 1 1 2 2 4h-1c-1 0 0 0-1 1l-1 1h-1c1-2 1-2 1-3v-2z" class="u"></path><path d="M410 284c1 1 1 1 1 3v1h1v5l-1-1-1 1h-1v-3c1-2 1-4 1-6z" class="AH"></path><path d="M414 283v-3c1-1 1-2 2-3h1v1c1 0 2 1 2 2l-1 1c-2 1-2 1-3 2h-1z" class="Y"></path><path d="M324 374v-1l9 13c1 2 2 3 4 5 2 4 5 7 6 12-1 0-1-1-2-2h-1l-1 2-6-8c-1-3-4-5-6-7 1 0 2 1 3 2l3 3v-1h-1c0-2-1-3-2-4v-1l-2-2h1l-3-3 2-1c-1-2-3-5-4-7z" class="h"></path><path d="M328 381l6 8 5 8c1 1 1 3 2 4h0-1 0c0-3-3-5-5-7-2-3-4-7-6-9l-3-3 2-1z" class="M"></path><path d="M329 385c2 2 4 6 6 9 2 2 5 4 5 7h0l-1 2-6-8c-1-3-4-5-6-7 1 0 2 1 3 2l3 3v-1h-1c0-2-1-3-2-4v-1l-2-2h1z" class="g"></path><path d="M309 252h1c-1 2-3 4-5 5l-2 3-1 4c-2 0-3 0-5 2h-2v-2h0l-1-1c-1-3-5-5-7-6l-2-1c1-1 1-1 2-1h5l1 1h5l1-1c4 0 7-1 10-3z" class="L"></path><path d="M297 261c1 0 2 0 3-1 0-1 0-2 1-2 2-1 2-1 4-1l-2 3-1 4c-2 0-3 0-5 2h-2v-2c0-2 1-2 2-3z" class="H"></path><path d="M285 256c1-1 1-1 2-1 5 1 8 2 12 2l1 1-1 1h-3v1l1 1c-1 1-2 1-2 3h0l-1-1c-1-3-5-5-7-6l-2-1z" class="AT"></path><path d="M142 219c1 0 2 0 3-1h1c1 1 2 1 3 1h1c7 3 15 6 22 10 5 1 9 3 14 4l-1 2-4-1c-2-1-3-1-5-2h0c-3-2-5 0-8-1l-15-7c-4-1-7-4-11-4v-1z" class="g"></path><path d="M315 333l4 3 10 16c1 2 3 4 4 6l3 6c0 1 1 2 1 3 1 1 1 0 1 1 0 2 1 2 2 3l3 12c-1-1-1-2-2-3 1-2-3-5-4-8l-1-1c0-1 0-1-1-2 0-1-1-2-1-3-1-2-3-3-3-5v-1h-1v-2c-1-1-2-2-2-3-2-3-4-6-6-10-2-3-5-7-7-12zm-19-96c2 1 4 2 5 3h1l5 5c-1 1-3 3-5 4v1c-1 1-1 1-2 1h-1c-1 1-4 1-6 1l-1-1h-3 0l-2-1h-1l-2-1h-1c-1 0-2-1-2-1h-1c-1 0-1 0-2-1h-1c-1 0-2 0-2-1-1 0-2-1-3-1s0 0-1-1h-1-1-1c-1-1-2-1-3-2 3 0 5 1 8 2 2 1 3 2 5 3h4l2 1h8 1l1-1h3c1 0 0 0 1-1l-2-1h1c2 0 3-1 5-2h0c-1-2-3-3-5-5l-1-1z" class="AF"></path><path d="M296 237c2 1 4 2 5 3h1l1 3v1c-1 1 0 0 0 2h-1c-1 0-3 3-5 3-1 0-2 0-4 1-3 1-5 0-8-2h-1 8 1l1-1h3c1 0 0 0 1-1l-2-1h1c2 0 3-1 5-2h0c-1-2-3-3-5-5l-1-1z" class="I"></path><path d="M444 608l2 1c0 2 2 5 3 8 1 1 1 2 2 4v1h1v2l1 1v2l2 4v1c1 0 1 1 2 2 0 1 1 1 1 2l1 2 1 2 1 1c0 1 0 1 1 2 0 2 1 2 1 4l5 10 1 3 3 4c0 1 0 1 1 2v1 1l4 8c1 1 1 2 2 3s1 2 1 4c0 1 0 2-1 3v-2c-1-1 0-3-1-5 0 0-1-1-1-2h-1v-1-1c-2-3-4-6-4-9h-1v-1l-2-4-7-15c-1-2-2-3-2-5h0l-2-3-2-2v-2l-2-2h0c-1 4 5 8 5 12h0l-1-1c0-1-1-1-1-2s0 0-1-1v-1c0-1-1-2-2-3v-1h0l-1-2-2-6c0-1-2-3-3-4s0-2-1-4h-1c0-1 0-2 1-2h0l1-1c-1-1-1-1-1-2l-1-1-2-5z" class="AE"></path><path d="M357 271l3 1c2 1 4 2 5 4v5l-1 3-3 8c-1 3-3 6-5 10 0 1-1 3-2 3h0 0-1c0-2 2-6 3-8 0-1 1-3 1-5 2-5 3-11 1-15-1-2-1-3-1-5v-1z" class="s"></path><path d="M356 297c0 1 0 2-1 3v3h0c1-2 1-3 2-4 0-1 0-2 1-3s2-3 3-4c-1 3-3 6-5 10 0 1-1 3-2 3h0 0-1c0-2 2-6 3-8z" class="d"></path><path d="M322 286c2 0 2 0 3 1l1 1 1 1c2 2 6 12 6 15v1 1 1l-1-1h-1l-3-3v-2c-1 0-1 1-1 2l-1-1v-1c-1 0-2-1-2-1h0l-1-1-1-1c-1-3-4-6-6-8 0-1 0-1 1-2v-1h1c1-1 3 0 4-1z" class="s"></path><path d="M321 289c1 0 2 0 2 1l3 6v1 1c-2-3-4-6-5-9z" class="M"></path><path d="M468 558c0-1 0-2-1-4l-1-1 1-1c0 1 0 1 1 2h1l3 6 5 12h1c1 1 1 2 1 3 1 0 1 0 2 1h0l5 5h-2l-1-1c-1 1-2 1-2 1l4 12v1c1 1 2 6 3 7l1 4-2-2-3-6c0-2-3-7-4-9l-4-11c-2-5-4-9-6-14l-2-5z" class="V"></path><path d="M477 572h1c1 1 1 2 1 3 1 0 1 0 2 1h0l5 5h-2l-1-1c-1 1-2 1-2 1-1-3-3-6-4-9z" class="W"></path><path d="M304 359c0 1 3 3 3 4l7 5c4 5 8 10 12 14l3 3h-1l2 2v1c1 1 2 2 2 4h1v1l-3-3c-1-1-2-2-3-2-2-2-5-4-7-6s-3-3-5-4c-3-2-3-3-5-5l-6-9v-5z" class="V"></path><path d="M328 385c-5-4-11-9-14-14-2-2-6-6-7-8l7 5c4 5 8 10 12 14l3 3h-1z" class="f"></path><path d="M318 346c2 1 3 2 4 4l2 3c2 2 4 5 5 8 1 1 1 1 1 2 1 1 2 2 3 4v1h0l2 3c1 2 3 5 4 8 0 1 0 2 1 3l1 2h1v-1c-1-1-1-2-1-3 1 1 1 2 2 3s1 2 1 4v1l-2 1h-1l-1-1c-2 0-3-4-5-6 0-1-1-3-2-4l-1-1c-1 0-1 0-1-1l1-1 1 1v-1l-3-7h1c-2-2-2-5-4-6-1-1-2-3-3-4-1-4-6-7-6-11v-1z" class="h"></path><path d="M318 347c4 4 7 9 10 14 2 3 4 6 5 9l-2-2c-2-2-2-5-4-6-1-1-2-3-3-4-1-4-6-7-6-11z" class="Aa"></path><path d="M331 368l2 2 7 14 4 3v1l-2 1h-1l-1-1c-2 0-3-4-5-6 0-1-1-3-2-4l-1-1c-1 0-1 0-1-1l1-1 1 1v-1l-3-7h1z" class="I"></path><path d="M340 384l4 3v1l-2 1-2-5z" class="r"></path><path d="M333 375l7 13c-2 0-3-4-5-6 0-1-1-3-2-4l-1-1c-1 0-1 0-1-1l1-1 1 1v-1z" class="d"></path><path d="M335 382c2 2 3 6 5 6l1 1h1l2-1 2 5c1 3 1 6 3 8h0v1c1 0 1 1 1 2v-1l1-2 1 8h0-1v2 1h-1v1c0 1 0 3 1 5h-1c-2-5-4-10-7-15-1-5-4-8-6-12 0 0 1 0 2-1-1-2-3-5-4-8z" class="V"></path><path d="M335 382c2 2 3 6 5 6l1 1 2 5c0 1 0 3 1 4v1c2 3 2 7 4 10v1c-1-1-2-3-2-4l-1-1c0-1-1-2-1-3-1-5-4-7-5-12-1-2-3-5-4-8z" class="M"></path><path d="M344 388l2 5c1 3 1 6 3 8h0v1c1 0 1 1 1 2v-1l1-2 1 8h0-1v2 1c-2-7-5-13-8-18l-2-5h1l2-1z" class="AE"></path><path d="M344 388l2 5-2-1c-1-1-2-2-2-3l2-1z" class="g"></path><path d="M349 401h0v1c1 0 1 1 1 2v-1l1-2 1 8h0-1l-2-8z" class="V"></path><defs><linearGradient id="Av" x1="389.981" y1="214.915" x2="382.019" y2="236.085" xlink:href="#B"><stop offset="0" stop-color="#05070a"></stop><stop offset="1" stop-color="#222628"></stop></linearGradient></defs><path fill="url(#Av)" d="M399 220h0 5v1c-1 0-1 0-2 1v1l-4 1-6 1-4 2-9 2c-2 1-5 3-8 3-1 0-2-1-3 0h-2-1l6-3 1-1h1v-1c1-1 2-1 3-2-1 0-3-1-4-1 6-1 12-3 18-3 1 0 2 0 3-1l1 1c1-1 3-1 5-1z"></path><path d="M431 241l12-9 1 6v2l-2 2v3l-1 1c-1 0-2 0-3-1v2l-2-1c0 2-1 2-3 4l1 1c-1 0-3-1-3-1 0 1-1 2-1 2v1l-1-1-2-2-3-2 1-1 3-3 1-1 2-2z" class="u"></path><path d="M432 245h2v1h1 1c0 2-1 2-3 4l-1-1 1-1-1-1v-2z" class="Q"></path><path d="M436 246c0-2 1-2 2-3v-2c0-2 0-2 1-3h1l2-2h0v2 1c1 0 0 0 1-1h1v2l-2 2v3l-1 1c-1 0-2 0-3-1v2l-2-1z" class="J"></path><path d="M431 241h1c1 0 1-1 2-1l1 1 1 2h-1c-1 1-2 1-3 1v1h0v2l1 1-1 1 1 1 1 1c-1 0-3-1-3-1 0 1-1 2-1 2v1l-1-1-2-2-3-2 1-1 3-3 1-1 2-2z" class="AO"></path><path d="M428 244l1-1c1 2 1 3 1 5h-1c0-1-1-2-1-4z" class="AR"></path><path d="M432 247l1 1-1 1 1 1 1 1c-1 0-3-1-3-1 0 1-1 2-1 2v1l-1-1-2-2-3-2 1-1 3 3s1 0 2-1l1-1 1-1z" class="u"></path><path d="M424 450l3 9c0 1 2 3 2 5 0 1 2 3 3 5 1 1 1 3 2 4l2 7c1 1 1 2 2 3 0 1 0 2 1 3l1 2h1l1 2c0 1 0 2 1 3l4 10v1l1 4c1 1 1 0 1 1s1 3 1 4c1 1 1 2 1 2 1 1 1 3 1 4 1 2 2 3 2 5l-4-4-2-5s-2-3-2-4c-1-2-1-5-2-7s-2-4-2-5c-3-5-5-11-7-16-1-3-3-6-3-9h0c-1-1-1-2-2-3v-2c0-1-1-2-1-3 0 0-1-1-1-2-1-4-4-9-4-14z" class="r"></path><path d="M448 515c1 1 2 1 3 3l1 1c1 2 2 3 2 5l-4-4-2-5z" class="AD"></path><path d="M443 486l6 16c2 1 3 1 5 1h1v1l1 2h-1l2 2s0 1 1 0h1v2 5l-1 2h0v2l-1 1h0v4c1 1 1 1 1 2l-1-1-1 1h-1c0-1 0-1-1-2 0-2-1-3-2-5 0-1 0-3-1-4 0 0 0-1-1-2 0-1-1-3-1-4s0 0-1-1l-1-4v-1l-4-10c-1-1-1-2-1-3s0-3 1-4z" class="s"></path><path d="M449 502c2 1 3 1 5 1h1v1l1 2h-1v-1h-1c-1 2-1 4 0 7v1c1 1 1 3 3 4v-1l1 1v2l-1 1c-4-5-6-12-8-18z" class="X"></path><path d="M457 516v1c-2-1-2-3-3-4v-1c-1-3-1-5 0-7h1v1l2 2s0 1 1 0h1v2 5l-1 2h0l-1-1z" class="AH"></path><path d="M457 516c-1-2-1-3-1-6l1-1 2 1v5l-1 2h0l-1-1z" class="v"></path><path d="M208 227l12 3 28 9 27 10c6 2 11 4 17 5l1 2-1-1h-5c-1 0-1 0-2 1l-3-2-1 3s-1-1-2-1l-4-1c-1-1-2-3-5-3h0c-1-1-2-1-3-3l-1-1-30-12-19-6c-3 0-5-2-8-2h-1v-1z" class="U"></path><path d="M266 248c5 2 11 4 16 6l-1 3s-1-1-2-1l-4-1c-1-1-2-3-5-3h0c-1-1-2-1-3-3l-1-1z" class="I"></path><path d="M444 569c1 2 2 2 2 4l1 1v1c0 2 0 0 1 2 0 0 0 1 1 2v2c1 1 1 2 2 3 0 1 0 2 1 3v2c1 1 1 2 2 4l1 2c1 2 1 3 2 5l1 2 3 6 4 10 1 2c2 5 3 9 6 13l4 10c1 2 1 3 2 5 2 3 2 9 5 13 0 2 0 4 1 6v1l2 2h0c0 3 0 3 2 5h0c0 1 0 3-1 4h0c0-2 0-3-1-4l-1-1v-3l-3-6v-1c-1-1-1-1-1-2h0l-1-1c0-1 1-2 0-3v-1c0-1 0-2-1-2v1-1l-1-1c0-2 0-3-1-4l-2-3s0-1 1-1l-1-1c-1-1-1-2-2-3v-5h-1c-1-4-3-7-4-10v-1c-1-2-2-4-2-5-1-1-1-2-1-3-1 0 0 0-1-1v-1c-1-1-1-1-1-2-1-1-1 0-1-1v-1c-1-2-2-4-3-5v-1h-1 0l-2-2c0-3-3-6-4-9v-1-1h-1v-1c-1-1-1-2-1-2-1-1 0-1-1-2 0-1-1-2-2-4l-1-3v-1l-1-1v-1l1-1-2-4h1l-1-4z" class="g"></path><path d="M444 573h1c2 4 13 30 13 31-3-3-5-9-7-13l-3-6c-1-3-1-5-2-8l-2-4z" class="p"></path><defs><linearGradient id="Aw" x1="418.59" y1="542.096" x2="444.91" y2="542.904" xlink:href="#B"><stop offset="0" stop-color="#283035"></stop><stop offset="1" stop-color="#4f5669"></stop></linearGradient></defs><path fill="url(#Aw)" d="M424 530l-2-2v-4c-1-1-1 0-1-2h0v-1c-1-1-2-1-2-2v-2-2l-1-1c0-1 1-2 0-2v-1c1 0 1 1 1 2l1-2c0 1 1 1 1 3v1l1 1c0 1 1 3 2 4 0 3 2 5 3 8l17 41 1 4h-1c-3-4-5-10-7-14l-5-12-3-6c0-1 0-3-1-4 0-1-1-2-2-4 0-1 1 0 0-1s0-1-1-2h0-1z"></path><path d="M412 288l3 3v1s-1 1-1 2 1 3 2 4c0 1 0 2-1 3v1c-1 1-2 2-3 2l1 1h1v1c0 1 0 2-1 3l2 2-1 1h-2v3l-1-1c0 1-1 1-2 2l-1 1c0 1 0 2-1 2-1 1-1 2-1 3l-1 1c-1-1-2-1-2-2v-1h2v-4-1-7h-1l1-1h2l-1-1c1-2 1-4 2-6h0v-3c1-2 1-3 1-4h1l1-1 1 1v-5z" class="Q"></path><path d="M412 312c0-1 1-2 0-3v-1l1 1 2 2-1 1h-2zm0-24l3 3v1s-1 1-1 2 1 3 2 4c0 1 0 2-1 3v1c-1 1-2 2-3 2v-1c0-2-1-4-2-5 0 1 0 1-1 2v-1c0-1 1-2 2-2 0-1 0-1 1-1l-1-1c0-1 1-2 1-2v-5z" class="x"></path><path d="M408 300c0 1 1 2 1 2v2h2c1 1 1 3 1 4l-1 1h0-1l-1 1c0 2 1 2 2 4 0 1-1 1-2 2l-1 1c0 1 0 2-1 2-1 1-1 2-1 3l-1 1c-1-1-2-1-2-2v-1h2v-4-1-7h-1l1-1h2l-1-1c1-2 1-4 2-6z" class="q"></path><path d="M409 305h1v2l-1 1-1-1v-1l1-1z" class="AO"></path><path d="M405 308l2 3h0c-1 2-1 3-1 4h-1v-7z" class="i"></path><path d="M437 509s1 1 1 2l1 2v4l1 1c0 1 1 1 1 3v1l1 2c1 1 0 1 1 2 1 2 2 5 3 7l1 5 1 1v1c0 1 1 1 1 2v1l2 6 1 3 1 4c0 1 1 1 1 2 0 0 0 1 1 2 0 1 0 2 1 4 1 0 1 1 1 2h1 0l1-1-1-1v-1h-1l1-1-1-1v-1-3h-1 0c-1-1-1-1-1-2-1-1 0 0 0-1h-1c0-2 0-2-1-3l1-2 1 1v-2c0 1 1 2 1 3v1l1 1v3l1 1h0c0 1 1 2 2 3v3 1-1l-1 1v3l-1 1c1 1 1 3 1 4 1 2 1 4 2 5v1 1 2h-1c-1-5-3-8-5-12 0-2-1-3-1-4s0-1-1-2h0v-1c0-1-1-2-1-3h0l-1-2c0-1-1-2-1-3v-1l-1-3c0-1-1-2-1-3h0c0-1-1-2-1-2-1-2-1-3-1-5-1-4-4-7-5-11-1-2-1-4-2-6v-1c0-1 0-2-1-3 0-1-1-2-1-3v-1-1c-1-1-2-1-2-3l1 1 1-1v-2z" class="h"></path><path d="M420 263c1 0 2 1 3 0 0 0 1 1 2 1v1l1-1c1 0 2-1 3-1v5c0 1 1 3 1 5h1c1 1 1 1 2 1-1 2-3 2-2 4l1 1-2 1-2-2-1-1c-3 2-5 5-6 7v1h-2c-2 1-2 2-4 2v-2h-1v-2h1c1-1 1-1 3-2l1-1 1-1c0-1 0-2 1-3-1-2-1-2-3-3 0 1-1 2-2 2 0-1 1-1 1-2v-2c1-1 1-2 2-3v-3h0l1-2z" class="a"></path><path d="M420 267v1l3 3v1h-1c-1 0-2 0-2 1h-1-1c0 1-1 2-2 2 0-1 1-1 1-2v-2c1-1 1-2 2-3l1-1z" class="k"></path><path d="M420 263c1 0 2 1 3 0 0 0 1 1 2 1v1l1 1h-1s-1 1-1 2l-1-1h-3l-1 1v-3h0l1-2z" class="X"></path><path d="M426 276c0-1 0-2-1-3 0 0 0-1 1-1 1-1 1-1 2-1 0 1 0 1 1 2h1 1c1 1 1 1 2 1-1 2-3 2-2 4l1 1-2 1-2-2-1-1c-3 2-5 5-6 7v1h-2c-2 1-2 2-4 2v-2h-1v-2h1c1-1 1-1 3-2l1-1 1-1c0-1 0-2 1-3 2-1 2-1 5 0z" class="AJ"></path><path d="M414 283h1c1-1 1-1 3-2v2c-1 1-2 2-3 2h-1v-2z" class="x"></path><path d="M426 276c0-1 0-2-1-3 0 0 0-1 1-1 1-1 1-1 2-1l-1 1c0 2 2 2 1 4v1l-2-1z" class="AI"></path><path d="M421 276c2-1 2-1 5 0l-3 3h-3c0-1 0-2 1-3z" class="Ak"></path><path d="M364 316h0l3-8h0l1-1-1-1c0-1-1-1 0-1v-2-1l1-1v-1h0l1 1v-2h1c0-1 1-1 1-2 0 0 1-1 1-2h1c0-3 2-6 3-8 1-3 3-9 6-10v1l1 1c0 2-3 5-4 8 0 1 0 1-1 2-4 5-5 15-7 22 0 1-1 5-1 6-1 1-1 2-2 3l-1 2v3c-1 1-1 3-2 4h0l-2 2h0v1c0-2 0-4-1-6 0-1 1-5 2-7v-3z" class="M"></path><path d="M312 242h0l-1-1c-5-6-15-8-22-10-10-4-19-7-29-10-15-3-30-5-44-5-7 0-14-1-20 0h-8 0c1-1 5 0 6 0h41c3 1 5 0 8 1l17 3 20 6c3 1 7 3 10 3 8 4 19 7 28 6 5 0 9-1 13-1-2 2-4 4-7 4h-1-1-2c-2 1-2 2-3 3s-1 2-2 2l-1-1h-2z" class="l"></path><path d="M314 242v-2c2 0 2 0 3 1-1 1-1 2-2 2l-1-1z" class="t"></path><path d="M475 619l1 1 1 1s0 1 1 2v1s1 1 1 2c1 3 3 6 4 10 1 1 1 2 2 3 0 1 1 2 1 3s0 0 1 1c0 1 0 2 1 3v3c1 1 1 2 2 4v1c1 1 1 1 2 3v2c0 1 0 1 1 2v1c1 1 1 2 2 3 1-2-1-3 0-5-1-1-1-1-1-2-1-2-2-3-2-5 0-1 0-2-1-3s-1-2-1-3l-1-1v-2h0c-1-1-1-2-2-3v-3c-1-1-1-1-1-2-1-1-1-2-2-2l1-1c2 3 3 6 5 10l1 5c1 1 1 2 2 3v1 2c1 1 1 1 2 3s1 5 2 8v3l1 3 1 4c1 1 2 3 2 5 1 2 3 4 3 7 1 1 1 2 2 3l1 5c1 1 1 3 2 4 0 1 1 3 1 4 1 1 1 2 1 2 0 3 2 6 3 9v1l1 2v2l1 2v3c2 3 3 6 3 10-1-1-1-2-1-2l-1-3c-1-1-1-1-1-2l-1-5h-1v-2l-1-5-1-2v-2l-1-2-1-1c0-1-1-3-1-4-1-2-2-4-2-6v-1l-1-1c0-1 0-1-1-2v-1-1l-1-1v-1l-1-1v-2c-1-1-1-2-2-3 0-2-2-4-2-6l-1-2c0-1 0-1-1-2v-1c-1 1-1 3-1 4l-1-1c0-1 0-3 1-3l1-1c-1-1-1-1-1-2s0-1-1-2-1-1-1-2l-2-2c0-1 0-1-1-2v-2h0c-1-1-2-2-2-3l-1-1h1v-1h0c0-1-1-2-1-2v-2s-1-1-2-1v-1l1-1c-1-1-1-2-2-2-1-1-1-2-1-3l1-1c-1-1-1-2-2-3h0v-1c0-1 0-1-1-2 0-1-1-2-1-4h0c-1-1-1-2-2-3l-1-3-3-7z" class="d"></path><path d="M191 222c8-1 17-1 26-1h13c12 0 26 1 37 6l-19-2-5-1c-2 1-4 1-5 2-2 0-4-1-6 0l4 1c-2 0-3 1-4 1-2 0-2 0-3 1l-3-1c-2 0-3-1-5-2-1 0-2 0-3-1-6 0-11-2-17-2l-10-1z" class="AF"></path><path d="M218 225v-1h4c6-2 12-1 18 0h3c-2 1-4 1-5 2-2 0-4-1-6 0l4 1c-2 0-3 1-4 1-2 0-2 0-3 1l-3-1c-2 0-3-1-5-2-1 0-2 0-3-1z" class="f"></path><path d="M240 224h3c-2 1-4 1-5 2-2 0-4-1-6 0s-4 0-5 0c3-2 7 0 10-1 1-1 2-1 3-1z" class="AE"></path><path d="M221 226h5 1c1 0 3 1 5 0l4 1c-2 0-3 1-4 1-2 0-2 0-3 1l-3-1c-2 0-3-1-5-2z" class="I"></path><path d="M399 297l2-2h1v3 2 2l2 2-1 1h0 1 1v2l-1 1h1v7 1 4h-2v1c0 1 1 1 2 2 0 1-1 4-1 4v3 2 2 9h-3v1 1c-1-1-2-10-2-12-1-5-2-11-1-16 0-1 0-1-1-2v-3-2l2-13z" class="Y"></path><path d="M405 316c-1 0-2 1-2 2h-1-1v-11c0-1 1-1 1-1l2 1v1h1v7 1z" class="AR"></path><path d="M400 333v-6-1-2l2-1v-2l1-1v1c0 1 1 1 2 2 0 1-1 4-1 4v3 2 2 9h-3v1 1c-1-1-2-10-2-12h1z" class="AB"></path><path d="M400 333c1 1 1 2 1 4h0c1 2 3 4 2 5l-2 1v1 1c-1-1-2-10-2-12h1z" class="AA"></path><path d="M475 542c0-1 1-2 2-3 1 2 2 4 4 6h0 1c-1 1-1 2-1 3v1c0 2-1 2-2 4l-1 3c0 1 1 2 1 3v1c0 2 1 3 1 5h-1l-1 1 1 1c1 2 2 3 4 5-1 1-2 1-3 1l-1 2c0-1 0-2-1-3h-1l-5-12-3-6c-1-3-3-5-3-8v-1-1c1-1 1-2 2-2l1-2h1l1 2h0 2c0 1 1 1 1 2l1-1v-1z" class="Q"></path><path d="M469 554c2 0 3 1 4 2v1l1-2 2 2c-1 1-1 1-1 3v3h0c-2-1-2-3-3-4v1l-3-6z" class="AB"></path><path d="M472 560v-1c1 1 1 3 3 4h0v-3l1 1c1 1 1 1 2 3l1 1-1 1 1 1c1 2 2 3 4 5-1 1-2 1-3 1l-1 2c0-1 0-2-1-3h-1l-5-12z" class="q"></path><path d="M476 561c1 1 1 1 2 3l1 1-1 1 1 1c1 2 2 3 4 5-1 1-2 1-3 1l-1 2c0-1 0-2-1-3v-1c-1-2-1-3-1-5v-2c0-1-1-2-1-3z" class="m"></path><path d="M468 542l1-2h1l1 2h0 2c0 1 1 1 1 2 1 1 1 2 1 2v1 3l-1 1c-1 0-3-1-3-2l-2-4c-1 0-2 1-3 1v-1-1c1-1 1-2 2-2z" class="k"></path><path d="M468 542c1 0 1 0 1 1 1 2 2 4 4 5-1 1-1 1-2 1l-2-4c-1 0-2 1-3 1v-1-1c1-1 1-2 2-2z" class="a"></path><path d="M475 542c0-1 1-2 2-3 1 2 2 4 4 6h0 1c-1 1-1 2-1 3v1c0 2-1 2-2 4l-2-1v-2c-1-1-1-2-1-3l-1-1s0-1-1-2l1-1v-1z" class="J"></path><path d="M355 279v-1-1c-2-7-10-11-16-15 8 3 16 5 24 6h5c3 0 5-2 8-3l-2 3c-3 3-6 4-10 5h-1c-1-1-2-1-3-1l-3-1v1c0 2 0 3 1 5 2 4 1 10-1 15 0 2-1 4-1 5-1 2-3 6-3 8-1 1-2 2-2 3v1c-1 1-1 1-1 3v2 1l-1 1v-1-1-5c1-2 0-4 1-6 0-1 1-2 1-3l-1-2v-1c1 0 1 1 2 2v-1-2l1-1c0-1 1-2 1-3h-1c0-1-1-2-1-2-1 1-1 2-1 3s-1 2-2 4v-1h-1c1-1 2-2 2-3l-1-1 1-1c0-1 0-2 1-3h1 0c1 1 1 1 2 1-1 0-1-2-1-2h-1l-1 1-1-1h0c1-2 2-2 3-2v-1l-1-3c2 2 2 5 3 7 0-3 1-6 0-9z" class="AD"></path><path d="M353 270l4 1v1c0 2 0 3 1 5 2 4 1 10-1 15v-1c-1-1-1-2-1-3 1-2 1-8 0-10v-1l-1-1v-1c0-2-1-2-2-4l-1-1h1z" class="z"></path><path d="M368 268c3 0 5-2 8-3l-2 3c-3 3-6 4-10 5h-1c-1-1-2-1-3-1l-3-1-4-1 1-1h0c2 1 4 1 6 2v-1l3-2h5z" class="f"></path><path d="M363 268h5c-3 2-5 2-8 2l3-2z" class="V"></path><path d="M473 200c8 5 16 10 25 13 0 2 2 3 1 5-2 2-5 4-7 7 0 2 0 4 1 6 0 2 1 4 1 6-1-2-2-3-2-5-1-3-2-7 0-10l6-6c-1-1 0-1-2-1v-1c-1-1-2-1-4-1-5-2-9-3-14-4-9-1-18 1-26 1l-35 1 2-1h-23v-1c2-1 2-1 4-1h2 1 1 1c2 1 3 1 5 1v-2c1 0 1 1 2 1l1 1 2-1h1 2 1c1 0 1 1 2 0 3 0 6 1 9 0 2 1 4 0 7 0h4 6 6c7-1 11-3 17-5 1-1 1-2 3-3z" class="AU"></path><path d="M470 203l1 1h2c-1 1-2 1-3 1-6 1-10 3-16 4-11 1-22-1-32 1h-3-23v-1c2-1 2-1 4-1h2 1 1 1c2 1 3 1 5 1v-2c1 0 1 1 2 1l1 1 2-1h1 2 1c1 0 1 1 2 0 3 0 6 1 9 0 2 1 4 0 7 0h4 6 6c7-1 11-3 17-5z" class="AD"></path><path d="M397 315c1 1 1 1 1 2-1 5 0 11 1 16 0 2 1 11 2 12l1 5v1 1l1 7c0 1 0 2-1 3l2 9 35 112 1 4 1 1h-1l-1-2c-1-1-1-2-1-3-1-1-1-2-2-3l-2-7c-1-1-1-3-2-4-1-2-3-4-3-5 0-2-2-4-2-5l-3-9c-3-8-5-15-6-23-1-2-2-6-3-9-3-11-7-23-10-34-2-7-3-14-4-21s-3-14-4-21c0-3-1-6 0-9 1-5 0-12 0-18z" class="g"></path><path d="M418 427c1 3 3 7 3 10 1 1 1 2 2 3l1 4v1c1 1 1 1 1 2v1l1 1c0 1 1 3 1 4l2 5c0 1 1 2 1 3 0 2 1 2 1 4 1 1 1 1 1 3v1c-1-2-3-4-3-5 0-2-2-4-2-5l-3-9c-3-8-5-15-6-23z" class="Aa"></path><path d="M397 315c1 1 1 1 1 2-1 5 0 11 1 16 0 2 1 11 2 12l1 5v1 1l1 7c0 1 0 2-1 3-3-9-4-19-5-29 1-5 0-12 0-18z" class="V"></path><defs><linearGradient id="Ax" x1="352.252" y1="395.871" x2="381.165" y2="382.943" xlink:href="#B"><stop offset="0" stop-color="#0a0806"></stop><stop offset="1" stop-color="#1b2830"></stop></linearGradient></defs><path fill="url(#Ax)" d="M365 356c1 0 1-1 1-1 1-2 3-5 4-6v4c0 3-1 6-2 9l-1 8v6 31 4c-2-1 0-5-1-6v4l-1-1 1 9 1 10h-2v-1c-2-9-3-18-4-27l-1-1c-1-3-1-6 0-9 0-8 0-19 3-27l1-7 1 1z"></path><path d="M360 389c1 3 1 7 1 10l-1-1c-1-3-1-6 0-9z" class="M"></path><path d="M364 355l1 1c0 2-1 6-2 7v-1l1-7z" class="r"></path><path d="M365 408c0-9-1-20 1-29v-4l1 1v31 4c-2-1 0-5-1-6v4l-1-1z" class="AY"></path><path d="M494 632v-2c2 2 3 4 4 6l9 21c3 7 6 14 10 21h0l2 4 1-1h0c1 0 1 0 1 1l-1-4 1-2h1c0 1 2 2 2 3h2c1 1 2 1 3 1v1l1 2c-1 1-1 1-1 2v2 1h1v1l-3 6h-1c1 1 1 2 1 3l-1 20v13h-1v10l-1 3v11l-1 2v1c0 1 1 4 0 5l-1 1c1 1 1 1 1 2v2 2h-1l-1-1c0-1 1-2 1-3h0c-1-2-1-4-1-5v-2-2c-1-1-1-1-1-2s1-3 1-4 0-2 1-3v-1l-1-2c1-1 1-2 2-2v-7-1c1-3 0-10 0-13 1-3 1-10 1-13 1-2 0-4 0-6 0-1 1-3 0-4v-3h0v-1c0-1 0-1-1-2 0-1-1-1-1-2l-2-3-1-2c-2-3-4-6-5-10 0-1-2-3-2-4-1-1-1-2-1-3l-3-5-1-4-1-1-1-2c0-1 0-1-1-2l-1-2c0-1 0-2-1-3l-1-2v-2c-1 0-1-1-2-2v-1l-3-6h0c-1-2-2-3-2-5z" class="d"></path><path d="M524 723c2 3 1 5 1 8v10l-1 3v-7-14z" class="V"></path><path d="M521 761v-2s0-1 1-2v-5c1-2 1-4 1-6l-1-1 1-1v-1c1-1 0-4 1-6v7 11l-1 2v1c0 1 1 4 0 5l-1 1c1 1 1 1 1 2v2 2h-1l-1-1c0-1 1-2 1-3h0c-1-2-1-4-1-5z" class="M"></path><path d="M524 723l1-21v-6c0-1 1-1 1-1 1 1 1 2 1 3l-1 20v13h-1c0-3 1-5-1-8z" class="r"></path><path d="M521 676h1c0 1 2 2 2 3h2c1 1 2 1 3 1v1l1 2c-1 1-1 1-1 2v2 1h1v1l-3 6c-2-1-1-1-1-2-1-2-3-3-4-5-2-3-4-6-5-10h0l2 4 1-1h0c1 0 1 0 1 1l-1-4 1-2z" class="V"></path><path d="M521 682l4 1 1 1h-1v3h0c-2-1-3-3-4-5z" class="x"></path><path d="M525 684l1 1 2 1 1-1v2 1c-1 0-2 1-3 1l-1-2h0v-3z" class="q"></path><g class="X"><path d="M525 684l1 1 1 2h-2v-3z"></path><path d="M521 676h1c0 1 2 2 2 3h2c1 1 2 1 3 1v1l1 2c-1 1-1 1-1 2l-1 1-2-1-1-1h1l-1-1-4-1-1-4 1-2z"></path></g><path d="M524 679h2c1 1 2 1 3 1v1l1 2c-1 1-1 1-1 2l-1 1-2-1-1-1h1v-1c1-1 1-1 0-2s-1-1-2-1v-1z" class="AO"></path><path d="M376 209h8l-3 1c-4 0-7 0-11 2H200l-1 1c-17-2-34 0-50-2h0 10 20l100-1h1v-1h12 7 48 9 20z" class="AZ"></path><path d="M562 685h1v4c0 2-1 5-2 7-1 1-1 2-1 3h0l2-2v1l-1 1v1c-1 1-1 1-1 2v1l-1 1v1 1h0c-1 1-1 2-1 3l-20 54c0 2-1 4-2 5-1 2-2 3-2 4v1l-1 1c-1 3-4 11-6 12-1 1-2 2-4 3l1-1c2-5 4-9 5-13l21-59h0c2-4 4-9 6-13-2 3-4 5-5 7v-2l3-5 1-2 2-4c2-2 3-4 4-7h0l1-5z" class="Aa"></path><path d="M562 685h1v4c0 2-1 5-2 7v-1c1-1 0-1 0-2l-1 3h-1c1-2 2-4 2-6h0l1-5z" class="AE"></path><path d="M370 317c-1 3-1 6 0 9v1h0 2l1 4h-1c1 3 2 6 2 8v1c0 3-2 7-3 9h-1c-1 1-3 4-4 6 0 0 0 1-1 1l-1-1-1 7c-3 8-3 19-3 27-1 3-1 6 0 9l2 20h0c-1-1 0-1 0-2-1-2-1-2-1-3v-1c0 2-1 3 0 4v4l-1-2v-3c0-2 0-4-1-5v2h0l-1-1v-3c0-1-1-2-1-3-1-8-1-17-1-25l3-26h0c1-1 1 0 1-1l1-5v-2h0l2-8v-1l1-3c1-2 1-3 1-5h0c1-1 1-3 2-4v-3l1-2c1-1 1-2 2-3z" class="V"></path><path d="M370 327h2l1 4h-1l-2-4z" class="t"></path><path d="M367 348v-1c1-1 1-2 1-3 1-2 1-3 2-5l1 1h2l1-1v1c-2 2-3 3-5 6l-2 2z" class="s"></path><path d="M367 348l2-2c2-3 3-4 5-6 0 3-2 7-3 9h-1c-1 1-3 4-4 6 0 0 0 1-1 1l-1-1 3-7z" class="I"></path><path d="M570 597h1l-20 49-10 25c-4 9-8 19-9 28v7c0 4 0 10 1 15 1 1 0 4 1 6 0 1 0 1 1 2v4h-1v-1-2c-1-1-1-2-1-3v-2c-1-2 0-4-1-5 0-2 1-6 0-8 0-3-1-14 0-15 0-2 0-3 1-4v-3c0-1 0-2 1-3 0-1 1-3 1-4l1-1c0-1 0-2 1-2l-1-1h0c0 1-1 2-1 2l-1 1c0 1-1 3-1 4-1 1-1 1-1 3h0v1l-1 1c-1 1-1 1-1 2v1 1 1c-1 2-1 4 0 6 0 1 0 2 1 3 0 2-1 5 0 7v4c0 2-1 6 0 8-1 1 0 2 0 3 0 2 1 13 0 15 0 3 1 4 0 7v1c0 1-1 1-1 2h0v2c-1 3 0 6-1 9v1h0-1c1-4 1-7 1-11-1 1-1 3-2 4v-4-4c-1-2-1-4-1-5v-13-13l1-20c0-1 0-2-1-3h1l3-6v-1h-1v-1-2c0-1 0-1 1-2l-1-2v-1l1-2 1 1 1-1h3c1 0 2-3 2-3v-2c1-2 2-3 2-5l1-3 1-1 3-6 1-3-2-2 1-1c0-1 1-2 1-3 1 0 2-1 2-1v-2l2-1 2-4c1-3 3-6 4-9l4-10 11-25z" class="Aj"></path><path d="M547 646l2-1c-1 3-3 6-4 10l-2-2 1-1c0-1 1-2 1-3 1 0 2-1 2-1v-2z" class="AI"></path><path d="M532 678h3l-2 5c-1 2-2 3-4 4v-2c0-1 0-1 1-2l-1-2v-1l1-2 1 1 1-1z" class="AH"></path><path d="M530 689c0 3-1 5-1 7-1 2-1 5-1 7 2 7 2 14 2 21v17 6c-1 2-1 4-1 6-1 1-1 3-2 4v-4-4c-1-2-1-4-1-5v-13-13l1-20c0-1 0-2-1-3h1l3-6z" class="Aa"></path><path d="M527 753l3-6c-1 2-1 4-1 6-1 1-1 3-2 4v-4z" class="AF"></path><path d="M526 731v-13c1 2 0 4 1 6l1 1c1 7 0 16-1 24-1-2-1-4-1-5v-13z" class="Ag"></path><path d="M530 689c0 3-1 5-1 7-1 2-1 5-1 7v22l-1-1c-1-2 0-4-1-6l1-20c0-1 0-2-1-3h1l3-6z" class="AQ"></path><path d="M360 398l1 1c1 9 2 18 4 27v1h2l1 4 12 53v2l1 2v2l-2 1c0-3-2-7-3-10-3-7-5-14-7-21h-2l-2-6-2-4-1-5-8-27 1-1c1 3 1 6 3 8v-1-2c-1-2-1-4-2-5v-4h1v-8c0 1 1 2 1 3v3l1 1h0v-2c1 1 1 3 1 5v3l1 2v-4c-1-1 0-2 0-4v1c0 1 0 1 1 3 0 1-1 1 0 2h0l-2-20z" class="M"></path><path d="M362 445c1 1 2 2 3 4h0l4 11h-2l-2-6-2-4-1-5z" class="Ag"></path><path d="M362 445c1 1 2 2 3 4v5l-2-4-1-5z" class="p"></path><path d="M360 398l1 1c1 9 2 18 4 27v1h2l1 4c-1 1-1 2-1 3v1 2l2 11 3 10c0 1 1 2 0 4h0c-4-14-7-30-10-44l-2-20z" class="h"></path><path d="M365 427h2l1 4c-1 1-1 2-1 3l-1-3c0-2 0-3-1-4z" class="s"></path><path d="M354 418l1-1c1 3 1 6 3 8v-1-2c-1-2-1-4-2-5v-4h1l8 36h0c-1-2-2-3-3-4l-8-27z" class="O"></path><path d="M166 220s1-1 2 0c3 0 7 2 10 2h1 4 3l3 1c1 0 1 0 2 1h4l3 1h3l2 1h2 0c1 1 2 1 3 1v1h1c3 0 5 2 8 2l19 6 30 12 1 1c1 2 2 2 3 3h0c5 4 10 9 13 14v3h-1v-1c-1-1-1 0-2-1-1 0-2-1-2-2-1-2-3-4-5-5-6-6-11-10-19-13l-8-2c-3-1-5-2-8-3l-6-3-1 1h0-1-2 0c0 1 2 1 2 3h0l-2-1-1 1c-4-2-8-5-12-6-1 0 0 0-1-1h-2-1c0-1 0-1-1-1-3 0-5-1-7-2l3-1-29-8c-2 0-4 0-6-1l-2-1c-1-1-2-1-3-2z" class="d"></path><path d="M228 240l-6-3c3-1 8 1 10 2l-1 1h0-1-2 0z" class="I"></path><path d="M179 222h4 3l3 1c1 0 1 0 2 1 0 1 1 0 3 1-2 1-3 1-6 0h-2l-5-1-2-2z" class="s"></path><path d="M206 232c6 1 11 3 16 7 2 1 4 1 6 3h0l-1 1c-4-2-8-5-12-6-1 0 0 0-1-1h-2-1c0-1 0-1-1-1-3 0-5-1-7-2l3-1z" class="AD"></path><path d="M236 236l30 12 1 1c-2 0-4 0-6-1h1l-1-1c-1-1-3-1-4-1h-2c-1-1-2-1-3-1-2-1-4-2-7-3-1 0-1 0-1-1h0c-1-1-3-2-4-2-1-1-2-1-4-3z" class="h"></path><path d="M251 216c2-1 4-1 6-1 1 1 3 1 5 1 0 1 3 0 4 0h1 5 1 3 54l1-1h29c1-1 5 0 7 0h20c3 0 7 0 9-1h2l-1 1h-1c2 1 4 0 5 0h1 4c-3 1-5 1-8 1-2 0-5 1-8 2h-4l-9 1c-5 2-11 2-16 3-5 0-10 2-15 2s-10-1-15-1h-9c-3-1-6-1-9-1v-1l2-3h-13-19-17c-4 0-8 1-12 0l-1-1h-1l-1-1z" class="M"></path><path d="M364 216c6-1 12 0 17-1 4 0 9 0 13 1-5 1-9 1-14 2-2 0-5 0-7-1-1-2-6-1-9-1z" class="f"></path><path d="M364 216c3 0 8-1 9 1-5 0-11 1-17 1h-7v-1h-37c6-1 12 0 18 0l34-1z" class="AL"></path><path d="M252 217c7 1 16 0 24 0h36 37v1h-34-13-19-17c-4 0-8 1-12 0l-1-1h-1z" class="j"></path><path d="M349 218h7c6 0 12-1 17-1 2 1 5 1 7 1h-3-1l1 1c-5 2-11 2-16 3-5 0-10 2-15 2s-10-1-15-1h-9c-3-1-6-1-9-1v-1l2-3h34z" class="r"></path><path d="M404 371l3 1 2 9 1 6h1l1-1c0 1 0 2 1 3h2l-1 1c0 3 1 4 3 6 0 1 0 2-1 3v2c0 1 0 1 1 2h2v1l1 1h-2v1l2 1h0l1-1c1-1 1-1 3-1 0 0 0 1 1 2 0 2 1 6 3 7h1v3c1 1 1 1 1 2s0 2 1 2v2 2l1 1 1 2v-1l1 1-1 1 1 1-2 2c2 2 2 3 2 5-2 2 0 5-2 6h0c0 1-1 3 0 4h-2l-2-2 5 13c1 2 2 4 3 5v1l1 2 1 7h0l3 7 2 6c-1 1-1 3-1 4l-1-2-1-1-1-4-35-112z" class="Aj"></path><path d="M410 387h1l1-1c0 1 0 2 1 3h2l-1 1c-1 1-2 1-2 2l-2-5z" class="k"></path><path d="M414 390c0 3 1 4 3 6 0 1 0 2-1 3v2c0 1 0 1 1 2h2v1l1 1h-2l-1-1v1c0 1 0 2-1 3-1-3-2-6-2-8-1-3-2-5-2-8 0-1 1-1 2-2z" class="q"></path><path d="M414 400v-1-3l1-1c1 2 0 4 1 6 0 1 0 1 1 2h2v1l1 1h-2l-1-1v1c0 1 0 2-1 3-1-3-2-6-2-8z" class="Y"></path><path d="M416 408c1-1 1-2 1-3v-1l1 1v1l2 1h0l1-1c1-1 1-1 3-1 0 0 0 1 1 2 0 2 1 6 3 7h1v3l-2-1-2 2h0l-1 2 1 1-2 1v2 1c-1-1-1-1-1-2h-1 0c-1-1-3-9-3-10l-2-5z" class="X"></path><path d="M424 405s0 1 1 2c-1 1-2 2-3 2l1-2-2-1c1-1 1-1 3-1z" class="Z"></path><path d="M422 409c1 0 2-1 3-2 0 2 1 6 3 7h1v3l-2-1c-2 0-3 0-4-1l-1-1 1-2c-1-1-2-2-2-3h1z" class="v"></path><path d="M418 413c0-2 0-2-1-3l1-1 1 1c0 2 0 2 1 3h1c-1 2 0 3-1 3 1-1 2-1 2-2l1 1c1 1 2 1 4 1l-2 2h0l-1 2 1 1-2 1v2 1c-1-1-1-1-1-2h-1 0c-1-1-3-9-3-10z" class="x"></path><path d="M422 414l1 1c1 1 2 1 4 1l-2 2h0l-1 2s-1 0-1-1c-2 0-2-2-3-3 1-1 2-1 2-2z" class="Z"></path><path d="M423 415c1 1 2 1 4 1l-2 2h0c-1-1-2-1-2-3z" class="AG"></path><path d="M427 416l2 1c1 1 1 1 1 2s0 2 1 2v2 2l1 1 1 2v-1l1 1-1 1 1 1-2 2c2 2 2 3 2 5-2 2 0 5-2 6h0c0 1-1 3 0 4h-2l-2-2c0-1-1-2-1-3l-3-10-3-9h0 1c0 1 0 1 1 2v-1-2l2-1-1-1 1-2h0l2-2z" class="Z"></path><path d="M430 427l2-1 1 2v-1l1 1-1 1h-1l-1 1c-1-1-1-2-1-3z" class="AJ"></path><path d="M423 424v-2l2-1v1c1 1 1 2 1 4 1 0 1 1 2 2h2l-1 1h-1 0c-1 1 0 1-1 2-1-2-1-3-2-4h0c-1-1-1-1-2-3z" class="n"></path><path d="M421 423h0 1c0 1 0 1 1 2v-1c1 2 1 2 2 3h0c0 3 0 4 2 6v2h-1c0-1-1-1-1-2s0-1-1-2v1l-3-9z" class="q"></path><path d="M424 432v-1c1 1 1 1 1 2s1 1 1 2h1v-2c1 0 2 0 3 2h0c-1 1-1 2 0 2h-2c-1 1-1 1-1 2v3l-3-10z" class="m"></path><path d="M427 442v-3c0-1 0-1 1-2h2c1 2 1 3 2 6 0 1-1 3 0 4h-2l-2-2c0-1-1-2-1-3z" class="c"></path><path d="M427 416l2 1c1 1 1 1 1 2s0 2 1 2v2 2l1 1-2 1-2 1c-1-1-1-2-2-2 0-2 0-3-1-4v-1l-1-1 1-2h0l2-2z" class="AC"></path><path d="M427 416l2 1c1 1 1 1 1 2s0 2 1 2v2 2h-1c-1 0-2-1-3-2 1-1 2-2 2-3-2-2-2-1-4-2l2-2z" class="Z"></path><defs><linearGradient id="Ay" x1="393.386" y1="554.665" x2="428.709" y2="556.619" xlink:href="#B"><stop offset="0" stop-color="#010101"></stop><stop offset="1" stop-color="#273137"></stop></linearGradient></defs><path fill="url(#Ay)" d="M391 515h1s0 1 1 2l1 1v1c1 0 2 1 3 2l1 1 1-1 1 1h1 1l1 1h1c1 1 2 1 3 1l-3-3c-1 0-1-1-1-2l-2-2v-1l1 1c0 1 1 1 2 2v1c1 1 2 1 3 2 1 0 1 1 2 2l1 1c-1 1-1 1-1 2h-2-1v1h1 3v1c1 0 1 0 2 1 0 1 0 1 1 2h0v2s0 1-1 1v2c0 1 0 1 1 2-1 1-1 2-1 4v1 1h0l1 2h0c-1 1 0 3 1 5 0 1 0 2 1 4 0 2 1 5 3 7h0c2 6 4 11 6 15 1 2 2 4 2 5l2 5h-1l-3-3v-1h-1c-1-1-1-3-1-4l-3-3c0 1 1 2 2 3 0 1 0 2 1 3-3-3-5-9-7-13-2-1-3-5-5-8v1l-2-1c-1-2-2-4-4-5l-2-5c-2-4-4-7-5-11-2-4-2-8-3-13v-1l-2-2h0l2-1v-1-1l-4 1 1-1c1-1 1-2 2-3l-2-4z"></path><path d="M410 532l-1-1c0-1 0-2-1-2l1-1 1 1 2 3h-1-1z" class="V"></path><path d="M399 536c0-1-1-1 0-3l1 1h0c0 2 1 2 1 3 1 1 1 1 1 2l-2-2v-1h-1z" class="d"></path><path d="M402 540c1 0 2 1 3 2v3c0 2 2 4 3 6v2h0l-1-1-3-6v-1c-1-2-1-3-2-4v-1z" class="M"></path><path d="M393 519c2 1 3 3 6 4-3 2-4 0-5 4l-2-2h0l2-1v-1-1l-4 1 1-1c1-1 1-2 2-3z" class="l"></path><path d="M399 536h1v1l2 2v1 1l-1 1c0 2 1 4 1 6-1 2 1 2 0 4-2-4-4-7-5-11l3 6v-4c-1-2-1-4-1-7z" class="z"></path><path d="M411 559c1 2 2 3 3 6 2 5 6 9 8 15l-3-3c0 1 1 2 2 3 0 1 0 2 1 3-3-3-5-9-7-13-1-4-4-7-4-11z" class="I"></path><path d="M402 548c0-2-1-4-1-6l1-1c1 1 1 2 2 4v1l3 6 1 1 3 6c0 4 3 7 4 11-2-1-3-5-5-8v1l-2-1c-1-2-2-4-4-5l-2-5c1-2-1-2 0-4z" class="Aa"></path><path d="M402 548c0-2-1-4-1-6l1-1c1 1 1 2 2 4v1h-1v1l1 4h-1s0-2-1-3z" class="r"></path><path d="M243 224l5 1 19 2 29 10 1 1c2 2 4 3 5 5h0c-2 1-3 2-5 2h-1l2 1c-1 1 0 1-1 1h-3l-1 1h-1-8l-2-1h-4c-2-1-3-2-5-3-3-1-5-2-8-2-2-2-7-3-10-5-1 0-4-1-5-1l-10-3c-4-1-8-3-11-4 1-1 1-1 3-1 1 0 2-1 4-1l-4-1c2-1 4 0 6 0 1-1 3-1 5-2z" class="Ab"></path><path d="M297 240c1 1 2 2 4 3h-2l-1 1h-2c0-2 0-3 1-4z" class="AU"></path><path d="M294 239h2l1 1c-1 1-1 2-1 4l-1-1h0l-2-2c-1 0-1 1-1 0l1-1 1-1z" class="Ag"></path><path d="M276 233l9 3c4 1 6 1 9 3l-1 1c-2-1-4-2-6-2h-2l-11-3 2-2z" class="AU"></path><path d="M272 236v-1h2l11 3h2c2 0 4 1 6 2l-1 1c0 1 0 0 1 0l2 2h0l-5-1-5-1h-1c-2-1-5-2-7-3s-3-1-5-2z" class="O"></path><path d="M274 238c2 1 4 1 6 2 2 0 3 1 4 1l9 3h2l1 1h0l2 1c-1 1 0 1-1 1h-3l-1 1h-1c-2-1-4-1-7-2v-3c-4-1-7-2-11-4h0v-1z" class="AY"></path><path d="M285 243h1c2 1 4 3 6 3 1-1 1-1 1-2h2l1 1h0l2 1c-1 1 0 1-1 1h-3l-1 1h-1c-2-1-4-1-7-2v-3z" class="AZ"></path><path d="M265 236c2 0 4 1 5 1 2 1 3 1 4 1v1h0c4 2 7 3 11 4v3c3 1 5 1 7 2h-8l-2-1-6-3-3-3h-1c0-1 0 0-1-1v-1l-7-2 1-1z" class="Ag"></path><path d="M273 241c2 1 5 2 7 3 2 0 4 1 5 2 3 1 5 1 7 2h-8l-2-1-6-3-3-3z" class="Ab"></path><path d="M256 236c1-1 2-1 3-1 1 1 2 1 3 2h2l7 2v1c1 1 1 0 1 1h1l3 3 6 3h-4c-2-1-3-2-5-3-3-1-5-2-8-2-2-2-7-3-10-5h2l-1-1z" class="f"></path><path d="M256 236c1-1 2-1 3-1 1 1 2 1 3 2h0c-2 1-3 0-5 0l-1-1z" class="Ab"></path><path d="M262 237h2l7 2v1c1 1 1 0 1 1-4-1-7-2-10-4h0z" class="p"></path><path d="M252 229l1-1 1 1c1 0 1-1 2-1l5 1c5 1 9 2 14 4h1l-2 2h-2v1c0 1-1 1-2 1s-3-1-5-1l-1 1h-2c-1-1-2-1-3-2l1-1 1 1v-1l-1-1 2-1-2-1h-1c-1 0-2-1-3-1-2-1-3 0-4-1z" class="Ag"></path><path d="M252 229l1-1 1 1c1 0 1-1 2-1l5 1h-2v1h1c2 0 4 1 6 2-2 0-4-1-6-1h-1c-1 0-2-1-3-1-2-1-3 0-4-1z" class="Ai"></path><path d="M260 231c2 0 4 1 6 1 1 1 3 1 4 2l2-1h0 3 1l-2 2h-2v1c-3-1-7-2-10-4l-2-1z" class="AZ"></path><path d="M260 233l2-1c3 2 7 3 10 4 0 1-1 1-2 1s-3-1-5-1l-1 1h-2c-1-1-2-1-3-2l1-1 1 1v-1l-1-1z" class="AE"></path><path d="M260 234l1 1 4 1-1 1h-2c-1-1-2-1-3-2l1-1z" class="AL"></path><path d="M232 226c2-1 4 0 6 0l14 3c1 1 2 0 4 1 1 0 2 1 3 1h1l2 1-2 1 1 1v1l-1-1-1 1c-1 0-2 0-3 1l1 1h-2c-1 0-4-1-5-1l-10-3c-4-1-8-3-11-4 1-1 1-1 3-1 1 0 2-1 4-1l-4-1z" class="I"></path><path d="M252 234c-2 0-3 0-4-2 1 0 2 0 4 1v1z" class="Ac"></path><path d="M252 233c2 1 5 1 8 1l-1 1c-1 0-2 0-3 1h0c-1-1-3-1-4-2v-1z" class="p"></path><path d="M241 228l7 2h-1l-1 1c-3-1-6 0-9-1h-1-2l1-1c3 1 5 0 8 0l-2-1z" class="f"></path><path d="M236 227c1 0 3 1 5 1l2 1c-3 0-5 1-8 0l-1 1h0c2 1 4 1 6 3-4-1-8-3-11-4 1-1 1-1 3-1 1 0 2-1 4-1z" class="AF"></path><path d="M232 226c2-1 4 0 6 0l14 3c1 1 2 0 4 1 1 0 2 1 3 1h1l2 1-2 1c-2-2-6-2-8-3h-4l-7-2c-2 0-4-1-5-1l-4-1z" class="AL"></path><path d="M235 216h5c2 0 2 0 4-1 2 0 4 1 6 1h1l1 1h1l1 1c4 1 8 0 12 0h17 19 13l-2 3v1c3 0 6 0 9 1h9c2 1 3 1 4 1h0c-2 0-2 1-4 1l2 1h4 4v1c-2 1-3 0-5 2 2 0 3 1 4 2l-9 3c-4 0-8 1-13 1-9 1-20-2-28-6-3 0-7-2-10-3-6-2-13-4-20-6l-17-3c-3-1-5 0-8-1z" class="AE"></path><path d="M295 225h0c-1-1-3-1-4-1-2-1-3-1-5-1 3 0 7-1 9 0l14 1c3-1 6-1 9-1h4 9c2 1 3 1 4 1h0c-2 0-2 1-4 1l2 1h4c-2 1-4 1-6 2s-6 2-9 2h-2-2v-2h-2c-2 1-2 1-4 0h0c-2-1-3-1-4-2-4-2-9 1-13-1z" class="AL"></path><path d="M295 223l14 1h1c1 0 1 0 2 1-5 0-11 0-16-1l-1-1z" class="AZ"></path><defs><linearGradient id="Az" x1="323.721" y1="221.712" x2="318.681" y2="231.479" xlink:href="#B"><stop offset="0" stop-color="#5f6676"></stop><stop offset="1" stop-color="#797b88"></stop></linearGradient></defs><path fill="url(#Az)" d="M308 226c3 0 20 1 22 0v-1h1l2 1h4c-2 1-4 1-6 2s-6 2-9 2h-2-2v-2h-2c-2 1-2 1-4 0h0c-2-1-3-1-4-2z"></path><path d="M318 228c2 0 3 1 5 1l-1 1h-2-2v-2z" class="b"></path><defs><linearGradient id="BA" x1="321.929" y1="241.89" x2="303.071" y2="216.11" xlink:href="#B"><stop offset="0" stop-color="#343744"></stop><stop offset="1" stop-color="#4d5357"></stop></linearGradient></defs><path fill="url(#BA)" d="M288 225h7c4 2 9-1 13 1 1 1 2 1 4 2h0c2 1 2 1 4 0h2v2h2 2c3 0 7-1 9-2s4-1 6-2h4v1c-2 1-3 0-5 2 2 0 3 1 4 2l-9 3c-4 0-8 1-13 1-9 1-20-2-28-6h0v-1h-2 0l1-1c-1 0-2 0-3-1h1l1-1z"></path><path d="M337 226h4v1c-2 1-3 0-5 2-3 1-7 2-11 2l-8 1h-5l3-1s1-1 2-1h1 2 2c3 0 7-1 9-2s4-1 6-2z" class="Ae"></path><g class="AZ"><path d="M318 230h2 2l-5 2h-5l3-1s1-1 2-1h1z"></path><path d="M288 225h7c4 2 9-1 13 1 1 1 2 1 4 2h0c2 1 2 1 4 0h2v2h-1c-1 0-2 1-2 1l-3 1c-1-1-3-1-4-1-3 0-6-1-9-2-2 0-4-2-7-2l-4-2z"></path></g><path d="M307 229l-1-1h6c2 1 2 1 4 0h2v2h-1c-1 0-2 1-2 1l-3 1c-1-1-3-1-4-1l-1-2z" class="O"></path><path d="M307 229l-1-1h6c2 1 2 1 4 0h2v2h-1-5c0-1-1-1-2-1h-3z" class="AQ"></path><path d="M322 320h-1v-2c-1-1-1 0-1-2l-1-1-4-7-6-10c-1-1-1-1-1-2v-1h0c-2-2-2-2-2-4h1l6 4 2 2 1 1h0 0-1v1c1 0 2 1 2 1l5 4 1 1c2 1 3 3 5 4s3 3 5 3c1 1 5 5 6 5 1-1 2-1 3-1 0 2 1 4 1 6 0 1 1 1 2 2h0c1 1 1 2 2 3h0 0v7c0-1-1-2-2-3v3c0 4 0 8-1 12v2-1c-1 1-1 1-1 2l-1-2c0 1-1 2-1 3 1 1 2 2 1 3l3 7c0 2 0 3 1 5h0c1 3 1 6 1 9-1-1-1-2-1-3h-2c-1-4-2-8-4-13-3-9-7-18-11-26-2-4-4-9-7-12z" class="V"></path><path d="M333 312c1 1 5 5 6 5 1-1 2-1 3-1 0 2 1 4 1 6 0 1 1 1 2 2h0c1 1 1 2 2 3h0 0v7c0-1-1-2-2-3-1-9-6-13-12-19z" class="AY"></path><path d="M342 316c0 2 1 4 1 6l-4-5c1-1 2-1 3-1z" class="h"></path><defs><linearGradient id="BB" x1="326.611" y1="336.285" x2="336.196" y2="333.63" xlink:href="#B"><stop offset="0" stop-color="#171c1e"></stop><stop offset="1" stop-color="#2e353e"></stop></linearGradient></defs><path fill="url(#BB)" d="M317 309l-7-11v-1l1 1c0 1 1 1 2 2 1 2 2 4 4 6h0l1-2c9 10 16 20 19 32l5 11c0 1-1 2-1 3 1 1 2 2 1 3l3 7c0 2 0 3 1 5h0c1 3 1 6 1 9-1-1-1-2-1-3h-2c-1-4-2-8-4-13-3-9-7-18-11-26-2-4-4-9-7-12l-2-5h0l-3-6z"></path><path d="M333 337c1 1 1 1 1 2 0-1 1-2 1-2v4 1l-2-5z" class="r"></path><path d="M317 309c1 1 1 1 2 1v-1c-1-1 0 0 0-1v1c1 1 1 2 2 3h0v1c1 1 1 2 1 3v1c1 0 1 1 1 2l-3-4h0l-3-6zm20 27l5 11c0 1-1 2-1 3 1 1 2 2 1 3 0 0-1-3-1-4-3-4-4-8-4-13z" class="z"></path><path d="M320 315l3 4c2 4 5 9 7 13 1 1 2 4 3 5l2 5 8 22c1 2 1 1 2 2 0 0 1 4 1 5h0-2c-1-4-2-8-4-13-3-9-7-18-11-26-2-4-4-9-7-12l-2-5z" class="AL"></path><path d="M186 233c2 1 4 1 5 1h5 2c1 1 3 0 4 1 1 0 1 0 2 1h6v-1c1 0 1 0 1 1h1 2c1 1 0 1 1 1 4 1 8 4 12 6l1-1 2 1h0c0-2-2-2-2-3h0 2 1 0l1-1 6 3c3 1 5 2 8 3l8 2c8 3 13 7 19 13 2 1 4 3 5 5 0 1 1 2 2 2v2c-2-1-2-1-4-1v1c0 1 0 2 1 3v1l-1 1c-1-1-2-3-4-4h1c1 3 3 5 4 8l-4-4-1 2h-1l1 1-1 1c-5-3-9-6-13-10-2-1-4-3-6-4-1 0-3-2-4-2s0 0-1-1c-3-1-3-1-6 0-3-2-18-12-22-12-1 0-3-2-4-2l-9-5c-3 0-6-2-9-3-1-1-2-1-3-2h-1c-2 0-3-1-5-1h0v1c-1-1-2-1-2-1l-1-1h0l1-2z" class="O"></path><path d="M231 240l1-1 6 3h-2v1l3 2c1 0 3 1 4 2l2 2h0c-1 0-2-1-2-1-1 0-1 0-2 1l1 1c1 0 1 1 2 1l-1 1c-2-2-4-3-6-5-2-1-5-2-7-4h0c0-2-2-2-2-3h0 2 1 0z" class="r"></path><path d="M231 240l1-1 6 3h-2v1l-5-3z" class="AE"></path><path d="M238 254v-1c-1-1-2-1-2-1-2-1-2-3-2-4 2 1 3 2 4 3h0 1l21 16c5 2 8 5 11 9l1 1-1 1c-5-3-9-6-13-10-2-1-4-3-6-4-1 0-3-2-4-2l-10-8z" class="AF"></path><path d="M260 267c5 2 8 5 11 9l1 1-1 1c-5-3-9-6-13-10 1 0 2 0 2 1 1 0 1 1 2 1 2 1 3 2 5 3-1-2-4-3-6-6h-1z" class="Ab"></path><path d="M243 252l1-1c-1 0-1-1-2-1l-1-1c1-1 1-1 2-1 0 0 1 1 2 1h0c4 2 8 6 11 9 3 1 5 4 8 5v-2l3 2c2 2 3 6 5 7h1c1 3 3 5 4 8l-4-4c-1-1-2-2-3-2-5-3-10-8-14-11s-10-6-13-9z" class="Ac"></path><path d="M256 258c3 1 5 4 8 5v-2l3 2c2 2 3 6 5 7h1c1 3 3 5 4 8l-4-4c-1-1-2-2-3-2-5-3-10-8-14-11 2 0 3 1 4 1 0 1 1 1 2 2 2 2 5 5 8 7l-1-1 1-1c0-1-1-1-1-2-1-1-1-2-2-2l-1 1 1 1h0l-2-1c-1-2-4-3-6-5-1-1-2-2-2-3h-1z" class="Ai"></path><path d="M236 243v-1h2l8 3 8 2c8 3 13 7 19 13 2 1 4 3 5 5 0 1 1 2 2 2v2c-2-1-2-1-4-1v1c0 1 0 2 1 3v1l-1 1c-1-1-2-3-4-4s-3-5-5-7l-3-2v2c-3-1-5-4-8-5-3-3-7-7-11-9l-2-2c-1-1-3-2-4-2l-3-2z" class="AE"></path><path d="M276 268h0l-5-5v-1c2 2 5 2 7 3 0 1 1 2 2 2v2c-2-1-2-1-4-1z" class="p"></path><path d="M236 243v-1h2l8 3c1 1 3 1 5 2 6 3 14 9 16 16l-3-2v2c-3-1-5-4-8-5-3-3-7-7-11-9l-2-2c-1-1-3-2-4-2l-3-2z" class="Ab"></path><path d="M239 245c1-1 1-1 2-1s2 1 3 2l-1 1c-1-1-3-2-4-2z" class="p"></path><path d="M244 246c2 2 6 3 9 5 4 3 8 7 11 10v2c-3-1-5-4-8-5-3-3-7-7-11-9l-2-2 1-1z" class="AU"></path><path d="M186 233c2 1 4 1 5 1h5 2c1 1 3 0 4 1 1 0 1 0 2 1h6v-1c1 0 1 0 1 1h1 2c1 1 0 1 1 1 4 1 8 4 12 6 4 1 9 5 12 8h-1 0c-1-1-2-2-4-3 0 1 0 3 2 4 0 0 1 0 2 1v1l10 8c-1 0 0 0-1-1-3-1-3-1-6 0-3-2-18-12-22-12-1 0-3-2-4-2l-9-5c-3 0-6-2-9-3-1-1-2-1-3-2h-1c-2 0-3-1-5-1h0v1c-1-1-2-1-2-1l-1-1h0l1-2z" class="h"></path><path d="M212 238h0c2 0 5 1 7 3-3-1-5-2-7-3h0z" class="AD"></path><path d="M194 237v-1l4 1 2 1c2 1 4 1 6 1h1l3 2h-1c-1 0-1 0-2 1h-1c-3 0-6-2-9-3-1-1-2-1-3-2z" class="g"></path><defs><linearGradient id="BC" x1="225.822" y1="240.359" x2="226.699" y2="260.64" xlink:href="#B"><stop offset="0" stop-color="#2e333b"></stop><stop offset="1" stop-color="#56606d"></stop></linearGradient></defs><path fill="url(#BC)" d="M207 239c4 1 7 3 10 4 8 3 14 7 21 11l10 8c-1 0 0 0-1-1-3-1-3-1-6 0-3-2-18-12-22-12-1 0-3-2-4-2l-9-5h1c1-1 1-1 2-1h1l-3-2z"></path><path d="M490 628v-3c-1-1-1-1-1-2v-1c-1-1-2-3-2-4l-1-1-1-3-1-1v-1c0-1-1-3-1-4-2-4-3-9-5-13-1-2-2-4-2-6 1 0 2 3 2 5v1l1 1 1 2 1 3 1 1v2l1 1 1 4s0 1 1 2l1 1v2l1 2c1 1 1 1 1 2l1 2 1 2v1c1 1 1 1 1 3h0c1 0 1 0 2-1l1 1v-1l-1-1c-1-4-2-8-4-12v-3-2 1l2 2h0l2 3h1l2-1v1l2 3 1 1-2 2c0 1 1 2 2 3 1 2 4 3 5 5h1 2l1 1 4 7 2 1s0 1 1 2l8 7v2l1 1c1 1 2 2 2 4l2-2 3 3 3 3h0c1 1 2 1 3 1l3 3h-4l1 1v2c1 1 2 1 3 2l-1 3c0 2-1 3-2 5v2s-1 3-2 3h-3l-1 1-1-1-1 2c-1 0-2 0-3-1h-2c0-1-2-2-2-3h-1l-1 2 1 4c0-1 0-1-1-1h0l-1 1-2-4h0c-4-7-7-14-10-21l-9-21c-1-2-2-4-4-6v2-1l-1 2h-1l-1-1-1-4h0z" class="s"></path><path d="M494 616h2l1 2-2 2-1-4z" class="Q"></path><path d="M493 613h1l2 3h-2l-1-3z" class="i"></path><path d="M490 628c2 1 2 3 3 5h-1l-1-1-1-4h0z" class="d"></path><path d="M497 618v1c0 1 1 2 2 3l-2 2-2-4 2-2z" class="m"></path><path d="M494 613l2-1v1l2 3 1 1-2 2v-1l-1-2-2-3z" class="u"></path><path d="M510 655h4c0 1 0 2 1 3v1c-1 0-2 0-3 1h0c-1-2-2-3-2-5z" class="AS"></path><path d="M499 622c1 2 4 3 5 5h1l-1 1h-1-2v1l-2 2-2-7 2-2z" class="W"></path><path d="M505 627h2l1 1 4 7v2l1 3-1 4v1h-1c-2 0-2 0-4 1l-1 1-1-2c0-1-1-3-1-3l-1-3c-2-3-2-6-4-8l2-2v-1h2 1l1-1z" class="X"></path><path d="M505 629l2 1v2h-1-1l-1-1 1-2z" class="c"></path><path d="M507 632c1 1 1 1 1 3h0l1 2-1-1c-2-1-1-2-2-4h1z" class="Y"></path><path d="M505 629c1 0 2 0 3-1l4 7v2c-2-2-2-2-4-2h0c0-2 0-2-1-3v-2l-2-1z" class="x"></path><path d="M501 629c1 0 2 1 3 2l-1 1 1 1h-1-1l1 2v4c-2-3-2-6-4-8l2-2z" class="n"></path><path d="M508 635c2 0 2 0 4 2l1 3-1 4-3-4v-3l-1-2z" class="AA"></path><path d="M509 640l3 4v1h-1c-2 0-2 0-4 1l-1 1-1-2c0-1-1-3-1-3l2-2h1c1 0 1 1 2 0z" class="c"></path><path d="M504 642l2-2h1v1 1l2 1c-1 0-2 1-3 1l-1 1c0-1-1-3-1-3z" class="AN"></path><path d="M512 635l2 1s0 1 1 2l8 7v2l1 1c-1 2-1 3-1 4v3l-1 2c-1-1-1-1-2-1l-3-1h-3-4l-2-4-1-2-1-2 1-1c2-1 2-1 4-1h1v-1l1-4-1-3v-2z" class="q"></path><path d="M519 648h1l1-1c1 1 1 1 1 2h-2-3l2-1z" class="Y"></path><path d="M518 646l1 2-2 1h-2l-1-1 1-2 1 1 2-1z" class="c"></path><path d="M514 640c1 0 2 1 3 1l1 1v4l-2 1-1-1v-3c-1-1-1-2-1-3z" class="AN"></path><path d="M522 649v2l1 1v3l-1 2c-1-1-1-1-2-1v-7h2z" class="AO"></path><path d="M507 649h1c1 0 1-1 2-1l1-1c1 1 2 2 2 3l-2 2-3-1-1-2z" class="AN"></path><path d="M512 635l2 1s0 1 1 2l8 7v2l1 1c-1 2-1 3-1 4l-1-1v-2c0-1 0-1-1-2l-1 1h-1l-1-2v-4l-1-1c-1 0-2-1-3-1h-1 0l-1-3v-2z" class="X"></path><path d="M518 642c1 2 3 3 3 5l-1 1h-1l-1-2v-4z" class="AB"></path><path d="M523 652c0-1 0-2 1-4 1 1 2 2 2 4l2-2 3 3 3 3h0c1 1 2 1 3 1l3 3h-4l1 1v2c1 1 2 1 3 2l-1 3c0 2-1 3-2 5v2s-1 3-2 3h-3l-1 1-1-1-1 2c-1 0-2 0-3-1h-2c0-1-2-2-2-3h-1l-1 2c-3-5-5-10-7-15l-1-2v-1h0c1-1 2-1 3-1v-1c-1-1-1-2-1-3h3l3 1c1 0 1 0 2 1l1-2v-3z" class="AO"></path><path d="M533 674h1 1l2 1s-1 3-2 3h-3v-1c1 0 1-1 2-1l-1-2z" class="W"></path><path d="M528 671h0c1 1 2 2 4 3h1l1 2c-1 0-1 1-2 1h-2c-1-1-1-1-1-2l-1-4z" class="AS"></path><path d="M533 668c1 0 2 0 3-1h1c1 0 1 1 2 1h0c0 2-1 3-2 5l-1-1c0-1 0-1-1-2h-1c-1 0-1-1-2-2h1z" class="Y"></path><path d="M518 665l1-1 1 1c1 0 2 0 2 1v-2l4 2h2v1h-3c-2 1-4 1-6 0v-2h-1z" class="W"></path><path d="M518 665l1-1 1 1c1 0 2 0 2 1h1l-1 1-3-2h-1z" class="c"></path><path d="M525 671l1-1c1 0 1 0 2 1l1 4c0 1 0 1 1 2h2v1l-1 1-1-1-1 2c-1 0-2 0-3-1 1 0 2-1 2-2l-1-1v-2-2l-2-1z" class="x"></path><path d="M512 660c2 1 4 1 6 1h2v1c1 1 2 1 2 2v2c0-1-1-1-2-1l-1-1-1 1-2-2v1l1 1-1 1c-1-1-2-2-2-3v-1l-1 1-1-2v-1h0z" class="AA"></path><path d="M512 660c2 1 4 1 6 1l-1 2c-1-1-1-1-2-1-1-1-2-1-3-1v-1h0z" class="Al"></path><path d="M520 675l-1-2c0-1-1-1-1-2 1-1 1-1 3-1h0c2 0 3 0 4 1l2 1v2 2l1 1c0 1-1 2-2 2h-2c0-1-2-2-2-3h-1l-1-1z" class="AB"></path><path d="M520 675l4-3h2 1v2 2c-1-1-2-2-3-2l-2 2h-1l-1-1z" class="c"></path><path d="M522 676l2-2c1 0 2 1 3 2l1 1c0 1-1 2-2 2h-2c0-1-2-2-2-3z" class="AS"></path><path d="M517 655l3 1c1 0 1 0 2 1v1c1 2 3 2 5 3h0l-2 2v1h1l2 2h-2l-4-2c0-1-1-1-2-2v-1h-2c-2 0-4 0-6-1 1-1 2-1 3-1v-1c-1-1-1-2-1-3h3z" class="q"></path><path d="M517 655l3 1c1 0 1 0 2 1v1l-1 1c-1-1-1-1-1-2-2 0-3 2-3 3l-1-1h-1v-1c-1-1-1-2-1-3h3z" class="k"></path><path d="M514 655h3l1 2c-1 1-2 1-3 1-1-1-1-2-1-3z" class="Al"></path><path d="M523 652c0-1 0-2 1-4 1 1 2 2 2 4l2-2 3 3 3 3h0c1 1 2 1 3 1l3 3h-4l1 1v2c1 1 2 1 3 2l-1 3h0c-1 0-1-1-2-1h-1c-1 1-2 1-3 1h-1c0 1 0 1-1 2-1-1-1-2-2-3h-1v-1l-2-2h-1v-1l2-2h0c-2-1-4-1-5-3v-1l1-2v-3z" class="Q"></path><path d="M523 655c2 1 3 2 5 2l-1 2h-1l-1-1h-2-1v-1l1-2z" class="m"></path><path d="M530 660c1 0 1-1 2-1h3l1 1 1 1v2l-1 1c-1-1-1-1-3-1v-1l-2-1-1-1z" class="Y"></path><path d="M532 659h3c-1 1-1 1-1 2h0c-1 0-2 0-2-1v-1z" class="k"></path><path d="M526 664h-1v-1l2-2c2 1 3 3 5 4 0 0 1 1 1 2v1h-1c0 1 0 1-1 2-1-1-1-2-2-3h-1v-1l-2-2z" class="AS"></path><path d="M526 664h-1v-1l2-2c2 1 3 3 5 4 0 0 1 1 1 2h-1c-1 0-1-1-2-2v-1h-4z" class="k"></path><path d="M523 652c0-1 0-2 1-4 1 1 2 2 2 4l2-2 3 3 3 3h0c1 1 2 1 3 1l3 3h-4l-1-1h-3c-1 0-1 1-2 1-1-1-1-2-2-3h0c-2 0-3-1-5-2v-3z" class="X"></path><path d="M523 652c0-1 0-2 1-4 1 1 2 2 2 4l4 3-1 1c-1 1-1 0-1 1h0c-2 0-3-1-5-2v-3z" class="W"></path><path d="M430 169l-1 3 1 1h0l1 2 2-3c2 1 3 0 4-1l2-1-1 2c1 1 1 1 2 1 2 0 3 0 4 1h2c1 0 1-1 2-1h1v1h4c-2 1-3 3-4 5l-1 5-3 2v3h1c0 2 0 3 1 4l-2 2c0 1-1 1-1 2l-1 4-1 1h0c0 2-1 3-1 4v2h-4c-3 0-5 1-7 0-3 1-6 0-9 0-1 1-1 0-2 0h-1-2-1l-2 1-1-1c-1 0-1-1-2-1v2c-2 0-3 0-5-1h-1-1-1-2c-2 0-2 0-4 1l-1-1h-1s-1 0-2-1v-1-1l8-9 5-4h1l3-3c4-1 9-6 13-8v1 1l1 1 2-9c0-1 1-2 1-2l1 1c0-1 1-1 1-2l2-3z" class="s"></path><path d="M414 188l8-4c0 2-1 2-2 4h-1c-1 1-1 2-2 2v-1h-1v2h-1l-1-3z" class="AA"></path><path d="M410 192c1-2 3-3 4-4l1 3h1v-2h1v1l-1 2c-1 1-2 1-2 2v2h-1l-2-1c1-1 1-2 1-3h1l-1-1-1 1h-1z" class="AR"></path><path d="M422 183l1 1v3c0 3-1 7 1 9h0l-1 1v1h-2l1-2c-1-2-1-2-3-4h-2-1l1-2c1 0 1-1 2-2h1c1-2 2-2 2-4v-1z" class="c"></path><path d="M411 192l1-1 1 1h-1c0 1 0 2-1 3l2 1h1v1h0-2l-2 2c0 1 0 1-1 2l-1-2s0-1 1-2l-3-1h1l-1-2v-1h0l-1-1h1 4 0 1z" class="k"></path><path d="M411 195l2 1h1v1h0-2l-2 2c0-2 0-3 1-4z" class="AA"></path><path d="M410 192h1l-1 2c-1 1 0 1-1 2v1l-3-1h1c1-2 2-2 3-4h0z" class="AS"></path><path d="M406 192h4c-1 2-2 2-3 4l-1-2v-1h0l-1-1h1z" class="AN"></path><path d="M414 196v-2c0-1 1-1 2-2h1 2c2 2 2 2 3 4l-1 2c-1 0-1 0-2 1h-1c-1 1-3 2-2 3v1l-1 1-2-2c0-1 1-2 1-4v-1h0v-1z" class="W"></path><path d="M417 194h1v1 3 1c-1 1-3 2-2 3v1l-1 1-2-2c0-1 1-2 1-4v-1h0l1-1v-1h1l1-1z" class="AB"></path><path d="M414 197l1-1v-1h1l1-1v1c-1 1-1 2-1 4v1h-1c0-1 0-2-1-2v-1h0z" class="Y"></path><path d="M410 199l2-2h2v1c0 2-1 3-1 4-1 2-1 2-1 4v2c-1 0-1-1-2-1v2c-2 0-3 0-5-1h-1v-1c0-2 0-2 1-3v-1c2-1 2-2 3-4l1 2c1-1 1-1 1-2z" class="AR"></path><path d="M408 199l1 2 2 1-1 1h0c-1-1-1 0-2 0h0c0 2-1 3-2 4h-1v1h-1v-1c0-2 0-2 1-3v-1c2-1 2-2 3-4z" class="m"></path><path d="M405 192l1 1h0v1l1 2h-1l3 1c-1 1-1 2-1 2-1 2-1 3-3 4v1c-1 1-1 1-1 3v1h-1-1-2c-2 0-2 0-4 1l-1-1h-1s-1 0-2-1v-1-1l8-9 5-4z" class="AA"></path><path d="M406 196l3 1c-1 1-1 2-1 2-1 2-1 3-3 4 0-1-1-2-2-3l1-1h1l1-1v-2z" class="AR"></path><path d="M400 196v1c-1 2-2 3-3 5 2 0 2-1 3-2 1 2 0 2 0 4l2 1 1-1h2c-1 1-1 1-1 3v1h-1-1-2c-2 0-2 0-4 1l-1-1h-1s-1 0-2-1v-1-1l8-9z" class="AS"></path><path d="M392 206l2-2h2l-2 4s-1 0-2-1v-1z" class="AA"></path><path d="M396 204l2-1c1 0 1 1 2 2-1 0-2 0-2 1h-2c0 1 0 1-1 2h-1l2-4z" class="Y"></path><path d="M395 208c1-1 1-1 1-2h2c0-1 1-1 2-1 1 1 3 2 3 3h-1-2c-2 0-2 0-4 1l-1-1z" class="W"></path><path d="M427 186h0v1h2l1 2c-2 0-3 2-4 3 1 2 2 2 3 4h0l1 3h-1v1l-3 3 1 1c0 1 1 3 2 4h1c-3 1-6 0-9 0-1 1-1 0-2 0h-1-2-1l-2 1-1-1v-2c0-2 0-2 1-4l2 2 1-1v-1c-1-1 1-2 2-3h1c1-1 1-1 2-1h2v-1l1-1h0c-2-2-1-6-1-9h3l1-1z" class="Y"></path><path d="M418 199h1l1 3c-1 2-3 2-3 4-1-1-2 0-3-1l1-1 1-1v-1c-1-1 1-2 2-3z" class="k"></path><path d="M412 208v-2c0-2 0-2 1-4l2 2-1 1c1 1 2 0 3 1l1 1v1h-2-1l-2 1-1-1z" class="W"></path><path d="M415 208l-2-2 1-1 2 1v2h-1z" class="c"></path><path d="M425 202h1 0v1l1 1c0 1 1 3 2 4h1c-3 1-6 0-9 0-1 1-1 0-2 0v-1l1-1 1 1h0v-1l-1-1c1-1 1-2 2-2l2 1h0 1v-2z" class="Z"></path><path d="M427 186h0v1h2l1 2c-2 0-3 2-4 3 1 2 2 2 3 4h0l1 3h-1v1l-3 3v-1h0-1v-1h0c1-1 1-1 2-1v-1-1l-1 1h-1v-1l-1-2h0c-2-2-1-6-1-9h3l1-1z" class="i"></path><path d="M429 196h0l1 3h-1-1c0-2 0-2 1-3z" class="X"></path><path d="M423 187h3c0 3-1 7-2 9-2-2-1-6-1-9z" class="K"></path><path d="M442 184v1c1 1 1 0 1 1h1 1v3h1c0 2 0 3 1 4l-2 2c0 1-1 1-1 2l-1 4-1 1h0c0 2-1 3-1 4v2h-4c-3 0-5 1-7 0h-1c-1-1-2-3-2-4l-1-1 3-3v-1h1l-1-3h0c-1-2-2-2-3-4 1-1 2-3 4-3l-1 1h2c1-1 1-1 1-2v-1h2 2 0l4 1 1 1v-1-1l1-3z" class="u"></path><path d="M437 196l2 2-2 3-2-2s0-1-1-1v-1l1-1v1l2-1z" class="X"></path><path d="M430 199l2 2-1 1 1 1-2 1h-1v-4-1h1z" class="Ao"></path><path d="M429 200v4h1v1c0 1-1 2-1 3-1-1-2-3-2-4l-1-1 3-3z" class="Aq"></path><path d="M430 205h2 1 1l2-1c0 2 0 3 1 4-3 0-5 1-7 0h-1c0-1 1-2 1-3z" class="a"></path><path d="M441 196c1 2 1 4 1 6h0c0 2-1 3-1 4v2h-4c-1-1-1-2-1-4v-1l1-2 2-3 2-2z" class="Ak"></path><path d="M436 203l5 1 1-2c0 2-1 3-1 4v2h-4c-1-1-1-2-1-4v-1z" class="Ar"></path><path d="M442 184v1c1 1 1 0 1 1h1 1v3h1c0 2 0 3 1 4l-2 2c0 1-1 1-1 2l-1 4-1 1c0-2 0-4-1-6l-2 2-2-2 1-1c0-1 2-1 2-3v-2-2l1 1v-1-1l1-3z" class="AI"></path><path d="M441 188c1 0 3 1 4 0v1c0 1 0 1-1 1l-2 1c-1-1-1-1-2-1v-2l1 1v-1z" class="AH"></path><path d="M440 190c1 0 1 0 2 1l-1 1 1 1c0 1 0 0 1 1l-1 1c-1 0-1 0-1 1l-2 2-2-2 1-1c0-1 2-1 2-3v-2z" class="i"></path><path d="M434 187h2 0l4 1v2 2c0 2-2 2-2 3l-1 1-2 1v-1l-1 1c-1 1-2 1-4 2l-1-3h0c-1-2-2-2-3-4 1-1 2-3 4-3l-1 1h2c1-1 1-1 1-2v-1h2z" class="Z"></path><g class="Q"><path d="M434 187h2c0 2 0 2-1 4-1-1-1-2-1-4z"></path><path d="M436 187l4 1v2h-1v3l-1-1h0c0-2 0-3-1-4l-1-1z"></path></g><path d="M431 190c1-1 1-1 1-2v-1h2c0 2 0 3 1 4-1 1-2 1-3 2v3h0v-1h0c-1 0-2 0-3 1h0c-1-2-2-2-3-4 1-1 2-3 4-3l-1 1h2z" class="Ar"></path><path d="M431 190c1-1 1-1 1-2v-1h2c0 2 0 3 1 4-1 1-2 1-3 2h-2v-1c1-1 1-1 2-1l-1-1z" class="n"></path><path d="M430 169l-1 3 1 1h0l1 2 2-3c2 1 3 0 4-1l2-1-1 2c1 1 1 1 2 1 2 0 3 0 4 1h2c1 0 1-1 2-1h1v1h4c-2 1-3 3-4 5l-1 5-3 2h-1-1c0-1 0 0-1-1v-1l-1 3v1 1l-1-1-4-1h0-2-2v1c0 1 0 1-1 2h-2l1-1-1-2h-2v-1h0l-1 1h-3v-3l2-9c0-1 1-2 1-2l1 1c0-1 1-1 1-2l2-3z" class="m"></path><path d="M432 178c0 2 0 2 1 3v1h0l-1 1c-1 0-2-1-3-2h0v-1l3-2z" class="AS"></path><path d="M435 180h1v4h1v-1l1 1c0 1-1 1-2 2l-3-4h0 2v-1-1z" class="Z"></path><path d="M436 175l1 2v1l-2 2v1 1h-2v-1c-1-1-1-1-1-3l4-3z" class="AO"></path><path d="M437 178h2 0 1v1 2 1c1 1 1 0 1 1h0v2h-1-1l-1-1-1-1v1h-1v-4h-1l2-2z" class="i"></path><path d="M439 178h1v1 2 1c1 1 1 0 1 1h0-1c-2-1-2-1-3-2 1 0 1-1 2-2v-1h0z" class="AI"></path><path d="M439 170l-1 2c1 1 1 1 2 1-1 1-3 1-4 2l-4 3-3 2c0-2 1-1 2-3v-2l2-3c2 1 3 0 4-1l2-1z" class="R"></path><path d="M430 169l-1 3 1 1h0l1 2v2c-1 2-2 1-2 3v1h0c-1 2-2 3-2 5h0l-1 1h-3v-3l2-9c0-1 1-2 1-2l1 1c0-1 1-1 1-2l2-3z" class="C"></path><path d="M427 186l-2-1c0-2 1-3 1-5 1 1 2 1 3 1h0c-1 2-2 3-2 5h0z" class="F"></path><path d="M426 180c1-2 3-4 4-7l1 2v2c-1 2-2 1-2 3v1c-1 0-2 0-3-1z" class="S"></path><path d="M444 174h2c1 0 1-1 2-1h1v1h4c-2 1-3 3-4 5l-1 5-3 2h-1-1c0-1 0 0-1-1v-1h0l-1-1c0-1 0 0-1-1v-1-2-1h-1 0-2v-1l-1-2c1-1 3-1 4-2 2 0 3 0 4 1z" class="AM"></path><path d="M442 177c0 1 1 1 1 2h1l-1 1c0 1 0 1 1 2l-2 2-1-1c0-1 0 0-1-1v-1-2-1l2-1z" class="AH"></path><path d="M444 174h2c1 0 1-1 2-1h1v1c-1 2-1 3-2 5l-2 2c-1-1 0-1-1-2h-1c0-1-1-1-1-2l-2 1h-1 0-2v-1l-1-2c1-1 3-1 4-2 2 0 3 0 4 1z" class="i"></path><path d="M441 176l1-1c2 1 1 1 2 2 0 1 0 1-1 2 0-1-1-1-1-2 0 0-1 0-1-1z" class="AB"></path><path d="M437 177l2-2 1 1h1c0 1 1 1 1 1l-2 1h-1 0-2v-1z" class="k"></path><path d="M440 176h1c0 1 1 1 1 1l-2 1h-1l1-2z" class="c"></path><path d="M444 174h2c1 0 1-1 2-1h1v1c-1 2-1 3-2 5-1-2-2-3-3-5z" class="R"></path><path d="M366 283l1 2 1 1h1v2 1c0 2-1 5-1 7v1l-4 8-7 17v1c0 1-1 2 0 4h-1v2 1l-1 4c-1 0 0 0-1 1 0 2 1 0 0 2v3c-1 1-1 2-1 3-1 2 1 4 0 6v3h0v-1c0-2 0 1 1-1v-3h0c0-1 0-2 1-3v-2c0-1 0-2 1-3v-2c0-2 1-3 1-4 1-1 0-2 1-2v-1c0-1 1-3 1-4v-1c-1-1 0-1 0-2v-2c0 1 1 2 0 3v1l1 2c2-3 3-7 4-11v3c-1 2-2 6-2 7 1 2 1 4 1 6v-1h0l2-2c0 2 0 3-1 5l-1 3v1l-2 8h0v2l-1 5c0 1 0 0-1 1h0l-3 26c0 8 0 17 1 25v8h-1v4c1 1 1 3 2 5v2 1c-2-2-2-5-3-8l-1 1c-1-2-1-6-2-9h0l-1-8-7-30h2c0 1 0 2 1 3 0-3 0-6-1-9h0c-1-2-1-3-1-5l-3-7c1-1 0-2-1-3 0-1 1-2 1-3l1 2c0-1 0-1 1-2v1-2c1-4 1-8 1-12v-3c1 1 2 2 2 3v-7h0c0-2 1-2 2-3v-1c3-6 6-13 9-20 2-7 6-13 8-20z" class="Aj"></path><path d="M347 327l3 3-1 6-2-2v-7z" class="t"></path><path d="M349 324c1-1 2-3 3-4h0l-1 1h1l-2 9-3-3h0c0-2 1-2 2-3z" class="j"></path><path d="M348 371c2 3 1 7 2 10v-2l1-1v-4-1c0-6 1-11 2-17l1 4v1l-2 17c-1 2-1 6-2 8-1-5-2-10-2-15z" class="M"></path><path d="M366 283l1 2-15 36h-1l1-1h0c-1 1-2 3-3 4v-1c3-6 6-13 9-20 2-7 6-13 8-20z" class="L"></path><path d="M346 360l1 1c1-3 0-8 0-11l1 21c0 5 1 10 2 15 0 4 1 8 2 12 0 1 0 3 1 5v5l-1 1-1-8-7-30h2c0 1 0 2 1 3 0-3 0-6-1-9v-5z" class="AU"></path><path d="M345 331c1 1 2 2 2 3l2 2-2 14c0 3 1 8 0 11l-1-1v5h0c-1-2-1-3-1-5l-3-7c1-1 0-2-1-3 0-1 1-2 1-3l1 2c0-1 0-1 1-2v1-2c1-4 1-8 1-12v-3z" class="AQ"></path><path d="M345 334l1 26v5h0c-1-2-1-3-1-5l-3-7c1-1 0-2-1-3 0-1 1-2 1-3l1 2c0-1 0-1 1-2v1-2c1-4 1-8 1-12z" class="d"></path><path d="M364 316v3c-1 2-2 6-2 7 1 2 1 4 1 6v-1h0l2-2c0 2 0 3-1 5l-1 3v1l-2 8h0v2l-1 5c0 1 0 0-1 1h0l-2 7-1-1h0l-2 1v-1l-1-4 7-29h0c2-3 3-7 4-11z" class="h"></path><path d="M354 360c1-2 0-5 2-7v1l1 2c-1 1-1 3-1 4h0l-2 1v-1z" class="f"></path><path d="M364 316v3c-1 2-2 6-2 7 1 2 1 4 1 6v-1h0l2-2c0 2 0 3-1 5l-1 3v1l-2 8h0v2l-1 5c0 1 0 0-1 1h0l-2 7-1-1c0-1 0-3 1-4 1-3 2-8 2-12 1-5 3-9 3-14l-1-1c0 4-2 7-3 11-1-5 3-9 2-13h0c2-3 3-7 4-11z" class="d"></path><path d="M357 361l2-7-3 26c0 8 0 17 1 25v8h-1v4c1 1 1 3 2 5v2 1c-2-2-2-5-3-8l-1 1c-1-2-1-6-2-9h0l1-1v-5c-1-2-1-4-1-5-1-4-2-8-2-12 1-2 1-6 2-8l2-17 2-1h0l1 1z" class="AL"></path><path d="M352 378v11c0 4 2 10 1 14-1-2-1-4-1-5-1-4-2-8-2-12 1-2 1-6 2-8z" class="r"></path><path d="M357 361c0 1 0 3-1 5l-1 6v17c0 2 1 20 1 21-4-5-3-36-2-43l2-7h0l1 1z" class="AD"></path><defs><linearGradient id="BD" x1="302.737" y1="247.254" x2="371.913" y2="243.551" xlink:href="#B"><stop offset="0" stop-color="#060608"></stop><stop offset="1" stop-color="#232a2a"></stop></linearGradient></defs><path fill="url(#BD)" d="M390 218l2 1v-1h1 2c1-1 2 0 3-1h5c0 1-1 1-2 1h-2l-1 1h-3-3l1 1c-1 1-2 1-3 1-6 0-12 2-18 3 1 0 3 1 4 1-1 1-2 1-3 2v1h-1l-1 1-6 3h1 2c1-1 2 0 3 0l-25 13-12 8-11 8c-2 1-4 3-5 5l-3 4c0 1 0 1-1 2v2l-3-2-5-4-1 1c-1-1-1-1-1-2 0-3 0-4-1-7l2-3c2-1 4-3 5-5h-1c2-1 3-3 4-4 1-2 0-4-1-6h2l1 1c1 0 1-1 2-2s1-2 3-3h2 1 1c3 0 5-2 7-4l9-3c-1-1-2-2-4-2 2-2 3-1 5-2v-1h-4-4l-2-1c2 0 2-1 4-1h0c-1 0-2 0-4-1 5 0 10 1 15 1s10-2 15-2c5-1 11-1 16-3l9-1h4z"></path><path d="M362 226l10-2c1 0 3 1 4 1-1 1-2 1-3 2h0 0c-2-2-7-1-10 0l-1-1z" class="h"></path><path d="M315 243h2c1 0 2-1 3-1h0c-3 3-6 7-10 10h-1c2-1 3-3 4-4 1-2 0-4-1-6h2l1 1z" class="F"></path><path d="M373 227h0v1h-1l-1 1-6 3c-5 2-11 3-17 4l-1-1c1-1 3-1 4-2 4 0 7-1 11-3 1 0 3 0 5-1 2 0 4-2 6-2z" class="z"></path><path d="M340 231l20-5 14-3c2-1 4-1 6-1 3-1 7-1 10-2v1c-6 0-12 2-18 3l-10 2c-15 4-29 8-42 16h0c-1 0-2 1-3 1h-2c1 0 1-1 2-2s1-2 3-3h2 1 1c3 0 5-2 7-4l9-3z" class="U"></path><defs><linearGradient id="BE" x1="371.294" y1="232.651" x2="345.976" y2="214.227" xlink:href="#B"><stop offset="0" stop-color="#2e2f2c"></stop><stop offset="1" stop-color="#525c6f"></stop></linearGradient></defs><path fill="url(#BE)" d="M390 218l2 1v-1h1 2c1-1 2 0 3-1h5c0 1-1 1-2 1h-2l-1 1h-3-3l1 1c-1 1-2 1-3 1v-1c-3 1-7 1-10 2-2 0-4 0-6 1l-14 3-20 5c-1-1-2-2-4-2 2-2 3-1 5-2v-1h-4-4l-2-1c2 0 2-1 4-1h0c-1 0-2 0-4-1 5 0 10 1 15 1s10-2 15-2c5-1 11-1 16-3l9-1h4z"></path><path d="M304 267c1-1 1 0 2-1h0c1-2 3-3 3-5l2-2c8-12 18-19 31-24 3-1 5-2 8-2v-1l1 1c-1 1-3 1-4 2l1 1c-1 1-2 2-4 2v1l-3 1-1 1-2 1-8 5c-2 1-4 3-7 4l-2 1c0 1-1 1-2 2-2 2-5 4-7 7h2 0 1c-1 1-1 2-2 3v2c1 1 1 1 3 0l-1 1v3c0 1 0 1-1 2v2l-3-2-5-4-1 1c-1-1-1-1-1-2z" class="r"></path><path d="M340 237c2 1 3 1 4 1v1c-3-1-6 0-8 2v-1h-1l2-2h1l2-1z" class="h"></path><path d="M336 241c2-2 5-3 8-2l-3 1-1 1-2 1-2-1h0z" class="AF"></path><path d="M340 237c1 0 2-1 3-1l4-1 1 1c-1 1-2 2-4 2-1 0-2 0-4-1z" class="V"></path><path d="M314 261h1c-1 1-1 2-2 3s-2 2-3 4c-1 0-2 0-2-1h-1l1-1h2c1 0 1-3 3-4 0 0 1 0 1-1z" class="g"></path><path d="M336 241l2 1-8 5c-2 1-4 3-7 4l-2 1c2-2 6-6 8-7 3-1 5-2 7-4z" class="f"></path><path d="M313 264v2c1 1 1 1 3 0l-1 1v3c0 1 0 1-1 2v2l-3-2-1-4c1-2 2-3 3-4z" class="M"></path><defs><linearGradient id="BF" x1="338.276" y1="251.692" x2="334.188" y2="244.949" xlink:href="#B"><stop offset="0" stop-color="#20252c"></stop><stop offset="1" stop-color="#3e4450"></stop></linearGradient></defs><path fill="url(#BF)" d="M365 232h1 2c1-1 2 0 3 0l-25 13-12 8-11 8c-2 1-4 3-5 5l-3 4v-3l1-1c-2 1-2 1-3 0v-2c1-1 1-2 2-3h-1 0-2c2-3 5-5 7-7 1-1 2-1 2-2l2-1c3-1 5-3 7-4l8-5 2-1 1-1 3-1v-1c2 0 3-1 4-2 6-1 12-2 17-4z"></path><g class="I"><path d="M340 241l1-1c2 1 3 1 4 1l-1 1h-3l-1-1z"></path><path d="M338 242l2-1 1 1h3 0c-1 1-1 2-2 2-4 2-6 4-9 7-1 0-3 1-4 1-4 2-8 6-12 8-1 0-1 1-2 1h-1 0-2c2-3 5-5 7-7 1-1 2-1 2-2l2-1c3-1 5-3 7-4l8-5z"></path></g><path d="M341 242h3 0l-3 1v-1z" class="AE"></path><path d="M338 242l2-1 1 1v1l-7 5c-1 1-2 1-4 2-1 1-2 1-3 2-2 0-3 0-4 1l-4 3h0v-2c1-1 2-1 2-2l2-1c3-1 5-3 7-4l8-5z" class="Ab"></path><defs><linearGradient id="BG" x1="458.939" y1="187.263" x2="476.513" y2="200.861" xlink:href="#B"><stop offset="0" stop-color="#e4d5d3"></stop><stop offset="1" stop-color="#fbffff"></stop></linearGradient></defs><path fill="url(#BG)" d="M467 170l3 1h0l3 2c1 1 3 2 4 3l1 2c2 1 3 2 4 4l1-2 1 3c1-1 1-1 2-1h1c1 0 3 0 3-1l2 4 1 1c1 1 1 2 2 4 1 1 1 1 2 1l1 1 1-1 1 1-1 1v1c1 1 1 2 1 3v2c0 1 0 2 1 3l-4 3-2 2v1c0 1 0 2 2 3h1v2c-9-3-17-8-25-13-2 1-2 2-3 3-6 2-10 4-17 5h-6-6v-2c0-1 1-2 1-4h0l1-1 1-4c0-1 1-1 1-2l2-2c-1-1-1-2-1-4h-1v-3l3-2 1-5c1-2 2-4 4-5l9-2c2-1 3-1 5-2z"></path><path d="M477 189c1 1 3 2 4 4v1h1v-1l2 2c0 1-1 1-2 2v3 2l-3-1h1v-2-1c1-2 1-4-1-5v-1c-2 0-1 2-3 2l-1-1c1-1 1-3 2-4z" class="D"></path><path d="M478 178c2 1 3 2 4 4l1-2 1 3c0 2 0 5-1 7l-1 3v1h-1v-1c-1-2-3-3-4-4v-1l1-1c0-1 0-1 1-2v-1l1-1s-1-1-1-2-1-1-2-2c0-1 0-1 1-1z" class="S"></path><path d="M482 182l1-2 1 3c0 2 0 5-1 7l-1 3v1h-1v-1c1-2 1-3 1-4 1-2 1-5 0-7z" class="e"></path><path d="M479 185h3v3 1c0 1 0 2-1 4-1-2-3-3-4-4v-1l1-1c0-1 0-1 1-2z" class="E"></path><path d="M447 193c1 0 2-1 3 0l3 2 1 1h-2v2l3 1 1 1c-1 1-1 1-1 2l1-1v1l-1 2h0l-2 4h-6-6v-2c0-1 1-2 1-4h0l1-1 1-4c0-1 1-1 1-2l2-2z" class="An"></path><path d="M455 199l1 1c-1 1-1 1-1 2l1-1v1l-1 2h0-1l-1-1c-1-1-1 0-3 0v-1c1-2 3-1 4-2l1-1z" class="Af"></path><path d="M442 202l1-1 1-4v7h1l1 1h1v1h0 2 0c0 1-1 1-2 2h-6v-2c0-1 1-2 1-4h0z" class="AM"></path><path d="M445 195v1 1l1 1c0-1 0-2 1-3h1v-1h1c1 1 1 2 1 3-1 3-4 5-4 8l-1-1h-1v-7c0-1 1-1 1-2z" class="G"></path><path d="M484 183c1-1 1-1 2-1h1c1 0 3 0 3-1l2 4 1 1c1 1 1 2 2 4 1 1 1 1 2 1l1 1 1-1 1 1-1 1v1c1 1 1 2 1 3v2c0 1 0 2 1 3l-4 3-2 2v1c0 1 0 2 2 3h1v2c-9-3-17-8-25-13 1-1 3-2 4-3h3s0-3-1-4c2 1 2 3 1 5v1 2h-1l3 1v-2-3c1-1 2-1 2-2l-2-2 1-3c1-2 1-5 1-7z" class="E"></path><path d="M482 197h0l2 4c0 1 0 1-1 2h-2l1-1v-2-3z" class="S"></path><path d="M484 183c1-1 1-1 2-1h1c-1 2-1 4-1 6 1 1 0 1 1 1l2 1v1h-2c1 1 1 2 2 2v1c-2-1-2-1-3-3-1 0-2-1-3-1 1-2 1-5 1-7zm8 25c-1-2-1-3-1-5v-1c1 0 2 0 3-1s1-1 3-1v1l1 1-1 1v2l-2 2v1l-1 1-1-1h-1z" class="F"></path><path d="M492 208c1-3 3-5 5-7l1 1-1 1v2l-2 2v1l-1 1-1-1h-1z" class="J"></path><path d="M479 193c2 1 2 3 1 5v1 2h-1l3 1-1 1c0 1 0 1 1 2 2 0 4 1 5 2l1-1-1-1v-2-1c2 3 2 6 4 7h1l1-1 1 1 1-1c0 1 0 2 2 3h1v2c-9-3-17-8-25-13 1-1 3-2 4-3h3s0-3-1-4z" class="H"></path><path d="M479 193c2 1 2 3 1 5v1 2h-1v1h-1l-1-1v-2h2l-2-2h3s0-3-1-4z" class="AT"></path><path d="M487 182c1 0 3 0 3-1l2 4 1 1c1 1 1 2 2 4 1 1 1 1 2 1l1 1 1-1 1 1-1 1v1c1 1 1 2 1 3v2c0 1 0 2 1 3l-4 3v-2l1-1-1-1v-1l-2-2c-1 0-2 1-3 1h-2l1-2h1l-1-2-1-1v-1l1-1c0 1 1 1 1 1l1 1h1c0-1 0-1-1-2l-1-1c0-1 0-2-1-3l-1 2-3-2v1c-1 0 0 0-1-1 0-2 0-4 1-6z" class="S"></path><path d="M492 185l1 1c1 1 1 2 2 4 1 1 1 1 2 1l1 1 1-1 1 1-1 1v1c1 1 1 2 1 3v2c0 1 0 2 1 3l-4 3v-2l1-1-1-1v-1h0c1-3-4-12-5-15z" class="w"></path><path d="M467 170l3 1h0l3 2c1 1 3 2 4 3l1 2c-1 0-1 0-1 1l-1 2-4 3h-1c-2 1-3 3-5 3 0-2 2-2 3-3h0c-1 0-1 0-3 1l-1 1c-1-1-2-1-3 0h-2c2 1 2 0 3 1-1 1-1 1-2 1-2 1-6 3-8 5v2l-3-2c-1-1-2 0-3 0-1-1-1-2-1-4h-1v-3l3-2 1-5c1-2 2-4 4-5l9-2c2-1 3-1 5-2z" class="Ak"></path><path d="M451 179c0 3 1 4 2 6-1 1-1 2-2 2l-2-2c1-1 1-2 1-3s1-2 1-3z" class="Af"></path><path d="M452 178c0-1 1-2 3-3v2l4 1c1 0 1 0 2 1h2v1c-1 0-3-1-3-1l-2 2h-1c-2 1-3 2-4 4h0 0c-1-2-2-3-2-6l1-1z" class="T"></path><path d="M452 178c0-1 1-2 3-3v2l4 1-1 1c-2 0-4-1-6-1h0z" class="P"></path><path d="M470 171l3 2c1 1 3 2 4 3l1 2c-1 0-1 0-1 1l-1 2v-3l-1-1c-1-1-3-3-5-3-3 0-5 1-7 4v1h-2c-1-1-1-1-2-1l-4-1v-2c2 0 4 0 6 1h3c1-2 1-3 2-3l1-1c1 0 1 0 3-1z" class="Af"></path><path d="M457 181v1 6l1-1-1-1v-1l1 1c1 1 0 1 1 1l2 1c-2 1-6 3-8 5v2l-3-2c-1-1-2 0-3 0-1-1-1-2-1-4h-1v-3l3-2v1c0 1 0 2-1 3l2 1c0-1 1-1 2-2 1 0 1-1 2-2h0 0c1-2 2-3 4-4z" class="AV"></path><path d="M453 185h0v5h-1v1c-1 0-2-1-3-2 0-1 1-1 2-2 1 0 1-1 2-2z" class="An"></path><path d="M445 186l3-2v1c0 1 0 2-1 3l2 1c1 1 2 2 3 2s1 0 2 1l5-5 2 1c-2 1-6 3-8 5v2l-3-2c-1-1-2 0-3 0-1-1-1-2-1-4h-1v-3z" class="AW"></path><path d="M463 179v-1c2-3 4-4 7-4 2 0 4 2 5 3l1 1v3l-4 3h-1c-2 1-3 3-5 3 0-2 2-2 3-3h0c-1 0-1 0-3 1l-1 1c-1-1-2-1-3 0h-2c2 1 2 0 3 1-1 1-1 1-2 1l-2-1c-1 0 0 0-1-1l-1-1v1l1 1-1 1v-6-1h1l2-2s2 1 3 1v-1z" class="B"></path><path d="M475 177l1 1v3l-4 3c0-2 1-3 2-4v-1l1-2z" class="S"></path><path d="M404 557c2 1 3 3 4 5l2 1v-1c2 3 3 7 5 8 2 4 4 10 7 13-1-1-1-2-1-3-1-1-2-2-2-3l3 3c0 1 0 3 1 4h1v1l3 3h1l3 5h0l6 17c1 1 2 6 4 7 0 0 1 0 2 1v1c1 1 3 4 4 5s0 1 1 2c1 2 2 3 2 6h1c0 1 1 2 2 4h0v1l3 5v1l1 2c1 1 1 3 2 5l2 5v1c1 1 1 1 1 2v1c0 3 1 3 2 5 1 1 1 1 1 2l1 1h0v1l2 2h0l2 1c0 1 0 1-1 2l2 3c0 1 1 2 2 4 1 1 1 3 2 4v2l1 2h0l1 3c1 2 2 3 2 5 1 2 1 1 2 2l1 1v-1 1l-1 2v2l-1 1-1-1-1-1c-1 0-1 0-2-1l-1-1h0c-1-1-1-1-2-1l-2-2v-1l-1-1-1 1-1-1v2 1h0c1 1 1 2 1 3 1 1 1 2 2 4h1c0 1 0 1 1 2l1-1s1 0 1 1l4 8c1 1 0 0 1 2h0c1 3 5 8 5 11 1 3 3 7 5 11 2 3 4 8 6 11s3 7 5 10c1 1 1 1 1 2s0 2 1 3l1 1v2c1 1 1 2 1 3 1 0 1 1 1 2v2c1 1 2 1 2 3 1 0 1 1 2 2s1 2 1 4h0v2h-1l1 2c0 1 3 4 2 5l-1 1c0 2 1 2 2 4h1 1c1 1 0 1 2 1v-1l-1-1v-1l1 1c2-2 5-6 5-8 2-1 3-2 4-3 2-1 5-9 6-12l1-1v-1c0-1 1-2 2-4l-8 21-1 4c-3 6-6 11-11 15h-2c-2-1-3-2-4-4 0 0-3-4-3-5 0 0-2-3-2-4l-2-4-5-16c-2-4-4-9-5-14l-6-13c-1-2-2-5-3-7-2-4-3-8-5-11l-14-34-2-4-13-32-3-7-3-7-12-26-2-1-1-2-2-4 1-1v1h1l-19-44-6-12z" class="Ab"></path><path d="M429 617l2-1 1 4-2-1-1-2z" class="t"></path><path d="M427 613l1-1v1h1l2 3-2 1-2-4z" class="L"></path><path d="M442 621l1 1c0 1 1 2 1 3v1l-1-1v4l-2-7h0 1v-1z" class="I"></path><path d="M437 610c1 1 2 6 4 7l1 4v1h-1 0l-3-5c0-2-1-4-1-5v-2z" class="f"></path><path d="M438 617l3 5 2 7 1 3 6 14c1 1 2 3 2 4-1 0-1-1-1-2h-1 0l-3-3c-1-2-1-3-1-5l-3-7c-1-2-2-3-2-5v2h1c1-2-1-5-2-7s-2-4-2-6z" class="AL"></path><path d="M424 584v1l3 3h1l3 5h0l6 17v2c-3-5-4-10-8-15h0v-1c0-1-1-2-2-3l-3-3h0l2 1 1-1c0-1-1-1-1-2-1-1-2-2-2-4z" class="AF"></path><path d="M404 557c2 1 3 3 4 5l2 1v-1c2 3 3 7 5 8 2 4 4 10 7 13 1 1 1 3 1 5h-1v1c0 1 1 1 0 3 0-2-1-4-3-6v-1l-1-1-1-1-3-7c0-1 0-2-1-3l-1-1c-1-2-1-2-2-3l-6-12z" class="f"></path><path d="M493 761v1c2 2 3 5 5 8 0 1 0 1 1 3v1c0 1 1 2 1 3 1 1 1 1 1 2 1 0 1 1 1 2 1 0 1 0 1 1s1 2 1 3 1 1 1 2l2 2v2c3 3 3 9 8 11l1-1h0c1 0 2-2 3-2 3-3 6-7 9-9v-1l-1 4c-2 2-3 4-5 6s-3 3-6 4c-1 1-3 0-4 0-1-1-2-3-2-4-2-3-5-7-6-11s-3-9-6-12v-1c-2-4-4-9-5-14z" class="Ae"></path><path d="M498 775v1c3 3 5 8 6 12s4 8 6 11c0 1 1 3 2 4 1 0 3 1 4 0 3-1 4-2 6-4s3-4 5-6c-3 6-6 11-11 15h-2c-2-1-3-2-4-4 0 0-3-4-3-5 0 0-2-3-2-4l-2-4-5-16z" class="AZ"></path><path d="M270 252c3 0 4 2 5 3l4 1c1 0 2 1 2 1l1-3 3 2 2 1c2 1 6 3 7 6l1 1h0v2h2c2-2 3-2 5-2l1-4c1 3 1 4 1 7 0 1 0 1 1 2l6 5-2 1-1 2h3c-3 3-8 9-8 12l-1 3v1c0 1 0 1-1 3-1 0-1 1-1 2v1c-1 1-1 2-2 3v1 1l-1 4h-1l-2 9c0 1 0 2-1 3v5 1c-1 1-1 0-1 1v2 2c-1 1-1 0-1 1v2c-1 1-1 3-1 4v6l12 12c2 2 4 5 7 6 1 1 5 5 5 6l-7-5c0-1-3-3-3-4v5l6 9c2 2 2 3 5 5 2 1 3 2 5 4s5 4 7 6 5 4 6 7l6 8 1-2h1c1 1 1 2 2 2 3 5 5 10 7 15h1c-1-2-1-4-1-5v-1h1v-1-2h1c1 3 1 7 2 9l8 27 1 5 2 4 2 6h2c2 7 4 14 7 21 1 3 3 7 3 10l2-1c1 3 1 6 3 9 1 1 2 4 2 6h0c1 3 3 7 5 10l2 4c-1 1-1 2-2 3l-1 1 4-1v1 1l-2 1h0l2 2v1c1 5 1 9 3 13 1 4 3 7 5 11l2 5 6 12 19 44h-1v-1l-1 1 2 4 1 2 2 1 12 26 3 7 3 7 13 32 2 4 14 34c2 3 3 7 5 11 1 2 2 5 3 7l6 13c1 5 3 10 5 14l5 16 2 4c0 1 2 4 2 4 0 1 3 5 3 5 1 2 2 3 4 4h2 3v-1c1 0 2-1 2-1h1l-5 8c-2 7-3 15-3 23 0 1 0 4-1 5-1 2-1 4-1 7l1 53 1-4v15h0c0 1 0 3-1 4l-1-1v-1-1l-1-1c-1-1-1-2-1-3-1-2-1-3-3-5l-12-27-21-50c-4-10-8-20-13-30l-5-13s-1-2-1-3l-1-3-3-7c-3-2-4-4-5-6h0c-1-1-1-1-2-1 0-1-1-2-1-2-3-2-4-5-7-6v-1l-1-1v-1l-3-2v-2l-1-1c2 0 5 2 6 3 1-1 0-1 0-2-2-1-3-2-4-4h-1c0-1 0-2-1-3s-1-2-2-3l1-1c2 1 3 1 4 2h0v-3-1c-1-2-1-5-3-7l1-2-8-18-18-42-5-12c0-4-3-8-5-11-7-7-16-14-21-22 0-4-4-8-6-10l-3-6-6-9c0-1-1-2-2-4l-3-5c0-1-2-3-3-4h3l-2-3-1-2h2v-2s0-1-1-2l-3-2c-2-2-4-4-5-6v-1l-3-7v-1c-1-1-3-3-4-5 0-1 0-2 1-3h0c-3-3-4-6-6-8l-2-4c0-2-1-3 0-5h3l1 1c-2-4-4-9-5-13v-1l2 1c-1-2-1-3-2-4s-1-1-1-2c0-2-1-4-1-6v-1-3c-1-2-1-4-1-6v-1c1-3 1-6 0-10h0v-4l-1 1v1l-2-2 1-1v-2-9l1-1-3-7-2-6c-1-2-1-4-2-5l-1-3v-1c-1-1-1-1-1-2h-2l-3-7c0-1 0-2-1-2l-2 2v-10h0c-1-4-3-9-5-13l-11-22c-1 1-2 1-3 2h-1v3h-1v-3c-1-1-2-2-2-4v-2c-1-2-2-2-2-4v-3l-2-2c0 1 0 2-1 2l-1 1v1-6l3-20c0-5 2-11 3-16l1-8 1-8h-2l1-4c0-5 0-10-1-15 1-2 1-5 2-7l1-11c-1-2-1-2-1-3v1c-2-1-1-2-2-4 0 0-1 0-1-1v1l-1-1-2 2v-1c-2-2-3-4-4-5 0-1-1-2-1-3-1-3-3-5-4-8h-1c2 1 3 3 4 4l1-1v-1c-1-1-1-2-1-3v-1c2 0 2 0 4 1v-2c1 1 1 0 2 1v1h1v-3c-3-5-8-10-13-14z" class="Aj"></path><path d="M483 808c-1-2-2-3-2-4 1-1 1-1 1-2 0 1 1 2 1 3h0v3z" class="V"></path><path d="M376 597c1 1 2 3 1 4l-1 2-1-2c0-1 0-2 1-3v-1z" class="x"></path><path d="M461 693l2-1 2 4-2 2-2-5z" class="F"></path><path d="M503 795h2c0 1 2 4 2 4l-2 1-2-5z" class="U"></path><path d="M505 800l2-1c0 1 3 5 3 5-1 0-1 0-2 1-1 0-3-4-3-5z" class="F"></path><path d="M329 479c1 2 1 4 3 6l-2 1-2-1v-4l1-2z" class="AA"></path><path d="M361 560l3 6h-2l-2-2c-1-1-1-2-2-2 1-1 2-2 3-2z" class="Q"></path><path d="M442 648l2-2 3 7c-1 1-1 0-2 1v1c-1-3-2-5-3-7zm58 140h0l3 3 2 4h-2l-3-7z" class="l"></path><path d="M493 864c-1-3-4-9-4-12l1 1v1l1 2v1l1 1c0-1 0-1 1-1 0 2 1 4 2 5l-2 2z" class="I"></path><path d="M302 278c2-2 4-2 7-3l-1 2-1 1-3 2h-2l1-1-1-1z" class="h"></path><path d="M303 279l3-2 1 1-3 2h-2l1-1z" class="d"></path><path d="M328 485l2 1 2-1 1 2c-1 0-2 0-2 1l-2 2s-1-1-2-1v-4h1z" class="AN"></path><path d="M445 655v-1c1-1 1 0 2-1l3 7c-1 1-1 2-1 3l-4-8z" class="P"></path><path d="M312 441c0-1 0-2 1-3 1 3 3 7 4 10h-2l-3-7z" class="k"></path><path d="M376 603l1-2c2 3 3 6 4 8l-1 1v-1c-2-1-4-4-4-6z" class="Z"></path><path d="M348 526c1 2 2 4 3 7h-1c-1 0-1 0-1-1v-1c-1 1-1 2-2 3v-3h0l-1-4 2-1z" class="x"></path><path d="M438 741l4 12-2-1h0c0-1 0-2-1-2v-1c-2-3-2-5-1-8z" class="AU"></path><path d="M309 431l4 7c-1 1-1 2-1 3 0-1 0-2-1-2l-2 2v-10z" class="AR"></path><path d="M386 619c2 4 4 8 5 13h0c-3-2-4-5-5-8v-5z" class="J"></path><path d="M324 405c1 1 1 1 1 2 1 1 2 2 2 3l-1-1h0-2v-1h-1l-1 1c2 1 1 2 2 3l3 4v2 1c-2-2-3-5-4-7l-2-5 1-1 1 2c0-1 1-2 1-3z" class="d"></path><path d="M380 610l1-1 5 10v5c-2-3-4-6-4-10v-1-1c-1-1-1-2-2-2z" class="v"></path><path d="M302 278l1 1-1 1h2l-5 10c-1-2 0-4 0-6l1-1c0-2 1-3 2-5z" class="M"></path><path d="M391 632c2 3 4 7 5 11-3-1-6-6-7-8 0-1 1-1 2-2v-1h0z" class="a"></path><path d="M289 323h1c0 1-1 2-1 3v2 1h0v3 1h0v3l-1 3h0c-1-1-1-1-1-2h0l1-8h-2l1-4 1 1 1-3z" class="s"></path><path d="M287 325l1 1v3h-2l1-4z" class="U"></path><path d="M353 538c1 3 3 7 4 10l-2 1c-1-1-2-1-2-2v-1c0-2-1-4-2-5l2-3zm-11-23l1-1 5 12-2 1 1 4-2-2 1-1c0-2-2-4-3-6 0-1-1-2-1-3l-1-1-1-1 2-1v-1z" class="AG"></path><path d="M340 517l2-1 1 2-1 1-1-1-1-1z" class="i"></path><path d="M395 542c0-1-1-1-2-2-3-4-8-7-13-8h-1 0l1-1c3 1 6 2 9 2v1c1 0 2 1 3 2 1-1 1 0 1-1l2 7z" class="h"></path><path d="M325 473l1-1c0 1 2 4 2 5l1 2-1 2v4h-1l-1 1v1l-2-2 1-1v-2-9z" class="X"></path><path d="M327 482l-1-2c1-2 1-2 2-3l1 2-1 2-1 1z" class="Z"></path><path d="M327 482l1-1v4h-1l-1 1v1l-2-2 1-1 2-2z" class="v"></path><path d="M495 844c2 1 2 4 3 6 1 4 3 8 4 12 1 2 2 4 2 7h-1l-9-23v-1l1 1c0 2 1 4 2 6 0 1 0 0 1 1h0v-2c-1-2-2-5-3-7z" class="V"></path><path d="M506 868c0 1 1 2 1 3s1 2 1 3v2h1c-1-2-1-3-1-4-1-3 1-8 0-11v-1c1 6 1 13 2 19v3c1 1 1 3 1 5h-1v-1c0 1-1 2-1 2l-1-1h0c1-2 0-4 0-5 0-5-1-9-2-13v-1z" class="h"></path><path d="M340 505l3 9-1 1v1l-2 1 1 1-2-1-1 1-2-1v-1l1-3c1-1 1-2 2-3s1-3 1-5z" class="AH"></path><path d="M339 513h2c1 0 1 1 1 2v1l-2 1c-1-1-1-2-1-4z" class="n"></path><path d="M337 513h2c0 2 0 3 1 4l1 1-2-1-1 1-2-1v-1l1-3z" class="X"></path><path d="M356 509h0c0 1 0 1 1 2h0l1 2v1l1 1c0 1 0 2 1 3v1c1 1 1 2 2 4h1v1c0 2 0 3 1 5v2s1 0 1 1h0c-1 0-2-1-2-1v-2l-1-1-1-2v-1l1-1c-1 0-1 0-2-1v-1c0-1-1-2-1-2-1-1-1-2-1-3h0v1 1l1 2h-1c-1-1-1-2-1-3s-1-1-1-2c-1-1-1-3-1-5h0l1-2z" class="d"></path><path d="M370 600c1 0 3 0 5 1l1 2c0 2 2 5 4 6v1h-1l1 2v1l-1-1h-1v2c-1-2-1-4-2-6l-2-2v-1c-1-2-3-3-5-4l1-1z" class="AJ"></path><path d="M299 359c-3-3-8-10-9-14 4 5 9 10 14 14v5c-1 0-1-1-1-2l-3-3h-1z" class="I"></path><path d="M379 491l2-1c1 3 1 6 3 9 1 1 2 4 2 6h0c0 1 0 2 1 3l-2 4-5-17 1-1-2-3z" class="AD"></path><path d="M379 491l2-1c1 3 1 6 3 9v2 3l-3-10-2-3z" class="s"></path><path d="M367 460h2c2 7 4 14 7 21 1 3 3 7 3 10l2 3-1 1c-1-4-4-9-6-14l-7-21z" class="O"></path><path d="M286 345l1 1c-1 0-1 1-1 2v3c-1 1-1 2-1 4v3c-1 1-1 2-1 3v3c-1 1-1 3-1 4v3c-1 1-1 2-1 3v1h2v1l6 12h0c-3-4-4-9-7-12l-1 5c-1 2-1 3-2 5v1-6l3-20c0-5 2-11 3-16z" class="V"></path><path d="M513 816h1c1-1 2-1 3-2h0c-2 7-3 15-3 23 0 1 0 4-1 5v-17c0-3-1-6 0-9z" class="t"></path><path d="M295 264h0v2h2c-3 9-2 18-4 27l-1-1-1-2 1-5c0-1 1-1 1-1l1-3-1-5 1-8h0l1-4z" class="B"></path><path d="M292 285c0-1 1-1 1-1 0 3 0 6-1 8l-1-2 1-5z" class="AY"></path><path d="M430 619l2 1 12 26-2 2-3-9-9-20z" class="j"></path><path d="M508 805c1-1 1-1 2-1 1 2 2 3 4 4h2 3v-1c1 0 2-1 2-1h1l-5 8h0c-1 1-2 1-3 2h-1c-1-4-3-8-5-11z" class="B"></path><path d="M392 525h0l1 10c0 1 0 0-1 1-1-1-2-2-3-2v-1c-3 0-6-1-9-2l-1-1 13-5z" class="V"></path><path d="M449 663c0-1 0-2 1-3l13 32-2 1-12-30zm-67-141c2 1 3 1 6 2l2-1 4-1v1 1l-2 1-13 5-6 2h0l2-2 7-8z" class="U"></path><path d="M382 522c2 1 3 1 6 2-4 2-8 4-12 5l-1 1 7-8z" class="p"></path><path d="M432 728l6 13c-1 3-1 5 1 8v1c1 0 1 1 1 2l-2-3-1 1c-2-1-3-2-4-4h-1c0-1 0-2-1-3s-1-2-2-3l1-1c2 1 3 1 4 2h0v-3-1c-1-2-1-5-3-7l1-2z" class="b"></path><path d="M434 737c0 2 1 3 1 5-2 0-2-1-3 0 2 2 5 5 6 7l-1 1c-2-1-3-2-4-4h-1c0-1 0-2-1-3s-1-2-2-3l1-1c2 1 3 1 4 2h0v-3-1z" class="AK"></path><path d="M347 534c1-1 1-2 2-3v1c0 1 0 1 1 1h1c0 1 1 3 2 5l-2 3c1 1 2 3 2 5v1l-2-1-7-6c0-1 1-3 1-4l2-2z" class="W"></path><path d="M350 533h1c0 1 1 3 2 5l-2 3c-1-1-3-4-3-5 1-1 2-1 2-3z" class="m"></path><path d="M308 277h3c-3 3-8 9-8 12l-1 3-3 6-5 14h0v-2l2-7 1-2v-2c1-3 1-6 2-9l5-10 3-2 1-1z" class="g"></path><path d="M380 610c1 0 1 1 2 2v1 1c0 4 2 7 4 10 1 3 2 6 5 8v1c-1 1-2 1-2 2-3-3-5-6-7-10s-3-7-4-11v-2h1l1 1v-1l-1-2h1z" class="AC"></path><path d="M387 508c-1-1-1-2-1-3 1 3 3 7 5 10l2 4c-1 1-1 2-2 3l-1 1-2 1c-3-1-4-1-6-2l2-4 1-3h0v-3l2-4z" class="I"></path><path d="M387 508l1 3h0c0 3 0 5-1 7-1-1-1-1-1-2l-1-1h0v-3l2-4z" class="AF"></path><path d="M387 508l1 3c-1 1-1 2-2 4h-1v-3l2-4z" class="g"></path><path d="M387 508c-1-1-1-2-1-3 1 3 3 7 5 10l2 4c-1 1-1 2-2 3v-2c-1-2-2-6-3-9h0l-1-3z" class="j"></path><path d="M384 518l2 2h1l2 2 2-2v2l-1 1-2 1c-3-1-4-1-6-2l2-4z" class="Ac"></path><path d="M487 748l6 13c1 5 3 10 5 14l5 16-3-3h0c-1-1-1-2-2-3l-2-7c-4-9-8-18-11-27h1l4 9v-2c0-1-1-2-1-3s-1-1-1-2c-1-2-1-2-1-4v-1z" class="j"></path><path d="M282 381l1-5c3 3 4 8 7 12l2 6 1 2c-1 1-2 1-3 2h-1v3h-1v-3c-1-1-2-2-2-4v-2c-1-2-2-2-2-4v-3l-2-2c0 1 0 2-1 2l-1 1c1-2 1-3 2-5z" class="AK"></path><path d="M282 381l1-5c3 3 4 8 7 12l2 6-1-1-1-1c0-1-2-5-3-5v2c-2-3-2-6-5-8zm149 368c2 0 5 2 6 3 1-1 0-1 0-2l1-1 2 3h0l2 1 9 19c-3-2-4-4-5-6h0c-1-1-1-1-2-1 0-1-1-2-1-2-3-2-4-5-7-6v-1l-1-1v-1l-3-2v-2l-1-1z" class="b"></path><path d="M438 749l2 3h0c0 1 1 1 0 2l-3-2c1-1 0-1 0-2l1-1z" class="AP"></path><path d="M432 750c2 1 3 2 5 3 1 2 4 3 6 6h-1l-6-3-1-1v-1l-3-2v-2z" class="j"></path><path d="M436 756l6 3h1l3 6v1h0c-1-1-1-1-2-1 0-1-1-2-1-2-3-2-4-5-7-6v-1z" class="AK"></path><path d="M327 489c1 0 2 1 2 1l2-2c0-1 1-1 2-1v1l1 3 1 2 2 6 1 2v1 1 3h-1c-2-2-2-3-5-3h0c-2-1-3-2-4-2l-1-1v-1c1-3 1-6 0-10h0z" class="AS"></path><path d="M333 488l1 3c-1 1 0 1-1 1s-1 0-2 1h-1c1-3 1-3 3-5z" class="q"></path><path d="M331 498l1-1 2 1v1h3l1 2v1l-7-4z" class="m"></path><path d="M334 491l1 2 2 6h-3v-1l-2-1-1 1-2-2c0-1 0-2 1-3h1c1-1 1-1 2-1s0 0 1-1z" class="c"></path><path d="M334 498v-5h1l2 6h-3v-1z" class="n"></path><defs><linearGradient id="BH" x1="281.369" y1="301.016" x2="295.77" y2="313.321" xlink:href="#B"><stop offset="0" stop-color="#868d96"></stop><stop offset="1" stop-color="#bbbac0"></stop></linearGradient></defs><path fill="url(#BH)" d="M289 291h1l1 1v-2l1 2 1 1c0 10-4 20-4 30l-1 3-1-1c0-5 0-10-1-15 1-2 1-5 2-7l1-11v-1z"></path><path d="M392 525l2 2v1c1 5 1 9 3 13 1 4 3 7 5 11l2 5 6 12 19 44h-1v-1l-1 1c-1-1-19-46-22-51l-10-20-2-7-1-10z" class="AQ"></path><path d="M463 698l2-2 14 34c2 3 3 7 5 11 1 2 2 5 3 7v1c0 2 0 2 1 4 0 1 1 1 1 2s1 2 1 3v2l-4-9h-1c-3-5-5-12-7-17-2-3-3-8-5-12l-10-24z" class="B"></path><path d="M478 734h1c0-2-1-3-2-5l-3-8c-1-1-1-2-1-3 1 1 1 2 1 3 1 0 0 0 1 1l1 2v1l1 1h0l2 4h0c2 3 3 7 5 11 1 2 2 5 3 7v1c0 2 0 2 1 4 0 1 1 1 1 2s1 2 1 3v2l-4-9h-1c-3-5-5-12-7-17z" class="U"></path><path d="M327 500l1 1c1 0 2 1 4 2h0c3 0 3 1 5 3h1v-3-1-1c1 1 1 2 2 3v1c0 2 0 4-1 5s-1 2-2 3l-1 3v1l1 3c0 1 0 2-2 3 0-1-1-1-2-2l-1 1c-1-2-1-3-2-4s-1-1-1-2c0-2-1-4-1-6v-1-3c-1-2-1-4-1-6z" class="c"></path><path d="M339 510c-1-1-1-2-2-3 2 0 2-1 2-2l1-1v1c0 2 0 4-1 5z" class="k"></path><path d="M328 506c0-2 0-2 2-3v2l-2 5v-1-3z" class="W"></path><path d="M333 521c0-1-1-2-1-3l1-1 3 1 1 2c0 1 0 2-2 3 0-1-1-1-2-2zm-3-16l3 3v1 1l-2 4-2 2c0-2-1-4-1-6l2-5z" class="AS"></path><path d="M331 514h-1v-1h-1c0-2 0-3 1-4h1l2 1-2 4z" class="Al"></path><path d="M351 409h1c1 3 1 7 2 9l8 27 1 5h-1l1 2v2l1 1h0v1l-1-1h0c0-1-1-1-1-2h0l-1-2c-1-1-1-2-2-3l1-1c-2-1-2-3-3-5l-7-17-1-1c2-2 0-2 0-4 0-1 1-2 1-2h1c-1-2-1-4-1-5v-1h1v-1-2z" class="d"></path><path d="M483 808v-3c0 2 1 3 1 4v-1-1h0c1 0 1 1 1 1 0 1 1 2 1 3v1l1 3h1c0 1 0 2 1 4 0 0 0 1 1 2s1 3 1 4c1 1 1 2 1 3h1 0c0 1 0 1 1 2v1c0 1 0 1 1 2 0 1 0 2 1 4l1 3h0c1 2 2 4 2 6 1 1 1 0 1 1s1 2 1 3v1l1 1 1 4c0 1 0 0 1 1v1 2l1 2h0c0 2 0 1 1 3v1 1 1 1h-1v1 1l-1-2c0-3-1-5-2-7-1-4-3-8-4-12-1-2-1-5-3-6 0-1-1-2-1-3-1-1-1-2-1-2v-1c0-1 0-1-1-2h0v-1h-1c1-1 0-2 0-3l-1-1v-2l-1-2c-1-1-1-1-1-2h0l-1-1v-1h0c0-2-1-3-1-5-1 0-1-1-1-2l1-1c-1-1-1-1-1-2h0l-1-1v-1c0-1-1-2-1-3z" class="M"></path><path d="M299 359h1l3 3c0 1 0 2 1 2l6 9c2 2 2 3 5 5 2 1 3 2 5 4s5 4 7 6 5 4 6 7l6 8 1-2h1c1 1 1 2 2 2 3 5 5 10 7 15 0 0-1 1-1 2 0 2 2 2 0 4-1-2-2-4-2-6-1-1-3-5-4-6-3-6-7-12-11-17s-9-11-15-13c-1-1-5-1-6-2-2-1-4-6-5-7-2-5-4-9-7-14z" class="Ab"></path><path d="M340 401h1c1 1 1 2 2 2 3 5 5 10 7 15 0 0-1 1-1 2 0 2 2 2 0 4-1-2-2-4-2-6v-1l-2-5-6-9 1-2z" class="z"></path><path d="M493 857l1 1v-1c-1-1-1-3-2-4v-3l-2-4c0-2-2-4-2-6 0 0 1 1 1 2 3 4 4 10 6 15s4 10 7 16h0c-2-2-3-5-4-7s-2-3-2-4 0-1-1-2v1l1 1v1h0c0 1 0 1 1 2 0 1 0 2 1 3v1c1 1 0 1 1 2v1l1 1v1h0l1 1v2l1 2 1 2v1c1 3 0 0 1 2 0 2 2 3 2 5l1 2h0c0 1 1 2 1 2v1 1h1v2 1l1 1c0 1 0 2 1 3 0 1 1 2 1 4v1-3c-1-1 0-5 0-6v-3h0v-4c-1-1-1-8-1-9v-1-2c1-7 0-15 0-22 0-2 0-5 1-8h0l1 53 1-4v15h0c-1-1-1-1-1-2l-1-1 1-1-2-2v1c-2-1-2-4-3-6v-1-1l-1-1h0v-2l-1-1c0-1-1-1-1-2s-1-2-1-3c-1-1-1-3-2-4s-1-2-1-3h0v-1l-1-3-1-1h0c-1-1-1-1-1-3 0-1-1-3-1-3l-4-9 2-2c-1-1-2-3-2-5z" class="V"></path><path d="M493 864l2-2c1 4 3 8 4 12 2 4 3 7 4 11 2 3 9 18 8 21l-2-5-7-15c-1-4-2-7-4-10 0-1-1-3-1-3l-4-9z" class="Aa"></path><path d="M360 598h2v1c2 0 4 1 6 2h1c2 1 4 2 5 4v1l2 2c1 2 1 4 2 6 1 4 2 7 4 11s4 7 7 10c1 2 4 7 7 8v2c-7-7-16-14-21-22 0-4-4-8-6-10l-3-6-6-9z" class="AG"></path><path d="M362 599c2 0 4 1 6 2h1c2 1 4 2 5 4v1h-1l-3-1-2-2c-2 0-3-1-4-3l-2-1z" class="Q"></path><path d="M368 603c2 0 2 0 4 1l1 2-3-1-2-2z" class="v"></path><path d="M373 606h1l2 2c1 2 1 4 2 6 1 4 2 7 4 11-2-1-5-4-5-7h0c0-1-1-2-1-3h-1c0-1 0-1-1-2s-2-3-3-4l-1-1v-3l3 1z" class="i"></path><path d="M360 598h2v1l2 1c1 4 4 7 5 10a30.44 30.44 0 0 0 8 8c0 3 3 6 5 7 2 4 4 7 7 10 1 2 4 7 7 8v2c-7-7-16-14-21-22 0-4-4-8-6-10l-3-6-6-9z" class="AA"></path><path d="M338 518l1-1 2 1 1 1c0 1 1 2 1 3 1 2 3 4 3 6l-1 1 2 2h0v3l-2 2c0 1-1 3-1 4l7 6c-1 1-1 1-3 0h-1c-4-2-9-7-12-11-2-4-4-9-5-13v-1l2 1 1-1c1 1 2 1 2 2 2-1 2-2 2-3l-1-3 2 1z" class="Y"></path><path d="M338 530h2v1c0 1 0 1-1 0l-1-1z" class="c"></path><path d="M344 531l1 1c1 0 1 0 2-1v3l-2 2c0 1-1 3-1 4l-3-2v-1-2c1-1 1-1 1-2h1v1l1 1v-1l-1-1 1-2z" class="n"></path><path d="M341 538l3-2h1 0c0 1-1 3-1 4l-3-2z" class="x"></path><path d="M338 518l1-1 2 1 1 1c0 1 1 2 1 3 1 2 3 4 3 6l-1 1 2 2h0c-1 1-1 1-2 1l-1-1-1-3v-1c-1-4-3-6-5-9z" class="J"></path><path d="M330 522v-1l2 1c2 5 4 11 9 15v1l3 2 7 6c-1 1-1 1-3 0h-1c-4-2-9-7-12-11-2-4-4-9-5-13z" class="AJ"></path><path d="M270 252c3 0 4 2 5 3l4 1c1 0 2 1 2 1l1-3 3 2 2 1c2 1 6 3 7 6l1 1-1 4h0l-1 8 1 5-1 3s-1 0-1 1l-1 5v2l-1-1h-1v1c-1-2-1-2-1-3v1c-2-1-1-2-2-4 0 0-1 0-1-1v1l-1-1-2 2v-1c-2-2-3-4-4-5 0-1-1-2-1-3-1-3-3-5-4-8h-1c2 1 3 3 4 4l1-1v-1c-1-1-1-2-1-3v-1c2 0 2 0 4 1v-2c1 1 1 0 2 1v1h1v-3c-3-5-8-10-13-14z" class="j"></path><path d="M283 272c1 1 2 1 3 2l2 8c-1-1-1 0-1-1-1-1-1-2-1-3l-2-2-1-3v-1z" class="AE"></path><path d="M283 266c1 2 3 5 3 8-1-1-2-1-3-2l-3-3v-2c1 1 1 0 2 1v1h1v-3z" class="g"></path><path d="M275 255l4 1c1 0 2 1 2 1 2 1 3 2 4 3l1 3-2-1-1 2-8-9z" class="Ac"></path><path d="M288 275c1 1 2 2 2 3s0 2 1 2l1 1h2l-1 3s-1 0-1 1l-1 5v2l-1-1h-1v-12l-1-4z" class="L"></path><path d="M292 281h2l-1 3s-1 0-1 1c0-2-1-3-1-5l1 1z" class="Ag"></path><path d="M285 260c1 0 2 1 2 1 1 2 3 4 4 5v3h0v1 2c0 1 0 2 1 3l1 1 1 5h-2l-1-1c-1 0-1-1-1-2s-1-2-2-3c-1-4-2-8-5-11l1-2 2 1-1-3z" class="p"></path><path d="M285 260c1 0 2 1 2 1 1 2 3 4 4 5v3c-2-2-4-4-5-6l-1-3z" class="I"></path><g class="f"><path d="M291 269v1 2c0 1 0 2 1 3l1 1 1 5h-2c0-1 0-2-1-3-1-3-1-5 0-9z"></path><path d="M282 254l3 2 2 1c2 1 6 3 7 6l1 1-1 4h0l-1 8-1-1c-1-1-1-2-1-3v-2-1h0v-3c-1-1-3-3-4-5 0 0-1-1-2-1-1-1-2-2-4-3l1-3z"></path></g><path d="M287 261s1-1 1-2c1 1 1 2 2 3l1-1 1 1v4h-1 0c-1-1-3-3-4-5z" class="AD"></path><path d="M282 254l3 2 2 1c0 1 1 2 1 2 0 1-1 2-1 2s-1-1-2-1c-1-1-2-2-4-3l1-3z" class="g"></path><path d="M292 262l2 2v4l-1 8-1-1c-1-1-1-2-1-3v-2-1h0v-3h0 1v-4z" class="r"></path><defs><linearGradient id="BI" x1="275.818" y1="274.929" x2="283.748" y2="285.019" xlink:href="#B"><stop offset="0" stop-color="#52596a"></stop><stop offset="1" stop-color="#777d87"></stop></linearGradient></defs><path fill="url(#BI)" d="M276 268c2 0 2 0 4 1l3 3v1l1 3 2 2c0 1 0 2 1 3 0 1 0 0 1 1v5 2 1c-2-1-1-2-2-4 0 0-1 0-1-1v1l-1-1-2 2v-1c-2-2-3-4-4-5 0-1-1-2-1-3-1-3-3-5-4-8h-1c2 1 3 3 4 4l1-1v-1c-1-1-1-2-1-3v-1z"></path><path d="M276 268c2 0 2 0 4 1l3 3v1l1 3c-1 0-2-1-3-1h0v3c-1-1-2-1-2-3 0-1-1-2-1-3-1-1-2-2-2-3v-1z" class="AZ"></path><path d="M276 268c2 0 2 0 4 1l3 3v1c-2-1-3-2-5-1-1-1-2-2-2-3v-1z" class="Ae"></path><path d="M281 278v-3h0c1 0 2 1 3 1l2 2c0 1 0 2 1 3 0 1 0 0 1 1v5 2 1c-2-1-1-2-2-4 0 0-1 0-1-1v1l-1-1c-1-1-1-2-2-3 0-1-1-2-1-4z" class="O"></path><path d="M282 282c0-1 1-2 1-3h1l2 4 1 1c0 1 0 2 1 3v2 1c-2-1-1-2-2-4 0 0-1 0-1-1v1l-1-1c-1-1-1-2-2-3z" class="t"></path><path d="M331 534h3l1 1c3 4 8 9 12 11h1c2 1 2 1 3 0l2 1c0 1 1 1 2 2l2-1 2 5c1 2 1 4 2 6v1c-1 0-2 1-3 2 1 0 1 1 2 2l2 2h2v2c0 2 1 2 2 3v2c-2-1-4-2-5-3l-1 1c0 1 1 1 2 2l1 1h-1 0c-2 0-3-1-4-1-3-1-5-4-7-7-3-3-7-7-9-11l-3-4h0c-3-3-4-6-6-8l-2-4c0-2-1-3 0-5z" class="AN"></path><path d="M357 559c0 1 1 2 1 3 1 0 1 1 2 2l2 2-2-1-2-2c-1 0-2-2-4-3 1 0 2 0 3-1z" class="q"></path><path d="M360 565l2 1h2v2c0 2 1 2 2 3v2c-2-1-4-2-5-3l-1-2-2-2c0-1 1-1 2-1z" class="AA"></path><path d="M351 557c-1-1-2-2-4-3l-1-1c-1-2-3-4-4-6l6 5 3 2h1l2 1v2c0 1-1 1-1 2l-2-2z" class="W"></path><path d="M351 554h1l2 1v2c0 1-1 1-1 2l-2-2v-2h-1l1-1z" class="AB"></path><path d="M351 546l2 1c0 1 1 1 2 2l2-1 2 5c1 2 1 4 2 6v1c-1 0-2 1-3 2 0-1-1-2-1-3-1 1-2 1-3 1l-1-1c0-1 1-1 1-2v-2l-2-1h1v-2l-3-3v-1l-3-2h1c2 1 2 1 3 0z" class="AS"></path><path d="M354 555c2 2 4 3 7 4v1c-1 0-2 1-3 2 0-1-1-2-1-3-1 1-2 1-3 1l-1-1c0-1 1-1 1-2v-2z" class="i"></path><path d="M354 557l3 2c-1 1-2 1-3 1l-1-1c0-1 1-1 1-2z" class="AO"></path><path d="M351 546l2 1c0 1 1 1 2 2l2-1 2 5-1 1c-2-2-5-4-8-6l-3-2h1c2 1 2 1 3 0z" class="v"></path><path d="M339 551l3 4c2 4 6 8 9 11 2 3 4 6 7 7 1 0 2 1 4 1h0 1l-1-1c-1-1-2-1-2-2l1-1c1 1 3 2 5 3l6 14c1 2 3 5 3 7l1 3v1c-1 1-1 2-1 3-2-1-4-1-5-1l-1 1h-1c-2-1-4-2-6-2v-1h-2c0-1-1-2-2-4l-3-5c0-1-2-3-3-4h3l-2-3-1-2h2v-2s0-1-1-2l-3-2c-2-2-4-4-5-6v-1l-3-7v-1c-1-1-3-3-4-5 0-1 0-2 1-3z" class="a"></path><path d="M371 589v2l-1-1-3-3h0c0-1 1-1 1-2l3 4z" class="J"></path><path d="M356 575c3 4 7 8 10 12-2-1-3-2-5-3 0 1 1 3 1 4-2-1-2-3-3-4s0-1-1-1l-4-5s0-1-1-2l3-1z" class="AG"></path><path d="M342 560c2 1 4 4 5 6 2 4 6 6 9 9l-3 1-3-2c-2-2-4-4-5-6v-1l-3-7z" class="m"></path><path d="M358 573c1 0 2 1 4 1h0 1l-1-1c-1-1-2-1-2-2l1-1c1 1 3 2 5 3l6 14c1 2 3 5 3 7l-4-5-3-4c-2-4-6-8-10-12z" class="k"></path><path d="M362 588c0-1-1-3-1-4 2 1 3 2 5 3 1 3 4 5 5 8 1 2 3 2 5 3-1 1-1 2-1 3-2-1-4-1-5-1l-1-1-2-1c-2-2-5-6-5-10z" class="q"></path><path d="M354 578l4 5c1 0 0 0 1 1s1 3 3 4c0 4 3 8 5 10l2 1 1 1-1 1h-1c-2-1-4-2-6-2v-1h-2c0-1-1-2-2-4l-3-5c0-1-2-3-3-4h3l-2-3-1-2h2v-2z" class="AJ"></path><path d="M368 601c0-1 0-1 1-2l1 1-1 1h-1z" class="a"></path><path d="M352 580h2 0c1 2 2 3 3 4v1 2l-2-2-2-3-1-2z" class="X"></path><path d="M358 587c0 1 1 2 1 3 0 2 3 5 4 7l-1 1h0-2c0-1-1-2-2-4v-7z" class="v"></path><path d="M352 585h3l2 2h1 0v7l-3-5c0-1-2-3-3-4z" class="m"></path></svg>