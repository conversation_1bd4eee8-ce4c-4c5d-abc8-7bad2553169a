<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="138 60 784 890"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#3e3c3d}.C{fill:#661d19}.D{fill:#807a79}.E{fill:#2a2a2a}.F{fill:#383738}.G{fill:#625f5e}.H{fill:#898382}.I{fill:#948d8b}.J{fill:#8f2620}.K{fill:#561815}.L{fill:#b6b0ae}.M{fill:#a6a09d}.N{fill:#401110}.O{fill:#313031}.P{fill:#585657}.Q{fill:#beb8b5}.R{fill:#942822}.S{fill:#1b1313}.T{fill:#39100e}.U{fill:#2f0d0b}.V{fill:#736e6e}.W{fill:#b7372f}.X{fill:#9f9997}.Y{fill:#787372}.Z{fill:#ae332b}.a{fill:#c4443b}.b{fill:#141211}.c{fill:#a82a22}.d{fill:#ada6a3}.e{fill:#201f20}.f{fill:#504d4e}.g{fill:#7a2520}.h{fill:#c13b33}.i{fill:#8f231d}.j{fill:#491513}.k{fill:#cf4d44}.l{fill:#661a16}.m{fill:#cc5248}.n{fill:#8f8a88}.o{fill:#cdc8c7}.p{fill:#696666}.q{fill:#681b17}.r{fill:#444243}.s{fill:#681814}.t{fill:#e66b5f}.u{fill:#999392}.v{fill:#494849}.w{fill:#9f312b}.x{fill:#6d6968}.y{fill:#252425}.z{fill:#2a0b09}.AA{fill:#cfc9c6}.AB{fill:#5a5755}.AC{fill:#79241f}.AD{fill:#9c9390}.AE{fill:#0b090a}.AF{fill:#4b4847}.AG{fill:#240a08}.AH{fill:#e67e75}.AI{fill:#4a100e}.AJ{fill:#d1cdca}.AK{fill:#682824}.AL{fill:#210b0a}.AM{fill:#da5d54}.AN{fill:#db665d}.AO{fill:#c53d34}.AP{fill:#f57365}.AQ{fill:#ee8a83}.AR{fill:#c92e25}.AS{fill:#f97e71}.AT{fill:#f5988d}.AU{fill:#f7b1ab}.AV{fill:#d9d5d3}.AW{fill:#ee8c84}.AX{fill:#f5f3f3}.AY{fill:#ebbeb8}.AZ{fill:#fff}.Aa{fill:#e75b4e}.Ab{fill:#efa49d}.Ac{fill:#f5bfb8}.Ad{fill:#f4c7c3}</style><path d="M888 249l3-2v1 4l-2 2c0-2-1-3-1-5z" class="u"></path><path d="M243 346c-1 0-1 0-1-1l1-1c2-3 5-2 9-2-2 1-3 1-5 2-2 0-3 0-4 1v1z" class="D"></path><path d="M261 285l5 3 2 1-1 2-6-3 1-1c0-1 0-1-1-2z" class="f"></path><path d="M343 480c1 1 2 1 4 2 1 1 2 3 3 5-3-2-6-3-8-4l1-3z" class="B"></path><path d="M157 186l5 5 6 6c-2-1-3-1-4-2s-1-1-2-1c-3-2-4-5-5-8z" class="p"></path><path d="M778 314c3-3 5-7 9-9-2 1-4 3-5 5v1c-1 1 0 1-1 2 0 1 0 1-1 2-1 2-2 3-4 4h0l-1-1c1-1 1-1 1-2v-1s1 0 2-1z" class="AD"></path><path d="M894 147c1 3 3 6 3 8-1 1-1 1-2 1s-2-1-3-2c1-2-1-3-2-5l1-1c2 1 2 0 3-1z" class="AR"></path><path d="M772 334c4 1 9 2 13 3l-3 2s-1 0-1 1c1 0 1 1 2 2h-1-1-2c0-1-1-2-1-3h2v-1h1c-2-1-4-1-5-1-2-1-3-2-4-3z" class="u"></path><path d="M252 341l2 1c-1 2-5 5-7 6h-1l-3-2v-1c1-1 2-1 4-1 2-1 3-1 5-2v-1z" class="V"></path><path d="M826 248c3 0 6 0 9 1v2c-4-1-6 0-10 1h-1v-1c-1-1-1-1-2-1v-1c2-1 3-1 4-1z" class="B"></path><path d="M787 305c1-2 3-2 5-2 1 0 2 1 3 1v1c-1 1-1 1-2 1-2 0-3 0-4 1v2h-2v-1c-2 1-3 2-4 3l-1-1c1-2 3-4 5-5z" class="Q"></path><path d="M684 485c3-2 5-5 9-4v1l1 1c-5 1-10 4-13 9l-1-2c1-2 3-4 4-5z" class="V"></path><path d="M251 336l12-3c-1 2-1 2-3 3v1c-1 1-3 1-4 2-2 0-2 0-3 1-1-1-3-2-4-3l2-1z" class="n"></path><path d="M253 340c-1-1-3-2-4-3l2-1c2 1 4 1 6 1l3-1v1c-1 1-3 1-4 2-2 0-2 0-3 1z" class="V"></path><defs><linearGradient id="A" x1="238.335" y1="275.484" x2="233.814" y2="285.696" xlink:href="#B"><stop offset="0" stop-color="#5e5957"></stop><stop offset="1" stop-color="#726e6f"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M240 275v1c-1 1-2 3-3 4 0 1 0 1 1 1l-1 1c0 2-1 3-1 5l1-1v4h-1l-2-1c-1-2-1-5 0-7 0-3 3-5 6-7z"></path><defs><linearGradient id="C" x1="256.262" y1="345.021" x2="248.238" y2="344.479" xlink:href="#B"><stop offset="0" stop-color="#736f6d"></stop><stop offset="1" stop-color="#918782"></stop></linearGradient></defs><path fill="url(#C)" d="M253 340c1-1 1-1 3-1v2h1c0 2-1 2-1 4-3 1-5 3-8 5h0c-3 1-3 3-6 4 2-2 3-3 4-6h1c2-1 6-4 7-6l-2-1 1-1z"></path><path d="M243 464c2 2 3 4 3 6l-1 1v2c0 2 0 3-1 4v1c-1 2-3 3-4 5h-1c-1 0-2 1-3 1l1 1h0c-2 0-2 0-3-1 1-1 1-1 2-1 1-1 2-1 3-1 2-2 3-4 3-6l1-1c1-4 1-7 0-11z" class="AA"></path><path d="M347 482c4 1 8 6 10 11 1 1 1 2 2 2v3 2c-3-6-5-9-9-13-1-2-2-4-3-5z" class="F"></path><path d="M411 694l1 2h1s1 0 2 1v2l-2 1h0c-1 1-2 2-4 3l-3 4v-2c0-2 1-4 1-6 1-1 3-2 4-3v-2z" class="D"></path><path d="M412 696h1s1 0 2 1v2l-2 1h0c-1 1-2 2-4 3 1-1 2-2 2-3 1-1 0-3 1-4z" class="P"></path><path d="M726 492h6c5 2 14 1 19 0l4-1s1 1 2 1c-10 3-21 5-31 0z" class="H"></path><path d="M518 128v-1c0-1 0-3 1-4 1 3 1 6 2 9l1 1c1 3 3 6 5 8 1 3 4 5 5 7l-1 1c-2-1-4-4-6-6-2-3-4-6-5-10v-2c-1 0-1 1-1 2h0c0-2 0-3-1-4v-1z" class="AA"></path><path d="M266 332l-8-3c-2-1-4-1-6-2-3-2-4-8-5-12-1 0-1 0-2-1 0-2 2-1 2-3-1-3-3-4-5-6h1c2 1 4 3 5 4 0 2 0 3 1 5 0 5 3 9 6 13l11 4v1z" class="E"></path><path d="M714 476l2 2h0l-1 1c-2 0-3 2-5 3-1 2-6 5-8 5 0 0-6-4-8-4l-1-1v-1l-1-2 2-1 2 2c2 2 4 3 7 3 4 0 9-4 11-7z" class="F"></path><path d="M617 758v-3c0-2 1-3 2-4 3-2 7-2 11-1s6 3 8 7c-1 0-3-1-4-1-2-1-3-3-5-3h-3c-2 0-4 0-6 1-1 1-2 2-3 4z" class="Q"></path><path d="M243 462l2-1c1 4 2 8 5 11h0c-1 5-3 9-7 11-2 1-4 1-6 2l-1-1c1 0 2-1 3-1h1c1-2 3-3 4-5v-1c1-1 1-2 1-4v-2l1-1c0-2-1-4-3-6v-2z" class="X"></path><path d="M798 376h1c2 5 3 11 6 16 2 4 4 7 6 10-3-1-5-3-7-5v1c-1-1-2-4-2-5-1-2-1-3-2-5 0-2-2-6-2-8h0v-4zM404 702l3-3c0 2-1 4-1 6v2l1 4h0c1 1 2 3 4 4h0v1c-1 0-1 0-2 1-1 0-3 0-4-1-2-1-3-3-4-5 0-3 1-6 3-9z" class="L"></path><path d="M404 702l3-3c0 2-1 4-1 6v2l1 4c-2 0-2 0-4-1v-2c1-2 1-3 1-5v-1z" class="H"></path><path d="M237 282c7-2 14-1 20 1l4 2c1 1 1 1 1 2l-1 1-9-3c-5-1-10-2-15 1l-1 1c0-2 1-3 1-5z" class="E"></path><path d="M395 764l1-1c0-4 2-8 5-10 3-3 7-4 12-3 2 1 3 2 4 3v1c0 1 1 2 1 4v1c-1 0-2 1-3 1h0l2-2c0-1 0-1-1-2l-3-2c-1-1-3-1-4-1-2 0-3 0-5 1-4 2-5 8-9 10z" class="o"></path><defs><linearGradient id="D" x1="252.493" y1="318.725" x2="249.094" y2="304.633" xlink:href="#B"><stop offset="0" stop-color="#858080"></stop><stop offset="1" stop-color="#9e9996"></stop></linearGradient></defs><path fill="url(#D)" d="M243 305c2-1 4 0 6 0v1l5 5c1 1 1 3 3 4l3-3c1 1 1 1 0 2l-3 3h1c0 1 0 0 1 1h-1c-2 1-2 1-4 0h-2l-2-3c0-1 0-1-1-1-1-2-1-3-1-5-1-1-3-3-5-4z"></path><path d="M249 306l5 5c1 1 1 3 3 4-1 1-1 0-2 0-2-1-3-3-4-5l-1-1h1c-1-1-1-1-2-3z" class="AD"></path><path d="M357 522l1 2-3 9c-3 6-5 13-7 19-1 3-3 6-3 9v1h-1s-1 0-1 1h-1l15-41z" class="D"></path><defs><linearGradient id="E" x1="787.152" y1="278.613" x2="782.548" y2="289.897" xlink:href="#B"><stop offset="0" stop-color="#171616"></stop><stop offset="1" stop-color="#494545"></stop></linearGradient></defs><path fill="url(#E)" d="M771 287l1-1c7-4 16-7 24-5 2 2 3 4 4 7v-1h1c0-1 1-1 1-2l1 1c-1 1-1 2-2 3h-2l-1-3c-2-2-5-3-9-3l-3 1h-1c-6 1-12 3-16 7h-1c0-1 2-3 3-4z"></path><path d="M792 460c-1 8-3 14 1 21l4 3h1v1c-4 0-7-1-9-4s-3-6-5-8l1-2c3-3 4-7 5-11h2z" class="M"></path><path d="M150 287v-3h1c1 7 3 12 5 18h1l5 9c1 1 1 2 1 4-5-4-10-11-12-17 0-2 0-2-1-3 0-1-1-3-1-4 0 0 1-1 1-2v-2z" class="F"></path><path d="M683 483l1 2c-1 1-3 3-4 5l1 2c-4 5-7 13-5 20-2-1-3-2-4-4-2-2-3-4-4-6h0c-1-2-1-3-2-4h0l1-1c1 4 3 8 6 11 0-1 0-1-1-2 0-2 0-4 1-6h0 0c1-1 1-1 1-2 2-6 5-11 9-15z" class="f"></path><defs><linearGradient id="F" x1="776.724" y1="314.411" x2="764.256" y2="296.961" xlink:href="#B"><stop offset="0" stop-color="#7f7877"></stop><stop offset="1" stop-color="#a39c96"></stop></linearGradient></defs><path fill="url(#F)" d="M763 296v1l2 2 1 1h0 1 1l-1-1v-1-2h0 0 1v1h1l1 1c1 7 2 12 8 16-1 1-2 1-2 1v1c0 1 0 1-1 2-2-1-4-3-5-5h0c-1-2-1-3-1-4-1-1-2-4-2-4 0-2-2-4-3-5-3-3-6-3-9-3 3 0 5 0 8-1z"></path><path d="M833 276l1 2c-4 4-6 7-7 11v7l1 1 1 3-3-1-2 3c-2-4-2-8-1-12 1-6 5-10 10-14z" class="G"></path><path d="M355 533v-1c1-1 1-2 2-3l1 2-1 1c-1 0-1 1-1 2v2l-4 13c-1 3-3 11-4 13h-3v-1c0-3 2-6 3-9 2-6 4-13 7-19z" class="x"></path><path d="M504 151h1v-1c0-4 4-8 6-11 3-6 4-12 7-17l1 1c-1 1-1 3-1 4v1c-3 9-6 15-12 22 2 1 4 3 5 5 1 1 1 2 2 3l1 5h0c-1 0-1 0-1-1-1-1-1-2-2-4-2-1-3-2-5-3l-1-2c0-1 0-1-1-2z" class="o"></path><path d="M715 471v1c0 1 0 2 1 3l-2 1c-2 3-7 7-11 7-3 0-5-1-7-3l-2-2-1-1c0-2 0-2 1-4h1v2c3 2 4 3 7 2 2 1 3 0 4 0h1c4 1 5-3 8-5v-1z" class="d"></path><path d="M625 699c3 2 7 6 7 11 0 1 0 3-1 4 0 1 0 1-1 2h-1c-1 1-1 1-2 1-2 0-3-1-5-2l3-3c1-1 2-3 1-4 0-4-3-6-6-7v-1-1l4 1 1-1z" class="AD"></path><path d="M629 716h-1c-1 0 0-1-1 0h0l-1-1 4-4v-1c1-2 0 0 1-1v-1h0v1s1 1 1 2c0 2-1 2-2 4v1h-1zm172-294c-1-1-1-2 0-3h0c-1-1-1-2-1-3l1-2c1-5 1-10 0-15v-6-1l1 1c0 1 1 4 2 5 1 6 0 12 0 19-1 6-2 13-4 20l-1-1c0-2 0-5 1-7 0-3 0-5 1-7z" class="M"></path><path d="M694 473h0c-1 2-1 2-1 4l1 1-2 1 1 2c-4-1-6 2-9 4l-1-2c-4 4-7 9-9 15 0 1 0 1-1 2h0c0-3 1-7 2-10 2-5 7-11 12-14l6-3h1z" class="p"></path><path d="M693 477l1 1-2 1 1 2c-4-1-6 2-9 4l-1-2c1-1 4-3 6-4 0 0 1 0 1-1 2-1 2-1 3-1z" class="P"></path><path d="M772 482c2-2 4-3 5-5 2-1 3-3 5-4h2c-8 9-16 15-27 19-1 0-2-1-2-1 1-1 3-2 5-3h0l-1-1h1v-2c1 0 2-1 3-2 2-1 4-1 6-1h3z" class="p"></path><path d="M769 482h3l-3 3h-2c-2 0-4 1-5 3h-2 0l-1-1h1v-2c1 0 2-1 3-2 2-1 4-1 6-1z" class="P"></path><defs><linearGradient id="G" x1="815.171" y1="251.297" x2="816.989" y2="263.455" xlink:href="#B"><stop offset="0" stop-color="#525152"></stop><stop offset="1" stop-color="#6d6b6b"></stop></linearGradient></defs><path fill="url(#G)" d="M822 250c1 0 1 0 2 1v1c-4 2-8 4-10 8-2 2-3 5-4 8l-3-12v-1c3-3 10-4 15-5z"></path><path d="M153 286h1l1 4c1 3 3 7 5 10l1 4c1 3 3 6 5 8l6 6c1 1 3 2 4 3v1c-5-1-9-4-13-7 0-2 0-3-1-4l-5-9c-2-5-3-10-4-16z" class="e"></path><path d="M491 164c0-2 0-4 1-5 1-3 3-6 6-8 2-1 4-1 6 0 1 1 1 1 1 2l1 2-1 1c-1-1-2-1-3-1 1 1 1 1 1 2-1 0-1 0-1 2h-1c0 1 0 0-1 1s-2 2-4 3h1l-2 2h-2l-1-1h-1z" class="AV"></path><path d="M492 164s1-1 1-2h0l2-1c2-2 4-5 7-6 1 1 1 1 1 2-1 0-1 0-1 2h-1c0 1 0 0-1 1s-2 2-4 3h1l-2 2h-2l-1-1z" class="AA"></path><defs><linearGradient id="H" x1="881.893" y1="271.114" x2="893.473" y2="266.888" xlink:href="#B"><stop offset="0" stop-color="#575653"></stop><stop offset="1" stop-color="#8a8687"></stop></linearGradient></defs><path fill="url(#H)" d="M886 256c2 0 3-1 5-1 0 11-1 25-5 35v-3-1l1-1v-1c0-1 0-1-1-2v-1c0-2 0-4-1-6 0 0 1-2 1-3 0-4 1-8 0-12v-4z"></path><defs><linearGradient id="I" x1="147.387" y1="276.071" x2="156.613" y2="269.929" xlink:href="#B"><stop offset="0" stop-color="#6d6b6a"></stop><stop offset="1" stop-color="#8a8686"></stop></linearGradient></defs><path fill="url(#I)" d="M158 238l1 1c-2 6-6 14-5 20-1 7-2 13-1 20v7c1 6 2 11 4 16h-1c-2-6-4-11-5-18l-1-5v-3c-1-3-1-15 1-18h1v-1l-3-1h0l1-1h2c2-3 3-9 4-12 1-2 1-3 2-5z"></path><defs><linearGradient id="J" x1="367.764" y1="516.41" x2="353.995" y2="524.163" xlink:href="#B"><stop offset="0" stop-color="#2e2d31"></stop><stop offset="1" stop-color="#656462"></stop></linearGradient></defs><path fill="url(#J)" d="M359 498c2 4 1 7 3 11h1l2-1c0 2-1 2 0 3v-1c1-1 1 0 1-1l3-3h1l-2 3c-1 3-4 5-4 9l-5 11-3 7v-2c0-1 0-2 1-2l1-1-1-2c-1 1-1 2-2 3v1l3-9-1-2 3-12c0-4 0-7-1-10v-2z"></path><path d="M360 510c1 2 1 4 0 7 0 2-1 5-2 7l-1-2 3-12z" class="G"></path><path d="M801 422c-1 2-1 4-1 7-1 2-1 5-1 7l1 1-3 10-2 5-3 8h-2c0-3 2-6 3-9-1-1-1-2-1-3v-1-8l1 1 2-8h0l1-2v1l1-1v-4c1-1 2-3 4-4z" class="D"></path><path d="M792 447h0c1-2 1-3 1-4l1-1c1 3-1 7 1 10l-3 8h-2c0-3 2-6 3-9-1-1-1-2-1-3v-1z" class="p"></path><path d="M801 422c-1 2-1 4-1 7-1 2-1 5-1 7l1 1-3 10c-1-2-1-3-1-5 1-2 1-4 1-6h-1c0 1 0 3-1 5-1-2 0-6 0-9l1-2v1l1-1v-4c1-1 2-3 4-4z" class="H"></path><path d="M518 128v1l-1 10c0 4 0 8-1 12-1 2-1 6-1 8-1 0-1-1-2-1h0c-1-1-1-2-2-3-1-2-3-4-5-5 6-7 9-13 12-22z" class="M"></path><path d="M515 141c1 2 1 4 1 6l-1 1-4 1c0-1 4-6 4-8z" class="L"></path><path d="M517 139c0 4 0 8-1 12-1 2-1 6-1 8-1 0-1-1-2-1h0c-1-1-1-2-2-3v-2c1-1 2-3 3-4 1 0 1 0 1-1l1-1c0-2 0-4-1-6l1-1v1-1l1-1z" class="d"></path><defs><linearGradient id="K" x1="806.566" y1="248.946" x2="796.319" y2="262.725" xlink:href="#B"><stop offset="0" stop-color="#3f3e40"></stop><stop offset="1" stop-color="#5d5c5e"></stop></linearGradient></defs><path fill="url(#K)" d="M792 257l1-1c2-2 7-4 10-4 2-1 4 0 5-1 5-1 10-2 14-2v1c-5 1-12 2-15 5-6 3-11 5-13 11l-3 6h-1c-1-1-1-1-1-2l1 1v-8-3-1l2-2z"></path><path d="M323 470c1 2 2 4 4 5v-2c3 3 5 5 9 5v1c2-1 3-1 4-1v1l1 1h2l-1 3-10 3c-5-2-9-5-11-9-1-2-1-3-2-4l1-1 1 2c1-1 1-1 1-2s0-1 1-2z" class="E"></path><path d="M336 479c2-1 3-1 4-1v1c-2 2-4 3-7 4l-6-3h1l4 1 1-1 3-1z" class="AJ"></path><path d="M323 470c1 2 2 4 4 5v-2c3 3 5 5 9 5v1l-3 1-1 1-4-1h-1c-2-1-5-4-6-6 1-1 1-1 1-2s0-1 1-2z" class="X"></path><path d="M812 368h4 1c2-2 3-5 4-7h0c1 2 1 3 1 5h0l-3 6h0c-2 3-6 4-9 5l-1 1c-2 0-3 0-5-1h-1c-1-1-4-4-5-4l-1-1 1-1h1 4v1l-1 1v1h1c2-2 4-4 6-5l-1-1 1-1c1 1 2 1 3 1z" class="L"></path><path d="M833 276c2-1 4-1 5-2 6-1 11 0 17 0h4l4 1c1 3 1 7 1 10l-1 1-1-1-1 1h-1 1c-1-2-4-2-6-3-1 0 0-1-1-2l-2-2h-2v-1l-4-1c-4-2-9-1-12 1l-1-2z" class="d"></path><path d="M859 274l4 1c1 3 1 7 1 10l-1 1-1-1-1 1h-1 1c-1-2-4-2-6-3-1 0 0-1-1-2l-2-2h-2v-1c3 0 8 3 10 4l-3-3c0-2 1-3 2-5z" class="e"></path><defs><linearGradient id="L" x1="140.782" y1="286.37" x2="154.021" y2="260.029" xlink:href="#B"><stop offset="0" stop-color="#424144"></stop><stop offset="1" stop-color="#76716f"></stop></linearGradient></defs><path fill="url(#L)" d="M151 298c-4-7-5-16-6-23 0-4-2-17 1-20l3 1h0l3 1v1h-1c-2 3-2 15-1 18v3l1 5h-1v3 2c0 1-1 2-1 2 0 1 1 3 1 4 1 1 1 1 1 3z"></path><defs><linearGradient id="M" x1="142.72" y1="277.914" x2="154.866" y2="261.284" xlink:href="#B"><stop offset="0" stop-color="#41433f"></stop><stop offset="1" stop-color="#686165"></stop></linearGradient></defs><path fill="url(#M)" d="M150 287l-2-8c-1-4 0-7-1-11 0-3-1-8 1-11l1-1 3 1v1h-1c-2 3-2 15-1 18v3l1 5h-1v3z"></path><path d="M780 260c4-2 8-2 12-3l-2 2v1 3 8l-1-1c0-3 0-7-2-9h-2c-3 1-5 3-7 6l1 1c0 1 1 1 2 2h0-1c-3 0-5 1-7 2-3 2-6 4-8 8l-1 1-1-1v-4c1-1 2-2 2-3 1-1 3-2 4-4 1-1 1-1 1-3l-1-1-3-3h3l3 2 8-4z" class="F"></path><path d="M780 260c4-2 8-2 12-3l-2 2v1 3 8l-1-1c0-3 0-7-2-9h-2c-3 1-5 3-7 6-1 1-1 1-2 1h-1l2-2v-1h-1c-1 0-2 1-2 2l-2 2c-1 0-1 0-1-1v-2h-1c1-1 1-2 2-2l8-4z" class="O"></path><path d="M252 474c0-2-1-3 0-5 1 3 4 5 5 7s4 4 5 5c2 1 6 3 8 3v-1c2 0 3 1 5 2 0 0-1 1-1 2 3 1 6 2 9 2 2 1 4 2 7 2-1 0-3 0-4 1 1 0 2 0 3 1l1-1 3 1h-4 10c2 0 3 0 4-1l2 2c-4 0-7 1-11 1-11 0-23-4-31-11-4-3-8-7-11-10z" class="p"></path><path d="M262 481c2 1 6 3 8 3v-1c2 0 3 1 5 2 0 0-1 1-1 2-1 0-1 0-1 1-1 0-2 0-3-1-3-2-6-4-8-6z" class="P"></path><path d="M274 487c3 1 6 2 9 2 2 1 4 2 7 2-1 0-3 0-4 1 1 0 2 0 3 1l1-1 3 1h-4c-5-1-11-2-16-5 0-1 0-1 1-1z" class="f"></path><path d="M598 712s1 0 1 1c1 1 2 2 2 3l1-1-1-1c0-7 3-12 8-16 2 0 3-1 4-2 2-2 4-3 6-4s3-2 5-3c-1 2-2 4-2 7l3 3-1 1-4-1v1 1c-4-1-6 0-10 2-3 3-6 6-5 11 0 6 3 9 7 14h0l-7-6-1-2c-1-1-2-3-3-3v1c-2-2-3-4-3-6z" class="r"></path><path d="M610 703c-1 0-2 1-3 1-1 1-1 1-2 1h0c1-3 3-4 5-6 3-1 6-3 9-2v1c-1 0-1 0-2 1h0l3 1v1c-4-1-6 0-10 2z" class="F"></path><path d="M788 312c1 1 2 1 3 3-1 0-2 0-3 1 0 0 0 3-1 3 0 4-2 7-5 9-5 2-11 4-17 5 2 0 5 1 7 1 1 1 2 2 4 3 1 0 3 0 5 1h-1v1h-2c0 1 1 2 1 3h2 1 1l4-1c2 0 4 1 6 2l1 1c-2 1-3 3-4 5h-3c-4-3-8-5-12-8-1-1-2-2-3-2l-1-1c-2-1-4-3-7-3-1-2-1-2 0-4v1h1c2-1 4-1 6-2l5-2c2 0 3-1 4-1l1-1c1-1 2-1 2-2l1-1c1-2 2-3 2-6 1-1 1-1 1-2s0-1 1-3z" class="H"></path><path d="M714 476l2-1 1 1c1 1 2 3 3 5 3 6 8 7 14 9-1-2-3-2-4-3 4 1 10 3 14 1 5-1 10-1 16-3v2h-1l1 1h0c-2 1-4 2-5 3l-4 1c-5 1-14 2-19 0h-6-1c-6-3-8-8-10-13l1-1h0l-2-2z" class="f"></path><path d="M744 488c5-1 10-1 16-3v2h-1c-6 2-11 4-17 4-3 0-5-1-8-1h0c-1-2-3-2-4-3 4 1 10 3 14 1z" class="H"></path><path d="M790 349c2 3 5 5 8 7 1 1 1 1 1 2l-1 2c1 2 5 3 7 4l7 4c-1 0-2 0-3-1l-1 1 1 1c-2 1-4 3-6 5h-1v-1l1-1v-1h-4-1l-1 1 1 1 1 3h-1l-5-11c-2-2-3-5-5-7-1-1-1-2-1-3l2 1h1l-2-3h2v-1l-3-3h3z" class="M"></path><path d="M793 365l-1-4c-1-1 0-1-1-1 3 3 6 7 8 11h-1l-1 1 1 1 1 3h-1l-5-11z" class="o"></path><path d="M805 364c-1 1-1 1-1 2h-3c-2-1-4-3-5-5s-3-3-4-6h1c1 1 2 1 4 1h1c1 1 1 1 1 2l-1 2c1 2 5 3 7 4z" class="I"></path><path d="M783 275c4-2 6-3 10-2 4 2 8 6 9 10 1 1 1 1 1 3l-1-1c0 1-1 1-1 2h-1v1c-1-3-2-5-4-7-8-2-17 1-24 5l-1 1c-3-1-3-1-5-3l17-9z" class="D"></path><path d="M783 275h1c1 0 2 0 3-1 2-1 4 0 6 0v1c-4 0-7 1-11 3-3 1-5 3-7 4 2 0 3 0 4-1h2l6-2h1c3-1 5 0 8 1v1c-8-2-17 1-24 5l-1 1c-3-1-3-1-5-3l17-9z" class="M"></path><path d="M789 427c0 2 1 3 1 5 0-1 0-2 1-3v7h1v-4c0 2 1 5 0 7v8 1c0 1 0 2 1 3-1 3-3 6-3 9-1 4-2 8-5 11l-1 2h-2c-2 1-3 3-5 4-1 2-3 3-5 5h-3c1-2 2-2 3-4 1-1 2-2 3-2h0c3-2 4-4 6-7 1-1 4-9 4-11 2-6 3-11 3-17h1c1-2 0-10 0-14z" class="v"></path><path d="M789 427c0 2 1 3 1 5-1 4 0 9 0 13h0 1v8c-1-1-1 0-1-1v-1l-1 1h-1l1-3v-1c0-2 1-5 0-7 1-2 0-10 0-14z" class="P"></path><path d="M791 429v7 9h-1 0c0-4-1-9 0-13 0-1 0-2 1-3z" class="p"></path><path d="M788 452h1l1-1v1c0 1 0 0 1 1l-1 1v2c-1 1-1 3-2 5l-3 6-1-1 2-7c1-1 1-2 1-3l1-4z" class="AB"></path><defs><linearGradient id="N" x1="893.798" y1="126.26" x2="883.18" y2="141.225" xlink:href="#B"><stop offset="0" stop-color="#790e06"></stop><stop offset="1" stop-color="#a31a14"></stop></linearGradient></defs><path fill="url(#N)" d="M888 121h1c2 9 3 18 5 26-1 1-1 2-3 1l-1 1c-4-5-8-11-14-15l2-2 2-2-2-2 10-7z"></path><defs><linearGradient id="O" x1="606.561" y1="719.09" x2="605.223" y2="737.37" xlink:href="#B"><stop offset="0" stop-color="#4a4849"></stop><stop offset="1" stop-color="#747170"></stop></linearGradient></defs><path fill="url(#O)" d="M601 718v-1c1 0 2 2 3 3l1 2 7 6h0c4 3 7 6 11 8l-6 3h-1l2-2c-2 0-5 1-8 2v-1h0-1-3-1c-1-1-2-2-3-2h-1l-1-2-2 1v-17c0-1 0-1 1-2l1 2h0 1z"></path><defs><linearGradient id="P" x1="235.607" y1="357.879" x2="242.442" y2="367.506" xlink:href="#B"><stop offset="0" stop-color="#8f8988"></stop><stop offset="1" stop-color="#aea7a3"></stop></linearGradient></defs><path fill="url(#P)" d="M242 354c3-1 3-3 6-4-1 2-1 3-2 4s-2 2-2 4c-1 1-1 2-2 3v2c-2 3-4 6-5 9-1 2-3 4-5 4l-1 1h-6-1l-2-1h-1c-2-1-3-2-5-4-1-2-2-4-1-6h0c0 1 1 3 2 4v1c2 2 2 2 5 3h0l-4-4-1-2v-1h0l1 1c2 0 3 1 5 0 3-1 6 0 8-3-1 1-3 1-4 2h-1v-1c3-1 7-2 10-4 1-2-1-2-1-4l7-4z"></path><path d="M218 370l-1-2v-1h0l1 1c2 0 3 1 5 0 3 2 5 3 7 6h1v1c-1 0-2 1-3 1l-1-1c-1 0-3-1-4-2s-3-3-5-3z" class="Q"></path><path d="M230 398h1c1 1 1 2 1 3l1 1 2 21c0-1 0-4 1-5v-1c-1-3-1-10 0-13l2-6v8 1l1 3h2l1 1 1 1-2 10c-1-2-1-2-1-4h0l-2 1 2 19c2 8 2 16 5 23h0l-2 1c-3-13-9-24-10-37-1-6-1-11-2-17 0-2 1-7-1-10z" class="I"></path><path d="M238 398v8 1l1 3h2l1 1 1 1-2 10c-1-2-1-2-1-4h0l-2 1 2 19h-1c-1-2-1-4-1-6-2-9-2-18-2-28l2-6z" class="o"></path><path d="M238 419c0-2 0-5 1-7l3-1 1 1-2 10c-1-2-1-2-1-4h0l-2 1z" class="F"></path><path d="M162 194c1 0 1 0 2 1s2 1 4 2h0c2 1 4 3 6 5 2 0 2 0 4-1l1-1h0c2 0 3 0 4-1v1c-2 2-5 3-6 6a30.44 30.44 0 0 0-8 8l-2 3h-1c-1 2-1 4-2 6-3 5-5 10-6 15-1 2-1 3-2 5-1 3-2 9-4 12h-2c-2 0-3 0-5-1v-5c1 0 2 1 3 2 1-1 1-3 2-5 1-4 2-7 4-11 3-8 6-15 11-22 3-3 6-7 10-10-5-2-10-5-13-8v-1z" class="Y"></path><path d="M150 246l1-1v1c2-4 4-8 5-12l1 1v1c-1 2-1 4-1 6v1c-1 3-2 9-4 12h-2c-2 0-3 0-5-1v-5c1 0 2 1 3 2 1-1 1-3 2-5z" class="V"></path><path d="M432 769c3-3 6-9 7-12l1 4h2c-6 10-8 18-21 22-5 2-12 1-17-1-4-2-7-6-8-10-1-2-1-5 0-7 3 5 5 10 12 11 6 2 14 1 19-3 2-1 3-3 5-4z" class="O"></path><path d="M144 120h7l7 7h0c-1 0-2 1-3 1l2 2h6-1c-3 1-6 0-8 3v3l-1 1c-4 4-13 11-13 16v1l-3 3h-1c-1-3 2-11 3-14 2-7 3-15 5-23z" class="C"></path><path d="M144 120h7l7 7h0c-1 0-2 1-3 1l-1-1c-3-2-7-6-10-7z" class="AT"></path><path d="M154 127l1 1 2 2h6-1c-3 1-6 0-8 3v3l-1 1c-4 4-13 11-13 16v1l-3 3h-1c-1-3 2-11 3-14h3c2-3 3-6 6-8 2-3 4-5 6-8z" class="J"></path><path d="M293 487c3 0 6 0 9-1 4-3 9-7 11-12h1c1-3 0-7 0-10l4 7 1 2h0c1 1 1 2 2 4-3 7-4 12-11 15l-5 2-2-2c-1 1-2 1-4 1h-10 4l-3-1-1 1c-1-1-2-1-3-1 1-1 3-1 4-1-3 0-5-1-7-2-3 0-6-1-9-2 0-1 1-2 1-2 6 2 11 3 18 2z" class="d"></path><path d="M298 488c3 0 6-1 9-2-3 2-5 3-8 4-1 0-1 0-2 1-2 1-5 0-7 0-3 0-5-1-7-2 5-1 10 1 15-1z" class="V"></path><defs><linearGradient id="Q" x1="278.443" y1="489.792" x2="287.057" y2="484.708" xlink:href="#B"><stop offset="0" stop-color="#787476"></stop><stop offset="1" stop-color="#8c8782"></stop></linearGradient></defs><path fill="url(#Q)" d="M275 485c6 2 11 3 18 2 2 1 3 1 5 1-5 2-10 0-15 1-3 0-6-1-9-2 0-1 1-2 1-2z"></path><path d="M319 473h0c1 1 1 2 2 4-3 7-4 12-11 15l-5 2-2-2c-1 1-2 1-4 1h-10 4l-3-1-1 1c-1-1-2-1-3-1 1-1 3-1 4-1 2 0 5 1 7 0 3-1 7-1 9-3 7-3 10-8 13-15z" class="AB"></path><defs><linearGradient id="R" x1="239.97" y1="280.734" x2="266.17" y2="281.789" xlink:href="#B"><stop offset="0" stop-color="#817a77"></stop><stop offset="1" stop-color="#a09896"></stop></linearGradient></defs><path fill="url(#R)" d="M261 271c4 2 7 6 9 10 2 0 4-1 5-2h1c1 1 1 1 1 2-1 1-3 2-5 3v1 1c-1 2-2 3-2 5-1 1-1 1-3 2v-2l1-2-2-1-5-3-4-2c-6-2-13-3-20-1l1-1c-1 0-1 0-1-1 1-1 2-3 3-4v-1c1-1 2-2 4-2 1 0 2 1 4 1v-1c3 0 5-1 8-1l5-1z"></path><path d="M242 278c0-1 1-1 2-2s2-1 3-1c2 2 2 2 4 2-1 1-3 2-5 2-1 0-1-1-2-1h-2z" class="D"></path><path d="M240 275c1-1 2-2 4-2 1 0 2 1 4 1v-1l6 3c-1 0-1 1-2 0-1 0-3 0-5-1-1 0-2 0-3 1s-2 1-2 2c-2 1-3 2-4 3-1 0-1 0-1-1 1-1 2-3 3-4v-1z" class="AB"></path><path d="M275 279h1c1 1 1 1 1 2-1 1-3 2-5 3v1 1c-1 2-2 3-2 5-1 1-1 1-3 2v-2l1-2-2-1 2-2c-2-4-8-4-9-7 3 2 6 4 9 4l2-2c2 0 4-1 5-2z" class="V"></path><path d="M261 271c4 2 7 6 9 10l-2 2c-3 0-6-2-9-4-1-1-3-2-5-3l-6-3c3 0 5-1 8-1l5-1z" class="AX"></path><path d="M782 310l1 1c1-1 2-2 4-3v1h2v-2c1-1 2-1 4-1-2 2-3 3-4 5l-1 1c-1 2-1 2-1 3s0 1-1 2c0 3-1 4-2 6l-1 1c0 1-1 1-2 2l-1 1c-1 0-2 1-4 1l-5 2c-2 1-4 1-6 2h-1v-1c-1 0-1 0-2 1h-1l1-2c-1-1-3-1-4-2l1-1c-3 0-5-1-6-4h3v1c1 0 1 0 2 1 2-1 3 0 5-2 5-4 5-8 6-14 0 1 0 2 1 4h0c1 2 3 4 5 5l1 1h0c2-1 3-2 4-4 1-1 1-1 1-2 1-1 0-1 1-2v-1z" class="d"></path><path d="M769 309c0 1 0 2 1 4h0c1 2 3 4 5 5l1 1-1-1c-2 0-3-1-4-2 0 1 0 0 1 1v3h0l-2-6-1 5c-1 3-3 6-5 8h-2v2 1c-1-1-3-1-4-2l1-1c-3 0-5-1-6-4h3v1c1 0 1 0 2 1 2-1 3 0 5-2 5-4 5-8 6-14z" class="X"></path><path d="M608 121h6l11 6h0l5 4h-1-2l-1 1h-1c-3 3-4 4-4 8-2 2-3 3-4 6-1 1-4 3-4 4-1 1-2 2-3 2l-1-2v-4-6l-3-17v-2h2z" class="a"></path><path d="M622 129c1 0 2 0 3 1-1 2-6 7-8 9 0-3 1-6 3-8l2-2z" class="Z"></path><path d="M606 123v-2h2c1 1 2 2 4 2 1 0 3 0 4 1l1 1c1 1 3 3 5 4l-2 2c-2 2-3 5-3 8-3 3-7 7-8 11v-4-6c1-1 3-1 4-2s3-4 3-5h0 1 1c0-1 0-1 1-2 0-2-2-3-3-3v-2l-1-1-2-1c-2-1-5-2-7-1z" class="R"></path><defs><linearGradient id="S" x1="604.407" y1="127.144" x2="616.829" y2="134.279" xlink:href="#B"><stop offset="0" stop-color="#5f1715"></stop><stop offset="1" stop-color="#8f2823"></stop></linearGradient></defs><path fill="url(#S)" d="M606 123c2-1 5 0 7 1l2 1 1 1v2c1 0 3 1 3 3-1 1-1 1-1 2h-1-1 0c0 1-2 4-3 5s-3 1-4 2l-3-17z"></path><path d="M593 750l1 1c2 10 6 19 15 24 5 3 11 4 17 3 5-1 8-5 11-9 1-2 2-4 2-6 0-1 0-1 1-2 1 5 1 10-3 15-3 5-8 7-14 8-7 1-14-2-19-6-6-4-10-11-12-17l-23 49c0-1 0-3 1-3 0-1 0-1 1-2 0-1 1-2 1-3l1-1v-1-1-1c1 0 1-1 1-1 1-2 0-1 1-2l1-1v-1c0-1 2-4 2-5v-1c1-1 1 0 1-1l1-3c1-1 1-1 1-2l2-3c0-1 0-1 1-2h0l1-3 1-1 1-1c0-1 0-2 1-3v-1h0l1-1v-1l1-2c0-1 1-1 1-2h-1l1-3c0-2 0-2-2-3 1 0 1-1 1-2l3-3z" class="e"></path><defs><linearGradient id="T" x1="693.357" y1="594.612" x2="685.095" y2="593.573" xlink:href="#B"><stop offset="0" stop-color="#6d6b6b"></stop><stop offset="1" stop-color="#8a8786"></stop></linearGradient></defs><path fill="url(#T)" d="M689 550c2 5 4 11 5 17 2 9 2 19 1 28h0c-1 12-4 26-10 37 0-2 0-3 1-4v-1l1-1h0l1-3h0v-1-1c1-1 1-1 1-2h0v-1c1-1 1-1 1-2v-1c0-1 0-1 1-2h0c-1-1-1-2-2-2-1 2-1 4-2 6v1h0c0 1 0 2-1 3v1c-1 1-1 3-2 5h-1 0-2l-6 12c-1-3 1-4 1-6 1-4 4-7 5-11 8-19 10-38 6-59l2 2c0 1-1 0 1 1 1-1 0-1 1-1l1-1h0v-1h-3 1c1-1 0-1 1-1h1v-2l-1-1v-1c-1-2 0-3-1-4h-1c1-2 0-2 0-3l-1-1h1z"></path><path d="M683 627c1-3 2-7 3-10 3-8 4-17 6-26h0v2 10c1-2 1-4 1-6 1-1 1-2 1-3l1 1c-1 12-4 26-10 37 0-2 0-3 1-4v-1l1-1h0l1-3h0v-1-1c1-1 1-1 1-2h0v-1c1-1 1-1 1-2v-1c0-1 0-1 1-2h0c-1-1-1-2-2-2-1 2-1 4-2 6v1h0c0 1 0 2-1 3v1c-1 1-1 3-2 5h-1 0z" class="f"></path><path d="M238 419l2-1h0c0 2 0 2 1 4v17l1 1c1-2 0-3 0-5 1-2 1-4 2-5v6h1v2h0c1-2 1-4 2-7v1h1c0-2 0-3 1-4v1 3-2c1-2 1-3 1-4l1 1c0 4 0 8-1 12v14c1 4 2 7 4 10h1c3 4 5 6 8 8 0 0 0 1-1 1s-2 0-3 1l2 2c1 1 7 5 8 6l2 1-1 1v1c-2 0-6-2-8-3-1-1-4-3-5-5s-4-4-5-7c-1 2 0 3 0 5l-2-2h0c-3-3-4-7-5-11h0c-3-7-3-15-5-23l-2-19z" class="AF"></path><path d="M245 461v-1s0-1-1-2v-2h0c2 1 3 5 4 7 0 3 2 4 4 6-1 2 0 3 0 5l-2-2h0c-3-3-4-7-5-11h0z" class="P"></path><path d="M249 432v-2c1-2 1-3 1-4l1 1c0 4 0 8-1 12v14c1 4 2 7 4 10h1c3 4 5 6 8 8 0 0 0 1-1 1s-2 0-3 1c-4-4-7-11-8-16h-1c-2-8-1-17-1-25z" class="Y"></path><path d="M851 196c2 2 4 5 7 6l1 1h1c7 4 15 15 19 23 1 2 2 5 3 8l6 15c0 2 1 3 1 5l2-2v3c-2 0-3 1-5 1h-2v2c-1 0-1 0-1-1-3-18-13-33-25-45-3-3-6-7-10-9l1-1 2 2v-1l-2-2 2-1h0c1 1 2 1 3 1-2-2-2-2-3-5z" class="H"></path><path d="M849 201l2-1h0c1 1 2 1 3 1l3 4c2 4 6 7 9 10 5 6 8 12 11 19 1 2 2 3 2 5 1 1 1 2 2 4 0 3 1 9 3 12 2 0 3-1 5-1l2-2v3c-2 0-3 1-5 1h-2v2c-1 0-1 0-1-1-3-18-13-33-25-45-3-3-6-7-10-9l1-1 2 2v-1l-2-2z" class="o"></path><path d="M205 251v-1c3-1 5-1 8-1 12-1 21 2 31 6 1 1 2 2 3 2h1c1 0 2 1 3 2v1l1 1c1 0 2 1 4 1 1 1 2 2 3 4l1 3v1l1 1-5 1c-3 0-5 1-8 1v1c-2 0-3-1-4-1l2-1v-2l-2-8h-2v2c-1-1-1 0-1-1-3-3-5-4-9-6l-5 11h-1v-3c-1-2-2-5-4-7-4-5-11-6-17-7z" class="B"></path><path d="M244 260h1 1l1-1 1 2-1 1c1 1 1 1 1 2 0 2 0 4-1 6h-1l-2-8v-2z" class="E"></path><path d="M248 257c1 0 2 1 3 2v1l1 1c1 0 2 1 4 1 1 1 2 2 3 4h-3-3 0l-3-2h0l1-2h-1c0-2-1-3-2-5z" class="e"></path><path d="M251 262l1 1h0c1 0 2 0 3 1h0c0 1 0 1 1 2h-3 0l-3-2h0l1-2z" class="E"></path><path d="M227 254c4 0 5 1 8 2 1 0 3 1 4 1 1 1 2 1 3 2 0 0 1 0 2 1v2h-2v2c-1-1-1 0-1-1-3-3-5-4-9-6-2-1-3-2-5-3z" class="P"></path><path d="M226 265c1-2 2-3 1-5 0-3-3-5-5-6-1-1-2-2-3-2 3 0 6 1 8 2s3 2 5 3l-5 11h-1v-3zm22-1c2 1 3 2 5 2h0 3 3l1 3v1l1 1-5 1c-3 0-5 1-8 1v1c-2 0-3-1-4-1l2-1v-2h1c1-2 1-4 1-6z" class="G"></path><path d="M253 266h3 3l1 3c-3-1-5-1-7-3z" class="O"></path><path d="M246 272c3-2 3-3 7-2 1 0 2 1 3 2-3 0-5 1-8 1v1c-2 0-3-1-4-1l2-1z" class="D"></path><path d="M755 297c3 0 6 0 9 3 1 1 3 3 3 5 0 0 1 3 2 4-1 6-1 10-6 14-2 2-3 1-5 2-1-1-1-1-2-1v-1h-3-1c-3-3-4-5-5-9v1c-2-1-1-1-1-2s-1-2-2-3c1-1 2-3 3-5-1-1 0-3 1-5 2-1 5-2 7-3z" class="Q"></path><defs><linearGradient id="U" x1="753.961" y1="302.007" x2="756.237" y2="314.793" xlink:href="#B"><stop offset="0" stop-color="#1c1a1a"></stop><stop offset="1" stop-color="#353433"></stop></linearGradient></defs><path fill="url(#U)" d="M747 305c3-2 6-4 11-4 2 1 4 2 6 4 1 2 2 5 1 7-1 3-2 6-4 8h-1l1-1c0-1 1-1 1-2 1-2 0-7-1-9s-2-3-4-3c-2-1-4 0-5 1l-2 2c-2 2-3 4-3 6v1c-2-1-1-1-1-2s-1-2-2-3c1-1 2-3 3-5z"></path><path d="M750 308l2-2c1-1 3-2 5-1 2 0 3 1 4 3s2 7 1 9c0 1-1 1-1 2l-1 1h1c-2 1-3 2-5 3h-3-1c-3-3-4-5-5-9 0-2 1-4 3-6z" class="AB"></path><path d="M750 308l2-2c1-1 3-2 5-1 2 0 3 1 4 3h-4c-1 0-3 1-3 1v-1c-1 0-2 0-2 1 0 2 0 2-1 3h-1l1-3-1-1z" class="AF"></path><defs><linearGradient id="V" x1="615.352" y1="744.32" x2="617.294" y2="777.802" xlink:href="#B"><stop offset="0" stop-color="#87817d"></stop><stop offset="1" stop-color="#b8b1ae"></stop></linearGradient></defs><path fill="url(#V)" d="M594 751v-1-4l1-1c1 1 1 1 1 2v1l3 8c0 1 1 2 1 3 1 1 1 2 1 3 3 4 7 8 11 9v1c4 2 7 2 11 1 1 0 2 0 3-1 0-1 4-4 5-5h-2l-1-1-4 3v-1h0v-1h-2v-1c1 0 2-1 3-2s0-2 0-3v-1c-3-1-5 0-8 0l-1-1 1-1c1-2 2-3 3-4 2-1 4-1 6-1h3c2 0 3 2 5 3 1 0 3 1 4 1 1 1 2 3 2 4-1 1-1 1-1 2 0 2-1 4-2 6-3 4-6 8-11 9-6 1-12 0-17-3-9-5-13-14-15-24z"></path><path d="M617 758c1-2 2-3 3-4 2-1 4-1 6-1 3 1 6 3 7 6s1 5 0 7c-1 3-5 5-7 6 0-1 4-4 5-5h-2l-1-1-4 3v-1h0v-1h-2v-1c1 0 2-1 3-2s0-2 0-3v-1c-3-1-5 0-8 0l-1-1 1-1z" class="B"></path><path d="M625 760c2 1 3 0 4 1v2c1-1 1-2 2-2 1 1 1 3 1 5-1 1-1 0-1 1h-2l-1-1-4 3v-1h0v-1h-2v-1c1 0 2-1 3-2s0-2 0-3v-1z" class="AB"></path><defs><linearGradient id="W" x1="273.665" y1="319.252" x2="255.109" y2="317.143" xlink:href="#B"><stop offset="0" stop-color="#968b87"></stop><stop offset="1" stop-color="#b0a9a6"></stop></linearGradient></defs><path fill="url(#W)" d="M267 293c2-1 2-1 3-2v4h1v1h0l1-1 3-3v2l-3 3-1 2h1l-1 1c-1 1-3 3-3 5-1 1 0 6 0 7 1 2 1 4 2 5h1c1 2 2 3 4 4h0c1 0 1 0 2-1h0 3c1-1 1-1 3-1 0 0 1 0 1-1h1c0-1 1-2 2-3 0 2 0 4 1 6l-2 2c-1 1-3 3-5 4h0l-3 1c0-1 0-1-1-2h0-1c0 2-2 3-2 4s0 1-1 2c-3 0-4-1-7-1l-11-4c-3-4-6-8-6-13 1 0 1 0 1 1l2 3h2c2 1 2 1 4 0h1c-1-1-1 0-1-1h-1l3-3c1-1 1-1 0-2 4-6 5-12 7-19z"></path><path d="M270 322l-1-1c-1-3-3-6-2-8l1-1c1 2 1 4 2 5 0 2 1 3 1 4l-1 1z" class="I"></path><path d="M270 322l1-1c2 2 4 4 7 4h2c-1 0-1 1-3 1h0-1c0 2-2 3-2 4l-1-2c0-1-1-1-1-2 0-2-1-3-2-4z" class="D"></path><path d="M259 318c1-1 2-1 3-2 0 2-2 4-3 6-2 0-6-2-7-3v-1h2c2 1 2 1 4 0h1z" class="H"></path><path d="M271 317c1 2 2 3 4 4h0c1 1 3 1 4 2h3l-2 2h-2c-3 0-5-2-7-4 0-1-1-2-1-4h1z" class="Q"></path><path d="M285 318c0-1 1-2 2-3 0 2 0 4 1 6l-2 2c-1 1-3 3-5 4h0l-3 1c0-1 0-1-1-2 2 0 2-1 3-1l2-2h-3c-1-1-3-1-4-2 1 0 1 0 2-1h0 3c1-1 1-1 3-1 0 0 1 0 1-1h1z" class="r"></path><path d="M284 318h1c0 2 0 3-1 4-2 0-3 0-5 1-1-1-3-1-4-2 1 0 1 0 2-1h0 3c1-1 1-1 3-1 0 0 1 0 1-1z" class="O"></path><defs><linearGradient id="X" x1="259.298" y1="304.57" x2="267.363" y2="305.448" xlink:href="#B"><stop offset="0" stop-color="#777174"></stop><stop offset="1" stop-color="#88847f"></stop></linearGradient></defs><path fill="url(#X)" d="M267 293c2-1 2-1 3-2v4l-3 10c-1 4-3 8-5 11-1 1-2 1-3 2-1-1-1 0-1-1h-1l3-3c1-1 1-1 0-2 4-6 5-12 7-19z"></path><defs><linearGradient id="Y" x1="668.227" y1="533.318" x2="685.986" y2="530.097" xlink:href="#B"><stop offset="0" stop-color="#5d5a5b"></stop><stop offset="1" stop-color="#989492"></stop></linearGradient></defs><path fill="url(#Y)" d="M657 502v-3-1l-1-1v-1h0c2 0 4 6 6 7v1h1c0-1 1-2 1-2 2-1 2-1 4 0h0c1 2 2 4 4 6 1 2 2 3 4 4l13 38h-1l1 1c0 1 1 1 0 3h1c1 1 0 2 1 4v1l1 1v2h-1c-1 0 0 0-1 1h-1 3v1h0l-1 1c-1 0 0 0-1 1-2-1-1 0-1-1l-2-2c-1-2-1-5-2-7h0c-4-11-7-22-13-33v3h0c-2-1-4-6-6-8l-1 2c-2-3-2-6-3-8l-5-10z"></path><path d="M666 518l-3-6v-1c0-1 0-2-1-2l-1-4 3 3 6 10c0 1 2 4 2 5v3h0c-2-1-4-6-6-8z" class="d"></path><path d="M670 518c3 3 6 10 8 16 1 1 1 4 2 5h1c2 1 3 3 4 5s0 5 0 8c1 0 0 1 0 2l1 1-1 1h0c-4-11-7-22-13-33 0-1-2-4-2-5z" class="n"></path><defs><linearGradient id="Z" x1="274.462" y1="302.981" x2="281.375" y2="315.79" xlink:href="#B"><stop offset="0" stop-color="#1a1818"></stop><stop offset="1" stop-color="#3a3838"></stop></linearGradient></defs><path fill="url(#Z)" d="M272 299c1 0 2-1 3-1h2 1c2 2 6 2 6 4 2 2 4 3 6 5l1 1v1c1 2 1 4 1 6l1 1v4h1c-1 4 0 11-2 15 0-2 1-5 0-7l-1 2h-1c-1 3-2 4-4 5-1 1-3 0-4 0s-1 0-2 1l-2-2h1c0-1 0-2 1-3l1 1v-3-2h0c2-1 4-3 5-4l2-2c-1-2-1-4-1-6-1 1-2 2-2 3h-1c0 1-1 1-1 1-2 0-2 0-3 1h-3 0c-1 1-1 1-2 1h0c-2-1-3-2-4-4h-1c-1-1-1-3-2-5 0-1-1-6 0-7 0-2 2-4 3-5l1-1z"></path><path d="M272 299c1 0 2-1 3-1h2 1c2 2 6 2 6 4-2-1-4-1-6-1l-1 1c-3 0-5 1-6 4-2 2-2 5-1 8l1 3h-1c-1-1-1-3-2-5 0-1-1-6 0-7 0-2 2-4 3-5l1-1z" class="L"></path><path d="M277 305l2 1-1 2h1 1v1l3 2c1 2 2 3 2 6l-1 1h0c0 1-1 1-1 1-2 0-2 0-3 1l-4-1c-2-1-3-4-4-6h0c0-3 1-4 2-6 1-1 2-1 3-2z" class="f"></path><path d="M277 305l2 1-1 2h1 1v1h-2c0 1-1 2-2 3h-1c-1-2 0-2 0-3l-1-2c1-1 2-1 3-2z" class="B"></path><path d="M272 313l1-1c1 0 1 1 1 2 2-1 4-2 4-4l1 1v2c1 0 1 1 2 1s1 0 2 1v1c0 1 0 1 1 2h0c0 1-1 1-1 1-2 0-2 0-3 1l-4-1c-2-1-3-4-4-6z" class="P"></path><path d="M277 305l1-1c2 1 5 3 7 4h1v-1h0v-2c2 1 2 2 4 2l1 1v1c1 2 1 4 1 6l1 1v4h1c-1 4 0 11-2 15 0-2 1-5 0-7l-1 2h-1c-1 3-2 4-4 5-1 1-3 0-4 0s-1 0-2 1l-2-2h1c0-1 0-2 1-3l1 1v-3-2h0c2-1 4-3 5-4l2-2c-1-2-1-4-1-6-1 1-2 2-2 3h-1 0l1-1c0-3-1-4-2-6l-3-2v-1h-1-1l1-2-2-1z" class="O"></path><path d="M283 311v-1h1v-1c1 2 3 4 3 6-1 1-2 2-2 3h-1 0l1-1c0-3-1-4-2-6z" class="F"></path><path d="M291 316l-2-7 2 2c1 3 1 7 1 10h1v-1h1c-1 4 0 11-2 15 0-2 1-5 0-7l-1 2h-1-2-1v-1-1c2-2 3-4 4-6 0-2 1-5 0-6z" class="x"></path><path d="M291 316c1 1 0 4 0 6-1 2-2 4-4 6v1 1h1 2c-1 3-2 4-4 5-1 1-3 0-4 0s-1 0-2 1l-2-2h1c0-1 0-2 1-3l1 1v-3-2h0c2-1 4-3 5-4l2-2c1-2 2-3 3-5z" class="D"></path><path d="M286 323c1 1 1 2 0 3-1 2-3 4-5 6v-3-2h0c2-1 4-3 5-4z" class="AD"></path><path d="M598 735l2-1 1 2h1c1 0 2 1 3 2h1 3 1 0v1c3-1 6-2 8-2l-2 2h1c-3 2-6 4-6 7 0 5 0 11 3 14l3 3c2 1 2 1 4 1 1-1 3-2 4-3 0 1 1 2 0 3s-2 2-3 2v1h2v1h0v1l4-3 1 1h2c-1 1-5 4-5 5-1 1-2 1-3 1-4 1-7 1-11-1v-1c-4-1-8-5-11-9 0-1 0-2-1-3 0-1-1-2-1-3-1-2-2-5-3-8v-1c0-1 0-1-1-2l-1 1v4 1l-1-1h0l-1-2c-1-3 1-5 2-8 1 0 2-1 2-2h2c0 3 0 6 1 9 0 2 1 5 2 7-1-7-3-13-3-19z" class="V"></path><defs><linearGradient id="a" x1="607.393" y1="756.262" x2="620.434" y2="746.161" xlink:href="#B"><stop offset="0" stop-color="#817c7a"></stop><stop offset="1" stop-color="#9a9191"></stop></linearGradient></defs><path fill="url(#a)" d="M610 739c3-1 6-2 8-2l-2 2h1c-3 2-6 4-6 7 0 5 0 11 3 14l3 3c2 1 2 1 4 1 1-1 3-2 4-3 0 1 1 2 0 3s-2 2-3 2v1h2v1h-4l-1-1c-2 0-4-1-6-2-4-6-7-15-6-22 0-1 0-2-1-3h0c1 0 3-1 4-1h0z"></path><defs><linearGradient id="b" x1="876.466" y1="316.908" x2="869.295" y2="285.033" xlink:href="#B"><stop offset="0" stop-color="#222223"></stop><stop offset="1" stop-color="#585454"></stop></linearGradient></defs><path fill="url(#b)" d="M862 250h0c1 2 0 1 1 2 0 1 1 2 2 2h2 1l-2 2 1 3c2 1 3 2 5 2 2 5 3 10 4 15l1 5 1-3v-6l1-1-1-6 3-1 1 6h1 0v-5-8c0 1 0 1 1 1v-2h2v4c1 4 0 8 0 12 0 1-1 3-1 3 1 2 1 4 1 6v1c1 1 1 1 1 2v1l-1 1v1 3c-3 11-10 23-19 29-3 2-6 2-9 3h0c0-1 1-1 1-2 7-5 11-10 14-17 0-2 0-3 1-6v-5c-1-3-2-7-2-10 0-2-1-2-2-3-1-5-2-10-4-15l-2-4-3-9 1-1z"></path><path d="M883 257c0 1 0 1 1 1v-2h2v4c1 4 0 8 0 12 0 1-1 3-1 3 0 3 0 6-1 8l-1-17v-1-8z" class="I"></path><path d="M881 264l1 6c0 3 1 11-1 14-1 1-1 6-2 8v2h0-1l-1-1h0l-1 2c0 2-1 6-3 8 0-2 0-3 1-6v-5c-1-3-2-7-2-10 2 3 2 7 3 10 1 0 1 0 1-1 0-2-1-5-1-7h0c1-1 1-2 1-3-1-2 0-3 0-5l1 5 1-3v-6l1-1-1-6 3-1z" class="v"></path><path d="M877 281l1-3v-6l1-1c0 2 0 5 1 7v4 1c-1-1-1-1-1-2-1 1-1 2 0 3h-1c0-1 0-2-1-2v-1z" class="AF"></path><path d="M876 276l1 5v1 8l-1 1c0-2-1-5-1-7h0c1-1 1-2 1-3-1-2 0-3 0-5z" class="x"></path><path d="M881 264l1 6c0 3 1 11-1 14v-11h0c-1 1-1 3-1 5-1-2-1-5-1-7l-1-6 3-1z" class="D"></path><defs><linearGradient id="c" x1="877.765" y1="276.443" x2="861.579" y2="266.661" xlink:href="#B"><stop offset="0" stop-color="#44403c"></stop><stop offset="1" stop-color="#5f595c"></stop></linearGradient></defs><path fill="url(#c)" d="M862 250h0c1 2 0 1 1 2 0 1 1 2 2 2h2 1l-2 2 1 3c2 1 3 2 5 2 2 5 3 10 4 15 0 2-1 3 0 5 0 1 0 2-1 3h0c0 2 1 5 1 7 0 1 0 1-1 1-1-3-1-7-3-10 0-2-1-2-2-3-1-5-2-10-4-15l-2-4-3-9 1-1z"></path><defs><linearGradient id="d" x1="870.056" y1="276.208" x2="876.049" y2="264.588" xlink:href="#B"><stop offset="0" stop-color="#848281"></stop><stop offset="1" stop-color="#a5a2a1"></stop></linearGradient></defs><path fill="url(#d)" d="M867 259c2 1 3 2 5 2 2 5 3 10 4 15 0 2-1 3 0 5 0 1 0 2-1 3h0c-2-8-4-18-8-25z"></path><path d="M415 737c2 0 3 0 5 1l2 1c1 1 3 2 4 2h0c1 1 1 1 2 1 0 1 2 0 2 1l1 1 2 2c0 2 0 5 1 6-1 3-2 4-3 6l1 1-2 4h1c0 1 0 1-1 2l-2 4c1-1 2-1 4 0-2 1-3 3-5 4-5 4-13 5-19 3-7-1-9-6-12-11l-1-1c4-2 5-8 9-10 2-1 3-1 5-1 1 0 3 0 4 1l3 2c1 1 1 1 1 2l-2 2h0c1 0 2-1 3-1v-1c0-2-1-3-1-4v-1c2 3 2 6 4 8 1 0 1 0 2-1 3-3 3-7 4-11-1-3-2-4-3-6-3-3-6-4-9-6z" class="Y"></path><path d="M424 743c2 0 2 0 4 2 1 2 1 4 1 7-1-1-2-2-2-3-1-3-2-4-3-6z" class="H"></path><path d="M409 753c1 0 3 0 4 1l3 2-1 3c-1-1-2-2-3-2l-1-1c-1 0-2 0-4 1l-2 2v1c-1 0-2 1-2 1 0 2 1 5 3 6l1 2h1-1-1c-4-3-4-5-4-10 2-3 4-4 7-6z" class="B"></path><path d="M415 737c2 0 3 0 5 1l2 1c1 1 3 2 4 2h0c1 1 1 1 2 1 0 1 2 0 2 1l1 1 2 2c0 2 0 5 1 6-1 3-2 4-3 6l-2 4c-1-2 1-6 1-7 1-2 1-4 1-5-1-3-1-4-3-5-2-2-2-2-4-2-3-3-6-4-9-6z" class="G"></path><path d="M405 760v-1l2-2c2-1 3-1 4-1l1 1c-2 1-4 3-6 5h9c0 1 0 1 1 2h0 2c1 1 1 1 2 0h3l-2 1c-3 0-7 2-10 3-1 0-2-1-4-2-2-2-2-4-2-6z" class="n"></path><path d="M427 749c0 1 1 2 2 3-2 5-2 8-6 12h-3c-1 1-1 1-2 0h-2 0c-1-1-1-1-1-2h-9c2-2 4-4 6-5 1 0 2 1 3 2l1-3c1 1 1 1 1 2l-2 2h0c1 0 2-1 3-1v-1c0-2-1-3-1-4v-1c2 3 2 6 4 8 1 0 1 0 2-1 3-3 3-7 4-11zm4 9l1 1-2 4h1c0 1 0 1-1 2l-2 4c-4 2-8 3-12 4-1 1-9 1-11 1-2-1-4-2-5-4-2-3-1-3 0-6 0-2 0-3 2-5 0 5 0 7 4 10h1c1 2 3 2 4 2 7 0 13-4 17-8l1-1 2-4z" class="AD"></path><path d="M431 758l1 1-2 4h1c0 1 0 1-1 2l-2 4c-4 2-8 3-12 4 0-1 2-1 4-2 4-2 6-3 9-7l-1-1 1-1 2-4z" class="I"></path><path d="M395 764c4-2 5-8 9-10 2-1 3-1 5-1-3 2-5 3-7 6-2 2-2 3-2 5-1 3-2 3 0 6 1 2 3 3 5 4 2 0 10 0 11-1 4-1 8-2 12-4 1-1 2-1 4 0-2 1-3 3-5 4-5 4-13 5-19 3-7-1-9-6-12-11l-1-1z" class="L"></path><path d="M760 264l9 1 1 1c0 2 0 2-1 3-1 2-3 3-4 4 0 1-1 2-2 3v4l1 1 1-1c0 1 0 2 1 4 2 2 2 2 5 3-1 1-3 3-3 4h1l1 7-1-1h-1v-1h-1 0 0v2 1l1 1h-1-1 0l-1-1-2-2v-1c-3 1-5 1-8 1-2 1-5 2-7 3-3 1-5 3-7 5-1 5-2 11-2 16v1h0c-1-1-1-3-1-4-1-3 0-6 0-9-1-1-1-2-1-4h0v-2-1-1c2-7 5-13 8-19v-1c1-1 1-2 1-3l2-2-1-2c1-1 3-4 4-5l2-1h0l2-1c2-1 2-1 3-2l2-1z" class="X"></path><path d="M742 299c0-2 3-3 4-5l2-2c3-1 5-1 8 0v1c-6 1-10 2-14 6z" class="d"></path><path d="M761 273c-1 3-2 4-3 7l1 1c1 1 1 1 2 1l-1 1h-1v1h1c-2 1-4 1-6 2l-1-1v1l6 5v1l-1-1c-1 0-2 0-4-1h-2l2-1c-1 0-3-1-3-2-1-2 1-4 1-6l2-1c2-1 2-2 4-4l3-3zm-19 26c4-4 8-5 14-6 1 1 2 1 4 1 1 1 2 2 3 2-3 1-5 1-8 1-2 1-5 2-7 3-3 1-5 3-7 5-1 5-2 11-2 16v1h0c-1-1-1-3-1-4-1-3 0-6 0-9 1-3 2-6 4-10z" class="D"></path><path d="M760 294c1 1 2 2 3 2-3 1-5 1-8 1-2 1-5 2-7 3-3 1-5 3-7 5 0-1 1-3 2-4v-1l3-3c1 0 2-1 3-1h1c1-1 3-1 4-1h1 1c1 0 2 0 4-1z" class="I"></path><path d="M760 264l9 1 1 1c0 2 0 2-1 3-1 2-3 3-4 4 0 1-1 2-2 3-1 0-3 1-3 3 0 0 0 1 1 1v1h-2l-1-1c1-3 2-4 3-7l-3 3c-2 2-2 3-4 4l-2 1c0 2-2 4-1 6 0 1 2 2 3 2l-2 1-1-1c-1-1-2-2-2-4 0-5 2-10 5-14 0 0-1-1-1 0-2 0-4 3-5 5l-1-2c1-1 3-4 4-5l2-1h0l2-1c2-1 2-1 3-2l2-1z" class="Y"></path><path d="M761 273c1-1 2-1 4-1 1-1 2-2 3-4 0-1 0-2 2-2 0 2 0 2-1 3-1 2-3 3-4 4 0 1-1 2-2 3-1 0-3 1-3 3 0 0 0 1 1 1v1h-2l-1-1c1-3 2-4 3-7z" class="H"></path><path d="M754 271l6-3h2c2-1 2-1 4-1v1c-1 1-1 2-3 3h-1c-2 0-3 1-5 2-2 3-4 5-5 8 0 2-2 4-1 6 0 1 2 2 3 2l-2 1-1-1c-1-1-2-2-2-4 0-5 2-10 5-14z" class="F"></path><path d="M857 212h1c12 12 22 27 25 45v8 5h0-1l-1-6-3 1 1 6-1 1v6l-1 3-1-5c-1-5-2-10-4-15-2 0-3-1-5-2l-1-3 2-2h-1-2c-1 0-2-1-2-2-1-1 0 0-1-2h0l-1 1h0c-5-8-9-16-16-21-1-2-4-5-6-6h-1c-3-2-6-3-9-6h1 0c1 0 2 1 3 1 2 1 3 1 4 2s2 1 3 2c2 0 4 2 6 3-2-3-5-5-7-7l9 3c2 2 5 3 8 5l3 2h3l-3-2v-1h1c3 1 7 1 9 2l2 3v-1c-2-2-4-6-6-9l-2-2c-2-3-4-5-6-7z" class="x"></path><path d="M876 256v-1c0-1 0-2-1-3v-2c1 1 1 2 2 3h0 1c1 3 2 7 3 11l-3 1c-1-3-2-6-2-9z" class="u"></path><path d="M862 229l-3-2v-1h1c3 1 7 1 9 2l2 3c2 3 3 5 4 8 1 2 2 6 3 9l-2-4-1-1c-2-3-10-11-14-12h0l-2-2h3z" class="AB"></path><path d="M869 228l2 3c2 3 3 5 4 8h-1c-2-1-2-3-3-4l-3-3c1-1 1-2 1-4z" class="AF"></path><path d="M862 229l-3-2v-1h1c3 1 7 1 9 2 0 2 0 3-1 4-2-2-4-2-6-3z" class="F"></path><path d="M865 241c-4-7-12-10-16-16 3 0 5 1 7 2l3 2 2 2h0c6 6 15 14 17 22h-1 0c-1-1-1-2-2-3v2c1 1 1 2 1 3v1h-1c-1-4-2-7-6-8l1-1-5-6z" class="d"></path><defs><linearGradient id="e" x1="869.961" y1="271.239" x2="878.539" y2="259.761" xlink:href="#B"><stop offset="0" stop-color="#515556"></stop><stop offset="1" stop-color="#716968"></stop></linearGradient></defs><path fill="url(#e)" d="M870 254c0-2-1-4-3-5v-1h0 2v-1l-2-1c0-1-1-2-2-3v-2l5 6-1 1c4 1 5 4 6 8h1c0 3 1 6 2 9l1 6-1 1v6l-1 3-1-5c-1-5-2-10-4-15-2 0-3-1-5-2l-1-3 2-2c1 1 2 0 2 0z"></path><path d="M869 248c4 1 5 4 6 8h1l-1 1v1l-1-1c-1-2-2-4-3-5-1-2-1-3-2-4z" class="Y"></path><path d="M870 254l2 7c-2 0-3-1-5-2l-1-3 2-2c1 1 2 0 2 0z" class="o"></path><path d="M829 218h1 0c1 0 2 1 3 1 2 1 3 1 4 2s2 1 3 2c2 0 4 2 6 3-2-3-5-5-7-7l9 3c2 2 5 3 8 5-2-1-4-2-7-2 4 6 12 9 16 16v2c1 1 2 2 2 3l2 1v1h-2 0v1c2 1 3 3 3 5 0 0-1 1-2 0h-1-2c-1 0-2-1-2-2-1-1 0 0-1-2h0l-1 1h0c-5-8-9-16-16-21-1-2-4-5-6-6h-1c-3-2-6-3-9-6z" class="H"></path><path d="M852 234c3 0 7 6 9 8l1 2-1 1-3-3c-1-3-4-6-6-8z" class="I"></path><path d="M829 218h1 0c1 0 2 1 3 1 2 1 3 1 4 2s2 1 3 2c2 0 4 2 6 3 1 0 3 1 4 2v1c1 1 2 3 3 4h-1l-2-2v1c0 1 1 1 2 2 2 2 5 5 6 8s3 5 4 8l-1 1h0c-5-8-9-16-16-21-1-2-4-5-6-6h-1c-3-2-6-3-9-6z" class="Y"></path><defs><linearGradient id="f" x1="248.68" y1="358.974" x2="256.578" y2="364.63" xlink:href="#B"><stop offset="0" stop-color="#746b69"></stop><stop offset="1" stop-color="#88817d"></stop></linearGradient></defs><path fill="url(#f)" d="M274 330c0-1 2-2 2-4h1 0c1 1 1 1 1 2l3-1v2 3l-1-1c-1 1-1 2-1 3h-1l2 2c1-1 1-1 2-1s3 1 4 0c2-1 3-2 4-5h1l1-2c1 2 0 5 0 7v4l1 1h0l-3 3-2 1-1 1h-1c-1-1-1-1-2 0h-2c-1 0-2 0-3 1h0c-1 1-3 2-5 3l-6 3c-4 4-9 7-13 11-8 9-14 23-17 35h0l-2 6c-1 3-1 10 0 13v1c-1 1-1 4-1 5l-2-21-1-1c0-1 0-2-1-3h-1c0 2-1 2-2 3l-1-1 3-4c2-4 3-8 4-12s3-8 3-12c1-3 3-6 5-9v-2c1-1 1-2 2-3 0-2 1-3 2-4s1-2 2-4h0c3-2 5-4 8-5 0-2 1-2 1-4h-1v-2c1-1 3-1 4-2v-1c2-1 2-1 3-3h4v-1h-1v-1c3 0 4 1 7 1 1-1 1-1 1-2z"></path><path d="M280 341l1-1 2 1c-2 1-3 2-5 3-4 0-7 3-11 4-1-1-1-1-1-2 3-1 8-5 11-5h3z" class="I"></path><path d="M263 341h1c1 0 2 0 4-1l2 1-6 3h0l-3 3c-3 4-7 6-10 9-2 2-4 5-5 7-2 2-3 5-5 7l-5 12-2 2c1-4 3-8 3-12 1-3 3-6 5-9v-2c1-1 1-2 2-3 0-2 1-3 2-4s1-2 2-4h0c3-2 5-4 8-5l3-2c1-1 3-1 4-2z" class="L"></path><path d="M248 350h0v2c1 1 1 1 2 1-1 1-2 2-3 4-2 2-3 4-5 6v-2c1-1 1-2 2-3 0-2 1-3 2-4s1-2 2-4z" class="M"></path><path d="M241 370c3-11 13-21 23-26l-3 3c-3 4-7 6-10 9-2 2-4 5-5 7-2 2-3 5-5 7z" class="D"></path><path d="M274 330c0-1 2-2 2-4h1 0c1 1 1 1 1 2l3-1v2 3l-1-1c-1 1-1 2-1 3h-1l2 2c1-1 1-1 2-1s3 1 4 0c2-1 3-2 4-5h1l1-2c1 2 0 5 0 7v4l1 1h0l-3 3-2 1-1 1h-1c-1-1-1-1-2 0h-2c-1 0-2 0-3 1l-1-1v-1c2-1 3-2 5-3l-2-1-1 1-3-3c0-1-1 0-2 0l1 1c-1 1-5 2-6 2l-2-1c-2 1-3 1-4 1h-1c-1 1-3 1-4 2l-3 2c0-2 1-2 1-4h-1v-2c1-1 3-1 4-2v-1c2-1 2-1 3-3h4v-1h-1v-1c3 0 4 1 7 1 1-1 1-1 1-2z" class="Y"></path><path d="M263 341c2-1 4-2 6-2 2-1 4-1 6-1l1 1c-1 1-5 2-6 2l-2-1c-2 1-3 1-4 1h-1z" class="d"></path><path d="M278 328l3-1v2 3l-1-1c-1 1-1 2-1 3h-1c-1-1-3-1-3-3s1-2 3-3h0z" class="Q"></path><path d="M291 330l1-2c1 2 0 5 0 7v4l1 1h0l-3 3-2 1v-3l-3-3c-2-1-3-2-5-2 1-1 1-1 2-1s3 1 4 0c2-1 3-2 4-5h1z" class="I"></path><path d="M291 330l1-2c1 2 0 5 0 7v4l1 1h0l-3 3c0-2 0-3-1-5 0-1 0-2 1-4 1-1 1-2 1-4zm-25 16c0 1 0 1 1 2 4-1 7-4 11-4v1l1 1h0c-1 1-3 2-5 3l-6 3c-4 4-9 7-13 11-8 9-14 23-17 35h0l-2 6c-1 3-1 10 0 13v1c-1 1-1 4-1 5l-2-21-1-1c0-1 0-2-1-3h-1c0 2-1 2-2 3l-1-1 3-4c2-4 3-8 4-12l2-2v6c1-1 2-2 2-3 2-5 3-9 6-14 2-5 6-10 10-14 3-2 5-3 7-6 1-2 3-3 5-5z" class="H"></path><path d="M762 332c1-1 1-1 2-1-1 2-1 2 0 4 3 0 5 2 7 3l1 1c1 0 2 1 3 2 4 3 8 5 12 8l3 3v1h-2l2 3h-1l-2-1c0 1 0 2 1 3 2 2 3 5 5 7l5 11v4h0c0 2 2 6 2 8 1 2 1 3 2 5l-1-1v1 6c1 5 1 10 0 15l-1 2c0 1 0 2 1 3h0c-1 1-1 2 0 3-2 1-3 3-4 4v4l-1 1v-1c0-13 2-27-4-39-4-14-16-29-29-38-4-2-9-4-13-7l-1-1v-1-1c1-3 6-6 8-8 2 0 4-1 5-3h0z" class="I"></path><path d="M762 332c1-1 1-1 2-1-1 2-1 2 0 4-2 1-3 1-5 1h-1c-1 1-1 2-1 3l-4 1c0 1 1 2 2 2h-1 0v1l1 1h-1c-1 0-2-1-2-1h-3c1-3 6-6 8-8 2 0 4-1 5-3h0z" class="D"></path><path d="M760 347c2 0 4 2 6 3h1 2c1 1 3 2 4 3 4 3 10 7 13 11 0 1 1 3 1 4h1c0 1 0 2 1 3 1 2 1 2 1 5-6-8-12-16-20-22-3-2-8-4-10-7z" class="X"></path><path d="M764 335c3 0 5 2 7 3l1 1c1 0 2 1 3 2 4 3 8 5 12 8l3 3v1h-2l2 3h-1l-2-1c-2-2-4-3-6-5-6-5-13-9-21-12h-1-1l1-2c2 0 3 0 5-1z" class="L"></path><path d="M764 335c3 0 5 2 7 3l1 1c1 0 2 1 3 2-4-1-7-3-10-4-2 0-4 0-5 1h-1-1l1-2c2 0 3 0 5-1z" class="AD"></path><defs><linearGradient id="g" x1="771.764" y1="380.315" x2="786.216" y2="371.817" xlink:href="#B"><stop offset="0" stop-color="#afa6a2"></stop><stop offset="1" stop-color="#cfcac7"></stop></linearGradient></defs><path fill="url(#g)" d="M749 345c3-1 5-1 7 0l4 2c2 3 7 5 10 7 8 6 14 14 20 22 4 9 8 19 8 30 1 3 0 6 0 9l-1 10v1 4l-1 1v-1c0-13 2-27-4-39-4-14-16-29-29-38-4-2-9-4-13-7l-1-1z"></path><defs><linearGradient id="h" x1="188.09" y1="292.746" x2="181.619" y2="322.461" xlink:href="#B"><stop offset="0" stop-color="#4a4544"></stop><stop offset="1" stop-color="#635f5f"></stop></linearGradient></defs><path fill="url(#h)" d="M163 251v1 2c-1 1-1 2-1 3h1v-1l1-1c2-1 3 0 5 1l1 1c-1 3-4 7-5 11 1 1 1 2 1 4v3c-1 4-2 11 0 15 0-4 0-9 1-13 0-1 1-2 2-2v3c-2 7-1 17 3 23 3 5 6 8 12 10 4 1 8 0 12-1 2-2 5-3 7-6 1-1 1-1 3-2l1 1 1 3h1l1-1s1 0 2-1c-1 1-1 2-2 3l1 1c-3 7-7 11-14 14-6 2-15 2-21 0v-1c-1-1-3-2-4-3l-6-6c-2-2-4-5-5-8l-1-4c-2-3-4-7-5-10l-1-4h-1v-7c-1-7 0-13 1-20v4l1-1h0l1-1c0 1 1 2 2 2 0-4 2-8 5-12z"></path><path d="M158 279h0c1 2 2 5 3 7l1-7c2 6 0 12 1 19v1c1 3 2 7 3 11h0v2c-2-2-4-5-5-8l-1-4c0-2 0-4-1-6v-6c-1-3-1-6-1-9z" class="B"></path><path d="M158 279h0c1 2 2 5 3 7v14 4l-1-4c0-2 0-4-1-6v-6c-1-3-1-6-1-9z" class="G"></path><path d="M163 256l1-1c2-1 3 0 5 1l1 1c-1 3-4 7-5 11l-1 4-2 7-1 7c-1-2-2-5-3-7 1-4 1-7 1-11 1-4 2-8 4-11v-1z" class="D"></path><path d="M163 256l1-1c2-1 3 0 5 1l1 1c-1 3-4 7-5 11l-1 4h-1c0-3-1-5-1-7 1-3 1-6 1-9z" class="Q"></path><defs><linearGradient id="i" x1="186.708" y1="295.487" x2="182.363" y2="312.224" xlink:href="#B"><stop offset="0" stop-color="#958e8c"></stop><stop offset="1" stop-color="#b5b2b1"></stop></linearGradient></defs><path fill="url(#i)" d="M166 290c0-4 0-9 1-13 0-1 1-2 2-2v3c-2 7-1 17 3 23 3 5 6 8 12 10 4 1 8 0 12-1 2-2 5-3 7-6 1-1 1-1 3-2l1 1-1 2c-3 4-8 8-13 9s-12 0-17-4c-7-4-9-12-10-20z"></path><path d="M163 251v1 2c-1 1-1 2-1 3h1c-2 3-3 7-4 11 0 4 0 7-1 11h0c0 3 0 6 1 9v6c1 2 1 4 1 6-2-3-4-7-5-10l-1-4h-1v-7c-1-7 0-13 1-20v4l1-1h0l1-1c0 1 1 2 2 2 0-4 2-8 5-12z" class="f"></path><path d="M154 279h1c1-4 2-7 2-10l1 3v7c0 3 0 6 1 9v6c-1-3-2-7-2-11-1 0-1 0-2-1h-1v-3z" class="AF"></path><path d="M154 259v4l1-1h0l1-1c0 1 1 2 2 2-1 3-1 4-2 6h-1c0 1 0 2-1 2-1 3 0 6 0 8v3h1c1 1 1 1 2 1 0 4 1 8 2 11 1 2 1 4 1 6-2-3-4-7-5-10l-1-4h-1v-7c-1-7 0-13 1-20z" class="F"></path><path d="M155 262l1-1c0 1 1 2 2 2-1 3-1 4-2 6h-1c0 1 0 2-1 2 0-3 0-6 1-9z" class="V"></path><defs><linearGradient id="j" x1="200.568" y1="278.236" x2="191.648" y2="299.743" xlink:href="#B"><stop offset="0" stop-color="#010100"></stop><stop offset="1" stop-color="#2d2b2c"></stop></linearGradient></defs><path fill="url(#j)" d="M172 276c0-1 1-2 1-3h5c4-1 10-1 14 0h0c3 0 5 1 7 2h1c5 2 11 5 13 11s1 11 0 16c-1 2-1 4-2 6l-1-1c1-1 1-2 2-3-1 1-2 1-2 1l-1 1h-1l-1-3-1-1c-2 1-2 1-3 2-2 3-5 4-7 6-4 1-8 2-12 1-6-2-9-5-12-10-4-6-5-16-3-23l1 1h-1v4l1-1h1v-3 1c1-1 1-2 1-4z"></path><path d="M191 282v-2h2 0c0 1 1 1 1 2 1 1 1 2 1 3l-2 1h0v1c-2 0-2 0-4-1l1-1 1-3z" class="S"></path><path d="M191 282h1v4h1 0v1c-2 0-2 0-4-1l1-1 1-3z" class="y"></path><path d="M200 275c5 2 11 5 13 11s1 11 0 16l-7-1 1-1c0-2 1-3 2-5h0c1-3 0-5 0-7-1-5-4-8-8-10l-1-3z" class="G"></path><path d="M193 280h3c3 4 7 6 9 11 1 1 1 3 1 4l-1 2c-1 0-2 1-2 3h-1c0 2-1 2-3 3h-1l1 1h4c-2 3-5 4-7 6l-1-1c-3-2-5-8-7-12 0-2-1-3-2-4h0l-2-2h0v-1l-2-3c1-1 2-1 3-1h1c0-1 0-1 1-2l2 2c2 1 2 1 4 1v-1h0l2-1c0-1 0-2-1-3 0-1-1-1-1-2h0z" class="B"></path><path d="M205 291c1 1 1 3 1 4l-1 2-1-2h0v-1c-1-1-1-1-2-3h3z" class="F"></path><path d="M204 294v1h0l1 2c-1 0-2 1-2 3h-1l-2-4 4-2z" class="O"></path><path d="M186 286c0-1 0-1 1-2l2 2c2 1 2 1 4 1v-1l1 1h2 2 0l2 2 1 1h-1c-1 1-2 3-3 4h0-1c-1-2-3-3-6-3h-1l-3 2h0l-2-2h0v-1l-2-3c1-1 2-1 3-1h1z" class="f"></path><path d="M182 287c1-1 2-1 3-1h1v1c1 0 2 1 4 1-1 1-1 2-1 3l-3 2h0l-2-2h0v-1l-2-3z" class="p"></path><path d="M189 291h1c3 0 5 1 6 3h1 0l3 2 2 4c0 2-1 2-3 3h-1l1 1h4c-2 3-5 4-7 6l-1-1c-3-2-5-8-7-12 0-2-1-3-2-4l3-2z" class="r"></path><path d="M190 291c3 0 5 1 6 3h1c-1 1-2 3-3 3l-1-1-4-4 1-1z" class="S"></path><path d="M193 296l1 1 4 6 1 1h4c-2 3-5 4-7 6l-1-1c0-1 1-1 1-2v-2c0-1 0-1-1-1 0-1 0-2-1-2v-1c-1-2-1-3-1-5z" class="F"></path><path d="M197 294h0l3 2 2 4c0 2-1 2-3 3h-1l-4-6c1 0 2-2 3-3z" class="y"></path><path d="M172 276c0-1 1-2 1-3h5c4-1 10-1 14 0h0c3 0 5 1 7 2h1l1 3c-4-2-7-2-11-1-2 1-5 4-6 6 1 1 1 1 1 3-1 0-2 0-3 1l2 3v1h0l2 2h0c1 1 2 2 2 4 2 4 4 10 7 12l1 1c-4 1-8 2-12 1-6-2-9-5-12-10-4-6-5-16-3-23l1 1h-1v4l1-1h1v-3 1c1-1 1-2 1-4z" class="AE"></path><path d="M186 295l2 7-1 1-2-2v-1-4s0-1 1-1z" class="G"></path><path d="M175 300l1-2c0-2 0-6 2-8h2 1s2 1 3 1v-1 1h0l2 2v2c-1 0-1 1-1 1v4 1l2 2 1-1v1c-3 1-8 1-11 1l-2-4z" class="F"></path><path d="M184 291l2 2v2c-1 0-1 1-1 1v4 1c-2 0-2 0-3 1l-1-1c0-2 1-4 1-7 1-1 1-2 2-3z" class="v"></path><path d="M172 276c0-1 1-2 1-3h5c4-1 10-1 14 0h0c3 0 5 1 7 2h1l1 3c-4-2-7-2-11-1-2 1-5 4-6 6 1 1 1 1 1 3-1 0-2 0-3 1l2 3v1c-1 0-3-1-3-1h-1-2c-2 2-2 6-2 8l-1 2h-1c-3-7-3-13-3-20 1-1 1-2 1-4z" class="D"></path><path d="M172 276c0-1 1-2 1-3h5 1l-1 1c-1 0-2 1-2 2h-3-1z" class="f"></path><path d="M177 284c5 0 8-6 13-7-2 1-5 4-6 6 1 1 1 1 1 3-1 0-2 0-3 1l-1 1c-2 0-2-1-3-2l-2 1c0-2-1-2-2-3l1-1 2 1z" class="S"></path><path d="M176 276c1 0 2-1 3 0v2h1l1-1 1 1-5 6-2-1v-2c-1 0-2-1-2-1v-3-1h3z" class="B"></path><path d="M172 276h1v1 3s1 1 2 1v2l-1 1c1 1 2 1 2 3l2-1c1 1 1 2 3 2l1-1 2 3v1c-1 0-3-1-3-1h-1-2c-2 2-2 6-2 8l-1 2h-1c-3-7-3-13-3-20 1-1 1-2 1-4z" class="E"></path><path d="M151 120l294 1c-4 0-6 1-9 3l-4 2-1 1h0l-1 1-1 1c-1 0-1 1-2 1-1 1-6 0-8 0h-22-13l-124 1-80-1c-1-1-3-1-4-1 2 0 3-1 4 0h1v-1h-10c-4 0-10 1-13-1h0l-7-7z" class="AS"></path><path d="M181 128h36 55 103l31-1c7 0 15 1 21 0l2-1c1 0 1 0 2 1l-1 1-1 1c-1 0-1 1-2 1-1 1-6 0-8 0h-22-13l-124 1-80-1c-1-1-3-1-4-1 2 0 3-1 4 0h1v-1z" class="AU"></path><path d="M886 120c1 0 1 0 2 1l-10 7 2 2-2 2-2 2h-1c-5 0-11 0-16 1h-7-2l2-2h1 4c-3-3-9 0-12-2l-1-1-209 1h-5l-5-4h0l-11-6 272-1z" class="AP"></path><path d="M886 120c1 0 1 0 2 1l-10 7 2 2-2 2-2 2h-1c-5 0-11 0-16 1h-7-2l2-2h1 4c-3-3-9 0-12-2l-1-1-209 1h-5l-5-4 1-1c1 0 2 0 3 1h2c2 1 6 1 8 1l14-1h30 99 62c10 1 20 1 29 0 4 0 5-2 8-4 2-1 3-2 5-3z" class="AU"></path><path d="M844 130h8 17c2 0 5 1 7 0 0 0 2-1 2-2l2 2-2 2-2 2h-1l-1-2c-2-1-3-1-4-1h-2c-8-1-16 0-23 0l-1-1z" class="R"></path><path d="M845 131c7 0 15-1 23 0h2c1 0 2 0 4 1l1 2c-5 0-11 0-16 1h-7-2l2-2h1 4c-3-3-9 0-12-2z" class="s"></path><path d="M635 131l209-1 1 1c3 2 9-1 12 2h-4-1l-2 2c-1 0-3 0-4 1h-9v-1l-18 3h-2c-5 0-12 1-16 3l-2 1c-7 2-13 6-19 10-1 0-2 1-3 2l-4 1c-1 1-2 1-3 2-5 4-11 7-15 11-3 3-5 6-8 9-1 2-3 3-4 5v1c-2 2-3 4-4 7h0v-2l-1-1v1c0-3-2-4-3-7l-10-13-10-10h0c-1-2-4-4-6-6h0v-3l1-1-1-2 1-2c0-2 2-3 3-3v-1h-5-3-2c-2 1-2 1-4 0s-3-1-5-1h-1l-12-1c-4 0-6-1-9 0-3-1-7-1-11-1-2 0-6 0-7 1h0-9c-6 0-11 1-17 2-4 1-7 3-11 6 1-3 2-4 4-6 0-4 1-5 4-8h1l1-1h2 1 5z" class="K"></path><path d="M630 131h5-6v1l-3 3c-2 1-4 3-5 5 0-4 1-5 4-8h1l1-1h2 1z" class="h"></path><path d="M693 139c7-1 16-1 23 0l-2 2h-1v-1h-5-3-2c-2 1-2 1-4 0s-3-1-5-1h-1z" class="Z"></path><path d="M808 137c2 0 4 0 6-1s5-1 7-1c3-1 11-2 14-1 1 0 4 1 5 0 2-1 4 0 5 0 3-1 5-1 7-1l-2 2c-1 0-3 0-4 1h-9v-1l-18 3c-1-1-1-1-2-1-3 1-6 1-9 0z" class="J"></path><path d="M628 140v-1h1l1-1h4c1-1 1-1 2-1s2-1 3-1h2c1 0 2 0 3-1 2-1 5-1 8-1l1 1c2 0 6 1 8 2-2 0-6 0-7 1h0-9c-6 0-11 1-17 2z" class="g"></path><path d="M808 137c3 1 6 1 9 0 1 0 1 0 2 1h-2c-5 0-12 1-16 3l-2 1c-7 2-13 6-19 10-1 0-2 1-3 2v-1c0-2 1-3 2-5v-4c0-2-1-3-2-3s-2 0-3 1c-1-2-3-3-4-3v-1c5 1 11 0 16 0l22-1z" class="a"></path><path d="M779 148h1c0-2 0-3 1-5 3 0 5-1 7-1h5c3-1 5-1 8-1l-2 1c-7 2-13 6-19 10-1 0-2 1-3 2v-1c0-2 1-3 2-5z" class="AQ"></path><path d="M718 138l-3-1c3-2 6-3 9-3v1l2 1s1 0 2-1c1 1 2 1 3 1 2-1 7 0 8-1h9 3c3-1 8-1 12-1 1 1 2 1 4 1l1-1 1 2h-1v3h-1c-1 0-3 2-4 2-1-1-1-2-2-3-4 1-8 1-11 4l-1-1h0c-1 1-1 0-2 1 0 0-1 0-2-1-1 1-1 2-2 3v-1l-1-1h-2v-1h-1c-1 1-2 1-3 1h-1l-4-2h-4c-3 0-6-1-9-2z" class="l"></path><path d="M731 140s0-1 1-1c1-1 2-1 3-1 4 1 8 0 12 0 2 0 3 1 5 1 3-1 7-1 10-2 2-1 3-1 5-1v1 2c-1 0-3 2-4 2-1-1-1-2-2-3-4 1-8 1-11 4l-1-1h0c-1 1-1 0-2 1 0 0-1 0-2-1-1 1-1 2-2 3v-1l-1-1h-2v-1h-1c-1 1-2 1-3 1h-1l-4-2z" class="a"></path><path d="M750 142c3-3 7-3 11-4 1 1 1 2 2 3 1 0 3-2 4-2h1 2c1 0 3 1 4 3 1-1 2-1 3-1s2 1 2 3v4c-1 2-2 3-2 5v1l-4 1c-1 1-2 1-3 2-5 4-11 7-15 11-3 3-5 6-8 9-1-1 0-1-1-2h0v-1c-1 0-2-1-3-2h0v-3-6-2c1-7 2-14 6-20l1 1z" class="h"></path><path d="M752 155h1l3-3 2-2h3c0 1-1 1-2 2 0 1-1 2-1 2h-1l-1 2c0 1-2 1-2 2h-1c0-1 0-2-1-2v-1z" class="a"></path><path d="M750 151c2-2 3-3 6-5v1l3-1c2 1 4 1 6 1l3 1 1-1c1 2-1 3 1 4h2c-1 1-2 2-3 2s-1-1-2-1l1-1c-1-2-2-2-4-2-1 0-2 0-3-1-2 0-3 0-5 1s-2 3-3 4l-1-1h-1l-1-1z" class="g"></path><path d="M767 139h1 2c1 0 3 1 4 3s2 4 1 6l-1 1c-1 1-2 1-2 2h-2c-2-1 0-2-1-4l-1 1-3-1c1-2 2-2 1-4l-3-2h0c1 0 3-2 4-2z" class="AL"></path><defs><linearGradient id="k" x1="761.19" y1="139.836" x2="748.747" y2="156.783" xlink:href="#B"><stop offset="0" stop-color="#170807"></stop><stop offset="1" stop-color="#320c0b"></stop></linearGradient></defs><path fill="url(#k)" d="M750 142c3-3 7-3 11-4 1 1 1 2 2 3h0l3 2c1 2 0 2-1 4-2 0-4 0-6-1l-3 1v-1c-3 2-4 3-6 5-1 2-1 3-2 5l-1 1v-4c0-1 0-2 1-3 0-1 1-3 1-4v-1c1-1 1-2 1-3h0z"></path><path d="M749 141l1 1h0c0 1 0 2-1 3v1c0 1-1 3-1 4-1 1-1 2-1 3v4l1-1c1-2 1-3 2-5l1 1h1l1 1-1 2v1c0 1-1 2-1 3v4h0l1 2c-2 1-6 6-9 7v-3-6-2c1-7 2-14 6-20z" class="T"></path><path d="M751 152h1l1 1-1 2v1c0 1-1 2-1 3v4h0l1 2c-2 1-6 6-9 7v-3c1 0 1 1 3 0 3-5 2-11 5-16v-1z" class="R"></path><path d="M774 142c1-1 2-1 3-1s2 1 2 3v4c-1 2-2 3-2 5v1l-4 1c-1 1-2 1-3 2-5 4-11 7-15 11-3 3-5 6-8 9-1-1 0-1-1-2h0v-1c-1 0-2-1-3-2h0c3-1 7-6 9-7l-1-2c2-2 5-4 7-5l9-6c1 0 1 1 2 1s2-1 3-2c0-1 1-1 2-2l1-1c1-2 0-4-1-6z" class="AS"></path><path d="M772 151c0-1 1-1 2-2-5 7-15 11-22 16l-1-2c2-2 5-4 7-5l9-6c1 0 1 1 2 1s2-1 3-2z" class="J"></path><path d="M718 138c3 1 6 2 9 2h4l4 2h1c1 0 2 0 3-1h1v1h2l1 1v1c1-1 1-2 2-3 1 1 2 1 2 1 1-1 1 0 2-1h0c-4 6-5 13-6 20v2 6 3h0c1 1 2 2 3 2v1h0c1 1 0 1 1 2-1 2-3 3-4 5v1c-2 2-3 4-4 7h0v-2l-1-1v1c0-3-2-4-3-7l-10-13-10-10h0c-1-2-4-4-6-6h0v-3l1-1-1-2 1-2c0-2 2-3 3-3h1l2-2 2 1v-2z" class="g"></path><path d="M728 146l5 2c0 1 1 2 1 3h0c-2 0-3-1-5-1h0c0 1 2 2 2 4h-1 0l-3-3c-1-1-1-2-2-3s0-1 0-1l3-1z" class="s"></path><path d="M738 149c0 2 1 3 1 4v12 3l-1 3-1 1-1-2v-2l-1-2v1l-2-1 1-2 1-1v1h1v-3-5l1-1c1-1 1-4 1-6z" class="J"></path><path d="M738 149c0 2 1 3 1 4l-1 7s-1 0-2 1v-5l1-1c1-1 1-4 1-6z" class="AC"></path><path d="M722 158l3-4c2 0 2 0 4 1l-1 1-1-1c-1 2 0 4 0 5l1 1c0-2-1-2 0-4h1c1 3 2 4 0 7l2 2-1 2h-1l-6-6 1-2-2-2z" class="AU"></path><path d="M718 155l4 3 2 2-1 2 6 6-1 1c-2-1-2-1-3-1l-10-10c1-1 1-1 1-2l2-1z" class="Ab"></path><path d="M718 155l4 3 2 2-1 2-6-4c0-1 0-1 1-3z" class="AY"></path><defs><linearGradient id="l" x1="728.554" y1="140.244" x2="729.039" y2="150.774" xlink:href="#B"><stop offset="0" stop-color="#150606"></stop><stop offset="1" stop-color="#371110"></stop></linearGradient></defs><path fill="url(#l)" d="M718 138c3 1 6 2 9 2h4l4 2 2 3c0 1 1 3 1 4 0 2 0 5-1 6l-1 1c0-2-1-3-2-5 0-1-1-2-1-3l-5-2h-2-4c-1-3-2-4-4-6v-2z"></path><path d="M718 138c3 1 6 2 9 2h-4l-1 1c0 2 1 2 2 3l4 1h1c1 0 2 0 3 1s1 1 1 2l-5-2h-2-4c-1-3-2-4-4-6v-2z" class="U"></path><path d="M735 142h1c1 0 2 0 3-1h1v1h2l1 1v1c1-1 1-2 2-3 1 1 2 1 2 1 1-1 1 0 2-1h0c-4 6-5 13-6 20v2h-1l1-2-1-1-1 1-1 1c-1 1-1 2-1 3v-12c0-1-1-2-1-4 0-1-1-3-1-4l-2-3z" class="AQ"></path><path d="M737 145h0c1-1 1-1 2-1l1-1v1c2 5 3 11 3 17v2h-1l1-2-1-1-1 1-1 1c-1 1-1 2-1 3v-12c0-1-1-2-1-4 0-1-1-3-1-4z" class="AU"></path><path d="M716 139l2 1c2 2 3 3 4 6h4 2l-3 1s-1 0 0 1 1 2 2 3l3 3-1 1c-2-1-2-1-4-1l-3 4-4-3-2 1c0 1 0 1-1 2h0c-1-2-4-4-6-6h0v-3l1-1-1-2 1-2c0-2 2-3 3-3h1l2-2z" class="i"></path><path d="M722 146h4 2l-3 1s-1 0 0 1c-2 2-4 4-5 6-1-1-2-1-3-2l1-2c1-1 2-2 3-4h1z" class="N"></path><path d="M710 148v-1c2 4 6 5 8 8l-2 1c0 1 0 1-1 2h0c-1-2-4-4-6-6h0v-3l1-1z" class="AW"></path><path d="M716 139l2 1c2 2 3 3 4 6h-1c-1 2-2 3-3 4-3 0-4-2-5-4v-1c-1 0-2 1-2 2h-1v1l-1-2 1-2c0-2 2-3 3-3h1l2-2z" class="w"></path><path d="M713 146l1-1h3c1 0 1 1 2 1h2c-1 2-2 3-3 4-3 0-4-2-5-4z" class="AH"></path><path d="M739 165c0-1 0-2 1-3l1-1 1-1 1 1-1 2h1v6 3h0c1 1 2 2 3 2v1h0c1 1 0 1 1 2-1 2-3 3-4 5v1c-2 2-3 4-4 7h0v-2l-1-1v1c0-3-2-4-3-7l-10-13c1 0 1 0 3 1l1-1h1l1-2 5 4 1 2 1-1 1-3v-3z" class="AT"></path><path d="M731 166l5 4 1 2 1-1 1-3v6c1 1 1 1 2 1h1v1h-1-2 0c-2-2-5-4-7-5h-1l-3-2 1-1h1l1-2z" class="AW"></path><path d="M739 165c0-1 0-2 1-3l1-1 1-1 1 1-1 2c0 4 1 8 0 12h-1c-1 0-1 0-2-1v-6-3z" class="AY"></path><path d="M725 168c1 0 1 0 3 1l3 2 7 8c1 1 2 3 3 4l2-1v1c-2 2-3 4-4 7h0v-2l-1-1v1c0-3-2-4-3-7l-10-13z" class="o"></path><defs><linearGradient id="m" x1="700.493" y1="365.162" x2="785.233" y2="351.507" xlink:href="#B"><stop offset="0" stop-color="#0c0909"></stop><stop offset="1" stop-color="#28292a"></stop></linearGradient></defs><path fill="url(#m)" d="M741 305c2-2 4-4 7-5-1 2-2 4-1 5-1 2-2 4-3 5 1 1 2 2 2 3s-1 1 1 2v-1c1 4 2 6 5 9h1c1 3 3 4 6 4l-1 1c1 1 3 1 4 2l-1 2h1 0c-1 2-3 3-5 3-2 2-7 5-8 8v1 1l1 1c4 3 9 5 13 7 13 9 25 24 29 38 6 12 4 26 4 39l-1 2h0l-2 8-1-1c1-2 0-5 0-7v4h-1v-7c-1 1-1 2-1 3 0-2-1-3-1-5 0 4 1 12 0 14h-1v-6c-1-2-1-4-1-7-1-5-1-8-3-13 0-1-2-4-2-5-1-2-1-3-2-4v-3c-1-2-1-3-1-5-1-2-4-6-6-7s-4-3-5-4h-1l-2-2c-6-5-17-7-24-8-8 0-16 0-22 6-1-1 0-1-1-1h-1c-1-1-1-2-1-3h-4v-1c2-1 3-3 4-5h-6-3 0-2v4l3 2c-2 1-3 0-5 0l-3-3h0v-1l-2-1 1-1c-1 0 0 0-1-1 0 0-1-1-1-2-6-11-2-22 1-33l2-3h0c1 0 2-1 3 0h1l1-1c-1-2 0-3 0-4 2-2 6-3 8-4 1-2 2-4 2-5h1l1-3h0c0-1 0-2 1-3s1-1 3-2c1 0 2 0 3 1 0 2 2 5 4 6 2 4 7 7 10 10h1c0-2-2-3-3-5l1-2h0l1-4c0 1 0 3 1 4h0v-1c0-5 1-11 2-16z"></path><path d="M715 367c1-3 3-2 6-3 1 0 3-1 4-1h5c-1 1-3 1-4 1l-1 1h1c-2 0-3 0-4 1h-2c-1 0-1 1-2 1h-3z" class="b"></path><path d="M715 338h0c2 1 3 1 4 2l1-1h2v1l3 7c-4-4-6-7-12-8h-1-2l2-1h3z" class="R"></path><path d="M714 360c-1 1-2 1-3 1-1-3-1-6 0-9 1-2 1-2 3-3 0 3 0 4-1 6v4l1 1z" class="e"></path><path d="M701 368l1-1c0-2 0-6 1-8 1-1 2-5 3-6v1c-1 6-1 12 0 18h-1c-1 0-1 1-1 1-1-1-2-3-3-5z" class="AK"></path><path d="M715 320h1v2l1 1v2h1c1 1 1 2 1 3h0l3 5-3 1h1v1c1 2 0 0 1 1s1 2 1 4v-1l-6-6c-2-1-4-2-4-4v-1-2l1-1c1-2 2-4 2-5z" class="O"></path><path d="M715 320h1v2l1 1v2h1c1 1 1 2 1 3h0-1v-2h-1l-1 1c-1 1-1 2-2 3l-1-1-1-1v-2l1-1c1-2 2-4 2-5z" class="e"></path><path d="M722 324c4 0 9 4 12 6 1 1 2 2 4 2 1 2 3 4 4 4l3 6c-1-1-2-2-4-3v1c-4-2-7-5-10-8-2 0-7-7-9-8z" class="X"></path><path d="M731 332c2 0 7 2 9 3 1 1 1 2 2 3l-1 1v1c-4-2-7-5-10-8z" class="L"></path><path d="M705 329c2-2 6-3 8-4l-1 1v2 1c0 2 2 3 4 4l6 6h-2l-1 1c-1-1-2-1-4-2h0l-3-1h-5 0-2c-1 1-1 1-2 1h-1c1-1 2-1 3-3l-2-1h0 1l1-1c-1-2 0-3 0-4z" class="J"></path><path d="M716 333l6 6h-2c-2-2-5-3-7-4-3-1-5-1-8 0l-2-1h0 1c3-1 5-1 8-1h4z" class="h"></path><path d="M705 329c2-2 6-3 8-4l-1 1v2 1c0 2 2 3 4 4h-4c-3 0-5 0-8 1l1-1c-1-2 0-3 0-4z" class="a"></path><path d="M705 329c2-2 6-3 8-4l-1 1c-2 1-3 2-5 4 1 1 1 2 2 2h2l1 1c-3 0-5 0-8 1l1-1c-1-2 0-3 0-4z" class="w"></path><defs><linearGradient id="n" x1="722.474" y1="362.144" x2="732.526" y2="373.356" xlink:href="#B"><stop offset="0" stop-color="#242428"></stop><stop offset="1" stop-color="#555453"></stop></linearGradient></defs><path fill="url(#n)" d="M722 366v1c1 0 2 0 3-1h5c4 0 10-1 14 1 3 1 5 2 8 3-1 1-1 2-1 2-3 0-5-1-7 0-9-3-21-5-30-3h-2l-1-1c1-1 1-1 2-1h1 1 3c1 0 1-1 2-1h2z"></path><defs><linearGradient id="o" x1="719.754" y1="313.68" x2="726.356" y2="322.703" xlink:href="#B"><stop offset="0" stop-color="#494545"></stop><stop offset="1" stop-color="#706a69"></stop></linearGradient></defs><path fill="url(#o)" d="M721 312c1 0 2 0 3 1 0 2 2 5 4 6 2 4 7 7 10 10v3c-2 0-3-1-4-2-3-2-8-6-12-6-2-2-4-4-5-7 0-1 0-2 1-3s1-1 3-2z"></path><path d="M717 317c0-1 0-2 1-3s1-1 3-2l-2 2h1v2c-1 1-2 1-3 1z" class="r"></path><defs><linearGradient id="p" x1="720.555" y1="379.681" x2="744.742" y2="372.35" xlink:href="#B"><stop offset="0" stop-color="#1a1a1b"></stop><stop offset="1" stop-color="#3a393b"></stop></linearGradient></defs><path fill="url(#p)" d="M716 373h2l1 1 1 1 1-1v-2c2 0 4 0 5 1 10-1 19 2 28 6 4 1 7 2 10 5 1 0 1 0 1 1h0c-6-5-17-7-24-8-8 0-16 0-22 6-1-1 0-1-1-1h-1c-1-1-1-2-1-3h-4v-1c2-1 3-3 4-5z"></path><path d="M716 373h2l1 1 1 1 1-1v-2c2 0 4 0 5 1-2 1-4 2-6 4-1 1-1 2-2 2l-1-1c0 1-1 1-1 1h-4v-1c2-1 3-3 4-5z" class="b"></path><defs><linearGradient id="q" x1="759.606" y1="354.132" x2="756.166" y2="361.766" xlink:href="#B"><stop offset="0" stop-color="#313132"></stop><stop offset="1" stop-color="#4d4c4d"></stop></linearGradient></defs><path fill="url(#q)" d="M738 350c1 1 2 1 3 0l1-1h1c1 0 1-1 2-2 1 0 2 0 3 1 10 6 20 11 27 21h0c-2-1-4-2-5-4 0 2 2 3 3 4l-1 1 2 1v1c0 1 1 2 2 4-3-3-5-7-8-8l-9-6c-1-1-2-2-3-2-4-1-8-5-11-7h0l-2-2c-1 0-2 0-3 1h-2v-2z"></path><path d="M700 334c1 0 2-1 3 0h0l2 1c-1 2-2 2-3 3h1c1 0 1 0 2-1h2 0 5l3 1h-3l-2 1h2c-4 1-8 2-11 6-3 3-3 9-2 13v4l2 6c1 2 2 4 3 5 0 0 0-1 1-1h1l1 1h-2v4l3 2c-2 1-3 0-5 0l-3-3h0v-1l-2-1 1-1c-1 0 0 0-1-1 0 0-1-1-1-2-6-11-2-22 1-33l2-3h0z" class="i"></path><path d="M700 375l5 2 3 2c-2 1-3 0-5 0l-3-3h0v-1z" class="m"></path><path d="M700 334c1 0 2-1 3 0h0l2 1c-1 2-2 2-3 3s-1 1-2 1v-5h0z" class="R"></path><defs><linearGradient id="r" x1="716.22" y1="348.767" x2="727.793" y2="360.22" xlink:href="#B"><stop offset="0" stop-color="#242426"></stop><stop offset="1" stop-color="#484748"></stop></linearGradient></defs><path fill="url(#r)" d="M714 349c2 0 10 0 12-1 1-1 0-1 1-1h2c0 1 1 2 2 3h2c1-1 1-2 2-3 2 1 2 1 3 3v2h2c-2 1-4 3-5 5h0v2h2v1 1l-7-1h-4c-2 0-4-1-6 0h-6l-1-1v-4c1-2 1-3 1-6z"></path><path d="M733 350c1-1 1-2 2-3 2 1 2 1 3 3v2h2c-2 1-4 3-5 5h0v2h2v1 1l-7-1c0-3 2-6 3-9v-1z" class="r"></path><path d="M733 351h1c0 2-1 4-2 6v2h0 1c0-1 1-2 2-2v2h2v1 1l-7-1c0-3 2-6 3-9z" class="P"></path><defs><linearGradient id="s" x1="734.059" y1="311.236" x2="754.478" y2="335.308" xlink:href="#B"><stop offset="0" stop-color="#98938c"></stop><stop offset="1" stop-color="#d2c5c1"></stop></linearGradient></defs><path fill="url(#s)" d="M741 305c2-2 4-4 7-5-1 2-2 4-1 5-1 2-2 4-3 5 1 1 2 2 2 3s-1 1 1 2v-1c1 4 2 6 5 9h1c1 3 3 4 6 4l-1 1c1 1 3 1 4 2l-1 2h0l-6 4c-3 2-6 5-9 8l-5-4v-1c2 1 3 2 4 3l-3-6c-1 0-3-2-4-4v-3h1c0-2-2-3-3-5l1-2h0l1-4c0 1 0 3 1 4h0v-1c0-5 1-11 2-16z"></path><path d="M738 318c0 1 0 3 1 4h0v-1c2 5 1 9 3 15-1 0-3-2-4-4v-3h1c0-2-2-3-3-5l1-2h0l1-4z" class="H"></path><path d="M748 323s-2-2-2-3c-2-3-2-6-2-10 1 1 2 2 2 3s-1 1 1 2v-1c1 4 2 6 5 9h1c1 3 3 4 6 4l-1 1c-4-1-7-3-10-5z" class="B"></path><path d="M746 325c1-1-1-3-1-4l1 2h1 1c3 2 6 4 10 5 1 1 3 1 4 2l-1 2h0l-6 4h-2c-1-2 0-2-1-4 0 0-2-1-3-1-1-1-1-3-2-4l-1-2z" class="Q"></path><path d="M747 327c4 2 7 3 10 5 2 0 2-1 4 0l-6 4h-2c-1-2 0-2-1-4 0 0-2-1-3-1-1-1-1-3-2-4z" class="d"></path><defs><linearGradient id="t" x1="750.431" y1="322.74" x2="742.066" y2="337.399" xlink:href="#B"><stop offset="0" stop-color="#908885"></stop><stop offset="1" stop-color="#bcb3aa"></stop></linearGradient></defs><path fill="url(#t)" d="M745 342l1 1v-1c0-1 1-2 0-3 0-2-1-4-1-6s-2-6-2-9c-1-4-1-8-1-12l2 6c0 2 0 5 2 7h0l1 2c1 1 1 3 2 4 1 0 3 1 3 1 1 2 0 2 1 4h2c-3 2-6 5-9 8l-5-4v-1c2 1 3 2 4 3z"></path><path d="M740 352c1-1 2-1 3-1l2 2h0c3 2 7 6 11 7 1 0 2 1 3 2l9 6c3 1 5 5 8 8-1-2-2-3-2-4v-1l-2-1 1-1c-1-1-3-2-3-4 1 2 3 3 5 4h0c7 6 10 14 14 22 0 1 1 1 1 2l1 2h1v-2-1-1c6 12 4 26 4 39l-1 2h0l-2 8-1-1c1-2 0-5 0-7v4h-1v-7c-1 1-1 2-1 3 0-2-1-3-1-5 0 4 1 12 0 14h-1v-6c-1-2-1-4-1-7-1-5-1-8-3-13 0-1-2-4-2-5-1-2-1-3-2-4v-3c-1-2-1-3-1-5-1-2-4-6-6-7-2-3-4-4-6-6-7-7-14-10-23-13 2-1 4 0 7 0 0 0 0-1 1-2-3-1-5-2-8-3-4-2-10-1-14-1h-5c-1 1-2 1-3 1v-1c1-1 2-1 4-1h-1l1-1c1 0 3 0 4-1v-1h-3-2v-2h-5c2-1 4 0 6 0h4l7 1v-1-1h-2v-2h0c1-2 3-4 5-5z" class="E"></path><path d="M787 404l2 2 2 1 3 9c-1 4 0 10-2 12 0-4 0-8-1-12l-4-12z" class="o"></path><defs><linearGradient id="u" x1="765.054" y1="387.404" x2="776.992" y2="380.49" xlink:href="#B"><stop offset="0" stop-color="#98918c"></stop><stop offset="1" stop-color="#c4bdba"></stop></linearGradient></defs><path fill="url(#u)" d="M748 364l1-2h2 0c3 0 4 1 6 2h1c5 3 9 7 14 11l6 7c6 8 10 16 13 25l-2-1-2-2c-2-5-4-9-7-14-8-12-19-20-32-26z"></path><path d="M740 352c1-1 2-1 3-1l2 2h0c3 2 7 6 11 7 1 0 2 1 3 2l9 6c3 1 5 5 8 8 1 2 3 4 2 6l-6-7c-5-4-9-8-14-11h-1c-2-1-3-2-6-2h0-2l-1 2c-1 0-2-1-3-1l-8-2v-1-1h-2v-2h0c1-2 3-4 5-5z" class="G"></path><path d="M748 359l1-1c2 2 4 3 6 3l3 3h-1c-2-1-3-2-6-2h0-2l-1 2c-1 0-2-1-3-1l1-1h1c0-1 0-2 1-3h0z" class="Y"></path><path d="M735 357h1c1-1 2-1 3-3h0l3-1h0c1 1 0 1 1 2v4c1 0 2 0 3-1v1h2 0c-1 1-1 2-1 3h-1l-1 1-8-2v-1-1h-2v-2h0z" class="x"></path><defs><linearGradient id="v" x1="770.372" y1="381.034" x2="766.022" y2="390.6" xlink:href="#B"><stop offset="0" stop-color="#7e7371"></stop><stop offset="1" stop-color="#8c8b86"></stop></linearGradient></defs><path fill="url(#v)" d="M752 370c17 7 27 21 34 37 3 7 4 15 5 22-1 1-1 2-1 3 0-2-1-3-1-5 0 4 1 12 0 14h-1v-6c-1-2-1-4-1-7-1-5-1-8-3-13 0-1-2-4-2-5-1-2-1-3-2-4v-3c-1-2-1-3-1-5-1-2-4-6-6-7-2-3-4-4-6-6-7-7-14-10-23-13 2-1 4 0 7 0 0 0 0-1 1-2z"></path><defs><linearGradient id="w" x1="779.621" y1="416.07" x2="789.53" y2="418.243" xlink:href="#B"><stop offset="0" stop-color="#3a3936"></stop><stop offset="1" stop-color="#535053"></stop></linearGradient></defs><path fill="url(#w)" d="M779 398c5 7 10 21 10 29 0 4 1 12 0 14h-1v-6c-1-2-1-4-1-7-1-5-1-8-3-13 0-1-2-4-2-5-1-2-1-3-2-4v-3c-1-2-1-3-1-5z"></path><path d="M776 376c-1-2-2-3-2-4v-1l-2-1 1-1c-1-1-3-2-3-4 1 2 3 3 5 4h0c7 6 10 14 14 22 0 1 1 1 1 2l1 2h1v-2-1-1c6 12 4 26 4 39l-1 2h0l-2 8-1-1c1-2 0-5 0-7v-4c2-2 1-8 2-12l-3-9c-3-9-7-17-13-25 1-2-1-4-2-6z" class="f"></path><path d="M794 416c1 5 2 11 1 16l-2 8-1-1c1-2 0-5 0-7v-4c2-2 1-8 2-12z" class="M"></path><path d="M661 137c4 0 8 0 11 1 3-1 5 0 9 0l12 1h1c2 0 3 0 5 1s2 1 4 0h2 3 5v1c-1 0-3 1-3 3l-1 2 1 2-1 1v3h0c2 2 5 4 6 6h0l10 10 10 13-4 17-7 20-1-3v3 1c-1 0-1 1-1 2-1 0-2 1-2 2 0-2 2-4 2-7v-1l-2 3c0-2 0-3-1-5 0 1-1 4-1 4h-1c-1 2-1 4-3 5v1 1c-1 1 0-1-1 1v1c-1 0-1 1-1 2h-1c-1-2-1-2-3-3h-1-1c0-2-1-3-3-4l-5 6-1-1-2 2c0-2 0-3-1-4l-4 3v-1c0-2 1-2 2-3 1-2 2-3 3-5-1-1-1-2-2-3l-1-1 1-1-1-1-1-2h-1v-2l1-2c1 0 1-1 2-2h1c0-2-1-3-2-4-2-1-4-2-5-4l-1-1s-1 0-1 1h-2l1-1c1-1 0-1 0-2h-1l-1 1-4 4h0c-2 1 0 1-2 1v-1l1-2v-1l-2 3-3 4-1 1c-1 0-2 0-4 1l-4 7-4-3c-1-1 0-2 0-4l-1-1c0-2-1-3-1-5h0c1-2 1-3 0-4 1-5 5-11 8-16l1-2h0-1c-1 1-3-1-4-1-10-6-20-9-32-11l1-1-1-1h0c0-1-1-1-2-2h-3c-1 0-1-1-1-2-3 0-5 1-7-1-1 0-2 1-3 1-3-1-5-1-6-3v-1h-6l-11 4-1-1c6-2 12-4 19-5l1-1c-1-1-1-2-2-2v-2-1h2v4l1 2c1 0 2-1 3-2 0-1 3-3 4-4 4-3 7-5 11-6 6-1 11-2 17-2h9 0c1-1 5-1 7-1z" class="AE"></path><path d="M712 203v-1c0-2 0-2 2-4v7l-2-2z" class="C"></path><path d="M681 168h0c1 1 1 0 1 1 0 2-2 4-4 5h-1v-1c0-2 0-1 1-2 2-1 2-1 3-3z" class="b"></path><path d="M693 192l2-2 2 2c-1 1-2 2-3 4l-1 1-3-3h1 1l1-2z" class="O"></path><path d="M712 203l2 2c0 3 0 6-1 9l-2-9c1 0 1-1 1-2z" class="j"></path><path d="M719 213l4-10v5c0 2-1 5-1 7l-2 3c0-2 0-3-1-5z" class="k"></path><path d="M700 219c3-3 6-5 10-6l2-1-3 3c-2 3-4 5-7 7l-2-3z" class="b"></path><path d="M689 188s1-1 2-1l3 1s1 1 2 1v-1c2-1 4-3 6-4-1 1-5 7-5 8l-2-2-2 2c0-1 1-2 1-3h0c-1 1-2 2-2 3l-1-1c-1-1-2-2-2-3z" class="S"></path><path d="M700 219l2 3-5 4-2 2c0-2 0-3-1-4 1-1 1-2 2-2l4-3zm-15-36h0c3-2 6-5 9-7-2 3-6 5-8 9l3 3c0 1 1 2 2 3l1 1c0-1 1-2 2-3h0c0 1-1 2-1 3l-1 2h-1-1c-2-1-3-3-5-5h1v-1c0-2 0-4-1-5z" class="E"></path><path d="M678 156v-1c1 1 2 1 3 2 0 0 1 1 2 1v-1c2 1 3 2 4 3 4 2 10 9 12 13h-3c-5-7-10-12-18-17z" class="J"></path><path d="M692 212l4-3c0 2-2 3-1 4l2-1c1 1 1 1 2 3h0c-2 2-3 3-3 5-1 1-3 3-5 4l1 1c0-1 1-2 2-2l1-2 1 1c-1 0-1 1-2 2l-4 3v-1c0-2 1-2 2-3 1-2 2-3 3-5-1-1-1-2-2-3l-1-1 1-1-1-1z" class="y"></path><path d="M661 137c4 0 8 0 11 1l6 2c-2 0-5 0-6 1l-18-3h0c1-1 5-1 7-1z" class="AP"></path><path d="M616 153l-2-1 1-1c2-1 3-1 5-2s10-1 12-1l1 1h2l5 1h-1v-1l-5-1v-1c3 1 9 1 12 4 1 1 3 1 4 1-2 0-4 1-6 2h0c-2 0-4-1-5-2h2c-1-1-2-1-3-1l-3-1-4-1c-5 0-11 0-16 2 0 1 0 1 1 2z" class="S"></path><path d="M658 150l1-1c1-1 8-1 10-1h4l1 1h-1c-2 0-4 1-5 0 0 1 0 1 1 1 5 1 10 4 14 7v1c-1 0-2-1-2-1-1-1-2-1-3-2v1c-7-3-13-5-20-6z" class="AC"></path><path d="M659 146c3 0 11 0 14 2h-4c-2 0-9 0-10 1l-1 1c-3 1-5 1-8 2-1 0-3 0-4-1 4-3 8-4 13-5z" class="K"></path><path d="M659 146c3-2 6-2 10-1l9 3 6 3c2 1 4 1 6 3l1 1h1v-1-1h0c2 1 3 1 5 3h0l-1 1v1c1 0 1 1 1 2h0c-2-1-5-3-7-3-5-4-10-7-16-8l-1-1c-3-2-11-2-14-2z" class="z"></path><path d="M670 184h1c1 2 2 1 4 1 1 1 1 2 3 3 1 1 2-1 3 1l-1 2c-1 1-2 2-3 4l-2 3c-1-1-2-1-3-1v-1l1-2h-1l-1-1-2-2h-2l3-7z" class="O"></path><path d="M673 194c1-1 2-3 4-4 0 0 1 1 1 0 1 0 1 0 2 1-1 1-2 2-3 4l-2 3c-1-1-2-1-3-1v-1l1-2z" class="D"></path><path d="M670 184h1c1 2 2 1 4 1 1 1 1 2 3 3 1 1 2-1 3 1h-4-1l-2-2h-1c0 1-1 1-1 2-1 1 0 3-1 4l-2-2h-2l3-7z" class="e"></path><path d="M696 173h3c3 5 6 8 8 13 1 1 2 3 3 5h1l1-2 1 7 1 2c-2 2-2 2-2 4v1c0 1 0 2-1 2-3-12-8-22-15-32z" class="q"></path><path d="M672 141c1-1 4-1 6-1l3 1c3 1 6 1 8 3 6 2 13 6 16 11 0 2 1 3 1 4-11-8-21-14-34-18z" class="AU"></path><path d="M667 175c1-1 2-2 3-4v2c1 1 2 1 3 1h1 0c4 2 8 6 11 9 1 1 1 3 1 5v1h-1c-3-2-7-5-9-8l-2-2c-1 1-2 3-3 5h-1v-1h-2-1v-1c0-2-1-2-2-4l1-2 1-1z" class="F"></path><path d="M667 175l1 1c0 1 0 3 1 4v1l5-3c1 1 2 2 2 3l-2-2c-1 1-2 3-3 5h-1v-1h-2-1v-1c0-2-1-2-2-4l1-2 1-1z" class="f"></path><path d="M697 160h0c0-1 0-2-1-2v-1l1-1c5 6 13 11 15 19h0c1 4 2 7 2 11 0 2 0 6 1 8-1-1-1-1-1-2-1 1 0 3-1 4l-1-7-2-6c-2-4-4-7-6-11v-1h1c-1-1-1-2-1-3l-7-8z" class="U"></path><path d="M707 170v-1c2 2 3 3 3 5l2 4h-1-1l-3-8z" class="AL"></path><path d="M704 168l2 2h1l3 8c1 1 1 2 0 3v2c-2-4-4-7-6-11v-1h1c-1-1-1-2-1-3z" class="j"></path><path d="M607 146h2v4l1 2c1 0 2-1 3-2 0 1-1 2-1 3 2 0 8 1 10 0h-6c-1-1-1-1-1-2 5-2 11-2 16-2l-3 1c-1 1 0 0 0 2 1 0 2 1 4 1 1 1 1 1 1 2 0 0 1-1 2-1s2 1 3 2h0-1c0 1 0 1 1 2h0-1c-3 0-4 0-6-2h-6l2 2h0-4c-3 0-5 1-7-1-1 0-2 1-3 1-3-1-5-1-6-3v-1h-6l-11 4-1-1c6-2 12-4 19-5l1-1c-1-1-1-2-2-2v-2-1z" class="E"></path><path d="M607 155c5 0 10-1 15 0 1 0 2 0 3 1l2 2h0-4c-3 0-5 1-7-1-1 0-2 1-3 1-3-1-5-1-6-3z" class="P"></path><path d="M674 149c6 1 11 4 16 8 2 0 5 2 7 3l7 8c0 1 0 2 1 3h-1v1c2 4 4 7 6 11l2 6-1 2h-1c-1-2-2-4-3-5-2-5-5-8-8-13-2-4-8-11-12-13-1-1-2-2-4-3-4-3-9-6-14-7-1 0-1 0-1-1 1 1 3 0 5 0h1z" class="R"></path><path d="M690 157c2 0 5 2 7 3l7 8c0 1 0 2 1 3h-1v1h0l-14-15z" class="AK"></path><defs><linearGradient id="x" x1="644.1" y1="156.678" x2="635.53" y2="168.266" xlink:href="#B"><stop offset="0" stop-color="#242426"></stop><stop offset="1" stop-color="#636263"></stop></linearGradient></defs><path fill="url(#x)" d="M638 156c2 0 3 0 5 1l1-1c1 0 2 1 4 1h1l1 1h-2-4c1 0 2 1 3 1l2 1c2 0 5 2 7 4h-1c0 2 1 3 2 4-2 1-2 1-2 2s0 1 1 2c1 0 2 1 4 1h1c2 1 3 1 5 3h-1c-1 1-3-1-4-1-10-6-20-9-32-11l1-1-1-1h0c0-1-1-1-2-2h-3c-1 0-1-1-1-2h4 0l-2-2h6c2 2 3 2 6 2h1 0c-1-1-1-1-1-2h1z"></path><path d="M672 138c3-1 5 0 9 0l12 1h1c2 0 3 0 5 1s2 1 4 0h2 3 5v1c-1 0-3 1-3 3l-1 2 1 2-1 1v3h0c2 2 5 4 6 6l-1 1 2 4h-2l-4-4v-1 2c-2-1-2-1-3 0l-1-1c0-1-1-2-1-4-3-5-10-9-16-11-2-2-5-2-8-3l-3-1-6-2z" class="a"></path><path d="M681 141c1 0 3-1 4-1 2 0 2 1 4 1l1 1c2-2 3-2 5-1h1c-1 1-1 2-2 3h-5c-2-2-5-2-8-3z" class="k"></path><path d="M705 155h2l-3-4v-1c2 3 5 5 8 7l2 2 2 4h-2l-4-4v-1 2c-2-1-2-1-3 0l-1-1c0-1-1-2-1-4z" class="t"></path><path d="M708 140h5v1c-1 0-3 1-3 3l-1 2 1 2-1 1v3h0 0c-2-2-4-5-4-7s2-3 3-5z" class="AS"></path><path d="M709 149c-1-1-1 0-1-1-1-1-1-2-1-3h1l1 1 1 2-1 1z" class="Ab"></path><path d="M696 141h2l1 3v1l1 1c0 2 3 3 4 4v1l3 4h-2c-3-5-10-9-16-11h5c1-1 1-2 2-3z" class="AN"></path><defs><linearGradient id="y" x1="669.507" y1="185.159" x2="656.954" y2="196.071" xlink:href="#B"><stop offset="0" stop-color="#565556"></stop><stop offset="1" stop-color="#858181"></stop></linearGradient></defs><path fill="url(#y)" d="M665 178c1 2 2 2 2 4v1h1 2v1l-3 7h2l2 2 1 1h1l-1 2v1c1 0 2 0 3 1l-3 4-1 1c-1 0-2 0-4 1l-4 7-4-3c-1-1 0-2 0-4l-1-1c0-2-1-3-1-5h0c1-2 1-3 0-4 1-5 5-11 8-16z"></path><path d="M667 191h2l2 2 1 1h1l-1 2h0c-2 0-3-1-4-2 0-1 0-1-1-2h0v-1z" class="B"></path><defs><linearGradient id="z" x1="666.358" y1="184.741" x2="656.642" y2="187.259" xlink:href="#B"><stop offset="0" stop-color="#5f5c5e"></stop><stop offset="1" stop-color="#858280"></stop></linearGradient></defs><path fill="url(#z)" d="M665 178c1 2 2 2 2 4l-2 1c-1 2-3 5-3 6-1 1-1 2-1 2-2 2-1 3-2 5v2c0 1 1 1 1 2v1h-1v1l-1 1c0-2-1-3-1-5h0c1-2 1-3 0-4 1-5 5-11 8-16z"></path><path d="M659 204l6-11 6 5 1-1c1 0 2 0 3 1l-3 4-1 1c-1 0-2 0-4 1l-4 7-4-3c-1-1 0-2 0-4z" class="e"></path><path d="M672 197c1 0 2 0 3 1l-3 4-1 1c-1 0-2 0-4 1 1-2 3-4 4-6l1-1z" class="I"></path><path d="M707 160c1-1 1-1 3 0v-2 1l4 4h2l-2-4 1-1h0l10 10 10 13-4 17-7 20-1-3v3 1c-1 0-1 1-1 2-1 0-2 1-2 2 0-2 2-4 2-7v-1c0-2 1-5 1-7v-5c-1-2 0-3 0-5 1-3 1-5 1-8 0-13-9-22-17-30z" class="AN"></path><path d="M723 208l1-2c0-2 0-4 1-6 1-4 2-7 2-10 1-1 1-2 2-3h0c1 2 1 2 0 3 0 2 0 2 1 4v-1h1l-5 15c-1 3-1 5-3 7v3 1c-1 0-1 1-1 2-1 0-2 1-2 2 0-2 2-4 2-7v-1c0-2 1-5 1-7z" class="t"></path><path d="M715 158h0l10 10 10 13-4 17-7 20-1-3c2-2 2-4 3-7l5-15c1-1 1-4 1-5 0-6-5-11-8-16-3-3-5-6-8-9l-2-4 1-1z" class="m"></path><path d="M775 208c7 0 14 0 20 1 7 1 12 3 19 5 5 1 10 2 15 4 3 3 6 4 9 6h1c2 1 5 4 6 6 7 5 11 13 16 21h0l3 9 2 4c2 5 3 10 4 15 1 1 2 1 2 3 0 3 1 7 2 10v5c-1 3-1 4-1 6-3 7-7 12-14 17 0 1-1 1-1 2h0c-3 1-9 2-13 1-4 0-7-1-10-3-7-4-9-11-11-18l2-3 3 1-1-3-1-1v-7c1-4 3-7 7-11 3-2 8-3 12-1l4 1v1h2l2 2c1 1 0 2 1 2 2 1 5 1 6 3h-1 1l1-1 1 1 1-1c0-3 0-7-1-10 0-3-1-5-2-8-6-8-16-15-26-16v-2c-3-1-6-1-9-1-1 0-2 0-4 1-4 0-9 1-14 2-1 1-3 0-5 1-3 0-8 2-10 4l-1 1c-4 1-8 1-12 3l-8 4-3-2h-3l3 3-9-1-2 1c-1 1-1 1-3 2l-2 1h0l-2 1c-1 1-3 4-4 5l1 2-2 2c0 1 0 2-1 3v1c-3 6-6 12-8 19v1 1 2h0c0 2 0 3 1 4 0 3-1 6 0 9l-1 4h0l-1 2c1 2 3 3 3 5h-1c-3-3-8-6-10-10-2-1-4-4-4-6-1-1-2-1-3-1-2 1-2 1-3 2s-1 2-1 3h0l-1 3h-1c0 1-1 3-2 5-2 1-6 2-8 4h-1l-1-1c1-1 2-2 2-3l-1-1c-1-5 18-56 21-65l12-40 1 1 1 1h0l5-2h0c3 0 5-1 8-1 0-2 1-3 2-4 3-1 4-3 6-5 3 1 6 0 9 0 2-1 4 0 6-1z" class="b"></path><path d="M724 277l1-1v2s-1 1-1 2c1 1 1 2 0 3h0c-1 0-1 0-1-1-1-2 0-3 1-5z" class="e"></path><path d="M729 282c0-1-1-3-2-4h0v-2h2 3-1l-1 2c0 1-1 3-1 4z" class="E"></path><path d="M714 314c-1-1-1-2-2-2 0-2 0-3 1-5h0v-1c1-1 0-1 1-2 1 1 2 2 3 2s1 0 1 1h-1l-1-1c-1 1-1 0-1 1 0 2-1 4-1 7z" class="e"></path><path d="M793 236c4-1 8-1 12-1v1h0l-18 3v-1c2-1 4-2 6-2z" class="F"></path><path d="M775 230c6-2 11-2 17-1h10 2l1 1h-6c-3-1-7 1-11 0-3-2-8 1-13 0z" class="S"></path><path d="M715 320c0 1-1 3-2 5-2 1-6 2-8 4h-1l-1-1c1-1 2-2 2-3 4-2 7-3 10-5z" class="m"></path><path d="M725 303c0 1 0 1 1 1-1-1-1-2-1-3l-1-1v-1c0-1-1-2-1-2 0-1 0-2-1-3 0-2 0-4-2-5l-1-2c1-1 0-2 0-4 0-1 1-1 1-2h1c1 1 1 3 1 4 1 2 3 5 4 7h-2c0 2 0 3 1 4l2 10c-1 0 0 1-1 0 0 0-1-1-1-2v-1z" class="y"></path><path d="M772 260c8-4 17-8 26-10-3 4-10 6-15 6h0c-2 1-2 2-3 3v1l-8 4-3-2 3-2z" class="v"></path><path d="M756 247c1 0 1 0 2-1l-4 11c-2 1-4 1-5 3-4 2-4 5-7 8-1-1-1-2-2-3 0-1 0-1-1-1l-1 1-1 4v-2c1-2 0-3 2-4h1c1 1 1 2 1 3h1c2-2 2-5 5-6l2-2v-1c-2 1-3 1-5 3 0 1 0 1-1 2v-1-2l2-2h1c1-1 2-1 4-1h0 0l1-1-1-1c1 0 1-1 2-1 0-1 1-2 2-3 0-1 1-2 2-3z" class="E"></path><path d="M772 238c6-4 12-3 19-4 5-1 11-1 16-1l15 2c-3 1-6 1-10 1h-7 0 0v-1c-4 0-8 0-12 1h-7-4c-2 1-4 0-7 1-1 1-2 1-3 1z" class="e"></path><path d="M798 250c10-1 18-3 28-2-1 0-2 0-4 1-4 0-9 1-14 2-1 1-3 0-5 1-3 0-8 2-10 4l-1 1c-4 1-8 1-12 3v-1c1-1 1-2 3-3h0c5 0 12-2 15-6z" class="P"></path><defs><linearGradient id="AA" x1="838.851" y1="232.614" x2="829.948" y2="236.993" xlink:href="#B"><stop offset="0" stop-color="#373638"></stop><stop offset="1" stop-color="#555"></stop></linearGradient></defs><path fill="url(#AA)" d="M826 228c5 2 9 5 14 7 2 0 2 1 3 3l-1 1h1c2 1 3 3 5 4l4 4v1h0l4 7v1h0c-8-12-18-19-31-25h0c1-1 1-2 1-3z"></path><defs><linearGradient id="AB" x1="761.325" y1="213.162" x2="766.965" y2="215.693" xlink:href="#B"><stop offset="0" stop-color="#7a7879"></stop><stop offset="1" stop-color="#999493"></stop></linearGradient></defs><path fill="url(#AB)" d="M769 209c2-1 4 0 6-1l-6 4h3c3 0 6-1 9 0-1 0-2 1-3 0-1 0-2 1-3 1h-2l1 1-1 1h3l-24 3c0-2 1-3 2-4 3-1 4-3 6-5 3 1 6 0 9 0z"></path><path d="M769 209c2-1 4 0 6-1l-6 4-1 1h-1c1-2 1-3 2-4z" class="I"></path><path d="M750 232c-1 0-3 1-4 1-1-1-1-2-1-3 1-2 1-3 2-5h1l1-2h1c2-2 8-5 11-5h1c1-1 1 0 3 0 0 1 2 0 3 0h12c2-1 9 0 11 0h1v-1c1 0 1 0 2 1h6c0 1 1 1 2 1h1 0c3 0 4 1 6 2-14-2-26-3-40-2-6 1-11 3-16 7l-3 3c0 1 0 0 1 1 0 0-1 1-1 2z" class="O"></path><path d="M758 246h0c1 0 1 0 2-1 1 1 0 2 0 3h0v3c1 3 2 7 4 10l1 1h1l3 3-9-1-2 1c-1-1-2-1-3-1h-1l-1-1c-2-1-3-2-4-3 1-2 3-2 5-3l4-11z" class="G"></path><path d="M760 264c0-1 0 0 1-1h0c0-2-1-2-1-3 1 0 2 0 3 1 1 0 1 1 2 1h0 1l3 3-9-1z" class="P"></path><path d="M754 257v1 3l2 2s-1 0-2 1l-1-1c-2-1-3-2-4-3 1-2 3-2 5-3z" class="F"></path><path d="M758 246h0c1 0 1 0 2-1 1 1 0 2 0 3h0c-1 1-1 2-2 4 0 1 1 2 2 3h1l-1 1h-4l-2 2v-1l4-11z" class="P"></path><path d="M718 307c1 1 2 2 3 2l-1-2c-1-3-4-7-4-11-1-1 0-3 0-5l7 10c0 1 1 1 2 2v1c0 1 1 2 1 2-1 2-1 3 0 4l1 4c1 1 1 2 1 3v1 1c-2-1-4-4-4-6-1-1-2-1-3-1-2 1-2 1-3 2s-1 2-1 3h0c-1 0-1-1-2-2l-1-1c0-3 1-5 1-7 0-1 0 0 1-1l1 1h1z" class="E"></path><path d="M715 315l2-1v-2l1-2 2-1c0 1 1 1 2 2h0 1l1 1v1c-1-1-2-1-3-1-2 1-2 1-3 2s-1 2-1 3h0c-1 0-1-1-2-2z" class="B"></path><path d="M723 311c-1-3-2-5-1-9h1l2 2c0 1 1 2 1 2-1 2-1 3 0 4l1 4c1 1 1 2 1 3v1 1c-2-1-4-4-4-6v-1l-1-1z" class="AF"></path><path d="M775 230c5 1 10-2 13 0-10 1-21 5-30 11-4 2-8 5-12 7-2 2-4 4-6 5l1-1-1-1h1c0-1 1-2 2-3l1-2c1-2 4-3 6-5v-1h2l6-6c2 0 3 0 5-1l12-3z" class="B"></path><path d="M753 226c5-4 10-6 16-7 14-1 26 0 40 2 6 2 11 4 17 7 0 1 0 2-1 3h0l-8-2 1-1h1c-2-3-6-2-9-4-2-1-5-1-7-1-7-1-13-1-19-2-7-1-13 1-19 3-5 0-8 3-12 2z" class="P"></path><defs><linearGradient id="AC" x1="783.673" y1="230.253" x2="783.88" y2="221.545" xlink:href="#B"><stop offset="0" stop-color="#484849"></stop><stop offset="1" stop-color="#6f6d6e"></stop></linearGradient></defs><path fill="url(#AC)" d="M753 226c4 1 7-2 12-2 6-2 12-4 19-3 6 1 12 1 19 2 2 0 5 0 7 1 3 2 7 1 9 4h-1l-1 1c-21-6-46-5-67 3 0-1 1-2 1-2-1-1-1 0-1-1l3-3z"></path><path d="M772 238c1 0 2 0 3-1 3-1 5 0 7-1h4 7c-2 0-4 1-6 2v1l-4 1-5 2c-1 1-7 4-7 5-3 2-5 4-7 6-2 0-2 0-3-1l-1-1v-3h0c0-1 1-2 0-3-1 1-1 1-2 1h0c-1 1-1 1-2 1v-1c-3 2-6 4-8 6-3 2-4 5-7 7 0 0 0 1-1 2v-1c-1-1 0-1 0-1 1-1 2-3 4-4l-1-1c1 0 1 0 1-1 3-1 5-2 7-4s6-6 9-6c2 0 3-2 5-3 2 0 5-1 7-2z" class="O"></path><path d="M787 238v1l-4 1-5 2c-1 1-7 4-7 5-3 2-5 4-7 6-2 0-2 0-3-1 1-1 1-2 1-3l1-1h-1v-2c3-3 5-3 9-5h0c2-1 3-1 5-2s5 0 8 0l3-1z" class="f"></path><path d="M771 241h0c2-1 3-1 5-2s5 0 8 0c-4 1-12 2-15 5h0l3-2-1-1h0z" class="B"></path><defs><linearGradient id="AD" x1="862.275" y1="290.965" x2="851.053" y2="294.157" xlink:href="#B"><stop offset="0" stop-color="#282526"></stop><stop offset="1" stop-color="#494747"></stop></linearGradient></defs><path fill="url(#AD)" d="M852 279l2 2c1 1 0 2 1 2 2 1 5 1 6 3h-1l1 2c2 1 1 0 2 0 1 2 0 7-1 8-1 2-3 6-5 7h-3l-9 3 5-10v-1h0v-1c-1 0-2 0-3-1h1l1-1 1 1h0c0-1 1-2 2-3v-1-1l-1-1c1-2 1-2 1-3l-1-1c0-2 0-2 1-4z"></path><path d="M850 295c1-1 1-2 2-3 0 1-1 4-1 5h1c0-2 0-2 1-3 0 2 0 5-1 7h-1v1h1c1 0 1 1 2 1l-9 3 5-10v-1z" class="G"></path><defs><linearGradient id="AE" x1="869.269" y1="271.898" x2="840.852" y2="313.354" xlink:href="#B"><stop offset="0" stop-color="#8d8a89"></stop><stop offset="1" stop-color="#b5b2b2"></stop></linearGradient></defs><path fill="url(#AE)" d="M862 259l2 1 2 4c2 5 3 10 4 15 0 10-1 21-8 29-5 4-11 6-17 6s-11-3-15-8c-1-1-2-2-2-4h2v-1l1-1 1-1c0 2 1 2 3 4h1c0 2 1 3 1 4l1 1-1 1 2 1c4 2 10 1 14 0 6-2 9-8 12-13 5-12 1-27-3-38z"></path><path d="M830 301l1-1 1-1c0 2 1 2 3 4h1c0 2 1 3 1 4l1 1-1 1c-3-2-5-5-7-7v-1z" class="E"></path><defs><linearGradient id="AF" x1="814.739" y1="241.44" x2="821.749" y2="218.375" xlink:href="#B"><stop offset="0" stop-color="#908d8d"></stop><stop offset="1" stop-color="#d8d5d2"></stop></linearGradient></defs><path fill="url(#AF)" d="M775 208c7 0 14 0 20 1 7 1 12 3 19 5 5 1 10 2 15 4 3 3 6 4 9 6h1c2 1 5 4 6 6 7 5 11 13 16 21h0l3 9-2-1c-4-8-8-15-14-21-19-19-47-23-72-23h-3l1-1-1-1h2c1 0 2-1 3-1 1 1 2 0 3 0-3-1-6 0-9 0h-3l6-4z"></path><path d="M775 208c7 0 14 0 20 1 7 1 12 3 19 5 5 1 10 2 15 4 3 3 6 4 9 6h1c2 1 5 4 6 6-4-1-8-5-11-7-3-1-5-2-8-3-12-5-24-8-37-9-3 0-6 0-8 1-3-1-6 0-9 0h-3l6-4z" class="d"></path><path d="M775 208c7 0 14 0 20 1-5 2-10 0-15 0-3 0-5 2-8 3h-3l6-4z" class="X"></path><path d="M834 278c3-2 8-3 12-1l4 1v1h2c-1 2-1 2-1 4l1 1c0 1 0 1-1 3l1 1v1 1c-1 1-2 2-2 3h0l-1-1-1 1h-1c1 1 2 1 3 1v1c-3 1-7 7-9 10l-3 3-1-1c0-1-1-2-1-4h-1c-2-2-3-2-3-4l-1 1-1 1-1-1-1-3-1-1v-7c1-4 3-7 7-11z" class="e"></path><path d="M832 299c1-1 4-1 6-2l-2 6h-1c-2-2-3-2-3-4z" class="O"></path><path d="M827 289v4c1 0 2 1 3 0l1-2 2 2h2v1c0 1 0 1-2 2l-1 1h-4l-1-1v-7z" class="E"></path><path d="M832 297c-1-1-1-2-1-3 1-1 2-1 4 0 0 1 0 1-2 2l-1 1z" class="b"></path><path d="M839 283h1l2 2 2 5-9 3h-2l-2-2c2-3 5-5 8-8z" class="p"></path><path d="M839 283h1l2 2-1 1-1 1c-2 0-3 0-4 2l-2 3h0c-1 1-1 0-1 1l-2-2c2-3 5-5 8-8z" class="P"></path><path d="M850 279h2c-1 2-1 2-1 4l1 1c0 1 0 1-1 3l1 1v1 1c-1 1-2 2-2 3h0l-1-1-1 1h-1c1 1 2 1 3 1v1c-3 1-7 7-9 10l-3 3-1-1c0-1-1-2-1-4l2-6c1 0 1-1 2-1h0c2-1 5-3 5-5l-1-1-2-5-2-2h-1v-2h1c1 0 2 1 2 2l2 2h2 1c2-1 1 0 3 0l1-1-1-5z" class="B"></path><path d="M841 305l-1-2c1-4 3-7 6-9 0 1 0 0 1 0 0-1-1-3-1-5l1-1c0 1 0 1 1 1 1-1 2 0 4-1v1 1c-1 1-2 2-2 3h0l-1-1-1 1h-1c1 1 2 1 3 1v1c-3 1-7 7-9 10z" class="f"></path><path d="M834 278c3-2 8-3 12-1l4 1v1l1 5-1 1c-2 0-1-1-3 0h-1-2l-2-2c0-1-1-2-2-2h-1v2c-3 3-6 5-8 8l-1 2c-1 1-2 0-3 0v-4c1-4 3-7 7-11z" class="AE"></path><defs><linearGradient id="AG" x1="839.151" y1="288.152" x2="860.973" y2="312.375" xlink:href="#B"><stop offset="0" stop-color="#373534"></stop><stop offset="1" stop-color="#64605f"></stop></linearGradient></defs><path fill="url(#AG)" d="M870 279c1 1 2 1 2 3 0 3 1 7 2 10v5c-1 3-1 4-1 6-3 7-7 12-14 17 0 1-1 1-1 2h0c-3 1-9 2-13 1-4 0-7-1-10-3-7-4-9-11-11-18l2-3 3 1 1 1v1h-2c0 2 1 3 2 4 4 5 9 8 15 8s12-2 17-6c7-8 8-19 8-29z"></path><path d="M845 323c1-2 3-2 4-3s1-1 1-2c2-2 3-3 6-3 1 0 2-1 3-1 2-1 3-2 4-3l2-2 1-1c0-1 1-2 2-3 2-2 2-9 4-10 1 0 1 2 2 2-1 3-1 4-1 6-3 7-7 12-14 17 0 1-1 1-1 2h0c-3 1-9 2-13 1z" class="G"></path><path d="M784 241l19-3c11-1 25 0 36 5 3 2 7 5 10 8 2 1 3 3 5 5-2-2-5-4-7-6-5-3-10-5-16-6-4-1-9-1-13-1-16 1-32 5-46 14l-3 3h3l-3 2h-3-1l-1-1c-2-3-3-7-4-10l1 1c1 1 1 1 3 1 2-2 4-4 7-6 0-1 6-4 7-5l5-2 1 1z" class="r"></path><path d="M775 246l4-1h2 1c4-1 7-2 11-2 3-1 5-2 8-2-5 3-12 3-17 5-3 1-5 3-7 3s-4 2-6 3l-1-1c1-2 4-3 5-5z" class="E"></path><path d="M771 247c0-1 6-4 7-5l5-2 1 1c-3 1-7 3-9 5h0c-1 2-4 3-5 5l1 1h-1c-1 1-3 2-5 3v3l2 2 2-1v1h3l-3 2h-3-1l-1-1c-2-3-3-7-4-10l1 1c1 1 1 1 3 1 2-2 4-4 7-6z" class="O"></path><path d="M761 252c1 1 1 1 3 1 2-2 4-4 7-6l-1 1c-1 2-3 3-4 5-1 1-2 2-2 4 0 1 1 0 1 2 0 1 0 1-1 2-2-3-3-7-4-10l1 1z" class="y"></path><defs><linearGradient id="AH" x1="728.989" y1="286.531" x2="744.407" y2="291.86" xlink:href="#B"><stop offset="0" stop-color="#605a58"></stop><stop offset="1" stop-color="#908885"></stop></linearGradient></defs><path fill="url(#AH)" d="M749 260c1 1 2 2 4 3l1 1h1c1 0 2 0 3 1-1 1-1 1-3 2l-2 1h0l-2 1c-1 1-3 4-4 5l1 2-2 2c0 1 0 2-1 3v1c-3 6-6 12-8 19v1 1 2h0c0 2 0 3 1 4 0 3-1 6 0 9l-1 4h0l-1 2c1 2 3 3 3 5h-1c-3-3-8-6-10-10v-1-1c0-1 0-2-1-3l-1-4c-1-1-1-2 0-4 1 1 0 0 1 0l-2-10c-1-1-1-2-1-4h2c0 1 0 1 1 2v-2c1-3 2-6 2-10h0c0-1 1-3 1-4l1-2h1c0-2 0-3 1-3l2 2c1-2 2-4 2-6l1-4 1-1c1 0 1 0 1 1 1 1 1 2 2 3 3-3 3-6 7-8z"></path><path d="M738 265l1-1c1 0 1 0 1 1 1 1 1 2 2 3h0c-1 1-1 1-2 1-1 1-1 0-1 0l-1-4z" class="x"></path><path d="M730 315h1c1 1 2 1 2 2l1 1v-8c0-2 1-3 1-4v1c-1 5-1 10 2 15l-1 2c-2-3-5-5-6-9z" class="D"></path><path d="M753 268v-1l-4 1h0-1c0-1 1-2 1-2-1-1-1-2-1-3h1s1 1 2 1l2-1 1 1h1c1 0 2 0 3 1-1 1-1 1-3 2l-2 1z" class="H"></path><path d="M755 264c1 0 2 0 3 1-1 1-1 1-3 2l-2-2h0l2-1z" class="n"></path><path d="M732 276c0-2 0-3 1-3l2 2c0 3-2 9-4 11-1-2 0-2-2-4h0c0-1 1-3 1-4l1-2h1z" class="AF"></path><path d="M737 301v1 1 2h0c0 2 0 3 1 4 0 3-1 6 0 9l-1 4h0c-3-5-3-10-2-15 0 0 1-1 1-2s0-2 1-4z" class="AD"></path><path d="M727 306l1 2h0-1-1l1 1c1 1 1 2 2 4 0 1 0 2 1 2 1 4 4 6 6 9 1 2 3 3 3 5h-1c-3-3-8-6-10-10v-1-1c0-1 0-2-1-3l-1-4c-1-1-1-2 0-4 1 1 0 0 1 0z" class="AB"></path><defs><linearGradient id="AI" x1="801.837" y1="158.97" x2="822.914" y2="205.975" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2e2e2f"></stop></linearGradient></defs><path fill="url(#AI)" d="M875 134h1c6 4 10 10 14 15 1 2 3 3 2 5-7-8-15-12-26-13-5 0-11 0-15 3v1c7 0 13 2 18 6h0c6 5 11 13 12 21 1 3 1 7 0 10l-3 6c-3 7-9 12-17 14l-1 1h-1l-1-1c-3-1-5-4-7-6 1 3 1 3 3 5-1 0-2 0-3-1h0l-2 1 2 2v1l-2-2-1 1c4 2 7 6 10 9h-1c2 2 4 4 6 7l2 2c2 3 4 7 6 9v1l-2-3c-2-1-6-1-9-2h-1v1l3 2h-3l-3-2c-3-2-6-3-8-5l-9-3c2 2 5 4 7 7-2-1-4-3-6-3-1-1-2-1-3-2s-2-1-4-2c-1 0-2-1-3-1h0-1c-5-2-10-3-15-4-7-2-12-4-19-5-6-1-13-1-20-1-2 1-4 0-6 1-3 0-6 1-9 0-2 2-3 4-6 5-1 1-2 2-2 4-3 0-5 1-8 1h0l-5 2h0l-1-1-1-1 2-5v-2h-1c1-3 2-7 4-9 0-2 1-3 2-4 1-2 2-5 2-7l5-6h0c3-3 7-6 8-10l-5 3h-2v1l-2 2c0-1 1-2 1-3 1-4 7-7 10-10 1-1 1-2 2-2 1-1 1-2 1-2-7 4-15 11-21 18v-1c1-2 3-3 4-5 3-3 5-6 8-9 4-4 10-7 15-11 1-1 2-1 3-2l4-1c1-1 2-2 3-2 6-4 12-8 19-10l2-1c4-2 11-3 16-3h2l18-3v1h9c1-1 3-1 4-1h2 7c5-1 11-1 16-1z"></path><path d="M750 212h-1v-1-1c2-1 6-1 8-3h0 3c-2 1-4 2-5 3-1 0-3 0-4 2h-1zm26-23c-1-1-1-2-2-2v-1h2c2 1 6 3 7 5h-4l-3-2z" class="S"></path><path d="M758 205l2-2c1 0 2 1 3 0v-1h-2v1h-1v-1c-2-2-4-2-7-2h0l-1-1c4 0 7-1 10-1l1 1h2 0l-4 1v1h2v3l-5 1z" class="e"></path><path d="M806 185c-1 1-4 0-6 0l-1-2c-2 1-3 0-5-1h-3c-3 1-4-1-6-2l1-1h2c1 1 1 1 3 2h3v1c2 0 3-1 4-1l1 1h0l2 1h0l-1 1 1 1h5z" class="S"></path><path d="M771 197h4l-1 1c-1 1-2 1-4 1v1c1 1 2 0 3 0l1 2h-6c-2 0-3-1-5-1h-2v-1l4-1h0-2l-1-1 9-1z" class="B"></path><path d="M755 210h0c2 0 3-1 5-1-2 2-3 4-6 5-1 1-2 2-2 4-3 0-5 1-8 1 2-1 3-3 5-4 1-1 1-1 1-3h1c1-2 3-2 4-2z" class="O"></path><path d="M790 193l-11-10c1 1 2 1 3 2 2 1 6 2 9 2v-1l-1-1v-1l2 1c1 1 1 1 1 2s0 1 1 1l2 2-1 1c-1-1-2-1-4-1 0 1 0 1 1 2s1 1 2 1l1 1h0-1-1l-3-1z" class="P"></path><path d="M751 192l2-1v1l-7 9-7 13v-2h-1c1-3 2-7 4-9 0-2 1-3 2-4 1-2 2-5 2-7l1 1v1c-1 1-1 1-1 2h0l1 1c1-2 2-4 4-5h0z" class="g"></path><path d="M776 189l3 2h4l3 3c2 1 2 2 3 4-5-2-10-2-14-1h-4c1-1 2-1 3-2v-1c-1 0-2-1-3-1h0v-1h1c2-1 2-1 4-3h0z" class="y"></path><path d="M779 191h4l3 3h-2c-2-1-6-1-8 0l-1-2 1-1h3z" class="E"></path><path d="M850 135h2 7c-2 1-3 1-5 2l-3 1c-2 1-4 3-5 4l-1 2c1 0 1 1 1 1v2c1 1 2 0 1 2-1 1-1 1-2 3h0c-1 1 0 0-1 2h0c-4-1-8-2-11-3-9-1-16 2-24 4 1 0 2 0 2-1v-1l2-1 2-2c1 0 2-1 3-1 2-1 2-1 3-3 4-1 7-1 11-2v-1c2-1 5-1 7-2 1 0 1-1 2-2v-1l-4-2h0 9c1-1 3-1 4-1z" class="z"></path><path d="M844 154c-2-3-8-3-11-5l-1-3h4 1c1 0 1 0 2 1 2 0 3 1 4 1 2 1 2 1 4 1h0c-1 1-1 1-2 3h0c-1 1 0 0-1 2h0z" class="b"></path><path d="M850 135h2 7c-2 1-3 1-5 2h-3c-2 0-3 2-5 3-1 1-2 1-3 1 0 1-1 2-2 2-3 1-6 1-9 1v-1c2-1 5-1 7-2 1 0 1-1 2-2v-1l-4-2h0 9c1-1 3-1 4-1z" class="U"></path><path d="M816 184h8v1l1 1h-2l-1 1c1 2 3 2 4 3v1h-1-3v1c1 1 1 1 2 1l-2 1 2 1v1l-5 1-1 1c-2 0-5 0-7-1-3 0-6 0-8-1h-5l-5-2h1 1 0l-1-1c-1 0-1 0-2-1s-1-1-1-2c2 0 3 0 4 1l1-1-2-2c-1 0-1 0-1-1s0-1-1-2c4 1 7 1 11 3h2c1 1 4 1 5 1l-1-1v-1c1 0 1-1 2-1l5-2z" class="D"></path><path d="M819 193l1-1v-2c2 0 3 0 5 1h-3v1c1 1 1 1 2 1l-2 1c-5-1-11 1-15-1l1-1h5c2 1 4 1 6 1z" class="n"></path><path d="M819 193c-1-1-2 0-2-2 1 0 0 0 1-1l-1-1v-1l2 1v-1l-1-1c1-1 2-1 3-1l1-1h2l1 1h-2l-1 1c1 2 3 2 4 3v1h-1c-2-1-3-1-5-1v2l-1 1z" class="H"></path><path d="M803 188h2c1 1 4 1 5 1l-1-1v-1c1 0 1-1 2-1l5-2c0 2 0 2 1 3h-1s-1 0-1 1l-1 2h-5c-3-1-4-1-6 0h-1c-1-1-2-1-2-1l-1 1c1 1 3 2 4 3l-1 1h1c1 0 3 1 4 2h1 2c1 0 1 0 2 1h-1c-3 0-6 0-8-1h-5l-5-2h1 1 0l-1-1c-1 0-1 0-2-1s-1-1-1-2c2 0 3 0 4 1l1-1-2-2c-1 0-1 0-1-1s0-1-1-2c4 1 7 1 11 3z" class="x"></path><defs><linearGradient id="AJ" x1="818.142" y1="157.306" x2="827.762" y2="175.563" xlink:href="#B"><stop offset="0" stop-color="#0c0c0d"></stop><stop offset="1" stop-color="#2a2a2c"></stop></linearGradient></defs><path fill="url(#AJ)" d="M803 171h0c-2 0-4 0-5 1h-1c-1 0-1 1-2 1h-3c0 1-1 1-2 1 1-1 3-2 4-3s3-1 4-2c2-1 4-3 6-3 1-1 3-1 4-2 2-1 6-2 9-3 6-2 13-2 20-1h4c1 1 4 0 6 0 4 0 9 1 12 4-7-1-10-1-17 2h0c-1 1-2 1-4 3-1-1-2-1-4-1-3 1-5 1-8 1l-18 1c-1 0-1 0-2 1h-3z"></path><path d="M832 143v1c-4 1-7 1-11 2-1 2-1 2-3 3-1 0-2 1-3 1l-2 2-2 1v1c0 1-1 1-2 1l-33 17-12 9-11 11v-1l-2 1c2-1 2-3 3-4l9-11c2-1 5-3 6-5v-1c14-12 32-20 49-25 4-1 9-2 14-3z" class="l"></path><path d="M776 172l1-1v-1c5-3 9-7 14-10 4-2 7-3 11-5 2-1 5-3 7-5 4-1 8-2 12-4-1 2-1 2-3 3-1 0-2 1-3 1l-2 2-2 1v1c0 1-1 1-2 1l-33 17z" class="U"></path><path d="M842 166h0c7-3 10-3 17-2 3 2 5 4 6 7 2 3 2 7 1 10-1 2-3 4-4 5s-2 1-3 1-3 0-5-1v-1h1l-1-1-1-2v-5c-1 1-1 1-2 1v-1c-2 1-2 1-2 3l-2 1c-1 0-2-2-4-2h-1c-1 0-2-1-3-2 0-4 1-7 4-10l-1-1z" class="Y"></path><path d="M853 164c4 2 7 3 9 7l1 1v1l-6-3c-3-1-5-1-8-1 2-1 4-1 5-3l-1-2z" class="e"></path><path d="M852 176l2-1h2v-3c2 0 3 1 4 1 1 1 1 1 1 2 1 2-1 4-2 5v1h-1c-2 1 0 1-2 1-1 0-1 1-2 2l-1-2v-5l3 1 1-1-1-1h-4z" class="I"></path><path d="M853 177l3 1-1 1c0 1 2 2 4 2h-1c-2 1 0 1-2 1-1 0-1 1-2 2l-1-2v-5z" class="H"></path><path d="M843 167c3-2 6-2 10-3l1 2c-1 2-3 2-5 3s-6 3-6 5c-1 2-1 4-1 5-1 0-2-1-3-2 0-4 1-7 4-10z" class="AE"></path><path d="M859 180l1 1h0c2-1 4-3 5-5 0-2 0-2-1-4l1-1c2 3 2 7 1 10-1 2-3 4-4 5s-2 1-3 1-3 0-5-1v-1h1l-1-1c1-1 1-2 2-2 2 0 0 0 2-1h1v-1z" class="D"></path><defs><linearGradient id="AK" x1="843.237" y1="173.922" x2="851.554" y2="175.753" xlink:href="#B"><stop offset="0" stop-color="#484646"></stop><stop offset="1" stop-color="#5f5d5d"></stop></linearGradient></defs><path fill="url(#AK)" d="M849 169c3 0 5 0 8 1-3 0-5 0-6 2v1l-1 1v1h2v1h4l1 1-1 1-3-1c-1 1-1 1-2 1v-1c-2 1-2 1-2 3l-2 1c-1 0-2-2-4-2h-1c0-1 0-3 1-5 0-2 4-4 6-5z"></path><defs><linearGradient id="AL" x1="781.78" y1="216.827" x2="788.374" y2="200.559" xlink:href="#B"><stop offset="0" stop-color="#312927"></stop><stop offset="1" stop-color="#4e5051"></stop></linearGradient></defs><path fill="url(#AL)" d="M775 197c4-1 9-1 14 1 3 3 6 5 9 7 2 1 4 1 6 2l1-1c3 2 5 3 8 3l4-2 1 1-1 2c1 1 2 2 4 2 1 2 4 3 6 4 1 1 2 1 3 2h-1c-5-2-10-3-15-4-7-2-12-4-19-5-6-1-13-1-20-1-2 1-4 0-6 1-3 0-6 1-9 0-2 0-3 1-5 1h0c1-1 3-2 5-3l3-1h-5-2c1-1 1-1 2-1l5-1v-3c2 0 3 1 5 1h6l-1-2c-1 0-2 1-3 0v-1c2 0 3 0 4-1l1-1z"></path><path d="M763 201c2 0 3 1 5 1h6 2l3 1c-3 0-7 0-10 1h-2l1 1h0c-3 0-8 0-10 1h-2c1-1 1-1 2-1l5-1v-3z" class="F"></path><path d="M763 201c2 0 3 1 5 1h1c-1 1-4 2-6 2v-3z" class="O"></path><path d="M775 197c4-1 9-1 14 1 3 3 6 5 9 7h0l1 1h-6c-5-2-11-1-16-2-2 1-5 0-7 0h-1c3-1 7-1 10-1l-3-1h-2l-1-2c-1 0-2 1-3 0v-1c2 0 3 0 4-1l1-1z" class="G"></path><path d="M776 202h8c1 0 2-1 3 0h4l1 1c-1 1-5 0-7 1-2 0-6-1-8 0s-5 0-7 0h-1c3-1 7-1 10-1l-3-1z" class="P"></path><path d="M842 166l1 1c-3 3-4 6-4 10 1 1 2 2 3 2h1c2 0 3 2 4 2l2-1c0-2 0-2 2-3v1c1 0 1 0 2-1v5l1 2 1 1h-1v1l-2 6c-1 1-1 3-1 4 1 3 1 3 3 5-1 0-2 0-3-1h0l-2 1 2 2v1l-2-2-1 1-1-1c-3-2-7-4-10-6-2-1-4-3-6-4-1-1-1-2-2-2-1-2-3-5-3-7 1 0 2 0 3 1 1 0 2 1 2 1h1 1c1-2 1-7 1-10 1-2 3-4 4-6 2-2 3-2 4-3z" class="X"></path><path d="M839 177c1 1 2 2 3 2h1c1 1 1 2 1 3v1c1 1 0 0 0 1h-1c-2-1-3-4-4-7z" class="e"></path><path d="M833 185c1 1 2 2 3 4s3 3 5 5c2 3 6 5 8 8l-1 1-1-1c-3-2-7-4-10-6 0-1 0-1 1-2 1 1 1 2 3 2-1-2-3-4-5-5h-1v-2c-1-2-2-2-3-3v-1h1z" class="d"></path><path d="M829 190c-1-2-3-5-3-7 1 0 2 0 3 1 1 0 2 1 2 1h1v1c1 1 2 1 3 3v2h1c2 1 4 3 5 5-2 0-2-1-3-2-1 1-1 1-1 2-2-1-4-3-6-4-1-1-1-2-2-2z" class="X"></path><path d="M849 180c0-2 0-2 2-3v1c1 0 1 0 2-1v5l1 2 1 1h-1v1l-2 6c-1 1-1 3-1 4 1 3 1 3 3 5-1 0-2 0-3-1h0l-2 1c-3-2-4-4-5-6-2-4-2-5-2-9h1c1-1 2-1 3-1l1 1v-2h-3c0-1 1 0 0-1v-1c0-1 0-2-1-3 2 0 3 2 4 2l2-1z" class="Q"></path><path d="M849 180c0-2 0-2 2-3v1c1 0 1 0 2-1v5l1 2 1 1h-1l-1 1c-2 1-6 2-6 5-1 3 0 4 1 7-2-2-3-4-2-7v-3l1-2v-2h-3c0-1 1 0 0-1v-1c0-1 0-2-1-3 2 0 3 2 4 2l2-1z" class="G"></path><path d="M849 180c0-2 0-2 2-3v1c1 0 1 0 2-1v5l1 2 1 1h-1l-1 1c-1-1-2-1-3-2s0-2 0-3l-1-1z" class="x"></path><path d="M850 184v-1l3-1 1 2 1 1h-1l-1 1c-1-1-2-1-3-2z" class="V"></path><defs><linearGradient id="AM" x1="872.07" y1="174.241" x2="880.31" y2="180.419" xlink:href="#B"><stop offset="0" stop-color="#414041"></stop><stop offset="1" stop-color="#5c5b5c"></stop></linearGradient></defs><path fill="url(#AM)" d="M869 151c6 5 11 13 12 21 1 3 1 7 0 10l-3 6c-3 7-9 12-17 14l-1 1h-1l-1-1c-3-1-5-4-7-6 0-1 0-3 1-4l2-6c2 1 4 1 5 1s2 0 3-1c0 2 1 2 1 3 4-3 6-7 8-11v-4c-1 0-1-1-1-2 0-2 1-2 2-3l-1-3h1c1 1 2 2 2 4h1c1 0 0 0 1 1h1c1-2 1-2 2-3v-2l-1-1c-1-4-3-6-5-9l-3-3c-1 0-1-1-1-2z"></path><path d="M876 187c1-2 2-4 5-5l-3 6-2 2v-3z" class="p"></path><path d="M866 198l1-1 1-1c0-1 1-1 2-2 1-3 3-5 6-7v3l-3 3c-1 2-4 4-7 5z" class="G"></path><path d="M876 190l2-2c-3 7-9 12-17 14l-1 1h-1l-1-1v-1c1 0 1-1 3-1 1-1 3-1 5-2 3-1 6-3 7-5l3-3z" class="D"></path><path d="M872 169l-1-3h1c1 1 2 2 2 4v1 1c1 3 1 7 0 10l-1 2h-1v1 1l-1 2c-1 1-1 2-2 3l-2 2c0 1-1 1-1 2l-3 1v1h-1 0c-1 1-3 1-4 2s-1 1 0 1v1 1c-3-1-5-4-7-6 0-1 0-3 1-4l2-6c2 1 4 1 5 1s2 0 3-1c0 2 1 2 1 3 4-3 6-7 8-11v-4c-1 0-1-1-1-2 0-2 1-2 2-3z" class="f"></path><path d="M872 169c1 1 1 3 1 4s0 1-1 2c0 1 0 2-1 3v-4c-1 0-1-1-1-2 0-2 1-2 2-3z" class="E"></path><path d="M854 186c2 1 4 1 5 1s2 0 3-1c0 2 1 2 1 3l-3 2c-2 1-5 2-7 1h-1l2-6z" class="AE"></path><defs><linearGradient id="AN" x1="809.645" y1="171.853" x2="815.204" y2="186.763" xlink:href="#B"><stop offset="0" stop-color="#1e1e1f"></stop><stop offset="1" stop-color="#585758"></stop></linearGradient></defs><path fill="url(#AN)" d="M826 169c3 0 5 0 8-1 2 0 3 0 4 1-1 2-3 4-4 6 0 3 0 8-1 10h-1-1s-1-1-2-1c-1-1-2-1-3-1 0 2 2 5 3 7h0l-4-4-1-1v-1h-8l-5 2c-1 0-1 1-2 1v1l1 1c-1 0-4 0-5-1h-2c2-1 4-1 6-2h0l-3-1h-5l-1-1 1-1h0l-2-1h0l-1-1c-1 0-2 1-4 1v-1h-3c-2-1-2-1-3-2l1-2h2 3l15-2c-2-1-3-1-5-1l-1-1h0l-1-1 1-1h3c1-1 1-1 2-1l18-1z"></path><path d="M788 179l1-2h2 3-1l1 1c-1 1-2 2-3 1h-2-1z" class="b"></path><path d="M809 181c1 1 3 1 5 0l-3-1h4 9c-1 0-2 1-3 1s-2 0-3 1h-3c-2 0-4 0-6-1z" class="F"></path><path d="M798 181h1 1c0 1 0 1 1 1 2 0 6 0 7-1h1c2 1 4 1 6 1-3 1-5 1-8 2-2 0-5 0-6 1l-1-1 1-1h0l-2-1h0l-1-1z" class="E"></path><path d="M824 180c1-1 3-1 5-1 2 1 3 1 3 3v1c-1 1 0 1 0 2h-1s-1-1-2-1c-1-1-2-1-3-1 0 2 2 5 3 7h0l-4-4-1-1v-1h-8l-5 2c-1 0-1 1-2 1v1l1 1c-1 0-4 0-5-1h-2c2-1 4-1 6-2h0l-3-1h-5c1-1 4-1 6-1 3-1 5-1 8-2h3c1-1 2-1 3-1s2-1 3-1z" class="B"></path><path d="M826 169c3 0 5 0 8-1 2 0 3 0 4 1-1 2-3 4-4 6-3 2-8 1-12 1h0 0c2 0 9 0 11-1h-7-1v-1l-10 1c-2 0-5 1-6 0-2-1-3-1-5-1l-1-1h0l-1-1 1-1h3c1-1 1-1 2-1l18-1z" class="F"></path><path d="M818 173h16 0c-2 2-8 0-9 1l-10 1c-2 0-5 1-6 0-2-1-3-1-5-1 5-1 10 0 14-1z" class="e"></path><path d="M803 171h3c1-1 1-1 2-1l18-1c-1 0-6 0-7 2l2 1-3 1c-4 1-9 0-14 1l-1-1h0l-1-1 1-1z" class="E"></path><defs><linearGradient id="AO" x1="795.29" y1="150.825" x2="799.839" y2="162.055" xlink:href="#B"><stop offset="0" stop-color="#030101"></stop><stop offset="1" stop-color="#300907"></stop></linearGradient></defs><path fill="url(#AO)" d="M819 138l18-3v1h0l4 2v1c-1 1-1 2-2 2-2 1-5 1-7 2l-14 3c-17 5-35 13-49 25v1c-1 2-4 4-6 5l-9 11c-1 1-1 3-3 4h0c-2 1-3 3-4 5l-1-1h0c0-1 0-1 1-2v-1l-1-1 5-6h0c3-3 7-6 8-10l-5 3h-2v1l-2 2c0-1 1-2 1-3 1-4 7-7 10-10 1-1 1-2 2-2 1-1 1-2 1-2-7 4-15 11-21 18v-1c1-2 3-3 4-5 3-3 5-6 8-9 4-4 10-7 15-11 1-1 2-1 3-2l4-1c1-1 2-2 3-2 6-4 12-8 19-10l2-1c4-2 11-3 16-3h2z"></path><path d="M824 138c4-1 8-1 13-2l4 2c-9-1-16 4-24 5 3-1 5-2 7-5z" class="AL"></path><path d="M808 143c1-1 3-1 5-2l11-3c-2 3-4 4-7 5l-13 4c0-1 0-1 1-2l3-2z" class="AG"></path><path d="M804 144l4-1-3 2c-1 1-1 1-1 2l-16 7 1-1c1-2 2-3 3-4s2-1 3-1l9-4z" class="K"></path><path d="M769 171v1c-1 2-4 4-6 5l-9 11c-1 1-1 3-3 4h0c-2 1-3 3-4 5l-1-1h0c0-1 0-1 1-2v-1c1-1 2-2 3-4 3-2 5-5 7-7l12-11z" class="C"></path><path d="M819 138l18-3v1h0c-5 1-9 1-13 2l-11 3c-2 1-4 1-5 2l-4 1c-1-1-3-2-5-2l2-1c4-2 11-3 16-3h2z" class="Ab"></path><path d="M764 165h0c8-7 19-13 28-16-1 1-2 2-3 4l-1 1c-10 6-22 12-29 22l-5 3h-2v1l-2 2c0-1 1-2 1-3 1-4 7-7 10-10 1-1 1-2 2-2 1-1 1-2 1-2z" class="l"></path><defs><linearGradient id="AP" x1="780.808" y1="164.066" x2="769.442" y2="153.195" xlink:href="#B"><stop offset="0" stop-color="#ffc1b8"></stop><stop offset="1" stop-color="#e3d1d2"></stop></linearGradient></defs><path fill="url(#AP)" d="M799 142c2 0 4 1 5 2l-9 4c-1 0-2 0-3 1-9 3-20 9-28 16h0c-7 4-15 11-21 18v-1c1-2 3-3 4-5 3-3 5-6 8-9 4-4 10-7 15-11 1-1 2-1 3-2l4-1c1-1 2-2 3-2 6-4 12-8 19-10z"></path><path d="M825 191h1v-1c-1-1-3-1-4-3l1-1h2l4 4h0c1 0 1 1 2 2 2 1 4 3 6 4 3 2 7 4 10 6l1 1c4 2 7 6 10 9h-1c2 2 4 4 6 7l2 2c2 3 4 7 6 9v1l-2-3c-2-1-6-1-9-2h-1v1l3 2h-3l-3-2c-3-2-6-3-8-5l-9-3c2 2 5 4 7 7-2-1-4-3-6-3-1-1-2-1-3-2s-2-1-4-2c-1 0-2-1-3-1h0c-1-1-2-1-3-2-2-1-5-2-6-4-2 0-3-1-4-2l1-2-1-1-4 2c-3 0-5-1-8-3-6-3-11-8-15-13l3 1 5 2h5c2 1 5 1 8 1 2 1 5 1 7 1l1-1 5-1v-1l-2-1 2-1c-1 0-1 0-2-1v-1h3z" class="u"></path><path d="M841 217c2-1 4 0 6 0l18 4c2 3 4 7 6 9v1l-2-3c-2-1-6-1-9-2l2-2 1 1v-1c-1-1-3-2-4-3l-14-3-4-1z" class="L"></path><path d="M824 204h5c5 2 13 7 18 5 1 1 3 2 5 1 0-1-2-2-3-3 3 1 5 2 7 5h1c2 2 4 4 6 7h-1c-4 0-6-5-9-6-1-1-3 0-4-1-2 0-5-2-8-2-2 0-4 1-6 0-1-1-2-1-3-2-2-1-4-1-6-2-1 0-1-1-2-2z" class="M"></path><defs><linearGradient id="AQ" x1="806.967" y1="198.439" x2="804.966" y2="206.203" xlink:href="#B"><stop offset="0" stop-color="#898382"></stop><stop offset="1" stop-color="#aea9a8"></stop></linearGradient></defs><path fill="url(#AQ)" d="M790 193l3 1 5 2h5l-1 1c1 1 0 1 2 1 1 0 2 2 4 2h2v1h0 0c1 1 1 1 2 1s1 0 2 1h1c2 0 3 0 5 1v1h-3l1 1s0 1 1 1v1h-1l-1-1-4 2c-3 0-5-1-8-3-6-3-11-8-15-13z"></path><path d="M798 196h5l-1 1c1 1 0 1 2 1 1 0 2 2 4 2-1 0-1 0-1 1-3 0-7-2-9-3v-2z" class="H"></path><path d="M819 208c2 0 3 1 5 2 4 2 9 3 13 2 2 0 2 0 3 1h1v4l4 1 14 3c1 1 3 2 4 3v1l-1-1-2 2h-1v1l3 2h-3l-3-2c-3-2-6-3-8-5l-9-3c2 2 5 4 7 7-2-1-4-3-6-3-1-1-2-1-3-2s-2-1-4-2c-1 0-2-1-3-1h0c-1-1-2-1-3-2-2-1-5-2-6-4-2 0-3-1-4-2l1-2h1z" class="E"></path><path d="M859 221c1 1 3 2 4 3v1l-1-1-2 2h-1v1l3 2h-3l-3-2c-3-2-6-3-8-5 2 0 5 1 7 1 1 1 2 1 4 1v-1c-1 0-3-1-4-1v-1c1 1 2 1 4 0z" class="AF"></path><path d="M819 208c2 0 3 1 5 2 4 2 9 3 13 2 2 0 2 0 3 1h1v4l4 1c-2 0-5 1-7 0 1-1 2-1 2-3h0l-3 1c-6 0-11-2-16-4-2 0-3-1-4-2l1-2h1z" class="d"></path><path d="M825 191h1v-1c-1-1-3-1-4-3l1-1h2l4 4h0c1 0 1 1 2 2 2 1 4 3 6 4 3 2 7 4 10 6l1 1c4 2 7 6 10 9h-1-1c-2-3-4-4-7-5 1 1 3 2 3 3-2 1-4 0-5-1-5 2-13-3-18-5h-5c-3-2-7-2-11-2 1-2 4-2 5-4h0l1-1 5-1v-1l-2-1 2-1c-1 0-1 0-2-1v-1h3z" class="L"></path><path d="M824 196l2 1-3 1v1l1 1c-2 0-3 0-5-1v-2l5-1z" class="Q"></path><path d="M826 197c3 1 7 1 10 3 1 1 4 2 5 2h2l2 2c2 0 3 2 4 3s3 2 3 3c-2 1-4 0-5-1h0c-7-4-16-7-23-9l-1-1v-1l3-1z" class="AA"></path><path d="M825 191h1v-1c-1-1-3-1-4-3l1-1h2l4 4h0c1 0 1 1 2 2 2 1 4 3 6 4 3 2 7 4 10 6l1 1c4 2 7 6 10 9h-1-1c-2-3-4-4-7-5-1-1-2-3-4-3l-2-2h-2c-1 0-4-1-5-2-3-2-7-2-10-3l-2-1v-1l-2-1 2-1c-1 0-1 0-2-1v-1h3z" class="I"></path><path d="M707 373h0 3 6c-1 2-2 4-4 5v1h4c0 1 0 2 1 3h1c1 0 0 0 1 1 6-6 14-6 22-6 7 1 18 3 24 8l2 2h1c1 1 3 3 5 4s5 5 6 7c0 2 0 3 1 5v3c1 1 1 2 2 4 0 1 2 4 2 5 2 5 2 8 3 13 0 3 0 5 1 7v6c0 6-1 11-3 17 0 2-3 10-4 11-2 3-3 5-6 7h0c-1 0-2 1-3 2-1 2-2 2-3 4-2 0-4 0-6 1-1 1-2 2-3 2-6 2-11 2-16 3-4 2-10 0-14-1 1 1 3 1 4 3-6-2-11-3-14-9-1-2-2-4-3-5l-1-1c-1-1-1-2-1-3v-1 1c-3 2-4 6-8 5h-1c-1 0-2 1-4 0-3 1-4 0-7-2v-2h-1 0-1l-6 3c-5 3-10 9-12 14-1 3-2 7-2 10h0c-1 2-1 4-1 6 1 1 1 1 1 2-3-3-5-7-6-11l-1 1h0c1 1 1 2 2 4-2-1-2-1-4 0 0 0-1 1-1 2h-1v-1c-2-1-4-7-6-7h0v1l1 1v1 3c-2-2-3-5-3-7l-1-1c-1-3 0-4-1-6 1-9 4-20 10-28v-1h1v-1c1-2 1-5 0-7h0v6h0-1c-1-1-2-2-4-3h0v-1c-1 0-1-1-2-2l1-2c1-9 4-20 8-28l5-19v-5h1l1-2 1 1v-1l1-1 1-1c0-1 0-1 1-1v-1h2v-1l1-1c1 0 1-1 1-1 2-3 3-7 5-9l2-2c1-2 3-3 5-4h3l3 1 2 1v1h0l3 3c2 0 3 1 5 0l-3-2v-4h2z" class="AZ"></path><path d="M715 472c1 0 1 1 2 1 2-2 1-5 3-7l1 1v5c1 4 4 7 6 10 6 4 10 5 17 6-4 2-10 0-14-1 1 1 3 1 4 3-6-2-11-3-14-9-1-2-2-4-3-5l-1-1c-1-1-1-2-1-3z" class="L"></path><path d="M720 466l1 1v5c1 4 4 7 6 10-1 0-2 0-3-1-1-2-3-3-4-5-1-4 0-5 0-9v-1z" class="Q"></path><path d="M782 415h2c2 5 2 8 3 13 0 3 0 5 1 7v6c0 6-1 11-3 17 0 2-3 10-4 11h-1l-1 2h-4l-1-2 6-6c5-8 5-14 5-23 1-1 1-1 1-2 0-9-1-15-4-23z" class="p"></path><path d="M786 438c1 7 0 12-2 20-1 2-1 3-2 5h-2c5-8 5-14 5-23 1-1 1-1 1-2z" class="F"></path><defs><linearGradient id="AR" x1="769.679" y1="469.739" x2="767.216" y2="476.222" xlink:href="#B"><stop offset="0" stop-color="#7c7675"></stop><stop offset="1" stop-color="#978e8a"></stop></linearGradient></defs><path fill="url(#AR)" d="M753 474v-1c0-2 1-5 2-6v1c1 2 4 5 6 5 4 1 10-1 13-4l1 2h4l1-2h1c-2 3-3 5-6 7h0c-1 0-2 1-3 2-1 2-2 2-3 4-2 0-4 0-6 1-2 0-4-1-6-2-3-2-3-4-4-7z"></path><path d="M755 473h0 1c3 3 8 4 13 4h0c2 0 3 0 5-1h1c-1 0-2 1-3 2-1 2-2 2-3 4-2 0-4 0-6 1-2 0-4-1-6-2-3-2-3-4-4-7l1-1h1z" class="AA"></path><path d="M753 474l1-1h1c1 3 3 5 6 6 2 2 5 2 8 1l1-1 2-1c-1 2-2 2-3 4-2 0-4 0-6 1-2 0-4-1-6-2-3-2-3-4-4-7z" class="E"></path><path d="M726 410c3-2 6-2 9-1 4 1 9 4 11 9 2 3 3 8 2 12-2 5-8 10-13 12-2 1-4 1-6 2-7 1-13 0-20-2 0-1 1-1 1-2 4 1 6 2 10 1h4 1c1-1 2-2 3-2s1 0 2-1c1 0 3 0 5-1v-1c1 0 2-1 3-2v1 1 1l2-1h-1c0-2 2-6 3-7 1-3 2-4 1-7v-1c0-2-1-5-3-7l-2-2c-2-1-3-1-5-2h0c-2 0-3 0-5 1h0l-2-1z" class="Y"></path><path d="M738 412c3 1 4 2 5 5 2 1 2 3 2 5 1 2 0 7-2 9 0 2-2 4-3 5h-1c0-2 2-6 3-7 1-3 2-4 1-7v-1c0-2-1-5-3-7l-2-2z" class="Q"></path><path d="M725 441h1c4-1 7-2 11-4-2 2-4 3-6 5-1 0-1 1-2 2-7 1-13 0-20-2 0-1 1-1 1-2 4 1 6 2 10 1h4 1z" class="P"></path><path d="M701 438c1 0 2 1 3 1v1c-2 1-3 2-4 3 0 1 0 2 1 3l8 5c4 4 7 10 7 16 0 2 0 3-1 4v1c-3 2-4 6-8 5h-1c-1 0-2 1-4 0h1c3-2 5-4 6-7-1-2-2-5-3-8h3l-1-2c-1-3-3-4-7-6-4-1-8-1-12 0-1 0-2 0-2-1h0l3-1 3-1c1 0 1-1 2-2l-1-1v-1c0-2 0-4 1-5 0-1 2-1 2-1 2-2 2-3 4-3z" class="H"></path><path d="M701 454c2-1 2-1 4 0 3 0 4 2 5 4 2 3 2 8 1 11-1 4-3 6-5 8-1 0-2 1-4 0h1c3-2 5-4 6-7-1-2-2-5-3-8h3l-1-2c-1-3-3-4-7-6z" class="o"></path><path d="M709 462c0 2 1 5 0 8-1-2-2-5-3-8h3z" class="B"></path><path d="M701 438c1 0 2 1 3 1v1c-2 1-3 2-4 3 0 1 0 2 1 3l8 5c0 1-1 2 0 3h0-1c-5-3-13-2-18-2l3-1c1 0 1-1 2-2l-1-1v-1c0-2 0-4 1-5 0-1 2-1 2-1 2-2 2-3 4-3z" class="AF"></path><path d="M701 438c1 0 2 1 3 1v1c-2 1-3 2-4 3h-1l-2 1v2l2 1h-1l1 1h-1l-2 2h0c-1-2-1-2-1-3h-1c0-2 0-4 1-5 0-1 2-1 2-1 2-2 2-3 4-3z" class="B"></path><path d="M701 438c1 0 2 1 3 1v1h-6v1h-1c2-2 2-3 4-3z" class="F"></path><path d="M726 410l2 1h0c2-1 3-1 5-1h0c2 1 3 1 5 2l2 2c2 2 3 5 3 7v1c1 3 0 4-1 7-1 1-3 5-3 7h1l-2 1v-1-1-1c-1 1-2 2-3 2v1c-2 1-4 1-5 1-1 1-1 1-2 1s-2 1-3 2h-1-4-1l-2-1c-1-1-2-1-2-3h0c-1-1-3-2-5-3l-5-5 2-1 1 1h2c2 0 3 0 4-1l1 1c1 0 1-1 1-1l1 1c1-1 1-3 1-4 1-2 1-3 2-4s1-2 1-3c-1-2 0-2 1-3v-3l1 1c1-2 2-2 3-3z" class="H"></path><path d="M729 433c0 2 0 3-2 5l-3 2v1h-4-1l-2-1c-1-1-2-1-2-3 2 1 5 3 8 2 0-1 1-1 1-1h1c2-2 3-3 3-5h1z" class="e"></path><path d="M737 422l1 1v-2l1 1c1 4-1 7-3 10l-4 4c0-2 0-4 1-6l1 1v-1c1-1 1-2 2-3h1v-5z" class="d"></path><path d="M733 430c-1-1-1-2-2-4v-2-3l2 1c1 0 2 0 3-1h0l1 1v5h-1c-1 1-1 2-2 3v1l-1-1z" class="L"></path><path d="M723 424l4 6c1 1 2 2 2 3h-1c0 2-1 3-3 5h-1c-2 0-2-1-4-1-1 0-2-1-2-2l2-2v1c0-1 1-2 1-3l1-4-1-1 2-2z" class="V"></path><path d="M720 433v1c1 0 3 1 4 2h1 1c0-2 1-2 2-3 0 2-1 3-3 5h-1c-2 0-2-1-4-1-1 0-2-1-2-2l2-2z" class="P"></path><path d="M722 412l1 1v1l-1 1 1 1c1 1 3 2 5 3l1-1c0 1 1 3 1 4-1 0-1 0-2 1h-2v-1l-2-2v1c-1 1-1 2-1 3l-2 2 1 1-1 4c0 1-1 2-1 3v-1c-2-1-3-2-3-4 1-1 1-3 1-4 1-2 1-3 2-4s1-2 1-3c-1-2 0-2 1-3v-3z" class="p"></path><path d="M714 428l1 1c1 0 1-1 1-1l1 1c0 2 1 3 3 4l-2 2c0 1 1 2 2 2 2 0 2 1 4 1 0 0-1 0-1 1-3 1-6-1-8-2h0c-1-1-3-2-5-3l-5-5 2-1 1 1h2c2 0 3 0 4-1z" class="f"></path><path d="M726 410l2 1h0c2-1 3-1 5-1h0c2 1 3 1 5 2l2 2c2 2 3 5 3 7v1h0c-1-2-1-3-3-5l-1 1c-1-1-1-1-2 0h-3 0-3c-1 0-1-1-2-1v1l-1 1c-2-1-4-2-5-3l-1-1 1-1v-1c1-2 2-2 3-3z" class="P"></path><path d="M726 410l2 1h0c2-1 3-1 5-1h0c2 1 3 1 5 2l2 2v2l-1-1h-4c-2 0-4-2-6-1h0c-1 0-1 0-2 1h-3l-1-1v-1c1-2 2-2 3-3z" class="e"></path><path d="M719 383c6-6 14-6 22-6 7 1 18 3 24 8l2 2h1c1 1 3 3 5 4s5 5 6 7c0 2 0 3 1 5v3c1 1 1 2 2 4 0 1 2 4 2 5h-2c3 8 4 14 4 23 0 1 0 1-1 2l-2-9c-3-13-10-27-23-34-7-4-15-5-23-3-7 2-12 6-16 13 0 1-1 3 0 5v1 2l-1-1h0-1c1-1 1-2 1-3-1 1-1 3-2 3h-1c1-2 1-3 1-5 0 0-1 0-1-1s0-1 1-2c-1-2 0-2-1-2l-2 3h-2l1-4c0-2 0-3 1-3l1 1 1-1h1v-4c1-1 0-1 0-2v-3c0-1 1-2 1-3h-2c0-1-1-1-1-2 1-1 2-2 2-3h1z" class="E"></path><path d="M740 384c4 0 8 0 11 1v1h-3 1v1l-3-1h-13 0 0l1-1c2 0 4-1 6-1z" class="B"></path><path d="M774 398c3 3 4 5 6 8 1 1 1 2 2 4 0 1 2 4 2 5h-2c-2-4-4-9-6-13-1-1-2-2-2-4z" class="r"></path><path d="M736 387c4 0 8 0 13 1h2l2 1c1 0 2 0 2 1h1c1 0 2 1 2 2h0 0c-1 0-3-1-4-1h-2l-5-1h-8c-4 0-8 0-11 2l-2 1c0-1 2-2 3-3 2-2 4-2 7-3z" class="F"></path><defs><linearGradient id="AS" x1="729.899" y1="372.847" x2="767.449" y2="413.634" xlink:href="#B"><stop offset="0" stop-color="#020303"></stop><stop offset="1" stop-color="#2a2828"></stop></linearGradient></defs><path fill="url(#AS)" d="M719 383c6-6 14-6 22-6 7 1 18 3 24 8l2 2h1c1 1 3 3 5 4s5 5 6 7c0 2 0 3 1 5v3c-2-3-3-5-6-8h0c-6-6-11-10-18-14-7-3-19-5-26-2-4 1-7 3-11 6h-2c0-1-1-1-1-2 1-1 2-2 2-3h1z"></path><defs><linearGradient id="AT" x1="661.999" y1="431.635" x2="707.204" y2="460.177" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#363738"></stop></linearGradient></defs><path fill="url(#AT)" d="M707 373h0 3 6c-1 2-2 4-4 5v1h4c0 1 0 2 1 3h1c1 0 0 0 1 1h-1c0 1-1 2-2 3 0 1 1 1 1 2h2c0 1-1 2-1 3v3c0 1 1 1 0 2v4h-1l-1 1-1-1c-1 0-1 1-1 3l-1 4h2l2-3c1 0 0 0 1 2-1 1-1 1-1 2s1 1 1 1c0 2 0 3-1 5h1c1 0 1-2 2-3 0 1 0 2-1 3h1 0l1 1v-2-1c-1-2 0-4 0-5l1 5v3c-1 1-2 1-1 3 0 1 0 2-1 3s-1 2-2 4c0 1 0 3-1 4l-1-1s0 1-1 1l-1-1c-1 1-2 1-4 1h-2l-1-1-2 1 5 5c2 1 4 2 5 3h0c0 2 1 2 2 3l2 1h1c-4 1-6 0-10-1 0 1-1 1-1 2-1-1-2-1-4-2l-1-1c-1 0-2-1-3-1-2 0-2 1-4 3 0 0-2 0-2 1-1 1-1 3-1 5v1l1 1c-1 1-1 2-2 2l-3 1-3 1h0c0 1 1 1 2 1 4-1 8-1 12 0 4 2 6 3 7 6l1 2h-3c1 3 2 6 3 8-1 3-3 5-6 7h-1c-3 1-4 0-7-2v-2h-1 0-1l-6 3c-5 3-10 9-12 14-1 3-2 7-2 10h0c-1 2-1 4-1 6 1 1 1 1 1 2-3-3-5-7-6-11l-1 1h0c1 1 1 2 2 4-2-1-2-1-4 0 0 0-1 1-1 2h-1v-1c-2-1-4-7-6-7h0v1l1 1v1 3c-2-2-3-5-3-7l-1-1c-1-3 0-4-1-6 1-9 4-20 10-28v-1h1v-1c1-2 1-5 0-7h0v6h0-1c-1-1-2-2-4-3h0v-1c-1 0-1-1-2-2l1-2c1-9 4-20 8-28l5-19v-5h1l1-2 1 1v-1l1-1 1-1c0-1 0-1 1-1v-1h2v-1l1-1c1 0 1-1 1-1 2-3 3-7 5-9l2-2c1-2 3-3 5-4h3l3 1 2 1v1h0l3 3c2 0 3 1 5 0l-3-2v-4h2z"></path><path d="M705 393h2l3 3c1 1 0 1 0 3h-1v-1h0c-1 0 0 0-1-1s-1 0-2 0l1-2-2-2z" class="b"></path><path d="M690 410c-2-1-4-1-6-1h-1-1l2-1c1-2 1-2 3-2l1 1h0c2 1 3 1 4 3h-2z" class="F"></path><path d="M667 471v1l-2 2c0 1 0 2-1 3v-2-1c0-2 1-4-1-6v-1-1c1-1 2-2 4-2l1 2c-1 2-1 3-1 5z" class="S"></path><path d="M677 443c-1-2-1-4-1-6 1-1 1-3 1-4v-1l-1-1 2-5c1-2 1-3 1-4s0-2 1-3c0 0 1-1 1-2v-1c1 0 1 0 1-1v-1-1c1 0 2-1 3-1h1c-1 1-2 1-3 2l1 1h0c0 1-1 1-1 2l1 1h0 0c-2 2-2 3-3 5h-1c-2 5-2 11-3 17v3zm36-53l3 6c1 1 1 3 1 4l-1 1-1-1c-1 0-1 1-1 3l-1 4-1 2v1c0 2 0 3 1 5h0v1l-3-2v-1l-1-1c-1-3-1-6 0-9v7c0 1 1 2 2 3h1v-3-4-1c0-1 0-2 1-2l1-5c-1-1-1-2-1-3s-1-1-1-2 0-2 1-3z" class="b"></path><path d="M668 466c0-1 4-4 4-5v-6-14c1-4 1-10 3-14v1c0 1-1 4-1 6l-1 14c-1 5-1 10 0 15 2-2 3-4 6-4-3 2-5 3-7 6-2 2-4 3-5 6 0-2 0-3 1-5z" class="y"></path><path d="M687 406c4 1 7 3 10 5 1 5 1 9 3 14-2-1-3-1-4 0-1-1 0-1-1-2h-1c-1 0-1 0-2-1h0c1-1 3-1 5-2v-2l-1-1c-1 0-1-1-1-2s0-1-1-2l-2-3c-1-2-2-2-4-3h0l-1-1z" class="P"></path><path d="M677 408c3-9 11-18 19-22-4 5-9 10-11 16-1 0-1 1-2 2v-1h-1l-5 5z" class="b"></path><path d="M690 447l1 2 2-2h0s0 1 1 1l1 1c-1 1-1 2-2 2l-3 1-3 1h0c0 1 1 1 2 1-1 0-1 1-2 1-3 0-5 3-8 4-3 0-4 2-6 4-1-5-1-10 0-15 0 3 0 7 1 10 1 0 1 0 2-1 2-1 4-3 6-4 1 0 3 0 4-1 1 0 1-2 2-3h0c0-1 1-1 2-2z" class="F"></path><path d="M715 407l2-3c1 0 0 0 1 2-1 1-1 1-1 2s1 1 1 1c0 2 0 3-1 5h1c1 0 1-2 2-3 0 1 0 2-1 3h1 0l1 1v-2-1c-1-2 0-4 0-5l1 5v3c-1 1-2 1-1 3 0 1 0 2-1 3s-1 2-2 4c0 1 0 3-1 4l-1-1s0 1-1 1l-1-1c-1 1-2 1-4 1h-2l-1-1-2 1-1-1 1-1 1 1 1-1c2 1 3 1 5 1-1-2-1-2-1-4l-1 1c0 1-1 1-3 2l-1-1c1-1 1-2 1-2l1-1c0-2 0-2 1-4h0c2 1 2 1 2 3h0v-2l1-1-1-1-1-1v-3l3 2v-1h0c-1-2-1-3-1-5v-1l1-2h2z" class="B"></path><path d="M715 407l1 1-2 2v-1h-2l1-2h2z" class="O"></path><path d="M714 428c-1 0-2-2-2-2-1-2 1-5 0-7h0l1-2c3 0 4 3 6 4 1-1 1-2 2-3 0 1 0 2-1 3s-1 2-2 4c0 1 0 3-1 4l-1-1s0 1-1 1l-1-1z" class="P"></path><path d="M690 410h2l2 3c1 1 1 1 1 2s0 2 1 2l1 1v2c-2 1-4 1-5 2h0c1 1 1 1 2 1h1c1 1 0 1 1 2h-1v2l-1-1-5 2c0 1-1 2-2 2l-1 2c-3 4-3 9-4 13v1c1 0 2-1 3-2 1 0 1-1 2-2l1-1c0 2-1 3-2 5 0 1 0 1 1 1h1 2c-1 1-2 1-2 2h0c-1 1-1 3-2 3-1 1-3 1-4 1-2 1-4 3-6 4v-5c1-3 1-6 1-9v-3c1-6 1-12 3-17h1c1-2 1-3 3-5h0 0l-1-1c0-1 1-1 1-2h0c2-2 5-3 6-5z" class="y"></path><path d="M689 428s-1-1-2-1h-1v-1c1-2 3-3 6-4h0c1 1 1 1 2 1h1c1 1 0 1 1 2h-1v2l-1-1-5 2z" class="r"></path><path d="M696 425c1-1 2-1 4 0h0l1 2 2-1v1l1 1 1 1 5 5c2 1 4 2 5 3h0c0 2 1 2 2 3l2 1h1c-4 1-6 0-10-1 0 1-1 1-1 2-1-1-2-1-4-2l-1-1c-1 0-2-1-3-1-2 0-2 1-4 3 0 0-2 0-2 1-1 1-1 3-1 5v1c-1 0-1-1-1-1h0l-2 2-1-2h-2-1c-1 0-1 0-1-1 1-2 2-3 2-5l-1 1c-1 1-1 2-2 2-1 1-2 2-3 2v-1c1-4 1-9 4-13l1-2c1 0 2-1 2-2l5-2 1 1v-2h1z" class="b"></path><path d="M696 425c1-1 2-1 4 0h0l1 2c-1 1-2 4-3 5l-1-1c-1-1-2-2-3-4-3 1-5 2-7 3 1 0 2-1 2-2l5-2 1 1v-2h1z" class="G"></path><path d="M697 431v-4h-1l1-2h3l1 2c-1 1-2 4-3 5l-1-1z" class="p"></path><path d="M704 436c-1 0-1-1-1-1 1-1 1-3 3-3 1 1 2 2 4 2h0c2 1 4 2 5 3h0c0 2 1 2 2 3l2 1h1c-4 1-6 0-10-1 0 1-1 1-1 2-1-1-2-1-4-2h1c-1-2-1-3-2-4z" class="F"></path><path d="M704 436l1-1c1 1 5 4 5 5s-1 1-1 2c-1-1-2-1-4-2h1c-1-2-1-3-2-4z" class="r"></path><path d="M688 441v-1c1-2 4-2 5-4l2-2 3 3c1 0 2-1 3 1-2 0-2 1-4 3 0 0-2 0-2 1-1 1-1 3-1 5v1c-1 0-1-1-1-1h0l-2 2-1-2h-2-1c-1 0-1 0-1-1 1-2 2-3 2-5z" class="O"></path><defs><linearGradient id="AU" x1="664.121" y1="492.128" x2="694.718" y2="474.859" xlink:href="#B"><stop offset="0" stop-color="#1a1a1c"></stop><stop offset="1" stop-color="#5a5859"></stop></linearGradient></defs><path fill="url(#AU)" d="M682 461c1-1 3-2 5-3h0c5-2 11-2 15 0 2 1 3 2 4 4 1 3 2 6 3 8-1 3-3 5-6 7h-1c-3 1-4 0-7-2v-2h-1 0-1l-6 3c-5 3-10 9-12 14-1 3-2 7-2 10h0c-1 2-1 4-1 6 1 1 1 1 1 2-3-3-5-7-6-11-1-1-2-3-3-5 0-1 0-1 1-2-1-3-1-7 0-9h0c1-3 2-6 4-8 3-5 8-10 13-12z"></path><path d="M679 475c2 0 3-1 5-2l-1 1-1 2 1 1c-2 2-4 4-5 6-1 0-1 1-2 1l1-4-1-1 1-1c0-1 1-2 2-3z" class="v"></path><defs><linearGradient id="AV" x1="661.944" y1="490.299" x2="691.216" y2="461.774" xlink:href="#B"><stop offset="0" stop-color="#242425"></stop><stop offset="1" stop-color="#444344"></stop></linearGradient></defs><path fill="url(#AV)" d="M682 461h2c3-1 7-3 10-2h0l-2 1c-3 1-6 3-8 4-4 3-6 5-9 8v1c-1 1-1 2-2 2 0 1 0 2-1 2l-1 4c-1 4-1 8-1 12v4c0 1-1 1-1 1v1h-1l-3-9c-1-3-1-7 0-9h0c1-3 2-6 4-8 3-5 8-10 13-12z"></path><path d="M669 498c0-2-1-3 0-5 0-4 0-13 3-16l-1 4c-1 4-1 8-1 12v4c0 1-1 1-1 1z" class="b"></path><defs><linearGradient id="AW" x1="693.049" y1="456.85" x2="698.181" y2="476.974" xlink:href="#B"><stop offset="0" stop-color="#565556"></stop><stop offset="1" stop-color="#7f7a7a"></stop></linearGradient></defs><path fill="url(#AW)" d="M682 461c1-1 3-2 5-3h0c5-2 11-2 15 0 2 1 3 2 4 4 1 3 2 6 3 8-1 3-3 5-6 7h-1c-3 1-4 0-7-2v-2h-1 0v-2l-2 1c-3 1-7 3-9 5l-1-1 1-2 1-1c-2 1-3 2-5 2 2-2 4-4 7-5 2-2 4-2 7-4l-1-1c-3 1-5 0-8 0v-1c2-1 5-3 8-4l2-1h0c-3-1-7 1-10 2h-2z"></path><path d="M686 470h0c2 0 3-1 4-1h1c-2 2-4 5-7 5h-1l1-1c-2 1-3 2-5 2 2-2 4-4 7-5z" class="P"></path><path d="M707 373h0 3 6c-1 2-2 4-4 5v1h4c0 1 0 2 1 3h1c1 0 0 0 1 1h-1c0 1-1 2-2 3 0 1 1 1 1 2h2c0 1-1 2-1 3v3c0 1 1 1 0 2v4h-1c0-1 0-3-1-4l-3-6c-3-2-5-4-8-5-3 0-6 0-9 1-8 4-16 13-19 22-4 8-7 17-10 26 0 3-1 6-2 9s-1 5-2 8h0v6h0-1c-1-1-2-2-4-3h0v-1c-1 0-1-1-2-2l1-2c1-9 4-20 8-28l5-19v-5h1l1-2 1 1v-1l1-1 1-1c0-1 0-1 1-1v-1h2v-1l1-1c1 0 1-1 1-1 2-3 3-7 5-9l2-2c1-2 3-3 5-4h3l3 1 2 1v1h0l3 3c2 0 3 1 5 0l-3-2v-4h2z" class="C"></path><path d="M700 381c1 0 3 1 4 1-1 1-1 1-2 1-5-1-9 2-13 5l-3 2v-1c0-1 1-2 1-2l1-1c3-1 5-3 8-5h4z" class="K"></path><path d="M713 383l5 8v3h-1c0-1 0-2-1-3h-1c-1 0-2-1-2-2-1-1-1-2-2-3v-2c1 0 1-1 2-1z" class="W"></path><path d="M712 379h4c0 1 0 2 1 3h1c1 0 0 0 1 1h-1c0 1-1 2-2 3 0 1 1 1 1 2h2c0 1-1 2-1 3l-5-8c-1-2-1-3-1-4z" class="AE"></path><path d="M708 379h1c0 2 1 4 2 5v2c-2-1-4-3-7-4-1 0-3-1-4-1l-2-2h5c2 0 3 1 5 0z" class="h"></path><path d="M688 384c2-2 5-3 8-3-3 2-5 4-8 5l-1 1s-1 1-1 2v1c-1 2-3 3-5 5v-3l4-6 3-2z" class="l"></path><path d="M672 395l1 1c0 2-1 4-1 6l1-1 1 1-1 1c0 1-1 2-2 3v1c-1 2-2 4-3 5-1 3-2 6-2 9h-1l5-19v-5h1l1-2z" class="K"></path><path d="M707 373h0 3 6c-1 2-2 4-4 5v1c0 1 0 2 1 4-1 0-1 1-2 1-1-1-2-3-2-5h-1l-3-2v-4h2z" class="c"></path><path d="M707 373h3v1c0 1 0 1-1 3l-1-1c-1-1-1-2-1-3z" class="h"></path><path d="M709 379c0-1 0-1 1-2h0l1-1h1v2 1c0 1 0 2 1 4-1 0-1 1-2 1-1-1-2-3-2-5z" class="Z"></path><path d="M657 449l1 1 1-5c0-2 0-4 1-5s1-1 2 0 1 2 2 4v-1h1c-1 3-1 5-2 8h0v6h0-1c-1-1-2-2-4-3h0v-1c-1 0-1-1-2-2l1-2z" class="i"></path><path d="M679 389c1 0 1-1 1-1 2-3 3-7 5-9 1 3-2 5 0 7l-4 6v3c-1 2-1 4-3 4l-3 3-1-1v1l-1-1-1 1c0-2 1-4 1-6v-1l1-1 1-1c0-1 0-1 1-1v-1h2v-1l1-1z" class="s"></path><path d="M673 395l1-1 1-1c0-1 0-1 1-1v-1h2l-2 4c-1 1-1 1-1 2s-1 3-2 4l-1 1c0-2 1-4 1-6v-1z" class="AI"></path><path d="M679 389c1 0 1-1 1-1 2-3 3-7 5-9 1 3-2 5 0 7l-4 6-3 3v1-3l1-4z" class="AC"></path><path d="M695 373l3 1 2 1v1h0l3 3h-5l2 2h-4c-3 0-6 1-8 3l-3 2c-2-2 1-4 0-7l2-2c1-2 3-3 5-4h3z" class="W"></path><path d="M685 379l2-2h6 0l-1 1h-1c0 1-1 1-2 2-1 0-2 1-3 2v1l2 1-3 2c-2-2 1-4 0-7z" class="i"></path><path d="M695 373l3 1 2 1v1h0l3 3h-5-4c-1 0-1 0-2-1l1-1h0-6c1-2 3-3 5-4h3z" class="k"></path><path d="M687 377c1-2 3-3 5-4l-1 1 2 2h3c1 1 1 1 3 1l-1 1h-3l-1 1h0c-1 0-1 0-2-1l1-1h0-6z" class="h"></path><path d="M174 147c1 0 4-1 6-1l2 1c0 2-1 3 1 5h2l1-1h2c3-2 10 1 13 2 16 6 33 16 46 28 4 4 8 8 12 13 7 7 13 16 18 25v3h0 2 2v1c1 3 3 5 4 8 1 2 3 5 4 8l8 20c1 5 3 12 6 16l7 22c1 1 2 4 2 5l4 10c0 2 0 4 1 5v1l1 1v1c1 2 3 3 5 4h-2c-1-1-2-1-2-1-1 1-1 1-1 2-3-1-5-2-7-4-2 1-4 0-6 0h-3l-1 1c0 2 0 2 1 4l1 1h0-2v-1h-1v1c0 2-1 2-2 3v1c1-1 2-1 3-1v1c1 0 2 0 2-1 0 1 1 1 1 1-4 3-7 6-11 9h0l-1-1v-4c2-4 1-11 2-15h-1v-4l-1-1c0-2 0-4-1-6v-1l-1-1c-2-2-4-3-6-5 0-2-4-2-6-4h-1-2c-1 0-2 1-3 1h-1l1-2 3-3v-2l-3 3-1 1h0v-1h-1v-4c0-2 1-3 2-5v-1-1c2-1 4-2 5-3 0-1 0-1-1-2h-1c-1 1-3 2-5 2-2-4-5-8-9-10l-1-1v-1l-1-3c-1-2-2-3-3-4-2 0-3-1-4-1l-1-1v-1c-1-1-2-2-3-2h-1c-1 0-2-1-3-2-10-4-19-7-31-6-3 0-5 0-8 1v1c-11 1-21 8-28 16-2 2-3 4-4 6 0 1-1 2-1 3 0 2 0 3-1 4v-1 3h-1l-1 1v-4h1l-1-1v-3c-1 0-2 1-2 2-1 4-1 9-1 13-2-4-1-11 0-15v-3c0-2 0-3-1-4 1-4 4-8 5-11l-1-1c-2-1-3-2-5-1l-1 1v1h-1c0-1 0-2 1-3v-2-1c-3 4-5 8-5 12-1 0-2-1-2-2l-1 1h0l-1 1v-4c-1-6 3-14 5-20l-1-1c1-5 3-10 6-15 1-2 1-4 2-6h1l2-3a30.44 30.44 0 0 1 8-8c1-3 4-4 6-6v-1c-1 1-2 1-4 1h0l-1 1c-2 1-2 1-4 1-2-2-4-4-6-5h0l-6-6-5-5c-2-3-2-7-3-10 0-6 2-11 4-16 4-6 9-11 16-13z" class="AE"></path><path d="M266 222c1 0 1 0 2 1v2c-2 0-3-1-4-1l2-2z" class="e"></path><path d="M250 203h1l3 3h-8-1c0 1-1 1-2 1h-1-1v-1c3 0 6-2 9-3z" class="S"></path><path d="M279 265c1-2-1-3 2-4 1 0 2 1 3 2 0 2 0 2-1 4h-2v1c-1-1-2-2-2-3z" class="B"></path><path d="M270 255h1l-1-2h2v1h1l2 2h2c0 2 0 0-1 2v2c-1 0-1 0-2-1h0l-1 1c1 1 2 1 3 2h1l-2 1-1-1c-1 0-1-1-2-1v-1h-2-1 0l1-1v-1l-1-1h0l1-1h0v-1z" class="b"></path><path d="M270 256c2 0 2 1 3 2l-1 2h-2-1 0l1-1v-1l-1-1h0l1-1h0z" class="O"></path><path d="M294 285l1-1v3c1 1 1 2 1 4h0 1c0-2 1-4 2-5-1 3-2 6-1 9 1 1 2 1 2 2s1 1 0 2h0l-1-2c-1 0-1 0-2 1h0l-3-3c1-2 0-7 0-10z" class="b"></path><path d="M226 239c-5 0-10 0-16 1v-1c11-1 21-4 31 0h-4 1 0c-2 0-3 0-5 1-1 0-3-1-4-1h-3zm57 32l3-1v1c1 1 2 2 3 2h0l1 1h-2c0 2 1 2 2 3h2c-1 1-1 1-1 3v7c1 0 2-1 3-2 0 3 1 8 0 10l-1 1h0v-3h-1l1-1c0-1-1-3-1-3h-2v-1-1-4l-2 2v-7l-2-1v-1c0-3-1-3-3-5z" class="E"></path><path d="M259 244l2-1h1c1 4-2 7 2 9l1 1c2 1 3 2 5 2v1h0l-1 1h0l1 1h-2c-1 0-2 0-3 1h-1c-2-1-4-3-5-5 0-1 0-2 1-2l1-1v-2l-1-3c-1-1-1-2-1-2z" class="F"></path><path d="M306 309l12 11c1 2 3 3 5 4h-2c-1-1-2-1-2-1-1 1-1 1-1 2-3-1-5-2-7-4-2-4-5-8-5-12z" class="g"></path><path d="M241 239c5 0 14 1 18 5h-1c-1 1-1 1-2 1-3-1-6 0-9-2-3-1-6-1-8-2s-4-1-6-1c2-1 3-1 5-1h0-1 4z" class="O"></path><path d="M208 229l4-2 15-4 2-1c1 0 2-1 3-1h1 2c1-1 3-1 5-1h0 5l-5 2h0l-2 1c-6 0-13 2-18 3-2 1-5 2-7 3h-5z" class="r"></path><defs><linearGradient id="AX" x1="254.522" y1="216.997" x2="249.402" y2="226.699" xlink:href="#B"><stop offset="0" stop-color="#292929"></stop><stop offset="1" stop-color="#535154"></stop></linearGradient></defs><path fill="url(#AX)" d="M245 220c5 0 11-1 16 0l5 2-2 2c-1-1-2-1-2-1-2 1-5 0-6-1l-3 1v-1h-3l2 1c0 1 0 1-1 1h-7c-2 0-4 0-6-1l2-1h0l5-2z"></path><defs><linearGradient id="AY" x1="268.661" y1="226.487" x2="251.82" y2="224.691" xlink:href="#B"><stop offset="0" stop-color="#1e1e1f"></stop><stop offset="1" stop-color="#494749"></stop></linearGradient></defs><path fill="url(#AY)" d="M251 224c1 0 1 0 1-1l-2-1h3v1l3-1c1 1 4 2 6 1 0 0 1 0 2 1 1 0 2 1 4 1v1 1l1 1 1 2c1 0 2 1 3 1v1c-3 0-6-1-9-2l-13-2h0l1-1c-2 0-3 0-5-1h-4v-1h7 5 0c-1 0-2 0-3-1h-1z"></path><path d="M226 239h3c1 0 3 1 4 1 2 0 4 0 6 1s5 1 8 2c3 2 6 1 9 2 1 0 1 0 2-1h1 0s0 1 1 2l1 3v2l-1 1c-1 0-1 1-1 2-1 1-1 2-2 3h0c-1 1-2 1-3 0v-1-1l-1 1-1-1c-2-1-2-2-3-3h3 0c1 0 2 0 2-1v-2c-1-1-3-2-5-3-9-4-17-5-27-6 1 0 3 0 4-1z" class="y"></path><path d="M260 246l1 3v2l-1 1c-1 0-1 1-1 2-1 1-1 2-2 3h0c-1 1-2 1-3 0v-1-1l3-3c0-2 2-4 3-6z" class="AF"></path><path d="M257 252c0 2 0 2-1 3l1 2c-1 1-2 1-3 0v-1-1l3-3z" class="f"></path><path d="M217 183h5 1s1 0 1 1h5 2l1 1-1 1c1 1 5 1 5 1 1 1 1 1 1 2l4-1h0v2h0 1l-3 3h1c1 0 2 1 2 2l1-1h0c2 0 3 0 5-1h1c-1 1-1 1-1 2h1c0 2-1 3-2 4h-4c-1-2 2-2 2-4-1 1-5 1-6 1l-2-2v-1c-1-2-3-2-5-3l-6-1c-3 0-5-1-8-1 1-1 1-1 2-1 1-1 1-1 3-1l-1-1h-3c-1-1-2-1-3-1v-1h1z" class="y"></path><path d="M229 184h2l1 1-1 1c1 1 5 1 5 1 1 1 1 1 1 2-2 0-3 0-5-1h-4l-1-1h-1v-1h2c1-1 1 0 1-2z" class="b"></path><path d="M202 175c3 0 9 0 12 1h0c1 0 1 0 2 1h1l1 1h-1l-1 1-1 1h1c1 0 2 0 3 1h2l2 1h1l-2 1h-5-1v1c1 0 2 0 3 1-2 0-6-1-9 1h0c-1 2-1 2-3 3 0-1 1-2 1-3h-2c-2-2-2-4-3-6 0-2-1-4-1-5z" class="B"></path><path d="M216 183h0c-3 0-4 2-7 0h-1-2 0-1v-2h0c-1-2-1-3-1-4l-1-1c1-1 2 0 3 0l1 1c0 1-1 0 0 2l-1 1v1h4c1-1 2 0 4 0 1 1 2 1 3 2h-1z" class="f"></path><path d="M234 231c7 0 13 1 20 2 2 1 5 1 7 3 2 1 4 3 6 4l8 7-17-8c-10-3-22-6-33-5-4 0-7 1-11 1-1 0-1 0-2 1h0v-1h1c7-2 14-3 21-4z" class="B"></path><defs><linearGradient id="AZ" x1="197.667" y1="234.24" x2="238.959" y2="226.423" xlink:href="#B"><stop offset="0" stop-color="#3a3a3b"></stop><stop offset="1" stop-color="#5c5b5c"></stop></linearGradient></defs><path fill="url(#AZ)" d="M238 223c2 1 4 1 6 1h7 1c1 1 2 1 3 1h0-5-7v1h4c2 1 3 1 5 1l-1 1c-14-1-28 0-42 5-3 2-7 4-10 6h0c-1-1 0-1-1-1l-5 3h-1c1-1 1-2 2-3v-1c1-1 3-2 4-3l10-5h5c2-1 5-2 7-3 5-1 12-3 18-3z"></path><defs><linearGradient id="Aa" x1="232.005" y1="201.204" x2="218.227" y2="191.951" xlink:href="#B"><stop offset="0" stop-color="#464748"></stop><stop offset="1" stop-color="#6d6666"></stop></linearGradient></defs><path fill="url(#Aa)" d="M232 190c2 1 4 1 5 3v1l2 2c1 0 5 0 6-1 0 2-3 2-2 4h4c-1 1-2 3-4 4-1 0-2 2-3 2-2-1-4-1-6-1l-6-1c-2 0-3-1-4-2h-1c-2 0-4 0-6-1v-1h-4 1v-2c2-1 5-1 7-1h3c2-1 6 0 8 0 1 0 2-1 3-1v-1l-3-4z"></path><path d="M214 199h15 1v1l-6 1h-1c-2 0-4 0-6-1v-1h-4 1z" class="L"></path><path d="M229 199l7-1c-1 1-4 2-5 2h2c3 0 6 1 8 3h2c-1 0-2 2-3 2-2-1-4-1-6-1l-6-1c-2 0-3-1-4-2l6-1v-1h-1z" class="D"></path><defs><linearGradient id="Ab" x1="221.606" y1="253.54" x2="224.729" y2="239.782" xlink:href="#B"><stop offset="0" stop-color="#262628"></stop><stop offset="1" stop-color="#656364"></stop></linearGradient></defs><path fill="url(#Ab)" d="M222 240c10 1 18 2 27 6 2 1 4 2 5 3v2c0 1-1 1-2 1h0-3l-8-4c-6-2-12-3-19-4-11 0-20 2-31 6v1l-1-1h1c1-1 0-1 1-1 2-1 5-3 7-4 6-4 15-5 23-5z"></path><path d="M222 244c3-2 8-1 11 0 5 1 11 3 16 6 1 1 2 1 3 2h-3l-8-4c-6-2-12-3-19-4z" class="v"></path><path d="M219 185h3l1 1c-2 0-2 0-3 1-1 0-1 0-2 1 3 0 5 1 8 1l6 1 3 4v1c-1 0-2 1-3 1-2 0-6-1-8 0h-3c-2 0-5 0-7 1v2h-1-4l-1-2h-1-2c-1 0-1 0-2-1l1-2c0-1 1-2 2-4h0 0l1-1c2-1 2-1 3-3h0c3-2 7-1 9-1z" class="D"></path><path d="M226 189l6 1 3 4v1c-1 0-2 1-3 1l1-1v-1c-2-2-2-2-4-2-1 0-2-2-3-3z" class="G"></path><path d="M213 193c1-1 3-1 4-2 2 0 6 1 8 1l2 1c1 0 1 0 2 1-3 0-6-1-9-1v2h-5-3l1-2z" class="p"></path><path d="M219 185h3l1 1c-2 0-2 0-3 1-1 0-1 0-2 1v1c-2 1-4 1-5 4l-1 2-1 1-1-1c0-1 1-2 2-4 1 0 1 0 2-1-2-1-3-1-4-3v-1h0c3-2 7-1 9-1z" class="V"></path><path d="M207 189c2-1 2-1 3-3v1c1 2 2 2 4 3-1 1-1 1-2 1-1 2-2 3-2 4l-2 2h-1-2c-1 0-1 0-2-1l1-2c0-1 1-2 2-4h0 0l1-1z" class="H"></path><path d="M204 194c1 0 2-1 3-1l2-1c1-1 2-1 3-1-1 2-2 3-2 4l-2 2h-1-2c-1 0-1 0-2-1l1-2z" class="L"></path><path d="M176 187l-2-1-3-3v-1c-1-1-1-1-1-2v-7-1h0v-1c1-3 3-5 6-7 1-1 2-2 4-3 4-1 10 0 13 2l4 2h0l3 1v1c2 2 5 2 8 2 1 0 0 1 2 1 0 1 1 1 2 1l-4 1h0c3 0 4 0 7 1v1l-1 1h1c3 0 3 1 6 3-2 0-3 1-4 1h-1l1-1h1l-1-1h-1c-1-1-1-1-2-1h0c-3-1-9-1-12-1l-1-2c-2-2-2-3-4-3l-1 1c-1-1-1-2-2-3-3-3-8-4-12-3-3 0-6 2-8 5-1 1-1 2-2 3 0 2 0 4 1 6h1 0v-1c0-1 0-2 1-4 0 1-1 3 0 4 0 1 2 3 2 4 1 0 1 1 2 1h0l1 1h4v2s-1 0-1 1h-2-5z" class="E"></path><path d="M192 165c3 0 4 0 6 2l1 1h0l4 2 2 1v1c-2 0-3 0-4 1-2-2-2-3-4-3-1-2-3-3-5-5z" class="B"></path><path d="M176 187l-2-2c-3-2-3-5-3-8-1-5 1-8 4-12h3c6-2 9-2 14 0 2 2 4 3 5 5l-1 1c-1-1-1-2-2-3-3-3-8-4-12-3-3 0-6 2-8 5-1 1-1 2-2 3 0 2 0 4 1 6h1 0v-1c0-1 0-2 1-4 0 1-1 3 0 4 0 1 2 3 2 4 1 0 1 1 2 1h0l1 1h4v2s-1 0-1 1h-2-5z" class="H"></path><defs><linearGradient id="Ac" x1="188.258" y1="177.088" x2="179.215" y2="181.403" xlink:href="#B"><stop offset="0" stop-color="#5a5554"></stop><stop offset="1" stop-color="#777474"></stop></linearGradient></defs><path fill="url(#Ac)" d="M174 170c2-3 5-5 8-5 4-1 9 0 12 3 1 1 1 2 2 3 1 4 1 7-1 11v1c-2 0-2 0-3-1l-1 1v1l2 2-3 4c-1-1-3-3-6-4v-2h-4l-1-1h0c-1 0-1-1-2-1 0-1-2-3-2-4-1-1 0-3 0-4-1 2-1 3-1 4v1h0-1c-1-2-1-4-1-6 1-1 1-2 2-3z"></path><path d="M176 172h0c1 1 1 0 2 1v-1l1 1-1 1c-1 1-1 4 0 6 0 1 1 2 1 3h0c-1 0-1-1-2-1 0-1-2-3-2-4-1-1 0-3 0-4l1-2z" class="M"></path><path d="M181 173h3c1 0 1 1 2 2h-1l-4 2v1l-1 2h-1c0-1-1-3 0-5 0-1 1-2 2-2z" class="H"></path><path d="M174 170c3 0 5-2 8 0 0 0 1-1 2-1 4 0 5 3 8 6 0 1 1 3 0 4h-2 0c-1-2 0-1-1-2 0-1-1-1-2-2 0-1-1-2-2-3h-1c-2-1-3 0-5-2-2 0-2 1-3 2l-1 2c-1 2-1 3-1 4v1h0-1c-1-2-1-4-1-6 1-1 1-2 2-3z" class="AB"></path><path d="M174 170c2-3 5-5 8-5 4-1 9 0 12 3 1 1 1 2 2 3 1 4 1 7-1 11v1c-2 0-2 0-3-1l-3-1v-1c1 0 2 0 2-1h1c1-1 0-3 0-4-3-3-4-6-8-6-1 0-2 1-2 1-3-2-5 0-8 0z" class="b"></path><defs><linearGradient id="Ad" x1="169.914" y1="173.29" x2="157.303" y2="180.202" xlink:href="#B"><stop offset="0" stop-color="#2e2e2f"></stop><stop offset="1" stop-color="#4b4a4c"></stop></linearGradient></defs><path fill="url(#Ad)" d="M154 176c0-6 2-11 4-16 4-6 9-11 16-13l-1 1c-1 1-3 2-5 3-3 1-6 5-7 7 1 2 1 2 1 4h2l1 1c0-1 1-2 1-3h0l2-2 1-2 1 1h0c-3 2-3 4-4 7 0 1-1 2-1 3v1l-1 1 2 2c0-1 0-1 1-1l1-1c0-1 1-2 1-2 1-1 0-1 1-1 0 2-3 5-3 8v1 2 2c0 3 2 7 4 9 2 1 3 2 5 2l1 1h3c1-1 2-2 1-3v-1h2c1 2 1 4 2 6 0 2 0 3-1 4-2 1-3 2-5 3h0l-1 1c-2 1-2 1-4 1-2-2-4-4-6-5h0l-6-6-5-5c-2-3-2-7-3-10z"></path><path d="M157 180c0-3-1-8 1-10h1l-1 4c1 3 1 6 1 8h-1l-1-2z" class="B"></path><path d="M165 188v-1c0-1-1-2-1-3h1 0l4 5c2 3 6 3 8 4h1 3c1 0 1 0 2 1l2-1c0 2 0 3-1 4-2 1-3 2-5 3h0l-1 1c-3-1-6-3-8-5v-1l-1-1 1-1-3-3c-1 0-1-1-2-2z" class="v"></path><path d="M170 195l-1-1 1-1c2 1 4 2 7 3 2 0 4 0 7 1-2 1-3 2-5 3h0l-1 1c-3-1-6-3-8-5v-1z" class="G"></path><path d="M170 195c2 1 3 1 5 2l3 1c1 1 1 1 1 2l-1 1c-3-1-6-3-8-5v-1z" class="r"></path><path d="M158 174c1 1 2 2 2 3 1 3 3 9 5 11 1 1 1 2 2 2l3 3-1 1 1 1v1c2 2 5 4 8 5-2 1-2 1-4 1-2-2-4-4-6-5h0l-6-6-5-5c-2-3-2-7-3-10 3 1 2 1 3 4l1 2h1c0-2 0-5-1-8z" class="P"></path><path d="M158 182h1c1 2 2 5 2 7h0c-2-2-3-5-3-7z" class="r"></path><path d="M162 191h1v-2h0 1c1 2 4 5 5 7h1c2 2 5 4 8 5-2 1-2 1-4 1-2-2-4-4-6-5h0l-6-6z" class="G"></path><path d="M257 257c1-1 1-2 2-3 1 2 3 4 5 5h1c1-1 2-1 3-1h2v1l-1 1h0 1 2v1c1 0 1 1 2 1l1 1 2-1 2 3c0 1 1 2 2 3h0l2 3c2 2 3 2 3 5v1l2 1v7l2-2v4 1l-1-2h-1l-3 3c-3 0-8 1-10 3l-3 3-1 1h0v-1h-1v-4c0-2 1-3 2-5v-1-1c2-1 4-2 5-3 0-1 0-1-1-2h-1c-1 1-3 2-5 2-2-4-5-8-9-10l-1-1v-1l-1-3c-1-2-2-3-3-4-2 0-3-1-4-1l-1-1v-1l2-3 1-1v1 1c1 1 2 1 3 0h0z" class="E"></path><path d="M266 264c1 1 3 1 4 2h1l-4 1c-1 0-1-1-2-1h-2c-1 0-2 0-3-1l6-1z" class="x"></path><path d="M260 270h3l2 2c2 1 3 2 4 3h1 1l-1 1 1 1c1 0 1 1 2 0 1 2 1 2 2 2-1 1-3 2-5 2-2-4-5-8-9-10l-1-1z" class="F"></path><path d="M263 266h2c1 0 1 1 2 1l4-1 1 1c1 1 3 2 3 3 1 2 1 3 2 4-1 1 0 1-2 1-3-4-7-7-12-9z" class="V"></path><path d="M257 257c1-1 1-2 2-3 1 2 3 4 5 5h1c1-1 2-1 3-1l-2 3c-3 1-5 3-9 1h-1c-2 0-3-1-4-1l-1-1v-1l2-3 1-1v1 1c1 1 2 1 3 0h0z" class="B"></path><path d="M254 255v1 1c1 1 2 1 3 0h0c1 1 2 1 3 3l-2 1h0c-2-1-4-1-6 0l-1-1v-1l2-3 1-1z" class="AB"></path><path d="M274 262l1 1 2-1 2 3c0 1 1 2 2 3h0v2c1 2 3 3 2 4s-1 1-1 2l1 3h0c-1 1-1 2-1 3h-1c0 1 0 1-1 2l1 1-2-1c-2 0-3 0-6 1h0-1v-1c2-1 4-2 5-3 0-1 0-1-1-2h-1c-1 0-1 0-2-2-1 1-1 0-2 0l-1-1 1-1 3 2h1v-2c2 0 1 0 2-1-1-1-1-2-2-4 0-1-2-2-3-3l-1-1h-1c-1-1-3-1-4-2l2-1c2-1 4-1 6-1z" class="AF"></path><path d="M274 262l1 1 2 6-6-6c-2 1-2 1-3 0 2-1 4-1 6-1z" class="G"></path><path d="M276 279h2 1l-1-1h-1l-1-1c1-1 1 0 2-1l1 1 1-1c1 1 1 1 2 0l1 3h0c-1 1-1 2-1 3h-1c0 1 0 1-1 2l1 1-2-1c-2 0-3 0-6 1h0-1v-1c2-1 4-2 5-3 0-1 0-1-1-2z" class="B"></path><defs><linearGradient id="Ae" x1="286.724" y1="277.877" x2="279.905" y2="280.893" xlink:href="#B"><stop offset="0" stop-color="#3d3a3a"></stop><stop offset="1" stop-color="#57514f"></stop></linearGradient></defs><path fill="url(#Ae)" d="M281 268l2 3c2 2 3 2 3 5v1l2 1v7l2-2v4 1l-1-2h-1l-3 3c-3 0-8 1-10 3l-3 3-1 1h0v-1h-1v-4c0-2 1-3 2-5v-1h1 0c3-1 4-1 6-1l2 1-1-1c1-1 1-1 1-2h1c0-1 0-2 1-3h0l-1-3c0-1 0-1 1-2s-1-2-2-4v-2z"></path><path d="M272 285h1 0c3-1 4-1 6-1l2 1c-1 1 0 1-1 2h0c-2 1 0 1-2 1s-5 3-6 5v2l-1 1h0v-1h-1v-4c0-2 1-3 2-5v-1z" class="I"></path><path d="M272 285h1 0c3-1 4-1 6-1-1 2-2 2-3 3s-2 1-2 2c-1-1-2-2-2-3v-1z" class="u"></path><defs><linearGradient id="Af" x1="309.091" y1="315.105" x2="287.644" y2="314.66" xlink:href="#B"><stop offset="0" stop-color="#050303"></stop><stop offset="1" stop-color="#343535"></stop></linearGradient></defs><path fill="url(#Af)" d="M288 286h1l1 2v1h2s1 2 1 3l-1 1h1v3h0l1-1 3 3h0c1-1 1-1 2-1l1 2h0c1 1 1 3 2 4s4 4 4 6c0 4 3 8 5 12-2 1-4 0-6 0h-3l-1 1c0 2 0 2 1 4l1 1h0-2v-1h-1v1c0 2-1 2-2 3v1c1-1 2-1 3-1v1c1 0 2 0 2-1 0 1 1 1 1 1-4 3-7 6-11 9h0l-1-1v-4c2-4 1-11 2-15h-1v-4l-1-1c0-2 0-4-1-6v-1l-1-1c-2-2-4-3-6-5 0-2-4-2-6-4h-1-2c-1 0-2 1-3 1h-1l1-2 3-3v-2c2-2 7-3 10-3l3-3z"></path><path d="M272 297h1c1 0 1 0 2-1 2-1 4-1 6-1 4 1 8 4 10 8v4 1l-1-1c-2-2-4-3-6-5 0-2-4-2-6-4h-1-2c-1 0-2 1-3 1h-1l1-2z" class="D"></path><path d="M288 286h1l1 2v1h2s1 2 1 3l-1 1h1v3h0l-2 2-3-3h-2c-2 0-3-1-4-2h-5l-2 1v-2c2-2 7-3 10-3l3-3z" class="Y"></path><path d="M288 286v2h1v2h1c0 1 1 1 1 2h0-3v1h-1v-1h-2-1l-2-1h-3l-2 2-2 1v-2c2-2 7-3 10-3l3-3z" class="H"></path><path d="M291 303c2 2 2 4 3 6v1h0v3c1 2 0 6 1 9 1 4 0 9-1 13 2-1 4-3 4-4 1-1 2-1 3-1v1c1 0 2 0 2-1 0 1 1 1 1 1-4 3-7 6-11 9h0l-1-1v-4c2-4 1-11 2-15h-1v-4l-1-1c0-2 0-4-1-6v-1-1-4z" class="B"></path><defs><linearGradient id="Ag" x1="234.462" y1="235.629" x2="173.205" y2="287.926" xlink:href="#B"><stop offset="0" stop-color="#050505"></stop><stop offset="1" stop-color="#262525"></stop></linearGradient></defs><path fill="url(#Ag)" d="M194 237v1c-1 1-1 2-2 3h1l5-3c1 0 0 0 1 1h0c-4 3-9 6-12 10l-1 1-2 3h0c1 0 2-1 3-2 1 0 1-1 2-1h1c0-1 1-1 2-1-1 0 0 0-1 1h-1l1 1v-1c11-4 20-6 31-6 7 1 13 2 19 4l8 4c1 1 1 2 3 3l1 1-2 3c-1-1-2-2-3-2h-1c-1 0-2-1-3-2-10-4-19-7-31-6-3 0-5 0-8 1v1c-11 1-21 8-28 16-2 2-3 4-4 6 0 1-1 2-1 3 0 2 0 3-1 4v-1 3h-1l-1 1v-4h1l-1-1v-3c-1 0-2 1-2 2-1 4-1 9-1 13-2-4-1-11 0-15v-3c0-2 0-3-1-4 1-4 4-8 5-11l-1-1 1-1h0l3-1v2h1c1-3 2-5 4-7v1c-1 1-1 2-1 3v1l5-9 3-3c0 2-1 2-1 4 0-1 1-3 3-3l1-1h1l1-1c1-1 3-2 4-4z"></path><path d="M241 248l8 4c1 1 1 2 3 3l1 1-2 3c-1-1-2-2-3-2h-1c-1 0-2-1-3-2h1s1 0 2 1h1 0 1 2v-1c-2 0-4-1-6-3-1 0-1-1-2-2-1 0-1-1-2-1v-1z" class="b"></path><path d="M194 237v1c-1 1-1 2-2 3h1l5-3c1 0 0 0 1 1h0c-4 3-9 6-12 10-3 2-6 5-7 8l-3 5h0-1v-2l1-1c0-1 1-2 2-3 0-2 2-3 3-5l3-3c-1-1-1-1-1-2s1-3 3-3l1-1h1l1-1c1-1 3-2 4-4z" class="O"></path><path d="M173 256h1c1-3 2-5 4-7v1c-1 1-1 2-1 3v1c-3 5-5 10-6 16-1 1-2 5-2 5-1 0-2 1-2 2-1 4-1 9-1 13-2-4-1-11 0-15v-3c0-2 0-3-1-4 1-4 4-8 5-11l-1-1 1-1h0l3-1v2z" class="V"></path><path d="M170 255h0l3-1v2c-4 4-5 13-7 19v-3c0-2 0-3-1-4 1-4 4-8 5-11l-1-1 1-1z" class="f"></path><path d="M197 170c2 0 2 1 4 3l1 2c0 1 1 3 1 5 1 2 1 4 3 6h2c0 1-1 2-1 3l-1 1h0 0c-1 2-2 3-2 4l-1 2c1 1 1 1 2 1h2 1l1 2h4 4v1c-2 1-3 1-4 1-2 1-3 1-5 1-1 0-2 1-4 1-3 1-8 4-11 6-1 1-1 1-2 1l-1 1h-1c-1 0-2 1-3 1-2 1-3 2-5 3l-2 2-3 1-1-1-2 2v-2c-6 7-10 14-14 22l-1-1c1-5 3-10 6-15 1-2 1-4 2-6h1l2-3a30.44 30.44 0 0 1 8-8c1-3 4-4 6-6v-1c-1 1-2 1-4 1 2-1 3-2 5-3 1-1 1-2 1-4-1-2-1-4-2-6 0-1 1-1 1-1 3 1 5 3 6 4l3-4-2-2v-1l1-1c1 1 1 1 3 1v-1c2-4 2-7 1-11l1-1z" class="X"></path><path d="M195 195l1-1 2 2-2 1h-2 0l1-2zm2-25c2 0 2 1 4 3l1 2c0 1 1 3 1 5h0v3c-1 0-1-1-1-1h-1c0-1 1-1 1-3-1 0-1-1-2-2 0-1-1-2-1-3l-2-4z" class="H"></path><path d="M183 207c2 0 3-1 5-1-2 2-4 3-7 5 1 1 1 1 1 2-1 1-1 1-1 2l-2 2-3 1-1-1-2 2v-2s1-1 1-2c3-3 6-5 9-8z" class="Y"></path><path d="M175 217l6-6c1 1 1 1 1 2-1 1-1 1-1 2l-2 2-3 1-1-1z" class="M"></path><path d="M183 207c1-1 3-2 5-4 0 0 0-1 1-1 2 0 12-4 13-4 0-1 0-1 1-2 1 1 1 1 2 1h2 1l1 2c-4 1-8 2-13 4-2 1-5 2-8 3-2 0-3 1-5 1z" class="n"></path><path d="M197 170l2 4c0 4-1 5-2 9l-1 3v1c-2 4-5 7-8 10v-1h0c1-1 1-2 1-3h0l-2 2-4 4c-1 1-2 1-4 1 2-1 3-2 5-3 1-1 1-2 1-4-1-2-1-4-2-6 0-1 1-1 1-1 3 1 5 3 6 4l3-4-2-2v-1l1-1c1 1 1 1 3 1v-1c2-4 2-7 1-11l1-1z" class="L"></path><defs><linearGradient id="Ah" x1="198.746" y1="211.304" x2="198.249" y2="200.064" xlink:href="#B"><stop offset="0" stop-color="#aca5a3"></stop><stop offset="1" stop-color="#cdcbca"></stop></linearGradient></defs><path fill="url(#Ah)" d="M209 199h4 4v1c-2 1-3 1-4 1-2 1-3 1-5 1-1 0-2 1-4 1-3 1-8 4-11 6-1 1-1 1-2 1l-1 1h-1c-1 0-2 1-3 1-2 1-3 2-5 3 0-1 0-1 1-2 0-1 0-1-1-2 3-2 5-3 7-5 3-1 6-2 8-3 5-2 9-3 13-4z"></path><path d="M186 212c1 0 2-1 3-1h1l1-1c1 0 1 0 2-1 3-2 8-5 11-6 2 0 3-1 4-1 2 0 3 0 5-1 1 0 2 0 4-1 2 1 4 1 6 1h1c1 1 2 2 4 2l6 1c2 0 4 0 6 1-1 1-2 2-3 2l-1 1h1c1 0 6-1 7 0-2 2-5 1-8 2h0l3 1 13-1 5 7c-24 1-46 3-65 18l-4 4-3 3-3 3-5 9v-1c0-1 0-2 1-3v-1c-2 2-3 4-4 7h-1v-2l-3 1h0l-1 1c-2-1-3-2-5-1l-1 1v1h-1c0-1 0-2 1-3v-2-1c-3 4-5 8-5 12-1 0-2-1-2-2l-1 1h0l-1 1v-4c-1-6 3-14 5-20 4-8 8-15 14-22v2l2-2 1 1 3-1 2-2c2-1 3-2 5-3z" class="Q"></path><path d="M198 217h0l1 1-1 1 1 1c2-1 4-1 7-1-2 1-3 1-4 2s-1 2-2 2v1h0c-4 1-10 7-11 10v2h1l2-1-4 4c0-1-1-2-1-3h0v-2h-1v-1c1-3 4-4 5-6l1-1c-2 1-3 2-4 2h0v-2c2-2 5-3 5-5l1-1c1 0 1 0 2-1l1-2h1z" class="d"></path><path d="M198 217h0l1 1-1 1 1 1c2-1 4-1 7-1-2 1-3 1-4 2-4 1-7 3-10 5-2 1-3 2-4 2h0v-2c2-2 5-3 5-5l1-1c1 0 1 0 2-1l1-2h1z" class="P"></path><path d="M218 208h1v3c2 1 5 0 7-1l10-2h1c1 0 6-1 7 0-2 2-5 1-8 2h0l3 1c-12 1-22 5-33 8-3 0-5 0-7 1l-1-1 1-1-1-1h0 3c1 0 16-5 17-5 0-2-1-3 0-4z" class="O"></path><path d="M186 212c1 0 2-1 3-1h1l1-1c1 0 1 0 2-1 3-2 8-5 11-6 2 0 3-1 4-1 2 0 3 0 5-1 1 0 2 0 4-1 2 1 4 1 6 1h1c1 1 2 2 4 2l6 1c2 0 4 0 6 1-1 1-2 2-3 2l-1 1-10 2c-2 1-5 2-7 1v-3h-1c-1 1 0 2 0 4-1 0-16 5-17 5h-3 0 0-1l-1 2c-1 1-1 1-2 1l-1 1c0 2-3 3-5 5l-3 1h-4l-4 3-2-1c1-1 1-2 1-3h2v-1l-2-2v1l-2-2c1 0 2-1 3-2s2-2 2-3l2-2c2-1 3-2 5-3z" class="L"></path><path d="M178 220c3-2 6-4 10-4-1 0-2 2-2 2-2 1-6 2-8 2z" class="M"></path><path d="M197 217c-1 0-2 0-4 1h0l-1-1h1l-1-1c1-1 3-1 4-2l1 1h-1l2 2h-1z" class="Q"></path><path d="M186 212h0 1c3 0 6-2 9-2h0c2 0 2 0 3 1-4 0-7 3-11 5-4 0-7 2-10 4h-1c1-1 2-2 2-3l2-2c2-1 3-2 5-3z" class="u"></path><path d="M176 223h0c3-2 13-5 17-4h3c-1 1-1 1-2 1l-1 1c0 2-3 3-5 5l-3 1h-4l-4 3-2-1c1-1 1-2 1-3h2v-1l-2-2z" class="y"></path><path d="M178 225v-1c3 1 5 0 7-1l-4 4-4 3-2-1c1-1 1-2 1-3h2v-1z" class="B"></path><path d="M185 223c3 0 6-2 8-2 0 2-3 3-5 5l-3 1h-4l4-4z" class="Y"></path><path d="M186 212c1 0 2-1 3-1h1l1-1c1 0 1 0 2-1 3-2 8-5 11-6 2 0 3-1 4-1 2 0 3 0 5-1 1 0 2 0 4-1 2 1 4 1 6 1h1c1 1 2 2 4 2l6 1c2 0 4 0 6 1-1 1-2 2-3 2l-1 1-10 2c-2 1-5 2-7 1v-3h-1l1-2c-1 0-2 0-3 1l-1 2h0c-1 0-2 1-3 1-4 2-10 2-14 4h0v-1c1-1 2-1 4-1-1-1-2-1-3-1-1-1-1-1-3-1h0c-3 0-6 2-9 2h-1 0z" class="M"></path><path d="M223 201h1c1 1 2 2 4 2l6 1c2 0 4 0 6 1-1 1-2 2-3 2s-1-1-1-1c-1-1-4 0-5 0-3 1-8 1-11-1-1-1-1-1-1-3l4-1z" class="I"></path><path d="M175 217l1 1 3-1c0 1-1 2-2 3s-2 2-3 2l2 2v-1l2 2v1h-2c0 1 0 2-1 3l2 1 4-3h4l3-1v2h0c1 0 2-1 4-2l-1 1c-1 2-4 3-5 6v1h1v2h0c0 1 1 2 1 3l-3 3-3 3-5 9v-1c0-1 0-2 1-3v-1c-2 2-3 4-4 7h-1v-2l-3 1h0l-1 1c-2-1-3-2-5-1l-1 1v1h-1c0-1 0-2 1-3v-2-1c-3 4-5 8-5 12-1 0-2-1-2-2l-1 1h0l-1 1v-4c-1-6 3-14 5-20 4-8 8-15 14-22v2l2-2z" class="H"></path><path d="M188 228c1 0 2-1 4-2l-1 1c-1 2-4 3-5 6v1h1v2h0c0 1 1 2 1 3l-3 3-3 3c-3 0-4 2-6 4 2-3 3-7 6-9v-1l-2 1c-4 1-5 5-8 7l-1-1 4-4-1-1c1 0 2-1 2-2v-1c2-2 4-4 6-5s4-3 6-5z" class="X"></path><defs><linearGradient id="Ai" x1="172.201" y1="249.249" x2="164.918" y2="240.282" xlink:href="#B"><stop offset="0" stop-color="#7c7877"></stop><stop offset="1" stop-color="#ada8a7"></stop></linearGradient></defs><path fill="url(#Ai)" d="M185 227l3-1v2h0c-2 2-4 4-6 5s-4 3-6 5c-3 2-6 5-7 9-1 2-4 4-4 6 1 1 4 1 5 2l-1 1c-2-1-3-2-5-1l-1 1v1h-1c0-1 0-2 1-3v-2-1c-3 4-5 8-5 12-1 0-2-1-2-2 2-7 4-14 8-19s8-8 13-12l4-3h4z"></path><path d="M185 227l3-1v2h0c-2 2-4 4-6 5s-4 3-6 5c-3 2-6 5-7 9-1 2-4 4-4 6 1 1 4 1 5 2l-1 1c-2-1-3-2-5-1l-1 1v1h-1c0-1 0-2 1-3v-2-1c0-1 2-3 2-4a188.83 188.83 0 0 1 20-20z" class="G"></path><path d="M175 217l1 1 3-1c0 1-1 2-2 3s-2 2-3 2l2 2v-1l2 2v1h-2c0 1 0 2-1 3l2 1c-5 4-9 7-13 12s-6 12-8 19l-1 1h0l-1 1v-4c-1-6 3-14 5-20 4-8 8-15 14-22v2l2-2z" class="f"></path><path d="M175 217l1 1 3-1c0 1-1 2-2 3s-2 2-3 2l2 2v-1l2 2v1h-2c0 1 0 2-1 3-3 2-4 2-7 2 0 0-1-1-2-1l-3 4c2-5 6-11 10-15l2-2z" class="H"></path><path d="M176 224c-1 0-1 1-1 1h-3v-1l2-2 2 2z" class="X"></path><path d="M166 230c4-2 7-3 10-4 0 1 0 2-1 3-3 2-4 2-7 2 0 0-1-1-2-1z" class="O"></path><defs><linearGradient id="Aj" x1="333.067" y1="387.533" x2="245.421" y2="413.418" xlink:href="#B"><stop offset="0" stop-color="#0f0909"></stop><stop offset="1" stop-color="#363a3c"></stop></linearGradient></defs><path fill="url(#Aj)" d="M284 172l7 4c1 1 3 3 5 3a30.44 30.44 0 0 1 8 8c6 7 9 17 13 25 4 4 5 8 8 12l2 4c0-1 1-2 1-3v-1l2-1c1 3 2 6 4 9h0l5 11 2 7 1 1c0 4 1 9 3 13v5l-1 2 6 15 10 22v1h1v-1c2 4 3 8 6 11 1 0 0 0 1-1 1 2 2 3 3 5h2v-1l3-2c0-1 1-3 1-5l1 1c1 0 1 1 1 1 1 0 2-1 3-1h3 0 2v-2l1-1 1 1 4-2h1c1 1 1 2 2 2 1-1 0-1 1-2h2c-1 1-1 2-1 3l1 1 1-1 2 3h-2l1 3v1c2 1 3 1 6 1l3 2v-2l3 2 4 2c2 2 5 4 7 7 1 0 2 1 3 2 1 0 2 1 2 2 0 3 1 4 2 7h0c2 5 6 10 8 15l1 5c1 2 3 3 4 5h0l1 1c1 2 2 4 3 7 0 1 2 3 1 4h0l-2-1-1 1c-1 1-1 2-1 3l1 2-1 2c1 2 1 3 0 6-1-1-1-2-2-2l-2 6c1 2 2 5 3 8h0c2 3 4 5 7 7 1 1 1 2 2 3l3 1 2 2h3c-1 1-3 2-4 2l-1-2c-2 1-2 1-3 1h-1-1-4l-1 1c-2 3-5 4-7 9-1 1-2 3-2 5l-1 2v1c-2-1-2-2-2-3v-5l-3 6c0 3-1 5-1 7l-1 3h-1c0 2 0 3-1 4l-1 1c-1-1-1-3-2-4v3l1 1-1 1v2h0-1c-1 1-1 0-1 1 1 1 0 1 1 2v4l1 1v2h1v2l1 1v1l-1 1-7-17v6l1 3c-1-1-3-4-5-5l-2-2c-1 1-3 2-4 2 0 2-1 4-1 6l-1-1v1c-1 1-1 2-1 3 0-1 0-1-1-2v-1l1-1c0-1 1-5 0-7s-2-4-2-6c0-1-2-2-2-2-2-2-3-3-5-4h1l2 1 1-1-1-1-1-1c-2-1-2-1-3-2h-2l-1-2 1-1c1 0 1 1 2 1h0v-2l-1-1h0c0-4-2-5-5-7l1-1c2 1 3 2 4 4v-4l-1-1v-1c0-1-1-1-1-2-1-1-2-3-2-4l-6-5-21-12c-6-3-14-6-21-4h-2-1 0c-1-2-3-2-5-2h-1c-2 0-4 0-5 2-2 1-3 3-3 5h0v2 1 1l2 2v2l-1 1s-1 0-2 1h0c-1 0-2 0-3 1l-3 2c1 0 1 1 1 2v2c1 0 2 0 3 1v5c1 0 1 1 1 2s1 2 1 3l-4 3v2h0l-1-1c-3 0-4 0-6-2-1-1-1-3-1-5-1 1-1 3-3 3h0v-1h0l-2 2c-1 2-1 4 0 6 1 1 2 3 3 4 3 1 5 2 8 1 0 1 1 1 2 2 3-1 7-2 10-4h3l3 5-9 6c-2 2-5 6-6 9l1 1h0c0 2 0 4 1 6v3h0c-1 1-1 1-1 2s0 1-1 2l-1-2-1 1h0l-1-2-4-7c0 3 1 7 0 10h-1c-2 5-7 9-11 12-3 1-6 1-9 1-7 1-12 0-18-2-2-1-3-2-5-2l1-1-2-1c-1-1-7-5-8-6l-2-2c1-1 2-1 3-1s1-1 1-1c-3-2-5-4-8-8h-1c-2-3-3-6-4-10v-14c1-4 1-8 1-12l-1-1c0 1 0 2-1 4v2-3-1c-1 1-1 2-1 4h-1v-1c-1 3-1 5-2 7h0v-2h-1v-6c-1 1-1 3-2 5 0 2 1 3 0 5l-1-1v-17l2-10-1-1-1-1h-2l-1-3v-1-8h0c3-12 9-26 17-35 4-4 9-7 13-11l6-3c2-1 4-2 5-3h0c1-1 2-1 3-1h2c1-1 1-1 2 0h1l1-1 2-1 3-3c4-3 7-6 11-9 0 0-1 0-1-1 0 1-1 1-2 1v-1c-1 0-2 0-3 1v-1c1-1 2-1 2-3v-1h1v1h2 0l-1-1c-1-2-1-2-1-4l1-1h3c2 0 4 1 6 0 2 2 4 3 7 4 0-1 0-1 1-2 0 0 1 0 2 1h2c-2-1-4-2-5-4v-1l-1-1v-1c-1-1-1-3-1-5l-4-10c0-1-1-4-2-5l-7-22c-3-4-5-11-6-16l-8-20c-1-3-3-6-4-8-1-3-3-5-4-8v-1h-2-2l1-1c1-1 6-2 8-3s4-2 7-4h2c3-2 4-4 6-7l1 1h0c0-4 0-7-1-11-1-1-2-3-3-5l-2-2-2-3c-1-2-2-4-4-5 0-2 0-2 1-3l-3-4c-2-1-3-2-4-3z"></path><path d="M319 408c0-1 0-2 1-3v-1-1-1c1 1 2 2 2 4-1 1-1 2-3 2z" class="S"></path><path d="M322 407h1c0 2 1 2 2 4h1s-1 0-2 1h0c-1 0-2 0-3 1v-1-1h1c1-2 0-3 0-4z" class="b"></path><path d="M287 378c8-1 14-2 22 0l3 1 3 2 3 3-2 4v-1-3l-2-2c0-1-1-2-3-3h-4c-1 0-1-1-2-1h-1-2c-3 0-6-1-8 0h-1-1c-2 0-3 1-4 0h-1z" class="AE"></path><path d="M329 391h7l-3 4c-2 0-4 0-5 2-2 1-3 3-3 5v-4c0-3 2-5 4-7z" class="g"></path><path d="M314 406c2 0 3 0 4 1v2l1 1v-2c2 0 2-1 3-2v1c0 1 1 2 0 4h-1v1 1l-3 2c1 0 1 1 1 2h-1l-2 3h-1v-3-3-1c0-3-1-5-1-7z" class="E"></path><path d="M325 386c3-1 5-1 8 1 3 1 4 2 7 3h0 1c2 0 3 1 4 1l2 1v1h-1c0 1-1 1-2 1v1h0c-2 0-3 0-4 1l-1 1h0c-1-2-3-2-5-2h-1l3-4h-7l2-1h3c-1-1-1-2-1-3-3-1-5-1-8-1z" class="AI"></path><path d="M336 391l3 1c3 1 5 1 8 1h-1c0 1-1 1-2 1v1h0c-2 0-3 0-4 1l-1 1h0c-1-2-3-2-5-2h-1l3-4z" class="l"></path><path d="M336 391l3 1c-2 2-2 2-5 3h-1l3-4z" class="s"></path><path d="M330 370l4-1v3c0 1-2 2-2 3l1 1c-1 1-1 1-2 1l-2 1c4 7 12 8 17 13h-1c-1 0-2-1-4-1h-1 0c-3-1-4-2-7-3-3-2-5-2-8-1-4 1-6 3-8 6l-1-1v-3l2-4-3-3-3-2-4-8c1-1 9 2 11 3 3 1 6 1 9 2 1-2 1-4 2-5v-1z" class="l"></path><path d="M315 381c1 0 3 0 4 1 0 1 0 2-1 2l-3-3z" class="AK"></path><defs><linearGradient id="Ak" x1="253" y1="398.999" x2="266.941" y2="414.384" xlink:href="#B"><stop offset="0" stop-color="#222020"></stop><stop offset="1" stop-color="#616163"></stop></linearGradient></defs><path fill="url(#Ak)" d="M284 378h2l-3 1c-3 1-6 3-9 5-2 3-5 5-7 8l-1 1h1 0c1-1 1-1 2-1l3-3c2-2 3-3 6-3-2 1-4 3-6 4-3 2-8 6-8 9-1 2-3 5-4 8l-9 20-1-1c0 1 0 2-1 4v2-3-1c-1 1-1 2-1 4h-1v-1c0-3 0-7 1-10 4-17 12-33 27-42l-2 3c1 0 2-1 2-1h1 0c1-1 3-1 4-2s3-1 4-1z"></path><path d="M284 368h5c2 0 3-1 4-1 3 0 5 1 8 2h5l2 2 4 8-3-1c-8-2-14-1-22 0h-1-2c-1 0-3 0-4 1s-3 1-4 2h0-1s-1 1-2 1l2-3 6-3c-3 0-4 0-5 1-2 1-2 1-4 1-1 0-2 1-3 1l2-3 3-3 10-5z" class="b"></path><path d="M284 378c2-2 3-2 5-3h1c4-2 11-4 16-2 2 1 3 2 4 4l-1 1c-8-2-14-1-22 0h-1-2z" class="y"></path><path d="M284 368h5c2 0 3-1 4-1 3 0 5 1 8 2l-7 2c-2 0-13 5-13 5-3 0-4 0-5 1-2 1-2 1-4 1-1 0-2 1-3 1l2-3 3-3 10-5z" class="f"></path><path d="M271 376l4-1c4-2 9-5 13-5 2 1 4 0 6 1-2 0-13 5-13 5-3 0-4 0-5 1-2 1-2 1-4 1-1 0-2 1-3 1l2-3z" class="V"></path><path d="M270 368l2 1c1-1 2 0 4 0h0l-2 1-1 2-3 2h2l2-1-3 3-2 3c1 0 2-1 3-1 2 0 2 0 4-1 1-1 2-1 5-1l-6 3c-15 9-23 25-27 42-1 3-1 7-1 10-1 3-1 5-2 7h0v-2h-1v-6c-1 1-1 3-2 5 0 2 1 3 0 5l-1-1v-17l2-10 2-9 3-7c1-6 3-10 6-14l2-2c2-1 5-3 6-5 1-3 6-6 8-7z" class="F"></path><path d="M248 396c1-1 1-2 2-3v-1h1v1 1h0c1-1 1-2 2-3 1-2 1-3 4-4-3 5-6 10-8 16-1-1-3 0-4 0l3-7z" class="M"></path><path d="M245 403c1 0 3-1 4 0-3 9-5 17-5 26v1c-1 1-1 3-2 5 0 2 1 3 0 5l-1-1v-17l2-10 2-9z" class="L"></path><path d="M269 379c1 0 2-1 3-1 2 0 2 0 4-1 1-1 2-1 5-1l-6 3c-15 9-23 25-27 42-1 3-1 7-1 10-1 3-1 5-2 7h0v-2c1-17 6-35 16-49l8-8z" class="H"></path><path d="M270 368l2 1c1-1 2 0 4 0h0l-2 1-1 2-3 2c-5 3-9 8-13 13h0c-3 1-3 2-4 4-1 1-1 2-2 3h0v-1-1h-1v1c-1 1-1 2-2 3 1-6 3-10 6-14l2-2c2-1 5-3 6-5 1-3 6-6 8-7z" class="X"></path><defs><linearGradient id="Al" x1="268.559" y1="377.247" x2="247.14" y2="370.333" xlink:href="#B"><stop offset="0" stop-color="#9f9a9a"></stop><stop offset="1" stop-color="#c9c5c4"></stop></linearGradient></defs><path fill="url(#Al)" d="M279 346c1-1 2-1 3-1h2c1-1 1-1 2 0h1c-2 2-2 2-1 4l3 4h0c1-1 1-2 2-1 1 0 0 0 2-1 1 0 1 1 2 1-1 1-1 2-2 2l-1 2h0 2c0 2 1 2 3 4v2l-3 1-7 2 1 1c-2 1-3 1-4 2l-10 5-2 1h-2l3-2 1-2 2-1h0c-2 0-3-1-4 0l-2-1c-2 1-7 4-8 7-1 2-4 4-6 5l-2 2c-3 4-5 8-6 14l-3 7-2 9-1-1-1-1h-2l-1-3v-1-8h0c3-12 9-26 17-35 4-4 9-7 13-11l6-3c2-1 4-2 5-3h0z"></path><path d="M286 345h1c-2 2-2 2-1 4-1 0-2 0-2 1-2 2-7 4-9 5-3 2-5 4-8 6h-1c-2 2-2 5-5 4h0-1l1-2c6-8 17-13 25-18z" class="b"></path><defs><linearGradient id="Am" x1="241.684" y1="377.925" x2="256.598" y2="398.33" xlink:href="#B"><stop offset="0" stop-color="#221f1f"></stop><stop offset="1" stop-color="#3c3c3c"></stop></linearGradient></defs><path fill="url(#Am)" d="M261 363l-1 2h1 0c3 1 3-2 5-4h1l-4 6c-1 0-2-1-2-1h-2c1 2 2 1 1 3s-2 3-3 4c-1 3-3 5-5 7h1v1h1v1c-3 4-5 8-6 14l-3 7-2 9-1-1-1-1h-2l-1-3 4-13c3-12 9-23 19-31z"></path><path d="M286 349l3 4h0c1-1 1-2 2-1 1 0 0 0 2-1 1 0 1 1 2 1-1 1-1 2-2 2l-1 2h0 2c0 2 1 2 3 4v2l-3 1-7 2 1 1c-2 1-3 1-4 2l-10 5-2 1h-2l3-2 1-2 2-1h0c-2 0-3-1-4 0l-2-1c-2 1-7 4-8 7-1 2-4 4-6 5l-2 2v-1h-1v-1h-1c2-2 4-4 5-7 1-1 2-2 3-4s0-1-1-3h2s1 1 2 1l4-6c3-2 5-4 8-6 2-1 7-3 9-5 0-1 1-1 2-1z" class="F"></path><defs><linearGradient id="An" x1="296.014" y1="358.37" x2="270.479" y2="367.944" xlink:href="#B"><stop offset="0" stop-color="#595859"></stop><stop offset="1" stop-color="#9a9795"></stop></linearGradient></defs><path fill="url(#An)" d="M294 356c0 2 1 2 3 4v2l-3 1-7 2 1 1c-2 1-3 1-4 2l-10 5-2 1h-2l3-2 1-2 2-1h0c-2 0-3-1-4 0l-2-1c4-4 10-7 14-9 3-1 6-1 8-3h2z"></path><path d="M273 372c5-3 9-5 14-7l1 1c-2 1-3 1-4 2l-10 5-2 1h-2l3-2z" class="O"></path><path d="M324 292c1 1 1 1 1 2v2l-2 6c1 2 2 6 3 7v1l-2 1c2 1 4 3 5 3 3 1 5 1 8 1h0l-2 1c0 1 0 2 1 3l1 3c2 2 6 3 9 5 4 2 9 4 13 7h-1c2 2 5 4 7 7 0 1 0 1-1 1 1 1 1 2 2 3l1 2h0v1c1 0 2 1 2 2v3c1 2 2 3 3 4l-1 1 1 1c1 0 1 0 1 1l1 1h-1l-4-4v1h-1c-4-1-7-6-10-8-1 1-2 1-3 2l-1-1c-1 1-1 1-2 1-1 1-3 2-4 2h-2l-2 1c-1 1-2 2-2 3l-1 1c-1-2-1-4-2-5-3 2-1 6-3 9v4l-2 2-4 1v1c-1 1-1 3-2 5-3-1-6-1-9-2-2-1-10-4-11-3l-2-2h-5c-3-1-5-2-8-2-1 0-2 1-4 1h-5c1-1 2-1 4-2l-1-1 7-2 3-1v-2c-2-2-3-2-3-4h-2 0l1-2c1 0 1-1 2-2-1 0-1-1-2-1-2 1-1 1-2 1-1-1-1 0-2 1h0l-3-4c-1-2-1-2 1-4l1-1 2-1 3-3c4-3 7-6 11-9 0 0-1 0-1-1 0 1-1 1-2 1v-1c-1 0-2 0-3 1v-1c1-1 2-1 2-3v-1h1v1h2 0l-1-1c-1-2-1-2-1-4l1-1h3c2 0 4 1 6 0 2 2 4 3 7 4 0-1 0-1 1-2 0 0 1 0 2 1h2c-2-1-4-2-5-4v-1l-1-1v-1c-1-1-1-3-1-5l-4-10 1-1c1 1 1 2 1 3h1v-1-1-2c1 0 1 0 1-1h1c0-1 0-1 1 0h1v-1c0-1 1-1 1-2 2-1 4-2 4-4z" class="AE"></path><path d="M314 331c2-1 4-1 6-1 1 1 4 3 4 5-2-1-4-1-7 0h-1l-6 3-4-5v-1l1 1h1c2-1 4-1 6-2zm5 11c-4 1-8 4-11 6 4-5 10-10 18-11h0 3c1 0 2 0 3 1 3 1 6 1 9 3h0c-7-1-16-2-22 1z" class="AC"></path><defs><linearGradient id="Ao" x1="303.815" y1="347.69" x2="293.113" y2="351.521" xlink:href="#B"><stop offset="0" stop-color="#171718"></stop><stop offset="1" stop-color="#333334"></stop></linearGradient></defs><path fill="url(#Ao)" d="M289 353v-5l1 1c0 1 0 1 1 2l2-2c2-2 5-4 8-4 1 0 2 1 3 2 1 2 2 3 4 5v1l-1 1h-1-5 0l2-1h0c-2 0-3 0-4 1l-1 2 1 1v-2h0l1-1v1c0 1 1 2 2 2h1c1-1 1-1 2-1h1l1 1-3 1v2h1l-1-1 1-1v1c2 1 4 1 5 1h0c-1 0-2 0-4 1h-2l-7 1v-2c-2-2-3-2-3-4h-2 0l1-2c1 0 1-1 2-2-1 0-1-1-2-1-2 1-1 1-2 1-1-1-1 0-2 1z"></path><path d="M294 356c1-1 2-1 3-1v1c1 2 3 2 5 2 1 1 1 2 2 3l-7 1v-2c-2-2-3-2-3-4z" class="v"></path><defs><linearGradient id="Ap" x1="319.315" y1="376.765" x2="313.215" y2="360.914" xlink:href="#B"><stop offset="0" stop-color="#070302"></stop><stop offset="1" stop-color="#222426"></stop></linearGradient></defs><path fill="url(#Ap)" d="M287 365l7-2c1 0 2 1 3 1 5-1 10 1 15 2 6 0 13-1 19 1l1 2-1 1h-1v1c-1 1-1 3-2 5-3-1-6-1-9-2-2-1-10-4-11-3l-2-2h-5c-3-1-5-2-8-2-1 0-2 1-4 1h-5c1-1 2-1 4-2l-1-1z"></path><path d="M288 366c2 0 4-1 6-1 2 1 6 0 7 1h-1c2 1 1 1 2 1s3 1 4 2h-5c-3-1-5-2-8-2-1 0-2 1-4 1h-5c1-1 2-1 4-2z" class="F"></path><defs><linearGradient id="Aq" x1="334.008" y1="348.682" x2="351.931" y2="341.663" xlink:href="#B"><stop offset="0" stop-color="#0a0202"></stop><stop offset="1" stop-color="#210908"></stop></linearGradient></defs><path fill="url(#Aq)" d="M319 342c6-3 15-2 22-1h1c2 2 5 3 8 4s5 3 8 5c-1 1-2 1-3 2l-1-1c-1 1-1 1-2 1-1 1-3 2-4 2h-2l-2 1c-1 1-2 2-2 3l-1 1c-1-2-1-4-2-5-3 2-1 6-3 9h0l-2-2c1-1 0-1 0-2-1-1-1-1 0-2-1-1-1-3-2-5v-1l-1-2-1-5v-1c-1-1-1-1-1-2-4 0-6 0-9 2h-1v-1z"></path><path d="M344 355l1-3c-2-2-3-5-4-7l6 3c2 1 5 2 7 3-1 1-1 1-2 1-1 1-3 2-4 2h-2l-2 1z" class="s"></path><path d="M348 354c-1-1 0-1-1-1l-1-1 2-2c-1 0-2 0-2-2h1c2 1 5 2 7 3-1 1-1 1-2 1-1 1-3 2-4 2z" class="g"></path><path d="M336 363l-3-9-3-12c3 0 5 0 7 2 0 3 1 6 2 10-3 2-1 6-3 9h0zm-25-42c2 2 4 3 7 4 0-1 0-1 1-2 0 0 1 0 2 1h2c1 0 2 1 3 2l2 1v2c3 2 10 5 14 6h2l9 6c3 2 7 5 10 6 2 1 5 5 6 6 1 2 2 3 3 4l-1 1 1 1c1 0 1 0 1 1l1 1h-1l-4-4v1h-1c-4-1-7-6-10-8s-5-4-8-5-6-2-8-4h-1 0c-3-2-6-2-9-3-1-1-2-1-3-1h-3 0v-1c0-1-1-1-2-1 0-2-3-4-4-5-2 0-4 0-6 1s-4 1-6 2h-1l-1-1v1l-2-2s-1 0-1-1c0 1-1 1-2 1v-1c-1 0-2 0-3 1v-1c1-1 2-1 2-3v-1h1v1h2 0l-1-1c-1-2-1-2-1-4l1-1h3c2 0 4 1 6 0z" class="K"></path><path d="M303 327l-1-1c-1-2-1-2-1-4l1-1c1 2 7 4 7 6h-1c-1 0 0 0-1 1l-4-1z" class="N"></path><path d="M298 331v-1c1-1 2-1 2-3v-1h1v1h2 0l4 1c2 1 5 2 7 3-2 1-4 1-6 2h-1l-1-1v1l-2-2s-1 0-1-1c0 1-1 1-2 1v-1c-1 0-2 0-3 1z" class="e"></path><path d="M368 358c-4-4-8-10-13-13-5-2-9-5-13-7s-9-3-13-6c-2-1-7-5-7-6 2-1 4 2 6 3 3 2 10 5 14 6h2l9 6c3 2 7 5 10 6 2 1 5 5 6 6 1 2 2 3 3 4l-1 1 1 1c1 0 1 0 1 1l1 1h-1l-4-4v1h-1z" class="U"></path><path d="M324 292c1 1 1 1 1 2v2l-2 6c1 2 2 6 3 7v1l-2 1c2 1 4 3 5 3 3 1 5 1 8 1h0l-2 1c0 1 0 2 1 3l1 3c2 2 6 3 9 5 4 2 9 4 13 7h-1c2 2 5 4 7 7 0 1 0 1-1 1 1 1 1 2 2 3l1 2h0v1c1 0 2 1 2 2v3c-1-1-4-5-6-6-3-1-7-4-10-6l-9-6h-2c-4-1-11-4-14-6v-2l-2-1c-1-1-2-2-3-2-2-1-4-2-5-4v-1l-1-1v-1c-1-1-1-3-1-5l-4-10 1-1c1 1 1 2 1 3h1v-1-1-2c1 0 1 0 1-1h1c0-1 0-1 1 0h1v-1c0-1 1-1 1-2 2-1 4-2 4-4z" class="t"></path><path d="M346 329h1 1c3 2 7 3 10 5 2 2 5 4 7 7 0 1 0 1-1 1-2-2-4-4-6-5l-12-8z" class="q"></path><path d="M320 311c5 5 11 8 17 11 2 2 6 3 9 5 4 2 9 4 13 7h-1c-3-2-7-3-10-5h-1-1c-4-2-9-4-13-7v1h-1c2 1 2 2 3 3v1c-2-1-4-2-6-4h0c-4-2-7-5-9-9v-3z" class="AM"></path><path d="M329 323c2 2 4 3 6 4l14 8c1 0 2 0 3 1 2 0 3 1 5 2l1-1c2 1 4 3 6 5 1 1 1 2 2 3l1 2h0v1c1 0 2 1 2 2v3c-1-1-4-5-6-6-3-1-7-4-10-6l-9-6-6-4c-1-2-4-3-5-4-2-1-4-2-4-4z" class="l"></path><path d="M349 335c1 0 2 0 3 1 2 0 3 1 5 2l1-1c2 1 4 3 6 5 1 1 1 2 2 3l1 2h0l-18-12z" class="w"></path><path d="M312 302l1-1c1 1 1 2 1 3h1l5 7v3c2 4 5 7 9 9h0c0 2 2 3 4 4 1 1 4 2 5 4l6 4h-2c-4-1-11-4-14-6v-2l-2-1c-1-1-2-2-3-2-2-1-4-2-5-4v-1l-1-1v-1c-1-1-1-3-1-5l-4-10z" class="N"></path><path d="M312 302l1-1c1 1 1 2 1 3h1l5 7v3c2 4 5 7 9 9h0c0 2 2 3 4 4 1 1 4 2 5 4-4-2-9-5-12-8-4-3-7-7-10-11l-4-10z" class="W"></path><path d="M324 292c1 1 1 1 1 2v2l-2 6c1 2 2 6 3 7v1l-2 1c2 1 4 3 5 3 3 1 5 1 8 1h0l-2 1c0 1 0 2 1 3l1 3c-6-3-12-6-17-11l-5-7v-1-1-2c1 0 1 0 1-1h1c0-1 0-1 1 0h1v-1c0-1 1-1 1-2 2-1 4-2 4-4z" class="j"></path><path d="M320 303l1-1 2 2v-2c1 2 2 6 3 7v1l-2 1c-1-1-1-3-2-3-1-2-2-3-2-5z" class="AC"></path><path d="M324 292c1 1 1 1 1 2v2l-2 6v2l-2-2-1 1-1-1c-1 0-2 1-2 1-1 0-1-1-1-1l-1 1v-1-2c1 0 1 0 1-1h1c0-1 0-1 1 0h1v-1c0-1 1-1 1-2 2-1 4-2 4-4z" class="K"></path><defs><linearGradient id="Ar" x1="327.523" y1="255.305" x2="309.523" y2="267.664" xlink:href="#B"><stop offset="0" stop-color="#150403"></stop><stop offset="1" stop-color="#2c0807"></stop></linearGradient></defs><path fill="url(#Ar)" d="M284 172l7 4c1 1 3 3 5 3a30.44 30.44 0 0 1 8 8c6 7 9 17 13 25 4 4 5 8 8 12l2 4c0-1 1-2 1-3v-1l2-1c1 3 2 6 4 9h0l5 11 2 7 1 1c0 4 1 9 3 13v5l-1 2 6 15c0 3 0 5 1 8 1 4 1 8 3 12 1 5 3 10 4 16 0 3 1 7 2 10 0 1 0 1-1 2h0c-4-3-9-5-13-7-3-2-7-3-9-5l-1-3c-1-1-1-2-1-3l2-1h0c-3 0-5 0-8-1-1 0-3-2-5-3l2-1v-1c-1-1-2-5-3-7l2-6v-2c0-1 0-1-1-2 0 2-2 3-4 4 0 1-1 1-1 2v1h-1c-1-1-1-1-1 0h-1c0 1 0 1-1 1v2 1 1h-1c0-1 0-2-1-3l-1 1c0-1-1-4-2-5l-7-22c-3-4-5-11-6-16l-8-20c-1-3-3-6-4-8-1-3-3-5-4-8v-1h-2-2l1-1c1-1 6-2 8-3s4-2 7-4h2c3-2 4-4 6-7l1 1h0c0-4 0-7-1-11-1-1-2-3-3-5l-2-2-2-3c-1-2-2-4-4-5 0-2 0-2 1-3l-3-4c-2-1-3-2-4-3z"></path><path d="M306 254v-1c0-1 0-1 1-2v-2c0-1 0-2 1-2l1 1h-1c1 1 1 1 1 3 0 1-1 2-2 3h-1z" class="AG"></path><path d="M315 233l2 2c1 1 2 1 3 2l2 3 1 1v2h-1c2 3 6 9 7 12h0c-5-7-11-14-20-17 1-1 2-1 3-2l3-3z" class="AC"></path><path d="M315 233l2 2c1 1 2 1 3 2l2 3 1 1v2h-1c-2-2-5-5-8-6l-2-1 3-3zm-6 15c1 1 4 5 4 5h2c1-1-1-1 1-2l1 2h0c1 1 1 2 2 3s2 2 2 4c-1-1 0-1-1-1v1 2c-1 1-1 3-1 4l-4-7h-1v3c-2-1-3-2-5-3 0-1 0-1-1-1-1-1-2-3-2-4h1c1-1 2-2 2-3 0-2 0-2-1-3h1z" class="T"></path><path d="M317 253c1 1 2 1 3 3h1l3 5v1c0-1-1-2-1-3l-2-2 6 13c0-2-1-3-2-5 2 1 1 0 2 1h0 1l1 1c0-3-7-9-6-11l-1-2h0c5 5 7 10 10 17 1 3 3 6 4 10v1l-2-2v1l3 3c1 2 2 4 2 6l-1-1c-1-2-2-3-4-4v1c-1-1-2-4-3-6l-10-20c0-2-1-3-2-4s-1-2-2-3z" class="U"></path><path d="M325 238l1 1c2 4 4 8 5 12 2 5 4 9 6 14s3 10 5 15l-1 2c-3-10-7-17-12-26v-1h0c-1-3-5-9-7-12h1v-2l1 1 1-4z" class="j"></path><path d="M325 238l1 1v5c-1 0-2-1-2-2l1-4z" class="K"></path><defs><linearGradient id="As" x1="337.848" y1="279.324" x2="316.772" y2="282.094" xlink:href="#B"><stop offset="0" stop-color="#2d0806"></stop><stop offset="1" stop-color="#4b110e"></stop></linearGradient></defs><path fill="url(#As)" d="M308 258c1 0 1 0 1 1 2 1 3 2 5 3v-3h1l4 7c0-1 0-3 1-4v-2-1c1 0 0 0 1 1l10 20c1 2 2 5 3 6v-1c2 1 3 2 4 4l1 1v1 3c0 1 0 3 1 5l2 1-1 2-4 4c-2 1-2 1-4 1v3c-1 1-3 0-5 1l-1-2h-1c-1-1-2-5-3-7l2-6c1-1 2-2 2-3 1-1 1-1 2-1l-1-1h-1c-3-1-4-3-5-4-4-4-7-8-9-13l1-2c-2-5-4-9-6-14z"></path><path d="M322 272h3l2 6-1 1-1-1-3-6z" class="AI"></path><path d="M327 293c1-1 1-1 2-1h0c2 1 3 1 4 2 2 2 3 2 3 4l1 2-1 1c-1 1-2 2-2 4 1 1 2 1 3 1-2 1-2 1-4 1v3c-1 1-3 0-5 1l-1-2h-1c-1-1-2-5-3-7l2-6c1-1 2-2 2-3z" class="N"></path><path d="M333 294c2 2 3 2 3 4v1h-2l-3-2c0-1-1-1-1-2h1c1 0 1 0 2-1z" class="R"></path><path d="M327 293c1-1 1-1 2-1h0c2 1 3 1 4 2-1 1-1 1-2 1h-1c0 1 1 1 1 2-2 0-3 0-5 2h0c0-2 1-4 1-5v-1z" class="Z"></path><path d="M329 307h2l1-1c0-1-1-2-1-2v-1c0-1 0-2-1-3l1-1c0 1 1 2 1 4 0 1 0 1 1 2v2 3c-1 1-3 0-5 1l-1-2h1c0-1 1-1 1-2z" class="R"></path><path d="M327 293v1c0 1-1 3-1 5h0l1 3c0 2 1 3 2 5h0c0 1-1 1-1 2h-1-1c-1-1-2-5-3-7l2-6c1-1 2-2 2-3z" class="c"></path><path d="M308 258c1 0 1 0 1 1 2 1 3 2 5 3 1 3 4 7 6 10l2 4s1 1 1 2c1 0 1 1 1 2 2 1 2 2 3 3l1 1c1 1 3 2 3 3v1c1 1 2 2 1 3s0 1-2 0l-1 1h0l-1-1h-1c-3-1-4-3-5-4-4-4-7-8-9-13l1-2c-2-5-4-9-6-14z" class="C"></path><path d="M314 272c2 3 4 5 6 8s3 5 6 7h0v-1c-2-2-5-6-6-10h0c3 3 4 7 8 8 1 1 3 2 3 3v1c1 1 2 2 1 3s0 1-2 0l-1 1h0l-1-1h-1c-3-1-4-3-5-4-4-4-7-8-9-13l1-2z" class="T"></path><defs><linearGradient id="At" x1="299.517" y1="208.968" x2="314.248" y2="204.4" xlink:href="#B"><stop offset="0" stop-color="#541817"></stop><stop offset="1" stop-color="#8b2622"></stop></linearGradient></defs><path fill="url(#At)" d="M284 172l7 4c1 1 3 3 5 3a30.44 30.44 0 0 1 8 8c6 7 9 17 13 25l3 6c-1 1-1 2-2 2l3 8 2 2v1c0 2 1 5 2 7l-1 4-1-1-1-1-2-3c-1-1-2-1-3-2l-2-2-3 3c-1 1-2 1-3 2h-6c-1-2 0-3-1-4v-1c-1-1-1-2-2-4s-1-3-1-5h0v-1h1c2 0 3-2 4-4v-2h0v-1-2l-2-1c-1-1 0-3 1-5h-1c0-4 0-7-1-11-1-1-2-3-3-5l-2-2-2-3c-1-2-2-4-4-5 0-2 0-2 1-3l-3-4c-2-1-3-2-4-3z"></path><path d="M319 231c0-5-2-12-5-16h0v-1h1l3 6 3 8v4h-1l-1-1z" class="i"></path><path d="M321 228l2 2v1c0 2 1 5 2 7l-1 4-1-1-1-1-2-3-1-2v-4l1 1h1v-4z" class="C"></path><path d="M323 231c0 2 1 5 2 7l-1 4-1-1-1-1h1c-1-3-1-6 0-9z" class="q"></path><path d="M284 172l7 4c1 1 3 3 5 3a30.44 30.44 0 0 1 8 8l-1 1h-2l-3-3h0c0 1 0 1 1 2v1l-8-9-3-4c-2-1-3-2-4-3z" class="g"></path><path d="M304 219c2-2 3-6 4-9h1c0 1 1 2 1 2 2 3 4 7 5 10 1 4 1 8 0 11l-3 3c-1 1-2 1-3 2h-6c-1-2 0-3-1-4v-1c-1-1-1-2-2-4s-1-3-1-5h0v-1h1c2 0 3-2 4-4z" class="U"></path><path d="M300 229l2 1h0c0 1 1 1 2 2v-1c1 0 4-1 5 0h1l1 1c-1 2-3 3-5 4-1 0-3 1-3 2-1-2 0-3-1-4v-1c-1-1-1-2-2-4z" class="S"></path><path d="M313 228v-4c-1-1-1 0 0-2h2c1 4 1 8 0 11l-3 3c-1 1-2 1-3 2h-6c0-1 2-2 3-2 2-1 4-2 5-4v-2c-2-3-6-4-9-5 2-1 3 0 4 0 2 1 5 3 6 4h1v-1z" class="AM"></path><path d="M304 219c2-2 3-6 4-9h1c0 1 1 2 1 2 2 3 4 7 5 10h-2c-1 2-1 1 0 2v4 1h-1c-1-1-4-3-6-4-1 0-2-1-4 0l-3-1v-1h1c2 0 3-2 4-4z" class="Z"></path><path d="M313 228c-1-2-3-5-3-7s-2-4-2-6c0-1 2-2 2-3 2 3 4 7 5 10h-2c-1 2-1 1 0 2v4z" class="a"></path><path d="M293 214h2c3-2 4-4 6-7l1 1h0 1c-1 2-2 4-1 5l2 1v2 1h0v2c-1 2-2 4-4 4h-1v1h0-2-1c0 1-1 1 0 2v1c1 2 2 5 4 6v3l1 2v1c0 2 0 2 1 3v1l-1-1c0-1-1-3-2-4l1 5 2 10 6 14 5 7c2 5 5 9 9 13 1 1 2 3 5 4h1l1 1c-1 0-1 0-2 1 0 1-1 2-2 3v-2c0-1 0-1-1-2 0 2-2 3-4 4 0 1-1 1-1 2v1h-1c-1-1-1-1-1 0h-1c0 1 0 1-1 1v2 1 1h-1c0-1 0-2-1-3l-1 1c0-1-1-4-2-5l-7-22c-3-4-5-11-6-16l-8-20c-1-3-3-6-4-8-1-3-3-5-4-8v-1h-2-2l1-1c1-1 6-2 8-3s4-2 7-4z" class="j"></path><path d="M297 246l-7-18 2-2h1l6 12 1 5h-1c-1-1-1-2-2-3v1 1 3 1z" class="c"></path><path d="M281 222c2 1 2 1 3 1 2 1 6 0 8 0h3c2 0 1 0 2 1h-1c0 1-1 1 0 2v1c1 2 2 5 4 6v3l1 2v1c0 2 0 2 1 3v1l-1-1c0-1-1-3-2-4l-6-12-1-1h-3l-1 1c1 1 1 2 1 4 0 1 1 1 0 2l-1-2c-1-1-1-2-3-2-1-2-2-4-4-5v-1z" class="S"></path><path d="M297 246v-1-3-1-1c1 1 1 2 2 3h1l2 10 6 14h0c0 1 0 1 1 2l-1 2h-1v1c1 1 1 2 1 3v1l-1-3v3l-3-6-7-24z" class="J"></path><path d="M302 253l6 14h0c0 1 0 1 1 2l-1 2h-1c-1-2-2-3-3-5v-6c-2-2-2-4-2-7z" class="R"></path><path d="M293 214h2c3-2 4-4 6-7l1 1h0 1c-1 2-2 4-1 5l2 1v2 1h0v2c-1 2-2 4-4 4h-1v1h0-2c-1-1 0-1-2-1h-3c-2 0-6 1-8 0-1 0-1 0-3-1h-2-2l1-1c1-1 6-2 8-3s4-2 7-4z" class="AK"></path><path d="M304 216v1h0v2c-1 2-2 4-4 4h-1v1h0-2c-1-1 0-1-2-1h-3c2-1 3-1 5-2 1-1 1 0 2 0 2-1 3-3 5-5z" class="K"></path><path d="M293 214h2c3-2 4-4 6-7l1 1c-1 4-2 7-6 10s-11 4-17 4h-2l1-1c1-1 6-2 8-3s4-2 7-4z" class="AQ"></path><path d="M308 267l5 7c2 5 5 9 9 13 1 1 2 3 5 4h1l1 1c-1 0-1 0-2 1 0 1-1 2-2 3v-2c0-1 0-1-1-2 0 2-2 3-4 4 0 1-1 1-1 2v1h-1c-1-1-1-1-1 0h-1c0 1 0 1-1 1v2 1 1h-1c0-1 0-2-1-3l-1 1c0-1-1-4-2-5l-7-22c1 0 1 1 1 2h1l1 1h0l-1-1v-2c0-2-1-3-1-4v-1l3 6v-3l1 3v-1c0-1 0-2-1-3v-1h1l1-2c-1-1-1-1-1-2h0z" class="q"></path><path d="M307 276v-3l1 3v-1c2 3 4 7 6 11 1 4 3 8 5 12v1h-1c-1-1-1-1-1 0h-1c0 1 0 1-1 1 0-1-1-4-2-6-2-6-5-12-6-18z" class="i"></path><path d="M308 267l5 7c2 5 5 9 9 13 1 1 2 3 5 4h1l1 1c-1 0-1 0-2 1 0 1-1 2-2 3v-2c0-1 0-1-1-2 0 2-2 3-4 4 0 1-1 1-1 2-2-4-4-8-5-12-2-4-4-8-6-11 0-1 0-2-1-3v-1h1l1-2c-1-1-1-1-1-2h0z" class="S"></path><path d="M308 267l5 7c2 5 5 9 9 13 1 1 2 3 5 4h1l1 1c-1 0-1 0-2 1 0 1-1 2-2 3v-2c0-1 0-1-1-2-2-2-5-4-7-6l-6-9c-1-2-2-4-4-5v-1h1l1-2c-1-1-1-1-1-2h0z" class="i"></path><defs><linearGradient id="Au" x1="356.014" y1="332.145" x2="313.545" y2="234.322" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#360e0b"></stop></linearGradient></defs><path fill="url(#Au)" d="M317 212c4 4 5 8 8 12l2 4c0-1 1-2 1-3v-1l2-1c1 3 2 6 4 9h0l5 11 2 7 1 1c0 4 1 9 3 13v5l-1 2 6 15c0 3 0 5 1 8 1 4 1 8 3 12 1 5 3 10 4 16 0 3 1 7 2 10 0 1 0 1-1 2h0c-4-3-9-5-13-7-3-2-7-3-9-5l-1-3c-1-1-1-2-1-3l2-1h0c-3 0-5 0-8-1-1 0-3-2-5-3l2-1v-1h1l1 2c2-1 4 0 5-1v-3c2 0 2 0 4-1l4-4 1-2 1-3c-1-2 0-6-1-8 0-3 0-5-1-7l1-2c-2-5-3-10-5-15s-4-9-6-14c-1-4-3-8-5-12l-1-1c-1-2-2-5-2-7v-1l-2-2-3-8c1 0 1-1 2-2l-3-6z"></path><path d="M350 318v1c1 2 1 3-1 5h-1c0-1-1-2 0-3 0-1 0-2 2-3z" class="b"></path><path d="M341 282l1-2c4 9 9 20 4 29-1 3-2 5-5 6-1 1-3 1-4 0h0c-3 0-5 0-8-1-1 0-3-2-5-3l2-1v-1h1l1 2c2-1 4 0 5-1v-3c2 0 2 0 4-1l4-4 1-2 1-3c-1-2 0-6-1-8 0-3 0-5-1-7z" class="C"></path><path d="M343 297c1 1 1 3 1 4v1c-1-1 0-1-1-1l-2 1 1-2 1-3z" class="q"></path><path d="M342 305h2l1-3h1c0 5-2 8-5 11-2 2-2 2-4 2-3 0-5 0-8-1h2c1 0 1-1 2-1s3 0 5-1c1-1 2-3 3-4 0-1 1-2 1-3z" class="s"></path><defs><linearGradient id="Av" x1="338.347" y1="299.51" x2="333.165" y2="313.956" xlink:href="#B"><stop offset="0" stop-color="#641a17"></stop><stop offset="1" stop-color="#902823"></stop></linearGradient></defs><path fill="url(#Av)" d="M341 302l2-1c1 0 0 0 1 1l-2 3c0 1-1 2-1 3-1 1-2 3-3 4-2 1-4 1-5 1s-1 1-2 1h-2c-1 0-3-2-5-3l2-1v-1h1l1 2c2-1 4 0 5-1v-3c2 0 2 0 4-1l4-4z"></path><path d="M326 309h1l1 2c1 1 2 1 3 2 1-1 2 0 4-1h3c-2 1-4 1-5 1s-1 1-2 1h-2c-1 0-3-2-5-3l2-1v-1z" class="J"></path><path d="M317 212c4 4 5 8 8 12l2 4c0-1 1-2 1-3v-1l2-1c1 3 2 6 4 9h0l5 11 2 7 1 1c0 4 1 9 3 13v5l-1 2-17-39-3-3-1 1-2-2-3-8c1 0 1-1 2-2l-3-6z" class="AU"></path><path d="M328 225l4 7c0 1 0 2-1 2-2-2-3-4-4-6 0-1 1-2 1-3z" class="Ac"></path><path d="M328 224l2-1c1 3 2 6 4 9h0-1l1 1c-1 0-1 0-2-1l-4-7v-1z" class="AW"></path><path d="M318 220c1 0 1-1 2-2l7 14-3-3-1 1-2-2-3-8z" class="q"></path><path d="M255 463c-4-7-4-15-3-24 2-14 8-30 20-39 6-5 14-8 22-7s15 6 20 13c0 2 1 4 1 7v1 3 3h1l2-3h1v2c1 0 2 0 3 1v5c1 0 1 1 1 2s1 2 1 3l-4 3v2h0l-1-1c-3 0-4 0-6-2-1-1-1-3-1-5-1 1-1 3-3 3h0v-1h0l-2 2c-1 2-1 4 0 6 1 1 2 3 3 4 3 1 5 2 8 1 0 1 1 1 2 2 3-1 7-2 10-4h3l3 5-9 6c-2 2-5 6-6 9l1 1h0c0 2 0 4 1 6v3h0c-1 1-1 1-1 2s0 1-1 2l-1-2-1 1h0l-1-2-4-7c0 3 1 7 0 10h-1c-2 5-7 9-11 12-3 1-6 1-9 1-7 1-12 0-18-2-2-1-3-2-5-2l1-1-2-1c-1-1-7-5-8-6l-2-2c1-1 2-1 3-1s1-1 1-1c-3-2-5-4-8-8z" class="AZ"></path><path d="M319 467c1 1 3 2 4 3h0c-1 1-1 1-1 2s0 1-1 2l-1-2-1 1h0l-1-2 1-4z" class="n"></path><path d="M321 460l1 1h0c0 2 0 4 1 6v3c-1-1-3-2-4-3 1-2 1-5 2-7z" class="D"></path><path d="M289 432l1-2c0 1 1 1 1 1l4 7-1 1v-1c-2-2-4-3-5-6z" class="G"></path><path d="M263 471l7 3c-2 1-4 1-6 1h-3l-2-2c1-1 2-1 3-1s1-1 1-1z" class="n"></path><path d="M298 437c2 2 4 4 7 5v2c-4-1-7-3-11-5l1-1c1-1 2-1 3-1z" class="H"></path><path d="M278 479c1-1 3-2 4-3-1 3-2 5-5 6-2 1-4 1-6 0l-2-1c3 0 6-1 9-2z" class="E"></path><path d="M305 442v-1-1c-1 0-2-1-2-1l-1-1c-1-1-2-1-2-2v-1l2 1c1 1 2 1 3 2 2 1 3 3 5 3 3 1 5 2 8 1 0 1 1 1 2 2-5 1-10 1-15 0v-2z" class="x"></path><path d="M280 470v-3c2 0 2 3 2 5 1 1 1 3 0 4s-3 2-4 3c-3 1-6 2-9 2-1-1-7-5-8-6h3c2 0 4 0 6-1h3 1 0c3-1 4-2 6-4z" class="AD"></path><path d="M280 470v5c-1 0-1 1-2 1-2 0-3-1-5-2h1 0c3-1 4-2 6-4z" class="I"></path><path d="M261 475h3c2 0 4 1 6 2 1 0 2 1 4 1h3l1 1c-3 1-6 2-9 2-1-1-7-5-8-6z" class="M"></path><defs><linearGradient id="Aw" x1="295.303" y1="410.996" x2="289.469" y2="430.639" xlink:href="#B"><stop offset="0" stop-color="#373535"></stop><stop offset="1" stop-color="#635f5e"></stop></linearGradient></defs><path fill="url(#Aw)" d="M289 432c-1-2-2-4-1-7 0-5 3-9 6-12s8-4 12-4c3 0 6 2 8 4h1v1 3 3h1l2-3h1v2c1 0 2 0 3 1v5c1 0 1 1 1 2s1 2 1 3l-4 3v2h0l-1-1c-3 0-4 0-6-2-1-1-1-3-1-5-1 1-1 3-3 3h0v-1h0l-2 2c-1 2-1 4 0 6 1 1 2 3 3 4-2 0-3-2-5-3-1-1-2-1-3-2l-2-1v1c0 1 1 1 2 2l1 1s1 1 2 1v1 1c-3-1-5-3-7-5-1 0-2 0-3 1l-4-7s-1 0-1-1l-1 2z"></path><path d="M309 417h0v-2h-3-3l-1-1c-1 1-2 2-2 3l-1-1c0-1-1-2 0-4l3-1h0c2 0 3-1 5 0h1 1c2 2 4 4 5 7-1 1 0 1-1 2l-1 1c-2-2-3-3-3-4z" class="S"></path><path d="M309 411c3 1 2 1 4 3l1-1h1v1 3 3h1l2-3h1v2c1 0 2 0 3 1v5c1 0 1 1 1 2s1 2 1 3l-4 3v2h0l-1-1c-3 0-4 0-6-2-1-1-1-3-1-5-1 1-1 3-3 3h0v-1h0l3-4c0-1 0-1-1-2s-2-2-2-3v-1c-3-1-5-1-7-1 2-1 4-1 7-1 0 1 1 2 3 4l1-1c1-1 0-1 1-2-1-3-3-5-5-7z" class="r"></path><path d="M314 425v-3l1-1c0 4 0 6 1 9l1 1v1l2 2c-3 0-4 0-6-2-1-1-1-3-1-5 1 0 1-1 2-2z" class="E"></path><path d="M314 425c0 2 0 5 1 7h0-1-1c-1-1-1-3-1-5 1 0 1-1 2-2z" class="B"></path><path d="M320 433h0c1-1 1-2 1-2v-3h-1c-2-4-2-6-1-9 1 0 2 0 3 1v5c1 0 1 1 1 2s1 2 1 3l-4 3z" class="F"></path><path d="M292 419l3-3v2c1 0 1 0 2-1l1 1c0 1 0 0 1 1 1-1 2-1 3-1 2 0 4 0 7 1v1c0 1 1 2 2 3s1 1 1 2l-3 4-2 2c-1 2-1 4 0 6 1 1 2 3 3 4-2 0-3-2-5-3-1-1-2-1-3-2l-2-1v1c0 1 1 1 2 2l1 1s1 1 2 1v1 1c-3-1-5-3-7-5-1 0-2 0-3 1l-4-7c-1-4-1-7 1-12z" class="AD"></path><path d="M295 418c1 0 1 0 2-1l1 1c0 1 0 0 1 1 1-1 2-1 3-1 2 0 4 0 7 1v1c0 1 1 2 2 3s1 1 1 2h-1c-1 0-3 3-5 5 0-3 3-3 2-6 0-2-1-3-2-4-1 1-3 1-4 1l-1-1-1 1-2 2v1l-1-1-2 2v-4c-1 0 0 0-1 1h0c0-2 0-2 1-4z" class="G"></path><defs><linearGradient id="Ax" x1="305.656" y1="436.94" x2="293.483" y2="428.363" xlink:href="#B"><stop offset="0" stop-color="#5f5c5a"></stop><stop offset="1" stop-color="#787170"></stop></linearGradient></defs><path fill="url(#Ax)" d="M292 419l3-3v2c-1 2-1 2-1 4h0c1-1 0-1 1-1v4l2-2 1 1v-1l2-2v4h-1v-1h-1c0 4 1 7 3 10l1 1v1l-2-1v1c0 1 1 1 2 2l1 1s1 1 2 1v1 1c-3-1-5-3-7-5-1 0-2 0-3 1l-4-7c-1-4-1-7 1-12z"></path><path d="M292 419c2 2 1 6 1 9l1 4c1 2 2 4 4 5-1 0-2 0-3 1l-4-7c-1-4-1-7 1-12z" class="X"></path><defs><linearGradient id="Ay" x1="389.876" y1="372.979" x2="365.703" y2="384.484" xlink:href="#B"><stop offset="0" stop-color="#280908"></stop><stop offset="1" stop-color="#9e312b"></stop></linearGradient></defs><path fill="url(#Ay)" d="M350 286l10 22v1h1v-1c2 4 3 8 6 11 1 0 0 0 1-1 1 2 2 3 3 5h2v-1l3-2c0-1 1-3 1-5l1 1c1 0 1 1 1 1 1 0 2-1 3-1h3 0 2v-2l1-1 1 1 4-2h1c1 1 1 2 2 2 1-1 0-1 1-2h2c-1 1-1 2-1 3l1 1 1-1 2 3h-2l1 3v1c2 1 3 1 6 1l3 2v-2l3 2 4 2c2 2 5 4 7 7 1 0 2 1 3 2 1 0 2 1 2 2 0 3 1 4 2 7h0c2 5 6 10 8 15l1 5c1 2 3 3 4 5h0l1 1c1 2 2 4 3 7 0 1 2 3 1 4h0l-2-1-1 1c-1 1-1 2-1 3l1 2-1 2c1 2 1 3 0 6-1-1-1-2-2-2l-2 6c1 2 2 5 3 8h0c2 3 4 5 7 7 1 1 1 2 2 3l3 1 2 2h3c-1 1-3 2-4 2l-1-2c-2 1-2 1-3 1h-1-1-4l-1 1c-2 3-5 4-7 9-1 1-2 3-2 5l-1 2v1c-2-1-2-2-2-3v-5l-3 6c0 3-1 5-1 7l-1 3h-1c0 2 0 3-1 4l-1 1c-1-1-1-3-2-4v3l1 1-1 1v2h0-1c-1 1-1 0-1 1 1 1 0 1 1 2v4l1 1v2h1v2l1 1v1l-1 1-7-17v6l1 3c-1-1-3-4-5-5l-2-2c-1 1-3 2-4 2 0 2-1 4-1 6l-1-1v1c-1 1-1 2-1 3 0-1 0-1-1-2v-1l1-1c0-1 1-5 0-7s-2-4-2-6c0-1-2-2-2-2-2-2-3-3-5-4h1l2 1 1-1-1-1-1-1c-2-1-2-1-3-2h-2l-1-2 1-1c1 0 1 1 2 1h0v-2l-1-1h0c0-4-2-5-5-7l1-1c2 1 3 2 4 4v-4l-1-1v-1c0-1-1-1-1-2-1-1-2-3-2-4l-6-5-21-12c-6-3-14-6-21-4h-2-1l1-1c1-1 2-1 4-1h0v-1c1 0 2 0 2-1h1v-1l-2-1h1c-5-5-13-6-17-13l2-1c1 0 1 0 2-1l-1-1c0-1 2-2 2-3v-3l2-2v-4c2-3 0-7 3-9 1 1 1 3 2 5l1-1c0-1 1-2 2-3l2-1h2c1 0 3-1 4-2 1 0 1 0 2-1l1 1c1-1 2-1 3-2 3 2 6 7 10 8h1v-1l4 4h1l-1-1c0-1 0-1-1-1l-1-1 1-1c-1-1-2-2-3-4v-3c0-1-1-2-2-2v-1h0l-1-2c-1-1-1-2-2-3 1 0 1 0 1-1-2-3-5-5-7-7h1 0c1-1 1-1 1-2-1-3-2-7-2-10-1-6-3-11-4-16-2-4-2-8-3-12-1-3-1-5-1-8z"></path><path d="M406 444s0 1-1 1c-1-1-1-1-1-2l-1-2h0c0-1 0-2 1-2-1-1-1-2-1-3l-2-2v-1l2 1 3 10zm-39-96l1-2s1 1 2 1h0c2 2 3 3 4 5-2 0-4-1-5-2 0-1-1-2-2-2z" class="AC"></path><path d="M400 427c-2-3-3-7-4-11l1-1 1 1c1 2 3 4 3 7l-1-1s0-1-1-2v1c0 2 1 4 1 6z" class="K"></path><path d="M395 431c0 1 1 1 1 2s0 2 1 3l1 1v4c-2-1-2-1-3-2h-2l-1-2 1-1c1 0 1 1 2 1h0v-2-4z" class="R"></path><path d="M359 334h0c5 4 8 8 11 13h0c-1 0-2-1-2-1l-1 2v-1h0l-1-2c-1-1-1-2-2-3 1 0 1 0 1-1-2-3-5-5-7-7h1z" class="J"></path><path d="M369 353v-3c1 1 3 2 5 2 2 4 3 9 5 14 1 1 2 3 2 5-3-5-6-10-9-14-1-1-2-2-3-4z" class="K"></path><path d="M386 394c4 8 5 15 7 23 1 5 2 9 2 14v4l-1-1h0c0-4-2-5-5-7l1-1c2 1 3 2 4 4v-4l-1-1v-1c0-1-1-1-1-2-1-1-2-3-2-4-1-1-1-2-1-3h1 0c1-1 0-1 0-3-1-3-3-5-5-7-1-1-1-2-2-3-2-1-7-3-8-6v-1h0c1 0 2 1 2 1l1-1c0 1 1 2 2 3 0 1 2 1 2 2 2 1 3 3 5 4v1c1 1 1 2 2 3l1-1c-1-1-1-2-1-4-1-3-3-5-3-8v-1z" class="z"></path><path d="M360 308v1h1v-1c2 4 3 8 6 11 1 0 0 0 1-1 1 2 2 3 3 5-1 2 0 4 1 6 2 4 4 8 5 12l-5-3c0-2-2-5-3-6l-8-20-1-4z" class="AQ"></path><path d="M369 358v-1l4 4h1l-1-1c0-1 0-1-1-1l-1-1 1-1c3 4 6 9 9 14v1s2 2 2 3-1 1 0 2l3 6h0l-1 1-1-2v-1h-1c0-1-1-2-1-3l-1-1-1-1c-1-1-2-1-3-1 0-2-2-3-3-4-1-2-3-4-5-6l1-2h1 1 0c0-2-2-4-3-5z" class="z"></path><path d="M371 363h1c1 3 3 5 5 7 3 4 6 9 9 13l-1 1-1-2v-1h-1c0-1-1-2-1-3l-1-1-1-1c-1-1-2-1-3-1 0-2-2-3-3-4-1-2-3-4-5-6l1-2h1z" class="J"></path><path d="M350 286l10 22 1 4h-1c0-1 0-2-1-3l-1-1c0-1 0-1-1-2v-2l-1-1-5-11c0 1 0 2 1 3 0 3 1 5 1 8l5 15c1 1 1 2 2 3h0c0-2 1-1 0-2l-3-8v-1l-1-4-2-2v-2c2 0 3 3 3 5 0 1 1 1 1 2v2c2 5 4 10 4 15 1 3 0 6 1 8v1l-3-3c-1-3-2-7-2-10-1-6-3-11-4-16-2-4-2-8-3-12-1-3-1-5-1-8z" class="K"></path><path d="M400 427c0-2-1-4-1-6v-1c1 1 1 2 1 2l1 1 6 12 3 8v1c0 1 0 3 1 5v1c0 1 1 2 2 3 0-2 0-3-1-4v-1l2-1h0 2l2 6v6l1 3c-1-1-3-4-5-5l-2-2c-1 1-3 2-4 2v-4l-2-9-3-10-3-7z" class="N"></path><path d="M408 453l1-1h0c1 1 2 1 3 1h1c1-1 0-3 1-4 2 1 3 7 4 10l1 3c-1-1-3-4-5-5l-2-2c-1 1-3 2-4 2v-4z" class="U"></path><path d="M383 350c1 3 3 5 6 8l1 2c0 4 2 9 4 12 0 2 1 4 3 6h0c0 2 1 3 1 5 3 4 5 10 6 15 0 1-1 2 0 4v2h-1c-1 2-1 3-2 5h0l-4-12-14-34h1c1 2 1 5 3 6h1c-2-6-4-12-7-17l2-2z" class="t"></path><path d="M397 397c0 1 1 1 1 2l1 3 2 1v-1l1-1c0 1 1 2 1 3-1 2-1 3-2 5h0l-4-12z" class="AN"></path><path d="M383 350c1 3 3 5 6 8l1 2c0 4 2 9 4 12 0 2 1 4 3 6h0c0 2 1 3 1 5 0 1 0 2 1 3 0 1 1 2 1 3v1h1v2h0l-1-1c-1 0-1 0-2-1 1-1 0-2-1-3v-1-1s0-1-1-2l-8-14c-2-6-4-12-7-17l2-2z" class="AW"></path><path d="M397 378c0-1 0-1 1-1l5 11c1 0 1 1 2 2v2c1-1 1 0 2-1l2-1v4h0l-1 5v1c0 1 0 2-1 3v2c0 4 0 6 2 9v1l1 5 2-2 3 4v1c1 1 2 2 3 4 1 3 3 6 4 9 1 1 2 3 3 4-1 2-1 3-2 4h0c0 1 1 3 1 4v3l1 1-1 1v2h0-1c-1 1-1 0-1 1 1 1 0 1 1 2v4l1 1v2h1v2l1 1v1l-1 1-7-17-2-6-15-38h0c1-2 1-3 2-5h1v-2c-1-2 0-3 0-4-1-5-3-11-6-15 0-2-1-3-1-5z" class="Z"></path><path d="M408 400l-1-2h-1v-4l2-1 1 1-1 5v1z" class="c"></path><path d="M410 420l2-2 3 4v1c1 1 2 2 3 4 1 3 3 6 4 9 1 1 2 3 3 4-1 2-1 3-2 4h0c0 1 1 3 1 4v3c-2-2-3-5-4-8l-10-23z" class="Aa"></path><path d="M415 423c1 1 2 2 3 4 1 3 3 6 4 9 1 1 2 3 3 4-1 2-1 3-2 4h0l-8-21z" class="s"></path><path d="M404 398c1 2 1 3 1 6 1 0 0 1 0 2v2c2 9 6 18 9 26 2 6 5 16 10 19v2h0-1c-1 1-1 0-1 1 1 1 0 1 1 2v4l1 1v2h1v2l1 1v1l-1 1-7-17-2-6-15-38h0c1-2 1-3 2-5h1v-2c-1-2 0-3 0-4z" class="Aa"></path><path d="M394 312c1 1 1 2 2 2 1-1 0-1 1-2h2c-1 1-1 2-1 3l1 1 1-1 2 3h-2l1 3v1h-1l-3 1c-2 0-4 1-6 2h-1l-2 2c-2 4-2 6-3 10v2l1 6c2 1 3 1 5 0 0 3-2 7-2 9v4c-3-3-5-5-6-8l-2 2c3 5 5 11 7 17h-1c-2-1-2-4-3-6h-1l-11-25 5 3c-1-4-3-8-5-12-1-2-2-4-1-6h2v-1l3-2c0-1 1-3 1-5l1 1c1 0 1 1 1 1 1 0 2-1 3-1h3 0 2v-2l1-1 1 1 4-2h1z" class="AS"></path><path d="M383 350l-1-2c1 0 1 0 2 1l5 5v4c-3-3-5-5-6-8z" class="t"></path><path d="M378 333h0c1-1 2-2 2-3 0 2 1 3 2 5 0 2 0 3 1 5 1 1 0 2 0 3-2-3-4-6-5-10z" class="l"></path><path d="M376 320l2 5c0 1 2 3 2 5 0 1-1 2-2 3h0c-2-3-3-7-5-10v-1l3-2z" class="K"></path><path d="M384 363c-1-2-2-4-2-6l-4-10c1 1 2 3 3 5 3 5 5 11 7 17h-1c-2-1-2-4-3-6z" class="AN"></path><path d="M386 326l1 1 1-1v1c-2 4-2 6-3 10v2l1 6h-2l-1-2c0-1 1-2 0-3-1-2-1-3-1-5l1-6 3-3z" class="h"></path><path d="M382 316h3v1h-1c-1 1-2 2-3 2-1 1 0 2 0 3 1 3 2 5 2 7l-1 6c-1-2-2-3-2-5s-2-4-2-5l-2-5c0-1 1-3 1-5l1 1c1 0 1 1 1 1 1 0 2-1 3-1z" class="C"></path><path d="M382 316h3v1c-2 0-4 0-5 1-1 2-2 4-2 7l-2-5c0-1 1-3 1-5l1 1c1 0 1 1 1 1 1 0 2-1 3-1z" class="q"></path><path d="M394 312c1 1 1 2 2 2 1-1 0-1 1-2h2c-1 1-1 2-1 3l1 1 1-1 2 3h-2l1 3v1h-1l-3 1c-2 0-4 1-6 2h-1l-2 2v-1l-1 1-1-1-3 3c0-2-1-4-2-7 0-1-1-2 0-3 1 0 2-1 3-2h1v-1h0 2v-2l1-1 1 1 4-2h1z" class="J"></path><path d="M399 316l1-1 2 3h-2l1 3v1h-1l-3 1c-2 0-4 1-6 2h-1c1-1 1-3 2-4l-1-2v-1c2-1 3-1 4-2s2 2 4 2v-2z" class="Z"></path><path d="M392 321l2 1 2-2 1 1c0-1 0-1 1-1s2 1 2 2l-3 1c-2 0-4 1-6 2h-1c1-1 1-3 2-4z" class="k"></path><path d="M381 322h3 1v-3h1 1l2-1c0-1 0 0 1-1l1 1v1l1 2c-1 1-1 3-2 4l-2 2v-1l-1 1-1-1-3 3c0-2-1-4-2-7z" class="W"></path><path d="M391 319l1 2c-1 1-1 3-2 4l-2 2v-1l-1 1-1-1c1-1 2-2 3-2 2-1 1-3 2-5z" class="a"></path><path d="M358 350c3 2 6 7 10 8h1c1 1 3 3 3 5h0-1-1l-1 2c2 2 4 4 5 6v4c5 6 9 12 12 19v1c0 3 2 5 3 8 0 2 0 3 1 4l-1 1c-1-1-1-2-2-3v-1c-2-1-3-3-5-4 0-1-2-1-2-2-1-1-2-2-2-3l-1 1s-1-1-2-1h0v1c1 3 6 5 8 6 1 1 1 2 2 3 2 2 4 4 5 7 0 2 1 2 0 3h0-1c0 1 0 2 1 3l-6-5-21-12c-6-3-14-6-21-4h-2-1l1-1c1-1 2-1 4-1h0v-1c1 0 2 0 2-1h1v-1l-2-1h1c-5-5-13-6-17-13l2-1c1 0 1 0 2-1l-1-1c0-1 2-2 2-3v-3l2-2v-4c2-3 0-7 3-9 1 1 1 3 2 5l1-1c0-1 1-2 2-3l2-1h2c1 0 3-1 4-2 1 0 1 0 2-1l1 1c1-1 2-1 3-2z" class="AE"></path><path d="M347 361s1 0 2 1h2l1 1c-2 0-3 1-4 3 1 3 1 5 3 7l2 2h0c-3-1-4-1-6-3v-1l-1 1-1-1c-1-3 0-7 2-10z" class="l"></path><path d="M346 372l1-1v1c2 2 3 2 6 3 2 0 5 1 7 1h5c0 1-1 1-1 2-4 1-6 2-9 0h-1c-4-1-6-3-8-6zm14-9h0l-2-2h-1l2-1 10 5c2 2 4 4 5 6v4c-4-6-9-8-14-12z" class="C"></path><path d="M344 355l2-1v1h1c2 2 4 0 6 2 0 1 0 1 1 1 2 0 3 1 5 2l-2 1h1l2 2h0c-3-1-5-1-8-1v1l-1-1h-2c-1-1-2-1-2-1h-4l1-2h0c-1 0-1 1-2 1v-2h0c0-1 1-2 2-3z" class="K"></path><path d="M344 359h5l6 1v1c-1 0-2 0-3 1v1l-1-1h-2c-1-1-2-1-2-1h-4l1-2z" class="AI"></path><path d="M344 355l2-1v1h1c2 2 4 0 6 2h-5c0 1 0 1 1 1v1h-5 0c-1 0-1 1-2 1v-2h0c0-1 1-2 2-3z" class="J"></path><path d="M358 350c3 2 6 7 10 8h1c1 1 3 3 3 5h0-1-1l-1 2-10-5c-2-1-3-2-5-2-1 0-1 0-1-1-2-2-4 0-6-2h-1v-1h2c1 0 3-1 4-2 1 0 1 0 2-1l1 1c1-1 2-1 3-2z" class="w"></path><path d="M358 350c3 2 6 7 10 8h1c1 1 3 3 3 5h0-1c-2-2-5-5-8-7-2-1-5-2-7-4h-1c1-1 2-1 3-2z" class="N"></path><path d="M347 355c8-1 16 3 22 8h1l-1 2-10-5c-2-1-3-2-5-2-1 0-1 0-1-1-2-2-4 0-6-2z" class="g"></path><path d="M336 363c2-3 0-7 3-9 1 1 1 3 2 5l1-1h0v2c1 0 1-1 2-1h0l-1 2h4c-2 3-3 7-2 10l1 1c2 3 4 5 8 6h1c3 2 5 1 9 0h-1c-1 2-2 2-4 2h-3-1c3 1 5 2 8 2 1 2 1 2 2 2h0 1c0 1 0 1 1 2h0c1 0 1 0 2 1 1 0 2 1 3 3h0v1c-1 1-3 2-4 3h0-1l-3-3-11-6c0 1-1 1-1 2l-3-2-3-1 3 2c-4 0-6-2-9-4l-9-5c1 0 1 0 2-1l-1-1c0-1 2-2 2-3v-3l2-2v-4z" class="b"></path><path d="M366 388l6 2v1c-1 1-3 2-4 3h0-1l-3-3 2-3z" class="AP"></path><path d="M341 372c3 5 8 7 14 10 3 2 7 4 11 6l-2 3-11-6c0 1-1 1-1 2l-3-2-3-1c-1 0-3-2-4-3l2-1-2-2v-1c0-1-1-2-1-3v-2z" class="AH"></path><path d="M344 380l9 5c0 1-1 1-1 2l-3-2-3-1c-1 0-3-2-4-3l2-1z" class="h"></path><path d="M336 363c2-3 0-7 3-9 1 1 1 3 2 5l1-1h0v2c1 0 1-1 2-1h0l-1 2c0 1-1 2-2 3h0v8 2c0 1 1 2 1 3v1l2 2-2 1c1 1 3 3 4 3l3 2c-4 0-6-2-9-4l-9-5c1 0 1 0 2-1l-1-1c0-1 2-2 2-3v-3l2-2v-4z" class="w"></path><path d="M339 376c0-1-1-2-1-3v-2l1-1-1-2c1-1 1-2 2-3h0l1-1v8 2c0 1 1 2 1 3v1l2 2-2 1c-1-1-3-2-4-3v-1c1 1 2 1 3 1 0-1-1-2-2-2z" class="a"></path><path d="M336 363c2-3 0-7 3-9 1 1 1 3 2 5l-1 1c0 2-2 4-2 6l-1 1v3c0 1-1 1-1 2l-1 4h-1-1l1-2c0-1 1-1 1-2v-1c1-2 1-3 1-4v-4z" class="l"></path><path d="M336 367c0 1 0 2-1 4v1c0 1-1 1-1 2l-1 2h1 1c1 0 1-1 2 0v-1h1v1h1c1 0 2 1 2 2-1 0-2 0-3-1v1c1 1 3 2 4 3s3 3 4 3l3 2c-4 0-6-2-9-4l-9-5c1 0 1 0 2-1l-1-1c0-1 2-2 2-3v-3l2-2z" class="Z"></path><path d="M331 377l9 5c3 2 5 4 9 4l-3-2 3 1 3 2c0-1 1-1 1-2l11 6 3 3h1 0c1-1 3-2 4-3v2h2l1 2v1c1 3 6 5 8 6 1 1 1 2 2 3 2 2 4 4 5 7 0 2 1 2 0 3h0-1c0 1 0 2 1 3l-6-5-21-12c-6-3-14-6-21-4h-2-1l1-1c1-1 2-1 4-1h0v-1c1 0 2 0 2-1h1v-1l-2-1h1c-5-5-13-6-17-13l2-1z" class="q"></path><path d="M363 401c1-1 1-2 3-2 1-1 1 0 2 0l1 1h-2l1 2v1c2 1 4 2 5 3l5 3 3 1 2 1 1 1v1l-21-12z" class="l"></path><path d="M346 384l3 1 3 2c0-1 1-1 1-2l11 6 3 3h1v3l-2-1c-5-4-12-6-17-10l-3-2z" class="k"></path><path d="M368 394c1-1 3-2 4-3v2h2l1 2v1c1 3 6 5 8 6 1 1 1 2 2 3 2 2 4 4 5 7-1 0-2 0-2-1-6-5-14-9-20-14v-3h0z" class="AN"></path><path d="M368 394c1-1 3-2 4-3v2h2l1 2v1c1 3 6 5 8 6 1 1 1 2 2 3-6-4-11-8-17-11z" class="T"></path><path d="M400 322h1c2 1 3 1 6 1l3 2v-2l3 2 4 2c2 2 5 4 7 7 1 0 2 1 3 2 1 0 2 1 2 2 0 3 1 4 2 7h0c2 5 6 10 8 15l1 5c1 2 3 3 4 5l-5 2h-5v1h-1v-4l-1-2v-3c-1 1-1 2-2 3l-1 1h-1c-1 0-2 0-3 1-1 0-2 0-4 1s-4 3-6 5h-1c-2 5-4 10-5 15l-2 1c-1 1-1 0-2 1v-2c-1-1-1-2-2-2l-5-11c-1 0-1 0-1 1h0c-2-2-3-4-3-6-2-3-4-8-4-12l-1-2v-4c0-2 2-6 2-9-2 1-3 1-5 0l-1-6v-2c1-4 1-6 3-10l2-2h1c2-1 4-2 6-2l3-1z" class="l"></path><path d="M435 359h0c2 2 2 4 2 7 0 0 0 1 1 1 0 2-1 3 0 4l1 1h-5v1h-1v-4-1l1-1c1-1 0-2 0-3v-2l1-3z" class="Z"></path><path d="M435 359c2 2 2 4 2 7 0 0 0 1 1 1 0 2-1 3 0 4h-2v-1-2c-1-3-1-6-1-9z" class="J"></path><defs><linearGradient id="Az" x1="404.64" y1="326.236" x2="420.335" y2="343.736" xlink:href="#B"><stop offset="0" stop-color="#2f0907"></stop><stop offset="1" stop-color="#511e1e"></stop></linearGradient></defs><path fill="url(#Az)" d="M403 328c10 2 17 6 24 13 1 1 2 2 3 4h-1c-1 0-1 0-2-1l-1-2h-2 0l-1-1c-1 0-2-2-4-3-6-4-11-6-19-7l1-1v-1c1-1 1-1 2-1z"></path><path d="M394 337v-1c3-1 5-1 7 0l8 1c-1-1-3-1-4-2 12 3 22 10 28 21l2 3-1 3v2c0 1 1 2 0 3l-1 1v1l-1-2v-3-2c-1-6-6-11-10-15-8-7-17-11-28-10z" class="W"></path><path d="M400 322h1c2 1 3 1 6 1l3 2v-2l3 2 4 2c2 2 5 4 7 7 1 0 2 1 3 2 1 0 2 1 2 2 0 3 1 4 2 7h0-1c-1-2-2-3-3-4-7-7-14-11-24-13-3-1-5-1-8-1-3 1-6 3-8 6-1 2-1 4-2 6v-2c1-4 1-6 3-10l2-2h1c2-1 4-2 6-2l3-1z" class="AM"></path><path d="M424 334c1 0 2 1 3 2 1 0 2 1 2 2 0 3 1 4 2 7h0-1c-1-2-2-3-3-4 1-2-2-3-3-4v-3z" class="h"></path><path d="M400 322h1c2 1 3 1 6 1l3 2c1 0 2 1 3 2l-1 1c-1 0-2-1-2-1-2 0-5-1-6 0h-1-8c-3 1-6 3-8 6-1 2-1 4-2 6v-2c1-4 1-6 3-10l2-2h1c2-1 4-2 6-2l3-1z" class="AH"></path><path d="M400 322h1c2 1 3 1 6 1l-1 1v2c-1 0-3 0-4-1-3 0-6 0-9 1h-1l-1-1c2-1 4-2 6-2l3-1z" class="AS"></path><path d="M400 322h1c2 1 3 1 6 1l-1 1v2c-1 0-3 0-4-1l1-1h0c-1 0-3 0-5-1h-1l3-1z" class="AQ"></path><path d="M395 327c3 0 5 0 8 1-1 0-1 0-2 1v1l-1 1c-2 0-5 0-7 2h3v1c-2 1-3 1-5 4h0l3-1h0c11-1 20 3 28 10 4 4 9 9 10 15v2c-1 1-1 2-2 3h-1c-1-1-1-2-1-3-1 0-1-1-2-1-1-2-3-4-4-6l-3-3v2l-1-1-1 1 1 1-1 1c-2-3-3-6-6-9h-3-1v1l-1-1c-1 0-1 0-1 1l-1-1-1 1h0c1 2 1 2 1 4-1 0-1 0-1-1h-1v2c-1-1-1-1-2-1l-1 1v-1c0 1-1 1-1 2-1 0-1 1-1 2l-1 1c0-2-1-3-1-4-1 1 0 3-1 4h-1c0-1-1-2-1-3l-1-1c-1 1-1 2-1 3v1 1l-1-2v-4c0-2 2-6 2-9-2 1-3 1-5 0l-1-6c1-2 1-4 2-6 2-3 5-5 8-6z" class="U"></path><path d="M419 353l2-1c2 2 4 4 6 7 1 1 1 4 1 5-1 0-1-1-2-1-1-2-3-4-4-6 0-2-1-3-3-4z" class="C"></path><path d="M391 344c1-2 3-3 5-4 6 1 10 1 16 5l3 2c1 1 5 4 6 5l-2 1c2 1 3 2 3 4l-3-3v2l-1-1-1 1 1 1-1 1c-2-3-3-6-6-9 0-1-1-1-2-2h-5v1l-3-2v1c-1-1-1-1-1-2h-1-1c-1 0-2 1-2 1l-2-1s-1 1-2 1v-1l-1-1z" class="q"></path><path d="M401 346l1-1c2-1 5-1 7 1l2 1 2 2c2 2 4 2 6 4 2 1 3 2 3 4l-3-3v2l-1-1-1 1 1 1-1 1c-2-3-3-6-6-9 0-1-1-1-2-2h-5v1l-3-2z" class="i"></path><path d="M391 344l1 1v1c1 0 2-1 2-1l2 1s1-1 2-1h1 1c0 1 0 1 1 2v-1l3 2v-1h5c1 1 2 1 2 2h-3-1v1l-1-1c-1 0-1 0-1 1l-1-1-1 1h0c1 2 1 2 1 4-1 0-1 0-1-1h-1v2c-1-1-1-1-2-1l-1 1v-1c0 1-1 1-1 2-1 0-1 1-1 2l-1 1c0-2-1-3-1-4-1 1 0 3-1 4h-1c0-1-1-2-1-3l-1-1c-1 1-1 2-1 3v1 1l-1-2v-4c0-2 2-6 2-9v-1z" class="w"></path><path d="M393 351l2 1v2h1l1-4c1 1 2 3 2 4s-1 1-1 2c-1 0-1 1-1 2l-1 1c0-2-1-3-1-4-1 1 0 3-1 4h-1c0-1-1-2-1-3s0-3 1-5z" class="W"></path><path d="M391 344l1 1v1c1 0 2-1 2-1l2 1s1-1 2-1h1 1c0 1 0 1 1 2v2h-1v-1h-1c0 1 0 0-1 1v-3l-1 1v2h-1l-1-1c-1 1-2 2-2 3-1 2-1 4-1 5l-1-1c-1 1-1 2-1 3v1 1l-1-2v-4c0-2 2-6 2-9v-1z" class="g"></path><defs><linearGradient id="BA" x1="399.174" y1="376.231" x2="414.262" y2="369.038" xlink:href="#B"><stop offset="0" stop-color="#bc3e38"></stop><stop offset="1" stop-color="#d95246"></stop></linearGradient></defs><path fill="url(#BA)" d="M399 355l1-1c1 0 1 0 2 1v-2h1c0 1 0 1 1 1 0-2 0-2-1-4h0l1-1 1 1c0-1 0-1 1-1l1 1v-1h1 3c3 3 4 6 6 9l1-1-1-1 1-1 1 1v-2l3 3c1 2 3 4 4 6 1 0 1 1 2 1 0 1 0 2 1 3h1l-1 1h-1c-1 0-2 0-3 1-1 0-2 0-4 1s-4 3-6 5h-1c-2 5-4 10-5 15l-2 1c-1 1-1 0-2 1v-2c-1-1-1-2-2-2l-5-11c-1 0-1 0-1 1h0c-2-2-3-4-3-6-2-3-4-8-4-12v-1-1c0-1 0-2 1-3l1 1c0 1 1 2 1 3h1c1-1 0-3 1-4 0 1 1 2 1 4l1-1c0-1 0-2 1-2 0-1 1-1 1-2v1z"></path><path d="M410 358h1c2 6 4 10 4 17h-1c-1-1 0-4-1-5 0-4-2-8-3-12z" class="h"></path><path d="M399 354v1c1 3 2 6 4 9 1 5 2 9 3 13 1 1 2 3 1 4 0 3 2 5-1 7-1-1-2-3-3-4l-1-1 1-1c1-2-1-6-2-8v-1l-1-1v-2l-1-1v-2c-1-1-1 0-1-2v-1l-2-5 1-1c0-1 0-2 1-2 0-1 1-1 1-2z" class="m"></path><path d="M398 356c0 5 1 9 3 13l1 3v1c1 1 1 2 1 3l3 6 1-1h-1v-4c1 1 2 3 1 4 0 3 2 5-1 7-1-1-2-3-3-4l-1-1 1-1c1-2-1-6-2-8v-1l-1-1v-2l-1-1v-2c-1-1-1 0-1-2v-1l-2-5 1-1c0-1 0-2 1-2z" class="a"></path><path d="M393 359h1c1-1 0-3 1-4 0 1 1 2 1 4l2 5v1c0 2 0 1 1 2v2l1 1v2l1 1v1c1 2 3 6 2 8l-1 1 1 1v4l-5-11c-1 0-1 0-1 1h0c-2-2-3-4-3-6-2-3-4-8-4-12v-1-1c0-1 0-2 1-3l1 1c0 1 1 2 1 3z" class="m"></path><path d="M394 365c2 4 3 8 4 12-1 0-1 0-1 1h0c-2-2-3-4-3-6v-7z" class="AO"></path><path d="M390 360v-1-1c0-1 0-2 1-3l1 1c0 1 1 2 1 3 0 2 1 4 1 6v7c-2-3-4-8-4-12z" class="Z"></path><path d="M399 355l1-1c1 0 1 0 2 1v-2h1c0 1 0 1 1 1 0-2 0-2-1-4h0l1-1 1 1c0-1 0-1 1-1l1 1v-1h1 3c3 3 4 6 6 9l1-1-1-1 1-1 1 1v-2l3 3c1 2 3 4 4 6 1 0 1 1 2 1 0 1 0 2 1 3h1l-1 1h-1c-1 0-2 0-3 1-1 0-2 0-4 1s-4 3-6 5c0-7-2-11-4-17h-1c0-1-1-2-1-3-1 0-1-1-1-2l-1 1v1h-1v7l-2-6-1 1c0 2 2 7 1 9l-1-2c-2-3-3-6-4-9z" class="W"></path><path d="M411 358c0-1-1-2-1-2l1-1h0c1 1 1 1 2 1 3 4 4 8 7 12 0 1 0 1 1 2-2 1-4 3-6 5 0-7-2-11-4-17z" class="k"></path><path d="M417 358l1-1-1-1 1-1 1 1v-2l3 3c1 2 3 4 4 6 1 0 1 1 2 1 0 1 0 2 1 3h1l-1 1h-1c-1 0-2 0-3 1-1 0-2 0-4 1-1-1-1-1-1-2h0c0-1 1-2 1-3 0 0-1-1-1-2-1-1-2-3-3-5z" class="c"></path><path d="M432 364v3l1 2v4h1v-1h5l5-2h0l1 1c1 2 2 4 3 7 0 1 2 3 1 4h0l-2-1-1 1c-1 1-1 2-1 3l1 2-1 2c1 2 1 3 0 6-1-1-1-2-2-2l-2 6c1 2 2 5 3 8h0c2 3 4 5 7 7 1 1 1 2 2 3l3 1 2 2h3c-1 1-3 2-4 2l-1-2c-2 1-2 1-3 1h-1-1-4l-1 1c-2 3-5 4-7 9-1 1-2 3-2 5l-1 2v1c-2-1-2-2-2-3v-5l-3 6c0 3-1 5-1 7l-1 3h-1c0 2 0 3-1 4l-1 1c-1-1-1-3-2-4 0-1-1-3-1-4h0c1-1 1-2 2-4-1-1-2-3-3-4-1-3-3-6-4-9-1-2-2-3-3-4v-1l-3-4-2 2-1-5v-1c-2-3-2-5-2-9v-2c1-1 1-2 1-3v-1l1-5h0v-4c1-5 3-10 5-15h1c2-2 4-4 6-5s3-1 4-1c1-1 2-1 3-1h1l1-1c1-1 1-2 2-3z" class="t"></path><path d="M433 382c1 1 2 1 2 3h-1c-1 2 1 2-1 3l-1-1-2 1c-2-2-2-2-5-2h-1c2-1 1-1 2-2h2v-2 1l2-1c1 1 2 1 3 2v-2z" class="AO"></path><path d="M421 370c2-1 3-1 4-1l1 1c-1 1-3 2-3 3-1 2-4 4-5 6-2 3-4 5-4 8 0 2-2 3-2 6h1c-1 2-3 5-5 6l1-5h0v-4c1-5 3-10 5-15h1c2-2 4-4 6-5z" class="AS"></path><path d="M432 364v3l1 2v4h1v-1h5l5-2h0l1 1h-1c-2 3-3 4-6 5-2 1-3 2-6 2l-2 2-4 1c-2 0-3 2-5 2v-1c0-1 1-1 1-2v-1c1-4 6-3 8-6-1-1-1-1-1-2v-1c-1-1-1 0-3 0l-1-1c1-1 2-1 3-1h1l1-1c1-1 1-2 2-3z" class="Aa"></path><path d="M432 364v3l1 2v4h1v-1h5l5-2h0l1 1h-1c-2 1-4 1-6 2s-3 1-5 2l-1-1-1 1c-1 1-2 1-3 1l-6 3c1-4 6-3 8-6-1-1-1-1-1-2v-1c-1-1-1 0-3 0l-1-1c1-1 2-1 3-1h1l1-1c1-1 1-2 2-3z" class="AW"></path><path d="M445 371c1 2 2 4 3 7 0 1 2 3 1 4h0l-2-1-1 1c-1 1-1 2-1 3l1 2-1 2c1 2 1 3 0 6-1-1-1-2-2-2l-2 6h0c-1 3-3 6-6 8-1 0-2 1-3 0 0 0-1-1-1-2h-1c1-1 1-1 1-2h0l-1 1-1-1c0-2 1-3 0-5h0c1-1 1-3 1-5-2 0-2-2-4-2-1 0-2 0-3-1 0-2 1-2 2-4 3 0 3 0 5 2l2-1 1 1c2-1 0-1 1-3h1c0-2-1-2-2-3v-1c-1 0-2 0-3-1l2-2c3 0 4-1 6-2 3-1 4-2 6-5h1z" class="c"></path><path d="M448 378c0 1 2 3 1 4h0l-2-1c-1 0-3 0-5-1v-1c2-2 4-1 6-1z" class="m"></path><path d="M430 388l2-1 1 1c2-1 0-1 1-3h1c1 2 0 7 0 9l-1 1h-1c0-2-2-5-3-7z" class="AR"></path><path d="M442 380c2 1 4 1 5 1l-1 1c-1 1-1 2-1 3l1 2-1 2c1 2 1 3 0 6-1-1-1-2-2-2v-7c-1-2-1-4-1-6z" class="N"></path><path d="M436 398v-4c2-2 2-3 3-5v-2h1 0c0 1 0 1 1 2 0 2 0 7-1 9l1 1c-1 3-3 6-6 8v-3h1v-2c0-1 1-1 2-2v-1-1c0-2 1-3 1-5h-1c0 2-1 3-2 4v1zm-11-12c3 0 3 0 5 2 1 2 3 5 3 7 0 1 0 1 1 2-1 2-1 3-1 4l1 1-2 2v-3-1-4c-1-1-1-2-2-3-2 0-2-2-4-2-1 0-2 0-3-1 0-2 1-2 2-4z" class="R"></path><path d="M436 398v-1c1-1 2-2 2-4h1c0 2-1 3-1 5v1 1c-1 1-2 1-2 2v2h-1v3c-1 0-2 1-3 0 0 0-1-1-1-2h-1c1-1 1-1 1-2h0l-1 1-1-1c0-2 1-3 0-5h0c1-1 1-3 1-5 1 1 1 2 2 3v4 1 3l2-2c1-1 1-3 2-4z" class="C"></path><path d="M424 386h1c-1 2-2 2-2 4 1 1 2 1 3 1 2 0 2 2 4 2 0 2 0 4-1 5h0c1 2 0 3 0 5l1 1 1-1h0c0 1 0 1-1 2h1c0 1 1 2 1 2v1 3h2l-1 1h-2c-1 0-1-1-3 0h0v4h-1c-1 0-1 0-2 1h0c-1 0-2 1-2 1 0 2 1 3 1 4l1 2h-2l-2 1v2l-3-3 5 10c0 1 0 1-1 2-1-3-3-6-4-9-1-2-2-3-3-4v-1l-3-4-2 2-1-5v-1c-2-3-2-5-2-9v-2c1-1 1-2 1-3v-1c2-1 4-4 5-6l5-5c2-1 4-2 6-2z" class="AR"></path><path d="M409 415v-5l1 1c0 1 1 2 1 3s1 3 1 4l-2 2-1-5z" class="m"></path><path d="M424 386h1c-1 2-2 2-2 4 1 1 2 1 3 1 0 1 0 2-1 3s-1 0-2 0l-1-1h0l-1-1h1c-1-1-1-1-2-1l-1 1h-2v1l-1-1c1-1 2-1 2-3v-1c2-1 4-2 6-2z" class="Z"></path><path d="M426 391c2 0 2 2 4 2 0 2 0 4-1 5h0c1 2 0 3 0 5l-1-1-1-2c0-1 0-1-1-2-1 0-1-1-2-1l-2-2 1-1c1 0 1 1 2 0s1-2 1-3z" class="J"></path><path d="M416 419v-3c-1-4-2-9-1-13 1-3 2-4 4-6 3 0 5 1 8 3l1 2 1 1 1 1 1-1h0c0 1 0 1-1 2h1c0 1 1 2 1 2v1 3h2l-1 1h-2c-1 0-1-1-3 0h0v4h-1c-1 0-1 0-2 1h0c-1 0-2 1-2 1 0 2 1 3 1 4l1 2h-2l-2 1v2l-3-3-2-5z" class="N"></path><path d="M423 418c-1-1-2-3-3-5 0-2 0-4 1-5l1 1c1 2 2 4 2 7l1 1h0c-1 0-2 1-2 1z" class="U"></path><path d="M422 409h3c1 2 1 2 3 3h0v4h-1c-1 0-1 0-2 1l-1-1c0-3-1-5-2-7z" class="T"></path><path d="M416 419v-3c-1-4-2-9-1-13 1-3 2-4 4-6 3 0 5 1 8 3l1 2v1c0 1-1 2 0 3l-2 2h-1v-2h0l-2-2c-1 1-2 1-3 2l-1 4c0 2 1 5 1 8v5h0c-1-1-2-2-2-3 0 0-1-1-2-1z" class="AG"></path><path d="M441 399h0c1 2 2 5 3 8h0c2 3 4 5 7 7 1 1 1 2 2 3l3 1 2 2h3c-1 1-3 2-4 2l-1-2c-2 1-2 1-3 1h-1-1-4l-1 1c-2 3-5 4-7 9-1 1-2 3-2 5l-1 2v1c-2-1-2-2-2-3v-5l-3 6c0 3-1 5-1 7l-1 3h-1c0 2 0 3-1 4l-1 1c-1-1-1-3-2-4 0-1-1-3-1-4h0c1-1 1-2 2-4-1-1-2-3-3-4 1-1 1-1 1-2l-5-10 3 3v-2l2-1h2l-1-2c0-1-1-2-1-4 0 0 1-1 2-1h0c1-1 1-1 2-1h1v-4h0c2-1 2 0 3 0h2l1-1h-2v-3-1c1 1 2 0 3 0 3-2 5-5 6-8z" class="t"></path><path d="M427 424c2-1 4-1 6-2l-3 8h-2c-1-2-1-4-1-6z" class="i"></path><path d="M437 427h1c1-2 4-4 6-5 1-1 1-1 2-1 1-2 2-2 4-2h6v1c-2 1-2 1-3 1h-1-1-4l-1 1c-2 3-5 4-7 9-1 1-2 3-2 5-1-1-1-2-1-4s0-3 1-5z" class="AM"></path><path d="M428 412c3 3 5 6 5 10-2 1-4 1-6 2h-2l-1-2c0-1-1-2-1-4 0 0 1-1 2-1h0c1-1 1-1 2-1h1v-4z" class="N"></path><path d="M425 417c1 0 1 1 2 1 1 1 2 1 3 1l1 1c-2 2-3 1-4 4h-2l-1-2c0-1-1-2-1-4 0 0 1-1 2-1z" class="AL"></path><path d="M430 431h0c2-1 2-3 3-5 0-1 1-1 1-2v-3h1 2v2 4c-1 2-1 3-1 5s0 3 1 4l-1 2v1c-2-1-2-2-2-3v-5l-3 6c0 3-1 5-1 7l-1 3h-1c0-4 0-7 1-10v-5l1-1z" class="a"></path><path d="M425 424h2c0 2 0 4 1 6h2v1l-1 1v5c-1 3-1 6-1 10 0 2 0 3-1 4l-1 1c-1-1-1-3-2-4 0-1-1-3-1-4h0c1-1 1-2 2-4-1-1-2-3-3-4 1-1 1-1 1-2l-5-10 3 3v-2l2-1h2z" class="R"></path><path d="M425 424h2c0 2 0 4 1 6h2v1l-1 1v5c-1-2-2-2-3-3h-1c-1-3 0-6 0-9l-2-1h2z" class="q"></path><path d="M425 434l1-2c1-1 1-1 2-1l1 1v5c-1-2-2-2-3-3h-1z" class="AC"></path><path d="M426 434c1 1 2 1 3 3-1 3-1 6-1 10 0 2 0 3-1 4l-1 1c-1-1-1-3-2-4 0-1-1-3-1-4h0c1-1 1-2 2-4l1-6z" class="q"></path><path d="M441 399h0c1 2 2 5 3 8h0c2 3 4 5 7 7 1 1 1 2 2 3-1 0-2-1-3-1-2-1-2-2-3-2-2 0-2 0-3-1h-2c-1 1-1 2-1 3s0-1 0 1h-1l-1-1c-1-2-1-2-3-2v1h-1l-3-3h-1 2l1-1h-2v-3-1c1 1 2 0 3 0 3-2 5-5 6-8z" class="s"></path><path d="M441 399h0c1 2 2 5 3 8h0l-2-1h-2l-2 2-1 1c-1 1-2 2-3 2h-2v-3-1c1 1 2 0 3 0 3-2 5-5 6-8z" class="T"></path><path d="M432 408c2 0 3 0 5 1h0c-1 1-2 2-3 2h-2v-3z" class="z"></path><path d="M445 121h6v2l-3 12c-1 3-1 6-2 9l-1 3c0 2-2 6-1 8h-1v3h-2c-2 0-3 1-5 1h-1v1l-2 1v1h0l2 2v1c20 5 33 15 46 30 3 3 7 6 9 10v1l-7-8c-1 0-2 0-3 1h-1c0 1 0 1-1 2-5 1-10 0-16 1-3-1-6-1-10 1h0-5-3l-3 1c-1 0-2 0-3 1h-1c-2 1-3 3-4 5v1 6l1 4v6c2 5 5 9 9 12l3 3c2 1 4 1 6 1 4 0 9 1 12 0h1c1 0 2 0 4-1h3 1c-2 1-3 2-5 4h0l-6 3h-1l-6 5c0 1 0 1-1 1 0 1-4-3-5-4h-4 0c-4-2-7-2-12-1h0c-1 1-2 1-2 2-3-1-5-2-7-1l-1 1 2 2c2 2 2 4 3 7h0v3c-2 1-3 2-3 3h-1c-2 2-3 4-5 5v1l1 1v1 4c-1 1-1 1-1 2 1 1 1 1 2 1-1 3-2 5-4 8l-1 1 1 1v1c-1 0-1 1-2 1h-1l-2-1h-1c-1 0-2 0-3-1-4 1-8 1-12 1 0 1 0 1 1 1l-1 1c0 1 1 2 2 3 6 0 12 1 16 5 2 2 4 5 5 8 5 7 8 14 11 22l-1 3c-1 0-1-1-2-1v1c-1-1-2-2-3-2-2-3-5-5-7-7l-4-2-3-2v2l-3-2c-3 0-4 0-6-1v-1l-1-3h2l-2-3-1 1-1-1c0-1 0-2 1-3h-2c-1 1 0 1-1 2-1 0-1-1-2-2h-1l-4 2-1-1-1 1v2h-2 0-3c-1 0-2 1-3 1 0 0 0-1-1-1l-1-1c0 2-1 4-1 5l-3 2v1h-2c-1-2-2-3-3-5-1 1 0 1-1 1-3-3-4-7-6-11v1h-1v-1l-10-22-6-15 1-2v-5c-2-4-3-9-3-13l-1-1-2-7-5-11h0c-2-3-3-6-4-9l-2 1v1c0 1-1 2-1 3l-2-4c-3-4-4-8-8-12-4-8-7-18-13-25a30.44 30.44 0 0 0-8-8c-2 0-4-2-5-3l-7-4c1 1 2 2 4 3l3 4c-1 1-1 1-1 3 2 1 3 3 4 5l2 3 2 2c1 2 2 4 3 5 1 4 1 7 1 11h0l-1-1c-2 3-3 5-6 7h-2c-3 2-5 3-7 4s-7 2-8 3l-1 1h0v-3c-5-9-11-18-18-25-4-5-8-9-12-13-13-12-30-22-46-28-3-1-10-4-13-2h-2l-1 1h-2c-2-2-1-3-1-5l-2-1 3-1c0-1 0-1-1-2-4-3-14-2-19-1-9 2-16 6-23 12v-1c0-5 9-12 13-16l1-1v-3c2-3 5-2 8-3h1-6l-2-2c1 0 2-1 3-1 3 2 9 1 13 1h10v1h-1c-1-1-2 0-4 0 1 0 3 0 4 1l80 1 124-1h13 22c2 0 7 1 8 0 1 0 1-1 2-1l1-1 1-1h0l1-1 4-2c3-2 5-3 9-3z" class="AK"></path><path d="M412 140c7 1 13 6 19 9l-1 1h-4-1v-2c-1-1-2-2-4-3h0 1c-3-2-7-3-10-5z" class="U"></path><path d="M368 152h0l-1-1h-1v-1h-1c2-2 3-3 5-3l2-1c0-1 0-1 1-1 2-1 4-2 6-1-1 0-2 1-4 2-1 0-2 1-3 1v2h2l-6 3zm63-3l3 2c2 1 4 3 5 4s2 1 3 2l1 1h-2c-2-1-3-1-4-2s-1-1-2-1-2 0-2-1h0c-2-2-5-3-7-4h4l1-1z" class="T"></path><path d="M357 149l3-1 1 1h2 1v1h-1c0 1 0 1-1 2h1c1 1 1 0 2 0l-2 2h-1-2-1c-2 0-3 1-3 3l-1 1-3 4-1-1c0-1 0-1 1-3h-1c0-1 2-2 2-3v-1h-2c-1 1-2 1-4 2l2-2 8-5z" class="g"></path><path d="M357 149l3-1 1 1h2 1 0c-1 1-2 1-3 1-3 0-6 3-8 5v-1h-2c-1 1-2 1-4 2l2-2 8-5z" class="N"></path><path d="M374 149c2 0 3-1 5-1v1h0c-1 0-1 0-2 1 2 1 3 0 5 0l-4 1-2 2c-2 1-3 2-5 4-1 0-3 0-4 1l-1 1-1 1c-4 1-5 3-7 6-2 2-3 3-4 6 0 0-1 1-1 2l-1-1c2-6 5-12 9-18l-6 3 1-1c0-2 1-3 3-3h1 2l-1 3h1c2 0 3-2 5-3l1-2 6-3z" class="J"></path><path d="M374 149c2 0 3-1 5-1v1h0c-1 0-1 0-2 1 2 1 3 0 5 0l-4 1c-3 0-5 1-7 3h-1c-1 1-1 1-2 1 0 1-1 1-2 1l1-2 1-2 6-3z" class="AC"></path><path d="M445 121h6v2l-3 12c-1 3-1 6-2 9l-1 3c0 2-2 6-1 8h-1c-1-2-3-4-4-7-5-6-8-12-13-17-4-1-9-1-13-1-9 0-19 1-29 0h13 22c2 0 7 1 8 0 1 0 1-1 2-1l1-1 1-1h0l1-1 4-2c3-2 5-3 9-3z" class="AO"></path><path d="M432 126c3 3 8 3 10 6h4v3l-3 7-1 2c-1 0-1 0-2-1h-1v-4c-1-2-2-3-3-4-1-3-4-3-6-4 0 0-1-1-1-2l1-1 1-1h0l1-1z" class="c"></path><path d="M436 131c2 0 2 0 4 1 1 3 1 5 2 7v3h1l-1 2c-1 0-1 0-2-1h-1c1-3 1-8 0-10-1-1-1-1-3-1v-1z" class="Z"></path><path d="M432 126c3 3 8 3 10 6h4v3c-2 0-2 1-4 0 0-1 0-2-1-3l-2-1v-1c-3 0-6-1-8-3l1-1z" class="R"></path><path d="M430 128c2 2 4 2 6 3v1c2 0 2 0 3 1 1 2 1 7 0 10v-4c-1-2-2-3-3-4-1-3-4-3-6-4 0 0-1-1-1-2l1-1z" class="W"></path><path d="M445 121h6v2c-2 2-3 4-4 6l-1 3h-4c-2-3-7-3-10-6l4-2c3-2 5-3 9-3z" class="C"></path><path d="M436 124s0 2 1 2c0 1 1 1 1 2h0c2 1 3 2 5 3h3v-1l1-1-1 3h-4c-2-3-7-3-10-6l4-2z" class="J"></path><path d="M373 137c10 0 20-2 31-1h-6c-3 1-6 1-9 1-13 2-28 5-40 12 2 0 4 0 6-1l2 1-8 5-2 2h1c-3 0-5 1-8 3l-2 1c-1 1-2 2-3 2l-5 1 2-3c-1-1-1-1-1-3 0-1 1-2 1-2l3-3c-1 0-1 0-2 1h-1s0-1 1-1l-1-1c0 1 0 0-1 1h-1c1-1 1-3 2-4 2-2 4-3 6-4 1-1 2-1 3-2 1 0 2-1 3-1l1-1c3 0 5-1 8-1 7-1 13-2 20-2z" class="AU"></path><path d="M349 149c2 0 4 0 6-1l2 1-8 5-2 2h1c-3 0-5 1-8 3l-2 1c-1 1-2 2-3 2l-5 1 2-3c2-2 3-3 5-4 3-3 8-6 12-7z" class="C"></path><path d="M337 156h1 1 1c2-2 4-4 7-4v1h0c-2 1-3 2-4 3-2 1-3 2-5 3v1c-1 1-2 2-3 2l-5 1 2-3c2-2 3-3 5-4z" class="K"></path><path d="M345 140c3 0 5-1 8-1 7-1 13-2 20-2-1 1-2 1-4 2h-1c-1 0-2 0-4 1h-3c-6 2-13 6-18 8-2 1-6 4-8 4-1 0-1 0-2 1h-1s0-1 1-1l-1-1c0 1 0 0-1 1h-1c1-1 1-3 2-4 2-2 4-3 6-4 1-1 2-1 3-2 1 0 2-1 3-1l1-1z" class="AT"></path><defs><linearGradient id="BB" x1="398.004" y1="139.662" x2="398.272" y2="148.76" xlink:href="#B"><stop offset="0" stop-color="#130a09"></stop><stop offset="1" stop-color="#2b0f0d"></stop></linearGradient></defs><path fill="url(#BB)" d="M379 144c5-2 10-3 15-4 6-1 12-2 18 0 3 2 7 3 10 5h-1 0c2 1 3 2 4 3v2l-2-1-4 2h-7l-8 1-8 1c-7 2-13 4-19 7l-5 2-5 5c1-1 1-2 2-3l-1-1 2-2c-1 0-1 0-2 1-1 0-2 1-3 1l6-6c2-2 3-3 5-4l2-2 4-1c-2 0-3 1-5 0 1-1 1-1 2-1h0v-1c-2 0-3 1-5 1h-2v-2c1 0 2-1 3-1 2-1 3-2 4-2z"></path><path d="M401 143h6 1c3 1 5 1 7 4l-5-1c-1 0-2-1-2-1l-1-1c-2 0-4 0-6-1z" class="U"></path><path d="M389 145h1c4-2 7-2 11-2 2 1 4 1 6 1l1 1s1 1 2 1h-6-2c-4 0-9-1-13-1z" class="T"></path><path d="M410 146l5 1 8 2-4 2h-7s2 0 3-1c-2-1-3-2-5-2s-4-1-6-2h6z" class="g"></path><path d="M389 145c4 0 9 1 13 1-4 1-8 1-12 2l-8 2c-2 0-3 1-5 0 1-1 1-1 2-1h0v-1c2-2 7-3 10-3z" class="l"></path><path d="M402 146h2c2 1 4 2 6 2s3 1 5 2c-1 1-3 1-3 1l-8 1-1-1c-1-1-1 0-1-1h3v-1l-5-1c-3-1-7 0-10 0 4-1 8-1 12-2z" class="J"></path><path d="M390 148c3 0 7-1 10 0l5 1v1h-3c0 1 0 0 1 1l1 1-8 1v-1c1-1 1-1 1-2-1-1-3-1-4-1-4 1-7 2-10 2v1c-2 0-4 1-7 1l2-2 4-1 8-2z" class="R"></path><path d="M393 149c1 0 3 0 4 1 0 1 0 1-1 2v1c-7 2-13 4-19 7l-5 2-5 5c1-1 1-2 2-3l-1-1 2-2c-1 0-1 0-2 1-1 0-2 1-3 1l6-6c2-2 3-3 5-4 3 0 5-1 7-1v-1c3 0 6-1 10-2z" class="Z"></path><path d="M393 149v2l-10 4 1-3h-1v-1c3 0 6-1 10-2z" class="h"></path><path d="M383 152h1l-1 3c-4 1-9 4-10 7h-1l-5 5c1-1 1-2 2-3l-1-1 2-2c-1 0-1 0-2 1-1 0-2 1-3 1l6-6c2-2 3-3 5-4 3 0 5-1 7-1z" class="k"></path><defs><linearGradient id="BC" x1="390.911" y1="151.303" x2="406.878" y2="173.531" xlink:href="#B"><stop offset="0" stop-color="#0c0a0b"></stop><stop offset="1" stop-color="#262729"></stop></linearGradient></defs><path fill="url(#BC)" d="M423 149l2 1h1c2 1 5 2 7 4h0c0 1 1 1 2 1s1 0 2 1 2 1 4 2c-2 0-3 1-5 1h-1v1l-2 1v1h0l2 2v1c-3-1-7-2-11-2-8 0-16 2-24 3l-1 1-3 1c-6 1-13 4-18 7-2 2-3 4-6 5v-4c0-3 0-5 1-8h0c1-3 2-5 4-8 6-3 12-5 19-7l8-1 8-1h7l4-2z"></path><path d="M423 149l2 1h1c2 1 5 2 7 4h0l-14-3 4-2z" class="C"></path><path d="M400 166h0l3-1c2-1 5-1 7-1l17-2v-2h1l2-2-1-1 1-1c2-1 5 0 7 0 1 1 2 1 4 2-2 0-3 1-5 1h-1v1l-2 1v1h0l2 2v1c-3-1-7-2-11-2-8 0-16 2-24 3z" class="r"></path><path d="M330 152h1c1-1 1 0 1-1l1 1c-1 0-1 1-1 1h1c1-1 1-1 2-1l-3 3s-1 1-1 2c0 2 0 2 1 3l-2 3 5-1c1 0 2-1 3-2l2-1c3-2 5-3 8-3h-1c2-1 3-1 4-2h2v1c0 1-2 2-2 3h1c-1 2-1 2-1 3l1 1 3-4 6-3c-4 6-7 12-9 18-4 10-5 23-4 33h0l1 8-4 1-1-1c0 1-1 2-1 2-2 1-3 0-4 1l-1-1h0c-1-1-1 0-2-2v2h-1c-2 1-4 0-6 1l1 1-1 1v2l1 2-2 1v1c0 1-1 2-1 3l-2-4c-3-4-4-8-8-12-4-8-7-18-13-25a30.44 30.44 0 0 0-8-8c1-1 2-1 4-1l1 2-1 1c3 2 5 4 7 6h1l3-9 3-6 1-2 1-2 1-3h2c1-1 2-1 3-2l1-2 2 3c1-1 2-1 3-2 0-1 2-2 2-3 1-1 0-4 0-6v-1z" class="t"></path><path d="M351 158h1c-1 2-1 2-1 3l1 1c-1 2-2 5-3 7v3l-1 1v1 5c-1 1-1 1-1 2-1 2-1 4-2 5v-8-1l-3 3 2-10 4-7-1-1c2-1 3-3 4-4z" class="AM"></path><path d="M351 158h1c-1 2-1 2-1 3l1 1c-1 2-2 5-3 7l-1 2-1-1c0-2 1-4 2-6l-1-1-1-1c2-1 3-3 4-4z" class="J"></path><path d="M339 178c1 0 1 0 1-1l1-2c1-2 2-3 3-5l-2 10 3-3v1 8c-1 5-2 11-1 16v1 2-1c-1 2-1 4-1 5l2 2c0 1-1 2-1 2v1c0 1-1 2-1 2-2 1-3 0-4 1l-1-1h0c-1-1-1 0-2-2v2h-1c-1-1 0-1-1-1l-1-1v-5s1-1 1-2l2-2v1l1-1-1-13 1-3v-1-5l1-1h0c1-1 1-2 1-4h0z" class="AP"></path><path d="M342 180l3-3v1 8c-1 5-2 11-1 16h-1l-1 1v-1c-1-1-1-2 0-3 0-3 0-5-1-8v-4l1-7z" class="AH"></path><path d="M337 205v-3h1v3h0v-6l1-2 1-3-1 21-1 1c-1-1-1 0-2-2v2h-1c-1-1 0-1-1-1l-1-1v-5s1-1 1-2l2-2v1l1-1z" class="g"></path><path d="M333 209s1-1 1-2l2-2v1c1 2 1 5 0 8v2h-1c-1-1 0-1-1-1l-1-1v-5z" class="AM"></path><path d="M339 178c1 0 1 0 1-1l1-2c1-2 2-3 3-5l-2 10-1 7-1 7-1 3-1 2v6h0v-3h-1v3l-1-13 1-3v-1-5l1-1h0c1-1 1-2 1-4h0z" class="AK"></path><path d="M339 178h0c1 3 1 5 0 7 0 3-1 6-1 8-1-2-1-3-1-4v-1-5l1-1h0c1-1 1-2 1-4z" class="j"></path><path d="M347 156c2-1 3-1 4-2h2v1c0 1-2 2-2 3-1 1-2 3-4 4l1 1-4 7c-1 2-2 3-3 5l-1 2c0 1 0 1-1 1h0c0 2 0 3-1 4h0l-1 1v5 1l-1 3h0c0-2-1-2-1-3 0-2 0-4-1-5 0-2 0-2-2-2v-1h-2v-1-2c-2 0-2 1-3 1h0c-2 1-3 2-4 3l-1 2-2 2-1-2 1-2c0-2 1-4 1-5 0-5 6-11 9-14l5-1c1 0 2-1 3-2l2-1c3-2 5-3 8-3h-1z" class="z"></path><path d="M333 170l2-2h2 0c0 3 1 6 1 9h-1c-1-3-3-4-4-7z" class="AG"></path><path d="M340 164h1l2 2c2-1 2-2 3-3 0-1 0-1 1-1l1 1-4 7c-1 2-2 3-3 5l-1 2c0 1 0 1-1 1h0l-1-1c0-3-1-6-1-9 1-1 2-2 3-4z" class="N"></path><path d="M320 182h1c2 0 3-4 5-5h2l2-2h1c1 1 1 0 2 0 0 1 1 2 2 3v2c1 1 2 1 2 3v5 1l-1 3h0c0-2-1-2-1-3 0-2 0-4-1-5 0-2 0-2-2-2v-1h-2v-1-2c-2 0-2 1-3 1h0c-2 1-3 2-4 3l-1 2-2 2-1-2 1-2z" class="g"></path><path d="M330 163l5-1c0 1-1 3-2 4h1c2 0 4-2 6-2h0c-1 2-2 3-3 4h0-2l-2 2h-2-2l-1 1-3 5-1 1c-1 1-2 2-2 3l-1 1v-4c0-5 6-11 9-14z" class="U"></path><path d="M347 156c2-1 3-1 4-2h2v1c0 1-2 2-2 3-1 1-2 3-4 4-1 0-1 0-1 1-1 1-1 2-3 3l-2-2h-1 0c-2 0-4 2-6 2h-1c1-1 2-3 2-4 1 0 2-1 3-2l2-1c3-2 5-3 8-3h-1z" class="S"></path><path d="M340 159c3-2 5-3 8-3v1c-1 2-2 3-4 4h0c-1-1 0-1-1-1h-1l2-2c-1 1-2 1-4 1z" class="AL"></path><path d="M347 156c2-1 3-1 4-2h2v1c0 1-2 2-2 3-1 1-2 3-4 4-1 0-1 0-1 1-1 1-1 2-3 3l-2-2h-1 0l4-3h0c2-1 3-2 4-4v-1h-1z" class="T"></path><path d="M319 184l1 2 2-2 1-2c1-1 2-2 4-3h0c1 0 1-1 3-1v2 1h2v1c2 0 2 0 2 2 1 1 1 3 1 5 0 1 1 1 1 3h0l1 13-1 1v-1l-2 2c0 1-1 2-1 2v5l1 1c1 0 0 0 1 1-2 1-4 0-6 1l1 1-1 1h0c-2-2-4-6-5-8v-4c-1-2-2-5-3-7 0-2-1-3-1-5h0c-1-4-1-7-1-11h0z" class="W"></path><path d="M324 207h0l1 1c0-2 1-4 2-5 0 5 1 9 2 14l1 1-1 1h0c-2-2-4-6-5-8v-4z" class="AS"></path><path d="M324 207c-1-2-2-5-3-7 0-2-1-3-1-5l2 2c2 1 5-1 7-1l-2 7c-1 1-2 3-2 5l-1-1h0z" class="Ab"></path><path d="M330 181h2v1c2 0 2 0 2 2 1 1 1 3 1 5 0 1 1 1 1 3h0l1 13-1 1v-1l-2 2c0 1-1 2-1 2l-1 1c-3-5 3-15 0-21v-1-5h-1v4l1 1c-1 1-1 2-2 2v1-8-2z" class="R"></path><path d="M319 184l1 2 2-2 1-2c1-1 2-2 4-3h0c1 0 1-1 3-1v2 1 2 8c0 2 0 3-1 5-2 0-5 2-7 1l-2-2h0c-1-4-1-7-1-11h0z" class="w"></path><path d="M324 190c0-2 0-3 1-5h2c1 1 1 3 1 5l-2-1s-1 1-2 1z" class="a"></path><path d="M330 183v8c0 2 0 3-1 5-2 0-5 2-7 1l1-1c0-1 1-4 1-6 1 0 2-1 2-1l2 1v1h1c0-2 0-5 1-8z" class="h"></path><path d="M319 184l1 2 2-2 1-2c1-1 2-2 4-3h0c1 0 1-1 3-1v2c-4 1-5 4-7 7-1 1-1 2-1 3h0c1 1 1 2 1 2l-3 3c-1-4-1-7-1-11h0z" class="AC"></path><path d="M330 152h1c1-1 1 0 1-1l1 1c-1 0-1 1-1 1h1c1-1 1-1 2-1l-3 3s-1 1-1 2c0 2 0 2 1 3l-2 3c-3 3-9 9-9 14 0 1-1 3-1 5l-1 2h0c0 4 0 7 1 11h0c0 2 1 3 1 5 1 2 2 5 3 7v4c1 2 3 6 5 8h0v2l1 2-2 1v1c0 1-1 2-1 3l-2-4c-3-4-4-8-8-12-4-8-7-18-13-25a30.44 30.44 0 0 0-8-8c1-1 2-1 4-1l1 2-1 1c3 2 5 4 7 6h1l3-9 3-6 1-2 1-2 1-3h2c1-1 2-1 3-2l1-2 2 3c1-1 2-1 3-2 0-1 2-2 2-3 1-1 0-4 0-6v-1z" class="AH"></path><path d="M296 179c1-1 2-1 4-1l1 2-1 1c3 2 5 4 7 6h1c2 2 2 4 4 7l16 30v1c0 1-1 2-1 3l-2-4c-3-4-4-8-8-12-4-8-7-18-13-25a30.44 30.44 0 0 0-8-8z" class="Ad"></path><path d="M330 152h1c1-1 1 0 1-1l1 1c-1 0-1 1-1 1h1c1-1 1-1 2-1l-3 3s-1 1-1 2c0 2 0 2 1 3l-2 3c-3 3-9 9-9 14 0 1-1 3-1 5l-1 2h0v-2l-1 1c0 1 1 2 0 4-1 1-1 2-1 3v1c0-1-2-3-3-3s-1 1-2 0l-2-2v-1c0-1 1-2 1-3 1-1 1-2 0-3v-1l3-6 1-2 1-2 1-3h2c1-1 2-1 3-2l1-2 2 3c1-1 2-1 3-2 0-1 2-2 2-3 1-1 0-4 0-6v-1z" class="AN"></path><path d="M317 165h2c1-1 2-1 3-2l-2 4v1c1 3-4 6-4 10h-2c-1-2 1-4 1-6v-1-1l1-2 1-3z" class="k"></path><path d="M365 160l1-1 1-1c1-1 3-1 4-1l-6 6c1 0 2-1 3-1 1-1 1-1 2-1l-2 2 1 1c-1 1-1 2-2 3l5-5 5-2c-2 3-3 5-4 8h0c-1 3-1 5-1 8v4l1 7c2 6 3 13 6 18l1-3 1 4c2 0 2 1 4 1 1 0 0-1 1 0 2 1 4 0 5 1 2 1 3 1 5 2h-4-1c0 1 1 2 1 3h0v1l1 1-1 1c-1 1-2 0-3 1-2 1-3 1-4 2v2l3 6c1 2 2 5 3 7l3 6 2 4v1h1 0l1 3 1 2-1 1c-3 1-3 3-7 3h-4-1 0v-1l-4 3c0-1 0-1 1-2-2-1-3-2-4-4 0-1 1-2 0-2 0-3-4-7-5-9h-1c-1-2-2-2-3-3l-1-3c0-1 1-2 0-4h0 1l1 2 1-1c-2-4-3-8-6-12v1h-1v1c1 1 1 1 1 2h-1v1l-1 1c-1 0-2 0-3-1-2-1-3-1-6-1-2 0-4 1-5 2l-2 1v-1c-1 0-1 0-2-1h-1 0l-1-1c1-1 1-1 1-3-1-1 0-2 0-2v-2l4-1-1-8h0c-1-10 0-23 4-33l1 1c0-1 1-2 1-2 1-3 2-4 4-6 2-3 3-5 7-6z" class="T"></path><path d="M365 194c2 8 4 16 8 24-3-1-3-5-5-7 0-2-2-3-2-4-2-3-2-5-4-8h-1v-1-3l2 3v-1c1 1 1 1 2 1v-1c-1-1 0-2 0-3h0z" class="C"></path><path d="M377 160c-2 3-3 5-4 8h0-2c-1 1-3 4-4 5v-1c0-1 1-2 2-3h-3l1 1c-1 1-2 2-4 3h0c1-2 2-4 4-6l5-5 5-2z" class="N"></path><path d="M357 188c1-3 2-7 3-10v1c1 2 0 4 0 6 1 1 1 0 2 1 1 0 2 7 3 8h0 0c0 1-1 2 0 3v1c-1 0-1 0-2-1v1l-2-3h0l-1-2s-1 0-2 1l-2-2 1-4z" class="j"></path><g class="g"><path d="M357 188c2 2 3 3 3 5 0 0-1 0-2 1l-2-2 1-4z"></path><path d="M356 192l2 2c1-1 2-1 2-1l1 2h0v3 1h1c2 3 2 5 4 8 0 1 2 2 2 4 2 2 2 6 5 7 3 7 6 14 9 20 1 4 4 8 4 12l1 1-1 2-4 3c0-1 0-1 1-2-2-1-3-2-4-4 0-1 1-2 0-2 0-3-4-7-5-9h-1c-1-2-2-2-3-3l-1-3c0-1 1-2 0-4h0 1l1 2 1-1c-2-4-3-8-6-12h0l-4-4h-1c-2-1-2-1-3-2s-2-2-2-4v-5c-1-4 0-8 0-11z"></path></g><path d="M363 206c2 3 3 6 3 10v2l-4-4 1-1v-1c-1-2 0-4 0-6z" class="m"></path><path d="M356 192l2 2c1-1 2-1 2-1l1 2h0v3 1l1 4c1 1 1 2 1 3 0 2-1 4 0 6v1l-1 1h-1c-2-1-2-1-3-2s-2-2-2-4v-5c-1-4 0-8 0-11z" class="J"></path><path d="M356 208c1 0 2-3 2-3 0-1-1-2-1-2 0-1 1-2 1-2 1-1 1-3 1-3l1-1 1 1v1l1 4-2-1-1 1s0 1 1 2l-1 3c0 1-1 1-1 2v1 1c-1-1-2-2-2-4z" class="Z"></path><path d="M358 212v-1-1c0-1 1-1 1-2l1-3c-1-1-1-2-1-2l1-1 2 1c1 1 1 2 1 3 0 2-1 4 0 6v1l-1 1h-1c-2-1-2-1-3-2z" class="k"></path><path d="M366 216l4 3h0c1 0 1 1 1 2l2 2v1c0 2 1 3 2 5v2c0 1 1 1 1 2s1 2 1 2l1 3c1 3 3 6 5 9 1 1 1 2 1 4 0 0-1 0-1 1v2c-2-1-3-2-4-4 0-1 1-2 0-2 0-3-4-7-5-9h-1c-1-2-2-2-3-3l-1-3c0-1 1-2 0-4h0 1l1 2 1-1c-2-4-3-8-6-12h0v-2z" class="J"></path><path d="M369 233c0-1 1-2 0-4h0 1l1 2 1-1c3 3 5 9 6 13l5 9v2c-2-1-3-2-4-4 0-1 1-2 0-2 0-3-4-7-5-9h-1c-1-2-2-2-3-3l-1-3z" class="a"></path><path d="M365 160l1-1 1-1c1-1 3-1 4-1l-6 6c1 0 2-1 3-1 1-1 1-1 2-1l-2 2 1 1c-1 1-1 2-2 3-2 2-3 4-4 6s-2 3-3 5c-1 3-2 7-3 10l-1 4c0 3-1 7 0 11v5c0 2 1 3 2 4s1 1 3 2h1l4 4h0v1h-1v1c1 1 1 1 1 2h-1v1l-1 1c-1 0-2 0-3-1-2-1-3-1-6-1-2 0-4 1-5 2l-2 1v-1c-1 0-1 0-2-1h-1 0l-1-1c1-1 1-1 1-3-1-1 0-2 0-2v-2l4-1-1-8h0c-1-10 0-23 4-33l1 1c0-1 1-2 1-2 1-3 2-4 4-6 2-3 3-5 7-6z" class="AM"></path><path d="M353 184s0-3 1-3h1 0 0c0 2-1 4-1 6 0 1 0 2-1 4s1 6 0 9c-2 2 0 9 0 12v2h0c0 1 1 1 2 1l1 1c-1 0-1 1-2 1-2 1-3 1-4 2h0c-2 1-3 2-5 4h0l-1-1c1-1 1-1 1-3-1-1 0-2 0-2v-2l4-1-1-8 2-1c1-1 0-4 0-5s1-2 1-3c0-2-1-4 0-6 1-1 1-1 1-2s0-3 1-5z" class="m"></path><path d="M348 206l2-1c0 3 1 6 1 9h-2l-1-8z" class="W"></path><path d="M356 203v5c0 2 1 3 2 4s1 1 3 2h1l4 4h0v1h-1v1c1 1 1 1 1 2h-1v1l-1 1c-1 0-2 0-3-1-2-1-3-1-6-1-2 0-4 1-5 2l-2 1v-1c-1 0-1 0-2-1h-1c2-2 3-3 5-4h0c1-1 2-1 4-2 1 0 1-1 2-1l-1-1c1-3-2-5-1-8 1-2 1-3 2-4z" class="AN"></path><path d="M353 219c2 0 4-1 7 0h1c1 1 2 2 4 3v1l-1 1c-1 0-2 0-3-1-2-1-3-1-6-1l4-1-1-1h0c-2-1-3-1-5-1z" class="AQ"></path><path d="M350 219h3c2 0 3 0 5 1h0l1 1-4 1c-2 0-4 1-5 2l-2 1v-1c-1 0-1 0-2-1h-1c2-2 3-3 5-4z" class="AT"></path><path d="M365 160l1-1 1-1c1-1 3-1 4-1l-6 6c-2 3-4 5-5 7-2 4-4 7-5 11h0-1c-1 0-1 3-1 3-1 2-1 4-1 5s0 1-1 2c-1 2 0 4 0 6 0 1-1 2-1 3s1 4 0 5l-2 1h0c-1-10 0-23 4-33l1 1c0-1 1-2 1-2 1-3 2-4 4-6 2-3 3-5 7-6z" class="h"></path><path d="M357 171l3-1c-2 4-4 7-5 11h0-1c-1 0-1 3-1 3-2-3 1-7 3-9 0-2 1-2 1-4z" class="k"></path><path d="M365 160l1-1 1-1c1-1 3-1 4-1l-6 6c-2 3-4 5-5 7l-3 1h0c1-3 3-4 4-6 1-1 1-1 1-2l3-3z" class="AO"></path><path d="M367 173c1-1 3-4 4-5h2c-1 3-1 5-1 8v4l1 7c2 6 3 13 6 18l1-3 1 4c2 0 2 1 4 1 1 0 0-1 1 0 2 1 4 0 5 1 2 1 3 1 5 2h-4-1c0 1 1 2 1 3h0v1l1 1-1 1c-1 1-2 0-3 1-2 1-3 1-4 2v2l3 6c1 2 2 5 3 7l3 6 2 4v1h1 0l1 3 1 2-1 1c-3 1-3 3-7 3h-4-1 0v-1l1-2c1 0 2-1 3-2l-11-28c-6-12-11-25-13-39-1-3-1-7 1-9z" class="h"></path><path d="M379 205l1-3 1 4c2 0 2 1 4 1h-1l-2 2v4l-3-8z" class="O"></path><path d="M391 254l-2-2c3-2 6-3 8-7h0l1 3 1 2-1 1c-3 1-3 3-7 3z" class="q"></path><path d="M367 173c1-1 3-4 4-5h2c-1 3-1 5-1 8-1-1-2-1-3 0-1 2-1 5-1 8-1-1-1-2-1-3-1 1-1 0-1 1-1-3-1-7 1-9z" class="i"></path><path d="M385 207c1 0 0-1 1 0 2 1 4 0 5 1 2 1 3 1 5 2h-4-1c0 1 1 2 1 3h0v1l1 1-1 1c-1 1-2 0-3 1-2 1-3 1-4 2v2l-1-1-2-7v-4l2-2h1z" class="v"></path><path d="M392 216c-1 0-3 0-4-1v-1c-1 0-1 0-2-1s-1-1-2-1l1-1 2 1c1-1 1-1 2-1l1-2c1 0 1 0 2 1h-1c0 1 1 2 1 3h0v1l1 1-1 1z" class="P"></path><path d="M385 207c1 0 0-1 1 0 2 1 4 0 5 1h0c-3 2-6 0-8 3v2l4 2h0l-2 1c-1 2-1 2-1 4l-2-7v-4l2-2h1z" class="B"></path><path d="M400 166c8-1 16-3 24-3 4 0 8 1 11 2 20 5 33 15 46 30 3 3 7 6 9 10v1l-7-8c-1 0-2 0-3 1h-1c0 1 0 1-1 2-5 1-10 0-16 1-3-1-6-1-10 1h0-5-3l-3 1c-1 0-2 0-3 1h-1c-2 1-3 3-4 5v1 6l1 4v6c2 5 5 9 9 12l3 3c2 1 4 1 6 1 4 0 9 1 12 0h1c1 0 2 0 4-1h3 1c-2 1-3 2-5 4h0l-6 3h-1l-6 5c0 1 0 1-1 1 0 1-4-3-5-4h-4 0c-4-2-7-2-12-1h0c-1 1-2 1-2 2-3-1-5-2-7-1l-1 1c-5 1-14-3-19-4-2 0-4-1-6 0l-1-3h0-1v-1l-2-4-3-6c-1-2-2-5-3-7l-3-6v-2c1-1 2-1 4-2 1-1 2 0 3-1l1-1-1-1v-1h0c0-1-1-2-1-3h1 4c-2-1-3-1-5-2-1-1-3 0-5-1-1-1 0 0-1 0-2 0-2-1-4-1l-1-4-1 3c-3-5-4-12-6-18l-1-7c3-1 4-3 6-5 5-3 12-6 18-7l3-1 1-1z" class="f"></path><defs><linearGradient id="BD" x1="392.869" y1="227.11" x2="410.497" y2="234.419" xlink:href="#B"><stop offset="0" stop-color="#847e7d"></stop><stop offset="1" stop-color="#9d9592"></stop></linearGradient></defs><path fill="url(#BD)" d="M396 226c7 2 15 4 21 7-1 1-2 1-3 0h-8-1c-3-1-6-1-9-1h-1c-1-1-1-1-2-1 0-1 0-1-1-2h0l1-1c1-1 2-1 3-2z"></path><defs><linearGradient id="BE" x1="402.468" y1="203.005" x2="416.065" y2="203.433" xlink:href="#B"><stop offset="0" stop-color="#83807f"></stop><stop offset="1" stop-color="#a39d9c"></stop></linearGradient></defs><path fill="url(#BE)" d="M403 196c2 1 2 1 3 1 3 1 5 2 7 2 1 1 2 1 3 1l1 1 2-3 1 1c0 1-1 2-2 3 0 1 0 2-1 3v1c-2 1-6 0-8-1-3-1-6-2-9-2l-2-1c0-1 0-1 1-2h2 2v-1-2-1z"></path><path d="M401 200h4c-2 1-3 2-5 3l-2-1c0-1 0-1 1-2h2z" class="V"></path><defs><linearGradient id="BF" x1="404.95" y1="202.172" x2="414.381" y2="197.862" xlink:href="#B"><stop offset="0" stop-color="#6b6868"></stop><stop offset="1" stop-color="#807d7b"></stop></linearGradient></defs><path fill="url(#BF)" d="M403 196c2 1 2 1 3 1 3 1 5 2 7 2 1 1 2 1 3 1l1 1 2-3 1 1c0 1-1 2-2 3 0 1 0 2-1 3-3-3-8-3-12-5h-4 2v-1-2-1z"></path><path d="M414 186l5 2c1 1 3 1 4 2h1c1 1 2 1 4 1 0 2-2 3-4 4l-4 4h0l-1-1-2 3-1-1c-1 0-2 0-3-1-2 0-4-1-7-2l2-1c2 0 3 0 4-2l1-1 3 1c0-2-2-2-3-3h2 0l-3-2c1-1 1-2 2-3z" class="H"></path><path d="M413 199l-1-1c1-1 5 0 7 0l-2 3-1-1c-1 0-2 0-3-1z" class="u"></path><path d="M424 195c-2-1-7-4-8-5l3-2c1 1 3 1 4 2h1c1 1 2 1 4 1 0 2-2 3-4 4z" class="X"></path><defs><linearGradient id="BG" x1="400.389" y1="186.365" x2="409.094" y2="186.651" xlink:href="#B"><stop offset="0" stop-color="#494849"></stop><stop offset="1" stop-color="#686667"></stop></linearGradient></defs><path fill="url(#BG)" d="M397 185h3l-1-2 1-1 4 1c1 0 2 1 3 1s2 0 3 1h1c1 0 2 1 3 1-1 1-1 2-2 3l3 2h0-2c1 1 3 1 3 3l-3-1-1 1c-1 2-2 2-4 2l-2 1c-1 0-1 0-3-1 0 0-1 0-2-1h0c1-1 2-2 3-2l-3-1c1 0 1 0 2-1h-2 0l-1-2 1-1c-2 0-3-2-4-3z"></path><path d="M410 185h1c1 0 2 1 3 1-1 1-1 2-2 3l3 2h0-2c1 1 3 1 3 3l-3-1-1 1c-1 2-2 2-4 2l-2 1c-1 0-1 0-3-1 0 0-1 0-2-1h0c1-1 2-2 3-2h5l1 1 1-1c-2-1-2-1-3-1l-1-1h4c-1-1-1-2-1-2l-1-1c-1-1-1-1-1-2s1-1 2-1z" class="x"></path><path d="M388 227s0-1 1-2l7 1c-1 1-2 1-3 2l-1 1h0c1 1 1 1 1 2 1 0 1 0 2 1h1c3 0 6 0 9 1h1l-3 1h0c1 1 2 1 4 1 0 1 0 1-1 2h2c1 0 1 0 2 1l5 1c1 0 1 0 2 1l-5-1-1 1h2-5-1c-2 1-3 0-5 1-3 1-6-1-8-1l-3-6c-1-2-2-5-3-7z" class="D"></path><path d="M391 234l12 3s1 1 2 1h2l5 1-1 1h2-5-1c-2 1-3 0-5 1-3 1-6-1-8-1l-3-6z" class="n"></path><path d="M392 216c2 0 3 1 4 1l3 1c5 0 9 2 14 3 1 0 3 0 4 1 1 2 1 4 0 6v3h-1-2l-5-2c-5-1-9-3-13-3-3-1-5-3-7-4l-2-2 2-3c1-1 2 0 3-1z" class="H"></path><path d="M401 221c1 0 1 0 2 1 2 1 4 1 6 1 2 1 5 1 6 3l-1 1h-1c0-1 0-1-1-1l1 1v1c-2 0-3 0-4-1-3-2-4-3-7-3-1 0 0 0-1-1v-2z" class="I"></path><path d="M392 216c2 0 3 1 4 1l3 1c5 0 9 2 14 3 1 0 3 0 4 1 1 2 1 4 0 6v-5c-7-1-13-4-20-4-1 1-2 1-2 3 1 1 1 2 1 3v1c-3-1-5-3-7-4l-2-2 2-3c1-1 2 0 3-1z" class="p"></path><path d="M392 216c2 0 3 1 4 1h0c-1 1-1 1-2 1h-1c-1 1-3 1-4 2l-1 1h1v1l-2-2 2-3c1-1 2 0 3-1z" class="AB"></path><path d="M397 185c1 1 2 3 4 3l-1 1 1 2h0 2c-1 1-1 1-2 1l3 1c-1 0-2 1-3 2h0c1 1 2 1 2 1v1 2 1h-2-2c-1 1-1 1-1 2h-3l1 1h-1c0-1 0-1-1-2h-2v1c-2 0-3 0-5-1-1 0-4-1-6 0l-1 1-1 3c-3-5-4-12-6-18 2-1 3-1 5-1h0l9 1h5 0 4l-2-2h3z" class="O"></path><path d="M392 193c1 1 2 1 4 2v1h0c-2-1-3 0-5 0v2h-1l-2-1c1 0 0 0 1-1l-1-1c0-1 3 0 5-1l-1-1z" class="B"></path><path d="M392 193c-1 0 0 0-1-1 1-1 3-1 4-1h1v-1c-2 0-5-1-7-2 4 0 8 1 11 1l1 2h0 2c-1 1-1 1-2 1h-2l-2 3h1l1 1h-1-2v-1c-2-1-3-1-4-2z" class="v"></path><path d="M401 192l3 1c-1 0-2 1-3 2h0c1 1 2 1 2 1v1 2 1h-2-2c-1 1-1 1-1 2h-3l1 1h-1c0-1 0-1-1-2h-2-1v-2h-1v-1h1v-2c2 0 3-1 5 0h0 2 1l-1-1h-1l2-3h2z" class="P"></path><path d="M391 198v-2c2 0 3-1 5 0v2h-1-4z" class="r"></path><path d="M395 202v-2h0l1-1h4v-2h3v2 1h-2-2c-1 1-1 1-1 2h-3z" class="p"></path><defs><linearGradient id="BH" x1="371.91" y1="189.883" x2="385.907" y2="198.532" xlink:href="#B"><stop offset="0" stop-color="#151314"></stop><stop offset="1" stop-color="#303032"></stop></linearGradient></defs><path fill="url(#BH)" d="M378 186h0l8 2c-2 1-3 2-4 3h4l1 1-1 1-2 1 2 2 1 1h1l2 1v1h1v2h1v1c-2 0-3 0-5-1-1 0-4-1-6 0l-1 1-1 3c-3-5-4-12-6-18 2-1 3-1 5-1z"></path><path d="M388 197l2 1v1h1v2c-1 0-3-1-5-1 1-1 1-2 1-3h1z" class="v"></path><path d="M386 200c-1 0-4-2-5-3 3-2 3-1 5-1l1 1c0 1 0 2-1 3z" class="F"></path><path d="M392 201h2c1 1 1 1 1 2h1l-1-1h3l2 1c3 0 6 1 9 2v1c2 0 6 1 7 3 1 0 1 1 1 2v6 5c-1-1-3-1-4-1-5-1-9-3-14-3l-3-1c-1 0-2-1-4-1l1-1-1-1v-1h0c0-1-1-2-1-3h1 4c-2-1-3-1-5-2-1-1-3 0-5-1-1-1 0 0-1 0-2 0-2-1-4-1l-1-4 1-1c2-1 5 0 6 0 2 1 3 1 5 1v-1z" class="V"></path><path d="M396 210l3-1c2 1 3 1 4 2-2 1-5 0-8 0 0 1 0 1 1 1-2 1-2 1-4 1h0c0-1-1-2-1-3h1 4z" class="G"></path><path d="M406 205l3 1c2 0 6 1 7 3 1 0 1 1 1 2-1 1-1 2-2 3l-1-1h-1l-8-3c-1-1-1 0-2-1v-1l1-1 1-1 1-1z" class="n"></path><path d="M399 218c0-2 0-2-1-3v-1c1-1 3-1 5-1v-1c4 1 9 1 12 3 0 1 1 1 0 2h1 1v5c-1-1-3-1-4-1-5-1-9-3-14-3z" class="H"></path><path d="M416 217c0 2 0 1-1 2-2 0-5 0-7-1s-3-1-4-2h-1l1-1h4c2 0 5 1 7 2h1z" class="I"></path><defs><linearGradient id="BI" x1="381.279" y1="201.392" x2="394.703" y2="207.559" xlink:href="#B"><stop offset="0" stop-color="#343335"></stop><stop offset="1" stop-color="#605f5f"></stop></linearGradient></defs><path fill="url(#BI)" d="M392 201h2c1 1 1 1 1 2h1l-1-1h3l2 1c3 0 6 1 9 2v1l-3-1-1 1-1 1-1 1v1c1 1 1 0 2 1l-2 1c-1-1-2-1-4-2l-3 1c-2-1-3-1-5-2-1-1-3 0-5-1-1-1 0 0-1 0-2 0-2-1-4-1l-1-4 1-1c2-1 5 0 6 0 2 1 3 1 5 1v-1z"></path><defs><linearGradient id="BJ" x1="394.449" y1="205.76" x2="402.729" y2="207.383" xlink:href="#B"><stop offset="0" stop-color="#626061"></stop><stop offset="1" stop-color="#827f7e"></stop></linearGradient></defs><path fill="url(#BJ)" d="M399 209l-1-1c-2 1-2 1-3 0 0-1 0-1-1-2h0v-1h3l1-1c2 0 6 1 8 1l-1 1-1 1-1 1v1c1 1 1 0 2 1l-2 1c-1-1-2-1-4-2z"></path><defs><linearGradient id="BK" x1="412.943" y1="219.175" x2="440.457" y2="205.62" xlink:href="#B"><stop offset="0" stop-color="#9e9c9d"></stop><stop offset="1" stop-color="#dcd4cd"></stop></linearGradient></defs><path fill="url(#BK)" d="M433 190l1 3c-1 1-3 2-4 4l-1 3v1l1-1 1-1-2 4 1 3c-1 1-1 3-2 4 0 2 0 4-1 7-1 5 0 11 0 16 1 2 1 3 0 4-1 0-1-1-2-1h-1c-1-1-1-1-1-2h0c-1 2-1 4-3 6l-1 1h-2l1-1h-1c-1-1-1-1-2-1l-5-1c-1-1-1-1-2-1h-2c1-1 1-1 1-2-2 0-3 0-4-1h0l3-1h8c1 1 2 1 3 0h1c1-5 1-11 1-16v-8l1-2c2-6 6-12 12-16l1-1z"></path><path d="M419 209c2 3 2 4 1 8 0 3 1 6 0 9-1-3 0-6-1-9v-8z" class="M"></path><path d="M417 238v-1c2-1 4-3 6-3-1 2-1 4-3 6l-1 1h-2l1-1v-1l-1-1z" class="L"></path><path d="M419 217c1 3 0 6 1 9 0 2 0 5-1 8-1 1-1 1-2 1h-2l-1 1c1 1 2 1 3 2l1 1v1h-1c-1-1-1-1-2-1l-5-1c-1-1-1-1-2-1h-2c1-1 1-1 1-2-2 0-3 0-4-1h0l3-1h8c1 1 2 1 3 0h1c1-5 1-11 1-16z" class="u"></path><path d="M430 197l-1 3v1l1-1 1-1-2 4 1 3c-1 1-1 3-2 4 0 2 0 4-1 7-1 5 0 11 0 16 1 2 1 3 0 4-1 0-1-1-2-1h-1c-1-1-1-1-1-2 2-13-2-26 7-37z" class="G"></path><path d="M425 222c1 4 1 8 2 11 1 2 1 3 0 4-1 0-1-1-2-1-1-5 0-9 0-14z" class="H"></path><path d="M425 222c0-6 0-13 4-19l1 3c-1 1-1 3-2 4 0 2 0 4-1 7-1 5 0 11 0 16-1-3-1-7-2-11z" class="n"></path><path d="M428 210c1 1 1 4 1 6v7c-1 3-1 7 1 9h0 0 0c1-1 1-2 3-3l1 3c1-1 0-3 0-5 2 5 5 9 9 12l3 3c2 1 4 1 6 1 4 0 9 1 12 0h1c1 0 2 0 4-1h3 1c-2 1-3 2-5 4h0l-6 3h-1l-6 5c0 1 0 1-1 1 0 1-4-3-5-4h-4 0c-4-2-7-2-12-1h0c-1 1-2 1-2 2-3-1-5-2-7-1l-1 1c-5 1-14-3-19-4-2 0-4-1-6 0l-1-3h0-1v-1l-2-4c2 0 5 2 8 1 2-1 3 0 5-1h1 5-2l1-1 5 1h1l-1 1h2l1-1c2-2 2-4 3-6h0c0 1 0 1 1 2h1c1 0 1 1 2 1 1-1 1-2 0-4 0-5-1-11 0-16 1-3 1-5 1-7z" class="d"></path><path d="M423 234h0c0 1 0 1 1 2h1c1 0 1 1 2 1l3 3h-1c-2-1-3 0-5 0h0s-1 1-2 1c0 0-1-1-2-1 2-2 2-4 3-6z" class="x"></path><path d="M394 240c2 0 5 2 8 1 1 1 2 1 4 1 3 2 5 2 8 3s4 2 7 1l2 2c-1 1-1 1-2 1-5-1-11-1-16-4-1 1-2 2-4 1h-1l-3-1h0-1v-1l-2-4z" class="o"></path><path d="M396 245l1-1c4 0 4-1 8 1-1 1-2 2-4 1h-1l-3-1h0-1z" class="Q"></path><path d="M412 239l5 1h1l-1 1h2l1-1c1 0 2 1 2 1 1 0 2-1 2-1l-1 2c1 0 1 1 1 2l1 1s0 1-1 1h0-3c-3 1-4 0-7-1s-5-1-8-3c-2 0-3 0-4-1 2-1 3 0 5-1h1 5-2l1-1z" class="M"></path><path d="M420 240c1 0 2 1 2 1 1 0 2-1 2-1l-1 2h0c-2 1 0 2 0 3l-1-1c-2-1-3-1-3-3l1-1z" class="E"></path><path d="M412 239l5 1h1l-1 1c-2 1-4 0-7 0h-6 1l1 1h0c-2 0-3 0-4-1 2-1 3 0 5-1h1 5-2l1-1z" class="AD"></path><path d="M434 227c2 5 5 9 9 12l3 3c2 1 4 1 6 1 4 0 9 1 12 0h1c-6 2-15 2-21 1-3-1-6-1-9 0h-2l1-1-2-1c1-1 1-2 2-2l-4-8h0c1-1 1-2 3-3l1 3c1-1 0-3 0-5z" class="B"></path><path d="M433 229l1 3h0c2 4 6 7 9 10h-1c-3-1-6 0-8 1l-2-1c1-1 1-2 2-2l-4-8h0c1-1 1-2 3-3z" class="AJ"></path><path d="M435 237h1c1 1 1 1 1 3h-2c-1-1-1-1 0-3z" class="AV"></path><path d="M469 242h3 1c-2 1-3 2-5 4h0l-6 3h-1l-6 5c0 1 0 1-1 1 0 1-4-3-5-4h-4 0c-4-2-7-2-12-1 2-1 4-1 5-1v-1-2h1c2-1 3-2 5-2 6 1 15 1 21-1 1 0 2 0 4-1z" class="L"></path><path d="M438 248l1-1c4-2 11 0 15 0-4 1-9-1-13 2h-3v-1z" class="Q"></path><path d="M454 247h1c3 0 6 0 9-2 0 1 0 1-1 2 0 0-1 1-1 2h-1l-6 5c0 1 0 1-1 1 0 1-4-3-5-4h-4 0c-4-2-7-2-12-1 2-1 4-1 5-1h3c4-3 9-1 13-2z" class="AJ"></path><path d="M455 254v-3c3-2 3-2 6-2l-6 5z" class="o"></path><path d="M400 166c8-1 16-3 24-3 4 0 8 1 11 2 20 5 33 15 46 30 3 3 7 6 9 10v1l-7-8c-1 0-2 0-3 1h-1c0 1 0 1-1 2-5 1-10 0-16 1-3-1-6-1-10 1h0-5-3l-3 1c-1 0-2 0-3 1h-1c-2 1-3 3-4 5v1 6l1 4v6c0 2 1 4 0 5l-1-3c-2 1-2 2-3 3h0 0 0c-2-2-2-6-1-9v-7c0-2 0-5-1-6 1-1 1-3 2-4l-1-3 2-4-1 1-1 1v-1l1-3c1-2 3-3 4-4l-1-3-1 1-1-1c-3 1-5-1-7-1l-1 1c-1-1-3-1-4-2l-5-2c-1 0-2-1-3-1h-1c-1-1-2-1-3-1s-2-1-3-1l-4-1-1 1 1 2h-3-3l2 2h-4 0-5l-9-1h0c-2 0-3 0-5 1l-1-7c3-1 4-3 6-5 5-3 12-6 18-7l3-1 1-1z" class="M"></path><path d="M445 189c2-1 4 0 6 0l8 1c-1 0-6 1-8 1-2-2-3-2-6-2z" class="Y"></path><path d="M439 190c2 0 4-1 6-1 3 0 4 0 6 2l-6 1h-1c-1-1-3-1-5-2z" class="G"></path><path d="M434 193l5-3c2 1 4 1 5 2h1c-2 0-4 1-6 2-3 1-5 3-8 5l-1 1-1 1v-1l1-3c1-2 3-3 4-4z" class="AB"></path><path d="M440 187c4-1 8 0 13 0l-2 2c-2 0-4-1-6 0-2 0-4 1-6 1l-5 3-1-3 5-2 2-1z" class="AV"></path><path d="M431 199c3-2 5-4 8-5 2 1 3 1 5 1v1h-1c-2 1-9 3-11 6v1l-2 3-1-3 2-4z" class="AD"></path><path d="M439 202c0-1-1-1-2-1v1-1c1-1 4-3 6-4h3 9c2-1 4-1 7-1h10v1c-2 0-4-1-6 0l-1 1c-6 0-13 0-19 2l-7 2z" class="X"></path><path d="M439 202l7-2v1c-4 1-7 2-9 4-2 1-3 3-4 5v1 6l1 4v6c0 2 1 4 0 5l-1-3c-2 1-2 2-3 3h0 0 0c-2-2-2-6-1-9v-7c0-2 0-5-1-6 1-1 1-3 2-4l2-3v1c0 1-1 1-1 2-1 1-1 1-1 2h1l1-2h1 0c2-2 4-3 6-4z" class="Q"></path><path d="M439 202l7-2v1c-4 1-7 2-9 4-2 1-3 3-4 5v1 6l1 4v6c0 2 1 4 0 5l-1-3-1-1c-1-7 0-14 1-22h0c2-2 4-3 6-4z" class="r"></path><path d="M438 171l12 3c9 4 17 9 24 15 2 3 4 5 6 8v1c-2 0-4 1-6 1 1 1 1 0 2 1l3-1c0 1 0 1-1 2-5 1-10 0-16 1-3-1-6-1-10 1h0-5-3l-3 1c-1 0-2 0-3 1h-1c2-2 5-3 9-4v-1c6-2 13-2 19-2 3 0 10 1 12-1-1-1-2-4-4-5h-1c-1-1-3-2-4-3-2-2-4-3-6-4-4-2-9-3-13-5h-1v-1c-1-2-5-2-7-3h-1-1c0-1-1-1-1-1-1 0-2 0-2-1h0 1c4 1 8 2 11 3 1 1 1 1 2 1l-9-4-1-1c-1 0-2-1-2-2z" class="D"></path><path d="M446 201c4 0 8-1 12-1 6-1 13 0 18 0l3-1c0 1 0 1-1 2-5 1-10 0-16 1-3-1-6-1-10 1h0-5-3l-3 1c-1 0-2 0-3 1h-1c2-2 5-3 9-4z" class="o"></path><path d="M431 169l4 1c0 1 2 1 3 1 0 1 1 2 2 2l1 1 9 4c-1 0-1 0-2-1-3-1-7-2-11-3h-1 0c0 1 1 1 2 1 0 0 1 0 1 1h1 1c2 1 6 1 7 3v1l-9-3h-3c1 2 2 2 3 2l4 2c2 0 5 1 7 3h0c3 0 3 1 5 2l6 3c-3 0-6-1-8-2-5 0-9-1-13 0l-2 1-5 2-1 1-1-1c-3 1-5-1-7-1l-1 1c-1-1-3-1-4-2l-5-2c-1 0-2-1-3-1h0c-1-1-3-2-4-2v-1h2 3v-1-1c1-1 0-1 0-2 2 0 3-1 4 0h3v-1c-2 0-3-1-4-2h0 4c1-1-1-1 0-2 2 0 4 0 7 1h0l1-1c-1-1-2-1-3-2 3 0 6 1 9 1l-2-1v-2z" class="G"></path><path d="M416 186c2-1 5 0 8 0v3l-8-3z" class="u"></path><path d="M412 181v-1c1-1 0-1 0-2 2 0 3-1 4 0v1h2v1h-1c-1 0-1 0-2 1h-3z" class="P"></path><path d="M416 186h0c-1-1-3-1-4-2h0c1 0 2 0 2-1h4c1 0 1 0 2-1h-1v-1l4 1h2c2 0 5 2 7 3 1 1 1 1 2 1s1 1 2 1 1 0 1 1h1l-5 2-1 1-1-1c-3 1-5-1-7-1h0v-3c-3 0-6-1-8 0z" class="Y"></path><path d="M424 186l10 2c-1 1-2 2-3 2-3 1-5-1-7-1h0v-3z" class="d"></path><path d="M433 178c-1-1-3-2-5-2h0v-1c3 0 7 1 11 2h-3c1 2 2 2 3 2l4 2c2 0 5 1 7 3h0c3 0 3 1 5 2l6 3c-3 0-6-1-8-2-5 0-9-1-13 0l-2 1h-1c0-1 0-1-1-1s-1-1-2-1-1 0-2-1c-2-1-5-3-7-3l-3-2h1 2c1 0 2 1 4 1h1c-1-1-1-1-2-1-1-1-2-1-2-1-3-1-4-2-6-3 2 0 6 0 9 1l4 1z" class="H"></path><path d="M425 182l-3-2h1 2c1 0 2 1 4 1 2 1 4 2 6 2l-1 1 2 1h0c1 0 2 0 2 1h1l1 1-2 1h-1c0-1 0-1-1-1s-1-1-2-1-1 0-2-1c-2-1-5-3-7-3z" class="I"></path><path d="M430 181c-1-1-1-1-2-1-1-1-2-1-2-1-3-1-4-2-6-3 2 0 6 0 9 1l4 1h0c3 2 6 2 9 3 1 0 3 1 4 2h0v1l-1-1h-2l-1-1h-5c-3-1-5-1-7-1z" class="n"></path><defs><linearGradient id="BL" x1="414.84" y1="161.536" x2="428.255" y2="200.905" xlink:href="#B"><stop offset="0" stop-color="#121213"></stop><stop offset="1" stop-color="#333231"></stop></linearGradient></defs><path fill="url(#BL)" d="M400 166c8-1 16-3 24-3 4 0 8 1 11 2 20 5 33 15 46 30 3 3 7 6 9 10v1l-7-8c-1 0-2 0-3 1h-1l-3 1c-1-1-1 0-2-1 2 0 4-1 6-1v-1c-2-3-4-5-6-8-7-6-15-11-24-15l-12-3c-1 0-3 0-3-1l-4-1v2l2 1c-3 0-6-1-9-1 1 1 2 1 3 2l-1 1h0c-3-1-5-1-7-1-1 1 1 1 0 2h-4 0c1 1 2 2 4 2v1h-3c-1-1-2 0-4 0 0 1 1 1 0 2v1 1h-3-2v1c1 0 3 1 4 2h0-1c-1-1-2-1-3-1s-2-1-3-1l-4-1-1 1 1 2h-3-3l2 2h-4 0-5l-9-1h0c-2 0-3 0-5 1l-1-7c3-1 4-3 6-5 5-3 12-6 18-7l3-1 1-1z"></path><path d="M427 165c3 1 6 1 9 3l-1 2-4-1h-1l1-1h-1l-2-2-1-1z" class="O"></path><path d="M418 167c1 0 1-1 2-1h1l2-1h4l1 1 2 2h1l-1 1-9-2h-3z" class="e"></path><path d="M436 168c5 1 11 3 14 6l-12-3c-1 0-3 0-3-1l1-2z" class="B"></path><path d="M387 182v-1h0 0c0-1-1-1-1-2 0 0 1-1 2-1h0c-1-1-2-1-3-1v-1c3 0 6 0 8 1v1c1 0 1 1 2 1l2 1-1 1h-1l-1 1h2c-2 2-3 0-4 2h0-2c-1-1-2-1-3-2z" class="y"></path><path d="M387 182h0 4c0-1 0-1-1-1v-1h0c2 0 3 0 5 1l-1 1h2c-2 2-3 0-4 2h0-2c-1-1-2-1-3-2z" class="O"></path><path d="M404 174l-2-1 1-1c2 0 3 0 5 1v1h1c2 1 3 2 5 2v1h-5l1 2h0 0c-2 1-3 1-4 2-2 0-3 0-5-1v1l4 2h-1l-4-1-1 1 1 2h-3-3l-2-1c1-2 2 0 4-2h-2l1-1h1l1-1-2-1c-1 0-1-1-2-1v-1l6 2c1-1 2-1 3-1h1l-3 1c3 1 2 0 4-1h2 0l-2-1v-2c-1 0-2 0-3-1h0 3z" class="F"></path><path d="M404 174c-2-1-2 0-3-1l1-2h3c-1-1-2-1-3-1h-1c2-1 5-2 7-2s3 0 5-1h3 2 3l9 2h1v2l2 1c-3 0-6-1-9-1 1 1 2 1 3 2l-1 1h0c-3-1-5-1-7-1-1 1 1 1 0 2h-4 0c1 1 2 2 4 2v1h-3c-1-1-2 0-4 0 0 1 1 1 0 2v1 1h-3-2v1c1 0 3 1 4 2h0-1c-1-1-2-1-3-1s-2-1-3-1h1l-4-2v-1c2 1 3 1 5 1 1-1 2-1 4-2h0 0l-1-2h5v-1c-2 0-3-1-5-2h-1v-1c-2-1-3-1-5-1l-1 1 2 1z" class="v"></path><path d="M404 174c-2-1-2 0-3-1l1-2h3c-1-1-2-1-3-1h-1c2-1 5-2 7-2s3 0 5-1h3 2 3l-2 2h-4v1c1 0 3 0 4 1v1c-2 0-10-2-11-1 2 1 5 2 7 3l-7-1c-2-1-3-1-5-1l-1 1 2 1z" class="E"></path><path d="M336 214c1 2 1 1 2 2h0l1 1c1-1 2 0 4-1 0 0 1-1 1-2l1 1v2s-1 1 0 2c0 2 0 2-1 3l1 1h0 1c1 1 1 1 2 1v1l2-1c1-1 3-2 5-2 3 0 4 0 6 1 1 1 2 1 3 1l1-1v-1h1c0-1 0-1-1-2v-1h1v-1c3 4 4 8 6 12l-1 1-1-2h-1 0c1 2 0 3 0 4l1 3c1 1 2 1 3 3h1c1 2 5 6 5 9 1 0 0 1 0 2 1 2 2 3 4 4-1 1-1 1-1 2l4-3v1h0 1 4c4 0 4-2 7-3l1-1-1-2c2-1 4 0 6 0 5 1 14 5 19 4l2 2c2 2 2 4 3 7h0v3c-2 1-3 2-3 3h-1c-2 2-3 4-5 5v1l1 1v1 4c-1 1-1 1-1 2 1 1 1 1 2 1-1 3-2 5-4 8l-1 1 1 1v1c-1 0-1 1-2 1h-1l-2-1h-1c-1 0-2 0-3-1-4 1-8 1-12 1 0 1 0 1 1 1l-1 1c0 1 1 2 2 3 6 0 12 1 16 5 2 2 4 5 5 8 5 7 8 14 11 22l-1 3c-1 0-1-1-2-1v1c-1-1-2-2-3-2-2-3-5-5-7-7l-4-2-3-2v2l-3-2c-3 0-4 0-6-1v-1l-1-3h2l-2-3-1 1-1-1c0-1 0-2 1-3h-2c-1 1 0 1-1 2-1 0-1-1-2-2h-1l-4 2-1-1-1 1v2h-2 0-3c-1 0-2 1-3 1 0 0 0-1-1-1l-1-1c0 2-1 4-1 5l-3 2v1h-2c-1-2-2-3-3-5-1 1 0 1-1 1-3-3-4-7-6-11v1h-1v-1l-10-22-6-15 1-2v-5c-2-4-3-9-3-13l-1-1-2-7-5-11h0c-2-3-3-6-4-9l-1-2v-2l1-1-1-1c2-1 4 0 6-1h1v-2z" class="Z"></path><path d="M369 257l1 4h3v-1c1 2 2 5 3 7l-1 2c-2 0-3-3-4-4-1-2-2-3-3-5 1 0 1-3 1-3z" class="a"></path><path d="M366 249c1 0 2-1 3-1 2 1 2 3 2 5 1 2 2 4 2 7h0v1h-3l-1-4s0 3-1 3l-2-3c0-1 0-2-1-3 0-2 0-3 1-5z" class="W"></path><path d="M369 257l-1-2h2l1 2c0 1 1 2 2 3h0v1h-3l-1-4z" class="m"></path><path d="M359 256c1 0 1 1 2 1 0-2-1-4-2-6l1-1 9 19c2 2 3 5 5 7s3 2 5 3l4 4c1 0 2 1 3 1 1 1 2 1 3 2l3 1 3 1h-4c0 1-1 0-2 0v1c-5-1-10-5-14-7 0-2-4-5-5-6h0c-3-3-5-5-7-9 0-1-1-3-1-4h0l-3-7z" class="a"></path><path d="M370 276c4 1 7 6 11 8v1h3 1c1 1 3 1 4 1l3 1 3 1h-4c0 1-1 0-2 0v1c-5-1-10-5-14-7 0-2-4-5-5-6z" class="k"></path><path d="M370 236c1 1 2 1 3 3h1c1 2 5 6 5 9 1 0 0 1 0 2 1 2 2 3 4 4-1 1-1 1-1 2v1l-2-1v1c0 2-1 2-2 4 0 1 0 0-1 1h-1c-1-1-1-3-2-4s-1-3-1-4l-1-1h-1c0-2 0-4-2-5-1 0-2 1-3 1v-2c2-3 3-6 4-9v-2z" class="AO"></path><path d="M378 250h1c1 2 2 3 4 4-1 1-1 1-1 2v1l-2-1-1-1c0-2 0-4-1-5z" class="c"></path><path d="M370 238c1 1 1 2 1 4l-1 1c0 2 0 3 1 5l1 5h-1c0-2 0-4-2-5-1 0-2 1-3 1v-2c2-3 3-6 4-9z" class="i"></path><path d="M374 239c1 2 5 6 5 9 1 0 0 1 0 2h-1l-1-1c-2-2-3-3-4-6 0-1 0-1-1-2l1-1 1-1z" class="W"></path><path d="M356 232h1v1h4v3c0 3 1 4 2 6v6c-1-2-2-3-3-5v-1h0c0 1 0 3-1 5v-1l-1-3-2 4c0 3 1 5 2 7 0 1 0 1 1 2l3 7h-1 0c-2-4-4-9-6-13 0 4 2 8 4 12-1-1-2-2-2-3h-1c0-2-1-2-1-3h-1c0 2-1 4-1 6h0c-2 1-3-3-5-4v-1c-1-1-1-3-2-5 0 0-1-3-1-4l-1-5c1 0 2-1 2-1 1-1 1-3 1-4 1 0 2 0 3 1h1c1-2 0-1 0-3 1-1 2-2 2-3l1-1 1 1 1-1z" class="C"></path><path d="M356 247c0-1-1-3-1-5v-3h3v4l-2 4z" class="c"></path><path d="M346 252c2 1 2 2 3 3v1c1 1 1 2 2 4l1-1h-1v-3h0v-4-2c1 0 1 0 2 1 1-1 1-4 1-5v-1c0 2 1 3 1 4v1c0 4 2 8 4 12-1-1-2-2-2-3h-1c0-2-1-2-1-3h-1c0 2-1 4-1 6h0c-2 1-3-3-5-4v-1c-1-1-1-3-2-5z" class="w"></path><path d="M356 232h1v1h4v3l-1-1c-1 1-1 2-1 3h-1v-1c0-1-1-2-2-3-1 0-1 1-2 2v2 2l-1-2c-2 2 1 3-2 4h-2c-1 2-1 3-1 4-1 0-2 1-3 2l-1-5c1 0 2-1 2-1 1-1 1-3 1-4 1 0 2 0 3 1h1c1-2 0-1 0-3 1-1 2-2 2-3l1-1 1 1 1-1z" class="K"></path><path d="M366 218c3 4 4 8 6 12l-1 1-1-2h-1 0c1 2 0 3 0 4l1 3v2c-1 3-2 6-4 9v2c-1 2-1 3-1 5h0c-1-2-1-4-2-6v-6c-1-2-2-3-2-6v-3h-4v-1h-1l-1 1-1-1-1 1c0 1-1 2-2 3 0 2 1 1 0 3h-1c-1-1-2-1-3-1h0-2v-2-3c0-2 1-4 3-6v-2l2-1c1-1 3-2 5-2 3 0 4 0 6 1 1 1 2 1 3 1l1-1v-1h1c0-1 0-1-1-2v-1h1v-1z" class="z"></path><path d="M348 225l2-1c1-1 3-2 5-2 3 0 4 0 6 1 1 1 2 1 3 1l1-1v1l-4 1c-3-1-7-2-10 0-1 1-2 2-3 2v-2z" class="AW"></path><path d="M363 231c3 4 3 7 2 13-1 0-1 1-1 1 1 1 1 2 2 2v2c-1 2-1 3-1 5h0c-1-2-1-4-2-6v-6c1-3 0-8 0-11z" class="T"></path><path d="M366 218c3 4 4 8 6 12l-1 1-1-2h-1 0c1 2 0 3 0 4l-2-3c-1-2-3-4-6-5l4-1v-1-1h1c0-1 0-1-1-2v-1h1v-1z" class="m"></path><path d="M365 222h1c1 2 3 5 2 7l-1 1c-1-2-3-4-6-5l4-1v-1-1z" class="AH"></path><path d="M345 236h1l1 1c0-2 0-4 2-5 1 0 0 1 1 0 2-1 3-2 5-3l1-1c2 0 3 1 5 1h1l1 2c0 3 1 8 0 11-1-2-2-3-2-6v-3h-4v-1h-1l-1 1-1-1-1 1c0 1-1 2-2 3 0 2 1 1 0 3h-1c-1-1-2-1-3-1h0-2v-2z" class="N"></path><path d="M354 231c2-1 3-1 5-1h1c1 1 1 2 1 3h-4v-1h-1c-1 0-1 0-2-1z" class="j"></path><path d="M347 238l2-4h1c2-1 2-1 3-3h0 1c1 1 1 1 2 1l-1 1-1-1-1 1c0 1-1 2-2 3 0 2 1 1 0 3h-1c-1-1-2-1-3-1h0z" class="AI"></path><defs><linearGradient id="BM" x1="338.278" y1="245.764" x2="354.343" y2="234.21" xlink:href="#B"><stop offset="0" stop-color="#e6726c"></stop><stop offset="1" stop-color="#f39e94"></stop></linearGradient></defs><path fill="url(#BM)" d="M336 214c1 2 1 1 2 2h0l1 1c1-1 2 0 4-1 0 0 1-1 1-2l1 1v2s-1 1 0 2c0 2 0 2-1 3l1 1h0 1c1 1 1 1 2 1v1 2c-2 2-3 4-3 6v3 2h2 0c0 1 0 3-1 4 0 0-1 1-2 1l1 5c0 1 1 4 1 4 1 2 1 4 2 5v3c-2-2-4-6-6-9l-1-1-2-7-5-11h0c-2-3-3-6-4-9l-1-2v-2l1-1-1-1c2-1 4 0 6-1h1v-2z"></path><path d="M345 233v3 2h2 0c0 1 0 3-1 4 0 0-1 1-2 1 0-4 0-7 1-10z" class="j"></path><path d="M345 219c0 2 0 2-1 3l1 1h0c-1 1-1 1-1 2-2 3-3 5-3 8-1 3-1 8-1 11 1 2 2 4 1 6l-2-7c0-3-1-7-1-10 1-6 2-9 7-14z" class="t"></path><path d="M336 214c1 2 1 1 2 2h0l1 1c1-1 2 0 4-1 0 0 1-1 1-2l1 1v2s-1 1 0 2c-5 5-6 8-7 14 0 3 1 7 1 10l-5-11h0c-2-3-3-6-4-9l-1-2v-2l1-1-1-1c2-1 4 0 6-1h1v-2z" class="k"></path><path d="M336 214c1 2 1 1 2 2h0l1 1-3 3c0 2-1 3-1 5-1 2-1 3 0 5 0 0-1 1-1 2-2-3-3-6-4-9l-1-2v-2l1-1-1-1c2-1 4 0 6-1h1v-2z" class="AH"></path><path d="M336 214c1 2 1 1 2 2h0c-2 1-4 3-7 3l-1-1-1-1c2-1 4 0 6-1h1v-2z" class="w"></path><path d="M330 218l1 1c-1 1-1 1-1 3 1-1 1-1 2-1 0 1 0 1-1 2h1c1-1 3-2 4-3 0 2-1 3-1 5-1 2-1 3 0 5 0 0-1 1-1 2-2-3-3-6-4-9l-1-2v-2l1-1z" class="m"></path><path d="M356 259h1c0 1 1 2 2 3-2-4-4-8-4-12 2 4 4 9 6 13h0 1 0c0 1 1 3 1 4 2 4 4 6 7 9h0c1 1 5 4 5 6 4 2 9 6 14 7v-1c1 0 2 1 2 0h4l-3-1v-1l-1-1v-4h0 6c1-1 1 0 2 0v-1c2 0 4 0 6 1h1 3c2 1 2 1 4 1v-1c2 0 3-2 4-3l1-4v-3l1 2 1 1v1 4c-1 1-1 1-1 2 1 1 1 1 2 1-1 3-2 5-4 8l-1 1 1 1v1c-1 0-1 1-2 1h-1l-2-1h-1c-1 0-2 0-3-1-4 1-8 1-12 1 0 1 0 1 1 1l-1 1c0 1 1 2 2 3-4 0-8 0-12 1-2-1-3-1-5-1l-1-1c-2-1-3-2-5-3l-1-1c-3-1-6-4-9-6l-2-2c1 0 2 0 3 1h1 0l-3-6c-1-2-4-4-5-5v-3h0c-1-1-2-3-3-4l-1-1c-1-2 0-3 0-5 1-1 1-2 1-3z" class="AM"></path><path d="M406 288c2 0 4-1 6-1-1 1-2 3-4 3s-3 0-5 1c-2 0-5 0-6-1l-2-1h4c2 0 5 0 7-1z" class="m"></path><path d="M381 288c-2-3-5-4-6-6 4 2 9 6 14 7l1 1c2 1 3 1 5 2h1-1c-2 0-4 0-7-1-1-1-3-1-4-1v1l-3-3z" class="W"></path><path d="M375 294c5 1 11 2 16 1 2-1 4 0 5 0 0 1 1 2 2 3-4 0-8 0-12 1-2-1-3-1-5-1l-1-1c-2-1-3-2-5-3z" class="AH"></path><path d="M420 274v1 4c-1 1-1 1-1 2 1 1 1 1 2 1-1 3-2 5-4 8l-1 1 1 1v1c-1 0-1 1-2 1h-1l-2-1h-1c-1 0-2 0-3-1l1-1c1 0 2 0 2-1 2-1 6-5 6-7 0-1 0-1 1-1l2-8z" class="m"></path><path d="M359 272c2 2 4 4 6 7 1 3 6 8 9 11 2 1 4 1 5 4h0l-3-1h-2c-3-1-6-4-9-6l-2-2c1 0 2 0 3 1h1 0l-3-6c-1-2-4-4-5-5v-3z" class="a"></path><path d="M356 259h1c0 1 1 2 2 3-2-4-4-8-4-12 2 4 4 9 6 13h0 1 0c0 1 1 3 1 4 2 4 4 6 7 9h0c1 1 5 4 5 6 1 2 4 3 6 6-6-1-10-7-13-11l-1-1-1 1v2c-1-3-4-4-6-7h0-1c-1-1-2-3-3-4l-1-1c-1-2 0-3 0-5 1-1 1-2 1-3z" class="AO"></path><path d="M356 268l1-1 4 6c-1-4-2-7-2-10l9 14-1-1-1 1v2c-1-3-4-4-6-7h0-1c-1-1-2-3-3-4z" class="k"></path><path d="M413 281c2 0 3-2 4-3-1 4-3 6-5 9-2 0-4 1-6 1-2 1-5 1-7 1h-4v-1l-3-1v-1l-1-1v-4h0 6c1-1 1 0 2 0v-1c2 0 4 0 6 1h1 3c2 1 2 1 4 1v-1z" class="J"></path><path d="M391 281c3 2 5 4 8 5 0 1-1 1-1 1-2-1-4-1-6-1l-1-1v-4z" class="m"></path><path d="M399 286l4 1c1 0 2 0 3 1-2 1-5 1-7 1h-4v-1l-3-1v-1c2 0 4 0 6 1 0 0 1 0 1-1z" class="a"></path><path d="M342 251c2 3 4 7 6 9v-3 1c2 1 3 5 5 4h0c0-2 1-4 1-6h1c0 1 1 1 1 3 0 1 0 2-1 3 0 2-1 3 0 5l1 1c1 1 2 3 3 4h0v3c1 1 4 3 5 5l3 6h0-1c-1-1-2-1-3-1l2 2c3 2 6 5 9 6l1 1c2 1 3 2 5 3l1 1-5 1-2 2h0c1 1 1 2 3 3v1c-2 0-4-1-5 0-2 1-3 1-4 1v-1h-2 0c2 3 3 7 4 10s2 5 3 7v1h-2c-1-2-2-3-3-5-1 1 0 1-1 1-3-3-4-7-6-11v1h-1v-1l-10-22-6-15 1-2v-5c-2-4-3-9-3-13z" class="AT"></path><path d="M357 277l6 8s0 1-1 2v-1c-3-1-5-6-5-9z" class="AW"></path><path d="M365 287c3 2 6 5 9 6l1 1c2 1 3 2 5 3l1 1-5 1c-1 0-2 0-3-1h0c-1-1-2-1-3-2s-3-2-4-4c-1-1-1-3-1-5z" class="Ab"></path><path d="M360 290v-1c2 2 4 4 6 7 2 2 4 3 7 4l1 1h0c1 1 1 2 3 3v1c-2 0-4-1-5 0-2 1-3 1-4 1v-1h-2c-3-5-4-10-6-15z" class="U"></path><path d="M348 257v1c2 1 3 5 5 4h0c0-2 1-4 1-6h1c0 1 1 1 1 3 0 1 0 2-1 3 0 2-1 3 0 5l1 1c1 1 2 3 3 4h0v3c1 1 4 3 5 5l3 6h0-1c-1-1-2-1-3-1h0l-6-8c-3-5-6-11-9-17v-3z" class="W"></path><path d="M345 264c0-1 0-1 1-2 1 4 4 9 4 13 0 2 2 4 3 6s2 5 3 8c1-3-2-5-2-8h1v1l1 2h1v1s1 1 1 2l1 1c0 1 1 3 1 4v-2c2 5 3 10 6 15h0c2 3 3 7 4 10s2 5 3 7v1h-2c-1-2-2-3-3-5-1 1 0 1-1 1-3-3-4-7-6-11v1h-1v-1l-10-22-6-15 1-2v-5z" class="AW"></path><path d="M366 305c2 3 3 7 4 10s2 5 3 7v1h-2c-1-2-2-3-3-5-1-3-4-7-4-10h0l2-3z" class="AP"></path><defs><linearGradient id="BN" x1="411.105" y1="301.26" x2="403.599" y2="328.685" xlink:href="#B"><stop offset="0" stop-color="#100706"></stop><stop offset="1" stop-color="#4d1513"></stop></linearGradient></defs><path fill="url(#BN)" d="M398 298c6 0 12 1 16 5 2 2 4 5 5 8 5 7 8 14 11 22l-1 3c-1 0-1-1-2-1v1c-1-1-2-2-3-2-2-3-5-5-7-7l-4-2-3-2v2l-3-2c-3 0-4 0-6-1v-1l-1-3h2l-2-3-1 1-1-1c0-1 0-2 1-3h-2c-1 1 0 1-1 2-1 0-1-1-2-2h-1l-4 2-1-1-1 1v2h-2 0-3c-1 0-2 1-3 1 0 0 0-1-1-1l-1-1c0 2-1 4-1 5l-3 2c-1-2-2-4-3-7s-2-7-4-10h0 2v1c1 0 2 0 4-1 1-1 3 0 5 0v-1c-2-1-2-2-3-3h0l2-2 5-1c2 0 3 0 5 1 4-1 8-1 12-1z"></path><path d="M410 310h0l1-1 3 3c2 2 6 6 6 10h0l-5-6c-2-2-4-4-7-5-1 0-2-1-3-1h0c1 0 3 1 5 0z" class="j"></path><path d="M386 311c0-3 2-3 4-5h4 2c1 0 1 0 2 1 1 0 2-1 3-1s2 1 3 1h2l1 1h0c1 2 1 1 3 2-2 1-4 0-5 0-3-1-5-2-8-2h-2c-1 1-2 2-4 1h-1c0 1-2 2-2 2-1 0-1-1-2 0h0z" class="U"></path><path d="M386 311c1-1 1 0 2 0 0 0 2-1 2-2h1c2 1 3 0 4-1h2c3 0 5 1 8 2h0c1 0 2 1 3 1 3 1 5 3 7 5l-8-3s-1-1-2-1c-1-1-3-1-4-1h-2-2l-1-1c-1 0-1 2-2 2h-1l-4 2-1-1-1 1v2h-2 0-3c0-1 1-1 2-3h0l2-2z" class="C"></path><path d="M381 298c2 0 3 0 5 1l-2 1h0-3v-1l-1 1c1 1 1 2 2 3v2 1l1 1v2h-1l1 2h2 1 0l-2 2h0c-1 2-2 2-2 3-1 0-2 1-3 1 0 0 0-1-1-1l-1-1c0 2-1 4-1 5l-3 2c-1-2-2-4-3-7s-2-7-4-10h0 2v1c1 0 2 0 4-1 1-1 3 0 5 0v-1c-2-1-2-2-3-3h0l2-2 5-1z" class="T"></path><path d="M370 315c1 0 1-1 1-2 1-2 3-3 5-5 2 0 4 0 6 1l1 2h2 1 0l-2 2h0c-1 2-2 2-2 3-1 0-2 1-3 1 0 0 0-1-1-1l-1-1c0 2-1 4-1 5l-3 2c-1-2-2-4-3-7z" class="S"></path><path d="M386 311h0l-2 2h0c-1 2-2 2-2 3-1 0-2 1-3 1 0 0 0-1-1-1l-1-1 2-2c2 0 3-1 4-2h2 1z" class="j"></path><path d="M394 312c1 0 1-2 2-2l1 1h2 2c1 0 3 0 4 1 1 0 2 1 2 1l8 3 5 6c2 3 3 5 4 8 1 2 2 3 3 5v1c-1-1-2-2-3-2-2-3-5-5-7-7l-4-2-3-2v2l-3-2c-3 0-4 0-6-1v-1l-1-3h2l-2-3-1 1-1-1c0-1 0-2 1-3h-2c-1 1 0 1-1 2-1 0-1-1-2-2z" class="R"></path><path d="M402 318c2 1 2 1 4 3h0v-1c0-1-1-2-1-2 1 0 2 2 2 3 1 1 2 2 3 2v2l-3-2c-3 0-4 0-6-1v-1l-1-3h2z" class="a"></path><path d="M413 325c-1-2-2-2-3-4l-3-3 1-1 4 4c2 1 3 2 5 3h1l6 6c1 2 2 3 3 5v1c-1-1-2-2-3-2-2-3-5-5-7-7l-4-2z" class="c"></path><path d="M394 312c1 0 1-2 2-2l1 1h2 2c1 0 3 0 4 1 1 0 2 1 2 1l8 3 5 6c2 3 3 5 4 8l-6-6c-2-3-6-8-10-8-1-1-1-1-1-2l-1 1-1 1c0-1-1-2-2-2v2l-4-4h-2c-1 1 0 1-1 2-1 0-1-1-2-2z" class="AC"></path><path d="M398 248c2-1 4 0 6 0 5 1 14 5 19 4l2 2c2 2 2 4 3 7h0v3c-2 1-3 2-3 3h-1c-2 2-3 4-5 5v1l-1-2v3l-1 4c-1 1-2 3-4 3v1c-2 0-2 0-4-1h-3-1c-2-1-4-1-6-1v1c-1 0-1-1-2 0h-6 0v4c-1-1-2-1-3-2-2-2-4-4-5-7h0l-1 1h0v2c-1-1-1-1-1-2-2-2-3-4-3-7h0v-2c-1-4 2-8 4-11v-1l4-3v1h0 1 4c4 0 4-2 7-3l1-1-1-2z" class="t"></path><path d="M381 277c0-1-1-2-1-3v-6l1-1c0 1 0 2 1 3l1-1v7l-1 1h0v2c-1-1-1-1-1-2z" class="h"></path><path d="M391 258c-5 2-6 6-9 8 1-2 2-5 2-7 1-1 1-2 2-3h2c1-1 1-1 2-1v1l1 2h0z" class="AQ"></path><path d="M387 266h0l3-3 1-1 1 1c-3 2-5 5-5 9v2c1 3 1 4 3 6l1 1h0v4c-1-1-2-1-3-2-2-2-4-4-5-7h0v-7-2l1 1h0 1l2-2z" class="k"></path><path d="M383 269v-2l1 1h0 1c0 3 0 6-2 8h0v-7z" class="AM"></path><path d="M391 254c4 0 4-2 7-3l1-1c3 7 6 12 6 19v-1h-1v1h-1v-3c-1-1-1-2-2-2-1-2-3-2-5-3-1 1-2 1-4 2l-1-1-1 1-3 3h0c1-2 1-4 3-5 1 0 1-1 2-1l-1-2h0l2-1c-1-1-1-2-2-3z" class="AO"></path><path d="M393 257c1 0 2-2 3-3l1-2 1 1-1 1c0 1 0 3 1 5h0c-1 1-1 1-1 2h-1c-1 1-2 1-4 2l-1-1-1 1-3 3h0c1-2 1-4 3-5 1 0 1-1 2-1l-1-2h0l2-1z" class="a"></path><path d="M396 261c2 1 4 1 5 3 1 0 1 1 2 2v3h1v-1h1v1c0 1 0 4 1 5v1l-2 2c-1 1-1 1-1 2h0c2 0 2 1 3 2h-1c-2-1-4-1-6-1v1c-1 0-1-1-2 0h-6l-1-1c-2-2-2-3-3-6v-2c0-4 2-7 5-9 2-1 3-1 4-2z" class="S"></path><path d="M403 266v3c0 1 1 1 0 2-3-1-4-1-7 1-1 0-3 1-4 2h-1c1-2 2-4 3-5h6c1 0 1 0 2-1l1-2z" class="C"></path><path d="M403 269h1v-1h1v1c0 1 0 4 1 5v1l-2 2c-1 1-1 1-1 2h0c2 0 2 1 3 2h-1c-2-1-4-1-6-1v1c-1 0-1-1-2 0h-6l-1-1c-2-2-2-3-3-6l2 2 2 2v-2-2h1c1-1 3-2 4-2 3-2 4-2 7-1 1-1 0-1 0-2z" class="J"></path><path d="M391 274h1c1-1 3-2 4-2l-2 3c1 2 1 2 2 3h-1c-2 0-3-1-4-2v-2z" class="s"></path><path d="M396 278h2c1 0 5-1 5-2v-2c0-1 1-2 1-2h1v2l1 1-2 2c-1 1-1 1-1 2h0c2 0 2 1 3 2h-1c-2-1-4-1-6-1v1c-1 0-1-1-2 0h-6l-1-1c-2-2-2-3-3-6l2 2 2 2v-2c1 1 2 2 4 2h1z" class="K"></path><path d="M390 280h1c1 0 1-1 2-1l6 1h0v1c-1 0-1-1-2 0h-6l-1-1z" class="g"></path><path d="M398 248c2-1 4 0 6 0 5 1 14 5 19 4l2 2c2 2 2 4 3 7h0v3c-2 1-3 2-3 3h-1c-2 2-3 4-5 5v1l-1-2v3l-1 4c-1 1-2 3-4 3v1c-2 0-2 0-4-1h-3c-1-1-1-2-3-2h0c0-1 0-1 1-2l2-2v-1c-1-1-1-4-1-5 0-7-3-12-6-19l-1-2z" class="AZ"></path><path d="M410 264v-2h1c3 2 5 4 7 7v1 1 3l-2-1c-1-3-1-3-3-5 0-2-1-3-2-4h-1z" class="c"></path><path d="M410 264h1c1 1 2 2 2 4 2 2 2 2 3 5l2 1-1 4c-1 1-2 3-4 3v1c-2 0-2 0-4-1h-3c-1-1-1-2-3-2h0c0-1 0-1 1-2l2-2v-1c2-3 3-6 4-10z" class="U"></path><path d="M406 281c-1-1-1-2-3-2h0c0-1 0-1 1-2l1 1c1 1 3 0 4 0 0 0 1 1 1 2v1h2 1 0v1c-2 0-2 0-4-1h-3z" class="C"></path><path d="M416 273l2 1-1 4c-1 1-2 3-4 3h0-1-2v-1h1l3-3c2-1 2-2 2-4z" class="T"></path><path d="M155 128c1 0 2-1 3-1 3 2 9 1 13 1h10v1h-1c-1-1-2 0-4 0 1 0 3 0 4 1l80 1h0l152 1h-1c1 1 2 1 3 1h-5c-2 2-3 2-5 2v1c-11-1-21 1-31 1-7 0-13 1-20 2-3 0-5 1-8 1l-1 1c-1 0-2 1-3 1-1 1-2 1-3 2-2 1-4 2-6 4-1 1-1 3-2 4v1c0 2 1 5 0 6 0 1-2 2-2 3-1 1-2 1-3 2l-2-3-1 2c-1 1-2 1-3 2h-2l-1 3-1 2-1 2-3 6-3 9h-1c-2-2-4-4-7-6l1-1-1-2c-2 0-3 0-4 1-2 0-4-2-5-3l-7-4c1 1 2 2 4 3l3 4c-1 1-1 1-1 3 2 1 3 3 4 5l2 3 2 2c1 2 2 4 3 5 1 4 1 7 1 11h0l-1-1c-2 3-3 5-6 7h-2c-3 2-5 3-7 4s-7 2-8 3l-1 1h0v-3c-5-9-11-18-18-25-4-5-8-9-12-13-13-12-30-22-46-28-3-1-10-4-13-2h-2l-1 1h-2c-2-2-1-3-1-5l-2-1 3-1c0-1 0-1-1-2-4-3-14-2-19-1-9 2-16 6-23 12v-1c0-5 9-12 13-16l1-1v-3c2-3 5-2 8-3h1-6l-2-2z" class="j"></path><path d="M253 135c6-1 12-1 18-1h14c5 0 12 2 17 1 3-1 4 0 7 0 4 0 9 1 13 0h1l1 1v-1h25c-1 0-2 0-3 1h-5s0 1-1 1h-5-7l-1-1h-1c-1 1-1 1-2 1l-9 2c-1-1-2-1-3-1v-1h-2c-3 0-6 0-8-1-8-1-17 0-25 0s-16 0-24-1z" class="AK"></path><path d="M349 135c6 0 11-1 16 0h10c5 0 9-1 14 0h11 4v1c-11-1-21 1-31 1-7 0-13 1-20 2-3 0-5 1-8 1-4 0-9 2-12 4-1 1-1 1-2 1v-3c-1-2-1-4-3-5h7 5c1 0 1-1 1-1h5c1-1 2-1 3-1z" class="C"></path><path d="M155 128c1 0 2-1 3-1 3 2 9 1 13 1h10v1h-1c-1-1-2 0-4 0 1 0 3 0 4 1l80 1h0-46c-7 0-15-1-23 0-3 1-7 0-11 0-1 0-1 1-2 1-1 1-2 0-4 0h-6c-2 1-3 1-5 0h-5-1l-1 1c2 0 4 1 6 0h3 7c3 0 10-2 13 0 2 0 6 0 7 1 1 0 2-1 3-1s2 1 3 1h-1c-3 1-6 0-9 0v1c0 1 1 1 2 1h-1-5c-10-1-22-2-31 1l1-1v-3c2-3 5-2 8-3h1-6l-2-2z" class="l"></path><path d="M171 128h10v1h-1c-1-1-2 0-4 0 1 0 3 0 4 1h-13 0c1 0 3 0 4-1h-4l4-1z" class="Ac"></path><path d="M155 128c1 0 2-1 3-1 3 2 9 1 13 1l-4 1h4c-1 1-3 1-4 1h0-4-6l-2-2z" class="AU"></path><path d="M253 135c8 1 16 1 24 1s17-1 25 0c2 1 5 1 8 1h2v1c-2 0-4 1-5 2h-1v-1c-1 1-3 1-5 1s-4 1-6 0c-5-2-12-1-18 0h-1c-4-1-6-1-10 0h-1c-2 1-4 2-5 5 0 2 1 3 1 5-1 0-3 1-4 1l-1-1c-1-1-3-3-4-3-2-1-3-2-4-3l1-1c-3-1-4-3-7-4h-2c-1 0-2-1-3-1h-2l1-1-1-1h-1 0 12c2 0 5 0 7-1z" class="i"></path><path d="M236 137l14 2c0 1 0 2-1 2v2c-3-1-4-3-7-4h-2c-1 0-2-1-3-1h-2l1-1z" class="K"></path><path d="M252 147c-1-2-1-3-1-4l-1-1v-1c2-1 3-1 5-1 1 3 1 6 1 10-1-1-3-3-4-3z" class="T"></path><path d="M255 140h0 6 0 3 1c-2 1-4 2-5 5 0 2 1 3 1 5-1 0-3 1-4 1l-1-1c0-4 0-7-1-10z" class="AT"></path><path d="M315 139l9-2c1 0 1 0 2-1h1l1 1c2 1 2 3 3 5v3c1 0 1 0 2-1 3-2 8-4 12-4l-1 1c-1 0-2 1-3 1-1 1-2 1-3 2-2 1-4 2-6 4-1 1-1 3-2 4v1c0 2 1 5 0 6 0 1-2 2-2 3-1 1-2 1-3 2l-2-3-1 2c-1 1-2 1-3 2h-2l-1 3-1 2-1 2-1-1c-1 1-2 1-3 2v1h-1c-2-1-3 0-5-1-1 0-2-1-3-2 0-2-1-4-1-6l-1 1c-1-2-2-4-2-7 0-1 0-1-1-2 0-1-1-1-1-2v-3c0-2-2-4-2-6h-2c-1-1-2-1-3-1l-3-2-8-3c6-1 13-2 18 0 2 1 4 0 6 0s4 0 5-1v1h1c1-1 3-2 5-2 1 0 2 0 3 1z" class="AC"></path><path d="M328 137c2 1 2 3 3 5v3 2-1h-3l-5 5h-1v-1l1-1h1l2-1v-6c0-1 0-1 1-2v-2l1-1z" class="k"></path><path d="M331 142v3 2-1h-3c0-3 0-2 2-3l1-1z" class="AP"></path><path d="M318 144c0 1 1 1 1 2s0 2 1 4l1-1h2l-1 1v1h1l5-5h3v1c0 2-1 3-2 4h0c-2 2-5 5-8 5-2-1-3-2-4-3h-1v-1c0-1-1-2-1-3 0-2 0-2 1-4l2-1z" class="AH"></path><path d="M312 138c1 0 2 0 3 1l-1 1 3 1c1 0 2 1 3 2h-1l-1 1-2 1c-1 2-1 2-1 4 0 1 1 2 1 3v1h1c-2 1-3 1-5 0h0c-2 0-2 1-3 2l-1 1c-2 0-2-1-3-2 0-2 0-3 1-4 0-1 0 0 1-1-2-3-3-4-2-7v-1l1-1h1c1-1 3-2 5-2z" class="N"></path><path d="M312 138c1 0 2 0 3 1l-1 1-2 2-1 4c-1-1-2-2-3-2h-1c-1-1-1-2-2-3l1-1h1c1-1 3-2 5-2z" class="T"></path><path d="M312 138c1 0 2 0 3 1l-1 1-2 2v-2c-2-1-3 0-5 0 1-1 3-2 5-2z" class="N"></path><path d="M314 140l3 1c1 0 2 1 3 2h-1l-1 1-2 1c-1 2-1 2-1 4 0 1 1 2 1 3v1h1c-2 1-3 1-5 0h0l1-1-1-2h-1v1l-1 1v-1-1c0-2 0-3 1-4l1-4 2-2z" class="C"></path><path d="M317 141c1 0 2 1 3 2h-1l-1 1-2 1h-3v-1c1-2 2-2 4-3z" class="j"></path><path d="M331 145c1 0 1 0 2-1 3-2 8-4 12-4l-1 1c-1 0-2 1-3 1-1 1-2 1-3 2-2 1-4 2-6 4-1 1-1 3-2 4v1c0 2 1 5 0 6 0 1-2 2-2 3-1 1-2 1-3 2l-2-3-1 2c-1 1-2 1-3 2h-2l-1 3c-1-3 0-5-1-8v-3h-1l-2-2c-1 1-1 1-2 1h-1v-1c1-1 1-2 3-2h0c2 1 3 1 5 0 1 1 2 2 4 3 3 0 6-3 8-5h0c1-1 2-2 2-4v-2z" class="h"></path><path d="M321 157c1-1 1-1 2-1l1 2c-2 1-4 4-6 5l3-6z" class="g"></path><path d="M315 157c1-1 1-1 3-1h2l1 1-3 6-1 2-1 3c-1-3 0-5-1-8v-3z" class="AY"></path><path d="M329 151c0 3-1 4-3 6-1 0-1 0-2 1l-1-2c-1 0-1 0-2 1l-1-1h-2c-2 0-2 0-3 1h-1l-2-2c-1 1-1 1-2 1h-1v-1c1-1 1-2 3-2h0c2 1 3 1 5 0 1 1 2 2 4 3 3 0 6-3 8-5z" class="J"></path><path d="M277 140c6-1 13-2 18 0 2 1 4 0 6 0s4 0 5-1v1l-1 1v1c-1 3 0 4 2 7-1 1-1 0-1 1-1 1-1 2-1 4 1 1 1 2 3 2l1-1v1h1c1 0 1 0 2-1l2 2h1v3c1 3 0 5 1 8l-1 2-1 2-1-1c-1 1-2 1-3 2v1h-1c-2-1-3 0-5-1-1 0-2-1-3-2 0-2-1-4-1-6l-1 1c-1-2-2-4-2-7 0-1 0-1-1-2 0-1-1-1-1-2v-3c0-2-2-4-2-6h-2c-1-1-2-1-3-1l-3-2-8-3z" class="m"></path><path d="M303 166c-1-1-1-6-1-8-1-4-1-7-2-11 0-2 0-4 1-5h4c-1 3 0 4 2 7-1 1-1 0-1 1-1 1-1 2-1 4 0 3 1 6 0 9l1 5c-1 0-1-1-3-2z" class="AT"></path><path d="M305 163v-1l-1 1c-1-1-2-6-1-8 0-1 1-1 1-2v-3h2c-1 1-1 2-1 4 0 3 1 6 0 9z" class="AH"></path><path d="M285 143h12c1 5 2 8 3 13 1 3 0 10 3 13v-1c1 1 1 3 2 4 0 1 0 1-1 1s-2-1-3-2c0-2-1-4-1-6l-1 1c-1-2-2-4-2-7 0-1 0-1-1-2 0-1-1-1-1-2v-3c0-2-2-4-2-6h-2c-1-1-2-1-3-1l-3-2z" class="Ab"></path><path d="M293 146h1c2 3 2 5 3 8 1 2 2 4 3 7v1 2 1l-1 1c-1-2-2-4-2-7 0-1 0-1-1-2 0-1-1-1-1-2v-3c0-2-2-4-2-6z" class="Ad"></path><path d="M305 154c1 1 1 2 3 2l1-1v1h1c1 0 1 0 2-1l2 2h1v3c1 3 0 5 1 8l-1 2-1 2-1-1c-1 1-2 1-3 2v1h-1c-2-1-3 0-5-1 1 0 1 0 1-1-1-1-1-3-2-4v-2c2 1 2 2 3 2l-1-5c1-3 0-6 0-9z" class="C"></path><path d="M309 156h1c1 0 1 0 2-1l2 2h1v3c-1 2-3 3-3 5 0 1-1 2-2 2-1-4-1-7-1-11z" class="AT"></path><path d="M315 160c1 3 0 5 1 8l-1 2-1 2-1-1c-1 1-2 1-3 2v1h-1c-2-1-3 0-5-1 1 0 1 0 1-1-1-1-1-3-2-4v-2c2 1 2 2 3 2 2 1 2 2 3 1l1-2c1 0 2-1 2-2 0-2 2-3 3-5z" class="Ac"></path><path d="M315 160c1 3 0 5 1 8l-1 2-1 2-1-1c-1 1-2 1-3 2v-1l1-1v-1c1-1 1-3 1-5s2-3 3-5z" class="Ad"></path><path d="M198 134h18c4 0 8 0 12 1 2 0 4 0 6 1h0 1l1 1-1 1h2c1 0 2 1 3 1h2c3 1 4 3 7 4l-1 1c1 1 2 2 4 3 1 0 3 2 4 3l1 1c1 0 3-1 4-1 0-2-1-3-1-5 1-3 3-4 5-5h1c4-1 6-1 10 0h1l8 3 3 2c1 0 2 0 3 1h2c0 2 2 4 2 6v3c0 1 1 1 1 2 1 1 1 1 1 2 0 3 1 5 2 7l1-1c0 2 1 4 1 6 1 1 2 2 3 2 2 1 3 0 5 1h1v-1c1-1 2-1 3-2l1 1-3 6-3 9h-1c-2-2-4-4-7-6l1-1-1-2c-2 0-3 0-4 1-2 0-4-2-5-3l-7-4-3-3-4-2c-4-1-6-3-10-4h1c1 1 2 2 4 3h-1 0c-1-1-2-1-3-1h-1 0c-1 1 0 1-1 1l2 2h0c-5-2-9-5-13-8-2-2-4-3-6-4s-3-2-5-3v1l1 1c-1 0-1-1-2-2h-2c-1 0-2-1-2-1l-9-4c-7-3-14-5-21-7 1-1 2-1 4-1l-15-3c-2 0-4 0-5-1h-3c-1 0-2 0-2-1v-1c3 0 6 1 9 0h1z" class="AU"></path><path d="M281 169c4 0 7 2 10 4v3l-7-4-3-3z" class="AT"></path><path d="M291 173c3 2 6 3 9 5-2 0-3 0-4 1-2 0-4-2-5-3v-3z" class="AY"></path><path d="M249 152c7 3 13 6 19 9l9 6c-4-1-6-3-10-4-2-1-5-3-8-4-2-2-5-2-7-4h0l-3-3z" class="s"></path><path d="M261 150l2 2c2 1 4 2 7 3 0 1 2 1 2 2 3 3 7 5 10 7l9 6c2 1 4 3 6 3l1 1-1 1-20-12c-3-2-6-5-9-6-4-2-8-4-11-6 1 0 3-1 4-1z" class="AQ"></path><path d="M198 134h18v2c1 1 2 1 3 2h2c-2 1-5 1-7 0h-2c-3 0-6-1-8-1-3-1-4-1-6 0-2 0-4 0-5-1h-3c-1 0-2 0-2-1v-1c3 0 6 1 9 0h1z" class="AK"></path><path d="M216 134c4 0 8 0 12 1 2 0 4 0 6 1h0 1l1 1-1 1h2c1 0 2 1 3 1h2c3 1 4 3 7 4l-1 1c-7-3-14-5-21-6h-1c-1 1-3 1-5 0h-2c-1-1-2-1-3-2v-2z" class="s"></path><path d="M301 171c1 1 2 2 3 2 2 1 3 0 5 1h1v-1c1-1 2-1 3-2l1 1-3 6-3 9h-1c-2-2-4-4-7-6l1-1v1h1l1-1 2 2c1-1 1-1 1-2l-4-3-2-2c0-1 0-2 1-3v-1z" class="o"></path><path d="M305 176h3v1c-1 1-1 2-1 3s-1 2-1 3c-2 0-3-1-5-2h1l1-1 2 2c1-1 1-1 1-2l-4-3c1 0 2 0 3-1z" class="AY"></path><path d="M301 171c1 1 2 2 3 2 2 1 3 0 5 1h1c0 1-1 1-2 2h-3c-1 1-2 1-3 1l-2-2c0-1 0-2 1-3v-1z" class="Ad"></path><g class="Ac"><path d="M301 172c1 2 2 3 4 4-1 1-2 1-3 1l-2-2c0-1 0-2 1-3z"></path><path d="M295 152v3c0 1 1 1 1 2 1 1 1 1 1 2 0 3 1 5 2 7l1-1c0 2 1 4 1 6v1c-1 1-1 2-1 3l-2-1-1-1c-2 0-4-2-6-3l-9-6c-3-2-7-4-10-7l4 1c2 2 4 3 6 4l3-1 2-1 4 3c0 1 0 1 1 2l1 1h1c1-3 1-6 0-9 1-1 1-3 1-5z"></path></g><path d="M287 160l4 3c0 1 0 1 1 2l1 1c-3 0-8-3-11-4l3-1 2-1z" class="W"></path><path d="M300 165c0 2 1 4 1 6v1c-1 1-1 2-1 3l-2-1-1-1h0c1-3 1-5 2-7l1-1z" class="AY"></path><path d="M213 140c12 3 25 6 36 12l3 3h0c2 2 5 2 7 4 3 1 6 3 8 4h1c1 1 2 2 4 3h-1 0c-1-1-2-1-3-1h-1 0c-1 1 0 1-1 1l2 2h0c-5-2-9-5-13-8-2-2-4-3-6-4s-3-2-5-3v1l1 1c-1 0-1-1-2-2h-2c-1 0-2-1-2-1l-9-4c-7-3-14-5-21-7 1-1 2-1 4-1z" class="K"></path><path d="M265 140h1c4-1 6-1 10 0h1l8 3 3 2c1 0 2 0 3 1h2c0 2 2 4 2 6s0 4-1 5c1 3 1 6 0 9h-1l-1-1c-1-1-1-1-1-2l-4-3-2 1-3 1c-2-1-4-2-6-4l-4-1c0-1-2-1-2-2-3-1-5-2-7-3l-2-2c0-2-1-3-1-5 1-3 3-4 5-5z" class="U"></path><path d="M288 145c1 0 2 0 3 1h2c0 2 2 4 2 6s0 4-1 5c-1-5-2-8-6-12z" class="AY"></path><path d="M264 147c1-1 1-2 1-2 3-2 7-1 10-1 1 0 1 0 3 1h3c3 2 6 5 8 9v1l1 4c1 2 1 3 1 4l-4-3h0v-1-1l-2-3c0-1 0-2-1-2-1-3-3-5-6-6h-1c-1-1-2-1-3-1h0l-1-1-1 1h-2c-1 0-1 1-1 1l-1 1h0l-2-1h-2z" class="C"></path><path d="M264 147h2l2 1h0l1-1s0-1 1-1h2l1-1 1 1h0c1 0 2 0 3 1h1c3 1 5 3 6 6 1 0 1 1 1 2l2 3v1 1h0l-2 1-3 1c-2-1-4-2-6-4l-4-1c0-1-2-1-2-2-3-1-5-2-7-3l1-1v-4z" class="a"></path><path d="M273 149c4 3 9 6 12 9-2 0-4 0-6-1l-2-2c-2-1-4-3-4-6z" class="k"></path><path d="M272 149s1-1 2-1l1 1c4 0-2-3 5 1 2 1 4 4 5 5l2 3v1 1l-2-2c-3-3-8-6-12-9h-1z" class="W"></path><path d="M264 147h2l2 1h0l1-1s0-1 1-1h2l1-1 1 1h0c1 0 2 0 3 1h1c3 1 5 3 6 6 1 0 1 1 1 2-1-1-3-4-5-5-7-4-1-1-5-1l-1-1c-1 0-2 1-2 1l-1-1h-1v2l2 1v1h-1l-1 2c1 1 2 1 3 2l3 1v1l-4-1c0-1-2-1-2-2-3-1-5-2-7-3l1-1v-4z" class="c"></path><path d="M270 154c-1-1-2-3-3-4l1-1 2 1 2 1v1h-1l-1 2z" class="h"></path><path d="M153 137c9-3 21-2 31-1h5 1 3c1 1 3 1 5 1l15 3c-2 0-3 0-4 1 7 2 14 4 21 7l9 4s1 1 2 1h2c1 1 1 2 2 2l-1-1v-1c2 1 3 2 5 3s4 2 6 4c4 3 8 6 13 8h0l-2-2c1 0 0 0 1-1h0 1c1 0 2 0 3 1h0 1c-2-1-3-2-4-3h-1c4 1 6 3 10 4l4 2 3 3c1 1 2 2 4 3l3 4c-1 1-1 1-1 3 2 1 3 3 4 5l2 3 2 2c1 2 2 4 3 5 1 4 1 7 1 11h0l-1-1c-2 3-3 5-6 7h-2c-3 2-5 3-7 4s-7 2-8 3l-1 1h0v-3c-5-9-11-18-18-25-4-5-8-9-12-13-13-12-30-22-46-28-3-1-10-4-13-2h-2l-1 1h-2c-2-2-1-3-1-5l-2-1 3-1c0-1 0-1-1-2-4-3-14-2-19-1-9 2-16 6-23 12v-1c0-5 9-12 13-16z" class="b"></path><path d="M190 136h3c1 1 3 1 5 1l15 3c-2 0-3 0-4 1-2 0-4-1-7-1-5-1-9-1-13-4h1z" class="N"></path><path d="M241 153h2c1 1 1 2 2 2l-1-1v-1c2 1 3 2 5 3s4 2 6 4l-1 1c1 1 3 1 4 3 2 1 3 2 4 4l6 6h-1l-3-3h-1l-2-2v-1h0-1c3 3 5 5 7 9-5-4-8-9-13-13-4-4-9-7-13-11z" class="AI"></path><path d="M272 183c3 5 7 10 10 15 2 4 5 8 7 12v2c1 1 1 1 2 1h2v1c-3 2-5 3-7 4-2-1-2-1-2-3 1 1 1 1 2 1s2-1 2-2c-1 0-1 0-2-1-1-2-2-3-2-5-1-3-2-5-3-8-1-1-2-2-2-4-1-3-4-6-5-9-1-1-2-2-2-3v-1z" class="z"></path><path d="M267 177c-2-4-4-6-7-9h1 0v1l2 2h1l3 3h1l6 7c5 7 9 15 13 23l3 6h-1c-2-4-5-8-7-12-3-5-7-10-10-15l-5-6z" class="l"></path><defs><linearGradient id="BO" x1="283.79" y1="183.628" x2="275.358" y2="190.159" xlink:href="#B"><stop offset="0" stop-color="#070202"></stop><stop offset="1" stop-color="#1f0806"></stop></linearGradient></defs><path fill="url(#BO)" d="M255 160c4 3 8 6 13 8h0l-2-2c1 0 0 0 1-1h0 1c1 0 2 0 3 1h0 1c-2-1-3-2-4-3h-1c4 1 6 3 10 4l4 2 3 3c1 1 2 2 4 3l3 4c-1 1-1 1-1 3 2 1 3 3 4 5l2 3 2 2c1 2 2 4 3 5 1 4 1 7 1 11h0l-1-1c-2 3-3 5-6 7h-2v-1h-2c-1 0-1 0-2-1v-2h1l-3-6c-4-8-8-16-13-23l-6-7-6-6c-1-2-2-3-4-4-1-2-3-2-4-3l1-1z"></path><defs><linearGradient id="BP" x1="296.554" y1="201.73" x2="299.596" y2="209.838" xlink:href="#B"><stop offset="0" stop-color="#cc534d"></stop><stop offset="1" stop-color="#e97061"></stop></linearGradient></defs><path fill="url(#BP)" d="M293 192c1 1 2 1 3 2l2-2c1 2 2 4 3 5 1 4 1 7 1 11h0l-1-1c-2 3-3 5-6 7h-2v-1c1 0 2-2 2-3 3-4 2-10 0-14-1-1-1-3-2-4z"></path><path d="M293 192c1 1 2 1 3 2l2-2c1 2 2 4 3 5-1 1-1 1-1 2v1h-1v-1l-3-3h-1c-1-1-1-3-2-4z" class="h"></path><defs><linearGradient id="BQ" x1="285.565" y1="189.474" x2="278.657" y2="196.792" xlink:href="#B"><stop offset="0" stop-color="#0f0605"></stop><stop offset="1" stop-color="#2e0a06"></stop></linearGradient></defs><path fill="url(#BQ)" d="M274 181v-1-1-1l1 1c2 1 2 2 3 3 2 2 4 3 6 6l2 3 1 1 1 2c1 1 1 3 2 4 2 4 3 6 2 11-1 0 0 0-1-1-1 1 0 2 0 3v2c-1 0-1 0-2-1v-2h1l-3-6c-4-8-8-16-13-23z"></path><path d="M267 163c4 1 6 3 10 4l4 2 3 3c1 1 2 2 4 3l3 4c-1 1-1 1-1 3 2 1 3 3 4 5l2 3 2 2-2 2c-1-1-2-1-3-2s-2-3-3-4c-1 0-3-3-4-4-5-6-12-10-18-16h0l-2-2c1 0 0 0 1-1h0 1c1 0 2 0 3 1h0 1c-2-1-3-2-4-3h-1z" class="s"></path><path d="M288 175l3 4c-1 1-1 1-1 3l-4-6s1 0 2-1z" class="K"></path><path d="M290 188c1-1 2-1 3-1l3 3 2 2-2 2c-1-1-2-1-3-2s-2-3-3-4z" class="i"></path><path d="M267 163c4 1 6 3 10 4l4 2 3 3c1 1 2 2 4 3-1 1-2 1-2 1-5-4-10-5-15-8-2-2-3-2-5-2 1 0 0 0 1-1h0 1c1 0 2 0 3 1h0 1c-2-1-3-2-4-3h-1z" class="l"></path><defs><linearGradient id="BR" x1="225.609" y1="191.004" x2="246.612" y2="169.691" xlink:href="#B"><stop offset="0" stop-color="#0f0000"></stop><stop offset="1" stop-color="#6e211d"></stop></linearGradient></defs><path fill="url(#BR)" d="M185 152v-1l-1-1v-2c0-1 1-1 2-1 2 0 5 0 7-2h1v-2c3 0 7 2 10 3 1 0 2 1 3 1 2 0 4 1 6 1 4 2 8 3 12 6 25 13 46 37 59 61 0 2 0 2 2 3-2 1-7 2-8 3l-1 1h0v-3c-5-9-11-18-18-25-4-5-8-9-12-13-13-12-30-22-46-28-3-1-10-4-13-2h-2l-1 1z"></path><path d="M227 160h2l1 1v2c1 0 0 0 1 1v-2l1-1 1 1v1c0 1 2 3 3 3 3 1 5 3 7 6 0 0-1 0-1 1-5-2-8-6-11-9v1c-1 0-1-1-2-1h0 0c-1-2-1-3-2-4z" class="q"></path><path d="M207 147c2 0 4 1 6 1 4 2 8 3 12 6-4 0-9-3-13-1 0 1 0 0-1 1-1-1-1-1-2-1v1h0c-2 0-3-2-5-3-1-1-3-2-4-2l1-1 1-1h2l1 1h0c1 0 1 0 2-1h1-1z" class="T"></path><path d="M333 395h1c2 0 4 0 5 2h0 1 2c7-2 15 1 21 4l21 12 6 5c0 1 1 3 2 4 0 1 1 1 1 2v1l1 1v4c-1-2-2-3-4-4l-1 1c3 2 5 3 5 7h0l1 1v2h0c-1 0-1-1-2-1l-1 1 1 2h2c1 1 1 1 3 2l1 1 1 1-1 1-2-1h-1c2 1 3 2 5 4 0 0 2 1 2 2 0 2 1 4 2 6s0 6 0 7l-1 1v1c1 1 1 1 1 2 0-1 0-2 1-3v-1l1 1c0-2 1-4 1-6 1 0 3-1 4-2l2 2c2 1 4 4 5 5l-1-3v-6l7 17 1-1v-1l-1-1v-2h-1v-2l-1-1v-4c-1-1 0-1-1-2 0-1 0 0 1-1h1 0v-2l1-1v2c0 1 1 2 2 3v1l-1 1 1 1c1 2 1 5 2 7s1 4 2 5c0 2 1 5 1 7l6 13 3 6c1 2 1 5 2 7l3 6c0 3 1 6 2 9 1 1 2 3 2 4v1l-1 1 1 1h0c0 1 1 2 1 4l1 1-1 1c3 8 7 16 9 24 3 7 5 14 8 21 1 3 3 6 4 10l8 21c1 2 4 12 5 13l3 10 24 66c0 2 1 6 2 8l1-1c2 3 2 6 3 9h1l1 3 1 4c0 1 1 1 2 2 2 3 3 6 4 9s1 6 3 8c1-1 3-2 5-3l6-5h0c2-2 3-3 5-4 4-3 8-8 11-12 1-2 3-4 4-6v-1c2-2 4-5 5-8l6-11 1-2-1-1v-4c-1 1-3 5-3 6-1 1-2 3-3 4-2 0-2 1-4 3v1c-1 1-2 1-3 2v-1c2-2 4-4 5-7l-1-1-2 1-1 3h-3c1-2 3-4 4-6 0-2 1-4 1-6-1-2-2-4-2-6v-1s0-1-1-2c0-1 0-1-1-2v-1-2h0c0-2-1-3-2-4-1 2-2 3-4 3l-1-1 3-8v-1c-1-1-1-2-1-3l1-2c-1 0-2 0-3-1 0-1 1-1 2-1 2 0 4-1 6-2l2-1 2-1c1-1 3-2 4-3 2 0 3 0 5-1 1 0 3-1 4-1v1l-1 1c2-1 3-3 4-4 2 0 3-1 4-2 1 0 1-1 2-1h1c2-1 3-3 4-4 1 0 2 0 3 1 0-1 1-1 1-2l3-8v-1h-3c1-2 2-5 2-7l1-8v-2c-2 1-4 4-5 6l-2-1h0c0-2-1-3-1-5l1-1c2-3 4-5 6-9h0l1-7v-5c1-1 1-1 1-2l1-6-6-6c-1-1-2-1-3-1l-2 2h-1l1-2c1-1 2-1 4-1l1 1 1-1c0-1-1-2-2-3h1c2 2 3 4 6 4 1 2 2 3 3 4h0l2-1c1-4 2-8 4-13 1-4 1-8 4-11v-4h0c6-5 9-12 14-17l1 1v-1c1-1 2-2 2-3l3-8 4-11 4-11c1-2 1-5 1-7 1-1 1-3 2-5l1-5 2-11c2-1 3-3 4-5 0-1 0-1 1-1l1 1h0l2-8 4 2h0c2 1 3 2 4 3h1 0v-6h0c1 2 1 5 0 7v1h-1v1c-6 8-9 19-10 28 1 2 0 3 1 6l1 1c0 2 1 5 3 7l5 10c1 2 1 5 3 8l1-2c2 2 4 7 6 8h0v-3c6 11 9 22 13 33h0c1 2 1 5 2 7 4 21 2 40-6 59-1 4-4 7-5 11 0 2-2 3-1 6l6-12h2 0 1c1-2 1-4 2-5v-1c1-1 1-2 1-3h0v-1c1-2 1-4 2-6 1 0 1 1 2 2h0c-1 1-1 1-1 2v1c0 1 0 1-1 2v1h0c0 1 0 1-1 2v1 1h0l-1 3h0l-1 1v1c-1 1-1 2-1 4-7 17-19 31-35 41l-14 7c-3 2-6 5-10 7l-2 2h0c-2 1-3 2-5 3s-4 2-6 4c-1 1-2 2-4 2-5 4-8 9-8 16l1 1-1 1c0-1-1-2-2-3 0-1-1-1-1-1 0 2 1 4 3 6h-1 0l-1-2c-1 1-1 1-1 2v17c0 6 2 12 3 19-1-2-2-5-2-7-1-3-1-6-1-9h-2c0 1-1 2-2 2-1 3-3 5-2 8l1 2h0l-3 3c0 1 0 2-1 2 2 1 2 1 2 3l-1 3h1c0 1-1 1-1 2l-1 2v1l-1 1h0v1c-1 1-1 2-1 3l-1 1-1 1-1 3h0c-1 1-1 1-1 2l-2 3c0 1 0 1-1 2l-1 3c0 1 0 0-1 1v1c0 1-2 4-2 5v1l-1 1c-1 1 0 0-1 2 0 0 0 1-1 1v1 1 1l-1 1c0 1-1 2-1 3-1 1-1 1-1 2-1 0-1 2-1 3s-1 3-1 4l-5 14-28 75c-5 11-10 23-15 33-1 1-1 2-3 2-2-2-4-6-5-9-17-34-27-71-40-106-9-21-19-42-30-62h-2l-1-4c-1 3-4 9-7 12-2-1-3-1-4 0l2-4c1-1 1-1 1-2h-1l2-4-1-1c1-2 2-3 3-6-1-1-1-4-1-6l-2-2-1-1c0-1-2 0-2-1-1 0-1 0-2-1h0c-1 0-3-1-4-2l-2-1c-2-1-3-1-5-1-1 0-2 0-2-1 1-2 3-3 4-4 5-3 8-6 10-11 2-4 1-8-1-12-1-2-2-4-3-5-3-3-6-4-10-4h0l2-1v-2c-1-1-2-1-2-1h-1l-1-2v-1c0-2-1-4-2-6-1 0-3-2-4-2-3-2-6-5-9-7-4-2-9-5-13-8-10-6-19-15-25-25-16-24-21-54-16-82h1c0-1 1-1 1-1h1 3c1-2 3-10 4-13l4-13 3-7 5-11c0-4 3-6 4-9l2-3h-1l-3 3c0 1 0 0-1 1v1c-1-1 0-1 0-3l-2 1h-1c-2-4-1-7-3-11v-3c-1 0-1-1-2-2-2-5-6-10-10-11-2-1-3-1-4-2h-2l-1-1v-1c-1 0-2 0-4 1v-1c-4 0-6-2-9-5v2c-2-1-3-3-4-5h0v-3c-1-2-1-4-1-6h0l-1-1c1-3 4-7 6-9l9-6-3-5h-3c-3 2-7 3-10 4-1-1-2-1-2-2-3 1-5 0-8-1-1-1-2-3-3-4-1-2-1-4 0-6l2-2h0v1h0c2 0 2-2 3-3 0 2 0 4 1 5 2 2 3 2 6 2l1 1h0v-2l4-3c0-1-1-2-1-3s0-2-1-2v-5c-1-1-2-1-3-1v-2c0-1 0-2-1-2l3-2c1-1 2-1 3-1h0c1-1 2-1 2-1l1-1v-2l-2-2v-1-1-2h0c0-2 1-4 3-5 1-2 3-2 5-2z" class="b"></path><path d="M638 565l1-1-1-1c-1 0-1-1-1-2s0-1-1-1l1-1h2l-1 1h1l1 1c0 2-1 3-1 4h-1z" class="e"></path><path d="M338 422v1c1 2 0 1 1 2v6l-1 1c0-1-1-3-2-4 0-2 1-4 2-6z" class="G"></path><path d="M329 421h-1c0-2 1-1 1-2 0 0-1-1 0-2 1 1 2 2 4 3 0 1-1 2-1 3-1-1-1-1-1-2h-2z" class="e"></path><path d="M329 421h2c0 1 0 1 1 2l2-1v4l-1-2h-1c-1 1-2 2-3 2h-2l3-3-1-2zm307 165v-1c2-3 2-3 1-6v-3c1 2 1 3 2 4l-1 1 1 1 2-1-3 6-2-1z" class="E"></path><path d="M411 657l4 2c0 2 0 3-1 5l-6-3c2-1 2-2 3-4z" class="x"></path><path d="M417 692c3 0 5 0 8 1h1v1c1 1 1 1 2 1 1 2 3 4 3 6 0-1-1-1-1-1l-2-1c-2-2 1 0-1-1-1-1-1-2-2-2-3-1-5-3-8-4z" class="e"></path><path d="M415 659c2 1 6 2 7 3l-1 1h-1v2h0c-1 1-2 0-2 0-2 0-3-1-4-1 1-2 1-3 1-5z" class="F"></path><path d="M338 457c3-1 4-1 7 0h2l1 2h-2c1 1 2 1 2 2h1 1l1 1c-1 0-1 1-2 0h-1c-1-1-2-1-4-1l-3-3c-1 0-2 0-3-1h0z" class="E"></path><path d="M339 421c2 1 2 1 3 3v1l1 4-3 3-1 1-1-1 1-1v-6c-1-1 0 0-1-2v-1l1-1z" class="B"></path><path d="M370 506l2-1v2h1c0 1-1 1-1 2-1 1 0 1-1 2l1 1c-1 2-1 5-3 8-2-4 1-6 1-10l-2-1 2-3zm10 62l1 1h0c0 1 1 1 2 1v-1c1-1 1-2 2-2s1 0 2 2v1 2l1 2 1 1h-1l1 2c1-1 0-1 1-2v1 1c-1 0-1 1-1 0-3-1-4-4-6-6l-1 1h0 0l-2-2h-1l1-2z" class="E"></path><path d="M333 440c1-1 2-2 2-3 1 0 3 1 3 2v1h1c1 1 2 3 4 4h-1c-1 0-2-1-3-1l-2 3-1-1-3-5zm6-19l2-1c2 1 5 4 6 6l1-1 2 2c1 0 2 2 2 2v1c1 1 1 1 0 2 0-1-2-3-2-3-1-1-1 0-2-1-2-1-3-1-4-1s-1 1-1 2l-1-4v-1c-1-2-1-2-3-3z" class="S"></path><path d="M377 641l4 1c3 2 7 4 9 8-1 0-2-1-3-1h-1c-1-1-1 0-1-1l-2-2h-1c-1-1-2-1-3-1l-1-1h-2l-1-1 2-2z" class="AE"></path><path d="M653 521c-1-2-2-3-2-5 0 1 1 1 2 1 0 0 1-1 1-2s0-2 1-3c2 2 4 4 5 6h-1c-1 1-1 1-2 1l-2 2h-2z" class="E"></path><path d="M462 779c1 4 3 7 5 10 4 9 6 19 9 28h-1 0c0-1 0-1-1-2v-2c-1-3-2-7-3-9-1-3-2-6-3-8-2-5-5-11-6-17z" class="e"></path><path d="M378 567l2 1-1 2h1l2 2h0l-2 1v1c1 0 2 1 2 2h1l2 1c0 1 1 2 1 2l-2-1-2-1-3-2c-2-2-4-3-7-2-2 1-2 2-3 3v-2c1-2 3-2 5-3h1c1-2 2-3 3-4z" class="F"></path><path d="M399 670l2 1c4 2 8 4 12 5l2 1s-1 1-2 1c-2 0-5-2-6-2-3-1-6-1-8-2s-3-2-5-3l5-1z" class="f"></path><path d="M370 638v-2-1l1-1v1c0 2 1 3 2 5v2h1 0 0v2l1 1h0c2 0 2 1 4 1l1 1c2 0 3 1 5 2-3 0-7-2-9-1l-2 2c0-1-1-2-2-3l-2-7v-2z" class="E"></path><path d="M400 653c2 0 8 3 11 4-1 2-1 3-3 4l-10-4 2-4z" class="D"></path><path d="M612 678l1 1 2-1c2 0 4-1 6-1-1 1-2 1-3 1h1c1 1 2 1 3 1v1l-1-1-1 2h0c-3 1-8 2-11 2h1 0l-1-1 1-1v-1l-2 1c0-1-1-1-2-2h0l1-1h5z" class="O"></path><path d="M365 538c1-3 3-8 5-9l-1 1v2l-1 1v1 1l-1 1h2c0-1 0-2 1-3 0-1 0-2 1-2 0 2-1 3-2 5h1c1-1 2-4 3-5-1 4-3 9-5 12h0c-1 1-1 0-2 1l-1 4c-1-1 0-3 0-4l-2-2c1-1 1-3 2-4z" class="v"></path><path d="M365 538l2 2c0 1-1 3-1 4h0l-1 4c-1-1 0-3 0-4l-2-2c1-1 1-3 2-4z" class="p"></path><path d="M612 645l-2-2c1-2 1-4 1-5 1-1 0-1 1-2h0v-3h0c1-1 2-1 4-1l-1-1c1 0 2-1 3-2v1 1 1c-1 1-1 1-2 1v1h2l-1 1h-1c0 2 2 1 1 2h-1v3c-1 1-1 1-2 1v1c0 1-1 2-2 3z" class="y"></path><path d="M653 521h2l2-2c1 0 1 0 2-1h1c1 2 2 4 3 5l1 2v2c-1-1-2-1-4-1 0 2 1 2 2 3h0c0 1 1 3 1 4l-2-2h-1c-1 0-3-5-4-6-1-2-2-3-3-4z" class="B"></path><path d="M656 525c1 0 2 1 3 2 0-2-1-3 0-5l4 1 1 2v2c-1-1-2-1-4-1 0 2 1 2 2 3h0c0 1 1 3 1 4l-2-2h-1c-1 0-3-5-4-6z" class="P"></path><path d="M343 464c2 0 3 1 4 1h1c1 0 2 1 2 1 0 1-1 1-1 2h1l1-2c1 2 0 0 0 2l1 1-1 1 3 1v2 1l-2-1v1c1 0 2 1 2 2-1 0-2-1-3-2l-1 1c-1 0-2-1-3 0v1h1 0c1-1 1 0 2-1h1c1 1 1 0 1 1h0c-1 0-3 0-4 1h-1c-2-2-2-3-3-4h0l2-2 1-1v-2l-1-1h2c-2-1-3-2-5-3z" class="O"></path><path d="M438 729l1-5c-1-1-1-1-1-2l2 2v1h1 0c2 4 3 7 6 9l2 3-1 1c-1-1-2-2-3-2l1 2-1 1v-2h-1v1c-1 0-1-1-2-2h2l-1-1-2-1v4c-1-2-1-3-1-5l-1 1c0-1-1-1-1-1v-4h0z" class="E"></path><path d="M440 725h1 0c2 4 3 7 6 9-2 1-2 1-3 1-1-1-4-3-4-4v-6z" class="F"></path><path d="M414 667c4 1 7 2 10 4h-1 0c-1-1-1-1-2-1l-1-1h-1l-1 1c1 0 2 1 3 1 2 1 3 1 4 2s2 2 2 3c1 2 0 3 0 5-3 0-7-1-9-3h2 1l1-3h0c-2-1-3-2-5-2 1-1 1-1 1-2-1-1-2-1-2-2-1 0-1-1-2-2z" class="O"></path><path d="M409 687c3 2 5 4 8 5s5 3 8 4c1 0 1 1 2 2 2 1-1-1 1 1l2 1s1 0 1 1h1c0 1 0 1 1 2v1l1 1v2-1c1 1 1 3 0 5v2h0v1c1-2 1-2 2-3-1 2-2 3-3 5v-3c1-2 0-3 0-6l-3-3v-2c-3-4-5-5-9-6-2-1-4-2-6-2s-3 0-4-1c0-2-1-4-2-6z" class="y"></path><path d="M630 674h1c2-1 4-1 7-2v1c-1 0-2 1-3 2h1c-4 3-11 5-16 6l1-2 1 1v-1c-1 0-2 0-3-1h-1c1 0 2 0 3-1-2 0-4 1-6 1l-2 1-1-1 3-2 7-1h3l5-1z" class="B"></path><path d="M368 509l2 1c0 4-3 6-1 10l-7 16v-5c0-2 2-4 2-6-1-3 1-5 0-7 0-4 3-6 4-9z" class="f"></path><path d="M618 632h1v-2l1-2h-1c0-1 1-2 1-2v-2l-1-1h3c0 1 1 1 1 2h1l-1 5v-1l-1-1-2 2 1 2-1 1c-1 1 0 1-1 2v1 2 2l-1 2c1 2 0 1 0 3v3l-3-2-3-1c1-1 2-2 2-3v-1c1 0 1 0 2-1v-3h1c1-1-1 0-1-2h1l1-1h-2v-1c1 0 1 0 2-1z" class="F"></path><path d="M615 646c0-1 1-1 1-2 0-2 0-2 1-4l1 1-1 1h1c1 2 0 1 0 3v3l-3-2z" class="v"></path><path d="M668 547c5 10 6 22 7 33v11h-3v-1-1l-1-1c1 0 0 0 1-1l1-1c-1-5-1-22-5-26h-2c0-1 1-2 1-3h1l1 1v1c1 0 1 1 1 2s0 1 1 2 0 1 0 2l1 1v4c1 1 1 3 1 5 1 1 0 2 1 3v-4c-1-1-1-2-1-3 1-1 0-1 0-2v-3c-1-1 0-2 0-2l-1-1v-1c0-1-1-2-1-3v-1-1h-1v-1-1-1l-1-1v-1h-1l1-1-1-1v-3z" class="AF"></path><path d="M596 722l2 16h-2c0 1-1 2-2 2v-4h1l-1-1h0c-2 2-3 3-4 5l-1 1c-1 1-2 1-3 2h0c-1 1-1 2-2 2l-3 3-3 3h0l9-12c3-3 6-8 7-12 1-2 1-4 2-5z" class="P"></path><path d="M595 720l1 2c-1 1-1 3-2 5-1 4-4 9-7 12 1-1 1-1 1-2 1-1 2-2 2-4l-3 3c-1 1-2 3-4 4h-1c0-3 3-6 5-8h-2v-1c0-1 1-2 2-3 1 0 4-3 5-4 2-1 2-2 3-4z" class="O"></path><path d="M587 728c1 0 4-3 5-4 0 4-3 5-5 8h-2v-1c0-1 1-2 2-3z" class="y"></path><path d="M475 774h0c2 4 4 6 6 9 1 0 1 1 2 0l1-2c1 0 2 1 3 2l-1 2c0 4 1 7 2 11v1l-1-1-3-3v-1-1h0l-1-1c0-1-1-2-1-3h0l-1-1v-1l1 1v-2h-1c0-1-1-1-1-1v1s1 6 1 7c1 1 1 1 1 2v2l1 2h-1v-1l-1-1v-1c-1-2-1-3-1-4v-1c-1-1-1-1-1-2h-1 0l-1-1v-2l-1-1c0-1 0-1 1-2h0c1-1 0-1-1-2v-1c-1-2-1-2-1-4z" class="C"></path><path d="M484 781c1 0 2 1 3 2l-1 2v3h-2c-1-1-1-3-1-5l1-2z" class="K"></path><defs><linearGradient id="BS" x1="607.723" y1="648.751" x2="613.937" y2="649.515" xlink:href="#B"><stop offset="0" stop-color="#1e1e1f"></stop><stop offset="1" stop-color="#39393a"></stop></linearGradient></defs><path fill="url(#BS)" d="M611 658c-2 1-3 1-4 1l-1-1c0-1 1-2 1-2v-1c1-2 1-2 1-3 1-1 0-2 0-3h0-1l1-3h1c2 0 4 1 7 2l2 1 1 1-2 2h-1 0c-2 1-2 2-3 4v2h0-2z"></path><path d="M616 648l2 1 1 1-2 2h-1 0c-2 1-2 2-3 4v2h0-2-4 1c1-1 1-1 1-2h0c0-1 0-2 1-3h0l1-1 4-1c0-1 1-2 1-3z" class="B"></path><path d="M608 658c1-1 1-1 1-2h0c0-1 0-2 1-3h0l1-1c0 2 0 3-1 5l-2 1z" class="E"></path><path d="M339 462l1 1h-1 2 4v-1-1l1 1c0 2 0 1-1 2h-2c2 1 3 2 5 3h-2l1 1v2l-1 1-2 2h0c1 1 1 2 3 4h1c1 0 2 1 3 2h0-1c-2-1-5-3-7-4l-1 1c0-1 0-1-1-1l-1-1v-3l-4-2c-1-1 0-2 1-3v-1-1c0-1 1-1 2-2z" class="B"></path><path d="M337 464l2 1 1 2 3 2c0 1-1 2-2 2 1 2 2 2 2 4l-1 1c0-1 0-1-1-1l-1-1v-3l-4-2c-1-1 0-2 1-3v-1-1z" class="P"></path><path d="M343 475c2 1 5 3 7 4h1 0c1 0 1 0 2 1l1 1 1 1 1 1c2 2 4 6 5 10l1 2v1c-1-1-1-2-2-3 0-1-1-3-2-4v2 2l1 1v1c-1 0-1-1-2-2-2-5-6-10-10-11-2-1-3-1-4-2h-2l-1-1v-1c1 0 2-1 2-2l1-1z" class="S"></path><path d="M370 548l1 2v2h3v1 1l3 6 3 3v1l1 1v1c1-2 1-2 3-2l-3 3c-3-1-5-3-8-4l-6-3c0-1 0-2-1-3l1-3 3-6z" class="f"></path><path d="M370 548l1 2v2h3v1 1c-2 1-3 3-5 3l-1-2-1-1 3-6z" class="r"></path><path d="M533 851h0c2-3 3-6 4-9 1-2 2-3 4-5h0v3c-2 4-10 33-11 34l-1-1v-1l-1-1 3-13c1-2 1-5 2-7z" class="N"></path><path d="M441 738v-4l2 1 1 1h-2c1 1 1 2 2 2v-1h1v2l1-1-1-2c1 0 2 1 3 2l1-1 5 7c-1-2-2-3-4-4v3l1 1h0c0 1 1 2 1 3 1 0 1 1 1 1 1 2 1 3 1 4l1 1-2-2c0-1 0-2-1-2l-1 1c0 1 0 2 1 3s2 4 3 6h0c-1-2-2-3-3-5 0 0 0-1-1-1l-3-7-1 1c0 3 4 8 5 12-1-2-3-5-4-7 0-1-1-2-2-4 0-1-1-2-2-4l-3-6z" class="S"></path><path d="M336 445l1 1 2-3c1 0 2 1 3 1h1l1 1 1 2-1 3 2 2h4v1c-3 0-8 1-11 0-1-2-8 0-10 0-1 0-1-1-2-2l9-6z" class="E"></path><path d="M346 452c-1-1-7-2-7-3 2 0 3-2 5-4l1 2-1 3 2 2z" class="e"></path><defs><linearGradient id="BT" x1="439.061" y1="740.405" x2="427.758" y2="742.436" xlink:href="#B"><stop offset="0" stop-color="#383838"></stop><stop offset="1" stop-color="#5e5d5d"></stop></linearGradient></defs><path fill="url(#BT)" d="M432 729c1-1 2-2 2-3s1-1 1-2h0c1 1 1 1 0 2l-1 2v1h0 0 1l1 2h0 1l1-1-1-1v-2c-1-1-1 0-1-1l1-1c0 1 1 2 1 4h0v4s1 0 1 1c-1 3-2 6-2 9h0l-1 5-2 4c-1-1-1-4-1-6l-2-2-1-1c0-1-2 0-2-1-1 0-1 0-2-1 2-1 5-2 6-3v-1c2 0 3-1 4-2v-3h-1v-1l-1-1-1 1c-1 0-1 0-2-1 1 0 0 0 1-1h0z"></path><path d="M374 650l2-2c2-1 6 1 9 1 3 3 6 5 6 10v2l1 1h-2l-1-1c-2-1-3-2-4-3-3-3-7-4-11-7v-1z" class="H"></path><path d="M590 740c1-2 2-3 4-5h0l1 1h-1v4c-1 3-3 5-2 8l1 2h0l-3 3c0 1 0 2-1 2 2 1 2 1 2 3l-1 3c-1 2-2 5-3 7-1 0-1 1-1 2-1 1-2 3-3 5v1l-1 2-1 1c0 1 0 1-1 1v-1l1-1c0-1 0-2 1-3l3-6v-1l-1-1h2c0-2 1-3 1-4l3-3c0-1 0-2 1-3-2 0-2 0-2-1 0-2 0-3 1-4 0-1 1-1 1-2l-2-1-1 2c-1 2-2 4-3 7-2 2-3 4-4 7l-4 8-1 2c-1 1-2 3-2 4-1 3-3 6-4 9v2c-1 1-1 1-2 3 0 2-2 6-3 8h-1l4-9c6-16 13-31 22-45 0-2 1-4 2-5l1-1h0v-3 1 1l-1-1h-1c0 3-1 4-3 7 0-2 2-4 2-5v-1z" class="v"></path><path d="M463 750l1 1c1 1 1 2 2 4l3 10c0-1 0-3 1-4 0-1 1-2 0-3 0-1 0-1-1-2 0-1-1-1 0-2 1 1 2 3 2 5h1c0-1 1-3 1-4h1l2-1v3c-1 0-1 1-2 3h1l1-1h1-1c0 2-1 4-1 6l-1 1c-1 3 1 5 1 8h0-1v-2c0-1-1-2-1-3-1 1-1 0-2 2l1 2 1 2v1s0 1 1 2v1c0 1 1 3 2 5v1l1 3v1l1 1c0 1 0 2 1 3v2c0 1 0 1 1 1v4c-3-7-4-15-7-22l-5-14c-2-5-4-10-5-14z" class="AL"></path><path d="M585 728h-1c1-3 5-5 6-8v-1l1-1h-1c1-2 3-3 3-5v-1l1-1v-2-1l1-1c1 3 0 5 0 8l3 3h0v17c0 6 2 12 3 19-1-2-2-5-2-7-1-3-1-6-1-9l-2-16-1-2c-1 2-1 3-3 4-1 1-4 4-5 4h-2z" class="E"></path><path d="M585 728c2-3 5-4 7-6 1-1 1-2 3-3v1c-1 2-1 3-3 4-1 1-4 4-5 4h-2z" class="S"></path><path d="M400 601c1 2 1 4 2 6 0 1 1 2 1 4 3 7 4 16 2 24-1-2-1-3-1-5 0-1 0 0-1-1v-1c-2-1-3-2-3-5-1-1-1-2-1-4l1-4v-2l-1-1c1-1 1-2 2-3l-2-1v2h-1l-2-1v-2c1 0 1 0 2 1l1-3 1-4z" class="r"></path><path d="M400 615l1 5c0 2 1 6 2 8-2-1-3-2-3-5-1-1-1-2-1-4l1-4z" class="v"></path><path d="M400 601c1 2 1 4 2 6 0 1 1 2 1 4l-2 1c0 2 1 6 0 8l-1-5v-2l-1-1c1-1 1-2 2-3l-2-1v2h-1l-2-1v-2c1 0 1 0 2 1l1-3 1-4z" class="y"></path><path d="M327 408c0-2 1-4 1-6h1c1 0 1 0 2 2l-2 4 1 1-1 1v2h0l-1 2c0 1 1 2 1 3-1 1 0 2 0 2 0 1-1 0-1 2h1l1 2-3 3c-1 1-2 2-3 4 0-1-1-2-1-3s0-2-1-2v-5c-1-1-2-1-3-1v-2c0-1 0-2-1-2l3-2c1-1 2-1 3-1h0c1-1 2-1 2-1l1-1v-2z" class="S"></path><path d="M325 419h2v1 2h-1c0-1-1-2-1-3z" class="y"></path><path d="M324 412c1 0 2 0 2 1s1 1 1 2l-3 2c-1 0-2 1-2 2v1c-1-1-2-1-3-1v-2c0-1 0-2-1-2l3-2c1-1 2-1 3-1h0z" class="E"></path><path d="M321 413c1-1 2-1 3-1 0 2-2 3-4 5h-1c0-1 0-2-1-2l3-2z" class="F"></path><path d="M675 639l6-12h2 0c-7 18-19 33-36 42 1-2 4-3 5-5h0c-1 0-2 1-4 1l3-2v-2-1h2c2 0 5-3 7-4 5-4 11-11 15-17z" class="p"></path><path d="M321 460c1-3 4-7 6-9 1 1 1 2 2 2 2 0 9-2 10 0l-6 2c-2 1-3 2-4 3-2 3-3 7-2 10 0 3 2 6 5 8h2v1c3 0 4-1 7-2 1 0 1 0 1 1s-1 2-2 2-2 0-4 1v-1c-4 0-6-2-9-5v2c-2-1-3-3-4-5h0v-3c-1-2-1-4-1-6h0l-1-1z" class="L"></path><path d="M321 460c1-3 4-7 6-9 1 1 1 2 2 2 2 0 9-2 10 0l-6 2c-2-1-3-1-4-1-2 1-3 2-4 5v1h0c-1 3-1 9 1 11 0 1 1 2 1 2v2c-2-1-3-3-4-5h0v-3c-1-2-1-4-1-6h0l-1-1z" class="Y"></path><defs><linearGradient id="BU" x1="415.297" y1="639.189" x2="403.131" y2="643.609" xlink:href="#B"><stop offset="0" stop-color="#202021"></stop><stop offset="1" stop-color="#3d3c3e"></stop></linearGradient></defs><path fill="url(#BU)" d="M409 632c2 1 2 3 3 4h1c1 1 3 3 3 4s1 1 1 2c1 1 1 1 1 2-1 0-2 1-3 2s-5 3-6 5c0 1 0 1-1 2h-2c-2 0-3 0-5-2 1-3 3-5 4-8 1-2 2-6 3-8h0 2l-1-3z"></path><defs><linearGradient id="BV" x1="336.509" y1="460.306" x2="329.432" y2="469.145" xlink:href="#B"><stop offset="0" stop-color="#494949"></stop><stop offset="1" stop-color="#6b696a"></stop></linearGradient></defs><path fill="url(#BV)" d="M338 457h0c0 1-1 1-2 1v1h-1l2 2h0c1 0 1 0 2 1-1 1-2 1-2 2v1 1c-1 1-2 2-1 3l4 2v3l1 1c-3 1-4 2-7 2v-1h-2c-3-2-5-5-5-8-1-3 0-7 2-10v1 1l-1 2h0l2-2c1-2 3-2 5-2l3-1z"></path><path d="M337 465v1c-1 1-2 2-1 3l4 2v3h-1l-1 1v-1c-2-1-3-1-4-3v-1-1h1l-1-1-1-1 1-1 3-1z" class="p"></path><path d="M329 458v1 1l-1 2h0l2-2c1-2 3-2 5-2-3 1-5 3-6 5-1 4 0 7 2 10l3 3h0-2c-3-2-5-5-5-8-1-3 0-7 2-10z" class="y"></path><path d="M371 545c0-1 0-3 2-3h0c4 5 13 11 13 16h1c0 1 0 1 1 2l-4 4c-2 0-2 0-3 2v-1l-1-1v-1l-3-3-3-6v-1-1h-3v-2l-1-2c-1 0 0-3 1-3z" class="e"></path><path d="M371 545c0 1 1 2 1 3l1 1c2 1 3 4 4 5 0 1 0 2 1 2 1 2 1 2 1 4 0 1 1 2 1 2v1l-3-3-3-6v-1-1h-3v-2l-1-2c-1 0 0-3 1-3z" class="E"></path><defs><linearGradient id="BW" x1="432.637" y1="751.493" x2="437.863" y2="762.507" xlink:href="#B"><stop offset="0" stop-color="#716c6c"></stop><stop offset="1" stop-color="#9a9999"></stop></linearGradient></defs><path fill="url(#BW)" d="M439 734l1-1c0 2 0 3 1 5l3 6v2c-1 1-2 4-2 5l-3 6c-1 3-4 9-7 12-2-1-3-1-4 0l2-4c1-1 1-1 1-2h-1l2-4-1-1c1-2 2-3 3-6l2-4 1-5h0c0-3 1-6 2-9z"></path><path d="M437 743h0l3-7v2l-1 1 1 1c0 2-1 4-1 6h1c0 2-2 5-4 7v-1h0l-1 2c-1-2 2-5 1-6l1-5z" class="G"></path><path d="M439 734l1-1c0 2 0 3 1 5l3 6v2c-1-1-2-1-3-1l-1 1h-1c0-2 1-4 1-6l-1-1 1-1v-2l-3 7h0 0c0-3 1-6 2-9z" class="v"></path><path d="M393 661c1-1 2-2 3-2 2 0 3 1 5 2l8 4c1 1 3 2 5 2 1 1 1 2 2 2 0 1 1 1 2 2 0 1 0 1-1 2 2 0 3 1 5 2h0l-1 3h-1-2l-3-1-2-1c-4-1-8-3-12-5h2c-1-2-2-2-3-3l2-1c-2-1-3-2-5-3-1-1-3-2-4-3z" class="x"></path><path d="M409 665c1 1 3 2 5 2 1 1 1 2 2 2 0 1 1 1 2 2 0 1 0 1-1 2-3-1-5-4-8-6v-2z" class="G"></path><defs><linearGradient id="BX" x1="407.566" y1="674.719" x2="406.078" y2="666.572" xlink:href="#B"><stop offset="0" stop-color="#a9a8a5"></stop><stop offset="1" stop-color="#d2c8c7"></stop></linearGradient></defs><path fill="url(#BX)" d="M402 667c4 1 8 3 11 6 2 0 3 1 4 2l-1 1h0c-1-1-2 0-3 0-4-1-8-3-12-5h2c-1-2-2-2-3-3l2-1z"></path><defs><linearGradient id="BY" x1="394.453" y1="663.161" x2="374.51" y2="657.471" xlink:href="#B"><stop offset="0" stop-color="#999697"></stop><stop offset="1" stop-color="#bab4b2"></stop></linearGradient></defs><path fill="url(#BY)" d="M374 651c4 3 8 4 11 7 1 1 2 2 4 3l1 1h2l1-1c1 1 3 2 4 3 2 1 3 2 5 3l-2 1c1 1 2 1 3 3h-2l-2-1-5 1c-5-1-10-6-14-9-3-3-8-5-9-10l4 3-1-3v-1z"></path><path d="M395 669v-1h0l-1-2c-1 0-2-1-2-2l1-1c1 0 3 1 4 1 2 1 3 2 5 3l-2 1c1 1 2 1 3 3h-2l-2-1-4-1z" class="o"></path><path d="M371 652l4 3c6 5 12 11 20 14l4 1-5 1c-5-1-10-6-14-9-3-3-8-5-9-10z" class="P"></path><defs><linearGradient id="BZ" x1="602.415" y1="692.039" x2="610.332" y2="699.831" xlink:href="#B"><stop offset="0" stop-color="#19191b"></stop><stop offset="1" stop-color="#333334"></stop></linearGradient></defs><path fill="url(#BZ)" d="M598 702v-4c0-1 1-2 1-2 0-1 0-1 1-2h2c1-1 2-1 3-1l17-5 4-1-2 2h0c-2 1-3 2-5 3s-4 2-6 4c-1 1-2 2-4 2-5 4-8 9-8 16l1 1-1 1c0-1-1-2-2-3 0-1-1-1-1-1-1-1-1-3-2-4v-2l-1-1c0-1 0-1 1-2l1 1 1-2z"></path><path d="M598 702c0 2 1 5 1 8h-1c0-1-1-2-1-2h-1v-2l-1-1c0-1 0-1 1-2l1 1 1-2z" class="S"></path><path d="M427 721c1 1 1 1 2 1 0 1 0 1 1 2l3-3v1c-1 1-1 2-2 3l-3 3h1 1 1l1-1c1 2-1 0 0 1v1h0c-1 1 0 1-1 1 1 1 1 1 2 1l1-1 1 1v1h1v3c-1 1-2 2-4 2v1c-1 1-4 2-6 3h0c-1 0-3-1-4-2l-2-1c-2-1-3-1-5-1-1 0-2 0-2-1 1-2 3-3 4-4 5-3 8-6 10-11z" class="f"></path><path d="M425 737l-1 1v-1c0-1 0-2 1-3 1 0 2 0 3 1h1c1-1 1-1 2-1l1 1c-1 0-2 0-3 1l-1 1h2c1 0 1-1 2 0v1c-1 1-4 2-6 3h0l-1-4z" class="P"></path><path d="M425 737c1 0 2 1 3 2l4-1c-1 1-4 2-6 3h0l-1-4z" class="v"></path><path d="M427 721c1 1 1 1 2 1 0 1 0 1 1 2-3 4-7 7-12 10-1 1-3 2-4 2h-1c1-2 3-3 4-4 5-3 8-6 10-11z" class="d"></path><defs><linearGradient id="Ba" x1="431.277" y1="709.137" x2="426.775" y2="715.4" xlink:href="#B"><stop offset="0" stop-color="#464646"></stop><stop offset="1" stop-color="#5e5c5e"></stop></linearGradient></defs><path fill="url(#Ba)" d="M411 693c1 1 2 1 4 1s4 1 6 2c4 1 6 2 9 6v2l3 3c0 3 1 4 0 6v3c1-2 2-3 3-5l3-5v1c-1 5-3 9-6 14l-3 3c-1-1-1-1-1-2-1 0-1 0-2-1 2-4 1-8-1-12-1-2-2-4-3-5-3-3-6-4-10-4h0l2-1v-2c-1-1-2-1-2-1h-1l-1-2v-1z"></path><path d="M413 696s1-1 2 0c3 1 9 2 11 5v1l-2-1c-2-1-3-2-5-3-1 1-2 1-3 1h-1v-2c-1-1-2-1-2-1z" class="v"></path><path d="M421 696c4 1 6 2 9 6v2l3 3c0 3 1 4 0 6v3c0 1-1 1-2 2v-2c1-1 1-2 1-3-1-8-5-13-11-17z" class="O"></path><path d="M415 699h1c1 0 2 0 3-1 2 1 3 2 5 3l2 1v1c1 2 2 2 2 4 0 1-1 1-2 2-1-2-2-4-3-5-3-3-6-4-10-4h0l2-1z" class="F"></path><defs><linearGradient id="Bb" x1="618.934" y1="603.866" x2="630.675" y2="610.975" xlink:href="#B"><stop offset="0" stop-color="#151516"></stop><stop offset="1" stop-color="#373638"></stop></linearGradient></defs><path fill="url(#Bb)" d="M622 623l1-1c-1-1-1-1-1-2h-1l-1-1c-1-1-1-1-1-2v-1c0-1-1-1-1-2l1-1v-2c0-1 0-3 1-4h0 0v-1-1c0-1 1-2 1-3 0 0 1-1 1-2h0c0-1 1-1 1-2h1c0-1 0-2-1-3h0 1c2-1 0-1 1-1h3c2-1 2-1 3-1l1 1-1 1c1 4-1 9-2 13v1c-1 2-1 4-2 7l-3 9h-1c0-1-1-1-1-2z"></path><defs><linearGradient id="Bc" x1="324.835" y1="420.515" x2="316.361" y2="440.368" xlink:href="#B"><stop offset="0" stop-color="#302d2d"></stop><stop offset="1" stop-color="#4e4d4e"></stop></linearGradient></defs><path fill="url(#Bc)" d="M327 426h2c1 0 2-1 3-2h1l1 2c-1 3-4 7-7 11 1 1 2 1 2 2l1 1c-3 2-7 3-10 4-1-1-2-1-2-2-3 1-5 0-8-1-1-1-2-3-3-4-1-2-1-4 0-6l2-2h0v1h0c2 0 2-2 3-3 0 2 0 4 1 5 2 2 3 2 6 2l1 1h0v-2l4-3c1-2 2-3 3-4z"></path><path d="M307 431c2 4 4 8 9 10h0 1c0 1 1 1 2 1h-1c-3 1-5 0-8-1-1-1-2-3-3-4-1-2-1-4 0-6z" class="e"></path><path d="M316 441c2-1 3 0 5-1l6-3h0c1 1 2 1 2 2l1 1c-3 2-7 3-10 4-1-1-2-1-2-2h1c-1 0-2 0-2-1h-1z" class="B"></path><path d="M563 773l-15 48c-3 7-4 13-7 19v-3h0c-2 2-3 3-4 5-1 3-2 6-4 9h0l1-7c1-4 1-7 1-11 1 1 2 0 3 0l1-1 6-20 12-37h1c0 2-1 5-2 7l-5 16v5c1 1 1 1 1 2l-1 3h1l5-19c1-3 2-6 3-8 1-3 1-5 3-7v-1z" class="AG"></path><path d="M538 833l1-1c-1 4-3 8-5 12 1-4 1-7 1-11 1 1 2 0 3 0z" class="AI"></path><path d="M361 418l1 2v1l1 1c0 1 0 1 1 3v1 2l1 1v-2c0-1 0-3-1-3 0-2 1-3 0-3 0-2 0-2-1-2v-1-1-1-1c0-1 0-1-1-2v-3l-1-1s0-1-1-1v-3h2c2 1 3 0 4 1l2 1 3 3c1 0 2 2 3 3l1 1c1 0 1 2 2 3s2 3 2 5h2 1 0l1 1h0c1 0 1 0 2 1 1 0 1-1 2 1h0c1 0 2 1 3 1l-1 1c-5-3-8-3-13-3-5 2-8 4-11 9l-1-2c0-4-2-8-3-13z" class="AL"></path><path d="M368 414l1 1c1 2 3 3 4 5l-1 2c-1 1-1 1-2 0 0-2-2-5-2-8z" class="U"></path><path d="M360 441l-6-18c-3-7-5-15-6-22l6 1c1 2 2 4 2 6l5 10h0c1 5 3 9 3 13l1 2h0v6 3 2l1 1h-1l-1-1h-2c0-1-1-2-2-3h0z" class="j"></path><path d="M356 408l5 10h0c1 5 3 9 3 13v4h-1c-1-1-1-4-2-6h1c-3-4-5-10-5-14 0-3-1-5-1-7z" class="AC"></path><path d="M639 559c1 1 3 1 4 1h1l-1-3c1-1 2-1 3-2v-1l-1-2c2-1 2-2 3-4l-1-1c-1-1-1 0-1-1l1 1 1-1c-2-2-4-5-6-7 2 0 2 1 4 2l3 3h0v-4l1-1h0c1-2 1-4 2-6s2-3 3-5c2 2 5 6 5 9 1 1 1 2 2 3 2 2 4 6 4 9 0 1 0 1 1 2v1c0 1 0 1 1 2v3h-1c-1-4-2-12-5-15h-2l-5 9c-3 1-3 0-4 2-1 1 0 2 0 3-1 1-1 2-2 3v2h-1c1 1 1 1 1 2v1h-1-1l1-2-2-2-9 12c-1-1-2-3-3-4 1 0 2-1 2-1 1 0 2-1 2-2h1c0-1 1-2 1-4l-1-1h-1l1-1z" class="O"></path><path d="M657 540h0c0 2-2 4-3 6-2 2-5 6-6 10-1 1-1 1-2 3l-1-2c2-1 3-3 4-5v-1c-1-1 0-1 0-3v-2h0 3l1-1c1-2 3-3 4-5z" class="F"></path><path d="M476 759v-2c1 0 1-1 2-1v1 2c1-1 1-1 2-1 1-1 2-1 3-1 1-1 3-1 4-1l3 1c1 0 2 1 3 2 1 2 1 3 1 5-1 3-3 5-4 7l-2 2c-1 1-2 3-3 5l-1 3-1 2c-1 1-1 0-2 0-2-3-4-5-6-9 0-3-2-5-1-8l1-1c0-2 1-4 1-6h1-1z" class="AE"></path><path d="M487 756l3 1c1 0 2 1 3 2l-2 1c-2-2-3-2-6-3-1 2-3 4-5 5h-1c1-2 2-3 4-5 1-1 3-1 4-1z" class="b"></path><path d="M476 759v-2c1 0 1-1 2-1v1 2c1-1 1-1 2-1 1-1 2-1 3-1-2 2-3 3-4 5-1 6 0 9 3 14h-1c-1-1-1-1-1-2-1-1-1-2-2-3v-1c-1-1-1-2-1-3s0-1-1-2h0-1c0-2 1-4 1-6h1-1z" class="AC"></path><path d="M475 765h1 0c1 1 1 1 1 2s0 2 1 3v1c1 1 1 2 2 3 0 1 0 1 1 2h1l1 1h1v-2c2-2 2-2 4-2-1 1-2 3-3 5l-1 3-1 2c-1 1-1 0-2 0-2-3-4-5-6-9 0-3-2-5-1-8l1-1z" class="S"></path><path d="M475 765h1 0c1 1 1 1 1 2s0 2 1 3v1c1 1 1 2 2 3 0 1 0 1 1 2h1l1 1c0 1-1 1-1 2-4-3-7-8-8-13l1-1z" class="C"></path><path d="M444 744c1 2 2 3 2 4 1 2 2 3 2 4 1 2 3 5 4 7l10 20c1 6 4 12 6 17 1 2 2 5 3 8v2 1l1 1c0 1 0 2-1 3 0-1 0-2-1-3v-2l-1-1c0-1 0-1-1-1 0 1 0 2 1 3v2c-1 0-1-1-1-1v-1l-1-1-1-2h0c0-1 0-1-1-2v-1s-1-1-1-2h0l-1-1c0-3-2-7-4-10l-13-27c-2-3-3-7-4-10 0-1 1-4 2-5v-2z" class="E"></path><defs><linearGradient id="Bd" x1="333.846" y1="591.931" x2="351.752" y2="572.546" xlink:href="#B"><stop offset="0" stop-color="#49484c"></stop><stop offset="1" stop-color="#686763"></stop></linearGradient></defs><path fill="url(#Bd)" d="M342 563h1 1c0 2-1 2-1 4h0v4l1 1c0 1-1 5-1 6 0 2 1 3 1 5v9c1 1 1 8 1 9 2 1 2 1 3 3 1 11 6 21 12 31l1 2c3 3 5 7 8 11l1-1v-1c1 1 1 1 2 1 1 1 2 2 2 3v1 1l1 3-4-3c1 5 6 7 9 10h-1c-2 0-2-1-3-2-2-1-4-3-6-5l-1-1h-1c-1-1-1-2-1-3h1c-1-2-2-3-4-4-1-1 0 0 0-2-1-1-2-1-2-2l-1-1-4-5v-2c-1 0-1-1-2-1v-1c0-1-1-2-1-3l-2-2v-2h-1v-1l-1-1v-1l-1-1v-2-1c-1-1-1-2-2-4v-2h-1c0-2-1-4-1-6v-2-2c-1 0-1 0-1-1v-2h-2v1h0v2c1 1 1 2 1 3v1h0v2l1 1v2 1l1 2c1 2 2 5 2 7 0 1 1 3 1 4 1 1 2 2 2 4 0 1 1 2 2 3 0 1 0 1 1 2l5 9 2 3 2 2 3 3v1c1 2 4 4 5 5 3 3 6 6 9 8 1 1 1 2 2 2l1 1h1v1c2 1 1 0 2 1h0c1 1 2 1 4 2 0 1 1 1 1 1 1 0 2 1 2 1 1 0 1 0 2-1 1 1 3 2 4 3 1 0 1 0 2 1 0 0 1 0 2 1l4 2c1 0 2 1 3 1 0 0 1 0 1 1h1c1 0 2 1 3 1 0 1 1 1 2 1 2 1 8 2 9 3-6 0-13-2-18-5l-2 2c-3-2-6-5-9-7-4-2-9-5-13-8-10-6-19-15-25-25-16-24-21-54-16-82z"></path><path d="M357 633l1 1 2 3h1c3 3 5 7 8 11l1-1v-1c1 1 1 1 2 1 1 1 2 2 2 3v1 1l1 3-4-3c-6-6-10-11-14-19z" class="M"></path><path d="M369 648l1-1v-1c1 1 1 1 2 1 1 1 2 2 2 3v1 1l-5-4z" class="Q"></path><path d="M345 601c2 1 2 1 3 3 1 11 6 21 12 31l1 2h-1l-2-3-1-1h0c-7-9-11-20-12-32z" class="I"></path><path d="M624 625l3-9c1-3 1-5 2-7v4l1-1h0l1-1c0 1 1 1 1 2-1 1 0 2 0 3v3 1 3c-1 4 0 7 2 10 0 3 2 6 1 8h0l4 7c0 2 1 3 2 5v1c-2-1-4-2-5-4 0 3 1 3 3 4l1 1-1 1-3-2h0c-1 0-2 0-3-1-2 0-8 4-11 5l1-2c-1 0 0 0-1 1h-1c-2 0-3 0-3 1h-2c-1 1-2 0-3 0h0v-2c1-2 1-3 3-4h0 1l2-2-1-1v-1-3c0-2 1-1 0-3l1-2v-2-2-1c1-1 0-1 1-2l1-1-1-2 2-2 1 1v1l1-5z" class="e"></path><path d="M616 652h0 1l2-2 6 5-10 2-2 1v-2c1-2 1-3 3-4z" class="f"></path><path d="M613 658v-2c1-2 1-3 3-4l-1 2c1 1 1 1 1 2 0 0 0 1-1 1l-2 1z" class="v"></path><defs><linearGradient id="Be" x1="622.773" y1="620.591" x2="645.776" y2="641.406" xlink:href="#B"><stop offset="0" stop-color="#6c6b6b"></stop><stop offset="1" stop-color="#aca09c"></stop></linearGradient></defs><path fill="url(#Be)" d="M630 612l1-1c0 1 1 1 1 2-1 1 0 2 0 3v3 1 3c-1 4 0 7 2 10 0 3 2 6 1 8h0l4 7c0 2 1 3 2 5v1c-2-1-4-2-5-4 0-1-3-5-3-6-3-5-6-10-6-15 0-6 2-12 3-17z"></path><path d="M635 641c-2-3-3-6-5-9v-7h0c0-2 1-4 1-5h1v3c-1 4 0 7 2 10 0 3 2 6 1 8h0z" class="V"></path><defs><linearGradient id="Bf" x1="627.898" y1="638.903" x2="618.67" y2="644.691" xlink:href="#B"><stop offset="0" stop-color="#3e3d3e"></stop><stop offset="1" stop-color="#5f5e5f"></stop></linearGradient></defs><path fill="url(#Bf)" d="M619 640v-2-2-1c1-1 0-1 1-2l1-1-1-2 2-2 1 1v1c1 6 4 13 7 18 1 1 3 2 2 4-1 0-2 1-2 1h-2c-2 1-2 0-4-1-1 0-2-1-2-1l-4-3v-3c0-2 1-1 0-3l1-2z"></path><path d="M624 652c1-1 2-1 3-2l3 3h-2c-2 1-2 0-4-1z" class="p"></path><path d="M619 640l2 1h0v1h0c-2 3 0 6 1 9l-4-3v-3c0-2 1-1 0-3l1-2z" class="P"></path><path d="M655 551l5-9h2c3 3 4 11 5 15 0 1-1 2-1 3h2c4 4 4 21 5 26l-1 1-6 2c1-2 2-3 2-6-3-6-9-9-14-13l-2-2c-2 0-2 0-3 1-2 2-3 5-4 7s-3 3-4 5h0l-2 1-1-1 1-1c-1-1-1-2-2-4 2-4 7-8 10-12h1 1v-1c0-1 0-1-1-2h1v-2c1-1 1-2 2-3 0-1-1-2 0-3 1-2 1-1 4-2z" class="D"></path><path d="M647 564h1 1v-1c0-1 0-1-1-2h1v-2c1-1 1-2 2-3 0-1-1-2 0-3 1-2 1-1 4-2-1 2-2 3-2 5 0 1-1 2-2 3h-1c0 1 0 1 1 2-1 0-1 0-1 1 2 1 4 1 7 1h1 0l8-3h0 2v1l-7 2-7 2-1 1h1v2h0c2 2 4 4 6 5 3 3 8 7 8 10h0c-3-6-9-9-14-13l-2-2c-2 0-2 0-3 1-2 2-3 5-4 7s-3 3-4 5h0l-2 1-1-1 1-1c-1-1-1-2-2-4 2-4 7-8 10-12z" class="F"></path><defs><linearGradient id="Bg" x1="654.75" y1="551.914" x2="665.539" y2="558.635" xlink:href="#B"><stop offset="0" stop-color="#555"></stop><stop offset="1" stop-color="#7f7776"></stop></linearGradient></defs><path fill="url(#Bg)" d="M655 551l5-9h2c3 3 4 11 5 15 0 1-1 2-1 3h0c-3 1-5 2-8 3h0-1c-3 0-5 0-7-1 0-1 0-1 1-1-1-1-1-1-1-2h1c1-1 2-2 2-3 0-2 1-3 2-5z"></path><path d="M369 576c1-1 1-2 3-3 3-1 5 0 7 2l3 2 2 1 2 1v1l3 2c6 6 9 11 11 19l-1 4-1 3c-1-1-1-1-2-1v2l2 1h1v-2l2 1c-1 1-1 2-2 3l1 1v2l-1 4c0 2 0 3 1 4l-1 1v-1c-1-2-1-2-2-3h-1c-1-1-2-3-3-3v-4h0l-1-3c-1-5-5-9-9-13l-1-1-8-5-11-3h0v-1h3l1-3h0l1-6c0-1 1-1 1-2z" class="v"></path><path d="M384 578l2 1v1c0 1 0 2 1 2v2c1 2 2 3 2 4v1l-1-1h-1l-1-1c-1-1-3-4-3-5 0-2 1-2 0-3l1-1z" class="P"></path><path d="M389 582c6 6 9 11 11 19l-1 4v-1c0-1-1-1-1-2 0-3-2-6-3-8-1-3-3-6-5-8-1-1-1-2-1-4z" class="F"></path><defs><linearGradient id="Bh" x1="389.608" y1="614.545" x2="390.924" y2="588.828" xlink:href="#B"><stop offset="0" stop-color="#4e4e4e"></stop><stop offset="1" stop-color="#676363"></stop></linearGradient></defs><path fill="url(#Bh)" d="M386 587l1 1c1 3 2 10 5 12 1 1 1 2 1 3 0 2 2 4 3 5v1l2 1h1v-2l2 1c-1 1-1 2-2 3l1 1v2l-1 4c0 2 0 3 1 4l-1 1v-1c-1-2-1-2-2-3h-1c-1-1-2-3-3-3v-4h0l-1-3c-1-5-5-9-9-13 1-1 2-2 2-3l-1-1c-1-1-2-3-2-4 1 0 3-1 4-2h0z"></path><path d="M393 613l1-1h0c1 1 1 2 2 2l3 3v2c0 2 0 3 1 4l-1 1v-1c-1-2-1-2-2-3h-1c-1-1-2-3-3-3v-4z" class="G"></path><path d="M369 576c1-1 1-2 3-3 3-1 5 0 7 2l3 2 2 1-1 1c1 1 0 1 0 3 0 1 2 4 3 5h0c-1 1-3 2-4 2 0 1 1 3 2 4l1 1c0 1-1 2-2 3l-1-1-8-5-11-3h0v-1h3l1-3h0l1-6c0-1 1-1 1-2z" class="Y"></path><path d="M373 583v-1l2-2v2h1v-1c2 0 2 0 3 1 0 2 2 2 1 3l-1 1c-3 0-4-1-6-3h0z" class="r"></path><path d="M373 577c1 0 2 1 3 1l-1 2-2 2v1h0c0 2 2 4 3 5h1 0c2 2 4 3 5 5v3l-8-5v-1c-1 0-2-1-3-2 0-1-1-1-1-2 0-2 0-4 2-7l1-2z" class="H"></path><path d="M373 577c1 0 2 1 3 1l-1 2-2 2v1c-1-2-1-2-1-4l1-2z" class="V"></path><path d="M369 576c1-1 1-2 3-3 3-1 5 0 7 2l3 2-2 1c-2 0-2-1-3 0h-1c-1 0-2-1-3-1l-1 2c-2 3-2 5-2 7 0 1 1 1 1 2 1 1 2 2 3 2v1l-11-3h0v-1h3l1-3h0l1-6c0-1 1-1 1-2z" class="M"></path><path d="M368 578c1 2 1 5 1 8l-1 1h-1v-3h0l1-6z" class="d"></path><path d="M370 586c-1-2 0-4 0-7 0-2 0-3 1-4l4-1-2 3-1 2c-2 3-2 5-2 7z" class="u"></path><path d="M375 574c1 0 2 0 4 1l3 2-2 1c-2 0-2-1-3 0h-1c-1 0-2-1-3-1l2-3z" class="H"></path><path d="M636 650c1 2 3 3 5 4s4 3 6 3l1 2c-1 1-1 1-2 1l1 1c1-1 2-1 3-1v-3l3 1 5-3 2 1c-2 1-5 4-7 4h-2v1 2l-3 2c2 0 3-1 4-1h0c-1 2-4 3-5 5-3 3-7 4-11 6h-1c1-1 2-2 3-2v-1c-3 1-5 1-7 2h-1l-5 1h-3l-7 1-3 2h-5l-1 1h-4v-4c0-1 1-2 2-3l1-2c3 0 4-1 6-3l1 1h1c1 0 1 0 2-1h2l1-1 1-1h-1v1h-2-2s-1 0-1 1h-3l-1-1h-1c0-1 0 0 1-1h0l-1-1c0-1 1-2 1-2 2-1 7-2 9-3l4-1c3-1 9-5 11-5 1 1 2 1 3 1h0l3 2 1-1-1-1c-2-1-3-1-3-4z" class="n"></path><path d="M624 670l-1-1-1 1-1-1c1 0 2-1 3-1 5-1 10-5 15-6-2 2-3 3-5 4-3 1-6 4-10 4z" class="X"></path><path d="M615 676l-1-1c-2 1-2 1-4 1v-1c2-2 7-3 9-3l1 1c2 0 4 1 6-1h1 2l-7 3-7 1z" class="L"></path><path d="M604 672h2 1c1-1 1-1 2-1 1-1 2-1 4-2l-1 1c-1 1-2 2-4 2v2l-2 1c1 1 1 2 0 3h1l-1 1h-4v-4c0-1 1-2 2-3z" class="B"></path><path d="M644 659l1 1v2s1 0 2-1h2v1c-5 4-14 8-20 10h-2-1c-2 2-4 1-6 1l-1-1c2-1 4-2 5-2 4 0 7-3 10-4 2-1 3-2 5-4 2-1 3-2 5-3z" class="AA"></path><path d="M636 650c1 2 3 3 5 4s4 3 6 3l1 2c-1 1-1 1-2 1h-1l-1-1c-1 0-2-1-3 0-3 0-6 2-8 3l-14 6c-2 1-3 0-5 1h-1c-2 1-3 1-4 2-1 0-1 0-2 1h-1-2l1-2c3 0 4-1 6-3l1 1h1c1 0 1 0 2-1h2l1-1 1-1h-1v1h-2-2s-1 0-1 1h-3l-1-1c3-1 7-1 10-2 5-2 10-3 15-6 1 0 3-1 4-2-1 0-1-1-2-1v-1h0l3 2 1-1-1-1c-2-1-3-1-3-4z" class="E"></path><path d="M622 658c3-1 9-5 11-5 1 1 2 1 3 1v1c1 0 1 1 2 1-1 1-3 2-4 2-5 3-10 4-15 6-3 1-7 1-10 2h-1c0-1 0 0 1-1h0l-1-1c0-1 1-2 1-2 2-1 7-2 9-3l4-1z" class="H"></path><path d="M609 666h-1c0-1 0 0 1-1h0l-1-1c0-1 1-2 1-2 2-1 7-2 9-3h1c1 1 2 1 3 1h0c-2 1-4 1-6 2h-1v1c2 0 3 0 4 1-3 1-7 1-10 2zm41-9l3 1 5-3 2 1c-2 1-5 4-7 4h-2v1 2l-3 2c2 0 3-1 4-1h0c-1 2-4 3-5 5-3 3-7 4-11 6h-1c1-1 2-2 3-2v-1c-3 1-5 1-7 2h-1l-5 1h-3l7-3c6-2 15-6 20-10v-1h-2c-1 1-2 1-2 1v-2h1l1 1c1-1 2-1 3-1v-3z" class="V"></path><path d="M648 665c2 0 3-1 4-1h0c-1 2-4 3-5 5-3 3-7 4-11 6h-1c1-1 2-2 3-2v-1c-3 1-5 1-7 2h-1l1-1c3-1 6-3 9-3 1-1 1-1 2-1 2-2 4-3 6-4z" class="G"></path><defs><linearGradient id="Bi" x1="399.845" y1="646.714" x2="379.861" y2="639.616" xlink:href="#B"><stop offset="0" stop-color="#635c5c"></stop><stop offset="1" stop-color="#9f9692"></stop></linearGradient></defs><path fill="url(#Bi)" d="M393 617c1 0 2 2 3 3h1c1 1 1 1 2 3v1l1-1c0 3 1 4 3 5v1c1 1 1 0 1 1 0 2 0 3 1 5l-4 10v3c-1 1-2 3-2 5h1l-2 4v-1c-1 1-3 1-4 2-1-3-2-5-4-8h0c-2-4-6-6-9-8l-4-1c-1 0-2 0-3 1h0-1v-2c-1-2-2-3-2-5v-1-3-7c2 2 3 3 6 3 1-1 2-1 3-2 1 0 2-1 3-1l2-1h-1-1l3-2 2-1 3-2h0l2-1z"></path><path d="M392 630c1 2 1 2 1 3l-2 2h0l-1-1 1-2 1-2z" class="D"></path><path d="M396 643l1-1 1 1c1 1 2 1 3 2v3h-1v-1c-2-1-3-2-4-4z" class="v"></path><path d="M385 623l2 1c2 2 4 4 5 6l-1 2-3-1-3 3h-1c-2 1-2 1-2 3-1 1-1 2 0 4h-1v1l-4-1c-1 0-2 0-3 1h0-1v-2c-1-2-2-3-2-5v-1-3-7c2 2 3 3 6 3 1-1 2-1 3-2 1 0 2-1 3-1l2-1z" class="L"></path><path d="M383 624l3 3c-3 2-8 2-11 4-2 1-2 3-4 4v-1-3-7c2 2 3 3 6 3 1-1 2-1 3-2 1 0 2-1 3-1z" class="AJ"></path><path d="M373 640v-1c1-2-1 1 0-1 2-4 6-6 9-7 2-1 4-1 6 0l-3 3h-1c-2 1-2 1-2 3-1 1-1 2 0 4h-1v1l-4-1c-1 0-2 0-3 1h0-1v-2z" class="AD"></path><path d="M393 617c1 0 2 2 3 3h1c1 1 1 1 2 3v1l1-1c0 3 1 4 3 5v1c1 1 1 0 1 1 0 2 0 3 1 5l-4 10c-1-1-2-1-3-2l-1-1-1 1c-1-1-4-7-5-8l2-2c0-1 0-1-1-3s-3-4-5-6l-2-1h-1-1l3-2 2-1 3-2h0l2-1z" class="AB"></path><path d="M393 617c1 0 2 2 3 3l3 8c-2 3-3 5-5 7h-1l1-1-1-1c0-1 0-1-1-3s-3-4-5-6l-2-1h-1-1l3-2 2-1 3-2h0l2-1z" class="u"></path><path d="M391 618c1 2 2 3 3 4l-1 1h-2l-3-3 3-2z" class="d"></path><path d="M386 621l2-1 3 3 2 4s-1-1-2-1c0-1-1-2-2-3l-2 1-2-1h-1-1l3-2z" class="F"></path><path d="M387 624l2-1c1 1 2 2 2 3 1 0 2 1 2 1 1 1 2 3 2 5 0 1-1 1-1 2l-1-1c0-1 0-1-1-3s-3-4-5-6z" class="B"></path><defs><linearGradient id="Bj" x1="381.129" y1="580.472" x2="342.969" y2="596.508" xlink:href="#B"><stop offset="0" stop-color="#a29d9c"></stop><stop offset="1" stop-color="#c2bebd"></stop></linearGradient></defs><path fill="url(#Bj)" d="M364 518c1 2-1 4 0 7 0 2-2 4-2 6v5c-1 2-1 5-3 7-6 18-7 36-6 55v7c1 2 1 4 2 6l1-1c1 1 1 2 1 3 1 1 1 2 2 2l1 1c2 4 3 8 4 11 2 4 4 8 6 11v2l2 7c-1 0-1 0-2-1v1l-1 1c-3-4-5-8-8-11l-1-2c-6-10-11-20-12-31-1-2-1-2-3-3 0-1 0-8-1-9v-9c0-2-1-3-1-5 0-1 1-5 1-6l-1-1v-4h0c0-2 1-2 1-4h-1c0-1 1-1 1-1h1 3c1-2 3-10 4-13l4-13 3-7 5-11z"></path><path d="M345 580l3 24c-1-2-1-2-3-3 0-1 0-8-1-9 0-4 0-8 1-12z" class="d"></path><path d="M344 563c1 1 2 1 3 3v1c-1 4-2 9-2 13-1 4-1 8-1 12v-9c0-2-1-3-1-5 0-1 1-5 1-6l-1-1v-4h0c0-2 1-2 1-4z" class="G"></path><path d="M355 611l1-1c1 1 1 2 1 3 1 1 1 2 2 2l1 1c2 4 3 8 4 11 2 4 4 8 6 11v2c-4-4-7-9-9-14-1-2-1-5-3-7-1-1-1-1-1-2 0-2-1-4-2-6z" class="D"></path><path d="M364 518c1 2-1 4 0 7 0 2-2 4-2 6v5c-1 2-1 5-3 7-6 18-7 36-6 55v7c-6-17-4-36 2-53 1 0 1-1 1-2l1-2v-2c0-2 0-8 1-10 1-1 1-1 1-2l1-1c0-2 0-3-1-4l5-11z" class="X"></path><path d="M425 632h5 1l6 15c1 3 3 15 6 16 1 1 1 1 1 3 0 1 1 2 1 4l4 11 11 34c4 13 8 27 13 40 0 1-1 3-1 4h-1c0-2-1-4-2-5-1 1 0 1 0 2 1 1 1 1 1 2 1 1 0 2 0 3-1 1-1 3-1 4l-3-10c-1-2-1-3-2-4l-1-1-39-118h1z" class="C"></path><defs><linearGradient id="Bk" x1="345.614" y1="572.478" x2="380.524" y2="615.814" xlink:href="#B"><stop offset="0" stop-color="#969393"></stop><stop offset="1" stop-color="#c1bdbc"></stop></linearGradient></defs><path fill="url(#Bk)" d="M366 544c1-1 1 0 2-1-1 4-3 11-5 14v1l1 1c1-1 2-1 2-2 1 1 1 2 1 3l-1 1 3 1c4 1 6 3 9 5-1 1-2 2-3 4h-1c-2 1-4 1-5 3v2c0 1-1 1-1 2l-1 6h0l-1 3h-3v1h0l11 3 8 5 1 1c4 4 8 8 9 13l1 3h0v4l-2 1h0l-3 2-2 1-3 2h1 1l-2 1c-1 0-2 1-3 1-1 1-2 1-3 2-3 0-4-1-6-3v7 3l-1 1v1 2c-2-3-4-7-6-11-1-3-2-7-4-11l-1-1c-1 0-1-1-2-2 0-1 0-2-1-3l-1 1c-1-2-1-4-2-6v-7c-1-19 0-37 6-55 0 1 0 2 1 3h-1v1 1 1l-1 1v1l-1 5c-1 2-2 4-1 7h-1c0 2 0 3 1 4v-1s0-1 1-1l-1-2 1-1c0-2 0-2 1-3 0 0-1 0-1-1 1-1 1 0 1-2h1c-1-2-1-1 0-2 0-2 0-2 1-3v-2l1-1c0-1 0-2 1-3l1-3 2 2c0 1-1 3 0 4l1-4z"></path><path d="M363 558l1 1c1-1 2-1 2-2 1 1 1 2 1 3l-1 1c-2 1-2 3-2 5l-1 5-3-1c1-4 1-8 3-12z" class="B"></path><path d="M366 544c1-1 1 0 2-1-1 4-3 11-5 14l-1-1-1 5-1 3-1-1v-1c0-2 1-7 2-8l1-4-1-1 1-4 1-3 2 2c0 1-1 3 0 4l1-4z" class="I"></path><path d="M363 542l2 2c0 1-1 3 0 4-1 2-2 5-3 6h-1l1-4-1-1 1-4 1-3z" class="Y"></path><path d="M355 604c0-3 0-7 1-10l4 17v5l-1-1c-1 0-1-1-2-2 0-1 0-2-1-3l-1 1c-1-2-1-4-2-6v-7l2 6z" class="B"></path><path d="M353 598l2 6c1 4 3 8 4 11-1 0-1-1-2-2 0-1 0-2-1-3l-1 1c-1-2-1-4-2-6v-7z" class="H"></path><path d="M360 616v-5c3 7 6 14 11 20v3l-1 1v1 2c-2-3-4-7-6-11-1-3-2-7-4-11z" class="O"></path><path d="M359 543c0 1 0 2 1 3h-1v1 1 1l-1 1v1l-1 5c-1 2-2 4-1 7h-1c0 2 0 3 1 4v-1s0-1 1-1l-1-2 1-1c0-2 0-2 1-3 0 0-1 0-1-1 1-1 1 0 1-2h1c-1-2-1-1 0-2 0-2 0-2 1-3v-2l1-1c0-1 0-2 1-3l-1 4c-4 15-7 29-5 45-1 3-1 7-1 10l-2-6c-1-19 0-37 6-55z" class="f"></path><path d="M360 570l3 1v3c-1 4-3 12-1 16l2 12c2 8 5 17 12 23h4 0c-1 1-2 1-3 2-3 0-4-1-6-3-8-12-14-30-12-45 0-3 1-6 1-9z" class="P"></path><path d="M360 570l3 1v3c-1 0-1 1-2 2v2l-1-1v3 5l-1-6c0-3 1-6 1-9z" class="f"></path><path d="M366 561l3 1c4 1 6 3 9 5-1 1-2 2-3 4h-1c-2 1-4 1-5 3v2c0 1-1 1-1 2l-1 6h0l-1 3h-3v1h0l1 1c-1 1-2 1-2 1-2-4 0-12 1-16v-3l1-5c0-2 0-4 2-5z" class="p"></path><path d="M369 562c4 1 6 3 9 5-1 1-2 2-3 4h-1v-3l-1-1c-1-3-2-3-4-5z" class="P"></path><path d="M363 588l-1-1c0-1 1-1 1-2v-1h2v-7-3h1v3 3 3l1 1h0l-1 3h-3v1z" class="V"></path><path d="M363 588l11 3 8 5 1 1c4 4 8 8 9 13l1 3h0v4l-2 1h0l-3 2-2 1-3 2h1 1l-2 1c-1 0-2 1-3 1h0-4c-7-6-10-15-12-23l-2-12s1 0 2-1l-1-1z" class="H"></path><path d="M367 601c1 0 1 0 3 1l-1 1c2 0 5-1 6-1l-6 4h-1-1c-1-2-1-3 0-5z" class="M"></path><path d="M364 589l1 1 3 5 1 2v3c0 1 0-1 1 1v1c-2-1-2-1-3-1l-1 1-2-1v1l-2-12s1 0 2-1z" class="I"></path><path d="M364 595h1l3 3-1 1h-1-1c0-1-1-2-1-4z" class="X"></path><path d="M364 602v-1l2 1 1-1c-1 2-1 3 0 5h1 1 1c2-1 4-3 7-2v1c0 1 0 2-1 2h1 1l1 1h0v-2h1c0 1 1 2 0 4h-1c0 3 0 5-1 7h1c1-1 1-2 1-3h1c0 3 0 4-1 6s-1 3 0 5h-4c-7-6-10-15-12-23z" class="u"></path><path d="M370 606c2-1 4-3 7-2v1c0 1 0 2-1 2-1 1-1 1-1 2h1 2v1c-1 0-4 1-4 1-1 1-1 2-2 3-2-1-3-4-4-5l-1-1c1 0 2-1 3-2z" class="M"></path><path d="M363 588l11 3 8 5 1 1c4 4 8 8 9 13l1 3h0v4l-2 1h0l-3 2-2 1-2-3v-1c1 0 2 0 3-1h0l-1-2-1-4c0-2-1-2-1-3-2-2-3-4-4-5h-1v-1l-1-1c-2-1-1 0-2-2h-3l-4-4-1 1-3-5-1-1-1-1z" class="V"></path><path d="M363 588l11 3 8 5 1 1c4 4 8 8 9 13l1 3h0v4l-2 1h0c-1-4-1-6-2-9-5-9-15-16-24-19l-1-1-1-1z" class="e"></path><path d="M483 790l1 1h0v1 1l3 3 1 1v-1l2 9 3 8c2 4 3 8 3 12 1 2 1 5 2 8l2 9c1 1 0 2 1 2 0-1 0-3 1-5v-1l1-4c1 0 3 0 4-1v1l3-3c3-2 6-2 10-1 3 1 5 2 7 5l2 2c2 1 2 1 2 3h0c1-2 1-4 1-6l3-1c0 4 0 7-1 11l-1 7c-1 2-1 5-2 7l-3 13 1 1v1l1 1-4 15c-1 2-2 6-3 8l-6 27h0l-1-3-3-13c-2-11-2-21-4-32-1-5-3-10-5-15-1-4-3-9-4-13 0-1-1-2-1-3h0c-1-1-1-1-1-3v-1c-1-2-1-4-2-6-2-2-4-5-4-7v-1h-1v-3c-1-1-1-2-1-3l-1-2-1-2v-2c-1-2-1-3-1-5l-1-1v-1-4h-1v-4l-1-1v-2-3c-1-1-1-2-1-4z" class="z"></path><path d="M491 822l1 1v-3l1-1c1 1 2 3 2 5l1 1c1 2 1 5 2 8l2 9v3l-3-9c0-3-1-7-2-10l-1 1v2c-1-1-1-1-1-2h-1l-1-5zm23 72h0c-1-3 0-5 0-8h-1c0-5 0-11-2-16-1-1-1-2-1-2 0-2-1-3-1-4h-1v-3h0v-1c3 3 6 6 9 7l3-1-2 2-1 1c-1-1-1-1-1-2l-2-1 2 13c-1 1-1 2-2 3v1c-1 3 0 7 0 11z" class="U"></path><path d="M483 790l1 1h0v1 1l3 3 1 1v-1l2 9 3 8c2 4 3 8 3 12l-1-1c0-2-1-4-2-5l-1 1v3l-1-1c-2-6-3-12-5-18h-1v-4l-1-1v-2-3c-1-1-1-2-1-4z" class="j"></path><path d="M507 834l3-3c3-2 6-2 10-1 0 1 0 2-1 4-2 0-3 0-5 1-4 3-6 7-6 12-1 3 0 8 2 10v1c2 2 4 4 7 5 2 0 4 0 6-1h1c-1 2-2 3-4 4l-3 1c-3-1-6-4-9-7v-1c-2-4-4-7-4-12h-1c-1-2-1-6-1-9l1-4c1 0 3 0 4-1v1z" class="AE"></path><path d="M503 834c1 0 3 0 4-1v1c-2 4-3 8-3 13h-1c-1-2-1-6-1-9l1-4z" class="AI"></path><path d="M516 879l-2-13 2 1c0 1 0 1 1 2l1-1c0 2-1 4-1 5 2 2-1 13 2 14l1-2v2 1c1 1 1 2 1 3h0l1-1v-1-1h1c-1 1-1 2-1 4-1 1-1 4 0 6h0 0l1-1-6 27h0l-1-3-3-13c1-1 1-1 0-2h0 1v-1c-1-2 0-8 0-11 0-4-1-8 0-11v-1c1-1 1-2 2-3z" class="l"></path><path d="M516 879l-2-13 2 1c0 1 0 1 1 2l1-1c0 2-1 4-1 5 0 3 1 6 0 9s-1 7-1 10c-1-4 0-9 0-13z" class="C"></path><path d="M514 894c0-4-1-8 0-11v-1c1-1 1-2 2-3 0 4-1 9 0 13l1 5c-1 1-1 2-2 3v-2l-1 7c-1-2 0-8 0-11z" class="T"></path><path d="M514 905l1-7v2c1-1 1-2 2-3-1 3-1 8 0 11l-1 13-3-13c1-1 1-1 0-2h0 1v-1z" class="N"></path><path d="M520 887v1c1 1 1 2 1 3h0l1-1v-1-1h1c-1 1-1 2-1 4-1 1-1 4 0 6h0 0l1-1-6 27h0l-1-3 1-13c2-7 3-14 3-21z" class="q"></path><path d="M520 830c3 1 5 2 7 5l2 2c2 1 2 1 2 3l-1 4c-1 1-1 1-1 2-1 1-1 1-1 2l-1 1c-2 2-4 4-4 7-1 1-2 1-3 2s-1 1-2 1c-2-1-3-1-4-2l-1 1h1c-1 0-1 0-2-1l-2 1v-1c-2-2-3-7-2-10 0-5 2-9 6-12 2-1 3-1 5-1 1-2 1-3 1-4z" class="z"></path><path d="M520 830c3 1 5 2 7 5l2 2v3h-1c-3-4-5-4-9-6 1-2 1-3 1-4z" class="b"></path><path d="M523 843v-2c-1-2-2-3-3-4v-1c2 0 4 1 5 2s1 3 1 5v2h0c1 1 1 2 1 4-2 2-4 4-4 7-1 1-2 1-3 2 0-1 0-2 1-3v-4l1-1c1-1 1-4 1-6 0-1-1 0 0-1z" class="S"></path><path d="M515 841c0-1 1-2 2-2s2 0 3 1l1 1 2 2c-1 1 0 0 0 1 0 2 0 5-1 6l-1 1h-1v3h-2l-3-3v-3h0c-1-3-1-4 0-7z" class="AE"></path><defs><linearGradient id="Bl" x1="518.595" y1="846.068" x2="507.862" y2="847.292" xlink:href="#B"><stop offset="0" stop-color="#130302"></stop><stop offset="1" stop-color="#250604"></stop></linearGradient></defs><path fill="url(#Bl)" d="M510 858v-1c-2-2-3-7-2-10 0-5 2-9 6-12 1 0 2 0 3 1h-1c-1 0-1 1-2 1v3l-1 1h2c-1 3-1 4 0 7h0v3l3 3h2v-3h1v4c-1 1-1 2-1 3-1 1-1 1-2 1-2-1-3-1-4-2l-1 1h1c-1 0-1 0-2-1l-2 1z"></path><path d="M518 854v1l-2-1-2-4h-1c0-1-1-2-1-3 0-2 0-4 1-6h2c-1 3-1 4 0 7h0v3l3 3z" class="AL"></path><path d="M532 834l3-1c0 4 0 7-1 11l-1 7c-1 2-1 5-2 7l-3 13 1 1v1l1 1-4 15c-1 2-2 6-3 8l-1 1h0 0c-1-2-1-5 0-6 0-2 0-3 1-4h-1v1 1l-1 1h0c0-1 0-2-1-3v-1-2l-1 2c-3-1 0-12-2-14 0-1 1-3 1-5l2-2c2-1 3-2 4-4h-1c-2 1-4 1-6 1-3-1-5-3-7-5l2-1c1 1 1 1 2 1h-1l1-1c1 1 2 1 4 2 1 0 1 0 2-1s2-1 3-2c0-3 2-5 4-7l1-1c0-1 0-1 1-2 0-1 0-1 1-2l1-4h0c1-2 1-4 1-6z" class="s"></path><path d="M531 840h0c0 6-2 14-5 19 0 1-1 1-1 2l-1 1h-1c-2 1-4 1-6 1-3-1-5-3-7-5l2-1c1 1 1 1 2 1h-1l1-1c1 1 2 1 4 2 1 0 1 0 2-1s2-1 3-2c0-3 2-5 4-7l1-1c0-1 0-1 1-2 0-1 0-1 1-2l1-4z" class="C"></path><path d="M531 840h0c0 6-2 14-5 19 0 1-1 1-1 2l-1 1h-1c0-1 1-1 1-2 1-1 2-2 2-4l1-2c1-2 1-3 1-5h-1v4c-1 1-2 2-4 2v1c0-3 2-5 4-7l1-1c0-1 0-1 1-2 0-1 0-1 1-2l1-4z" class="j"></path><defs><linearGradient id="Bm" x1="651.243" y1="596.733" x2="680.572" y2="598.11" xlink:href="#B"><stop offset="0" stop-color="#333131"></stop><stop offset="1" stop-color="#5e5c5b"></stop></linearGradient></defs><path fill="url(#Bm)" d="M666 518c2 2 4 7 6 8h0v-3c6 11 9 22 13 33h0c1 2 1 5 2 7 4 21 2 40-6 59-1 4-4 7-5 11 0 2-2 3-1 6-4 6-10 13-15 17l-2-1-5 3-3-1v3c-1 0-2 0-3 1l-1-1c1 0 1 0 2-1l-1-2h0c-1-4 0-10 2-13v-1c1-3 4-7 3-11 1 0 1 0 2-1h1c0 1 1 2 2 2 2-1 6-8 7-9 6-11 10-21 11-33v-11c-1-11-2-23-7-33l-1-3c-1-1-2-3-2-4-2-3-3-6-5-9h1l2 2c0-1-1-3-1-4h0c-1-1-2-1-2-3 2 0 3 0 4 1v-2c1 1 2 1 2 2v1c2 3 3 6 4 9 0 1 1 2 1 4 1 1 1 3 2 5h1c-1 1 0 2 1 4h0v-2-1-1c-1 0-1-1-1-2v-1l-1-1v-1-1l-1-1v-1h0v-1l-1-1v-1c0-1-1-1-1-1v-2s-1 0-1-1v-1l-1-2-1-2v-3c0-1-1-2-2-3l1-2z"></path><path d="M662 529h0c-1-1-2-1-2-3 2 0 3 0 4 1 2 2 3 5 4 7-1-1-2-1-3-2s-1-3-3-3z" class="G"></path><path d="M665 540h1c1 1 0 0 1 0 0-1 1-1 2-2h0l3 6-2 1h-1l-2-1c-1-1-2-3-2-4z" class="u"></path><path d="M662 529c2 0 2 2 3 3s2 1 3 2l1 4h0c-1 1-2 1-2 2-1 0 0 1-1 0h-1c-2-3-3-6-5-9h1l2 2c0-1-1-3-1-4z" class="H"></path><path d="M649 644v2l1-1h1c2 0 4-2 6-3l-3 3c0 1-1 2-1 4l-4 4v2l1 2v3c-1 0-2 0-3 1l-1-1c1 0 1 0 2-1l-1-2h0c-1-4 0-10 2-13z" class="O"></path><path d="M682 595h0v4l-1 3v3l-5 15v1c-1 1-1 1-1 2l1 1-1 2c-2 4-4 9-6 12-2 2-3 4-5 6h0c0-1 3-4 3-6 1-1 1-1 1-2-2 2-3 4-6 7l-7 7c-1 1-2 3-4 4-1 0-1 0-2 1v-2l4-4c11-10 20-22 24-36 2-6 3-12 5-18z" class="L"></path><defs><linearGradient id="Bn" x1="647.553" y1="553.857" x2="679.473" y2="627.549" xlink:href="#B"><stop offset="0" stop-color="#9e9794"></stop><stop offset="1" stop-color="#cac8c8"></stop></linearGradient></defs><path fill="url(#Bn)" d="M672 544c8 22 10 51 0 73-3 6-11 21-17 23h-1v-3c1-2 2-3 3-4 2-1 6-8 7-9 6-11 10-21 11-33v-11c-1-11-2-23-7-33l-1-3 2 1h1l2-1z"></path><path d="M666 518c2 2 4 7 6 8h0v-3c6 11 9 22 13 33h0c1 2 1 5 2 7 4 21 2 40-6 59-1 4-4 7-5 11 0 2-2 3-1 6-4 6-10 13-15 17l-2-1-5 3-3-1-1-2c1-1 1-1 2-1 2-1 3-3 4-4l7-7c3-3 4-5 6-7 0 1 0 1-1 2 0 2-3 5-3 6h0c2-2 3-4 5-6 2-3 4-8 6-12l1-2-1-1c0-1 0-1 1-2v-1l5-15v-3l1-3v-4h0c-1-2 0-5 0-7 0-6 0-13-1-20-1-8-3-15-6-23-2-7-4-15-8-22 0-1-1-2-2-3l1-2z" class="o"></path><path d="M676 633c0 2-2 3-1 6-4 6-10 13-15 17l-2-1c7-7 13-14 18-22z" class="D"></path><path d="M666 518c2 2 4 7 6 8 2 3 3 5 4 9 1 2 2 5 3 8 2 5 3 12 5 18 2 5 2 14 2 20h-1c0-4 0-8-1-12 0-3 0-7-1-8-1 0-1 0-1 1 1 3 1 7 2 10 0 9 1 18-1 27l-3 15c-1 0-1 1-1 2v1c-1 2-2 5-3 7l-1-1c0-1 0-1 1-2v-1l5-15v-3l1-3v-4h0c-1-2 0-5 0-7 0-6 0-13-1-20-1-8-3-15-6-23-2-7-4-15-8-22 0-1-1-2-2-3l1-2z" class="X"></path><path d="M641 581h0c1-2 3-3 4-5s2-5 4-7c1-1 1-1 3-1l2 2c5 4 11 7 14 13 0 3-1 4-2 6l6-2c-1 1 0 1-1 1l1 1v1 1h3c-1 12-5 22-11 33-1 1-5 8-7 9-1 0-2-1-2-2h-1c-1 1-1 1-2 1 1 4-2 8-3 11v1c-2 3-3 9-2 13h0c-2 0-4-2-6-3v-1c-1-2-2-3-2-5l-4-7h0c1-2-1-5-1-8-2-3-3-6-2-10v-3-1-3c0-1-1-2 0-3 0-1-1-1-1-2l4-21c0-1 1-2 1-3v-1l2 1 3-6z" class="D"></path><path d="M641 607v-3-2c1-1 1-4 2-4 1-1 5-3 6-4 2-1 3-2 5-2-3 2-7 4-9 6 0 1 1 1 0 2v1c-2 2-2 6-3 7-1 0-1-1-1-1z" class="G"></path><path d="M652 578l1-2c2 1 2 1 4 1-2 1-3 0-4 2l1 1c-2 3-6 5-8 9l-3 4c1-2 2-5 2-8l7-7z" class="I"></path><defs><linearGradient id="Bo" x1="646.49" y1="603.997" x2="653.008" y2="591.001" xlink:href="#B"><stop offset="0" stop-color="#7c7878"></stop><stop offset="1" stop-color="#989592"></stop></linearGradient></defs><path fill="url(#Bo)" d="M654 592c1-1 2-1 4-2h2 1l1 1c-4 2-8 4-11 7-1 2-4 5-5 7l-1 1v-6c1-1 0-1 0-2 2-2 6-4 9-6z"></path><defs><linearGradient id="Bp" x1="633.622" y1="592.685" x2="655.224" y2="573.045" xlink:href="#B"><stop offset="0" stop-color="#302f2f"></stop><stop offset="1" stop-color="#575656"></stop></linearGradient></defs><path fill="url(#Bp)" d="M641 581h0c1-2 3-3 4-5s2-5 4-7c1-1 1-1 3-1l2 2-1 4v2l-1 2-7 7c-4 4-5 8-6 14l-1-1-1-3h-1c0-2 0-3-1-5 0-1 1-2 1-3v-1l2 1 3-6z"></path><path d="M652 578h-3 0c1-2 2-4 3-5v1h1v2l-1 2z" class="G"></path><path d="M635 590c0-1 1-2 1-3 2 3 2 4 1 8h0-1c0-2 0-3-1-5z" class="r"></path><path d="M654 570c5 4 11 7 14 13 0 3-1 4-2 6h-1-1c-2 1-3 0-5 0l-2 1-2-1h0c-2 0-3-1-4-2 3-3 4-5 7-7h0l1-1h-2l-3 1-1-1c1-2 2-1 4-2-2 0-2 0-4-1v-2l1-4z" class="M"></path><path d="M658 584l1-2c2 0 2 1 3 2v2l-2 2-1-1c-1-1-1-2-1-3z" class="e"></path><path d="M657 579h3 1l1 1h1 1l2 3c0 1 0 1-1 2h-1c0-1-1-2-2-3s-2-1-3-2c-1 1-2 1-3 3v3l1 1h0 1l-1-1c0-1 0-1 1-2 0 1 0 2 1 3l1 1h-1v1l-2 1-2-1h0c-2 0-3-1-4-2 3-3 4-5 7-7h0l1-1h-2z" class="u"></path><path d="M635 590c1 2 1 3 1 5h1l1 3 1 1h0c0 3 0 5 2 8 0 0 0 1 1 1 1-1 1-5 3-7v-1 6l1-1 1 1-1 1v3c0 2 0 3-1 4h0c0 1 0 2-1 3 1 2 1 2 3 3l-3 3c-1 1-2 1-3 2-1 0-1 1-1 2-1 0-1-1-2-1h-3l1-2c-2 2-2 3-2 6 0 1 0 2 1 3l1 3-2-3c-2-3-3-6-2-10v-3-1-3c0-1-1-2 0-3 0-1-1-1-1-2l4-21z" class="V"></path><path d="M646 605l1 1-1 1v3c0 2 0 3-1 4h0c0 1 0 2-1 3v1c-2 1-2 0-3 0 1-2 1-4 1-5 1-3 2-5 3-7l1-1z" class="e"></path><path d="M636 623c0-1 1-2 3-3h0c0-1 1-2 1-3l1 1c1 0 1 1 3 0v-1c1 2 1 2 3 3l-3 3c-1 1-2 1-3 2-1 0-1 1-1 2-1 0-1-1-2-1h-3l1-2v-1z" class="Q"></path><defs><linearGradient id="Bq" x1="635.243" y1="593.533" x2="632.529" y2="613.572" xlink:href="#B"><stop offset="0" stop-color="#575556"></stop><stop offset="1" stop-color="#736c6a"></stop></linearGradient></defs><path fill="url(#Bq)" d="M635 590c1 2 1 3 1 5h1l1 3 1 1h0l-2 15h0c-1 3-1 6-2 9h1v1c-2 2-2 3-2 6 0 1 0 2 1 3l1 3-2-3c-2-3-3-6-2-10v-3-1-3c0-1-1-2 0-3 0-1-1-1-1-2l4-21z"></path><path d="M636 595h1l1 3 1 1h0l-2 15h0v-2-7c-1-3-1-6-1-10z" class="B"></path><path d="M632 619c1-4 2-7 4-10h0l1 3v2c-1 3-1 6-2 9h1v1c-2 2-2 3-2 6 0 1 0 2 1 3l1 3-2-3c-2-3-3-6-2-10v-3-1z" class="n"></path><path d="M635 626h3c1 0 1 1 2 1 0-1 0-2 1-2 1-1 2-1 3-2h0c4 2 5 7 8 9 1 4-2 8-3 11v1c-2 3-3 9-2 13h0c-2 0-4-2-6-3v-1c-1-2-2-3-2-5l-4-7h0c1-2-1-5-1-8l2 3-1-3c-1-1-1-2-1-3 0-3 0-4 2-6l-1 2z" class="M"></path><path d="M644 623c4 2 5 7 8 9 1 4-2 8-3 11-1-1-2-1-2-2v-3c-1-1-1-2-2-3v-1h-1l1-1v-2-4c-1-2-1-3-1-4z" class="Q"></path><path d="M636 624l-1 2c0 2 0 2 1 4v2c1 1 1 1 3 2 2-1 3-2 5-4l-1-2h1c0 1 0 4-1 5v1c0 1 0 2 1 3l1 2h-1c-1 2-3 4-2 7 0 1 0 2 1 3 0 1 0 3-1 4h-1c-1-2-2-3-2-5l-4-7h0c1-2-1-5-1-8l2 3-1-3c-1-1-1-2-1-3 0-3 0-4 2-6z" class="I"></path><path d="M634 633l2 3 2 3c1 2 2 3 2 6v1c0 1-1 2-1 2l-4-7h0c1-2-1-5-1-8z" class="D"></path><path d="M666 589l6-2c-1 1 0 1-1 1l1 1v1 1h3c-1 12-5 22-11 33-1 1-5 8-7 9-1 0-2-1-2-2h-1c-1 1-1 1-2 1-3-2-4-7-8-9h0l3-3c-2-1-2-1-3-3 1-1 1-2 1-3h0c1-1 1-2 1-4v-3l1-1-1-1c1-2 4-5 5-7 3-3 7-5 11-7 1-1 2-1 3-2h1z" class="I"></path><path d="M665 618c0 1-2 4-1 6-1 1-5 8-7 9-1 0-2-1-2-2 2-1 8-11 10-13z" class="G"></path><path d="M658 610l3-1v2c-1 0-1 0-1 1-3 2-4 5-6 8h-5v-1-1h-1l1-2h0 2 1c-1 1-1 1-1 2h1l6-8z" class="u"></path><path d="M647 620l1 1c2 3 5 7 6 10-1 1-1 1-2 1-3-2-4-7-8-9h0l3-3z" class="E"></path><path d="M649 616c3-3 6-6 8-10 1-1 1-2 3-3l3 3h0-2v1h-1l-2 3-6 8h-1c0-1 0-1 1-2h-1-2z" class="M"></path><path d="M663 606l4 3v1c-2 2-4 5-6 8 0 1 0 1-1 2h0l-1 1h0v2l-6 3c1-2 3-3 4-5 2-4 6-10 6-14v-1h0z" class="d"></path><path d="M646 605c1-2 4-5 5-7 1 1 2 2 3 2v1l1 1v-1c1 0 2 0 3 1l2 1c-2 1-2 2-3 3-2 4-5 7-8 10h0l-1 2h1v1l-1 2-1-1c-2-1-2-1-3-3 1-1 1-2 1-3h0c1-1 1-2 1-4v-3l1-1-1-1z" class="D"></path><path d="M646 605c1-2 4-5 5-7 1 1 2 2 3 2v1c-3 1-5 4-6 6-1 1-1 2-2 3v-3l1-1-1-1z" class="O"></path><path d="M658 602l2 1c-2 1-2 2-3 3-2 4-5 7-8 10h0c0-2 1-3 2-6 2-3 5-5 7-8z" class="I"></path><path d="M666 589l6-2c-1 1 0 1-1 1l1 1v1 1h3c-1 12-5 22-11 33-1-2 1-5 1-6 0-2 3-7 3-8l-1-1-4-3-3-3-2-1c-1-1-2-1-3-1v1l-1-1v-1c-1 0-2-1-3-2 3-3 7-5 11-7 1-1 2-1 3-2h1z" class="AB"></path><path d="M665 603v-5c1-3 2-5 5-7 1 0 0 0 1-1h1v1 6c-1 2-2 4-2 6-1 2-1 3-2 4-2 0-3-1-4-2 0-1 0-2 1-2z" class="I"></path><path d="M665 603l2-2 1 1c0 1 0 1-1 2l-3 1c0-1 0-2 1-2z" class="X"></path><path d="M666 589l6-2c-1 1 0 1-1 1l1 1v1h-1c-1 1 0 1-1 1-3 2-4 4-5 7v5c-1 0-1 1-1 2l-1-1-5-4-2-2-2 2c-1 0-2-1-3-2 3-3 7-5 11-7 1-1 2-1 3-2h1z" class="D"></path><path d="M658 600c2-2 4-4 6-5 0 1 0 3-2 4h0l-1 2 2 2v1l-5-4z" class="H"></path><path d="M666 589l6-2c-1 1 0 1-1 1-2 2-4 3-6 4l-9 6-2 2c-1 0-2-1-3-2 3-3 7-5 11-7 1-1 2-1 3-2h1z" class="E"></path><defs><linearGradient id="Br" x1="492.857" y1="756.486" x2="561.106" y2="770.263" xlink:href="#B"><stop offset="0" stop-color="#070303"></stop><stop offset="1" stop-color="#330e0b"></stop></linearGradient></defs><path fill="url(#Br)" d="M572 690c2-2 4-6 5-9l1 1 1-1c0-1 1-2 2-3v1l-1 3-3 6c1 2 1 4 1 7l-15 39 1 1h0c1 1 1 1 1 3v1 1s-1 1-1 2-1 2-1 4 0 0-1 2c0 1-1 4-1 6 1 1 2 1 3 2l-4 11c-1 3-2 5-3 8l-12 37-6 20-1 1c-1 0-2 1-3 0l-3 1c0 2 0 4-1 6h0c0-2 0-2-2-3l-2-2c-2-3-4-4-7-5-4-1-7-1-10 1l-3 3v-1c-1 1-3 1-4 1l-1 4v1c-1 2-1 4-1 5-1 0 0-1-1-2l-2-9c-1-3-1-6-2-8 0-4-1-8-3-12-1-3-2-5-3-8l-2-9c-1-4-2-7-2-11l1-2c-1-1-2-2-3-2l1-3c1-2 2-4 3-5l2-2c1-2 3-4 4-7 0-2 0-3-1-5-1-1-2-2-3-2l-3-1h2c2-1 3 1 5-1l2 2h0l-2-3 1-1 2 2 3 3c1-1 3-2 4-3l9-5c5-2 9-5 13-8 2 0 3-1 4-2s3-2 5-3l6-5h0c2-2 3-3 5-4 4-3 8-8 11-12 1-2 3-4 4-6v-1c2-2 4-5 5-8l6-11z"></path><path d="M507 827h0l1 1-1 2 1 1-1 2c-1 1-3 1-4 1 1-3 2-5 4-7z" class="N"></path><path d="M504 813c1 3 1 6 1 9v3c-1 0-1 1-2 1-1-2-1-3-1-4 2-3 2-6 2-9z" class="z"></path><path d="M494 798l6 7-1 1c-3-2-5-5-8-7l1-1 2 2v-2z" class="S"></path><path d="M503 810h0l2 1h1c0 1 1 4 2 5v2h1l1 1-2 2-1-1c-1 0-1 1-2 2 0-3 0-6-1-9l-1-3z" class="K"></path><path d="M491 784h-1c1-1 1-1 1-2 1-2 7-7 10-8l3-1c-1 1-1 2-1 4h0l-4 2-1-1c-3 0-6 4-7 6z" class="j"></path><path d="M537 754c2-2 5-5 7-6l1-1v-1c2 0 4-2 5-3l1-2h2c2-2 5-7 6-10 0 2-2 5-3 7-2 3-4 6-6 8-1 1-2 1-4 2l-4 4v-1l-1 1c-1 1-2 1-4 2z" class="U"></path><path d="M491 789c1 1 1 1 1 2l1-1v1c1 4 5 8 8 11s4 6 5 9h-1l-2-1h0c-2-1-3-3-4-4l1-1-6-7c-1-3-2-5-3-9z" class="T"></path><path d="M539 773c-1 0-1 0-2 1h0-1c1-6 5-9 9-13l7-9c0 3-1 5-1 8-1 3-2 6-4 9l2-7c-2 1-4 3-6 5-1 2-3 4-4 6z" class="AG"></path><defs><linearGradient id="Bs" x1="491.289" y1="787.53" x2="502.317" y2="791.916" xlink:href="#B"><stop offset="0" stop-color="#3d0e0a"></stop><stop offset="1" stop-color="#611c17"></stop></linearGradient></defs><path fill="url(#Bs)" d="M491 784c1-2 4-6 7-6l1 1c-2 3-4 4-3 8v3l1 1c0 2 2 3 4 5 0 2 0 3 1 4l-1 2c-3-3-7-7-8-11v-1l-1 1c0-1 0-1-1-2v-4h1l-1-1z"></path><path d="M523 753c-2 4-4 5-7 7-2 2-5 3-8 5-1 1-2 2-3 2h-1-1c-3 2-7 4-10 8-1 1-2 2-3 4-1 1-1 2-2 3l-1 1c-1-1-2-2-3-2l1-3c1-2 2-4 3-5l2-2c0 1 0 1 1 1s1 1 2 1l12-9 9-5 9-6z" class="U"></path><path d="M490 771c0 1 0 1 1 1s1 1 2 1l-3 4c-1 2-2 3-2 5l-1 1c-1-1-2-2-3-2l1-3c1-2 2-4 3-5l2-2z" class="J"></path><path d="M485 778c2 1 3 0 5-1-1 2-2 3-2 5l-1 1c-1-1-2-2-3-2l1-3z" class="s"></path><path d="M572 690c2-2 4-6 5-9l1 1 1-1c0-1 1-2 2-3v1l-1 3-3 6-12 24c-2 2-3 5-5 7h-1c-1 0-1 0-1 1v1h-1c1-3 1-3 0-5 1-2 3-4 4-6v-1c2-2 4-5 5-8l6-11z" class="k"></path><path d="M557 716c1 2 1 2 0 5h1v-1c0-1 0-1 1-1h1c-7 11-19 21-29 29l-1-1v-1c1-2 2-3 4-4 1-2 2-5 4-6 1-1 2-2 3-4h0c2-2 3-3 5-4 4-3 8-8 11-12z" class="t"></path><path d="M552 750l1 1-1 1-7 9c-4 4-8 7-9 13h1 0c1-1 1-1 2-1-2 3-2 5-3 8-1 0-2-1-3-2l-1-3c0-2-2-5-2-7l-1-3c8-5 15-12 23-16z" class="AE"></path><path d="M556 738l2-1v1c1 0 1 0 1-1l1 1c-2 3-5 9-5 13l-1 1h-1c0-1 1-1 0-2h-1 0c-8 4-15 11-23 16-4 2-9 5-13 7h-2-2l-4-1c0-1 1-1 2-1 2-1 5-2 7-3l19-14h1c2-1 3-1 4-2l1-1v1l4-4c2-1 3-1 4-2 2-2 4-5 6-8z" class="AI"></path><path d="M510 819c3-2 3-2 6-1 2 0 4 1 6 2h4l1 3c1 0 1 1 2 1l1-1c0 2-1 4 0 5l1 1c0 2 0 2 1 4v1c0 2 0 4-1 6h0c0-2 0-2-2-3l-2-2c-2-3-4-4-7-5-4-1-7-1-10 1l-3 3v-1l1-2-1-1 1-2-1-1h0 0l-2-2v-3c1-1 1-2 2-2l1 1 2-2z" class="W"></path><path d="M508 828c4-2 8-3 12-2 1 1 2 2 3 2v1h-2c-3-2-6-2-9-1-2 1-3 2-4 3l-1-1 1-2z" class="T"></path><path d="M521 823c1 1 2 1 2 3v2c-1 0-2-1-3-2-4-1-8 0-12 2l-1-1c2-1 3-2 5-3 1-1 5-1 7-1h0 2z" class="R"></path><path d="M505 822c1-1 1-2 2-2l1 1v2l3-1c3-1 6-1 9 0l1 1h-2 0c-2 0-6 0-7 1-2 1-3 2-5 3h0 0l-2-2v-3z" class="T"></path><path d="M522 820h4l1 3c1 0 1 1 2 1l1-1c0 2-1 4 0 5l1 1c0 2 0 2 1 4v1c0 2 0 4-1 6h0c0-2 0-2-2-3l-2-2v-1-1l2-1-1-2v-2c-1-4-3-6-6-8z" class="N"></path><path d="M535 737l6-5c-1 2-2 3-3 4-2 1-3 4-4 6-2 1-3 2-4 4v1l1 1-8 5-9 6-9 5-12 9c-1 0-1-1-2-1s-1 0-1-1c1-2 3-4 4-7 0-2 0-3-1-5-1-1-2-2-3-2l-3-1h2c2-1 3 1 5-1l2 2h0l-2-3 1-1 2 2 3 3c1-1 3-2 4-3l9-5c5-2 9-5 13-8 2 0 3-1 4-2s3-2 5-3z" class="k"></path><path d="M508 758c2-2 7-4 9-6l1 1c-3 2-7 3-9 6l2 1v-1h3l-9 5h-4c1-2 5-4 7-6h0z" class="W"></path><path d="M494 755l2 2h0l-2-3 1-1 2 2 3 3c1-1 3-2 4-3l-1 4 1 1c1-1 3-2 4-2h0c-2 2-6 4-7 6h4l-12 9c-1 0-1-1-2-1s-1 0-1-1c1-2 3-4 4-7 0-2 0-3-1-5-1-1-2-2-3-2l-3-1h2c2-1 3 1 5-1z" class="R"></path><path d="M496 760l3 1c-1 1-1 2-2 3-1-1-1-3-1-4z" class="w"></path><path d="M494 755l2 2h0l-2-3 1-1 2 2 3 3c-1 1-1 2-1 3l-3-1c-2-2-3-3-6-3l-3-1h2c2-1 3 1 5-1z" class="Z"></path><path d="M500 758c1-1 3-2 4-3l-1 4 1 1c-2 2-5 3-7 5v-1c1-1 1-2 2-3 0-1 0-2 1-3z" class="AO"></path><path d="M563 734l1 1h0c1 1 1 1 1 3v1 1s-1 1-1 2-1 2-1 4 0 0-1 2c0 1-1 4-1 6 1 1 2 1 3 2l-4 11-3 8-12 37-1 1c-1 1-1 3-1 5l-1 1v1l-1 3h0c-1 1 0 1-1 2v1c0-1-1-2-1-3v-6c1-3 0-6 2-9-1-2 0-7 0-10 0-1 1-3 1-4 1-3 2-8 3-11 3-9 7-17 10-26 3-7 5-16 8-23z" class="q"></path><path d="M545 783v5l-3 11c1 1 1 2 2 3v3c0 2-1 4-2 6v3 4c-1 0-1 0-2-1 0-2 0-3 1-5v-4c-1-2 0-7 0-10 0-1 1-3 1-4 1-3 2-8 3-11z" class="K"></path><path d="M514 773h2c4-2 9-5 13-7l1 3c0 2 2 5 2 7l1 3 6 23c1-1 1-2 2-4 0 3-1 8 0 10-2 3-1 6-2 9v6c0 1 1 2 1 3v-1c1-1 0-1 1-2h0l1-3v-1l1-1c0-2 0-4 1-5l1-1-6 20-1 1c-1 0-2 1-3 0l-3 1v-1c-1-2-1-2-1-4l-1-1c-1-1 0-3 0-5l-1 1c-1 0-1-1-2-1l-1-3 1 1c0 1 1 1 2 2v-1-4-1-4c1 0 1-1 1-1v-10l-1-1v-1c-1-1-2-3-3-3v-2h-4l-2 2-1-4 1-1c3-2 3-5 3-8v-1c-1-5-5-8-9-10z" class="AL"></path><path d="M523 784l2 2v-1c1 0 1 0 1 1 1 2 1 4 0 5v4h-4l-2 2-1-4 1-1c3-2 3-5 3-8z" class="T"></path><path d="M529 789c3 3 3 11 4 14 1-1 1-2 1-4 0 4 1 8 1 12l-1-1h-2l-1 4c-1-9 0-16-2-25z" class="K"></path><path d="M529 789h0l-3-7c-1-1-2-2-2-4 0-1 0-2 1-3h3c0-1 1-1 1-2v-3l1-1c0 2 2 5 2 7-1 1-1 1-1 2h-1c-1-2-1-2-2-3l-1 2h-1c0 2 3 7 4 10 2 4 2 8 4 12 0 2 0 3-1 4-1-3-1-11-4-14z" class="U"></path><path d="M534 799c-2-4-2-8-4-12-1-3-4-8-4-10h1l1-2c1 1 1 1 2 3h1c0-1 0-1 1-2l1 3 6 23c1-1 1-2 2-4 0 3-1 8 0 10-2 3-1 6-2 9v-3c0-1-1-2-1-3s-1-1-1-1h-1l-1 1h0c0-4-1-8-1-12z" class="s"></path><path d="M539 802c1-1 1-2 2-4 0 3-1 8 0 10-2 3-1 6-2 9v-3c0-4-1-8 0-12z" class="j"></path><path d="M531 814l1-4h2l1 1h0l1-1h1s1 0 1 1 1 2 1 3v3 6c0 1 1 2 1 3v-1c1-1 0-1 1-2h0l1-3v-1l1-1c0-2 0-4 1-5l1-1-6 20-1 1c-1 0-2 1-3 0l-3 1v-1c-1-2-1-2-1-4l-1-1c-1-1 0-3 0-5l1-9z" class="C"></path><path d="M531 829c2 1 4 2 7 4-1 0-2 1-3 0l-3 1v-1c-1-2-1-2-1-4z" class="T"></path><defs><linearGradient id="Bt" x1="500.657" y1="778.888" x2="530.709" y2="820.921" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#1a0807"></stop></linearGradient></defs><path fill="url(#Bt)" d="M504 773l4-1 4 1h2c4 2 8 5 9 10v1c0 3 0 6-3 8l-1 1 1 4 2-2h4v2c1 0 2 2 3 3v1l1 1v10s0 1-1 1v4 1 4 1c-1-1-2-1-2-2l-1-1h-4c-2-1-4-2-6-2-3-1-3-1-6 1l-1-1h-1v-2c-1-1-2-4-2-5-1-3-2-6-5-9l1-2c-1-1-1-2-1-4-2-2-4-3-4-5l-1-1v-3c-1-4 1-5 3-8l4-2h0c0-2 0-3 1-4z"></path><path d="M504 773l4-1 4 1c-1 0-2 0-3 2h0l-6 2h0c0-2 0-3 1-4z" class="K"></path><path d="M521 786v-2c1 1 0 1 1 2l1-1v-1h0c0 3 0 6-3 8l-1 1c-3 1-4 2-8 1v-1c2 0 4-1 6-2 2-2 3-3 4-5z" class="i"></path><path d="M512 773h2c4 2 8 5 9 10v1h0v1l-1 1c-1-1 0-1-1-2v2c0-2-1-4-1-5-3-4-6-5-11-6h0c1-2 2-2 3-2z" class="J"></path><path d="M511 794c4 1 5 0 8-1l1 4 2-2h4v2c-1 4-3 5-6 7-4 0-5 0-8-2v-1c3 0 5 2 7 1l2-2c1-1 1-2 2-3 1 0 0 0 1-1h-2l-1 2h-2 0v-2c-1 1-2 3-4 4-1 0-1 0-2-1 0-1 2-2 3-3h-1-1c-2-1-2-1-3-2z" class="AG"></path><path d="M501 796c4 4 9 9 9 15 0 2 0 5-1 7h-1v-2c-1-1-2-4-2-5-1-3-2-6-5-9l1-2c-1-1-1-2-1-4z" class="l"></path><path d="M663 451c1 2 1 5 0 7v1h-1v1c-6 8-9 19-10 28-5 12-8 25-11 37l-9 31c-2 5-5 11-5 17h1c1 2 3 4 4 5l3 3c0 1 0 1-1 2-3 1-8 0-10 2-1 1-2 2-2 3l-1 3-9 30-22 70-11 36-12 36c-1 3-4 8-4 10v1c-2 2-2 4-3 7-1 2-2 5-3 8l-5 19h-1l1-3c0-1 0-1-1-2v-5l5-16c1-2 2-5 2-7h-1c1-3 2-5 3-8l4-11c-1-1-2-1-3-2 0-2 1-5 1-6 1-2 1 0 1-2s1-3 1-4 1-2 1-2v-1-1c0-2 0-2-1-3h0l-1-1 15-39c0-3 0-5-1-7l3-6 1-3v-1c-1 1-2 2-2 3l-1 1-1-1c-1 3-3 7-5 9l1-2-1-1v-4c-1 1-3 5-3 6-1 1-2 3-3 4-2 0-2 1-4 3v1c-1 1-2 1-3 2v-1c2-2 4-4 5-7l-1-1-2 1-1 3h-3c1-2 3-4 4-6 0-2 1-4 1-6-1-2-2-4-2-6v-1s0-1-1-2c0-1 0-1-1-2v-1-2h0c0-2-1-3-2-4-1 2-2 3-4 3l-1-1 3-8v-1c-1-1-1-2-1-3l1-2c-1 0-2 0-3-1 0-1 1-1 2-1 2 0 4-1 6-2l2-1 2-1c1-1 3-2 4-3 2 0 3 0 5-1 1 0 3-1 4-1v1l-1 1c2-1 3-3 4-4 2 0 3-1 4-2 1 0 1-1 2-1h1c2-1 3-3 4-4 1 0 2 0 3 1 0-1 1-1 1-2l3-8v-1h-3c1-2 2-5 2-7l1-8v-2c-2 1-4 4-5 6l-2-1h0c0-2-1-3-1-5l1-1c2-3 4-5 6-9h0l1-7v-5c1-1 1-1 1-2l1-6-6-6c-1-1-2-1-3-1l-2 2h-1l1-2c1-1 2-1 4-1l1 1 1-1c0-1-1-2-2-3h1c2 2 3 4 6 4 1 2 2 3 3 4h0l2-1c1-4 2-8 4-13 1-4 1-8 4-11v-4h0c6-5 9-12 14-17l1 1v-1c1-1 2-2 2-3l3-8 4-11 4-11c1-2 1-5 1-7 1-1 1-3 2-5l1-5 2-11c2-1 3-3 4-5 0-1 0-1 1-1l1 1h0l2-8 4 2h0c2 1 3 2 4 3h1 0v-6h0z" class="s"></path><path d="M663 451c1 2 1 5 0 7v1h-1v1l-3-4h0c-1-1-2-1-3 0s-2 2-2 3l-1 1h-1l2-8 4 2h0c2 1 3 2 4 3h1 0v-6h0z" class="AI"></path><path d="M621 591l-1-1c1-1 1-1 1-2l1-1v-1h0v-3-1l1 3v-2c0-2 1-1 1-2v-2-6l2-2c1-1 0-1 1-2v4h1c1 2 3 4 4 5l3 3c0 1 0 1-1 2-3 1-8 0-10 2-1 1-2 2-2 3l-1 3z" class="i"></path><path d="M646 465c2-1 3-3 4-5 0-1 0-1 1-1l1 1-15 48h-1v-4l4-11c1-2 1-5 1-7 1-1 1-3 2-5l1-5 2-11z" class="AR"></path><path d="M632 515l4-11v4h1l-15 54-1-1 1-1-1-1c1-2 1-4 1-5h-1v4l-1-1h0v1c0-1-1-2-1-2v-1-2c0-1 0-1 1-2v-2c0-1 1-2 2-2 0-1 1-2 1-3l1-3c1-2 1-3 1-4v-1l1-1c2-3 4-8 3-12l3-8z" class="h"></path><path d="M593 648h1c1-2 2-5 3-6h0c0 1-1 2-1 3l-1 5-16 54h0c-1-4 1-9 1-12l-2 3c0-3 0-5-1-7l3-6 1-3c1-1 2-3 3-5 1-1 1-3 2-5l1 1c0-2 0-3 1-4-1 0-2 0-2-1 3-5 5-11 7-17z" class="AO"></path><path d="M581 679c1-1 2-3 3-5 1-1 1-3 2-5l1 1c-1 1-2 3-2 5-1 2-3 4-3 6l2-1c0 2-2 6-3 8l-1 4-2 3c0-3 0-5-1-7l3-6 1-3z" class="c"></path><path d="M580 682h1c0 1-1 2-1 4 0 1 0 1 1 2l-1 4-2 3c0-3 0-5-1-7l3-6z" class="R"></path><path d="M578 695l2-3c0 3-2 8-1 12h0c-1 6-3 12-5 17l-4 15c-2 7-5 13-6 20-1-1-2-1-3-2 0-2 1-5 1-6 1-2 1 0 1-2s1-3 1-4 1-2 1-2v-1-1c0-2 0-2-1-3h0l-1-1 15-39zm51-172c1 4-1 9-3 12l-1 1v1c0 1 0 2-1 4l-1 3c0 1-1 2-1 3-1 0-2 1-2 2v2c-1 1-1 1-1 2v2 1s1 1 1 2v-1h0l1 1v-4h1c0 1 0 3-1 5l1 1-1 1 1 1-6 18c-1 2-1 5-2 6-2 10-5 20-8 30l-8 25c-1 3-1 6-3 9l1-5c0-1 1-2 1-3h0c-1 1-2 4-3 6h-1c1-2 2-4 1-5 2-3 4-9 4-12h-1l-1 3-1-1c-1 1-2 3-3 5l-1-2s1-2 2-3c0-1 1-1 1-2l3-8v-1h-3c1-2 2-5 2-7l1-8v-2c-2 1-4 4-5 6l-2-1h0c0-2-1-3-1-5l1-1c2-3 4-5 6-9h0l1-7v-5c1-1 1-1 1-2l1-6-6-6c-1-1-2-1-3-1l-2 2h-1l1-2c1-1 2-1 4-1l1 1 1-1c0-1-1-2-2-3h1c2 2 3 4 6 4 1 2 2 3 3 4h0l2-1c1-4 2-8 4-13 1-4 1-8 4-11v-4h0c6-5 9-12 14-17l1 1v-1c1-1 2-2 2-3z" class="c"></path><path d="M604 585h1c0 3-1 8-3 10l-1 1h-2l2-6h1c1-1 1-2 2-3v-2h0z" class="a"></path><path d="M611 584l-4 7v-7-2c0-1 0-3-1-4h0v-1h1l1 2h1v-2h3c0 1 0 2-1 3v4z" class="J"></path><path d="M597 602c2-2 4-4 7-6l5-3h0l-1 2-5 4c-3 4-3 7-4 11-1 1-1 1-2 1v-4-2c-2 1-4 4-5 6l-2-1c2-3 5-5 7-8z" class="N"></path><path d="M593 568l1-1c0-1-1-2-2-3h1c2 2 3 4 6 4 1 2 2 3 3 4l3 3-1 1h0v3l1 1v5h-1 0v2c-1 1-1 2-2 3h-1c0-5 0-8-1-13l-1-2-6-6c-1-1-2-1-3-1l-2 2h-1l1-2c1-1 2-1 4-1l1 1z" class="AN"></path><path d="M593 568l1-1c0-1-1-2-2-3h1c2 2 3 4 6 4 1 2 2 3 3 4l3 3-1 1h0v3l1 1v5h-1v-4c-1-2-2-6-4-7-3-2-5-4-7-6z" class="AR"></path><path d="M599 575l1 2c1 5 1 8 1 13l-2 6c-1 1-2 2-2 4v2c-2 3-5 5-7 8h0c0-2-1-3-1-5l1-1c2-3 4-5 6-9h0l1-7v-5c1-1 1-1 1-2l1-6z" class="K"></path><path d="M599 575l1 2c-1 3-1 6-1 9v4l-2-2v-5c1-1 1-1 1-2l1-6z" class="j"></path><path d="M596 595v1c0 2-2 4-3 6l1 1 3-3v2c-2 3-5 5-7 8h0c0-2-1-3-1-5l1-1c2-3 4-5 6-9z" class="i"></path><path d="M621 558v-4h1c0 1 0 3-1 5l1 1-1 1 1 1-6 18c-1 2-1 5-2 6-2 1-4 3-5 5v1l1-5c0-1 1-1 1-3h0v-4c1-1 1-2 1-3h0c1-4 3-7 3-10 1-2 1-3 1-4 1-3 2-6 3-8v1s1 1 1 2v-1h0l1 1z" class="W"></path><path d="M621 558v-4h1c0 1 0 3-1 5l1 1-1 1 1 1-6 18-1-2c0-3 2-7 3-11l3-9z" class="Z"></path><path d="M603 599l5-4c-1 1-1 2-2 4 1 1 1 2 0 3-1 5-3 9-4 14-1 4-1 10-3 13l-1 1 1-5h-1c-1 2-1 4-1 6l-1 3-1-1c-1 1-2 3-3 5l-1-2s1-2 2-3c0-1 1-1 1-2l3-8v-1h-3c1-2 2-5 2-7l1-8v4c1 0 1 0 2-1 1-4 1-7 4-11z" class="i"></path><path d="M603 599c1 5-1 8-2 13-1 2-1 5-1 7l-1 1v-1c-1 1-1 3-2 4v-1h-3c1-2 2-5 2-7l1-8v4c1 0 1 0 2-1 1-4 1-7 4-11z" class="J"></path><path d="M597 607v4c1 0 1 0 2-1v9c-1 1-1 3-2 4v-1h-3c1-2 2-5 2-7l1-8z" class="g"></path><defs><linearGradient id="Bu" x1="609.552" y1="549.925" x2="615.956" y2="552.364" xlink:href="#B"><stop offset="0" stop-color="#060504"></stop><stop offset="1" stop-color="#200807"></stop></linearGradient></defs><path fill="url(#Bu)" d="M629 523c1 4-1 9-3 12l-1 1v1c0 1 0 2-1 4l-1 3c0 1-1 2-1 3-1 0-2 1-2 2v2c-1 1-1 1-1 2v2c-1 2-2 5-3 8l-2-4-3 11c-1 2-2 3-3 5h-2-1l-3-3h0l2-1c1-4 2-8 4-13 1-4 1-8 4-11v-4h0c6-5 9-12 14-17l1 1v-1c1-1 2-2 2-3z"></path><path d="M611 570v-1l-1-1v-1c0-2 1-6 3-8 0 0 0-1 1-1v-1h1l-1 2-3 11z" class="S"></path><path d="M612 547c0 4-3 10-4 15-1 3-1 7-2 11h0l-1-1-1 1c1 1 2 1 2 2h-1l-3-3h0l2-1c1-4 2-8 4-13 1-4 1-8 4-11z" class="z"></path><path d="M629 523c1 4-1 9-3 12l-1 1v1c0 1 0 2-1 4l-1 3c0 1-1 2-1 3-1 0-2 1-2 2v2c-1 1-1 1-1 2v2c-1 2-2 5-3 8l-2-4 1-2 12-30v-1c1-1 2-2 2-3z" class="Z"></path><defs><linearGradient id="Bv" x1="568.938" y1="645.407" x2="583.276" y2="672.509" xlink:href="#B"><stop offset="0" stop-color="#0a0101"></stop><stop offset="1" stop-color="#29100f"></stop></linearGradient></defs><path fill="url(#Bv)" d="M597 631h1c0 3-2 9-4 12 1 1 0 3-1 5-2 6-4 12-7 17 0 1 1 1 2 1-1 1-1 2-1 4l-1-1c-1 2-1 4-2 5-1 2-2 4-3 5v-1c-1 1-2 2-2 3l-1 1-1-1c-1 3-3 7-5 9l1-2-1-1v-4c-1 1-3 5-3 6-1 1-2 3-3 4-2 0-2 1-4 3v1c-1 1-2 1-3 2v-1c2-2 4-4 5-7l-1-1-2 1-1 3h-3c1-2 3-4 4-6 0-2 1-4 1-6-1-2-2-4-2-6v-1s0-1-1-2c0-1 0-1-1-2v-1-2h0c0-2-1-3-2-4-1 2-2 3-4 3l-1-1 3-8v-1c-1-1-1-2-1-3l1-2c-1 0-2 0-3-1 0-1 1-1 2-1 2 0 4-1 6-2l2-1 2-1c1-1 3-2 4-3 2 0 3 0 5-1 1 0 3-1 4-1v1l-1 1c2-1 3-3 4-4 2 0 3-1 4-2 1 0 1-1 2-1h1c2-1 3-3 4-4 1 0 2 0 3 1-1 1-2 3-2 3l1 2c1-2 2-4 3-5l1 1 1-3z"></path><path d="M579 639c2 0 3-1 4-2 1 0 1-1 2-1h1c-4 5-9 10-15 12 0-3 2-3 4-5 2-1 3-3 4-4z" class="m"></path><path d="M584 656c1 2-1 5-1 8l-4 7-6 6c-1 0-1 0-1 1-1 0-2 1-3 1h0c3-6 11-10 12-17l-3 1c2-2 4-4 6-7z" class="N"></path><path d="M567 643c2 0 3 0 5-1 1 0 3-1 4-1v1l-1 1c-2 2-4 2-4 5-6 2-11 4-17 4-1 0-2 0-3-1 0-1 1-1 2-1 2 0 4-1 6-2l2-1 2-1c1-1 3-2 4-3z" class="t"></path><path d="M569 679h0c1 0 2-1 3-1 0-1 0-1 1-1l6-6c-1 2-2 4-4 6l-3 6h0c-1 1-3 5-3 6-1 1-2 3-3 4-2 0-2 1-4 3v1c-1 1-2 1-3 2v-1c2-2 4-4 5-7l-1-1-2 1-1 3h-3c1-2 3-4 4-6 3-2 6-6 8-9z" class="K"></path><path d="M561 691l2-3c4-1 6-4 8-7h1v2h0c-1 1-3 5-3 6-1 1-2 3-3 4-2 0-2 1-4 3v1c-1 1-2 1-3 2v-1c2-2 4-4 5-7l-1-1-2 1z" class="l"></path><path d="M591 636l1 2-2 4c-1 1-1 1-1 2 0 3-2 9-5 11v1c-2 3-4 5-6 7-1 1-2 2-3 2-2 1-3 2-5 3l-1-1v-1a30.44 30.44 0 0 1 8-8c6-7 10-14 14-22z" class="R"></path><path d="M575 665h-1l8-9h1l1-1v1c-2 3-4 5-6 7-1 1-2 2-3 2z" class="w"></path><path d="M597 631h1c0 3-2 9-4 12 1 1 0 3-1 5-2 6-4 12-7 17 0 1 1 1 2 1-1 1-1 2-1 4l-1-1c-1 2-1 4-2 5-1 2-2 4-3 5v-1c-1 1-2 2-2 3l-1 1-1-1c-1 3-3 7-5 9l1-2-1-1v-4h0l3-6c2-2 3-4 4-6l4-7c0-3 2-6 1-8v-1c3-2 5-8 5-11 0-1 0-1 1-2l2-4c1-2 2-4 3-5l1 1 1-3z" class="i"></path><path d="M586 665c0 1 1 1 2 1-1 1-1 2-1 4l-1-1c-1 2-1 4-2 5-1 2-2 4-3 5v-1c-1 1-2 2-2 3l-1 1-1-1c-1 3-3 7-5 9l1-2 13-23z" class="W"></path><path d="M597 631h1c0 3-2 9-4 12l-4 10c-1 2-2 6-4 8h0l-3 3c0-3 2-6 1-8v-1c3-2 5-8 5-11 0-1 0-1 1-2l2-4c1-2 2-4 3-5l1 1 1-3z" class="q"></path><path d="M597 631h1c0 3-2 9-4 12l-4 10c-1 2-2 6-4 8h0c2-4 3-8 4-13l6-14 1-3z" class="Z"></path><path d="M376 424c5 0 8 0 13 3 3 2 5 3 5 7h0l1 1v2h0c-1 0-1-1-2-1l-1 1 1 2h2c1 1 1 1 3 2l1 1 1 1-1 1-2-1h-1c2 1 3 2 5 4 0 0 2 1 2 2 0 2 1 4 2 6s0 6 0 7l-1 1v1c1 1 1 1 1 2 0-1 0-2 1-3v-1l1 1c0-2 1-4 1-6 1 0 3-1 4-2l2 2c2 1 4 4 5 5l-1-3v-6l7 17 1-1v-1l-1-1v-2h-1v-2l-1-1v-4c-1-1 0-1-1-2 0-1 0 0 1-1h1 0v-2l1-1v2c0 1 1 2 2 3v1l-1 1 1 1c1 2 1 5 2 7s1 4 2 5c0 2 1 5 1 7l6 13 3 6c1 2 1 5 2 7l3 6c0 3 1 6 2 9 1 1 2 3 2 4v1l-1 1 1 1h0c0 1 1 2 1 4l1 1-1 1c3 8 7 16 9 24 3 7 5 14 8 21 1 3 3 6 4 10l8 21c1 2 4 12 5 13l3 10 24 66c0 2 1 6 2 8l1-1c2 3 2 6 3 9h1l1 3 1 4c0 1 1 1 2 2 2 3 3 6 4 9s1 6 3 8c-1 1-2 2-4 2-4 3-8 6-13 8l-9 5c-1 1-3 2-4 3l-3-3-2-2-1 1 2 3h0l-2-2c-2 2-3 0-5 1h-2c-1 0-3 0-4 1-1 0-2 0-3 1-1 0-1 0-2 1v-2-1c-1 0-1 1-2 1v2l-1 1h-1c1-2 1-3 2-3v-3l-2 1h-1c-5-13-9-27-13-40l-11-34-4-11c0-2-1-3-1-4 0-2 0-2-1-3-3-1-5-13-6-16l-6-15h-1-5-1l-1-3-1-5c-3-5-4-11-6-17l-19-53-17-50-5-16c-2-5-3-11-5-16-4-10-7-21-10-31h0c1 1 2 2 2 3h2l1 1h1l-1-1v-2-3-6h0c3-5 6-7 11-9z" class="b"></path><path d="M440 617h-3-1l1-1c1-1 2-2 4-2v2l-1 1z" class="AG"></path><path d="M441 614c3 0 5 1 8 3l-1 1-1-1h-3v-1h-3v-2z" class="T"></path><path d="M471 650l1 1 1 1c0 2 0 3-2 5-1-3-3-5-5-7 2 1 3 1 5 1v-1z" class="w"></path><path d="M408 474v-2c1-1 2-1 3-2h1c0 2 1 4 1 6l-3-1c-1 0-1-1-2-1h0z" class="S"></path><path d="M449 618c1 0 2 1 2 3v3h-1l-4-2c1-1 1-1 1-2l2-2z" class="AG"></path><path d="M441 616h3v1h3l1 1 1-1v1l-2 2c0 1 0 1-1 2l-6-5 1-1z" class="U"></path><path d="M472 674c2 3 3 5 3 9v3l-3-1s1-1 1-2c0-3-1-5-3-7l2-2z" class="a"></path><path d="M473 635h1c-1 2-1 2-1 4h2v1l-2 2c-1 0-2-1-3-1h0c-1-1-1-1-2-1l-1-2c2 0 4-1 6-2v-1z" class="AG"></path><path d="M425 614h1c1-1 1-2 1-3 1-1 2-1 3-2h1c0 2-1 4-2 6v3 1c-1-1-1 0-1-1s0-1-1-1v1h-1l-1-3v-1z" class="N"></path><path d="M411 530l-2 4v-1c1 0 1 0 2-1l2 1c-3 2-5 5-6 8-1 0-1-1-1-2-1-3 1-4 2-6v-1l3-2z" class="J"></path><path d="M450 645l2-2c5-2 12-1 17 0-4 2-8 1-12 1-2 0-4 1-6 1h-1z" class="m"></path><path d="M371 445l1-1v-2c1-1 2-2 4-2v3l1 1c0 1 0 2-1 3 0 0-1 1-2 1h0l-3-3z" class="N"></path><path d="M397 518c2 1 5 3 7 3h1 1l-3 1c0 1 0 2 1 3h-3l-1 2h0c-1-4-2-6-3-9z" class="AG"></path><path d="M393 507l-3-3v-5c0-2-1-4-1-7 1 2 0 0 1 0 1 1 2 2 3 2v3c-1 2-1 7 0 10z" class="U"></path><path d="M448 666c2-1 2-2 3-4-1 0 0 0-1-1h-1c0-1 0-1 1-3 1 1 3 1 4 1 1 1 2 2 2 3l-1 1c0 1-1 1-1 1h-1 0c-1 1-3 2-4 2h-1z" class="z"></path><path d="M442 633l-1-1h1c1 1 2 1 4 1 3 2 5 3 9 3h-4c1 2 2 2 3 2l-3 1c-4-1-6-3-9-6z" class="g"></path><path d="M434 556c3 3 5 5 6 10-1 1-2 3-3 5v-5c-1-2-2-5-3-7v-3z" class="J"></path><path d="M422 547h2v2h4v1h-2l-1 1c2 1 4 1 5 2 1 0 2 1 2 2-3-1-4-1-7 0h0-1 0l-2-1 1-3c0-2-1-3-1-4z" class="AL"></path><path d="M396 443c2 1 3 2 5 4 0 1 0 2 1 3 1 2 1 3 1 5h0c-1 0-1 1-2 1-1-1-1-2-1-3-1-4-3-6-6-9l2-1z" class="K"></path><path d="M365 442v-3-6c0 2 1 3 2 5v-1-1c0 4 1 6 3 8l1 1 3 3h0l-1 1-3-1c-2-2-4-3-5-6z" class="J"></path><path d="M408 474h0c1 0 1 1 2 1l3 1v7h1l-4 2c-1-4-2-7-2-11z" class="z"></path><path d="M376 447l2 2c1-1 0-1 1-2v1c1 3 6 5 5 7-1 1-2 2-3 2l-2-1h0v-2c-1 0-1-1-1-2l1-1-1-2h-5l1-1c1 0 2-1 2-1z" class="AG"></path><path d="M370 459l-4-10h1c3 4 5 7 7 11 1 2 3 5 3 7-2-3-4-6-7-8z" class="K"></path><path d="M393 439h2c1 1 1 1 3 2l1 1 1 1-1 1-2-1h-1l-2 1h-6v-1c0-1 0-1 1-2s2-1 3 0l1-2z" class="g"></path><path d="M393 439h2c1 1 1 1 3 2l1 1-6-1h-1l1-2z" class="J"></path><path d="M420 570c2 3 4 6 7 8 1 1 3 1 5 2 0 3 2 7 3 10-1-2-4-5-5-7 0-1 0 0-1-1-1-2-3-3-4-3-1 2 1 5 2 7h0c-1-1-3-4-3-6 0-1-1-1-1-2h0 2c-1-2-3-3-4-5h0-1 0v-3z" class="AG"></path><path d="M376 440h1v1h2 1v-3h1v1c0 1 1 2 1 2l1 1h0c0 1 0 2 1 3 0 2-1 2-2 3 0-1-1-2-1-2 0-1 0-1-1-1l-1 2c-1 1 0 1-1 2l-2-2c1-1 1-2 1-3l-1-1v-3z" class="z"></path><path d="M440 635l1-1 1-1c3 3 5 5 9 6 0 1-1 2-1 3-1 0-3 0-3 1-2 1-1 2-4 2-1-1-3-3-3-5 1 2 2 3 3 3 0-1 0-2 1-3 1 0 2 1 4 1h0l-1-1c-2-1-4-4-7-5z" class="U"></path><path d="M477 658h0c0-2 0-2 1-3l1 2 1-1c2 1 2 2 2 3l2 5c1 4 1 8 0 12-1-6-3-12-5-18h-1-1z" class="T"></path><path d="M380 460c2-1 4-2 7-1 2 1 4 3 5 5s1 5 0 7c0 1-1 1-2 1h0l-1-4c0-1 0-2-1-3-1-2-2-3-4-4-2 0-2 0-4-1z" class="R"></path><path d="M383 442l2 3c2 0 4 3 6 4 2 3 5 8 5 12h0c-1 0-1-1-1-2-1-2-2-3-4-4s-3-3-5-4c-1-1-3-2-4-3 1-1 2-1 2-3-1-1-1-2-1-3h0z" class="AL"></path><path d="M408 550l1-3c1-1 3-4 4-4 1 1 1 1 2 1 2 0 2-1 4-2h0 0c2 0 1 0 2 1v3c0-1-1-2-1-2l-1-1c-2 1-2 3-3 5-1 1-1 4 0 6-2 0-2 0-2-2-2 0-4-2-6-2z" class="S"></path><path d="M400 453c0 1 0 2 1 3 1 0 1-1 2-1 1 2 1 4 1 6-1 4-3 8-6 10v-2h-1c1-4 1-7 2-10 1-2 1-4 1-6z" class="N"></path><path d="M403 455c1 2 1 4 1 6-1-1-1-2-1-2-1 1-1 0-2 1v-4c1 0 1-1 2-1z" class="AI"></path><path d="M472 651c1-1 1-1 3-1l1 1 1-1 1 3 2 3-1 1-1-2c-1 1-1 1-1 3h0l-4 6h-1v-4l-1-3c2-2 2-3 2-5l-1-1z" class="K"></path><path d="M472 651c1-1 1-1 3-1l1 1 1-1 1 3c-1 0-2 0-3 1v2c0-2-1-2-2-4l-1-1z" class="s"></path><path d="M473 652c1 2 2 2 2 4v3l-2 1h-1 0l-1-3c2-2 2-3 2-5z" class="AC"></path><path d="M425 615l1 3h1v-1c1 0 1 0 1 1s0 0 1 1v4c0 2 1 6 2 9h-1-5-1l-1-3 2-1v-2-1-10z" class="T"></path><path d="M425 625l1 2c1 1 1 2 2 2-2 1-3 0-3 1v2h-1l-1-3 2-1v-2-1zm-21-100l1 1c1 1 2 2 2 4 1 1 1 1 1 2v1c-1 2-3 3-2 6 0 1 0 2 1 2-1 2-1 3-2 4-1-4-1-8-2-11l-1-1-2-6 1-2h3z" class="N"></path><path d="M404 525l1 1c-2 2-2 5-3 7l-2-6 1-2h3zm12 29c-1-2-1-5 0-6 1-2 1-4 3-5l1 1s1 1 1 2l1 1c0 1 1 2 1 4l-1 3c-1 0-2 1-3 2h0c-1 0-1-1-2 0h-1v-2z" class="U"></path><path d="M446 649l1 1c2-1 3-2 4-3l1 2c-3 3-6 6-7 10 0 2 0 4 1 5l2 2h1c1 0 3-1 4-2h0 1c-2 2-3 3-5 3s-3-1-5-1c0-2 0-2-1-3v-1c-1-5 0-9 3-13z" class="w"></path><path d="M441 579c1 1 2 2 4 2h-2c-1 2-2 6-1 9 0 3-1 5-3 8l1 2c-2 2-8 5-12 6l2-1c3-5 12-15 11-21-1-2-1-2-2-3 0-1 1-2 2-2z" class="Z"></path><path d="M422 554l2 1h0c-2 2-4 4-4 7-1 3-1 5 0 8v3h0l4 13c-1 0-2-1-2-2v-1l-1-1v5c0-2-1-3-1-5-2-7-4-14-4-21 1-2 2-4 3-5s2-2 3-2z" class="S"></path><path d="M437 566v5h0c-1 1-2 3-2 4 2-1 2-1 3-3v3h1v2l1-1h2v-1h0l2-2 1 1 3-4 2 1c1 1 1 2 1 3l-3 2v1l-1 1-6 1c-1 0-2 1-2 2l-5-1h-2c-2-1-4-1-5-2v-1l-1-2v-2l4 2c1 1 1 1 2 1 4-3 4-5 5-10z" class="N"></path><path d="M445 574l3-4 2 1c1 1 1 2 1 3l-3 2v1l-1 1-6 1c-1 0-2 1-2 2l-5-1v-1c1-1 3-2 4-2 3 0 4-1 7-3z" class="g"></path><path d="M469 643c3 1 5 3 6 5l2 2-1 1-1-1c-2 0-2 0-3 1l-1-1v1c-2 0-3 0-5-1h0c-5-2-9-2-14-1l-1-2c-1 1-2 2-4 3l-1-1c1-2 2-3 4-4h1c2 0 4-1 6-1 4 0 8 1 12-1z" class="h"></path><path d="M468 647v-1c2 1 3 1 4 1 1 1 1 1 2 1h1l2 2-1 1-1-1c-2 0-2 0-3 1l-1-1v-1c-1-1-2-1-3-2z" class="AC"></path><path d="M451 647c2 0 4-2 6-2 3 0 7 0 9 2h2c1 1 2 1 3 2v1 1c-2 0-3 0-5-1h0c-5-2-9-2-14-1l-1-2z" class="c"></path><path d="M468 647c1 1 2 1 3 2l-1 1c-2 0-3-2-4-3h0 2z" class="W"></path><path d="M440 600l-1-2c2-3 3-5 3-8-1-3 0-7 1-9h2 1l2 4c-1 2-1 4-2 6 0 2 1 2 2 4l-1 1c1 0 1 1 2 2-1 2 0 5 1 8v1c-1-1-1-3-2-4h-1v3l2 2v1c-3-3-4-7-7-10l-2 1z" class="R"></path><path d="M448 585c-1 2-1 4-2 6 0 2 1 2 2 4l-1 1-2-2c-1 0-1-1-1-1 0-4 1-6 4-8z" class="c"></path><path d="M440 640c-2-2-3-5-5-8-1-1-1-1-1-3 1-1 0-1 0-2l1-1h0v-3-1c1 0 1 0 2-1l1-1c2 1 5 3 5 6l-3 3c1 1 2 2 3 2l3 2c-2 0-3 0-4-1h-1l1 1-1 1-1 1c3 1 5 4 7 5l1 1h0c-2 0-3-1-4-1-1 1-1 2-1 3-1 0-2-1-3-3z" class="N"></path><path d="M440 635l1 1h-1l-1-1c-2-2-3-4-3-7l1 1v-1h0c-1-2-1-4-1-5h1c0 2 0 3 1 4s2 1 2 2c1 1 2 2 3 2l3 2c-2 0-3 0-4-1h-1l1 1-1 1-1 1z" class="C"></path><path d="M474 607c1 1 2 1 2 3 1 2 0 5 1 6-2 7-7 15-13 18-3 2-6 3-10 4-1 0-2 0-3-2h4 0l4-2c2-1 5-3 6-5 2-4-1-11 0-16h1c0 4 0 9 1 13h1c1-1 2-3 3-5 1-1 2-3 3-5 1-3 0-6 0-9z" class="R"></path><path d="M408 550c2 0 4 2 6 2 0 2 0 2 2 2v2h1c1-1 1 0 2 0h0c-1 1-2 3-3 5 0 7 2 14 4 21 0 2 1 3 1 5v4h0l-12-38c-1 0-1-2-1-3z" class="j"></path><path d="M408 550c2 0 4 2 6 2 0 2 0 2 2 2v2h1c1-1 1 0 2 0h0c-1 1-2 3-3 5l-1-5c-1-1-1-2-2-2-2-1-3-1-4-1s-1-2-1-3z" class="z"></path><path d="M360 441h0c1 1 2 2 2 3h2l1 1h1l-1-1v-2c1 3 3 4 5 6h-3v1h-1l4 10c-2-3-3-6-5-9v1 1c0 1 1 1 1 2l1 1v1l12 21c-1 1-1 2-1 2l-1-1c0 1-1 1-1 1-1 1 0 3-1 3v5 1c-2-5-3-11-5-16-4-10-7-21-10-31z" class="U"></path><path d="M370 472c2 1 2 4 3 5 0 1 0 1 1 2 1-2-1-4 0-5 1 0 2 2 3 4h0c0 1-1 1-1 1-1 1 0 3-1 3v5 1c-2-5-3-11-5-16z" class="N"></path><path d="M376 424c5 0 8 0 13 3 3 2 5 3 5 7h0l1 1v2h0c-1 0-1-1-2-1l-1 1c-2-2-3-3-5-4-4-3-9-4-13-2-3 1-5 3-7 5v1 1c-1-2-2-3-2-5h0c3-5 6-7 11-9z" class="h"></path><path d="M376 424c5 0 8 0 13 3 3 2 5 3 5 7h0l1 1v2h0c-1 0-1-1-2-1-2-1-3-2-4-4-3-5-8-5-13-8z" class="J"></path><path d="M447 548c0-1 0-2 1-3 1 1 1 2 2 4 0 1 1 3 1 4v1c0 3 0 8-1 11 0 2-1 4-2 5l-3 4-1-1-2 2h0v1h-2l-1 1v-2h-1v-3l1-1 2-6c2-6 1-12-2-18l1 1 2 2v1 1h1 0l1-1-1-1c1 0 1 1 2 2l1-2v1h1v-3z" class="C"></path><path d="M439 571c1 1 0 1 1 1 4-6 4-11 4-18 1 3 0 6 2 9h0c0 1 0 2 1 3v1c-1 2-2 5-3 6l-2 2h0v1h-2l-1 1v-2h-1v-3l1-1z" class="K"></path><path d="M447 548c0-1 0-2 1-3 1 1 1 2 2 4 0 1 1 3 1 4v1c0 3 0 8-1 11 0 2-1 4-2 5l-3 4-1-1c1-1 2-4 3-6 2-6 1-12 0-19z" class="U"></path><path d="M406 521v1l1-1 2 2h1 0v-1h1c2 1 3 1 5 1 1 0 2 1 3 1 3 1 6 2 10 3 1 1 3 2 4 5l1 1c2 3 3 6 3 10-3-5-6-9-12-11-4-2-7-1-12 1l-2-1c-1 1-1 1-2 1v1l2-4-3 2c0-1 0-1-1-2 0-2-1-3-2-4l-1-1c-1-1-1-2-1-3l3-1z" class="Z"></path><defs><linearGradient id="Bw" x1="404.094" y1="525.846" x2="413" y2="525.49" xlink:href="#B"><stop offset="0" stop-color="#0d0302"></stop><stop offset="1" stop-color="#250f0d"></stop></linearGradient></defs><path fill="url(#Bw)" d="M406 521v1l1-1 2 2h1 0v-1l1 1s1 0 1 1c1 0 2 1 3 2l-1 1-3 3-3 2c0-1 0-1-1-2 0-2-1-3-2-4l-1-1c-1-1-1-2-1-3l3-1z"></path><path d="M401 447s2 1 2 2c0 2 1 4 2 6s0 6 0 7l-1 1v1c1 1 1 1 1 2 0-1 0-2 1-3v-1l1 1c0 1-1 2-1 4-2 3-5 7-9 9-3 1-8 2-12 0h0c-2-1-5-2-6-4h0c-1-1-1-2-1-4 0-3 0-5 2-8 2 1 2 1 4 1 2 1 3 2 4 4 1 1 1 2 1 3l1 4h0l-1 2h3c3-1 4-3 5-5h1v2c3-2 5-6 6-10 0-2 0-4-1-6h0c0-2 0-3-1-5-1-1-1-2-1-3z" class="l"></path><path d="M392 474c0 1-1 2-2 2-2 0-4 0-5-1 0-1 0 0 1-1h2 1 3z" class="C"></path><path d="M384 461c2 1 3 2 4 4 1 1 1 2 1 3l1 4h-1c-2 1-3 0-4 0-1-1-2-1-3-2s-2-3-2-4c1-2 2-3 4-5z" class="U"></path><path d="M385 472v-1l-1-1c-1-2-1-2-1-4l2-1c1 1 0 5 1 6h1c2-1 2-1 2-3l1 4h-1c-2 1-3 0-4 0z" class="N"></path><path d="M424 555h1 0c3-1 4-1 7 0l2 1v3c1 2 2 5 3 7-1 5-1 7-5 10-1 0-1 0-2-1l-4-2v2l1 2v1c-3-2-5-5-7-8-1-3-1-5 0-8 0-3 2-5 4-7z" class="AE"></path><path d="M426 573c-1-2-2-4-3-7v-1c-1-3-1-6 1-9h4c-2 3-3 4-3 7l1 2h0c1 4 3 7 4 10l-4-2z" class="AL"></path><path d="M424 555h1 0c3-1 4-1 7 0l2 1v3c-2-2-4-3-6-3h-4c-2 3-2 6-1 9v1c1 3 2 5 3 7v2l1 2v1c-3-2-5-5-7-8-1-3-1-5 0-8 0-3 2-5 4-7z" class="K"></path><path d="M420 562c1 1 1 3 2 5 1 3 0 6 4 8l1 2v1c-3-2-5-5-7-8-1-3-1-5 0-8z" class="AK"></path><path d="M449 598c1-2 3-4 5-5 4-1 6 0 8 1l6 5c3 2 4 5 6 8 0 3 1 6 0 9-1 2-2 4-3 5-1-4 1-8-1-12-1-1-2-1-3-2s-2-2-4-2h-1 0-1l-2 1h0c-1 1-4 4-5 4-2 0-4 0-5-1v-1l-2-2v-3h1c1 1 1 3 2 4v-1c-1-3-2-6-1-8z" class="S"></path><path d="M462 594l6 5c3 2 4 5 6 8 0 3 1 6 0 9-1 2-2 4-3 5-1-4 1-8-1-12-1-2-3-5-5-6h0c-1-1-1-2-1-3h1l1-1c-1-2-2-3-4-5z" class="N"></path><path d="M455 593h2c3 1 5 2 7 5-1 1 0 2-1 3h-1c-2 1-3 2-4 3-2 1-4-1-5-1-2-2-2-3-2-5 1-2 2-4 4-5z" class="AE"></path><path d="M477 693h1c5-3 8-9 9-14 2-8 1-17 1-25v-1 2c1 2 0 4 1 6v9c1 1 2 0 3 1l1-1c-1-1-1-4-1-5 1 5 1 10 0 16h0c0 5-2 12-7 15-3 2-7 4-10 3h-2l-1-1c-5-1-9-4-12-8v-1h3l-1-3v-1h2l3 2h1c1-1 1 0 2 0s2-1 2-2l3 1c-1 2 0 4 0 6v1h2z" class="K"></path><path d="M464 685l3 2h1c1-1 1 0 2 0s2-1 2-2l3 1c-1 2 0 4 0 6v1h2l-4 1h-3c-3 0-5-3-7-5l-1-3v-1h2z" class="S"></path><path d="M456 560h1c1-3-1-5-1-8 1 1 2 2 2 3l1 2h1c3 7 5 14 8 21 1 3 3 6 4 10l8 21h-3c1 3 1 5 0 7-1-1 0-4-1-6 0-2-1-2-2-3-2-3-3-6-6-8l-6-5c-2-1-4-2-8-1-2 1-4 3-5 5-1-1-1-2-2-2l1-1c-1-2-2-2-2-4 1-2 1-4 2-6l-2-4h-1c-2 0-3-1-4-2l6-1 1-1v-1l3-2c0-1 0-2-1-3l-2-1c1-1 2-3 2-5 1-3 1-8 1-11 0 0 1 0 1 1 0 2 0 2 1 4l1-2c1 1 2 2 2 3z" class="k"></path><path d="M448 585l1-1h0 1v-1l1 1v1h3 1 0-1c1-1 1-1 2-1s1 0 2 1c1 0 2 1 3 2h0l-1 1c-2 1-1 1-2 0-2-1-5 1-7 2-1 0-2 1-3 2v3c-1-2-2-2-2-4 1-2 1-4 2-6z" class="AR"></path><path d="M448 577c4 0 5 0 9 2 0 2-1 2 0 4 0 1 0 1-1 1s-1 0-2 1h1 0-1-3v-1l-1-1v1h-1 0l-1 1-2-4h-1c-2 0-3-1-4-2l6-1 1-1z" class="h"></path><path d="M441 579l6-1 3 2c-2 2-2 1-4 1h-1c-2 0-3-1-4-2zm16 4c2 1 4 1 6 2 1 0 3 1 4 1 1 1 1 2 2 2h0l1-1v1l1 1v-1h1l8 21h-3c1 3 1 5 0 7-1-1 0-4-1-6 0-2-1-2-2-3-2-3-3-6-6-8 1-1 1-2 1-3-2-4-5-7-8-9h0c-1-1-2-2-3-2-1-1-1-1-2-1 1 0 1 0 1-1z" class="W"></path><path d="M463 585c1 0 3 1 4 1 1 1 1 2 2 2h0l1-1v1l1 1v-1h1l8 21h-3c-3-9-7-17-14-24z" class="C"></path><path d="M456 560h1c1-3-1-5-1-8 1 1 2 2 2 3l1 2h1c3 7 5 14 8 21 1 3 3 6 4 10h-1v1l-1-1v-1l-1 1h0c-1 0-1-1-2-2-1 0-3-1-4-1-2-1-4-1-6-2-1-2 0-2 0-4-4-2-5-2-9-2v-1l3-2c0-1 0-2-1-3l-2-1c1-1 2-3 2-5 1-3 1-8 1-11 0 0 1 0 1 1 0 2 0 2 1 4l1-2c1 1 2 2 2 3z" class="K"></path><path d="M461 573c1 2 2 4 2 7 1 1 1 2 1 3h1c2 1 3 2 5 4l-1 1h0c-1 0-1-1-2-2-1 0-3-1-4-1-2-1-4-1-6-2-1-2 0-2 0-4h4l-1-2c0-2 0-3 1-4z" class="j"></path><path d="M451 554s1 0 1 1c0 2 0 2 1 4l1-2c1 1 2 2 2 3l3 8c1 2 1 3 2 5-1 1-1 2-1 4l1 2h-4c-4-2-5-2-9-2v-1l3-2c0-1 0-2-1-3l-2-1c1-1 2-3 2-5 1-3 1-8 1-11z" class="AG"></path><path d="M452 574c0 1 1 1 2 2 1 0 2-1 3-1h1v2h2l1 2h-4c-4-2-5-2-9-2v-1l3-2h1z" class="N"></path><path d="M451 554s1 0 1 1c0 2 0 2 1 4 2 3 2 7 1 11v1c-1 1-1 2-2 3h-1c0-1 0-2-1-3l-2-1c1-1 2-3 2-5 1-3 1-8 1-11zm-72-77l3 7 10 31 17 49c2 5 3 12 5 17 4 6 6 13 8 21l3 12v1 10 1 2l-2 1-1-5c-3-5-4-11-6-17l-19-53-17-50-5-16v-1-5c1 0 0-2 1-3 0 0 1 0 1-1l1 1s0-1 1-2z" class="AK"></path><path d="M379 477l3 7h-1c-1-1-2-3-2-4v2c0 1 0 2 1 3l-2-1c0 1 1 3 1 4h0c-2-2-1-4-4-6 1 0 0-2 1-3 0 0 1 0 1-1l1 1s0-1 1-2z" class="C"></path><path d="M414 581c4 6 6 13 8 21l3 12v1 10 1 2l-2 1-1-5h0c1 1 1 2 2 3h0c0-2 0-3-1-4v-1c0-5-2-10-3-15-1-1-1-3-2-5 0-1-1-3-1-5l1 2c0-1 0-2-1-2 0-2-1-2-1-3v-1-3h0l2 5c0 1 0 2 1 3v-1c0-1 0-2-1-3l-1-4c0-1 0-1-1-2v-1c-1-3-2-4-2-6z" class="j"></path><path d="M425 452v2c0 1 1 2 2 3v1l-1 1 1 1c1 2 1 5 2 7s1 4 2 5c0 2 1 5 1 7l6 13 3 6c1 2 1 5 2 7l3 6c0 3 1 6 2 9 1 1 2 3 2 4v1l-1 1 1 1h0c0 1 1 2 1 4l1 1-1 1c3 8 7 16 9 24h-1l-1-2c0-1-1-2-2-3 0 3 2 5 1 8h-1c0-1-1-2-2-3l-1 2c-1-2-1-2-1-4 0-1-1-1-1-1v-1c0-1-1-3-1-4-1-2-1-3-2-4-1 1-1 2-1 3v3h-1v-1l-1 2c-1-1-1-2-2-2l1 1-1 1h0-1v-1-1l-2-2-1-1-2-4c0-4-1-7-3-10l-1-1c-1-3-3-4-4-5-4-1-7-2-10-3-1 0-2-1-3-1-2 0-3 0-5-1h-1v1h0-1l-2-2-1 1v-1h-1-1c-2 0-5-2-7-3-1-4-2-9-4-11-1-3-1-8 0-10v-3c1-1 1-2 2-2 1-2 3-4 5-4l10-3 4-2h0c3-1 4-1 6-3 1-3 1-6 1-9l-2-9-1-3v-6l7 17 1-1v-1l-1-1v-2h-1v-2l-1-1v-4c-1-1 0-1-1-2 0-1 0 0 1-1h1 0v-2l1-1z" class="q"></path><path d="M406 494c1-1 3-1 5 0h0c4 1 6 2 10 5-3 0-7-2-10-2h-4 0l-1-1h1 0v-1h-1v-1z" class="N"></path><path d="M411 497c3 0 7 2 10 2l5 4c2 3 4 4 6 8-4-3-6-6-10-8-3-3-7-4-11-5h0v-1z" class="T"></path><path d="M421 471c3 3 3 6 4 10l-1 1c1 3 1 7 2 10 1 1 1 2 1 2h-1l-2-2h0c-2 0-3-2-5-2-1-1-2-1-3-2h4l2 1h1c-1-2-1-5-2-6 0-1 0-2-1-3h0c1-3 1-6 1-9z" class="g"></path><path d="M420 480h0c1 1 1 2 1 3 1 1 1 4 2 6h-1l-2-1h-4c1 1 2 1 3 2 2 0 3 2 5 2 0 1 1 1 1 2-4-1-6-3-10-4l-1-1c-1-1-3-1-5-1h-1c-1-1-2 0-4-1l-1 1h-3l10-3 4-2h0c3-1 4-1 6-3z" class="a"></path><path d="M404 487c4 0 9 1 12-1 1 1 2 1 4 2h-4c1 1 2 1 3 2 2 0 3 2 5 2 0 1 1 1 1 2-4-1-6-3-10-4l-1-1c-1-1-3-1-5-1h-1c-1-1-2 0-4-1z" class="J"></path><path d="M425 452v2c0 1 1 2 2 3v1l-1 1 1 1c1 2 1 5 2 7s1 4 2 5c0 2 1 5 1 7l6 13 3 6c1 2 1 5 2 7l3 6c0 3 1 6 2 9 1 1 2 3 2 4v1l-1 1 1 1h0c0 1 1 2 1 4l1 1-1 1-26-63 1-1v-1l-1-1v-2h-1v-2l-1-1v-4c-1-1 0-1-1-2 0-1 0 0 1-1h1 0v-2l1-1z" class="AM"></path><path d="M432 479l6 13 3 6c1 2 1 5 2 7l3 6c0 3 1 6 2 9h-1c-1-2-2-3-2-4 0-2-1-3-2-5h0l-1-2c0-1 0-2-1-3h0l-1-2c-1-4-2-8-4-12h-1v-2c-1-2-2-5-3-6v-1-4z" class="k"></path><path d="M400 488h3l1-1c2 1 3 0 4 1h1c2 0 4 0 5 1l1 1c4 1 6 3 10 4h0c2 2 3 4 4 6-2-1-4-3-7-3h-1c-4-2-6-3-10-3-2-1-4-1-5 0v1h1v1h0-1l1 1h0 4v1c-3 0-6-1-8 1-1 2-3 3-5 5-1 4-1 6 1 10 1 3 4 5 6 7h-1c-2 0-5-2-7-3-1-4-2-9-4-11-1-3-1-8 0-10v-3c1-1 1-2 2-2 1-2 3-4 5-4z" class="T"></path><path d="M400 488h3l1-1c2 1 3 0 4 1h1c2 0 4 0 5 1l1 1c-3 0-6-1-9 0-3 0-5 2-8 4h0c-2 0-2 0-3 1l-2 2v-3c1-1 1-2 2-2 1-2 3-4 5-4z" class="g"></path><path d="M393 494c1-1 1-2 2-2h3 1l-1 1v1h0c-2 0-2 0-3 1l-2 2v-3z" class="q"></path><path d="M393 497l2-2c1-1 1-1 3-1-2 3-4 6-5 10h1l3-5c1-1 2-1 2-2 2-1 4-3 6-3h1v1h1v1h0-1l1 1h0 4v1c-3 0-6-1-8 1-1 2-3 3-5 5-1 4-1 6 1 10 1 3 4 5 6 7h-1c-2 0-5-2-7-3-1-4-2-9-4-11-1-3-1-8 0-10z" class="K"></path><path d="M406 495h1v1h0-1l1 1h0 4v1c-3 0-6-1-8 1-1 2-3 3-5 5-1-1-1-2 0-3 0-2 5-5 8-6z" class="AI"></path><defs><linearGradient id="Bx" x1="424.452" y1="502.309" x2="417.803" y2="540.063" xlink:href="#B"><stop offset="0" stop-color="#030101"></stop><stop offset="1" stop-color="#310f0c"></stop></linearGradient></defs><path fill="url(#Bx)" d="M403 499c9 0 16 2 23 8 9 8 15 20 18 32 0 2 0 6-1 8h0l-1 2c1 0 1 1 2 1h2l-1 2c-1-1-1-2-2-2l1 1-1 1h0-1v-1-1l-2-2-1-1-2-4c0-4-1-7-3-10l-1-1c-1-3-3-4-4-5-4-1-7-2-10-3-1 0-2-1-3-1-2 0-3 0-5-1h-1v1h0-1l-2-2-1 1v-1h-1c-2-2-5-4-6-7-2-4-2-6-1-10 2-2 4-3 5-5z"></path><path d="M411 511v-2h0-1l-1 4h-1-1v-3l1-1v-1c0-1 0-2 1-3 2 1 5 1 6 3h-1c-1 1-2 1-3 3z" class="S"></path><path d="M411 511c1-2 2-2 3-3h1l3 2 1 1c0 2 0 2-2 4 0 1 0 2-2 3-1 0-2 1-3 1-2-1-2-2-3-3 1-1 1-3 1-3 1-2 1-1 1-2h0z" class="U"></path><path d="M418 510l1 1c0 2 0 2-2 4-1 1-3 3-5 3l-2-2c0-3 2-4 3-6v7c1-2 3-6 5-7z" class="N"></path><path d="M411 522c2-2 4-2 7-3 2-1 3-2 5-4v-1c0 2-2 3-1 5h0c2 1 3 2 5 3h1c2 2 4 3 5 5-1 0-1 0-2-1-1 1-2 0-3-1h-1-1c2 1 2 1 3 2-4-1-7-2-10-3-1 0-2-1-3-1-2 0-3 0-5-1z" class="s"></path><path d="M429 527c-1-1-1-1-3-2h1 1c1 1 2 2 3 1 1 1 1 1 2 1 5 6 9 12 10 20l-1 2c1 0 1 1 2 1h2l-1 2c-1-1-1-2-2-2l1 1-1 1h0-1v-1-1l-2-2-1-1-2-4c0-4-1-7-3-10l-1-1c-1-3-3-4-4-5z" class="AK"></path><defs><linearGradient id="By" x1="484.719" y1="749.531" x2="502.101" y2="650.51" xlink:href="#B"><stop offset="0" stop-color="#100504"></stop><stop offset="1" stop-color="#350d0b"></stop></linearGradient></defs><path fill="url(#By)" d="M488 632l24 66c0 2 1 6 2 8l1-1c2 3 2 6 3 9h1l1 3 1 4c0 1 1 1 2 2 2 3 3 6 4 9s1 6 3 8c-1 1-2 2-4 2-4 3-8 6-13 8l-9 5c-1 1-3 2-4 3l-3-3-2-2-1 1 2 3h0l-2-2c-2 2-3 0-5 1h-2c-1 0-3 0-4 1-1 0-2 0-3 1-1 0-1 0-2 1v-2-1c-1 0-1 1-2 1v2l-1 1h-1c1-2 1-3 2-3v-3c1-2 0-4 0-5l-5-13c-1-2-1-3-2-4 0-1-1-2-1-4l-1-2-2-7-1-4c0-1-1-3-1-4-1-1-1-1-1-2-2-4-4-9-5-14l-4-12v-1c1 2 2 9 3 9 0-1 1-2 0-3h0v-1c-1-1 1-1 1-2l-1-1v-2c0-2 0-2 1-3h0v-1c1-1 1-1 1-2h1v-1l4-4c2 0 0-1 1-2 1 1 1 2 2 3 2 1 4 1 6 2l-2 2c2 2 3 4 3 7 0 1-1 2-1 2 0 1-1 2-2 2s-1-1-2 0h-1l-3-2h-2v1l1 3h-3v1c3 4 7 7 12 8l1 1h2c3 1 7-1 10-3 5-3 7-10 7-15h0c1-6 1-11 0-16v-4c-1-2-1-4-1-5v-1c-1-1 0-2-1-3 0-1 0-2-1-3l-1-6-1-1c-1-2-1-2-1-4 2 4 4 9 6 13v6c1 2 1 3 1 4h0v-2h0v-2-3c-1-1-1-3-1-4-2-6-3-12-4-18z"></path><path d="M460 689c-1-2-2-3-2-4 2 0 3 0 4 1l1 3h-3z" class="C"></path><path d="M490 715c3 3 4 6 5 9v1c-1 1 0 1-1 1h-1c-1-2-1-3-2-4l-1-1c1-2 0-4 0-6z" class="l"></path><path d="M472 702h0c1 1 2 1 3 2 4-3 3 0 6 0 1 1 3 1 4 1h0-6-2v2c1 1 1 2 1 4h-1v-1c-1-1-1-1-1-2-1-1-1-2-2-3s-1-2-2-3zm16 46c4 3 7 3 12 2h4c-1 1-1 1-3 2h-1l-3 3-2-2c-1-1-4-2-6-3l-1-2z" class="S"></path><path d="M489 707c2 0 3 1 4 2l3 6c1 0 1 1 1 2s0 2 1 4c1 1 2 3 3 5-2 0-3-6-5-7-1-1-1-1-1-2-1-4-4-7-6-10z" class="U"></path><path d="M505 735h0c3-4 3-6 4-10 0 3-1 7-3 9l1 1 1-1c1 0 1 0 2 1 1-1 1-1 1-2v-1c1-1 1-1 1-2v-5c0 3 0 6-1 9v1c-1 3-3 6-7 7l2-2c0-1 0-1-1-1h-5c2-1 3-3 5-4z" class="l"></path><path d="M498 686v3h3c1 1 2 4 3 6h0c1 1 1 2 1 4-1 1-1 2 0 3v2c-2-5-4-8-9-10v-1c0-2 2-4 2-6v-1z" class="K"></path><path d="M498 745c-2 0-5-1-7 0v1c-5-2-8-2-12-5l-2-3h0c-1-1-2-3-3-4v-1c-1 0-1-1-1-2-1-1-2-3-2-5h0c-1-1-1-1-1-2 1-1 1-2 1-3s1-2 1-3l1-1c-1-2-1-1 0-2l1-1c1-1 2-2 3-2l1-1c1 0 1-1 2 0l2 1c-4 1-8 3-9 7l-1 3c-1 2-1 4 0 6l1 1c2 6 8 12 14 14 3 2 7 2 11 2z" class="AG"></path><path d="M514 706l1-1c2 3 2 6 3 9h1l1 3 1 4c0 1 1 1 2 2 2 3 3 6 4 9s1 6 3 8c-1 1-2 2-4 2 0-3-1-6-2-9-3-9-7-18-10-27z" class="J"></path><path d="M504 750v-1h-1 0l6-3c1-1 2-1 3-1h1l1 1 1-1v1c-1 1-2 2-2 4l-9 5c-1 1-3 2-4 3l-3-3 3-3h1c2-1 2-1 3-2z" class="b"></path><path d="M482 712c3 0 5 1 8 3h0c0 2 1 4 0 6 0-1-1-1-1-2-3-1-5-2-9-1-2 0-3 2-5 4v1 4h-1v-3-2h-1-1l1-3c1-4 5-6 9-7z" class="R"></path><path d="M475 722c0-1 0-2 2-3v-1c1-1 2-3 3-3h1c2-1 2-1 4 0s3 2 4 4c-3-1-5-2-9-1-2 0-3 2-5 4z" class="a"></path><path d="M466 672c2 1 4 1 6 2l-2 2c2 2 3 4 3 7 0 1-1 2-1 2 0 1-1 2-2 2s-1-1-2 0h-1l-3-2h-2c0-1-1-1-2-2h0-1c-1-1-1-1-1-2 1 0 1-1 1-1 0-1 0-1 1-2v-1-1c2-3 3-3 6-4z" class="b"></path><path d="M464 685v-2c2-1 3-2 5-1l1 2c-1 1-1 2-3 3l-3-2z" class="AL"></path><path d="M466 672c2 1 4 1 6 2l-2 2c-2-1-4-2-7-1-1 0-2 1-3 2v-1c2-3 3-3 6-4z" class="m"></path><path d="M476 749c1-1 2-1 3-1 0-2 0-3 1-4l3 2c1 1 4 2 5 2l1 2c2 1 5 2 6 3l-1 1 2 3h0l-2-2c-2 2-3 0-5 1h-2c-1 0-3 0-4 1-1 0-2 0-3 1-1 0-1 0-2 1v-2-1c-1 0-1 1-2 1v2l-1 1h-1c1-2 1-3 2-3v-3c1-2 0-4 0-5z" class="J"></path><path d="M478 757h1c1-1 3-2 4-3 4-1 7 0 11 1-2 2-3 0-5 1h-2c-1 0-3 0-4 1-1 0-2 0-3 1-1 0-1 0-2 1v-2z" class="h"></path><path d="M476 749c1-1 2-1 3-1 0-2 0-3 1-4l3 2c1 1 4 2 5 2l1 2h-5-4l-2 1v1 1h0l-2 4v-3c1-2 0-4 0-5z" class="U"></path><path d="M498 745c-4 0-8 0-11-2-6-2-12-8-14-14l-1-1c-1-2-1-4 0-6h1 1v2 3h1v-4c0 3 1 6 3 8 1 2 3 3 5 3h0c3 0 6-2 8-3-1 3-2 5-4 6v1c4 2 6 3 10 2 1 0 2 0 3-1h5c1 0 1 0 1 1l-2 2c-2 2-4 2-6 3z" class="g"></path><path d="M475 722c2-2 3-4 5-4 4-1 6 0 9 1 0 1 1 1 1 2l1 1c1 1 1 2 2 4h1c1 0 0 0 1-1v-1c1 2 2 4 3 5l1 1c1 1 2 1 2 3l2-1v1c1 1 1 1 1 2h1c-2 1-3 3-5 4-1 1-2 1-3 1-4 1-6 0-10-2v-1c2-1 3-3 4-6-2 1-5 3-8 3h0c-2 0-4-1-5-3-2-2-3-5-3-8v-1z" class="b"></path><path d="M491 722c1 1 1 2 2 4h1c1 0 0 0 1-1v-1c1 2 2 4 3 5l-1 2 1 1h2v2c-1 1-1 1-3 1h0c0-2-1-3-1-4-2-2-2-2-4-3h1c0-1 0-2-1-2 0-2-1-3-1-4z" class="U"></path><path d="M498 729l1 1c1 1 2 1 2 3l2-1v1c1 1 1 1 1 2h1c-2 1-3 3-5 4-1 1-2 1-3 1l1-2c0-1-1-2-2-3h1 0c2 0 2 0 3-1v-2h-2l-1-1 1-2z" class="AL"></path><path d="M518 129c1 1 1 2 1 4h0c0-1 0-2 1-2v2c1 4 3 7 5 10 2 2 4 5 6 6l1-1c0 2 0 2-1 4h3c1-1 3-1 5 0 3 2 6 6 7 10l1 1h1l11-6c7-6 18-10 27-12 5-2 11-3 16-3l1 1v2c2 1 3 1 4 1v1 2c1 0 1 1 2 2l-1 1c-7 1-13 3-19 5l1 1 11-4h6v1c1 2 3 2 6 3 1 0 2-1 3-1 2 2 4 1 7 1 0 1 0 2 1 2h3c1 1 2 1 2 2h0l1 1-1 1c12 2 22 5 32 11 1 0 3 2 4 1h1 0l-1 2c-3 5-7 11-8 16 1 1 1 2 0 4h0c0 2 1 3 1 5l1 1c0 2-1 3 0 4l4 3 4-7c2-1 3-1 4-1l1-1 3-4 2-3v1l-1 2v1c2 0 0 0 2-1h0l4-4 1-1h1c0 1 1 1 0 2l-1 1h2c0-1 1-1 1-1l1 1c1 2 3 3 5 4 1 1 2 2 2 4h-1c-1 1-1 2-2 2l-1 2v2h1l1 2 1 1-1 1 1 1c1 1 1 2 2 3-1 2-2 3-3 5-1 1-2 1-2 3v1l4-3c1 1 1 2 1 4l2-2 1 1 5-6c2 1 3 2 3 4h1 1c2 1 2 1 3 3h1c0-1 0-2 1-2v-1c1-2 0 0 1-1v-1-1c2-1 2-3 3-5h1s1-3 1-4c1 2 1 3 1 5l2-3v1c0 3-2 5-2 7 0-1 1-2 2-2 0-1 0-2 1-2v-1-3l1 3 7-20 4-17c1 3 3 4 3 7v-1l1 1v2h0c1-3 2-5 4-7 6-7 14-14 21-18 0 0 0 1-1 2-1 0-1 1-2 2-3 3-9 6-10 10 0 1-1 2-1 3l2-2v-1h2l5-3c-1 4-5 7-8 10h0l-5 6c0 2-1 5-2 7-1 1-2 2-2 4-2 2-3 6-4 9h1v2l-2 5-12 40c-3 9-22 60-21 65l1 1c0 1-1 2-2 3l1 1h1c0 1-1 2 0 4l-1 1h-1c-1-1-2 0-3 0h0l-2 3c-3 11-7 22-1 33 0 1 1 2 1 2 1 1 0 1 1 1l-1 1-3-1h-3c-2 1-4 2-5 4l-2 2c-2 2-3 6-5 9 0 0 0 1-1 1l-1 1v1h-2v1c-1 0-1 0-1 1l-1 1-1 1v1l-1-1-1 2h-1v5l-5 19c-4 8-7 19-8 28l-1 2c1 1 1 2 2 2v1l-4-2-2 8h0l-1-1c-1 0-1 0-1 1-1 2-2 4-4 5l-2 11-1 5c-1 2-1 4-2 5 0 2 0 5-1 7l-4 11-4 11-3 8c0 1-1 2-2 3v1l-1-1c-5 5-8 12-14 17h0v4c-3 3-3 7-4 11-2 5-3 9-4 13l-2 1h0c-1-1-2-2-3-4-3 0-4-2-6-4h-1c1 1 2 2 2 3l-1 1-1-1c-2 0-3 0-4 1l-1 2h1l2-2c1 0 2 0 3 1l6 6-1 6c0 1 0 1-1 2v5l-1 7h0c-2 4-4 6-6 9l-1 1c0 2 1 3 1 5h0l2 1c1-2 3-5 5-6v2l-1 8c0 2-1 5-2 7h3v1l-3 8c0 1-1 1-1 2-1-1-2-1-3-1-1 1-2 3-4 4h-1c-1 0-1 1-2 1-1 1-2 2-4 2-1 1-2 3-4 4l1-1v-1c-1 0-3 1-4 1-2 1-3 1-5 1-1 1-3 2-4 3l-2 1-2 1c-2 1-4 2-6 2-1 0-2 0-2 1 1 1 2 1 3 1l-1 2c0 1 0 2 1 3v1l-3 8 1 1c2 0 3-1 4-3 1 1 2 2 2 4h0v2 1c1 1 1 1 1 2 1 1 1 2 1 2v1c0 2 1 4 2 6 0 2-1 4-1 6-1 2-3 4-4 6h3l1-3 2-1 1 1c-1 3-3 5-5 7v1c1-1 2-1 3-2v-1c2-2 2-3 4-3 1-1 2-3 3-4 0-1 2-5 3-6v4l1 1-1 2-6 11c-1 3-3 6-5 8v1c-1 2-3 4-4 6-3 4-7 9-11 12-2 1-3 2-5 4h0l-6 5c-2 1-4 2-5 3-2-2-2-5-3-8s-2-6-4-9c-1-1-2-1-2-2l-1-4-1-3h-1c-1-3-1-6-3-9l-1 1c-1-2-2-6-2-8l-24-66-3-10c-1-1-4-11-5-13l-8-21c-1-4-3-7-4-10-3-7-5-14-8-21-2-8-6-16-9-24l1-1-1-1c0-2-1-3-1-4h0l-1-1 1-1v-1c0-1-1-3-2-4-1-3-2-6-2-9l-3-6c-1-2-1-5-2-7l-3-6-6-13c0-2-1-5-1-7-1-1-1-3-2-5s-1-5-2-7l-1-1 1-1v-1c-1-1-2-2-2-3v-2l-1-1v-3c1 1 1 3 2 4l1-1c1-1 1-2 1-4h1l1-3c0-2 1-4 1-7l3-6v5c0 1 0 2 2 3v-1l1-2c0-2 1-4 2-5 2-5 5-6 7-9l1-1h4 1 1c1 0 1 0 3-1l1 2c1 0 3-1 4-2h-3l-2-2-3-1c-1-1-1-2-2-3-3-2-5-4-7-7h0c-1-3-2-6-3-8l2-6c1 0 1 1 2 2 1-3 1-4 0-6l1-2-1-2c0-1 0-2 1-3l1-1 2 1h0c1-1-1-3-1-4-1-3-2-5-3-7l-1-1h0c-1-2-3-3-4-5l-1-5c-2-5-6-10-8-15h0c-1-3-2-4-2-7 0-1-1-2-2-2v-1c1 0 1 1 2 1l1-3c-3-8-6-15-11-22-1-3-3-6-5-8-4-4-10-5-16-5-1-1-2-2-2-3l1-1c-1 0-1 0-1-1 4 0 8 0 12-1 1 1 2 1 3 1h1l2 1h1c1 0 1-1 2-1v-1l-1-1 1-1c2-3 3-5 4-8-1 0-1 0-2-1 0-1 0-1 1-2v-4-1l-1-1v-1c2-1 3-3 5-5h1c0-1 1-2 3-3v-3h0c-1-3-1-5-3-7l-2-2 1-1c2-1 4 0 7 1 0-1 1-1 2-2h0c5-1 8-1 12 1h0 4c1 1 5 5 5 4 1 0 1 0 1-1l6-5h1l6-3h0c2-2 3-3 5-4h-1-3c-2 1-3 1-4 1h-1c-3 1-8 0-12 0-2 0-4 0-6-1l-3-3c-4-3-7-7-9-12v-6l-1-4v-6-1c1-2 2-4 4-5h1c1-1 2-1 3-1l3-1h3 5 0c4-2 7-2 10-1 6-1 11 0 16-1 1-1 1-1 1-2h1c1-1 2-1 3-1l7 8v-1c-2-4-6-7-9-10-13-15-26-25-46-30v-1l-2-2h0v-1l2-1v-1h1c2 0 3-1 5-1h2v-3h1c-1-2 1-6 1-8l1-3c4 0 8 2 12 3 7 3 14 6 20 10 4 3 8 6 13 9v-2h1l1 1h2l2-2h-1c2-1 3-2 4-3s1 0 1-1h1c0-2 0-2 1-2 0-1 0-1-1-2 1 0 2 0 3 1l1-1c2 1 3 2 5 3 1 2 1 3 2 4 0 1 0 1 1 1h0l-1-5h0c1 0 1 1 2 1 0-2 0-6 1-8 1-4 1-8 1-12l1-10z" class="AZ"></path><path d="M468 322c1 1 3 2 4 3-1 1-2 2-4 2v-1c-1 0-1-1-2-1l2-3z" class="AA"></path><path d="M497 367c-1-2-1-3-1-5 1-1 2-2 4-2l1 1h0c-1 1-1 1-1 2-1 2-2 3-3 4z" class="L"></path><path d="M576 250c1 2 1 2 1 5h0c-2 1-3 2-4 4 0-1 1-2 1-3h0l-1-2-3 2 2-3h1c1-1 1-2 3-3z" class="AX"></path><path d="M528 383c1-1 2-1 3 0l2 1c-2 1-3 1-5 3-1 1-1 1-2 1h-1c1-1 1-1 2-1-1-2-1-2 0-3l1-1h0z" class="Y"></path><path d="M430 260c1 1 1 2 2 4-1 2-3 4-4 6-1-2 0-4 0-6v-3c0 1 0 2 1 2 0-1 1-2 1-3z" class="D"></path><path d="M560 455c2-1 4-2 5-4h1c-1 3-2 6-4 8v1l-2-1v-4z" class="AF"></path><path d="M424 267h1c0-1 1-2 3-3 0 2-1 4 0 6-1 1-2 3-4 3l-1 1c1-3 1-4 1-7z" class="AA"></path><path d="M520 388h2c-1 2-2 5-4 6-2-1-3-2-4-3 1-2 2-1 4-1 0-1 1-1 2-2z" class="X"></path><path d="M431 252c0-1 1-1 2-2 0 1 1 2 1 3 0 3-2 8-2 11-1-2-1-3-2-4l1-4 1-3-1-1z" class="H"></path><path d="M425 254c1 0 2 0 3 1s2 1 3 1l-1 4c0 1-1 2-1 3-1 0-1-1-1-2h0c-1-3-1-5-3-7z" class="x"></path><path d="M626 405c1-2 2-4 5-4 0 0 1 1 2 1h0v1c-3 1-3 2-4 4l-2 3c0-1-1-4-1-5z" class="J"></path><path d="M423 252l1-1c2-1 4 0 7 1l1 1-1 3c-1 0-2 0-3-1s-2-1-3-1l-2-2z" class="AF"></path><path d="M626 405c0 1 1 4 1 5l2 5-4 1c-1-4-1-7 1-11z" class="AI"></path><path d="M602 491l1-6c2 1 3 2 4 3s2 3 3 3c1 1 3 1 4 2-2 0-2 1-3 0-2-1-2-1-4-1h-1l1-1-1-1-2 1h-2z" class="Z"></path><path d="M424 267c0 3 0 4-1 7l-2 8c-1 0-1 0-2-1 0-1 0-1 1-2v-4-1l-1-1v-1c2-1 3-3 5-5z" class="AN"></path><path d="M529 380h8 0 1 3v1c-1 2-5 3-8 3l-2-1c-1-1-2-1-3 0l1-3z" class="G"></path><path d="M457 269c2 2 3 3 3 5-3 3-6 5-10 7 2-4 5-8 7-11v-1z" class="V"></path><path d="M579 335c2 1 3 2 4 3-2 3-3 6-6 8v-2c0-1 1-1 0-2h-3c2-2 4-4 5-7z" class="F"></path><path d="M587 555v1c-2 1-5 3-5 6 0 2 1 4 2 6 0 2 1 4 2 6l-1 3c0-1-1-2-2-3-2-3-4-7-3-11s4-6 7-8z" class="T"></path><path d="M590 491v-2l1-1 2 3c3 0 6 1 9 0h2c-2 1-2 1-3 3v1l-1 1 2 2v3h0c-2-1-2-3-4-4v1c-2-3-5-7-8-7z" class="c"></path><path d="M579 331c1-2 1-4 1-6h2c2 3 3 7 2 11 0 1-1 2-1 2-1-1-2-2-4-3v-4z" class="L"></path><path d="M604 491l2-1 1 1-1 1 1 2 1 1v2h-1l-1 3h-1l-2-2h-1l-2-2 1-1v-1c1-2 1-2 3-3z" class="h"></path><path d="M603 498c2-1 2-1 4-1l-1 3h-1l-2-2z" class="AR"></path><path d="M604 491l2-1 1 1-1 1 1 2v2c-1-1-1-1-1-2-3 0-3 2-5 1v-1c1-2 1-2 3-3z" class="i"></path><path d="M451 327h1 1 1 0l1 3c0 2 0 3 1 5v2h-1-2c-1-1-1-1-3-1h0c-1-3-1-7 1-9z" class="L"></path><path d="M500 360l3-2c1 2 1 2 0 4v3l1 2c0 2-1 4-2 6-1-3-1-3-4-5l-1-1c1-1 2-2 3-4 0-1 0-1 1-2h0l-1-1z" class="n"></path><path d="M498 368v-1l1 1 1-2h2l1-1 1 2c0 2-1 4-2 6-1-3-1-3-4-5zm106-76l1-1h1c1 3 3 5 5 8 1 2 2 3 5 5v1h1c1 1 1 1 1 2h-3c-5-2-8-6-11-10 1-2 1-3 0-5z" class="D"></path><path d="M564 325v-1c1-1 2-2 3-2 1-1 5-2 7-2 3 0 6 3 8 5h-2c0 2 0 4-1 6 0 2-1 3-2 4v-1c0-1 1-3 0-5h0c0-1 0-2-1-3-1-2-4-3-6-3-3 0-3 0-5 2h-1z" class="AJ"></path><path d="M567 496l4 1c3 0 5 1 7 2-7 0-15 0-22 3h-1c-1-1-1-2-3-3 2-2 12-2 15-3z" class="B"></path><defs><linearGradient id="Bz" x1="572.313" y1="548.327" x2="569.859" y2="539.306" xlink:href="#B"><stop offset="0" stop-color="#787674"></stop><stop offset="1" stop-color="#958c8c"></stop></linearGradient></defs><path fill="url(#Bz)" d="M575 530h0c1 0 1 1 2 1v1h0c1-1 1-2 1-3 2 6 1 12-3 17-2 3-4 4-8 5-2 0-5 0-7-2 3-1 6 0 8 0 4-3 6-6 8-10v-7h0c0 2-1 3-1 5v-7z"></path><path d="M452 259c2 0 3 0 5 1 1 3 3 6 4 9 1-2 1-4 0-6 0-1 1-2 1-3 1 3 3 6 2 9 0 2-2 4-4 5 0-2-1-3-3-5-1-1-4-8-5-10z" class="n"></path><path d="M629 407c1 4 2 7 6 8 2 1 4 2 6 2s4-1 5 1l2 1c-4 1-8 2-11 2-4-1-6-3-8-6l-2-5 2-3z" class="k"></path><path d="M471 426c1 0 2 1 3 1l-2-2 1-2 12 25v1c-1 0-2 0-2 1h-2 0c-2-6-3-12-6-17-1-3-3-5-5-7h1z" class="t"></path><path d="M451 327c1-3 2-5 5-6 4-2 8-1 12 1l-2 3-2-2-2 2c-2 0-3 1-4 3l-1 2v1 1l-2-2-1-3h0-1-1-1z" class="AJ"></path><path d="M457 330h0c0-1 0-2-1-3v-1-1c2-1 2-2 4-2l1 1 1-1c1 0 3 0 4 1l-2-1-2 2c-2 0-3 1-4 3l-1 2z" class="AV"></path><path d="M585 494c3 3 6 5 9 8 1 5-1 6-3 10l-2 3c-1-1-2-2-2-3-1 2-2 3-3 4l-3-5c-1 0-1-1-1-1h0c3 1 6 1 8 0 1-2 2-2 2-4 1-2-5-9-6-11l1-1z" class="C"></path><path d="M581 511c1 0 3 0 4 1h2c-1 2-2 3-3 4l-3-5z" class="w"></path><path d="M598 498v-1c2 1 2 3 4 4h0c1 1 1 1 1 3-1 1-2 3-3 3-2 1-3 3-4 5-3 4-4 7-3 12h-1c-1-2 0-3-1-5 0-1-2-2-2-2v-2l2-3c2-4 4-5 3-10l2 2 2-1 1 1v-2h1v-1c0-1-1-2-2-3z" class="Z"></path><path d="M594 502l2 2 2-1 1 1c-1 3-4 4-6 7l-1 1h-1c2-4 4-5 3-10z" class="AO"></path><path d="M584 495l-4-5c3 0 6 1 9 1h1c3 0 6 4 8 7 1 1 2 2 2 3v1h-1v2l-1-1-2 1-2-2c-3-3-6-5-9-8l-1 1z" class="J"></path><path d="M585 494l-1-1c3 0 5 2 7 2 3 1 6 4 8 7h0v2l-1-1-2 1-2-2c-3-3-6-5-9-8z" class="k"></path><path d="M549 512c1-1 2-1 3-1l1 1c8 0 14 2 20 8 2 2 3 4 4 6l1 3c0 1 0 2-1 3h0v-1c-1 0-1-1-2-1h0c-1 0-5-6-6-7s-2-2-4-3l2-1c-1-1-2-2-4-3l-3-1c-2-1-4-2-6-2s-3 0-5-1z" class="AA"></path><path d="M567 519c1 1 3 1 4 2 2 2 4 5 6 5l1 3c0 1 0 2-1 3h0v-1c-1 0-1-1-2-1h0c-1 0-5-6-6-7s-2-2-4-3l2-1z" class="d"></path><path d="M606 492h1c2 0 2 0 4 1 1 1 1 0 3 0s4 0 6 1h1l-2 5v1c-1 1-1 3-2 4 0 2-1 3-1 5h0l-3-3-1-1-2-2c-1-1-2-2-4-2h0l-1-1h1l1-3h1v-2l-1-1-1-2z" class="c"></path><path d="M610 496h3l1 1c-1 2-2 3-2 5-1-1-1-1-2-1-1-1-2-1-4-1l1-3h1v1l2-2z" class="W"></path><path d="M606 492h1c2 0 2 0 4 1 1 1 1 0 3 0s4 0 6 1c0 1-1 2-2 3-1-1-1-1-2-1-2 0-4-1-6 0l-2 2v-1-2l-1-1-1-2z" class="J"></path><path d="M612 505c2-1 1-3 1-4s1-2 2-2l4-1v1 1c-1 1-1 3-2 4 0 2-1 3-1 5h0l-3-3-1-1z" class="W"></path><path d="M545 449c4 0 18-1 22 1h0l-1 1h-1c-1 2-3 3-5 4v4l2 1v3h0c-1 0-2-1-4-1h-3c0-1-2-3-2-3-2 1-3 3-5 4v-1c1-1 3-2 3-3 1-1 1-2 1-3 1-2 2-2 2-4h-8l2-2c-1-1-2 0-3-1z" class="AA"></path><path d="M558 462l1-2-1-1c-1-1-1-2-3-2v-1h0c1-1 2-2 3-2 1 1 1 1 2 1v4l2 1v3h0c-1 0-2-1-4-1z" class="AX"></path><path d="M558 462c2 0 3 1 4 1 6 1 11 3 16 4-1 0-1 0-1 1-6 3-13 3-18 7l-3-3 1-1c-1-1-1-1-1-2l-1-1c0-1 1-1 1-2v-1l-2-1 2-1c1 0 2 0 2-1z" class="o"></path><path d="M558 462c2 0 3 1 4 1l1 1c-1 1-3 1-3 1-1 1-2 3-3 4 2 1 4 0 6 0l-6 2c-1-1-1-1-1-2l-1-1c0-1 1-1 1-2v-1l-2-1 2-1c1 0 2 0 2-1z" class="AV"></path><path d="M563 469c3 0 10-3 14-1-6 3-13 3-18 7l-3-3 1-1 6-2z" class="B"></path><path d="M597 544h1c1 1-1 3-2 4 0 1 0 2-1 4v2c0 1-1 3-1 4l-1 1c-1 0-2 1-3 3 0 2 1 3 2 5h0c-2 0-3 0-4 1l-1 2h1l-2 4c-1-2-2-4-2-6-1-2-2-4-2-6 0-3 3-5 5-6v-1c2-1 3-2 5-3 2-2 3-5 5-8z" class="W"></path><path d="M587 556l1-1 5-3c-3 4-8 10-9 16h0c-1-2-2-4-2-6 0-3 3-5 5-6z" class="AH"></path><path d="M562 327c-2-1-2-4-3-6h0c1 1 1 1 2 1l3 3h1c2-2 2-2 5-2 2 0 5 1 6 3 1 1 1 2 1 3h0c1 2 0 4 0 5-2 2-4 4-7 4v-1h-5c2-1 5-2 6-4h-1-1c-2 0-5 0-6-1 0-2-1-3-2-4 0-1 0 0 1-1z" class="M"></path><path d="M562 327c-2-1-2-4-3-6h0c1 1 1 1 2 1l3 3h1l3 3h0-2l-1 1h-2v-1c0-1 0-1-1-1z" class="d"></path><path d="M551 480h-1l1-1c0-1 1-2 3-2h1c1-1 1-1 3-1 1 1 3 1 4 2h2v-1c2 1 5 2 7 4l1 1c-4 2-10 2-13 6l2 2-1 1-4-1c-2 0-4-2-5-4h-3v-1h3c0-1 0-1-1-1h-1l-1-1c2 0 4-2 6-2h1v-1h0-4z" class="AA"></path><path d="M558 476c1 1 3 1 4 2h2l1 1h0l-4 1c-2-1-3-1-4-2l1-2z" class="o"></path><path d="M571 481l1 1c-4 2-10 2-13 6-1 0-4-2-5-3 6-3 10-4 17-4z" class="F"></path><path d="M545 485c1 0 2-1 3-2l1 1h1c1 0 1 0 1 1h-3v1h3c1 2 3 4 5 4l4 1 1-1c3 2 8 4 10 7l-4-1c-3 1-13 1-15 3l-2 2-2 1-1-1c1-1 1-1 2-1l1-1 1-2-1-1-3 2h-1l3-3 4-4c-1-1-3-2-4-3-5 0-7 2-10 5 0-1 1-2 2-3l-1-1c-1 1-2 1-2 1h-1c2-2 5-4 8-5z" class="AJ"></path><path d="M561 490c3 2 8 4 10 7l-4-1c-3-1-7-1-10-1-1-1-2-1-2-2l1-1 1 1c2 0 3 1 4 0l-1-2 1-1z" class="AV"></path><path d="M553 491l2 1c-1 1-1 2-2 3l1 1c1 0 2 0 3-1 3 0 7 0 10 1-3 1-13 1-15 3l-2 2-2 1-1-1c1-1 1-1 2-1l1-1 1-2-1-1-3 2h-1l3-3 4-4z" class="L"></path><path d="M579 259c4-2 4-5 8-5-4 5-8 11-7 18 0 3 2 6 4 9-2 0-4-1-6-3-3-2-6-7-6-12v-4l1-3c1-2 2-3 4-4h0c1 0 1 1 2 1h1l-1 1v2z" class="X"></path><path d="M577 255h0c1 0 1 1 2 1h1l-1 1v2c-1 1-1 4-3 5-2-1-3-2-4-2l1-3c1-2 2-3 4-4z" class="x"></path><path d="M629 396c2-2 3-4 6-5 4-2 10-1 14 1 3 2 5 4 6 7 0 1-1 1-1 3v2c-2 3-2 6-5 9h-1c-1 1-2 1-3 1 2-3 4-6 4-10-1-3-3-6-5-8-3-2-5-3-8-2s-4 3-5 5h0-1c-1-1-1-1-1-3z" class="b"></path><path d="M473 242l2-1-2 4-3 4v1c0 2-1 3 0 4v5h-2l-5-4s-1 0-1-1c-1 1-1 3-1 4l1 2c0 1-1 2-1 3 1 2 1 4 0 6-1-3-3-6-4-9-2-1-3-1-5-1-2-3-4-6-7-8h4c1 1 5 5 5 4 1 0 1 0 1-1l6-5h1l6-3h0c2-2 3-3 5-4z" class="D"></path><path d="M461 258l-1-2c1-4 4-5 7-7 1 2 1 3 1 5-2 0-3 0-5 1 0 0-1 0-1-1-1 1-1 3-1 4z" class="AX"></path><path d="M473 242l2-1-2 4-3 4v1c0 2-1 3 0 4v5h-2l-5-4c2-1 3-1 5-1 0-2 0-3-1-5l1-3h0c2-2 3-3 5-4z" class="O"></path><path d="M473 242l2-1-2 4-3 4v1l-1 1v-3l1-1c-1 0-1 0-2-1h0c2-2 3-3 5-4z" class="E"></path><path d="M587 512c0 1 1 2 2 3v2s2 1 2 2c1 2 0 3 1 5h1s1 1 1 2c1 0 1 1 2 1 2 1 3 2 4 3 2 2 4 4 5 7l-3 3-2 1-1 1v-1c-2-3-7-6-10-10-1-2-2-5-3-8 0-2-1-4-2-7 1-1 2-2 3-4z" class="l"></path><path d="M600 530c2 2 4 4 5 7l-3 3-2 1c1-3 0-5 0-7v-4z" class="J"></path><path d="M588 524l1-1h1c1 1 2 2 2 4 2 4 5 6 7 9h0c-6-3-9-6-11-12z" class="Z"></path><path d="M587 512c0 1 1 2 2 3v2s2 1 2 2c1 2 0 3 1 5h1s1 1 1 2l-2 1c0-2-1-3-2-4h-1l-1 1-2-1c0-2-1-4-2-7 1-1 2-2 3-4z" class="R"></path><path d="M629 396c0-2 0-3 1-5 2-3 5-5 8-6 7-2 11 0 16 3l4 3 2 3c2-2 3-5 4-7l1 1c-1 2-2 3-2 5v2 1h1c-1 1-2 2-1 3v3h0l-1 1-2 2h-2v-1l-3-5c-1-3-3-5-6-7-4-2-10-3-14-1-3 1-4 3-6 5z" class="h"></path><path d="M649 392c1-1 1-1 2 0h2c3 1 4 3 6 6l1 2c0 1 0 3-2 4h0l-3-5c-1-3-3-5-6-7z" class="AO"></path><path d="M664 387l1 1c-1 2-2 3-2 5v2 1h1c-1 1-2 2-1 3v3h0l-1 1-2 2h-2v-1h0c2-1 2-3 2-4v-6c2-2 3-5 4-7z" class="c"></path><path d="M603 283c1-1 1 0 1-1 1-1 1-2 2-3 0 1 0 1 1 2 1-1 3-4 4-5h1l-1 1 1 1h3c-1 1-2 3-3 4v1c0 2 1 2 3 4v2l1-2h1l2 3v1c0 1-1 1-1 2s0 2 1 3c-1 1-1 2-1 3l1 1v1 1c0 2 0 3-1 5 0-1 0-1-1-2h-1v-1c-3-2-4-3-5-5-2-3-4-5-5-8h-1l-1 1c1 2 1 3 0 5-2-5-3-10-2-15l1 2v-1z" class="I"></path><path d="M604 292h0c0-2 0-5 1-7 1 2 1 4 1 6h-1l-1 1z" class="n"></path><path d="M608 286c1-4 1-7 3-9l1 1h3c-1 1-2 3-3 4 0 1-1 1-2 2l-2 2z" class="o"></path><path d="M608 286l2-2c1-1 2-1 2-2v1 3c-1 1-2 2-2 3l1 2c0 2 0 3-1 4-1-3-2-6-2-9z" class="L"></path><defs><linearGradient id="CA" x1="617.219" y1="304.188" x2="609.826" y2="293.118" xlink:href="#B"><stop offset="0" stop-color="#847d7a"></stop><stop offset="1" stop-color="#aaa09c"></stop></linearGradient></defs><path fill="url(#CA)" d="M612 283c0 2 1 2 3 4v2l1-2h1l2 3v1c0 1-1 1-1 2s0 2 1 3c-1 1-1 2-1 3l1 1v1 1c0 2 0 3-1 5 0-1 0-1-1-2h-1v-1c-3-2-4-3-5-5 0-1 0-2-1-4 1-1 1-2 1-4l-1-2c0-1 1-2 2-3v-3z"></path><path d="M612 283c0 2 1 2 3 4v2c0 1 1 3 0 4v2h-1c-2-3-2-6-2-9v-3z" class="F"></path><path d="M615 289l1-2h1l2 3v1c0 1-1 1-1 2s0 2 1 3c-1 1-1 2-1 3l1 1v1c-2-2-4-4-5-6h1v-2c1-1 0-3 0-4z" class="x"></path><path d="M569 523c1 1 5 7 6 7v7c0-2 1-3 1-5h0v7c-2 4-4 7-8 10-2 0-5-1-8 0-2-1-4-3-6-5-4 0-7 1-10 3-4 3-6 6-7 10l-2 11c-2-2-3-5-4-7v-7c1 1 1 2 2 3 1-2 2-4 3-7h-1l1-1c2-2 3-5 7-7v1l2-1c3-1 6-1 10-2h1v-2h1c1 1 1 2 2 3s3 1 4 2c1 0 5 0 7-1 1 0 1-1 2-2 0-2 1-4 1-5 0-5-1-7-5-10l1-2z" class="G"></path><path d="M536 550c1-1 2-2 4-3v1h0c-2 1-4 5-4 7l1 2-2 11c-2-2-3-5-4-7v-7c1 1 1 2 2 3 1-2 2-4 3-7z" class="B"></path><path d="M569 523c1 1 5 7 6 7v7c-1 4-2 7-5 9-2 1-5 1-7 1-3-1-5-3-7-5v-2-2h1c1 1 1 2 2 3s3 1 4 2c1 0 5 0 7-1 1 0 1-1 2-2 0-2 1-4 1-5 0-5-1-7-5-10l1-2z" class="AV"></path><path d="M515 372l3 3c0 1 1 2 1 3h1c1 0 3 0 4-1l2 1 3 2-1 3h0l-1 1c-1 1-1 1 0 3-1 0-1 0-2 1l-2-1-1 1h-2c-1 1-2 1-2 2-2 0-3-1-4 1-1-2-1-3-1-4l-1-1c-1 0-2 1-3 2l-6-3c-3-1-9-1-11-4l1-1 3-1c3 0 3-1 5-3l1 1c3-1 4-2 7-4l2-2v1c-1 2-2 3-2 5v1l6-6z" class="D"></path><path d="M514 378l3 1v2 1c0 1 0 1-1 2v-2h0c-1-2-3-2-5-2l3-2z" class="G"></path><path d="M513 387h1v-2c1 1 1 2 3 3v-2c1 0 1 1 2 2h1c-1 1-2 1-2 2-2 0-3-1-4 1-1-2-1-3-1-4z" class="I"></path><path d="M515 372l3 3c-1 1-3 2-4 3l-3 2c-4 2-7 2-12 2v-1h4l6-3 6-6z" class="F"></path><path d="M511 371v1c-1 2-2 3-2 5v1l-6 3-10-1 3-1c3 0 3-1 5-3l1 1c3-1 4-2 7-4l2-2z" class="o"></path><path d="M518 375c0 1 1 2 1 3h1c1 0 3 0 4-1l2 1 3 2-1 3h0l-1 1v-1c-3 0-5 1-7 0s-2-1-3-1v-1-2l-3-1c1-1 3-2 4-3z" class="f"></path><path d="M517 381c2-1 3-1 5-1s3 1 4 2l1 1c-3 0-5 1-7 0s-2-1-3-1v-1z" class="V"></path><defs><linearGradient id="CB" x1="580.367" y1="270.281" x2="540.368" y2="294.277" xlink:href="#B"><stop offset="0" stop-color="#564d4a"></stop><stop offset="1" stop-color="#808081"></stop></linearGradient></defs><path fill="url(#CB)" d="M556 268c2-2 4-5 6-7 1-2 4-5 5-8 0-1-1-1-2-2h1l3 1c1 0 2 1 3 1l-2 3c-11 12-15 25-17 41 0 4 1 7 1 11l1 1c1 5 3 9 6 13-1 0-1 0-2-1h0c1 2 1 5 3 6-1 1-1 0-1 1 1 1 2 2 2 4-3-2-5-4-7-6-1-2-2-5-3-7-5-11-4-21-3-32 1-2 1-4 2-6 1-5 3-9 4-13z"></path><defs><linearGradient id="CC" x1="548.404" y1="309.973" x2="563.27" y2="318.122" xlink:href="#B"><stop offset="0" stop-color="#8d8784"></stop><stop offset="1" stop-color="#a8a4a2"></stop></linearGradient></defs><path fill="url(#CC)" d="M561 328l-5-5c-2-5-4-11-4-17v-3c1-2 1-4 1-6 0 4 1 7 1 11l1 1c1 5 3 9 6 13-1 0-1 0-2-1h0c1 2 1 5 3 6-1 1-1 0-1 1z"></path><path d="M625 416l4-1c2 3 4 5 8 6 3 0 7-1 11-2 1-1 2-1 3-2l2-1h1l1 2-1 5v2c-1 6-3 14-9 17l-1 1-2 1h-6l-1-2 4-13c0-1-3-1-4-2-5-2-8-6-10-11z" class="AE"></path><path d="M635 442l4-13 1 5c1 2 1 3 0 5l1 1c2-2 3-6 5-9v1c0 3-3 8-2 11l-2 1h-6l-1-2z" class="T"></path><path d="M516 348l1 2h1c0 3 0 8 1 10 1-1 1-1 1-3l1-3v2l1 3 1 1s1-1 2-1c0 1 0 2 1 3h0c0-3 1-7 3-10 1 1 1 1 2 1h0c1 1 1 2 1 3l1 1c1 2 3 3 5 5 1 0 3 0 3 1-1 2-4 4-5 6s-1 5-1 7v2l3 2h-1 0-8l-3-2-2-1c-1 1-3 1-4 1h-1c0-1-1-2-1-3l-3-3-6 6v-1c0-2 1-3 2-5v-1c0-1 3-4 3-5 2-2 2-5 2-8l-1-2c1 1 1 1 2 0l-1-8z" class="X"></path><path d="M528 364l2 2v1h0l-2 2v-1-4z" class="u"></path><path d="M517 364c2 6 3 10 7 13-1 1-3 1-4 1h-1c0-1-1-2-1-3l-3-3c1-3 2-5 2-8z" class="E"></path><path d="M515 356c1 1 1 1 2 0 0 3-1 6 0 8 0 3-1 5-2 8l-6 6v-1c0-2 1-3 2-5v-1c0-1 3-4 3-5 2-2 2-5 2-8l-1-2z" class="L"></path><path d="M526 378c0-1 0-2-1-3s-1-2-1-4c1-1 2 1 3 1v-3l1-1v1c0 2 2 5 4 6 1 1 0 1 1 1h1 1v2l3 2h-1 0-8l-3-2z" class="AA"></path><defs><linearGradient id="CD" x1="532.786" y1="357.099" x2="528.27" y2="364.435" xlink:href="#B"><stop offset="0" stop-color="#777474"></stop><stop offset="1" stop-color="#8f8b8a"></stop></linearGradient></defs><path fill="url(#CD)" d="M521 356l1 3 1 1s1-1 2-1c0 1 0 2 1 3h0c0-3 1-7 3-10 1 1 1 1 2 1h0c1 1 1 2 1 3v5l3 3c-1 0-1 0-2 1-1 0-1 0-1 1h0l-2 1v-1l-2-2v-4h-1v3c0 1 0 2-1 3-2-1-1-2-2-4v1c-1 0-2 0-3-1v-2c-1-1-1-2-1-3l1-3v2z"></path><path d="M532 356l1 1c1 2 3 3 5 5 1 0 3 0 3 1-1 2-4 4-5 6s-1 5-1 7h-1-1c-1 0 0 0-1-1-2-1-4-4-4-6l2-2h0l2-1h0c0-1 0-1 1-1 1-1 1-1 2-1l-3-3v-5z" class="n"></path><path d="M530 367l2-1 1 3c1 2 0 4 0 6v1c-1 0 0 0-1-1-2-1-4-4-4-6l2-2h0z" class="H"></path><path d="M530 367l2-1 1 3c0 1 0 2-1 3h0c-1-2-2-3-2-5h0z" class="Y"></path><path d="M481 425v-1l1-1c3 0 5 2 7 3v1c3 1 5 4 8 5h0l2 2h1l1 2 2 2h2c1 2 1 4 2 6 0 1 1 3 1 5 0 3-1 6 0 9v9c-1 2-1 4 0 6h1v3c-1-1-2-2-3-2h0-1c-2-1-3-1-3-2l-2-2c-1 0-1 0-2 1l3 2c-2 0-5 0-7 1v-1l4-5v-1l2-2c0-1-1-1 0-2v-1l-1-1c-1-1-1-1-1-2h1v-1c0-2 0-2-2-3 0-2 0-4-1-6-2-6-7-12-11-17-1-3-3-5-4-7z" class="I"></path><path d="M503 449c2 5 3 11 1 16v1l-1 1c0 1 0 1-1 2v3l-2-2c-1 0-1 0-2 1l3 2c-2 0-5 0-7 1v-1l4-5 3-3c3-4 1-8 0-12 2 2 2 5 2 8h0c1-2 1-3 1-4-1-2-1-3-1-5v-3z" class="Q"></path><path d="M488 429c0-1 0-1-1-2h0 1l2 2h1c3 2 4 5 6 8s5 7 6 11v1 3c0 2 0 3 1 5 0 1 0 2-1 4h0c0-3 0-6-2-8-1-4-3-8-5-12s-5-8-8-12z" class="AA"></path><defs><linearGradient id="CE" x1="497.046" y1="440.557" x2="490.624" y2="441.812" xlink:href="#B"><stop offset="0" stop-color="#7e7a7c"></stop><stop offset="1" stop-color="#94918e"></stop></linearGradient></defs><path fill="url(#CE)" d="M481 425v-1l1-1c3 0 5 2 7 3v1l2 2h-1l-2-2h-1 0c1 1 1 1 1 2 3 4 6 8 8 12s4 8 5 12 3 8 0 12l-3 3v-1l2-2c0-1-1-1 0-2v-1l-1-1c-1-1-1-1-1-2h1v-1c0-2 0-2-2-3 0-2 0-4-1-6-2-6-7-12-11-17-1-3-3-5-4-7z"></path><path d="M481 425v-1l1-1c3 0 5 2 7 3v1l2 2h-1l-2-2h-1 0c1 1 1 1 1 2l-4-3h0l3 4h-1c-1 0-1-1-2-2v-1c-1-1-2-2-3-2z" class="AJ"></path><path d="M602 498h1l2 2 1 1h0c2 0 3 1 4 2l2 2 1 1 3 3h0c0 2 0 3-1 5 1 1 1 2 2 3 0 1 0 2-1 3v4c-1 2-2 4-3 5h-1 0c0-1 0-1-1-1h0c-1-1-1-2-2-3l-1 5-3 7h0c-1-3-3-5-5-7-1-1-2-2-4-3-1 0-1-1-2-1 0-1-1-2-1-2-1-5 0-8 3-12 1-2 2-4 4-5 1 0 2-2 3-3 0-2 0-2-1-3v-3z" class="AE"></path><path d="M602 498h1l2 2 1 1h0c2 0 3 1 4 2l2 2 1 1 3 3h0c0 2 0 3-1 5 1 1 1 2 2 3 0 1 0 2-1 3-1-3-1-6-4-8-1-2-4-4-7-4-4 0-6 2-9 4 1-2 2-4 4-5 1 0 2-2 3-3 0-2 0-2-1-3v-3z" class="AM"></path><path d="M602 498h1l2 2 1 1h0c2 0 3 1 4 2l2 2 1 1c-3-1-6-2-10-2 0-2 0-2-1-3v-3z" class="AO"></path><path d="M596 527v-1c0-2 0-3 1-5 1-1 2-2 4-3 1 0 3 0 4 1 3 1 3 3 4 6l-1 5-3 7h0c-1-3-3-5-5-7-1-1-2-2-4-3z" class="AZ"></path><path d="M590 568c1 0 2 0 3 1l6 6-1 6c0 1 0 1-1 2v5l-1 7h0c-2 4-4 6-6 9l-1 1c-3 3-5 5-8 5h-1 0c0 1-1 2-1 2-1 1-1-1-3 1l-3-9c0-1 0-3 1-4 1-5 4-8 9-11 2 0 3-1 5 0l2 1v-3c0-4-2-8-5-10l1-3 2-4 2-2z" class="b"></path><path d="M590 568c1 0 2 0 3 1l6 6-1 6c0 1 0 1-1 2 0-2 0-3-1-5-1-3-3-8-6-10z" class="T"></path><path d="M583 595c2-1 3-2 5-2l1 1c0 4-4 11-7 13-1 1-1 0-2 0l-1-6c1-3 2-4 4-6z" class="AZ"></path><path d="M583 589c2 0 3-1 5 0l2 1v2h-2c-2 0-4 1-5 2v1c-2 2-3 3-4 6l1 6v3h0c0 1-1 2-1 2-1 1-1-1-3 1l-3-9c0-1 0-3 1-4 1-5 4-8 9-11z" class="c"></path><path d="M583 589c2 0 3-1 5 0l2 1v2h-2c-2 0-4 1-5 2v1c-2 2-3 3-4 6v-1c-1-1 0-4 1-4 1-3 2-4 3-7z" class="i"></path><path d="M617 517c1 1 2 1 2 3 1 2 0 5-1 7v2l1 1c-2 4-8 8-7 13h0v4c-3 3-3 7-4 11-2 5-3 9-4 13l-2 1h0c-1-1-2-2-3-4-3 0-4-2-6-4h-1c1 1 2 2 2 3l-1 1-1-1h0c-1-2-2-3-2-5 1-2 2-3 3-3l1-1c0-1 1-3 1-4v-2c1-2 1-3 1-4 1-1 3-3 2-4h-1c0-1 1-2 2-3v1l1-1 2-1 3-3h0l3-7 1-5c1 1 1 2 2 3h0c1 0 1 0 1 1h0 1c1-1 2-3 3-5v-4c1-1 1-2 1-3z" class="N"></path><path d="M608 530c1 2 1 3 1 5-1 2-3 7-5 8h-1c1-2 2-4 2-6l3-7z" class="AK"></path><path d="M610 538c0 3-1 5-1 7-4 4-8 9-10 14-1-3 1-5 2-8 1-4 6-9 9-13z" class="k"></path><path d="M617 517c1 1 2 1 2 3 1 2 0 5-1 7v2l1 1c-2 4-8 8-7 13h0l-3 2c0-2 1-4 1-7 3-5 5-8 6-14v-4c1-1 1-2 1-3z" class="a"></path><path d="M600 541l2-1 3-3h0c0 2-1 4-2 6-2 4-4 7-6 11-1 2-1 5-1 7s2 5 3 7c-3 0-4-2-6-4h-1c1 1 2 2 2 3l-1 1-1-1h0c-1-2-2-3-2-5 1-2 2-3 3-3l1-1c0-1 1-3 1-4v-2c1-2 1-3 1-4 1-1 3-3 2-4h-1c0-1 1-2 2-3v1l1-1z" class="c"></path><path d="M612 543v4c-3 3-3 7-4 11-2 5-3 9-4 13l-2 1v-1-3h0c-2-2-2-2-2-4-1-1-1-2-2-3 0 0 1-1 1-2h0c2-5 6-10 10-14l3-2z" class="AE"></path><path d="M573 604l3 9c2-2 2 0 3-1 0 0 1-1 1-2h0 1c3 0 5-2 8-5 0 2 1 3 1 5h0l2 1c1-2 3-5 5-6v2l-1 8c0 2-1 5-2 7h3v1l-3 8c0 1-1 1-1 2-1-1-2-1-3-1-1 1-2 3-4 4h-1c-1 0-1 1-2 1-1 1-2 2-4 2-1 1-2 3-4 4l1-1v-1c-1 0-3 1-4 1-2 1-3 1-5 1 1-1 3-2 4-3 2 0 5-4 6-6-2 0-3 1-4 0 3-4 5-8 6-13l-4-4c-3-3-3-8-2-13z" class="S"></path><path d="M589 605c0 2 1 3 1 5-2 3-5 5-8 5-2 0-4-1-5-2h-1c2-2 2 0 3-1 0 0 1-1 1-2h0 1c3 0 5-2 8-5zm-10 25c1-4 4-9 6-12l-3 11c2-2 3-5 5-6 0-1 2-1 3-1-1 3-3 9-6 11h-2c-2 2-3 4-5 6 1-2 3-5 3-8l-1-1z" class="w"></path><path d="M590 621l1 1c-1 4-3 7-5 10v1c-2 2-5 3-7 6-1 1-2 3-4 4l1-1v-1c-1 0-3 1-4 1-2 1-3 1-5 1 1-1 3-2 4-3 2 0 5-4 6-6l2-4 1 1c0 3-2 6-3 8 2-2 3-4 5-6h2c3-2 5-8 6-11v-1z" class="k"></path><path d="M592 611c1-2 3-5 5-6v2l-1 8c0 2-1 5-2 7h3v1l-3 8c0 1-1 1-1 2-1-1-2-1-3-1-1 1-2 3-4 4h-1c-1 0-1 1-2 1-1 1-2 2-4 2 2-3 5-4 7-6v-1c2-3 4-6 5-10l-1-1c0-4 1-7 2-10z" class="a"></path><path d="M594 622h3v1l-3 8c0 1-1 1-1 2-1-1-2-1-3-1 0-1 1-2 2-3 1-2 2-5 2-7z" class="q"></path><path d="M592 611c1-2 3-5 5-6v2l-1 8s-1 0-1 1c-1 0 0 2-2 2v-2h-1c0 2 0 4-1 6l-1-1c0-4 1-7 2-10z" class="m"></path><path d="M666 400c1-1 2-2 4-3v5l-5 19c-4 8-7 19-8 28l-1 2c1 1 1 2 2 2v1l-4-2-4-1c-5-2-11-6-16-7l1-2 1 2h6l2-1 1-1c6-3 8-11 9-17v-2l1-5-1-2h-1l-2 1c-1 1-2 1-3 2l-2-1c-1-2-3-1-5-1l4-3c1 0 2 0 3-1h1c3-3 3-6 5-9v-2c0-2 1-2 1-3l3 5v1h2l2-2 1-1c1-1 1-2 3-2z" class="AR"></path><path d="M642 444s1 1 2 1v-1c1 0 2-1 2-1h1c0 1 0 1 1 1v1 2l1 1v1c-2 0-3-2-4-2v-1h-2-2c-1-1-3-2-5-2h6z" class="c"></path><path d="M652 441h1l1 1v2h1l-2 6h-1l-3-1v-1c0-2 2-5 3-7z" class="h"></path><path d="M656 431l1 1c0-1 0-1 1-2l1-4 1-4 1-1v1 1l-1 1-1 4-2 6v2l-2 8h-1v-2l-1-1h-1c1-3 2-7 4-10z" class="W"></path><path d="M666 400c1-1 2-2 4-3v5c-1-1-1-2-1-3-2 2-3 5-4 7-3 6-5 13-8 19l1-6 1-4c1-5 3-7 3-12l1-1c1-1 1-2 3-2z" class="J"></path><path d="M654 425l4-6-1 6-1 6c-2 3-3 7-4 10-1 2-3 5-3 7l-1-1v-2-1c-1 0-1 0-1-1h-1s-1 1-2 1v1c-1 0-2-1-2-1l2-1 1-1c6-3 8-11 9-17z" class="R"></path><path d="M655 399l3 5v1h2l2-2c0 5-2 7-3 12l-1 4-4 6v-2l1-5-1-2h-1l-2 1c-1 1-2 1-3 2l-2-1c-1-2-3-1-5-1l4-3c1 0 2 0 3-1h1c3-3 3-6 5-9v-2c0-2 1-2 1-3z" class="j"></path><path d="M651 417c0-1 0-2 1-3s1-2 2-4c0-1 1-3 2-5v1c1 0 1 1 2 1 0 1-1 6-2 7l-1 4-1-2h-1l-2 1z" class="U"></path><defs><linearGradient id="CF" x1="551.005" y1="338.45" x2="549.982" y2="357.796" xlink:href="#B"><stop offset="0" stop-color="#0f0f10"></stop><stop offset="1" stop-color="#373536"></stop></linearGradient></defs><path fill="url(#CF)" d="M525 335l5-1h3 1 3c2 1 2 1 4 3l5 1c5 4 13 8 20 7h2 0l6-3h3c1 1 0 1 0 2v2c-7 6-14 6-23 5-5-1-16-6-20-3-2 1-2 2-2 3l-1 2h0c-1 0-1 0-2-1-2 3-3 7-3 10h0c-1-1-1-2-1-3-1 0-2 1-2 1l-1-1-1-3v-2l-1 3c0 2 0 2-1 3-1-2-1-7-1-10v-3c1-3 2-5 3-7s3-3 4-5z"></path><path d="M523 348c1 2 1 3 1 5l1 1-1 1v1h-3v-2c0-2 1-4 2-6z" class="F"></path><path d="M525 335l5-1h3 1 3c2 1 2 1 4 3-5 1-9 2-13 5-2 2-3 4-5 6-1 2-2 4-2 6l-1 3c0 2 0 2-1 3-1-2-1-7-1-10v-3c1-3 2-5 3-7s3-3 4-5z" class="Q"></path><path d="M525 335l5-1h3 1 3c2 1 2 1 4 3-5 1-9 2-13 5l-1-1 5-4v-1h-5c-2 1-3 3-5 4h-1c1-2 3-3 4-5z" class="I"></path><path d="M611 224v5h1v4l3 1h1 1l1 1c0 1 1 2 2 3 2-1 3-1 4-1s3 0 4-1h2 0c2 1 3 1 5 1v2l-3 2h0c-3 1-4 2-7 2-1 0-2 1-2 1h-1l-1 1-2-1h-4v1c1 1 1 2 2 3v1h1 1c-2 1-3 1-4 1-2 0-5 0-7-1l-2 1c0 1 0 2 1 3l-2 2c0-2-1-3-2-5h-10c-2 1-4 2-6 4-4 0-4 3-8 5v-2l1-1h-1c-1 0-1-1-2-1h0 0c0-3 0-3-1-5-2 1-2 2-3 3h-1c-1 0-2-1-3-1l1-4v-3c-2-2-4-3-6-5v-1c1 0 1 0 2 1 1 0 2 1 3 0 2 1 3 1 5 2l-2 1c1 0 2 1 3 1h1 2 4s13-3 14-3l4-4c1 1 2 1 3 1 0 1 0 1-1 2 1 0 1 0 2-1v1h1l1-1c2-1 3-2 5-3-1-4-1-8 0-11v-1z" class="AJ"></path><path d="M576 250c2-2 8 0 11 0v1c-3 1-7 1-10 4 0-3 0-3-1-5z" class="AV"></path><path d="M606 246h2l1-1h1 5c1 1 1 2 2 3v1h1 1c-2 1-3 1-4 1-2 0-5 0-7-1h1c1 0 1 0 2-1v-1c0-1 0-1-1-1h-3-1 0z" class="Q"></path><path d="M587 251c2 0 4-1 6-1-2 1-4 2-6 4-4 0-4 3-8 5v-2l1-1h-1c-1 0-1-1-2-1h0 0c3-3 7-3 10-4z" class="Y"></path><path d="M564 239c1 0 1 0 2 1 1 0 2 1 3 0 2 1 3 1 5 2l-2 1c1 0 2 1 3 1h1 2 4 1l-4 1h0l17 1c1 0 2 0 2-1h6c0 1 1 1 2 1h0c-1 0-1 0-2 1h0c-3 0-5 1-8 1-6 1-17 1-22-2-2-1-3-1-4-1-2-2-4-3-6-5v-1z" class="L"></path><path d="M566 240c1 0 2 1 3 0 2 1 3 1 5 2l-2 1h0c-2 0-4-1-6-3z" class="M"></path><path d="M611 224v5h1v4l3 1h1 1l1 1c0 1 1 2 2 3 2-1 3-1 4-1s3 0 4-1h2 0c2 1 3 1 5 1v2l-3 2h0c-3 1-4 2-7 2-1 0-2 1-2 1h-1l-1 1-2-1h-4v1h-5-1l-1 1h-2c-1 0-2 0-2-1h-6c0 1-1 1-2 1l-17-1h0l4-1h-1s13-3 14-3l4-4c1 1 2 1 3 1 0 1 0 1-1 2 1 0 1 0 2-1v1h1l1-1c2-1 3-2 5-3-1-4-1-8 0-11v-1z" class="X"></path><path d="M600 237c1 1 2 1 3 1 0 1 0 1-1 2 1 0 1 0 2-1v1h1l2 2h-1c0 1 0 1-1 0l-2-1h-1 0l-2 1-4-1 4-4z" class="Q"></path><path d="M619 244l2-1c1 0 1-1 2-1h2l1-1c2 0 3 0 4-2l1-1h3l-2 3h0c-3 1-4 2-7 2-1 0-2 1-2 1h-1l-1 1-2-1z" class="M"></path><path d="M611 224v5h1v4l2 4c0 1 1 2 1 3l1 1c-1 1-2 2-4 2s-4-2-6-4c2-1 3-2 5-3-1-4-1-8 0-11v-1z" class="f"></path><path d="M615 240c-1 1-3 0-5 0v-1l4-2c0 1 1 2 1 3z" class="O"></path><path d="M549 488c1 1 3 2 4 3l-4 4-3 3h1l3-2 1 1-1 2-1 1c-1 0-1 0-2 1l1 1 2-1 2-2c2 1 2 2 3 3h1l1 2-1 1c1 1 1 1 2 1h2 1v1h-1c-2 1-5 3-8 4-1 0-2 0-3 1l-2 1c-5 2-11 6-13 10 0 2-1 2 0 4h0v1c-2 2-3 4-4 7h0l-1 5c-1-1-1-2-2-2v9h0c-1-1-1-3-1-5l1-21c-1-1 0-6-1-8 0-3 1-6 1-8 1-1 2-2 2-3v-1c1-2 1-3 1-5 1-1 2-3 4-3l3-3h1s1 0 2-1l1 1c-1 1-2 2-2 3 3-3 5-5 10-5z" class="d"></path><path d="M552 499c2 1 2 2 3 3h1l1 2-1 1c1 1 1 1 2 1h2 1v1h-1c-2-1-5 0-7 0h-2-3-1c1-1 2-1 3-2v-4l2-2z" class="o"></path><path d="M552 499c2 1 2 2 3 3-1 1-3 2-5 3v-4l2-2z" class="E"></path><path d="M528 527c1-8 2-15 6-21-1 2-1 5-1 7-1 2-2 3-2 5 0 3-3 8-1 11 1 0 1-1 1-1 1-2 2-4 2-6 2-2 5-7 8-8l1-1c1 0 3-1 4-2l2-1h0c1-1 1-2 3-3h2c2 0 5-1 7 0-2 1-5 3-8 4-1 0-2 0-3 1l-2 1c-5 2-11 6-13 10 0 2-1 2 0 4h0v1c-2 2-3 4-4 7h0v-3h-1c-1-2-1-3 0-5h-1z" class="x"></path><path d="M549 488c1 1 3 2 4 3l-4 4-3 3h1l3-2 1 1-1 2-1 1c-1 0-1 0-2 1-2 2-5 3-7 6v1c-2 2-3 4-4 5-2 2-2 4-3 6 2-2 4-5 6-7 2-3 5-4 7-7l1 1c-1 1-2 1-3 2l-3 2c-2 3-4 6-6 8-2 1-2 2-2 4h0c0 2-1 4-2 6 0 0 0 1-1 1-2-3 1-8 1-11 0-2 1-3 2-5 0-2 0-5 1-7 2-5 6-10 10-14 2-1 3-2 5-3v-1z" class="Y"></path><path d="M536 513v-4h1v-1c2-5 7-11 12-13l-3 3h1l3-2 1 1-1 2-1 1c-1 0-1 0-2 1-2 2-5 3-7 6v1c-2 2-3 4-4 5z" class="X"></path><path d="M539 493c3-3 5-5 10-5v1c-2 1-3 2-5 3-4 4-8 9-10 14-4 6-5 13-6 21h1c-1 2-1 3 0 5h1v3l-1 5c-1-1-1-2-2-2v9h0c-1-1-1-3-1-5l1-21c-1-1 0-6-1-8 0-3 1-6 1-8 1-1 2-2 2-3v-1c1-2 1-3 1-5 1-1 2-3 4-3l3-3h1s1 0 2-1l1 1c-1 1-2 2-2 3z" class="O"></path><path d="M528 527h1c-1 2-1 3 0 5h1v3l-1 5c-1-1-1-2-2-2l1-4v-7z" class="G"></path><path d="M537 490h1s1 0 2-1l1 1c-1 1-2 2-2 3-3 3-5 7-7 11l-5 17c-1-1 0-6-1-8 0-3 1-6 1-8 1-1 2-2 2-3v-1c1-2 1-3 1-5 1-1 2-3 4-3l3-3z" class="L"></path><path d="M530 496c1-1 2-3 4-3-2 2-4 7-4 10v2s-1 1-1 2c-1 1-1 2 0 3h0 0c0-3 1-5 3-6l-5 17c-1-1 0-6-1-8 0-3 1-6 1-8 1-1 2-2 2-3v-1c1-2 1-3 1-5z" class="u"></path><path d="M502 333c3 1 4 2 6 4 5 3 7 6 8 11l1 8c-1 1-1 1-2 0l1 2c0 3 0 6-2 8 0 1-3 4-3 5l-2 2c-3 2-4 3-7 4l-1-1 1-3c1-2 2-4 2-6l-1-2v-3c1-2 1-2 0-4 1-2 1-4 1-6v-2c-1-1-2-2-3-2-2-1-5-1-7 0-6 1-12 3-18 3-4 1-8 0-12-1-7-3-11-7-14-14 2 0 2 0 3 1h2 1c4 4 7 7 13 8 8 1 15-3 21-7h0c3 0 6-1 9 0 0-1 1-1 1-2 0 0 0-1 1-2l1-1z" class="S"></path><path d="M502 333c3 1 4 2 6 4 5 3 7 6 8 11l1 8c-1 1-1 1-2 0v-2c-3-6-5-10-11-14l-5-2c0-1 1-1 1-2 0 0 0-1 1-2l1-1z" class="Q"></path><path d="M502 333c3 1 4 2 6 4h-3c-1 1-1 1-1 2v1l-5-2c0-1 1-1 1-2 0 0 0-1 1-2l1-1z" class="L"></path><path d="M504 352l2 1h1l1-1v2c1 0 0 1 0 2 1 0 1-1 1-2 1 1 2 3 2 5h1c1-1 1-2 2-3l1-2v2l1 2c0 3 0 6-2 8 0 1-3 4-3 5l-2 2c-3 2-4 3-7 4l-1-1 1-3c1-2 2-4 2-6l-1-2v-3c1-2 1-2 0-4 1-2 1-4 1-6z" class="Y"></path><path d="M516 358c0 3 0 6-2 8 0 1-3 4-3 5l-2 2c-3 2-4 3-7 4l-1-1 1-3c1-2 2-4 2-6 1-2 2-4 4-6l1 8c1-2 1-4 3-6l1 1c0-1 1-1 1-2s1-2 1-3l1-1z" class="I"></path><path d="M541 430c2-3 5-6 9-7h0v1h1 1c0 1-1 3-2 4-4 7-10 14-13 23 1 0 2 2 3 2l1-1-1-1c1-2 3-1 5-2 1 1 2 0 3 1l-2 2h8c0 2-1 2-2 4 0 1 0 2-1 3 0 1-2 2-3 3v1c2-1 3-3 5-4 0 0 2 2 2 3h3c0 1-1 1-2 1l-2 1 2 1v1c0 1-1 1-1 2l1 1c0 1 0 1 1 2l-1 1 3 3c2 1 3 2 5 2v1h-2c-1-1-3-1-4-2-2 0-2 0-3 1h-1c-2 0-3 1-3 2l-1 1h1c-1 1-2 1-3 1l1-2v-2l-1-1h-1c-6-1-13-2-18 1l-1 1-1 1h0c-1-1-1-1-1-2h0c-1-4 0-10-1-13h-1c-1-3 1-26 1-32h2 1c3 0 5 1 8 0h0c2 0 3-1 5-2z" class="AJ"></path><path d="M530 456c1 4 1 7 3 11l4 4 1 1c-1 0-2 0-4-1l-2-2h0c-2-4-2-8-2-13zm8 14h5v-1c4-1 6-2 9-3l1-1c1 1 1 1 2 0v1h0l-2 2 1 1c-1 0-2 1-3 2 1 0 2-1 3-1 1 1 1 2 1 3v1c-1 1-3 0-4 1-1 0-1-1-2-1-1-1-3-1-5-1l-1-1c-1-1-2-1-4-2h-1z" class="o"></path><path d="M551 424h1c0 1-1 3-2 4-4 7-10 14-13 23-1 0-1 1-2 2h0c-1 2-1 4-1 6l-1 1v-5c1-5 3-11 7-16 2-3 4-6 7-9 1-2 3-4 4-6z" class="Y"></path><path d="M548 463c2-1 3-3 5-4 0 0 2 2 2 3h3c0 1-1 1-2 1l-2 1-1 1-1 1c-3 1-5 2-9 3v1h-5l-2-1 1-2h2l4-3c2-2 3-2 5-2v1z" class="Q"></path><path d="M543 464c2-2 3-2 5-2v1c-1 2-3 3-6 4h-3 0l4-3z" class="o"></path><defs><linearGradient id="CG" x1="541.801" y1="459.61" x2="553.196" y2="453.884" xlink:href="#B"><stop offset="0" stop-color="#b5adad"></stop><stop offset="1" stop-color="#d4cfcc"></stop></linearGradient></defs><path fill="url(#CG)" d="M545 449c1 1 2 0 3 1l-2 2h8c0 2-1 2-2 4 0 1 0 2-1 3 0 1-2 2-3 3-2 0-3 0-5 2l-4 3h-2l-1 2c-1-3-3-5-3-7v-1c-1-2 0-4 0-6v5l1-1c0-2 0-4 1-6h0c1-1 1-2 2-2s2 2 3 2l1-1-1-1c1-2 3-1 5-2z"></path><path d="M537 451c1 0 2 2 3 2h1c1 1 4 0 4 1v1c-2 1-2 2-3 4 0-2 0-3 1-4-2 0-4 1-5 2h-2l-1-4c1-1 1-2 2-2z" class="L"></path><path d="M538 457c1-1 3-2 5-2-1 1-1 2-1 4-1 1 0 2 0 3l1 2-4 3h-2c1-1 2-2 2-4h-1-1v-4l1-1v-1z" class="M"></path><path d="M535 453l1 4h2v1l-1 1v4h1 1c0 2-1 3-2 4l-1 2c-1-3-3-5-3-7v-1c-1-2 0-4 0-6v5l1-1c0-2 0-4 1-6h0z" class="n"></path><path d="M541 430s0 1-1 1c-2 2-3 6-4 8-3 6-6 11-6 17 0 5 0 9 2 13h0l2 2c-3 2-6 3-8 6h0c-1-4 0-10-1-13h-1c-1-3 1-26 1-32h2 1c3 0 5 1 8 0h0c2 0 3-1 5-2z" class="u"></path><path d="M525 432h2 1c1 1 2 1 3 1h0c-1 1-3 1-5 1v8l1 1c1 1 1 1 1 2h-1c-1 1 0 5 0 6 1 5 1 9 2 14h0 0l2 4 1 1v-1l2 2c-3 2-6 3-8 6h0c-1-4 0-10-1-13h-1c-1-3 1-26 1-32z" class="n"></path><path d="M547 513l2-1c2 1 3 1 5 1s4 1 6 2l3 1c2 1 3 2 4 3l-2 1c2 1 3 2 4 3l-1 2c4 3 5 5 5 10 0 1-1 3-1 5-1 1-1 2-2 2-2 1-6 1-7 1-1-1-3-1-4-2s-1-2-2-3h-1v2h-1c-4 1-7 1-10 2l-2 1v-1c-4 2-5 5-7 7l-1 1h1c-1 3-2 5-3 7-1-1-1-2-2-3v7l-3-7-7-16c1-1 1-1 0-2v-4c1 1 3 3 3 5 1 1 1 3 2 5h0c0 2 0 4 1 5h0v-9c1 0 1 1 2 2l1-5h0c1-3 2-5 4-7v-1h0c-1-2 0-2 0-4 2-4 8-8 13-10z" class="S"></path><path d="M542 522c2-1 4-2 6-2 5-2 12-2 17 0 2 1 3 2 4 3l-1 2c-6-4-13-4-20-2-4 1-7 3-10 5l-1-1 5-5z" class="AJ"></path><defs><linearGradient id="CH" x1="526.598" y1="543.704" x2="532.402" y2="540.796" xlink:href="#B"><stop offset="0" stop-color="#54504e"></stop><stop offset="1" stop-color="#6b6666"></stop></linearGradient></defs><path fill="url(#CH)" d="M534 528c1-2 3-3 5-4 1-1 2-1 3-1v-1l-5 5 1 1c-3 3-5 6-6 10-1 3-2 6-2 9v6c-1 0-1 0-2 1l-7-16c1-1 1-1 0-2v-4c1 1 3 3 3 5 1 1 1 3 2 5h0c0 2 0 4 1 5h0v-9c1 0 1 1 2 2l1-5h0c1-3 2-5 4-7z"></path><defs><linearGradient id="CI" x1="540.988" y1="529.753" x2="554.47" y2="511.35" xlink:href="#B"><stop offset="0" stop-color="#897e7b"></stop><stop offset="1" stop-color="#b0a9a4"></stop></linearGradient></defs><path fill="url(#CI)" d="M547 513l2-1c2 1 3 1 5 1s4 1 6 2l3 1c2 1 3 2 4 3l-2 1c-5-2-12-2-17 0-2 0-4 1-6 2v1c-1 0-2 0-3 1-2 1-4 2-5 4v-1h0c-1-2 0-2 0-4 2-4 8-8 13-10z"></path><path d="M547 513l2-1c2 1 3 1 5 1s4 1 6 2h-10c-1-1-2-1-3-2h0z" class="L"></path><path d="M552 525c5 0 9 0 13 2 3 2 4 3 5 6l1 2 1 5c-1 1-1 2-2 2-2 1-6 1-7 1-1-1-3-1-4-2s-1-2-2-3h-1v2h-1c-4 1-7 1-10 2l-2 1v-1c-4 2-5 5-7 7l-1 1h1c-1 3-2 5-3 7-1-1-1-2-2-3 1-6 1-12 5-18s9-9 16-11z" class="I"></path><path d="M552 530c3 0 6 0 9 1l1 1c-5-1-9 0-14 2l-2-1v-1c2 0 3-1 4-1s1 0 2-1z" class="G"></path><path d="M553 536c2 0 5 0 8 1 1 1 2 3 2 5v1c-1-1-3-1-4-2s-1-2-2-3h-1v2h-1c0-1 0-2-1-3l-1-1zm-1-11c5 0 9 0 13 2 3 2 4 3 5 6l1 2h-4c-2-1-4-1-5-3l-1-1c-3-1-6-1-9-1-1 1-1 1-2 1s-2 1-4 1v1h-2c1-2 2-3 5-4 1-1 2-2 3-4z" class="Y"></path><path d="M552 525c5 0 9 0 13 2-1 0-2 1-4 0-3 0-6 0-9 1l-1 1h1v1h0 0c-1 1-1 1-2 1s-2 1-4 1v1h-2c1-2 2-3 5-4 1-1 2-2 3-4z" class="p"></path><defs><linearGradient id="CJ" x1="533.514" y1="540.418" x2="545.025" y2="542.338" xlink:href="#B"><stop offset="0" stop-color="#4b4544"></stop><stop offset="1" stop-color="#626161"></stop></linearGradient></defs><path fill="url(#CJ)" d="M531 554c1-6 1-12 5-18s9-9 16-11c-1 2-2 3-3 4-3 1-4 2-5 4h2l2 1c-1 1-3 1-4 3h1c2-1 3-2 5-2 0 1-1 1-2 1v1l5-1 1 1c1 1 1 2 1 3-4 1-7 1-10 2l-2 1v-1c-4 2-5 5-7 7l-1 1h1c-1 3-2 5-3 7-1-1-1-2-2-3z"></path><path d="M554 537c1 1 1 2 1 3-4 1-7 1-10 2l-1-1c3-2 7-3 10-4z" class="n"></path><defs><linearGradient id="CK" x1="451.973" y1="347.475" x2="429.846" y2="361.864" xlink:href="#B"><stop offset="0" stop-color="#ea564a"></stop><stop offset="1" stop-color="#f48577"></stop></linearGradient></defs><path fill="url(#CK)" d="M417 290c7 14 12 29 18 43l22 53c2 4 16 36 16 37l-1 2 2 2c-1 0-2-1-3-1l-3-4c-2 0-3 0-5-1l-2-1h-3l-2-2-3-1c-1-1-1-2-2-3-3-2-5-4-7-7h0c-1-3-2-6-3-8l2-6c1 0 1 1 2 2 1-3 1-4 0-6l1-2-1-2c0-1 0-2 1-3l1-1 2 1h0c1-1-1-3-1-4-1-3-2-5-3-7l-1-1h0c-1-2-3-3-4-5l-1-5c-2-5-6-10-8-15h0c-1-3-2-4-2-7 0-1-1-2-2-2v-1c1 0 1 1 2 1l1-3c-3-8-6-15-11-22-1-3-3-6-5-8-4-4-10-5-16-5-1-1-2-2-2-3l1-1c-1 0-1 0-1-1 4 0 8 0 12-1 1 1 2 1 3 1h1l2 1h1c1 0 1-1 2-1v-1l-1-1 1-1z"></path><path d="M430 333l3 8v1c-2-1-3-3-4-4 0-1-1-2-2-2v-1c1 0 1 1 2 1l1-3z" class="q"></path><path d="M439 354l5 16h0c-1-2-3-3-4-5l-1-5v-2c-1-2-1-2 0-4z" class="w"></path><path d="M460 415l1-1c0-1 0-3 1-4l6 12c-2 0-3 0-5-1l-3-6z" class="C"></path><path d="M429 338c1 1 2 3 4 4v-1l6 13c-1 2-1 2 0 4v2c-2-5-6-10-8-15h0c-1-3-2-4-2-7z" class="R"></path><path d="M447 381l2 1h0l13 28c-1 1-1 3-1 4l-1 1-7-14v-1c-1-3-2-3-3-5-2-3-3-5-4-8l-1-2c0-1 0-2 1-3l1-1z" class="AI"></path><path d="M447 381l2 1v3c1 5 4 10 5 15 0 1 0 0-1 1v-1c-1-3-2-3-3-5-2-3-3-5-4-8l-1-2c0-1 0-2 1-3l1-1z" class="AG"></path><defs><linearGradient id="CL" x1="442.864" y1="404.867" x2="451.31" y2="403.507" xlink:href="#B"><stop offset="0" stop-color="#240808"></stop><stop offset="1" stop-color="#3f110e"></stop></linearGradient></defs><path fill="url(#CL)" d="M445 395c1-3 1-4 0-6l1-2c1 3 2 5 4 8 1 2 2 2 3 5v1l7 14 3 6-2-1h-3l-2-2-3-1c-1-1-1-2-2-3-3-2-5-4-7-7h0l-3-8 2-6c1 0 1 1 2 2z"></path><path d="M448 403h1c2 2 3 6 5 8l1-1 1 1h0c1 4 3 4 2 9l-2-2c1 0 0 0 1-1-4-4-6-9-9-14z" class="g"></path><path d="M445 395c1-3 1-4 0-6l1-2c1 3 2 5 4 8 1 2 2 2 3 5v1l7 14 3 6-2-1h-3c1-5-1-5-2-9h0l-1-1-1 1c-2-2-3-6-5-8h-1c-1-2-2-6-3-8z" class="s"></path><path d="M455 410c-1-1-3-5-4-6v-3c-1-1-1-2-1-3v-1l3 3v1l7 14 3 6-2-1h-3c1-5-1-5-2-9h0l-1-1z" class="i"></path><path d="M634 444c5 1 11 5 16 7l4 1-2 8h0l-1-1c-1 0-1 0-1 1-1 2-2 4-4 5l-2 11-1 5c-1 2-1 4-2 5 0 2 0 5-1 7l-4 11-4 11-3 8c0 1-1 2-2 3v1l-1-1c-5 5-8 12-14 17-1-5 5-9 7-13l-1-1v-2c1-2 2-5 1-7 0-2-1-2-2-3s-1-2-2-3c1-2 1-3 1-5h0 0c0-2 1-3 1-5 1-1 1-3 2-4v-1l2-5c2-6 3-12 3-18 0-2-2-7-2-8 1-1 5-2 5-3 1-1 1-4 2-5 1-4 2-7 3-11 0-2 1-3 2-5z" class="N"></path><path d="M629 480c-1-2 1-3 2-5 0-2-1-6-1-7 0-2 1-3 1-4 0-5 1-10 3-14l1-1c1 1 2 3 3 4 1 4 2 6 1 10l-2 4c1 2 2 4 2 6 0 1-1 2-1 3-2 4-4 5-6 8h-1v-2h0c-1 0-1-1-2-1v-1z" class="b"></path><path d="M629 481c2 0 2 0 3-1l5-13c1 2 2 4 2 6 0 1-1 2-1 3-2 4-4 5-6 8h-1v-2h0c-1 0-1-1-2-1z" class="K"></path><path d="M634 444c5 1 11 5 16 7l4 1-2 8h0l-1-1c-1 0-1 0-1 1-1 2-2 4-4 5l-2 11-1 1v-3c0-3 1-5 1-7h0l-2 2c-1 3-3 5-4 7 0-1 1-2 1-3 0-2-1-4-2-6l2-4c1-4 0-6-1-10-1-1-2-3-3-4l-1-1-2 1c0-2 1-3 2-5z" class="C"></path><path d="M637 467l2-4h0 2 0c1 1 1 2 1 3v2 1c-1 3-3 5-4 7 0-1 1-2 1-3 0-2-1-4-2-6z" class="l"></path><path d="M650 451l4 1-2 8h0l-1-1c-1 0-1 0-1 1-1 2-2 4-4 5 0-3 2-6 3-10 0-1 0-3 1-4z" class="Z"></path><path d="M634 444c5 1 11 5 16 7-1 1-1 3-1 4-2-2-3-3-5-4-1 1-2 1-3 1h-1c-1 0-2 0-2 1-1-1-2-3-3-4l-1-1-2 1c0-2 1-3 2-5z" class="K"></path><path d="M642 469l2-2h0c0 2-1 4-1 7v3l1-1-1 5c-1 2-1 4-2 5 0 2 0 5-1 7l-4 11-4 11-3 8c0 1-1 2-2 3v1l-1-1c-5 5-8 12-14 17-1-5 5-9 7-13l-1-1v-2c1-2 2-5 1-7 0-2-1-2-2-3s-1-2-2-3c1-2 1-3 1-5h0 0c0-2 1-3 1-5 1-1 1-3 2-4v2h1c5-4 8-11 10-17l-2-4 1-1v1c1 0 1 1 2 1h0v2h1c2-3 4-4 6-8 1-2 3-4 4-7z" class="J"></path><path d="M641 486c0 2 0 5-1 7l-4 11-4 11v-1h-1c1-1 1-2 1-2v-1-1-1c1-2 1-2 0-3v-2l2-4h0v-1c0-1 1-2 1-3 1-1 2-2 2-3l1-1 1-2v-2l2-2z" class="c"></path><path d="M632 511c1-2 1-4 1-6 3-3 3-7 5-10 1-1 1-2 2-2l-4 11-4 11v-1h-1c1-1 1-2 1-2v-1z" class="R"></path><path d="M633 494l1 2c-1 2-2 3-2 5-2 5-5 9-7 14v2c-1 1-1 1-1 2s0 2-1 3v3s-3 2-3 3l-1 2-1-1v-2c1-2 2-5 1-7 0-2-1-2-2-3s-1-2-2-3c1-2 1-3 1-5h0 0c0-2 1-3 1-5 1-1 1-3 2-4v2 2l1 1c0 1 0 1 1 2l2 4v1h1c1-3 3-5 4-7 2-3 3-7 5-11z" class="c"></path><path d="M616 509h0l5 8c1 2 1 3 2 5v3s-3 2-3 3l-1 2-1-1v-2c1-2 2-5 1-7 0-2-1-2-2-3s-1-2-2-3c1-2 1-3 1-5h0z" class="k"></path><path d="M621 517c1 2 1 3 2 5v3s-3 2-3 3l1-11z" class="W"></path><path d="M642 469l2-2h0c0 2-1 4-1 7v3l1-1-1 5h-2v-1h0c0-2 1-2 1-4h-2l-7 18c-2 4-3 8-5 11-1 2-3 4-4 7h-1v-1l-2-4c-1-1-1-1-1-2l-1-1v-2h1c5-4 8-11 10-17l-2-4 1-1v1c1 0 1 1 2 1h0v2h1c2-3 4-4 6-8 1-2 3-4 4-7z" class="C"></path><path d="M642 469l2-2h0c0 2-1 4-1 7v3l1-1-1 5h-2v-1h0c0-2 1-2 1-4h-2c-3 4-5 8-8 12-1 2-2 3-3 5l-8 11h1v1 2l2 2-1 2-2-4c-1-1-1-1-1-2l-1-1v-2h1c5-4 8-11 10-17l-2-4 1-1v1c1 0 1 1 2 1h0v2h1c2-3 4-4 6-8 1-2 3-4 4-7z" class="R"></path><path d="M635 239l1 1-1 1v1h1l1 1h4 2v2c-1 6-1 12-1 18 1 1 0 3 1 4l1 24c0 1 0 2-1 2-1 3-2 5-5 6h-2-1-1l-1 1c1 1 1 1 2 1 3 1 6 3 9 4-1 1-2 0-3 1h-2l-17-6-2 1-1-1-1-1c0-1 0-2 1-3-1-1-1-2-1-3s1-1 1-2v-1l-2-3h-1l-1 2v-2c-2-2-3-2-3-4v-1c1-1 2-3 3-4h-3l-1-1 1-1h-1c-1 1-3 4-4 5-1-1-1-1-1-2-1 1-1 2-2 3 0 1 0 0-1 1v1l-1-2 3-9c1-2 3-3 3-5-1-4-2-9-3-13l2-2c-1-1-1-2-1-3l2-1c2 1 5 1 7 1 1 0 2 0 4-1h-1-1v-1c-1-1-1-2-2-3v-1h4l2 1 1-1h1s1-1 2-1c3 0 4-1 7-2h0l3-2z" class="AZ"></path><defs><linearGradient id="CM" x1="610.025" y1="250.361" x2="609.144" y2="266.036" xlink:href="#B"><stop offset="0" stop-color="#524f4d"></stop><stop offset="1" stop-color="#6a6868"></stop></linearGradient></defs><path fill="url(#CM)" d="M608 249c2 1 5 1 7 1-4 5-6 10-5 15v2c-1 0-1 1-2 1-1-4-2-9-3-13l2-2c-1-1-1-2-1-3l2-1z"></path><defs><linearGradient id="CN" x1="627.247" y1="240.378" x2="628.123" y2="247.119" xlink:href="#B"><stop offset="0" stop-color="#958d89"></stop><stop offset="1" stop-color="#bfbbba"></stop></linearGradient></defs><path fill="url(#CN)" d="M635 239l1 1-1 1v1h1l1 1h4 2v2l-24 4h-1-1v-1c-1-1-1-2-2-3v-1h4l2 1 1-1h1s1-1 2-1c3 0 4-1 7-2h0l3-2z"></path><path d="M634 279l1-1v-1c0-1 0-3 1-4v-1c1-1 1-2 2-3l2 4c1 2 1 4 1 5l2 15c-1 3-2 5-5 6h-2-1v-1h0c0-3 1-4 1-7 0-2-1-4-1-6s-1-4-1-6z" class="D"></path><path d="M634 279l1-1v-1c0-1 0-3 1-4v-1c1-1 1-2 2-3l2 4c-2 3-3 6-2 10v2h-3c0-2-1-4-1-6z" class="AD"></path><path d="M635 298c2-2 3-4 3-6 1-2 0-5 1-7 2-2 1-4 2-7l2 15c-1 3-2 5-5 6h-2-1v-1z" class="F"></path><path d="M610 267c3-2 7-4 11-5 5-1 9 0 13 3 2 1 3 3 4 4-1 1-1 2-2 3v1c-1 1-1 3-1 4v1l-1 1-1-1c-2-2-3-4-6-4-2 0-2-1-4-1l1-1h3l-1-1h-1c-2 1-4 1-6 1-3 0-4 2-6 3l-1 1h-1c-1 1-3 4-4 5-1-1-1-1-1-2-1 1-1 2-2 3 0 1 0 0-1 1v1l-1-2 3-9c1-2 3-3 3-5 1 0 1-1 2-1z" class="o"></path><path d="M603 283c0-2 1-4 2-6 0-1 1-1 2-2l1 1s1-1 2-1c4-3 6-5 12-6v1l-3 2c-3 0-4 2-6 3l-1 1h-1c-1 1-3 4-4 5-1-1-1-1-1-2-1 1-1 2-2 3 0 1 0 0-1 1z" class="Q"></path><defs><linearGradient id="CO" x1="633.896" y1="274.615" x2="624.605" y2="265.375" xlink:href="#B"><stop offset="0" stop-color="#a59e99"></stop><stop offset="1" stop-color="#cdc2be"></stop></linearGradient></defs><path fill="url(#CO)" d="M634 265c2 1 3 3 4 4-1 1-1 2-2 3v1c-1 1-1 3-1 4v1l-1 1-1-1c-2-2-3-4-6-4-2 0-2-1-4-1l1-1h3l-1-1h-1c-2 1-4 1-6 1l3-2v-1l1-1 1-1h5 2l1 1c0-1 0-1 1-1h1v-2z"></path><path d="M619 272c2 0 4 0 6-1h1l1 1h-3l-1 1c2 0 2 1 4 1 3 0 4 2 6 4l1 1c0 2 1 4 1 6s1 4 1 6c0 3-1 4-1 7h0v1h-1l-1 1c1 1 1 1 2 1 3 1 6 3 9 4-1 1-2 0-3 1h-2l-17-6-2 1-1-1-1-1c0-1 0-2 1-3-1-1-1-2-1-3s1-1 1-2v-1l-2-3h-1l-1 2v-2c-2-2-3-2-3-4v-1c1-1 2-3 3-4h-3l-1-1 1-1 1-1c2-1 3-3 6-3z" class="n"></path><path d="M619 289l1-1c1 0 1-1 2-2v1 1 1 2l1 1 2-1 1 1-2 1h2c-1 0-2 1-3 1v4h0c-2-1-2-3-3-3v4c1 0 2 1 2 1l-2 1-1-1-1-1c0-1 0-2 1-3-1-1-1-2-1-3s1-1 1-2v-1-1z" class="D"></path><path d="M627 274c3 0 4 2 6 4l1 1c0 2 1 4 1 6s1 4 1 6c0 3-1 4-1 7h0v1h-1-1s-1 1-2 1l-2-1c2-2 3-3 4-5 2-3 2-8 1-11h0c-1-2-2-4-3-5-1-2-3-3-4-4zm1 7c1 2 2 3 2 4s1 2 1 3c-1 1-2 1-3 3l-2 2h0-2l2-1-1-1-2 1-1-1v-2-1-1-1c-1 1-1 2-2 2l-1 1-1-1c1-2 2-4 4-5 3 0 4 0 6-2z" class="x"></path><defs><linearGradient id="CP" x1="619.895" y1="275.679" x2="624.485" y2="291.22" xlink:href="#B"><stop offset="0" stop-color="#1b191a"></stop><stop offset="1" stop-color="#3a3837"></stop></linearGradient></defs><path fill="url(#CP)" d="M619 272c2 0 4 0 6-1h1l1 1h-3l-1 1c2 0 2 1 4 1 1 1 3 2 4 4l-1 1c3 4 4 8 2 13 0 3-2 5-4 7h-2-1l2-2c2-1 3-3 4-5v-4c0-1-1-2-1-3s-1-2-2-4c-2 2-3 2-6 2-2 1-3 3-4 5l1 1v1l-2-3h-1l-1 2v-2c-2-2-3-2-3-4v-1c1-1 2-3 3-4h-3l-1-1 1-1 1-1c2-1 3-3 6-3z"></path><path d="M619 272c2 0 4 0 6-1h1l1 1h-3l-1 1c2 0 2 1 4 1 1 1 3 2 4 4l-1 1c-1-2-3-4-6-4-4-1-7 1-9 3h-3l-1-1 1-1 1-1c2-1 3-3 6-3z" class="AV"></path><path d="M617 281c2-1 4-2 7-2 2 0 3 1 4 2-2 2-3 2-6 2-2 1-3 3-4 5l1 1v1l-2-3h-1l-1 2v-2c0-2 1-4 2-6z" class="f"></path><path d="M617 281c1 2 2 2 1 4l-1 2h0l1 1 1 1v1l-2-3h-1l-1 2v-2c0-2 1-4 2-6z" class="G"></path><defs><linearGradient id="CQ" x1="491.877" y1="478.997" x2="537.502" y2="449.082" xlink:href="#B"><stop offset="0" stop-color="#998f8f"></stop><stop offset="1" stop-color="#dadbd9"></stop></linearGradient></defs><path fill="url(#CQ)" d="M515 412c1-1 1-2 2-3 3 2 4 5 6 7 3 3 5 5 9 6 1 0 3 0 4 1l-3 3c-2 1-6 3-7 5l1 1h-2c0 6-2 29-1 32h1c1 3 0 9 1 13h0c0 1 0 1 1 2h0l1-1 1-1c5-3 12-2 18-1h1l1 1v2l-1 2c1 0 2 0 3-1h4 0v1h-1c-2 0-4 2-6 2-1 1-2 2-3 2-3 1-6 3-8 5l-3 3c-2 0-3 2-4 3 0 2 0 3-1 5v1c0 1-1 2-2 3 0 2-1 5-1 8 1 2 0 7 1 8l-1 21h0c-1-2-1-4-2-5 0-2-2-4-3-5v4c1 1 1 1 0 2l-5-13c-2-4-5-10-6-14 1-1 1-2 1-3h1c-1-2-1-5-1-7l-1-12c0-1-1-8-1-9 0-3-3-4-4-6h1 0c1 0 2 1 3 2v-3h-1c-1-2-1-4 0-6v-9c-1-3 0-6 0-9 0-2-1-4-1-5-1-2-1-4-2-6h-2l-2-2-1-2h-1l-2-2c3 0 9 1 11 0v-1c-1-2-7-5-9-7h-1l-1-2 1-1 6 1c4-3 8-6 11-10z"></path><path d="M498 424c2-1 3 0 4 1 2 1 6 3 7 5v3l3 56v1l-1-1h-1c0-1-1-8-1-9 0-3-3-4-4-6h1 0c1 0 2 1 3 2v-3h-1c-1-2-1-4 0-6v-9c-1-3 0-6 0-9 0-2-1-4-1-5-1-2-1-4-2-6h-2l-2-2-1-2h-1l-2-2c3 0 9 1 11 0v-1c-1-2-7-5-9-7h-1z" class="D"></path><path d="M517 464l1 2h0v-1c1 6 0 12 2 18l1 31v1l-1 2v-1c0-1 0-1-1-2l2 18v4c1 1 1 1 0 2l-5-13c-2-4-5-10-6-14 1-1 1-2 1-3h1l1 4 2 2h0c1-3 1-5 1-8l-1-1h1v-2c1-4 1-9 1-13v-10-16z" class="V"></path><path d="M516 525c1-2 1-5 1-7s1-5 1-7v-14h0l1 17 2 18v4c1 1 1 1 0 2l-5-13z" class="AF"></path><path d="M517 464l1 2h0v-1c1 6 0 12 2 18l1 31v1l-1 2v-1c0-1 0-1-1-2l-1-17c1-4 0-10 0-14v-1l-1 8v-10-16z" class="H"></path><path d="M515 412c1-1 1-2 2-3 3 2 4 5 6 7 3 3 5 5 9 6 1 0 3 0 4 1l-3 3c-2 1-6 3-7 5l1 1h-2c-1 1-3 5-3 7v2 2c-1 3 0 6-1 8 0 2 0 2-1 4v12c1 2 2 6 1 8v3h0c-1 2 0 4-1 5-2-6-1-12-2-18v1h0l-1-2-1-48c-1-1-1-2-1-4z" class="M"></path><path d="M523 416c3 3 5 5 9 6 1 0 3 0 4 1l-3 3c-2 1-6 3-7 5l1 1h-2c-1 1-3 5-3 7v2 2c-1 3 0 6-1 8 0 2 0 2-1 4v-14c1-3 0-6 1-9s2-6 5-8h1v-1c-1 0-2 0-3-1 0-2 0-4-1-6z" class="Q"></path><defs><linearGradient id="CR" x1="512.248" y1="530.49" x2="534.326" y2="435.91" xlink:href="#B"><stop offset="0" stop-color="#7b6e6d"></stop><stop offset="1" stop-color="#ccd0cc"></stop></linearGradient></defs><path fill="url(#CR)" d="M520 455c1-2 1-2 1-4 1-2 0-5 1-8v-2-2c0-2 2-6 3-7 0 6-2 29-1 32h1c1 3 0 9 1 13h0c0 1 0 1 1 2h0l1-1 1-1c5-3 12-2 18-1h1l1 1v2l-1 2c1 0 2 0 3-1h4 0v1h-1c-2 0-4 2-6 2-1 1-2 2-3 2-3 1-6 3-8 5l-3 3c-2 0-3 2-4 3 0 2 0 3-1 5v1c0 1-1 2-2 3 0 2-1 5-1 8 1 2 0 7 1 8l-1 21h0c-1-2-1-4-2-5 0-2-2-4-3-5l-2-18c1 1 1 1 1 2v1l1-2v-1l-1-31c1-1 0-3 1-5h0v-3c1-2 0-6-1-8v-12z"></path><path d="M524 504h1v5 12c-1-1-1-3-1-4-1-5 0-8 0-13z" class="I"></path><path d="M524 464h1c1 3 0 9 1 13h0c0 1 0 1 1 2h0l1-1 1-1c5-3 12-2 18-1l-1 1c-5 0-14 0-17 4-1 1-2 2-2 3h0c-1 2 0 4 0 5v14 2c0 2-1 5-1 8v3c-1-8 0-17-1-25l-1-27z" class="B"></path><path d="M548 476l1 1v2l-1 2c1 0 2 0 3-1h4 0v1h-1c-2 0-4 2-6 2-1 1-2 2-3 2-3 1-6 3-8 5l-3 3c-2 0-3 2-4 3 0 2 0 3-1 5v1c0 1-1 2-2 3v-2-14c0-1-1-3 0-5h0c0-1 1-2 2-3 3-4 12-4 17-4l1-1h1z" class="D"></path><path d="M527 503c0-1 1-2 1-3h0c2-3 1-8 1-12 0-2 1-3 3-4h2v-2h2l-3 3-2 2v1c2 0 3-2 5-2-1 1-2 2-2 3-1 2-4 5-4 7s0 3-1 5v1c0 1-1 2-2 3v-2z" class="H"></path><path d="M548 476l1 1v2l-1 2c1 0 2 0 3-1h4 0v1h-1c-2 0-4 2-6 2-1 1-2 2-3 2-3 1-6 3-8 5l-3 3c-2 0-3 2-4 3 0-2 3-5 4-7 0-1 1-2 2-3-2 0-3 2-5 2v-1l2-2 3-3h1c3-1 8-3 9-5l1-1h1z" class="M"></path><path d="M548 476l1 1v2l-1 2c1 0 2 0 3-1h4 0v1h-1c-2 0-4 2-6 2-1 1-2 2-3 2l-1-1c-1 1-1 1-2 1l-1-1c1-2 5-3 6-5 0-1 0-2 1-3z" class="Q"></path><path d="M663 295l6 18 5-4 5-3-1 2c-2 1-3 3-5 4-1 1-3 2-4 4l1 1h3v2h1v2h0l1-3c-1-1-1-1-1-2 3-2 4-4 7-5 1 1 3 2 4 2l1 2-6 23-16 49c-1 2-2 5-4 7l-2-3-4-3 2-8 1-6c0-6-1-12-2-18-4-15-16-27-31-32-4-1-9-1-13-3v-1c1-2 2-3 3-5 3-3 5-7 6-11l-1-2v-1-1l1 1 2-1 17 6h2c1-1 2 0 3-1 3 1 6 3 8 4 1 0 1-1 2-2h0c1 0 4-3 6-4 0-2 2-6 3-8z" class="E"></path><path d="M642 325c1 1 7 5 7 7 1 3 3 4 5 7 2 4 3 9 4 14-2-3-3-6-4-9-2-5-6-8-10-12l1-1c0-1-2-2-2-2-1-1-1-3-1-4z" class="G"></path><defs><linearGradient id="CS" x1="634.753" y1="317.855" x2="624.798" y2="326.102" xlink:href="#B"><stop offset="0" stop-color="#6a6262"></stop><stop offset="1" stop-color="#a09994"></stop></linearGradient></defs><path fill="url(#CS)" d="M619 313h4 0v1l1 1v1 1c1 0 5 2 5 1 5 2 9 4 13 7 0 1 0 3 1 4 0 0 2 1 2 2l-1 1c-4-4-8-7-13-9-4-1-14-5-18-3h-2c1-2 2-3 3-5h1 1c1-1 2-1 3-2z"></path><path d="M619 313h4 0v1l1 1v1c-2 0-1 0-2 1-2 0-3 0-4-1l1-3z" class="n"></path><defs><linearGradient id="CT" x1="655.568" y1="328.279" x2="643.241" y2="318.56" xlink:href="#B"><stop offset="0" stop-color="#454243"></stop><stop offset="1" stop-color="#615c5a"></stop></linearGradient></defs><path fill="url(#CT)" d="M629 318h1c1-1 2-1 4-1h0 1c1 0 2 1 3 1 1 1 3 1 4 2l1-1 1 1 3-3c4 4 8 8 10 13-2 0-2 0-3 1l-2-1h0l-2-1 2 3h0c-1 1-1 0-1 1-1 0-1 0-2-1 0-2-6-6-7-7-4-3-8-5-13-7z"></path><defs><linearGradient id="CU" x1="649.073" y1="322.804" x2="635.007" y2="326.968" xlink:href="#B"><stop offset="0" stop-color="#393534"></stop><stop offset="1" stop-color="#585657"></stop></linearGradient></defs><path fill="url(#CU)" d="M629 318h1c1-1 2-1 4-1h0l6 3c4 2 7 5 10 9l2 3h0c-1 1-1 0-1 1-1 0-1 0-2-1 0-2-6-6-7-7-4-3-8-5-13-7z"></path><defs><linearGradient id="CV" x1="666.477" y1="360.858" x2="642.717" y2="348.741" xlink:href="#B"><stop offset="0" stop-color="#0f1112"></stop><stop offset="1" stop-color="#463b3a"></stop></linearGradient></defs><path fill="url(#CV)" d="M650 329l2 1h0l4 6c0 1 0 1 1 2s1 3 2 4c1 2 2 5 2 7 1 1 2 2 2 3s0 1 1 2c0 2-1 3-2 4l1 1v4h-1 1l1-1-4 13c-1 2-1 5-3 6l-1-1 1-6v-2h0 1l1-5v-3h1c1-2 0-6-1-8v-2c0-1 0-1-1-1-1-5-2-10-4-14-2-3-4-4-5-7 1 1 1 1 2 1 0-1 0 0 1-1h0l-2-3z"></path><path d="M661 349c1 1 2 2 2 3s0 1 1 2c0 2-1 3-2 4 0-3-1-6-1-9z" class="O"></path><defs><linearGradient id="CW" x1="644.713" y1="312.556" x2="620.115" y2="313.983" xlink:href="#B"><stop offset="0" stop-color="#605e5e"></stop><stop offset="1" stop-color="#978f8c"></stop></linearGradient></defs><path fill="url(#CW)" d="M620 304c2 1 5 3 8 3 6 3 13 6 19 10l-3 3-1-1-1 1c-1-1-3-1-4-2-1 0-2-1-3-1h-1 0c-2 0-3 0-4 1h-1c0 1-4-1-5-1v-1-1l-1-1v-1h0-4c-1 1-2 1-3 2h-1-1c3-3 5-7 6-11z"></path><path d="M624 316v-1l-1-1v-1c4 1 8 1 11 4-2 0-3 0-4 1h-1c0 1-4-1-5-1v-1z" class="V"></path><defs><linearGradient id="CX" x1="668.954" y1="344.615" x2="645.055" y2="317.929" xlink:href="#B"><stop offset="0" stop-color="#413e3e"></stop><stop offset="1" stop-color="#6c6764"></stop></linearGradient></defs><path fill="url(#CX)" d="M619 300l1 1 2-1 17 6h2c1-1 2 0 3-1 3 1 6 3 8 4 1 0 1-1 2-2h0c1 0 4-3 6-4l-3 7c-1 1-1 3-2 4l-1 1 4 4 1 1c0 2 2 5 3 7s1 4 1 6c1 5 2 11 2 16 0 2-1 3-1 5-1-1-1-1-1-2s-1-2-2-3c0-2-1-5-2-7-1-1-1-3-2-4s-1-1-1-2l-4-6 2 1c1-1 1-1 3-1-2-5-6-9-10-13-6-4-13-7-19-10-3 0-6-2-8-3l-1-2v-1-1z"></path><path d="M654 331c1-1 1-1 3-1 0 1 0 3 1 4h0c-2 0-3-2-4-3z" class="r"></path><path d="M628 307v-1l-1 1v-1h0c2 0 4 1 6 1h1c3 1 6 2 9 4 2 1 3 2 6 3h0c3 3 7 6 9 10 1 3 2 5 2 8l-2 2c-1-1-1-3-1-4-2-5-6-9-10-13-6-4-13-7-19-10z" class="E"></path><path d="M619 300l1 1 2-1 17 6h2c1-1 2 0 3-1 3 1 6 3 8 4 1 0 1-1 2-2h0c1 0 4-3 6-4l-3 7c-1 1-1 3-2 4l-1 1c-2-2-4-2-6-3 0-1-1-1-2-2v1c1 1 2 1 3 3h0 0c-3-1-4-2-6-3-3-2-6-3-9-4h-1c-2 0-4-1-6-1h0v1l1-1v1c-3 0-6-2-8-3l-1-2v-1-1z" class="AB"></path><path d="M619 300l1 1c4 3 9 4 14 6h-1c-2 0-4-1-6-1h0v1l1-1v1c-3 0-6-2-8-3l-1-2v-1-1z" class="F"></path><path d="M654 307c1 0 4-3 6-4l-3 7c-1 1-1 3-2 4-6-3-10-6-16-8h2c1-1 2 0 3-1 3 1 6 3 8 4 1 0 1-1 2-2h0z" class="H"></path><defs><linearGradient id="CY" x1="664.761" y1="354.497" x2="665.487" y2="299.539" xlink:href="#B"><stop offset="0" stop-color="#212021"></stop><stop offset="1" stop-color="#535253"></stop></linearGradient></defs><path fill="url(#CY)" d="M663 295l6 18 5-4 5-3-1 2c-2 1-3 3-5 4-1 1-3 2-4 4l1 1h3v2h1v2h0c0 3 0 6-1 9 0 2 0 4-1 6l-1 6-7 20-1 1h-1 1v-4l-1-1c1-1 2-2 2-4s1-3 1-5c0-5-1-11-2-16 0-2 0-4-1-6s-3-5-3-7l-1-1-4-4 1-1c1-1 1-3 2-4l3-7c0-2 2-6 3-8z"></path><path d="M657 310c1 2 1 3 1 5 0 0-1 0-1 1v1c1 0 1 1 1 2l-4-4 1-1c1-1 1-3 2-4z" class="B"></path><path d="M674 309l5-3-1 2c-2 1-3 3-5 4-1 1-3 2-4 4l1 1c1 0 1 1 2 2v1l-1-1c-2 0-3-2-3-4-1-1 0-1 1-2l5-4z" class="m"></path><defs><linearGradient id="CZ" x1="664.866" y1="350.154" x2="674.251" y2="353.119" xlink:href="#B"><stop offset="0" stop-color="#0e0605"></stop><stop offset="1" stop-color="#230c0b"></stop></linearGradient></defs><path fill="url(#CZ)" d="M681 311c1 1 3 2 4 2l1 2-6 23-16 49c-1 2-2 5-4 7l-2-3-4-3 2-8 1 1c2-1 2-4 3-6l4-13 7-20 1-6c1-2 1-4 1-6 1-3 1-6 1-9l1-3c-1-1-1-1-1-2 3-2 4-4 7-5z"></path><path d="M672 336l1-1c0-1 0-2 1-3 0-3 2-6 4-8 0 7-2 14-4 21-3 11-7 22-11 33-2 4-5 9-5 13l-4-3 2-8 1 1c2-1 2-4 3-6l4-13 7-20 1-6z" class="C"></path><path d="M677 195v1l-1 2v1c2 0 0 0 2-1h0l4-4 1-1h1c0 1 1 1 0 2l-1 1h2c0-1 1-1 1-1l1 1c1 2 3 3 5 4 1 1 2 2 2 4h-1c-1 1-1 2-2 2l-1 2v2h1l1 2 1 1-1 1 1 1c1 1 1 2 2 3-1 2-2 3-3 5-1 1-2 1-2 3v1c-1 2-4 5-5 6h-1c-2 2-4 5-6 7-1 1-1 1-1 2l-5 8-4 8-3 14-1 5c0 2-1 4-1 6l-1 4v6l1 2c-1 2-3 6-3 8-2 1-5 4-6 4h0c-1 1-1 2-2 2-2-1-5-3-8-4s-6-3-9-4c-1 0-1 0-2-1l1-1h1 1 2c3-1 4-3 5-6 1 0 1-1 1-2l-1-24c-1-1 0-3-1-4 0-6 0-12 1-18v-2-3l1-7v-1l2-8 2-5v-2c-1-1 0-2 0-2l2-3c1-2 2-4 4-5h1c1-3 1-6 1-9l1-1v1c0 2 1 3 1 5l1 1c0 2-1 3 0 4l4 3 4-7c2-1 3-1 4-1l1-1 3-4 2-3z" class="O"></path><path d="M648 279c1 2 1 3 1 4 1-1 1-3 1-5 0-4 1-7 2-11v16 15h-1c0-4-1-8-2-12 0-2 0-2-1-3-1-3 0-8 0-11v7h0zm12-40v2h0c2-4 3-7 5-10h0c-1 4-3 7-4 11 0 2-1 6-2 8 0 1 0 3 1 4h1c-1 2-2 4-3 5s-1 0-1 2h0c-1 2-1 3-1 4l-1 1v-1c1-9 1-18 5-26z" class="p"></path><path d="M659 250c0 1 0 3 1 4h1c-1 2-2 4-3 5l1-9z" class="H"></path><path d="M677 195v1l-1 2v1c2 0 0 0 2-1h0l4-4 1-1h1c0 1 1 1 0 2l-1 1h2c0-1 1-1 1-1l1 1c1 2 3 3 5 4 1 1 2 2 2 4h-1c-1 1-1 2-2 2l-1 2v2h1l1 2-3 1v-3-2-1l-1-1c-1-2-3-4-4-7h1l1 1h1c-1-1-1-2-2-3l-3 2h0v-1-3c-1 1-3 4-4 5-2 0-2-1-3 0l-1 1c0 1-1 1-2 1l3-4 2-3z" class="e"></path><defs><linearGradient id="Ca" x1="677.363" y1="220.152" x2="658.852" y2="224.683" xlink:href="#B"><stop offset="0" stop-color="#484849"></stop><stop offset="1" stop-color="#7b7675"></stop></linearGradient></defs><path fill="url(#Ca)" d="M671 210c1 0 2-1 4 0h1l1-1h0v1c0 1 0 1-1 2-2 2-3 4-4 7l-2 4-5 8h0c-2 3-3 6-5 10h0v-2c0-2 1-4 1-6 3-8 6-15 10-23z"></path><path d="M671 210l4-7c2 2 4 3 6 4-1-2-5-5-6-7h3v1c2 2 3 2 5 2 1 1 3 3 4 5v1 2l1 1h-1c-1 2-2 4-2 5v1l-2 3-1 1-2 2v1c-1 1-1 1-2 1 1-3 3-6 5-9v-3l-1 1-1 1v-1c1-1 2-2 2-4l-2 1c-1-2 1-1 1-3-2 1-3 1-4 1h-1v-1h0l-1 1h-1c-2-1-3 0-4 0z" class="r"></path><path d="M672 219h0l3-3c0-1 1-1 2-2v1l-1 1c-1 2 0 2-1 5 0 1-2 4-3 6l1 1h-1c-2 4-4 8-5 12-1 1-1 1-1 2 0 2-2 4-2 6l-3 6h-1c-1-1-1-3-1-4 1-2 2-6 2-8 1-4 3-7 4-11l5-8 2-4z" class="I"></path><path d="M672 219h0l3-3c0-1 1-1 2-2v1l-1 1h0v1l-1 1v1c-1 1-1 2-2 3s-2 1-3 1l2-4z" class="H"></path><path d="M677 210h1c1 0 2 0 4-1 0 2-2 1-1 3l2-1c0 2-1 3-2 4v1l1-1 1-1v3c-2 3-4 6-5 9 1 0 1 0 2-1s2-1 3-2c-3 4-6 6-8 11-2 2-4 4-5 7-1 0 0 0-1-1h-2c1-4 3-8 5-12h1l-1-1c1-2 3-5 3-6 1-3 0-3 1-5l1-1v-1c-1 1-2 1-2 2l-3 3h0c1-3 2-5 4-7 1-1 1-1 1-2z" class="V"></path><path d="M681 215v1l1-1 1-1v3c-2 3-4 6-5 9 1 0 1 0 2-1s2-1 3-2c-3 4-6 6-8 11-2 2-4 4-5 7-1 0 0 0-1-1 2-4 6-8 7-13h0v-1-1c1-1 1-3 2-4 1-2 1-4 3-6z" class="G"></path><defs><linearGradient id="Cb" x1="640.786" y1="306.181" x2="653.583" y2="292.129" xlink:href="#B"><stop offset="0" stop-color="#444040"></stop><stop offset="1" stop-color="#5f5c5c"></stop></linearGradient></defs><path fill="url(#Cb)" d="M648 283c1 1 1 1 1 3 1 4 2 8 2 12h1c1 2 1 3 1 4v1c0 1 0 2 1 3v1h0c-1 1-1 2-2 2-2-1-5-3-8-4s-6-3-9-4c-1 0-1 0-2-1l1-1h1 1 2c3-1 4-3 5-6 1 0 1-1 1-2l1-2c1-1 1-1 2 0 1-2 1-4 1-6z"></path><path d="M652 298c1 2 1 3 1 4 0 2 0 3-1 4h0c-1-2-1-3-1-5-1 0-1-1-1-2l1-1h1z" class="AB"></path><defs><linearGradient id="Cc" x1="661.403" y1="232.082" x2="655.359" y2="229.746" xlink:href="#B"><stop offset="0" stop-color="#969190"></stop><stop offset="1" stop-color="#d1cecd"></stop></linearGradient></defs><path fill="url(#Cc)" d="M667 204c2-1 3-1 4-1-4 9-9 17-12 26-2 5-3 9-4 14 0 2-1 6-2 8h0v1l-1 1v1 1 2 2c-1 1-1 1-2 0-1-6 2-12 3-18v-1-2h-1c0-2 1-2 1-4l1-2v-2c1-4 2-7 3-10 0-3 1-5 1-7 0-1 0-2 1-2h2c-1-1-1-1-2-3l4 3 4-7z"></path><path d="M659 208l4 3c-1 5-4 9-6 14-2 4-3 9-4 13h-1c0-2 1-2 1-4l1-2v-2c1-4 2-7 3-10 0-3 1-5 1-7 0-1 0-2 1-2h2c-1-1-1-1-2-3z" class="I"></path><defs><linearGradient id="Cd" x1="655.492" y1="283.389" x2="638.963" y2="252.95" xlink:href="#B"><stop offset="0" stop-color="#666260"></stop><stop offset="1" stop-color="#918e8e"></stop></linearGradient></defs><path fill="url(#Cd)" d="M655 207c1-3 1-6 1-9l1-1v1c0 2 1 3 1 5l1 1c0 2-1 3 0 4 1 2 1 2 2 3h-2c-1 0-1 1-1 2 0 2-1 4-1 7-1 3-2 6-3 10v2l-1 2c0 2-1 2-1 4h1v2 1c-1 6-4 12-3 18 1 1 1 1 2 0v-2-2-1-1l1-1v-1h0l-1 16c-1 4-2 7-2 11 0 2 0 4-1 5 0-1 0-2-1-4h0v-7c0 3-1 8 0 11 0 2 0 4-1 6-1-1-1-1-2 0l-1 2-1-24c-1-1 0-3-1-4 0-6 0-12 1-18v-2-3l1-7v-1l2-8 2-5v-2c-1-1 0-2 0-2l2-3c1-2 2-4 4-5h1z"></path><defs><linearGradient id="Ce" x1="653.917" y1="279.894" x2="645.851" y2="261.063" xlink:href="#B"><stop offset="0" stop-color="#70706e"></stop><stop offset="1" stop-color="#9a9394"></stop></linearGradient></defs><path fill="url(#Ce)" d="M653 240v1c-1 6-4 12-3 18 1 1 1 1 2 0v-2-2-1-1l1-1v-1h0l-1 16c-1 4-2 7-2 11 0 2 0 4-1 5 0-1 0-2-1-4 1-13 0-26 5-39z"></path><defs><linearGradient id="Cf" x1="645.968" y1="253.16" x2="641.872" y2="252.306" xlink:href="#B"><stop offset="0" stop-color="#989594"></stop><stop offset="1" stop-color="#b8b3b3"></stop></linearGradient></defs><path fill="url(#Cf)" d="M643 240c1-1 2-2 2-3v-1c1 0 1 0 2-1v-1l-2 14c-1 4-1 9 0 13v9c-1-1-1-3-2-4v1c-1-1 0-3-1-4 0-6 0-12 1-18v-2-3z"></path><defs><linearGradient id="Cg" x1="648.106" y1="226.333" x2="659.178" y2="227.519" xlink:href="#B"><stop offset="0" stop-color="#83807e"></stop><stop offset="1" stop-color="#b3b0b0"></stop></linearGradient></defs><path fill="url(#Cg)" d="M655 207c1-3 1-6 1-9l1-1v1c0 2 1 3 1 5l1 1c0 2-1 3 0 4 1 2 1 2 2 3h-2c-1 0-1 1-1 2 0 2-1 4-1 7-1 3-2 6-3 10l-1-1v1-1-4h0c0 3-1 5-1 7-2 6-2 13-4 20-1 1-1 1-1 2v2h-1c-1-2 0-6-1-8l2-14v1c-1 1-1 1-2 1v1c0 1-1 2-2 3l1-7v-1l2-8 2-5v-2c-1-1 0-2 0-2l2-3c1-2 2-4 4-5h1z"></path><path d="M650 212c1-2 2-4 4-5h1c-1 4-3 7-4 11-2 5-3 11-4 16v1c-1 1-1 1-2 1v1c0 1-1 2-2 3l1-7v-1l2-8 2-5v-2c-1-1 0-2 0-2l2-3z" class="o"></path><path d="M648 215l3-1v1c0 1 0 1-1 2h-2c-1-1 0-2 0-2z" class="AA"></path><defs><linearGradient id="Ch" x1="669.301" y1="258.644" x2="660.699" y2="252.356" xlink:href="#B"><stop offset="0" stop-color="#5b5757"></stop><stop offset="1" stop-color="#736d6c"></stop></linearGradient></defs><path fill="url(#Ch)" d="M689 215c0-1 1-1 2-1v2 1c1 2 1 3 1 4v2c-1 1-2 1-2 3v1c-1 2-4 5-5 6h-1c-2 2-4 5-6 7-1 1-1 1-1 2l-5 8-4 8-3 14-1 5c0 2-1 4-1 6l-1 4v6l1 2c-1 2-3 6-3 8-2 1-5 4-6 4v-1c-1-1-1-2-1-3h1v-3-13c0-4-1-8 0-12 0-3 0-7 1-10v1l1-1c0-1 0-2 1-4h0c0-2 0-1 1-2s2-3 3-5l3-6c0-2 2-4 2-6 0-1 0-1 1-2h2c1 1 0 1 1 1 1-3 3-5 5-7 2-5 5-7 8-11-1 1-2 1-3 2v-1l2-2 1-1 2-3 4-3z"></path><path d="M660 271c0-1 1-3 1-4v3-1l1-1h0c0 1 0 2 1 2v1c-1 2-1 4-1 7v-1c-1-1-1-1-1-2 0-2 0-3-1-4z" class="AB"></path><path d="M666 242l1 1-1 1c1 0 1 0 2-1v1c-1 3-3 5-4 9v-2-3c0-2 2-4 2-6z" class="Y"></path><path d="M665 260c0 2-1 4-1 6v7c-1 1-1 2 0 4 0 2-1 4-1 6l-1 4c0-2 1-4 0-6h0v-3c0-3 0-5 1-7 0-4 1-7 2-11z" class="B"></path><path d="M664 248v3 2 1c-2 3-3 5-4 8h-1v-1l-1-1v2l-1-1c0-2 0-1 1-2s2-3 3-5l3-6z" class="V"></path><path d="M667 255c0-1 1-2 1-3 1 1 1 1 0 3v2 1l-3 14-1 5c-1-2-1-3 0-4v-7c0-2 1-4 1-6v-1l2-4z" class="G"></path><path d="M657 261v7h0v-2c1 1 1 2 1 3v-4c1 1 1 2 1 3v3 2 1 1h-1c-1 1-1 1-2 1v-6h-1c0 2 0 3-1 4v1c0-3 0-7 1-10v1l1-1c0-1 0-2 1-4z" class="AB"></path><path d="M675 234c-1 2-1 4-2 7-1 1-1 2-1 3 3-2 5-8 8-10l-2 5c-1 1-1 2-1 3l-5 8-4 8v-1-2c1-2 1-2 0-3 0 1-1 2-1 3h-1c0-2 0-3 1-4 1 0 1-1 1-2 1-2 3-5 2-8 1-3 3-5 5-7z" class="V"></path><defs><linearGradient id="Ci" x1="677.336" y1="224.426" x2="682.101" y2="236.907" xlink:href="#B"><stop offset="0" stop-color="#6c6a6b"></stop><stop offset="1" stop-color="#858180"></stop></linearGradient></defs><path fill="url(#Ci)" d="M683 223c1-1 1-2 2-2 1-1 1-1 2-1 0 1 0 2-1 3 0 1 0 1-1 2l1 1-6 8c-3 2-5 8-8 10 0-1 0-2 1-3 1-3 1-5 2-7 2-5 5-7 8-11z"></path><path d="M689 215c0-1 1-1 2-1v2 1c1 2 1 3 1 4v2c-1 1-2 1-2 3v1c-1 2-4 5-5 6h-1c-2 2-4 5-6 7-1 1-1 1-1 2 0-1 0-2 1-3l2-5 6-8-1-1c1-1 1-1 1-2 1-1 1-2 1-3-1 0-1 0-2 1-1 0-1 1-2 2s-2 1-3 2v-1l2-2 1-1 2-3 4-3z" class="f"></path><path d="M689 215c0-1 1-1 2-1v2 1c1 2 1 3 1 4v2c-1 1-2 1-2 3v1c-1 2-4 5-5 6h-1c1-2 3-3 3-5l3-6c-1-2-1-3-1-6h0v-1z" class="r"></path><path d="M654 275v-1c1-1 1-2 1-4h1v6c1 0 1 0 2-1h1v-1l1-3c1 1 1 2 1 4 0 1 0 1 1 2v1 3h0c1 2 0 4 0 6v6l1 2c-1 2-3 6-3 8-2 1-5 4-6 4v-1c-1-1-1-2-1-3h1v-3-13c0-4-1-8 0-12z" class="AF"></path><path d="M657 300c-1-3 0-9 0-12 1-2 1-4 1-6 1-1 1-3 2-4 0 3-1 8 1 12v1 2h1l1 2c-1 2-3 6-3 8-2 1-5 4-6 4v-1l3-6z" class="F"></path><path d="M661 293h1l1 2c-1 2-3 6-3 8-2 1-5 4-6 4v-1l3-6 4-7z" class="X"></path><path d="M485 448l1 1c4 0 8 0 10 4l1 1v1c2 1 2 1 2 3v1h-1c0 1 0 1 1 2l1 1v1c-1 1 0 1 0 2l-2 2v1l-4 5v1c2-1 5-1 7-1l-3-2c1-1 1-1 2-1l2 2c0 1 1 1 3 2 1 2 4 3 4 6 0 1 1 8 1 9l1 12c0 2 0 5 1 7h-1c0 1 0 2-1 3 1 4 4 10 6 14l5 13 7 16 3 7c1 2 2 5 4 7l20 49 10 24c1 0 1 0 2 1 2-2 4-5 6-8 1 1 2 0 4 0-1 2-4 6-6 6-1 1-3 2-4 3s-3 2-4 3l-2 1v-1l-3-6-4-9c0-2-1-4-2-5l-1-1c-1-3-4-7-5-10h0l-1-1v2c-1-1-4-5-5-6l-9-9c-1-1-3-3-4-3v-1l1-1c-1-1-2-3-3-4l1-1c-5-4-8-9-14-13l1-1-1-2-5-4c0-1-2-2-3-3v-4c-2-1-5-3-6-5h-1l-1-1-4-3c-1 0-1-1-2-1v-4c2 0 3 0 5-1 1-1 1-2 2-3 0 1 1 2 2 2l1-2c-1-1-1-1-1-2l-1-1 5 3v-2l-3-3 2-1 2 1-1-3 1 1 1-1-3-5c0-2-1-5-2-7v-4h-1v2l-1-2-1 1v-1c0-1-1-2-1-3h0l3-12c0-5-1-7-4-10l-2-5v-2h0l2-4c1-1 1-1 1-2-1-1-3-1-4-1 0-3-1-6-2-9 0-1-1-2-2-3l-4-10c0-2-1-5-3-6v-1-4h0 2c0-1 1-1 2-1v-1z" class="AR"></path><path d="M531 575c2 2 3 4 4 6l-2 1-3-5c1-1 1-1 1-2z" class="k"></path><path d="M535 581c1 1 1 2 2 4v1c1 2 2 6 2 8l-6-10v-2l2-1z" class="h"></path><path d="M525 561h2c2 3 5 8 4 11h-1l-5-10v-1z" class="Z"></path><path d="M495 486l6 16c-2 2-2 3-2 5 0-5-1-7-4-10l-2-5v-2h0l2-4z" class="K"></path><path d="M558 635c-1-1-1-3-2-4l-1-2c0-1-1-2-1-3l-3-9c-1-2-2-3-1-5h0c1 3 2 4 5 5h0l10 24c1 0 1 0 2 1 2-2 4-5 6-8 1 1 2 0 4 0-1 2-4 6-6 6-1 1-3 2-4 3s-3 2-4 3h0v-1c-2-3-3-7-5-10z" class="J"></path><path d="M558 635v-3-1c2 1 2 3 3 5l1 2h0c0 1 1 2 2 3l1-1v1c1 0 1 0 2 1 2-2 4-5 6-8 1 1 2 0 4 0-1 2-4 6-6 6-1 1-3 2-4 3s-3 2-4 3h0v-1c-2-3-3-7-5-10z" class="i"></path><path d="M485 448l1 1c4 0 8 0 10 4l1 1v1c2 1 2 1 2 3v1h-1c0 1 0 1 1 2l1 1v1c-1 1 0 1 0 2l-2 2v1l-4 5c-2-3-3-7-5-10l-4-14v-1z" class="I"></path><path d="M485 448l1 1h0c1 1 2 1 3 1l1 1h2s1 0 2 1h-1l-3-1-2 2c0 2 0 3 1 5v1l1 1c0 1 0 2-1 3l-4-14v-1z" class="M"></path><path d="M494 474c2-1 5-1 7-1l-3-2c1-1 1-1 2-1l2 2c0 1 1 1 3 2 1 2 4 3 4 6 0 1 1 8 1 9l1 12c0 2 0 5 1 7h-1c0 1 0 2-1 3-3-6-5-13-7-19-2-3-3-8-5-11l-1-1c-1-2-2-4-2-6h-1z" class="O"></path><path d="M494 474c2-1 5-1 7-1l-3-2c1-1 1-1 2-1l2 2c0 1 1 1 3 2 1 2 4 3 4 6l-1-1c-4-4-8-4-13-5h-1z" class="L"></path><path d="M504 480c2 1 3 2 4 4l1 2h0c1 3 1 7 1 10l-1-1c-1-2-1-4-2-5l-1-1c1-2-1-6-2-9z" class="V"></path><path d="M498 481v-3h0c2 0 5 1 6 2 1 3 3 7 2 9l1 1c1 1 1 3 2 5l1 1c0 4 0 8 1 12 0 1 0 2-1 3-3-6-5-13-7-19-2-3-3-8-5-11z" class="D"></path><path d="M498 481v-3h0c2 0 5 1 6 2h-1c-1 3 0 6 1 8 0 1 1 1 1 2v1l-1 1h-1c-2-3-3-8-5-11z" class="V"></path><path d="M499 507c0-2 0-3 2-5l15 37c1 1 4 6 4 8 2 4 4 9 5 14v1c-3-3-6-7-9-10-4-5-7-9-12-13l1-1-3-5c0-2-1-5-2-7v-4h-1v2l-1-2-1 1v-1c0-1-1-2-1-3h0l3-12z" class="AG"></path><path d="M500 521h1c0-1 1-1 2-2h1c1 1 1 1 1 3h0l9 24c1 2 5 5 5 7 0 1 1 2 2 3l2 3c0-3 0-3-1-5 0-1-1-2-1-2v-1h-1v-4c2 4 4 9 5 14v1c-3-3-6-7-9-10-4-5-7-9-12-13l1-1-3-5c0-2-1-5-2-7v-4h-1v2l-1-2c0-1 0-2 1-2l1 1z" class="T"></path><path d="M500 521h1c0-1 1-1 2-2h1c1 1 1 1 1 3-1-1-1-1-2-1-1 1 0 2-1 3-1-1-1-1-2-3z" class="U"></path><path d="M502 533h1l-1-1v-4h1c1 1 2 3 3 5 1 3 2 4 3 7h0c-2 0-3-1-4-2h0l-3-5z" class="z"></path><path d="M503 538l1 1c5 4 8 8 12 13 3 3 6 7 9 10l5 10 1 3c0 1 0 1-1 2l3 5v2l6 10 13 32-1-1c-1-3-4-7-5-10h0l-1-1v2c-1-1-4-5-5-6l-9-9c-1-1-3-3-4-3v-1l1-1c-1-1-2-3-3-4l1-1c-5-4-8-9-14-13l1-1-1-2-5-4c0-1-2-2-3-3v-4c-2-1-5-3-6-5h-1l-1-1-4-3c-1 0-1-1-2-1v-4c2 0 3 0 5-1 1-1 1-2 2-3 0 1 1 2 2 2l1-2c-1-1-1-1-1-2l-1-1 5 3v-2l-3-3 2-1 2 1-1-3z" class="w"></path><path d="M500 558c1-1 2-1 2-1 3 2 5 4 6 7h0-2l-6-6z" class="W"></path><path d="M496 558v-2l-1-1 1-1c1 1 3 2 4 4l6 6c1 0 1 1 2 2l-1 1-3-3c-2-1-5-3-6-5h-1l-1-1z" class="k"></path><path d="M506 564h2l5 5c2 1 4 3 6 3v1l1 1c0 2 2 3 2 5-2-1-2-2-3-3s-2-1-3-1c-2-1-3-2-4-3 0-2-3-4-5-5l1-1c-1-1-1-2-2-2z" class="h"></path><path d="M498 543l5 3 5 4 6 7 1 2 1 2-1 2c2 3 5 6 6 9l-2 1v-1l-6-8c-4-4-7-8-11-12-1-1-2-3-4-3v2l-1 1c-2 0-3 0-4 1 0 1-1 1-1 2-1 0-1-1-2-1v-4c2 0 3 0 5-1 1-1 1-2 2-3 0 1 1 2 2 2l1-2c-1-1-1-1-1-2l-1-1z" class="C"></path><path d="M515 563l-3-4 1-1 2 1 1 2-1 2z" class="l"></path><path d="M503 538l1 1c5 4 8 8 12 13 3 3 6 7 9 10l5 10 1 3c0 1 0 1-1 2l3 5v2l-1 1c1 0 1 1 1 2v1c1 1 1 1 1 3h-1l-12-19c-1-3-4-6-6-9l1-2-1-2-1-2-6-7-5-4v-2l-3-3 2-1 2 1-1-3z" class="Aa"></path><path d="M502 540l2 1 2 3c2 2 3 3 4 5v1h-2l-5-4v-2l-3-3 2-1z" class="t"></path><path d="M502 540l2 1 2 3v1c-1 0-2-1-3-1l-3-3 2-1z" class="AP"></path><path d="M514 557c6 5 12 12 16 20l3 5v2l-1 1c1 0 1 1 1 2v1c1 1 1 1 1 3h-1l-12-19c-1-3-4-6-6-9l1-2-1-2-1-2z" class="K"></path><path d="M514 557c6 5 12 12 16 20l3 5v2l-1 1c-5-9-10-16-16-24l-1-2-1-2z" class="R"></path><path d="M504 564l3 3c2 1 5 3 5 5 1 1 2 2 4 3 1 0 2 0 3 1s1 2 3 3c0-2-2-3-2-5l-1-1 2-1 12 19h1c0-2 0-2-1-3v-1c0-1 0-2-1-2l1-1 6 10 13 32-1-1c-1-3-4-7-5-10h0l-1-1v2c-1-1-4-5-5-6l-9-9c-1-1-3-3-4-3v-1l1-1c-1-1-2-3-3-4l1-1c-5-4-8-9-14-13l1-1-1-2-5-4c0-1-2-2-3-3v-4z" class="l"></path><path d="M526 591c5 4 13 12 14 19l-9-9c-1-1-3-3-4-3v-1l1-1c-1-1-2-3-3-4l1-1zm-22-27l3 3c2 1 5 3 5 5 1 1 2 2 4 3 1 0 2 0 3 1s1 2 3 3c0-2-2-3-2-5l-1-1 2-1 12 19h1v1c0 3 2 5 3 8-2-1-2-3-4-5l-4-4c-1-2-3-3-4-5l-3-2c-2-1-3-2-4-3l-5-4-1-2-5-4c0-1-2-2-3-3v-4z" class="C"></path><path d="M504 564l3 3c2 1 5 3 5 5h-1c1 3 7 5 7 9l-5-4-1-2-5-4c0-1-2-2-3-3v-4z" class="J"></path><path d="M519 573l2-1 12 19h-1c-1-1-2-1-2-2-2-2-3-3-5-4-1-2-2-3-3-4-2-3-4-4-6-6 1 0 2 0 3 1s1 2 3 3c0-2-2-3-2-5l-1-1z" class="w"></path><path d="M480 199c1-1 2-1 3-1l7 8 6 10c1 2 3 5 3 7l-3-3 3 8c2 3 4 6 5 10 1 2 1 3 3 5l3 16v2c0 2 0 1 1 2 1 3 1 9 1 13 0 5 0 10-1 15l-1 12h0l1 2-1 6h1l1-1c0-1 0-1 1-1v1l2 1 1 3v2l-1-2h-1v1c0 1-1 2-1 3v1c-1 3-3 4-3 7l-1 1c-1-1-1-1-1 0l-2 1c-1 2-2 4-4 5l-1 1c-1 1-1 2-1 2 0 1-1 1-1 2-3-1-6 0-9 0h0c-6 4-13 8-21 7-6-1-9-4-13-8v-2c-1-2-1-3-1-5l2 2v-1-1l1-2c1-2 2-3 4-3l2-2 2 2c1 0 1 1 2 1v1c2 0 3-1 4-2h1c7-5 9-12 11-19-1-4 0-7-1-10-1-14-7-25-15-37h2v-5c-1-1 0-2 0-4v-1l3-4 2-4-2 1h-1-3c-2 1-3 1-4 1h-1c-3 1-8 0-12 0-2 0-4 0-6-1l-3-3c-4-3-7-7-9-12v-6l-1-4v-6-1c1-2 2-4 4-5h1c1-1 2-1 3-1l3-1h3 5 0c4-2 7-2 10-1 6-1 11 0 16-1 1-1 1-1 1-2h1z" class="AF"></path><path d="M488 236c2 1 2 2 4 2 1 1 2 4 3 6h0v3c-2-4-4-7-7-11z" class="x"></path><path d="M494 267v3l2-1c0 1 1 1 1 2 0 2 1 3 2 4l2 5-3-2c-2-3-5-4-7-6 1-2 2-3 3-5z" class="X"></path><path d="M483 264l6 2h3 1l1 1c-1 2-2 3-3 5l-3-2-2-1c0 2 0 3 1 4l-1 1v-1h-1c0-1-1-2-1-3l-1-1c1 0 0-1 0-2s0-1-1-2h0l1-1z" class="L"></path><path d="M492 266h1l1 1c-1 2-2 3-3 5l-3-2h1v-2c1-1 2-1 3-2z" class="d"></path><path d="M494 254l4 3v1c0 2 1 4 0 6v1c0 1-1 2-1 3l1 1c1 2 1 4 1 6-1-1-2-2-2-4 0-1-1-1-1-2l-2 1v-3l-1-1h-1-3l2-1c2-2 2-4 3-7v-1c-1-1-1-2-1-4l1 1z" class="D"></path><path d="M494 254l4 3v1c0 2 1 4 0 6-1-1-1-3-2-4-1-2-1-2-2-2v-1c-1-1-1-2-1-4l1 1z" class="B"></path><path d="M478 230l3 2c7 4 15 16 17 25h0l-4-3v-2h0l-1-1c-1-1-3-2-5-3 0-1 1-1 1-2l-1-1 3-1c-1-1-3-5-4-5 0 0-1-1-2-1-1-1-2-1-3-1h0c-1-1-2-1-3-2s-1-1-1-2v-3z" class="Q"></path><path d="M488 248c0-1 1-1 1-2l-1-1 3-1c1 2 3 5 4 8h-1 0l-1-1c-1-1-3-2-5-3z" class="I"></path><path d="M488 275h2c0 2 1 4 1 6 0 3 2 7 2 10v2 9 2 5c-1 2-1 3-1 4 0 4-3 7-5 10 0 1-1 2-2 2v1c-1 1-2 2-3 2l3-7h0c2-3 3-6 4-9 1-2 0-5 1-6 1-4 0-7 1-11v6 1c0-3 1-7 0-11-1-6-1-11-3-16z" class="M"></path><path d="M483 252c2-1 4-1 6 0 2 0 3 1 4 1 0 2 0 3 1 4v1c-1 3-1 5-3 7l-2 1-6-2v-1l-1-1c-2-1-2-2-3-4l1-1 1-1c0-1 1-3 1-4h1z" class="d"></path><path d="M482 252h1c1 0 2 0 3 1-1 1-2 2-2 4l-1-1h-1c-1 1-1 1-2 1l1-1c0-1 1-3 1-4z" class="Q"></path><path d="M491 258v4c-1 0-1 1-1 2h-2c0-2-1-2-1-5 1 0 2 0 3-1h1z" class="I"></path><path d="M483 252c2-1 4-1 6 0 2 0 3 1 4 1 0 2 0 3 1 4v1c-1 3-1 5-3 7l-2 1-6-2v-1c2 1 3 1 5 1h2c0-1 0-2 1-2v-4l-1-1c0-1-2-3-4-4-1-1-2-1-3-1z" class="O"></path><path d="M488 275c0-2-1-3-1-4 6 3 10 6 14 11-1 5 0 10-1 15v-1c-1 0-1-1-1-1l-1-1c-1 2 1 5-1 7v3 2h-3v-1c-1 0 0 0-1-1v-2-9-2c0-3-2-7-2-10 0-2-1-4-1-6h-2z" class="AD"></path><path d="M478 233h0c0 1 0 1 1 2s2 1 3 2h0c1 0 2 0 3 1 1 0 2 1 2 1 1 0 3 4 4 5l-3 1 1 1c0 1-1 1-1 2 2 1 4 2 5 3l1 1h0v2l-1-1c-1 0-2-1-4-1-2-1-4-1-6 0h-1c0 1-1 3-1 4l-1 1-1 1v-1c-1-1-1-1-2-1-1-1-1-4-2-6 0-1 1-2 1-3-1-2-2-2-3-2l2-4h1c1-2 1-3 0-5l2-3z" class="M"></path><path d="M479 257c-1-2-1-3-1-4 1-2 1-3 3-4v3h1c0 1-1 3-1 4l-1 1-1 1v-1z" class="o"></path><path d="M481 249c2-2 4-1 7-1 2 1 4 2 5 3l1 1h0v2l-1-1c-1 0-2-1-4-1-2-1-4-1-6 0h-1-1v-3z" class="AA"></path><path d="M501 282v-4c0-2 1-4 0-6v-3c-1-1-1-1-1-2v-2-1h1c3 16 2 33-4 48-5 11-12 22-24 25-3 1-8 1-11 0-1-1 0-1-1-1s-1-1-1-1l-1-1c-1-2-1-4-1-6 1-2 2-3 4-3l2-2 2 2c1 0 1 1 2 1l-2 2h1l-2 1 1 2c1 1 2 1 4 1l-1 1v1h2c2 1 3 0 5-1s4-3 6-5c1 0 2-1 3-2v-1c1 0 2-1 2-2 2-3 5-6 5-10 0-1 0-2 1-4v-5c1 1 0 1 1 1v1h3v-2-3c2-2 0-5 1-7l1 1s0 1 1 1v1c1-5 0-10 1-15z" class="B"></path><path d="M487 323l1 2c-4 4-8 10-14 11l2-3c2-1 4-3 6-5 1 0 2-1 3-2v-1c1 0 2-1 2-2z" class="AD"></path><path d="M493 304c1 1 0 1 1 1v1h3v-2-3c2-2 0-5 1-7l1 1s0 1 1 1v1c0 3-2 6-2 9 0 2-1 4-2 5-2 5-5 11-8 14l-1-2c2-3 5-6 5-10 0-1 0-2 1-4v-5z" class="n"></path><path d="M458 328c1-2 2-3 4-3l2-2 2 2c1 0 1 1 2 1l-2 2h1l-2 1 1 2c1 1 2 1 4 1l-1 1v1h2c2 1 3 0 5-1l-2 3-3 1c-4 1-7 0-10-1-1 0-1-1-1-1l-1-1c-1-2-1-4-1-6z" class="d"></path><path d="M458 328c1-2 2-3 4-3l2-2 2 2c1 0 1 1 2 1l-2 2h1l-2 1 1 2h-1l-2-2c-1 0-2 1-4 2 1 1 1 2 1 4l-1-1c-1-2-1-4-1-6z" class="AD"></path><defs><linearGradient id="Cj" x1="461.257" y1="280.658" x2="493.743" y2="301.342" xlink:href="#B"><stop offset="0" stop-color="#605f61"></stop><stop offset="1" stop-color="#898482"></stop></linearGradient></defs><path fill="url(#Cj)" d="M473 245c1 0 2 0 3 2 0 1-1 2-1 3 1 2 1 5 2 6 1 0 1 0 2 1v1c1 2 1 3 3 4l1 1v1l-1 1h0c1 1 1 1 1 2s1 2 0 2l1 1c0 1 1 2 1 3h1v1l1-1c0 2 1 3 1 4v1c2 5 3 12 3 17-1 4 0 7-1 11-1 1 0 4-1 6-1 3-2 6-4 9h0l-3 7c-2 2-4 4-6 5s-3 2-5 1h-2v-1l1-1c-2 0-3 0-4-1l-1-2 2-1h-1l2-2v1c2 0 3-1 4-2h1c7-5 9-12 11-19-1-4 0-7-1-10-1-14-7-25-15-37h2v-5c-1-1 0-2 0-4v-1l3-4z"></path><path d="M484 306c1 2 0 5-1 8l-1 4c-3 5-4 10-10 12-1 1-2 1-4 1 1-1 0-1 1-1 0-1 0-1-1-1h-1v-1h-1l2-2v1c2 0 3-1 4-2h1c7-5 9-12 11-19z" class="M"></path><path d="M473 245c1 0 2 0 3 2 0 1-1 2-1 3 1 2 1 5 2 6 1 0 1 0 2 1v1c1 2 1 3 3 4l1 1v1l-1 1h0c1 1 1 1 1 2s1 2 0 2l-1 1v5l-4-7-2-3c-1-2-3-5-4-7-1-1-1-2-2-4-1-1 0-2 0-4v-1l3-4z" class="H"></path><path d="M470 249c1 1 3 2 3 3 1 4 2 8 3 13-1-2-3-5-4-7-1-1-1-2-2-4-1-1 0-2 0-4v-1z" class="Y"></path><path d="M475 250c1 2 1 5 2 6 1 0 1 0 2 1v1c1 2 1 3 3 4l1 1v1l-1 1h0c1 1 1 1 1 2s1 2 0 2l-1 1v5l-4-7c-1-6-3-12-3-18z" class="X"></path><defs><linearGradient id="Ck" x1="470.226" y1="281.238" x2="496.01" y2="312.148" xlink:href="#B"><stop offset="0" stop-color="#a8a5a7"></stop><stop offset="1" stop-color="#c6c2bf"></stop></linearGradient></defs><path fill="url(#Ck)" d="M483 269l1 1c0 1 1 2 1 3h1v1l1-1c0 2 1 3 1 4v1c2 5 3 12 3 17-1 4 0 7-1 11-1 1 0 4-1 6-1 3-2 6-4 9h0l-3 7c-2 2-4 4-6 5s-3 2-5 1h-2v-1l1-1 5-2c3-3 6-5 8-9v-1l2-3c5-13 2-29-3-42v-5l1-1z"></path><path d="M475 330l1 1c2 0 4-4 5-6 1-1 3-2 4-4l-3 7c-2 2-4 4-6 5s-3 2-5 1h-2v-1l1-1 5-2z" class="Q"></path><path d="M497 251h1c1 2 2 5 4 7v-3c3 11 6 22 4 33h1c0-7 1-14 1-21l1-1v-1c1-2 1-4 1-6v2c0 2 0 1 1 2 1 3 1 9 1 13 0 5 0 10-1 15l-1 12h0l1 2-1 6h1l1-1c0-1 0-1 1-1v1l2 1 1 3v2l-1-2h-1v1c0 1-1 2-1 3v1c-1 3-3 4-3 7l-1 1c-1-1-1-1-1 0l-2 1c-1 2-2 4-4 5l-1 1c-1 1-1 2-1 2 0 1-1 1-1 2-3-1-6 0-9 0h0c-6 4-13 8-21 7-6-1-9-4-13-8v-2c-1-2-1-3-1-5l2 2v-1-1l1-2c0 2 0 4 1 6l1 1s0 1 1 1 0 0 1 1c3 1 8 1 11 0 12-3 19-14 24-25 6-15 7-32 4-48l-4-13z" class="u"></path><path d="M513 310l2 1 1 3v2l-1-2h-1v1c0 1-1 2-1 3v1c-1 3-3 4-3 7l-1 1c-1-1-1-1-1 0l-2 1c-1 2-2 4-4 5l-1 1c1-3 4-5 5-8 3-5 5-10 7-16z" class="B"></path><defs><linearGradient id="Cl" x1="481.153" y1="302.029" x2="482.986" y2="343.626" xlink:href="#B"><stop offset="0" stop-color="#a69c97"></stop><stop offset="1" stop-color="#d7d1cd"></stop></linearGradient></defs><path fill="url(#Cl)" d="M497 312h1c1-2 2-2 2-4v-1c2-1 2-2 2-4 1-1 1-2 2-3 0-1 0-1 1-2 0 0 0 1 1 1 0 1 0 3-1 4 0 2-2 4-1 6 0-1 1-3 2-4v2h1v2-1c-1 0-2 1-2 2-1 1-1 3-1 5-3 8-7 17-14 23h0c-6 4-13 8-21 7-6-1-9-4-13-8v-2c-1-2-1-3-1-5l2 2v-1-1l1-2c0 2 0 4 1 6l1 1s0 1 1 1 0 0 1 1c3 1 8 1 11 0 12-3 19-14 24-25z"></path><path d="M480 199c1-1 2-1 3-1l7 8 6 10c1 2 3 5 3 7l-3-3 3 8c2 3 4 6 5 10 1 2 1 3 3 5l3 16c0 2 0 4-1 6v1l-1 1c0 7-1 14-1 21h-1c2-11-1-22-4-33v3c-2-2-3-5-4-7h-1c-1-2-1-3-2-4v-3h0c-1-2-2-5-3-6-2 0-2-1-4-2-1 0-1-1-2-1-1-2-3-3-5-4v1l-3-2v3h0l-2 3c1 2 1 3 0 5h-1l-2 1h-1-3c-2 1-3 1-4 1h-1c-3 1-8 0-12 0-2 0-4 0-6-1l-3-3c-4-3-7-7-9-12v-6l-1-4v-6-1c1-2 2-4 4-5h1c1-1 2-1 3-1l3-1h3 5 0c4-2 7-2 10-1 6-1 11 0 16-1 1-1 1-1 1-2h1z" class="B"></path><path d="M454 232l1-1c1-1 2-2 3-2h0l-1 5-3-1v-1z" class="E"></path><path d="M453 226c2-2 3-6 5-7 0 4-1 8-5 10h0c0-1 0-1-1-3h1z" class="L"></path><path d="M451 223c1-1 2-4 2-5 2 1 2 1 3 1h2 0c-2 1-3 5-5 7l-1-2-1-1z" class="r"></path><path d="M479 223c4 1 8 5 9 8l-1 1c-2-2-5-3-8-5v-1c0-1 0-1-1-1s0 0-1-1c1 0 2 0 2-1z" class="V"></path><path d="M504 246c-4-3-6-9-9-13-2-3-5-6-6-8v-1l4 4h1 0c3 5 6 9 8 13l2 5z" class="AB"></path><path d="M496 228h3c2 3 4 6 5 10h-1v2l-1 1c-2-4-5-8-8-13h2z" class="AA"></path><path d="M496 228h3c2 3 4 6 5 10h-1l-4-6c-1-2-2-3-3-4z" class="AJ"></path><path d="M487 232l1-1c5 6 10 13 13 20 0 1 1 3 1 4v3c-2-2-3-5-4-7h-1c-1-2-1-3-2-4v-3h0c-1-2-2-5-3-6l-5-6z" class="D"></path><path d="M458 229c1-1 1-1 1-3 4-1 8 2 11 2 1 0 2 1 2 1 2 0 3 1 5 1h1v3h0l-2-1c-1 0-2 1-2 1h-1l-6-1c-3-1-5-1-7-1v-1h-1l-1-1h0z" class="V"></path><path d="M472 229c2 0 3 1 5 1h1v3h0l-2-1c-1 0-2 1-2 1h-1 0l-1-1-1-1h-3 0c-1-1-1-1-2-1 1-1 0-1 2-1v1c2 0 3 0 4-1z" class="D"></path><path d="M471 225v-1l3 1c1 0 2 1 3 1l2 1c3 2 6 3 8 5l5 6c-2 0-2-1-4-2-1 0-1-1-2-1-1-2-3-3-5-4l-12-4-8-2c2-1 5-1 7-1 1 0 1 1 2 2l1-1z" class="n"></path><path d="M471 225v-1l3 1c1 0 2 1 3 1l2 1c3 2 6 3 8 5l5 6c-2 0-2-1-4-2-1 0-1-1-2-1v-1c-4-5-10-8-15-9z" class="p"></path><defs><linearGradient id="Cm" x1="503.79" y1="262.432" x2="508.941" y2="242.127" xlink:href="#B"><stop offset="0" stop-color="#a09e9e"></stop><stop offset="1" stop-color="#cac4c2"></stop></linearGradient></defs><path fill="url(#Cm)" d="M503 238h1c1 2 1 3 3 5l3 16c0 2 0 4-1 6v1l-1 1c-1-7-2-14-4-21l-2-5 1-1v-2z"></path><path d="M461 225h-2c1-1 1-2 1-3 1-1 2-3 3-3 4-2 12 2 16 4 0 1-1 1-2 1 1 1 0 1 1 1s1 0 1 1v1l-2-1c-1 0-2-1-3-1l-3-1v1l-1 1c-1-1-1-2-2-2-2 0-5 0-7 1z" class="Y"></path><defs><linearGradient id="Cn" x1="467.206" y1="211.34" x2="468.329" y2="219.348" xlink:href="#B"><stop offset="0" stop-color="#141212"></stop><stop offset="1" stop-color="#353232"></stop></linearGradient></defs><path fill="url(#Cn)" d="M453 217c2-3 5-5 9-5 7-2 14 1 20 5h1l-1 1v1h0c-3-2-8-3-11-4h-1c-2-1-5 0-7 0-2 1-4 2-5 3l-2 1c-1 0-1 0-3-1v-1z"></path><path d="M458 229l1 1h1v1c2 0 4 0 7 1l6 1h1s1-1 2-1l2 1-2 3-1 1c-4 0-7 0-11-2h-4c-1 1-2 0-2 1-1 2-2 3-3 5-2 0-2 1-3 2-2 0-4 0-6-1 2 0 3 0 5-1l1-2c1-2 2-4 2-6l3 1 1-5z" class="G"></path><path d="M454 233l3 1c-1 2-2 5-4 5h-1c1-2 2-4 2-6z" class="F"></path><path d="M460 231c2 0 4 0 7 1l6 1 1 1 1 1h-1c-3 1-8 0-11-1-1-1-1-1-3 0h0l-1-1h1v-1h0v-1z" class="I"></path><defs><linearGradient id="Co" x1="460.343" y1="241.339" x2="470.422" y2="234.994" xlink:href="#B"><stop offset="0" stop-color="#7d7675"></stop><stop offset="1" stop-color="#978e88"></stop></linearGradient></defs><path fill="url(#Co)" d="M460 235h4c4 2 7 2 11 2l1-1c1 2 1 3 0 5h-1l-2 1h-1-3c-2 1-3 1-4 1h-1c-3 1-8 0-12 0 1-1 1-2 3-2 1-2 2-3 3-5 0-1 1 0 2-1z"></path><path d="M452 243c1-1 1-2 3-2 1-2 2-3 3-5 0-1 1 0 2-1l-1 1c0 1 0 1-1 2l1 2-1 1h1c2 0 3 0 4 1h1c1 0 3-1 5 0-2 1-3 1-4 1h-1c-3 1-8 0-12 0z" class="x"></path><path d="M480 199c1-1 2-1 3-1l7 8 6 10c1 2 3 5 3 7l-3-3 3 8h-3-2 0c-3-5-7-8-11-11h-1c-6-4-13-7-20-5-4 0-7 2-9 5v1c0 1-1 4-2 5l1 1 1 2h-1c1 2 1 2 1 3h0v3h1v1c0 2-1 4-2 6l-1 2c-2 1-3 1-5 1l-3-3c-4-3-7-7-9-12v-6l-1-4v-6-1c1-2 2-4 4-5h1c1-1 2-1 3-1l3-1h3 5 0c4-2 7-2 10-1 6-1 11 0 16-1 1-1 1-1 1-2h1z" class="d"></path><path d="M442 222c1-3 2-6 4-7v1c0 1 0 5 1 6 0 1 1 1 2 2l2-1 1 1 1 2h-1c-1 1-3 1-5 1-1 0-2-1-4-2l-1-3z" class="E"></path><path d="M479 199h1v1 1c1 2 2 4 4 5l7 6-1 1c-2-2-5-5-7-6-3-1-5-2-7-3-1 0-2-1-2-2-4-1-8 1-11 0h-1c6-1 11 0 16-1 1-1 1-1 1-2z" class="Q"></path><path d="M479 199h1v1 1c1 2 2 4 4 5l-5-3c-1-1-1-1-1-2 1-1 1-1 1-2z" class="M"></path><path d="M480 199c1-1 2-1 3-1l7 8 6 10c-2-1-4-3-5-4l-7-6c-2-1-3-3-4-5v-1-1z" class="u"></path><defs><linearGradient id="Cp" x1="440.898" y1="214.108" x2="439.92" y2="205.644" xlink:href="#B"><stop offset="0" stop-color="#89807e"></stop><stop offset="1" stop-color="#a49c98"></stop></linearGradient></defs><path fill="url(#Cp)" d="M437 205h1c1-1 2-1 3-1l3-1h3 5c-1 1 0 1-2 1h1-1c-4 2-8 5-10 9-1 1-1 2-2 3h0l-2 4-2 1-1-4v-6-1c1-2 2-4 4-5z"></path><path d="M433 217c2-1 3-1 5-1l-2 4-2 1-1-4z" class="E"></path><path d="M436 220l1 1c2-1 3-3 5-3v4l1 3c2 1 3 2 4 2 2 0 4 0 5-1 1 2 1 2 1 3h0v3h1v1c0 2-1 4-2 6l-1 2c-2 1-3 1-5 1l-3-3c-4-3-7-7-9-12v-6l2-1z" class="AD"></path><path d="M443 235c-1-2-2-3-5-3v-1c-1-1-1-1-1-2 2-1 4 1 6 2h1l1 3c-1 0-1 1-2 1z" class="D"></path><path d="M443 225c2 1 3 2 4 2 2 0 4 0 5-1 1 2 1 2 1 3-3 2-4 2-7 2-2-2-6-4-6-6h1l1 1 1-1z" class="Q"></path><path d="M453 229h0v3h1v1c0 2-1 4-2 6l-1 2c-2 1-3 1-5 1l-3-3h0v-1h0l-1-2 1-1c1 0 1-1 2-1l-1-3h2c3 0 4 0 7-2z" class="I"></path><defs><linearGradient id="Cq" x1="465.979" y1="227.809" x2="477.199" y2="206.805" xlink:href="#B"><stop offset="0" stop-color="#b4afac"></stop><stop offset="1" stop-color="#d9d4d1"></stop></linearGradient></defs><path fill="url(#Cq)" d="M453 217h-2l-1-1c0-1 2-3 2-4-1-1-2 0-3-1-2 1-3 2-5 3 0 1-1 2-2 2h-1v-2l2-3c2-2 4-3 7-5 3-1 5-2 9-2 4-1 9-1 14 0l1 1c7 2 12 6 18 11l4 4h0c1 3 2 5 3 8h-3-2 0c-3-5-7-8-11-11h-1c-6-4-13-7-20-5-4 0-7 2-9 5z"></path><defs><linearGradient id="Cr" x1="546.883" y1="234.858" x2="527.401" y2="266.267" xlink:href="#B"><stop offset="0" stop-color="#757070"></stop><stop offset="1" stop-color="#9c9a9a"></stop></linearGradient></defs><path fill="url(#Cr)" d="M555 196s1-1 3-1c-1 1-1 2-1 3h-1c1 1 1 2 2 2l1 1c1 0 3 0 4 1l-1 1c9 0 23-2 30 3l3 3h0c0 1-1 2-1 2-3-1-5-2-7-3l2 2c-1 1-2 1-4 2 1 2 2 4 3 5 0 1 0 1 1 1 0 1 1 2 1 4l2-1-1-1-1-5c2 4 5 7 4 12 0 1-1 3-1 4-1 3-3 5-7 6 0 1-3 1-3 1v1 1l-3 1c-2 0-3 0-5 1h-1c-2-1-3-1-5-2-1 1-2 0-3 0-1-1-1-1-2-1v1c2 2 4 3 6 5v3l-1 4-3-1h-1c1 1 2 1 2 2-1 3-4 6-5 8-2 2-4 5-6 7-1 4-3 8-4 13-1 2-1 4-2 6-1 11-2 21 3 32 1 2 2 5 3 7 2 2 4 4 7 6 1 1 4 1 6 1h1 1c-1 2-4 3-6 4h5v1c3 0 5-2 7-4v1c1-1 2-2 2-4v4c-1 3-3 5-5 7l-6 3h0-2c-7 1-15-3-20-7l-5-1c-2-2-2-2-4-3h-3-1-3l-5 1c-1 2-3 3-4 5s-2 4-3 7v3h-1l-1-2c-1-5-3-8-8-11-2-2-3-3-6-4 2-1 3-3 4-5l2-1c0-1 0-1 1 0l1-1c0-3 2-4 3-7v-1c0-1 1-2 1-3v-1h1l1 2v-2l-1-3-2-1v-1c1-2 1-6 2-7 2-7 2-15 2-21v-10h1l2 20 1-2v-8l1-27c2-3 3-6 3-9h1c0 2 0 5 1 7 0-2 1-3 1-4l5-15v-4l1-4 2-2c1-2 2-4 2-7 1-1 3-3 4-5 0-1 1-2 2-3l11-12z"></path><path d="M548 231c-1 0-1 0-1-1 1-1 1-2 3-4 3-3 6-4 10-6 2-1 2-1 4-1l1 1c-5 2-8 4-12 7-2 1-4 2-5 4z" class="P"></path><path d="M548 233v1c0 1-1 2-2 3l1 1c1-1 1-2 2-2-6 8-12 17-15 27v1 2l-1 2-1-1 1-2c2-8 5-19 11-26l4-6z" class="f"></path><path d="M550 236c0 4-4 7-5 11 0 1-1 1-1 2 1 0 3-1 3-2h1c1 0 2 0 3 1h-2c-2 0-6 3-7 5l-1 1v1c-2 4-4 7-6 12l-1-1v-2-1c3-10 9-19 15-27h1z" class="I"></path><path d="M534 266l1 1c-1 3-1 6-1 10v9c1 3 1 6 1 8-1 2-1 4-1 6 0 1 0 1 1 1 1 3 1 6 3 9l-1 1c0-1-1-1-1-1 0-1-1-2-1-3-1-2-1-3-1-4v-1h-1c0-2-1-5-1-7v-2h1l-1-7c-1-5-2-14 0-19l1 1 1-2z" class="n"></path><path d="M534 266l1 1c-1 3-1 6-1 10 0 1 0 2-1 3v-12l1-2z" class="AD"></path><path d="M548 247v-1c2-1 3-1 6-1l2 2c0 3-1 5-1 7h1l1-1c0 3-1 5-2 8l-3 3v1c-1 1-3 1-4 1s-1-1-1-1c-2-1-2-1-3-3h-1v3c0 1 0 1-1 1-1 2-4 8-4 10l-1 1-1 1v-1l-1 1h0l-1 8v-9c0-4 0-7 1-10 2-5 4-8 6-12v-1l1-1c1-2 5-5 7-5h2c-1-1-2-1-3-1z" class="AB"></path><path d="M537 277h0c-1-4 2-10 3-13 0-2 0-5 1-6 1-2 4-2 5-4 1-1 2-1 3-2l-1 3c-1 1-1 2-1 3l-1 1v2c0 1 0 2 1 2 1 2 3 2 5 1h0v1c-1 1-3 1-4 1s-1-1-1-1c-2-1-2-1-3-3h-1v3c0 1 0 1-1 1-1 2-4 8-4 10l-1 1z" class="D"></path><path d="M548 247v-1c2-1 3-1 6-1l2 2c0 3-1 5-1 7h1l1-1c0 3-1 5-2 8l-3 3h0c-2 1-4 1-5-1-1 0-1-1-1-2v-2l1-1c0-1 0-2 1-3l1-3 2-1h0c-1-1-4 0-6 0-1 1-3 2-4 3l1-1c1-2 5-5 7-5h2c-1-1-2-1-3-1z" class="Q"></path><path d="M549 252l2-1 1 1c0 2-1 3-1 4-1 1-1 1-2 1l-1-2 1-3z" class="AJ"></path><path d="M548 255l1 2c1 0 1 0 2-1 1 0 2-1 3-1v1c-2 2-3 4-7 5h-1v-2l1-1c0-1 0-2 1-3z" class="L"></path><path d="M554 233l1-1c2-1 2 0 4 0l2 1h1v1 1c1-1 2-1 2-1 2 0 3 0 5 1h1l1 2-2-1-1 1c-2-1-3-1-5 0l-1 1c1 1 1 1 2 1v1c2 2 4 3 6 5v3l-1 4-3-1h-1c1 1 2 1 2 2-1 3-4 6-5 8-2 2-4 5-6 7 0-1 0-2 1-3v-1l-1-1 1-1c0-3 2-5 3-6v-5l-1-1s1-1 1-2v-1-1l-1-1c0 2 1 5 0 6v1c0 1 0 2-1 3h0l-1 4-2 2c1-3 2-5 2-8l-1 1h-1c0-2 1-4 1-7l-2-2c-3 0-4 0-6 1v1h-1c0 1-2 2-3 2 0-1 1-1 1-2 1-4 5-7 5-11 1-1 2-2 4-3z" class="X"></path><path d="M561 233h1v1 1c1-1 2-1 2-1 2 0 3 0 5 1h1l1 2-2-1h-8 0v-1-2z" class="AB"></path><path d="M556 247c1 0 0 0 1-1l-2-2c-1 0-1-1-2-1 1-2 2-1 3-1 1 1 2 3 2 5s0 4-1 6l-1 1h-1c0-2 1-4 1-7z" class="d"></path><path d="M570 248l-4 1h-1c0-2 0-3-1-4v-1c-1-1-1-3 0-4 2 2 4 3 6 5v3z" class="AF"></path><path d="M554 233l1-1c2-1 2 0 4 0l2 1v2h-2l-2 2h0l-1 1 1 1v1l-1 1v1c-1 0-2-1-3 1 1 0 1 1 2 1l2 2c-1 1 0 1-1 1l-2-2c-3 0-4 0-6 1v1h-1c0 1-2 2-3 2 0-1 1-1 1-2 1-4 5-7 5-11 1-1 2-2 4-3z" class="M"></path><path d="M554 233l1-1c2-1 2 0 4 0l2 1v2h-2l-2 2c-1 0-2 0-4-1l1-3z" class="o"></path><defs><linearGradient id="Cs" x1="590.236" y1="219.002" x2="521.089" y2="253.764" xlink:href="#B"><stop offset="0" stop-color="#1c1a1a"></stop><stop offset="1" stop-color="#535150"></stop></linearGradient></defs><path fill="url(#Cs)" d="M544 226c5-6 11-10 19-13 6-3 11-3 17 0 2 2 4 4 5 6 1 0 2 0 4-1 0 1 1 2 1 4l2-1-1-1-1-5c2 4 5 7 4 12 0 1-1 3-1 4h-3v-1-1c0-2-4-3-5-4l-3-3c-2-4-4-4-8-4l1-1h2c-3-2-7-1-10-1h-1c-7 1-17 8-21 14l-2 2c-9 14-16 35-12 53 0 2 1 6 1 8v2l-3-15v-7c-1-4-1-9 0-13 1-8 3-16 7-23 2-4 5-8 8-11z"></path><path d="M589 218c0 1 1 2 1 4-1 0-1 0-2 1-2-1-2-2-3-4 1 0 2 0 4-1z" class="Q"></path><path d="M574 218c4 0 6 0 8 4l3 3c1 1 5 2 5 4v1 1h3c-1 3-3 5-7 6 0 1-3 1-3 1v1 1l-3 1c-2 0-3 0-5 1h-1c-2-1-3-1-5-2-1 1-2 0-3 0-1-1-1-1-2-1s-1 0-2-1l1-1c2-1 3-1 5 0l1-1 2 1-1-2h-1c-2-1-3-1-5-1 0 0-1 0-2 1v-1-1h-1l-2-1c-2 0-2-1-4 0l-1 1c-2 1-3 2-4 3h-1c-1 0-1 1-2 2l-1-1c1-1 2-2 2-3v-1-1-1c1-2 3-3 5-4 4-3 7-5 12-7 2-1 4-1 6-2h3z" class="Y"></path><path d="M574 227c2 0 7-1 9-1 2 2 2 1 4 2h1l1 2-1 1h1c-1 1-3 1-4 1-1-1-1-1-1-3-1-1-1-1-3-1h0c-1-1-4 0-6 0l-1-1z" class="p"></path><path d="M564 234c3-1 6-1 9 0h3v1h-1c-1 0-2 0-2 1h0c2 0 4 1 6 1h2 2 1 2c0 1-3 1-3 1-4 1-8 0-12-1l-1-2h-1c-2-1-3-1-5-1z" class="G"></path><path d="M564 239c-1 0-1 0-2-1l1-1c2-1 3-1 5 0l1-1 2 1c4 1 8 2 12 1v1 1l-3 1c-2 0-3 0-5 1h-1c-2-1-3-1-5-2-1 1-2 0-3 0-1-1-1-1-2-1z" class="L"></path><path d="M564 239c-1 0-1 0-2-1l1-1c2-1 3-1 5 0 1 1 1 1 3 2h2 1c-2 1-3 1-5 1-1 1-2 0-3 0-1-1-1-1-2-1z" class="AJ"></path><path d="M574 218c4 0 6 0 8 4l3 3c1 1 5 2 5 4v1 1h3c-1 3-3 5-7 6h-2l4-2c1 0 2-1 3-2l-2-2h-1l1-1-1-2h-1c-2-1-2 0-4-2-2 0-7 1-9 1l-9 3-2-2 13-3 7-1c-2 0-5-1-6-2h-3c1-1 1-1 1-2-1 0-2 0-3-1l-1-1h3z" class="AB"></path><path d="M571 218l1 1c1 1 2 1 3 1 0 1 0 1-1 2h3c1 1 4 2 6 2l-7 1-13 3 2 2c-2 0-4 1-6 2-2 0-2-1-4 0l-1 1c-2 1-3 2-4 3h-1c-1 0-1 1-2 2l-1-1c1-1 2-2 2-3v-1-1-1c1-2 3-3 5-4 4-3 7-5 12-7 2-1 4-1 6-2z" class="V"></path><path d="M556 230v-1c4-3 9-4 14-5h2 4v1l-13 3c-3 1-5 1-7 2z" class="u"></path><path d="M548 232c1-1 2-1 3-2s3-2 5-3v1l-3 2 1 1h1l1-1c2-1 4-1 7-2l2 2c-2 0-4 1-6 2-2 0-2-1-4 0l-1 1c-2 1-3 2-4 3h-1c-1 0-1 1-2 2l-1-1c1-1 2-2 2-3v-1-1z" class="p"></path><path d="M555 196s1-1 3-1c-1 1-1 2-1 3h-1c1 1 1 2 2 2l1 1c1 0 3 0 4 1l-1 1c9 0 23-2 30 3l3 3h0c0 1-1 2-1 2-3-1-5-2-7-3l2 2c-1 1-2 1-4 2 1 2 2 4 3 5 0 1 0 1 1 1-2 1-3 1-4 1-1-2-3-4-5-6-6-3-11-3-17 0-8 3-14 7-19 13-3 3-6 7-8 11-4 7-6 15-7 23h-1v3 1-1l-2-2v-1c1-3 0-6 1-8 0-2 1-3 1-4l5-15v-4l1-4 2-2c1-2 2-4 2-7 1-1 3-3 4-5 0-1 1-2 2-3l11-12z" class="AA"></path><path d="M580 213l2-2c1-1 0-1 2-1v-2l-2-2h0 0c2 0 4 0 5 2l2 2c-1 1-2 1-4 2 1 2 2 4 3 5 0 1 0 1 1 1-2 1-3 1-4 1-1-2-3-4-5-6z" class="L"></path><defs><linearGradient id="Ct" x1="537.106" y1="262.801" x2="534.045" y2="220.761" xlink:href="#B"><stop offset="0" stop-color="#9e9895"></stop><stop offset="1" stop-color="#dad1cd"></stop></linearGradient></defs><path fill="url(#Ct)" d="M549 211c0 3-5 7-7 9h1c3-2 5-5 7-7 0 2-4 6-6 7h1c-1 1-1 1-1 2s0 1-1 2v1l1 1c-3 3-6 7-8 11-4 7-6 15-7 23h-1v3 1-1l-2-2v-1c1-3 0-6 1-8 0-2 1-3 1-4l5-15c4-9 9-15 16-22z"></path><path d="M555 196s1-1 3-1c-1 1-1 2-1 3h-1c1 1 1 2 2 2l1 1c1 0 3 0 4 1l-1 1c-4 2-9 5-13 8-7 7-12 13-16 22v-4l1-4 2-2c1-2 2-4 2-7 1-1 3-3 4-5 0-1 1-2 2-3l11-12z" class="X"></path><path d="M555 261l2-2 1-4h0c1-1 1-2 1-3v-1c1-1 0-4 0-6l1 1v1 1c0 1-1 2-1 2l1 1v5c-1 1-3 3-3 6l-1 1 1 1v1c-1 1-1 2-1 3-1 4-3 8-4 13-1 2-1 4-2 6-1 11-2 21 3 32 1 2 2 5 3 7 2 2 4 4 7 6 1 1 4 1 6 1h1 1c-1 2-4 3-6 4h5v1h-3c-2 0-4-1-5-2-8-4-13-9-18-16-1 2 1 7 1 9-1-1-2-3-3-4l-3-5-6-13 1-1 1 1c0 1 1 2 1 3 0 0 1 0 1 1l1-1c-2-3-2-6-3-9-1 0-1 0-1-1 0-2 0-4 1-6 0-2 0-5-1-8l1-8h0l1-1v1l1-1 1-1c0-2 3-8 4-10 1 0 1 0 1-1v-3h1c1 2 1 2 3 3 0 0 0 1 1 1s3 0 4-1v-1l3-3z" class="Q"></path><path d="M543 289l3-3c0 6-1 11 0 17v5 2c1 1 1 3 2 5v2l1 1-1 1c-1-2-1-3-1-4l-1-1v1c-2-2-1-3-3-5h-1v-2l1-1v-1h-1-1v-1l1-1v-1c-1 0-1 0-2-1 0-1-1-6-1-8 0 0 1 0 1-1 1 0 1 0 2-1s1-2 1-3z" class="M"></path><path d="M555 261l2-2 1-4h0c1-1 1-2 1-3v-1c1-1 0-4 0-6l1 1v1 1c0 1-1 2-1 2l1 1v5c-1 1-3 3-3 6l-1 1 1 1v1c-1 1-1 2-1 3-1 4-3 8-4 13-1 2-1 4-2 6h0 0v-2l1-4v-1l1-2c-1 1-1 1-2 1 1-1 2-3 1-4-1 0-1 1-1 2h0c-1-1 1-6 1-8l-1-1-1 5c-2 4-2 9-3 13l-3 3 1-4c0-3 2-7 2-10 0-1 0-1 1-2h-1-1c-3 0-5 1-7 3 0-2 3-8 4-10 1 0 1 0 1-1v-3h1c1 2 1 2 3 3 0 0 0 1 1 1s3 0 4-1v-1l3-3z" class="L"></path><path d="M538 276c0-2 3-8 4-10l2 1 1 1c-1 0-1 0-2 1v1c1 1 2 1 4 2v1h-1-1c-3 0-5 1-7 3z" class="M"></path><path d="M545 273h1 1c-1 1-1 1-1 2 0 3-2 7-2 10l-1 4c0 1 0 2-1 3s-1 1-2 1c0 1-1 1-1 1 0 2 1 7 1 8 1 1 1 1 2 1v1l-1 1v1h1 1v1l-1 1v2h1c2 2 1 3 3 5 0 3 3 6 2 9 3 3 6 6 9 8 2 2 6 4 8 5h5v1h-3c-2 0-4-1-5-2-8-4-13-9-18-16-1 2 1 7 1 9-1-1-2-3-3-4l-3-5-6-13 1-1 1 1c0 1 1 2 1 3 0 0 1 0 1 1l1-1c-2-3-2-6-3-9-1 0-1 0-1-1 0-2 0-4 1-6 0-2 0-5-1-8l1-8h0l1-1v1l1-1 1-1c2-2 4-3 7-3z" class="B"></path><path d="M539 292h3c-1 1-1 1-2 1 0 1-1 1-1 1 0 2 1 7 1 8 1 1 1 1 2 1v1l-1 1v1h1 1v1l-1 1v2h1c2 2 1 3 3 5 0 3 3 6 2 9-8-8-10-20-11-32h2z" class="D"></path><path d="M535 294c1 4 1 7 2 10 2 5 4 10 6 14l1 2c-1 2 1 7 1 9-1-1-2-3-3-4l-3-5-6-13 1-1 1 1c0 1 1 2 1 3 0 0 1 0 1 1l1-1c-2-3-2-6-3-9-1 0-1 0-1-1 0-2 0-4 1-6z" class="M"></path><path d="M545 273h1 1c-1 1-1 1-1 2 0 3-2 7-2 10l-1 4c0 1 0 2-1 3h-3-2c-1-3-1-7-1-10 0-1 0-2 1-2 1-3 3-5 6-6l2-1h0z" class="I"></path><path d="M536 282c0-1 0-2 1-2 1-3 3-5 6-6l1 1c-1 3-4 4-4 7h-4zm3 10h-1v-1-6h6l-1 4c0 1 0 2-1 3h-3z" class="AD"></path><defs><linearGradient id="Cu" x1="508.988" y1="271.151" x2="550.512" y2="304.349" xlink:href="#B"><stop offset="0" stop-color="#918c8b"></stop><stop offset="1" stop-color="#cac0b8"></stop></linearGradient></defs><path fill="url(#Cu)" d="M525 245h1c0 2 0 5 1 7-1 2 0 5-1 8v1l2 2v1-1-3h1c-1 4-1 9 0 13v7l3 15c0 2 1 5 1 7h1v1c0 1 0 2 1 4l-1-1-1 1 6 13 3 5c1 1 2 3 3 4 0-2-2-7-1-9 5 7 10 12 18 16 1 1 3 2 5 2h3c3 0 5-2 7-4v1c1-1 2-2 2-4v4c-1 3-3 5-5 7l-6 3h0-2c-7 1-15-3-20-7l-5-1c-2-2-2-2-4-3h-3-1-3l-5 1c-1 2-3 3-4 5s-2 4-3 7v3h-1l-1-2c-1-5-3-8-8-11-2-2-3-3-6-4 2-1 3-3 4-5l2-1c0-1 0-1 1 0l1-1c0-3 2-4 3-7v-1c0-1 1-2 1-3v-1h1l1 2v-2l-1-3-2-1v-1c1-2 1-6 2-7 2-7 2-15 2-21v-10h1l2 20 1-2v-8l1-27c2-3 3-6 3-9z"></path><path d="M525 269c1 2 1 4 1 6 1 2 2 5 2 7l1 4-2 1v-2c-2-5-2-11-2-16z" class="L"></path><path d="M529 260c-1 4-1 9 0 13v7l-1 2c0-2-1-5-2-7 0-2 0-4-1-6 0-2 1-6 1-9v1l2 2v1-1-3h1z" class="M"></path><path d="M521 298l1-3h0c1-4-1-8 0-12 1 0 1 0 2 1h0v2c1 4 1 8 2 12v2l4 13 1 3c1 3 3 7 5 10 3 4 6 8 10 12l-5-1c-2-2-2-2-4-3-9-10-14-23-16-36z" class="X"></path><defs><linearGradient id="Cv" x1="557.476" y1="317.874" x2="555.192" y2="343.436" xlink:href="#B"><stop offset="0" stop-color="#bab1a9"></stop><stop offset="1" stop-color="#dad5d2"></stop></linearGradient></defs><path fill="url(#Cv)" d="M530 313h1c1 2 1 3 2 5l3 6c0-2-1-5 0-8h1c1 1 1 4 2 4h0l3 5c1 1 2 3 3 4 0-2-2-7-1-9 5 7 10 12 18 16 1 1 3 2 5 2h3c3 0 5-2 7-4v1c1-1 2-2 2-4v4c-1 3-3 5-5 7l-6 3h0-2c-7 1-15-3-20-7-4-4-7-8-10-12-2-3-4-7-5-10l-1-3z"></path><defs><linearGradient id="Cw" x1="512.326" y1="300.958" x2="535.991" y2="307.686" xlink:href="#B"><stop offset="0" stop-color="#101011"></stop><stop offset="1" stop-color="#312c29"></stop></linearGradient></defs><path fill="url(#Cw)" d="M518 271l2 20c0 2 1 4 1 7 2 13 7 26 16 36h-3-1-3l-5 1c-1 2-3 3-4 5s-2 4-3 7v3h-1l-1-2c-1-5-3-8-8-11-2-2-3-3-6-4 2-1 3-3 4-5l2-1c0-1 0-1 1 0l1-1c0-3 2-4 3-7v-1c0-1 1-2 1-3v-1h1l1 2v-2l-1-3-2-1v-1c1-2 1-6 2-7 2-7 2-15 2-21v-10h1z"></path><path d="M515 302c2 3 1 8 1 12l-1-3-2-1v-1c1-2 1-6 2-7z" class="E"></path><path d="M527 332l5-2 2 3v1h-1-3l-5 1c1-2 1-2 2-3z" class="p"></path><path d="M519 318v-5l1-1c3 2 4 8 5 10l3 6-2-1c-1-1-1-2-2-3 0-2-1-4-2-5h-1l-2-1z" class="f"></path><defs><linearGradient id="Cx" x1="515.83" y1="321.459" x2="523.452" y2="330.911" xlink:href="#B"><stop offset="0" stop-color="#575553"></stop><stop offset="1" stop-color="#757274"></stop></linearGradient></defs><path fill="url(#Cx)" d="M519 318l2 1h1c1 1 2 3 2 5 1 1 1 2 2 3l2 1h1l1 1h-1l-2 2c-2 1-4 2-5 4h0c-1 1-2 2-4 3 1-2 1-3 0-5 0-5 0-10 1-15z"></path><path d="M524 324c1 1 1 2 2 3l2 1h1l1 1h-1l-2 2c-2 1-4 2-5 4v-1h-2v-1-1l1-6 1-1v1h1l1-2z" class="Y"></path><path d="M524 324c1 1 1 2 2 3l2 1h1l1 1h-1-1c-2 1-3 2-5 3h0 0l1-2c0-2 0-2-1-4l1-2z" class="G"></path><path d="M508 327c0-1 0-1 1 0l1-1c0-3 2-4 3-7v-1c0-1 1-2 1-3v-1h1l1 2v2c1 3 1 5 1 9 0 2 0 5 1 6 1 2 1 3 0 5 2-1 3-2 4-3h0c1-2 3-3 5-4v1c-1 1-1 1-2 3s-3 3-4 5-2 4-3 7v3h-1l-1-2c-1-5-3-8-8-11-2-2-3-3-6-4 2-1 3-3 4-5l2-1z" class="D"></path><path d="M508 327l1 2c2 2 6 4 8 7v-8-1c0 2 0 5 1 6 1 2 1 3 0 5 2-1 3-2 4-3h0c1-2 3-3 5-4v1c-1 1-1 1-2 3s-3 3-4 5-2 4-3 7c-1 0-1-1-2-2 0-1 0-1 1-3-1-1 0-3 0-4-1-4-8-8-11-10l2-1z" class="AB"></path><path d="M522 335c-1 2-1 4-3 5v1c-1-2-1-2-1-3h0c2-1 3-2 4-3z" class="V"></path><defs><linearGradient id="Cy" x1="511.437" y1="317.406" x2="515.231" y2="330.228" xlink:href="#B"><stop offset="0" stop-color="#605b59"></stop><stop offset="1" stop-color="#7c7a7a"></stop></linearGradient></defs><path fill="url(#Cy)" d="M508 327c0-1 0-1 1 0l1-1c0-3 2-4 3-7v-1c0-1 1-2 1-3v-1h1l1 2v2c1 3 1 5 1 9v1 8c-2-3-6-5-8-7l-1-2z"></path><path d="M516 318c1 3 1 5 1 9v1c-1 0-2-1-2-1-1-3 0-7 1-9z" class="G"></path><defs><linearGradient id="Cz" x1="643.734" y1="165.151" x2="627.303" y2="192.817" xlink:href="#B"><stop offset="0" stop-color="#0f0f10"></stop><stop offset="1" stop-color="#434243"></stop></linearGradient></defs><path fill="url(#Cz)" d="M601 154h6v1c1 2 3 2 6 3 1 0 2-1 3-1 2 2 4 1 7 1 0 1 0 2 1 2h3c1 1 2 1 2 2h0l1 1-1 1c12 2 22 5 32 11 1 0 3 2 4 1h1 0l-1 2c-3 5-7 11-8 16 1 1 1 2 0 4h0v-1l-1 1c0 3 0 6-1 9h-1c-2 1-3 3-4 5l-2 3s-1 1 0 2v2l-2 5-2 8v1l-1 7v3h-2-4l-1-1h-1v-1l1-1-1-1v-2c-2 0-3 0-5-1h0-2c-1 1-3 1-4 1s-2 0-4 1c-1-1-2-2-2-3l-1-1h-1-1l-3-1v-4h-1v-5 1c-1 3-1 7 0 11-2 1-3 2-5 3l-1 1h-1v-1c-1 1-1 1-2 1 1-1 1-1 1-2-1 0-2 0-3-1l-4 4c-1 0-14 3-14 3h-4-2-1c-1 0-2-1-3-1l2-1h1c2-1 3-1 5-1l3-1v-1-1s3 0 3-1c4-1 6-3 7-6 0-1 1-3 1-4 1-5-2-8-4-12l1 5 1 1-2 1c0-2-1-3-1-4-1 0-1 0-1-1-1-1-2-3-3-5 2-1 3-1 4-2l-2-2c2 1 4 2 7 3 0 0 1-1 1-2h0l-3-3c-7-5-21-3-30-3l1-1c-1-1-3-1-4-1l-1-1c-1 0-1-1-2-2h1c0-1 0-2 1-3-2 0-3 1-3 1l-11 12c-1 1-2 2-2 3-1 2-3 4-4 5 0 3-1 5-2 7l-2 2 1-3h-1c-1-1 0-3 1-4l-1-2c1-5 3-11 6-16 5-8 10-16 17-23 9-9 20-16 32-20l1 1 11-4z"></path><path d="M642 198c3-1 6-2 9-2l-2 2h-1c0 1-1 1-1 2-1 0-1 1-2 1l-3-3z" class="P"></path><path d="M645 171v1c1 0 2 0 3 1h1c1 0 1 0 2 1h-1l-2 1v-1l-8 1v-1c1-1 2-1 3-2l2-1z" class="e"></path><path d="M628 168c1 1 3 1 4 1l1-1c4 1 8 1 12 3l-2 1c-1-1-2-1-4-1h-1v-1-1l-3 1c-3 0-6 1-10-1 1 0 2 0 3-1z" class="y"></path><path d="M642 183c2 0 2 0 3-1l1 1v-1h9c-2 2-4 4-7 4l-2-1c-1 0-1 0-2-1-1 0-3 0-4 1h-2c1-1 1-1 2-1l2-1z" class="B"></path><defs><linearGradient id="DA" x1="656.69" y1="195.562" x2="653.183" y2="206.225" xlink:href="#B"><stop offset="0" stop-color="#979393"></stop><stop offset="1" stop-color="#b3afae"></stop></linearGradient></defs><path fill="url(#DA)" d="M657 194c1 1 1 2 0 4h0v-1l-1 1c0 3 0 6-1 9h-1c-2 1-3 3-4 5l1-4 6-14z"></path><defs><linearGradient id="DB" x1="633.344" y1="197.295" x2="620.45" y2="207.573" xlink:href="#B"><stop offset="0" stop-color="#85807e"></stop><stop offset="1" stop-color="#a7a3a3"></stop></linearGradient></defs><path fill="url(#DB)" d="M618 206s-1-1-1-2c1-1 3-2 4-2l9-2c3-1 5-1 8-1-1 1-1 1-1 2v2h-1c-6 1-12 3-18 3z"></path><path d="M610 194c2-2 5-2 7-3s4-2 6-2c1 0 2 0 3-1h2 1c0 2-1 3-1 4l-10 2v1c3-1 6-2 8-2s3-1 4-1v1l-4 1-10 2h-1-2c-1 0-2-1-3-2z" class="u"></path><defs><linearGradient id="DC" x1="607.759" y1="229.034" x2="620.492" y2="210.603" xlink:href="#B"><stop offset="0" stop-color="#908784"></stop><stop offset="1" stop-color="#a8a6a6"></stop></linearGradient></defs><path fill="url(#DC)" d="M617 207l1 4c0 2 0 5-1 8v4c-1 1-1 2-1 3v5 1h0l1 2h-1-1l-3-1v-4c1-7 1-15 0-22h5z"></path><defs><linearGradient id="DD" x1="649.566" y1="192.691" x2="639.708" y2="188.491" xlink:href="#B"><stop offset="0" stop-color="#3b3a3b"></stop><stop offset="1" stop-color="#626162"></stop></linearGradient></defs><path fill="url(#DD)" d="M638 190l20-6-7 4c2 1 3 0 4 0 0 1 1 0 0 2h0v2c-1 0-2 1-2 2-1 0-3 0-4 1-3 1-6 2-9 2l-1-1 1-1v-1c-2-1-4 0-5 0h-1v-1h2c1 0 2-1 3-2l-1-1z"></path><defs><linearGradient id="DE" x1="651.937" y1="189.766" x2="641.067" y2="196.425" xlink:href="#B"><stop offset="0" stop-color="#414042"></stop><stop offset="1" stop-color="#676667"></stop></linearGradient></defs><path fill="url(#DE)" d="M640 194c1-1 0-1 1-1 3 0 4 0 6-1 1-1 1 0 2 0s4-1 5-2l1 2c-1 0-2 1-2 2-1 0-3 0-4 1-3 1-6 2-9 2l-1-1 1-1v-1z"></path><path d="M620 168h0l4-1h7l-3 1c-1 1-2 1-3 1 4 2 7 1 10 1l3-1v1 1h1c2 0 3 0 4 1-1 1-2 1-3 2v1c-1 1-3 1-4 1h-4v-1l-1-1h-1l-5 1c0-1 1-1 2-2h-5l-1-1c-2 1-4 1-6 0-2 1-2 1-3 1v-1l-4-1s4-1 5-1l7-2z" class="v"></path><path d="M620 168l1 1c-1 1 0 1-2 1-1 1-3 2-4 2-2 1-2 1-3 1v-1l-4-1 5-1 7-2z" class="Y"></path><path d="M635 170l3-1v1 1h1c2 0 3 0 4 1-1 1-2 1-3 2v1c-1 1-3 1-4 1h-4v-1c2 0 4-1 7-2l-1-1c-1 1-4 1-5 1 1-1 3-2 3-3h-1z" class="O"></path><defs><linearGradient id="DF" x1="632.438" y1="201.796" x2="620.591" y2="191.59" xlink:href="#B"><stop offset="0" stop-color="#7e7b7b"></stop><stop offset="1" stop-color="#a49f9e"></stop></linearGradient></defs><path fill="url(#DF)" d="M630 192c2-1 5-1 8-2l1 1c-1 1-2 2-3 2h-2v1h1c1 0 3-1 5 0v1l-1 1 1 1-2 1-12 2c-3 1-6 2-9 2-1-1-3-3-3-4l1-1 1-1 10-2 4-1v-1z"></path><path d="M635 194c1 0 3-1 5 0v1l-1 1 1 1-2 1c0-1 0-2-1-3v-1h-2z" class="Y"></path><path d="M630 192c2-1 5-1 8-2l1 1c-1 1-2 2-3 2h-2v1l-19 3 1-1 10-2 4-1v-1z" class="V"></path><path d="M625 184c1-1 3-1 4-1l-1 1h3l2 1c-1 0-2 1-3 1l-2 1h1c2 0 3-1 5-1h2l3 3-11 3c0-1 1-2 1-4h-1-2c-1 1-2 1-3 1-2 0-4 1-6 2s-5 1-7 3l-4-3-2-1v-1l3-1c4-1 7-2 10-3 2 0 4 0 5-1h3z" class="H"></path><path d="M622 184h3v3c-2 1-6 1-9 2-1 1-3 2-4 3h-1c-1 0-2 0-3-1h-2l-2-1v-1l3-1c4-1 7-2 10-3 2 0 4 0 5-1z" class="X"></path><path d="M607 188h1 6 0c-1 1-2 2-4 2l-2 1h-2l-2-1v-1l3-1z" class="L"></path><path d="M645 201c1 0 1-1 2-1 0-1 1-1 1-2h1 0c1 0 1 0 2 1 0 1 0 1-1 2h0c0 2-1 3-2 5h0c-1 0-3 1-4 1l-4 1v1h1c1-1 3-1 5-1-2 2-6 2-9 3h-1c-2 0-5 1-6 2v1h-4c-1 0-2 1-4 1h-2-1v-3-1-2-2l-1-1c6 0 12-2 18-3h1v-2c0-1 0-1 1-2l4-1 3 3z" class="Y"></path><path d="M645 201c1 0 1-1 2-1 0-1 1-1 1-2h1 0c1 0 1 0 2 1 0 1 0 1-1 2h0c-2 1-4 1-6 1l-6 1h0l7-2z" class="B"></path><path d="M632 205h1v1c-3 2-8 3-12 3h-2v-1c1 0 1-1 2-1h2c3-1 6-1 9-2z" class="I"></path><path d="M644 202c2 0 4 0 6-1 0 2-1 3-2 5h0c-1 0-3 1-4 1l-1-1c-1 0-3 1-4 1h0-1c2-1 5-2 7-4l-1-1z" class="P"></path><path d="M631 209l2-1c1 1 1 0 3 0v1h-1l1 2c-2 0-5 1-6 2v1h-4c-1 0-2 1-4 1h-2-1v-3-1c3-2 9-2 12-2z" class="D"></path><path d="M631 209h1c-1 2-3 3-5 3s-5 1-8 0v-1c3-2 9-2 12-2z" class="I"></path><defs><linearGradient id="DG" x1="646.08" y1="175.475" x2="632.055" y2="177.828" xlink:href="#B"><stop offset="0" stop-color="#333335"></stop><stop offset="1" stop-color="#565556"></stop></linearGradient></defs><path fill="url(#DG)" d="M622 173h5c-1 1-2 1-2 2l5-1h1l1 1v1h4c1 0 3 0 4-1l8-1v1 1h2v1l-1 1h3v1h-1-1v1h-1v-1h-1v1c0 1-1 2-2 2v1l-1-1c-1 1-1 1-3 1l-2 1c-1 0-1 0-2 1h2c1-1 3-1 4-1 1 1 1 1 2 1l2 1c-3 1-6 3-9 3l-3-3h-2c-2 0-3 1-5 1h-1l2-1c1 0 2-1 3-1l-2-1h-3l1-1c-1 0-3 0-4 1h-3 0c1-1 1-1 2-1l1-1h-5-1-2v-2h-3c-3 1-7 2-11 2l8-3c2 0 5-1 7-2 1 0 2 0 2-1 1 0 1 0 1-1l1-1v-1z"></path><path d="M642 183l-2-2-2 1-1-2h2l1 1h0c2-1 4-1 6 0l-1 1c-1 1-1 1-3 1z" class="v"></path><path d="M622 173h5c-1 1-2 1-2 2l5-1c-1 2-1 1-3 2-1 0-2 0-3 1h-2l1 1c1 0 4 0 5 1-1 0-3 1-4 0l-2 2-3 1h-2v-2h-3c-3 1-7 2-11 2l8-3c2 0 5-1 7-2 1 0 2 0 2-1 1 0 1 0 1-1l1-1v-1z" class="V"></path><path d="M614 180c4-1 7-1 10-1l-2 2-3 1h-2v-2h-3z" class="H"></path><path d="M635 179c2-1 5 0 7-1l1 1-4 1h-2l1 2 2-1 2 2-2 1c-1 0-1 0-2 1h2c1-1 3-1 4-1 1 1 1 1 2 1l2 1c-3 1-6 3-9 3l-3-3h-2c-2 0-3 1-5 1h-1l2-1c1 0 2-1 3-1l-2-1h-3l1-1c-1 0-3 0-4 1h-3 0c1-1 1-1 2-1l1-1h-5-1l3-1h1v1c1 0 2 0 3-1 3-1 6-1 9-2z" class="P"></path><path d="M635 179v1c-1 1-2 1-3 1l-1 1c2 0 2 1 3 1h3c-1 1-2 1-4 2h0l-2-1h-3l1-1c-1 0-3 0-4 1h-3 0c1-1 1-1 2-1l1-1h-5-1l3-1h1v1c1 0 2 0 3-1 3-1 6-1 9-2z" class="x"></path><path d="M612 173c1 0 1 0 3-1 2 1 4 1 6 0l1 1v1l-1 1c0 1 0 1-1 1 0 1-1 1-2 1-2 1-5 2-7 2l-8 3c4 0 8-1 11-2h3v2h2 1 5l-1 1c-1 0-1 0-2 1h0c-1 1-3 1-5 1-3 1-6 2-10 3l-3 1v1c-2-1-2-1-4-1h-1c-1-1-2-1-3-1-4-1-6-1-10-1l-5 1v1h7 0c-2 0-4 1-6 0h-2l-5 1c-1 0-2 0-3 1-2 0-3 0-4 1h-1l-1-1c1-1 1-2 2-2l2-2c-1 0-2 1-3 0l1-1c3-1 7-3 10-4l14-6h1c2-1 4-2 7-2 1 1 1 1 3 0l1 1h2l6-2z" class="M"></path><path d="M584 186s1-1 2-1c4-2 9-4 13-3-5 2-10 3-15 4z" class="X"></path><path d="M612 173c1 0 1 0 3-1 2 1 4 1 6 0l1 1v1l-1 1c0 1 0 1-1 1 0 1-1 1-2 1-2 1-5 2-7 2 0-1 0 0-1-1h-5c-2 0-4 1-6 1-3 1-5 2-8 2h0 0l1-1 12-5h2l6-2z" class="D"></path><path d="M605 178c2 0 4-1 5-2h1 7v1c-2 1-5 2-7 2 0-1 0 0-1-1h-5z" class="u"></path><path d="M612 173c1 0 1 0 3-1 2 1 4 1 6 0l1 1v1c-2 0-3 1-5 1s-5 0-7 1c-2 0-3 0-4-1l6-2z" class="G"></path><path d="M600 174c1 1 1 1 3 0l1 1-12 5-1 1h0 0c-3 1-6 1-9 2-3 0-9 2-12 4-1 0-2 1-3 0l1-1c3-1 7-3 10-4l14-6h1c2-1 4-2 7-2zm3 8c4 0 8-1 11-2h3v2h2 1 5l-1 1c-1 0-1 0-2 1h0c-1 1-3 1-5 1-3 1-6 2-10 3l-3 1v1c-2-1-2-1-4-1h-1c-1-1-2-1-3-1-4-1-6-1-10-1l-5 1h-3c-2 1-3 1-4 1l-1-1c4 0 7-2 11-2 5-1 10-2 15-4h4z" class="I"></path><path d="M617 182h2 1 5l-1 1c-1 0-1 0-2 1h0c-1 1-3 1-5 1-3 1-6 2-10 3l-3 1c-1 0-1-1-2-1l9-3h1v-1c-4 0-8 3-12 3v-1c3 0 7-2 11-3 2 0 4-1 6-1z" class="Y"></path><path d="M644 207c1 0 3-1 4-1 1 1 2 1 3 2l-1 4-2 3s-1 1 0 2v2l-2 5-2 8v1l-1 7v3h-2-4l-1-1h-1v-1l1-1-1-1v-2c-2 0-3 0-5-1h0-2c-1 1-3 1-4 1s-2 0-4 1c-1-1-2-2-2-3h1l-1-2c-1-2 0-5 0-7v-3l1-1v-2-5h1 2c2 0 3-1 4-1h4v-1c1-1 4-2 6-2h1c3-1 7-1 9-3-2 0-4 0-5 1h-1v-1l4-1z" class="I"></path><path d="M630 213h2v1 1h2c1 0 2 0 3 1l-1 3-6 1c1-2 3-2 3-4l-1-1c-1 1-1 1-1 2-2 0-2 0-3-1h1l1-1h1l-1-1v-1z" class="H"></path><path d="M630 230h1c2 0 3 0 5-1s4-1 6-1c1 1 1 2 2 2v1 1 1c-2 1-4 1-6 1h-1c-2 1-3 1-4 1s-2 1-3 1h-2c-1 1-3 1-4 1s-2 0-4 1c-1-1-2-2-2-3h1l1 1c3 0 8-1 11-2h2 1 1c1-1 1-1 2-1l-1-3h-2c-2 1-3 1-5 1l1-1z" class="V"></path><path d="M644 233l-1 7v3h-2-4l-1-1h-1v-1l1-1-1-1v-2c-2 0-3 0-5-1h0c1 0 2-1 3-1s2 0 4-1h1c2 0 4 0 6-1z" class="D"></path><path d="M637 234c1 2 0 4 1 6h-1c1 1 2 1 3 1 0 1 1 2 1 2h-4l-1-1h-1v-1l1-1-1-1v-2c-2 0-3 0-5-1h0c1 0 2-1 3-1s2 0 4-1z" class="n"></path><path d="M634 220c4-1 10-2 14-1l-2 5-2 8v-1-1c-1 0-1-1-2-2-2 0-4 0-6 1s-3 1-5 1h-1c-2-1-5 0-7 0v-1h1l1-1c3-2 8-2 12-4h-2l1-1c-1-1-3-1-4 0l1-3h1z" class="P"></path><path d="M634 220c1 0 3-1 4 0s1 1 2 1v1c-1 0-1 0-1 1h1c-1 1-2 1-3 1h-2l1-1c-1-1-3-1-4 0l1-3h1z" class="Y"></path><defs><linearGradient id="DH" x1="640.452" y1="223.81" x2="628.909" y2="230.966" xlink:href="#B"><stop offset="0" stop-color="#706c6c"></stop><stop offset="1" stop-color="#9b9697"></stop></linearGradient></defs><path fill="url(#DH)" d="M646 224l-2 8v-1-1c-1 0-1-1-2-2-2 0-4 0-6 1s-3 1-5 1h-1c-2-1-5 0-7 0v-1h1l1-1 13-3h2 3 1c0-1 1-1 2-1z"></path><path d="M644 207c1 0 3-1 4-1 1 1 2 1 3 2l-1 4-2 3s-1 1 0 2v2c-4-1-10 0-14 1h-1c-4 1-7 1-11 2h-1 0l9-2 6-1 1-3c-1-1-2-1-3-1h-2v-1-1h-2c1-1 4-2 6-2h1c3-1 7-1 9-3-2 0-4 0-5 1h-1v-1l4-1z" class="r"></path><path d="M636 211h1 2 7c0 1-1 2-2 3l-1 1h1 1v1c-1 1-2 1-3 1v-1c-1 0-1-1-2-1l1-1-2-2c-2 0-4 0-7 1h-2c1-1 4-2 6-2z" class="G"></path><path d="M632 213c3-1 5-1 7-1l2 2-1 1c1 0 1 1 2 1v1l-6 2 1-3c-1-1-2-1-3-1h-2v-1-1z" class="Y"></path><path d="M581 188l5-1c4 0 6 0 10 1 1 0 2 0 3 1h1c8 5 13 9 17 18h-5c1 7 1 15 0 22h-1v-5 1c-1 3-1 7 0 11-2 1-3 2-5 3l-1 1h-1v-1c-1 1-1 1-2 1 1-1 1-1 1-2-1 0-2 0-3-1 2-4 4-8 5-12 1-5 1-11 0-17h0c-2-4-8-8-12-9l-1-1c-6-2-13-1-19-1-3 1-7 1-9 1h-3s-1 0-1-1l-1-1 2-3 6-6c1 1 2 0 3 0l-2 2c-1 0-1 1-2 2l1 1h1c1-1 2-1 4-1 1-1 2-1 3-1l5-1h2c2 1 4 0 6 0h0-7v-1z" class="d"></path><path d="M582 189c2 1 4 0 6 0 8 1 15 3 20 10 2 2 3 5 4 8 1 7 1 15 0 22h-1v-5c0-6 1-13-2-19-2-5-8-10-13-12l-1-1c-5-1-9-2-14-2l1-1z" class="B"></path><defs><linearGradient id="DI" x1="599.624" y1="204.422" x2="601.496" y2="188.562" xlink:href="#B"><stop offset="0" stop-color="#afaba9"></stop><stop offset="1" stop-color="#d3cfcd"></stop></linearGradient></defs><path fill="url(#DI)" d="M581 188l5-1c4 0 6 0 10 1 1 0 2 0 3 1h1c8 5 13 9 17 18h-5c-1-3-2-6-4-8-5-7-12-9-20-10h0-7v-1z"></path><path d="M570 187l-2 2c-1 0-1 1-2 2l1 1h1c1-1 2-1 4-1 1-1 2-1 3-1l5-1h2l-1 1c-1 1-1 1-2 1-2 0-3 0-4 1h-2c-3 0-6 1-9 3v1c3 1 8-1 11-1 4 0 9 0 13-1h3c2 0-2 0 1 0 1 0 2 0 3 1h1c1 2 2 2 3 3 2 1 3 2 4 4l1 1c1 1 2 2 2 4l-1 1h0c-2-4-8-8-12-9l-1-1c-6-2-13-1-19-1-3 1-7 1-9 1h-3s-1 0-1-1l-1-1 2-3 6-6c1 1 2 0 3 0z" class="X"></path><path d="M568 183c1-1 2-1 3-1l-3 4-1 1-6 6-2 3 1 1c0 1 1 1 1 1h3c2 0 6 0 9-1 6 0 13-1 19 1l1 1c4 1 10 5 12 9h0c1 6 1 12 0 17-1 4-3 8-5 12l-4 4c-1 0-14 3-14 3h-4-2-1c-1 0-2-1-3-1l2-1h1c2-1 3-1 5-1l3-1v-1-1s3 0 3-1c4-1 6-3 7-6 0-1 1-3 1-4 1-5-2-8-4-12l1 5 1 1-2 1c0-2-1-3-1-4-1 0-1 0-1-1-1-1-2-3-3-5 2-1 3-1 4-2l-2-2c2 1 4 2 7 3 0 0 1-1 1-2h0l-3-3c-7-5-21-3-30-3l1-1c-1-1-3-1-4-1l-1-1c-1 0-1-1-2-2h1c0-1 0-2 1-3 0 0 3-5 4-5 1-3 4-5 6-7z" class="AF"></path><path d="M563 202c-1-1-3-1-4-1l-1-1c-1 0-1-1-2-2h1c1 1 2 2 4 2s5-1 7-1c7-1 16-2 22 0-3 2-7 1-11 1-5 0-11 0-16 2z" class="AA"></path><defs><linearGradient id="DJ" x1="601.004" y1="230.988" x2="595.901" y2="229.203" xlink:href="#B"><stop offset="0" stop-color="#746d6c"></stop><stop offset="1" stop-color="#8c847f"></stop></linearGradient></defs><path fill="url(#DJ)" d="M600 218l2 3h1c1 2 1 4 0 6v3c-1 1-2 5-3 5l-1 1c-2 0-3 2-5 3-1 1-3 1-4 2 1-3 3-4 4-6 2-2 3-4 3-7 1-3 1-5 2-7l1-3z"></path><defs><linearGradient id="DK" x1="586.724" y1="213.36" x2="578.055" y2="195.266" xlink:href="#B"><stop offset="0" stop-color="#9f928e"></stop><stop offset="1" stop-color="#b9b4b1"></stop></linearGradient></defs><path fill="url(#DK)" d="M590 199c5 1 10 3 12 7 2 2 2 3 2 5v5c0 1 0 1-1 2h0c-2-1-3-2-4-2h-1-1c1-1 1-1 2-1-1-1-2-2-2-3l1-2-1-1h-2 0l-3-3c-7-5-21-3-30-3l1-1c5-2 11-2 16-2 4 0 8 1 11-1z"></path><path d="M595 209c0-2 0-3 2-3 1 1 3 1 3 3h-3-2 0z" class="I"></path><path d="M604 216c-2-2-4-2-6-5l2-1c1 1 2 1 3 1h1v5z" class="D"></path><path d="M587 208c2 1 4 2 7 3 0 0 1-1 1-2h2l1 1-1 2c0 1 1 2 2 3-1 0-1 0-2 1h1l2 2-1 3c-1 2-1 4-2 7 0 3-1 5-3 7-1 2-3 3-4 6-2 0-4 1-7 1 0 0-1 1-2 1h-3v1h-2-1c-1 0-2-1-3-1l2-1h1c2-1 3-1 5-1l3-1v-1-1s3 0 3-1c4-1 6-3 7-6 0-1 1-3 1-4 1-5-2-8-4-12l1 5 1 1-2 1c0-2-1-3-1-4-1 0-1 0-1-1-1-1-2-3-3-5 2-1 3-1 4-2l-2-2z" class="X"></path><path d="M599 221c-2-2-4-4-4-7h0l2 2h1l2 2-1 3z" class="L"></path><path d="M580 241c3 0 6-2 8-3l3-2c1-1 2-2 4-3 0-2 0-3 2-5h0c0 3-1 5-3 7-1 2-3 3-4 6-2 0-4 1-7 1 0 0-1 1-2 1h-3v1h-2-1c-1 0-2-1-3-1l2-1h1c2-1 3-1 5-1z" class="I"></path><defs><linearGradient id="DL" x1="581.761" y1="150.125" x2="567.136" y2="209.343" xlink:href="#B"><stop offset="0" stop-color="#6d6c6c"></stop><stop offset="1" stop-color="#97918e"></stop></linearGradient></defs><path fill="url(#DL)" d="M601 154h6v1c1 2 3 2 6 3 1 0 2-1 3-1 2 2 4 1 7 1 0 1 0 2 1 2h3c1 1 2 1 2 2h0l1 1-1 1-3-1h-8l-1 1-11 2c1 1 2 2 4 2 3 0 5-2 8-1h-1c-2 1-3 1-4 2v1c-1 0-5 1-5 1l4 1v1l-6 2h-2l-1-1c-2 1-2 1-3 0-3 0-5 1-7 2h-1l-14 6c-3 1-7 3-10 4l3-4c-1 0-2 0-3 1-2 2-5 4-6 7-1 0-4 5-4 5-2 0-3 1-3 1l-11 12c-1 1-2 2-2 3-1 2-3 4-4 5 0 3-1 5-2 7l-2 2 1-3h-1c-1-1 0-3 1-4l-1-2c1-5 3-11 6-16 5-8 10-16 17-23 9-9 20-16 32-20l1 1 11-4z"></path><path d="M601 154h6v1c1 2 3 2 6 3 1 0 2-1 3-1 2 2 4 1 7 1 0 1 0 2 1 2h3c1 1 2 1 2 2h0l1 1-1 1-3-1h1l-5-3-1-1c-1 0-2 1-3 0h-5c-3 0-8 0-10-2 0-1-2-2-2-3z" class="G"></path><defs><linearGradient id="DM" x1="606.963" y1="189.808" x2="572.691" y2="166.962" xlink:href="#B"><stop offset="0" stop-color="#6d6869"></stop><stop offset="1" stop-color="#999695"></stop></linearGradient></defs><path fill="url(#DM)" d="M555 196c0-3 1-4 3-6 3-5 8-12 12-14 3-2 6-5 9-6 5-4 13-7 19-9 5-1 8 0 13-1h6 5l5 3h-1-8c-19 0-36 7-50 20-2 2-5 4-6 7-1 0-4 5-4 5-2 0-3 1-3 1z"></path><defs><linearGradient id="DN" x1="546.099" y1="181.307" x2="560.401" y2="205.193" xlink:href="#B"><stop offset="0" stop-color="#4e4a49"></stop><stop offset="1" stop-color="#918d8c"></stop></linearGradient></defs><path fill="url(#DN)" d="M557 177c9-9 20-16 32-20l1 1h-1c-1 0-2 1-2 1-7 3-11 6-16 10-2 2-4 3-6 5-6 5-13 12-17 19h0l-1 5v-1c1 2-1 5-2 7-1 1-1 2-1 4-1 1-2 2-2 3-1 2-3 4-4 5 0 3-1 5-2 7l-2 2 1-3h-1c-1-1 0-3 1-4l-1-2c1-5 3-11 6-16 5-8 10-16 17-23z"></path><path d="M548 193l-1 5v-1c1 2-1 5-2 7-1 1-1 2-1 4-1 1-2 2-2 3-1 2-3 4-4 5v-2c2-3 4-7 5-10l2-6c1-1 2-3 3-5z" class="n"></path><path d="M568 183c14-13 31-20 50-20l-1 1-11 2c1 1 2 2 4 2 3 0 5-2 8-1h-1c-2 1-3 1-4 2v1c-1 0-5 1-5 1l4 1v1l-6 2h-2l-1-1c-2 1-2 1-3 0-3 0-5 1-7 2h-1l-14 6c-3 1-7 3-10 4l3-4c-1 0-2 0-3 1z" class="y"></path><path d="M595 170c1-1 2-1 4-1-1 2-4 4-6 5h-1l-3 2h3l-14 6c-3 1-7 3-10 4l3-4c1-1 3-2 4-2 6-5 13-8 20-10z" class="p"></path><path d="M575 180c6-5 13-8 20-10v1h0l-2 2c-2 0-4 2-6 3-1 1-3 1-5 1-2 1-4 2-5 3h-2z" class="V"></path><path d="M606 166c1 1 2 2 4 2 3 0 5-2 8-1h-1c-2 1-3 1-4 2v1c-1 0-5 1-5 1l4 1v1l-6 2h-2l-1-1c-2 1-2 1-3 0-3 0-5 1-7 2h-1-3l3-2h1c2-1 5-3 6-5 2-1 5-2 7-3z" class="P"></path><path d="M608 171l4 1v1l-6 2h-2l-1-1c-2 1-2 1-3 0l1-1c1 0 2-1 3-1s3 0 4-1z" class="n"></path><path d="M461 420l2 1c2 1 3 1 5 1l3 4h-1c2 2 4 4 5 7 3 5 4 11 6 17v4 1c2 1 3 4 3 6l4 10c1 1 2 2 2 3 1 3 2 6 2 9 1 0 3 0 4 1 0 1 0 1-1 2l-2 4h0v2l2 5c3 3 4 5 4 10l-3 12h0c0 1 1 2 1 3v1l1-1 1 2v-2h1v4c1 2 2 5 2 7l3 5-1 1-1-1 1 3-2-1-2 1 3 3v2l-5-3c-1-2-6-5-8-6l-2 2 2 1v1c-1 0-3 1-5 2s-3 4-5 6l-5 3c-1 2-2 4-3 7v5c0 1-1 2-1 2-1-1-1-2-1-4h-3l2 6-1 1 1 6c-1 1-1 1-1 2v1c-3-7-5-14-8-21-2-8-6-16-9-24l1-1-1-1c0-2-1-3-1-4h0l-1-1 1-1v-1c0-1-1-3-2-4-1-3-2-6-2-9l-3-6c-1-2-1-5-2-7l-3-6-6-13c0-2-1-5-1-7-1-1-1-3-2-5s-1-5-2-7l-1-1 1-1v-1c-1-1-2-2-2-3v-2l-1-1v-3c1 1 1 3 2 4l1-1c1-1 1-2 1-4h1l1-3c0-2 1-4 1-7l3-6v5c0 1 0 2 2 3v-1l1-2c0-2 1-4 2-5 2-5 5-6 7-9l1-1h4 1 1c1 0 1 0 3-1l1 2c1 0 3-1 4-2z" class="k"></path><path d="M465 545c1 2 1 4 1 5h1l1 1c0 3 0 7 2 11h-3c-2-5-3-11-2-17z" class="AP"></path><path d="M464 541c0-1 0-2 1-3 1-2 1-2 3-2-1 1-1 2-2 4l2 2h0v2c0 1 0 1 1 2v1l-1 4-1-1h-1c0-1 0-3-1-5 1-1 1-2 1-3l-2 2h0-1c-1-1-1-1-1-2l2-1z" class="AW"></path><path d="M466 531v-3l1-4h2v2c1 2 2 4 4 6l1 2h-1l-3-3h-1c0 1 0 2 1 3l-2 2c-2 0-2 0-3 2-1 1-1 2-1 3-1-1-1-1-1-2h-1l1-2v-1c1-2 2-3 3-5z" class="AP"></path><path d="M443 505h1c1 2 2 3 4 4 1 2 1 5 3 7l1 3 3 7h-1-1c0 2 3 6 4 9l-1 1c-1-2-1-4-2-6-2-1-3-3-4-5v-1c0-1-1-3-2-4-1-3-2-6-2-9l-3-6z" class="AN"></path><path d="M443 505h1c1 2 2 3 4 4 1 2 1 5 3 7l1 3v1l-1-1-5-8-3-6z" class="AP"></path><path d="M468 536l2-2c-1-1-1-2-1-3h1l3 3h1c5 0 9 2 13 4h-1-3c-2-1-3-1-5 0-1 0-2 0-3 1-3 2-5 4-6 7-1-1-1-1-1-2v-2h0l-2-2c1-2 1-3 2-4z" class="AS"></path><path d="M475 539l2 1c-2 2-4 4-5 7h2 0c1 1 1 1 2 1 0 1-1 2-1 3v1c-1 2-2 4-3 7v5c0 1-1 2-1 2-1-1-1-2-1-4-2-4-2-8-2-11l1-4v-1c1-3 3-5 6-7z" class="R"></path><path d="M474 547c1 1 1 1 2 1 0 1-1 2-1 3v1c-1 2-2 4-3 7-1-4-1-5 0-8 1-1 1-2 2-4z" class="g"></path><path d="M475 539l2 1c-2 2-4 4-5 7h2 0c-1 2-1 3-2 4h0-1v-1l-1 2h-1c1-2 1-3 1-5h-1v-1c1-3 3-5 6-7z" class="c"></path><path d="M475 539c1-1 2-1 3-1 2-1 3-1 5 0h3 1l1 1 2 1v1c-1 0-3 1-5 2s-3 4-5 6l-5 3v-1c0-1 1-2 1-3-1 0-1 0-2-1h0-2c1-3 3-5 5-7l-2-1z" class="i"></path><path d="M475 539c1-1 2-1 3-1 2-1 3-1 5 0h3 1l1 1 2 1h-4c-5 1-8 0-11 4v2l-1 1h-2c1-3 3-5 5-7l-2-1z" class="a"></path><defs><linearGradient id="DO" x1="452.842" y1="555.189" x2="467.658" y2="555.311" xlink:href="#B"><stop offset="0" stop-color="#d15c49"></stop><stop offset="1" stop-color="#f86963"></stop></linearGradient></defs><path fill="url(#DO)" d="M452 532c1 2 3 5 4 7s2 3 2 5c1 3 4 7 5 11 2 4 3 9 5 14l1 6c-1 1-1 1-1 2v1c-3-7-5-14-8-21-2-8-6-16-9-24l1-1z"></path><path d="M449 502v-2c1 2 3 3 3 4l3-2c0 5 2 8 3 12 0 3 2 6 3 9 0-1 1-1 2-2h1l1 4 1 4v2c-1 2-2 3-3 5-2-1-2-1-3-3l-5-7-3-7-1-3c-2-2-2-5-3-7h0c1-1 1-2 1-2v-5z" class="w"></path><path d="M449 502v-2c1 2 3 3 3 4 1 3 2 7 3 10h-1c-1-1-2-3-3-5v7h0c-2-2-2-5-3-7h0c1-1 1-2 1-2v-5z" class="c"></path><path d="M449 502c1 3 2 5 2 7v7h0c-2-2-2-5-3-7h0c1-1 1-2 1-2v-5z" class="R"></path><path d="M452 504l3-2c0 5 2 8 3 12 0 3 2 6 3 9h-1l-1 1h0-1v-1l-2-6c0-2 0-2-1-3-1-3-2-7-3-10z" class="q"></path><path d="M461 523c0-1 1-1 2-2h1l1 4 1 4v2c-1 2-2 3-3 5-2-1-2-1-3-3v-4l-2-5h1 0l1-1h1z" class="C"></path><path d="M461 525h3 1l1 4-3-1c-1-1-1-2-2-3z" class="s"></path><path d="M458 524h1 0l1-1 1 2c1 1 1 2 2 3l-3 2v-1l-2-5z" class="l"></path><path d="M463 528l3 1v2c-1 2-2 3-3 5-2-1-2-1-3-3v-4 1l3-2z" class="AK"></path><path d="M463 528l3 1v2l-4 1-2-2 3-2z" class="K"></path><path d="M472 511h3 2 1c0 1 1 2 1 3l2 2 1 2 1 1 1 1 2 3-1 2c-1 0-2-1-3-2v-1l-1 1h0l-2 1v1h-1c1 2 3 4 5 5l5 5 2 2-2 2-1-1c-4-2-8-4-13-4l-1-2c-2-2-3-4-4-6v-2h-2l-1 4v3-2l-1-4-1-4v-4c1 1 1 1 1 2h1c1-1 0-1 0-2s1-2 1-3v-3c0 4 1 5 3 8 0-2 0-6 2-7v-1z" class="C"></path><path d="M479 514l2 2 1 2 1 1 1 1 2 3-1 2c-1 0-2-1-3-2v-1l-1 1h0l-2 1v1h-1c-1-1-2-3-3-5v-1h1c1-2 3-2 3-4v-1z" class="Z"></path><path d="M482 518l1 1 1 1 2 3-1 2c-1 0-2-1-3-2v-1l-1 1c-2-1-2-1-2-3h0c1 0 2 1 3 1 0-1 0-1-1-2l1-1z" class="m"></path><path d="M469 526c1 1 2 2 4 2l1 3v-2l1 1h0c2 1 3 1 5 2v-1l-1-1h1c1 1 3 3 5 4l1 1h2l2 2-2 2-1-1c-4-2-8-4-13-4l-1-2c-2-2-3-4-4-6z" class="s"></path><path d="M472 511h3 2 1c-1 0-2 0-2 1s-1 2-1 2c-1 0-1-1-1-2-1 1-1 3-2 4-1 3 0 7 1 9 0 1 1 1 1 2 0 2 1 2 1 3h0l-1-1v2l-1-3c-2 0-3-1-4-2v-2h-2l-1 4v3-2l-1-4-1-4v-4c1 1 1 1 1 2h1c1-1 0-1 0-2s1-2 1-3v-3c0 4 1 5 3 8 0-2 0-6 2-7v-1z" class="q"></path><path d="M464 517c1 1 1 1 1 2h1c1-1 0-1 0-2s1-2 1-3c0 3 0 6 1 8l1 2h-2l-1 4v3-2l-1-4-1-4v-4z" class="AN"></path><path d="M483 510l2 2c0 1 0 2 1 3s1 1 3 1l-1 1c0 1 1 1 1 2h1c2 1 2 2 5 1l1-1h0c0 1 1 2 1 3v1l1-1 1 2v-2h1v4c1 2 2 5 2 7l3 5-1 1-1-1 1 3-2-1-2 1 3 3v2l-5-3c-1-2-6-5-8-6l-2-2-5-5c-2-1-4-3-5-5h1v-1l2-1h0l1-1v1c1 1 2 2 3 2l1-2-2-3-1-1-1-4c1-2 1-3 1-5z" class="N"></path><path d="M496 519h0c0 1 1 2 1 3v1l1-1 1 2v2l-1-2c-2 1-2 1-4 1-1 0-2-1-2-2h-1-1c0-2 0-2-1-4h1c2 1 2 2 5 1l1-1z" class="z"></path><path d="M481 523l1-1v1c1 1 2 2 3 2l1-2 11 10h-1-1c-1-1-3-2-5-2l-3-2h-1c-2-1-3-4-5-6h0z" class="AN"></path><path d="M483 510l2 2c0 1 0 2 1 3s1 1 3 1l-1 1c0 1 1 1 1 2 1 2 1 2 1 4h-1c-1 0-1-1-2-1-1-1-2-2-3-2l-1-1-1-4c1-2 1-3 1-5z" class="AL"></path><path d="M486 529h1l3 2c2 0 4 1 5 2h1 1c2 1 4 4 6 5l1 3-2-1-2 1-14-12z" class="AS"></path><path d="M490 531c2 0 4 1 5 2h1 1c2 1 4 4 6 5l1 3-2-1c-3-1-4-3-6-5s-4-3-6-4z" class="t"></path><path d="M478 525h1v-1l2-1c2 2 3 5 5 6l14 12 3 3v2l-5-3c-1-2-6-5-8-6l-2-2-5-5c-2-1-4-3-5-5z" class="m"></path><path d="M478 525h1v-1l2-1-1 2c1 1 3 2 3 3v2c-2-1-4-3-5-5z" class="W"></path><path d="M493 492l2 5c3 3 4 5 4 10l-3 12-1 1c-3 1-3 0-5-1h-1c0-1-1-1-1-2l1-1c-2 0-2 0-3-1s-1-2-1-3l-2-2c0 2 0 3-1 5l1 4-1-1-1-2-2-2c0-1-1-2-1-3h-1-2-3v1c-2 1-2 5-2 7-2-3-3-4-3-8 1-4 3-7 6-9 2-2 4-3 6-3 4-1 8-1 12 1v1l1-1c0-2-1-2-2-4 1 0 2 0 3-1v-3z" class="b"></path><path d="M476 506h2s0 1 1 1l1-1c2 1 2 3 3 4 0 2 0 3-1 5l1 4-1-1-1-2-2-2c0-1-1-2-1-3h-1-2-3v-1l3-3 1-1z" class="z"></path><path d="M475 507c2 1 2 2 4 3 1 0 1 1 2 2v4l-2-2c0-1-1-2-1-3h-1-2-3v-1l3-3z" class="T"></path><path d="M473 502c2-2 4-3 6-3-1 1-2 2-3 2-2 1-3 2-4 4 1 1 2 1 4 1l-1 1-3 3v1 1c-2 1-2 5-2 7-2-3-3-4-3-8 1-4 3-7 6-9z" class="N"></path><path d="M472 505c1 1 2 1 4 1l-1 1-3 3h-1l-1-1c0-1 0-1 1-2l1-2z" class="AG"></path><path d="M493 492l2 5c3 3 4 5 4 10l-3 12-1 1c-3 1-3 0-5-1h-1c0-1-1-1-1-2l1-1c1-1 2-1 3-2 2-2 2-5 3-8-1-2-2-4-4-5l1-1c0-2-1-2-2-4 1 0 2 0 3-1v-3z" class="g"></path><path d="M493 492l2 5c1 2 1 4 1 6 0 1-1 2-1 3-1-2-2-4-4-5l1-1c0-2-1-2-2-4 1 0 2 0 3-1v-3z" class="Z"></path><path d="M476 446l1 6c2 2 2 2 4 2v1c2 1 3 4 3 6l4 10c1 1 2 2 2 3 1 3 2 6 2 9 1 0 3 0 4 1 0 1 0 1-1 2l-2 4h0v2 3c-1 1-2 1-3 1 1 2 2 2 2 4l-1 1v-1c-4-2-8-2-12-1-2 0-4 1-6 3-3 2-5 5-6 9v3c0 1-1 2-1 3s1 1 0 2h-1c0-1 0-1-1-2v4h-1c-1 1-2 1-2 2-1-3-3-6-3-9-1-4-3-7-3-12l-1-2 1-1 1-1 1-1 1-3h0c-2-2-1-2-1-4h4c1 0 2-1 3-2l5-2v-3c1-3 2-6 2-9l1-4v-5l1-7s1-1 1-2c1-1 1-1 1-2 0-3 1-5 1-8z" class="AO"></path><path d="M480 493h-2-1 0l-1-1h1c1-1 1-1 1-2 2 0 2-1 4 0 1 1 2 1 2 1 2 1 2 2 3 3-2 0-4-1-6-1h-1z" class="h"></path><path d="M482 490c3 0 4 0 6 2 0-1 0-1 1-2 0-1 1-1 2-1 1 1 1 1 2 1v2 3c-1 1-2 1-3 1s-2-1-3-2-1-2-3-3c0 0-1 0-2-1z" class="AR"></path><path d="M480 493h1c2 0 4 1 6 1 1 1 2 2 3 2 1 2 2 2 2 4l-1 1v-1c-4-2-8-2-12-1-2 0-4 1-6 3l-2-1-2 1-1-1h0c3-5 7-7 12-8z" class="AS"></path><path d="M480 493h1v2h-1c-2 0-3 0-4 1h1c1 0 2 0 2 1h-1c-1 0-2 0-3 1-1 2-2 2-4 3h0l-2 1-1-1h0c3-5 7-7 12-8z" class="AH"></path><path d="M469 486h0 4c2-1 3-1 5-2-2-1-2-1-3 0l-1-1 1-1 1-1s1 1 2 1c0 0 1-1 2-1l5 4c2 1 3 1 5 0 1 0 1-1 2-2h0c1 0 3 0 4 1 0 1 0 1-1 2l-2 4-1-1 1-3c-1 0-1 0-2 1h-6c-1-1-2-1-4-1s-3 1-5 1l-6 2c-2 1-4 2-5 4v1l-3 3-1-2v-3c-1 1-2 2-3 4v2l-1-1 1-3h0c-2-2-1-2-1-4h4c1 0 2-1 3-2l5-2z" class="Aa"></path><path d="M457 490h4l-3 4h0c-2-2-1-2-1-4z" class="g"></path><path d="M461 492c1-2 3-2 5-3h1c0 1 0 1-1 2-1 0-1 1-1 2v1l-3 3-1-2v-3z" class="k"></path><path d="M461 492v3l1 2 3-3v2-1c1 0 2-1 3-1l1-1v2c-1 2-3 3-4 6h1v2l2-2 1 1 2-1 2 1c-3 2-5 5-6 9v3c0 1-1 2-1 3s1 1 0 2h-1c0-1 0-1-1-2v4h-1c-1 1-2 1-2 2-1-3-3-6-3-9-1-4-3-7-3-12l-1-2 1-1 1-1 1-1 1 1v-2c1-2 2-3 3-4z" class="c"></path><path d="M458 507c1 2 1 2 1 3 1 3 1 5 2 7l1-1v-1h1v1l1 1v4h-1c-1 1-2 1-2 2-1-3-3-6-3-9h1c-1-2-1-4-1-7z" class="i"></path><path d="M465 496v-1c1 0 2-1 3-1l1-1v2c-1 2-3 3-4 6h1v2l-1 3h0c-1-1-1-1-2-1-1 2-1 3-2 4 0-1-1-2 0-3v-1h0c-1-2 0-3 1-4v-2c1-1 1-3 3-3z" class="h"></path><path d="M466 503l2-2 1 1 2-1 2 1c-3 2-5 5-6 9v3c0 1-1 2-1 3s1 1 0 2h-1c0-1 0-1-1-2 0-4 0-7 1-11l1-3z" class="Aa"></path><path d="M461 492v3l1 2c-1 1-1 3-2 4s-1 2-2 2v2 2c0 3 0 5 1 7h-1c-1-4-3-7-3-12l-1-2 1-1 1-1 1-1 1 1v-2c1-2 2-3 3-4z" class="W"></path><path d="M458 503v-1c0-2 2-5 3-7l1 2c-1 1-1 3-2 4s-1 2-2 2z" class="AR"></path><path d="M476 446l1 6c2 2 2 2 4 2v1c2 1 3 4 3 6l4 10c1 1 2 2 2 3 1 3 2 6 2 9h0c-1 1-1 2-2 2-2 1-3 1-5 0-1-1-5-4-5-4-1 0-2 1-2 1-1 0-2-1-2-1l-1 1-1 1 1 1c1-1 1-1 3 0-2 1-3 1-5 2h-4 0v-3c1-3 2-6 2-9l1-4v-5l1-7s1-1 1-2c1-1 1-1 1-2 0-3 1-5 1-8z" class="z"></path><path d="M490 474c1 3 2 6 2 9h0c-1 0-1 0-2 1h-2c-1-1-3-2-4-4l1-6v1c1 1 1 2 2 4 0 2 1 3 2 4l1-1c1-2 0-5 0-8z" class="N"></path><path d="M481 455c2 1 3 4 3 6h-1c-2 3 1 10 2 13l-1 6v-3h0l-2-1c0-2-1-3-2-4 0-1-1-2-2-3l3-14z" class="j"></path><path d="M478 469c1 1 2 2 2 3 1 1 2 2 2 4l2 1h0v3c1 2 3 3 4 4h2c1-1 1-1 2-1-1 1-1 2-2 2-2 1-3 1-5 0-1-1-5-4-5-4-1 0-2 1-2 1-1 0-2-1-2-1 1-4 2-8 2-12z" class="AI"></path><path d="M476 446l1 6c2 2 2 2 4 2v1l-3 14c0 4-1 8-2 12l-1 1-1 1 1 1c1-1 1-1 3 0-2 1-3 1-5 2h-4 0v-3c1-3 2-6 2-9l1-4v-5l1-7s1-1 1-2c1-1 1-1 1-2 0-3 1-5 1-8z" class="AQ"></path><path d="M475 466l-2 11c0 3 0 6-1 8l-3 1v-3c1-3 2-6 2-9l1-4c1-2 2-2 3-4z" class="W"></path><g class="R"><path d="M476 446l1 6-2 14c-1 2-2 2-3 4v-5l1-7s1-1 1-2c1-1 1-1 1-2 0-3 1-5 1-8z"></path><path d="M461 420l2 1c2 1 3 1 5 1l3 4h-1c2 2 4 4 5 7 3 5 4 11 6 17v4c-2 0-2 0-4-2l-1-6c0 3-1 5-1 8 0 1 0 1-1 2 0 1-1 2-1 2l-1 7v5l-1 4c0 3-1 6-2 9v3l-5 2c-1 1-2 2-3 2h-4c0 2-1 2 1 4h0l-1 3-1 1-1 1-1 1 1 2-3 2c0-1-2-2-3-4v2 5s0 1-1 2h0c-2-1-3-2-4-4h-1c-1-2-1-5-2-7l-3-6-6-13c0-2-1-5-1-7-1-1-1-3-2-5s-1-5-2-7l-1-1 1-1v-1c-1-1-2-2-2-3v-2l-1-1v-3c1 1 1 3 2 4l1-1c1-1 1-2 1-4h1l1-3c0-2 1-4 1-7l3-6v5c0 1 0 2 2 3v-1l1-2c0-2 1-4 2-5 2-5 5-6 7-9l1-1h4 1 1c1 0 1 0 3-1l1 2c1 0 3-1 4-2z"></path></g><path d="M450 455v1 1l1 1c0-1 1-2 1-3 2-2 2-3 5-3-1 0-1 2-1 2-1 1-1 0-2 2v3c-1 0-2 0-2-1l-1 1c1 1 1 1 1 2-1 0-1 0-1-1h-1l-1 2-1-1c-1 1-1 2-2 2 0-1 0-1 1-2l3-6z" class="C"></path><path d="M457 452l1-1 1 1c2 3 5 7 5 11 0 2 0 3 1 5-1 2-1 3-1 5l-2-2h0l1-1c0-1 0-1-1-2 0-1 0 0-1-1v-1h-1v-2c0-2-1-3-2-5 0-1 0-1 1-2h0c0-2-1-2-1-3l-1 1v1h-2-1c1-2 1-1 2-2 0 0 0-2 1-2z" class="i"></path><path d="M451 470c1-1 1-1 2-1 0-3-1-3 0-5h1c0 1 0 1 1 2l1-1c1 1 1 1 1 2 0 2 1 2 2 3 0 2 0 2 1 3s1 3 2 4l1 2v1c-1 1-1 2-1 3h0c-1 1-1 2-2 2h0l-3-3c-2-3-3-6-4-9l-2-3z" class="c"></path><path d="M446 463c1 0 1-1 2-2l1 1 1-2c0 3 0 5 1 7v3l2 3c1 3 2 6 4 9v2l2 4h-1l-2 1 1 1c0 2-1 2 1 4h0l-1 3-1 1-1 1-1 1-3-2 1-3c-3-3-4-6-6-9l-1-5-1-4h0 1v-1c1-3 1-8 1-11v-2z" class="J"></path><path d="M455 480c0 1 1 3 2 4l2 4h-1l-2-2c-1-2-1-4-1-6z" class="c"></path><path d="M447 478c0-2-1-5 0-7 1 2 2 2 3 3h0v-2h1v1h2c1 3 2 6 4 9v2c-1-1-2-3-2-4l-2-2c0-1-1-3-2-4 0 2 0 3 1 4v4 1c-1-1-1-2-2-3s-1-1-2-1v1l-1-2z" class="g"></path><path d="M450 488h1c1-2-3-4-1-6 1 1 2 3 2 4v1h1v-2c1 0 3 2 3 4h0l1 1c0 2-1 2 1 4h0l-1 3-1 1c-1-4-4-6-6-10z" class="c"></path><path d="M447 478l1 2c0 4 0 6 2 8 2 4 5 6 6 10l-1 1-1 1-3-2 1-3c-3-3-4-6-6-9l-1-5c0-1 1-2 2-3z" class="AC"></path><path d="M452 495l3 4-1 1-3-2 1-3z" class="a"></path><path d="M446 463c1 0 1-1 2-2l1 1 1-2c0 3 0 5 1 7v3l2 3h-2v-1h-1v2h0c-1-1-2-1-3-3-1 2 0 5 0 7-1 1-2 2-2 3l-1-4h0 1v-1c1-3 1-8 1-11v-2z" class="C"></path><path d="M446 463c1 0 1-1 2-2l1 1 1-2c0 3 0 5 1 7l-3 2c-1-1-1-2-1-3l-1-1v-2z" class="K"></path><path d="M453 447c2-1 4-3 6-4 2 1 5 3 6 5 3 5 5 11 4 18v1 3h0c2-1 3-3 3-5v5l-1 4c0 3-1 6-2 9v3l-5 2c-1 1-2 2-3 2h-4l-1-1 2-1h1l-2-4v-2l3 3h0c1 0 1-1 2-2h0c0-1 0-2 1-3v-1l-1-2v-1c1-2 0-4-1-6l1 1h0l2 2c0-2 0-3 1-5-1-2-1-3-1-5 0-4-3-8-5-11l-1-1-1 1c-3 0-3 1-5 3 0 1-1 2-1 3l-1-1v-1-1c0-1 1-2 1-2l1-1v-2c1-1 1-1 1-3z" class="j"></path><path d="M469 467c-1 1-1 2-1 4l-1-1c0-3-1-6-1-9h0c1-2 0-1 1-1 0 1 0 1 1 2v1l1 3v1zm2 7c0 3-1 6-2 9l-2 1c0-2 1-3 1-5 1-3 1-3 3-5z" class="AI"></path><path d="M465 468c0 2 1 6 0 9 0 1-1 2-1 3l-1-1-1-2v-1c1-2 0-4-1-6l1 1h0l2 2c0-2 0-3 1-5z" class="s"></path><path d="M464 480c0-1 1-2 1-3v1c0 1 0 6 1 7 0 0 1 0 1-1l2-1v3l-5 2-2-1h-1c1-1 1-2 2-2l1-1v-4z" class="q"></path><path d="M463 479l1 1v4l-1 1c-1 0-1 1-2 2h1l2 1c-1 1-2 2-3 2h-4l-1-1 2-1h1l-2-4v-2l3 3h0c1 0 1-1 2-2h0c0-1 0-2 1-3v-1z" class="C"></path><path d="M453 447c2-1 4-3 6-4 2 1 5 3 6 5-1 0-1 0-2 1l1 1c-1 0-1 1-1 1 0 2 1 3 1 5l1 2c-2-2-3-5-6-6l-1-1-1 1c-3 0-3 1-5 3 0 1-1 2-1 3l-1-1v-1-1c0-1 1-2 1-2l1-1v-2c1-1 1-1 1-3z" class="T"></path><defs><linearGradient id="DP" x1="457.333" y1="437.545" x2="459.413" y2="468.674" xlink:href="#B"><stop offset="0" stop-color="#010101"></stop><stop offset="1" stop-color="#2b0705"></stop></linearGradient></defs><path fill="url(#DP)" d="M461 432c5 1 8 3 11 7 2 2 3 5 4 7 0 3-1 5-1 8 0 1 0 1-1 2 0 1-1 2-1 2l-1 7c0 2-1 4-3 5h0v-3-1c1-7-1-13-4-18-1-2-4-4-6-5-2 1-4 3-6 4 0 2 0 2-1 3v2l-1 1s-1 1-1 2l-3 6c-1 1-1 1-1 2v2c0 3 0 8-1 11v1h-1 0l1 4 1 5v1c-2-3-3-7-4-11 0-1 0-3-1-4v-14c0-2 1-3 0-4 0-3 1-5 1-7 1 1 1 2 2 3l2-4s2-2 2-3l1-2c2-1 5-3 7-4 1 0 1-1 1-2l1-2h3v-1z"></path><path d="M461 432c5 1 8 3 11 7 2 2 3 5 4 7 0 3-1 5-1 8 0 1 0 1-1 2 0 1-1 2-1 2 0-5-1-9-3-14l-3-3c-2-3-6-4-10-4h-1c1 0 1-1 1-2l1-2h3v-1z" class="s"></path><path d="M457 437h3c0-2-1-2 0-3 2-1 3-1 5 0v1h1l4 4-1 1h0c1 2 1 3 1 4l-3-3c-2-3-6-4-10-4z" class="l"></path><path d="M442 447c1 1 1 2 2 3h0c1 1 1 1 1 3v6h0c0-2 0-4 1-6l1 1c1-1 0-1 1-2 1-2 2-3 5-5 0 2 0 2-1 3v2l-1 1s-1 1-1 2l-3 6c-1 1-1 1-1 2v2c0 3 0 8-1 11v1h-1 0l1 4 1 5v1c-2-3-3-7-4-11 0-1 0-3-1-4v-14c0-2 1-3 0-4 0-3 1-5 1-7z" class="N"></path><path d="M447 454c1-1 0-1 1-2 1-2 2-3 5-5 0 2 0 2-1 3v2l-1 1s-1 1-1 2l-3 6c-1 1-1 1-1 2l-1-2h1v-3c0-2 1-3 1-4z" class="U"></path><path d="M442 447c1 1 1 2 2 3h0c-2 6-1 14-1 20 0 2 0 5 1 7l1 4 1 5v1c-2-3-3-7-4-11 0-1 0-3-1-4v-14c0-2 1-3 0-4 0-3 1-5 1-7z" class="t"></path><path d="M461 420l2 1c2 1 3 1 5 1l3 4h-1c2 2 4 4 5 7 3 5 4 11 6 17v4c-2 0-2 0-4-2l-1-6c-1-2-2-5-4-7-3-4-6-6-11-7v1h-3l-1 2c0 1 0 2-1 2-2 1-5 3-7 4l-1 2c0 1-2 3-2 3l-2 4c-1-1-1-2-2-3 0 2-1 4-1 7-2-2-1-4-1-6s0-3 1-5c-1 0-1 0-1-1l-1 2-1-1c-1 2-1 4-1 6-1-2-1-3-2-5 1-2 1-4 1-6l1-2c0-2 1-4 2-5 2-5 5-6 7-9l1-1h4 1 1c1 0 1 0 3-1l1 2c1 0 3-1 4-2z" class="AP"></path><path d="M461 432h0v1h-3l-1 2c0 1 0 2-1 2-2 1-5 3-7 4l-1 2c0 1-2 3-2 3 0-3 2-6 4-9 3-4 6-4 11-5z" class="C"></path><path d="M445 433h2v-1c0-1 2-2 3-2-1 3-4 6-5 9l-3 8c0 2-1 4-1 7-2-2-1-4-1-6s0-3 1-5c0-1 1-2 1-4 0-1 1-2 1-2 1-2 1-3 2-4z" class="h"></path><path d="M461 420l2 1c2 1 3 1 5 1l3 4h-1c-1-1-2-2-3-2-2-1-4-1-6-1h-1c-3 1-7 3-9 6l-1 1c-1 0-3 1-3 2v1h-2c2-3 4-6 7-8-1-1-1-2-2-3l1-1h1 1c1 0 1 0 3-1l1 2c1 0 3-1 4-2z" class="AR"></path><path d="M456 420l1 2c-2 0-4 1-5 3-1-1-1-2-2-3l1-1h1 1c1 0 1 0 3-1z" class="AQ"></path><path d="M439 431c2-5 5-6 7-9l1-1h4l-1 1c1 1 1 2 2 3-3 2-5 5-7 8-1 1-1 2-2 4 0 0-1 1-1 2 0 2-1 3-1 4-1 0-1 0-1-1l-1 2-1-1c-1 2-1 4-1 6-1-2-1-3-2-5 1-2 1-4 1-6l1-2c0-2 1-4 2-5z" class="AS"></path><path d="M441 433c1 0 1 0 2 1l-1 1v4c0 2-1 3-1 4-1 0-1 0-1-1l-1 2-1-1c0-1 0-3 1-5s1-4 2-5z" class="m"></path><path d="M439 431c2-5 5-6 7-9l1-1h4l-1 1c-4 2-6 6-8 10l-1 1c-1 1-1 3-2 5s-1 4-1 5c-1 2-1 4-1 6-1-2-1-3-2-5 1-2 1-4 1-6l1-2c0-2 1-4 2-5z" class="W"></path><path d="M439 431c1 2-1 5 0 7-1 2-1 4-1 5-1 2-1 4-1 6-1-2-1-3-2-5 1-2 1-4 1-6l1-2c0-2 1-4 2-5z" class="c"></path><path d="M467 424c1 0 2 1 3 2 2 2 4 4 5 7 3 5 4 11 6 17v4c-2 0-2 0-4-2l-1-6c-1-2-2-5-4-7-3-4-6-6-11-7h0s0-1 1-1c-3-1-5-1-7 0-2 0-2 1-4 1v-1c1-2 4-4 6-5h2c1-1 2-1 3-1h1c1-1 3-1 4-1z" class="Ac"></path><path d="M467 424c1 0 2 1 3 2 2 2 4 4 5 7-3-1-4 0-6-2-1-2-4-4-6-5-1 1-2 0-4 1l1 1h-1-1l-2 1c-1 0-1 1-1 2-2 0-2 1-4 1v-1c1-2 4-4 6-5h2c1-1 2-1 3-1h1c1-1 3-1 4-1z" class="AU"></path><path d="M429 447l1-3c0-2 1-4 1-7l3-6v5c0 1 0 2 2 3v-1c0 2 0 4-1 6 1 2 1 3 2 5 0-2 0-4 1-6l1 1 1-2c0 1 0 1 1 1-1 2-1 3-1 5s-1 4 1 6c1 1 0 2 0 4v14c1 1 1 3 1 4 1 4 2 8 4 11v-1c2 3 3 6 6 9l-1 3 3 2 1 2-3 2c0-1-2-2-3-4v2 5s0 1-1 2h0c-2-1-3-2-4-4h-1c-1-2-1-5-2-7l-3-6-6-13c0-2-1-5-1-7-1-1-1-3-2-5s-1-5-2-7l-1-1 1-1v-1c-1-1-2-2-2-3v-2l-1-1v-3c1 1 1 3 2 4l1-1c1-1 1-2 1-4h1z" class="R"></path><path d="M434 447c0-4-1-7 0-11 0 1 0 2 2 3v-1c0 2 0 4-1 6v3h-1z" class="W"></path><path d="M435 470c-1-3-3-6-3-9l-1-1v-5l1-1c-1-1-1-3-1-5h1v3h1c0-1 0-1 1-3v1 4 1c1 3 0 7 2 11l1 7c-1-1-2-2-2-3z" class="C"></path><path d="M435 444c1 2 1 3 2 5v5c-1 4 0 7 0 11-1 3 2 6 1 10l-1-2-1-7c-2-4-1-8-2-11v-1-4-1-2h1v-3z" class="Z"></path><path d="M429 447c-1 10 2 19 5 28 0 0-1 0-1-1h0v-2h0-1c-1-1-1 0-1-1s-1-3-2-4c-1-2-1-5-2-7l-1-1 1-1v-1c-1-1-2-2-2-3v-2l-1-1v-3c1 1 1 3 2 4l1-1c1-1 1-2 1-4h1z" class="AH"></path><path d="M438 443l1 1 1-2c0 1 0 1 1 1-1 2-1 3-1 5s-1 4 1 6c1 1 0 2 0 4v14c1 1 1 3 1 4v7l-4-8c1-4-2-7-1-10 0-4-1-7 0-11v-5c0-2 0-4 1-6z" class="a"></path><path d="M437 454v6l1-1c0-2 0-3 1-5 1 6 0 13 2 18 1 1 1 3 1 4v7l-4-8c1-4-2-7-1-10 0-4-1-7 0-11z" class="W"></path><path d="M429 467c1 1 2 3 2 4s0 0 1 1h1 0v2h0c0 1 1 1 1 1l1 1c0-2-1-4-1-6h0 1c0 1 1 2 2 3l1 2 4 8v-7c1 4 2 8 4 11v-1c2 3 3 6 6 9l-1 3 3 2 1 2-3 2c0-1-2-2-3-4v2 5s0 1-1 2h0c-2-1-3-2-4-4h-1c-1-2-1-5-2-7l-3-6-6-13c0-2-1-5-1-7-1-1-1-3-2-5z" class="g"></path><path d="M449 500c-2-2-4-3-5-6l1-1v-1l6 6 3 2 1 2-3 2c0-1-2-2-3-4z" class="K"></path><path d="M440 489c4 6 5 13 8 20h0c-2-1-3-2-4-4h-1c-1-2-1-5-2-7l-3-6h0c1-1 0-1 1-1l1 2 2 2h0l-1-2c-1-1-1-2-1-3v-1z" class="t"></path><path d="M442 476c1 4 2 8 4 11v-1c2 3 3 6 6 9l-1 3-6-6-3-9v-7z" class="m"></path><path d="M429 467c1 1 2 3 2 4s0 0 1 1h1 0v2h0c0 1 1 1 1 1l1 1 3 9c1 1 2 3 2 4v1c0 1 0 2 1 3l1 2h0l-2-2-1-2c-1 0 0 0-1 1h0l-6-13c0-2-1-5-1-7-1-1-1-3-2-5z" class="AP"></path><defs><linearGradient id="DQ" x1="702.806" y1="264.713" x2="673.517" y2="258.086" xlink:href="#B"><stop offset="0" stop-color="#080809"></stop><stop offset="1" stop-color="#383737"></stop></linearGradient></defs><path fill="url(#DQ)" d="M743 183c6-7 14-14 21-18 0 0 0 1-1 2-1 0-1 1-2 2-3 3-9 6-10 10 0 1-1 2-1 3l2-2v-1h2l5-3c-1 4-5 7-8 10h0l-5 6c0 2-1 5-2 7-1 1-2 2-2 4-2 2-3 6-4 9h1v2l-2 5-12 40c-3 9-22 60-21 65l1 1c0 1-1 2-2 3l1 1h1c0 1-1 2 0 4l-1 1h-1c-1-1-2 0-3 0h0l-2 3c-3 11-7 22-1 33 0 1 1 2 1 2 1 1 0 1 1 1l-1 1-3-1h-3c-2 1-4 2-5 4l-2 2c-2 2-3 6-5 9 0 0 0 1-1 1l-1 1v1h-2v1c-1 0-1 0-1 1l-1 1-1 1v1l-1-1-1 2h-1c-2 1-3 2-4 3-2 0-2 1-3 2h0v-3c-1-1 0-2 1-3h-1v-1-2c0-2 1-3 2-5l-1-1 16-49 6-23-1-2c-1 0-3-1-4-2-3 1-4 3-7 5 0 1 0 1 1 2l-1 3h0v-2h-1v-2h-3l-1-1c1-2 3-3 4-4 2-1 3-3 5-4l1-2-5 3-5 4-6-18-1-2v-6l1-4c0-2 1-4 1-6l1-5 3-14 4-8 5-8c0-1 0-1 1-2 2-2 4-5 6-7h1c1-1 4-4 5-6l4-3c1 1 1 2 1 4l2-2 1 1 5-6c2 1 3 2 3 4h1 1c2 1 2 1 3 3h1c0-1 0-2 1-2v-1c1-2 0 0 1-1v-1-1c2-1 2-3 3-5h1s1-3 1-4c1 2 1 3 1 5l2-3v1c0 3-2 5-2 7 0-1 1-2 2-2 0-1 0-2 1-2v-1-3l1 3 7-20 4-17c1 3 3 4 3 7v-1l1 1v2h0c1-3 2-5 4-7z"></path><path d="M692 235c1-1 1-2 3-2v-1c1-1 3-1 4-1 1 1 1 0 1 1h1 1c-1 2-2 4-3 5-1-1-1-1-1-2l-1-1c1-1 1-1 1-2-2 0-4 2-6 3z" class="y"></path><path d="M694 224c1 1 1 2 1 4-5 4-8 9-12 13 0-1 0-2 1-3s1-2 1-3l-1-1 1-1c1-1 4-4 5-6l4-3z" class="u"></path><defs><linearGradient id="DR" x1="664.1" y1="293.833" x2="671.4" y2="286.167" xlink:href="#B"><stop offset="0" stop-color="#403d3d"></stop><stop offset="1" stop-color="#5e5c5c"></stop></linearGradient></defs><path fill="url(#DR)" d="M668 266c0 4-2 11 0 16v2-1c1-1 1-1 1-2v11l1 7c0 1 1 2 1 3l-1 1c-2-5-3-10-4-15 0-3 0-6-1-9 0-5 1-9 3-13z"></path><defs><linearGradient id="DS" x1="662.357" y1="277.191" x2="675.643" y2="265.309" xlink:href="#B"><stop offset="0" stop-color="#686767"></stop><stop offset="1" stop-color="#948e8d"></stop></linearGradient></defs><path fill="url(#DS)" d="M668 266c0-4 3-11 5-15v1c0 2-1 2-1 4-1 1-1 1-1 2h1c0 2 0 3-1 4l1 1v-1c0-2 1-3 2-4v-1c-3 8-5 16-5 24 0 1 0 1-1 2v1-2c-2-5 0-12 0-16z"></path><path d="M684 233h1l-1 1c-2 3-11 14-11 17-2 4-5 11-5 15-2 4-3 8-3 13 1 3 1 6 1 9l-1-2h0l-2-3c0-2 1-4 1-6l1-5 3-14 4-8 5-8c0-1 0-1 1-2 2-2 4-5 6-7z" class="F"></path><path d="M684 234l1 1c0 1 0 2-1 3s-1 2-1 3c-4 6-7 11-9 16v1c-1 1-2 2-2 4v1l-1-1c1-1 1-2 1-4h-1c0-1 0-1 1-2 0-2 1-2 1-4v-1c0-3 9-14 11-17z" class="M"></path><path d="M663 283l2 3h0l1 2c1 5 2 10 4 15v1c1 1 1 2 1 4h1l2 1-5 4-6-18-1-2v-6l1-4z" class="y"></path><path d="M719 213c1 2 1 3 1 5l2-3v1c0 3-2 5-2 7 0-1 1-2 2-2 0-1 0-2 1-2v-1-3l1 3c-1 3-2 7-4 10-1 2-2 4-3 5h0l-2 6c-1 4-2 9-4 13l-4 11-1 2-1 1v2c0 1-1 2-1 3h0-1c0-3 0-5-1-7l1-1c-1-1 0-4-1-6h-2c-3 0-6 2-8 1l5-4c-1-1-2-1-4-2l8-7 5-6c4-7 9-14 12-22 0 0 1-3 1-4z" class="AM"></path><path d="M701 245v2h0c-1 2-2 4-4 5v2h0c-1-1-2-1-4-2l8-7z" class="t"></path><path d="M719 213c1 2 1 3 1 5-3 7-7 16-13 22v-1h-1c4-7 9-14 12-22 0 0 1-3 1-4z" class="m"></path><path d="M697 254c5-2 10-10 14-14 1-2 3-6 6-7l-2 6c-2 2-4 5-5 8l-9 9-1 1c-3 0-6 2-8 1l5-4h0z" class="U"></path><path d="M710 247c1-3 3-6 5-8-1 4-2 9-4 13l-4 11-1 2-1 1v2c0 1-1 2-1 3h0-1c0-3 0-5-1-7l1-1c-1-1 0-4-1-6h-2l1-1 9-9z" class="J"></path><path d="M701 256c3 0 4-3 6-4-1 3-1 5-2 7-1 1-1 2-1 3-1 1-1 0-1 1-1-1 0-4-1-6h-2l1-1z" class="R"></path><path d="M710 247c1-3 3-6 5-8-1 4-2 9-4 13l-4 11-1 2-1-1h0c1-6 4-11 5-17z" class="w"></path><defs><linearGradient id="DT" x1="689.431" y1="267.163" x2="676.566" y2="260.927" xlink:href="#B"><stop offset="0" stop-color="#232425"></stop><stop offset="1" stop-color="#605d5e"></stop></linearGradient></defs><path fill="url(#DT)" d="M692 235c2-1 4-3 6-3 0 1 0 1-1 2l1 1c0 1 0 1 1 2-3 4-8 10-9 14l2 2 1-1c2 1 3 1 4 2l-5 4c2 1 5-1 8-1h2c1 2 0 5 1 6l-1 1c1 2 1 4 1 7h-1c0 2-1 3-2 5-1-1-1-2-2-3h-2-2c-2-1-2-2-4-2-1 0-1 1-2 2v1 1c1 1 1 2 1 3l-1 1c-2 1-3 6-4 8l-1 4v2c0 3-3 4-1 7l1 1c0 1-4 4-4 5l-5 3-2-1v-6c1-4-1-7-1-11 0-6 1-12 2-18h0v3c-1 3 0 6 0 9 1-2 1-3 1-5v-9c1-3 1-7 2-10h0c4-10 10-19 16-26z"></path><path d="M692 253l1-1c2 1 3 1 4 2l-5 4h-1c-3 2-7 5-10 7h-2v-1c3-4 9-7 13-11z" class="AH"></path><path d="M673 285c0 5-1 14 1 17l1-1h0v-9c0-4 0-7 1-11 0-4 0-10 1-14l2-2c1 3 1 9 3 11h0l2 1c0 1 0 1-1 1-1 2-1 3-2 4 1 2 1 3 3 5l-1 4v2c0 3-3 4-1 7l1 1c0 1-4 4-4 5l-5 3-2-1v-6c1-4-1-7-1-11 0-6 1-12 2-18h0v3c-1 3 0 6 0 9z" class="e"></path><path d="M700 257h2c1 2 0 5 1 6l-1 1c1 2 1 4 1 7h-1c0 2-1 3-2 5-1-1-1-2-2-3h-2-2c-2-1-2-2-4-2-1 0-1 1-2 2v1 1c1 1 1 2 1 3l-1 1c-2 1-3 6-4 8-2-2-2-3-3-5 1-1 1-2 2-4 1 0 1 0 1-1l-2-1h0c-2-2-2-8-3-11h0 2c3-2 7-5 10-7h1c2 1 5-1 8-1z" class="b"></path><path d="M697 260c1 0 2 1 3 2l-1 1c-1 1-2 1-3 1 1-1 1-3 1-4z" class="S"></path><path d="M696 265h1c-1 2-1 4 0 6 0 1 1 1 1 2h0 0-2c-2-1-3-2-5-3h0c0-2 3-4 5-5z" class="C"></path><path d="M700 257h2c1 2 0 5 1 6l-1 1-2-2c-1-1-2-2-3-2-3-1-6 1-9 2l2-2c1 0 1 0 1-2h1c2 1 5-1 8-1z" class="Z"></path><path d="M686 265l1 3-1 1c-1 2 1 5 1 7l1 1v2c-2 1-3 6-4 8-2-2-2-3-3-5 1-1 1-2 2-4 1 0 1 0 1-1l-2-1c2-1 2-1 3-2 0-2 1-3 0-5l1-4z" class="AK"></path><path d="M700 262l2 2c1 2 1 4 1 7h-1c0 2-1 3-2 5-1-1-1-2-2-3h0 0c0-1-1-1-1-2-1-2-1-4 0-6h-1v-1c1 0 2 0 3-1l1-1z" class="j"></path><path d="M701 269l1 2c0 2-1 3-2 5-1-1-1-2-2-3h0c1-1 2-3 3-4z" class="U"></path><path d="M700 262l2 2c1 2 1 4 1 7h-1l-1-2v-2h-1c1-1 0-2 0-3l-1-1 1-1z" class="T"></path><path d="M679 265h2c3-2 7-5 10-7 0 2 0 2-1 2l-2 2c-1 1-2 2-2 3l-1 4c1 2 0 3 0 5-1 1-1 1-3 2h0c-2-2-2-8-3-11h0z" class="W"></path><path d="M685 274c-1-1-1-1-2-1v-1c-1-1 0-3 1-5v1l1 1c1 2 0 3 0 5z" class="w"></path><path d="M707 263l4-11c1 2 1 3 1 5l-1 6-3 13-7 21c1 3 0 5 0 8-1 1-1 3 0 4l-6 18c-1-2-1-3-1-4 1-1 0-2-1-3l-1 2v-3c-1-2 0-3-1-5 0 1-1 2-1 3l-8 23v-1-3c-1 0-1 1-2 2l6-23-1-2c-1 0-3-1-4-2-3 1-4 3-7 5 0 1 0 1 1 2l-1 3h0v-2h-1v-2h-3l-1-1c1-2 3-3 4-4 2-1 3-3 5-4l1-2c0-1 4-4 4-5l-1-1c-2-3 1-4 1-7v-2l1-4c1-2 2-7 4-8l1-1c0-1 0-2-1-3v-1-1c1-1 1-2 2-2 2 0 2 1 4 2h2 2c1 1 1 2 2 3 1-2 2-3 2-5h1 1 0c0-1 1-2 1-3v-2l1-1 1-2z" class="h"></path><path d="M687 305l3-3c0-1 1-2 2-3-1 5-3 9-5 13 0 1 0 1-1 2v1l-1-2 2-4c1-2 1-3 0-4z" class="J"></path><path d="M704 278l-1 5h0c0 3-3 7-4 9v-3-2l1-1c0-3 2-6 4-8z" class="m"></path><path d="M687 305c1 1 1 2 0 4l-2 4c-1 0-3-1-4-2 1-3 4-5 6-6z" class="Z"></path><path d="M707 263l4-11c1 2 1 3 1 5l-1 6-3 13-1-1v-3-1-1c0 2-1 6-3 8 1-4 2-7 3-10 0-1 1-3 1-3 0-1 0-2-1-2z" class="AM"></path><path d="M704 278c2-2 3-6 3-8v1 1 3l1 1-7 21-2 2v-1c-1 1-1 3-2 4 0-1 0-2-1-3 1-1 1 0 1-1 0-2 1-4 2-6s4-6 4-9h0l1-5z" class="t"></path><path d="M699 299l2-2c1 3 0 5 0 8-1 1-1 3 0 4l-6 18c-1-2-1-3-1-4 1-1 0-2-1-3l-1 2v-3c-1-2 0-3-1-5l3-8 2-7c1 1 1 2 1 3 1-1 1-3 2-4v1z" class="AH"></path><path d="M696 299c1 1 1 2 1 3 1-1 1-3 2-4v1l-5 17c0 1-1 3-1 4l-1 2v-3c-1-2 0-3-1-5l3-8 2-7z" class="AM"></path><path d="M697 280c1 1 1 2 2 3-1 1-2 3-2 4-1 1 0 2-1 3h0l-4 9c-1 1-2 2-2 3l-3 3c-2 1-5 3-6 6-3 1-4 3-7 5 0 1 0 1 1 2l-1 3h0v-2h-1v-2h-3l-1-1c1-2 3-3 4-4 2-1 3-3 5-4l1-2c0-1 4-4 4-5 3-3 6-6 8-9l6-12z" class="a"></path><path d="M673 312c6-3 12-8 16-14 3-2 4-5 7-8l-4 9c-1 1-2 2-2 3l-3 3c-2 1-5 3-6 6-3 1-4 3-7 5 0 1 0 1 1 2l-1 3h0v-2h-1v-2h-3l-1-1c1-2 3-3 4-4z" class="T"></path><path d="M688 279l1-1c0-1 0-2-1-3v-1-1c1-1 1-2 2-2 2 0 2 1 4 2h2 2c1 1 1 2 2 3-1 1-3 3-3 4l-6 12c-2 3-5 6-8 9l-1-1c-2-3 1-4 1-7v-2l1-4c1-2 2-7 4-8z" class="AE"></path><defs><linearGradient id="DU" x1="667.8" y1="353.764" x2="692.771" y2="349.481" xlink:href="#B"><stop offset="0" stop-color="#a7352c"></stop><stop offset="1" stop-color="#c8524d"></stop></linearGradient></defs><path fill="url(#DU)" d="M702 310l3-7c1 2 1 3 0 5-1 3-1 6-2 8-1 3-2 5-2 7s-1 4-2 6l-1 6c1-2 2-3 3-5 0-1 0-1 1-2v1l1-1 1 1h1c0 1-1 2 0 4l-1 1h-1c-1-1-2 0-3 0h0l-2 3c-3 11-7 22-1 33 0 1 1 2 1 2 1 1 0 1 1 1l-1 1-3-1h-3c-2 1-4 2-5 4l-2 2c-2 2-3 6-5 9 0 0 0 1-1 1l-1 1v1h-2v1c-1 0-1 0-1 1l-1 1-1 1v1l-1-1-1 2h-1c-2 1-3 2-4 3-2 0-2 1-3 2h0v-3c-1-1 0-2 1-3h-1v-1-2c0-2 1-3 2-5l-1-1 16-49c1-1 1-2 2-2v3 1l8-23c0-1 1-2 1-3 1 2 0 3 1 5v3l1-2c1 1 2 2 1 3 0 1 0 2 1 4l6-18h0l1 1z"></path><path d="M682 340l8-23c0-1 1-2 1-3 1 2 0 3 1 5v3 1l-1-2c-2 1-3 5-3 7-1 1-2 3-2 4-1 3-3 11-5 12l1-4z" class="m"></path><path d="M678 365l6-19c2-5 3-11 6-16 1 4-1 9-3 13 0 1 0 1-1 1-2 6-3 11-5 16-1 2-1 4-3 5z" class="t"></path><path d="M678 365c2-1 2-3 3-5 2-5 3-10 5-16 0 2 0 4-1 6 0 2-1 3-1 5-2 4-4 9-4 14v1l-5 16-1-1v-2h-1c-1-1 0-3 1-5 0-1 1-2 1-3 1-3 1-7 3-10z" class="a"></path><path d="M693 320c1 1 2 2 1 3 0 1 0 2 1 4l-4 12-2 6-9 25v-1c0-5 2-10 4-14 0-2 1-3 1-5 1-2 1-4 1-6 1 0 1 0 1-1 2-4 4-9 3-13l2-7v-1l1-2z" class="AN"></path><path d="M672 371l1-2c1 1 0 2 0 3v6h1c-1 2-2 4-1 5h1v2l1 1-2 9v1l-1-1-1 2h-1c-2 1-3 2-4 3-2 0-2 1-3 2h0v-3c-1-1 0-2 1-3h-1v-1-2c0-2 1-3 2-5 3-5 4-12 6-17h0 1z" class="W"></path><path d="M669 393c2 1 2 1 3 2l-1 2h-1c-2 1-3 2-4 3 1-2 2-5 3-7z" class="j"></path><path d="M665 388c3-5 4-12 6-17h0 1l-3 11c-1 4-3 10-5 14h-1v-1-2c0-2 1-3 2-5z" class="J"></path><path d="M673 378h1c-1 2-2 4-1 5h1v2l1 1-2 9v1l-1-1c-1-1-1-1-3-2l2-5c0-4 1-7 2-10z" class="c"></path><path d="M702 310l3-7c1 2 1 3 0 5-1 3-1 6-2 8-1 3-2 5-2 7s-1 4-2 6l-1 6c1-2 2-3 3-5 0-1 0-1 1-2v1l1-1 1 1h1c0 1-1 2 0 4l-1 1h-1c-1-1-2 0-3 0h0l-2 3c-3 11-7 22-1 33 0 1 1 2 1 2 1 1 0 1 1 1l-1 1-3-1h-3c-2 1-4 2-5 4l-2 2c-2 2-3 6-5 9 0 0 0 1-1 1l-1 1v1h-2v1c-1 0-1 0-1 1l-1 1-1 1 2-9 5-16 9-25 2-6 4-12 6-18h0l1 1z" class="AK"></path><path d="M702 329l1-1 1 1h1c0 1-1 2 0 4l-1 1h-1c-1-1-2 0-3 0l2-5z" class="J"></path><path d="M691 339l3-6c1-3 2-5 4-8 0 4-3 9-5 13 0 2-1 3-2 4h0 0c-1 2-1 2-2 3l2-6z" class="g"></path><path d="M701 309l1 1-4 15c-2 3-3 5-4 8l-3 6 4-12 6-18h0z" class="AC"></path><path d="M695 373l-1-1c0-4-1-7-1-10-1-5 0-9 0-14 1-2 1-4 1-6h1c0-1 0-2 1-3h0c0-1 1-2 2-2h0c-3 11-7 22-1 33 0 1 1 2 1 2 1 1 0 1 1 1l-1 1-3-1z" class="j"></path><defs><linearGradient id="DV" x1="734.957" y1="260.018" x2="710.885" y2="248.174" xlink:href="#B"><stop offset="0" stop-color="#672b27"></stop><stop offset="1" stop-color="#9a332f"></stop></linearGradient></defs><path fill="url(#DV)" d="M743 183c6-7 14-14 21-18 0 0 0 1-1 2-1 0-1 1-2 2-3 3-9 6-10 10 0 1-1 2-1 3l2-2v-1h2l5-3c-1 4-5 7-8 10h0l-5 6c0 2-1 5-2 7-1 1-2 2-2 4-2 2-3 6-4 9h1v2l-2 5-12 40c-3 9-22 60-21 65l1 1c0 1-1 2-2 3l-1 1v-1c-1 1-1 1-1 2-1 2-2 3-3 5l1-6c1-2 2-4 2-6s1-4 2-7c1-2 1-5 2-8 1-2 1-3 0-5l-3 7-1-1h0c-1-1-1-3 0-4 0-3 1-5 0-8l7-21 3-13 1-6c0-2 0-3-1-5 2-4 3-9 4-13l2-6h0c1-1 2-3 3-5 2-3 3-7 4-10l7-20 4-17c1 3 3 4 3 7v-1l1 1v2h0c1-3 2-5 4-7z"></path><path d="M729 220l2-3c0 2-1 4-1 5v1c-1 3-1 6-2 9-1 2-2 4-4 6 2-6 5-12 5-18z" class="R"></path><path d="M733 201v4h1 0l-3 10v2l-2 3h0c0-4 1-9 2-13l2-6z" class="AU"></path><path d="M738 188v-1l1 1v2h0c-1 5-3 10-5 15h0-1v-4c2-4 2-9 5-13z" class="Ad"></path><path d="M750 182l2-2v-1h2l5-3c-1 4-5 7-8 10h0c-2 1-5 4-7 6v1c2-4 3-8 6-11z" class="K"></path><path d="M731 207c-1 4-2 9-2 13h0c0 6-3 12-5 18l-2 7-1-1v-2c1-1 1 0 1-2v-1c1-2 0-3 0-4-1-2 2-6 2-8h-1v-1-1c1-1 1-2 1-2 0-1 1-2 1-4l1-2c0-1 0-1 1-2s1-2 2-4c0-1 1-2 2-4z" class="AQ"></path><path d="M735 181c1 3 3 4 3 7-3 4-3 9-5 13l-2 6c-1 2-2 3-2 4-1 2-1 3-2 4s-1 1-1 2l-1 2c0 2-1 3-1 4 0 0 0 1-1 2v1 1c-1 1-2 3-2 4-2 3-3 4-3 7h-1 0l-2 1 2-6h0c1-1 2-3 3-5 2-3 3-7 4-10l7-20 4-17z" class="AP"></path><path d="M723 227h1c0 2-3 6-2 8 0 1 1 2 0 4v1c0 2 0 1-1 2v2l1 1-3 9-1-1v1c0 1-1 2-2 4-1 1-1 3-2 5v-3c0-3 2-3 0-6l-3 9 1-6c0-2 0-3-1-5 2-4 3-9 4-13l2-1h0 1c0-3 1-4 3-7 0-1 1-3 2-4z" class="AH"></path><path d="M723 227h1c0 2-3 6-2 8-1 2-1 3-2 5v2l-1 3c-1 2-1 3-2 4v1l-2-2c0-1 1-2 1-3 1 0 1-1 1-2l1-5c0-3 1-4 3-7 0-1 1-3 2-4z" class="AT"></path><path d="M718 238c0-3 1-4 3-7l-3 13 1 1c-1 2-1 3-2 4v1l-2-2c0-1 1-2 1-3 1 0 1-1 1-2l1-5z" class="AW"></path><path d="M715 239l2-1h0 1l-1 5c0 1 0 2-1 2 0 1-1 2-1 3-1 2-1 4-1 6h0l-3 9 1-6c0-2 0-3-1-5 2-4 3-9 4-13z" class="t"></path><path d="M714 254c2 3 0 3 0 6v3c1-2 1-4 2-5 1-2 2-3 2-4v-1l1 1c-1 4-3 9-4 13l-9 27c-2 5-4 10-5 15h0c-1-1-1-3 0-4 0-3 1-5 0-8l7-21 3-13 3-9z" class="AQ"></path><path d="M518 129c1 1 1 2 1 4h0c0-1 0-2 1-2v2c1 4 3 7 5 10 2 2 4 5 6 6l1-1c0 2 0 2-1 4h3c1-1 3-1 5 0 3 2 6 6 7 10l1 1h1l11-6c7-6 18-10 27-12 5-2 11-3 16-3l1 1v2c2 1 3 1 4 1v1 2c1 0 1 1 2 2l-1 1c-7 1-13 3-19 5-12 4-23 11-32 20-7 7-12 15-17 23-3 5-5 11-6 16l1 2c-1 1-2 3-1 4h1l-1 3-1 4v4l-5 15c0 1-1 2-1 4-1-2-1-5-1-7h-1c0 3-1 6-3 9l-1 27v8l-1 2-2-20h-1v10c0 6 0 14-2 21-1 1-1 5-2 7-1 0-1 0-1 1l-1 1h-1l1-6-1-2h0l1-12c1-5 1-10 1-15 0-4 0-10-1-13-1-1-1 0-1-2v-2l-3-16c-2-2-2-3-3-5-1-4-3-7-5-10-1-3-2-5-3-8l3 3c0-2-2-5-3-7l-6-10v-1c-2-4-6-7-9-10-13-15-26-25-46-30v-1l-2-2h0v-1l2-1v-1h1c2 0 3-1 5-1h2v-3h1c-1-2 1-6 1-8l1-3c4 0 8 2 12 3 7 3 14 6 20 10 4 3 8 6 13 9v-2h1l1 1h2l2-2h-1c2-1 3-2 4-3s1 0 1-1h1c0-2 0-2 1-2 0-1 0-1-1-2 1 0 2 0 3 1l1-1c2 1 3 2 5 3 1 2 1 3 2 4 0 1 0 1 1 1h0l-1-5h0c1 0 1 1 2 1 0-2 0-6 1-8 1-4 1-8 1-12l1-10z" class="M"></path><path d="M520 254c1-1 1-3 1-4v-1-1l1-1v-3s0-1 1-2v-1c0-1 1-1 1-2 1 1-1 8-1 10-1 1-1 3-1 4v1l-1 27c-2-8-1-18-1-27z" class="L"></path><path d="M528 236l6-20 1 2c-1 1-2 3-1 4h1l-1 3-1 4v4l-5 15c0 1-1 2-1 4-1-2-1-5-1-7h-1c0-1 0-4 1-4 1-2 1-3 2-5z" class="H"></path><path d="M525 245c0-1 0-4 1-4 1-2 1-3 2-5 1 2 1 4 0 6 0 2-1 3-1 5l1 1c0 1-1 2-1 4-1-2-1-5-1-7h-1z" class="AD"></path><path d="M549 176v2c-1 1-4 2-3 3 1 0 2-1 3-2l-2 4-2 4h-1c0-1 1-2 1-3 1-1 1-1 1-2-5 5-7 10-10 16-2 5-5 9-6 14v-4h0l-1 1v-6l9-16c3-2 5-8 8-10l3-1z" class="AJ"></path><path d="M529 203v6l1-1h0v4c-2 6-5 13-6 19v1c0 1 0 2-1 3 0 1 0 1-1 2 0 2 0 3-1 5-1 3-1 9-1 12 0 9-1 19 1 27v8l-1 2-2-20c0-23 2-47 11-68z" class="Q"></path><path d="M531 181l-2-3c0-1-1-3-2-4 0-4 1-7 3-11h2l2-2c2 1 2 0 4 2-4 0-5 0-7 3-1 1-1 2-2 3 0 3 1 5 2 8v1c2 3 5 5 7 9l-9 16c-9 21-11 45-11 68h-1v-14l-1-15c1-2 2-5 3-8l1-5c-1-5 1-9 0-13v-5h-1l-1-3v-4c2-3 1-7 2-11l1 1h0l1 1c0-1 1-2 1-2l2 2c1 1 1 1 3 1h0c1 1 1 1 2 0h0l-1-1c-3-1-4-2-6-5l3 3c2-2 3-5 4-7h1v-5z" class="B"></path><path d="M516 242c1-2 2-5 3-8l-2 22v1l-1-15zm15-61l4 6-3 6c-2 0-3 1-5 0h-1c2-2 3-5 4-7h1v-5z" class="I"></path><path d="M520 193l1 1h0l1 1c0-1 1-2 1-2l2 2c1 1 1 1 3 1h0c1 1 1 1 2 0 0 2 0 3-1 5l-2 3c-3 6-4 13-6 20 0 1-1 3-1 5-1-5 1-9 0-13v-5h-1l-1-3v-4c2-3 1-7 2-11z" class="AD"></path><path d="M520 193l1 1h0l1 1v4c-1 2-1 4-1 5 1-1 1-2 1-3h0 0v9c0 5-2 9-1 14 0 1-1 3-1 5-1-5 1-9 0-13v-5h-1l-1-3v-4c2-3 1-7 2-11z" class="M"></path><path d="M520 193l1 1c0 3-1 5-1 8v9h-1l-1-3v-4c2-3 1-7 2-11z" class="L"></path><path d="M519 133c0-1 0-2 1-2v2c1 4 3 7 5 10 2 2 4 5 6 6l1-1c0 2 0 2-1 4h3c1-1 3-1 5 0 3 2 6 6 7 10-1 1-3 3-5 4v1c-1 1-1 1-2 3-1 0-1 1-1 2-2 0-4 0-5 1s-1 2-1 3l-1 2v-1c-1-3-2-5-2-8 1-1 1-2 2-3 2-3 3-3 7-3-2-2-2-1-4-2l-2 2h-2c-2 4-3 7-3 11 1 1 2 3 2 4l2 3v5h-1c-1 2-2 5-4 7l-3-3-1-1v-2c-1-4-1-9-1-14 0-2 1-7 0-9-1-1-1-4-1-5l-1-26z" class="d"></path><path d="M524 157v-2c0-1 1-2 1-3 0-2 0-3 1-4l1-1c0-1-2-2-3-3l1-1c2 2 4 5 6 6-2 2-4 4-5 6l-2 2z" class="M"></path><path d="M526 155c1 1 3-3 4-3v1c-2 1-2 2-2 3 1 1 2 0 3 0l4 1c-4 1-7 1-9 5-2 2-2 5-3 7l-1 1c0-5 1-8 2-13h0l2-2z" class="AA"></path><path d="M522 170l1-1c1 1 1 1 2 1 1 2 1 5 2 7 1 3 1 6-1 10 0 1-1 2-2 3-2-5 0-10-1-15l-1-5z" class="L"></path><path d="M532 148c0 2 0 2-1 4h3c1-1 3-1 5 0 3 2 6 6 7 10-1 1-3 3-5 4v1c-1 1-1 1-2 3-1 0-1 1-1 2-2 0-4 0-5 1s-1 2-1 3l-1 2v-1c-1-3-2-5-2-8 1-1 1-2 2-3 2-3 3-3 7-3h1c0-1-1-1-1-2-2-2-3-2-5-2l-1-1c2 0 3 0 5-1-1-1-2 0-2 0l-4-1c-1 0-2 1-3 0 0-1 0-2 2-3v-1c-1 0-3 4-4 3 1-2 3-4 5-6l1-1z" class="o"></path><path d="M530 153c1 1 2 1 4 1-1 1-2 1-3 2-1 0-2 1-3 0 0-1 0-2 2-3zm-1 16c1-1 1-2 2-3 2-3 3-3 7-3h1v1c0 1 0 1 1 2v1c-1 1-2 1-3 1-3 0-4 2-6 4h-1c0-1 0-2-1-3z" class="AX"></path><path d="M518 129c1 1 1 2 1 4h0l1 26c0 1 0 4 1 5 1 2 0 7 0 9 0 5 0 10 1 14v2l1 1c2 3 3 4 6 5l1 1h0c-1 1-1 1-2 0h0c-2 0-2 0-3-1l-2-2s-1 1-1 2l-1-1h0l-1-1c-1 4 0 8-2 11 0-6-1-12-1-18l-1-1h0c-1 2-1 3-1 4l-1 1v-1c-1 1-1 2-1 3-2 2-3 3-6 4l-5-5-1-2c-1-5 4-10 6-15h0c0-5-1-7-4-10l-1-1c-2-1-4-1-5 0h-1c2-1 3-2 4-3s1 0 1-1h1c0-2 0-2 1-2 0-1 0-1-1-2 1 0 2 0 3 1l1-1c2 1 3 2 5 3 1 2 1 3 2 4 0 1 0 1 1 1h0l-1-5h0c1 0 1 1 2 1 0-2 0-6 1-8 1-4 1-8 1-12l1-10z" class="X"></path><path d="M517 173v4c1 3 1 6 0 9l-1-1 1-12z" class="Q"></path><path d="M507 185h1l3-2 2-2h0 0v2h1c1-1 0-2 0-3h1c0 2 0 6-1 8l-1-1c-2 1-3 1-4 1-2 0-2-1-3-2l1-1z" class="d"></path><path d="M507 174l3 1v1c-3 4-7 7-7 12 1 2 2 4 3 5s2 1 3 1c2-1 3-1 4-2-2 2-3 3-6 4l-5-5-1-2c-1-5 4-10 6-15z" class="G"></path><path d="M518 129c1 1 1 2 1 4h0l1 26c-1 3-1 6-2 9l-1 2 1 1h0c0 3 0-1 0 2v2c0 1 0 1 1 2v2c-1-1-1 0-1-1s0-1-1-1v-4l-1-22c1-4 1-8 1-12l1-10z" class="AJ"></path><path d="M520 159c0 1 0 4 1 5 1 2 0 7 0 9 0 5 0 10 1 14v2l1 1c2 3 3 4 6 5l1 1h0c-1 1-1 1-2 0h0c-2 0-2 0-3-1l-2-2s-1 1-1 2l-1-1h0l-1-1c-1 4 0 8-2 11 0-6-1-12-1-18 1-3 1-6 0-9 1 0 1 0 1 1s0 0 1 1v-2c-1-1-1-1-1-2v-2c0-3 0 1 0-2h0l-1-1 1-2c1-3 1-6 2-9z" class="AA"></path><path d="M521 173c0 5 0 10 1 14v2l1 1c2 3 3 4 6 5l1 1h0c-1 1-1 1-2 0h0c-2 0-2 0-3-1l-2-2s-1 1-1 2l-1-1h0l-1-1c0-4 1-9 0-12 0-2-1-5-1-7h1c0 1 0 1 1 2v-3zm-19-18c1 0 2 0 3 1l1-1c2 1 3 2 5 3 1 2 1 3 2 4 0 1 0 1 1 1h0c1 2 1 4 1 6v11h-1c0 1 1 2 0 3h-1v-2h0 0l-2 2-3 2h-1v-1-2c1-3 3-2 3-6v-1l-3-1h0c0-5-1-7-4-10l-1-1c-2-1-4-1-5 0h-1c2-1 3-2 4-3s1 0 1-1h1c0-2 0-2 1-2 0-1 0-1-1-2z" class="Q"></path><path d="M500 160l3 1c4 2 6 5 7 10v4l-3-1h0c0-5-1-7-4-10l-1-1c-2-1-4-1-5 0h-1c2-1 3-2 4-3z" class="x"></path><path d="M502 155c1 0 2 0 3 1l1-1c2 1 3 2 5 3 1 2 1 3 2 4 0 1 0 1 1 1h0c1 2 1 4 1 6h0c0-1 0-2-1-3h0c-1 0-1 1-1 2h-1v1l-1-4-2-1c0-1-1-2-1-2l-1-1c-1-1-2-1-3-2l-1 2-3-1c1-1 1 0 1-1h1c0-2 0-2 1-2 0-1 0-1-1-2z" class="AJ"></path><path d="M502 155c1 0 2 0 3 1h0c1 2 4 5 5 6s1 3 1 3l-2-1c0-1-1-2-1-2l-1-1c-1-1-2-1-3-2l-1 2-3-1c1-1 1 0 1-1h1c0-2 0-2 1-2 0-1 0-1-1-2z" class="L"></path><path d="M559 157c7-6 18-10 27-12 5-2 11-3 16-3l1 1v2c2 1 3 1 4 1v1 2c1 0 1 1 2 2l-1 1c-7 1-13 3-19 5-12 4-23 11-32 20 0-1-1-1-1-2-3 3-5 6-8 8h-1l2-4c-1 1-2 2-3 2-1-1 2-2 3-3v-2l-3 1c-3 2-5 8-8 10-2-4-5-6-7-9l1-2c0-1 0-2 1-3s3-1 5-1c0-1 0-2 1-2 1-2 1-2 2-3v-1c2-1 4-3 5-4l1 1h1l11-6z" class="AA"></path><path d="M546 162l1 1h1c-3 3-7 8-10 9 0-1 0-2 1-2 1-2 1-2 2-3v-1c2-1 4-3 5-4z" class="AX"></path><path d="M559 157c7-6 18-10 27-12 5-2 11-3 16-3l1 1-1 1h-4c-5 1-11 3-16 4-3 1-7 2-9 4-1 0-1 1-2 1-2 1-5 1-7 3v-1h1l1-1c-1 0-2 1-3 1-2 1-3 2-4 2zm-10 19l7-7 2-2c5-3 8-7 13-10 3-2 7-3 10-5h1 1 2c-8 4-15 9-21 14-1 1-1 2-1 3-1 2-5 5-7 6-3 3-5 6-8 8h-1l2-4c-1 1-2 2-3 2-1-1 2-2 3-3v-2z" class="AV"></path><path d="M549 179l15-13c-1 1-1 2-1 3-1 2-5 5-7 6-3 3-5 6-8 8h-1l2-4z" class="d"></path><path d="M602 144l1-1v2c2 1 3 1 4 1v1 2c1 0 1 1 2 2l-1 1c-7 1-13 3-19 5-12 4-23 11-32 20 0-1-1-1-1-2 2-1 6-4 7-6 0-1 0-2 1-3 6-5 13-10 21-14 3 0 6-3 9-4 3 0 6-1 8-2v-2z" class="Y"></path><path d="M446 144c4 0 8 2 12 3 7 3 14 6 20 10 4 3 8 6 13 9v-2h1l1 1h2l2-2c1-1 3-1 5 0l1 1c3 3 4 5 4 10h0c-2 5-7 10-6 15l1 2 5 5c3-1 4-2 6-4 0-1 0-2 1-3v1l1-1c0-1 0-2 1-4h0l1 1c0 6 1 12 1 18v4l1 3h1v5c1 4-1 8 0 13l-1 5c-1 3-2 6-3 8l1 15v14 10c0 6 0 14-2 21-1 1-1 5-2 7-1 0-1 0-1 1l-1 1h-1l1-6-1-2h0l1-12c1-5 1-10 1-15 0-4 0-10-1-13-1-1-1 0-1-2v-2l-3-16c-2-2-2-3-3-5-1-4-3-7-5-10-1-3-2-5-3-8l3 3c0-2-2-5-3-7l-6-10v-1c-2-4-6-7-9-10-13-15-26-25-46-30v-1l-2-2h0v-1l2-1v-1h1c2 0 3-1 5-1h2v-3h1c-1-2 1-6 1-8l1-3z" class="Q"></path><path d="M502 191l5 5c1 2 3 5 4 7v2l1 3 1 1c1 4 2 10 2 15-1-1-1-2-1-4 0-3-3-8-4-11-2-3-3-7-5-9l-1-1c0-1 0-1-1-2h0c1-1 1-1 1-2h1l-1-1c-1-1-2-1-3-3h1z" class="L"></path><path d="M516 185l1 1c0 6 1 12 1 18v4l1 3h1v5c1 4-1 8 0 13l-1 5c-1 3-2 6-3 8l-3-15c0-3 0-4 1-7 0 2 0 3 1 4 0-5-1-11-2-15l-1-1-1-3v-2c-1-2-3-5-4-7 3-1 4-2 6-4 0-1 0-2 1-3v1l1-1c0-1 0-2 1-4h0z" class="M"></path><path d="M517 215c-1-2-3-9-3-11-1-2-1-8 0-10 2 2 0 6 1 9 0 3 1 6 2 9v3z" class="X"></path><defs><linearGradient id="DW" x1="510.336" y1="228.941" x2="524.121" y2="221.546" xlink:href="#B"><stop offset="0" stop-color="#a09b95"></stop><stop offset="1" stop-color="#bdb3b1"></stop></linearGradient></defs><path fill="url(#DW)" d="M518 208l1 3h1v5c1 4-1 8 0 13l-1 5c-1 3-2 6-3 8l-3-15c0-3 0-4 1-7 0 2 0 3 1 4 0 1 1 2 1 3s0 2 1 3v-1c0-3 1-12 0-14v-3l1-4z"></path><path d="M459 161c-2-2-3-3-5-3-1-1-2-2-4-2-2-1-3-1-5-2 0-1 0-2 1-3 1 1 2 2 3 2l6 3c14 6 26 17 36 28 3 3 5 4 7 7 3 7 7 14 10 21 2 5 3 10 5 15l3 15 1 15v14 10c-2-3-1-16-1-21-1-1-1-1-1-2 0-3-1-6-1-9-1-2-1-5-1-7 0-3-1-5-2-8l-2-9c-7-20-16-36-31-50-5-4-10-9-15-12l-4-2z" class="AA"></path><defs><linearGradient id="DX" x1="470.497" y1="169.148" x2="487.332" y2="151.333" xlink:href="#B"><stop offset="0" stop-color="#c6bdb6"></stop><stop offset="1" stop-color="#e9e7e6"></stop></linearGradient></defs><path fill="url(#DX)" d="M446 144c4 0 8 2 12 3 7 3 14 6 20 10 4 3 8 6 13 9v-2h1l1 1h2l2-2c1-1 3-1 5 0l1 1c3 3 4 5 4 10h0c-2 5-7 10-6 15l1 2h-1c-2-3-3-6-5-9-3-4-6-7-9-10-7-5-13-11-21-16-6-3-14-6-21-9l1-3z"></path><path d="M497 163c1-1 3-1 5 0l1 1c1 2 2 4 1 6h-1 0c-2-2-4-2-6-2-1-1-2-2-2-3l2-2z" class="AX"></path><defs><linearGradient id="DY" x1="420.549" y1="173.92" x2="534.75" y2="275.141" xlink:href="#B"><stop offset="0" stop-color="#5d5c5c"></stop><stop offset="1" stop-color="#988e8a"></stop></linearGradient></defs><path fill="url(#DY)" d="M444 155c5 1 10 4 15 6l4 2c5 3 10 8 15 12 15 14 24 30 31 50l2 9c1 3 2 5 2 8 0 2 0 5 1 7 0 3 1 6 1 9 0 1 0 1 1 2 0 5-1 18 1 21 0 6 0 14-2 21-1 1-1 5-2 7-1 0-1 0-1 1l-1 1h-1l1-6-1-2h0l1-12c1-5 1-10 1-15 0-4 0-10-1-13-1-1-1 0-1-2v-2l-3-16c-2-2-2-3-3-5-1-4-3-7-5-10-1-3-2-5-3-8l3 3c0-2-2-5-3-7l-6-10v-1c-2-4-6-7-9-10-13-15-26-25-46-30v-1l-2-2h0v-1l2-1v-1h1c2 0 3-1 5-1h2v-3h1z"></path><path d="M496 220l3 3c4 6 7 13 8 20-2-2-2-3-3-5-1-4-3-7-5-10-1-3-2-5-3-8z" class="o"></path><path d="M444 155c5 1 10 4 15 6l4 2-1 1-1-1c-1-1-6-3-7-4-2 0-3-1-5-1-2-1-2 0-3 1h1l4 2h0c-3-1-4-1-7-1-2 0-6 0-8-1 2 0 3-1 5-1h2v-3h1z" class="AF"></path><path d="M511 234c1 3 2 5 2 8 0 2 0 5 1 7 0 3 1 6 1 9 0 1 0 1 1 2 0 5-1 18 1 21 0 6 0 14-2 21-1 1-1 5-2 7-1 0-1 0-1 1l-1 1h-1l1-6c2-7 3-14 3-22v-18c0-4 0-9-1-13s-1-7-2-10v-3-5z" class="d"></path><defs><linearGradient id="DZ" x1="515.013" y1="657.795" x2="482.14" y2="650.843" xlink:href="#B"><stop offset="0" stop-color="#8a221c"></stop><stop offset="1" stop-color="#e04b40"></stop></linearGradient></defs><path fill="url(#DZ)" d="M490 537c2 1 7 4 8 6l1 1c0 1 0 1 1 2l-1 2c-1 0-2-1-2-2-1 1-1 2-2 3-2 1-3 1-5 1v4c1 0 1 1 2 1l4 3 1 1h1c1 2 4 4 6 5v4c1 1 3 2 3 3l5 4 1 2-1 1c6 4 9 9 14 13l-1 1c1 1 2 3 3 4l-1 1v1c1 0 3 2 4 3l9 9c1 1 4 5 5 6v-2l1 1h0c1 3 4 7 5 10l1 1c1 1 2 3 2 5l4 9 3 6v1l-2 1c-2 1-4 2-6 2-1 0-2 0-2 1 1 1 2 1 3 1l-1 2c0 1 0 2 1 3v1l-3 8 1 1c2 0 3-1 4-3 1 1 2 2 2 4h0v2 1c1 1 1 1 1 2 1 1 1 2 1 2v1c0 2 1 4 2 6 0 2-1 4-1 6-1 2-3 4-4 6h3l1-3 2-1 1 1c-1 3-3 5-5 7v1c1-1 2-1 3-2v-1c2-2 2-3 4-3 1-1 2-3 3-4 0-1 2-5 3-6v4l1 1-1 2-6 11c-1 3-3 6-5 8v1c-1 2-3 4-4 6-3 4-7 9-11 12-2 1-3 2-5 4h0l-6 5c-2 1-4 2-5 3-2-2-2-5-3-8s-2-6-4-9c-1-1-2-1-2-2l-1-4-1-3h-1c-1-3-1-6-3-9l-1 1c-1-2-2-6-2-8l-24-66-3-10c-1-1-4-11-5-13l-8-21c-1-4-3-7-4-10v-1c0-1 0-1 1-2l-1-6 1-1-2-6h3c0 2 0 3 1 4 0 0 1-1 1-2v-5c1-3 2-5 3-7l5-3c2-2 3-5 5-6s4-2 5-2v-1l-2-1 2-2z"></path><path d="M495 608l1 3v-4c1 0 2 0 3 1l-1 1v1 1 2l1-1h0c-1 10 0 18 1 28l-1 1h1v1 1l1 1v2 1c0 1 0 1 1 2v2h0c1 1 1 1 1 2h0v4c-1-2-2-4-2-6-1-1-1-3-2-4 0-1-1-1-1-1v-2c-1-2-1-2-1-3l-1-1h-1c-1-1-1-1-1-2v-1l-1-4v1c1 1 1 1 1 2l1-1 1-1c0-2-1-5-1-6-1-6 0-14 0-20z" class="a"></path><path d="M506 662c2 0 2 1 3 3 1 4 11 25 15 27h0c-2 1-3 3-5 4v-1c-2 2-2 5-3 7-1-3-2-7-3-10-1-2-1-4-2-6-1-1-1-2-1-3 1-2 1-4 1-6l-5-15z" class="N"></path><path d="M511 677c2 3 3 7 5 10l3 6v1 1c-2 2-2 5-3 7-1-3-2-7-3-10-1-2-1-4-2-6-1-1-1-2-1-3 1-2 1-4 1-6z" class="i"></path><path d="M500 593c0 2-2 4-3 5-1 3-1 7-2 10 0 6-1 14 0 20 0 1 1 4 1 6l-1 1-1 1c0-1 0-1-1-2v-1h-1v-1c-1-2-1-2-2-3l1-1v1h1c-1-2-1-4-2-5v-2l-3-6-1-3h1l1 4h0c1-2-1-6-1-8h1l1 1h0c0-3-1-6-2-8l1-1c1 1 2 1 3 1 3-1 5-4 7-6l2-3z" class="c"></path><path d="M488 601c1 1 2 1 3 1l1 1h0v6c0 3 2 10 1 12-1-4-2-8-4-11 0-3-1-6-2-8l1-1z" class="AI"></path><path d="M486 613h1l1 4h0c1-2-1-6-1-8h1l1 1h0c2 3 3 7 4 11v4l1 1 1 9-1 1c0-1 0-1-1-2v-1h-1v-1c-1-2-1-2-2-3l1-1v1h1c-1-2-1-4-2-5v-2l-3-6-1-3z" class="h"></path><path d="M468 578v-1c0-1 0-1 1-2v2c1 2 1 3 2 5l2-1 3 6c1 0 2 1 3 1l1 1h5c2-2 4-3 4-5h1v2l-2 2c0 3-2 5-4 7 1 2 2 4 4 6l-1 1c1 2 2 5 2 8h0l-1-1h-1c0 2 2 6 1 8h0l-1-4h-1l1 3-1 1v1 2c1 1 2 2 1 4-1-1-1-2-1-2h-1c-1-1-4-11-5-13l-8-21c-1-4-3-7-4-10z" class="AM"></path><path d="M471 582l2-1 3 6c1 3 4 6 5 9 0 2 0 3 1 4v1c-1 0-1-1-2-2-2-3-4-4-5-8l-4-9z" class="AO"></path><path d="M489 584h1v2l-2 2c0 3-2 5-4 7 1 2 2 4 4 6l-1 1c1 2 2 5 2 8h0l-1-1h-1c0 2 2 6 1 8h0l-1-4h-1c-1-1-1-2-1-3l-3-9v-1c-1-1-1-2-1-4-1-3-4-6-5-9 1 0 2 1 3 1l1 1h5c2-2 4-3 4-5z" class="a"></path><path d="M476 587c1 0 2 1 3 1l1 1h5l-2 3c-2 0-2-3-3-2 1 3 3 6 4 9h0l-3-3c-1-3-4-6-5-9z" class="AN"></path><defs><linearGradient id="Da" x1="493.59" y1="617.671" x2="519.837" y2="636.294" xlink:href="#B"><stop offset="0" stop-color="#0b0000"></stop><stop offset="1" stop-color="#471412"></stop></linearGradient></defs><path fill="url(#Da)" d="M500 607c1-1 2-2 2-3 1-1 1-1 1-2 3-2 6-4 10-5l2 2c1 2 1 3 1 5 0 3-1 4-4 7-2 0-3 3-4 3-3 3-4 5-5 9-1 2-1 5-1 8 1 1 1 1 1 2v1c1 3 1 7 2 9 0 5 1 11 2 16 1 2 2 4 2 6-1-2-1-3-3-3-1-2-1-5-2-7l-4-15c-1-10-2-18-1-28l1-5z"></path><path d="M516 604c0 3-1 4-4 7-2 0-3 3-4 3h-2-1v-1c1-1 1-3 2-4 2 0 2 0 4-1 1-3 3-2 5-4h0z" class="AG"></path><path d="M505 643v1c1 2 1 2 1 4 0 1 1 1 1 2 1 0 1 0 1 1v-2l1 4v2c1-1 0-3 1-5v-3h1l1 7c1-2 1-3 1-4h1v1l1-1h1c0 1 0 1 1 2l2-1h0v4c1 3 1 7 2 10v1 2c1 5 4 11 7 15 0 1 2 3 3 5v1c0 1 2 2 2 3l1 1 3 3h0c0 2 2 2 1 4h1c0 1 1 2 1 3-1-1-1-1-2-1h-2c-1-1-3-3-5-4-1-2-2-4-3-5h-2c-1-1-1-1-2-1h0c-4-2-14-23-15-27 0-2-1-4-2-6-1-5-2-11-2-16z" class="q"></path><path d="M515 650h1c0 1 0 1 1 2l2-1h0v4c1 3 1 7 2 10v1 2c1 5 4 11 7 15 0 1 2 3 3 5v1c0 1 2 2 2 3l1 1 3 3h0c0 2 2 2 1 4h1c0 1 1 2 1 3-1-1-1-1-2-1h-2c-1-1-3-3-5-4-1-2-2-4-3-5l-4-10c-2-3-4-8-5-12l-2-5h-1 0c-2-3-3-6-3-8v-2c-1-1-1-1-1-2 1-2 1-3 1-4h1v1l1-1z" class="j"></path><path d="M515 650h1c0 1 0 1 1 2l2-1h0v4c1 3 1 7 2 10v1l-1 1h-1c-2-3-2-8-5-12l3 11h-1 0c-2-3-3-6-3-8v-2c-1-1-1-1-1-2 1-2 1-3 1-4h1v1l1-1z" class="C"></path><path d="M504 568c1 1 3 2 3 3l5 4 1 2-1 1c6 4 9 9 14 13l-1 1c1 1 2 3 3 4l-1 1v1c-1-1-2-1-3-2l-1 1h-1c-1 1-1 3-2 5h-1c-2 4-3 7-7 9 3-3 4-4 4-7 0-2 0-3-1-5l-2-2c-4 1-7 3-10 5 0 1 0 1-1 2 0 1-1 2-2 3l-1 5h0l-1 1v-2-1-1l1-1c-1-1-2-1-3-1v4l-1-3c1-3 1-7 2-10 1-1 3-3 3-5l-2 3c-2 2-4 5-7 6-1 0-2 0-3-1-2-2-3-4-4-6 2-2 4-4 4-7l2-2 1-3 1 1c0 1 0 2-1 3 1 1 2 2 2 3v1h0l3-3h1c1-1 0-1 1-1l3-3v-2-1c1 1 1 1 2 0l-1-1c-2 0-3-1-5-2l1-1 1-1h1l1 1 1-1 2-2v-6z" class="s"></path><path d="M493 591h0 1v1c0 1 0 2-1 3h-3l-1-1c0-1 0-1 1-1 0-2 1-2 3-2z" class="N"></path><path d="M522 589h-2-1c-1 0-1 0-2-1-1 0-2-2-2-4h1c2 1 4 3 6 5z" class="l"></path><path d="M510 578c1-1 1-1 2 0 6 4 9 9 14 13l-1 1-3-3c-2-2-4-4-6-5-2-3-4-5-6-6z" class="J"></path><path d="M504 568c1 1 3 2 3 3l5 4 1 2-1 1c-1-1-1-1-2 0h-4-2c-1 0-1 1-2 1s-1 0-1-1c-1 0-1-1-2-2h1l1 1 1-1 2-2v-6z" class="AC"></path><path d="M490 586l1-3 1 1c0 1 0 2-1 3l-2 4c-1 1-2 2-1 4 0 1 0 2 1 2 2 0 3 0 4-1 2-1 3-4 4-5 1-2 2-3 3-4 1 0 1 0 1 1v3h1l-2 2-2 3c-2 2-4 5-7 6-1 0-2 0-3-1-2-2-3-4-4-6 2-2 4-4 4-7l2-2z" class="Aa"></path><path d="M500 587c1 0 1 0 1 1v3h1l-2 2-2 3c-1-1-2-1-2-2 1-1 1-2 1-3 1-2 2-3 3-4z" class="t"></path><path d="M500 587c3-2 5-5 9-5 2 0 3 1 4 2l1 1c-1 1 0 1-1 1s0 0-1 1h-2v1h0l7 2c2 1 3 1 4 2 1 2 1 3 0 4 0 1-1 1-2 2v4c-2 4-3 7-7 9 3-3 4-4 4-7 0-2 0-3-1-5l-2-2c-4 1-7 3-10 5 0 1 0 1-1 2 0 1-1 2-2 3l-1 5h0l-1 1v-2-1-1l1-1c-1-1-2-1-3-1v4l-1-3c1-3 1-7 2-10 1-1 3-3 3-5l2-2h-1v-3c0-1 0-1-1-1z" class="AN"></path><path d="M510 588l7 2c-3 2-7 0-10 2s-4 4-6 6c0-2 1-3 1-5 2-3 5-4 8-5z" class="AQ"></path><path d="M500 587c3-2 5-5 9-5 2 0 3 1 4 2l-1 1-1-1v1h-3c0 1-1 2-2 2l-4 4h-1v-3c0-1 0-1-1-1z" class="AS"></path><path d="M501 588c2-1 3-2 4-3v-1h3v1h-1l-1 2-4 4h-1v-3z" class="AH"></path><path d="M517 590c2 1 3 1 4 2 1 2 1 3 0 4 0 1-1 1-2 2v4c-2 4-3 7-7 9 3-3 4-4 4-7 0-2 0-3-1-5l-2-2c-4 1-7 3-10 5 0 1 0 1-1 2 0 1-1 2-2 3 0-3 1-6 2-8 2-4 5-5 9-6l-4-1c3-2 7 0 10-2z" class="i"></path><path d="M517 590c2 1 3 1 4 2v2l-6-1h-4l-4-1c3-2 7 0 10-2z" class="AH"></path><defs><linearGradient id="Db" x1="473.056" y1="558.218" x2="498.138" y2="564.33" xlink:href="#B"><stop offset="0" stop-color="#130604"></stop><stop offset="1" stop-color="#380e0d"></stop></linearGradient></defs><path fill="url(#Db)" d="M490 537c2 1 7 4 8 6l1 1c0 1 0 1 1 2l-1 2c-1 0-2-1-2-2-1 1-1 2-2 3-2 1-3 1-5 1v4c1 0 1 1 2 1l4 3 1 1h1c1 2 4 4 6 5v4 6l-2 2-1 1-1-1h-1l-1 1-1 1c2 1 3 2 5 2l1 1c-1 1-1 1-2 0v1 2l-3 3c-1 0 0 0-1 1h-1l-3 3h0v-1c0-1-1-2-2-3 1-1 1-2 1-3l-1-1-1 3v-2h-1c0 2-2 3-4 5h-5l-1-1c-1 0-2-1-3-1l-3-6-2 1c-1-2-1-3-2-5v-2l-1-6 1-1-2-6h3c0 2 0 3 1 4 0 0 1-1 1-2v-5c1-3 2-5 3-7l5-3c2-2 3-5 5-6s4-2 5-2v-1l-2-1 2-2z"></path><path d="M477 560c0 1 1 2 1 4l-2 2-1-2c0-2 1-3 2-4z" class="U"></path><path d="M487 569h3c2 0 4 0 5 1h-5l-2 1v1c-1 1-1 3-1 4 0-1-1-1-1-2s-1-1-1-2c1-1 1-1 2-3z" class="AI"></path><path d="M480 574c1 3 2 4 4 7h0c0 2 1 3 3 5-2 1-3 2-5 2h0c1-1 1-2 2-3-1-1-1-1-2-1-1-1-2-4-2-6v-4z" class="j"></path><path d="M480 574l1-1h2l1-2 1 1c0 1 1 1 1 2l-1 1 1 2c1 1 1 2 0 4h0l-1-1-1 1h0c-2-3-3-4-4-7h0z" class="K"></path><path d="M480 578l-1-1c-1-2-1-5-1-7 2-2 2-2 4-2h2 1l1 1h1c-1 2-1 2-2 3l-1-1-1 2h-2l-1 1h0v4z" class="z"></path><path d="M480 574v-2l1-2c2-1 3-1 5-1h1c-1 2-1 2-2 3l-1-1-1 2h-2l-1 1z" class="j"></path><path d="M488 571l2 1c1 1 2 5 1 7 0 0-1 0-1 1l-2-1v1 3h-1l-1-2h0c1-2 1-3 0-4l-1-2 1-1c0 1 1 1 1 2 0-1 0-3 1-4v-1z" class="l"></path><path d="M491 579h1c0 1 1 3 2 4s1 3 2 5l-3 3h0v-1c0-1-1-2-2-3 1-1 1-2 1-3l-1-1-1 3v-2h-1c-1 1-2 1-2 2-2-2-3-3-3-5l1-1 1 1 1 2h1v-3-1l2 1c0-1 1-1 1-1z" class="i"></path><path d="M483 565v-1h-1c1-1 2-1 2-2l1-1h6c1 0 2 0 3 1h0c2 0 3 1 4 2l1 1c2 1 2 2 4 2v1h-1c-1 0-2-1-3-1v-1c-6-1-11-1-16-1z" class="J"></path><path d="M477 560c0-2 0-4 1-5 0-2 2-3 3-3h3c0-3 1-4 3-6l3 1-3 2h0c0 2-2 3-3 5-3 3-3 7-6 10 0-2-1-3-1-4z" class="z"></path><path d="M495 570c1 0 2 1 3 1h1s1 1 2 1l2-2c0 2 0 2-1 3h0c0 1-1 2-2 3h-1l-1 1-1 1s-1 0-1-1v-2h-3l-1 4h-1c1-2 0-6-1-7l-2-1 2-1h5z" class="K"></path><path d="M503 570c0 2 0 2-1 3h0c0 1-1 2-2 3h-1l-1 1-1 1s-1 0-1-1v-2h-3l1-2h5l1 1 1-2 2-2z" class="j"></path><path d="M493 575h3v2c0 1 1 1 1 1 2 1 3 2 5 2l1 1c-1 1-1 1-2 0v1 2l-3 3c-1 0 0 0-1 1h-1c-1-2-1-4-2-5s-2-3-2-4l1-4z" class="T"></path><path d="M497 588l-1-1c0-2 1-3 2-4 1-2 1-1 3-1v2l-3 3c-1 0 0 0-1 1z" class="z"></path><path d="M488 555l3 1c1 0 1 0 1-1l4 3 1 1h1c1 2 4 4 6 5v4 6l-2 2-1 1-1-1c1-1 2-2 2-3h0c1-1 1-1 1-3l-1-2h1v-1c-2 0-2-1-4-2l-1-1c-1-1-2-2-4-2h0c-1-1-2-1-3-1l1-2c-1-1-3-2-4-3v-1z" class="g"></path><path d="M467 562h3c0 2 0 3 1 4 2 6 5 19 10 22h1c2 0 3-1 5-2 0-1 1-1 2-2 0 2-2 3-4 5h-5l-1-1c-1 0-2-1-3-1l-3-6-2 1c-1-2-1-3-2-5v-2l-1-6 1-1-2-6z" class="t"></path><path d="M469 568l4 13-2 1c-1-2-1-3-2-5v-2l-1-6 1-1z" class="m"></path><path d="M490 537c2 1 7 4 8 6l1 1c0 1 0 1 1 2l-1 2c-1 0-2-1-2-2-1 1-1 2-2 3-2 1-3 1-5 1v4c1 0 1 1 2 1 0 1 0 1-1 1l-3-1v1c1 1 3 2 4 3l-1 2h-6l-1 1c0 1-1 1-2 2h1v1h-3l-3 1h-1l2-2c3-3 3-7 6-10 1-2 3-3 3-5h0l3-2h0c1-1 1-1 2-1l1 1c1-1 2-1 3-1 0-1-1 0 1-1h0c-1-1-1-2-2-2l-1-1c-2-1-2-1-4-1v-1l-2-1 2-2z" class="K"></path><path d="M480 565l2-3v-1c1 0 3 0 5-1v-5h0 1v1c1 1 3 2 4 3l-1 2h-6l-1 1c0 1-1 1-2 2h1v1h-3z" class="q"></path><path d="M569 689c0-1 2-5 3-6v4l1 1-1 2-6 11c-1 3-3 6-5 8v1c-1 2-3 4-4 6-3 4-7 9-11 12-2 1-3 2-5 4h0l-6 5c-2 1-4 2-5 3-2-2-2-5-3-8s-2-6-4-9c-1-1-2-1-2-2l-1-4-1-3h-1c-1-3-1-6-3-9l-1 1c-1-2-2-6-2-8 1-2 0-4 0-6h1c1 3 2 7 3 10 1-2 1-5 3-7v1c2-1 3-3 5-4 1 0 1 0 2 1h2c1 1 2 3 3 5 2 1 4 3 5 4h2c1 0 1 0 2 1 0-1-1-2-1-3h-1c1-2-1-2-1-4h0l-3-3-1-1c0-1-2-2-2-3v-1c2 1 3 2 5 4l4 2c1 1 1 1 2 1l2 1h3 0c1 0 2 1 3 1 0 1 0 1 1 1 2-1 3-2 5-3l1-1h3l1-3 2-1 1 1c-1 3-3 5-5 7v1c1-1 2-1 3-2v-1c2-2 2-3 4-3 1-1 2-3 3-4z" class="R"></path><path d="M543 716c1 0 3 0 4-1l1 1c-2 1-3 2-5 2l-1 1h-2c1-1 2-2 2-3h1z" class="i"></path><path d="M557 709c1-1 2-1 3-2 0 0 0 1 1 2h0v1c-2 1-4 3-5 4 0-1 0-1-1-2-1 2-4 5-6 6 2-3 5-6 8-9z" class="Z"></path><path d="M532 715c1 1 4 1 6 0l3-1c3 0 6-2 9-3v1l-7 4h-1l-3 1c-3 1-6 1-8 0s-3-2-5-3c1 0 2 0 3 1h3z" class="m"></path><path d="M557 709c0-1 1-2 1-3h1c1-2 3-4 4-5 2-2 3-4 5-6v1c0 1-2 3-3 5h1c-1 3-3 6-5 8h0c-1-1-1-2-1-2-1 1-2 1-3 2z" class="c"></path><path d="M569 689c0 4-8 13-11 16-2 2-5 5-8 7v-1-1c-2 1-4 2-6 2l-1-1 2-1 3-3h0l6-3 5-5c1-1 2-1 3-2v-1c2-2 2-3 4-3 1-1 2-3 3-4z" class="k"></path><path d="M545 710l3-1h0 1l1 1c-2 1-4 2-6 2l-1-1 2-1z" class="m"></path><path d="M559 699c1-1 2-1 3-2v-1c2-2 2-3 4-3-1 3-6 10-9 10l-3 1 5-5z" class="w"></path><path d="M523 701c2 4 4 7 9 8 1 1 3 1 5 1l4-1 2-1c1 0 3-1 5-1l-3 3-2 1 1 1c2 0 4-1 6-2v1c-3 1-6 3-9 3l-3 1c-2 1-5 1-6 0 0-1-1-1-1-1-1-1-2-1-2-2h-2c-1-1-2-2-2-3l-1-1c0-1-1-3-1-4l-1-1 1-2z" class="Z"></path><path d="M541 709l2-1c1 0 3-1 5-1l-3 3-2 1 1 1-3 1c-2 1-2 1-4 1-1 0-1 0-2-1 1-1 1-1 2-3l4-1z" class="a"></path><path d="M528 693c1 1 2 3 3 5 2 1 4 3 5 4 2 2 3 3 5 4h1l1 1h0l-2 1v1l-4 1c-2 0-4 0-5-1-5-1-7-4-9-8h0c0-1-1-2 0-3 0-1 1-3 2-4h1v-1h2z" class="S"></path><path d="M528 693c1 1 2 3 3 5 2 1 4 3 5 4 2 2 3 3 5 4h1l1 1h0l-2 1h0c-6-2-13-9-16-14h1v-1h2z" class="R"></path><path d="M528 693c1 1 2 3 3 5h0c-2 0-4-2-5-4v-1h2z" class="i"></path><path d="M512 698c1-2 0-4 0-6h1c1 3 2 7 3 10 1-2 1-5 3-7v1c2-1 3-3 5-4 1 0 1 0 2 1v1h-1c-1 1-2 3-2 4-1 1 0 2 0 3h0l-1 2 1 1c0 1 1 3 1 4l1 1c0 1 1 2 2 3h2c0 1 1 1 2 2 0 0 1 0 1 1h-3c-1-1-2-1-3-1l-1 1c-2-1-2-3-3-3v3c1 1 3 2 2 4-1-1-1-2-2-3h-1c-1 0-2-1-2-2h-1c-1-3-1-6-3-9l-1 1c-1-2-2-6-2-8z" class="c"></path><path d="M512 698c1-2 0-4 0-6h1c1 3 2 7 3 10 1-2 1-5 3-7v1c-1 3-2 6-1 10v1c1 3 2 6 4 9h-1c-1 0-2-1-2-2h-1c-1-3-1-6-3-9l-1 1c-1-2-2-6-2-8z" class="s"></path><path d="M531 688c2 1 3 2 5 4l4 2c1 1 1 1 2 1l2 1h3 0c1 0 2 1 3 1 0 1 0 1 1 1 2-1 3-2 5-3l1-1h3l1-3 2-1 1 1c-1 3-3 5-5 7v1l-5 5-6 3h0c-2 0-4 1-5 1l-2 1v-1l2-1h0l-1-1h-1c-2-1-3-2-5-4h2c1 0 1 0 2 1 0-1-1-2-1-3h-1c1-2-1-2-1-4h0l-3-3-1-1c0-1-2-2-2-3v-1z" class="N"></path><path d="M537 696l7 5c1 0 2 2 3 2l2-1h1v1c-1 1-3 3-4 3-2 0-2 0-3 1l-1-1h-1c-2-1-3-2-5-4h2c1 0 1 0 2 1 0-1-1-2-1-3h-1c1-2-1-2-1-4z" class="K"></path><path d="M561 691l2-1 1 1c-1 3-3 5-5 7v1l-5 5-6 3h0c-2 0-4 1-5 1l-2 1v-1l2-1h0c1-1 1-1 3-1 1 0 3-2 4-3v-1l2-2 4-5 1-1h3l1-3z" class="C"></path><path d="M557 694h3c-2 4-6 4-7 8h-1c-1 0-1 1-2 1v-1l2-2 4-5 1-1z" class="AC"></path><path d="M556 714c1-1 3-3 5-4-1 2-3 4-4 6-3 4-7 9-11 12-2 1-3 2-5 4h0l-6 5c-1-2-2-4-2-6 0-1-1-3-2-3 0-1-1-1-1-1-1 0 0-1-2-2v-1c0-2-1-1-1-3h1c2 1 5 3 8 3v-1c3 0 5-1 7-2 1-1 2-1 3-2s2-1 3-1c2-1 5-4 6-6 1 1 1 1 1 2z" class="c"></path><path d="M541 732c0-4 5-8 8-11-1 3-2 4-4 6l1 1c-2 1-3 2-5 4z" class="Z"></path><path d="M556 714c1-1 3-3 5-4-1 2-3 4-4 6-3 4-7 9-11 12l-1-1c2-2 3-3 4-6 0 0 2-1 2-2l5-5z" class="W"></path><path d="M523 597l1-1c1 1 2 1 3 2 1 0 3 2 4 3l9 9c1 1 4 5 5 6v-2l1 1h0c1 3 4 7 5 10l1 1c1 1 2 3 2 5l4 9 3 6c-4-1-4-4-6-6-1-1-2-2-3-4-3-1-5-2-7-3l-3-1h-6v-1h-2-2-1c-2 2-5 4-7 7l2 3-1 2-3-1h0l-1 3h0l-1 2v3l-1 5v-4h0l-2 1c-1-1-1-1-1-2h-1l-1 1v-1h-1c0 1 0 2-1 4l-1-7h-1v3c-1 2 0 4-1 5v-2l-1-4v2c0-1 0-1-1-1 0-1-1-1-1-2 0-2 0-2-1-4v-1c-1-2-1-6-2-9v-1c0-1 0-1-1-2 0-3 0-6 1-8 1-4 2-6 5-9 1 0 2-3 4-3 4-2 5-5 7-9h1c1-2 1-4 2-5h1z" class="a"></path><path d="M518 617l2-1c3-2 5-2 8-2l2 1c-1 1-1 1-2 1l1 1 1-1c-1 2-1 2-2 3l-4 1-3 1v-1c1-1 2-2 4-2l-1-1h-1-2c-1 0-1 1-2 1h-1v-1z" class="W"></path><path d="M510 637l1-1v2l1 1 1 1 1-5h1c-1 3-1 6-1 9h1v6l-1 1v-1h-1c0 1 0 2-1 4l-1-7-1-5c1-1 0-4 0-5z" class="Z"></path><path d="M509 626c2-4 5-8 9-9v1c-4 3-6 6-7 11l-1 8c0 1 1 4 0 5l-1 1v1h-1c0-4-2-15 1-18z" class="w"></path><path d="M504 629l1-1c1-2 2-4 4-5v3c-3 3-1 14-1 18h1v-1l1-1 1 5h-1v3c-1 2 0 4-1 5v-2l-1-4v2c0-1 0-1-1-1 0-1-1-1-1-2 0-2 0-2-1-4v-1c-1-2-1-6-2-9h0c1-2 1-3 1-5z" class="l"></path><path d="M528 619c2 0 4 0 5 1 0 1-2 1-3 1 2 1 3 1 4 1h0c-1 1-2 1-3 1s-2 1-4 2c-1 0-1 0-2 1-3 1-6 6-8 8l1 1h0c-2 1-2 2-2 3-1 2-1 4-1 6h-1c0-3 0-6 1-9v-2c1-3 1-5 3-7 0-1 1-1 2-3 1-1 1-1 1-2l3-1 4-1z" class="k"></path><path d="M534 622c2 0 2 0 4 1v1h-1c-1 0-2 0-4 1h-1c-1 0-2 1-3 1 2 0 4 0 5 1v1l-2 1c-1 0-2 0-2 1h0l1 1c-2 2-5 4-7 7l2 3-1 2-3-1h0l-1 3h0l-1 2v3l-1 5v-4h0l-2 1c-1-1-1-1-1-2h-1v-6c0-2 0-4 1-6 0-1 0-2 2-3h0l-1-1c2-2 5-7 8-8 1-1 1-1 2-1 2-1 3-2 4-2s2 0 3-1h0z" class="Z"></path><path d="M515 644v1c1 0 1 1 1 2h0v-2c0 1 1 2 1 3 1-1 1-2 2-3h2l-1 2v3l-1 5v-4h0l-2 1c-1-1-1-1-1-2h-1v-6z" class="R"></path><path d="M522 642l-1-2-1 1-1 2h-1c-1-3 1-7 2-9v-3c2-2 3-3 5-3l-5 6c1 1 3 2 3 4h1l2 3-1 2-3-1h0z" class="k"></path><path d="M524 638l2 3-1 2-3-1c0-1 1-3 1-4h1z" class="AH"></path><path d="M529 626c2 0 4 0 5 1v1l-2 1c-1 0-2 0-2 1h0l1 1c-2 2-5 4-7 7h-1c0-2-2-3-3-4l5-6c0-1 2-2 2-2h2z" class="t"></path><path d="M520 602v3l1-3 2 3h1c1 0 2 1 3 2 2 1 3 3 5 5l1 1-5-2-1 3h0 1c-3 0-5 0-8 2l-2 1c-4 1-7 5-9 9v-3c-2 1-3 3-4 5l-1 1c0 2 0 3-1 5h0v-1c0-1 0-1-1-2 0-3 0-6 1-8 1-4 2-6 5-9 1 0 2-3 4-3 4-2 5-5 7-9h1z" class="U"></path><path d="M508 620l3-1c0 1-1 3-2 4-2 1-3 3-4 5l-1 1c0-5 1-6 4-9z" class="q"></path><path d="M527 607c2 1 3 3 5 5l1 1-5-2-1 3h0 1c-3 0-5 0-8 2l-2 1c-4 1-7 5-9 9v-3c1-1 2-3 2-4l-3 1 2-3 4-4c1 0 2-1 3-1 1-1 2-1 2-2l1-2c2-1 5-1 7-1z" class="N"></path><path d="M510 617c1 0 2 0 3-1l1-1 7-5c3 0 4-1 6 1-2 0-3 1-5 2-4 2-8 3-11 6l-3 1 2-3z" class="K"></path><path d="M511 619c3-3 7-4 11-6 2-1 3-2 5-2h1l-1 3h0 1c-3 0-5 0-8 2l-2 1c-4 1-7 5-9 9v-3c1-1 2-3 2-4z" class="AC"></path><path d="M523 597l1-1c1 1 2 1 3 2 1 0 3 2 4 3l9 9c1 1 4 5 5 6v-2l1 1h0c1 3 4 7 5 10l1 1c1 1 2 3 2 5l4 9 3 6c-4-1-4-4-6-6-1-1-2-2-3-4-3-1-5-2-7-3l-3-1h-6v-1h-2-2-1l-1-1h0c0-1 1-1 2-1l2-1v-1c-1-1-3-1-5-1 1 0 2-1 3-1h1c2-1 3-1 4-1h1v-1c-2-1-2-1-4-1-1 0-2 0-4-1 1 0 3 0 3-1-1-1-3-1-5-1 1-1 1-1 2-3l-1 1-1-1c1 0 1 0 2-1l-2-1h-1 0l1-3 5 2-1-1c-2-2-3-4-5-5-1-1-2-2-3-2h-1l-2-3-1 3v-3c1-2 1-4 2-5h1z" class="w"></path><path d="M546 623l6 10c2 3 3 6 6 7l3 6c-4-1-4-4-6-6-1-1-2-2-3-4s-4-5-5-7l-4-4c1 0 2 1 3 1v-3z" class="C"></path><path d="M533 608c5 4 9 9 13 15v3c-1 0-2-1-3-1l-4-4c-2-3-3-5-6-8l-1-1 2 1 1-1-2-4z" class="T"></path><path d="M523 597l1-1c1 1 2 1 3 2 1 0 3 2 4 3 0 1 0 2-1 4l3 3 2 4-1 1-2-1c-2-2-3-4-5-5-1-1-2-2-3-2h-1l-2-3-1 3v-3c1-2 1-4 2-5h1z" class="AG"></path><path d="M523 597l1-1c1 1 2 1 3 2 1 0 3 2 4 3 0 1 0 2-1 4-2-3-5-5-7-8z" class="J"></path><path d="M528 611l5 2c3 3 4 5 6 8l4 4 4 4c1 2 4 5 5 7-3-1-5-2-7-3l-3-1h-6v-1h-2-2-1l-1-1h0c0-1 1-1 2-1l2-1v-1c-1-1-3-1-5-1 1 0 2-1 3-1h1c2-1 3-1 4-1h1v-1c-2-1-2-1-4-1-1 0-2 0-4-1 1 0 3 0 3-1-1-1-3-1-5-1 1-1 1-1 2-3l-1 1-1-1c1 0 1 0 2-1l-2-1h-1 0l1-3z" class="i"></path><path d="M528 611l5 2c3 3 4 5 6 8-3-1-5-3-9-5l-1 1-1-1c1 0 1 0 2-1l-2-1h-1 0l1-3z" class="C"></path><path d="M533 625c3 0 5 1 8 2l3 3h0c1 1 1 2 1 2v1l-3-1h-6v-1h-2-2-1l-1-1h0c0-1 1-1 2-1l2-1v-1c-1-1-3-1-5-1 1 0 2-1 3-1h1z" class="k"></path><path d="M541 627l3 3h0c1 1 1 2 1 2v1l-3-1-1-1h-1v-2c-1 0-2 0-3-1 1-1 2-1 4-1z" class="W"></path><path d="M531 631h1 2 2v1h6l3 1c2 1 4 2 7 3 1 2 2 3 3 4 2 2 2 5 6 6v1l-2 1c-2 1-4 2-6 2-1 0-2 0-2 1 1 1 2 1 3 1l-1 2c0 1 0 2 1 3v1l-3 8 1 1c2 0 3-1 4-3 1 1 2 2 2 4h0v2 1c1 1 1 1 1 2 1 1 1 2 1 2v1c0 2 1 4 2 6 0 2-1 4-1 6-1 2-3 4-4 6l-1 1c-2 1-3 2-5 3-1 0-1 0-1-1-1 0-2-1-3-1h0-3l-2-1c-1 0-1 0-2-1l-4-2c-2-2-3-3-5-4-1-2-3-4-3-5-3-4-6-10-7-15v-2-1c-1-3-1-7-2-10l1-5v-3l1-2h0l1-3h0l3 1 1-2-2-3c2-3 5-5 7-7z" class="AL"></path><path d="M529 656c1 2 3 4 3 6l-1-1c-2 2-2 3-3 5-1-3 0-7 1-10z" class="AK"></path><path d="M547 687c1 1 1 2 3 2 1 1 1 1 1 3l-2 2c-1 0-1 0-3-1l2-3c0-1-1-2-1-2v-1z" class="T"></path><path d="M522 642h0l3 1c-1 2-1 4-2 6-1 0-2-1-3-2l1-2h0l1-3z" class="t"></path><path d="M546 682l1 1c-1 1-1 2-2 2l2 2h0v1s1 1 1 2l-2 3-2-2c1-1 0-6 0-7h1l1-2z" class="N"></path><path d="M538 645c3-2 4-2 6-2 1 1 1 1 2 1l1 1c2 0 3 1 5 2h0l1-1c1 0 4 1 5 1l1 1c-2 1-4 2-6 2l-2-2c-1 0-1 0-2-1s-2-2-4-2-4-1-7 0z" class="T"></path><path d="M541 681c1 0 0 0 1-1l2 2c0-2 0-2 1-3l1 1h0l-1 1 1 1-1 2h-1c0 1 1 6 0 7-2-1-3-2-5-3 1-2 1-3 3-5l-1-2z" class="C"></path><path d="M531 631h1 2 2v1c-5 2-7 4-10 9l-2-3c2-3 5-5 7-7z" class="AT"></path><path d="M532 673c1 3 2 6 5 7h1c1 0 1 1 2 2h1v-1l1 2c-2 2-2 3-3 5-3-4-6-7-8-11 0-1 0-3 1-4z" class="AC"></path><path d="M528 666c1-2 1-3 3-5l1 1c0 1 1 2 1 3l2 2-1 1 1 2h0l-1-1c-1 1-1 0-2 1 0 1 0 1-1 2l1 1c-1 1-1 3-1 4-2-3-3-7-3-11z" class="g"></path><path d="M520 647c1 1 2 2 3 2v2 6c0 4 1 9 2 14v1c-1-2-3-3-4-4v-2-1c-1-3-1-7-2-10l1-5v-3z" class="Z"></path><path d="M520 654l3-3v6h-2-1v-3z" class="a"></path><path d="M520 647c1 1 2 2 3 2v2l-3 3v-4-3z" class="AM"></path><path d="M521 668c1 1 3 2 4 4v-1c5 11 11 19 22 25h-3l-2-1c-1 0-1 0-2-1l-4-2c-2-2-3-3-5-4-1-2-3-4-3-5-3-4-6-10-7-15z" class="C"></path><path d="M546 661l2 2c1 2 1 3 3 4 0 1 1 2 1 3v1s1 1 2 1c-1 1-2 0-2 2 0 3 0 4-1 7v1 1l-2-2v1c1 2 4 4 4 7h0c-1-1-1-1-1-2-2-1-4-3-5-5l-1-2h0l-1-1c-1-2-4-5-3-7 1 0 1 0 2 1 1 0 2 0 3-1 0-2 0-2-1-4l1-1v1h1c0-3-2-5-3-7h1z" class="T"></path><path d="M542 672c1 0 1 0 2 1 1 0 2 0 3-1 0-2 0-2-1-4l1-1v1h1v1 1 1 1c0 2-1 4-1 5-2 0-5-3-5-5z" class="g"></path><path d="M536 663l3 5 1 2 2 2c-1 2 2 5 3 7-1 1-1 1-1 3l-2-2c-1 1 0 1-1 1v1h-1c-1-1-1-2-2-2h-1c-3-1-4-4-5-7l-1-1c1-1 1-1 1-2 1-1 1 0 2-1l1 1h0l-1-2 1-1-2-2 1-1c1 2 2 4 4 5h0c-1-2-2-3-2-6z" class="R"></path><path d="M536 663l3 5 1 2 2 2c-1 2 2 5 3 7-1 1-1 1-1 3l-2-2c-1 1 0 1-1 1v1l-3-6c1 0 2 1 2 2 1 1 1 0 2 1-1-3-3-4-4-6l-3-6-2-2 1-1c1 2 2 4 4 5h0c-1-2-2-3-2-6z" class="AK"></path><path d="M538 645c3-1 5 0 7 0s3 1 4 2 1 1 2 1l2 2c-1 0-2 0-2 1 1 1 2 1 3 1l-1 2c0 1 0 2 1 3v1l-3 8v1c-2-1-2-2-3-4l-2-2c-2-4-5-7-8-10l-2-2-1-2 3-2z" class="C"></path><path d="M538 651l1-2c3 1 9 7 10 10v2 1c-1 1-1 0-1 1l-2-2c-2-4-5-7-8-10z" class="i"></path><path d="M538 645c3-1 5 0 7 0s3 1 4 2 1 1 2 1l2 2c-1 0-2 0-2 1 1 1 2 1 3 1l-1 2c0 1 0 2 1 3v1c-2-1-3 0-4-1l1-2-2-2c-3-1-4-5-7-6-2 0-4 1-6 2l-1-2 3-2z" class="K"></path><path d="M531 651v-2c2-1 3-1 4-2l1 2 2 2c3 3 6 6 8 10h-1c1 2 3 4 3 7h-1v-1l-1 1c1 2 1 2 1 4-1 1-2 1-3 1-1-1-1-1-2-1l-2-2-1-2-3-5c0 3 1 4 2 6h0c-2-1-3-3-4-5l-1 1c0-1-1-2-1-3 0-2-2-4-3-6 0-1 1-4 2-5z" class="C"></path><path d="M531 651c0 1 1 1 2 1v5c-1 0-1-1-1-2l-1 1 3 8-1 1c0-1-1-2-1-3 0-2-2-4-3-6 0-1 1-4 2-5z" class="K"></path><path d="M531 651v-2c2-1 3-1 4-2l1 2 2 2c3 3 6 6 8 10h-1c-2-2-4-5-6-8-1-1-3-3-5-3l-1 2c-1 0-2 0-2-1z" class="j"></path><path d="M536 660l-1-1 1-1 1 1v-1c0-1-1-2-1-2l1-1 1 1v4c2 2 4 3 5 5 0 2 0 4 2 5l-1 1c-2-1-2-1-4-1l-1-2-3-5v-3z" class="R"></path><path d="M536 660c2 2 3 4 4 6 0 1 0 1-1 2l-3-5v-3z" class="i"></path></svg>