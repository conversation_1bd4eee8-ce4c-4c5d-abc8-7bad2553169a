<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="180 114 686 812"><!--oldViewBox="0 0 1021 1024"--><style>.B{fill:#c4c2bf}.C{fill:#d8d5d2}.D{fill:#bfbcb9}.E{fill:#dddbd8}.F{fill:#e2e0de}.G{fill:#aca8a6}.H{fill:#e9e7e5}.I{fill:#d1cecc}.J{fill:#f1efee}.K{fill:#b2afab}.L{fill:#96938f}.M{fill:#2c2a29}.N{fill:#817e7b}.O{fill:#a4a29e}.P{fill:#b9b6b3}.Q{fill:#cbc8c4}.R{fill:#9d9a95}.S{fill:#333230}.T{fill:#797774}.U{fill:#666460}.V{fill:#ccc9c8}.W{fill:#1b1918}.X{fill:#21201f}.Y{fill:#6e6c69}.Z{fill:#908d8a}.a{fill:#8b8884}.b{fill:#fff}.c{fill:#575653}.d{fill:#484644}.e{fill:#252422}.f{fill:#0e0d0d}.g{fill:#383635}.h{fill:#a4a19b}.i{fill:#52504e}.j{fill:#b5b3b1}.k{fill:#5e5c59}.l{fill:#3d3c3a}.m{fill:#151413}.n{fill:#42413e}.o{fill:#ecebe9}.p{fill:#4d4c48}.q{fill:#86847f}.r{fill:#74716e}.s{fill:#f8f7f6}.t{fill:#3b3935}.u{fill:#010101}</style><path d="M227 804c2 2 2 1 4 1l1 1-2 2-1 1-2-5z" class="e"></path><path d="M817 707v-1l1 1c1 2 1 4 0 7l-2-1v-2l1-4z" class="W"></path><path d="M819 700c0 2 0 4-1 7l-1-1v1h-1c1-2 1-3 0-5v-1l3-1z" class="l"></path><path d="M630 592c2 1 3 1 5 1v1 1c1 1 1 2 1 3l-7-5 1-1z" class="f"></path><path d="M284 621h1c1 0 2-1 3-1h1l-1 2c-1 2-1 3-2 5v-1l-1-1-1 1h-1l2-3h0l-1-2z" class="F"></path><path d="M814 693c2 2 4 3 5 6v1l-3 1v-4c-2-1-2-2-4-2l1-1h1v-1zm0 30c1 2 3 5 3 7l-3 1c0-2-1-3-2-4 1-1 2-2 2-4z" class="d"></path><path d="M817 730c0 3-1 5-2 7h-1-2l-1-2 3-4 3-1z" class="l"></path><path d="M635 593h5 1c-1 1-2 1-3 2v1c0 1 0 1 1 2v1c0 1-1 3-2 3 0-1-1-2-1-4 0-1 0-2-1-3v-1-1z" class="u"></path><path d="M309 597h2c1 0 1 0 1 2-1 1-3 2-5 3h-3v-1c2-2 3-3 5-4z" class="H"></path><path d="M810 747c1 2 2 6 0 8-1 2-3 4-4 5v-2h1c1-2 1-4 1-6v-3l2-2z" class="S"></path><path d="M801 764c0-1 0-2 1-3 1-2 2-2 4-3v2l-1 1 1 3c0 1 1 3 0 4h-2c-1-1 1-1 0-3-1 0-2-1-3-1z" class="M"></path><path d="M812 727c1 1 2 2 2 4l-3 4 1 2c-1 0-1 1-2 1v-1l1-1c-1-1-1-1-1-3 0 0-1-1-2-1l-1-2h0 2l3-3z" class="T"></path><path d="M316 512c1-1 2-1 3-1l1 2c1 1 1 2 1 3-1 1-2 1-3 2h-1c-2 0-2-1-3-2 0-2 1-3 2-4z" class="J"></path><path d="M312 619h2c1 1 1 1 2 3-2 2-3 3-5 4-1 0-1 0-2-1h-1-1v-1-1c2 0 4-2 5-4z" class="s"></path><path d="M299 625c1-1 1-2 4-2 1 0 2 0 3 1-1 2-3 3-4 5 0 1-1 2-2 2h-1v-1-1l-1-1c-1-1 0-2 1-3z" class="F"></path><path d="M284 621l1 2h0l-2 3h1l1-1 1 1v1l-1 9c-1-1-1-2-2-4h0l-2-2-1-1c1-4 2-5 4-8z" class="E"></path><path d="M798 760c1-1 3-2 4-4 1-1 2-3 4-4v1l2-1c0 2 0 4-1 6h-1c-2 1-3 1-4 3-1 1-1 2-1 3l-2-2 1-2h-1-1z" class="i"></path><path d="M688 899c3 0 8 0 10 1h0c-2 0-3 0-4 1h-1v1c-2 0-5-1-6 0-1 0-1 0-2 1h-3c-1 0-2 0-3 1h-8c1-1 3-1 5-2 1-1 4 0 5 0h1c0-1 1-1 1-1l-1-1 6-1z" class="C"></path><path d="M816 711v2l2 1c-2 2-3 6-4 9 0 2-1 3-2 4l-3 3h-2v-1c1-1 2-2 2-3l1-1 2-5c1-3 3-6 4-9z" class="e"></path><path d="M800 753l3-5 3-3 2 4v3l-2 1v-1c-2 1-3 3-4 4-1 2-3 3-4 4l-1-1 1-1c0-1 1-2 2-2l1-1c0-1 0-1-1-2z" class="N"></path><path d="M272 482h1c2 2 4 0 6 2l-1 2h0v1l-1 1 1 1v1c-1 0-2-1-2-1v2c0 2 0 3-1 4-1-1-1-3-1-5l-1-1-1 1v-1h-1v-1-3c0-1 1-2 1-3z" class="o"></path><path d="M277 865c3 1 5 2 7 1 2 2 4 4 5 6s2 3 3 4l3 1c-3 0-5-1-8-1h0c-1 0-1 0-2-1h1v-1h-2-1 0-4c-2-1-3-1-4 0-2-1-3-1-5-1-3 0-10 1-13 0l18-1c4 1 8 1 13 1l-1-1c-2-2-5-1-8-1l-12-1h0 19 0c-1-2-2-2-3-3h-4l-2-2z" class="I"></path><path d="M796 785c2 0 3 0 4-1v-1l1 1c-2 4-7 7-11 8-2 1-7 1-8 3l-1 2c-1-1-2-1-2-3 1-2 2-2 4-3h0c4 0 10-3 13-6z" class="e"></path><path d="M812 737h2l-1 4c-1 1-2 2-3 4v2l-2 2-2-4-3 3-3 5c-2 2-4 5-7 6l-1-1 2-1c2-1 4-2 4-4 1-1 1-2 2-2l1-2c1-1 1-2 2-3s1-1 1-2h0c3-2 5-4 6-6 1 0 1-1 2-1z" class="p"></path><path d="M806 745c1-1 2-2 3-2l1 2v2l-2 2-2-4z" class="M"></path><path d="M704 418l2 14h2c0 1-1 1 0 3l3 2h-1l-1 1c-1 0-2 0-3 1v3h0c-1 0-1 0-1-1-1-2 0-3 1-4 0-2 0-2-1-3-1 0-1 0-2 1v1 2l1 1-1 2h0l-1-1v-3c-1-1-2 0-3-1 1-2 1-1 3-2v-2c1-4 1-10 2-14z" class="G"></path><path d="M249 827h1c1 3 2 6 4 9 0 1 0 1 1 2s2 1 3 2c2 2 3 3 6 4 2 1 2 1 3 3v1c1 2 2 3 3 5 1 0 1 0 2 1 0 1 0 2-1 3-3-2-5-5-6-9v-2c-1-1-6-1-8-3-4-3-7-11-8-16z" class="S"></path><path d="M770 475v1l1 1v3 1 3h1c0-1 0-2 1-2h0v1 3h2s1 0 1 1h0l-2 1v1l1 1h0c-1 0-2 1-2 1-2 1-1 6-2 7l-1-1 1-1v-2-1l-1-1c-1 0-1 0-2 1h0v-3h-1-1 0c1-1 1-1 2-1l-3-2 2-1 1 1c0-2 0-3 1-4h0v-1c1-2 1-5 1-7z" class="E"></path><path d="M270 655c1 3 3 5 4 7 0 2 0 3 1 5l-1 1c0 1 0 1 1 1 1 2 2 3 3 5l1 1h1l1-1c1 1 2 1 3 2-1 2-1 3 0 5-1 1-1 2-2 2v1c-7-8-12-19-12-29z" class="B"></path><path d="M276 623c1-2 2-4 3-5 4-5 10-12 15-16 1-1 2-1 4-1h0c0 2-1 2-3 3-3 1-6 5-8 8-3 3-5 4-7 8v3h0c0 1 0 2-1 3v2 4l-1 1c0 1 1 2 0 3v1c1-1 1-2 1-3v-1l1 1c0 1-1 4-2 5l-1-1c-1-4 0-8 1-12 0-1-1-2-1-2l-1-1z" class="s"></path><path d="M276 623l1 1s1 1 1 2c-1 4-2 8-1 12l1 1c1-1 2-4 2-5l1-4 2 2h0c-2 2-1 2-2 4 1 1 2 0 2 2-1 1-2 2-4 3v-2l-1 1-1 1h0c-1-2-1-2-3-3l-1 1-1-1-1-3 5-12z" class="D"></path><path d="M279 641c2-1 3-2 4-3 0-2-1-1-2-2 1-2 0-2 2-4 1 2 1 3 2 4 1 2 1 4 4 6h1 1c-2 1-2 1-3 2 0 0 0 1 1 2h-1-2c0 1-1 1-1 1-2-1-2-2-2-4l-1-1-1 1v4c1 2 2 4 3 5-1 1-1 1-3 1-1-1-1-3-1-5v-3h-4l1-4 1-1 1-1v2z" class="C"></path><path d="M283 643l2 1 1-1h1v1c-1 1-1 1-1 2s-1 1-1 1c-2-1-2-2-2-4z" class="F"></path><path d="M277 641l1-1 1-1v2l1 4h-4l1-4z" class="K"></path><path d="M302 660c1 1 1 0 1 1 2 1 6 3 7 5l-1 2v3h1v3c-3 0-5 0-8 1l-1 1-1-1v-3l2-1v-2l-1-4c0-1 0-3 1-5z" class="J"></path><path d="M303 661c2 1 6 3 7 5l-1 2c-1 0-1 0-3-1 0-1-1-2-1-3-1-1-2-2-2-3z" class="C"></path><path d="M276 385h1v2h1c1 0 2 0 3 1s4 0 6 0c1 1 4 0 6 1l-1 1v1 1h0c-1 1-4 1-5 1-2 1-4 0-6 0v2h1l-1 1h0c-1 0-1-1-2-2v-1h-1l1 2h0v3c-2 0-1-3-2-4l-1 1-1-1s0-1-1-2v-1c0-2 0-2 2-3v-3z" class="J"></path><path d="M637 658l1 1h1v1c1 1 1 2 3 3v3 2c0 2 0 4-1 6l1 5c-2 0-2 1-4-1-1-2-2-2-3-2 1-1 1-3 1-5v-12c0 1 0 1 1 1v-2z" class="O"></path><path d="M638 673c0-2 1-3 2-4v-1h2c0 2 0 4-1 6l-3 1h0v-2z" class="H"></path><path d="M637 658l1 1v14 2h0l3-1 1 5c-2 0-2 1-4-1-1-2-2-2-3-2 1-1 1-3 1-5v-12c0 1 0 1 1 1v-2z" class="c"></path><path d="M381 643h1c0 2 0 3 1 4v9c1 1 0 4 0 6h2v1c3 1-1-2 2 0l-1 1h-2v1c1 0 1 0 2 1h-1v1h-1-2-1c1 1 1 1 1 2l-2-1-1 1c-1-1-1-1-1-2-1 0-1 0-2-1l2-1c-1-1-1-2-1-3l-1-1h1 1 1v-1c1-2 0-3 0-4s1-1 1-2c0-2-1-4 0-6 0 0 1-3 1-4v-1z" class="H"></path><path d="M287 876c3 0 5 1 8 1h0 5c1-1 2-1 3-1 0 0 1 1 2 1 1 1 3 2 5 3 3 1 6 1 9 2 4 1 8 3 12 4h0 1v6l-1-1v-4h-1v-1h-3 0l-2-1c-2 0-3-1-4-2h-1-2c-1-1-1-1-2-1h-3c-1-1-2-1-3-1s-1 0-2-1c-3-2-9 1-13-1h-5-9c-4-1-10-1-14-1h-15-5 13c1-1 2-1 4-1h12c1 1 4 1 6 1 2 1 7 0 10 0h-1 0l-1-1c-1 0-2 0-3-1z" class="E"></path><path d="M295 622s1 0 1-1v-1c-1-1-1-2-1-4 1-2 3-6 6-7 2-1 4-3 7-3 1 1 2 1 2 2h-2l-1-1c-2 1-4 2-5 4l-2 5c-1 3 1 4 3 7-3 0-3 1-4 2s-2 2-1 3l1 1v1c-2 0-3 0-5-1-1-1-2-2-1-3 0-2 0-3 2-4z" class="H"></path><path d="M295 622v5 1 1h-1c-1-1-2-2-1-3 0-2 0-3 2-4z" class="D"></path><path d="M299 625v-5c1-2-1-1-1-2l2-2c-1 3 1 4 3 7-3 0-3 1-4 2z" class="J"></path><path d="M630 911l4-1c1 0 2-1 2-1l5-1v1c4 0 8 1 12 0h5 4c9-1 18 0 27 1h0c-3 1-9 1-12 1-6 0-11-1-16 0h-4-13c-1 0-3 0-4 1h-4-1c1 2 1 2 3 1h1l1 1s-1 1-2 0h-1v1l-1-1h-1l-1 1v-1c-1 0-2 1-2 1l-1-1c-1 0-1 1-2 0-2 0-9 0-10-1h-5c4-2 11 0 16-1h1 2l1-1h6-10z" class="C"></path><path d="M630 911l4-1c1 0 2-1 2-1l5-1v1c4 0 8 1 12 0h5 4v1l-22 1h-10z" class="B"></path><path d="M633 676h2c1 0 2 0 3 2 2 2 2 1 4 1v3c-1 1-3 1-2 3l1 1v2c-3 0-5 1-8 2-1 1-3 1-4 3v1l-1 1h0c-2-3 0-11 1-14 0-3 2-3 4-5z" class="c"></path><path d="M634 677h1v3c-2 1-3 1-5 2v-1c1-2 2-3 4-4z" class="J"></path><path d="M629 681c0 1 1 1 1 2-1 2-1 2-1 4h1l1 1-1 1h1l1-1v-2l1 1v1 2c-1 1-3 1-4 3v1l-1 1h0c-2-3 0-11 1-14z" class="X"></path><path d="M633 676h2c1 0 2 0 3 2 2 2 2 1 4 1v3c-1 1-3 1-2 3l1 1v2c-3 0-5 1-8 2v-2l1 1c1-1 2-2 2-3 1-1 1-3 1-4l-2-2v-3h-1l-1-1z" class="l"></path><path d="M346 586h1c2 0 5 0 7 1 7 1 16 4 22 8 7 5 14 11 19 18 1 2 3 4 3 6 0 0-5-6-7-7-2-2-6-4-8-7-3-1-4-4-7-5-2-2-5-3-7-4h-2v-1l-6-2c-2-1-4-1-5-2h-1-10c-3-1-5-1-8-1s-5 1-7 2l-1-1c1 0 1-1 2-1h1c1-1 2-1 3-1h0 5 5 0v1h9l2 1h2 1c1 1 2 1 3 2l4 1c2 0 4 1 6 2h1l1 1 2 1 1 1h1 2l-1-1c-1-1-2-1-4-2l-2-2h0-1c-1-1-3-2-5-2 0-1 0 0-1-1-2-1-4-1-6-2s-3-1-4-1h-2c-1-1-2-1-3-1h-1c-1 0-2 0-3-1h-1z" class="H"></path><path d="M669 900c6 0 13-1 19-1l-6 1 1 1s-1 0-1 1h-1c-1 0-4-1-5 0-2 1-4 1-5 2h-1c0 1 1 1 2 1h9c-2 1-5 0-7 1l-11 1c-3 0-6 0-8 1h-9c2 1 7 1 9 0 1 0 2 1 3 1h-5c-4 1-8 0-12 0v-1l-5 1s-1 1-2 1l-4 1h-2l1-1 2-1c3-1 6-1 9-3 3 0 7-1 10-2 2 0 3-1 4-2 4-1 11-1 15-2z" class="I"></path><path d="M640 906c3 0 7-1 10-2 2 0 3-1 4-2 4-1 11-1 15-2-2 1-3 2-5 2l-1 1h0l-22 5-5 1s-1 1-2 1l-4 1h-2l1-1 2-1c3-1 6-1 9-3z" class="P"></path><path d="M745 415c1-1 1-1 2 0h0c-2 1-3 1-5 2-1 0-1 1-2 1 0 1-1 2-2 2l-1 1c-2 1-6 6-7 9v1 5h0v2l-1 2c0 1 0 2-1 4 1 0 1 1 1 2h0c1 1 2 2 4 2 2 2 4 0 6 2h0-1c-2 1-3 1-4 1-2 0-3 0-4 1h-3v3l-1-1v-1c-1-1-1-2-2-3v-1h-1v-1c-1-2 0-4 0-5v-3l1-1v-2l1-4v-2l1-1v-1l1-2c1-2 5-6 7-7l1-1c1-1 2-2 4-2l2-1 2-1h2z" class="J"></path><path d="M496 917c7-1 15-1 23-1 2 0 3 1 4 1 2-1 3-1 4 0h9c3-1 6 0 8 0h11 2c3 1 8 1 11 1h1 29 7s1 0 1-1c2 1 6 1 8 1h22 0c-6 1-12 0-18 0l-34 1c-4 0-10-1-13 0-5 1-9 1-14 1h-24-8v-1h-2v1c-1 0-1 0-2-1h0-1c-1-1-9-1-11-1-1-1-7 0-9 0v-1h-4z" class="o"></path><path d="M271 635l1 3 1 1 1-1c2 1 2 1 3 3h0l-1 4h4v3c0 2 0 4 1 5 2 0 2 0 3-1h0c1 2 1 2 0 4 1 1 1 2 2 2h1l1 3h-1 0c0 2 2 2 3 3l-1 1h-3l-2 1-3-3-1 1h0c-1-2-2-3-3-4-1-2-3-3-4-5l-2-2h-1 0v2c-1-7-1-13 1-20z" class="P"></path><path d="M276 645h4v3h-1c-2-1-3-1-4-2l1-1z" class="B"></path><path d="M279 656c2 0 3 1 4 2v1c1 1 1 2 2 3h-1-2c-2 0-3-2-4-4l1-2z" class="G"></path><path d="M273 639l1-1c2 1 2 1 3 3h0l-1 4-1 1h-1-1c-1-1-1-3-1-5l1-2z" class="J"></path><defs><linearGradient id="A" x1="274.408" y1="647.323" x2="267.989" y2="640.222" xlink:href="#B"><stop offset="0" stop-color="#9f9a94"></stop><stop offset="1" stop-color="#bdb9b2"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M271 635l1 3 1 1-1 2c0 1-1 2 0 3 0 1 0 2-1 3l2 1v2l2 1v2l-1 1-2-3h-1v2h-1 0v2c-1-7-1-13 1-20z"></path><path d="M298 674l2-2v3l1 1c1 1 3 2 4 3 0 2 1 2 2 3 0 1-1 2-1 3s1 1 0 2-3 1-4 1l-2 1-3 2c-1 1-2 1-2 2h-1c-5-3-8-5-12-9v-1c1 0 1-1 2-2-1-2-1-3 0-5 2 1 5 2 8 3 1 0 1-1 2-1l1-2h0v-1l2-2 1 1z" class="I"></path><path d="M295 676c1 1 1 3 2 4l1 2h2v1l-1 1c-1 0-2-1-3-2h-1c-2-1 1 1-1-1-1 0-1-1-1-1l-1-1c1 0 1-1 2-1l1-2h0z" class="D"></path><path d="M295 676v-1l2-2 1 1c1 1 1 2 2 3 0 1 1 2 2 3l-2 1c-1 0-2-1-3-1-1-1-1-3-2-4z" class="H"></path><path d="M298 674l2-2v3l1 1c1 1 3 2 4 3 0 2 1 2 2 3 0 1-1 2-1 3-1-1-2-1-3-1 0-2 0-3-1-4h0c-1-1-2-2-2-3-1-1-1-2-2-3z" class="Q"></path><path d="M288 681c1-1 2-1 3 0 1 0 2 2 3 2l1 1s1 0 2 1h1l1 1 1 1-1 1c-2-1-2 0-4 1-2-2-4-3-5-5 0-2-1-2-2-3z" class="B"></path><path d="M295 689v1 1h-1c-2 0-3 0-4-1-1-2-2-2-2-5-2-1-2-2-3-3l1-1h2c1 1 2 1 2 3 1 2 3 3 5 5z" class="H"></path><path d="M777 792c2-1 3-1 6-1-2 1-3 1-4 3 0 2 1 2 2 3h0c-2 3-6 5-8 8 0 1 1 4 0 6 0 2 0 5-2 7-4 6-13 8-19 12l1-1c1-1 1-1 3-2-3 0-4 1-6 2v1c0 1-1 1-1 2h-1v-1l1-1-1-1c-1 0-2 1-3 0h-1c-2 1-4 1-5 3l-1-1c3-2 6-3 9-5 2-1 5-2 8-3 2 0 3-1 5-2 3-1 6-5 7-8l1-2c-1-1-1-2 0-3v-1c-1-1-1-1-1-2v-1c1-1 1-2 2-3 0-1-1-2 0-3 0-2 0-4 1-6v-1c1 1 2 1 3 0 1 0 1 1 2 1h2z" class="U"></path><path d="M770 814l2-3h1c0 2 0 5-2 7-3 0-4 2-7 2h0l1-1c2-1 3-3 5-5z" class="M"></path><path d="M773 791c1 0 1 1 2 1 0 2 0 3-1 5h-1-1c-1 0-1 1-1 2s0 2-1 2h-1c0-1-1-2 0-3 0-2 0-4 1-6v-1c1 1 2 1 3 0z" class="Y"></path><path d="M764 820c3 0 4-2 7-2-4 6-13 8-19 12l1-1c1-1 1-1 3-2-3 0-4 1-6 2v1c0 1-1 1-1 2h-1v-1l1-1-1-1 16-9z" class="X"></path><path d="M777 792c2-1 3-1 6-1-2 1-3 1-4 3 0 2 1 2 2 3h0c-2 3-6 5-8 8 0 1 1 4 0 6h-1l-2 3c0-2 1-5 0-7v-1c0-2 0-2 1-3 0-1 1-1 1-1 2-1 2-2 2-4h2c1-2 1-4 1-6z" class="l"></path><path d="M768 792h2c-1 2-1 4-1 6-1 1 0 2 0 3-1 1-1 2-2 3v1c0 1 0 1 1 2v1c-1 1-1 2 0 3l-1 2c-1 3-4 7-7 8-2 1-3 2-5 2-3 1-6 2-8 3 1-1 2-2 4-3l-1-1c1-1 2-1 2-2h1 1v-1c1-1 2-1 3-2 0-1 1-2 1-3v-1c-2 1-3 2-4 2l-1-4c2 0 2 0 3-1l-1-1c2-1 1-1 2-2-1-2-1-2-3-2l1-3h0l1-2c1-1 1-2 2-3h1 1c1-1 1-1 2-1 2-2 4-3 6-4z" class="O"></path><path d="M768 792h2c-1 2-1 4-1 6-1 1 0 2 0 3-1 1-1 2-2 3l-1-2c0-2 1-5 1-7 1-1 1-2 1-3z" class="q"></path><path d="M766 802l1 2v1c0 1 0 1 1 2v1c-1 1-1 2 0 3l-1 2c-1 3-4 7-7 8-2 1-3 2-5 2 3-3 6-4 9-7 0-1 1-2 1-2 0-3 1-5 0-7 0-2 0-3 1-5h0z" class="L"></path><path d="M758 797h1 1c1-1 1-1 2-1l-2 6c1 1 3-1 3 3v2c-1 5 0 8-5 12-1 2-2 2-4 3l-1 1h-2l-1-1c1-1 2-1 2-2h1 1v-1c1-1 2-1 3-2 0-1 1-2 1-3v-1c-2 1-3 2-4 2l-1-4c2 0 2 0 3-1l-1-1c2-1 1-1 2-2-1-2-1-2-3-2l1-3h0l1-2c1-1 1-2 2-3z" class="I"></path><path d="M756 800h3v1 1c-1 0-3 1-4 0l1-2z" class="F"></path><path d="M291 642c1-2 5-9 7-9 1 2 1 3 3 4 1 0 2 0 3 1v1h1c0-1 0-2 1-3h1v1c-1 2-1 4-2 7 0 0-1 1-1 2v1h-1v-4c-1-1-2-3-3-4l-1 1h0c1 2 1 3 0 5h-1v-2l-1 1v2c1 2 3 4 4 5 0 0-2 2-2 3v3l2 2c-2 1-2 0-4 2l-1-1-3-1-3-3-3 2h-1c-1 0-1-1-2-2 1-2 1-2 0-4h0c-1-1-2-3-3-5v-4l1-1 1 1c0 2 0 3 2 4 0 0 1 0 1-1h2 1c-1-1-1-2-1-2 1-1 1-1 3-2z" class="E"></path><path d="M293 659c0-1 0-2 1-3h2 0l3-2v3l2 2c-2 1-2 0-4 2l-1-1-3-1z" class="J"></path><path d="M296 660c1-1 0-2 2-3h1l2 2c-2 1-2 0-4 2l-1-1z" class="F"></path><path d="M292 642c1-1 1-2 2-2v1c1 2 1 3 2 4v4l-2 2c-2 0-2 0-4-1s-2-2-2-4h1c-1-1-1-2-1-2 1-1 1-1 3-2h1z" class="B"></path><path d="M291 642h1c1 0 1 1 2 1h-1c-1 1-1 1-2 1 0 2 1 2 2 3-1 1-1 2-3 2v1c-2-1-2-2-2-4h1c-1-1-1-2-1-2 1-1 1-1 3-2z" class="P"></path><path d="M284 652c-1-1-2-3-3-5v-4l1-1 1 1c0 2 0 3 2 4 0 0 1 0 1-1h2c0 2 0 3 2 4s2 1 4 1h1c-1 1-1 2-1 3h-2c-1 0-1-1-1-1 0-1-3-1-3-1l-1 1c1 1 2 1 4 2l-1 1h0l-3 2h-1c-1 0-1-1-2-2 1-2 1-2 0-4h0z" class="J"></path><defs><linearGradient id="C" x1="756.442" y1="884.209" x2="813.268" y2="858.646" xlink:href="#B"><stop offset="0" stop-color="#c6c2c2"></stop><stop offset="1" stop-color="#fcfbf6"></stop></linearGradient></defs><path fill="url(#C)" d="M763 868c2 0 8-1 10 0 3 0 10-1 13 1 1 0 4-1 5 0 1 0 1 0 3 1h0 1 3l1 1c2 0 6-1 8 0 2 0 14 0 15 1h3 1l1 1-1 1c-2 0-9-1-11 0h-1-5-13-12l-33-1-6 1c2-1 2-1 3-2h-2c0-1-1-1-1-1h0l3-2c1-1 3 0 4-1 3 0 7 1 9 0h2z"></path><path d="M752 868c3 0 7 1 9 0l3 1h0c-4 0-10 0-13 2h0c1 1 3 1 4 2h-4l-6 1c2-1 2-1 3-2h-2c0-1-1-1-1-1h0l3-2c1-1 3 0 4-1z" class="P"></path><path d="M250 827l4 1h0c2 1 3 3 4 4l1 3h0c2-1 3-1 4-1l3 3h3v1 1c2 1 4 2 5 4l-1 2c1 1 1 2 1 4l1 1c1 1 3 2 4 3 2 1 5 3 6 5v1 2l2-1c0 1 1 2 1 2h0l-2 2v2h-2c-2 1-4 0-7-1 0 0-1-2-2-2 0-3-2-4-4-6 1-1 1-2 1-3-1-1-1-1-2-1-1-2-2-3-3-5v-1c-1-2-1-2-3-3-3-1-4-2-6-4-1-1-2-1-3-2s-1-1-1-2c-2-3-3-6-4-9z" class="T"></path><path d="M279 853c2 1 5 3 6 5v1h0-3v-3c-1 0-1 1-3 2v-2c-1-1 0-2 0-3z" class="G"></path><path d="M279 856h-2c-1-1-4-3-4-4l2-2c1 1 3 2 4 3 0 1-1 2 0 3z" class="Z"></path><path d="M272 854l1 1c0 1 1 1 2 2s1 2 2 4l1 1c-1 0-2 0-3 1 0-3-2-4-4-6 1-1 1-2 1-3z" class="l"></path><path d="M285 861l2-1c0 1 1 2 1 2h0l-2 2v2h-2c-2 1-4 0-7-1 0 0-1-2-2-2 1-1 2-1 3-1h1c1 0 1 1 2 1 2 0 3 0 4-2z" class="M"></path><path d="M250 827l4 1h0c2 1 3 3 4 4l1 3h0c2-1 3-1 4-1l3 3h3v1 1c2 1 4 2 5 4l-1 2c1 1 1 2 1 4h-3c-1-1-1-2-3-2h0v-1c1 0 2 0 3 1v-1c0-1 0-2-1-3v2c-2 0-2 0-3-1v-1-1c-1 1-2 2-3 2-3-1-4-2-6-4-1-1-2-1-3-2s-1-1-1-2c-2-3-3-6-4-9z" class="O"></path><path d="M254 828c2 1 3 3 4 4l1 3h-1c-2-1-3-1-5-3 0-2 1-2 1-4z" class="G"></path><path d="M263 834l3 3h3v1 1c2 1 4 2 5 4l-1 2c-1-2-3-4-6-5-1-1-3-1-4-1-2-1-3-2-4-4 2-1 3-1 4-1z" class="m"></path><path d="M368 898c0-2 0-2-1-4v-1l2 1v2h2 0 4 2c1 0 2 1 3 1v2 2l1 1h2l3 2 3 1h1c1 0 1 0 1-1l3 2-2 2h-3s1 1 2 1 2 0 3 1c1 0 3 1 5 1h-9c-1 1-3 0-5 0-1-1-3 0-4 0-1-1-1-1-2-1h-3c-1-1-2-1-3-1h0 3s-1-1-2-1c-1-1-2-1-3-1h-1c-1 0-1 0-2-1-1 0-2 0-3-1h-2c-2-1-7-1-9-1-1 1-2 1-3 1-2-1-2-1-3-1-2 0-3-1-5-1-1 0-3 0-5-1h4c2 0 3 0 4 1h4c1 0 2 1 3 1 2-1-1-1 1-1h2c-1-1-2-1-2-1l-1-1h-1c-1-1-1-1-2-1-1-2-5 0-7-1h-1c-2-1-5-1-6-2-2-1-2-1-2-3 3 3 6 4 10 4h0c3 1 8 1 10 0 3-1 5 0 7-2 2 1 4 1 6 1l1 1z" class="V"></path><path d="M354 898c3-1 5 0 7-2 2 1 4 1 6 1l1 1-1 2h0c-4-2-9-2-13-2z" class="X"></path><path d="M367 900h0c3 2 6 5 9 6h-6l-2-1c-3-2-1-2-1-3v-2z" class="B"></path><path d="M380 901l1 1h2l3 2 3 1h1c1 0 1 0 1-1l3 2-2 2h-3l-7-2h-6l-1-2h2l-1-2h1 0l3-1z" class="X"></path><path d="M380 901l1 1h2c-2 1-3 2-5 2l-1-2h0l3-1z" class="d"></path><path d="M389 905h1c1 0 1 0 1-1l3 2-2 2h-3l-7-2c2-1 5-1 7-1z" class="W"></path><path d="M368 898c0-2 0-2-1-4v-1l2 1v2h2 0 4 2c1 0 2 1 3 1v2 2l-3 1h0-1l1 2h-2l1 2h0c-3-1-6-4-9-6l1-2z" class="g"></path><path d="M371 896h4 2c1 0 2 1 3 1v2 2l-3 1-1-1c-2 0-2-1-3-2s-2-1-3-2l1-1h0z" class="N"></path><path d="M312 677c1 1 2 3 3 5 1-1 2-1 3-1 1-1 2-1 4-2h0c1-2 4-4 7-5h1c0-1 1-1 2-1h0 1c1-1 3-1 4 0h1c1 0 3 0 4-1h0c2-1 4 0 5 0v1c1 0 1 0 2 1l2 3h1c0-1 1-1 2-2h1 2 0c2 0 2 1 3 1v1h-2l-1-1c-1 0-4 1-5 2l-1 1-1-2c-1-2-2-2-4-4h-3c-2 1-3 1-5 2-2-1-2-1-4-1h0l-2 4-2 3-1 2c0 2-1 4-1 6 0 1 0 1-1 3 0 1-1 2-2 2-1 1-2 1-2 1-1 1-2 2-3 2h0l-2 1h-1v-1l-1-1c-1 2-1 2-1 4-1-1-1-2-1-3 1-2 1-2 3-2 0-2 0-3-2-4 0 0-1-1-1-2h0c0-1-1-1-1-2-3 0-3 0-4-2-1-1-1-2-2-3s-2-1-2-3c-1-1-3-2-4-3l1-1c3-1 5-1 8-1 1 1 1 2 2 3z" class="D"></path><path d="M332 678c-1-1-2-1-3-2v-1l5-1-2 4z" class="V"></path><path d="M323 682c1-1 2-1 3-2s1-1 2-1c0 3-1 4-2 5h-2l-1-2z" class="E"></path><path d="M301 676l1-1c3-1 5-1 8-1 1 1 1 2 2 3h-1l-1 2c0 1 0 1 1 1l-1 1c-2-2-3-2-5-2-1-1-3-2-4-3z" class="K"></path><path d="M314 686c0-1 1-2 2-2l1-1h0c3-2 4-2 6-1l1 2h2l1 1 2-2c0 2-1 4-1 6 0 1 0 1-1 3 0 1-1 2-2 2-1 1-2 1-2 1-1 1-2 2-3 2h0l-2 1h-1v-1l-1-1c-1 2-1 2-1 4-1-1-1-2-1-3 1-2 1-2 3-2 0-2 0-3-2-4 0 0-1-1-1-2h0c0-1-1-1-1-2l1-1z" class="V"></path><path d="M317 695v1l1-1c0-1 0-1 1-2 1 0 2 0 3-1h1c1 1 1 1 3 1l1-1c0 1-1 2-2 2-1 1-2 1-2 1-1 1-2 2-3 2h0l-2 1h-1v-1l-1-1c-1 2-1 2-1 4-1-1-1-2-1-3 1-2 1-2 3-2z" class="C"></path><path d="M314 686c0-1 1-2 2-2l1-1h0c3-2 4-2 6-1l1 2-1 1c-1 2 0 4-1 5-1 0-2-1-2-1-1-1 1-3-3-2h0-3v-1z" class="H"></path><path d="M270 655v-2h0 1l2 2c1 2 3 3 4 5 1 1 2 2 3 4h0l1-1 3 3 2-1h3l1-1c-1-1-3-1-3-3h0 1l-1-3 3-2 3 3 3 1 1 1c2-2 2-1 4-2l1 1c-1 2-1 4-1 5l1 4v2l-2 1-2 2-1-1-2 2v1h0l-1 2c-1 0-1 1-2 1l-8-3c-1-1-2-1-3-2l-1 1h-1l-1-1c-1-2-2-3-3-5-1 0-1 0-1-1l1-1c-1-2-1-3-1-5-1-2-3-4-4-7h0z" class="K"></path><path d="M301 659l1 1c-1 2-1 4-1 5l-4-4c2-2 2-1 4-2z" class="H"></path><path d="M296 668c1-2 2-2 3-3h1c0 2-1 2-1 3v1c-1 1-1 2-2 3l-1-4zm-12-2l2-1h3c0 1-1 3-1 3-1 0-1 0-2 1h0l-2-1v-2z" class="O"></path><path d="M288 671l1 2 2-1v-1-1l1-1c1 1 1 1 1 3v1 1l-6-1v-2h0 1z" class="h"></path><path d="M280 664l1-1 3 3v2c0 1 1 1 1 2h1l-1 1c0-1-1-1-1-1l-2-2-2-4z" class="E"></path><path d="M277 660c1 1 2 2 3 4h0l2 4c0 1-1 2-2 4h-1l-4-5c-1-2-1-3-1-5 0 1 1 1 2 2l1 1h1c0-2 0-3-1-5z" class="h"></path><path d="M293 673l1-2h1v-3l-4-4h1c1 0 1 0 1 1l3 3 1 4c1-1 1-2 2-3h3v2l-2 1-2 2-1-1-2 2v1h0c-1-1-2 0-3-1h1 1 0l-1-2z" class="j"></path><path d="M270 655v-2h0 1l2 2c1 2 3 3 4 5s1 3 1 5h-1l-1-1c-1-1-2-1-2-2-1-2-3-4-4-7h0z" class="L"></path><path d="M754 805c2 0 2 0 3 2-1 1 0 1-2 2l1 1c-1 1-1 1-3 1l1 4c1 0 2-1 4-2v1c0 1-1 2-1 3-1 1-2 1-3 2v1h-1-1c0 1-1 1-2 2l1 1c-2 1-3 2-4 3-3 2-6 3-9 5l1 1c1-2 3-2 5-3h1c-1 2-3 3-5 5l-2 4c-1 1-2 1-2 3l-1-2-3 3-2-1c0-1-1 0 0-1l-1-1v2h-2l-2-2c-1 2-2 3-3 4l-2 2h-1v-1c0-1-1-2-2-2v-1c1 0 2 0 4-1 0-1 0-2 1-3 0-1 1-3 2-4v-1c2 0 14-10 17-12s7-4 9-8c2-2 3-5 4-7z" class="b"></path><path d="M727 836h-1c-1-2 0-2 1-4 1 0 1 0 3 1l-3 3z" class="H"></path><path d="M750 822l1 1c-2 1-3 2-4 3-3 2-6 3-9 5l1 1c1-2 3-2 5-3h1c-1 2-3 3-5 5l-2 4c-1 1-2 1-2 3l-1-2-3 3-2-1c0-1-1 0 0-1l-1-1v2h-2l-2-2v-1c0-1 1-1 2-2l3-3h1c0 1-1 2-1 3h0c1 0 2-1 2-2v-1c1 0 2-1 3-2 2-1 4-3 6-4l1-1h3l1-2c1 0 2-1 4-2z" class="D"></path><path d="M739 832c1-2 3-2 5-3h1c-1 2-3 3-5 5l-2 4c-1 1-2 1-2 3l-1-2-3 3-2-1c0-1-1 0 0-1 2-1 2-3 3-4 2-2 3-4 5-5l1 1z" class="R"></path><path d="M739 832c1-2 3-2 5-3h1c-1 2-3 3-5 5-3 1-4 4-6 5v-1c0-2 3-5 5-6z" class="O"></path><path d="M725 665v-1c1-3 3-5 5-7 1 0 1 0 2-1s2-2 4-2h2v-3s1 0 1-1v-2c1-1 3-2 4-3 2-2 3-5 6-5h1c1-1 2-1 4-1 2 1 2 1 4 3l2-2c1 0 2 0 3 1 3 0 5 1 8 3 2 1 1 0 2-1h2c1 0 2 1 4 1v2l-1 2h-1 0c-1 1-1 1-1 2l-2-1c0 1-1 1-1 3-1 0 0 0-1-1h0c-1 0-1 1-1 2l-1-1-6-3h-5v1c-2 0-5 3-6 4 0 1 0 1-1 2 0 0-1 0-2 1s-2 1-3 2c-1 0-2 1-3 2v-2c2-1 3-1 4-4-2 0-4 0-5 1v-2h-1c-2 1-2 1-4 1-1 0-2 0-3 1h-1c-1 1-3 2-4 3l-2 2h0l-1 2s-1 1-1 2h-1z" class="D"></path><path d="M775 643c1 0 2 1 4 1v2h-3c-1 0-1 1-2 1v-3l1-1z" class="E"></path><path d="M758 643c2-1 3-2 5-1h1l3 1 1 1c1 1 2 1 4 1v2c-2 1-5 1-6 0l-2-1v3h-5v1c-1-1-2-1-3-2l-1 1v-2-1h1c2 0 3 0 4-1l1-1 1 1 2-1 1 1 1-1h0l-2-1h-4l-1 1-1-1z" class="o"></path><path d="M760 645l2 1 2-1h0v1 3h-5v1c-1-1-2-1-3-2l-1 1v-2-1h1c2 0 3 0 4-1z" class="B"></path><path d="M739 652l1-1 1-2c0-1 0-1 1-2 3-1 5-4 7-6 4 0 6 0 9 2l1 1 1-1h4l2 1h0l-1 1-1-1-2 1-1-1-1 1c-1 1-2 1-4 1h-1c0-1-1-1-2-1s-1 0-1 1l-6 3c-2 1-4 5-6 4l-1-1z" class="s"></path><path d="M739 652l1 1c2 1 4-3 6-4l6-3c0-1 0-1 1-1s2 0 2 1v1 2l1-1c1 1 2 1 3 2-2 0-5 3-6 4 0 1 0 1-1 2 0 0-1 0-2 1s-2 1-3 2c-1 0-2 1-3 2v-2c2-1 3-1 4-4-2 0-4 0-5 1v-2h-1c-2 1-2 1-4 1 1 0 0 0 1-1v-2z" class="E"></path><path d="M755 649l1-1c1 1 2 1 3 2-2 0-5 3-6 4 0 1 0 1-1 2 0 0-1 0-2 1s-2 1-3 2c-1 0-2 1-3 2v-2c2-1 3-1 4-4v-1c1-1 0-1 0-2l1-1c1 0 1 2 1 3 2-2 1-2 1-4h3l1-1z" class="D"></path><defs><linearGradient id="D" x1="721.771" y1="876.117" x2="758.915" y2="878.711" xlink:href="#B"><stop offset="0" stop-color="#9c9894"></stop><stop offset="1" stop-color="#dad8d4"></stop></linearGradient></defs><path fill="url(#D)" d="M723 868h3c1 0 4-1 4 0 1 1 2 1 3 1l1 1 14-1-3 2h0s1 0 1 1h2c-1 1-1 1-3 2l6-1 33 1c-3 1-6 0-9 0h-15-5s0 1-1 2h1c6 0 13-1 19 0 2 1 5 1 7 0h1l1 1c-4 1-9 0-13 1h-10-1c0 1-1 1-1 2l-2 1h2c1-1 5-1 6 0h2c3-1 8 0 10-1h2 0c-1 1-2 1-3 1h-1c-1 1-3 0-4 1h-20-7-3s-1 1-1 2c-1 0 0 0-1 1h2 0c-2 1-2 1-4 0h0-1-4v-1c-1-1-2-1-3-1h-2c-1-1-1-1-2-1h5 0l-10-1c-4 0-9 2-13 3 2-3 5-3 7-3v-1l4-1c2-1 2-2 3-4l2-6h1v-1z"></path><path d="M729 882l20-1 1 1h-7-3s-1 1-1 2c-1 0 0 0-1 1h2 0c-2 1-2 1-4 0h0-1-4v-1c-1-1-2-1-3-1h-2c-1-1-1-1-2-1h5 0z" class="V"></path><defs><linearGradient id="E" x1="722.827" y1="868.222" x2="742.9" y2="874.196" xlink:href="#B"><stop offset="0" stop-color="#857f79"></stop><stop offset="1" stop-color="#b0aeab"></stop></linearGradient></defs><path fill="url(#E)" d="M723 868h3c1 0 4-1 4 0 1 1 2 1 3 1l1 1 14-1-3 2h0s1 0 1 1h2c-1 1-1 1-3 2h-5c-3 1-6 1-9 0l-5 1-1 1h1c-4 3-7 3-11 4-1 1-1 1-2 1v-1l4-1c2-1 2-2 3-4l2-6h1v-1z"></path><path d="M720 875c1 0 2 0 4-1h2c1 0 2-1 3-1s2-1 3-1h5v1h-1c-1 0-2 0-2 1h-2-1l-5 1-1 1h1c-4 3-7 3-11 4-1 1-1 1-2 1v-1l4-1c2-1 2-2 3-4z" class="a"></path><defs><linearGradient id="F" x1="741.809" y1="878.276" x2="773.229" y2="845.434" xlink:href="#B"><stop offset="0" stop-color="#9f9d97"></stop><stop offset="1" stop-color="#fffdfb"></stop></linearGradient></defs><path fill="url(#F)" d="M737 852c2-3 3-4 6-5 2 2 9 2 12 2h-3c-3 0-8 1-9 0h-2l1 2h6 2c2 0 11-1 14 0 1 0 6 0 8 1 2 0 5-1 7 1h-2l1 1c-2 1-8 0-11 0h0c-2 0-7 0-8-1-1 0-2 0-2 1h-5-2 0l1 2c1 1 2 2 3 2s5 1 5 0c1 0 1 0 2-1 2 0 0 1 2 0h3-1l-2-1h0v-1h2c2 1 4 0 7 0h1c2 0 4 0 5 1h3 5l1 1c-2 2-9-1-11 1h0c-2 0-6-1-7 0h-7l1 1h1c1 0 3-1 4 0 1 0 2 0 4 1h4 0 2 2 2 2c1 1 4 0 6 0 0 1 2 2 2 2-3 2-16 1-20 1h-3c-3 0-6 0-9 1h-1l2 1c1 0 2 1 2 2v1h-2c-2 1-6 0-9 0-1 1-3 0-4 1l-14 1-1-1c-1 0-2 0-3-1 0-1-3 0-4 0h-3c2-2 4-4 5-6 0 0 0-1 1-2v-2c1-2 6-5 8-6h0z"></path><path d="M737 852c2-3 3-4 6-5 2 2 9 2 12 2h-3c-3 0-8 1-9 0h-2l1 2c1 1 3 1 5 1 3 0 9-1 12 0l-12 1h-4l-1-1h-1-4z" class="B"></path><path d="M723 868c2-2 4-4 5-6 0 0 0-1 1-2l1 2c1 0 3 0 4 1-1 1-1 1-2 1h-3v2c2 1 3 0 4 1 4 0 7 1 11 1 2 0 4-1 5 0h3c-1 1-3 0-4 1l-14 1-1-1c-1 0-2 0-3-1 0-1-3 0-4 0h-3z" class="L"></path><path d="M496 917h-1c-2-1-11-1-13-1-1 1-2 1-3 1h-24-10c-1-1 0-1-1-1h-1-7c-5 0-12 1-17 0h-1-2-1-3c-2-1-5-1-7-1-1-1-1-1-2-1h-1-2-1c-1-1-4 0-6 0l-2-1c-2 0-4-1-6-1-3 0-6 1-8-1h-2c-2-1-2-1-3-1-2-1-6-1-9-1-4-1-8-1-12-1h-6 20 4c1 0 2 0 3 1h1 0c1 0 2 0 3 1h3c1 0 1 0 2 1 1 0 3-1 4 0 2 0 4 1 5 0h9 1l33 1c4 1 8 0 11 0s6 1 9 0 7 0 11 0h15l7 2c9 1 19 0 28 0h40 24c4 0 8 0 12-1l37-3 1 1h2 10-6l-1 1h-2-1c-5 1-12-1-16 1 0 1-1 1-2 1 2 1 3 1 4 1 3 1 7-1 9 1h-15c-2 0-8-1-9 0h-1-2c-3 1-7 0-10 0h-3 0c-2 0-4 1-5 0-2 0-7 0-8-1-3 0-18 1-20 0h-2c-1 0-2 0-4 1h-1c1 0 2 0 3 1 2 0 5-1 7 0h-11c-2 0-5-1-8 0h-9c-1-1-2-1-4 0-1 0-2-1-4-1-8 0-16 0-23 1z" class="I"></path><path d="M800 753c1 1 1 1 1 2l-1 1c-1 0-2 1-2 2l-1 1 1 1h1 1l-1 2 2 2c1 0 2 1 3 1 1 2-1 2 0 3h2l1 1c-1 3-2 5-3 7-2 3-2 5-3 8l-1-1v1c-1 1-2 1-4 1-3 3-9 6-13 6h0c-3 0-4 0-6 1h-2c-1 0-1-1-2-1-1 1-2 1-3 0 1-1 3-2 5-3v-1h-2l-1-1c-2 1-3 3-4 5h-2c0-2 0-3 1-4 1-3 2-6 2-9v-1c3-4 10-5 14-6 1-1 1-1 1-3l3-3c1-3 3-3 5-5l1-1c3-1 5-4 7-6z" class="K"></path><path d="M787 765c1 0 1 0 2-1 1 0 2-1 3-1s1 0 2 1c0 1 0 2-1 3h0v4 1c0 1 0 1-1 2-1 2-1 4-2 6 0 1-1 3-3 4l-3 1c-1 1-1 1-2 1-2 0-5 3-7 2v-1l2-1c1-2 3-5 5-6h1c1 0 2-1 3-2 0-1 0-1 1-2h0c2-2 1-2 1-4l1-1v-1c0-2 0-2-1-3-2 1-3 1-4 1l3-3z" class="I"></path><path d="M777 786c1-2 3-5 5-6h1c1 0 2-1 3-2 0-1 0-1 1-2h0c2-2 1-2 1-4l1-1c0 3 0 4-1 7h-1c-1 3-5 7-8 7l-2 1z" class="E"></path><path d="M773 791v-1h4c1-1 1-1 2-1h3c1-1 2-1 4-1 4-2 6-3 9-7l2-4c1-1 2-3 1-5s1-3 1-5l-1-1h-1l1-1c-1-1-1-2-1-3v-1l-4 1-1-1c2-1 3-1 5-2l1 1h1 1l-1 2 2 2c1 0 2 1 3 1 1 2-1 2 0 3h2l1 1c-1 3-2 5-3 7-2 3-2 5-3 8l-1-1v1c-1 1-2 1-4 1-3 3-9 6-13 6h0c-3 0-4 0-6 1h-2c-1 0-1-1-2-1z" class="T"></path><path d="M796 785l3-4s0-1 1-2v-1c1-2 2-3 2-5 0-1 0-2 1-3v1l1-1c0-1 2-1 3-1-1 3-2 5-3 7-2 3-2 5-3 8l-1-1v1c-1 1-2 1-4 1z" class="M"></path><path d="M784 768c1 0 2 0 4-1 1 1 1 1 1 3v1l-1 1c0 2 1 2-1 4h0c-1 1-1 1-1 2-1 1-2 2-3 2h-1c-2 1-4 4-5 6l-2 1h-2l-1-1c-2 1-3 3-4 5h-2c0-2 0-3 1-4 1-3 2-6 2-9v-1c3-4 10-5 14-6 1-1 1-1 1-3z" class="o"></path><path d="M748 832h1c0-1 1-1 1-2v-1c2-1 3-2 6-2-2 1-2 1-3 2l-1 1c-1 2-2 4-3 7-1 4-3 7-6 10-3 1-4 2-6 5h0c-2 1-7 4-8 6v2c-1 1-1 2-1 2-1 2-3 4-5 6v1h-1c-1 0-2 0-2 1h-1c-1 0-1-1-2-1l-1 1c-1 0-2-1-3 0s-1 1-2 1c1-1 1-2 2-3l-2 1-2-2h0c3-4 5-6 8-9l-2-1-1 1-7-1h-1c-1-1-4 0-6 0v-1l1-2h1v1l2-2h0c1-1 0-1 0-1l1-1c3 0 2-1 4-2l1-2c0-1 0 0 1-1l1-1v-1h2l2-2-1-1h2v1c1 0 2 1 2 2v1h1l2-2c1-1 2-2 3-4l2 2h2v-2l1 1c-1 1 0 0 0 1l2 1 3-3 1 2c0-2 1-2 2-3l2-4c2-2 4-3 5-5 1 1 2 0 3 0l1 1-1 1v1z" class="W"></path><path d="M745 829c1 1 2 0 3 0l1 1-1 1v1h0c-1 3-2 5-4 7v2c-1 2-2 3-4 4h0v-2h-1l-2 2c-1-1-1-1-1-2 1 0 0 0 1-1h1v-4l2-4c2-2 4-3 5-5z" class="g"></path><path d="M736 841c0-2 1-2 2-3v4h-1c-1 1 0 1-1 1 0 1 0 1 1 2l1 1-3 3-1 1c-2 2-4 2-6 4-1 1-2 1-4 1h-2-3 0c-1-1-1-1-2-1 0-1 1-1 1-2h2 0c1 0 1 0 2-1 1 0 1-1 2-1s2-1 2-1h1 0l1 1c1-2 2-3 3-4l-1-2 2-2 3-3 1 2z" class="T"></path><path d="M732 842l3-3 1 2-5 5-1-2 2-2z" class="N"></path><path d="M736 843c0 1 0 1 1 2l1 1-3 3-1 1c-2 2-4 2-6 4-1 1-2 1-4 1h-2-3c0-1 0-1 1-2h1 0l1-1h2c1-1 2 0 4-1 3 0 3-2 4-3l1-1 1-1c1-1 2-1 2-3z" class="k"></path><path d="M717 858c2-1 4-1 6-1h1l1 1c1 2 1 2 0 4h-1v-1l-1 1v1c0 1-1 2-2 3 0 1 1 1 2 2v1h-1c-1 0-2 0-2 1h-1c-1 0-1-1-2-1l-1 1c-1 0-2-1-3 0s-1 1-2 1c1-1 1-2 2-3l-2 1-2-2h0c3-4 5-6 8-9h0z" class="r"></path><path d="M717 869l4-3c0 1 1 1 2 2v1h-1c-1 0-2 0-2 1h-1c-1 0-1-1-2-1z" class="m"></path><path d="M717 858c2 0 3 0 4 1-1 2-1 3-3 4-1 1-3 1-4 2l-1 3-2 1-2-2h0c3-4 5-6 8-9h0z" class="O"></path><path d="M725 839l2 2h2v-2l1 1c-1 1 0 0 0 1l2 1-2 2 1 2c-1 1-2 2-3 4l-1-1h0-1s-1 1-2 1-1 1-2 1c-1 1-1 1-2 1h0-2c0 1-1 1-1 2l-1 1h-1-2-1-2l-1-1-2 2h0l-1-1-2 1h-1-3l1-2h1v1l2-2h0c1-1 0-1 0-1l1-1c3 0 2-1 4-2l1-2c0-1 0 0 1-1l1-1v-1h2l2-2-1-1h2v1c1 0 2 1 2 2v1h1l2-2c1-1 2-2 3-4z" class="a"></path><path d="M730 843c0 2 0 2-2 3l-1 1-2-1c0 1-1 2-1 2-2 1-3 1-4 2h-1v-1h-1c-1 2-2 3-4 3h-3-1l-2 1c-1-1-2-1-3-2 3 0 2-1 4-2 0 0 1 0 2 1 0-1 1-1 2-2 1 0 1 1 2 1s1 0 2-2h1 1 2c1 0 2-1 3-2 0-1 1-1 1-2l1 1 1 1c1-1 2-2 3-2z" class="h"></path><path d="M725 839l2 2h2v-2l1 1c-1 1 0 0 0 1v2c-1 0-2 1-3 2l-1-1-1-1c0 1-1 1-1 2-1 1-2 2-3 2h-2-1-1c-1 2-1 2-2 2s-1-1-2-1c-1 1-2 1-2 2-1-1-2-1-2-1l1-2c0-1 0 0 1-1l1-1v-1h2l2-2-1-1h2v1c1 0 2 1 2 2v1h1l2-2c1-1 2-2 3-4z" class="K"></path><path d="M334 674c2 0 2 0 4 1 2-1 3-1 5-2h3c2 2 3 2 4 4l1 2 1-1c1-1 4-2 5-2l1 1h2c1 1 3 3 4 5v3l1 1v1c-1 0-1 0-2 1l1 1c1-1 1-1 2 0h1l-1 2-3-1v-1c-2 1-2 2-3 3s-2 1-3 1v1 1c-1 1-1 1-3 2 0 1-1 1-1 2h-1l-1 1c-2 0-7 1-8-1l-2 1 1 1h-1-4l-1 1 1 2h-1l-2-2c-2 1-3 3-4 6h0-1c0-1-1-2-1-3h-1v-2c0-2-2-3-4-4h-3v-2c1 0 2-1 3-2 0 0 1 0 2-1 1 0 2-1 2-2 1-2 1-2 1-3 0-2 1-4 1-6l1-2 2-3 2-4h0z" class="J"></path><path d="M334 674h0c0 3 1 5 2 7l-2 1-1-1h-3l2-3 2-4z" class="H"></path><path d="M334 701c2-1 3-3 5-5h1v2h1 2v1l-2 1 1 1h-1-4l-1 1 1 2h-1l-2-2v-1z" class="V"></path><path d="M330 681h3l1 1-1 2c1 1 1 2 2 3h0l-1 1c0 1 0 2-1 3v1c-1 2-2 4-5 6h-2v-1c0-1 0-1-1-2h-1c1 2 1 2 2 3v1l1 1h1l1 1-1 3 1 1c2 0 2 0 3-1v-1c0-1 1-1 1-2h1v1c-2 1-3 3-4 6h0-1c0-1-1-2-1-3h-1v-2c0-2-2-3-4-4h-3v-2c1 0 2-1 3-2 0 0 1 0 2-1 1 0 2-1 2-2 1-2 1-2 1-3 0-2 1-4 1-6l1-2z" class="F"></path><path d="M330 681h3l1 1-1 2-1 1v2c-1 1-2 2-3 2 0 1-1 1-1 2l-1 1c1-2 1-2 1-3 0-2 1-4 1-6l1-2z" class="E"></path><path d="M714 824c1-1 0-1 1-1 2 0 3-1 4-2s1-2 1-3 0-1 1-2c0 2 1 3 1 4 1 1 1 1 2 1l2-1 2 2c1 0 2-1 3-1l3-3c1 1 2 1 3 1-2 1-2 1-2 3 1 0 1-1 2-1l2-2 2 1c-3 2-15 12-17 12v1c-1 1-2 3-2 4-1 1-1 2-1 3-2 1-3 1-4 1h-2l1 1-2 2h-2v1l-1 1c-1 1-1 0-1 1l-1 2c-2 1-1 2-4 2l-1 1s1 0 0 1h0l-2 2v-1h-1l-1 2v1h-3l-2-2v-1l-1-1h-1l-1 1c-1 0-1 0-2 1h0c-1 0-2-1-2-1l-1-1c0-1 0-3-1-4 2-2 4-1 5-3l2 2 1-2c1-4 1-10 1-14h0c-1-1-1-2-1-3 0-2 0-2 1-2l2-1h1 0l3-1c2-2 1-3 1-5 1 1 2 2 3 2 1 2 1 2 3 2 0-2 0-3 1-4 2 0 2 0 4 1l1-1v4z" class="G"></path><path d="M724 821l2-1 2 2c0 1 0 1-1 2l-2-2-1-1z" class="K"></path><path d="M703 834c1 0 2 1 3 0 1 0 1-2 2-1l-2 3c0 1-1 1-2 2-1-1-1-1-1-3v-1h0z" class="D"></path><path d="M708 829c5-2 10-5 14-9 1 1 1 1 2 1l1 1-2 2c-1 1-1 1-2 1-1 1-1 2-2 3 1 0 1 1 2 1-1 1-3 1-4 1-1 1-3 2-5 3h-1v2h-1v-1c0-1 0-2-1-2l-1-3z" class="B"></path><path d="M735 822c1 0 1-1 2-1l2-2 2 1c-3 2-15 12-17 12h-1c0 1-1 1-2 2 0 1-1 2-3 2l1 1v1 1h-1v-1l-1-1c-2 2-4 2-5 3v-4c2-1 4-1 6-3h0l2-1c0-1 0-1 1-1h1v-2h2c0-1 2-2 3-2 2-1 2-2 3-4h1 1l1-1h2z" class="Z"></path><path d="M714 824c1-1 0-1 1-1 2 0 3-1 4-2s1-2 1-3 0-1 1-2c0 2 1 3 1 4-4 4-9 7-14 9-2 2-4 3-5 5h0v1c-1 2-1 4-1 6h-2v-1h-1 0c-1 1-1 2-3 2v-1h-1c0-2 0-2 1-4l-1-1v-4h0c-1-1-1-2-1-3 0-2 0-2 1-2l2-1h1 0l3-1c2-2 1-3 1-5 1 1 2 2 3 2 1 2 1 2 3 2 0-2 0-3 1-4 2 0 2 0 4 1l1-1v4z" class="U"></path><path d="M696 841l3-3v-2l3-2h1v1c-1 2-1 4-1 6h-2v-1h-1 0c-1 1-1 2-3 2v-1z" class="M"></path><path d="M698 826h0l1 3 1 1 1-2c3 0 3 0 5-1h0v2l-1 1-2-1c0 1-1 1-1 2-1 1-2 2-3 2l-1 3h-3v-4h0c-1-1-1-2-1-3 0-2 0-2 1-2l2-1h1z" class="T"></path><path d="M694 829c0-2 0-2 1-2 0 1 1 1 1 2v2c1 1 2 2 3 2l-1 3h-3v-4h0c-1-1-1-2-1-3z" class="Y"></path><path d="M702 820c1 1 2 2 3 2 1 2 1 2 3 2 0-2 0-3 1-4 2 0 2 0 4 1l1-1v4 1h-1v-1l-1-1c-1 1-2 3-3 4h0l-1-1h-1l-1 1c-2 1-2 1-5 1l-1 2-1-1-1-3 3-1c2-2 1-3 1-5z" class="a"></path><path d="M702 820c1 1 2 2 3 2l-2 2c1 1 1 1 2 1l-1 1c-1 0-2 1-3 2l-1 2-1-1-1-3 3-1c2-2 1-3 1-5z" class="L"></path><path d="M695 832v4l1 1c-1 2-1 2-1 4h1v1c2 0 2-1 3-2h0 1v1h2c0-2 0-4 1-6 0 2 0 2 1 3 1-1 2-1 2-2h4c1 1 1 1 2 0v4c1-1 3-1 5-3l1 1v1h1v-1-1l-1-1c2 0 3-1 3-2 1-1 2-1 2-2h1v1c-1 1-2 3-2 4-1 1-1 2-1 3-2 1-3 1-4 1h-2l1 1-2 2h-2v1l-1 1c-1 1-1 0-1 1l-1 2c-2 1-1 2-4 2l-1 1s1 0 0 1h0l-2 2v-1h-1l-1 2v1h-3l-2-2v-1l-1-1h-1l-1 1c-1 0-1 0-2 1h0c-1 0-2-1-2-1l-1-1c0-1 0-3-1-4 2-2 4-1 5-3l2 2 1-2c1-4 1-10 1-14z" class="d"></path><path d="M705 848l-3-1c1-1 1-2 2-3h0l1-1c0 1 1 2 2 2l-2 3z" class="l"></path><path d="M703 835c0 2 0 2 1 3 1-1 2-1 2-2h4c1 1 1 1 2 0v4h-1l-2-1c-1 1-1 0-1 1l-2 2c-2 1-2 1-2 0l-1 2c-1-1-1-1-1-2v-1c0-2 0-4 1-6z" class="L"></path><path d="M707 845c2-2 5-4 8-4l1 1-2 2h-2v1l-1 1c-1 1-1 0-1 1l-1 2c-2 1-1 2-4 2l-1 1s1 0 0 1h0l-2 2v-1l2-4 1-2 2-3z" class="D"></path><path d="M693 848c0 1 0 1 1 2 1-1 1-2 2-3l2-3 2 2c0 1 0 1-1 2h1 1c1 0 1 0 2 1l1 1-2 4h-1l-1 2v1h-3l-2-2v-1l-1-1h-1l-1 1c-1 0-1 0-2 1h0c-1 0-2-1-2-1l-1-1c0-1 0-3-1-4 2-2 4-1 5-3l2 2z" class="t"></path><path d="M695 854c2-1 2-2 4-2 1 1 2 1 2 2l-1 2v1h-3l-2-2v-1z" class="f"></path><path d="M759 777v3c1 0 1 0 2-1l1 1-1 1h0l-4 4c0 1-1 2-1 2-2 3-4 5-6 7 1 0 2 0 3-1l1 1-2 3-1 3v1 1l1-2h2c1-1 0-1 0-3h3v-2h2l-1 2c-1 1-1 2-2 3l-1 2h0l-1 3c-1 2-2 5-4 7-2 4-6 6-9 8l-2-1-2 2c-1 0-1 1-2 1 0-2 0-2 2-3-1 0-2 0-3-1l-3 3c-1 0-2 1-3 1l-2-2-2 1c-1 0-1 0-2-1 0-1-1-2-1-4-1 1-1 1-1 2s0 2-1 3-2 2-4 2c-1 0 0 0-1 1v-4-1h-2l1-1h0l-1-3 2-2v-1h2c1-1 3-2 4-3s1-2 2-3h0c3-1 1-1 2-2 0 0 1 0 2-1 1 0 2-2 3-3v-4c2-2 4-5 6-6s4-3 7-3h2c1 0 1-1 2-1 2 0 2-1 3-1v-1c2 0 3 1 4 2 2-3 3-6 6-9z" class="N"></path><path d="M716 812v1l1 1c-1 1-3 2-4 4h0l-1-3 2-2v-1h2z" class="L"></path><path d="M759 777v3c1 0 1 0 2-1l1 1-1 1h0l-4 4c0 1-1 2-1 2-2 1-4 4-6 6-4 1-8 3-11 5-2 1-4 4-7 5 0-1 1-2 2-3s3-3 4-3h0c2-1 4-1 5-3 4-1 8-4 10-8 2-3 3-6 6-9z" class="C"></path><path d="M750 794c1 0 2 0 3-1l1 1-2 3-6 3c-1 1-2 2-3 4-2 1-4 2-6 4-1 0-2 0-3-1v-3c4-4 7-7 12-9l4-1z" class="J"></path><path d="M742 787h2c1 0 1-1 2-1 2 0 2-1 3-1v-1c2 0 3 1 4 2-2 4-6 7-10 8-1 2-3 2-5 3h0c-1 0-3 2-4 3v-1c-1 0-2 1-3 2 0 1 0 0-1 0l-1-1h0v-4c2-2 4-5 6-6s4-3 7-3z" class="h"></path><path d="M738 797l-2-1-1 1h-1v-1c1-1 4-4 6-5 1 0 1 0 2 1l1-2 1 1 2-2c1 0 1 0 2-1h2v1l-1 1c-2 1-3 2-4 2l-2 1v1c-1 2-3 2-5 3h0z" class="K"></path><path d="M751 802l1-2h2c1-1 0-1 0-3h3v-2h2l-1 2c-1 1-1 2-2 3l-1 2h0l-1 3c-1 2-2 5-4 7-2 4-6 6-9 8l-2-1-2 2c-1 0-1 1-2 1 0-2 0-2 2-3-1 0-2 0-3-1l-3 3c-1 0-2 1-3 1l-2-2-2 1c-1 0-1 0-2-1 0-1-1-2-1-4v-1c1-2 3-3 5-4l8-7v3c1 1 2 1 3 1 2-2 4-3 6-4 1-2 2-3 3-4l6-3-1 3v1 1z" class="Q"></path><path d="M726 820c2 0 3-1 5-2s5-1 6-2c2-1 3-2 5-3l2-2 2 2h-1c-1 1-1 1-3 2-1 0-2 1-3 2v-1c-1 1-1 2-1 3h-1c-1 0-2 0-3-1l-3 3c-1 0-2 1-3 1l-2-2z" class="P"></path><path d="M746 800l6-3-1 3v1 1h0c-1 0-2 0-2 1-1 1-2 3-3 4l-1 1c-1 0-1-1-2 0v1c-1 1-3 2-5 2 1-1 3-2 4-3v-1h-1l-3 2-1-1c2-2 4-3 6-4 1-2 2-3 3-4z" class="C"></path><path d="M746 800v3c-1 1-2 2-2 4l-1-1v-2c1-2 2-3 3-4zm-12 4v3c1 1 2 1 3 1l1 1 3-2h1v1c-1 1-3 2-4 3l-2 2c0 1 0 1-1 2h-2l-1 1c-2 1-4 1-6 3v-4l-2 1h-1v-1l2-2c0-1 1-1 1-2l8-7z" class="F"></path><path d="M725 813h0c3-2 5-3 7-5l1 1c-1 1-2 2-4 3v2h-1c-1 0-1 1-2 1h0l-2 1h-1v-1l2-2z" class="J"></path><path d="M729 812c2 2 2 2 4 2l1-1-1-1 1-1h1v2h1 0c0 1 0 1-1 2h-2l-1 1c-2 1-4 1-6 3v-4h0c1 0 1-1 2-1h1v-2z" class="C"></path><path d="M751 802l1-2h2c1-1 0-1 0-3h3v-2h2l-1 2c-1 1-1 2-2 3l-1 2h0l-1 3c-1 2-2 5-4 7-2 4-6 6-9 8l-2-1-2 2c-1 0-1 1-2 1 0-2 0-2 2-3h1c0-1 0-2 1-3v1c1-1 2-2 3-2 2-1 2-1 3-2h1l-2-2 1-1 1-1c1-2 3-4 4-5h1v-2h0z" class="R"></path><path d="M751 802l1-2h2c1-1 0-1 0-3h3v-2h2l-1 2c-1 1-1 2-2 3l-1 2h0-1l-1 1h0l-3 6v1l-1-2-2 2h-2l1-1c1-2 3-4 4-5h1v-2h0z" class="K"></path><path d="M807 730l1 2c1 0 2 1 2 1 0 2 0 2 1 3l-1 1v1c-1 2-3 4-6 6h0c0 1 0 1-1 2s-1 2-2 3l-1 2c-1 0-1 1-2 2 0 2-2 3-4 4l-2 1 1 1-1 1c-2 2-4 2-5 5l-3 3c0 2 0 2-1 3-4 1-11 2-14 6v1c0 3-1 6-2 9-1 1-1 2-1 4h2c1-2 2-4 4-5l1 1h2v1c-2 1-4 2-5 3v1h-2c-2 1-4 2-6 4-1 0-1 0-2 1h-1-1l1-2h-2v2h-3c0 2 1 2 0 3h-2l-1 2v-1-1l1-3 2-3-1-1c-1 1-2 1-3 1 2-2 4-4 6-7 0 0 1-1 1-2l4-4h0l1-1-1-1c-1 1-1 1-2 1v-3c-3 3-4 6-6 9-1-1-2-2-4-2v1c-1 0-1 1-3 1-1 0-1 1-2 1h-2l3-2c1 0 2-1 2-2 1-1 2-1 3-1 1-1 1-1 1-2 1-1 2-3 3-5 0 1 2 1 3 2 1-1 2-2 4-3 2-2 5-5 6-7v-2h1 1 0l3-3 4-5c1-2 2-4 4-5s3-2 4-3l4-2 2-1c2-1 4-3 5-5l1-1c0-1 0-2 1-3 1-2 4-5 7-5 1 0 2 0 2-1l1-1z" class="F"></path><path d="M772 770h0v-1h-1c1-2 1-2 2-3l1 1c2-3 4-4 6-6l1 1-1 1v2h-1l-5 4-1 1h-1z" class="b"></path><path d="M754 794c1 0 2-1 2-1 2-1 2 1 3-1l1-1h2v1l-3 3h0-2v2h-3c0 2 1 2 0 3h-2l-1 2v-1-1l1-3 2-3z" class="I"></path><path d="M788 747c1 0 3 0 5 1h0c-2 2-3 2-4 3h-1l-1 1-1 1h1 0c1 0 2-1 3-1 0 2-2 2-3 3-2 1-3 2-5 4l-1-1v-2l1-1v-1l-1-1-2 1v3c-1 0-1 1-2 1h-2v3c-1 1-2 1-3 1l4-5c1-2 2-4 4-5s3-2 4-3l4-2z" class="C"></path><path d="M757 777c1-1 2-2 4-3 2-2 5-5 6-7v-2h1 1c-1 3-2 5-4 8-1 0-3 1-3 2s0 2-1 3l-1-1 1-1c-1 0-2 0-2 1-3 3-4 6-6 9-1-1-2-2-4-2v1c-1 0-1 1-3 1-1 0-1 1-2 1h-2l3-2c1 0 2-1 2-2 1-1 2-1 3-1 1-1 1-1 1-2 1-1 2-3 3-5 0 1 2 1 3 2zm34-21c1 0 1-1 2-1 0 1 1 1 1 2l-2 1 1 1-1 1c-2 2-4 2-5 5l-3 3c0 2 0 2-1 3-4 1-11 2-14 6v1c0 3-1 6-2 9-1 0-2 0-2 1-2 0-3 1-4 1-1 1-2 1-3 1l6-8c0-1 1-2 2-3 2-5 2-6 6-9h1l1-1 5-4h1v-2l1-1-1-1 2-2c2-2 3-3 5-4v4l1-1c1-1 1 0 2-1 1 0 1 0 1-1z" class="L"></path><path d="M788 758l1 2c-1 1-2 2-5 2l3-3 1-1z" class="O"></path><path d="M782 759c2-2 3-3 5-4v4l-3 3c0 1-1 2-2 2v1c0 1-1 2-2 3-1 0-2 0-3 1l-1 1h-3l1-1 5-4h1v-2l1-1-1-1 2-2z" class="D"></path><path d="M807 730l1 2c1 0 2 1 2 1 0 2 0 2 1 3l-1 1v1c-1 2-3 4-6 6h0c0 1 0 1-1 2s-1 2-2 3l-1 2c-1 0-1 1-2 2 0 2-2 3-4 4 0-1-1-1-1-2-1 0-1 1-2 1 0 1 0 1-1 1-1 1-1 0-2 1l-1 1v-4c1-1 3-1 3-3-1 0-2 1-3 1h0-1l1-1 1-1h1c1-1 2-1 4-3h0c-2-1-4-1-5-1l2-1c2-1 4-3 5-5l1-1c0-1 0-2 1-3 1-2 4-5 7-5 1 0 2 0 2-1l1-1z" class="K"></path><path d="M805 737c1 0 1 0 3 1-3 2-5 4-6 7-1 0-1 0-2-1l1-1-2-2v-1h2 1c1 0 2-1 3-2v-1z" class="R"></path><path d="M808 732c1 0 2 1 2 1 0 2 0 2 1 3l-1 1h-2v1c-2-1-2-1-3-1h-2c-1 1 0 1-1 1v-2c1 0 2 0 4-1s2-2 2-3z" class="a"></path><path d="M795 741c1 1 2 1 3 2v1h-1c-2 1-3 2-4 4-2-1-4-1-5-1l2-1c2-1 4-3 5-5z" class="B"></path><path d="M808 738v-1h2v1c-1 2-3 4-6 6h0c0 1 0 1-1 2s-1 2-2 3l-1 2c-1 0-1 1-2 2 0 2-2 3-4 4 0-1-1-1-1-2-1 0-1 1-2 1 1-1 1-3 2-4s1-1 1-2v-1h1l1 1h1c-1-1-1-1-1-2h2v-2h2v-2c1 1 1 1 2 1 1-3 3-5 6-7z" class="q"></path><path d="M794 750c1 1 2 1 2 2l-3 3c-1 0-1 1-2 1 1-1 1-3 2-4s1-1 1-2z" class="O"></path><path d="M658 536l2-2v-2-3c1-1 1-1 2 0s2 1 2 3l-1 1c1 3 4 5 6 7 5 8 8 17 10 26 1 4 5 5 4 9v1h0c-4-1-8-2-11-3-5-1-12-1-16-1h-1l-1-1v-1l-2 1h-2-2c-1 0-1 0-2 1h-2-1-1c-1 1-1 1-1 2-1-1-1-1-1-2 1-2 2-3 2-5 2-4 2-8 4-12 1-5 4-10 7-14 2-2 3-4 5-5z" class="u"></path><path d="M654 571c8-2 19-3 25 1 2 1 3 2 4 4h0c-4-1-8-2-11-3-5-1-12-1-16-1h-1l-1-1z" class="B"></path><path d="M658 536c1 1 1 1 2 3v1h-1c0 1-1 2-2 3l-1 1h-1c-2 1-3 3-4 4v3c-1 0-1 1-1 2l-2 5-1 2v2c0 2-1 4-2 5h0l-1-1c1-1 1-2 1-4 0 0 0-1 1-1v-2l1-3c1-3 2-6 4-9 1-2 2-3 2-5v-1c2-2 3-4 5-5z" class="W"></path><path d="M669 543c5 8 7 16 8 24v1l-1-1-1-1c-4-5-5-11-7-17l-1-1c0-2 1-3 2-5z" class="J"></path><path d="M657 543c1-1 2-2 2-3h1l-2 16v1c-1 3 0 6-2 9-2 0-4 1-6 0 0-2 1-5 1-8 1-5 2-10 5-14l1-1z" class="r"></path><path d="M657 543c1-1 2-2 2-3h1l-2 16h-1 0-3v-2c-1-1 0-2 0-3 1-1 1-1 1-2 1-2 2-4 2-6h0z" class="K"></path><path d="M668 566h-9c0-10 0-20 4-29l3 2 2 2 1 2c-1 2-2 3-2 5l1 1c2 6 3 12 7 17l1 1-8-1z" class="D"></path><path d="M664 554h1c1 1 0 2 1 3 0 1-1 0 0 1 0 1 0 1-1 2-1-1-1-2-3-3 0-1 1-2 2-3z" class="E"></path><path d="M668 541l1 2c-1 2-2 3-2 5l1 1c2 6 3 12 7 17l1 1-8-1h-1v-4c1 0 1-1 2-2-1 0-1 0-2-1s0-3-1-5v4c-1-1 0 0 0-1-1-1 0-2-1-3h-1l-2-2c3-3 3-7 6-11z" class="C"></path><path d="M307 682c1 1 1 2 2 3 1 2 1 2 4 2 0 1 1 1 1 2h0c0 1 1 2 1 2 2 1 2 2 2 4-2 0-2 0-3 2 0 1 0 2 1 3-1 1-1 2-1 3v2c-1 0-2 0-2-1l-2 1v3 1l-1 1h-2c-2-1-3-1-6-2v1c0 1 0 1 1 2l1 1-1 2h0 2 1c1 1 1 1 2 1h0c1 2 2 2 3 3 0 1 1 1 1 1l-1 1c0 1 0 1 1 2 1 0 2 0 2 1v1l-3 3c0 1-1 1-2 1 0 0-1 1-1 2-1 0-1 0-2-1v1h1l-1 2h-1l1 1v1c-1 1-1 1-1 2h0v1s-1 1-1 2c-1 1 0 1-1 2-2-1-2-1-3 0v2 2h0c-1 1-1 1-1 2-2-1-2-3-3-3l-2 2v-1l-3-4h1c1-1 2-1 2-3 1-1 2-3 1-4v-2h-1l-1 1c-1-1-3-2-5-2h-2l-1-1c0 1 0 2-1 2s-1 0-2-1h-1c-1-1-2-2-2-3s1-2 2-3l1 1h1c0-1 1-2 2-3l-1-1-2-3c-2 0-3 1-5 2l-1 1h0-1c-1-2-2-3-3-4l1-1c1 0 2 1 2 1l1 1c2 0 3-1 4-1 3-5 6-8 10-12h1c-1-5 2-8 5-13 0-1 1-1 2-2l3-2 2-1c1 0 3 0 4-1s0-1 0-2 1-2 1-3z" class="Z"></path><path d="M287 716c2-1 2-1 4 0h1l1 1 2 2 2 1-1 1v1l-1-1-2 1c0-1-1-2-1-2-1 0-3-1-3-2-1-1 0-1-2-2z" class="K"></path><path d="M281 719c3 0 4 0 7 2v1s1 1 1 2v1c-1 1-2 1-3 1l-2-3-1-1-2-3z" class="J"></path><path d="M285 715v-1c-1 1-1 2-2 2 0-2 3-5 5-7 1-1 4 0 5 0 1 1 1 3 1 4v1c0 1 0 1-1 2h-1-1c-2-1-2-1-4 0l-2-1z" class="Q"></path><path d="M285 715c1-2 3-3 3-5l1-1 1 1c-1 1-1 2-2 3h0l1 1s1-1 2-1h2 1v1c0 1 0 1-1 2h-1-1c-2-1-2-1-4 0l-2-1z" class="E"></path><path d="M284 723l2 3c1 0 2 0 3-1v-1c1 1 0 2 0 3l2 2c1 1 2 2 2 3l-1 1c-1-1-3-2-5-2h-2l-1-1c0 1 0 2-1 2s-1 0-2-1h-1c-1-1-2-2-2-3s1-2 2-3l1 1h1c0-1 1-2 2-3z" class="I"></path><path d="M284 723l2 3c-2 0-2 1-4 0 0-1 1-2 2-3z" class="Q"></path><path d="M281 731v-3h1 2v2c0 1 0 2-1 2s-1 0-2-1z" class="P"></path><path d="M287 731v-2l1-1 3 1c1 1 2 2 2 3l-1 1c-1-1-3-2-5-2z" class="F"></path><path d="M294 732l2-2c1 2 3 2 6 2 0 0 1 0 2-1v1l1 1v1c-1 1-1 1-1 2h0v1s-1 1-1 2c-1 1 0 1-1 2-2-1-2-1-3 0v2 2h0c-1 1-1 1-1 2-2-1-2-3-3-3l-2 2v-1l-3-4h1c1-1 2-1 2-3 1-1 2-3 1-4v-2z" class="n"></path><path d="M304 736h0v1s-1 1-1 2c-1 1 0 1-1 2-2-1-2-1-3 0v2 2h0c-1 1-1 1-1 2-2-1-2-3-3-3l-2 2v-1c0-1 0-2-1-3 4-4 7-5 12-6z" class="V"></path><path d="M291 704c2 1 3 0 5 0l3 3v1h1 1v1c0 1 0 1 1 2l1 1-1 2h0 2 1c1 1 1 1 2 1h0c1 2 2 2 3 3 0 1 1 1 1 1l-1 1c0 1 0 1 1 2 1 0 2 0 2 1v1l-3 3c0 1-1 1-2 1 0 0-1 1-1 2-1 0-1 0-2-1v1c-2-1-2-2-4-2l-1 1c-3 0-3-2-5-4l1-3v-1l1-1-2-1-2-2-1-1h1c1-1 1-1 1-2v-1c0-1 0-3-1-4 1-2-1-2-2-3v-2z" class="h"></path><path d="M296 713l3-2 1 1 2-1 1 1-1 2h0l-3 4h-1c-2-1-3-1-5-1l-1-1h1c1-1 1-1 1-2l2 1v-1-1z" class="I"></path><path d="M296 713l3-2 1 1 2-1 1 1-1 2h-1-4l-1-1z" class="C"></path><path d="M291 704c2 1 3 0 5 0l3 3v1h1 1v1c0 1 0 1 1 2l-2 1-1-1-3 2v1 1l-2-1v-1c0-1 0-3-1-4 1-2-1-2-2-3v-2z" class="D"></path><path d="M304 714h1c1 1 1 1 2 1h0c1 2 2 2 3 3 0 1 1 1 1 1l-1 1c-1 1-1 1-2 0h-1c-1 1-2 2-4 3h-2-1c-1 0-1 0-1-1v-1l2-3 3-4z" class="K"></path><path d="M304 714h1c1 1 1 1 2 1-1 2-2 3-3 4h-1-1l-1-1 3-4z" class="B"></path><path d="M307 682c1 1 1 2 2 3 1 2 1 2 4 2 0 1 1 1 1 2h0c0 1 1 2 1 2 2 1 2 2 2 4-2 0-2 0-3 2 0 1 0 2 1 3-1 1-1 2-1 3v2c-1 0-2 0-2-1l-2 1v3 1l-1 1h-2c-2-1-3-1-6-2h-1-1v-1l-3-3c-2 0-3 1-5 0 0-2 1-4 2-5l1-1c1-1 2-3 3-4l3-4v-1l2-1c1 0 3 0 4-1s0-1 0-2 1-2 1-3z" class="s"></path><path d="M297 694l2 2v1c-1 1-3 0-4 0l-1 1c1-1 2-3 3-4z" class="B"></path><path d="M293 699c1 0 2 1 2 2h1v1h2c-1 2-1 2-2 2-2 0-3 1-5 0 0-2 1-4 2-5z" class="o"></path><path d="M307 682c1 1 1 2 2 3 1 2 1 2 4 2 0 1 1 1 1 2h0c-3 0-4 0-6 2-1 0-1-1-2-2-2 0-4 0-6 1v-1l2-1c1 0 3 0 4-1s0-1 0-2 1-2 1-3z" class="P"></path><path d="M298 702c0-1 1-1 2-3l1 1h1c1 1 2 3 3 4 2 1 3 0 4 1h1v3 1l-1 1h-2c-2-1-3-1-6-2h-1-1v-1l-3-3c1 0 1 0 2-2z" class="C"></path><path d="M299 707v-2-1c1 0 1 0 1 1l1 1c1 1 2 2 3 2l1-2 2-1v1c0 1 1 2 1 3h2l-1 1h-2c-2-1-3-1-6-2h-1-1v-1z" class="Q"></path><path d="M301 700h1c1 1 2 3 3 4 2 1 3 0 4 1h1v3 1h-2c0-1-1-2-1-3v-1l-2 1c-2-1-1-2-1-2-1-1-2 0-3-1v-3z" class="F"></path><path d="M771 644c1-1 1-2 2-2h4l4 1c2-3 4-5 8-5 3 0 6 3 7 5 1 0 1 0 2 1l1 3c1 1 2 1 2 2s1 2 1 4c1 0 2 1 3 1 1 1 3 3 3 5v1c0 2 1 3 1 4l-2 2-1 1v3 1 2h-1v1h-3c-1 0-2 3-3 2v-1c-1-2-1-2-1-4h-2c-1 1-2 2-4 2h-3l-2 1-2 1c-1 0-3 0-4 1l-1 1v-1l1-3-3 1v-1l-2 1v-2l-1 1-1 1h-2-1c-1 0-1 1-2 1v-1c-1-2-1-2-2-2-1 1-2 1-4 2h0c0-2-1-2-1-4l-1-1c-1 1-1 2-2 3h0-1c0-2 0-2-1-4l3-3c1 0 2 0 3-1l2-1c1-1 2-3 2-5 1-1 2-2 2-3s0-2-1-3c-2-2-5-2-8-2l-1-1h5l6 3 1 1c0-1 0-2 1-2h0c1 1 0 1 1 1 0-2 1-2 1-3l2 1c0-1 0-1 1-2h0 1l1-2v-2c-2 0-3-1-4-1h-2c-1 1 0 2-2 1z" class="K"></path><path d="M780 655l3 3h0c-1 2-2 4-3 5l-2 2h-1v-3c0-2 2-3 2-5 0-1 0-1 1-2z" class="E"></path><path d="M763 664l2-1c1-1 2-3 2-5 2 0 3 0 5 1 0 1 0 1-1 2v2 1h-2 0-1l-1-1-1 1 1 1h-2l-1 1h0c-1 0-1-1-1-2z" class="D"></path><path d="M780 655c1-2 3-5 5-7h-1s-1 1-2 1c-1-1-2-1-2-2v-1l1-1c1-2 5-5 7-6 1 0 4 2 5 2 1 1 1 2 2 3v2l3-2 1 3v1c1 2 1 2 1 5-1 0-2-2-4 0l-1 1c-1 1-1 1-3 2h-1 0c-1 0-2 0-2-1l-2-2c-2 1-3 2-4 4v1l-3-3z" class="o"></path><path d="M786 650c1-1 0-1 1-2h1c0-1-1-1-1-2h0l2-3h2v1c-1 0-1 0-2 1v1c1 1 2 2 3 2h1c-2 1-2 4-4 4l-2-2h-1z" class="E"></path><path d="M786 650h1l2 2c2 0 2-3 4-4 0 3-2 4-4 6h1l1 1v1c-1 0-2 0-2-1l-2-2c-2 1-3 2-4 4h0v-4h1c1-1 2-2 2-3z" class="I"></path><path d="M799 647c1 1 2 1 2 2s1 2 1 4c1 0 2 1 3 1 1 1 3 3 3 5v1c0 2 1 3 1 4l-2 2-1 1v3 1 2h-1v1h-3c-1 0-2 3-3 2v-1c-1-2-1-2-1-4h-2c-1 1-2 2-4 2h-3l-2 1-2 1c-1 0-3 0-4 1l-1 1v-1l1-3-3 1v-1-3c-1 0-2-1-2-1l1-1v1c1-1 2-1 2-2 0-2 1-3 1-4 1-1 2-3 3-5h0v-1c1-2 2-3 4-4l2 2c0 1 1 1 2 1h0 1c2-1 2-1 3-2l1-1c2-2 3 0 4 0 0-3 0-3-1-5v-1z" class="o"></path><path d="M783 657c1-2 2-3 4-4l2 2c0 1 1 1 2 1h0l-1 2h0c1 1 1 1 1 2 2 1 3 1 5 1l1-1h3l1 1-3 2c-2 1-2 2-3 3v3c-1 0-2 1-2 1-2 0-2 0-3-1 0-1 1-1 1-2l-2-2c-1 0-1 0-1-1 1-1 2 0 3 0 1-1 1-1 0-2h-1l-1-1c-2 0-2 1-3 2v1c-1 1-3 2-4 2-2-3 1-4 2-6l-1-2h0v-1z" class="s"></path><path d="M799 647c1 1 2 1 2 2s1 2 1 4c1 0 2 1 3 1 1 1 3 3 3 5v1c0 2 1 3 1 4l-2 2-1 1v3 1 2h-1v1h-3c-1 0-2 3-3 2v-1c-1-2-1-2-1-4h-2c-1 1-2 2-4 2h-3c3-2 5-2 7-5l-1-2c1-1 1-2 3-3l3-2-1-1h-3l-1 1c-2 0-3 0-5-1 0-1 0-1-1-2h0l1-2h1c2-1 2-1 3-2l1-1c2-2 3 0 4 0 0-3 0-3-1-5v-1z" class="H"></path><path d="M797 660c1 0 2-1 3-1h1c0 1 0 1 1 1s2 1 3 2h1v2h0c-2 0-2 1-3 2h-1-3l-2 2-1-1c2-2 2-3 5-3v-1h1c0-2 0-1-1-2l-1-1h-3z" class="o"></path><path d="M805 667h1v3 1 2h-1v1h-3c-1 0-2 3-3 2v-1c-1-2-1-2-1-4 1 0 1 0 2-1 2-1 3-2 5-3z" class="G"></path><path d="M798 671c1 0 1 0 2-1 2-1 3-2 5-3-2 3-4 6-6 8-1-2-1-2-1-4z" class="D"></path><path d="M262 824h2l1-2 3 3h1c1 1 3 1 4 2 2 2 4 3 6 4 1 1 1 1 2 1s0 0 1 1h2l2 1h2 0l1 1h2 1c0 1 1 1 2 2h1c1 2 2 4 4 4h3c1 1-1 2 0 3l-3-1v2l-4-2v4c-1 2-2 3-3 4h0l6 2c3 1 7 2 10 3l2 2c2 2 3 4 4 7 0 1 0 3 1 4l3 3c1 1 1 2 0 3v1c1 1 1 0 1 1-2 2-9 0-9 3-2-1-4-2-5-3-1 0-2-1-2-1-1 0-2 0-3 1h-5 0l-3-1c-1-1-2-2-3-4s-3-4-5-6h2v-2l2-2h0s-1-1-1-2l-2 1v-2-1c-1-2-4-4-6-5-1-1-3-2-4-3l-1-1c0-2 0-3-1-4l1-2c-1-2-3-3-5-4v-1-1h-3l-3-3c-1 0-2 0-4 1h0l-1-3c-1-1-2-3-4-4h0c2-1 2-1 3-2l-2-1c1-1 2-1 3-1h1 0 3z" class="d"></path><path d="M288 862c0 1 1 1 1 1 0 3 1 4 2 7h0l-2 2c-1-2-3-4-5-6h2v-2l2-2h0z" class="S"></path><path d="M292 870l1-1h1v1c1 1 1 1 2 0l1 1v1h1l1-1 2 1 1 1h1c-2 0-2 1-4 1-2 1-2 0-4 0 0 0-1 1-2 1l-1 1c-1-1-2-2-3-4l2-2h1z" class="g"></path><path d="M292 870l1 1c0 1 1 1 1 1-1 1-1 0-2 2h1v1l-1 1c-1-1-2-2-3-4l2-2h1z" class="M"></path><path d="M293 875c1 0 2-1 2-1 2 0 2 1 4 0 2 0 2-1 4-1 2 1 3 2 5 2 1 1 2 2 4 2s4 0 6-2v1c1 1 1 0 1 1-2 2-9 0-9 3-2-1-4-2-5-3-1 0-2-1-2-1-1 0-2 0-3 1h-5 0l-3-1 1-1z" class="W"></path><path d="M292 851l6 2c3 1 7 2 10 3l2 2c2 2 3 4 4 7 0 1 0 3 1 4l3 3v1c-2 0-4-1-5-1l-1-1-2 2c0-1 0 0 1-1v-1h-1l-1 1-1-1c1-1 1 0 0-1l-2 2-1-1 1-1h-1c-1 1-1 1-2 1v-2c-1 1-1 0-1 1h-1l-1-1-1 1v-2c-1-1-1-1-3-1l-1-1c0 1-1 1-2 1 1-1 1-1 1-2h-2c0-1 0-1-1-2v-1c0-1-1-2-1-2v-2l-1 1v-2l-1-1 1-1h1c-1-1-1-2-1-3 1 0 2 0 3-1h0z" class="N"></path><path d="M294 862v-1h-2c0-2 0-2-1-3 0-1 0-1 1-1l-2-2 1-1 1 1c1 0 1 0 2-1v1 1l2 1c-1 1-1 2-3 3h1 1l1 1-2 1z" class="Z"></path><path d="M294 862l2-1-1-1h-1-1c2-1 2-2 3-3l-2-1v-1h4c1 1 3 1 4 2 0 0-1 1-2 1s-2-1-4-1c1 2 1 2 3 3h0c0 1 0 1-1 2-1 0 0 0-1 1h1c1 1 1 0 1 1-1-1-1 0-3 0v-1h-2v-1z" class="R"></path><path d="M302 857c1 0 2 1 2 1 1 1 2 0 3 1s2 1 3 2l1 2 1 1 1 1c0 2 0 2-1 3-2-1-3 0-4-1l-2 1h0l-1-2c-1 1-1 1-2 1v-1h0-2 0l1-2c-1 0-2 1-3 1v-1c0-1 0 0-1-1h-1c1-1 0-1 1-1 1-1 1-1 1-2h0c-2-1-2-1-3-3 2 0 3 1 4 1s2-1 2-1z" class="O"></path><path d="M305 861c3 0 4 1 6 2l1 1-1 2v1l-2-2-1-1c-1-1-2-1-3-3z" class="B"></path><path d="M309 865h-3l-1-1c-1 0-1 0-2-1h0-2v-2-1l3 1v1l1-1c1 2 2 2 3 3l1 1z" class="P"></path><path d="M262 824h2l1-2 3 3h1c1 1 3 1 4 2 2 2 4 3 6 4 1 1 1 1 2 1s0 0 1 1h2l2 1h2 0l1 1h2 1c0 1 1 1 2 2h1c1 2 2 4 4 4h3c1 1-1 2 0 3l-3-1v2l-4-2v4c-1 2-2 3-3 4h0 0c-1 1-2 1-3 1 0 1 0 2 1 3h-1l-1 1h-2l-2-2v-3h-1v-1c-1-1-2-2-2-3h0c-1 0-2 1-2 1v-2c0-1-1-2-2-3-2-1-4-3-6-5h-2v-1h-3l-3-3c-1 0-2 0-4 1h0l-1-3c-1-1-2-3-4-4h0c2-1 2-1 3-2l-2-1c1-1 2-1 3-1h1 0 3z" class="O"></path><path d="M268 826l6 3c-1 1-2 1-2 1-2 0-4-1-5 0l-1-1 1-2 1-1z" class="Z"></path><path d="M269 834c0-1 0-1 1-2h0 2v1l1 1h1 1l1 1h3l-1 2 1 1 1-1v1 1c-2-1-3 0-5-1v-1c-1-1-3-1-4-3v-1l-2 1z" class="L"></path><path d="M280 840l3-3 1 1h-1l-1 1 1 1h2v1c-1 0-1 0-2 2h1l1-1 1 1s0 1 1 1h1l1 3c1 0 1-1 3-1v-2h1c0 2 0 2 2 2l-1 1h-2-2c0 1 0 2-1 3v-1c0-1 0-1-1-2-2 0-1 0-2-1l-2-1-1-1h-1c-1 0-1 0-2-1h0v-3z" class="a"></path><path d="M268 825h1c1 1 3 1 4 2 2 2 4 3 6 4 1 1 1 1 2 1s0 0 1 1h2l2 1h2 0l1 1h2 1c0 1 1 1 2 2h1c1 2 2 4 4 4h3c1 1-1 2 0 3l-3-1v2l-4-2v4h-1l1-1v-2c-1-2-2-3-3-4l-1-1c-2-1-5-2-7-4-4-1-7-3-10-6l-6-3h0v-1z" class="S"></path><path d="M262 824h2l1-2 3 3v1h0l-1 1-1 2 1 1 1 2h0c1-1 1-1 2-1v1c-1 1-1 1-1 2l2-1v1c1 2 3 2 4 3v1c2 1 3 0 5 1v1 3h0c1 1 1 1 2 1h1l1 1 2 1c1 1 0 1 2 1 1 1 1 1 1 2v1c1-1 1-2 1-3h2 2 1c-1 2-2 3-3 4h0 0c-1 1-2 1-3 1 0 1 0 2 1 3h-1l-1 1h-2l-2-2v-3h-1v-1c-1-1-2-2-2-3h0c-1 0-2 1-2 1v-2c0-1-1-2-2-3-2-1-4-3-6-5h-2v-1h-3l-3-3c-1 0-2 0-4 1h0l-1-3c-1-1-2-3-4-4h0c2-1 2-1 3-2l-2-1c1-1 2-1 3-1h1 0 3z" class="Y"></path><path d="M269 834l2-1v1c1 2 3 2 4 3v1c2 1 3 0 5 1v1 3h-1l-2-1v-1-1c-1-1-1-1-2-1h-1c-2-1-4-3-5-5z" class="T"></path><path d="M262 831c-2-2-2-2-3-4 1-1 2-1 3-1h-1v1c1 1 2 2 3 4l3 3 2 3h-3l-3-3c0-1-1-2-1-3z" class="i"></path><path d="M262 824h2l1-2 3 3v1c-2-1-4-1-6 0-1 0-2 0-3 1 1 2 1 2 3 4 0 1 1 2 1 3-1 0-2 0-4 1h0l-1-3c-1-1-2-3-4-4h0c2-1 2-1 3-2l-2-1c1-1 2-1 3-1h1 0 3z" class="X"></path><path d="M262 831c0 1 1 2 1 3-1 0-2 0-4 1h0l-1-3 4-1z" class="M"></path><defs><linearGradient id="G" x1="678.265" y1="886.877" x2="689.137" y2="907.439" xlink:href="#B"><stop offset="0" stop-color="#a09e9b"></stop><stop offset="1" stop-color="#e3dedb"></stop></linearGradient></defs><path fill="url(#G)" d="M692 876c2-1 4-2 5-3 4-2 9-3 12-6l2 2 2-1c-1 1-1 2-2 3 1 0 1 0 2-1s2 0 3 0l1-1c1 0 1 1 2 1h1c0-1 1-1 2-1l-2 6c-1 2-1 3-3 4l-4 1v1c-2 0-5 0-7 3 4-1 9-3 13-3l10 1h0-5c1 0 1 0 2 1h2c1 0 2 0 3 1v1h4 1-1-1c0 1-1 2-1 2v1h1 1c2 1 5 0 8 0h7l10 1h0c-2 2-7 0-9 1-2 0-5 1-7 0h-15-1-7c-3 1-8 0-10 1h-3l-1 1h4 2 16c2 1 12-1 13 1h-3l1 1h-2v1h-16c-3-1-11-1-12 0-2 0-2 1-3 0h0l-1 1c-1 0-2 1-4 1l-1 1v1h29-1c-3 2-12 0-13 1-6 0-13 1-18 0h0c-2-1-7-1-10-1-6 0-13 1-19 1-4 1-11 1-15 2-1 1-2 2-4 2-3 1-7 2-10 2h0l3-1c2-2 4-4 4-6 2 0 3-1 5-1l5-1c3-2 6-3 9-3-1-1-2-1-3-2 4-1 7-1 11-3l6-4 1 1c1-1 1-1 3-2l1-2 2-2 5-4z"></path><path d="M671 897l9-1h0l1 1h-3l1 1h0-9v-1h1z" class="D"></path><path d="M673 894h4l-6 3h-1c-2 0-2 1-4-1l7-2z" class="G"></path><path d="M647 899c2 0 3-1 5-1l5-1h5c-3 1-6 3-9 3-2 0-3 2-4 2-2 2-4 2-6 3 2-2 4-4 4-6z" class="q"></path><path d="M666 894l5-1c4-2 7-5 11-6l12-2h0l-1 1v2h-1 1l1 1 1 1c-2 0-3 0-5 2-4 0-9 1-13 2h0-4l-7 2-4 1h-5c3-2 6-3 9-3z" class="r"></path><path d="M692 888h1l1 1 1 1c-2 0-3 0-5 2-4 0-9 1-13 2h0-4c3-1 4-2 7-2 4-2 7-4 12-4z" class="a"></path><defs><linearGradient id="H" x1="708.98" y1="887.142" x2="734.02" y2="886.874" xlink:href="#B"><stop offset="0" stop-color="#adaba8"></stop><stop offset="1" stop-color="#d7d4d1"></stop></linearGradient></defs><path fill="url(#H)" d="M731 885h4 1-1-1c0 1-1 2-1 2v1h1 1c2 1 5 0 8 0h7l10 1h0c-2 2-7 0-9 1-2 0-5 1-7 0-5-1-12-1-17-1-12 1-25 1-37 3 2-2 3-2 5-2 3-1 6-1 9-1l10-2c6-1 11-1 17-2z"></path><defs><linearGradient id="I" x1="703.179" y1="877.507" x2="719.316" y2="893.978" xlink:href="#B"><stop offset="0" stop-color="#888583"></stop><stop offset="1" stop-color="#bdbbb4"></stop></linearGradient></defs><path fill="url(#I)" d="M706 884c4-1 9-3 13-3l10 1h0-5c1 0 1 0 2 1h2c1 0 2 0 3 1v1c-6 1-11 1-17 2l-10 2c-3 0-6 0-9 1l-1-1-1-1h-1 1v-2l1-1v1c3 1 9-1 12-2z"></path><path d="M692 876c2-1 4-2 5-3 4-2 9-3 12-6l2 2 2-1c-1 1-1 2-2 3 1 0 1 0 2-1s2 0 3 0l1-1c1 0 1 1 2 1h1c0-1 1-1 2-1l-2 6c-1 2-1 3-3 4l-4 1v1c-2 0-5 0-7 3-3 1-9 3-12 2v-1h0l-12 2c-4 1-7 4-11 6l-5 1c-1-1-2-1-3-2 4-1 7-1 11-3l6-4 1 1c1-1 1-1 3-2l1-2 2-2 5-4z" class="T"></path><path d="M711 871c1 0 1 0 2-1s2 0 3 0h-1c-1 1-5 5-6 5h-1c-1 2-3 2-5 3h-2c-1 1-2 1-3 2-3 0-5 2-8 2l-1 1c-1 1-3 2-5 1h0l1-2 2-2h3l1 1 1-1c1-1 3-3 6-2v-1c4 0 4-1 6-3 1 0 1 0 2-1h2v-1l1-1 1 1 1-1z" class="U"></path><path d="M716 870l1-1c1 0 1 1 2 1h1c0-1 1-1 2-1l-2 6c-1 2-1 3-3 4l-4 1-7 1v-3h0l-2 1c-1 0-1 1-2 1h-4c1-1 2-1 3-2h2c2-1 4-1 5-3h1c1 0 5-4 6-5h1z" class="X"></path><path d="M720 870c0 1-1 2-1 3s-2 3-2 3c-2 1-6 3-8 3 3-3 6-6 10-9h1z" class="T"></path><path d="M698 880h4c1 0 1-1 2-1l2-1h0v3l7-1v1c-2 0-5 0-7 3-3 1-9 3-12 2v-1h0l-12 2c-4 1-7 4-11 6l-5 1c-1-1-2-1-3-2 4-1 7-1 11-3l6-4 1 1c1-1 1-1 3-2h0c2 1 4 0 5-1l1-1c3 0 5-2 8-2z" class="m"></path><path d="M706 881l7-1v1c-2 0-5 0-7 3-3 1-9 3-12 2v-1h0l12-4z" class="N"></path><path d="M197 741c2-2 3-5 6-6 2-2 5-1 8 0h0c1-1 3-4 3-6 1-1 0-3 0-4v-3c0 1 0 1 1 1l1-4c1 1 1 1 2 1v-1c-1-1-2-1-3-2 0-2 0-4 1-6l1-1c1 1 2 1 3 1 0-4 1-8 2-13 2 3 3 5 3 8 1 3 1 10 3 12 3 0 3-1 6-1 4-2 6 4 10 5 4 2 7-4 11-6 2-1 5-1 7 0 1 0 2 1 3 2l2-2c2 0 3 0 5 1l-1 1c1 1 2 2 3 4h1 0l1-1c2-1 3-2 5-2l2 3 1 1c-1 1-2 2-2 3h-1l-1-1c-1 1-2 2-2 3s1 2 2 3h1c-1 2-2 1-3 2-1 2-2 2-4 3l-1-1h-2-2c-2 1-3-1-5-2-1 0-3-1-4-2-2 0-3 1-5 0v-1l-2 1h-1v1c-1 0-1 2-2 2-3 0-6 1-9-1h-1l-1-1h-2c-1-1-1-1-2-1l-2-1-3-3h-2c3 3 6 4 9 6l-2 3c1 0 1 1 2 2h0l2 2v4l-1 1h0l-2 1-3 1h-4c0 1-1 2 0 4 1 1 3 2 4 3 2 1 3 2 5 3v2c1 1 1 1 0 2-2 0-2-2-4-2h-1c0-1-1-2-1-3h-1c-2 0-3-1-4-2h-3c-1-1-1-2-2-2h-1c1-1 0-2 0-3-1 1-2 2-4 2l-2-1v1 2c-1-1-2-1-3-2v-1c-1-1-3-2-4-3-2-1-5-4-7-5-1 0-1-1-2-2l-1 1h-1z" class="u"></path><path d="M222 701l1 2c2 4 3 8 2 12l-3-3c-1-4 0-7 0-11z" class="F"></path><path d="M220 731h0c0-1 0-2-1-3v-1s1-1 2-1h0 2c1 0 1-1 2-2l-2-4 1-1c-1-1-1-1-1-2h2 0l1 1 1 1 1 1h1c1 0 0 0 1 1v-1l2-1c1-1 2-1 3-2 1 0 2 1 3 1 2 3 5 5 7 7h1l2 2c1 0 1-1 2-2h2l-1 4v1l1 1v1c-1 0-1 2-2 2-3 0-6 1-9-1h-1l-1-1h-2c-1-1-1-1-2-1l-2-1-3-3h-2l-1-1h-2v2 2h-1-1c-1-1-1-2-3-2v1h1c1 1 2 2 2 3h0c-1 0-2 0-3-1z" class="G"></path><path d="M245 725h1l2 2c1 0 1-1 2-2h2l-1 4v1h-3l-2-2h-1c0 1 1 2 1 3l-1 1c-2-1-1-2-2-2 0-1-1-1 0-3h2v-2z" class="B"></path><path d="M232 719c1-1 2-1 3-2 1 0 2 1 3 1-1 2-1 2-1 3l1 1 1 1c-2 1-3 1-4 1h-1v-1c-1 1-2 2-2 3l-2-2c1-1 1-1 2-1 0-2 0-2-1-3l1-1z" class="J"></path><path d="M238 718c2 3 5 5 7 7v2h-2c-1 2 0 2 0 3l-1 1h-1c-1-1-1-2-2-3l-3-3v2h-1-1v-3h1c1 0 2 0 4-1l-1-1-1-1c0-1 0-1 1-3z" class="F"></path><path d="M252 720v-1h1l1-1c1-1 4-2 5-2s3 1 4 2h2c1 0 2-1 4-1l2 1c1 1 2 2 3 4h1 0l1-1c2-1 3-2 5-2l2 3 1 1c-1 1-2 2-2 3h-1l-1-1c-1 1-2 2-2 3s1 2 2 3h1c-1 2-2 1-3 2-1 2-2 2-4 3l-1-1h-2-2c-2 1-3-1-5-2-1 0-3-1-4-2-2 0-3 1-5 0v-1l-2 1h-1l-1-1v-1l1-4h-2c-1 1-1 2-2 2l-2-2 6-5z" class="H"></path><path d="M280 731h1c-1 2-2 1-3 2-1 2-2 2-4 3l-1-1h-2-2c-2 1-3-1-5-2h0c1-1 1-1 2-1l1 1 2-1c1 1 1 1 2 1v1c1 0 2-1 3-1 2-2 4 0 6-2z" class="C"></path><path d="M255 726h1l2 2h2v1l-1 1 1 1c-2 0-3 1-5 0v-1l-2 1h-1l-1-1v-1l1-4c1 1 2 1 3 1z" class="I"></path><path d="M252 725c1 1 2 1 3 1l-1 3h-3l1-4z" class="E"></path><path d="M252 720v-1h1l1-1c1-1 4-2 5-2s3 1 4 2h2c1 0 2-1 4-1l2 1c1 1 2 2 3 4h1 0l1-1h1 2c1 0 0 0 1 1v1c-1 1-1 2-2 3l-1-1h-2c-1 0-1-1-2-1v1l2 2c1 1 1 2 1 3-3 0-4-1-5-3-1-1-1-2-2-3h-1v2c-1 0-1 1-2 2h-1-3l-6-6c-1 0-2 0-2 1h-1-1v-3z" class="J"></path><path d="M220 731c1 1 2 1 3 1h0c0-1-1-2-2-3h-1v-1c2 0 2 1 3 2h1 1v-2-2h2l1 1c3 3 6 4 9 6l-2 3c1 0 1 1 2 2h0l2 2v4l-1 1h0l-2 1-3 1h-4c0 1-1 2 0 4 1 1 3 2 4 3 2 1 3 2 5 3v2c1 1 1 1 0 2-2 0-2-2-4-2h-1c0-1-1-2-1-3h-1c-2 0-3-1-4-2h-3c-1-1-1-2-2-2h-1c1-1 0-2 0-3-1 1-2 2-4 2l-2-1v1 2c-1-1-2-1-3-2v-1c-1-1-3-2-4-3-2-1-5-4-7-5-1 0-1-1-2-2l1-1c0-1 1-2 2-3 2-1 4-1 6-1l6 2c2 1 5 2 7 2h1c-2-1-3-2-5-2 1-1 1-2 1-2l-1 1-1-1c-1 1-1 1-2 1v-1c1 0 1-1 2-1 1-1 1-1 1-2l-1-1s1-1 2-1l2 1z" class="D"></path><path d="M220 741l1-1h4v2l-2 2-3-3z" class="E"></path><path d="M227 735c2 0 3 0 5 1l1 2 1-1c1 0 1 1 2 1h1 0l2 2v4l-1 1h0l-2 1-1-2c-1-1-2-1-2-2 1-1 2 1 4 1h0c-1-1-1-2-2-2-2-1-3-2-3-2-1-1-2-2-3-2s-2-1-2-1v-1z" class="K"></path><path d="M200 739c0-1 1-2 2-3 2-1 4-1 6-1 0 1 0 2-1 3 0 1 0 1 1 2 1 0 1 1 1 2l-3 2h-1v-2c-1-1-4-2-5-3z" class="V"></path><path d="M227 726l1 1c3 3 6 4 9 6l-2 3c1 0 1 1 2 2h-1c-1 0-1-1-2-1l-1 1-1-2c-2-1-3-1-5-1-1-1-1-1-1-2 0-2 0-2 1-3v-1h0c-1-2-1-2 0-3z" class="I"></path><path d="M199 740l1-1c1 1 4 2 5 3v2h1 2v1c0 1 1 1 2 2l1-1c1-1 2-1 3-2h1l-1-2h1l2 1 1-1h1v-1-1l1 1 3 3-2 1v4h0c-1 1-2 2-4 2l-2-1v1 2c-1-1-2-1-3-2v-1c-1-1-3-2-4-3-2-1-5-4-7-5-1 0-1-1-2-2z" class="G"></path><path d="M225 740h3c2 1 3 1 5 2 0 1 1 1 2 2l1 2-3 1h-4c0 1-1 2 0 4 1 1 3 2 4 3 2 1 3 2 5 3v2c1 1 1 1 0 2-2 0-2-2-4-2h-1c0-1-1-2-1-3h-1c-2 0-3-1-4-2h-3c-1-1-1-2-2-2h-1c1-1 0-2 0-3h0v-4l2-1 2-2v-2z" class="B"></path><path d="M233 747c-1-1-2-2-4-3l-1-1 1-1c2 0 1 0 3 1 0 1 2 1 3 1l1 2-3 1z" class="F"></path><path d="M227 754v-3c-1-1-2-1-3-1-1-2 0-3 0-5 1-1 1-1 3-1v1l-2 2 1 1c2-1 1-2 3-2v1c0 1-1 2 0 4 1 1 3 2 4 3 2 1 3 2 5 3v2c1 1 1 1 0 2-2 0-2-2-4-2h-1c0-1-1-2-1-3h-1c-2 0-3-1-4-2z" class="E"></path><path d="M295 837l2-1c1 1 0 1 1 2 1 0 1 0 2-1h0v2h1l1-1c1 1 0 0 0 1h1l3 1h2l10 3 4 2c1 1 4 2 6 3h2c1 1 2 3 2 4 1 0 1 1 2 1 2 1 9 5 9 7l3 1c1 1 4 3 5 4h1l1 1h-2v1c1 1 1 1 3 1 0 1 1 2 1 3l-1 1c-1 0-2 0-2-1l-1 1h-3c1 1 1 2 2 2 1 1 2 1 2 2l1-1 2 2 1-1 1 1h0 3c1 0 3 0 5-1h2 1 2 0l2 3h-3v1h1c1 0 1 1 2 1h1v2l1-1c1 1 1 0 1 1 1 0 4 1 5 1 3 2 6 1 9 3h0 2l1 1v-1l2 1v1 2l1-1v2c2 0 2 0 4-1h3v1c1-1 2-1 4 0h0c-3 2-5 4-8 6l-1 1c-1 1-3 2-5 1h0l-1 1c-1 1-2 1-3 3h-2l-3-2h-2l-1-1v-2-2c-1 0-2-1-3-1h-2-4 0-2v-2l-2-1v1c1 2 1 2 1 4l-1-1c-2 0-4 0-6-1-2 2-4 1-7 2-2 1-7 1-10 0h0c-4 0-7-1-10-4l-2-2v-6h-1 0c-4-1-8-3-12-4-3-1-6-1-9-2 0-3 7-1 9-3 0-1 0 0-1-1v-1c1-1 1-2 0-3l-3-3c-1-1-1-3-1-4-1-3-2-5-4-7l-2-2c-3-1-7-2-10-3l-6-2h0c1-1 2-2 3-4v-4l4 2v-2l3 1c-1-1 1-2 0-3h-3c-2 0-3-2-4-4z" class="f"></path><path d="M336 891l-1-1c-1 0-1 0-2-1l1-2c1-1 3-1 5-1 0 1 0 2-1 3h-1v2h-1z" class="Y"></path><path d="M308 856h1c1 1 2 2 3 2h2c1 1 3 2 4 3-1 2-2 2-4 4-1-3-2-5-4-7l-2-2z" class="m"></path><path d="M346 878h0 1 0 2l1 1h1 4v1c1 0 2 1 2 1 1 0 2-1 3-2 0 1 1 1 1 2-2 1-2 1-4 1-2-1-3-1-5-1 0 0-1-1-1-2-1 1-1 1-2 1s-1-1-2-1l-3 3c-1-1-2-1-3-2l1-2c1 1 2 1 3 0h1zm-51-31v-4l4 2h1c0 2 0 2-1 3l-1 2c-1-1-1 0-1-1h-2v1l3 3-6-2h0c1-1 2-2 3-4z" class="W"></path><path d="M297 849l-1-2h1 1l1 1-1 2c-1-1-1 0-1-1z" class="X"></path><path d="M324 868v1l-1-1c1-1 1-2 2-3l-1-1c-1 1-1 2-2 3h-1l2-6c2 1 4 2 5 4l4 3h-1c-1 1-2 3-3 2h-2c1-1 1-1 1-2v-1c-2 1-2 1-3 1z" class="Y"></path><path d="M344 876l2-2h1v1c1 1 1 1 2 0h1l2 1 1-1 2 2 1-1 1 1h0 3c1 0 3 0 5-1h2l-5 2 1 2-2 1c0-1-1-1-1-2-1 1-2 2-3 2 0 0-1-1-2-1v-1h-4-1l-1-1h-2 0-1 0l-2-2z" class="M"></path><path d="M299 845v-2l3 1 2-1v2h2c0 1 0 2 1 2l1 1c1 0 1 0 1 1v1c1 0 2-1 2-1h1l-1 3h1l2-2c1 1 1 1 3 2l-1 1 1 1 2-1 1 1h0 1c0 1 0 1 1 2-1 1-1 1-2 1-2-2-2 0-4-1-1 0-3-2-4-3h-1l-2-2c-4-1-3-2-4-5h-2l-1 1 1 1c1 1 1 2 1 3l-2-3h-1-1c1-1 1-2 1-2h1l-2-2v1h-1z" class="X"></path><path d="M336 891h1v-2h1c0 1 1 2 2 3 2-1 6-1 7-1s1 1 2 1c2-1 4-1 7 0 1 0 2 1 3 1h1c0-1 1-1 2-1l2 2-3 1-1-1c-1 1-2 1-3 1-4-1-8 1-12 0-3 0-6-1-9-4z" class="d"></path><path d="M339 886c5 1 10 1 15 1 1 0 4 1 5 1 2-1 3-3 5-4h3c1 2 3 3 5 4v1h-2c-2-1-1-2-3-2l-1 1c-1 0-2 0-3-1-2 1-3 1-4 2l-1 1c-1 0-1 0-2-1v1 2c-3-1-5-1-7 0-1 0-1-1-2-1s-5 0-7 1c-1-1-2-2-2-3 1-1 1-2 1-3z" class="T"></path><path d="M324 868c1 0 1 0 3-1v1c0 1 0 1-1 2h2c1 1 2-1 3-2h1l2 1 6 6v1c-1 2-2 3-3 4l-2-1-1 1h-1v-2h0c-1 1-1 1-2 1l-1-1h-2l-3-3-1-1h0l1-2h0-1l-1-1 2-2-1-1z" class="i"></path><path d="M324 874h1c1 0 2-2 3-2 0 1 0 2-1 3h1l1-1 1 2c1 0 2-1 3-1v1 1c1 0 0 0 1-1h2l1 1v-1h3v-1 1c-1 2-2 3-3 4l-2-1-1 1h-1v-2h0c-1 1-1 1-2 1l-1-1h-2l-3-3-1-1z" class="S"></path><path d="M356 892v-2-1c1 1 1 1 2 1l1-1c1-1 2-1 4-2 1 1 2 1 3 1l1-1c2 0 1 1 3 2h2v-1-1c3 1 6 2 9 4l1 1c-1 0-3 0-4 1h-1 0c-1 1-1 2-2 3h-4 0-2v-2l-2-1v1c1 2 1 2 1 4l-1-1c-2 0-4 0-6-1v-1l3-1-2-2c-1 0-2 0-2 1h-1c-1 0-2-1-3-1z" class="c"></path><path d="M364 894l2 2v-1c0-1-1-1-2-2l-3-3 1-1 3 1c1 1 3 2 4 1h1c1 0 1 1 2 1l1 1 2-1 2 1c-1 1-1 2-2 3h-4 0-2v-2l-2-1v1c1 2 1 2 1 4l-1-1c-2 0-4 0-6-1v-1l3-1z" class="M"></path><path d="M373 893l2-1 2 1c-1 1-1 2-2 3h-4v-1h-1l3-2z" class="e"></path><path d="M295 837l2-1c1 1 0 1 1 2 1 0 1 0 2-1h0v2h1l1-1c1 1 0 0 0 1h1l3 1h2l10 3 4 2c1 1 4 2 6 3h2c1 1 2 3 2 4 1 0 1 1 2 1 2 1 9 5 9 7h0c-1-1-1-1-2-1-1-1-3-2-5-3-1-1-4-3-5-4-2 0 0 1-2 0h-1l-2 2-2 1v-1l-2 2c-1-1-1-1-1-2h-1 0l-1-1-2 1-1-1 1-1c-2-1-2-1-3-2l-2 2h-1l1-3h-1s-1 1-2 1v-1c0-1 0-1-1-1l-1-1c-1 0-1-1-1-2h-2v-2l-2 1c-1-1 1-2 0-3h-3c-2 0-3-2-4-4z" class="n"></path><path d="M318 845c-1 1-3 3-4 3v-1h-1l-2 1v-2c-1 0-1-1-2-1s-1 1-2 0l2-2 2-1c1 1 3 2 4 2 1 1 2 1 3 1h0z" class="U"></path><path d="M308 840l10 3 4 2c1 1 4 2 6 3h2c1 1 2 3 2 4 1 0 1 1 2 1 2 1 9 5 9 7h0c-1-1-1-1-2-1-1-1-3-2-5-3-1-1-4-3-5-4-2 0 0 1-2 0h-1l-2 2-2 1v-1c0-1 1-2 2-3 1 0 0 0 1-1-2-2-4-1-5-3-2-1-2 0-3-1l-1-1h0c-1 0-2 0-3-1-1 0-3-1-4-2h-3-2v-1l2-1z" class="g"></path><path d="M326 854l2-2h1c2 1 0 0 2 0 1 1 4 3 5 4 2 1 4 2 5 3 1 0 1 0 2 1h0l3 1c1 1 4 3 5 4h1l1 1h-2v1c1 1 1 1 3 1 0 1 1 2 1 3l-1 1c-1 0-2 0-2-1l-1 1h-3c1 1 1 2 2 2 1 1 2 1 2 2l-2-1h-1c-1 1-1 1-2 0v-1h-1l-2 2c-1-1-2-2-3-2-2-2-4-4-6-5h-1l-2-1-4-3 2-2h-1s-1 0-1-1c-1 0-3-2-4-4v-1-1-1l2-1z" class="Y"></path><path d="M326 854l2-2h1c2 1 0 0 2 0 1 1 4 3 5 4l-4 4v-1l1-1-1-1-1 1c-2-1-2-2-3-3-1 0-2-1-2-1z" class="O"></path><path d="M324 856c1 1 3 1 4 3 1 1 2 1 3 2l2 2c1 0 1 0 2 1l1 1 1-1v2c1 1 1 1 2 1l1 1h1c1 1 2 1 2 2l-1 1c-1 0-2-1-2-2h-1c-1 0 0-1-1-1h-3c-1-1-2-2-3-2-1-1-1-1-2-3h-1s-1 0-1-1c-1 0-3-2-4-4v-1-1z" class="d"></path><path d="M330 863c1 2 1 2 2 3 1 0 2 1 3 2h3c1 0 0 1 1 1h1c0 1 1 2 2 2l1-1c1 1 2 1 3 1l2 1c1 1 1 2 2 2 1 1 2 1 2 2l-2-1h-1c-1 1-1 1-2 0v-1h-1l-2 2c-1-1-2-2-3-2-2-2-4-4-6-5h-1l-2-1-4-3 2-2z" class="t"></path><path d="M368 876h2 0l2 3h-3v1h1c1 0 1 1 2 1h1v2l1-1c1 1 1 0 1 1 1 0 4 1 5 1 3 2 6 1 9 3h0 2l1 1v-1l2 1v1 2l1-1v2c2 0 2 0 4-1h3v1c1-1 2-1 4 0h0c-3 2-5 4-8 6l-1 1c-1 1-3 2-5 1h0l-1 1c-1 1-2 1-3 3h-2l-3-2h-2l-1-1v-2-2c-1 0-2-1-3-1h-2c1-1 1-2 2-3h0 1c1-1 3-1 4-1l-1-1c-3-2-6-3-9-4v1c-2-1-4-2-5-4l1-1c0-1 0-2-1-2s-2 0-4-1h0l-1-2 5-2h1z" class="i"></path><path d="M368 876l2 1c0 2-2 3-2 5v1c0-1 0-2-1-2s-2 0-4-1h0l-1-2 5-2h1z" class="X"></path><path d="M368 882h1l3 3c3 0 1-2 5 0 1 0 2 0 3 1h4c1 1 2 1 3 1h2 0 2l1 1h-4c-2-1-4 0-6 0-1-1-1-1-2-1h0-5c-1 0-1-1-3-1v1 1c-2-1-4-2-5-4l1-1v-1z" class="n"></path><path d="M372 887v-1c2 0 2 1 3 1h5 0c1 0 1 0 2 1 2 0 4-1 6 0h4v-1l2 1v1c-3 0-5 1-7 1-1 0-2 1-2 2v2h-1v1l-1 1v1h0l-1 1-2 1v-2c-1 0-2-1-3-1h-2c1-1 1-2 2-3h0 1c1-1 3-1 4-1l-1-1c-3-2-6-3-9-4z" class="M"></path><path d="M385 894v-2c0-1 1-2 2-2 2 0 4-1 7-1v2l1-1v2c2 0 2 0 4-1h3v1c1-1 2-1 4 0h0c-3 2-5 4-8 6l-1 1c-1 1-3 2-5 1h0l-1 1c-1 1-2 1-3 3h-2l-3-2h-2l-1-1v-2l2-1 1-1h0v-1l1-1v-1h1z" class="t"></path><path d="M385 894l1 1c1-1 1-1 2-1v1c0 1 0 2 1 3v1c-2-1-4-1-6-2h0v-1l1-1v-1h1z" class="e"></path><path d="M383 897c2 1 4 1 6 2l3 1-1 1c-1 1-2 1-3 3h-2l-3-2h-2l-1-1v-2l2-1 1-1z" class="Z"></path><path d="M382 898c0 2 0 2 1 3 2 0 3 0 5-1h1c-1 1-1 2-1 3v1h-2l-3-2h-2l-1-1v-2l2-1z" class="Y"></path><path d="M402 892c1-1 2-1 4 0h0c-3 2-5 4-8 6l-1 1c-1 1-3 2-5 1h0l1-1c0-1-1-2-1-3l-2 1v-1-1h1c2-1 3 0 5 1l2-2c1-1 2-2 4-2z" class="M"></path><path d="M393 899l3-3v1l1 2c-1 1-3 2-5 1h0l1-1z" class="e"></path><path d="M228 727h2l3 3 2 1c1 0 1 0 2 1h2l1 1h1c3 2 6 1 9 1 1 0 1-2 2-2v-1h1l2-1v1c2 1 3 0 5 0 1 1 3 2 4 2 2 1 3 3 5 2h2 2l1 1c2-1 3-1 4-3 1-1 2 0 3-2 1 1 1 1 2 1s1-1 1-2l1 1h2c2 0 4 1 5 2l1-1h1v2c1 1 0 3-1 4 0 2-1 2-2 3h-1l3 4v1l2-2c1 0 1 2 3 3 0-1 0-1 1-2h0v-2c3-1 9 0 12 1v1c1 0 1 1 2 2l1-1c0 1 1 1 1 1 1 1 1 1 2 1h1c1 1 1 3 1 5l-1 1c1 1 1 2 1 3-1 0-1 1-2 1-1-1-1-2-2-1l-1 1c-1 1-2 1-3 1l-1 2h0c1 1 1 2 1 3h1v-1l2-2h1l1 1-1 1s-1 1-1 2-1 2-1 3v1h0c2 1 3 1 4 1l1 1v1c-1 0-2 0-2 1h11 2l-3 1-1 1h1v2 1c-1 0-1 2-1 3v4h2v2h1l2 2c0 1 0 1 1 1v2c-3-1-6-1-9-1l-9-2c-6-2-10-5-16-9-3-3-7-5-11-7h-1l-5-3-7-2h0c-1-1-2-1-2-2l-3-4c-2-2-5-4-7-5h-1v-1c-3-1-5-4-8-6h0l-2-2-1-3c0-2 0-3-1-5 0-1-1-2-2-3l-9-4c-3-2-6-3-9-6z" class="U"></path><path d="M285 760c2-1 3-3 4-3s1 0 2 1c1 0 1 1 2 1h1l1 3h0l-1 3h-1c-2-1-3-2-3-4h-1v4l-3-3c0-1 0-2-1-2z" class="Q"></path><path d="M272 752l1 1 4-2h1v2h2l2-2c0 1 1 2 1 3v1c-1 2 0 1 0 3 0 0-2 4-2 5 0-1-1-1-1-2h-1c-1 1-2 1-3 1l-1-2c-1-2 2-2 2-4-1 0-2-1-3-1v-1h-1c-1 0-1-1-2-1l1-1z" class="b"></path><path d="M280 761h1l-1-1c-1-1-2-1-3 0h0l-1-1h2v-3c1-1 1-1 1-2l2 2 2-1c-1 2 0 1 0 3 0 0-2 4-2 5 0-1-1-1-1-2z" class="R"></path><path d="M281 743h3c1 1 1 1 1 3h2c2 0 4 1 6 1l1 1h2v1h-1c0 2 0 2 1 3l-2 2h1l-1 1c0 1 0 2-1 2l1 1h3c1 1 3 2 4 2h1l1-1c2 0 2 1 3 2-2 1-4 2-6 4h0-1v-1c0-2 0-3-1-4h-2c-1-1-1-1-2-1h-1c-1 0-1-1-2-1-1-1-1-1-2-1s-2 2-4 3h-1v-1c1-1 1-1 1-2 1-1 1 0 1-1h1c-1-1-2-2-2-4l-1-1-3-2 1-1h1l-1-1h-3v-1l2-3z" class="G"></path><path d="M285 746h2c2 0 4 1 6 1l1 1h2v1h-1c0 2 0 2 1 3l-2 2c-1-1-4-2-5-4v-1l-2-1c-1 0-1-1-2-2z" class="b"></path><path d="M296 760h2c1 1 1 2 1 4v1h1 0c2-2 4-3 6-4 0 1 1 2 1 3h1l2-3h0c1 1 1 2 1 3h1v-1l2-2h1l1 1-1 1s-1 1-1 2-1 2-1 3v1h0c2 1 3 1 4 1l1 1v1c-1 0-2 0-2 1h-1c-1-1-2-1-3-1s-2-1-3-2l-1 3h0l-2 2 1 2-2 2 1 3-2 2-2-2v-1c-1-2-1-1-2-1v-2h0c-1-2-1-3-3-4 1 0 2-1 2-1l1-1c0-1 1-2 2-2l-2-1c2-1 2-1 2-3-2 0-4 1-5 1h-1v1c1 1 2 1 3 2l-1 1h-1c-1-1-1-1-2 0l-1-1c0-2 1-4 1-6 0-1 2-1 2-2l-1-2z" class="a"></path><path d="M302 770l1 1h-1c-1 2-2 4-2 7-1-2-1-3-3-4 1 0 2-1 2-1l1-1c0-1 1-2 2-2z" class="k"></path><path d="M309 770l1-2v-2l1-1h3c0 1-1 2-1 3v1h0c2 1 3 1 4 1l1 1v1c-1 0-2 0-2 1h-1c-1-1-2-1-3-1s-2-1-3-2z" class="W"></path><path d="M299 743c3-1 9 0 12 1v1c1 0 1 1 2 2l1-1c0 1 1 1 1 1 1 1 1 1 2 1h1c1 1 1 3 1 5l-1 1c1 1 1 2 1 3-1 0-1 1-2 1-1-1-1-2-2-1l-1 1c-1 1-2 1-3 1l-1 2-2 3h-1c0-1-1-2-1-3-1-1-1-2-3-2l-1 1h-1c-1 0-3-1-4-2h-3l-1-1c1 0 1-1 1-2l1-1h-1l2-2c-1-1-1-1-1-3h1v-1h-2l-1-1v-1l2-2c1 0 1 2 3 3 0-1 0-1 1-2h0v-2z" class="E"></path><path d="M312 753h0c2 0 2 0 3 1l-1 2h-1l-1-3zm-6-3h3c0 2 1 2 0 4h-2c-1-1-2-2-2-3l1-1z" class="J"></path><path d="M295 754c1 1 2 1 3 1l1 1c0 1 1 1 2 2h1c0-1 1-1 1-1l1 1h1c1-1 2-1 3 0 2-1 2-1 4-1l1 1h1c-1 1-2 1-3 1l-1 2-2 3h-1c0-1-1-2-1-3-1-1-1-2-3-2l-1 1h-1c-1 0-3-1-4-2h-3l-1-1c1 0 1-1 1-2l1-1z" class="D"></path><path d="M299 743c3-1 9 0 12 1v1c1 0 1 1 2 2l1-1c0 1 1 1 1 1 1 1 1 1 2 1h1c1 1 1 3 1 5l-1 1-2-2c-1 0-3-1-5-2-1 0-1 0-2-1h-4l-4 2c-1 1 0 1-1 1-1 1-1 1-2 1l-2-1c-1-1-1-1-1-3h1v-1h-2l-1-1v-1l2-2c1 0 1 2 3 3 0-1 0-1 1-2h0v-2z" class="q"></path><path d="M303 746h1 4c0 1 1 1 2 2v1h0c-2-1-4-1-5-1l-2-2z" class="D"></path><path d="M311 745c1 0 1 1 2 2l1-1c0 1 1 1 1 1 1 1 1 1 2 1h1c1 1 1 3 1 5l-1 1-2-2h1c-1-1-2-2-3-2-2 0-2-1-4-1v-1l1-3z" class="K"></path><path d="M299 743c3-1 9 0 12 1v1l-1 3c-1-1-2-1-2-2h-4-1c0 1-1 1-2 2l-2-3v-2z" class="b"></path><path d="M253 749l2 2c2-1 2 0 4 1h1c1 1 0 1 1 1l1-1h2c1-1 2-2 4-2 0 1 0 2 2 2 1 0 1 0 1-1l1 1-1 1c1 0 1 1 2 1h1v1c1 0 2 1 3 1 0 2-3 2-2 4l1 2c1 0 2 0 3-1h1l-1 1c0 1 1 2 2 2v1l1-2 1 1-1 2 5 4-1 1 1 1 2-2c0-1-2-3-1-4h1v1h1c1 1 1 2 1 3 1-1 0-2 2-3 1 2-1 6-1 8 0 0 3 2 4 2h0c1-1 1-2 1-3 2 1 2 2 3 4h0v2c1 0 1-1 2 1v1l2 2 2-2-1-3 2-2-1-2 2-2h0l1-3c1 1 2 2 3 2s2 0 3 1h1 11 2l-3 1-1 1h1v2 1c-1 0-1 2-1 3v4h2v2h1l2 2c0 1 0 1 1 1v2c-3-1-6-1-9-1l-9-2c-6-2-10-5-16-9-3-3-7-5-11-7h-1l-5-3-7-2h0c-1-1-2-1-2-2l-3-4c-2-2-5-4-7-5h-1v-1c-3-1-5-4-8-6l1-1z" class="n"></path><path d="M308 773c2 1 2 2 3 4v1h0c-3 0-2-1-4-1l-1-2 2-2h0z" class="g"></path><path d="M312 772c1 0 2 0 3 1-2 2-2 3-1 6l-1 4h0v4l-1-1c-2 0-2 0-2 1l-1-1v-1l2-1c-1-1-1-1-1-2l-1-1c1-1 1-1 3-2v-7z" class="t"></path><path d="M311 784c-1-1-1-1-1-2l-1-1c1-1 1-1 3-2 0 2 0 4-1 6v-1z" class="k"></path><path d="M253 749l2 2c2-1 2 0 4 1h1c1 1 0 1 1 1l1-1h2c1-1 2-2 4-2 0 1 0 2 2 2 1 0 1 0 1-1l1 1-1 1c1 0 1 1 2 1h1v1c1 0 2 1 3 1 0 2-3 2-2 4l1 2c1 0 2 0 3-1h1l-1 1c0 1 1 2 2 2v1c0 1 0 2-1 3-2 0-5-1-6-2-2-1 0-1-3-2 0-1 0-1-1-2-2-2-7-5-9-5h-1v-1c-3-1-5-4-8-6l1-1z" class="h"></path><path d="M262 752h2c1-1 2-2 4-2 0 1 0 2 2 2 1 0 1 0 1-1l1 1-1 1c1 0 1 1 2 1h1v1c1 0 2 1 3 1 0 2-3 2-2 4h-2v-1h0c-2-2-4-3-6-3-1-1-1-1-1-2l-1 1-2-1c0 1-1 1-2 1-3 0-4-2-6-4 2-1 2 0 4 1h1c1 1 0 1 1 1l1-1z" class="D"></path><path d="M316 773h11 2l-3 1-1 1h1v2 1c-1 0-1 2-1 3v4 5h-1c-3-1-2-2-4-3v2h-1c-2 0-4 0-5-1l-1-5 1-4c-1-3-1-4 1-6h1z" class="L"></path><path d="M324 790c-1-2 0-3 0-5-2-1-2-1-3-3h1l3-1v4 5h-1z" class="B"></path><path d="M316 773h11 2l-3 1-1 1h1c-2 1-2 1-4 1-1 2-1 4-2 6v-7h0l-2 1h-1c-1-1-1-1-3-1v4c-1-3-1-4 1-6h1z" class="r"></path><path d="M228 727h2l3 3 2 1c1 0 1 0 2 1h2l1 1h1c3 2 6 1 9 1 1 0 1-2 2-2v-1h1l2-1v1c2 1 3 0 5 0 1 1 3 2 4 2 2 1 3 3 5 2h2 2l1 1c2-1 3-1 4-3 1-1 2 0 3-2 1 1 1 1 2 1s1-1 1-2l1 1h2c2 0 4 1 5 2l1-1h1v2c1 1 0 3-1 4 0 2-1 2-2 3h-1l3 4v1 1c-2 0-4-1-6-1h-2c0-2 0-2-1-3h-3l-2 3h-1 0c-1 1-2 1-3 1l-1-1h-1l-1-1c1 2 1 3 2 4 2 0 6 0 7 2h1l-2 2h-2v-2h-1l-4 2-1-1-1-1c0 1 0 1-1 1-2 0-2-1-2-2-2 0-3 1-4 2h-2l-1 1c-1 0 0 0-1-1h-1c-2-1-2-2-4-1l-2-2-1 1h0l-2-2-1-3c0-2 0-3-1-5 0-1-1-2-2-3l-9-4c-3-2-6-3-9-6z" class="q"></path><path d="M281 743c1-1 2-2 3-2 2-1 4 0 5 0l-2 4v1h-2c0-2 0-2-1-3h-3z" class="C"></path><path d="M289 741h1l3 4v1 1c-2 0-4-1-6-1v-1l2-4z" class="F"></path><path d="M264 736l1-1c1 1 2 1 2 2 1 1 3 1 4 1 1 1 1 1 2 1 1 1 1 1 2 1s2 1 3 1h1c1-1 2 0 2 0l-2 2v1h-1c-1 1-2 1-3 1 0-2-1-3-2-5l-2 1c-2-2-5-4-7-5z" class="R"></path><path d="M252 731h1l2-1v1c2 1 3 0 5 0 1 1 3 2 4 2 2 1 3 3 5 2h2c1 1 1 2 3 3h1l2 1 1-2h0l5-1c1 0 2 0 3 1h-3c-1 1-1 2-1 3l-1 1s-1-1-2 0h-1c-1 0-2-1-3-1s-1 0-2-1c-1 0-1 0-2-1-1 0-3 0-4-1 0-1-1-1-2-2l-1 1-6-2v-2l-1 1c-2 0-3 0-5-1v-1z" class="j"></path><path d="M281 731c1 1 1 1 2 1s1-1 1-2l1 1h2c2 0 4 1 5 2l1-1h1v2c1 1 0 3-1 4 0 2-1 2-2 3-1-1-2-1-3-2h-1-1c-2-1-3 0-4 1 0-1 0-2 1-3h3c-1-1-2-1-3-1l-5 1h0l-1 2-2-1h-1c-2-1-2-2-3-3h2l1 1c2-1 3-1 4-3 1-1 2 0 3-2z" class="B"></path><path d="M278 737l-1-1 2-2c2 0 2 0 4 2l-5 1z" class="C"></path><path d="M294 734c1 1 0 3-1 4 0 2-1 2-2 3-1-1-2-1-3-2h-1-1c-2-1-3 0-4 1 0-1 0-2 1-3h3c2-1 1 0 3 0 0 0 0-1 1-1h2 0l1-1c1 0 1-1 1-1z" class="h"></path><path d="M249 736c2-1 4-2 7-1h0c5 1 12 3 14 7 0 1 0 1 1 1l1 2c1 2 1 3 2 4 2 0 6 0 7 2h1l-2 2h-2v-2h-1l-4 2-1-1-1-1c0 1 0 1-1 1-2 0-2-1-2-2-2 0-3 1-4 2h-2l-1 1c-1 0 0 0-1-1h-1c-2-1-2-2-4-1l-2-2-1 1h0l-2-2-1-3c0-2 0-3-1-5 0-1-1-2-2-3 1 0 2-1 2-1h1z" class="C"></path><path d="M263 740c1 0 2-1 4 0 0 1 0 2-1 3-1-1-2-1-3-3z" class="H"></path><path d="M254 737h1c2 1 2 1 4 1 0 1 0 1-1 2h1 4c1 2 2 2 3 3l-1 1 1 2h0-2v-1c-1-1-1-1-1-2-2-1-2-1-4-1h-1c0-2-1-2-2-3s-1 0-2-2zm14 13v-1-1h-1l1-1c1 1 1 0 3 0v-1c-2 0-2 0-3-1v-1l3-1 1 2c1 2 1 3 2 4 2 0 6 0 7 2h1l-2 2h-2v-2h-1l-4 2-1-1-1-1c0 1 0 1-1 1-2 0-2-1-2-2z" class="E"></path><path d="M248 736h1c1 0 1 1 2 1s2-1 2-1l1 1c1 2 1 1 2 2s2 1 2 3h1c2 0 2 0 4 1 0 1 0 1 1 2l-2 2h-1v-1c1-1 0-1 1-2h-1l-3 3 1 2h1c1-1 1-1 2-1s1 0 2-1v1c0 1 0 2-1 3l-1 1-1 1c-1 0 0 0-1-1h-1c-2-1-2-2-4-1l-2-2-1 1h0l-2-2-1-3c0-2 0-3-1-5 0-1-1-2-2-3 1 0 2-1 2-1z" class="B"></path><path d="M256 739c1 1 2 1 2 3h1c-1 1-1 2-2 2l-1-1c0-1-1-1-1-2l1-2z" class="I"></path><path d="M246 737c1 0 2-1 2-1 1 1 1 2 2 3h2v2c0 1 1 1 1 2l1 1 1 1v3l-1 1h-1l-1 1h0l-2-2-1-3c0-2 0-3-1-5 0-1-1-2-2-3z" class="R"></path><path d="M657 653c10-1 19 0 28 1 2 1 1 1 2 3v1h2v17c2 1 5 3 7 5 1 2 0 3 1 5 0 3 0 6 1 8v1 2c-2 1-4 2-6 4l-3 3h-1v-2-1l-5-1h-13c-2 0-4 0-6-1h0c-2 1-4 1-5 1l-1 1c-1 0-2 1-2 1h-2s-2 2-2 3h0c-1 1-1 1-2 1l-2-1c0 1-1 2-2 2 0 1 0 1-1 1v-1h-4c-1 0-1 0-1-1v-1l-2-1v-2c0 1-1 2-1 3-3-3-6-5-9-9l1-1v-1c1-2 3-2 4-3 3-1 5-2 8-2v-2l-1-1c-1-2 1-2 2-3v-3l-1-5c1-2 1-4 1-6v-2-3c-2-1-2-2-3-3v-1h-1l-1-1c2-1 2 0 3 0 2-2 3-3 5-3 1 1 0 0 2 1v-2c3-1 8 0 10-1z" class="l"></path><path d="M673 679h5c1 2 0 4 0 6h-6l1-1v-3-2z" class="j"></path><path d="M655 663c1-1 1-1 2-1 1 1 1 1 1 3l-1 1v1h-2v1h2 1c1 1 1 1 0 1v2l1 1v2c1-1 1-1 2-1h2c1 1 2 2 3 2l1-1h1c1 1 2 1 3 1 2 0 4 2 5 3-1 0-4-1-5 0l1 1h1v2c0 1-1 1-1 1l-1 2h-5c-1-2 0-5 0-7l-1 1v6c-2 0-4 1-6 0 0 0 0-1-1-1 0-2 0-4 1-5h2v-1h-2-4v-1s1 0 1-1h-2c1-3 0-7 0-9l1-3z" class="O"></path><path d="M648 657c2 0 2 0 4 1h0c2 1 2 3 3 5l-1 3v1h-1l-2-3h-1c0 1 0 1-2 2l2 2v1c-1 0 0 0 0 0 0 3 0 4-1 6 0 1 1 2 0 3l1 1h1l-1 1-1 1c0 1 0 1 1 3 0 1 0 1-1 2 0 1-1 1-1 1l-7 1v-2l-1-1c-1-2 1-2 2-3v-3l-1-5c1-2 1-4 1-6v-2h0c0-1 0-3 1-5 1-1 2-3 4-4h1z" class="g"></path><path d="M647 657h1c1 1 2 0 2 2 1 0 1 1 1 2v1l-2 2h-1l-1-1-1-1v-1l1-1v-3z" class="f"></path><path d="M648 657c2 0 2 0 4 1h0c2 1 2 3 3 5l-1 3v1h-1l-2-3v-2-1c0-1 0-2-1-2 0-2-1-1-2-2z" class="W"></path><path d="M651 662v2h-1c0 1 0 1-2 2l2 2v1c-1 0 0 0 0 0 0 3 0 4-1 6 0 1 1 2 0 3v1l-1 1-1-1 1-3v-1l-1-1c0-3 0-3 1-5l-1-1v-5l1 1h1l2-2z" class="M"></path><path d="M657 653c10-1 19 0 28 1 2 1 1 1 2 3-1-1-2-1-3 0-1 0-2-1-4-1l-2 2c-2 0-5-1-7 0v2 2c-1 2-1 4-1 6v5l1 2c-1 0-2 0-3-1h-1l-1 1c-1 0-2-1-3-2h-2c-1 0-1 0-2 1v-2l-1-1v-2c1 0 1 0 0-1h-1-2v-1h2v-1l1-1c0-2 0-2-1-3-1 0-1 0-2 1-1-2-1-4-3-5h0c-2-1-2-1-4-1h-1c-2 1-3 3-4 4-1 2-1 4-1 5h0v-3c-2-1-2-2-3-3v-1h-1l-1-1c2-1 2 0 3 0 2-2 3-3 5-3 1 1 0 0 2 1v-2c3-1 8 0 10-1z" class="a"></path><path d="M652 658l1-1c1 1 2 2 2 3h0l2 1v-1-1l2-3c1-1 12-2 13-1 2 0 3 1 5 1 0 0 1-1 2-1l1 1-2 2c-2 0-5-1-7 0v2-2c-2-1-4-1-6-1s-4 1-6 2c-1 1-1 1 0 2v6h-1 0c-1 0-1 0-1 1h-2v-1h2v-1l1-1c0-2 0-2-1-3-1 0-1 0-2 1-1-2-1-4-3-5z" class="D"></path><path d="M657 668c0-1 0-1 1-1h0 1v-6c-1-1-1-1 0-2 2-1 4-2 6-2s4 0 6 1v2 2c-1 2-1 4-1 6v5l1 2c-1 0-2 0-3-1h-1l-1 1c-1 0-2-1-3-2h-2c-1 0-1 0-2 1v-2l-1-1v-2c1 0 1 0 0-1h-1z" class="V"></path><path d="M663 670c-3-1-1-2-2-4 1-1 1-2 2-3l-1-2c0-2 0-2 2-3 1 0 3 0 4 1 0 1 1 2 0 3h-1v3c-2-1-2-2-3-2-1 1-1 2 0 3l-1 4z" class="C"></path><path d="M664 666c-1-1-1-2 0-3 1 0 1 1 3 2 1 1 1 1 2 1 0 1 1 1 1 2h0v5l1 2c-1 0-2 0-3-1h-1l-1 1c-1 0-2-1-3-2h0c0-1 1-2 1-2l-1-1 1-4z" class="F"></path><path d="M664 666c-1-1-1-2 0-3 1 0 1 1 3 2 1 1 1 1 2 1 0 1 1 1 1 2l-1 1-1-1c-1 0-2 0-2 1h-2v-3z" class="H"></path><path d="M680 656c2 0 3 1 4 1 1-1 2-1 3 0v1h2v17c2 1 5 3 7 5 1 2 0 3 1 5 0 3 0 6 1 8-1 1-1 1-2 1-2-1-3-1-5-3l4 1h1c-2-2-10-4-12-4v-1h2l-2-2h0l-1 1h0c-1 0-1 0-2-1v-2h0c0-2 0-2 1-4l-1-1h-5c-1-1-3-3-5-3l-1-2v-5c0-2 0-4 1-6v-2-2c2-1 5 0 7 0l2-2z" class="t"></path><path d="M689 681v-4c1 0 3 0 4 1s1 2 2 2l1 1-1 2-6-2z" class="E"></path><path d="M687 659c1 7 0 14 1 22-1 2-1 4-1 6h-1l-2-2h0l-1 1h0c-1 0-1 0-2-1v-2h0c1-1 1-2 1-3 1 0 1 0 2 1l2 1v-1c0-1-1-1-2-2h-1v-1l3-2 1-1c1-5 0-11 0-16h0z" class="b"></path><path d="M683 669c0-3-1-8 0-10 2-1 2-1 4-1v1h0c0 5 1 11 0 16l-1 1c-3-2-3-2-4-4 1-1 1-2 1-3z" class="J"></path><path d="M696 680c1 2 0 3 1 5 0 3 0 6 1 8-1 1-1 1-2 1-2-1-3-1-5-3l4 1h1c-2-2-10-4-12-4v-1h2 1c0-2 0-4 1-6h1l6 2 1-2v-1z" class="W"></path><path d="M680 656c2 0 3 1 4 1 1-1 2-1 3 0v1c-2 0-2 0-4 1-1 2 0 7 0 10 0 1 0 2-1 3 1 2 1 2 4 4l-3 2v1h1c1 1 2 1 2 2v1l-2-1c-1-1-1-1-2-1 0 1 0 2-1 3 0-2 0-2 1-4l-1-1h-5c-1-1-3-3-5-3l-1-2v-5c0-2 0-4 1-6v-2-2c2-1 5 0 7 0l2-2z" class="D"></path><path d="M679 666c0-1 1-2 2-3l1 1v2c0 1-1 1-1 2l1 1v-1l1 1c0 1 0 2-1 3v2h-1c-2-1-1-3-2-4v-4z" class="F"></path><path d="M680 656c2 0 3 1 4 1 1-1 2-1 3 0v1c-2 0-2 0-4 1-1 2 0 7 0 10l-1-1v1l-1-1c0-1 1-1 1-2v-2l-1-1c-1 1-2 2-2 3l-1-1-1-1h-2l-1-1h-1v1h-1l-1-2h0v-2-2c2-1 5 0 7 0l2-2z" class="C"></path><path d="M671 660v-2c2-1 5 0 7 0l2 1c0 1 0 2-1 3-1 0-1 0-1-1h-2c-1 0-2-1-4 0l-1 1h0v-2z" class="S"></path><path d="M648 687c4 0 7-1 11-1 7 0 13 1 20 1h1c1 1 3 1 4 1 2 0 10 2 12 4h-1l-4-1c2 2 3 2 5 3 1 0 1 0 2-1v1 2c-2 1-4 2-6 4l-3 3h-1v-2-1l-5-1h-13c-2 0-4 0-6-1h0c-2 1-4 1-5 1l-1 1c-1 0-2 1-2 1h-2s-2 2-2 3h0c-1 1-1 1-2 1l-2-1c0 1-1 2-2 2 0 1 0 1-1 1v-1h-4c-1 0-1 0-1-1v-1l-2-1v-2c0 1-1 2-1 3-3-3-6-5-9-9l1-1v-1c1-2 3-2 4-3 3-1 5-2 8-2l7-1z" class="g"></path><path d="M688 697v-1c2 1 3 2 4 4l-3 3h-1v-2-1-3z" class="X"></path><path d="M651 688c2 0 8-1 10 0l4 1v1h-14v-2z" class="S"></path><path d="M648 687c4 0 7-1 11-1 7 0 13 1 20 1h1c1 1 3 1 4 1 2 0 10 2 12 4h-1l-4-1c-2-1-5-1-8-2-5 0-10-1-15-1h-7c-2-1-8 0-10 0-7 0-14 1-21 5l-1 1v-1c1-2 3-2 4-3 3-1 5-2 8-2l7-1z" class="Z"></path><path d="M630 693h1c1 1 2 3 4 4l1-1h1v3h1l1-1v-2-1h1v1c2 0 6-1 8-2 4-1 10 0 15 0 2 0 4 0 6-1l2 1h1 1 3l1 1h3c0 1 2 1 2 1l2 1h4v3l-5-1h-13c-2 0-4 0-6-1h0c-2 1-4 1-5 1l-1 1c-1 0-2 1-2 1h-2s-2 2-2 3h0c-1 1-1 1-2 1l-2-1c0 1-1 2-2 2 0 1 0 1-1 1v-1h-4c-1 0-1 0-1-1v-1l-2-1v-2c0 1-1 2-1 3-3-3-6-5-9-9l1-1 1-1z" class="d"></path><path d="M630 693h1c1 1 2 3 4 4l1-1h1v3h1l1-1c5-2 10-3 16-3v1h-2l-1 2-6 2c-3-1-6-1-8 0v1c0 1-1 2-1 3-3-3-6-5-9-9l1-1 1-1z" class="W"></path><path d="M646 700l2 1 1-1s0 1 1 1l2-2c3-1 5 0 7-2 2-1 6 1 8 0 3-1 9 0 12 1l4 1h-13c-2 0-4 0-6-1h0c-2 1-4 1-5 1l-1 1c-1 0-2 1-2 1h-2s-2 2-2 3h0c-1 1-1 1-2 1l-2-1c0 1-1 2-2 2 0 1 0 1-1 1v-1h-4c-1 0-1 0-1-1v-1l-2-1v-2-1c2-1 5-1 8 0z" class="q"></path><path d="M806 667l1-1c2 2 3 3 3 6 0 1-1 2-1 3 1 1 1 1 2 1 2 1 4 3 5 5 0 3-1 4-2 7-1 1-1 3-1 4l1 1v1h-1l-1 1c2 0 2 1 4 2v4 1c1 2 1 3 0 5h1l-1 4c-1 3-3 6-4 9l-2 5-1 1c0 1-1 2-2 3v1h0l-1 1c0 1-1 1-2 1-3 0-6 3-7 5-1 1-1 2-1 3l-1 1c-1 2-3 4-5 5l-2 1-4 2c-1 1-2 2-4 3s-3 3-4 5l-4 5-3 3h0-1-1v2c-1 2-4 5-6 7-2 1-3 2-4 3-1-1-3-1-3-2-1 2-2 4-3 5 0 1 0 1-1 2l-1-1 1-1c1-1 1-3 2-4l1-2v-1l1-1c0-2 0-3 1-4h1l-1-1-1-1-2-1h0c-1 0-2 1-2 1-1 0-2-1-2-1l-3 1 2-2h0c2 0 4-1 5-3h1c1 0 2 0 3-1l2-2c1 0 2-1 3-2 1 0 1-1 1-2-2 0-2 0-3 2h-2c-2 1-3 3-4 4s-2 1-3 1h-1l-2 1-2 2h-2v-7c0-1 1-1 1-2v-5c1-2 0-4 0-6 1-2 2-3 3-4 0-1 1-1 2-1l1 1 2-1c1-1 2-1 3-2 0-1 0-2 1-3l3-6 1-4-1-1c0-1 1-2 1-2h1v-2c-1 0-1-1-1-2l-1-1v1c0-2-1-2-2-2l1-2-1-1c0-2 0-4 1-6 1 1 6-4 8-5 0 0 1 0 2-1 1 0 4-2 4-4 0-1 1-1 1-2 2-2 2-3 5-3 1-1 0-2 0-3 1-1 2-2 2-3v-2c2 0 2 0 3-1l-2-2c0-1 0-1 1-2l-1-2c1-1 3-1 4-1l2-1 2-1h3c2 0 3-1 4-2h2c0 2 0 2 1 4v1c1 1 2-2 3-2h3v-1h1v-2-1-3z" class="E"></path><path d="M802 718l1 1c0 2 0 5-2 6-2 2-4 2-6 4l-3 1c-1 2-2 2-2 3h-2v-1l-1 1c-3 0-4 3-6 3v-1c2-2 4-5 6-6h1l2-2c1-1 1-1 2-1l-1-2 5-2 3-1 2-2s0-1 1-1z" class="O"></path><path d="M802 718l1 1c0 2 0 5-2 6-2 2-4 2-6 4l-3 1v-1c1-2 4-2 6-4-1-1-1 0-1-1 1-1 2-1 2-2v-1l2-2s0-1 1-1zm-18-2l-1 2 1 1 1-1c0 3-4 5-4 7 2-1 4-3 5-5h1 0l-2 2c1 1 1 1 1 2v1c-8 5-16 13-22 20-1-1 4-5 4-7v-2c1 0 2-1 2-2 1-2 4-3 5-5 0-1 0-2-1-3l3-3c1-1 1-2 2-2l1-1v-1c0-1 1-2 1-2l3-1z" class="a"></path><path d="M784 716l3-3c1-1 2-2 3-4h0l3-3c0-1 0-2 1-2l1-1c0-1 1-2 2-2 0 2-1 3-2 5 1 0 1 0 2-1v1c0 1-1 2-1 3-1 1-1 1-1 2v1h2c0 1-1 1-1 2l-3 5 2 2 1 1-5 2-5 1v-1c0-1 0-1-1-2l2-2h0-1c-1 2-3 4-5 5 0-2 4-4 4-7l-1 1-1-1 1-2z" class="N"></path><path d="M793 719l2 2 1 1-5 2-5 1v-1c1-1 2-2 4-2h0c2 0 2-2 2-3h1z" class="c"></path><path d="M795 712h2c0 1-1 1-1 2l-3 5h-1c-1-1-1 0-3 0l2-2c2-1 3-4 4-5z" class="Y"></path><path d="M798 695c2-1 2-1 3-3l1 2c-1 1-1 2-2 3s-2 2-2 3l-1 1c-1 0-2 1-2 2l-1 1c-1 0-1 1-1 2l-3 3h0c-1 2-2 3-3 4l-3 3-3 1s-1 1-1 2v1l-1 1c-1 0-1 1-2 2l-3 3c-1 3-2 3-4 4 1-1 1-2 1-3v-1c1-1 1-1 3-2l1-1c-1-1-2-2-3-2l1-1 1-1c2 0 3-1 3-3h1c0-1-1-4 0-4 0-1 1-1 1-1 0-1 0-2 1-2v-1h0c3-1 4-3 6-6h1c0-1 1-2 1-3 1-1 2-3 4-4l4-2h1l1 2z" class="D"></path><path d="M778 716c0 1 1 1 1 2-1 1-1 2-2 2v2c-1 1-1 1-3 2l1-1c-1-1-2-2-3-2l1-1 1-1c2 0 3-1 3-3h1z" class="Q"></path><path d="M780 709h1c1-2 1-1 3-2l1 1c-1 2-2 2-3 3l-1 1c0 1-2 3-3 4 0-1-1-4 0-4 0-1 1-1 1-1 0-1 0-2 1-2z" class="E"></path><path d="M796 693h1l1 2-2 2h-1-1-1c0 1-1 2-2 3v1h0-1c0-1-1-2-2-2 1-1 2-3 4-4l4-2z" class="I"></path><path d="M798 695c2-1 2-1 3-3l1 2c-1 1-1 2-2 3s-2 2-2 3l-1 1c-1 0-2 1-2 2l-1 1c-1 0-1 1-1 2l-3 3h0c-1 2-2 3-3 4l-3 3-3 1c0-1 1-1 1-2 1-1 2-3 4-5 1-2 3-2 4-4l-1-1h0l2-3 1 1c1-1 3-4 4-6l2-2z" class="L"></path><path d="M814 681c1 2 0 4-1 6l1 1c-1 1-1 3-1 4l1 1v1h-1l-1 1c2 0 2 1 4 2v4 1c-1 0-1 1-2 2v2c-1 0-2 1-3 2 0 1-1 1-1 2-2 1-4 3-5 4l1 1 2-2c0 1 0 1 1 1l-3 3v1c0 1-2 1-3 1l-1-1c-1 0-1 1-1 1l-2 2-3 1-1-1-2-2 3-5c0-1 1-1 1-2h-2v-1c0-1 0-1 1-2 0-1 1-2 1-3v-1c-1 1-1 1-2 1 1-2 2-3 2-5l1-1c0-1 1-2 2-3s1-2 2-3v-2l5-1c1-2 3-3 4-5 1-1 1-2 2-3 1 0 1-1 1-2z" class="O"></path><path d="M803 706l4 1-3 3-1-3v-1z" class="S"></path><path d="M803 707l1 3-3 7 1 1c-1 0-1 1-1 1h-2l-1-3 2-2v-2c0-1 2-4 3-5z" class="l"></path><path d="M796 714c1 0 3-1 4-2v2l-2 2 1 3h2l-2 2-3 1-1-1-2-2 3-5z" class="k"></path><path d="M795 721c0-2 1-3 3-5l1 3h2l-2 2-3 1-1-1z" class="d"></path><path d="M808 699c1 1 2 2 3 2 0 3-1 4-3 5l-1 1-4-1c1-1 1-3 2-3 2-2 2-2 3-4z" class="l"></path><path d="M813 687l1 1c-1 1-1 3-1 4l1 1v1h-1l-1 1v2h0 2l1 1h0-3v2l-1 1c-1 0-2-1-3-2v-1c2-3 2-3 2-5l-1-1c1-2 3-3 4-5z" class="g"></path><path d="M808 698h1 2l1 2-1 1c-1 0-2-1-3-2v-1z" class="M"></path><path d="M814 681c1 2 0 4-1 6s-3 3-4 5l1 1c0 2 0 2-2 5v1c-1 2-1 2-3 4-1 0-1 2-2 3v1c-1 1-3 4-3 5-1 1-3 2-4 2 0-1 1-1 1-2h-2v-1c0-1 0-1 1-2 0-1 1-2 1-3v-1c-1 1-1 1-2 1 1-2 2-3 2-5l1-1c0-1 1-2 2-3s1-2 2-3v-2l5-1c1-2 3-3 4-5 1-1 1-2 2-3 1 0 1-1 1-2z" class="Y"></path><path d="M802 692l5-1c-2 3-3 4-4 6l-1 1c-1 1-2 1-4 2h0c0-1 1-2 2-3s1-2 2-3v-2z" class="N"></path><path d="M798 700h0c2-1 3-1 4-2 0 2-1 3-3 5l-1 2c0 1 1 1 1 2s0 2-2 3h-1l-1 1c0-1 0-1 1-2 0-1 1-2 1-3v-1c-1 1-1 1-2 1 1-2 2-3 2-5l1-1z" class="T"></path><path d="M799 707c2-1 3-2 4-4 0-1 2-3 2-5 0-1 1-1 1-2h1c0 1 0 0 1 1v1 1c-1 2-1 2-3 4-1 0-1 2-2 3v1c-1 1-3 4-3 5-1 1-3 2-4 2 0-1 1-1 1-2h-2v-1l1-1h1c2-1 2-2 2-3z" class="U"></path><path d="M780 708v1c-1 0-1 1-1 2 0 0-1 0-1 1-1 0 0 3 0 4h-1c0 2-1 3-3 3l-1 1-1 1c1 0 2 1 3 2l-1 1c-2 1-2 1-3 2v1c0 1 0 2-1 3 2-1 3-1 4-4 1 1 1 2 1 3-1 2-4 3-5 5 0 1-1 2-2 2v2c0 2-5 6-4 7l-1 1 1 3h1c0 2-2 3-3 5h0c-2 0-2 0-3 2h-2c-2 1-3 3-4 4s-2 1-3 1h-1l-2 1-2 2h-2v-7c0-1 1-1 1-2v-5c1-2 0-4 0-6 1-2 2-3 3-4 0-1 1-1 2-1l1 1 2-1c1-1 2-1 3-2 0-1 0-2 1-3l3-6 1-4-1-1c0-1 1-2 1-2h1v-2c-1 0-1-1-1-2l-1-1h2c1 1 1 1 2 1 0-1 1-1 2-2 0-2 2-3 4-4 0-1 0-1 1-1s0 0 1-1h1l2 1 6-2z" class="J"></path><path d="M747 740c0-1 1-1 2-1l1 1 2-1v2c-1 2-2 2-3 4v-2h-2c1-2 0-2 0-3z" class="H"></path><path d="M744 750c1-2 0-4 0-6 1-2 2-3 3-4 0 1 1 1 0 3-1 3-2 5-2 9l1 1c0 1 0 2-1 3v3h1l2-1-1 4-2 2h-2v-7c0-1 1-1 1-2v-5zm36-42v1c-1 0-1 1-1 2 0 0-1 0-1 1-1 0 0 3 0 4h-1c0 2-1 3-3 3l-1 1-1 1-1-1c-1 0-2 0-3 1 1-1 1-2 2-3l-1-2h1c0-1 1-1 1-2l-1-1-2 2c-1 3-4 5-5 8 0 0-1 1-1 2-1 1 0 1-1 2 0 1-2 1-2 1l1-4-1-1c0-1 1-2 1-2h1v-2c-1 0-1-1-1-2l-1-1h2c1 1 1 1 2 1 0-1 1-1 2-2 0-2 2-3 4-4 0-1 0-1 1-1s0 0 1-1h1l2 1 6-2z" class="F"></path><path d="M771 720c1-1 1-1 1-2 2-1 3-2 5-2 0 2-1 3-3 3l-1 1-1 1-1-1z" class="C"></path><path d="M763 717c0-1 1-1 2-2 0-2 2-3 4-4 0-1 0-1 1-1s0 0 1-1h1l2 1c-6 1-9 6-12 11l-2 3-1-1c0-1 1-2 1-2h1v-2c-1 0-1-1-1-2l-1-1h2c1 1 1 1 2 1z" class="G"></path><path d="M768 721c1-1 2-1 3-1l1 1c1 0 2 1 3 2l-1 1c-2 1-2 1-3 2v1c0 1 0 2-1 3 2-1 3-1 4-4 1 1 1 2 1 3-1 2-4 3-5 5 0 1-1 2-2 2v2c0 2-5 6-4 7l-1 1 1 3h1c0 2-2 3-3 5h0c-2 0-2 0-3 2h-2c-2 1-3 3-4 4s-2 1-3 1h-1l-2 1 1-4c0-2 2-3 2-5 1-1 1-2 1-3l2-3c0-1 1-2 2-3 0 0 1 0 1-1l3-6c1-1 2-1 3-2 2-2 0-1 0-2s2-2 2-3 1-2 2-3c0-1 1-3 1-4s0-1 1-2z" class="I"></path><path d="M749 761v-1c2-1 2-4 3-5s1-1 2-1v1l-4 6h-1zm10-24c1 1 1 2 1 4l-2 2s-1 1-1 2v1c0 1 0 0-1 1s-1 4-3 4l-2-1 2-3c0-1 1-2 2-3 0 0 1 0 1-1l3-6z" class="E"></path><path d="M770 730c2-1 3-1 4-4 1 1 1 2 1 3-1 2-4 3-5 5 0 1-1 2-2 2v2c0 2-5 6-4 7l-1 1-3 4h-1v-2c1-1 3-3 3-4v-1s1-1 1-2c1-2 4-3 4-5h-1-1c1-3 3-4 5-6z" class="K"></path><path d="M816 702c1 2 1 3 0 5h1l-1 4c-1 3-3 6-4 9l-2 5-1 1c0 1-1 2-2 3v1h0l-1 1c0 1-1 1-2 1-3 0-6 3-7 5-1 1-1 2-1 3l-1 1c-1 2-3 4-5 5l-2 1-4 2c-1 1-2 2-4 3s-3 3-4 5l-4 5-3 3h0-1-1v2c-1 2-4 5-6 7-2 1-3 2-4 3-1-1-3-1-3-2-1 2-2 4-3 5 0 1 0 1-1 2l-1-1 1-1c1-1 1-3 2-4l1-2v-1l1-1c0-2 0-3 1-4h1l-1-1-1-1-2-1h0c-1 0-2 1-2 1-1 0-2-1-2-1l-3 1 2-2h0c2 0 4-1 5-3h1c1 0 2 0 3-1l2-2c1 0 2-1 3-2 1 0 1-1 1-2h0c1-2 3-3 3-5l3-3c2-2 3-4 6-6 2-1 4-3 7-5v1c2 0 3-3 6-3l1-1v1h2c0-1 1-1 2-3l3-1c2-2 4-2 6-4 2-1 2-4 2-6 1 0 3 0 3-1v-1l3-3c-1 0-1 0-1-1l-2 2-1-1c1-1 3-3 5-4 0-1 1-1 1-2 1-1 2-2 3-2v-2c1-1 1-2 2-2z" class="B"></path><path d="M782 742c2 1 1 2 3 2h1 1c-1 1-2 1-3 2-2 0-3-2-3-3l1-1zm-4 3l2 1v1c-1 0-1 0-1 1l-1 1c-1 1-3 3-4 5-1 1-1 2-2 2l-2 2c-1 1-2 1-3 1 2-4 3-7 6-10 1-2 3-3 5-4z" class="I"></path><path d="M774 754h-3 0l1-2 1-1c1-1 3-3 4-3l1 1c-1 1-3 3-4 5z" class="E"></path><path d="M787 733l1-1v1h2c-1 4-1 5-4 7l-4 2-1 1c-1 1-2 1-3 2-2 1-4 2-5 4l-1-1 2-2 2-1v-2c2-3 4-3 6-5 2-1 3-3 5-5z" class="L"></path><path d="M816 702c1 2 1 3 0 5h1l-1 4c-1 3-3 6-4 9l-2 5-1 1c0 1-1 2-2 3v1h0l-1 1c0 1-1 1-2 1-3 0-6 3-7 5-1 1-1 2-1 3l-1 1c-1 2-3 4-5 5l-2 1-4 2c3-2 5-4 7-6h-2-1c2-3 7-7 8-10 0-2 4-3 6-4v-1l-2 1c-1-1 0-1-1-2-1 1-2 1-3 1l-1 1c2-2 4-2 6-4 2-1 2-4 2-6 1 0 3 0 3-1v-1l3-3c-1 0-1 0-1-1l-2 2-1-1c1-1 3-3 5-4 0-1 1-1 1-2 1-1 2-2 3-2v-2c1-1 1-2 2-2z" class="R"></path><path d="M816 702c1 2 1 3 0 5h1l-1 4c-1 3-3 6-4 9l-2 5-1 1c0 1-1 2-2 3v1h0l-1 1c0 1-1 1-2 1-3 0-6 3-7 5-1 1-1 2-1 3l-1 1c-1 2-3 4-5 5l-2 1-4 2c3-2 5-4 7-6l2-1c2-6 6-9 11-12 1-2 2-4 2-5 1-3 2-6 4-8 0-2 2-3 2-5 0-1 1-2 2-3v-3-2c1-1 1-2 2-2z" class="k"></path><path d="M810 725l-2-2 2-2c0-1 1-1 2-1l-2 5z" class="c"></path><path d="M781 735v1c2 0 3-3 6-3-2 2-3 4-5 5-2 2-4 2-6 5v2l-2 1-2 2 1 1c-3 3-4 6-6 10 0 0-1 1-1 2-2 2-7 6-7 8h1c0 1 0 1-1 2s-2 1-3 3l1 1v2c-1-1-3-1-3-2-1 2-2 4-3 5 0 1 0 1-1 2l-1-1 1-1c1-1 1-3 2-4l1-2v-1l1-1c0-2 0-3 1-4h1l-1-1-1-1-2-1h0c-1 0-2 1-2 1-1 0-2-1-2-1l-3 1 2-2h0c2 0 4-1 5-3h1c1 0 2 0 3-1l2-2c1 0 2-1 3-2 1 0 1-1 1-2h0c1-2 3-3 3-5l3-3c2-2 3-4 6-6 2-1 4-3 7-5z" class="O"></path><path d="M781 735v1c2 0 3-3 6-3-2 2-3 4-5 5-2 2-4 2-6 5-2 1-5 1-6 3-1 1 0 1-1 2-1 2-4 4-5 6l-1 1-2 2h0l-1 1c-1 1-2 1-4 2l2-2c1 0 2-1 3-2 1 0 1-1 1-2h0c1-2 3-3 3-5l3-3c2-2 3-4 6-6 2-1 4-3 7-5z" class="j"></path><path d="M806 667l1-1c2 2 3 3 3 6 0 1-1 2-1 3 1 1 1 1 2 1 2 1 4 3 5 5 0 3-1 4-2 7l-1-1c1-2 2-4 1-6 0 1 0 2-1 2-1 1-1 2-2 3-1 2-3 3-4 5l-5 1v2l-1-2c-1 2-1 2-3 3l-1-2h-1l-4 2c-2 1-3 3-4 4 0 1-1 2-1 3h-1c-2 3-3 5-6 6h0l-6 2-2-1h-1c-1 1 0 1-1 1s-1 0-1 1c-2 1-4 2-4 4-1 1-2 1-2 2-1 0-1 0-2-1h-2v1c0-2-1-2-2-2l1-2-1-1c0-2 0-4 1-6 1 1 6-4 8-5 0 0 1 0 2-1 1 0 4-2 4-4 0-1 1-1 1-2 2-2 2-3 5-3 1-1 0-2 0-3 1-1 2-2 2-3v-2c2 0 2 0 3-1l-2-2c0-1 0-1 1-2l-1-2c1-1 3-1 4-1l2-1 2-1h3c2 0 3-1 4-2h2c0 2 0 2 1 4v1c1 1 2-2 3-2h3v-1h1v-2-1-3z" class="i"></path><path d="M803 689l1 1c0 1-1 2-2 2v2l-1-2c-1 2-1 2-3 3l-1-2h-1v-1c2 0 2-1 4-2 1 0 2-1 3-1z" class="B"></path><path d="M807 684c0-2 1-5 2-6h1c2 1 3 1 4 3-3 1-4 3-7 3z" class="P"></path><path d="M799 682l1-1h1c1 0 1 0 2-1h1 0l2 2c-1 0-3 1-3 2l-3 3c0 1 0 1-1 3-1-1-1-2-2-2 1-1 2-2 2-3-1-1-1-1-1-2l1-1zm8 2c3 0 4-2 7-3 0 1 0 2-1 2-1 1-1 2-2 3-1 2-3 3-4 5l-5 1c1 0 2-1 2-2l-1-1c1-1 1-2 2-2 0-1 1-2 2-3z" class="Z"></path><path d="M799 676c1 1 2-2 3-2h3v-1c1 2 2 4 1 5s-1 2-2 2h-1c-1 1-1 1-2 1h-1l-1 1c-2 1-4 4-5 6-1 0-1 1-2 2h-1-1l1-2h0c1-1 1-2 1-3l-2-1 1-1c1-1 2-2 3-2v-1c2-1 3-2 4-3l1-1z" class="P"></path><path d="M805 674v-1c1 2 2 4 1 5s-1 2-2 2l-1-1v-1l-1-2c1-1 2-2 3-2z" class="R"></path><path d="M798 677c0 1 1 1 1 2l-1 1h-1c-1 2-2 4-4 5h-1l-2-1 1-1c1-1 2-2 3-2v-1c2-1 3-2 4-3z" class="V"></path><path d="M798 677c0 1 1 1 1 2l-1 1h-1-1l-1 1-1 1v-1-1c2-1 3-2 4-3z" class="B"></path><path d="M790 690h1 1c1-1 1-2 2-2 1-2 3-5 5-6l-1 1c0 1 0 1 1 2 0 1-1 2-2 3l-1 4h0v1l-4 2c-2 1-3 3-4 4 0 1-1 2-1 3h-1c-2 3-3 5-6 6h0l-6 2-2-1c1 0 1-1 2-1 1-1 2 0 4-1l-1-1c1-1 1-2 2-2h1c1-1 3-3 4-5 0-1 1-1 2-2v-2-1c1 0 2 0 2-1 1-1 1-1 1-2l1-1z" class="h"></path><path d="M789 673h3c2 0 3-1 4-2h2c0 2 0 2 1 4v1l-1 1c-1 1-2 2-4 3v1c-1 0-2 1-3 2l-1 1 2 1c0 1 0 2-1 3h0l-1 2-1 1c0 1 0 1-1 2 0 1-1 1-2 1v1 2h-3c1-1 1-1 1-2v-1c-2 0-2 0-3 1v-1-1l-1-2h0c0-1 0-1-1-1l1 1h-1-1c1-1 0-2 0-3 1-1 2-2 2-3v-2c2 0 2 0 3-1l-2-2c0-1 0-1 1-2l-1-2c1-1 3-1 4-1l2-1 2-1z" class="C"></path><path d="M785 676l3 3c2-2 3-3 6-4 0-1 1-1 2-2 0 0 1 0 1 1l-2 3c-1 1-2 2-4 3v-1c-2 0-3 1-5 2 0 1 0 2-1 3h-3c-1 0-1 1-2 1v-2c2 0 2 0 3-1l-2-2c0-1 0-1 1-2l-1-2c1-1 3-1 4-1v1z" class="B"></path><path d="M781 676c1-1 3-1 4-1v1c0 1 0 2-1 3h-2l-1 1c0-1 0-1 1-2l-1-2z" class="I"></path><path d="M786 681c1 0 3 1 3 2-1 0-1 0-2 2v1l3-2 2 1c0 1 0 2-1 3h0l-1 2-1 1c0 1 0 1-1 2 0 1-1 1-2 1v1 2h-3c1-1 1-1 1-2v-1c-2 0-2 0-3 1v-1-1l-1-2h0c0-1 0-1-1-1l1 1h-1-1c1-1 0-2 0-3 1-1 2-2 2-3 1 0 1-1 2-1h3c1-1 1-2 1-3z" class="Q"></path><path d="M790 684l2 1c0 1 0 2-1 3h0c-1-1-1-1-3-1 0 1 0 1-1 2v1h-3 0c1-1 2-2 3-4l3-2z" class="D"></path><path d="M786 681c1 0 3 1 3 2-1 0-1 0-2 2v1c-1 2-2 3-3 4l-3 3-1-2h0c0-1 0-1-1-1l1 1h-1-1c1-1 0-2 0-3 1-1 2-2 2-3 1 0 1-1 2-1h3c1-1 1-2 1-3z" class="o"></path><path d="M778 691h1 1l-1-1c1 0 1 0 1 1h0l1 2v1 1c1-1 1-1 3-1v1c0 1 0 1-1 2h3c-1 1-2 1-2 2-1 2-3 4-4 5h-1c-1 0-1 1-2 2l1 1c-2 1-3 0-4 1-1 0-1 1-2 1h-1c-1 1 0 1-1 1s-1 0-1 1c-2 1-4 2-4 4-1 1-2 1-2 2-1 0-1 0-2-1h-2v1c0-2-1-2-2-2l1-2-1-1c0-2 0-4 1-6 1 1 6-4 8-5 0 0 1 0 2-1 1 0 4-2 4-4 0-1 1-1 1-2 2-2 2-3 5-3z" class="Q"></path><path d="M773 701c0 2 0 3-2 4l-2 1v-1h-2l1-1 1-1c2 0 3-1 4-2z" class="E"></path><path d="M775 700c2-2 2-2 2-4h0 1c1-1 2-2 3-2v1c1-1 1-1 3-1v1c0 1 0 1-1 2-1 0-2-1-3 0h-2c0 1-1 2 0 3v1c-1 0-2 1-3 2v-3z" class="C"></path><path d="M783 697h3c-1 1-2 1-2 2-1 2-3 4-4 5h-1c-1 0-1 1-2 2l1 1c-2 1-3 0-4 1-1 0-1 1-2 1h-1c-1 1 0 1-1 1s-1 0-1 1c-2 1-4 2-4 4-1 1-2 1-2 2-1 0-1 0-2-1h-2v1c0-2-1-2-2-2l1-2c0 1 0 1 1 1h0 1l1-3c2 0 3-1 5-1 1 0 2-2 3-3h1c1 0 1-1 2-1h2v-2h1v1h1c0-1 0-1 1-2h1l3-3c1-1 1-2 2-3h0z" class="P"></path><path d="M761 711c2 0 3-1 5-1-2 2-3 3-5 4l2 2v1c-1 0-1 0-2-1h-2v1c0-2-1-2-2-2l1-2c0 1 0 1 1 1h0 1l1-3z" class="D"></path><path d="M778 691h1 1l-1-1c1 0 1 0 1 1h0l1 2v1c-1 0-2 1-3 2h-1 0c0 2 0 2-2 4l-2 1c-1 1-2 2-4 2l-1 1-1 1-1-1-1 2h0 2v1c-2 0-2 0-3 1h-1c-1 0-2 1-3 2v1h1l-1 3h-1 0c-1 0-1 0-1-1l-1-1c0-2 0-4 1-6 1 1 6-4 8-5 0 0 1 0 2-1 1 0 4-2 4-4 0-1 1-1 1-2 2-2 2-3 5-3z" class="J"></path><path d="M360 676l1 1c1 0 2 1 3 2l1 1h1c1 1 1 1 2 1 2 0 3 0 5-1v-1c1 0 2-1 4-1 0-1 1-1 1-2l1 1h0c2-1 3-1 4-1s1 0 2 1h0c1 0 1 0 2 1h1c2 2 4 3 4 5 2 1 4 1 5 3 0 0 1 0 1 1h0v2l1 2 1 1 2 2h0c1 1 1 2 1 3h0v2 2h-1c0 1-1 2-1 3-2 2-4 2-5 5v1l2-1c0 1 0 1 1 1s1 1 2 1h0c1 0 2 1 3 2h-1l1 2 1 4 2 1h1v-1l1-1c0 1 0 1 1 1v1c2 0 3 1 5 1-1 1-1 1-1 2-3 4-4 6-4 10h-1v1 2c-1 0-1 0-2-1 0 0 1-1 1-2-1 0-2-1-2-1-1 0-1 0-1 1l1 1-1 1v1l-2 2v1h1 1l-1 2h-2-1l-1 1 1 1c-2 1-4 1-5 2l1 3 1-1h1l1-1 1 1-1 2c1 1 1 1 2 1l1 1-1 1h0 1v-1c1 0 1-1 2-1v1 3c2-1 2 0 3 0v2 3c1-1 2-1 3-2v2l1 1-1 2h1l1-1 1 1-1 1v3 1h0c-1 2-1 2-3 2v-1c0-1 0-2-1-3 0-1-1-1-1-2l-2-1h0l-2 2c-1 0-3 0-4 1-2-1-2-1-3-3v-1l1-2c-1-1-1-1-1-2h-3-1c0-1-1-1-2-1-2-1-4-2-6-2-1 1-3 3-5 3v-3-2-1c-1-1-1-2-1-3-2-4-4-4-7-6v-1l-2-2c-4-2-5-7-10-9-4-2-8 1-12 3l-1-1c-3-1-3-1-5-1h-2c-2 0-3 0-5 1v-1h-1c-1 1-2 1-3 2v-1h-2l-1-2c2-2 4-3 5-6h-1l-5 3v-1h0-2 0v-1c-2-1-3-1-4 0l-1-1 1-1h-1l-2 1h-1v-1h-1c-1 1-1 1-2 0l-1 1v-1c0-1-1-1-2-1-1-1-1-1-1-2l1-1s-1 0-1-1c-1-1-2-1-3-3h0c-1 0-1 0-2-1h-1-2 0l1-2-1-1c-1-1-1-1-1-2v-1c3 1 4 1 6 2h2l1-1v-1-3l2-1c0 1 1 1 2 1v-2c0-1 0-2 1-3 0-2 0-2 1-4l1 1v1h1l2-1h0v2h3c2 1 4 2 4 4v2h1c0 1 1 2 1 3h1 0c1-3 2-5 4-6l2 2h1l-1-2 1-1h4 1l-1-1 2-1c1 2 6 1 8 1l1-1h1c0-1 1-1 1-2 2-1 2-1 3-2v-1-1c1 0 2 0 3-1s1-2 3-3v1l3 1 1-2h-1c-1-1-1-1-2 0l-1-1c1-1 1-1 2-1v-1l-1-1v-3c-1-2-3-4-4-5v-1z" class="Q"></path><path d="M391 707c0-1 0-1 1-2l3-1h0c0 2-1 3-2 5l-1 1h-1v-3z" class="I"></path><path d="M401 711c1 0 2 1 3 2h-1l-1-1c-2 2-2 3-5 3h0l-3 1-1-1h1c0-1 1-1 1-1h1c2-1 4-1 5-3h0z" class="K"></path><path d="M397 692l1 1h2c1 2 1 2 2 3v3c-1 2-3 2-5 2 1-1 2-4 2-6h-1c0-1-1-2-1-3z" class="H"></path><path d="M389 721c1 0 2 1 3 2 0 2 0 2-1 3h0-1s-1-2-2-2h-2c-2 0-6 0-8-2h6 0c2 1 3 1 4 1l1-2z" class="C"></path><path d="M379 720c3 0 7 0 10 1l-1 2c-1 0-2 0-4-1h0-6c-1 0-1-1-1-2h2z" class="F"></path><path d="M377 717h0-4v-1c1-1 2-2 3-2s2 0 3-1c1 0 2-1 3-1l1 1c1 0 2 0 2-1h1c3 1 5 1 8 3h-1-4c-2-1-6-1-8 0-1 1-2 1-4 2z" class="C"></path><path d="M310 705l2-1 1 2h0c2 1 3 1 4 2h2v-2l1 1c1 1 0 2 1 2l2 1 2 2c1 0 1-1 2-2 1 0 1 0 1-1l1-1h1l-1 1c0 1 0 1-1 2h0c0 2-2 1-3 2l1 1-1 1-1 1c0-1-1-2-1-3v-2h-1c-1 0-1 0-2 1-2-2 0-2-1-3h-2-2-2l-1-2-2 1v-3z" class="E"></path><path d="M398 695h1c0 2-1 5-2 6s-1 2-2 3h0l-3 1c-1 1-1 1-1 2-2 1-6 4-8 4l-1-1c1-2 2-3 5-3l1-2 1-1c1 0 2-1 3-1h2c2-2 3-5 3-7l1-1z" class="h"></path><path d="M334 702l2 2h1l-1-2 1-1h4c-1 2-2 3-2 5l1 1c1 0 1 1 1 2h-4v-1l-1 1c-1-1-1-1-2-1l-1 1c-2 0-2 0-3-1 1-3 2-5 4-6z" class="D"></path><path d="M314 703c0 1 0 1 1 2l1-1c1 0 7 0 9 1 1 0 2 3 3 4 0 1 0 1-1 1-1 1-1 2-2 2l-2-2-2-1c-1 0 0-1-1-2l-1-1v2h-2c-1-1-2-1-4-2h0l-1-2c0 1 1 1 2 1v-2z" class="o"></path><path d="M315 700c0-2 0-2 1-4l1 1v1h1l2-1h0v2h3c2 1 4 2 4 4v2h1c0 1 1 2 1 3l-1 1c-1-1-2-4-3-4-2-1-8-1-9-1l-1 1c-1-1-1-1-1-2s0-2 1-3z" class="V"></path><path d="M310 708l2-1v1c0 1 0 2-1 3h1l2-1c1 1 1 2 2 3 1 0 1 0 2-1l1 1-1 1v1c2 0 2 0 3 2l1 1 2 3-7-4-1 2 1 1v1c-1 0-1 1-1 2h0c-1 1-1 1-2 0l-1 1v-1c0-1-1-1-2-1-1-1-1-1-1-2l1-1s-1 0-1-1c-1-1-2-1-3-3h0c-1 0-1 0-2-1h-1-2 0l1-2-1-1c-1-1-1-1-1-2v-1c3 1 4 1 6 2h2l1-1v-1z" class="K"></path><path d="M303 712l1-2c1 0 1 0 2 1l2 2c-1 1-1 1-1 2h0 0c-1 0-1 0-2-1h-1-2 0l1-2z" class="L"></path><path d="M307 715l2-2 1-1v1 2h2c0-1 0-1 1-2l1 1s1 1 2 1h0v1l1 1h0l-1 2 1 1v1c-1 0-1 1-1 2h0c-1 1-1 1-2 0l-1 1v-1c0-1-1-1-2-1-1-1-1-1-1-2l1-1s-1 0-1-1c-1-1-2-1-3-3h0z" class="Z"></path><path d="M314 714s1 1 2 1h0v1l1 1h0l-1 2-2-1h-1l1-2v-2z" class="R"></path><path d="M311 719v-1c2 1 2 2 3 3h1l1-2 1 1v1c-1 0-1 1-1 2h0c-1 1-1 1-2 0l-1 1v-1c0-1-1-1-2-1-1-1-1-1-1-2l1-1z" class="T"></path><path d="M352 699c1 2 2 2 2 4 0 0-1 0-1 1h0l2 2v-1l2-2h3c1 2 0 5 2 6h2c0 1 0 1 1 2 0 0 1 1 2 1v2h6c0 1-1 1-2 2v-1h-1-1-1 0c-1 0-2 0-3-1h-1-2c-3-1-5-4-7-6l-1 2h-1c-1-1-1-1 0-2l-1-1-2 2-1-1c-1 1-1 0-2 1h0-2c0 1 0 1-1 2-2 0-2-1-3-2 0-1 0-2-1-2l-1-1c0-2 1-3 2-5h1l-1-1 2-1c1 2 6 1 8 1l1-1z" class="D"></path><path d="M352 699c1 2 2 2 2 4 0 0-1 0-1 1l-3-3-1 1c-1 0-2 0-4-1h-2-1l-1-1 2-1c1 2 6 1 8 1l1-1z" class="E"></path><path d="M342 701h1c-1 1-1 2-1 3l2 1s1-1 2-1v1c0 1-1 2-1 4 0 1 0 1-1 2-2 0-2-1-3-2 0-1 0-2-1-2l-1-1c0-2 1-3 2-5h1z" class="K"></path><path d="M332 715h0l-1-2h0c1-1 2-2 3-2s1 1 2 1 3 0 4 1c1 0 1 1 2 2h0 1v1h1c2 1 3 1 4 3h2c1 1 3 1 4 1l-1 1s-1 0-1 1h-1c-1-1-1-1-2-1v-1c-1 0-2 1-2 1-1-1-1-1-1-2h-1v2c-1-1-1-1-1-2h-2v-1c-1 1-1 1-1 2l-1-1-2 1v3l-1-1v-1l-2 1 1 2h-1-2-1l-5 3v-1h0-2 0v-1c-2-1-3-1-4 0l-1-1 1-1h-1l-2 1h-1v-1h-1 0c0-1 0-2 1-2v-1l-1-1 1-2 7 4-2-3h3c1 0 1-1 2-1l1-1c0-1 0-1 1-1v-1c1 0 2 1 3 1z" class="L"></path><path d="M332 724l2-2c0-2 0-2-1-3l1-1c1 1 1 1 3 1v1h1v3l-1-1v-1l-2 1 1 2h-1-2-1z" class="a"></path><path d="M332 715h0l-1-2h0c1-1 2-2 3-2s1 1 2 1 3 0 4 1c1 0 1 1 2 2h0l-1 2c-1 0-2-1-3-2v-1h-1v3h-1l-1-3h0l-2 2-1-1z" class="R"></path><path d="M322 718h3c1 0 1-1 2-1l1-1c0-1 0-1 1-1v-1 1c1 0 1 0 2 1s1 2 1 3c-1 2-2 3-3 5-2 0-2 0-3-1l-1-2h-1l-2-3z" class="P"></path><path d="M328 717c1 0 1 0 3 1l-1 1-1 1h-3v-1l2-2z" class="Q"></path><path d="M397 715h0c3 0 3-1 5-3l1 1 1 2 1 4 2 1h1v-1l1-1c0 1 0 1 1 1v1c2 0 3 1 5 1-1 1-1 1-1 2-3 4-4 6-4 10h-1v1 2c-1 0-1 0-2-1 0 0 1-1 1-2-1 0-2-1-2-1-1 0-1 0-1 1l1 1-1 1v1l-2 2v1h1 1l-1 2h-2-1l-1-2h-1c0-2 0-3-1-4-1 0-1-1-2-1l1-1v-2c-1-1-3-2-4-3l-2-2h0c1-1 1-1 1-3-1-1-2-2-3-2-3-1-7-1-10-1-1-1-1-2-2-3 2-1 3-1 4-2 2-1 6-1 8 0h4l1 1 3-1z" class="D"></path><path d="M394 716l3-1-1 3c0 1 0 1 1 2l-1 1h-1c-1-1-1-2-2-2l-1-1 2-2z" class="I"></path><path d="M396 723h7l-2 2c1 1 2 1 3 2-2 1-3 0-4 2h-1c0-1 0-1-1-2l-4 1-1-2 3-3z" class="E"></path><path d="M397 733l2-1 3 3v-1-1-1c1 0 2 1 2 2h0l1 1v1l-2 2v1h1 1l-1 2h-2-1l-1-2h-1c0-2 0-3-1-4-1 0-1-1-2-1l1-1z" class="Z"></path><path d="M397 715h0c3 0 3-1 5-3l1 1 1 2 1 4 2 1h1v-1l1-1c0 1 0 1 1 1v1c2 0 3 1 5 1-1 1-1 1-1 2-3 4-4 6-4 10h-1v1 2c-1 0-1 0-2-1 0 0 1-1 1-2l1-1v-1c-1-1-1-1-2-1-2-1-2-2-2-3h-1c-1-1-2-1-3-2l2-2h-7l1-1-1-1 1-1c-1-1-1-1-1-2l1-3z" class="B"></path><path d="M403 723c1 0 2 1 3 1l1 1v1l-2 1h-1c-1-1-2-1-3-2l2-2z" class="H"></path><path d="M397 715h0c3 0 3-1 5-3l1 1 1 2c-1 0-2 1-2 1l-2-1c-1 1-1 1-1 2s-1 1-2 3c-1-1-1-1-1-2l1-3z" class="F"></path><path d="M360 676l1 1c1 0 2 1 3 2l1 1h1c1 1 1 1 2 1 2 0 3 0 5-1v-1c1 0 2-1 4-1 0-1 1-1 1-2l1 1h0c2-1 3-1 4-1s1 0 2 1h0c1 0 1 0 2 1h1c2 2 4 3 4 5l-1 2c3 2 5-1 6 5v1 1c0 1 1 2 1 3l-1 1c0 2-1 5-3 7h-2c-1 0-2 1-3 1l-1 1h-2c-2 1-4 2-5 3h-1c-2 2-4 2-6 3 0 1 0 0 1 1l-1 1-1 1h0-6v-2c-1 0-2-1-2-1-1-1-1-1-1-2h-2c-2-1-1-4-2-6h-3l-2 2v1l-2-2h0c0-1 1-1 1-1 0-2-1-2-2-4h1c0-1 1-1 1-2 2-1 2-1 3-2v-1-1c1 0 2 0 3-1s1-2 3-3v1l3 1 1-2h-1c-1-1-1-1-2 0l-1-1c1-1 1-1 2-1v-1l-1-1v-3c-1-2-3-4-4-5v-1z" class="s"></path><path d="M386 705l1-1c1-1 3-2 5-2h1c1-1 1-1 1-3l1-1c0-1 1-2 2-2 0 2-1 5-3 7h-2c-1 0-2 1-3 1l-1 1h-2z" class="J"></path><path d="M368 696l3-1c1-1 1-1 3-1v1c1 1 1 2 1 3h0v-1l1 2 1-1c1-1 3-1 5-2-2 2-5 6-8 6h-1-1c-1 1-2 2-4 3 0 0 1 0 1 1l-1-1h-1c1-1 1-2 2-2s1-1 2-1v-4l-3-2z" class="H"></path><path d="M360 676l1 1c1 0 2 1 3 2l1 1h1c1 1 1 1 2 1 2 0 3 0 5-1v-1c1 0 2-1 4-1 0-1 1-1 1-2l1 1h0c2-1 3-1 4-1-1 1-2 2-3 2v4c-1 1-1 3-2 4l1 1c1 0 1-1 3-1v1l-2 2 1 2h1c0 1-1 2-2 3s-3 2-5 3v1h0c0-1 0-2-1-3v-1h1v-1h-2l-1-2 1-1v-2c-1 0-3 1-4 1h-2-1c-1-1-1-1-2 0l-1-1c1-1 1-1 2-1v-1l-1-1v-3c-1-2-3-4-4-5v-1z" class="F"></path><path d="M369 689c0-1-1-2 0-3 0 0 1 0 1-1 1 1 2 1 3 2v1h0c-1 0-3 1-4 1z" class="s"></path><path d="M380 689l-2 3c-2-2-2-1-4-3l1-1c1-1 2-2 3-2l1 1c1 0 1-1 3-1v1l-2 2zm-11 0c1 0 3-1 4-1v2l-1 1 1 2h2v1h-1c-2 0-2 0-3 1l-3 1 3 2v4c-1 0-1 1-2 1s-1 1-2 2h1l1 1h1c2 1 2 2 4 3h0c2-1 4-1 6-1h1-1c-2 2-4 2-6 3 0 1 0 0 1 1l-1 1-1 1h0-6v-2c-1 0-2-1-2-1-1-1-1-1-1-2h-2c-2-1-1-4-2-6h-3l-2 2v1l-2-2h0c0-1 1-1 1-1 0-2-1-2-2-4h1c0-1 1-1 1-2 2-1 2-1 3-2v-1-1c1 0 2 0 3-1s1-2 3-3v1l3 1 1-2h2z" class="E"></path><path d="M368 696c-1 0-3-1-4-2 0-1 2-2 3-3s3-1 4-1l1 1 1 2h2v1h-1c-2 0-2 0-3 1l-3 1z" class="J"></path><path d="M352 699h1c0-1 1-1 1-2 2-1 2-1 3-2v-1-1c1 0 2 0 3-1s1-2 3-3v1 2c-1 1-1 1-2 1l1 1v1h-1-2l2 2 2-1 1 1c-1 1 0 1-1 1-1 1-1 2-2 3l-1 1c3 2 4 3 5 7 2 0 2 0 4 1 0 0 1 0 2 1h0l1 2v-1h1 0l1 1-1 1h0-6v-2c-1 0-2-1-2-1-1-1-1-1-1-2h-2c-2-1-1-4-2-6h-3l-2 2v1l-2-2h0c0-1 1-1 1-1 0-2-1-2-2-4z" class="I"></path><path d="M338 720l2-1 1 1c0-1 0-1 1-2v1h2c0 1 0 1 1 2v-2h1c0 1 0 1 1 2 0 0 1-1 2-1v1c1 0 1 0 2 1h1c0-1 1-1 1-1l1-1h1c1 0 2 0 3-1v2l2-1c1 0 2-1 3 0l-1 1h2v2-1c1 0 2 0 3-1v1l1 1v-1h2 1 3v-1l1 1c-1 1-1 2-1 3l1-1c2 1 2 1 4 0l2 1h0c2 0 6-1 8 0l1 1h0 1l2 2c1 1 3 2 4 3v2l-1 1c1 0 1 1 2 1 1 1 1 2 1 4h1l1 2-1 1 1 1c-2 1-4 1-5 2l1 3 1-1h1l1-1 1 1-1 2c1 1 1 1 2 1l1 1-1 1h0 1v-1c1 0 1-1 2-1v1 3c2-1 2 0 3 0v2 3c1-1 2-1 3-2v2l1 1-1 2h1l1-1 1 1-1 1v3 1h0c-1 2-1 2-3 2v-1c0-1 0-2-1-3 0-1-1-1-1-2l-2-1h0l-2 2c-1 0-3 0-4 1-2-1-2-1-3-3v-1l1-2c-1-1-1-1-1-2h-3-1c0-1-1-1-2-1-2-1-4-2-6-2-1 1-3 3-5 3v-3-2-1c-1-1-1-2-1-3-2-4-4-4-7-6v-1l-2-2c-4-2-5-7-10-9-4-2-8 1-12 3l-1-1c-3-1-3-1-5-1h-2c-2 0-3 0-5 1v-1h-1c-1 1-2 1-3 2v-1h-2l-1-2c2-2 4-3 5-6h2 1l-1-2 2-1v1l1 1v-3z" class="U"></path><path d="M375 733c1-2 1-1 2-2v1 1l3 2 2-1v2h2c0 1 0 1 1 2l1-1h2 0l-2 2c1 1 1 1 2 1l1 1-1 1v1h-1c0-1 0-1-1-1h0-1v-2h0c-1 0-2 1-3 1v-2h0v-1c-1 1-2 2-3 2v-1c0-1 0-1-1-2l-2 1c0-1 0-1 1-2h0v-1h-2c1-1 1 0 0-1v-1z" class="q"></path><path d="M370 739l2-2c1 0 1 1 2 1h1c0 1-1 2 0 3 0 0 1-1 1-2l1 1v3h0l3-2v3l-1 1v1h3l1 1-1 1-1 1h2v-1l2-1v1c0 1-1 2-2 3h-3c-1-1-1-2-1-3-2-4-4-4-7-6v-1l-2-2z" class="d"></path><path d="M338 720l3 1h0v2h1c1 0 1 1 2 1h2v1c1 0 1-1 1-2 1 1 1 1 2 1h1l1 1-1 2v1l1-1 1 1h1v1c-2 0-3 0-4 1h-3-1v-4h-1 0c-1 0-2 0-2 1h-2v-1c-1 0-1 0-1 1-1 0-1 1-2 1v1l-2 2h-1c-1 1-2 1-3 2v-1h-2l-1-2c2-2 4-3 5-6h2 1l-1-2 2-1v1l1 1v-3z" class="r"></path><path d="M370 722h1 3v-1l1 1c-1 1-1 2-1 3s0 1 2 2h1v1h1 3 0v2l2 2c0 1 0 1-1 2l-2 1-3-2v-1-1c-1 1-1 0-2 2l-1 1h-1l1-1-1-1-1 2-1-1c1-1 1-1 0-2l-1 1c0 1-1 1-1 2l-1-1 2-2h-1l-1-1c-1 0-1 0-2 1l-1-1c1 0 1-1 2-2l-1-1v1l-1-1c0-1 0 0 1-1v-1-1l-1 2-1-1 3-3 1 1v-1h2z" class="L"></path><path d="M367 722l1 1v-1h2c0 3-2 3-3 6l1 1c1-1 1-2 2-3h1c0 2 1 2 1 4l2-2h2v2c-1 1-2 2-2 4h-1l1-1-1-1-1 2-1-1c1-1 1-1 0-2l-1 1c0 1-1 1-1 2l-1-1 2-2h-1l-1-1c-1 0-1 0-2 1l-1-1c1 0 1-1 2-2l-1-1v1l-1-1c0-1 0 0 1-1v-1-1l-1 2-1-1 3-3z" class="a"></path><path d="M338 720l2-1 1 1c0-1 0-1 1-2v1h2c0 1 0 1 1 2v-2h1c0 1 0 1 1 2 0 0 1-1 2-1v1c1 0 1 0 2 1h1c0-1 1-1 1-1l1-1h1c1 0 2 0 3-1v2l2-1c1 0 2-1 3 0l-1 1h2v2-1c1 0 2 0 3-1v1l-3 3 1 1 1-2v1 1c-1 1-1 0-1 1l1 1v-1l1 1c-1 1-1 2-2 2l-1-2h-8l-3 1v-1h-1l-1-1-1 1v-1l1-2-1-1h-1c-1 0-1 0-2-1 0 1 0 2-1 2v-1h-2c-1 0-1-1-2-1h-1v-2h0l-3-1z" class="N"></path><path d="M351 727h1v-3h1 0c1 1 2 1 2 1 1 1 1 2 1 3l-3 1v-1h-1l-1-1z" class="T"></path><path d="M374 725l1-1c2 1 2 1 4 0l2 1h0c2 0 6-1 8 0l1 1h0 1l2 2c1 1 3 2 4 3v2l-1 1c1 0 1 1 2 1 1 1 1 2 1 4h1l1 2-1 1 1 1c-2 1-4 1-5 2l1 3s1 1 0 1c-1 1-2 2-3 2 0-1 0-2-1-3 0-1-2-3-3-3h-1l-1-2v-1l1-1-1-1c-1 0-1 0-2-1l2-2h0-2l-1 1c-1-1-1-1-1-2h-2v-2c1-1 1-1 1-2l-2-2v-2h0-3-1v-1h-1c-2-1-2-1-2-2z" class="N"></path><path d="M386 737h-1c1-2 3-3 5-4 2 2 3 2 4 5h0c-2 1-2 1-3 2v1c1 1 2 1 3 3l2 1 1 3s1 1 0 1c-1 1-2 2-3 2 0-1 0-2-1-3 0-1-2-3-3-3h-1l-1-2v-1l1-1-1-1c-1 0-1 0-2-1l2-2h0-2z" class="K"></path><path d="M391 741c1 1 2 1 3 3l-1 1c-2 0-2-2-3-3l1-1z" class="B"></path><path d="M374 725l1-1c2 1 2 1 4 0l2 1h0c2 0 6-1 8 0l1 1h0 1l2 2c1 1 3 2 4 3l-2 1c-1 0-1-1-2-1v-2c-1 0-3 1-4 1v1l-1-1c-1 0-1 1-2 2l2 1 1-1 1 1c-2 1-4 2-5 4h1l-1 1c-1-1-1-1-1-2h-2v-2c1-1 1-1 1-2l-2-2v-2h0-3-1v-1h-1c-2-1-2-1-2-2z" class="R"></path><path d="M386 742h0c1 0 1 0 1 1h1l1 2h1c1 0 3 2 3 3 1 1 1 2 1 3 1 0 2-1 3-2 1 0 0-1 0-1l1-1h1l1-1 1 1-1 2c1 1 1 1 2 1l1 1-1 1h0 1v-1c1 0 1-1 2-1v1 3c2-1 2 0 3 0v2 3c1-1 2-1 3-2v2l1 1-1 2h1l1-1 1 1-1 1v3 1h0c-1 2-1 2-3 2v-1c0-1 0-2-1-3 0-1-1-1-1-2l-2-1h0l-2 2c-1 0-3 0-4 1-2-1-2-1-3-3v-1l1-2c-1-1-1-1-1-2h-3-1c0-1-1-1-2-1-2-1-4-2-6-2-1 1-3 3-5 3v-3l2-1h0l2-2 2 1c1-1 0-1 1-3l2 3c1-1 2-1 3-2l-1-1c-1 1-1 1-2 1 0-1 1-1 1-2s-2-1-3-2v-2h-1v-1-1z" class="N"></path><path d="M398 759l1-1 1-1c0 2 0 1-1 2 1 1 1 1 2 1 2 1 3 1 5 2l-2 2c-1 0-3 0-4 1-2-1-2-1-3-3v-1l1-2z" class="M"></path><path d="M380 754l2-1h0l2-2 2 1c1-1 0-1 1-3l2 3-1 1h1c1-1 2-1 4-1l1 1-2 1h0c0 1-1 1-1 2-2-1-4-2-6-2-1 1-3 3-5 3v-3z" class="p"></path><path d="M640 572c0 1 0 1 1 2 0-1 0-1 1-2h1 1 2c1-1 1-1 2-1h2 2l2-1v1l1 1h1c4 0 11 0 16 1 3 1 7 2 11 3h0v-1c1 1 1 2 1 2 3 2 6 4 8 7 1 1 4 5 4 7-2 3-6 4-8 7l-1 3v3c0 1 1 2 1 3 1 1 0 4-1 5 0 1 0 1-1 2v2 7l1 1c1 4 1 9 1 13 2 1 4 1 5 2 2 3 4 7 3 10v4l-1 1c-2 1-5 2-6 4h-2v-1c-1-2 0-2-2-3-9-1-18-2-28-1-2 1-7 0-10 1v2c-2-1-1 0-2-1-2 0-3 1-5 3-1 0-1-1-3 0v2c-1 0-1 0-1-1v-1c-2-2-5-3-7-5-1-1-1-1-1-2-1-2 0-10 1-12 1-1 2-2 4-2h0c-1 1-2 1-2 3h0c2 0 5-1 7-2l-1-1v-1-1-5c1-2 0-6 1-8 1-5 0-6-2-10-1-2-1-4 0-6 0-2 0-3 1-4 1 0 2-2 2-3v-1c-1-1-1-1-1-2v-1c1-1 2-1 3-2h-1-5c-2 0-3 0-5-1l-1 1c-1 0-1-1-2-2 0-2 2-4 4-6 2-4 5-6 9-9 0-1-1-3 0-4h0z" class="c"></path><path d="M651 597c2 0 4 0 5 1l-1 1-2 1h0 0v-1l-2-1v-1z" class="X"></path><path d="M687 601c-2-1-3-2-4-4h1 1 1 2v1l-1 3z" class="f"></path><path d="M656 598c2-1 2-2 3-2l2 2c-1 1-2 2-3 2l-2 1-3-1h0l2-1 1-1z" class="e"></path><path d="M661 598c4-1 12-1 17 1 3 0 4-1 6 2v1c-1 0-1 0-1 1h0-1v-2h-1-2 0c-1 0-1 1-1 1-1 0-1-1-1-1-2-1-3-1-5-1h-3c-1 1-2 0-3 0h-8c1 0 2-1 3-2z" class="M"></path><path d="M639 598c2-1 3-2 5-2 1 0 3-1 5-1h4v1h0-2v1 1l2 1v1 1c-2 0-3 1-5 1h-2l-7 1c-2 1-1 2-3 3 0-2 0-3 1-4 1 0 2-2 2-3v-1z" class="d"></path><path d="M639 603v-1h1c3-2 4-3 7-3l-1 3-7 1z" class="f"></path><path d="M647 599h1c1-1 2-1 3-1l2 1v1 1c-2 0-3 1-5 1h-2l1-3zm-17-7c2-2 13-3 16-4l1 1h11c2 0 4 0 6 1l14 1 3 1c2 1 3 1 4 2v1l-35-1c-3 0-6 1-9 1h-1-2c1-1 2-1 3-2h-1-5c-2 0-3 0-5-1z" class="W"></path><path d="M640 593l3-1h0 6 3l1 1c-1 0-2 0-3 1-3 0-6 1-9 1h-1-2c1-1 2-1 3-2h-1z" class="f"></path><path d="M630 592c2-2 13-3 16-4l1 1h11c2 0 4 0 6 1-7 1-15 0-21 2l-3 1h-5c-2 0-3 0-5-1z" class="N"></path><path d="M648 602c2 0 3-1 5-1v4c-1 1-2 0-3 1v1c1 0 2 0 3 1v4h-2v1c0 1 0 1-1 1l-2 1-3 1c-1 0-2 1-3 2 0 0 1 1 0 2h-2c0 1 0 1-2 2 1-5 0-6-2-10-1-2-1-4 0-6 2-1 1-2 3-3l7-1h2z" class="T"></path><path d="M637 608v1 2c1 0 1 0 2-1 0-1-1-1 1-2 0 1-1 3-1 4l-1 1 1 1h2c0 1 0 0-1 1 1 1 1 1 2 1v2s1 1 0 2h-2c0 1 0 1-2 2 1-5 0-6-2-10v-3l1-1z" class="e"></path><path d="M646 602h2v1h3v1h-3-1l-4 2c-2 0-2-1-3-1l-1 1c-1 1-1 1-2 1v1l-1 1v3c-1-2-1-4 0-6 2-1 1-2 3-3l7-1z" class="U"></path><path d="M658 600h8c1 0 2 1 3 0h3c2 0 3 0 5 1 0 0 0 1 1 1 0 0 0-1 1-1h0 2 1v2h1 0l1-1v3c-1 0-1-1-2-1-3 0-6-2-9-2h-2c0-1-1 0-2 0-1 1 0 7 0 9v1h1v6 1c1 0 1 1 2 1l1-1 2 1h1l1 1v1c-2 0-5 0-8-1h-4c-3-1-9-1-11 0h-1v-3s-1-1 0-2v-4-4c-1-1-2-1-3-1v-1c1-1 2 0 3-1v-4-1h0l3 1 2-1z" class="G"></path><path d="M653 600l3 1h-1c-1 6 0 11 0 17 0 1 1 1 3 1 1 0 2-1 3 0h9c1 0 1 1 2 1l1-1 2 1h1l1 1v1c-2 0-5 0-8-1h-4c-3-1-9-1-11 0h-1v-3s-1-1 0-2v-4-4c-1-1-2-1-3-1v-1c1-1 2 0 3-1v-4-1h0z" class="p"></path><path d="M670 618c-1-1-1 0-2-1v-1c-1 0-1 1-2 1s-1-1-2-1c-1 1-1 1-2 1h-1c-1-3 0-9 0-12h-1v11c-1 2-1 1-3 2 0-1-1-1-1-1v-5-9l11-1v12l1 1 1-1v-2h1v6z" class="n"></path><path d="M669 612v-1c0-2-1-8 0-9 1 0 2-1 2 0h2c3 0 6 2 9 2 1 0 1 1 2 1l1 1c0 1 0 0 1 1v4h1v1c0 1 0 1-1 2v2 7l1 1c1 4 1 9 1 13 2 1 4 1 5 2 2 3 4 7 3 10l-1-1-2-2c0-1-1-2-2-2 0-1-1-2-2-3l-1 1v3h0c-1-1-1-4-1-6l-8-1c0-1 0-1 1-1h3c1-2 0-4 0-6v-4-2h0c-1-2-3-2-5-3h-1v-1l-1-1h-1l-2-1-1 1c-1 0-1-1-2-1v-1-6h-1z" class="S"></path><path d="M682 615l2 1c1 2 0 3 0 4l-3-3v-1l1-1z" class="C"></path><path d="M672 604l1-1h0l1 1h2c1 1 2 1 3 1 2 1 4 2 5 2 1 1 1 1 1 3h-1c-1 0-2 0-3-1h-1c-3 0-4-2-7-2-1-1-1-1-1-3z" class="Q"></path><path d="M670 612l1-1h1l-2-1v-1-5h2c0 2 0 2 1 3 2 3 10 3 11 5 1 1 1 1 1 2-1 1-2 1-3 1l-1 1v1l3 3v3h-1c-1-1-2-1-3-2l-2 1h-1v-1l-1-1h-1l-2-1-1 1c-1 0-1-1-2-1v-1-6z" class="D"></path><path d="M681 617h-2c0 1 0 0-1 1l-2-1-1-1 1-1c-1-1 0-1-1-2h2c1 1 1 1 2 1l2 2v1z" class="B"></path><path d="M648 615l2-1c1 0 1 0 1-1v-1h2v4c-1 1 0 2 0 2v3h1c2-1 8-1 11 0h4c3 1 6 1 8 1h1c2 1 4 1 5 3h0v2 4 4c-3 0-6-1-8-1-4 0-8-1-12-1-2 0-3 0-5-1h-3v-1l-2 1-1-1c-2 0-3 0-4 1h-1v-1-1l-4-1c1 0 0-1 0-1 0-1 0-3 1-4l-2-4c1-1 0-2 0-2 1-1 2-2 3-2l3-1z" class="j"></path><path d="M669 621c3 1 6 1 8 1h1c2 1 4 1 5 3h0v2h-3-4c0-1 0-1-1-1h-2c-2-1-6 0-8-1l1-1h5c1 1 1 1 2 1l1-1h1 0c1 0 1 1 1 1 1 0 2 0 3-1h-1c-2-1-7-1-10-1v-1l1-1z" class="B"></path><path d="M648 615l2-1c1 0 1 0 1-1v-1h2v4c-1 1 0 2 0 2v3h1c2-1 8-1 11 0h-10l-1 1c2 0 5 0 7 1-2 1-4-1-7 1v1c1 1 1 0 1 1h1 1c0-1 0-1 1-1h1v2s2 0 2-1c1 0 1-1 2-1 1 1 1 2 2 2 0 2 0 3-2 4-2 0-2 0-4-1v-2h-1c-1 1 0 2-1 3-1-1-1-2-2-2v2h0l-2 1-1-1c-2 0-3 0-4 1h-1v-1-1l-4-1c1 0 0-1 0-1 0-1 0-3 1-4l-2-4c1-1 0-2 0-2 1-1 2-2 3-2l3-1z" class="N"></path><path d="M648 615l1 1-2 2h-1c-1 0-2 1-2 1l1 1c1-1 2 0 3-1 1 0 2-2 3-3l1 1-1 1v1h-1c-1 1-1 1-2 1s-1 1-1 2c-1 1-2 1-3 2l-2-4c1-1 0-2 0-2 1-1 2-2 3-2l3-1z" class="L"></path><path d="M640 572c0 1 0 1 1 2 0-1 0-1 1-2h1 1 2c1-1 1-1 2-1h2 2l2-1v1l1 1h1c4 0 11 0 16 1 3 1 7 2 11 3h0v-1c1 1 1 2 1 2 3 2 6 4 8 7 1 1 4 5 4 7-2 3-6 4-8 7v-1h-2c0-1 0-1-1-2v-1c-1-1-2-1-4-2l-3-1-14-1c-2-1-4-1-6-1h-11l-1-1c-3 1-14 2-16 4l-1 1c-1 0-1-1-2-2 0-2 2-4 4-6 2-4 5-6 9-9 0-1-1-3 0-4h0z" class="W"></path><path d="M666 581l1-3-1-1c2 0 4 0 6 1v4h-1l-1 1c-1-1-1-1-2-1h0l-1-1h-1z" class="Q"></path><path d="M672 573c3 1 7 2 11 3h0v-1c1 1 1 2 1 2 3 2 6 4 8 7-1 0-2 1-2 2h0l-1 1c-1-1-2-3-3-4h-1l-2-2c-2-1-4-2-7-3-1-1-2-1-4-2-1 0-2 0-3-1h-3-3c-1 1-4 0-6 0h-1l1-1c2 0 5-1 7-1s5 1 7 1l1-1z" class="S"></path><path d="M653 583c0-2 0-4 1-6 1 0 1 0 3 1 1 0 1-1 2-1h0 1 5c1 2 0 3 1 5v-1h1l1 1h0c1 0 1 0 2 1l1-1h1l1 2c1 0 2 1 3 0 0-1-1-1-2-2v-2-1-1l3 2 1 1h1c0 2-1 3-2 4l1 1 1-1c1-1 1-2 2-3v2c0 1 0 2-1 2h-3c-7-1-16-1-24 0h-4-1v-1l3-3c-1-1 0-2 0-3l1-1 1 1v4h0z" class="P"></path><path d="M648 585l3-3c-1-1 0-2 0-3l1-1 1 1v4h0 1 4l1-1c-1-2-1-2 0-4v1c0 2 0 2 1 3 1 0 1 1 2 0h0c1 1 2 1 3 1h0c-4 1-7 1-11 1h-2c-1 1-2 0-4 1z" class="u"></path><path d="M676 578c3 1 5 2 7 3l2 2h1c1 1 2 3 3 4l1-1h0c0-1 1-2 2-2 1 1 4 5 4 7-2 3-6 4-8 7v-1h-2c0-1 0-1-1-2v-1c-1-1-2-1-4-2l-3-1-14-1c-2-1-4-1-6-1h-11l-1-1h4l1-1-2-1h4c8-1 17-1 24 0h3c1 0 1-1 1-2v-2c-1 1-1 2-2 3l-1 1-1-1c1-1 2-2 2-4h-1l-1-1-1-2z" class="X"></path><path d="M650 588c9-1 18-1 27 0 3 0 6 1 9 2 2 0 4 1 6 2-4 1-7 0-10-1h-4l-14-1c-2-1-4-1-6-1h-11l-1-1h4z" class="L"></path><path d="M638 622c2-1 2-1 2-2h2l2 4c-1 1-1 3-1 4 0 0 1 1 0 1l4 1v1 1h1c1-1 2-1 4-1l1 1 2-1v1h3c2 1 3 1 5 1 4 0 8 1 12 1 2 0 5 1 8 1v-4c0 2 1 4 0 6h-3c-1 0-1 0-1 1l8 1c0 2 0 5 1 6h0v-3l1-1c1 1 2 2 2 3 1 0 2 1 2 2l2 2 1 1v4l-1 1c-2 1-5 2-6 4h-2v-1c-1-2 0-2-2-3-9-1-18-2-28-1-2 1-7 0-10 1v2c-2-1-1 0-2-1-2 0-3 1-5 3-1 0-1-1-3 0v2c-1 0-1 0-1-1v-1c-2-2-5-3-7-5-1-1-1-1-1-2-1-2 0-10 1-12 1-1 2-2 4-2h0c-1 1-2 1-2 3h0c2 0 5-1 7-2l-1-1v-1-1-5c1-2 0-6 1-8z" class="d"></path><path d="M674 650c5 0 10 2 15 3 1 1 2 1 4 0l2 1c-2 1-5 2-6 4h-2v-1c-1-2 0-2-2-3-2-2-7-3-11-4z" class="e"></path><path d="M651 649h7c3 0 6 0 8 1h8c4 1 9 2 11 4-9-1-18-2-28-1l1-2c1 0 1 0 2-1-2 0-7 0-9-1z" class="k"></path><path d="M669 643v-1c0-1 0-2-1-3v-1h2 5v1c1 0 2-1 3-1h1l8 1c0 2 0 5 1 6-2 1-3 1-4 0-1 0-1 0-1-1-1 0-1 1-1 1-2 0-4 0-5-1v-1l3-1h0c-1-1-2-1-3-1 0 1 0 1-1 2s-3 0-4 0l-1-2c0 1-1 1-1 2h-1z" class="D"></path><path d="M646 636c1 0 1 1 2 0l4-1h2 4v1l4 2v1c-2 0-3 0-4 1l1 1 1-1c1 1 1 1 1 3h-3l-1-1c0 1 0 0-1 1h-5c-2 1-5 1-7 2h-1v-3c0-1 0-1 1-2h0c0-2-1-1-2-2l-1-2h5z" class="L"></path><path d="M646 636c1 0 1 1 2 0l4-1h2 4v1h0l-1 2h-4-2-3c0 1 0 3-1 4l-1-1v-1-1c-1 0-1 0-1 1v1c-1 1-1 1-2 1 0-1 0-1 1-2h0c0-2-1-1-2-2l-1-2h5z" class="n"></path><path d="M683 631c0 2 1 4 0 6h-3c-1 0-1 0-1 1h-1c-1 0-2 1-3 1v-1h-5-2v1c1 1 1 2 1 3v1h-2-5v-4-1l-4-2v-1h-4-2l-4 1c-1 1-1 0-2 0 1-2 2-2 4-3h2c1-1 2-1 3-1h3c2 1 3 1 5 1 4 0 8 1 12 1 2 0 5 1 8 1v-4z" class="a"></path><path d="M662 638c1-1 3-1 4-1 0 1 0 3 1 4v2h-5v-4-1z" class="G"></path><path d="M637 649c3-1 6 0 9-1v1c2 0 2-1 3-1 1-1 3 0 5 0 1 0 3 0 4 1h-7c2 1 7 1 9 1-1 1-1 1-2 1l-1 2c-2 1-7 0-10 1v2c-2-1-1 0-2-1-2 0-3 1-5 3-1 0-1-1-3 0v2c-1 0-1 0-1-1v-1c-2-2-5-3-7-5v-2c2 0 3 0 5-1l3-1z" class="d"></path><path d="M633 654c3-1 8-3 10-2 1 0 1 0 1 1 0 0-1 0-1 1-2 0-4 1-6 2-3 0-2 0-4-2z" class="f"></path><path d="M637 649c3-1 6 0 9-1v1c2 0 2-1 3-1 1-1 3 0 5 0 1 0 3 0 4 1h-7-1c-1 0-3 1-5 1v-1l-1 1c-1 0-2 0-4 1-2 0-6 1-8 2l1 1h0c2 2 1 2 4 2v1l-1 1c-2-2-5-3-7-5v-2c2 0 3 0 5-1l3-1z" class="W"></path><path d="M638 622c2-1 2-1 2-2h2l2 4c-1 1-1 3-1 4 0 0 1 1 0 1l4 1v1 1h1c1-1 2-1 4-1l1 1 2-1v1c-1 0-2 0-3 1h-2c-2 1-3 1-4 3h-5l1 2c1 1 2 0 2 2h0c-1 1-1 1-1 2v3h-1c1 0 1 0 2 1h-1c-1 0-1 1-1 1l-5 1-1 1h1l-3 1c-2 1-3 1-5 1v2c-1-1-1-1-1-2-1-2 0-10 1-12 1-1 2-2 4-2h0c-1 1-2 1-2 3h0c2 0 5-1 7-2l-1-1v-1-1-5c1-2 0-6 1-8z" class="S"></path><path d="M635 642h2c0 1 1 2 1 3l-2 1h-2-1v-2l2-2z" class="B"></path><path d="M642 638c1 1 2 0 2 2h0c-1 1-1 1-1 2v3h-1-2c-2-1-2-1-2-3h1s1 0 1 1v-2c0-1 1-2 2-3z" class="K"></path><path d="M641 636c-1 0-1 1-1 0-1-3-1-4 1-6-1-2-1-4-2-6h1 1c1 1 1 2 1 3l-1 8v1c1-1 2-3 3-4l-1-1v-2l4 1v1 1h1c1-1 2-1 4-1l1 1 2-1v1c-1 0-2 0-3 1h-2c-2 1-3 1-4 3h-5z" class="L"></path><path d="M663 824h5 6l6 1h3c2 1 4 1 5 1l4 1c1 0 1 0 2 2 0 1 0 2 1 3h0c0 4 0 10-1 14l-1 2-2-2c-1 2-3 1-5 3 1 1 1 3 1 4l1 1s1 1 2 1h0c1-1 1-1 2-1l1-1h1l1 1v1l2 2h3c2 0 5-1 6 0h1l7 1 1-1 2 1c-3 3-5 5-8 9h0c-3 3-8 4-12 6-1 1-3 2-5 3l-5 4-2 2-1 2c-2 1-2 1-3 2l-1-1-6 4c-4 2-7 2-11 3 1 1 2 1 3 2-3 0-6 1-9 3l-5 1c-2 0-3 1-5 1v-2-2c-1 0-1 0-2 1-3-1-4-4-6-5l-2 1h0l-1-1c-1-1-3-3-5-2-3 0-6 1-9 1v-1l1-1v-1l-1-1c-2-2-5-3-6-4h2c1 1 2 0 3 0h-1c0-1-1-1-2-2-1 0-1 0-2-1v1c-1 1-2 0-2 0-2-1-3-2-5-3-4-2-7-2-11-4-1 0-3-1-4-1h-1c-1 0-2-1-2-1h-2-1l-1-2c-2 0-4 0-6-1 1-1 2-1 2-3 1 0 2 0 4 1l1-1-1-1c1-1 4-1 5-1v-1c3 0 5 0 9 1h3c2-1 4-1 6 0h0c3 1 7-1 10 1l6 2h0l2-1c1 0 1 1 2 1l-1-2 2-2c-4-2-7-2-11-3h3l1-2h1c3 0 2-1 4-2v-5-1h1l1-1 1 1h2 1l1-1v-1c0-2 0-4 1-6-1-2 0-5-1-6s-1-1-1-2h2v-2l1 2 2-3 2-2c2 0 2 0 3-1h4 0c3 1 7-1 11 0h0c2 0 3 0 4-1l-1-2z" class="k"></path><path d="M688 875c1 0 3 0 4 1l-5 4-2 2h-3v-1c-1 0-1 0-2-1h-2l-1-2h-1v-1h1c2 0 3 1 5 1l1-1h0 1c1-1 2-1 3-1l1-1z" class="S"></path><path d="M688 875c1 0 3 0 4 1l-5 4-2 2h-3v-1c2-2 4-3 5-5l1-1z" class="W"></path><path d="M657 874l1-1c1 0 2 0 3 1 1 0 2 1 3 1l1-1v1l1 2c1 1 5 0 6 1 0 0 0 1 1 1l1-1c1 0 1 0 1 1l1-1h1l1 2h2c1 1 1 1 2 1v1h3l-1 2c-2 1-2 1-3 2l-1-1-4-2v-1c-2 0-3-1-4-1l-2-1-1 1h-1-1c0 1-1 2-1 3l-3-3c-1 0-2-1-3-2 0-1-1 0-2-1h0v-1c1-1 2-1 3-2l-3-1h-1z" class="M"></path><path d="M676 883c0-1 0-1 2-2l1 1c2 1 2 0 3 0h3l-1 2c-2 1-2 1-3 2l-1-1-4-2z" class="f"></path><path d="M661 875c3 2 5 3 6 6 0 1-1 2-1 3l-3-3c-1 0-2-1-3-2 0-1-1 0-2-1h0v-1c1-1 2-1 3-2z" class="K"></path><path d="M667 881h1 1l1-1 2 1c1 0 2 1 4 1v1l4 2-6 4c-4 2-7 2-11 3 0 0-1 0-2-1h0l2-2h0c0-1 1-1 1-1v-1h-1v1h-1l-2-3 2-1c1 1 1 1 3 2l1-2c0-1 1-2 1-3z" class="d"></path><path d="M676 882v1l4 2-6 4v-1-3c0-1 0-1-1-2 2 0 2 0 3-1z" class="n"></path><path d="M635 871c1 0 3 0 4 1h3c1 1 2 1 3 1h4c1 1 2 1 4 1h4 1l3 1c-1 1-2 1-3 2v1h0c1 1 2 0 2 1v1h0c0 1 1 1 1 2v1c-2-1-3-2-4-2-3 0-5-1-8-2-1 0-1 1-2 1s-3-1-4-1c-2-1-5-2-8-3l1-3c-1 1-1 1-2 1h-1c0-1 1-2 2-3z" class="G"></path><path d="M653 874h4 1l3 1c-1 1-2 1-3 2v1h0c-2 0-3-1-4-1s-2-1-3-1l2-2z" class="b"></path><path d="M653 874h4 1c-2 1-3 1-4 2v1c-1 0-2-1-3-1l2-2z" class="j"></path><path d="M635 871c1 0 3 0 4 1h3c1 1 2 1 3 1h4c1 1 2 1 4 1l-2 2h-1-3v2h-2v-2h-1c-1 1-2 1-3 1v-1l1-1-1-1h-2v2h0c-2-1-1-2-2-4l-1 1c-1 1-1 1-2 1h-1c0-1 1-2 2-3z" class="D"></path><path d="M645 873h4c1 1 2 1 4 1l-2 2h-1c-1-1-3-1-5-1h0v-2z" class="V"></path><path d="M639 859l29-1 1 1h1c0 1-1 2-2 3-3 0-1 1-3 2-1 0-3-1-3-1-1-1 0-2-2-3-3 0-6 0-9 2h-1c1 1 1 1 3 1l4 2c2 1 4 2 5 2 0 1-1 2-2 2l1 1h2l1-2 1 1c1 1 0 1 0 2l2 2-2 1c-1-1-2-1-4-3h0-1c-2 1-3 1-5 1l-2-2v-1h-1l-1 1v2h0c-2-2-3-3-5-3h-1l2 2h-1c-2 0-2-1-3-2l-1-1c-1 0-3 1-4 2l-2-1 1-1v-1c-1 0-2 1-3 2l-8-3h0l2-1c1 0 1 1 2 1l-1-2 2-2c-4-2-7-2-11-3h3 16z" class="h"></path><path d="M642 864c3 0 5 0 8-2 1 1 1 1 3 1l4 2c0 1-1 2-2 3-2 0-2-1-4-2-3 0-6 0-9-2z" class="B"></path><path d="M639 859l29-1 1 1h1c0 1-1 2-2 3-3 0-1 1-3 2-1 0-3-1-3-1-1-1 0-2-2-3-3 0-6 0-9 2h-1c-3 2-5 2-8 2-1-1-4-2-6-2 0 0-1 0-2-1s-2-1-3-1h-1v-1h8 1z" class="b"></path><path d="M651 862c3-2 6-2 9-2 2 1 1 2 2 3 0 0 2 1 3 1 2-1 0-2 3-2 1 0 4 1 5 1l1 1c1 1 4 0 5 0h0l3-3h0l5 2c4 1 7 3 11 3l2 1h0c-1 1-1 1-2 1l-1 1c-2 0-2 0-2-1-2 0-3 0-5 2-1 1-1 2-2 3-2 0-3 0-5 2h-1-2c-1 0-2 0-3-1-2 1-3 2-5 2v-1l-2 1c0-1 0-1-1-2h0v-1l-1-1-1 1-2-2c0-1 1-1 0-2l-1-1-1 2h-2l-1-1c1 0 2-1 2-2-1 0-3-1-5-2l-4-2c-2 0-2 0-3-1h1z" class="r"></path><path d="M674 869c2 1 4 1 7 2l-1 1h-1c-1 0-2 1-3 2l-4-1-2 1h-1 0v-1l-1-1 1-1 2 1c0-1 1-1 1-1l2-2z" class="O"></path><path d="M662 867c3 0 4 1 7 1 1 1 3 1 5 1l-2 2s-1 0-1 1l-2-1-1 1-1 1-2-2c0-1 1-1 0-2l-1-1-1 2h-2l-1-1c1 0 2-1 2-2z" class="P"></path><path d="M687 863c4 1 7 3 11 3v1h-5c-1 0-2 1-3 1-1-1-2-1-3 0l-2-2h-1v-2h2l1-1z" class="B"></path><path d="M682 861l5 2-1 1h-2v2h1l2 2c-3 1-5 0-7 1h-1c-1-1-3-1-4-1 1-1 1-2 2-3l1 1s0 1 1 1h1c0-1-1-2-1-3h0l3-3h0z" class="P"></path><path d="M682 861l5 2-1 1h-2v2c-2 0-2 0-3-2h-2l3-3h0z" class="b"></path><path d="M651 862c3-2 6-2 9-2 2 1 1 2 2 3 0 0 2 1 3 1 2-1 0-2 3-2 1 0 4 1 5 1l1 1c1 1 4 0 5 0 0 1 1 2 1 3h-1c-1 0-1-1-1-1l-1-1c-1 1-1 2-2 3l-13-3c-4-1-7-3-11-3z" class="D"></path><path d="M687 853l1 1s1 1 2 1h0c1-1 1-1 2-1l1-1h1l1 1v1l2 2h3c2 0 5-1 6 0h1l7 1 1-1 2 1c-3 3-5 5-8 9h0c-3 3-8 4-12 6-1 1-3 2-5 3-1-1-3-1-4-1l1-1h3v-2c-1 1-1 1-2 1h-2c1-1 1-2 2-3 2-2 3-2 5-2 0 1 0 1 2 1l1-1c1 0 1 0 2-1h0l-2-1c-4 0-7-2-11-3l-5-2h0l-3 3h0c-1 0-4 1-5 0l-1-1c-1 0-4-1-5-1 1-1 2-2 2-3h-1l-1-1-1-2c1-1 2-1 3-1v-1c1 0 1 1 1 1h1 5 2s1-1 2-1c0 0 1 1 2 1 1 1 2 0 4 0v-2z" class="G"></path><path d="M693 861c0-1 1-2 2-2s2 1 2 1l1 1c-1 0-1 0-2 1-2 0-2-1-3-1z" class="P"></path><path d="M706 857h1v2 1l-2-1c-1 0-2 1-2 1l-2-1c-2 1-2 2-3 2l-1-1 1-1-1-1 3-1h6z" class="b"></path><path d="M687 853l1 1s1 1 2 1h0c1-1 1-1 2-1l1-1h1l1 1v1l2 2h3c2 0 5-1 6 0h-6l-3 1 1 1-1 1s-1-1-2-1-2 1-2 2h-2-1v-2h0l1-2h-3c0-1 0-1-1-2v-2z" class="M"></path><path d="M691 857h9l-3 1 1 1-1 1s-1-1-2-1-2 1-2 2h-2-1v-2h0l1-2z" class="E"></path><path d="M667 856c1-1 2-1 3-1v-1c1 0 1 1 1 1h1 5 2s1-1 2-1c0 0 1 1 2 1 1 1 2 0 4 0 1 1 1 1 1 2h3l-1 2h0v2h1l-1 1h-1v-2c-2 0-2 0-3 2-1-1-2-1-3-2l-1 1h0l-3 3h0c-1 0-4 1-5 0l-1-1c-1 0-4-1-5-1 1-1 2-2 2-3h-1l-1-1-1-2z" class="B"></path><path d="M670 859h4l-1 2c2 1 2 1 4 1 1-2 1-2 3-1h2l-3 3h0c-1 0-4 1-5 0l-1-1c-1 0-4-1-5-1 1-1 2-2 2-3z" class="E"></path><path d="M667 856c1-1 2-1 3-1v-1c1 0 1 1 1 1h1 5 2s1-1 2-1c0 0 1 1 2 1 1 1 2 0 4 0 1 1 1 1 1 2-6 2-13 1-19 2l-1-1-1-2z" class="g"></path><path d="M715 857l2 1c-3 3-5 5-8 9h0c-3 3-8 4-12 6-1 1-3 2-5 3-1-1-3-1-4-1l1-1h3v-2c-1 1-1 1-2 1h-2c1-1 1-2 2-3 2-2 3-2 5-2 0 1 0 1 2 1l1-1c1 0 1 0 2-1h0l-2-1c-4 0-7-2-11-3l-5-2 1-1c1 1 2 1 3 2l13 2c1 1 7 0 8-1 0-1 1-1 2-2 0 0 1 0 1-1l1-1c1 0 2 0 3-1l1-1z" class="U"></path><path d="M715 857l2 1c-3 3-5 5-8 9h0c-3 3-8 4-12 6-1 1-3 2-5 3-1-1-3-1-4-1l1-1h3v-2h0c2 0 3 0 4-1h0c1-2 1-1 3-2 1 0 1-1 2-1h1l1-1c2 0 3-2 5-3s3-2 5-4c0 0 1-1 2-3z" class="M"></path><path d="M592 863v-1c3 0 5 0 9 1h3c5 1 11 1 16 3l9 3c1 0 5 1 6 2-1 1-2 2-2 3h1c1 0 1 0 2-1l-1 3c3 1 6 2 8 3 1 0 3 1 4 1s1-1 2-1c3 1 5 2 8 2 1 0 2 1 4 2v-1c0-1-1-1-1-2h0v-1c1 1 2 2 3 2l3 3-1 2c-2-1-2-1-3-2l-2 1 2 3h1v-1h1v1s-1 0-1 1h0l-2 2h0c1 1 2 1 2 1 1 1 2 1 3 2-3 0-6 1-9 3l-5 1c-2 0-3 1-5 1v-2-2c-1 0-1 0-2 1-3-1-4-4-6-5l-2 1h0l-1-1c-1-1-3-3-5-2-3 0-6 1-9 1v-1l1-1v-1l-1-1c-2-2-5-3-6-4h2c1 1 2 0 3 0h-1c0-1-1-1-2-2-1 0-1 0-2-1v1c-1 1-2 0-2 0-2-1-3-2-5-3-4-2-7-2-11-4-1 0-3-1-4-1h-1c-1 0-2-1-2-1h-2-1l-1-2c-2 0-4 0-6-1 1-1 2-1 2-3 1 0 2 0 4 1l1-1-1-1c1-1 4-1 5-1z" class="a"></path><path d="M614 874l3 1 13 2-1 1h-4c-2 0-3 1-4 2h-1c1-1 1-2 2-3h-1l-1 1c-2 0-2 0-3-1h-1l-1 1-2-1c1-1 1-2 1-3z" class="G"></path><path d="M621 882c3 1 5 0 7 0 2-1 2-1 3-1s1 0 2 1h1c2 0 4 1 6 2 1 0 2 1 3 1v1l-7-2c-5-2-10-1-14 2-2-2-5-3-6-4h2c1 1 2 0 3 0z" class="M"></path><path d="M617 873c4 0 9 2 13 2 1 0 1 1 2 1h3c3 1 6 2 8 3 1 0 3 1 4 1s1-1 2-1c3 1 5 2 8 2 1 0 2 1 4 2v-1c0-1-1-1-1-2h0v-1c1 1 2 2 3 2l3 3-1 2c-2-1-2-1-3-2l-2 1h-4-1-1-1-1c-2-1-4-2-5-3s-2 0-2-1h-1c-1-1-3-1-4-1-4-1-7-2-10-3l-13-2c1-1 0-2 0-2z" class="T"></path><path d="M583 865c1 0 2 0 4 1 2 0 5 1 8 1 2 0 5 1 7 2 3 0 5 1 8 2 1 0 2 0 4 1h1c1 0 2 0 2 1 0 0 1 1 0 2l-3-1c0 1 0 2-1 3l-1-1c-2 0-2-1-3-2h-1-1-1c-1-1-1-2-2-2h-2c-4-1-8-1-11-2-2-1-2-1-4-1s-4 0-6-1c1-1 2-1 2-3z" class="L"></path><path d="M609 872l5 2c0 1 0 2-1 3l-1-1-3-4z" class="O"></path><path d="M587 869l-1-1 1-1h0c1 0 2 1 3 0 1 0 3 1 4 1l14 4h1l3 4c-2 0-2-1-3-2h-1-1-1c-1-1-1-2-2-2h-2c-4-1-8-1-11-2-2-1-2-1-4-1zm35 17c4-3 9-4 14-2l7 2 3 2h1c-1 1-2 1-2 2s0 1 1 2h1v3c-1 0-1 0-2 1-3-1-4-4-6-5l-2 1h0l-1-1c-1-1-3-3-5-2-3 0-6 1-9 1v-1l1-1v-1l-1-1z" class="G"></path><path d="M646 888h1c-1 1-2 1-2 2s0 1 1 2h1v3c-1 0-1 0-2 1-3-1-4-4-6-5l-2 1h0l-1-1c1-1 2-2 2-3l1 1c1 0 3 1 4 1v-1l3-1z" class="U"></path><path d="M623 887c3-1 5-1 8-1 1 0 2 0 2 1h2c1 0 2 1 3 1 0 1-1 2-2 3-1-1-3-3-5-2-3 0-6 1-9 1v-1l1-1v-1z" class="i"></path><path d="M655 885h1 4l2 3h1v-1h1v1s-1 0-1 1h0l-2 2h0c1 1 2 1 2 1 1 1 2 1 3 2-3 0-6 1-9 3l-5 1c-2 0-3 1-5 1v-2-2-3h-1c-1-1-1-1-1-2s1-1 2-2h-1l-3-2v-1c2 1 3 1 4 2l3-1 4 2c1 0 1 0 2-1h2l-3-2z" class="n"></path><path d="M643 885c2 1 3 1 4 2 2 0 3 1 5 2h3v1 1h1 2l1 2c-1 1-1 1-2 1l-1-1-2-1-2-1-5-3h-1l-3-2v-1z" class="W"></path><path d="M647 895v-3h-1c-1-1-1-1-1-2s1-1 2-2l5 3 2 1 2 1c-2 2-2 3-5 4h-4v-2z" class="Y"></path><path d="M654 892l-3 3c-1-1-1-1-2-1-1-1 0 0-1-2 1-1 2-1 4-1l2 1z" class="L"></path><path d="M655 885h1 4l2 3h1v-1h1v1s-1 0-1 1h0l-2 2h0c1 1 2 1 2 1 1 1 2 1 3 2-3 0-6 1-9 3l-5 1c-2 0-3 1-5 1v-2h4c3-1 3-2 5-4l1 1c1 0 1 0 2-1l-1-2h1v-2l2-1-1-1h-2l-3-2z" class="M"></path><path d="M592 863v-1c3 0 5 0 9 1h3c5 1 11 1 16 3l9 3c1 0 5 1 6 2-1 1-2 2-2 3h1c1 0 1 0 2-1l-1 3h-3c-1 0-1-1-2-1-4 0-9-2-13-2 0-1-1-1-2-1h-1c-2-1-3-1-4-1-3-1-5-2-8-2-2-1-5-2-7-2-3 0-6-1-8-1l1-1-1-1c1-1 4-1 5-1z" class="K"></path><path d="M592 863v-1c3 0 5 0 9 1h3c5 1 11 1 16 3l9 3h0c-1 1-1 1-1 2s0 0-1 1c-2 0-1-1-2-2h-5-1-1v-1c-1 0-2-1-3 0-4 1-5-2-8-2h-2l-4-1-9-3z" class="E"></path><path d="M663 824h5 6l6 1h3c2 1 4 1 5 1l4 1c1 0 1 0 2 2 0 1 0 2 1 3h0c0 4 0 10-1 14l-1 2-2-2c-1 2-3 1-5 3 1 1 1 3 1 4v2c-2 0-3 1-4 0-1 0-2-1-2-1-1 0-2 1-2 1h-2-5-1s0-1-1-1v1c-1 0-2 0-3 1l1 2-29 1h-16l1-2h1c3 0 2-1 4-2v-5-1h1l1-1 1 1h2 1l1-1v-1c0-2 0-4 1-6-1-2 0-5-1-6s-1-1-1-2h2v-2l1 2 2-3 2-2c2 0 2 0 3-1h4 0c3 1 7-1 11 0h0c2 0 3 0 4-1l-1-2z" class="l"></path><path d="M630 849l1-1 1 1h2 1l-1 2-2-1c-2 2 0 3-1 5h-1c-1-2-1-4-1-6h1z" class="M"></path><path d="M664 851l-1 2c0 1 0 2-1 3s-3 1-4 1c-1-1-2-1-2-1l1-4 1 2h2l4-3z" class="O"></path><path d="M658 851h1c0-1 1-1 1-1 1-1 2-1 3-1l1 1v1l-4 3h-2l-1-2 1-1h0z" class="D"></path><path d="M637 841h1c0 1 0 2 1 2v3 1c-1 0-1 1-2 2h1c1 2 0 3 0 5l-1 2h-1v-5h-2 0l1-2 1-1v-1c0-2 0-4 1-6z" class="X"></path><path d="M660 843h2v1c2 0 4 0 5 1h3 3l-1 3h1v3c-1 1-1 2-1 3v1h-1s0-1-1-1v1c-1 0-2 0-3 1h0c-1-1-2-2-4-3l1-2v-1l-1-1c-1 0-2 0-3 1 0 0-1 0-1 1h-1 0l-1-1-1 1c-1 2-1 2-2 3h-1c0-1-1-2 0-3l1-1v-2c1 0 1 0 2-1l-1-2h0c2-1 3-2 5-2z" class="Y"></path><path d="M660 843h2v1c2 0 4 0 5 1h0-12c2-1 3-2 5-2z" class="O"></path><path d="M667 845h3 3l-1 3h-1l-2 1h-4l-1 1-1-1c-1 0-2 0-3 1 0 0-1 0-1 1h-1v-3-1h3 2l1-1c1 0 1 0 2-1h1 0z" class="q"></path><path d="M670 845h3l-1 3h-1l-1-2v-1z" class="T"></path><path d="M672 848h1v3c-1 1-1 2-1 3v1h-1s0-1-1-1v1c-1 0-2 0-3 1h0c-1-1-2-2-4-3l1-2v-1l1-1h4l2-1h1z" class="c"></path><path d="M642 828c2 0 2 0 3-1h4 0c3 1 7-1 11 0v1h9c-1 1-1 2-1 2v3 2 7l2 1v2h-3c-1-1-3-1-5-1v-1h-2c-2 0-3 1-5 2h0l-14 2-2-1v-3c-1 0-1-1-1-2h-1c-1-2 0-5-1-6s-1-1-1-2h2v-2l1 2 2-3 2-2z" class="S"></path><path d="M657 831c1-1 3 0 4 0l1 1-1 1c-1 1-1 1-3 1l-1-3z" class="X"></path><path d="M662 830h5 1v3 2h0c-2-1-2-1-3 0-1 0-2 1-3 2h0v-7z" class="j"></path><path d="M650 831c3 0 3 1 4 3 1 1 1 0 1 1l-2 1v1h2c1 2 1 2 1 4l-1 2h-4l-1-1v-1-10z" class="c"></path><path d="M662 837c1-1 2-2 3-2 1-1 1-1 3 0h0v7l2 1v2h-3c-1-1-3-1-5-1v-1-2-4h0z" class="D"></path><path d="M662 837c1-1 2-2 3-2 1-1 1-1 3 0l-1 2h-1c-1 0-1 0-2 1-1-1-1-1-2-1z" class="I"></path><path d="M662 841l2-1c2 0 3 0 4 2l2 1v2h-3c-1-1-3-1-5-1v-1-2z" class="Q"></path><path d="M642 828c2 0 2 0 3-1h4 0c3 1 7-1 11 0v1h9c-1 1-1 2-1 2h-1-5v-1c-2 0-4-1-6 0v1c0 3 0 3-1 5 0-1 0 0-1-1-1-2-1-3-4-3v-1-1c-2 0-5 0-7 1s-2 2-3 4v4c1 2 0 7 1 9l-2-1v-3c-1 0-1-1-1-2h-1c-1-2 0-5-1-6s-1-1-1-2h2v-2l1 2 2-3 2-2z" class="i"></path><path d="M650 830c1-1 1-1 2-1h2l1 2 1-1c0 3 0 3-1 5 0-1 0 0-1-1-1-2-1-3-4-3v-1z" class="l"></path><path d="M637 831l1 2 2-3c0 1 0 2-1 3l-2 1v1l2 1c0 2 0 3-1 5h-1c-1-2 0-5-1-6s-1-1-1-2h2v-2z" class="W"></path><path d="M641 847c-1-2 0-7-1-9v-4c1-2 1-3 3-4s5-1 7-1v1 1 10 1l1 1h4 5c-2 0-3 1-5 2h0l-14 2z" class="L"></path><path d="M650 829v1 1 10 1 2c-2 0-2 0-3-1-1 0-1 0-1 1h-1-1c0-3-1-7 0-10s4-4 6-5z" class="G"></path><path d="M648 838h-2c-1-1-1-1-2-1 1-1 1-2 2-2h0c2 1 3 1 3 3h-1z" class="B"></path><path d="M649 838c0 1 0 2 1 3v1 2c-2 0-2 0-3-1h0l-2-1v-1h1 1l1-1v-2h1z" class="P"></path><path d="M663 824h5 6l6 1h3c2 1 4 1 5 1l4 1c1 0 1 0 2 2 0 1 0 2 1 3h0c0 4 0 10-1 14l-1 2-2-2c-1 2-3 1-5 3 1 1 1 3 1 4v2c-2 0-3 1-4 0-1 0-2-1-2-1-1 0-2 1-2 1h-2-5v-1c0-1 0-2 1-3v-3h-1l1-3h-3v-2l-2-1v-7-2-3s0-1 1-2h-9v-1h0c2 0 3 0 4-1l-1-2z" class="U"></path><path d="M680 825h3v2h-6l1-2h2z" class="e"></path><path d="M674 824l6 1h-2l-1 2h-3v-3z" class="t"></path><path d="M670 829c2 0 5-1 7 0v1c-1 0-2 1-3 1-1-1-2-1-3 0h-1v-2z" class="n"></path><path d="M674 831c1 0 2-1 3-1v3l-3 1h0v-1-2z" class="M"></path><path d="M679 845h6v1c0 1 0 1 1 2h-5c-1-1-1-2-2-3z" class="T"></path><path d="M678 845h1c1 1 1 2 2 3h-1c-2-1-5 0-7 0h-1l1-3h5z" class="N"></path><path d="M663 824h5 6v3h-6c-1 0-3-1-4-1l-1-2z" class="S"></path><path d="M673 851l1-1s1 0 2-1l2 2v1 2l-1 1h-5v-1c0-1 0-2 1-3z" class="O"></path><path d="M678 836c0-2-1-5 0-7h1l6 1c1 4 1 8 1 12l-2-3c1-2 1-3 1-5l-4-2c-2 1-1 1-2 2 0 1-1 1-1 2zm7 13h1c1 1 1 3 1 4v2c-2 0-3 1-4 0-1 0-2-1-2-1v-3l-1-1c2-1 3-1 5-1z" class="j"></path><path d="M685 849h1c1 1 1 3 1 4v2c-2 0-3 1-4 0v-1c2-1 2-3 2-5z" class="G"></path><path d="M669 828l1 1v2h1c1-1 2-1 3 0v2 1h0l3-1 1 3c0 2 0 5-1 7h-1c-2-1-4-1-6 0l-2-1v-7-2-3s0-1 1-2z" class="t"></path><path d="M669 828l1 1v2h1c1-1 2-1 3 0v2l-1-1h-1l-1-1c-1 1-2 1-3 2v-3s0-1 1-2z" class="g"></path><path d="M672 835h0c1 0 3 1 4 1 0 1 0 2-1 3l-1 1h-2c0-1 0-2-1-3 0-1 0-1 1-2z" class="X"></path><path d="M678 836c0-1 1-1 1-2 1-1 0-1 2-2l4 2c0 2 0 3-1 5l2 3v1l-1 2h-6-1-5-3v-2c2-1 4-1 6 0h1c1-2 1-5 1-7h0z" class="D"></path><path d="M678 843v-1l3-2c1 1 2 1 3 2-1 1-3 2-4 1h-2z" class="b"></path><path d="M684 842l2 1-1 2h-6-1v-2h2c1 1 3 0 4-1z" class="B"></path><path d="M683 825c2 1 4 1 5 1l4 1c1 0 1 0 2 2 0 1 0 2 1 3h0c0 4 0 10-1 14l-1 2-2-2-1-1 1-1-1-1 1-1c-1-2 0-4 0-6-1-1-1-1-1-2v-1c-1 0-1-1-2-1l-1-1h-1v-1l-3-3v-2z" class="f"></path><path d="M688 826l4 1c1 0 1 0 2 2 0 1 0 2 1 3l-3-2c-1-1-2-3-4-4z" class="X"></path><path d="M318 724l2-1h1l-1 1 1 1c1-1 2-1 4 0v1h0 2 0v1l5-3h1c-1 3-3 4-5 6l1 2h2v1c1-1 2-1 3-2h1v1c2-1 3-1 5-1h2c2 0 2 0 5 1l1 1c4-2 8-5 12-3 5 2 6 7 10 9l2 2v1c3 2 5 2 7 6 0 1 0 2 1 3v1 2 3c2 0 4-2 5-3 2 0 4 1 6 2 1 0 2 0 2 1h1 3c0 1 0 1 1 2l-1 2v1c1 2 1 2 3 3l-2 1 2 1c-1 3-1 5-1 7 1 1 1 5 0 7v1c1 2 1 2 2 3l1 1-2 1c-1 0-1 1-1 2v1h0v3 1 10 4 1c-1 0-1 1-2 0-1 0-2 0-3 1h-1c-1-1-2-1-3-1s-1 1-1 1h-3 0c-2-1-5-1-8-1h-4-2v1c-3 0-5-1-8-1 0-1-1-2-1-2l1-1c1 1 1 1 2 1h1c-1-3-2-6-4-9l-1-1c-4-2-6-4-10-5-5-1-10-1-14 0h-4-3v-2c-1 0-1 0-1-1l-2-2h-1v-2h-2v-4c0-1 0-3 1-3v-1-2h-1l1-1 3-1h-2-11c0-1 1-1 2-1v-1l-1-1c-1 0-2 0-4-1h0v-1c0-1 1-2 1-3s1-2 1-2l1-1-1-1h-1l-2 2v1h-1c0-1 0-2-1-3h0l1-2c1 0 2 0 3-1l1-1c1-1 1 0 2 1 1 0 1-1 2-1 0-1 0-2-1-3l1-1c0-2 0-4-1-5h-1c-1 0-1 0-2-1 0 0-1 0-1-1l-1 1c-1-1-1-2-2-2v-1c-3-1-9-2-12-1v-2c1-1 1-1 3 0 1-1 0-1 1-2 0-1 1-2 1-2v-1h0c0-1 0-1 1-2v-1l-1-1h1l1-2h-1v-1c1 1 1 1 2 1 0-1 1-2 1-2 1 0 2 0 2-1l3-3 1-1c1 1 1 1 2 0h1v1h1z" class="E"></path><path d="M362 769c0-1 1-2 2-2 1 1 0 1 0 2s2 3 3 4l-2 1h-1c0-2-1-2-2-3v-2z" class="V"></path><path d="M379 748c0 1 0 2 1 3v1c-2 0-4-1-5 0l1 2h-2 0c-1-1-2-1-3-2l2-1c0-1 0-1 1-2l1 1h3l1-2z" class="F"></path><path d="M353 764c2-1 2-1 4 0 0 0 1 1 2 1v1l-1 1c1 1 1 2 2 3h0l-1 1c-1-1-1 0-1-1h-3v-1h-1-3c-2-1-2-1-2-3h0l4-2z" class="B"></path><path d="M353 764c2-1 2-1 4 0 0 0 1 1 2 1v1l-1 1h-2-1-3l1-2v-1z" class="I"></path><path d="M356 755v-2c2-3 3-1 5-1l2-1h1v1c-1 1-1 2-1 4h0c1-1 2-1 3-2 0-1 1-1 1-2h2 0l-1-1h1l2 1c1 1 2 1 3 2h0c-1 1-2 1-3 2h-1l-2 2-3-1h-1-6v-1c2-1 2-1 3-2l-1-2c0 1-1 1-2 1s-2 1-2 2z" class="H"></path><path d="M365 757c1-1 1-2 2-2h2l1 1-2 2-3-1z" class="J"></path><path d="M328 742l1-2c1 2 2 1 4 3 1 1 3 4 5 4 1 0 0 0 2 1h0c1 0 1 1 2 1h0c4 1 6 3 9 6l-1 1-3-3c-3-1-3-2-7-2-1 0-1-1-2-2h-1l-1 1v2h-2v-1l-6-6v-3z" class="I"></path><path d="M328 742l1-2c1 2 2 1 4 3v4c1 2 3 2 1 5v-1l-6-6v-3z" class="K"></path><path d="M338 754l1 1v2l1 1c1 0 2 0 3 1l1 1h2 0l1-1-1-2c2-1 2 0 3 0 2 1 3 1 5 1 1 0 1 0 1-1l2 2c-1 0-1 1-1 2-2 1-5 0-7 1v1 2 1h0l-3 1v-1c-1-1-1 0-2-1 1-1 1-2 1-3l-4-2-1-2-2 1h-1v2c-1-1-2-1-3-1v1h0l-2-1v-2c1-1 0-3 1-4l4 3v-1-2h1z" class="V"></path><path d="M337 761v-2h1l2-1 1 2 4 2c0 1 0 2-1 3 1 1 1 0 2 1v1l3-1c0 2 0 2 2 3h3 1v1h3c0 1 0 0 1 1l1-1 2-1v2c1 1 2 1 2 3h1c1 1 1 1 1 2l1 2v1l-1 1h-1v-1-1c0 1-1 1-1 1l-1-1-1-1-2 2v-1-1-1c-1 1-1 1-2 1h-1l2-1-3-3c-1 0-1 0-2 1-3-2-4 0-7 0h-2-2v-1c-1 0-2-1-2-2h0-2l-1-2 4-1-1-2v-1l1-2c-1 0-1 0-2 1l-2-1-1-2z" class="P"></path><path d="M341 766h2c1 1 1 1 2 1l-1 1c1 1 1 1 2 3l2-2h0c1 1 2 1 3 1 0 0 1 1 2 1 0-1 1-1 1-1h1c0 1 0 1 1 1-1 1-1 1-2 0-1 0-4 1-4 1v1l-2-2-2 1c-2 0-2 0-4-1v-3l-1-2z" class="O"></path><path d="M338 769l4-1v3c2 1 2 1 4 1l2-1 2 2v-1s3-1 4-1c1 1 1 1 2 0 1 0 2 1 3 2h0 0l1 1 2 2 1-1v1h2v2h1 1v1l-1 1h-1v-1-1c0 1-1 1-1 1l-1-1-1-1-2 2v-1-1-1c-1 1-1 1-2 1h-1l2-1-3-3c-1 0-1 0-2 1-3-2-4 0-7 0h-2-2v-1c-1 0-2-1-2-2h0-2l-1-2z" class="q"></path><path d="M304 736c2 0 4 0 6 1l3 3 2 4h4c3 0 6 0 9 1l6 6v1h2v-2l1-1h1c1 1 1 2 2 2 1 2 3 4 3 6h-1-1v-2c-1-1-1-1-3-1h0-1v2 1l-4-3c-3-1-4-2-5-4-2 0-3 0-4 1-1-1-2-1-3-1h-1c0-1-1-2-2-2h-1c-1 0-1 0-2-1 0 0-1 0-1-1l-1 1c-1-1-1-2-2-2v-1c-3-1-9-2-12-1v-2c1-1 1-1 3 0 1-1 0-1 1-2 0-1 1-2 1-2v-1z" class="H"></path><path d="M304 736c2 0 4 0 6 1l3 3h-4c-2 1-3 1-4 1-1-1-1 0-1-1 1-1 1-1 3-1v-1c-1-1-1-1-3-1v-1z" class="C"></path><path d="M380 757c2 0 4-2 5-3 2 0 4 1 6 2 1 0 2 0 2 1 0 2 1 3 0 4l-1 1c-1 0-2 1-4 2 0 2 0 3 1 4l-2 1c-2 2-2 2-4 2l-2-1-1 2 1 1-1 1c-1-1-2-1-3-2-1 0-2 1-3 1l-1 1c0-1-1-1-2-2l-1 1h-1c1-2 2-3 4-4h-5c-1 0-2-1-3-1v-1l1-1c2-1 3-1 5-2l-2-2-1 2c-2 0-3-1-4-1l1-1 4-1 2-2 3-1 1-1h1 1 2 1z" class="J"></path><path d="M371 759v1 2h2c0-1 1-1 1-2 1-1 1-1 2-1v2c-1 1-3 3-5 3h0l-2-2-1 2c-2 0-3-1-4-1l1-1 4-1 2-2z" class="H"></path><path d="M373 769l4-2h1s0 1 1 1v1h1c0 1-1 2 0 3l1 1-1 1c-1-1-2-1-3-2-1 0-2 1-3 1l-1 1c0-1-1-1-2-2l-1 1h-1c1-2 2-3 4-4z" class="b"></path><path d="M374 773v-3l2 1c1-1 2-2 4-2 0 1-1 2 0 3l1 1-1 1c-1-1-2-1-3-2-1 0-2 1-3 1z" class="B"></path><path d="M379 769c1-2 1-2 1-4l2-2h1c1 0 2-1 3-1l2 2c0 2 0 3 1 4l-2 1c-2 2-2 2-4 2l-2-1-1 2c-1-1 0-2 0-3h-1z" class="F"></path><path d="M381 770l2-2c2 0 3 1 4 1-2 2-2 2-4 2l-2-1z" class="B"></path><path d="M318 724l2-1h1l-1 1 1 1c1-1 2-1 4 0v1h0 2 0v1l5-3h1c-1 3-3 4-5 6l1 2h2v1c1-1 2-1 3-2h1v1c-2 1-3 2-4 4l-1 2-1 2-1 2v3c-3-1-6-1-9-1h-4l-2-4-3-3c-2-1-4-1-6-1h0c0-1 0-1 1-2v-1l-1-1h1l1-2h-1v-1c1 1 1 1 2 1 0-1 1-2 1-2 1 0 2 0 2-1l3-3 1-1c1 1 1 1 2 0h1v1h1z" class="c"></path><path d="M305 733c2-1 5-1 7-1 1 0 2 1 2 1l-2 2-1 1h-1l-5-2v-1z" class="D"></path><path d="M335 731v1c-2 1-3 2-4 4l-1 2h-2c-2-1-2-1-4-1 0 0 1-1 2-1 1-1 2-3 3-4h2v1c1-1 2-1 3-2h1z" class="g"></path><path d="M317 735l2-2 2 2h2c0 1 0 1-1 2l1 1 1-1c2 0 2 0 4 1h2l-1 2-1 2c-3-2-6-2-8-4l-3-3z" class="M"></path><path d="M316 723h1v1h1l1 1c0 1-1 2-2 3h-1l-3 3c-1-1-1-1-1-2l-2-1c-1 1-1 2-3 2 0-1 1-2 1-2 1 0 2 0 2-1l3-3 1-1c1 1 1 1 2 0z" class="U"></path><path d="M320 738c2 2 5 2 8 4v3c-3-1-6-1-9-1l-2-3c1 0 2-2 3-3z" class="G"></path><path d="M318 724l2-1h1l-1 1 1 1c1-1 2-1 4 0v1h0 2 0v1l5-3h1c-1 3-3 4-5 6l-2 2h-1l1-1v-1l-2 2-1-1 2-3v-1c-1 1-2 3-3 4h-1c1-2 1-3 2-4 1 0 1 0 1-1h-1c-1 2-2 3-4 4v-1-1l-2 2v-2c1-1 2-2 2-3l-1-1z" class="N"></path><path d="M312 735l2-2 3 2 3 3c-1 1-2 3-3 3l2 3h-4l-2-4-3-3c-2-1-4-1-6-1h0c0-1 0-1 1-2l5 2h1l1-1z" class="R"></path><path d="M312 735l2-2 3 2 3 3c-1 1-2 3-3 3l-1-2c-1-1-3-2-4-4z" class="B"></path><path d="M318 748c1 0 2 1 2 2h1c1 0 2 0 3 1 1-1 2-1 4-1 1 2 2 3 5 4-1 1 0 3-1 4v2l2 1h0v-1c1 0 2 0 3 1l1 2 2 1c1-1 1-1 2-1l-1 2v1l1 2-4 1 1 2h2 0c0 1 1 2 2 2v1h-1l-1 1h-1v-2h-3c-1-1-1-3-2-4h-1c-1 1-1 2-1 3h-1 0l-1 2-2-1h-2-11c0-1 1-1 2-1v-1l-1-1c-1 0-2 0-4-1h0v-1c0-1 1-2 1-3s1-2 1-2l1-1-1-1h-1l-2 2v1h-1c0-1 0-2-1-3h0l1-2c1 0 2 0 3-1l1-1c1-1 1 0 2 1 1 0 1-1 2-1 0-1 0-2-1-3l1-1c0-2 0-4-1-5z" class="R"></path><path d="M319 753l1 1v1h2l2 1c1-1 1-1 2-1v1l1 1c1 0 3 0 4 1h1v2l2 1h0v-1c1 0 2 0 3 1l1 2h-1-1-5v-1-1l-2 1c-1-1-2-1-2-2v-1h-2c-1-1-1-1-2-1v-1c-1 0-1-1-2-1h-1 0l-1 1c0-1 0-2-1-3l1-1z" class="O"></path><path d="M327 757c1 0 3 0 4 1h1v2c-1 0-1 0-2-1-1 0-2 1-2 0l-1-2z" class="K"></path><path d="M318 748c1 0 2 1 2 2h1c1 0 2 0 3 1 1-1 2-1 4-1 1 2 2 3 5 4-1 1 0 3-1 4h-1c-1-1-3-1-4-1l-1-1v-1c-1 0-1 0-2 1l-2-1h-2v-1l-1-1c0-2 0-4-1-5z" class="D"></path><path d="M321 750c1 0 2 0 3 1 1-1 2-1 4-1 1 2 2 3 5 4-1 1 0 3-1 4h-1c0-2 0-2-1-3s-1-1-2-1c-1-1-2-1-3-1v-1c-1-1-1 0-2-1l-2-1z" class="F"></path><path d="M316 769c0-1-1-3 0-5l1 1h1c1-1 2-1 2-2h-1-1l-1-1c1-1 1-1 1-2l2 1c0-1 1-1 2-1h0 2c0 1 1 2 1 3l1 2c1-1 2-1 3-2h1v3h0v1h2c1 0 2 0 3 1h0c1-1 1 0 2-1v-1l1 1v2l1 2h2 0c0 1 1 2 2 2v1h-1l-1 1h-1v-2h-3c-1-1-1-3-2-4h-1c-1 1-1 2-1 3h-1 0l-1 2-2-1h-2-11c0-1 1-1 2-1v-1l-1-1c-1 0-2 0-4-1h0 3z" class="N"></path><path d="M325 770c2 0 4-1 6 0l1 2h0l-1 2-2-1h-2l-2-2v-1z" class="X"></path><path d="M316 769s2 0 2-1c2-1 2-2 4-3v2l3-1c1 1 1 1 1 2l-1 2v1l2 2h-11c0-1 1-1 2-1v-1l-1-1c-1 0-2 0-4-1h0 3z" class="M"></path><path d="M342 731c2 0 2 0 5 1l1 1c4-2 8-5 12-3 5 2 6 7 10 9l2 2v1c3 2 5 2 7 6l-1 2h-3l-1-1c-1 1-1 1-1 2l-2 1-2-1h-1l1 1h0-2c0 1-1 1-1 2-1 1-2 1-3 2h0c0-2 0-3 1-4v-1h-1l-2 1c-2 0-3-2-5 1v2h0l-1 1c-1 0-2 0-3-1-1-2-1-3-3-4s-5-3-7-4h0c-1 0-2 0-3-1 1-3 6-3 7-6h-1c-3 1-3 1-5 0-1 1-3 2-4 3l-2-2 1-1v-2h-2c-1 0-1 0-2-2 1-2 2-3 4-4s3-1 5-1h2z" class="s"></path><path d="M369 751c2-1 2-2 3-4h0l2-1v3c-1 1-1 1-1 2l-2 1-2-1z" class="I"></path><path d="M342 731c-1 3-2 3-3 5l-4 2h-2c3-2 5-3 7-7h2z" class="F"></path><path d="M372 742c3 2 5 2 7 6l-1 2h-3l-1-1v-3c1-1 1 0 1-1l-1-1h-1l-1-2z" class="o"></path><path d="M335 732c2-1 3-1 5-1-2 4-4 5-7 7-1 0-1 0-2-2 1-2 2-3 4-4z" class="J"></path><path d="M354 734l5 1h1c1 2 1 2 0 4h0c-2 1-3 3-3 5v1l-1 1c-1-1-1-2-2-3l-1-1s-1-1-2-1v-1c-1 1-2 3-3 5h0l-1-1c0 1-1 2-2 2h0l2-3v-3h-1-1c-3 1-3 1-5 0-1 1-3 2-4 3l-2-2 1-1h0l1 1c2-1 4-2 5-4h1l1 1h2l1-1h3s1-1 2-1l1 2v-1l1-1 1-2z" class="H"></path><path d="M353 736l1-2 2 5c0 2 0 2-1 2-2-1-2-1-2-3v-2z" class="E"></path><path d="M393 757h1 3c0 1 0 1 1 2l-1 2v1c1 2 1 2 3 3l-2 1 2 1c-1 3-1 5-1 7 1 1 1 5 0 7v1c1 2 1 2 2 3l1 1-2 1c-1 0-1 1-1 2v1h0v3 1 10 4 1c-1 0-1 1-2 0-1 0-2 0-3 1h-1c-1-1-2-1-3-1s-1 1-1 1h-3 0c-2-1-5-1-8-1h-4-2v1c-3 0-5-1-8-1 0-1-1-2-1-2l1-1c1 1 1 1 2 1h1c-1-3-2-6-4-9l-1-1c-4-2-6-4-10-5-5-1-10-1-14 0h-4-3v-2c-1 0-1 0-1-1l-2-2h-1v-2h-2v-4c0-1 0-3 1-3v-1-2h-1l1-1 3-1 2 1 1-2h0 1c0-1 0-2 1-3h1c1 1 1 3 2 4h3v2h1l1-1h1 2 2c3 0 4-2 7 0 1-1 1-1 2-1l3 3-2 1h1c1 0 1 0 2-1v1 1 1l2-2 1 1 1 1s1 0 1-1v1 1h1l1-1v-1l-1-2c0-1 0-1-1-2l2-1 2 1v-1h1l1-1c1 1 2 1 2 2l1-1c1 0 2-1 3-1 1 1 2 1 3 2l1-1-1-1 1-2 2 1c2 0 2 0 4-2l2-1c-1-1-1-2-1-4 2-1 3-2 4-2l1-1c1-1 0-2 0-4z" class="c"></path><path d="M376 785h1 0c1-1 3-2 4-1h11v2c-4-1-9-1-13 0h-3v-1z" class="g"></path><path d="M392 784h1c0-1 0-2 1-3 2 1 3 1 5 1 1 2 1 2 2 3l1 1-2 1c-2-1-6-1-8-1h0v-2z" class="p"></path><path d="M371 790c1-2 0-2 0-3 1-1 2-2 3-2l1-2c0-1 1-2 2-2h1c-1 1-1 2-2 4v1l-2 2v1h1l2 1v1c-1 0-2 0-3 1h-2l-2-1v-1h1z" class="X"></path><path d="M376 786h3c4-1 9-1 13 0h0-3v3h-6-8-1v-1l2-2z" class="N"></path><path d="M383 789c1-1 2-1 3-2v-1h3v3h-6z" class="Z"></path><path d="M369 773h1l1-1c1 1 2 1 2 2l-2 4c0 2 0 5-1 6-1 0-1-2-2-1h0c0 1-1 2-1 3h-2c0-1 1-2 2-2-1-1-2-1-3-1v-2h0l-1 1-3-3 2-2 1 1 1 1s1 0 1-1v1 1h1l1-1v-1l-1-2c0-1 0-1-1-2l2-1 2 1v-1z" class="N"></path><path d="M369 773h1l1-1c1 1 2 1 2 2l-2 4v-3c-1 0-1 1-2 2v2h-2v-1l-1-2c0-1 0-1-1-2l2-1 2 1v-1z" class="D"></path><path d="M392 786c2 0 6 0 8 1-1 0-1 1-1 2v1h0v3 1c0-1 0-1-1-2h-4 0c-2-1-5-1-7-1-1 0-3-1-4 0h-1-1-4v-1l-2-1h8 6v-3h3z" class="n"></path><path d="M392 786c2 0 6 0 8 1-1 0-1 1-1 2-2 1-8 0-10 0v-3h3z" class="a"></path><path d="M335 769c1 1 1 3 2 4h3v2h1l1-1h1 2 2c3 0 4-2 7 0 1-1 1-1 2-1l3 3-2 1c-1-1-3-1-5-1-1 1-1 1-3 1l-1-1c-2 0-3 2-4 3h0-2v-2h0c-1 1-3 1-3 3h-1c0-2-1-3-1-3l-1-1h-1-1l-1-1v-1s-1-1-1-2h0 1c0-1 0-2 1-3h1z" class="U"></path><path d="M332 772h1c0-1 0-2 1-3h1c1 2 1 3 0 5h2l1 2-1 1-1-1h-1-1l-1-1v-1s-1-1-1-2h0z" class="M"></path><path d="M393 757h1 3c0 1 0 1 1 2l-1 2v1 2c-1 2-1 3-3 5 0 2 0 2-1 3l-2 2c-1 1-2 1-2 2h-2c-1 1-1 1-1 2v1h0l-2-2-2 1v-1c0-1-1-2-2-3h0l1-1-1-1 1-2 2 1c2 0 2 0 4-2l2-1c-1-1-1-2-1-4 2-1 3-2 4-2l1-1c1-1 0-2 0-4z" class="K"></path><path d="M390 766c0-1 1-2 2-3 0 1 0 1 1 2l2-1v-3h2v1 2c-2 1-3 1-4 2v3 1l-2-1c0-1 0-1 1-2l-1-1h-1z" class="B"></path><path d="M393 757h1 3c0 1 0 1 1 2l-1 2h-2v3l-2 1c-1-1-1-1-1-2-1 1-2 2-2 3l-1 2c-1-1-1-2-1-4 2-1 3-2 4-2l1-1c1-1 0-2 0-4z" class="b"></path><path d="M332 772c0 1 1 2 1 2v1l1 1h1 1l1 1s1 1 1 3h1c1 2 2 3 4 4 0 0 2 0 2-1 2 0 4-3 5-4 2-1 3-2 4-2 2 1 3 1 4 2h1c2 2 2 3 3 5h0c1 1 1 1 1 2l2 2h0 3c1 0 2 1 3 2h-1v1l-1 4h-1v-1l-1 1c-1 0-2-1-2-1-2 1-3 2-3 3-4-2-6-4-10-5-5-1-10-1-14 0h-4-3v-2c-1 0-1 0-1-1l-2-2h-1v-2h-2v-4c0-1 0-3 1-3v-1-2h-1l1-1 3-1 2 1 1-2z" class="O"></path><path d="M350 779c2-1 3-2 4-2l1 2c-1 1-1 2-2 2-1 1-1 1-2 1v-2l-1-1z" class="I"></path><path d="M354 777c2 1 3 1 4 2h1c2 2 2 3 3 5l-1 1c-2 0-2-1-4-1v2c-1-2-1-2-2-3h-1v-1l-1-1c1 0 1-1 2-2l-1-2z" class="B"></path><path d="M347 785c2-1 3-2 4-2 1 1 3 2 4 3v1h1l1-1v-2c2 0 2 1 4 1l1-1h0v3c0 2 0 3-1 4h-2l-3 1h-1l-1-2s-1 0-1 1h-2c0-1 0 0-1-1 2 0 2 0 3-1l-1-1-1-1c-2 0-3-1-4-2z" class="N"></path><path d="M362 784h0v3l-1 1h-4c-1 0-2-1-3-1l1-1v1h1l1-1v-2c2 0 2 1 4 1l1-1z" class="a"></path><path d="M362 784c1 1 1 1 1 2l2 2h0 3c1 0 2 1 3 2h-1v1l-1 4h-1v-1l-1 1c-1 0-2-1-2-1-2 1-3 2-3 3-4-2-6-4-10-5l-1-1h2c0-1 1-1 1-1l1 2h1l3-1h2c1-1 1-2 1-4v-3z" class="M"></path><path d="M332 772c0 1 1 2 1 2v1l1 1h1 1l1 1s1 1 1 3c-1 1-1 2-1 4 0 1 1 1 1 1h1c-1 1 0 1-1 2h3l4-2 1 1 1-1c1 1 2 2 4 2l1 1 1 1c-1 1-1 1-3 1 1 1 1 0 1 1l1 1c-5-1-10-1-14 0h-4-3v-2c-1 0-1 0-1-1l-2-2h-1v-2h-2v-4c0-1 0-3 1-3v-1-2h-1l1-1 3-1 2 1 1-2z" class="c"></path><path d="M336 776l1 1s1 1 1 3c-1 1-1 2-1 4 0 1 1 1 1 1l-1 3h0c-2-2-1-4-1-7h-1c0-2 0-3 1-5z" class="h"></path><path d="M347 785c1 1 2 2 4 2l1 1h-3l-1 2h-1l-3-3h0v2h-1l-2-2 4-2 1 1 1-1zm-15-13c0 1 1 2 1 2v1c1 1 1 3 1 4 0 2 0 3-1 5l1 2v1 4h0v1h-3v-2c-1 0-1 0-1-1l-2-2h-1v-2h-2v-4c0-1 0-3 1-3v-1-2h-1l1-1 3-1 2 1 1-2z" class="T"></path><path d="M333 774v1c1 1 1 3 1 4 0 2 0 3-1 5l1 2v1 4c-1 0-1-1-2-1v-4h-1l1-1v-5c0-2 0-4 1-6z" class="O"></path><path d="M377 791h4 1 1c1-1 3 0 4 0 2 0 5 0 7 1h0 4c1 1 1 1 1 2v10 4 1c-1 0-1 1-2 0-1 0-2 0-3 1h-1c-1-1-2-1-3-1s-1 1-1 1h-3 0c-2-1-5-1-8-1h-4-2v1c-3 0-5-1-8-1 0-1-1-2-1-2l1-1c1 1 1 1 2 1h1c-1-3-2-6-4-9l-1-1c0-1 1-2 3-3 0 0 1 1 2 1l1-1v1h1l1-4 2 1h2c1-1 2-1 3-1z" class="a"></path><path d="M378 797l1-5h1v2 2c-1 1-1 1-2 1z" class="G"></path><path d="M387 795c1-2 1-2 3-3h1v5 3c-1-1-2-1-3-2h-1v-3z" class="B"></path><path d="M380 794l1 3 1 1v1h1l1 2h2c-1 2-2 3-3 4h-1v-5l-1 1c0 1 0 3-1 4l-2 2-3-1 1-2v-1-3h1c0 1 0 1 1 2l1-1c0-1 0-3-1-4 1 0 1 0 2-1v-2z" class="L"></path><path d="M382 798c0-3 0-5 2-8v8h2c1-1 1-1 0-2l1-1v3h1c1 1 2 1 3 2v-3h1 1 0v-3c0-1 0-2 1-2h0 4c1 1 1 1 1 2v10l-1-1h-1v-4l-1-1-2 2h0-1 0l1 1 1 1h-3l-1-1v1h-3c-1 0-1-2-1-2l-4-1h-1v-1z" class="O"></path><path d="M391 802v-1l1 1h3l-1-1-1-1h0 1 0l2-2 1 1v4h1l1 1v4 1c-1 0-1 1-2 0-1 0-2 0-3 1h-1c-1-1-2-1-3-1s-1 1-1 1h-3 0c-2-1-5-1-8-1h0c1-1 2-1 3-1l-1-1h-2l2-2c1-1 1-3 1-4l1-1v5h1c1-1 2-2 3-4h-2l-1-2 4 1s0 2 1 2h3z" class="c"></path><path d="M388 802h3c0 2 0 4-2 6h-3v-1c1-2 1-2 2-5z" class="D"></path><path d="M397 803h1l1 1v4 1c-2-1-4 0-7-1 0-2-1-3 0-4h5v-1z" class="j"></path><path d="M370 791l2 1h2c-1 3-1 5-1 8 0 2 0 4 1 6h1l3 1h2l1 1c-1 0-2 0-3 1h0-4-2v1c-3 0-5-1-8-1 0-1-1-2-1-2l1-1c1 1 1 1 2 1h1c-1-3-2-6-4-9l-1-1c0-1 1-2 3-3 0 0 1 1 2 1l1-1v1h1l1-4z" class="S"></path><path d="M370 791l2 1v7l-2-1c0-1-1-2-1-3l1-4z" class="N"></path><path d="M370 798l2 1v6l-1 1c-1-2-2-3-2-4l1-1v-3z" class="T"></path><path d="M364 809c0-1-1-2-1-2l1-1c1 1 1 1 2 1h1 1c1 0 2 1 3 1 2 0 5 0 7 1h0-4-2v1c-3 0-5-1-8-1z" class="D"></path><path d="M237 738c-1-1-1-2-2-2l2-3 9 4c1 1 2 2 2 3 1 2 1 3 1 5l1 3 2 2h0c3 2 5 5 8 6v1h1c2 1 5 3 7 5l3 4c0 1 1 1 2 2h0l7 2 5 3h1c4 2 8 4 11 7 6 4 10 7 16 9l-1 1h-3c-1 1-1 1-3 1l1 1h1c1 1 1 1 3 2-3 1-6 3-8 2h-2-1 0l1 2c-1 1-2 1-2 2h0c-1-1-2-2-4-2 0 1-1 2-2 2l-1-1c-2 1-3 1-4 2h-2-2-1-1-2-2c-1-1-1-1-2-1l3 3 3 1c1 0 2 0 2 1l-1 2h-2c-2-1-3-2-5-1h-3 0-3c1 0 2 2 3 2s0-1 1 1v1h1 1l1 1-1 1 1 1c2 2 2 2 4 3l2 2-4 4v1h1 1v1h1 0l2 1h1v1c2 0 2 1 4 1l1 1c1 0 2-1 2-1l1 1h2 0l-1 2c1-1 2-1 2-2l1 1-1 2h0-1 0-1c-2-1-3-1-4-1v1h0c-1 1-1 1-2 0l-1-1c-1 0-2 1-3 1v2h-2c-1-1 0-1-1-1s-1 0-2-1c-2-1-4-2-6-4-1-1-3-1-4-2h-1l-3-3-1 2h-2-3 0-1c-1 0-2 0-3 1l2 1c-1 1-1 1-3 2l-4-1h-1l-1-1c0-1-1-4-2-4-1-1-2-1-3-1-2 0-4 0-6-1-4-2-7-7-8-11l1-1 2-2-1-1c-2 0-2 1-4-1-3-2-8-3-12-5-3-2-7-4-9-9h0c-1-1-1-1-2-1h-2c-5-2-5-5-8-9-2-3-4-4-6-7-1-3-1-6 0-9 2-4 6-5 9-6l1-1-3-6v-2c-1-3 0-6 2-8h1l1-1c1 1 1 2 2 2 2 1 5 4 7 5 1 1 3 2 4 3v1c1 1 2 1 3 2v-2-1l2 1c2 0 3-1 4-2 0 1 1 2 0 3h1c1 0 1 1 2 2h3c1 1 2 2 4 2h1c0 1 1 2 1 3h1c2 0 2 2 4 2 1-1 1-1 0-2v-2c-2-1-3-2-5-3-1-1-3-2-4-3-1-2 0-3 0-4h4l3-1 2-1h0l1-1v-4l-2-2h0z" class="G"></path><path d="M200 777v-2h1c1 2 2 2 3 2h1l1 2h-1l-2-1c-1 2 0 2-2 3 0-1-1-2-1-4z" class="P"></path><path d="M254 808c1 0 1-1 2-1l-1-1h-1l-3-3h-1v-1h3c1 1 2 1 3 0h0l1 1c1 0 1 0 1 1s-1 1-2 2c0 1 1 1 2 2l1 1h0v1l-3 2-1-1-1 1h-1l1-2c-1 0-1 0-2 1h0v-1l2-2z" class="j"></path><path d="M193 764c2 0 2 0 4 1 1 1 2 2 3 2h3c1 1 2 2 4 3l-3 1h-1 0c0-1-1-2-1-2-1 1 0 2-1 3-2 2-2 2-2 4h0l-1 3-1-1c0-1 1-3 1-5h0v-2l1-1h0l-2-2c-2-1-3-2-4-4z" class="P"></path><path d="M198 758c2 0 2-1 4 0 1 0 1 0 2 1 2 2 4 3 4 5v3l-1-1-1-1h-3v1 1h-3c-1 0-2-1-3-2-2-1-2-1-4-1l-2-2 7-4h0z" class="I"></path><path d="M202 758c1 0 1 0 2 1 2 2 4 3 4 5h0l-1-1h-3 0l-2 1h-1v-2s0-1 1-2h0v-2z" class="E"></path><path d="M198 758c2 0 2-1 4 0v2h0c-1 1-1 2-1 2v2c-1 0-3 1-4 1-2-1-2-1-4-1l-2-2 7-4h0z" class="H"></path><path d="M198 758c2 0 2-1 4 0v2h0c-1 1-1 2-1 2-2 1-2 1-3 1-1-2 0-3 0-5z" class="J"></path><path d="M199 776l1 1c0 2 1 3 1 4l2 2h4v1h0c1-1 1-1 1-2h1c0 1-1 1 0 3h0l2-2 1 1-2 2v1l1-1c1 0 2-1 4-2v1c0 1 0 1-1 1s-2 1-3 2h1c1 0 4-1 5-2l1-1h0v1s-2 2-2 3c1 0 1 0 2-1h1l1 1 1-1h1v3h1l1-1v2h1 0c0 1 1 0 0 1h-1l-2 1h0l1-1-1-1-2 2c-1 1-1 1-2 1 1-1 1-1 0-2h-2l-1-1c-1 0-3-1-4-1-1-1-2-1-3-2s-1-1-2-3c0-1 0 0-1-1-1 1-1 1-2 1-1-1-2-1-3-2h-1c-1-1-2-2-2-3l1-1v-1l1-3z" class="T"></path><path d="M218 788h1l1 1 1-1h1v3h1l1-1v2h1 0c0 1 1 0 0 1h-1l-2 1h0l1-1-1-1-2 2v-3l-2 1-1-1 2-2-1-1z" class="N"></path><path d="M197 758h1l-7 4 2 2c1 2 2 3 4 4l2 2h0l-1 1v2h0c0 2-1 4-1 5l1 1v1l-1 1c0 1 1 2 2 3h1c1 1 2 1 3 2 1 0 1 0 2-1 1 1 1 0 1 1 1 2 1 2 2 3v1c-1 1-1 1-2 0h0c-1-1-1-1-2-1h-2c-5-2-5-5-8-9-2-3-4-4-6-7-1-3-1-6 0-9 2-4 6-5 9-6z" class="M"></path><path d="M190 773l-1-1v-5-1c0-2 1-3 2-4l2 2c1 2 2 3 4 4l-2 2c-1 0-3-1-3 0-1 0-2 2-2 3z" class="G"></path><path d="M197 768l2 2h0l-1 1v2h0c0 2-1 4-1 5l-5-3-2-2c0-1 1-3 2-3 0-1 2 0 3 0l2-2z" class="j"></path><path d="M238 792h4c1 1 1 2 2 2 2 0 3 0 4 1s3 1 4 1v1c3 0 4 1 6 2l2 2v1h1 2v1 2l4 4c0 1-1 1 0 1l2 1h1l-1-1c0-1-1-2-1-3l-3-3h1c1 1 2 1 4 2h0c1 0 2 2 3 2s0-1 1 1v1h1 1l1 1-1 1 1 1c2 2 2 2 4 3l2 2-4 4v-1l-1 1-1-1-1-1c-1-1-1-1-2-1h-1v-1-1l-2 1h-7-1v1l-1-1h-2v-1c-1 0-2 1-3 1h-1l1-1h-1c-1 0-1 1-2 1v-2c-1 1-1 1-2 1v-1h-1c-1 0-1-1-2-1h-1l-1-1h-2l1-1-1-1v1c-1-1-1-2-2-2l1-1h1v2h0l2-2c1 0 1 0 2 1l1-1c1-1 2-2 4-2l-2 2v1h0c1-1 1-1 2-1l-1 2h1l1-1 1 1 3-2v-1h0l-1-1c-1-1-2-1-2-2 1-1 2-1 2-2s0-1-1-1l-1-1h0l-1-1-2-2h-1c-1-1-2 0-4-1v-2h-1c0 1 0 2-1 2h-1l1-2-1-1h-2l-1-1c-1 0-3-1-4-2z" class="D"></path><path d="M257 812c1-1 2-2 3-2h0c1 0 1 1 1 1h0 2v1h1 1 2c0 1 0 1-1 2 2 0 2-1 4 0h1c1 0 2 1 3 1-1 1-1 1-1 2l-2 1v-1h-2l-1-1-2 1v-2h-3 0l-1-1-1 1-1-1s1 0 0-1c0 0-1 0-2 1h0v-2h-1z" class="G"></path><path d="M268 807l-3-3h1c1 1 2 1 4 2h0c1 0 2 2 3 2s0-1 1 1v1h1 1l1 1-1 1 1 1c2 2 2 2 4 3l2 2-4 4v-1l-1 1-1-1-1-1c-1-1-1-1-2-1h-1v-1-1c0-1 0-1 1-2l1-2h0l-2-1v-1l-2-2c-1-1-2-2-3-2z" class="h"></path><path d="M250 810c1-1 2-2 4-2l-2 2v1h0c1-1 1-1 2-1l-1 2h1l1-1 1 1h1 1v2h0c1-1 2-1 2-1 1 1 0 1 0 1l1 1 1-1 1 1h0 3v2l2-1 1 1h2v1h-7-1v1l-1-1h-2v-1c-1 0-2 1-3 1h-1l1-1h-1c-1 0-1 1-2 1v-2c-1 1-1 1-2 1v-1h-1c-1 0-1-1-2-1h-1l-1-1h-2l1-1-1-1v1c-1-1-1-2-2-2l1-1h1v2h0l2-2c1 0 1 0 2 1l1-1z" class="r"></path><path d="M222 788l1 1v1c1 0 1 0 2-1l1 1h1c0 1 0 1 1 1s2 0 3 1l1 1c1 0 2 1 3 1h1c1 1 1 1 3 2l1-1h1l-1 1 3 3c0 1 0 1-1 2h1s0-1 1-1v2h2l-1 2h3-1l1 1c1-1 1-1 2-1l-1 2v2l3-2v1c-1 1-2 2-2 3l-1 1c-1-1-1-1-2-1l-2 2h0v-2h-1l-1 1c1 0 1 1 2 2v-1l1 1-1 1h2l1 1h1c1 0 1 1 2 1-1 1-1 1-2 1h-2-1v-2h-1c-2 0-3 0-4-2h0l-1-1-1-1h-1-1c1-1 1-1 1-2-1 0-1 1-3 1 0-1 0-1 1-3h0l-2 1v-1c0-1 1-2 1-3h-1l-1 2h-1 0l-1-1c-2 0-2 1-4-1-3-2-8-3-12-5-3-2-7-4-9-9 1 1 1 1 2 0v-1c1 1 2 1 3 2 1 0 3 1 4 1l1 1h2c1 1 1 1 0 2 1 0 1 0 2-1l2-2 1 1-1 1h0l2-1h1c1-1 0 0 0-1h0-1v-2l-1 1h-1v-3z" class="k"></path><path d="M224 798l3-1h0v2c1-1 1-1 3-1 1 0 1 1 2 1h0l1 1-1 1 1 1h0l1 1-2 3h0l-1-1v-2c-2-1-2-1-2-3h-1-1c-1 0-2-1-3-2z" class="g"></path><defs><linearGradient id="J" x1="220.635" y1="805.96" x2="218.011" y2="791.827" xlink:href="#B"><stop offset="0" stop-color="#171915"></stop><stop offset="1" stop-color="#322e30"></stop></linearGradient></defs><path fill="url(#J)" d="M206 790c1 1 1 1 2 0 0 1 1 2 2 3 2 1 5 2 8 4v-1l1 1h1 1v1c1 0 2 0 2-1l1 1c1 1 2 2 3 2h1 1c0 2 0 2 2 3v2c-2 0-2 1-4-1-3-2-8-3-12-5-3-2-7-4-9-9z"></path><path d="M222 788l1 1v1c1 0 1 0 2-1l1 1h1c0 1 0 1 1 1s2 0 3 1l1 1c1 0 2 1 3 1h1c1 1 1 1 3 2l1-1h1l-1 1 3 3c0 1 0 1-1 2h1s0-1 1-1v2h2l-1 2h3-1l1 1c1-1 1-1 2-1l-1 2v2l3-2v1c-1 1-2 2-2 3l-1 1c-1-1-1-1-2-1l-2 2h0v-2h-1l-1 1h0-1c1-1 1-1 1-3l-2 1 1-1c0-1 0-2 1-3-2 1-3 3-4 4l4-7-6 6c1-2 2-4 3-5l-1-1c-1 1-1 2-3 2 0-1 1-2 2-3l-1-1-1 1v-2-1l-2 1v-1h-1s-2-2-2-3l-2 1v-1l-2-2-2 2v-2c1-1 0 0 0-1h0-1v-2l-1 1h-1v-3z" class="Z"></path><path d="M242 808s1 0 1-1c1-1 1-1 3-2h0c-1 1-2 2-2 3 1 0 2 0 3-1h1v2h1v-1l3-2v1c-1 1-2 2-2 3l-1 1c-1-1-1-1-2-1l-2 2h0v-2h-1l-1 1h0-1c1-1 1-1 1-3l-2 1 1-1z" class="N"></path><path d="M232 806h1l1-2h1c0 1-1 2-1 3v1l2-1h0c-1 2-1 2-1 3 2 0 2-1 3-1 0 1 0 1-1 2h1 1l1 1 1 1h0c1 2 2 2 4 2h1v2h1 2c1 0 1 0 2-1h1v1c1 0 1 0 2-1v2c1 0 1-1 2-1h1l-1 1h1c1 0 2-1 3-1v1h2l1 1v-1h1 7l2-1v1 1h1c1 0 1 0 2 1l1 1 1 1 1-1v1 1h1 1v1h1 0l2 1h1v1c2 0 2 1 4 1l1 1c1 0 2-1 2-1l1 1h2 0l-1 2c1-1 2-1 2-2l1 1-1 2h0-1 0-1c-2-1-3-1-4-1v1h0c-1 1-1 1-2 0l-1-1c-1 0-2 1-3 1v2h-2c-1-1 0-1-1-1s-1 0-2-1c-2-1-4-2-6-4-1-1-3-1-4-2h-1l-3-3-1 2h-2-3 0-1c-1 0-2 0-3 1l2 1c-1 1-1 1-3 2l-4-1h-1l-1-1c0-1-1-4-2-4-1-1-2-1-3-1-2 0-4 0-6-1-4-2-7-7-8-11l1-1 2-2h0z" class="d"></path><path d="M256 822c2-1 2-1 3 0h2 1 1c0 1 0 1-1 2h-3 0-1l-2-2z" class="g"></path><path d="M257 818c1 0 2-1 3-1v1h2l1 1v-1h1 3l1 2h-1-1-5s0-1-1-1l-3-1z" class="k"></path><path d="M233 811h2c1 1 1 1 0 2h2 1c2 2 4 3 6 5h-2c-5-1-6-3-9-7z" class="l"></path><path d="M271 818l2-1v1 1h1c1 0 1 0 2 1l1 1 1 1 1-1v1 1h1 1v1h1 0l2 1h1v1c2 0 2 1 4 1l1 1c1 0 2-1 2-1l1 1h2 0l-1 2c1-1 2-1 2-2l1 1-1 2h0-1 0-1c-2-1-3-1-4-1v-1c-1 0-2-1-4-1h0c-1 2-3 3-5 3l-1-1h0l-2-1-2-2c0-1 0 0-1-1h-1v-1c-1 0-2-1-3-1s0 0-1-1h2c0-1-1-1-1-2l-2 2h-1c1-1 1-2 1-3h-1l-1-2h-3 7z" class="k"></path><path d="M271 818l2-1v1 1h1c1 0 1 0 2 1l1 1 1 1 1-1v1 1h1 1v1h1 0l2 1-1 1-2-1c-1 0-1 0-1 1h-1v-1l-1-1c0 1 0 2-1 2 0-2 1-2 1-3-2 0-3 1-4 3v-1c-1 0-2-1-3-1s0 0-1-1h2c0-1-1-1-1-2l-2 2h-1c1-1 1-2 1-3h-1l-1-2h-3 7z" class="Y"></path><defs><linearGradient id="K" x1="240.769" y1="823.293" x2="243.889" y2="812.852" xlink:href="#B"><stop offset="0" stop-color="#0f0e0d"></stop><stop offset="1" stop-color="#302e2d"></stop></linearGradient></defs><path fill="url(#K)" d="M229 809l1-1c1 0 2 1 3 1v2c3 4 4 6 9 7h2c3 1 4 1 6 3 1 0 1 0 2-1l1 1c1 0 2 1 3 1l2 2c-1 0-2 0-3 1l2 1c-1 1-1 1-3 2l-4-1h-1l-1-1c0-1-1-4-2-4-1-1-2-1-3-1-2 0-4 0-6-1-4-2-7-7-8-11z"></path><defs><linearGradient id="L" x1="255.17" y1="822.08" x2="253.073" y2="823.729" xlink:href="#B"><stop offset="0" stop-color="#2a2627"></stop><stop offset="1" stop-color="#2f312a"></stop></linearGradient></defs><path fill="url(#L)" d="M253 821c1 0 2 1 3 1l2 2c-1 0-2 0-3 1-1-1-2 0-3-2l1-2z"></path><path d="M203 767v-1-1h3l1 1 1 1 1 1h1c1 0 3 0 5 1 2 0 4 3 6 4 2 0 4 0 5-1 3 0 6 0 9 1 2 1 3 2 4 3l1-1 2 2c1-1 1-1 1-2h-1c-1-1-1-2-1-3 0 1 1 1 2 2h1v2c1 0 2 0 2-1v3h2c2 2 4 5 5 7l-1 1h0c1 1 1 2 1 2l-1 1v1h1c4 0 8 1 10 3l1 1c2 2 2 3 5 3l1 1c1 0 2 1 3 1 1 1 2 1 3 1h0l3 3 3 1c1 0 2 0 2 1l-1 2h-2c-2-1-3-2-5-1h-3 0-3 0c-2-1-3-1-4-2h-1l3 3c0 1 1 2 1 3l1 1h-1l-2-1c-1 0 0 0 0-1l-4-4v-2-1h-2-1v-1l-2-2c-2-1-3-2-6-2v-1c-1 0-3 0-4-1s-2-1-4-1c-1 0-1-1-2-2h-4-3v-1h-2c-1 0-2-1-4-1 0-1-1-1-1-2l-2 2v-2c-1 0-2-1-3-2h-1c-1 1-1 1-2 0 1-1 1-1 0-3-1 0 0 0-1 1h-1-1v-2c0-1-2-2-3-3-2 0-2 0-3-1v-2c-1-2-4-2-5-3 0-1 0-1 1-1v-1-1c-2-1-3-2-4-3z" class="b"></path><path d="M232 777c1 1 3 1 4 3h-1v1h-4v-1l1-3z" class="C"></path><path d="M252 796c2 0 2 0 3-1 3 0 1 4 5 2v4l-2-2c-2-1-3-2-6-2v-1z" class="B"></path><path d="M211 776h0c1 0 1-1 2-1v1 1h1 1 1c0 1 1 1 1 2 1 1 2 1 3 3 1 0 3-1 4 1l-3 3h1c-1 1-1 1-2 0 1-1 1-1 0-3-1 0 0 0-1 1h-1-1v-2c0-1-2-2-3-3-2 0-2 0-3-1v-2z" class="D"></path><path d="M223 786c1 0 1 0 1-1 1 0 1 0 1-1 1 1 2 2 2 3h5c1 1 2 2 3 2 1 1 2 1 3 1h1c1 2 2 2 3 2h-4-3v-1h-2c-1 0-2-1-4-1 0-1-1-1-1-2l-2 2v-2c-1 0-2-1-3-2z" class="B"></path><path d="M253 790c4 0 8 1 10 3l1 1c2 2 2 3 5 3l1 1c1 0 2 1 3 1 1 1 2 1 3 1h0l3 3 3 1c1 0 2 0 2 1l-1 2h-2c-2-1-3-2-5-1h-3 0c-3-2-6-2-7-5h0c-1 0-1-1-2-1l-1-2c-2-1-4-4-6-5l-1 1h-1l-2-4z" class="F"></path><path d="M270 798c1 0 2 1 3 1 1 1 2 1 3 1h0l3 3c-1 0-2 0-3 1h-1c-1-1-1-2-1-2l-1-1-1 2h-2v-5z" class="C"></path><path d="M203 767v-1-1h3l1 1 1 1 1 1h1c1 0 3 0 5 1 2 0 4 3 6 4 2 0 4 0 5-1 3 0 6 0 9 1 2 1 3 2 4 3l1-1 2 2c1-1 1-1 1-2h-1c-1-1-1-2-1-3 0 1 1 1 2 2h1v2c1 0 2 0 2-1v3h2c2 2 4 5 5 7l-1 1h0c1 1 1 2 1 2l-1 1v1h-1l1 2h-1l-2 1c-1-1-1-2-2-2h-2c-1 0-3-2-4-3h-1-2c-1 0-2-1-2-2l-2 1c-2-1-3-2-4-3v-1c2 0 3 0 5-1v-2h1c-1-2-3-2-4-3h-1l-2 1c-1 0-2-1-3-1h0-3-1c-1-1-2 0-3-1s0-2-2-3c0 1-1 2-2 2h0c-1-1-2-1-2-2l-2-2c-1-1-2 0-4 0v-1c-2-1-3-2-4-3z" class="F"></path><path d="M241 772c0 1 1 1 2 2h1v2c1 0 2 0 2-1v3h2c2 2 4 5 5 7l-1 1h0c1 1 1 2 1 2l-1 1v1h-1c-2 0-4-2-6-3-1-2 0-3-2-5-1-2-3-1-4-3-1 0 0-2 0-3l1-1 2 2c1-1 1-1 1-2h-1c-1-1-1-2-1-3z" class="U"></path><path d="M239 776l1-1 2 2c1 1 4 3 4 4 1 1 0 2 1 2 0 1 1 1 1 1h2v1l-1 1c-1 0-3 0-4 1-1-2 0-3-2-5-1-2-3-1-4-3-1 0 0-2 0-3z" class="S"></path><path d="M198 741l1-1c1 1 1 2 2 2 2 1 5 4 7 5 1 1 3 2 4 3v1c1 1 2 1 3 2v-2-1l2 1c2 0 3-1 4-2 0 1 1 2 0 3h1c1 0 1 1 2 2h3c1 1 2 2 4 2h1c0 1 1 2 1 3h1c2 0 2 2 4 2 1-1 1-1 0-2v-2c1 1 2 2 4 3 5 2 14 2 15 8 1 1 0 2 0 3h-1c-1 0-2 0-3 1 0-1-1-1-1-1l-6-3s-1 0-2-1l-1-1c-1 0-1 0-2 1v2h1 2 0c0 1 0 1 1 2 1 0 2 0 3-1 1 1 0 1 0 2s0 1 1 2c-1 0-2 1-3 1 0 1-1 1-2 1v-2h-1c-1-1-2-1-2-2 0 1 0 2 1 3h1c0 1 0 1-1 2l-2-2-1 1c-1-1-2-2-4-3-3-1-6-1-9-1-1 1-3 1-5 1-2-1-4-4-6-4-2-1-4-1-5-1h-1l-1-1v-3c0-2-2-3-4-5-1-1-1-1-2-1-2-1-2 0-4 0h0-1l1-1-3-6v-2c-1-3 0-6 2-8h1z" class="M"></path><path d="M212 754c2 0 3 0 4 1s1 1 1 2c1 0 1 0 2 1l-1 1c0 1 0 2 1 2l3 1v2c-3-1-4-2-7-2l2 2h2c1 1 1 1 1 3-1-1-2-1-3-1v1c-3 2-4 0-7 1h-1l-1-1v-3c0-2-2-3-4-5l1-1h2c0 1 1 1 1 2h3 3l1-1v-2l-3-3z" class="p"></path><path d="M205 758h2c0 1 1 1 1 2s0 1 1 1 2 1 3 2v1l2 1c1 0 2 1 3 2-3 2-4 0-7 1h-1l-1-1v-3c0-2-2-3-4-5l1-1z" class="e"></path><path d="M215 753v-2-1l2 1c2 0 3-1 4-2 0 1 1 2 0 3h1c1 0 1 1 2 2 1 0 2 1 3 2h-1l-1 2h-1c1 1 3 2 3 3 1 1 1 1 1 2l3 2v-1l-2-1h1v-1l1 1c2 3 4 3 7 5 1 0 1 0 2-1 0 1 0 1 1 2h1 2 0c0 1 0 1 1 2 1 0 2 0 3-1 1 1 0 1 0 2s0 1 1 2c-1 0-2 1-3 1 0 1-1 1-2 1v-2h-1c-1-1-2-1-2-2 0 1 0 2 1 3h1c0 1 0 1-1 2l-2-2-1 1c-1-1-2-2-4-3 1-1 1-2 1-3-1 0-1-1-2-1h-3c0-1-1-1-1-2v-1c-1 0-2 0-3 1h0v-2c-2 0-3-1-5-1v-2l-3-1c-1 0-1-1-1-2l1-1c-1-1-1-1-2-1 0-1 0-1-1-2s-2-1-4-1c0-1-2-2-3-2l1-1h2c1 1 2 1 3 2z" class="Y"></path><path d="M230 767h1c1-1 1 0 2-1v1l1 1h1c1 0 1 0 2 1h1v1l-1 1h0c2 0 2 0 4 1 0 1 0 2 1 3h1c0 1 0 1-1 2l-2-2-1 1c-1-1-2-2-4-3 1-1 1-2 1-3-1 0-1-1-2-1h-3c0-1-1-1-1-2z" class="n"></path><path d="M215 753v-2-1l2 1c2 0 3-1 4-2 0 1 1 2 0 3h1c1 0 1 1 2 2 1 0 2 1 3 2h-1l-1 2h-1c-1 0-2-1-4-1h-1v-1l1-1c-2 0-2 0-3-1l-1-1h-1z" class="Z"></path><path d="M224 754h3c1 1 2 2 4 2h1c0 1 1 2 1 3h1c2 0 2 2 4 2 1-1 1-1 0-2v-2c1 1 2 2 4 3 5 2 14 2 15 8 1 1 0 2 0 3h-1c-1 0-2 0-3 1 0-1-1-1-1-1l-6-3s-1 0-2-1l-1-1c-1 0-1 0-2 1v2c-1-1-1-1-1-2-1 1-1 1-2 1-3-2-5-2-7-5l-1-1v1h-1l2 1v1l-3-2c0-1 0-1-1-2 0-1-2-2-3-3h1l1-2h1c-1-1-2-2-3-2z" class="j"></path><path d="M227 756s1 1 2 1c0 1 1 0 2 1s2 2 4 3v1h1c5 1 9 5 14 6 1 1 2 1 3 2 1 0 1 0 2-1 1 1 0 1 1 2-1 0-2 0-3 1 0-1-1-1-1-1l-6-3s-1 0-2-1l-1-1c-1 0-1 0-2 1v2c-1-1-1-1-1-2-1 1-1 1-2 1-3-2-5-2-7-5l-1-1v1h-1l2 1v1l-3-2c0-1 0-1-1-2 0-1-2-2-3-3h1l1-2h1zm-29-15l1-1c1 1 1 2 2 2 2 1 5 4 7 5 1 1 3 2 4 3v1h-2l-1 1c1 0 3 1 3 2l3 3v2l-1 1h-3-3c0-1-1-1-1-2h-2l-1 1c-1-1-1-1-2-1-2-1-2 0-4 0h0-1l1-1-3-6v-2c-1-3 0-6 2-8h1z" class="N"></path><path d="M198 741l1-1c1 1 1 2 2 2 2 1 5 4 7 5l-1 1c-2 0-3 0-5-1-1 0-3 1-4 0h-1-1c0-1 0-2 1-3l1-3z" class="T"></path><path d="M195 751v-2c-1-3 0-6 2-8h1l-1 3c-1 1-1 2-1 3v3h1c1-1 1 0 1-1 1 0 3 0 4 1l-1 1 1 1c1 0 2-2 3-1l-1 2 1 1h2c1 1 1 2 1 3 1 0 2 1 3 1l2-1 1 1c-1 0-1 0-2 1l2 1h-3-3c0-1-1-1-1-2h-2l-1 1c-1-1-1-1-2-1-2-1-2 0-4 0h0-1l1-1-3-6z" class="U"></path><path d="M195 751h2c0 1 1 1 1 2l2 1c0 1-1 2-2 3l-3-6z" class="W"></path><path d="M200 754h1c1 1 1 1 2 1l2 3-1 1c-1-1-1-1-2-1-2-1-2 0-4 0h0-1l1-1c1-1 2-2 2-3z" class="X"></path><path d="M237 738c-1-1-1-2-2-2l2-3 9 4c1 1 2 2 2 3 1 2 1 3 1 5l1 3 2 2h0c3 2 5 5 8 6v1h1c2 1 5 3 7 5l3 4c0 1 1 1 2 2h0l7 2 5 3h1c4 2 8 4 11 7 6 4 10 7 16 9l-1 1h-3c-1 1-1 1-3 1l1 1h1c1 1 1 1 3 2-3 1-6 3-8 2h-2-1 0l1 2c-1 1-2 1-2 2h0c-1-1-2-2-4-2 0 1-1 2-2 2l-1-1c-2 1-3 1-4 2h-2-2-1-1-2-2c-1-1-1-1-2-1h0c-1 0-2 0-3-1-1 0-2-1-3-1l-1-1c-3 0-3-1-5-3l-1-1c-2-2-6-3-10-3h-1v-1l1-1s0-1-1-2h0l1-1c-1-2-3-5-5-7h-2v-3c1 0 2-1 3-1-1-1-1-1-1-2s1-1 0-2c-1 1-2 1-3 1-1-1-1-1-1-2h0-2-1v-2c1-1 1-1 2-1l1 1c1 1 2 1 2 1l6 3s1 0 1 1c1-1 2-1 3-1h1c0-1 1-2 0-3-1-6-10-6-15-8-2-1-3-2-4-3-2-1-3-2-5-3-1-1-3-2-4-3-1-2 0-3 0-4h4l3-1 2-1h0l1-1v-4l-2-2h0z" class="T"></path><path d="M264 780c2-1 3-2 6-2-1 1-2 1-3 2 0 1 0 1 1 2h-1-1v1h0c2 2 2 4 3 7l-3-3c-1 0-1-1-1-1v-4c-1 0-2 1-3 2l-1-3c1-1 2-1 3-1z" class="k"></path><path d="M263 777l1-1c1 0 2 0 3-1l2 1c1-1 0-2 2-2v2c2 0 1-2 3 0 1 1 3 2 5 2-2 1-7 0-9 0-3 0-4 1-6 2v-2l-1-1z" class="g"></path><path d="M262 784c1-1 2-2 3-2v4s0 1 1 1l3 3c3 2 6 1 10 1h1c2 0 2-2 5 0h3 2 1l2 1c1 1 3 2 4 3s2 0 3 0v1l1 2c-1 1-2 1-2 2h0c-1-1-2-2-4-2-4-3-9-4-14-5l-11-1h-4v-1l-2-2-2-5z" class="n"></path><path d="M280 791c2 0 2-2 5 0h3 2 1l2 1c1 1 3 2 4 3-4 1-7-2-11-2-2-1-5-1-7-2h1z" class="Y"></path><path d="M286 773c4 2 8 4 11 7 6 4 10 7 16 9l-1 1h-3c-2 0-4-1-6-2-1-1-1 0-2 0v1l5 2h-6v-2l-3-1-1-2c0-1-2-1-3-2l-1-1v-1c1 1 1 1 2 1l1-1c-2-1-3-2-5-3h-1c-1-1-2-2-3-2-1-1 0-3 0-4z" class="P"></path><path d="M268 762l3 4c0 1 1 1 2 2h0l7 2 5 3h1c0 1-1 3 0 4-1 0-1-1-2-1h-4c-2 0-4-1-5-2h-2c-1 0-1-1-2-1l-1-1c-1 1-2 1-3 2h0l-1 1-1-1v-1h1c0-1 1-1 1-1l-1-1v-3-3l-1-1c1-1 2-1 3-2z" class="G"></path><path d="M285 773h1c0 1-1 3 0 4-1 0-1-1-2-1h-4v-1c0-1 1-2 2-2l2 1 1-1z" class="D"></path><path d="M273 768l7 2-1 1c0 1 0 2-1 3-2-2-1-2-2-4-1 1-1 2-2 2s-2-1-2-2l1-2z" class="B"></path><path d="M264 794v-1l1-1h5l11 1c5 1 10 2 14 5 0 1-1 2-2 2l-1-1c-2 1-3 1-4 2h-2-2-1-1-2-2c-1-1-1-1-2-1h0c-1 0-2 0-3-1-1 0-2-1-3-1l-1-1c-3 0-3-1-5-3z" class="P"></path><path d="M281 793c5 1 10 2 14 5 0 1-1 2-2 2l-1-1h-3l-1-2h-4c-1 1-1 1-3 1h0c0-2-1-2-2-2 0 0-1 0-1 1h-1-1c0-2 0-2 1-3l4-1z" class="V"></path><path d="M270 783l-1-1c1-2 2-1 3-3 3 0 15 1 17 3 1 1 2 2 2 4 1 1 1 1 2 3l1 1c0 1 0 1-1 2l-2-1h-1-2-3c-3-2-3 0-5 0l-1-1c-1 0-2 0-3-1h-5c-1-2-1-4-1-6z" class="j"></path><path d="M285 788c1-1 1-2 3-2v1 2c-1 1-1 1-3 1v-2z" class="O"></path><path d="M270 783c1 0 4 0 6 1-1 0-2 1-3 1l-1 2v1h2c0-1 0-1 1-2l1 2h2 3 4v2c2 0 2 0 3-1 2 1 2 1 4 1l1-1 1 1c0 1 0 1-1 2l-2-1h-1-2-3c-3-2-3 0-5 0l-1-1c-1 0-2 0-3-1h-5c-1-2-1-4-1-6z" class="L"></path><path d="M241 769v-2c1-1 1-1 2-1l1 1c1 1 2 1 2 1l6 3s1 0 1 1l4 2c1 0 2 1 2 2 2 0 3 0 4 1l1 1v2c-1 0-2 0-3 1l1 3 2 5 2 2v1h4-5l-1 1v1l-1-1c-2-2-6-3-10-3h-1v-1l1-1s0-1-1-2h0l1-1c-1-2-3-5-5-7h-2v-3c1 0 2-1 3-1-1-1-1-1-1-2s1-1 0-2c-1 1-2 1-3 1-1-1-1-1-1-2h0-2-1z" class="T"></path><path d="M253 774c0 2 1 1 2 2s2 3 3 4l-1 2 1 1h2v1l-1 1-1-1c-3 0-6-4-9-6l4-4z" class="L"></path><path d="M252 790v-1l1-1s0-1-1-2h0l1-1h1 2c0 1 0 1 1 2h2c2 2 2 4 6 4h1v1h4-5l-1 1v1l-1-1c-2-2-6-3-10-3h-1z" class="k"></path><path d="M241 769v-2c1-1 1-1 2-1l1 1c1 1 2 1 2 1l6 3s1 0 1 1l4 2c1 0 2 1 2 2 2 0 3 0 4 1l1 1v2c-1 0-2 0-3 1l1 3 2 5-1 1-1-4c-1 0-2 0-3-1l1-1v-1h-2l-1-1 1-2c-1-1-2-3-3-4s-2 0-2-2h-4c-1-1-1-1-1-2s1-1 0-2c-1 1-2 1-3 1-1-1-1-1-1-2h0-2-1z" class="B"></path><path d="M259 776c2 0 3 0 4 1l1 1v2c-1 0-2 0-3 1l-2-5z" class="e"></path><path d="M237 738c-1-1-1-2-2-2l2-3 9 4c1 1 2 2 2 3 1 2 1 3 1 5l1 3 2 2h0c3 2 5 5 8 6v1h1c2 1 5 3 7 5-1 1-2 1-3 2l1 1v3 3l1 1s-1 0-1 1h-1v1c-1-1-1 0-2-1-1 0 0 0-1-1s-2-2-3-4-2-3-4-5c-1 0-3-2-4-2-2-1-4-1-6-2-1 0-2-1-3-1-1-1-1-1-2-1-1-1-2-1-3-2s-2-1-4-1c-1-1-3-2-4-3-1-2 0-3 0-4h4l3-1 2-1h0l1-1v-4l-2-2h0z" class="I"></path><path d="M254 757h-2c-1 0-2-2-2-2v-1c1 1 2 1 3 2v-1h2c2 1 3 2 4 4l-1 1c-2-1-2-2-4-3z" class="F"></path><path d="M249 758c0-1 1-1 2-1 1 1 2 1 2 2 1-1 1-1 1-2 2 1 2 2 4 3l1-1h1v1c1 1 2 1 3 1v1h0v1l-3 1c-2-2-6-4-9-5-1 0-2-1-2-1z" class="B"></path><path d="M239 747v-1l1-1 1-1h0 1l1-1h1v1c2 2 3 3 5 4h1 0l2 2 1 2c-1 1-1 0-2 0s0 0-1 1c-2 0-4-1-6-2h-3-1-4l-1-1h-3v1l-1-1v-2h3 1c1 0 2 0 3-1h1z" class="H"></path><path d="M239 747v-1l1-1 1-1h0 1l1-1h1v1c0 1-1 2 0 3s4 3 5 4c-3-1-5-2-8-2l-1 1h-1c-1-1-3-1-4 0h-3v1l-1-1v-2h3 1c1 0 2 0 3-1h1z" class="I"></path><path d="M237 738c-1-1-1-2-2-2l2-3 9 4c1 1 2 2 2 3 1 2 1 3 1 5l1 3h0-1c-2-1-3-2-5-4v-1h-1l-1 1h-1 0l-1 1-1 1v1l-1-2h0l1-1v-4l-2-2h0z" class="Q"></path><path d="M237 738l2-1 2 1 2 2-1 1-3-1-2-2z" class="E"></path><path d="M241 738c3-1 4 0 6 2h1c1 2 1 3 1 5h0-1c-2-1-3-3-5-5l-2-2z" class="J"></path><path d="M237 738c-1-1-1-2-2-2l2-3 9 4c1 1 2 2 2 3h-1c-2-2-3-3-6-2l-2-1-2 1h0z" class="F"></path><path d="M238 745l1 2h-1c-1 1-2 1-3 1h-1-3v2l1 1v-1h3l1 1 4 4h1l1-1s1 1 2 1c1 2 1 2 3 2l2 1s1 1 2 1c3 1 7 3 9 5l3-1 1 1-1 1c0 1 2 2 3 3v3l1 1s-1 0-1 1h-1v1c-1-1-1 0-2-1-1 0 0 0-1-1s-2-2-3-4-2-3-4-5c-1 0-3-2-4-2-2-1-4-1-6-2-1 0-2-1-3-1-1-1-1-1-2-1-1-1-2-1-3-2s-2-1-4-1c-1-1-3-2-4-3-1-2 0-3 0-4h4l3-1 2-1z" class="h"></path><path d="M263 763l1 1-1 1c0 1 2 2 3 3v3l1 1s-1 0-1 1h-1l-2-2h-1l1-1c-1-1-1-1-1-2h0c-1-2-1-3-2-4l3-1z" class="j"></path><path d="M658 700l1-1c1 0 3 0 5-1h0c2 1 4 1 6 1h13l5 1v1 2h1l1 12c0 1-1 2 0 4s0 3 0 4l1 1h1c1 1 3 2 4 3l1 1s1 0 1 1l-1 2c1 1 1 2 2 2 1 1 2 1 3 2 0 1-1 3-1 4h-1l-2 2 1 1 1-1c0 1 0 2-1 3l-1 2c-1 1-1 2-2 4l-1 3-1 2c0 2 0 3 1 5l-2 3v1l-3-3v1h-1c-2 0-3 1-4 3h0c1 0 3 1 3 2v3h0-2l1 1v3l1 4v2l-1 12v11 11 4 2c2 0 3 1 5 2 0 1 1 1 1 3 0 0-1 1-1 2l-4-1c-1 0-3 0-5-1h-3l-6-1h-6-5l1 2c-1 1-2 1-4 1h0c-4-1-8 1-11 0h0-4c-1 1-1 1-3 1l-2 2-2 3-1-2c-2 0-2 0-3 2-1-3-2-4-4-5-1 1-1 1-2 1l-2-1-1-1v-2l-1 1v2h-1l-1-1 2-5c3-5 2-17 0-22 0-2-2-5-2-6l1-2c1-3 3-6 5-8 0-2-1-4-1-6 0-4 3-7 5-10v-1c0-4 2-6 3-9 0-1-2-2-3-3v-3c0-2 1-4 0-6-1-1-1-1-2-1 1-1 2-2 4-3h-5v-1c-1-3-1-10 1-12 1-1 2-2 3-2v1c-1 1-2 2-2 3l1 1c0-1 0-1 1-2h0 1c0-1 1-2 1-3h-1l2-2 1-21c0-1 1-2 1-3v2l2 1v1c0 1 0 1 1 1h4v1c1 0 1 0 1-1 1 0 2-1 2-2l2 1c1 0 1 0 2-1h0c0-1 2-3 2-3h2s1-1 2-1z" class="M"></path><path d="M639 735l3-1v1c0 1-1 1-1 2 1 1 2 0 2 1-2 1-4 1-6 1 1-2 0-3 2-4z" class="O"></path><path d="M692 741c1 0 2 1 3 1l-1 2 1 1c1-1 2-1 3-2v3c-1 1-1 2-2 4l-1 3c0-1 0-1-1-1v-1l-1-1 2-2h0v-3l-2 1c0-2-1-3-2-4l1-1z" class="t"></path><path d="M635 727h-1l2-2h2l1 6v2h3 8v1h-8l-3 1h-2l-1-2c1-2 1-2 1-3l-1-1c-1-1 1-1 1-2-1-1-1-1-2 0z" class="W"></path><path d="M639 741c2 0 4 1 5 0s3 0 5 0c4-1 8-1 12-1l1 2c-2 0-2 0-4 1h-1c-1 0-1 1-2 2v-1l-1-1h-1l-2-1c-1 0-1 1-2 1-2 0-3 0-4 1v1l-1 2h-1v-1c-1-1-1-1-2-3l-1 1h-1l-2-2 2-1z" class="X"></path><path d="M639 741l4 1c-1 0-1 1-2 1l-1 1h-1l-2-2 2-1z" class="W"></path><path d="M665 745l1-2 1-1c1 1 1 2 1 3v1c-1 1 0 3 0 4h1c1-2 2-3 3-4h-1v-3h2c1 0 1 1 2 2-1 1-2 3-1 5v1c2 0 2 0 3 2l-2-1h-9l-4-1c1-1 2-4 3-5l1-1h-1z" class="T"></path><path d="M650 734h4l2 2h1c0-1 0-1-1-2l1-1h4l-1 1 2 1h-1 0c-1 1-2 1-4 2l-14 1c0-1-1 0-2-1 0-1 1-1 1-2v-1h8zm30 24l1 1v-1-1h3c1 1 2 1 3 1l1 2h0c-1 0-1 0-1 1h0c-2 1-3 1-5 1l-2-2v1h-2l-1 1h1 4v1c-3 2-6 0-9 2h-2v-1c1-1 2-2 2-3-1 0-3 1-5 1v1h1v1h-3c-1 1-1 1-2 1l1-1c1-2 5-6 8-6 1 0 1 0 2 1h0c2 0 2 0 3-1h1 1zm-7-15h0c1-1 2-1 3-2l1 1h1l1 1c1 0 1 0 1 1 2 1 2 2 3 2 1-1 1 0 1-1s0-1 1-1l-1-2c1 0 1 0 2 1 1 0 1 1 2 1 0 3-2 6-4 8-1 1-1 1-2 1h-4-1c-1-2-1-2-3-2v-1c-1-2 0-4 1-5-1-1-1-2-2-2z" class="N"></path><path d="M673 743h0c1-1 2-1 3-2l1 1h1l1 1c1 0 1 0 1 1l-2 4-1 2 1 1h1v-1c0-1 1-1 2-1 0-1 1-1 1-2l1 1c-1 2-2 3-3 4l-2 1h-1c-1-2-1-2-3-2v-1c-1-2 0-4 1-5-1-1-1-2-2-2z" class="n"></path><path d="M677 742h1l1 1c1 0 1 0 1 1l-2 4c-1-2-1-4-1-6z" class="g"></path><path d="M645 745v-1c1-1 2-1 4-1 1 0 1-1 2-1l2 1h1l1 1v1c1-1 1-2 2-2h1c2-1 2-1 4-1l1 1c1 1 1 1 1 2h1 1l-1 1c-1 1-2 4-3 5-4 0-8 1-11 0 0-1 1-1 2-2-1-1-1-1-1-2-1 1-2 1-3 2-1 0-1 0-2-1 0-1 0-1 1-2h1l-2-2-1 1h-1z" class="d"></path><path d="M678 733c1-1 2-1 3-2h2 2c1 1 1 2 2 3l-1 1c-1-1-2-1-3 0h-1c-2 1-4 1-5 1h0c3 1 6 1 8 2 3 1 7 0 10 3v1c-1 0-2-1-3-1l-1 1h-2-2c-1 0-1-1-2-1s-2 0-3-1c-3-1-6 0-9 0l-10-1h3c1-1 3-1 5 0 3 1 7 0 10 0v-1l-24-1c2-1 3-1 4-2h0 1l-2-1 1-1c6 1 12 0 18 1l-1-1z" class="L"></path><path d="M682 740h1l1-1c1 0 5 1 6 1s1 0 2 1l-1 1h-2-2c-1 0-1-1-2-1s-2 0-3-1z" class="p"></path><path d="M684 723c2 1 3 1 4 1h0l2 2 1-1c1 1 1 3 1 4l3 1c0 1-1 2-1 3 1 1 1 1 3 1l2-1c1 1 2 1 3 2 0 1-1 3-1 4h-1l-2 2 1 1 1-1c0 1 0 2-1 3l-1 2v-3c-1 1-2 1-3 2l-1-1 1-2v-1c-3-3-7-2-10-3-2-1-5-1-8-2h0c1 0 3 0 5-1h1c1-1 2-1 3 0l1-1c-1-1-1-2-2-3h-2-2c-1 1-2 1-3 2 0-2 1-4 1-6h-1c0-2 0-2 1-3h0l1 1c2 0 3-1 4-2z" class="M"></path><path d="M690 726l1-1c1 1 1 3 1 4l3 1c0 1-1 2-1 3 1 1 1 1 3 1l2-1c1 1 2 1 3 2 0 1-1 3-1 4h-1l-2 2 1 1 1-1c0 1 0 2-1 3l-1 2v-3l-1-1c0-2 0-4-1-5l-3-3c-1-2-1-4-2-5s-1-2-1-3z" class="r"></path><path d="M684 723c2 1 3 1 4 1h0v1c-1 1 0 3 0 4v1h-1v1h1v5l-1 1h-1c0-1-1-1-2-1l-1-1c1-1 2-1 3 0l1-1c-1-1-1-2-2-3h-2-2c-1 1-2 1-3 2 0-2 1-4 1-6h-1c0-2 0-2 1-3h0l1 1c2 0 3-1 4-2z" class="Z"></path><path d="M641 743c1 2 1 2 2 3v1h1l1-2h1l1-1 2 2h-1c-1 1-1 1-1 2 1 1 1 1 2 1 1-1 2-1 3-2 0 1 0 1 1 2-1 1-2 1-2 2 3 1 7 0 11 0l4 1h9l2 1h1 4l-1 2h1v1c-1 1-2 0-2 2h-1-1c-1 1-1 1-3 1h0c-1-1-1-1-2-1-3 0-7 4-8 6l-1 1c1 0 1 0 2-1h3v-1h-1v-1c2 0 4-1 5-1 0 1-1 2-2 3v1h0-2-2c-1 2-1 2-3 2 1 1 2 1 3 2l1-1h2v1h-1-4l-2 1h0c-1 1 0 1-1 1l-1-1c-2-1-3-1-5-1-1 1-1 2-1 3l-2-1v-1h-2v-1c1 0 2 0 3-1h0 3l1-1c-1-1-1-2-2-2-1-1-1-1-1-2h3c1 1 1 1 2 1 2-1 3-2 5-3h1c0-1 1-2 2-3h0v-1l-1-1c-1 0-1 1-2 1l-1-1-1-1h-1l1 2h-1 0l-1 1h0 0c-2 0-2-1-2-2 1-1 1-1 3-1v-1-1h-10l-3 1h-1c-2 1-4 0-5 0l1-1h0l-1-1c-1 0-3-1-3-2h0c0-1-1-1-1-1 0-2 0-4 1-5l1-1z" class="g"></path><path d="M668 758l2-2 1 1c0 1-1 1-2 2-2 1-2 2-4 3l-2 2c-2 2-4 2-5 3h0c-1-1-1-2-2-2-1-1-1-1-1-2h3c1 1 1 1 2 1 2-1 3-2 5-3h1c0-1 1-2 2-3z" class="N"></path><path d="M634 742h3l2 2h1c-1 1-1 3-1 5 0 0 1 0 1 1h0c0 1 2 2 3 2l1 1h0l-1 1c1 0 3 1 5 0h1l3-1h10v1 1c-2 0-2 0-3 1 0 1 0 2 2 2h0 0l1-1h0 1l-1-2h1l1 1 1 1c1 0 1-1 2-1l1 1v1h0c-1 1-2 2-2 3h-1c-2 1-3 2-5 3-1 0-1 0-2-1h-3c0 1 0 1 1 2 1 0 1 1 2 2l-1 1h-3 0c-1 1-2 1-3 1v1h2v1c0 1-1 2-1 3v1c-1-1-3-2-5-3 0 1 0 2-1 4l-1 1h-1c1-1 1-1 1-2 1-1 1-2 0-4h-2-1c1 0 2-1 3-2h0l-1-1c1-1 1-1 1-2l-4 2h-4-2c-3 2-6 5-6 8-1 1-1 3 0 5v3h-1c0-2-1-4-1-6 0-4 3-7 5-10v-1c0-4 2-6 3-9 0-1-2-2-3-3v-3c0-2 1-4 0-6-1-1-1-1-2-1 1-1 2-2 4-3z" class="d"></path><path d="M634 742h3l2 2h1c-1 1-1 3-1 5 0 0 1 0 1 1h0c0 1 2 2 3 2l1 1h0l-1 1c1 0 3 1 5 0h1l3-1h10v1 1c-2 0-2 0-3 1l-1-1h1c-1-1-2-1-3-1l-1 1 1 1h-3l-1 1h-1v-1-1c-3 0-4 1-6 1h-1c0 1-1 1-1 1l-1 1c-1-1-1 0-1-1-2 0-3 1-4 2l-1 1 1 1h-1c-1 1-2 2-2 3s0 0-1 2h0l-1 1c0-4 2-6 3-9 0-1-2-2-3-3v-3c0-2 1-4 0-6-1-1-1-1-2-1 1-1 2-2 4-3z" class="X"></path><path d="M635 745v1l2 7c1 1 1 1 2 1h0l-1 1c-2 0-3 0-5-1l1-1v-1c-1-2 0-5 1-7z" class="B"></path><path d="M658 700l1-1c1 0 3 0 5-1h0c2 1 4 1 6 1h13l5 1v1 2h1l1 12c0 1-1 2 0 4s0 3 0 4l1 1h1c1 1 3 2 4 3l1 1s1 0 1 1l-1 2c1 1 1 2 2 2l-2 1c-2 0-2 0-3-1 0-1 1-2 1-3l-3-1c0-1 0-3-1-4l-1 1-2-2h0c-1 0-2 0-4-1-1 1-2 2-4 2l-1-1h0c-1 1-1 1-1 3h1c0 2-1 4-1 6l1 1c-6-1-12 0-18-1h-4l-1 1c1 1 1 1 1 2h-1l-2-2h-4v-1h-8-3v-2l-1-6h-2l1-21c0-1 1-2 1-3v2l2 1v1c0 1 0 1 1 1h4v1c1 0 1 0 1-1 1 0 2-1 2-2l2 1c1 0 1 0 2-1h0c0-1 2-3 2-3h2s1-1 2-1z" class="i"></path><path d="M644 724h1 2c1 0 1 0 1 1v1 4l1 1c0-1 1-1 1-2l1-1 1 1v1 2l-2 1h-8 0l2-1c1-1 1-1 1-2-1-2 0-4-1-6z" class="c"></path><path d="M667 726l2-2c1 0 3 0 4 1s1 2 1 3l-1 1c1 1 1 1 1 2h2v1h-9l2-2v-1-1-1h-2v-1h0z" class="D"></path><path d="M639 731v-5c2-2 3-1 5-2 1 2 0 4 1 6 0 1 0 1-1 2l-2 1h0-3v-2z" class="B"></path><path d="M638 703l2 1v1c0 1 0 1 1 1h4v1h-1-1l-3 2c1 1 1 1 1 2l-1 1 1 1-1 1v3h0 1v6l-2 1-1-1v-20z" class="K"></path><path d="M654 718c1 0 2-1 3 0l2 2h1v2c1 0 2 0 2 1v1l3 1c0 1 1 1 1 1h1v1h2v1 1 1l-2 2h-13-1v-6s1-2 2-2c0-1 1 0 2-1l-1-2c-1 1-2 1-3 1l-1-1 2-3z" class="O"></path><path d="M662 724l3 1c0 1 1 1 1 1h1v1l-1 3h-1-3v-6z" class="C"></path><path d="M660 722c1 0 2 0 2 1v1 6c0 1-1 1-2 1-1-2 0-5 1-7-2 0-2 0-3-1l2-1z" class="d"></path><path d="M645 707c1 0 1 0 1-1 1 0 2-1 2-2l2 1c1 0 1 0 2-1l3 3-1 1h0c-1-1-1 0-2 0l1 3-2 2 1 1v2l-1 1h-1l-1-1h-1c0 1-1 1-2 1 0 2 1 3 0 4v1l-3 1h-2v-6h-1 0v-3l1-1-1-1 1-1c0-1 0-1-1-2l3-2h1 1z" class="L"></path><path d="M649 716c-1-1-1-1-2-1-1-1-1-2-2-3h1c1 0 3 1 4 1h1l1 1v2l-1 1h-1l-1-1z" class="M"></path><path d="M641 717c0-1 1-2 3-3h0v1c-1 3-1 5-1 8h-2v-6z" class="C"></path><path d="M645 707c1 0 1 0 1-1 1 0 2-1 2-2l2 1c1 0 1 0 2-1l3 3-1 1h0c-1-1-1 0-2 0-2 0-2-2-4-1l-3 3v1c-1 0-2 0-3-1 1-1 1-2 2-3h1z" class="p"></path><path d="M662 718c1-2 2-5 1-8v-1c1-1 1-4 1-6h2c2 0 3 0 5 1l-1 1 1 1c-1 1-2 1-2 3v1c1 3 1 4 2 6l1 1v1h2 0l1 2c0 1 0 2-1 3-1 0-7-1-7 1v2h0-1s-1 0-1-1l-3-1v-1c0-1-1-1-2-1v-2-1s1-1 2-1z" class="F"></path><path d="M670 719l-3-3c0-3 0-4 1-6v-1c-1-1-1-3 0-4s1 0 2 0l1 1c-1 1-2 1-2 3v1c1 3 1 4 2 6-1 1-1 1-1 3z" class="b"></path><g class="B"><path d="M671 716l1 1v1h2 0l1 2c0 1 0 2-1 3-1 0-7-1-7 1v2h0-1s-1 0-1-1l-3-1v-1c0-1-1-1-2-1v-2c1 0 2 1 3 1 2-2 4 0 7 0h2v1c1-1 1-1 1-2v-1c-2 1-1 1-2 1l-1-1c0-2 0-2 1-3z"></path><path d="M652 704h0c0-1 2-3 2-3h2s1-1 2-1l-2 2 1 1c1 0 2-1 3-1v-1h8l1 1c2-1 3-2 4-1l1 1-1 2h-2 0c-2-1-3-1-5-1h-2c0 2 0 5-1 6v1c1 3 0 6-1 8-1 0-2 1-2 1v1h-1l-2-2c-1-1-2 0-3 0l-3-1 1-1v-2l-1-1 2-2-1-3c1 0 1-1 2 0h0l1-1-3-3z"></path></g><path d="M668 701l1 1c2-1 3-2 4-1l1 1-1 2h-2 0c-2-1-3-1-5-1h-2c0 2 0 5-1 6v1c1 3 0 6-1 8-2-4 0-7-1-11 0-1 1 0 1-1v-2c2-2 3-2 6-3z" class="C"></path><path d="M652 704h0c0-1 2-3 2-3h2s1-1 2-1l-2 2 1 1c1 2 2 2 2 4v1 1l-1 1 1 1c0 2 0 4-1 6-2 0-2-1-4 0v1l-3-1 1-1v-2l-1-1 2-2-1-3c1 0 1-1 2 0h0l1-1-3-3z" class="G"></path><path d="M655 707l1 1c0 3-2 6-4 8v-2l-1-1 2-2-1-3c1 0 1-1 2 0h0l1-1z" class="i"></path><path d="M658 700l1-1c1 0 3 0 5-1h0c2 1 4 1 6 1h13l5 1h-1c-2 1-5 1-6 3v1c0 2 0 2 1 3s1 3 1 5h-1-1v1c-3 0-5 1-8 0l-1 1 1 1 1 1 1-1c-1 1-1 1-1 2h0v1h-2v-1l-1-1c-1-2-1-3-2-6v-1c0-2 1-2 2-3l-1-1 1-1h0 2l1-2-1-1c-1-1-2 0-4 1l-1-1h-8v1c-1 0-2 1-3 1l-1-1 2-2z" class="K"></path><path d="M688 700v1 2h1l1 12c0 1-1 2 0 4s0 3 0 4l1 1h1c1 1 3 2 4 3l1 1s1 0 1 1l-1 2c1 1 1 2 2 2l-2 1c-2 0-2 0-3-1 0-1 1-2 1-3l-3-1c0-1 0-3-1-4l-1 1-2-2h0c-1 0-2 0-4-1-1 0-3 0-4-1h-1c-1-1 0-3-1-5v-1l3-3v-1h1 1c0-2 0-4-1-5s-1-1-1-3v-1c1-2 4-2 6-3h1z" class="E"></path><path d="M681 703c1-2 4-2 6-3v3 1c0 1 0 5-1 6h-1v3 1 1c-1-1-1-1-1-2s0 0-1-1c0-2 0-4-1-5s-1-1-1-3v-1z" class="H"></path><path d="M681 704v-1l3 1 1 1v1c-2 0-3 0-4-2z" class="s"></path><path d="M688 700v1 2h1l1 12c0 1-1 2 0 4s0 3 0 4l1 1h1c1 1 3 2 4 3l1 1s1 0 1 1l-1 2c1 1 1 2 2 2l-2 1c-2 0-2 0-3-1 0-1 1-2 1-3l-3-1c0-1 0-3-1-4l-1 1-2-2h0l-1-21v-3h1z" class="n"></path><path d="M670 769v-1h-2l-1 1c-1-1-2-1-3-2 2 0 2 0 3-2h2 2l1 1c2 1 5-1 7-1h4 2c1 0 3 1 3 2v3h0-2l1 1v3l1 4v2l-1 12v11 11 4 2c2 0 3 1 5 2 0 1 1 1 1 3 0 0-1 1-1 2l-4-1c-1 0-3 0-5-1h-3l-6-1h-6-5l1 2c-1 1-2 1-4 1h0c-4-1-8 1-11 0h0-4c-1 1-1 1-3 1l-2 2-2 3-1-2c-2 0-2 0-3 2-1-3-2-4-4-5-1 1-1 1-2 1l-2-1-1-1v-2l-1 1v2h-1l-1-1 2-5c3-5 2-17 0-22 0-2-2-5-2-6l1-2c1-3 3-6 5-8h1v-3c-1-2-1-4 0-5 0-3 3-6 6-8h2 4l4-2c0 1 0 1-1 2l1 1h0c-1 1-2 2-3 2h1 2c1 2 1 3 0 4 0 1 0 1-1 2h1l1-1c1-2 1-3 1-4 2 1 4 2 5 3v-1c0-1 1-2 1-3l2 1c0-1 0-2 1-3 2 0 3 0 5 1l1 1c1 0 0 0 1-1h0l2-1h4 1z" class="M"></path><path d="M644 768l1 1h0c-1 1-2 2-3 2l-1 1h0v1c1 1 2 1 3 2 0 1 0 2-1 3v-1h-4l1-7c1-2 2-2 4-2z" class="L"></path><path d="M630 822c1-1 1-2 0-3h0l1-1 1 1 1-2 1-1h3c1 1 1 2 1 3h1l1 1-5 2c-1 1-2 1-3 3 0-1 0-1-1-2l-1 1v-1-1z" class="X"></path><path d="M625 827l1-2 1 1c1-1 0-1 1-2-1-1-1-2-1-3h0l1-1h1c0 1 1 1 1 2v1 1l1-1c1 1 1 1 1 2l1 1c3 1 7 1 10 0 0 1 0 1-1 2l-2 2-2 3-1-2c-2 0-2 0-3 2-1-3-2-4-4-5-1 1-1 1-2 1l-2-1-1-1z" class="f"></path><path d="M639 777h4v1h0l-3 3 1 2 1 1 1-1v3h-1c0 1 0 0-1 1l1 1 1 1-2 2c0 1 0 2 1 3 0 1 1 1 1 2h-2v2l2 1v1h-1l-1 2v1h1l2 2-1 1h0c-1 1-1 1-2 1v4c1 0 2 0 3 1h-1c-1 1-2 1-3 0l-1-7v-28z" class="Q"></path><path d="M636 791h1v7s-1 1-1 2l1 2-2 2c0 1 0 0 1 1v1 1 3h1l-1 2h-1c-1-1-2-1-2-2l-1-1v-2c1-1 2-2 2-3v-1c-1 1-1 0-2 1v1h-1l-3-6 8-8z" class="I"></path><path d="M639 814v-9l1 7c1 1 2 1 3 0h1c-1-1-2-1-3-1v-4c3 0 6-1 9 0h1c0 1 1 2 1 2 1 1 1 2 0 3l3 1v1l-1 2h-1l-7 1c-3 1-5 2-7 2h-1l1-5z" class="Y"></path><path d="M653 816c-1-1-1-1-1-2l-1-1-1-1h1 1l3 1v1l-1 2h-1z" class="X"></path><path d="M639 814h0c1 0 2 0 3-1 2 1 3 3 4 4-3 1-5 2-7 2h-1l1-5z" class="L"></path><path d="M653 794h2v3c0 2 0 3 1 5v1h-1v6 4l-3-1c1-1 1-2 0-3 0 0-1-1-1-2h-1c-3-1-6 0-9 0 1 0 1 0 2-1h0l1-1-2-2h-1v-1l1-2h1v-1l-2-1v-2h2 6l4-2z" class="M"></path><path d="M642 803h0c1-2 1-1 3-1l1-1 1 1v1c2 0 4 0 6 1v2l-3 1c-3-1-6 0-9 0 1 0 1 0 2-1h0l1-1-2-2z" class="R"></path><path d="M643 796h6c2 1 3 1 5 3-1 1 0 1-2 1 0 1-1 1-2 1h-1-1l-1 1-1-1-1 1c-2 0-2-1-3 1h0-1v-1l1-2h1v-1l-2-1v-2h2z" class="T"></path><path d="M643 799c1-1 2-1 4-2 1 0 2 1 3 2l-2 2-1 1-1-1-1 1c-2 0-2-1-3 1h0-1v-1l1-2h1v-1z" class="G"></path><path d="M635 768h2c1 5 0 9 0 14l-1 2c1 1 1 1 1 3h-2v1c0 1-2 4-3 4l-1 1h-3l1 1h1 0c-1 2-2 3-4 4-1-1-1-1-2-3 1-4 3-7 5-11v-3c-1-2-1-4 0-5 0-3 3-6 6-8z" class="b"></path><path d="M629 781c-1-2-1-4 0-5 0-3 3-6 6-8 1 3 0 4 0 6-1 2-2 3-4 4l2 2h0-1c-1 0-2 0-3 1z" class="J"></path><path d="M643 778c1-1 1-2 1-3-1-1-2-1-3-2v-1h0l1-1h1 2c1 2 1 3 0 4 0 1 0 1-1 2h1l1-1c1-2 1-3 1-4 2 1 4 2 5 3v1l-2 2c1 1 1 1 2 1l1 1-1 2c0 1 1 1 1 2-1 2-2 2-2 4l2 1c0 1 0 2 2 3v-1h0c0 1 0 3 1 4 0 1 0 2-1 2v-3h-2l-4 2h-6c0-1-1-1-1-2-1-1-1-2-1-3l2-2-1-1-1-1c1-1 1 0 1-1h1v-3l-1 1-1-1-1-2 3-3h0z" class="N"></path><path d="M643 779c2-1 3-1 5-1l1 1v1h-3l-1 1h-1 0c-1-1-1-1-1-2z" class="L"></path><path d="M641 787c1-1 1 0 1-1h1v-3l-1 1-1-1-1-2 3-3v1c0 1 0 1 1 2h0 1l1-1h3v5h-2c0-1 0-2-2-2h0c0 2 0 2-1 3-1 0-2 1-3 1z" class="G"></path><path d="M641 787c1 0 2-1 3-1 1-1 1-1 1-3h0c2 0 2 1 2 2v1h3c1-1 2-1 3-2-1 2-2 2-2 4l2 1c0 1 0 2 2 3v-1h0c0 1 0 3 1 4 0 1 0 2-1 2v-3h-2l-4 2h-6c0-1-1-1-1-2-1-1-1-2-1-3l2-2-1-1-1-1z" class="r"></path><path d="M647 786h3c1-1 2-1 3-2-1 2-2 2-2 4l2 1v2l-1-2c-1-1-4-1-5-1l-1-1 1-1z" class="p"></path><path d="M653 789c0 1 0 2 2 3v-1h0c0 1 0 3 1 4 0 1 0 2-1 2v-3h-2v-1h-1c-1 0-1 1-2 1l-2-1c1-1 3-2 5-2v-2z" class="g"></path><path d="M642 788c2 0 2 0 4 2v1c-1 1-3 1-4 1h0c1 1 1 1 2 1h4l2 1c1 0 1-1 2-1h1v1l-4 2h-6c0-1-1-1-1-2-1-1-1-2-1-3l2-2-1-1z" class="a"></path><path d="M662 817h3l1-2h0l1 2h3 2l15 1v2c2 0 3 1 5 2 0 1 1 1 1 3 0 0-1 1-1 2l-4-1c-1 0-3 0-5-1h-3l-6-1h-6-5l1 2c-1 1-2 1-4 1h0c-4-1-8 1-11 0h0-4c-1 1-1 1-3 1 1-1 1-1 1-2-3 1-7 1-10 0l-1-1c1-2 2-2 3-3l5-2-1-1c2 0 4-1 7-2l7-1h1c1 0 1 1 2 1h4 2z" class="B"></path><path d="M654 816c1 0 1 1 2 1 1 1 1 1 1 2l-17 2v-1l-1-1c2 0 4-1 7-2l7-1h1z" class="M"></path><path d="M662 817h3l1-2h0l1 2h3 2l15 1v2c-9-2-21-2-30-1 0-1 0-1-1-2h4 2z" class="l"></path><defs><linearGradient id="M" x1="686.142" y1="827.084" x2="669.858" y2="819.916" xlink:href="#B"><stop offset="0" stop-color="#5f5f5d"></stop><stop offset="1" stop-color="#7b7774"></stop></linearGradient></defs><path fill="url(#M)" d="M660 822c6-1 13 0 19 0l8 1h3s1 0 2-1c0 1 1 1 1 3 0 0-1 1-1 2l-4-1c-1 0-3 0-5-1h-3l-6-1h-6 0-6c1-1 5-1 6-2h-8z"></path><path d="M640 820v1h0c2 2 3 2 5 2 2-1 4-1 6-1h9 8c-1 1-5 1-6 2h6 0-5l1 2c-1 1-2 1-4 1h0c-4-1-8 1-11 0h0-4c-1 1-1 1-3 1 1-1 1-1 1-2-3 1-7 1-10 0l-1-1c1-2 2-2 3-3l5-2z" class="Y"></path><path d="M633 826c1 0 1-1 2-2h2 4c3 2 5 1 8 0h14l1 2c-1 1-2 1-4 1h0c-4-1-8 1-11 0h0-4c-1 1-1 1-3 1 1-1 1-1 1-2-3 1-7 1-10 0z" class="M"></path><path d="M663 770h0l2-1h4 1v6 1c1 3 0 7 1 10v1 3c-1 1-1 2-2 2s-1 1-1 1c1 2 2 3 2 4v1l1 1-1 2c0 1 1 1 1 3l-1 1 1 2-1 3c0 1 1 1 1 2l-1 5h-3l-1-2h0l-1 2h-3-2-4c-1 0-1-1-2-1l1-2v-1-4-6h1v-1c-1-2-1-3-1-5 1 0 1-1 1-2-1-1-1-3-1-4h0v1c-2-1-2-2-2-3l-2-1c0-2 1-2 2-4 0-1-1-1-1-2l1-2-1-1c-1 0-1 0-2-1l2-2v-1-1c0-1 1-2 1-3l2 1c0-1 0-2 1-3 2 0 3 0 5 1l1 1c1 0 0 0 1-1z" class="L"></path><path d="M655 809v2h1l2 1c1 0 1 1 2 0v5h-4c-1 0-1-1-2-1l1-2v-1-4z" class="N"></path><path d="M653 771l2 1v5c0 1 0 2 2 3-2 2-1 8-2 11h0v1c-2-1-2-2-2-3l-2-1c0-2 1-2 2-4 0-1-1-1-1-2l1-2-1-1c-1 0-1 0-2-1l2-2v-1-1c0-1 1-2 1-3z" class="S"></path><path d="M661 770l1 1c1 0 0 0 1-1v8c0 1-1 2-1 3 1 6 0 12 0 18v7c1 1 0 2 0 3v8h-2v-5-2h0v-1c1-2 0-2-1-3l2-1v-2-6l-1 1-2-2h1l1 1 1-1v-18l-2-2-1-1h2v1l1-1c1-2 0-3 0-5z" class="U"></path><path d="M663 770h0l2-1h4 1v6 1c1 3 0 7 1 10v1 3c-1 1-1 2-2 2s-1 1-1 1c1 2 2 3 2 4v1l1 1-1 2c0 1 1 1 1 3l-1 1 1 2-1 3c0 1 1 1 1 2l-1 5h-3l-1-2h0l-1 2h-3v-8c0-1 1-2 0-3v-7c0-6 1-12 0-18 0-1 1-2 1-3v-8z" class="R"></path><path d="M671 812h-4c0-2-1-5-1-7l3-3v2l1 1 1 2-1 3c0 1 1 1 1 2z" class="K"></path><path d="M669 804l1 1 1 2-1 3h-1l-1-2 1-4zm-6-34h0l2-1h4 1v6 1c1 3 0 7 1 10v1 3c-1 1-1 2-2 2s-1 1-1 1c1 2 2 3 2 4v1c-1 1-1 1-2 1h-1v-2-2h0c-1 0-2 0-3-1 0-1-1-1-1-2v-5h1 1v-3c0-2 0-2-1-3h-2c0-1 1-2 1-3v-8z" class="G"></path><path d="M670 776c-1 0-2 1-3 1l-1-1c0-1 1-2 1-3h1l2 2v1z" class="B"></path><path d="M663 770h0l2-1h4l-2 2h-2v7h-2v-8z" class="O"></path><path d="M668 793h-1c-1-1-1-4-2-5 1-1 1-1 1-2l1-1h1c0 1 1 1 2 1l1 1v3c-1 1-1 2-2 2s-1 1-1 1z" class="R"></path><path d="M670 769v-1h-2l-1 1c-1-1-2-1-3-2 2 0 2 0 3-2h2 2l1 1c2 1 5-1 7-1h4 2c1 0 3 1 3 2v3h0-2l1 1v3l1 4v2l-1 12v11 11 4l-15-1h-2l1-5c0-1-1-1-1-2l1-3-1-2 1-1c0-2-1-2-1-3l1-2-1-1v-1c0-1-1-2-2-4 0 0 0-1 1-1s1-1 2-2v-3-1c-1-3 0-7-1-10v-1-6z" class="D"></path><path d="M670 776l2 1v3c-1 1 0 2 0 3v1c1 0 0 0 1-1 2 0 2 0 4 1h2v1l-1 2c1 1 0 1 1 2l1 2c-2 1-4 0-6 0h-1c1-2 1-3 1-4s-1-1-2-2l-1 1c-1-3 0-7-1-10z" class="g"></path><path d="M684 780h4l-1 12v5c-1 0-2 1-3 1 0-1 0-2-1-3 1-1 1-2 1-4 0 0-1-1-2-1h0v-1h2v-1l-1-2 1-1v-1c0-2-1-2 0-4z" class="C"></path><path d="M684 785c2 1 2 1 3 2-1 1-2 1-3 1l-1-2 1-1z" class="F"></path><path d="M676 776h1 0c2 0 2 0 4-1v-1h-3v-1l1-1h1c0-1 0-1 1-2 1 0 1 1 2 2 0 0 0 1 1 1v1h3l1 4v2h-4c-1 2 0 2 0 4v1l-1 1c-1-3-1-5-2-8-1 1-1 1-2 1s-1 1-3 2c-1-2 0-4 0-5z" class="B"></path><path d="M687 774l1 4v2h-4v-2l-1-1c0-2 0-2 1-3h3z" class="E"></path><path d="M684 778h4v2h-4v-2z" class="I"></path><path d="M670 769v-1h-2l-1 1c-1-1-2-1-3-2 2 0 2 0 3-2h2 2l1 1c2 1 5-1 7-1h4 2c1 0 3 1 3 2v3h0-2l1 1v3h-3v-1c-1 0-1-1-1-1-1-1-1-2-2-2-1 1-1 1-1 2h-1l-1 1v1h3v1c-2 1-2 1-4 1h0-1-2l-1 1h1l1 2v1c-1 1-2 1-2 3h0c-1 1 0 1-1 1v-1c0-1-1-2 0-3v-3l-2-1v-1-6z" class="h"></path><path d="M671 786l1-1c0 2 0 3 1 5l-1 1c0 1 0 1 1 2 3 0 5 0 7 1h1l1-1s0 1 1 2l-1 1v2c1 3 0 7 0 10h0l1 1 1-1v-4s0-1-1-1v-2l2-1 1 1 1 2v11 4l-15-1h-2l1-5c0-1-1-1-1-2l1-3-1-2 1-1c0-2-1-2-1-3l1-2-1-1v-1c0-1-1-2-2-4 0 0 0-1 1-1s1-1 2-2v-3-1z" class="B"></path><path d="M680 810h2c1 2 1 2 2 3l1-1 1 1v1h-2l-1-1-2 2h-1c0-2 0-2 1-3-1-1-1-1-1-2z" class="b"></path><path d="M671 786l1-1c0 2 0 3 1 5l-1 1c0 1 0 1 1 2 3 0 5 0 7 1v2l-5 1c-1-1-2-1-2-2-1 1-1 4-1 5s1 1 1 2c1 0 1 0 2-1l-1-1v-1c1-1 1-1 3 0h0l-1 2c1 1 1 0 3 0h0l-1-2h1 1c0 2 0 3-1 5-2 1-4 0-6-1l-1 1v1c1 1 2 1 3 2-2 1-3 0-3 1v1 4 4h-2l1-5c0-1-1-1-1-2l1-3-1-2 1-1c0-2-1-2-1-3l1-2-1-1v-1c0-1-1-2-2-4 0 0 0-1 1-1s1-1 2-2v-3-1z" class="p"></path><path d="M671 790c0 3 0 6-1 8v-1c0-1-1-2-2-4 0 0 0-1 1-1s1-1 2-2z" class="q"></path><path d="M759 649l1 1c3 0 6 0 8 2 1 1 1 2 1 3s-1 2-2 3c0 2-1 4-2 5l-2 1c-1 1-2 1-3 1l-3 3c1 2 1 2 1 4h1 0c1-1 1-2 2-3l1 1c0 2 1 2 1 4h0c2-1 3-1 4-2 1 0 1 0 2 2v1c1 0 1-1 2-1h1 2l1-1 1-1v2l2-1v1l3-1-1 3v1l1-1 1 2c-1 1-1 1-1 2l2 2c-1 1-1 1-3 1v2c0 1-1 2-2 3 0 1 1 2 0 3-3 0-3 1-5 3 0 1-1 1-1 2 0 2-3 4-4 4-1 1-2 1-2 1-2 1-7 6-8 5-1 2-1 4-1 6l1 1-1 2c1 0 2 0 2 2v-1l1 1c0 1 0 2 1 2v2h-1s-1 1-1 2l1 1-1 4-3 6c-1 1-1 2-1 3-1 1-2 1-3 2l-2 1-1-1c-1 0-2 0-2 1-1 1-2 2-3 4 0 2 1 4 0 6v5c0 1-1 1-1 2v7h2l2-2 2-1h1c1 0 2 0 3-1s2-3 4-4h2c1-2 1-2 3-2 0 1 0 2-1 2-1 1-2 2-3 2l-2 2c-1 1-2 1-3 1h-1c-1 2-3 3-5 3h0l-2 2 3-1s1 1 2 1c0 0 1-1 2-1h0l2 1 1 1 1 1h-1c-1 1-1 2-1 4l-1 1v1l-1 2c-1 1-1 3-2 4l-1 1 1 1c-1 0-2 0-3 1 0 1-1 2-2 2l-3 2c-3 0-5 2-7 3s-4 4-6 6v4c-1 1-2 3-3 3-1 1-2 1-2 1-1 1 1 1-2 2h0c-1 1-1 2-2 3s-3 2-4 3h-2v1l-2 2 1 3h0l-1 1h2v1l-1 1c-2-1-2-1-4-1-1 1-1 2-1 4-2 0-2 0-3-2-1 0-2-1-3-2 0 2 1 3-1 5l-3 1h0-1l-2 1c-1 0-1 0-1 2-1-2-1-2-2-2 0-1 1-2 1-2 0-2-1-2-1-3-2-1-3-2-5-2v-2-4-11-11l1-12v-2l-1-4v-3l-1-1h2 0v-3c0-1-2-2-3-2h0c1-2 2-3 4-3h1v-1l3 3v-1l2-3c-1-2-1-3-1-5l1-2 1-3c1-2 1-3 2-4l1-2c1-1 1-2 1-3l-1 1-1-1 2-2h1c0-1 1-3 1-4-1-1-2-1-3-2-1 0-1-1-2-2l1-2c0-1-1-1-1-1l-1-1c-1-1-3-2-4-3h-1l-1-1c0-1 1-2 0-4s0-3 0-4l-1-12 3-3c2-2 4-3 6-4v-2h1c1 0 1 0 1-1l2-4v-1l2-2h0c0-1 1-3 2-4h0c0-1 1-2 1-3h0c1-2 1-2 1-3l1-1c0-2 2-3 4-4l1-1 1-1h1c1-1 1-1 2-1h1c1-1 2-1 3-1h1c1-1 2-1 2-2h1c0-1 1-2 1-2l1-2h0l2-2c1-1 3-2 4-3h1c1-1 2-1 3-1 2 0 2 0 4-1h1v2c1-1 3-1 5-1-1 3-2 3-4 4v2c1-1 2-2 3-2 1-1 2-1 3-2s2-1 2-1c1-1 1-1 1-2 1-1 4-4 6-4v-1z" class="C"></path><path d="M724 709c1 1 2 1 3 2l-2 1 1 1 1 1-1 1c-1 1 0 1-1 2l-2-1v-4l1-3z" class="F"></path><path d="M721 740l2-2c1-1 2-2 2-3 0-2 1-3 3-4h0c0 2-1 3-1 4s-1 2-1 3l-3 4-2-2z" class="Q"></path><path d="M743 711l-1 1h-1l1-1-1-2c1-2 2-1 4-2 1 0 1-1 2-1v-2l1-1c0 1 1 1 1 2v1l2 2v1h-1-2v3c-1 0-2 0-3-1l-2-1c-1 1-1 0 0 1z" class="B"></path><path d="M735 729c1-1 1-1 1-2 1-1 1 0 1-1-1 0-2 1-3 1h-1l3-3c2-1 1-2 1-3 1 0 3-2 3-2h1l3-1h1v3h0c-2 1-3 3-4 4-1 2-3 3-5 4h-1z" class="V"></path><path d="M744 718h1v3h0c-2 1-3 3-4 4l-2-1c1-3 2-3 4-5l1-1z" class="C"></path><path d="M724 723l1 3c2 0 2-1 3-2v-1l1-1h2 0v-3c1-1 3-1 4-1l3-3 1-1v1c-2 2-4 3-5 6-1 0-1 0-2 1 0 2-7 7-9 9 0 0 1 1 1 2l-2 3v1c0 1-1 2-1 3l2 2c0 1-1 2-2 3-1 2-2 4-3 5l-2-2-1-3-1-2c-1 0-2 1-3 1 0-2 0-2-1-3 0 0 1-1 1-2l-1-1c1-1 1-2 1-2l-1-3v-2l1-1h0c1-2 1-2 0-4h3 1l2-1h0l3-3h0c0 1-1 2-2 3 1 1 1 1 2 1v-1c1-1 1-2 2-2 1-1 0-2 1-4 1 0 1 0 2 1l-1 3z" class="p"></path><path d="M717 725s0 2-1 2c-1 2-2 2-2 4 0 0 1 0 1 1v1c-1 2-1 2-2 2v-2c-1-1-1-1-2-3 1-2 1-2 0-4h3 1l2-1z" class="R"></path><path d="M717 725h0l3-3h0c0 1-1 2-2 3 1 1 1 1 2 1v-1c1-1 1-2 2-2 1-1 0-2 1-4 1 0 1 0 2 1l-1 3c-1 2-1 3-2 5h0l-3 3s0 1 1 2h-3s-1-1-1-2l-1 1c0-1-1-1-1-1 0-2 1-2 2-4 1 0 1-2 1-2zm-4 16v-2c2-1 4-2 6-4 1-2 3-3 4-4 0 0 1 1 1 2l-2 3v1c0 1-1 2-1 3l2 2c0 1-1 2-2 3-1 2-2 4-3 5l-2-2-1-3-1-2-1-2z" class="D"></path><path d="M713 741l4 1 1-1c1 0 1 1 1 2v1l-2-1c-1 1-2 1-2 2l-1-2-1-2z" class="P"></path><path d="M772 674h2l1-1 1-1v2l2-1v1l3-1-1 3v1l1-1 1 2c-1 1-1 1-1 2l2 2c-1 1-1 1-3 1v2c0 1-1 2-2 3 0 1 1 2 0 3-3 0-3 1-5 3 0 1-1 1-1 2 0 2-3 4-4 4-1 1-2 1-2 1-2 1-7 6-8 5h-2v2l-1 1v2h-1l-1-1c-1 2 0 3-1 4-2 0-2 0-2-1h-1l-1 1s-1-1-2-1h0c-1-1-2-1-3-2s-1 0 0-1l2 1c1 1 2 1 3 1v-3h2 1v-1l-2-2v-1h2c2-2 3-4 4-6v-1-2h0v-1l2-2c0-3 2-4 3-6 1-1 2-2 2-3 3-1 5-4 7-7l-1-1 1 1 3-3z" class="B"></path><path d="M774 678h2l1 3c-1 0-1 1-2 1-1 1-2 2-2 3l-2 2h-6 0l-1-2c1-1 3-1 4-2 2 0 5-4 6-5z" class="J"></path><path d="M772 674h2l1-1 1-1v2l2-1v1 1h-2l1 2c-1 0-2 0-3 1s-4 5-6 5c-1 1-3 1-4 2l-3 2h-1c1-1 2-2 2-3 3-1 5-4 7-7l-1-1 1 1 3-3z" class="O"></path><path d="M761 687l3-2 1 2h0 6l-2 2c-1 1-1 2-2 2-1 1-2 3-3 4l-5 3c-1 0-1 0-2-1 1-1 1-2 1-3h0l1-4 2-3z" class="I"></path><path d="M759 690h2s1-1 2-1 2 0 3 1l-4 3h-1c-1 0-2 0-3 1l1-4z" class="J"></path><path d="M761 687l3-2 1 2h0 6l-2 2c-1 0-3 1-3 1-1-1-2-1-3-1s-2 1-2 1h-2l2-3z" class="H"></path><path d="M765 695c2-1 3-3 5-3l2-2c2-1 1 0 3 0v-1l1-1h1l1-3v-2c1-2 0-3 1-5l1 1 1 1 2 2c-1 1-1 1-3 1v2c0 1-1 2-2 3 0 1 1 2 0 3-3 0-3 1-5 3 0 1-1 1-1 2 0 2-3 4-4 4-1 1-2 1-2 1-2 1-7 6-8 5h-2v2l-1 1v2h-1l-1-1c-1 2 0 3-1 4-2 0-2 0-2-1h-1l-1 1s-1-1-2-1h0c-1-1-2-1-3-2s-1 0 0-1l2 1c1 1 2 1 3 1v-3h2 1v-1l-2-2v-1h2c2-2 3-4 4-6v-1-2h0v-1l2-2c0-3 2-4 3-6h1l-2 3-1 4h0c0 1 0 2-1 3 1 1 1 1 2 1l5-3h1z" class="O"></path><path d="M764 695h1c0 1 0 3-1 3l-2 1-2 2h-1v-1l1-1-1-1 5-3z" class="P"></path><path d="M751 705c2-2 3-4 4-6v-1-2h0v-1l2-2c0-3 2-4 3-6h1l-2 3-1 4h0c0 1 0 2-1 3 1 1 1 1 2 1l1 1-1 1v1h1v2c-1 1-2 2-3 2l1-1h0l-1-2c-1 1-1 1-1 2-1 0-1 1-1 2l-1 1c-2-1-2-1-3-2z" class="K"></path><path d="M748 714l1-1h1c0 1 0 1 2 1 1-1 0-2 1-4l1 1h1v-2l1-1v-2h2c-1 2-1 4-1 6l1 1-1 2c1 0 2 0 2 2v-1l1 1c0 1 0 2 1 2v2h-1s-1 1-1 2l1 1-1 4-3 6c-1 1-1 2-1 3-1 1-2 1-3 2l-2 1-1-1c-1 0-2 0-2 1-1 1-2 2-3 4 0 2 1 4 0 6v-1c-1 1-1 2-1 2-2 1-2 0-4 2 0 1 0 1-1 2-1 0-1 1-2 1-1 1-2 1-3 1h-1 0c-2 1-3 2-4 3l-2 2v1 2l-2 3c0 1 0 2-1 2v3c-1 0-1 0-1 1-1 1-1 0-2 1v-1-1l-2-2v-2l-1-4v-3c-2-1-2-2-3-4v-3l2-5v-2l2 2c1-1 2-3 3-5 1-1 2-2 2-3l3-4c0-1 1-2 1-3l1-1c1-1 2-2 3-2 1-1 1-1 2-1 1-1 1-2 2-2h1c2-1 4-2 5-4 1-1 2-3 4-4h0v-3h-1v-1-1-1h1v1h0c2 0 2 0 3-2z" class="o"></path><path d="M726 748c1 0 2 0 3 1 1 0 2-1 3-1h1 0l-2 2c-2 1-4 3-5 4v1h1l1-1c0 1 1 1 2 1l2-1h3c-1 1-3 2-3 3h0c-2-1-2 0-4 0l-1-1h-2-1v-2c1-1 1-1 0-2l-1-1 1-1c1 0 1-1 2-2zm31-33c1 0 2 0 2 2l-2 1c-1 1-2 2-2 4-1 2-5 6-6 8v-2h-1l1-1c1-2 3-4 4-5v-1c-2 1-5 4-6 6v2h-2c1-1 1-3 1-4h0c4-4 7-7 11-10z" class="F"></path><path d="M726 739c1-2 2-3 3-4l2 1h1l1 1h0c-3 3-6 7-7 11-1 1-1 2-2 2l-1-2h0v-1c-1-3 2-6 3-8z" class="B"></path><path d="M759 717v-1l1 1c0 1 0 2 1 2v2h-1s-1 1-1 2l1 1-1 4-3 6c-1 1-1 2-1 3l-1-1v-3c-1-2-1-2 0-4-1 1-2 1-3 1l-1 1 1 2h-1c-1 0-2 1-3 3l-1 1-3 3h0c1-2 3-3 3-6 1-1 2-3 3-4 1-2 5-6 6-8 0-2 1-3 2-4l2-1z" class="Q"></path><path d="M759 717v-1l1 1c0 1 0 2 1 2v2h-1s-1 1-1 2l1 1-1 4-3 6v-2c0-1-1-1-1-1l1-3-1-1v-1l1-1-1-1c1-2 2-2 3-3 0-1 0-2-1-3l2-1z" class="K"></path><path d="M743 740h0l3-3 1-1c1-2 2-3 3-3h1l-1-2 1-1c1 0 2 0 3-1-1 2-1 2 0 4v3l1 1c-1 1-2 1-3 2l-2 1-1-1c-1 0-2 0-2 1-1 1-2 2-3 4 0 2 1 4 0 6v-1c-1 1-1 2-1 2-2 1-2 0-4 2 0 1 0 1-1 2-1 0-1 1-2 1-1 1-2 1-3 1h-1c0-1 2-2 3-3 2-1 3-3 5-5 1-1 1-2 1-3l3-6c-2 2-4 4-6 4v-1c1 0 2-1 3-1l2-2z" class="C"></path><path d="M748 714l1-1h1c0 1 0 1 2 1 1-1 0-2 1-4l1 1h1v-2l1-1v-2h2c-1 2-1 4-1 6l1 1-1 2c-4 3-7 6-11 10h0c0 1 0 3-1 4l-2 2c-1 1-2 2-4 3-1 0-2 1-4 1l-2 2h0 0l-1-1h-1l-2-1c-1 1-2 2-3 4v-1c0-1 1-2 1-3l1-1c1-1 2-2 3-2 1-1 1-1 2-1 1-1 1-2 2-2h1c2-1 4-2 5-4 1-1 2-3 4-4h0v-3h-1v-1-1-1h1v1h0c2 0 2 0 3-2z" class="D"></path><path d="M748 714l1-1h1c0 1 0 1 2 1 1-1 0-2 1-4l1 1h1v-2l1-1v-2h2c-1 2-1 4-1 6l1 1-1 2c-4 3-7 6-11 10h0c-1 1-2 2-4 2v-1l1-1h1c1-2 1-2 3-2 1-2 1-2 1-3h-1c-1-1 1-1 1-3h-2l-1 1h-1v-1-1-1h1v1h0c2 0 2 0 3-2z" class="R"></path><path d="M726 738v1c-1 2-4 5-3 8v1h0l1 2-1 1 1 1c1 1 1 1 0 2v2h1 2l1 1c2 0 2-1 4 0-2 1-3 2-4 3l-2 2v1 2l-2 3c0 1 0 2-1 2v3c-1 0-1 0-1 1-1 1-1 0-2 1v-1-1l-2-2v-2l-1-4v-3c-2-1-2-2-3-4v-3l2-5v-2l2 2c1-1 2-3 3-5 1-1 2-2 2-3l3-4z" class="C"></path><path d="M721 759l2-4v1c1 0 1 0 0 1v2c1 0 1-1 2-1l2 2h1l-2 2v1 2h-4l-1 1v-1c1-2 2-3 3-4v-1h-1c-1 0-1-1-2-1z" class="Q"></path><path d="M726 738v1c-1 2-4 5-3 8v1h0l-2 3h0l1 1c-1 2-4 4-5 5h-2l-1 1v-3l2-5v-2l2 2c1-1 2-3 3-5 1-1 2-2 2-3l3-4z" class="R"></path><path d="M721 759c1 0 1 1 2 1h1v1c-1 1-2 2-3 4v1l1-1h4l-2 3c0 1 0 2-1 2v3c-1 0-1 0-1 1-1 1-1 0-2 1v-1-1l-2-2v-2l-1-4v-3h2c1-1 1-2 1-2l1-1z" class="j"></path><path d="M759 649l1 1c3 0 6 0 8 2 1 1 1 2 1 3s-1 2-2 3c0 2-1 4-2 5l-2 1c-1 1-2 1-3 1l-3 3c1 2 1 2 1 4h1 0c1 0 2 1 2 2s-1 2 0 3c1-1 1-1 1-2h1l2 2c0 2-2 4-3 5l-2 1c-1 3-3 5-5 7l-1 1v1 2h-1c-1 1-2 1-3 2h-1c-1 1-1 2-2 2-1 1-1 0-2 1 0 2 0 2-1 3s-1 2-2 2c0 1-1 2-2 2v1l-2 3v2h0-2s0 1-1 2l-1 1h-1c-1 1 0 1-1 1s-1 0-2 1l-1 1c-1-1-1-1-1-2-1 0-1 0-2-1l1-1-1-1-1-1 2-1c-1-1-2-1-3-2l1-2v-1c0-1 0-1-1-2l-1-1 1-1-1-2c0-2 1-4 3-6h2c0-2 1-3 1-5v-9-2-2l-1-1 1-1c0-1 3-3 4-4 1 0 0-1 2-2h1 0c1-1 1-2 2-3l1-1c1-1 2-1 2-2h2l1-1c1-1 2-2 3-2 1-1 2-1 3-2s2-1 2-1c1-1 1-1 1-2 1-1 4-4 6-4v-1z" class="H"></path><path d="M751 673c-1 2-3 4-5 6v-1c0-1 1-2 2-3s1-2 2-3c2-2 5-3 7-4 1 2 1 2 1 4h1c-1 2-2 3-3 5h-1v-1l-2 1v-1l-2-3z" class="I"></path><path d="M751 673l2-2 1 1c0 2 0 2-1 4l-2-3z" class="E"></path><path d="M727 711v-2c0-1 1-1 1-2v-3l1-1c1-2 3-5 5-5l1 1-3 6c0 1 0 1-1 2-1 3-1 6-3 9-1 0-1 0-2-1l1-1-1-1-1-1 2-1z" class="P"></path><path d="M735 699v-1l6-9c1-2 0-2 1-4l1-1 3 3c0-1 1-1 1-1v-1h1c0-1 1-2 2-3h0l3-1v-1l2-2 1 1h0l2 3-1 1c-2 1-2 2-3 4l-2 2c-1 0-2 1-3 2 0 1 0 1-1 2s-3 2-3 3v2c-1 1-2 1-2 2-1 2-2 3-3 5 0 1-1 2-1 2-1-1-1-1-1-2l-1-1h-1c0 1-1 1-2 1h-2l3-6zm24-50l1 1c3 0 6 0 8 2 1 1 1 2 1 3s-1 2-2 3c0 2-1 4-2 5l-2 1c-1 1-2 1-3 1-1 1-3 1-4 2h-1v-1c0-1 0-1-1-2l-2 2c0 1 1 2 1 2v1l-1 1-2-2h-1v2l1 1c-1 1-1 1-2 1-1 1-2 1-3 1-1 1-2 2-4 3l-2 1c-1 0-2 1-2 2-1 0-1 1-1 2l1 1h-1v1s0 1-1 1h-1l-1-2v1c0 1-1 2 0 4 0 2-1 4-2 7l-3 3-2 1v1h0c0 1-1 2-2 3l-1-2c0-2 1-4 3-6h2c0-2 1-3 1-5v-9-2-2l-1-1 1-1c0-1 3-3 4-4 1 0 0-1 2-2h1 0c1-1 1-2 2-3l1-1c1-1 2-1 2-2h2l1-1c1-1 2-2 3-2 1-1 2-1 3-2s2-1 2-1c1-1 1-1 1-2 1-1 4-4 6-4v-1z" class="J"></path><path d="M739 677v-1-2c2-1 3-3 6-4v3c-1 1-2 2-4 3l-2 1z" class="s"></path><path d="M763 654h1 1v1h-2c-2 1-5 3-6 6l-1 1-1-1v-1c1-1 1-2 2-3l2-2h1c1-1 2-1 3-1z" class="F"></path><path d="M759 649l1 1c-1 1-3 1-4 2-3 4-4 9-7 12-1 2-3 1-5 2-1 2-2 5-4 6-1 0-2 0-3 1-3 1-6 2-7 5l-1 2v-2c1-2 2-4 4-5s3-1 4-2c1 0 1-1 2-1h2c0-2 0-3 2-4 1-1 3-4 5-4 1 0 3-4 4-6h0c1-1 1-1 1-2 1-1 4-4 6-4v-1z" class="i"></path><path d="M744 661c1-1 2-2 3-2 1-1 2-1 3-2s2-1 2-1h0c-1 2-3 6-4 6-2 0-4 3-5 4-2 1-2 2-2 4h-2c-1 0-1 1-2 1-1 1-2 1-4 2s-3 3-4 5v-2l-1-1 1-1c0-1 3-3 4-4 1 0 0-1 2-2h1 0c1-1 1-2 2-3l1-1c1-1 2-1 2-2h2l1-1z" class="P"></path><path d="M738 655c2 0 2 0 4-1h1v2c1-1 3-1 5-1-1 3-2 3-4 4v2l-1 1h-2c0 1-1 1-2 2l-1 1c-1 1-1 2-2 3h0-1c-2 1-1 2-2 2-1 1-4 3-4 4l-1 1 1 1v2 2 9c0 2-1 3-1 5h-2c-2 2-3 4-3 6l1 2-1 1 1 1c1 1 1 1 1 2v1l-1 2-1 3c-1 1-2 3-3 4h-1c-1 1-4 5-5 7-1 1-2 2-3 2v1c1 2 1 2 0 4h0l-1 1-2 1-1 1c-1 0-2 1-3 2v1h2c0 1 0 2-1 2-2 2-2 4-2 7h-1c0-1-1-1-1-1h-2c1-1 1-2 1-3l-1 1-1-1 2-2h1c0-1 1-3 1-4-1-1-2-1-3-2-1 0-1-1-2-2l1-2c0-1-1-1-1-1l-1-1c-1-1-3-2-4-3h-1l-1-1c0-1 1-2 0-4s0-3 0-4l-1-12 3-3c2-2 4-3 6-4v-2h1c1 0 1 0 1-1l2-4v-1l2-2h0c0-1 1-3 2-4h0c0-1 1-2 1-3h0c1-2 1-2 1-3l1-1c0-2 2-3 4-4l1-1 1-1h1c1-1 1-1 2-1h1c1-1 2-1 3-1h1c1-1 2-1 2-2h1c0-1 1-2 1-2l1-2h0l2-2c1-1 3-2 4-3h1c1-1 2-1 3-1z" class="H"></path><path d="M719 685v-1-2h0l1 1c1 0 1 0 2 1 0 2-1 3-2 4h-1v-3z" class="J"></path><path d="M712 702c3-2 5-4 7-7l1-1v3l-2 1 1 1h0v3h0l-2-1c-1 0-2 1-3 2h-1l-1-1z" class="C"></path><path d="M743 656c1-1 3-1 5-1-1 3-2 3-4 4v2l-1 1h-2c0 1-1 1-2 2 1-2 2-3 3-4-1 0-1 0-2-1l3-3z" class="Q"></path><path d="M738 655c2 0 2 0 4-1h1v2l-3 3h0c-3 2-3 4-6 5-1 0-2 0-3-1h-1l-2-2 2-2c1-1 3-2 4-3h1c1-1 2-1 3-1z" class="J"></path><path d="M720 694c0-2 1-3 2-5 1-3 1-5 3-8v-2-1-2c1-1 2-3 3-4v-1-1l3-1c0-1 0-1 1-2s2 0 4 0c0-1 1-2 2-2-1 1-1 2-2 3h0-1c-2 1-1 2-2 2-1 1-4 3-4 4l-1 1 1 1v2 2 9c-2 0-2 0-3 1v2h0c-2 1-2 2-3 3l-2 2 1 1-1 1c-1-1-1 0-1-1-1 1-1 0-1 1h0l-1-1 2-1v-3z" class="B"></path><path d="M719 699c0-1 0 0 1-1 0 1 0 0 1 1l1-1-1-1 2-2c1-1 1-2 3-3h0v-2c1-1 1-1 3-1 0 2-1 3-1 5h-2c-2 2-3 4-3 6l1 2-1 1 1 1c1 1 1 1 1 2v1l-1 2-1 3c-1 1-2 3-3 4h-1c-1 1-4 5-5 7-1 1-2 2-3 2 0-2-1-5-2-7l-1-2-3-9v-1h0l5-1h0c1 1 3 3 4 3v-4-1c1-1 2-2 3-2l2 1h0v-3z" class="K"></path><path d="M719 704l2-2 2 1 1 1-2 2v3l-1 1c-1 1 1 1 0 2s-2 1-2 1c-1-1-2-2-3-2s0 0-1-1c1-2 3-4 4-6z" class="J"></path><path d="M719 704v1 3h-2c0 1 0 1 1 2 1 0 2-1 3-1h1l-1 1c-1 1 1 1 0 2s-2 1-2 1c-1-1-2-2-3-2s0 0-1-1c1-2 3-4 4-6z" class="E"></path><path d="M724 704c1 1 1 1 1 2v1l-1 2-1 3c-1 1-2 3-3 4h-1c-1 1-4 5-5 7-1 1-2 2-3 2 0-2-1-5-2-7l2-2c0-1 1-2 2-3h2 1 2 1s1 0 2-1-1-1 0-2l1-1v-3l2-2z" class="B"></path><path d="M702 688l2-2h0c0-1 1-3 2-4h0c0-1 1-2 1-3h0c1-2 1-2 1-3l1-1c0-2 2-3 4-4l1-1 1-1h1c1-1 1-1 2-1h1c1-1 2-1 3-1 1 1 1 2 2 3-1 2-2 1-3 3 0 1-1 3 0 5l1-1v2l2 2h-1c-2 0-3 0-4 1h0v2 1c-1 1-2 2-2 3v1h-2c-1 1-1 2-2 3l1 1 2-2v1c0 1-1 2-2 3h-1v1c0 1-1 2-1 3l-1 2 1 1 1 1h1v1 4c-1 0-3-2-4-3h0l-5 1h0v1l-1 1h-1c0-1-1-1-1-2s0-2-1-2v-1c-1-1-1 0-1-1h-1 0c0-2 1-4 1-6h-2v-2h1c1 0 1 0 1-1l2-4v-1z" class="s"></path><path d="M702 688c1 1 2 1 3 2v3c2 0 3 0 4-1l1 1c-1 2-2 3-1 5v2l2 1 1 1 1 1h1v1 4c-1 0-3-2-4-3h0l-5 1h0v1l-1 1h-1c0-1-1-1-1-2s0-2-1-2v-1c-1-1-1 0-1-1h-1 0c0-2 1-4 1-6h-2v-2h1c1 0 1 0 1-1l2-4v-1z" class="E"></path><path d="M708 703c1 0 2-1 3 0l-1 2-5 1v-1c0-1 1-2 3-2z" class="F"></path><path d="M705 697v-3h1c0 1 1 2 1 3 0 2-1 2 0 4l-1 1c-1-2-1-2-1-5z" class="G"></path><path d="M701 697l1 1c1 0 1-1 3-1 0 3 0 3 1 5l1-1 1 1v1c-2 0-3 1-3 2v1h0c-1-1-1-3-2-3 0-1-1-2-2-3v-3z" class="D"></path><path d="M700 696h1v1 3c1 1 2 2 2 3 1 0 1 2 2 3v1l-1 1h-1c0-1-1-1-1-2s0-2-1-2v-1c-1-1-1 0-1-1h-1 0c0-2 1-4 1-6z" class="N"></path><path d="M698 696h2c0 2-1 4-1 6h0 1c0 1 0 0 1 1v1c1 0 1 1 1 2s1 1 1 2h1l1-1 3 9 1 2c1 2 2 5 2 7v1c1 2 1 2 0 4h0l-1 1-2 1-1 1c-1 0-2 1-3 2v1h2c0 1 0 2-1 2-2 2-2 4-2 7h-1c0-1-1-1-1-1h-2c1-1 1-2 1-3l-1 1-1-1 2-2h1c0-1 1-3 1-4-1-1-2-1-3-2-1 0-1-1-2-2l1-2c0-1-1-1-1-1l-1-1c-1-1-3-2-4-3h-1l-1-1c0-1 1-2 0-4s0-3 0-4l-1-12 3-3c2-2 4-3 6-4z" class="a"></path><path d="M704 722c0 2 1 3 0 5h0l-2 1c-1 1-1 1-2 1-1-2-1-2 0-5 1 0 1 0 1 1l3-3z" class="h"></path><path d="M704 708l1-1 3 9c-1 1-1 1-1 2l1 2c-1 1-1 2-2 2 0-1 0 0-1-1l-1 1-3 3c0-1 0-1-1-1 1-2 2-3 2-5 0-1-1-1-2-2h-3v3c-1 1-1 2-2 3h-2c0-2 0-1 1-2v-1c0-2 1-3 2-4 0-1 1-1 2-2l1 1 1 1c2 0 2-2 4-3-1 0-1 0-1-1h2v-1c0-1-1-2-1-3z" class="R"></path><path d="M704 708l1-1 3 9c-1 1-1 1-1 2-2 0-2 0-3 1h-1c0-1 1-1 1-2v-4c-1 0-1 0-1-1h2v-1c0-1-1-2-1-3z" class="p"></path><path d="M707 718c0-1 0-1 1-2l1 2c1 2 2 5 2 7v1c1 2 1 2 0 4h0l-1 1-2 1v-1c-1 1-1 1-2 1s-1 1-2 1h-2-1-1l-1-3h2c2-1 2-1 3-3h0c1-2 0-3 0-5l1-1c1 1 1 0 1 1 1 0 1-1 2-2l-1-2z" class="K"></path><path d="M707 718c0-1 0-1 1-2l1 2c1 2 2 5 2 7v1c1 2 1 2 0 4h0c-1 0 0 0-1-1h-1 0l-1-1 1-1v-2l-2-2-1-1c1 0 1-1 2-2l-1-2z" class="n"></path><path d="M699 702h1c0 1 0 0 1 1v1c1 0 1 1 1 2s1 1 1 2h1c0 1 1 2 1 3v1h-2c0 1 0 1 1 1-2 1-2 3-4 3l-1-1-1-1c-1-1-1-1 0-2-1-1-1-1-1-2-2 2-1 3-2 4h0c-1-1-1 0-2-1 0 1-1 2-1 3s-1 2-2 3c-1-2 0-3 0-4l1-1c2-4 6-7 8-12z" class="Y"></path><path d="M698 696h2c0 2-1 4-1 6h0c-2 5-6 8-8 12l-1 1-1-12 3-3c2-2 4-3 6-4z" class="J"></path><path d="M692 706l1-1 1 1c0 1-1 2-1 3h-1 0v-3z" class="s"></path><path d="M708 732l2-1v2l1 3s0 1-1 2l1 1c0 1-1 2-1 2 1 1 1 1 1 3 1 0 2-1 3-1l1 2 1 3v2l-2 5v3c1 2 1 3 3 4v3l1 4v2l2 2v1 1c1-1 1 0 2-1 0-1 0-1 1-1v-3c1 0 1-1 1-2l2-3v-2-1l2-2c1-1 2-2 4-3h0 1c1 0 2 0 3-1 1 0 1-1 2-1 1-1 1-1 1-2 2-2 2-1 4-2 0 0 0-1 1-2v1 5c0 1-1 1-1 2v7h2l2-2 2-1h1c1 0 2 0 3-1s2-3 4-4h2c1-2 1-2 3-2 0 1 0 2-1 2-1 1-2 2-3 2l-2 2c-1 1-2 1-3 1h-1c-1 2-3 3-5 3h0l-2 2 3-1s1 1 2 1c0 0 1-1 2-1h0l2 1 1 1 1 1h-1c-1 1-1 2-1 4l-1 1v1l-1 2c-1 1-1 3-2 4l-1 1 1 1c-1 0-2 0-3 1 0 1-1 2-2 2l-3 2c-3 0-5 2-7 3s-4 4-6 6v4c-1 1-2 3-3 3-1 1-2 1-2 1-1 1 1 1-2 2h0c-1 1-1 2-2 3s-3 2-4 3h-2v1l-2 2 1 3h0l-1 1h2v1l-1 1c-2-1-2-1-4-1-1 1-1 2-1 4-2 0-2 0-3-2-1 0-2-1-3-2 0 2 1 3-1 5l-3 1h0-1l-2 1c-1 0-1 0-1 2-1-2-1-2-2-2 0-1 1-2 1-2 0-2-1-2-1-3-2-1-3-2-5-2v-2-4-11-11l1-12v-2l-1-4v-3l-1-1h2 0v-3c0-1-2-2-3-2h0c1-2 2-3 4-3h1v-1l3 3v-1l2-3c-1-2-1-3-1-5l1-2 1-3c1-2 1-3 2-4l1-2h2s1 0 1 1h1c0-3 0-5 2-7 1 0 1-1 1-2h-2v-1c1-1 2-2 3-2l1-1z" class="B"></path><path d="M719 803c1 0 1 0 2 1v2c-1 0-2 1-3 1v-1c1-1 1-2 1-3h0z" class="C"></path><path d="M720 795c1-1 2-1 3-2 0 0 0-1 1-2h1c1-1 2-2 4-3 0 1 1 1 1 2h2 0l-4 6-1-1h-2c-1 0-1 1-2 2h-1l-2-2z" class="K"></path><path d="M720 795l2 2h1c1-1 1-2 2-2h2l-1 1s0 1-1 1c-1 2-4 4-6 5l-1 1c0-1 0-2-1-3l-2 2v-1c-1 1-2 1-2 2h-3v1c-1-1-2-1-3-1 1-1 2-2 3-2 2 0 4-2 6-2h1c1-2 2-2 3-4zm-17 23l1 1 3-1v-1l-1-1h-1v-1h3l1-2c1 0 2 0 3-1h2l1-1c0-1 1-1 2-2h3c-1 1-3 2-4 3h-2v1l-2 2 1 3h0l-1 1h2v1l-1 1c-2-1-2-1-4-1-1 1-1 2-1 4-2 0-2 0-3-2-1 0-2-1-3-2l1-2z" class="R"></path><path d="M752 765l2 1 1 1 1 1h-1c-1 1-1 2-1 4l-1 1v1l-1 2c-1 1-1 3-2 4l-1 1 1 1c-1 0-2 0-3 1 0 1-1 2-2 2h-4-1l-1 1-3 2h-1c0-1 1-2 2-2l1-1h1c-1-1-3-1-4-2l3-3 2-1c1-1 1-1 1-3h-2c1-1 3-2 4-3 2-2 7-7 9-8z" class="D"></path><path d="M749 781c0-1-1-2-1-3 0 0 1-2 2-2h1 1c-1 1-1 3-2 4l-1 1z" class="K"></path><path d="M752 765l2 1 1 1-2 2h-1c-1 1-1 1-2 3 1 1 0 2 0 3l-1 1h-2-1l3-3-1-1h-1l-1 2c-1-1-2-1-3-1 2-2 7-7 9-8z" class="B"></path><path d="M743 773c1 0 2 0 3 1-1 2-1 1-2 3-1 0-2 1-2 2v1l2-2h1l1 2-1 1-5 3c-1 1-1 0-1 1-1-1-3-1-4-2l3-3 2-1c1-1 1-1 1-3h-2c1-1 3-2 4-3z" class="C"></path><path d="M743 773c1 0 2 0 3 1-1 2-1 1-2 3-1-2-1-2-1-4z" class="E"></path><path d="M712 785c1 0 2-1 3-2s1-2 3-2c1-1 2-1 3-1 5-1 7-3 11-6l7 2h2c0 2 0 2-1 3l-2 1-3 3v1l-2 2h-1c-2-1-3 0-5 0l-1 3c-2 0-3-1-4 0l1 1c-1 1-1 1-2 1h0c0 1 0 1-1 2 0-1-1-1-1-1h-1c-1 1 0 1-1 1 0 2-2 3-4 3-2 1-6 2-8 2h0l-3 1-1-1c1-1 1-2 2-3 0-1 0-1 1-2v-1c2-2 3-4 6-6l2-1z" class="J"></path><path d="M701 798c1-1 1-2 2-3 0-1 0-1 1-2v-1c2-2 3-4 6-6l-1 3-2 1v1c0 1-1 1-1 2-1 1-1 2-1 3l-1 1h3l1-2v-2c1-1 2-1 2-2l2 1c-1 1-1 2-2 3h1c1-1 3 0 3-1v-2h2l1-1c1 0 1 0 2 1h-1c-1 1 0 1-1 1 0 2-2 3-4 3-2 1-6 2-8 2h0l-3 1-1-1z" class="b"></path><path d="M710 791c1 0 2-2 3-3l1 2h2c1-1 2-3 3-3 2-1 3-3 5-4h5l1-1 2-4h0 1l1 1c-1 1-1 1-1 2-1 1 0 1 1 2h1v1l-2 2h-1c-2-1-3 0-5 0l-1 3c-2 0-3-1-4 0l1 1c-1 1-1 1-2 1h0c0 1 0 1-1 2 0-1-1-1-1-1-1-1-1-1-2-1l-1 1h-2v2c0 1-2 0-3 1h-1c1-1 1-2 2-3l-2-1z" class="F"></path><path d="M732 757h1c1 0 2 0 3-1 1 0 1-1 2-1 1-1 1-1 1-2 2-2 2-1 4-2 0 0 0-1 1-2v1 5c0 1-1 1-1 2v7h2l2-2 2-1h1c1 0 2 0 3-1s2-3 4-4h2c1-2 1-2 3-2 0 1 0 2-1 2-1 1-2 2-3 2l-2 2c-1 1-2 1-3 1h-1c-1 2-3 3-5 3h0l-2 2 3-1s1 1 2 1c0 0 1-1 2-1h0c-2 1-7 6-9 8-1 1-3 2-4 3l-7-2c-4 3-6 5-11 6-1 0-2 0-3 1-2 0-2 1-3 2s-2 2-3 2v-1l1-1c0-1-1-1-1-2l1-1h1c1 0 2-1 3-1l1-4h0v-4l2 2v1 1c1-1 1 0 2-1 0-1 0-1 1-1v-3c1 0 1-1 1-2l2-3v-2-1l2-2c1-1 2-2 4-3h0z" class="D"></path><path d="M726 763h1c0-1 1-1 1-1 1-1 1-2 2-2s1 0 2 1c1 0 2-2 3-2h1l-1 1c0 1-1 1-1 2l3 1c1-2 2-2 3-4v-1l1 2-1 2h1v4l-1 1h-3l1-2-1-1-4 3-2 1-2 3v-2l-1 1v3l-1 1-1-1c1-1 1 0 0-1v-1l-3 2h0v-3c1 0 1-1 1-2l2-3v-2z" class="G"></path><path d="M731 768v-2c3-3 4-1 7-2v-1l3 3-1 1h-3l1-2-1-1-4 3-2 1z" class="h"></path><path d="M733 767l4-3 1 1-1 2h3v2h1 0c1-1 2-2 3-2h1v-1l3-1s1 1 2 1c0 0 1-1 2-1h0c-2 1-7 6-9 8-1 1-3 2-4 3l-7-2c-4 3-6 5-11 6-1 0-2 0-3 1-2 0-2 1-3 2s-2 2-3 2v-1l1-1c0-1-1-1-1-2l1-1h1c1 0 2-1 3-1l1-4h0v-4l2 2v1 1c1-1 1 0 2-1 0-1 0-1 1-1h0l3-2v1c1 1 1 0 0 1l1 1 1-1v-3l1-1v2l2-3 2-1z" class="p"></path><path d="M733 767l4 2v4l-1-1c-2 0-2 1-4-1v1l-3 3h-3l1-1 1-1v-3l1-1v2l2-3 2-1z" class="N"></path><path d="M723 773h0l3-2v1c1 1 1 0 0 1l1 1-1 1c-1 1-2 3-4 3h-2c-1 1-2 1-3 1l1-4h0v-4l2 2v1 1c1-1 1 0 2-1 0-1 0-1 1-1z" class="a"></path><path d="M733 767l4-3 1 1-1 2h3v2h1 0c1-1 2-2 3-2h1v-1l3-1s1 1 2 1h-1c-3 2-6 6-9 7v-1l-1 1v1h-2v-1-4l-4-2z" class="Z"></path><path d="M685 765h0c1-2 2-3 4-3h1v-1l3 3c-1 2-1 4-2 6v4 4 3 1c-1 4-1 7-2 10l1 1c-1 1-1 1-1 3h2v-1-1-2c1-2 2-3 3-4h1c0 1 0 2-1 3 1 0 2 1 3 2h0c1-1 1-2 2-2a30.44 30.44 0 0 0 8-8l3-3-1 2 1 1 2-2c0 1 1 1 1 2l-1 1v1l-2 1c-3 2-4 4-6 6v1c-1 1-1 1-1 2-1 1-1 2-2 3l1 1 3-1v1 2l1 2h1c1 0 2 0 3 1v-1h3c0-1 1-1 2-2v1l2-2c1 1 1 2 1 3l1-1c2-1 5-3 6-5 1 0 1-1 1-1l1-1 1 1c-2 3-5 5-9 7h0l-4 2c-5 2-9 5-11 10 0 1-1 2-1 3l-1 2c0 2 1 3-1 5l-3 1h0-1l-2 1c-1 0-1 0-1 2-1-2-1-2-2-2 0-1 1-2 1-2 0-2-1-2-1-3-2-1-3-2-5-2v-2-4-11-11l1-12v-2l-1-4v-3l-1-1h2 0v-3c0-1-2-2-3-2z" class="g"></path><path d="M696 824l1-1h1v3h-1-2l1-2z" class="X"></path><path d="M691 774v4 3 1c-1 4-1 7-2 10l-1-1v-10c0-2 0-4 1-5h0c1 0 1 0 1-1l1-1z" class="S"></path><path d="M690 814c0-1 0-2 1-3 0-1-1-1 0-3l1-1v-1l1-1h1v5 4 1 1c0 1 0 1 1 2v1h-4v-2c-1-1-1-2-1-3z" class="W"></path><path d="M687 814h3c0 1 0 2 1 3v2h4v-1c-1-1-1-1-1-2v-1h1 0l2 2c1 0 0 0 1 1v1c-1 1-1 2-2 2l-2-1h0v3c1 1 1 1 2 1l-1 2h2l-2 1c-1 0-1 0-1 2-1-2-1-2-2-2 0-1 1-2 1-2 0-2-1-2-1-3-2-1-3-2-5-2v-2-4z" class="M"></path><path d="M689 796h2v-1-1-2c1-2 2-3 3-4h1c0 1 0 2-1 3 1 0 2 1 3 2h0v1l-2 4v2l-2 1v2l-1 1c0-1-1-1-2-1v1c1 1 1 1 1 2s0 1-1 2l-1-1v-8c1 1 1 1 2 1v-3h-2v-1z" class="r"></path><path d="M694 791c1 0 2 1 3 2h0v1l-2 4-2-2c-1-2 0-3 1-5z" class="q"></path><path d="M697 793c1-1 1-2 2-2a30.44 30.44 0 0 0 8-8l3-3-1 2 1 1 2-2c0 1 1 1 1 2l-1 1v1l-2 1c-3 2-4 4-6 6v1c-1 1-1 1-1 2-1 1-1 2-2 3 0 1-1 2-1 3l1 1 1-1v1c-1 1-1 1-3 1v-2c-1 0-1 1-2 2 0 1 1 1-1 3h0v-4c1-1 1-1 1-2s0-2 1-2c0-1 1-2 1-3l-2-1v-1z" class="i"></path><path d="M701 798l1 1 3-1v1 2l1 2h1c1 0 2 0 3 1v-1h3c0-1 1-1 2-2v1l2-2c1 1 1 2 1 3-3 0-9 4-11 6l-2 3c-1 0-1 1-2 2 0 1 0 1-1 2-1-1-1-1-2-1l-1 1c-1-2-1-4-1-6l1-2c-1-2 0-3 0-5 2 0 2 0 3-1v-1l-1 1-1-1c0-1 1-2 1-3z" class="a"></path><path d="M708 732l2-1v2l1 3s0 1-1 2l1 1c0 1-1 2-1 2 1 1 1 1 1 3 1 0 2-1 3-1l1 2 1 3v2l-2 5v3c1 2 1 3 3 4v3l1 4v2 4h0l-1 4c-1 0-2 1-3 1h-1l-1 1-2 2-1-1 1-2-3 3a30.44 30.44 0 0 1-8 8c-1 0-1 1-2 2h0c-1-1-2-2-3-2 1-1 1-2 1-3h-1c-1 1-2 2-3 4v2 1 1h-2c0-2 0-2 1-3l-1-1c1-3 1-6 2-10v-1-3-4-4c1-2 1-4 2-6v-1l2-3c-1-2-1-3-1-5l1-2 1-3c1-2 1-3 2-4l1-2h2s1 0 1 1h1c0-3 0-5 2-7 1 0 1-1 1-2h-2v-1c1-1 2-2 3-2l1-1z" class="N"></path><path d="M715 772c1-1 1 0 2-1 1 2 0 3 1 4l-1 4c-1 0-2 1-3 1h-1v-2-1c1-2 2-3 2-5z" class="M"></path><path d="M696 779h2c1-1 2-3 3-3 2-2 4-3 5-5s3-4 3-6c1-2 1-3 2-4 1 0 1 1 2 2h0l2-1v1c0 1 0 1-1 3-1 1 0 1 0 2 2-1 1-1 1-2l2-1 1 4v2 4h0c-1-1 0-2-1-4-1 1-1 0-2 1-2 1-2 2-4 3h-1c0 1 0 1-1 2l-1-1 7-6v-2c-2 1-3 3-4 4-2 2-3 3-5 4-1 1-2 3-3 4-2 1-1-1-3 0-1 2-4 3-5 4-2 2-3 4-5 5v3 1l-1-1c1-3 1-6 2-10 2-1 4-2 5-3z" class="c"></path><path d="M713 750h3l-2 5v3c1 2 1 3 3 4v3l-2 1c0 1 1 1-1 2 0-1-1-1 0-2 1-2 1-2 1-3v-1l-2 1h0c-1-1-1-2-2-2-1 1-1 2-2 4 0 2-2 4-3 6s-3 3-5 5c-1 0-2 2-3 3h-2c-1 1-3 2-5 3v-1-3-4-4c1-2 1-4 2-6v-1c2 1 2 2 4 2l1-1v3c2-2 5-5 5-7v-1c2-1 2-1 4-1 2-2 3-4 5-5 1-1 1-2 1-3z" class="p"></path><path d="M691 778l1-1s1 0 2-1l1 1h1v2c-1 1-3 2-5 3v-1-3z" class="l"></path><path d="M698 767c2-2 5-5 5-7v-1c2-1 2-1 4-1-1 2-2 2-2 4l-1 1c-1 1-3 4-5 6 0 1-1 3-2 4v-1l-1-1 2-4z" class="T"></path><path d="M693 763c2 1 2 2 4 2l1-1v3l-2 4c-1 1-1 2-2 2v-2h-2l-1-1c1-2 1-4 2-6v-1z" class="L"></path><path d="M708 732l2-1v2l1 3s0 1-1 2l1 1c0 1-1 2-1 2 1 1 1 1 1 3 1 0 2-1 3-1l1 2 1 3v2h-3c0 1 0 2-1 3-2 1-3 3-5 5-2 0-2 0-4 1v1c0 2-3 5-5 7v-3l-1 1c-2 0-2-1-4-2l2-3c-1-2-1-3-1-5l1-2 1-3c1-2 1-3 2-4l1-2h2s1 0 1 1h1c0-3 0-5 2-7 1 0 1-1 1-2h-2v-1c1-1 2-2 3-2l1-1z" class="R"></path><path d="M714 743l1 2 1 3v2h-3c-2-1-3 2-5 3h0l3-3 1-2v-2c-1 0-1-2-1-2 1 0 2-1 3-1z" class="l"></path><path d="M707 733c1 2 1 3 0 5 1 1 1 1 2 1v2 1l-1 1 1 2v1c-1 1-1 2-1 3l-1 2h-1c-1-1-2-2-2-4 0-1 0-1-1-2 0-3 0-5 2-7 1 0 1-1 1-2h-2v-1c1-1 2-2 3-2z" class="Q"></path><path d="M708 743l-1 1c-1 1-2 0-3 0v-2c1-1 2-2 4-3l1 2v1l-1 1z" class="E"></path><path d="M699 744h2s1 0 1 1h0c0 2 0 3 1 5 1 0 1 2 1 2v2h0-1c-1 1-2 1-3 1l-1 1h0c-2 0-2 1-4 1l-1-2 1-2 1-3c1-2 1-3 2-4l1-2z" class="K"></path><path d="M696 750h1 3 1v1l-2 1v4c-2 0-2 1-4 1l-1-2 1-2 1-3zm-176 97c1 0 1 0 2 1l1 1 2-1h0c1 1 1 1 2 1l1 1c1 1 2 0 3 0h0c3 1 4 1 7 0l-1 2c3 1 6 3 9 3 2 1 4 2 5 2 3 1 8 3 9 4h1c3 1 6 2 10 3h1 4l7 1c0 2-1 2-2 3 2 1 4 1 6 1l1 2h1 2s1 1 2 1h1c1 0 3 1 4 1 4 2 7 2 11 4 2 1 3 2 5 3 0 0 1 1 2 0v-1c1 1 1 1 2 1 1 1 2 1 2 2h1c-1 0-2 1-3 0h-2c1 1 4 2 6 4l1 1v1l-1 1v1c3 0 6-1 9-1 2-1 4 1 5 2l1 1h0l2-1c2 1 3 4 6 5 1-1 1-1 2-1v2 2c0 2-2 4-4 6l-3 1h0c-3 2-6 2-9 3l-2 1-1 1-1-1-37 3c-4 1-8 1-12 1h-24-40c-9 0-19 1-28 0l-7-2h-15c-4 0-8-1-11 0s-6 0-9 0-7 1-11 0l-33-1h-1c-2 0-4-1-5-1-1-1-2-1-3-1s-2-1-2-1h3l2-2-3-2c0 1 0 1-1 1h-1l-3-1h2c1-2 2-2 3-3l1-1h0c2 1 4 0 5-1l1-1c3-2 5-4 8-6h0c-2-1-3-1-4 0v-1h-3c-2 1-2 1-4 1v-2l-1 1v-2-1l-2-1v1l-1-1h-2 0c-3-2-6-1-9-3-1 0-4-1-5-1 0-1 0 0-1-1l-1 1v-2h-1c-1 0-1-1-2-1h-1v-1h3l-2-3c1 0 2 0 3-1l1-1c2 0 3-1 4-2 2 0 3-1 4-1 1-2 2-2 4-2v-1l3-1h0c3-1 5 0 7 0s2-1 3-2v-1c0-1-1-2-2-2h-1l4-1h10l1-1h1c3 0 7-1 10-1 3 1 7 2 10 4l12 5c2 1 3 1 5 0h1c2 0 5-1 7 0 3 1 6 2 8 4l1 2 2 3 4 2c-1 2-1 3-3 4 0 1 0 1 1 2l2-1 1 1 2-2c1 0 1 0 1 2 1 1 1 1 2 1v1c-1 1-2 1-3 1v1h4v1h3 1c2 1 3 3 6 2l1 1h-1l1 1 1-1 3 2c1 0 2 1 3 1v-1h1c1-1 1-2 3-2l1 1v-1c1-2 1-2 3-4h0v-2l3-1c5 1 6 1 10-1h2l2-1h5v1l2 1v2h0c0-1 1-3 1-4 0-2 0-2 1-3l2 2v1h5c-1-1-1-1-2-1l-3-3h-1s-1 1-1 0v-1c0-1-2-2-3-3 2 0 3 0 4 1l1 2h2l1 1h2l-1-2h1c2 0 3 0 4-2h3l-3-1v-1c0-1-1-1-2-2h0v-1c5-2 9-4 15-3v-1h0c-3-1-5-1-7-1l-2 1c-2 0-3-1-5 0h-2c-1-2-3-3-5-4s-3-1-4-1l-2-2c-1 0-2 1-3 1h0-1l-2-1c0-1 0-2 1-3h3 1c1 0 1-1 1-1-2-2-4-4-5-6h0l-2-1-1-2z" class="D"></path><path d="M623 908c5-3 10-2 17-2h0c-3 2-6 2-9 3l-2 1-1 1-1-1c-4 0-8 1-12 1h-8c-2 0-4 1-6 1h-2c-1 0-2 0-3-1 3 0 6 0 9-1 1-1 3-2 4-2s3 0 4 1c3 0 6 0 10-1z" class="O"></path><path d="M595 906v1l-1 1v1h4c1 0 1 0 2 1v-1h2c1 0 1 0 2-1h1l2-2c2 0 4 0 6 1h2l1-1c1 0 2 1 2 1h4l1 1c-4 1-7 1-10 1-1-1-3-1-4-1s-3 1-4 2c-3 1-6 1-9 1-5-1-10 1-14 1-2 1-4 1-6 1l-2-2h2v-1c5 1 10 0 15-2h0l1-2h3z" class="W"></path><path d="M448 906c4 0 7 1 11 1h17 4c1 1 2 2 3 2 2 1 3 2 5 2l1 1c-5 0-9-1-14-1l-29-1h-2l4-4z" class="a"></path><path d="M448 906c4 0 7 1 11 1-4 2-9 2-13 3h-2l4-4z" class="T"></path><path d="M524 909l1-1c1 0 1 1 2 1h1c1 0 2 0 3-1 1 0 2 0 3 1h6c2 0 3 0 4 1 2-1 7 0 9-1h1 2s1-1 2-1l1 1c1 0 1-1 2-1l1 1c2 0 3 0 4 1h3c1 0 5 0 5 1l2 2-47-1h-12c2-1 3 0 4-2h2l1-1z" class="M"></path><path d="M524 909l1-1c1 0 1 1 2 1h1c1 0 2 0 3-1 1 0 2 0 3 1h6c2 0 3 0 4 1h0 5l-1 1c-5 0-13-1-19 1h-12c2-1 3 0 4-2h2l1-1z" class="W"></path><path d="M394 906l3-1c3 0 2 1 4 1 1 0 1-1 2 0 3 1 4 1 7 0 1 0 2 0 3 1l11-1c2 2 5 2 7 3 2 0 2-1 3-2h5 1c1 1 1 1 2 1 2 0 3-1 4-2h2l-4 4c-2 1-4 0-6 0-3 0-7 1-10 0-2 0-4-1-7-1h-12c-6 0-12-1-17-1l2-2z" class="X"></path><path d="M427 901h0c0 2 2 2 2 3 1 1 1 1 2 1h3c1 0 2 0 3-1 2 1 2 1 2 3h-5c-1 1-1 2-3 2-2-1-5-1-7-3l-11 1c-1-1-2-1-3-1-3 1-4 1-7 0-1-1-1 0-2 0-2 0-1-1-4-1l-3 1-3-2c0 1 0 1-1 1h-1l-3-1h2c1-2 2-2 3-3l2 2 1-1h2l1 1c1-1 1 0 2-1 0-1 0-1 1-1l1 1-2 2h1c1 0 1 0 2 1h6 3c2-1 2-1 3 0 2 0 3-1 5-1l1-2h4c1 0 2-1 3-1z" class="k"></path><path d="M427 901h0c0 2 2 2 2 3s0 1-1 2l-1-1c-2 1-2 0-4 0-1-1-2 0-3 0h0l-1-1 1-2h4c1 0 2-1 3-1z" class="a"></path><path d="M427 901h0c0 2 2 2 2 3s0 1-1 2l-1-1-3-3c1 0 2-1 3-1z" class="O"></path><path d="M637 892l2-1c2 1 3 4 6 5 1-1 1-1 2-1v2 2c0 2-2 4-4 6l-3 1c-7 0-12-1-17 2l-1-1h-4s-1-1-2-1l-1 1h-2c0-1 0-2 1-3 0 0 1 0 1-1h2 1l1 1 1-1h2l-1 1h1l1-1h2c1-1 2-1 4-1 0 0 1 0 1-1h1l1 1c1 0 2-1 3-2 0 1 0 0 1 1h0c1-1 1-1 1-2l2-2c0 1 1 1 2 1l2-2c-2-1-4-3-6-4h0z" class="c"></path><path d="M645 896c1-1 1-1 2-1v2 2c0 2-2 4-4 6l-3 1c-7 0-12-1-17 2l-1-1c3-2 6-4 9-4h3 1c1 0 2 0 3 1 0-1 1-1 2-2 1 0 3-1 4-2 1-2 1-3 1-4z" class="m"></path><path d="M406 892c3 0 5 0 8 2 1 1 1 2 2 2l1 2c1 2 1 3 3 4l-1 2c-2 0-3 1-5 1-1-1-1-1-3 0h-3-6c-1-1-1-1-2-1h-1l2-2-1-1c-1 0-1 0-1 1-1 1-1 0-2 1l-1-1h-2l-1 1-2-2 1-1h0c2 1 4 0 5-1l1-1c3-2 5-4 8-6z" class="q"></path><path d="M406 892c3 0 5 0 8 2 1 1 1 2 2 2-1 1-2 2-3 2l-1-1c1-1 0-1 1-1-1-1-5 1-7 1h0c-1 1-2 1-4 1-1 1-2 1-3 1l-1-1c3-2 5-4 8-6z" class="G"></path><path d="M406 897v1 2h1 1c0 1 0 2 2 2h1l1-2c2 0 2 1 3 1l1 1-1 1-2-1c-1 0 0 0-1 1 0 1 0 0-1 1v1h-3-6c-1-1-1-1-2-1h-1l2-2-1-1c-1 0-1 0-1 1-1 1-1 0-2 1l-1-1h-2l-1 1-2-2 1-1h0c2 1 4 0 5-1l1-1 1 1c1 0 2 0 3-1 2 0 3 0 4-1z" class="r"></path><path d="M478 901l2-2c2 1 1 2 2 3h2c1 1 2 3 3 4 0 1 1 2 2 2s1 0 2-1c0 1 0 2 2 2h5l1-1 2 1h0 2c2 1 3 1 4 0l2-1 1 2c2-1 3-1 5-1h2c2 0 3 1 4 1-1 2-2 1-4 2-9-1-19 0-28 0l-1-1c-2 0-3-1-5-2-1 0-2-1-3-2h-4-17c-4 0-7-1-11-1h-2l1-4h0v-2h1c2 2 3 2 6 4h0c2 0 2 0 4 1h1 1 0 2c1 1 2 1 3 0h1 0c2 1 3 0 5 0h1 1 0c1 0 3 1 4 0l1-1h2 1l-3-3z" class="m"></path><path d="M443 891v-1h2c1 0 3 1 4 0 3 0 5 1 7 2h1 3c1 0 2 0 3-1h1c1 1 1 1 2 1 2 0 3 0 4 1l1 1c2 1 6 4 7 7l3 3h-1-2l-1 1c-1 1-3 0-4 0h0-1-1c-2 0-3 1-5 0h0-1c-1 1-2 1-3 0h-2 0-1-1c-2-1-2-1-4-1h0c-3-2-4-2-6-4l-1-3-1-2h-2l-1 1h0l-4-4c1 0 2 0 4-1z" class="r"></path><path d="M439 892c1 0 2 0 4-1 1 2 3 3 3 4h-2l-1 1h0l-4-4z" class="S"></path><path d="M445 890c1 0 3 1 4 0 3 0 5 1 7 2 0 1-1 2-2 3h2v1c-3 0-4 0-6-1-1-1-1-1-2-1v-2h-3v-2z" class="O"></path><path d="M457 892h3c1 0 2 0 3-1h1c1 1 1 1 2 1 2 0 3 0 4 1l1 1-1 1h2l-2 2h-2c-1 0-1 0-1 1l-2-2c-1 0-2 0-2 1h-2v1c-1 0-3 0-4-1l-1-1v-1h-2c1-1 2-2 2-3h1z" class="L"></path><path d="M464 891c1 1 1 1 2 1 2 0 3 0 4 1l1 1-1 1h-3c-1 1-2 0-3 0v-1-3z" class="U"></path><path d="M457 892h3c1 0 2 0 3-1h1v3 1h-1l-3-1-1 1h-1v-1c1 0 1 0 1-1l-2-1z" class="a"></path><path d="M447 897c1 0 2 0 3-1v2c2 0 2 1 4 1l1 2c1 0 1 0 2-1l1 2 1-1c0-1 1-1 2-2l1 1h0c1 1 1 1 1 2l-2 2h1 2v-1c1 0 1 0 2 1v-2l1 1v1c1-1 1-1 2-1s1 0 1 1l1-1h0v-2l1 1s0 1 1 1v-2h1v2c1 0 1 0 2-1l1 1 1-1c1 1 0 1 2 2h-2l-1 1c-1 1-3 0-4 0h0-1-1c-2 0-3 1-5 0h0-1c-1 1-2 1-3 0h-2 0-1-1c-2-1-2-1-4-1h0c-3-2-4-2-6-4l-1-3z" class="l"></path><path d="M396 887l2-1h0 2c1 0 3 1 4 1h1c4-2 5 0 9-1v1c2 0 4 0 5 1 2 0 3 0 4-1 2 0 2 1 4 0l3 3 1-1h2v1l-1 1c1 1 1 2 2 2 2 1 3 2 5 3h0v-1-1l2 2v1h1l1-1h0l1-1h2l1 2 1 3h-1v2h0l-1 4c-1 1-2 2-4 2-1 0-1 0-2-1h-1c0-2 0-2-2-3-1 1-2 1-3 1h-3c-1 0-1 0-2-1 0-1-2-1-2-3h0c-1 0-2 1-3 1h-4c-2-1-2-2-3-4l-1-2c-1 0-1-1-2-2-3-2-5-2-8-2h0c-2-1-3-1-4 0v-1h-3c-2 1-2 1-4 1v-2l-1 1v-2-1l-2-1 2-1 2 1z" class="p"></path><path d="M396 887l2-1h0 2c1 0 3 1 4 1h1c2 0 4 1 5 1 2-1 5 0 7 0 1 1 2 1 3 1h-5-2c-2 0-5-1-8-1h0c-2 1-4 1-6 2h-3v-2-1z" class="c"></path><path d="M405 887c4-2 5 0 9-1v1c2 0 4 0 5 1 2 0 3 0 4-1 2 0 2 1 4 0l3 3 1-1h2v1l-1 1c1 1 1 2 2 2 2 1 3 2 5 3h0v-1-1l2 2v1h1l1-1h0l1-1h2l1 2 1 3h-1v2h0-1-3v-1h-1l1 1-1 1h-1v-1l1-1c-1 0-1-1-1-2-1 1-1 1-2 1-1-1-1-2-3-3h0c-1-1-2-1-3-2v-1c-1 0-2-1-3-2h-1c-2 0-3-1-4-1-1-1-2-1-3-1-1-1-1-1-2-1s-2 0-3-1c-2 0-5-1-7 0-1 0-3-1-5-1z" class="U"></path><path d="M443 896l1-1h2l1 2 1 3h-1v2c-1-2-3-4-4-6z" class="e"></path><path d="M402 891c3-2 7-1 10 0 1 1 1 1 3 1l1-1 1 1h4l1 1s1 0 2-1c1 1 1 1 1 2h2v2c2 0 1-1 3 0h1 1c1 1 2 1 4 1h0c2 1 2 2 3 3 1 0 1 0 2-1 0 1 0 2 1 2l-1 1v1h1l1-1-1-1h1v1h3 1l-1 4c-1 1-2 2-4 2-1 0-1 0-2-1h-1c0-2 0-2-2-3-1 1-2 1-3 1h-3c-1 0-1 0-2-1 0-1-2-1-2-3h0c-1 0-2 1-3 1h-4c-2-1-2-2-3-4l-1-2c-1 0-1-1-2-2-3-2-5-2-8-2h0c-2-1-3-1-4 0v-1z" class="g"></path><path d="M417 892h4l1 1s1 0 2-1c1 1 1 1 1 2h0c0 1 0 2 1 2v1 1l-3-3-1 1-2-1c-1 0-2-2-3-3z" class="n"></path><path d="M427 901c4-1 7 1 10 3h0c-1 1-2 1-3 1h-3c-1 0-1 0-2-1 0-1-2-1-2-3z" class="G"></path><path d="M417 898c1 0 2-1 3 0 2 0 5 1 6 0l1 1h1c1 0 3 0 4 1h0-1c-1 0-2 0-4 1-1 0-2 1-3 1h-4c-2-1-2-2-3-4z" class="m"></path><path d="M470 885l2-1 1 1 2-2c1 0 1 0 1 2 1 1 1 1 2 1v1c-1 1-2 1-3 1v1h4v1h3l-1 2 1 1h1l-1 1c1 1 2 0 2 0l1 1c1 0 1 0 2-1 1 1 0 2 2 2l1-1v2h0c1-1 1-1 2-1 1 1 1 2 2 3h2c2 1 2-1 4 1l1-1 1 1h2 1 2v1c1 0 1-1 2-1v1h1l1-1h0v2h1l1-2c1 1 1 0 1 1h2 1 0l1 1h1l1 1 2-1v1l1 1 1-1 1 1c1 0 0 0 1-1 1 1 1 1 1 2 1 0 3 0 4 1 1 0 2-1 3-1s1 0 1 1l1-1 1 1h1s1-1 2-1h1 1 2 1c1 0 2 0 3-1l1-1 2 2h1c1 0 2 1 3 1l3-2 4-3 3-3c2 1 7 3 9 5h2 0c2-1 6-1 8 0h1c2 0 5 2 6 3v2h0c-5 2-10 3-15 2v1h-2c0-1-4-1-5-1h-3c-1-1-2-1-4-1l-1-1c-1 0-1 1-2 1l-1-1c-1 0-2 1-2 1h-2-1c-2 1-7 0-9 1-1-1-2-1-4-1h-6c-1-1-2-1-3-1-1 1-2 1-3 1h-1c-1 0-1-1-2-1l-1 1-1 1h-2c-1 0-2-1-4-1h-2c-2 0-3 0-5 1l-1-2-2 1c-1 1-2 1-4 0h-2 0l-2-1-1 1h-5c-2 0-2-1-2-2-1 1-1 1-2 1s-2-1-2-2c-1-1-2-3-3-4h-2c-1-1 0-2-2-3l-2 2c-1-3-5-6-7-7l-1-1 1-1-2-2v-2l-1-1 2-2z" class="U"></path><path d="M567 903l2 1h2v2l1 1-1 1c-2 0-2-1-3-2h-2l-1-1 2-2z" class="r"></path><path d="M476 885c1 1 1 1 2 1v1c-1 1-2 1-3 1v1h4v1h3l-1 2 1 1-1 1h-1c-1-2-2-3-4-3h-3c1-1 1-1 1-2h-3v-1c4 0 3-2 5-3z" class="N"></path><path d="M569 910l1-1c1 0 3 0 4-1h0 2c2 0 2 0 3 1 0 0 2-1 3-1h5 4c-5 2-10 3-15 2v1h-2c0-1-4-1-5-1z" class="i"></path><path d="M537 906h1s1-1 2-1h1 1 2 1c1 0 2 0 3-1l1-1 2 2h1c1 0 2 1 3 1l-1 2-1-1c-1-1-2 0-3 0-1-1-1 0-1-1-2 1-3 1-4 1h-2l-1 1c-1-1-1-1-2-1h0c-1 0-2-1-3-1z" class="N"></path><path d="M565 898c2 1 7 3 9 5v1c-1 1-1 0 0 1v1h0l-1 1h-1l-1-1v-2h-2l-2-1 2-2h-1c-2 0-3 1-4 3l-1-1c-2 0-1 1-2 1h-3l4-3 3-3z" class="a"></path><path d="M500 907l4-4c1 1 0 2 0 3h0c1-1 2-2 4-3h0l-1 1c0 1-1 1-1 2h1 1v1h1 0c1-1 2-2 3-2l1-1v1l-1 2h1l2-2h0v2h1l2-1 1 1-1 1c1 0 1-1 2-1h1s0 1 1 1l2-1h0v1 1l-1 1h-2c-1 0-2-1-4-1h-2c-2 0-3 0-5 1l-1-2-2 1c-1 1-2 1-4 0h-2 0l-2-1 1-1z" class="S"></path><path d="M471 892h0c1 1 2 1 3 2 2 0 2-1 4 1h1v1h0 1l1-1c0 1 1 1 1 2h2v1h1l1-1v3h0 2c0-1 1-1 2-1v3c2 1 2 0 4 2l2-2h0v2l3-2c1 1 0 1 1 2 1-1 1-1 2-1v2c-1 0-2 0-2 1v1l-1 1-1 1h-5c-2 0-2-1-2-2-1 1-1 1-2 1s-2-1-2-2c-1-1-2-3-3-4h-2c-1-1 0-2-2-3l-2 2c-1-3-5-6-7-7l-1-1 1-1z" class="l"></path><path d="M400 861h10c-1 0-2 0-3 1v1 1l2 1v1l8 3-1 1c2 1 5 3 7 4v1l2 1c1 2 3 4 3 6l1 2-1 1c1 1 1 1 2 1 4 1 6 5 9 6l4 4-1 1h-1v-1l-2-2v1 1h0c-2-1-3-2-5-3-1 0-1-1-2-2l1-1v-1h-2l-1 1-3-3c-2 1-2 0-4 0-1 1-2 1-4 1-1-1-3-1-5-1v-1c-4 1-5-1-9 1h-1c-1 0-3-1-4-1h-2 0l-2 1-2-1-2 1v1l-1-1h-2 0c-3-2-6-1-9-3-1 0-4-1-5-1 0-1 0 0-1-1l-1 1v-2h-1c-1 0-1-1-2-1h-1v-1h3l-2-3c1 0 2 0 3-1l1-1c2 0 3-1 4-2 2 0 3-1 4-1 1-2 2-2 4-2v-1l3-1h0c3-1 5 0 7 0s2-1 3-2v-1c0-1-1-2-2-2h-1l4-1z" class="h"></path><path d="M426 882h2l1 2-1 1c-1 0-2 1-2 1-3-1-5 0-7-2h2c1 1 2 1 3 1l2-3z" class="D"></path><path d="M373 875l1-1 2 2v2l1 1 1-1v1h-1l1 1 2-1c-1 1-1 1 0 2l2 1-1 1h1l2-2v1h2c1 0 2 1 4 1h2l1 1c1 0 0-1 2 0h4c2-1 4-2 6-1 2 2 5 1 8 2h1c1 0 2 0 3 1h1c2 0 3 1 5 1-1 1-2 1-4 1-1-1-3-1-5-1v-1c-4 1-5-1-9 1h-1c-1 0-3-1-4-1h-2 0l-2 1-2-1-2 1v1l-1-1h-2 0c-3-2-6-1-9-3-1 0-4-1-5-1 0-1 0 0-1-1l-1 1v-2h-1c-1 0-1-1-2-1h-1v-1h3l-2-3c1 0 2 0 3-1z" class="a"></path><path d="M378 872c2 0 3-1 4-1l-1 3h1l2-1 1 1h2v2c1-1 1-1 2-1h1 2 0c2 1 0 0 2 0v1h2l2 2c2 0 5-1 7-2h1c0 1 0 1-1 2h1l1-1h1l2-1h0l-1 1v1l2 1h1s1 1 1 2h0c1 0 2 1 3 1l-3 1-1-1h-5c0-1-1-1-2-1h-1-6v-1c-2 1-2 1-3 2l-1-1c-2 0-3 0-4-1h-1-4-2l-1 2h0l-2-1c-1-1-1-1 0-2l-2 1-1-1h1v-1l-1 1-1-1v-2l-2-2c2 0 3-1 4-2z" class="D"></path><path d="M374 874c2 0 3-1 4-2l2 1v1h-1-1l-1 1 1 1h3c1 0 1-1 2 0s1 2 2 3 3 1 5 1h-1-4-2l-1 2h0l-2-1c-1-1-1-1 0-2l-2 1-1-1h1v-1l-1 1-1-1v-2l-2-2z" class="O"></path><path d="M400 861h10c-1 0-2 0-3 1v1 1l2 1v1l8 3-1 1c2 1 5 3 7 4v1l2 1c1 2 3 4 3 6h-2l-2 3c-1 0-2 0-3-1h-2c-2 0-2-1-3-2-1 0-2-1-3-1h0c0-1-1-2-1-2h-1l-2-1v-1l1-1h0l-2 1h-1l-1 1h-1c1-1 1-1 1-2h-1c-2 1-5 2-7 2l-2-2h-2v-1c-2 0 0 1-2 0h0-2-1c-1 0-1 0-2 1v-2h-2l-1-1-2 1h-1l1-3c1-2 2-2 4-2v-1l3-1h0c3-1 5 0 7 0s2-1 3-2v-1c0-1-1-2-2-2h-1l4-1z" class="C"></path><path d="M423 875l2 1c-1 1-2 2-3 2l-1-1v-1l2-1z" class="E"></path><path d="M389 867c3-1 5 0 7 0l2 2h-4 0l-1-1c-1 0-2 0-4-1z" class="I"></path><path d="M387 869c2-1 4 0 5 0l1 1v1h-2-4c0 1 0 1-1 1 0-2 0-2 1-3z" class="E"></path><path d="M382 871c1-2 2-2 4-2l-1 4h2c1 0 0 0 1 1 2 0 3-1 4-1h1c-1 1 0 1-1 1l-2 1h-1c-1 0-1 0-2 1v-2h-2l-1-1-2 1h-1l1-3z" class="V"></path><path d="M399 864c3 2 7 2 10 2 3 1 5 2 8 3l-1 1c-4-1-11-4-15-2-1 0-1 1-1 1h-2l-2-2c2 0 2-1 3-2v-1z" class="T"></path><path d="M400 861h10c-1 0-2 0-3 1v1 1l2 1v1c-3 0-7 0-10-2 0-1-1-2-2-2h-1l4-1z" class="L"></path><path d="M400 861h10c-1 0-2 0-3 1v1h-6c-1-1-2-1-3-1h-1-1l4-1z" class="G"></path><path d="M407 877v-1l-2-1c1 0 2 0 4 1 1-1 2-1 3 0v1h-1v1h2v1h1 5v1c1 0 1 0 2-1 1 2 0 3 1 4l2-1h2l-2 3c-1 0-2 0-3-1h-2c-2 0-2-1-3-2-1 0-2-1-3-1h0c0-1-1-2-1-2h-1l-2-1v-1l1-1h0l-2 1h-1z" class="B"></path><path d="M530 884c0-2 0-2 1-3l2 2v1h5c1 1 4 1 5 1h2c1 1 1 2 3 2 1 0 1 1 2 1 2 2 5 1 8 2h1c1 1 2 2 4 3v1h-2l-1-1c-1 0-2 0-2 2 1 1 2 1 3 1v1c1 0 3 0 4 1l-3 3-4 3-3 2c-1 0-2-1-3-1h-1l-2-2-1 1c-1 1-2 1-3 1h-1-2-1-1c-1 0-2 1-2 1h-1l-1-1-1 1c0-1 0-1-1-1s-2 1-3 1c-1-1-3-1-4-1 0-1 0-1-1-2-1 1 0 1-1 1l-1-1-1 1-1-1v-1l-2 1-1-1h-1l-1-1h0-1-2c0-1 0 0-1-1l-1 2h-1v-2h0l-1 1h-1v-1c-1 0-1 1-2 1v-1h-2-1-2l-1-1-1 1c-2-2-2 0-4-1h-2c-1-1-1-2-2-3-1 0-1 0-2 1h0v-2l-1 1c-2 0-1-1-2-2-1 1-1 1-2 1l-1-1s-1 1-2 0l1-1h-1l-1-1 1-2h1c2 1 3 3 6 2l1 1h-1l1 1 1-1 3 2c1 0 2 1 3 1v-1h1c1-1 1-2 3-2l1 1v-1c1-2 1-2 3-4h0v-2l3-1c5 1 6 1 10-1h2l2-1h5v1l2 1v2h0c0-1 1-3 1-4z" class="L"></path><path d="M541 899c1-1 2-1 4 0v2 2h0l-2-1h-1v1c-1 0 0-1-2-1h0c-1 1-2 1-3 2h-1v-3h0v-1l1-1h4z" class="R"></path><path d="M561 897c1 0 3 0 4 1l-3 3h-2c-2 1-3 1-5 2l-2-1h-2l-1-2c-1 0-1 0-2 1v1c-1 0-2-1-3-1v-2c0-1 0-1 1-1 1-1 1 0 2 0v1h1v-1h2l1 1c3 0 6 0 9-1v-1z" class="h"></path><path d="M522 884h5v1l2 1v2c0 2-1 3-1 4-1 0-1 1-2 1h-1l-2 2c0 1 1 1 2 2h1v1 1h1 4l2 1v-1h2v1h1v1l-1 1h-2l-2 2-1-1c1 0 1 0 1-1h-2v-1h-2l-1-1c-1 0-1 1-2 1h0l-1-1-1 1-1-1h-1l-1-2-1 1-1-1h-1-1-2c-1-1-2 0-3-1h0c-1 0-2 1-4 1v-1c-1-1-3-1-4-1-2 0-3-1-4-1 1-1 1-2 3-2l1 1v-1c1-2 1-2 3-4h0v-2l3-1c5 1 6 1 10-1h2l2-1z" class="O"></path><path d="M502 893c1-1 2-1 3-2l1 1c0 1 1 2 2 3v1h-2v-1c-2 0-3 0-4-1v-1z" class="P"></path><path d="M506 892h2c2 0 3-1 4-2 1 1 2 1 3 0l4 5-2 1c-1-1-2-1-3-1h-1l-1 1h-4 0v-1c-1-1-2-2-2-3z" class="D"></path><path d="M508 895l1-2h3l2 2h-1l-1 1h-4 0v-1z" class="K"></path><path d="M508 886c5 1 6 1 10-1l1 2v2l-4 1c-1 1-2 1-3 0-1 1-2 2-4 2h-2l-1-1c-1 1-2 1-3 2 1-2 1-2 3-4h0v-2l3-1z" class="V"></path><path d="M522 884h5v1l2 1v2c0 2-1 3-1 4-1 0-1 1-2 1h-1l-2 2c0 1 1 1 2 2h1v1 1h0-1c-1-1-1 0-2 0h0l-1-1h-2v-1h-2l-1-1 2-1-4-5 4-1v-2l-1-2h2l2-1z" class="K"></path><path d="M524 890l1 3-2 2v-3l1-2zm-9 0l4-1 2 1-1 1h-1l-1 1c1 1 2 1 3 2l-2 2v-1l-4-5z" class="G"></path><path d="M527 885l2 1v2c0 2-1 3-1 4-1 0-1 1-2 1h-1l-1-3c0-1 1-2 1-3 1 0 2 0 3-1l-1-1z" class="D"></path><path d="M522 884h5v1l1 1c-1 1-2 1-3 1s-2 1-2 1v2h-2l-2-1v-2l-1-2h2l2-1z" class="b"></path><path d="M518 885h2l2 2c0 1 0 1 1 1v2h-2l-2-1v-2l-1-2z" class="D"></path><path d="M530 884c0-2 0-2 1-3l2 2v1h5c1 1 4 1 5 1h2c1 1 1 2 3 2 1 0 1 1 2 1 2 2 5 1 8 2h1c1 1 2 2 4 3v1h-2l-1-1c-1 0-2 0-2 2 1 1 2 1 3 1v1 1c-3 1-6 1-9 1l-1-1h-2v1h-1v-1c-1 0-1-1-2 0-1 0-1 0-1 1-2-1-3-1-4 0h-4l-1 1h-1v-1h-2v1l-2-1h-4-1v-1-1h-1c-1-1-2-1-2-2l2-2h1c1 0 1-1 2-1 0-1 1-2 1-4h0c0-1 1-3 1-4z" class="b"></path><path d="M540 891h3l1 1v2h-1l-3 2c-2-1-4-3-5-4h3c1 1 1 1 2 1v-2z" class="X"></path><path d="M543 891l1 1v2h-1c-1-1-1-1-1-2l1-1z" class="m"></path><path d="M529 888h6l1 1 4 2v2c-1 0-1 0-2-1h-3c-3-1-5 0-7 0 0-1 1-2 1-4h0z" class="W"></path><path d="M525 893h1c0 1 1 2 2 3 2 0 3-1 4-1h1l-1 1c-1 1-2 1-2 1 1 1 2 1 4 1l1-1c1 0 1 1 2 1 1 1 3 0 4 1h-4l-1 1h-1v-1h-2v1l-2-1h-4-1v-1-1h-1c-1-1-2-1-2-2l2-2z" class="Q"></path><path d="M530 884c0-2 0-2 1-3l2 2v1h5c1 1 4 1 5 1h2c1 1 1 2 3 2 1 0 1 1 2 1 2 2 5 1 8 2h1c1 1 2 2 4 3v1h-2l-1-1c-1 0-2 0-2 2 1 1 2 1 3 1-2 0-4 1-6 0h-1l-1-1c-2-2-6-2-9-1v-2l-1-1h-3l-4-2-1-1h-6c0-1 1-3 1-4z" class="d"></path><path d="M530 884h1c1 1 1 2 2 3h1v-2c2 1 1 2 2 3v1l-1-1h-6c0-1 1-3 1-4z" class="M"></path><path d="M536 888l2-1h0c3 3 8 2 12 3h2l1 1c1 0 3 1 4 1l1 1c-1 1-1 2-2 3h-1-1l-1-1c-2-2-6-2-9-1v-2l-1-1h-3l-4-2v-1z" class="e"></path><path d="M411 860h1c3 0 7-1 10-1 3 1 7 2 10 4l12 5c2 1 3 1 5 0h1c2 0 5-1 7 0 3 1 6 2 8 4l1 2 2 3 4 2c-1 2-1 3-3 4 0 1 0 1 1 2l-2 2 1 1v2l2 2-1 1c-1-1-2-1-4-1-1 0-1 0-2-1h-1c-1 1-2 1-3 1h-3-1c-2-1-4-2-7-2-1 1-3 0-4 0h-2v1c-2 1-3 1-4 1-3-1-5-5-9-6-1 0-1 0-2-1l1-1-1-2c0-2-2-4-3-6l-2-1v-1c-2-1-5-3-7-4l1-1c-3-1-5-2-8-3v-1l-2-1v-1-1c1-1 2-1 3-1l1-1z" class="N"></path><path d="M450 871c0-1 1-1 2-2l1 1-1 2h2l1-1c1 2 1 2 3 3-2 1-2 1-2 3-1-1-3-4-5-5h-1v-1z" class="K"></path><path d="M450 889h0c2 0 3-1 5-2v1 1c1 1 1 1 3 1l3-1 4-1 3-1 1 1v2l2 2-1 1c-1-1-2-1-4-1-1 0-1 0-2-1h-1c-1 1-2 1-3 1h-3-1c-2-1-4-2-7-2h0-1l-1-1h3z" class="e"></path><path d="M458 874l1 1c1 1 2 2 3 4l1 2 1 1-1 1c0 1 0 1 1 2s1 2 1 3l-4 1c0-1 0-2 1-2v-1h-3 0-2c1-2 1-3 1-5-1-2-1-3-2-4 0-2 0-2 2-3z" class="O"></path><path d="M459 875c1 1 2 2 3 4l1 2h-3c0-1-1-2-2-3v-1l1-2z" class="K"></path><path d="M440 878h1c0 2 0 3 1 4h0c-1 1-1 1-1 2 1 1 2 1 3 1 1 2 0 2 2 4l2-2 2 2h-3l1 1h1 0c-1 1-3 0-4 0h-2v1c-2 1-3 1-4 1-3-1-5-5-9-6-1 0-1 0-2-1l1-1v-3l2 2h2c1 1 1 1 2 0v2c1 1 1 1 3 1 0-1 1-1 1-2v-4l1-2z" class="k"></path><path d="M457 868c3 1 6 2 8 4l1 2 2 3 4 2c-1 2-1 3-3 4 0 1 0 1 1 2l-2 2-3 1c0-1 0-2-1-3s-1-1-1-2l1-1-1-1-1-2c-1-2-2-3-3-4l-1-1c-2-1-2-1-3-3l-1 1c0-2-1-3 0-4h3z" class="b"></path><path d="M462 879l1-3-2-2 1-1 2 1h0c1 1 1 3 1 4 1 1 1 1 2 1l1-2 4 2c-1 2-1 3-3 4 0 1 0 1 1 2l-2 2-3 1c0-1 0-2-1-3s-1-1-1-2l1-1-1-1-1-2z" class="P"></path><path d="M464 885c1-1 2-3 3-4l1-1c0 1 1 2 1 3s0 1 1 2l-2 2-3 1c0-1 0-2-1-3z" class="L"></path><path d="M411 860h1c3 0 7-1 10-1 3 1 7 2 10 4l12 5c2 1 3 1 5 0h1c2 0 5-1 7 0h-3c-1 1 0 2 0 4h-2l1-2-1-1c-1 1-2 1-2 2l-3-1-1 1c1 0 1 0 2 1 2 1 4 2 6 4 1 2 2 3 2 4 0 2 0 4-1 6-2 0-3-1-4-2-1 0-2 0-2 1-1-1-1-1-2-1l-1-1 1-2-1-1-1 2h-1v-2c1-1 1-1 1-2l-2-1h-1l2-2c-1-1-3-2-4-4h0-1c-1 0-1-1-2-2h-2c-2-2-3-2-5-2l-2-2h0-2l-4-2-1-1c-3-1-4-1-6 1h0c-3-1-5-1-8-1 1-1 2-1 3-1l1-1z" class="B"></path><path d="M452 880v-2l2-2c1 2 2 3 2 4-2 1-3 1-4 0z" class="I"></path><path d="M437 869l4 1c2 1 2 3 4 4h1 1v1l-2 1c0 1 0 1 1 1l2 2s0 1 1 1v2h1l2-2h0c1 1 2 1 4 0 0 2 0 4-1 6-2 0-3-1-4-2-1 0-2 0-2 1-1-1-1-1-2-1l-1-1 1-2-1-1-1 2h-1v-2c1-1 1-1 1-2l-2-1h-1l2-2c-1-1-3-2-4-4h0-1c-1 0-1-1-2-2z" class="h"></path><path d="M415 863c2-2 3-2 6-1l1 1 4 2h2 0l2 2c2 0 3 0 5 2h2c1 1 1 2 2 2h1 0c1 2 3 3 4 4l-2 2h1l2 1c0 1 0 1-1 2-1-1-2-1-3-2h-1l-1 2v4c0 1-1 1-1 2-2 0-2 0-3-1v-2c-1 1-1 1-2 0h-2l-2-2v3l-1-2c0-2-2-4-3-6l-2-1v-1c-2-1-5-3-7-4l1-1-8-3v-1l-2-1v-1-1c3 0 5 0 8 1h0z" class="Z"></path><path d="M422 863l4 2-2 1c-1 0-3-1-3-2h-1l2-1z" class="h"></path><path d="M415 863c2-2 3-2 6-1l1 1-2 1c-2-1-3-1-5-1z" class="G"></path><path d="M416 865c2 1 5 1 7 3v2h-1c-2-1-3-2-5-3 0 0-1-1-1-2z" class="Q"></path><path d="M409 865c2 0 5-1 7 0 0 1 1 2 1 2 2 1 3 2 5 3l-5-1-8-3v-1z" class="j"></path><path d="M423 868l8 3c1 1 2 2 3 2 1 1 3 1 5 3 0 1 0 2 1 2l-1 2v4c0 1-1 1-1 2-2 0-2 0-3-1v-2c-1 1-1 1-2 0h-2l-2-2v3l-1-2c0-2-2-4-3-6l-2-1v-1c-2-1-5-3-7-4l1-1 5 1h1v-2z" class="Z"></path><path d="M434 873c1 1 3 1 5 3 0 1 0 2 1 2l-1 2h-3v-1c0-1 1-1 1-2l-1-2-1 1c0 1-1 1 0 3h0-1c0-1-1-2-2-3 0-2 0-2 2-3z" class="I"></path><path d="M423 868l8 3c1 1 2 2 3 2-2 1-2 1-2 3-1 0-2-1-3-2-1 0-1 0-2-1-1 0-1-1-2-1-1-1 0-2-1-3l-1 1v-2z" class="B"></path><path d="M423 874l1-1v1h2 1c1 2 1 2 2 3 2 1 2 3 4 3h2 1 3v4c0 1-1 1-1 2-2 0-2 0-3-1v-2c-1 1-1 1-2 0h-2l-2-2v3l-1-2c0-2-2-4-3-6l-2-1v-1z" class="N"></path><path d="M520 847c1 0 1 0 2 1l1 1 2-1h0c1 1 1 1 2 1l1 1c1 1 2 0 3 0h0c3 1 4 1 7 0l-1 2c3 1 6 3 9 3 2 1 4 2 5 2 3 1 8 3 9 4h1c3 1 6 2 10 3h1 4l7 1c0 2-1 2-2 3 2 1 4 1 6 1l1 2h1 2s1 1 2 1h1c1 0 3 1 4 1 4 2 7 2 11 4 2 1 3 2 5 3 0 0 1 1 2 0v-1c1 1 1 1 2 1 1 1 2 1 2 2h1c-1 0-2 1-3 0h-2c1 1 4 2 6 4l1 1v1l-1 1v1c3 0 6-1 9-1 2-1 4 1 5 2l1 1h0 0c2 1 4 3 6 4l-2 2c-1 0-2 0-2-1l-2 2c0 1 0 1-1 2h0c-1-1-1 0-1-1-1 1-2 2-3 2l-1-1h-1c0 1-1 1-1 1-2 0-3 0-4 1h-2l-1 1h-1l1-1h-2l-1 1-1-1h-1-2c0 1-1 1-1 1-1 1-1 2-1 3-2-1-4-1-6-1l-2 2h-1c-1 1-1 1-2 1h-2v1c-1-1-1-1-2-1h-4v-1l1-1v-1h-3l-1 2v-2c-1-1-4-3-6-3h-1c-2-1-6-1-8 0h0-2c-2-2-7-4-9-5-1-1-3-1-4-1v-1c-1 0-2 0-3-1 0-2 1-2 2-2l1 1h2v-1c-2-1-3-2-4-3h-1c-3-1-6 0-8-2-1 0-1-1-2-1-2 0-2-1-3-2h-2c-1 0-4 0-5-1s-1-1-2-1l-3-3h-1s-1 1-1 0v-1c0-1-2-2-3-3 2 0 3 0 4 1l1 2h2l1 1h2l-1-2h1c2 0 3 0 4-2h3l-3-1v-1c0-1-1-1-2-2h0v-1c5-2 9-4 15-3v-1h0c-3-1-5-1-7-1l-2 1c-2 0-3-1-5 0h-2c-1-2-3-3-5-4s-3-1-4-1l-2-2c-1 0-2 1-3 1h0-1l-2-1c0-1 0-2 1-3h3 1c1 0 1-1 1-1-2-2-4-4-5-6h0l-2-1-1-2z" class="j"></path><path d="M611 895c-1-1-1-1-1-3h2c0 1 0 4 1 4h0c1 0 1 1 2 1h1c0 1 0 1 1 1l1-1 1 1v1c1-1 2-1 3-1v2l-1 1v-1h-1c-1 0-2 0-3-1h0l-2 1c-2-1-3-2-3-4h-1v-1z" class="O"></path><path d="M637 892c2 1 4 3 6 4l-2 2c-1 0-2 0-2-1l-2 2h-1v-1l-2 2c-1-1-1 0-1-1-4 0-5 1-8 1l-1 1c-1-1-1-1-2-1v-2c2 0 3-1 5 0h1c2 0 1-1 3-2 0 1 0 1 1 2v-2c1-1 1 0 2 0s3-1 3-1v-3z" class="R"></path><path d="M631 889c2-1 4 1 5 2l1 1h-1-1v1h-2v1 1c-2 0-4 1-6 1h-2-2-1l-1 1c-3-2-6-2-9-3 1-1 5-1 7-1v-2c1 2 0 2 1 4 2 0 6-1 9-2l2-2v-2z" class="E"></path><path d="M610 887c1 1 1 1 3 1 3 1 5 1 7 2h2c3 0 6-1 9-1v2l-2 2c-3 1-7 2-9 2-1-2 0-2-1-4-2 1-4 1-6 1v-1h-1-1c-2 0-2 0-4-2h-1v-1c2 0 2 0 4-1z" class="F"></path><path d="M578 879c2 0 3-1 4-2 3 4 5 6 10 7l4 1v4h-1 2v-1h3v1h0c2 1 4 2 5 4 0 1 1 1 1 3l-1 1h-1c-1 0-2-2-3-2h-2c-3 0-4 0-6-2h0l-2 1v-2c-1 0-2-1-4-1-1 0-3 0-5-1-1-1-1-1-3-1l-1-1h-1c-2-1-3-2-4-2h-3l-1-2h-1v1l-1-2h-1-3l-1-1v1c-2 0-2-1-3-1-2 0-1 0-2-1h-2v-1h4c1 0 1 0 2 1h3l3 1c1-2 1-2 1-4h1v3c2 1 3 1 5 1 1-1 3-2 4-3z" class="I"></path><path d="M578 879c2 0 3-1 4-2 3 4 5 6 10 7l4 1v4h-1l-14-5h0c-2-1-4-1-7-2 1-1 3-2 4-3z" class="H"></path><path d="M555 880h4c1 0 1 0 2 1h3l3 1v1c1-1 1-1 2-1v1h1c1 0 2 1 3 2 1 0 1 0 2-1h2v1c0 1 0 0 1 1h0 2 1s1 1 2 1 2 1 3 2l1 1c1 0 2 1 4 1 0 0 1 0 2 1h0 3c1 1 2 1 4 1h5c0 1 1 1 1 3l-1 1h-1c-1 0-2-2-3-2h-2c-3 0-4 0-6-2h0l-2 1v-2c-1 0-2-1-4-1-1 0-3 0-5-1-1-1-1-1-3-1l-1-1h-1c-2-1-3-2-4-2h-3l-1-2h-1v1l-1-2h-1-3l-1-1v1c-2 0-2-1-3-1-2 0-1 0-2-1h-2v-1z" class="D"></path><path d="M540 871c5-2 9-4 15-3l5 2c4-1 6-2 10 0l11 6 1 1c-1 1-2 2-4 2-1 1-3 2-4 3-2 0-3 0-5-1v-3h-1c0 2 0 2-1 4l-3-1h-3c-1-1-1-1-2-1h-4l-2-1h0-4 0l-2-1-2-2h0l-3-1v-1c0-1-1-1-2-2h0v-1z" class="F"></path><path d="M563 874l4 1c1 1 2 0 4 1h1c3 0 6 1 9 0l1 1c-1 1-2 2-4 2-1 1-3 2-4 3-2 0-3 0-5-1v-3h-1c-1-1-3-1-5-1h-1l1-3z" class="D"></path><path d="M569 878l9 1c-1 1-3 2-4 3-2 0-3 0-5-1v-3z" class="F"></path><path d="M542 875h3c1 1 1 1 3 2h0 1c1 0 2 0 3 1h0c1-1 2-2 3-2h0c-1-1 0-1-1-1h-2c-3-1-6 0-9-1v-1c0-1 0-1 1-2 2 0 2 0 4 1s4 0 6 0c3 1 5 2 8 2h1l-1 3h1c2 0 4 0 5 1 0 2 0 2-1 4l-3-1h-3c-1-1-1-1-2-1h-4l-2-1h0-4 0l-2-1-2-2h0l-3-1z" class="C"></path><path d="M548 872c2 1 4 0 6 0 3 1 5 2 8 2l-2 2c-2-2-8-2-11-2-2 0-4 0-5-1v-1h4z" class="B"></path><path d="M545 876h0l2 2 2 1h0 4 0l2 1v1h2c1 1 0 1 2 1 1 0 1 1 3 1v-1l1 1h3 1l1 2v-1h1l1 2h3c1 0 2 1 4 2h1l1 1c2 1 2 2 4 3h1 0c1 0 2 0 3 1h0c1 0 2 1 2 1 2 1 2 0 3 2h1v1h2 1 1c2 0 3 1 4 1 1 1 1 1 2 1s3 0 4-1c0-1 0-2-1-3l1-1v1c2 1 2 1 4 0v1h1c0 2 1 3 3 4l2-1h0c1 1 2 1 3 1h1v1l1-1c1 0 1 0 2 1l1-1c3 0 4-1 8-1 0 1 0 0 1 1l2-2v1h1c0 1 0 1-1 2h0c-1-1-1 0-1-1-1 1-2 2-3 2l-1-1h-1c0 1-1 1-1 1-2 0-3 0-4 1h-2l-1 1h-1l1-1h-2l-1 1-1-1h-1-2c0 1-1 1-1 1-1 1-1 2-1 3-2-1-4-1-6-1l-2 2h-1c-1 1-1 1-2 1h-2v1c-1-1-1-1-2-1h-4v-1l1-1v-1h-3l-1 2v-2c-1-1-4-3-6-3h-1c-2-1-6-1-8 0h0-2c-2-2-7-4-9-5-1-1-3-1-4-1v-1c-1 0-2 0-3-1 0-2 1-2 2-2l1 1h2v-1c-2-1-3-2-4-3h-1c-3-1-6 0-8-2-1 0-1-1-2-1-2 0-2-1-3-2h-2c-1 0-4 0-5-1s-1-1-2-1l-3-3h-1s-1 1-1 0v-1c0-1-2-2-3-3 2 0 3 0 4 1l1 2h2l1 1h2l-1-2h1c2 0 3 0 4-2h3z" class="N"></path><path d="M562 883v-1l1 1h3 1l1 2v-1h1l1 2h3c1 0 2 1 4 2h1l1 1c2 1 2 2 4 3h1 0c1 0 2 0 3 1h0c1 0 2 1 2 1 2 1 2 0 3 2h1v1h2c-1 1-2 1-3 2v-2h0c-3 0-6-1-8-2-2 0-2 0-3-1h-1l-1 1-1-1 1-2c-2 0-2 1-3 1 0-1 1-1 0-2l-2 1-1-1v-1c-1-1-2-1-3-1v-1l-2-2h-1-2-1l-1-1h-1v-2z" class="G"></path><path d="M561 896c-1 0-2 0-3-1 0-2 1-2 2-2l1 1h2 2c1 2 1 2 2 2h2l-1-2h1c2 1 5 3 7 4l2-1v1h4c3 1 5 2 8 2 2 0 3 0 4 1h2c2 1 2 1 4 1 0 0 1 0 1-1 1 1 0 1 1 2 1-1 3-1 3-1s0-1 1-1h3c1-1 1-2 1-3 2 2 0 4 3 4h1 1c1 1 2 1 3 1h-1-2c0 1-1 1-1 1-1 1-1 2-1 3-2-1-4-1-6-1l-2 2h-1c-1 1-1 1-2 1h-2v1c-1-1-1-1-2-1h-4v-1l1-1v-1h-3l-1 2v-2c-1-1-4-3-6-3h-1c-2-1-6-1-8 0h0-2c-2-2-7-4-9-5-1-1-3-1-4-1v-1z" class="k"></path><path d="M593 904c2 0 3 0 5 1h0 4 3l1-2 1 1h1c1 0 1 1 1 1 1 0 1 0 2-1h3c-1 1-1 2-1 3-2-1-4-1-6-1l-2 2h-1c-1 1-1 1-2 1h-2v1c-1-1-1-1-2-1h-4v-1l1-1v-1l-2-2z" class="S"></path><path d="M561 896c-1 0-2 0-3-1 0-2 1-2 2-2l1 1h2 2c1 2 1 2 2 2h2l1 2c2 1 5 2 6 2h1 4c3 2 8 2 11 3l1-1v2l2 2h-3l-1 2v-2c-1-1-4-3-6-3h-1c-2-1-6-1-8 0h0-2c-2-2-7-4-9-5-1-1-3-1-4-1v-1z" class="M"></path><path d="M545 876h0l2 2 2 1h0 4 0l2 1v1h2c1 1 0 1 2 1 1 0 1 1 3 1v2h1l1 1h1 2 1l2 2c-3 1-4 0-7 0 2 1 3 2 4 3 2 0 3 2 5 3 2 0 2 1 3 1h2c0 1 0 1 1 1 1 1 3 0 4 0 1 1 0 1 2 1s5 1 6 3c-3 0-5-1-8-2h-4v-1l-2 1c-2-1-5-3-7-4h-1l1 2h-2c-1 0-1 0-2-2h-2v-1c-2-1-3-2-4-3h-1c-3-1-6 0-8-2-1 0-1-1-2-1-2 0-2-1-3-2h-2c-1 0-4 0-5-1s-1-1-2-1l-3-3h-1s-1 1-1 0v-1c0-1-2-2-3-3 2 0 3 0 4 1l1 2h2l1 1h2l-1-2h1c2 0 3 0 4-2h3z" class="Y"></path><path d="M545 885h1 0l2 2c1 0 1-1 2 0h2l5 2h2c2 0 5 3 7 3 1 1 2 1 2 2l1 2h-2c-1 0-1 0-2-2h-2v-1c-2-1-3-2-4-3h-1c-3-1-6 0-8-2-1 0-1-1-2-1-2 0-2-1-3-2z" class="c"></path><path d="M545 876h0l2 2 2 1h0 4 0l2 1v1h2c1 1 0 1 2 1 1 0 1 1 3 1v2h1l1 1h1 2 1l2 2c-3 1-4 0-7 0 0 0-1 0-1-1-2 0-3 0-5-1h-3c-2-2-4-2-6-2-1 0-1 0-2-1v1h-3l-1-1h-2c-1-1-1-2-2-4v1l-1-2h1c2 0 3 0 4-2h3z" class="a"></path><path d="M545 880h1c1 0 2 1 3 1s1 0 3 1c0-1 0-1 1-1h2 2c1 1 0 1 2 1-1 1-2 2-2 3l-3-1v-1l-1 1-1-1h-1-1-2c-1-1-1-2-2-2l-1-1z" class="R"></path><path d="M545 876h0l2 2 2 1h0 4 0l2 1v1h-2c-1 0-1 0-1 1-2-1-2-1-3-1s-2-1-3-1h-1-5-1l-1-1v1l-1-2h1c2 0 3 0 4-2h3z" class="K"></path><path d="M520 847c1 0 1 0 2 1l1 1 2-1h0c1 1 1 1 2 1l1 1c1 1 2 0 3 0h0c3 1 4 1 7 0l-1 2c3 1 6 3 9 3 2 1 4 2 5 2 3 1 8 3 9 4h1c3 1 6 2 10 3h1 4l7 1c0 2-1 2-2 3 2 1 4 1 6 1l1 2h1 2s1 1 2 1h1c1 0 3 1 4 1 4 2 7 2 11 4 2 1 3 2 5 3 0 0 1 1 2 0v-1c1 1 1 1 2 1 1 1 2 1 2 2h1c-1 0-2 1-3 0h-2c1 1 4 2 6 4l1 1v1l-1 1v1h-2c-2-1-4-1-7-2-2 0-2 0-3-1-2 1-2 1-4 1v1h1l-1 1-1 1c-1-1-2-1-3-2l-2-1h-3v1h-2 1v-4l-4-1c-5-1-7-3-10-7l-1-1-11-6c-4-2-6-1-10 0l-5-2v-1h0c-3-1-5-1-7-1l-2 1c-2 0-3-1-5 0h-2c-1-2-3-3-5-4s-3-1-4-1l-2-2c-1 0-2 1-3 1h0-1l-2-1c0-1 0-2 1-3h3 1c1 0 1-1 1-1-2-2-4-4-5-6h0l-2-1-1-2z" class="d"></path><path d="M580 869c1 0 4 1 5 2l1 2h1c-2 0-4 0-6-1h-2c1-1 0-1 1-2v-1z" class="Y"></path><path d="M571 864h1 4l7 1c0 2-1 2-2 3h0-1c-3-1-6-3-9-4z" class="q"></path><path d="M591 874c4 0 8 1 12 2h0l1 1-1 3h-1l-2-1c-2 0-2 0-3-1v-1l-1-1-1 1c-2 0-2-1-4-2v-1z" class="P"></path><path d="M603 876l1 1-1 3h-1l-2-1 3-3z" class="D"></path><path d="M569 865c4 1 7 3 11 4v1c-1 1 0 1-1 2h2v2c-2-1-3-2-5-3l-6-3c1-1 0-2-1-3z" class="N"></path><path d="M581 872c2 1 4 1 6 1 0 0 4 0 4 1v1c2 1 2 2 4 2l1-1 1 1v1l-2 1c-2 0-4 0-5-2-1 0-1-1-2-1l-1 1c-2-1-2-2-3-2l-1-1h-2 0v-2z" class="K"></path><path d="M581 874h2l1 1c1 0 1 1 3 2l1-1c1 0 1 1 2 1 1 2 3 2 5 2l2-1c1 1 1 1 3 1l2 1h1l1-3c3 1 6 2 8 4 1 0 3 1 4 1 1 1 4 2 6 4l1 1v1l-1 1v1h-2c-2-1-4-1-7-2-2 0-2 0-3-1h0v-1c-1-1-2-2-4-2 0-1 0-1-1-1s-1-1-3 0h0l-1-1c-1 1-2 1-2 1-2-1-2-2-4-2-1 0-2 0-2-1h-6-1l-5-6z" class="T"></path><path d="M604 877c3 1 6 2 8 4 1 0 3 1 4 1 1 1 4 2 6 4l1 1v1l-1 1v1h-2c-2-1-4-1-7-2 1 0 2 0 3-1l-1-1c-1-1-2-1-3 0h-1v-1-1c-2-2-4-2-5-3s-2-1-3-1l1-3z" class="P"></path><path d="M548 866l-1-1c1-1 1-1 2-1l1 1h2c0-1 0-1 1-1h2c1 0 2 0 3 1h0l2-1 10 4 6 3c2 1 3 2 5 3h0l5 6h1 6c0 1 1 1 2 1 2 0 2 1 4 2 0 0 1 0 2-1l1 1h0c2-1 2 0 3 0s1 0 1 1c2 0 3 1 4 2v1h0c-2 1-2 1-4 1v1h1l-1 1-1 1c-1-1-2-1-3-2l-2-1h-3v1h-2 1v-4l-4-1c-5-1-7-3-10-7l-1-1-11-6c-4-2-6-1-10 0l-5-2v-1h0c-3-1-5-1-7-1z" class="k"></path><path d="M600 885c4-1 6 0 10 2h0c-2 1-2 1-4 1v1h1l-1 1-1 1c-1-1-2-1-3-2l-2-1h-3v1h-2 1v-4h4z" class="I"></path><path d="M596 885h4 4c0 2 0 2-1 3l-1 1-2-1h-3v1h-2 1v-4z" class="E"></path><path d="M520 847c1 0 1 0 2 1l1 1 2-1h0c1 1 1 1 2 1l1 1c1 1 2 0 3 0h0c3 1 4 1 7 0l-1 2c3 1 6 3 9 3 2 1 4 2 5 2 3 1 8 3 9 4v1l9 3c1 1 2 2 1 3l-10-4-2 1h0c-1-1-2-1-3-1h-2c-1 0-1 0-1 1h-2l-1-1c-1 0-1 0-2 1l1 1-2 1c-2 0-3-1-5 0h-2c-1-2-3-3-5-4s-3-1-4-1l-2-2c-1 0-2 1-3 1h0-1l-2-1c0-1 0-2 1-3h3 1c1 0 1-1 1-1-2-2-4-4-5-6h0l-2-1-1-2z" class="r"></path><path d="M545 865c-1 0-2 1-3 1-1-1-1-2-1-2h-1l-1-1h0 1l1-2h1l1 2 2 1v1z" class="Z"></path><path d="M528 856l4 3h0c-1 1-2 1-4 1-1 0-2 1-3 1h0-1l-2-1c0-1 0-2 1-3h3 1c1 0 1-1 1-1zm20 3c2 1 3 1 4 2 2 0 3 1 5 2 1 0 2 1 3 1l-2 1h0c-1-1-2-1-3-1h-2c-1 0-1 0-1 1h-2l-1-1c-1 0-1 0-2 1l1 1-2 1-1-2v-1l-2-1c1 0 2-1 3-2l5 2h1l-1-1c-1-1-2-1-2-2l-1-1z" class="q"></path><path d="M520 847c1 0 1 0 2 1l1 1 2-1h0c1 1 1 1 2 1l1 1c1 1 2 0 3 0h0c3 1 4 1 7 0l-1 2c3 1 6 3 9 3 2 1 4 2 5 2 3 1 8 3 9 4v1l9 3c1 1 2 2 1 3l-10-4c-1 0-2-1-3-1-2-1-3-2-5-2-1-1-2-1-4-2 0 0-1 0-2 1h0l-8-2h-1c-1-1-1-1-2-1s-2 0-3-1h-1c-2-2-5-5-7-6h-1 0l-2-1-1-2z" class="h"></path><path d="M531 850c3 1 4 1 7 0l-1 2-1 1 1 1c1 0 1 0 2 1 1 0 1 0 1 1h-5c-2-2-3-3-4-6h0z" class="V"></path><path d="M406 762h0l2 1c0 1 1 1 1 2 1 1 1 2 1 3l-1 1v2c2 1 6-1 10 1v-1l1 1c-1 0-2 0-3 1 2 1 2 1 3 2 0 1 1 1 1 1 3-1 6-3 9-4 1 0 2 2 3 2l1 1c1 1 3 2 4 3 2 1 3 2 5 3l2 1 1-1h1 3l9-3 2-1-1-1c1-1 3-2 4-3 1 0 1-1 2-2 1 0 1-1 2-1 2 1 4 3 6 5 1 2 4 4 5 6l2 2c0 1 0 2-1 2 0 2 1 3 2 4 0 1-1 2-1 3s1 2 2 3l-2 2 1 2v1c-1 2-1 3-1 4l3 2h0c1 2 2 3 4 4h-1c3 2 5 4 7 7 1 0 3 1 4 2 2 1 4 1 5 2 2 1 3 3 4 4 3 3 8 6 12 8 2 1 3 2 5 3 5 2 10 3 14 6v1l1 3-1 3v1c-3 1-4 1-7 0h0c-1 0-2 1-3 0l-1-1c-1 0-1 0-2-1h0l-2 1-1-1c-1-1-1-1-2-1l1 2 2 1h0c1 2 3 4 5 6 0 0 0 1-1 1h-1-3c-1 1-1 2-1 3l2 1h1 0c1 0 2-1 3-1l2 2c1 0 2 0 4 1s4 2 5 4h2c2-1 3 0 5 0l2-1c2 0 4 0 7 1h0v1c-6-1-10 1-15 3v1h0c1 1 2 1 2 2v1l3 1h-3c-1 2-2 2-4 2h-1l1 2h-2l-1-1h-2l-1-2c-1-1-2-1-4-1 1 1 3 2 3 3v1c0 1 1 0 1 0h1l3 3c1 0 1 0 2 1h-5v-1l-2-2c-1 1-1 1-1 3 0 1-1 3-1 4h0v-2l-2-1v-1h-5l-2 1h-2c-4 2-5 2-10 1l-3 1v2h0c-2 2-2 2-3 4v1l-1-1c-2 0-2 1-3 2h-1v1c-1 0-2-1-3-1l-3-2-1 1-1-1h1l-1-1c-3 1-4-1-6-2h-1-3v-1h-4v-1c1 0 2 0 3-1v-1c-1 0-1 0-2-1 0-2 0-2-1-2l-2 2-1-1-2 1c-1-1-1-1-1-2 2-1 2-2 3-4l-4-2-2-3-1-2c-2-2-5-3-8-4-2-1-5 0-7 0h-1c-2 1-3 1-5 0l-12-5c-3-2-7-3-10-4-3 0-7 1-10 1h-1l-1 1h-10l-4 1h1c1 0 2 1 2 2v1c-1 1-1 2-3 2s-4-1-7 0h0l-3 1v1c-2 0-3 0-4 2-1 0-2 1-4 1-1 1-2 2-4 2l-1 1c-1 1-2 1-3 1h0-2-1-2c-2 1-4 1-5 1h-3 0l-1-1-1 1-2-2-1 1c0-1-1-1-2-2-1 0-1-1-2-2h3l1-1c0 1 1 1 2 1l1-1c0-1-1-2-1-3-2 0-2 0-3-1v-1h2l-1-1h-1c-1-1-4-3-5-4l-3-1c0-2-7-6-9-7-1 0-1-1-2-1 0-1-1-3-2-4h-2c-2-1-5-2-6-3l-4-2-10-3h-2l-3-1h-1c0-1 1 0 0-1l-1 1h-1v-2h0c-1 1-1 1-2 1-1-1 0-1-1-2l-2 1h-1c-1-1-2-1-2-2h-1-2l-1-1h0-2l-2-1v-2c1 0 2-1 3-1l1 1c1 1 1 1 2 0h0v-1c1 0 2 0 4 1h1 0 1 0l1-2-1-1c0 1-1 1-2 2l1-2h0-2l-1-1s-1 1-2 1l-1-1c-2 0-2-1-4-1v-1h-1l-2-1h0-1v-1h-1-1v-1l4-4-2-2c-2-1-2-1-4-3l-1-1 1-1-1-1h-1-1v-1c-1-2 0-1-1-1s-2-2-3-2h3 0 3c2-1 3 0 5 1h2l1-2c0-1-1-1-2-1l-3-1-3-3c1 0 1 0 2 1h2 2 1 1 2 2c1-1 2-1 4-2l1 1c1 0 2-1 2-2 2 0 3 1 4 2h0c0-1 1-1 2-2l-1-2h0 1 2c2 1 5-1 8-2-2-1-2-1-3-2h-1l-1-1c2 0 2 0 3-1h3l1-1 9 2c3 0 6 0 9 1h3 4c4-1 9-1 14 0 4 1 6 3 10 5l1 1c2 3 3 6 4 9h-1c-1 0-1 0-2-1l-1 1s1 1 1 2c3 0 5 1 8 1v-1h2 4c3 0 6 0 8 1h0 3s0-1 1-1 2 0 3 1h1c1-1 2-1 3-1 1 1 1 0 2 0v-1-4-10-1-3h0v-1c0-1 0-2 1-2l2-1-1-1c-1-1-1-1-2-3v-1c1-2 1-6 0-7 0-2 0-4 1-7l-2-1 2-1c1-1 3-1 4-1l2-2z" class="O"></path><path d="M364 845h4c-2 2-4 2-7 2v-1l3-1z" class="R"></path><path d="M479 876c1-1 3-4 5-5l2 1v1c-2 1-3 2-4 4-1-1-3-1-3-1z" class="L"></path><path d="M368 853c-1 0-3-1-4-1v-1l1-1h6c-2 1-2 0-3 2 2 1 5-2 6 2h0c-2 0-4 0-6-1z" class="P"></path><path d="M340 841c2 0 3 1 5 2h5 1c1 1 0 1 1 1l1 2c-1 0-2 0-3-1h0-3s-1-1-2-1h-3l-2-3z" class="L"></path><path d="M360 843c2 0 5-1 7-1l3 2c1 0 1 0 2 1h3c1 1 2 1 3 1v1h1c1 1 1 1 2 1 1 1 1 0 1 0h1l1 1-1 1h-1c-1-1-2-1-3-2-1 0-1 0-2-1h0c-1-1-2 0-3 0s-2-1-3-1c-2-1-1 0-3-1h-4c-1-1-2-1-3-2h-3 2z" class="P"></path><path d="M374 854h0c-1-4-4-1-6-2 1-2 1-1 3-2 1 1 2 1 4 1 1 0 2 1 3 1l6 2-2 3c-1 0-2 0-2-1-2 0-2 1-3 0s-2-1-3-2z" class="B"></path><path d="M367 842h6l1 1h6c1 0 1 1 2 1l1 1v3h-1s0 1-1 0c-1 0-1 0-2-1h-1v-1c-1 0-2 0-3-1h-3c-1-1-1-1-2-1l-3-2z" class="D"></path><path d="M459 864c1 1 3 1 4 1h1c1 1 2 1 3 2 1 0 2 0 3 1h0 4c0 1 0 1-1 2h2v1h0c-1 0-1 0-2 1v1h-2-1-2l-1-1h2v-1l-1-1s-1 0-2-1l-2-2-2 1h0l-1-1v-1l-2-2z" class="L"></path><path d="M397 851c1 0 2-1 3-1l1 1h3 5c1 0 2 1 3 1 2-1 4 0 5-2 1 1 2 1 2 1l2 1s0 1 1 1c-1 1-4 1-5 1h-2c-1-1-1 0-2 0s-2-1-2-1c-4 0-6 1-9-1-2 0-3 0-5-1z" class="D"></path><path d="M383 845c1 0 2 1 3 1l1 1c2 1 4 2 6 2l1 1 3 1h0c2 1 3 1 5 1l-3 1c-1 0-1 0-2 1h-3c-1-1-2-1-4-2h0c-1-1-2-1-3-2l-2 2-1-1v-2h0l-1-1v-3z" class="P"></path><path d="M383 845c1 0 2 1 3 1l1 1-1 2h-2 0l-1-1v-3z" class="Q"></path><path d="M322 836l1-1h1c0 1 1 1 2 2 1 0 2 1 3 1 2 1 2 0 4 1 0 1 0 1 1 1l1-2v1c1 1 2 1 3 2h1 1l2 3h3c1 0 2 1 2 1h3 0c1 1 2 1 3 1l2 1h1c1 1 2 1 3 2 1 0 1 0 2 1l-1 1c-1 0-2-1-2-1h-1c-1 0-1 0-2-1 0 0-1 0-1-1l-3-1h-1c-1 0-1-1-2-1h-1-3-1c-2-1-1-1-2-1h-3l-2-1v-1h-1l-2-1v-1c-1-1-1-1-2-1h0c-3-1-5-2-7-3-1 0-1 0-2-1z" class="Z"></path><path d="M414 846c5 0 14 1 19 2h0c1 1 2 2 3 2l-1 2-1-1h-1v1 1h-3c0-1-1-1-1-2l-1-1c-3 0-5 1-7 0h-2v1s-1 0-2-1h-2c0-2-1-2-1-4z" class="j"></path><path d="M466 864c-3 0-5-2-8-3h-2c-2-1-5-2-8-2-2 0-4 0-6-1-1-1-1-2-2-2-2 0-3 0-4-1v-1h3c8 1 16 2 24 5v1l3 4z" class="b"></path><defs><linearGradient id="N" x1="405.303" y1="855.163" x2="395.536" y2="840.089" xlink:href="#B"><stop offset="0" stop-color="#d5ccce"></stop><stop offset="1" stop-color="#e2e6e1"></stop></linearGradient></defs><path fill="url(#N)" d="M391 844l4 1c2 0 4 1 7 2 2 0 4 2 6 2l2-1 3 1c0 1 0 1 1 1h1 2c-1 2-3 1-5 2-1 0-2-1-3-1h-5-3l-1-1c-1 0-2 1-3 1h0l-3-1-1-1c-2 0-4-1-6-2l-1-1h4l-1-1c1-1 1-1 2-1z"></path><path d="M386 846h4c1 1 3 1 4 1h3c1 0 2 2 3 2 2 0 3 0 4 2h-3l-1-1c-1 0-2 1-3 1h0l-3-1-1-1c-2 0-4-1-6-2l-1-1z" class="C"></path><path d="M487 872h3 1c2 1 3 3 5 3 1 0 1 0 2 1 1-1 4 0 5-1 0-2-2-2-3-3h3c2 0 3 2 4 2 2 1 6 1 8 0 1 0 1 0 2-1 2 0 5 1 8 2-2 1-2 1-3 1h-1l-2 1h0c-1 0-2-1-3-1l-1 1h-1l-2 1-1-1h-1c-2 1-1-1-2-1l-1 1 1 1v1h-1l-1-1-1-1c-1 1-1 1-3 2-1-1-6-1-6-2l-2-2c-2-1-5-1-7-3z" class="Q"></path><path d="M463 859c4 1 8 2 13 3 7 1 17 1 24 5l2 1c2 1 5 3 8 4 3 0 5 1 7 1-1 1-1 1-2 1-2 1-6 1-8 0-1 0-2-2-4-2h-3c1 1 3 1 3 3-1 1-4 0-5 1-1-1-1-1-2-1-2 0-3-2-5-3h-1-3c0-1 0-1-1-1l-3-1-2-2c-1 0-2-1-3-1-2-1-3 0-5-1s-2-1-3-1l-4-1-3-4v-1z" class="b"></path><path d="M490 872c-1-2-2-2-4-3v-1c2-1 5 1 8 2 1 0 1 0 2 1 1 0 3 1 4 1 1 1 3 1 3 3-1 1-4 0-5 1-1-1-1-1-2-1-2 0-3-2-5-3h-1zm-27-13c4 1 8 2 13 3 7 1 17 1 24 5h0c0 2 2 2 3 3v1c-3 0-3-2-5-2h-2c-2-1-5-1-7-2s-4-1-6-1c-1 0-2 0-3-1-2 0-5-1-6-2s-1-1-2-1l-2 3-4-1-3-4v-1z" class="F"></path><path d="M296 828c1-1 1-1 2-1h1c1 0 2 1 3 1h0 2s1-1 2-1l1 1h1 1c1 1 1 1 2 1 1 1 2 2 2 3 1 0 2 1 3 1 2 1 4 2 5 3h1c1 1 1 1 2 1 2 1 4 2 7 3h0c1 0 1 0 2 1v1l2 1h1v1c-6 0-13-3-18-6l-2 3h1l2-1h1l1 3h-3l-10-3h-2l-3-1h-1c0-1 1 0 0-1l-1 1h-1v-2h0c-1 1-1 1-2 1-1-1 0-1-1-2l-2 1h-1c-1-1-2-1-2-2h-1-2l-1-1h0-2l-2-1v-2c1 0 2-1 3-1l1 1c1 1 1 1 2 0h0v-1c1 0 2 0 4 1h1 0 1 0l1-2-1-1z" class="U"></path><path d="M290 830c1 0 2 0 4 1h1 0 1 1c0 1-1 2-1 2h-2-2v-1l-2 1v-2-1z" class="l"></path><path d="M296 828c1-1 1-1 2-1h1c1 0 2 1 3 1h0 2s1-1 2-1l1 1h1 1c1 1 1 1 2 1 1 1 2 2 2 3 1 0 2 1 3 1 2 1 4 2 5 3h1c1 1 1 1 2 1 2 1 4 2 7 3h0c1 0 1 0 2 1v1l-4-1c-3-1-8-3-11-4-1-1-2-2-3-2h0l-2-1h-1c-1-1-1-1-2-1h-1v-1h-1v1h-1v-2h-3v-2h0l-1 1c-1 0-1-1-2-1 0 0-1 1-2 1v-1l-1-1-1 3h-1 0l1-2-1-1z" class="q"></path><path d="M307 828h1 1c1 1 1 1 2 1 1 1 2 2 2 3-1 0-2-1-4-1h-1c-1-1-1-3-1-3z" class="Z"></path><path d="M303 839v-1l-1-1h-1v-1c1 0 2-1 2-1 3-1 7 0 9 1 2 0 4 1 6 2l-2 3h1l2-1h1l1 3h-3l-10-3h-2l-3-1z" class="a"></path><path d="M316 841h-1c-1-1-2-1-3-1v-1l1-1-1-2h0c2 0 4 1 6 2l-2 3z" class="L"></path><path d="M378 852v-1c4 0 7 4 11 4v-1l6 2h2c2 1 2 1 4 1 4 0 8-1 12-1h4c3-2 5-1 7-1l1 1c1 0 2 0 3 1l1-2c2 1 2 2 4 3h0c4 1 8 3 12 4h6c2 0 3 0 5 1 0 1 1 1 2 1h1l2 2v1l1 1h0l2-1 2 2c1 1 2 1 2 1l1 1v1h-2l1 1h2s-1 1-2 1h-2l-1-2c-2-2-5-3-8-4-2-1-5 0-7 0h-1c-2 1-3 1-5 0l-12-5c-3-2-7-3-10-4-3 0-7 1-10 1h-1l-1 1h-10c-3 0-6-1-9-3-3-1-5-3-7-4l-6-2z" class="Y"></path><path d="M391 858c0-1 1-1 1-1 6 3 12 2 18 2l1 1-1 1h-10c-3 0-6-1-9-3z" class="n"></path><path d="M410 859l4-1c5 0 11 0 15 2 1 1 2 1 3 1l1 1c1 0 2 1 3 1 2 1 3 1 4 2 2 0 3 1 4 2 1 0 1-1 2 0h1 1c1 0 1 1 2 1h-1c-2 1-3 1-5 0l-12-5c-3-2-7-3-10-4-3 0-7 1-10 1h-1l-1-1z" class="c"></path><path d="M417 856c3-2 5-1 7-1l1 1c1 0 2 0 3 1l1-2c2 1 2 2 4 3h0c4 1 8 3 12 4h6c2 0 3 0 5 1 0 1 1 1 2 1h-7l-1-1c-2 0-2 0-4 1h1l-1 1-1-1c-6-2-13-5-19-7-3-1-6-1-9-1zm56 17h2 0v2h2v1h2s2 0 3 1l1 1 3-1c2 1 2 3 3 1 0-1-1-1-2-2h1l1-1 1 2 2 2c1 0 2 0 3-1 1 0 2 2 2 2l1-1c1 0 2 2 2 2h4s0-1 1-1h4 2l2-1 1 1v-1c2 0 3 1 4 2l-2 1 1 2 2-1h3v1l-2 1h-2c-4 2-5 2-10 1l-3 1v2h0c-2 2-2 2-3 4v1l-1-1c-2 0-2 1-3 2h-1v1c-1 0-2-1-3-1l-3-2-1 1-1-1h1l-1-1c-3 1-4-1-6-2h-1-3v-1h-4v-1c1 0 2 0 3-1v-1c-1 0-1 0-2-1 0-2 0-2-1-2l-2 2-1-1-2 1c-1-1-1-1-1-2 2-1 2-2 3-4l-4-2-2-3h2c1 0 2-1 2-1h1 2z" class="L"></path><path d="M502 883h1 5 2 1c0 1 1 1 2 1 0-1 0-1 2-1l1-1 1 2 2-1h3v1l-2 1h-2c-4 2-5 2-10 1-1-2-4 0-6-2v-1z" class="d"></path><path d="M479 880l1-1h1c0 2 0 2 1 3 1-1 1-1 3-1 0 1 0 1 2 2l2-1c2 1 3 0 5 1v-1l1-1 2 2c1 0 1 0 2-1l1 1h2v1l-7 1h-2-2l-1 1h-1c0-1-1-1-2-1v1h-2c0-1-5-3-7-4l1-2z" class="i"></path><path d="M473 873h2 0v2h2v1h2s2 0 3 1l1 1c1 0 2 1 3 2h2l1 2-2 1c-2-1-2-1-2-2-2 0-2 0-3 1-1-1-1-1-1-3h-1l-1 1-1 2-6-3-4-2-2-3h2c1 0 2-1 2-1h1 2z" class="q"></path><path d="M470 873h1c0 2 1 2 2 3v2l2 1 1-1h1v2h2l-1 2-6-3-4-2-2-3h2c1 0 2-1 2-1z" class="c"></path><path d="M502 884c2 2 5 0 6 2l-3 1v2h0c-2 2-2 2-3 4v1l-1-1c-2 0-2 1-3 2h-1c-1 0-1 0-2-1 0-1 0-1-1-1l-1-2c0-1-2-2-4-3l-2-2v-1c1 0 2 0 2 1h1l1-1h2 2l7-1z" class="l"></path><path d="M493 891c1 0 3 1 3 1l2-2c2-2 4-3 7-3v2h0c-2 2-2 2-3 4v1l-1-1c-2 0-2 1-3 2h-1c-1 0-1 0-2-1 0-1 0-1-1-1l-1-2z" class="D"></path><path d="M472 879l6 3c2 1 7 3 7 4h2l2 2c2 1 4 2 4 3l1 2c1 0 1 0 1 1 1 1 1 1 2 1v1c-1 0-2-1-3-1l-3-2-1 1-1-1h1l-1-1c-3 1-4-1-6-2h-1-3v-1h-4v-1c1 0 2 0 3-1v-1c-1 0-1 0-2-1 0-2 0-2-1-2l-2 2-1-1-2 1c-1-1-1-1-1-2 2-1 2-2 3-4z" class="G"></path><path d="M485 886h2l2 2c-1 0-2 1-3 1h-1v-3z" class="P"></path><path d="M478 886l1-2 1 1-1 1v1h3c2 0 1-1 2 0v1l-2 1v1h-3v-1h-4v-1c1 0 2 0 3-1v-1z" class="L"></path><path d="M447 853c2 0 4 1 5 1l4 1h2l4 2c-1-1-1-2-1-3 1-1 3 0 4 0l3 1 2-1 8 3 13 3c6 1 11 4 16 7 1 0 2 0 3 1 2 1 3 1 6 1v-1-2c4 2 8 2 12 3 1 1 4 1 5 2 2-1 3-1 5-1 1 0 1 0 2 1v1h0c1 1 2 1 2 2v1l3 1h-3c-1 2-2 2-4 2h-1l1 2h-2l-1-1h-2l-1-2c-1-1-2-1-4-1 1 1 3 2 3 3v1c0 1 1 0 1 0h1l3 3c1 0 1 0 2 1h-5v-1l-2-2c-1 1-1 1-1 3 0 1-1 3-1 4h0v-2l-2-1v-1h-5v-1h-3l-2 1-1-2 2-1h4l1-2-4-2 2-1h1c1 0 1 0 3-1-3-1-6-2-8-2s-4-1-7-1c-3-1-6-3-8-4l-2-1c-7-4-17-4-24-5-5-1-9-2-13-3-8-3-16-4-24-5 0 0 0-1 1-1h7z" class="R"></path><path d="M473 857l14 4c2 0 5 1 7 2-3 0-6-1-9-1-5-1-10-1-14-3l2-2z" class="b"></path><path d="M465 854l3 1 5 2-2 2-9-2c-1-1-1-2-1-3 1-1 3 0 4 0z" class="F"></path><path d="M514 871c0-1 1-1 1-1l4-2 1 1c1 0 2 0 3 1 3 0 7 2 10 2h3 1v-1h-4c2-1 3-1 5-1 1 0 1 0 2 1v1h0c1 1 2 1 2 2v1l3 1h-3c-1 2-2 2-4 2h-1l1 2h-2l-1-1h-2l-1-2c-1-1-2-1-4-1h0c-2-2-5-3-7-4h-2c-2 0-3-1-5-1z" class="B"></path><path d="M534 876l2-2c2 1 4 1 6 2h0c-1 2-2 2-4 2h-1l-1-1h-1l-1-1z" class="P"></path><path d="M514 871c0-1 1-1 1-1l4-2 1 1c1 0 2 0 3 1h-2l1 1h0c3 0 5 2 8 3 2 0 3 1 4 2l1 1h1l1 1 1 2h-2l-1-1h-2l-1-2c-1-1-2-1-4-1h0c-2-2-5-3-7-4h-2c-2 0-3-1-5-1z" class="G"></path><path d="M502 868l1-1c1 1 2 2 4 2 2 1 4 1 6 2h1c2 0 3 1 5 1h2c2 1 5 2 7 4h0c1 1 3 2 3 3v1c0 1 1 0 1 0h1l3 3c1 0 1 0 2 1h-5v-1l-2-2c-1 1-1 1-1 3 0 1-1 3-1 4h0v-2l-2-1v-1h-5v-1h-3l-2 1-1-2 2-1h4l1-2-4-2 2-1h1c1 0 1 0 3-1-3-1-6-2-8-2s-4-1-7-1c-3-1-6-3-8-4z" class="Z"></path><path d="M525 875c1 1 3 2 4 3l-3 2-3-1-4-2 2-1h1c1 0 1 0 3-1z" class="B"></path><path d="M502 868l1-1c1 1 2 2 4 2 2 1 4 1 6 2h1c2 0 3 1 5 1h2c2 1 5 2 7 4h0c1 1 3 2 3 3v1c0 1 1 0 1 0h1l3 3c1 0 1 0 2 1h-5v-1l-2-2c-1 1-1 1-1 3 0 1-1 3-1 4h0v-2l-2-1v-1l-1-1h2c1 0 1 1 2 1 0-2 0-4-1-6-1-1-3-2-4-3-3-1-6-2-8-2s-4-1-7-1c-3-1-6-3-8-4z" class="i"></path><path d="M502 839v1h2l3 1c1 1 2 2 4 3 1-1 1-1 2-1 1 1 0 1 1 0h0 1c1 2 3 3 5 4l1 2 2 1h0c1 2 3 4 5 6 0 0 0 1-1 1h-1-3c-1 1-1 2-1 3l2 1h1 0c1 0 2-1 3-1l2 2c1 0 2 0 4 1s4 2 5 4h2c2-1 3 0 5 0l2-1c2 0 4 0 7 1h0v1c-6-1-10 1-15 3-1-1-1-1-2-1-2 0-3 0-5 1-1-1-4-1-5-2-4-1-8-1-12-3v2 1c-3 0-4 0-6-1-1-1-2-1-3-1-5-3-10-6-16-7l-13-3-8-3-2 1-3-1v-1-1c7 0 14 0 21 2l9 1v-1c-3-1-5-2-8-3-2 0-7-1-9-1-1-2-2-2-2-3l13-3 1-2h4l1 1 3-1c2-1 2-2 3-3h1z" class="b"></path><path d="M465 852c7 0 14 0 21 2h-10l-1-1h-4c2 1 5 2 8 3h2l1 1h1 2c2 0 4 1 6 1 3 1 6 2 10 3h4c2 1 5 2 8 3 1 1 3 1 3 2v2 1c-3 0-4 0-6-1-1-1-2-1-3-1-5-3-10-6-16-7l-13-3-8-3-2 1-3-1v-1-1z" class="B"></path><path d="M487 851h5v-1c1 1 1 2 2 2 2 0 4 0 6 1h2c1 0 2 1 3 1 1 1 2 1 3 1l1 1 2 1 1-1h1 0c1 1 2 1 3 2 1 0 2 1 3 1l1-1c1 0 1 2 2 2l2 1h1 0c1 0 2-1 3-1l2 2c1 0 2 0 4 1s4 2 5 4h2c2-1 3 0 5 0l2-1c2 0 4 0 7 1h0v1c-6-1-10 1-15 3-1-1-1-1-2-1-6-5-15-4-21-8-7-3-14-5-22-7v-1c-3-1-5-2-8-3z" class="n"></path><path d="M533 863h1c2 1 4 2 5 4h-2c-1 0-2-1-3-1-1-1-1 0-2-1 1-1 1-1 1-2h0z" class="c"></path><path d="M525 861h0c1 0 2-1 3-1l2 2c1 0 2 0 4 1h-1-1l-1 1c-3-2-4 1-6 0v-3z" class="U"></path><path d="M512 856h1 0c1 1 2 1 3 2 1 0 2 1 3 1l1-1c1 0 1 2 2 2l2 1c0 1-1 1-1 2-2-1-4-1-6-2l-1-1c-1 0-1 0-2-1l-1-1-2-1 1-1z" class="k"></path><path d="M502 839v1h2l3 1c1 1 2 2 4 3 1-1 1-1 2-1 1 1 0 1 1 0h0 1c1 2 3 3 5 4l1 2 2 1h0c1 2 3 4 5 6 0 0 0 1-1 1h-1-3c-1 1-1 2-1 3-1 0-1-2-2-2l-1 1c-1 0-2-1-3-1-1-1-2-1-3-2h0-1l-1 1-2-1-1-1c-1 0-2 0-3-1-1 0-2-1-3-1h-2c-2-1-4-1-6-1-1 0-1-1-2-2v1h-5c-2 0-7-1-9-1-1-2-2-2-2-3l13-3 1-2h4l1 1 3-1c2-1 2-2 3-3h1z" class="G"></path><path d="M515 854c1-1 1-1 1-2l2 1h3l2 3h-1l-1 1-1-2-1 1c-2 0-3-1-4-2z" class="R"></path><path d="M502 839v1h2l3 1c1 1 2 2 4 3 1-1 1-1 2-1 1 1 0 1 1 0h0 1c1 2 3 3 5 4l1 2h-3c-2-1-2-1-3-1l-1 2-1-1-1-1h-1c-1 0-3 0-5-1h-1 0c-2-1-2-1-4-1l-1-1v2l-2-1c0-1 0-2 1-3l-1-1c2-1 2-2 3-3h1z" class="Q"></path><path d="M499 843l2 1 1-1c1 0 1 0 2 1h0c3 0 4 2 6 3l1 1c-1 0-3 0-5-1h-1 0c-2-1-2-1-4-1l-1-1v2l-2-1c0-1 0-2 1-3z" class="D"></path><path d="M495 843l3-1 1 1c-1 1-1 2-1 3l2 1v-2l1 1v2h2c2 0 4 3 6 3s3 0 4 1h1l1 2c1 1 2 2 4 2l1-1 1 2 1-1h1 2c1 0 1 1 2 1h-1-3c-1 1-1 2-1 3-1 0-1-2-2-2l-1 1c-1 0-2-1-3-1-1-1-2-1-3-2h0-1l-1-1h1l-1-1-2-1h-2-1l-4-2h0l-1-1h-1c-1-1-2-2-4-2s-4 0-5-1l1-1 1 1h1c-1-2-1-2-3-2v-1h1 0c1 0 2-1 3-1z" class="L"></path><defs><linearGradient id="O" x1="478.977" y1="850.949" x2="487.307" y2="846.174" xlink:href="#B"><stop offset="0" stop-color="#302e2e"></stop><stop offset="1" stop-color="#4d4b4a"></stop></linearGradient></defs><path fill="url(#O)" d="M490 842h4l1 1c-1 0-2 1-3 1h0-1v1c2 0 2 0 3 2h-1l-1-1-1 1c1 1 3 1 5 1s3 1 4 2h1l1 1h0l4 2h1 2l2 1 1 1h-1l1 1-1 1-2-1-1-1c-1 0-2 0-3-1-1 0-2-1-3-1h-2c-2-1-4-1-6-1-1 0-1-1-2-2v1h-5c-2 0-7-1-9-1-1-2-2-2-2-3l13-3 1-2z"></path><path d="M321 843h1v-1c1 0 1 0 2 1v1l1 1c1-1 1-1 2-1l5 1c1 0 3 1 4 2 2 0 3-1 6 0 1 1 1 1 2 1h1c1 1 3 1 4 2v1 1c2 1 2 1 2 2l1-1c1 0 1 1 2 1h0v-1l-1-1c1-1 1-1 2 0l5 2 2 1 1-1h2l1 1 2-2c2 1 4 1 6 1 1 1 2 1 3 2s1 0 3 0c0 1 1 1 2 1l2-3c2 1 4 3 7 4 3 2 6 3 9 3l-4 1h1c1 0 2 1 2 2v1c-1 1-1 2-3 2s-4-1-7 0h0l-3 1v1c-2 0-3 0-4 2-1 0-2 1-4 1-1 1-2 2-4 2l-1 1c-1 1-2 1-3 1h0-2-1-2c-2 1-4 1-5 1h-3 0l-1-1-1 1-2-2-1 1c0-1-1-1-2-2-1 0-1-1-2-2h3l1-1c0 1 1 1 2 1l1-1c0-1-1-2-1-3-2 0-2 0-3-1v-1h2l-1-1h-1c-1-1-4-3-5-4l-3-1c0-2-7-6-9-7-1 0-1-1-2-1 0-1-1-3-2-4h-2c-2-1-5-2-6-3l-4-2h3z" class="T"></path><path d="M338 852h-1c-1-1-1-1-2-1h-1v-1c0-1 1-2 2-2 2 0 3 1 4 2l-2 2z" class="h"></path><path d="M340 850c1-1 1-1 2 0 2 0 4 0 7 1v1 3c0 1 2 2 3 3h1v2h2v1h2 1c1 1 2 1 3 2h1l2-1 1 1h3 8l2 1c1-2 1-2 2-3 1 1 3 3 4 3l1 2h-3v1h-2c-1-1-1-1-2 0l-1-1-2 1c-3-3-8 0-12-3h0l-2 1c-1-2-3-1-5-2h-1l-1-1c-1-1-2-1-3-2h-2l-2-3h-2v-1h-2c-1-1 0-1 0-2-1-1-2-1-3-2l-1 1-1-1 2-2z" class="j"></path><path d="M348 872h3l1-1c0 1 1 1 2 1l1-1c0-1-1-2-1-3-2 0-2 0-3-1v-1h2c0 1 1 1 2 2l1 1 2-2v1 1 1h1v-1c0-1 0 0 1-1l1 1-1 1 1 1v-1h0c1-1 1-1 1-2l1-1c0 1 0 1 2 2v-1l1 1-1 2 2-2c1 0 1 1 2 2 1 0 1 0 2-1h0l2 1v-1l1 1h0c-1 2-1 3-1 4-1 1-2 1-3 1h0-2-1-2c-2 1-4 1-5 1h-3 0l-1-1-1 1-2-2-1 1c0-1-1-1-2-2-1 0-1-1-2-2z" class="U"></path><path d="M349 852c2 1 2 1 2 2l1-1c1 0 1 1 2 1h0v-1l-1-1c1-1 1-1 2 0l5 2 2 1c1 1 6 3 8 3v-1c2 1 3 1 5 2 2 0 4 1 5 2-1 1-1 1-2 3l-2-1h-8-3l-1-1-2 1h-1c-1-1-2-1-3-2h-1-2v-1h-2v-2h-1c-1-1-3-2-3-3v-3z" class="D"></path><path d="M360 854l2 1c1 1 6 3 8 3v-1c2 1 3 1 5 2 2 0 4 1 5 2-1 1-1 1-2 3l-2-1c-1 0-1 0-2-1l-11-2c0-1 0-1-1-2h-1c-1-2-1-2-1-4z" class="Q"></path><path d="M362 855l1-1h2l1 1 2-2c2 1 4 1 6 1 1 1 2 1 3 2s1 0 3 0c0 1 1 1 2 1l2-3c2 1 4 3 7 4 3 2 6 3 9 3l-4 1h1c1 0 2 1 2 2v1c-1 1-1 2-3 2s-4-1-7 0h0l-3 1c0-1-1-1-1-2l-1-2c-1 0-3-2-4-3s-3-2-5-2c-2-1-3-1-5-2v1c-2 0-7-2-8-3z" class="T"></path><path d="M384 864h4c0 1 1 2 1 3l-3 1c0-1-1-1-1-2l-1-2z" class="p"></path><path d="M388 864h0s1-1 2-1h1 2 0c3 0 2 0 3 1h3v1c-1 1-1 2-3 2s-4-1-7 0h0c0-1-1-2-1-3z" class="Y"></path><path d="M362 855l1-1h2l1 1 2-2c2 1 4 1 6 1 1 1 2 1 3 2s1 0 3 0c0 1 1 1 2 1l2-3c2 1 4 3 7 4 3 2 6 3 9 3l-4 1c-2 0-3-2-4-2-2 0-2 0-2 1-2 0-2-1-3-1h-5-1c-2-2-4-2-6-3-1 0-2-1-3-1l-2 1v1c-2 0-7-2-8-3z" class="R"></path><path d="M313 789l9 2c3 0 6 0 9 1h3 4c4-1 9-1 14 0 4 1 6 3 10 5l1 1c2 3 3 6 4 9h-1c-1 0-1 0-2-1l-1 1s1 1 1 2c3 0 5 1 8 1v-1h2 4c3 0 6 0 8 1h0 3l2 1 1 1h0 3v1l3 3c0 1 1 1 1 2 1 1 2 1 2 2h1l1-1-2-2v-1c2 1 4 1 5 3-1 2-2 3-4 5l1 2c-1 2-3 2-5 3h-1-4c-1 0-1 1-2 1h-4l2 1h0c0 1 2 3 2 3 1 0 1 2 1 2h2l1 1h1c2 0 3 1 5 1h2 2c1 1 0 1 1 1v3c2 0 1-1 3-1 1 0 3 2 4 3h11 6 5c0 2 0 3-2 4h0c-5-1-14-2-19-2 0 2 1 2 1 4h-1c-1 0-1 0-1-1l-3-1-2 1c-2 0-4-2-6-2-3-1-5-2-7-2l-4-1c-1 0-1 0-2 1l1 1h-4c-1 0-2-1-3-1l-1-1c-1 0-1-1-2-1h-6l-1-1h-6c-2 0-5 1-7 1h-2 0-1c-1-1-2-1-2-2h-2l-3-3h-4l2 2v1c-2 0-2 0-4-1s-4 0-6-2l-3-1h-6-3c-1-1-2-1-2-2h-1l-1 1h-1c-1-1-3-2-5-3-1 0-2-1-3-1 0-1-1-2-2-3-1 0-1 0-2-1h-1-1l-1-1c-1 0-2 1-2 1h-2 0c-1 0-2-1-3-1h-1c-1 0-1 0-2 1 0 1-1 1-2 2l1-2h0-2l-1-1s-1 1-2 1l-1-1c-2 0-2-1-4-1v-1h-1l-2-1h0-1v-1h-1-1v-1l4-4-2-2c-2-1-2-1-4-3l-1-1 1-1-1-1h-1-1v-1c-1-2 0-1-1-1s-2-2-3-2h3 0 3c2-1 3 0 5 1h2l1-2c0-1-1-1-2-1l-3-1-3-3c1 0 1 0 2 1h2 2 1 1 2 2c1-1 2-1 4-2l1 1c1 0 2-1 2-2 2 0 3 1 4 2h0c0-1 1-1 2-2l-1-2h0 1 2c2 1 5-1 8-2-2-1-2-1-3-2h-1l-1-1c2 0 2 0 3-1h3l1-1z" class="C"></path><path d="M277 813h3c1 0 1 1 2 1s1-1 1 0c1 2 2 2 4 3v1 2h-1c-2-1-3-1-3-2l-2-2c-2-1-2-1-4-3z" class="D"></path><path d="M306 813l2 1v3c1 0 3-1 4 0v1c-1 1-2 1-3 2h-1 0c-2-1-3-2-4-2h0c-1 0-2-1-3-2l1-1c1 0 1 0 2 1l1-1 1-2z" class="V"></path><path d="M306 813l2 1v3c-2 0-2 0-4-1l1-1 1-2z" class="H"></path><path d="M287 818h3v1c1 0 2 0 3 1 2 0 0-1 2 0 1 1 2 1 3 1h0c1 0 3 0 4 1h2c2 0 3 1 4 1 1 1 2 1 4 1 1 1 2 1 2 2h-4l-4-1c-1 0-2 0-3-1h-2-1l-7-2c-1 0-2 0-3-1s-2-1-3-1v-2zm-3-13c2 1 4 2 6 2-1 1-1 2-1 3v1l3 1-2 1h-1c-1 0-1-1-2 0l-1-1c-2-1-5 0-7-1v1l1 1h-3l-1-1 1-1-1-1h-1-1v-1c-1-2 0-1-1-1s-2-2-3-2h3 0 3c2-1 3 0 5 1h2l1-2z" class="B"></path><path d="M284 805c2 1 4 2 6 2-1 1-1 2-1 3v1c-4-1-8-1-11-3h-1c1-1 2-1 4-1h2l1-2z" class="b"></path><path d="M308 814h1c2 1 7 1 9 0 4 1 6 2 9 5 2 1 3 2 4 3l1 1v1c-3 0-5-1-7-1-3 0-5 0-7-1-3-1-6-1-9-2 1-1 2-1 3-2v-1c-1-1-3 0-4 0v-3z" class="H"></path><path d="M308 814h1c2 1 7 1 9 0 4 1 6 2 9 5h-3c-1 0-1-1-2-2-1 1-1 1-2 1s-2-1-3-1c-1-1-2-1-3-2h0-4c-1 0 0 0-1 1h3v1 1-1c-1-1-3 0-4 0v-3z" class="E"></path><path d="M283 818c0 1 1 1 3 2h1c1 0 2 0 3 1s2 1 3 1l7 2h1 2c1 1 2 1 3 1l4 1h4l1 1h1c2 1 3 2 5 2l1 1h1l1 1c1 2 4 3 7 3h0c2 1 2 1 4 3h-6-3c-1-1-2-1-2-2h-1l-1 1h-1c-1-1-3-2-5-3-1 0-2-1-3-1 0-1-1-2-2-3-1 0-1 0-2-1h-1-1l-1-1c-1 0-2 1-2 1h-2 0c-1 0-2-1-3-1h-1c-1 0-1 0-2 1 0 1-1 1-2 2l1-2h0-2l-1-1s-1 1-2 1l-1-1c-2 0-2-1-4-1v-1h-1l-2-1h0-1v-1h-1-1v-1l4-4z" class="G"></path><path d="M308 828c2 0 4 1 6 1l1 1 2 1c1 1 1 1 3 2h0l1 3c-1-1-3-2-5-3-1 0-2-1-3-1 0-1-1-2-2-3-1 0-1 0-2-1h-1z" class="R"></path><path d="M282 824l2-3 1 2h1l1-2c1 1 1 0 1 1l1 1s1 0 1 1l1-1c1 0 1 0 2 1 3 0 6 0 8 2h2c1 0 2 1 3 1-1 0-2 1-2 1h-2 0c-1 0-2-1-3-1h-1c-1 0-1 0-2 1 0 1-1 1-2 2l1-2h0-2l-1-1s-1 1-2 1l-1-1c-2 0-2-1-4-1v-1h-1l-2-1z" class="L"></path><path d="M326 829l-3-3h0c1 0 9 3 9 2 2-1 3 0 5 0 1 1 1 1 2 1 1 1 1 1 2 1 1 1 2 1 3 1h0c1 1 4 2 5 2h1 1l1 1h0c3 1 5 1 8 0l1 1c2 2 2 2 5 2 0 1 0 1 1 0 1 0 1 0 2 1l4 1v1c-1 1-3 1-5 1h0 1c2 0 3 1 4 1h-6c-2 0-5 1-7 1h-2 0-1c-1-1-2-1-2-2h-2l-3-3h-4l2 2v1c-2 0-2 0-4-1s-4 0-6-2l-3-1c-2-2-2-2-4-3h0c-3 0-6-1-7-3l-1-1h-1l-1-1c-2 0-3-1-5-2h3l1 1h5l1 1z" class="B"></path><path d="M331 834c2 1 4 0 6 2l1 2-3-1c-2-2-2-2-4-3z" class="P"></path><path d="M346 838c-1-1-1-1-1-2v-1h4l1 3h-4z" class="K"></path><path d="M349 835c0 1 2 1 2 1l1 1c1 1 2 0 3 1s1 2 1 3l1 1c1 0 1 0 1 1h-1c-1-1-2-1-2-2h-2l-3-3-1-3z" class="D"></path><path d="M326 829l-3-3h0c1 0 9 3 9 2 2 1 3 1 5 2 3 1 4 2 6 4-1 1-1 1-2 1s-2-1-3-2h-1c-1-1-2-1-3-1-3 0-2-1-5-1v-1c-1-1-2-1-3-1z" class="H"></path><path d="M350 833h1l1 1h0c3 1 5 1 8 0l1 1c2 2 2 2 5 2 0 1 0 1 1 0 1 0 1 0 2 1l4 1v1c-1 1-3 1-5 1h0 1c2 0 3 1 4 1h-6c-2 0-5 1-7 1h-1l1-1c1-1 3-1 5-1h1c1-2 4 0 6-1v-1h-4-1-8c1-1 1-1 1-2-2-1-3 0-4-1h-1c0 1-1 0-2 0-1-1-2-1-3-3z" class="V"></path><path d="M361 835c2 2 2 2 5 2 0 1 0 1 1 0 1 0 1 0 2 1h-1c-3 1-5 1-7-1v-1-1z" class="C"></path><path d="M295 798c2 0 3 1 4 2h0l5 3 2 1 4 3 3 1 5 1c3 0 7 0 9 2 1 0 2-1 3-2h2l1 2c1 1 2 1 3 1l1 1h5v1c1 0 2 1 4 1l1 2v1h-1v1c1 2 3 2 5 2l1 2h3v1h-4-1c-3-1-4-2-6-4-1 0-3 0-4 1h-4-2l-1 1-1-1-1 1c-1-1-2-2-4-3-3-3-5-4-9-5-2 1-7 1-9 0h-1l-2-1-1 2-1-1-1 1c-2 0-4-1-6-1l-1-1c-1 0-1 0-2-1h-1-1l-3-1v-1c0-1 0-2 1-3-2 0-4-1-6-2 0-1-1-1-2-1l-3-1-3-3c1 0 1 0 2 1h2 2 1 1 2 2c1-1 2-1 4-2l1 1c1 0 2-1 2-2z" class="G"></path><path d="M295 798c2 0 3 1 4 2l-2 2c-1-1-1-1-2-1s-1 0-2-1c1 0 2-1 2-2z" class="D"></path><path d="M290 807c2 1 3 1 5 2h1c1 1 0 1 1 1h3c0 1 0 1 1 1 2 1 3 2 5 2l-1 2-1-1-1 1c-2 0-4-1-6-1l-1-1c-1 0-1 0-2-1h-1-1l-3-1v-1c0-1 0-2 1-3z" class="E"></path><defs><linearGradient id="P" x1="313.581" y1="806.529" x2="340.688" y2="821.393" xlink:href="#B"><stop offset="0" stop-color="#bebbb9"></stop><stop offset="1" stop-color="#e4e1e1"></stop></linearGradient></defs><path fill="url(#P)" d="M332 821c-2-1-4-4-6-6l-6-3c-2 0-4-2-6-2h-2v-2h1l5 1c3 0 7 0 9 2 1 0 2-1 3-2h2l1 2c1 1 2 1 3 1l1 1h5v1c1 0 2 1 4 1l1 2v1h-1v1c1 2 3 2 5 2l1 2h3v1h-4-1c-3-1-4-2-6-4-1 0-3 0-4 1h-4-2l-1 1-1-1z"></path><path d="M330 809h2l1 2c1 1 2 1 3 1l1 1h5v1c1 0 2 1 4 1l1 2v1h-1v1c1 2 3 2 5 2l1 2h3v1h-4-1c-3-1-4-2-6-4-1-5-10-2-13-5-1-1-3-2-4-4 1 0 2-1 3-2z" class="S"></path><path d="M330 809h2l1 2c1 1 2 1 3 1l1 1h-4l-2 2c-1-1-3-2-4-4 1 0 2-1 3-2z" class="l"></path><path d="M340 821c1-1 3-1 4-1 2 2 3 3 6 4h1 4c4 0 7 1 11 1l1-1c2 1 4 1 6 1h0c0 1 1 1 1 1h1c2 0 4 0 6 2h2l4 2 2 1h0c0 1 2 3 2 3 1 0 1 2 1 2h2l1 1h1c2 0 3 1 5 1h2 2c1 1 0 1 1 1v3c2 0 1-1 3-1 1 0 3 2 4 3h11 6 5c0 2 0 3-2 4h0c-5-1-14-2-19-2 0 2 1 2 1 4h-1c-1 0-1 0-1-1l-3-1-2 1c-2 0-4-2-6-2-3-1-5-2-7-2l-4-1c-1 0-1 0-2 1l1 1h-4c-1 0-2-1-3-1l-1-1c-1 0-1-1-2-1h-6l-1-1c-1 0-2-1-4-1h-1 0c2 0 4 0 5-1v-1l-4-1c-1-1-1-1-2-1-1 1-1 1-1 0-3 0-3 0-5-2l-1-1c-3 1-5 1-8 0h0l-1-1h-1-1c0-1 0-2 1-3-4-1-6-3-9-4-1-1-3-1-5-2-1 0-2-1-4-1l-1-1 1-1 1 1 1-1h2 4z" class="F"></path><path d="M413 849c0-1 0-1-1-2l1-1h1c0 2 1 2 1 4h-1c-1 0-1 0-1-1z" class="b"></path><path d="M391 844l-1-1c1-1 2-1 3-1h0c1 1 3 0 4 1l3 1c1 0 0 0 1 1 2 2 6 1 9 3l-2 1c-2 0-4-2-6-2-3-1-5-2-7-2l-4-1z" class="I"></path><path d="M370 827c8 2 17 6 22 12l3 1h-4c-6 0-10-5-15-8-3-1-6 1-9 2h-3l-3-3v-2l3 1 8-1h1c1 1 2 1 3 1l-2-1c-1 0-2-1-3-2h-1z" class="G"></path><path d="M370 827c8 2 17 6 22 12l3 1h-4l-1-2c-4 1-10-5-13-7-3-1-10-1-13-1l8-1h1c1 1 2 1 3 1l-2-1c-1 0-2-1-3-2h-1z" class="I"></path><path d="M367 824c2 1 4 1 6 1h0c0 1 1 1 1 1h1c2 0 4 0 6 2h2l4 2 2 1h0c0 1 2 3 2 3 1 0 1 2 1 2h2l1 1h1c2 0 3 1 5 1h2 2c1 1 0 1 1 1v3c2 0 1-1 3-1 1 0 3 2 4 3l-18-4-3-1c-5-6-14-10-22-12 0-1-4-2-4-2l1-1z" class="S"></path><path d="M340 821c1-1 3-1 4-1 2 2 3 3 6 4h1 4c4 0 7 1 11 1 0 0 4 1 4 2h1c1 1 2 2 3 2l2 1c-1 0-2 0-3-1h-1l-8 1-3-1v2h-1c-4-1-7-1-10-1-4-1-6-3-9-4-1-1-3-1-5-2-1 0-2-1-4-1l-1-1 1-1 1 1 1-1h2 4z" class="F"></path><path d="M340 821c1-1 3-1 4-1 2 2 3 3 6 4h1l2 2h-6v1h1 2 1-1c-2 0-5-1-7-1-1-2-2-3-3-5z" class="J"></path><path d="M333 822l1-1h2 4c1 2 2 3 3 5 2 0 5 1 7 1 1 1 11 2 11 2v2h-1c-4-1-7-1-10-1-4-1-6-3-9-4-1-1-3-1-5-2-1 0-2-1-4-1l-1-1 1-1 1 1z" class="D"></path><path d="M333 822l1-1h2 4c1 2 2 3 3 5-1-1-2-1-3-2l-7-2z" class="C"></path><path d="M313 789l9 2c3 0 6 0 9 1h3 4c4-1 9-1 14 0 4 1 6 3 10 5l1 1c2 3 3 6 4 9h-1c-1 0-1 0-2-1l-1 1s1 1 1 2c3 0 5 1 8 1v-1h2 4c3 0 6 0 8 1h0 3l2 1 1 1h0 3v1l3 3c0 1 1 1 1 2 1 1 2 1 2 2h1l1-1-2-2v-1c2 1 4 1 5 3-1 2-2 3-4 5l1 2c-1 2-3 2-5 3h-1-4c-1 0-1 1-2 1h-4l-4-2h-2c-2-2-4-2-6-2h-1s-1 0-1-1h0c-2 0-4 0-6-1l-1 1c-4 0-7-1-11-1v-1h-3l-1-2c-2 0-4 0-5-2v-1h1v-1l-1-2c-2 0-3-1-4-1v-1h-5l-1-1c-1 0-2 0-3-1l-1-2h-2c-1 1-2 2-3 2-2-2-6-2-9-2l-5-1-3-1-4-3-2-1-5-3c0-1 1-1 2-2l-1-2h0 1 2c2 1 5-1 8-2-2-1-2-1-3-2h-1l-1-1c2 0 2 0 3-1h3l1-1z" class="P"></path><path d="M345 803c2 1 4 0 5-1v-1c2 0 5 3 7 4l-1 1-5-3-2 1h-1l-2 4c0-1 0-1-1-2l-3 2-2-2c2-1 2-1 4-1 0-1 0-1 1-1v-1z" class="N"></path><path d="M329 802h2 1v1c2 1 6-1 8 0l1 1h3l-1-1 1-1 1 1v1c-1 0-1 0-1 1-2 0-2 0-4 1h-2c-2 0-2-1-3-1h-6c-1 0-1 0-2-1l2-2z" class="Z"></path><path d="M329 805h6c1 0 1 1 3 1h2l2 2c-1 1-2 1-2 2l-2 1-2 1c-1 0-2 0-3-1 1-1 1-1 2-3h-1c-1-1-3-2-5-3z" class="r"></path><path d="M335 808l3 1v2l-2 1c-1 0-2 0-3-1 1-1 1-1 2-3z" class="c"></path><path d="M357 805c1 1 3 2 4 3 2 2 2 3 4 3 0-1 0-2-1-2 3 0 5 1 8 1l6 1s1 0 2 1c-1 0-1 1-1 1-2 0-3-1-4-1 0 1 1 1 1 1h0c-1 1-2 1-3 1s-1-1-2 0h-4c0-1 0-1-1-2h-3l-1-1v-1c-2-1-4-3-6-4h0l1-1z" class="G"></path><path d="M342 808l3-2c1 1 1 1 1 2 0 2 0 2 1 3 2 1 3 2 5 2h-3l-1 1s-1 1-2 1c-2 0-3-1-4-1v-1h-5l-1-1 2-1 2-1c0-1 1-1 2-2z" class="i"></path><path d="M342 808l3-2c1 1 1 1 1 2 0 2 0 2 1 3h0l-1 1c-2 0-3-1-4-2l-1 1h0v1l-1-2c0-1 1-1 2-2z" class="U"></path><path d="M349 804l2-1 5 3h0c2 1 4 3 6 4v1l1 1h3c1 1 1 1 1 2-1 0-2-1-3-1l-5-1c-2 0-4 0-6 1h-1c-2 0-3-1-5-2-1-1-1-1-1-3l2-4h1z" class="R"></path><path d="M356 806c2 1 4 3 6 4v1l-5-2c-2 0-3-1-4-2 1 0 2 0 3-1z" class="D"></path><path d="M349 804l2-1 5 3h0c-1 1-2 1-3 1-2 0-4 1-5 0 0 0 1-1 1-2v-1z" class="B"></path><path d="M313 789l9 2c3 0 6 0 9 1h3 4c4-1 9-1 14 0 4 1 6 3 10 5l1 1v1c0 1 0 2 1 4h0c-2 0-2 0-3-1-1 0-1-1-1-1-2 0-4 0-5-1-2 0-3-1-5 0h0l-2-2h0v2h-1c-2-1-6-1-9-1h0c-2 1-5 0-7 0-1 1 0 1 0 2v1h-2c-1 0-1 0-2 1-1-1-2-1-2-2-1-1-1-1-1-3-1 0-1 1-3 0h-1-2l-1-2h0v-1l5 1-2-4c-3 0-5-1-8-2l1-1z" class="j"></path><path d="M348 798c-1 0-2-1-2-1-1 0-1-1-2-2 2-1 6 2 8 2 1 1 2 0 2 0 2 0 2 0 3 1-1 0-1 1-2 1v1c-2 0-3-1-5 0h0l-2-2h0z" class="V"></path><path d="M313 789l9 2c3 0 6 0 9 1h3 4c2 1 3 1 5 1-1 0-2 0-3 1-3 2-6-1-9 0l-1 2h-1l-1-1c-2-2-5-2-8-3-3 0-5-1-8-2l1-1z" class="B"></path><path d="M317 795l5 1c9 1 17 1 26 4h-1c-2-1-6-1-9-1h0c-2 1-5 0-7 0-1 1 0 1 0 2v1h-2c-1 0-1 0-2 1-1-1-2-1-2-2-1-1-1-1-1-3-1 0-1 1-3 0h-1-2l-1-2h0v-1z" class="d"></path><path d="M324 798l2 1h0 3c2-1 6-1 9-1v1h0c-2 1-5 0-7 0-1 1 0 1 0 2v1h-2c-1 0-1 0-2 1-1-1-2-1-2-2-1-1-1-1-1-3z" class="q"></path><path d="M338 792c4-1 9-1 14 0 4 1 6 3 10 5l1 1v1c0 1 0 2 1 4h0c-2 0-2 0-3-1-1 0-1-1-1-1-2 0-4 0-5-1v-1c1 0 1-1 2-1-1-1-1-1-3-1-2-1-5-2-8-3-1-1-1-1-3-1s-3 0-5-1z" class="E"></path><path d="M309 790h3c3 1 5 2 8 2l2 4-5-1v1h0l1 2h2 1c2 1 2 0 3 0 0 2 0 2 1 3 0 1 1 1 2 2 1-1 1-1 2-1l-2 2c1 1 1 1 2 1 2 1 4 2 5 3h1c-1 2-1 2-2 3l-1-2h-2c-1 1-2 2-3 2-2-2-6-2-9-2l-5-1-3-1-4-3-2-1-5-3c0-1 1-1 2-2l-1-2h0 1 2c2 1 5-1 8-2-2-1-2-1-3-2h-1l-1-1c2 0 2 0 3-1z" class="c"></path><path d="M305 801c2 0 2 0 4 1v1l-3 1-2-1 1-2z" class="X"></path><path d="M309 803c1 0 2 0 3 1l-2 3-4-3 3-1z" class="S"></path><path d="M301 798c2 0 2 1 3 2s0 1 1 1l-1 2-5-3c0-1 1-1 2-2z" class="M"></path><path d="M312 804c1 1 2 1 3 2l1 1h1l1 1v1l-5-1-3-1 2-3z" class="e"></path><path d="M317 807l1-1h0c1 1 1 1 2 1l2-1v1l1 1h1v-1h1 2l2 2h1c-1 1-2 2-3 2-2-2-6-2-9-2v-1l-1-1z" class="d"></path><path d="M324 807s0-1 1-1c0-1 0-1 2-2 1 1 1 1 2 1 2 1 4 2 5 3h1c-1 2-1 2-2 3l-1-2h-2-1l-2-2h-2-1z" class="U"></path><path d="M317 796l1 2-1 1-1 1c0 1 0 1 1 2l2 2h-4l-1 1-1-1v-1c-1-2-2-3-4-4h-1v-1l1-1 3 2h3c1-1 1-1 2-3z" class="k"></path><path d="M318 798h2 1c2 1 2 0 3 0 0 2 0 2 1 3 0 1 1 1 2 2-2 1-4 2-5 3l-1-1-1-1h-1l-2-2c-1-1-1-1-1-2l1-1 1-1z" class="r"></path><path d="M309 790h3c3 1 5 2 8 2l2 4-5-1v1h0c-1 2-1 2-2 3h-3l-3-2 5-3-1-1c-1 1-1 1-2 1-2-1-2-1-3-2h-1l-1-1c2 0 2 0 3-1z" class="U"></path><path d="M309 790h3c3 1 5 2 8 2l2 4-5-1c-3-2-6-3-9-3h-1l-1-1c2 0 2 0 3-1z" class="G"></path><path d="M378 809c3 0 6 0 8 1h0 3l2 1 1 1h0 3v1l3 3c0 1 1 1 1 2 1 1 2 1 2 2h1l1-1-2-2v-1c2 1 4 1 5 3-1 2-2 3-4 5l1 2c-1 2-3 2-5 3h-1-4c-1 0-1 1-2 1h-4l-4-2h-2c-2-2-4-2-6-2h-1s-1 0-1-1h0c-2 0-4 0-6-1l-1 1c-4 0-7-1-11-1v-1h-3l-1-2c-2 0-4 0-5-2v-1h1v-1l-1-2c1 0 2-1 2-1l1-1h3 1c2-1 4-1 6-1l5 1c1 0 2 1 3 1h4c1-1 1 0 2 0s2 0 3-1h0s-1 0-1-1c1 0 2 1 4 1 0 0 0-1 1-1-1-1-2-1-2-1l-6-1v-1h2 4z" class="n"></path><path d="M355 823h-3l-1-2c-2 0-4 0-5-2v-1h1c1 1 2 1 3 1l1 1h3v1h1l1-1c2 0 3-1 5 0h1v1l-2 1c-2 0-1-1-3-1l-1 1v1h-1z" class="e"></path><path d="M353 813c2-1 4-1 6-1l5 1c-1 2-1 2-2 3-1 0-2-1-3-1v1c1 0 1 0 2 1h0l-1 1v1c-1 0-2-1-3-2-3 0-5 0-7-1l-3 1-1-2c1 0 2-1 2-1l1-1h3 1z" class="c"></path><path d="M352 813h1l1 1c1 0 1 0 2 1h0-1-3-2v1l-3 1-1-2c1 0 2-1 2-1l1-1h3z" class="p"></path><path d="M381 822h1l11 7c-1 0-1 1-2 1h-4l-4-2h-2c-2-2-4-2-6-2h-1s-1 0-1-1h0c-2 0-4 0-6-1-1 0-2-1-2-1-2 0-2 0-4-1h5l1 1h1l1-1h12 0z" class="T"></path><path d="M381 822h1l11 7c-1 0-1 1-2 1h-4l-4-2h-2c-2-2-4-2-6-2l1-1 2 1c1-1 1-1 2-1 2 0 3 1 5 1l-4-4h0z" class="U"></path><path d="M367 814h4c1-1 1 0 2 0s2 0 3-1h0l8 4h-2l-1 1v1h0l-2 2c-2 0-3-1-5-1h0-3-1c-1-1-3 0-4-1-2 0-4-1-6-1l1-1h0c-1-1-1-1-2-1v-1c1 0 2 1 3 1 1-1 1-1 2-3 1 0 2 1 3 1z" class="Y"></path><path d="M364 813c1 0 2 1 3 1l9 3c-2 1-7 0-9 0l-1-2h-1v1c-1 1-2 1-3 1h-1 0c-1-1-1-1-2-1v-1c1 0 2 1 3 1 1-1 1-1 2-3z" class="i"></path><path d="M367 814h4c1-1 1 0 2 0s2 0 3-1h0l8 4h-2l-1 1v1h0l-5-2-9-3z" class="R"></path><path d="M378 809c3 0 6 0 8 1h0 3l2 1 1 1h0 3v1l3 3c0 1 1 1 1 2 1 1 2 1 2 2h1l1-1-2-2v-1c2 1 4 1 5 3-1 2-2 3-4 5l1 2c-1 2-3 2-5 3h-1-4l-11-7h-1l-2-1 2-2h0v-1l1-1h2l-8-4s-1 0-1-1c1 0 2 1 4 1 0 0 0-1 1-1-1-1-2-1-2-1l-6-1v-1h2 4z" class="Q"></path><path d="M392 812h3v1l3 3c-3 0-3 1-5-1l-1-3z" class="J"></path><path d="M385 817c0-1 0-1-1-2h-1l1-1c1 1 1 1 3 2s3 1 5 3c-3 0-5-1-7-2z" class="B"></path><path d="M378 809c3 0 6 0 8 1h0 3l2 1 1 1h0l1 3-3-1h-4c-1-1-1 0-2 0l-1 1h1c1 1 1 1 1 2h-1l-8-4s-1 0-1-1c1 0 2 1 4 1 0 0 0-1 1-1-1-1-2-1-2-1l-6-1v-1h2 4z" class="C"></path><path d="M386 810h3l2 1 1 1h0l1 3-3-1c-2-1-2-2-4-2h-1l1-1-1-1h1z" class="H"></path><path d="M378 811c2 0 2 0 4 1 1 0 3 1 4 0 2 0 2 1 4 2h-4c-1-1-1 0-2 0l-1 1h1c1 1 1 1 1 2h-1l-8-4s-1 0-1-1c1 0 2 1 4 1 0 0 0-1 1-1-1-1-2-1-2-1z" class="D"></path><path d="M401 816c2 1 4 1 5 3-1 2-2 3-4 5l1 2c-1 2-3 2-5 3h-1-4l-11-7h-1l-2-1 2-2h0v-1l1-1h2 1c2 1 4 2 7 2l1 1c0 1 0 1-1 2 1 1 2 1 4 1v1c2 0 3 0 4-1l1-1v-1-1h1l1-1-2-2v-1z" class="G"></path><path d="M381 819c2 0 3 0 4 1l-3 2h-1l-2-1 2-2h0z" class="a"></path><path d="M385 820c3 2 6 3 8 6v1c3 1 6 0 8-1 1-1 1-1 1-2l1 2c-1 2-3 2-5 3h-1-4l-11-7 3-2z" class="R"></path><path d="M406 762h0l2 1c0 1 1 1 1 2 1 1 1 2 1 3l-1 1v2c2 1 6-1 10 1v-1l1 1c-1 0-2 0-3 1 2 1 2 1 3 2 0 1 1 1 1 1 3-1 6-3 9-4 1 0 2 2 3 2l1 1c1 1 3 2 4 3 2 1 3 2 5 3l2 1 1-1h1 3l9-3 2-1-1-1c1-1 3-2 4-3 1 0 1-1 2-2 1 0 1-1 2-1 2 1 4 3 6 5 1 2 4 4 5 6l2 2c0 1 0 2-1 2 0 2 1 3 2 4 0 1-1 2-1 3s1 2 2 3l-2 2 1 2v1c-1 2-1 3-1 4l3 2h0c1 2 2 3 4 4h-1c3 2 5 4 7 7 1 0 3 1 4 2 2 1 4 1 5 2 2 1 3 3 4 4 3 3 8 6 12 8 2 1 3 2 5 3 5 2 10 3 14 6v1l1 3-1 3v1c-3 1-4 1-7 0h0c-1 0-2 1-3 0l-1-1c-1 0-1 0-2-1h0l-2 1-1-1c-1-1-1-1-2-1-2-1-4-2-5-4h-1 0c-1 1 0 1-1 0-1 0-1 0-2 1-2-1-3-2-4-3l-3-1h-2v-1h-1c-1 1-1 2-3 3l-3 1-1-1h-4l-1 2-13 3c0 1 1 1 2 3 2 0 7 1 9 1 3 1 5 2 8 3v1l-9-1c-7-2-14-2-21-2v1 1c-1 0-3-1-4 0 0 1 0 2 1 3l-4-2h-2l-4-1c-1 0-3-1-5-1-2-1-4-1-5-1-2 0-4-2-6-2-1 0-2-1-3-2 2-1 2-2 2-4h-5-6-11c-1-1-3-3-4-3-2 0-1 1-3 1v-3c-1 0 0 0-1-1h-2-2c-2 0-3-1-5-1h-1l-1-1h-2s0-2-1-2c0 0-2-2-2-3h0l-2-1h4c1 0 1-1 2-1h4 1c2-1 4-1 5-3l-1-2c2-2 3-3 4-5-1-2-3-2-5-3v1l2 2-1 1h-1c0-1-1-1-2-2 0-1-1-1-1-2l-3-3v-1h-3 0l-1-1-2-1s0-1 1-1 2 0 3 1h1c1-1 2-1 3-1 1 1 1 0 2 0v-1-4-10-1-3h0v-1c0-1 0-2 1-2l2-1-1-1c-1-1-1-1-2-3v-1c1-2 1-6 0-7 0-2 0-4 1-7l-2-1 2-1c1-1 3-1 4-1l2-2z" class="c"></path><path d="M470 840h2 0l2 1 1-1v1c1 1 2 1 3 1 2 0 4 1 7 1-4 1-7 2-11 2-1-1-1-2-1-2v-1c-1-1-2-1-3-2z" class="M"></path><path d="M448 797c1 2 1 4 2 6 0 0 1 1 1 2l1 1c1 2 3 5 4 8l3 8v1c-2-2-2-5-3-8h-1c-1-3-2-5-4-7l-4-8c0-1 0-2 1-3z" class="G"></path><path d="M456 814l1-1c1 1 1 1 1 2s1 2 1 2c1 2 0 3 1 5s2 1 2 4v1l-1 1c0 1 0 2-1 4h0v-2-1c-1-1-1-2-2-3h0c0-1 0-1-1-2 0-2 0-3-1-4s-2-2-2-3l2-2c1 3 1 6 3 8v-1l-3-8z" class="X"></path><path d="M451 793l1-2c2 1 2 0 3 1h1 2 2c0 1 1 2 0 3 0 2 0 3 1 5v4h-1c-1 1-2 0-3 0-2-1-3-1-4 0h0l-1-1c0-1-1-2 0-3h3l-1-3c0-2 0-3-1-4l-1 1 1 4h-3l-1-1 2-1v-2-1z" class="j"></path><path d="M460 832c1-2 1-3 1-4l1-1 1 1c1 0 2 1 2 2v1h-1l1 1h2l1 1h0 1v3 1l1-1h1l1 1h2v1h-1c0 1-1 1-1 2h0-2c1 1 2 1 3 2v1s0 1 1 2h-1c-2 0-6 0-7-1 1-1 1-1 1-2v-2h-1c-2 1-2 2-3 3v-2l-1-1h0l1-2h-1v-1l-1 1c-2-1-2-2-3-4h0l1-1h2l-1-1z" class="g"></path><path d="M460 832c1-2 1-3 1-4l1-1 1 1c0 1 0 3 1 4l-2 2h-1v-1l-1-1z" class="m"></path><path d="M465 835l2-1 1 1-2 2 1 2h1l1-1c1 1 1 1 1 2 1 1 2 1 3 2v1s0 1 1 2h-1c-2 0-6 0-7-1 1-1 1-1 1-2v-2h-1c-2 1-2 2-3 3v-2l-1-1h0l1-2h-1v-1c0-1 0-1 1-2h2z" class="e"></path><path d="M462 837c0-1 0-1 1-2h2c0 1 1 2 0 3s-1 1-2 0h0-1v-1zm1 6c1-1 1-2 3-3h1v2c0 1 0 1-1 2 1 1 5 1 7 1h1c4 0 7-1 11-2h1l4-1-1 2-13 3c0 1 1 1 2 3 2 0 7 1 9 1 3 1 5 2 8 3v1l-9-1c-7-2-14-2-21-2l-1-1-1-3c1-1 0-3 0-5z" class="f"></path><path d="M478 850c-2 0-5-1-6-2v-1h4c0 1 1 1 2 3z" class="e"></path><path d="M446 798l1 2 4 8c2 2 3 4 4 7h1l-2 2c0 1 1 2 2 3s1 2 1 4c1 1 1 1 1 2h0c1 1 1 2 2 3v1 2c-2-1-5-1-7-1l-1 1h-1 0c-1 1-1 1-2 1-3 0-7-1-9 0v-1l-1-3v-2l-1-2v-3l-1-1v-2-1c-1-2-1-4-2-5l-1-1c1-1 1-1 2-1v-1h-3v-1c2-2 5-1 7-1 1-1 1-1 1-2l-1-1c0-2 0-2 1-3h2v-4l1 1h1l1-1z" class="k"></path><path d="M460 830c-2 0-7 0-9-1l1-1h5c1 0 2 0 3 1v1z" class="L"></path><path d="M440 832l1-1c1 0 5-1 6 0 1 0 2 1 3 1h1c-1 1-1 1-2 1-3 0-7-1-9 0v-1z" class="Z"></path><path d="M451 808c2 2 3 4 4 7h0l-1-1h-1c-1 1-2 0-3-1h-1c1-2 0-3 0-4l2-1z" class="g"></path><path d="M439 827h0 11v2l-2-1c-1 1-1 1 0 2h-3v-1c-2-1-3-1-4-1l-2 1v-2z" class="Z"></path><path d="M446 798l1 2 4 8-2 1c-3-1-3-5-4-7l-1 1-1-1v-4l1 1h1l1-1z" class="n"></path><path d="M452 832l1-1c2 0 5 0 7 1h0l1 1h-2l-1 1h0c1 2 1 3 3 4l1-1v1h1l-1 2h0l1 1v2c0 2 1 4 0 5l1 3 1 1v1 1c-1 0-3-1-4 0 0 1 0 2 1 3l-4-2h-2l-4-1c-1 0-3-1-5-1-2-1-4-1-5-1-2 0-4-2-6-2-1 0-2-1-3-2 2-1 2-2 2-4h-5-6c1 0 0 0 1-1h1v-1c1-2 2-2 4-2h0 4v-1l2 1c0-1 1-1 2-1h1c0-1 0-2 1-3 0 0 0-1 1-1l-1-2c2-1 6 0 9 0 1 0 1 0 2-1h0 1z" class="t"></path><path d="M447 845h2l2 1 2 2c-2 1-3 1-5 1h-3l2-1v-2-1z" class="S"></path><path d="M441 841h0c2 0 9-1 11 0l-1 1-1 2c1 1 2 0 1 2l-2-1h-2-4-2v-4z" class="U"></path><path d="M441 841c2 0 9-1 11 0l-1 1-9 1h-1v-2zm11-9l1-1c2 0 5 0 7 1h0l1 1h-2l-1 1h0c1 2 1 3 3 4l1-1v1c-1 1-1 1-2 1h-2c-3 0-4 0-5 1 0-1 0-3 1-4l1-1h0l-2-1-1-2z" class="Z"></path><path d="M462 838h1l-1 2h0l1 1v2c0 2 1 4 0 5-1 0-2 0-3-1h-5-1l-1-1v-6c1-1 2-1 5-1h2c1 0 1 0 2-1z" class="i"></path><path d="M462 838h1l-1 2h0l1 1c-2-1-6 0-9 0l-1 1 1 1 5 1v1c-2 0-3-1-6 1v-6c1-1 2-1 5-1h2c1 0 1 0 2-1zm-11-6h0c0 2 0 3-1 4h-3v1c1 0 2 0 2 1 2 0 2 0 3 1v2c-2-1-9 0-11 0h0c0-2-1-3-1-5 0 0 0-1 1-1l-1-2c2-1 6 0 9 0 1 0 1 0 2-1z" class="d"></path><path d="M430 840h0 4v-1l2 1c0-1 1-1 2-1h1c0-1 0-2 1-3 0 2 1 3 1 5v4h2 4v1 2l-2 1c-2-1-4-1-6-2-1-2-2-2-4-3h-5-6c1 0 0 0 1-1h1v-1c1-2 2-2 4-2z" class="M"></path><path d="M430 844c1-1 2-1 4-2 1 0 1 0 2-1 2 1 2 1 3 3v3c-1-2-2-2-4-3h-5z" class="p"></path><path d="M435 844c2 1 3 1 4 3 2 1 4 1 6 2h3c4 1 9 1 13 2l2 1c1 0 2 1 2 1v1c-1 0-3-1-4 0 0 1 0 2 1 3l-4-2h-2l-4-1c-1 0-3-1-5-1-2-1-4-1-5-1-2 0-4-2-6-2-1 0-2-1-3-2 2-1 2-2 2-4z" class="V"></path><path d="M452 854c2-1 2-2 4-2v3l-4-1z" class="B"></path><path d="M461 851l2 1c1 0 2 1 2 1v1c-1 0-3-1-4 0 0 1 0 2 1 3l-4-2c-1-1-1-2-1-4h4z" class="C"></path><path d="M424 810c1-1 3 0 5 0l1 1 1 1 2-2h3v1c-1 0-1 0-2 1l1 1c1 1 1 3 2 5v1 2l1 1v3l1 2v2l1 3v1l1 2c-1 0-1 1-1 1-1 1-1 2-1 3h-1c-1 0-2 0-2 1l-2-1v1h-4 0c-2 0-3 0-4 2v1h-1c-1 1 0 1-1 1h-11c-1-1-3-3-4-3-2 0-1 1-3 1v-3c-1 0 0 0-1-1h-2-2c-2 0-3-1-5-1h-1l-1-1h-2s0-2-1-2c0 0-2-2-2-3h0l-2-1h4c1 0 1-1 2-1h4 1c2-1 4-1 5-3l-1-2c2-2 3-3 4-5-1-2-3-2-5-3v1l2 2-1 1h-1c0-1-1-1-2-2 0-1-1-1-1-2l-3-3c6 0 11 1 16 2 1-1 2-1 2-1 1 0 1-2 2-2 0-1 4-1 4-1 2 0 3-1 4-1h1z" class="L"></path><path d="M401 816l-1-1 1-1 5 4c1 0 3 1 3 2 1 2 1 6 3 7h1c1-1 2-1 3 0v2l2-1v1l-2 1v1h-1c-2 1-4 2-7 2l-1-1v-2c1-1 1 0 1-1v-1h-1 0v-2h-1v-2c1 0 1 0 1-1s0-2-1-4-3-2-5-3z" class="k"></path><path d="M408 828l1-1h1v3h0 2s1 1 2 1l2-2 2-1v1l-2 1v1h-1c-2 1-4 2-7 2l-1-1v-2c1-1 1 0 1-1v-1z" class="M"></path><path d="M424 810h2v2c-3 0-6 2-8 4h0l-2 4v4h-2c-4-1-2-4-4-6-1 0-1-1-2-2h0 3v-1c1-1 2-1 2-1 1 0 1-2 2-2 0-1 4-1 4-1 2 0 3-1 4-1h1z" class="B"></path><path d="M424 810h2v2c-3 0-6 2-8 4h0l-2 2c-1 0-2 0-3-1v-1s1-1 2-1c2-1 3-2 4-4h0c2 0 3-1 4-1h1z" class="I"></path><path d="M424 810c1-1 3 0 5 0l1 1 1 1c2 2 2 4 3 7l-2 2c-1-1-1-1-1-2l1-1-2-1h-1l-1 2-1-1h-1c-1 1-1 2-1 3l-1 1h-1l-1 1c-1 1-1 1-1 2l-1 1s-1 1-2 1h-2l1-1c1 0 1 0 2-1l-1-2c0-2 1-1 2-2 1 0 2-1 2-1l-1-2 1-1c1-1 2-2 3-2l1-3v-2h-2z" class="i"></path><path d="M424 810c1-1 3 0 5 0l1 1 1 1c2 2 2 4 3 7l-2 2c-1-1-1-1-1-2l1-1v-1c0-1 0-1-1-2-2-1-4-1-6 0l1-3v-2h-2z" class="N"></path><path d="M433 810h3v1c-1 0-1 0-2 1l1 1c1 1 1 3 2 5v1 2l1 1v3l1 2v2l1 3v1l1 2c-1 0-1 1-1 1-1 1-1 2-1 3h-1c-1 0-2 0-2 1l-2-1v1h-4 0c-1-1-2-2-3-1-1 0-1 1-3 1v-2-4c-1-1 0-2 1-3v-2h-2 0-1c-1 0-3-1-4-1l-2 1v-2h2c1 0 2-1 2-1l1-1c0-1 0-1 1-2l1-1h1l1-1c0-1 0-2 1-3h1l1 1 1-2h1l2 1-1 1c0 1 0 1 1 2l2-2c-1-3-1-5-3-7l2-2z" class="n"></path><path d="M425 831l1-1c1 1 1 2 1 3h0c0 1-1 2 0 2v1s-2 2-3 2v-4c-1-1 0-2 1-3z" class="f"></path><path d="M428 819l1-2h1l2 1-1 1c0 1 0 1 1 2l2-2c1 1 1 2 0 3l2-1c1 1 0 2 1 3v3c0 2 2 3 1 5h-1l1 1v2h0c-2 0-2 0-3-1h-1c-2 1-2 1-4 0-1 0-2 0-2-1l5-4c2-2 3-2 3-4l-2-1c0-1 0 0-1-1l-2 2h-1v-2c-1-1-2-1-2-2v-1-1z" class="U"></path><path d="M425 821c0-1 0-2 1-3h1l1 1v1 1c0 1 1 1 2 2v2h1l2-2c1 1 1 0 1 1l2 1c0 2-1 2-3 4l-5 4h-1 0c0-1 0-2-1-3l-1 1v-2h-2 0-1c-1 0-3-1-4-1l-2 1v-2h2c1 0 2-1 2-1l1-1c0-1 0-1 1-2l1-1h1l1-1z" class="S"></path><path d="M426 829h0c2 0 3-1 4-2h2l1 2-5 4h-1 0c0-1 0-2-1-3l-1 1v-2h1z" class="W"></path><path d="M425 821c1 1 1 3 1 4h-2l-1 1 1 1h2v2h-1-2 0-1c-1 0-3-1-4-1l-2 1v-2h2c1 0 2-1 2-1l1-1c0-1 0-1 1-2l1-1h1l1-1z" class="d"></path><path d="M406 819c1 2 1 3 1 4s0 1-1 1v2h1v2h0 1v1c0 1 0 0-1 1v2l1 1c3 0 5-1 7-2h1v-1l2-1v-1c1 0 3 1 4 1h1 0 2v2c-1 1-2 2-1 3v4 2c2 0 2-1 3-1 1-1 2 0 3 1-2 0-3 0-4 2v1h-1c-1 1 0 1-1 1h-11c-1-1-3-3-4-3-2 0-1 1-3 1v-3c-1 0 0 0-1-1h-2-2c-2 0-3-1-5-1h-1l-1-1h-2s0-2-1-2c0 0-2-2-2-3h0l-2-1h4c1 0 1-1 2-1h4 1c2-1 4-1 5-3l-1-2c2-2 3-3 4-5z" class="c"></path><path d="M418 828c1 0 3 1 4 1h1 0 2v2c-1 1-2 2-1 3l-1 1c0-1-1-2-2-3s-3-1-5-1v-1l2-1v-1z" class="X"></path><path d="M405 838l1-1 7 1h1c0 1 1 1 3 2 1-1 2-2 2-4l-2-2v-1h3 1c0 1 0 1 2 1v1l1-1v4 2c-1 0-2 0-3-1h-1c0 1 0 1-1 2 0 0-1 0-1-1-2 0-2 0-4 1-2-1-5-2-7-2h-1c-1 0 0 0-1-1z" class="p"></path><path d="M424 840c2 0 2-1 3-1 1-1 2 0 3 1-2 0-3 0-4 2v1h-1c-1 1 0 1-1 1h-11c-1-1-3-3-4-3-2 0-1 1-3 1v-3h1c2 0 5 1 7 2 2-1 2-1 4-1 0 1 1 1 1 1 1-1 1-1 1-2h1c1 1 2 1 3 1z" class="e"></path><path d="M406 819c1 2 1 3 1 4s0 1-1 1v2h1v2h0 1v1c0 1 0 0-1 1v2l1 1c-5 0-10 1-15 1v-1h-1c-1-1-2-2-3-2h0l-2-1h4c1 0 1-1 2-1h4 1c2-1 4-1 5-3l-1-2c2-2 3-3 4-5z" class="g"></path><path d="M387 830h4c1 0 1-1 2-1l2 2h3c2-1 2-1 4 0-2 1-2 1-4 1h-1-4v1h-1c-1-1-2-2-3-2h0l-2-1z" class="d"></path><path d="M406 819c1 2 1 3 1 4s0 1-1 1v2c0 1-1 2-2 3 0 1-2 1-2 1v1c-2-1-2-1-4 0h-3l-2-2h4 1c2-1 4-1 5-3l-1-2c2-2 3-3 4-5z" class="Y"></path><path d="M457 808c7 1 14 0 21 1 3 0 6 0 9 1 3 2 5 4 7 7 1 0 3 1 4 2 2 1 4 1 5 2 2 1 3 3 4 4 3 3 8 6 12 8 2 1 3 2 5 3 5 2 10 3 14 6v1l1 3-1 3v1c-3 1-4 1-7 0h0c-1 0-2 1-3 0l-1-1c-1 0-1 0-2-1h0l-2 1-1-1c-1-1-1-1-2-1-2-1-4-2-5-4h-1 0c-1 1 0 1-1 0-1 0-1 0-2 1-2-1-3-2-4-3l-3-1h-2v-1h-1c-1 1-1 2-3 3l-3 1-1-1h-4l-4 1-2-2h-3l1-3c-1 0-2-1-2-1-2 0-2 0-2-1l-2 1c0-1 0-1-1-2 0 0-1 1-2 1v-2h0c-1-1-2-2-3-2h-1v-1l1-1-1-1c-1 1-1 1-2 0v-1c-1-1-1-2-1-3h0l-2-1-1-2c-1-1-1-1-1-2 1-1 1-2 2-3h0v-1-1c-1-1-1 0-2-1l-1 1-2-2h0v-1h1l-1-1c-1 0-2 1-3 0 0-1 0-1 1-3z" class="V"></path><path d="M479 828c1-3 3-1 6-2h1c1 1 2 3 2 5h-3v-1c0-1 0-1-1-2h0c-2 1-3 1-5 0h0z" class="E"></path><path d="M478 809c3 0 6 0 9 1 3 2 5 4 7 7-1 0-1 1-2 1-1-1-2-2-4-2-1-1-1-2-2-2-1-1-2 0-2 0-1-1-2-2-3-2-3-1-6 0-8-1v-1h1c1 1 2 0 4 0h-3l3-1z" class="b"></path><path d="M493 829c-1 0-2-1-2-1l1-1c1 0 1 1 2 1 1 1 1 1 2 1 1 1 1 1 1 2h1l3 4 1 1c-1 1-1 1-2 1h-2v-1h-1-2c-1 0-2-1-2-2l-3 1h0c-1-1-2-2-3-2l-1 1-2-2h4l1-2c2-1 2-1 4-1z" class="B"></path><path d="M493 829v2c-2 1-3 1-5 1l1-2c2-1 2-1 4-1z" class="F"></path><path d="M472 818h2 0c3 0 4 1 6 2 0 1 1 1 2 1 1 1 1 1 2 1v1h2l1 1c1 0 1 1 2 1s1 1 2 1l-1 1h0l-2-1h-2-1c-3 1-5-1-6 2l-2 1h-1v-2c-1-1-1 0-2-1v-1c0-1 0-1-1-2h0l-1-1 1-2h-1v-2z" class="D"></path><path d="M472 818h2 0l1 1c1 1 2 1 3 2l2 1 1 1v1h-1c-1 0-1 0-2 1-2-2-2-2-5-2h0l-1-1 1-2h-1v-2z" class="E"></path><path d="M464 815c1 0 4-1 6 0h0l2 2v1 2h1l-1 2 1 1h0c1 1 1 1 1 2v1c1 1 1 0 2 1v2h1 3c0 1 1 1 2 1h1s1 1 1 2l2 2 1-1c1 0 2 1 3 2h0l3-1c0 1 1 2 2 2h2 1v1h2c1 0 1 0 2-1v1 2h-1c-1 1-1 2-3 3l-3 1-1-1h-4l-4 1-2-2h-3l1-3c-1 0-2-1-2-1-2 0-2 0-2-1l-2 1c0-1 0-1-1-2 0 0-1 1-2 1v-2h0c-1-1-2-2-3-2h-1v-1l1-1-1-1c-1 1-1 1-2 0v-1c-1-1-1-2-1-3h0l-2-1-1-2c-1-1-1-1-1-2 1-1 1-2 2-3h0v-1-1z" class="K"></path><path d="M467 828c2 0 2 0 3-1l1 1-1 2 1 1c1-1 2-1 3 0 1 0 1 0 1 1l2-1h0c0 2 1 3 3 3h0l1 1 2-1v2c1 1 2 1 4 1l1-1c1 1 1 2 2 3-1 0-1 1-1 2-2-1-3-1-4-1h-1l-1-2h-1c-1 0-2-1-2-1-2 0-2 0-2-1l-2 1c0-1 0-1-1-2 0 0-1 1-2 1v-2h0c-1-1-2-2-3-2h-1v-1l1-1-1-1c-1 1-1 1-2 0v-1z" class="q"></path><path d="M464 815c1 0 4-1 6 0h0l2 2v1 2h1l-1 2 1 1h0c1 1 1 1 1 2v1l-2-1-1 1-1-1 1-1c-2-2-3-1-4-2-1 0-2 1-3 2h0l-1-2c-1-1-1-1-1-2 1-1 1-2 2-3h0v-1-1z" class="Q"></path><path d="M472 820h-2l-2-2h-1v-1c2-1 3 0 5 0v1 2z" class="F"></path><path d="M490 835l3-1c0 1 1 2 2 2h2 1v1h2c1 0 1 0 2-1v1 2h-1c-1 1-1 2-3 3l-3 1-1-1h-4l-4 1-2-2h-3l1-3h1l1 2h1c1 0 2 0 4 1 0-1 0-2 1-2-1-1-1-2-2-3l2-1h0z" class="R"></path><path d="M490 835h0c1 1 1 2 2 3s1 2 1 3h1 1-2c-1-1-1-1-1-2l-1 1-1-1c-1-1-1-2-2-3l2-1z" class="L"></path><path d="M498 836v1h2c1 0 1 0 2-1v1 2h-1-1 0c-1 1-2 1-3 2l-1-1c1-1 2-2 2-4z" class="G"></path><path d="M482 838h1l1 2h1c1 0 2 0 4 1 0-1 0-2 1-2l1 1 1-1c0 1 0 1 1 2h2 2c1-1 2-1 3-2h0 1c-1 1-1 2-3 3l-3 1-1-1h-4l-4 1-2-2h-3l1-3z" class="Y"></path><path d="M481 812c1 0 2 1 3 2 0 0 1-1 2 0 1 0 1 1 2 2 2 0 3 1 4 2 1 0 1-1 2-1s3 1 4 2c2 1 4 1 5 2 2 1 3 3 4 4 3 3 8 6 12 8 2 1 3 2 5 3 5 2 10 3 14 6v1l1 3-1 3v1c-3 1-4 1-7 0h0c-1 0-2 1-3 0l-1-1c-1 0-1 0-2-1h0l-2 1-1-1c-1-1-1-1-2-1-2-1-4-2-5-4h-1 0c-1 1 0 1-1 0-1 0-1 0-2 1-2-1-3-2-4-3l-3-1h-2v-1-2-1l-1-1-3-4v-1l-1-1-3-3v-1c-1 0-2-1-2-1-1 0-2 0-2-1-2 0-4-1-5-2h-1c-1-1-3-1-4-3l2-2h-3c0 1 0 0-1 1-2-2-5-3-7-4 3 0 6 0 9-1h1z" class="H"></path><path d="M495 823c2 0 4 0 6 1 2 2 4 4 7 6-1 1 0 1-1 1-3-3-6-1-9-3 0-1-1-2-2-2-1-1-2-2-2-3h1z" class="I"></path><path d="M495 823c2 0 4 0 6 1l-1 1c1 1 2 2 2 3-1 0-1-1-2-1-1-1-1-1-2-1l-3-3z" class="C"></path><path d="M482 816c3 1 4 2 6 3s3 2 4 3c1 0 1 1 2 1 0 1 1 2 2 3 1 0 2 1 2 2 3 2 6 0 9 3v1 1h1c1 2 0 2 0 3v2l2-1c1-1 1-1 2-1l-1 2c0 1 0 2 1 3l2 2h0c-1 1 0 1-1 0-1 0-1 0-2 1-2-1-3-2-4-3l-3-1h-2v-1-2-1l-1-1-3-4v-1l-1-1-3-3v-1c-1 0-2-1-2-1-1 0-2 0-2-1-2 0-4-1-5-2h-1c-1-1-3-1-4-3l2-2z" class="E"></path><path d="M504 840h1v-2c0-2 1-2 2-3v1c0 1 1 2 0 3v2l-3-1z" class="C"></path><path d="M512 836c1 0 2-2 3-2s1 1 2 1c0 1 1 1 2 2l1 1v-1c1-1 3-1 4-1 5 2 10 3 14 6v1l1 3-1 3v1c-3 1-4 1-7 0h0c-1 0-2 1-3 0l-1-1c-1 0-1 0-2-1h0l-2 1-1-1c-1-1-1-1-2-1-2-1-4-2-5-4h-1l-2-2c-1-1-1-2-1-3l1-2z" class="D"></path><path d="M515 843c2 0 2 1 3 2h3c1 0 1 0 2 1l-1 2c-1-1-1-1-2-1-2-1-4-2-5-4z" class="K"></path><path d="M512 836c1 0 2-2 3-2s1 1 2 1c0 1 1 1 2 2v1 2l-1 1-2-2c-1 0-1 0-2 1-2 0-2-1-3-2l1-2z" class="V"></path><path d="M520 841h1c2 0 4 1 5 2h1 2c1 0 1 1 2 1v1c0 1-1 2-2 3h0v1h-1v-1c-1-1-2-1-3-1h0-1c0-1 1-2 1-2-2-2-3-2-5-4z" class="I"></path><path d="M535 845h0c2-1 2-2 3-2l1 3-1 3v1c-3 1-4 1-7 0v-2c1-1 2-2 4-3z" class="F"></path><path d="M520 838v-1c1-1 3-1 4-1 5 2 10 3 14 6v1c-1 0-1 1-3 2h0c-2-1-3-2-5-2-4-2-7-4-10-5z" class="J"></path><path d="M406 762h0l2 1c0 1 1 1 1 2 1 1 1 2 1 3l-1 1v2c2 1 6-1 10 1v-1l1 1c-1 0-2 0-3 1 2 1 2 1 3 2 0 1 1 1 1 1 3-1 6-3 9-4 1 0 2 2 3 2l1 1c1 1 3 2 4 3 2 1 3 2 5 3l2 1 1-1h1 3l9-3 2-1-1-1c1-1 3-2 4-3 1 0 1-1 2-2 1 0 1-1 2-1 2 1 4 3 6 5 1 2 4 4 5 6l2 2c0 1 0 2-1 2 0 2 1 3 2 4 0 1-1 2-1 3s1 2 2 3l-2 2 1 2v1c-1 2-1 3-1 4l3 2h0c1 2 2 3 4 4h-1c-3-1-6-1-9-1-7-1-14 0-21-1l-1-1h1 4l1-1c-1 0-1 0-2-1h1v-2-3c-1-2-1-3-1-5 1-1 0-2 0-3h-2-2-1c-1-1-1 0-3-1l-1 2v1h0c-1 1-1 0-2 1l-1-1v1 2c-1 1-1 2-1 3l-1-2-1 1h-1l-1-1v4h-2c-1 1-1 1-1 3l1 1c0 1 0 1-1 2-2 0-5-1-7 1v1l-2 2-1-1-1-1c-2 0-4-1-5 0h-1c-1 0-2 1-4 1 0 0-4 0-4 1-1 0-1 2-2 2 0 0-1 0-2 1-5-1-10-2-16-2v-1h-3 0l-1-1-2-1s0-1 1-1 2 0 3 1h1c1-1 2-1 3-1 1 1 1 0 2 0v-1-4-10-1-3h0v-1c0-1 0-2 1-2l2-1-1-1c-1-1-1-1-2-3v-1c1-2 1-6 0-7 0-2 0-4 1-7l-2-1 2-1c1-1 3-1 4-1l2-2z" class="T"></path><path d="M416 784l1-1c1 1 2 2 2 3l1 1v1h-1-1-3c0-1 0-2 1-4z" class="l"></path><path d="M405 786l3-1h2c1 1 1 1 2 1v-2c2 0 2-1 4 0-1 2-1 3-1 4h-1l-1 2h-2c-1-1-1-1-1-2h-1-1c0-1-2-2-3-2z" class="n"></path><path d="M408 772c1 0 3 1 4 1h3l2 1v-1c2 1 2 1 3 2 0 1 1 1 1 1l-12 6h-1l1-1-2-2 1-2v-5z" class="S"></path><path d="M408 777c1-1 3-1 4-2l1 1v1l-2 3c-1 0-1 0-2 1l-2-2 1-2z" class="m"></path><path d="M439 786c-3 1-3-1-5-2l-1 2c-1-2-1-3-2-4h-2l-1 4h-1c0-2-1-3-2-4v1h-1v-1c1-1 2-2 3-2v-1h0c-2 1-4 2-6 2l5-3c1-1 2-2 3-2l3 1 7 7v2h0z" class="I"></path><path d="M411 804c3 0 4-1 6-1l1-1c1-3-1-8 1-11v1 10l-1 1h1 1c1-4 0-7 1-11h1c2 1 1 2 2 4h0v-2c2-2 2-2 4-2v1 8l-2-1h-1l-1-2h-1v1l2 2c1 1 1 1 1 2l-2 2-1 1-1-1c-2 0-2 0-2 1-1 0-1-1-2-1h0-2l-1 1c-1 0-1 0-2-1h-3c0-1 0-1 1-1z" class="K"></path><path d="M438 792h2c1 1 1 2 1 4h0c1 1 1 1 1 2v1c-1 1-1 2-1 3-1 1-1 1-1 3l1 1c0 1 0 1-1 2-2 0-5-1-7 1l-2-2v-2c-1-1-2 0-3 0v-4-8h5v1 1c2-1 3-2 5-2v-1z" class="n"></path><path d="M438 793c-1 1-1 2 0 3v2c-1 2-1 4-1 5l1 1c-1 2-1 1-1 2l-2-1h1v-6c-3-2-3-2-6-2 0-2-1-2 0-3 0-1 1 0 2 0v1h1c2-1 3-2 5-2z" class="g"></path><path d="M438 792h2c1 1 1 2 1 4h0c1 1 1 1 1 2v1c-1 1-1 2-1 3-1 1-1 1-1 3-1 0-1-1-2-1l-1-1c0-1 0-3 1-5v-2c-1-1-1-2 0-3v-1z" class="p"></path><path d="M406 762h0l2 1c0 1 1 1 1 2 1 1 1 2 1 3l-1 1v2c2 1 6-1 10 1v-1l1 1c-1 0-2 0-3 1v1l-2-1h-3c-1 0-3-1-4-1v5l-1 2 2 2-1 1h1c-1 1-4 2-5 4h1l-2 1-1-1-1-1c-1-1-1-1-2-3v-1c1-2 1-6 0-7 0-2 0-4 1-7l-2-1 2-1c1-1 3-1 4-1l2-2z" class="Z"></path><path d="M403 775l1-1c0-1 1 0 0-1 0-1 0-1 1-2 0 0 1 0 1-1 1 2 1 3 1 5h-3-1z" class="S"></path><path d="M407 779l2 2-1 1h1c-1 1-4 2-5 4h1l-2 1-1-1-1-1c1-2 2-3 4-4 1 0 1-1 2-2z" class="n"></path><path d="M406 762h0l2 1c0 1 1 1 1 2 1 1 1 2 1 3l-1 1v2c2 1 6-1 10 1v-1l1 1c-1 0-2 0-3 1v1l-2-1h-3c-1 0-3-1-4-1v-3l-2 1c0 1-1 1-1 1-1 1-1 1-1 2 1 1 0 0 0 1l-1 1v1l-1 1h-1c0-1 1-2 1-3v-6h-1l-1-1-2-1 2-1c1-1 3-1 4-1l2-2z" class="d"></path><path d="M404 764c2 0 2 0 3 1v2c-1 0-2 0-3-1-1 1-2 1-2 2h-1l-1-1-2-1 2-1c1-1 3-1 4-1z" class="L"></path><path d="M402 786l1 1 2-1h0c1 0 3 1 3 2h1v2c-1 1-1 1-1 2l2-1c1 3-2 11 1 13-1 0-1 0-1 1h3c1 1 1 1 2 1l1-1h2 0c1 0 1 1 2 1 0-1 0-1 2-1l1 1 1-1 2-2c0-1 0-1-1-2l-2-2v-1h1l1 2h1l2 1v4c1 0 2-1 3 0v2l2 2v1l-2 2-1-1-1-1c-2 0-4-1-5 0h-1c-1 0-2 1-4 1 0 0-4 0-4 1-1 0-1 2-2 2 0 0-1 0-2 1-5-1-10-2-16-2v-1h-3 0l-1-1-2-1s0-1 1-1 2 0 3 1h1c1-1 2-1 3-1 1 1 1 0 2 0v-1-4-10-1-3h0v-1c0-1 0-2 1-2l2-1z" class="U"></path><path d="M405 786h0c1 0 3 1 3 2l-1 1 1 1h-4l-1 1-1-1v-1l1-2 2-1z" class="S"></path><path d="M400 795c2 0 3 0 3 2h1l-1 2c0 1 1 1 2 1l1-1c0 2 0 2-1 3s-1 1-2 1l-2 1v-1c1-2 0-4-1-6v-2z" class="W"></path><path d="M408 792l2-1c1 3-2 11 1 13-1 0-1 0-1 1h3c-2 1-5 1-7 0-1 0-1 0-2-1 2 0 2 0 4-1v-7h-2v-2l1-1c0 2 0 2 1 2v-3z" class="G"></path><path d="M428 805c1 0 2-1 3 0v2l2 2v1l-2 2-1-1-1-1c-2 0-4-1-5 0h-1-3c1-2 3-1 5-2 1-1 1-1 1-2 1 0 1-1 2-1z" class="e"></path><path d="M402 786l1 1-1 2v1l1 1c0 1 0 1-1 2h-1l-1 1v1 2c1 2 2 4 1 6v1c1 1 1 2 2 3l-2 2c0 1 0 1 1 2l3-3 1 1c1-1 1-1 2-1v1c1 1 1 1 1 3h6c-1 0-1 2-2 2 0 0-1 0-2 1-5-1-10-2-16-2v-1h-3 0l-1-1-2-1s0-1 1-1 2 0 3 1h1c1-1 2-1 3-1 1 1 1 0 2 0v-1-4-10-1-3h0v-1c0-1 0-2 1-2l2-1z" class="M"></path><path d="M391 811c4 2 13 0 18 1h6c-1 0-1 2-2 2 0 0-1 0-2 1-5-1-10-2-16-2v-1h-3 0l-1-1z" class="b"></path><path d="M466 771c1 0 1-1 2-1 2 1 4 3 6 5 1 2 4 4 5 6l2 2c0 1 0 2-1 2 0 2 1 3 2 4 0 1-1 2-1 3s1 2 2 3l-2 2 1 2v1c-1 2-1 3-1 4l3 2h0c1 2 2 3 4 4h-1c-3-1-6-1-9-1-7-1-14 0-21-1l-1-1h1 4l1-1c-1 0-1 0-2-1h1v-2-3c-1-2-1-3-1-5 1-1 0-2 0-3h-2-2-1c-1-1-1 0-3-1l-1 2v1h0c-1 1-1 0-2 1l-1-1v1 2c-1 1-1 2-1 3l-1-2-1 1h-1l-1-1v4h-2c0-1 0-2 1-3v-1c0-1 0-1-1-2h0c0-2 0-3-1-4h-2-2v-1h4c0-1 1-1 3-1v1c1 0 1 0 2-1l1 1 2-1c-1-1-1-1-1-2h-2v-1l-1-1c-1 1 0 1-1 1-2-1-2 0-4-1h0c1-2 2-2 3-3v-1c1 0 1 1 2 1v1l1 1 2-2h1 0l1-1h0c-1 0-1 0-2-1h0 3l9-3 2-1-1-1c1-1 3-2 4-3 1 0 1-1 2-2z" class="V"></path><path d="M475 792h1l2 2-1 2 1 2-2 2-1-1c0-2-1-4 0-7zm-7-14c2 0 4 1 6 2 1 1 2 1 2 3h-2c-1 0-1 0-2-1l-1 1c1 1 1 2 2 2v3h-6c0-1-1-2-1-3v-2c0-1 0-2-1-3h0c1-1 2-1 3-2z" class="U"></path><path d="M466 771c1 0 1-1 2-1 2 1 4 3 6 5 1 2 4 4 5 6l2 2c0 1 0 2-1 2 0 2 1 3 2 4 0 1-1 2-1 3s1 2 2 3l-2 2 1 2v1c-1 2-1 3-1 4l-2 1v-12-6c-1-1-1-4-1-5-1-4-6-7-8-9l-1-1c-3 1-5 3-8 5h0l-1-1c1-1 3-2 4-3 1 0 1-1 2-2z" class="l"></path><path d="M473 792v7c-1-1-1-2-2-3h-1c-1 2-1 1 0 2l1 5s1 0 2-1l1 1h1 3v1l-1 2 1 1c1-1 1-1 1-2l2-1 3 2h0c1 2 2 3 4 4h-1c-3-1-6-1-9-1-7-1-14 0-21-1l-1-1h1 4l1-1c-1 0-1 0-2-1h1v-2l1-1v3h6c1-2 1-5 0-7v-4c1-1 1-1 2-1s1 0 2 1v-1l1-1z" class="M"></path><path d="M459 778c1 0 2 0 4-1h2l1-1h1v1l1 1c-1 1-2 1-3 2h0c1 1 1 2 1 3v2c0 1 1 2 1 3h-4-2c-1 0-1-1-3 0l-1-1c-1 1-1 1-2 1h-1-2-1v1 1h-3c-1-1-1-1-1-2h-2v-1l-1-1c-1 1 0 1-1 1-2-1-2 0-4-1h0c1-2 2-2 3-3v-1c1 0 1 1 2 1v1l1 1 2-2h1 0l1-1h0c-1 0-1 0-2-1h0 3l9-3z" class="c"></path><g class="l"><path d="M452 783h2 2 0c1 1 1 3 1 4h-2c0-1-1-1-1-2h-2-1l1-2z"></path><path d="M458 788c0-2 0-4-1-5 0-1 0-2 1-2h2l1-1h2l1-1 1 1h0c1 1 1 2 1 3v2c0 1 1 2 1 3h-4-2c-1 0-1-1-3 0z"></path></g><path d="M463 788l-1-2-1-2 1-1h1c1 0 1 0 2-1-1 0-1-1-1-2h1c1 1 1 2 1 3v2c0 1 1 2 1 3h-4z" class="p"></path><path d="M451 790h1 3c2-1 6-1 8-1h1 1c4 0 9 0 13 1v1h-1 0-3 0l-1 1-1 1v1c-1-1-1-1-2-1s-1 0-2 1v4c1 2 1 5 0 7h-6v-3l-1 1v-3c-1-2-1-3-1-5 1-1 0-2 0-3h-2-2-1c-1-1-1 0-3-1l-1 2v1h0c-1 1-1 0-2 1l-1-1v1 2c-1 1-1 2-1 3l-1-2-1 1h-1l-1-1v4h-2c0-1 0-2 1-3v-1c0-1 0-1-1-2h0c0-2 0-3-1-4h-2-2v-1h4c0-1 1-1 3-1v1c1 0 1 0 2-1l1 1 2-1h3z" class="d"></path><path d="M446 798c-1-2-2-3-2-5v-1h5c1 1 0 1 1 1h1v1h0c-1 1-1 0-2 1l-1-1v1 2c-1 1-1 2-1 3l-1-2z" class="L"></path><path d="M462 802c-1-2 0-4 0-6l1 1h0v-2-4h1c1 2 1 4 1 7h0-2v1 1h3 1l-1-7v-1l2-1v3 4c1 2 1 5 0 7h-6v-3z" class="B"></path><path d="M414 697l2-1c0-1 2-3 3-4v-5-14-63l-1-335h-49c-46 4-85 28-114 63-15 18-29 38-39 60-2 3-4 8-5 12-1 2-1 5-1 7 0 5 0 10 1 15 14-39 36-76 66-105 23-21 49-36 80-41 16-3 33-3 49-3v1c-1 1-2 2-3 2-6 1-13 0-19 0-16 1-32 2-48 8-55 18-92 70-116 120-4 8-7 17-10 25-1 4-2 7-4 10 1-7 1-14 1-22v-27-92-76-22-9c-1-4-3-8-5-10l-10-19h327l5-35 5 34 1 1c6-1 13 0 19 0h33 196c7 0 50-1 53 0h0c0 3-2 6-4 9l-7 12c0 2-2 5-2 7-2 6-1 13-1 19v44 84l1 42c-21-46-59-87-107-105-7-3-15-5-23-7-13-2-26-3-38-3h-40l-3 561c1 1 1 1 2 1l7-8c4-29 2-58 2-87V622 396v-74-39h31c7 0 15 0 22 1 36 3 69 21 95 46 28 28 49 65 60 104h-1c-4-11-7-21-12-31-20-44-52-84-97-104-14-6-28-11-44-12-17-2-34-1-52-1v405 99l-1 24c0 4-1 10-1 15 2-1 3-2 4-2l1 1h1v-2l1-1v2l1 1 2 1c1 0 1 0 2-1 2 1 3 2 4 5 1-2 1-2 3-2v2h-2c0 1 0 1 1 2s0 4 1 6c-1 2-1 4-1 6v1l-1 1h-1-2l-1-1-1 1h-1v1 5c-2 1-1 2-4 2h-1l-1 2h-3c4 1 7 1 11 3l-2 2 1 2c-1 0-1-1-2-1l-2 1h0l-6-2c-3-2-7 0-10-1h0c-2-1-4-1-6 0h-3c-4-1-6-1-9-1v1c-1 0-4 0-5 1l1 1-1 1c-2-1-3-1-4-1l-7-1h-4-1c-4-1-7-2-10-3h-1c-1-1-6-3-9-4-1 0-3-1-5-2-3 0-6-2-9-3l1-2v-1l1-3-1-3v-1c-4-3-9-4-14-6-2-1-3-2-5-3-4-2-9-5-12-8-1-1-2-3-4-4-1-1-3-1-5-2-1-1-3-2-4-2-2-3-4-5-7-7h1c-2-1-3-2-4-4h0l-3-2c0-1 0-2 1-4v-1l-1-2 2-2c-1-1-2-2-2-3s1-2 1-3c-1-1-2-2-2-4 1 0 1-1 1-2l-2-2c-1-2-4-4-5-6-2-2-4-4-6-5-1 0-1 1-2 1-1 1-1 2-2 2-1 1-3 2-4 3l1 1-2 1-9 3h-3-1l-1 1-2-1c-2-1-3-2-5-3-1-1-3-2-4-3l-1-1c-1 0-2-2-3-2-3 1-6 3-9 4 0 0-1 0-1-1-1-1-1-1-3-2 1-1 2-1 3-1l-1-1v1c-4-2-8 0-10-1v-2l1-1v1c2 0 2 0 3-2h0v-1-3l1-1-1-1-1 1h-1l1-2-1-1v-2c-1 1-2 1-3 2v-3-2c-1 0-1-1-3 0v-3-1c-1 0-1 1-2 1v1h-1 0l1-1-1-1c-1 0-1 0-2-1l1-2-1-1-1 1h-1l-1 1-1-3c1-1 3-1 5-2l-1-1 1-1h1 2l1-2h-1-1v-1l2-2v-1l1-1-1-1c0-1 0-1 1-1 0 0 1 1 2 1 0 1-1 2-1 2 1 1 1 1 2 1v-2-1h1c0-4 1-6 4-10 0-1 0-1 1-2-2 0-3-1-5-1v-1c-1 0-1 0-1-1l-1 1v1h-1l-2-1-1-4-1-2h1c-1-1-2-2-3-2h0c-1 0-1-1-2-1s-1 0-1-1l-2 1v-1c1-3 3-3 5-5 5-1 9-4 13-7z" class="u"></path><path d="M442 680h1c1 0 1 1 2 2l-1 2c-1 0-1 0-2-1v-3zm-2-98c1 0 1 0 2 1 0 1 0 1-1 3-1-1-1-1-2-1 0-2 0-2 1-3z" class="E"></path><path d="M469 306l2-1c1 1 1 1 1 2v1l-1 1c-2-1-1-1-2-2v-1z" class="C"></path><path d="M605 211c1 0 1 0 2 1s1 1 0 2h-2c-1 0-1-1-1-1l1-2z" class="I"></path><path d="M489 730c1 1 2 1 4 1l-1 2-2 2-1-5z" class="d"></path><path d="M684 227h0c2 1 2 1 2 2l-1 1-1 1-1-1c0-2 0-2 1-3z" class="C"></path><path d="M448 366h1l2 2-1 2h-1c-1-1-2-1-2-2l1-2z" class="H"></path><path d="M490 735l2-2 1 3c-1 1-1 2-3 2v-3z" class="g"></path><path d="M445 314l1-1 2 2-1 1-1 1h-2c0-2 0-2 1-3z" class="H"></path><path d="M794 271s-1-1 0-2c0 0 1 0 1-1 0 0 0-2 1-3h0l1 7-3-1z" class="C"></path><path d="M592 436c1 0 2 1 2 2v1l-1 1h-2c-1-1 0-1 0-2l1-2z" class="B"></path><path d="M582 767h0c1 1 2 1 2 2l-1 2h-1l-2-1c0-2 1-2 2-3zm9-476h0c1 1 2 1 2 2h1l-2 2-1 1c-1-1-2-1-2-2l2-3z" class="D"></path><path d="M517 699h-3c0-3 0-5 1-6l3-1-1 3h0c-1 1-2 1-3 3h1c1 0 1 0 2 1z" class="d"></path><path d="M577 251h1c1 1 2 1 2 3l-2 1c-1 0-1 0-2-1 0-2 0-2 1-3z" class="J"></path><path d="M485 308l1 1 1-1 3-1 1 1c-1 0-1 1-1 1l-5 4h0v-1-4z" class="Y"></path><path d="M431 485h1l2 2c0 1-1 2-2 3h-1l-2-2c0-2 1-2 2-3z" class="F"></path><path d="M687 204c1-1 2-1 3-1 1 1 1 1 1 3l-1 1-1 1c-1 0-1-1-2-1v-3z" class="J"></path><path d="M438 503h1c1 1 3 5 3 6l-3-2c-1 0-2 1-3 1h-1c0-1 2-3 3-5h0z" class="V"></path><path d="M439 550h2c1 1 2 1 2 3l-2 1h-1c-1-1-2-1-2-2l1-2zM255 213h1c1 0 2 1 2 2s0 1-1 2h0c-2 0-2 0-3-1v-2l1-1z" class="H"></path><path d="M260 184h0l1 2 1 1h1c-1 0-2 1-2 2-1 0-1 1-2 1v-1c-1-1-2-2-3-2 0-1 1-1 2-1h0l2-2z" class="E"></path><path d="M515 760h0c1 1 1 1 2 1 0 1 0 2-1 3h-2c-1-1-3-1-3-3 1-1 2-1 4-1z" class="m"></path><path d="M594 573c3 1 5 3 6 6h0l-2-1-1 1c-1 1-2 1-4 1 1-1 3-3 3-4s-2-2-2-2v-1z" class="I"></path><path d="M773 221c2 0 3 1 4 3-1 1-2 1-3 2-1 0-3 0-4-1h0c1 0 1-1 2-1 1-1 1-2 1-3z" class="G"></path><path d="M766 186h2c1 0 1 1 2 2-1 2-1 2-3 3 0 0-1 0-1-1-1-1-2-1-1-3l1-1z" class="E"></path><path d="M583 389h1l2 2c0 2 0 2-1 3h-3c-1-2 0-2 0-4l1-1z" class="J"></path><path d="M638 231c2 2 6 4 7 7v1h0c-2 0-3-2-5-3l-3-3 1-2z" class="f"></path><path d="M741 219c1 0 1 0 2 1 1 0 1 1 1 2s-1 2-2 2h-2c-1-1-1-1-1-2 0-2 0-2 2-3zm-273-25h2l2 1v2c0 1-1 2-2 2h-1c-1-1-2-1-2-2 0-2 0-2 1-3z" class="J"></path><path d="M579 690c2 0 3 0 5 1h0v1c-3 1-5 0-7 0h-7v-1c2 0 4 0 6-1h3z" class="N"></path><path d="M445 412h2c1 1 1 2 2 4 0 1-1 1-2 2h-1l-3-3c0-1 1-2 2-3z" class="V"></path><path d="M449 275h1c1 4 2 2 4 3 0 2-2 1-3 2l-1 2c0 1 0 1-1 1 0-1 0-2-1-3h-2c0-2 1-1 2-2 0-1 1-2 1-2v-1z" class="I"></path><path d="M465 680h2c0 1 0 3 1 5l-1 1h0c1 1 0 5 0 6l-1-1c-1-1-1-2-2-3h0l1-1c0-1 1-2 1-3h-1v-4z" class="e"></path><path d="M416 258c1-1 2-1 4 0 1 1 1 2 1 3l-2 2h-1c-1 0-2-1-3-2 0-2 0-2 1-3zm34-41c1-1 2-1 3-1 1 1 1 2 2 3-1 2-1 2-3 3h-1c-2 0-2-1-3-2 0-1 1-2 2-3z" class="J"></path><path d="M797 272h6c-2 1-4 2-6 2-1 2-1 5-2 7l-1-7c-1 0-1 0-1 1l-1-1 2-3 3 1z" class="I"></path><path d="M470 386c2 1 3 2 4 2s1 0 1-1l-1-2c1-1 2 0 3 0l1 1h-1c-1 2-1 5-3 6-1 0-2 1-3 0l-1-4v-2z" class="j"></path><path d="M355 217c1 0 2 0 3 1s1 1 1 2c-1 2-1 2-3 3-1 0-2 0-2-1-1-1-1-1-1-3 0-1 1-1 2-2z" class="J"></path><path d="M655 189h0c1 1 1 2 2 3s2 1 3 2h-3c-1 1-2 4-2 5h-1c0-2 0-3-1-5-1 0-2 0-3-1h0l2-1c2 0 2-2 3-3z" class="F"></path><path d="M465 665c0-1 1-1 2 0 0 2-1 4-1 7v1l1 1h0v2 4h-2v-2c1 0 1-1 1-2h-2c1-4 1-8 1-11z" class="g"></path><path d="M460 565l1 4v-1c0-1 0-2 1-3v8 4 5h0l-1 4-1-21z" class="N"></path><path d="M515 822h2 5v1 1h0l-1 1c-2 0-6 1-8 0h0-2v-1l1-2h3z" class="W"></path><path d="M236 259h0c1 1 0 1 1 1 1 6 3 2 5 4-1 0 0 0-1 1-1 0-2 0-3 1l-1 2c1 1 1 1 0 2h0l-1-1h0c0-1 0-2-1-3l-6-1c2-1 4-1 6-2l1-4z" class="H"></path><path d="M274 269h1l1 3 4 1h0l-4 1c-2 1-1 4-2 6h0l-1-5c-2-1-4-1-5-2 1-1 3-1 4-1 1-1 1-2 1-3h1z" class="J"></path><path d="M477 430c1 0 3 1 5 1h1c1 0 1 0 2 1h-2c-2 1-5 1-8 1l-10 1 12-4z" class="B"></path><path d="M549 429l1-3c2 0 3 0 5 1l1 1 1 1c1 1 1 2 1 3-1 1-2 0-4 0-1-1 0-1-1-1h-1c-1-1-2-1-3-1v-1z" class="F"></path><path d="M564 431c5-1 12 0 17 3h-1l-17-1-1-1 2-1z" class="D"></path><path d="M489 730v-6l7-1v3c-2 1-3 2-3 4v1c-2 0-3 0-4-1z" class="i"></path><path d="M465 696c6-2 11-2 16-1-1 1-1 1-3 2l-1 1h-8c-1-1-2-1-4-2z" class="B"></path><path d="M602 255c1-1 2 0 3 0 1 1 2 1 2 3-1 2-1 2-2 3h-3c-2-1-2-1-2-3s1-2 2-3z" class="J"></path><path d="M595 647h0c1 3 1 5 2 7 1 1 2 1 3 2-1 1-2 1-3 1-1 1-1 2-1 4h-1c0-2-1-3-1-4-2 0-3-1-4-1v-1c1 0 2-1 4-1 1-2 1-4 1-7z" class="H"></path><path d="M562 189c1-1 1 0 3 0 1 1 2 1 2 3s-1 2-2 3l-2 1c-2-1-2-1-3-2s-1-2-1-3c1-1 1-1 3-2z" class="J"></path><path d="M588 452c0 2 1 4 1 6l8 1c-1 0-4 1-6 1-1 1-1 3-3 4l-1-1h-2v-1h2l-1-2c-2 0-5 0-8-1 3-1 6-1 8-1l2-6z" class="b"></path><path d="M485 313l5-4v3l-1 1 1 1c0 1-1 2-2 3l2 1h0c-1 2-1 3-1 4s0 1-2 2v-1l-2 1v-1l2-2v-1l-2 1c1-3 1-1 0-3 0 0 1-2 1-3 0 0 0-1-1-2z" class="p"></path><path d="M476 488c2 0 4 0 5 2 1 1 0 1 0 2 0 2 0 4-1 6h0c1 2 1 4 0 6v1c-1-1-1-2-1-2v-1c0-2 1-3 0-4l-2-1v-1c0-2-1-2 0-3 0-2 0-3-1-5z" class="O"></path><path d="M451 346c1 2 1 3 2 4 0 0 1-1 2-1 0 1-1 1-2 3l4 2h0c-2 0-3 1-5 1v3l-1 1c-1-1-2-2-2-3s1-1 0-2c-1 0-2-1-4-1h-1c1-1 5-2 6-3 0-1 1-3 1-4z" class="E"></path><path d="M471 700c-1-1-1-1-2-1-3 0-6 0-9 1h0c-1-1-1-1-1-2l2-2h4c2 1 3 1 4 2h8 5v2h-3-6-2z" class="X"></path><path d="M488 721l1-1h2c1-1 2-2 3-2 2-1 6 1 8 0h1c1 0 1 1 2 1h1v1c-1 0-1 0-2 1-2 0-3-1-4 0-2 0-2 0-3 1-2 0-3-1-4 0-1 0-2 0-3 1h-2v-2z" class="f"></path><path d="M645 238h2 1c4 4 9 7 12 11h1c1 1 2 2 3 2l1 3c-2-1-5-2-7-4l-3-3c-1-1-3-2-4-3-2-2-4-4-6-5v-1z" class="W"></path><path d="M481 695h4c4 1 7 0 10-1h3c1 0 2 0 3-1h6l-3 1c-4 1-9 2-12 3-1 0-2 0-3 1h-7-5l1-1c2-1 2-1 3-2z" class="O"></path><path d="M591 363h0c0 2 0 4 2 5 0 2 6 2 8 2-2 0-5 1-7 2 0 1 0 1 1 2h0c-1 0-1-1-2-2l-1 2c-1 2 0 3-1 5-1-2-1-5-1-7h-8c2-1 5-2 7-2l2-7z" class="b"></path><path d="M468 495c0-1 0-3 1-4v-1l1-1c2-1 4-1 6-1 1 2 1 3 1 5-1 1 0 1 0 3h-1-4c-1 1 1 0-1 1l-1-1c1-1 1-2 1-3v-1c-1 1-1 1-1 2l-1 1h-1z" class="P"></path><path d="M518 741c-1-1-1-2-1-3l-1-1c1-2 1-4 0-6-2 1-1 3-1 5l-1-16c-1-1-1-1 0-2 1 0 1-1 2-2h0c1 3 1 5 1 8 0 1 0 2 1 4v1c-1 1 0 8 0 10v2z" class="f"></path><path d="M331 248l-4 1c-5 1-13 5-16 8-1 0-1 1-1 2-1 0-2 1-2 1l-1-1 1-1h0c-1 0-1 0-2 1v-1c8-7 15-9 25-12v2z" class="X"></path><path d="M569 694c5-1 12-1 15 1 1 1 2 2 2 4h-2c-4-2-11-2-15-2 0-1-1-1-1-2l1-1h0z" class="H"></path><path d="M331 246c6-2 12-2 19-2 3 1 7 1 11 1-3 1-7 1-10 1-1 0-1 0-2 1-4 1-7-1-10 0s-6 1-8 1v-2z" class="W"></path><path d="M564 214h0c1 1 1 4 2 5l6-2-6 4 6 6c-1-1-4-3-6-3v2l-1 6-1-7h-1l-2 1h0v-4c0-2-1-2-3-2l4-1c1-1 1-3 2-5z" class="s"></path><path d="M520 755h4 2 1v2h0l-4 16-1-8s-1-1-1-2l-1-5v-3z" class="G"></path><path d="M520 755h4 2 1v2h0-1c-2 1-4 1-6 1v-3z" class="f"></path><path d="M503 344h1 3v1h-1s1 1 2 0h1 0c1 0 2 0 2 1-8 1-20 1-27 7-1 1-1 3-3 4v-1-3c1-1 2-2 3-2 6-3 13-5 19-7z" class="G"></path><path d="M497 756v1l-3 4c1 1 2 2 3 2 0 1 1 1 1 2s-1 3-1 4h2l-3 1h-2l-1-1h-1-1c-1-2 0-6 0-9h1c1-2 2-2 3-3 1 0 1 0 2-1z" class="D"></path><path d="M540 838l-5-1-1-1c-2 0-3 0-5-1v-1l1-2-1-1c1-1 1-2 1-3 2-1 5-1 7 0v1l-1 2v1c1 1 3 1 5 1l-3 1c0 1 0 1 1 1 1 1 1 2 1 3z" class="c"></path><path d="M592 406c1 1 0 2 1 2 1 1 1 0 2 1l-2 4c2 2 5 1 7 1l-6 2v2 2s0 1-1 2v2l-1-1-1-4v-3c0 1-1 2-2 3v-1-2l-9-2c3 0 8 0 10-1-1-1 0-1-1-1l-1-1h1 1c2-2 1-3 2-5zm-60 356l2-2h0v-1c2-1 3 0 5 0 4 0 10 1 14 2 0 2 0 2-1 4h0-1c-6-2-13-3-19-3z" class="I"></path><path d="M805 179c2 0 3 1 5 1h1v1c-1 1-3 0-4 0 0 1 0 2 1 2 1 1 2 1 3 1 2 1 2 4 2 6v3l-2 1c-1-1-2-1-4-1l-2-14z" class="f"></path><path d="M555 706l1 1c1 0 1 0 2-1l2 2-1 1c-1 1-2 1-3 2h1 1 0l1 1-1 1h0c1 1 1 2 1 3h-2v1h1l1 2-1 2h1c0 2 1 3 0 5 0 1 0 2-1 3v-3c-2-2-3-2-5-2-1-1-3-1-4-1v-1h1c2 0 3 1 5 0 0-1-1-3 0-5v-3c0-1 0-1 1-1l-1-2c1-1 0-3 0-5z" class="e"></path><path d="M345 249h2v1h-3c-13 3-24 7-35 14h-2c0-1 1-1 2-2 2-2 4-4 7-5 9-5 19-7 29-8z" class="C"></path><path d="M525 428v-1-1l-2-1h-1v-1c2-2 3-3 7-3l-2-1h0c3 0 4 1 7 1h0 0l-2 2-1-1h1c-1 0-1-1-2 0h-2v1h1c1 0 3 1 4 2h1v1c1 1 2 1 2 1h2c1 1 2 1 3 1-1 0-2 0-4 1h0l-1 2c-1-1-1 0-2-1-2-2-6-2-9-2z" class="M"></path><path d="M553 724c2 0 3 0 5 2v3l-1 13h-1c-1-1-3-1-4-2 0-2 0-5-1-7v-3h3l2 2 1-1c-1-1-1-2-1-3v-1c-1-1-2-2-2-3h-1z" class="b"></path><path d="M727 231c1 2 1 4 2 6h0l9 1c-2 2-6 2-9 3v1c1 1 2 2 2 3-1 0-1-1-2-1l-1 3-1 4c0-3 0-4-2-6 1-2 1-3 0-4l-9-2c3-1 5-1 7-1h2 0c1-3 0-5 2-7z" class="J"></path><path d="M504 771h0c1-1 2-1 3-2 1 0 1-1 2 0v1 1h1v1 1h1c1 0 3 0 5 1h0l1 1h-4v1c3 0 5 0 7 2h-2v1c-1-1-3-1-4-1l-1 1h-2-5v-5l-2-3z" class="f"></path><path d="M408 719c-1-1-2-2-3-4l13-9v7c-2 2-5 4-8 7v-1c-1 0-1 0-1-1l-1 1z" class="s"></path><path d="M442 448l1 1v3l1 1c-1 0-1 1-1 2 2 1 10 1 13 1l-13 2c0 2 0 5-1 7-1-1 0-3-1-4l-3 2-1 1h0l3-6c-3 0-6-1-9-2l9-1c-1-1-3-2-4-3h-2l1-1c3 0 3 1 6 2 1-1 1-3 1-5z" class="J"></path><path d="M440 602c1 1 1 5 1 7l11 1c-2 1-8 1-10 2 1 1 1 1 1 2h-2c-1 2-1 6-1 9v-1c0-2 0-7-2-8l-2 2h-1c1-2 2-3 1-4l-7-2c3 0 6-1 9-1v-1l-1-2h1 0c1-1 1-3 2-4z" class="s"></path><path d="M524 149h1l2 18c0 1 1 4 1 6h-1l1 2-1 1c-1-1-1-1-2-1s-1 0-2-1h1 1c1-2 1-2 2-3-2-1-2-1-3-1-1-1-1-1 0-2v-8-4h0c0 4-1 8-1 12-1 2-2 4-2 6 0 1 0 1-1 2h0l2-14c0-2 0-6 1-8 1-1 1-4 1-5z" class="C"></path><path d="M522 187c2-1 6-1 8 1v-1-1l-10-1c1-2 1-5 3-7 2-1 3 1 4-1 4 3 5 12 6 16l-2 1c0-1 0-2-1-3-1-2-1-2-3-2l-2-1h-2c-1 0-1 0-2-1h1z" class="V"></path><path d="M531 194l2-1 4 21c-3-2-6-3-10-3v-1c-1 0-2 0-3-1h-1-3c0-1 0-2 1-3 2 0 5 0 7 1l1 1c1-1 1-1 2-1l2-1v-1-3c-1-1 0-3-1-4s0-3-1-4z" class="E"></path><path d="M507 360h0-2c-2 1-5 2-7 1h0l1-2v-1h1l1 1h2l-1-1v-2c-1 1-2 1-4 1v1l-2-1c-1 0-2 1-3 1v-1h1c1 0 2 0 3-1 2-1 5-1 6-2v-1l-13 3 12-4c3-1 5-2 8-2l-2 3v1l1 1h1v1c0 1 0 1-1 2l-2 1v1z" class="U"></path><path d="M530 754h3c-1-1-1-1-1-2 0-2 0-2 2-3v-1l-1-1 2 1-1 2c0 2 0 3 1 5 1 1 5 1 7 1l12 3h1c-1 1-2 1-2 2-4-1-10-2-14-2-2 0-3-1-5 0v1h0l-2 2c-1 0-2-1-3-1l-1-1h2v-1c0-1 1-1 2-1l1-1c-1-1-1-1-2-1l-1-2z" class="S"></path><path d="M232 299l1 10h9v1c-2 1-3 1-4 2h-1c-6 1-3 6-5 9l-1-4c0-1 0-2-1-3l-3-1h-1l2-2v-1c-3 2-4 1-7 0 2 0 5-1 7-1 0-1-1-2-1-3 1 0 1 0 2 1h1c1-1 1-6 2-8z" class="J"></path><path d="M521 198v3h1 1l1 2h1 1c0 1 1 1 1 1 1 1 1 2 1 3-2-1-5-1-7-1-1 1-1 2-1 3h3 1c1 1 2 1 3 1v1c-3 0-11 1-14 0 1-1 2-2 2-3 0-2 0-2 1-4 0-1 1 0 0-1 0-1 0-2 1-2l2-2c1-1 1-1 2-1z" class="B"></path><path d="M534 421h2l1 2h1 2l1 1 1-1h0c2 1 4 1 6 1l1 1-1 1c0 2 0 2 1 3v1c1 0 2 0 3 1h1c1 0 0 0 1 1h-3l-1-1h-2c0-1-1-1-2-1h-1l-4-2c-1 0-2 0-3-1h-2s-1 0-2-1v-1h-1c-1-1-3-2-4-2h-1v-1h2c1-1 1 0 2 0h-1l1 1 2-2h0z" class="V"></path><path d="M549 430c-1 0-2-1-3-1 0 0-1 0-2-1-1 0-1-1-1-2h0 3 1 1c0 2 0 2 1 3v1z" class="E"></path><path d="M621 212c-1-1-2-2-2-3-2-4-5-6-7-10-1-2-1-4-2-5-4-7-9-14-8-23 2 2 3 5 4 7l1 3-1 2c2 4 5 8 7 13 3 4 7 9 9 14 0 1 0 1-1 2z" class="m"></path><path d="M555 706h0c0 2 1 4 0 5l1 2c-1 0-1 0-1 1v3c-1 2 0 4 0 5-2 1-3 0-5 0h0l-2-2v-4h0c0-2-1-2-1-3-1-1-2-1-2-2h-1c1 0 2 0 3 1h1 1c0-2 0-3 1-4l2-2h3z" class="F"></path><path d="M551 711c0-1 0-1 1-2h3v2c-1 0-2 1-3 2h-1v-2z" class="H"></path><path d="M499 722c3 0 5-1 8 0 1 2 1 5 2 8v1 1h0v2 2h1l-1 2 1 1-1 1 2 3-1 1c1 0 1 1 2 1-1 1-1 1-2 1h-1c-1-1-1-1-1-3 0-1-1-1-1-2v-2h1l-2-2-1-1c1-1 1-2 1-3v-4h-1v2h-3l-1-1 1-2v-1l-1-1c0-1 0-1-1-1-1-1-1-2-1-3z" class="R"></path><path d="M501 726l1-2 1-1 1 1c0 1-1 2 0 4 0 0 1 0 1 1v2h-3l-1-1 1-2v-1l-1-1z" class="B"></path><path d="M495 399v-1h2c1 0 2 0 2 1h2 2v1 1 1h0-1l-1 1h0c-8 3-20 4-24 12h0c-1 0-1-1-1-1 1-4 3-5 7-7l6-3c1-2 2-3 4-4 1-1 1-1 2-1z" class="O"></path><path d="M495 399v-1h2c1 0 2 0 2 1h2 2v1 1c-5 0-9 2-14 3 1-2 2-3 4-4 1-1 1-1 2-1z" class="e"></path><path d="M461 530h1 0l3 3h0l1 2c0 3 0 6-1 9h0l-2 1v3l1 2-2 1c0 4 1 10 0 14-1 1-1 2-1 3v1l-1-4v-10l1-25z" class="Z"></path><path d="M462 530l3 3h0l1 2c0 3 0 6-1 9h0l-2 1v3l1 2-2 1c0-7-1-14 0-21z" class="S"></path><path d="M465 544l-1-1-1-1c1-1 1-2 1-3h0c0-1 0-2-1-3v-1l1-1 2 1c0 3 0 6-1 9z" class="X"></path><path d="M461 530c-1-2-1-7 0-9v-7h1c0-2 0-2 1-3 1 0 2-1 3 0l-1 1v2c1 1 2 1 2 1v5c-1 1 0 1-1 1l-1 1 2 2c0 1-1 2-1 3l1 1-1 2c-1 1-1 2-1 3h0l-3-3h0-1z" class="e"></path><path d="M465 649c1 0 1 0 1-1l1-1c1-1 1 0 2-1v5 6 4c-1 1 0 1 0 3v3c-1 2-1 5-1 7v1h0c0 1 1 3 1 4s0 1-1 2c1 1 1 2 2 3v1c-1 1-1 2-1 3s1 1 2 1v4c-1 0-2 0-4-1 0-1 1-5 0-6h0l1-1c-1-2-1-4-1-5v-4-2h0l-1-1v-1c0-3 1-5 1-7-1-1-2-1-2 0v-4h1c-1-1-1-3-1-4v-8z" class="d"></path><path d="M520 646v9h0l2 2-2 2v7c1 1 2 1 3 1-1 1-2 1-3 1-1 2-1 3-1 4l3 1 1 1h-1l-1 1c-1 0-1 1-1 1h-1l1 1s1 1 2 1l-2 2c0 2-1 3 1 5v1l-1-1h-5v-3h1l-1-1c0-3 1-3 2-4-1-1-1-2-1-3v-1c0-2 0-4 2-6h-1c0-3 1-5 1-8 1-4 1-8 2-13z" class="p"></path><path d="M505 729h1v4c0 1 0 2-1 3l1 1 2 2h-1v2c0 1 1 1 1 2 0 2 0 2 1 3h1v1 1h-6 0c-2-1-3-2-4-2h-1c0-2 0-2 1-2 1-2 0-1-1-2l1-1v-1-2c1-2 1-2 1-3l-2-1 2-2-2-2c1-1 1-1 3-2l-1 2 1 1h3v-2z" class="P"></path><path d="M507 739c-1 1-2 1-4 1v-2l1-1c0-2-1-2-2-4l2-1 2 1c0 1 0 2-1 3l1 1 2 2h-1zm3 9c1 1 1 2 2 4h-1v2l-2 1h2v1c-2 0-3 0-5 1h-2c-2 1-3 1-5 1l-2-1v-1l-1-2h0c0-2-1-3-2-4 1 0 3 0 4-1h1c2 0 3-1 5-1h0 6z" class="B"></path><path d="M494 750c1 0 3 0 4-1h1l2 1 1 2 1 1v1 1c1 1 1 2 1 2-2 1-3 1-5 1l-2-1v-1l-1-2h0c0-2-1-3-2-4z" class="X"></path><path d="M496 754l2-1h1c1 1 0 3 0 5l-2-1v-1l-1-2z" class="m"></path><path d="M539 772c-3 0-9 0-11-1l-1-1h4c-1-1-1-1-1-2l2-1 1-1c2-1 5-1 7 0 3 0 8 0 10 2v3l-1 3c-2 1-8-1-10-2z" class="O"></path><path d="M539 772h0c1-1 1-1 2-1h1c-2-2-3-1-5-1l1-1c4-1 8 0 12 2l-1 3c-2 1-8-1-10-2z" class="E"></path><path d="M468 495h1l1-1c0-1 0-1 1-2v1c0 1 0 2-1 3l1 1c2-1 0 0 1-1h4 1v1l2 1c1 1 0 2 0 4v1s0 1 1 2h2v1h-11c-1-1-2 0-2-1h-4-1l1-6 1-1v-1h1 1v-2z" class="G"></path><path d="M477 497l2 1c-1 1-2 2-3 2h-1l-1 1c2 0 2 0 3 1l-1 1h-2l-2-3v-2h1c1-1 3-1 4-1z" class="B"></path><path d="M469 505h-4-1l1-6 1-1v-1h1l-1 2 2 1c0 1-1 2-1 3h0 1c0-1 0-1 1-1v3z" class="m"></path><path d="M466 511v2h1v-3c2-1 4-1 6-1-1 2-1 3-2 4h-2v7c0 1 0 1-1 2 0 1 1 3 1 4v2l1 1h0l-2 2v1c0 2 0 5 1 7v1 1c-1 1 0 6 1 7l-1 2 2 1h-2-2v-1h-2c0-2 0-3 1-4h0l-1-2h0c1-3 1-6 1-9l-1-2c0-1 0-2 1-3l1-2-1-1c0-1 1-2 1-3l-2-2 1-1c1 0 0 0 1-1v-5s-1 0-2-1v-2l1-1z" class="p"></path><path d="M304 204h0c1 1 2 2 4 2l2-2-1 5c1 1 2 1 3 1s0-1 1 0v2h1l9 1h0c-3 0-7 0-9 1-1 1-2 1-3 2l1 3v1c-1-1-1-2-3-2-1 1-1 1-1 3h0l-1 4h0v-2c-1-2-1-4-2-6-2 0-2 0-4 1v-1l2-2h0c-4-1-8-1-11-1l11-2-6-5c3 2 5 4 8 3v-1l-1-5z" class="s"></path><path d="M544 722c2 1 3 1 5 1 1 0 3 0 4 1h1c0 1 1 2 2 3v1c0 1 0 2 1 3l-1 1-2-2h-3v3c-1 0-1 1-2 2-2-1-2-1-2-2l-1 1h0l-3 1c-2 1-1 1-2 0 0-3-1-5 1-8l-1-3c1-1 2-1 3-2z" class="H"></path><path d="M549 729c0-1 1-1 2-1v-1s1-1 1-2c2 2 2 3 2 5h-3c-1-1-2-1-2-1z" class="E"></path><path d="M549 729s1 0 2 1v3c-1 0-1 1-2 2-2-1-2-1-2-2 0-2 1-3 2-4z" class="m"></path><path d="M543 735l-2-2v-2c0-2 1-4 3-5 1 0 1 0 2-1 1 0 1 0 2 1l-3 3h-1c-1 1-1 1-1 2 2 1 2 2 3 3h0l-3 1z" class="J"></path><path d="M473 393c2 0 3 0 4-1l4 3 2-2-1 5v7l-3 2c-2 1-3 2-5 4 0-1-1-3-1-4l-3-11 1-1 2-2z" class="j"></path><path d="M471 395l2-2c1 2 1 2 1 4l-1 1c-1-2-1-2-2-3z" class="B"></path><path d="M481 395l2-2-1 5v7l-3 2-1-2h-1c0-2-1-3 0-5h0c1-1 1-2 2-3 0-1 1-1 2-2z" class="G"></path><path d="M477 400l3 1c0 2 0 2-1 3l-1 1h-1c0-2-1-3 0-5z" class="B"></path><path d="M491 381c1 0 2 1 4 1h-2c0 1 0 1 1 2v5c1 1 3 0 5 0-2 1-4 1-6 3v1h1l-2 1c-1 1-1 2-1 3l-2 2-1-2c-1 1 0 4-1 4l-1 1c-2 1-3 2-4 3v-7c1-1 2-1 3-1v-5c1-1 1-1 1-2v-2-5l2-2c1 0 1 0 2 1l1-1z" class="U"></path><path d="M491 381c1 0 2 1 4 1h-2c0 1 0 1 1 2v5c1 1 3 0 5 0-2 1-4 1-6 3v1h1l-2 1c-1 1-1 2-1 3l-1-1v-1-3h1l2-2h-3l1-1h1c0-1-1-2-1-2-1-1-1-2-1-3v-2l1-1z" class="T"></path><path d="M487 712v-10h2c1 1 3 1 5 1 1 0 1 0 2-1l-1-1h0 2v1h1s1-1 1-2c1 0 1-1 2-1s2 0 3 1v1c1 0 2 1 2 1h0v1c-1 0-2 0-3 1 1 1 2 0 3 1-1 1-2 0-3 1 1 0 1 0 2 1h-1l-2-1-2 2c1 1 1 1 2 1v1c-2 0-3 0-4-1-1 0-1 1-2 1l1 2h-2-1-3c-1-1-2-1-4 0h0z" class="m"></path><path d="M487 712h0c2-1 3-1 4 0h3 1 2l-1-2c1 0 1-1 2-1 1 1 2 1 4 1v-1c-1 0-1 0-2-1l2-2 2 1v1h2v1c0 1-1 1-2 1 1 1 2 0 3 1l-2 2c1 0 1-1 2 1l-3 1h0 1 2v1h0c-1 0-1 0-2 1h-2l-1 1c-2 1-6-1-8 0-1 0-2 1-3 2h-2l-1 1c-1-3-1-6-1-9z" class="W"></path><path d="M462 582v15 10 5l1 78c0 1 0 2-1 3h0c-2-2-1-7-1-9v-30-68l1-4z" class="L"></path><path d="M507 344c9-3 19-2 27-2 10 2 20 4 29 8l1 1c1 1 2 2 3 4h-1 0c-4-3-8-5-13-6-13-4-28-5-42-3 0-1-1-1-2-1h0-1c-1 1-2 0-2 0h1v-1z" class="C"></path><path d="M464 733c-1-1-1-1-1-2-1-1-1-1-1-2-1-2 0-3 0-4-1-1-1-3-1-4-1-2 0-16 0-19l1-1v1c-1 1 0 4 0 6h3c0-1 0-3 1-4v3c1 0 1 1 1 2l-1 1v1c1 3 1 6 1 9 0 2 1 4 1 6-1 2-1 3 0 5 0 1 0 1 1 1 2 1 4 1 7 1v1h-4 0-2c-2 1-4-1-6-1z" class="f"></path><path d="M541 833h0c1-1 2-3 2-5h0 1l1 1 5 5c3 3 7 6 9 10l1 2h1l-2 2-1 1h-1c-1-1-2-1-4-2l1-1c-1-1-2-2-3-2l-1-1c-1 0-2 0-3-1-2-2-4-3-7-4 0-1 0-2-1-3-1 0-1 0-1-1l3-1z" class="r"></path><path d="M551 844c2 0 4 1 6 1 1 1 2 2 2 3l-1 1h-1c-1-1-2-1-4-2l1-1c-1-1-2-2-3-2z" class="M"></path><path d="M545 829l5 5c-1 1-1 2-1 3h-1l1 1v2c-3-1-5-3-7-5h0 3 1l-2-1-1-1c1 0 1 0 1-1h1l-1-2 1-1z" class="B"></path><path d="M550 834c3 3 7 6 9 10l-1 1c-1-1-4-1-5-3h0l-4-2v-2l-1-1h1c0-1 0-2 1-3z" class="E"></path><path d="M499 334c-2 0-4 0-6 1l-1-1c1-2 1-5 1-7 0-1 1-2 1-3l-1-1v-1h-1c1-2 2-2 2-4h-1v-3l-1-1c1-1 1-1 1-2h2l-2-2h0 1v-1c-1-1 0-1-1-1l-1-1v-2l3-1h1c0 1 0 1 1 1v2h1c1 0 1 1 2 1l1 1h0-2v3l1 1h1v1l-1 2v1 2l-2 2h1l1 1v1 3 1l-1 1-2 1c-1 1-1 2-1 4l3 1z" class="Y"></path><path d="M500 323v3 1l-1 1-2 1v-1-4c1-1 2-1 3-1z" class="N"></path><path d="M560 375l3 1c1 2 0 5 0 8 1 3 1 7 1 10s-1 6-1 9c0 1 0 1-1 2-5-2-10-3-15-5l1-1c3 0 6 1 10 2h2v-8-5c-3-1-3-1-4-3l-1-3 2 2v-2c0-2-1-6 0-7h1 1 1 0z" class="O"></path><path d="M557 384l1-1c1 0 1 0 2-1v3 3c-3-1-3-1-4-3l-1-3 2 2z" class="C"></path><path d="M560 393l1-4c0 2 0 3 1 5v1c-1 1-1 0-1 1h1c1 2-1 6 1 7h0c0 1 0 1-1 2-5-2-10-3-15-5l1-1c3 0 6 1 10 2h2v-8z" class="m"></path><path d="M525 428c3 0 7 0 9 2 1 1 1 0 2 1l-6 9h-1l-2-1c-3 0-6 1-9 0-1-1-1-2-2-2 0-2-1-2-1-3-1-2-1-4-2-5h1c1 0 2 1 2 2h1c2 0 2 1 3 1l1 1c0-2 0-3 1-4l3-1z" class="S"></path><path d="M525 428c3 0 7 0 9 2-1 0-2 1-3 1h-2-5-1l-1-1v-1l3-1z" class="Y"></path><path d="M518 439c1-1 2-1 3-1v-3-1c1-1 1-1 2-1l1 1c1-1 1-1 3-1v2h-2v1c1 1 2 0 4 0l1 2-1 2-2-1c-3 0-6 1-9 0z" class="M"></path><path d="M513 429h1c1 0 2 1 2 2h1c2 0 2 1 3 1l1 1c0-2 0-3 1-4v1l1 1h1l-1 2c-1 0-1 0-2 1v1 3c-1 0-2 0-3 1-1-1-1-2-2-2 0-2-1-2-1-3-1-2-1-4-2-5z" class="m"></path><path d="M569 697c4 0 11 0 15 2h-2l1 1c-2 0-4-1-5-1h-8l-4 1h-1c0 1 1 1 2 1 0 4 0 8 2 11v3c0 2 1 5 0 7 2 2 0 3 1 5 1 1 1 2 1 3h0c2 1 4 1 6 1h-4l-8 1c-1-2-1-5-1-7v-16-6c0-1-1-2-1-2 1-2 1-2 1-3v-1h5 0z" class="S"></path><path d="M486 529c2-1 3-1 5-1 0 0-1 1-1 2 0 2 0 5 1 8v1l-1 1c0 1 1 2 0 3v5c-1 1-2 1-2 3h0 2v1 2 3l-1 1 1 1v1s0 1-1 2h1c0 1 0 2-1 3 0 1 0 1-1 2 1 1 2 0 1 2v1 1c-1 2-1 3-1 4l1 1h0l-1 2h0c1 2 1 4 1 6l-1 2c1 0 1 1 1 1l1 1-1 1-1-1c-1-1-1-4-2-6h0c-1-10 0-21 0-30v-23z" class="N"></path><path d="M556 385c1 2 1 2 4 3v5 8h-2c-4-1-7-2-10-2-2-1-2-1-2-2h-1l1-2c-1-1-1-3-1-4-1-1-1-1-2-1h-1l-1-1h-2l-1-1c3 0 5 1 7 0v-1-1h2c1 1 2 1 3 1s1 0 2 1l1-1c1 0 1 0 2 1 1-1 1-2 1-3z" class="D"></path><path d="M546 395h4c1 1 1 0 2 0l1 1h0c-2 0-2 0-3 1h-1-3-1l1-2z" class="Q"></path><path d="M539 389l-1-1c3 0 5 1 7 0l1 1h1c1-1 1-1 2-1v1 1h1c0 1 1 2 1 3 2 0 3-1 5 0v1h-8c1-1 1-1 2-1-2-1-2-2-4-2h-1c-1-1-1-1-2-1h-1l-1-1h-2z" class="V"></path><path d="M794 216v1s-1 1-1 2c0 2-3 6-4 8l-1 1-1 1-1 1v1l-1 1c-2 2-3 4-5 5l-3 3c-1 1-1 1-1 2h-1c-2 1-3 3-4 4h-1 2v1h1l1-1c0-2 1-3 3-3l4-4c0-1 1-2 2-2 0-2 1-4 3-4 1-1 2 0 3-1 1-2-1-1 0-2 2-1 4-5 5-8v-1l1-1h0c0 2-1 5-2 7s-3 5-5 7c-5 7-11 13-18 18-3 2-7 4-11 6-2 1-4 3-6 4h-1c-1 0-2 0-3 1h0v-1l1-1c4 0 9-4 13-5v-1c1-1 1-1 2-1 1-1 1-2 1-3l-8 4c-2 1-3 2-5 2 0-1 0-1 1-1h-2c-2 1-1 0-2 1-2 0-4 1-5 1s-3 1-4 1-1 0-2 1h-3c-1 0-2 0-3 1h-4c-1 0-2 0-3 1h-3c5-1 11-2 17-3 5-1 10-4 15-5h3c3-1 5-4 8-6 7-5 14-11 19-18 2-2 4-4 5-7l4-7z" class="M"></path><path d="M517 201v-4c1 0 1 1 2 0l-1-1v-1-1c0-1 1-2 1-3s1-1 1-3c0-1-1 0 0-1h2-1c1 1 1 1 2 1h2l2 1c2 0 2 0 3 2 1 1 1 2 1 3 1 1 0 3 1 4s0 3 1 4v3 1l-2 1c-1 0-1 0-2 1l-1-1c0-1 0-2-1-3 0 0-1 0-1-1h-1-1l-1-2h-1-1v-3c-1 0-1 0-2 1l-2 2z" class="C"></path><path d="M523 201c1-2 1-2 3-3-1 2-1 3-1 5h-1l-1-2z" class="P"></path><path d="M530 196c1 2 0 3 0 5 1 1 1 2 1 3l1 1h1v1h-2c-2-2-3-3-4-5l2-1h0c1-1 1-2 1-4z" class="V"></path><path d="M521 198v-2l2-2v2c1 0 2 0 3-1 1 0 2 1 2 1l1 1c-1 0-2 1-3 1h0c-2 1-2 1-3 3h-1-1v-3z" class="I"></path><path d="M517 201v-4c1 0 1 1 2 0l-1-1v-1-1c0-1 1-2 1-3s1-1 1-3c0-1-1 0 0-1h2-1c1 1 1 1 2 1h2l2 1c2 0 2 0 3 2 1 1 1 2 1 3 1 1 0 3 1 4s0 3 1 4v3h-1l-1-1c0-1 0-2-1-3 0-2 1-3 0-5 0-2 0-3-1-5 0 0 0-1-1-1-1-1-3 0-4 0l-1 4-2 2v2c-1 0-1 0-2 1l-2 2z" class="j"></path><path d="M565 732l8-1-3 1h0c2 0 4 0 6 1h-4c-2 3-2 13-1 16 1 0 1 0 1 1v3s1 0 1 1c-2 4 0 11 1 16 0 2-1 6 0 8l1 1c-1 0-1 1-1 1h-1v-3l-1-2-1-5s-1-1-1-2l-1-4c-1-3-1-5-1-8l-1-3v-4c-1-1-1-1-1-2 0-5 0-10-1-15h0z" class="f"></path><path d="M498 671c2 0 3 0 5-1h1l-1 2 1 1v1c1 1 3-1 4 1l1 1h0v3c0 1 0 2-2 4 1 1 1 1 1 3-2 1-4 3-6 2v1c-1 1-4 1-5 0l-1-2h-1v1h-1v-1l-1-1c1-1 1-1 2-1v-1h-3 0c1-1 3-1 4-1v-1c-1-1-1 0-1 0l-1-1c1 0 1 0 2-1h1l1-1-1-2-2-1c1-1 2-1 4-3h0l-1-2z" class="p"></path><path d="M498 671c2 0 3 0 5-1h1l-1 2 1 1v1c1 1 3-1 4 1l1 1h0v3c-1-1-1-1-2 0l-1 1h0c1 1 1 1 1 2-2 0-2 1-4 2 0 1 0 1-1 2-1-2-3 0-5-2l1-1 2-2h1l-1-1v-2l-1-1v-1h3v-1h-1l1-2v-1-1l-3 2-1-2z" class="U"></path><path d="M487 507c-1-2 0-5-1-7 0-13 0-26 1-39 0-4-1-9 0-14 1-1 0-5 1-6 0 0 2-1 3-2h0v1l-2 2h1 1l1 1-2 1c-1 1-1 1-1 2l1-1 1 2c-1 1-2 2-3 2v1l2-1c0 2-2 2-1 3l1 1-1 2s0 1 1 1l1 1-2 3h2l-1 2h0l1 1-2 3v1c1 3-1 15 0 17l-1 1 1 1h1v1c-1 3 1 9-1 12h1v4l-1 2h1c-1 1-2 2-3 2z" class="T"></path><path d="M496 723l3-1c0 1 0 2 1 3 1 0 1 0 1 1l1 1v1c-2 1-2 1-3 2l2 2-2 2 2 1c0 1 0 1-1 3v2 1l-1 1c1 1 2 0 1 2-1 0-1 0-1 2h1c1 0 2 1 4 2-2 0-3 1-5 1h-1c-1 1-3 1-4 1l-3-3c0-3 0-6-1-9 2 0 2-1 3-2l-1-3 1-2v-1c0-2 1-3 3-4v-3z" class="f"></path><path d="M493 736v5c-1 2 0 3 1 4l1 1c0-2-1-2 1-3h0v3s-1 0-2 1v3l-3-3c0-3 0-6-1-9 2 0 2-1 3-2z" class="X"></path><path d="M496 723l3-1c0 1 0 2 1 3 1 0 1 0 1 1l1 1v1c-2 1-2 1-3 2l2 2-2 2 2 1c0 1 0 1-1 3v2h-1v-1l-1-1v-1-5l-1-1v-1c0-2-1-3-1-4v-3z" class="a"></path><path d="M462 551l2-1-1-2v-3l2-1 1 2h0c-1 1-1 2-1 4h2v1h2v1c0 1 1 1 1 2 0 2-2 5 0 7l-1 1c-1 2-1 2 0 5v3c-1 0 0 2 0 3 0 2 0 3 1 5v1c-1 0-2 1-2 2-1 2 0 5 0 8-1-1-2-2-3-2v-1l1-1h1c-1-1-1-2-2-3h0c-1-2-1-2-1-3l-2-2v-4-8c1-4 0-10 0-14z" class="k"></path><path d="M463 572l3 1c0 1-1 3-1 4 1 2 1 3 0 5-1-2-1-2-1-3l-2-2v-4l1-1z" class="f"></path><path d="M462 551l2-1-1-2v-3l2-1 1 2h0c-1 1-1 2-1 4h2v1c-1 2-1 3-1 5-1 1 0 3 0 5 0 1 0 2 1 3-1 1-1 1-2 1v1c1 0 1 1 2 1v1 1c-1 0-1 1-2 2h0c-1 0-1 1-2 1l-1 1v-8c1-4 0-10 0-14z" class="X"></path><path d="M521 642c2 0 3 0 4 1h1l-1 1 2 1 3 22h0l-1 2h-2-4c-2 1-1 1-2 2 1 1 1 1 2 1v1 1l-1-1-3-1c0-1 0-2 1-4 1 0 2 0 3-1-1 0-2 0-3-1v-7l2-2-2-2h0v-9-1c1-1 2-1 3-1v-1h-1l-1-1z" class="G"></path><path d="M525 644l2 1 3 22h0l-3-1h-3s-1 0-1-1c-1-2-1-3 0-5l3-1h2v-4l-1 1c-1-1-1-1-2-1v-1c-1 0-1 0-1-1 1-3 0-5 0-8l1-1z" class="H"></path><path d="M526 659h2v6c-2 0-2 0-3-1 0-2 0-3 1-5z" class="J"></path><path d="M522 212c4 0 8 0 12 1 1 1 3 2 3 2 1 1 1 3 1 4l2 7-5-2h0-1c-6-2-12-1-18-1 0-3-1-6-1-8 1-2 5-3 7-3z" class="F"></path><path d="M551 733c1 2 1 5 1 7 1 1 3 1 4 2h1l-1 11c0 2 0 5-1 6h-1l-12-3c2 0 3 0 4-1h0c-1-1-1-2-1-4l-2-1h-1l1-1h1 1l-1-1-1-1v-1c1-1 1-1 2-1h0l-1-1 1-3 2-1c-1 0-1-1-2-1l1-5h0l1-1c0 1 0 1 2 2 1-1 1-2 2-2z" class="Q"></path><path d="M545 751l2 1 1 1v2h-2 0c-1-1-1-2-1-4zm9 8v-1-1c-1-1-1-2-1-3-1-1-1 0-2-1h0c-1 0 0-1-1 0h0l-1-1 1-1c3 0 3 0 6 2 0 2 0 5-1 6h-1z" class="H"></path><path d="M551 733c1 2 1 5 1 7l-1 1-1-1v1h0c1 0 1 1 2 2v2c-1 0 0 0-1-1h-1c-2 1-3 1-5 1l-1-1 1-3 2-1c-1 0-1-1-2-1l1-5h0l1-1c0 1 0 1 2 2 1-1 1-2 2-2z" class="W"></path><path d="M468 372c1 3 1 6 1 9v9h-1l-14-1c-2-1-4 0-6-1-1-1-2-1-3-1s-1 0-1-1c1-1 2-2 4-3h0c2-1 5-3 7-4l13-7z" class="Q"></path><path d="M468 372c1 3 1 6 1 9h-3 0c-2 0-4 0-6 1-3 1-8 2-11 2l-1 1h-1l1-1v-1c2-1 5-3 7-4l13-7zm100 117c2-1 6-1 8 0h0v5c1 1 0 3 1 5v2 3h1c1-1 1 0 1-1v-2-1c-1-1-1-3-1-4v-1c0-1-1-4 0-5 2 3 3 8 3 11 0 2 0 5 1 6 0 1 2 2 3 3l-2-1c-6-3-11-3-17-3l-1-1 2-1h-1c-1-2-1-2-1-4h0l-1-1v-2c1-3 0-6 2-8h2z" class="C"></path><path d="M568 489c2-1 6-1 8 0h0v5c1 1 0 3 1 5v2 3h0c-1 1-2 1-3 0h-3l-1-1v-4h0v-1c-1-2-1-4-1-5v-1-1c-1-1-1-1-1-2z" class="F"></path><path d="M576 489h0v5c1 1 0 3 1 5v2c-1 0-5-3-6-4v-5-2l5-1z" class="H"></path><path d="M473 375c2 0 5-1 8 0v-1h2 1 2c1 2 0 4 0 7v1 1 5 2c0 1 0 1-1 2v5c-1 0-2 0-3 1l1-5-2 2-4-3h0v-2l1-1 1 2v1h1v-1-2l-2-1v-2l-1-1c-1 0-2-1-3 0l1 2c0 1 0 1-1 1s-2-1-4-2c0-3-1-9 1-11h2z" class="Y"></path><path d="M480 379v2l1 1c1-1 2-4 4-4v2h0c-1 2-1 3-1 5l-1 1v1c1 2 0 4 0 6h0l-2 2-4-3h0v-2l1-1 1 2v1h1v-1-2l-2-1v-2l-1-1 1-1-1-1c1 0 1-1 2-2l-1-1 2-1z" class="Z"></path><path d="M470 386c0-3-1-9 1-11h2c1 1 2 1 4 2h0 1 0c1 1 1 1 2 1v1l-2 1 1 1c-1 1-1 2-2 2l1 1-1 1c-1 0-2-1-3 0l1 2c0 1 0 1-1 1s-2-1-4-2z" class="D"></path><path d="M473 380l1 1c1 1 2 1 2 3h-2c-1-1-1-2-1-4z" class="V"></path><defs><linearGradient id="Q" x1="482.246" y1="240.776" x2="468.72" y2="238.401" xlink:href="#B"><stop offset="0" stop-color="#928f89"></stop><stop offset="1" stop-color="#b6b3b2"></stop></linearGradient></defs><path fill="url(#Q)" d="M497 201c1 0 0-1 1 0-4 3-8 7-12 11-2 3-5 7-8 11-6 11-9 26-8 39l3 18-1-1c-6-14-7-27-3-42 4-16 13-28 28-36z"></path><path d="M534 740l2-3v-1c0-2 0-3 1-4-1-1 0-1-1-2 0-1 1 0 1-1v-2c0-2 1-4 2-5h5c-1 1-2 1-3 2l1 3c-2 3-1 5-1 8 1 1 0 1 2 0l3-1-1 5c1 0 1 1 2 1l-2 1-1 3 1 1h0c-1 0-1 0-2 1v1l1 1 1 1h-1-1l-1 1h1l2 1c0 2 0 3 1 4h0c-1 1-2 1-4 1s-6 0-7-1c-1-2-1-3-1-5l1-2c0-2-1-3 0-4 0-1 0-1-1-2l1-1-1-1z" class="E"></path><path d="M543 750v2 1l-3 1-1-1c0-2 0-2 1-3h2 1z" class="H"></path><path d="M540 738c1 0 2 0 3 1v4h-1l-2-1v-4z" class="F"></path><path d="M576 489v-1c-3-1-9-1-13-1 1-3 1-6 1-9h2l1-1-3-1c-1-2 0-4 0-6 0-1 1-1 2-1l-1-1c-1-1-1-1-2-1l2-2c0-1-1-2-2-3 0-1 0-1 1-1v-1c-1-2-1-4-2-6 6 9 11 18 14 28 1 2 2 6 2 8-1 1 0 4 0 5v1c0 1 0 3 1 4v1 2c0 1 0 0-1 1h-1v-3-2c-1-2 0-4-1-5v-5z" class="I"></path><path d="M569 481c1 0 0 0 1 1 1 0 2 1 3 1h1l1 1v2c-1 0-2 0-3-1h-3v-4z" class="C"></path><path d="M462 577l2 2c0 1 0 1 1 3h0c1 1 1 2 2 3h-1l-1 1v1c1 0 2 1 3 2h-1l1 3c0 1 1 1 1 2-1 1-1 1-1 2l1 1-1 4c1 2 1 1 1 3l-1 1v1 2h2 0l-2 1v1c0 1 1 4 1 4-1 1 0 3 0 4 0 2-1 3 0 5 0 2-1 2 0 4v1c-1 2 0 4-1 6v5 3c1 1 1 3 1 4-1 1-1 0-2 1l-1 1c0 1 0 1-1 1 0-3 1-6 0-8v-1-1-3h1c-1-1-1-2-1-2 0-2 1-2 0-3v-1-1-2c1-1 1-2 0-3v-3h0v-1c0-1 1-2 0-3h0c0-1 0-2 1-3v-1c-1-1-3-1-4-1v-5-10-15h0v-5z" class="c"></path><path d="M462 597c1-1 1-1 2-1s1 0 2 1c0 1-1 1 0 2v1c1 1 0 1 1 2h-1c0 2 1 2 1 3h-2c1 1 1 2 2 3v1c-1-1-1-1-2-1h-1l2 3h0l-4-1v-3-10z" class="m"></path><path d="M462 577l2 2c0 1 0 1 1 3h0c1 1 1 2 2 3h-1l-1 1v1c1 0 2 1 3 2h-1-1-1c0 1 0 1 1 2h-2v1h2c0 1-1 1-1 2l1 1v2c-1-1-1-1-2-1s-1 0-2 1v-15h0v-5z" class="W"></path><path d="M488 509l2-2c1 0 1 0 2 1l-1 1v3 2h4c1 0 2 1 2 2s0 5-1 7c1 0 1 1 2 1h0l-2 2v1l2 1c0 2-2 2-1 3l1 1-2 2 1 1v1c-1 1-1 1-1 3l1 1-1 1v4l-1 1 1 1c1 0 2 0 4 1h-3-4c-1-1-2-1-3 0v-5c1-1 0-2 0-3l1-1v-1c-1-3-1-6-1-8 0-1 1-2 1-2-2 0-3 0-5 1l1-2v-18h1z" class="e"></path><path d="M488 509c1 1 0 2 1 2 0 1 1 0 1 2l-1 2v1h1l1 1-1 1c-1 3 1 6-2 9h-1v-18h1z" class="Y"></path><defs><linearGradient id="R" x1="527.8" y1="714.735" x2="537.955" y2="718.868" xlink:href="#B"><stop offset="0" stop-color="#09050c"></stop><stop offset="1" stop-color="#31342b"></stop></linearGradient></defs><path fill="url(#R)" d="M533 700h1l1 1v1-1c2 3 0 5 1 8v3c-1 1-1 2-1 3l1 2c1-2 1-5 2-8 0 0-1-2-1-3l2-1c-1 2-1 2 0 4 1 1 2 1 3 1h0 1l1 1h1c0 1 1 1 2 2 0 1 1 1 1 3h0v4l2 2h0-1v1c-2 0-3 0-5-1h-5c-1 1-2 3-2 5v2c0 1-1 0-1 1 1 1 0 1 1 2-1 1-1 2-1 4v1l-2 3h-1v-7h-1v1h0l-1-2c0-5 2-11 1-16l-2-1v-1c1 0 1 0 2-1l1-13z"></path><path d="M536 717c1-2 1-5 2-8 0 0-1-2-1-3l2-1c-1 2-1 2 0 4 1 1 2 1 3 1h0 1l1 1h1c0 1 1 1 2 2 0 1 1 1 1 3h0v4l2 2-4-1c-2 0-6 0-8-1-1 0-1 0-2-1h0v-2z" class="B"></path><path d="M543 710l1 1h1c0 1 1 1 2 2 0 1 1 1 1 3-1 0-2 1-2 2v2l-5-1c-1-2-1-2-1-4l2-1c1-1 1-2 1-4z" class="C"></path><path d="M530 172c6-1 13 0 19 0h33 196c7 0 50-1 53 0h0-27l1 7 2 14c2 0 3 0 4 1-1 3-1 6-1 10-1 6-1 12-3 17v-6c0-3 0-5-1-8l-1-1c1-2 1-4 1-6h-3v-1c1-9 1-18-1-27H530z" class="a"></path><path d="M807 193c2 0 3 0 4 1-1 3-1 6-1 10-1-1-1-2-1-4-1-2-1-4-2-7z" class="W"></path><defs><linearGradient id="S" x1="746.582" y1="275.506" x2="749.657" y2="255.457" xlink:href="#B"><stop offset="0" stop-color="#b8b6b4"></stop><stop offset="1" stop-color="#f8f6f5"></stop></linearGradient></defs><path fill="url(#S)" d="M764 260l5-1h1c-1 1 0 1-1 1-2 1-3 2-4 4h1 1l2 1h-1c-4 2-7 3-11 7-7 2-15 3-23 3h-8l8-1-3-3c-2-1-3-2-3-4 1 0 3 0 4 1 3 0 4 0 6-1 10-2 17-3 26-7z"></path><path d="M416 699c11-11 18-23 23-37 0 2-1 5-2 7-3 13-8 24-17 33-4 4-12 10-16 11-1-1-2-2-3-2h0c-1 0-1-1-2-1s-1 0-1-1l-2 1v-1c1-3 3-3 5-5 5-1 9-4 13-7l1 1 1 1z" class="s"></path><path d="M414 697l1 1 1 1c-2 2-4 3-7 5-2 1-8 3-9 5l1 2h0c-1 0-1-1-2-1s-1 0-1-1l-2 1v-1c1-3 3-3 5-5 5-1 9-4 13-7z" class="W"></path><path d="M576 733l7 1-3 22c-1 8-3 15-5 23l-1-1c-1-2 0-6 0-8-1-5-3-12-1-16 0-1-1-1-1-1v-3c0-1 0-1-1-1-1-3-1-13 1-16h4z" class="b"></path><path d="M575 740c0-2 0-3 1-4l2-1c1 2 1 6 1 8h0l-1-2c-1-1-1-1-3-1z" class="o"></path><path d="M575 740c2 0 2 0 3 1l1 2c-1 3-1 6-2 8 0-1-1-3-1-4 0-3-1-5-1-7z" class="s"></path><path d="M800 214h1v-1l1 9c-2 6-4 11-8 17-2 4-4 7-6 11-5 5-11 11-17 13l-2 2-2-1h-1-1c1-2 2-3 4-4 1 0 0 0 1-1h-1l-5 1c3-3 6-4 9-7 10-7 18-16 23-27 2-4 3-8 4-12z" class="H"></path><path d="M771 263c1 0 2-1 3-2h1l1-1s1-1 2-1v-1l2-2 1-1 1-2h0l7-8c1-2 3-5 5-6-2 4-4 7-6 11-5 5-11 11-17 13z" class="C"></path><path d="M490 548c1-1 2-1 3 0h4l-2 2c-1 0-1 1-2 2v1 2s1 1 2 1c0 2-1 2-2 3l1 2h0l-1 1h0l3 1-2 2c2 1 2 0 3 0v2h1v1c0 1 1 1 1 2 2 2 0 4 1 6 1-1 2-2 4-2 0 2 1 2 0 4v4 1l-1 2c-2 0-3 0-4-1l-2 2-2-1c-1 0-2-1-3-1l-1 1v2l-1 1-1-1s0-1-1-1l1-2c0-2 0-4-1-6h0l1-2h0l-1-1c0-1 0-2 1-4v-1-1c1-2 0-1-1-2 1-1 1-1 1-2 1-1 1-2 1-3h-1c1-1 1-2 1-2v-1l-1-1 1-1v-3-2-1h-2 0c0-2 1-2 2-3z" class="M"></path><path d="M496 567h1 1v1c0 1 1 1 1 2 2 2 0 4 1 6 1-1 2-2 4-2 0 2 1 2 0 4v4 1l-1 2c-2 0-3 0-4-1h0-3v-4l1-2v-2-1h-2c1-1 2-2 3-2v-1c0-1-1-1-1-1-1-1-1-3-1-4z" class="N"></path><path d="M500 576c1-1 2-2 4-2 0 2 1 2 0 4v4 1h-3l-1-1h2v-1h-3l-1-1c1-1 2-1 3-1h0c0-2 0-2-1-3z" class="L"></path><path d="M490 548c1-1 2-1 3 0h4l-2 2c-1 0-1 1-2 2v1 2s1 1 2 1c0 2-1 2-2 3l1 2h0l-1 1h0l3 1-2 2c2 1 2 0 3 0v2h-1c-1 0-2 1-3 2s-1 2 0 3h0l2 2h-2c0 1 0 1 1 2l-1 1v1c1 0 0 0 1 1-1 1-2 1-3 2-2-2-2-4-1-6v-1c0-1-1-2-1-3v-1-1c1-2 0-1-1-2 1-1 1-1 1-2 1-1 1-2 1-3h-1c1-1 1-2 1-2v-1l-1-1 1-1v-3-2-1h-2 0c0-2 1-2 2-3z" class="m"></path><path d="M621 212c1-1 1-1 1-2-2-5-6-10-9-14-2-5-5-9-7-13l1-2c8 16 16 30 29 42 3 3 6 6 9 8 5 2 9 6 13 9 1 0 0 0 1 1l38 18c-5 0-12-1-15-4h-1-1c-1 0-2-1-3-1-2 0-4-2-6-3-3-3-7-5-10-7l-1 1h-1l-4-3c-2-2-4-4-7-4h-1-2c-1-3-5-5-7-7-2-1-4-2-4-4l-1-1c-1-2-2-3-5-4l1-1-6-7v-1-1h-1-1z" class="M"></path><path d="M645 231c5 2 9 6 13 9 1 0 0 0 1 1l-6-3-1-1v1c-4-2-5-4-7-7z" class="W"></path><path d="M480 458l2-3s0-1 1-1l-1 4c2 2 1 6 1 8v21c-1 1-2 1-4 0h0-5-5c-2 1-3 8-3 10v1l-1 1-1 6h1 4c0 1 1 0 2 1l-9 1c1-7 3-14 5-21 3-10 7-19 13-28z" class="O"></path><path d="M475 476c1 0 1 1 2 2h3 0l-1 1c-1 1-1 0-2 1h0c-1 0-2 0-3-1h0c0-1 0-1 1-3z" class="K"></path><path d="M480 458l2-3s0-1 1-1l-1 4c2 2 1 6 1 8v21c-1 1-2 1-4 0h0 3c-1-2-1-5-1-7h0c0-2 0-2-1-3l1-2v-1c0-1 0-5-1-6 0-1-1 0-1-2h2l-2-2-2 3c-1 3-3 6-4 9 0 1 0 1-1 2 0 2 0 1 1 3-1 0-2 1-3 1l3-8c1-3 3-6 4-9 2-2 3-4 3-7z" class="d"></path><path d="M478 509c1 0 2 0 3 1 1 0 1 0 1 1 1 1 1 0 1 1h-2v1l2 2v45 36 15 37l-1 34c0 3 1 8 0 11h-7c1-1 3-1 4-2s1-2 1-4v-18c1-1 0-3 0-5 1 0 1-1 1-2-1-1-1-4-1-5h-1c1-2 0-3 1-5v-1-5c1-4 2-9 0-12v-5c0-3 1-6 1-9 0-2-1-3 0-5 0-1-1-1-1-2s1-2 1-3c1-6 0-13 0-19 0-5 1-10 1-15 0-3-1-7 0-10v-1c-1-2 0-3 0-5s-1-5-1-7v-1l-1-1h-1c2 0 2 0 3-1 1-5 1-11 0-16-1-1-1-1-1-2v-2c0-3 1-4 1-6-1-3-1-6-1-9-1-2-1-1-3-2v-4z" class="t"></path><path d="M803 199v1h3c0 2 0 4-1 6l1 1c1 3 1 5 1 8v6c-3 17-13 31-27 40-7 5-15 9-23 11 4-4 7-5 11-7h1l2-2c6-2 12-8 17-13 2-4 4-7 6-11 4-6 6-11 8-17l-1-9v1h-1c0-2 1-5 1-7l1-3 1-5z" class="h"></path><path d="M802 204c1 0 2 0 3 2v9l-1 3v5c0 1 0 2-1 3h0c0 2-1 4-2 6-3 6-7 15-13 18 2-4 4-7 6-11 4-6 6-11 8-17l-1-9v1h-1c0-2 1-5 1-7l1-3z" class="P"></path><path d="M801 207c2 1 3 2 3 4v3c-1 2-1 5-2 8l-1-9v1h-1c0-2 1-5 1-7z" class="Q"></path><path d="M558 326h1v1l1 1c0 1 2 2 3 3h0c1-2 0-3 1-5 0 5 1 10 0 14v1h-1v1h1c1 2 1 5 1 7h0c-1 0-1 1-2 1-9-4-19-6-29-8v-1h2l-2-2h1v-3l3-2v-2h2c1 0 1 0 3-1l1 1v1h1l2 1c1 0 2 0 4 1h4 0l3 1v-10z" class="S"></path><path d="M538 334v7h-2l-2-2h1v-3l3-2z" class="I"></path><path d="M559 326v1l1 1c0 1 2 2 3 3h0c1-2 0-3 1-5 0 5 1 10 0 14v1h-1v1h1c1 2 1 5 1 7h0c-2 0-3-2-5-3v-2l-1-1c0-2 1-5 1-7l-1-10z" class="B"></path><path d="M543 331l1 1v1h1l2 1c1 0 2 0 4 1h4l2 1 1 1c0 3 1 6 0 9l-11-3c-1 0-3 0-4-1-2 0-2 0-3-1v-9c1 0 1 0 3-1z" class="o"></path><path d="M543 331l1 1v1h-1c0 2 2 2 1 3h-1l-2 1v2c2 0 4-1 5-1 1-1 1-2 2-2h0c0 1 1 2 1 3 1 1 1 2 1 3h-1c-2-1-2-1-3-1-1 1-2 1-3 1h0c-2 0-2 0-3-1v-9c1 0 1 0 3-1z" class="E"></path><path d="M550 342h1c1-1 1-1 1-2-1-1-1-2-2-3l2-1s1 0 1 1c1 1 0 4 1 5h2 0c0-1-1-2-1-3 1-1 2-1 2-3l1 1c0 3 1 6 0 9l-11-3c-1 0-3 0-4-1h0c1 0 2 0 3-1 1 0 1 0 3 1h1z" class="C"></path><path d="M537 429h0c2-1 3-1 4-1l4 2h1c1 0 2 0 2 1h2l1 1h3l4 2 2 18c-5-3-10-5-16-7-2 0-4 0-6-1s-4-2-7-1h0c-1-1-1-1-1-2v-1l6-9 1-2z" class="E"></path><path d="M539 431l3 3-1 1v-1h-1c-1 1-2 3-2 4l-3 1-1-1 3-3c0-2 1-3 2-4z" class="o"></path><path d="M536 431l1-2 2 2h0c-1 1-2 2-2 4l-3 3c-2 1-3 2-4 3v-1l6-9z" class="I"></path><path d="M497 536v3l2 1v3c1 1 1 1 1 2v1c3 1 6-1 8 1h1c0 1-1 2-1 3 1 2 2 4 1 6 0 1 1 2 1 3-1 0-2 0-2 1 1 1 2 0 3 1-1 1-2 1-3 1v1c1 1 3 0 3 2l-1 1v3h-2l-1 1 1 1v2c-1 1-1 1-1 3h1c0 1 1 1 1 2l-3 1v1c1-1 1-1 3 0l-1 1v1c2 0 2-1 3 0h4v1h-1l-1 1h-2l-2-1c-2-1-3-1-5-1v-4c1-2 0-2 0-4-2 0-3 1-4 2-1-2 1-4-1-6 0-1-1-1-1-2v-1h-1v-2c-1 0-1 1-3 0l2-2-3-1h0l1-1h0l-1-2c1-1 2-1 2-3-1 0-2-1-2-1v-2-1c1-1 1-2 2-2l2-2h3c-2-1-3-1-4-1l-1-1 1-1v-4l1-1-1-1c0-2 0-2 1-3z" class="O"></path><path d="M500 564h-1v-1h2l1 1h5l1 1c-1 0-2 0-3 1s0 2-1 4v-2c-1 1-2 1-3 1 0-1-1-2 0-3 1 0 1 0 3-1-2-1-2-1-4-1z" class="L"></path><path d="M500 564c2 0 2 0 4 1-2 1-2 1-3 1-1 1 0 2 0 3 1 0 2 0 3-1v2 2h2l1 1c-1 1-2 1-3 1-2 0-3 1-4 2-1-2 1-4-1-6 0-1-1-1-1-2v-1c1-1 2-1 2-3z" class="q"></path><path d="M497 536v3l2 1v3c1 1 1 1 1 2v1c3 1 6-1 8 1-2 0-5 0-7 1v1l1 1h0c-1 1-1 1-1 2h1 1c0 2-1 1-2 2l1 2-2 2 2 1c-1 1-2 1-2 2h0l1 1h0v1h-2v1h1c0 2-1 2-2 3h-1v-2c-1 0-1 1-3 0l2-2-3-1h0l1-1h0l-1-2c1-1 2-1 2-3-1 0-2-1-2-1v-2-1c1-1 1-2 2-2l2-2h3c-2-1-3-1-4-1l-1-1 1-1v-4l1-1-1-1c0-2 0-2 1-3z" class="Y"></path><path d="M496 563l-3-1h0l1-1h0l-1-2c1-1 2-1 2-3-1 0-2-1-2-1v-2-1c1-1 1-2 2-2s1 0 2 1l-1 1h1l1 1c0 1-1 1-2 2 1 0 1 1 2 2-1 0-1 1-2 1l1 1h1c0 2-2 1-1 3v1h0-1z" class="M"></path><path d="M570 699h8c1 0 3 1 5 1 1 2 1 19 0 22 0 2 1 7 0 9h-4v-2-7h-2v2c1 1 1 5 0 7-2 0-4 0-6-1h0c0-1 0-2-1-3-1-2 1-3-1-5 1-2 0-5 0-7v-3c-2-3-2-7-2-11-1 0-2 0-2-1h1l4-1z" class="C"></path><path d="M569 715l1 1c0 1 1 2 1 3h1 2l1 1h1c-1 1-2 1-3 1v3c1 1 1 0 3 0v-2h1v2c1 1 1 5 0 7-2 0-4 0-6-1h0c0-1 0-2-1-3-1-2 1-3-1-5 1-2 0-5 0-7z" class="V"></path><path d="M570 699h8c1 0 3 1 5 1 1 2 1 19 0 22 0 1 0 3-1 4-1 0-1 0-2-1-1-2 0-5 0-8v-14l-1-1-2 1v4h1v1 1l-1 1c0 1 0 2-1 3-1 2 0 4-1 6-1-1-1-1-2-1l-1 1v-2c1 0 2-1 3-1v-2l-1-1-2 1h0c-1-3 0-7-1-10 0-2 0-3-1-4v-1z" class="H"></path><path d="M523 687l9 1c1 1 1 1 1 3v6 2 1l-1 13c-1 1-1 1-2 1v1h-1 0l-1 1c-1-1-1-1-2-1h-2c-3 0-4 1-6 1h-2c0-1-1-2-1-4l-1-7c0-2 0-3 1-5 1 0 1 0 2-1h1-1c-1-1-1-1-2-1h-1c1-2 2-2 3-3h0l1-3c1 0 1 1 2 0v-1l-1-1c-2 0-3 1-4 1v-1l8-3z" class="d"></path><path d="M526 703l2 1c1 2 0 4 0 6v2h-1l-1-6v-3z" class="X"></path><path d="M515 712h3c1 0 2 0 3 1l3 2c-3 0-4 1-6 1h-2c0-1-1-2-1-4z" class="f"></path><path d="M518 699h1c2 3 1 7 1 10l-1-1v-1c-1 1 0 2-1 3h-1v-5-1-1c-1 1-2 1-2 1l-1 1c0-2 0-3 1-5 1 0 1 0 2-1h1z" class="i"></path><path d="M520 692h1c0 3-1 5 0 8v2 1c0 2 1 6-1 8v-2c0-3 1-7-1-10h-1-1c-1-1-1-1-2-1h-1c1-2 2-2 3-3h0l1-3c1 0 1 1 2 0z" class="U"></path><path d="M526 701v-2 4 3h0v7c-2-1-4-1-6-2 2-2 1-6 1-8v-1h1l1-1h3z" class="E"></path><path d="M521 702h1l1-1h3c0 2 0 2-1 3h0l-2 1-2-2v-1z" class="J"></path><path d="M523 687l9 1c1 1 1 1 1 3v6 2 1l-1 13c-1 0-2-1-3-1l-1-2c0-2 1-4 0-6l-2-1v-4 2h-3l-1 1h-1v-2c-1-3 0-5 0-8h-1v-1l-1-1c-2 0-3 1-4 1v-1l8-3z" class="b"></path><path d="M533 699c-1 1-2 0-3 0v-1-2l3 1v2z" class="s"></path><path d="M519 690c4 0 9 0 13 1v1c-1 0-2 0-2 1-2 2-1 8-2 11l-2-1v-4c0-2 1-5 0-7-2 0-3-1-5 0h-1v-1l-1-1z" class="M"></path><path d="M521 692c2-1 3 0 5 0 1 2 0 5 0 7v2h-3l-1 1h-1v-2c-1-3 0-5 0-8z" class="H"></path><path d="M523 695h3c0 2 0 2-1 3h-2v-3z" class="J"></path><path d="M510 350l9-1c14 0 26 2 39 7l1 1-1 1 1 1v-2c1 0 2 0 3 1 1 0 2 1 2 1l-1 7h0c-2-2 0-5-1-7h-1c0 2 0 4-1 5h0c-1-1-1-2-1-3l-1 1c0 1 0 0-1 1l-1-1v-5h0c-2 1-1 4-2 5h-1-1-1v-3h-1v2l-1 1c-1-2-1-4-1-6l1-1h-1c-1 2 0 4-1 6h-1 0-2c-2-2-1-5-1-7h0l-1 1v5c-2 1-4 0-6 0l1 1-21-1h9v-1h-5 0c-3 1-6 0-9 0l-4 1v-1l2-1c1-1 1-1 1-2v-1h-1l-1-1v-1l2-3z" class="N"></path><path d="M536 359h-2v-6h2v6h0z" class="G"></path><path d="M536 359h1l2-2c-1-1-1-2-1-3h2 0 1l1 1v5c-2 1-4 0-6 0v-1h0z" class="O"></path><path d="M510 350l9-1 1 1c1 1 3 1 4 1-4 1-9 1-13 2-1 0-2 0-2 1h-1v-1l2-3z" class="Y"></path><path d="M471 700h2 6 3l2 1c-1 10-2 21-4 31-2 1-3 0-4 1-3 0-5 0-7-1-1 0-1 0-1-1-1-2-1-3 0-5 0-2-1-4-1-6 0-3 0-6-1-9v-1l1-1c0-1 0-2-1-2v-3-2c1-2 3-2 5-2z" class="G"></path><path d="M472 702c1 1 1 2 2 3s1 2 1 4h-2c-1 0-1 0-2-1 0-3 0-4 1-6z" class="F"></path><path d="M473 700h6 0c2 1 2 1 2 3 0 1-1 1-1 2-1 2-1 6-1 8 0 4-2 5-1 9 0 2 0 4-1 5h0c-1 1-1 2-1 3h-1c-1-1 0-1 0-2-1 0-2-1-3-1l1-1h1l-1-1 2-1c-1-1-3-1-3-3 1-1 2-1 4-1 0-1 0-2-1-2h-1c0 1-1 1-1 1-1-1-1-2-2-3v-1c0-2 1-4 2-6h0 2c0-2 0-3-1-4s-1-2-2-3l-1-1 2-1z" class="C"></path><path d="M473 700h6 0v5h-5c-1-1-1-2-2-3l-1-1 2-1zm51 15h2c1 0 1 0 2 1l1-1h0 1l2 1c1 5-1 11-1 16l1 2h0v-1h1v7h1l1 1-1 1c1 1 1 1 1 2-1 1 0 2 0 4l-2-1 1 1v1c-2 1-2 1-2 3 0 1 0 1 1 2h-3c0-2 0-4-1-5l-2 6h-1-2-4c-1-3-1-10-2-14v-2c0-2-1-9 0-10v-1c-1-2-1-3-1-4 0-3 0-5-1-8h2c2 0 3-1 6-1z" class="H"></path><path d="M524 715h2c1 0 1 0 2 1l1-1h0c-1 2-1 5-1 7v11c-1 4 0 7-1 11h-1l-1-1v-2c1-2 1-3 1-5v-1c0-4 0-7 1-11v-4-2c0-1 0-1-1-2-1 0-4-1-5 0v3 2l-1-1-2 2h0c0 1 0 1-1 2 0-3 0-5-1-8h2c2 0 3-1 6-1z" class="L"></path><path d="M521 719c1 5 1 11 2 16 1 1 1 1 3 1 0 2 0 3-1 5v2l1 1v1l-1 1v-3c-2 0-2 0-3 1v4c-1-1-1-1-2-3l1-1c0-1 0-2-1-3h0l1-1c-1-2-1-5-1-7v-3h-2v-1-1c-1-2-1-3-1-4 1-1 1-1 1-2h0l2-2 1 1v-2z" class="h"></path><path d="M518 729v1h2v3c0 2 0 5 1 7l-1 1h0c1 1 1 2 1 3l-1 1c1 2 1 2 2 3v-4c1-1 1-1 3-1v3l1-1v4c-1 2 0 4 0 6h-2-4c-1-3-1-10-2-14v-2c0-2-1-9 0-10z" class="N"></path><path d="M522 748v-4c1-1 1-1 3-1v3c0 2 0 5-1 8-1-1-1 0-1-1-1-2 0-3-1-5z" class="I"></path><path d="M530 715l2 1c1 5-1 11-1 16l1 2h0v-1h1v7h1l1 1-1 1c1 1 1 1 1 2-1 1 0 2 0 4l-2-1 1 1v1c-2 1-2 1-2 3 0 1 0 1 1 2h-3c0-2 0-4-1-5l-2 6h-1c0-2-1-4 0-6v-4-1h1c1-4 0-7 1-11v-11c0-2 0-5 1-7h1z" class="C"></path><path d="M529 749c-1-2 0-4 0-5l2-12 1 2h0v-1h1v7h1l1 1-1 1c1 1 1 1 1 2-1 1 0 2 0 4l-2-1 1 1v1c-2 1-2 1-2 3 0 1 0 1 1 2h-3c0-2 0-4-1-5z" class="M"></path><path d="M495 455l1 1 2-1 1 1v2 1c1 1 1 0 1 1l-1 2h1c1 0 2 0 3 1h1c0 1 0 2 1 4v2h-1c0 2 0 3 1 4l1 2c-1 1-1 1 0 3 2 1 3 0 5 1h-3l-2 1h-1-1c-2-1-1-1-2 0-2 0-3 1-4 1v3 2l-3 1c1 1 1 1 2 1v1c-1 1-1 1-1 2l1 1-2 2v1c1 1 1 1 2 1 0 1-1 2-2 2l1 1h0 1v1c-1 1-1 1 0 2-2 1-2 2-4 3h4c0 1-1 2-2 2l-1 1c1 1 1 1 3 1l-1 2 2 1c-1 1-1 1-3 2h-4v-2-3l1-1c-1-1-1-1-2-1l-2 2h-1v-2c1 0 2-1 3-2h-1l1-2v-4h-1c2-3 0-9 1-12v-1h-1l-1-1 1-1c-1-2 1-14 0-17v-1l2-3-1-1h0l1-2h-2l2-3c1-1 2-1 3-2h1z" class="m"></path><path d="M496 458c0 2 1 3 1 4h4v1l-1 1h-1-1c-1-1-2-1-4-1h-2l1-1c1-2 2-3 3-4z" class="p"></path><path d="M495 455l1 1 2-1 1 1v2 1c1 1 1 0 1 1l-1 2h1c1 0 2 0 3 1-1 0-2 0-3 1l1-1v-1h-4c0-1-1-2-1-4 0-1 0-2-1-3z" class="U"></path><path d="M503 463h1c0 1 0 2 1 4v2h-1c0 2 0 3 1 4l1 2c-1 1-1 1 0 3 2 1 3 0 5 1h-3l-2 1h-1-1c-2-1-1-1-2 0-2 0-3 1-4 1v3 2l-3 1c1 1 1 1 2 1v1c-1 1-1 1-1 2h-2c1-1 0-1 1-2-1 0-1 0-1-1-1 0 1-2 1-3l-1-1c-1-1 1-2 2-3l-2-1-1 1 2-2v-1c-1 0-1 1-2 1 0-2 0-2 2-2v-1h-2l-1-1 2-1-1-2h1v-1l1-2h-2c0-1-1-1-1-3 2-1 3-2 6-2h1 1c1-1 2-1 3-1z" class="c"></path><path d="M503 463h1c0 1 0 2 1 4v2h-1c0 2 0 3 1 4l1 2c-1 1-1 1 0 3 2 1 3 0 5 1h-3l-2 1h-1-1c-2-1-1-1-2 0l-3-1h-1c1-1 2-1 2-2h0c-1-1 0-1-1-2h-2 0l2-1v-1c1-2 0-1 0-3l3-1v-1h-3c0-1 1-2 1-3l-1-1h1c1-1 2-1 3-1z" class="a"></path><path d="M503 463h1c0 1 0 2 1 4v2h-1c0 2 0 3 1 4l1 2c-1 1-1 1 0 3 2 1 3 0 5 1h-3l-2 1v-1h-4l2-2-1-1v-2l-1-1c0-2 0-3 1-5h0c0-1 0-2-1-3h-2l-1-1h1c1-1 2-1 3-1z" class="R"></path><path d="M503 325l-1-1c1-2 3-2 5-3h3c1 1 2 2 2 3l-1 5c1 2 2 1 4 2h0l7-1h7c2 0 5 1 7 0 2 1 3 1 5 1h2c-2 1-2 1-3 1h-2v2l-3 2v3h-1l2 2h-2v1c-8 0-18-1-27 2h-3-1c-1-1-4-1-5-1h-2l-1-1c-1 0-2 1-3 1v-1l2-2h-1v-1c1 0 1 0 2-1l-1-1c1-2 4-2 6-3h-1l-3-1c0-2 0-3 1-4l2-1 1-1v-1h1l2-1z" class="H"></path><path d="M503 325l-1-1c1-2 3-2 5-3h3c1 1 2 2 2 3l-1 5c1 2 2 1 4 2h0l7-1h7c2 0 5 1 7 0 2 1 3 1 5 1h2c-2 1-2 1-3 1h-2c-5 0-25-1-27 1h0v-1-1h-4c-1 0-1 1-3 1v-2-1c-1 1-2 1-2 2h-1s-1-1 0-1c0-2 2-3 3-4h1v-1h-2z" class="D"></path><path d="M510 321c1 1 2 2 2 3l-1 5c-1 0-2 1-4 0v-8h3z" class="W"></path><path d="M508 337c0-2 0-3 1-4h1 1 0v5c1 1 3 1 4 1 4-1 10-1 13 0 2 0 3 0 4-1l2 1 2 2h-2v1c-8 0-18-1-27 2h-3-1c-1-1-4-1-5-1v-2l1 1h7c2-2 1-3 2-5h0z" class="M"></path><path d="M511 333h0v5c1 1 3 1 4 1 4 2 8 0 13 2h-18v-8h1z" class="G"></path><path d="M515 339c4-1 10-1 13 0 2 0 3 0 4-1l2 1 2 2h-2-6c-5-2-9 0-13-2z" class="D"></path><path d="M503 325h2v1h-1c-1 1-3 2-3 4-1 0 0 1 0 1h1c0-1 1-1 2-2v1 2c2 0 2-1 3-1h4v1 1h-1-1c-1 1-1 2-1 4h0c-1 2 0 3-2 5h-7l-1-1v2h-2l-1-1c-1 0-2 1-3 1v-1l2-2h-1v-1c1 0 1 0 2-1l-1-1c1-2 4-2 6-3h-1l-3-1c0-2 0-3 1-4l2-1 1-1v-1h1l2-1z" class="a"></path><path d="M506 342l-2-3-1-1c-1 0-1 1-2 2l-1-3 1-1c1 0 1 0 2 1 1 0 1-1 2-1v-2h0c1-1 1-1 2-1l1 4h0c-1 2 0 3-2 5z" class="K"></path><path d="M506 779h5 2l1-1c1 0 3 0 4 1l1 1-1 1h3v4 1c1 1 1 2 1 3v2 1c1 3 1 28 0 30h-5-2-3c-2-1-3 0-4-2l-1-1v-2h-1l-1-1c1-2 1-2 3-3v-1l-2-2v-1l1-2-1-3c1-1 2-2 2-3h0c1-1 1-2 1-3-1-1-1-2-1-3l2-3v-1c0-2 0-4-1-5h0c-2-2-3-2-3-5v-2z" class="c"></path><path d="M510 814c1 1 1 2 1 4v1l1 2c2 0 2 0 3 1h-3c-2-1-3 0-4-2v-2h1l-2-3v-1c1 1 1 1 2 1l1-1z" class="M"></path><path d="M515 796c-1-1-2 0-3-1v-1c1 0 2 0 3-1l-1-2v-1c2 0 2-1 3-2 2 0 2 2 4 2l1 1v1 1c-2-1-3-1-4 0h-2v1c0 1-1 1-1 2h0z" class="r"></path><path d="M508 801l2 1h1c-1 2-1 5 0 7l-1 1v1c1 0 0 0 1 1-1 1-1 1-1 2l-1 1c-1 0-1 0-2-1v1l2 3h-1v2l-1-1v-2h-1l-1-1c1-2 1-2 3-3v-1l-2-2v-1l1-2-1-3c1-1 2-2 2-3z" class="W"></path><path d="M506 779h5 2l1-1c1 0 3 0 4 1l1 1-1 1c-2-1-3 0-5 0v1h2v1h-1-2l1 1 1 1 1-1v1c0 1-1 1-2 2h0v2 2h-1c-1 2 0 3-1 5v1c0 1 1 5 0 5h-1l-2-1h0c1-1 1-2 1-3-1-1-1-2-1-3l2-3v-1c0-2 0-4-1-5h0c-2-2-3-2-3-5v-2z" class="S"></path><path d="M506 779h5 2l1-1c1 0 3 0 4 1l1 1-1 1c-2-1-3 0-5 0h-2c-1 1 1 2 1 4l-1 1-1-1v-2-1c-1-1-1-1-3 0l-1-1v-2z" class="X"></path><path d="M515 796h0c0-1 1-1 1-2v-1h2c1-1 2-1 4 0v4 4 12 4h-1v3c-2 0-2-1-3-1s-2 1-2 1c-1-1-1-1-1-2-1 0-1-1-3-2h0c1-3 1-6 0-8l2-2-1-1c-1-1 1-1 2-3h-2c0-2 1-3 0-5l2-1z" class="R"></path><path d="M516 807c2 0 3-1 5 0 0 2-2 1-3 3h1v1c1 2 1 3 2 4h-1c-1 1-2 1-3 2v-1l2-1-1-2v-2c-1-1-1-2-1-3-1 0-1-1-1-1z" class="G"></path><path d="M522 801l-3 2c-1 0-2 0-4-1v-1l1-1v-2h2v-1h0l1-1c1 1 2 1 3 1v4z" class="h"></path><path d="M527 439l2 1h1v1c0 1 0 1 1 2h0c3-1 5 0 7 1s4 1 6 1c6 2 11 4 16 7v2c-1 3 0 7 0 11 0-1-1-1-1-1h-1c-2-1-3-1-5 0l-1-1h-3c-1-1-2-1-3-1l-3-1h-1c-2 0-5 0-7-1-3 0-8 1-10 0s-3-3-5-4l-1 1v-1l2-1v-3c0-2 0-3 1-4 0-2 0-4 1-5l1-1c1-1 1-2 1-2l2-1z" class="H"></path><path d="M527 439l2 1h1v1c0 1 0 1 1 2h0c3-1 5 0 7 1l-5-1c-3 0-6 0-9-1 1-1 1-2 1-2l2-1z" class="Q"></path><path d="M522 448l1 1c1 0 1-1 1-2h2l1 1c-1 1-1 1-2 3-1 1-1 2-1 3s0 1-1 1h-2v-3c0-2 0-3 1-4zm21 13h2c1 1 3 1 5 1l1 1v-1-1c-2-1-3 0-4 0l-2-2h-2c-1-1-2 0-3 0-1-1-3-2-5-2-1 0-1-1-2-2 0-1 0-1 1-2v-1c0-2 1-2 2-2 0 1 1 1 2 2v-1c0-1 0-2 1-3h5c1 1 1 1 2 1 2 1 4 2 7 3 1 0 2 1 3 2h4c-1 3 0 7 0 11 0-1-1-1-1-1h-1c-2-1-3-1-5 0l-1-1h-3c-1-1-2-1-3-1l-3-1z" class="E"></path><path d="M545 453l1-3h1v7c-1 1-1 1-2 1v-1c0-2 1-3 0-4z" class="Q"></path><path d="M545 453c1 1 0 2 0 4v1h-1l-1-1c-1 1-2 1-3 1-1-1-1-1-2-1h0v-1h2l1-3h4z" class="I"></path><path d="M541 453l2 1c0 2-1 2-2 3h-1v-1l1-3zm11-1h1c1 3-1 6 0 9 1 0 1 0 1 1-2 0-2 0-3-2h0c-1 0-2 0-3-1v-1c1-1 1-2 1-3 1-1 0-1 1-1l1 1h1v-3z" class="Q"></path><path d="M648 238c3 0 5 2 7 4l4 3h1l1-1c3 2 7 4 10 7 2 1 4 3 6 3 1 0 2 1 3 1h1 1c3 3 10 4 15 4l16 4 6 2c6 1 13 2 19 2-2 1-3 1-6 1-1-1-3-1-4-1 0 2 1 3 3 4l3 3-8 1-4-1v1h0-2l-1-1-4-1h-1-1 0l-1-1h-1-1c-1 0-1-1-2-1l1 1v1h0l-4-1h0c-1-1-2-1-2-1-1 0-2-1-2-1h0-1l-1-1h-2c0-1 0-1-1-1h-1 0c-2-1-1 0-2-1h-1l-1-1h-1l-2-1c-2 0-3-1-4-1h0l-1-1v1h1l3 1 1 1h1c2 1 2 2 4 2 2 1 5 3 7 4 1 0 0 0 2 1h0c-4-1-8-4-12-5-4-2-8-4-11-6l-14-8-1-3c-1 0-2-1-3-2h-1c-3-4-8-7-12-11z" class="e"></path><path d="M648 238c3 0 5 2 7 4l4 3h1l1-1c3 2 7 4 10 7h-2l1 1-1 1c2 1 4 2 5 3 7 4 14 7 21 10 2 0 4 1 5 2v1l-10-4-2-1c-6-2-12-5-17-8-2-1-4-3-6-4l-1-1c-1 0-2-1-3-2h-1c-3-4-8-7-12-11z" class="g"></path><path d="M670 252l-1-1h2c2 1 4 3 6 3 1 0 2 1 3 1h1 1c3 3 10 4 15 4l16 4c-2 0-3 0-5 1-3 0-7-3-9-1 1 1 1 1 1 3-11-4-21-8-30-14z" class="Y"></path><defs><linearGradient id="T" x1="703.232" y1="267.42" x2="733.305" y2="270.748" xlink:href="#B"><stop offset="0" stop-color="#9a9590"></stop><stop offset="1" stop-color="#c7c5c4"></stop></linearGradient></defs><path fill="url(#T)" d="M700 266c0-2 0-2-1-3 2-2 6 1 9 1 2-1 3-1 5-1l6 2c6 1 13 2 19 2-2 1-3 1-6 1-1-1-3-1-4-1 0 2 1 3 3 4l3 3-8 1-4-1c-1 0-4-1-5-1-5-1-8-3-12-5l-5-2z"></path><path d="M700 266c0-2 0-2-1-3 2-2 6 1 9 1 2-1 3-1 5-1l6 2h-1-8c-1 0-3 0-5-1l-1 1 1 3-5-2z" class="Z"></path><path d="M517 225c3-1 8-1 12 0 3 0 10 1 12 4 2 1 2 4 3 6l5 14c-2 0-4-1-6-2-7-2-14-3-21-3h-8-3v-1c0-2 0-3 1-5 0-1-1-1-2-3h0c1-2 1-4 2-6h1c-1-1-1-1-1-2h0c2-2 3-2 5-2z" class="E"></path><path d="M517 225c3-1 8-1 12 0-2 0-3 0-4 1l-1 2v-1c-1 0-1 0-1-1h-2v1c0 1 1 2 1 3-1 0-1 0-1-1-1 1-1 2-1 3h-1c-1-1-2-2-4-2l-1-1h0c0-1 1-1 2-1 1-1 1-2 1-3z" class="P"></path><path d="M529 225c3 0 10 1 12 4 2 1 2 4 3 6l5 14c-2 0-4-1-6-2v-1-1c0-1-1-2-2-3-1-4-2-9-5-12-1-1-1-1-1-2h-1c-1-1-2 1-2 0-1 0-2-1-2-1h-2-1l2 2h-1c-1 0-2-1-4-1l1-2c1-1 2-1 4-1z" class="C"></path><path d="M512 227h0c2-2 3-2 5-2 0 1 0 2-1 3-1 0-2 0-2 1h0l1 1c2 0 3 1 4 2h1v3l1 1c0 2-2 2-1 4l1 1h0v2h0c0 1 1 1 1 1h-8-3v-1c0-2 0-3 1-5 0-1-1-1-2-3h0c1-2 1-4 2-6h1c-1-1-1-1-1-2z" class="D"></path><path d="M512 238h2l-1-1 2-5v3l2 1v4l-4 1 1 1h1c0 1-1 2-1 2h-3v-1c0-2 0-3 1-5z" class="P"></path><path d="M512 227h0c2-2 3-2 5-2 0 1 0 2-1 3-1 0-2 0-2 1h0l1 1v2l-2 5 1 1h-2c0-1-1-1-2-3h0c1-2 1-4 2-6h1c-1-1-1-1-1-2z" class="R"></path><path d="M486 582h0c1 2 1 5 2 6l1 1 1 1c0 1 0 5-1 6l1 1v1 6 2l-1 1 1 1v2c2 1 3 2 5 2h3v-1l1 1v4l1 1c-1 0-2 1-2 1l-2 1v3h1v2c0 1-1 2-1 3s1 1 1 2l1 1c-1 0-1 1-1 2v4 1 2l-1 2h-1c-1 0-1 0-2 1l1 1c-1 1-2 1-3 2h0l-1 1-1 1 1 1v4c0 2 0 6-1 9 0 2 1 1 1 3 0 1-1 2-1 3v1c2 0 4-1 6-2 2 1 2 1 3 3v1h1l-1 1 1 2h0c-2 2-3 2-4 3l2 1 1 2-1 1h-1c-1 1-1 1-2 1l1 1s0-1 1 0v1c-1 0-3 0-4 1h0l-1 1c1 1 1 3 1 5-1 0-1 1-2 2h-2-1v-4c-1-3 0-6 0-9v-13-51l-1-33z" class="c"></path><path d="M487 630h1c0 1 1 1 2 2h0c-1 3-1 5-1 7s0 2-1 3c0 1 0 0 1 1v1l-1 1c0 1 0 1-1 1v-16z" class="Y"></path><path d="M491 632h0l1 3 1 1v1c1 0 3 1 4 0v2l-1 2h-1c-1 0-1 0-2 1l1 1c-1 1-2 1-3 2v-3l-1-1c1-3 1-6 1-9z" class="M"></path><path d="M487 668c3 4 2 6 3 11l-1 1h1c0 1 0 2 1 4-1 1-1 1-1 2h0v1h-1c0 1 0 1 1 2h-1c-1 1-1 2-1 3h-1v-4c1-6 0-13 0-20z" class="T"></path><path d="M487 668v-8-12h3v4c0 2 0 6-1 9 0 2 1 1 1 3 0 1-1 2-1 3v1l2 1c-1 1-1 2-1 3 0 2 1 4 0 7-1-5 0-7-3-11z" class="Y"></path><path d="M491 621h0v3c2 1 3 0 5-1v-1h1v2c0 1-1 2-1 3s1 1 1 2l1 1c-1 0-1 1-1 2v4 1c-1 1-3 0-4 0v-1l-1-1-1-3h0v-5-6z" class="t"></path><path d="M491 632h3c1-1 0-1 0-2h1c0 1 1 2 1 2-1 1-1 1-2 1v1h2v1h0c-2 0-2 0-3 1l-1-1-1-3zm7-21l1 1v4l1 1c-1 0-2 1-2 1l-2 1v3 1c-2 1-3 2-5 1v-3h0l-1-7 1-1-1-3c2 1 3 2 5 2h3v-1z" class="W"></path><path d="M490 610c2 1 3 2 5 2l-1 1c-1 1-2 1-3 0l-1-3z" class="m"></path><path d="M498 611l1 1v4l1 1c-1 0-2 1-2 1-1-1-1-1-2-3h-1v-1c1 0 1-1 2-2h1v-1z" class="U"></path><path d="M489 668c2 0 4-1 6-2 2 1 2 1 3 3v1h1l-1 1 1 2h0c-2 2-3 2-4 3l2 1 1 2-1 1h-1c-1 1-1 1-2 1l1 1s0-1 1 0v1c-1 0-3 0-4 1h0l-1 1c1 1 1 3 1 5-1 0-1 1-2 2h-2c0-1 0-2 1-3h1c-1-1-1-1-1-2h1v-1h0c0-1 0-1 1-2-1-2-1-3-1-4h-1l1-1c1-3 0-5 0-7 0-1 0-2 1-3l-2-1z" class="S"></path><g class="f"><path d="M491 685c0-2 0-5 1-6 1 0 2 0 3-1h1l1 1v1h-1c-1 1-1 1-2 1l1 1s0-1 1 0v1c-1 0-3 0-4 1h0l-1 1z"></path><path d="M495 676l-1 2-1-1c-1-2-1-4 0-5s1-2 3-2c1 2 2 2 3 3-2 2-3 2-4 3z"></path></g><path d="M486 582h0c1 2 1 5 2 6l1 1 1 1c0 1 0 5-1 6l1 1v1 6 2l-1 1 1 1v2l1 3-1 1h0v3c-1 1-2 1-2 2 1 1 1 1 2 1 0 1 0 1-1 2 0 1 0 3-1 4l2 2-2 2h-1c0-5 1-10 0-15l-1-33z" class="T"></path><path d="M490 614l-1 1-1-1c0-1 0-3-1-4v-1c0-1 1-1 2-2l1 1v2l1 3-1 1h0z" class="i"></path><defs><linearGradient id="U" x1="529.563" y1="367.787" x2="530.419" y2="360.759" xlink:href="#B"><stop offset="0" stop-color="#a8a7a3"></stop><stop offset="1" stop-color="#cecac9"></stop></linearGradient></defs><path fill="url(#U)" d="M469 371c4 0 11 0 15-2 2 0 4-2 6-3 8-3 17-5 26-6l21 1c8 0 23 3 28 9 1 1 2 2 2 3h-1 0c-2-1-4-1-5 1l-1 1h0c-2-2-5-2-7-3l-17-4-19-1-9 1-15 4 1 1h2v2 5c0 1 0 1-1 2-2 0-3-1-4-1l-1 1c-1-1-1-1-2-1l-2 2v-1-1c0-3 1-5 0-7h-2-1-2v1c-3-1-6 0-8 0h-2c-2 2-1 8-1 11v2h0v1c-1 0-1 0-1 1v-9c0-3 0-6-1-9l1-1z"></path><path d="M530 363c11 1 27 3 36 10-2-1-4-1-5 1l-1 1h0c-2-2-5-2-7-3l-17-4-19-1-9 1c2 0 3-1 4-1l1-1c4-1 9-1 14-1 1-1 4 0 6-1l-3-1z" class="W"></path><path d="M517 367c1 0 2 0 3-1h2 0 9c1-1 1-1 2 0 2 0 5 1 8 1 2 1 3 1 5 1 2 1 4 0 6 1l1 3-17-4-19-1z" class="e"></path><path d="M469 371l1 1c4 1 11 0 14-2 4-3 12-4 17-5 10-2 20-3 29-2l3 1c-2 1-5 0-6 1-5 0-10 0-14 1l-1 1c-1 0-2 1-4 1l-15 4 1 1h2v2 5c0 1 0 1-1 2-2 0-3-1-4-1l-1 1c-1-1-1-1-2-1l-2 2v-1-1c0-3 1-5 0-7h-2-1-2v1c-3-1-6 0-8 0h-2c-2 2-1 8-1 11v2h0v1c-1 0-1 0-1 1v-9c0-3 0-6-1-9l1-1z" class="f"></path><path d="M491 381v-6h0v-1l-1 1v2l-1 1-1-1v-3c2-1 4-1 5-2l1 1h2v2 5c0 1 0 1-1 2-2 0-3-1-4-1z" class="k"></path><path d="M497 442l1-1h-3c0-2 2-1 3-2h-4c0-1 3-2 3-2v-1h0 1v-2-2c1-1 2 0 4-1h1 0c-3-1-5 1-7 0 2-1 5-2 8-2 1 0 2 0 2-1 3 0 5 0 7 1 1 1 1 3 2 5 0 1 1 1 1 3 1 0 1 1 2 2 3 1 6 0 9 0l-2 1s0 1-1 2l-1 1c-1 1-1 3-1 5-1 1-1 2-1 4v3c-2 0-4-1-6 0l-2 2h-4l-1 2h0c1 1 1 1 1 2h1v1h-1l-5 1h-1c-1-1-2-1-3-1h-1l1-2c0-1 0 0-1-1v-1-2l-1-1-2 1-1-1h-1c-1 1-2 1-3 2l-1-1c-1 0-1-1-1-1l1-2-1-1c-1-1 1-1 1-3l-2 1v-1c1 0 2-1 3-2l-1-2h1l1 1c2 0 3-1 4-3h0l1-1z" class="N"></path><path d="M507 437v-1l3-3 1 1v3l-1 1-2 2-1-3z" class="R"></path><path d="M508 444h-1c-2-1-1-1-2-2 2-1 2-1 3-2l-1-2c-1 1-2 1-3 1l-1-1h2l-1-1 1-1h1 0v1l1 1v-1l1 3v1l1 1c1 0 1 1 1 1l-1 1h-1z" class="q"></path><path d="M511 434h2c1 2 1 2 3 3 1 0 1 1 2 2 3 1 6 0 9 0l-2 1s0 1-1 2l-1 1-3 1-4 1h-4c-1 1-2 1-3 1l-1 1s1 1 1 2 0 1 1 2l-1 2-2-1v-3h0c-1-2-2-1-3-2l1-1c1 0 2-1 3-2h1l1-1s0-1-1-1l-1-1v-1l2-2 1-1v-3z" class="R"></path><path d="M508 440l2-2 1-1 1 1v5h-2s0-1-1-1l-1-1v-1z" class="Z"></path><path d="M511 434h2c1 2 1 2 3 3 1 0 1 1 2 2 3 1 6 0 9 0l-2 1-2-1-2 2h0c-2 1-3-1-5-1h-2v-2h-2l-1-1v-3z" class="q"></path><path d="M520 444l3-1c-1 1-1 3-1 5-1 1-1 2-1 4v3c-2 0-4-1-6 0l-2 2h-4v-2h1c0-1-1-2-1-2l1-2c-1-1-1-1-1-2s-1-2-1-2l1-1c1 0 2 0 3-1h4l4-1z" class="D"></path><path d="M516 445l4-1c-1 1-2 3-3 4-1-1-2-1-3-1l-1-1c1-1 1-1 3-1z" class="I"></path><path d="M520 444l3-1c-1 1-1 3-1 5h-2-3c1-1 2-3 3-4z" class="E"></path><path d="M509 449c1-1 2-2 3-2l1 2h0c1 0 1 0 2 1v1 2 2l-2 2h-4v-2h1c0-1-1-2-1-2l1-2c-1-1-1-1-1-2z" class="K"></path><path d="M509 453l1-2c1 1 2 0 2 2l1 1c-1 1-2 1-3 1 0-1-1-2-1-2z" class="G"></path><path d="M490 445h1l1 1c2 0 3-1 4-3h0l1-1c0 1 0 1 1 2 2-2 2-2 5-2 0 2 0 2-1 4h3l-1 1c1 1 2 0 3 2h0v3l2 1s1 1 1 2h-1v2l-1 2h0c1 1 1 1 1 2h1v1h-1l-5 1h-1c-1-1-2-1-3-1h-1l1-2c0-1 0 0-1-1v-1-2l-1-1-2 1-1-1h-1c-1 1-2 1-3 2l-1-1c-1 0-1-1-1-1l1-2-1-1c-1-1 1-1 1-3l-2 1v-1c1 0 2-1 3-2l-1-2z" class="Y"></path><path d="M490 453c1-1 2-1 3-2 2 0 2 0 3 1-2 2-3 3-6 4-1 0-1-1-1-1l1-2z" class="f"></path><path d="M490 445h1l1 1c2 0 3-1 4-3h0l1-1c0 1 0 1 1 2l1 1h-2v1c0 1-1 2-2 3l1 1 1-1h1c0 1-1 2-2 2v1c-1-1-1-1-3-1-1 1-2 1-3 2l-1-1c-1-1 1-1 1-3l-2 1v-1c1 0 2-1 3-2l-1-2z" class="W"></path><path d="M502 446h3l-1 1c1 1 2 0 3 2h0v3l2 1s1 1 1 2h-1v2l-1 2h0c1 1 1 1 1 2h1v1h-1l-5 1h-1c-1-1-2-1-3-1h-1l1-2 2-1-1-2c1 0 1-1 2-1l-2-2 1-1v-1h-1v-1c1-1 0-3 0-4l-1 1v-1l2-1z" class="N"></path><path d="M509 455v2l-1 2h0c1 1 1 1 1 2h-6 0 0c0-1 1-2 1-2 1-2 0-2 1-3 1 0 3 0 4-1z" class="O"></path><path d="M502 446h3l-1 1c1 1 2 0 3 2h0v3l2 1s1 1 1 2h-1c-1 1-3 1-4 1h0c0-1 0-3-1-4h0v-1c-1-1-1-2-1-2v-1l-2-1-1 1v-1l2-1z" class="L"></path><path d="M497 622h1s1 0 2-1v1l-1 1 1 1-1 1h3l1 2h-2v1l1 1c0 1 0 1-1 2l1 1 1-1 1 1v3c0 1-2 1-2 2l1 2h3 2c1-2 1-4 1-6 1 1 2 1 4 2h1c2-1 2-2 3-3 1-2 1-2 3-3-1 3-1 5-2 7v1c-3 7-4 14-5 21-1 1-1 3-1 4-1 2-1 4-1 5 0 2-1 3-1 4 0 2 0 3-1 4v1h0l-1-1c-1-2-3 0-4-1v-1l-1-1 1-2h-1c-2 1-3 1-5 1l1-1h-1v-1c-1-2-1-2-3-3-2 1-4 2-6 2v-1c0-1 1-2 1-3 0-2-1-1-1-3 1-3 1-7 1-9v-4l-1-1 1-1 1-1h0c1-1 2-1 3-2l-1-1c1-1 1-1 2-1h1l1-2v-2-1-4c0-1 0-2 1-2l-1-1c0-1-1-1-1-2s1-2 1-3v-2z" class="k"></path><path d="M500 665h1 3c1-1 2 0 2-1h3l1 1h-1v2h-1l-1-1c-1 1 0 3-3 2-1 1-2 1-3 1l-1-1c0-1 0-1-2-2l2-1z" class="T"></path><path d="M506 654v-2h0c1 1 2 1 3 1h1l2 1-1 1h0l-1 3h1l-2 2c-1 1-1 1-1 3h-3 0v-6l-2 1 1-1c1-1 1-1 2-3z" class="N"></path><path d="M509 653h1l2 1-1 1h0l-2 2h-1l-1-1c1-2 1-2 2-3z" class="Z"></path><path d="M491 645c1-1 2-1 3-2l-1-1c1-1 1-1 2-1h1l1-2v5l2 1-1 1c0 1-1 3 0 4l1 2v1l-2 1v-2h-3v1h-1l-2-2-1 1v-4l-1-1 1-1 1-1h0z" class="S"></path><path d="M491 645c2 0 5 0 6 1-1 1-2 1-4 2-1-1-2-1-3-2l1-1z" class="m"></path><path d="M490 646c1 1 2 1 3 2l-1 1c1 1 2 2 2 3v1h-1l-2-2-1 1v-4l-1-1 1-1z" class="e"></path><path d="M490 652l1-1 2 2h1v-1h3v2l-1 1 2 1c0 1-1 2-2 3 1 0 1 0 2 1l-1 2v1 1c1 0 2 0 3 1l-2 1h-3c-2 1-4 2-6 2v-1c0-1 1-2 1-3 0-2-1-1-1-3 1-3 1-7 1-9z" class="m"></path><path d="M502 625l1 2h-2v1l1 1c0 1 0 1-1 2l1 1 1-1 1 1v3c0 1-2 1-2 2l1 2v1h-2l2 2-1 1h1l1 1c0 1 0 1-1 2 0 0-1 1-1 2s0 1 1 1l-1 2c1 1 1 0 1 1h0v4l1 1-1 1 2-1v6c-1-1-1-1-2-1v1h-3v-1c0-1 1-1 1-2h1l-1-2 1-1c-1-1-2-1-3-2 1-2 1-2 2-2l1-1c-1 0-1 0-2-1 1-1 1-1 1-2h-2v-1h1v-1-1l2-1v-1l-2-1 1-2h-1l-1-1 2-1c-2-3-1-5-2-7l-1-1c1-1 2-2 3-2l-2-2v-2h3z" class="T"></path><path d="M513 635h1c2-1 2-2 3-3 1-2 1-2 3-3-1 3-1 5-2 7-2 1-3 2-3 4-1 0-1 1-1 2s0 1-1 2v2h0c0 3-1 5-3 7h-1c-1 0-2 0-3-1h0v2c-1 2-1 2-2 3l-1-1v-4h0c0-1 0 0-1-1l1-2c-1 0-1 0-1-1s1-2 1-2c1-1 1-1 1-2l-1-1h-1l1-1-2-2h2v-1h3 2c1-2 1-4 1-6 1 1 2 1 4 2z" class="R"></path><path d="M503 639h3 2v1l2-1 1 1c-2 1-2 1-3 1l-2-1h-2v1l2 1c0 1-1 1-2 1l1 1h2s1-2 2-2c0 1 0 1 1 1-1 1-1 1-1 2-2 0-2-1-3 0l1 2c0 1-1 1-2 1 0 1 0 2-1 2v1 3h2c-1 2-1 2-2 3l-1-1v-4h0c0-1 0 0-1-1l1-2c-1 0-1 0-1-1s1-2 1-2c1-1 1-1 1-2l-1-1h-1l1-1-2-2h2v-1z" class="Z"></path><path d="M464 733c2 0 4 2 6 1h2 0 4c1 1 2 3 4 4 1 2 3 3 3 4 1 2 3 2 4 3l4 2 3 3c1 1 2 2 2 4h0l1 2c-1 1-1 1-2 1-1 1-2 1-3 3h-1c0 3-1 7 0 9h1 1l1 1v2h-1v1c-1-1-2-1-3-2 0 1 0 2-1 3h-1-4v1 2c-1 0 0 0-1-1l-2-2-1 1c-1-1-2-2-4-3v-2c-2 1-1-1-3-2v3c-1-1-2-2-2-3-1 0-1 0-2-1h-1l-2 2v-5h-2c0-2-2-4-2-5l1-2h2c1 1 1 1 2 1 2 0 1 0 2-2l1-5h0c1-2 1-3 1-5-2-1-3-2-4-4v-1l-3-2c-2 0-3 0-5-2l1-1c0-1 1-1 2-2l2-1z" class="C"></path><path d="M483 742c1 2 3 2 4 3v3c-1 1-1 1-2 1v-2h-2c-1-1 0-1-1-1l-1-1h0l1-1c0-1 0-1 1-2z" class="F"></path><path d="M486 751h0c2-1 3-3 5-3 1 2 1 2 1 4-1 0-1 1-2 1 0 1 1 2 1 3h1c1-2 1-2 2-2h2 0l1 2c-1 1-1 1-2 1-1 1-2 1-3 3h-1l-1 1h-2c-1-1-3-2-4-4h-1c0-3 2-4 3-6z" class="o"></path><path d="M492 756c1-2 1-2 2-2h2 0l1 2c-1 1-1 1-2 1-1 1-2 1-3 3h-1l-1 1c0-1-1-2-2-4h0v-1h3 1z" class="H"></path><path d="M492 756c1-2 1-2 2-2l1 2-1 1c-1 0-1-1-2-1z" class="J"></path><path d="M474 761c2-2 6-3 8-6l2-4h2c-1 2-3 3-3 6h1v1c-2 0-3-1-4 0-2 1-4 3-5 4v2h2l1 1-3 1v1l1 1v1l-1 1h1c-2 1-1-1-3-2v3c-1-1-2-2-2-3-1 0-1 0-2-1h-1l-2 2v-5h-2c0-2-2-4-2-5l1-2h2c1 1 1 1 2 1 2 0 1 0 2-2l1 1 1 1c2 1 2 2 3 3z" class="P"></path><path d="M464 764c0-2-2-4-2-5l1-2h2c1 1 1 1 2 1 2 0 1 0 2-2l1 1 1 1c2 1 2 2 3 3v2c-2-1-2-2-3-1h-1c-1 0-2 0-3-1v-1l-1-1-1 1v1 1c1 1 1 1 1 2h-2z" class="Q"></path><path d="M484 757c1 2 3 3 4 4h2l1-1c0 3-1 7 0 9h1 1l1 1v2h-1v1c-1-1-2-1-3-2 0 1 0 2-1 3h-1-4v1 2c-1 0 0 0-1-1l-2-2-1 1c-1-1-2-2-4-3v-2h-1l1-1v-1l-1-1v-1l3-1-1-1h-2v-2c1-1 3-3 5-4 1-1 2 0 4 0v-1z" class="E"></path><path d="M478 765h1c1 1 1 1 1 3-1 0-1 1-2 1l-2-1-1-1v-1l3-1z" class="J"></path><path d="M476 772l6 1c2 1 2-1 4-2 0 1 0 2 1 2h1v-2h2c0 1 0 2-1 3h-1-4v1 2c-1 0 0 0-1-1l-2-2-1 1c-1-1-2-2-4-3z" class="Q"></path><path d="M477 764h-2v-2c1-1 3-3 5-4 1-1 2 0 4 0 1 2 2 3 4 5l-1 1c-1 0-2 0-3-1l-1 1c2 1 3 3 4 5 0 1 0 1-1 2h-2l-2-1c0-3 0-4-1-5-2-1-3-1-4-1z" class="J"></path><path d="M464 733c2 0 4 2 6 1h2 0 4c1 1 2 3 4 4 1 2 3 3 3 4-1 1-1 1-1 2l-1 1h0l1 1c1 0 0 0 1 1-1 0-3 0-4 1 2 1 2 1 4 1 0 1-2 1-2 2v4h0-2c-1-2-1-1 0-2v-2l-1-1c-1 2 0 1-1 2 0 1-1 2-2 3 0 1-1 2-1 2l-3 1-1-1-1-1 1-5h0c1-2 1-3 1-5-2-1-3-2-4-4v-1l-3-2c-2 0-3 0-5-2l1-1c0-1 1-1 2-2l2-1z" class="H"></path><path d="M470 734h2l1 1v1 1l1 1c1 1 0 0 1 2v1h1 1c0-1 1-1 2-1s1 0 1 1c-1 1-3 1-4 1-1 1-2 1-2 2l-1-1v-2h0v-2c-1-1-3-1-4-3l1-2z" class="J"></path><path d="M467 741c1 1 2 1 3 1l2-1h1v2c-1 1-1 0-1 2 1 1 0 2 0 3 0 0 0 1-1 2h0l3 4-1 1v1h-2-1v1l-1-1 1-5h0c1-2 1-3 1-5-2-1-3-2-4-4v-1z" class="F"></path><path d="M470 751h0c1 1 3 2 2 3 0 1 0 1-1 2h-1v1l-1-1 1-5z" class="I"></path><path d="M464 733c2 0 4 2 6 1l-1 2c1 2 3 2 4 3v2h0-1l-2 1c-1 0-2 0-3-1l-3-2c-2 0-3 0-5-2l1-1c0-1 1-1 2-2l2-1z" class="H"></path><path d="M462 734c0 1 0 1 1 2s1 2 1 3c-2 0-3 0-5-2l1-1c0-1 1-1 2-2z" class="b"></path><defs><linearGradient id="V" x1="534.726" y1="404.463" x2="535.312" y2="399.348" xlink:href="#B"><stop offset="0" stop-color="#bfbdbd"></stop><stop offset="1" stop-color="#e7e4e3"></stop></linearGradient></defs><path fill="url(#V)" d="M520 388c1-1 3-1 4 0h1c1 0 5 0 6 1h7 1 2l1 1h1c1 0 1 0 2 1 0 1 0 3 1 4l-1 2h1c0 1 0 1 2 2l-1 1c5 2 10 3 15 5 3 1 5 3 7 5v3l-2-2c-1-2-4-3-7-4-8-3-16-4-24-4 2 1 7 1 10 1 4 1 8 2 13 4h0-1c-3-1-7-2-10-2h-2-1 0 2c1 1 3 1 4 2 5 1 11 2 15 5h-1 0-1c-4-3-10-2-14-4-2 0-4-1-5-1h-3-3c-3-1-7-1-10-1-12-3-23 1-35 3 5-3 10-4 16-5 2 0 5 0 6-1h1 1l1-1c3 0 7-1 11 0h4l-1-1h-15c-4 0-9 0-13 1h-4 0l1-1h1 0v-1-1-1h-2-2c0-1-1-1-2-1h-2v1c0-3 0-4-1-6h-1v-1c2-2 4-2 6-3l2 1c0-1 0-1 1-1 2 0 3 0 5-1 2 1 4 1 6 1l-1 1 2 1h2 2l1-1 1-1v-1z"></path><path d="M521 396c3 1 6 1 9 1h9c1 0 1 0 2 1l4-1h1c0 1 0 1 2 2l-1 1c-8-1-17-1-26-1v-3z" class="l"></path><defs><linearGradient id="W" x1="528.258" y1="396.561" x2="531.83" y2="386.44" xlink:href="#B"><stop offset="0" stop-color="#c6c3c3"></stop><stop offset="1" stop-color="#e7e5e2"></stop></linearGradient></defs><path fill="url(#W)" d="M520 388c1-1 3-1 4 0h1c1 0 5 0 6 1h7 1 2l1 1h1c1 0 1 0 2 1 0 1 0 3 1 4l-1 2-4 1c-1-1-1-1-2-1h-9c-3 0-6 0-9-1l-1-3v-2h-2l1-1 1-1v-1z"></path><path d="M542 390h1c1 0 1 0 2 1 0 1 0 3 1 4l-1 2-4 1c-1-1-1-1-2-1h-2v-1c-2 0-4 1-5-1-1-1-1-1-3-1v-1h-2c5 0 9 0 14-2h1v-1z" class="I"></path><path d="M542 390h1c1 0 1 0 2 1 0 1 0 3 1 4l-1 2-4 1 1-7v-1z" class="S"></path><defs><linearGradient id="X" x1="502.581" y1="395.346" x2="504.463" y2="387.544" xlink:href="#B"><stop offset="0" stop-color="#9f9b96"></stop><stop offset="1" stop-color="#c2bfbc"></stop></linearGradient></defs><path fill="url(#X)" d="M507 388c2 1 4 1 6 1l-1 1 2 1h2 2 2v2l1 3v3l-18 1v-1h-2-2c0-1-1-1-2-1h-2v1c0-3 0-4-1-6h-1v-1c2-2 4-2 6-3l2 1c0-1 0-1 1-1 2 0 3 0 5-1z"></path><path d="M518 391h2v2l1 3v3l-18 1v-1h-2l1-1h2c1 0 2 0 4-1 0-1 2 0 3-1h1l2 1c-1-1-1-2-1-3-1 0-2 0-3-1h0c2-1 2-2 4-2h2 2z" class="t"></path><path d="M515 395c2-1 3-1 5 0v1c-2 1-3 0-4 1l-1-2z" class="f"></path><path d="M518 391h2v2 2c-2-1-3-1-5 0l1 2h-2 0c-1-1-1-2-1-3-1 0-2 0-3-1h0c2-1 2-2 4-2h2 2z" class="K"></path><path d="M515 395c0-2 0-2 1-4h4v2 2c-2-1-3-1-5 0z" class="S"></path><path d="M539 822c-4 0-10 0-13-1-1-2-1-6-1-9v-17c0-3 0-7 1-10v-2-1l1-1-1-1c-1-2 1-5 2-6 3-1 7-1 10-1 2 1 4 1 6 1 1 0 3 1 4 2l-3 35 1 12c-3-1-5-1-7-1z" class="H"></path><path d="M540 803l2 1h1v1c0 1-1 2-1 4h0l-1 1c-1-1-1-2-2-3l-1 1h0v-2h1c-1-1-1-1-1-2h1l1-1z" class="o"></path><path d="M533 815c1 2 1 3 2 4 3 1 6 1 10 1v-9h0l1 12c-3-1-5-1-7-1h-2l-1-1c-2-1-3-2-4-4l1-2z" class="I"></path><path d="M532 799c1 1 3 1 4 2v2c-1 1-2 2-4 2 1 2 1 3 3 5l-1 2h-2c0 2 2 2 1 3l-1 2c1 2 2 3 4 4l1 1c-3-1-4-1-6-3-1-2-2-4-2-6 1 0 2-2 1-2l-1-1v-4c1-1 2-3 4-4h0c0-1 0-2-1-3z" class="B"></path><path d="M538 773c2 1 4 1 6 1-2 1-2 1-3 3h-4l2 2c1 0 2 1 2 2 2 2 3 2 3 5l-1 1c-2 0-4 0-6-1l-5 1v-1-2c0-1 0-1-1-2 1-1 1-1 2-1v-1h-1c-1-1-1 0-2-1 1-1 2-3 3-4h3c1 0 2-1 2-2z" class="D"></path><path d="M532 780c2-1 3 0 4 0v1h-1v1l1 1c1 0 1 1 1 2v1l-5 1v-1-2c0-1 0-1-1-2 1-1 1-1 2-1v-1h-1z" class="O"></path><path d="M539 822c-4 0-10 0-13-1-1-2-1-6-1-9v-17c0-3 0-7 1-10v-2-1l1-1-1-1c-1-2 1-5 2-6 3-1 7-1 10-1 0 1-1 2-2 2h-3c-1 1-2 3-3 4 1 1 1 0 2 1h1v1c-1 0-1 0-2 1 1 1 1 1 1 2v2 1l-2 1c1 1 2 1 3 1l2 1c0 2-1 3-1 6-1 1-2 0-3 2v1h1c1 1 1 2 1 3h0c-2 1-3 3-4 4v4l1 1c1 0 0 2-1 2 0 2 1 4 2 6 2 2 3 2 6 3h2z" class="Z"></path><path d="M532 786v1l-2 1c1 1 2 1 3 1l2 1c0 2-1 3-1 6-1 1-2 0-3 2v1h1c1 1 1 2 1 3h0c-2 1-3 3-4 4 0-2 0-2 1-4l-1-1c-2-2 0-3 0-4l-1-1c0-2 1-5 2-6-1-1-1-1-2-1v-1c1-1 2-1 4-2z" class="D"></path><path d="M515 455c2-1 4 0 6 0l-2 1v1l1-1c2 1 3 3 5 4s7 0 10 0c2 1 5 1 7 1h1l3 1c1 0 2 0 3 1h3l1 1c2-1 3-1 5 0h1s1 0 1 1v2 1 1 16 1l-5-3h0l-6-2h-1c-2 0-3 0-5-1-6-1-14-2-21-2l-4 1h-6-1c-2-1-3 0-5-1-1-2-1-2 0-3l-1-2c-1-1-1-2-1-4h1v-2c-1-2-1-3-1-4l5-1h1v-1h-1c0-1 0-1-1-2h0l1-2h4l2-2z" class="J"></path><path d="M551 470c2 0 4 0 6 3v1l-7-3 1-1z" class="C"></path><path d="M521 467l2 2c1 0 1 0 2-1 1 1 1 1 1 2 0 2-1 3-2 4l-5-1v1l-1-1 1-1c1 1 1 1 2 1 1-2 2-2 3-3-1-1-2 0-3-1v-2z" class="H"></path><path d="M515 455c2-1 4 0 6 0l-2 1v1l1-1c2 1 3 3 5 4s7 0 10 0c2 1 5 1 7 1h1l3 1c1 0 2 0 3 1h3l1 1c2-1 3-1 5 0h1s1 0 1 1v2 1c-2-1-5-3-8-3h0c-5-1-9-2-13-3-5-1-10-1-15-1h-1l-8 1h-4-1v-1h-1c0-1 0-1-1-2h0l1-2h4l2-2z" class="I"></path><path d="M553 464c2-1 3-1 5 0h1s1 0 1 1v2c-2-1-3-2-5-2-1-1-1-1-2-1z" class="H"></path><path d="M509 457h4c0 1-1 2 0 3h1c0-2 0-2 1-2l1 1v1c2 0 5 0 7 1l-8 1h-4-1v-1h-1c0-1 0-1-1-2h0l1-2z" class="P"></path><path d="M536 478l3-1c0-1 1-2 1-4 0-1 0 0 1-1 1 1 0 3 1 4l1-1c0-1 1-3 0-5h-1s0 1-1 1l-2-2 1-1h5 2c1 1 2 1 4 2l-1 1 7 3-1 3c0 2 0 2 1 4 1 1 1 1 3 1v-13h0v16c0-1-1-2-2-2s-2-1-3-1h-1l-2-1h0c-2-1-1-1-2-1h-2c-2-1-2-1-3-1h-1-2c-1 0-1-1-2-1h-4z" class="F"></path><path d="M523 461h1l-3 1-1 1 2 1h0 1c0 1-1 2-2 3h0v2c1 1 2 0 3 1-1 1-2 1-3 3-1 0-1 0-2-1l-1 1 1 1c1 0 2 0 2 2v1l15 1h4c1 0 1 1 2 1h2 1c1 0 1 0 3 1h2c1 0 0 0 2 1h0l2 1h1c1 0 2 1 3 1s2 1 2 2v1l-5-3h0l-6-2h-1c-2 0-3 0-5-1-6-1-14-2-21-2l-4 1h-6-1c-2-1-3 0-5-1-1-2-1-2 0-3l-1-2c-1-1-1-2-1-4h1v-2c-1-2-1-3-1-4l5-1h1 1 4l8-1z" class="E"></path><path d="M511 462h4c0 1-1 2-1 4h0c0 1-1 2-2 2v1c2 1 4 1 4 3-1 0-2 0-4 1h0c1 1 2 0 3 2h-1c0 1-1 1-1 2 2 1 6 0 9 1h0l-4 1h-6-1c-2-1-3 0-5-1-1-2-1-2 0-3l-1-2c-1-1-1-2-1-4h1v-2c-1-2-1-3-1-4l5-1h1 1z" class="B"></path><path d="M511 462h4c0 1-1 2-1 4-1-1-1-1-2-1h-1v-1-2z" class="Q"></path><path d="M504 463l5-1c-2 0-2 0-3 2 0 1 0 1 1 2 1 0 1 0 2 1v1l-4 1v-2c-1-2-1-3-1-4z" class="G"></path><path d="M505 469l4-1-2 3h1 0c2 1 1 0 2 1-1 1-1 2-2 3l1 1-1 2h4v1h-1c-2-1-3 0-5-1-1-2-1-2 0-3l-1-2c-1-1-1-2-1-4h1z" class="K"></path><path d="M466 769l2-2h1c1 1 1 1 2 1 0 1 1 2 2 3v-3c2 1 1 3 3 2v2c2 1 3 2 4 3l1-1 2 2c1 1 0 1 1 1v-2-1h4 1c1-1 1-2 1-3 1 1 2 1 3 2v-1h1v-2h2l3-1c1 1 3 1 5 2l2 3v5 2c0 3 1 3 3 5h0c1 1 1 3 1 5v1l-2 3c0 1 0 2 1 3 0 1 0 2-1 3h0c0 1-1 2-2 3l1 3-1 2v1l-2 2-1 2-3 3-2 2c-1-1-3-2-4-2-2-3-4-5-7-7h1c-2-1-3-2-4-4h0l-3-2c0-1 0-2 1-4v-1l-1-2 2-2c-1-1-2-2-2-3s1-2 1-3c-1-1-2-2-2-4 1 0 1-1 1-2l-2-2c-1-2-4-4-5-6-2-2-4-4-6-5-1 0-1 1-2 1l-1-1 1-1z" class="N"></path><path d="M484 795c2 1 2 2 4 3l2-2 1 1c1 1 2 1 3 2h-2v1c-1 1-1 1-3 1l-2-2h-2c-1-1-1-2-2-3l1-1z" class="G"></path><path d="M482 799l1 1v1c0 1 0 1 1 1s2 0 3 1 0 1 1 2c0 1 1 1 2 1 0 1 1 2 1 3h-1c-1-1-2-1-3-2-1 0-2 0-3-1h0l-3-2c0-1 0-2 1-4v-1z" class="k"></path><path d="M491 809h2l5 3c1 0 1 1 2 1h1l2-1h1l-1 2-3 3c-1-1-2-1-3-2s-3-1-4-2v-2l-2-2z" class="U"></path><path d="M497 815c2-1 4-1 6-1l-3 3c-1-1-2-1-3-2z" class="c"></path><path d="M484 806c1 1 2 1 3 1 1 1 2 1 3 2h1 0l2 2v2c1 1 3 1 4 2s2 1 3 2l-2 2c-1-1-3-2-4-2-2-3-4-5-7-7h1c-2-1-3-2-4-4z" class="n"></path><path d="M466 769l2-2h1c1 1 1 1 2 1 0 1 1 2 2 3v-3c2 1 1 3 3 2v2c2 1 3 2 4 3l1-1 2 2c1 1 0 1 1 1h2v1h0c0 1-1 2-1 3l-2 2c0-1 0-1-1-2h-1c1-2 1-2 2-2v-1c-1 0-1-1-2-1v-1c-1 1-1 1-2 1s-2-2-3-2c-1-1-1-1-2 0-2-2-4-4-6-5-1 0-1 1-2 1l-1-1 1-1z" class="R"></path><path d="M484 777v-2-1h4l-1 2h4c1 2-1 4 0 6-1 1-2 2-2 3l-1 1h0-2l-1 1c0 1 0 1-1 2v6l-1 1h0v-8c0-1 0-2-1-2v-1c1-1 1-1 1-2h0l2-2c0-1 1-2 1-3h0v-1h-2z" class="O"></path><path d="M490 771c1 1 2 1 3 2v-1h1l3 1v1c0 1-1 1-2 1 1 3 2 5 3 7-1 1-1 2-2 2l-3 3v1h-3l-2-2 1-1c0-1 1-2 2-3-1-2 1-4 0-6h-4l1-2h1c1-1 1-2 1-3z" class="P"></path><path d="M493 773v-1h1l3 1v1c0 1-1 1-2 1h-4l2-2h0z" class="o"></path><path d="M492 782c0-1 0-2 2-3l1 1h0c0 2 1 3 1 4l-3 3v1h-3l-2-2 1-1c0-1 1-2 2-3h1z" class="O"></path><path d="M491 782h1v1c0 2 0 3 1 4v1h-3l-2-2 1-1c0-1 1-2 2-3z" class="D"></path><path d="M500 800h1c1 0 2-1 3-2l1 1 1 1c1 1 1 1 2 1h0c0 1-1 2-2 3l1 3-1 2h-1-4c-1 1-3 0-4 0h-1c-1-1-1-2-1-3v-1c-2-2-2-3-3-5v-1h2v1c2-1 2-1 4-1 1 0 1 0 2 1z" class="L"></path><path d="M494 799v1c2-1 2-1 4-1 1 0 1 0 2 1l1 1h0v1c-2 1-2 1-3 3l-2 2h1c2-1 3-2 5-2v1 1h-2-2l-1 2h-1c-1-1-1-2-1-3v-1c-2-2-2-3-3-5v-1h2z" class="a"></path><path d="M499 769c1 1 3 1 5 2l2 3v5 2c0 3 1 3 3 5h0c1 1 1 3 1 5v1l-2 3c0 1 0 2 1 3 0 1 0 2-1 3-1 0-1 0-2-1l-1-1-1-1c-1 1-2 2-3 2h-1c-1-1-1-1-2-1-2 0-2 0-4 1v-1c-1-1-2-1-3-2l-1-1-2 2c-2-1-2-2-4-3v-6c1-1 1-1 1-2l1-1h2 0l2 2h3v-1l3-3c1 0 1-1 2-2-1-2-2-4-3-7 1 0 2 0 2-1v-1l-3-1v-2h2l3-1z" class="I"></path><path d="M487 789l3 1h0v1c-2 1-2 1-2 2h-1c0-2-1-2 0-4z" class="C"></path><path d="M488 786h0l2 2h3l-1 2c-1 2-1 2-2 3v-2-1h0l-3-1h-1c0-1 1-2 2-3z" class="B"></path><path d="M484 795v-6c1-1 1-1 1-2l1-1h2c-1 1-2 2-2 3 0 2 0 4 1 6l3 1-2 2c-2-1-2-2-4-3z" class="D"></path><path d="M504 788c0-2 1-2 2-3 1 1 1 1 2 1h1 0c0 1-1 3-1 4v1h-2-5c-1 1-2 0-3 0l6-3z" class="F"></path><path d="M504 788h1c1 1 1 1 0 2-2 1-3 1-4 1-1 1-2 0-3 0l6-3z" class="H"></path><path d="M491 797l2-1c0 1 1 1 1 1 3 0 5-1 7-3 1 1 2 3 4 3h0c1-1 1-3 1-4 2-1 2-1 4-1l-2 3c0 1 0 2 1 3 0 1 0 2-1 3-1 0-1 0-2-1l-1-1-1-1c-1 1-2 2-3 2h-1c-1-1-1-1-2-1-2 0-2 0-4 1v-1c-1-1-2-1-3-2z" class="D"></path><path d="M498 782l2 2h1v-1c-1-1-3-1-1-3h1l3 4-2 2v1l2 1-6 3h0-1v4c-1 0-2 1-3 1-1-1-1-2 0-3l-2-3 1-2v-1l3-3c1 0 1-1 2-2z" class="E"></path><path d="M495 787c1-1 1-1 3 0v2l-1 1-2-2v-1z" class="H"></path><path d="M499 769c1 1 3 1 5 2l2 3v5 2c0 3 1 3 3 5h-1c-1 0-1 0-2-1-1 1-2 1-2 3l-2-1v-1l2-2-3-4h-1c-2 2 0 2 1 3v1h-1l-2-2c-1-2-2-4-3-7 1 0 2 0 2-1v-1l-3-1v-2h2l3-1z" class="Q"></path><path d="M499 769c1 1 3 1 5 2l2 3-2 2-1-1c-2 1-2 2-3 3-1-1-1-2-1-3h1c0-2 0-2-1-3-1 0-1 1-2 0l-1-2 3-1z" class="o"></path><path d="M504 582c2 0 3 0 5 1l2 1h2v1h2c2 2 2 7 2 10v1c0 2 0 1 1 3 0 0-1 3-2 4h4c0 1 0 2-1 4l-2 1-1 1h0c0 1 0 3 1 4l1 1c0 2 0 3-2 5 1 0 1 1 1 1l2 1 2 1c1 0 0-1 2-1l1 1c-1 0-2 0-2 1l-2 6c-2 1-2 1-3 3-1 1-1 2-3 3h-1c-2-1-3-1-4-2 0 2 0 4-1 6h-2-3l-1-2c0-1 2-1 2-2v-3l-1-1-1 1-1-1c1-1 1-1 1-2l-1-1v-1h2l-1-2h-3l1-1-1-1 1-1v-1c-1 1-2 1-2 1h-1-1v-3l2-1s1-1 2-1l-1-1v-4l-1-1v1h-3c-2 0-3-1-5-2v-2l-1-1 1-1v-2-6-1l-1-1c1-1 1-5 1-6l-1-1 1-1 1-1v-2l1-1c1 0 2 1 3 1l2 1 2-2c1 1 2 1 4 1l1-2v-1z" class="B"></path><path d="M513 595l-1 1c-1-1-1-2-2-2 0-1 1-1 1-1 1-1 2 0 2 0v2zm2 18h1v6h-1c-1-2-1-4 0-6z" class="P"></path><path d="M513 585h2c2 2 2 7 2 10v1c0 2 0 1 1 3 0 0-1 3-2 4h0-4c3-3 0-5 2-7l-1-1v-2c1-1 0-4 1-5l1-1v-1l-1-1h-1z" class="C"></path><path d="M509 628c0 1 0 1 1 1s2 0 3 1h1 0v-5-1c0-2 0 0 1-2h-1v-1l1-1h2l2 1 2 1c1 0 0-1 2-1l1 1c-1 0-2 0-2 1l-2 6c-2 1-2 1-3 3-1 1-1 2-3 3h-1c-2-1-3-1-4-2l-1-1h0c1-1 1-2 1-3h-1l1-1z" class="P"></path><path d="M509 629l1 1h1c0 1 0 2 1 3l1 2c-2-1-3-1-4-2l-1-1h0c1-1 1-2 1-3z" class="h"></path><path d="M519 621l2 1c1 0 0-1 2-1l1 1c-1 0-2 0-2 1-2 0-2 1-3 2l1 2-1 1h-2c-1-2 0-2 1-3-1-1-1-2-1-3h2v-1z" class="D"></path><path d="M509 583l2 1c-1 1-2 4-1 5h3v1c-2 1-4 1-5 4v3h1c1 0 1 0 2 1v1c-1 1-1 1-3 1h0c0 1 0 2-1 3h1 1 1c1 1 1 2 2 3v3l-1 2h0c1 2 1 7 0 9h1l-1 3h1v2c-1 2-1 2-2 3h-1l-1 1h1c0 1 0 2-1 3h0l1 1c0 2 0 4-1 6h-2-3l-1-2c0-1 2-1 2-2v-3l-1-1h-1l1-1h1v-4h-1l3-4h-1v-2l1-1c1-1 0-3-1-4v-1l1-2v-5c1-1 1-1 1-2l-2-1c0-2 0-6 1-8l-1-1h-1-1c1-1 3-2 4-3h-3c0-1 2-1 2-2l-2-1h0l1-2c-1-1-1-1-1-2h1c1-1 3-1 4-2z" class="P"></path><path d="M509 614l1-1 1 1c0 2 0 4-1 5-1 0-1 0-2-1v-3l1-1z" class="O"></path><path d="M505 622v-2l1-1c1-1 0-3-1-4v-1l1-2c1 1 1 2 1 3 1 2 0 5 0 8l1-1c0-1 0-1 1-1 1-1 2-1 3-1l-1 3h1v2c-1 2-1 2-2 3h-1l-1 1h1c0 1 0 2-1 3h-1c-1-1-2-1-2-2s1-1 2-2 0-3 0-5l-1-1h-1z" class="G"></path><path d="M506 622l1 1c0 2 1 4 0 5s-2 1-2 2 1 1 2 2h1 0l1 1c0 2 0 4-1 6h-2-3l-1-2c0-1 2-1 2-2v-3l-1-1h-1l1-1h1v-4h-1l3-4z" class="L"></path><path d="M506 639c0-2-1-4 0-6l2-1 1 1c0 2 0 4-1 6h-2z" class="c"></path><path d="M504 582c2 0 3 0 5 1-1 1-3 1-4 2h-1c0 1 0 1 1 2l-1 2h0l2 1c0 1-2 1-2 2h3c-1 1-3 2-4 3h1 1l1 1c-1 2-1 6-1 8l2 1c0 1 0 1-1 2v5l-1 2v1c1 1 2 3 1 4l-1 1v2h1l-3 4h1v4h-1l-1 1h1l-1 1-1-1c1-1 1-1 1-2l-1-1v-1h2l-1-2h-3l1-1-1-1 1-1v-1c-1 1-2 1-2 1h-1-1v-3l2-1s1-1 2-1l-1-1v-4l-1-1v1h-3c-2 0-3-1-5-2v-2l-1-1 1-1v-2-6-1l-1-1c1-1 1-5 1-6l-1-1 1-1 1-1v-2l1-1c1 0 2 1 3 1l2 1 2-2c1 1 2 1 4 1l1-2v-1z" class="q"></path><path d="M505 622h0c-1-2-1-2 0-3v-2c-1-1-1-2-1-4v-3l-1-1 2-2v-3c-1 1-1 0-2 0l-1-3c1-2 2-3 4-5-1 2-1 6-1 8l2 1c0 1 0 1-1 2v5l-1 2v1c1 1 2 3 1 4l-1 1v2z" class="R"></path><path d="M497 611c0-1 0-1 1-2l3-1v3c1 1 1 1 2 1-2 2-1 4-2 7v1c0 1 0 1 1 2v3h-3l1-1-1-1 1-1v-1c-1 1-2 1-2 1h-1-1v-3l2-1s1-1 2-1l-1-1v-4l-1-1h-1z" class="Y"></path><path d="M490 588l1-1v-2l1-1c1 0 2 1 3 1l2 1c1 0 1 1 2 2v3c-1 1-1 1-1 2 1 1 2 0 3 1l-3 3h0 2v3 3c-1 0-2 0-2 1 1 1 2 0 3 1v1l-1 1h0c1 1 1 0 1 1l-3 1c-1 1-1 1-1 2h1v1h-3c-2 0-3-1-5-2v-2l-1-1 1-1v-2-6-1l-1-1c1-1 1-5 1-6l-1-1 1-1z" class="c"></path><path d="M490 606c2 0 5-1 7 0v1h-1c-2 1-4 1-6 1l-1-1 1-1z" class="m"></path><path d="M490 608c2 0 4 0 6-1-1 1-1 2-1 4h2 1v1h-3c-2 0-3-1-5-2v-2z" class="X"></path><path d="M490 588l1-1v-2l1-1c1 0 2 1 3 1 1 2 1 3 0 5v1h0 0c1 1 0 2 0 3 1 1 2-1 1 1l-1 1 1 1c-1 1-1 1-2 1l1 1h1v1 1h-2 0l2 2c-2 1-3 2-5 2l-1-1v-6-1l-1-1c1-1 1-5 1-6l-1-1 1-1z" class="m"></path><path d="M517 367l19 1 17 4c2 1 5 1 7 3h-1-1-1c-1 1 0 5 0 7v2l-2-2 1 3c0 1 0 2-1 3-1-1-1-1-2-1l-1 1c-1-1-1-1-2-1s-2 0-3-1h-2v1 1c-2 1-4 0-7 0l1 1h-1-7c-1-1-5-1-6-1h-1c-1-1-3-1-4 0v1l-1 1-1 1h-2-2l-2-1 1-1c-2 0-4 0-6-1-2 1-3 1-5 1-1 0-1 0-1 1l-2-1c-2 0-4 1-5 0v-5c-1-1-1-1-1-2h2c1-1 1-1 1-2v-5-2h-2l-1-1 15-4 9-1z" class="I"></path><path d="M519 376l1-5 1-1v8h-1-1v-2zm9-6h3v3c0 3-1 5-1 7l-2-10z" class="i"></path><path d="M512 387c-1-1-1-1-1-2l2-1v-2c1-1 1-1 2-1v1l1 1v3l-1 1h-3z" class="F"></path><path d="M516 383v-2h4v-1 6 2 1l-1 1-1 1h-2-2l-2-1 1-1 2-2 1-1v-3z" class="e"></path><path d="M515 387l1-1v2c1 1 2 1 4 1l-1 1-1 1h-2-2l-2-1 1-1 2-2z" class="V"></path><path d="M536 368l17 4c2 1 5 1 7 3h-1-1-1c-1 1 0 5 0 7v2l-2-2h-2-1c-1 0-1 0-1-1v-1-6l-1 1h0-1v-2h-1c0 2 0 6-1 7s-1 1-2 1c-1-1-2-1-3-2h-2-1v-8h0-2c-2 1 0 5-1 7l-1 1c-1-1 0-6-2-9h-2-3l-1-1h2 3c1-1 3-1 4-1z" class="P"></path><path d="M539 379h1 2c1 1 2 1 3 2 1 0 1 0 2-1s1-5 1-7h1v2h1 0l1-1v6 1c0 1 0 1 1 1h1 2l1 3c0 1 0 2-1 3-1-1-1-1-2-1l-1 1c-1-1-1-1-2-1s-2 0-3-1h-2v1 1c-2 1-4 0-7 0l1 1h-1-7c-1-1-5-1-6-1h-1c-1-1-3-1-4 0v-2-6l1-1h18z" class="F"></path><path d="M520 386h5c1 1 2 0 4 0 1 0 0 0 1 1h7 0v-1h2v1c1 0 4 0 5 1v-2l-3-1s0-1-1-1v-1h1c1 1 2 0 4 0l2 1 1-1v-1l2 2h0c-2 1-2 1-3 2h-2v1 1c-2 1-4 0-7 0l1 1h-1-7c-1-1-5-1-6-1h-1c-1-1-3-1-4 0v-2z" class="E"></path><path d="M517 367l19 1c-1 0-3 0-4 1h-3-2l-1 1v1 2c-1 1 0 4 0 5l-1 1c-2-2-1-4-1-6v-3c-1-1-2-1-3-1v1l-1 1-1 5v2c-1 0-2-1-3-1v2c-3 1-5 1-7 1l-1 1c0 2 0 4 2 6h2 3l-2 2c-2 0-4 0-6-1-2 1-3 1-5 1-1 0-1 0-1 1l-2-1c-2 0-4 1-5 0v-5c-1-1-1-1-1-2h2c1-1 1-1 1-2v-5-2h-2l-1-1 15-4 9-1z" class="N"></path><path d="M516 377c0-2 0-5 1-7h2v6 2c-1 0-2-1-3-1z" class="E"></path><path d="M513 370h1v8l-1 1c-1 0-1 0-2-1v-4c0-2 0-3 2-4z" class="I"></path><path d="M496 375l1-1h1l1 3c0 1-1 2-1 3l2 1-3 1v3c-1-1-2-1-3-1-1-1-1-1-1-2h2c1-1 1-1 1-2v-5z" class="G"></path><path d="M506 380l-1-2c0-1 1-1 1-2v-4c2 0 2-1 3 0v8l-1 1c0 2 0 4 2 6h2 3l-2 2c-2 0-4 0-6-1-2 1-3 1-5 1-1 0-1 0-1 1l-2-1c-2 0-4 1-5 0v-5c1 0 2 0 3 1v-3l3-1 1-1v-5c0-1 0-2 1-3l1 1v2c1 2 1 3 0 5h3z" class="D"></path><path d="M506 380c1 1 1 2 1 4h-4c0-1-1-2-1-3l1-1h3z" class="Q"></path><path d="M494 384c1 0 2 0 3 1h1 2v1s0 1-1 2h1c1 0 3 1 4 0h3c-2 1-3 1-5 1-1 0-1 0-1 1l-2-1c-2 0-4 1-5 0v-5z" class="j"></path><path d="M510 235c1 2 2 2 2 3-1 2-1 3-1 5v1h3 8c7 0 14 1 21 3 2 1 4 2 6 2l1 3 8 31v1l-1-1c-2 0-4-1-6-2-1 0-3-1-5-2-3-1-7-1-10-2s-7 0-10-1h0-1c-2-1-6-1-8-1l-2-2h-2l-1-2c1-1 1 0 1-1-1-3-1-6-1-8v-1-2h1l-1-1 1-2v-2s0-1 1-2l1-1c0-1 2-4 3-6-2 1-4 1-5 1h-1c-2-1-3-1-5-1h-1 0c0-2 1-3 1-4l1-1c1-2 1-1 1-2l1-3z" class="H"></path><path d="M524 261h1 1c1 3 1 6 1 9h-2l-1-1v-8z" class="E"></path><path d="M519 261h2l1 1c0 2 0 7-1 8-1 0-1 0-2-1 0-3-1-5 0-8z" class="C"></path><path d="M512 261l1-1h2l1 1s1 1 2 1l-1 8-1 1h0-4c1-1 1 0 1-1-1-3-1-6-1-8v-1z" class="B"></path><path d="M512 261l1-1h2l1 1c-1 1-1 3-1 4-1 2 0 3 0 4s1 1 1 2h0-4c1-1 1 0 1-1-1-3-1-6-1-8v-1z" class="V"></path><path d="M512 271h4c0 1 1 1 2 1 1 1 2 1 3 2 5 0 10 0 15 1 2 1 4 1 6 1 1 1 2 1 3 1h1l6 3 2 1 1 1c1 0 1 1 2 1-2 0-4-1-6-2-1 0-3-1-5-2-3-1-7-1-10-2s-7 0-10-1h0-1c-2-1-6-1-8-1l-2-2h-2l-1-2z" class="C"></path><path d="M510 235c1 2 2 2 2 3-1 2-1 3-1 5v1h3 8c7 0 14 1 21 3 2 1 4 2 6 2l1 3c-4-3-9-5-14-6h-1c-5-1-9-1-14-1h-3c-2 1-4 1-5 1h-1c-2-1-3-1-5-1h-1 0c0-2 1-3 1-4l1-1c1-2 1-1 1-2l1-3z" class="Z"></path><path d="M518 245h3c0 2 0 3-1 4 0 1 0 2 1 3h0c2 0 3 0 4 1 1 2 0 3 1 5l-1 1c-1-1-2-1-3-2h-1v1l-2 2c-1 0-1 0-2 1l1 1c-1 0-2-1-2-1l-1-1h-2l-1 1v-2h1l-1-1 1-2v-2s0-1 1-2l1-1c0-1 2-4 3-6z" class="C"></path><path d="M515 251l3 1h0v1c-1 0-1 0-2 1l1 4c0 1 0 1-2 2h-2l-1 1v-2h1l-1-1 1-2v-2s0-1 1-2l1-1z" class="B"></path><path d="M567 832v-1c1-3 5-6 8-6l2 1c2-1 4-3 6-4s3-1 5 0l3 3-2 2c0 1 0 2 1 2 1 1 2 1 2 2l1 1h-1c-1 2-1 4-2 5l2 2h2 0 0c-2 1-2 2-3 4v1c1 0 2 1 2 1l2 1-2 1v1h-3l-1 2 1 1-1 3v2 1l1 2c-1 1-1 1-1 2-3 0-6 0-8 1s-3 1-5 2h-4-1c-4-1-7-2-10-3h-1c-1-1-6-3-9-4-1 0-3-1-5-2-3 0-6-2-9-3l1-2v-1l1-3s1-1 2-1h2s1 1 2 1h0c3-1 4-1 6 0 1 0 2 1 2 1 2 1 3 1 4 2h1l1-1 2-2c1-2 0-3-1-5v-3c0-1 2-2 3-3v-1l4-2z" class="D"></path><path d="M577 847c0 1 0 1-1 1 0 1-1 3-2 3-1-1-1-1-1-2l1-2v-1h-2 0c1-2 2-3 4-4 0 1 1 2 1 4v1z" class="C"></path><path d="M563 835c-1 1-1 2-2 3v2l1 1 3-2c1 0 2-1 2-1 1-1 1 0 2-1h3c0 2 0 2-2 3-1 2-1 2-3 2-1 2-2 3-2 4h0l-1 1c-1-2-2-2-2-3l2-1-1-1-3-1v-3c0-1 2-2 3-3z" class="E"></path><path d="M572 837c0 2 0 2-2 3-1 2-1 2-3 2 0-2 0-3 2-5h3z" class="C"></path><path d="M563 835v-1l4-2c1 1 3 1 4 2h0c0 1 0 1 1 1v2h-3c-1 1-1 0-2 1 0 0-1 1-2 1l-3 2-1-1v-2c1-1 1-2 2-3z" class="o"></path><path d="M580 848c1 1 3 1 4 2l-1 2c-1 1-2 2-3 4l-1 1-1-1-4-1h-2l-1 1c0 1 0 0-1 1h-1v-1c1 0 1-1 2-2s2-1 4-1h0c1 0 1 0 2-1v-2h2l1-2z" class="O"></path><path d="M574 855h1c2 0 2-1 3-1 1-1 1-2 2-3l3 1c-1 1-2 2-3 4l-1 1-1-1-4-1z" class="L"></path><path d="M588 822l3 3-2 2c0 1 0 2 1 2 1 1 2 1 2 2-1 1-1 1-3 1v-1c-2 1-2 1-3 2v2s-1 1-1 2h-3l-1-1c-1-1-3 2-3 2h-2 0l-2 2c-1-1-2 0-4 0h0c2-1 2-1 2-3v-2c1 0 2-1 3-2 2 1 1 2 2 4l3-6c2 0 2 1 4 1l1-2v-1-1c2-2 3-3 3-6z" class="Q"></path><path d="M567 832v-1c1-3 5-6 8-6l2 1c2-1 4-3 6-4s3-1 5 0c0 3-1 4-3 6v1 1l-1 2c-2 0-2-1-4-1l-3 6c-1-2 0-3-2-4-1 1-2 2-3 2s-1 0-1-1h0c-1-1-3-1-4-2z" class="F"></path><path d="M586 838c1 0 2-1 3-1h1 0l2 2h2 0 0c-2 1-2 2-3 4v1c1 0 2 1 2 1l2 1-2 1v1h-3l-1 2c-1 1-2 1-2 2-1 0-2 1-3 1v2l-4 1c1-2 2-3 3-4l1-2c-1-1-3-1-4-2l-3-1h0v-1c0-2-1-3-1-4 1 0 2-1 3-1h1 1s1-1 2-1c1-1 1-2 3-2z" class="R"></path><path d="M584 850c1 1 1 1 3 1v-1-1c-1-2-1 0-1-2l1-1c1 0 2 0 3 2l-1 2c-1 1-2 1-2 2-1 0-2 1-3 1v2l-4 1c1-2 2-3 3-4l1-2z" class="N"></path><path d="M586 838c1 0 2-1 3-1h1l-1 2h1v3c-1-1-1-1-2-1-1 1-1 1-1 2h-1l-1 1c1 0 1 1 1 2-1 0-2 1-3 1s-2-1-2-2c-2 0-3 0-4 2h0v-1c0-2-1-3-1-4 1 0 2-1 3-1h1 1s1-1 2-1c1-1 1-2 3-2z" class="O"></path><path d="M581 841s1-1 2-1c1-1 1-2 3-2 0 1 0 3-1 4-2-2-2-1-4-1z" class="K"></path><path d="M576 842c1 0 2-1 3-1h1l-1 1 2 3h0c-2 0-3 0-4 2h0v-1c0-2-1-3-1-4z" class="j"></path><path d="M541 845h2s1 1 2 1h0c3-1 4-1 6 0 1 0 2 1 2 1 2 1 3 1 4 2h1v2c1 1 2 1 3 1l1-1c1-2-1-1 2-2 1 0 1 1 2 0h0c1-1 2-2 3-2h0c-1 3-2 3-5 5v1h1c2 1 3 1 4 3v1h1c1-1 1 0 1-1l1-1h2l4 1 1 1 1-1 4-1v-2c1 0 2-1 3-1 0-1 1-1 2-2l1 1-1 3v2 1l1 2c-1 1-1 1-1 2-3 0-6 0-8 1s-3 1-5 2h-4-1c-4-1-7-2-10-3h-1c-1-1-6-3-9-4-1 0-3-1-5-2-3 0-6-2-9-3l1-2v-1l1-3s1-1 2-1z" class="l"></path><path d="M572 864l1-2h1 7c-2 1-3 1-5 2h-4z" class="D"></path><path d="M564 859c3 1 7 2 10 3h0-1l-1 2h-1c-4-1-7-2-10-3 1 0 2-1 3-2z" class="G"></path><path d="M538 850v-1l26 10c-1 1-2 2-3 2h-1c-1-1-6-3-9-4-1 0-3-1-5-2-3 0-6-2-9-3l1-2z" class="B"></path><path d="M541 845h2s1 1 2 1h0c3-1 4-1 6 0 1 0 2 1 2 1 2 1 3 1 4 2h1v2 2c1 1 1 1 1 2v1l-3-2c-1 0-2-1-4-1-1 0-1 0-2-1s-2-1-3-1c-1-1-2-1-3-2-2 0-3 0-4-1 0-1 0-2 1-3z" class="k"></path><path d="M550 852c0-1 1-2 3-3 0 1 1 2 2 3 1 0 1 0 2-1v-2h1v2 2c1 1 1 1 1 2v1l-3-2c-1 0-2-1-4-1-1 0-1 0-2-1z" class="i"></path><path d="M558 851c1 1 2 1 3 1l1-1c1-2-1-1 2-2 1 0 1 1 2 0h0c1-1 2-2 3-2h0c-1 3-2 3-5 5v1h1c2 1 3 1 4 3v1h1c1-1 1 0 1-1l1-1h2l4 1 1 1 1-1 4-1v-2c1 0 2-1 3-1 0-1 1-1 2-2l1 1-1 3v2 1c-1 0-2 1-3 2-1-1-3-1-4-1l-1 1h-1v-1h-1v2h0-8c0-1-1-1-1-1h-1c-1 0-2-1-3-1v-1l-3-3v1c0 1 1 1 1 2-2 0-3-1-5-1v-1c0-1 0-1-1-2v-2z" class="r"></path><path d="M570 857c1-1 1 0 1-1l1-1h2l4 1-1 1c-2 1-3 1-5 1l-2-1z" class="a"></path><path d="M622 827l1 1h1v-2l1-1v2l1 1 2 1c1 0 1 0 2-1 2 1 3 2 4 5 1-2 1-2 3-2v2h-2c0 1 0 1 1 2s0 4 1 6c-1 2-1 4-1 6v1l-1 1h-1-2l-1-1-1 1h-1v1 5c-2 1-1 2-4 2h-1l-1 2h-3c4 1 7 1 11 3l-2 2 1 2c-1 0-1-1-2-1l-2 1h0l-6-2c-3-2-7 0-10-1h0c-2-1-4-1-6 0h-3c-4-1-6-1-9-1v1c-1 0-4 0-5 1l1 1-1 1c-2-1-3-1-4-1l-7-1c2-1 3-1 5-2s5-1 8-1c0-1 0-1 1-2l-1-2v-1-2l1-3-1-1 1-2h3v-1l2-1-2-1s-1-1-2-1v-1c1-2 1-3 3-4h0 0-2l-2-2c1-1 1-3 2-5h1l-1-1c0-1-1-1-2-2-1 0-1-1-1-2l2-2 4 4c1 0 2 0 2 1 1 1 2 1 3 2 1 0 2 1 2 3l4 4 4-2h1c1 1 0 1 1 1l1-1-2-2 2-2c1-2 2-3 4-3h1v-1c2-1 3-2 4-2z" class="B"></path><path d="M595 839l5-2v1 2l1 1 1-1c0 1 1 1 1 2l-2 1c-1-1-1-1-2-1h0-3c-1-1-2-1-2-3h0 1z" class="F"></path><path d="M612 847l2 1v2c1 0 2 0 3-1v-1l-1-1v-1h2l3-6 1-1v5l-1 1c-1 1-2 1-3 3v1l1 1v1h-1l-2 1c-1 0-2 0-3-1 0-1 0-2-1-2v-1-1z" class="G"></path><path d="M621 845l1 1c-2 1-1 1-1 3v4c-1 1-2 1-3 2v1l1 1-2 1h0l-2-3-1 1v-1c-1-2-1-4-2-6 1 0 1 1 1 2 1 1 2 1 3 1l2-1h1v-1l-1-1v-1c1-2 2-2 3-3z" class="h"></path><path d="M613 837l-2-2 2-2c1-2 2-3 4-3l2 1v1 2l-1 1h0l-1 2c0 1 0 2-1 3v1l-1 1c-1 0-1-1-2-2v-2h1l1-1h-1-1z" class="J"></path><path d="M622 827l1 1h1v-2l1-1v2l1 1v5l-1 1c-1 0-2 1-3 1l1-1c-1-1-2-1-2-1l-1 1 1 1v2h0c-1 0-2-1-3-2l1-1v-2-1l-2-1h1v-1c2-1 3-2 4-2z" class="L"></path><path d="M618 830c1-1 2-1 3-1v4l-1 1 1 1v2h0c-1 0-2-1-3-2l1-1v-2-1l-2-1h1z" class="E"></path><path d="M591 825l4 4v1c0 1 1 2 2 3v3c0 1-1 2-2 3h-1 0-2l-2-2c1-1 1-3 2-5h1l-1-1c0-1-1-1-2-2-1 0-1-1-1-2l2-2z" class="C"></path><path d="M592 832l1 1 1 1c0 1 0 2 1 3l-1 2h-2l-2-2c1-1 1-3 2-5z" class="H"></path><path d="M626 828l2 1c1 0 1 0 2-1 2 1 3 2 4 5 1-2 1-2 3-2v2h-2c0 1 0 1 1 2s0 4 1 6c-1 2-1 4-1 6v1l-1 1h-1-2l-1-1-1 1h-2c0 1-1 2-2 2s-1 0-1 1l-1-2-1 2h-1l-1-3c0-2-1-2 1-3l-1-1 1-1v-5s1 0 2-1c-1-1-1-2-2-3 1 0 2-1 3-1l1-1v-5z" class="T"></path><path d="M628 834h2c0 2 0 2-1 4v1h0c-1-2-1-4-1-5z" class="U"></path><path d="M634 833c1-2 1-2 3-2v2h-2c0 1 0 1 1 2s0 4 1 6c-1 2-1 4-1 6v1l-1 1h-1-2l-1-1-1 1h-2 1 1c0-2 1-3 2-5 0-1 0-1 1-1 0-1 1-2 1-2 0-1 0-5-1-6-1 0 0 0-1-1l1-1 1 1v-1z" class="k"></path><path d="M622 835c1 0 2-1 3-1v1 2 1h0c2 1 3 2 4 3-1 2-3 4-4 6 0 1-1 2-1 3l-1 2h-1l-1-3c0-2-1-2 1-3l-1-1 1-1v-5s1 0 2-1c-1-1-1-2-2-3z" class="S"></path><path d="M593 845s-1-1-2-1v-1c1-2 1-3 3-4 0 2 1 2 2 3h3 0c1 0 1 0 2 1l2-1h1 1c0 2-3 4-3 6 1 0 2 1 4 1l1 1h0v-2c1-2 2 0 4-2v-2-1-1h1l1 1c0 1 0 2-1 4v1 1c1 2 1 4 2 6v1l1-1 2 3h0l2-1-1-1v-1c1-1 2-1 3-2v-4l1 3h1l1-2 1 2c0-1 0-1 1-1s2-1 2-2h2-1v1 5c-2 1-1 2-4 2h-1l-1 2h-3c4 1 7 1 11 3l-2 2 1 2c-1 0-1-1-2-1l-2 1h0l-6-2c-3-2-7 0-10-1h0c-2-1-4-1-6 0h-3c-4-1-6-1-9-1v1c-1 0-4 0-5 1l1 1-1 1c-2-1-3-1-4-1l-7-1c2-1 3-1 5-2s5-1 8-1c0-1 0-1 1-2l-1-2v-1-2l1-3-1-1 1-2h3v-1l2-1-2-1z" class="T"></path><path d="M602 858c1-1 1-2 2-2v2l1-1s0-1 1-1c0-1 1-2 2-2v1l1 1 2-2v2h1v-1h2v1l1-1 2 3h-7v-2h-3v3c-2 0-3 0-5-1z" class="c"></path><path d="M602 848c1 0 2 1 4 1l1 1h0v-2c1-2 2 0 4-2v-2-1-1h1l1 1c0 1 0 2-1 4v1h-2l-2 2c1 1 1 1 1 3-1 0-2-2-4-1-1 0-1 1-2 2s-2 0-3 0v-1c0-2 1-3 2-5z" class="R"></path><path d="M590 848h3l1 1h-3l1 1h3l1 2 1 1h-2v1c1 1 1 0 3 0v1l-3 3-1-1c-2 0-3-1-5-1v-2l1-3-1-1 1-2z" class="N"></path><path d="M590 848h3l1 1h-3l1 1h3l1 2h-1l-2 2h-3 0c1-2 1-1 2-2h-1l-1-1-1-1 1-2z" class="G"></path><path d="M593 845s-1-1-2-1v-1c1-2 1-3 3-4 0 2 1 2 2 3h3 0c1 0 1 0 2 1l2-1h1c-1 1-2 2-2 4l-3 3-1-1h-1c-1 0-1-1-2-2l-2-1z" class="K"></path><path d="M593 845s-1-1-2-1v-1c1-2 1-3 3-4 0 2 1 2 2 3h3l-1 1 1 2v1h-1l-1-1h-4z" class="C"></path><path d="M589 856c2 0 3 1 5 1l1 1 2 1h1s0-1 1-2c1 0 1 2 3 1h0c2 1 3 1 5 1l1 1c-7 1-13 2-19 1 0-1 0-1 1-2l-1-2v-1z" class="n"></path><path d="M628 849h2-1v1 5c-2 1-1 2-4 2h-1l-1 2h-3l-12 1-1-1v-3h3v2h7 0l2-1-1-1v-1c1-1 2-1 3-2v-4l1 3h1l1-2 1 2c0-1 0-1 1-1s2-1 2-2z" class="i"></path><path d="M617 858h2l2-2v-2l1-1 1 1c2 1 2 0 4 0v-1l1 1c-2 1-2 2-4 2l-1-1v1l1 1-1 2h-3l-12 1-1-1v-3h3v2h7 0z" class="S"></path><path d="M608 860l12-1c4 1 7 1 11 3l-2 2 1 2c-1 0-1-1-2-1l-2 1h0l-6-2c-3-2-7 0-10-1h0c-2-1-4-1-6 0h-3c-4-1-6-1-9-1v1c-1 0-4 0-5 1l1 1-1 1c-2-1-3-1-4-1l-7-1c2-1 3-1 5-2s5-1 8-1c6 1 12 0 19-1z" class="V"></path><path d="M522 478c7 0 15 1 21 2 2 1 3 1 5 1h1l6 2h0l5 3v15c0 2 0 5-1 8v1c-3-1-7-3-10-3-10-3-20-4-30-4h-1l-4 1h-3-1c-2-1-5-1-8 0 1-2 2-3 3-4v-2c1 0 1 0 2-1h-1c-1-1-2-1-2-2 0-2 0-2 1-4h0-1l-2-2-2 1v-2l-2-1v-1-2-3c1 0 2-1 4-1 1-1 0-1 2 0h1 1l2-1h3 1 6l4-1z" class="H"></path><path d="M523 490c2 0 2 0 3 2v3l-1 2h-1l-1-1v-6z" class="k"></path><path d="M523 490c0-2 0-3-1-4l1-1c1-1 1 0 2 0 1 1 0 1 0 2 1 2 1 3 1 5 0 1 1 2 0 3v-3c-1-2-1-2-3-2z" class="F"></path><path d="M512 502s-1-1-1-2 2-1 3-2l1 1h0v2 1h4l19 1c2 0 5 0 8 1l2 1c1 0 2 0 3 1h2c1 1 2 1 3 1v1c2 0 2 0 3 1v1c-3-1-7-3-10-3-10-3-20-4-30-4h3c-3-1-7-1-10-1z" class="Q"></path><path d="M543 490h2l2 1h1c1 0 2 1 3 2v1 2c0 2 0 5-1 7h-2c-2-1-4 0-6-1-1 0-1-1-2-2 1-1 1-2 1-3-1-2-1-3-1-5 1-1 2-1 3-2z" class="E"></path><path d="M512 479h6c-2 0-3 0-4 1v1 1h2c2 2 1 6 1 9l1-1h1l-2 2v1h2c1 2 0 3 1 6l-1 1-2-1-1 1v1h3v1h-4v-1-2h0l1-1v-1c-1 0-1 0-2 1-3-3 0-6-1-9h-1c0 1-1 2-1 3l-1-1v-3c0-1 1-2 0-2l-1-1c-1-2-1-3-1-6h3 1z" class="C"></path><path d="M508 479h3l-1 1v1c1 1 1 1 1 2v1l1 1c1-1 2-1 3 0l-2 2 1 1c0 1 1 1 2 1l1 9h-1v-1c-1 0-1 0-2 1-3-3 0-6-1-9h-1c0 1-1 2-1 3l-1-1v-3c0-1 1-2 0-2l-1-1c-1-2-1-3-1-6z" class="B"></path><path d="M502 480c1-1 0-1 2 0h1 1l2-1c0 3 0 4 1 6l1 1c1 0 0 1 0 2v3l1 1c0-1 1-2 1-3h1c1 3-2 6 1 9 1-1 1-1 2-1v1l-1 1-1-1c-1 1-3 1-3 2s1 2 1 2c3 0 7 0 10 1h-3-1l-4 1h-3-1c-2-1-5-1-8 0 1-2 2-3 3-4v-2c1 0 1 0 2-1h-1c-1-1-2-1-2-2 0-2 0-2 1-4h0-1l-2-2-2 1v-2l-2-1v-1-2-3c1 0 2-1 4-1z" class="G"></path><path d="M509 485l1 1c1 0 0 1 0 2v3l1 1c0-1 1-2 1-3h1c1 3-2 6 1 9 1-1 1-1 2-1v1l-1 1-1-1c-1 1-3 1-3 2s1 2 1 2h-3 0l-2-2h0v-1l1 1h1v-1-5h0c0-1 0-1 1-2h-2v-1l1-1-1-1 1-1-1-1c0-1 1-1 1-2z" class="D"></path><path d="M502 480c1-1 0-1 2 0h1 1l2-1c0 3 0 4 1 6 0 1-1 1-1 2l-2-2 1 3c-1 1-2 1-3 2h4c-1 1-2 1-3 1h0-1l-2-2-2 1v-2l-2-1v-1-2-3c1 0 2-1 4-1z" class="R"></path><path d="M506 480l2-1c0 3 0 4 1 6 0 1-1 1-1 2l-2-2h-1c0-2 0-2-1-3l1-2h1z" class="K"></path><path d="M502 480c1-1 0-1 2 0l-2 1v4l-1 1h2c0 1 0 1-1 2v1l-2 1v-2l-2-1v-1-2-3c1 0 2-1 4-1z" class="N"></path><path d="M509 228l3-1c0 1 0 1 1 2h-1c-1 2-1 4-2 6h0l-1 3c0 1 0 0-1 2l-1 1c0 1-1 2-1 4h0 1c2 0 3 0 5 1h1c1 0 3 0 5-1-1 2-3 5-3 6l-1 1c-1 1-1 2-1 2v2l-1 2 1 1h-1v2 1c0 2 0 5 1 8 0 1 0 0-1 1l1 2h2l2 2c2 0 6 0 8 1h1-5c-3 0-6 0-8 1l-1 1c1 3 1 16 1 17-1 1-1 2-1 3h2 0l-3 1h0v2h-3-3l-3 1c-1 0-3 1-4 1h-1v-1c-3 0-5 1-8 2l-4 4v4 1h0c1 1 1 2 1 2 0 1-1 3-1 3 1 2 1 0 0 3l2-1v1l-2 2v1l2-1v1c0 1-2 1-2 2v4 1-1l1-1c1-1 2-2 4-2-1 1 0 1-1 2s-1 1-2 3c1 0 1 1 1 2 1 1 1 1 0 2-1 0-2 0-2 1-2 1-2 1-3 3 1 0 1-1 2-1 0-1 2-1 3-1v1h-1c-1 1-4 3-4 4v6h-1c-1-12 0-24 1-36 2-14 3-27 7-41 2-11 6-21 10-32 1-2 3-9 5-11 1 0 2-1 3-1h1z" class="T"></path><path d="M491 281l-1-2v-3l1-1v-2-1l2-7c1-3 2-4 4-7 0 3-1 6-2 9l-2 7-1 3c0 1 1 2 1 3l-2 1z" class="Y"></path><path d="M490 289h1c0 2-1 1-1 2 0 0 0 1 1 1 0 1-1 4-1 5v1c0 2-1 4-2 6l-1 1h-1c0-5 1-10 2-15l2-1z" class="W"></path><path d="M493 280c1 0 3-1 5-1l-1 1-2 1c1 1 1 2 2 2 0 1-1 2-1 3s0 2 1 4c0 1-1 1-2 2l1 1h0l-1 1 1 1c0 2-1 3-2 4l1 1h0c-1 1-2 2-4 2h-1c1-2 1-5 1-7 1-2 1-4 1-6v-1l1-1v-3l1-1-2-1-1 2c-1 0-1 1-2 2v-1c0-2 1-3 2-4l2-1z" class="f"></path><path d="M497 280h0c1 0 2 0 3-1v1l2 2h-1l-1 1 1 1c0 1 1 2 1 3l-1 1s0 1 1 2h-1c0 2 1 5 0 6 0 2-1 1-1 3 1 0 2 1 3 1s2 0 2 1l-3 1c-1 0-3 1-4 1h-1v-1c-3 0-5 1-8 2 1-1 1-1 1-2h1c2 0 3-1 4-2h0l-1-1c1-1 2-2 2-4l-1-1 1-1h0l-1-1c1-1 2-1 2-2-1-2-1-3-1-4s1-2 1-3c-1 0-1-1-2-2l2-1z" class="r"></path><path d="M509 228l3-1c0 1 0 1 1 2h-1c-1 2-1 4-2 6h0l-1 3c0 1 0 0-1 2l-1 1c0 1-1 2-1 4h0 1c2 0 3 0 5 1l-4 1-1 1-1 1-1 1c0 1 0 1 1 2l-2 1v1l1 1-2 2h1 1v1l-2 1 1 1h1c-1 1-1 2-2 2h-1c0 1 0 1 1 2h-1v6c-1 1-2 1-2 2v3h2c0 1-1 1-1 2h0 4c-1 1-1 1-2 1l-3 1c-1 1-2 1-3 1h0l1-1c-2 0-4 1-5 1 0-1-1-2-1-3l1-3 2-7c1-3 2-6 2-9l3-9c0-2 1-4 2-6 0-2 1-5 2-7s1-3 2-4c1-2 2-3 2-4h1z" class="U"></path><path d="M508 228h1v3c-1 0 0 1 0 2l-2 1 1 2h-1-1l1-1h0c-1-1-1-2-1-3 1-2 2-3 2-4z" class="f"></path><path d="M493 274l1 2h0c2-1 2-2 3-4v1c0 2 0 3-1 4v1h7l-3 1c-1 1-2 1-3 1h0l1-1c-2 0-4 1-5 1 0-1-1-2-1-3l1-3z" class="d"></path><path d="M495 267h4c0 2-1 3-2 5h0c-1 2-1 3-3 4h0l-1-2 2-7zm11-35c0 1 0 2 1 3h0l-1 1h1c0 1 0 2-1 3-1 2-2 5-3 7 1 0 1 0 1 1s0 1-1 2c-1 0-2 0-2 1 0-2 1-4 1-6v-1c0-2 1-5 2-7s1-3 2-4z" class="m"></path><path d="M497 258l3-9c0-2 1-4 2-6v1c0 2-1 4-1 6 0-1 1-1 2-1v3l-2 1 1 1 1-1v1l-1 1c-1 0-1 0-1 1v2l-1 1s1 0 1 1c0 0-2 1-2 2 1 1 0 2-1 3v1l1 1h-4c1-3 2-6 2-9z" class="X"></path><path d="M513 246c1 0 3 0 5-1-1 2-3 5-3 6l-1 1c-1 1-1 2-1 2v2l-1 2 1 1h-1v2 1c0 2 0 5 1 8 0 1 0 0-1 1l1 2h2l2 2c2 0 6 0 8 1h1-5c-3 0-6 0-8 1l-1 1c1 3 1 16 1 17-1 1-1 2-1 3h2 0l-3 1h0v2h-3-3c0-1-1-1-2-1s-2-1-3-1c0-2 1-1 1-3 1-1 0-4 0-6h1c-1-1-1-2-1-2l1-1c0-1-1-2-1-3l-1-1 1-1h1l-2-2v-1l3-1c1 0 1 0 2-1h-4 0c0-1 1-1 1-2h-2v-3c0-1 1-1 2-2v-6h1c-1-1-1-1-1-2h1c1 0 1-1 2-2h-1l-1-1 2-1v-1h-1-1l2-2-1-1v-1l2-1c-1-1-1-1-1-2l1-1 1-1 1-1 4-1h1z" class="R"></path><path d="M509 272c1 0 2 1 3 2v1c-1 0-2 1-4 0v-2l1-1z" class="P"></path><path d="M506 270c1-1 1-2 2-3v4l-2 2c-1-1-1-2-1-3h1z" class="G"></path><path d="M513 270c0 1 0 0-1 1l1 2h2l2 2h-5v-1c-1-1-2-2-3-2l2-1c1-1 1 0 2-1z" class="B"></path><path d="M506 270c-2-2-2-3-2-5 1-2 1-2 3-3l1 5c-1 1-1 2-2 3z" class="S"></path><path d="M505 289c1-1 2-2 3-4l-1-1h-2v-1l1-1-1-1v-3h1l1 1c0 1 1 2 1 2l1 6c-1 1-2 1-3 3h0l-1-1z" class="G"></path><path d="M513 246c1 0 3 0 5-1-1 2-3 5-3 6l-1 1c-1 1-1 2-1 2v2l-1 2 1 1h-1v2 1c0 2 0 5 1 8-1 1-1 0-2 1l-2 1-1 1v-2-4l-1-5 1-1c0-1 1-1 1-2l-1-1 1-1v-1h-1c1-2 2-1 3-2l-1-1h-3l1-2-1-1 1-1h1v-1h-1v-1l4-1h1z" class="K"></path><path d="M512 262h-1l-1 1-1-1v-1c1-2 1-1 3-2v2 1z" class="D"></path><path d="M513 246c1 0 3 0 5-1-1 2-3 5-3 6l-1 1c-1 0-2 0-3-1 1-1 1-2 1-3h1v-2z" class="Q"></path><path d="M508 277h5l-1 1c1 3 1 16 1 17-1 1-1 2-1 3h2 0l-3 1h0v2h-3-3c0-1-1-1-2-1l2-1-1-1c1-2 1-1 1-3 0-1 1-1 2-3l-1-1-1 1v-3l1 1h0c1-2 2-2 3-3l-1-6s-1-1-1-2l-1-1 2-1z" class="P"></path><path d="M508 277h5l-1 1-2 2h0-1l-1-3z" class="B"></path><path d="M506 290h1c1 0 1 0 2-1 1 2 1 4 0 6h0-2v2c1 1 1 2 2 1 2 0 1 0 2 1h0v2h-3-3c0-1-1-1-2-1l2-1-1-1c1-2 1-1 1-3 0-1 1-1 2-3l-1-1-1 1v-3l1 1z" class="O"></path><path d="M505 299h6v2h-3-3c0-1-1-1-2-1l2-1z" class="N"></path><path d="M523 546c12 0 25 1 36 6v31 7-1c-9-4-17-5-27-6h-12-6 1v-1h-4c-1-1-1 0-3 0v-1l1-1c-2-1-2-1-3 0v-1l3-1c0-1-1-1-1-2h-1c0-2 0-2 1-3v-2l-1-1 1-1h2v-3l1-1c0-2-2-1-3-2v-1c1 0 2 0 3-1-1-1-2 0-3-1 0-1 1-1 2-1 0-1-1-2-1-3 1-2 0-4-1-6 0-1 1-2 1-3h4l1-1 3 1 6-1z" class="H"></path><path d="M546 563c2 0 4 0 6 1 1 0 4 1 5 3h-1-2c-2-1-4-2-7-3h0l-1-1z" class="F"></path><path d="M524 568c1 0 1 0 2 2 0 1 0 4-1 5h-1c-1 0-1 0-1-1 0-2 0-5 1-6z" class="Y"></path><path d="M545 573h0l1 1v6h-1c1-1 2-2 3-2l1 1c0 1 0 2-1 3h-2c-1-1-2-1-3-1-1-1-2-2-2-3 0-2 0-2 1-3 1 1 0 2 2 3 0-2 0-3 1-5z" class="E"></path><path d="M517 547l6-1c-2 1-4 1-5 2 2 2 3 2 6 2h1 1l-2 2c2 1 2 2 2 4-2 1-2 0-4 0-1 0-3 2-3 3l3 2h-1c-1 0 0 0-1 1h-1c2 2 2 2 2 5v1l1 1v1l-2-1-1 1-1-1v-2l-1-1c0-1 0-1 1-1v-2-2h1-1-1l-2-1c1-2 1-5 0-8v-1l-1-1-2 1v-1-1c0-1 0-1 1-2l1-1 3 1z" class="C"></path><path d="M514 546l3 1h0c0 2 0 2 1 3v2h-1l-1-1h-1 0l-1-1-2 1v-1-1c0-1 0-1 1-2l1-1z" class="I"></path><path d="M509 547h4c-1 1-1 1-1 2v1 1l2-1 1 1v1c1 3 1 6 0 8l2 1h1 1-1v2c-1-1-1-1-2-1v4c0 1 0 1 1 2s0 3 0 5c1-1 1-1 1-2 1 0 2 1 3 2v1c-2 1-2 0-4 0v3c0 2 0 3-1 4l1 1c7-1 15 0 22 1 7 0 13 1 20 4v-4 7-1c-9-4-17-5-27-6h-12-6 1v-1h-4c-1-1-1 0-3 0v-1l1-1c-2-1-2-1-3 0v-1l3-1c0-1-1-1-1-2h-1c0-2 0-2 1-3v-2l-1-1 1-1h2v-3l1-1c0-2-2-1-3-2v-1c1 0 2 0 3-1-1-1-2 0-3-1 0-1 1-1 2-1 0-1-1-2-1-3 1-2 0-4-1-6 0-1 1-2 1-3z" class="D"></path><path d="M509 578c0-1-1-1-1-2h-1c0-2 0-2 1-3v-2l-1-1 1-1h2c0 1-1 1-1 2 1 0 2 1 3 0h0l1 1c-1 2-2 1-3 2-1 0-1 1-2 1l2 2-1 1zm-1-28l2-1 1 1h0c0 2 0 2 1 4-1 1-1 3-1 4l-1 1c0-1-1-2-1-3 1-2 0-4-1-6z" class="K"></path><g class="Q"><path d="M516 562l-1-1-1 1h-1v-4h0l-1-3 2-2c0-1 0-1 1-2v1c1 3 1 6 0 8l2 1h1 1-1v2c-1-1-1-1-2-1z"></path><path d="M517 582c-1 0-3 0-4-1v-3l-1-4h0c2 0 2 0 4-1v-4c-1-2-2-1-3-2v-2l1-1h1c0-1 0-1 1-2v4c0 1 0 1 1 2s0 3 0 5c1-1 1-1 1-2 1 0 2 1 3 2v1c-2 1-2 0-4 0v3c0 2 0 3-1 4l1 1z"></path></g><path d="M473 509l1 1v1l1-2 1 1h1v-1h1v4c2 1 2 0 3 2 0 3 0 6 1 9 0 2-1 3-1 6v2c0 1 0 1 1 2 1 5 1 11 0 16-1 1-1 1-3 1h1l1 1v1c0 2 1 5 1 7s-1 3 0 5v1c-1 3 0 7 0 10 0 5-1 10-1 15 0 6 1 13 0 19 0 1-1 2-1 3s1 1 1 2c-1 2 0 3 0 5 0 3-1 6-1 9v5c2 3 1 8 0 12v5 1c-1 2 0 3-1 5h1c0 1 0 4 1 5 0 1 0 2-1 2 0 2 1 4 0 5v18c0 2 0 3-1 4s-3 1-4 2c-1-1-2-1-4 0v-4c-1 0-2 0-2-1s0-2 1-3v-1c-1-1-1-2-2-3 1-1 1-1 1-2s-1-3-1-4h0v-1c0-2 0-5 1-7v-3c0-2-1-2 0-3v-4-6-5c0-1 0-3-1-4v-3-5c1-2 0-4 1-6v-1c-1-2 0-2 0-4-1-2 0-3 0-5 0-1-1-3 0-4 0 0-1-3-1-4v-1l2-1h0-2v-2-1l1-1c0-2 0-1-1-3l1-4-1-1c0-1 0-1 1-2 0-1-1-1-1-2l-1-3h1c0-3-1-6 0-8 0-1 1-2 2-2v-1c-1-2-1-3-1-5 0-1-1-3 0-3v-3c-1-3-1-3 0-5l1-1c-2-2 0-5 0-7 0-1-1-1-1-2v-1h2l-2-1 1-2c-1-1-2-6-1-7v-1-1c-1-2-1-5-1-7v-1l2-2h0l-1-1v-2c0-1-1-3-1-4 1-1 1-1 1-2v-7h2c1-1 1-2 2-4z" class="a"></path><path d="M472 687l5 1h0c-1 1-1 2-3 2-1-1-1-1-2-3z" class="h"></path><path d="M473 666h2 0 0c1 1 3 2 4 3l-1 1h0v4 1c-1 1-1 1-1 3h1c0 3 0 6-1 9v1l-5-1v-1c0-1 1-3 0-4l-1-2v-2-1c0-1 0-1-1-2h0c1-1 1-1 1-2s-1-2-1-3 0-1 1-2l2-2z" class="D"></path><path d="M471 668c1 2 1 3 2 4 0 1-2 2-1 3l1 1v2c-1 2 0 4 0 6l1 2h1l1-1-2-2h1 0c1 0 1 0 2-1 0-1 0-1-1-2h-1v-1h2c1-1 1-1 0-1h1c0 3 0 6-1 9v1l-5-1v-1c0-1 1-3 0-4l-1-2v-2-1c0-1 0-1-1-2h0c1-1 1-1 1-2s-1-2-1-3 0-1 1-2z" class="j"></path><path d="M473 632c2 1 2 1 4 1h0v3c1 2 1 7 0 9 0 2 1 4 1 6 0 0-1 1-1 2 0 0 0 1 1 1 0 3 0 5-1 8 0 1 0 2 1 3-1 1-2 1-3 1h0 0-2 0v-3h0c0-1 0-2-1-2v-1c1-1 1-1 1-2v-4-7l-1-1c0-1 0-2 1-3v-1-2h0l-1-1c0-2 0-3 1-4v-3z" class="Q"></path><path d="M474 659c1-1 1-1 2 0l1 1-1 1h-2v-2z" class="C"></path><path d="M474 635c1 0 1 1 2 1 0 2 0 3-1 5h-2c0-2 0-4 1-6z" class="b"></path><path d="M473 647v-1c1-1 1-2 1-2l1-1 1 1v1c-1 1 0 2 0 3v8c-1 1-1 1-3 1v-3-7z" class="I"></path><path d="M471 586c1-2 1-3 1-5 1 3 1 5 1 8l1 1 2 1h0l-1 1c-1 0-1 0-2 1v8 7c0 1 0 0 1 1h1v1c-1 0-1 1-2 1 0 1 1 1 0 2v3 1 6 9h0v3c-1 1-1 2-1 4l1 1h0v2 1c-1 1-1 2-1 3l1 1v7 4c0 1 0 1-1 2v1c1 0 1 1 1 2h0v3c-1-1-2-2-2-4v-2-1l1-1-1-1s-1-2 0-2v-1c1-1 0-2 1-2l-1-1v-4l-1-1 1-1c0-3-1-6 0-8 0-1 0-1-1-2l2-2h0l-2-2c0-1 1-1 1-1 0-1 1-3 1-4h-2c0-2 1-5 1-8v-6-5c0-1 0 0-1-1 0-1 1-3 1-3 0-1-1-2-1-2 0-1 1-2 1-4v-4l1-2c-1-1 0-1-1-1h-1 0l1-2-1-2h1z" class="P"></path><path d="M472 557c0-1 0-2 1-3h0v-2h0 4 2v2 1 5s-1 2-1 3 1 0 0 2l-1 1h0v3c1 1 2 2 2 4l-1 1v3 2c1 1 1 2 1 3s-1 1-1 2c0 2 1 5 0 6h-1v-5h-1c-1 1-2 3-3 4 0-3 0-5-1-8h1v-2h0l2-1h0l-1-1c-1-2-1-2-1-4v-2l-1-1c1-1 1-2 2-3h0c-1-1-1-1-1-2v-2-2l-1-1v-3z" class="C"></path><path d="M473 571c1-1 2-1 3-1 1 1 1 2 1 3l-1 1-3-1v-2z" class="F"></path><path d="M472 557c0-1 0-2 1-3h0v-2h0 4v6l-5-1z" class="E"></path><path d="M473 579c1 1 2 1 3 2-1 1-2 1-2 1l-1 1c2 1 3 1 5 1 0 2 1 5 0 6h-1v-5h-1c-1 1-2 3-3 4 0-3 0-5-1-8h1v-2z" class="V"></path><path d="M473 589c1-1 2-3 3-4h1v5h1v2c1 1 1 3 1 5h0c-1 2 0 5 0 7-1 1-1 2-1 3l1 1h-1l-1 1 1 1c0 1-1 1-1 2s1 3 2 4v1c-1 1 0 1 0 3l-1 2c1 2 0 3 0 5-1 1 0 1-1 1-1 2 0 3 0 5-2 0-2 0-4-1h0v-9-6-1-3c1-1 0-1 0-2 1 0 1-1 2-1v-1h-1c-1-1-1 0-1-1v-7-8c1-1 1-1 2-1l1-1h0l-2-1-1-1z" class="Q"></path><path d="M473 632l2-1c0-2 0-2-2-3v-3c1-1 1-1 3-2v1c0 1 0 2 1 4h0c-1 2 0 3 0 5-2 0-2 0-4-1h0z" class="b"></path><path d="M473 589c1-1 2-3 3-4h1v5h1v2c0 1 0 3-1 5v2 1c0 1 0 1 1 2v2h-1c-1 0-1 1-2 1s-1 0-1-1l1-2-2-1v-8c1-1 1-1 2-1l1-1h0l-2-1-1-1z" class="C"></path><path d="M473 509l1 1v1l1-2 1 1c0 1 0 2 1 3v1l1 1c0 2 1 5 0 7 1 1 1 2 1 3-1 2-1 3-2 4l2 2c0 2 0 5-1 7 0 1-1 2-1 3s1 1 1 2 0 1-1 2c0 1 0 4 1 5-1 0-1 1-1 2h-4 0v2h0c-1 1-1 2-1 3v3l1 1v2 2c0 1 0 1 1 2h0c-1 1-1 2-2 3l1 1v2c0 2 0 2 1 4l1 1h0l-2 1h0v2h-1c0 2 0 3-1 5 0-1-1-1-1-2v-5-1c-1-2-1-3-1-5 0-1-1-3 0-3v-3c-1-3-1-3 0-5l1-1c-2-2 0-5 0-7 0-1-1-1-1-2v-1h2l-2-1 1-2c-1-1-2-6-1-7v-1-1c-1-2-1-5-1-7v-1l2-2h0l-1-1v-2c0-1-1-3-1-4 1-1 1-1 1-2v-7h2c1-1 1-2 2-4z" class="B"></path><path d="M469 562v3c1 2 3 0 1 4v1c0 1 1 1 1 2 0 2 0 2 1 4 0 1 1 2 1 3h0v2h-1c0 2 0 3-1 5 0-1-1-1-1-2v-5-1c-1-2-1-3-1-5 0-1-1-3 0-3v-3c-1-3-1-3 0-5z" class="K"></path><path d="M477 513v1l1 1c0 2 1 5 0 7 1 1 1 2 1 3-1 2-1 3-2 4l2 2c0 2 0 5-1 7 0 1-1 2-1 3s1 1 1 2 0 1-1 2c0 1 0 4 1 5h-5v-3c0-2-1-2-1-3v-3l1-1c0-2-1-2-2-4v-5c1-1 2-1 3-2h1c0-1 0-2-1-3s-2-2-2-3l1-1c-1-2-1-5-1-7 2-2 2-2 5-2z" class="C"></path><defs><linearGradient id="Y" x1="570.377" y1="602.505" x2="555.585" y2="601.708" xlink:href="#B"><stop offset="0" stop-color="#060a01"></stop><stop offset="1" stop-color="#3d383c"></stop></linearGradient></defs><path fill="url(#Y)" d="M562 512s0-1 1-1c1-2 3-2 5-2h8c3 1 5 1 7 3l1 3-1-2h-1c-2 0-2 0-2 2h-1l-3-3v3h-4c-2 2-1 4-1 6v1c-1 1-1 4-1 5l1 1c0 1 0 1-1 2l-1 1v1c1 3 2 6 0 9 0 2 1 5 0 7l-1 1c1 1 1 1 2 1v1l-2 1v3l-2 2h0l2 1c0 2-1 3-3 4 1 1 1 2 1 3-2 2-1 5-1 6v1 1l-1 1c1 3 1 5 1 8v5 11 5c-1 3 0 6 0 8-1 2 0 2 0 3v7 11 1 1c0 2-1 3-1 5-1 1 0 2-1 3v5c0 4 2 7 1 11-1 1 0 3 0 4 0 4-1 8 0 12h0l-2 1v16c3 1 5 1 7 1v2h0l-1 1c0 1 1 1 1 2h0-5v1l-4 1c-1 1-2 0-2 0h-2-1c0 2 1 4 0 5v2h-3l-2 2c-1 1-1 2-1 4h-1-1c-1-1-2-1-3-1l-1-1h-1 0c-1 0-2 0-3-1-1-2-1-2 0-4l-2 1c0 1 1 3 1 3-1 3-1 6-2 8l-1-2c0-1 0-2 1-3v-3c-1-3 1-5-1-8v1-1l-1-1h-1v-1-2-6c0-2 0-2-1-3l-9-1c-1 0-1-1-2-1v-1c-2-2-1-3-1-5l2-2c-1 0-2-1-2-1l-1-1h1s0-1 1-1l1-1h1v-1-1c-1 0-1 0-2-1 1-1 0-1 2-2h4 2l1-2h0l-3-22c0-3-1-5-1-7l-1-12h1l1 5c0 1 1 4 1 5 1 0 2 1 2 1 0 1-1 1-1 2l2 2-1 1 1 2h0c0 1 0 2 1 3h0l1 2v3h1c1 2 0 4 1 6s0 5 2 7v3h1 0c0-1 0-1 1-2h0l-1-2 1-1c1 2 1 2 4 3h4c2 1 3 2 4 2l1 1c2 2 3 4 4 7l1-1 1 1-1 1h-1v5c1-1 1-1 1-2 1 0 1 0 1 1v-4c2-2-1-9 1-11v-76-7-31c1-4 1-8 1-13v-10h-1l1-2v-1-10c0-2 1-3 1-5h0l1 1h0z"></path><path d="M531 673c0-2 0-3 1-4 1 1 1 1 1 2v3h1c0 1 0 2 1 3 0 1 0 3-1 4v1h0c1 1 1 1 1 2l-3-3c0-1 0-7-1-8z" class="l"></path><path d="M532 681l3 3c0 1 0 2 1 3h1c2 1 3 2 5 3 2 0 4 0 6 2h-1l-10-1v1h-2l1-1c2-1 3-1 5-1l-3-1-1-1c-1 0-2 0-3 1v1l-1 1c0-2 0-2-1-3l1-2-1-5z" class="X"></path><path d="M533 691l1-1v-1c1-1 2-1 3-1l1 1 3 1c-2 0-3 0-5 1l-1 1h2l4 2-1 1h0l-1 1c-1 0-2 0-3 1s-1 3-1 4v1-1l-1-1h-1v-1-2-6z" class="f"></path><path d="M562 607v-31-11c1 3 1 5 3 7v1l-1 1c-1 1-1 3-1 5 0 1 1 2 1 3v7 4c0 5-1 9-2 14z" class="Y"></path><path d="M558 681v-4c2-2-1-9 1-11v16c0 3 1 6 0 9-1 0-1 0-2-1h-3c-2-2-3-2-5-2s-4-1-5-2v-3c2 0 3 0 4 2l-1 1h1 1 1c2 2 6 2 9 2-1-1-3-3-4-5 2 0 2 0 3-2z" class="Q"></path><path d="M537 692v-1l10 1c4 1 10 0 13 3l9-1-1 1c0 1 1 1 1 2h0l-9-1c-5 1-14-1-19-2l-4-2z" class="B"></path><path d="M527 645c0-3-1-5-1-7l-1-12h1l1 5v4c1 2 0 4 1 6 1 1 1 2 2 4l-1 2c1 1 2 1 2 2v2c0 1 1 1 0 2-1 3 2 6 1 8v3s1 0 1 1h0c0 2 1 8 1 9h-1v-3c0-1 0-1-1-2-1 1-1 2-1 4l-1-6-3-22z" class="i"></path><path d="M563 528v-1 1c1 0 2 1 3 1l1 1c-2 0-2 0-3 2h0c1 2 1 5 2 7-2 2-1 4-1 6s-1 3 0 5l1 1h0l-1 1h0v3c1 2 0 4 0 7 1 1 1 2 1 3-2 2-1 5-1 6v1c-2-2-2-4-3-7 0-4 0-33 1-35h0v-2z" class="T"></path><path d="M541 694c5 1 14 3 19 2l9 1h-5v1l-4 1c-1 1-2 0-2 0h-2-1-4l-10-2c-2 1-2 2-2 4v2l1 1 1 1h-2l-2 1c0 1 1 3 1 3-1 3-1 6-2 8l-1-2c0-1 0-2 1-3v-3c-1-3 1-5-1-8 0-1 0-3 1-4s2-1 3-1l1-1h0l1-1z" class="g"></path><path d="M532 647l1 2v3h1c1 2 0 4 1 6s0 5 2 7v3h1l1 2v1h0c1 2 1 4 2 6h-1l-1 1c1 0 1 1 2 1h3v1h0-2c-1 1-1 2-1 2 1 1 2 1 3 1v3c1 1 3 2 5 2s3 0 5 2h3c1 1 1 1 2 1l-1 1c-4 0-7-2-11-2 0-1-1-1-2-2-2 0-4 0-6-2-1-1-2-3-2-5h0l-1-1c1-3 0-11-1-14-1-2-1-3-1-4l-2-15z" class="D"></path><path d="M562 607c1-5 2-9 2-14v-4-7c0-1-1-2-1-3 0-2 0-4 1-5 1 3 1 5 1 8v5 11 5c-1 3 0 6 0 8-1 2 0 2 0 3v7 11 1 1c0 2-1 3-1 5-1 1 0 2-1 3v5c0 4 2 7 1 11-1 1 0 3 0 4 0 4-1 8 0 12h0l-2 1v-68z" class="r"></path><path d="M540 704l-1-1v-2c0-2 0-3 2-4l10 2h4c0 2 1 4 0 5v2h-3l-2 2c-1 1-1 2-1 4h-1-1c-1-1-2-1-3-1l-1-1h-1 0c-1 0-2 0-3-1-1-2-1-2 0-4h2l-1-1z" class="J"></path><path d="M540 704c2-1 3-1 4 0 2 0 4-1 6 0h5v2h-3l-2 2c-1 1-1 2-1 4h-1-1c-1-1-2-1-3-1l-1-1h-1 0c-1 0-2 0-3-1-1-2-1-2 0-4h2l-1-1z" class="C"></path><path d="M543 710c1 0 2-2 3-2h2v-2h0 4l-2 2c-1 1-1 2-1 4h-1-1c-1-1-2-1-3-1l-1-1z" class="H"></path><path d="M530 667h0l1 6c1 1 1 7 1 8l1 5-1 2-9-1c-1 0-1-1-2-1v-1c-2-2-1-3-1-5l2-2c-1 0-2-1-2-1l-1-1h1s0-1 1-1l1-1h1v-1-1c-1 0-1 0-2-1 1-1 0-1 2-2h4 2l1-2z" class="C"></path><path d="M521 686h12l-1 2-9-1c-1 0-1-1-2-1z" class="M"></path><path d="M523 672c-1 0-1 0-2-1 1-1 0-1 2-2h4l2 1v2 1c-2 0-4-1-5-2l-1 1z" class="H"></path><path d="M539 663c1 2 1 2 4 3h4c2 1 3 2 4 2l1 1c2 2 3 4 4 7l1-1 1 1-1 1h-1v5c1-1 1-1 1-2 1 0 1 0 1 1-1 2-1 2-3 2 1 2 3 4 4 5-3 0-7 0-9-2h-1-1-1l1-1c-1-2-2-2-4-2-1 0-2 0-3-1 0 0 0-1 1-2h2 0v-1h-3c-1 0-1-1-2-1l1-1h1c-1-2-1-4-2-6h0v-1l-1-2h0c0-1 0-1 1-2h0l-1-2 1-1z" class="F"></path><path d="M546 672l3 1v4l-2 1h-1c-1-1-1-2-1-3s1-2 1-3z" class="L"></path><g class="C"><path d="M547 666c2 1 3 2 4 2-1 1-1 2-2 3v2l-3-1c1-1 1 0 1-2h-2-1v-1l3-3z"></path><path d="M551 668l1 1c2 2 3 4 4 7l1-1 1 1-1 1h-1v5c1-1 1-1 1-2 1 0 1 0 1 1-1 2-1 2-3 2l-1-1-6-1c-1-1-1-2-2-3h1l2-1v-4-2c1-1 1-2 2-3z"></path></g><path d="M551 679v-2c1-1 2-1 3-2l1 2v3h-1-2l-1-1z" class="B"></path><path d="M551 668l1 1c2 2 3 4 4 7l-1 1-1-2c-1 1-2 1-3 2v2c0-1 0-1-1-1l-1 1-2-1 2-1v-4-2c1-1 1-2 2-3z" class="V"></path><path d="M562 512s0-1 1-1c1-2 3-2 5-2h8c3 1 5 1 7 3l1 3-1-2h-1c-2 0-2 0-2 2h-1l-3-3v3h-4c-2 2-1 4-1 6v1c-1 1-1 4-1 5l1 1c0 1 0 1-1 2l-1 1v1c1 3 2 6 0 9 0 2 1 5 0 7l-1 1c1 1 1 1 2 1v1l-2 1v3l-2 2h0l2 1c0 2-1 3-3 4 0-3 1-5 0-7v-3h0l1-1h0l-1-1c-1-2 0-3 0-5s-1-4 1-6c-1-2-1-5-2-7h0c1-2 1-2 3-2l-1-1c-1 0-2-1-3-1v-1 1l-1-1v-6c0-3 1-6 0-9z" class="L"></path><path d="M562 512s0-1 1-1c1-2 3-2 5-2 0 1 0 2-1 2 0 1-1 1-2 2s0 4-1 5v2l-1 1 1 1c0 1 0 2-1 3l1 1-1 1v1l-1-1v-6c0-3 1-6 0-9z" class="i"></path><path d="M576 509c3 1 5 1 7 3l1 3-1-2h-1c-2 0-2 0-2 2h-1l-3-3v3h-4c-2 2-1 4-1 6v1c-1 1-1 4-1 5l1 1c0 1 0 1-1 2l-1 1v1c1 3 2 6 0 9-1-3-1-5-1-8-1-2 1-2 1-4l-2-1c0-2 1-4 1-6h0c-1-1-1-2-1-3h1v-3c1-1 0-3 1-3 0-1 1-1 2-3 1-1 3 0 5-1z" class="K"></path><path d="M570 530v-1c-1-2-1-4-1-7 1-3 0-8 2-10 2 1 3 0 5 0v3h-4c-2 2-1 4-1 6v1c-1 1-1 4-1 5l1 1c0 1 0 1-1 2z" class="C"></path><path d="M498 486v1l2 1v2l2-1 2 2h1 0c-1 2-1 2-1 4 0 1 1 1 2 2h1c-1 1-1 1-2 1v2c-1 1-2 2-3 4 3-1 6-1 8 0h1 3l4-1h1c10 0 20 1 30 4 3 0 7 2 10 3 1 2 1 4 1 6v10 1l-1 2h1v10c0 5 0 9-1 13-11-5-24-6-36-6l-6 1-3-1-1 1h-4-1c-2-2-5 0-8-1v-1c0-1 0-1-1-2v-3l-2-1v-3-1l-1-1 2-2-1-1c-1-1 1-1 1-3l-2-1v-1l2-2h0c-1 0-1-1-2-1 1-2 1-6 1-7s-1-2-2-2c2-1 2-1 3-2l-2-1 1-2c-2 0-2 0-3-1l1-1c1 0 2-1 2-2h-4c2-1 2-2 4-3-1-1-1-1 0-2v-1h-1 0l-1-1c1 0 2-1 2-2-1 0-1 0-2-1v-1l2-2-1-1c0-1 0-1 1-2v-1c-1 0-1 0-2-1l3-1z" class="U"></path><path d="M503 508l-3-1c0-2 3-2 4-3h1v1h0c1 1 0 1 1 1 0 1-1 1-3 2h0z" class="L"></path><path d="M505 505c2-1 3-1 5-1h1l-3 1c0 2 1 4 2 5l1 2h0l-1 1-2 1v-3-1c-1 1-1 1-1 2h-1c0-2-2-2-3-4 2-1 3-1 3-2-1 0 0 0-1-1h0z" class="K"></path><path d="M511 504h3l-1 2c1 1 3 0 3 2l-1 1h0c1 1 1 1 0 2-1 0-2 0-3-1l-1 2h0l-1-2c-1-1-2-3-2-5l3-1z" class="B"></path><path d="M500 490l2-1 2 2h1 0c-1 2-1 2-1 4 0 1 1 1 2 2h1c-1 1-1 1-2 1v2c-1 1-2 2-3 4h-3v-1h2v-2c0-1-1-1 0-3h1v-1l-1-1c0-1 0-2-1-2 0-1 0-2 1-3h-1-1l1-1z" class="Z"></path><path d="M503 508h0c1 2 3 2 3 4h1c0-1 0-1 1-2v1 3h-3l1 1h2l-1 1h-1l-1 1h0c1 1 1 1 2 1v1h-1s-1 0-1 1l1 2h1v-1l1-1v1l-1 1h8v1h-5-3 0c-2 0-3-1-5 0h0l-2-2c-1-1 1-1 1-2v-1-4h0l-1-1 1-1v-1l-1-1h0c1-1 1-1 3-2z" class="a"></path><path d="M510 523h5v-1h-8l1-1v-1l-1 1v1h-1l-1-2c0-1 1-1 1-1h1v-1c-1 0-1 0-2-1h0l1-1h1c1 1 3 1 4 1h1 1l-1 3 1 1 9 1h0l-2 1h0l-2 2v1c3 0 2-1 4 1v3h-2l-1 1h-1-1l-1-1-1 1h-2-3l-1-1v-1c1-2 1-4 1-6z" class="K"></path><path d="M520 523h0l-2 2v1c3 0 2-1 4 1v3h-2l-1 1h-1-1l-1-1-1 1-1-3v1h-1c0-2 0-4 1-5 2-1 4-1 6-1z" class="I"></path><path d="M514 528l1-1c2 0 2 1 3 2 0 1 0 1 1 2h-1-1l-1-1-1 1-1-3z" class="B"></path><path d="M507 523h3c0 2 0 4-1 6v1l1 1h3 2l1-1 1 1h1l-2 2c0 2 0 3 1 4h2v1 1l-1 1c0 1 0 1 1 2l1-1 1 1-1 2h-1v2h-5l-1 1h-4-1c-2-2-5 0-8-1v-1c0-1 0-1-1-2v-3l-2-1v-3-1l-1-1 2-2-1-1c-1-1 1-1 1-3l-2-1v-1l2-2 3 1 3-1h3v-1z" class="Z"></path><path d="M507 523h3c0 2 0 4-1 6-1 1-2 1-3 1h-1l-1-1c-1-1 0-3 0-5h3v-1z" class="G"></path><path d="M504 524h3v2l-1 1v2l-1 1-1-1c-1-1 0-3 0-5z" class="R"></path><path d="M497 536v-1l-1-1 2-2-1-1c-1-1 1-1 1-3l-2-1v-1l2-2 3 1-1 2 2 1v1l-2 1h2c0 2-1 2-1 3 0 2 0 4-1 6 1 1 1 1 1 3 0 0-1 1-2 1v-3l-2-1v-3z" class="N"></path><path d="M509 530l1 1-1 1c0 2 1 2 0 3v2l1 1s0 1 1 2c0 1 0 1-1 2h-1v1h2v2h-7v-1h-1-1c1-2 3-1 4-2 0-1-1-1-1-2s-1-1-1-1c1-1 0-3 1-4v-1l-1-1 1-1h3c0-1 1-1 1-2z" class="G"></path><path d="M515 531l1-1 1 1h1l-2 2c0 2 0 3 1 4h2v1 1l-1 1c0 1 0 1 1 2l1-1 1 1-1 2h-1l-3 1h-5v-2h-2v-1h1c1-1 1-1 1-2-1-1-1-2-1-2l-1-1v-2c1-1 0-1 0-3l1-1h3 2z" class="I"></path><path d="M510 531h3 2c-1 1-2 1-2 2l-1 1c-1 1-1 1-1 2l1 1 1 1 1-1 2 1h0c-1 1-2 1-3 1l-1 1c2 1 3 0 3 2h0l-2-1-1 2c1 0 3 0 4 2h-5v-2h-2v-1h1c1-1 1-1 1-2-1-1-1-2-1-2l-1-1v-2c1-1 0-1 0-3l1-1z" class="D"></path><path d="M514 504l4-1h1c10 0 20 1 30 4 3 0 7 2 10 3 1 2 1 4 1 6v10 1l-1 2-10-3c-10-3-19-3-29-3h0l2-1h0l-9-1-1-1 1-3h-1-1c-1 0-3 0-4-1l1-1h-2l-1-1h3l2-1 1-1 1-2c1 1 2 1 3 1 1-1 1-1 0-2h0l1-1c0-2-2-1-3-2l1-2z" class="H"></path><path d="M514 504l4-1v3h1c1 1 1 1 1 2v1c1 1 1 1 2 1h0l-2 1c-2 0-3 0-5-2h0l1-1c0-2-2-1-3-2l1-2z" class="E"></path><path d="M515 509c2 2 3 2 5 2l2-1h0l3 1c1-1 1-1 2 0v3h-2-2l-3 1v1h1 2c2 1 4 1 6 1v1 1l2 1 1-1-1-1 1-1c1 1 1 1 1 2h1l1-2h1v3c0-1-1-1-1-2l-1 3h2c1 0 1 0 2 1 1-1 2-1 3 0h3v1c1 0 1-1 2-1 1 1 2 1 3 1l2 1h1l-1-2c-1-1-1-3-1-4h0c1 1 1 1 1 2 1 0 1 0 2 1h2v-1c1 1 0 1 1 2 0 1 2 2 2 3l2 1v1l-1 2-10-3c-10-3-19-3-29-3h0l2-1h0l-9-1-1-1 1-3h-1-1c-1 0-3 0-4-1l1-1h-2l-1-1h3l2-1 1-1 1-2c1 1 2 1 3 1 1-1 1-1 0-2z" class="F"></path><path d="M515 509c2 2 3 2 5 2l2-1h0l3 1-1 1h-3c-1 0-2 0-3 1 0 1-2 2-1 3 1 0 2 0 3 1h-2l-1 2-1-1v-1h-3-1-1c-1 0-3 0-4-1l1-1h-2l-1-1h3l2-1 1-1 1-2c1 1 2 1 3 1 1-1 1-1 0-2z" class="V"></path><path d="M511 512l1-2c1 1 2 1 3 1l1 1v1h-1l-3 3v1h0-1c-1 0-3 0-4-1l1-1h-2l-1-1h3l2-1 1-1z" class="D"></path><path d="M513 517h3v1l1 1c1 1 1 2 2 2h10c7 0 15 1 21 3l6 2 4 1-1 2-10-3c-10-3-19-3-29-3h0l2-1h0l-9-1-1-1 1-3z" class="B"></path><path d="M520 523c10 0 19 0 29 3l10 3h1v10c0 5 0 9-1 13-11-5-24-6-36-6l-6 1-3-1h5v-2h1l1-2-1-1-1 1c-1-1-1-1-1-2l1-1v-1-1h-2c-1-1-1-2-1-4l2-2h1l1-1h2v-3c-2-2-1-1-4-1v-1l2-2z" class="o"></path><path d="M531 536h2l1 1-1 1-3-1h0l1-1zm-12 3h2c1 1 2 1 3 2l1 1-2 1-2-1-1-1-1 1c-1-1-1-1-1-2l1-1z" class="F"></path><path d="M522 527l2-1c0 1-1 1-1 2s0 3-1 3c-1 1-1 2-2 3 1 1 1 1 3 2v1c2 0 2-1 4-2v1 2c-2 1-4 1-6 1h-2v-1-1h-2c-1-1-1-2-1-4l2-2h1l1-1h2v-3z" class="E"></path><path d="M549 526l10 3h1v10c0 5 0 9-1 13-11-5-24-6-36-6l-6 1-3-1h5v-2h1 11l3-1c1 1 3 1 5 1v-1h1c0-1 1-1 1-2-1-3 0-5-1-7v-1 1h1c1 2 0 4 0 6 1 1 1 3 2 4 1 0 1-1 2-1 1-2 0-3 0-5h1 0v2c0 2 1 4 2 4l1 1v1h1c0-1 0-1-1-2h0v-3c1 0 2 2 3 3s1 1 2 1c0-1 0-1 1-3 0-2-1-6-2-9l-2-2c0-2 0-2 1-3h3c-2-1-4-1-5-2h-1z" class="F"></path><path d="M553 533c-1-1-1-2-1-4h3l1 1c0 1-1 1-1 3-1 0 1 2 1 3l-1 1c0 2 0 3 2 5l-1 1v-1l-1 1v-1c0-2-1-6-2-9z" class="E"></path><path d="M560 529v10c0 5 0 9-1 13-11-5-24-6-36-6l-6 1-3-1h5v-2h1 11c10 1 19 2 28 6l1-21z" class="j"></path><path d="M415 721c2-1 4-2 6-1l3 3h1c1-1 2-1 4-1 1 1 2 1 2 2v4h1c1-1 4-2 5-2 2 0 4 0 5 2l1 1 1-1c2 0 4 1 6 3 1 1 1 2 1 4v1c1 0 3 0 4 1 2 0 3 2 5 3-1-1-1-1-1-3 2 2 3 2 5 2l3 2v1c1 2 2 3 4 4 0 2 0 3-1 5h0l-1 5c-1 2 0 2-2 2-1 0-1 0-2-1h-2l-1 2c0 1 2 3 2 5h2v5l-1 1 1 1c-1 1-1 2-2 2-1 1-3 2-4 3l1 1-2 1-9 3h-3-1l-1 1-2-1c-2-1-3-2-5-3-1-1-3-2-4-3l-1-1c-1 0-2-2-3-2-3 1-6 3-9 4 0 0-1 0-1-1-1-1-1-1-3-2 1-1 2-1 3-1l-1-1v1c-4-2-8 0-10-1v-2l1-1v1c2 0 2 0 3-2h0v-1-3l1-1-1-1-1 1h-1l1-2-1-1v-2c-1 1-2 1-3 2v-3-2c-1 0-1-1-3 0v-3-1c-1 0-1 1-2 1v1h-1 0l1-1-1-1c-1 0-1 0-2-1l1-2-1-1-1 1h-1l-1 1-1-3c1-1 3-1 5-2l-1-1 1-1h1 2l1-2h-1-1v-1l2-2v-1l1-1-1-1c0-1 0-1 1-1 0 0 1 1 2 1 0 1-1 2-1 2 1 1 1 1 2 1v-2-1h1c0-4 1-6 4-10 0-1 0-1 1-2z" class="R"></path><path d="M429 759c-1 0-1-1-2-1v-1h2c1-1 2-1 3-1l-1 1-2 2z" class="Z"></path><path d="M405 735l1-1-1-1c0-1 0-1 1-1 0 0 1 1 2 1 0 1-1 2-1 2l-1 2c1 1 1 1 0 2 1 0 1 1 2 2l-1 1v-2l-1 1h0v2h-2v-2l1-2h-1-1v-1l2-2v-1z" class="N"></path><path d="M431 757l2 3-2 2 1 2h-2c-1 0-1-1-1-1s-3 1-3 2v1l2 1h-2c-1 1 0 1-2 1 0-2-1-2-1-4h1c1 0 4-4 5-5l2-2z" class="G"></path><path d="M404 741v2h2c-1 0-2 1-3 2h0 2l2 2c0 1-1 1-2 2h1c1 0 2-1 4-1 1 0 1-1 2-2l1 1c-1 1-2 1-1 2 1-1 1-2 2-1s1 0 0 2h0c1 1 0 2 0 3-1-1-2-3-4-3-1-1-2 0-4 1h-1v-1c-1 0-1 1-2 1v1h-1 0l1-1-1-1c-1 0-1 0-2-1l1-2-1-1-1 1h-1l-1 1-1-3c1-1 3-1 5-2l-1-1 1-1h1 2z" class="a"></path><path d="M406 751c2-1 3-2 4-1 2 0 3 2 4 3l1 2c1 1 3 3 3 4h1l2 2c0 2-2 2-3 3l-2-1c-1 0-2 1-3 0l1-1-1-1-1 1h-1l1-2-1-1v-2c-1 1-2 1-3 2v-3-2c-1 0-1-1-3 0v-3h1z" class="K"></path><path d="M406 751c2-1 3-2 4-1 2 0 3 2 4 3l1 2-3 1-2-2 1-2-1-1c-1 1-2 1-3 1l-1-1z" class="C"></path><path d="M413 767l1-1 2 1h2 0c0 1 1 2 2 2h2l1 1 1-1c1 1 1 0 2 1 1 0 1-1 2-1s1 0 2-1l2 1 2 2h0c0 1 0 2-1 3-1 0-2-2-3-2-3 1-6 3-9 4 0 0-1 0-1-1-1-1-1-1-3-2 1-1 2-1 3-1l-1-1v1c-4-2-8 0-10-1v-2l1-1v1c2 0 2 0 3-2z" class="c"></path><path d="M432 769l2 2h0c0 1 0 2-1 3-1 0-2-2-3-2-3 1-6 3-9 4 0 0-1 0-1-1-1-1-1-1-3-2 1-1 2-1 3-1h1l1-1c1-1 2 0 3 0v1l1 1c1-1 3-3 5-3l1-1z" class="l"></path><path d="M415 721c2-1 4-2 6-1l3 3h1c1-1 2-1 4-1 1 1 2 1 2 2v4h1c1-1 4-2 5-2 1 2 0 4 2 6 1 2 0 2 0 4v4h1v1c-1 0-1 0-2 1-2 1-2 1-3 3h-1-2l1 2c-1 0-1 0-1-1-1 1-2 2-2 3v1h-2l-1-1c-2-1-4-2-6-4l-3-3c-1-1-1-2-2-2h-2c-1 0-1-1-2-1 0-2-1-4-3-6h1c0-4 1-6 4-10 0-1 0-1 1-2z" class="C"></path><path d="M428 743h2 2 1c1 0 1 1 1 2h-2l1 2c-1 0-1 0-1-1l-3 1c1-1 0-1 1-2l-2-2zm-9-1l2-2c1 1 2 1 2 2l3 1c-1 1-1 1-1 2h-4l-3-3h1z" class="Q"></path><path d="M419 742l2-2c1 1 2 1 2 2l-1 1c-1 0-2-1-3-1z" class="E"></path><path d="M426 743h2l2 2c-1 1 0 1-1 2l3-1c-1 1-2 2-2 3v1h-2l-1-1c-2-1-4-2-6-4h4c0-1 0-1 1-2z" class="D"></path><path d="M426 743h2l2 2c-1 1 0 1-1 2-1 0-1 1-2 1 0-1-1-2-2-2v-1c0-1 0-1 1-2z" class="E"></path><path d="M432 728c1-1 4-2 5-2 1 2 0 4 2 6 1 2 0 2 0 4v4h1v1c-1 0-1 0-2 1-1-1-2-1-3-1l-1 1c-1-1-3-3-3-4-1-1 0-4 0-5h3v-1c-2 0-2 0-3-1 0-1 0-2 1-3z" class="o"></path><path d="M436 727l1 1v1c0 1 0 4-1 5-1 0-1 0-2-1 1-1 1-2 1-4-1-1 0-1 1-2z" class="J"></path><path d="M439 732c1 2 0 2 0 4v4h1v1c-1 0-1 0-2 1-1-1-2-1-3-1 1-2 1-4 1-6h1v-2l2-1zm-24-11c2-1 4-2 6-1l3 3h1c1-1 2-1 4-1 1 1 2 1 2 2v4h0-2l-4 2h0c-1 1-2 1-2 2v1c-1 3-2 4-4 6h-1-1l-1-2c-1 2-2 2-4 2 0-2-1-4-3-6h1c0-4 1-6 4-10 0-1 0-1 1-2z" class="E"></path><path d="M425 723c1-1 2-1 4-1 1 1 2 1 2 2v4h0l-2-2-4 2-1-1c1-1 2-2 2-3l-1-1z" class="B"></path><path d="M416 724l1-2 1-1c1 1 1 2 1 3s0 1-1 2-2 2-2 3l-2 2c-1-1-1-2-1-3 1-1 1-2 3-4z" class="s"></path><path d="M414 723l2 1c-2 2-2 3-3 4 0 1 0 2 1 3l-1 1c1 1 1 1 2 1l1 1c1-1 0-3 2-5v1c0 1 1 1 1 2v3h1c1-1 1-2 3-2-1 3-2 4-4 6h-1-1l-1-2c-1 2-2 2-4 2 0-2-1-4-3-6h1c0-4 1-6 4-10z" class="H"></path><path d="M437 726c2 0 4 0 5 2l1 1 1-1c2 0 4 1 6 3 1 1 1 2 1 4v1c1 0 3 0 4 1 2 0 3 2 5 3-1-1-1-1-1-3 2 2 3 2 5 2l3 2v1c1 2 2 3 4 4 0 2 0 3-1 5h0l-1 5c-1 2 0 2-2 2-1 0-1 0-2-1h-2l-1 2c0 1 2 3 2 5h2v5l-1 1 1 1c-1 1-1 2-2 2-1 1-3 2-4 3l1 1-2 1-9 3h-3-1l-1 1-2-1c-2-1-3-2-5-3-1-1-3-2-4-3l-1-1c1-1 1-2 1-3h0l-2-2-2-1c0-1-1-1-2-1l-2-1v-1c0-1 3-2 3-2s0 1 1 1h2c1 0 1 0 3-1l1 2-1 2h1c1 0 1-1 1-1 0-1 1-2 1-2h0-1l-1-1v-2c1-1 2-1 3-2h-1-1l-2-1v-2h1c1 0 1 1 2 0h2 1l1-1v-1c-1-1-2-1-3-2-1 1-1 0-2 1 0-2 0-2-1-3h-2v-1c1-1 1-2 1-3v-1c1-2 1-2 3-3 1-1 1-1 2-1v-1h-1v-4c0-2 1-2 0-4-2-2-1-4-2-6z" class="L"></path><path d="M447 767c2-1 2-2 4-1 2 0 3 1 4 2h0l-1 1c-1 0-1 1-2 1-1 1 0 1-1 1-2 1-3 0-4 0-1-1-2-1-2-2l2-2z" class="V"></path><path d="M451 771c-2 1-3 0-4 0-1-1-2-1-2-2l2-2 1 1h2 1c0 1-1 2-1 2 0 1 0 1 1 1z" class="D"></path><path d="M462 765h0l1-1h1 2v5l-1 1 1 1c-1 1-1 2-2 2-1 1-3 2-4 3-1 0-2 1-3 1-1-1-2-3-3-4l1-1c2-1 6 0 8 0h0 1l-2-2h1v-5h-1z" class="O"></path><path d="M459 767h-1c-1 0-2 0-2-1-1 0-1-1-2-1l-1-1c-1-1-2 0-3 0s-2-1-2-1c-1-1-1-2-2-2l-1-1v-1c2 0 3 0 4-1v-1h2 1 0 2l-1-2c1 0 1 0 2 1h1s1 0 1 1h1 1l1 1h0l-3 4 1 1c0 1 0 2 1 4h0z" class="G"></path><path d="M438 764l1-1 2-1h0 1l-1 2h0l2-1 1-1h1l1 2v1c-2 1-2 3-4 3-1 1-2 2-3 2h-2c-1 1-1 1-3 1l-2-2-2-1c0-1-1-1-2-1l-2-1v-1c0-1 3-2 3-2s0 1 1 1h2c1 0 1 0 3-1l1 2-1 2h1c1 0 1-1 1-1 0-1 1-2 1-2z" class="K"></path><path d="M434 771l2 2h2l1 1h1l1-2v2h1l1-1h2l1-1 1 2h4l1-1h2 0c1 1 2 3 3 4 1 0 2-1 3-1l1 1-2 1-9 3h-3-1l-1 1-2-1c-2-1-3-2-5-3-1-1-3-2-4-3l-1-1c1-1 1-2 1-3z" class="Y"></path><path d="M447 778l3-1c1 1 1 2 2 2l-1 1h-3c-1 0-1 1-1 1h-1l-1-1 2-2z" class="i"></path><path d="M439 777h1c1 0 1-1 2-1s2 1 2 1c1 1 2 1 3 1l-2 2 1 1-1 1-2-1c1 0 1 0 0-1 0-1-1-2-3-2l-1-1z" class="k"></path><path d="M434 775c2-1 2-1 4-1l1 3 1 1c2 0 3 1 3 2 1 1 1 1 0 1-2-1-3-2-5-3-1-1-3-2-4-3z" class="S"></path><path d="M454 773h0c1 1 2 3 3 4 1 0 2-1 3-1l1 1-2 1-9 3h-3s0-1 1-1h3l1-1v-1c0-2 1-2 1-3 0 0 0-1 1-2z" class="n"></path><path d="M453 750h1c1-1 1-1 2-1l1-2h2 1 0l2 1 2-1 1 1c0 1 1 2 1 3h3v-1l1 1-1 5c-1 2 0 2-2 2-1 0-1 0-2-1h-2l-1 2c0 1 2 3 2 5h-1l-1 1h0c-1 1-2 2-3 2h0c-1-2-1-3-1-4l-1-1 3-4h0l-1-1c0-2 0-2-1-3-2 0-4 0-5-1-1 0-2 1-3 1v-1c0-1 1-2 1-3h2z" class="K"></path><path d="M460 749v1c1 0 2 0 2-1h1v3c-2 2-2 2-5 2-1-1-1-1-2-1s-1-1-2-1v-1h1 3c0-1 1-2 2-2z" class="B"></path><path d="M437 726c2 0 4 0 5 2l1 1 1-1c2 0 4 1 6 3 1 1 1 2 1 4v1c1 0 3 0 4 1 2 0 3 2 5 3-1-1-1-1-1-3 2 2 3 2 5 2l3 2v1c1 2 2 3 4 4 0 2 0 3-1 5h0l-1-1v1h-3c0-1-1-2-1-3l-1-1-2 1-2-1h0-1-2l-1 2c-1 0-1 0-2 1h-1-2c0 1-1 2-1 3-3-1-4-1-6 1h-2c-1-1-2-1-3-2-1 1-1 0-2 1 0-2 0-2-1-3h-2v-1c1-1 1-2 1-3v-1c1-2 1-2 3-3 1-1 1-1 2-1v-1h-1v-4c0-2 1-2 0-4-2-2-1-4-2-6z" class="Q"></path><path d="M460 747c1 0 1-1 2-1 2-1 3-1 4 0 0 1-1 1-2 1l-2 1-2-1z" class="E"></path><path d="M440 740c2-1 5-1 7-1-1 2-1 2-4 3l-3-1v-1z" class="C"></path><path d="M451 745l3-2v1 2c-1 1-1 2 0 3l-1 1h-2l-2-2c1-1 2-2 2-3z" class="F"></path><path d="M444 732h1 1c-1 2-2 2-3 4h2 0v2l-3 1-1-1 1-1c0-1 0-1-1-2l3-3z" class="K"></path><path d="M459 737c2 2 3 2 5 2l3 2v1c1 2 2 3 4 4 0 2 0 3-1 5h0l-1-1v1h-3c0-1-1-2-1-3l-1-1c1 0 2 0 2-1l1-1-4-3c-1 0-2-1-3-1v-1c-1-1-1-1-1-3z" class="B"></path><path d="M447 739l1-1c2 1 2 2 3 3l-1 4h1c0 1-1 2-2 3l2 2c0 1-1 2-1 3-3-1-4-1-6 1h-2c-1-1-2-1-3-2-1 1-1 0-2 1 0-2 0-2-1-3h-2v-1c1-1 1-2 1-3v-1c1-2 1-2 3-3 1-1 1-1 2-1l3 1c3-1 3-1 4-3z" class="E"></path><path d="M444 754v-3c0-1 1-1 2-2-1-1-1-2 0-2h1c2-1 2-1 3-2h1c0 1-1 2-2 3l2 2c0 1-1 2-1 3-3-1-4-1-6 1z" class="C"></path><path d="M438 742c1-1 1-1 2-1l3 1c2 1 2 1 3 3h1c-1 1-2 1-3 1h0c-2 2-3 2-4 3h-1-3l1-2-2-2c1-2 1-2 3-3z" class="o"></path><path d="M513 277c2-1 5-1 8-1h5 0c3 1 7 0 10 1s7 1 10 2c2 1 4 2 5 2 2 1 4 2 6 2l1 1h0v2c1 3 2 7 2 11 1 2 2 7 1 9l-2-1-1 1c1 0 3 1 4 1 1 3 2 16 2 19-1 2 0 3-1 5h0c-1-1-3-2-3-3l-1-1v-1h-1v10l-3-1h0-4c-2-1-3-1-4-1l-2-1h-1v-1l-1-1h-2c-2 0-3 0-5-1-2 1-5 0-7 0h-7l-7 1h0c-2-1-3 0-4-2l1-5c0-1-1-2-2-3h-3c-2 1-4 1-5 3l1 1-2 1h-1v-3-1l-1-1h-1l2-2v-2-1l1-2v-1h-1l-1-1v-3h2 0l-1-1c-1 0-1-1-2-1h-1v-2c-1 0-1 0-1-1h-1l2-1h1c1 0 3-1 4-1l3-1h3 3v-2h0l3-1h0-2c0-1 0-2 1-3 0-1 0-14-1-17l1-1z" class="C"></path><path d="M514 312l1-2h1l1 1-1 2h-1l-1-1z" class="F"></path><path d="M511 299l3-1c2 1 4 0 6 1l-9 2v-2h0z" class="L"></path><path d="M515 331v-1c-1-1 0-2 0-3h3 2 1c0 2 0 2 1 3l-7 1z" class="H"></path><path d="M512 303h2l1 1s-1 1-2 1l1 1v5 1l1 1h-1c-1 1-2 3-4 4 4 0 7-1 10 0-2 1-5 1-7 2l2 2-1 1c1 1 2 1 2 3h0c-2-1-2-1-4-1 0-1-1-2-2-3l-1-1v-1c1 0 2 0 3-1h0l-3-1c0-1-1-2-1-3l1-1-1-2c2-2 0-3 1-5l2-2h1v-1z" class="Q"></path><path d="M521 327v-1-1c0-2 0-4 1-5h2s1 1 1 2h0 2 0l-1-2c0-1 1-1 1-1h9v8 2 1c-2 1-5 0-7 0h-7c-1-1-1-1-1-3z" class="J"></path><path d="M505 301h3c1 1 2 1 2 2l1 1v-1h1v1h-1l-2 2c-1 2 1 3-1 5l1 2-1 1c0 1 1 2 1 3l3 1h0c-1 1-2 1-3 1v1l1 1h-3c-2 1-4 1-5 3l1 1-2 1h-1v-3-1l-1-1h-1l2-2v-2-1l1-2v-1h-1l-1-1v-3h2 0l-1-1c-1 0-1-1-2-1h-1v-2c-1 0-1 0-1-1h-1l2-1h1c1 0 3-1 4-1l3-1z" class="R"></path><path d="M495 304l2-1h1l1 1h1c1 1 1 0 1 1s0 2 1 3h0l-1 1h0l-1-1c-1 0-1-1-2-1h-1v-2c-1 0-1 0-1-1h-1z" class="T"></path><path d="M505 301h3c1 1 2 1 2 2l1 1v-1h1v1h-1l-2 2c-1 2 1 3-1 5l1 2-1 1c0 1 1 2 1 3-1 1-3 1-5 1v-3l-1-1h-1l2-2c0-1 0 0-1-1v-2-1c1-2 2-2 4-2v-1h-1-2c-1 0-1-1-1-2l-1-1 3-1z" class="G"></path><path d="M508 314c0 1 1 2 1 3-1 1-3 1-5 1 1-2 0-2 1-4l2 1 1-1z" class="P"></path><path d="M540 322l1-2c2 0 4 1 6 2h2l2 1c1 1 2 1 3 1l4 2v10l-3-1h0-4c-2-1-3-1-4-1l-2-1h-1v-1l-1-1h-2c-2 0-3 0-5-1v-1-2c1-3 0-5 1-7l3 1v1z" class="H"></path><path d="M544 332h2c1 0 0 0 1 1h3c1-2 1-3 1-5l1-1 1 1c0 2-1 5 1 6l1 1h0-4c-2-1-3-1-4-1l-2-1h-1v-1z" class="C"></path><path d="M536 327c1-3 0-5 1-7l3 1v1 7h-1-3v-2z" class="e"></path><path d="M520 300c10-1 19 0 28 3l9 3c0 1-1 2-1 4h0l2 2c1 0 1 0 1-1l1-3h0 1v1 2 1c0 1 1 0 0 2h0c1 2 1 3 1 4v2 1 4c-1 0-1 0-2-1h-1l-1-1c-3 0-4-1-7-1-3-2-7-3-10-4-7-1-14-2-21-2-1-2-1-3-1-5l-1 1v-1l1-1-1-2v-1c0-1 0-1 1-2v-1c0-2-1-1-2-3 1-1 2-1 3-1z" class="H"></path><path d="M540 308h2c0 2 0 3-1 5l-1-1v-4zm5 1h3v1c0 2 1 4 0 6h-1 0c0-1 0-2-1-3h-1 0-1l1-4zm14 2h1l-1 2c0 1 1 3 2 4l-1 2h-2 0c0 1 1 2 1 3h-1c-1-2-1-2-2-3h-2v-1c-1-1-2-1-3-2s0-3 0-4v-1l1 2h1l1-1 1 1 1 1v1h1c0-2 0-3-1-4v-1h0l2 2c1 0 1 0 1-1z" class="E"></path><path d="M513 277c2-1 5-1 8-1h5 0c3 1 7 0 10 1s7 1 10 2c2 1 4 2 5 2 2 1 4 2 6 2l1 1h0v2c1 3 2 7 2 11 1 2 2 7 1 9l-2-1-1 1h-1l-9-3c-9-3-18-4-28-3v-1c-2-1-4 0-6-1h0-2c0-1 0-2 1-3 0-1 0-14-1-17l1-1z" class="H"></path><path d="M543 287l1 1v7c-1 1 0 1-1 0v-8z" class="E"></path><path d="M536 281h1v14h0-1v-14z" class="F"></path><path d="M525 285h1s1 0 1 1c1 2 1 5 0 7h-1-1c0-3-1-6 0-8z" class="d"></path><path d="M550 297l2-1h1l1-1c0-1 0-2 1-3s1 0 1-1 0-1 1-1l1 1v1 4c1 1 1 1 1 2h1v-1c1 2 2 7 1 9l-2-1c-3-2-7-3-10-4h4l2 1c2 1 3 2 5 2h1l-1-2h-2c0-1-1-2-2-3h-1c-2-1-3-2-5-2z" class="I"></path><path d="M520 298c10 0 19 1 29 3 3 1 7 2 10 4l-1 1h-1l-9-3c-9-3-18-4-28-3v-1c-2-1-4 0-6-1h0 6z" class="h"></path><path d="M551 281c2 1 4 2 6 2l1 1h0v2c1 3 2 7 2 11v1h-1c0-1 0-1-1-2v-4-1l-1-1c-1 0-1 0-1 1s0 0-1 1-1 2-1 3l-1 1h-1l-2 1h0l-2-1c0-1 0-2 1-2v1c1 0 0 0 1-1 0-1-1-1-1-2v-1-5h1c-1-1-1-1-2-1v-1c1-1 1-1 2-1l1 1v-3z" class="F"></path><path d="M549 286l4 4c0 1 0 3-1 5-1-1-2-3-3-4v-5z" class="V"></path><path d="M513 277c2-1 5-1 8-1l-1 1h-3l-1 1h1c1 0 1 1 2 1h2c0 1 0 1 1 2-1 1-1 1-2 1l-1 1v-1c-1 1-1 3-1 4-1 3 1 7-1 10l-2-1-1 1h1c2 1 3 1 5 2h0-6-2c0-1 0-2 1-3 0-1 0-14-1-17l1-1z" class="I"></path><path d="M520 583h12c10 1 18 2 27 6v1 76c-2 2 1 9-1 11v4c0-1 0-1-1-1 0 1 0 1-1 2v-5h1l1-1-1-1-1 1c-1-3-2-5-4-7l-1-1c-1 0-2-1-4-2h-4c-3-1-3-1-4-3l-1 1 1 2h0c-1 1-1 1-1 2h0-1v-3c-2-2-1-5-2-7s0-4-1-6h-1v-3l-1-2h0c-1-1-1-2-1-3h0l-1-2 1-1-2-2c0-1 1-1 1-2 0 0-1-1-2-1 0-1-1-4-1-5l-1-5h-1l1 12c0 2 1 4 1 7l-2-1 1-1h-1c-1-1-2-1-4-1v-2-3c0-1 0-1-1-1l-2 1v-1c1-2 1-4 2-7l2-6c0-1 1-1 2-1l-1-1c-2 0-1 1-2 1l-2-1-2-1s0-1-1-1c2-2 2-3 2-5l-1-1c-1-1-1-3-1-4h0l1-1 2-1c1-2 1-3 1-4h-4c1-1 2-4 2-4-1-2-1-1-1-3v-1c0-3 0-8-2-10h-2v-1l1-1h6z" class="o"></path><path d="M532 604h3v1c-1 1-1 2-1 3v1l-1 1h-1v-6z" class="F"></path><path d="M552 669c1-1 2-1 3-1 1 1 1 2 1 3 0 2 0 3 1 4l-1 1c-1-3-2-5-4-7z" class="E"></path><path d="M530 623c0-1 1-1 1-2 1 0 1-2 1-2 0-2 0-4 1-5 1 2 1 4 1 6l1 2 1-1c0-1 0-2-1-3 0-1 1-2 1-3h1c0 2 0 4 1 6v2c-2 1-3 1-5 1l-3-1z" class="F"></path><path d="M540 624h0v-1-4c0-3 1-4 0-7v-4l1-1 1 1c0 1 0 1-1 2l1 4 2 2h0c-2 1-2 1-2 3l1 1v-1l2 2v3c-1 1-2 1-3 1l-2-1z" class="E"></path><path d="M528 626c-1-1-1 0-1-2h2l1-1 3 1h7l2 1 1 1-1 2c-2 0-4 0-6-1h-1c-1 1-2 1-3 1-2 0-2 0-4-2z" class="J"></path><path d="M548 625c-1 0-1 0-2-1 0-1 1-1 1-2s-1-1-1-1c0-1 0-3 1-4 0-3-1-8 1-10 1-1 1-1 2-1 0 2 1 5 1 7 1 1 1 5 1 6s0 2 1 3v4h-1-1c-1-1-2 0-3-1z" class="C"></path><path d="M548 625h1c0-1 0-2 1-3h0v-1c0-1-1-1-1-2 0-3-1-6-1-10h1c0 2 0 3 1 4h1c1 1 1 5 1 6s0 2 1 3v4h-1-1c-1-1-2 0-3-1z" class="F"></path><path d="M545 651h1c1 0 2 0 4 1l1-1c1 0 2 1 3 1s3 2 3 3l-1 2c0 1 1 2 1 3v2c0 2 0 4-1 5l-4-2c-2 0-2 0-3-1v-1c-2 1-4 0-5-1l-2-2c1-2 0-4 1-6l2-3z" class="E"></path><path d="M514 583h6-1c-1 1-1 1-3 1l1 6c1 1 0-1 0 1l1 1c0 3 1 7 0 10 1 0 1 0 2-1 0-1 0-1 1-2-1-1-2-1-3-2 1-1 0-1 1-1 0 1 0 1 1 2l1-1v-2c-1-2-2 0-2-3h-1v-4h0v-2c2-1 6 0 8 0v11c-1 2-2 0-4 2 2 2 4 2 5 5l2-1s1 1 2 1v2c-1 0-2 1-2 2s1 4 1 6c0 1-1 3-1 4l-1 1h0-3c-3-3 0-3-1-5v-2c-1-2-1-3 0-5 0-3 0-3-1-5-2 0-2 0-3 1h-4c1-1 2-4 2-4-1-2-1-1-1-3v-1c0-3 0-8-2-10h-2v-1l1-1z" class="F"></path><path d="M520 603c1-1 1-1 3-1 1 2 1 2 1 5-1 2-1 3 0 5v2c1 2-2 2 1 5h3 0l1 5h-2c0 2 0 1 1 2 0 3 2 6 3 8 0 1 0 1 1 2h-1v2h5v-2-3l1-2c1 1 2 1 2 1 1 2 1 3 1 4v3h-3 0v2c1 0 2-1 3-1s2-1 2-2l2-2h1v1c2-2 1-5 2-7h2v1l1 2c1 2 2 5 1 8v1l2-1c1-1 1-2 1-3-2-1-2-1-3-2 0-2 1-3 1-4h1c1 0 1-1 2-1l1-1h0v1c0 1-1 1 0 3 1 3 0 6 0 9-3-1-6-1-9-1l-1-1v-1-1c-1 1-1 2-1 3 1 1 1 1 2 1s2 1 4 2h1c1 0 1 1 3 1l1-1c1 2 1 2 1 4l-1 1v-1c-2-1-3-1-5-2-2 0-7-1-8-2v-1c1-2 1-3 0-4l-1-1c-1 1-1 1-2 3 1 1 1 2 1 3l-5-2h-3c0 1 1 2 1 3s1 2 1 3c2 2 3 1 3 4v1 1c0 2-1 3-2 4l1 1h3l1 1c-1 0-2 1-2 2l-1 1 1 2h0c-1 1-1 1-1 2h0-1v-3c-2-2-1-5-2-7s0-4-1-6h-1v-3l-1-2h0c-1-1-1-2-1-3h0l-1-2 1-1-2-2c0-1 1-1 1-2 0 0-1-1-2-1 0-1-1-4-1-5l-1-5h-1l1 12c0 2 1 4 1 7l-2-1 1-1h-1c-1-1-2-1-4-1v-2-3c0-1 0-1-1-1l-2 1v-1c1-2 1-4 2-7l2-6c0-1 1-1 2-1l-1-1c-2 0-1 1-2 1l-2-1-2-1s0-1-1-1c2-2 2-3 2-5l-1-1c-1-1-1-3-1-4h0l1-1 2-1c1-2 1-3 1-4z" class="C"></path><path d="M519 610h2v1l-1 1h-2v-1l1-1zm15 36c0 1 1 2 1 3 2 2 3 1 3 4v1c-1 0-2 0-3-1v-1l-1-6z" class="F"></path><path d="M536 633l1-2c1 1 2 1 2 1 1 2 1 3 1 4l-1 2c-1 0-1 0-2-1l-1-4z" class="i"></path><path d="M524 622c0 2-1 5-1 8-1 3-1 7-2 10h0v-3c0-1 0-1-1-1l-2 1v-1c1-2 1-4 2-7l2-6c0-1 1-1 2-1z" class="t"></path><path d="M576 512l3 3h1c0-2 0-2 2-2h1l1 2v5 171h0c-2-1-3-1-5-1h-3c-2 1-4 1-6 1v1h-1c-2 0-4 0-7-1v-16l2-1h0c-1-4 0-8 0-12 0-1-1-3 0-4 1-4-1-7-1-11v-5c1-1 0-2 1-3 0-2 1-3 1-5v-1-1-11-7c0-1-1-1 0-3 0-2-1-5 0-8v-5-11-5c0-3 0-5-1-8l1-1v-1-1c0-1-1-4 1-6 0-1 0-2-1-3 2-1 3-2 3-4l-2-1h0l2-2v-3l2-1v-1c-1 0-1 0-2-1l1-1c1-2 0-5 0-7 2-3 1-6 0-9v-1l1-1c1-1 1-1 1-2l-1-1c0-1 0-4 1-5v-1c0-2-1-4 1-6h4v-3z" class="F"></path><path d="M573 683c1 1 2 1 3 1l1 1c-2 0-3 2-5 2v-4h1z" class="o"></path><path d="M576 512l3 3h1c0-2 0-2 2-2h1l1 2v5l-1-2h-2c-1 1-2 2-1 4 0 1 0 0-1 2-1-1 0-3-1-4v-2c-1-1-1-2-2-3v-3zm4 84c1 1 1 1 1 2v28h0l-1-2v6h0v-7l-1-4v-1c-1 0-2 1-3 1 0-1-1-3 0-4h0l1 1c1 0 1 1 2 0v-4c0-1-1-2-1-2h-1c1-1 1-1 2-1h0v-1l-1-2v-1h1v-3-4l1-2z" class="I"></path><path d="M576 631c2 0 3 1 5 0 1 3 0 11 0 14l-1 2h0c1 1 1 3 1 4v4h-2c-1-1-2-2-3-2v-2l1-1h1 1c-1-1-1-1-2-1v-1c0-1 1-2 1-3l1-1c-1 0-1-1-2-2 0-2 1-3 2-5h0l-1-1c-2-1-1-3-2-5z" class="Q"></path><path d="M578 669c1-1 1-2 1-3l1-1 1 1h0c1 2 1 3 1 5h-1l-3-1c-1-1-1 0-2-1s-2-1-2-1c-1 1-2 2-2 3h1c1-1 1 0 3 0l2 2c1 0 2 0 3 2v13-1h-1c-1-1-1-2-1-3v-2c-1-1 0-1-1-1-1 1-1 2-1 3h-1c-1 0-2 0-3-1l-1-2h0l1-1h0 1v-4c0-2 1-1 0-2 0-1 0 0-1-1v-1c-1 0-1 0-2-1 0-1 1-2 2-3h2s1 0 2 1h1z" class="o"></path><path d="M578 669c1-1 1-2 1-3l1-1 1 1h0v4h0l-3-1z" class="P"></path><path d="M573 680l3 2c0-1 0-1 1-2v-1c0-2 0-4 1-5h2c1 2 1 11 1 13h-1c-1-1-1-2-1-3v-2c-1-1 0-1-1-1-1 1-1 2-1 3h-1c-1 0-2 0-3-1l-1-2h0l1-1h0z" class="C"></path><path d="M571 593h5c1 1 3 1 5 1h0l-1 2-1 2v4 3h-1v1l1 2v1h0c-1 0-1 0-2 1h1s1 1 1 2v4c-1 1-1 0-2 0l-1-1h0c-1 1 0 3 0 4v6c0 2 1 4 0 6h0c1 2 0 4 2 5l1 1h0c-1 2-2 3-2 5 1 1 1 2 2 2l-1 1c0 1-1 2-1 3-2 0-3 1-4 2h0-1c-1-3-1-8-1-11v-2c0-1 0-3 1-4h1l1-1h-2c-2-2 0-7-1-10v-5c0-1 1-2 0-3v-5-16z" class="J"></path><path d="M579 602h0-3 0c0-1 0-2 1-3 0-1 1-1 2-1v4z" class="C"></path><path d="M576 515c1 1 1 2 2 3l-1 1h-1c0 2 1 8 0 10l-3-1v1 1h1c1 0 2 0 3 1h2 1l2 1c1 0 1 0 1 1v14c0 2 1 4 0 5l-1 1c-1 1-3 0-5-1s-4-1-7-1v-1c-1 0-1 0-2-1l1-1c1-2 0-5 0-7 2-3 1-6 0-9v-1l1-1c1-1 1-1 1-2l-1-1c0-1 0-4 1-5v-1c0-2-1-4 1-6h4z" class="J"></path><path d="M576 548h1 1c0-1 1-1 2-2h1v3c0 1-1 1-1 2l3 1-1 1c-1 1-3 0-5-1s-4-1-7-1v-1c2 0 4 0 6 1h1l1-1-2-2z" class="B"></path><path d="M576 548h0v-4l1-1c1-1-1-6-1-8 2 0 3-1 4 0 1 3 0 7 0 10l1 1h-1c-1 1-2 1-2 2h-1-1z" class="C"></path><path d="M566 557h0l2-2v-3l2-1c3 0 5 0 7 1l1 2-1 1c0 1 0 1-1 3l1 1c2 0 2 0 4 2v3c-1 0 0 2 0 2 1 2 0 5-1 7v3h1v7 4c-1 2 2 6 0 7h0c-2 0-4 0-5-1h-5 0l-1-1c1-4 0-8 0-12 0-2 0-5 1-7h0c-1-1-1-2-1-3v-3-5c-1 0-1 0-2-1v-1c1-1 1-1 1-2v-1h-3z" class="J"></path><path d="M566 557h0l2-2v-3l2-1v11h0c-1 0-1 0-2-1v-1c1-1 1-1 1-2v-1h-3z" class="K"></path><path d="M577 559c2 0 2 0 4 2v3c-1 0 0 2 0 2 1 2 0 5-1 7l-2-2c1-1 2-1 2-3-1-1-1-2-2-2-1-1-1 0-1-1 2-1 2-2 3-4v-1h-3v-1z" class="Q"></path><path d="M580 576h1v7 4c-1 2 2 6 0 7h0c-2 0-4 0-5-1h-5 0l-1-1c2 0 4-1 6 0v-1-5c0-1 0-1 1-2l2 1v-1c1-2 1-3 0-5l-1-1-2 1v-1l1-1c1 0 2 0 2-1h1z" class="I"></path><path d="M576 586h2c1 1 1 1 1 3v1s0 1 1 2h-2c-1-1-1-1-2 0h0v-1-5z" class="F"></path><path d="M566 557h3v1c0 1 0 1-1 2v1c1 1 1 1 2 1v5 3c0 1 0 2 1 3h0c-1 2-1 5-1 7 0 4 1 8 0 12l1 1h0v16 5c1 1 0 2 0 3v5c1 3-1 8 1 10h2l-1 1h-1c-1 1-1 3-1 4v2c0 3 0 8 1 11h1 0c1-1 2-2 4-2v1c1 0 1 0 2 1h-1-1l-1 1v2c1 0 2 1 3 2h2l-1 1v1c1 3 1 6 1 9l-1-1-1 1c0 1 0 2-1 3h-1c-1-1-2-1-2-1h-2c-1 1-2 2-2 3 1 1 1 1 2 1v1c1 1 1 0 1 1 1 1 0 0 0 2l-1-1s-1 0-1 1c-1 1-2 2-2 4s1 4 0 6v1c0 1 0 1 1 2 2 1 6 0 8 1h-3c-2 1-4 1-6 1v1h-1c-2 0-4 0-7-1v-16l2-1h0c-1-4 0-8 0-12 0-1-1-3 0-4 1-4-1-7-1-11v-5c1-1 0-2 1-3 0-2 1-3 1-5v-1-1-11-7c0-1-1-1 0-3 0-2-1-5 0-8v-5-11-5c0-3 0-5-1-8l1-1v-1-1c0-1-1-4 1-6 0-1 0-2-1-3 2-1 3-2 3-4l-2-1z" class="O"></path><path d="M568 673v3c1 1 1 4 1 6s0 3-1 4c-1-1-1-1-3-2h0v-3l1-1-1-1c0-1 0-2 1-3v-1c0-1 1-1 2-2z" class="Z"></path><path d="M565 681h1c1 1 1 0 1 1l1 1 1-1c0 2 0 3-1 4-1-1-1-1-3-2h0v-3z" class="L"></path><path d="M562 675l2-1 2 2c-1 1-1 2-1 3l1 1-1 1v3h0c2 1 2 1 3 2v4h8c-2 1-4 1-6 1v1h-1c-2 0-4 0-7-1v-16z" class="U"></path><path d="M565 684c2 1 2 1 3 2v4c-1 0-2 0-3-1v-3-2z" class="Z"></path><path d="M565 634c1 1 2 2 3 2 0 1-1 2-1 3v2 4 5c0 2 1 6-1 8v3l1 1-1 1c0 1 1 2 1 4s-2 2-1 3l1 1v1l1 1c-1 1-2 1-2 2v1l-2-2h0c-1-4 0-8 0-12 0-1-1-3 0-4 1-4-1-7-1-11v-5c1-1 0-2 1-3 0-2 1-3 1-5z" class="N"></path><path d="M567 641v4 5c0 2 1 6-1 8v3l1 1-1 1c0 1 1 2 1 4s-2 2-1 3l1 1v1h-1v2l-1-1c-1-1-1-9 0-10 0-1 0-1-1-2v-3h0l1-1c1-3 0-9-1-12v-1l3-3zm-1-76c0 2 2 2 2 3l-2 2c1 1 2 2 2 3-1 2 0 3 0 4l-2 2c1 0 1 1 1 2h0c0 2 1 4 0 6 1 1 1 1 1 2v3 1 4 9c-2 2 1 6-1 9l1 1c0 1 0 1-1 2 0 1 1 2 1 2 0 1-1 3-1 4v1s0 1 1 1l-1 2c1 0 1 0 1 2v2h0v4c-1 0-2-1-3-2v-1-1-11-7c0-1-1-1 0-3 0-2-1-5 0-8v-5-11-5c0-3 0-5-1-8l1-1v-1-1c0-1-1-4 1-6z" class="a"></path><path d="M571 593h0v16 5c1 1 0 2 0 3v5c1 3-1 8 1 10h2l-1 1h-1c-1 1-1 3-1 4v2c0 3 0 8 1 11h1 0c1-1 2-2 4-2v1c1 0 1 0 2 1h-1-1l-1 1v2c1 0 2 1 3 2h2l-1 1v1c1 3 1 6 1 9l-1-1-1 1c0 1 0 2-1 3h-1c-1-1-2-1-2-1h-2c-1 1-2 2-2 3 1 1 1 1 2 1v1c1 1 1 0 1 1 1 1 0 0 0 2l-1-1s-1 0-1 1c-1-2-1-1-3-1v-3c0-2 1-2 0-3 0-4 0-11 1-14l1-3c-1-1-1-1-1-2v-3l-1-1c1-2 1-4 1-6v-25-5c1-4-1-9 0-12 0-2 0-3 1-5z" class="F"></path><path d="M572 653c1 1 3 1 5 2h-1c0 1 1 3 1 5-1 2-1 3-1 6l-1 1c-1-1-2 0-4-1 0-3-1-10 1-13z" class="J"></path><path d="M571 652h2v1h-1c-2 3-1 10-1 13 2 1 3 0 4 1h1l3-3v-9h2l-1 1v1c1 3 1 6 1 9l-1-1-1 1c0 1 0 2-1 3h-1c-1-1-2-1-2-1h-2c-1 1-2 2-2 3 1 1 1 1 2 1v1c1 1 1 0 1 1 1 1 0 0 0 2l-1-1s-1 0-1 1c-1-2-1-1-3-1v-3c0-2 1-2 0-3 0-4 0-11 1-14l1-3z" class="C"></path></svg>