<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="76 76 348 588"><!--oldViewBox="0 0 480 752"--><style>.B{fill:#302b2e}.C{fill:#252023}.D{fill:#464248}.E{fill:#6a656f}.F{fill:#3b373c}.G{fill:#1a1516}.H{fill:#b8141e}.I{fill:#fd141b}.J{fill:#716d77}.K{fill:#ff2b2f}.L{fill:#504b52}.M{fill:#f2eff0}.N{fill:#f1121a}</style><path d="M393 238l1 1c1 1 1 2 2 3-1 0-1 0-1-1v3l-1 1-1-7z" class="G"></path><path d="M288 117c0 1 1 1 1 2v2 8s0-1-1-1v4c-1-5-1-10 0-15z" class="H"></path><path d="M204 244l6 8h-6v-3c-1-1-1-4 0-5z" class="I"></path><path d="M303 394c0 2 1 3 2 5l-5 6-1 1-1-2c0-2 4-4 4-6 1-1 0-2 1-4z" class="C"></path><path d="M414 227l3 1c-1 5-4 12-7 16h-1c3-5 4-10 5-17z" class="G"></path><path fill="#f8494c" d="M206 260l5 12c-1 1-2 1-2 1-2-1-4-4-5-6 1-1 1-3 1-4l1-3z"></path><path d="M149 103c7-4 14-5 22-7-2 1-5 2-8 3-4 3-8 5-12 8 1-2 3-2 4-4 1 0 2-1 2-1h1v-1h-2-1l-1 1h-2c0 1 0 1-1 1h0-2z" class="B"></path><defs><linearGradient id="A" x1="75.401" y1="229.047" x2="70.766" y2="232.508" xlink:href="#B"><stop offset="0" stop-color="#22201e"></stop><stop offset="1" stop-color="#3b363b"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M70 226c1-1 2-1 3-2 1 0 0-1 1-1l1-1c-2 6-2 11 0 17 1 2 3 4 4 6-5-5-8-12-9-19z"></path><path fill="#030202" d="M154 372l1-1 2 1c0 4 0 35-1 36l-1-14c0-1 1-4 0-5-1-2-2-1 0-3l-1-14z"></path><defs><linearGradient id="C" x1="333.15" y1="105.29" x2="327.905" y2="96.296" xlink:href="#B"><stop offset="0" stop-color="#222121"></stop><stop offset="1" stop-color="#3f393f"></stop></linearGradient></defs><path fill="url(#C)" d="M337 108c-4-3-8-6-13-8-2-2-5-3-7-4h0l12 3c4 1 8 3 12 5-1 0-3 1-4 0h0c-1 0-1 0-1 1h-1l2 1v2z"></path><path d="M209 276h1c1 1 1 2 3 1 1 2 2 6 3 9 0 1 1 2 1 3s2 3 3 5c2 4 5 8 7 13 1 1 2 2 1 4-2-3-4-6-5-9l-6-9c-2-4-3-7-5-11l-3-6z" class="M"></path><path d="M89 249h0v3h3l-1 61h0l-2-64zm99-157c-1-2-2-4-2-6-1-3-3-6-4-9-5-7-11-13-19-17l-12-3c1-1 3 0 4 0 4 1 8 2 12 4 12 5 21 17 26 29-1 0-2-1-2-1v-1c-1-1 0-1-1-1 0-2-2-4-2-5-1-1-1-1-1-2l-2 2v1c1 0 1 0 1 1v1l1 1v2h0c1 0 1 0 1 1h0v3z" class="B"></path><path d="M396 242c1-1 1-1 2-1v2l1-1-2 69v16 7c-1-3-1-5-1-8v-13-44c-1-8-1-16-2-24l1-1v-3c0 1 0 1 1 1z" class="C"></path><defs><linearGradient id="D" x1="136.719" y1="341.119" x2="165.888" y2="322.762" xlink:href="#B"><stop offset="0" stop-color="#110f0f"></stop><stop offset="1" stop-color="#2d2a2d"></stop></linearGradient></defs><path fill="url(#D)" d="M146 292c2 1 3 2 3 4h1c3 9 2 18 4 27 1-5 0-11 0-16 1 2 3 3 4 5 2 3 4 9 7 10v1c-2 1-7 4-7 7v1 1c-1 1-1 2-1 3l1 1h-1l1 1c-2 11-1 23-1 35l-2-1-1 1v-2-6c0-1-1-1-1-2-1 0-1-1-1-2l1-2-1-27c0-11 0-25-7-35 0-1 1-1 1-1v-3z"></path><path fill="#030202" d="M288 117c1-15 6-32 16-43 7-9 18-16 30-17-1 0-3 1-4 1-3 1-7 2-10 4-11 7-18 19-21 31-4 16-1 36 8 49 4 8 12 14 21 16 0 4-1 7 0 11 1 6 4 10 9 13 2 1 4 2 6 1 7-2 12-9 16-15-1 8-4 15-7 23 0 1-2 5-1 6 1-2 2-6 3-8l7-22c1-4 2-9 2-13 0-3-2-7-3-11-4-14-12-25-23-35v-2l-2-1h1c0-1 0-1 1-1h0c1 1 3 0 4 0 23 13 42 36 50 62 1 4 1 7 2 11 0 3 1 5 2 8 2 5 6 11 10 16 5 5 9 10 11 17 1 3 1 7 1 10l-3-1c-1-3-2-5-4-7-1-1-2-1-4-1s-3 2-4 3c-1 2-1 4-2 6-1 5-1 9-1 14l-1 1v-2c-1 0-1 0-2 1-1-1-1-2-2-3l-1-1c-2-4-4-9-8-11-2-1-4 0-5 1-8 3-12 14-16 21-2 4-4 8-6 13-2 6-3 12-5 18-1 3-3 6-4 9v4c-2 0-2-2-4-1-2 2-4 4-5 6-2 5-3 9-3 14-1 7-1 15-2 22 0 4-1 23 0 26h0 1c0 2-2 2-2 3-1 2-1 4-1 6v11c0 2-1 4 0 6v1l1 1c0 1-1 1-2 2v12h-1v-45c-1-7 0-14-2-20-1-2-2-4-3-5s-2-1-3-1h-1c-1 3 1 6 1 8-1 1-2 3-3 3-3 6-6 10-7 17 0 1 0 3 1 5h0v1h0c0 1-1 2-2 3-1 0-2 1-2 2-5 8-6 15-8 24h1c-1 2 0 3-1 4 0 2-4 4-4 6l1 2c-8 10-11 24-15 36l-13 51-7 27-9 44-6 41-3 19-2 21-1 11c0 2 0 4-1 6-1-21-4-41-6-61-8-55-20-109-37-163l-7-21c-1-4-3-8-5-12s-4-6-7-8c1-3 1-4 1-7-1-9-4-18-7-27-1-3-2-5-3-8-2-3-4-7-5-10-2-3-2-9-4-11h-1c-2 0-2 1-3 3l-1-1h1l-1-1c0-1 0-2 1-3v-1-1c0-3 5-6 7-7v-1c-3-1-5-7-7-10-1-2-3-3-4-5 0 5 1 11 0 16-2-9-1-18-4-27h-1c0-2-1-3-3-4v3s-1 0-1 1c-1-1-3-4-4-3h-2c0-4-3-9-5-13l-5-16c-2-6-5-11-7-16-3-5-5-9-8-13-3-5-5-7-10-8-2 1-3 1-4 2-6 6-7 15-8 23h-3v-3h0c0-7-1-13-2-19-1-3-1-6-3-9-1-1-2-2-3-2-3 0-4 2-6 3l-1 1c-1 0 0 1-1 1-1 1-2 1-3 2 0-4 0-8 1-11 3-6 6-9 10-14 4-4 9-10 11-16 2-3 1-7 2-10 1-5 2-10 4-14 9-24 28-47 51-58h2 0c1 0 1 0 1-1h2l1-1h1 2v1h-1s-1 1-2 1c-1 2-3 2-4 4l-3 2c-10 10-18 23-22 36-1 3-2 7-2 10 0 4 1 7 2 11 2 8 5 16 8 24 1 3 1 6 3 7l-9-28c1 1 1 3 2 4 3 4 6 7 10 9 2 2 5 2 8 1 1 0 3-1 4-2l1-1c4-4 7-9 7-14 0-2-1-4-1-6v-1l2-1c2-1 4-1 5-2 6-3 11-8 14-14 9-15 12-34 8-50v-3h0c0-1 0-1-1-1h0v-2l-1-1v-1c0-1 0-1-1-1v-1l2-2c0 1 0 1 1 2 0 1 2 3 2 5 1 0 0 0 1 1v1s1 1 2 1c4 10 6 22 6 33 0 4 1 8 0 12 0 8-3 17-5 25-3 7-5 14-6 22-1 4-1 10 0 15 1 1 2 3 3 4 0 1 1 2 1 2l-1 1v1c0 13 6 27 13 39-1 1-1 4 0 5v3h0v3c1 2 1 4 2 5l-1 3c0 1 0 3-1 4 1 2 3 5 5 6 0 0 1 0 2-1l2 5c-2 1-2 0-3-1h-1l3 6c2 4 3 7 5 11l6 9c1 3 3 6 5 9l7 10 6 7c1 2 4 4 5 6h-1l-2-1-1 1 1 3 1 8 6 27h1v-1l4-12c7-22 15-44 19-66v-4c-1-1-3 0-5 0 5-8 10-15 14-23 5-11 8-22 11-33 3-14 7-28 7-42 0-5 0-9-1-14-4-15-13-29-12-45v-4c1 0 1 1 1 1v-8-2c0-1-1-1-1-2z"></path><path d="M230 362c-1-2-1-6-1-8h0l1-1v1c1 2 2 4 2 7l-2-3v4z" class="C"></path><path d="M210 351c1 0 1 0 2-1l1 7c-1 1 0 2 0 4l-3-10zm-44-24c1 1 2 1 3 3v2 2h-1c-1-1-2-2-2-3-1-2 0-2 0-4z" class="E"></path><path d="M320 324c2 0 4 1 6 3-1 0-2-1-2-1h-1l1 1c-2 0-4 1-6 1 0-1 1-2 2-4h0z" class="B"></path><path d="M317 325l3-1c-1 2-2 3-2 4-2 2-3 4-4 6-1-4 2-7 3-9z" class="D"></path><path d="M377 223h1c0 1 1 1 1 2l-3 3c-1 0-1 1-2 1h-1l-1 1h0 0c1-3 3-5 5-7z" class="B"></path><path d="M330 324h0-1c0-2 0-5 1-7 0-1 1-2 2-4 0 0 0-1 1-2v3 1c-1 2-1 7-1 10 0 1 1 3 1 5-2-2-1-2-1-4-1-2 0-5 0-7h-1c-1 1-1 3-1 4h0v1z" class="C"></path><path d="M298 404l-4 4c1-3 4-6 6-9 1-2 1-4 2-5h1c-1 2 0 3-1 4 0 2-4 4-4 6z" class="M"></path><path d="M239 337h4l1 8h-2l-3-8z" class="N"></path><path d="M227 330c1-2 0-5 0-6-1-3-2-7-3-10-1-1-1-2-1-3 3 6 6 11 7 17h0c-1 0-1-1-1-2v5l-1 1h0c0-1-1-1-1-2z" class="D"></path><path d="M207 335c1 2 1 3 2 4s2 4 2 6h0c1 2 1 3 1 5h0c-1 1-1 1-2 1l-3-16z" class="J"></path><path fill="#8e858c" d="M213 361c0-2-1-3 0-4l4 17c0 1 0 1-1 2h0l-3-15z"></path><path d="M289 325v-3h0l1-1v-2-1-2-3h0 0 0l1-1s0-1 1-1c0-1 0-1 1-1 0 0 0-1 1-1v-1c0-2 1-4 2-6v-2c1-2 1-4 3-6l-3 8-3 13c-1 1-2 2-2 4-1 2-1 4-2 6z" class="B"></path><path d="M213 311h1c3 8 6 16 7 24-1-1-2-3-2-4l-4-11c-1-3-3-6-3-9h1z" class="C"></path><path d="M307 231c1-1 1-2 3-3l-3 12c-1 3-1 6-2 8 0 0-1 0-1-1-1-2 1-3 1-5 0-4 0-7 2-11z" class="H"></path><path d="M253 372c0 6-1 12-4 16-1 1-1 3-2 4h0v-5c1-3 2-6 3-10 1 0 1-1 1-2h-1v-1c1 0 2-1 3-2z" class="D"></path><path d="M190 244l9 27 2 6c-1 1-1 1-2 1h0c-2-4-3-8-4-12l-6-21 1-1z" class="J"></path><path d="M244 345l6 27h1v-1h2v1c-1 1-2 2-3 2 0-1-1-2-2-3 0-2-1-5-1-7l-5-19h2z" class="H"></path><path d="M201 277l12 34h-1l-3-8-6-15c-1-4-3-7-4-10h0c1 0 1 0 2-1z" class="E"></path><path d="M235 321l6 7c1 2 4 4 5 6h-1l-2-1-1 1 1 3h-4c-1-3-3-6-3-9h2l-2-3c-1-1-1-3-1-4z" class="K"></path><path d="M217 374l7 38 1 6c0 1 0 2 1 3h0c-1 0-1-1-1-1 0-1-9-42-9-44h0c1-1 1-1 1-2z" class="E"></path><path d="M349 226c1 0 1-1 1-1 1-2 3-5 5-6h0c-1 3-2 5-4 8v2h-1 0c0 1 0 1-1 2v1h0c-1 0-1 1-1 1h6 2 0c0-1 0-1 1-1 0-1 0-2 1-3h0s0-1 1-1h0l1-1h0c0-1 0-1 1-2s2-3 3-4c0-1 0-1 1-1v-1l1-1h1c-1 1-1 2-2 3l-2 4-3 6c-1 1-1 2-2 2h0c-1 0-1 0-2 1h0c-2 1-3 0-5 0h-1-1c-1 0-2 1-3 2 0-2 2-6 4-7v-2l-1-1z" class="B"></path><path d="M293 294v3c0 1 0 1-1 2l1 1h-1v3h-1v1c0 1 0 3-1 4v1 1 1c-1 1 0 2-1 3v1c0 1-1 2-1 3 0 2-1 4-1 6-1 1-1 3-1 4-1 0 0 1-1 1v1h0v2c1 2-1 5-1 7v1c-1 1 0 2-1 2v-1c-1 0-1-1-1-1 0 1-1 3 0 4v1c-1 0-1 0-1 1s-1 2-1 3c1 1 0 4-1 6-1 1-1 3-2 4l16-65z" class="G"></path><path d="M331 212l1 1c0 1 0 3 1 5 0 9-1 19-3 28v4l-1 1v2 2l-1 1c0 4-2 7-3 10l-1 2h-1c1-3 2-5 2-8l1-1v-1-1c0-1 1-2 1-3v-1c0-1 0-2 1-3v-2l1-1c-1 0-1-1 0-1v-2c1-2 1-4 1-6 1-8 1-17 1-26z" class="D"></path><defs><linearGradient id="E" x1="203.455" y1="344.87" x2="195.705" y2="309.548" xlink:href="#B"><stop offset="0" stop-color="#141311"></stop><stop offset="1" stop-color="#49464c"></stop></linearGradient></defs><path fill="url(#E)" d="M197 309l6 25 1 7c1 2 1 4 2 6v1c-1 0-1-1-1-1v-1c-1 1-1 2-1 2 0-1 0-3-1-4l-5-17s1-1 0-2c-1-5-2-9-4-14l3-2z"></path><path d="M337 296v3 1 1 2h-1v1 1 2c-1 1 0 5-1 6v2h0c1-1 0-1 0-2l1-1v-2-1c0-1 1-2 1-3h0v-2h0c1-1 1-1 1-2v-1-1h-1c1-1 2-1 3-2-2 5-3 9-3 14-1 7-1 15-2 22-2-1-2-2-2-4s-1-4-1-5c0-3 0-8 1-10l1-1h0v-3-1c1-1 1-2 1-3h0l1-5v-3c1-1 1-2 1-3z" class="B"></path><defs><linearGradient id="F" x1="242.195" y1="414.348" x2="257.891" y2="408.661" xlink:href="#B"><stop offset="0" stop-color="#100f0d"></stop><stop offset="1" stop-color="#413e44"></stop></linearGradient></defs><path fill="url(#F)" d="M255 396l-8 32c0 2-1 4-1 6h-1v-2c2-4 2-10 3-14l4-20 1-9c0 2-1 5 0 7l1 1h0c0-1 1-1 1-2v1z"></path><path d="M189 262c-1-1-1-3-2-4s-2-3-3-4c-1-3-3-4-5-6v-1c-1-1-2-2-3-4-2-3-4-5-6-8 2 1 4 3 5 5-2-6-3-11-3-16-1-10-2-18 0-28v6 9c1 6 2 12 2 18 1 3 1 7 2 10v1h0l4 6 4 4c1 2 2 3 3 5 1 1 1 2 2 3 0 1 1 2 1 3l-1 1z" class="C"></path><path d="M126 226c2 0 2 0 2 1h1c2 1 4 4 5 6s2 3 3 4l1 1h0 1l-1-1-2-2c-1-1-1-2-1-2 0-1-1-2-1-3 1-1 1-1 3-1l2 1-1 1c-1-1-1-1-3-1 0 1 0 1 1 2h0 0 1c1 1 2 1 2 2l1 2c1 1 1 2 2 2h0l1 1h1l-1-1c0-1-1-3-1-4-1-3-3-6-4-9h0c5 7 7 15 10 22l6 16c3 5 4 10 5 16 1 2 1 4 2 6-1 0-1 0-1-1-1-1-1-2-1-4-1-1-1-2-1-3s-1-3-1-4v-1h-1c-1-1-1-1-1-2-1-2-1-4-2-5-1-3-3-6-3-9 0-2-2-4-2-6l-2-5c-1-2-1-2-3-2h0c0-2-2-4-3-5h0v3h-1c-1-1-2-3-3-4-2-2-4-3-5-5l-2-2c1 1 2 3 2 4-2-2-3-6-5-8z" class="B"></path><defs><linearGradient id="G" x1="224.57" y1="361.659" x2="211.111" y2="361.327" xlink:href="#B"><stop offset="0" stop-color="#616261"></stop><stop offset="1" stop-color="#817a8b"></stop></linearGradient></defs><path fill="url(#G)" d="M212 335l12 45c2 6 4 13 6 19v1c-1-1-1-3-2-4-6-15-11-30-16-46 0-2 0-3-1-5h0c2-3-1-7 1-10z"></path><path d="M268 379c0 2 0 4-1 5l-3 17v2c-1 1-1 2-1 4v-1h0c1-1 1 0 1-1v-2c1 0 0-1 1-2v-2-1c1-2 0-3 1-4s0-2 1-3 0-2 1-3v-3h1v-1 3c-1 1-1 4-1 5s0 1-1 2c0 1 0 2-1 3 0 2 0 3-1 5 0 2-2 6-1 9h0l-8 36-2 8c0 2 0 3-1 4v-3l2-13 12-60 1-4z" class="I"></path><path d="M329 273c1 3 0 6 0 9 1 0 0 1 0 2l-5 20-1 4c-4 6-5 11-9 16 1-5 3-9 5-14l4-15c2-7 3-15 6-22z" class="F"></path><path d="M210 284h1l14 43 3 9 6 18 3 12c0 1 0 2 1 3v2 2h0l-28-89z" class="M"></path><defs><linearGradient id="H" x1="334.93" y1="184.795" x2="330.252" y2="211.934" xlink:href="#B"><stop offset="0" stop-color="#171615"></stop><stop offset="1" stop-color="#403c44"></stop></linearGradient></defs><path fill="url(#H)" d="M331 212v-9c-1-7-3-14-5-21l17 8c-2 2-8 2-9 5s-1 7-1 10v13c-1-2-1-4-1-5l-1-1z"></path><path d="M164 325h4 0l2 2 1 1 1 1v1c0 1 1 2 1 3 2 2 3 5 5 7 1 5 3 9 4 14l13 41 3 13 4 12c0 2 2 5 2 8-1-2-2-4-2-6l-10-30-9-32-4-12c-1-2-1-5-3-8l-3-4v-1h0c-1-1-3-5-4-5v2-2c-1-2-2-2-3-3l-2-2z" class="D"></path><path d="M190 241c0-1-1-3-1-4h1l5 9 9 21c1 2 3 5 5 6 0 0 1 0 2-1l2 5c-2 1-2 0-3-1h-1l3 6-1 1v1h-1c-3-5-4-10-8-14h0 1c0-1-1-3-2-4v-1l-1-1s0-1-1-1v-2c-1 0-1 0-1-1l-1-1c0-2-1-4-2-6-1-1-1-2-2-3h0v1 1h1c0 1-1 2-1 2 1 1 1 3 2 4v1l1 2c0 1 0 2 1 3v1 1c1 0 1 1 1 2 1 0 0 0 1 1v2l-9-27v-3z" class="C"></path><path fill="#bcb3b9" d="M190 241c0-1-1-3-1-4h1l5 9 9 21c1 2 3 5 5 6 0 0 1 0 2-1l2 5c-2 1-2 0-3-1h-1-1c-7-11-12-23-18-35z"></path><defs><linearGradient id="I" x1="154.069" y1="287.891" x2="131.668" y2="262.988" xlink:href="#B"><stop offset="0" stop-color="#1d1c1b"></stop><stop offset="1" stop-color="#353137"></stop></linearGradient></defs><path fill="url(#I)" d="M129 235c3 4 5 10 7 15s4 9 5 14c0 2 1 3 1 5 1 2 1 4 2 7h1l2 8 3 12h-1c0-2-1-3-3-4 0-3-1-6-2-9-1-2-2-4-3-7l-5-11c-2-5-5-11-6-17-1-1-2-4-1-5v3c1 0 1 0 1 1v1 1l1 2v1c1 2 2 8 4 9 0-2 2-3 3-4h0c0-1 0-1-1-1h0c0-3-2-7-3-9v-1l-2-3c0-1-2-3-2-4v-2l-1-2z"></path><path d="M328 295c1 0 1-1 2-2h0 1v-3l2-2c0 1 0 2-1 3v3c-1 1-2 3-2 4-1 1-1 2-1 4l-1 2c-1 1-1 2-2 3 0 1-1 3-1 4-1 1-2 2-2 4 0 0-1 1-1 2s-1 3-2 4l-3 4c-1 2-4 5-3 9-6 5-8 16-11 24 0 1-1 3-1 3 0-2 1-6 2-8l3-11c1-3 2-7 4-10 0-3 2-5 3-8 4-5 5-10 9-16l1-4c0 1 1 1 1 1h0c1-1 1-1 1-2 1-1 1-2 1-3s0-1 1-1v-2-2h0z" class="J"></path><path d="M314 324c4-5 5-10 9-16-1 4-2 6-3 9-2 6-5 11-9 15 0-3 2-5 3-8z" class="L"></path><defs><linearGradient id="J" x1="226.354" y1="394.154" x2="244.832" y2="383.916" xlink:href="#B"><stop offset="0" stop-color="#161719"></stop><stop offset="1" stop-color="#423d40"></stop></linearGradient></defs><path fill="url(#J)" d="M230 362v-4l2 3 4 15 3 10c2 7 4 14 4 22 0 5-1 10-2 14 0-1-1-3-2-4-1-4-1-9-1-13-1-7-1-15-2-22s-4-14-6-21z"></path><defs><linearGradient id="K" x1="140.607" y1="194.962" x2="169.402" y2="221.775" xlink:href="#B"><stop offset="0" stop-color="#19191a"></stop><stop offset="1" stop-color="#403d43"></stop></linearGradient></defs><path fill="url(#K)" d="M160 259l-1-2v-3c0-1 0-1-1-2 0-2-1-5-1-7-1-3-1-6-2-10-2-13-1-27-1-40-4-2-8-2-11-4l16-7c1 1 0 3 0 4l-2 11c-2 16-2 32 2 48 0 2 1 4 1 6 1 1 0 2 1 3v2l-1 1z"></path><defs><linearGradient id="L" x1="227.221" y1="361.897" x2="245.374" y2="348.288" xlink:href="#B"><stop offset="0" stop-color="#26252f"></stop><stop offset="1" stop-color="#504c4a"></stop></linearGradient></defs><path fill="url(#L)" d="M227 330c0 1 1 1 1 2h0l1-1v-5c0 1 0 2 1 2h0c3 9 5 17 7 25l7 24c0 4 3 10 2 14-1-3-2-6-4-9h0v3 2s1 0 1 1v1 2h-1c-1-1 0-1-1-3h0c1 0 0-1 0-1v-1l-3-11v-2h0v-2-2c-1-1-1-2-1-3l-3-12-6-18-3-9c1 1 2 1 2 3z"></path><path fill="#8e858c" d="M190 261c2 5 5 11 7 17l8 30 7 27c-2 3 1 7-1 10 0-2-1-5-2-6s-1-2-2-4c-1-1-1-3-1-4l-3-12-6-25-5-24c-1-3-1-5-3-8l1-1z"></path><defs><linearGradient id="M" x1="304.063" y1="309.277" x2="294.7" y2="304.945" xlink:href="#B"><stop offset="0" stop-color="#282629"></stop><stop offset="1" stop-color="#464148"></stop></linearGradient></defs><path fill="url(#M)" d="M299 294l2-3h1v1c1 0 1 0 1 1h4l4-4c2-3 5-7 7-10 1-2 2-3 2-4 1-1 1-2 1-3 1 0 1-1 1-1v-1l1-1v-1h1c-1 4-3 8-5 12s-5 8-7 12c-2 2-4 5-7 7-1 2-2 3-3 5s-1 4-2 6l-7 14c0 2-1 4-2 7 0 0-1 2-1 3-1 4-3 9-5 14v-1c1-1 1-2 1-3s0-2 1-2c0-1 1-2 1-3v-1c1-1 1-2 1-3 1-1 0-1-1-2h0l-1 1v1c0 1 0 2-1 2v3h0v1c-1 1 0 2-1 3v1 2h0-1l5-22c1-2 1-4 2-6 0-2 1-3 2-4l3-13c1-3 2-5 3-8z"></path><path d="M290 334v-2-1s0-1 1-1h0c0-4 1-9 3-12l5-12c1-1 2-2 2-4v-1c1-2 2-2 4-2-1 2-2 3-3 5s-1 4-2 6l-7 14c0 2-1 4-2 7 0 0-1 2-1 3z" class="B"></path><path fill="#5c5760" d="M332 294v1c0-1 2-2 2-3 1 0 0-1 1-1v-3c1-1 0-1 0-1 1-2 1-2 1-4h1c0-2 1-3 1-5 1-2 2-5 2-8v1l-2 10v1c1-1 1-2 1-3l1-1c0 2-1 3-1 4h1c0 4-1 7-2 10h0c-1 1-1 3-1 4s0 2-1 3v3l-1 5h0c0 1 0 2-1 3v1 3h0l-1 1v-1-3c-1 1-1 2-1 2-1 2-2 3-2 4-1 2-1 5-1 7h1 0v2 2 1l1 1v2h0-1v-1c-1-2-2-3-4-4-2-2-4-3-6-3h0l-3 1 3-4c1-1 2-3 2-4s1-2 1-2c0-2 1-3 2-4 0-1 1-3 1-4 1-1 1-2 2-3l1-2c0-2 0-3 1-4 0-1 1-3 2-4z"></path><path d="M320 324h1c0-3 2-6 3-9l3-6 3-6c1-2 1-4 2-5l3-6c0 2-1 4-2 6s-1 4-1 5c-1 1-2 4-3 5-1 2-2 5-3 7-1 1-1 2-2 3-1 2-1 3-3 5 1 0 1 0 1 1 3 1 6 2 7 5 1 1 1 1 1 2-1-2-2-3-4-4-2-2-4-3-6-3z" class="L"></path><path d="M335 292h0c1-2 2-6 3-7 0 1 0 4-1 5v2h1c-1 1-1 3-1 4s0 2-1 3v3l-1 5h0c0 1 0 2-1 3v1l-1-1c1-1 1-1 1-2v-4-1l-2 3h-1l1-2v-1c0-1 0-3 1-5s2-4 2-6z" class="D"></path><defs><linearGradient id="N" x1="330.942" y1="324.609" x2="324.803" y2="320.087" xlink:href="#B"><stop offset="0" stop-color="#191718"></stop><stop offset="1" stop-color="#383338"></stop></linearGradient></defs><path fill="url(#N)" d="M326 315c1-2 2-5 3-7 1-1 2-4 3-5v1l-1 2h1l2-3v1 4c0 1 0 1-1 2l1 1v3h0l-1 1v-1-3c-1 1-1 2-1 2-1 2-2 3-2 4-1 2-1 5-1 7h1 0v2 2 1l1 1v2h0-1v-1c0-1 0-1-1-2-1-3-4-4-7-5 0-1 0-1-1-1 2-2 2-3 3-5 1-1 1-2 2-3z"></path><g class="F"><path d="M332 306l2-3v1 4c0 1 0 1-1 2l1 1v3h0l-1 1v-1-4l-3 3c0-2 1-5 2-7z"></path><path d="M326 315c1-1 2-1 2-2s1-1 1-2h1l-2 5h0l-1 1v1c-1 1-1 1-1 2l-1 1-1 1-1 1-1 1c0-1 0-1-1-1 2-2 2-3 3-5 1-1 1-2 2-3z"></path></g><path d="M161 258c1 1 1 2 1 3 1 2 2 5 3 7 0 2 1 4 2 6 3 6 6 12 10 16 0-3-2-5-3-7h1 0c1 3 3 5 4 8l1 1h1c0-2-1-5-1-7 0 0 0-2 1-2v-1c-1 0 0-1 0-1h-1l-2-2h0l-2-2v-1c1 1 3 3 4 3h0l1 1c2 1 9 7 9 8 1 1 1 1 1 2 1 2 2 2 2 4l4 15-3 2c2 5 3 9 4 14 1 1 0 2 0 2v-1h-1c-1-3-2-7-3-10-2-6-5-13-9-19h-1c-3-1-5-2-7-5-1 0-2-1-2-2-3-4-5-7-7-11-1-2-2-3-2-5l-2-3v-1c-1-2-2-3-2-5-1-2-1-4-2-6l1-1z" class="B"></path><path d="M189 298c-1-1-2-2-2-3l-1-2s0-1-1-1v-2c1-1 0-3 0-4s1 0 1-1l1 1c-1 2-1 4-1 6l3 6z" class="F"></path><path d="M177 292h1c2 0 4 1 6 1h1c0 1 1 1 1 1v1c0 1 0 1-1 2h-1c-3-1-5-2-7-5z" class="D"></path><path d="M187 286c3 3 4 5 6 8l4 15-3 2-5-13-3-6c0-2 0-4 1-6z" class="L"></path><path d="M139 241h1v-3h0c1 1 3 3 3 5h0c2 0 2 0 3 2l2 5c0 2 2 4 2 6 0 3 2 6 3 9 1 1 1 3 2 5 0 1 0 1 1 2h1v1c0 1 1 3 1 4s0 2 1 3c0 2 0 3 1 4 0 1 0 1 1 1 2 11 5 22 9 32l5 14c1 3 3 6 3 9-2-2-3-5-5-7 0-1-1-2-1-3v-1l-1-1-1-1c0-1-1-2-1-3s-1-1-1-2l-1-2-2-7-2-6h0c-1-1-1-2-1-3l-2-5h0c0-3-1-5-1-8-1-4-3-8-4-12-5-13-11-25-16-38z" class="F"></path><defs><linearGradient id="O" x1="357.522" y1="262.699" x2="326.746" y2="265.719" xlink:href="#B"><stop offset="0" stop-color="#343335"></stop><stop offset="1" stop-color="#756f7c"></stop></linearGradient></defs><path fill="url(#O)" d="M349 226l1 1v2c-2 1-4 5-4 7 1-1 2-2 3-2h1 1c2 0 3 1 5 0h0c1-1 1-1 2-1h0v2c-1 1-1 2-2 3-1 2-1 3-2 4 0 1-1 2-1 3s-1 3-2 4c0 1-1 2-1 3l-4 13-4 11v1c-1 1-1 4-2 5h-1c0-1 1-2 1-4l-1 1c0 1 0 2-1 3v-1l2-10v-1c0 3-1 6-2 8 0 2-1 3-1 5h-1c0 2 0 2-1 4 0 0 1 0 0 1v3c-1 0 0 1-1 1 0 1-2 2-2 3v-1-3c1-1 1-2 1-3l-2 2v3h-1 0c-1 1-1 2-2 2h0v2 2c-1 0-1 0-1 1s0 2-1 3c0 1 0 1-1 2h0s-1 0-1-1l5-20c0-1 1-2 0-2 0-3 1-6 0-9l8-21c0-1 0-2 1-3 0-2 1-3 2-5 1-3 3-7 5-9 1-3 3-6 4-9z"></path><path d="M340 254l1-1c0 2-4 14-5 16v1c0-1 0-2-1-3 0-1 1-2 1-3 1-3 3-7 4-10z" class="J"></path><path d="M335 267c1 1 1 2 1 3-2 3-2 7-4 11s-3 9-4 14h0v2 2c-1 0-1 0-1 1s0 2-1 3c0 1 0 1-1 2h0s-1 0-1-1l5-20 3-9 3-8z" class="E"></path><path d="M339 250v-1c1-1 1-1 1-2l1-1h0c0 3-1 5-1 8-1 3-3 7-4 10 0 1-1 2-1 3l-3 8-3 9c0-1 1-2 0-2 0-3 1-6 0-9l8-21c0-1 0-2 1-3l1 1z" class="F"></path><path d="M329 273l8-21c0-1 0-2 1-3l1 1c-1 2-2 5-3 7 0 1-1 2-1 4-1 1-1 3-2 4-1 2 0 4 0 6-1 1-1 2-1 3v1l-3 9c0-1 1-2 0-2 0-3 1-6 0-9z" class="D"></path><path d="M129 235c0-1-1-2-1-3-1-1-2-3-3-4l-8-16v-1c1 0 1 1 1 2l4 6c2 3 3 4 4 7 2 2 3 6 5 8 0-1-1-3-2-4l2 2c1 2 3 3 5 5 1 1 2 3 3 4 5 13 11 25 16 38 1 4 3 8 4 12 0 3 1 5 1 8h0l2 5c0 1 0 2 1 3h0l2 6 2 7 1 2c0 1 1 1 1 2s1 2 1 3l-2-2h0-4c-3 2-4 3-6 5 0-3 5-6 7-7v-1c-3-1-5-7-7-10-1-2-3-3-4-5 0 5 1 11 0 16-2-9-1-18-4-27l-3-12-2-8h-1c-1-3-1-5-2-7 0-2-1-3-1-5-1-5-3-9-5-14s-4-11-7-15z" class="E"></path><path fill="#5c5760" d="M154 307c-1-1-1-1-1-2h0l1 1h0 0c2 1 2 2 3 3l1 1s1 0 1 1v1 1l3 5v1l3 3c1-1 0-3-1-4 0-2-2-5-3-8h1c1 2 1 4 2 5 0 2 1 4 2 6l1 1h1c0 1 1 1 1 2s1 2 1 3l-2-2h0-4c-3 2-4 3-6 5 0-3 5-6 7-7v-1c-3-1-5-7-7-10-1-2-3-3-4-5z"></path><path d="M199 123c0 4 1 8 0 12 0 8-3 17-5 25-3 7-5 14-6 22-1 4-1 10 0 15 1 1 2 3 3 4 0 1 1 2 1 2l-1 1v1c0 13 6 27 13 39-1 1-1 4 0 5v3h0v3c1 2 1 4 2 5l-1 3c0 1 0 3-1 4l-9-21-5-9h-1c0 1 1 3 1 4v3l-1 1c-4-14-7-29-9-43 0-4-1-8 0-12 0-9 3-17 6-24 6-13 10-25 12-38h1c1-1 0-4 0-5z" class="N"></path><path d="M191 229h1c1 1 1 1 1 2 1 3 4 5 4 7v1c-1-1-2-2-2-4h0l-1-1h0c0 2 1 3 2 5l-1-1c-1-3-4-6-4-9zm-7-48h0l1 1c0 4-1 8-1 12v10c-2-6-2-13-1-19 0-1 0-2 1-4z" class="H"></path><path d="M196 245h0l-3-6c-1-2-3-3-3-5h1c1 1 1 2 2 3l9 15c0 1 1 3 1 4h1v-1c1 2 1 4 2 5l-1 3c0-4-4-9-6-12l-3-6z" class="K"></path><path d="M195 246v-1h0 1l3 6c2 3 6 8 6 12 0 1 0 3-1 4l-9-21z" class="I"></path><path d="M288 272c0-1 0-2 1-2 1-2 2-6 2-9l2-10c1-4 3-7 4-11 0-1-1-2-2-3h1 0c1 2 2 3 1 5-1 3-2 5-3 8l-3 14c0 2-1 4-1 6v-1h1v-3-1c1-1 1-3 1-3 1-1 0-2 1-3v-2c1 0 0-1 1-2 0-1-1-2 0-3s1-2 1-3l1-1v-1c1-1 1-2 1-2l3-5c1 0 1-1 1-1 1 0 1 0 1-1h0c-1-1-1-1-1-2h1c0-1 1-2 1-2 0 1 0 2-1 2v3c-2 2-4 5-5 9-1 1-1 3-2 4s0 3-1 4v1l-3 12c2 1 4 1 5 1v1c1 1 2 0 2 2-1 1 0 1-1 2v2c0 4-2 8-3 13 0 1-1 3-1 4l-16 65-3 12-3 14-3 9c-1 5-2 11-4 17-1-3 1-7 1-9 1-2 1-3 1-5 1-1 1-2 1-3 1-1 1-1 1-2s0-4 1-5v-3 1h-1v3c-1 1 0 2-1 3s0 2-1 3 0 2-1 4v1 2c-1 1 0 2-1 2v2c0 1 0 0-1 1h0v1c0-2 0-3 1-4v-2l3-17c1-1 1-3 1-5l-1 4h0l-1-1 1-3h0v-1-1s0-1 1-2h0 0v-2-1 1l-1 1h0-1v2h0c-1 0-2 2-3 3v1c-1 1-1 1-1 2v2c-1 0-1 0-1 1v1c-1 1-1 2-2 3 0 1-1 2-1 4 0 1-1 1-2 2l-1 1v-1c0 1-1 1-1 2h0l-1-1c-1-2 0-5 0-7 0-3 1-7 2-10h0c1-2 1-5 2-7 1-5 3-9 5-14-1-1 1-5 1-6l8-21 3-11 6-21v-1c1-1 0-2 0-4 0-1 0-2 1-3l3-8c0-1 0-1 1-2 0-2 1-5 2-7 0 0 1-1 1-2z" class="K"></path><path d="M280 308c1 0 2 0 3 1l-2 9c-1 0-1-1 0-2h0c-1-2-1-4-1-7v-1z" class="C"></path><path d="M282 302c2-6 3-12 5-18v1l-4 24c-1-1-2-1-3-1l2-6z" class="B"></path><path fill="#ac3e4a" d="M288 272c0 4 0 8-1 12v1-1c-2 6-3 12-5 18 0-2 0-3-1-4l-1 1v-1c1-1 0-2 0-4 0-1 0-2 1-3l3-8c0-1 0-1 1-2 0-2 1-5 2-7 0 0 1-1 1-2z"></path><path fill="#75293f" d="M284 283l1 1c0 2-1 3-1 4-1 3-3 7-3 10l-1 1v-1c1-1 0-2 0-4 0-1 0-2 1-3l3-8z"></path><path d="M277 328c1-2 1-5 3-7v1c-2 11-5 22-7 33l-2 11-3 9v4l-1 4h0l-1-1 1-3h0v-1-1s0-1 1-2h0 0v-2-1 1l-1 1h0-1v2h0c-1 0-2 2-3 3v1c-1 1-1 1-1 2v2c-1 0-1 0-1 1v1c-1 1-1 2-2 3 1-5 3-9 5-13 1-2 1-3 2-4 2-4 2-8 3-12 3-8 5-16 6-25 1-2 1-6 2-7z" class="G"></path><defs><linearGradient id="P" x1="275.382" y1="334.775" x2="267.98" y2="331.889" xlink:href="#B"><stop offset="0" stop-color="#4e4953"></stop><stop offset="1" stop-color="#79737a"></stop></linearGradient></defs><path fill="url(#P)" d="M281 298c1 1 1 2 1 4l-2 6v1c0 3 0 5 1 7h0c-1 1-1 2 0 2-1 1-1 3-1 4v-1c-2 2-2 5-3 7-1 1-1 5-2 7-1 9-3 17-6 25-1 4-1 8-3 12-1 1-1 2-2 4-2 4-4 8-5 13 0 1-1 2-1 4 0 1-1 1-2 2l-1 1v-1c0 1-1 1-1 2h0l-1-1c-1-2 0-5 0-7 0-3 1-7 2-10h0c1-2 1-5 2-7 1-5 3-9 5-14-1-1 1-5 1-6l8-21 3-11 6-21 1-1z"></path><path d="M280 309c0 3 0 5 1 7h0c-1 1-1 2 0 2-1 1-1 3-1 4v-1c-2 2-2 5-3 7 0-7 2-13 3-19z" class="B"></path><path d="M265 362c2-4 5-9 6-14 1-4 2-10 4-13-1 9-3 17-6 25-1 1-2 2-2 3l-1 1-2 2v2s-1 0-1 1h0v-2c1 0 1-1 1-2l1-3z" class="D"></path><defs><linearGradient id="Q" x1="262.376" y1="383.075" x2="256.802" y2="379.375" xlink:href="#B"><stop offset="0" stop-color="#1f1c22"></stop><stop offset="1" stop-color="#403d41"></stop></linearGradient></defs><path fill="url(#Q)" d="M262 358v1 1l-2 13c1-1 1-1 1-2 1-2 1-4 1-5 2-1 2-2 3-4l-1 3c0 1 0 2-1 2v2h0c0-1 1-1 1-1v-2l2-2 1-1c0-1 1-2 2-3-1 4-1 8-3 12-1 1-1 2-2 4-2 4-4 8-5 13 0 1-1 2-1 4 0 1-1 1-2 2l-1 1v-1c0 1-1 1-1 2h0l-1-1c-1-2 0-5 0-7 0-3 1-7 2-10h0c1-2 1-5 2-7 1-5 3-9 5-14z"></path><path d="M262 358v1 1l-2 13-4 18-1 4c0 1-1 1-1 2h0l-1-1c-1-2 0-5 0-7 0-3 1-7 2-10h0c1-2 1-5 2-7 1-5 3-9 5-14z" class="E"></path><path d="M255 379h0c0 4-1 8 0 11l1 1-1 4c0 1-1 1-1 2h0l-1-1c-1-2 0-5 0-7 0-3 1-7 2-10z" class="L"></path><path d="M288 132v-4c1 0 1 1 1 1 2 10 6 20 10 29 4 11 9 22 11 34 1 5 2 11 1 16 1 6 0 11-1 16v4c-2 1-2 2-3 3-2 4-2 7-2 11 0 2-2 3-1 5 0 1 1 1 1 1l-5 20c-1 3-1 6-3 9v-2c1-1 0-1 1-2 0-2-1-1-2-2v-1c-1 0-3 0-5-1l3-12v-1c1-1 0-3 1-4s1-3 2-4c1-4 3-7 5-9v-3c1 0 1-1 1-2 0 0-1 1-1 2h-1c0 1 0 1 1 2h0c0 1 0 1-1 1 0 0 0 1-1 1l-3 5s0 1-1 2v1l-1 1c0 1 0 2-1 3s0 2 0 3c-1 1 0 2-1 2v2c-1 1 0 2-1 3 0 0 0 2-1 3v1 3h-1v1c0-2 1-4 1-6l3-14c1-3 2-5 3-8 1-2 0-3-1-5h0-1c1 1 2 2 2 3-1 4-3 7-4 11l-2 10c0 3-1 7-2 9-1 0-1 1-1 2s-1 2-1 2c-1 2-2 5-2 7-1 1-1 1-1 2l-3 8c-1 1-1 2-1 3 0 2 1 3 0 4v1l-6 21-3 11-8 21c0 1-2 5-1 6-2 5-4 9-5 14-1 2-1 5-2 7h0c0-1-1-5-1-6 1-1 1-2 1-4 1-1 1-3 2-4h0 0v-2c0-2 1-3 0-4l-4 12h-2l4-12c7-22 15-44 19-66v-4c-1-1-3 0-5 0 5-8 10-15 14-23 5-11 8-22 11-33 3-14 7-28 7-42 0-5 0-9-1-14-4-15-13-29-12-45z" class="I"></path><path d="M274 289l2 1c1 1 1 1 0 1 0 1-1 1-2 2v-4zm22-33h0l1 1c0 1 0 1-1 2v6h-1-1c0-3 1-6 2-9z" class="K"></path><path d="M311 208c1 6 0 11-1 16v4c-2 1-2 2-3 3 0-2 0-4 1-6 0-3-1-9 1-12h1v1h1v-6z" class="N"></path><path d="M280 294c0 2 1 3 0 4v1l-6 21c-1 1-2 1-2 3l-1 1s0 1-1 1c0 1-1 2-1 4l-2 2h0c0 3-1 5-2 8 0 1-1 2-1 3s-1 1-2 2v-1l18-49z" class="C"></path><path d="M262 343v1c1-1 2-1 2-2s1-2 1-3c1-3 2-5 2-8h0l2-2c0-2 1-3 1-4 1 0 1-1 1-1l1-1c0-2 1-2 2-3l-3 11-8 21c0 1-2 5-1 6-2 5-4 9-5 14-1 2-1 5-2 7h0c0-1-1-5-1-6 1-1 1-2 1-4 1-1 1-3 2-4h0 0v-2c0-2 1-3 0-4l5-16z" class="B"></path></svg>