<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="189 130 669 800"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#a0080f}.C{fill:#8b090d}.D{fill:#5a0a0c}.E{fill:#181517}.F{fill:#640304}.G{fill:#9e0a12}.H{fill:#700305}.I{fill:#910d12}.J{fill:#aea4a3}.K{fill:#270605}.L{fill:#42080b}.M{fill:#570203}.N{fill:#232224}.O{fill:#a80d15}.P{fill:#c0252e}.Q{fill:#450304}.R{fill:#3f3c40}.S{fill:#be242c}.T{fill:#cac3c2}.U{fill:#7d0308}.V{fill:#3e3c41}.W{fill:#636065}.X{fill:#e9e7e8}.Y{fill:#c42f3b}.Z{fill:#989092}.a{fill:#555156}.b{fill:#7b797d}.c{fill:#060506}.d{fill:#deddde}.e{fill:#690408}.f{fill:#302e31}.g{fill:#2f0101}.h{fill:#8f8c8f}.i{fill:#c30c12}.j{fill:#987877}.k{fill:#a18a89}.l{fill:#6e3536}.m{fill:#7f4849}.n{fill:#0a0809}.o{fill:#8f6768}.p{fill:#373438}.q{fill:#fffffe}.r{fill:#de5f6b}.s{fill:#674145}.t{fill:#5d4a4d}.u{fill:#765152}</style><path d="M591 264c1 1 1 2 1 3 1 0 2 1 2 1l1 2v1 4c-1 1-1 1-3 0l-1-11z" class="G"></path><path d="M535 916l26 1c-5 1-9 1-14 1l-12 1v-3z" class="J"></path><path d="M258 176c1 2 1 2 1 5v2c-1 3-3 6-6 8-3 1-6 2-10 3h-1c3-2 6-3 9-5 5-4 6-8 7-13z" class="W"></path><path d="M539 204l-16-3c-2-1-4-1-6-2l1-1c3 1 6 1 9 2h18l-5 1 2 2c-1 0-2 1-3 1z" class="M"></path><path d="M478 221h1c3 0 5-3 8-5-2 3-3 6-2 9v2h-3c-2-1-4 0-6 1l-2-1h0c0-4 1-2 3-4 1 0 1-1 1-2z" class="B"></path><path d="M768 176h0c-1-5 2-14 5-17l4-5c0 2-1 4-1 5h-1c-2 2-4 9-4 13v5 3l-1 2-2-6z" class="J"></path><path d="M258 176c0-4 1-8 0-12s-2-7-4-10c4 4 7 8 9 14h0c1 3 1 7 1 10l-1-3v-5-1-1l-1-1h-1l1 3c0 2 0 4-1 5v3c-1 1-1 2-1 2v1c0 1 0 1-1 2v-2c0-3 0-3-1-5z" class="b"></path><defs><linearGradient id="A" x1="579.642" y1="240.35" x2="566.481" y2="238.345" xlink:href="#B"><stop offset="0" stop-color="#9b0106"></stop><stop offset="1" stop-color="#c41012"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M580 239c-2 0-5 1-8 1-3 2-7 4-10 6 2-3 5-6 6-10 0-1 1-2 1-3l2 1 1-2v1l-1 3 1 1 5-1h4v3h-1z"></path><defs><linearGradient id="C" x1="340.449" y1="189.446" x2="354.551" y2="186.054" xlink:href="#B"><stop offset="0" stop-color="#8a8888"></stop><stop offset="1" stop-color="#a8a6ac"></stop></linearGradient></defs><path fill="url(#C)" d="M326 176c9 7 19 11 31 14l-1 1h1 0c-2 0-3 0-5 1-3-1-7-2-10-4-4-2-13-6-16-10v-2z"></path><path d="M771 180v-3-5c0-4 2-11 4-13-2 7-3 18 0 24 2 5 9 9 14 10l-1 1-11-4c-4-2-5-6-6-10z" class="R"></path><path d="M652 594c0-1 1-1 1-2 4 17 6 35-1 52-1-2-1-2 0-4l2-7c2-13 1-27-2-39z" class="c"></path><path d="M463 231l8-1-2 2h0c-3 3-5 6-7 10-3 6-3 13-3 20-1-6-1-13-1-19 0-2 0-4 1-5h0l3-2c0-2 1-3 1-5z" class="i"></path><path d="M463 231l8-1-2 2h0c-3 1-5 3-7 4 0-2 1-3 1-5z" class="X"></path><path d="M719 160h1l1-1v2 1c-1 1-1 1-1 2l-1 2c-1 1-1 3-2 4l-1 2v1h-1v1c0 1-1 2-2 3h-1v-1c-3 2-4 4-6 5l-1 1c-2 1-4 2-5 3h-1c1-1 1-2 1-3 2-1 2-1 3-3 7-5 12-11 16-19z" class="E"></path><path d="M542 203c7 2 14 5 21 10 1 1 3 2 4 4 0 0 1 1 1 2 3 4 5 9 4 14v-1c-1-2-1-5-2-7-6-12-20-17-31-21 1 0 2-1 3-1z" class="C"></path><path d="M643 605h1c0 1 1 2 1 3 1 4 3 7 3 11 1 3 1 5 1 7 1 2 0 7 1 8v-1h1l1-1c1-2 0-4 1-7 0-1 0-4-1-5 0-2 0-3 1-5 1 7 1 16-1 23v2c-1 2-1 2 0 4-2 5-6 10-9 14 1-3 3-6 4-9 4-15 1-30-4-44z" class="E"></path><path d="M610 302v-1l1-1v-1c1-3 0-4-2-6v-1h1c2 0 5 1 6 2-1 3-2 4-1 7v1c-1 4-2 7-2 12s1 10 1 15c-2-7-4-14-4-22v-5z" class="S"></path><path d="M535 919l-49-2h-1l50-1v3z" class="j"></path><path d="M315 164c4 5 7 8 11 12v2c3 4 12 8 16 10 3 2 7 3 10 4h0-5-1c-2-1-7-2-9-4-6-3-14-9-18-14-2-3-3-5-5-8h-1v-2c1 1 1 1 2 0h0zm53 196l1-3c0 1 1 2 1 2 1 5 1 13 0 19h-1c-2 0-4 1-5 2-1 2-4 3-5 4l-2-1c7-7 9-13 11-23z" class="E"></path><defs><linearGradient id="D" x1="539.204" y1="204.858" x2="559.042" y2="187.932" xlink:href="#B"><stop offset="0" stop-color="#6e3228"></stop><stop offset="1" stop-color="#9c6f78"></stop></linearGradient></defs><path fill="url(#D)" d="M575 198h-55c20-4 41-4 62-3h-1 0c-5 0-10 1-14 1h-1c3 1 6 0 9 0 1 1 1 0 3 0 1 0 2 1 4 1l1-1c2 0 6 1 8 1h-15c-4 0-7 0-10 1h9z"></path><path d="M567 217l3-1 5 4 2 2h0c0 4 6 9 8 13 0 1 0 2 1 4s1 6 1 9l-2-3c-1-3-2-5-5-6h1v-3h-4l-5 1-1-1 1-3c1-5-1-10-4-14 0-1-1-2-1-2z" class="I"></path><path d="M568 219c4 4 12 10 12 16v1h-3l-5 1-1-1 1-3c1-5-1-10-4-14z" class="q"></path><defs><linearGradient id="E" x1="273.141" y1="254.691" x2="264.562" y2="263.11" xlink:href="#B"><stop offset="0" stop-color="#b6adac"></stop><stop offset="1" stop-color="#d8d8d8"></stop></linearGradient></defs><path fill="url(#E)" d="M267 246c5 5 10 13 11 22h-3 0-1v-2-1h-1v1c-2-2-3-1-4-2h-1c-1 0-1 0-2-1h0l-2-1-2-1v-1l-1-1c1-1 1-1 1-2h-1-2 0v-1h-1v1h-2c2-2 6-4 7-6 2-2 3-4 4-5z"></path><path d="M385 628c-1 5-1 10 0 15 1 6 3 11 5 16-6-6-11-16-12-24-4-19-1-45 10-62v1c-1 4-3 7-4 11-3 8-4 17-6 25 0 4-1 14 1 17l1 1v5 2l1 1c1-2 0-4 1-6 0-1 0-3 1-4v-2l1-1v2c-1 1 0 1 0 2l1 1z" class="N"></path><defs><linearGradient id="F" x1="759.575" y1="258.205" x2="766.686" y2="264.581" xlink:href="#B"><stop offset="0" stop-color="#bcb5b5"></stop><stop offset="1" stop-color="#d5d5d6"></stop></linearGradient></defs><path fill="url(#F)" d="M764 246l1 1c1 2 3 5 4 6s2 2 3 4h-3c0 1 0 1 1 2v2c0 1-2 3-3 4h-1c-1 0-2 1-3 1l-1 1c-2 0-2 0-3 2l-1-1v1h0l-1 1 1 1c-2 1-3 0-4 0h0c0-4 0-7 1-10h0c3-6 5-11 9-15z"></path><path d="M764 246l1 1c-1 1-2 2-2 3l-1 1c-1 4 0 8-5 11h0v1h-1 0c0-1 0-1-1-2h0c3-6 5-11 9-15z" class="J"></path><path d="M700 182c0 1 0 2-1 3h1c-4 3-8 5-12 6-23 9-48 9-72 8l-41-1h-9c3-1 6-1 10-1h15 37c17 1 35 0 51-5 7-2 15-6 21-10z" class="N"></path><path d="M789 193c4 1 9 3 14 5 2 0 3 1 5 2 5 2 8 7 11 12 8 12 15 24 20 38 2 3 3 7 3 11-3-5-4-11-6-16-5-10-10-18-17-27-8-9-14-16-25-20l-3-3-3-1 1-1z" class="J"></path><path d="M803 198c2 0 3 1 5 2v5c-3-2-5-4-5-7z" class="h"></path><defs><linearGradient id="G" x1="827.106" y1="215.313" x2="808.311" y2="210.164" xlink:href="#B"><stop offset="0" stop-color="#403c3c"></stop><stop offset="1" stop-color="#787980"></stop></linearGradient></defs><path fill="url(#G)" d="M808 200c5 2 8 7 11 12 8 12 15 24 20 38-1-1-4-8-5-10-3-7-7-13-12-19-4-6-9-12-14-16v-5z"></path><path d="M593 287c5 0 8 1 12 1 6 0 12 4 18 4 0 0 0 1 1 1 3 0 4 2 6 3l2 2c2 3 3 5 4 8l1 3v1 5 1h-1c0 1-1 2-1 3v3-1-4-1-1c-1-2-2-5-4-6h0c-1 0-2-1-3-1v-1c-3-4-6-8-10-8-2 0-2 1-3 3v-1c-1-3 0-4 1-7-1-1-4-2-6-2h-1v1c2 2 3 3 2 6v1l-1 1v1c0-2 0-4-1-5-3-8-9-8-16-10z" class="F"></path><path d="M494 427c1 3 3 5 3 8 1 5 1 10 1 15 1 8 3 16 4 23l3 9v2h1v-1c-1-1-1-3 0-4h0c0 2 1 5 2 7l-1 1c0 1 0 3 1 4v2c0 1 0 1 1 2s0 3 1 5v3c1 1 1 2 2 4v2l1 2v3 1c0 1 0 1-1 2 0 1 0 1 1 2h0c0 1 0 2 1 3v2l1 1 1 1h-1c0 1 0 1 1 2 0 1 1 3 1 4v1l1 1v2l1 1h0v2l1-2c2 2 0 4 1 7l1 1v2l1 1v2l1 1v2 1c1 1 1 2 2 3v2 1l2 3v2c1 1 1 2 1 4-3-6-5-13-7-20l-13-38-1-4-3-12c-1-13-6-25-8-37 0-5-1-9-1-13 0-5 0-9-2-14h0v-1l-1 1h0l1-4z" class="X"></path><path d="M259 183c1-1 1-1 1-2v-1s0-1 1-2v-3c1-1 1-3 1-5l-1-3h1l1 1v1 1 5c0 5-3 11-7 15-6 5-14 6-21 9-3 1-6 3-8 5-6 4-11 11-16 16-5 7-9 13-13 20-1 3-3 6-4 9-1 2-1 4-3 6 0-1 0-2 1-3 0-2 0-3 1-5l1-2c4-12 11-23 18-33 3-3 5-7 8-10 1-1 3-2 5-3 5-2 11-4 17-5h1c4-1 7-2 10-3 3-2 5-5 6-8z" class="J"></path><defs><linearGradient id="H" x1="212.048" y1="223.932" x2="218.283" y2="199.782" xlink:href="#B"><stop offset="0" stop-color="#4d4f55"></stop><stop offset="1" stop-color="#898485"></stop></linearGradient></defs><path fill="url(#H)" d="M193 247l1-2c4-12 11-23 18-33 3-3 5-7 8-10 1-1 3-2 5-3l2 2c-1 2-4 5-6 6-6 7-11 13-16 20-5 6-8 13-11 20h-1z"></path><path d="M484 201l5-1 8 1v1s-1 0 0 1l2 1c-8 3-17 8-21 15v2c0 1 0 2-1 2-2 2-3 0-3 4h0l2 1c-2 0-4 1-5 2l-8 1 1-2c-1 1-2 1-3 2l-1-1 5-11h0c1-3 3-5 4-7h1v1c1 0 2 1 4-1l3-2c1-1 2-1 2-2 2-1 3-2 4-2l4-1 1-1h2c2-1 2-1 3-2h1v-1h-9-1z" class="O"></path><path d="M484 201l5-1 8 1v1s-1 0 0 1c-16 4-25 12-33 26-1 1-2 1-3 2l-1-1 5-11h0c1-3 3-5 4-7h1v1c1 0 2 1 4-1l3-2c1-1 2-1 2-2 2-1 3-2 4-2l4-1 1-1h2c2-1 2-1 3-2h1v-1h-9-1z" class="K"></path><defs><linearGradient id="I" x1="410.964" y1="190.612" x2="410.254" y2="198.533" xlink:href="#B"><stop offset="0" stop-color="#a79696"></stop><stop offset="1" stop-color="#bab8b8"></stop></linearGradient></defs><path fill="url(#I)" d="M357 190c12 2 25 3 38 4l61 1h19 15c-6 1-15-1-20 1-2 1-4 1-6 1-6 0-13-1-18 0h-63c-8-3-17-4-26-6h0-1l1-1z"></path><defs><linearGradient id="J" x1="625.406" y1="613.016" x2="634.808" y2="626.955" xlink:href="#B"><stop offset="0" stop-color="#ae9f9d"></stop><stop offset="1" stop-color="#ded9d5"></stop></linearGradient></defs><path fill="url(#J)" d="M630 607v-4h0c1-1 2-2 3-2 0-1 0-1 1-1h1c0-1 1-1 2-2l4 13c0 2 1 5 1 6l-1 1c0-1-1-2-1-3l-1 2c0 1 0 1-1 2v2l-1 1v1 1c-1 2-2 3-3 4h-1v1h-2c0 1 1 1 1 2h0c1 2 2 4 3 5v1l1 1v1c-1-1-2-1-2-3-2-2-4-5-6-7l-1-1v-1c0-2-2-2-3-3h0l-1-1v-1l-6-2h0c1-1 1-1 2-1v-1-1h-4l1-1c4-1 7 0 11-2 2-2 3-3 4-6l-1-1z"></path><path d="M503 499l2 5h1c1 1 1 1 1 2l1 1 1 4 13 38c2 7 4 14 7 20l4 14-3-3c-1-4-3-8-4-12 0-1 0-2-1-3-2-1-3-3-5-4l-1-4c-1-2-2-4-3-5v-3l-2-1c-1-2-1-4-2-7v-4c0-4-1-6-3-9-1-1-1-1-2-1v1l-1 1v-4c-1-3 0-7-1-10-1-5-2-11-2-16z" class="i"></path><path d="M503 499l2 5h1c1 1 1 1 1 2l1 1 1 4c0 2-1 5 0 6v1c1 2 2 5 2 7 3 10 6 21 8 32-1-2-2-4-3-5v-3l-2-1c-1-2-1-4-2-7v-4c0-4-1-6-3-9-1-1-1-1-2-1v1l-1 1v-4c-1-3 0-7-1-10-1-5-2-11-2-16z" class="B"></path><defs><linearGradient id="K" x1="626.966" y1="190.99" x2="627.39" y2="197.368" xlink:href="#B"><stop offset="0" stop-color="#aa9c9b"></stop><stop offset="1" stop-color="#b8b6ba"></stop></linearGradient></defs><path fill="url(#K)" d="M582 195l23-1 42-1c15-1 30-3 45-8 3-2 7-4 11-6-1 2-1 2-3 3-6 4-14 8-21 10-16 5-34 6-51 5h-37c-2 0-6-1-8-1l-1 1c-2 0-3-1-4-1-2 0-2 1-3 0-3 0-6 1-9 0h1c4 0 9-1 14-1h0 1z"></path><path d="M628 197c11-2 22-2 32-3h0l-2 1h-2c-1 0-3 1-4 1h4c1-1 3-1 4-1h2c1 0 0 0 1-1h3 3l-1-1h0l11-1c-16 5-34 6-51 5z" class="J"></path><defs><linearGradient id="L" x1="704.99" y1="336.871" x2="710.407" y2="358.309" xlink:href="#B"><stop offset="0" stop-color="#ab9b9a"></stop><stop offset="1" stop-color="#dbd6d3"></stop></linearGradient></defs><path fill="url(#L)" d="M694 348c1-1 1-1 1-2 3-2 4-7 7-9 4-2 9 3 12 5 2 1 4 3 7 3v1c4 7 9 16 10 24h-2c1-1 1-1 0-1 0-2-1-4-1-6l-1-1v-1c-1-2-1-4-2-6l-1 1c-2 0-3 0-5-1-1 0-3 0-4-1h-2l-1-1-1 1-1 1c-1 1-2 2-4 3h-1c1 1 1 1 2 1v1h0l1 1 1 2 3 3 2 2v1c1 1 2 2 2 4-1 0-1-1-1-2l-1-1c-1 0-2-1-2-2-2-3-4-5-6-8h-1c-1 0-1-1-2-1l-2-2c0-1-1-2-2-2v-1c-1 0-1 0-2-1-1 0-2-1-2-2h-5c-1 1-2 2-2 3-1 2-1 3-2 5 0 1 0 1-1 2 0-2 1-6 2-8v-1c0-1 1-1 2-2h3 2c0 1 1 1 2 1h0 0c-1-1-1-1-2-1h0v-2z"></path><defs><linearGradient id="M" x1="412.486" y1="611.148" x2="398.278" y2="628.442" xlink:href="#B"><stop offset="0" stop-color="#ad9b98"></stop><stop offset="1" stop-color="#d8d8d8"></stop></linearGradient></defs><path fill="url(#M)" d="M401 599h0l1 1c1 0 1 0 2 1l2 2c0 4 1 6 2 10 1 1 4 2 6 3 2 0 5-2 7-1l2 2 2 1v1l2 1h1v1c-1 0-3 1-4 1-5 2-9 3-13 5-2 1-6 4-8 7h0c-1 1-2 2-2 4l-2 2v1l-1 1c-1 2-1 2-2 3h-1c1-1 1-1 1-2l3-6c1-1 0-1 1-1 0-1 1-1 1-2v-1l1-1c-1-1-2-2-3-4h0v-2c-1-1-1-1-1-2-2-3 2-10-1-13 0-4 2-9 4-12z"></path><defs><linearGradient id="N" x1="318.142" y1="337.815" x2="322.981" y2="356.329" xlink:href="#B"><stop offset="0" stop-color="#a39392"></stop><stop offset="1" stop-color="#dcd7d4"></stop></linearGradient></defs><path fill="url(#N)" d="M310 342c6-1 13-6 19-9 2-2 4-2 7-3l1 4c3 8 7 17 8 25-2-2-2-4-3-7 0-1-1-2-1-4h-1 0-1c-2 0-3-1-4 0h-1 0-1c-1 1-1 1-2 1l-1 1c-1 0-1 0-2 1l-3 2c-1 2-3 3-4 4l-1 2c-1 1-1 2-2 3-1 3-2 5-3 7h0c-1 1-1 1-2 1h0c0-1 0-1 1-2v-1l4-8c-1-1-1-1-2-1l-1 1c-2 0-5-1-6 0h-5c0 1-1 1-1 2s-1 2 0 3c-1 1-1 1-1 2v1s-1 1-1 2v1h0v2 1h-1v-5c2-8 5-15 8-23l2-3z"></path><path d="M310 342c6-1 13-6 19-9 2-2 4-2 7-3l1 4c-3 0-6 0-9 1h-1c-6 2-11 12-19 10l2-3z" class="k"></path><path d="M685 361c1-1 1-1 1-2 1-2 1-3 2-5 0-1 1-2 2-3h5c0 1 1 2 2 2 1 1 1 1 2 1v1c1 0 2 1 2 2-1-1-2-2-4-2l-4 4v1l-2 2-4 10c-1 0-1 1-1 1l-1 4c-1 1 0 1-1 2v2l-1 1-3 10s-1 1-1 2v1c-1 1-1 1-1 2v1s-1 1-1 2l-1 4c0 1-1 2-1 2v2s-1 1-1 2l-2 7c0 1-1 2-1 2l-2 6v2l-1 1v1l-1 3v2l-1 3s-1 1-1 2v1c0 1-1 2-1 2v2s-1 1-1 2v1c0 1-1 2-1 2v1h0c-1 1-1 2-1 2l-1 3v2l-1 1v2l-2 5v2l-1 3s-1 1-1 2l-1 3v2h-1v2l-1 2v3c0 1-1 2-1 2l-2 6v3h2 1v1c0 2-3 2-4 3v3c-1 0-1 2-1 2v3h-1v3c0 1 0 1-1 2v3c0 1 0 1-1 2v3c0 1 0 1-1 3v2c-1 1-1 2-1 3l-1 2v2l-1 2v2l-1 2v2l-1 2v2c-1 1 0 3-1 4v3 3h1l-1 1h1 0l1 1-1 1h1v1l1 1c0 1 0 2 1 3l-8-12c-2-2-3-4-4-5 1-7 3-13 5-19l4-15c1-3 1-6 3-9 2-2 7-4 10-6h-7c-1-1-1-1-1-2 1-5 3-9 5-13l22-76 11-34c1-3 2-7 4-10z" class="T"></path><path d="M634 526l4-15c1-3 1-6 3-9-1 4 0 7-1 11v1l-7 33v3c-2-2-3-4-4-5 1-7 3-13 5-19z" class="J"></path><path d="M341 198c-1-2-2-2-2-3h-1l-1-1c1-1 0-1 1-1l2-2 2 1c1 0 2 1 4 0h0 1 5 0c2-1 3-1 5-1 9 2 18 3 26 6h63c5-1 12 0 18 0 2 0 4 0 6-1 5-2 14 0 20-1 5 1 10 1 15 2 3 0 6 0 10 1l1 1c-2 1-5 1-7 2l-10 3-2-1c-1-1 0-1 0-1v-1l-8-1-5 1c-13-1-26 1-38 1-24 2-48 3-72 8l-12 3c-6-1-16 5-22 8-1 0-3 0-5 1-2 0-4 2-6 1 7-8 19-16 29-21h0l-4 1-1-1h2v-1h-4-1l-1-1h-4l-4-2z" class="q"></path><path d="M515 198l1 1c-2 1-5 1-7 2l-10 3-2-1c-1-1 0-1 0-1v-1l-8-1c8-1 16 0 25-1l1-1z" class="D"></path><path d="M446 197c5-1 12 0 18 0 2 0 4 0 6-1 5-2 14 0 20-1 5 1 10 1 15 2 3 0 6 0 10 1l-1 1c-4-2-50-1-58-1h0l-10-1z" class="l"></path><path d="M352 192c2-1 3-1 5-1 9 2 18 3 26 6h63l10 1h0l-45 1c-21 1-45 1-64-7h5 0z" class="c"></path><path d="M352 192c2-1 3-1 5-1 9 2 18 3 26 6-10 0-22-2-31-5h0z" class="J"></path><path d="M441 260c1 1 0 5 1 7 0 1 0 2 1 3v4 2l1 1v2c1 0 1 1 2 2l1 4v1c1 1 1 2 1 3h0c1 1 1 2 2 4v1 1l1-1c1 1 0 2 1 3 0 1 1 3 1 4v1h1v3l1-1v2l1 2v1 1c1 0 1 1 1 3l1 2s1 1 1 2v1c1 2 1 4 2 6v2l1 1v2c0 1 0 0 1 2l2 5-1 1 1 1v1 1h0c1 1 1 2 2 3h-1l1 1c0 1 1 2 1 3h0v2 1c0 1 0 1 1 2v3l1 1v1c0 1-1 1 0 2h0v2c1 1 1 1 1 2v1h-1l1 1v1 1 1c1 1 1 1 1 2l1 1v1c1 1 1 2 2 2l-1 1 1 1h-1l1 1c1 1 1 3 1 5l1-1 1 1-1 1 1 1h0 0c1 1 1 2 2 3l-1 1 1 1v1c0 2 1 2 0 3l1 1v1 1h1v1c0 1 0 1 1 2h-1v1c1 0 2 1 2 2v3 1h1v2 1 1c1 1 1 2 1 4 1 1 1 2 1 3h-1-1v2c1 1 2 1 3 3 1 1 2 1 3 2l3 2h2v1l1 1 1 1c0 1-1 2 0 4 1 1 1 4 2 5l-1 1c0 1 0 1 1 3-1 1-1 2-1 3l1 1v3h0c0 2-1 4 0 6v1l1 1h-1v1l1 1h0l-1 1 1 1h0c0 1 1 1 1 2l1 1h-1c1 1 1 1 1 2v1 1l1-1v-4h0c0 2 0 5 1 7v12l-3-9c-1-7-3-15-4-23 0-5 0-10-1-15 0-3-2-5-3-8l-1 4h0l1-1v1h0c2 5 2 9 2 14 0 4 1 8 1 13 2 12 7 24 8 37l3 12-1-1c0-1 0-1-1-2h-1l-2-5-13-43v-1c-3-2-2-6-3-9 0-1-1-2-2-3-1 2-1 5 0 8l-2-2-3-22c-1-2-1-3-2-5-2-1-1-4-4-5 0-2-1-4-1-5l-3-12c-1-3-1-5-1-7h0c-2-1-2-2-2-3l-2-10v-3c-1-1-1-1-2-1v-2c0-2 0-3-1-4 0 0 0-1-1 0 0-2 0-3 1-5v-4c-1-3-2-5-3-7v-4c-1-1-1-3-1-4-1-2 0-4 0-6l-1-2c-5-5-5-11-9-16-1-1-1-3-2-5l-3-13c-1-1-1-2-1-3-2-1-1-1-2-2l-3 2v-8l-1-6v-2c0-2 0-2-1-4 0-2-1-5-1-7l1-1c1 1 1 1 3 0h0c0-2 0-6 1-7 0-1 1-2 1-3l1-3z" class="d"></path><path d="M441 260c1 1 0 5 1 7 0 1 0 2 1 3v4 2l1 1v2c1 0 1 1 2 2-1 1-1 1-2 1h-1s-1 2-2 2l16 54c-5-5-5-11-9-16-1-1-1-3-2-5l-3-13c-1-1-1-2-1-3-2-1-1-1-2-2l-3 2v-8l-1-6v-2c0-2 0-2-1-4 0-2-1-5-1-7l1-1c1 1 1 1 3 0h0c0-2 0-6 1-7 0-1 1-2 1-3l1-3z" class="i"></path><defs><linearGradient id="O" x1="439.125" y1="270.902" x2="442.875" y2="274.598" xlink:href="#B"><stop offset="0" stop-color="#ecced4"></stop><stop offset="1" stop-color="#dedddd"></stop></linearGradient></defs><path fill="url(#O)" d="M441 260c1 1 0 5 1 7 0 1 0 2 1 3v4 2l1 1v2c1 0 1 1 2 2-1 1-1 1-2 1h-1s-1 2-2 2c-1-7-1-13-1-21l1-3z"></path><path d="M438 273v1 2c0 1 1 2 1 3 0 4 1 8 2 11 1 5 2 9 2 14-1-1-1-2-1-3-2-1-1-1-2-2l-3 2v-8l-1-6v-2c0-2 0-2-1-4 0-2-1-5-1-7l1-1c1 1 1 1 3 0h0z" class="B"></path><path d="M434 274l1-1c1 1 1 1 3 0-1 4-1 5-3 8 0-2-1-5-1-7z" class="G"></path><path d="M458 340l13 43c4 12 6 24 13 35 1 3 4 6 7 7 1 1 2 1 3 2l-1 4h0l1-1v1h0c2 5 2 9 2 14 0 4 1 8 1 13 2 12 7 24 8 37l3 12-1-1c0-1 0-1-1-2h-1l-2-5-13-43v-1c-3-2-2-6-3-9 0-1-1-2-2-3-1 2-1 5 0 8l-2-2-3-22c-1-2-1-3-2-5-2-1-1-4-4-5 0-2-1-4-1-5l-3-12c-1-3-1-5-1-7h0c-2-1-2-2-2-3l-2-10v-3c-1-1-1-1-2-1v-2c0-2 0-3-1-4 0 0 0-1-1 0 0-2 0-3 1-5v-4c-1-3-2-5-3-7v-4c-1-1-1-3-1-4-1-2 0-4 0-6z" class="i"></path><path d="M485 443h0c-1-2-1-4-1-6v-1l1-1c1-2-1-5 0-7l2 1h3c-1 2-2 2-3 4 0 1-1 2-1 2v4c1 6 4 11 4 17v-1c-3-2-2-6-3-9 0-1-1-2-2-3z" class="C"></path><path d="M493 431h0l1-1v1h0c0 1-1 2-1 3 2 3 1 7 1 10l1 10c1 6 3 11 3 17l-4-15c-2-7-5-19-1-25z" class="T"></path><path d="M469 393h1c1 2 2 6 2 8 2 5 4 9 6 14 2 4 3 7 6 10-2 0-4-2-6-3s-1-4-4-5c0-2-1-4-1-5l-3-12c-1-3-1-5-1-7z" class="G"></path><path d="M478 422c2 1 4 3 6 3 2 2 4 3 6 4h0-3l-2-1c-1 2 1 5 0 7l-1 1v1c0 2 0 4 1 6h0c-1 2-1 5 0 8l-2-2-3-22c-1-2-1-3-2-5z" class="B"></path><path d="M494 431c2 5 2 9 2 14 0 4 1 8 1 13 2 12 7 24 8 37-3-7-6-16-7-24 0-6-2-11-3-17l-1-10c0-3 1-7-1-10 0-1 1-2 1-3z" class="d"></path><path d="M615 617h4v1 1c-1 0-1 0-2 1h0l6 2v1l1 1h0c1 1 3 1 3 3v1l-1-1c-1 0-1 0-2-1h-2v-1c-2 0-3 0-4-1h-1-1 0c-2 1-4 2-5 4h0l-1 2-2 2-2 6c-1 2-2 5-3 7l-2 7s-1 1-1 2l-2 7c0 1-1 2-1 2v1c0 1 0 2-1 3v2l-1 1v2l-1 2v2l-1 1v2l-1 2v2h-1v2c0 1 0 2-1 3v2l-1 2v2h-1v2l-1 5s-1 1-1 2v1c0 1-1 2-1 2l-1 5s-1 1-1 2v1l-2 6v1l-1 2v2c0 1 0 2-1 3v2l-1 1v2l-1 2v2l-1 1v2l-1 3c0 1-1 2-1 2v1c0 1 0 2-1 3v2l-1 1v2l-1 1v3s-1 1-1 2v1c0 1-1 2-1 2v2s-1 1-1 2v1c0 1-1 2-1 2l-1 3v2l-1 1v2l-1 2v2 2l-1 3-1 2v2l-1 1-2 8h0l-1 2v3c0 1-1 2-1 2v2s-1 1-1 2h0l-1 2v3c0 1-1 2-1 2l-1 3v2h0c0 1 0 2-1 3v2h-1v1 2l-1 1v2l-1 2v2 1l-1 3-1 2v3s-1 1-1 2v1c0 1-1 2-1 2v2c-1 1-1 1-1 2v1c0 1 0 1-1 2v2c-1 1-2 4-2 6-1 1 0 1-1 2v2l-1 1-1 1-1 4c-1 1-1 2-2 4h0c0 1-1 1-1 2-1 2 0 0-1 1v1l-1 1c-1 1-1 2-2 2l-1 1c0 1-1 2-2 2l-2 2c-1 0-2 0-3 1h0v1c-1 0-1 1-1 1v1l-1 3v1 1 1h-1v-1l5-19 72-246c1-2 2-4 2-5 2-5 4-10 7-14 2-2 7-1 10-1z" class="T"></path><path d="M615 617h4v1 1c-1 0-1 0-2 1h0c-3-1-6-1-8 0-3 3-5 5-6 9-5 10-8 21-11 32l-13 47-30 103c-5 20-10 39-17 58-1 4-3 9-5 11 0-1 0-3 1-4v-1l-1-1c-1 3-1 6-3 9l72-246c1-2 2-4 2-5 2-5 4-10 7-14 2-2 7-1 10-1z" class="J"></path><defs><linearGradient id="P" x1="591.616" y1="257.938" x2="618.975" y2="229.884" xlink:href="#B"><stop offset="0" stop-color="#3a0000"></stop><stop offset="1" stop-color="#160303"></stop></linearGradient></defs><path fill="url(#P)" d="M545 200l25 2c7 0 14 0 20 1h-3v1c1 1 1 1 1 2 4 2 9 4 13 7 0 0 1 1 2 1 5 5 13 10 16 16l1 2h1c1 2 3 2 4 3 3 1 4 3 6 6 1 2 2 3 3 5h1v-3h1c2 2 4 8 5 12l3 7v3 1h0v1c1 1 1 2 1 3 1 1 0 2 0 3 1 2 1 5 1 7 1 4 1 7 2 10 0 2-1 4 0 6v5 1c-1 2 0 4 0 5l-1 1v1c-1 1-1 2-1 4h-1l-1-2h0v3h-1v-1c0-1 0-1-1-2 0 2 1 4 0 6-1-1-1-1-1-2v-1h-2v-1-2c-1-1-1-1-1-2h-1l-1-3c-1-3-2-5-4-8l-2-2c-2-1-3-3-6-3-1 0-1-1-1-1-6 0-12-4-18-4-4 0-7-1-12-1l-1-1c-8-6-10-16-11-25 1 4 3 7 6 10h0c2 2 3 4 5 4 2 1 2 1 3 0v-4-1l-1-2s-1-1-2-1c0-1 0-2-1-3s-1-5-1-6c-1-3-2-7-3-10 0-3 0-7-1-9s-1-3-1-4c-2-4-8-9-8-13h0l-2-2-5-4-3 1c-1-2-3-3-4-4-7-5-14-8-21-10l-2-2 5-1z"></path><path d="M579 212h1c0 1 1 1 1 2 1 0 1 0 2 1l3 2-1 1v-1l-1 1v1 1c0-1-1-1-2-2-1 0-2-1-3-2s0-2 0-4z" class="K"></path><path d="M563 213h3c0-1-1-2-2-3h1s3 2 4 3c3 0 6 3 8 5l1 1-3 1-5-4-3 1c-1-2-3-3-4-4z" class="M"></path><path d="M627 259c0 2 1 3 2 5 2 4 1 7 2 12v10c1 1 1 2 1 3h-1c0-2-1-3-2-4-2 0-2-1-3-1l-1-1s1 0 2-1v-1c2-2 1-5 1-7l-1-15z" class="e"></path><path d="M637 286l-1-4v-1h0c-1-1-1-2-1-3s-1-1-1-2v-1h2c0 1 1 1 1 2 0 2 1 3 1 4 2 2 2 3 3 5 1 4 1 8 2 12 1 3 0 6 1 9 1 1 0 2 0 4h0c0-1-1-2-1-2l-1-1c0-3 1-8-1-11-1-4-2-8-4-11z" class="Q"></path><path d="M621 234c3 1 4 6 7 8 1 1 2 2 2 3 2 1 3 2 4 3 2 4 3 8 4 13 1 4 3 8 3 12h-1c-3-15-11-27-19-39z" class="L"></path><path d="M621 234c-1-1-3-2-4-4a57.31 57.31 0 0 0-11-11c-2-1-3-2-4-3l1-2c5 5 13 10 16 16l1 2h1c1 2 3 2 4 3 3 1 4 3 6 6 1 2 2 3 3 5v2c-1-1-2-2-4-3 0-1-1-2-2-3-3-2-4-7-7-8zm-5 8c-1-1-1-1-1-2-2-3-4-6-6-8l-1-2h1l2 2s1 0 2 1c1 2 3 3 5 4 4 4 7 10 7 15l2 6c-2-1-2-1-4 0l-1-2c0-2 0-3-2-4v-2c-1 0-1-1-1-2-1-1-1-1-1-2 1-2 0-3-2-5v1z" class="K"></path><path d="M618 246c3 1 4 2 6 4l1 1v1l2 6c-2-1-2-1-4 0l-1-2c0-2 0-3-2-4v-2c-1 0-1-1-1-2-1-1-1-1-1-2z" class="D"></path><defs><linearGradient id="Q" x1="641.788" y1="295.166" x2="635.594" y2="298.622" xlink:href="#B"><stop offset="0" stop-color="#250000"></stop><stop offset="1" stop-color="#400203"></stop></linearGradient></defs><path fill="url(#Q)" d="M631 286h1l1-1 2-2c1 1 1 2 2 3 2 3 3 7 4 11 2 3 1 8 1 11l1 1s1 1 1 2v3h-1v-1c0-1 0-1-1-2-1 0-2-6-3-8-1-1-1-1-1-2-1-2-3-3-3-5-1-2-1-4-2-5 0-1-1-1-1-2s0-2-1-3z"></path><path d="M602 244h0l-1-1c-1-2-2-3-3-5-1-4-5-9-8-12-1-2-2-3-3-5 2 0 3 2 4 3h3 1c1 1 5 6 6 7 1 2 2 3 3 4l1 3 4 6h-5 0-2z" class="H"></path><path d="M599 235h5l1 3-1 1v1h-1c-2-1-3-3-4-5z" class="F"></path><path d="M595 230c1 0 3 1 4 2 1 0 1 0 2-1 1 2 2 3 3 4h-5c-1-2-3-4-4-5z" class="M"></path><path d="M591 224h3 1c1 1 5 6 6 7-1 1-1 1-2 1-1-1-3-2-4-2 0-2-3-4-4-6z" class="L"></path><path d="M616 242v-1c2 2 3 3 2 5 0 1 0 1 1 2 0 1 0 2 1 2v2c1 2 1 3 1 4l1 2v2c1 2 0 4 1 6s0 5 0 7v5c-1 1-3 3-3 4-2 1-2 1-4 1v-1-1c3-3 3-7 2-11l-2-7c0-2 0-4-1-6 1 2 2 3 2 4 1 1 1 2 1 2 1 2 1 3 2 5v1 3c0 1-1 3 0 4h1c-1-1-1-2-1-4 1-1 1-2 1-4-1-1-1-2-2-4v-7l-1-3c0-2-1-5-1-7v-2c-1-1-1-2-1-3z" class="F"></path><path d="M625 283l1 1c1 0 1 1 3 1 1 1 2 2 2 4h1c0 1 1 1 1 2 1 1 1 3 2 5 0 2 2 3 3 5 0 1 0 1 1 2 1 2 2 8 3 8 0 2 1 4 0 6-1-1-1-1-1-2v-1h-2v-1-2c-1-1-1-1-1-2h-1l-1-3c-1-3-2-5-4-8l-2-2c-2-1-3-3-6-3-1 0-1-1-1-1-1-1-2-1-2-2l2-2c-1-1-1-2-1-3l3-2z" class="H"></path><path d="M625 283l1 1c1 0 1 1 3 1 1 1 2 2 2 4h1c0 1 1 1 1 2-1 0-1 1-1 1l-6-4h-3c-1-1-1-2-1-3l3-2z" class="Q"></path><path d="M575 220l3-1c2 2 5 4 7 7l2 2 1 1 3 3c2 2 4 5 6 7 3 3 4 8 6 11 1 1 1 0 1 2l2 2-1-3v-1c0-1-1-1-1-2l-1-1c0-1-1-2-1-3h2l7 13v1c1 2 1 3 1 5l-1 1c-2-4-3-6-8-7l-1 1-2 2c-1-2-3-3-5-3v4c1 2 0 4 1 6 0 1 0 2-1 3l-1-2s-1-1-2-1c0-1 0-2-1-3s-1-5-1-6c-1-3-2-7-3-10 0-3 0-7-1-9s-1-3-1-4c-2-4-8-9-8-13h0l-2-2z" class="e"></path><path d="M587 233c2 1 3 2 4 4l1 3h1c2 3 5 3 5 6 3 3 5 6 7 9l-2 2-1 1-2 2c-1-2-3-3-5-3l-1-3c0-3-1-6-2-8v-2c-1-4-4-8-5-11z" class="G"></path><path d="M592 240h1c2 3 5 3 5 6 3 3 5 6 7 9l-2 2-1 1c-1-1-1-2 0-3h0c-2-3-3-3-6-3-1-3-1-6-2-9-1-1-1-2-2-3z" class="B"></path><path d="M577 222c4 3 7 6 10 11 1 3 4 7 5 11v2c1 2 2 5 2 8l1 3v4c1 2 0 4 1 6 0 1 0 2-1 3l-1-2s-1-1-2-1c0-1 0-2-1-3s-1-5-1-6c-1-3-2-7-3-10 0-3 0-7-1-9s-1-3-1-4c-2-4-8-9-8-13z" class="Y"></path><path d="M585 235c2 3 4 7 5 11l1 1c0 1 0 3 1 4v1c1 3 0 7 2 9v7s-1-1-2-1c0-1 0-2-1-3s-1-5-1-6c-1-3-2-7-3-10 0-3 0-7-1-9s-1-3-1-4z" class="O"></path><path d="M609 244c2 2 3 4 4 7l2 6c1 2 1 4 1 6l2 7c1 4 1 8-2 11v1 1c2 0 2 0 4-1 0-1 2-3 3-4v-5c0-2 1-5 0-7s0-4-1-6v-2l-1-2c0-1 0-2-1-4 2 1 2 2 2 4l1 2c2-1 2-1 4 0v1l1 15c0 2 1 5-1 7v1c-1 1-2 1-2 1l-3 2c0 1 0 2 1 3l-2 2c0 1 1 1 2 2-6 0-12-4-18-4-4 0-7-1-12-1l-1-1c-8-6-10-16-11-25 1 4 3 7 6 10h0c2 2 3 4 5 4 2 1 2 1 3 0v-4-1c1-1 1-2 1-3-1-2 0-4-1-6v-4c2 0 4 1 5 3l2-2 1-1c5 1 6 3 8 7l1-1c0-2 0-3-1-5v-1l-7-13h0 5z" class="B"></path><path d="M623 258c2-1 2-1 4 0v1l1 15c0 2 1 5-1 7v1c-1 1-2 1-2 1l-3 2h-1l1-1c1-2 2-3 3-4 1-2 1-8 0-11h0c0-4-1-7-2-11z" class="U"></path><path d="M609 244c2 2 3 4 4 7l2 6c1 2 1 4 1 6l-1-3-1-1v3l1 1c2 4 2 9 2 14l-4 4h-2c1-1 3-2 3-3 2-4-1-11-3-14l1-1c0-2 0-3-1-5v-1l-7-13h0 5z" class="I"></path><path d="M593 287l-1-1c-8-6-10-16-11-25 1 4 3 7 6 10h0l-1 1 2 2c0 2 3 5 5 5l1 1 2 1v1c1 1 3 1 4 1l1 1c1 0 2 0 3 1h0 2l3 1c2 1 5 0 7 0l3-1h2 1c0 1 0 2 1 3l-2 2c0 1 1 1 2 2-6 0-12-4-18-4-4 0-7-1-12-1z" class="C"></path><path d="M621 285h1c0 1 0 2 1 3l-2 2c-2 0-4-1-6-1l-1-1c2-1 3-2 5-3h2z" class="F"></path><path d="M603 257c5 1 6 3 8 7 2 3 5 10 3 14 0 1-2 2-3 3h0c-3 1-5 0-8-1-1 0-3-1-3-3v-1c-2-5-1-11 0-16l2-2 1-1z" class="q"></path><path d="M264 178c0 1 1 3 1 4 1 2 2 3 4 3 7 2 16-1 22-5 7-4 12-9 14-17l1-1c1-6 1-12 1-18 2 3 3 7 4 10 1 4 3 7 4 10h0c-1 1-1 1-2 0v2h1c2 3 3 5 5 8 4 5 12 11 18 14 2 2 7 3 9 4h0c-2 1-3 0-4 0l-2-1-2 2c-1 0 0 0-1 1l1 1h1c0 1 1 1 2 3l4 2h4l1 1h1 4v1h-2l1 1 4-1h0c-10 5-22 13-29 21l-7 6c-3 3-5 5-7 8l-1 1c-1-1-1-1-2-1l5-7c-1 1-3 2-4 3l-2-1v-3s-1 1-1 2c-3 1-4 1-7 1l-2-1c-3 2-4 4-5 8l2 6h-1v2l-2 2c-1-1-1-2-3-3 1 3 4 10 2 13h-1l-1-4c-1-4-3-7-5-10 0-2-1-2-1-3l-1 1h-1l2 3c-2 1-2 2-2 4v1c1 1 1 1 1 2l1 1v-2c3 13 4 25-3 37-1 3-6 8-6 11l1 2c2 5 4 13 3 18v1c-1-2-1-4-1-5l-1-1c-1-1-1 0-1-1l-1-3c-1-3-3-7-6-9-3 0-6 4-9 6l6-12c0-2 5-4 6-6 4-5 5-16 4-22-1-9-6-17-11-22l-12-9 11-13c-5-4-12-5-18-3-3 1-6 5-7 8l-1 1c-3-2-6-5-9-5-9 1-23 16-27 23-8 12-14 31-10 46l2 8c-8-13-9-29-5-45v-2c2-2 2-4 3-6 1-3 3-6 4-9 4-7 8-13 13-20 5-5 10-12 16-16 2-2 5-4 8-5 7-3 15-4 21-9 4-4 7-10 7-15l1 3z" class="c"></path><path d="M270 201v-1h1c2-2 5-2 7-3l8-4c-2 3-7 6-11 7-2 1-3 1-5 1z" class="N"></path><path d="M263 205c4 0 8 0 13 1l-4 2h-8c-5-1-11-1-16-1h0c5-1 10-1 15-2z" class="J"></path><path d="M313 166h1l5 8c4 5 12 11 18 14 2 2 7 3 9 4h0c-2 1-3 0-4 0l-2-1-2 2c-1 0 0 0-1 1l1 1h1c0 1 1 1 2 3-6-2-13-7-16-12h0l2 1c0-1-3-4-4-5s-1-3-2-4c-1-2-3-2-4-4s-2-5-4-8z" class="d"></path><path d="M264 208h8l21 3c6 2 14 4 17 10 1 2 2 5 1 8 0 0-1 1-1 2-3 1-4 1-7 1l-2-1c-1-2-2-3-2-5 0-1 0-3 1-4s3-1 5-1h1c-3-1-6-2-10-3l1-1c-1 0-1 0-2-1h0-1-3c-1-1 0-1-1-1h-2c0-1-1-1-2-1l-3-1-4-1h-2-2-1v-1h3l1 1h2c1 0 2 1 4 1h2l1 1 5 1h3c-1-1-2-1-2-2v-1c-1 0-3-1-4-1l-9-1-4-1c-3 0-6-1-10 0-1 0-1-1-2-1z" class="E"></path><path d="M264 197v-1h0l5 4 1 1c2 0 3 0 5-1h3c1 0 1 2 1 3 2 0 4 0 6 1 3 1 10 1 13 0l2-1v1h3 1 1c1 2 2 3 3 4-2 1-5 1-6 2l-5-2h0c-2 1-4 0-6 2 1 0 2 0 3 1h-1l-21-3 4-2c-5-1-9-1-13-1-5 1-10 1-15 2h0c-3 2-6 2-9 3-3 0-6 2-8 4 3-6 23-12 30-16 1-1 2-1 3-1z" class="N"></path><path d="M264 197v-1h0l5 4 1 1c2 0 3 0 5-1h3c1 0 1 2 1 3 2 0 4 0 6 1h-8c-2-1-4-1-6-1l-19 1c2-2 5-4 8-4v-1h2 1l1-1v-1z" class="E"></path><path d="M275 200h3c1 0 1 2 1 3h-3c-2 0-4-1-6-1-1-1-1-1-1-2l1 1c2 0 3 0 5-1z" class="V"></path><path d="M298 204l2-1v1h3 1 1c1 2 2 3 3 4-2 1-5 1-6 2l-5-2h0c-2 1-4 0-6 2 1 0 2 0 3 1h-1l-21-3 4-2c-5-1-9-1-13-1 1-1 7 0 9 0s3-1 5-1h8c3 1 10 1 13 0z" class="T"></path><path d="M277 204h8c2 3 9 2 13 4h-1 0c-7-2-15-2-21-2-5-1-9-1-13-1 1-1 7 0 9 0s3-1 5-1z" class="R"></path><path d="M298 204l2-1v1h3 1 1c1 2 2 3 3 4-2 1-5 1-6 2l-5-2h1c-4-2-11-1-13-4 3 1 10 1 13 0z" class="b"></path><defs><linearGradient id="R" x1="281.444" y1="212.901" x2="296.855" y2="231.487" xlink:href="#B"><stop offset="0" stop-color="#343237"></stop><stop offset="1" stop-color="#78777b"></stop></linearGradient></defs><path fill="url(#R)" d="M286 234c-1 0-3-4-4-4-2-4-6-9-10-11h0c-1 0-1-1-2-2 2 1 3 2 4 3h1c1-1 1-2 2-3s1-1 1-2h-10c-3-1-6-1-9-1v-1c2 0 4 1 6 1 5 0 10 0 15 1 5 0 11 1 16 3 4 1 7 2 10 3h-1c-2 0-4 0-5 1s-1 3-1 4c0 2 1 3 2 5-3 2-4 4-5 8l2 6h-1v2l-2 2c-1-1-1-2-3-3l-6-12z"></path><defs><linearGradient id="S" x1="289.406" y1="230.697" x2="297.596" y2="234.591" xlink:href="#B"><stop offset="0" stop-color="#6a686d"></stop><stop offset="1" stop-color="#868487"></stop></linearGradient></defs><path fill="url(#S)" d="M286 234c1 1 2 2 3 4h0 0c0-3 0-5 1-7s2-3 4-4c1 0 2-1 3-1v1l2-1c0 2 1 3 2 5-3 2-4 4-5 8l2 6h-1v2l-2 2c-1-1-1-2-3-3l-6-12z"></path><defs><linearGradient id="T" x1="316.32" y1="195.533" x2="330.528" y2="221.718" xlink:href="#B"><stop offset="0" stop-color="#d6d4d5"></stop><stop offset="1" stop-color="#fcfcfb"></stop></linearGradient></defs><path fill="url(#T)" d="M328 192c2 1 4 3 6 4 4 1 7 3 11 4h4l1 1h1 4v1h-2l1 1 4-1h0c-10 5-22 13-29 21l-7 6c-3 3-5 5-7 8l-1 1c-1-1-1-1-2-1l5-7c-1 1-3 2-4 3l-2-1v-3c1-3 0-6-1-8-3-6-11-8-17-10h1c-1-1-2-1-3-1 2-2 4-1 6-2h0l5 2c1-1 4-1 6-2 2 2 4 3 8 3h1c2-1 4-2 5-4s1-4 0-7c1-1 1-2 2-3 0-1 1-1 2-2 0-1 1-2 2-3z"></path><path d="M308 208c2 2 4 3 8 3h1c2 1 3 1 6 1v1l1 1c-8 1-16-1-22-4 1-1 4-1 6-2z" class="h"></path><path d="M328 192c2 1 4 3 6 4-2 2-6 4-8 7-1 1-2 3-2 5-1 1-2 1-2 3 0 1 0 1 1 1-3 0-4 0-6-1 2-1 4-2 5-4s1-4 0-7c1-1 1-2 2-3 0-1 1-1 2-2 0-1 1-2 2-3z" class="J"></path><path d="M354 203l4-1h0c-10 5-22 13-29 21l-7 6c-3 3-5 5-7 8l-1 1c-1-1-1-1-2-1l5-7c7-8 17-16 26-21 4-2 7-3 10-5l1-1z" class="N"></path><defs><linearGradient id="U" x1="297.214" y1="180.768" x2="306.079" y2="195.549" xlink:href="#B"><stop offset="0" stop-color="#29272c"></stop><stop offset="1" stop-color="#6a686d"></stop></linearGradient></defs><path fill="url(#U)" d="M293 189c5-4 12-8 16-14h0l2-4h0v-1c1 1 1 1 1 2 3 5 7 11 11 15l5 5c-1 1-2 2-2 3-1 1-2 1-2 2-1 1-1 2-2 3 1 3 1 5 0 7s-3 3-5 4h-1c-4 0-6-1-8-3-1-1-2-2-3-4h-1-1-3v-1l-2 1c-3 1-10 1-13 0-2-1-4-1-6-1 0-1 0-3-1-3h-3c4-1 9-4 11-7h0l7-4z"></path><defs><linearGradient id="V" x1="301.177" y1="196.5" x2="304.958" y2="202.722" xlink:href="#B"><stop offset="0" stop-color="#6a676c"></stop><stop offset="1" stop-color="#8d8b8e"></stop></linearGradient></defs><path fill="url(#V)" d="M303 195h4c1-1 2 0 3 0v1c-3 2-4 4-5 8h-1-1-3v-1l-2 1-1-1c-3-1-6 0-9 0v-2c2-1 3-1 5-1 2-1 3-1 5-1l3-2 2-2z"></path><path d="M298 199l3-2 1 1v1c-1 0-1 0-1 1h-1c-1 0-2 1-3 1h0v-1l1-1z" class="W"></path><defs><linearGradient id="W" x1="281.209" y1="197.388" x2="285.323" y2="201.691" xlink:href="#B"><stop offset="0" stop-color="#333137"></stop><stop offset="1" stop-color="#4f4c52"></stop></linearGradient></defs><path fill="url(#W)" d="M286 193h0l7-4-1 3c0 1-1 1-1 2l-1-1c-1 1 0 1 0 2l-2 2-1 1h2 1c1-1 1-1 2 0-1 2-3 2-5 3l-1 1c1 1 0 1 2 1 3 0 6-1 9 0l1 1c-3 1-10 1-13 0-2-1-4-1-6-1 0-1 0-3-1-3h-3c4-1 9-4 11-7z"></path><defs><linearGradient id="X" x1="313.062" y1="189.78" x2="318.219" y2="199.136" xlink:href="#B"><stop offset="0" stop-color="#67656a"></stop><stop offset="1" stop-color="#989799"></stop></linearGradient></defs><path fill="url(#X)" d="M323 187l5 5c-1 1-2 2-2 3-1 1-2 1-2 2-1 1-1 2-2 3-1-1-1-2-2-3-3-2-6-2-10-1v-1c-1 0-2-1-3 0h-4c3-1 7-4 10-4 1 0 1 0 2-1 1 1 1 1 2 1 1-1 0-1 2-1 1 0 1 0 2-1s2-1 2-2z"></path><path d="M310 196c4-1 7-1 10 1 1 1 1 2 2 3 1 3 1 5 0 7s-3 3-5 4h-1c-4 0-6-1-8-3-1-1-2-2-3-4 1-4 2-6 5-8z" class="E"></path><path d="M719 160l6-18c1 12-1 24 8 33 7 6 17 11 26 11 3-1 5-1 7-3s2-4 2-7l2 6 1-2c1 4 2 8 6 10l11 4 3 1 3 3c11 4 17 11 25 20 7 9 12 17 17 27 2 5 3 11 6 16 2 14 2 28-6 41l2-10c3-14-3-33-11-45-5-7-11-13-18-18-3-1-6-3-9-3-3-1-6 2-8 4-2-4-4-7-7-9-6-2-14 0-20 2 4 5 8 10 11 15l-12 8c-4 4-6 9-9 15h0c-1 3-1 6-1 10 0 7 0 16 6 20 2 2 4 4 5 7l1 3 4 7-5-4c-1-1-2-1-4-2-2 1-3 3-4 4-2 4-3 7-3 11-2-1-2-1-3-3-1 1-1 2-2 4 0 3 0 8 1 11v2l-1-1v-1c-1-1-1-2-1-3v-2-1c0-1-1-1-1-2v-1c-1 1-1 2 0 3v4l1 1v6 2h-1l-3-15-9-34c1-2 0-5 1-8 1 2 1 4 2 6 0 1 0 1 1 2v-1l-2-21h0v4c-2-3-2-5-4-8-3-6-7-14-12-19-4-6-10-13-16-17l-7-7c-8-6-16-11-25-16h-1v-1c2 0 2 0 3-1h6 1 1 3c6-2 12-5 17-10h0v-1c1-3 3-5 5-7 1-2 2-3 3-5h1 1c1-1 2-2 2-3v-1h1v-1l1-2c1-1 1-3 2-4l1-2c0-1 0-1 1-2v-1-2l-1 1h-1z" class="c"></path><path d="M724 217l3-3v2l2-1v1h1c1-1 3-1 4-2l1 1c-1 1-1 0-3 1 0 0-1 0-1 1-3 0-4 1-6 3l-3-1 2-2z" class="n"></path><path d="M763 204c6 0 13 0 19 2h2c-1 1 0 1-1 1h-11-1c-1-1-2-1-3-1-1-1-3-2-5-2z" class="Z"></path><path d="M782 206l1-1h2c6 1 11 4 16 8h-1 0c-6-3-11-4-17-6h0c1 0 0 0 1-1h-2zm-48-21l18 10v1l-4-1v1l-1 1h-1c-1-3-3-3-4-5-3-2-6-4-8-7z" class="f"></path><path d="M770 182l1-2c1 4 2 8 6 10l11 4 3 1 3 3c-4-1-8-2-12-4-6-2-10-6-12-12z" class="T"></path><path d="M745 246v1c0 4-3 9-4 13 0 1 0 2-1 3v3 1c1 0 2 1 2 3 2-3 0-7 2-9v-3-1c1 0 1 0 0 1 0 3-1 7 0 10 0 6 2 12 4 17-1 1-1 2-1 3l-1-1c-2-1-2-2-3-4h0c-1-2-2-5-2-7-1-1-1-2-2-3-1-2 0-5 0-6l2-10 2-6 2-5z" class="V"></path><path d="M733 230c1 0 3 2 4 3 2 3 0 8 2 10h1l2-1c-2 4-4 7-5 11 0 3 0 8-1 11l-4-9h1c1-2 1-5 2-7s1-4 1-5v-2-3c-1-3-4-5-6-6l3-2z" class="b"></path><defs><linearGradient id="Y" x1="750.719" y1="212.542" x2="747.075" y2="202.915" xlink:href="#B"><stop offset="0" stop-color="#99979d"></stop><stop offset="1" stop-color="#cdc9cc"></stop></linearGradient></defs><path fill="url(#Y)" d="M756 204h7c2 0 4 1 5 2 1 0 2 0 3 1h1c-14 1-27 1-41 5-1 1-3 1-4 2l-3 3c0-2 0-2 1-3v-1l-1-1 2-1c2-1 4-2 7-3l13-3 10-1z"></path><path d="M724 212l2-1h4l1 1c-1 1-3 1-4 2l-3 3c0-2 0-2 1-3v-1l-1-1z" class="d"></path><path d="M766 301l-1-1c-2-1-3-3-4-4-4-6-9-10-11-17-3-12 0-23 6-34 1-2 5-9 7-9l1 1-1 1-1 1-1 1c0 1-1 2-1 3l-4 7c-1 3-3 8-2 11h1 0 0c-1 3-1 6-1 10 0 7 0 16 6 20 2 2 4 4 5 7l1 3z" class="E"></path><defs><linearGradient id="Z" x1="733.448" y1="244.468" x2="726.202" y2="243.66" xlink:href="#B"><stop offset="0" stop-color="#908d91"></stop><stop offset="1" stop-color="#b4b2b3"></stop></linearGradient></defs><path fill="url(#Z)" d="M722 229c3 2 4 3 8 3 2 1 5 3 6 6v3 2c0 1 0 3-1 5s-1 5-2 7h-1c-4-9-10-17-17-24h0c3 0 7 6 9 8h0l-1-1c0-2-1-3-2-4v-1h-1c1-2 1-3 2-4z"></path><path d="M737 265c0-1 0-2 1-3v-3c4-11 10-21 17-31 2-4 7-8 11-10h1c1-1 3-1 4-1l1 1c-2 0-4 1-6 1-8 4-16 16-19 24l-2 3-2 5-2 6-2 10c0 1-1 4 0 6 0 3 0 6 1 9 1 8 8 30 5 38h0l-1 1-9-34c1-2 0-5 1-8 1 2 1 4 2 6 0 1 0 1 1 2v-1l-2-21z" class="W"></path><defs><linearGradient id="a" x1="752.634" y1="296.034" x2="742.759" y2="298.496" xlink:href="#B"><stop offset="0" stop-color="#28262a"></stop><stop offset="1" stop-color="#49464b"></stop></linearGradient></defs><path fill="url(#a)" d="M739 273c1 1 1 2 2 3 0 2 1 5 2 7h0c1 2 1 3 3 4l1 1c0-1 0-2 1-3 0 2 1 5 2 7 1 1 4 5 5 6 0 1-1 3-1 3-1 4-2 9-3 13-1 1-1 2-2 4 0 3 0 8 1 11v2l-1-1v-1c-1-1-1-2-1-3v-2-1c0-1-1-1-1-2v-1c-1 1-1 2 0 3v4l1 1v6 2h-1l-3-15 1-1h0c3-8-4-30-5-38-1-3-1-6-1-9z"></path><path d="M673 202h-1v-1c2 0 2 0 3-1h6 1 1 3 1 1 4 1c1 1 1 0 1 1h1l1 1h0l1 1h0l2 2c4 1 6 6 9 8 1 0 2 1 2 1h7l7-2 1 1v1c-1 1-1 1-1 3l-2 2c-1 3-2 5-1 8l1 2c-1 1-1 2-2 4h1v1c1 1 2 2 2 4l1 1h0c-2-2-6-8-9-8h0l-9-9c-7-6-15-11-23-16-3-1-6-4-10-4z" class="X"></path><defs><linearGradient id="b" x1="735.849" y1="183.335" x2="724.435" y2="198.791" xlink:href="#B"><stop offset="0" stop-color="#302e34"></stop><stop offset="1" stop-color="#727073"></stop></linearGradient></defs><path fill="url(#b)" d="M708 188c5-5 10-11 13-18 3 6 7 10 13 15 2 3 5 5 8 7 1 2 3 2 4 5h1l1-1c0 2 0 3 1 4h1c1 0 1 0 2 1h1 2c1 1 1 1 2 1 0 1-1 2-1 2l-10 1c1 0 2-1 2-2-1-1-1-1-3-1l-5-2h-4l-11-4v1l-2-1v-1l-9-3c-1-1-1-1-2-1-1-2-3-2-4-3z"></path><defs><linearGradient id="c" x1="745.917" y1="211.707" x2="730.55" y2="238.076" xlink:href="#B"><stop offset="0" stop-color="#3f3c42"></stop><stop offset="1" stop-color="#848286"></stop></linearGradient></defs><path fill="url(#c)" d="M731 217c8-1 17-3 25-2 1 0 2 0 3 1h-2c-1 1-1 1-1 2l1 1 1-1h1c-3 2-5 5-7 7-1 2-10 17-10 17l-2 1h-1c-2-2 0-7-2-10-1-1-3-3-4-3l-3 2c-4 0-5-1-8-3l-1-2c-1-3 0-5 1-8l3 1c2-2 3-3 6-3z"></path><path d="M722 229l-1-2c-1-3 0-5 1-8l3 1h6c2 1 3 2 3 5 0 2 0 3-1 5l-3 2c-4 0-5-1-8-3z" class="E"></path><path d="M728 222c1 0 2 2 3 2 0 2 0 2-1 4h-1-1c-1 0-1-1-2-2 0-1 1-2 2-4z" class="n"></path><defs><linearGradient id="d" x1="719.08" y1="208.169" x2="713.974" y2="210.57" xlink:href="#B"><stop offset="0" stop-color="#838085"></stop><stop offset="1" stop-color="#9b999d"></stop></linearGradient></defs><path fill="url(#d)" d="M706 190l2-2c1 1 3 1 4 3 1 0 1 0 2 1l9 3v1l2 1v-1l11 4h4l5 2c2 0 2 0 3 1 0 1-1 2-2 2l-13 3c-3 1-5 2-7 3l-2 1-7 2h-7s-1-1-2-1c-3-2-5-7-9-8l-2-2h0l-1-1h0l-1-1h-1c0-1 0 0-1-1h-1-4-1c4-1 7-3 11-4l5-3 3-3z"></path><path d="M703 193c3 2 5 3 9 3 0 1 0 1-1 1l-1 2c-2 1-2 2-2 5l-1-1c0-1 0-2-1-3-1 0-1 0-2-1v-2c-1 0-1 0-2-1h-4l5-3z" class="J"></path><defs><linearGradient id="e" x1="746.804" y1="204.949" x2="727.19" y2="202.295" xlink:href="#B"><stop offset="0" stop-color="#58555b"></stop><stop offset="1" stop-color="#888689"></stop></linearGradient></defs><path fill="url(#e)" d="M717 199c2 2 5 2 8 3 0-1 1-1 1-2 3 0 6 1 9 1l1-1h4l5 2c2 0 2 0 3 1 0 1-1 2-2 2l-13 3c0-2 0-2-1-3-2 0-3 0-5 1-1 1 0 2-2 2-1-1-1-1-1-2-1-1 0-1-1-1 0-1-4-1-5-2 0-1 0-2-1-3v-1z"></path><path d="M706 190l2-2c1 1 3 1 4 3 1 0 1 0 2 1l9 3v1l2 1v-1l11 4-1 1c-3 0-6-1-9-1 0 1-1 1-1 2-3-1-6-1-8-3l-2-1c-1-1-2-1-4-1 1 0 1 0 1-1-4 0-6-1-9-3l3-3z" class="b"></path><path d="M703 193l3-3c5 5 14 7 20 10 0 1-1 1-1 2-3-1-6-1-8-3l-2-1c-1-1-2-1-4-1 1 0 1 0 1-1-4 0-6-1-9-3z" class="Z"></path><path d="M637 309h1c0 1 0 1 1 2v2 1h2v1c0 1 0 1 1 2 1-2 0-4 0-6 1 1 1 1 1 2v1h1v-3h0l1 2h1c0-2 0-3 1-4v-1c0 4 0 9-1 13v3 2h0v1 3 1c-1 2 0 3 0 5h-1v2h1c1 1 0 2 1 3v2l2 1c0-2 1-3 1-5l1 3v1c1-1 1-2 1-3s-1-1 0-2c0-1 1-3 1-4v1c1-1 1-1 1-2 1 0 2 1 2 1v2 1l-1 1h0v1 1c0 1 0 2-1 3v2c1-2 1-3 2-5l-23 78-6 23c-1 1-2 4-2 5l-11 39-2 5-26 90c-2 4-3 8-4 12-2 5-4 11-5 16l-1 5-1 3v2s0 1-1 2v1c0 2 0 3 1 5-1 0-1 2-1 3-1 2-2 5-3 7 1 2 1 2 0 4l-1 1c-2 1-3 1-5 3h0 1c-3 5-4 11-5 17-1 4-3 8-4 13v2l-2 6-2-3c1-2 0-4 1-7h-1c-1-2-3-5-3-7h0v-5c1-1 0-4 0-6s1-3 1-5c0-3-1-7 0-11h0l-1-1c-3 3 0 7-1 10l-2-2v-2l-1-1 1-2c-1-1-1-2-2-3 0-1 0 0-1-1v-1h-2 0l-1-1 3-9h-1-2c0-1 0-2-1-3v-1c0-3 1-6 2-8v-4-1c1 0 1-1 1-2 2-4 3-8 4-12l9-31 26-86 27-93c6-22 13-43 18-65l1-11c1 0 2 1 3 1h0c2 1 3 4 4 6v1 1 4 1-3c0-1 1-2 1-3h1v-1-5-1z" class="i"></path><path d="M563 583c1 1 2 3 2 5-1 0-1 0-1 1l-2 2c0-1-1-1-1-2-2-1-3 0-4 0l-1 1c0-1 1-2 2-3h1l1-2h1l2-2z" class="O"></path><path d="M542 609c1 0 1 1 2 2 0 5-2 9-1 14h-2c0-1 0-2-1-3v-1c0-3 1-6 2-8v-4z" class="P"></path><path d="M613 395v-3-1l1-1v-1c0-2 0-4 1-6 0-1 0-2 1-3v-1c0-1 1-2 1-2v-1c0-1 0-1 1-2h0l1-2v-1-4c1-1 1 1 1-1 0-1 0-1 1-2 0-1 1-3 1-4v-1c0 1-1 2-1 2v-1c0-2 1-3 1-5 2-5 4-22 7-25v4l1-1c1 2-1 14-1 17s-1 7-1 11l-1 1c0 1 0 1 1 2v4l1 4v2l1 1c0 1 0 1-1 2-1-1-1-2-1-4v-1l-1-1v-2c0-1 0-1-1-2s-1-1-2-1c-1 2-3 4-4 6 0 2-1 3-2 4 0 2-1 3-1 5l-4 17-1-1c0-1 0-1 1-2z" class="S"></path><path d="M601 434h-1l1-1v-1-1c0-2 1-4 1-6h0l1-1v-3h0l1-1c0-1-1-2 0-3h2l1-1v-2-1c0-1 1-2 0-3v-1l1-1v-1-1-1c1-2 0-3 2-4v-3-1h1v-1l1-1v-1l1 1c-1 1-1 1-1 2l1 1v3 3l-1 1v3 1c0 1 0 3-1 4v16c0 3-1 5 0 8v1h0v4 1c1 1 1 5 1 6h-1v-1c-1-1-1-2-2-3-2-2-3-2-6-3 0-1 0-1 1-1 0-1 1-2 3-2l-4-1-1-2v-1l1-1h-2z" class="B"></path><path d="M603 434c0-1-1-1 0-2l2 1h0v-1-2-1c1 1 1 2 1 3l1 1h0c1 2 2 3 3 4l1 1v4 1c1 1 1 5 1 6h-1v-1c-1-1-1-2-2-3-2-2-3-2-6-3 0-1 0-1 1-1 0-1 1-2 3-2l-4-1-1-2v-1l1-1z" class="S"></path><path d="M603 434c2 0 3 0 4 1l1 2c0 1-1 1-1 2l-4-1-1-2v-1l1-1z" class="I"></path><path d="M574 528l-1-1c0-1 0-2 1-4l1-2v-1l1-4 1-1 3-10c1-2 1-3 2-4v-1-2c1-2 1-2 1-4h0c-1 1-1 1-1 2s-1 2-2 3c-1-2 3-10 4-12 0-1 0-1 1-2 0 3-2 5-2 8 1-1 2-3 2-4v-1c1-3 1-5 1-8 0-2 1-4 2-5 0-2 0-4 1-5s1-2 1-3c1-1 1-2 1-3 0-2 1-2 2-3l-1 5c0 8-2 18-4 26l-1 5c0 3 0 9 2 12h-1l4 7v1 4c1 3 1 12 0 14 0 2 1 5 0 7 0 1 0 4-1 5v4h-1v-3c1-1 0-3 0-5l1-13v-7c-1-2-1-3-2-5-1-3-2-5-5-6h-3c-4 3-6 11-7 16z" class="P"></path><path d="M588 509l-1-1c-1 0-1-1-1-1-1-2-1-5 0-7l1-3c0 3 0 9 2 12h-1z" class="Y"></path><path d="M574 528c1-5 3-13 7-16h3c3 1 4 3 5 6 1 2 1 3 2 5v7l-1 13c0 2 1 4 0 5v3h1v3c0 2-1 4-2 7l-3 12c-2 6-4 13-6 18l-3 13c-1 2-1 4-1 5l-2 3v1-7l2-39c1-6-1-11-3-17v-1c-2-8-1-14 1-21z" class="B"></path><path d="M573 549c1-1 2-2 3-2 1 1 1 2 0 4h0l1 1h2l1-1-1-2h0l1-1 1-1v-1h2v3l-2 3c-1 2 0 7-1 10v6c-1 0-2 1-3 2h0l-1-3c1-6-1-11-3-17v-1z" class="H"></path><path d="M584 542c0-2 0-3 1-5l1 2h0c3-2 1-5 3-7v-1c1-1 1-5 1-7-1-1-1-2-1-3h1v4c1 1 0 3 0 5h1l-1 13c0 2 1 4 0 5v3h1v3c0 2-1 4-2 7l-3 12c-2 6-4 13-6 18-1-1-1-2-1-3s1-3 1-4c1-2 0-5 1-7v-3-2h0c1-2 1-5 1-6 1-2 0-5 0-7 1-2 1-5 1-7v-3-3c1-1 1-3 1-4z" class="S"></path><path d="M584 542c0-2 0-3 1-5l1 2h0c3-2 1-5 3-7v-1c1-1 1-5 1-7-1-1-1-2-1-3h1v4c1 1 0 3 0 5h1l-1 13c0 2 1 4 0 5v3 1c-1 1-1 1-1 2v2 2c-1 0-1 1-1 2l-1 1c0 1-1 1-1 2-1 0-1 1-1 1h0c-1-3-1-8 0-10v-5-2-4l-1-1z" class="G"></path><path d="M574 528c1-5 3-13 7-16h3c3 1 4 3 5 6 1 2 1 3 2 5v7h-1c0-2 1-4 0-5v-4h-1c0 1 0 2 1 3 0 2 0 6-1 7v1c-2 2 0 5-3 7h0l-1-2c-1 2-1 3-1 5 0 1 0 3-1 4h-2v1l-1 1-1 1h0l1 2-1 1h-2l-1-1h0c1-2 1-3 0-4-1 0-2 1-3 2-2-8-1-14 1-21z" class="M"></path><path d="M582 513h2l3 3c1 1 1 6 1 8l-1 1-2-1c-1 1-3 3-3 4l-1 3c-1 2-1 3-2 4-2-1 0-12-1-14 0-1 0-1-1-2 1 0 1-1 2-2 1-2 2-3 3-4z" class="L"></path><path d="M617 381c0-2 1-3 1-5 1-1 2-2 2-4 1-2 3-4 4-6 1 0 1 0 2 1s1 1 1 2v2l1 1v1c0 2 0 3 1 4v6c2 7 2 16 1 23-1 4-1 7-3 11 0 2 0 4-1 6v6c-1 2-3 14-4 15-2 1-3 2-3 3h-1 0c0 1 0 3-2 4v2c-1 1-2 3-2 4 1 2 0 3-1 5h0c0-3 1-9-1-12v-1c0-1 0-5-1-6v-1-4h0v-1c-1-3 0-5 0-8v-16c1-1 1-3 1-4v-1-3l1-1v-3-3l4-17z" class="C"></path><path d="M611 438h1c1 3 1 6 2 8l1-2c1-3 1-5 1-7 0-3 0-5 1-7v-2c-1-3-1-7-1-10 0-1 1-1 1-2 0-4-1-7 1-11 1 4 0 9 0 13 0 6 1 12 0 18 0 5-1 10-2 15v2c-1 1-2 3-2 4 1 2 0 3-1 5h0c0-3 1-9-1-12v-1c0-1 0-5-1-6v-1-4h0z" class="G"></path><path d="M621 386l2-5c0 5-2 11-2 17 2 2 2 2 2 4-1 2-1 3-1 5v15c1-1 1-2 1-2l2-1v-3h1l1 1c0 2 0 4-1 6v6c-1 2-3 14-4 15-2 1-3 2-3 3h-1 0c0 1 0 3-2 4 1-5 2-10 2-15 1-6 0-12 0-18 0-4 1-9 0-13 1-7 1-13 3-19z" class="r"></path><path d="M621 398c2 2 2 2 2 4-1 2-1 3-1 5v15 9h-1v-3c-1 1 0 5-1 5h0v-15c0-7 0-14 1-20z" class="I"></path><path d="M623 420l2-1v-3h1l1 1c0 2 0 4-1 6v6c-1 2-3 14-4 15-2 1-3 2-3 3h-1 0l2-14h0c1 0 0-4 1-5v3h1v-9c1-1 1-2 1-2z" class="O"></path><path d="M623 420l2-1v-3h1l1 1c0 2 0 4-1 6-2 2-1 6-3 8h-1v-9c1-1 1-2 1-2z" class="B"></path><path d="M617 381c0-2 1-3 1-5 1-1 2-2 2-4 1-2 3-4 4-6 1 0 1 0 2 1s1 1 1 2v2l1 1v1c0 2 0 3 1 4v6c2 7 2 16 1 23-1 4-1 7-3 11l-1-1h-1v3l-2 1s0 1-1 2v-15c0-2 0-3 1-5 0-2 0-2-2-4 0-6 2-12 2-17l-2 5c-2 2-1 4-3 7v4c0 1-1 4-1 5-1 1-1 1-1 3v1 1h-1c0 1 1 2 1 3s-1 2-1 3v-1c-1-2-1-4-1-6l-1 1v-2l1-6c0-2 1-5 2-8v-4c1-1 1-2 1-4v-2h0z" class="H"></path><path d="M623 420c0-4 1-7 2-10 0-3 0-5 1-7l1-1v5l-1 9h-1v3l-2 1z" class="I"></path><path d="M630 406c-1 4-1 7-3 11l-1-1 1-9 3-1z" class="U"></path><path d="M601 434h2l-1 1v1l1 2 4 1c-2 0-3 1-3 2-1 0-1 0-1 1 3 1 4 1 6 3 1 1 1 2 2 3v1c0 6 1 12-1 19l-3 16-1 4 1 2c-1 4-3 9-3 13l-1 5-2-1v3h-1c0 2-1 4-2 6 0 1-1 1-1 2l-1 2v-3c0-1-1-2-2-2-1 1-1 1-2 1l-4-7h1c-2-3-2-9-2-12l1-5c2-8 4-18 4-26l1-5c0-6 2-13 4-19 0-3 2-5 4-8z" class="G"></path><path d="M596 499v4c1 0 1 1 2 1l1 1h0v3c0 1 0 2-1 4v1c-1 1-1 2-1 3l-1 1c0-1-1-2-2-2v-2c1-1 2-3 1-5 0-1 1-2 0-3h0v2l-2-1h0c1-1 1-2 2-3s1-2 1-4z" class="I"></path><path d="M603 486h2v2h1l1 2c-1 4-3 9-3 13l-1 5-2-1 1-3v-6c1-1 1-1 1-2v-3-1-1-1c0-2-1-3 0-4z" class="O"></path><path d="M597 468c2-5 4-11 8-16h0c1 5 1 12 1 17-1 5-1 10-1 15h2l-1 4h-1v-2h-2v-4c0-1 0-2 1-3 1-3 0-6 0-9v-8c0-1 1-2 1-4-2 3-4 5-5 8l-2 4c-1 2 0 5-1 7v4 4c0 1-1 3-1 4v6h0l-1-2c0 2 0 4 1 6 0 2 0 3-1 4s-1 2-2 3c2-5 1-9 1-14 0-1 0-1-1-2 0-3 1-4 2-6 0-1 1-3 1-4 0-3 0-5 1-7v-5z" class="B"></path><path d="M588 492l1 4h1c2-2 2-9 3-12 1-1 1-3 2-4v-1c0-1 1-3 0-4 0-3 1-4 2-7v5c-1 2-1 4-1 7 0 1-1 3-1 4-1 2-2 3-2 6 1 1 1 1 1 2 0 5 1 9-1 14h0l2 1v-2h0c1 1 0 2 0 3 1 2 0 4-1 5v2c-1 1-1 1-2 1l-4-7h1c-2-3-2-9-2-12l1-5z" class="H"></path><path d="M593 506l2 1v-2h0c1 1 0 2 0 3 1 2 0 4-1 5v2c-1 1-1 1-2 1l-4-7h1 0c1 1 2 3 3 4v1h1v-1h0v-7z" class="B"></path><path d="M601 434h2l-1 1v1l1 2 4 1c-2 0-3 1-3 2-1 0-1 0-1 1 3 1 4 1 6 3 1 1 1 2 2 3v1c0 6 1 12-1 19l-3 16h-2c0-5 0-10 1-15 0-5 0-12-1-17h0c-4 5-6 11-8 16-1 3-2 4-2 7 1 1 0 3 0 4v1c-1 1-1 3-2 4-1 3-1 10-3 12h-1l-1-4c2-8 4-18 4-26l1-5c0-6 2-13 4-19 0-3 2-5 4-8z" class="F"></path><path d="M609 445c1 1 1 2 2 3v1c0 6 1 12-1 19-2-4-1-10-1-14 1-1 1-2 0-3v-6z" class="B"></path><path d="M601 434h2l-1 1v1l1 2 4 1c-2 0-3 1-3 2-1 0-1 0-1 1-1 3-2 8-3 11-2 6-5 11-7 16 0-1 0-2-1-3l1-5c0-6 2-13 4-19 0-3 2-5 4-8z" class="O"></path><path d="M637 309h1c0 1 0 1 1 2v2 1h2v1c0 1 0 1 1 2 1-2 0-4 0-6 1 1 1 1 1 2v1h1v-3h0l1 2h1c0-2 0-3 1-4v-1c0 4 0 9-1 13v3 2h0v1 3 1c-1 2 0 3 0 5h-1v2h1c1 1 0 2 1 3v2l2 1c0-2 1-3 1-5l1 3c-2 1-1 3-2 5-2 4-5 8-5 12v2 2c0 1 0 1-1 2-1 2-1 5-2 7 0 1-1 3-2 5-1 3-2 7-3 11-1 2-2 5-2 8h0c-1 2-1 4-1 6-1 2-1 5-2 7v3l-1 1h0v2 1h-1v2 1c-1 1-1 2-1 3l-2 7v-6c1-2 1-4 1-6 2-4 2-7 3-11 1-7 1-16-1-23v-6c1-1 1-1 1-2l-1-1v-2l-1-4v-4c-1-1-1-1-1-2l1-1c0-4 1-8 1-11s2-15 1-17l-1 1v-4h0v-2l-1-2 1-1c0-3 0-4-2-6l1-11c1 0 2 1 3 1h0c2 1 3 4 4 6v1 1 4 1-3c0-1 1-2 1-3h1v-1-5-1z" class="U"></path><path d="M628 308c1 0 2 1 3 1v4c0 1 0 4-1 5 0 1-1 1 0 2 1 3 0 7-1 10h0v-2l-1-2 1-1c0-3 0-4-2-6l1-11z" class="C"></path><path d="M644 311l1 2h1c0-2 0-3 1-4v-1c0 4 0 9-1 13v3 1h-1c-1 2-1 4-1 6h0l-1-2 1-1v-1-3c0-1 0-1-1-2v-1l-1 8h0v-12c1-2 0-4 0-6 1 1 1 1 1 2v1h1v-3h0z" class="D"></path><path d="M646 324v2h0v1 3 1c-1 2 0 3 0 5h-1v2h1c1 1 0 2 1 3v2l2 1c0-2 1-3 1-5l1 3c-2 1-1 3-2 5-2 4-5 8-5 12h-1l3-34v-1z" class="M"></path><path d="M637 309h1c0 1 0 1 1 2v2 1h2v1c0 1 0 1 1 2v12c0 1 0 1-1 2l-1-1c0 2-1 2-1 4v3c-1 1-1 1-1 2h-1v-1c1-1 0-4 0-6 1-1 1-3 1-4v-13l-1-1v-4h0v-1z" class="C"></path><path d="M629 350c0 2 0 4 1 5 0 2-1 7 0 9h0c-1 3 1 4 2 6 1 3 1 6 0 9 0 1 1 3 1 4v1h0l-1-1c-1 1 0 5 0 7 0 3-1 6-1 10l1 2v1-4l1-1 1-2c-1 2-1 4-1 6-1 2-1 5-2 7v3l-1 1h0v2 1h-1v2 1c-1 1-1 2-1 3l-2 7v-6c1-2 1-4 1-6 2-4 2-7 3-11 1-7 1-16-1-23v-6c1-1 1-1 1-2l-1-1v-2l-1-4v-4c-1-1-1-1-1-2l1-1c0-4 1-8 1-11z" class="P"></path><path d="M651 342v1c1-1 1-2 1-3s-1-1 0-2c0-1 1-3 1-4v1c1-1 1-1 1-2 1 0 2 1 2 1v2 1l-1 1h0v1 1c0 1 0 2-1 3v2c1-2 1-3 2-5l-23 78-6 23c-1 1-2 4-2 5l-11 39-2 5-26 90c-2 4-3 8-4 12-2 5-4 11-5 16l-1 5-1 3c0-2 0-4 1-7 0-1 0-3 1-5l3-13c2-5 4-12 6-18l3-12c1-3 2-5 2-7v-3-4c1-1 1-4 1-5 1-2 0-5 0-7 1-2 1-11 0-14v-4-1c1 0 1 0 2-1 1 0 2 1 2 2v3l1-2c0-1 1-1 1-2 1-2 2-4 2-6h1v-3l2 1 1-5c0-4 2-9 3-13l-1-2 1-4 3-16c2-7 1-13 1-19h1v1c2 3 1 9 1 12h0c1-2 2-3 1-5 0-1 1-3 2-4v-2c2-1 2-3 2-4h0 1c0-1 1-2 3-3 1-1 3-13 4-15l2-7c0-1 0-2 1-3v-1-2h1v-1-2h0l1-1v-3c1-2 1-5 2-7 0-2 0-4 1-6h0c0-3 1-6 2-8 1-4 2-8 3-11 1-2 2-4 2-5 1-2 1-5 2-7 1-1 1-1 1-2v-2-2c0-4 3-8 5-12 1-2 0-4 2-5z" class="X"></path><path d="M618 447h0 1c0-1 1-2 3-3l-4 12-2-3v-2c2-1 2-3 2-4z" class="S"></path><path d="M611 478h2c-1 2-1 4-2 5-1 6-2 11-4 16-1 1-2 2-3 4 0-4 2-9 3-13l4-12z" class="r"></path><path fill="#e19ea8" d="M604 503c1-2 2-3 3-4-1 3-1 5-2 8l-5 20c-1 4-2 9-4 13 0 1-1 2-2 3s-1 2-1 3c0-5 1-11 3-16l7-22 1-5z"></path><path d="M601 507l2 1-7 22c-2 5-3 11-3 16l-2 8v-3-4c1-1 1-4 1-5 1-2 0-5 0-7 1-2 1-11 0-14v-4-1c1 0 1 0 2-1 1 0 2 1 2 2v3l1-2c0-1 1-1 1-2 1-2 2-4 2-6h1v-3z" class="B"></path><path d="M594 515c1 0 2 1 2 2v3l-1 1h-1c0-1-1-2-2-4v-1c1 0 1 0 2-1z" class="C"></path><path d="M611 449h1v1c2 3 1 9 1 12h0c1-2 2-3 1-5 0-1 1-3 2-4l2 3-4 14c-1 3-1 6-3 8l-4 12-1-2 1-4 3-16c2-7 1-13 1-19z" class="P"></path><path d="M639 377h4v-1l1-1-19 63v3l-3 8v3l-1 2v-2c1-1 1-2 1-3 1-4 2-8 3-11l2-8c1-3 2-5 2-8h-1c0-1 0-2 1-3v-1-2h1v-1-2h0l1-1v-3c1-2 1-5 2-7 0-2 0-4 1-6h0c0-3 1-6 2-8 1-4 2-8 3-11z" class="S"></path><path d="M651 342v1c1-1 1-2 1-3s-1-1 0-2c0-1 1-3 1-4v1c1-1 1-1 1-2 1 0 2 1 2 1v2 1l-1 1h0v1 1c0 1 0 2-1 3v2l-10 30-1 1v1h-4c1-2 2-4 2-5 1-2 1-5 2-7 1-1 1-1 1-2v-2-2c0-4 3-8 5-12 1-2 0-4 2-5z" class="C"></path><path d="M566 556c1-2 2-4 3-5 2-1 3-1 4-1 2 6 4 11 3 17l-2 39v7-1l2-3c-1 3-1 5-1 7v2s0 1-1 2v1c0 2 0 3 1 5-1 0-1 2-1 3-1 2-2 5-3 7 1 2 1 2 0 4l-1 1c-2 1-3 1-5 3h0 1c-3 5-4 11-5 17-1 4-3 8-4 13v2l-2 6-2-3c1-2 0-4 1-7h-1c-1-2-3-5-3-7h0v-5c1-1 0-4 0-6s1-3 1-5c0-3-1-7 0-11h0l-1-1c-3 3 0 7-1 10l-2-2v-2l-1-1 1-2c-1-1-1-2-2-3 0-1 0 0-1-1v-1h-2 0l-1-1 3-9 6-21c1-4 2-11 6-14l1-1c1 0 2-1 4 0 0 1 1 1 1 2l2-2c0-1 0-1 1-1 0-2-1-4-2-5v-3-4-10l1-1c0-2 0-3 1-5 0-2 1-3 1-4z" class="k"></path><path d="M574 606v7-1l2-3c-1 3-1 5-1 7v2s0 1-1 2v1c0 2 0 3 1 5-1 0-1 2-1 3-1 2-2 5-3 7 1 2 1 2 0 4l-1 1v-3-1c1-3 1-5 1-8 1-2 1-5 1-8 1-2 1-4 1-5 1-3 0-7 1-10z" class="W"></path><path d="M560 632c1-2 1-4 1-5 0-2 0-4 1-5v-1c1-3 1-4 3-6l1 3c0 4-3 9-4 14-2 5-2 11-2 16-1 1-1 3-1 5v2l-1 2v2h-1v-2c1-1 1-1 1-2l1-7v-6-1-4c1-2 1-4 1-5z" class="T"></path><path d="M556 633c0-1 0-2 1-3v-3h1l1-1c0-1 0-2 1-3v3c-2 4-2 8-3 12v4c0 4 0 8-1 11v1c-1 1 0 2-1 3v3h-1c-1 1-1 2-1 3h-1c-1-2 0-6 0-8 1-2 2-6 3-8v-4c2-3 1-7 1-10z" class="l"></path><path d="M572 591l1 2h0c-2 2-1 7-1 10 0 2 0 4-1 7 0 2-1 7-1 10v3h-1 0c-1-2-2-3-2-5 1-2 0-4 0-6v-9h0c-1-2-1-4-1-5 1 1 2 2 2 3h1c0-1 0-1 1-2s0-3 0-4h1c0-2 0-3 1-4z" class="C"></path><path d="M566 618h1c0 2 1 3 2 5h0 1v-3 6 2c-1 1-1 2-1 4v2h-1v-1c-1 0-2 1-3 1v1c-1 3-1 7-2 10h-1-1c0 1 0 2-1 3 0-5 0-11 2-16 1-5 4-10 4-14h0z" class="m"></path><path d="M565 634c1-2 0-7 2-9-1 0-1 0-1-1 0-2 0-3 1-5v3l1 1h1 0 1v-3 6 2c-1 1-1 2-1 4v2h-1v-1c-1 0-2 1-3 1z" class="o"></path><path d="M575 563l-1 18c0 1-1 1-1 2v1 1 1l-1 1v4c-1 1-1 2-1 4h-1c0 1 1 3 0 4s-1 1-1 2h-1c0-1-1-2-2-3 0-2 1-5-1-6 0-1-1-2-1-3s0-1 1-1c0 1 0 1 1 1s1-1 1-2h0 1 1c0-1 0-2 1-3l2-15c0-1 0-3 1-4l2-2h0z" class="U"></path><path fill="#e19ea8" d="M557 589c1 0 2-1 4 0 0 1 1 1 1 2l2-2c0 1 1 2 1 3 2 1 1 4 1 6 0 1 0 3 1 5h0v9c0 2 1 4 0 6h-1 0l-1-3c-2 2-2 3-3 6v1c-1 1-1 3-1 5 0 1 0 3-1 5v-6-3c1-2 2-5 2-7v-16c0-3-1-5 0-7l-1-1c-1-1-3-2-4-3z"></path><path d="M562 591l2-2c0 1 1 2 1 3 2 1 1 4 1 6 0 1 0 3 1 5h0v9c0 2 1 4 0 6h-1c-1-9-1-19-4-27z" class="I"></path><path d="M547 640v-1c1-2 1-4 2-6v-1l5-16 1 1-2 7c0 1 0 2 1 3v4-3c1 2 0 3 1 4l1 1c0 3 1 7-1 10v4c-1 2-2 6-3 8 0 2-1 6 0 8h1c0-1 0-2 1-3h1v-3c1-1 0-2 1-3l-2 18h-1c-1-2-3-5-3-7h0v-5c1-1 0-4 0-6s1-3 1-5c0-3-1-7 0-11h0l-1-1c-3 3 0 7-1 10l-2-2v-2l-1-1 1-2z" class="s"></path><path d="M553 624c0 1 0 2 1 3v4-3c1 2 0 3 1 4l1 1c0 3 1 7-1 10v4c-1 2-2 6-3 8v-7h-1c1 0 1-1 1-1-2-8-1-16 1-23z" class="D"></path><path d="M566 556c2-2 2-3 5-3 2 1 3 2 4 5v5h0l-2 2c-1 1-1 3-1 4l-2 15c-1 1-1 2-1 3h-1-1 0c0 1 0 2-1 2s-1 0-1-1c0-2-1-4-2-5v-3-4-10l1-1c0-2 0-3 1-5 0-2 1-3 1-4z" class="M"></path><path d="M563 580l3 6v-2h0v-2c1-2 0-4 1-6l1-1c1 3 1 7 0 10v2h-1 0c0 1 0 2-1 2s-1 0-1-1c0-2-1-4-2-5v-3z" class="F"></path><path d="M556 590l1-1c1 1 3 2 4 3l1 1c-1 2 0 4 0 7v16c0 2-1 5-2 7-1 1-1 2-1 3l-1 1h-1v3c-1 1-1 2-1 3l-1-1c-1-1 0-2-1-4v3-4c-1-1-1-2-1-3l2-7-1-1-5 16v1c-1 2-1 4-2 6v1c-1-1-1-2-2-3 0-1 0 0-1-1v-1h-2 0l-1-1 3-9 6-21c1-4 2-11 6-14z" class="e"></path><path d="M590 203c23 1 46 2 69 7 4 1 9 3 14 4h5c6 3 13 5 19 8 3 2 5 4 8 5l1 1 2 1v-1h-1c-1-1-2-2-2-3 6 4 12 11 16 17 5 5 9 13 12 19 2 3 2 5 4 8v-4h0l2 21v1c-1-1-1-1-1-2-1-2-1-4-2-6-1 3 0 6-1 8l9 34 3 15h1v-2-6l-1-1v-4c-1-1-1-2 0-3v1c0 1 1 1 1 2v1 2c0 1 0 2 1 3v1l1 1v-2c-1-3-1-8-1-11 1-2 1-3 2-4 1 2 1 2 3 3 1 13 4 26 8 38 0 2 3 7 2 8 5 10 11 16 20 21-8 2-13 4-17 11-4 6-5 13-7 20l-6 22c-11 37-37 77-77 87l-12 2h0c6-2 12-4 17-6 21-11 38-32 47-53 5-12 11-29 6-41-2-4-4-7-8-8-2-1-5-2-7-2h-4c3-2 6-4 8-6 1-2 2-3 4-4l1-2c6-10 4-23 2-34-1-8-6-17-10-24v-1c-3 0-5-2-7-3-3-2-8-7-12-5-3 2-4 7-7 9 0 1 0 1-1 2v2h0c1 0 1 0 2 1h0 0c-1 0-2 0-2-1h-2-3c-1 1-2 1-2 2v1c-1 2-2 6-2 8-2 3-3 7-4 10l-11 34-22 76c-2 4-4 8-5 13 0 1 0 1 1 2h7c-3 2-8 4-10 6-2 3-2 6-3 9l-4 15h-1l-1 3h0c-1 1-1 2-2 3h0c-1 1-4 3-5 3l-2 1c-1-2-1-2-2-3s-1-3-1-5c-2-4-3-10-3-14h-1v-2c-1-2-1-3-1-5v-9l-1-1v2c-2-1-1-3-1-5-1-1 0-3-1-4l2-5 11-39c0-1 1-4 2-5l6-23 23-78c-1 2-1 3-2 5v-2c1-1 1-2 1-3v-1-1h0l1-1v-1-2s-1-1-2-1c0 1 0 1-1 2v-1c0 1-1 3-1 4-1 1 0 1 0 2s0 2-1 3v-1l-1-3c0 2-1 3-1 5l-2-1v-2c-1-1 0-2-1-3h-1v-2h1c0-2-1-3 0-5v-1-3-1h0v-2-3c1-4 1-9 1-13l1-1c0-1-1-3 0-5v-1-5c-1-2 0-4 0-6-1-3-1-6-2-10 0-2 0-5-1-7 0-1 1-2 0-3 0-1 0-2-1-3v-1h0v-1-3l-3-7c-1-4-3-10-5-12h-1v3h-1c-1-2-2-3-3-5-2-3-3-5-6-6-1-1-3-1-4-3h-1l-1-2c-3-6-11-11-16-16-1 0-2-1-2-1-4-3-9-5-13-7 0-1 0-1-1-2v-1h3z" class="c"></path><path d="M694 319l2-6c4 2 8 8 11 11-3 0-5-1-8-3v-1l-5-1z" class="l"></path><path d="M694 319l5 1v1c3 2 5 3 8 3l5 6c-3 0-6-2-9-3l-6-4c-1-1-3 0-5 1l2-5z" class="m"></path><path d="M650 286c-1-9-1-18-3-26 0-2 0-4-1-5l1-1v1c0 1 1 1 1 2v2l1 1c2 4 2 11 3 16 1 4 0 9 0 12-1-1-1-3-1-4v1l-1 1zm-33 211v2l1-1c1 1 1 2 1 4v1 3c-1 3 1 9 2 12-1 3 0 7-1 10-2-4-3-10-3-14v-6c-1-4-1-8 0-11z" class="E"></path><defs><linearGradient id="f" x1="688.428" y1="327.967" x2="694.718" y2="335.355" xlink:href="#B"><stop offset="0" stop-color="#7d5454"></stop><stop offset="1" stop-color="#8d6c6e"></stop></linearGradient></defs><path fill="url(#f)" d="M692 324c2-1 4-2 5-1l6 4c-2-1-4-1-6-2-1 1-2 2-2 3-1 1-2 2-2 4h1v1 1c-2 2-3 4-4 6-1 1-2 3-2 4-1 0-2 0-2 1l6-21z"></path><path d="M651 335v-2h0l1-1v-1-1-1c1-1 1-1 1-2l-1-1c0-1 0-2 1-4 1-1 0-1 0-2 1-1 1-4 1-5v-2c2-2 3-4 5-6 1-1 1-2 2-3 0-2 1-3 2-5v3 5h-1v1 1 1c-1 0-1 0-1 2v2c-1 0-1 0-1 2v2c-1 0-1 0-1 2h0v1c0 1 0 2 1 3h1v-2c-1-1 0-1 0-2l1 1 2-6v-2l1-1h0l-4 13c-2-1-2-1-3-2v-2-1-1l1-5c1-1 1-2 1-2l-1-1c-2 5-4 12-6 17 0 2-1 5-2 7z" class="K"></path><path d="M651 335c1-2 2-5 2-7 2-5 4-12 6-17l1 1s0 1-1 2l-1 5v1 1 2c1 1 1 1 3 2l-3 9c0 2-1 4-2 6s-1 3-2 5v-2c1-1 1-2 1-3v-1-1h0l1-1v-1-2s-1-1-2-1c0 1 0 1-1 2v-1c0 1-1 3-1 4-1 1 0 1 0 2s0 2-1 3v-1l-1-3c0-1 1-3 1-4z" class="L"></path><path d="M650 286l1-1v-1c0 1 0 3 1 4 1 4 1 8 0 12v3c1 3 0 8-1 11v9 3c1 2 1 1 0 2v1-1c-1-1-1 0-1-1v-2h0l-1 2h-1v6l-1 1v-2c0-1-1-4 1-5v-9c-1 1-1 2-1 3v1 1l-1 3v-2-3c1-4 1-9 1-13l1-1v3 2h0v4c1 1 1 2 1 3v-3-1c0-2 0-1 1-2v-2-3c1-1 0-3 0-4v-1c2-3 1-13 0-17z" class="K"></path><path d="M614 485l11-39v4h0c-1 2-1 3-1 4l-1 1v1c0 1 0 2-1 2v1 2c0 1 0 0-1 2h0v1l-1 1v2l-1 1v2 1 1h1s0 1-1 2c0 1 0 2-1 3v1 2c-1 2-1 3-1 5v11 1c-1 3-1 7 0 11v6h-1v-2c-1-2-1-3-1-5v-9l-1-1v2c-2-1-1-3-1-5-1-1 0-3-1-4l2-5z" class="V"></path><path d="M614 485c1 2 1 3 1 5-1 2 0 5-1 6v1 2c-2-1-1-3-1-5-1-1 0-3-1-4l2-5z" class="a"></path><path d="M728 348c0 1 1 1 1 3h0c1 1 1 2 1 4l1 1 1 4 1 4 2 8c2 9 1 20 2 30 0 4 1 7 1 10-1-2-2-4-2-7l-1-1c0-1 1-3 0-4h0v-1c0-1 0-2-1-3v-4-1 1 2h-1v1 1c0 1 0 2-1 3-1 2-1 4-3 6l-1 1 1-2c6-10 4-23 2-34l1 1 1 3v2-3c-1-1-1-5-1-6l-1-5-1-3-3-11h1z" class="f"></path><path d="M721 303c1 1 1 2 2 3 0 1 0 1 1 2v1s1 1 1 2c1 1 1 1 1 2v1l1 1v1 1l1 1v1 1l1 1h0v1 1c1 1 1 0 1 2h0v1l1 1h0v1c0 1 1 2 1 2 0 1 0 1 1 2v1h0c1 2 0 4 1 6l1 1v3 16c0 4 1 8 1 13-2-5-3-11-4-17v-1h1v-3c-1-1-1-3-1-4l-1-1c0-2 0-6-1-8v-3c-1-2 0-5-1-7v-1c0-1 0-1-1-2v-2c0-1 0-2-1-3v-3l-3-5-3-8v-1z" class="V"></path><path d="M694 332h-1c0-2 1-3 2-4 0-1 1-2 2-3 2 1 4 1 6 2 3 1 6 3 9 3l3 5 1 2h-1c-4-1-8-3-12-5h-3l-1 1c0 1-1 1-1 1-4 3-5 11-11 12l-2-1h1c0-1 1-1 2-1 0-1 1-3 2-4 1-2 2-4 4-6v-1-1z" class="j"></path><path d="M694 332h-1c0-2 1-3 2-4 0-1 1-2 2-3 2 1 4 1 6 2 3 1 6 3 9 3l3 5c-2 0-4-1-6-3-4-2-7-3-11-4-1 2-3 3-4 4z" class="o"></path><path d="M685 345l2 1c6-1 7-9 11-12 0 0 1 0 1-1l1-1h3c4 2 8 4 12 5h1l3 4 2 5v-1c-3 0-5-2-7-3-3-2-8-7-12-5-3 2-4 7-7 9 0 1 0 1-1 2h0c-3 1-4 0-6-1-1 0-2 0-4 1l1-3z" class="k"></path><path d="M688 347c1 0 4 0 5-2h0c2-2 5-5 6-8 0-1 2-1 2-2 5-1 13 4 18 6l2 5v-1c-3 0-5-2-7-3-3-2-8-7-12-5-3 2-4 7-7 9 0 1 0 1-1 2h0c-3 1-4 0-6-1z" class="Z"></path><defs><linearGradient id="g" x1="630.662" y1="508.454" x2="623.532" y2="498.171" xlink:href="#B"><stop offset="0" stop-color="#2c282c"></stop><stop offset="1" stop-color="#464448"></stop></linearGradient></defs><path fill="url(#g)" d="M619 506c2 1 2 1 4 0 2-2 1-3 2-5l-1-1c0 2 0 2-1 3-2 0-2-1-3-2 0-2 0-2 1-4h1c0-2-1-3-1-5-1-3-3-9-3-12h1c0 5 2 11 4 16 7 2 14 2 22 3-3 1-6 2-8 3-7 3-13 9-16 16-1-3-3-9-2-12z"></path><defs><linearGradient id="h" x1="692.398" y1="229.854" x2="692.581" y2="217.145" xlink:href="#B"><stop offset="0" stop-color="#c0c0c1"></stop><stop offset="1" stop-color="#e5e3e4"></stop></linearGradient></defs><path fill="url(#h)" d="M673 214h5c6 3 13 5 19 8 3 2 5 4 8 5l1 1 2 1v-1h-1c-1-1-2-2-2-3 6 4 12 11 16 17h-2c-2-3-6-4-9-6-1-1-3-3-4-3h0l-1 1c2 2 3 3 4 5l4 4c0 1 1 2 1 3v1c-4-6-9-12-14-16-8-7-17-13-27-17z"></path><defs><linearGradient id="i" x1="733.811" y1="255.28" x2="711.773" y2="257.809" xlink:href="#B"><stop offset="0" stop-color="#5a575c"></stop><stop offset="1" stop-color="#878589"></stop></linearGradient></defs><path fill="url(#i)" d="M714 247v-1c0-1-1-2-1-3l-4-4c-1-2-2-3-4-5l1-1h0c1 0 3 2 4 3 3 2 7 3 9 6h2c5 5 9 13 12 19 2 3 2 5 4 8v-4h0l2 21v1c-1-1-1-1-1-2-1-2-1-4-2-6-1 3 0 6-1 8-3-6-5-11-7-16-4-9-9-17-14-24z"></path><path d="M661 293l15 1c1-2 2-4 4-5 2-3 5-4 8-4 2 2 4 4 6 7l2 3h1c1 2 3 5 5 7v1c1 1 1 2 2 4l4 4 11 13 7 11c0 2 1 3 1 4 2 4 4 10 5 15v1l-1 1-1-1c0-2 0-3-1-4h0c0-2-1-2-1-3h-1c-4-11-10-21-17-31-10-12-23-19-39-21-6 0-10 1-15 4 2-2 3-4 5-7z" class="X"></path><path d="M697 302c3 1 5 3 7 5l4 4 11 13 7 11c0 2 1 3 1 4 2 4 4 10 5 15v1l-1 1-1-1c0-2 0-3-1-4h0c0-2-1-2-1-3-2-4-3-9-5-13-7-13-15-23-26-33z" class="E"></path><path d="M661 293l15 1c1-2 2-4 4-5 2-3 5-4 8-4 2 2 4 4 6 7l2 3h1c1 2 3 5 5 7v1c1 1 1 2 2 4-2-2-4-4-7-5h-1c-1-1-1-1-2-1l-2-2-1-1h-2c-1-1-1-1-2-1h-1l-3-1c-1-1-3-1-5-1-2-1-5-1-7-1-2 1-3 1-4 1h0c2 0 3 0 4 1-6 0-10 1-15 4 2-2 3-4 5-7z" class="d"></path><defs><linearGradient id="j" x1="757.741" y1="339.131" x2="749.354" y2="340.85" xlink:href="#B"><stop offset="0" stop-color="#242324"></stop><stop offset="1" stop-color="#3f3c42"></stop></linearGradient></defs><path fill="url(#j)" d="M747 336h1v-2-6l-1-1v-4c-1-1-1-2 0-3v1c0 1 1 1 1 2v1 2c0 1 0 2 1 3v1l1 1v-2c-1-3-1-8-1-11 1-2 1-3 2-4 1 2 1 2 3 3 1 13 4 26 8 38 0 2 3 7 2 8v1l-1-2h-1l1 1h-1c0 1 1 1 0 2-1 3 0 8 0 11l-1 9c0 2 1 4 0 5v-29h2c-1-1-1-2-1-3-1-1 0-1-1-1l-2 4h0l-3 3c-1 2 0 6-1 8 0-5 0-9-1-14 0-3-1-6-2-8v-1-1c0-1-1-1-1-2v4h0c0 4 0 8 1 12v9c0 13 0 27-3 41 0 2 0 6-1 8l-1-1v-1c2-10 3-20 3-31v-2-15-2c0-7 0-13-2-20v-8c-1-1-1-3-1-4z"></path><path d="M660 258l-5-9c-2-4-4-6-6-9 2 1 4 3 6 5 0 0 0 1 1 1 0 1 0 1 1 2 0 1 1 1 1 2l1-1c0-1 1-2 2-2l1 1h1 1l2 1 9 2c1 1 2 1 3 1 2 2 4 3 7 4 1 1 3 1 4 2l-1 1 2 1 2 2c2 1 4 4 6 5s5 4 5 6l2 2c1 0 2 1 2 2l1 1 2 2c0 1 1 1 1 2 1 0 1 0 1 1l1 1c0-1-1-1-1-2v-1l-3-3v-1l-1-1c-1-1-1-2-2-3s-2-1-2-2c-2-1 0 0-1-2-2-2-5-3-6-6h-1l-1-1v-1c17 16 29 37 36 59h-1c-1-1-1-3-2-5v-1c0-1-1-1-1-2v-1l-1-1v-1c-1-2-2-5-3-7v-1l-4-8h-1l1 2c1 1 1 1 1 2v1c0 1 0 0 1 2v1l1 1-1 1v1c-1-1-1-2-2-3 0-1 0-1-1-2s-1-3-2-4l-3-6c-2-2-4-4-5-6-5-6-10-12-16-17v1l-1-1c-2-1-2-1-4-1 0 0-1 0-2-1h0l-1 1-2-2c-2-1-4-3-6-3-1-1-3-2-5-3v1c1 0 1 1 2 1h1v1h-1-1c-3 0-5 2-8 2v-2h-1c-1-1-1-2-3-2z" class="p"></path><path d="M684 348c2-1 3-1 4-1 2 1 3 2 6 1h0v2h0c1 0 1 0 2 1h0 0c-1 0-2 0-2-1h-2-3c-1 1-2 1-2 2v1c-1 2-2 6-2 8-2 3-3 7-4 10l-11 34-22 76c-2 4-4 8-5 13 0 1 0 1 1 2h-5l45-148z" class="J"></path><path d="M676 260c2 0 4 2 6 3l2 2 1-1h0c1 1 2 1 2 1 2 0 2 0 4 1l1 1v-1c6 5 11 11 16 17 1 2 3 4 5 6l3 6c1 1 1 3 2 4s1 1 1 2c1 1 1 2 2 3l3 8 3 5v3c1 1 1 2 1 3v2c1 1 1 1 1 2v1c1 2 0 5 1 7v3c1 2 1 6 1 8l1 1c0 1 0 3 1 4v3h-1c-1-5-3-11-5-15 0-1-1-2-1-4l-7-11-11-13v-3c-2-2-1-4-2-7-2-7-5-12-9-17-2-1-3-3-4-4-2-2-3-3-5-4h5c0-2-6-6-7-7v-2c-1 0-1-1-2-1-2-1-3-2-4-3s-2-2-4-3z" class="R"></path><path d="M708 311v-3c-2-2-1-4-2-7-2-7-5-12-9-17-2-1-3-3-4-4-2-2-3-3-5-4h5c0-2-6-6-7-7v-2l6 6 4 4h1c0 1 0 1 1 2 1 2 4 3 5 6 2 2 4 4 5 6 4 5 7 11 10 17 3 5 4 11 4 17 1 3 4 8 4 10l-7-11-11-13z" class="b"></path><path d="M719 324c0-2-1-4-1-6-2-7-6-14-8-22 2 3 4 6 5 9 1 1 1 2 2 3h1c3 5 4 11 4 17 1 3 4 8 4 10l-7-11z" class="W"></path><defs><linearGradient id="k" x1="677.84" y1="260.225" x2="676.87" y2="274.65" xlink:href="#B"><stop offset="0" stop-color="#4e4d52"></stop><stop offset="1" stop-color="#7d7b7f"></stop></linearGradient></defs><path fill="url(#k)" d="M660 258c2 0 2 1 3 2h1v2c3 0 5-2 8-2h1 1v-1h-1c-1 0-1-1-2-1v-1c2 1 4 2 5 3 2 1 3 2 4 3s2 2 4 3c1 0 1 1 2 1v2c1 1 7 5 7 7h-5c2 1 3 2 5 4 1 1 2 3 4 4 4 5 7 10 9 17 1 3 0 5 2 7v3l-4-4c-1-2-1-3-2-4v-1c-2-2-4-5-5-7h-1l-2-3c-2-3-4-5-6-7-3 0-6 1-8 4-2 1-3 3-4 5l-15-1c4-7 4-13 3-21l-2-10-2-4z"></path><path d="M662 262l1-1c1 3 1 5 2 8l1 1 4-1h2l2 1 1 2s0 1 1 2v1c-1-1-2-1-3-1h-1-5l-3-2-2-10z" class="h"></path><path d="M666 270l4-1h2l2 1c-1 1 0 1-1 1v1c-1-1-2-1-3-1s-2 0-3 1h-1v-2z" class="b"></path><path d="M679 275c9 6 21 14 24 25h-1 0c-2-2-3-4-5-5h-1l-2-3c-2-3-4-5-6-7v-1c0-3-8-6-9-9z" class="X"></path><defs><linearGradient id="l" x1="666.74" y1="274.867" x2="676.374" y2="292.293" xlink:href="#B"><stop offset="0" stop-color="#918e92"></stop><stop offset="1" stop-color="#e8e8e8"></stop></linearGradient></defs><path fill="url(#l)" d="M675 272c1 1 3 2 4 3 1 3 9 6 9 9v1c-3 0-6 1-8 4-2 1-3 3-4 5l-15-1c4-7 4-13 3-21l3 2h5 1c1 0 2 0 3 1v-1c-1-1-1-2-1-2z"></path><path d="M586 580l26-90c1 1 0 3 1 4 0 2-1 4 1 5v-2l1 1v9c0 2 0 3 1 5v2h1c0 4 1 10 3 14 0 2 0 4 1 5s1 1 2 3l2-1c1 0 4-2 5-3h0c1-1 1-2 2-3h0l1-3h1c-2 6-4 12-5 19 1 1 2 3 4 5l8 12c4 8 8 16 11 25 0 1 1 3 1 5 0 1-1 1-1 2 3 12 4 26 2 39l-2 7v-2c2-7 2-16 1-23-1 2-1 3-1 5 1 1 1 4 1 5-1 3 0 5-1 7l-1 1h-1v1c-1-1 0-6-1-8 0-2 0-4-1-7 0-4-2-7-3-11 0-1-1-2-1-3h-1c0-1-1-3-2-4-1-3-2-5-4-7-1 2 0 2 0 4-1 1-2 1-2 2h-1c-1 0-1 0-1 1-1 0-2 1-3 2h0v4l1 1c-1 3-2 4-4 6-4 2-7 1-11 2l-1 1c-3 0-8-1-10 1-3 4-5 9-7 14 0 1-1 3-2 5l-72 246-5 19-4 14-87-295v-1h-1l-2-1v-1l-2-1-2-2c-2-1-5 1-7 1-2-1-5-2-6-3-1-4-2-6-2-10l-2-2c-1-1-1-1-2-1l-1-1h0c0-3 1-6 1-8-3 1-5 3-7 5-5 6-8 16-9 24l-1 8-1-1c0-1-1-1 0-2v-2l-1 1v2c-1 1-1 3-1 4-1 2 0 4-1 6l-1-1v-2-5l-1-1c-2-3-1-13-1-17 2-8 3-17 6-25 1-4 3-7 4-11v-1c2-7 11-13 14-20v-1c1-2 1-4 2-5-2-5-4-10-5-15l-7-21c0-1-1-6-2-7s-4-3-5-3c-3-1-5-3-8-5h13c3 2 7 0 10 0l1 1h1 1c2 0 4-3 6-4l1-1h2c3 4 4 11 5 16l1-1c1 2 1 3 2 5 0 1 0 2 1 2h1v3l1 1v1l1 1h0l2 3v2l3 9c0 1 1 2 1 2v2s1 1 1 2l3 9 2 10c1 1 1 3 2 4v2c2 4 3 8 4 12 2 5 4 9 4 14 2 4 3 7 4 11l8 24c0 1 2 2 2 2 2 3 2 7 3 10v1 4l27 97 24 90 3-15 4-13c0-7-1-14 0-21 0-8 2-16 3-23 1-13 3-26 5-39 1-2 1-4 1-6l1-4s0-1 1-2v1l1-1-1-1c1 0 1-1 1-1v-1c1-2 1-3 2-4-1 1-1 2-2 3h0v-2c1-4 2-6 2-9v-2-1l-1-1 1-3v-1c0-3 1-4 3-6 0-1 1-2 2-2v-3l-1-1c0-1 0-1-1-2v-1c-1-1-1-1-1-3h0v-1-1-2l-1-1s1-1 0-2v2c-1 0-1 1-2 1v-2h0l-1-1c0-1 1-2 1-4l1-2c0-1 0-2 1-3v-1c1-3 0-2-1-4 0-3 1-5 0-7v-1c1-2 2-4 2-5l3-11v1c1 1 1 2 1 3h2 1l-3 9 1 1h0 2v1c1 1 1 0 1 1 1 1 1 2 2 3l-1 2 1 1v2l2 2c1-3-2-7 1-10l1 1h0c-1 4 0 8 0 11 0 2-1 3-1 5s1 5 0 6v5h0c0 2 2 5 3 7h1c-1 3 0 5-1 7l2 3 2-6v-2c1-5 3-9 4-13 1-6 2-12 5-17h-1 0c2-2 3-2 5-3l1-1c1-2 1-2 0-4 1-2 2-5 3-7 0-1 0-3 1-3-1-2-1-3-1-5v-1c1-1 1-2 1-2v-2l1-3 1-5c1-5 3-11 5-16 1-4 2-8 4-12z" class="n"></path><path d="M601 575c2-1 6-4 8-3v3c-2 1-5 0-7 1l-1-1z" class="a"></path><path d="M571 640c1-2 1-2 0-4 1-2 2-5 3-7v1c0 4 1 6 3 9 0 1 1 2 1 2-3 0-5 0-7-1z" class="t"></path><path d="M584 600l-9 26c-1-2-1-3-1-5v-1c1-1 1-2 1-2v-2l1-3 1-5c1 0 1-1 2-2 2-2 2-4 5-6z" class="a"></path><defs><linearGradient id="m" x1="623.917" y1="572.212" x2="617.791" y2="576.745" xlink:href="#B"><stop offset="0" stop-color="#39373c"></stop><stop offset="1" stop-color="#5a565c"></stop></linearGradient></defs><path fill="url(#m)" d="M609 572l5-1c4 0 9 0 14 1h0c3 1 7 3 9 5h0c-1 0-2 0-3-1h-1l-3-1-1 2c-1-1-1-2-2-2v1 1 1h-1l-17-3v-3z"></path><path d="M607 600c1-5 3-10 5-15l9 1c-2 2-2 5-3 7-2 4-7 7-11 7z" class="D"></path><path d="M414 587h2l1 1 5 15 1 3c-2 0-5-1-7-3-2-1-3-3-3-5v-3l-1-1c1-2 0-4 0-6h-1l-1-1h4z" class="u"></path><path d="M414 587h2c0 5 3 9 3 14-1-1-3-3-4-5v-1c-1-3-1-5-1-8z" class="m"></path><path d="M416 587l1 1 5 15c-1 0-2 0-3-2 0-5-3-9-3-14z" class="s"></path><path d="M396 583c2-2 3-3 6-4 4-2 9-3 13-4 3 3 9 1 13 3h-3c-1 0-1 0-2 1l3 1h1-1c-2 0-5-1-7 0-2 0-3 1-5 1s0 0-2 1h-1l-1 1c-2 0-4 1-7 1-6 2-9 6-14 10 2-2 3-3 4-5 0-1 0-1 1-2v-1c1-1 1-2 2-3z" class="R"></path><path d="M396 583c4-2 10-4 15-4-3 2-6 4-9 5-2 0-3 1-4 2-2 1-3 2-5 3 0-1 0-1 1-2v-1c1-1 1-2 2-3z" class="N"></path><defs><linearGradient id="n" x1="407.361" y1="573.769" x2="419.38" y2="582.914" xlink:href="#B"><stop offset="0" stop-color="#171718"></stop><stop offset="1" stop-color="#353238"></stop></linearGradient></defs><path fill="url(#n)" d="M396 583c2-2 3-3 6-4 4-2 9-3 13-4 3 3 9 1 13 3h-3c-1 0-1 0-2 1h-12c-5 0-11 2-15 4z"></path><path d="M409 493v1c-1 2-1 3-3 5h0c2 0 2 0 4-1 0-1 1-1 1-1 2 1 1 3 1 4h1v1c-1 1-3 2-4 3-1-1-2-1-3-2-2 0-4-1-6 0l-3-1c-3 0-6-2-8-2-2-1-3 0-4 1-3-1-5-3-8-5h13c3 2 7 0 10 0l1 1h1 1c2 0 4-3 6-4z" class="N"></path><path d="M410 535v3c-2 4-2 8-4 12v1c-5 9-11 17-16 27-5 9-10 22-11 33-1 5 0 11 0 16-2-3-1-13-1-17 2-8 3-17 6-25 1-4 3-7 4-11v-1c2-7 11-13 14-20v-1c1-2 1-4 2-5 3-4 5-8 6-12zm206-21h1c0 4 1 10 3 14 0 2 0 4 1 5 4 13 13 24 19 36 5 8 9 16 12 25 3 12 4 26 2 39l-2 7v-2c2-7 2-16 1-23 0-5-1-10-2-14-4-15-11-27-18-40-4-6-7-11-10-18-4-8-5-15-6-24 0-2-1-4-1-5z" class="d"></path><defs><linearGradient id="o" x1="634.838" y1="530.196" x2="639.858" y2="593.463" xlink:href="#B"><stop offset="0" stop-color="#161515"></stop><stop offset="1" stop-color="#5a595f"></stop></linearGradient></defs><path fill="url(#o)" d="M621 533c1 1 1 1 2 3l2-1c1 0 4-2 5-3h0c1-1 1-2 2-3h0l1-3h1c-2 6-4 12-5 19 1 1 2 3 4 5l8 12c4 8 8 16 11 25 0 1 1 3 1 5 0 1-1 1-1 2-3-9-7-17-12-25-6-12-15-23-19-36z"></path><path d="M607 600c4 0 9-3 11-7 1-2 1-5 3-7l2 1c1 0 2 1 3 0l2 1c-1 3-2 7-2 10-2 3-4 5-7 7-1 1-2 2-4 2l-12 5 1-3 2-6c0-1 0-2 1-3z" class="u"></path><path d="M623 587c1 0 2 1 3 0v1c-2 3-2 6-2 9-3 3-6 7-10 8 0-1 1-2 1-2l1-1c4-3 5-6 6-11 1-1 1-3 1-4z" class="m"></path><path d="M622 591c-1 5-2 8-6 11l-1 1s-1 1-1 2c-3 0-7 3-10 4l2-6h0 1c3 0 9-3 11-5 1-2 2-4 4-6v-1z" class="s"></path><path d="M607 600c4 0 9-3 11-7 1-2 1-5 3-7l2 1c0 1 0 3-1 4v1c-2 2-3 4-4 6-2 2-8 5-11 5h-1 0c0-1 0-2 1-3z" class="l"></path><path d="M397 502l3 1c2-1 4 0 6 0 1 1 2 1 3 2h0c1 1 2 1 4 2l1-1v2 10h0c2-3 0-11 1-13 1 2 1 4 1 6 0 8 0 17-3 25h0c-2 7-4 13-7 19-4 7-9 14-12 21-6 10-12 24-13 37h-1v-2-1c2-10 5-20 10-30 2-4 4-8 7-12 2-4 5-8 7-13 1-1 2-2 2-4v-1c2-4 2-8 4-12v-3-2-1c-1-2 0-4 0-6 0-3 0-6-2-9-1-3-3-5-6-8-2-2-4-4-5-7z" class="W"></path><path d="M397 502l3 1c3 1 8 4 10 7 1 3 1 5 2 8 1 4 0 11-2 15v-1c-1-2 0-4 0-6 0-3 0-6-2-9-1-3-3-5-6-8-2-2-4-4-5-7z" class="E"></path><path d="M408 588l2-1 1 1h1c0 2 1 4 0 6l1 1v3c0 2 1 4 3 5 2 2 5 3 7 3l1 3 1 3 1 2 2 6h-1l-2-1v-1l-2-1-2-2c-2-1-5 1-7 1-2-1-5-2-6-3-1-4-2-6-2-10l-2-2c-1-1-1-1-2-1l-1-1h0c0-3 1-6 1-8 1-2 3-2 5-3h1z" class="Z"></path><path d="M407 588h1l1 1v4c0 1-1 2-1 3-1 3 1 8 3 11 5 3 8 4 14 5l1 2h-4c-6 0-10-1-14-5v-4c-2-4-1-9-1-13 0-1 1-2 0-3v-1z" class="k"></path><path d="M408 588l2-1 1 1h1c0 2 1 4 0 6l1 1v3c0 2 1 4 3 5 2 2 5 3 7 3l1 3 1 3c-6-1-9-2-14-5-2-3-4-8-3-11 0-1 1-2 1-3v-4l-1-1z" class="j"></path><path d="M411 588h1c0 2 1 4 0 6l1 1v3c0 2 1 4 3 5 2 2 5 3 7 3l1 3-5-1c-3-1-4-3-7-5 0 0-1-1-1-2s1-3 0-5l-1-1c1-2 1-5 1-7z" class="o"></path><path d="M628 588l1 1 2 1c2 1 5 2 6 4-1 2 0 2 0 4-1 1-2 1-2 2h-1c-1 0-1 0-1 1-1 0-2 1-3 2h0v4l1 1c-1 3-2 4-4 6-4 2-7 1-11 2l-1 1c-3 0-8-1-10 1-3 4-5 9-7 14 0 1-1 3-2 5 1-5 3-11 4-16l2-5 1-2v-2l12-5c2 0 3-1 4-2 3-2 5-4 7-7 0-3 1-7 2-10z" class="k"></path><path d="M630 607l1 1c-1 3-2 4-4 6-4 2-7 1-11 2l-1 1c-3 0-8-1-10 1-3 4-5 9-7 14 0 1-1 3-2 5 1-5 3-11 4-16 2-1 4-4 6-5 4-2 9-1 14-2 4-1 8-3 10-7z" class="Z"></path><path d="M628 588l1 1 2 1h0c-1 2-1 3-1 5 0 7-1 13-9 16-1 0-2 1-4 1-4 1-9 1-13 3-1 1-1 1-2 1l1-2v-2l12-5c2 0 3-1 4-2 3-2 5-4 7-7 0-3 1-7 2-10z" class="j"></path><path d="M628 588l1 1c-1 6 0 13-6 17-1 1-1 1-2 1-2 1-3 2-5 2l-13 5v-2l12-5c2 0 3-1 4-2 3-2 5-4 7-7 0-3 1-7 2-10z" class="o"></path><defs><linearGradient id="p" x1="431.949" y1="577.463" x2="432.037" y2="596.55" xlink:href="#B"><stop offset="0" stop-color="#48464b"></stop><stop offset="1" stop-color="#7d7b7f"></stop></linearGradient></defs><path fill="url(#p)" d="M415 575c8 0 12 1 18 4 2 1 5 2 6 4 1 1 2 3 3 4s2 1 2 2c1 1 1 2 2 2 2 4 3 7 4 11l8 24c0 1 2 2 2 2 2 3 2 7 3 10v1 4h0-1c-1-1-2-3-3-5v-1-2c-1-1-1-2-1-3-1 1-1 1-1 3-1 1-2 4-3 5-1 0-3 0-4 1 3-4 5-8 4-12 0-4-1-8-3-12-4-11-7-21-17-29-7-6-15-6-24-5l1-1h1c2-1 0-1 2-1s3-1 5-1c2-1 5 0 7 0h1-1l-3-1c1-1 1-1 2-1h3c-4-2-10 0-13-3z"></path><path d="M458 626c0 1 2 2 2 2 2 3 2 7 3 10v1 4h0l-5-17z" class="a"></path><defs><linearGradient id="q" x1="611.673" y1="574.801" x2="576.779" y2="600.482" xlink:href="#B"><stop offset="0" stop-color="#626064"></stop><stop offset="1" stop-color="#767578"></stop></linearGradient></defs><path fill="url(#q)" d="M586 580c1-1 2-4 3-5h0c0 1 0 3 1 4 1-1 2-1 3-1l-2 2h-1c0 1-1 1-1 2v1c4-4 7-6 12-8l1 1c2-1 5 0 7-1l17 3h1v-1h3 1 0c-1 1-1 2-3 2 2 0 3 1 5 2s5 3 6 6c2 2 3 4 4 7h0c-6-8-15-11-24-13-8-1-17 0-23 5-5 3-10 9-12 14-3 2-3 4-5 6-1 1-1 2-2 2 1-5 3-11 5-16 1-4 2-8 4-12z"></path><path d="M586 580c1-1 2-4 3-5h0c0 1 0 3 1 4 1-1 2-1 3-1l-2 2h-1c-2 1-4 2-4 4-1 1 0 1 0 2-1 1-2 3-3 5l-1 1h0c1-4 2-8 4-12z" class="W"></path><path d="M615 578c4 0 7 0 11 1h2c2 0 3 1 5 2s5 3 6 6c-6-4-12-5-19-7-1-1-3-2-5-2z" class="f"></path><defs><linearGradient id="r" x1="612.445" y1="581.576" x2="606.055" y2="572.424" xlink:href="#B"><stop offset="0" stop-color="#221f23"></stop><stop offset="1" stop-color="#3e3d41"></stop></linearGradient></defs><path fill="url(#r)" d="M609 575l17 3h1v-1h3 1 0c-1 1-1 2-3 2h-2c-4-1-7-1-11-1s-9-1-13 0c-3 1-7 2-10 4 3-3 6-5 10-6 2-1 5 0 7-1z"></path><path d="M385 501c1-1 2-2 4-1 2 0 5 2 8 2 1 3 3 5 5 7 3 3 5 5 6 8 2 3 2 6 2 9 0 2-1 4 0 6v1 2c-1 4-3 8-6 12-2-5-4-10-5-15l-7-21c0-1-1-6-2-7s-4-3-5-3z" class="c"></path><defs><linearGradient id="s" x1="417.643" y1="553.94" x2="432.205" y2="549.886" xlink:href="#B"><stop offset="0" stop-color="#38343c"></stop><stop offset="1" stop-color="#68666a"></stop></linearGradient></defs><path fill="url(#s)" d="M417 508l1-1c1 2 1 3 2 5 0 1 0 2 1 2h1v3l1 1v1l1 1h0l2 3v2l3 9c0 1 1 2 1 2v2s1 1 1 2l3 9 2 10c1 1 1 3 2 4v2c2 4 3 8 4 12 2 5 4 9 4 14-1 0-1-1-2-2 0-1-1-1-2-2 0-2-1-3-1-4-5-7-15-12-23-13h-3v-1l2-1-2-1v-2l1 1v-4l-1-1v-1c-1-3 0-7 0-10 1-2 0-4 0-5l1-1v-2-3h0c1-1 1-2 1-3 1-2 0-3 1-5h0v-2c1-3 0-6 1-9v-1l-2-11z"></path><path d="M419 519v1c1 0 1 0 1 1 2 3 3 6 3 9 0 1 0 2 1 2 0 2-1 2-1 3s1 2 1 2c1 3 1 5 1 8h-1v-2c0-1 0 0-1-1v-1l-1-3v-2l-1-1v-2l-1-1v-1l-1-1v-10-1z" class="a"></path><path d="M419 520v10c0 12 0 25-1 38 2 1 5 1 7 2-2 0-5-1-7 0h-3v-1l2-1-2-1v-2l1 1v-4l-1-1v-1c-1-3 0-7 0-10 1-2 0-4 0-5l1-1v-2-3h0c1-1 1-2 1-3 1-2 0-3 1-5h0v-2c1-3 0-6 1-9z" class="E"></path><defs><linearGradient id="t" x1="620.329" y1="542.063" x2="598.424" y2="534.909" xlink:href="#B"><stop offset="0" stop-color="#3f3c44"></stop><stop offset="1" stop-color="#5e5c60"></stop></linearGradient></defs><path fill="url(#t)" d="M586 580l26-90c1 1 0 3 1 4 0 2-1 4 1 5v-2l1 1v9c0 2 0 3 1 5v2c0 1 1 3 1 5h-1c-2 9 3 19 0 27l1 3h-1 0c-1-1-1 0-1-1l-1 2c-1 0-1 0-1-1l-2 2c3 5 7 8 5 14-1 1-1 2-2 3h-1c-4 1-9 3-13 5-1 1-6 5-7 5s-2 0-3 1c-1-1-1-3-1-4h0c-1 1-2 4-3 5z"></path><path d="M614 497l1 1v9c0 2 0 3 1 5v2c0 1 1 3 1 5h-1c-2 9 3 19 0 27l1 3h-1 0c-1-1-1 0-1-1l-1 2c-1 0-1 0-1-1l-2 2h0s-1-1-1-2c0-2 1-5 2-7 1-6 2-12 2-17v-11-15-2z" class="p"></path><path d="M535 637c1-2 2-4 2-5l3-11v1c1 1 1 2 1 3h2 1l-3 9 1 1h0 2v1c1 1 1 0 1 1 1 1 1 2 2 3l-1 2 1 1v2l2 2c1-3-2-7 1-10l1 1h0c-1 4 0 8 0 11 0 2-1 3-1 5s1 5 0 6v5h0c0 2 2 5 3 7h1c-1 3 0 5-1 7l2 3 2-6v-2c1-5 3-9 4-13 1-6 2-12 5-17h2l-40 138-9 33c-2 7-3 13-5 19l-52-186c-1-2-1-4-4-5-4-1-7 0-12 2l4-4c1-1 3-1 4-1 1-1 2-4 3-5 0-2 0-2 1-3 0 1 0 2 1 3v2 1c1 2 2 4 3 5h1 0l27 97 24 90 3-15 4-13c0-7-1-14 0-21 0-8 2-16 3-23 1-13 3-26 5-39 1-2 1-4 1-6l1-4s0-1 1-2v1l1-1-1-1c1 0 1-1 1-1v-1c1-2 1-3 2-4-1 1-1 2-2 3h0v-2c1-4 2-6 2-9v-2-1l-1-1 1-3v-1c0-3 1-4 3-6 0-1 1-2 2-2v-3l-1-1c0-1 0-1-1-2v-1c-1-1-1-1-1-3h0v-1-1-2l-1-1s1-1 0-2v2c-1 0-1 1-2 1v-2h0l-1-1c0-1 1-2 1-4l1-2c0-1 0-2 1-3v-1c1-3 0-2-1-4 0-3 1-5 0-7v-1z" class="T"></path><path d="M530 768c0-10 4-22 7-31 2-3 2-7 5-10l-1 5-2 4-3 14c-1 3-3 6-3 9 0 1-1 1-1 2l-2 7zm20-78c0-2 0-4 1-6h1v-1c0-1 1-2 1-4l2 3h0c-1 3-2 6-3 10l-3 11-2 7h-1l1-19h0l1 5v-2l1 1h0c1-2 1-3 1-5z" class="h"></path><path d="M527 772c2-5 2-10 3-15s2-9 4-14c1-4 2-9 4-13s4-8 5-13c0-2 1-3 2-5 0 1 1 3 0 5h0v2l-1 3-1 3-1 2c-3 3-3 7-5 10-3 9-7 21-7 31l-4 15c0-4 0-7 1-11z" class="Z"></path><path d="M547 645l2 2c1-3-2-7 1-10l1 1h0c-1 4 0 8 0 11 0 2-1 3-1 5s1 5 0 6v5h0c0 2 2 5 3 7h1c-1 3 0 5-1 7 0 2-1 3-1 4v1h-1c-1 2-1 4-1 6s0 3-1 5h0l-1-1v2l-1-5h0l-2-20c0-2-1-4-1-6l-1-5c-1-2-1-3-1-4l-1-1h-1s0-2-1-2c0-1 1-3 0-4l-1-1 1-1c1-1 1-3 1-5 0-1 1-2 3-3 1 1 2 1 3 3l1 1v2z" class="t"></path><path d="M545 671l2 1c1 2 1 5 1 7 0 3 1 5 1 8 0 1 0 2 1 3 0 2 0 3-1 5h0l-1-1v2l-1-5h0l-2-20z" class="b"></path><path d="M539 647c1-1 1-3 1-5 0-1 1-2 3-3 1 1 2 1 3 3l1 1v2c-1 3-1 11 0 14l1 1v1 3l-1 1h-3l-1-5c-1-2-1-3-1-4l-1-1h-1s0-2-1-2c0-1 1-3 0-4l-1-1 1-1z" class="u"></path><path d="M539 647c1-1 1-3 1-5 0-1 1-2 3-3 1 1 2 1 3 3l1 1-1 1v4c0 1-1 3-1 4s0 1-1 2c1 0 1 1 1 2l-1-1-1-2c-2-2-3-4-4-6z" class="L"></path><defs><linearGradient id="u" x1="529.948" y1="697.35" x2="545.959" y2="702.788" xlink:href="#B"><stop offset="0" stop-color="#323035"></stop><stop offset="1" stop-color="#7d7b7e"></stop></linearGradient></defs><path fill="url(#u)" d="M535 637c1-2 2-4 2-5l3-11v1c1 1 1 2 1 3h2 1l-3 9 1 1h0 2v1c1 1 1 0 1 1 1 1 1 2 2 3l-1 2c-1-2-2-2-3-3-2 1-3 2-3 3 0 2 0 4-1 5l-1 1 1 1c1 1 0 3 0 4 1 0 1 2 1 2 1 3 2 6 2 10v5c0 2 1 6 1 8l2 24v10c-1 2-2 3-2 5-1 5-3 9-5 13s-3 9-4 13c-2 5-3 9-4 14s-1 10-3 15c-1 4-1 7-1 11 0 1-1 2-1 3-1 5-2 11-4 16 0-7-1-14 0-21 0-8 2-16 3-23 1-13 3-26 5-39 1-2 1-4 1-6l1-4s0-1 1-2v1l1-1-1-1c1 0 1-1 1-1v-1c1-2 1-3 2-4-1 1-1 2-2 3h0v-2c1-4 2-6 2-9v-2-1l-1-1 1-3v-1c0-3 1-4 3-6 0-1 1-2 2-2v-3l-1-1c0-1 0-1-1-2v-1c-1-1-1-1-1-3h0v-1-1-2l-1-1s1-1 0-2v2c-1 0-1 1-2 1v-2h0l-1-1c0-1 1-2 1-4l1-2c0-1 0-2 1-3v-1c1-3 0-2-1-4 0-3 1-5 0-7v-1z"></path><path d="M521 781c1-1 1-1 1-2l4-11 2-5h0l-1 9c-1 4-1 7-1 11 0 1-1 2-1 3-1 5-2 11-4 16 0-7-1-14 0-21z" class="h"></path><path d="M535 637c1-2 2-4 2-5l3-11v1c1 1 1 2 1 3h2 1l-3 9 1 1h0 2v1c1 1 1 0 1 1 1 1 1 2 2 3l-1 2c-1-2-2-2-3-3-2 1-3 2-3 3 0 2 0 4-1 5l-1 1 1 1c1 1 0 3 0 4 1 0 1 2 1 2 1 3 2 6 2 10 0 1 0 2-1 3 0 1 1 0 0 1h-1-1l-1-2 1-2c-1-2-2-5-2-7 1-1 1-1 1-2v-1l-1 1-2 2-1 2h0l-1-1c0-1 1-2 1-4l1-2c0-1 0-2 1-3v-1c1-3 0-2-1-4 0-3 1-5 0-7v-1z" class="D"></path><path d="M535 637c1-2 2-4 2-5l3-11v1c1 1 1 2 1 3h2 1l-3 9-1 2c-1 4-2 9-3 13-1 2-1 5-3 6l1-2c0-1 0-2 1-3v-1c1-3 0-2-1-4 0-3 1-5 0-7v-1z" class="O"></path><path d="M362 213l12-3c24-5 48-6 72-8 12 0 25-2 38-1h1 9v1h-1c-1 1-1 1-3 2h-2l-1 1-4 1c-1 0-2 1-4 2 0 1-1 1-2 2l-3 2c-2 2-3 1-4 1v-1h-1c-1 2-3 4-4 7h0l-5 11 1 1c1-1 2-1 3-2l-1 2c0 2-1 3-1 5l-3 2h0v-2c-3 0-5 3-6 5-4 6-6 11-8 17-1 4-1 8-2 12-1-1-1-2-1-3-1-2 0-6-1-7l-1 3c0 1-1 2-1 3-1 1-1 5-1 7h0c-2 1-2 1-3 0l-1 1c0 2 1 5 1 7 1 2 1 2 1 4v2l1 6v8l3-2c1 1 0 1 2 2 0 1 0 2 1 3l3 13c1 2 1 4 2 5 4 5 4 11 9 16l1 2c0 2-1 4 0 6 0 1 0 3 1 4v4c1 2 2 4 3 7v4c-1 2-1 3-1 5 1-1 1 0 1 0 1 1 1 2 1 4v2c1 0 1 0 2 1v3l2 10c0 1 0 2 2 3h0c0 2 0 4 1 7l3 12c0 1 1 3 1 5 3 1 2 4 4 5 1 2 1 3 2 5l3 22 2 2c-1-3-1-6 0-8 1 1 2 2 2 3 1 3 0 7 3 9v1l13 43c0 5 1 11 2 16 1 3 0 7 1 10v4l1-1v-1c1 0 1 0 2 1 2 3 3 5 3 9v4c1 3 1 5 2 7l2 1v3c1 1 2 3 3 5l1 4c2 1 3 3 5 4 1 1 1 2 1 3 1 4 3 8 4 12l3 3 8 23 1 2v1 4c-1 2-2 5-2 8l-3 11c0 1-1 3-2 5v1c1 2 0 4 0 7 1 2 2 1 1 4v1c-1 1-1 2-1 3l-1 2c0 2-1 3-1 4l1 1h0v2c1 0 1-1 2-1v-2c1 1 0 2 0 2l1 1v2 1 1h0c0 2 0 2 1 3v1c1 1 1 1 1 2l1 1v3c-1 0-2 1-2 2-2 2-3 3-3 6v1l-1 3 1 1v1 2c0 3-1 5-2 9v2h0c1-1 1-2 2-3-1 1-1 2-2 4v1s0 1-1 1l1 1-1 1v-1c-1 1-1 2-1 2l-1 4c0 2 0 4-1 6-2 13-4 26-5 39-1 7-3 15-3 23-1 7 0 14 0 21l-4 13-3 15-24-90-27-97v-4-1c-1-3-1-7-3-10 0 0-2-1-2-2l-8-24c-1-4-2-7-4-11 0-5-2-9-4-14-1-4-2-8-4-12v-2c-1-1-1-3-2-4l-2-10-3-9c0-1-1-2-1-2v-2s-1-1-1-2l-3-9v-2l-2-3h0l-1-1v-1l-1-1v-3h-1c-1 0-1-1-1-2-1-2-1-3-2-5l-1 1c-1-5-2-12-5-16h-2l-1 1c-2 1-4 4-6 4h-1-1l-1-1c-3 0-7 2-10 0l-36-111 3-2 2 1c1-1 4-2 5-4 1-1 3-2 5-2h1c1-6 1-14 0-19 0 0-1-1-1-2l-1 3-1-16h-1c1 7 1 14 0 22-1 3-2 6-4 8-1 3-6 8-8 10l-8-24-1-1c-1-8-5-17-8-25l-1-4c-3 1-5 1-7 3-6 3-13 8-19 9l-2 3c-3 8-6 15-8 23v5c-2 6-2 13-1 19 1 11 8 17 16 24-4 0-9 0-13 2-3 2-5 6-6 10h-1c-2 9 0 19 3 28 8 25 26 50 49 63 4 2 8 3 11 5 3 0 5 1 7 2-2 0-4 0-5-1-32-5-58-32-72-59-4-9-7-18-10-27l-6-23c-2-8-4-17-9-23-5-7-9-8-17-9 13-6 17-13 22-25 0-1 1-2 1-3l3-10c3-11 6-21 6-32 0 1 0 0 1 1l1 1c0 1 0 3 1 5v-1c1-5-1-13-3-18l-1-2c0-3 5-8 6-11 7-12 6-24 3-37v2l-1-1c0-1 0-1-1-2v-1c0-2 0-3 2-4l-2-3h1l1-1c0 1 1 1 1 3 2 3 4 6 5 10l1 4h1c2-3-1-10-2-13 2 1 2 2 3 3l2-2v-2h1l-2-6c1-4 2-6 5-8l2 1c3 0 4 0 7-1 0-1 1-2 1-2v3l2 1c1-1 3-2 4-3l-5 7c1 0 1 0 2 1l1-1c2-3 4-5 7-8l7-6c2 1 4-1 6-1 2-1 4-1 5-1 6-3 16-9 22-8z" class="n"></path><path d="M324 322c2-3 4-5 7-7 1 0 1 1 1 2-2 2-4 4-8 5z" class="D"></path><path d="M446 227v-2c1-2 2-3 3-4l2-2 1-1c1-2 1-4 4-4 1-1 2-2 4-3-2 2-2 2-2 4l-4 4c-2 0-6 3-7 6l-1 2z" class="K"></path><path d="M367 323l1-4v-1c0-1 0-2 1-4h-1c0-2 0-2 1-4 1 2 0 3 1 5 0 1-1 2-1 4-1 2 0 4 1 6l-1 10c0-3-2-5-2-7v-5z" class="E"></path><path d="M324 322c4-1 6-3 8-5l1 3c-3 2-6 6-10 6l-3 1 4-5z" class="l"></path><path d="M463 606l1-1c-1-1-1-2-1-3l1-1 1-1h0 1 3s1 0 1 1v4c-1 1-2 2-4 2h-2l-1-1z" class="E"></path><path d="M361 318l-1-3c-1-3-3-7-2-9h0l1 2c1 1 1 2 2 3s1 1 2 3l1 2c0 1 0 1 1 2v3h1c0 1 1 2 1 2v5c0-2-1-4-2-6-1 0-1-1-1-2h-1l-1-1v-1h-1z" class="N"></path><path d="M446 227l1-2c1-3 5-6 7-6-1 2-3 4-4 6-2 2-4 4-5 6-2 3-3 7-4 11l-1 1c0 1-1 3-1 3l-1 1v-1l1-1c0-1 1-2 1-3v-2c1-1 1-3 2-4 0-1 0-2 1-2 0-2 1-4 2-5 0-1 1-1 1-2z" class="g"></path><path d="M430 515l3 10c1 5-2 6 3 10-2 6-2 13 1 19v-7c1 2 1 4 1 6-1 0-1 0-2 1v1l1 2v1c1 1 2 2 2 3-1 0-2-1-3-2l-2-10v-3-1-1-1l-1-4-1-1h0v-1-1c-1-3-1-5-1-7l1-1v-2-1h-1v-2-3c-1-1-1-3-1-5z" class="R"></path><path d="M294 384c2-13 4-28 9-40l1 1-4 15c-3 10-4 21-4 32 0 3 0 8 2 10 1 2 3 4 4 6h0c-4-2-6-7-8-10 1-1 0-3 0-4v-10z" class="a"></path><path d="M445 218h0c1-2 3-3 5-6 1 0 1-2 2-2 1-1 6-4 7-4h2l1 1c-4 2-7 4-10 8-2 2-4 3-5 5-1 1-1 2-2 3l-1 1c0 1 0 1-1 2h0c0 1-1 2-1 2l-1 3-1 1c0 1 0 1-1 2h0v2c-1 0-1 1-1 1l-1 3c0 1 0-1 0 1 0 1-1 2-1 3h0v1c0 1-1 1-1 2v1h0-1c2-6 3-11 5-17 3-3 4-7 7-11l-1-2z" class="g"></path><path d="M320 327l3-1c4 0 7-4 10-6l1 4c-6 5-12 9-20 12 1-3 4-6 6-9z" class="m"></path><path d="M302 408h2c0 1 0 1-1 2-4 2-7 6-10 9v1-13c1-3 0-6 1-9 2 3 4 8 8 10h0z" class="E"></path><path d="M314 336c8-3 14-7 20-12l2 6c-3 1-5 1-7 3-6 3-13 8-19 9l4-6z" class="j"></path><path d="M407 449c0 3 0 6 1 8l2-11v4c-1 1 0 3-1 4v3h-1-1c0 1 0 1 1 2v1l1-1s1 1 1 2h0l1 1c0 1 1 2 1 3h0c1 1 1 1 1 2v1s1 1 1 2v2c0 1 0 3 1 4 0 2-1 6 0 7 0 1 0 1 1 2 1 2 1 5 2 7l1 4s0 1-1 1c-3-6-4-14-6-21s-5-13-6-20c0-2 1-4 1-7zm-46-131h1v1c0 1-1 2 0 3v1c0 1 0 1 1 2v2c1 1 0 1 1 2l1 4 1 2v2c0 1 0 2 1 3v2h1v1l1 4 1 4v2c1 1 1 2 1 4h-1v2s-1-1-1-2l-1 3-1-16h-1c1 7 1 14 0 22-1 3-2 6-4 8v-2l1-2 1-1v-3c1-1 1-2 1-3l-1-1v-1c-1-1-1-2-1-3 1-1 0-4 1-5 1-2 1-2 1-3-2-2 0-8-1-10v-1-1h-1v-1-2l-1-1v-1c0-1-1-2-1-3h0v-1l-1-1c0-1 0-1 1-2h0c0 1 0 1 1 2v-2-2c-1-1-1-1-1-2h0v-1-3z" class="f"></path><path d="M281 390c1-5-2-16 2-20-1 4-1 7 0 11l2 27c0 9 2 19 4 28 0 2 0 5 1 6v3l-1-2v-2h-1c0 1 0 2 1 3l-1 1h0l-1-2-2-10v-2c-1-3-2-6-2-8v-2-3c-1-2-1-5-1-7-1-1-1-2-1-4h1l-1-7v-10z" class="E"></path><path d="M401 409v1-1c0-1 0 0 1-1v-5c1 3 1 7 1 10v1c1 1 2 2 2 4 0 1 0 2 1 3 0-2 1-5 0-7v-7l1 10h1c0 1-1 3 0 5 0 2 0 4 1 6v1 1c-3 6-3 12-2 19 0 3-1 5-1 7l-4-13c1-3 0-7-1-10l-1-15c0-3 0-6 1-9zm62 230l3 9 1-13c0 3-1 7 1 9v2h1v3c-1 1-2 1-3 1 0 2 2 2 3 4 0 1 1 2 1 4v1l1 4v2c1 0 1 1 1 2v1l1 4 1 1h0c0 1 1 1 1 2 1 1 1 3 2 4v2c0 1 0 2 1 3l2 6h-1l1 2v3c1 1 1 1 1 3h-1c0-1 0-2-1-3 0-1-1-3-1-4h0v2c0 1 0 1 1 2v1 1c0 1 0 3 1 4v1h0c0 1 0 1 1 2v2 1l1 1v2 1h1c0 1 0 2 1 4v2 1h1c0 1 1 2 1 3s0 0 1 2v1h0c0 2 0 3 1 5v2c0 1 0 1 1 2h0v3c0-2 0-8 1-9v-4l1 1c-1 2-1 5-1 8s1 5 0 8l-27-97v-4z" class="p"></path><defs><linearGradient id="v" x1="405.929" y1="401.387" x2="390.571" y2="408.113" xlink:href="#B"><stop offset="0" stop-color="#2f2f34"></stop><stop offset="1" stop-color="#535054"></stop></linearGradient></defs><path fill="url(#v)" d="M396 393c0-6 0-12 1-18 2 7 2 14 3 20 0 5 1 10 1 14-1 3-1 6-1 9l1 15c1 3 2 7 1 10l-8-25s1-1 2-1v-4l-1-2v-2-3-1 1s0 1 1 2c0 1 0 2 1 3l-1-6v-3-2-6-1h0z"></path><defs><linearGradient id="w" x1="395.877" y1="411.006" x2="396.868" y2="432.077" xlink:href="#B"><stop offset="0" stop-color="#2d2b2f"></stop><stop offset="1" stop-color="#54525a"></stop></linearGradient></defs><path fill="url(#w)" d="M395 405v1s0 1 1 2c0 1 0 2 1 3l-1-6v-3-2-6-1h0l2 29c1 3 0 5 1 8v1l2 2c1 3 2 7 1 10l-8-25s1-1 2-1v-4l-1-2v-2-3-1z"></path><path d="M496 633c1 2 0 2 1 4v1 1l1 4h0v3l1 1 7 29 1 4 1 2v2l1 2v2c1 1 1 2 1 2 0 1 0 2 1 2v3c0 1 1 2 1 2v1l1 2v2c0 1 0 1 1 2v4h-1l-2-3v-1c-1 0-1-1-1-1l-2-7-2-5v-1l-2-5v-3l-2-5h0l-4-11-1-6-1-3c-1-1-1-2-1-3s-1-2-1-2v-2c-1-1-1-2-1-4-1-1-1-1-1-2h0v-4c0-3 1-4 3-7h1z" class="R"></path><defs><linearGradient id="x" x1="322.946" y1="244.682" x2="331.874" y2="223.741" xlink:href="#B"><stop offset="0" stop-color="#b1b0b2"></stop><stop offset="1" stop-color="#d3d1d1"></stop></linearGradient></defs><path fill="url(#x)" d="M340 221c6-3 16-9 22-8-6 2-12 5-17 8-20 12-33 32-43 53l-4 9h0c0-3 3-9 5-12 4-8 7-17 13-24l-1-3h-1v2h-2l-2-2 4-6 1-1c2-3 4-5 7-8l7-6c2 1 4-1 6-1 2-1 4-1 5-1z"></path><path d="M329 223c2 1 4-1 6-1 2-1 4-1 5-1l-8 5c-3 1-7 4-10 3l7-6z" class="d"></path><path d="M315 237l1 2c3-1 5-4 8-6l-8 14-1-3h-1v2h-2l-2-2 4-6 1-1z" class="h"></path><defs><linearGradient id="y" x1="513.5" y1="728.748" x2="520" y2="722.752" xlink:href="#B"><stop offset="0" stop-color="#0c0a0b"></stop><stop offset="1" stop-color="#2b292c"></stop></linearGradient></defs><path fill="url(#y)" d="M511 705l2 3h1v-4c-1-1-1-1-1-2v-2l-1-2v-1s-1-1-1-2v-3c-1 0-1-1-1-2 0 0 0-1-1-2v-2l-1-2v-2l-1-2-1-4-7-29-1-1v-3l5 16c1 4 3 8 3 13l3 1c0 3 1 6 3 9l4 17 1 3h5l1-7 3-10c2-6 5-13 5-19l1 1-4 16v2c-1 1-1 2-1 2l-1 2c-1 5-2 10-4 15l-1 4c0 1 0 2-1 3v1c0 1 0 2-1 3v1 2 6l-2 9h-1l-1-1v-1-1c0-1 0-1-1-2h1c0-2-1-2-1-3-1-3-2-6-2-10 0-3 0-6-1-9v-1z"></path><path d="M517 702h5c-1 1-1 2-1 3-1 1-1 2-1 4-1-3-2-4-3-7z" class="D"></path><path d="M506 672l3 1c0 3 1 6 3 9l4 17 1 3c1 3 2 4 3 7l-1 3v1c0 1-1 1-1 1-1-1-1-2-1-3l-3-9v-2c-1-5-3-10-4-14l-4-14z" class="I"></path><path d="M404 237l-6 8c-9 12-12 30-11 45 1 5 2 10 4 16l13 43 11 40 7 22c1 4 3 8 4 12 0 4 1 8 1 12l3 16c1 2 2 5 1 7 0 2 1 4 2 5 0 2 1 3 1 5 1 3 1 6 2 9h1v-5s1 1 1 2v1 1c0 1 0 2-1 3 0 0-1 0-1-1-1-1-2-6-2-8l-8-32h0v-2-3c-1-1-1-3-1-4v-1h0c0-2-1-4-1-5h-1c0-5-3-11-4-16l-8-27-4-15-3-9c-1-3-1-6-2-9s-4-7-3-9v-1l-1-1h0c-1-4-3-9-4-13l-4-15c-3-8-5-17-5-26 0-11 4-21 8-31 2-3 4-9 7-11h0c1-1 3-2 4-3z" class="E"></path><defs><linearGradient id="z" x1="436.937" y1="494.527" x2="416.026" y2="519.422" xlink:href="#B"><stop offset="0" stop-color="#2e2b2f"></stop><stop offset="1" stop-color="#454449"></stop></linearGradient></defs><path fill="url(#z)" d="M419 496l1-1v-1-5h0v-4-2c0-4 0-10 1-12 1-4 1-9 1-13h1v8c-1 1-1 4-1 6-1 1 0 6 0 8-1 1-1 2-1 4 0-1 0-1 1-2 0 1 1 1 1 2h1 1l1-2h0l1-1c1-3-1 1 0-2h1v-1h1c2-1 4 0 6 1-1 1-3 0-4 1-1 0-1 0-2 1v5h-1c-1-1-1-2-1-4l-1 1c0 2 1 3 1 4 0 8 0 16 3 23v5c0 2 0 4 1 5v3 2h1v1 2l-1 1c0 2 0 4 1 7v1 1h0l1 1 1 4v1 1 1 3l-3-9c0-1-1-2-1-2v-2s-1-1-1-2l-3-9v-2l-2-3-6-23c1 0 1-1 1-1z"></path><defs><linearGradient id="AA" x1="368.087" y1="364.532" x2="380.705" y2="375.215" xlink:href="#B"><stop offset="0" stop-color="#68696b"></stop><stop offset="1" stop-color="#878287"></stop></linearGradient></defs><path fill="url(#AA)" d="M364 329c1 1 2 1 2 3l1 2v2l1 1v2l2 5v2l3 7 1 4 1 5c1 2 2 4 2 6l2 5 2 8c1 3 3 6 4 8 1 5 3 10 4 15l5 14 8 25 4 13c1 7 4 13 6 20s3 15 6 21l6 23h0l-1-1v-1l-1-1v-3h-1l-38-123c-2-4-3-8-7-10s-8-2-12-1c1-1 3-2 5-2h1c1-6 1-14 0-19v-2h1c0-2 0-3-1-4v-2l-1-4-1-4v-1h-1v-2c-1-1-1-2-1-3v-2l-1-2-1-4z"></path><defs><linearGradient id="AB" x1="295.876" y1="343.514" x2="307.625" y2="343.008" xlink:href="#B"><stop offset="0" stop-color="#2c2a2d"></stop><stop offset="1" stop-color="#5c5a5f"></stop></linearGradient></defs><path fill="url(#AB)" d="M316 299c0 1 0 1 1 3l1-1v4 2c-1 4-6 10-5 13v1c1 0 3-1 3-2l1-1 1-2 1 1c1-1 1-1 1-2 1-1 4-4 5-4h1c-8 9-16 19-20 31-1 1-1 2-2 3l-1-1c-5 12-7 27-9 40v-5h0v-1c1-2 1-3 1-4v-1c1-1 0-1 0-2s1-1 1-1v-3-1l1-1v-3h0l1-1-1-1 1-1h-1c-1 0-1 1-1 1v2 1c-1 1-1 1-1 2-1 0 0 1 0 2s-1 1-1 2v6c0 1-1 0 0 1 0 2 0 4-1 5h0l1-15c0-12 1-24 4-35 1 2 1 9 0 11h-2v4c0 2 0 4-1 5v1c1 1 1 2 1 2v2-2c0-1 0-2 1-3v-2-2c0-1 0-1 1-2v-2c0-1 0-1 1-2h1c1-2 3-7 3-10 1-5 3-10 5-14 1-1 1-3 2-4h0c1-2 2-4 2-7h0c1-2 3-5 4-7z"></path><path d="M316 299c0 1 0 1 1 3l-7 12c0 1-1 2-2 3 1-1 1-3 2-4h0c1-2 2-4 2-7h0c1-2 3-5 4-7z" class="a"></path><defs><linearGradient id="AC" x1="278.528" y1="259.612" x2="297.22" y2="267.284" xlink:href="#B"><stop offset="0" stop-color="#000001"></stop><stop offset="1" stop-color="#393738"></stop></linearGradient></defs><path fill="url(#AC)" d="M285 243c1 1 1 2 2 3v2c2 2 3 5 4 7l1 5c0 1 0 2 1 3v3c1 3 0 8 0 11 0 2 0 4-1 7-1 6-3 11-3 18 0 1 0-1 0 1-1 1 0 1 0 2v4 1l-1 2 1 1-5 23c3 4 0 13 0 18l-1 7v9c-4 4-1 15-2 20v10l1 7h-1 0l-2-31c0-4 0-9 1-12v-5-4c1-4 1-9 2-13 0-1 0-2 1-3 0-1 0-4 1-6v-4c1-1 1-3 1-4s0-1 1-2v-1-1-1-2-1-1l1-2v-3c1-1 1-1 1-2h0v-4h0-1-1v-1l2-2h-1v-2-3h0v-2h1 0c-3-4 3-10 1-14 0 1-1 2-2 4 0 1 0 2-1 2-2 1-3 5-4 7-1 1-2 3-3 4h-1v4l-1-2c0-3 5-8 6-11 7-12 6-24 3-37v2l-1-1c0-1 0-1-1-2v-1c0-2 0-3 2-4l-2-3h1z"></path><path d="M283 361v9c-4 4-1 15-2 20-1-2 0-5 0-7v-16h0c1-2 1-4 2-6z" class="N"></path><path d="M283 348c0 2 0 4 1 6l-1 7c-1 2-1 4-2 6h0v-1-9c1-1 1-3 1-4l1-5z" class="f"></path><path d="M284 336c3 4 0 13 0 18-1-2-1-4-1-6l1-8v-4zm152 126c2 3 4 8 5 12l4 11 6 20 14 43c1 4 3 8 5 12 1 3 2 8 3 12l7 22c3 9 3 18 6 26l2 9c1 1 2 0 2 2l-1 1v3h0c0 1 0 2-1 3l-3 6h0v-2c1-1 1-1 1-2v-1-1c-1-1 0-1 0-2-1-2-1-2-1-3v-1c0-3-1-5-1-7 0-1-1-2-1-3v-2c-1-1-1-1-1-2v-2-1l-1-5-1-2h0v-2-1l-2-8v-1l-1-4-1-1c-1-2 0 0 0-1l-2-4-1-4-3-10c0-1-1-2-1-3-1-2-1-5-2-6-1-3-1-8-1-10-1-2-2-4-3-5-1-2-2-4-2-6l-2-4-3-10-2-7c-1-6-4-11-5-16 0-1-1-1-1-2l-1-2h0c0-1 0-3-1-3 0-2-1-4-1-6l-1-1c0-1-1-2-1-2 0-1 0-2-1-3l-2-10c-1-2-3-5-3-7v-1c-1-1-1-4-1-6z" class="N"></path><path d="M517 815c-1-3 0-6 0-8v-22l1-26v-1-2c1-1 1-2 1-3h0v-2l1-1c0-3 0-8 1-11s1-6 1-8 0-5 1-6v-2c0-1 1-2 1-3v-1c1-1 0-2 0-3 1-2 0-3 1-5l1 1c-1 1 0 2 0 3 1-2 1-6 2-8 0-1 1-1 1-2v-1c0-1 1-2 1-3l3-8v-2c1-2 1-2 1-3l1 1v1 2c0 3-1 5-2 9v2h0c1-1 1-2 2-3-1 1-1 2-2 4v1s0 1-1 1l1 1-1 1v-1c-1 1-1 2-1 2l-1 4c0 2 0 4-1 6-2 13-4 26-5 39-1 7-3 15-3 23-1 7 0 14 0 21l-4 13z" class="R"></path><path d="M370 325v-6c0-2 0-4 1-6 0-1-1-5 0-6 1 1 1 1 1 3v6 6c-1 2-1 5 0 7v2 3c0 1 0 2 1 3 0 2-1 4 0 5 0 1 0 2 1 3v1h0l2 2c1 1 2 2 2 4 1 2 1 3 2 4v1c1 1 1 2 2 3v2c1 0 1 1 1 2l1 1v2l1 1v2c0 1 0 1 1 2v-2h0c1 1 1 2 2 3l-1 1 1 1v1 1h0-1c1 1 1 1 1 2 0 2 2 1 2 3l-1 1c0 1 0 1 1 2 0 2-1 1 1 3h-1c1 1 1 1 1 2s0 1 1 2v3c1 1 1 2 1 3 1 1 1 2 1 4 1 1 1 2 1 3v1 3 2l1 2v4c-1 0-2 1-2 1l-5-14c-1-5-3-10-4-15-1-2-3-5-4-8l-2-8-2-5c0-2-1-4-2-6l-1-5-1-4-3-7v-2l-2-5v-2l-1-1v-2l-1-2c0-2-1-2-2-3s0-1-1-2v-2c-1-1-1-1-1-2v-1c-1-1 0-2 0-3l1 1h1c0 1 0 2 1 2 1 2 2 4 2 6s2 4 2 7l1-10z" class="V"></path><path d="M437 213c6-4 11-7 18-6 2 0 3-2 5-2l1 1h-2c-1 0-6 3-7 4-1 0-1 2-2 2-2 3-4 4-5 6h0l1 2c-3 4-4 8-7 11-2 6-3 11-5 17l-1 10v1l-1 1c-1 2-2 6-1 8l-2-1-2-3c-1 1-1 2-2 3 0 2-1 2-2 4h-1l-1-1c0-1 0-2 1-3h0c0-2 0-5 1-7 1-1 1-2 1-3v-1-1l1-4c1-1 1-2 1-3 1-7 5-14 4-21v-2l1-1c0-5 2-8 6-11z" class="K"></path><path d="M434 235c0-2 0-4 2-6h0v-2h0c2-2 4-3 7-4l-4 5c-1 2-2 3-3 4s-1 2-2 3h0z" class="g"></path><path d="M445 218l1 2c-3 4-4 8-7 11v-1c-2 4-4 8-5 12-1 3-2 7-4 9 0-5 2-11 4-16h0c1-1 1-2 2-3s2-2 3-4l4-5 2-5z" class="Q"></path><path d="M430 251c2-2 3-6 4-9 1-4 3-8 5-12v1c-2 6-3 11-5 17l-1 10v1l-1 1c-1 2-2 6-1 8l-2-1-2-3c1-4 2-9 3-13z" class="C"></path><defs><linearGradient id="AD" x1="362.908" y1="282.824" x2="354.851" y2="296.122" xlink:href="#B"><stop offset="0" stop-color="#bfbebe"></stop><stop offset="1" stop-color="#f6f6f7"></stop></linearGradient></defs><path fill="url(#AD)" d="M369 263l1-2c0-2 0-2 1-4l1 1c-4 10-6 20-4 30v1c1 4 5 8 8 11-9-5-18-5-27-2-8 2-17 7-23 13h-1l4-4c2-2 4-3 6-5-2 0-3 1-4 2h-1c0-5 3-6 4-10 2-2 4-5 7-6 3-2 5-4 8-6 1-1 3-2 5-3l-1-1c-1 0-2 0-2-1v-2c1 0 3-1 3-2 1-1 2-2 4-2v1c1 1 2 1 3 1h0l1-1h5v-2-2l1-2c0-1 0-2 1-3z"></path><defs><linearGradient id="AE" x1="365.281" y1="273.979" x2="361.63" y2="283.739" xlink:href="#B"><stop offset="0" stop-color="#908e91"></stop><stop offset="1" stop-color="#c0bfc0"></stop></linearGradient></defs><path fill="url(#AE)" d="M351 275c1 0 3-1 3-2 1-1 2-2 4-2v1c1 1 2 1 3 1h0l1-1h5c-1 1-1 2-1 3-1 2 0 6 0 9l1 1-1 1c-2-1-3-2-4-2l-1 1-1-1h0c0-2-1-2-3-2-1-1-2-1-2-1-3 0-4 1-6 1 1-1 3-2 5-3l-1-1c-1 0-2 0-2-1v-2z"></path><path d="M334 294c2-2 4-5 7-6h1c3 1 8-1 10 3v2h0l1 1c-1 1 0 1-1 1-5 1-10 4-15 6h0c-1 1-1 1-2 1-2 0-3 1-4 2h-1c0-5 3-6 4-10z" class="X"></path><path d="M398 245c0 2-1 4-2 6v3c0 1-1 0-1 2 0 3-1 7-2 10-3 7-4 17-3 25 1 5 3 9 4 14l10 38 5 12 13 47c2 7 3 14 6 20 1 4 3 9 3 13l3 9v3l1 9v1c0 1 1 2 1 3v5 2c1 1 1 3 1 5v5h-1c-1-3-1-6-2-9 0-2-1-3-1-5-1-1-2-3-2-5 1-2 0-5-1-7l-3-16c0-4-1-8-1-12-1-4-3-8-4-12l-7-22-11-40-13-43c-2-6-3-11-4-16-1-15 2-33 11-45z" class="N"></path><path d="M396 251v3c0 1-1 0-1 2 0 3-1 7-2 10-3 7-4 17-3 25 1 5 3 9 4 14l10 38 5 12 13 47c2 7 3 14 6 20 1 4 3 9 3 13-1-1-2-3-2-4h0c-1-3-1-6-2-8l-18-59-16-54c-1-6-3-12-4-18-2-10 0-20 3-30 1-4 2-8 4-11z" class="f"></path><path d="M460 211c2-2 3-3 6-3l1 1h1l3-2 1-1 1 2h1l-4 4h-1c-1 2-3 4-4 7h0l-5 11 1 1c1-1 2-1 3-2l-1 2c0 2-1 3-1 5l-3 2h0v-2c-3 0-5 3-6 5-4 6-6 11-8 17-1 4-1 8-2 12-1-1-1-2-1-3-1-2 0-6-1-7l-1 3c0 1-1 2-1 3-1 1-1 5-1 7h0c-2 1-2 1-3 0l-1 1-1-6v-9-1l1-10h1v5s1 1 2 1v-1c0-1 1-2 1-2v-2c0-1 1-1 1-2v-1s1-2 1-3l1-1c1-4 2-8 4-11 1-2 3-4 5-6 1-2 3-4 4-6l4-4c0-2 0-2 2-4z" class="D"></path><path d="M450 227c1 2 1 3 0 4-1 2-2 3-3 5l-3 1c2-4 3-7 6-10z" class="I"></path><path d="M458 215l1 1-1 1v5c-1 0-2-1-3-1-2 1-3 3-5 4 1-2 3-4 4-6l4-4z" class="L"></path><path d="M442 246c-1 4-3 10-1 14l-1 3c0 1-1 2-1 3-1 1-1 5-1 7h0c-2 1-2 1-3 0l-1 1-1-6v-9-1c0 1 1 2 1 3v1l2 1c1-1 1-2 2-4 0-2 0-2 1-4 1-3 1-6 3-9z" class="I"></path><path d="M433 258c0 1 1 2 1 3v1l2 1c1-1 1-2 2-4v1c0 2 0 6-1 8l-1 2c-1 0-1 0-1-1-1 0-2 0-2-1v-9-1z" class="H"></path><defs><linearGradient id="AF" x1="461.733" y1="209.514" x2="461.823" y2="223.979" xlink:href="#B"><stop offset="0" stop-color="#230000"></stop><stop offset="1" stop-color="#3a0304"></stop></linearGradient></defs><path fill="url(#AF)" d="M460 211c2-2 3-3 6-3l1 1h1l3-2 1-1 1 2h1l-4 4h-1c-1 2-3 4-4 7h0c0-2 0-2-1-3l-7 14 1-8v-5l1-1-1-1c0-2 0-2 2-4z"></path><path d="M464 216v-2c2-2 4-5 7-6 0 2-1 3-2 4-1 2-3 4-4 7h0c0-2 0-2-1-3z" class="L"></path><path d="M450 227l1-1 1-1h1v1c0 1 1 1 1 3h0c-1 1-1 2-1 4 0 1 0 0-1 1 0 2 0 3-1 4h0v2l2 1c-4 6-6 11-8 17-1 4-1 8-2 12-1-1-1-2-1-3-1-2 0-6-1-7-2-4 0-10 1-14l2-9 3-1c1-2 2-3 3-5 1-1 1-2 0-4z" class="B"></path><path d="M444 237l3-1c-2 7-4 14-5 22v9c-1-2 0-6-1-7-2-4 0-10 1-14l2-9z" class="P"></path><path d="M442 258h1v5c1-1 0-1 1-1v-3c1-3 3-7 1-10l1-1c2-3 2-8 4-10h1v2l2 1c-4 6-6 11-8 17-1 4-1 8-2 12-1-1-1-2-1-3v-9z" class="I"></path><defs><linearGradient id="AG" x1="346.587" y1="266.367" x2="349.793" y2="280.836" xlink:href="#B"><stop offset="0" stop-color="#646167"></stop><stop offset="1" stop-color="#979598"></stop></linearGradient></defs><path fill="url(#AG)" d="M354 261l4-2v1c-2 2-4 3-6 5h0 2c4-1 9 0 14 0v-2h0 1c-1 1-1 2-1 3l-1 2v2 2h-5l-1 1h0c-1 0-2 0-3-1v-1c-2 0-3 1-4 2 0 1-2 2-3 2v2c0 1 1 1 2 1l1 1c-2 1-4 2-5 3-3 2-5 4-8 6-3 1-5 4-7 6-1 4-4 5-4 10h1c1-1 2-2 4-2-2 2-4 3-6 5l-4 4c-1 0-4 3-5 4 0 1 0 1-1 2l-1-1-1 2-1 1c0 1-2 2-3 2v-1c-1-3 4-9 5-13v-2-4c3-5 6-10 10-15l3-3c2-4 6-7 8-10l3-2 3-3c2-2 7-5 9-7z"></path><path d="M351 275v2c0 1 1 1 2 1l1 1c-2 1-4 2-5 3-3 2-5 4-8 6-3 1-5 4-7 6h-2c0-2 2-2 3-4s5-7 8-8l3-3c2-1 3-3 5-4z" class="T"></path><path d="M328 286c1 0 2-1 3 0l1-1v1 1h0c-1 1-3 3-4 5-4 6-5 13-6 20 2-2 5-5 7-6 0-1 1-1 2-2s2-2 4-2c-2 2-4 3-6 5l-4 4c-1 0-4 3-5 4 0 1 0 1-1 2l-1-1-1 2-1 1c0 1-2 2-3 2v-1c-1-3 4-9 5-13v-2-4c3-5 6-10 10-15z" class="Z"></path><defs><linearGradient id="AH" x1="465.364" y1="591.117" x2="448.016" y2="597.427" xlink:href="#B"><stop offset="0" stop-color="#151415"></stop><stop offset="1" stop-color="#464348"></stop></linearGradient></defs><path fill="url(#AH)" d="M439 561l2 1c1-1 1-1 1-2 1 0 1 0 1 1s0 1 1 3v2c1 1 1 2 1 3v1c0 1 0 1 1 2v3h1v3 1c0 1 0 3 1 4v3c0 1 0 1 1 2v2c0 1 0 1 1 2v2h0l1 3v-2-14-2-5-6c0-1 0-1 1-2 0-2-1-6 0-8 1 2 2 2 3 4v1l1 3h1v4h1v3c0 1 0 1 1 3 0 1-1 1 0 2 0 1 0 1 1 2v2c1 1 1 2 1 3v1c1 1 1 2 1 3l2 5c1 1 1 2 2 3v1c1 1 1 1 3 2h-3-1 0l-1 1-1 1c0 1 0 2 1 3l-1 1h-1v3l3 1v1c-1 2-1 5-1 7-1 7-1 13-1 20-1-3-1-7-3-10 0 0-2-1-2-2l-8-24c-1-4-2-7-4-11 0-5-2-9-4-14-1-4-2-8-4-12v-2c-1-1-1-3-2-4 1 1 2 2 3 2h0z"></path><path d="M436 559c1 1 2 2 3 2h0c0 1 1 2 1 3v1c1 1 1 1 1 2 1 1 1 2 1 2 0 1 0 2 1 2l2 8v2l1 3 2 7c0 1 0 1 1 2 0 1 0 1 1 2 0 2 1 3 1 5l-1 2c-1-4-2-7-4-11 0-5-2-9-4-14-1-4-2-8-4-12v-2c-1-1-1-3-2-4z" class="V"></path><defs><linearGradient id="AI" x1="296.658" y1="265.394" x2="310.538" y2="267.957" xlink:href="#B"><stop offset="0" stop-color="#59565a"></stop><stop offset="1" stop-color="#908f93"></stop></linearGradient></defs><path fill="url(#AI)" d="M311 229v3l2 1c1-1 3-2 4-3l-5 7c1 0 1 0 2 1l-4 6 2 2h2v-2h1l1 3c-6 7-9 16-13 24-2 3-5 9-5 12h0l-9 30-1-1 1-2v-1-4c0-1-1-1 0-2v-1c0-7 2-12 3-18 1-3 1-5 1-7 0-3 1-8 0-11v-3c-1-1-1-2-1-3l-1-5c-1-2-2-5-4-7v-2c-1-1-1-2-2-3l1-1c0 1 1 1 1 3 2 3 4 6 5 10l1 4h1c2-3-1-10-2-13 2 1 2 2 3 3l2-2v-2h1l-2-6c1-4 2-6 5-8l2 1c3 0 4 0 7-1 0-1 1-2 1-2z"></path><defs><linearGradient id="AJ" x1="304.012" y1="245.146" x2="309.594" y2="246.377" xlink:href="#B"><stop offset="0" stop-color="#0e0910"></stop><stop offset="1" stop-color="#1c2020"></stop></linearGradient></defs><path fill="url(#AJ)" d="M312 237c1 0 1 0 2 1l-4 6-10 15c-1 2-2 6-4 8 0-3 2-5 2-8l3-6 8-12 3-4z"></path><defs><linearGradient id="AK" x1="299.175" y1="242.492" x2="309.325" y2="241.508" xlink:href="#B"><stop offset="0" stop-color="#99979b"></stop><stop offset="1" stop-color="#c5c3c3"></stop></linearGradient></defs><path fill="url(#AK)" d="M311 229v3l2 1c1-1 3-2 4-3l-5 7-3 4-8 12-3 6c-1-2-2-7-3-10l2-2v-2h1l-2-6c1-4 2-6 5-8l2 1c3 0 4 0 7-1 0-1 1-2 1-2z"></path><path d="M311 229v3l2 1c1-1 3-2 4-3l-5 7-3 4c-2-2 1-7 1-10 0-1 1-2 1-2z" class="X"></path><path d="M301 231l2 1c-2 1-3 3-4 6-1 2-1 5 0 7l1 1h-1c0 2 0 5 1 6l1 1-3 6c-1-2-2-7-3-10l2-2v-2h1l-2-6c1-4 2-6 5-8z" class="Z"></path><defs><linearGradient id="AL" x1="451.832" y1="513.954" x2="429.417" y2="521.933" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#333033"></stop></linearGradient></defs><path fill="url(#AL)" d="M429 486c2 0 3 2 6 0v1c1-1 2-2 2-3l1-2c-1 0-1-1-1-1v-1h1v-1s0-2 1-2c0 2-1 5 0 7 0 1 0 1 1 2l1 4v2l1 1v2l1-2v3h1c1 2 1 5 2 7 0 1 0 1 1 2v1 1c1 1 1 2 1 3v1c0 1 0 1 1 2 0 1 0 2 1 2v2 1l1 1v2h1v1 2l1 3c0 1 0 2 1 3v1c0 1 1 2 1 3 1 1 1 3 2 4s1 2 1 3v1c1 1 1 3 1 4 1 0 1 1 1 1l1 1c1 1 1 2 2 3l1 1v1h-3l-1 1c-1 0-1 0-1 1h-1l-1-2v-2l-5-15c-1-1-1-2-2-3 0-3-2-6-3-8h-3c0-1 0-2-2-2v1h-1-1c-3 3-4 7-4 11-5-4-2-5-3-10l-3-10v-5c-3-7-3-15-3-23 0-1-1-2-1-4l1-1c0 2 0 3 1 4h1z"></path><path d="M427 487c1 1 2 3 2 4 1 2 0 4 0 6 0 1 1 2 1 4v9c-3-7-3-15-3-23z" class="V"></path><path d="M278 302v-4h1c1-1 2-3 3-4 1-2 2-6 4-7 1 0 1-1 1-2 1-2 2-3 2-4 2 4-4 10-1 14h0-1v2h0v3 2h1l-2 2v1h1 1 0v4h0c0 1 0 1-1 2v3l-1 2v1 1 2 1 1 1c-1 1-1 1-1 2s0 3-1 4v4c-1 2-1 5-1 6-1 1-1 2-1 3-1 4-1 9-2 13v4-1c-1-1-1-2-1-3 1 0 1-1 1-2v-3-2h-1l-1 3v3 1c0 2 0 4-1 6v2c-1 5-1 10-1 15 0 11 1 23 2 35l1 10v4 1c0-1 0-1-1-2l-7-28c-2-8-2-19-1-28v-10c0-1 0-1 1-2v-1l1-1v-1h0v-1-1-1l-2 4-1 3h-1 0c0-1 1-2 1-3l3-10c3-11 6-21 6-32 0 1 0 0 1 1l1 1c0 1 0 3 1 5v-1c1-5-1-13-3-18z" class="f"></path><defs><linearGradient id="AM" x1="313.646" y1="289.238" x2="343.773" y2="306.071" xlink:href="#B"><stop offset="0" stop-color="#312e33"></stop><stop offset="1" stop-color="#49474c"></stop></linearGradient></defs><path fill="url(#AM)" d="M298 331c0-5 2-10 3-15 1-6 4-12 7-17 10-20 24-38 43-50l1 1-4 2s-1 1-2 1c0 1-1 1-1 2-2 0-2 1-3 2-2 1-3 3-5 5-2 1-3 1-4 3l5-3 1-1 2-2c1-1 2-2 3-2l1-1c1-1 3-1 4-2s1-1 2-1l4-2c0-1 1-1 2-1l4-1 1-1h2c0-1 0-1 2-1h2v-1h3 1s1 0 2 1h0 1v2l1-1c1-1 1-1 1-2 0 2-1 3-2 5l-3 7-1-1c-1 2-1 2-1 4l-1 2h-1 0v2c-5 0-10-1-14 0h-2 0c2-2 4-3 6-5v-1l-4 2c-2 2-7 5-9 7l-3 3-3 2c-2 3-6 6-8 10l-3 3c-4 5-7 10-10 15l-1 1c-1-2-1-2-1-3-1 2-3 5-4 7h0c0 3-1 5-2 7h0c-1 1-1 3-2 4-2 4-4 9-5 14 0 3-2 8-3 10h-1c-1 1-1 1-1 2v2c-1 1-1 1-1 2v2 2c-1 1-1 2-1 3v2-2s0-1-1-2v-1c1-1 1-3 1-5v-4h2c1-2 1-9 0-11z"></path><path d="M312 306l-4 8h0l2-5c0-2 1-3 2-5h0v-1c0-2 0-3 1-4l7-11c1-2 3-4 4-6v-1l1 1c-1 1-2 2-2 4h0c1-2 2-3 3-4 0-1 1-2 2-3l3-3v-1c2-2 3-4 5-6s6-4 8-5c1-1 2-1 3-1h1 1 0c1 0 1 0 2-1h1l1-1h0 1c-2 2-7 5-9 7l-3 3-3 2c-2 3-6 6-8 10l-3 3c-4 5-7 10-10 15l-1 1c-1-2-1-2-1-3-1 2-3 5-4 7h0z" class="V"></path><path d="M316 299c2-3 3-6 5-9 3-6 8-12 13-16 2-3 5-3 8-5 0-1 0-1 1-1h1 1l-3 3-3 2c-2 3-6 6-8 10l-3 3c-4 5-7 10-10 15l-1 1c-1-2-1-2-1-3z" class="W"></path><path d="M436 454v-4h2l2 8h3c0-3-1-4-1-7-1 0 0-1 0-1l2 4 4 15 1 1 1 5 1 3 2 6 1 2 2 8 8 27 2 7c1 2 2 5 2 7l10 32 1 2 9 33v3l2 7c0 3 1 5 1 8 0 2 0 4 1 5-1 1-1 3-1 4v3l-2 3h0v-3l1-1c0-2-1-1-2-2l-2-9c-3-8-3-17-6-26l-7-22c-1-4-2-9-3-12-2-4-4-8-5-12l-14-43-6-20-4-11c-1-4-3-9-5-12v-2c0-1-1-2-1-3v-1c1-1 1-1 1-2z" class="p"></path><path d="M436 454v-4h2l2 8h3c0-3-1-4-1-7-1 0 0-1 0-1l2 4 4 15 1 1 1 5 1 3c-1 0 0 0-1 1v2l-2-1 20 69c1 3 3 5 4 8 4 13 8 26 11 39 1 3 3 7 4 11 1 5 2 12 2 17-3-7-3-15-5-22l-11-39c-2-5-5-10-7-15l-13-45-3-12-6-16c0-1-2-2-2-4s-2-4-2-6c-1-4-2-7-2-10l-1-1v-2l-1 2z" class="R"></path><path d="M440 458h3c0-3-1-4-1-7-1 0 0-1 0-1l2 4 4 15 1 1 1 5 1 3c-1 0 0 0-1 1v2l-2-1-5-14 1-1v-2c0-1-1-1-1-2v-1h-1c-1 0-2-2-2-2z" class="a"></path><path d="M448 480l2 1v-2c1-1 0-1 1-1l2 6 1 2 2 8 8 27 2 7c1 2 2 5 2 7l10 32 1 2 9 33v3l2 7c0 3 1 5 1 8-1 2 0 5-1 6-1-1-1-1-1-2 0-5-1-12-2-17-1-4-3-8-4-11-3-13-7-26-11-39-1-3-3-5-4-8l-20-69z" class="V"></path><path d="M393 266l2-1c-2 6-3 14-2 20l3 12c0 1 1 3 1 4s0 1 1 2v2l2 6c1 1 0 1 1 2v2l1 2 3 9v2l1 2 5 17c1 1 0 1 1 2v2l1 1v2 1l1 2c1 1 0 1 1 2v2 1l2 4 2 6v1c0 1 0 1 1 2v2l2 7 1 1v2l1 2 1 4 3-2v-1-2-2c-1-2-1-2-1-3h1c1 1 1 0 1 1v1c0 1 0 3 1 5 0 2 0 6 1 9h0c0 1 0 1 1 2 0 1 1 3 1 4 1 1 1 2 1 4v2c0-2 1-6 0-8-1 0-1 0-1-1s0-2-1-3l1-1s0-1 1-2c0 2 0 4 1 5v2c0 2 0 4 1 5l1-1v-3h1l1 5h0c1 2 1 3 2 5h0l1 4c0-1 1-2 2-4 0 3 0 7 1 9h0l1 2h1c1 2 1 2 1 4v1c1 2 1 3 2 6 0 1 0 3 1 5v5c-1 1-1 1-1 2 0 3 1 5 0 8 1 2 1 2 2 3-1 2-2 5-2 7 1 1 1 1 0 3l-1 1-1-1-4-15-2-4s-1 1 0 1c0 3 1 4 1 7h-3l-2-8h-2v4c0 1 0 1-1 2l-1-9v-3l-3-9c0-4-2-9-3-13-3-6-4-13-6-20l-13-47-5-12-10-38c-1-5-3-9-4-14-1-8 0-18 3-25z" class="R"></path><path d="M434 444v-10c-1-2-1-2-1-3-1-1 0-3 0-4v-1-3h0c0-1-1-2-1-2l-1-3v-2l-3-9c0-1-1-2-1-2l-2-8s-1-1-1-2l-4-12v-2l-1-1v-2l-1-5-1-2h0c0-1-1-2-1-2l-1-6-1-1h1l2 4 2 6v1c0 1 0 1 1 2v2l2 7 1 1v2l1 2 1 4c0 1 0 1 1 2v2l4 11v1c0 1 0 1 1 2l5 16 1 3v2c0 1 0 1 1 2v2l3 7 1 4v3s-1 1 0 1c0 3 1 4 1 7h-3l-2-8h-2v4c0 1 0 1-1 2l-1-9v-3z" class="t"></path><path d="M428 383c1 1 1 0 1 1v1c0 1 0 3 1 5 0 2 0 6 1 9h0c0 1 0 1 1 2 0 1 1 3 1 4 1 1 1 2 1 4v2c0-2 1-6 0-8-1 0-1 0-1-1s0-2-1-3l1-1s0-1 1-2c0 2 0 4 1 5v2c0 2 0 4 1 5l1-1v-3h1l1 5h0c1 2 1 3 2 5h0l1 4c0-1 1-2 2-4 0 3 0 7 1 9h0l1 2h1c1 2 1 2 1 4v1c1 2 1 3 2 6 0 1 0 3 1 5v5c-1 1-1 1-1 2 0 3 1 5 0 8 1 2 1 2 2 3-1 2-2 5-2 7 1 1 1 1 0 3l-1 1-1-1-4-15-2-4v-3l-1-4-3-7v-2c-1-1-1-1-1-2v-2l-1-3-5-16c-1-1-1-1-1-2v-1l-4-11v-2c-1-1-1-1-1-2l3-2v-1-2-2c-1-2-1-2-1-3h1z" class="H"></path><path d="M439 409c1 2 1 3 2 5h0l1 4 4 17-3-2c-1 0-1-1-1-1 1-3 0-6-1-9-1-4-2-9-2-14z" class="C"></path><path d="M442 418c0-1 1-2 2-4 0 3 0 7 1 9h0l1 2h1c1 2 1 2 1 4v1c1 2 1 3 2 6 0 1 0 3 1 5v5c-1 1-1 1-1 2 0 3 1 5 0 8-1-2-1-5-1-8-1-4-2-9-3-13l-4-17z" class="Y"></path><path d="M445 423h0l1 2h1c1 2 1 2 1 4v1c1 2 1 3 2 6 0 1 0 3 1 5v5c-1 1-1 1-1 2l-5-25z" class="C"></path><defs><linearGradient id="AN" x1="404.147" y1="262.163" x2="417.366" y2="273.087" xlink:href="#B"><stop offset="0" stop-color="#1c0000"></stop><stop offset="1" stop-color="#370706"></stop></linearGradient></defs><path fill="url(#AN)" d="M415 226l1-2h0c1-2 7-6 9-7 5-1 7-4 12-4-4 3-6 6-6 11l-1 1v2c1 7-3 14-4 21 0 1 0 2-1 3l-1 4v1 1c0 1 0 2-1 3-1 2-1 5-1 7h0c-1 1-1 2-1 3s-1 2-1 3h-1 0c-1 3-2 7-2 10-1 1-1 3-2 4-1-2 0-5 0-7 0 0-1 1-1 2-1 1 0 2-1 3v1 1 2c-1 1-2 3-3 4 0 1-1 1-1 1-1 1-1 2-1 2l-1 1h-1c0-1 0-1-1-2h-1c0 2 0 2-1 4v1 2 1c-1 1 0 1-1 1h-1l1-2-1-1c-1-2-2-5-2-8h-1v2l-2 2-3-12c-1-6 0-14 2-20l-2 1c1-3 2-7 2-10 0-2 1-1 1-2v-3c1-2 2-4 2-6l6-8c1-3 7-8 10-10l1-1z"></path><path d="M417 247c0-1 0-2 1-3v11l-1-1h0c0 3 0 5-1 7v1-3c1-3 1-9 1-12z" class="g"></path><defs><linearGradient id="AO" x1="411.216" y1="252.341" x2="397.854" y2="260.842" xlink:href="#B"><stop offset="0" stop-color="#210000"></stop><stop offset="1" stop-color="#380203"></stop></linearGradient></defs><path fill="url(#AO)" d="M408 243v4c3 3 1 6 2 9-3 3-2 6-4 9v2c0 1-1 2-2 4v-3l1-1h0v-1l-1 1-1-1-2 1-1-1 4-13c2-3 2-6 4-10z"></path><defs><linearGradient id="AP" x1="419.435" y1="234.868" x2="406.856" y2="254.32" xlink:href="#B"><stop offset="0" stop-color="#080001"></stop><stop offset="1" stop-color="#1b0001"></stop></linearGradient></defs><path fill="url(#AP)" d="M414 233l1-1v1 1c1 2 0 5 1 8v1 8h0l-1 2c-1 2-1 2-3 3v2c-2-1-2-1-2-2-1-3 1-6-2-9v-4c2-3 3-6 5-9l1-1z"></path><path d="M400 266l1 1 2-1 1 1 1-1v1h0l-1 1v3 9 1l1 1h0l1-1c0-2 0-3 2-4v-2l1-1 1 1v9c2-2 2-6 4-9h0c0-1 0-2 1-2 0-2 1-3 1-4 1 1 1 2 0 3v3 1c0 1 0 3-1 4 0 0-1 1-1 2-1 1 0 2-1 3v1 1 2c-1 1-2 3-3 4 0 1-1 1-1 1-1 1-1 2-1 2l-1 1h-1c0-1 0-1-1-2h-1c0 2 0 2-1 4v1 2 1c-1 1 0 1-1 1h-1l1-2-1-1c-1-2-2-5-2-8h-1c-1-6 0-13 1-19-1-3 0-5 1-8z" class="Q"></path><path d="M404 280v1l1 1h0c0 1 1 3 0 4l-2-1c0-2 0-4 1-5z" class="K"></path><path d="M399 274v3l2-1v1c-2 3 0 6-2 10-1 2 0 4 0 6h-1c-1-6 0-13 1-19zm4 25v-6l1 1 1-1h0v-1-3c1-2 1-4 1-6 2 0 2 0 3-1v1 6 1 1 3c-1 1-1 2-1 2l-1 1h-1c0-1 0-1-1-2h-1c0 2 0 2-1 4z" class="D"></path><path d="M406 297v-4c1-1 1-1 1-3v-2h1l1 3v3c-1 1-1 2-1 2l-1 1h-1z" class="H"></path><path d="M415 226h1c1 2-1 4-2 7l-1 1c-2 3-3 6-5 9-2 4-2 7-4 10l-4 13c-1 3-2 5-1 8-1 6-2 13-1 19v2l-2 2-3-12c-1-6 0-14 2-20l-2 1c1-3 2-7 2-10 0-2 1-1 1-2v-3c1-2 2-4 2-6l6-8c1-3 7-8 10-10l1-1z" class="U"></path><defs><linearGradient id="AQ" x1="401.086" y1="232.224" x2="403.414" y2="259.276" xlink:href="#B"><stop offset="0" stop-color="#181818"></stop><stop offset="1" stop-color="#38363a"></stop></linearGradient></defs><path fill="url(#AQ)" d="M415 226h1c1 2-1 4-2 7l-1 1c-2 3-3 6-5 9-2 4-2 7-4 10v-3l1-3c1-2 1-3 2-4-1 1-2 2-2 3-1 1-1 2-2 3s-2 2-2 3c-1 2-2 3-3 4l-3 9-2 1c1-3 2-7 2-10 0-2 1-1 1-2v-3c1-2 2-4 2-6l6-8c1-3 7-8 10-10l1-1z"></path><path d="M415 226h1c1 2-1 4-2 7l-1 1h-1c-2 1-3 2-4 3l6-10 1-1z" class="g"></path><path d="M408 237c1-1 2-2 4-3h1c-2 3-3 6-5 9-2 4-2 7-4 10v-3l1-3c1-2 1-3 2-4-1 1-2 2-2 3-1 1-1 2-2 3s-2 2-2 3c-1 2-2 3-3 4 2-7 6-13 10-19z" class="Q"></path><path d="M415 226l1-2h0c1-2 7-6 9-7 5-1 7-4 12-4-4 3-6 6-6 11l-1 1v2c1 7-3 14-4 21 0 1 0 2-1 3l-1 4v1 1c0 1 0 2-1 3-1 2-1 5-1 7h0c-1 1-1 2-1 3s-1 2-1 3h-1 0c-1 3-2 7-2 10-1 1-1 3-2 4-1-2 0-5 0-7 1-1 1-3 1-4v-1-3c1-1 1-2 0-3v-2-5-1c1-2 1-4 1-7h0l1 1v-11c-1 1-1 2-1 3l-1-4v-1c-1-3 0-6-1-8v-1-1l-1 1c1-3 3-5 2-7h-1z" class="E"></path><path d="M416 242v-6c0-2 0-4 1-5l1-1v6c0 1 0 3 1 4h0c-1 2-1 3-1 4-1 1-1 2-1 3l-1-4v-1zm11-15l-2-2v-1c-1-1-1-2-1-3l5-5h1c0 1 0 2-1 3 0 0 0 1-1 1v6h0l-1 1z" class="K"></path><path d="M427 227l1-1v6c1-1 1-3 2-5 1 7-3 14-4 21 0 1 0 2-1 3 0 1-1 0-1 0h0c1-2 0-4 0-6 0-1 0-2 1-3l2-8v-7z" class="L"></path><path d="M421 254l3-9c0 2 1 4 0 6h0s1 1 1 0l-1 4v1 1c0 1 0 2-1 3-1 2-1 5-1 7h0c-1 1-1 2-1 3s-1 2-1 3h-1 0c-1 3-2 7-2 10-1 1-1 3-2 4-1-2 0-5 0-7 1-1 1-3 1-4v-1-3c1-1 1-2 0-3v-2-5-1c1-2 1-4 1-7h0l1 1v8c0 1 0 1 1 1v1c1-3 1-7 2-10v-1z" class="K"></path><path d="M421 254l3-9c0 2 1 4 0 6h0s1 1 1 0l-1 4v1 1c0 1 0 2-1 3-1 2-1 5-1 7h0c-1 1-1 2-1 3s-1 2-1 3h-1c0-1 0-2 1-3v-2l2-6v-2-4l-1-2z" class="D"></path><path d="M421 270l1 1h1c1-2 2-2 2-4 1-1 1-2 2-3l2 3c-1 3-3 6-3 10 0 3-3 4-4 6l-1 4-2 1c0 1 0 2 1 3v1c0 2 1 3 1 5h0l1 2h0v1c0 1 0 1 1 2v6l-1 1c0 1 1 3 0 4 0 1 0 2-1 3v3c0 1 0 0-1 2v1l-1 3-2 5v5c0 1 0 2-1 2v4c0 1 0 2-1 3v2 3 1c0 1 1 2 2 3 0 0 1 1 1 2s1 2 2 3l1 2v-10c1-1 1-2 1-3 1-3 2-7 3-10 1-1 2-3 3-4l1-1c1-2 3-3 5-4 2 0 4 1 6 2 3 2 4 6 5 9 0 1 1 3 1 4l1 5v3c-1 2-1 4-1 7-1-1-1-1-1-2l-3-6v-1c0-1-1-1-2-2h0-5v-1l-1-1c-1 1-1 6-1 7v1 6 1c-1 2 0 5 0 7 0 6-1 11 0 17l1 12c-1 1-1 2-1 2l-1 1c1 1 1 2 1 3s0 1 1 1c1 2 0 6 0 8v-2c0-2 0-3-1-4 0-1-1-3-1-4-1-1-1-1-1-2h0c-1-3-1-7-1-9-1-2-1-4-1-5v-1c0-1 0 0-1-1h-1c0 1 0 1 1 3v2 2 1l-3 2-1-4-1-2v-2l-1-1-2-7v-2c-1-1-1-1-1-2v-1l-2-6-2-4v-1-2c-1-1 0-1-1-2l-1-2v-1-2l-1-1v-2c-1-1 0-1-1-2l-5-17-1-2v-2l-3-9-1-2v-2c-1-1 0-1-1-2l-2-6v-2c-1-1-1-1-1-2s-1-3-1-4l2-2v-2h1c0 3 1 6 2 8l1 1-1 2h1c1 0 0 0 1-1v-1-2-1c1-2 1-2 1-4h1c1 1 1 1 1 2h1l1-1s0-1 1-2c0 0 1 0 1-1 1-1 2-3 3-4v-2-1-1c1-1 0-2 1-3 0-1 1-2 1-2 0 2-1 5 0 7 1-1 1-3 2-4 0-3 1-7 2-10h0 1c0-1 1-2 1-3z" class="e"></path><path d="M408 296c1 2 0 3 1 4h2v-2c0-1 0-1 1-2h1c0 2 0 3-1 5v2c-1 2 0 4-1 5v2 4l-1 1v1l-1 5c0 2 1 4 0 6-3-2 0-16-1-20-1-3-1-6-1-10l1-1z" class="F"></path><path d="M425 337c1-1 2-3 3-4l1-1c1-2 3-3 5-4 2 0 4 1 6 2h-1-1l-2 3c0 1 1 3 2 4l-1 1v1h-3l-2-2-1 1-1 1c0 1-1 1-1 2-1 2-1 3-1 5-1 3-1 6-1 9 0 1 0 2-1 3-1 3 1 7-1 10-1 0-2 0-3-1-1-2 0-6-1-7v-10c1-1 1-2 1-3 1-3 2-7 3-10z" class="L"></path><path d="M425 337c1-1 2-3 3-4l1-1c1-2 3-3 5-4 2 0 4 1 6 2h-1-1l-2 3c0 1 1 3 2 4l-1 1v1h-3l-2-2-1 1-1 1c-1 0-1 1-2 1 0 1-1 2-1 2h0c1-1 1-2 2-4 0-3 3-3 4-6l1-1c1 0 2 0 3-1l-1-1-1 1v-1l-2 1-1 1c-1 1-2 3-3 5-2 0-2 1-2 2l-1 1v1l-1-3z" class="K"></path><path d="M440 330c3 2 4 6 5 9 0 1 1 3 1 4l1 5v3c-1 2-1 4-1 7-1-1-1-1-1-2l-3-6v-1c0-1-1-1-2-2h0-5v-1l-1-1c-1 1-1 6-1 7v1 6 1c-1 2 0 5 0 7 0 6-1 11 0 17l1 12c-1 1-1 2-1 2l-1 1c1 1 1 2 1 3s0 1 1 1c1 2 0 6 0 8v-2c0-2 0-3-1-4 0-1-1-3-1-4-1-1-1-1-1-2h0c-1-3-1-7-1-9-1-2-1-4-1-5v-1c0-1 0 0-1-1v-9c1-1 1-4 1-6 1-1 0-4 0-6 1-1 1-4 1-5v-2-4c1-3 1-6 2-9 0-2 0-3-1-4l1-1 2 2h3v-1l1-1c-1-1-2-3-2-4l2-3h1 1z" class="M"></path><path d="M445 339c0 1 1 3 1 4l1 5v3c-1 2-1 4-1 7-1-1-1-1-1-2l-3-6v-1c0-1-1-1-2-2h0-5v-1l3 1 1-1c0-1 0-1 1-2h1l1 1 1 1c2 2 0 3 0 5 1 1 2 2 2 4v-3-1l2-2c-1-1-1-2-1-3s0-1-1-1v-6z" class="H"></path><path d="M421 270l1 1h1c1-2 2-2 2-4 1-1 1-2 2-3l2 3c-1 3-3 6-3 10 0 3-3 4-4 6l-1 4-2 1c0 1 0 2 1 3v1c0 2 1 3 1 5h0v3c-1 2-1 5 0 7h0c-1 2-1 4-1 6h-1c-1-1 0-3-1-5h-1c-1 2 0 6-1 9h0c0 3-2 4-3 6h-2l-1 1 2 2c0 1 0 2-1 3h1v1c-2-2-2-3-2-5s0-5 1-7v-1-1c1-2 0-4 0-6v-2c1-1 0-3 1-5v-2c1-2 1-3 1-5h-1c-1 1-1 1-1 2v2h-2c-1-1 0-2-1-4 0 0 0-1 1-2 0 0 1 0 1-1 1-1 2-3 3-4v-2-1-1c1-1 0-2 1-3 0-1 1-2 1-2 0 2-1 5 0 7 1-1 1-3 2-4 0-3 1-7 2-10h0 1c0-1 1-2 1-3z" class="D"></path><path d="M415 280c0 2-1 5 0 7 1-1 1-3 2-4 0-3 1-7 2-10h0 1c0 1 0 2-1 4v2l-2 5c0 2-1 4-1 6 0 3 0 5-1 8-1 2-1 6-1 9h0v-6c0-1 0 1 0-1l-1-1v-1c1 0 1-1 1-2s0-4-1-5c0 2 0 3-1 4l1 1h-1c-1 1-1 1-1 2v2h-2c-1-1 0-2-1-4 0 0 0-1 1-2 0 0 1 0 1-1 1-1 2-3 3-4v-2-1-1c1-1 0-2 1-3 0-1 1-2 1-2z" class="M"></path><path d="M421 270l1 1h1c1-2 2-2 2-4 1-1 1-2 2-3l2 3c-1 3-3 6-3 10 0 3-3 4-4 6l-1 4-2 1c0 1 0 2 1 3v1c0 2 1 3 1 5h0v3c-1 2-1 5 0 7h0c-1 2-1 4-1 6h-1c-1-1 0-3-1-5v-1-2c0-1-1-1-1-2 0-2-1-3-2-5 1-3 1-5 1-8 0-2 1-4 1-6l2-5v-2c1-2 1-3 1-4s1-2 1-3z" class="Q"></path><defs><linearGradient id="AR" x1="382.127" y1="441.212" x2="402.214" y2="436.939" xlink:href="#B"><stop offset="0" stop-color="#1c1a1d"></stop><stop offset="1" stop-color="#4e4c51"></stop></linearGradient></defs><path fill="url(#AR)" d="M364 380c4-1 8-1 12 1s5 6 7 10l38 123c-1 0-1-1-1-2-1-2-1-3-2-5l-1 1c-1-5-2-12-5-16h-2l-1 1c-2 1-4 4-6 4h-1-1l-1-1c-3 0-7 2-10 0l-36-111 3-2 2 1c1-1 4-2 5-4z"></path><path d="M364 380c4-1 8-1 12 1s5 6 7 10h-1c-3-2-5-6-9-5l1 1h-1c-1 3-1 6 0 10v4-1c0-1-1-1-1-2v-1c-1 2 2 8 2 11 1 2 2 3 3 5v1c1 1 1 2 1 3v1l2 4v1h0c1 2 0 5 1 7v4c1 3 1 6 1 8h0l-2-6c-1 0-1-1-1-1 0 2 2 6 3 9l19 45c0 1 2 5 2 6l-3 1c-3 0-7 2-10 0l-36-111 3-2 2 1c1-1 4-2 5-4z" class="c"></path><defs><linearGradient id="AS" x1="502.716" y1="661.257" x2="537.009" y2="655.535" xlink:href="#B"><stop offset="0" stop-color="#210000"></stop><stop offset="1" stop-color="#450202"></stop></linearGradient></defs><path fill="url(#AS)" d="M514 548l2 1v3c1 1 2 3 3 5l1 4c2 1 3 3 5 4 1 1 1 2 1 3 1 4 3 8 4 12l3 3 8 23 1 2v1 4c-1 2-2 5-2 8l-3 11c0 1-1 3-2 5v1c1 2 0 4 0 7 1 2 2 1 1 4v1c-1 1-1 2-1 3l-1 2c0 2-1 3-1 4l1 1c0 1 0 2-1 3v1c0 1-1 2-1 3l-1-1c0 6-3 13-5 19l-3 10-1 7h-5l-1-3-4-17c-2-3-3-6-3-9l-3-1c0-5-2-9-3-13l-5-16h0l-1-4v-1-1c-1-2 0-2-1-4h-1l-1-1c0-1 0-3 1-4 0-2 2-3 3-6 0-1 1-3 2-5 0-1 1-2 2-3 1-2 1-4 2-6 1 0 1-1 1-2l3-8 2-9h-1 0c3-9 5-15 6-24l-1-17z"></path><path d="M514 630c-1 2 0 4-2 5v-1h0c0-1 1-2 1-3-1-2-3-3-2-5v-1c-1 0-1 0-2-1l2-1c1 2 2 4 3 7z" class="g"></path><path d="M524 680c1 2 1 3 2 5l-3 10-1 7h-5l-1-3h3l1-1c0-3 1-5 3-7l1-1v-1-1c1-1 1-2 1-3-1-1-1-2-1-4v-1z" class="L"></path><path d="M504 640c1 2 1 4 2 6 1 1 1 2 0 3v2 2h0l-1-1v2h0c1 1 1 2 2 3h-1c0 3 0 5 1 7l1 3v2c1 2 2 4 2 6 1 2 2 4 2 7-2-3-3-6-3-9-1-4-4-8-4-12v-1c-1-2 0-3-1-5-1-4 0-10 0-15z" class="K"></path><path d="M499 624v1l-2 2h1 0 1c1-1 2-1 2-3h1l4 4c0 1-1 3 0 4 0 1 2 1 2 2v3c-1 0-1 1-2 2v2c1 1 0 3 0 5-1-2-1-4-2-6 0-2-1-3-1-4 0-2 1-6 0-7 0-1-1-2-2-2h0-1c-2 1-3 2-5 4l1 2h-1l-1-1c0-1 0-3 1-4s2-3 4-4z" class="g"></path><path d="M530 653c0 3 1 6 0 8v3 2h1c0 6-3 13-5 19-1-2-1-3-2-5l3-15c1-3 2-5 3-8v-4z" class="F"></path><path d="M530 653c2-5 3-10 5-15 1 2 0 4 0 7 1 2 2 1 1 4v1c-1 1-1 2-1 3l-1 2c0 2-1 3-1 4l1 1c0 1 0 2-1 3v1c0 1-1 2-1 3l-1-1h-1v-2-3c1-2 0-5 0-8z" class="C"></path><path d="M496 633l-1-2c2-2 3-3 5-4h1 0c1 0 2 1 2 2 1 1 0 5 0 7 0 1 1 2 1 4 0 5-1 11 0 15 1 2 0 3 1 5v1c0 4 3 8 4 12l-3-1c0-5-2-9-3-13l-5-16h0l-1-4v-1-1c-1-2 0-2-1-4zm4-16c0-1 1-2 2-3 1-2 1-4 2-6h2c2 1 4 1 5 2l1 1c2 1 3 3 3 5 3 5 5 10 5 15v8l1 1c-1 0-1 1-1 2s0 1 1 3c-1 1-1 1-2 1l-4 13v1l-1-1c0-6-1-15 2-21l-2-8c-1-3-2-5-3-7l-2-2c-1-1-3-1-4-1-2 1-5 2-6 4-2 1-3 3-4 4 0-2 2-3 3-6 0-1 1-3 2-5z" class="Q"></path><path d="M498 622c3-2 5-4 10-2 1 0 1 0 2 1h1l1-1c1 1 0 2 1 3 2 2 2 4 3 7l1 7-1 1-2-8c-1-3-2-5-3-7l-2-2c-1-1-3-1-4-1-2 1-5 2-6 4-2 1-3 3-4 4 0-2 2-3 3-6z" class="D"></path><path d="M500 617c0-1 1-2 2-3 1-2 1-4 2-6h2c2 1 4 1 5 2l1 1c2 1 3 3 3 5l-4-3c-1 0-1 0-2 1l1 2h-1l-5-2c-1 1-2 3-3 4l-1-1z" class="M"></path><path d="M514 548l2 1v3c1 1 2 3 3 5l1 4c2 1 3 3 5 4 1 1 1 2 1 3 1 4 3 8 4 12l3 3 8 23 1 2v1 4c-1 2-2 5-2 8l-3 11c0 1-1 3-2 5v-1 1c-2-1-3-2-4-2-1 1-1 1-1 2l-1-2v-2c-1-2-2-5-4-6l-1 1c-1-1-1-2-1-3h-1c-1 2 0 6 0 8-1 2-1 4-2 6v-8c0-5-2-10-5-15 0-2-1-4-3-5l-1-1c-1-1-3-1-5-2h-2c1 0 1-1 1-2l3-8 2-9h-1 0c3-9 5-15 6-24l-1-17z" class="e"></path><path d="M510 589c1 0 1-1 2-2l1 3v1c-1 1-1 1-1 3 0 1-1 1-1 2l1 2c-2 1-3 2-3 4-1 2-2 2-2 4-1 1-1 1-1 2h-2c1 0 1-1 1-2l3-8 2-9z" class="D"></path><path d="M507 606c1 0 2-1 4 1 3 2 5 5 7 9s3 10 3 15h-1c0-5-2-10-5-15 0-2-1-4-3-5l-1-1c-1-1-3-1-5-2 0-1 0-1 1-2z" class="C"></path><path d="M514 548l2 1v3c1 1 2 3 3 5l1 4 1 4-3-2c0 6 1 11 2 17 1 3 1 6 2 10l-1-1-1 1c1 1 2 1 2 3v1 3l1 1c0 1 0 1-1 1 0 1 0 2 1 3-1 1-1 2-1 3s-1 3-2 4v-2-2c-1-2-1-5-2-7v-3c0-3-2-6-2-9 0-2-1-2-1-3l-2-1c-1 2-1 4-1 5-1 1-1 2-2 2h-1 0c3-9 5-15 6-24l-1-17z" class="M"></path><path d="M514 548l2 1v3c1 1 2 3 3 5l1 4 1 4-3-2c0-3 0-5-2-7v-1c-2 3-1 7-1 10l-1-17z" class="H"></path><path d="M520 561c2 1 3 3 5 4 1 1 1 2 1 3 1 4 3 8 4 12l3 3 8 23 1 2v1 4l-1-2-1-1c-2-1-2-4-3-6-2-2-1-2-3-2l-1 1c1 1 1 3 1 4-1 1-1 1-3 2l-1-1h0c1-2 1-3 0-4v-2c-1-1-1-2-1-3-1-1 0-2 0-3l-6-21c-1-4-1-7-2-10l-1-4z" class="B"></path><path d="M530 580l3 3 8 23v-1c-2-1-3-2-4-4s-3-5-4-7c0-5-2-9-3-14z" class="G"></path><path d="M520 561c2 1 3 3 5 4 1 1 1 2 1 3h-1c0 2 0 4 1 7l3 13c0 3 1 6 1 9-1 0 0 0-1-1l-6-21c-1-4-1-7-2-10l-1-4z" class="O"></path><path d="M477 444v-6l1-1c0-1-1-1 0-2v-3-4l-1-2v-1h1c0 1 1 3 1 4l1-2 3 22 2 2c-1-3-1-6 0-8 1 1 2 2 2 3 1 3 0 7 3 9v1l13 43c0 5 1 11 2 16 1 3 0 7 1 10v4l1-1v-1c1 0 1 0 2 1 2 3 3 5 3 9v4c1 3 1 5 2 7l1 17c-1 9-3 15-6 24h0 1l-2 9-3 8c0 1 0 2-1 2-1 2-1 4-2 6-1 1-2 2-2 3-1 2-2 4-2 5-1 3-3 4-3 6-1 1-1 3-1 4l-1-1c-1 0-1 0-2 1v-3c0-1 0-3 1-4-1-1-1-3-1-5 0-3-1-5-1-8l-2-7v-3l-9-33-1-2-10-32c0-2-1-5-2-7l1-12c-1-4-1-8 0-11l1-8c0-2 1-3 1-5v-6c1-1 1-6 1-7 0-4 0-7 1-10 0-1 0-2 1-3h0l1-3 1-3c1-6 2-10 3-16z" class="r"></path><path d="M496 517l1 8v6l-2 13c-1-1 0-3 0-5 0-4 0-9-1-13v-2c0-2-1-4-1-6 2 2 2 4 2 6h0l1 1v-3l-1-1c0-1 0-2 1-3v-1z" class="D"></path><path d="M492 545c0-5 1-10 1-15 0-3-1-7-1-10h0l-1-1c0-2 0-2 1-3l1 2c0 2 1 4 1 6v2c1 4 1 9 1 13 0 2-1 4 0 5l-2 7h-1c-1-2 0-4 0-6z" class="U"></path><path d="M467 505c2-3 2-6 4-8 1-2 1-4 3-5-1 4-2 7-2 11l-1 5v5c-1 3-1 9-1 12 0-1 0-3-1-4h-1c-1-1-1-3-1-5-1-4-1-8 0-11z" class="B"></path><path d="M487 472c1 2 1 4 1 6v1h1c1 1 1 2 1 4l1 1c-1 2-1 4 0 7 1 2 1 3 1 5 0-1 0-1-1-2-1-2-3-3-6-4-1 0-3 0-4-1 0-1 0-1 1-2v-1s1-1 1-2c1-1 2-3 2-4l1-1 1-7z" class="Y"></path><path d="M487 472c1 2 1 4 1 6v1h1c1 1 1 2 1 4h0l-1 1-1 1c-1 1-2 1-2 1-1-1-1-1 0-3v-3-1l1-7z" class="P"></path><path d="M491 484c0 2 1 4 2 6 0 1 0 2 1 3 1 2 1 3 1 5 1 3 3 6 3 9 0 1 0 0 1 1v3 1c0 1 0 1 1 3v2h0l1 8c-1 3 0 8-3 10v-11-5-1c0-1 0-2-1-3 0-5-2-10-3-15-1-1-1-3-2-4 0-2 0-3-1-5-1-3-1-5 0-7z" class="B"></path><path d="M485 443c1 1 2 2 2 3 1 3 0 7 3 9v1l13 43c0 5 1 11 2 16 1 3 0 7 1 10l-1 3c-1 1-1 2-1 3v1c-1-1 0-3 0-4v-2c1-1 1-2 0-3l-1 1c-1 2-1 3-1 6h0v2l-1 1h0v-3-5l-1-8h0v-2c-1-2-1-2-1-3v-1-3c-1-1-1 0-1-1 0-3-2-6-3-9 0-2 0-3-1-5-1-1-1-2-1-3-1-2-2-4-2-6l-1-1c0-2 0-3-1-4h-1v-1c0-2 0-4-1-6v-9-2c0-2-2-8-2-10-1-3-1-6 0-8z" class="F"></path><path d="M493 490c1-1 1-2 2-3 1 6 4 12 4 17v2c1 3 2 8 1 11h0v-2c-1-2-1-2-1-3v-1-3c-1-1-1 0-1-1 0-3-2-6-3-9 0-2 0-3-1-5-1-1-1-2-1-3z" class="G"></path><path d="M487 463h0c1 1 1 2 2 3v2l1 1c1 2 0 4 1 5 1 5 3 9 4 13-1 1-1 2-2 3-1-2-2-4-2-6l-1-1c0-2 0-3-1-4h-1v-1c0-2 0-4-1-6v-9z" class="C"></path><path d="M477 444v-6l1-1c0-1-1-1 0-2v-3-4l-1-2v-1h1c0 1 1 3 1 4l1-2 3 22 2 2c0 2 2 8 2 10-2 3-1 8-1 11-1 4-2 8-4 12-1 3-3 4-5 6-1 1-1 3-2 4-1 2-1 4-2 6l-1 3c0-4 1-7 2-11-2 1-2 3-3 5-2 2-2 5-4 8l1-8c0-2 1-3 1-5v-6c1-1 1-6 1-7 0-4 0-7 1-10 0-1 0-2 1-3h0l1-3 1-3c1-6 2-10 3-16z" class="G"></path><path d="M473 463l2 2h0c0 3 1 4 1 7v1l1 1c1 1 1 1 1 3-2 0-2 0-2 2-1 1 0 3 0 4s-2 3-2 4c-1 3-4 4-4 8l-2 2c0-2 1-3 1-5v-6c1-1 1-6 1-7 0-4 0-7 1-10 0-1 0-2 1-3h0l1-3z" class="B"></path><path d="M471 469c1 2 2 3 1 6 0 1-1 2-2 4 0-4 0-7 1-10z" class="H"></path><path d="M479 429l1-2 3 22 2 2c0 2 2 8 2 10-2 3-1 8-1 11-1 4-2 8-4 12-1 3-3 4-5 6-1 1-1 3-2 4-1 2-1 4-2 6l-1 3c0-4 1-7 2-11 0-1 2-4 2-4 1-1 1-2 1-3 1-1 2-2 2-4 2-9 3-20 2-29-1-8 0-15-2-23z" class="Y"></path><path d="M483 449l2 2c0 2 2 8 2 10-2 3-1 8-1 11-1 4-2 8-4 12l-1-1v-2c0-5 1-10 1-14 1-6 1-12 1-18z" class="G"></path><path d="M472 528c0-5-1-10 0-14 1-5 1-10 4-15 1-2 3-6 6-7 2 0 4 0 6 2 6 4 7 17 8 23v1c-1 1-1 2-1 3l1 1v3l-1-1h0c0-2 0-4-2-6l-1-2c-1 1-1 1-1 3l1 1h0c0 3 1 7 1 10 0 5-1 10-1 15h-3l-1-2c0-2-1-4 0-6 0-2-1-4-1-6v-5c-1-2-1-4-2-5-1 1 0 2-1 3v6l-1 1c0 2 0 3-2 5v4 1c0 1-1 2-2 3l2 2c0 3 1 10 0 13v2 2c1 2 1 2 1 4h0c-2 2-2 3-2 5l-1-3h0l-1-2-10-32c0-2-1-5-2-7l1-12c0 2 0 4 1 5h1c1 1 1 3 1 4 1 1 1 2 2 3z" class="M"></path><path d="M479 525l-1-1v-1-3c0-1 1-3 1-4h1l1 1-1 1c0 2 0 5-1 7z" class="e"></path><path d="M479 525c1-2 1-5 1-7l1 5h1s0-1 1-2c0 2-1 4-1 5-1 1-1 2-1 3l-2-4zm3-12c0-2 1-2 2-3 1 0 2 0 3 2v1h-1c-1 2-2 3-2 5-1-2-1-4-2-5z" class="Q"></path><path d="M482 513c1 1 1 3 2 5-1 1-1 2-1 3-1 1-1 2-1 2h-1l-1-5 1-1c1-1 1-3 1-4z" class="L"></path><path d="M481 498h0c3-1 3 0 6 2 0 0 1 0 1 1v1c1 1 3 4 3 5-1-1-2-2-2-3l-1-1c-1-1-3-2-4-2l-1 1-1-1h-2l-2 2h0-1l1-1c0-1 0-1 1-2l2-2z" class="Q"></path><path d="M486 513c0 2 0 3 1 4 0 1 0 2 1 4 0 3-1 5 0 9 0 2 1 4 0 7 0-2-1-4-1-6v-5c-1-2-1-4-2-5-1 1 0 2-1 3v6l-1 1c0 2 0 3-2 5v-3c1-1 1-5 1-7 0-1 1-3 1-5 0-1 0-2 1-3 0-2 1-3 2-5z" class="F"></path><path d="M467 516c0 2 0 4 1 5h1c1 1 1 3 1 4 1 1 1 2 2 3 0 2 0 4 1 6v3l-1-1c0-1 0-1-1-2l-3 1c0-2-1-5-2-7l1-12z" class="C"></path><path d="M487 517l1-2v-1c-1-1-1-2 0-4-1-1-1-2-2-3h1c1 1 1 1 2 3l1-1c1 1 1 2 2 4v3c-1 1-1 1-1 3l1 1h0c0 3 1 7 1 10 0 5-1 10-1 15h-3l-1-2c0-2-1-4 0-6 1-3 0-5 0-7-1-4 0-6 0-9-1-2-1-3-1-4z" class="D"></path><path d="M483 531l1-1v-6c1-1 0-2 1-3 1 1 1 3 2 5v5c0 2 1 4 1 6-1 2 0 4 0 6l1 2h3c0 2-1 4 0 6h1c0 2 0 2 1 3 2-7 3-12 4-19 3-2 2-7 3-10v5 3h0l1-1v-2h0c0-3 0-4 1-6l1-1c1 1 1 2 0 3v2c0 1-1 3 0 4v-1c0-1 0-2 1-3l1-3v4l1-1v-1c1 0 1 0 2 1 2 3 3 5 3 9v4c1 3 1 5 2 7l1 17c-1 9-3 15-6 24h0 1l-2 9-3 8c0 1 0 2-1 2-1 2-1 4-2 6-1 1-2 2-2 3-1 2-2 4-2 5-1 3-3 4-3 6-1 1-1 3-1 4l-1-1c-1 0-1 0-2 1v-3c0-1 0-3 1-4-1-1-1-3-1-5 0-3-1-5-1-8l-2-7v-3l-9-33h0l1 3c0-2 0-3 2-5h0c0-2 0-2-1-4v-2-2c1-3 0-10 0-13l-2-2c1-1 2-2 2-3v-1-4c2-2 2-3 2-5z" class="H"></path><path d="M491 575c0 1-1 2-1 3h-1c0-5 1-8 3-12l2 2-3 7z" class="B"></path><path d="M494 567h1l2-1s1 0 1 1v1 2l-1 2c0 1 0 1 1 2h-1-1c-1 1-3 2-4 2l-1-1 3-7v-1z" class="F"></path><path d="M481 536c2-2 2-3 2-5 0 2 0 8 1 10 0 4-1 10 0 13l1 3v1 1l-1-1v-1h-1c0 2 1 3 0 4-2-2-1-16-2-20v-1-4z" class="B"></path><path d="M499 552v1 3h0c0 2 0 3 1 5 2 0 2 0 4 1l1 1h0c1 3 2 6 2 9h0-1c0-3-1-7-3-9-1-1-2-1-3-1-3 0-5 3-6 5v1l-2-2 7-14z" class="S"></path><path d="M498 599l2-10c1-1 1-1 1-2 1-2 0-3 1-5 0-1 1-1 1-2v-4c1-2 1-4 1-6h0c1 2 1 5 0 7v1 1 12c-1 3-2 7-4 10l-2-2z" class="F"></path><path d="M488 602c0-1 0-2-1-3l1-1v-3-1l1-1c1-4 2-8 4-12l1 1c0 1 0 1 1 3h-1v4c0 2-1 3-1 5-1 3 0 10-2 12-1 0-2-1-3-1v-3z" class="U"></path><path d="M483 531l1-1v-6c1-1 0-2 1-3 1 1 1 3 2 5v5c0 4 1 22 0 24h-1l-1 2-1-3c-1-3 0-9 0-13-1-2-1-8-1-10z" class="C"></path><path d="M507 577s0-1 1-2v-4l1-1v-2l1-1v3 1c0 1 0 2-1 3v3c0 2 0 4-1 6v3-1l1 1v3h0 1l-2 9-3 8c0 1 0 2-1 2-1 2-1 4-2 6-1 1-2 2-2 3-1 2-2 4-2 5-1 3-3 4-3 6-1 1-1 3-1 4l-1-1c-1 0-1 0-2 1v-3c0-1 0-3 1-4-1-1-1-3-1-5 0-3-1-5-1-8 0 1 1 2 1 3h1l6-16 2 2c2-3 3-7 4-10l1-5 1-3v-2l1-4z" class="O"></path><path d="M509 589h1l-2 9-1-1h-1v1c-1 1-2 1-3 2 0-1 1-2 1-3 2-2 3-5 5-8z" class="L"></path><path d="M498 599l2 2-8 24c-1-1-1-3-1-5 0-3-1-5-1-8 0 1 1 2 1 3h1l6-16z" class="D"></path><defs><linearGradient id="AT" x1="497.101" y1="598.308" x2="501.399" y2="629.192" xlink:href="#B"><stop offset="0" stop-color="#3d0001"></stop><stop offset="1" stop-color="#1d0808"></stop></linearGradient></defs><path fill="url(#AT)" d="M503 600c1-1 2-1 3-2v-1h1l1 1-3 8c0 1 0 2-1 2-1 2-1 4-2 6-1 1-2 2-2 3-1 2-2 4-2 5-1 3-3 4-3 6-1 1-1 3-1 4l-1-1c-1 0-1 0-2 1v-3l12-29z"></path><path d="M506 529l1-1v-1c1 0 1 0 2 1 2 3 3 5 3 9v4c1 3 1 5 2 7l1 17c-1 9-3 15-6 24v-3l-1-1v1-3c1-2 1-4 1-6v-3c1-1 1-2 1-3v-1-3l-1 1v2l-1 1v4c-1 1-1 2-1 2-1-2-1-3-1-5h1 0c0-3-1-6-2-9h0l-1-1c-2-1-2-1-4-1-1-2-1-3-1-5h0v-3-1c0-2 2-5 3-7 0-2 1-4 2-6v-5c1-2 1-3 2-5z" class="G"></path><path d="M504 539c1-1 2-3 3-3 0 1 0 3-1 4h1c1 0 1 0 1-1 1 1 1 2 1 3l-1 2c0 2 0 3 1 5-1 2-2 5-3 8 0 2 0 4-1 6h0l-1-1c-2-1-2-1-4-1-1-2-1-3-1-5h0v-3-1c0-2 2-5 3-7 0-2 1-4 2-6z" class="P"></path><path d="M506 540h1c1 0 1 0 1-1 1 1 1 2 1 3l-1 2c0 2 0 3 1 5-1 2-2 5-3 8h0c-1 1-1 1-2 1 0-1-1-7 0-9 0-1 1-1 1-2v-2c0-1 1-2 1-3v-2z" class="B"></path><path d="M431 268c-1-2 0-6 1-8l1-1v9l1 6c0 2 1 5 1 7 1 2 1 2 1 4v2l1 6v8l3-2c1 1 0 1 2 2 0 1 0 2 1 3l3 13c1 2 1 4 2 5 4 5 4 11 9 16l1 2c0 2-1 4 0 6 0 1 0 3 1 4v4c1 2 2 4 3 7v4c-1 2-1 3-1 5 1-1 1 0 1 0 1 1 1 2 1 4v2c1 0 1 0 2 1v3l2 10c0 1 0 2 2 3h0c0 2 0 4 1 7l3 12c0 1 1 3 1 5 3 1 2 4 4 5 1 2 1 3 2 5l-1 2c0-1-1-3-1-4h-1v1l1 2v4 3c-1 1 0 1 0 2l-1 1v6c-1 6-2 10-3 16l-1 3-1 3h0c-1 1-1 2-1 3-1 3-1 6-1 10 0 1 0 6-1 7v6c0 2-1 3-1 5l-1 8c-1 3-1 7 0 11l-1 12-2-7-8-27-2-8-1-2-2-6-1-3-1-5 1-1c1-2 1-2 0-3 0-2 1-5 2-7-1-1-1-1-2-3 1-3 0-5 0-8 0-1 0-1 1-2v-5c-1-2-1-4-1-5-1-3-1-4-2-6v-1c0-2 0-2-1-4h-1l-1-2h0c-1-2-1-6-1-9-1 2-2 3-2 4l-1-4h0c-1-2-1-3-2-5h0l-1-5h-1v3l-1 1c-1-1-1-3-1-5v-2c-1-1-1-3-1-5l-1-12c-1-6 0-11 0-17 0-2-1-5 0-7v-1-6-1c0-1 0-6 1-7l1 1v1h5 0c1 1 2 1 2 2v1l3 6c0 1 0 1 1 2 0-3 0-5 1-7v-3l-1-5c0-1-1-3-1-4-1-3-2-7-5-9-2-1-4-2-6-2-2 1-4 2-5 4l-1 1c-1 1-2 3-3 4-1 3-2 7-3 10 0 1 0 2-1 3v10l-1-2c-1-1-2-2-2-3s-1-2-1-2c-1-1-2-2-2-3v-1-3-2c1-1 1-2 1-3v-4c1 0 1-1 1-2v-5l2-5 1-3v-1c1-2 1-1 1-2v-3c1-1 1-2 1-3 1-1 0-3 0-4l1-1v-6c-1-1-1-1-1-2v-1h0l-1-2h0c0-2-1-3-1-5v-1c-1-1-1-2-1-3l2-1 1-4c1-2 4-3 4-6 0-4 2-7 3-10l2 1z" class="P"></path><path d="M433 315l1 1c0 1 0 3-1 5h0c1-1 2-3 2-5h0v-1c1 0 1-1 1-1l1-1v1h0l-2 7v1c2 0 2 2 4 3 1 2 3 3 4 5l2 2c0 2 0 4 1 7 0 0 1 1 1 2v1c0 1 1 2 1 2v1 1l-1 2-1-5c0-1-1-3-1-4-1-3-2-7-5-9-2-1-4-2-6-2-2 1-4 2-5 4l-1 1c-1 1-2 3-3 4-1 3-2 7-3 10h-1v-1-1c0-1 1-2 1-2v-1-1c1-2 0-5 2-7 0-1 0-1 1-2v-1l1-1c0-3 2-4 3-5 0-2 1-2 2-3v-2h1v-2c1-1 0-2 1-3z" class="Y"></path><path d="M433 286c0 4 0 7 1 10 0 3 0 9-1 12v1c0 2-1 3 0 4l1-1v-2-2h1c0 2 0 3-1 4v1l-1 2c-1 1 0 2-1 3v2h-1v2c-1 1-2 1-2 3-1 1-3 2-3 5l-1 1v1c-1 1-1 1-1 2-2 2-1 5-2 7v1 1s-1 1-1 2v1 1h1c0 1 0 2-1 3v10l-1-2c-1-1-2-2-2-3s-1-2-1-2c-1-1-2-2-2-3v-1-3-2c1-1 1-2 1-3v-4c1 0 1-1 1-2v-5l2-5v3h0l2-2c3-2 3-7 4-10 1 1 1 2 2 3l1-1c1-1 1-2 2-3v-1c0-1 0-2 1-3 0-2 1-3 1-5v-10c1-3 1-6 1-10z" class="G"></path><path d="M419 325v3h0l2-2c3-2 3-7 4-10 1 1 1 2 2 3 0 2 0 2-1 3v2c0 1-2 2-2 3-1 1-1 3-2 5-1 1-1 3-2 5-1 1-3 2-3 4-1 2 0 4 0 6-1 2-1 4 0 6-1-1-2-2-2-3v-1-3-2c1-1 1-2 1-3v-4c1 0 1-1 1-2v-5l2-5z" class="B"></path><path d="M437 301l3-2c1 1 0 1 2 2 0 1 0 2 1 3l3 13c1 2 1 4 2 5v4c1 1 1 3 2 5s2 5 2 8c3 9 4 17 4 26l-3 6v-1l-1-13c0-4 0-7-2-11-1-4-2-9-4-13-1-3-2-5-3-8-1-2-2-5-3-8-1-2-1-3-1-5-1-2-1-4-1-6s0-3-1-5z" class="D"></path><path d="M437 301l3-2c1 1 0 1 2 2 0 1 0 2 1 3l3 13c1 2 1 4 2 5v4c1 1 1 3 2 5-3-2-2-4-4-6-1-1-2-3-2-5s-1-4-2-5c-1 0-1 1-1 2s1 1 1 2v1c1 1 1 2 1 4v1c-1-2-2-5-3-8-1-2-1-3-1-5-1-2-1-4-1-6s0-3-1-5z" class="C"></path><path d="M440 317v-4c-1-2 0-5 0-6l1-1 1 1c0 1 0 3 1 4 0 3 1 7 3 10l2 5c1 1 1 3 2 5-3-2-2-4-4-6-1-1-2-3-2-5s-1-4-2-5c-1 0-1 1-1 2s1 1 1 2v1c1 1 1 2 1 4v1c-1-2-2-5-3-8z" class="H"></path><path d="M433 353v-1c0-1 0-6 1-7l1 1v1h5 0c1 1 2 1 2 2v1l3 6c0 1 0 1 1 2 0-3 0-5 1-7 1 1 1 2 1 3 1 2 0 3 1 4v5 2c1 2 1 8 0 10 0 2-1 5 0 6 0 0 1 0 1 1v-3c1-2 0-3 1-5 1-6 0-11 0-17l-1-1v-10c2 4 2 7 2 11l1 13v1l3-6c0 3 0 8-1 11h1c-1 1-1 2-1 3v3c-1 4-2 8-2 12v2c0 1-1 1-1 2v1 1c-1-1-2-1-2-1-1-2-1-6-2-8v-1l-2-7c0-1 0-2-1-3l-1-2v1l-1 1v-5-1c-1-1-2-2-1-4v-4c0-1 0-1-1-2v-6c0-1 0-3-1-4h-2l-1-1v-1c-1 2 0 2-1 4 0-1 0-1-1-1-1-1-1-1-1-2h-1z" class="U"></path><path d="M450 382l-1 1v-1l-1-1c-2-6-1-13-3-19 0-3-3-8-3-12l3 6c0 1 0 1 1 2 0-3 0-5 1-7 1 1 1 2 1 3 1 2 0 3 1 4v5 2c1 2 1 8 0 10 0 2-1 5 0 6 0 0 1 0 1 1z" class="I"></path><path d="M448 322c4 5 4 11 9 16l1 2c0 2-1 4 0 6 0 1 0 3 1 4v4c1 2 2 4 3 7v4c-1 2-1 3-1 5 0 8 1 16 0 24-1 2 0 4-1 5v2h1v5l-2 8h-1v-9l-3 2-1 1v-1c0-1 0-1-1-2v-3c-1-1-1-2-1-3v-1c0-1 1-1 1-2v-2c0-4 1-8 2-12v-3c0-1 0-2 1-3h-1c1-3 1-8 1-11 0-9-1-17-4-26 0-3-1-6-2-8s-1-4-2-5v-4z" class="B"></path><path d="M456 376l1 1c0 2-1 7 0 9 1 1 1 3 1 5l-1 1v8c1 2 1 3 1 5l-3 2-1 1v-1c0-1 0-1-1-2v-3c-1-1-1-2-1-3v-1c0-1 1-1 1-2v-2c0-4 1-8 2-12v-3c0-1 0-2 1-3z" class="P"></path><path d="M429 267l2 1c2 7 2 12 2 18 0 4 0 7-1 10v10c0 2-1 3-1 5-1 1-1 2-1 3v1c-1 1-1 2-2 3l-1 1c-1-1-1-2-2-3-1 3-1 8-4 10l-2 2h0v-3l1-3v-1c1-2 1-1 1-2v-3c1-1 1-2 1-3 1-1 0-3 0-4l1-1v-6c-1-1-1-1-1-2v-1h0l-1-2h0c0-2-1-3-1-5v-1c-1-1-1-2-1-3l2-1 1-4c1-2 4-3 4-6 0-4 2-7 3-10z" class="D"></path><path d="M429 285c0 4 1 6-2 9v1-7l2-3z" class="F"></path><path d="M426 285c0-3 2-5 1-8h2c1 2 0 5 0 8l-2 3c0-2 0-2-1-3z" class="e"></path><path d="M426 302h0c1 2 1 4 1 6h0v5l1 1c1 0 1 0 1-1l2-2c-1 1-1 2-1 3v1c-1 1-1 2-2 3l-1 1c-1-1-1-2-2-3 2-4 1-10 1-14z" class="U"></path><path d="M420 291l1 1 2-2v-3c1-1 2-1 3-2 1 1 1 1 1 3v7c-1 2-1 5-1 7h0v-6l-1-1c-1 1 0 4-1 6 0-1 0 0-1-1l-1-1h0l-1-2h0c0-2-1-3-1-5v-1z" class="C"></path><path d="M420 291l1 1 2-2v-3c1-1 2-1 3-2 0 2 0 3-1 5h0c-1 2-2 3-3 4-1-1-2-1-2-2v-1z" class="H"></path><path d="M422 299l1 1c1 1 1 0 1 1 1-2 0-5 1-6l1 1v6c0 4 1 10-1 14-1 3-1 8-4 10l-2 2h0v-3l1-3v-1c1-2 1-1 1-2v-3c1-1 1-2 1-3 1-1 0-3 0-4l1-1v-6c-1-1-1-1-1-2v-1zm11 54h1c0 1 0 1 1 2 1 0 1 0 1 1 1-2 0-2 1-4v1l1 1h2c1 1 1 3 1 4v6c1 1 1 1 1 2v4c-1 2 0 3 1 4v1 5l1-1v-1l1 2c1 1 1 2 1 3l2 7-2 2v9 5 2 9c0 2 1 6 1 8h-1l-1-2h0c-1-2-1-6-1-9-1 2-2 3-2 4l-1-4h0c-1-2-1-3-2-5h0l-1-5h-1v3l-1 1c-1-1-1-3-1-5v-2c-1-1-1-3-1-5l-1-12c-1-6 0-11 0-17 0-2-1-5 0-7v-1-6z" class="G"></path><path d="M441 409c1 1 1 2 3 3v2c-1 2-2 3-2 4l-1-4v-5z" class="P"></path><path d="M441 409c-1-3 0-6 0-9 2 4 3 8 3 12-2-1-2-2-3-3z" class="Y"></path><path d="M445 380c1 1 1 2 1 3l2 7-2 2v9 5c-2-3-1-6-1-9-1-3-2-8-1-11l1-2-1-1h-1c1-2 1-2 2-3z" class="O"></path><path d="M433 353h1c0 1 0 1 1 2 1 0 1 0 1 1 1-2 0-2 1-4v1l1 1h2c1 1 1 3 1 4l-3-1 1 2v3c1 1 1 2 1 3h1c0 1 0 2-1 4 0 5 1 12-1 17v2l-1-1c-1 2-1 4-1 7-1 2 1 8-1 9l1 1v3l-1 1c-1-1-1-3-1-5v-2c-1-1-1-3-1-5l-1-12c-1-6 0-11 0-17 0-2-1-5 0-7v-1-6z" class="P"></path><path d="M433 384c0-1 0-3 1-4 0-1 1-1 1-1 1-1 0-6 1-8v16c0 1-1 4 0 5 0 1 1 1 1 2-1 2 1 8-1 9l1 1v3l-1 1c-1-1-1-3-1-5v-2c-1-1-1-3-1-5l-1-12z" class="I"></path><path d="M433 353h1c0 1 0 1 1 2 1 0 1 0 1 1 1-2 0-2 1-4v1l1 1h2c1 1 1 3 1 4l-3-1v-1h-1v2c-1 4-1 9-1 13-1 2 0 7-1 8 0 0-1 0-1 1-1 1-1 3-1 4-1-6 0-11 0-17 0-2-1-5 0-7v-1-6z" class="B"></path><path d="M461 370c1-1 1 0 1 0 1 1 1 2 1 4v2c1 0 1 0 2 1v3l2 10c0 1 0 2 2 3h0c0 2 0 4 1 7l3 12c0 1 1 3 1 5 3 1 2 4 4 5 1 2 1 3 2 5l-1 2c0-1-1-3-1-4h-1v1l1 2v4 3c-1 1 0 1 0 2l-1 1v6c-1 6-2 10-3 16l-1 3-1 3h0c-1 1-1 2-1 3-1 3-1 6-1 10 0 1 0 6-1 7v6c0 2-1 3-1 5l-1 8c-1 3-1 7 0 11l-1 12-2-7-8-27-2-8-1-2-2-6-1-3-1-5 1-1c1-2 1-2 0-3 0-2 1-5 2-7-1-1-1-1-2-3 1-3 0-5 0-8 0-1 0-1 1-2v-5c-1-2-1-4-1-5-1-3-1-4-2-6v-1c0-2 0-2-1-4 0-2-1-6-1-8v-9-2-5-9l2-2v1c1 2 1 6 2 8 0 0 1 0 2 1v-1c0 1 0 2 1 3v3c1 1 1 1 1 2v1l1-1 3-2v9h1l2-8v-5h-1v-2c1-1 0-3 1-5 1-8 0-16 0-24z" class="F"></path><path d="M470 439c1-1 1 0 2-1 0 0 1 0 1-1v1c1 1 1 1 1 2 0 9-1 17-2 26-1 1-1 2-1 3-1 3-1 6-1 10 0 1 0 6-1 7 0 1-1 2-1 3-1 1 0 1-1 3h-1v-2-1c0-1-1-2-1-2v-1c-1-1 0-2-1-3-1-3 0-6 0-8-1-2-1-5-1-7h0c0-2-1-4-1-6l-1-1-1-4c-1-1-1-3-1-5 0-1-1-1-1-2s0-1 1-2l2 1 2-2h0c2-2 3-4 4-6v-1l3-1z" class="D"></path><path d="M470 439c1 1 0 4 0 6l-1 3c-1 1-1 3 0 4l-1 2h-1l-1 3h0-1v1-1c0-3-3-5-3-7 0-1 1-2 1-3h0c2-2 3-4 4-6v-1l3-1z" class="Q"></path><path d="M448 390v1c1 2 1 6 2 8 0 0 1 0 2 1v-1c0 1 0 2 1 3v3c1 1 1 1 1 2v1l1-1 3-2v9h1v6c-1 8-1 15-2 22-1 9-4 17-7 24 0-2 1-5 2-7-1-1-1-1-2-3 1-3 0-5 0-8 0-1 0-1 1-2v-5c-1-2-1-4-1-5-1-3-1-4-2-6v-1c0-2 0-2-1-4 0-2-1-6-1-8v-9-2-5-9l2-2z" class="Y"></path><path d="M446 401c1 1 2 1 2 1 1 6 3 12 5 18l-2-2c-1-1-2-5-2-7-1-2-1-2-3-3v-2-5z" class="B"></path><path d="M446 408c2 1 2 1 3 3 0 2 1 6 2 7l2 2 1 6-2-1c-1 0-2 1-2 2v2 7c-1-3-1-4-2-6v-1c0-2 0-2-1-4 0-2-1-6-1-8v-9z" class="H"></path><path d="M446 417l2 3v2c0 1 0 0 1 1 0 2 0 4 1 6v7c-1-3-1-4-2-6v-1c0-2 0-2-1-4 0-2-1-6-1-8z" class="B"></path><path d="M448 390v1c1 2 1 6 2 8 0 0 1 0 2 1v-1c0 1 0 2 1 3v3c1 1 1 1 1 2v1c1 3 2 11 1 14v1c0-1 0-1-1-2 0-2-1-4-1-6l-3-8c0-2-1-4-1-6l-1 1s-1 0-2-1v-9l2-2z" class="G"></path><path d="M454 408l1-1 3-2v9h1v6c-1 8-1 15-2 22h0c-3-4 0-10-2-14v-5-1c1-3 0-11-1-14z" class="S"></path><path d="M450 429v-2c0-1 1-2 2-2l2 1c2 10 1 23-2 33-1-1-1-1-2-3 1-3 0-5 0-8 0-1 0-1 1-2v-5c-1-2-1-4-1-5v-7z" class="F"></path><path d="M461 370c1-1 1 0 1 0 1 1 1 2 1 4v2c1 0 1 0 2 1v3l2 10c0 1 0 2 2 3h0c0 2 0 4 1 7l3 12c0 1 1 3 1 5 3 1 2 4 4 5 1 2 1 3 2 5l-1 2c0-1-1-3-1-4h-1v1l1 2v4 3c-1 1 0 1 0 2l-1 1v6c-1 6-2 10-3 16l-1 3-1 3h0c1-9 2-17 2-26 0-1 0-1-1-2v-1c0 1-1 1-1 1-1 1-1 0-2 1l-3 1v1h-2v-1l-1-2c0-1-1-4-1-5-1 0-1-1-1-1-1-2 1-5 0-8l-1 1v1h-1c-1-2-1-4-1-6v-6l2-8v-5h-1v-2c1-1 0-3 1-5 1-8 0-16 0-24z" class="O"></path><path d="M474 418v1l3 25c-1 6-2 10-3 16l-1 3-1 3h0c1-9 2-17 2-26l1-4-1-18z" class="T"></path><path d="M468 423c1-2 1-4 2-5 1-2 1-8 0-10v-2h0l1 2 1 2v2 1 1l1 1v3h1l1 18-1 4c0-1 0-1-1-2v-1c0 1-1 1-1 1-1 1-1 0-2 1l-3 1v1h-2v-1c1-3 1-4 1-7 1-1 1-3 2-5v-5z" class="I"></path><path d="M469 433v-1c1-3 1-5 1-8l1-1c1 3 0 10 2 12l2 1-1 4c0-1 0-1-1-2v-1c0 1-1 1-1 1-1 1-1 0-2 1l-3 1v1h-2v-1c1-3 1-4 1-7 1-1 1-3 2-5 0 2 0 3 1 5z" class="F"></path><path d="M466 433c1-1 1-3 2-5 0 2 0 3 1 5l-2 7v1h-2v-1c1-3 1-4 1-7z" class="C"></path><defs><linearGradient id="AU" x1="456.548" y1="415.617" x2="471.286" y2="410.935" xlink:href="#B"><stop offset="0" stop-color="#650000"></stop><stop offset="1" stop-color="#9e0c10"></stop></linearGradient></defs><path fill="url(#AU)" d="M461 370c1-1 1 0 1 0 1 1 1 2 1 4v2c1 0 1 0 2 1v3l2 10c0 6 0 13 1 20 0 1-1 3 0 5 0 1 0 0-1 1 0 1 0 3 1 4v3 5c-1 2-1 4-2 5 0 3 0 4-1 7l-1-2c0-1-1-4-1-5-1 0-1-1-1-1-1-2 1-5 0-8l-1 1v1h-1c-1-2-1-4-1-6v-6l2-8v-5h-1v-2c1-1 0-3 1-5 1-8 0-16 0-24z"></path><path d="M466 433v-3c1-3 1-8 0-10v-3c-1-2-1-2-1-3l2 1v1c0 1 0 3 1 4v3 5c-1 2-1 4-2 5z" class="B"></path><path d="M461 370c1-1 1 0 1 0 1 1 1 2 1 4v2c1 0 1 0 2 1v3l2 10c0 6 0 13 1 20 0 1-1 3 0 5 0 1 0 0-1 1v-1c-1-2 0-3-1-4v-7c-1-4 0-12-2-15 0 0 0 1-1 2s0 3-1 4v3 2h0l-1 1h-1v-2c1-1 0-3 1-5 1-8 0-16 0-24z" class="S"></path></svg>