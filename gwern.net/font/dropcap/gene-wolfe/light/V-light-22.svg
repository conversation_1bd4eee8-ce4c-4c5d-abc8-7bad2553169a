<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="132 116 794 848"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#292929}.C{fill:#0f0f0f}.D{fill:#1c1c1c}.E{fill:#979696}.F{fill:#2c2b2b}.G{fill:#dddcdc}.H{fill:#272727}.I{fill:#d5d3d4}.J{fill:#636262}.K{fill:#333233}.L{fill:#c8c6c7}.M{fill:#b0afaf}.N{fill:#f5f5f5}.O{fill:#474647}.P{fill:#b2b1b1}.Q{fill:#858484}.R{fill:#7a7879}</style><defs><linearGradient id="A" x1="586.575" y1="199.128" x2="589.305" y2="193.134" xlink:href="#B"><stop offset="0" stop-color="#656565"></stop><stop offset="1" stop-color="#7c7a7b"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M588 194l8 1h0c1 0 3 0 5 1l-18 3v-2c-1 0-2-1-2-1 2-1 5-1 7-2z"></path><defs><linearGradient id="C" x1="573.007" y1="206.885" x2="575.122" y2="195.416" xlink:href="#B"><stop offset="0" stop-color="#3a3c40"></stop><stop offset="1" stop-color="#696665"></stop></linearGradient></defs><path fill="url(#C)" d="M546 233h-1v-1c1-4 3-8 5-11l1-1-1-1c1 0 1-1 2-2l1-2 3-3c5-6 14-13 21-16h4s1 1 2 1v2c-15 5-27 14-34 28l-3 6z"></path><path d="M566 724c1 4-1 7-2 11s-2 8-4 13c-1 4-3 7-4 11-2 4-4 8-5 12l-9 23-8 18v2l-1-3c-1-4 29-77 33-87z" class="Q"></path><path d="M512 603l10 39c4 20 8 42 10 63h-2c0-5 0-11-1-16h-2c-1-3-1-7-2-10-1-8-2-17-5-25l-1-5-5-23c1-1 0-6 0-8 0-3-1-7-3-10l1-5z" class="L"></path><path d="M514 618c1 4 1 7 2 10 1 4 2 8 4 11v8l-1 2-5-23c1-1 0-6 0-8z" class="I"></path><defs><linearGradient id="D" x1="658.498" y1="207.499" x2="635.994" y2="232.259" xlink:href="#B"><stop offset="0" stop-color="#6a6968"></stop><stop offset="1" stop-color="#858485"></stop></linearGradient></defs><path fill="url(#D)" d="M577 196c23-10 50-7 72 2 3 1 9 3 11 5 0 1 0 1 1 1 2 2 3 3 5 4v1c2 2 4 1 5 4l5 4c2 3 5 6 8 9 4 6 9 13 12 19v1c0 1 0 2 1 3v2h-1v3c1 1 1 1 1 2l3 6c0 1 0 3 1 5v7c1 2 0 6 0 9-1-1-2-1-3-2l-5-20-4-9c-2-3-3-6-4-8-4-7-9-14-15-20-15-15-34-25-55-28h-14c-2-1-4-1-5-1h0l-8-1c-2 1-5 1-7 2h-4z"></path><path d="M653 205c3 1 5 3 7 4 3 2 10 5 12 9h0c1 2 3 3 4 5 5 6 9 13 13 19 1 1 1 3 1 4h0c-2-4-5-6-6-10-1-2-3-4-4-5s-2-3-3-3c-1-2-2-3-3-4v-1c-3-1-5-4-7-6l-1-1c-1-2 0-2-2-3v1l-1-1v-1c-3-1-6-4-9-6h-1v-1z" class="R"></path><path d="M631 196h4l1 1h3c1 1 2 1 3 1h1c7 2 13 7 19 9 3 2 6 5 9 6l5 4c2 3 5 6 8 9 4 6 9 13 12 19v1c0 1 0 2 1 3v2h-1c-1-3-2-6-5-8h0v-2c-3-3-5-7-7-10s-4-6-7-8c-1-2-2-4-5-5h0c-2-4-9-7-12-9-2-1-4-3-7-4-7-4-14-6-22-9z" class="E"></path><path d="M672 218c3 1 4 3 5 5 3 2 5 5 7 8s4 7 7 10v2h0c3 2 4 5 5 8v3c1 1 1 1 1 2l3 6c0 1 0 3 1 5v7c1 2 0 6 0 9-1-1-2-1-3-2l-5-20-4-9c2 0 3 1 4 2 0-2-1-3-1-5l-2-3c0-1 0-3-1-4-4-6-8-13-13-19-1-2-3-3-4-5z" class="Q"></path><path d="M697 256l3 6c0 1 0 3 1 5v7l-2-1v-8-1l-1-1v-2c0-2-1-3-1-5z" class="E"></path><path d="M692 249c1 1 1 2 2 4 0 2 4 9 4 12l-1-2v1 1c-1-2-1-3-3-4h0-1l-4-9c2 0 3 1 4 2 0-2-1-3-1-5z" class="R"></path><defs><linearGradient id="E" x1="626.61" y1="191.223" x2="626.033" y2="200.081" xlink:href="#B"><stop offset="0" stop-color="#8d8c8c"></stop><stop offset="1" stop-color="#a8a7a8"></stop></linearGradient></defs><path fill="url(#E)" d="M577 196c23-10 50-7 72 2 3 1 9 3 11 5 0 1 0 1 1 1 2 2 3 3 5 4v1c2 2 4 1 5 4-3-1-6-4-9-6-6-2-12-7-19-9h-1c-1 0-2 0-3-1h-3l-1-1h-4c-4-1-8-2-13-3-6-1-13 0-20 0-3 0-6 0-10 1-2 1-5 1-7 2h-4z"></path><path d="M428 193c4-2 9-4 13-5 13-4 26-5 39-2 9 2 15 6 22 12 1 1 0 1 1 1l1 1-1 1c-19-9-40-11-60-3-16 7-28 21-35 38-6 13-8 30-7 45v-1c-1-2-1-4 0-6-3-2-4-7-4-10l-1-3c-1-2 0-4-1-6v-1h1c1-2 0-4 1-6 0-2 0-4 1-6h0v-1c0-1 0-2 1-3h-1c1-10 7-19 11-27h0c1-3 4-4 5-7h1c3-3 5-6 9-8h0l5-2-1-1z" class="E"></path><path d="M424 196h1c1 0 1-1 2-1h1l2-1 1-1h3l1-1s1 0 2-1c0 0 1-1 2-1h3c1-1 1 0 2 0-2 2-5 3-8 4s-8 3-11 6c-4 3-8 6-12 10-7 7-10 17-12 26l-1-1v6c-1 2-2 5-3 7 0-2 0-4 1-6h0v-1c0-1 0-2 1-3h-1c1-10 7-19 11-27h0c1-3 4-4 5-7h1c3-3 5-6 9-8z" class="M"></path><defs><linearGradient id="F" x1="600.86" y1="631.189" x2="625.238" y2="640.084" xlink:href="#B"><stop offset="0" stop-color="#b7b6b6"></stop><stop offset="1" stop-color="#e9e9e8"></stop></linearGradient></defs><path fill="url(#F)" d="M649 534l1 1c-1 1-3 5-3 6v3h1 0l1-5c1-1 1-2 2-3 0 3-3 9-4 12v1c2 2 3 4 5 4-3 9-7 18-10 27-1 5-3 11-2 17 0 1 1 5 0 5-6 3-10 8-13 13-4 6-8 12-11 19l-17 37-19 42-5 9c0 1-1 3-1 3h-1 0l-1 1v-1c-2 4-4 8-6 13l-4 8c-1 4-3 10-6 13 1-4 3-7 4-11 2-5 3-9 4-13s3-7 2-11c1-4 4-9 6-13l14-33 43-96 20-48z"></path><path fill="#020202" d="M138 175c6-1 14 0 20 0h44 158 66 18c4 0 8-1 11 0 15 0 31 4 42 15 3 2 5 6 6 9-1 0 0 0-1-1-7-6-13-10-22-12-13-3-26-2-39 2-4 1-9 3-13 5l1 1-5 2h0c-4 2-6 5-9 8h-1c-1 3-4 4-5 7h0c-4 8-10 17-11 27h1c-1 1-1 2-1 3v1h0c-1 2-1 4-1 6-1 2 0 4-1 6h-1v1c1 2 0 4 1 6l1 3c0 3 1 8 4 10-1 2-1 4 0 6v1 1c2 13 3 26 6 39 6 25 15 49 25 72l40 100c4 12 10 23 14 35l19 51c2 8 6 16 7 24l-1 5c2 3 3 7 3 10 0 2 1 7 0 8l5 23 1 5c3 8 4 17 5 25 1 3 1 7 2 10h2c1 5 1 11 1 16h2c2 11 2 86-4 95v5l-6 44c-1 6-3 12-3 18 0-1 1-2 1-3l13-53 1 3v-2l8-18 9-23c1-4 3-8 5-12 3-3 5-9 6-13l4-8c2-5 4-9 6-13v1l1-1h0 1s1-2 1-3l5-9 19-42 17-37c3-7 7-13 11-19 3-5 7-10 13-13 1 0 0-4 0-5-1-6 1-12 2-17 3-9 7-18 10-27-2 0-3-2-5-4v-1c1-3 4-9 4-12-1 1-1 2-2 3l-1 5h0-1v-3c0-1 2-5 3-6l-1-1c1-4 3-8 4-12l6-12c9-27 19-54 27-82l7-28c1-6 2-13 4-20h0c5-33 7-66 1-99 1 1 2 1 3 2 0-3 1-7 0-9v-7c-1-2-1-4-1-5l-3-6c0-1 0-1-1-2v-3h1v-2c-1-1-1-2-1-3v-1c-3-6-8-13-12-19-3-3-6-6-8-9l-5-4c-1-3-3-2-5-4v-1c-2-1-3-2-5-4-1 0-1 0-1-1-2-2-8-4-11-5-22-9-49-12-72-2-7 3-16 10-21 16l-3 3-1 2c-1 1-1 2-2 2l1 1-1 1c-2 3-4 7-5 11v1h1c-2 7-4 13-5 20-3 18 2 37 13 52 5 7 10 11 18 15 11 5 22 6 35 5 6 0 12 0 18-2h1c8-3 14-11 17-18 8-15 9-30 3-46 3 3 6 11 6 16 4 20-1 42-13 59s-29 28-49 31c-16 3-33 0-46-9-15-11-25-29-28-47l-1-11c-1-32 13-69 35-92 22-22 53-31 84-31 5-1 10 0 16 0h25 84 99 26c5 0 10-1 14 0 0 1 1 2 1 3 0 3 0 11-1 14-3 1-6 0-9 1-8 1-16 7-21 14-3 6-5 14-4 21 2 6 7 12 10 18 5 9 10 20 7 30l-1 1c-1 6-5 10-10 13-6 3-13 4-19 2-2-1-5-2-7-4 6 2 12 4 18 1 5-2 8-5 10-10 2-6 0-14-2-19-4-8-11-13-18-16-10-3-20-2-28 2a53.56 53.56 0 0 0-15 15c-6 10-10 21-14 32l-19 44-55 132-22 51-62 147-21 49c-3 9-6 18-10 26-4 12-10 24-15 35l-29 69-27 63c-5 11-9 23-15 34-1 3-2 4-4 5-2 0-3 1-5-1s-4-8-5-11l-11-25c-1-2-4-7-4-9l-67-161c-6-13-15-20-27-25-6-1-10-1-15 0l-2 1c-4 1-7 3-10 5-6 6-11 15-12 23s1 15 7 21c4 5 11 8 19 9 8 0 15-1 21-7s7-13 7-21l1 4v2c1 7 0 14-3 19-5 6-12 10-20 11-11 2-21-1-29-7-9-6-15-18-16-29-2-16 1-33 11-46 6-9 16-14 26-14 4 0 8 0 12-1 0-1 0-3 1-4 0-5 0-10-1-15h0c-2-9-6-18-10-26l-17-41-65-156-17-40c-2-5-5-11-6-16-4-8-7-18-11-26l-27-66c-5-12-10-25-16-35-5-10-15-18-26-20-8-2-17-1-24 3-7 5-12 13-14 22-1 5 0 11 3 16 2 4 6 7 11 8h0c6 1 12-1 18-3-7 4-14 7-23 5-6-1-12-5-15-11-3-4-4-8-4-13a34.53 34.53 0 0 1 5-18c4-9 10-16 13-26 0-5-1-9-4-15l-3-5c-7-9-16-11-27-12 0-4-1-14 1-17h2z"></path><path d="M745 250l1 1v1c1 1 1 0 1 2l-1 1s-1 0-1-1c-2 0-2-1-2-3l1-1h1z" class="C"></path><path d="M778 271h1 0c1 1 2 1 2 3v1l1 1-1 1h0l-1-1v-1l-1 1-3 3c-1-1-1 0-1-1 1 0 1 0 2-1 0-1-1-3-1-4h2l-1-1 1-1z" class="D"></path><path d="M745 250l-1-1c-2 0-4-1-5-3v-1c0-1-1-2-1-3l1-1 2 1h2 5 0-1c-1 1-1 1-3 1h-1v1 1l-1 2c1 1 1 1 2 1l1 2z" class="C"></path><path d="M735 254l1-2c1 0 1 0 2 1l1-1-2-2c1 0 1 1 2 1h1c1 1 1 1 2 3v2h-1c-1 1-1 1-2 1-2-1-3-2-4-3z" class="B"></path><path d="M746 256l4 2 1 1h0v1l2-2v1c0 1 0 3 1 4v1c1 1 1 2 2 2l-1 4h0c0-3 0-4-2-6-2-1-7-6-7-8zm0-54l3-1c1-1 0 0 2-1h1s0 1 1 1c1 1 1 1 2 0h1l1 1h1 0 0v1l-1 1v1l-1 1v-1l-2 1v-1h-2v-2l-2 1c-1-1-2-2-4-2z" class="C"></path><path d="M165 209l1-1c0-1-2-2-1-4 2 2 3 4 3 6h1c1 3 2 6 1 10h0-1v4c0-5-1-9-4-15z" class="D"></path><path d="M484 583l2 1v2c1 1 3 2 2 4-2-1-4-2-6-2-2 1-3 2-4 4h0-1c1-4 4-6 7-9z" class="N"></path><path d="M398 194c0 1 0 1-1 2-3 1-5 1-7 2-3 0-5 1-7 1-2-1-6-2-8-1h-1v-1l1-1c8 2 15 1 23-2z" class="E"></path><path d="M291 217l-1-2h0c-1-1-1 0 0-1-1-1-2-1-2-2h-1l-2 1c0-1 0-1 1-2 0 0 0-1 1-1v-1h2 1v-3c2 1 3 4 3 6 0 1 0 2-1 4v1c0 1 0 2-1 3v-2-1zm494 16v-1-2-1c1-2 3-3 5-4h1l1 1c-1 0-1 1-2 2l1 1c0 1-1 1-2 2h0c-1 0-2 1-2 1l-1 2h1v2 1c1 1 2 1 2 2-1 1-2 1-3 2 0-1 0-2 1-2h0v-1l-1-1v-1l-1-1h1v-1l-1-1z" class="C"></path><path d="M759 288l1-1c0-1 1-1 1-1 0-1 2-2 2-3 0 0-1 0 0-1l1-1c0-1-1-1-1-1l1-2h1c0 1 0 1-1 2v1c1 0 1 1 2 1v1 1h1 0c1 1 1 1 1 2l-3 3-1-2h-1 0-1v1l-2 2h-1v-2z" class="F"></path><path d="M295 254l1 1h1l1 1-1 1 1 1c1-1 0-1 2-1v-1-1l1-1-1-1v-2l1 1v2c1 1 1 2 1 2l-1 1c1 1 1 1 0 2s-2 0-3-1c-1 1-1 1-1 2 0-1 0-1 1-1l1 2h0c1 1 1 1 1 3h-1l2 1c-1 1-1 1-1 2-1 0-2-1-2-1h-2v-1c2 0 1 0 2-1h-3l-1-1h0c-1-1 1-2 2-3l-1-1-1-1c1 0 1 0 2-1 1 0 0 0 0-1h0-1c-1-1-1-1-1-2h1z" class="B"></path><path d="M459 625v-1c-4-6-5-14-6-21 0-3-1-6-1-8 1 3 1 6 2 9 2 4 3 8 5 12v5l1 3-1 1z" class="G"></path><path d="M281 218c0-1 2-2 2-3v-1s0-1 1-2c-1 0-2-1-3-1 0-1 0-1 1-1 0-2 1-2 2-3l1 1 1 1-1 1v2h-1l1 1-1 1h1l1-1c0 1 1 1 1 2l1 1 1 1h0 2v1 2h-1l1 1 1 1h-1l-1-1-2 2-2-1v-4h-5z" class="D"></path><path d="M753 288h0v1 1c1 0 1 2 1 3l1 1-1 1c-3-2-2-4-2-6-1-1 0-1 0-2 0-2-1-1 0-3l-1-1c1-3-1-5 0-8l2-2v-2l1 1h0v1c1 0 1 1 2 2l-1 1h-1 0 1 1 0c1-1 1-1 2-1v1c-1 1-1 0-2 1h0l-2 1v1l2 2h-1v2l-2 1v1c1 1 0 2 0 3z" class="C"></path><path d="M379 496c1 2 3 3 2 4v1c2 1 4 1 6 2-10-1-16 2-25 8l-4 2c1-1 3-2 4-3 6-5 12-8 17-14z" class="L"></path><path d="M295 240l1-1h0v-1c1 1 2 1 3 2l-1 1v1h0l-1 1v1l1 1s0 1 1 2h1l-1 1-1 3h0l1 1h1l-2 2h0v-1c-2 0-1 0-2 1v-2h0l-1 1-2-1v-1-3-2l1-1-2-1c1-1 2-2 3-4h0z" class="D"></path><path d="M380 717l-1-1c-1 1-2 1-2 1h-1c-2 0-3 0-4 1l-2 2-1-1c1-1 2-1 3-2 2-1 4-3 6-3h1c4-1 7-2 10-2l2 2c1 0 2-1 4 0 1 0 1 0 2 1l1 1h-1c-6-1-10-1-15 0l-2 1z" class="C"></path><path d="M416 488c-2-5-3-9-4-15 4 8 9 20 18 23 2 0 4 1 5 0 2-1 3-2 4-3-1 1-1 2-2 3l-3 2c-3 0-5-1-8-3l-4-3c-1 0-1-1-2-1-1-1-2-2-4-3z" class="M"></path><path d="M275 247c0-1 0-1-1-2 0 0 0-1-1-1h0c0 1-1 2-2 3s-2 1-3 1l-1-1-1-2v-2l1-1h-1c0-1 1-1 1-2v-1h0v-1l2-1h-1v-1l-1 1h-1v1l-1-1v-1c0-1 1-1 1-2l-1-1c0-1 0-1-1-2l1-1 1 1 1-1 1 1 1 1-1 1-1 1c1 0 1 0 2-1 0 1 1 1 1 2l1 1c1 0 1 1 2 2l-6 6h1v-1c1 0 1 0 2-1 1 1 0 2 2 1l1-1c1 1 1 2 2 3h0v2z" class="F"></path><path d="M295 271h2l1-2h-1l1-2c-2 0-2 0-3-1h1c2 0 2 1 3 1 1 1 2 0 3 0l1 1h-1v1l-2 2h0c1 0 1 0 2 1v1h1v-1h0l2-2c1 0 1 0 1-1v1 3c-1 1-1 2-2 2 0-1 0 0 1-1l-1-1-1 1h0v1c-1 0-2 0-2 1h-1 1v2l1 2c-2 0-2-1-3-1 0 1 0 1 1 2h-1l-1-1h-2 0l1-1-1-1v1c-1 0-1 0-2-1v-2-3l1-2z" class="B"></path><path d="M711 235l6 6c2 2 5 4 6 7 0 1 0 2 1 3h-3l2 2c-1 0-2 0-3-1h0l-11-11h0 1l1 1c0 1 4 5 6 5 0-2-4-4-5-6l-2-2c-1 0-2-1-2-2 1-1 2-1 3-2z" class="F"></path><path d="M717 241c2 2 5 4 6 7 0 1 0 2 1 3h-3c-1-2-5-6-5-8l2 1h0c-1-1-1-1-1-3z" class="O"></path><path d="M753 288c0-1 1-2 0-3v-1l2-1v2h1c1 0 1 1 2 1 0 1 0 1 1 2v2h1 0v1l2 1v-2h0 1v1h0l1 1v2c1 0 1 1 2 1h1l-1 1h-1c0 1-1 2-2 3h-1-1l-1 1-2-1 1-1h-1c-1 0-1-2-2-3 1-2 1-2 2-3h0-1v1l-1-1h-1v-1h0v-1c0-1 0-1-1-2h-1z" class="D"></path><path d="M711 235c-1 0-2-1-2-2h0l-5-5v1l-6-5-1-1c-1-1-1-2-2-3l-1 1c-1-2-2-3-4-4 0-1-1-2-2-3h1c1 0 1 1 1 2h1c0 1 0 1 1 1l1 1c1 1 1 0 2 1h2v1l2 2h0c2 0 3 2 4 3l1 1c2 2 4 5 7 6l1 1h1c2 3 6 8 9 9l1 1h-1l3 3c1 0 2 1 2 2h-1l-2-2-1-1-1 1h1c1 1 2 3 2 4l-2-2c-1-3-4-5-6-7l-6-6zm49 36c2 0 2 1 3 2-1 2 0 2-1 4l2 1 1-1c1 0 1-1 3 0l-1 1h1v2l-1 1-1 1c-1 0-1-1-2-1v-1c1-1 1-1 1-2h-1l-1 2s1 0 1 1l-1 1c-1 1 0 1 0 1 0 1-2 2-2 3 0 0-1 0-1 1l-1 1c-1-1-1-1-1-2-1 0-1-1-2-1h-1v-2-2h1l-2-2v-1l2-1 1 1c1-1 2-1 2-2v-1l-1-1h-1l1-1v-2h2z" class="H"></path><path d="M720 234l9 8c4 2 9 5 11 9h-1c-1 0-1-1-2-1l2 2-1 1c-1-1-1-1-2-1l-1 2-8-6c0-1-1-2-2-2l-3-3h1l-1-1 1-1c0-1-1-1-1-1l-2-2c2-1 2 1 3 1v-1c-2-1-2-1-3-3v-1z" class="O"></path><path d="M505 781c1 4 1 7 3 10 0 1 1 3 1 3l1-1c2-1 3-2 5-2 1 6 3 12 4 18v1 5h0c0-5-1-9-2-13l-1-4c-1-1-2-3-3-4l-2 2h-1c0 5 4 11 5 16 1 8 2 17 1 25 0 3 0 5-1 8l-1-26c-1-7-1-14-5-20-4-5-4-12-4-18z" class="N"></path><path d="M722 205l-1-1c-1-1-1-1-2-1v-1c1 1 2 1 3 1v-2l2-2c1 0 1 0 2 1l1-1c3 0 4-1 6 0h2 4v1c-1 0-2-1-3-1-1 1-2 1-3 2h0v-1c-2 0-2 1-3 2-1 0-2 0-2-1-1 1-2 2-2 3v1 2c-1 1-1 1-2 1h0c-1 1-1 2-2 2v1c1 0 1 0 2-1h2c0 1-1 1-1 2 1 0 1 0 2-1v1l-3 3h0c-2 0-2 0-4 1l-11-6h1 1l1 1h1 0 1 1v-3h0l1 1c1 0 1 0 2 1v2c0 1 0 1 1 2h0v1h1 0 1l-1-1v-1 1l-1-1v-1s0-1-1-1c1-1 2-2 2-3l-1-1h0c1-1 2-2 3-2z" class="C"></path><path d="M722 205l2-3c1 1 1 1 1 2-1 2-1 2-3 4l-3-1h0c1-1 2-2 3-2z" class="B"></path><path d="M737 228l2-2 1 1c0 1 0 1 1 2v1h2c1 0 2 1 3 1s3 2 4 3c2 0 2 0 4 1l-1 1h2-1v2 1 1h0l-1 2-1-1-1 1h1c0 1 0 1 1 2h0c1 0 2 1 3 2v1c-1 0-1 0-2-1v1c-2 0-3-1-5-1v2 1 1s-1 1-1 2c0 0 1 0 2-1v1l1 1c-1 0-1 0-1 2v3l-4-2v-1l1-1c0-2 0-1-1-2v-1l-1-1-1-2c-1 0-1 0-2-1l1-2v-1-1h1c2 0 2 0 3-1h1 1 1v-3h-1-1l-1-1c1 0 2-1 2-1v-1c-1-1-2 0-3 0v-1h1l-1-2-2 1-1-2c-1 0-1 0-2-1s-2-2-4-3zm24-20h2 2c1 2 1 2 2 3l2-2h0c1 0 1 0 2-1h1 0l1 1-2 1c0 1 1 1 1 2v1l1 1h-1v2h3l1 2-1 1h1 1 4l1 1-1 1h0v-1h-1-2v1c-1 1-3 0-4 1v1-2h0l-1-1h-1c0 1-1 1-1 2l-2-2h0l-1 1v1h-2v2h1v1h-2 0l-1-1v-3h-2v-1c1 0 0 0 1-1h1l-1-1h0l-1-1c-1 0-1 0-1-1 1 0 2 0 3-1-1-2-1-3-2-4h-1l1-2-1-1z" class="D"></path><path d="M460 561c0 1 1 2 2 4h1c4 2 9 4 13 7 2 2 5 3 7 6 3 3 6 7 9 11 4 5 8 10 10 17 1 0 0 0 1 1l-1 1c-3-7-8-13-14-18 1-2-1-3-2-4v-2l-2-1c0-2-1-3-2-4-2-2-4-4-7-5-10-7-20-9-32-9 5-1 13-1 17-4h0z" class="G"></path><path d="M688 210l-1-1v-1l-1-1h-1c0 1 1 1 1 2 0 2 1 1 1 3-1-1-2-1-2-3l-1-1c-1-1-1-1-1-2-2 0-2-1-3-1-1-1-1-1-2-1-1-1-2-1-2-1-2 0-2-1-3-2h2v-1l-3-1c0-1 0-2 1-3 3 0 7 2 9 3h2c0 1 1 2 2 2h1c1 1 2 1 3 2h-1-1c1 2 3 2 4 3-3 0-5-2-8-3v1c1 1 1 1 2 1 3 1 5 3 8 3h1c2 0 4 1 5 2 1 2 1 2 3 3l2 2-5-2c3 2 5 4 9 6 1 1 3 3 5 4h0-1c-1 0-3-2-4-3-2-1-4-2-5-3h-1l1 1c1 0 1 1 2 1l5 4c1 1 2 2 2 3l-8-6v-1l-5-3c-1-1-5-3-6-4-1-2-3-3-5-4h-1-1l1 2z" class="C"></path><path d="M476 650c2 3 7 5 9 8 3 2 5 5 7 8 1 2 2 6 4 7 1 1 3 2 5 3 3 5 5 10 8 15 1 1 2 2 3 2l1 1 1-1 6 20v2l-1 2c1 2 2 7 1 9l-1 1c-1-10-2-18-5-27-1-2-2-5-4-6-1-2-3-2-5-2h0l-1-1 1-2c1-3 0-6-2-8-4-4-8-4-13-4l3-6c-1-1-1-4-2-5-3-5-10-11-16-12v-1c0-1 1-2 1-3z" class="I"></path><path d="M723 248l2 2c3 3 6 6 9 10 5 5 8 11 9 18l-1 1v-3l-3-6c0-1 0-2-1-2h0c0 2 2 4 2 7 1 2 1 4 2 6 1 1 1 2 1 3h-1c-5-12-14-22-22-32h0c1 1 2 1 3 1l-2-2h3c-1-1-1-2-1-3z" class="J"></path><path d="M411 530h1v2c1 2 2 3 4 4 5 2 11 1 16 2l6 3c6 2 12 4 18 7 2 1 5 3 7 3v1c-4-1-7-3-11-5-7-3-14-5-21-6-11-1-21 3-29 9 4-7 9-10 9-19v-1z" class="G"></path><defs><linearGradient id="G" x1="603.144" y1="331.737" x2="598.209" y2="316.816" xlink:href="#B"><stop offset="0" stop-color="#0f0a0d"></stop><stop offset="1" stop-color="#373a39"></stop></linearGradient></defs><path fill="url(#G)" d="M625 323v1 1c-1 1-2 1-3 2-4 1-8 1-13 1-12 1-26 1-37-5-13-6-25-19-29-32 0-1-1-3-1-4 1 1 1 1 1 2 3 8 7 15 13 21 3 4 8 8 12 10l2 1c1 0 1 0 2-1 11 5 22 6 35 5 6 0 12 0 18-2z"></path><path d="M688 210l-1-2h1 1c2 1 4 2 5 4 1 1 5 3 6 4l5 3v1l8 6c1 2 7 7 7 8v1c1 2 1 2 3 3v1c-1 0-1-2-3-1l2 2s1 0 1 1l-1 1c-3-1-7-6-9-9h-1l-1-1c-3-1-5-4-7-6l-1-1c-1-1-2-3-4-3 1-1 1-1 1-2-1 0-2-1-3-1s-1-2-3-2v1c-1-1-1-2-1-2v-1c-2 0-2-1-2-1-2-1-2-1-3-2v-1h1l-1-1z" class="F"></path><path d="M713 233c0-1-1-2-2-3s-3-2-3-3h1l-1-1-2-2v-2s-1-1-1-2l8 6c1 2 7 7 7 8v1c1 2 1 2 3 3v1c-1 0-1-2-3-1l2 2s1 0 1 1l-1 1c-3-1-7-6-9-9z" class="B"></path><path d="M781 232l2-1v1c1 0 2 0 2 1l1 1v1h-1l1 1v1l1 1v1h0c-1 0-1 1-1 2v2l-1 1h1c1 0 1-1 3-1v1c0 1-1 1-1 3 0 1-1 1-1 2l1 1v1 1h1l-1 1c0 1-1 1-1 1v1h0c1 0 1 1 1 2h0v3c-1 1-2 2-2 3s0 0-1 1c0 2 2 4 1 5h0l-1-2h-1l-1 1h0c0-2 2-2 1-5v1h-1v-1l1-1c0-1 0-1-1-2v-1l1-1-1-1c-1-1-1-1-2-1h0c-1-1-1-2-2-3v-1h-1v-2h1c-1-2-1-2 0-3l1-1c-1 0-1 0-1-1v-1h-1v-1l2-2v-1h-2v-4c2 0 1 0 2 1h1l-1-1 2-3v-1h-1z" class="D"></path><path d="M780 246h0l2-2 1 1h0l-2 2 1 1 1-1v1l-1 1c1 1 2 0 3 2-1 0-1 0-1 1l1 2c0 1 0 1-1 2v2l-1-1c-1-1-1-1-2-1h0c-1-1-1-2-2-3v-1h-1v-2h1c-1-2-1-2 0-3l1-1z" class="B"></path><path d="M277 288l-1-1h-1-1-2l-1-1h0c1 0 1 0 2-1h1 0c1-1 2-2 2-3l-2-3c1 0 0 0 1-1l-1-1v-1c1 0 2-1 2-2h2v2l-1 1 2 1v1l1-2h-1l1-2h-1v-2c2 0 3 0 5-1 1-1 1-1 2-1l1 2c1 2 1 1 2 1s1-1 2-1l1 1c1 0 1 0 2-1v3c-1 2-2 3-4 4h0v1c-1 0-2 1-3 0h0c-1 0-1 0-2 1h0 1 2v1h1 2 0s-1 1-2 1c1 1 2 1 3 1s2-1 2-1h1c-1 1-1 2-3 2 1 1 1 1 1 2h0c-1 1-1 1-1 2v1l-2 2v-1l1-1v-1c0-2 1-2 1-3l-2 1c-1-1-1-1-1-2h0v-1c-1 0 0 0-1-1 0 0-1-1-1 0-1 0-1 0-2 1l-1 1c-1 0-1 1-2 1v-1l-1 1h0c-1 0-2 1-3 0h-1v1z" class="F"></path><path d="M476 650l2-4c8 8 18 15 24 25 0 1 1 1 1 1 4 5 8 15 9 21-1 0-2-1-3-2-3-5-5-10-8-15-2-1-4-2-5-3-2-1-3-5-4-7-2-3-4-6-7-8-2-3-7-5-9-8zm-16-89c0-2 1-2 2-3 7 1 12 12 19 11 4 4 7 8 10 13 4 5 7 10 10 16 1 3 2 6 2 9-1-1 0-1-1-1-2-7-6-12-10-17-3-4-6-8-9-11-2-3-5-4-7-6-4-3-9-5-13-7h-1c-1-2-2-3-2-4z" class="H"></path><path d="M556 759c3-3 5-9 6-13l4-8c2-5 4-9 6-13v1l1-1h0 1l-8 19-25 56c-4 10-9 20-12 31l-1 2h0c1-6 3-14 6-19v-2l8-18 9-23c1-4 3-8 5-12z" class="L"></path><path d="M411 530c-1-1-2-4-3-6h1l4 2c1 0 1 1 2 1v1h1c1 1 3 2 4 3h1l3 1h1c1 1 3 2 4 2l5 2c3 0 5 2 7 3s5 1 8 3l2-2c5 3 10 8 14 13-1 0-1 0-2-1v-1c-2 0-5-2-7-3-6-3-12-5-18-7l-6-3c-5-1-11 0-16-2-2-1-3-2-4-4v-2h-1z" class="D"></path><path d="M460 624l1 2c1 1 3 2 5 2v1h3 1 0c1 1 2 1 3 2h0c1 1 2 1 2 2l1 1h1c1 1 3 2 4 3h1c1 2 3 4 5 5 0 2 4 5 6 7 10 13 16 28 21 44l-1 1-1-1c-1-6-5-16-9-21-1-4-3-7-5-11-7-11-19-25-32-28-2-1-7-1-9 0-1 1-1 2-2 4v1h0c0-6 2-6 4-10 1-1 0-2 0-3l1-1z" class="N"></path><path d="M272 230h0c-1 1-2 0-3 0v-1c-2 0-2-1-3-1v-1h0l2 1h1v-1l1 1h1v-2h1l-1-1 1-2 1-1-1-1 2-1 1 1c0-1 1-2 2-2l1-1-1-1h1l1 1h0 1c0-2 1-2 2-3h1l-2 2v1h5v4l2 1c1 1 1 1 1 2v1l-1-1c-1 0-1 0-2 1h0l1 1-2 1c1 0 2 0 3-1 0 0 1 0 2-1l1 1c0 1 1 1 2 2h0v2l1 1c-1 1-2 2-1 3-1 0-1 1-2 1s-1-2-2 0v2h-1v-1-2l-1-1h-1l-1 2c1 0 1 0 1 1-1 0-2 1-3 1l1-2-1-2h-2-1c-1-1-2-1-2 0h-2l1-1-1-2h0c-1-1-1-1-2 0v-1h-2z" class="B"></path><defs><linearGradient id="H" x1="702.464" y1="214.891" x2="709.759" y2="208.357" xlink:href="#B"><stop offset="0" stop-color="#3e3c3c"></stop><stop offset="1" stop-color="#5f605f"></stop></linearGradient></defs><path fill="url(#H)" d="M684 199l25 11 11 6 6 3v1c2 1 4 1 6 2 1 2 3 4 5 4l-1 1h-1c-2-2-4-3-7-3h-6l-2 1c-3 0-4 0-6-2h0c-2-1-4-3-5-4-4-2-6-4-9-6l5 2-2-2c-2-1-2-1-3-3-1-1-3-2-5-2h-1c-3 0-5-2-8-3-1 0-1 0-2-1v-1c3 1 5 3 8 3-1-1-3-1-4-3h1 1c-1-1-2-1-3-2h-1c-1 0-2-1-2-2z"></path><path d="M700 210c7 2 12 6 17 10 2 1 4 3 6 3 1 1 3 1 5 1h-6l-2 1c-3 0-4 0-6-2h0c-2-1-4-3-5-4-4-2-6-4-9-6l5 2-2-2c-2-1-2-1-3-3z" class="B"></path><path d="M660 203c18 8 35 26 46 43 3 5 6 10 8 15h0l-1 1c-1-2-1-3-2-4v-1c-1-1-2-3-2-4l-2-2v-2h-1v-1l-1-1v-1l-1-1c-1-1-1 0-1-1-1-1-1-2-1-2l-1-2h-1c-1-2 1 0-1-2l-2-2h0c0 2 3 6 4 7s1 1 1 2c1 1 2 2 3 4s2 3 3 5c0 2 1 3 2 5 0 2 1 3 1 5l-2-2-2-3v2l1 2-1 1s-1 0-1-1c-1-1-1-3-2-4-1-3-2-5-4-8h0 0c-1-2-1-3-2-4l1-1c0 1 1 2 2 3h0c0-2 0-2-1-4s-2-3-3-4c-1 1-1 3-1 4-3-6-8-13-12-19-3-3-6-6-8-9l-5-4c-1-3-3-2-5-4v-1c-2-1-3-2-5-4-1 0-1 0-1-1z" class="L"></path><path d="M676 217c7 5 13 12 18 19 6 7 12 14 13 23v2l1 2-1 1s-1 0-1-1c-1-1-1-3-2-4-1-3-2-5-4-8h0 0c-1-2-1-3-2-4l1-1c0 1 1 2 2 3h0c0-2 0-2-1-4s-2-3-3-4c-1 1-1 3-1 4-3-6-8-13-12-19-3-3-6-6-8-9z" class="P"></path><defs><linearGradient id="I" x1="336.903" y1="375.944" x2="303.689" y2="347.541" xlink:href="#B"><stop offset="0" stop-color="#b6b5b5"></stop><stop offset="1" stop-color="#dfdedf"></stop></linearGradient></defs><path fill="url(#I)" d="M341 383c-5-4-12-5-18-9-11-8-14-18-16-31-1-6-1-11-2-17v-1h0l3 12c3 12 5 21 16 29 5 3 11 5 17 4h1v3c1 3 1 7 0 10h-1z"></path><defs><linearGradient id="J" x1="360.078" y1="420.789" x2="357.6" y2="437.002" xlink:href="#B"><stop offset="0" stop-color="#b3b1b0"></stop><stop offset="1" stop-color="#f1f1f2"></stop></linearGradient></defs><path fill="url(#J)" d="M369 420l3 6h0c6-1 15 6 20 9l8 5s3 1 3 2c0 2 1 2 2 3l-1 1-12-6c-15-6-29-8-45-4-5 1-10 3-15 5 8-6 17-8 26-13 3-1 6-3 9-5l2-3z"></path><path d="M369 420l3 6h0-1c-2-1-2-2-3-4l-1 1 2-3z" class="M"></path><path d="M389 712c2 0 4 0 5 1 6 1 10 2 14 5h1c3 2 6 5 9 8 3 4 6 8 8 13 5 9 8 19 12 29l18 41 20 50 7 17h0c-1-1-1-2-2-2v-1-2l-1-1-1-2v-1c0-1-1-1-1-2v-1c-1-1-1-2-2-2v-1-2l-1-1c0-1 0-1-1-1v-1-2h0c-1-2-2-4-3-5-1-2 1 0 0-1v-1l-1-1c0-1-1-2-1-3v-1l-1-1c0-1 0-1-1-1v-1c0-2-1-2-1-3l-1-2v-1c-1-1-1-2-2-3v-1-1l-1-1h0v-1l-1-2v-1c0-1-1-1-1-1v-1c0-1-1-1-1-2h0v-1l-1-1v-1-1l-1-1h0c0-1-1-1-1-2h0l-1-1v-1h0c-1-1 0-1-1-2l-1-1h1l-1-1v-1h0c-1-2 0-1 0-2-1-1-1-1-1-2l-1-1v-1c0-1-1-2-1-3l-2-3c0-1 0-2-1-2 0-1 0-2-1-2v-1-1s0-1-1-1c0-2-1-2-1-4h-1c0-2-1-4-1-5l-1-1-2-5h0c0-1 0 0-1-1v-1c0-1-1-2-1-2v-1c-1-2-1-3-2-4 0-1 0-2-1-2v-2c-1-1-1-2-1-3l-1-1-1-2v-1l-1-1v-1c-1-1 0-1-1-1v-1h0c0-1-1-1-1-2l-1-3-1-2c-1-1-1-1-1-2-1-1-1-2-2-3v-1l-1 1c1 1 1 2 2 3l3 7 1 2h0c1 1 1 1 1 2l2 4v1l2 4c0 1 1 3 2 5v1l2 3v1c0 1 1 2 1 3s1 3 2 4l1 2v2l3 7c0 1 1 1 1 2v1l1 1 1 3 1 2v1c1 1 1 2 2 3 0 2 1 4 2 6 0 1 1 1 1 2s0 1 1 2c0 2 1 3 2 4 0 2 2 7 3 9v1h1v1l2 5 3 7 1 2c0 1 0 2 1 3l1 4 2 4c1 1 1 1 1 2v1l1 1v1c1 1 1 2 1 3l1 1 1 3 3 6v2l1 1s0 1 1 2l8 21c1 0 0 0 1 1 0 1 0 1 1 2 0 1 0 2 1 3v3l-67-161c-6-13-15-20-27-25h1l-1-1c-1-1-1-1-2-1-2-1-3 0-4 0l-2-2z" class="D"></path><path d="M369 420v-1c1-2 0-5-1-7-4-7-10-9-17-11-1-1-2-1-3-1h0c3 0 5 0 7 1 3 1 6 3 9 4 4 3 8 7 12 11l27 21c3 3 8 6 11 10 3 2 5 5 8 8 2 3 8 13 12 13 3 2 6 9 8 13l-6-6c-2-1-3-3-5-4-2-3-6-5-8-8-2-2-3-5-5-8-4-5-10-10-15-13 0-1-3-2-3-2l-8-5c-5-3-14-10-20-9h0l-3-6z" class="P"></path><path d="M773 252c0-2 1-2 2-3h-1v-1l2-2c2 0 2 0 3 1-1 1-1 1 0 3h-1v2h1v1c1 1 1 2 2 3h0c1 0 1 0 2 1l1 1-1 1v1c1 1 1 1 1 2l-1 1h-1l-1 1v1c0 1 0 2-1 3l-2 2v1h0l-1 1 1 1h-2c0 1 1 3 1 4-1 1-1 1-2 1l-2-1c0 1-1 1-1 1-2-1-2-1-4-1-2-1-2 0-3 0l-1 1-2-1c1-2 0-2 1-4-1-1-1-2-3-2h-2l1-1-1-1v-1l-2 1c0 1 1 1 0 2l-1-1 1-4c-1 0-1-1-2-2v-1c-1-1-1-3-1-4v-1l-2 2v-1h0l-1-1v-3c0-2 0-2 1-2l-1-1v-1c-1 1-2 1-2 1 0-1 1-2 1-2v-1-1-2c2 0 3 1 5 1l-1 2 1 1h-1c-1 0-2 0-2-1l-1 1c1 1 1 1 3 1v2h0l-1 1 1 1 1-2 2 1h1c0 1 1 1 2 2 1 0 2-1 3-1 1-1 0-1 1-1l-1 2 2 2 2-1 2-1v1h1l2-2v-1c1 0 1 0 2-1v-1z" class="K"></path><path d="M750 255l2 1 2-1 1 1c-1 1 0 1-1 2l1 1-1 1h1c-1 1-1 1-1 2l1 1h-1c-1-1-1-3-1-4v-1l-2 2v-1h0l-1-1v-3z" class="H"></path><path d="M755 260h1v-1h1c0 1-1 1 0 2 0 1 1 1 2 2v1h-2v1c2 1 2 0 4 1l-1 1h-1l-1 1c1 1 2 2 2 3h-2l1-1-1-1v-1l-2 1c0 1 1 1 0 2l-1-1 1-4c-1 0-1-1-2-2v-1h1l-1-1c0-1 0-1 1-2z" class="D"></path><path d="M773 252c0-2 1-2 2-3h-1v-1l2-2c2 0 2 0 3 1-1 1-1 1 0 3h-1v2h1v1c1 1 1 2 2 3h0c1 0 1 0 2 1l1 1-1 1v1c1 1 1 1 1 2l-1 1h-1l-1 1v1c0 1 0 2-1 3l-2 2v1h0l-1 1 1 1h-2c0 1 1 3 1 4-1 1-1 1-2 1l-2-1c0 1-1 1-1 1-2-1-2-1-4-1l-1-1h-1c-1 0-1-1-2-1 1-3 1-4 2-6h1l1 2-1 2c1 1 1 1 2 1l1-1 1-1h-2v-4-1c1 1 1 0 2 0l1-1v-2c1-1 1-2 2-2l1-1h-1v-2h1v-1h-2v-1c-1 0-2 1-2 2v3h-1v-2h-1v1h-1l-2 2-1-2h2c-1-2-1-3-1-4l2-1v1h1l2-2v-1c1 0 1 0 2-1v-1z" class="H"></path><path d="M138 175c6-1 14 0 20 0h44 158 66 18c4 0 8-1 11 0-6 1-14 0-21 0h-46-35c-7 0-14 1-20 1-5-1-11-1-16 0h-3c-7-1-15-1-23 0h0c1 1 1 0 2 0 0 0 0 1 1 1h3 10 4l1 1v-1c1 0 2 0 3 1v-1c1 0 2 1 3 1 1-1 1-1 2-1 3 2 25-1 31 1h-76-102c-5 0-35 0-37-1 2-3 13 1 15-2h-13z" class="K"></path><defs><linearGradient id="K" x1="518.142" y1="757.926" x2="512" y2="760.026" xlink:href="#B"><stop offset="0" stop-color="#0b0908"></stop><stop offset="1" stop-color="#25272a"></stop></linearGradient></defs><path fill="url(#K)" d="M525 780c-1 0-1 1-1 2h0v3c-1 2-1 2 0 4 0 2 0 3-1 4l-1 12h0v-1c-1-1 0-2-1-3v3 2c1 3 1 5 0 8h0l-1-4c0-2 0-3-1-5 0-4 0-8-1-12-1-7-1-14-3-21l-2-6s0-1-1-2v-2c-1-1-1-1-1-2l-1-2-1-1-3-5c-1-2-3-4-4-5l-3-2 6-5v-5-1c-1-1-1-1-1-2v-1c-1-1-1-2-2-3v-1c-1-2-5-5-8-7h0l1-1c1 1 2 1 3 1 0-1 1-4 0-5 0-2-1-3-1-4v-2l2 2s1 0 1 1c1 0 2 2 2 3v1c0 1 0 1 1 2v1l1 2v2c1 1 2 2 2 4 0 0 1 1 1 2v1c0 1 1 2 1 2v1 1c1 1 2 3 3 4h1c1 1 1 2 2 3l1 3c0 2 1 3 1 5l1 1v1 1h0l1 1v1 1c1 1 1 1 1 2v3c1 1 1 2 1 4 1 1 0 3 0 4 1 1 1 4 1 6v1 2c1 3 1 6 1 10h0c1-2 0-3 0-4l1-1v-4l2 2z"></path><path d="M634 175c5-1 10 0 16 0h25 84 99 26c5 0 10-1 14 0 0 1 1 2 1 3H670c0-1 0 0 1-1h2v1c2-2 35-1 41-1 0-1 0-1 1-1h2-54c-2 0-4-1-6-1h-23z" class="K"></path><path d="M745 176h53 13 6l2 1c-9 1-19 0-28 0h-45l-1-1z" class="O"></path><path d="M606 742c0-2 0-3 1-5v-1l1-1 1-3c0-1 1-2 1-3 1-3 3-6 4-9 0-1 0-2 1-2 0-1 0-1 1-2 0-1 0-1 1-2l2-6 1-1v-1c0-1 1-1 1-2s0-2 1-3c0-1 1-2 1-3h0l1-1v-1c1-1 1-2 2-3 0-2 1-4 2-5v-1l2-4v-2c1-1 1-2 2-3v-1l1-1 3-7 2-6 1-3 1-1v-2 1l-1 1c-1 3-2 7-4 9v1l-3 5h0v-1l-1 1v1h1l-3 9s-1 0-1 1h0l-1 3s-1 1-1 2l-1-1v1l-1 1v1l-1 1v1 1h0l-1 1c-1-2 2-7 3-9l11-27c2-5 5-10 7-15 0-2 1-3 2-5l4-8 34-82 1-2c1-4 4-8 5-12 0-1 1-2 2-3l1-3 2-6 3-5v-2c1-1 1-1 1-2l2-5c0-1 0-1 1-2h0l1 1-3 6v1c-1 2-1 3 0 4l-62 147-21 49c-3 9-6 18-10 26z" class="C"></path><defs><linearGradient id="L" x1="404.621" y1="202.895" x2="389.479" y2="203.127" xlink:href="#B"><stop offset="0" stop-color="#3e3d3e"></stop><stop offset="1" stop-color="#727171"></stop></linearGradient></defs><path fill="url(#L)" d="M398 194c6-4 14-7 21-10h2l1-1v2c-1 1-1 1-2 1l-3 1-5 4h3c1 0 2-1 4-2l1-1h1c-6 4-12 8-16 12l-1 1v1 4s-2 1-2 2c-1 2-3 3-3 6l1-1h1c-3 4-5 7-7 12-5 10-7 19-8 30l-1 9v15h-1c0-2-1-4-1-5-1-6-1-11-1-16 0-8-1-17 2-25 2-6 5-12 6-19 1-2 0-2-1-4l1-2c0-1-1-2-1-2l-1-1c0-2 0-2 1-4 1-1 1-2 1-3 2-1 4-1 7-2 1-1 1-1 1-2z"></path><path d="M385 264v5h-1c-1-11-2-22 1-33 2-8 4-17 8-24l1-1 1-1c0-1 0-1 1-2l1 1c-7 11-11 25-13 37 0 3 0 5 1 8l1 1-1 9z" class="J"></path><defs><linearGradient id="M" x1="389.849" y1="215.058" x2="392.648" y2="250.903" xlink:href="#B"><stop offset="0" stop-color="#101111"></stop><stop offset="1" stop-color="#3f3c3d"></stop></linearGradient></defs><path fill="url(#M)" d="M404 202v4s-2 1-2 2c-1 2-3 3-3 6l1-1h1c-3 4-5 7-7 12-5 10-7 19-8 30l-1-1c-1-3-1-5-1-8 2-12 6-26 13-37l7-7z"></path><path d="M279 388v-1c1-2-2-7-3-10l-7-18-5-11h1c1 1 1 3 2 4l1 4 1 3 126 303h0l-1-1v-2-1c-1 0-1-1-1-1v-1l-1-2-1-1v-2h-1v-1-1l-1-1v-1l-1-1h0v2c1 1 1 2 2 3 0 2 0 2 1 3v2l1 1v2h1v1 1s1 1 1 2 0-1 0 1c1 1 0 3 1 4v4l1 1c0 3-1 6-1 9 0-5 0-10-1-15h0c-2-9-6-18-10-26l-17-41-65-156-17-40c-2-5-5-11-6-16z" class="B"></path><path d="M272 230h2v1c1-1 1-1 2 0h0l1 2-1 1h2c0-1 1-1 2 0h1 2l1 2-1 2c1 0 2-1 3-1 0-1 0-1-1-1l1-2h1l1 1v2 1h1v-2c1-2 1 0 2 0s1-1 2-1c1 1 1 1 2 3h0v1 1h0c-1 2-2 3-3 4l2 1-1 1v2 3 1l2 1 1-1h0v2h-1-1c0 1 0 1 1 2h1 0c0 1 1 1 0 1-1 1-1 1-2 1l1 1 1 1c-1 1-3 2-2 3h0l1 1h3c-1 1 0 1-2 1v1h-1c1 1 1 1 3 1l-1 2h1l-1 2h-2l-1 2c-1 1-1 1-2 1l-1-1c-1 0-1 1-2 1s-1 1-2-1l-1-2c-1 0-1 0-2 1-2 1-3 1-5 1v2h1l-1 2h1l-1 2v-1l-2-1 1-1v-2h-2c0 1-1 2-2 2v1l1 1c-1 1 0 1-1 1l2 3c0 1-1 2-2 3h0-1c-1 1-1 1-2 1h0l1 1h2 1 1l1 1 1 1-2 2h1l-1 2c-1-1-2-1-3-2v-1h-1v-1c-2-1-3-2-4-4h-1c1-1 1 0 2-1 0 0 1-1 2-1 0-1 0-1-1-1h0v-1-2l1-2h1l-1-2 1-2-1-3 1-1v-1c-1-1-1-1-1-3 0-1 0 0 1-1v-1h0 0c0-1 0-2 1-2v-1c-1 1-2 1-3 2l1 1-1 1c-1 1-2 2-2 3-1 0-1 3-1 4l-1-1h1l-1-1h1v-2c0-2 2-3 2-4-1-1-1-2-2-2l1-2h0l1-1v-1-1-2c0-1 1-1 1-2v-1c0-1 0-2 1-2s2 0 2-1h1l1-1h0v-2h0c-1-1-1-2-2-3l-1 1c-2 1-1 0-2-1-1 1-1 1-2 1v1h-1l6-6c-1-1-1-2-2-2l-1-1c0-1-1-1-1-2v-1c1-1 2-1 3-2z" class="K"></path><path d="M293 235c1 1 1 1 2 3h0v1 1h0c-1 2-2 3-3 4l2 1-1 1v2 3 1l2 1 1-1h0v2h-1-1c0 1 0 1 1 2h1 0c0 1 1 1 0 1-1 1-1 1-2 1l1 1 1 1c-1 1-3 2-2 3h0l1 1h3c-1 1 0 1-2 1v1h-1c1 1 1 1 3 1l-1 2h1l-1 2h-2c0-1-1-1-2-1h0l-1-1v-2c0-2 0-2-2-4v-2h-1 0l2-2-1-1c-2 1-3 1-5 1l-1-1 1-1h-2c1-1 2-2 3-2l1 1c0 1 1 1 2 1h1v-1-1c-1-1-1-1-1-2 0 0-1 0 0-1v-1h-2c-1 0-1-1-3-1h-1l-2 1c-1 0-2 0-3-1h0-1 0l1-1c-1-1 0-1-1-2h0c1-1 1-2 3-2 1 0 1-1 1-1-1-1 0-1-1-2h1 1 2v2l-1 1h0l1 1 2-2v-1h2l-1-2 1-3h1v-2c1-2 1 0 2 0s1-1 2-1z" class="H"></path><path d="M283 250l-2-1c-1-1-1-2-2-2l1-1 1 2 2-2v1c2 1 2 1 4 1v1l-3 1h-1z" class="B"></path><path d="M459 616c2 3 4 8 8 9 2 1 4 0 6-1 1-1 2-3 3-4 0-2 0-4-1-6l-2-2c-3-4-5-8-8-13 0-2-2-4-2-5 3 2 3 5 6 8 1 0 1 1 2 2 1 3 5 6 7 9 1 0 2-1 3-1h0c-1 1-2 2-3 2 0 2-1 3 0 4 2 3 3 5 7 5 1 1 1 0 2 0h1c2-1 4-2 5-4 1-1 1-2 1-4 1 2 1 4 1 6v1h0c1 1 1 2 1 2l-1 1c5 7 11 16 14 25h-1l-1-1c0 1 0 1-1 1v1c1 1 1 3 0 5 5 10 9 21 13 32 1 3 2 8 2 11-1 1 0 2 0 3v1l-1 1c1 1 1 1 1 2-1 3 0 5-1 7l-6-20c-5-16-11-31-21-44-2-2-6-5-6-7-2-1-4-3-5-5h-1c-1-1-3-2-4-3h-1l-1-1c0-1-1-1-2-2h0c-1-1-2-1-3-2h0-1-3v-1c-2 0-4-1-5-2l-1-2-1-3v-5z" class="L"></path><path d="M481 635c-2-2-4-3-6-5-2-1-4-1-5-2 2-2 5-4 8-5 4 1 6 3 9 7v1c0 1 1 2 1 3l1 1c0 2 1 3 2 5-2-2-3-4-5-6l-1-2c-1-1-2-1-4-1v4z" class="C"></path><defs><linearGradient id="N" x1="496.689" y1="623.551" x2="496.333" y2="649.366" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2e2e2e"></stop></linearGradient></defs><path fill="url(#N)" d="M506 656c-3-5-5-10-8-15-1-2-3-5-5-7-1-2-3-4-5-5h-1l-3-3 7-3c1 0 2 0 3-1l1 3c5 7 11 16 14 25h-1l-1-1c0 1 0 1-1 1v1c1 1 1 3 0 5z"></path><defs><linearGradient id="O" x1="504.293" y1="674.035" x2="510.816" y2="671.034" xlink:href="#B"><stop offset="0" stop-color="#171717"></stop><stop offset="1" stop-color="#323131"></stop></linearGradient></defs><path fill="url(#O)" d="M487 630c11 14 21 30 27 47 3 7 5 15 7 22-1 1 0 2 0 3v1l-1 1c1 1 1 1 1 2-1 3 0 5-1 7l-6-20c-5-16-11-31-21-44-3-6-7-10-12-14v-4c2 0 3 0 4 1l1 2c2 2 3 4 5 6-1-2-2-3-2-5l-1-1c0-1-1-2-1-3v-1z"></path><defs><linearGradient id="P" x1="529.88" y1="725.706" x2="513.89" y2="725.324" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#4b4b4c"></stop></linearGradient></defs><path fill="url(#P)" d="M496 624c1 1 2 3 3 4 3 2 7 9 9 9 2 2 2 4 2 7l6 12h1v-3l-1-6 1 1 2 7c1 0 1 0 1-1 3 8 4 17 5 25 1 3 1 7 2 10h2c1 5 1 11 1 16h2c2 11 2 86-4 95v5h-1-1 0c1-3-1-8 0-11v-1h-1v-4l-1-2c1-1 1-3 1-4v-3h0l-2-2h0c1-2 1-7 1-9-1-3-1-7-1-9-1-2 0-5 0-6-1-2-1-4-1-5-1-2 0-4 0-5-1-1-1-3-1-4-1-1 0-2 0-3l-1-1v-4c0-2-1-3-2-4v-1h0 1l1-1c1-2 0-7-1-9l1-2v-2c1-2 0-4 1-7 0-1 0-1-1-2l1-1v-1c0-1-1-2 0-3 0-3-1-8-2-11-4-11-8-22-13-32 1-2 1-4 0-5v-1c1 0 1 0 1-1l1 1h1c-3-9-9-18-14-25l1-1z"></path><path d="M496 624c1 1 2 3 3 4l1 1c1 3 3 6 5 9 3 5 6 12 8 17 2 6 4 11 5 16 4 15 7 30 9 46 1 10 1 20 1 31-1-2-1-4-1-6 0-4 1-10 0-14-1-2-1-4-1-6 0-5-1-12-2-17-2-12-5-23-8-34-2-7-4-14-7-21-3-9-9-18-14-25l1-1z" class="M"></path><path d="M506 656c1-2 1-4 0-5v-1c1 0 1 0 1-1l1 1h1c3 7 5 14 7 21 3 11 6 22 8 34 1 5 2 12 2 17h-1v-3c-2-3-2-7-3-10l-1-6v-1c0-1-1-2 0-3 0-3-1-8-2-11-4-11-8-22-13-32z" class="K"></path><path d="M527 689h2c1 5 1 11 1 16h2c2 11 2 86-4 95 0-2 0-5 1-7v-11l1-21c1-24 1-48-3-72z" class="I"></path><path d="M521 703l1 6c1 3 1 7 3 10v3h1c0 2 0 4 1 6 1 4 0 10 0 14 0 2 0 4 1 6 1 5 0 12-1 17 0 2 1 4 0 5l-1 13h0-1 0v-3h0l-2-2h0c1-2 1-7 1-9-1-3-1-7-1-9-1-2 0-5 0-6-1-2-1-4-1-5-1-2 0-4 0-5-1-1-1-3-1-4-1-1 0-2 0-3l-1-1v-4c0-2-1-3-2-4v-1h0 1l1-1c1-2 0-7-1-9l1-2v-2c1-2 0-4 1-7 0-1 0-1-1-2l1-1z" class="C"></path><path d="M525 722h1c0 2 0 4 1 6 1 4 0 10 0 14-1-2-1-5-1-8 0 0 0-1-1-1h-1c0-4 2-7 1-10v-1z" class="B"></path><path d="M519 727l1-1c1-2 0-7-1-9l1-2c1 2 1 3 1 5v1c1 1 0 2 1 3s0 3 1 5c0 1-1 3 0 5h1v1c1 1 1 1 1 2v1c1 6 1 14 1 20-3 3 0 8-2 11-1-3-1-7-1-9-1-2 0-5 0-6-1-2-1-4-1-5-1-2 0-4 0-5-1-1-1-3-1-4-1-1 0-2 0-3l-1-1v-4c0-2-1-3-2-4v-1h0 1z" class="D"></path><path d="M739 200c1 1 2 1 3 1h2c-1-1-1-1-1-2h1l2 2h-1 0c0 1 0 1 1 1h0c2 0 3 1 4 2l2-1v2h2v1l2-1v1c0 1 1 2 2 3h-1v1l1 1c1-1 1-2 3-3l1 1-1 2h1c1 1 1 2 2 4-1 1-2 1-3 1 0 1 0 1 1 1l1 1h0l1 1h-1c-1 1 0 1-1 1v1h2v3l1 1h0 2v-1h-1v-2h2v-1l1-1h0l2 2c0-1 1-1 1-2h1l1 1h0v2l-1 1v-1c2 0 3 0 4 1v-1c1 1 1 1 1 2h1c1 0 1 0 2 1-2 0-2 0-3 1h1l1 1c0 1 0 1-1 2h0c1 1 2 1 2 2h1v1l-2 3 1 1h-1c-1-1 0-1-2-1v4h2v1l-2 2v1h1v1c0 1 0 1 1 1l-1 1c-1-1-1-1-3-1l-2 2v1h1c-1 1-2 1-2 3v1c-1 1-1 1-2 1v1l-2 2h-1v-1l-2 1-2 1-2-2 1-2c-1 0 0 0-1 1-1 0-2 1-3 1-1-1-2-1-2-2h-1l-2-1-1 2-1-1 1-1h0v-2c-2 0-2 0-3-1l1-1c0 1 1 1 2 1h1l-1-1 1-2v-1c1 1 1 1 2 1v-1c-1-1-2-2-3-2h0c-1-1-1-1-1-2h-1l1-1 1 1 1-2h0v-1-1-2h1-2l1-1c-2-1-2-1-4-1-1-1-3-3-4-3s-2-1-3-1h-2v-1c-1-1-1-1-1-2l-1-1-2 2-2-1h1l1-1c-2 0-4-2-5-4-2-1-4-1-6-2v-1l-6-3c2-1 2-1 4-1h0l3-3v-1c-1 1-1 1-2 1 0-1 1-1 1-2h-2c-1 1-1 1-2 1v-1c1 0 1-1 2-2h0c1 0 1 0 2-1v-2-1c0-1 1-2 2-3 0 1 1 1 2 1 1-1 1-2 3-2v1h0c1-1 2-1 3-2 1 0 2 1 3 1z" class="K"></path><path d="M757 212c1 1 1 2 1 3-1 1-2 0-4 0l3-3z" class="F"></path><path d="M739 206l3 2c-1 1-1 0-2 1-1 0-1 0-1 1l1 1v1c1 0 1 1 1 1v1l1-1h0l1-1v3s0 1-1 1-1-1-1-1c-1 0-1 1-2 1 0-1-1-2-2-3l1-1c0-1 0-1-1-2l-1-2c1-1 1-2 3-2z" class="H"></path><path d="M752 205h2v1l2-1v1c0 1 1 2 2 3h-1v1l1 1c1-1 1-2 3-3l1 1-1 2h1c1 1 1 2 2 4-1 1-2 1-3 1 0 1 0 1 1 1l1 1h0l1 1h-1c-1 1 0 1-1 1 0-1-1-1-2-2h0c-1-1-2-2-2-3s0-2-1-3l-3-2-1 1-1-1v-3l1-1-1-1z" class="B"></path><path d="M726 207h1l1 1 1-1c1 1 1 1 1 3h1v-1l-1-2c1-1 1-2 2-2l1 1h2l-1 2 1 1s-1 1-1 2l1 1h-1v-1c-3 0-5 2-7 3v1l2 1v-2h2 1 1l-2 2 1 1h-1v3c-2-1-2-1-2-3-1 0-1 1-2 1h0l-1 1-6-3c2-1 2-1 4-1h0l3-3v-1c-1 1-1 1-2 1 0-1 1-1 1-2h-2c-1 1-1 1-2 1v-1c1 0 1-1 2-2h0c1 0 1 0 2-1zm38 17l1 1h0 2v-1h-1v-2h2v-1l1-1h0l2 2c0-1 1-1 1-2h1l1 1h0v2l-1 1v-1c2 0 3 0 4 1v-1c1 1 1 1 1 2h1c1 0 1 0 2 1-2 0-2 0-3 1h1l1 1c0 1 0 1-1 2h0c1 1 2 1 2 2h1v1l-2 3 1 1h-1c-1-1 0-1-2-1 1-1 1-1 1-2h-3c0-1 1-2 2-3-1 0-1 0-2 1h-1-1c-2-2 2 1-1-1l1-2h-1l-1 1c-1-1-2-1-2-2v-1h-1c-1 0-2 0-3 1h-1c-1-1-2-1-2-2l2-1h-2l1-1z" class="H"></path><path d="M739 200c1 1 2 1 3 1h2c-1-1-1-1-1-2h1l2 2h-1 0c0 1 0 1 1 1h0c2 0 3 1 4 2l2-1v2l-2 1v2l-1 1-3-5-4 4-3-2 1-1-1-1-1 1v-1h-2l-1-1v3h-2l-1-1c-1 0-1 1-2 2l1 2v1h-1c0-2 0-2-1-3l-1 1-1-1h-1v-2-1c0-1 1-2 2-3 0 1 1 1 2 1 1-1 1-2 3-2v1h0c1-1 2-1 3-2 1 0 2 1 3 1zm4 30l1-2-1-1h0l-1-1h1c0-1 0-1 1-2h1c0-1 0-1 1-2h-1c1-2 2-1 4-1 0 0 0 1 1 2l-1 1 1 1-1 1h1v-1c1 0 2-1 3-1v-1l1-1h1c1-1 1-2 2-2l1 1-1 1h0l1 1-1 1c0 1 0 1 1 1 1 1 0 1 1 1 0 1 1 1 1 1h1c1 1 0 2 2 3v3c1-1 1 0 2-1h0c0 1 0 0 1 1v1c-1 1-1 2-2 3h1v1l1 1h-2c1 1 3 2 3 3l-3 2v1c1 0 1 0 2-1 0 1 0 0 1 1 1 0 1 0 2-1l-1-1h1 1v2 2l-1 2 1 1h0c1 0 2 1 3 2v1c-1 1-1 1-2 1v1l-2 2h-1v-1l-2 1-2 1-2-2 1-2c-1 0 0 0-1 1-1 0-2 1-3 1-1-1-2-1-2-2h-1l-2-1-1 2-1-1 1-1h0v-2c-2 0-2 0-3-1l1-1c0 1 1 1 2 1h1l-1-1 1-2v-1c1 1 1 1 2 1v-1c-1-1-2-2-3-2h0c-1-1-1-1-1-2h-1l1-1 1 1 1-2h0v-1-1-2h1-2l1-1c-2-1-2-1-4-1-1-1-3-3-4-3s-2-1-3-1z" class="B"></path><path d="M754 253c0-1 1-2 3-2 0-1 1-1 1-1h2 1 1c0 2 0 2 2 3 0-1 1-2 2-2v1h1c1 1 2 1 2 2h-1-2c-1 1-1 1-2 1l-1-1c-1 0 0 0-1 1-1 0-2 1-3 1-1-1-2-1-2-2h-1l-2-1z" class="O"></path><defs><linearGradient id="Q" x1="402.435" y1="227.52" x2="356.317" y2="279.259" xlink:href="#B"><stop offset="0" stop-color="#8c8b8b"></stop><stop offset="1" stop-color="#e8e7e7"></stop></linearGradient></defs><path fill="url(#Q)" d="M376 218c-1-8-4-14-8-20l-9-12c4 4 9 8 15 10h1l-1 1v1h1c2-1 6 0 8 1 2 0 4-1 7-1 0 1 0 2-1 3-1 2-1 2-1 4l1 1s1 1 1 2l-1 2c1 2 2 2 1 4-1 7-4 13-6 19-3 8-2 17-2 25 0 5 0 10 1 16 0 1 1 3 1 5h1l1 9c0 1 0 2 1 3v5c0 1 1 2 1 3 0 0 0 1 1 2v1 1c0 1 0 0 1 1 1 2 0 4 0 7 2 3 2 7 4 11v2l1 4c1 1 0 1 0 2 0 2 1 5 2 6 0 1 1 2 0 3l-2-5v-2l-7-23c-2-4-3-9-5-12-5-12-25-46-36-52-1-1-1 0-2 0l-1-1c3-4 3-8 3-13l2-10v1c0 2 0 4 1 6 1 4 4 8 8 9 3 1 6 1 9 0 4-2 6-5 7-8l1-1c1-3 1-7 1-10z"></path><path d="M383 282c1 2 1 4 3 6 0 1 0 2 1 3l-1 3c-1-1-2-3-2-5-1-2-1-4-1-7z" class="I"></path><defs><linearGradient id="R" x1="393.425" y1="214.574" x2="372.043" y2="219.795" xlink:href="#B"><stop offset="0" stop-color="#787777"></stop><stop offset="1" stop-color="#acabab"></stop></linearGradient></defs><path fill="url(#R)" d="M376 218c-1-8-4-14-8-20l-9-12c4 4 9 8 15 10h1l-1 1v1h1c2-1 6 0 8 1 2 0 4-1 7-1 0 1 0 2-1 3-1 2-1 2-1 4l1 1s1 1 1 2l-1 2c1 2 2 2 1 4-1 7-4 13-6 19-3 8-2 17-2 25 0 5 0 10 1 16 0 1 1 3 1 5h1l1 9c-2-2-2-4-3-6-1-4-4-11-3-14 1-2 0-4 0-5v-14h1c0-4-1-6-2-9v-1c-2-5 0-11 0-16v-2c-1-2 0-5 0-7v1c1-3 0-3-1-5l-1-1v-1c-1-3-3-6-6-8h0c1 2 2 5 3 7l1 1v2c0 1 0 1 1 2v1 3c1 0 1 1 1 2h-1z"></path><path d="M451 540c-11-7-22-12-33-18-8-5-16-11-22-17-15-15-13-35-13-54 1 2 1 6 1 9 2 10 4 17 9 26 3 3 5 5 10 5h2c1 0 2-1 3-1 1-1 1-2 2-2s0 1 0 2c1 1 1 1 1 2-1 4 1 6 2 10 2 0 5-1 7-3 1-1 1-1 1-3-2-2-3-5-5-8 2 1 3 2 4 3 1 0 1 1 2 1l4 3c3 2 5 3 8 3l3-2c2 4 6 7 9 9 2 2 3 4 5 5h0c1 1 2 2 3 4l9 10c0 1 1 3 2 4 3 3 6 7 8 11 2 3 3 6 5 8 3 5 5 10 7 15 1 2 2 5 4 7 3 5 7 12 7 18l5 10v1c-3-6-6-11-10-16-3-5-6-9-10-13-4-6-10-11-16-16-4-5-9-10-14-13z" class="G"></path><path d="M393 486c3 3 5 5 10 5h2c1 0 2-1 3-1 1-1 1-2 2-2s0 1 0 2c1 1 1 1 1 2-1 4 1 6 2 10h-3l-2-2c-1 0-1 0-2-1l1-1-1-1s-1 1-2 1h-1l1 1v1c0 2 1 3 1 4h0c0 1 2 2 2 3l1 3-1-1c-2-1-3-2-4-4-2-4-6-5-7-9-1-2-2-3-3-4v-1h1 2 1v1h1l-3-2c-1-1-2-2-2-4z" class="M"></path><path d="M405 491c1 0 2-1 3-1 0 1-1 2-2 3v2c-2 0-5 0-7-1v1l1 2c-2 0-4-2-6-4l5 1c2 0 4 0 6-2v-1z" class="E"></path><path d="M408 490c1-1 1-2 2-2s0 1 0 2c1 1 1 1 1 2-1 4 1 6 2 10h-3l-2-2c-1 0-1 0-2-1l1-1-1-1s-1 1-2 1h-1l1 1v1c-1-1-2-2-4-3l-1-2v-1c2 1 5 1 7 1v-2c1-1 2-2 2-3z" class="Q"></path><path d="M404 500v-1l-1-1h1c1 0 2-1 2-1l1 1-1 1c1 1 1 1 2 1l2 2h3c2 0 5-1 7-3l1 1c-2 1-6 4-9 4h1c1 1 2 2 2 3 2 1 2 2 4 2l1 1c1 1 3 1 5 3 6 0 8 4 12 7 2 2 5 4 8 6 4 3 6 7 11 9 1 0 2 2 3 3 3 3 7 6 9 9 4 4 8 8 10 13-2-1-4-4-7-6-5-6-12-12-18-17-4-3-9-6-13-8l-16-8c-3-2-8-3-10-6-2-2-4-4-6-5l-1-3c0-1-2-2-2-3h0c0-1-1-2-1-4z" class="E"></path><path d="M432 522l-18-10 8 3c2 0 3 1 5 2l1 1v-1c-1 0-2-1-3-2-2-2-3-1-5-3l-6-2c-2-2-4-3-4-5 0-1 1-1 2-1h0 1c1 1 2 2 2 3 2 1 2 2 4 2l1 1c1 1 3 1 5 3 1 0 2 1 3 1l5 5-1 1-1-1-1 1 2 2z" class="R"></path><path d="M425 513c6 0 8 4 12 7 2 2 5 4 8 6 4 3 6 7 11 9 1 0 2 2 3 3 3 3 7 6 9 9-1 0-1 0-2-1h-1c-3-3-6-7-10-9l-3-3h-1l-2-2h0c-6-4-11-7-17-10l-2-2 1-1 1 1 1-1-5-5c-1 0-2-1-3-1z" class="J"></path><defs><linearGradient id="S" x1="452.322" y1="517.693" x2="442.685" y2="534.845" xlink:href="#B"><stop offset="0" stop-color="#040403"></stop><stop offset="1" stop-color="#2e2d2e"></stop></linearGradient></defs><path fill="url(#S)" d="M421 496l1-1 9 6c0 1 1 2 2 2 0 2 1 3 3 4 0 1 0 1 1 2s2 2 3 2c2 1 4 4 5 5 0 1 1 1 1 2v1c3 3 7 5 10 8 5 5 10 11 15 17 2 3 5 6 6 9l7 11c1 2 2 4 2 6l-8-10c-2-5-6-9-10-13-2-3-6-6-9-9-1-1-2-3-3-3-5-2-7-6-11-9-3-2-6-4-8-6-4-3-6-7-12-7-2-2-4-2-5-3l-1-1c-2 0-2-1-4-2 0-1-1-2-2-3h-1c3 0 7-3 9-4l-1-1c1-1 1-1 1-3z"></path><path d="M421 496l1-1 9 6c0 1 1 2 2 2 0 2 1 3 3 4 0 1 0 1 1 2s2 2 3 2c2 1 4 4 5 5 0 1 1 1 1 2-7-4-17-18-25-18l-1-1c1-1 1-1 1-3z" class="N"></path><path d="M416 488c2 1 3 2 4 3 1 0 1 1 2 1l4 3c3 2 5 3 8 3l3-2c2 4 6 7 9 9 2 2 3 4 5 5h0c1 1 2 2 3 4l9 10c0 1 1 3 2 4 3 3 6 7 8 11 2 3 3 6 5 8 3 5 5 10 7 15 1 2 2 5 4 7 3 5 7 12 7 18-4-5-7-11-10-17 0-2-1-4-2-6l-7-11c-1-3-4-6-6-9-5-6-10-12-15-17-3-3-7-5-10-8v-1c0-1-1-1-1-2-1-1-3-4-5-5-1 0-2-1-3-2s-1-1-1-2c-2-1-3-2-3-4-1 0-2-1-2-2l-9-6-1 1c-2-2-3-5-5-8z" class="J"></path><path d="M416 488c2 1 3 2 4 3 1 0 1 1 2 1l4 3c3 2 5 3 8 3h-2c0 1 1 2 1 2l1 1s1 1 2 1c2 1 3 4 5 5l3 3c2 1 0-1 2 0 2 2 4 7 7 7l3 3c1 1 2 3 4 5 1 1 2 3 2 4l-29-26c-1 0-2-1-2-2l-9-6-1 1c-2-2-3-5-5-8z" class="E"></path><path d="M433 503l29 26c4 5 8 9 11 14 1 2 1 3 2 4 1 2 2 3 3 6h-1c-1-3-4-6-6-9-5-6-10-12-15-17-3-3-7-5-10-8v-1c0-1-1-1-1-2-1-1-3-4-5-5-1 0-2-1-3-2s-1-1-1-2c-2-1-3-2-3-4z" class="G"></path><path d="M349 365l-2-19c2 5 3 9 3 14 1 3 1 6 2 8 1 5 8 12 13 15l3 1c2 1 4 1 6 1 4-1 6-3 9-6v6c8 6 16 13 23 21 6 6 11 13 16 21 3 3 7 8 8 12l2 2 5 13 1 2c0 2 0 7 1 9 2 3 4 7 5 11l2 4c0 1 1 2 1 3l1 2c1 1 1 1 1 2 1 4 4 5 5 8l3 6v1c-1 0 0 0-1-1 0 1 0 1 1 2-1 1 0 1-1 1-1-1-2-3-3-4a30.44 30.44 0 0 0-8-8c-2-1-4-1-5-2-1 0 0-5-1-6-1-6-9-12-14-14-2-1-4-1-6-3 0-1 1-3 0-5-1-7-9-12-15-16l1-1c-1-1-2-1-2-3 5 3 11 8 15 13 2 3 3 6 5 8 2 3 6 5 8 8 2 1 3 3 5 4l6 6c-2-4-5-11-8-13-5-8-10-15-15-22-10-12-20-22-31-32-10-9-20-18-33-22-2-1-5-2-7-1-3 0-6 2-9 4-4 2-9 4-13 8-2 2-5 5-8 7 4-5 8-9 12-13 1-2 3-3 5-5l6-9h1c1-3 1-7 0-10v-3-1c3-1 4-3 7-4z" class="N"></path><path d="M349 365c0 6 2 17-2 22-2 3-6 4-8 6-3 1-6 3-9 4 1-2 3-3 5-5l6-9h1c1-3 1-7 0-10v-3-1c3-1 4-3 7-4z" class="D"></path><defs><linearGradient id="T" x1="406.561" y1="413.572" x2="399.3" y2="421.417" xlink:href="#B"><stop offset="0" stop-color="#c7c6c6"></stop><stop offset="1" stop-color="#edecec"></stop></linearGradient></defs><path fill="url(#T)" d="M383 379v6c8 6 16 13 23 21 6 6 11 13 16 21 3 3 7 8 8 12l2 2 5 13-2-2-1-2c-2-1-4-3-5-4-2-1-1 0-2-1l-1-1c-1-1-3-2-5-2 2 2 3 3 4 5h-1l-4-3c-2-2-4-4-6-7 0-1-1-1-1-2-1-1-3-2-3-3h0c-2-1-7-6-8-8-1 0-1-1-2-2l-1-1c-2-1-4-3-6-5-1 0-1 0-2-1v-1c-2-1-3-2-4-3l-3-3c-2-2-6-5-9-7-1 0-2-1-3-2-2-1-6-4-7-5 5 1 7 5 12 6v-1c-3-1-7-6-9-8l-2-2c2 0 5 1 7 1-1-1-1 0-1-1s-4-1-5-2c2-1 5 0 7-1h1l1-1v1c-2 0-6 0-8-1v-1c2 1 4 1 6 1 4-1 6-3 9-6z"></path><path d="M383 379v6c8 6 16 13 23 21 6 6 11 13 16 21 3 3 7 8 8 12-1-1-2-2-2-3l-1-1c-1-1-2-2-2-3 0 0-1-1-1-2-2 0-3-1-3-2-1-1 0-1-1-2h0l-1-1c-1-2-5-5-7-7-5-6-10-12-17-16-3-2-6-4-10-5-2 0-3-1-4-2-2 0-4-1-6-2-1 0-2 0-2-1-2 0-3-1-5-1l-2-2c2 0 5 1 7 1-1-1-1 0-1-1s-4-1-5-2c2-1 5 0 7-1h1l1-1v1c-2 0-6 0-8-1v-1c2 1 4 1 6 1 4-1 6-3 9-6z" class="P"></path><defs><linearGradient id="U" x1="666.775" y1="353.198" x2="722.334" y2="367.229" xlink:href="#B"><stop offset="0" stop-color="#a3a1a1"></stop><stop offset="1" stop-color="#f2f2f1"></stop></linearGradient></defs><path fill="url(#U)" d="M709 262l2 2c0-2-1-3-1-5-1-2-2-3-2-5-1-2-2-3-3-5s-2-3-3-4c0-1 0-1-1-2s-4-5-4-7h0l2 2c2 2 0 0 1 2h1l1 2s0 1 1 2c0 1 0 0 1 1l1 1v1l1 1v1h1v2l2 2c0 1 1 3 2 4v1c1 1 1 2 2 4l1-1c2 4 3 9 5 14 4 13 4 28 4 42 0 11-1 21-2 32-3 18-7 36-11 53l-10 38c-1 6-3 12-4 18 0 3-1 7-1 9 1 2 2 3 2 4l1 1c-10 5-16 13-21 23-1 1-2 4-3 5l-22 53c-2 0-3-2-5-4v-1c1-3 4-9 4-12-1 1-1 2-2 3l-1 5h0-1v-3c0-1 2-5 3-6l-1-1c1-4 3-8 4-12l6-12c9-27 19-54 27-82l7-28c1-6 2-13 4-20h0c5-33 7-66 1-99 1 1 2 1 3 2 0-3 1-7 0-9v-7c-1-2-1-4-1-5l-3-6c0-1 0-1-1-2v-3h1v-2c-1-1-1-2-1-3v-1c0-1 0-3 1-4 1 1 2 2 3 4s1 2 1 4h0c-1-1-2-2-2-3l-1 1c1 1 1 2 2 4h0 0c2 3 3 5 4 8 1 1 1 3 2 4 0 1 1 1 1 1l1-1-1-2v-2l2 3z"></path><path d="M659 510h0c5-11 8-23 12-34 1-1 1-3 2-5l2 1v1l1-1h0l-1 4c2 2 5 4 7 6l1 1v1h0l-1 1v1l-1 1c-1 1-2 1-2 2v1l-1 1v1s-1 0-1 1l-1 1v1 1h-1v1 1l-1 1v1l-22 53c-2 0-3-2-5-4v-1c1-3 4-9 4-12-1 1-1 2-2 3l-1 5h0-1v-3c0-1 2-5 3-6l-1-1c1-4 3-8 4-12l6-12z" class="G"></path><defs><linearGradient id="V" x1="688.654" y1="306.524" x2="709.744" y2="308.851" xlink:href="#B"><stop offset="0" stop-color="#8e8c8c"></stop><stop offset="1" stop-color="#bebdbc"></stop></linearGradient></defs><path fill="url(#V)" d="M696 245c0-1 0-3 1-4 1 1 2 2 3 4s1 2 1 4h0c-1-1-2-2-2-3l-1 1c1 1 1 2 2 4h0 0c2 3 3 5 4 8 1 1 1 3 2 4 0 1 1 1 1 1l1-1-1-2v-2l2 3v2c2 2 2 8 2 10 0 1 0 1 1 2 1 6 0 15-1 21v7l-1 6c-1 2-1 4-1 6-1 2 0 4-1 6v1l-1 7c-1 2 0 4-1 6l-5 29c0 3-2 5-1 7v-1c1 1 0 2 0 3h0-1l-2 6h0c5-33 7-66 1-99 1 1 2 1 3 2 0-3 1-7 0-9v-7c-1-2-1-4-1-5l-3-6c0-1 0-1-1-2v-3h1v-2c-1-1-1-2-1-3v-1z"></path><path d="M428 193l1 1-5 2h0c-4 2-6 5-9 8h-1c-1 3-4 4-5 7h0c-4 8-10 17-11 27h1c-1 1-1 2-1 3v1h0c-1 2-1 4-1 6-1 2 0 4-1 6h-1v1c1 2 0 4 1 6l1 3c0 3 1 8 4 10-1 2-1 4 0 6v1 1c2 13 3 26 6 39 6 25 15 49 25 72l40 100c4 12 10 23 14 35l19 51c2 8 6 16 7 24l-1 5c2 3 3 7 3 10 0 2 1 7 0 8l5 23 1 5c0 1 0 1-1 1l-2-7-1-1c-1-7-3-15-7-21-1-3-3-8-6-9 0-1-2-1-3-2 0-1 2-2 3-3l-1-4 1-1c0-3-1-6-2-9v-1l-5-10c0-6-4-13-7-18-2-2-3-5-4-7-2-5-4-10-7-15-2-2-3-5-5-8-2-4-5-8-8-11-1-1-2-3-2-4l-9-10c-1-2-2-3-3-4h0l-1-3-1-1v-2c0-3-2-3-4-5l-4-4v-3h4a30.44 30.44 0 0 1 8 8c1 1 2 3 3 4 1 0 0 0 1-1-1-1-1-1-1-2 1 1 0 1 1 1v-1l-3-6c-1-3-4-4-5-8 0-1 0-1-1-2l-1-2c0-1-1-2-1-3l-2-4c-1-4-3-8-5-11-1-2-1-7-1-9l-1-2-5-13-2-2c-1-4-5-9-8-12-5-8-10-15-16-21-7-8-15-15-23-21v-6l1-4-9-6c2 0 4-1 5-2s1-2 1-3c0-6-6-13-10-17-6-6-13-11-20-15-8-6-18-11-27-14-11-4-22-5-33-6 6-1 13-2 20-2 8-1 20 1 28-2 3-2 5-4 7-8h0c0-2 0-4-1-5s-1-5-1-6h-1c-4-3-6-5-11-4h-1-1v-4c1-2 0-3-1-5-3-8-9-15-12-22-9-17-15-35-21-53 2 2 6 13 8 17l7 13c7 8 12 14 23 16 4 0 7 0 10-3l1 1c1 0 1-1 2 0 11 6 31 40 36 52 2 3 3 8 5 12l7 23v2l2 5c1-1 0-2 0-3-1-1-2-4-2-6 0-1 1-1 0-2l-1-4v-2c-2-4-2-8-4-11 0-3 1-5 0-7-1-1-1 0-1-1v-1-1c-1-1-1-2-1-2 0-1-1-2-1-3v-5c-1-1-1-2-1-3l-1-9v-15l1-9c1-11 3-20 8-30 2-5 4-8 7-12 7-9 17-16 27-20z" class="G"></path><path fill="#020202" d="M350 279c1 0 2 0 4 1 1 1 1 2 1 3-1 2-2 2-3 3h-2c-2 0-2 0-3-1v-2c0-2 1-3 3-4z"></path><path d="M432 441h0c2 1 2 2 3 4h1 0c-1-1-1-2-1-4l21 50c-1-1-1-1-1-2-7-10-12-22-17-33l-1-2-5-13z" class="J"></path><path d="M344 289c6 4 11 10 15 15 6 7 12 13 16 20-6-4-10-9-16-14l-8-9c-2-1-3-3-4-4s0-1-1-2h-1c-1-1-1-5-1-6z" class="F"></path><path d="M511 608l-11-35c-1-3-4-8-3-11l-2-4c0-2-1-3-2-4l-1-4c0-1 0-2-1-2 0-1 0-2-1-3 1 0 2 1 2 2v-1-1l-1-1v-1-1l-1-1c0-2 0-2-1-3v-1h0l-1-2c-1-1-1-2-1-3-1-2 0 1-1-1l-1-2 1-1 19 51c2 8 6 16 7 24l-1 5zm-83-415l1 1-5 2h0c-4 2-6 5-9 8h-1c-1 3-4 4-5 7h0c-4 8-10 17-11 27h1c-1 1-1 2-1 3v1h0c-1 2-1 4-1 6-1 2 0 4-1 6-1-1-1-2-1-3-1 4-2 7-2 12 0 1 0 2-1 3h-2l-1 9c0-10 0-20 1-31 0-4 2-8 3-13 1-2 2-4 1-6 2-5 4-8 7-12 7-9 17-16 27-20z" class="I"></path><path d="M393 263c-1-4-1-8 0-12 0-4 0-9 1-13 3-13 8-27 21-34h-1c-1 3-4 4-5 7h0c-4 8-10 17-11 27h1c-1 1-1 2-1 3v1h0c-1 2-1 4-1 6-1 2 0 4-1 6-1-1-1-2-1-3-1 4-2 7-2 12z" class="P"></path><path d="M345 295h1c1 1 0 1 1 2s2 3 4 4l8 9c6 5 10 10 16 14 2 1 5 7 6 9 6 8 12 16 17 25 6 10 11 22 16 33h-1c-6-8-9-17-14-25-4-6-9-12-13-18l-7-10c-2-3-5-5-7-8-2-2-4-5-6-7l-16-17c-2-2-2-4-3-6h-1c0-2 0-4-1-5z" class="J"></path><defs><linearGradient id="W" x1="417.061" y1="403.54" x2="404.542" y2="417.774" xlink:href="#B"><stop offset="0" stop-color="#302f2e"></stop><stop offset="1" stop-color="#4d4c4d"></stop></linearGradient></defs><path fill="url(#W)" d="M384 375c19 11 32 33 43 52 3 4 6 9 8 14 0 2 0 3 1 4h0-1c-1-2-1-3-3-4h0l-2-2c-1-4-5-9-8-12-5-8-10-15-16-21-7-8-15-15-23-21v-6l1-4z"></path><defs><linearGradient id="X" x1="469.335" y1="562.247" x2="486.353" y2="555.129" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2a2929"></stop></linearGradient></defs><path fill="url(#X)" d="M445 492a30.44 30.44 0 0 1 8 8c1 1 2 3 3 4 6 8 12 16 17 25 4 7 8 15 12 22 7 16 14 32 20 49l9 26 5 23 1 5c0 1 0 1-1 1l-2-7-1-1c-1-7-3-15-7-21-1-3-3-8-6-9 0-1-2-1-3-2 0-1 2-2 3-3l-1-4 1-1c0-3-1-6-2-9v-1l-5-10c0-6-4-13-7-18-2-2-3-5-4-7-2-5-4-10-7-15-2-2-3-5-5-8-2-4-5-8-8-11-1-1-2-3-2-4l-9-10c-1-2-2-3-3-4h0l-1-3-1-1v-2c0-3-2-3-4-5l-4-4v-3h4z"></path><path d="M501 597c2 5 3 11 5 16 3 6 5 13 7 20 2 4 2 10 4 15l-1-1c-1-7-3-15-7-21-1-3-3-8-6-9 0-1-2-1-3-2 0-1 2-2 3-3l-1-4 1-1c0-3-1-6-2-9v-1z" class="I"></path><defs><linearGradient id="Y" x1="342.104" y1="256.602" x2="330.098" y2="259.768" xlink:href="#B"><stop offset="0" stop-color="#999798"></stop><stop offset="1" stop-color="#b8b7b7"></stop></linearGradient></defs><path fill="url(#Y)" d="M311 231c7 8 12 14 23 16 4 0 7 0 10-3l1 1c1 0 1-1 2 0 11 6 31 40 36 52 2 3 3 8 5 12l7 23c-3-1-5-9-7-12-3-8-7-14-12-21l-6-9c-2-2-4-5-7-7-2-4-5-7-9-9-1-1-1-1-2-1l-6-3c-4-2-6-5-10-7-4-3-8-4-11-7h0c1 0 3 1 4 1v-1l-1-1h0 3 1c0-1 1-1 0-2v-1l-2-2-2-1v-2h3c-3 0-6-1-9-3h0a30.44 30.44 0 0 1-8-8h0c-1-2-3-3-3-5z"></path><path d="M345 245c1 0 1-1 2 0 11 6 31 40 36 52-1 0-2 0-2-1-3-3-5-6-7-9l-6-7c-2-3-4-6-6-8-5-6-11-12-17-17-1-1-4-3-4-5 1-1 1-2 2-3l1-1 1-1z" class="C"></path></svg>