<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="100 106 853 816"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#1c1a1a}.C{fill:#322c2b}.D{fill:#f8ecea}.E{fill:#1a1110}.F{fill:#f1e6e7}.G{fill:#f8f4f3}.H{fill:#451919}.I{fill:#a61614}.J{fill:#363434}.K{fill:#524b49}.L{fill:#817e7b}.M{fill:#444342}.N{fill:#f1e1df}.O{fill:#3e3b3a}.P{fill:#797976}.Q{fill:#590c0c}.R{fill:#3c0a09}.S{fill:#0d0909}.T{fill:#ad2420}.U{fill:#3e1516}.V{fill:#911512}.W{fill:#6b1613}.X{fill:#757371}.Y{fill:#212020}.Z{fill:#80120f}.a{fill:#2a2322}.b{fill:#3f0c0a}.c{fill:#331616}.d{fill:#555251}.e{fill:#898986}.f{fill:#605d5c}.g{fill:#f5e3e2}.h{fill:#e03829}.i{fill:#646461}.j{fill:#871512}.k{fill:#75120f}.l{fill:#d82920}.m{fill:#931411}.n{fill:#510b0a}.o{fill:#a41a16}.p{fill:#ca1e19}.q{fill:#6a6967}.r{fill:#faf0ee}.s{fill:#541614}.t{fill:#f4dfdb}.u{fill:#710d0b}.v{fill:#f2d4d1}.w{fill:#941513}.x{fill:#c01d19}.y{fill:#cc241e}.z{fill:#5c0b0a}.AA{fill:#6d5c5a}.AB{fill:#898a86}.AC{fill:#291313}.AD{fill:#979894}.AE{fill:#f2d8d5}.AF{fill:#848480}.AG{fill:#615553}.AH{fill:#472827}.AI{fill:#1a1212}.AJ{fill:#281110}.AK{fill:#eec7c4}.AL{fill:#a4a5a1}.AM{fill:#cf2e25}.AN{fill:#b2b1b0}.AO{fill:#fff}.AP{fill:#040404}.AQ{fill:#9f1210}.AR{fill:#a09f9d}.AS{fill:#6e4e4d}.AT{fill:#690907}.AU{fill:#3d3635}.AV{fill:#674947}.AW{fill:#4e2f2f}.AX{fill:#7b605e}.AY{fill:#543a39}.AZ{fill:#c5c2c1}.Aa{fill:#abaca9}.Ab{fill:#ec5142}.Ac{fill:#dfafab}</style><path d="M645 641c2 1 4 2 6 2v1c-2 1-5 0-7 1l1-4z" class="T"></path><path d="M357 593c1 0 1 0 1 1l-3 3-1 1v1 2l-1 3v-5-2-3c2 0 2-1 4-1z" class="o"></path><path d="M183 335v10 4l-1 1c-1-2-1-7-1-9 2-2 1-3 2-6z" class="l"></path><path d="M367 646c-1 0-2 0-3-1-1-2 0-5 0-8 1 1 1 2 1 3 1 1 0 1 1 2s1 2 1 4z" class="T"></path><path d="M311 651c2 1 4 1 6 1v1c0 1 0 2-1 3-1 0-2 0-3-1s-2-2-2-4z" class="g"></path><path d="M311 651v-1c1-2 1-2 3-2l2 2 1 2c-2 0-4 0-6-1z" class="p"></path><path d="M160 240h1c1 1 2 1 3 2l-1 2c-1 1-2 0-3 0-1-1-2-1-2-3l2-1z" class="h"></path><path d="M347 639l1-2h0v3c1-1 0-2 1-3h0v4c0 3 0 7-1 10-1-4-1-8-1-12z" class="l"></path><path d="M473 238h0c-1-4 0-6 1-9h1c-1 2-1 4 0 7l1 1-1 2-2-1z" class="C"></path><path d="M402 709v-1c2 2 5 4 7 6h-1c-1 0-1-1-2-1s-1 0-2 1l-1-1v1 2 1c0-1-1-3-1-4v-4z" class="F"></path><path d="M356 608v2 5c1 1 1 1 1 2h-3l-1-8c1 0 2-1 3-1z" class="T"></path><path d="M837 378h0c1-3 5-6 7-8 0 2-1 3-1 5l-4 5h0v-1l-2-1z" class="h"></path><path d="M754 162c6-1 8 2 13 3l-2 2c-1-2-4-2-6-3-2 0-3-1-5-2z" class="i"></path><path d="M353 583l1 1v1c0 1 1 2 2 3h0l1 1c-1 1-1 1-1 3l1 1c-2 0-2 1-4 1v-7l-1-3 1-1z" class="V"></path><path d="M624 672c1 1 1 2 0 4l-6 8v-2c0-3 3-5 5-8 0-1 0-1 1-2z" class="T"></path><path d="M676 240c1 1 1 3 2 4l-3 5-3-1h0l-1-1c1 0 2-1 2-1 1 0 1-1 2-2l-2-2v-1l2 1c1-1 1-1 1-2z" class="G"></path><path d="M259 451h1c0 3 0 7-1 9h-1-1l-1-1-1 2v-1c-1-1-1-1 0-2h2v1c0-2 1-4 0-6h0l1-1 1-1z" class="F"></path><path d="M618 695c0 1 1 1 1 2-2 4-3 7-4 11l-1 1c-1-1-1-3-1-4v-1l1-4 2-2c0-1 1-2 2-3z" class="x"></path><path d="M161 374c0-1 0-2 2-3 1-2 7-6 8-6h2c-4 4-8 7-12 9zm619 159h0v-2l2-2h1v-2h3l-2 1 1 1v1 3c-1 0-1 1-1 1l-1 1c-1 0-1 0-2-1v-2h0l-1 1h0z" class="G"></path><path d="M583 272h4c1 0 3-1 4-2v-1h1l2 2c-2 1-4 3-6 3-1 1-3 1-4 2h0c0-2-1-3-1-4zm-83-49c1 1 1 1 0 2l-2 3-3 3v1c-3 1-4 2-6 3h0l1-1h1c0-1 1-2 1-2h-4l1-1c3-1 6-2 7-4l1-1 3-3z" class="B"></path><path d="M827 284h1c-2 5-5 8-8 12v-5c2-3 5-5 7-7z" class="Ab"></path><path d="M636 657l3 1c0 4 0 9-2 14-1-5-1-10-1-15z" class="h"></path><path d="M760 594c1 1 1 1 1 2-2 1-5 6-7 9-1 0-1 1-2 2 0-1-1-2-1-3l9-10z" class="l"></path><path d="M357 617l2 2c1 3 0 6 1 9-1 1-1 2-2 3l-1-1c2-5-2-8-3-13h3z" class="y"></path><path d="M567 167c6-2 14-3 21-3-1 0-3 1-3 2-4 1-15 3-18 1z" class="i"></path><path d="M200 345c0 1 1 1 1 2l1 1-1 1-1 1v2 1l-1 1 1 1c1 0 1 0 2 1h-1l-1 1c0 1 0 0-1 1l-1-1v-5-2l-1 1-2-2 5-4z" class="F"></path><path d="M797 122c1 1 2 3 3 4l1 3h0c1 1 0 1 1 2 0 0 2 2 1 3h-1v1h-1v-1c-1 0-1-1-1-2-1-1-3-2-3-4-1-2 0-3-1-5l-1-1h2z" class="D"></path><path d="M844 370c3-3 7-6 11-8h1c0 2-1 3-2 4l-11 9c0-2 1-3 1-5z" class="p"></path><path d="M207 432l1-1c0-1 0-1 1-2 0 0 1-2 1-3 1 1 1 2 2 4l2-1v1l3 3-1 1c-1 0-1-1-1-1v-1c-1 1-1 1-1 2h-3l-2 1h-1v-1c0-1-1-1-1-2zm481 255l6-8c1 2 2 2 2 4-1 2-3 4-5 6l-3 3v-2-1-2z" class="r"></path><path d="M423 722l17 2 1 2c-6 0-12-1-18-2v2c-1-1-1-3 0-4z" class="l"></path><path d="M641 645h2c-2 4-4 8-4 13l-3-1v-7h1c1 0 3-4 4-5z" class="AM"></path><path d="M768 531c1 0 2 0 2 2h0v4l1 2-1 1v2l1-2h2v1c0 1-2 2-3 3 0 1 0 1-1 2l-1-1v1l-1-6 1-9z" class="r"></path><path d="M447 251h1c1 1 2 1 3 1 2-1 3-5 7-6-1 4-2 7-5 10l-1 1c-1-2-3-3-4-4l-1-2z" class="AK"></path><path d="M166 229h0c1 0 2 0 3 1v1h1c-1 2-2 2-3 3-1-1-1-1-2-1v1c-2-1-3-1-4 0l-1 1-1-1h0c-1 0-1 0-1 1h-1c0-2 0-2 1-3h2l1-2c1 0 3-1 5-1z" class="r"></path><path d="M782 114h1v1c1 1 2 0 3 1 0 0-1 1-2 1s-4 2-5 3l-2 1c-1 1-2 1-3 2l-1-1c0-1 0 0 1-1 0-1 0-2 1-3 2-1 4-3 6-3l1-1z" class="g"></path><path d="M654 817v-3 1c1 2 1 3 1 4h0v-2c0-2 0-2 1-3v2 24h-1c-1-2-1-4-1-6v-17z" class="l"></path><path d="M361 161l-11-9 12 5h-2l5 3c0 1 0 3 1 4h0c-1 1-2 1-4 2 0-2 0-3-1-5z" class="O"></path><path d="M229 119h1c1 1 1 3 2 4 1 5 3 9 3 14l-1 3h-1l-2-4-1-2 1-1 3 5c0-2-1-4-1-6-2-4-5-9-4-13z" class="l"></path><path d="M367 646v6 15h0-1c-1 0-1-1-1-1l1-1 1-1c-1-1-1-2-2-2 0-1 0-1-1-2l-1 1-1-1 1-1 2-2c0-1-1-2-1-3l-1-1c1 0 1-1 2-2 0-1 1-1 1-1v-1-1c0-1 0-2 1-2z" class="g"></path><path d="M728 751c-1-2-2-3-2-5h0v3l-1-20v-3h1v1h2v24zM322 628c1 0 2 0 3 1v2c-1 1-1 1-2 1-1 1 0 4-2 5v-1s-1 0 0-1h0-1l-1 1v1l-1 1-1-1h0l-1-1c-1-1-1-1-1-3 1-1 2-1 3-1 1-1 1-1 1-2v-1c1-1 2 0 3-1z" class="G"></path><path d="M358 594l1 1v5h-1l-1 4c1 1 1 2 1 3h-1l-1 1c-1 0-2 1-3 1v-5l1-3v-2-1l1-1 3-3z" class="m"></path><path d="M358 594l1 1v5h-1l-1 4h0v-3-1-1l-2-2 3-3z" class="I"></path><path d="M531 231l1-1c1-2 2-5 1-8v-2h0c5 6 1 17 10 18h-1c-2 1-5-1-7-3l-1-1c-1 0-1-1-1-2l-2-1z" class="C"></path><path d="M816 408h0v-1c1-2 0-3 1-4 0-1 1-1 1-2 1 6 1 12 0 18s0 12-1 18c-1-2 0-5-1-7h0l1-1c0-4-2-8-1-11s0-7 0-10z" class="h"></path><path d="M223 510l15-1v5h2v1c1 1 1 3 2 3v-1l3 3 1-1 2 2-1 2v-1-1h-2v1h-1 0v-1l-1-1-1-1h-2v1c-2 0-3-1-4-2v-2c1-2 1-2 1-4h-1c1 0 1 0 2-1h-9c-1-1-4-1-6-1h0z" class="F"></path><path d="M842 148c6-5 13-13 21-15l-20 18-1-3z" class="h"></path><path d="M183 306v19 10c-1 3 0 4-2 6v-36c1 2 0 3 1 5 0 5-2 12 0 17v-5-7c1-2 0-6 0-8l1-1z" class="x"></path><path d="M423 730c0 3 1 26-1 28l-1 1c-4-4-3-16-3-22h0c1 2 1 3 1 5v7c1 2 1 2 3 3v-3-4c1-5-1-10 0-15h1z" class="I"></path><path d="M829 158v-8h-1v-5l1 2h0l1 1v9c5-2 8-5 12-9l1 3c-4 3-8 6-11 10l-3-3z" class="l"></path><path d="M879 123c1 0 0 0 1 1l-1 2-2 2-5 5c0 2-2 3-3 4 0 2-1 2-3 4v-1l1-1 1-2v-2l-1-1v-1h-1v-1c1 0 1-1 2-1l2-2h2c0-1 1-1 2-1s2-1 3-2 1-2 2-3zM192 349v-1-1-1h1 1v-1l-1-1s0-1-1-1v-1c1 0 1-1 2-1v-5c1-1 2 0 3 0l1 2h0l2 1 1 1c1 0 3 0 4-1 0 2-3 3-5 4h-1c-1 0-2 1-2 2-2 2-3 3-5 4z" class="G"></path><path d="M233 507h0c2 0 4 1 6 0v-1c1-1 2-1 4-1-1-3-7-7-9-9-1 0-1-1-2-1v-2c1-1 1-1 2-1v1c2 0 2 0 3 1l-1 1 3 3c2 2 3 4 5 6h1l1 1h3c1 1 1 1 2 3l-18-1z" class="v"></path><path d="M219 508l14-1 18 1h1 3c2 0 4 0 5-1v2h-2-4-6-10l-15 1-3-1-1-1z" class="h"></path><path d="M557 245l1-1c2-3 2-5 2-9 1 1 2 2 3 4 1 3 1 7 1 10 0 2 3 6 5 7l1 1c-1 1-1 2-2 4l-1-2c-1-3-2-5-3-7-1-1-3-2-3-3-1-2-1-3-4-4z" class="B"></path><path d="M137 284c-1-1-1-2-1-4 0 0-1-1-1-2 2-1 4-2 5-2l1-1c1 1 2 2 3 2-1 1-1 1-1 2-2 0-2 0-3 1l1 1v1c1 2 1 4 1 6h0c1 1 1 1 1 2l-1-1c-1 1-1 2-2 2h0v-2h-1 0-1c-2-1-1-2-1-5h0z" class="D"></path><path d="M137 284l1-1v-1c1 1 1 1 1 2l2 2c0 2 0 2-1 3h-1 0-1c-2-1-1-2-1-5h0z" class="G"></path><path d="M817 153h2c0-2 0-3 1-5 4-5 4-12 7-18 1 7-2 16-5 22l-2 7-3-1h0c0-1 0-1 1-2 0-1-1-2-1-3zM196 312h-1c-1 1-1 1-2 1v-1-1h0-2c-1 0-2-1-2-2v-1l1-1c1 1 1 2 2 2h1v-2h-1-2v-1h-1l1-1h1l-1-1c0-1 1-3 0-4h0v-1c2 1 1 1 2 2 1 0 1 0 2 1h1 0 1v2h1l1-1 1 1v2c-1 1-1 1 0 2l1-1v1l-1 2c-1 1-2 2-3 2h0z" class="N"></path><path d="M888 209h2v-2c-2 0-2 0-3-1 0-1 0-1 1-2h1c-1-1-1-1-1-2h1v-2h1 1c1 1 1 1 2 1h1l1 1v2c1 1 3 1 4 1l-8 8h-1l-1-1h0l-1 1v-1h0c1-1 1-1 1-2l-1-1z" class="D"></path><path d="M144 277c2 1 2 1 3 2s1 1 2 1l2 2-1 1c-1 0-2 0-2 1v2h2l1 1-1 1-1-1c-1 0-1 0-2 1 0 1 0 1 1 2l-1 1h-1v-1l-1 1-1 1-1-2c0-1 0-1-1-2h0c0-2 0-4-1-6v-1l-1-1c1-1 1-1 3-1 0-1 0-1 1-2z" class="r"></path><path d="M688 687v2 1 2l-2 2-1 1-2 3c-1 2-3 3-4 5l-1 1h-1c-1 1-1 2-2 2l-1 1v1h0c-1-1-1 0-1-1-2 0-2 1-3 1 5-7 11-14 18-21zm158-524c2-1 2-2 3-3 1-2 2-2 3-3 0-2 2-2 3-4 0-1 2-2 3-4 0-1 1-2 2-2 1-2 2-2 3-3l1-2v1c0 1-1 2-2 3l-1 2-3 3c-1 1 1-1-1 1 0 1-1 1-1 2l-1 1v2h0c-1 1-1 1-1 2 1 1 1 0 2 2 1 0 1-1 2 1 1 0 1 1 2 2h-13l-1-1z" class="G"></path><path d="M368 861v1c1 3 1 5 1 8v3h1c0 2 0 4 1 5v-3c0-2 0-3 1-4v13c0 2 1 5 0 7l-2 2h-1c-2-3-1-27-1-32z" class="y"></path><path d="M622 689l3 3c0 1 1 1 1 2l4 5 3 4 2 3h0-1l-1-1-7-4-6-3v-1l-1-1c1-1 1-2 2-4 0-1 0-2 1-3z" class="G"></path><path d="M200 345c2-2 4-3 7-5v5 4 1c0 2 1 5 0 7-2 0-3-1-5-1-1-1-1-1-2-1l-1-1 1-1v-1-2l1-1 1-1-1-1c0-1-1-1-1-2z" class="N"></path><path d="M202 348h2v-1c1 0 2 0 3 1-1 1-1 1-2 1v1 2h-1s-1 0-1 1h-1-2v-1-2l1-1 1-1z" class="G"></path><path d="M207 350c0 2 1 5 0 7-2 0-3-1-5-1-1-1-1-1-2-1l-1-1 1-1h2 1c0-1 1-1 1-1h1v-2h2z" class="D"></path><path d="M207 432v-1-1c-2-2 0-2 0-4h-1c0 1-1 1-2 1v-2l1-1c-2 0-2-1-4 0l-2-1h0c0-2 1-2 2-3h-1c0-1 0-2 1-3h2v1h0v1c1-1 0-1 1-2h0v-1c-1-1-1-1-1-2h2l1 1-1 1 2-1c1 1 0 1 1 2h1c-1 1-1 3-1 5h1 0 1c1 2 0 0 0 2l1 1-1 1c0 1-1 3-1 3-1 1-1 1-1 2l-1 1z" class="G"></path><path d="M721 640c0-1 1-2 1-3 1-1 1 0 1-1l1-1c-1 0-1-1-1-2 1 0 2 1 3 1-1 2-1 2 0 3h0l-21 28h0v-1l-1-1c0-3 6-9 7-11l9-11 1-1z" class="Ab"></path><path d="M266 649l-2 43h-1l-1-43h4z" class="h"></path><path d="M256 165c-2-2-4-4-6-5-6-4-11-7-16-12v-6h1c2 1 1 3 2 5 1 1 2 1 3 2 3 3 8 5 12 8 2 3 4 5 6 8h-2z" class="p"></path><path d="M199 310l1 1h1l2 1h2v-8c2-2 1-9 1-12v-1l1 9c-1 5 0 9-1 14v8c-1-1-2-1-3-2v2c-1 0-1 1-2 1l-1-1 1-2h-2c-1-1 0-2-1-3h-1-1-1c0-1 0-2 1-3v-1-1h0c1 0 2-1 3-2z" class="F"></path><path d="M167 80c3 1 14 13 17 16 3 2 5 3 7 5 5 3 9 9 9 15 1 1 1 2 1 3l2 1c1 0 1 0 1 2h-1l-7-2 3-3c-4-11-12-16-20-24-4-4-8-8-12-13z" class="h"></path><path d="M615 675c1 0 1 0 3-1h5c-2 3-5 5-5 8v2 2c1 2 2 2 4 3-1 1-1 2-1 3-1 2-1 3-2 4l1 1v1l-1-1c0-1-1-1-1-2-1 1-2 2-2 3l-2 2v-1c1-1 1-1 1-2 1-2 1-3-1-5 0 0 0-1-1-2v-5h0 1c0-1 0-2 1-3l1-2c1-1 1-2 1-4l-1 1h-3v-1h0l2-1z" class="w"></path><path d="M618 695h0c-2-3-2-6 0-9 1 2 2 2 4 3-1 1-1 2-1 3-1 2-1 3-2 4l1 1v1l-1-1c0-1-1-1-1-2z" class="N"></path><path d="M672 248l3 1c-9 13-22 23-32 35l-6 7c1-3 1-6 1-10 1-1 1-3 0-5 3 0 5 0 6-2l1-1 1 1h0l-1 1-2 2c-1 0-1 1-2 1h-1v1c0 1 1 2 2 3l6-6h1c1-1 3-2 4-4 2-3 5-5 8-8l-1-1c2-3 5-4 8-7 0-1 1-2 2-3h1c0-1 0-2 1-2 1-2 0-2 0-3z" class="F"></path><path d="M402 713c0 1 1 3 1 4h1c2 3 4 4 7 6l3 3v5 13-1 2 4 1c0 2 1 3 1 5h-1v-3c0-2-1-4-1-6s0-3-1-5l-1-1s0-10-1-12c-2-2-5-4-7-6l-1 1v3c-1-3 0-6-1-10 1-1 1-2 1-3z" class="p"></path><path d="M177 207c1 1 2 1 3 2v2 1c1-1 1-2 1-3l1-1 2 2h2c-2 6-1 13-2 19-1 1-2 2-4 2v-3c0-7-1-14-3-21z" class="s"></path><path d="M356 610c2 1 3 2 5 2l2 3h1c0-1 1-2 1-3h1v1 2h0c0 3 0 6 1 9l-1 1v3l-2 2-1-2h-3c-1-3 0-6-1-9l-2-2c0-1 0-1-1-2v-5z" class="o"></path><path d="M359 619v-3l2 2c1 2 2 8 2 10h-3c-1-3 0-6-1-9z" class="F"></path><path d="M616 677l1-1c0 2 0 3-1 4l-1 2c-1 1-1 2-1 3h-1 0v5c1 1 1 2 1 2 2 2 2 3 1 5 0 1 0 1-1 2v1l-1 4h-1v-3h-2c-1-3-1-4 0-7 1-1 0-2 0-3-1 0-1-1-2-1 0-1-1-2-1-3l1-1 2-2v-1-1h-1l-1-1v-2l5-2h3z" class="o"></path><path d="M616 677l-3 3h-2c0 1-1 2-1 2h-1l-1-1v-2l5-2h3z" class="V"></path><path d="M614 692c2 2 2 3 1 5 0 1 0 1-1 2v1l-1 4h-1v-3c0-3 1-6 2-9z" class="W"></path><path d="M252 473v-3c-1 0 0 0-1 1v1l-1-1v-2h-1v2h-1c-2-1-1-1-2-2v1h-2c-1-2-1-2-1-3-1-2-2-1-4-2 0-1 0-2 1-2 1-1 1-1 2-1 1-1 0-1 1-1 2 0 2 0 3 1v-3h2c1 1 0 1 1 1l1-2 1-1 1 1c0 1 0 1-1 2l-1 1v1 1h-1c2 0 3 0 5 1l1-1v2h-1v2l1 1c1 1 2 3 2 4h-1c-1 0-2 0-4 1h0z" class="D"></path><path d="M870 388h1l1 1h1 0c4-2 4 0 7 0 1 0 1 0 2 1l-1 1s-1 1-1 2l-1-1v-1h-1l-1 2h1v2c-1 1-1 0-2 1v1l1-1 1 2-1 1c-1 0-1-1-2-1l-1 1h-4c-1 1-1 1-2 1h0l-1 1c-1 0-1 0-3-1v-3c1-2 1-2 2-3 0-1 0-2 1-3h0c1-2 2-2 3-3z" class="r"></path><path d="M603 700l3 2 2 21v16c0 5 1 22-1 25-2-5-1-12-1-17l-1-22c0-8 0-16-2-25z" class="o"></path><path d="M803 164c-2 1-3 1-5 2l2 1-7 3c-2 0-3 1-4 1l-2-2h-3l-5 2c-1 0-2 1-2 1-2 0-3 1-4 1h-2c-2 1-2 1-3 0l7-2h2c1-1 2-1 2-1 0-1 0-1-1-1h-3l-3-2h-2v-2h-2c9-2 18 1 27 0h1l7-1z" class="X"></path><path d="M787 169l11-3 2 1-7 3c-2 0-3 1-4 1l-2-2z" class="AN"></path><defs><linearGradient id="A" x1="360.388" y1="163.162" x2="350.073" y2="164.545" xlink:href="#B"><stop offset="0" stop-color="#54524f"></stop><stop offset="1" stop-color="#757471"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M326 164l2-2h0 3l4-1v-1h2 0c3-1 7 1 10 2 3 2 11 0 14-1 1 2 1 3 1 5h-3-4-14c-3-1-7 1-10-1h-1l-1-1h-3z"></path><path d="M329 164l4-1c2-1 2 0 4 0 1 1 2 1 4 1v-1c3 0 5 1 8 2 3 0 8-1 10 1h-4-14c-3-1-7 1-10-1h-1l-1-1z" class="K"></path><path d="M255 461l1-2 1 1h1 3v3 10l-1 9-1-1h-1v2h-1c0-1-1-1-1-2h0c-1-1-1-1-2-1l-1 1c-1 0-1 0-2-1l2-1-1-1h-1c1-2 1-3 1-5 2-1 3-1 4-1h1c0-1-1-3-2-4l-1-1v-2h1v-2-1l-1 1-2-1 1-1 2-1v1z" class="t"></path><path d="M255 461l1-2 1 1h1 3v3 10h-1c0-1 0-3-1-4 0-1-1-2-1-4v-1h0c-3 0-1 0-2-2l-1-1z" class="v"></path><path d="M704 663l1 1v1h0c-3 5-7 10-11 14l-6 8c-7 7-13 14-18 21-2 1-2 2-3 4v1c-1 1-1 0-2 1h0l-4 4c-2 2-3 3-4 5v-1l-1-1 48-58zM243 165c-7-3-10-11-16-15-4-2-8-3-12-5-1-1-1-1-1-3 3 2 6 3 9 3-3-4-7-9-11-13-3-3-5-5-7-8h1c1 0 2 0 3 1 2 2 4 5 6 7 9 11 18 23 29 32l-1 1z" class="h"></path><path d="M827 130l1-11c3 10 2 19 2 29l-1-1h0l-1-2v5h1v8l-2-1h0c-1 2-1 3-1 4-1 1-1 1-1 2-2 0-2 2-3 2h-1c1-1 1-2 1-3h0v-1c0-1 0-2 1-3v-1l-1-1c0-1 1-2 1-4h-1c3-6 6-15 5-22z" class="AU"></path><path d="M823 152c1-1 1-2 3-3v4 4h1c-1 2-1 3-1 4-1 1-1 1-1 2-2 0-2 2-3 2h-1c1-1 1-2 1-3h0v-1c0-1 0-2 1-3v-1l-1-1c0-1 1-2 1-4z" class="d"></path><path d="M839 380c-1 2-2 4-2 6h3l2-1v-1h0c-1 1-1 1-2 1 2-3 6-5 9-7l2-1c-3 5-7 10-12 12-2-1-3-2-4-1-4 1-8 5-11 8-2 1-4 3-6 4v1c0 1-1 1-1 2-1 1 0 2-1 4v1h0-1v-1-2c0-1 1-2 1-2l-1-1c0-1-1-1-1-2l2-3v-5-9h0c1 2 1 3 1 5v7c1-1 1-1 1-2 0-3-1-7 0-10l1 12c4-3 8-6 13-8l3-3-1-1h-1l-1-1c2 0 2 0 4 1 1-2 1-3 1-5l2 1v1h0z" class="l"></path><path d="M822 152h1c0 2-1 3-1 4l1 1v1c-1 1-1 2-1 3v1h0c0 1 0 2-1 3h1c1 0 1-2 3-2-1 2-2 3-2 5l2-1h0c-2 2-3 2-5 3s-4 2-6 4h0c0 1 0 1-1 1-1 1-1 1-2 1-2 1-4 1-5 1l-1 1c0-4 3-7 6-10h-1l-3 3c0-2 2-4 2-6 1 0 2 0 3 1h0l3-2 5-5 2-7z" class="P"></path><path d="M819 165l3-3h0c0 1 0 2-1 3h1c1 0 1-2 3-2-1 2-2 3-2 5h0l-2 1c-2 0-4 1-6 2l4-6h0z" class="K"></path><path d="M822 152h1c0 2-1 3-1 4l1 1v1c-1 1-1 2-1 3v1l-3 3-1-1-12 13h0l-1 1c0-4 3-7 6-10h-1l-3 3c0-2 2-4 2-6 1 0 2 0 3 1h0l3-2 5-5 2-7z" class="J"></path><path d="M822 156l1 1v1c-1 1-1 2-1 3v1l-3 3-1-1c0-2 3-6 4-8z" class="i"></path><path d="M788 116l-1-1c0-1 1-1 2-1 2-1 2-2 3-1 1 0 2 1 2 2l9 15c1 2 3 5 4 7 1 1 2 2 2 3 1 2 2 4 3 5s1 2 2 2v1c1 2 2 3 3 5 0 1 1 2 1 3-1 1-1 1-1 2h0l3 1-5 5-3 2h0c-1-1-2-1-3-1-3 0-6 1-9 2l-2-1c2-1 3-1 5-2 4-3 9-2 13-7 0-1 0-2-1-3v-1l-3-5-2-3-1-3-2-3-4-5c1-1-1-3-1-3-1-1 0-1-1-2h0l-1-3c-1-1-2-3-3-4h-2c-1-1-3-3-5-4l-2-2z" class="AZ"></path><path d="M788 116c1-1 2-1 3-1h1l1 1c1 1 1 2 2 3s1 2 2 3h-2c-1-1-3-3-5-4l-2-2z" class="G"></path><path d="M768 583v2l63-75v1c-5 7-11 14-17 21l-31 37-12 16c-1 1-3 3-3 4-1 1 0 4 0 6-1 2-1 5-1 7h0v-12h-1c0-3-1-8 0-10 1-1 1-2 1-3h1v6z" class="h"></path><path d="M200 339l4-2v-1h1l1-1h1 0c1 0 2 2 3 3l1-1h1c1 1 1 2 2 2 1 1 2 1 2 2l-1 1-1-1h0c-1 1-2 1-2 2v1 4l-2-2c-1-1-2-1-3-1v-5c-3 2-5 3-7 5l-5 4-25 20c-5 5-11 8-15 13 0 3 1 13 0 15l-2-16c2-2 7-5 8-7 4-2 8-5 12-9 1 0 3-2 4-3l15-13c2-1 3-2 5-4 0-1 1-2 2-2h1c2-1 5-2 5-4-1 1-3 1-4 1l-1-1z" class="j"></path><path d="M183 156c0-2-1-4 0-5 1-2 1-2 1-3 1-4 0-8 0-11 0-7 0-13 2-19 1 3 0 6 1 9 0 10 4 22 9 30l-1 1 4 4c0 2 0 2-1 2l-8-10 2 6h-3c-1-3-2-9-4-10h0c-1 1-1 2-1 3v1 2h0-1z" class="E"></path><path d="M190 154c-2-4-3-8-3-12-1-4-2-8-1-12v2c1 3 1 6 2 9 2 6 4 12 7 17l4 4c0 2 0 2-1 2l-8-10z" class="AD"></path><defs><linearGradient id="C" x1="622.917" y1="284.155" x2="595.411" y2="286.767" xlink:href="#B"><stop offset="0" stop-color="#121111"></stop><stop offset="1" stop-color="#3b3535"></stop></linearGradient></defs><path fill="url(#C)" d="M617 266c2 1 2 1 3 3h2c0 2 1 3 1 4 0 2 1 3 0 4v-1h0c0 1-1 2-1 2v1c1 2 0 5 0 7-1 2-3 5-3 6l1 1v1h-1l-2 5h1v1l-1-1c-6-1-9 2-14 4-2 1-4 2-6 2-1-1 0-2 0-3h0c6-1 11-5 15-9 2-3 6-8 6-13v-1c2-3 0-9-1-13z"></path><path d="M619 292l1 1v1h-1l-2 5h1v1l-1-1c-6-1-9 2-14 4 1-2 2-2 4-3h0l1-1c3-3 7-3 11-7z" class="J"></path><path d="M261 165c4-2 7-2 12-2-6-5-10-10-14-16-2-2-4-4-4-6 1 0 2 2 3 3l7 9c3 4 6 8 10 11h1v3c-3 2-5 2-8 2l-5 1c-1 0-3-1-4 0h-19c-2-1-5-1-8-2 2-1 5 1 7 0l-1-1c-1 0-2 0-3-1h0c1 0 7 0 8-1l1-1c3 2 8 2 12 1h2 3z" class="q"></path><path d="M244 164c3 2 8 2 12 1h2 3 0l-1 1c-3 0-4 2-7 1h-3l-1 1c1 1 5 1 7 1 4-1 7-1 11-1l1 1-5 1c-1 0-3-1-4 0h-19c-2-1-5-1-8-2 2-1 5 1 7 0l-1-1c-1 0-2 0-3-1h0c1 0 7 0 8-1l1-1z" class="d"></path><path d="M597 694c2 0 2-1 4-2 2-2 4-2 7-2 1 0 1 1 2 1 0 1 1 2 0 3-1 3-1 4 0 7h2v3h1v1c0 1 0 3 1 4v7h-3 0v5l-1-1v-6l-2 9-2-21-3-2c-1-1-3-2-5-3l-3 3v-2c0-2 1-3 2-4z" class="s"></path><path d="M610 701h2v3h1v1c0 2-1 4-2 6-1-3-1-6-1-10z" class="j"></path><path d="M600 696c1 0 4-1 4 0 1 1 2 4 2 6l-3-2c-1-1-3-2-5-3l2-1z" class="m"></path><path d="M611 711c1-2 2-4 2-6 0 1 0 3 1 4v7h-3 0v-5z" class="I"></path><path d="M597 694c2 0 2-1 4-2 2-2 4-2 7-2 1 0 1 1 2 1v2h-1c-1 0-2 0-3 1h-3c-2 0-2 1-3 2l-2 1-3 3v-2c0-2 1-3 2-4z" class="H"></path><path d="M254 492s0-1-1-1v1l-1-1c-1-1 0-1 0-2l-1-1h0l-1 1h-1c-1 0-2 1-3 1h1 1c1 0 1 0 2 1h1l1 2h-1-2-1-1 0l-1 1h-1c-1-2-1-2-1-3l-1 1h-1l1 1v2 1l2 2-1 2c-1 0-2 0-2-1-1-1-1-4-2-6h0l1-2-2-3h1l1-1v-1l1-1h-1c1-2 1-2 1-4l2-2h1l-1-1s-1-1-2-1v-1l2-2 1 1c0-2 0-2 1-3l1-1 1 1h1 1c1 0 1 1 2 1h0c0 2 0 3-1 5h1l1 1-2 1-1 1h1c1 1 0 1 1 2 1 2 2 1 3 2 0 0 1 2 1 3h-1v-1l-3 3 1 1c1 0 1-1 2-2v1c0 1 0 1-1 2z" class="N"></path><path d="M344 575l1-1v-2c1 1 2 2 2 3s0 2 1 2v1c1 1 1 1 1 2h1 1c1 1 1 2 2 3l-1 1-1 1v1l-1 14-1 41v-4h0c-1 1 0 2-1 3v-3h0l-1 2-1-41v-19-2l-2-2z" class="I"></path><path d="M350 600c-1-2 0-5 0-7h-1 0c-1-1-1-2-1-3v-1c1-2 2-2 3-3l-1 14z" class="o"></path><path d="M751 162h3c2 1 3 2 5 2 2 1 5 1 6 3l2-2h1 2v2h2l3 2-11 1c-2 0-4-1-6-1l-5-1h0l-8-2h-7c-1 0-2 1-3 1h-1-2l-5 1c-2-1-4 0-6-1h-5l2-1v-1-1l33-2z" class="q"></path><path d="M738 166c2-2 5-2 7-3l8 3h-5-3-7zm13-4h3c2 1 3 2 5 2 2 1 5 1 6 3h-4c-2 0-4-1-6-2-2 0-3-1-4-3z" class="M"></path><path d="M223 510c-1 0-6 1-7 0s-4-5-5-6l-16-19c-2-2-4-6-7-8 2 5 5 10 7 16-3-4-5-8-7-12-2-5-6-11-10-16a57.31 57.31 0 0 0-11-11c-2-2-5-4-7-6 2 0 6 3 8 5-4-7-9-16-15-21-3-2-6-4-9-5-2-1-4-3-6-4-3-1-5-4-7-6s-4-3-5-6c4 4 9 8 13 11 5 4 12 6 17 11 5 6 8 13 13 19 3 4 7 7 11 11 2 4 4 8 7 11s6 6 8 9l12 13c3 3 7 9 11 11l1 1h0l1 1 3 1h0z" class="l"></path><path d="M251 480c1 1 1 1 2 1l1-1c1 0 1 0 2 1h0c0 1 1 1 1 2h1v-2h1l1 1 1 9-1 16c-1 1-3 1-5 1h-3-1c-1-2-1-2-2-3h-3l-1-1h2l2-1c1-2-1 0 1-1v-1-2h-1l-1-2c-1 0-1-1-1-2l1-1c1-1 1-1 2 0v1h1c0-1 1-2 2-2h1v-1c1-1 1-1 1-2v-1c-1 1-1 2-2 2l-1-1 3-3v1h1c0-1-1-3-1-3-1-1-2 0-3-2-1-1 0-1-1-2h-1l1-1z" class="AE"></path><path d="M245 504h2l2-1c1-2-1 0 1-1v-1-2h-1l-1-2c-1 0-1-1-1-2l1-1c1-1 1-1 2 0v1c-1 2-1 2 0 3h1l1 1-1 2h0 1c1 0 3-1 4 0h1c0-1 1-1 1-2h1c0 1 1 1 1 3-1 0-2 1-3 1 0 1 0 1 1 2h-1l-1-1c-1 1 0 1-1 2h-1l-1-1-1 1 1 1c-1 0-1 0-1 1h-1c-1-2-1-2-2-3h-3l-1-1z" class="G"></path><path d="M180 228v3c2 0 3-1 4-2l-1 77-1 1c0 2 1 6 0 8v7 5c-2-5 0-12 0-17-1-2 0-3-1-5l-1-77z" class="m"></path><path d="M815 402l1 1s-1 1-1 2v2 1h1c0 3 1 7 0 10s1 7 1 11l-1 1h0c0-1-1-2-1-3l1-1c0-1-4-4-4-4h-1v1h0c1 1 1 2 2 3l-1 2c-1 1-1 2-1 2l-1 1v-1l-1 1h-2-2 1l1 2c-1 1-1 0-2 2 1 2 2 1 1 4h0v-1c-2 0-1 0-2-1v-1-1l-1 1v1l-1-1-2 1h-1l1-1 1-1v-1l-1-1v-1h-2l1-1h1c1 1 1 1 2 1s1-1 2-1c2-1 3-2 4-3 0-1 1-2 0-3v-1c1 0 1 0 2 1l1-1c-1-1-3-1-4-2 1-1 1-1 1-2l-1-1v-2l-4 2c-1 0-1 0-3-1l1-1h0v-2-1-1l-1-1h1l3-3h0c4-2 8-4 11-7z" class="r"></path><path d="M411 740l1 1c1 2 1 3 1 5s1 4 1 6v3h1c0-2-1-3-1-5v-1-4-2 1c1 7 2 15 4 22 1 7 4 13 5 20h0c1-1 0-4 0-5 0-2-2-5-1-7 1 2 1 3 2 5 1 5 3 12 2 17 0 1 0 1-1 1v1c-2 3-5 6-8 9l-29 32c-4 5-9 9-12 14-1-1-1-2-1-3l15-15c3-2 6-6 9-9 6-7 12-13 17-21 1-1 3-4 3-6l2-5c-1-2-1-3-1-5v-1c1 2 1 3 2 4v-1l-1-6c-5-15-8-29-10-45z" class="h"></path><path d="M207 345c1 0 2 0 3 1l2 2v1 4h1c1-1 2-1 3-3 2 0 2 0 4 1 0 2 0 2-1 3l1 1 1-1 2 1-3 3v1c-1 1-2 2-2 3-2 1-6 5-6 7v3 1 2 8c-1 2-1 2-3 3l-2-2c0-1 1-2 0-3v-9l-10 11h-1c1-3 7-7 9-10 0-1 2-2 2-2v-5-8-1c1-2 0-5 0-7v-1-4z" class="x"></path><path d="M207 345c1 0 2 0 3 1l2 2v1c-1 0-1 0-2 1 0 0 0 1-1 1-2 3-1 9-1 12 0-2 0-3-1-5v-1c1-2 0-5 0-7v-1-4z" class="I"></path><path d="M213 353c1-1 2-1 3-3 2 0 2 0 4 1 0 2 0 2-1 3l1 1 1-1 2 1-3 3-6 6-2 3v-14h1z" class="F"></path><path d="M213 353c1-1 2-1 3-3 2 0 2 0 4 1 0 2 0 2-1 3l1 1c-1 2-2 3-4 4v-3h-3v-1c1-1 1-1 2-1l-2-1z" class="AO"></path><path d="M719 169c-2-1-5-1-7-1l-6-3 10 2h5c2 1 4 0 6 1l5-1h2 1c1 0 2-1 3-1h7l8 2h0l5 1c2 0 4 1 6 1l11-1h3c1 0 1 0 1 1 0 0-1 0-2 1h-2l-7 2-3 1-3 1c-3 0-6 0-8 1h-2v1h-1-1c-2 0-4-1-6-2h-1-1l1-1h1-3c-2-1-5-1-6-2-2-1-3 0-5-1h-2c-1-1-2-1-3-1h-4c-1-1-2-1-2-1z" class="AF"></path><path d="M758 169c2 0 4 1 6 1-1 1-4 1-7 1 0 1-1 1-2 1l-2-1c2 0 3-1 4-2h1z" class="L"></path><path d="M738 166h7l8 2h0c-2 1-9 3-11 2h-1 0c-2-1-2-1-3-1-2-1-4 0-6 0v-1h-5l5-1h2 1c1 0 2-1 3-1z" class="X"></path><path d="M366 615h1c1 1 1 1 1 2s1 2 1 3c0 3 0 6 1 10v1l1-1c0 2-1 7 0 9v7l1-4 1-1v7l-1 4v4c-2 2-1 4-2 6v1c-1 2-1 4-2 7 1 1 1 1 1 2 0 2 1 3 0 5l1 1c0 1 0 3-1 4 0 2-1 4 0 6v9c-1 3 0 7 0 10h-1v-8-18c-7-7-14-14-22-19-5-4-11-8-16-12v-1c6 4 14 9 20 14l18 15c0-4 0-8-2-11h1 0v-15-6c0-2 0-3-1-4s0-1-1-2c0-1 0-2-1-3 1-2 0-4 0-7l2-2v-3l1-1c-1-3-1-6-1-9z" class="V"></path><path d="M371 646l1-4 1-1v7l-1 4v4c-2 2-1 4-2 6 0-6 0-11 1-16z" class="Z"></path><path d="M366 615h1c1 1 1 1 1 2s1 2 1 3c0 3 0 6 1 10v1c0 3 1 10 0 12-2-2-1-11-2-13 0-2 0-4-1-6-1-3-1-6-1-9z" class="k"></path><path d="M356 580l1-1 3 1 2 1c1 1 1 3 3 3l2 3c1 1 1 2 2 3v1c0 2 1 4 1 6h-1-1c-1 1-1 1-1 2 1 2 1 3 1 5l1 4-1-1v1 4c-1 0-1 1-2 1v-1h-1c0 1-1 2-1 3h-1l-2-3c-2 0-3-1-5-2v-2l1-1h1c0-1 0-2-1-3l1-4h1v-5l-1-1c0-1 0-1-1-1l-1-1c0-2 0-2 1-3l1-1-1-1v-1l1-1s1 0 1-1l1-1-4-3z" class="o"></path><path d="M358 588h1l1 1c-2 1-1 4-1 6l-1-1c0-1 0-1-1-1l-1-1c0-2 0-2 1-3l1-1z" class="Z"></path><path d="M357 604l1-4h1c0 2-1 5 1 7h0 2l1 2v-3-1c1-1 2-2 2-4l1 1c1 0 1 1 2 2l1 4-1-1v1 4c-1 0-1 1-2 1v-1h-1c0 1-1 2-1 3h-1l-2-3c-2 0-3-1-5-2v-2l1-1h1c0-1 0-2-1-3z" class="j"></path><path d="M366 602c1 0 1 1 2 2l1 4-1-1v1 4c-1 0-1 1-2 1v-1c0-3-1-6 0-9v-1z" class="k"></path><path d="M356 580l1-1 3 1 2 1c1 1 1 3 3 3l2 3c1 1 1 2 2 3v1c0 2 1 4 1 6h-1-1c-1 1-1 1-1 2 1 2 1 3 1 5-1-1-1-2-2-2l-1-1v-5h-1l-1-2-1-1c0-2-1-3-2-4l-1-1h-1l-1-1v-1l1-1s1 0 1-1l1-1-4-3z" class="u"></path><path d="M356 580l1-1 3 1 2 1c1 1 1 3 3 3l2 3c1 1 1 2 2 3v1c0 2 1 4 1 6h-1c-1-1-1-2-2-3l-7-11-4-3z" class="I"></path><path d="M365 584l2 3c1 1 1 2 2 3v1c0 2 1 4 1 6h-1c-1-1-1-2-2-3 0-3-1-4-2-6v-4z" class="V"></path><path d="M835 460c-1 1-1 2-2 2l-1-1c1-1 1-1 1-2 1-1 1-1 1-2h-3l-1 1-2-1-1 2c-2-1-3-3-4-4-1-2-2-2-2-4 1-2 2-3 3-4l-2-2h0v-1c1-2 3-3 4-3s2 1 3 0c2-1 4 0 6-1l1 1v1c0 1 0 1-1 2l-1 1v1h1l3-3 3 3 3 4-3 4c1 0 2-1 3-1v2c-1 1-2 4-4 5l-1-1-4 1z" class="r"></path><path d="M835 460l1-2h1c1 0 1 0 2 1l-4 1z" class="G"></path><path d="M665 164c6-2 11-3 17-5s11-5 17-9c-4 5-7 8-12 11 3 1 6 2 9 2 7 1 15 1 22 2v1l-2 1-10-2 6 3c2 0 5 0 7 1-6 0-10-1-15-2-3-2-6-1-9-1h-4c-4 1-9 1-13 2l-2 1-1-1h-2-1c-3 1-6 1-9 1h-3c-2 0-4 0-6-1 3-3 7-2 10-3h1v-1z" class="AG"></path><path d="M665 165l2-1c7 0 12-5 18-5h0l-4 2c-3 1-8 3-11 5h0c-2 1-3 2-5 2-1 0-1 0-2 1h-3c-2 0-4 0-6-1 3-3 7-2 10-3h1z" class="AB"></path><defs><linearGradient id="D" x1="685.629" y1="162.128" x2="669.871" y2="170.872" xlink:href="#B"><stop offset="0" stop-color="#656562"></stop><stop offset="1" stop-color="#939191"></stop></linearGradient></defs><path fill="url(#D)" d="M681 161l1 1s1 1 1 2h3c1 1 3 0 4 0 1 1 1 1 1 2-4 1-9 1-13 2l-2 1-1-1h-2-1c-3 1-6 1-9 1 1-1 1-1 2-1 2 0 3-1 5-2h0c3-2 8-4 11-5z"></path><path d="M681 161l1 1s1 1 1 2h3c-2 0-5 0-7 1h-1c-3-1-6 0-8 1h0c3-2 8-4 11-5z" class="i"></path><path d="M784 169h3l2 2c1 0 2-1 4-1 2 1 3 1 6 1l2-1-9 8c-1 0-2 1-3 2-5 2-9 2-15 2-3-1-8-1-12-1-2 0-4-1-6-1l-13-3 1-2c2 1 4 2 6 2h1 1v-1h2c2-1 5-1 8-1l3-1 3-1c1 1 1 1 3 0h2c1 0 2-1 4-1 0 0 1-1 2-1l5-2z" class="AB"></path><path d="M784 169h3l2 2c1 0 2-1 4-1 2 1 3 1 6 1-2 1-4 3-6 3v-1h0c-2 0-5 1-7 1l-11 2v-1c4 0 6-1 9-2v-1-3z" class="q"></path><path d="M784 169h3l2 2c-2 1-3 2-5 2v-1-3zm-40 6c2 1 4 2 6 2h1 1c6 1 12 2 18 2l4 1c3 1 9 1 13 0v-1h1l1 1c-5 2-9 2-15 2-3-1-8-1-12-1-2 0-4-1-6-1l-13-3 1-2z" class="AR"></path><path d="M773 173c1 0 2-1 4-1 0 0 1-1 2-1l5-2v3 1c-3 1-5 2-9 2v1h-1c-2 1-5 1-8 1v1c2 0 3 0 4 1h0c-6 0-12-1-18-2v-1h2c2-1 5-1 8-1l3-1 3-1c1 1 1 1 3 0h2z" class="L"></path><path d="M773 173c1 0 2-1 4-1 0 0 1-1 2-1l5-2v3 1c-3 1-5 2-9 2h-3-1-3-1 0c1 0 3 0 5-1l1-1z" class="AF"></path><path d="M594 271c2 1 1 1 2 4-2 1-4 1-5 3v2l1 1h1v-1c0-1 1-2 2-3h3l1-1c1 0 3 1 4 1h1c1 1 2 2 2 4v3h0c-1 1-1 2-2 3l-1 2h-2 0v1c-1 1 0 1-2 1v1c-1 0-1 1-2 0h-1-3l-1 1 1 6h-2c-3-2-5-4-6-6-2-3-3-7-2-10h2v-4c-1-1-1-1-1-2v-1c1-1 3-1 4-2 2 0 4-2 6-3z" class="G"></path><path d="M584 277c3 1 6 3 7 6l-3 3c-2-1-3-2-5-3h2v-4c-1-1-1-1-1-2z" class="E"></path><path d="M591 283c1 2 1 5 1 8h0l1 1-1 1 1 6h-2c1-2 0-6-1-7l-1-4-1-2 3-3z" class="C"></path><path d="M583 283c2 1 3 2 5 3l1 2 1 4c1 1 2 5 1 7-3-2-5-4-6-6-2-3-3-7-2-10z" class="N"></path><path d="M590 292h-1l-1-1h-2v-1c1-1 2-2 3-2l1 4z" class="r"></path><path d="M588 164c1-1 6-1 8-1h21l34 1c5 1 9 0 14 0v1h-1c-3 1-7 0-10 3 2 1 4 1 6 1h3c3 0 6 0 9-1h1 2l1 1-5 1c-5 0-10 1-14 0-3-1-6-2-10-2 1 1 3 1 4 1l2 1c3 0 5 1 8 2s6 1 9 2l2 1 1 1s1 0 2 1l6 3c1 0 3 1 4 2h-1l-22-8c-3 0-6-1-8 0h0l-19-6-11-1c-13-1-26-2-39-1 0-1 2-2 3-2z" class="P"></path><path d="M635 168c2-1 6 1 9 1l11 3c2 0 5 1 7 2-3 0-6-1-8 0h0l-19-6z" class="e"></path><defs><linearGradient id="E" x1="161.23" y1="142.857" x2="149.99" y2="180.263" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#373435"></stop></linearGradient></defs><path fill="url(#E)" d="M183 156h1 0v-2-1c0-1 0-2 1-3h0c2 1 3 7 4 10 1 0 1 1 1 1 1 1 0 3 1 5 0 1 0 1-1 2-3 1-8 0-11 0-2 0-4 1-7 1l-2-1c3 0 6 1 8-1h0 0-7c-2-1-3 1-5 1-4 0-9 0-13 1 3 0 5 1 7 1h2c3 1 7 0 10 2 2 0 3 0 4 1l-23-2c-2 0-4 1-6 1v1h-3-7l-14-1c-2-2-3-1-5-1 0-1 2-2 3-2 6-2 12-2 18-3l28-1c5 0 9 0 13-1 1 0 2 0 3-1 1-2 0-4 0-7z"></path><defs><linearGradient id="F" x1="139.454" y1="168.581" x2="141.855" y2="173.527" xlink:href="#B"><stop offset="0" stop-color="#2f3033"></stop><stop offset="1" stop-color="#474440"></stop></linearGradient></defs><path fill="url(#F)" d="M137 173c-1-1 0-1-1-1h-1c0-1 1-1 1-2 6 0 11 0 17 1-2 0-4 1-6 1v1h-3-7z"></path><path d="M570 257c2-2 5-3 7-6h0c-1 6-2 12 2 17l3 3 1 1c0 1 1 2 1 4h0v1c0 1 0 1 1 2v4h-2c-1 3 0 7 2 10 1 2 3 4 6 6h2 0c1 3 2 3 2 5h-2 0-5c-2 1-3-1-5-2-1-1-3-3-3-5v-1l-2-5-3-3 1-1v-2l-3-6c-1-1-1-2-1-3-1-2 0-3 1-4v-1c-1-3-3-7-4-10h-1c1-2 1-3 2-4z" class="AP"></path><path d="M807 393l2-1c0 1 0 1-1 2l-3 6c1 1 2 1 3 1 2 0 3-1 5-1h1c0 1 1 1 1 2-3 3-7 5-11 7h0l-3 3h-1l1 1v1 1 2h0l-1 1c2 1 2 1 3 1l4-2v2l1 1c0 1 0 1-1 2 1 1 3 1 4 2l-1 1c-1-1-1-1-2-1v1c1 1 0 2 0 3-1 1-2 2-4 3-1 0-1 1-2 1s-1 0-2-1v-1h-1v-1-1-1l-1-1h-2c-1-1-3-2-4-2 0-1-1-1-2-1h0-4v-2l1-1v-1c1 0 1-1 1-1h1l-2-2v-1h2c1-2 2-3 3-4l6-8c2-2 3-4 5-6l4-4z" class="t"></path><path d="M799 429c2-1 3-1 5 0 0-1 1-1 2-1l-1-1v-1h2v1l1 1c-1 1-2 2-4 3-1 0-1 1-2 1s-1 0-2-1v-1h-1v-1z" class="D"></path><path d="M799 411h0c2-1 3-1 5-2l-3 3h-1l-8 6-1 1-1 1c1 1 2 2 3 2 0 1 2 2 2 2l2-1c1 1 0 2 1 2h3v1c-1 0-1 0-2 1l-1-1h-2c-1-1-3-2-4-2 0-1-1-1-2-1h0-4v-2l1-1v-1c1 0 1-1 1-1h1l-2-2v-1h2c1-2 2-3 3-4 1 0 2 1 2 1l-2 4 7-5z" class="Ab"></path><path d="M807 393l2-1c0 1 0 1-1 2l-3 6c1 1 2 1 3 1 2 0 3-1 5-1h1c0 1 1 1 1 2-3 3-7 5-11 7h0c-2 1-3 1-5 2h0l-7 5 2-4s-1-1-2-1l6-8c2-2 3-4 5-6l4-4z" class="F"></path><path d="M814 400c0 1 1 1 1 2-3 3-7 5-11 7h0c-2 1-3 1-5 2h0c4-4 10-7 14-11h1z" class="h"></path><path d="M807 393l2-1c0 1 0 1-1 2l-3 6-11 12s-1-1-2-1l6-8c2-2 3-4 5-6l4-4z" class="l"></path><path d="M829 235h1v-5h1l1 1h2v5c1 12 0 26 1 39l1 2v1c-2 2-1 6-1 9v18 10l-2 2c-1 0-1 0-2-1-2-3-2-7-2-11v-14c0-2 0-5-1-7h-1c1-1 2-3 2-4v-33c1-3 0-8 0-12zM652 721v-1c1-1 1-7 1-7 1-1 2 0 2 0v8l2 1v1c-1 4-1 7-1 11v20l1 30v19c0 4 0 9-1 13v-2c-1 1-1 1-1 3v2h0c0-1 0-2-1-4v-1 3c-1-5-2-10-2-15v-42c0-7 1-15 0-22l-3-1 1-1c-1-1-1-2-1-3l-1-1h-1v-1c0-2 2-3 3-5 2-2 2-3 2-5z" class="y"></path><path d="M647 732l4-5 1 11-3-1 1-1c-1-1-1-2-1-3l-1-1h-1z" class="F"></path><path d="M728 553h0c0-3-1-8 0-11h0v-2c-1-2-1-6 0-7 1 5 1 11 1 17 0 2 0 5 1 7h-1v49l1 9v3l-1 7v2 3l2 2c-1 1-1 2-1 4-1 13-1 27-1 40v70 37h-1v-32-24l-1-89-1-1h0c-1-1-1-1 0-3 1-1 1-1 1-2v-6c-1 0-2-1-3-1s-2 0-3-1c-6-1-12-2-18-4-2-1-4-2-6-2-1 1-1 2-2 3-2-1-2-1-3-2v-2c-1-2-3-2-4-4h1l3 1 1-2 1 1h0c0 2 1 3 3 3l7 3 23 5 1-71z" class="h"></path><path d="M827 219c3 3 2 12 2 16s1 9 0 12v33c0 1-1 3-2 4-2 2-5 4-7 7l-1-9v-18c1-1 1-3 2-4-1-1-1-2-1-3-1-3 0-4 0-7l-1-2c0-2 0-3 1-5v-2l1-5c1-3 0-5 2-8 0-3 1-5 2-7h0l2-2z" class="G"></path><path d="M821 236c1-3 0-5 2-8 1 4 2 7-1 11l-2 4v-2l1-5z" class="T"></path><path d="M820 250h2l3 3 1-1c0 2-2 3-3 4h-1v1c0 1 0 2-1 3-1-1-1-2-1-3-1-3 0-4 0-7z" class="D"></path><path d="M829 247v33c0 1-1 3-2 4-2 2-5 4-7 7l-1-9h3c0-1 0-1 1-2l3-3-1-2c-1-2 0 0-2 0v-1c1-2 1-4 3-5 1 0 1 0 2-1v-3c1-1 1-3 1-4v-13-1z" class="g"></path><path d="M822 282h0v3h1c2-1 2-3 4-4 0-1 2-1 2-1 0 1-1 3-2 4-2 2-5 4-7 7l-1-9h3z" class="F"></path><path d="M264 482h1v3h1c1-1 0-2 1-4l1 17v4c-1-1-1-1-1-2-1 1-1 1-1 2 0 2-1 4-1 6v1c0 3-1 5-1 7h0l-1 2c-1 2 0 8 0 10s-2 4-2 6l-1 5v1l-3-3h0c-2-2-6-5-7-8l1-1v-1l-2-1v-1l-2-2 1-2-2-2-1 1-3-3v1c-1 0-1-2-2-3v-1h-2v-5h10 6 4 2v-2l1-16 3-4v-5z" class="v"></path><path d="M248 509h6v2l-1 1c-1 0-2 1-4 1-1-1-1-2-1-4z" class="AE"></path><path d="M254 509h4v4h-1v1l-4-1v-1l1-1v-2z" class="D"></path><path d="M246 519h1c1 0 2 0 3 1h0l1-2c1 0 1 1 2 2 0 1-1 1-2 2l1 2c1-2 1-2 3-3 1 1 1 1 2 3 1 1 2 1 3 2v4l1 4-1 5v1l-3-3h0c-2-2-6-5-7-8l1-1v-1l-2-1v-1l-2-2 1-2-2-2z" class="N"></path><path d="M252 524c1-2 1-2 3-3 1 1 1 1 2 3 1 1 2 1 3 2v4h-3c0-1-1-1-2-2-1 0-2 0-3-1v-3h0z" class="g"></path><path d="M264 482h1v3h1c1-1 0-2 1-4l1 17v4c-1-1-1-1-1-2-1 1-1 1-1 2 0 2-1 4-1 6v1c0 3-1 5-1 7h0l-1 2c-1 2 0 8 0 10s-2 4-2 6l-1-4v-4-6-4c-1 0-2 0-2-1l-1-1v-1h1v-4h2v-2l1-16 3-4v-5z" class="Z"></path><path d="M258 509h2v5c-1 0-1 1-2 1l-1-1v-1h1v-4z" class="N"></path><path d="M264 482h1v3h1c1-1 0-2 1-4l1 17v4c-1-1-1-1-1-2-1 1-1 1-1 2 0 2-1 4-1 6v1c0 3-1 5-1 7h0v-29-5z" class="AM"></path><path d="M203 226c1 1 2 2 2 3 1 1 1 1 1 2 1 1 1 3 2 5h0c1 0 2 0 3-1v1s0 1 1 2v24c1 1 0 1 0 2 1 1 3 3 4 3v1l1 1 1 1v1c-1 0-3-1-5 0l1 1h1l1 1s-1 1-1 2h0l-3 2c0 4 1 9 0 14v5 6c0 4-1 10 0 14 0 3-1 11 1 13 1 0 1-1 2-2l1 1h0c-2 2-2 3-4 5 1 0 1 1 1 2h0c-1 1-1 1-1 2h-1l-1 1c-1-1-2-3-3-3h0-1l-1 1h-1v1l-4 2-2-1h0c3-1 5-3 7-5s1-8 1-11v-8c1-5 0-9 1-14l-1-9v-31c0-8 1-16 0-23 0-4-2-7-3-11z" class="k"></path><path d="M212 262c1 1 0 1 0 2 1 1 3 3 4 3v1l1 1 1 1v1c-1 0-3-1-5 0l1 1h1l1 1s-1 1-1 2h0l-3 2c0 4 1 9 0 14-1-4 0-8 0-11v-18z" class="N"></path><path d="M207 300c1 3 1 5 0 9v7l1-1c2 2 1 5 2 7h1v-2c0-1 1-4 0-5v-1-7c0-2-1-3 0-4 1-2 0-4 1-6v-1 6c0 4-1 10 0 14 0 3-1 11 1 13 1 0 1-1 2-2l1 1h0c-2 2-2 3-4 5 1 0 1 1 1 2h0c-1 1-1 1-1 2h-1l-1 1c-1-1-2-3-3-3h0-1l-1 1h-1v1l-4 2-2-1h0c3-1 5-3 7-5s1-8 1-11v-8c1-5 0-9 1-14z" class="w"></path><path d="M208 323c1 0 2 0 3 1 0 2 0 5-1 7-2-2-2-4-3-5 0-2 0-2 1-3z" class="o"></path><path d="M206 314c1 2 1 3 2 4 0 2 1 4 0 5s-1 1-1 3c1 1 1 3 3 5 0 2-1 2-2 3l-3-1c2-2 1-8 1-11v-8z" class="I"></path><path d="M205 402h1l1 1h1v-3h2l1-1h2 0 1c0-1-1-2-2-3v-1l1-1v-1c1 0 1-1 2-1 1 1 1 1 2 1h1c1 1 1 3 2 3h1 0 0l1 2h1c1 2 1 2 3 2l1 1v1c-2 1-4 2-5 5h0c1 0 1 1 1 2h-2c-1-1-1 0-2 0h-1 0v2h1c1 1 2 1 2 2v1h-2c0 1-1 2-1 3 1 1 0 2 1 3v1c0 1 0 1 1 1h1v1h0c1-1 1-1 3-1v1h2v1 2h1l-1 1c-1 0-1 1-2 2l2 2h2v-1h0l-1-1 1-1h1v1l1 1-1 1v1s-1 0-2 1h0v3h-1v1h-1l-1 1v1c1 0 2-1 3-1 2 0 2 0 4-1v-1h0l-2-1c0-1 0-1 1-2h1c0 1 1 2 2 3 1 0 1 0 2-1h1l-1 1c0 1 0 1 1 2l1-1v1l1 1v1l1 1v1h-1c-1 1 0 1-1 2l-1-1 1-2-1-1c0-1-1-1-1-2l-1 1h0-1l-1 1v3l1-1 1 1v-1h0l1 1-1 1h-1 0l-1 1h-1c-1-1-2-2-2-3l-1-1-1 1-1-1v-1h-2c1 1 1 1 1 2-1-1-1-1-2 0s-1 2-1 3l-1-1v-3h0l-1-1h-2v3h-2c0-1 0-2 1-2h1l-1-1h-2v-1c-1-1-1-1-1-2v-1h-1v1h-1-1l1-1h3 0 1v1h0v-3l1-1-3-3v-1l-2 1c-1-2-1-3-2-4l1-1-1-1c0-2 1 0 0-2h-1 0-1c0-2 0-4 1-5l1-2h-3v-3h0-2v1c-1-1-1-2-1-3l2-2 1-2h0c1-1 1-1 2 0v2l-1 1h1c0-1 1-1 2-2h0v-1l-1 1v-1l-1-1c-2 0-2-1-4-1l-1 1-1-1 2-2z" class="F"></path><path d="M617 266c-3-9-8-16-16-23-3-2-6-5-9-7l1-1c9 0 18 6 25 11v1c5 5 9 10 12 16l2 5c1 1 1 2 1 4v1l-1-1c-1 1-1 2-1 3v3h-1v-2l-1-1c0 4-2 8-1 12-1 3-2 5-3 7-2 6-7 14-12 18h0c-2 0-3 1-5 2h-3c2-1 3-2 4-3l-1-1 3-3 1-1 2-2v-1c1-1 2-2 3-4l1 1v-1h-1l2-5h1v-1l-1-1c0-1 2-4 3-6 0-2 1-5 0-7v-1s1-1 1-2h0v1c1-1 0-2 0-4 0-1-1-2-1-4h-2c-1-2-1-2-3-3z" class="E"></path><path d="M606 241c5 3 11 8 14 13 1 1 2 3 2 5-6-6-11-12-16-18z" class="C"></path><path d="M625 266l2-1v1 2c1 0 1 1 1 2s1 4 1 5c0 4-2 8-1 12-1 3-2 5-3 7-1-3 1-8 2-11 1-6 0-12-2-17z" class="AG"></path><path d="M606 241l-3-2c2 0 5 2 7 3l8 5c5 5 9 10 12 16-2 0-5-6-7-8 0-1-2-1-3-1-3-5-9-10-14-13z" class="AZ"></path><path d="M620 254c1 0 3 0 3 1 2 2 5 8 7 8l2 5c1 1 1 2 1 4v1l-1-1c-1 1-1 2-1 3v3h-1v-2l-1-1c0-1-1-4-1-5s0-2-1-2v-2-1l-2 1-3-7c0-2-1-4-2-5z" class="AF"></path><path d="M627 266c1 2 3 4 3 6h1c-1-2-1-3-1-4h1 1c1 1 1 2 1 4v1l-1-1c-1 1-1 2-1 3v3h-1v-2l-1-1c0-1-1-4-1-5s0-2-1-2v-2z" class="K"></path><defs><linearGradient id="G" x1="626.343" y1="291.295" x2="608.986" y2="309.017" xlink:href="#B"><stop offset="0" stop-color="#242222"></stop><stop offset="1" stop-color="#4a4847"></stop></linearGradient></defs><path fill="url(#G)" d="M622 286v2 1c0 1-1 3-1 4l1-2c1-2 1-3 3-3v-1-2l1-2h1c-1 3-3 8-2 11-2 6-7 14-12 18h0c-2 0-3 1-5 2h-3c2-1 3-2 4-3l-1-1 3-3 1-1 2-2v-1c1-1 2-2 3-4l1 1v-1h-1l2-5h1v-1l-1-1c0-1 2-4 3-6z"></path><defs><linearGradient id="H" x1="881.221" y1="166.415" x2="880.597" y2="174.413" xlink:href="#B"><stop offset="0" stop-color="#201d1e"></stop><stop offset="1" stop-color="#555453"></stop></linearGradient></defs><path fill="url(#H)" d="M827 157h0l2 1 3 3 1 2c3 1 7 1 10 2 2-1 2-1 3-2l1 1h13c18 1 36 3 54 7-1 0-2 1-2 1h-8-13l-36 3-23 1h-2-1 0l6-1c0-1-3-1-4-2 1-1 4 0 6-1-3 0-8 0-12 1h-3 0c-1 1-4 1-6 1h-2 0c2-2 4-3 6-4s3-1 5-3h0l-2 1c0-2 1-3 2-5 0-1 0-1 1-2 0-1 0-2 1-4z"></path><path d="M833 163c3 1 7 1 10 2h1 3l1 1h1 1c-1 1-1 1-2 1v1h5v1h-5c-2 1-4 1-7 0l-1-2h-2c-1-1-3-1-4-2l-1-2z" class="AI"></path><defs><linearGradient id="I" x1="842.264" y1="167.669" x2="840.956" y2="175.942" xlink:href="#B"><stop offset="0" stop-color="#595654"></stop><stop offset="1" stop-color="#717171"></stop></linearGradient></defs><path fill="url(#I)" d="M827 157h0l2 1 3 3 1 2 1 2c1 1 3 1 4 2h2l1 2c3 1 5 1 7 0l20 1-8 1c1 1 1 2 2 3l-5-1v1l-2 1-23 1h-2-1 0l6-1c0-1-3-1-4-2 1-1 4 0 6-1-3 0-8 0-12 1h-3 0c-1 1-4 1-6 1h-2 0c2-2 4-3 6-4s3-1 5-3h0l-2 1c0-2 1-3 2-5 0-1 0-1 1-2 0-1 0-2 1-4z"></path><path d="M827 157h0l2 1 3 3 1 2 1 2c1 1 3 1 4 2h2l1 2c-8 0-17 1-25 5h-2 0c2-2 4-3 6-4s3-1 5-3h0l-2 1c0-2 1-3 2-5 0-1 0-1 1-2 0-1 0-2 1-4z" class="a"></path><path d="M827 157h0l2 1 3 3 1 2 1 2c1 1 3 1 4 2-2 1-7 1-9 0-2 0-2-1-3-2v-1-3c0-1 0-2 1-4z" class="AP"></path><path d="M695 166c3 0 6-1 9 1 5 1 9 2 15 2 0 0 1 0 2 1h4c1 0 2 0 3 1h2c2 1 3 0 5 1 1 1 4 1 6 2h3-1l-1 1h1 1l-1 2-8-1c2 0 5 1 6 2l-1 1h-1l1 1h-12-20v1c1 0 2 1 2 1h0c-2 1-5-1-8-2l-10-3c-1-1-2-1-4-1l-4-1-2-1-1 1c2 2 4 3 7 4l8 3-1 1-5-2c-2-1-5-1-7-2h0l-2 1-6-3c-1-1-2-1-2-1l-1-1-2-1c-3-1-6-1-9-2s-5-2-8-2l-2-1c-1 0-3 0-4-1 4 0 7 1 10 2 4 1 9 0 14 0l5-1 2-1c4-1 9-1 13-2h4z" class="e"></path><path d="M678 168c1 2 1 2 2 2v1l-1 1h-1c-2 1-5-1-7-1v-1l5-1 2-1z" class="Aa"></path><path d="M691 166h4l-1 1c-1 1-4 3-5 3l-3 1h0l-6-1h0c-1 0-1 0-2-2 4-1 9-1 13-2z" class="P"></path><path d="M680 170h0c3-1 6-1 9 0l-3 1h0l-6-1h0z" class="i"></path><path d="M899 205c3-3 22-25 24-26h1c-8 11-19 20-26 30 1 1 1 1 2 1 2-1 3-1 4 0l4 1v1c3 1 4 3 5 5l1 1 1-1h0c1 2 5 2 7 3h6l-2-2 1-1v1c3 2 4 3 7 4h1c4 1 9 1 14 1v1c-2 1-5 0-7 0-6 0-11-1-16-2l-22-4c-2 0-11-3-12-3 0 1-1 2-2 3l-9 11-20 23c-8 9-17 16-25 25l-1-2 12-12c5-5 11-9 16-15l12-14c6-6 10-13 15-19l-38-7c-1 1-1 1-1 2l-1 1c0-1 0-2-1-4l-1 1c-1 1-1 1-2 1 0-1 1-1 1-3l-5-4 3-3c2-2 4-3 7-5 7-4 15-8 23-11 1 0 2-1 3-1-8 7-14 18-23 24 5 2 11 2 16 4 5 1 10 2 16 2 1-1 1-1 1-3l1 1c0 1 0 1-1 2h0v1l1-1h0l1 1h1l8-8z" class="h"></path><path d="M898 209c1 1 1 1 2 1 2-1 3-1 4 0l4 1v1c3 1 4 3 5 5h-3-2l-14-3 4-5z" class="F"></path><path d="M875 183v1c-5 4-18 21-22 22-4-1-5-3-8-7 2-2 4-3 7-5 7-4 15-8 23-11z" class="AO"></path><path d="M786 423h4 0c1 0 2 0 2 1 1 0 3 1 4 2h2l1 1v1 1 1h1v1h-1l-1 1h2v1l1 1v1l-1 1-1 1h1l2-1 1 1v-1l1-1v1 1c1 1 0 1 2 1v1c0 1 0 1-1 2-1 0-1 0-1 1v5c0 1-1 2-2 2-2-1-2-2-4-2l-1 1c0 1 0 2 1 3l-1 1-1-1c-1-1-1-1-1-2l-1 1v-1l-1-1-1 1-1 1v-1c-1 0-2 0-2-1h-1c0-1-1-1-1-2l-1-1-1 2v1c0 1 0 1-1 2 1 1 1 1 1 2h1c0 1 0 1 1 2l-1 1-2-3h-1v-1l1-1c0-2 0-3-1-4h-3v1l-1-1h-2c-1 1-1 2-2 2-1 1-1 2-2 3l-1 1v1 1h-1 0l-1-2h-1v1 1l-1 1-1-6 1-1-1-9v-1c-1-2-1-5 0-7 3-1 4-3 6-4l2 1 1-1v1l-1 1h1l1 1 2-2c1-1 3-3 4-3 1-1 2-2 3-2z" class="g"></path><path d="M783 440c0-1 0-1 1-1v1c1 1 1 1 1 2l-1 1h-2v1s1 1 2 1l2-1c1 0 1 0 1 1s1 1 1 2h1c1 1 2 1 3 2l-1 1v-1c-1 0-2 0-2-1h-1c0-1-1-1-1-2l-1-1-1 2v1c0 1 0 1-1 2 1 1 1 1 1 2h1c0 1 0 1 1 2l-1 1-2-3h-1v-1l1-1c0-2 0-3-1-4h-3v1l-1-1v-2c2-1 3-2 4-4z" class="D"></path><path d="M806 439c0 1 0 1-1 2-1 0-1 0-1 1v5c0 1-1 2-2 2-2-1-2-2-4-2l-1 1v-1c-1-1-2-2-4-2h0v-1h1c1 0 1 1 2 1 1-1 2 0 3-1v-3h-2c0-1 0 0 1-1h0 2c0 1 0 2 1 3s1 0 1 1h1 0c0-1-1-2-2-2v-2c1-1 1 0 2 1l1-2c1 1 1 0 2 0z" class="r"></path><path d="M794 444v-1s-1 0-1-1 0-2-1-3h-1l-1 2h-1v-2l-1-1v-1l2-2c-1 0-1-1-2-1 2-1 3 1 5 2 1 0 1 1 2 2 0 1 0 1 1 1v1l1 1h2v3c-1 1-2 0-3 1-1 0-1-1-2-1z" class="F"></path><path d="M767 431c3-1 4-3 6-4l2 1 1-1v1l-1 1h1l1 1 2-2c1 1 1 2 2 2l1 1v1 1 2h1c0 1 0 1 1 2 1-1 1-1 2 0l1 1c-1 0-1 1-2 1h-1c-1 0-1 0-1 1-1 2-2 3-4 4v2h-2c-1 1-1 2-2 2-1 1-1 2-2 3l-1 1v1 1h-1 0l-1-2h-1v1 1l-1 1-1-6 1-1-1-9v-1c-1-2-1-5 0-7z" class="g"></path><path d="M767 431c3-1 4-3 6-4l2 1 1-1v1l-1 1h1l1 1 2-2c1 1 1 2 2 2l1 1v1 1 2h1c0 1 0 1 1 2 1-1 1-1 2 0l1 1c-1 0-1 1-2 1h-1c-1 0-1 0-1 1h-2-2-1v-2h-1c-1-1-1-1 0-2-1 0-2 1-2 1-1 0-1 0-2-1v1l-1 1h0l-3-1-2 2v-1c-1-2-1-5 0-7z" class="v"></path><path d="M767 431c3-1 4-3 6-4l2 1 1-1v1l-1 1h1l1 1-3 3c-2 1-3 3-5 4l-2 2v-1c-1-2-1-5 0-7z" class="Ab"></path><path d="M265 509v5 3h0l1-1c0 2 1 3 0 4-1 2-1 3 0 6l1 5v2 11 2s2 1 2 2l7 7 5 6 11 12 14 16 2 3h0l-41-45 1 32v28c0 11 2 23 1 34-1 3-2 5-3 8h0-4v-5c-2-11 0-24-1-36-1-5-2-10-4-16-2-5 1-10 3-15v-2c1-3 0-6 1-8v-17-8c-1-1-3-4-4-5l3 3v-1l1-5c0-2 2-4 2-6s-1-8 0-10l1-2h0c0-2 1-4 1-7z" class="l"></path><path d="M257 592h1c2 0 3 0 5 1l1 19 1 17c0 4 0 9-1 14v5h-1v-1c-1-1-1-2-1-3-2-11 0-24-1-36-1-5-2-10-4-16z" class="I"></path><path d="M261 534c0-2 2-4 2-6s-1-8 0-10c0 4 1 10 0 13v2c1 4 0 8 1 11v26c-1 4 0 7-1 11h0l-1 1c2 3 1 7 1 11-2-1-3-1-5-1h-1c-2-5 1-10 3-15v-2c1-3 0-6 1-8v-17-8c-1-1-3-4-4-5l3 3v-1l1-5z" class="m"></path><path d="M260 577v1c1 3-2 8-3 11 2-2 4-4 5-7 2 3 1 7 1 11-2-1-3-1-5-1h-1c-2-5 1-10 3-15z" class="p"></path><path d="M227 402l1-1v-1h1 1l1 1 1-1c0-2 0-2-1-3l1-2h2c0 2-1 2-1 3l1 2c-1 1-2 0-2 2l2 2-1 1v1h1l1 1 1 1 2-1v1l3 1v1l-2 1v2s-1 1-1 2c-1 1 0 1 0 2h-1-3v2h3l1-1c0 1 1 1 1 2v1h-1c-1 0-1 1-1 1l-1 1h-1c-1 0-2 0-2-1l-1 1c1 0 2 1 3 2 0-1 0-1 1-1h2 1c3 0 2-1 3-3h1c1 0 1 0 2-1h0l1 1s-1 1-1 2h2c1-1 2-1 3-1h1c-1-1-1-3-2-4l1-1h5c0 1 0 1 1 2h0l1 1h0v2h1c1 1 1 0 1 1h1v6c0 2 1 4 0 6l1 25h-3 1c1-2 1-6 1-9h-1l-2-3c0-1 0-1 1-2l-1-1 1-1h0l-1-1h0l-1 2-1 1-1-1h-1 0-1c0 1 0 1-1 2h-3v-1h-1-1l-1-1h0c0 1 0 1-1 2-1-1-2-2-2-3-1-1 0 0-2-1v1c0 1-1 1-2 2l-1-1v-1c1-1 0-1 1-2h1v-1l-1-1v-1l-1-1v-1l-1 1c-1-1-1-1-1-2l1-1h-1c-1 1-1 1-2 1-1-1-2-2-2-3h-1c-1 1-1 1-1 2l2 1h0v1c-2 1-2 1-4 1-1 0-2 1-3 1v-1l1-1h1v-1h1v-3h0c1-1 2-1 2-1v-1l1-1-1-1v-1h-1l-1 1 1 1h0v1h-2l-2-2c1-1 1-2 2-2l1-1h-1v-2-1h-2v-1c-2 0-2 0-3 1h0v-1h-1c-1 0-1 0-1-1v-1c-1-1 0-2-1-3 0-1 1-2 1-3h2v-1c0-1-1-1-2-2h-1v-2h0 1c1 0 1-1 2 0h2c0-1 0-2-1-2h0c1-3 3-4 5-5z" class="N"></path><path d="M238 424h1c3 0 2-1 3-3h1c1 0 1 0 2-1h0l1 1s-1 1-1 2h2c1-1 2-1 3-1h1c-1-1-1-3-2-4l1-1h5c0 1 0 1 1 2h0l1 1h0v2h1c1 1 1 0 1 1h1v6c0 2 1 4 0 6-1-1-2-1-4-1 0 2 0 2-1 3 0-1 0-1-1-1 0 1 1 2 0 3h-1c-1-1-1-2-3-1h1l-1 1-1-1h0-1c-1 1-2 0-3 0l-1 1v1 1c-1 0-1-1-1-1 0-1 0-1-1-2 0 0-1-1-1-2v-1l-1 1c0 1-1 1-2 1h-1c1-1 1-1 1-2s-1-2-1-2c1-1 1-3 1-4h0l-2 2-2-1v-1-1h2v-1l1-1h2l-1-2z" class="AK"></path><path d="M284 162l15 1c6 0 12 2 19 2h5l3-1h3l1 1h1c3 2 7 0 10 1h14 4 3c2-1 3-1 4-2 2 0 5 0 7-1 2 0 6 0 7 2h-1l-4 2h-1c-1 1-2 1-4 1l-27 9c-4 0-6 0-9 2h-4-7 0c0-1 1-1 1-1 2-1 4-1 5-2h-6-6c-4 1-8 2-12 2-2-1-6-1-8-1v1c-2-1-4-1-6-1-3-1-7-1-11-2-1-1-4-2-5-3l-3-1h-1c-3 0-6-1-8-1l5-1c3 0 5 0 8-2v-3c2 0 6-2 8-2z" class="AD"></path><path d="M283 173l-1-1h-2l1-1h6v1c3 0 4 1 7 1v1h-2c-2-2-6 0-9-1z" class="AB"></path><path d="M328 173h-4c-2-1-3 0-5 0h-15c-4-1-7-1-10-1 3-1 6-1 9-1 9 0 18 1 27 1h4v1 1c-2 0-4-1-6-1z" class="AF"></path><defs><linearGradient id="J" x1="350.441" y1="167.969" x2="329.921" y2="181.825" xlink:href="#B"><stop offset="0" stop-color="#747472"></stop><stop offset="1" stop-color="#949592"></stop></linearGradient></defs><path fill="url(#J)" d="M360 169c3 0 7-1 10-1h0l-27 9c-4 0-6 0-9 2h-4-7 0c0-1 1-1 1-1 2-1 4-1 5-2h-6c1-1 2-1 3-1 1-1 2-1 2-2 2 0 4 1 6 1v-1-1h-4 6 9c4 1 8 0 11-1h1c1 0 2-1 3-2z"></path><path d="M373 163c2 0 6 0 7 2h-1l-4 2h-1c-1 1-2 1-4 1h0c-3 0-7 1-10 1-1 1-2 2-3 2h-1v-1h-14c-4 0-7 0-11-1 1 0 2-1 2-1 1 0 2 0 3-1s3-1 5-1h0 14 4 3c2-1 3-1 4-2 2 0 5 0 7-1z" class="AF"></path><path d="M342 170l-3-1c4-1 9-1 13 0h8c-1 1-2 2-3 2h-1v-1h-14z" class="P"></path><path d="M373 163c2 0 6 0 7 2h-1-2c-2 1-3 1-5 1-6 1-11 1-17 0h4 3c2-1 3-1 4-2 2 0 5 0 7-1z" class="f"></path><path d="M284 162l15 1h-2c1 1 1 1 2 1l1 2c-1 0-3-1-5-1h-2c-1 0-1 0-2-1h-2 0c1 1 2 1 3 2l5 2c-2 0-3 0-5-1v1c-2 1-4 1-6 1v1l1 1h-6l-1 1h2l1 1c-3 0-5-1-8-1l-3-1h-1c-3 0-6-1-8-1l5-1c3 0 5 0 8-2v-3c2 0 6-2 8-2z" class="L"></path><path d="M286 170h-8c1-1 5-2 6-2 3 0 4 1 6-1h1l1-1 5 2c-2 0-3 0-5-1v1c-2 1-4 1-6 1v1z" class="P"></path><path d="M284 162c0 1 0 2-1 3-2 1-5 2-7 2v-3c2 0 6-2 8-2z" class="J"></path><path d="M299 163c6 0 12 2 19 2h5l3-1h3l1 1h1c3 2 7 0 10 1h0c-2 0-4 0-5 1s-2 1-3 1c0 0-1 1-2 1h-5-9-11c-2-1-4-1-7-2-1 0-1 0-2 1l-5-2c-1-1-2-1-3-2h0 2c1 1 1 1 2 1h2c2 0 4 1 5 1l-1-2c-1 0-1 0-2-1h2z" class="i"></path><path d="M317 169h0c-3-1-5 0-7-2h-1l-2 1-2-2h1 5l1 1c1 0 3 0 4-1h1c1 1 2 2 4 2h0c2 0 4 0 5 1h-9z" class="AG"></path><path d="M326 164h3l1 1h1c3 2 7 0 10 1h0c-2 0-4 0-5 1s-2 1-3 1c0 0-1 1-2 1h-5c-1-1-3-1-5-1v-1c2-1 8 0 11 0l-9-2 3-1z" class="P"></path><path d="M401 664l1-8 7 5 4 3c1 3 3 6 4 9l2 2 1 4c2 3 3 5 4 8 1 2 3 5 3 7-1 1-4 2-4 3v1c0 8 1 16 0 24-1 1-1 3 0 4v4h-1c-1 5 1 10 0 15v4 3c-2-1-2-1-3-3v-7c0-2 0-3-1-5h0v-16l-9-7c-2-2-5-4-7-6v1c-1-1 0-3 0-4-1-2-1-7-1-9v-5-27z" class="G"></path><path d="M401 696l4 1h0c-1 1-1 2-2 3l1 3h0v3l-2-1c-1-2-1-7-1-9z" class="D"></path><path d="M401 664l1-8 7 5c-1 0-2 1-3 2l-1-1v-1h-1c-1 1-1 2-2 4h1 1l1 1c-1 0-1 0-2 1-2 3-1 8-1 11l6-7 1 1c-2 3-4 4-5 7l-1 1v1h1 1v1c-1 1 0 1-1 3 0 1-2 2-2 4l2 1c-1 1-2 1-2 1h-1v-27z" class="F"></path><path d="M418 687l1 17-1 33h0v-16l-9-7c-2-2-5-4-7-6v1c-1-1 0-3 0-4l2 1c4 4 10 7 14 11v1-31z" class="h"></path><path d="M417 673l2 2 1 4c2 3 3 5 4 8 1 2 3 5 3 7-1 1-4 2-4 3v1 1c-1 1-1 1-1 2v-4-3l-3 1v9l-1-17-1-14z" class="Q"></path><path d="M420 679c2 3 3 5 4 8-1 0-1 0-2 1 0 2 0 4-1 7-1-5-2-11-1-16z" class="b"></path><path d="M419 704v-9l3-1v3 4c0-1 0-1 1-2v-1c0 8 1 16 0 24-1 1-1 3 0 4v4h-1c-1 5 1 10 0 15v4 3c-2-1-2-1-3-3v-7c0-2 0-3-1-5l1-33z" class="m"></path><defs><linearGradient id="K" x1="614.495" y1="804.789" x2="607.365" y2="804.678" xlink:href="#B"><stop offset="0" stop-color="#de1610"></stop><stop offset="1" stop-color="#841513"></stop></linearGradient></defs><path fill="url(#K)" d="M608 723l2-9v6l1 1v-5h0 3c0 12 1 24 1 36v103l-1 23c0 5 0 10-1 15 0 1 0 3-1 4-1-1-1 0-1-2s-3-4-3-6c-1-4 0-8 0-12l-1-20v-43-16c0-11 1-23 0-34 2-3 1-20 1-25v-16z"></path><path d="M608 723l2-9v6l1 1v-5h0l-1 87c-1-2-1-11 0-14h0v-2-3-5-2-1-5-1-5-3h-1l-1-15c0-2 1-5 0-8v-16z" class="z"></path><defs><linearGradient id="L" x1="364.539" y1="762.172" x2="377.267" y2="762.696" xlink:href="#B"><stop offset="0" stop-color="#8a0908"></stop><stop offset="1" stop-color="#c71710"></stop></linearGradient></defs><path fill="url(#L)" d="M373 648h1v1 3 1c0 4 1 8-1 12v2 1l1 1c0 1 0 3-1 3v8l1 1v2c3 3 21 17 22 18l-1 2h0c-4-3-7-6-10-8l-11-9v116l1 23v10 4 11c0 1 0 2 1 3-1 2-1 3-2 5-2 4-1 9-2 13-1 1-1 2-1 4v3c-1-1-1-3-1-5h-1v-3c0-3 0-5-1-8v-1-154h1c0-3-1-7 0-10v-9c-1-2 0-4 0-6 1-1 1-3 1-4l-1-1c1-2 0-3 0-5 0-1 0-1-1-2 1-3 1-5 2-7v-1c1-2 0-4 2-6v-4l1-4z"></path><path d="M374 858l-2-2c-1-9 0-18 0-27 0-4-1-8 0-11v25c1 0 1 0 2-1h0c0-1 1-1 1-3v11c0 1 0 2 1 3-1 2-1 3-2 5z" class="l"></path><defs><linearGradient id="M" x1="760.579" y1="560.247" x2="767.595" y2="560.254" xlink:href="#B"><stop offset="0" stop-color="#b50b0b"></stop><stop offset="1" stop-color="#e62f26"></stop></linearGradient></defs><path fill="url(#M)" d="M765 426l2-1c0 2 1 4 0 6s-1 5 0 7v1l1 9-1 1 1 6v16l-1 4c0 2 0 5 1 7v5h1l3 1-1 1c-1 0-1 0-2-1-1 1-1 4-1 5v2c0 3 0 5 1 8 0 2-1 4-1 6v22h0l-1 9 1 6c0 1 0 1 1 2h-1l-1 10c0 3 0 7 1 10 0 5 1 10 0 15v-6h-1c0 1 0 2-1 3-1 2 0 7 0 10h1v12h0c0-2 0-5 1-7v20l1 34-3 41v13c0 2 0 5-1 7-1-16-1-33-2-50 0-6-3-10-3-16l1-31v-17c0-1 0-1-1-2 2-6 1-12 1-17 0-8 1-15 0-22l1-8-1-76v-19-2-1-2-6c1-1 1-2 1-3v-1c1-1 1 0 2-1v1c0-1 1-1 1-2 1-1 0-2 0-3v-6z"></path><path d="M765 573v-11-16h1s0 1 1 1c0-2 0-4-1-6v-1-1c0-1 1-2 1-2v3l1 6c0 1 0 1 1 2h-1l-1 10v3c-1-2 0-5-1-7v1 4 3 1c-1 3-1 7-1 10z" class="l"></path><path d="M765 573c0-3 0-7 1-10v-1-3-4-1c1 2 0 5 1 7v-3c0 3 0 7 1 10 0 5 1 10 0 15v-6h-1c0 1 0 2-1 3 0-1 0-1-1-2 1-1 1-3 1-4l-1-1z" class="p"></path><path d="M765 426l2-1c0 2 1 4 0 6s-1 5 0 7v1l1 9-1 1 1 6v16l-1 4h0-1v7c-1 10 0 21 0 31-1 0 0-35-1-40 0-2 0-3 1-5 0-4-1-8-1-11l1-11v-3c-1 0-1-1-1-1v-2l-1-3c0-1 1-1 1-2 1-1 0-2 0-3v-6z" class="h"></path><path d="M758 545c1 1 1 2 4 2l-1 8c1 7 0 14 0 22 0 5 1 11-1 17l-9 10c0 1 1 2 1 3-2 2-20 24-21 25l-2-2v-3-2l1-7v-3l-1-9v-49l2 1 1 1h0l2-1-1-1h-1c1-1 1-2 1-2v-1h1c0 1 0 1 1 1v1c2 0 2 1 4 0 0 0 0-1 1-1l1 2c-1 1-1 1 0 2l1 1c0-1 1-2 1-3l1-1h0c0-1 0-1 1-2h0c-1-1-1-1-2-1l-1-1c1-1 2-2 2-4 0-1 1-1 2-2v1s1 0 0 1v1h1l2-1 4 1 1-1v2l1 1c1 0 2 0 3-1v-2c0-1-1-1-2-2v-1h2z" class="AO"></path><path d="M758 545c1 1 1 2 4 2l-1 8v-2-2l-1-1-1 2-1 1-3-2c1 0 2 0 3-1v-2c0-1-1-1-2-2v-1h2z" class="r"></path><path d="M742 560c0-1 1-2 1-3l1-1h0c0-1 0-1 1-2h0c-1-1-1-1-2-1l-1-1c1-1 2-2 2-4 0-1 1-1 2-2v1s1 0 0 1v1h1l2-1 4 1h0v3l-1 1 1 1 1 1-2 1h1l1 1-2 2c-1-1-1 0-1-1v-1l-2-1v-1-1c-2 1-3 0-4 1v4c0 1 0 1-1 2l-2-1z" class="D"></path><path d="M751 604c0 1 1 2 1 3-2 2-20 24-21 25l-2-2v-3-2l1-7v4c1 0 1-1 2-1l1-1c0 2-1 1-1 3 2-1 3-2 4-3-1-2-1-3 0-4l1 3c1 0 2-2 3-3l11-12z" class="p"></path><path d="M772 453v-1l1-1c1-1 1-2 2-3 1 0 1-1 2-2h2l1 1v-1h3c1 1 1 2 1 4l-1 1v1h1l2 3 2 2v2l4 1-1 1 1 1 2 1v1l-1 1 1 2h0c-1 1-1 2-1 3l1 1-2 2 1 1c0 1 1 2 1 4l1 1-1 1c2 0 2 0 3 1 0 0-1 1 0 1 0 1 1 2 1 2s-1 1-2 1c0 1 1 3 1 4 0 3-2 3-2 5l2 1v2h0c-1 2 0 3-2 5v1h0 2l-1 1c1 2 0 3 0 5h-1v2l-1 1v2c-1 1-1 2-2 2-1 2-1 3-2 4 0 2-1 3-2 5 0 0 0 1-1 2h-1-3v2h-1l-2 2v2h0l-1-1v-1h-2c-1 0-2-1-2-2h-1 0-2c0 1 0 1-1 2h1l-1 1c-1 1-1 4-1 5v-4h0c0-2-1-2-2-2h0v-22c0-2 1-4 1-6-1-3-1-5-1-8v-2c0-1 0-4 1-5 1 1 1 1 2 1l1-1-3-1h-1v-5c-1-2-1-5-1-7l1-4v-16l1-1v-1-1h1l1 2h0 1v-1z" class="D"></path><path d="M773 479c1 0 1 0 1 1 0 0 0 1-1 1l1 2h-2c0-1-1-2-1-3l2-1z" class="r"></path><path d="M772 488c1 0 1 1 2 1-1 2 0 3-2 4l-1-1v2c-1 1-1 1-2 1h-1v-2c0-1 0-4 1-5 1 1 1 1 2 1l1-1z" class="N"></path><path d="M792 516c-1-1-2-3-2-4 1-1 1 0 1-1s-1-1-1-1v-1h2c1-1 1-1 1-2h1l2 2h-1v2l-1 1v2c-1 1-1 2-2 2z" class="G"></path><path d="M768 471h1 0c1 0 2 1 2 2v1h2v1c-2 1-2 3-4 3v1c1 0 1 0 1 1s-1 1-2 1v1c-1-2-1-5-1-7l1-4z" class="N"></path><path d="M797 495l-1 1h-3v-1h0c-2 1-2 1-4 1v-2c0-1 1-1 2-2h0v-2l1-1c1 0 1-1 2-1l1 1 1-1 1 1c0 3-2 3-2 5l2 1z" class="t"></path><path d="M779 479h2v1 1 3c1 1 1 1 0 2h0c1 1 2 1 3 1v1c-1 1-2 0-3 0l-1 1v2l-1-1v-1c0-1-1-3-2-4v-1l-1-1v-2c1-2 1-2 3-2z" class="N"></path><path d="M778 495h2v2 1 1c0 1 1 1 1 2s0 1-1 2h-1l1-2h-2l-1 2h0c0 2 0 2-1 3v-1-3l-1-1v1c-1-1-1-1-2-1l-1 1-1 1-1-1v-1h2v-1h2l1-1-2-2v-1h1l1 1c1 0 2-1 3-2z" class="g"></path><path d="M772 453v-1l1-1c1-1 1-2 2-3 1 0 1-1 2-2h2l1 1v-1h3c1 1 1 2 1 4l-1 1v1h1l2 3 2 2v2l4 1-1 1 1 1v3c-1 0-2 1-3 2h-1v2l-1-1c0-1 0 0-1-1-1 0-1 0-2 1v1l-1 1-1 1h0l-1-2-1 1-1-1v-1l-1 1-1-1-1-1c1-1 1-2 1-3-2 0-3 0-4-1 0-2 0-2 1-3h1l-1-1 1-2v-1-2c-1 0-2 0-3-1h0z" class="N"></path><path d="M779 460l2-2v-1-1l1-1-1-1 2-2h1l2 3 2 2-2 2v1c-1 0 0 0-1 1v1h-1c-1 0-1 0-2-1v-2c-1 0-2 1-3 1z" class="g"></path><path d="M772 453v-1l1-1c1-1 1-2 2-3 1 0 1-1 2-2h2l1 1v-1h3c1 1 1 2 1 4l-1 1v1h1-1l-2 2 1 1-1 1v1 1l-2 2c-2 0-3-1-4-3v-1-2c-1 0-2 0-3-1h0z" class="t"></path><path d="M842 202l5 4c0 2-1 2-1 3 1 0 1 0 2-1l1-1c1 2 1 3 1 4l1-1c0-1 0-1 1-2l38 7c-5 6-9 13-15 19l-12 14c-5 6-11 10-16 15l-12 12c-1-13 0-27-1-39l1-16 1-9 2-4 4-5z" class="AO"></path><path d="M863 248c0-1 0-2 1-3h0 0l1-1c-2 0-4 2-6 2 2-1 3-3 5-4 2-2 5-4 7-6l1-1c1-1 2-1 3-1l-12 14z" class="v"></path><path d="M842 202l5 4c0 2-1 2-1 3 1 0 1 0 2-1l1-1c1 2 1 3 1 4h-3l-2 3h0-1-2v1h0c-1 2-2 3-4 4-2 0-2 0-3 1l1-9 2-4 4-5z" class="F"></path><path d="M696 182l-8-3c-3-1-5-2-7-4l1-1 2 1 4 1c2 0 3 0 4 1l10 3c3 1 6 3 8 2h0s-1-1-2-1v-1h20 12l-1-1h1l1-1c-1-1-4-2-6-2l8 1 13 3c2 0 4 1 6 1 4 0 9 0 12 1 6 0 10 0 15-2 1-1 2-2 3-2 0 1 0 2-1 3v1c-2 3-5 5-7 8-1 1-1 1-1 3-1 1-1 2-2 2l-1-1-14 13s-3 3-4 3c-2 1-3 2-5 3l1-4h-1-1c-2 1-2 1-4 1h-1c-2 1-3 3-5 5h-2l-2 1v1l-1 3-1 3h-1l-2-5c1-1 1-2 1-4-1-3-4-6-6-8l-7-6c-4-2-9-4-13-5-1 0-4-2-5-3-4-1-8-3-12-5h0c-3 0-10-3-11-5h1c-1-1-3-2-4-2l2-1h0c2 1 5 1 7 2l5 2 1-1z" class="AL"></path><path d="M730 195c1-1 1 0 2-1s6-1 8-2l-3 4v1h-2c-2 0-4 0-5 1 0-1 0-1 1-2 1 0 0 0 1-1h-2z" class="AD"></path><path d="M754 181h8c2 1 2 2 3 3v1c-1 1-2 1-4 1l1-1c0-1-4 0-5-1v-2l-3-1z" class="e"></path><path d="M740 192c4 0 9-2 12-2h3 1 4c-3 1-6 1-10 1v1c-2 1-6 2-8 2l-1 1h0c-1 1-2 1-4 2v-1l3-4z" class="P"></path><path d="M740 180l-1-1h1l1-1c-1-1-4-2-6-2l8 1 13 3c2 0 4 1 6 1h0-8-1c-4-1-9-1-13-1z" class="J"></path><path d="M762 181c4 0 9 0 12 1-1 0-1 1-2 2-2 3-5 4-8 5h1 1 0 2c-1 1-3 1-4 2-2 1-5 1-6 1l-17 3h0l1-1c2 0 6-1 8-2v-1c4 0 7 0 10-1s6-3 9-5h-3-1v-1c-1-1-1-2-3-3h0z" class="AF"></path><path d="M789 180c1-1 2-2 3-2 0 1 0 2-1 3v1c-2 3-5 5-7 8-1 1-1 1-1 3-1 1-1 2-2 2l-1-1v-1l-2 1h0c1-2 4-4 6-6h0c-1 1-3 2-4 2h-3l1-1c-1 0-1 0-2 1h-1l-1-1h0c-4 2-7 2-10 2 1-1 3-1 4-2h-2 0-1-1c3-1 6-2 8-5 1-1 1-2 2-2 6 0 10 0 15-2z" class="e"></path><defs><linearGradient id="N" x1="753.931" y1="200.429" x2="748.134" y2="189.32" xlink:href="#B"><stop offset="0" stop-color="#838481"></stop><stop offset="1" stop-color="#aeb0ab"></stop></linearGradient></defs><path fill="url(#N)" d="M764 191c3 0 6 0 10-2h0l1 1h1c1-1 1-1 2-1l-1 1-14 7h0c-2 0-3 0-4 1h-2-1l2-2h-1c-5-1-12 3-16 6h0l-2 1-1-1c-1 1-1 1-2 1-2-1-2-1-4-1l1 1v1l-1 2-7-6h0v-1h-1l1-1 1-1 4 1c1-1 3-1 5-1h2c2-1 3-1 4-2l17-3c1 0 4 0 6-1z"></path><path d="M725 198h1c1 0 2 0 4 1h1l1 1c-1 1-2 2-3 2 1 1 2 1 4 1v1l-1 2-7-6h0v-1h-1l1-1z" class="AL"></path><path d="M681 180l2-1h0c2 1 5 1 7 2l5 2 1-1 5 2 3 1c4 2 12 6 16 6 3-1 7 1 10 1v1c-2 0-4-1-6-1l-1 1c1 0 2 0 3 1 1 0 3 0 4 1h2c-1 1 0 1-1 1-1 1-1 1-1 2l-4-1-1 1-1 1h1v1h0c-4-2-9-4-13-5-1 0-4-2-5-3-4-1-8-3-12-5h0c-3 0-10-3-11-5h1c-1-1-3-2-4-2z" class="P"></path><path d="M715 191h1c3 2 5 2 8 4-3 0-5-1-7-1-1-2-1-2-2-3z" class="AD"></path><path d="M707 192l1-1c1 0 1 0 2 1h2l5 2c2 0 4 1 7 1h3v1h-2l1 1-1 1-1 1h1v1h0c-4-2-9-4-13-5-1 0-4-2-5-3z" class="AN"></path><path d="M681 180l2-1h0c2 1 5 1 7 2l5 2 3 1 9 4 7 3h1c1 1 1 1 2 3l-5-2h-2c-1-1-1-1-2-1l-1 1c-4-1-8-3-12-5h0c-3 0-10-3-11-5h1c-1-1-3-2-4-2z" class="AR"></path><path d="M741 202h0c4-3 11-7 16-6h1l-2 2h1l-2 1h1c0 3-1 0 0 2l1 1c-3 2-5 4-7 4-2 2-4 3-5 5-2 2-2 3-3 5v1l-1 3-1 3h-1l-2-5c1-1 1-2 1-4-1-3-4-6-6-8l1-2v-1l-1-1c2 0 2 0 4 1 1 0 1 0 2-1l1 1 2-1z" class="AB"></path><path d="M741 202h0c4-3 11-7 16-6h1l-2 2h1l-2 1h1c0 3-1 0 0 2l1 1c-3 2-5 4-7 4l1-2 4-4c-4 2-8 3-11 4v1l-3-3z" class="L"></path><path d="M733 203l-1-1c2 0 2 0 4 1 1 0 1 0 2-1l1 1c-1 1-2 2-3 4h1c2 0 2 0 3 1s1 2 2 3l-2 1h1c1 2 1 3 1 5l-1 3-1 3h-1l-2-5c1-1 1-2 1-4-1-3-4-6-6-8l1-2v-1z" class="AD"></path><defs><linearGradient id="O" x1="770.867" y1="185.204" x2="757.132" y2="212.803" xlink:href="#B"><stop offset="0" stop-color="#565553"></stop><stop offset="1" stop-color="#777675"></stop></linearGradient></defs><path fill="url(#O)" d="M777 190h3c1 0 3-1 4-2h0c-2 2-5 4-6 6h0l2-1v1l-14 13s-3 3-4 3c-2 1-3 2-5 3l1-4h-1-1c-2 1-2 1-4 1h-1c-2 1-3 3-5 5h-2l-2 1c1-2 1-3 3-5 1-2 3-3 5-5 2 0 4-2 7-4l-1-1c-1-2 0 1 0-2h-1l2-1h2c1-1 2-1 4-1h0l14-7z"></path><path d="M757 198h2c1-1 2-1 4-1-2 2-4 4-6 5l-1-1c-1-2 0 1 0-2h-1l2-1z" class="X"></path><path d="M745 211c1 0 2-1 4-2 0 0 1 0 1 1l2-1h1l-2 1c-2 1-3 3-5 5h-2l-2 1c1-2 1-3 3-5z" class="L"></path><path d="M370 587h1c1 2 1 3 2 5 1-1 1-2 2-3l1 2c1 0 1 0 2-1 2 2 2 2 3 5l3 8h2v-1c1 4 3 10 6 13h0c0 2 1 3 2 5v1h0c2 3 4 6 5 9 0 0-2 1-2 2 0 2 0 4-1 7v3l-1-2c-1 2-1 3-2 3v4 2h-2l5 4v16 32c-1-1-19-15-22-18v-2l-1-1v-8c1 0 1-2 1-3l-1-1v-1-2c2-4 1-8 1-12v-1-3-1h-1v-7l-1 1-1 4v-7c-1-2 0-7 0-9l-1 1v-1c-1-4-1-7-1-10 0-1-1-2-1-3s0-1-1-2h-1 0v-2c1 0 1-1 2-1v-4-1l1 1-1-4c0-2 0-3-1-5 0-1 0-1 1-2h1 1c0-2-1-4-1-6v-1h1v-3z" class="m"></path><path d="M369 591l2 2c1 0 1 1 1 2s0 0 1 1c-1 1 0 2-1 3h-1l-1-2c0-2-1-4-1-6z" class="k"></path><path d="M373 625s0-1-1-2v-1c1-1 1-2 1-3h0c0-2 0-3 2-4v1c0 1 1 4 0 5v2h0c-1 2-1 2-2 2z" class="o"></path><path d="M370 587h1c1 2 1 3 2 5 2 3 2 5 1 9-1-2-1-3-1-5-1-1-1 0-1-1s0-2-1-2l-2-2v-1h1v-3z" class="W"></path><path d="M368 604c0-2 0-3-1-5 0-1 0-1 1-2h1l3 14v6l-3-8v-1l-1-4z" class="R"></path><path d="M375 623l2 2c-1 1-1 1 0 3v-1 4l2 1h-1-1v6c-2-2-1-3-1-5h-1v1l-1 1v-3l-1-3v-4c1 0 1 0 2-2z" class="V"></path><path d="M373 592c1-1 1-2 2-3l1 2c1 0 1 0 2-1 2 2 2 2 3 5l3 8h2v-1c1 4 3 10 6 13h0c0 2 1 3 2 5v1c-1 0-1-1-2-2h-4c-1 0-3-1-4-1s-2-1-3-2l1-1v-1c-1-1-2 0-3 0l-1 1h-1c1-1 1-4 1-5-1-1-2-3-3-4 0-2-1-4-1-5 1-4 1-6-1-9z" class="n"></path><path d="M377 603l-1-4 2-1c1 1 2 4 2 5l1 3h-1l-3-3z" class="b"></path><path d="M380 608l1 1 1-1c0 1 1 2 1 3 1 1 2 2 4 3l-1 1-2-1-2 1h0v-1c-1-1-2 0-3 0 0-1 1-2 1-3s0-1-1-1l1-2z" class="Z"></path><path d="M382 615h0l1 1 5-1 4 4h-4c-1 0-3-1-4-1s-2-1-3-2l1-1z" class="k"></path><path d="M377 603l3 3v2l-1 2c1 0 1 0 1 1s-1 2-1 3l-1 1h-1c1-1 1-4 1-5-1-1-2-3-3-4l2-3z" class="s"></path><path d="M377 625c-1-1-1-1-1-2h0c0-1 1-1 1-2 1-3 2-4 4-5 1 1 2 2 3 2s3 1 4 1h4c1 1 1 2 2 2h0c2 3 4 6 5 9 0 0-2 1-2 2 0 2 0 4-1 7v3l-1-2c-1 2-1 3-2 3v4 2h-2c-2 0-4-1-6-3-1 0-2 0-2-1-3-3-5-3-6-7v-6h1 1l-2-1v-4 1c-1-2-1-2 0-3z" class="G"></path><path d="M388 619h4c1 1 1 2 2 2h0v4c-1-1-1-1-3-2-1-2-2-3-3-4z" class="w"></path><path d="M394 632l1 1c0 2 1 4 0 7-1-2-2-2-4-3h0v-1c1 0 1 0 2-1-1 0-2-1-3-2 1-1 3-1 4-1z" class="AE"></path><path d="M377 625c-1-1-1-1-1-2h0c0-1 1-1 1-2 1-3 2-4 4-5 1 1 2 2 3 2-2 2-5 3-6 6v3h-1v1c-1-2-1-2 0-3z" class="Z"></path><path d="M377 638v-1c1 0 1 0 2-1 1 1 1 1 2 3l-1 1c1 1 1 1 2 1 1-1 1-2 1-4 1 1 1 0 1 1v1 1 2c1 2 1 2 1 4-1 0-2 0-2-1-3-3-5-3-6-7z" class="F"></path><path d="M390 633l-1-1 1-1v1h2c0-1-1-1-1-2l-3-1-1-1c-1-1-2-2-2-3l2-2c1 1 2 1 4 1l3 8c-1 0-3 0-4 1z" class="t"></path><path d="M384 640h2l1 2v-2h1l1 1c-1 0-1 1-1 2 1 1 2 1 3 1h1v-1h1v4 2h-2c-2 0-4-1-6-3 0-2 0-2-1-4v-2z" class="D"></path><path d="M394 621c2 3 4 6 5 9 0 0-2 1-2 2 0 2 0 4-1 7v3l-1-2h0c1-3 0-5 0-7l-1-1-3-8v-1c2 1 2 1 3 2v-4z" class="u"></path><path d="M391 623c2 1 2 1 3 2 1 2 2 4 2 6 0 1-1 1-1 2l-1-1-3-8v-1z" class="V"></path><path d="M374 683l1-22c0-5-1-10 0-14 1-1 1-2 2-3 2 0 4 0 6 1 0 1 1 1 2 1 2 2 4 3 6 3l5 4v16 32c-1-1-19-15-22-18z" class="AO"></path><path d="M241 365h1c1 0 1-1 2-2l-1-1h1v-2l1-1c0 1 2 3 3 3 0 1-1 1 0 2 2 0 3 2 5 4l1 1v1c-1 0 0 0-1 1h2l1-1c0 1 0 2 1 3v1c1 0 1 1 2 1l1 1v-1c2 3 0 4 3 6v3c0 1 0 2 1 3l1-4c1 2 1 3 3 4l1 1c2 1 2 2 2 4l-2 2h0c-1 0-1 0-2 1-2 0-2 0-3 1 0 1-1 2-1 2v6c0 1 0 2-1 4 0 2 0 7 1 10l-1 1v2l-2-2v4h-1c0-1 0 0-1-1h-1v-2h0l-1-1h0c-1-1-1-1-1-2h-5l-1 1c1 1 1 3 2 4h-1c-1 0-2 0-3 1h-2c0-1 1-2 1-2l-1-1h0c-1 1-1 1-2 1h-1c-1 2 0 3-3 3h-1-2c-1 0-1 0-1 1-1-1-2-2-3-2l1-1c0 1 1 1 2 1h1l1-1s0-1 1-1h1v-1c0-1-1-1-1-2l-1 1h-3v-2h3 1c0-1-1-1 0-2 0-1 1-2 1-2v-2l2-1v-1l-3-1v-1l-2 1-1-1-1-1h-1v-1l1-1-2-2c0-2 1-1 2-2l-1-2c0-1 1-1 1-3h-2l-1 2c1 1 1 1 1 3l-1 1-1-1h-1-1v1l-1 1v-1l-1-1c-2 0-2 0-3-2h-1l-1-2h0 0-1c-1 0-1-2-2-3h-1c-1 0-1 0-2-1-1 0-1 1-2 1v1l-1 1v1c1 1 2 2 2 3h-1 0-2l-1 1h-2v3h-1l-1-1h-1c-1-1-1-1-2-1v-1h1 1c1 0 2-1 3-2-1-1-2 0-3-1 0-1 1-1 2-1 0-1 0-1 2-1v-1h-2l-2-2 1-1h3v-1l-1-1 2-2h-2 0-1v-3l2 2c2-1 2-1 3-3v-8-2c2 0 2 0 3-1 2 0 4 0 6-1l1-1 1 1c0-2 0-2 1-3h2l1 1c1-1 1-1 2-1l1-1c0-1 1-1 2-2l1-1c0 1 0 1 1 2h2 3l2 4h0 1l-1-1v-2h-1l1-2z" class="AE"></path><path d="M263 404c0 1 0 2-1 4 0 2 0 7 1 10l-1 1v2l-2-2v-3c-1-1-1-2-2-3h2c0-2 0-4 1-5l1-1c0-1 0-2 1-3z" class="V"></path><path d="M226 382l7 1v1h-2c0 1-1 2-1 3h2v2l1 1 1-1v1 1h-2c-2 1-3 2-3 3l-1 1v1h-1c-1-1-1-2-1-3v-1l-1 1c-1 0-1-1-2-1h-1v1h1v1h2v1 1l-2-1v1h-2 0 0-1c-1 0-1-2-2-3 1-1 2 0 3-1l-1-1h0v-1c1-1 2-1 4-1v-2c2-2 2-1 4-3l-2-2z" class="D"></path><path d="M221 371c0 1 1 2 1 3l-1 1s-1 1-2 1c1 1 1 1 2 1v1h-1 0-2c2 1 3 2 4 3h0c1 0 1 0 2-1 1 1 2 1 2 2l2 2c-2 2-2 1-4 3v2c-2 0-3 0-4 1v1h0l1 1c-1 1-2 0-3 1h-1c-1 0-1 0-2-1-1 0-1 1-2 1v1l-1 1v1c1 1 2 2 2 3h-1 0-2l-1 1h-2v3h-1l-1-1h-1c-1-1-1-1-2-1v-1h1 1c1 0 2-1 3-2-1-1-2 0-3-1 0-1 1-1 2-1 0-1 0-1 2-1v-1h-2l-2-2 1-1h3v-1l-1-1 2-2h-2 0-1v-3l2 2c2-1 2-1 3-3v-8-2c2 0 2 0 3-1 2 0 4 0 6-1z" class="G"></path><path d="M221 371c0 1 1 2 1 3l-1 1s-1 1-2 1c1 1 1 1 2 1v1h-1 0-2c0-1 0 0-1-1 0 0-1 0-1-1h-1c-1 2 0 2 0 3-1 1-1 1-1 2h2v1c-1 1-3 1-4 1v-8-2c2 0 2 0 3-1 2 0 4 0 6-1z" class="D"></path><path d="M241 365h1c1 0 1-1 2-2l-1-1h1v-2l1-1c0 1 2 3 3 3 0 1-1 1 0 2 2 0 3 2 5 4l1 1v1c-1 0 0 0-1 1h2l1-1c0 1 0 2 1 3v1c1 0 1 1 2 1l1 1v-1c2 3 0 4 3 6v3c0 1 0 2 1 3l1-4c1 2 1 3 3 4l1 1c2 1 2 2 2 4l-2 2h0c-1 0-1 0-2 1-2 0-2 0-3 1 0 1-1 2-1 2v6c-1 1-1 2-1 3l-1 1-1-4c-2-1-3-3-4-5-1 0-2 0-2-1v-1h-1-1l1-2c-1-1-1-1-2-1l-1 1h-1l1-2v-1h-1c0-1-1-2-2-3h0c0-1 0-3-1-4h0v-1-1h-1l1-1-1-1c0-1 0-2-1-3 0-1 0-1-1-2l1-1c1-1 1-2 2-3v-3l-1-1h-1-1l1-1c-1-1-1-1-3-2z" class="m"></path><path d="M257 398l-3-3 1-1h-1-1l-2-2c-1-2-3-3-3-5 0-3 2-7 4-9 0 1 1 2 2 2v1l-2-1c0 1 1 2 1 3 1 1 0 0 0 1s1 2 2 2v1l1 1v1c0 2 1 3 0 5h0l1-1 2 1 1-1v3h-1c2 2 1 2 1 5h0l-3-3z" class="F"></path><path d="M260 375c2 3 0 4 3 6v3c0 1 0 2 1 3l1-4c1 2 1 3 3 4l1 1c2 1 2 2 2 4l-2 2h0c-1 0-1 0-2 1-2 0-2 0-3 1 0 1-1 2-1 2v6c-1 1-1 2-1 3l-1 1-1-4c-2-1-3-3-4-5l1-1 3 3h0c0-3 1-3-1-5h1v-3l-1 1-2-1-1 1h0c1-2 0-3 0-5v-1l1-1h1c0 1 0 2 1 2h1v-1-1c0-2-1-3-2-4h1 1v-1c1-1 0-1-1-2 1-1 1-1 1-2l-1-1 1-1v-1z" class="w"></path><path d="M265 383c1 2 1 3 3 4l1 1c2 1 2 2 2 4l-2 2h0c-1 0-1 0-2 1-2 0-2 0-3 1 0 1-1 2-1 2 0-3 0-8 1-10v-1l1-4z" class="Z"></path><path d="M265 383c1 2 1 3 3 4l1 1c2 1 2 2 2 4l-2 2c0-1 0-2-1-3-1-2-1-2-3-2l-1-1v-1l1-4z" class="U"></path><path d="M500 223c5-7 9-13 11-21 3 11 8 22 19 29h1l2 1c0 1 0 2 1 2l1 1c2 2 5 4 7 3h1 0c2-2 3-3 3-6v-3-1c2 3 3 4 2 7l-1 5c3 3 5 4 8 6 1 0 2 0 2-1 3 1 3 2 4 4 0 1 2 2 3 3 1 2 2 4 3 7l1 2h1c1 3 3 7 4 10v1c-1 1-2 2-1 4 0 1 0 2 1 3l3 6v2l-1 1 3 3 2 5v1c0 2 2 4 3 5h-2c-1 0-2-1-2-2l-1-1-2-2-1 1c0-2 0-2-1-3l-1 1c0 3-1 4-2 5s-2 1-3 1h-1l-1-1c-2-1-3-3-5-4h-1v-1h2c-1-1-2-1-3-2v-1h0 0c-1-1-1-2-3-2v1h-1-1v-1c-1 0-2 0-3 1-1-1-1-2-1-2h-2c0 1 0 1-1 1-2 1-3 1-5 2v-1c-2-1-4-4-6-5s-3-2-4-3h1l-3-3h1c0-2-1-3-2-4h0c-2-1-3-2-5-3 0-1-1-1-2-1l-3-3h4 0c1 0 2 0 3 1 0 1 1 1 2 2v-2h1l1 3c0-1 1-2 1-2 1-1 1-1 1-2s0-1-1-2c0-1-2 0-2-2h2c1 0 0 1 2 1v-3l-3-3c2 1 3 2 5 3-1-2-3-3-4-5v-2l1 1c0 1 0 1 1 2l1 2 1-1v-1c-1 0-2-2-1-3h0c-1-2-2-2-3-3 0-1-1-1-2-2v-2c0-1 3-1 4-2l7 3h3l-3-2-11-6h0c1 0 2 0 3 1l1-1c-2-1-3-2-4-3-3-4-7-7-11-10h0c-2-1-5-1-7-3h0c-1 0-2 0-3 1s-2 1-3 2c-1 0-1 0-2 1-2 2-5 3-7 3h-1l-3 1c-2 1-4 3-5 3 0-1 1-2 2-3 2-1 3-2 6-3v-1l3-3 2-3c1-1 1-1 0-2z" class="S"></path><g class="a"><path d="M540 249c-1-3-3-4-6-6l1-1 16 10 1 2h-1c-1 0-1-1-2-1-1-1-2-2-4-3v1c1 2 4 3 5 4 3 2 7 6 8 9-3-3-6-7-10-9-1-2-3-3-5-4l-3-2z"></path><path d="M541 242c2 1 5 4 7 4 1 0 0-1 1 0 2 2 4 4 6 5s3 3 4 5c0 1 1 2 2 4 0 1 0 1-1 2h-1c-2-2-6-5-7-7v-1l-1-2h1l-6-6c-2-1-4-2-5-4z"></path></g><path d="M552 252c2 1 4 3 5 5s2 3 3 5c-2-2-6-7-8-7v-1l-1-2h1z" class="C"></path><path d="M501 230c1-1 2-2 4-3s6-5 8-4l1 1h1c2 1 6 5 8 7h-1c-1-1-2-1-4-1h0c-2-1-5-1-7-3h0c-1 0-2 0-3 1s-2 1-3 2c-1 0-1 0-2 1-2 2-5 3-7 3h-1l-3 1c-2 1-4 3-5 3 0-1 1-2 2-3 2-1 3-2 6-3v-1l3-3 3 2z" class="a"></path><path d="M495 232v-1l3-3 3 2c-2 1-4 2-6 2z" class="E"></path><g class="J"><path d="M508 228c1-1 2-2 3-2h1 3c0 1 1 2 1 3l2 1c-2-1-5-1-7-3h0c-1 0-2 0-3 1z"></path><path d="M518 230c2 0 3 0 4 1h1c5 5 12 8 18 11 1 2 3 3 5 4l6 6h-1l-16-10-1 1c3 2 5 3 6 6l-11-6h0c1 0 2 0 3 1l1-1c-2-1-3-2-4-3-3-4-7-7-11-10z"></path></g><defs><linearGradient id="P" x1="563.95" y1="270.103" x2="556.06" y2="269.295" xlink:href="#B"><stop offset="0" stop-color="#151716"></stop><stop offset="1" stop-color="#2c272a"></stop></linearGradient></defs><path fill="url(#P)" d="M543 251c2 1 4 2 5 4 4 2 7 6 10 9s4 6 6 10c0 1 0 2 1 2 0 2 1 2 1 4-2 0-3 0-5-1v-1c-1-1-2-2-3-4 0-1-1-2-2-3l-6-10-2-2c-2 0-3-2-5-3l-4-3v-1l2 2 1-1-2-2h3z"></path><path d="M550 261c3 2 5 5 8 8 1 2 2 6 4 7h1 2c0 2 1 2 1 4-2 0-3 0-5-1v-1c-1-1-2-2-3-4 0-1-1-2-2-3l-6-10z" class="B"></path><defs><linearGradient id="Q" x1="542.435" y1="259.638" x2="534.875" y2="259.209" xlink:href="#B"><stop offset="0" stop-color="#52504f"></stop><stop offset="1" stop-color="#696965"></stop></linearGradient></defs><path fill="url(#Q)" d="M533 248l7 3 2 2-1 1-2-2v1l4 3c2 1 3 3 5 3l2 2 6 10c1 1 2 2 2 3 1 2 2 3 3 4v1c-2-1-4-2-5-4l-7-6-2 1 6 6h0c0 1 1 2 1 3h1v1h-1l-1-1-1-1c-3-3-7-6-11-9h1c-3-2-5-3-7-5-1-2-3-3-4-5v-2l1 1c0 1 0 1 1 2l1 2 1-1v-1c-1 0-2-2-1-3h0c-1-2-2-2-3-3 0-1-1-1-2-2v-2c0-1 3-1 4-2z"></path><path d="M547 270c-2-1-4-3-4-5-1-2-2-3-1-5h1c0 4 4 6 6 9l-2 1z" class="J"></path><path d="M531 259v-2l1 1c0 1 0 1 1 2l1 2 1-1v-1c1 1 2 3 3 4h2s1 4 2 5c-3-2-5-3-7-5-1-2-3-3-4-5z" class="e"></path><path d="M556 275v-1l-3-3c-2-1-6-6-8-8h1c2 1 3 4 5 5l1 1c1 0 1 1 2 1s1 1 2 1c1 1 2 2 2 3 1 2 2 3 3 4v1c-2-1-4-2-5-4z" class="Y"></path><path d="M542 257l1-1c2 1 3 3 5 3l2 2 6 10c-1 0-1-1-2-1s-1-1-2-1c0-1-1-2-1-3l-9-9z" class="C"></path><defs><linearGradient id="R" x1="539.954" y1="253.147" x2="529.037" y2="251.409" xlink:href="#B"><stop offset="0" stop-color="#514f4f"></stop><stop offset="1" stop-color="#73746f"></stop></linearGradient></defs><path fill="url(#R)" d="M533 248l7 3 2 2-1 1-2-2v1l4 3-1 1h-3-1-1l-6-3c0-1-1-1-2-2v-2c0-1 3-1 4-2z"></path><path d="M530 261c2 1 3 2 5 3 2 2 4 3 7 5h-1c4 3 8 6 11 9l1 1 1 1h1c1 0 2 0 3 1s3 2 4 3l4-2c0 1 1 3 1 4s1 1 1 2v1l-2-2h-1c1 3 2 6 4 8 1 1 1 2 2 2l-1 1h-2c-2 1-1 1-2 3-2-1-3-3-5-4h-1v-1h2c-1-1-2-1-3-2v-1h0 0c-1-1-1-2-3-2v1h-1-1v-1c-1 0-2 0-3 1-1-1-1-2-1-2h-2c0 1 0 1-1 1-2 1-3 1-5 2v-1c-2-1-4-4-6-5s-3-2-4-3h1l-3-3h1c0-2-1-3-2-4h0c-2-1-3-2-5-3 0-1-1-1-2-1l-3-3h4 0c1 0 2 0 3 1 0 1 1 1 2 2v-2h1l1 3c0-1 1-2 1-2 1-1 1-1 1-2s0-1-1-2c0-1-2 0-2-2h2c1 0 0 1 2 1v-3l-3-3z" class="B"></path><path d="M545 287h1 1 3 1c2 2 3 4 5 4v1h-1-1v-1c-1 0-2 0-3 1-1-1-1-2-1-2h-2c-1 0-2-1-3-3z" class="O"></path><path d="M562 296l2-1v-2l1-1v1c1 1 1 2 2 3 1 0 1 0 2-1 1 1 1 2 2 2l-1 1h-2c-2 1-1 1-2 3-2-1-3-3-5-4h-1v-1h2z" class="a"></path><path d="M530 261c2 1 3 2 5 3 2 2 4 3 7 5h-1c1 2 4 4 5 6 1 1 1 3 2 4s2 2 3 2l7 5v1h-2l-2 1-1-1h-2-1-3-1-1l-1-1-1-2 1-1c0-1-3-3-4-5h-1 1l2 1h0l-6-7-2-1h0l-2-1c0-1 0-1-1-2 0-1-2 0-2-2h2c1 0 0 1 2 1v-3l-3-3z" class="M"></path><path d="M550 287l-5-5 6 3c2 0 3 1 5 2l-2 1-1-1h-2-1z" class="E"></path><path d="M533 264l6 5c0 1 0 1-1 1l1 2h-1l-1-1s-1-1-2-1 0 0-1 1h0l-2-1c0-1 0-1-1-2 0-1-2 0-2-2h2c1 0 0 1 2 1v-3z" class="L"></path><path d="M523 270c1 0 2 0 3 1 0 1 1 1 2 2v-2h1l1 3c0-1 1-2 1-2 1-1 1-1 1-2l2 1h0l2 1 6 7h0l-2-1h-1 1c1 2 4 4 4 5l-1 1 1 2 1 1c1 2 2 3 3 3 0 1 0 1-1 1-2 1-3 1-5 2v-1c-2-1-4-4-6-5s-3-2-4-3h1l-3-3h1c0-2-1-3-2-4h0c-2-1-3-2-5-3 0-1-1-1-2-1l-3-3h4 0z" class="q"></path><path d="M529 277l6 4h0v2l-2 1h0l-3-3h1c0-2-1-3-2-4z" class="P"></path><path d="M532 270l2 1h0l2 1c-1 1-1 1-1 2l1 1-1 1c-1-1-1-1-2 0-1-1-2-2-3-2 0-1 1-2 1-2 1-1 1-1 1-2z" class="AB"></path><path d="M523 270c1 0 2 0 3 1 0 1 1 1 2 2v-2h1l1 3c1 0 2 1 3 2 2 1 3 2 4 3h0l-3-1-1 1c-1-1-3-2-5-3l-4-2c0-1-1-1-2-1l-3-3h4 0z" class="AD"></path><path d="M523 270h0c2 2 5 3 5 6l-4-2c0-1-1-1-2-1l-3-3h4z" class="AL"></path><path d="M540 278c1 2 4 4 4 5l-1 1 1 2 1 1c1 2 2 3 3 3 0 1 0 1-1 1-2 1-3 1-5 2v-1c-2-1-4-4-6-5s-3-2-4-3h1 0l2-1v-2h0c2 0 3 3 5 3l-1-1 1-2-1-1v-1l1-1z" class="K"></path><path d="M535 281c1 1 1 2 2 3l4 4 2 2h-1c-1-1-2-2-3-2-2-1-4-2-6-4l2-1v-2z" class="L"></path><path d="M540 278c1 2 4 4 4 5l-1 1c-2 0-1 0-3-1 1 3 2 2 1 5l-4-4c-1-1-1-2-2-3h0c2 0 3 3 5 3l-1-1 1-2-1-1v-1l1-1z" class="f"></path><defs><linearGradient id="S" x1="212.025" y1="170.867" x2="281.231" y2="193.517" xlink:href="#B"><stop offset="0" stop-color="#888984"></stop><stop offset="1" stop-color="#b4b4b1"></stop></linearGradient></defs><path fill="url(#S)" d="M190 154l8 10c1 0 1 0 1-2l-4-4 1-1c1 2 3 4 4 5 4 2 13 3 17 3h2c3 0 7 0 10 1 2 0 4-1 6 0h0c1 1 2 1 3 1l1 1c-2 1-5-1-7 0 3 1 6 1 8 2h19c1-1 3 0 4 0 2 0 5 1 8 1h1l3 1c1 1 4 2 5 3 4 1 8 1 11 2 2 0 4 0 6 1v-1c2 0 6 0 8 1 4 0 8-1 12-2h6 6c-1 1-3 1-5 2 0 0-1 0-1 1h0 7 4c3-2 5-2 9-2-5 3-10 3-15 5l-12 7c-2 0-3 1-5 2l-3 1-3 3-3 3h-1l-6 6-2 2-1 3v1c1 2 0 2 0 4v1c1 0 1 0 1-1 1-1 1 0 1-1l2-2c-1 2-2 5-2 7 0 1 0 2-1 3 0 2-1 4-1 6s0 4-1 5l-1-1c-1-3-1-5-3-8-5-4-10-6-16-9l-6-4h1l-1-1c-1-1-2-1-3-2h-1c1-1 3-1 4-1 1 1 2 1 4 1-3-4-8-4-11-7-3-2-6-3-8-4-3-2-6-5-9-5 0 0 0 1-1 1-1-1-2-1-3-1l-1 1c-11-6-20-13-32-16 3-1 5 0 7 0l-1-1c-1-1-3-1-4-1l-1-1h-1c-2-1-2-1-4-1l-2-5c-3-2-4-5-6-7l-2-6z"></path><path d="M232 181h0l1 1h1c1 0 2 1 4 2l-2 1 1 2h0l-2-1c-2-1-5-2-7-4h1 2l1-1zm54 2c1 0 3-1 4 0-2 2-9 6-12 7l5 1c2 1 3 2 5 3 0 1 1 2 1 3l1 2 1 1c-2 0-2 0-3 1h0l3 3-3-1-1-1c-1 0-2 0-3-1h-2c-1 0-1 0-2-1h-3-4 0c1-1 1 0 1-1v-1h-3c-1-1-2-1-3-2-2 0-4-1-6-2h-1 1l1-1h1c1 0 1 1 2 1 1-1 2-1 3-1h0c1-1 1 0 1-1h-4-3c-1 1-1 1-2 0h-1-3v-1h4c2 0 4 0 7-1 6-1 12-4 18-7z" class="e"></path><path d="M288 194c0 1 1 2 1 3l1 2c-2 0-3 0-4-1s-2-1-2-2c-1 1-2 1-3 1l-1-2c-1 0-2 0-3-1h1 6v1l1-1 2 1 1-1z" class="L"></path><path d="M286 183c1 0 3-1 4 0-2 2-9 6-12 7l5 1c2 1 3 2 5 3l-1 1-2-1c-1-1-4-3-6-3h0c-2 0-3-1-4-1l-7 1h-7c2 0 4 0 7-1 6-1 12-4 18-7z" class="C"></path><path d="M258 200h5 1c1-1 4 0 6 0 0 0 1 1 2 1s1 0 1-1h4 3c1 1 1 1 2 1h2c1 1 2 1 3 1l1 1 3 1 2 2-1 3v1c1 2 0 2 0 4v1c1 0 1 0 1-1 1-1 1 0 1-1l2-2c-1 2-2 5-2 7 0 1 0 2-1 3 0 2-1 4-1 6s0 4-1 5l-1-1c-1-3-1-5-3-8-5-4-10-6-16-9l-6-4h1l-1-1c-1-1-2-1-3-2h-1c1-1 3-1 4-1 1 1 2 1 4 1-3-4-8-4-11-7z" class="AB"></path><path d="M258 200h5 1c1-1 4 0 6 0 0 0 1 1 2 1-2 1-5 0-6 1h0c2 1 4 4 6 6h-1c-1 0-2-1-2-1-3-4-8-4-11-7z" class="AD"></path><path d="M265 209v-1l5 2 1-1 3 2c1 0 2-1 3 0s3 1 5 1l2 1v1l-1 1c-2 0-2 0-3 1-1-1-3-1-4-2-3-1-7-2-10-4l-1-1z" class="q"></path><path d="M274 211c1 0 2-1 3 0s3 1 5 1c-1 1-2 1-2 1v1c-2 0-2 0-3-1s-2-1-3-2z" class="X"></path><path d="M284 213h0v-1c-1 0-1-1-2-1l-3-3h-1l1-1c-2-1-4-3-6-4-1 0-1 0-2-1h3l1 1c3 0 5 1 7 3v1l-2-1v1c1 0 1 1 2 1h1c1 1 3 1 4 2 0 3 1 4 3 7l-1 1c-1-1-3-1-3-3l-2-1v-1z" class="L"></path><path d="M286 215h0c-1-3-1-4-3-5v-2c1 1 3 1 4 2 0 3 1 4 3 7l-1 1c-1-1-3-1-3-3z" class="q"></path><path d="M266 210c3 2 7 3 10 4 1 1 3 1 4 2 1-1 1-1 3-1l1-1 2 1c0 2 2 2 3 3l1-1h1l-1 6c-1 0-2 0-2-1l-1 1c-5-4-10-6-16-9l-6-4h1z" class="AN"></path><path d="M284 214l2 1c0 2 2 2 3 3l1-1h1l-1 6c-1 0-2 0-2-1-1-2-6-5-8-6 1-1 1-1 3-1l1-1z" class="K"></path><path d="M287 210v-6l1-1 3 1 2 2-1 3v1c1 2 0 2 0 4v1c1 0 1 0 1-1 1-1 1 0 1-1l2-2c-1 2-2 5-2 7 0 1 0 2-1 3 0 2-1 4-1 6s0 4-1 5l-1-1c-1-3-1-5-3-8l1-1c0 1 1 1 2 1l1-6h-1c-2-3-3-4-3-7z" class="L"></path><path d="M287 210v-6l1-1 3 1 2 2-1 3-1 8h-1c-2-3-3-4-3-7z" class="Y"></path><defs><linearGradient id="T" x1="240.818" y1="181.172" x2="244.284" y2="170.573" xlink:href="#B"><stop offset="0" stop-color="#777775"></stop><stop offset="1" stop-color="#8c8d88"></stop></linearGradient></defs><path fill="url(#T)" d="M190 154l8 10c1 0 1 0 1-2l-4-4 1-1c1 2 3 4 4 5 4 2 13 3 17 3h2c3 0 7 0 10 1 2 0 4-1 6 0h0c1 1 2 1 3 1l1 1c-2 1-5-1-7 0l-1 1c1 0 1 0 2 1l16 3c2 0 3 0 5 1 1 0 2-1 4 0 1 0 1 0 2 1h2c1 0 1 0 2 1h2c1 0 1 0 3 1h2c1 0 1 0 3 1h2c1 0 1 0 2 1h2c1 0 1 0 2 1h1l2 1c-2 1-3 1-4 2h-1c-1 0-3 1-5 1h0l-3-1h-2c-5-2-10-2-15-3-8-2-17-5-24-4h-2-2v1h-2-2 0v2l-1-1-4-1c-1-1-2-1-3-2s-3-1-3-1v2h-1l-1-1c-1-1-3-1-4-1l-1-1h-1c-2-1-2-1-4-1l-2-5c-3-2-4-5-6-7l-2-6z"></path><path d="M202 165h3c1 1 3 2 4 2l6 2c3 2 4 4 7 5h-4 1l-14-7c-1-1-2-1-3-2z" class="i"></path><path d="M196 157c1 2 3 4 4 5 4 2 13 3 17 3h2c3 0 7 0 10 1 2 0 4-1 6 0h0c1 1 2 1 3 1l1 1c-2 1-5-1-7 0l-1 1c1 0 1 0 2 1l16 3h0c-1 1-3 1-4 0h-3c-2 0-3 0-4-1h-3c-3-2-6-2-8-3-4-1-9-4-13-3h0l2 2c-1 1-4-2-6-1h-1c-1 0-3-1-4-2h-3l-3-3-4-4 1-1z" class="q"></path><path d="M190 154l8 10c1 0 1 0 1-2l3 3c1 1 2 1 3 2l14 7h-1c2 1 2 2 4 2l1 1h0v2l-1-1-4-1c-1-1-2-1-3-2s-3-1-3-1v2h-1l-1-1c-1-1-3-1-4-1l-1-1h-1c-2-1-2-1-4-1l-2-5c-3-2-4-5-6-7l-2-6z" class="X"></path><path d="M190 154l8 10c1 0 1 0 1-2l3 3c1 1 2 1 3 2l1 1-1 1-2-1h0l3 3h-1c-3 0-4-3-7-4-3-2-4-5-6-7l-2-6z" class="AF"></path><path d="M232 168c3 1 6 1 8 2h19c1-1 3 0 4 0 2 0 5 1 8 1h1l3 1c1 1 4 2 5 3 4 1 8 1 11 2 2 0 4 0 6 1v-1c2 0 6 0 8 1 4 0 8-1 12-2h6 6c-1 1-3 1-5 2 0 0-1 0-1 1h0 7 4c3-2 5-2 9-2-5 3-10 3-15 5l-12 7c-2 0-3 1-5 2l-3 1-3 3-3 3h-1l-6 6-2 2-2-2-3-3h0c1-1 1-1 3-1l-1-1-1-2c0-1-1-2-1-3-2-1-3-2-5-3l-5-1c3-1 10-5 12-7-1-1-3 0-4 0-1-1-1-1-1-2l-2-1h-1c-1-1-1-1-2-1h-2c-1-1-1-1-2-1h-2c-2-1-2-1-3-1h-2c-2-1-2-1-3-1h-2c-1-1-1-1-2-1h-2c-1-1-1-1-2-1-2-1-3 0-4 0-2-1-3-1-5-1l-16-3c-1-1-1-1-2-1l1-1z" class="AL"></path><path d="M291 188h4 0v1l-5 1h-1-3 0l5-2z" class="AN"></path><path d="M303 189c4 0 9-2 13 0-2 0-3 1-5 2l-3 1h-5l3-2c-1 0-2 0-3-1z" class="P"></path><path d="M306 190h4l1 1-3 1h-5l3-2z" class="L"></path><path d="M330 179h4c3-2 5-2 9-2-5 3-10 3-15 5-1-1-3-1-4-1h-6 0 4c2-1 5-1 7-2h1z" class="AD"></path><path d="M240 170h19c1-1 3 0 4 0 2 0 5 1 8 1h1l3 1c1 1 4 2 5 3 4 1 8 1 11 2 2 0 4 0 6 1v-1c2 0 6 0 8 1h-10c-3 0-6 1-9 0-4-1-8-2-11-3-5-2-10-1-14-3-1-1-2-1-3-1h-3-2-2c-4-1-7 0-11-1z" class="P"></path><path d="M283 191h4c1 1 1 1 3 1 3-1 5-1 7-2 2 0 4-1 6-1 1 1 2 1 3 1l-3 2h5l-3 3-3 3h-1l-6 6-2 2-2-2-3-3h0c1-1 1-1 3-1l-1-1-1-2c0-1-1-2-1-3-2-1-3-2-5-3z" class="e"></path><path d="M299 193h0v-1h4 5l-3 3c-2-2-3-2-6-2z" class="AB"></path><path d="M293 195c1-1 1-1 2-1l1-1h1v1c1 0 2 0 2-1 3 0 4 0 6 2l-3 3h-1l-1-1h-2-2c-1-1-2-2-3-2z" class="AR"></path><path d="M293 195c1 0 2 1 3 2h2 2l1 1-6 6-2 2-2-2-3-3h0c1-1 1-1 3-1l-1-1-1-2c2-1 3-1 4-2z" class="AF"></path><path d="M293 195c1 0 2 1 3 2h2l-1 2h-1-1v-1c-1 1-1 2-1 4-1-1-1 0-1-1-1-1-1-1-2-1l-1-1-1-2c2-1 3-1 4-2z" class="AB"></path><defs><linearGradient id="U" x1="417.896" y1="261.779" x2="505.597" y2="267.137" xlink:href="#B"><stop offset="0" stop-color="#010000"></stop><stop offset="1" stop-color="#2a2c2e"></stop></linearGradient></defs><path fill="url(#U)" d="M476 237c0 1 1 1 2 2 2-1 5-3 6-5 1-1 0-6 1-9 0-2 2-4 3-6 0 3-2 8 1 12h0l-1 1h4s-1 1-1 2h-1l-1 1h0c-1 1-2 2-2 3 1 0 3-2 5-3l3-1h1c2 0 5-1 7-3 1-1 1-1 2-1 1-1 2-1 3-2s2-1 3-1h0c2 2 5 2 7 3h0c4 3 8 6 11 10 1 1 2 2 4 3l-1 1c-1-1-2-1-3-1h0l11 6 3 2h-3l-7-3-15-7c-3-1-6-3-8-3v1l-1 1h0c-7 1-15 5-22 9l-6 3v1h1 1l-1 1v3c2 0 3-1 4-2l1 1-1 1c0 1 1 2 1 3h0c-1 1-1 1-1 2h0l2 1-7 7-4 4c-2 2-3 3-5 4h2c-1 1-3 2-3 3-2 2-5 4-7 5 0 1 1 2 2 3h0c0 2 0 1 1 2l-1 2c2 1 3 0 4 2h0v1l-5 2c-2 1-4 3-5 5l-1 3c-1 1-1 2-1 3s1 3 1 3l-1 2 1 3 1 1c-1 1-1 2-2 3h2l-1 1-1 1v1l2-1v1c-1 1-2 1-2 2h0l2-1 1 1-3 3-2 2-2-2-1-2c-1 1-1 1-2 1l-1-2c-2 0-5-1-6 0l-2 2v-2l-3-3-2-2h-1l-1-1h-2c-2-1-5-1-6-3h0c-3-1-5-2-7-3v-1l-2-2v-1l1-1 1 1h1l2-1h-1c-1-2-2-2-4-2l2-1c4 1 3 2 6 1l-3-2-5-1 1-1-3-1v-1c2 0 4 0 6 1 1 0 1 1 1 1h4c-1-1-1-1-1-2l1-1h2 0v-2l2 1v-1c0-1-1-1-1-1 0-2 1-3 1-5 1-2 2-3 2-5h0l-1-1c1-1 1 0 1-1v-3l5-5 3-3c0-1 0-3 1-4v-1l1-1c3-4 3-11 2-15h3c1 1 3 2 4 4l1-1c3-3 4-6 5-10 0-4 0-7 3-11h1c-1 3-1 4 0 7h0l2 4h1c3-1 6-3 8-5v-3l2 1 1-2z"></path><path d="M419 303c1 0 2 0 3 1h6 1c1-1 2-1 3-2l1-1c0 1 0 1 1 2h2c1-1 2-2 3-4 0-1 1-1 2-2l1 1h0 0l1 2h-1c-2 1-4 2-5 4-1 0-2 1-3 1-2 0-2 0-4 1-2 0-1-1-2-1h-5l-5-1 1-1z" class="E"></path><path d="M423 305h5c1 0 0 1 2 1 2-1 2-1 4-1 1 0 2-1 3-1 1-2 3-3 5-4h1v1c1 2 1 3 1 5-2-2-2-2-2-4-1 0-1 0-1 1l-2 2-1 1c-1 1-2 1-3 1v-1c-1 0-1 1-1 1-1 1-4 1-5 0h-2-1l-3-2z" class="J"></path><path d="M435 307c1 0 2 0 3-1l1-1 2-2c0-1 0-1 1-1 0 2 0 2 2 4l1 1h1v-1-1h1c1 2 1 2 1 4 1 1 1 2 2 3l1 1-2 2-1 1v-4c-1-2-4-4-6-5l-2-1c-1 1-1 1-1 2-1 0-3 0-4-1z" class="K"></path><path d="M445 298v2l-1-1c0-1 0-1 1-2v-2c1-1 1-1 1-2 1-2 3-4 3-6-1 0-1 0-2-1 0-2 0-3 1-4l2-1 1 1c0-1 0-1 1-1v2h1 1c0 1 0 1-1 2-1 0-1 1-1 2-1 1-1 2-2 3 0 1-1 2-2 3s-1 3-1 4v1h-1v-2l-1 2z" class="E"></path><defs><linearGradient id="V" x1="464.623" y1="274.892" x2="464.378" y2="262.108" xlink:href="#B"><stop offset="0" stop-color="#232124"></stop><stop offset="1" stop-color="#3a3835"></stop></linearGradient></defs><path fill="url(#V)" d="M467 263v-1c1 0 2-1 3-1 0 1 1 1 1 1 0 1 0 2-1 3v1h-1c-3 2-4 6-7 8l-6 6c1-4 4-8 6-11 2-2 4-4 5-6h0z"></path><path d="M433 287c2-2 3-3 5-3 1 1 0 3 0 4-1 5-2 7-6 10h-1c0-1-1-1-1-1 0-2 1-3 1-5 1-2 2-3 2-5h0z" class="AZ"></path><path d="M445 298l1-2v2h1c1 3 3 6 5 8l3 4-3 3v-1l1-1v-2h-2l-1 3c-1-1-1-2-2-3 0-2 0-2-1-4h-1v1 1h-1l-1-1c0-2 0-3-1-5l2-1v-2z" class="J"></path><path d="M445 298l1-2v2l1 2s-1 1-1 2c-1 0 1 2 1 3h0-1v1 1h-1l-1-1c0-2 0-3-1-5l2-1v-2z" class="C"></path><path d="M491 238h-1c-1 2-3 2-5 3v2c5-1 8-4 13-5-2 1-5 2-7 3-3 2-7 3-10 4-2 2-5 4-7 5-6 1-8 7-13 8 3-3 6-7 10-9 2-1 4-3 6-4 1 0 1 0 2-1 2-1 3-3 6-4l6-2z" class="J"></path><path d="M458 246c0-4 0-7 3-11h1c-1 3-1 4 0 7-1-1-2-2-2-3h0c-1 2-1 3-1 4v2h0c0-1 0-1 1-1v2h-1v1c0 1-1 3-1 4v1c0 1-1 1 0 2 0 4-2 7-4 10-1 0-2 0-3-1 0-1 1-3 1-4v-2l1-1c3-3 4-6 5-10z" class="B"></path><path d="M461 286h3c0 1 1 2 2 3h0c0 2 0 1 1 2l-1 2-3 1-4-4v-1l-1 1c0 1 0 1-1 2v2l-1 1c-2 0-3 2-4 3h-1l-1-1c1-2 2-3 4-4 0-1 2-3 3-4h0v-1l1-1h0 0l1 1h0v-2h1 1z" class="S"></path><path d="M479 251l21-10c2-2 7-4 10-4 4-1 12 4 16 5 1 1 2 1 3 1l11 6 3 2h-3l-7-3-15-7c-3-1-6-3-8-3-8 2-17 7-24 10-2 1-5 3-7 3z" class="AB"></path><path d="M457 294v-2c1-1 1-1 1-2l1-1v1l4 4 3-1c2 1 3 0 4 2h0v1l-5 2c-2 1-4 3-5 5l-1 3-2 1-1-1 3-3-1-1c-1 1 0 1-1 0l1-1v-1l-4 2c-1-1-2-2-2-4 1-1 2-3 4-3l1-1z" class="B"></path><path d="M457 294l1-1 2 2-2 2-1-1-1 1h-1l1-2 1-1z" class="C"></path><path d="M458 302h1c1-2 4-5 6-6v1h-1l1 1c-2 1-4 3-5 5l-1 3-2 1-1-1 3-3-1-1z" class="Y"></path><path d="M510 238v1l-1 1h0c-7 1-15 5-22 9l-6 3-2 1c1 1 1 0 2 1-2 1-2 1-3 3v1 1 1l1-1c0 1-2 3-3 4-1-1-1-1-1-2 1-1 1-2 2-2-2 0-4 2-6 3 0 0-1 0-1-1-1 0-2 1-3 1v1l-3 2c1-3 6-7 8-9h0l-2 1c-3 2-7 6-9 9h-1a79.93 79.93 0 0 1 13-13c2-1 4-2 6-2s5-2 7-3c7-3 16-8 24-10z" class="M"></path><path d="M454 302l4-2v1l-1 1c1 1 0 1 1 0l1 1-3 3 1 1 2-1c-1 1-1 2-1 3s1 3 1 3l-1 2 1 3 1 1c-1 1-1 2-2 3h2l-1 1-1 1v1l2-1v1c-1 1-2 1-2 2h0l2-1 1 1-3 3-2 2-2-2-1-2c-1 1-1 1-2 1l-1-2-1-3v-3h0c-1-1-1-3-1-4l1-1 2-2-1-1 1-3h2v2l-1 1v1l3-3-3-4 2-4z" class="C"></path><path d="M450 312l1-3h2v2l-1 1v1l3-3c1 1 1 1 2 3 0 0-1 0 0 1 0 2 0 3 1 4 0 1-1 2-1 2-1-1-2-2-3-2l-1 1c0-1-1-2-1-4-1-1-1-2-1-2l-1-1z" class="O"></path><path d="M451 313s0 1 1 2c0 2 1 3 1 4l3 5c0-1 1-2 2-3h2l-1 1-1 1v1l2-1v1c-1 1-2 1-2 2h0l2-1 1 1-3 3-2 2-2-2-1-2c-1 1-1 1-2 1l-1-2-1-3v-3h0c-1-1-1-3-1-4l1-1 2-2z" class="AG"></path><path d="M451 313s0 1 1 2c0 2 1 3 1 4l3 5v1h-2c0-2-3-4-4-5h-1 0c-1-1-1-3-1-4l1-1 2-2z" class="f"></path><path d="M508 228c1-1 2-1 3-1h0c2 2 5 2 7 3h0c4 3 8 6 11 10 1 1 2 2 4 3l-1 1c-1-1-2-1-3-1h0c-1 0-2 0-3-1-1-2-3-2-5-2-3-2-5-3-8-4-2-1-3-1-4-1h-2c-2 0-2 0-4 1l-5 2c-5 1-8 4-13 5v-2c2-1 4-1 5-3h1c1-2 3-3 5-4 2 0 5-1 7-3 1-1 1-1 2-1 1-1 2-1 3-2z" class="M"></path><path d="M481 252v1h1 1l-1 1v3c2 0 3-1 4-2l1 1-1 1c0 1 1 2 1 3h0c-1 1-1 1-1 2h0l2 1-7 7-4 4c-2 2-3 3-5 4h2c-1 1-3 2-3 3-2 2-5 4-7 5h-3l-1-1 1-3c0-2 0-3 1-5 1-3 4-4 5-7l2-4h1v-1c1-1 1-2 1-3 2-1 4-3 6-3-1 0-1 1-2 2 0 1 0 1 1 2 1-1 3-3 3-4l-1 1v-1-1-1c1-2 1-2 3-3-1-1-1 0-2-1l2-1z" class="d"></path><path d="M486 262l-6 4 6-9c0 1 1 2 1 3h0c-1 1-1 1-1 2h0z" class="q"></path><path d="M480 264h1l-4 4h1 1 0c1 1 1 1 2 1v1l-4 4-1-1c0-1 0-2-1-3v-1l2-2 3-3z" class="O"></path><path d="M481 252v1h1 1l-1 1c-1 1-1 3-1 5h0l2-1h0c-1 2-3 4-4 5l1 1-3 3-2 2v1c-2 4-6 7-9 10v-1c3-3 5-5 6-9 2-2 3-4 4-7 1-1 3-3 3-4l-1 1v-1-1-1c1-2 1-2 3-3-1-1-1 0-2-1l2-1z" class="K"></path><path d="M471 262c2-1 4-3 6-3-1 0-1 1-2 2 0 1 0 1 1 2-1 3-2 5-4 7-1 4-3 6-6 9v-1c-1 0-1 1-2-1v-1c-1 0-1 1-2 1 1-3 4-4 5-7l2-4h1v-1c1-1 1-2 1-3z" class="J"></path><path d="M464 276h1c1-2 3-4 4-6 0-2 2-5 4-5h0v1c-2 3-4 5-6 8h1l4-4c-1 4-3 6-6 9v-1c-1 0-1 1-2-1v-1z" class="M"></path><path d="M475 270c1 1 1 2 1 3l1 1c-2 2-3 3-5 4h2c-1 1-3 2-3 3-2 2-5 4-7 5h-3l-1-1 1-3c0-2 0-3 1-5 1 0 1-1 2-1v1c1 2 1 1 2 1v1 1c3-3 7-6 9-10z" class="Y"></path><path d="M462 277c1 0 1-1 2-1v1c1 2 1 1 2 1v1 1c-1 1-1 2-2 3v1h-1l-1 1c1 0 2 0 3-1h1c2-1 4-4 6-6h2c-1 1-3 2-3 3-2 2-5 4-7 5h-3l-1-1 1-3c0-2 0-3 1-5z" class="J"></path><path d="M462 277c1 0 1-1 2-1v1l-3 5c0-2 0-3 1-5z" class="B"></path><path d="M439 308c0-1 0-1 1-2l2 1c2 1 5 3 6 5v4c0 1 0 3 1 4h0v3l1 3c-2 0-5-1-6 0l-2 2v-2l-3-3-2-2h-1l-1-1h-2c-2-1-5-1-6-3h0c-3-1-5-2-7-3v-1l-2-2v-1l1-1 1 1h1l2-1h-1c-1-2-2-2-4-2l2-1c4 1 3 2 6 1h1 2c1 1 4 1 5 0 0 0 0-1 1-1v1c1 1 3 1 4 1z" class="AL"></path><path d="M430 316l-1-1h1c1-1 2-1 4-1l1 1h2v1l-1 1-1-1-2 2h0c-2-1-2-1-3-2z" class="Aa"></path><path d="M437 311h3 3c0 1 1 2 1 2 0 1-1 2-1 2h1c1 0 2 1 2 2-2 1-3 1-4 3l1 2h-1c-1 0-1-1-2 0l-1-2c-1 0-2 0-3 1l-1-1h-2c-2-1-5-1-6-3 1 0 1 0 2-1h1c1 1 1 1 3 2h0c1 0 2 1 3 0 1 0 3 1 5 2v-2-2c1 0 1 0 2-1l-1-1c-1-1-2-1-3 0h-1l-1-3z" class="AB"></path><path d="M439 308c0-1 0-1 1-2l2 1 1 4h-3-3 0-1-2c-1 1-1 0-1 0h-2-1-2c-2 0-4-1-5-2h-1c-1-2-2-2-4-2l2-1c4 1 3 2 6 1h1 2c1 1 4 1 5 0 0 0 0-1 1-1v1c1 1 3 1 4 1z" class="P"></path><path d="M439 308c0-1 0-1 1-2l2 1 1 4h-3-3 0-1-2c-1 1-1 0-1 0h-2-1-2l1-1h2c1 0 2 0 4-1h0 3v1l1-2z" class="L"></path><path d="M442 307c2 1 5 3 6 5v4c0 1 0 3 1 4h0v3l1 3c-2 0-5-1-6 0l-2 2v-2l-3-3-2-2h-1c1-1 2-1 3-1l1 2c1-1 1 0 2 0h1l-1-2c1-2 2-2 4-3 0-1-1-2-2-2h-1s1-1 1-2c0 0-1-1-1-2l-1-4z" class="q"></path><path d="M442 326h0c0-1 1-1 1-2l1-1v-1-1c3 0 3 0 5 2l1 3c-2 0-5-1-6 0l-2 2v-2z" class="M"></path><path d="M268 498h3v-1c3-1 3-1 6-1h3c0 2-1 2 0 3l2-1h0c1 2 2 3 3 3v2 2l1 2c2 0 3 0 5-1 1 0 1 0 2 1 4 1 6 1 10 1v3c-2 0-6-1-8-1v1c2 2 4 4 5 7l3 3 1 1v2l6 9 1 1c2 5 7 10 10 15 1 2 3 4 4 6l3 4c5 5 12 8 18 13h-1v2l-1 1 2 2v2l-6-2c-2 0-3 1-4 0s-2-1-3-1v1h-2l-1-1s-1 0-1 1h-1-2v-1h-2c-1 1 0 1-1 3l2 2c0 1 0 1-1 2v1h-1 0c-2 0-2 0-3-1l-1 1c1 0 1 1 2 2v1h-2c0-1 0-1-1-1l-2 2h-3c-1-1-2-1-3-1-1-1-2-1-3-1 0 1 0 2-1 3l-14-16-11-12-5-6-7-7c0-1-2-2-2-2v-2-11-2l-1-5c-1-3-1-4 0-6 1-1 0-2 0-4l-1 1h0v-3-5-1c0-2 1-4 1-6 0-1 0-1 1-2 0 1 0 1 1 2v-4z" class="D"></path><path d="M284 560c1 0 2 1 3 1 0 1 0 2 1 3-2 0-2 0-4-1v-3zm38 5h0l2 2 1-1h0l1-1v3h-1-2-1l-2-2 2-1z" class="G"></path><path d="M267 544h1l2 2h1v-2-1l2 1-1 2h1c1 0 0 0 1 1h0c-1 1-1 1-1 3 2 0 3-1 4 1h-3l1 1h1c1 1 0 2 0 3l-7-7c0-1-2-2-2-2v-2zm36 26l1 2c0-1 1-2 1-2h3c0 1 1 1 1 2h2v1h0v2c-1 0-2 1-2 1 0 1 1 4 1 4l1 1c-1 1-1 1-1 2h-2s-1 0-2-1h0-1l-2-1 1-1v-2l1-1v-1-1l-1 1-1-1 2-1-1-1h-1l-1-1 1-2z" class="N"></path><path d="M292 573h3 1c0-1 0-2 1-3 0 0 1 0 2-1h2l1-1 1 1h-1l1 1-1 2 1 1h1l1 1-2 1 1 1 1-1v1 1l-1 1v2l-1 1 2 1h1 0c1 1 2 1 2 1v1c-1 0-1 1-2 1v1h1c0 1 0 2-1 3l-14-16z" class="G"></path><path d="M328 559c5 5 12 8 18 13h-1v2l-1 1 2 2v2l-6-2c-5-2-9-5-12-10l-2-3c0-1-1-2-1-3 1-1 1-2 3-2z" class="p"></path><path d="M346 577l-1 1h-1c-5-2-12-8-14-13l14 10 2 2z" class="F"></path><path d="M289 514c0-1 1-2 1-2 0-2-1-1 0-3 0 1 1 1 2 2l3 1v-1c2 2 4 4 5 7l3 3 1 1v2l6 9 1 1c2 5 7 10 10 15 1 2 3 4 4 6l3 4c-2 0-2 1-3 2h-2c-1 0-1-1-2-1l-1-1h1 2l1 1v-1c-1-2-3-4-4-7l-2 2c0 1 0 1-1 2-1 0-2 0-3-1v-1c0-1 0-1 1-2-2 0-3 0-5 1h-1v-2-1l-1 1h-1c-1 0-1-1-1-2-1 0-2 0-2-1-1 1-2 1-2 2h-2v2h-3v2h-2c-3 0-1-1-2-2h-1-1v-2c-1 0-2 0-3-1v-1c-1 0-1 0-2-1h0l-1-1-2 1h0l-1 1c-1 0-1 0-2 1h0-1l1-4h0l-1-1h-1l-1 1h-1 0l2-1-1-1 2-2c0-1 0-1 1-2h3l1-2v-1c2-1 3-2 4-3l1-1 1 1-2 1h1c0 1 1 1 1 1l2-2-1-1c0-1 0-1-1-2h0l2-2c-1-1-1-1-1-2l-1 1h-1c-1 0-1-1-1-2h0c0-2 1-3 1-4v-3c0-1-1-1 0-2v-2z" class="t"></path><path d="M307 531c0 1 1 2 1 3 1 1 1 1 1 2h-1v3 1h2v1h-2c0-1-2-1-3-2-2-1-2-1-2-2l2-3v-2l2-1z" class="D"></path><path d="M292 511l3 1v-1c2 2 4 4 5 7l3 3 1 1v2l6 9 1 1c2 5 7 10 10 15 1 2 3 4 4 6l3 4c-2 0-2 1-3 2h-2c-1 0-1-1-2-1l-1-1h1 2l1 1v-1c-1-2-3-4-4-7-2-2-3-4-5-6-2-4-3-8-7-10h1c0-1 0-1-1-2 0-1-1-2-1-3-1-2-3-4-4-6l-1 1c-2 0-3-1-5-1 1 0 2-1 3 0h2v-2h-2v-1-1l-2-2c-1-3-4-5-6-8z" class="Ab"></path><path d="M268 498h3v-1c3-1 3-1 6-1h3c0 2-1 2 0 3l2-1h0c1 2 2 3 3 3v2 2l1 2c2 0 3 0 5-1 1 0 1 0 2 1 4 1 6 1 10 1v3c-2 0-6-1-8-1v1 1l-3-1c-1-1-2-1-2-2-1 2 0 1 0 3 0 0-1 1-1 2v2c-1 1 0 1 0 2v3c0 1-1 2-1 4h0c0 1 0 2 1 2h1l1-1c0 1 0 1 1 2l-2 2h0c1 1 1 1 1 2l1 1-2 2s-1 0-1-1h-1l2-1-1-1-1 1c-1 1-2 2-4 3v1l-1 2h-3c-1 1-1 1-1 2l-2 2 1 1-2 1h0l-2 2c-1-1 0-1-1-1h-1l1-2-2-1v1 2h-1l-2-2h-1v-11-2l-1-5c-1-3-1-4 0-6 1-1 0-2 0-4l-1 1h0v-3-5-1c0-2 1-4 1-6 0-1 0-1 1-2 0 1 0 1 1 2v-4z" class="v"></path><path d="M286 511c1 1 1 2 3 3v2c-1 1 0 1 0 2v3c0 1-1 2-1 4h0-1l-1-5v-2c1-3 0-5 0-7z" class="p"></path><path d="M269 528l2-1-1-2h2l1 1h1v-2l1-1c1 0 1 1 3 0h0c-1 2-2 3-3 4h1l1 1v2l-1 1h-2v-1h0c-1 1-2 1-2 2v1c-1-1-1-1-1-2-1-1-1-1-1-2l-1-1z" class="D"></path><path d="M277 530h2v1c0 2-1 2 1 3h2l2 3-1 2h-3v-1-2h-1c0 1-1 1-2 2h-1c-1-1-2-2-2-3 1-1 1-2 2-3h-4c0-1 1-1 2-2h0v1h2l1-1z" class="N"></path><path d="M265 509v-1c0-2 1-4 1-6 0-1 0-1 1-2 0 1 0 1 1 2l-1 6c2 0 6 0 8-1h0c1 1 2 1 3 1v1h-11c0 2 1 3 1 5v2l1-1h1l-2 2c-1 1-1 3-1 5v9l-1-5c-1-3-1-4 0-6 1-1 0-2 0-4l-1 1h0v-3-5z" class="y"></path><path d="M291 506c1 0 1 0 2 1 4 1 6 1 10 1v3c-2 0-6-1-8-1v1 1l-3-1c-1-1-2-1-2-2-1 2 0 1 0 3 0 0-1 1-1 2-2-1-2-2-3-3v-1-1l-1 1c-1-1-2-1-4-1-1 0-2 1-3 0v-1c1-1 2-1 3-1h5c2 0 3 0 5-1z" class="l"></path><path d="M269 528l1 1c0 1 0 1 1 2 0 1 0 1 1 2v-1h4c-1 1-1 2-2 3 0 1 1 2 2 3h1c1-1 2-1 2-2h1v2 1c-1 1-1 1-1 2l-2 2 1 1-2 1h0l-2 2c-1-1 0-1-1-1h-1l1-2-2-1v1 2h-1l-2-2h-1v-11l1-1h1l-1-1 1-3zm-1-30h3v-1c3-1 3-1 6-1h3c0 2-1 2 0 3l2-1h0c1 2 2 3 3 3v2 2l1 2h-5c-1 0-2 0-3 1-1 0-2 0-3-1h0c-2 1-6 1-8 1l1-6v-4z" class="r"></path><path d="M759 436l1 2c0 1-2 2-2 2v3c1-1 2-1 3-2v6 2 1 2 19l1 76c-3 0-3-1-4-2h-2v1c1 1 2 1 2 2v2c-1 1-2 1-3 1l-1-1v-2l-1 1-4-1-2 1h-1v-1c1-1 0-1 0-1v-1c-1 1-2 1-2 2 0 2-1 3-2 4l1 1c1 0 1 0 2 1h0c-1 1-1 1-1 2h0l-1 1c0 1-1 2-1 3l-1-1c-1-1-1-1 0-2l-1-2c-1 0-1 1-1 1-2 1-2 0-4 0v-1c-1 0-1 0-1-1h-1v1s0 1-1 2h1l1 1-2 1h0l-1-1-2-1h1c-1-2-1-5-1-7 0-6 0-12-1-17-1 1-1 5 0 7v2h0c-1 3 0 8 0 11h0l-1-1h0c0-1 0-2-1-3v4c-1 2 0 5-1 7v1h0-1c-1-2 0-4 0-6v-9-1l-1-1h0l1-1c0-2-1-3-2-6h0 0c-1 1-1 2-2 3-1-1-1-2-2-3l-1 2h-1v-2h-1 0l-1 1c-1 0-1 1-1 2-1 0-3-1-4-1l-1-3h0c-1 0-1-1-2-1v-1h-2v-1h-1l-1-1c-1-1-1-1-3-1v-4h0c2-1 3-3 5-3 1-1 1-1 2-1v-2h-3l-1 2v1l-1-1h-1c-1 0-1 1-2 2-1 0-2 1-3 1l1-8c-1 0-1-1-1-1v-1c0-2 1-4 1-6v-1h0c0-1 0-2-1-2v-1c1-2 2-3 4-4 0-2 1-4 0-5-2 1-3 2-4 3l-1-1c2-1 4-3 4-4 1-1 0-1 1-2l2-2c0-1 1-1 1-2h1l2-2-1-2v-2c3-1 6-3 8-6h3l1-1c2-1 4-1 5-1s4-3 5-3c2-2 4-2 5-4 0-1 0-1 1-1s1-1 2-1h-4c1-1 1-2 1-2 2-1 2-1 3-3v-1l1-1c2-1 3-2 4-3 1-2 5-5 7-7h1l-1-2c1-1 2-1 3-2 2-1 3-3 5-5l5-4z" class="t"></path><path d="M755 509l1 1c-1 0-1 0-1 2 0 0-1 1-2 1h-1c0-2 0-1 1-3h2v-1zm-1 10c1 0 1 0 2 1 0 2-1 2-2 3l-1 1v-2h-1c1-1 1-2 2-3z" class="F"></path><path d="M727 495h0l1 1c0 1 1 2 2 3v3 2h1v1h-1l1 2h-3c1-1 1-2 1-3-1-2-1 0-1-2v-1-2c0-2-1-2-2-3l1-1z" class="g"></path><path d="M733 534v1l-1-1h0l1-2-1-1c0-2 1-1 0-2l-1 1-1-1v-1-1-1l1 1c1 0 1 0 2-1h1v1h1c2 2 1 2 1 4-1 1-1 2-3 3z" class="AE"></path><path d="M713 508c1 0 1 1 2 1h1l1-1 1 1h2v1c1 0 1 0 2 1h0-2c-1 1-2 1-3 2h-1l-1-1c-1 0-1 0-1 1h0c0-1 0-1-1-1v-1l-1 2h-1l-3-3v-1h2 2l1-1z" class="g"></path><path d="M729 509h0c1 0 2 0 2 1 1 0 3 1 3 2l1 1 2-1h0c0 1-1 2-1 3v2c-1-1-2-2-3-2l-1 1-1-1h-1-1-1v-1c1-1 0-2 1-3v-2z" class="v"></path><path d="M699 527h0c2-1 3-3 5-3 1-1 1-1 2-1 0 1 0 1-1 3h0c0 2 0 3-1 4v3h-1l-1-1c-1-1-1-1-3-1v-4z" class="D"></path><path d="M730 557v-5c1 0 1 0 2-1l-2-2v-2h2l2 1 1 3s-1 1-1 2h0c1 1 1 2 1 3v-1c-1 0-1 0-1-1h-1v1s0 1-1 2h1l1 1-2 1h0l-1-1-2-1h1z" class="G"></path><path d="M737 531v-3c1-1 1-1 2-1s2-1 2-2l1 1 2-1 1 2c1 1-1 1 1 2v1h0l-1 1c0 1 1 1 0 2-2 0-2 1-4 2l-4-2 1-2h-1zm5-43c2 0 3 0 4-1l1 1v1h1v-1h1l2 1h1 3v-1l1-1 1 1c0 1 1 2 1 2l-1 1c-1-1-1 0-1-1-1 0-1 1-1 2-1-1-1-1-1-2-3 0-3 1-5 2h0l-2 2c-2 0-3-1-5-1v-2l-1-2v-1h1z" class="v"></path><path d="M696 510c1-1 2-3 3-4h2c0 2 0 2-1 4h1c0 1 1 1 1 2v3h-1c0 1 2 3 3 3h1c-1 2-2 2-2 3l-1 2v1l-1-1h-1c-1 0-1 1-2 2-1 0-2 1-3 1l1-8c-1 0-1-1-1-1v-1c0-2 1-4 1-6z" class="F"></path><path d="M722 537c-1 0 0 0-1-1 0 0 1-1 2-1h0c-1-2-2-1-3-2 0-1 0-1 2-2v1h1 1l2-1h2v-2l1 1v2l-1 1c-1 1-1 5 0 7v2h0c-1 3 0 8 0 11h0l-1-1h0c0-1 0-2-1-3v4c-1 2 0 5-1 7v1h0-1c-1-2 0-4 0-6v-9-1l-1-1h0l1-1c0-2-1-3-2-6h0z" class="N"></path><path d="M716 476c2-1 4-1 5-1l-8 8c-3 3-5 6-7 10l-1 1h1v-1c1-1 2-2 4-3h2v3h-1c-1 1-1 1-1 2h-1v-2c-2 1-2 2-4 2v1l1 1h-1-1c-2 2-3 5-4 7l1 1v1h-2c-1 1-2 3-3 4v-1h0c0-1 0-2-1-2v-1c1-2 2-3 4-4 0-2 1-4 0-5-2 1-3 2-4 3l-1-1c2-1 4-3 4-4 1-1 0-1 1-2l2-2c0-1 1-1 1-2h1l2-2-1-2v-2c3-1 6-3 8-6h3l1-1z" class="AM"></path><path d="M712 477h3c-1 1-2 3-4 4s-4 4-6 6l-1-2v-2c3-1 6-3 8-6z" class="I"></path><path d="M734 477v-1-1l1-1v2h1c0-1 0-1 1-2h5c0-1 0-1 1 0 1 2 0 1 0 4 1-1 2-2 3-2s1 0 2 1l1-1c2 1 2 0 4-1 0-1 3-1 3-1l2 1-2 1h0v1l2 2-1 2-1 1c-1-1-1-1-3-1 1 1 1 1 1 2h0v1h0c1 0 1 0 2 1l1-1 1 1-2 2-1 1v1h-3-1l-2-1h-1v1h-1v-1l-1-1c-1 1-2 1-4 1h-1v1c0 2 0 2-1 3h-1c-1-1-1-1-2-1h-2l1-2v-1l2-2v-1c1-1 0-1-1-2l1-1v-2l-2 1c-1-1-2-1-2-1l-2 1c0-2 0-3 2-4z" class="AE"></path><path d="M736 481v-2-2c1 1 2 1 3 1l-1 2-2 1z" class="t"></path><path d="M742 488c1-1 2-2 3-2v-1c1 0 1-1 2-1v-1h1v4 1 1h-1v-1l-1-1c-1 1-2 1-4 1z" class="g"></path><path d="M721 475c1 0 4-3 5-3h0c0 1-1 2-1 3h1l1-1c1 0 1 1 2 2h-1v1c1 0 2 0 3-1 1 0 2 0 3 1-2 1-2 2-2 4l2-1s1 0 2 1l2-1v2l-1 1c1 1 2 1 1 2v1l-2 2v1l-1 2h0-1l-4 2h-1v-1-1h-2c-1 0 0 1-1 2h-1v-3h0v-1l-2 1c0-1 0-1-1-1h-2v-1h-4v1h-2c-1 0-1 1-2 1h-2c-2 1-3 2-4 3v1h-1l1-1c2-4 4-7 7-10l8-8z" class="AK"></path><path d="M713 483l1 1h2l2-1h2c2 0 2-1 4 0l1-2 1 3c-1 1-2 2-3 2h-1 0c-1 1-1 1-2 1v1h-4v1h-2c-1 0-1 1-2 1h-2c-2 1-3 2-4 3v1h-1l1-1c2-4 4-7 7-10z" class="g"></path><path d="M737 531h1l-1 2 4 2c2-1 2-2 4-2v1h2v1h1l1-1s1 0 2-1h1c0 1-1 2-2 4h1l1-1 2 1 1-1c1 2-1 3 2 4l1-1-1-1h2v1c0 1 1 1 2 2h-1v1h1v2h0c-2 0-2 0-3 1h-2v1c1 1 2 1 2 2v2c-1 1-2 1-3 1l-1-1v-2l-1 1-4-1-2 1h-1v-1c1-1 0-1 0-1v-1c-1 1-2 1-2 2 0 2-1 3-2 4l1 1c1 0 1 0 2 1h0c-1 1-1 1-1 2h0l-1 1c0 1-1 2-1 3l-1-1c-1-1-1-1 0-2l-1-2c-1 0-1 1-1 1-2 1-2 0-4 0 0-1 0-2-1-3h0c0-1 1-2 1-2l-1-3v-1c0-1 0-1-1-2 0 1 0 1-1 2l-1-1v-2l3-3v-1l-1-1c-1 1-1 1-1 2h-1v-1c0-1 0-1 1-1l1-1c1-1 1-2 3-3h0l1-1-1-1-1 1-1 1-1-1c2-1 2-2 3-3h1z" class="N"></path><path d="M759 436l1 2c0 1-2 2-2 2v3c1-1 2-1 3-2v6 2 1 2 19l-2 1c0 1 0 1-1 2v1l-2-1s-3 0-3 1c-2 1-2 2-4 1l-1 1c-1-1-1-1-2-1s-2 1-3 2c0-3 1-2 0-4-1-1-1-1-1 0h-5c-1 1-1 1-1 2h-1v-2l-1 1v1 1c-1-1-2-1-3-1-1 1-2 1-3 1v-1h1c-1-1-1-2-2-2l-1 1h-1c0-1 1-2 1-3h0c2-2 4-2 5-4 0-1 0-1 1-1s1-1 2-1h-4c1-1 1-2 1-2 2-1 2-1 3-3v-1l1-1c2-1 3-2 4-3 1-2 5-5 7-7h1l-1-2c1-1 2-1 3-2 2-1 3-3 5-5l5-4z" class="g"></path><path d="M739 456c2-1 3-2 4-3 2-1 4-3 6-3 0 0 0 1-1 1l-1 2c-1 2-8 7-8 9v1h-1c-2 0-3 2-4 3h-4c1-1 1-2 1-2 2-1 2-1 3-3v-1l1-1c2-1 3-2 4-3z" class="o"></path><path d="M759 436l1 2c0 1-2 2-2 2v3l-4 4v1h1c1 0 1 0 2 1v1c-1 0-1 1-2 1l-1-1h-1 0v2l-1 1-1-1h-1c-1 1-1 2-1 3-1-1-1-1-1-2h-1l1-2c1 0 1-1 1-1-2 0-4 2-6 3-1 1-2 2-4 3 1-2 5-5 7-7h1l-1-2c1-1 2-1 3-2 2-1 3-3 5-5l5-4z" class="h"></path><defs><linearGradient id="W" x1="515.412" y1="304.12" x2="501.588" y2="301.38" xlink:href="#B"><stop offset="0" stop-color="#221e18"></stop><stop offset="1" stop-color="#49494c"></stop></linearGradient></defs><path fill="url(#W)" d="M510 238c2 0 5 2 8 3l15 7c-1 1-4 1-4 2v2c1 1 2 1 2 2 1 1 2 1 3 3h0c-1 1 0 3 1 3v1l-1 1-1-2c-1-1-1-1-1-2l-1-1v2c1 2 3 3 4 5-2-1-3-2-5-3l3 3v3c-2 0-1-1-2-1h-2c0 2 2 1 2 2 1 1 1 1 1 2s0 1-1 2c0 0-1 1-1 2l-1-3h-1v2c-1-1-2-1-2-2-1-1-2-1-3-1h0-4l3 3c-1 0-2-1-3 0-1-1-2-1-3-1-1-1-1-2-3-3 1 3 1 7 3 10l-1 3 1 12c1 4 2 9 2 13 1 1 1 2 1 3 0 2 0 2-1 3 0 1-1 3-1 3l-1 2h-1-1l-1 2c0 2 0 3-1 4l1 2h0c-1 1-1 2-1 2l-1 1v1c0 1 0 0-1 1l1 1-1 1v1l-1 19c0 3 1 7 0 10v-1c0-1 0-2-1-3v-7l1-10c-1 1-1 3-1 4-1 3 0 5-1 7v2l-1 8v1c0 2 0 2-1 3l-2 1c0 2-1 3-2 4h-1v-1-1h0l-1 1c0 1 0 0-1 1-1-4-2-7-3-11l-3-6-3-4c-1-2-2-4-4-6-1-1-2-2-3-4-1-3-4-5-6-6l-4-2c-2-1-3-1-5-2h-1c-3 0-5 0-8 1h-2c-2-1-2-2-2-3l2 2 2-2 3-3-1-1-2 1h0c0-1 1-1 2-2v-1l-2 1v-1l1-1 1-1h-2c1-1 1-2 2-3l-1-1-1-3 1-2s-1-2-1-3 0-2 1-3l1-3c1-2 3-4 5-5l5-2v-1h0c-1-2-2-1-4-2l1-2c-1-1-1 0-1-2h0c-1-1-2-2-2-3 2-1 5-3 7-5 0-1 2-2 3-3h-2c2-1 3-2 5-4l4-4 7-7-2-1h0c0-1 0-1 1-2h0c0-1-1-2-1-3l1-1-1-1c-1 1-2 2-4 2v-3l1-1h-1-1v-1l6-3c7-4 15-8 22-9h0l1-1v-1z"></path><path d="M505 317h0c1 3 0 0 1 2v5l-1 1v1l-1 1v-4-4-1l1-1zm2 16c0 1 0 2 1 4 0 4-1 8-2 12-1 2 0 4-1 6v-4-3c-1-1-1-1-1-2l-1-2v-1c0-2-1-4 0-6l1 2 1-3c2-1 1-2 2-3z" class="C"></path><path d="M503 337l1 2c1 3 1 5 0 7l-1-2v-1c0-2-1-4 0-6z" class="O"></path><path d="M504 319v4 4c2 2 3 3 3 6-1 1 0 2-2 3l-1 3-1-2c-1-1-1-2-1-3-1-2-2-2-2-4h-1c1-4 3-7 5-11z" class="M"></path><path d="M502 329h1v-1l1 1h0c0 3 0 5 1 7l-1 3-1-2c-1-1-1-2-1-3v-5z" class="a"></path><path d="M504 319v4c-2 2-2 4-3 6h1v5c-1-2-2-2-2-4h-1c1-4 3-7 5-11z" class="K"></path><path d="M502 350h0c0-2 1-4 1-6l1 2c0 1 0 1 1 2v3 4c-1 2-2 3-2 5h0l2-1v5c-1 2-2 2-2 4s-1 3-2 4h-1v-1-1h0l-1 1c0 1 0 0-1 1-1-4-2-7-3-11 2-2 2-4 3-6l1-1 1-2c0-1 1-2 2-2z" class="O"></path><path d="M502 350h0c0-2 1-4 1-6l1 2c0 1 0 1 1 2v3c-1 1-2 1-3 3l1 1h-1c-1 4 0 8-2 11 0-1-1-5 0-5 0-1 1-2 1-2l-1-1c0-2 2-6 2-8z" class="M"></path><path d="M495 361c2-2 2-4 3-6l1-1 1-2c0-1 1-2 2-2 0 2-2 6-2 8l1 1s-1 1-1 2c-1 0 0 4 0 5s-1 3-1 5c0 1 0 0-1 1-1-4-2-7-3-11z" class="L"></path><path d="M499 330h1c0 2 1 2 2 4 0 1 0 2 1 3-1 2 0 4 0 6v1c0 2-1 4-1 6h0c-1 0-2 1-2 2l-1 2-1 1c-1 2-1 4-3 6l-3-6v-1l1-1h-1c0-5 3-11 5-15 0-2 0-2 1-3h0v-1c0-1 0-1-1-2v-1l2-1h0z" class="d"></path><path d="M498 335c0 1 1 2 0 3-1 2 0 4 0 6-1 3-2 7-3 11h-2l-1-1 1-1h-1c0-5 3-11 5-15 0-2 0-2 1-3z" class="AN"></path><path d="M495 355h1c1-2 3-5 3-7l-1-1c1-2 2-4 4-5l1 1v1c0 2-1 4-1 6h0c-1 0-2 1-2 2l-1 2-1 1c-1 2-1 4-3 6l-3-6v-1l1 1h2z" class="i"></path><path d="M502 294h0 1c0-4 1-7 3-10v1l-2 14v6c1-1 1-3 2-4 0-1 0-3 1-4 1 3 0 5 0 7-1 2 0 5 0 7s-1 3-1 4l-1 2-1 1v1c-2 4-4 7-5 11h0l-2 1v1c1 1 1 1 1 2v1h0c-1 1-1 1-1 3l-2-1v1h-2v-3l-1 2-1-1c0-3-1-8-3-11l2-1h1 0v-5c1 0 1 0 1 1h0 2l-2-4h0c-1-4 3-9 4-13l3-6c0-1 0-2 1-3l1 1v-1h1z" class="X"></path><path d="M499 307l1 1-1 1c-1 1-1 3-1 4-1 1 0 1-1 1-1 2-1 5-2 7h-1v-1l-2-4c2-3 5-6 7-9z" class="M"></path><path d="M504 299v6c1-1 1-3 2-4 0-1 0-3 1-4 1 3 0 5 0 7-1 2 0 5 0 7s-1 3-1 4l-1 2-1 1-1-2c1-1 1-2 1-3h-2v-1h-1v4 1c0 1 0 3-1 4h-1c0-2 0-3 1-5l-1-1c0-1 0-1 1-1 0-2 0-4 1-6s2-4 2-7l1-2z" class="f"></path><path d="M502 294h0 1c0-4 1-7 3-10v1l-2 14-1 2c-1 2-2 5-4 6h0c-2 3-5 6-7 9h0c-1-4 3-9 4-13l3-6c0-1 0-2 1-3l1 1v-1h1z" class="AL"></path><defs><linearGradient id="X" x1="527.444" y1="247.468" x2="516.648" y2="266.371" xlink:href="#B"><stop offset="0" stop-color="#6f6e6c"></stop><stop offset="1" stop-color="#a1a39e"></stop></linearGradient></defs><path fill="url(#X)" d="M510 238c2 0 5 2 8 3l15 7c-1 1-4 1-4 2v2c1 1 2 1 2 2 1 1 2 1 3 3h0c-1 1 0 3 1 3v1l-1 1-1-2c-1-1-1-1-1-2l-1-1v2c1 2 3 3 4 5-2-1-3-2-5-3l3 3v3c-2 0-1-1-2-1h-2c0 2 2 1 2 2 1 1 1 1 1 2s0 1-1 2c0 0-1 1-1 2l-1-3h-1v2c-1-1-2-1-2-2-1-1-2-1-3-1h0-4l3 3c-1 0-2-1-3 0-1-1-2-1-3-1-1-1-1-2-3-3 1 3 1 7 3 10l-1 3 1 12c1 4 2 9 2 13 1 1 1 2 1 3 0 2 0 2-1 3 0 1-1 3-1 3l-1 2h-1-1l-1 2c0 2 0 3-1 4l1 2h0c-1 1-1 2-1 2l-1 1v1c0 1 0 0-1 1l-1-1c0-3 0-6 1-9v-73c0-3 1-7 0-9v-1z"></path><path d="M523 270l-2-1v-1c1 0 1-1 2 0h3l1 2c-1 0 0 0-1 1-1-1-2-1-3-1h0zm-6-10c1 2 1 5 2 7h-2 0l-1-2v-2c0-1 1-2 1-3z" class="e"></path><path d="M511 291h2l1 2-2 1 1 1c1 1 0 3 1 5l-1 1h0c-2 2-1 3-1 5h-1v-15z" class="M"></path><path d="M511 269l1-4h0c1 1 1 3 1 4 1 3 1 7 3 10l-1 3c-1-1-1-2-2-4 0-1 0-1-1-1l-1 4v-12z" class="d"></path><path d="M511 306h1v11c0 1 1 1 2 1h0l-1 2c0 2 0 3-1 4l1 2h0c-1 1-1 2-1 2l-1 1v1c0 1 0 0-1 1l-1-1c0-3 0-6 1-9 2-2 1-12 1-15z" class="O"></path><path d="M530 261c-1 0-2-1-3-2v-1h0-1l1-1-2-1v1c-1-1-3-2-4-3 1 1 1 0 1 0h1l-3-3v-1-1h1l1 1c3 1 4 3 6 5 0 0 1 1 2 1 0 2 0 2 1 3 1 2 3 3 4 5-2-1-3-2-5-3z" class="AF"></path><path d="M511 281l1-4c1 0 1 0 1 1 1 2 1 3 2 4l1 12v1c-1 1-1 1-1 2l-1-1v-1l1-1-1-1-1-2h-2v-10z" class="O"></path><defs><linearGradient id="Y" x1="508.115" y1="256.569" x2="521.387" y2="265.431" xlink:href="#B"><stop offset="0" stop-color="#787876"></stop><stop offset="1" stop-color="#8f918c"></stop></linearGradient></defs><path fill="url(#Y)" d="M511 269v-14c0-1 0-3 1-5l1 1c1-1 1-1 2-1 2 2 0 5 1 7l1 3c0 1-1 2-1 3v2l1 2c1 1 1 2 2 3l3 3c-1 0-2-1-3 0-1-1-2-1-3-1-1-1-1-2-3-3 0-1 0-3-1-4h0l-1 4z"></path><path d="M514 293l1 1-1 1v1l1 1c0-1 0-1 1-2v-1c1 4 2 9 2 13 1 1 1 2 1 3 0 2 0 2-1 3 0 1-1 3-1 3l-1 2h-1-1 0c-1 0-2 0-2-1v-11c0-2-1-3 1-5h0l1-1c-1-2 0-4-1-5l-1-1 2-1z" class="J"></path><path d="M515 304h1c2 3 1 7 0 10h0v-1c-1-2-1-7-1-9zm-2-3l1 1v11c1 2 0 3 1 5h-1 0c-1 0-2 0-2-1v-11c0-2-1-3 1-5z" class="C"></path><path d="M484 298c1-2 3-3 4-4l2 1h1v1c-1 1-2 2-2 3 1 0 2 0 3-1 0 0 1 1 1 2h1 0l1 1c-1 1-1 0 0 1 2-1 1-4 4-5l-3 6c-1 4-5 9-4 13h0l2 4h-2 0c0-1 0-1-1-1v5h0-1l-2 1c2 3 3 8 3 11l1 1 1-2v3h2v-1l2 1c-2 4-5 10-5 15h1l-1 1v1l-3-4c-1-2-2-4-4-6-1-1-2-2-3-4-1-3-4-5-6-6l-4-2c-2-1-3-1-5-2h-1c-3 0-5 0-8 1h-2c-2-1-2-2-2-3l2 2 2-2 3-3-1-1-2 1h0c0-1 1-1 2-2v-1l-2 1v-1l1-1 1-1h-2c1-1 1-2 2-3l-1-1-1-3 1-2s-1-2-1-3 0-2 1-3l1-3c1 1 1 2 2 3l2-2v1c0 1 0 2-1 3l1 1c0-1 1-2 1-3l1-1 1 1-1 1v3 1 1c2-2 4-3 6-4 2-2 4-3 7-3h0l1-1c2 0 2 0 3-1l-1-1v-1h1l1-3z" class="q"></path><path d="M482 307v-1l2-2h0c0 2 1 2 1 3 0 0 0 2-1 3h2 0 1l1 2c-1 0-1 1-1 2v1h-1v1c1 1 1 1 2 1h0v2h-1-1l-1 3h-1l1-1c0-2 0-4-1-6s-1-2-1-4v-1c-1-1-1-2-1-3z" class="M"></path><path d="M483 301h1c0-1 1-1 2-2v1l-1 2h1c0-1 1-2 2-2l-2 4 1 1h-1c0 2 0 3 1 5h0-1 0-2c1-1 1-3 1-3 0-1-1-1-1-3h0l-2 2v1h-1 0-1-1v1l2 1c0 1 0 1-1 2l-1 1 1 1h1l2 1c-1 1-1 1-2 1s-1 0-1-1h-1c0 1-1 1-1 2-1-1-1-1-1-2-1 0-1 0-2 1l1 1-2-2c0 1 0 2 1 3-1 0-1 0-2 1h0l-1-2 1-1-1-1c-1-2 2-2 2-4h-1-1c2-2 4-3 6-5h1 0l1-1c2 0 2 0 3-1l-1-1v-1h1z" class="f"></path><path d="M484 298c1-2 3-3 4-4l2 1h1v1c-1 1-2 2-2 3 1 0 2 0 3-1 0 0 1 1 1 2h1 0l1 1c-1 1-1 0 0 1 2-1 1-4 4-5l-3 6c-1 4-5 9-4 13h-1c0-1 0-1-1-2h-1v-1l-1-1 2-2h-1-2 0c-1-2-1-3-1-5h1l-1-1 2-4c-1 0-2 1-2 2h-1l1-2v-1c-1 1-2 1-2 2h-1l1-3z" class="X"></path><path d="M484 298c1-2 3-3 4-4l2 1h1v1c-1 1-2 2-2 3 1 0 2 0 3-1 0 0 1 1 1 2h1c-1 2-2 3-2 5 0 1-1 1-2 2 1 1 1 1 1 2h-1l-1-1c-1-2 0-2 0-4h0-1c1-1 1-2 2-3l-2-1c-1 0-2 1-2 2h-1l1-2v-1c-1 1-2 1-2 2h-1l1-3z" class="P"></path><path d="M484 298c1-2 3-3 4-4l2 1-3 4c-1-1-2-1-3-1z" class="AL"></path><path d="M460 325c1 0 1-1 3-2v2h1c0-2-1-3-1-4h1c1-1 0-1 1-1h1v-2c1 0 1 0 1 1l1 2h0c1 2 1 2 2 3h1c0 2 1 4 2 5s1 1 1 2c1 1 1 1 2 1v-2h1l5-5c0 2-1 2-2 4 0 1 0 1-1 2v1 1 1c-1 0-2 0-3 1l-4-2c-2-1-3-1-5-2h-1c-3 0-5 0-8 1h-2c-2-1-2-2-2-3l2 2 2-2 3-3-1-1z" class="d"></path><path d="M461 326c2 2 2 2 4 2h1 1 1l1-1c-1 0-2-1-3-2l2-1c1 1 3 3 4 5 0 1 0 2 1 3l-1 1c-2-1-3-1-5-2h-1c-3 0-5 0-8 1h-2c-2-1-2-2-2-3l2 2 2-2 3-3z" class="i"></path><path d="M460 303c1 1 1 2 2 3l2-2v1c0 1 0 2-1 3l1 1c0-1 1-2 1-3l1-1 1 1-1 1v3 1 1c2-2 4-3 6-4 2-2 4-3 7-3h-1c-2 2-4 3-6 5h1 1c0 2-3 2-2 4l1 1-1 1 1 2h0l1 2c1 2 1 4 2 6v2c-2-1-3-7-4-9l-2 2 1 3h-1c-1-1-1-1-2-3h0l-1-2c0-1 0-1-1-1v2h-1c-1 0 0 0-1 1h-1c0 1 1 2 1 4h-1v-2c-2 1-2 2-3 2l-2 1h0c0-1 1-1 2-2v-1l-2 1v-1l1-1 1-1h-2c1-1 1-2 2-3l-1-1-1-3 1-2s-1-2-1-3 0-2 1-3l1-3z" class="O"></path><path d="M470 321h-1c0-2-1-3-2-5h0c1-1 1-2 2-3 2 1 2 3 3 6l-2 2z" class="f"></path><path d="M462 306l2-2v1c0 1 0 2-1 3l1 1v3c0 1-1 2-2 3l-2-3 2-6z" class="Y"></path><path d="M481 331c1-2 2-3 3-5s1-3 3-4c1 0 0 0 1 1h1v-1l1-1v3l-2 1c2 3 3 8 3 11l1 1 1-2v3h2v-1l2 1c-2 4-5 10-5 15h1l-1 1v1l-3-4c-1-2-2-4-4-6-1-1-2-2-3-4-1-3-4-5-6-6 1-1 2-1 3-1v-1-1-1h2z" class="AD"></path><path d="M485 328l2-2 1 1c0 2-1 3 0 5 1 3 0 4 3 6v1h-2c-2-1-2-4-2-6-1 1-1 1-1 3-1-2-1-6-1-7v-1z" class="AN"></path><path d="M481 331c1-2 2-3 3-5s1-3 3-4c1 0 0 0 1 1h1v-1l1-1v3l-2 1h0v-2c-2 1-2 2-3 4v1 1h0c-1 1-1 2-2 2-1 2-1 3-1 4l1 2-2-2c-1-1 0-3 0-4z" class="L"></path><path d="M479 331h2c0 1-1 3 0 4l2 2v1c1 1 2 1 3 2l1 1h1c0 2 1 3 1 5h1l1-1c0-2 1-4 2-7h2v-1l2 1c-2 4-5 10-5 15h1l-1 1v1l-3-4c-1-2-2-4-4-6-1-1-2-2-3-4-1-3-4-5-6-6 1-1 2-1 3-1v-1-1-1z" class="K"></path><path d="M479 331h2c0 1-1 3 0 4l2 2v1c1 1 2 1 3 2l1 1h1c0 2 1 3 1 5h1v2h-1c-1-2-1-2-2-3-1-2-2-3-3-4h0c1 3 5 7 5 10-1-2-2-4-4-6-1-1-2-2-3-4-1-3-4-5-6-6 1-1 2-1 3-1v-1-1-1z" class="P"></path><path d="M510 239c1 2 0 6 0 9l-1 2c0 6-1 13-1 19s0 10-1 16h-1v-1c-2 3-3 6-3 10h-1 0-1v1l-1-1c-1 1-1 2-1 3-3 1-2 4-4 5-1-1-1 0 0-1l-1-1h0-1c0-1-1-2-1-2-1 1-2 1-3 1 0-1 1-2 2-3v-1h-1l-2-1c-1 1-3 2-4 4l-1 3h-1v1l1 1c-1 1-1 1-3 1l-1 1h0c-3 0-5 1-7 3-2 1-4 2-6 4v-1-1-3l1-1-1-1-1 1c0 1-1 2-1 3l-1-1c1-1 1-2 1-3v-1l-2 2c-1-1-1-2-2-3 1-2 3-4 5-5l5-2v-1h0c-1-2-2-1-4-2l1-2c-1-1-1 0-1-2h0c-1-1-2-2-2-3 2-1 5-3 7-5 0-1 2-2 3-3h-2c2-1 3-2 5-4l4-4 7-7-2-1h0c0-1 0-1 1-2h0c0-1-1-2-1-3l1-1-1-1c-1 1-2 2-4 2v-3l1-1h-1-1v-1l6-3c7-4 15-8 22-9h0l1-1z" class="i"></path><path d="M487 249l-1 1 1 1c-1 1-1 1-2 1s-1 1-2 1h-1-1v-1l6-3z" class="f"></path><path d="M486 286l-1-2c-1 1-2 2-3 2v-1h-1v-1c1-1 2-2 4-2-1 0-1 1-1 2 1 0 1 0 2-1l1 1h0 0l-1 2z" class="P"></path><path d="M472 285c1-1 2-2 2-4h-1-1c3-3 5-5 8-7l-1 4v1c1-1 1-1 2-1v1c-1 1-2 3-5 4-1 0-2 2-4 2z" class="d"></path><path d="M486 266v1 1c-1 0-1 1-2 2l-4 4c-3 2-5 4-8 7h1 1c0 2-1 3-2 4h-1c-1 1-1 1-1 2-1 1-2 2-2 3l-1 1c-1-1-1 0-1-2h0c-1-1-2-2-2-3 2-1 5-3 7-5 0-1 2-2 3-3l12-12z" class="B"></path><path d="M503 252c-1 3-6 7-9 9h1l7-6c0 2-1 3-1 5h-1c-2 3-5 5-7 8h-1l-1-1-4 4c-1 1-2 3-3 2 0-1 2-2 3-3l-2-1 18-17zm-16 4c3-2 6-5 9-6 0 0 1-1 2-1l1-1c2 0 4-2 6-3h0c-1 1-2 3-2 4-1 1-3 3-5 4-2 3-4 5-7 8-1 1-5 4-5 5l-12 12h-2c2-1 3-2 5-4l4-4 7-7-2-1h0c0-1 0-1 1-2h0c0-1-1-2-1-3l1-1z" class="e"></path><path d="M486 262c2-3 4-5 8-6-2 3-4 5-6 7l-2-1h0z" class="i"></path><path d="M471 285c0 1 1 2 1 3v1h1c2-2 2-3 4-3h2v1c-1 2-3 4-3 7l-2 1 1 1c1 0 1-1 2-1v3l1 1v3c2 1 2 0 4 0l1 1c-1 1-1 1-3 1l-1 1h0c-3 0-5 1-7 3-2 1-4 2-6 4v-1-1l6-3c2-2 3-2 5-3 0-2-1-2-1-3h-1l-1-1h-1l1-2c-1-1-2-1-3-2v2l-2-1 1-1v-1h0c-1-2-2-1-4-2l1-2 1-1c0-1 1-2 2-3 0-1 0-1 1-2z" class="K"></path><path d="M468 290s1 1 2 1h1v1c0 1 0 2-1 3h0c-1-2-2-1-4-2l1-2 1-1zm-3 8l5-2-1 1 2 1v-2c1 1 2 1 3 2l-1 2h1l1 1h1c0 1 1 1 1 3-2 1-3 1-5 3l-6 3v-3l1-1-1-1-1 1c0 1-1 2-1 3l-1-1c1-1 1-2 1-3v-1l-2 2c-1-1-1-2-2-3 1-2 3-4 5-5z" class="C"></path><path d="M469 297l2 1v-2c1 1 2 1 3 2l-1 2-1 1v1l-2-1v-1h-1l-5 5c0-1 1-2 1-3 1-1 1 0 1-1l1-2c0-1 1-1 2-2z" class="a"></path><path d="M491 285c0-1 2-3 3-3h1 0v1 1l-7 9v1c-1 1-3 2-4 4l-1 3h-1v1c-2 0-2 1-4 0v-3l-1-1c1-1 1-3 2-4l1-1h-1-1c1-1 1-2 2-2v1h1c0-1 1-2 1-3s1 0 2-1h2 1c1-1 1-1 2-1l2-2z" class="M"></path><path d="M484 288h2 1c1-1 1-1 2-1l-6 6h-1v-2l-1 1c0-1 1-2 1-3s1 0 2-1z" class="P"></path><path d="M479 294l1 1h0l-1 1c1 1 2 1 3 1l1 1-2 1c0 1 0 1 1 2v1c-2 0-2 1-4 0v-3l-1-1c1-1 1-3 2-4z" class="a"></path><path d="M491 285c0-1 2-3 3-3h1 0v1 1l-7 9h0-1v-1l1-2c2-1 3-3 3-5z" class="X"></path><path d="M487 271l4-4 1 1h1c2-3 5-5 7-8 0 1 0 2 1 3v2c1-1 2-1 2-2 1 1 1 2 1 3h-1c-1 2-1 4-2 5l-16 9h-1 0v-2c0-2 5-5 6-7l-1 1-2-1z" class="AB"></path><path d="M501 272l3-2-1 3 1 2c0 1 0 2-1 3v1h-1c-2 1-3 3-5 4l-2 1v-1-1h0-1c-1 0-3 2-3 3l-2 2c-1 0-1 0-2 1h-1-2l2-2 1-2h0 0l-1-1c-1 1-1 1-2 1 0-1 0-2 1-2l2-2c1-1 2-1 3-2l6-3c2 0 4-2 5-3z" class="L"></path><path d="M503 273l1 2c0 1 0 2-1 3v1h-1c-2 1-3 3-5 4l5-9 1-1z" class="AN"></path><path d="M501 272l3-2-1 3-1 1s-1 0-1 1l-2 2v-1h-1 0c-2 2-3 4-5 5v-1h0c2-2 3-3 3-5 2 0 4-2 5-3z" class="e"></path><path d="M503 252h0l2-4h0c1-2 2-3 3-4l1 1v5c0 6-1 13-1 19h-1c0 3 0 5-1 7v-7l-2 1-3 2v-1c1-1 1-3 2-5h1c0-1 0-2-1-3 0 1-1 1-2 2v-2c-1-1-1-2-1-3h1c0-2 1-3 1-5l-7 6h-1c3-2 8-6 9-9z" class="X"></path><path d="M501 263l1 1v-1l1-1h2l1 1h0v-1c1 3 2 5 1 7 0 3 0 5-1 7v-7l-2 1-3 2v-1c1-1 1-3 2-5h1c0-1 0-2-1-3 0 1-1 1-2 2v-2z" class="L"></path><path d="M504 270l2-1v7c1-2 1-4 1-7h1c0 6 0 10-1 16h-1v-1c-2 3-3 6-3 10h-1 0-1v1l-1-1c-1 1-1 2-1 3-3 1-2 4-4 5-1-1-1 0 0-1l-1-1h0-1c0-1-1-2-1-2-1 1-2 1-3 1 0-1 1-2 2-3v-1h-1l-2-1v-1l7-9 2-1c2-1 3-3 5-4h1v-1c1-1 1-2 1-3l-1-2 1-3z" class="Aa"></path><path d="M499 292c1-1 1-3 2-4 0 1-1 4-1 6-1 1-1 2-1 3-3 1-2 4-4 5-1-1-1 0 0-1l-1-1h0-1c1-2 2-6 4-7h1l1-1z" class="L"></path><path d="M507 269h1c0 6 0 10-1 16h-1v-1c-2 3-3 6-3 10h-1 0l4-18c1-2 1-4 1-7z" class="q"></path><path d="M495 290h1v2h3l-1 1h-1c-2 1-3 5-4 7 0-1-1-2-1-2-1 1-2 1-3 1 0-1 1-2 2-3v-1l4-5z" class="AD"></path><path d="M497 283c2-1 3-3 5-4h1c0 2-1 3-2 4s-5 5-6 7l-4 5h-1l-2-1v-1l7-9 2-1z" class="AZ"></path><path d="M399 630c1 2 2 5 3 8 0 1 1 2 1 3 4 7 6 14 9 21 0 1 1 1 1 2l-4-3-7-5-1 8v27 5c0 2 0 7 1 9 0 1-1 3 0 4v4c0 1 0 2-1 3 1 4 0 7 1 10v-3l1-1c2 2 5 4 7 6 1 2 1 12 1 12 2 16 5 30 10 45l1 6v1c-1-1-1-2-2-4v1c0 2 0 3 1 5l-2 5c0 2-2 5-3 6-5 8-11 14-17 21-3 3-6 7-9 9l-15 15v-11-4-10l-1-23V686l11 9c3 2 6 5 10 8h0l1-2v-32-16l-5-4h2v-2-4c1 0 1-1 2-3l1 2v-3c1-3 1-5 1-7 0-1 2-2 2-2z" class="AO"></path><path d="M390 835l-1-1 1-1 1-1c0-2 1-3 2-5 0-1 1-1 2-1v-1h1v1h3c-3 3-6 7-9 9z" class="G"></path><path d="M399 630c1 2 2 5 3 8 0 1 1 2 1 3 4 7 6 14 9 21 0 1 1 1 1 2l-4-3-7-5-1 8v27 5c0 2 0 7 1 9 0 1-1 3 0 4v4c0 1 0 2-1 3 1 4 0 7 1 10h0v17l-1 43v11c0 1 0 3-1 4-1-1 0-6 0-8 0-7 0-15-1-22 0-3-1-7-2-11 0-15 1-31 0-47-1-3 0-7-2-10l1-2v-32-16l-5-4h2v-2-4c1 0 1-1 2-3l1 2v-3c1-3 1-5 1-7 0-1 2-2 2-2z" class="AQ"></path><path d="M399 630c1 2 2 5 3 8 0 1 1 2 1 3 4 7 6 14 9 21 0 1 1 1 1 2l-4-3-7-5-1 8v3c0-5 0-11-1-16v-3l-2-2v-1-1-1-1h0c0 2 0 7-1 8s-1 3-1 5c1 4 1 11 0 15v-1h0v-16l-5-4h2v-2-4c1 0 1-1 2-3l1 2v-3c1-3 1-5 1-7 0-1 2-2 2-2z" class="W"></path><path d="M402 638c0 1 1 2 1 3-2 2-2 4-2 7-1-2-1-3-2-5 1-2 1-4 3-5z" class="s"></path><path d="M395 640l1 2c-1 3 0 7 0 10v1l-5-4h2v-2-4c1 0 1-1 2-3z" class="w"></path><path d="M403 641c4 7 6 14 9 21l-11-7v-7c0-3 0-5 2-7z" class="N"></path><path d="M351 221h0c6-6 12-13 17-19 4-4 9-11 14-14l-4 6 69 57 1 2h-3c1 4 1 11-2 15l-1 1v1c-1 1-1 3-1 4l-3 3-5 5v3c0 1 0 0-1 1l1 1h0c0 2-1 3-2 5 0 2-1 3-1 5 0 0 1 0 1 1v1l-2-1v2h0-2l-1 1c0 1 0 1 1 2h-4s0-1-1-1c-2-1-4-1-6-1v1l3 1-1 1-1-1c-2-1-4-2-6-2-3-1-5-2-7-5h0l-1-2-1 2-1-1 1-1h1l1-1v-1h-1c-1-1-2-1-2-2l-1 1c-1-1-3-2-3-4h-1c-3-3-3-5-3-9-2-3 1-11 2-15l-3 1c1-2 2-4 2-6-1 1-1 1-3 1l-1-1c2-3 5-5 6-9l5-5c2-1 3-1 3-3-7 4-16 12-19 20-5 10-7 18-5 29l-6-1c-3 1-5 1-8 1-2-2-4-5-7-8-1-2-3-4-4-6l-4-6-1 2h-1-1l-2-2c-2 0-3-1-6-1l1-7h0v-9c1-2 1-6 2-8l3-12c-1-4 3-8 5-12z" class="AO"></path><path d="M378 259l1 1v7 3h0c0 2 1 4 0 5l-1-1v-3c-1-1 0-2-1-2 0 0-1-1-2-1l-1-3v-1h1 3v-5z" class="F"></path><path d="M438 277l-1-1c-2 0-3-1-5-2s-3-2-3-5v1c2 1 2 2 5 3 3 0 5-2 8-4v1c-1 1-1 3-1 4l-3 3z" class="a"></path><path d="M351 270c-1-1-2-2-3-4l1 1c1 1 2 1 3 2v1c1 1 3 2 4 3s2 1 3 1l1-1c1 0 1 0 2 1l-1 1s-1 1-1 2c1 1 1 3 2 5h0c-3-2-4-4-6-6l-1-1v1l-4-6z" class="D"></path><path d="M341 253c2 2 3 5 5 7v1l6 9v-1c-1-1-2-1-3-2l-1-1c1 2 2 3 3 4l-1 2h-1-1l-2-2c-2 0-3-1-6-1l1-7h0v-9z" class="AM"></path><path d="M341 262l5 8c-2 0-3-1-6-1l1-7z" class="N"></path><path d="M351 221h0c6-6 12-13 17-19 4-4 9-11 14-14l-4 6 69 57 1 2h-3c-7-8-16-14-24-20l-28-25-16-13-31 38c-1-4 3-8 5-12z" class="y"></path><defs><linearGradient id="Z" x1="390.383" y1="268.78" x2="416.264" y2="262.421" xlink:href="#B"><stop offset="0" stop-color="#120f0d"></stop><stop offset="1" stop-color="#322c2e"></stop></linearGradient></defs><path fill="url(#Z)" d="M407 239c4-2 8-4 14-5-2 3-5 5-8 8-8 8-14 15-15 27 0 4-1 7 0 11l1 1c-1 1 0 2 0 3l2 6-1 1c-1-1-3-2-3-4h-1c-3-3-3-5-3-9-2-3 1-11 2-15l-3 1c1-2 2-4 2-6-1 1-1 1-3 1l-1-1c2-3 5-5 6-9l5-5c2-1 3-1 3-3 1-1 2-1 3-2z"></path><path d="M407 239h0c3 0 6-2 9-3-2 1-3 2-5 3s-3 3-5 4c-4 6-9 12-11 19v1l-3 1c1-2 2-4 2-6-1 1-1 1-3 1l-1-1c2-3 5-5 6-9l5-5c2-1 3-1 3-3 1-1 2-1 3-2z" class="e"></path><path d="M396 249l3-1c0 1-1 3-2 4l-3 6c-1 1-1 1-3 1l-1-1c2-3 5-5 6-9z" class="AR"></path><path d="M399 281c0-3-1-6 0-8 2-2 3-2 5-3h1c-2 1-3 2-4 3v5 5c1 1 1 2 2 3h2c0-2 0-2 1-2l1 1c1 0 2 0 2 1h1l2-1v1l1 1c3-3 2 0 6-1v1l-1 1 1 1 1-1v-2l2-1c1-1 2 0 3-1s2-1 3-2h1 4v3c0 1 0 0-1 1l1 1h0c0 2-1 3-2 5 0 2-1 3-1 5 0 0 1 0 1 1v1l-2-1v2h0-2l-1 1c0 1 0 1 1 2h-4s0-1-1-1c-2-1-4-1-6-1v1l3 1-1 1-1-1c-2-1-4-2-6-2-3-1-5-2-7-5h0l-1-2-1 2-1-1 1-1h1l1-1v-1h-1c-1-1-2-1-2-2l-2-6c0-1-1-2 0-3z" class="t"></path><path d="M407 290c1 0 2 1 3 1 2 0 2 0 3-1v1h0l-2 2v1h-2c-1 0-1-2-3-2l1-2z" class="F"></path><path d="M403 286h2c0-2 0-2 1-2l1 1v1c1 1 2 1 3 3h-5c-1-1-1-2-2-3z" class="r"></path><path d="M399 281c0-3-1-6 0-8 2-2 3-2 5-3h1c-2 1-3 2-4 3v5 5c0 1 0 3 1 4 1 4 6 9 10 10 2 1 3 2 5 1 0 1 0 1 1 0h1 3c1-1 1-1 2-1 0 0 1 0 2-1 1 0 1-2 1-3l3-6c0-1 1-2 2-3h0v-1h-2l-1-1h4v3c0 1 0 0-1 1l1 1h0c0 2-1 3-2 5 0 2-1 3-1 5 0 0 1 0 1 1v1l-2-1v2h0-2l-1 1c0 1 0 1 1 2h-4s0-1-1-1c-2-1-4-1-6-1v1l3 1-1 1-1-1c-2-1-4-2-6-2-3-1-5-2-7-5h0l-1-2-1 2-1-1 1-1h1l1-1v-1h-1c-1-1-2-1-2-2l-2-6c0-1-1-2 0-3z" class="C"></path><path d="M362 157h0c5 1 10 4 15 5 10 1 20 1 30 1 7 0 14 1 21 1 7 1 14 2 21 4 9 1 18 2 26 6-29-2-63-7-89 10-1 1-1 2-3 3l-1 1h0c-5 3-10 10-14 14-5 6-11 13-17 19h0c-2 4-6 8-5 12l-3 12c-1 2-1 6-2 8v9h0l-1 7v4l1 5v2l1 4v5l1 2 1 9 1 1 1 8c0 1-1 0-2 0l1 3-1-2c-1-1-2-2-3-4l-5-4c-1-1-2-1-3-2-4-2-6-4-9-8h1v-1c0-1 1-1 2-1-2-1-4-3-5-4l-7-8c1 3 2 5 2 8h0l-1-2h0l-1 1c0 1 0 1 1 2h-1c0 1-1 1-1 2 1 2 3 2 3 5h-1v1l-3-3c-1-2-3-5-4-7-2-2-4-5-5-8v-5l-2-1c-1-4-3-8-5-12-1-5-3-10-5-14v-1c-1-4-2-8-2-13l1 1c1-1 1-3 1-5s1-4 1-6c1-1 1-2 1-3 0-2 1-5 2-7l-2 2c0 1 0 0-1 1 0 1 0 1-1 1v-1c0-2 1-2 0-4v-1l1-3 2-2 6-6h1l3-3 3-3 3-1c2-1 3-2 5-2l12-7c5-2 10-2 15-5l27-9c2 0 3 0 4-1h1l4-2h1c-1-2-5-2-7-2-2 1-5 1-7 1h0c-1-1-1-3-1-4l-5-3h2z" class="AP"></path><path d="M303 211v-1c2-1 3-3 5-3v2 2l-3 5v1h-1l1-3c0-2-1-2-2-3z" class="J"></path><path d="M329 247v-4c0-1 1-1 2-2v1h1v13c-1-1-2-2-2-3-1-1-1-4-1-5z" class="L"></path><path d="M341 280l1 4v5l1 2 1 9 1 1 1 8c0 1-1 0-2 0l-3-6-3-10c2 2 2 4 3 6 1 0 0 0 1-1s1-1 1-2l-1-1c-1-4-3-7-2-11 1-1 1-3 1-4z" class="a"></path><path d="M358 184c1-1 2-1 3-2l6-1c-5 4-9 9-13 13l-1 1c-1 1 0 1-1 2 0 1-1 1-2 2h0c-1 1-2 2-3 4s-3 6-6 8c3-5 5-10 8-14 1-1 2-2 2-4 0-1 3-3 4-4v-1c1 0 1-1 2-1 0-1 1-2 1-3zm-40 16c0-1 1-1 2-2 0 1 0 0 1 1 1-2 3-3 5-4l-2 3v3h1l1-1c1-2 6-7 9-7-1 1-3 2-3 3l1 1-6 7-2 3c-1 2-2 3-4 5v-1c0-2 0-3-2-5h-2 0c1-2 1-4 1-6z" class="Y"></path><path d="M324 204c0-1 1-2 2-3v1 2h1l-2 3h-1c-1-1 0-2 0-3z" class="a"></path><path d="M317 206c1-1 2-5 4-6 0 2 1 1 1 3v2c-1 1-2 1-3 1h-2z" class="C"></path><path d="M324 204c0 1-1 2 0 3h1c-1 2-2 3-4 5v-1c0-2 0-3-2-5 1 0 2 0 3-1l2-1z" class="J"></path><path d="M318 200c0 2 0 4-1 6h0 2c2 2 2 3 2 5v1l-1 1h0l-1 1c-1-1 0-1-1-1-1 1-2 1-3 1h-1l-1 2v2l-1 1h-1c0-1 1-2 0-3 0 0-1 0-2-1-1 1-2 1-3 1h-1l3-5v-2l3-3c0-1 0 0 1-1l3-3c1 0 2-1 3-2z" class="O"></path><path d="M314 214c1-2 2-3 4-4 1 1 1 1 2 3l-1 1c-1-1 0-1-1-1-1 1-2 1-3 1h-1z" class="AG"></path><path d="M318 200c0 2 0 4-1 6h0l-1 3h-1 0v-2c-1 0-1-1-1-2-2 1-2 1-3 1 0-1 0 0 1-1l3-3c1 0 2-1 3-2z" class="C"></path><path d="M308 211h1c2-1 3-2 5-3-1 2-2 7-1 8v2l-1 1h-1c0-1 1-2 0-3 0 0-1 0-2-1-1 1-2 1-3 1h-1l3-5z" class="K"></path><path d="M386 184c-1 1-1 2-3 3l-1 1h0c-5 3-10 10-14 14-5 6-11 13-17 19h0c7-16 20-29 35-37z" class="G"></path><defs><linearGradient id="a" x1="324.758" y1="230.187" x2="336.518" y2="242.071" xlink:href="#B"><stop offset="0" stop-color="#6f706a"></stop><stop offset="1" stop-color="#989898"></stop></linearGradient></defs><path fill="url(#a)" d="M341 211c3-2 5-6 6-8s2-3 3-4v1c-2 3-5 7-7 10-6 10-10 21-11 32h-1v-1c-1 1-2 1-2 2v4l-1 4c-1-1-1-2-1-3h-1c0-2-1-5-1-6v-1-2c2-2 2-4 2-6 1-2 2-4 2-6 1-1 1-2 1-3 1 1 1 2 2 3l1-7c1-1 2-3 2-4l2-3 2-2v3l1-2c0-1 0-1 1-1z"></path><path d="M337 213l2-2v3c-2 6-5 11-7 17l-1 1h0c-1-2 0-3 1-5l1-7c1-1 2-3 2-4l2-3z" class="L"></path><path d="M358 184c0 1-1 2-1 3-1 0-1 1-2 1v1c-1 1-4 3-4 4 0 2-1 3-2 4-3 4-5 9-8 14-1 0-1 0-1 1l-1 2v-3l-2 2-2 3v-2l1-1h-1-1l7-15 2-3-1-1-2 2c0 1 0 1-2 2l1-2c-1-1-1-2-2-3l6-3c5-1 8-4 13-5l2-1z" class="Y"></path><path d="M358 184c0 1-1 2-1 3-1 0-1 1-2 1v1c-4 1-8 5-10 8v-2c2-2 6-7 8-8s2-1 3-2l2-1z" class="C"></path><defs><linearGradient id="b" x1="344.01" y1="194.888" x2="343.506" y2="210.628" xlink:href="#B"><stop offset="0" stop-color="#4c4947"></stop><stop offset="1" stop-color="#686868"></stop></linearGradient></defs><path fill="url(#b)" d="M345 197c2-3 6-7 10-8-1 1-4 3-4 4 0 2-1 3-2 4-3 4-5 9-8 14-1 0-1 0-1 1l-1 2v-3l-2 2c2-5 5-12 8-16z"></path><path d="M337 193c1 1 1 2 2 3l-1 2c2-1 2-1 2-2l2-2 1 1-2 3-7 15h1 1l-1 1v2c0 1-1 3-2 4l-1 7c-1-1-1-2-2-3 0 1 0 2-1 3 0 2-1 4-2 6 0 2 0 4-2 6v2 1c0 1 1 4 1 6-2-2-2-6-3-8l-3-6c0-1-1-1-2-2 1-1 1-3 2-5v-1l1-4-1-3v-1-1h0-2 0l1-3 1-1h0l1-1c2-2 3-3 4-5l2-3 6-7h1c0-2 2-3 3-4z" class="M"></path><path d="M337 193c1 1 1 2 2 3l-1 2c2-1 2-1 2-2l2-2 1 1-2 3h-1l-3 3h-1v-2c-1 1-3 4-4 5-1-2 1-2 0-4 1-1 1-2 1-3h1c0-2 2-3 3-4z" class="C"></path><path d="M325 207l2-1 1 1s-1 1-1 2l-1 3c-1 1-1 2 0 3v5l1 2c-1 1-1 2-2 2h-1-2l-1 4-1-2 1-4-1-3v-1-1h0-2 0l1-3 1-1h0l1-1c2-2 3-3 4-5z" class="a"></path><path d="M326 215v5l1 2c-1 1-1 2-2 2h-1-2c1-3 2-6 4-9zm-1-8l2-1 1 1s-1 1-1 2l-2 3c-2 2-3 4-4 7l1 1-1 2-1-3v-1-1h0-2 0l1-3 1-1h0l1-1c2-2 3-3 4-5z" class="K"></path><path d="M327 222v-4h0c1-1 2-2 2-3l1-1v1l2-1h0c0 2 0 3-1 5-1 1-1 1-1 3 1-1 2-1 3-2l-1 7c-1-1-1-2-2-3 0 1 0 2-1 3 0 2-1 4-2 6 0 2 0 4-2 6v2 1c0 1 1 4 1 6-2-2-2-6-3-8l-3-6c0-1-1-1-2-2 1-1 1-3 2-5v-1l1 2 1-4h2 1c1 0 1-1 2-2z" class="d"></path><path d="M320 227v-1l1 2c0 3 0 5 1 7 0 2 1 3 1 5l-3-6c0-1-1-1-2-2 1-1 1-3 2-5z" class="J"></path><path d="M325 239c-2-6 1-12 3-17v4 2c0 1 0 2-1 3v2c0 2 0 4-2 6z" class="q"></path><defs><linearGradient id="c" x1="366.261" y1="196.812" x2="354.299" y2="170.744" xlink:href="#B"><stop offset="0" stop-color="#9d9d9b"></stop><stop offset="1" stop-color="#c1c1c0"></stop></linearGradient></defs><path fill="url(#c)" d="M362 157h0c5 1 10 4 15 5 10 1 20 1 30 1 7 0 14 1 21 1 7 1 14 2 21 4h-1c-5 0-11-1-16-1-12-1-24-1-35-1-8 1-16 2-23 4-10 3-20 7-30 11l-28 12c-6 3-12 6-17 12-1 1-3 4-3 6l-2 2c0 1 0 0-1 1 0 1 0 1-1 1v-1c0-2 1-2 0-4v-1l1-3 2-2 6-6h1l3-3 3-3 3-1c2-1 3-2 5-2l12-7c5-2 10-2 15-5l27-9c2 0 3 0 4-1h1l4-2h1c-1-2-5-2-7-2-2 1-5 1-7 1h0c-1-1-1-3-1-4l-5-3h2z"></path><path d="M365 160l8 3c-2 1-5 1-7 1h0c-1-1-1-3-1-4z" class="C"></path><path d="M303 211c1 1 2 1 2 3l-1 3h1v-1h1c1 0 2 0 3-1 1 1 2 1 2 1 1 1 0 2 0 3h1l1-1v-2l1-2h1c1 0 2 0 3-1 1 0 0 0 1 1l-1 3h0 2 0v1 1l1 3-1 4v1c-1 2-1 4-2 5 1 1 2 1 2 2l3 6c1 2 1 6 3 8h1c0 1 0 2 1 3l1-4c0 1 0 4 1 5 0 1 1 2 2 3v18c1 1 1 2 1 4l5 16 3 10 3 6 1 3-1-2c-1-1-2-2-3-4l-5-4c-1-1-2-1-3-2-4-2-6-4-9-8h1v-1c0-1 1-1 2-1-2-1-4-3-5-4l-7-8c1 3 2 5 2 8h0l-1-2h0l-1 1c0 1 0 1 1 2h-1c0 1-1 1-1 2 1 2 3 2 3 5h-1v1l-3-3c-1-2-3-5-4-7-2-2-4-5-5-8v-5l-2-1c-1-4-3-8-5-12-1-5-3-10-5-14v-1c-1-4-2-8-2-13l1 1c1-1 1-3 1-5s1-4 1-6h1c1-1 1-2 2-2 0 1 0 1 1 2 1-1 1-3 2-4 1-2 3-4 4-6z" class="AF"></path><path d="M314 244c2 3 2 4 4 6 1 1 1 3 2 5 0 2 1 3 1 4 1 2 2 4 2 5v2c0 1 1 3 1 3v3c-1 0-1 0-1-1-4-2-5-7-6-11 0-2 0-4-1-6s-1-3-1-5c0-1 0-3-1-5z" class="e"></path><path d="M323 266c-2-2-3-4-3-7h1c1 2 2 4 2 5v2z" class="AB"></path><path d="M324 264h1c1-1 2-1 3 0 1 4 2 10 3 14l2-1 5 16 3 10h0l-3-2c-1-1-2-2-2-4-1-3-4-7-6-10 1 0 0-1 0-1l1-1v-1c-1-1 0-1-1-3 0 0-1-1-1-2v-1l1 1c0-3-2-7-3-10 0-2-1-2-2-3 0 0 0-1-1-2z" class="AD"></path><path d="M333 277l5 16 3 10h0l-3-2v-2l-3-6c0-2 0-4-1-5l-1-1c-1-3-1-6-2-9l2-1z" class="AF"></path><path d="M320 274l4 4h0c1 0 2 1 2 2l1 1c1 1 2 3 3 6h0c2 3 5 7 6 10 0 2 1 3 2 4l3 2h0l3 6 1 3-1-2c-1-1-2-2-3-4l-5-4c-1-1-2-1-3-2-4-2-6-4-9-8h1v-1c0-1 1-1 2-1-2-1-4-3-5-4 0-1 1 0 2-1-1-3-2-5-3-8-1-1-1-2-1-3z" class="e"></path><path d="M325 291c3 1 4 2 6 5 2 1 2 1 3 3 1 0 1 0 1 1h0c2 1 4 3 6 5v1l-5-4c-1-1-2-1-3-2-4-2-6-4-9-8h1v-1z" class="P"></path><path d="M320 274l4 4 3 7c0 1 1 2 1 2l2 4c-1 0-2-1-3-1-2-1-4-3-5-4 0-1 1 0 2-1l-3-8c-1-1-1-2-1-3z" class="Aa"></path><path d="M314 238c1-3 4-10 6-11-1 2-1 4-2 5 1 1 2 1 2 2l3 6c1 2 1 6 3 8h1c0 1 0 2 1 3l1-4c0 1 0 4 1 5 0 1 1 2 2 3v18c1 1 1 2 1 4l-2 1c-1-4-2-10-3-14-1-1-2-1-3 0h-1 0-1c0-1-1-3-2-5 0-1-1-2-1-4-1-2-1-4-2-5-2-2-2-3-4-6 0-1 0-2-1-3h0c0-1 1-2 1-3h0z" class="X"></path><path d="M313 241c2 1 2 1 3 2 1 2 2 5 2 7-2-2-2-3-4-6 0-1 0-2-1-3z" class="AD"></path><path d="M314 238c1-3 4-10 6-11-1 2-1 4-2 5l-1 1h0c1 1 0 2 0 3v2l-1 1v1 3c-1-1-1-1-3-2h0c0-1 1-2 1-3h0z" class="AL"></path><g class="AB"><path d="M324 264c-1-2-1-5-2-7-1-5-3-9-4-14 0-1 0-2 1-3h1c0 2 0 3 1 5 0 2 1 3 3 4 0 2 1 3 1 4s0 2 1 2v1c0 1 0 2 1 3v2c0 1 1 1 1 2v1c-1-1-2-1-3 0h-1 0z"></path><path d="M327 256l-1-4-1-4-2-5-1-1-3-8h1l3 6c1 2 1 6 3 8h1c0 1 0 2 1 3l1-4c0 1 0 4 1 5 0 1 1 2 2 3v18c1 1 1 2 1 4l-2 1c-1-4-2-10-3-14v-1c0-1-1-1-1-2v-2c-1-1-1-2-1-3h1z"></path></g><path d="M326 256h1c1 1 1 2 1 3 1 3 2 12 4 14 1 1 1 2 1 4l-2 1c-1-4-2-10-3-14v-1c0-1-1-1-1-2v-2c-1-1-1-2-1-3z" class="e"></path><defs><linearGradient id="d" x1="305.786" y1="267.598" x2="322.578" y2="268.931" xlink:href="#B"><stop offset="0" stop-color="#72726f"></stop><stop offset="1" stop-color="#8e8f8b"></stop></linearGradient></defs><path fill="url(#d)" d="M313 241h0c1 1 1 2 1 3 1 2 1 4 1 5 0 2 0 3 1 5s1 4 1 6l-2 1v3c0 1 0 0 1 1s1 2 1 3l3 6c0 1 0 2 1 3l3 8c-1 1-2 0-2 1l-7-8c1 3 2 5 2 8h0l-1-2h0l-1 1c0 1 0 1 1 2h-1c0 1-1 1-1 2 1 2 3 2 3 5h-1v1l-3-3c-1-2-3-5-4-7-2-2-4-5-5-8v-5c1 2 0 3 1 4l1 1 1-2-1-2 1-3h-1v-3l-1-2h1c-1-2-3-4-2-7l3 4h0c0 2 1 3 2 5v-2c1-1 1-2 1-4h0c1-1 0-1 0-2v-1-1c0-3 0-6 1-9v-1c0-3 1-4 2-6z"></path><path d="M311 251h0c1 2 1 6 1 9v2h-1v-11z" class="AF"></path><path d="M307 270h-1v-3l-1-2h1c-1-2-3-4-2-7l3 4h0c0 2 1 3 2 5l1 2v3 1c0 1 0 1 1 2l-1 1c-2-1-2-2-2-3-1-1-1-2-1-3z" class="e"></path><path d="M314 214h1c1 0 2 0 3-1 1 0 0 0 1 1l-1 3h0 2 0v1 1l1 3-1 4v1c-2 1-5 8-6 11h0c0 1-1 2-1 3-1 2-2 3-2 6v1c-1 3-1 6-1 9v1 1c0 1 1 1 0 2h0c0 2 0 3-1 4v2c-1-2-2-3-2-5h0l-3-4c-1-3-2-6-2-9v-10l1-9h1v3c3-2 3-4 5-7 1-2 2-4 3-7l1-1v-2l1-2z" class="P"></path><path d="M310 237h3l1 1h0c-1 1-2 1-3 2 0 2-1 3-2 4v-1c0-2 0-4 1-6z" class="q"></path><path d="M302 239l1-9h1v3 2c-1 4 0 7 0 11 0 1 0 1 1 2h0c-1 5-1 9 2 13v1h0l-3-4c-1-3-2-6-2-9v-10zm12-25h1c1 0 2 0 3-1 1 0 0 0 1 1l-1 3h0 2 0v1 1l1 3-1 4v1c-2 1-5 8-6 11l-1-1h-3v-1c0-2 1-4 2-7s3-7 4-10c1-1 1-1 1-2h-1l-2 1h-1v-2l1-2z" class="f"></path><path d="M312 229c2 0 2-2 3-2-1 3-2 7-5 9 0-2 1-4 2-7zm-9-18c1 1 2 1 2 3l-1 3h1v-1h1c1 0 2 0 3-1 1 1 2 1 2 1 1 1 0 2 0 3h1c-1 3-2 5-3 7-2 3-2 5-5 7v-3h-1l-1 9v10c0 3 1 6 2 9-1 3 1 5 2 7h-1l1 2v3h1l-1 3 1 2-1 2-1-1c-1-1 0-2-1-4l-2-1c-1-4-3-8-5-12-1-5-3-10-5-14v-1c-1-4-2-8-2-13l1 1c1-1 1-3 1-5s1-4 1-6h1c1-1 1-2 2-2 0 1 0 1 1 2 1-1 1-3 2-4 1-2 3-4 4-6z" class="i"></path><path d="M303 211c1 1 2 1 2 3l-1 3h1c0 1-1 2-1 3-1 2-1 4-1 6-3-3 2-7-4-9 1-2 3-4 4-6z" class="K"></path><path d="M294 232c1-3 1-5 1-7l1-2v2h1c2 3 0 6 1 9v10c0 2 1 4 1 6v1l-2-3c0-2 0-7-1-8 0 0-2-1-2-2v-6zm6 21l1-1v-3c-1-4-1-8 0-11v-2h1v3 10c0 3 1 6 2 9-1 3 1 5 2 7h-1l1 2v3h1l-1 3c-1-2 0-3-1-5s-2-4-3-7c-1-2-1-5-2-8z" class="P"></path><path d="M293 221h1c0 2 0 1 1 3 0 1 0 1-1 2v6 6c0 1 2 2 2 2 1 1 1 6 1 8l2 3c0 1 0 2 1 2 1 3 1 6 2 8 1 3 2 5 3 7s0 3 1 5l1 2-1 2-1-1c-1-1 0-2-1-4l-2-1c-1-4-3-8-5-12-1-5-3-10-5-14v-1c-1-4-2-8-2-13l1 1c1-1 1-3 1-5s1-4 1-6z" class="d"></path><path d="M294 238c0 1 2 2 2 2 1 1 1 6 1 8-1-1-1-2-2-3v-1l-1-1v-2-3z" class="i"></path><path d="M290 231l1 1c1-1 1-3 1-5 0 7 1 12 2 19h-1l-1-2c-1-4-2-8-2-13z" class="X"></path><path d="M404 241c0 2-1 2-3 3l-5 5c-1 4-4 6-6 9l1 1c2 0 2 0 3-1 0 2-1 4-2 6l3-1c-1 4-4 12-2 15 0 4 0 6 3 9h1c0 2 2 3 3 4l1-1c0 1 1 1 2 2h1v1l-1 1h-1l-1 1 1 1 1-2 1 2h0c2 3 4 4 7 5 2 0 4 1 6 2l1 1 5 1 3 2c-3 1-2 0-6-1l-2 1c2 0 3 0 4 2h1l-2 1h-1l-1-1-1 1v1l2 2v1c2 1 4 2 7 3h0c1 2 4 2 6 3h2l1 1h1l2 2 3 3v2l2-2c1-1 4 0 6 0l1 2c1 0 1 0 2-1l1 2c0 1 0 2 2 3h2l-2 2h-1-1c0-2-1-2-1-3-3 0-5-2-7-3l-2 2 1 1h-1l-2-2c-1 0 0 1 0 2h-1c2 3 4 7 3 10v1l-1 1h1c1-1 1-1 1-2h1l6 12c1 2 1 4 2 5v2 1c1 3 1 6 0 10l-2 7c-1 3-1 6-1 9v1c-1-1-1-2-2-3v4c-1 2-1 5-1 6 0 6 1 12-1 17 0 2-3 5-4 8-1 1-1 2-3 2l2-3-1-1c-4 3-4 8-7 11-2 2-3 2-5 3l-3 3c-1-1-1 0-2-2l1-2-1-1-1 3h-1c-1 0-2 1-4 1 0 1-1 1-1 1l-2 1h0-3l1 1-2 1c-1 0-2-1-2-3l-1 1v1h0l-1 1c0 2-1 5-1 7 0 1-1 2-1 3v1c1 1 1 2 1 3h1 1l1-1 2 3-1 1c0 1 1 2 1 4l2 3h-2v-1c-1 0-2 0-3-1 0-2-1-2-2-3v2l-6-11-4-7c-2-6-5-13-8-18l-9-22h1v-1c1-1 2-2 4-3l2-1c1-1 2-2 3-2l3-3h-1c-1 0-2 0-3-1l3-2h-1 0c2-1 3-2 5-3l-1-4v-2c-1-1-1-1-1-3v-3c-1-2-2-2-3-4 0-1 0-1 1-2s2-1 4-1h1s0-1 1-1h1c1-1 2-1 3 0v-6c1-2 1-3 2-5h0c1-2 1-3 1-4l-1-1 1-2v-3-3l1-1-3-3c1-2 1-2 2-3 2 0 3-1 4 0 0-1 1-2 1-3 0-3 1-2 3-4-1-1-2-1-3-2s-2-2-3-2c-3-1-5-2-8-4l-3-3c-7-5-14-15-15-24v-2c-2-11 0-19 5-29 3-8 12-16 19-20z" class="S"></path><path d="M412 353c1 0 1 0 2 1s1 1 0 3h-1c-1 0-1-1-2-2l1-2z" class="W"></path><path d="M404 347l1-1c1 0 2 0 2-1v3c1 0 1 0 2 1l1-1 1 1-1 1c-2 0-2 0-3 1-1 2-1 2-2 3h-2 0c1-2 1-3 1-4l-1-1 1-2z" class="AI"></path><path d="M420 366c1-2 1-3 2-4 0-1 1-3 1-4v-1h1c1 2 0 4 0 6 1 2 0 4 0 6v-3h-1c0 2-2 5-3 7l1-6s0-1-1-1z" class="E"></path><path d="M395 366h1s0-1 1-1h1c1-1 2-1 3 0-1 0-1 1-2 1-1 1-2 1-2 2v1l-3 2 1 1c1 0 1 0 2-1 1 1 2 1 2 2l1 1c-1-1-2-1-3-2-2 2-2 0-3 0l-1 1c-1-2-2-2-3-4 0-1 0-1 1-2s2-1 4-1z" class="AJ"></path><path d="M391 367h2v3c-2 0-2 0-3-1 0-1 0-1 1-2z" class="AC"></path><path d="M442 352v-1l1-1v1c1 1 1 2 2 4l-1 1h-1l1 1c0 1-1 1-1 2v1h0 0c-4 1-6 6-8 9v-11c1 0 2 1 2 2s-1 1 0 2l2-2c1-1 1-2 1-4l2-2v-2z" class="Aa"></path><path d="M439 348c1 0 3 0 4 1v1l-1 1v1 2l-2 2c0 2 0 3-1 4l-2 2c-1-1 0-1 0-2s-1-2-2-2c1-4 3-7 4-10z" class="AN"></path><path d="M440 356c-1 2-1 3-3 3v-1c0-1 1-2 1-3h0c1-2 2-2 4-3v2l-2 2z" class="AZ"></path><path d="M420 366c1 0 1 1 1 1l-1 6c1-2 3-5 3-7h1v3l-2 15c-1 2-1 4-2 6l-1-1c0-1 0-3 1-4 0-2 0-4 1-5v-1l-3-9c0-1 1-3 2-4z" class="H"></path><path d="M402 409c2-1 3-1 4-2l-2-1h0 2v-1h0c1 1 1 1 1 3-1 1-2 1-3 1-1 1-1 2-1 3l1 2c2-1 1-3 4-3l-1-1h0l-1-1h1 1c0 3 0 4-1 6 0 2 0 3 1 4v1c-2 1-2 3-2 6h1 0 2l-1 1h-2l-1-1c0-2 0-3-1-4h-1-2v-2l-2-3h0l1-1c0-3 2-4 2-7z" class="U"></path><path d="M403 422c0-1 1-2 1-4h0c0-1 0-1 1-2 1 1 1 2 3 4-2 1-2 3-2 6h1 0 2l-1 1h-2l-1-1c0-2 0-3-1-4h-1z" class="R"></path><path d="M405 310v-1c3 3 7 5 10 7 2 1 3 1 5 1h0c3 2 7 4 9 6 5 3 8 8 11 14l-2 1c-4-9-13-15-22-20-4-2-8-5-11-8z" class="e"></path><path d="M450 361c0-2 0-2-1-3v-1h0c2 1 3 2 5 3v1c1 3 1 6 0 10l-2 7c-1 3-1 6-1 9v1c-1-1-1-2-2-3 1-1 0-6 1-8v-2-14z" class="E"></path><path d="M450 361c0-2 0-2-1-3v-1h0c2 1 3 2 5 3v1l-2 2c0 1 0 4-1 6 0 3 1 4-1 6v-14z" class="B"></path><path d="M416 318c-2 0-4-1-6-2h0c-2-2-5-4-7-6-1-1-2-3-3-3h-1c-1-1-4-4-4-6h0c-1-1-2-1-2-2l-1-1v1c-2-4-6-6-6-11v-1-1-1c-1-1 0-2-1-3 0-1-1-1-1-2v-2h0v-1c0-1 0-2-1-2v-1c1-1 1-1 1-2v-1c0-1 0-2 1-3h0v-1l1-1v-2h0c1-2 1-2 2-3-1 2-1 3-1 5-1 1-2 3-2 5v1c-1 3 1 7 1 10l3 3v1c1 2 2 4 3 5 0 1 1 2 1 3 2 1 3 5 4 7 2 3 5 7 8 9 3 3 7 6 11 8z" class="E"></path><path d="M386 282l3 3v1c1 2 2 4 3 5 0 1 1 2 1 3 2 1 3 5 4 7l-2-1c-2-3-4-5-6-9-2-3-2-5-3-9z" class="J"></path><path d="M440 337l4 5-1 1h1c1-1 1-1 1-2h1l6 12c1 2 1 4 2 5v2c-2-1-3-2-5-3h0v1c1 1 1 1 1 3-1 0-2-1-4-1h-3 0v-1c0-1 1-1 1-2l-1-1h1l1-1c-1-2-1-3-2-4v-1-1c-1-1-3-1-4-1v-1c1-3 0-6-1-9l2-1z" class="e"></path><path d="M444 348c1 1 4 3 5 5v1c-1 1-1 1-2 0-2-1-2-3-3-6z" class="Aa"></path><path d="M440 337l4 5-1 1h-3c0 1 1 2 1 4h-2c1-3 0-6-1-9l2-1z" class="AF"></path><path d="M404 241c0 2-1 2-3 3l-5 5c-1 4-4 6-6 9l1 1c2 0 2 0 3-1 0 2-1 4-2 6-1 3-2 5-2 9v6 1 1c0 2-1 3 0 5h-1v-1l-3-3c0-3-2-7-1-10v-1c0-2 1-4 2-5 0-2 0-3 1-5 0-1 1-2 1-2l1-2v-1h1v-2c-2 2-4 5-6 7h0c3-8 12-16 19-20z" class="d"></path><path d="M394 258c0 2-1 4-2 6-1 3-2 5-2 9-1-2-1-3 0-4 0-2 0-3-1-4-1-3 0-4 1-7l1 1c2 0 2 0 3-1z" class="L"></path><path d="M386 282c0-3-2-7-1-10v-1c0-2 1-4 2-5h0c0 4-1 7 0 10v1c0 1 0 0 1 1v2c1 1 0 1 1 2v3l-3-3z" class="M"></path><defs><linearGradient id="e" x1="437.982" y1="326.667" x2="431.554" y2="330.051" xlink:href="#B"><stop offset="0" stop-color="#141314"></stop><stop offset="1" stop-color="#2f2e2d"></stop></linearGradient></defs><path fill="url(#e)" d="M420 317h1 6 0c1 2 4 2 6 3h2l1 1h1l2 2 3 3v2l2-2c1-1 4 0 6 0l1 2c1 0 1 0 2-1l1 2c0 1 0 2 2 3h2l-2 2h-1-1c0-2-1-2-1-3-3 0-5-2-7-3l-2 2 1 1h-1l-2-2c-1 0 0 1 0 2h-1c2 3 4 7 3 10v1l-4-5c-3-6-6-11-11-14-2-2-6-4-9-6z"></path><path d="M433 320h2l1 1h1l2 2 3 3v2l2-2c1-1 4 0 6 0l1 2c1 0 1 0 2-1l1 2c0 1 0 2 2 3h2l-2 2h-1-1c0-2-1-2-1-3-3 0-5-2-7-3l-2 2 1 1h-1l-2-2c-1 0 0 1 0 2h-1c-2-3-6-6-7-8 2 0 3 3 5 4-1-3-4-5-6-7z" class="O"></path><path d="M401 384c-1-1-1-2-2-2-1-1-1-2-2-4l1-1 2 2c1 2 2 4 4 5h0c1 1 2 2 2 3l1 1 3 3 1 1v1c0 2 2 5 3 7 0 2 2 5 3 7l-1 1c-2-2-3-7-5-9l-1-1v-1s-1-1-1-2l-3 5c-1 1-3 2-4 5l-2 2c1 1 2 1 2 2 0 3-2 4-2 7l-1 1h0c-1 1-1 1-2 1v-1c0-2 1-3 1-4v-6c1-1 2-2 3-4 0-1 0-1 1-2v-1c-1 1-1 1-2 1-2 1-3 5-5 4l4-7c2-2 3-4 5-6h1l-2-1c-1 0-2-1-3-1l1-2v-4z" class="R"></path><path d="M404 392v1 1c-1 1-3 2-3 4 0 1 0 2-1 3-2 1-3 5-5 4l4-7c2-2 3-4 5-6z" class="Q"></path><path d="M401 384l2 2c1 3 4 4 4 8h0c-2 0-2-1-2-2l-2-1c-1 0-2-1-3-1l1-2v-4z" class="b"></path><path d="M412 327c2 1 5 4 7 6l-1 3h-1v1c1 0 1 0 2 1h-2v1h0v2c1 1 1 1 2 1h0c1-1 2-1 2-3l1 1c0 2-1 3-3 4-1 2-2 3-4 3h-3c-1 0-2-2-3-2 0 1 0 2 1 3l-1 1c-1-1-1-1-2-1v-3c0 1-1 1-2 1l-1 1v-3-3l1-1-3-3c1-2 1-2 2-3 2 0 3-1 4 0 0-1 1-2 1-3 0-3 1-2 3-4z" class="AE"></path><path d="M404 344v-3l1-1-3-3c1-2 1-2 2-3 2 0 3-1 4 0 2 1 2 1 4 1h0c-1 1-1 1-2 1-1 2 1 4 1 6 1 2 2 3 3 5h1-3c-1 0-2-2-3-2 0 1 0 2 1 3l-1 1c-1-1-1-1-2-1v-3c0 1-1 1-2 1l-1 1v-3zm-11 29l1-1c1 0 1 2 3 0 1 1 2 1 3 2v5l-2-2-1 1c1 2 1 3 2 4 1 0 1 1 2 2v4l-1 2c1 0 2 1 3 1l2 1h-1c-2 2-3 4-5 6l-4 7h0c-1 1-1 3-2 4h-2c0-3-1-4-2-5l-1-1c-1 0-1-1-1-1l2-2c-1-1-2-1-3-1l-2-2 2-1c1-1 2-2 3-2l3-3h-1c-1 0-2 0-3-1l3-2h-1 0c2-1 3-2 5-3l-1-4v-2c-1-1-1-1-1-3v-3z" class="H"></path><path d="M399 398s0-1 1-2h0-2v-2c-1-1 0-1-1-2 1-1 2-1 3-2 1 0 2 1 3 1l2 1h-1c-2 2-3 4-5 6z" class="U"></path><path d="M395 385l1-2h1c0 2 1 2 0 3-1 2-3 5-5 5h-1c-1 0-2 0-3-1l3-2h-1 0c2-1 3-2 5-3z" class="AT"></path><path d="M386 396c1-1 2-2 3-2l-1 1c1 1 2 1 4 1 1-1 1-1 2 0s1 1 0 2-2 2-4 2l-2 2v1c-1 0-1-1-1-1l2-2c-1-1-2-1-3-1l-2-2 2-1z" class="b"></path><path d="M392 264l3-1c-1 4-4 12-2 15 0 4 0 6 3 9h1c0 2 2 3 3 4l1-1c0 1 1 1 2 2h1v1l-1 1h-1l-1 1 1 1 1-2 1 2h0c2 3 4 4 7 5 2 0 4 1 6 2l1 1 5 1 3 2c-3 1-2 0-6-1l-2 1c2 0 3 0 4 2h1l-2 1h-1l-1-1-1 1v1l2 2v1c2 1 4 2 7 3h-6-1 0c-2 0-3 0-5-1-3-2-7-4-10-7v1c-3-2-6-6-8-9-1-2-2-6-4-7 0-1-1-2-1-3-1-1-2-3-3-5h1c-1-2 0-3 0-5v-1-1-6c0-4 1-6 2-9z" class="K"></path><path d="M393 286c2 4 5 6 6 9 1 2 4 4 6 5l-3-4 1-2 1 2h0v1l2 2c0 1 1 2 1 3h0c-1 1-1 1-2 0l-2-1h0l1 1h-1l-4-4c0 1 0 2 1 3s1 1 1 2c-2-1-5-8-6-10s-2-4-2-7z" class="J"></path><path d="M393 278c0 4 0 6 3 9h1c0 2 2 3 3 4l1-1c0 1 1 1 2 2h1v1l-1 1h-1l-1 1 1 1 3 4c-2-1-5-3-6-5-1-3-4-5-6-9-1-2-1-5 0-8z" class="Y"></path><path d="M401 290c0 1 1 1 2 2l-1 1c-1 0-1 0-2-2l1-1z" class="B"></path><path d="M404 296c2 3 4 4 7 5 2 0 4 1 6 2l1 1 5 1 3 2c-3 1-2 0-6-1l-2 1c2 0 3 0 4 2h1l-2 1h-1l-1-1-1 1v1l2 2v1c2 1 4 2 7 3h-6-1 0l-2-1c-1-1-3-3-5-3-1-1-3-2-4-3-1-3-4-5-6-8h1l-1-1h0l2 1c1 1 1 1 2 0h0c0-1-1-2-1-3l-2-2v-1z" class="M"></path><path d="M410 308c-1 0-1-1-1-2h1c0-1 1-2 3-3h3 1l1 1 5 1 3 2c-3 1-2 0-6-1l-2 1c-3 0-5 0-8 1z" class="i"></path><path d="M410 308c3-1 5-1 8-1 2 0 3 0 4 2h1l-2 1h-1l-1-1-1 1v1l2 2v1l-4-1c-2-1-4-3-6-5z" class="e"></path><path d="M416 313l-2-2v-1-2c2 0 2 1 4 2v1l2 2v1l-4-1z" class="AF"></path><defs><linearGradient id="f" x1="424.142" y1="406.054" x2="429.43" y2="432.721" xlink:href="#B"><stop offset="0" stop-color="#180202"></stop><stop offset="1" stop-color="#330a09"></stop></linearGradient></defs><path fill="url(#f)" d="M409 426l1 1c0-1 1-1 1-2l1-2h0-1l1-1h1l1-1c1-1 1-1 2-1v-1c2-1 3-3 4-5s1-3 2-5l1 1c1 0 2 0 3 1h0c1 1 1 0 2 1 1-1 2-1 4-2 0-1 1-2 0-3v-1c1-1 0-1 1-1v-1c1-1 1-1 2-1l2 2c2 1 4 3 5 5v1c1 2 1 3 2 5 1-1 1-3 2-4h1c0 2-3 5-4 8-1 1-1 2-3 2l2-3-1-1c-4 3-4 8-7 11-2 2-3 2-5 3l-3 3c-1-1-1 0-2-2l1-2-1-1-1 3h-1c-1 0-2 1-4 1 0 1-1 1-1 1l-2 1h0-3l1 1-2 1c-1 0-2-1-2-3h-1c0-2 0-2-1-3 0-2 0-3-1-5h2l1-1z"></path><path d="M421 413h1l1 2c-1 1-1 1-1 2v1-1c-1 1-1 0-2 1h0-1c1-2 2-3 2-5h0z" class="H"></path><path d="M424 430c1-2 2-3 4-5h1l1 1c-1 3-3 4-5 5l-1-1z" class="I"></path><path d="M408 427c1 0 2 1 3 0s1-2 2-3c0-1 1-2 2-3h1c0 1 0 1-1 2 0 2-1 2-1 4l-1 1v3l1 1c-1 1-1 2 0 3l1 1h-3l1 1-2 1c-1 0-2-1-2-3h-1c0-2 0-2-1-3 0-2 0-3-1-5h2z" class="b"></path><path d="M380 401v-1c1-1 2-2 4-3l2 2c1 0 2 0 3 1l-2 2s0 1 1 1l1 1c1 1 2 2 2 5h2c1-1 1-3 2-4h0c2 1 3-3 5-4 1 0 1 0 2-1v1c-1 1-1 1-1 2-1 2-2 3-3 4v6c0 1-1 2-1 4v1c1 0 1 0 2-1l2 3v2h2 1c1 1 1 2 1 4l1 1c1 2 1 3 1 5 1 1 1 1 1 3h1l-1 1v1h0l-1 1c0 2-1 5-1 7 0 1-1 2-1 3v1c1 1 1 2 1 3h1 1l1-1 2 3-1 1c0 1 1 2 1 4l2 3h-2v-1c-1 0-2 0-3-1 0-2-1-2-2-3v2l-6-11-4-7c-2-6-5-13-8-18l-9-22h1z" class="n"></path><path d="M380 401v-1c1-1 2-2 4-3l2 2c0 2 1 4-1 6 1 1 1 2 1 3-1-1 0-2-2-3h-1v-1c-1-1-1-2-2-2v-1h-1z" class="z"></path><path d="M388 423h1c1 1 2 3 3 6s3 8 6 11h2c1 2 1 2 1 5 0 1-1 1-1 2v1l-4-7c-2-6-5-13-8-18z" class="R"></path><path d="M400 440v-2-1c-1 0-2-1-2-2-1-1-1-2 0-3v-1-1c-1-1-1-2 0-3v-1c1 1 1 1 1 2 1 2 1 3 2 4s2 1 3 1h0l1 1c-1 1-1 1-1 3h1l-2 2c1 0 1 1 2 1l2-2c0 2-1 5-1 7 0 1-1 2-1 3v1c1 1 1 2 1 3h1 1l1-1 2 3-1 1c0 1 1 2 1 4l2 3h-2v-1c-1 0-2 0-3-1 0-2-1-2-2-3v2l-6-11v-1c0-1 1-1 1-2 0-3 0-3-1-5z" class="z"></path><path d="M408 452l1-1 2 3-1 1c0 1 1 2 1 4l-2-1h0l-1-4v-2h0z" class="j"></path><path d="M585 166c13-1 26 0 39 1l11 1 19 6h0c2-1 5 0 8 0l22 8c1 2 8 5 11 5h0c4 2 8 4 12 5 1 1 4 3 5 3 4 1 9 3 13 5l7 6c2 2 5 5 6 8 0 2 0 3-1 4v7h-1c-1 1-1 1-2 3v2 1l-1-1v-1c0-1 0-2-1-3 0 6-1 11-3 17h0-1l-14 38-18 51c-1 3-3 5-4 8-2 7-1 11-7 15l-5 5-3 3-2 3 2 2c-2 1-3 2-5 2-6 1-9 0-14-3h-1l1-2v-2l-1-1 6-11 1-3v-1c-1 0-1 0-1-1 1-2 0-1 0-3h-2l8-9v-1c0-1 0-2 1-3v-2-1c2-3 4-7 4-10v-2-1c3-3 3-11 4-15 0-4 1-8 2-11v-6c1-9 1-19-1-28v-2l-1-8c-1-1-1-3-2-4-6-17-15-33-30-46s-35-20-55-22c-17-1-35 1-53 2l29-7c3 2 14 0 18-1z" class="AP"></path><path d="M719 251v-1c1 0 1-1 1-1h1l1 1v2l-1 1v5l-1 1-1-3v-3-2zm-35-51c2 1 2 1 3 3h0l-2 2c0 1 1 2 1 2l-1 1s-1-1-2-1l-1-1 1-1 1 1v-1-3h0v-2z" class="Y"></path><path d="M717 226c1 1 1 1 2 3l1-2s1 1 1 2c1 1 1 3 2 5 0 2 0 4 1 5v3l1 4c-1 2-1 2-2 3h-1v1l-1-1h-1s0 1-1 1v1c0-1 0-3 1-4s1-2 2-3h0c1 1 0 2 0 4h1 0c1-2 0-5 0-7 0-3-1-5-3-8v-1c-1-1-1-2-1-2-1-2-2-3-2-4z" class="B"></path><path d="M677 205c-3-4-5-7-8-10-2-2-5-3-6-5 7 3 15 10 18 17 1 1 1 3 1 4-2-1-4-5-5-6z" class="C"></path><path d="M719 237h1c0 1 0 1 1 2 0 1 1 1 2 2 0 2 1 5 0 7h0-1c0-2 1-3 0-4h0c-1 1-1 2-2 3s-1 3-1 4v2l-1 2-2 1-1-1 1-1h-1-1v-1l1-2v-1-1l1-1v-5c1-1 1-1 2-1h1l2-2-1-1c0 1 0 1-1 2l-1-1c-1-1-1-1 0-2 1 0 0 0 1-1h0z" class="a"></path><defs><linearGradient id="g" x1="733.344" y1="223.147" x2="724.156" y2="220.353" xlink:href="#B"><stop offset="0" stop-color="#9a999a"></stop><stop offset="1" stop-color="#b2b3af"></stop></linearGradient></defs><path fill="url(#g)" d="M724 207c1 1 2 1 3 2 0 1 1 2 2 3 2 1 3 2 3 5v3c0 2 1 4 0 6 0 6-1 11-3 17h0-1v-1c3-12 2-24-4-35z"></path><path d="M691 292c0 3 0 3-1 6 0 1-1 2-1 3-1 1-1 1-1 2l-6 8v3l-3 5c-1 0-2 1-3 3-1 1-2 3-4 4 0 1 0 1-1 2l-1-1c2-3 4-7 4-10v-2-1c3-3 3-11 4-15 0 5-1 9-1 13 1 0 3-3 3-4l2-3c1-2 1-3 2-4h1v-1c3-2 4-5 6-8z" class="a"></path><path d="M691 292c0 3 0 3-1 6 0 1-1 2-1 3-1 1-1 1-1 2l-6 8-6 7c2-6 6-10 9-16v-1-1c3-2 4-5 6-8z" class="X"></path><path d="M712 279v1c0 1-1 4-2 5l-6 20c-2 5-3 10-5 14l-4 10c0 1-1 3-1 4l-3 6v1 1l1-1c0-1 0-2 1-3l2-5 3-9 2-6s1-1 1-2h0c0-2 0-1 1-2v-1l1-1c0-2 1-3 1-5h1v-1-1h0c0-1 0-1 1-2 0-1 0-2 1-3v-1l1-1v-1c0-1 1-2 1-3 1-1 1-2 1-3h0l1-2 2-5c0-1 1-1 1-2l-18 51c-1 3-3 5-4 8-2 7-1 11-7 15v-1c0-1 1-3 2-5s2-4 3-7c0-1 0-3 1-4 2-7 6-14 8-21l4-13 4-12c2-5 3-9 5-13z" class="Y"></path><defs><linearGradient id="h" x1="706.533" y1="205.129" x2="708.049" y2="231.81" xlink:href="#B"><stop offset="0" stop-color="#111010"></stop><stop offset="1" stop-color="#2c2c2c"></stop></linearGradient></defs><path fill="url(#h)" d="M702 203c2 2 4 4 4 6l2 2h0c0 1 1 1 1 2l3 6 2 3c1 2 2 3 3 4 0 1 1 2 2 4 0 0 0 1 1 2v1c2 3 3 5 3 8-1-1-2-1-2-2-1-1-1-1-1-2h-1c0-1-1-2-1-3h-1v1c-1-1-2-1-3-2h0l-1-2h-1c0-1 1-1 0-2 0-3-9-18-11-20-2-1-2-3-3-4 1 0 0 0 1 1l2-1h1v-2z"></path><defs><linearGradient id="i" x1="693.847" y1="197.637" x2="686.804" y2="215.216" xlink:href="#B"><stop offset="0" stop-color="#090909"></stop><stop offset="1" stop-color="#2b2a2a"></stop></linearGradient></defs><path fill="url(#i)" d="M684 200l2-3-1-2v-1l6 3c4 2 9 1 11 5v1h0v2h-1l-2 1c-1-1 0-1-1-1h-1c0 2 0 4 1 5 1 2 1 4 2 6v3l-1-1c-1-1-1-2-1-3-1 2-1 3-2 4h0c-1 0-2-2-2-2h0c-1-2-2-5-3-7s-3-5-4-7h0c-1-2-1-2-3-3z"></path><path d="M693 207l5 8c-1 2-1 3-2 4 0-3-1-4-1-6l-1-1c0-2-1-3-1-5z" class="J"></path><path d="M693 207v-1l-1-1 1-1c-1-2-2-3-2-4h1 0v-1h1c1 0 2 2 2 3h1c0 1 0 2 1 3 0 2 0 4 1 5 1 2 1 4 2 6v3l-1-1c-1-1-1-2-1-3-1-2-3-5-5-8z" class="S"></path><defs><linearGradient id="j" x1="692.592" y1="192.081" x2="694.907" y2="187.229" xlink:href="#B"><stop offset="0" stop-color="#a4a4a1"></stop><stop offset="1" stop-color="#c8c7c7"></stop></linearGradient></defs><path fill="url(#j)" d="M654 174c2-1 5 0 8 0l22 8c1 2 8 5 11 5h0c4 2 8 4 12 5 1 1 4 3 5 3 4 1 9 3 13 5l7 6c2 2 5 5 6 8 0 2 0 3-1 4v7h-1c-1 1-1 1-2 3v2 1l-1-1v-1c0-1 0-2-1-3 1-2 0-4 0-6v-3c0-3-1-4-3-5-1-1-2-2-2-3-1-1-2-1-3-2-11-10-26-15-39-21l-31-12z"></path><path d="M712 195c4 1 9 3 13 5l7 6c2 2 5 5 6 8 0 2 0 3-1 4v7h-1c-1 1-1 1-2 3v2 1l-1-1v-1c0-1 0-2-1-3 1-2 0-4 0-6l1-1c0-1 1 0 1-1 0-2 0-3-1-5h0c-1-2-3-4-4-5-2-7-11-9-17-13h0z" class="K"></path><path d="M736 225c0-1-1-2-1-3 0-2 1-5 0-8l-1-2h1c1 1 1 2 2 3v1l1-2c0 2 0 3-1 4v7h-1z" class="q"></path><path d="M709 254v1c1-1 2-3 2-4s0-2 1-2v-2l1-1h1 0c0 2-1 4-1 5v6 1c1 2 1 3 0 5 1 0 1 1 2 0l-1 1h-1v-3c0-1 0-1-1-2v-6l1-5c-4 8-3 16-2 25 0 2 0 4 1 6-2 4-3 8-5 13l-4 12-4 13c-2 7-6 14-8 21-1 1-1 3-1 4-1 3-2 5-3 7-1-1-1 0-1-1 1-2 1-3 1-4v-1l-1 1h-1v-1c2-3 3-7 5-10 1 0 1-1 2-1 1-1 1-3 1-4l6-19c-1-1-1-3 0-4v-1l-1-1h0c-1-1-1-2-1-4l-1-4h-1c3-8 5-15 6-23 1-1 1-2 2-3h0c2-1 2-4 2-6 1-3 1-7 3-10v7l1-6z" class="O"></path><path d="M704 291l1 1-1 2-5 15c-1-1-1-3 0-4v-1l-1-1c1-4 4-8 6-12z" class="B"></path><path d="M706 285c0 2-2 3-2 6-2 4-5 8-6 12h0c-1-1-1-2-1-4l-1-4h2l8-10z" class="d"></path><path d="M705 263c1-3 1-7 3-10v7c1 8 1 16-2 24v1l-8 10h-2-1c3-8 5-15 6-23 1-1 1-2 2-3h0c2-1 2-4 2-6z" class="B"></path><path d="M704 246v1c1 3 1 5 1 8v4 1 3c0 2 0 5-2 6h0c-1 1-1 2-2 3-1 8-3 15-6 23l-7 8c0-1 0-1 1-2 0-1 1-2 1-3 1-3 1-3 1-6-2 3-3 6-6 8 1-1 1-2 1-4 1-1 1-1 1-2l1-3-1-1c-1 1-1 2-2 2 0-2 1-6 2-8v-4c1-8 1-15 1-23v-1c1-1 1-2 1-3l1 2c0 1-1 3 1 4 1-2 1-3 2-5h1 1c-1 2-1 4-2 5s-1 1-1 2c-1 1-1 2-2 3v1c2 0 2-1 3-2s1-1 1-2h0l1 1c2-5 5-9 5-15l1-1 1 2 1 1c1 0 1-2 1-3z" class="f"></path><path d="M687 280h3c-1 1-1 1-1 2l1 1-2 2-1-1v-4z" class="M"></path><path d="M690 283c0-1 1-3 3-3 0 2 0 3-1 4 0 1-1 2-1 3l-1-1c-2 1-3 2-3 3v1c-1 1-1 2-2 2 0-2 1-6 2-8l1 1 2-2z" class="O"></path><path d="M700 247l1-1 1 2c0 2 0 4-1 5 0 2-1 4-2 6-2 3-4 7-5 11l-1-1c0-2 1-4 2-7 2-5 5-9 5-15z" class="C"></path><path d="M700 265c0 9-5 20-9 27-2 3-3 6-6 8 1-1 1-2 1-4 1-1 1-1 1-2l1-3-1-1v-1c0-1 1-2 3-3l1 1c-1 2-2 4-2 6h1c3-7 5-13 7-20 1-3 1-5 3-8z" class="a"></path><defs><linearGradient id="k" x1="689.465" y1="265.505" x2="704.681" y2="289.222" xlink:href="#B"><stop offset="0" stop-color="#554f51"></stop><stop offset="1" stop-color="#828681"></stop></linearGradient></defs><path fill="url(#k)" d="M700 265l1-4c1-1 1-1 1-2v-1c2-1 2-2 3-3h0v4 1 3c0 2 0 5-2 6h0c-1 1-1 2-2 3-1 8-3 15-6 23l-7 8c0-1 0-1 1-2 0-1 1-2 1-3 1-3 1-3 1-6 4-7 9-18 9-27z"></path><path d="M701 272c0-3 1-6 2-8 0-2 1-3 2-4v3c0 2 0 5-2 6h0c-1 1-1 2-2 3z" class="C"></path><defs><linearGradient id="l" x1="701.119" y1="203.978" x2="708.241" y2="236.168" xlink:href="#B"><stop offset="0" stop-color="#2b2829"></stop><stop offset="1" stop-color="#464543"></stop></linearGradient></defs><path fill="url(#l)" d="M697 205h1c1 1 1 3 3 4 2 2 11 17 11 20 1 1 0 1 0 2h1l1 2h0c1 1 2 1 3 2l1 1-2 2h-1c0 2 0 2-1 3v1c-1 0-1 0-2 1s-1 1-1 3c-1 3-1 5-2 8l-1 6v-7c-2 3-2 7-3 10v-3-1-4c0-3 0-5-1-8v-1c0 1 0 3-1 3l-1-1-1-2-1 1v-3c0-1-1-2-2-2v-2-2h0c-1 2-2 6-4 7h0c1-5 2-10 1-16 0-4-2-7-1-12h0s1 2 2 2h0c1-1 1-2 2-4 0 1 0 2 1 3l1 1v-3c-1-2-1-4-2-6-1-1-1-3-1-5z"></path><path d="M700 216l1-1h1v1l2 4v1c1 1 1 2 1 3h0c-1-1-2-2-4-3l-1-2v-3z" class="J"></path><path d="M706 246h1v-3-1c-1-1-1-2-1-2v-1-3c0-2 1-3 1-4s-1-3-1-4l1-1c0 1 0 2 1 3v2c1 1 0 3 1 5h0l1 2-2 1v7c-1 1-1 1 0 3 0 3-2 7-3 9v-4c0-3 0-5-1-8l2-1z" class="O"></path><path d="M709 237l3-3h-2l1-2c2 0 1 1 2 2 1 2 0 4 0 6l1 1v1c-1 0-1 0-2 1s-1 1-1 3c-1 3-1 5-2 8l-1 6v-7c-2 3-2 7-3 10v-3-1c1-2 3-6 3-9-1-2-1-2 0-3v-7l2-1-1-2z" class="a"></path><path d="M698 215c0 1 0 2 1 3l1 1 1 2 1 3c2 8 3 14 4 22l-2 1v-1c0 1 0 3-1 3l-1-1-1-2-1 1v-3c0-1-1-2-2-2v-2-2h0c-1 2-2 6-4 7h0c1-5 2-10 1-16 0-4-2-7-1-12h0s1 2 2 2h0c1-1 1-2 2-4z" class="M"></path><path d="M694 217s1 2 2 2h0 0c0 1 1 2 1 3 1 2 2 5 1 7l1 1v3l1 1 1 6c-2-2-1-3-2-5-1-3-1-6-2-8 0-2-1-3-2-4 0-2 0-4-1-6z" class="J"></path><path d="M698 215c0 1 0 2 1 3l1 1 1 2 1 3c-1 1-2 1-2 2l1 8h-1l-1-1v-3l-1-1c1-2 0-5-1-7 0-1-1-2-1-3h0c1-1 1-2 2-4z" class="O"></path><path d="M699 218l1 1 1 2 1 3c-1 1-2 1-2 2-1-1-1-3-1-4v-4z" class="a"></path><path d="M700 226c0-1 1-1 2-2 2 8 3 14 4 22l-2 1v-1c0 1 0 3-1 3l-1-1-1-2v-6l-1-6h1l-1-8z" class="B"></path><path d="M701 234l3 12c0 1 0 3-1 3l-1-1-1-2v-6l-1-6h1z" class="q"></path><path d="M688 257c-1-10-2-19-5-29-4-13-14-27-24-37 3 2 6 5 8 7h1l-4-5c5 3 9 9 12 13l1-1c1 1 3 5 5 6l1-1v-3c1 0 2 1 2 1l1-1s-1-1-1-2l2-2c1 2 3 5 4 7s2 5 3 7c-1 5 1 8 1 12 1 6 0 11-1 16h0c2-1 3-5 4-7h0v2 2c1 0 2 1 2 2v3c0 6-3 10-5 15l-1-1h0c0 1 0 1-1 2s-1 2-3 2v-1c1-1 1-2 2-3 0-1 0-1 1-2s1-3 2-5h-1-1c-1 2-1 3-2 5-2-1-1-3-1-4l-1-2c0 1 0 2-1 3v1z" class="i"></path><path d="M677 205c1 1 3 5 5 6l1-1h1c1 0 1 0 2-1v4c0 1 1 2 1 3l-1 2v3c0 1 1 3 0 4-2-6-6-13-10-19l1-1z" class="B"></path><path d="M684 210c1 0 1 0 2-1v4c0 1 1 2 1 3l-1 2v3l-2-11z" class="O"></path><defs><linearGradient id="m" x1="683.872" y1="224.464" x2="698.18" y2="229.382" xlink:href="#B"><stop offset="0" stop-color="#4a4845"></stop><stop offset="1" stop-color="#626160"></stop></linearGradient></defs><path fill="url(#m)" d="M687 203c1 2 3 5 4 7s2 5 3 7c-1 5 1 8 1 12 1 6 0 11-1 16h0 0l-1-1v-3h-1c-1 1-1 1-1 0h-1v1c-1-2-1-4-2-6l-2-11c1-1 0-3 0-4v-3l1-2c0-1-1-2-1-3v-4c-1 1-1 1-2 1h-1v-3c1 0 2 1 2 1l1-1s-1-1-1-2l2-2z"></path><path d="M687 203c1 2 3 5 4 7h-3c1 7 4 13 4 20-1-1-1-3-1-4-1-4-2-7-4-10 0-1-1-2-1-3v-4c-1 1-1 1-2 1h-1v-3c1 0 2 1 2 1l1-1s-1-1-1-2l2-2z" class="C"></path><path d="M687 216c2 3 3 6 4 10 0 1 0 3 1 4s1 4 1 6v1c-1-1-1-2-1-3l-1-1c-1 2 0 3-1 5v3 1c-1-2-1-4-2-6l-2-11c1-1 0-3 0-4v-3l1-2zm8 79h1l1 4c0 2 0 3 1 4h0l1 1v1c-1 1-1 3 0 4l-6 19c0 1 0 3-1 4-1 0-1 1-2 1-2 3-3 7-5 10v1h1l1-1v1c0 1 0 2-1 4 0 1 0 0 1 1-1 2-2 4-2 5v1l-5 5-3 3-2 3 2 2c-2 1-3 2-5 2-6 1-9 0-14-3h-1l1-2v-2l-1-1 6-11 1-3v-1c-1 0-1 0-1-1 1-2 0-1 0-3h-2l8-9v-1c0-1 0-2 1-3v-2-1l1 1c1-1 1-1 1-2 2-1 3-3 4-4 1-2 2-3 3-3l3-5v-3l6-8 7-8z" class="K"></path><path d="M685 344h1l1-1v1c0 1 0 2-1 4 0 1 0 0 1 1-1 2-2 4-2 5v1l-5 5c0-2 1-5 1-6 1-4 3-7 4-10zm-7-9l4-5 1 1-2 3v1l-3 3c-1 1-2 2-2 4h0l-1 1c-2 3-4 7-6 10-1 3-2 5-3 7h-1 0v-1l-1-1v-1l6-11 8-11z" class="AU"></path><defs><linearGradient id="n" x1="691.119" y1="324.276" x2="680.951" y2="337.173" xlink:href="#B"><stop offset="0" stop-color="#302d2d"></stop><stop offset="1" stop-color="#4d4543"></stop></linearGradient></defs><path fill="url(#n)" d="M683 331l3-3c1-2 2-4 3-5 0-1 1-2 2-2l1 7h1c0 1 0 3-1 4-1 0-1 1-2 1-2 3-3 7-5 10-1 0-1-1-2-1h-2c-1 0-2 1-3 1h0c1-2 3-4 4-6v-1l-1-1v-1l2-3z"></path><path d="M682 337c0 1 1 1 1 2 1-1 3-2 3-4v-1c0-1 1-2 2-2 1-1 1-1 2-1v2c-2 3-3 7-5 10-1 0-1-1-2-1h-2c-1 0-2 1-3 1h0c1-2 3-4 4-6z" class="AV"></path><path d="M663 366c2 1 2 1 4 1v-1c0-1 1-2 1-3v-1c0-1 0 0 1-1s0-1 1-2c1 0 1 0 2-1v-1c1-2 4-5 5-7 1-1 2-2 2-3s1-1 2-2v-1c1-1 1 0 2 0 0 2-2 5-3 7l-1 1 1 1c-1 1-1 1-1 2v1l2-2c0 1-1 4-1 6l-3 3-2 3 2 2c-2 1-3 2-5 2-6 1-9 0-14-3h-1l1-2v-2l-1-1 6-11h1v-1h2l2-2h0c0-1 1-1 1-2h1l-6 11v1l1 1v1h0c0 2-1 4-1 6h-1z" class="AA"></path><path d="M679 356l2-2c0 1-1 4-1 6l-3 3v-1l2-6z" class="d"></path><path d="M664 358l1 1v1h0c0 2-1 4-1 6h-1-3l-1-1c2-2 4-5 5-7z" class="C"></path><path d="M679 319c1-2 3-3 4-4l1 1c-1 2-1 2-1 4l-3 4c1 1 0 1 1 1l2-1h0v1h2v1c-1 1-2 3-3 4l-4 5-8 11h-1c0 1-1 1-1 2h0l-2 2h-2v1h-1l1-3v-1c-1 0-1 0-1-1 1-2 0-1 0-3h-2l8-9v-1c0-1 0-2 1-3v-2-1l1 1c1-1 1-1 1-2 2-1 3-3 4-4 1-2 2-3 3-3z" class="AG"></path><path d="M669 334c-1 4-3 9-5 14v-1c-1 0-1 0-1-1 1-2 0-1 0-3h-2l8-9z" class="t"></path><path d="M681 325l2-1h0v1h2v1c-1 1-2 3-3 4l-4 5v-3h-1-1c0-1 0-1 1-2 2-1 3-3 4-5z" class="AA"></path><path d="M679 319c1-2 3-3 4-4l1 1c-1 2-1 2-1 4l-3 4c1 1 0 1 1 1-1 2-2 4-4 5h-1l-1 1v-1c1-1 1-1 1-2v-1-1c1 0 1 0 1-1l1-2-2-1c1-2 2-3 3-3z" class="d"></path><path d="M695 295h1l1 4c0 2 0 3 1 4h0l1 1v1c-1 1-1 3 0 4l-6 19h-1l-1-7c-1 0-2 1-2 2-1 1-2 3-3 5l-3 3-1-1c1-1 2-3 3-4v-1h-2v-1h0l-2 1c-1 0 0 0-1-1l3-4c0-2 0-2 1-4l-1-1c-1 1-3 2-4 4l3-5v-3l6-8 7-8z" class="M"></path><path d="M697 299c0 2 0 3 1 4h0c0 3-1 6-3 7h-1s-1-1-1-2h1c1-2 2-6 1-8l2-1z" class="Y"></path><defs><linearGradient id="o" x1="696.781" y1="301.613" x2="682.071" y2="305.299" xlink:href="#B"><stop offset="0" stop-color="#151617"></stop><stop offset="1" stop-color="#302d2b"></stop></linearGradient></defs><path fill="url(#o)" d="M695 295h1l1 4-2 1c-4 4-7 11-13 14v-3l6-8 7-8z"></path><path d="M683 320c1-2 3-4 5-6 1-1 3-2 3-3 1-1 1-2 2-3 0 1 1 2 1 2-2 6-4 11-9 16v-1h-2v-1h0l-2 1c-1 0 0 0-1-1l3-4z" class="O"></path><path d="M698 303l1 1v1c-1 1-1 3 0 4l-6 19h-1l-1-7c-1 0-2 1-2 2-1 1-2 3-3 5l-3 3-1-1c1-1 2-3 3-4 5-5 7-10 9-16h1c2-1 3-4 3-7z" class="E"></path><path d="M817 237c0 1-1 1 0 2 2-1 1-4 3-6v2l1 1-1 5v2c-1 2-1 3-1 5l1 2c0 3-1 4 0 7 0 1 0 2 1 3-1 1-1 3-2 4v18l1 9v5c-1 9 0 18 0 27 1 6 0 13 0 19 0 1 1 3 1 4 1 2 11 27 11 28-6-8-9-19-12-29l-2 38c-1 3 0 7 0 10 0 1 0 1-1 2v-7c0-2 0-3-1-5h0v9 5l-2 3h-1c-2 0-3 1-5 1-1 0-2 0-3-1l3-6c1-1 1-1 1-2l-2 1-4 4c-2 2-3 4-5 6l-6 8c-1 1-2 2-3 4h-2v1l2 2h-1s0 1-1 1v1l-1 1v2c-1 0-2 1-3 2-1 0-3 2-4 3l-2 2-1-1h-1l1-1v-1l-1 1-2-1c-2 1-3 3-6 4 1-2 0-4 0-6l-2 1v6c0 1 1 2 0 3 0 1-1 1-1 2v-1c-1 1-1 0-2 1v1c0 1 0 2-1 3s-2 1-3 2v-3s2-1 2-2l-1-2 2-1v-1c0-2-1-2 0-3v-1h-1s-1 0-1 1l-1-1c1-1 1-2 2-3h1l-1-2c-2 1-3 1-4 2l-1-1v-2c-1 1-1 1-2 1h-2c0-2 0-2-1-2h-1l-4 4h-2c-1-1-1-3 0-4-2-2-2-3-3-6v-6-3c0-2-1-5-1-7v-8-1c1-1 2-1 3-1 1-1 1 0 3 0l1-3h-2v1l-1 1c-1-2-1-3-1-5v-7-11l-2 1 2-7c-1 1-3 2-4 3h0l3-6h1c0-2 0-3 1-4 1-2 2-3 3-5l3-6c0-3 1-5 3-8h1l2-1c1 1 2 1 3 1s1-1 2-1c0-1 1-3 2-4l-1-1 1-1-1-1h1c1 0 1-1 2-2h1c1-1 1-2 2-2h1l2-2 1-1c1-4 1-8 4-11 1-1 2-2 2-3v-3c-1-1-1-3-2-4 0-3 0-5 1-8v-1h0v-2c1-2 1-2 1-3h-1c1-3 3-5 5-7v-2h0l3-3c2 0 4-1 5-2l2-1h1v-1c2-2 4-4 7-6v-1l1-2 1-1c3-2 4-3 6-5 0-1 0-2 1-3h0c0-1 1-1 1-2l1-1c1-1 1-2 2-3v-1h0v-1c2-2 2-6 2-9l1 1h0l2 2z" class="y"></path><path d="M815 314v5c0 3 0 6 1 9v5l-1-1c-1-1-1-2-2-4v-12h1l1-2zm-2-66c1 1 2 1 3 3h0v6h1c0 2 0 3-1 5-1-1 0-2-1-3 0-1 1-3 0-5l-1 1 1 2c0 1 0 1-1 2v3l1 1h-2v-6-9z" class="x"></path><path d="M810 303c1 0 2 1 2 1-1 5-6 10-9 13v-1-1l3-3c0-1-1-2-1-2v-1c0-1 0-2 1-3 0-1-1-1 1-2l1 1 2-2z" class="AE"></path><path d="M813 248v9 6l-4-1-1-1c-1 0-2 0-3-1-1 0-2 0-4-1 3-2 4-3 6-5l6-6z" class="r"></path><path d="M813 257v6l-4-1-1-1c1-1 1-2 3-3h1l1-1z" class="F"></path><path d="M805 309v1s1 1 1 2l-3 3v1 1l-9 12c-1 2-2 3-4 5h0l-1 1-1-1v-1l2-1h0l1-1-1-1c1-1 1-1 1-2s1-1 1-2c1-3 3-5 4-8l2-2 3-3c0-1 1-1 1-2l3-2z" class="r"></path><path d="M799 324l14-17c-1 2 0 9-2 12v1h0c1 2 0 5 0 7-1-1-1-2-2-3l1-1c-1-1-1-2-2-2v2l-1-1c-1 0-1 0-2 1h0l-3 3c-1-1-1-1-3-2z" class="F"></path><path d="M817 237c0 1-1 1 0 2 2-1 1-4 3-6v2l1 1-1 5v2c-1 2-1 3-1 5v2l-2-1-1 1c0 1 0 1 1 2v5h-1v-6h0c-1-2-2-2-3-3l-6 6c0-1 0-2 1-3h0c0-1 1-1 1-2l1-1c1-1 1-2 2-3v-1h0v-1c2-2 2-6 2-9l1 1h0l2 2z" class="I"></path><path d="M768 337h3v-1l-2-1c1 0 1 0 2-1h-1c1-1 1-2 1-2h-2v-1c1 0 2-1 2-1 0-1 0-2-1-2v-1h1c0-1 0-1 1-2h0v-1c1 1 1 2 2 2v-2h1v3 1 1l-1-1h-1l1 1h1v2 1h0-1-1c1 2 2 2 2 4l-2-1h-1l1 2c-1 0-1 1-2 1l1 1 1-1s1-1 2-1v1l-2 2v1h1s1 1 1 2v4h0l-1-1-1 1 1 1h0l-1 1h1l1-1h0v2 3h-1l-1 1-1-1h-1c0 1 0 2 1 3v1l-1 1s-1 1-1 2h1l3-3 1-1h0c0 2 1 4 0 7h-1-1 1l2 2h-1c0 2 1 3 1 5v4c-1 1-2 1-3 2h1v1h-1l-1-1c-1 0-2 1-3 1h0c-1-1-1-1-1-3l1 1 1-2-1-1-2 1v-12-4c1 0 1 0 2-1v-1h-2c0-1 0-1 1-2l1 1 1-1-1-1h-1c1-1 1-2 1-3-1 0-1 0-2 1-1 0-2 1-3 2v3c-1-2 0-3-1-3-1 2-1 3-1 5h-1c0-1-1-1-2-2 1 0 1 0 2-1l-1-1c-1 0 0 0-1-1h1v-4h2c1-1 1-2 1-3 1 0 2-1 3-1h0l1-2 1-1v-4z" class="D"></path><path d="M763 345c1 0 2-1 3-1h0l1-2 1-1v-4c1 2 2 2 2 3 1 0 0 1 0 2l-1 1v1 3h-1v-1h0c-1 1-1 2-1 4-1 0-2 1-3 2v3c-1-2 0-3-1-3-1 2-1 3-1 5h-1c0-1-1-1-2-2 1 0 1 0 2-1l-1-1c-1 0 0 0-1-1h1v-4h2c1-1 1-2 1-3z" class="T"></path><path d="M779 323h-1v-1l1-1v-2h0c1-1 1-2 2-2h1c0-1 0-2 1-2v-2l1-1c1-2 2-2 3-3l-1-1-2 2h-2v-2-1c0-2 0-2 1-3l2 1h2c0-2 0-2 1-2l3 2v1c0 1 1 1 2 2l2-1h1c1 1 1 1 2 1s3 2 4 3c0 1-1 1-1 2l-3 3-2 2c-1 3-3 5-4 8 0 1-1 1-1 2s0 1-1 2l1 1-1 1h0l-2 1v1l1 1v1c-2 2-5 6-7 8h0c0-1-1-2-1-3v-1c0-2 0-5 1-7h1c-1-1-1-2-2-3l-1 1c-1 2-1 4-1 6v1c-1-1-1-2-1-3 0 0 1-1 0-2h0c1-1 1-1 1-2-1-1-1-2-1-3s1-1 1-2h1v-2-1h0-1z" class="AK"></path><path d="M793 308l2-1h1c1 1 1 1 2 1s3 2 4 3c0 1-1 1-1 2l-1-2h-4 0-1-1l1 2c-1 1-1 1-3 1h-1v-1l2-2c1 0 0 0 1-1l-1-1v1l-1-1h1v-1z" class="t"></path><path d="M779 323h-1v-1l1-1v-2h0c1-1 1-2 2-2h1c0-1 0-2 1-2v-2l1-1c1-2 2-2 3-3l-1-1-2 2h-2v-2-1c0-2 0-2 1-3l2 1h2c0-2 0-2 1-2l3 2v1h0c-1 2-2 2-2 4h-1c-1 3-3 4-4 6l1 1 1 1c-2 1-3 0-3 2 1 1 1-1 2 1l-1 1h-1c0 1-1 2 0 4h-1v2c1 0 1-1 2-1 0 2-1 2-3 3l-1 1c-1 2-1 4-1 6v1c-1-1-1-2-1-3 0 0 1-1 0-2h0c1-1 1-1 1-2-1-1-1-2-1-3s1-1 1-2h1v-2-1h0-1z" class="x"></path><path d="M778 287c2 1 2 2 4 3 0 1 0 3 1 4h1v1h-1v2c1 2 2 2 4 3-1 1-1 1-3 1 1 1 1 1 2 1h1l1 1c-1 0-1 0-1 2h-2l-2-1c-1 1-1 1-1 3v1 2h2l2-2 1 1c-1 1-2 1-3 3l-1 1v2c-1 0-1 1-1 2h-1c-1 0-1 1-2 2h0v2l-1 1v1h1l-1 2h0l-2 2v1 4h0-1v-1-2h-1l-1-1h1l1 1v-1-1-3h-1v2c-1 0-1-1-2-2v1h0c-1 1-1 1-1 2h-1v1c1 0 1 1 1 2 0 0-1 1-2 1v1h2s0 1-1 2h1c-1 1-1 1-2 1l2 1v1h-3v4l-1 1-1 2h0c-1 0-2 1-3 1 1-4-2-2-3-5 0-1 1-1 0-2s0-3 0-4 1-3 2-4l-1-1 1-1-1-1h1c1 0 1-1 2-2h1c1-1 1-2 2-2h1l2-2 1-1c1-4 1-8 4-11 1-1 2-2 2-3v-3c-1-1-1-3-2-4 0-3 0-5 1-8 1-1 2-2 2-4z" class="o"></path><path d="M764 325h1l1 1-4 4-1-1 1-1-1-1h1c1 0 1-1 2-2z" class="H"></path><path d="M767 323h1l2-2 1-1c-1 2-1 4-1 6h-4l-1-1c1-1 1-2 2-2z" class="Q"></path><path d="M800 260l1-1c2 1 3 1 4 1 1 1 2 1 3 1l1 1-1 2 5 1v31c0 2 0 6-1 8 0 0-1-1-2-1l-2 2-1-1c-2 1-1 1-1 2-1 1-1 2-1 3l-3 2c-1-1-3-3-4-3s-1 0-2-1h-1l-2 1c-1-1-2-1-2-2v-1l-3-2-1-1h-1c-1 0-1 0-2-1 2 0 2 0 3-1-2-1-3-1-4-3v-2h1v-1h-1c-1-1-1-3-1-4-2-1-2-2-4-3 0 2-1 3-2 4v-1h0v-2c1-2 1-2 1-3h-1c1-3 3-5 5-7v-2h0l3-3c2 0 4-1 5-2l2-1h1v-1c2-2 4-4 7-6v-1l1-2z" class="G"></path><path d="M810 303h1c1-2-1-3-1-5-1 0-1 0-2-1l1-1h2 2c0 2 0 6-1 8 0 0-1-1-2-1z" class="F"></path><path d="M800 260l1-1c2 1 3 1 4 1 1 1 2 1 3 1l1 1-1 2c-3-1-6-2-9-1v-1l1-2z" class="T"></path><path d="M787 278l4-3v4h0c-1 1-1 1-1 2s1 2 3 3c0 0 1 1 1 2-1 0-1 1-3 1 0 1 0 1 1 2h0c-2 0-3-1-4-2h0v-1c-1 0-3 0-4 1v1h1c-1 1-1 1-2 1h-1v1c-2-1-2-2-4-3 0 2-1 3-2 4v-1h0v-2c1-2 1-2 1-3h-1c1-3 3-5 5-7l2-1v1c1 1 1 2 2 3l2-3z" class="g"></path><path d="M781 278l2-1v1c1 1 1 2 2 3l-1 1c-1 3-1 5-2 7v1c-2-1-2-2-4-3 0 2-1 3-2 4v-1h0v-2c1-2 1-2 1-3h-1c1-3 3-5 5-7z" class="k"></path><path d="M778 287l1-2c1-2 4-2 5-3-1 3-1 5-2 7v1c-2-1-2-2-4-3z" class="m"></path><path d="M789 271l2-1h1l1 1h1 3s1-1 2-1v1l1 3 4-2 1 1 1-1c1 0 2 1 3 2-1 1-1 1-1 3 1 0 2-1 3 0h1c-2 1-2 0-3 2h-1-1c-1 1-2 0-3 1l-1 1h0-2v-1c-3 0-4 3-7 1 0 0-1 0-1-1v-1h1l-1-1v-1c1-1 1-2 1-3l-3 1-4 3-2 3c-1-1-1-2-2-3v-1l-2 1v-2h0l3-3c2 0 4-1 5-2z" class="D"></path><path d="M789 271l2-1h1l1 1c1 0 1 1 2 1s1 1 2 1c-1 1-2 1-3 1l-3 1-4 3c0-2 1-4 2-5l1-1-1-1z" class="W"></path><path d="M789 271l1 1-1 1c-1 1-2 3-2 5l-2 3c-1-1-1-2-2-3v-1l-2 1v-2h0l3-3c2 0 4-1 5-2z" class="Q"></path><path d="M782 289h1c1 0 1 0 2-1h-1v-1c1-1 3-1 4-1v1h0c1 1 2 2 4 2h0c2 0 3-1 5 1v1h3l1-1v1l-1 1c-1 1-1 0-2 1h-2c-1 1-3 2-4 4 1 1 1 1 2 1v1c0 1 1 2 1 3 1 0 2 0 2-1 1-1 1-2 2-3h1c-1 2 0 3-2 4l3 3c1 0 3 0 4-1l1 2c-1 1-1 2-1 3l-3 2c-1-1-3-3-4-3s-1 0-2-1h-1l-2 1c-1-1-2-1-2-2v-1l-3-2-1-1h-1c-1 0-1 0-2-1 2 0 2 0 3-1-2-1-3-1-4-3v-2h1v-1h-1c-1-1-1-3-1-4v-1z" class="v"></path><path d="M787 300h0c2 0 3-1 4-1 1 1 1 2 1 3s-1 2-1 3l-3-2-1-1h-1c-1 0-1 0-2-1 2 0 2 0 3-1z" class="l"></path><path d="M782 289h1c1 0 1 0 2-1h-1v-1c1-1 3-1 4-1v1h0c1 1 2 2 4 2h0c2 0 3-1 5 1v1h3l1-1v1l-1 1c-1 1-1 0-2 1h-2 0c-2 0-2 0-4-1 0-1-1-1-2-1v3l-1 1 1 1c-1 2-2 2-4 2 0-2 0-1-1-2l-2 1v-2h1v-1h-1c-1-1-1-3-1-4v-1z" class="D"></path><path d="M799 324c2 1 2 1 3 2l3-3h0c1-1 1-1 2-1l1 1v-2c1 0 1 1 2 2l-1 1c1 1 1 2 2 3s0 2 1 3c0 2 1 3 2 4 2 7 2 15 2 23v26 9 5l-2 3h-1c-2 0-3 1-5 1-1 0-2 0-3-1l3-6c1-1 1-1 1-2l-2 1-4 4c-2 2-3 4-5 6l-6 8c-1 1-2 2-3 4h-2v1c-1 1-2 2-4 3h-1c-2-2-1-3-1-6-1-1-1-1-1-2 1-2 1-4 0-6l1-1v-2-31l1-7-1-11v-4c1-2 2-3 3-4 1-2 2-4 3-5l2-2c1-1 2-2 3-2l1-1v-1l6-10z" class="AO"></path><path d="M816 392v5l-2 3h-1c-2 0-3 1-5 1-1 0-2 0-3-1l3-6c1 1 1 1 1 2l3-1c1 0 0-1 1 0h1c1-1 1-2 2-3z" class="D"></path><path d="M782 364l-1-11v-4c1-2 2-3 3-4 1-2 2-4 3-5l2 1v1l1 1h0c-1 1-1 1-1 2l-1 1-1-1h-2v1l1 1h-1l-1 1c-1 3-1 11 0 14h0c-1 1-1 2-2 2z" class="F"></path><path d="M799 324c2 1 2 1 3 2l3-3h0c1-1 1-1 2-1l1 1v-2c1 0 1 1 2 2l-1 1c1 1 1 2 2 3s0 2 1 3c0 2 1 3 2 4h-3l-2-3-1 1c0 1-1 1-1 2l-3-1h1l-1-1-1 1c-1 0-1 0-2-1h1v-1h1 3 0-1c-1-1-1-2-2-2-2 0-2 1-3 2v1 2h1 2v2l2-1h1l1 2h-2c-1 1-1 2-1 3s1 1 1 2l-2 2h-4-1c0-1 0-1 1-2l-2-1-2-2c-1 0-1-1-2 0-1 0-1 1-2 2v2l-2 2c0-1 0-1 1-2h0l-1-1v-1l-2-1 2-2c1-1 2-2 3-2l1-1v-1l6-10z" class="r"></path><path d="M781 371c1 1 3 1 3 3l-1 1c0 1 1 2 1 3-1 1-1 1-1 2h1 1l1-1v1l-1 3h1c0-1 0-1 1 0s1 1 0 2l1 1v-1h2c-1-2-2-2-1-3v-1l2 2 1 1h-1v1l3 3v-1h1 0 2 1l-1-2 2-1c1 0 1 1 3 2l-3 3h-2v1l-2 2c1 1 2 1 3 2v1 1l1-1h2c1-1-1 0 1-1 1 0 2 0 2-1 1-1 1 0 3 0l-4 4c-2 2-3 4-5 6l-6 8c-1 1-2 2-3 4h-2v1c-1 1-2 2-4 3h-1c-2-2-1-3-1-6-1-1-1-1-1-2 1-2 1-4 0-6l1-1v-2-31z" class="N"></path><path d="M798 394v1 1l1-1h2c1-1-1 0 1-1 1 0 2 0 2-1 1-1 1 0 3 0l-4 4-2-1c-1 0-1 0-2 1l-1 1-1 1c-2-1-3-1-4-2 0-1 2 0 3 0 1-1 1-2 2-3z" class="G"></path><path d="M781 371c1 1 3 1 3 3l-1 1c0 1 1 2 1 3-1 1-1 1-1 2h1 1v1c-1 1-1 2-2 3v2l3-3 1 1h-1l1 1v2h1c1-1 1-1 2 0h1l2 2-1 1h-1v-1c-1 0-1 0-1-1h-3l-2-2h-1c0 1-1 2-1 3l1 1h0c-1 2-1 2-1 4l2-2v1c0 1 0 1-1 2-1 2-3 5-3 7v-31z" class="AK"></path><path d="M781 404c0-1 0-1 1-1s1 0 1 1h1l1 2h0l1-1h0c1-1 1-1 1-2h1c1-1 2-1 3-1s1 0 2-1v-1l1-1v1 1h0l1 1-1 1h1 3l-6 8c-1 1-2 2-3 4h-2v1c-1 1-2 2-4 3h-1c-2-2-1-3-1-6-1-1-1-1-1-2 1-2 1-4 0-6l1-1z" class="D"></path><path d="M755 334c1 1 2 1 3 1s1-1 2-1c0 1-1 3 0 4s0 1 0 2c1 3 4 1 3 5 0 1 0 2-1 3h-2v4h-1c1 1 0 1 1 1l1 1c-1 1-1 1-2 1 1 1 2 1 2 2h1c0-2 0-3 1-5 1 0 0 1 1 3v-3c1-1 2-2 3-2 1-1 1-1 2-1 0 1 0 2-1 3h1l1 1-1 1-1-1c-1 1-1 1-1 2h2v1c-1 1-1 1-2 1v4 12l2-1 1 1-1 2-1-1c0 2 0 2 1 3h0c1 0 2-1 3-1l1 1h1v-1h-1c1-1 2-1 3-2v7l1 15c0 3 1 7 1 10l1 1h1v-2c1 2 1 4 0 6 0 1 0 1 1 2 0 3-1 4 1 6h1c2-1 3-2 4-3l2 2h-1s0 1-1 1v1l-1 1v2c-1 0-2 1-3 2-1 0-3 2-4 3l-2 2-1-1h-1l1-1v-1l-1 1-2-1c-2 1-3 3-6 4 1-2 0-4 0-6l-2 1v6c0 1 1 2 0 3 0 1-1 1-1 2v-1c-1 1-1 0-2 1v1c0 1 0 2-1 3s-2 1-3 2v-3s2-1 2-2l-1-2 2-1v-1c0-2-1-2 0-3v-1h-1s-1 0-1 1l-1-1c1-1 1-2 2-3h1l-1-2c-2 1-3 1-4 2l-1-1v-2c-1 1-1 1-2 1h-2c0-2 0-2-1-2h-1l-4 4h-2c-1-1-1-3 0-4-2-2-2-3-3-6v-6-3c0-2-1-5-1-7v-8-1c1-1 2-1 3-1 1-1 1 0 3 0l1-3h-2v1l-1 1c-1-2-1-3-1-5v-7-11l-2 1 2-7c-1 1-3 2-4 3h0l3-6h1c0-2 0-3 1-4 1-2 2-3 3-5l3-6c0-3 1-5 3-8h1l2-1z" class="x"></path><path d="M765 371c0-4-1-9 0-13 0-2-1-4 0-5h1l1-2 1 1h1l1 1-1 1-1-1c-1 1-1 1-1 2h2v1c-1 1-1 1-2 1v4c-1 2-1 4-1 6-1 1-1 3-1 4z" class="l"></path><path d="M761 412l1 4h2v-4h0v7 13h1c0 1 1 2 0 3 0 1-1 1-1 2v-1c-1 1-1 0-2 1v1c0 1 0 2-1 3s-2 1-3 2v-3s2-1 2-2l-1-2 2-1v-1c0-2-1-2 0-3v-1h-1s-1 0-1 1l-1-1c1-1 1-2 2-3h1l-1-2c-2 1-3 1-4 2l-1-1v-2c1 1 1 0 2 1h1l3-3c1-2 0-7 0-10z" class="p"></path><path d="M765 371c0-1 0-3 1-4 0-2 0-4 1-6v12 28 6c0 2 0 4 1 6v8h1v1h-1l-1 3-2 1v-11-5-9-30z" class="y"></path><path d="M767 373l2-1 1 1-1 2-1-1c0 2 0 2 1 3h0c1 0 2-1 3-1l1 1h1v-1h-1c1-1 2-1 3-2v7l1 15c0 3 1 7 1 10v2c-1 0-2-1-3-2 1-1 1 0 0-1v-1c-2 2-3 3-4 5h-1c0-1 0-1 1-2h-2c-1 1-1 0-2 0v-6-28z" class="AE"></path><path d="M776 381l1 15c0 3 1 7 1 10v2c-1 0-2-1-3-2 1-1 1 0 0-1v-1c-2 2-3 3-4 5h-1c0-1 0-1 1-2h-2c-1 1-1 0-2 0v-6l3 1c-1-1-1-2-2-2v-1h1v-2l1-1c0-1 1-1 2-2 1 0 1 0 2-1-1-1 0-1-1-1l-1-1c0-1 0-1 1-2h1l1-1h-1l-1-3v-1l1 1h2v-4z" class="v"></path><path d="M767 401l3 1c1 1 3 1 3 2l-2 3h-2c-1 1-1 0-2 0v-6z" class="D"></path><path d="M771 407c-1 1-1 1-1 2h1c1-2 2-3 4-5v1c1 1 1 0 0 1 1 1 2 2 3 2v-2l1 1h1v-2c1 2 1 4 0 6 0 1 0 1 1 2 0 3-1 4 1 6h1c2-1 3-2 4-3l2 2h-1s0 1-1 1v1l-1 1v2c-1 0-2 1-3 2-1 0-3 2-4 3l-2 2-1-1h-1l1-1v-1l-1 1-2-1c-2 1-3 3-6 4 1-2 0-4 0-6l1-3h1v-1h-1v-8c-1-2-1-4-1-6 1 0 1 1 2 0h2z" class="t"></path><path d="M778 406l1 1h1v-2c1 2 1 4 0 6 0 1 0 1 1 2 0 3-1 4 1 6h1c2-1 3-2 4-3l2 2h-1s0 1-1 1v1l-1 1v2c-1 0-2 1-3 2-1 0-3 2-4 3l-2 2-1-1h-1l1-1v-1l-1 1-2-1 3-3c-1 0-1 0-1-1 2 0 2 0 3-1v-1-3h0c0-1 0-1-1-1l2-1c0-1-1-2 0-2 0-1 0-1-1-2 0-2 1-2 0-4v-2z" class="h"></path><path d="M746 388v1l1 1c1 0 1 0 2-1v-1l1-1s1 1 2 1v-2l2 1c0-1 1-1 1-2 0-2 1-3 2-4l1-1 2-1h1v12 4 17c0 3 1 8 0 10l-3 3h-1c-1-1-1 0-2-1-1 1-1 1-2 1h-2c0-2 0-2-1-2h-1l-4 4h-2c-1-1-1-3 0-4-2-2-2-3-3-6v-6-3c0-2-1-5-1-7v-8-1c1-1 2-1 3-1 1-1 1 0 3 0l1-3z" class="v"></path><path d="M761 395c-1-1-1-1-1-2s-1-1-1-1l-1 1 1 1v2c-1 0-1 0-2-1v-1h-2 0c1-1 2-2 3-4h0c1 0 2 1 3 1v4z" class="t"></path><path d="M746 407l1 1c0 1 1 1 2 2v1h-1c-1 0-1 0-1 1l-1 1c-1 1-1 1-2 1l-2 2v-4h0v-2c2 0 3-2 4-3z" class="AE"></path><path d="M739 393v-1c1-1 2-1 3-1l1 6v3l1-1-1-1c1-1 2-1 3-1 0 0 0 1-1 2h1c1 0 2 2 3 3h1 0c1 1 1 0 1 1-1 1-1 1-1 2-1 0-1 1-2 1s-1 0-2 1-2 3-4 3v2h0-1v-1c0-1 0-2-1-3 0-2-1-5-1-7v-8z" class="y"></path><path d="M742 410v-9c1 0 2 0 2-1h1 1v1l-1 1 2 1 2-1h1 0c1 1 1 0 1 1-1 1-1 1-1 2-1 0-1 1-2 1s-1 0-2 1-2 3-4 3z" class="AK"></path><path d="M740 408c1 1 1 2 1 3v1h1v4l2-2c1 0 1 0 2-1 0 2 0 2 2 4 1 0 1-1 2-1l1-1c1 1 1 2 2 3l-1 1h1 2v1l1 1 1-1s1-1 2-1l1-1v1h-1l1 1c0 1 0 1 1 2l-3 3h-1c-1-1-1 0-2-1-1 1-1 1-2 1h-2c0-2 0-2-1-2h-1l-4 4h-2c-1-1-1-3 0-4-2-2-2-3-3-6v-6-3z" class="g"></path><path d="M740 408c1 1 1 2 1 3v1h1v4c0 2 0 4 1 6 1-1 1-1 2-1 0 1-1 1-2 2-2-2-2-3-3-6v-6-3z" class="p"></path><path d="M755 334c1 1 2 1 3 1s1-1 2-1c0 1-1 3 0 4s0 1 0 2c1 3 4 1 3 5 0 1 0 2-1 3h-2v4h-1c1 1 0 1 1 1l1 1c-1 1-1 1-2 1 1 1 2 1 2 2v3 9h0v10h-1l-2 1-1 1c-1 1-2 2-2 4 0 1-1 1-1 2l-2-1v2c-1 0-2-1-2-1l-1 1v1c-1 1-1 1-2 1l-1-1v-1h-2v1l-1 1c-1-2-1-3-1-5v-7-11l-2 1 2-7c-1 1-3 2-4 3h0l3-6h1c0-2 0-3 1-4 1-2 2-3 3-5l3-6c0-3 1-5 3-8h1l2-1z" class="D"></path><path d="M749 368c1 0 3 1 4 1l-1 1h-4l1-2z" class="G"></path><path d="M742 367c1-3 2-4 4-7l1 2 2 1-2 2c-1 1-2 1-2 2l1 2-2 2v1l1 1h1c-1 1 0 1-1 1-1 1-1 1-1 2h0v1l-2 1v-11z" class="N"></path><path d="M745 373l1-2h1c0 1 1 2 2 3l2-1v1h1s0-1 1-1h1c0-1 1-2 1-2v-1h1l1 1c0 1 1 1 2 1h1l-1-2v-1l1 1 1-1v10h-1l-2 1-1 1c-1 1-2 2-2 4 0 1-1 1-1 2l-2-1v2c-1 0-2-1-2-1l-1 1v1c-1 1-1 1-2 1l-1-1v-1h-2v1l-1 1c-1-2-1-3-1-5v-7l2-1v-1h0c0-1 0-1 1-2 1 0 0 0 1-1h-1z" class="g"></path><path d="M755 334c1 1 2 1 3 1s1-1 2-1c0 1-1 3 0 4s0 1 0 2c1 3 4 1 3 5 0 1 0 2-1 3h-2l-1 1c0 1 0 1-1 2l-1-1 1-1v-1c-1 1-2 1-3 2s-1 2-1 3v2 6c0 1-1 4-2 6-1 0-1 0-1-1-1 0-1-1-1-1v-1l-1-1-2-1-1-2c-2 3-3 4-4 7l-2 1 2-7c-1 1-3 2-4 3h0l3-6h1c0-2 0-3 1-4 1-2 2-3 3-5l3-6c0-3 1-5 3-8h1l2-1z" class="Z"></path><path d="M750 343v2 1 1h0c-1 2-4 4-5 7h-2c1-2 2-3 3-5l3-6h1z" class="I"></path><path d="M750 343c2-2 6-3 9-4l-2 3v1c-1 1-2 1-3 1v1c-1 0-2 1-4 2h0v-1-1-2z" class="o"></path><path d="M755 334c1 1 2 1 3 1s1-1 2-1c0 1-1 3 0 4l-1 1h0c-3 1-7 2-9 4h-1c0-3 1-5 3-8h1l2-1z" class="n"></path><path d="M760 338c1 1 0 1 0 2 1 3 4 1 3 5 0 1 0 2-1 3h-2l-1 1c0 1 0 1-1 2l-1-1 1-1v-1c-1 1-2 1-3 2s-1 2-1 3v2 6c0 1-1 4-2 6-1 0-1 0-1-1-1 0-1-1-1-1v-1l-1-1-2-1 1-1h1v1h1 1v-3-1-3l1-2-1 1-3-1h0v-1c2-1 3-4 5-5l1-1v-1-1c1 0 2 0 3-1v-1l2-3h0l1-1z" class="V"></path><path d="M800 167c3-1 6-2 9-2 0 2-2 4-2 6l3-3h1c-3 3-6 6-6 10l1-1c1 0 3 0 5-1 1 0 1 0 2-1 1 0 1 0 1-1h2c2 0 5 0 6-1h0 3c4-1 9-1 12-1-2 1-5 0-6 1 1 1 4 1 4 2l-6 1h0 1 2l23-1 36-3h13 8s1-1 2-1l20 5c-5 1-11 0-17 0-13 0-26 2-39 6-1 0-2 1-3 1-8 3-16 7-23 11-3 2-5 3-7 5l-3 3-4 5-2 4-1 9-1 16v-5h-2l-1-1h-1v5h-1c0-4 1-13-2-16l-2 2h0c-1 2-2 4-2 7-2 3-1 5-2 8l-1-1v-2c-2 2-1 5-3 6-1-1 0-1 0-2l-2-2h0l-1-1c0 3 0 7-2 9v1h0v1c-1 1-1 2-2 3l-1 1c0 1-1 1-1 2h0c-1 1-1 2-1 3-2 2-3 3-6 5l-1 1-1 2v1c-3 2-5 4-7 6v1h-1l-2 1c-1 1-3 2-5 2l-3 3h0l-2 1c0-1 0-2 1-3v-2h-2c1-2 2-3 3-5h0c-1 1-5 6-7 7-2 3-5 5-8 8v-1l-7 7c-1 1-2 2-3 4-1 1-1 2-2 3-2 1 0-1-1 1l-2 2v1h-1 0v-2l-2 2v1l-3 3h0c-2 2-3 3-6 5-2 1-5 2-8 4l-1-1 8-4h-1l-1 1h-3l-1 1-1-1h0c1-1 4-4 4-6-1 0-1 1-2 1-2 3-4 5-5 8h0c-2 1-5 3-6 5-3 3-6 6-10 9s-10 7-14 11c0 0 0 1-1 1h-1c-1 0-1 1-2 2h0c-1-2 2-4 2-6v-1l18-51 14-38h1 0c2-6 3-11 3-17 1 1 1 2 1 3v1l1 1v-1-2c1-2 1-2 2-3h1v-7l2 5h1l1-3 1-3v-1l2-1h2c2-2 3-4 5-5h1c2 0 2 0 4-1h1 1l-1 4c2-1 3-2 5-3 1 0 4-3 4-3l14-13 1 1c1 0 1-1 2-2 0-2 0-2 1-3 2-3 5-5 7-8v-1c1-1 1-2 1-3l9-8-2 1c-3 0-4 0-6-1l7-3z" class="AP"></path><path d="M826 216c1-2 4-3 6-4 3-1 3-3 6-5l-2 4c-1 1-1 1-1 3-3 2-5 2-9 2z" class="c"></path><path d="M759 288v-1c-1 0-2 1-2 1l-1 1h-1c1-1 1-2 2-3 2-1 4-3 5-4v-1l2-2h0c1-2 2-4 4-5h0l-3 5 2-1 3-2v-1c0-1 0-1 1-2v1 2l1-1c1-1 1-1 2-1-2 3-5 5-8 8v-1l-7 7z" class="E"></path><path d="M826 216c4 0 6 0 9-2-1 1-2 2-2 3-1 1-1 2-1 3h-2c-1 0-1-1-2-2l-1 1-2 2h0c-2 0-2 0-3 1v1c-1 1-1 2-3 2l-1 2c-2 0-1 0-2-1-1 0-2-1-2-1h0c3-3 8-6 12-9z" class="R"></path><path d="M835 214c0-2 0-2 1-3l-1 9-1 16v-5h-2l-1-1h-1v5h-1c0-4 1-13-2-16l1-1c1 1 1 2 2 2h2c0-1 0-2 1-3 0-1 1-2 2-3z" class="w"></path><path d="M822 223v-1c1-1 1-1 3-1-1 2-2 4-2 7-2 3-1 5-2 8l-1-1v-2c-2 2-1 5-3 6-1-1 0-1 0-2l-2-2h0l-1-1-1-5v-2h-1l2-2s1 1 2 1c1 1 0 1 2 1l1-2c2 0 2-1 3-2z" class="u"></path><path d="M813 229l3 1c0 2 1 4 1 7l-2-2h0l-1-1-1-5z" class="w"></path><path d="M822 223h0c-1 2-1 4-1 6-1 1-1 2-2 3-1 0-1 0-1-1-1-2-3-3-5-4h0-1l2-2s1 1 2 1c1 1 0 1 2 1l1-2c2 0 2-1 3-2z" class="Q"></path><path d="M771 274c1-1 2-2 2-3h1l1-2 1-1h0c2-2 4-4 5-6 1-1 2-2 3-4 1-1 3-2 4-4v-1h1l-1 3 2-2 1 1h1l-1-2 2-2c0-1 0-2 1-2 0-1 1-2 1-2v-1l1-1c0-2 1-2 2-3h1c-2 3-4 7-4 10s0 3 2 5h0v1c-1 1-5 0-6 0-1-1-1-1-1-2-2 1-3 4-3 6-1 2-4 5-5 8l-2 2h-2c1-2 2-3 3-5h0c-1 1-5 6-7 7-1 0-1 0-2 1l-1 1v-2z" class="S"></path><path d="M795 252c1 1 2 3 3 4v3h1c0 1 0 1 1 1l-1 2v1c-3 2-5 4-7 6v1h-1l-2 1c-1 1-3 2-5 2l-3 3h0l-2 1c0-1 0-2 1-3v-2l2-2c1-3 4-6 5-8 0-2 1-5 3-6 0 1 0 1 1 2 1 0 5 1 6 0v-1h0c-2-2-2-2-2-5z" class="j"></path><path d="M782 270l1-1c1 0 1-1 2-2 0-1 1-1 2-2h0c0 2-1 3-2 4l1 1-2 2v1l-3 3h0l-2 1c0-1 0-2 1-3v-2l2-2z" class="m"></path><path d="M780 274c1-1 2-2 4-2v1l-3 3h0l-2 1c0-1 0-2 1-3z" class="V"></path><path d="M786 270c1-1 4-4 6-5h1s0-1 1-1l1-1c1-1 2-1 4-1v1c-3 2-5 4-7 6v1h-1l-2 1c-1 1-3 2-5 2v-1l2-2z" class="I"></path><defs><linearGradient id="p" x1="708.934" y1="290.589" x2="715.918" y2="292.863" xlink:href="#B"><stop offset="0" stop-color="#585656"></stop><stop offset="1" stop-color="#8c8c89"></stop></linearGradient></defs><path fill="url(#p)" d="M732 241l-1 3h1l2-2c1-1 1-2 2-2h1c-3 4-7 9-9 14-3 6-5 14-8 20l-10 28c-3 8-7 16-9 24-1 3-2 7-3 10 0 0 0 1-1 1h-1c-1 0-1 1-2 2h0c-1-2 2-4 2-6v-1l18-51 14-38h1v1l3-3z"></path><path d="M812 227h1v2l1 5c0 3 0 7-2 9v1h0v1c-1 1-1 2-2 3l-1 1c0 1-1 1-1 2h0c-1 1-1 2-1 3-2 2-3 3-6 5l-1 1c-1 0-1 0-1-1h-1v-3c-1-1-2-3-3-4 0-3 2-7 4-10 4-5 8-10 13-15z" class="g"></path><path d="M798 256c1 0 1 1 2 1 2-1 4-2 4-3h0c1-1 2-2 2-3h1 1c-1 1-1 2-1 3-2 2-3 3-6 5l-1 1c-1 0-1 0-1-1h-1v-3z" class="m"></path><defs><linearGradient id="q" x1="806.949" y1="223.33" x2="758.09" y2="195.478" xlink:href="#B"><stop offset="0" stop-color="#696769"></stop><stop offset="1" stop-color="#a9aba5"></stop></linearGradient></defs><path fill="url(#q)" d="M800 167c3-1 6-2 9-2 0 2-2 4-2 6l3-3h1c-3 3-6 6-6 10l1-1c1 0 3 0 5-1 1 0 1 0 2-1 1 0 1 0 1-1h2c2 0 5 0 6-1h0 3c4-1 9-1 12-1-2 1-5 0-6 1 1 1 4 1 4 2l-6 1h0 1 2-1c-7 2-14 1-21 4-5 2-10 6-15 9-13 9-26 19-37 30l-21 21h-1c-1 0-1 1-2 2l-2 2h-1l1-3-3 3v-1h0c2-6 3-11 3-17 1 1 1 2 1 3v1l1 1v-1-2c1-2 1-2 2-3h1v-7l2 5h1l1-3 1-3v-1l2-1h2c2-2 3-4 5-5h1c2 0 2 0 4-1h1 1l-1 4c2-1 3-2 5-3 1 0 4-3 4-3l14-13 1 1c1 0 1-1 2-2 0-2 0-2 1-3 2-3 5-5 7-8v-1c1-1 1-2 1-3l9-8-2 1c-3 0-4 0-6-1l7-3z"></path><path d="M757 213c2-1 3-2 5-3l-21 21c0-2 0-4 1-5v-2c3-2 5-4 7-6 3-2 5-4 8-5z" class="J"></path><path d="M736 225h1v2h1c1-1 3-2 4-3v2c-1 1-1 3-1 5l-5 5-4 5-3 3v-1h0c2-6 3-11 3-17 1 1 1 2 1 3v1l1 1v-1-2c1-2 1-2 2-3z" class="f"></path><path d="M742 226c-1 1-1 3-1 5l-5 5h0c-1-2 1-4 2-6s2-3 4-4z" class="M"></path><path d="M751 210h1c2 0 2 0 4-1h1 1l-1 4c-3 1-5 3-8 5-2 2-4 4-7 6-1 1-3 2-4 3h-1v-2-7l2 5h1l1-3 1-3v-1l2-1h2c2-2 3-4 5-5z" class="AB"></path><path d="M742 216l2-1h2c-1 1-3 4-5 5l1-3v-1z" class="X"></path><path d="M800 167c3-1 6-2 9-2 0 2-2 4-2 6l3-3h1c-3 3-6 6-6 10-3 3-7 5-11 8 0 2-1 3-3 3l-1 1-2 1-1-1 1-1 3-4 1-1c1-2 2-3 3-5-2 1-3 3-4 5-3 3-6 6-8 9 0-2 0-2 1-3 2-3 5-5 7-8v-1c1-1 1-2 1-3l9-8-2 1c-3 0-4 0-6-1l7-3z" class="i"></path><path d="M788 189h1c1-1 2-3 3-4 2-2 3-4 5-6h1c-1 3-4 5-5 7h1c0 2-1 3-3 3l-1 1-2 1-1-1 1-1z" class="e"></path><path d="M694 499l1 1c1-1 2-2 4-3 1 1 0 3 0 5-2 1-3 2-4 4v1c1 0 1 1 1 2h0v1c0 2-1 4-1 6v1s0 1 1 1l-1 8c1 0 2-1 3-1 1-1 1-2 2-2h1l1 1v-1l1-2h3v2c-1 0-1 0-2 1-2 0-3 2-5 3h0v4c2 0 2 0 3 1l1 1h1v1h2v1c1 0 1 1 2 1h0l1 3c1 0 3 1 4 1 0-1 0-2 1-2l1-1h0 1v2h1l1-2c1 1 1 2 2 3 1-1 1-2 2-3h0 0c1 3 2 4 2 6l-1 1h0l1 1v1 9c0 2-1 4 0 6h1 0v-1c1-2 0-5 1-7v-4c1 1 1 2 1 3h0l1 1-1 71-23-5-7-3c-2 0-3-1-3-3h0l-1-1-1 2-3-1h-1c1 2 3 2 4 4v2c1 1 1 1 3 2 1-1 1-2 2-3 2 0 4 1 6 2 6 2 12 3 18 4 1 1 2 1 3 1s2 1 3 1v6c0 1 0 1-1 2-1 0-2-1-3-1 0 1 0 2 1 2l-1 1c0 1 0 0-1 1 0 1-1 2-1 3l-1 1-9 11c-1 2-7 8-7 11l-48 58 1 1-2-1v-8s-1-1-2 0c0 0 0 6-1 7v1l-1-77v-1c-2 0-4-1-6-2l-3-1v-5c1-1 2-1 3-2 1-2 1-2 0-3-2-2-3-1-5-2l-1-1 1-4c0-4 2-6 4-9 2-2 4-5 6-7v-3l-1 1c1-2 1-3 1-5 0-1 0-2-1-3h-3c1-1 1-1 2-1s1 0 2-1v-9h-1l-1-1 1-1-1-1 2-3 1-2c0-3 0-5 1-7v-1c1-3 1-6 2-8 1-3 3-5 4-8s4-6 5-9c0-1 0-2 1-2 1-2 2-4 3-5l2-5 1-2c0-1 0-2 1-2 1-2 1-5 2-7l2-2 1 1 1-1h2l1 1 1-1c1 0 1-1 2 0h1c0-1 1-2 1-3 1 0 1-1 2-1-1-2-1-2 0-4l2-5c0-3 3-6 5-8z" class="AO"></path><path d="M701 554h3l1 1v3 1h-2l-1 1-1-1v-1l-1-2c0-1 1-1 1-2z" class="D"></path><path d="M682 637h4c0 3 1 10-1 12-1-1-1-6-1-8 0-1-1-2-1-4h-1z" class="h"></path><path d="M656 600h1v3h1c2 1 5 2 7 4v1l-8-2v4h-1c0-2 1-4 1-5-1-2-1-3-1-5z" class="l"></path><path d="M697 616l2-2c1-2 0-1 2-3l2 1v3h2 0c0 1-1 1-1 2v2l-7-3z" class="g"></path><path d="M711 652c0-1 0-3 1-4v-1c0-3 1-3 3-5h1c1-1 2 0 4-1l-9 11z" class="G"></path><path d="M721 624c1 1 2 1 3 1s2 1 3 1v6c0 1 0 1-1 2-1 0-2-1-3-1 0 1 0 2 1 2l-1 1c0 1 0 0-1 1 0 1-1 2-1 3-2-1-3 0-4-1v-5h1 1c0-2 0-2-1-3l1-2c1-1 1-3 2-4v-1zm-22-97v4c2 0 2 0 3 1l1 1h1v1h2v1c1 0 1 1 2 1h0l1 3c1 0 3 1 4 1 0-1 0-2 1-2l1-1h0 1v2h1l1-2c1 1 1 2 2 3 1-1 1-2 2-3h0 0c1 3 2 4 2 6l-1 1h0l1 1v1 9c-1 0-2 0-3-1h-1c-1-1-3-1-4 0-1 0-2 0-2 2l1 1-1 1c-1 0-1 0-2 1h0-1l-2 1-1-1c0-1-1-2-1-2l1-1v2h1l1-1v-1l1 1v-2-1-1c1-1 1-2 2-2l1-1-3-1c0-1-1-1-1-2v-1h-3l-1-1v-2c-1 0-2 0-3 1v1c1 0 1 0 2 1v1c1 1 2 1 3 2l-1 1c-1 1-1 1-2 0l-1-2h-1c0-1-1-1-1-2l-1-1h-2-1c-1 0-2 0-3 2 1 1 0 1 1 1h1l1 2h-1l-2-1-2 2v-6s1-1 1-2h0c0-2 1-3 3-4h-2l-1-1c-1-2 0-3 0-5l1-1c0-2 2-3 4-5z" class="D"></path><path d="M709 539c1 0 3 1 4 1l-1 1-3 2h-1c0 1 0 1-1 2-1-1-1-2-1-4h1l1 1c1-1 1-2 1-3z" class="N"></path><path d="M699 527v4c2 0 2 0 3 1l1 1h1v1h2v1c1 0 1 1 2 1h0l1 3c0 1 0 2-1 3l-1-1h-1c-1 0-2 1-3 2v-1l-1 1v1 1l-2-1v-2c-1-1-1-2-1-3l-1-1-1 1h-2l-1-1c-1-2 0-3 0-5l1-1c0-2 2-3 4-5z" class="g"></path><path d="M655 579v6 3h0v3 5c0 1 1 2 1 4s0 3 1 5c0 1-1 3-1 5h1v18c0 2-1 6 0 7h2c-1 0-1 1-2 1-2 20-1 41-1 61v15 9l1 1-2-1v-8s-1-1-2 0c0 0 0 6-1 7v1l-1-77v-1c-2 0-4-1-6-2l-3-1v-5c1-1 2-1 3-2 1-2 1-2 0-3-2-2-3-1-5-2l-1-1 1-4c0-4 2-6 4-9 2-2 4-5 6-7v-3l-1 1c1-2 1-3 1-5 0-1 0-2-1-3h-3c1-1 1-1 2-1s1 0 2-1v-9h-1l-1-1 1-1-1-1 2-3c1 1 1 2 1 3s2 2 3 3c0-2 0-5 1-7z" class="x"></path><path d="M655 579v6 3h0v3c0 2 0 4-1 5s0 3 0 5v10 20 6l-2-2-2-1v-27-3l-1 1c1-2 1-3 1-5 0-1 0-2-1-3h-3c1-1 1-1 2-1s1 0 2-1v-9h-1l-1-1 1-1-1-1 2-3c1 1 1 2 1 3s2 2 3 3c0-2 0-5 1-7z" class="m"></path><path d="M655 579v6 3h0c0 1-1 2-1 3l-1 1h-1c0-1 1-1 1-2l-1-1v-3c-1-1-1-1-1-2v-1c0 1 2 2 3 3 0-2 0-5 1-7z" class="j"></path><defs><linearGradient id="r" x1="655.648" y1="635.805" x2="639.873" y2="617.389" xlink:href="#B"><stop offset="0" stop-color="#efe1e4"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#r)" d="M640 623c0-4 2-6 4-9 2-2 4-5 6-7v27l1 9c-2 0-4-1-6-2l-3-1v-5c1-1 2-1 3-2 1-2 1-2 0-3-2-2-3-1-5-2l-1-1 1-4z"></path><path d="M665 572h2c1 0 2-1 3-1 0 2-1 3-1 5v3c1 3 9 6 12 7v1c1 1 0 3 1 4l-1 11v4c1 3 1 5 0 7-3-2-7-2-11-3l-2 81h-1c-1-4 0-10 0-14l-1-43-1-26v-1c-2-2-5-3-7-4h-1v-3h-1c0-2-1-3-1-4v-5-3h0c2-3 2-7 3-9l1-1c1-1 1-2 2-3h1 2l1-3z" class="y"></path><path d="M665 588c1 6 1 13 0 19-2-2-5-3-7-4h-1v-3c0-1 0-1 1-2 1 1 3 3 5 3l1-2h1v-11z" class="D"></path><path d="M674 608l-4-1v-10l6 4h1l1 2c-1 1-1 2-3 3h0c-1 1-1 1-1 2zm-16-29l1-1c1-1 1-2 2-3l1 2c1 0 1-1 2-1v2c1 2 1 7 1 10v11h-1l-1 2c-2 0-4-2-5-3-1 1-1 1-1 2h-1c0-2-1-3-1-4v-5-3h0c2-3 2-7 3-9z" class="r"></path><path d="M658 579l1-1c1-1 1-2 2-3l1 2c1 0 1-1 2-1v2c-1 2-1 2-3 3l-3 3c-1 3-1 8-1 11h1c2 1 4 3 6 4l-1 2c-2 0-4-2-5-3-1 1-1 1-1 2h-1c0-2-1-3-1-4v-5-3h0c2-3 2-7 3-9z" class="AM"></path><path d="M677 601l-4-3s-3-3-3-4c-1 0-1-2-1-3v-10c4 2 7 4 11 6h1c1 1 0 3 1 4l-1 11v4 4c-2 0-5-1-7-2 0-1 0-1 1-2h0c2-1 2-2 3-3l-1-2z" class="AO"></path><path d="M694 499l1 1c1-1 2-2 4-3 1 1 0 3 0 5-2 1-3 2-4 4v1c1 0 1 1 1 2h0v1c0 2-1 4-1 6v1s0 1 1 1l-1 8c1 0 2-1 3-1 1-1 1-2 2-2h1l1 1v-1l1-2h3v2c-1 0-1 0-2 1-2 0-3 2-5 3h0c-2 2-4 3-4 5l-1 1c0 2-1 3 0 5l1 1h2c-2 1-3 2-3 4h0c0 1-1 2-1 2v6 5h-1c0-4-1-7-1-11-1 2-3 4-4 7v4h0c0 5-1 27 0 30v2l11 5-1 1-10-5v25 1c-1 7-1 15-1 22h-4c-2-7 0-17-1-24 1-2 1-4 0-7v-4l1-11c-1-1 0-3-1-4v-1c-3-1-11-4-12-7v-3c0-2 1-3 1-5-1 0-2 1-3 1h-2l-1 3h-2-1c-1 1-1 2-2 3l-1 1c-1 2-1 6-3 9v-3-6c-1 2-1 5-1 7-1-1-3-2-3-3s0-2-1-3l1-2c0-3 0-5 1-7v-1c1-3 1-6 2-8 1-3 3-5 4-8s4-6 5-9c0-1 0-2 1-2 1-2 2-4 3-5l2-5 1-2c0-1 0-2 1-2 1-2 1-5 2-7l2-2 1 1 1-1h2l1 1 1-1c1 0 1-1 2 0h1c0-1 1-2 1-3 1 0 1-1 2-1-1-2-1-2 0-4l2-5c0-3 3-6 5-8z" class="p"></path><path d="M680 521l1-1c1 0 1-1 2 0h1c0-1 1-2 1-3v5c0 1 0 2-1 4v2c1 1 1 3 1 4 1 2 0 6-1 8-1 1-2 3-3 3 0-1 0-1-1-2h0l1-2v-1l-1-1h-1c0-1 0-1 1-2l1 1c0-2 0-5-1-7h-1c0-1 1-1 1-2v1c1 0 2 1 3 0 0-2-2-5-3-7z" class="x"></path><path d="M690 537c-1-1-1-1-2 0l-1-1 1-1v-1c-1-2-1-3 0-4h2l2 2c0-1 1-2 1-2h2v-1c-1 0-2 0-2-1l-1-2h-1l2-2-1-1c1-2 2-3 4-5l-1 8c1 0 2-1 3-1 1-1 1-2 2-2h1l1 1v-1l1-2h3v2c-1 0-1 0-2 1-2 0-3 2-5 3h0c-2 2-4 3-4 5l-1 1c0 2-1 3 0 5l1 1h2c-2 1-3 2-3 4h0c0 1-1 2-1 2v6 5h-1c0-4-1-7-1-11-1 2-3 4-4 7v4h0c-1-2-1-6 0-8v-7c0-2 1-3 3-4z" class="l"></path><path d="M690 537c1 1 1 3 1 4s0 1-1 2v1c-1 1-2 3-3 4h0v-7c0-2 1-3 3-4z" class="AK"></path><path d="M681 556v24c0 2 1 4 0 6-3-1-11-4-12-7v-3c0-2 1-3 1-5l1-1 7-9c1-2 2-3 3-5z" class="AO"></path><path d="M669 579c2 0 3 0 5 1h2v2h3c1-1 2-1 2-2 0 2 1 4 0 6-3-1-11-4-12-7zm12-41v1l-1 2h0c1 1 1 1 1 2h0l-2 2h1l1 1c0 2 0 3-1 5v1c0 2 0 2 1 4-1 2-2 3-3 5l-7 9c-1 0-2 0-3-1l1-2v-9-1-1c1-4 2-8 5-12 1 0 1-1 1-1 1-1 1-1 1-2h1c1 0 2-1 2-2 1 0 1-1 2-1z" class="F"></path><path d="M680 545l1 1c0 2 0 3-1 5v1c0 2 0 2 1 4-1 2-2 3-3 5l-7 9c-1 0-2 0-3-1l1-2c2-3 6-7 8-10-1-1-1-3-2-3-1-1-1 0-2-1l2-3 1-1c1-2 2-3 4-4z" class="h"></path><path d="M680 545l1 1c0 2 0 3-1 5v1c-1 1-2 3-3 4v-1c-1-2-1-3-2-4v-1l1-1c1-2 2-3 4-4z" class="D"></path><path d="M673 522l2-2 1 1 1-1h2l1 1c1 2 3 5 3 7-1 1-2 0-3 0v-1c0 1-1 1-1 2h1c1 2 1 5 1 7l-1-1c-1 1-1 1-1 2h1l1 1c-1 0-1 1-2 1 0 1-1 2-2 2h-1c0 1 0 1-1 2 0 0 0 1-1 1-3 4-4 8-5 12v1 1 9l-1 2c1 1 2 1 3 1l-1 1c-1 0-2 1-3 1h-2l-1 3h-2-1c-1 1-1 2-2 3l-1 1c-1 2-1 6-3 9v-3-6c-1 2-1 5-1 7-1-1-3-2-3-3s0-2-1-3l1-2c0-3 0-5 1-7v-1c1-3 1-6 2-8 1-3 3-5 4-8s4-6 5-9c0-1 0-2 1-2 1-2 2-4 3-5l2-5 1-2c0-1 0-2 1-2 1-2 1-5 2-7z" class="T"></path><path d="M665 565h0c2-3 1-5 3-7h0 1v9l-1 2c1 1 2 1 3 1l-1 1c-1 0-2 1-3 1h-2 0v-1-6z" class="l"></path><path d="M661 554c2-3 4-7 5-11 1 2 1 4 0 5-2 3-1 7-3 10s-2 7-5 9c-1 1-1 2-2 3 0-1 0-1 1-2v-1l1-4 3-9zm12-32l2-2 1 1 1-1h2l1 1c1 2 3 5 3 7-1 1-2 0-3 0v-1c-1-1-1-1-3-1h0c-2 1-3 1-5 2h0c-1 1-1 2-2 3 0-1 0-2 1-2 1-2 1-5 2-7z" class="V"></path><path d="M671 539v-1c1-3 1-6 3-9h2c0-1 0-1 2-1l1 1h1c1 2 1 5 1 7l-1-1v-1c-2 0-2 1-3 2-2 2-2 3-5 3h-1z" class="v"></path><path d="M671 539h1c3 0 3-1 5-3 1-1 1-2 3-2v1c-1 1-1 1-1 2h1l1 1c-1 0-1 1-2 1 0 1-1 2-2 2h-1c0 1 0 1-1 2 0 0 0 1-1 1-3 4-4 8-5 12v1c0-1 0-2-1-4h0c1-2 2-5 2-7v-2-1h1v-1-2-1z" class="AM"></path><path d="M662 566c1-1 1-2 2-3l1 2v6 1h0l-1 3h-2-1c-1 1-1 2-2 3l-1 1c-1 2-1 6-3 9v-3-6l1-6c1 0 1 0 2-1v-3c1-1 2-2 4-3z" class="t"></path><path d="M665 572c-1 1-1 0-1 1l-2-1-1-1c1 0 1 0 1-1v-1h2l1 2v1z" class="F"></path><path d="M656 573c1 0 1 0 2-1v-3c1-1 2-2 4-3-2 4-3 8-4 13-1 2-1 6-3 9v-3-6l1-6z" class="y"></path><path d="M658 554c1-3 4-6 5-9 0-1 0-2 1-2 1-2 2-4 3-5 0 1 0 3-1 5h0c-1 4-3 8-5 11l-3 9-1 4v1c-1 1-1 1-1 2v3l-1 6c-1 2-1 5-1 7-1-1-3-2-3-3s0-2-1-3l1-2c0-3 0-5 1-7v-1c1-3 1-6 2-8 1-3 3-5 4-8z" class="R"></path><path d="M661 554c0-2 0-3 1-5 1 0 1-1 1-1v-1c0-1 1-1 1-2 1-2 1-2 2-2h0c-1 4-3 8-5 11z" class="b"></path><path d="M652 571l2 2c2-1 1-4 2-6 0-1 1-2 2-4l-1 4v1c-1 1-1 1-1 2v3l-1 6c-1 2-1 5-1 7-1-1-3-2-3-3s0-2-1-3l1-2c0-3 0-5 1-7z" class="u"></path><defs><linearGradient id="s" x1="662.111" y1="400.812" x2="702.096" y2="416.076" xlink:href="#B"><stop offset="0" stop-color="#040605"></stop><stop offset="1" stop-color="#452928"></stop></linearGradient></defs><path fill="url(#s)" d="M774 274c2-1 6-6 7-7h0c-1 2-2 3-3 5h2v2c-1 1-1 2-1 3l2-1v2c-2 2-4 4-5 7h1c0 1 0 1-1 3v2h0v1c-1 3-1 5-1 8 1 1 1 3 2 4v3c0 1-1 2-2 3-3 3-3 7-4 11l-1 1-2 2h-1c-1 0-1 1-2 2h-1c-1 1-1 2-2 2h-1l1 1-1 1 1 1c-1 1-2 3-2 4-1 0-1 1-2 1s-2 0-3-1l-2 1h-1c-2 3-3 5-3 8l-3 6c-1 2-2 3-3 5-1 1-1 2-1 4h-1l-3 6h0c1-1 3-2 4-3l-2 7 2-1v11 7c0 2 0 3 1 5l1-1v-1h2l-1 3c-2 0-2-1-3 0-1 0-2 0-3 1v1 8c0 2 1 5 1 7v3c-1 1-1 2-1 4h-2v-3l-1 1h-2-3l-2 1-3-1 2-2c-3 0-5 0-7 1v-1c0-1 1-1 2-2v-1-4l-1-1h0l-1-1c-1 0-2 1-3 2-1 2-1 3-3 4l-1-2c-1 4-5 8-8 11l-8 9 1 1v1h0l-2 2 1 1c-6 6-13 14-20 19l-9 8c1-6 2-11 3-16h0v-3l1-2c-2 0-3 2-4 3l-14 31-2 1v-2c1-2 1-3 2-4l-1-2c-1 1-2 3-3 4-1 2-2 5-3 7l1 1c-1 3-2 5-3 6l-1 2c-2 2-4 4-5 6l-2 2c-2 2-4 3-5 5s-2 4-4 5c-1 3-3 5-5 7s-3 5-6 7h0l9-24v-1h-2l-3 5-1-1c1-1 1-2 1-4 0-1 1-2 2-3v-1l-1-1v-1h-1c-1 2-2 4-3 5h-1l2-3c0-1 0-2 1-3l2-5-4 4c0-1 1-2 1-4h0v-1-1l-1 2-1-1c0-1 2-2 3-3-1-1-2-2-3-2h-1c-1 1-1 2-3 3l-2 2-1-1c-1 2-2 3-3 4-3 1-5 4-7 6 1-2 2-3 3-4 1-2 3-4 4-7 1 0 5-3 5-4 1-2 2-3 3-4l1 1v-2c1-1 1-2 3-4v-1c0-1 0-3 1-5v-1c1-1 1-1 1-2l5-11 3-6 1-2 12-26 10-23 1-2c0-3 1-5 3-7 0-1 1-1 1-2s0-2-1-3c0-1 1-1 0-3h-1-1v-2h2 1c5 3 8 4 14 3 2 0 3-1 5-2l-2-2 2-3 3-3 5-5c6-4 5-8 7-15 1-3 3-5 4-8v1c0 2-3 4-2 6h0c1-1 1-2 2-2h1c1 0 1-1 1-1 4-4 10-8 14-11s7-6 10-9c1-2 4-4 6-5h0c1-3 3-5 5-8 1 0 1-1 2-1 0 2-3 5-4 6h0l1 1 1-1h3l1-1h1l-8 4 1 1c3-2 6-3 8-4 3-2 4-3 6-5h0l3-3v-1l2-2v2h0 1v-1l2-2c1-2-1 0 1-1 1-1 1-2 2-3 1-2 2-3 3-4l7-7v1c3-3 6-5 8-8z"></path><path d="M673 401c0 4-1 7-3 11h0l-1-1c-1-3 2-7 4-10z" class="E"></path><path d="M692 369c1-1 1-2 3-3h0 1c0 2-3 3-3 6h0c-2 1-3 3-4 5v2 1 1c-2 2-5 5-5 7h-1l-1-1 1-1v-2c3-4 4-9 7-12 0-1 1-2 1-2l1-1z" class="AW"></path><path d="M674 399v4h0l1-1v-1l1-1h0v-1-1l1 1-1 1-1 5v1l1-1 1 1h0c-1 1-1 2-1 4-1 2-3 5-5 7l-2 4-3 3h-1l5-12h0c2-4 3-7 3-11l1-2z" class="AC"></path><path d="M676 405l1 1h0c-1 1-1 2-1 4-1 2-3 5-5 7h0c2-2 0-2 1-3 1-3 3-5 3-8l1-1z" class="c"></path><path d="M677 368l8-8c0 2-1 4-2 6l-9 22c-1-2-1-3-1-5-1-1 2-6 3-7l-1-1v-1c0-1 1-2 1-3h-1c-1 0-2 0-3-1 2 0 3-1 5-2z" class="AY"></path><path d="M677 368l8-8c0 2-1 4-2 6l-1-1c-1 2-2 3-3 5s-3 3-3 6l-1-1v-1c0-1 1-2 1-3h-1c-1 0-2 0-3-1 2 0 3-1 5-2z" class="B"></path><path d="M629 502h0l-4 3c2-5 6-9 8-15l13-26c0-1 1-3 2-4 0-2 1-2 2-3v4c-2 4-3 8-5 12s-5 8-6 13c-1 1-1 2-2 3 1 1 0 0 2 1h1l-2 2c-2 2-4 3-5 5s-2 4-4 5z" class="AI"></path><path d="M722 316c0 2-4 5-6 6-6 7-14 13-19 20-2 3-3 6-4 9-2 3-5 6-8 9l-8 8-2-2 2-3 3-3 5-5c6-4 5-8 7-15 1-3 3-5 4-8v1c0 2-3 4-2 6h0c1-1 1-2 2-2h1c1 0 1-1 1-1 4-4 10-8 14-11s7-6 10-9z" class="L"></path><defs><linearGradient id="t" x1="638.439" y1="466.609" x2="659.561" y2="465.391" xlink:href="#B"><stop offset="0" stop-color="#251b17"></stop><stop offset="1" stop-color="#462729"></stop></linearGradient></defs><path fill="url(#t)" d="M653 452c0 3-2 5-2 7h0c1-1 1-2 1-3h1c0-2 1-2 2-4 0-1 0 0 1-1h0c1-2 2-3 2-4 1 0 1 0 2 1l-3 9c-1 1-3 5-3 7-1 1-2 3-3 4-1 2-2 5-3 7l1 1c-1 3-2 5-3 6l-1 2c-2 2-4 4-5 6h-1c-2-1-1 0-2-1 1-1 1-2 2-3 1-5 4-9 6-13s3-8 5-12v-4c1 0 0 0 1-1 0-1 1-2 2-4z"></path><path d="M643 483l2 1c-2 2-4 4-5 6h-1c0-3 2-5 4-7z" class="S"></path><path d="M648 475l1 1c-1 3-2 5-3 6l-1 2-2-1c1-3 3-6 5-8z" class="AI"></path><path d="M759 288l7-7v1c-6 8-11 17-17 26-2 2-3 5-5 6-1 1-2 3-3 4s-1 2-2 3c-2 3-5 7-7 9-2 3-5 4-7 6h-1l24-34h0c-1 0-1 1-2 1h-1 0l3-3v-1l2-2v2h0 1v-1l2-2c1-2-1 0 1-1 1-1 1-2 2-3 1-2 2-3 3-4z" class="S"></path><defs><linearGradient id="u" x1="679.993" y1="378.987" x2="688.175" y2="383.608" xlink:href="#B"><stop offset="0" stop-color="#271817"></stop><stop offset="1" stop-color="#4e2b2a"></stop></linearGradient></defs><path fill="url(#u)" d="M694 358l3-4 1 1 3-3v1s-1 2-2 2c-2 2-1 0-2 3-1 1-1 3-2 4-2 2-3 4-4 7h1l-1 1s-1 1-1 2c-3 3-4 8-7 12v2l-1 1 1 1h1c1 0 1-1 2-1 0 1 0 1-1 2 0 1 0 2-1 3h1c-1 2-2 4-2 6-1 1-2 3-2 4-2 3-3 5-5 8 0-2 0-3 1-4h0l-1-1-1 1v-1l1-5 1-1-1-1v1 1h0l-1 1v1l-1 1h0v-4c2-5 4-11 6-16l4-9c1-2 2-2 2-3v-1c0-3 5-11 8-12z"></path><path d="M694 358l3-4 1 1 3-3v1s-1 2-2 2c-2 2-1 0-2 3-1 1-1 2-2 3-2 1-2 2-3 4v1-1-4h0l2-3z" class="a"></path><path d="M684 392h1c-1 2-2 4-2 6-1 1-2 3-2 4l-5 8c0-2 0-3 1-4h0l-1-1c1-1 2-2 2-4l6-9z" class="AC"></path><path d="M681 402v1c0 1 0 1-1 2v2h1v-1l1 1v1l-2 2c-1 1-1 2-2 4h-1l1 2c-2 2-4 5-5 7v1l1-1h1c-2 2-3 3-4 5v1l1-1 1 1c0-1 1-1 2-2h1v1l1 1-4 8c-2 0-3 2-4 3l-14 31-2 1v-2c1-2 1-3 2-4l-1-2c0-2 2-6 3-7l3-9c-1-1-1-1-2-1 0 1-1 2-2 4h0c-1 1-1 0-1 1-1 2-2 2-2 4h-1c0 1 0 2-1 3h0c0-2 2-4 2-7 0-1 2-5 3-6l9-22h1l3-3 2-4c2-2 4-5 5-7l5-8z" class="AH"></path><path d="M675 427h1v1l1 1-4 8c-2 0-3 2-4 3 1-3 3-4 4-7 0-2 2-3 2-6zm-15 21v-1c0-2 2-4 3-6h0c0 2-1 4-2 6-1 3-5 18-6 19l-1-2c0-2 2-6 3-7l3-9z" class="c"></path><path d="M681 402v1c0 1 0 1-1 2v2h1v-1l1 1v1l-2 2c-1 1-1 2-2 4h-1v1c-3 3-4 5-6 8l-1 1c-1-1 0 1 0-1-1-1-1-1-1-2l2-4c2-2 4-5 5-7l5-8z" class="s"></path><path d="M657 367h1c5 3 8 4 14 3 1 1 2 1 3 1h1c0 1-1 2-1 3v1l1 1c-1 1-4 6-3 7 0 2 0 3 1 5l-47 104v-1h-2l-3 5-1-1c1-1 1-2 1-4 0-1 1-2 2-3v-1c3-5 5-9 7-14 1-3 3-5 4-7 0-4 3-8 5-11 1-3 2-5 3-8 1-1 2-3 2-5h0c0-2 1-3 2-4 0-2 0-3 1-4 1-2 1-4 2-7l11-23 3-9c-1-1-1-1-2-1h-1 0l1-2v-1-3h0-1s0-1 1-2c0-2 0-3-1-5l-1 2h-1l-1 1c-2 0-2 0-4 1l-1 1 1-2c0-3 1-5 3-7 0-1 1-1 1-2s0-2-1-3c0-1 1-1 0-3h-1-1v-2h2z" class="AX"></path><path d="M650 427l11-23v1c-1 1-1 2-1 3-1 2-4 6-3 8 0-1 1-1 2-1v-2-1c2-1 1-3 3-5l-10 24-1 1-3 6h-1c0-2 0-3 1-4 1-2 1-4 2-7z" class="AW"></path><path d="M647 438h1l3-6 1-1c0 2 0 3-1 5l-24 55h-2l-3 5-1-1c1-1 1-2 1-4 0-1 1-2 2-3v-1c3-5 5-9 7-14 1-3 3-5 4-7 0-4 3-8 5-11 1-3 2-5 3-8 1-1 2-3 2-5h0c0-2 1-3 2-4z" class="AY"></path><path d="M635 466v1l-3 6-7 18-3 5-1-1c1-1 1-2 1-4 0-1 1-2 2-3v-1c3-5 5-9 7-14 1-3 3-5 4-7z" class="C"></path><path d="M657 367h1c5 3 8 4 14 3 1 1 2 1 3 1h1c0 1-1 2-1 3v1l1 1c-1 1-4 6-3 7-5 8-6 17-11 24-2 2-1 4-3 5v1 2c-1 0-2 0-2 1-1-2 2-6 3-8 0-1 0-2 1-3v-1l3-9c-1-1-1-1-2-1h-1 0l1-2v-1-3h0-1s0-1 1-2c0-2 0-3-1-5l-1 2h-1l-1 1c-2 0-2 0-4 1l-1 1 1-2c0-3 1-5 3-7 0-1 1-1 1-2s0-2-1-3c0-1 1-1 0-3h-1-1v-2h2z" class="a"></path><path d="M671 374l1 1h0v2c-1 2-2 2-2 4h-1c0-3 0-5 2-7h0z" class="S"></path><g class="AH"><path d="M667 376l2-2h2c-2 2-2 4-2 7-1 1-1 2-2 4 0-3 1-6 0-9z"></path><path d="M667 376c1 3 0 6 0 9v1l-3 9c-1-1-1-1-2-1h-1 0l1-2v-1-3h0c1-2 1-5 1-7v-1l2-2 2-2z"></path></g><path d="M667 376c1 3 0 6 0 9v1c-1-1 0-1-1-1 0 1 0 1-1 2v-3c0-1-1-2 0-3v-3l2-2z" class="AY"></path><path d="M657 367h1c0 1 1 3 2 4h2c1 0 2 1 4 2h1c2 0 3 0 4 1h0-2l-2 2-2 2-2 2v1c0 2 0 5-1 7h0 0-1s0-1 1-2c0-2 0-3-1-5l-1 2h-1l-1 1c-2 0-2 0-4 1l-1 1 1-2c0-3 1-5 3-7 0-1 1-1 1-2s0-2-1-3c0-1 1-1 0-3h-1-1v-2h2z" class="AW"></path><path d="M657 379l1-1 3 3-1 2h-1l-1-1v-2l-1-1z" class="AV"></path><path d="M657 379l1 1v2l1 1-1 1c-2 0-2 0-4 1l-1 1 1-2 3-5z" class="AX"></path><path d="M725 336c2-2 5-3 7-6 2-2 5-6 7-9 1-1 1-2 2-3s2-3 3-4c0 2-1 3-2 5h1c-4 7-10 14-11 22l-2 2c-1 4-2 7-4 10l-6 6-1 3 1 1h-1v1h0c1 0 1 0 2 1h0l1 1h0c0 1 0 1-1 2h0l-3 3h-2v1c-2 5-7 10-10 15-1 2-3 4-3 5-1 1-1 2-2 3s1-1-1 1c0 1-2 2-2 3-3 3-5 7-7 10-1 3-3 5-3 7-2 3-2 6-4 9h-1l-1 1v-1l1-2v-3-1h1v-3c-1 2-3 3-5 5h1c-1 1-1 0-1 1-2 0-3 2-4 3v2c-1 1-2 1-2 2l-1-1-1 1v-1c1-2 2-3 4-5h-1l-1 1v-1c1-2 3-5 5-7l-1-2h1c1-2 1-3 2-4l2-2v-1l-1-1v1h-1v-2c1-1 1-1 1-2v-1c0-1 1-3 2-4 0-2 1-4 2-6h-1c1-1 1-2 1-3 1-1 1-1 1-2-1 0-1 1-2 1 0-2 3-5 5-7v-1-1c1-1 2-2 4-3 1-1 2-2 3-4s1-2 3-4c0 0 0-1 1-2s1-2 2-3c2-1 2-2 3-4h0c5-4 9-9 12-15h0c1-2 3-4 5-5 1-1 2-1 3-3z" class="AH"></path><path d="M706 376h0c0 1-1 1-1 2-1 4-2 7-4 10l-1 2-1-1 3-6h0v-2c1-2 3-5 4-5z" class="c"></path><path d="M683 398v2h1l1 1h0l-3 6-1-1v1h-1v-2c1-1 1-1 1-2v-1c0-1 1-3 2-4z" class="H"></path><path d="M718 349c4-6 8-10 13-14v1l-12 14-1-1z" class="a"></path><path d="M702 372c2 1 3 1 4 0l8-6c-1 3-4 5-6 7-1 0-2 1-3 1-2 0-4 1-5 2h-2l4-4z" class="c"></path><path d="M718 349l1 1c-1 1-1 3-2 4-2 2-3 3-4 5-4 5-6 9-11 13l-4 4c-4 1-6 3-9 6v-1-1c3-3 7-4 9-6 6-5 11-13 15-19h0l5-6z" class="AJ"></path><path d="M699 368c4-2 6-9 10-11 1 0 2-1 3-2h1c-4 6-9 14-15 19-2 2-6 3-9 6v-1c1-1 2-2 4-3 1-1 2-2 3-4s1-2 3-4z" class="U"></path><path d="M706 387h-1-1c0-1 0-1 1-1 2-4 4-7 6-10 1-2 2-3 2-5 1-4 3-7 6-9l1 1h-1v1h0c1 0 1 0 2 1h0l1 1h0c0 1 0 1-1 2h0l-3 3h-2v1c-2 5-7 10-10 15z" class="c"></path><path d="M719 364c1 0 1 0 2 1h0l1 1h0c0 1 0 1-1 2h0l-3 3h-2c1-2 3-4 3-7z" class="AC"></path><path d="M682 408l1 1c2-1 3-5 4-8 1 0 1 0 1-1 1-1 2-2 2-3 2-2 3-3 4-5 0-1 0-1 1-1l1 1c-1 2-2 3-3 5-1 3-3 6-4 9-1 1-2 3-2 5-1 1-2 3-2 5h-1c-1 2-3 3-5 5h1c-1 1-1 0-1 1-2 0-3 2-4 3v2c-1 1-2 1-2 2l-1-1-1 1v-1c1-2 2-3 4-5h-1l-1 1v-1c1-2 3-5 5-7l-1-2h1c1-2 1-3 2-4l2-2z" class="H"></path><path d="M661 381c1 2 1 3 1 5-1 1-1 2-1 2h1 0v3 1l-1 2h0 1c1 0 1 0 2 1l-3 9-11 23c-1 3-1 5-2 7-1 1-1 2-1 4-1 1-2 2-2 4h0c0 2-1 4-2 5l-3 8c-2 3-5 7-5 11-1 2-3 4-4 7-2 5-4 9-7 14l-1-1v-1h-1c-1 2-2 4-3 5h-1l2-3c0-1 0-2 1-3l2-5-4 4c0-1 1-2 1-4h0v-1-1l-1 2-1-1c0-1 2-2 3-3-1-1-2-2-3-2h-1c-1 1-1 2-3 3l-2 2-1-1c-1 2-2 3-3 4-3 1-5 4-7 6 1-2 2-3 3-4 1-2 3-4 4-7 1 0 5-3 5-4 1-2 2-3 3-4l1 1v-2c1-1 1-2 3-4v-1c0-1 0-3 1-5v-1c1-1 1-1 1-2l5-11 3-6 1-2 12-26 10-23 1-1c2-1 2-1 4-1l1-1h1l1-2z" class="AS"></path><path d="M654 385c2-1 2-1 4-1l-2 6v1c-1-2-1-2-3-2 0-1 1-2 1-4z" class="AA"></path><path d="M661 381c1 2 1 3 1 5-1 1-1 2-1 2-2 2-3 6-5 9 0-1 0-2 1-3h0c0-2 0-2 1-3 0-1 1-1 1-2v-1c1-1 1-3 1-5l1-2z" class="AH"></path><path d="M653 389c2 0 2 0 3 2-1 1-2 2-3 4l-3 7c0 2-1 4-2 5v-1c-1 0-1 0-2-1l1-1c0-1 0-2 1-3 2-3 3-8 5-12z" class="AX"></path><defs><linearGradient id="v" x1="630.543" y1="420.645" x2="648.385" y2="418.699" xlink:href="#B"><stop offset="0" stop-color="#886b6c"></stop><stop offset="1" stop-color="#978987"></stop></linearGradient></defs><path fill="url(#v)" d="M653 386l1-1c0 2-1 3-1 4-2 4-3 9-5 12-1 1-1 2-1 3l-1 1c1 1 1 1 2 1v1l-8 18c-2 3-4 6-6 10h-3l12-26 10-23z"></path><defs><linearGradient id="w" x1="650.345" y1="405.211" x2="658.918" y2="410.449" xlink:href="#B"><stop offset="0" stop-color="#5e3d3d"></stop><stop offset="1" stop-color="#785a58"></stop></linearGradient></defs><path fill="url(#w)" d="M661 388h1 0v3 1l-1 2h0 1c1 0 1 0 2 1l-3 9-11 23c-3 3-5 6-6 9l-3 6c-2-1-1 0-2-1 1-3 2-5 3-8 0-1 1-3 1-4l1-2c1-2 1-3 2-4 0-1 1-1 1-2h0v-2c0-2 2-5 3-6l1-1c1-2 2-5 3-8l-1-1 3-6c2-3 3-7 5-9z"></path><path d="M641 442l3-6c1-3 3-6 6-9-1 3-1 5-2 7-1 1-1 2-1 4-1 1-2 2-2 4h0c0 2-1 4-2 5-1 3-2 5-3 8-2 3-5 7-5 11-1 2-3 4-4 7-2 5-4 9-7 14l-1-1v-1h-1c-1 2-2 4-3 5h-1l2-3c0-1 0-2 1-3l2-5-4 4c0-1 1-2 1-4h0v-1-1l-1 2-1-1c0-1 2-2 3-3-1-1-2-2-3-2h-1c-1 1-1 2-3 3l-2 2-1-1c-1 2-2 3-3 4-3 1-5 4-7 6 1-2 2-3 3-4 1-2 3-4 4-7 1 0 5-3 5-4 1-2 2-3 3-4l1 1v-2c1-1 1-2 3-4v-1c0-1 0-3 1-5v-1c1-1 1-1 1-2l5-11 3-6h0 1c1-1 2-1 3-2l3-4v2l-1 1c-1 2-2 3-2 5-1 4-3 7-4 11-1 2-2 6-4 8l1 1c0-1 1-1 1-2 1 0 1-1 2-1l-2 2h2c2-1 2-2 3-4s3-2 4-5l1-1c1-2 1-4 3-6z" class="AV"></path><path d="M622 454c1 0 1 1 2 1v2h-1l1 2-3 3h-1c0-1 0-3 1-5v-1c1-1 1-1 1-2z" class="AW"></path><path d="M618 473l12-10h0c0 1 0 1-1 1 0 1-1 2-2 2l-2 2c-1 1-2 2-2 3h0c0 2-2 3-2 4-1-1-2-2-3-2zm3-11l1 1c2-1 3-2 4-3 2-1 2 0 3 0s4-5 5-6l1 1c-2 4-7 9-11 11-2 1-6 5-7 7-1 1-1 2-3 3l-2 2-1-1c-1 2-2 3-3 4-3 1-5 4-7 6 1-2 2-3 3-4 1-2 3-4 4-7 1 0 5-3 5-4 1-2 2-3 3-4l1 1v-2c1-1 1-2 3-4v-1h1z" class="AY"></path><defs><linearGradient id="x" x1="745.273" y1="317.019" x2="759.251" y2="327.229" xlink:href="#B"><stop offset="0" stop-color="#090807"></stop><stop offset="1" stop-color="#311211"></stop></linearGradient></defs><path fill="url(#x)" d="M774 274c2-1 6-6 7-7h0c-1 2-2 3-3 5h2v2c-1 1-1 2-1 3l2-1v2c-2 2-4 4-5 7h1c0 1 0 1-1 3v2h0v1c-1 3-1 5-1 8 1 1 1 3 2 4v3c0 1-1 2-2 3-3 3-3 7-4 11l-1 1-2 2h-1c-1 0-1 1-2 2h-1c-1 1-1 2-2 2h-1l1 1-1 1 1 1c-1 1-2 3-2 4-1 0-1 1-2 1s-2 0-3-1l-2 1h-1c-2 3-3 5-3 8l-3 6c-1 2-2 3-3 5-1 1-1 2-1 4h-1l-3 6c-1 0-1 1-2 1 0 1 0 1-1 1-2 1-4 4-5 6l-7 8c-1-1-1 0-1-1-2 2-3 4-5 6 0-2 0-3 1-4 0-1 0-1 1-2h0v-2-1-1l-2 1c1-2 1-4 1-5l3-3h0c1-1 1-1 1-2h0l-1-1h0c-1-1-1-1-2-1h0v-1h1l-1-1 1-3 6-6c2-3 3-6 4-10l2-2c1-8 7-15 11-22h-1c1-2 2-3 2-5 2-1 3-4 5-6 6-9 11-18 17-26 3-3 6-5 8-8z"></path><path d="M743 347l2-3h0c1 1 1 1 2 1 1-2 1-3 1-4 1-2 1-3 2-5 1-1 1-1 1-2v-1l1-1v-1c1-2 1-2 2-2l-2 6c-2 3-3 5-3 8l-3 6v-1h-1c-1-1-1-1-2-1z" class="c"></path><path d="M730 343v1c0 1 1 3 1 4-2 3-2 7-4 10h-1c-1-2-1-3 0-5 2-3 3-6 4-10z" class="E"></path><path d="M759 321l5-13c1 2 1 4 1 7v2h-1-1c0 1 0 3-1 3-1 2-1 1-2 3 0-1-1-1-1-2z" class="u"></path><path d="M764 317h1c1 1 1 2 1 4l1 1v1c-1 0-1 1-2 2h-1c-1 1-1 2-2 2-1-1-1-2-2-4 1-2 1-1 2-3 1 0 1-2 1-3h1z" class="w"></path><path d="M760 323c1-2 1-1 2-3 1 0 1-2 1-3h1c0 3 0 5-1 7 0 1 0 1 1 1-1 1-1 2-2 2-1-1-1-2-2-4z" class="n"></path><path d="M726 358h1c0 2-1 3-2 5 0 2-1 4-2 5l-2 7c-1 1-2 2-2 4v-2-1-1l-2 1c1-2 1-4 1-5l3-3h0c1-1 1-1 1-2l4-8z" class="U"></path><path d="M726 353c-1 2-1 3 0 5l-4 8h0l-1-1h0c-1-1-1-1-2-1h0v-1h1l-1-1 1-3 6-6z" class="S"></path><path d="M743 347c1 0 1 0 2 1h1v1c-1 2-2 3-3 5-1 1-1 2-1 4h-1 0c-1 0-2 0-2 1-1 1-2 1-2 2h-1c1-1 1-3 2-4v-2l5-7v-1z" class="AC"></path><path d="M743 348l1 1c-1 4-4 5-6 8v-2l5-7z" class="E"></path><path d="M721 375v1h1c1-1 2-3 3-5 0-1 1-2 1-3 1-1 1-1 1-2 1-2 4-8 6-9 1 1 1 1 2 1h1c-6 6-11 14-14 21-2 2-3 4-5 6 0-2 0-3 1-4 0-1 0-1 1-2h0c0-2 1-3 2-4z" class="AH"></path><path d="M759 321c0 1 1 1 1 2 1 2 1 3 2 4h-1l1 1-1 1 1 1c-1 1-2 3-2 4-1 0-1 1-2 1s-2 0-3-1l-2 1h-1l2-6 5-8z" class="Z"></path><path d="M755 334c2-2 4-5 6-7l1 1-1 1 1 1c-1 1-2 3-2 4-1 0-1 1-2 1s-2 0-3-1z" class="b"></path><path d="M738 355v2c-1 1-1 3-2 4h1c0-1 1-1 2-2 0-1 1-1 2-1h0l-3 6c-1 0-1 1-2 1 0 1 0 1-1 1-2 1-4 4-5 6l-7 8c-1-1-1 0-1-1 3-7 8-15 14-21 0 0 1-2 2-3z" class="AI"></path><path d="M776 285h1c0 1 0 1-1 3v2c-1 2-2 5-3 7s0 3-1 5c-2 2-1 4-2 6 0 2 0 4-1 5h0c-2 0-3 0-3 1l-1 1c0-3 0-5-1-7 4-8 7-16 12-23z" class="Z"></path><path d="M776 290h0v1c-1 3-1 5-1 8 1 1 1 3 2 4v3c0 1-1 2-2 3-3 3-3 7-4 11l-1 1-2 2h-1v-1l-1-1c0-2 0-3-1-4v-2l1-1c0-1 1-1 3-1h0c1-1 1-3 1-5 1-2 0-4 2-6 1-2 0-3 1-5s2-5 3-7z" class="s"></path><path d="M765 315l1-1c0-1 1-1 3-1h0l-2 9-1-1c0-2 0-3-1-4v-2z" class="I"></path><path d="M775 299c1 1 1 3 2 4v3c0 1-1 2-2 3-1-2-1-3-1-4l-1 1h-1c1-3 2-3 2-6l1-1z" class="Q"></path><path d="M774 274c2-1 6-6 7-7h0c-1 2-2 3-3 5h2v2c-1 1-1 2-1 3l-2 1v-1-1c-4 1-19 23-21 27l-8 10c-1 1-4 6-5 6h-1c1-2 2-3 2-5 2-1 3-4 5-6 6-9 11-18 17-26 3-3 6-5 8-8z" class="s"></path><path d="M738 364h0c1-1 3-2 4-3l-2 7 2-1v11 7c0 2 0 3 1 5l1-1v-1h2l-1 3c-2 0-2-1-3 0-1 0-2 0-3 1v1 8c0 2 1 5 1 7v3c-1 1-1 2-1 4h-2v-3l-1 1h-2-3l-2 1-3-1 2-2c-3 0-5 0-7 1v-1c0-1 1-1 2-2v-1-4l-1-1h0l-1-1c-1 0-2 1-3 2-1 2-1 3-3 4l-1-2c-1 4-5 8-8 11l-8 9 1 1v1h0l-2 2 1 1c-6 6-13 14-20 19l-9 8c1-6 2-11 3-16h0v-3l1-2 4-8-1-1v-1h-1v-2c1-1 2-3 4-3 0-1 0 0 1-1h-1c2-2 4-3 5-5v3h-1v1 3l-1 2v1l1-1h1c2-3 2-6 4-9 0-2 2-4 3-7 2-3 4-7 7-10 0-1 2-2 2-3l1-1c1-1 1-2 2-3 0-1 2-3 3-5 3-5 8-10 10-15v-1h2c0 1 0 3-1 5l2-1v1 1 2h0c-1 1-1 1-1 2-1 1-1 2-1 4 2-2 3-4 5-6 0 1 0 0 1 1l7-8c1-2 3-5 5-6 1 0 1 0 1-1 1 0 1-1 2-1z" class="H"></path><path d="M684 434h1c0 1-1 3-2 4v1c-1-1-1-1-1-2s1-2 2-3z" class="s"></path><path d="M716 371h2c0 1 0 3-1 5l-2 4c-2-3 1-5 1-8h0v-1z" class="AH"></path><path d="M730 372l2 2v1l-2-1c-1 0-1 1-2 1-1 2-2 3-3 5 0 1-1 1-2 2-2 1-3 3-4 4-2 1-4 3-6 5l4-6c2-2 3-4 5-6 0 1 0 0 1 1l7-8z" class="b"></path><path d="M683 419v1 3l-1 2v1l1-1h1c-2 5-4 9-6 14-1 2-2 3-2 5 2 1 2 1 2 3h0l-1-1c-1 0-1 0-2-1l1 2h-2c0-1 0-2 1-3 1-2 0-3 1-5 0-3 2-5 2-8h0l1-2c1-3 3-7 4-10z" class="c"></path><path d="M678 431c0 3-2 5-2 8-1 2 0 3-1 5-1 1-1 2-1 3l-1 5h-1v-1c-1 1-1 2-1 4h0c2-1-1 1 2-1 0 0 1-1 1-2h1l3-2h0l-9 8c1-6 2-11 3-16 1-1 1-3 2-3 1-3 2-5 4-8z" class="E"></path><path d="M675 427v-2c1-1 2-3 4-3 0-1 0 0 1-1h-1c2-2 4-3 5-5v3h-1c-1 3-3 7-4 10l-1 2h0c-2 3-3 5-4 8-1 0-1 2-2 3h0v-3l1-2 4-8-1-1v-1h-1z" class="U"></path><path d="M707 413l-1-1c3-4 6-9 9-12 3-4 7-8 9-12 1-1 2-3 4-4l1 1 2-1c-1 3-2 5-3 8l-2 5-1-2c-1 2-1 3-2 4 0 2 0 3-1 4l-1-1c-1 0-2 1-3 2-1 2-1 3-3 4l-1-2c-1 4-5 8-8 11v-3h1v-1z" class="b"></path><path d="M723 393c1-1 2-4 4-5l-2 7c-1 2-1 3-2 4v-2-4z" class="s"></path><path d="M707 413c1-2 3-4 4-6l12-14v4 2c0 2 0 3-1 4l-1-1c-1 0-2 1-3 2-1 2-1 3-3 4l-1-2c-1 4-5 8-8 11v-3h1v-1z" class="U"></path><path d="M714 406c2-4 4-6 6-8 0 2-2 4-2 6h0c-1 2-1 3-3 4l-1-2z" class="H"></path><path d="M738 364h0c1-1 3-2 4-3l-2 7 2-1v11 7c0 2 0 3 1 5l1-1v-1h2l-1 3c-2 0-2-1-3 0-1 0-2 0-3 1v1 8c0 2 1 5 1 7v3c-1 1-1 2-1 4h-2v-3l-1 1h-2-3l-2 1-3-1 2-2c-3 0-5 0-7 1v-1c0-1 1-1 2-2v-1-4l-1-1h0c1-1 1-2 1-4 1-1 1-2 2-4l1 2 2-5 3-8-2 1-1-1 4-9v-1l-2-2c1-2 3-5 5-6 1 0 1 0 1-1 1 0 1-1 2-1z" class="y"></path><path d="M733 374v1c1 0 2 0 3-1v1l-2 4c-1 2-2 3-2 4l-1 1-2 1-1-1 4-9v-1h1z" class="c"></path><path d="M736 394c0 1-1 1-2 2l1 1h-1l-1-1c-1-1-1-4-1-6 1-3 1-5 2-8 1-1 1-1 3-1v1h-2v1h2l-1 1c0 2 1 5 0 8v2z" class="N"></path><path d="M728 392c0 1 0 1 1 2s1 0 0 1c0 1-1 2-1 3l1 2c0 2 1 4 3 5h0v-2c-1-2-1-2-1-4h1c0 1 1 1 2 2 1 0 2-2 3-3 1 2 1 3 1 4v1c-1 0-2 1-3 1h-1c-1 1-2 2-2 3-2-2-4-2-7-2 0 1 0 2-1 3h0-1v-4l-1-1h0c1-1 1-2 1-4 1-1 1-2 2-4l1 2 2-5z" class="T"></path><path d="M725 395l1 2c0 2-2 6-1 8 0 1 0 2-1 3h0-1v-4l-1-1h0c1-1 1-2 1-4 1-1 1-2 2-4z" class="R"></path><path d="M739 393v8c0 2 1 5 1 7v3c-1 1-1 2-1 4h-2v-3l-1 1h-2-3l-2 1-3-1 2-2c-3 0-5 0-7 1v-1c0-1 1-1 2-2v-1h1 0c1-1 1-2 1-3 3 0 5 0 7 2 0-1 1-2 2-3h1c1 0 2-1 3-1v-1c0-1 0-2-1-4h0c1-2 1-3 2-5z" class="o"></path><path d="M739 393v8 4c-1 0-1-2-1-3s0-2-1-4h0c1-2 1-3 2-5z" class="I"></path><path d="M732 407c0-1 1-2 2-3h1c1 0 2-1 3-1 0 0 0 2-1 2 0 1-1 2-1 3h-2l-2-1z" class="x"></path><path d="M728 411h0c1 0 2 0 3-1l3 2v1h-3l-2 1-3-1 2-2z" class="Q"></path><path d="M738 364h0c1-1 3-2 4-3l-2 7 2-1v11 7c0 2 0 3 1 5l1-1v-1h2l-1 3c-2 0-2-1-3 0-1 0-2 0-3 1v1c-1 2-1 3-2 5 0-1 0-3-1-4v-2c1-3 0-6 0-8l1-1h-2v-1h2v-1-1-4h0l-1-1v-1c-1 1-2 1-3 1v-1h-1l-2-2c1-2 3-5 5-6 1 0 1 0 1-1 1 0 1-1 2-1z" class="p"></path><path d="M738 364h0c1-1 3-2 4-3l-2 7c-1 3 0 5-1 9-1 1 0 2-1 3h-1v-4h0l-1-1v-1c-1 1-2 1-3 1v-1h-1l-2-2c1-2 3-5 5-6 1 0 1 0 1-1 1 0 1-1 2-1z" class="H"></path><path d="M733 374c1-1 1-2 3-3h0v3c-1 1-2 1-3 1v-1z" class="AJ"></path><defs><linearGradient id="y" x1="638.473" y1="494.703" x2="661.453" y2="516.725" xlink:href="#B"><stop offset="0" stop-color="#080808"></stop><stop offset="1" stop-color="#1d0d0d"></stop></linearGradient></defs><path fill="url(#y)" d="M718 404c1-1 2-2 3-2l1 1h0l1 1v4 1c-1 1-2 1-2 2v1c2-1 4-1 7-1l-2 2 3 1 2-1h3 2l1-1v3h2c0-2 0-3 1-4v6c1 3 1 4 3 6-1 1-1 3 0 4h2l4-4h1c1 0 1 0 1 2h2c1 0 1 0 2-1v2l1 1c1-1 2-1 4-2l1 2h-1c-1 1-1 2-2 3l1 1c0-1 1-1 1-1h1v1c-1 1 0 1 0 3v1l-2 1-5 4c-2 2-3 4-5 5-1 1-2 1-3 2l1 2h-1c-2 2-6 5-7 7-1 1-2 2-4 3l-1 1v1c-1 2-1 2-3 3 0 0 0 1-1 2h4c-1 0-1 1-2 1s-1 0-1 1c-1 2-3 2-5 4-1 0-4 3-5 3s-3 0-5 1l-1 1h-3c-2 3-5 5-8 6v2l1 2-2 2h-1c0 1-1 1-1 2l-2 2c-1 1 0 1-1 2 0 1-2 3-4 4-2 2-5 5-5 8l-2 5c-1 2-1 2 0 4-1 0-1 1-2 1 0 1-1 2-1 3h-1c-1-1-1 0-2 0l-1 1-1-1h-2l-1 1-1-1-2 2-2-1-7 5c-1 1-2 3-4 4h0c1-2 2-3 3-4v-1-1c1-1 3-2 3-3h-1v-1h-1c0 2-1 3-2 4l-1-1 2-1v-1c-4 4-7 8-11 10l-1-1c-1 0-1-1-1-1-5 5-8 11-12 16l-6 6c-3 3-7 5-10 8l-1 1-1 1c-2 2-6 4-9 5s-5 4-7 6l-1 1c-2 1-3 1-4 3l-1-1-1 1h-1c-2 3-6 5-8 8-2 2-3 5-5 7h1c-1 1-2 2-2 3h-1c-1 1-1 1-1 3l-2 1h0l-2 2c-1 0-1 0-2 1 0 1 0 1-1 2-1 0-1 1-2 2 0 1-1 2-1 3-1 0-1 0-1 1-1 0-1 1-1 2-1 0-1 1-1 2-1 0-1 0-1 1h-1v1c-1 1-1 1-1 2l-1 1h0v1l-3 3v1l-1 1h0l-1 1v1l-1 1h0l-1 1v1l-1 1h0l-1 1v1l-1 1h0l-1 1v1h0c-1 1-4 6-5 7s-1 1-1 2l-1 1-2 4-2 2c-2 2-4 6-7 9 0 3 0 6-1 9l-1 1c0 1 0 2-1 3l-1 4c0 1-1 3-1 4l-1 1c-1 3-1 7-3 9v-8l-1-6h-1c-1 1-2 3-4 4v-1c2-2 3-4 3-6l2-6 1-2c1-2 3-7 4-9l1-2 1-8 1-9v-3l1-8 1-2c1-2 1-3 1-5h-3c4-3 6-7 9-11 1-1 1-2 1-3l1-1c0-1 0-2 1-2v2l1-2 2-2-2 4h1l1-1 1 1h0c2 0 4-2 5-3 0-1 0-1 1-1s2-1 3-2l-1-1h-1c2-2 3-5 4-7 2-2 3-4 4-7l2-1v-5-6c0-2 1-3 1-5v-5c0-3-1-6-3-9 0-1 0-2-1-2l-1 1-2 2c1-2 2-5 1-8 0-1-1-1-2-1-2-1-4 0-7 1h0l-5 4c0-2 1-4 0-6 0-1 1-2 1-4l3-5c0-2 0-3 1-4 1-2 1-3 1-5v-1l1-1v-2h2v-1l1 1c0-2 1-2 0-4h1 0l1 1c0-4 4-9 6-13 1-2 4-6 4-9l1-1c0 1 1 2 2 3h-1c-1 2-1 3-2 5 0 1-1 2-1 3v1l1-1v1c2-1 2-3 3-4l1-1c0-2-1-2 0-3v-2c2 0 2 0 3 1v4c1 0 1 0 1-1 1-1 3-1 5-1l1-1c2 0 3-1 4-2h1 1l2-1 1-1 10-9c2-2 4-5 7-6 1-1 2-2 3-4l1 1 2-2c2-1 2-2 3-3h1c1 0 2 1 3 2-1 1-3 2-3 3l1 1 1-2v1 1h0c0 2-1 3-1 4l4-4-2 5c-1 1-1 2-1 3l-2 3h1c1-1 2-3 3-5h1v1l1 1v1c-1 1-2 2-2 3 0 2 0 3-1 4l1 1 3-5h2v1l-9 24h0c3-2 4-5 6-7s4-4 5-7c2-1 3-3 4-5s3-3 5-5l2-2c1-2 3-4 5-6l1-2c1-1 2-3 3-6l-1-1c1-2 2-5 3-7 1-1 2-3 3-4l1 2c-1 1-1 2-2 4v2l2-1 14-31c1-1 2-3 4-3l-1 2v3h0l-3 16 9-8c7-5 14-13 20-19l-1-1 2-2h0v-1l-1-1 8-9c3-3 7-7 8-11l1 2c2-1 2-2 3-4z"></path><path d="M683 475l4-4 3-1h0v2c-2 2-4 3-5 4l-2-1z" class="b"></path><path d="M685 476h0c-3 3-6 6-10 7a30.44 30.44 0 0 1 8-8l2 1z" class="c"></path><path d="M684 490l6-4c0 1 1 2 1 2l-3 3-2 2c-1-1-2-2-2-3z" class="k"></path><path d="M677 489l9-9c-1 3-2 4-3 5v2c-2 1-4 3-5 4-1-1-1-1-1-2z" class="H"></path><path d="M708 418c1-2 2-4 4-5l1 4-8 8c-1-2 2-3 3-4v-3z" class="s"></path><path d="M699 427l4-4c1-2 3-3 5-5v3c-1 1-4 2-3 4-1 1-2 3-4 3l-3 3-1-1 2-2h0v-1zm-13 53c2-1 4-4 6-5l2 3c-1 1-2 1-3 2-3 2-5 5-8 7v-2c1-1 2-2 3-5z" class="U"></path><path d="M714 406l1 2-3 5c-2 1-3 3-4 5-2 2-4 3-5 5l-4 4-1-1 8-9c3-3 7-7 8-11z" class="b"></path><path d="M575 590c0 3-1 4-2 7l1 1c-2 3-4 8-7 11 0-1 1-2 1-3l1-1h-1l-3 3h0l2-4 2-4c1-1 3-5 4-6l2-4z" class="X"></path><path d="M591 575l1 1-1 2v1c2-1 3-2 4-4l1 1c-2 3-6 5-8 8-2 2-3 5-5 7l-3 3h0c1-2 3-4 4-7 2-4 4-8 7-12z" class="C"></path><path d="M575 590l8-14-1 4c1 0 1 0 2-1l-10 19-1-1c1-3 2-4 2-7z" class="L"></path><path d="M611 523c1-1 2-1 3-2 1-2 2-3 3-5l1 2c-5 6-8 12-11 19h-1v-1h0v-2l3-6 2-5z" class="AA"></path><path d="M646 482c2 0 3-1 4-2 0 1 0 2-1 3 0 1-1 2-1 3-1 1-3 3-4 5l-5 6-1-1 1-1-1-1v-2l2-2c1-2 3-4 5-6l1-2z" class="B"></path><path d="M657 472c4-6 7-12 10-18 1 2 0 5 1 7-1 1-1 2-1 3l-16 18c0-2 3-5 4-6 1-2 2-3 2-4z" class="AX"></path><defs><linearGradient id="z" x1="629.586" y1="496.143" x2="630.92" y2="508.41" xlink:href="#B"><stop offset="0" stop-color="#4c3e39"></stop><stop offset="1" stop-color="#694e4e"></stop></linearGradient></defs><path fill="url(#z)" d="M638 492v2c-2 4-4 9-7 12-4 4-9 7-13 12l-1-2h1 0c3-2 4-5 6-7s4-4 5-7c2-1 3-3 4-5s3-3 5-5z"></path><path d="M674 497c1 0 2-1 3-2 2-2 5-3 7-5 0 1 1 2 2 3-3 3-6 6-9 10h0c-2 0-3 0-4 2l-1-1c0-1 1-2 1-2l1-5z" class="U"></path><path d="M718 404c1-1 2-2 3-2l1 1h0l1 1v4 1c-1 1-2 1-2 2v1c-1 0-1 1-2 1s-1-1-1 0h-1c0 1-1 1-1 1l-3 3-1-4 3-5c2-1 2-2 3-4z" class="W"></path><path d="M722 403l1 1v4 1c-1 1-2 1-2 2v1c-1 0-1 1-2 1s-1-1-1 0h-1c0 1-1 1-1 1 1-2 3-4 4-7l2-4z" class="b"></path><path d="M684 453c3 0 7-6 10-9 1-1 1-2 3-2l-1 3c-3 4-6 6-9 9s-5 7-8 10c-1 1-1 3-1 4l-1 1h-1c0-1 1-2 1-3h-1c-1 1-2 3-3 4h-1c1-2 3-5 5-6 3-3 3-8 7-11z" class="E"></path><path d="M654 464l1 2c-1 1-1 2-2 4v2l2-1c-1 1-2 2-2 4 2-2 2-3 4-3 0 1-1 2-2 4-1 1-4 4-4 6-1 2-1 3-3 4 0-1 1-2 1-3 1-1 1-2 1-3-1 1-2 2-4 2 1-1 2-3 3-6l-1-1c1-2 2-5 3-7 1-1 2-3 3-4z" class="AJ"></path><path d="M649 476l1-1h1v1h2v1l-2 3h-1c-1 1-2 2-4 2 1-1 2-3 3-6z" class="a"></path><path d="M696 463c1-3 3-6 5-10v4c-1 1-1 2-1 3v2c-2 3-1 5-2 8h1c1 1 3 2 5 2 1 0 3-1 4-2l1 1h-1c-1 0-1 1-2 1-1 1-2 1-2 1-1 1-1 1-1 2h0c-1 0-2 1-3 0-2 0-4-1-5-3-1-3 0-6 1-9z" class="AQ"></path><path d="M704 473c-2 1-2 1-3 1-2-1-3-2-3-3h0l-1-1h0 1 1c1 1 3 2 5 2 1 0 3-1 4-2l1 1h-1c-1 0-1 1-2 1-1 1-2 1-2 1z" class="V"></path><path d="M698 431l3-3-34 36c0-1 0-2 1-3-1-2 0-5-1-7l5-12h0l-3 16 9-8c7-5 14-13 20-19z" class="AA"></path><path d="M706 477c2-1 4-3 7-3l-1 1 1 1-1 1c-2 3-5 5-8 6l-7 5 1-2h-1v-1l-6 3s-1-1-1-2l4-3c3-3 8-5 12-6z" class="m"></path><path d="M712 475l1 1-1 1c-2 3-5 5-8 6l-7 5 1-2h-1v-1l15-10z" class="R"></path><path d="M684 453c6-6 11-13 17-19 3-2 5-5 8-6 1-1 2-2 4-3l-1 2-2 6c-1 2-1 3-2 4h-1l-1 1v-1h-1v-1c-1 0-2 1-3 1l-2 4-4 4 1-3c-2 0-2 1-3 2-3 3-7 9-10 9z" class="c"></path><path d="M702 437c2-2 5-4 6-6l4-4-2 6c-1 2-1 3-2 4h-1l-1 1v-1h-1v-1c-1 0-2 1-3 1z" class="b"></path><path d="M605 550v4l-1 1-2 5v1c1-1 3-4 5-5v1l-4 6v1 1l1 1c-1 3-5 5-7 7l-2 2c-1 2-2 3-4 4v-1l1-2-1-1 11-22c1-2 2-2 3-3z" class="a"></path><path d="M592 576c2-2 3-5 5-7h1l-1 4-2 2c-1 2-2 3-4 4v-1l1-2z" class="AJ"></path><path d="M603 563v1 1l1 1c-1 3-5 5-7 7l1-4h0c2-1 4-4 5-6z" class="AC"></path><defs><linearGradient id="AA" x1="587.633" y1="550.405" x2="599.833" y2="564.556" xlink:href="#B"><stop offset="0" stop-color="#765a5c"></stop><stop offset="1" stop-color="#988b87"></stop></linearGradient></defs><path fill="url(#AA)" d="M606 534v2h0v1h1l-23 42c-1 1-1 1-2 1l1-4c0-1 2-5 3-6l13-24c1-2 2-4 2-5 2-2 3-5 5-7z"></path><defs><linearGradient id="AB" x1="666.459" y1="468.966" x2="661.778" y2="438.574" xlink:href="#B"><stop offset="0" stop-color="#060907"></stop><stop offset="1" stop-color="#2b1818"></stop></linearGradient></defs><path fill="url(#AB)" d="M669 440c1-1 2-3 4-3l-1 2v3l-5 12c-3 6-6 12-10 18-2 0-2 1-4 3 0-2 1-3 2-4l14-31z"></path><defs><linearGradient id="AC" x1="542.022" y1="627.368" x2="561.835" y2="632.219" xlink:href="#B"><stop offset="0" stop-color="#6f6865"></stop><stop offset="1" stop-color="#888786"></stop></linearGradient></defs><path fill="url(#AC)" d="M565 608h0l3-3h1l-1 1c0 1-1 2-1 3-2 5-5 9-8 13-6 9-13 19-18 29-2 2-4 6-7 9-1-7 6-15 10-20l7-12c3-2 5-7 7-10l7-10z"></path><path d="M731 460h1l-1 2 3-2v1c-1 2-1 2-3 3 0 0 0 1-1 2h4c-1 0-1 1-2 1s-1 0-1 1c-1 2-3 2-5 4-1 0-4 3-5 3s-3 0-5 1l-1 1h-3l1-1-1-1 1-1c-3 0-5 2-7 3v-1l1-1-1-1c-1 0-2 1-3 1h0c0-1 0-1 1-2 0 0 1 0 2-1 1 0 1-1 2-1h1l-1-1c1-1 3-1 4-1 2-1 3-3 5-3 3-1 7-6 11-5 1 0 1-1 3-1z" class="o"></path><path d="M725 466h1c0 1 0 1-1 2s-1 1-1 2c-1 1-1 1-2 1h-1-2c-2 0-2 0-4-1l10-4z" class="s"></path><path d="M734 460v1c-1 2-1 2-3 3 0 0 0 1-1 2-2 1-4 4-6 4 0-1 0-1 1-2s1-1 1-2h-1 0c2-1 4-3 6-4l3-2z" class="W"></path><path d="M730 466h4c-1 0-1 1-2 1s-1 0-1 1c-1 2-3 2-5 4-1 0-4 3-5 3s-3 0-5 1l6-5c1 0 1 0 2-1 2 0 4-3 6-4z" class="y"></path><path d="M706 474l9-4c2 1 2 1 4 1h2 1l-6 5-1 1h-3l1-1-1-1 1-1c-3 0-5 2-7 3v-1l1-1-1-1z" class="AT"></path><path d="M539 631c-1 4-2 9 0 13 0-1 1-1 1-1 3-5 6-9 9-13l2-2h0l-7 12c-4 5-11 13-10 20 0 3 0 6-1 9l-1 1c0 1 0 2-1 3l-1 4c0 1-1 3-1 4l-1 1c-1 3-1 7-3 9v-8l-1-6h-1c-1 1-2 3-4 4v-1c2-2 3-4 3-6l2-6 1-2c1-2 3-7 4-9l1-2 1-8 2 1 1-1v-2-2c1-2 1-4 2-6 0-2 1-3 2-4v-2h1z" class="Y"></path><path d="M531 647l2 1-1 14-2 1 2-3c-1-2-1-2-3-3l1-2 1-8z" class="q"></path><path d="M532 662c-1 7-3 13-4 20-1 3-1 7-3 9v-8l-1-6h1l2-1c0-4 3-9 3-13l2-1z" class="f"></path><path d="M527 676c0 2 0 5-2 7l-1-6h1l2-1z" class="X"></path><path d="M529 657c2 1 2 1 3 3l-2 3c0 4-3 9-3 13l-2 1h-1-1c-1 1-2 3-4 4v-1c2-2 3-4 3-6l2-6 1-2c1-2 3-7 4-9z" class="L"></path><path d="M562 606c1 0 1-1 2-2h3l-2 4-7 10c-2 3-4 8-7 10h0l-2 2c-3 4-6 8-9 13 0 0-1 0-1 1-2-4-1-9 0-13h0l2-10 1 3v1 2h0l3-3v-1l1-1v-3h0l-1-2c3-4 5-6 9-9l2-2h1v1h1 1v1h1c0-1 1-1 2-2z" class="K"></path><path d="M562 606c1 0 1-1 2-2h3l-2 4-7 10h-1c1-3 3-6 4-9l-5 5v-1-1l4-4c0-1 1-1 2-2z" class="d"></path><path d="M702 437c1 0 2-1 3-1v1h1v1l1-1h1l-5 12-2 4c-2 4-4 7-5 10l-1-2h-1c-2 2-3 3-6 5-5 4-11 9-14 15l-7 8-1-1 11-14 1-2s2-1 2-2v-1c0-1 2-2 2-2-1 0-2 1-3 1h-1c0-1 0-3 1-4 3-3 5-7 8-10s6-5 9-9l4-4 2-4z" class="H"></path><path d="M702 437c1 0 2-1 3-1v1h1v1l-3 4c0-1 0-2-1-3l-2 2 2-4z" class="R"></path><path d="M706 438l1-1h1l-5 12-2 4c-2 4-4 7-5 10l-1-2h-1c1-1 2-3 2-4 3-5 5-10 7-15l3-4z" class="AC"></path><path d="M678 468l1-1h1c2-2 4-5 7-6l3-3c1 1 2 0 4-1l1 1c-3 3-7 8-11 10-3 2-5 4-7 6l1-2s2-1 2-2v-1c0-1 2-2 2-2-1 0-2 1-3 1h-1z" class="c"></path><path d="M566 583l3-4v1c1 2 0 4-1 6h1 0 1l1-1c0 1 0 2 1 3 0 2-1 3-2 4h1v2h2c-1 1-3 5-4 6l-2 4h-3c-1 1-1 2-2 2-1 1-2 1-2 2h-1v-1h-1-1v-1h-1l-2 2c-4 3-6 5-9 9l1 2h0v3l-1 1v1l-3 3h0v-2-1l-1-3-2 10h0-1v2c-1 1-2 2-2 4-1 2-1 4-2 6v2 2l-1 1-2-1 1-9v-3l1-8 1-2c1-2 1-3 1-5h-3c4-3 6-7 9-11 1-1 1-2 1-3l1-1c0-1 0-2 1-2v2l1-2 2-2-2 4h1l1-1 1 1h0c2 0 4-2 5-3 0-1 0-1 1-1s2-1 3-2l-1-1h-1c2-2 3-5 4-7 2-2 3-4 4-7l2-1h1z" class="AG"></path><path d="M535 620l3-4h0l-1 5-1 4-1-1v1 1l-1-1c1-2 1-3 1-5z" class="AX"></path><path d="M538 616c1 0 3-3 4-4h1l2-1v1c-1 1-2 2-2 3v1c-3 1-4 3-6 5l1-5z" class="AU"></path><path d="M566 583l3-4v1c1 2 0 4-1 6l-2 2h0c-1 0-1 1-2 2 0 0-1 0-1 1-1 2-3 3-4 4 1-2 1-3 2-4 2-3 4-5 5-8z" class="AA"></path><path d="M537 621c2-2 3-4 6-5 0 2 0 3-1 5h-1l-2 10h0-1v2c-1 1-2 2-2 4-1 2-1 4-2 6v2 2l-1 1-2-1 1-9v-3l1-8 1-2 1 1v-1-1l1 1 1-4z" class="C"></path><path d="M534 625l1 1v-1-1l1 1c-2 7-2 15-2 22l-1 1-2-1 1-9v-3l1-8 1-2z" class="f"></path><path d="M571 585c0 1 0 2 1 3 0 2-1 3-2 4h1v2h2c-1 1-3 5-4 6l-2 4h-3c-1 1-1 2-2 2-1 1-2 1-2 2h-1v-1h-1-1v-1h-1v-1l-2 2-1 1h-1c1-3 4-5 5-8h0c0-2 1-3 2-5 1-1 3-2 4-4 0-1 1-1 1-1 1-1 1-2 2-2h0l2-2h1 0 1l1-1z" class="AV"></path><path d="M562 606c2-3 3-5 6-7l1 1-2 4h-3c-1 1-1 2-2 2z" class="AU"></path><path d="M568 586h1c-1 2-3 4-4 6-1 3-1 5-2 8l-1 1h0c0 2 0 2-1 3v-1-3l1-2c1-1 1-1 1-2-1 1-1 1-2 1h-1l-3 3c0-2 1-3 2-5 1-1 3-2 4-4 0-1 1-1 1-1 1-1 1-2 2-2h0l2-2z" class="AY"></path><path d="M559 595c1-1 3-2 4-4 0-1 1-1 1-1 1-1 1-2 2-2h0c-1 3-4 6-6 9l-3 3c0-2 1-3 2-5z" class="AX"></path><path d="M571 585c0 1 0 2 1 3 0 2-1 3-2 4h1v2l-3 4v-1c-2 0-3 2-5 3h0c1-3 1-5 2-8 1-2 3-4 4-6h0 1l1-1z" class="K"></path><path d="M638 511h0c3-2 4-4 6-6 0 4-2 6-3 9l-1 1-5 14c-2 4-2 9-4 12v1c-1 1-1 5-2 6h2c-1 1-1 1-2 1 2 0 2 0 3-1l1 1c-3 3-5 3-8 5-5 4-10 8-14 12-3 1-5 4-7 6l-1 1c-2 1-3 1-4 3l-1-1-1 1h-1l-1-1 2-2c2-2 6-4 7-7l-1-1v-1-1l4-6v-1c-2 1-4 4-5 5v-1l2-5 1-1v-4l7-11c4-8 11-24 20-27 2-1 3-2 5-3l1 2z" class="c"></path><path d="M638 511h0c3-2 4-4 6-6 0 4-2 6-3 9l-1 1c-2 1-2 4-3 7-4 10-7 27-17 33 0-3 4-6 5-9 2-5 5-10 6-15 1-3 2-5 3-8 1-2 1-4 2-6h0c0-1-1-2-1-3h0l-1 1c-3 1-4 2-6 4h-1c2-2 2-4 4-5 1 0 2-1 3-1l1-1h2v5c1-2 1-4 2-5l1-1c-1 1-1 0-2 1v-1z" class="E"></path><path d="M620 555c10-6 13-23 17-33 1-3 1-6 3-7l-5 14c-2 4-2 9-4 12v1c-1 1-1 5-2 6h2c-1 1-1 1-2 1 2 0 2 0 3-1l1 1c-3 3-5 3-8 5-5 4-10 8-14 12-3 1-5 4-7 6l-1 1c-2 1-3 1-4 3l-1-1-1 1h-1l-1-1 2-2c2-2 6-4 7-7 1-1 1-2 2-3l13-7 1-1z" class="U"></path><path d="M697 488l7-5v2l1 2-2 2h-1c0 1-1 1-1 2l-2 2c-1 1 0 1-1 2 0 1-2 3-4 4-2 2-5 5-5 8l-2 5c-1 2-1 2 0 4-1 0-1 1-2 1 0 1-1 2-1 3h-1c-1-1-1 0-2 0l-1 1-1-1h-2l-1 1-1-1-2 2-2-1-7 5c-1 1-2 3-4 4h0c1-2 2-3 3-4v-1-1c1-1 3-2 3-3h-1v-1h-1c0 2-1 3-2 4l-1-1 2-1v-1c-4 4-7 8-11 10l-1-1c-1 0-1-1-1-1-5 5-8 11-12 16l-6 6c-3 3-7 5-10 8l-1 1-1 1c-2 2-6 4-9 5 4-4 9-8 14-12 3-2 5-2 8-5l-1-1c-1 1-1 1-3 1 1 0 1 0 2-1h-2c1-1 1-5 2-6v-1 4c2-2 3-4 3-6 3-5 4-10 7-15 1-2 2-3 3-5 2-3 6-5 9-8 1-1 2-3 4-4 6-7 13-12 20-18 0 1 0 1 1 2l-18 18 1 1c4-4 8-9 13-13l-1 5s-1 1-1 2l1 1c1-2 2-2 4-2h0c3-4 6-7 9-10l2-2 3-3 6-3v1h1l-1 2z" class="c"></path><path d="M661 520h1v1c-3 3-7 6-10 9h-1c2-5 6-7 10-10zm-1-11l1 1-6 7c1 1 1 2 1 2l-1 1c-3 1-4 3-6 5 2-6 6-12 11-16z" class="E"></path><defs><linearGradient id="AD" x1="674.994" y1="504.491" x2="682.004" y2="511.509" xlink:href="#B"><stop offset="0" stop-color="#130706"></stop><stop offset="1" stop-color="#270e0e"></stop></linearGradient></defs><path fill="url(#AD)" d="M688 491c2-1 3-1 4-1l-1 1-1 2c-3 3-5 6-8 9-3 5-5 13-7 18l-2 2-2-1-7 5c-1 1-2 3-4 4h0c1-2 2-3 3-4v-1h1c2-1 3-5 5-7 1-2 3-3 4-5v-1l-6 6h-1l11-15c3-4 6-7 9-10l2-2z"></path><path d="M697 488l7-5v2l1 2-2 2h-1c0 1-1 1-1 2l-2 2c-1 1 0 1-1 2 0 1-2 3-4 4-2 2-5 5-5 8l-2 5c-1 2-1 2 0 4-1 0-1 1-2 1 0 1-1 2-1 3h-1c-1-1-1 0-2 0l-1 1-1-1h-2l-1 1-1-1c2-5 4-13 7-18 3-3 5-6 8-9l1-2 1-1c-1 0-2 0-4 1l3-3 6-3v1h1l-1 2z" class="AQ"></path><path d="M697 485v1h1l-1 2-7 5 1-2 1-1c-1 0-2 0-4 1l3-3 6-3z" class="c"></path><path d="M740 411v6c1 3 1 4 3 6-1 1-1 3 0 4h2l4-4h1c1 0 1 0 1 2h2c1 0 1 0 2-1v2l1 1c1-1 2-1 4-2l1 2h-1c-1 1-1 2-2 3l1 1c0-1 1-1 1-1h1v1c-1 1 0 1 0 3v1l-2 1-5 4c-2 2-3 4-5 5-1 1-2 1-3 2l1 2h-1c-2 2-6 5-7 7-1 1-2 2-4 3l-1 1-3 2 1-2h-1c-2 0-2 1-3 1-4-1-8 4-11 5-2 0-3 2-5 3-1 0-3 0-4 1s-3 2-4 2c-2 0-4-1-5-2h-1c1-3 0-5 2-8v-2c0-1 0-2 1-3v-4l2-4 5-12c1-1 1-2 2-4l2-6 1-2c1-2 3-4 4-6 3-2 6-5 9-6l3 1 2-1h3 2l1-1v3h2c0-2 0-3 1-4z" class="V"></path><path d="M717 419v4l-2 3c0 1-1 2-3 4 0 0 0 1-1 2l-1 1 2-6 1-2c1-2 3-4 4-6z" class="k"></path><path d="M726 413l3 1-2 2c-1 1-2 2-2 3-1 1 0 1-1 2s-1 1-1 2h-1l-1-1h0c-1 0-1 0-2 1 0 1-1 2-2 3l-1-1c1-1 1-1 1-2h0v-4c3-2 6-5 9-6z" class="AQ"></path><path d="M722 423h1c0-1 0-1 1-2s0-1 1-2c0-1 1-2 2-3 1 2 1 2 1 4-2 2-2 4-3 6s-2 4-1 7l-2 4-2 1c-1-1-1-2-2-3v-1-2c-1 1-1 1-1 2-1 3 0 6-1 9-1-3-1-6 0-9v-2c1-1 1-1 1-2l1-1c0-1 1-2 1-3l3-3z" class="T"></path><path d="M707 451c0-1 0-2 1-3h0v-1l3-3c1 1 1 2 1 2v1l1-1-1-1 1-2h0v5c1 1 1 5 2 7v1c1-2 1-5 1-7 1 2 0 5 0 8h-1c-1 0-1 0-1-1v-2c-2 1-2 2-2 4-1 2-3 4-5 6h0c-2 1-3 2-5 4v1h-2l-1 1h-1c1-3 0-5 2-8v-2c0-1 0-2 1-3v-4l2-4 2 2h2z" class="I"></path><path d="M703 449l2 2h2c0 2 0 3 1 5-1 0-2 1-2 2 0 2-1 3-2 6v1l3-1c-2 1-3 2-5 4v1h-2l-1 1h-1c1-3 0-5 2-8v-2c0-1 0-2 1-3v-4l2-4z" class="y"></path><path d="M700 460c2-1 2-1 2-4h0l2 1v3 2 1c-1 1-1 1-1 2-1-1-1-3-1-4v-2c0 1 0 2-1 2l-1 1v-2z" class="x"></path><path d="M703 449l2 2-1 6h0l-2-1h0c0 3 0 3-2 4 0-1 0-2 1-3v-4l2-4z" class="I"></path><path d="M707 451c0 2 0 3 1 5-1 0-2 1-2 2 0 2-1 3-2 6v1h-1 0c0-1 0-1 1-2v-1-2-3h0l1-6h2z" class="m"></path><path d="M716 443c1-3 0-6 1-9 0-1 0-1 1-2v2 1c1 1 1 2 2 3l2-1 2-4c0 2 0 5 2 7v1h3c1 0 1 1 3 0l-1-2c1-1 1-1 2-1v5 2l-1 1 1 1h0c-2 1-3 2-4 3v1c1 0 1 0 2 1-1 1-2 4-2 6 0 1 0 1 2 2-2 0-2 1-3 1-4-1-8 4-11 5-2 0-3 2-5 3-1 0-3 0-4 1s-3 2-4 2c-2 0-4-1-5-2l1-1h2v-1c2-2 3-3 5-4h0c2-2 4-4 5-6 0-2 0-3 2-4v2c0 1 0 1 1 1h1c0-3 1-6 0-8v-6z" class="p"></path><path d="M729 441c1 0 1 1 3 0l-1-2c1-1 1-1 2-1v5 2l-1 1 1 1h0c-2 1-3 2-4 3 0-1-1-2-1-2l1-2h0-1l-1-1c0-2-1-3-1-4h3z" class="l"></path><path d="M740 411v6c1 3 1 4 3 6-1 1-1 3 0 4h2l4-4h1c1 0 1 0 1 2h2c1 0 1 0 2-1v2l1 1c1-1 2-1 4-2l1 2h-1c-1 1-1 2-2 3l1 1c0-1 1-1 1-1h1v1c-1 1 0 1 0 3v1l-2 1-5 4c-2 2-3 4-5 5-1 1-2 1-3 2l1 2h-1c-2 2-6 5-7 7-1 1-2 2-4 3l-1 1-3 2 1-2h-1c-2-1-2-1-2-2 0-2 1-5 2-6-1-1-1-1-2-1v-1c1-1 2-2 4-3h0l-1-1 1-1v-2-5c-1 0-1 0-2 1l1 2c-2 1-2 0-3 0h-3v-1c-2-2-2-5-2-7-1-3 0-5 1-7s1-4 3-6c0-2 0-2-1-4l2-2 2-1h3 2l1-1v3h2c0-2 0-3 1-4z" class="y"></path><path d="M735 432v-3c1 0 1 1 2 2v5c1 2 0 4 1 6l-1 2h1c-1 1-2 3-2 4-1 0-1 1-1 1h-1l-1-2h0l-1-1 1-1v-2-5l1-1-1-1c0-2 1-2 2-4z" class="p"></path><path d="M735 432v-3c1 0 1 1 2 2v5c1 2 0 4 1 6l-1 2h1c-1 1-2 3-2 4l-1-2c2-3 1-7 1-10l-1-4z" class="l"></path><path d="M736 448c0-1 1-3 2-4 0 2 0 4 1 6v1-3-2h1c0 1 1 1 2 2h1l3-1 1 2h-1c-2 2-6 5-7 7-1 1-2 2-4 3l-1 1-3 2 1-2h-1c-2-1-2-1-2-2 0-2 1-5 2-6-1-1-1-1-2-1v-1c1-1 2-2 4-3l1 2h1s0-1 1-1z" class="h"></path><path d="M739 451v-3-2h1c0 1 1 1 2 2h1l3-1 1 2h-1c-2 2-6 5-7 7-1 1-2 2-4 3l-1 1-3 2 1-2h-1c-2-1-2-1-2-2h2s1 0 2-1h0c1 0 2 0 3-1 0-1 0-2 1-3l1 1c0-1 0-2 1-3z" class="l"></path><path d="M745 427l4-4h1c1 0 1 0 1 2h2c1 0 1 0 2-1v2l1 1c1-1 2-1 4-2l1 2h-1c-1 1-1 2-2 3l1 1c0-1 1-1 1-1h1v1c-1 1 0 1 0 3v1l-2 1-5 4c-2 2-3 4-5 5-1 1-2 1-3 2l-3 1h-1v-6-4c1-3 0-4 0-7h1l1-1-1-1c1 0 2-1 2-2z" class="AE"></path><path d="M742 448v-6-4c1-3 0-4 0-7h1l1 1c1-1 1-1 2-1l1 1v2c2 1 2 1 3 1 1-2 0-2 2-3l1 1v1l-1 1-2 2v1 1c1 0 1 0 1 1 1 0 1-1 2-1v1h1c-2 2-3 4-5 5-1 1-2 1-3 2l-3 1h-1z" class="AK"></path><path d="M740 411v6c0 3 0 5-2 7-1 1-1 3-1 4v3c-1-1-1-2-2-2v3c-1 2-2 2-2 4l1 1-1 1c-1 0-1 0-2 1l1 2c-2 1-2 0-3 0h-3v-1c-2-2-2-5-2-7-1-3 0-5 1-7s1-4 3-6c0-2 0-2-1-4l2-2 2-1h3 2l1-1v3h2c0-2 0-3 1-4z" class="AM"></path><path d="M740 411v6c0 3 0 5-2 7-1-3-1-6-1-9h2c0-2 0-3 1-4z" class="T"></path><path d="M728 420c0 1 1 2 1 3v1 6 1l-2-2c0-1-1-2-2-3 1-2 1-4 3-6z" class="AE"></path><path d="M734 413h2l1-1v3h0c-2 2-2 3-2 5v2 6h-1v-4-1h-1l1-1h0c0-3 1-4-1-6h-1-1v-3h3z" class="p"></path><path d="M725 426c1 1 2 2 2 3l2 2c0 2 1 4 0 6h0c1 2 0 3 0 4h-3v-1c-2-2-2-5-2-7-1-3 0-5 1-7z" class="v"></path><defs><linearGradient id="AE" x1="633.086" y1="491.642" x2="558.158" y2="575.128" xlink:href="#B"><stop offset="0" stop-color="#593535"></stop><stop offset="1" stop-color="#735150"></stop></linearGradient></defs><path fill="url(#AE)" d="M618 473c1 0 2 1 3 2-1 1-3 2-3 3l1 1 1-2v1 1h0c0 2-1 3-1 4l4-4-2 5c-1 1-1 2-1 3l-2 3h1c1-1 2-3 3-5h1v1l1 1v1c-1 1-2 2-2 3 0 2 0 3-1 4l1 1 3-5h2v1l-9 24h-1c-1 2-2 3-3 5-1 1-2 1-3 2l-2 5-3 6c-2 2-3 5-5 7 0 1-1 3-2 5l-13 24c-1 1-3 5-3 6l-8 14-2 4h-2v-2h-1c1-1 2-2 2-4-1-1-1-2-1-3l-1 1h-1 0-1c1-2 2-4 1-6v-1l-3 4h-1v-5-6c0-2 1-3 1-5v-5c0-3-1-6-3-9 0-1 0-2-1-2l-1 1-2 2c1-2 2-5 1-8 0-1-1-1-2-1-2-1-4 0-7 1h0l-5 4c0-2 1-4 0-6 0-1 1-2 1-4l3-5c0-2 0-3 1-4 1-2 1-3 1-5v-1l1-1v-2h2v-1l1 1c0-2 1-2 0-4h1 0l1 1c0-4 4-9 6-13 1-2 4-6 4-9l1-1c0 1 1 2 2 3h-1c-1 2-1 3-2 5 0 1-1 2-1 3v1l1-1v1c2-1 2-3 3-4l1-1c0-2-1-2 0-3v-2c2 0 2 0 3 1v4c1 0 1 0 1-1 1-1 3-1 5-1l1-1c2 0 3-1 4-2h1 1l2-1 1-1 10-9c2-2 4-5 7-6 1-1 2-2 3-4l1 1 2-2c2-1 2-2 3-3h1z"></path><path d="M603 515c1 0 1 0 1 1l-1 3h0l-2-3 2-1z" class="AS"></path><path d="M604 498h1c1-1 1-2 2-3h0v3c-1 1-3 3-4 5 0 1 0 1-1 2v-1h-1c-1 1 0 1-1 1 2-2 3-4 4-7z" class="AW"></path><path d="M602 506v1l-1 1v1c-1 0-1 0-2 1-1 2-3 4-4 7v1l-2 2-1-1c1-1 1-3 2-4h0l1-1 7-8z" class="AS"></path><path d="M599 521h2l-1 2c0 1 0 1-1 2v1 1c-1 1-1 2-2 3l-1 2h-1v-3c1-1 1-2 1-3h1 0l1-2c0-1 0-1 1-3zm19-24l1-1c0-2 2-3 3-5 0 2 0 3-1 4l-3 6c-1 2-1 4-1 6h-1c-1 1-1 1-2 1 1-2 1-2 1-3 1-1 1-1 1-2s0-1 1-2h0v-1c0-1 1-2 1-3h0z" class="AH"></path><path d="M618 490h1c1-1 2-3 3-5h1v1l1 1v1c-1 1-2 2-2 3-1 2-3 3-3 5l-1 1v-1c0-1 0-2 1-3v-2l-2 2v1h0v-2s1-1 1-2z" class="AS"></path><path d="M576 573c1 3 0 4-1 7l-3 8c-1-1-1-2-1-3l2-8 1 1c0-2 1-3 2-5h0z" class="AY"></path><path d="M602 497h1l1 1h0c-1 3-2 5-4 7-2 1-2 2-3 4-1 1-3 2-4 3v3l-1-1h-1c3-6 7-11 11-17z" class="AS"></path><path d="M577 561h0l1 1h0l2 1 1-1h0c-1 2-2 5-3 7-1 1-1 2-2 4h0c-1 2-2 3-2 5l-1-1v-5c1-3 1-5 2-7 0-1 0-1-1-2l1-1 2-1z" class="AW"></path><path d="M577 561h0l1 1h0l2 1-6 10 3-12z" class="AV"></path><path d="M571 592c1 0 2-4 2-5h0c3-2 3-7 5-9l3-4h0l-1 2c-1 2-2 5-3 8-1 1-1 0 0 2 0-1 0-2 1-2v-1c1-1 1-2 2-3l1-2v-1l1-1v-1s1-1 1-2l1-1 1-1v-1l1-2v-1l1-1h-1c1-1 1-2 2-3l1-2c1-1 1-2 2-3l1-2c1-3 3-6 4-8 2-2 2-5 4-7h1c0 1-1 3-2 5l-13 24c-1 1-3 5-3 6l-8 14-2 4h-2v-2z" class="AH"></path><defs><linearGradient id="AF" x1="616.492" y1="501.36" x2="620.22" y2="515.082" xlink:href="#B"><stop offset="0" stop-color="#704e4c"></stop><stop offset="1" stop-color="#826664"></stop></linearGradient></defs><path fill="url(#AF)" d="M625 491h2v1l-9 24h-1c-1 2-2 3-3 5-1 1-2 1-3 2 2-4 4-8 5-12 0-1 0-2 1-4 0-2 0-4 1-6l3-6 1 1 3-5z"></path><path d="M621 495l1 1-6 15c0-1 0-2 1-4 0-2 0-4 1-6l3-6z" class="AC"></path><path d="M618 473c1 0 2 1 3 2-1 1-3 2-3 3h-1c-5 6-11 12-15 19-4 6-8 11-11 17-3 4-4 8-6 12l-9 28v-4c0-2 0-3 1-5l8-24 1-1c1-1 1-2 1-3v-1c0-1 1-3 1-4l2-4c5-8 12-17 19-24v-1c-1-2 2-4 3-5l2-2c2-1 2-2 3-3h1z" class="C"></path><path d="M618 473c1 0 2 1 3 2-1 1-3 2-3 3h-1v-1c-2 0-6 5-8 7v-1c-1-2 2-4 3-5l2-2c2-1 2-2 3-3h1z" class="AH"></path><path d="M576 530c1 0 4-4 4-5 2-2 3-3 5-4l-8 24c-1 2-1 3-1 5v4 2c0 1 0 2-1 3v3l-1 1c1 1 1 1 1 2-1 2-1 4-2 7v5l-2 8-1 1h-1 0-1c1-2 2-4 1-6v-1l-3 4h-1v-5-6c0-2 1-3 1-5v-5c0-3-1-6-3-9 0-1 0-2-1-2l-1 1-2 2c1-2 2-5 1-8 0-1-1-1-2-1-2-1-4 0-7 1v-1c2-2 6-4 9-5l1-1c1 0 3-2 4-3s2-1 4-2v-1c1 0 2-2 3-3h3 1z" class="AV"></path><g class="AS"><path d="M575 535h2c0 1-1 1-1 2l-3 4c1 0 1 1 2 2l-1 1-1-2c-1 1-3 3-3 4s1 3 0 4v-2c0-1-1-1-1-2v-1l2-2h0v-1h-1l-2 2v2 3h0l-1-4c0-1 2-3 3-4s2 0 3 0v-1h-1v-1c1-3 1-3 3-4z"></path><path d="M567 540v5c0 2 0 4 1 6l3 9-1 1h0c0 1 0 2 1 3l-1 2-1-2v-1c0-6-4-15-8-19l-1-1 1-1c1 1 2 1 3 2h0c1-1 2-2 3-4z"></path></g><path d="M560 546l1-2c4 4 8 13 8 19v1c-1 0-1 1-2 1l-1-3c0-3-1-6-3-9 0-1 0-2-1-2l-1 1-2 2c1-2 2-5 1-8z" class="a"></path><path d="M572 530h3 1l-9 10c-1 2-2 3-3 4h0c-1-1-2-1-3-2l-1 1 1 1-1 2c0-1-1-1-2-1-2-1-4 0-7 1v-1c2-2 6-4 9-5l1-1c1 0 3-2 4-3s2-1 4-2v-1c1 0 2-2 3-3z" class="AJ"></path><path d="M569 534c-1 2-3 5-6 6l-1 1c-1 0-1 0-1 1l-1 1 1 1-1 2c0-1-1-1-2-1-2-1-4 0-7 1v-1c2-2 6-4 9-5l1-1c1 0 3-2 4-3s2-1 4-2z" class="c"></path><path d="M566 562l1 3c1 0 1-1 2-1l1 2c0 1 0 2 1 4h0v4c1 2-1 9-2 12h0-1c1-2 2-4 1-6v-1l-3 4h-1v-5-6c0-2 1-3 1-5v-5z" class="AH"></path><path d="M567 565c1 0 1-1 2-1l1 2c0 1 0 2 1 4h0c-1 2 0 4-1 5h-1l-2-10z" class="C"></path><defs><linearGradient id="AG" x1="557.633" y1="504.551" x2="570.071" y2="532.884" xlink:href="#B"><stop offset="0" stop-color="#a70c0c"></stop><stop offset="1" stop-color="#d21e1a"></stop></linearGradient></defs><path fill="url(#AG)" d="M601 487c2-2 4-5 7-6 1-1 2-2 3-4l1 1c-1 1-4 3-3 5v1c-7 7-14 16-19 24l-2 4c0 1-1 3-1 4v1c0 1 0 2-1 3l-1 1c-2 1-3 2-5 4 0 1-3 5-4 5h-1-3c-1 1-2 3-3 3v1c-2 1-3 1-4 2s-3 3-4 3l-1 1c-3 1-7 3-9 5v1h0l-5 4c0-2 1-4 0-6 0-1 1-2 1-4l3-5c0-2 0-3 1-4 1-2 1-3 1-5v-1l1-1v-2h2v-1l1 1c0-2 1-2 0-4h1 0l1 1c0-4 4-9 6-13 1-2 4-6 4-9l1-1c0 1 1 2 2 3h-1c-1 2-1 3-2 5 0 1-1 2-1 3v1l1-1v1c2-1 2-3 3-4l1-1c0-2-1-2 0-3v-2c2 0 2 0 3 1v4c1 0 1 0 1-1 1-1 3-1 5-1l1-1c2 0 3-1 4-2h1 1l2-1 1-1 10-9z"></path><path d="M563 513l1 1v1c-1 1-1 2-1 3l-1 1c-1 0-1 2-1 3s0 3 1 4v-1c-2-1-5-5-5-7l1 1h2c1-1 3-4 3-6z" class="AQ"></path><path d="M555 521l1 1c0-2 1-2 0-4h1 0c0 2 3 6 5 7v1 1h-1v1l-2-2h-1l-1-1c0-1-1-2-2-3v-1z" class="p"></path><path d="M564 506c0 2-2 5-1 7 0 2-2 5-3 6h-2c0-4 4-9 6-13zm7 4h0v1l2 2h0 2l1 1c1 1 1 2 1 3l-1 1-2-1c-1 1-1 2-3 2 0-1 0-2-1-2 0-2 0-2-1-3h-1v-3c1 0 2 0 2-1h1z" class="x"></path><path d="M555 522c1 1 2 2 2 3l1 1 2 3h-1l-1-1-1 1v1c0 1 0 2-1 4v-1l-1-1v1c0 1 0 1 1 2 0 3-3 5-5 7 2-1 4-1 6-3h2s0 1 1 1c-3 1-7 3-9 5v1h0l-5 4c0-2 1-4 0-6 0-1 1-2 1-4l3-5c0-2 0-3 1-4 1-2 1-3 1-5v-1l1-1v-2h2z" class="I"></path><path d="M555 522c1 1 2 2 2 3l-1 1c0-1 0-2-1-2l-1 1c0 1-2 3-2 4 0 0 1 1 1 2-1 0-3 3-3 4 0-2 0-3 1-4 1-2 1-3 1-5v-1l1-1v-2h2z" class="w"></path><path d="M601 487c2-2 4-5 7-6 1-1 2-2 3-4l1 1c-1 1-4 3-3 5v1c-7 7-14 16-19 24l-2 4c0 1-1 3-1 4v1c0 1 0 2-1 3l-1 1c-2 1-3 2-5 4 0 1-3 5-4 5h-1-3c-1 1-2 3-3 3v1c-2 1-3 1-4 2s-3 3-4 3l-1-1 3-1c2-2 3-4 5-6 2-1 4-2 5-4s4-4 5-6c2-2 3-3 4-5s1-2 1-4l-2-2c0-1-1-2-2-3l-1-2v-1c1-1 2-2 3-2l4-1c1-1 1-2 2-3h1l2-1 1-1 10-9z" class="AS"></path><path d="M589 501c1 0 1-1 2-1l4-1c-2 2-3 4-5 6-1 2-3 4-4 6-1-3 0-4 2-6 1-1 1-2 2-3l-1-1z" class="H"></path><path d="M579 507c2 0 2 0 4 1 1-2 2-2 4-3h1c-2 2-3 3-2 6-1 2-2 4-4 5 1-2 1-2 1-4l-2-2c0-1-1-2-2-3z" class="n"></path><path d="M589 501l1 1c-1 1-1 2-2 3h-1c-2 1-3 1-4 3-2-1-2-1-4-1l-1-2v-1c3 0 4-1 6 0 2-1 4-2 5-3z" class="R"></path><path d="M588 512c0 1-1 3-1 4v1c0 1 0 2-1 3l-1 1c-2 1-3 2-5 4 0 1-3 5-4 5h-1-3c1-2 3-3 5-5l7-8 4-5z" class="AH"></path><defs><linearGradient id="AH" x1="560.994" y1="371.689" x2="615.395" y2="397.759" xlink:href="#B"><stop offset="0" stop-color="#000001"></stop><stop offset="1" stop-color="#2b0a09"></stop></linearGradient></defs><path fill="url(#AH)" d="M678 244l1 8v2c2 9 2 19 1 28v6c-1 3-2 7-2 11-1 4-1 12-4 15v1 2c0 3-2 7-4 10v1 2c-1 1-1 2-1 3v1l-8 9h2c0 2 1 1 0 3 0 1 0 1 1 1v1l-1 3-6 11 1 1v2l-1 2h-2v2h1 1c1 2 0 2 0 3 1 1 1 2 1 3s-1 1-1 2c-2 2-3 4-3 7l-1 2-10 23-12 26-1 2-3 6-5 11c0 1 0 1-1 2v1c-1 2-1 4-1 5v1c-2 2-2 3-3 4v2l-1-1c-1 1-2 2-3 4 0 1-4 4-5 4-1 3-3 5-4 7-1 1-2 2-3 4l-10 9-1 1-2 1h-1-1c-1 1-2 2-4 2l-1 1c-2 0-4 0-5 1 0 1 0 1-1 1v-4c-1-1-1-1-3-1v2c-1 1 0 1 0 3l-1 1c-1 1-1 3-3 4v-1l-1 1v-1c0-1 1-2 1-3 1-2 1-3 2-5h1c-1-1-2-2-2-3l-1 1c0 3-3 7-4 9-2 4-6 9-6 13l-1-1h0-1c1 2 0 2 0 4l-1-1-3-5-1 1c-1 0-1-3-3-3-1 1-1 1 0 3l-1 1-1-1-1 1c0 2-1 2-2 3 0 4 0 4-2 6l-1-3c-1 0-1 1-2 2-2 2-3 5-4 8l-4 9h-1c1-3 2-6 2-9v-1l-1 1v1 2c0 1-1 4-3 5h0v-1-2h-1s0 1-1 1v-1c-1 0-1 1-2 1 1-1 0-2 0-3 1-3 1-6 3-9l2-2 1 1v-4l-1-1c-1-1-2-1-2-3l-1 1-1 2-1-1h-1c0-3 1-7 1-9 1-2 1-4 1-6h-1v-2-3c1-2 1-3 1-5l-3-6c-1-3-3-5-3-8-1-1-1-3-2-5 0-2 0-4 1-7 0-3 0-7 1-10l2-17c1-2 0-6 2-9v6c1-2 0-4 1-7h0 1v-3l1 1c1 0 2 0 3-1l1-2 1-1 1 1v1-2c0-2 0-2 1-3l-1-1c-1 1-1 1-2 1v-2l-1-1 1-1 3-6 2-2-1-1c1-1 1-2 2-2 1-2 3-4 4-6s2-5 4-7h0c2 0 3-1 4-2s1-3 2-3l3-6 4-7 1 3c2-3 3-5 4-8h2v-1l1-1v2h1 1l-1-5v-5h-1c0-1 1-2 1-3 0-4 3-9 5-13h0c1-2 3-5 4-7l-1-4v-1l1-1v-1c-1 0-2-1-3-2h0l-2 1-1-1-1-2h1 1c1-2 2-3 2-6h0c-1-2 0-4 0-5-1-1-2-2-1-3 1 0 1 0 2-1 0-2 1-3 2-4s2-1 3-1l1-3v-1-2l-1 1c-1-1-1-1-1-2v-3h-2v-2l-1-1 1-1c1 1 1 1 1 3l1-1 2 2 1 1c0 1 1 2 2 2h2c2 1 3 3 5 2h5 0 2c0-2-1-2-2-5l4 3c0 1-1 2 0 3 2 0 4-1 6-2 5-2 8-5 14-4-1 2-2 3-3 4v1l-2 2-1 1-3 3 1 1c-1 1-2 2-4 3h3c2-1 3-2 5-2h0c5-4 10-12 12-18 1-2 2-4 3-7-1-4 1-8 1-12l1 1v2h1v-3c0-1 0-2 1-3l1 1v-1c0-2 0-3-1-4l-2-5c-3-6-7-11-12-16v-1c11 7 18 18 20 30 1 2 1 4 0 5 0 4 0 7-1 10l6-7c10-12 23-22 32-35l3-5z"></path><path d="M570 426c1-1 1-1 2-1l1 1c2 2 2 4 3 7-1-1-3-3-3-4h-1 0l-2-3z" class="Q"></path><path d="M546 467l-1-2c1-1 1-2 1-3v-1l1-4h1v2 2 4l-2 2z" class="E"></path><path d="M604 331c1 2 2 2 2 5-1 0-1 1-2 2h0l-3-3c0-1 2-3 3-4z" class="Ac"></path><path d="M600 402h1c0 2 0 3 1 5-1 1-2 3-4 4l-1 1h0-1l1-2h0c1-1 1-1 1-2h0l1-2c0-1 0-2 1-4zm15-27c-1-2-3-4-3-6h1 3 0 4l1 1c2 1 3 1 4 3-1 0-1 1-1 1h-1c-1 0-1-1-2-2l-1-1v-1h-3c-2 1-2 0-4 0h0c1 1 1 3 2 4v1z" class="AJ"></path><path d="M552 385l4-7 1 3v1c-3 2-5 7-6 10h-1l3-6-1-1z" class="B"></path><path d="M572 429h0 1c0 1 2 3 3 4l2 5-3-1h-1l-2-8z" class="W"></path><path d="M574 437h1l3 1 1 4h-1v2h-3l-1-7z" class="k"></path><path d="M613 335c2 1 2 2 4 3v1 1 4c0 2 0 4 1 5l-1 3c0-1-1-2-1-2v-1c0-1-2-2-2-3l-1-2v-1c0-1-1-2-2-3 0-1 2-3 2-5z" class="AJ"></path><path d="M613 335c2 1 2 2 4 3v1c-2 1-3 2-4 4 0-1-1-2-2-3 0-1 2-3 2-5z" class="E"></path><path d="M575 444h3v-2h1l2 5 1 4s0 1-1 1c0 1 1 1 0 3v1h-1 0l-1 1h0c-1-2-1-5-2-8l-2-5z" class="j"></path><path d="M575 444h3v-2h1l2 5-2-2-1 1c1 1 2 2 1 4-1-1-1-2-2-3v2l-2-5z" class="Z"></path><path d="M597 380c-1-3 0-7-1-10v-2-1h0 0v-1-4-3-1h1c0 1 0 1 1 2 2 4 2 12 3 17h-3c0 1-1 2-1 3z" class="E"></path><path d="M526 503l1-2h1v-1h-1v-1c3-1 2-4 3-6v2c1-1 1-1 1-3-1-1-1-2-1-3h1 0l1-1c-1-2-1-1-1-2h1c1 2 0 4 0 6h1v3c-1 2-1 5-2 7v3l-1 1c0 1 0 4-1 5h0c-3-2 0-6-1-9l-2 1z" class="R"></path><path d="M581 456v-1c1-2 0-2 0-3 1 0 1-1 1-1 1 1 2 3 2 5 2 2 1 6 2 10l1 2-3 1c0-1-1-1-1-2l-1-1c-1-2-2-6-3-9l1-1h0 1z" class="AQ"></path><path d="M579 457l1-1h0 1c0 1 1 2 1 3s0 1 1 2c0 1 0 4-1 5-1-2-2-6-3-9z" class="V"></path><path d="M582 361l-1-2c-1-2-1-3-3-5h0v-1h2 0l-3-4 1-1h2v1h1 0c0 1 1 2 1 3h2c1 6 2 10 2 17v-1c-1-2-2-3-4-5 1-1 1-1 0-2z" class="AZ"></path><path d="M634 271c1 1 1 2 1 4l1 1v5 3c0 3-2 6-4 8-1 0-1 0-2-1 0-2 0-3 1-4v-1c0-1 1-1 1-2l-1-1c0-2 0-2 1-3h0-2c-1 2-2 4-1 6v1 5 4c0-2 0-7-1-9-1-4 1-8 1-12l1 1v2h1v-3c2 2 0 3 2 5v1h1v-2c1-1 1-3 1-4-1-1-1-3-1-4z" class="J"></path><path d="M526 494c1 2 1 3 0 5v3 1l2-1c1 3-2 7 1 9h0l1 1-1 6c-1-2-1-3-2-5 0 1-1 2-1 3 1 1 0 1 0 2h-1l-1-3c-1 2-1 4-1 6h-1c0-3 1-7 1-9 1-2 1-4 1-6 0-1 1-2 1-3v-2l1-7z" class="n"></path><path d="M524 515l2-9c1 2 1 5 1 7h0c0 1-1 2-1 3 1 1 0 1 0 2h-1l-1-3z" class="k"></path><path d="M618 246c11 7 18 18 20 30 1 2 1 4 0 5h0v-3c-1-1-1-2 0-3-1-1-1-2-2-3v-1-1-2l-1-1-4-7c-1-2-3-3-4-5 2 4 4 7 5 11 1 2 2 3 2 4v1c0 1 0 3 1 4 0 1 0 3-1 4v2h-1v-1c-2-2 0-3-2-5 0-1 0-2 1-3l1 1v-1c0-2 0-3-1-4l-2-5c-3-6-7-11-12-16v-1z" class="O"></path><path d="M579 423c-2-3-2-7-4-9v-3h1 1c-1 1-1 1-1 2 1 1 1 2 2 2l2 1v1c2 2 2 6 5 7l2 1c1-1 2-2 2-3h-1-1v-1h1c2-2 5-6 7-8-1 4-3 8-5 12v6l1 1-1 1s-1 0-1-1 0-1-1-2c-2-1-4-4-5-6h0c-1 0-2-1-2-1l-1-1-1 1z" class="R"></path><path d="M597 380c0-1 1-2 1-3h3v5c1 3 1 5 4 7h0c0 2 0 2-1 3 0 2 1 3 1 4l1 1h1v1 1 1h-2 0c-1-1-1-1-2-1-1-2-1-3-2-5l-1-2c-1-2-1-3-2-5 0-3-1-5-1-7z" class="U"></path><path d="M604 392l-2-2c-2-3-2-4-2-8h1c1 3 1 5 4 7h0c0 2 0 2-1 3z" class="n"></path><path d="M534 488h2c1 1 1 2 1 3l-1 1h1 4-1l-4 9v3h0c-1 1-2 3-3 5 0 3-2 7-1 10 0 1-1 1-1 2h-1c-1-1-1-1-1-3l1-6-1-1c1-1 1-4 1-5l1-1v-3c1-2 1-5 2-7v-3c1-1 1-3 1-4z" class="b"></path><path d="M533 509c0-1 0-2 1-3 0-2 0-2 1-4v-3-2c0-2-1-2-1-3v-2l1-1 1 1h1 4-1l-4 9v3h0c-1 1-2 3-3 5z" class="n"></path><path d="M536 492h1 4-1l-2 1v1c0 1 0 1-1 2 0 1 0 1-1 2v-6z" class="b"></path><path d="M604 409v3c-1 1-2 3-2 4l-1 2-3 5c-2 3-3 7-4 11l-1 1v1l-1 1c-1 1-1 1-2 1l-1 1c-2-1-2-3-4-3 0-1-1-2-1-3l-5-10 1-1 1 1s1 1 2 1h0c1 2 3 5 5 6 1 1 1 1 1 2s1 1 1 1l1-1c1-2 2-3 3-4 2-4 4-8 6-11 1-3 2-6 4-8z" class="I"></path><path d="M589 436c1-2 1 0 2-1 1 0 1-1 2-1v1 1l-1 1c-1 1-1 1-2 1l-1-2z" class="T"></path><path d="M579 423l1-1 1 1s1 1 2 1h0c1 2 3 5 5 6 1 1 1 1 1 2l-5-4c2 3 2 5 5 8l1 2-1 1c-2-1-2-3-4-3 0-1-1-2-1-3l-5-10z" class="AT"></path><path d="M628 349h3v-1-1h1v3c-1 1-1 0-1 1s-1 3-1 3v2c0 1-1 1 0 3v2c0 1 0 1 1 3v2l1 2c2 0 2 0 3-1h1l1 2-1 1c-2 1-2 2-3 4h-3l1-2h-2 0c0 1-1 1-1 2v-1-1l-2-2h-1v-1c0-1 0-2-1-3v-1l1-2v-2l-1 1-1-1s0-1-1-2h0l1-1h0l1 1v-1h2v-1c0-1 1-2 2-3l-2 1h0c-1-1-1-2-1-3l1-1c1 0 1-1 2-2z" class="c"></path><path d="M624 366l3-1c1 2 0 3 1 5 0 1 1 2 1 2 0 1-1 1-1 2v-1-1l-2-2h-1v-1c0-1 0-2-1-3z" class="AJ"></path><path d="M631 366l1 2c2 0 2 0 3-1h1l1 2-1 1c-2 1-2 2-3 4h-3l1-2h-2v-3c1-2 1-2 2-3z" class="R"></path><path d="M557 381c2-3 3-5 4-8h2v-1l1-1v2h1 1c0 1 1 2 1 3s0 1 1 2 1 2 1 4h1c1-1 1-2 1-4h-1l1-1h0c0 5 2 10 2 15 0 2-1 5-1 7-1-1-1-3-2-4-1-3-2-6-4-9-3-2-6-3-9-4v-1z" class="AD"></path><path d="M566 386v-2c-1 0-2-1-2-1v-4c1-1 2-1 3-2 1 2 0 3 1 5 0 1 0 2 1 2 0 1 1 2 1 3v2l1 2c0 1 0 3-1 4-1-3-2-6-4-9z" class="AN"></path><path d="M610 326c1 1 2 3 3 4l1 1 1 1h3c-1 2-1 4-1 6-2-1-2-2-4-3 0 2-2 4-2 5-2 3-4 5-6 7-1 0-3-2-4-3s-2-2-2-4c2-2 3-1 5-2h0c1-1 1-2 2-2 0-3-1-3-2-5l3-2 2-2 1-1z" class="I"></path><path d="M607 329c0 1 0 2 1 4l-1 1c1 2 1 2 1 3l-2 2-2-1c1-1 1-2 2-2 0-3-1-3-2-5l3-2z" class="w"></path><path d="M610 326c1 1 2 3 3 4l1 1 1 1h3c-1 2-1 4-1 6-2-1-2-2-4-3-1 0-1-1-2-2v-1l-2-5 1-1z" class="s"></path><path d="M634 302v3h1l1-1-4 5v1c0 1-1 2-1 3l1 1h0l-11 14c-1 1-2 4-3 4h-3l-1-1-1-1c-1-1-2-3-3-4 1-2 5-4 7-5 2-3 6-5 8-8 4-3 6-7 9-11z" class="Ac"></path><path d="M610 326c1-2 5-4 7-5-1 4-2 5-4 9-1-1-2-3-3-4z" class="AK"></path><path d="M632 309v1c0 1-1 2-1 3l1 1h0l-11 14c-1 1-2 4-3 4h-3l-1-1 18-22z" class="V"></path><path d="M615 375v-1c-1-1-1-3-2-4h0c2 0 2 1 4 0h3v1l1 1c1 1 1 2 2 2h1s0-1 1-1c0 1 1 2 2 2l1-1c0-1 1-1 1-2h0 2l-1 2c-1 2-1 2-2 3v2l-1 1c1 1 1 2 1 3h-1v-1c-2 0-3 2-4 3 0 2-1 2-2 4l-1-1c-1 1-2 2-3 4h-1c-1 2-1 3-2 5 0 1 0 1-1 2 0 1-1 2-1 3v1c-1 0 0 0-1 1l-2-1v-2-1c1-1 1-1 1-2l1-1-1-1c0-2 3-4 4-7v-1h1l1-2c0-2 0-2 1-3 1 0 0 0 1-1-1-1-1-2-1-3-1-1-2-2-2-3v-1z" class="U"></path><path d="M629 372h0 2l-1 2c-1 2-1 2-2 3v2c-2-2-3-3-4-5 0 0 0-1 1-1 0 1 1 2 2 2l1-1c0-1 1-1 1-2z" class="H"></path><path d="M616 392v-1l-1-1c0-2 1-4 2-5l3 1v-2-1c1 1 1 2 1 4l-1 1c-1 1-2 2-3 4h-1z" class="b"></path><path d="M621 387l1-1c-1 0-1-1-1-2v-1-2c0-2 0-3 2-4l1 2h1c1 1 1 1 2 1h0c1 1 1 2 1 3h-1v-1c-2 0-3 2-4 3 0 2-1 2-2 4l-1-1 1-1z" class="n"></path><path d="M534 488c0-1-1-1-1-2h0v-5l1-1-1-1v-1l2-2c1 1 0 1 2 1v-1-1c1-2 0 0 1-1l1 3 1-1h0c1-2 2-3 4-4 0 2-1 2-1 3v5l1 1c0 2 1 4 1 6 1 1 1 1 1 2h-1 0v1c2 2 3 5 4 8h0-1v-1c0-1-1-1-1-2h-1v1c0 1 0 2-1 3h-2c0 1-1 2-1 3-1 0-1 1-2 1v-1-1l-1-1c-1 1-1 2-1 3-1 0-1 0-2 1v-3l4-9h1-4-1l1-1c0-1 0-2-1-3h-2z" class="E"></path><path d="M540 486c0 1 1 2 2 2 0 2 0 2-1 3v1h-4-1l1-1h1c0-1 1-3 2-5z" class="U"></path><path d="M540 486c-1-2-3-3-4-5l1-1v1c1 1 2 3 4 3v-3-2c1-1 1-2 2-4v5l1 1c0 2 1 4 1 6 1 1 1 1 1 2h-1 0v1c2 2 3 5 4 8h0-1v-1c0-1-1-1-1-2s-1-2-2-3c0 0 0-1-1-2 0-1-1-2-2-3v1c-1 0-2-1-2-2z" class="Q"></path><path d="M542 488v-1c1 1 2 2 2 3 1 1 1 2 1 2 1 1 2 2 2 3h-1v1c0 1 0 2-1 3h-2c0 1-1 2-1 3-1 0-1 1-2 1v-1-1l-1-1c-1 1-1 2-1 3-1 0-1 0-2 1v-3l4-9h1v-1c1-1 1-1 1-3z" class="W"></path><path d="M540 501c1-2 3-8 3-10 1 0 2 3 3 4v1c0 1 0 2-1 3h-2c0 1-1 2-1 3-1 0-1 1-2 1v-1-1z" class="H"></path><path d="M548 459l2-1-1-1c0-1 0-1 1-2h0l1 1v2c0 5 4 14 6 19v1h-1c-1 2-2 3-3 5-2 3-2 7-4 9l-2-2-1-1c0-1 0-1-1-2 0-2-1-4-1-6l-1-1v-5c0-1 1-1 1-3v-1c1-1 2-2 2-4l2-2v-4-2z" class="s"></path><path d="M548 484h0c1 1 1 2 2 3-1 1-1 2-2 3h-1l-1-1c0-1 0-1-1-2 1-2 1-2 3-3z" class="R"></path><path d="M548 459l2-1-1-1c0-1 0-1 1-2h0l1 1v2c0 5 4 14 6 19v1h-1c-1 1-2 1-3 2l-1 1v-3c-1-1 0-3 0-4 0-2-1-4-1-6-1-2-1-5-3-7v-2z" class="AC"></path><path d="M548 465c1 2 1 4 0 7v12c-2 1-2 1-3 3 0-2-1-4-1-6l-1-1v-5c0-1 1-1 1-3v-1c1-1 2-2 2-4l2-2z" class="H"></path><path d="M544 472h1c0 3-1 6-1 9l-1-1v-5c0-1 1-1 1-3z" class="z"></path><path d="M557 477c2 2 3 5 4 8l-1 1c-1 0-1 1-2 3v1l-3 9v1l-3 3c0 1 1 2 1 3-1 4 2 8 4 12h-1c1 2 0 2 0 4l-1-1-3-5-1-2-1 1c-1-1-2-4-2-5l-1-2v-2h-1l-2 2v-1l1-3 2 1h1l1-1-1-2c0-2-1-4-2-6v-1h1c0 1 1 1 1 2v1h1 0c-1-3-2-6-4-8v-1h0 1l1 1 2 2c2-2 2-6 4-9 1-2 2-3 3-5h1v-1z" class="x"></path><path d="M549 504l2 10-1 1c-1-1-2-4-2-5l-1-2v-2h-1l-2 2v-1l1-3 2 1h1l1-1z" class="H"></path><path d="M557 477c2 2 3 5 4 8l-1 1c-1 0-1 1-2 3v1l-3 9v1l-3 3v-6c-1-2 0-3 0-5v-2c1-4 3-7 5-11v-1-1z" class="Q"></path><path d="M561 485c1 1 2 3 2 4 1 3 4 5 4 8h1c0 3-3 7-4 9-2 4-6 9-6 13l-1-1h0c-2-4-5-8-4-12 0-1-1-2-1-3l3-3v-1l3-9v-1c1-2 1-3 2-3l1-1z" class="u"></path><path d="M561 488h0c1 2 0 3-1 5 0 1 0 3-1 4-1 2-2 3-2 5l-1 3h-1 0v-5h1c1-2 2-4 2-7 1-2 2-3 3-5z" class="AT"></path><path d="M574 340c1-2 2-5 3-6 0 2 0 4-1 6l1 4v1c1 1 2 2 3 2v1h-2l-1 1 3 4h0-2v1h0c2 2 2 3 3 5l1 2c1 1 1 1 0 2-2-1-4-3-7-4h-1c-1-1-1 0-2 0-2 2-2 7-2 10l1 8h0l-1 1h1c0 2 0 3-1 4h-1c0-2 0-3-1-4s-1-1-1-2-1-2-1-3l-1-5v-5h-1c0-1 1-2 1-3 0-4 3-9 5-13h0c1-2 3-5 4-7z" class="X"></path><path d="M576 340l1 4v1l-5 2c1-2 2-3 3-5 0-1 1-2 1-2z" class="AD"></path><path d="M565 363l1-3c1-1 2-1 4-2v1 1 4c0 2 0 1-1 2 0 2 1 6 0 7s-1 2-2 3c0-1-1-2-1-3l-1-5v-5z" class="S"></path><path d="M572 347l5-2c1 1 2 2 3 2v1h-2l-1 1 3 4h0-2v1h0c2 2 2 3 3 5l1 2c-2-1-4-3-6-3-1-1-1-1-2-1l-1 1h-1v-1h-1l-2-1c0-3 2-6 3-9z" class="AL"></path><path d="M585 436c2 0 2 2 4 3l1-1c1 0 1 0 2-1l1-1v2h2c1 1 1 3 2 5l-1 2 4 7c1 0 1 0 2-1 1 1 1 1 2 3v1h-2c-1 0-1 1-2 2l-2-3v1c-1 2-2 3-2 5h0c-1 2-2 5-3 7l-2 2c-1 1-1 2-2 3l-1-1c0-1-1-2-1-3l-1-2c-1-4 0-8-2-10l1-1c2-5 4-9 4-14-2-1-3-3-4-5z" class="V"></path><path d="M586 466l2-2c0-1 0-1-1-2h0v-1l2 1c1 1 1 2 2 3l2 2-2 2c-1 1-1 2-2 3l-1-1c0-1-1-2-1-3l-1-2z" class="w"></path><path d="M587 456c0-4 1-7 3-11l1-1c0 1 1 2 2 3l3 4c-2 2-3 4-5 7v-5c1-2 1-3 1-5v-1c-2 3-2 7-4 9h-1z" class="H"></path><path d="M596 451l2 3v1c-1 2-2 3-2 5h0c-1 2-2 5-3 7l-2-2c-1-1-1-2-2-3l1-2c0-1 1-2 1-2 2-3 3-5 5-7z" class="Q"></path><path d="M596 451l2 3-1 1v-1c-1 1-1 1-1 2v3l-1 1-1-1v-2h-1c-1 1-1 2-3 3h0c0-1 1-2 1-2 2-3 3-5 5-7z" class="R"></path><path d="M585 436c2 0 2 2 4 3l1-1c1 0 1 0 2-1l1-1v2h2c1 1 1 3 2 5l-1 2 4 7c1 0 1 0 2-1 1 1 1 1 2 3v1h-2c-1 0-1 1-2 2l-2-3-2-3-3-4c-1-1-2-2-2-3l-1 1c-2 4-3 7-3 11-1 0-1 0-2-1 2-5 4-9 4-14-2-1-3-3-4-5z" class="AQ"></path><path d="M592 437l1-1v2h2c1 1 1 3 2 5l-1 2c-1 0-2-3-3-4 0-2 0-2-1-4z" class="j"></path><path d="M605 307c1-1 1-1 2-1h1c1 0 2 0 3 1l-3 3 1 1c-1 1-2 2-4 3h3c2-1 3-2 5-2-12 9-30 15-32 33-1 1-1 2 0 3 0-1 1-1 1-1h0l2 5h-2c0-1-1-2-1-3h0-1v-1-1c-1 0-2-1-3-2v-1l-1-4c1-2 1-4 1-6 1-1 1-2 2-4l3-6c1 0 2-1 3-1v-1c0-1 2-2 2-2l5-2 3-2h1c2-2 3-3 6-4v-2c0-1 0-1-1-2 2-1 2-1 4-1z" class="P"></path><path d="M587 320l5-2c2 0 3 0 5 1-3 2-6 4-8 6-1-1-1 0-2-1h-1v-2h-1c0-1 2-2 2-2z" class="Y"></path><path d="M608 310l1 1c-1 1-2 2-4 3-2 2-6 5-8 5-2-1-3-1-5-1l3-2h1c4-1 8-4 12-6z" class="C"></path><path d="M605 307c1-1 1-1 2-1h1c1 0 2 0 3 1l-3 3c-4 2-8 5-12 6 2-2 3-3 6-4v-2c0-1 0-1-1-2 2-1 2-1 4-1z" class="e"></path><path d="M601 308c2-1 2-1 4-1v2l-3 1c0-1 0-1-1-2z" class="AR"></path><path d="M585 322h1v2h1c1 1 1 0 2 1-6 6-9 11-11 18l-1 1-1-4c1-2 1-4 1-6 1-1 1-2 2-4l3-6c1 0 2-1 3-1v-1z" class="S"></path><path d="M546 496c1 2 2 4 2 6l1 2-1 1h-1l-2-1-1 3v1l2-2h1v2l1 2c0 1 1 4 2 5l1-1 1 2-1 1c-1 0-1-3-3-3-1 1-1 1 0 3l-1 1-1-1-1 1c0 2-1 2-2 3 0 4 0 4-2 6l-1-3c-1 0-1 1-2 2-2 2-3 5-4 8l-4 9h-1c1-3 2-6 2-9v-1l-1 1v1 2c0 1-1 4-3 5h0v-1-2h-1s0 1-1 1v-1c-1 0-1 1-2 1 1-1 0-2 0-3 1-3 1-6 3-9l2-2 1 1v-4l-1-1c-1-1-2-1-2-3l-1 1-1 2-1-1c0-2 0-4 1-6l1 3h1c0-1 1-1 0-2 0-1 1-2 1-3 1 2 1 3 2 5 0 2 0 2 1 3h1c0-1 1-1 1-2-1-3 1-7 1-10 1-2 2-4 3-5h0c1-1 1-1 2-1 0-1 0-2 1-3l1 1v1 1c1 0 1-1 2-1 0-1 1-2 1-3h2c1-1 1-2 1-3z" class="AT"></path><path d="M534 521c-1 1-1 2-1 2h-1c2-3 2-6 4-9v1h1l-3 6z" class="Q"></path><path d="M536 504v3 1c0 1-1 2-1 4h0c0 1-1 2-1 3-1 1-1 2-2 4-1-3 1-7 1-10 1-2 2-4 3-5z" class="W"></path><path d="M536 522c0-1 1-1 2-1-1 1-1 3-2 5h0l1-1 1 1c-2 2-3 5-4 8-1 0-1 0-2-1v-1-1c1-1 1-4 2-5 1-2 1-2 1-3l1-1z" class="z"></path><path d="M529 527v1l2-2c0 1-2 9-2 11l-2 4v-2h-1s0 1-1 1v-1c-1 0-1 1-2 1 1-1 0-2 0-3 1-3 1-6 3-9l2-2 1 1z" class="I"></path><path d="M546 496c1 2 2 4 2 6l1 2-1 1h-1l-2-1-1 3v1l2-2h1v2l1 2c0 1 1 4 2 5l1-1 1 2-1 1c-1 0-1-3-3-3-1 1-1 1 0 3l-1 1-1-1-1 1c0 2-1 2-2 3 0 4 0 4-2 6l-1-3c-1 0-1 1-2 2l-1-1-1 1h0c1-2 1-4 2-5-1 0-2 0-2 1-1 0-1 0-2-1l3-6h-1v-1c1-4 2-8 4-12v1c1 0 1-1 2-1 0-1 1-2 1-3h2c1-1 1-2 1-3z" class="V"></path><path d="M539 513c0-1 1-1 2-1 0 0 0-1 1-1v2c-1 1-2 2-2 3s0 1 1 2c-1 1-1 2-3 3-1 0-2 0-2 1-1 0-1 0-2-1l3-6 2-2z" class="m"></path><path d="M546 496c1 2 2 4 2 6l1 2-1 1h-1l-2-1c0-1 1-2 0-3-1 0-1 2-2 3l-4 9-2 2h-1v-1c1-4 2-8 4-12v1c1 0 1-1 2-1 0-1 1-2 1-3h2c1-1 1-2 1-3z" class="R"></path><path d="M541 518l1-4c1 0 1-1 2-2s2-3 3-4l1 2c0 1 1 4 2 5l1-1 1 2-1 1c-1 0-1-3-3-3-1 1-1 1 0 3l-1 1-1-1-1 1c0 2-1 2-2 3 0 4 0 4-2 6l-1-3c-1 0-1 1-2 2l-1-1-1 1h0c1-2 1-4 2-5 2-1 2-2 3-3z" class="AQ"></path><path d="M541 520l2 1c0 4 0 4-2 6l-1-3c-1 0-1 1-2 2l-1-1 4-5z" class="u"></path><path d="M548 510c0 1 1 4 2 5l1-1 1 2-1 1c-1 0-1-3-3-3-1 1-1 1 0 3l-1 1-1-1-1 1c0 2-1 2-2 3l-2-1c3-3 6-6 7-10z" class="Q"></path><path d="M552 385l1 1-3 6h1c0 1-1 2-1 2-3 8-6 16-8 25l-3 15-5 20-4 24c-1 5-2 11-4 16l-1 7v2c0 1-1 2-1 3h-1v-2-3c1-2 1-3 1-5l-3-6c-1-3-3-5-3-8 2 2 3 4 4 7 1-1 1-1 1-2h1 1v-2c1-1 1-2 1-3 1-2 0-3 0-5 1-1 0-2 1-4v-1-2c1-1 1-2 1-4l2-11-2-2c1-2 1-4 1-6v-2l1-1c1 1 1 2 2 3v-3c1-4 2-9 3-13l1-4-1-1v-3l-1-1c0-1 0-1-1-3-1 2-2 3-2 4-1 1-1 1-2 1v-2l-1-1 1-1 3-6 2-2-1-1c1-1 1-2 2-2 1-2 3-4 4-6s2-5 4-7h0c2 0 3-1 4-2s1-3 2-3l3-6z" class="Z"></path><path d="M528 466v4h1v1 2l-1 7-2 8v2l-1 1v4h-1v-1c0-2-1-3-2-5 1-1 1-1 1-2h1 1v-2c1-1 1-2 1-3 1-2 0-3 0-5 1-1 0-2 1-4v-1-2c1-1 1-2 1-4z" class="H"></path><path d="M552 385l1 1-3 6h1c0 1-1 2-1 2-3 2-8 18-9 22h-2v-2c1-1 1-3 2-5l6-15c1-1 1-3 2-3l3-6z" class="AI"></path><path d="M532 444h1l-4 26h-1v-4l2-11-2-2c1-2 1-4 1-6v-2l1-1c1 1 1 2 2 3v-3z" class="b"></path><path d="M529 445l1-1c1 1 1 2 2 3l-2 8-2-2c1-2 1-4 1-6v-2z" class="U"></path><path d="M536 415c2-1 2-1 3-1v2h2l-8 28h-1c1-4 2-9 3-13l1-4-1-1v-3l-1-1c0-1 0-1-1-3 0-1 1-2 1-3l1 1 1-2z" class="E"></path><path d="M536 415c2-1 2-1 3-1v2l-1 5-2 6-1-1v-3l-1-1c0-1 0-1-1-3 0-1 1-2 1-3l1 1 1-2z" class="AS"></path><path d="M536 415c2-1 2-1 3-1v2l-1 5c-1-1-1-2-2-3-1 1-1 1-1 2h-1c0-2 0-2 1-3l1-2z" class="AH"></path><path d="M543 396c2 0 3-1 4-2l-6 15c-1 2-1 4-2 5-1 0-1 0-3 1l-1 2-1-1c0 1-1 2-1 3-1 2-2 3-2 4-1 1-1 1-2 1v-2l-1-1 1-1 3-6 2-2-1-1c1-1 1-2 2-2 1-2 3-4 4-6s2-5 4-7h0z" class="AA"></path><path d="M536 412c0-1 2-3 3-4 0 0 0 1 1 1v1-1h1c-1 2-1 4-2 5-1 0-1 0-3 1v-3z" class="AS"></path><path d="M536 412v3l-1 2-1-1c0 1-1 2-1 3-1 2-2 3-2 4-1 1-1 1-2 1v-2l-1-1 1-1 3-6h2l2-2z" class="K"></path><path d="M533 419c1 2 1 2 1 3l1 1v3l1 1-1 4c-1 4-2 9-3 13v3c-1-1-1-2-2-3l-1 1v2c0 2 0 4-1 6l2 2-2 11c0 2 0 3-1 4v2 1c-1 2 0 3-1 4 0 2 1 3 0 5 0 1 0 2-1 3v2h-1-1c0 1 0 1-1 2-1-3-2-5-4-7-1-1-1-3-2-5 0-2 0-4 1-7 0-3 0-7 1-10l2-17c1-2 0-6 2-9v6c1-2 0-4 1-7h0 1v-3l1 1c1 0 2 0 3-1l1-2 1-1 1 1v1-2c0-2 0-2 1-3l-1-1c0-1 1-2 2-4z" class="AJ"></path><path d="M530 437c1 0 1 1 2 1l-2 6-1 1h0v-1-1c0-1 0-1-1-1v-1l1-1 1-3z" class="c"></path><path d="M531 427l2-1v1l-1 1v1c1 3 0 6 0 9-1 0-1-1-2-1 0-3-1-4 1-7v-1-2z" class="AC"></path><path d="M532 429c1 1 2 2 3 2-1 4-2 9-3 13v3c-1-1-1-2-2-3l2-6c0-3 1-6 0-9z" class="s"></path><path d="M533 419c1 2 1 2 1 3l1 1v3l1 1-1 4c-1 0-2-1-3-2v-1l1-1v-1l-2 1c0-2 0-2 1-3l-1-1c0-1 1-2 2-4z" class="AW"></path><path d="M523 484v-3c1-3 0-6 0-9 1-3 0-7 2-9l1-1h0c1-3-1-5 0-7s2-5 3-8c0 2 0 4-1 6l-1 3c0 4 0 8-1 12l-1 9-2 7z" class="R"></path><path d="M528 453l2 2-2 11c0 2 0 3-1 4v2 1c-1 2 0 3-1 4 0 2 1 3 0 5 0 1 0 2-1 3v2h-1-1v-3l2-7 1-9c1-4 1-8 1-12l1-3z" class="s"></path><path d="M573 296l1-1c1 1 1 1 1 3l1-1 2 2 1 1c0 1 1 2 2 2h2c2 1 3 3 5 2h5 0 2c0-2-1-2-2-5l4 3c0 1-1 2 0 3 2 0 4-1 6-2 5-2 8-5 14-4-1 2-2 3-3 4v1l-2 2-1 1c-1-1-2-1-3-1h-1c-1 0-1 0-2 1-2 0-2 0-4 1 1 1 1 1 1 2v2c-3 1-4 2-6 4h-1l-3 2-5 2s-2 1-2 2v1c-1 0-2 1-3 1l-3 6c-1 2-1 3-2 4s-2 4-3 6l-1-4v-1l1-1v-1c-1 0-2-1-3-2h0l-2 1-1-1-1-2h1 1c1-2 2-3 2-6h0c-1-2 0-4 0-5-1-1-2-2-1-3 1 0 1 0 2-1 0-2 1-3 2-4s2-1 3-1l1-3v-1-2l-1 1c-1-1-1-1-1-2v-3h-2v-2l-1-1z" class="e"></path><path d="M585 319c-2 1-3 1-4 1-1-1-2-1-3-2v-1h-2-1l-1-1 1-1c0-1 1-1 1-1 1 0 1-1 3-1l1 1c-2 1-2 0-3 2h1 4l2-2 1 2c0 1-1 1-2 2h-1l-1 1h4z" class="AF"></path><path d="M579 321c0 1 0 1 1 2l2-2c0 1 0 1 1 1 1-2 2-2 4-2h0s-2 1-2 2v1c-1 0-2 1-3 1l-3 6-1-1c1-1 1-1 0-2h-1c0-1 1-3 1-4h-2l-1-2 1-1 1 1 1 1 1-1z" class="AG"></path><path d="M571 323c-1-2 0-4 0-5-1-1-2-2-1-3 1 0 1 0 2-1 0-2 1-3 2-4s2-1 3-1v1c-2 2-3 4-5 5 0 1 0 1-1 2 1 1 2 1 3 2 0 0 1 0 2-1v1c1 1 1 1 2 1l1 1-1 1-1-1-1-1-1 1 1 2v1c-1 1-1 1-1 2h-1l-1-3h-2 0z" class="q"></path><path d="M571 323l2-3h1v3h2v1c-1 1-1 1-1 2h-1l-1-3h-2 0z" class="M"></path><path d="M576 323h2c0 1-1 3-1 4h1c1 1 1 1 0 2l1 1c-1 2-1 3-2 4s-2 4-3 6l-1-4v-1l1-1v-1c-1 0-2-1-3-2h0l-2 1-1-1-1-2h1 1c1-2 2-3 2-6h2l1 3h1c0-1 0-1 1-2v-1z" class="O"></path><path d="M577 327h1c1 1 1 1 0 2l1 1c-1 2-1 3-2 4s-2 4-3 6l-1-4v-1l1-1v-1c-1 0-2-1-3-2h0l3-1c1-1 1-1 2-1l1-2z" class="a"></path><path d="M571 331l3-1c1-1 1-1 2-1 0 2-1 1-1 2-1 2 0 2-1 3v-1c-1 0-2-1-3-2h0z" class="B"></path><path d="M601 308c1 1 1 1 1 2v2c-3 1-4 2-6 4h-1c-1 0-3 0-4 1h-2c-1 0-2 1-3 1v1h-1-4l1-1h1c1-1 2-1 2-2l-1-2h0c1-2 4-3 6-3 1 0 2 0 4-1 1-1 3-1 4-1h3v-1z" class="AR"></path><path d="M601 308c1 1 1 1 1 2-2 0-2 0-2 2-1-1-1 0-1-1h-1l-3 3c-1 0-3 1-4 0v-1l6-3c1 0 2-1 3-1h1v-1z" class="AL"></path><path d="M573 296l1-1c1 1 1 1 1 3l1-1 2 2 1 1c0 1 1 2 2 2h2c2 1 3 3 5 2h5 0 2c0-2-1-2-2-5l4 3c0 1-1 2 0 3 2 0 4-1 6-2 5-2 8-5 14-4-1 2-2 3-3 4v1l-2 2-1 1c-1-1-2-1-3-1h-1c-1 0-1 0-2 1-2 0-2 0-4 1v1h-3c-1 0-3 0-4 1-2 1-3 1-4 1-2 0-5 1-6 3h0l-2 2h-4-1c1-2 1-1 3-2l-1-1h0c-1-1-1-1-2-1l1-1-1-1v-1l1-3v-1-2l-1 1c-1-1-1-1-1-2v-3h-2v-2l-1-1z" class="M"></path><path d="M578 306l1 3c1 0 1-1 1-1 2-1 2 0 4 0h0 2l2-1c3 2 6 0 10 0-2 1-6 2-9 2h0c-1 1-1 1-2 1s-3 1-4 1c-2 0-1-1-2 0h-1-1v2c-1-1-1-1-2-1l1-1-1-1v-1l1-3z" class="X"></path><path d="M573 296l1-1c1 1 1 1 1 3l1-1 2 2 1 1c0 1 1 2 2 2h2c2 1 3 3 5 2h5 0 2c0-2-1-2-2-5l4 3c0 1-1 2 0 3-2 1-7 2-8 1-1 0-1-1-2-1l-1 1-1-1c-1 0-1 0-2-1h-2 0l-1-1c-1 0-1 1-2 2v-2l-1 1c-1-1-1-1-1-2v-3h-2v-2l-1-1z" class="Y"></path><path d="M598 307h0c3-2 8-3 12-3l2 2-1 1c-1-1-2-1-3-1h-1c-1 0-1 0-2 1-2 0-2 0-4 1v1h-3c-1 0-3 0-4 1-2 1-3 1-4 1-2 0-5 1-6 3h0l-2 2h-4-1c1-2 1-1 3-2l-1-1h0v-2h1 1c1-1 0 0 2 0 1 0 3-1 4-1s1 0 2-1h0c3 0 7-1 9-2z" class="P"></path><path d="M605 389c0-2-1-3 0-4 1 1 3 2 3 4 0 1-1 2-1 3h1l-1 1v2c1 1 2 1 3 1l1 1-1 1c0 1 0 1-1 2v1 2l2 1c1-1 0-1 1-1v-1c0-1 1-2 1-3 1-1 1-1 1-2 1-2 1-3 2-5h1c1-2 2-3 3-4l1 1h2 1 1 1l1 1 1 1c1 1 2 2 3 2v1l2 1 2 1c-2 2-2 3-3 5l2 1c-1 1-2 2-2 3l1 1h-2c1-1 1-2 1-3l-2-1c-1 1-1 2-2 3-1 2-1 4-3 5-1 2-2 4-2 7h-1l-1 2 1 1c-1 0-2 1-2 2-1 1-2 3-3 4l-6 7-1 2c-1 0-2 2-3 1h-1c-1-1-2-1-3-1l-1 1v2l-3 1c-1 0-1 0-1 1h-1c0-2-1-2-2-4h1c-1-1-2-1-2-2 1-4 2-8 4-11l3-5 1-2c0-1 1-3 2-4v-3l2-1c0-1-1-1-1-1l1-2c0-1 0-2-1-2 0-2-1-3-2-4 1 0 1 0 2 1h0 2v-1-1-1h-1l-1-1c0-1-1-2-1-4 1-1 1-1 1-3h0z" class="R"></path><path d="M611 408h1c1 1 2 2 3 2 1 1 0 1 0 3v1 1h-1l1 2c0 1 0 1-1 2h-1v-5h-1c-1-1-2-2-3-4l2-2z" class="n"></path><path d="M612 402h1c1 1 3 2 3 4v1l1 1c-1 1-1 2 0 2l1 3c-1 0-1 1-2 2 0 1 0 1-1 2l-1-2h1v-1-1c0-2 1-2 0-3-1 0-2-1-3-2h-1c0-1-1-2-1-3h1 1v-2-1z" class="z"></path><path d="M604 412h0v1c-1 2-2 4-2 6h0c0-1 0-1 1-1v-1h0c1-1 1-1 1-2l1 1c-1 2-2 3-3 4-2 4-3 8-6 11 0 1 0 1 1 2 0 1-1 2-1 3-1-1-2-1-2-2 1-4 2-8 4-11l3-5 1-2c0-1 1-3 2-4z" class="U"></path><path d="M621 407c0 1 1 2 2 3l1-2 1 2c-1 2-2 4-2 7h-1l-1 2 1 1c-1 0-2 1-2 2-1 1-2 3-3 4l-6 7-1 2c-1 0-2 2-3 1h-1c-1-1-2-1-3-1l-1 1v2l-3 1-1-3v-1l1-1h0v2h1c0-1 0-2 1-3 0-1 1-1 1-2v-1l1 1c1-1 2-2 3-4 0-2 3-3 4-5v-1l2-2h1 1c1-1 1-1 1-2 1-1 1-1 1-2 1-1 1-2 2-2l1-2v-1l2-3z" class="H"></path><path d="M624 408l1 2c-1 2-2 4-2 7h-1l-1 2c0-2-1-3-2-5 2-1 3-2 4-4l1-2z" class="k"></path><path d="M620 388l1 1h2 1 1 1l1 1 1 1c1 1 2 2 3 2v1l2 1 2 1c-2 2-2 3-3 5l2 1c-1 1-2 2-2 3l1 1h-2c1-1 1-2 1-3l-2-1c-1 1-1 2-2 3-1 2-1 4-3 5l-1-2-1 2c-1-1-2-2-2-3l-2 3v1l-1 2-1-3c-1 0-1-1 0-2l-1-1v-1c0-2-2-3-3-4h-1c0-1 1-2 1-3 1-1 1-1 1-2 1-2 1-3 2-5h1c1-2 2-3 3-4z" class="W"></path><path d="M627 390l1 1c1 1 2 2 3 2v1h-1c-1 2-1 4-1 6l-4-4 2-1v-1-2-2z" class="m"></path><path d="M629 400c0-2 0-4 1-6h1l2 1 2 1c-2 2-2 3-3 5l-3-1z" class="I"></path><path d="M621 407c0-2 1-3 2-5 0-1 1-2 2-4 1 0 1 1 2 1 1 1 1 2 1 4-1 1-4 4-4 5l-1 2c-1-1-2-2-2-3zm-5-15h1 0c1 2 2 3 4 4 0 1 0 1-1 2v2h0c-1 1-1 1-1 2 0 2-1 4-2 6l-1-1v-1c0-2-2-3-3-4h-1c0-1 1-2 1-3 1-1 1-1 1-2 1-2 1-3 2-5z" class="n"></path><path d="M661 343h2c0 2 1 1 0 3 0 1 0 1 1 1v1l-1 3-6 11 1 1v2l-1 2h-2v2h1 1c1 2 0 2 0 3 1 1 1 2 1 3s-1 1-1 2c-2 2-3 4-3 7l-1 2-10 23h-2l1-1v-1c1-1 1-1 1-2h-1v1l-1 1v-1h0l-5-3-2-1-2-1c1-2 1-3 3-5l-2-1-2-1v-1c-1 0-2-1-3-2l-1-1-1-1h-1-1-1-2c1-2 2-2 2-4 1-1 2-3 4-3v1h1c0-1 0-2-1-3l1-1v-2c1-1 1-1 2-3h3c1-2 1-3 3-4l1-1-1-2h-1c0-1-1-2 0-4 1 1 1 2 2 3 1-1 3-2 4-4v-1c2-1 4-3 6-5l9-10v2s0 1 1 1c1-2 3-4 4-6z" class="h"></path><path d="M641 362c0 1 0 2-1 2 0 1-1 2-2 3h3c0-1 0 0 1-1h1c0 1-1 3-2 4l-2 2c0 2-1 2 0 4h0c1 0 1 0 1 1l-1 2-1-1-1-1c1-1 1-2 0-4h0-2c1-1 1-1 1-2v-1l1-1-1-2h-1c0-1-1-2 0-4 1 1 1 2 2 3 1-1 3-2 4-4z" class="m"></path><path d="M657 362l1 1v2l-1 2h-2v2h1 1c1 2 0 2 0 3 1 1 1 2 1 3s-1 1-1 2h-1c-1 0 0 0-1-1v-1c1-1 1-1 1-2l-1-1c-1 0-2 1-3 2v3h-2c-1 0-1 2-1 3l-1 1h0c-2 2-3 2-4 4s-2 3-4 4l-1-1-2-2-1-3 3-2h1l1 1c1 0 1 0 2-1l-2-2v-1l1 1h1l1-1c-1-1-2-1-2-3v-1h0v1c1 1 1 2 3 2h1c-1-1-1-1-1-2l1-1 2-2h0l-1-1c1-1 2 0 3 1 1-1 1-2 2-4-1 0-2-1-2-1h-2l-1-1h5 1c2 0 3-2 4-4z" class="o"></path><path d="M661 343h2c0 2 1 1 0 3 0 1 0 1 1 1v1l-1 3-6 11c-1 2-2 4-4 4h-1-5-2-1c3-3 4-5 6-7 1-2 2-3 3-4l4-6c1-2 3-4 4-6z" class="v"></path><path d="M633 374c1-2 1-3 3-4v1c0 1 0 1-1 2h2 0c1 2 1 3 0 4l1 1 1 1c0 1 1 1 1 2h-1l-3 2 1 3 2 2 1 1c-1 2-2 3-3 4-1 0-1 0-1-1-1 0-3 2-3 3l-2-1v-1c-1 0-2-1-3-2l-1-1-1-1h-1-1-1-2c1-2 2-2 2-4 1-1 2-3 4-3v1h1c0-1 0-2-1-3l1-1v-2c1-1 1-1 2-3h3z" class="z"></path><path d="M632 376v1c1 1 0 1 1 2s1 2 1 4l-1 2h1c1 0 1 0 2 1h-4c-1 0-1-1-2-2 1-2 2-4 1-5v-1l1-2z" class="u"></path><path d="M637 386l2 2 1 1c-1 2-2 3-3 4-1 0-1 0-1-1-1 0-3 2-3 3l-2-1v-1c-1 0-2-1-3-2v-2h0l1 1h1 1v-2h1c1 0 1-1 2-1 0 0 1 1 2 1v-2h1z" class="W"></path><path d="M633 374c1-2 1-3 3-4v1c0 1 0 1-1 2h2 0c1 2 1 3 0 4l1 1 1 1c0 1 1 1 1 2h-1l-3 2 1 3h-1 0c-1-1-1-1-2-1h-1l1-2c0-2 0-3-1-4s0-1-1-2v-1h1v-2z" class="Z"></path><path d="M633 374c1-2 1-3 3-4v1c0 1 0 1-1 2h2 0c-2 2-2 2-4 3v-2z" class="u"></path><path d="M640 389c2-1 3-2 4-4s2-2 4-4h0l1-1c0-1 0-3 1-3h2v-3c1-1 2-2 3-2l1 1c0 1 0 1-1 2v1c1 1 0 1 1 1h1c-2 2-3 4-3 7l-1 2-10 23h-2l1-1v-1c1-1 1-1 1-2h-1v1l-1 1v-1h0l-5-3-2-1-2-1c1-2 1-3 3-5l-2-1c0-1 2-3 3-3 0 1 0 1 1 1 1-1 2-2 3-4z" class="x"></path><g class="T"><path d="M635 396c1 1 3 2 3 2l-2 2c0 1-1 2 0 3l-2-1-2-1c1-2 1-3 3-5z"></path><path d="M640 389c1 1 2 1 4 0h1 1c1-1 2-2 2-3l3-1c0 1-1 2-2 3l1 1v1c-1 1-2 2-2 3-1 2-4 4-4 6-1 1 0 1-1 2-2-1-3-1-4-2l-1-1s-2-1-3-2l-2-1c0-1 2-3 3-3 0 1 0 1 1 1 1-1 2-2 3-4z"></path></g><path d="M633 395c0-1 2-3 3-3 0 1 0 1 1 1 2 1 3 2 3 5 0 0-1 0-1 1l-1-1s-2-1-3-2l-2-1z" class="k"></path><path d="M634 402l2 1 5 3h0v1l1-1v-1h1c0 1 0 1-1 2v1l-1 1h2l-12 26-1 2-3 6-5 11c0 1 0 1-1 2v1c-1 2-1 4-1 5v1c-2 2-2 3-3 4v2l-1-1c-1 1-2 2-3 4 0 1-4 4-5 4l7-12c0-2 0-2-1-3-1 0-2 1-2 1l-1-1c-1 1-2 1-3 3v2l-2-1c0-1 0-2-1-2l-5-6c1-1 1-2 2-2h2v-1c-1-2-1-2-2-3-1 1-1 1-2 1l-4-7 1-2c-1-2-1-4-2-5h-2v-2-1l1-1c0 1 1 1 2 2h-1c1 2 2 2 2 4h1c0-1 0-1 1-1l3-1v-2l1-1c1 0 2 0 3 1h1c1 1 2-1 3-1l1-2 6-7c1-1 2-3 3-4 0-1 1-2 2-2l-1-1 1-2h1c0-3 1-5 2-7 2-1 2-3 3-5 1-1 1-2 2-3l2 1c0 1 0 2-1 3h2l-1-1c0-1 1-2 2-3z" class="o"></path><path d="M600 457c1-1 1-2 2-2h2l1 1c0 2 2 3 3 4s1 1 2 1h1 0c-1 1-2 1-3 3v2l-2-1c0-1 0-2-1-2l-5-6z" class="T"></path><path d="M634 402l2 1 5 3h0l-2 2c-1 1-2 2-2 5h-1-1l-1-1c-1-1-1-1-1-2h0-1v-1c1-1 1-1 1-2v-1l-1-1c0-1 1-2 2-3z" class="w"></path><path d="M627 443l-5 11c0 1 0 1-1 2v1c-1 2-1 4-1 5v1c-2 2-2 3-3 4v2l-1-1c-1 1-2 2-3 4 0 1-4 4-5 4l7-12c4-5 6-10 9-15l3-6z" class="H"></path><path d="M625 410c2-1 2-3 3-5 1-1 1-2 2-3l2 1c0 1 0 2-1 3h2v1c0 1 0 1-1 2v1h1 0c0 1 0 1 1 2v1l-5 5h0v2l1 1c-1 1-1 2-2 3v-4l-1-1v-1l-3-1v1c-1 1-2 1-2 2l-1-1 1-2h1c0-3 1-5 2-7z" class="m"></path><path d="M625 410c2-1 2-3 3-5 1-1 1-2 2-3l2 1c0 1 0 2-1 3v1 2c-1 1-2 2-2 3-1 2-2 3-4 5h-2c0-3 1-5 2-7z" class="n"></path><path d="M622 420c0-1 1-1 2-2v-1l3 1v1l1 1v4c-1 2-2 4-3 7-2 3-3 6-5 9 0 1 0 1-1 1-1 1-1 2-2 3l-1 1v1c1 0 2 1 3 2l-3 3c-3 1-4 7-7 7-1 0-2-1-2-1v-2c-1 0-2 1-2 1l-1-1v-1c-1-2-1-2-2-3-1 1-1 1-2 1l-4-7 1-2c-1-2-1-4-2-5h-2v-2-1l1-1c0 1 1 1 2 2h-1c1 2 2 2 2 4h1c0-1 0-1 1-1l3-1v-2l1-1c1 0 2 0 3 1h1c1 1 2-1 3-1l1-2 6-7c1-1 2-3 3-4 0-1 1-2 2-2z" class="AT"></path><path d="M627 419l1 1v4c-1 2-2 4-3 7l-1-3v-2l1-1v-2c1-1 1-2 1-3h0l1-1z" class="u"></path><path d="M616 439l4-4 1 1c0 1-1 2-1 4 0 1 0 1-1 1-1 1-1 2-2 3h-2v-3c0-1 0-1 1-2z" class="n"></path><path d="M622 420c0-1 1-1 2-2v-1l3 1v1l-1 1h0c-3 2-4 5-6 8-1 1-1 2-2 3-1-2 0-1 0-3 0 0-1-1-1-2 1-1 2-3 3-4 0-1 1-2 2-2z" class="AQ"></path><path d="M617 426c1-1 2-3 3-4v5 1c-1 1-1 2-2 3-1-2 0-1 0-3 0 0-1-1-1-2z" class="w"></path><path d="M617 426c0 1 1 2 1 2 0 2-1 1 0 3l-2 3-3 3-1-1c-3 1-3 4-5 5l-1-1c1-2 3-3 4-5l1-2 6-7z" class="Z"></path><path d="M617 426c0 1 1 2 1 2 0 2-1 1 0 3l-2 3-1-1c1-1 1-2 1-2l-1-1-3 4-1-1 6-7z" class="V"></path><path d="M594 434c0 1 1 1 2 2h-1c1 2 2 2 2 4h1c0-1 0-1 1-1l3-1v-2l1-1c1 0 2 0 3 1h1c1 1 2-1 3-1-1 2-3 3-4 5l1 1c-1 1-2 2-2 3s-1 2-2 3v1l-1-1h-1v1l1 2v1c-1 1-1 1-2 1l-4-7 1-2c-1-2-1-4-2-5h-2v-2-1l1-1z" class="Q"></path><path d="M599 439l3-1c0 1-1 2-1 4l-1 3h-1v-4l-1-1c0-1 0-1 1-1z" class="z"></path><path d="M606 440l1 1c-1 1-2 2-2 3s-1 2-2 3v1l-1-1h-1v1l1 2v1c-1 1-1 1-2 1l-4-7 1-2s1 1 1 2 0 1 1 1l2-1c1-2 1-3 2-3l3-2z" class="k"></path><path d="M613 440h0l1-1h2c-1 1-1 1-1 2v3h2l-1 1v1c1 0 2 1 3 2l-3 3c-3 1-4 7-7 7-1 0-2-1-2-1v-2c-1 0-2 1-2 1l-1-1v-1c-1-2-1-2-2-3v-1c2-2 3-4 5-5v1c2-1 2-2 3-3l1-1 2-2h0z" class="Z"></path><path d="M613 440h0l1-1h2c-1 1-1 1-1 2v3h2l-1 1v1 1c-1 1-1 2-2 3-1-2-2-3-3-3h-1-1c0-1 1-2 1-3 2 0 2 0 3-1 0-1 0-1 1-2l-1-1z" class="u"></path><path d="M566 429c0-1-1-1-1-1l-1 2h0v-4c-1-5-3-8-2-13v-2h0c0-3 1-13 3-15l1 1c1 2 1 3 1 6 1 3 2 7 3 10 0 1 0 2 1 4l2 9-1-1c-1 0-1 0-2 1l2 3 2 8 1 7 2 5c1 3 1 6 2 8h0c1 3 2 7 3 9l1 1c0 1 1 1 1 2l3-1c0 1 1 2 1 3l1 1c1-1 1-2 2-3l2-2c1-2 2-5 3-7h0c0-2 1-3 2-5v-1l2 3 5 6c1 0 1 1 1 2l2 1v-2c1-2 2-2 3-3l1 1s1-1 2-1c1 1 1 1 1 3l-7 12c-1 3-3 5-4 7-1 1-2 2-3 4l-10 9-1 1-2 1h-1-1c-1 1-2 2-4 2l-1 1c-2 0-4 0-5 1 0 1 0 1-1 1v-4c-1-1-1-1-3-1v2c-1 1 0 1 0 3l-1 1c-1 1-1 3-3 4v-1l-1 1v-1c0-1 1-2 1-3 1-2 1-3 2-5h1c-1-1-2-2-2-3l-1 1h-1c0-3-3-5-4-8 0-1-1-3-2-4-1-3-2-6-4-8-2-5-6-14-6-19 0 1 1 2 1 3l2 2v1c1-2 1-2 1-4l8-16s1-1 1-2c0-2 1-4 1-5 1-2 2-3 2-4-1-1-1-3-1-4h0z" class="U"></path><path d="M566 429l2 1h0c0 1 0 1 1 2v2 1c0-1-1-1-2-2s-1-3-1-4z" class="H"></path><path d="M566 454l1 1c-1 3-1 5-3 7h0c-1 0-1 0-2-1 1-2 3-5 4-7z" class="Q"></path><path d="M572 462l-1-1c1-1 2-1 3-2v-1c0-1 0-2 1-2h0 1c1 1 1 0 1 1 1 1 1 2 1 3-1 2-2 3-3 4 0-1 0-1-1-2l-1 2-1-2z" class="R"></path><path d="M562 461c1 1 1 1 2 1l-5 12-1 1-1-2 1-1c0-4 2-7 4-11z" class="AT"></path><path d="M570 426c-1-5-1-8-2-13v-2l2 2c0 1 0 2 1 4l2 9-1-1c-1 0-1 0-2 1z" class="n"></path><path d="M579 457h0l3 9 1 1c-2 1-3 1-3 3l-1 1v1c-1 0-2 1-3 0l1-4h0l-3 2c0-2 1-3 2-4 1-2 3-5 3-7h0v-2z" class="Q"></path><path d="M577 468h1c0-1 1-2 1-4h1c1 2 1 2 0 4 0 1-1 2-1 3v1c-1 0-2 1-3 0l1-4z" class="R"></path><path d="M566 429c0-1-1-1-1-1l-1 2h0v-4c-1-5-3-8-2-13v-2h0c0-3 1-13 3-15l1 1c1 2 1 3 1 6-1 1-1 1-1 3s1 5 1 7c-1-2-1-4-1-5-1-1-1-3-1-4l1-1h1l-1-1v-2c0-1 0-2-1-3v1c-1 1-1 3-1 5-1 1-1 2-1 4v2c0 1 0 2 1 3v1l1 1v4c0 1 1 3 1 5 0 1 0 2 1 4l-1 2z" class="E"></path><path d="M567 433c1 1 2 1 2 2v2c1 2 2 3 2 5-1 1-1 1-1 2 0 2 1 3-1 5l-1 1c0 2-2 4-2 4-1 2-3 5-4 7-2 4-4 7-4 11l-1 1-5-12 2 2v1c1-2 1-2 1-4l8-16s1-1 1-2c0-2 1-4 1-5 1-2 2-3 2-4z" class="c"></path><path d="M569 437c1 2 2 3 2 5-1 1-1 1-1 2l-1-1c-1-2 0-4 0-6zm-14 23l8-16c1 2 0 3 0 5l-2 3c0 1-1 2-1 3l-2 3c0 1 0 2-1 2v2h-1s0-1-1-2z" class="H"></path><path d="M559 474c1 1 0 1 1 1v1l1-1v-1-1c0-1 0-1 1-2h0c0-1 1-2 1-3 2-4 3-7 6-9l-1 3c-1 3-3 6-4 8l-1 4 2-2 1-1c0-1 1-1 1-2 1-2 2-3 3-4 0-1 1-2 2-3l1 2h-1c0 1 0 2-1 3l-1 1v1c0 1-1 1-1 2 0 2-2 5-2 6 0 0 0 1 1 2v-1c0-2 3-3 3-5v-1h1l2-2 3-2h0l-1 4c1 1 2 0 3 0l-1 2c-1 2-3 5-4 7l-1 2-1 1-1 2h-1c-1-1-1-2-2-3v1c-1 1-1 2-1 4h-2-1l-6-13 1-1z" class="n"></path><path d="M574 470l3-2h0l-1 4-4 5c-1 2-2 4-4 6v1c-1 1-1 2-1 4h-2c0-3 2-6 3-8 2-3 3-6 4-8l2-2z" class="k"></path><path d="M572 477l4-5c1 1 2 0 3 0l-1 2c-1 2-3 5-4 7l-1 2-1 1-1 2h-1c-1-1-1-2-2-3 2-2 3-4 4-6z" class="n"></path><path d="M572 477l4-5c1 1 2 0 3 0l-1 2c-1 0-1 0-2 1-1 0-1 1-2 1l-1 2-1-1z" class="H"></path><path d="M557 477c-2-5-6-14-6-19 0 1 1 2 1 3l5 12 1 2 6 13h1 2c0-2 0-3 1-4v-1c1 1 1 2 2 3h1l1-2 1-1 1-2c1-2 3-5 4-7l1-2v-1l1-1c0-2 1-2 3-3 0 1 1 1 1 2l3-1c0 1 1 2 1 3l1 1c-1 2-2 2-2 4l1 1 1-1c1 1 1 2 1 4l1 6c1-1 1-2 1-4 1 1 1 2 1 3v4c-2 2-7 6-11 7-3 1-4 3-7 3-1-1-1-1-3-1v2c-1 1 0 1 0 3l-1 1c-1 1-1 3-3 4v-1l-1 1v-1c0-1 1-2 1-3 1-2 1-3 2-5h1c-1-1-2-2-2-3l-1 1h-1c0-3-3-5-4-8 0-1-1-3-2-4-1-3-2-6-4-8z" class="w"></path><path d="M566 491c2 0 4-1 5-3l-2 7c-1-1-3-2-3-4z" class="AT"></path><path d="M568 483c1 1 1 2 2 3h1l1-2 1-1v2l-2 3c-1 2-3 3-5 3 0-1-1-2-2-3h1 2c0-2 0-3 1-4v-1z" class="Q"></path><path d="M580 491c0 1 0 0 1 1 0 0 1-1 1-2l2-1c0 1-1 2-2 3l1 1c2-1 3-3 4-6 1-1 2-3 3-4v-3l1 6s-1 1-1 2c-1 1 0 1-1 2s-2 1-2 2l-2 2h-1c-2 1-2 1-4 0h-1l-1-1-3 3h-1c0-2-1-3 0-5v1c1 1 1 1 1 2 1-1 1-2 2-3h3z" class="j"></path><path d="M580 491v-2c-1 1-2 0-4 0h0 3c1 0 1-1 2-2l-1-1 1-1c0-1 1-2 2-3 0-1 2-2 2-3 0-2 1-2 2-3h0l1 1 1-1c1 1 1 2 1 4v3c-1 1-2 3-3 4-1 3-2 5-4 6l-1-1c1-1 2-2 2-3l-2 1c0 1-1 2-1 2-1-1-1 0-1-1z" class="k"></path><path d="M583 467c0 1 1 1 1 2l3-1c0 1 1 2 1 3l1 1c-1 2-2 2-2 4h0c-1 1-2 1-2 3 0 1-2 2-2 3-1 1-2 2-2 3l-1 1 1 1c-1 1-1 2-2 2h-3 0c2 0 3 1 4 0v2h-3c-1 1-1 2-2 3 0-1 0-1-1-2v-1h0v-3c-1-1-1-2-1-3v-2l1-2c1-2 3-5 4-7l1-2v-1l1-1c0-2 1-2 3-3z" class="u"></path><path d="M577 480v-1l1-2h1v2h0v2l1 1c0 1 0 2-1 2h-3l1-4z" class="AT"></path><path d="M587 468c0 1 1 2 1 3l1 1c-1 2-2 2-2 4h0l-1-1c-1-2-1-4-2-6l3-1z" class="I"></path><path d="M574 481l2-2 1 1-1 4c0 2-1 4-2 7v-3c-1-1-1-2-1-3v-2l1-2z" class="V"></path><path d="M598 454l2 3 5 6c1 0 1 1 1 2l2 1v-2c1-2 2-2 3-3l1 1s1-1 2-1c1 1 1 1 1 3l-7 12c-1 3-3 5-4 7-1 1-2 2-3 4l-10 9-1 1-2 1h-1-1c-1 1-2 2-4 2l-1 1c-2 0-4 0-5 1 0 1 0 1-1 1v-4c3 0 4-2 7-3 4-1 9-5 11-7v-4c0-1 0-2-1-3 0 2 0 3-1 4l-1-6c0-2 0-3-1-4l-1 1-1-1c0-2 1-2 2-4 1-1 1-2 2-3l2-2c1-2 2-5 3-7h0c0-2 1-3 2-5v-1z" class="p"></path><path d="M589 476c1-1 2-3 3-4 0 1 1 3 1 4v2 2s0 2-1 2c0 2 0 3-1 4l-1-6c0-2 0-3-1-4z" class="V"></path><path d="M593 476c1 2 2 3 4 4v4c-1 1-2 3-4 5v-4c0-1 0-2-1-3 1 0 1-2 1-2v-2-2z" class="T"></path><path d="M598 454l2 3 5 6c1 0 1 1 1 2l2 1c0 1 1 2 0 2l-2 3c-1 1-1 2-2 4 0 1-1 1-2 2-2-2-4-4-5-6h0l-2-1v-1l-1 1h0-1c1-1 1-2 1-3l3-6-1-1h0c0-2 1-3 2-5v-1z" class="j"></path><path d="M606 465l2 1c0 1 1 2 0 2l-2 3-1-1v-1c1-2 1-2 1-4zm-9 3v-2l3-5h2 1v1h-2v2 3 1l-2 2h-1v-2h-1z" class="Z"></path><path d="M598 454l2 3 5 6-2-1v-1h-1-2l-3 5v2 1l-3-2 3-6-1-1h0c0-2 1-3 2-5v-1z" class="u"></path><path d="M598 454l2 3 5 6-2-1v-1l-1-1h-1l-3-1v-4-1z" class="z"></path><path d="M678 244l1 8v2c2 9 2 19 1 28v6c-1 3-2 7-2 11-1 4-1 12-4 15v1 2c0 3-2 7-4 10v1 2c-1 1-1 2-1 3v1l-8 9c-1 2-3 4-4 6-1 0-1-1-1-1v-2l-9 10c-2 2-4 4-6 5v1c-1 2-3 3-4 4-1-1-1-2-2-3-1 2 0 3 0 4-1 1-1 1-3 1l-1-2v-2c-1-2-1-2-1-3v-2c-1-2 0-2 0-3v-2s1-2 1-3 0 0 1-1v-3h-1v1 1h-3c-1 1-1 2-2 2l-1 1h-1-1-1l-1-1-1 1-2-1h0v1 1h-1v-1l1-3c-1-1-1-3-1-5v-4-1-1c0-2 0-4 1-6 1 0 2-3 3-4l11-14h0l-1-1c0-1 1-2 1-3v-1l4-5-1 1h-1v-3l3-4c-1-3 0-4 0-7l6-7c10-12 23-22 32-35l3-5z" class="g"></path><path d="M668 324v-1h0l3-1-2-2h0c0-1 0-2 1-2 0-1 2-2 2-2v-1c1-1 1-1 2-1v1 2c0 3-2 7-4 10v1 2c-1 1-1 2-1 3v1l-8 9c-1 2-3 4-4 6-1 0-1-1-1-1v-2-1c-1 1-1 1-2 0h-1 0c-1 1-2 1-3 2 0 1 1 1 1 2l-2 1h0l-1-1v-1h-1c0 2 0 3-2 4v-1h-2c1-1 1-1 1-2l-1-1c0-1 0-1 1-2s2 0 4 0h1v-1h0c-2-1-2-1-4 0v-1l1-2c-1 0 0 0-1-1h3 0l-1-1v-1-1c0-1 1-1 2-2 0 1 1 1 1 2l1 1v-1-1-1l3 1v1l-1 1-1 1h-1 0v2h2 1v-1c1-1 0-3 2-5v1l1 1h1v-2c-1-1 1-2 1-3l-2-2 1-1c0 1 1 1 1 1 2 0 3 0 4 1h1c0-1 1-1 1-2v-2-2l1-1c1 0 2-1 2-1z" class="v"></path><path d="M668 324l2 1c-1 2-3 3-5 5v-2-2l1-1c1 0 2-1 2-1z" class="D"></path><path d="M670 328v2c-1 1-1 2-1 3v1l-8 9c-1 2-3 4-4 6-1 0-1-1-1-1v-2-1c1-1 3-3 4-5l10-12z" class="AM"></path><path d="M642 316l-2-1 1-1v-1l-1-1h-1v-1-4c1-1 2-1 3-1h0c0-2 1-2 2-3h1v1h-1c0 2 0 2 1 3 1-1 1-1 2-1l1 1s1 0 2 1c1 0 2 1 3 0 1 0 1 0 2-1s1 1 2 1h2 2c1 0 1 1 2 1v1l1-2v-1c1-1 2-1 3-2h1 1v3c-1 1-1 1-1 3h-2l1 1h0c1 0 1 0 1 1v1c-1 1-1 1-1 2 0 2-2 3-3 5h-1l1-2h-1l-2 2h0l-1-1v-2c-1-2-2-2-2-4h1c0-3-1-2-2-3v-2l-2-1c-1 1-2 3-2 5h-1c0 1 0 2-1 2 1 1 1 1 2 1v2h-2c0 1-2 1-2 2s-1 1-1 2v1 2 1l-1 1c-1-1-1-1-1-2v-1l-1-1h-1l-1 1 1 1-1 1v1c0 1 1 0 0 1h0c-1 0-1-1-2-1v-1l-1-1h-1v1 1c0 1-1 2-2 4h-1-1l-1-1v-1c1-1 1-1 0-3h1l1-1c1 0 1 0 2-1v-2l1-1v-2h1v-1-1l2-1z" class="D"></path><path d="M642 316l1-2c1-1 1-1 2-1h1l1 4 1-1 1 1-3 3h-1c-1-1-1-1-3-1v1 1l4 2 1 1-1 1v-1l-1-1h-1l-1 1 1 1-1 1v1c0 1 1 0 0 1h0c-1 0-1-1-2-1v-1l-1-1h-1v1 1c0 1-1 2-2 4h-1-1l-1-1v-1c1-1 1-1 0-3h1l1-1c1 0 1 0 2-1v-2l1-1v-2h1v-1-1l2-1z" class="t"></path><path d="M645 313h1l1 4h-2c-1-2 0-3 0-4z" class="G"></path><path d="M632 314c0 2 0 3 1 4h-2c1 1 1 1 2 1l2-1v1h0c0 2 0 5-1 7 1 2 1 2 0 3v1l1 1h1 1c1-2 2-3 2-4v-1-1h1l1 1v1c1 0 1 1 2 1h0c2 1 2 0 3 1v1l-1 1c0 1-1 2-2 3v1c-2 0-2 0-4 2h1 0c1-1 2-1 3-1h0c1 1 1 1 2 1 0 1 1 2 1 3h1l1 1h0-3c1 1 0 1 1 1l-1 2v1c2-1 2-1 4 0h0v1h-1c-2 0-3-1-4 0s-1 1-1 2l1 1c0 1 0 1-1 2h2v1c2-1 2-2 2-4h1v1l1 1h0l2-1c0-1-1-1-1-2 1-1 2-1 3-2h0 1c1 1 1 1 2 0v1l-9 10c-2 2-4 4-6 5v1c-1 2-3 3-4 4-1-1-1-2-2-3-1 2 0 3 0 4-1 1-1 1-3 1l-1-2v-2c-1-2-1-2-1-3v-2c-1-2 0-2 0-3v-2s1-2 1-3 0 0 1-1v-3h-1v1 1h-3c-1 1-1 2-2 2l-1 1h-1-1-1l-1-1-1 1-2-1h0v1 1h-1v-1l1-3c-1-1-1-3-1-5v-4-1-1c0-2 0-4 1-6 1 0 2-3 3-4l11-14z" class="Ac"></path><path d="M617 340c2-2 4-3 4-5 0-1 0-1-1-2h0v-1l2 2 2-2v1 1h-1c-1 1-1 1 0 3v1l-1 1 1 2c-1 1-2 1-3 2h0c-2 0-2 0-3 1v-4zm18-9h1 1c1-2 2-3 2-4v-1-1h1l1 1v1c1 0 1 1 2 1h0c2 1 2 0 3 1v1l-1 1c0 1-1 2-2 3h-2c-2 0-2 2-4 3h-2c1-1 1-1 0-3 1-1 1-1 1-2-1 0-1 0-1-1z" class="AE"></path><path d="M623 337l1-1 1-1h1 1l1 1 1-1-2-3 1-1h0v1c0 1 0 1 1 2 0 0 1 1 1 2-1 0-1 1-2 2h1v2 2 1h0-2-2v1h1v2l-3 2 1 1 1-1 1 1h1 1 0c-1 1-1 2-2 2l-1 1h-1-1-1l-1-1-1 1-2-1h0v1 1h-1v-1l1-3c-1-1-1-3-1-5 1-1 1-1 3-1h0c1-1 2-1 3-2l-1-2 1-1v-1z" class="v"></path><path d="M678 244l1 8v2c2 9 2 19 1 28v6c-1 0-2 1-2 2-1 1 0 1-1 3v2c-2 2-2 2-4 3 0 0-1 0-1 1v1h-2v-2h-1v1l1 1h-1v2c-2 1-1 2-2 3s-2 1-3 2v1l-1 2v-1c-1 0-1-1-2-1h-2-2c-1 0-1-2-2-1s-1 1-2 1c-1 1-2 0-3 0-1-1-2-1-2-1l-1-1c-1 0-1 0-2 1-1-1-1-1-1-3h1v-1h-1c-1 1-2 1-2 3h0c-1 0-2 0-3 1v4 1h1l1 1v1l-1 1 2 1-2 1v1 1h-1v2l-1 1v2c-1 1-1 1-2 1l-1 1h-1c1-2 1-5 1-7h0v-1l-2 1c-1 0-1 0-2-1h2c-1-1-1-2-1-4h0l-1-1c0-1 1-2 1-3v-1l4-5-1 1h-1v-3l3-4c-1-3 0-4 0-7l6-7c10-12 23-22 32-35l3-5z" class="G"></path><path d="M673 262v3l-4 4c0 1-1 2-2 2v1h1l1 2c-1 0-1 1-2 1v1c0 1 0 1-1 2h0c-1 1-1 2-2 2l-2-1-1 2h1v1c-1 0-1 0-2-1l-3 2c1-3 5-5 6-8 1-2 2-4 4-6l6-7z" class="g"></path><path d="M646 297c0 1 0 2-2 4-1 1-2 2-3 4s-3 3-4 5h1v4l-1 1v1l-1 2h0l1 1c1-1 1-2 2-3l1 1h0v1 1h-1v2l-1 1v2c-1 1-1 1-2 1l-1 1h-1c1-2 1-5 1-7h0v-1l-2 1c-1 0-1 0-2-1h2c-1-1-1-2-1-4h0l-1-1c0-1 1-2 1-3v-1l4-5 4-4c0 2 0 2-1 4v1c3-3 5-6 7-8z" class="F"></path><path d="M640 300c0 2 0 2-1 4v1l-7 9-1-1c0-1 1-2 1-3v-1l4-5 4-4z" class="T"></path><path d="M679 252v2l-6 8-6 7c-2 2-3 4-4 6-1 3-5 5-6 8l-11 14c-2 2-4 5-7 8v-1c1-2 1-2 1-4 0-1 2-3 3-4l7-8 28-35 1-1z" class="AM"></path><path d="M678 244l1 8-1 1-28 35-7 8c-1 1-3 3-3 4l-4 4-1 1h-1v-3l3-4c-1-3 0-4 0-7l6-7c10-12 23-22 32-35l3-5z" class="F"></path><path d="M643 284l1 1h1 0 2l-10 13c-1-3 0-4 0-7l6-7z" class="T"></path><path d="M678 244l1 8-1 1c0-2 0-2-1-3-3 1-7 7-9 10l-12 14c-3 3-6 7-9 11h-2 0-1l-1-1c10-12 23-22 32-35l3-5z" class="l"></path><path d="M513 269c2 1 2 2 3 3 1 0 2 0 3 1 1-1 2 0 3 0s2 0 2 1c2 1 3 2 5 3h0c1 1 2 2 2 4h-1l3 3h-1c1 1 2 2 4 3s4 4 6 5v1c2-1 3-1 5-2 1 0 1 0 1-1h2s0 1 1 2c1-1 2-1 3-1v1h1 1v-1c2 0 2 1 3 2h0 0v1c1 1 2 1 3 2h-2v1h1c2 1 3 3 5 4l1 1h1c1 0 2 0 3-1s2-2 2-5l1 1v2h2v3c0 1 0 1 1 2l1-1v2 1l-1 3c-1 0-2 0-3 1s-2 2-2 4c-1 1-1 1-2 1-1 1 0 2 1 3 0 1-1 3 0 5h0c0 3-1 4-2 6h-1-1l1 2 1 1 2-1h0c1 1 2 2 3 2v1l-1 1v1l1 4c-1 2-3 5-4 7h0c-2 4-5 9-5 13 0 1-1 2-1 3h1v5l1 5h-1-1v-2l-1 1v1h-2c-1 3-2 5-4 8l-1-3-4 7-3 6c-1 0-1 2-2 3s-2 2-4 2h0c-2 2-3 5-4 7s-3 4-4 6c-1 0-1 1-2 2l1 1-2 2-3 6-1 1 1 1v2c1 0 1 0 2-1l1 1c-1 1-1 1-1 3v2-1l-1-1-1 1-1 2c-1 1-2 1-3 1l-1-1v3h-1 0c-1 3 0 5-1 7v-6c-2 3-1 7-2 9l-2 17c-1 3-1 7-1 10-1 3-1 5-1 7 1 2 1 4 2 5 0 3 2 5 3 8l3 6c0 2 0 3-1 5v3 2h1c0 2 0 4-1 6 0 2-1 6-1 9h1l1 1 1-2 1-1c0 2 1 2 2 3l1 1v4l-1-1-2 2c-2 3-2 6-3 9h-1l-2 9h0l-1-2v5l-1 1h-1c0 2 0 3-1 4l-1 1v7c-1 6-2 12-2 19l-1 12-1 1c-2-2-2-6-2-8 0-1-1-2-1-3v-3c-1-1-1-2-1-3 1-1 0-2 0-3s-1-2-1-2v-1h0l-1 1v2c1 2 0 2-2 4v-4c0-2 0-3-1-5 0-1-1-2-1-3v-1h-2c0-1 0-3-1-4v1c0 1-1 1-2 2-2-2-1-5-1-7s-1-2-1-3l-1 1c-1 0-1 0-1-1v-1h-1v-2l-2-1c-1-4-3-8-5-12v-2l-1-3c1 0 1 0 1-1v-1-1h-1v2h0l-2-3-1-1h-1v1h-2c-1-1-1-1-2-1v-1 1h-1c-1 0-1 0-2 1l-1-2-4-2-3 4-4 5c-1 1-2 2-2 3l-1 1-1 1h-1-1l-7-11c-3-3-5-7-7-10-3-4-6-9-8-13-3-4-5-9-8-14-1-1-2-3-2-4-2-2-3-5-5-7l-2-5c0-1 0-2-1-3v-1l2-1s-1-1-1-2c-2-2-4-3-4-6h2l-2-3c0-2-1-3-1-4l1-1-2-3-1 1h-1-1c0-1 0-2-1-3v-1c0-1 1-2 1-3 0-2 1-5 1-7l1-1h0v-1l1-1c0 2 1 3 2 3l2-1-1-1h3 0l2-1s1 0 1-1c2 0 3-1 4-1h1l1-3 1 1-1 2c1 2 1 1 2 2l3-3c2-1 3-1 5-3 3-3 3-8 7-11l1 1-2 3c2 0 2-1 3-2 1-3 4-6 4-8 2-5 1-11 1-17 0-1 0-4 1-6v-4c1 1 1 2 2 3v-1c0-3 0-6 1-9l2-7c1-4 1-7 0-10v-1-2c-1-1-1-3-2-5l-6-12h-1c0 1 0 1-1 2h-1l1-1v-1c1-3-1-7-3-10h1c0-1-1-2 0-2l2 2h1l-1-1 2-2c2 1 4 3 7 3 0 1 1 1 1 3h1 1l2-2c3-1 5-1 8-1h1c2 1 3 1 5 2l4 2c2 1 5 3 6 6 1 2 2 3 3 4 2 2 3 4 4 6l3 4 3 6c1 4 2 7 3 11 1-1 1 0 1-1l1-1h0v1 1h1c1-1 2-2 2-4l2-1c1-1 1-1 1-3v-1l1-8v-2c1-2 0-4 1-7 0-1 0-3 1-4l-1 10v7c1 1 1 2 1 3v1c1-3 0-7 0-10l1-19v-1l1-1-1-1c1-1 1 0 1-1v-1l1-1s0-1 1-2h0l-1-2c1-1 1-2 1-4l1-2h1 1l1-2s1-2 1-3c1-1 1-1 1-3 0-1 0-2-1-3 0-4-1-9-2-13l-1-12 1-3c-2-3-2-7-3-10z" class="AP"></path><path d="M449 332l4 1v1c0 1 0 1 1 2v1l-1 1h0c-2-2-4-3-4-6z" class="S"></path><path d="M550 346h0c0-1 0 0 1-1 0-1 0-2 1-2h0l1 1c1 0 2-1 3-2l-3 5-1 1c-1-1-2-1-2-2z" class="E"></path><path d="M570 347c0-2 0-5 1-7 0-3 0-3 2-4l1 4c-1 2-3 5-4 7z" class="B"></path><path d="M504 490c1-2 1-5 2-7l1 3h0c-1 4-1 8-1 12l-1-4h-1l-1-2 1-2z" class="AI"></path><path d="M556 342h0 1l1-2h0c0 2 1 5 0 7l-2 2-3-1v-1l3-5z" class="R"></path><path d="M462 344h0c0-2-1-4 1-5v-1 1c1 1 1 1 2 1 1 2 1 4 1 5v5 1c0 1 0 1 1 2l-1 1h0c-1-2-2-4-2-7 0-1-1-2-2-3z" class="AI"></path><path d="M519 362l2 2c0 2 0 4 1 6h1l-1 5v2 1h-1c-2-2-4-4-5-6-1-1-1-2-1-3h1c0 1 0 2 1 3s2 3 4 4c0-2 0-4-1-5l-2-4v-1c1-1 1-3 1-4z" class="M"></path><path d="M523 395h1v5h-1v2s0 1-1 2c0 1 0 1-1 2l-2-1h1v-1h-1l-2-2v-2l1-1c0-1 0-2 1-3l1 1v1 1l3-4z" class="S"></path><path d="M550 346c0 1 1 1 2 2l1-1v1l3 1c0 1 0 3-1 4l-1-1-2-1c-1 0-2 1-4 1l-1 1h0-4s-1 1-2 1c0 1-1 1-2 1 1-1 2-3 3-3 3-2 6-3 8-6z" class="U"></path><path d="M553 348l3 1c0 1 0 3-1 4l-1-1-2-1c-1 0-2 1-4 1l5-4z" class="Q"></path><path d="M442 331c0-1-1-2 0-2l2 2h1l-1-1 2-2c2 1 4 3 7 3 0 1 1 1 1 3h1 1l-2 2c-1-1-1-1-1-2v-1l-4-1c-1-1-2-1-3-1 0 1 0 2 1 3 0 2 1 1 1 2 1 1 1 5 1 6-3-3-5-7-7-11z" class="B"></path><path d="M537 377c1-1 2-2 4-3h2c1-1 2-1 3-1v1c-1 0-3 1-4 2l-2 2c-2 2-4 4-5 6l-2 3h0v-2c0-1-1-2-1-3v-1l-1-1c1-1 4-2 6-3z" class="d"></path><path d="M533 385l1-2c-1-1-1-2-1-3h1c1-1 3-2 5-3l1 1c-2 2-4 4-5 6l-2 3h0v-2z" class="L"></path><path d="M467 368h1c1 1 3 2 4 3 2 1 4 3 6 3h2c2 2 5 4 9 6v1c-1 1-1 2-1 4v3l-2-3c-3-5-8-11-14-13-2-1-4-2-6-4h1z" class="P"></path><path d="M546 374h1c1 0 2-1 4-2v1 2l-5 2c-2 2-4 2-5 5-2 0-3 1-4 2 0 1 1 1 1 2l-1 2h-1v-1c0-1 0-2-1-3 1-2 3-4 5-6l2-2c1-1 3-2 4-2z" class="Y"></path><path d="M546 374h1c1 0 2-1 4-2v1 2l-5 2 1-1v-1h0l-3 1h-2c1-1 3-2 4-2z" class="E"></path><path d="M458 332c3-1 5-1 8-1h1c2 1 3 1 5 2l4 2c2 1 5 3 6 6l-1-1c-5-3-11-5-17-6-4-1-7 1-10 3v-1l2-2 2-2z" class="AN"></path><path d="M509 363c1-3 0-7 0-10l1-19v-1c2 6 1 14 2 20l-2 41-1-31z" class="AR"></path><path d="M501 462c1 1 1 4 1 6h0l1 1h1v-5l-1-2v4l-1-1v-2h0c0-2 0-2 1-3 0-2-1-5 1-6-1-1-1-1-1-2v-1c2 3 1 24 2 25l-2 2-1 3h0 0l-3 6c-1-2 0-5 0-7v-3l1-2c2-4 0-9 1-13z" class="AJ"></path><path d="M502 481c-1-5 0-9 2-13v7c-1 1-1 2-1 3h0l-1 3h0 0z" class="c"></path><path d="M461 361c2 1 5 3 6 5v2h-1c2 2 4 3 6 4 6 2 11 8 14 13h-2c-1-1-1-2-3-2-1 0-1-1-2-1v-1c0-1-5-3-6-4-4-2-7-4-10-8h1l-3-8z" class="E"></path><path d="M514 530c1 2 1 5 0 8 0 2 0 3 1 5v1 1c1 2 0 3 0 6l-1 3 1 1v7c-1 6-2 12-2 19l-1 12-1 1c-2-2-2-6-2-8 0-6-1-11-2-17v-1c1-2 1-3 1-4 1 2 1 3 1 5l1 3h-1v6c1 2 0 4 1 6v2 1l1 3c1-2 0-3 1-5 0-2-1-4 0-6v-5c1-1 1-4 1-5h0c1-1 1-1 0-2v-3c1-1 1-1 1-2h-1c0-1 0-1-1-1 1-2 1-4 1-6 0-1 1-3 0-4 0-1-1-1-1-2v-3c1-1 1-2 1-3h0v-2-1h0v-2c1-2 1-6 1-8z" class="U"></path><path d="M548 352c2 0 3-1 4-1l2 1 1 1v1l-1 1-1-2-1 1c0 1-1 1-1 2l-1 2h-1l-1-1c-1 0-5 4-6 4-1 1-1 1-2 1 1-1 0-1 1-2l-1 1h0c-1 1-2 1-2 2-1 1-1 2-2 3h-1 0l-2-1v-2c0-3 4-6 6-8 1 0 2 0 2-1 1 0 2-1 2-1h4 0l1-1z" class="w"></path><path d="M548 352c2 0 3-1 4-1l2 1c-2 1-3 2-4 3l-3 1-2-1 2-2h0l1-1z" class="z"></path><path d="M539 355c1 0 2 0 2-1 1 0 2-1 2-1h4l-2 2c-1 1-2 2-3 2s-1 1-2 1c-1 1 0 1-1 1l-2 2c0 1-1 2-2 3v2l-2-1v-2c0-3 4-6 6-8z" class="Q"></path><path d="M558 370c-1 2-1 3-2 4h-2c-2 3-4 6-4 10v3c-1 3-5 6-7 9h0c-2 2-3 5-4 7s-3 4-4 6c0-3 2-5 3-8 0-2 2-5 3-7 2-2 3-6 5-9l1-1h0v-1-1l-5-1-1 1c1-3 3-3 5-5l5-2c2-2 4-4 7-5z" class="M"></path><defs><linearGradient id="AI" x1="547.123" y1="385.924" x2="540.267" y2="396.002" xlink:href="#B"><stop offset="0" stop-color="#191919"></stop><stop offset="1" stop-color="#353333"></stop></linearGradient></defs><path fill="url(#AI)" d="M542 381c1 0 3 0 4-1h1v1l2 2c0 2-1 3-2 5-1 1-1 2-2 4-1 1-2 2-2 4-2 2-3 5-4 7s-3 4-4 6c0-3 2-5 3-8 0-2 2-5 3-7 2-2 3-6 5-9l1-1h0v-1-1l-5-1z"></path><path d="M551 366h1c1-1 3-2 3-3l1-1 1-1v-2c1-1 1-1 1-2h0c1 0 1-1 2-1v-1l1-1 1 1c-2 3-4 8-6 11l-3 3c2 0 3 0 5-2v3c-3 1-5 3-7 5v-2-1c-2 1-3 2-4 2h-1v-1c-1 0-2 0-3 1h-2c-2 1-3 2-4 3 0-2 1-3 1-4v-2c0-1 1-1 1-1l-1-1 1-2c1 0 1-1 2-1v2h0 3l2-1v1c2-1 4-2 5-2z" class="S"></path><path d="M553 369c2 0 3 0 5-2v3c-3 1-5 3-7 5v-2-1c-2 1-3 2-4 2h-1v-1l2-1c1-1 3-2 5-3z" class="Y"></path><path d="M502 501l2-2-1-1c1-2 1-4 0-6l1 2h1l1 4v11c0 2 1 3 1 4 0 3 0 5 1 8 0 1 0 3-1 4v2c1 0 1 1 1 2l-3-1-2-3h0c0-4 0-6-1-9v-6c-2-2-1-6-1-8l1-1z" class="c"></path><path d="M466 345h1c1 0 2 0 3-1l10 10c2 2 8 6 8 10 0 0 0 1-1 1-1 2-2 2-4 2v-1c0-1 1 0 1-2-1-2-2-4-4-5-3-2-6-2-9-4l-1-1c-1-1-1-2-3-1-1-1-1-1-1-2v-1-5z" class="AJ"></path><path d="M465 378h0c-2-2-3-5-4-8 0-4-2-7-3-11-1-2-1-2 0-3-1-1 0-3 0-4l3 9 3 8h-1c3 4 6 6 10 8 1 1 6 3 6 4v1l-8-2v1h0v1c1 3 3 5 4 7 1 1 2 3 3 5h-1 0c-1 0-1-1-2-2h0c-2-2-5-6-6-8 0-1-1-3-2-4v1l-1-1-1-2z" class="d"></path><path d="M466 380c1-1 1-1 1-2-1-1-2-2-3-4v-1h0v-1c2 2 3 4 5 6v1c1 1 2 1 2 1v1h0v1c1 3 3 5 4 7 1 1 2 3 3 5h-1 0c-1 0-1-1-2-2h0c-2-2-5-6-6-8 0-1-1-3-2-4v1l-1-1z" class="K"></path><path d="M556 349l2-2c-1 3-1 6-2 8 0 5-2 7-5 11-1 0-3 1-5 2v-1l-2 1h-3 0v-2c-1 0-1 1-2 1l-4-1h1c1-1 1-2 2-3 0-1 1-1 2-2h0l1-1c-1 1 0 1-1 2 1 0 1 0 2-1 1 0 5-4 6-4l1 1h1l1-2c0-1 1-1 1-2l1-1 1 2 1-1v-1c1-1 1-3 1-4z" class="T"></path><path d="M556 355c0 5-2 7-5 11-1 0-3 1-5 2v-1l-2 1h-3 0c1 0 2-1 4-1 5-3 8-6 11-12z" class="B"></path><path d="M462 344c1 1 2 2 2 3 0 3 1 5 2 7h0l1-1c2-1 2 0 3 1l1 1c3 2 6 2 9 4 2 1 3 3 4 5 0 2-1 1-1 2v1c-2 0-6 1-8-1l-5-3c-5-4-7-12-8-19z" class="T"></path><path d="M441 331h1c2 4 4 8 7 11l1 3h2c1 2 1 4 2 5h1s1 0 2-1c1 1 1 2 1 3s-1 3 0 4c-1 1-1 1 0 3 1 4 3 7 3 11 1 3 2 6 4 8h0v1c1 1 1 2 1 4v1c-1-1-2-2-3-2v-1c-1-2-2-4-2-7-1 0 0 0-2 1-2-1-3-2-5-4 1-4 1-7 0-10v-1-2c-1-1-1-3-2-5l-6-12h-1c0 1 0 1-1 2h-1l1-1v-1c1-3-1-7-3-10z" class="B"></path><path d="M441 331h1c2 4 4 8 7 11l1 3c2 4 4 8 6 13v3c1 2 0 2 0 4l1-1 1 1-1 1 1 1v4 1c1 1 1 1 1 3-2-1-3-2-5-4 1-4 1-7 0-10v-1-2c-1-1-1-3-2-5l-6-12h-1c0 1 0 1-1 2h-1l1-1v-1c1-3-1-7-3-10z" class="f"></path><path d="M503 525l2 3 3 1-1 10v10c0 4 0 8 1 12v3c0 1 0 2-1 4v1c1 6 2 11 2 17 0-1-1-2-1-3v-3c-1-1-1-2-1-3 1-1 0-2 0-3s-1-2-1-2v-1h0l-1 1v2c1 2 0 2-2 4v-4h1v-6c0-2 0-4-1-6 0-1 0-6-1-8l-1-11 1-1h0v-2l1-1v-5c-1-2-1-3-1-4v-1c1-1 1-2 1-4z" class="b"></path><path d="M505 528l3 1-1 10-1-1c-2-2-1-7-1-10z" class="U"></path><path d="M502 554l-1-11 1-1 5 27c1 6 2 11 2 17 0-1-1-2-1-3v-3c-1-1-1-2-1-3 1-1 0-2 0-3s-1-2-1-2v-1h0l-1 1v2c1 2 0 2-2 4v-4h1v-6c0-2 0-4-1-6 0-1 0-6-1-8z" class="V"></path><path d="M561 354c0-1 1-2 1-3h1l1-1c1 1 1 1 2 1 1-2 2-3 4-4-2 4-5 9-5 13 0 1-1 2-1 3h1v5l1 5h-1-1v-2l-1 1v1h-2c-1 3-2 5-4 8l-1-3-4 7-3 6c-1 0-1 2-2 3s-2 2-4 2c2-3 6-6 7-9v-3c0-4 2-7 4-10h2c1-1 1-2 2-4v-3c-2 2-3 2-5 2l3-3c2-3 4-8 6-11l-1-1z" class="Y"></path><path d="M562 355l1-1v-1c1 0 2-1 2-1 1 1 0 1 0 2-1 4-3 7-4 11-2 4-3 9-5 13l-4 7-3 6c-1 0-1 2-2 3s-2 2-4 2c2-3 6-6 7-9v-3c0-4 2-7 4-10h2c1-1 1-2 2-4v-3c-2 2-3 2-5 2l3-3c2-3 4-8 6-11z" class="f"></path><path d="M505 476c0 2 1 5 1 7-1 2-1 5-2 7l-1 2c1 2 1 4 0 6l1 1-2 2-1 1c0 2-1 6 1 8v6c1 3 1 5 1 9h0c0 2 0 3-1 4v1c0 1 0 2 1 4v5l-1 1-3-13c-1-5-1-11-2-16 0-6-1-11 1-17l4-13h0l1-3 2-2z" class="U"></path><path d="M499 508h0c1-1 1-4 2-6 0 2-1 6 1 8-1 2-1 5-1 8-2-4-2-7-2-10z" class="W"></path><path d="M500 497h2v3 1l-1 1c-1 2-1 5-2 6h0l-1-1c0-4 0-6 2-10z" class="u"></path><path d="M502 510v6c1 3 1 5 1 9h0c0 2 0 3-1 4v1l-1-12c0-3 0-6 1-8z" class="n"></path><path d="M505 476c0 2 1 5 1 7-1 2-1 5-2 7l-1 2c1 2 1 4 0 6l1 1-2 2v-1-3h-2c0-1 0-2 1-3l-2 3h0l-1 1v-4l4-13h0l1-3 2-2z" class="AC"></path><path d="M501 494c0-1 1-2 1-3v-1c1-1 1-2 2-3v3l-1 2c1 2 1 4 0 6l1 1-2 2v-1-3h-2c0-1 0-2 1-3z" class="R"></path><path d="M520 443v-5c1-2 0-8 1-10v-5-2h1v-1c1-2 1-3 1-4 1-2 3-4 5-6 1-1 1-2 2-3v-2l1-1v-2l1-1h0v2h1c1 0 1-1 2-2l1-2c0-1 1-2 1-3s1-2 1-3h1c-1 1-1 2-1 3v3-2l3-3c-1 2-3 5-3 7-1 3-3 5-3 8-1 0-1 1-2 2l1 1-2 2-3 6-1 1 1 1v2c1 0 1 0 2-1l1 1c-1 1-1 1-1 3v2-1l-1-1-1 1-1 2c-1 1-2 1-3 1l-1-1v3h-1 0c-1 3 0 5-1 7v-6c-2 3-1 7-2 9z" class="E"></path><path d="M528 412v-1l5-5c1-2 2-4 4-6l1 1c-1 3-3 5-3 8-1 0-1 1-2 2l1 1-2 2-3 6c-1-2-1-2-1-3-1-1 0-1-1-2 0 0 0-2 1-3z" class="J"></path><path d="M523 433c0-2 1-5 1-7v-1c-1-2 1-5 2-6-1 0-2 1-3 1 1-4 1-5 5-8-1 1-1 3-1 3 1 1 0 1 1 2 0 1 0 1 1 3l-1 1 1 1v2c1 0 1 0 2-1l1 1c-1 1-1 1-1 3v2-1l-1-1-1 1-1 2c-1 1-2 1-3 1l-1-1v3h-1z" class="B"></path><path d="M524 430c0-1 0-2 1-3v-2c0-2 1-4 3-6l1 1-1 1c-1 2-1 4-2 5 0 1 1 1 1 2s-1 1 0 2l2-2-1 2c-1 1-2 1-3 1l-1-1z" class="AI"></path><path d="M528 421l1 1v2c1 0 1 0 2-1l1 1c-1 1-1 1-1 3v2-1l-1-1-1 1-2 2c-1-1 0-1 0-2s-1-1-1-2c1-1 1-3 2-5z" class="C"></path><path d="M516 477c1 2 1 4 2 5 0 3 2 5 3 8l3 6c0 2 0 3-1 5v3 2h1c0 2 0 4-1 6 0 2-1 6-1 9h1l1 1 1-2 1-1c0 2 1 2 2 3l1 1v4l-1-1-2 2c-2 3-2 6-3 9h-1l-2 9h0l-1-2v5l-1 1h-1c0 2 0 3-1 4l-1 1-1-1 1-3c0-3 1-4 0-6v-1-1c-1-2-1-3-1-5 1-3 1-6 0-8l-1 1h0v-1c2-5 0-10 1-15l1-10c-1-5 0-10 0-15-1-1-1-1-1-2 1-1 1-6 1-7v-1h1v-3z" class="b"></path><path d="M517 519c1-2 1-4 1-6l1 2h0l1-1-1 2v5l-2-2z" class="U"></path><path d="M516 477c1 2 1 4 2 5 0 3 2 5 3 8l3 6-1 1v2h-1c-2-3-4-7-5-11 0-1 0-1-1-1-1 1-1 3-1 5h1c0 4 0 9-1 13-1-5 0-10 0-15-1-1-1-1-1-2 1-1 1-6 1-7v-1h1v-3z" class="AC"></path><path d="M514 530l-1 1h0v-1c2-5 0-10 1-15h1v12 2c0 2 0 6 1 8v-5l1-6v-7l2 2 1 1c-1 2-1 4-1 6v1c0 3-1 5-1 7 1-1 1-2 1-3h1c0 4 0 7-1 11v5l-1 1h-1c0 2 0 3-1 4l-1 1-1-1 1-3c0-3 1-4 0-6v-1-1c-1-2-1-3-1-5 1-3 1-6 0-8z" class="R"></path><path d="M517 519l2 2 1 1c-1 2-1 4-1 6-1 0-1-2-1-3l-1 4v-3-7z" class="H"></path><path d="M518 536c1-1 1-2 1-3h1c0 4 0 7-1 11v5l-1 1h-1c0 2 0 3-1 4l2-18z" class="m"></path><path d="M523 506h1c0 2 0 4-1 6 0 2-1 6-1 9h1l1 1 1-2 1-1c0 2 1 2 2 3l1 1v4l-1-1-2 2c-2 3-2 6-3 9h-1l-2 9h0l-1-2c1-4 1-7 1-11h-1c0 1 0 2-1 3 0-2 1-4 1-7v-1c0-2 0-4 1-6h0c1-5 1-11 3-16z" class="W"></path><path d="M520 522h0v1l1 1c0 2-1 4-2 5v-1c0-2 0-4 1-6z" class="n"></path><path d="M524 522l1-2 1-1c0 2 1 2 2 3l1 1v4l-1-1-2 2c-2 3-2 6-3 9h-1v-2-5c1-3 2-5 2-8z" class="k"></path><path d="M526 528v-1-1-2l1-1c0-1 0-1 1-1l1 1v4l-1-1-2 2z" class="j"></path><defs><linearGradient id="AJ" x1="511.144" y1="333.39" x2="523.093" y2="340.944" xlink:href="#B"><stop offset="0" stop-color="#1f201d"></stop><stop offset="1" stop-color="#463c3d"></stop></linearGradient></defs><path fill="url(#AJ)" d="M510 331c1-1 1 0 1-1v-1l1-1s0-1 1-2h0l-1-2c1-1 1-2 1-4 1 1 1 2 2 2v-1h0l2 1 1 4 1-1 1 2c0 1 1 1 1 2h1 0l2 1c1-1 1-1 1-2 2 0 3 0 4 1h0c1 1 2 2 2 3v1c0 2 1 4 2 6h1l1 2 2-2c1-1 1-2 2-3v3l-2 3h0l-7 11c-3 5-6 11-7 17h-1c-1-2-1-4-1-6l-2-2c0 1 0 3-1 4-1-2-3-4-3-7h0c-1-2-1-2 0-3-1-2-1-3-1-5h0l-1-1c1-1 0-1 0-2v-2-5c1-2 1-4 1-6l-2-2v1c1 3-1 8 0 11s0 5 0 8c-1-6 0-14-2-20l1-1-1-1z"></path><path d="M515 322v-1h0l2 1-1 4-1 1v-5z" class="B"></path><path d="M518 326l1-1 1 2c0 1 1 1 1 2h1 0l-1 1 1 1-2 1v5c-1-1-1-1-2-1-1-2-1-3-1-5l1-1c0 1 0 1 1 2l1-1c0-1-1-2-1-3s-1-1-1-2h0z" class="C"></path><path d="M519 358c0-1 1-3 0-4v-1c-1-3 0-6 1-8 1 1 0 3 0 5l1 1v2c-1 0-1 1-1 2 1 3 3 6 1 9l-2-2v-4z" class="K"></path><path d="M510 331c1-1 1 0 1-1v-1l1-1s0-1 1-2h0l-1-2c1-1 1-2 1-4 1 1 1 2 2 2v5 6 2c1 2 1 5 1 8h-1c-2-2 0-9-2-10-1 0-1-1-2-1l-1-1z" class="J"></path><path d="M515 356v-7-1l1-1c1 2 1 3 2 5 0 2 0 4 1 6v4c0 1 0 3-1 4-1-2-3-4-3-7h0c-1-2-1-2 0-3z" class="C"></path><path d="M525 354l1 2h1l1-3v-1c1 1 1 1 2 1-3 5-6 11-7 17h-1c-1-2-1-4-1-6 2-3 0-6-1-9 0-1 0-2 1-2 0 2 1 3 2 5h1c0-1-1-2-1-3-1 0-1-1-1-1v-1l1 1 1 1 1-1z" class="L"></path><path d="M525 328c2 0 3 0 4 1h0c1 1 2 2 2 3v1c0 2 1 4 2 6h1l1 2 2-2c1-1 1-2 2-3v3l-2 3h0l-7 11c-1 0-1 0-2-1v1l-1 3h-1l-1-2-1-1c-1 0-1-1-2-1 0-1-1-2-1-3 0 0 1 0 1-1 0 0-2-5-2-6v-5-5l2-1-1-1 1-1 2 1c1-1 1-1 1-2z" class="q"></path><path d="M524 353h1 2c0-1-2-1-3-3-1 0-1-1-1-2h1 0c-1-2-1-3-1-5h0l-1-1v-1l1 1h1v-1-1l2 2c0 2 2 4 2 6v1c1 1 1 1 1 2l-1 1v1l-1 3h-1l-1-2-1-1z" class="e"></path><path d="M525 328c2 0 3 0 4 1h0c1 1 2 2 2 3v1c0 2 1 4 2 6h1l1 2 2-2c1-1 1-2 2-3v3l-2 3h0l-7 11c-1 0-1 0-2-1l1-1v1c1-1 1 0 1-1l1-1v-1h-1v-2l-2-2-1-1c0-2 0-2-1-3v-2c0-1 1-1 1-2-1-1-1-1-1-2h0l-1-1c0-2 1-2 2-4h0c-1 1-2 1-2 2-1 1-1 1-1 2-1-1-2-2-2-3l-1-1 1-1 2 1c1-1 1-1 1-2z" class="P"></path><path d="M465 378l1 2 1 1v-1c1 1 2 3 2 4 1 2 4 6 6 8h0c1 1 1 2 2 2h0 1 1c2 0 3 3 4 5l12 15c1 2 2 3 3 5 0 1-1 0 0 1v6c2 10 3 21 2 32 1 1 1 2 1 4-1 4 1 9-1 13l-1 2c-2-2-2-5-2-7l-2-1v-3c0-1 1 0 0-1h-2l-2-7c0-2 0-3-1-5l-1-2h-1v-1c-1-3-2-6-2-10-2-3-2-6-3-9-2-7-3-15-6-22 0-2-1-5-3-6 0 0-1-1-1-2v-1l-1-1-1-4s0-1-1-2v-1l-1-2v-1c-1 0-1-1-2-2 0-1-1-2-1-3v-1c0-2 0-3-1-4v-1z" class="J"></path><path d="M482 411c1 0 3 1 4 0 1 2 2 2 2 4 1 1 1 2 0 3 1 1 1 2 1 3h-1c-2-3-4-6-6-10z" class="X"></path><path d="M478 396l8 15c-1 1-3 0-4 0l-3-6c1-1 0-2-1-4 1-1 1-2 0-4h0v-1z" class="AA"></path><path d="M495 414c1 2 2 3 3 5 0 1-1 0 0 1v6h0c-1 0-1 0-2 1 0 1 0 2-1 3h0v-1-3h0v-1h-2v-1l-1-2c0-1-1-2-1-3v-1l2 1h3v-1c-1-1-1-2-1-4z" class="B"></path><path d="M486 429h1v-3c1 1 1 2 3 2h0c-1-2-2-2-2-4h1c0 1 1 1 2 1l1 1-2 1 1 2h-1v3l1 10v1c0-1-1-1-1-2-1 0-1-2-2-3-1-3-1-6-2-9z" class="AC"></path><path d="M465 378l1 2 1 1v-1c1 1 2 3 2 4 1 2 4 6 6 8h0c1 1 1 2 2 2h0 1 1l-1 2v1h0c1 2 1 3 0 4 1 2 2 3 1 4l-1 1c2 4 5 9 6 14l1 1v2l1 1-1 2c0 1 0 2 1 3 1 3 1 6 2 9 1 1 1 3 2 3 0 1 1 1 1 2l1 2c0 2 0 4 1 6-1 0-2-1-3-1v1l1 1-1 1-1-2h-1v-1c-1-3-2-6-2-10-2-3-2-6-3-9-2-7-3-15-6-22 0-2-1-5-3-6 0 0-1-1-1-2v-1l-1-1-1-4s0-1-1-2v-1l-1-2v-1c-1 0-1-1-2-2 0-1-1-2-1-3v-1c0-2 0-3-1-4v-1z" class="S"></path><path d="M488 438c1 1 1 3 2 3 0 1 1 1 1 2l1 2c0 2 0 4 1 6-1 0-2-1-3-1v1l1 1-1 1-1-2h-1v-1c-1-3-2-6-2-10l3 7-1-8v-1z" class="U"></path><path d="M467 381v-1c1 1 2 3 2 4 1 2 4 6 6 8h0c1 1 1 2 2 2h0 1 1l-1 2v1h0c1 2 1 3 0 4 1 2 2 3 1 4l-1 1c2 4 5 9 6 14l1 1v2l1 1-1 2-7-19v-1c-1-3-2-7-4-10-2-5-5-9-7-14v-1z" class="d"></path><path d="M492 426v-1c-1-1-1 0-1-1v-1h0l2 1v1h2v1h0v3 1h0c1-1 1-2 1-3 1-1 1-1 2-1h0c2 10 3 21 2 32 1 1 1 2 1 4-1 4 1 9-1 13l-1 2c-2-2-2-5-2-7l-2-1v-3c0-1 1 0 0-1h-2l-2-7c0-2 0-3-1-5l1-1-1-1v-1c1 0 2 1 3 1-1-2-1-4-1-6l-1-2v-1l-1-10v-3h1l-1-2 2-1z" class="E"></path><path d="M490 453l1-1-1-1v-1c1 0 2 1 3 1 0 2 0 4 1 6s2 6 2 8l1 5-2-1v-3c0-1 1 0 0-1h-2l-2-7c0-2 0-3-1-5z" class="s"></path><path d="M497 456c1-1 2-1 2-3l1 1v4c1 1 1 2 1 4-1 4 1 9-1 13l-2-2c0-2 1-4 0-6h0v-1-3c-1-1 0-1 0-2s-1-1-1-2v-3z" class="c"></path><path d="M492 426v-1c-1-1-1 0-1-1v-1h0l2 1v1h2v1h0v3 1h0c1-1 1-2 1-3 1-1 1-1 2-1h0c2 10 3 21 2 32v-4l-1-1c0 2-1 2-2 3v-7c0-2-1-14-1-14 0-1-1-1-1-2l-1-2-1-1-2 2h-1v-3h1l-1-2 2-1z" class="a"></path><path d="M513 269c2 1 2 2 3 3 1 0 2 0 3 1 1-1 2 0 3 0s2 0 2 1c2 1 3 2 5 3h0c1 1 2 2 2 4h-1l3 3h-1c1 1 2 2 4 3s4 4 6 5v1c2-1 3-1 5-2 1 0 1 0 1-1h2s0 1 1 2c1-1 2-1 3-1v1h1 1v-1c2 0 2 1 3 2h0 0v1c1 1 2 1 3 2h-2v1h1c2 1 3 3 5 4l1 1h1c1 0 2 0 3-1s2-2 2-5l1 1v2h2v3c0 1 0 1 1 2l1-1v2 1l-1 3c-1 0-2 0-3 1s-2 2-2 4c-1 1-1 1-2 1-1 1 0 2 1 3 0 1-1 3 0 5h0c0 3-1 4-2 6h-1-1l1 2 1 1 2-1h0c1 1 2 2 3 2v1l-1 1h0-2-1l-2-2h-1c0 1-1 2-1 2v1h-1c-7-3-13-2-20 1-3 1-5 3-8 5h0l2-3v-3c-1 1-1 2-2 3l-2 2-1-2h-1c-1-2-2-4-2-6v-1c0-1-1-2-2-3h0c-1-1-2-1-4-1 0 1 0 1-1 2l-2-1h0-1c0-1-1-1-1-2l-1-2-1 1-1-4-2-1h0v1c-1 0-1-1-2-2l1-2h1 1l1-2s1-2 1-3c1-1 1-1 1-3 0-1 0-2-1-3 0-4-1-9-2-13l-1-12 1-3c-2-3-2-7-3-10z" class="q"></path><path d="M548 322h1c0 1 1 2 1 3l1 1c0 1 0 1-1 2l-2 1v-3-4z" class="K"></path><path d="M565 334c0-1-1-1-2-2l1-1 1 1v-1l-1-1 1-1c1 1 2 1 3 2l1 1 2-1h0c1 1 2 2 3 2v1l-1 1h0-2-1l-2-2h-1c0 1-1 2-1 2v1h-1v-2z" class="E"></path><path d="M521 298h1l1 1-1 1c1 1 3 5 5 5l2 4c1 2 2 3 2 5-1 1-1 2-1 3l-2-2c-1-3-3-5-5-7h0v-3c-2-2-2-5-2-7zm18 19h1v3c1 1 2 0 3 0v-5h1c0 1 2 3 2 4-1 1-1 1-2 1v2l2 2v3 3c0 1 0 1-1 1v-1h-1-1l-2-2h1c-1-2-2-1-2-2-1 0-1-1-1-1v-1h1c0 1 0 1 1 1l1-1-3-2v-5z" class="e"></path><path d="M539 339c4-3 7-6 12-7l1-1c2 0 4-1 6 0 2 0 3 0 4 2 1 1 2 1 3 1v2c-7-3-13-2-20 1-3 1-5 3-8 5h0l2-3z" class="AL"></path><path d="M529 329c1-1 2-2 2-4h0c1-1 1-2 2-2l1 2c3 3 5 6 5 11-1 1-1 2-2 3l-2 2-1-2h-1c-1-2-2-4-2-6v-1c0-1-1-2-2-3z" class="Aa"></path><path d="M534 325c3 3 5 6 5 11-1 1-1 2-2 3v-5c-1-1-2-1-2-2l-1-4v-3z" class="AD"></path><path d="M529 329c1-1 2-2 2-4h0c1-1 1-2 2-2l1 2v3l-2 1 1 2v2c0 1 0 1 1 2v4h-1c-1-2-2-4-2-6v-1c0-1-1-2-2-3z" class="AR"></path><path d="M518 307c2 0 4 0 5 1 2 2 4 4 5 7l-1 1-1-1v3h-1l-2-2v4 1c1 1 1 2 2 3v2 2c0 1 0 1-1 2l-2-1h0-1c0-1-1-1-1-2l-1-2-1 1-1-4-2-1h0v1c-1 0-1-1-2-2l1-2h1 1l1-2s1-2 1-3c1-1 1-1 1-3 0-1 0-2-1-3z" class="L"></path><path d="M515 318h1l1-2c0 2 0 4 1 5l4 8h-1c0-1-1-1-1-2l-1-2-1 1-1-4-2-1h0v1c-1 0-1-1-2-2l1-2h1z" class="a"></path><path d="M519 310l3 5h0v1c-1 1-1 2-1 4h-1v-4-1h-1c0 1 0 4 1 5 2 2 2 4 3 7h0c-1-1-2-5-4-6h-1c-1-1-1-3-1-5 0 0 1-2 1-3 1-1 1-1 1-3z" class="d"></path><path d="M518 307c2 0 4 0 5 1 2 2 4 4 5 7l-1 1-1-1v3h-1l-2-2 1-1h-1-1 0l-3-5c0-1 0-2-1-3z" class="P"></path><path d="M523 308c2 2 4 4 5 7l-1 1-1-1c-1-1-3-2-4-3v-3l1-1z" class="J"></path><path d="M536 296c1 1 0 2 1 2l1 3c1 2 1 3 3 4 1 1 3 2 4 3l5 3 3 2v1c0 1 0 0 1 1l1 1h0c-1 0-3 0-4 1l-1 1c0 1 0 1-1 2-1-2-3-4-4-6h-1c0-1 0-1-1-2 0 1 0 1 1 3h-1v5c-1 0-2 1-3 0v-3h-1l-1-1c-1 2-2 2-3 3-1 0-1-1-2-2l-1-2-1-1c1-2 0-5 1-7 0-1 0-1-1-2 0-1 0-1 1-2h1 0v-2-1c-1-1-1-1 0-2h2l1-2z" class="f"></path><path d="M538 314l-2-2c-1-2 0-2 0-3h1l1 1c0 1 1 1 2 2l-1 2h-1z" class="O"></path><path d="M532 315c0-1 1-1 2-2 0 1 1 1 2 1l1 1v1l-1 1h-3l-1-2z" class="M"></path><path d="M536 296c1 1 0 2 1 2v1l-1 1v2h0c-1 1-1 2-1 4l1 1c-1 0-2 1-2 2-1 0-1-1-2-2 0-1 0-1-1-2 0-1 0-1 1-2h1 0v-2-1c-1-1-1-1 0-2h2l1-2z" class="X"></path><path d="M537 309c2 0 2 1 3 1 2 0 2 1 4 1 0 1 1 2 1 3h-1c0-1 0-1-1-2 0 1 0 1 1 3h-1v5c-1 0-2 1-3 0v-3h-1l-1-1c-1 2-2 2-3 3-1 0-1-1-2-2h3l1-1v-1l1-1h1l1-2c-1-1-2-1-2-2l-1-1z" class="P"></path><path d="M513 269c2 1 2 2 3 3 1 0 2 0 3 1 1-1 2 0 3 0s2 0 2 1c2 1 3 2 5 3h0c1 1 2 2 2 4h-1l3 3h-1c1 1 2 2 4 3s4 4 6 5h-2v3l1 2c-2 1-2 1-2 3l-1 1-1-3c-1 0 0-1-1-2l-1 2h-2c-1 1-1 1 0 2v1 2h0-1c-1 1-1 1-1 2-1 0-1-1-1-1 0-1-1-2-1-3l-1 1 1 1-1 1-1 1c-2 0-4-4-5-5l1-1-1-1h-1c0 2 0 5 2 7v3h0c-1-1-3-1-5-1 0-4-1-9-2-13l-1-12 1-3c-2-3-2-7-3-10z" class="AB"></path><path d="M519 273c1-1 2 0 3 0s2 0 2 1c2 1 3 2 5 3h0c1 1 2 2 2 4h-1-1c-3-3-7-5-10-8z" class="AR"></path><path d="M539 293c-1-4-12-8-14-13l4 1h1l3 3h-1c1 1 2 2 4 3s4 4 6 5h-2v3l-1-2z" class="i"></path><path d="M529 288l1-1 9 6 1 2 1 2c-2 1-2 1-2 3l-1 1-1-3c-1 0 0-1-1-2l-2-3-1-1c-1-2-2-3-4-4z" class="C"></path><path d="M513 269c2 1 2 2 3 3l1 2c1 2 2 4 4 6l5 5-1 2 2 2h-3l-1-2v4c-1-1-2-1-3-2l-3-7c0-1-1-2-1-3-2-3-2-7-3-10z" class="AZ"></path><path d="M513 269c2 1 2 2 3 3l1 2c-1 2-1 3-1 5 1 2 2 3 2 5 2 1 4 1 5 3v4c-1-1-2-1-3-2l-3-7c0-1-1-2-1-3-2-3-2-7-3-10z" class="AN"></path><path d="M526 285c0 1 3 3 3 3 2 1 3 2 4 4l1 1 2 3-1 2h-2c-1 1-1 1 0 2v1 2h0-1c-1 1-1 1-1 2-1 0-1-1-1-1 0-1-1-2-1-3l-1 1 1 1-1 1-2-4c0-1-1-1-2-3 0-2-1-4-1-6v-4l1 2h3l-2-2 1-2z" class="AB"></path><path d="M528 302c0-1-1-2-1-3l1-1v-2h1l1 2 2 2 1 1v2h0-1c-1 1-1 1-1 2-1 0-1-1-1-1 0-1-1-2-1-3l-1 1z" class="L"></path><path d="M526 285c0 1 3 3 3 3 2 1 3 2 4 4l1 1-1 2v2h0l-2-2c-3-1-5-3-7-6h3l-2-2 1-2z" class="AN"></path><path d="M516 279c0 1 1 2 1 3l3 7c1 1 2 1 3 2 0 2 1 4 1 6 1 2 2 2 2 3l2 4-1 1c-2 0-4-4-5-5l1-1-1-1h-1c0 2 0 5 2 7v3h0c-1-1-3-1-5-1 0-4-1-9-2-13l-1-12 1-3z" class="AL"></path><path d="M521 298h0v-3h-1 0c0-1-1-1-1-2 0-3-1-7-2-10l-1-1h1l3 7c1 1 2 1 3 2 0 2 1 4 1 6 1 2 2 2 2 3l2 4-1 1c-2 0-4-4-5-5l1-1-1-1h-1z" class="X"></path><path d="M520 289c1 1 2 1 3 2 0 2 1 4 1 6-2-2-3-5-4-8z" class="AL"></path><path d="M548 290h2s0 1 1 2c1-1 2-1 3-1v1h1 1v-1c2 0 2 1 3 2h0 0v1c1 1 2 1 3 2h-2v1h1c2 1 3 3 5 4l1 1h1c1 0 2 0 3-1s2-2 2-5l1 1v2h2v3c0 1 0 1 1 2l1-1v2 1l-1 3c-1 0-2 0-3 1s-2 2-2 4c-1 1-1 1-2 1-1 1 0 2 1 3 0 1-1 3 0 5h0c0 3-1 4-2 6h-1-1c-1 0-1-1-2-2-1 0-1-1-2-1v-1c-1 0-1 0-2 1-1 0-1-1-2-1h-2c-2-1-3 1-4 1-1-2-2-4-2-7h0l-1-1 1-1c1-1 3-1 4-1h0l-1-1c-1-1-1 0-1-1v-1l-3-2-5-3c-1-1-3-2-4-3-2-1-2-2-3-4l1-1c0-2 0-2 2-3l-1-2v-3h2v1c2-1 3-1 5-2 1 0 1 0 1-1z" class="J"></path><path d="M555 295c5 2 9 6 13 9-2 0-4-1-6-2-3-2-4-3-7-5h0-1c0-2 0-2 1-2z" class="B"></path><path d="M542 292v1s1 1 2 1h3l-2 2-1-1-1 1s0 1 1 1c0 2 1 3 3 5l1 1c1 3 2 4 5 7h-1c-1-1-2-1-3-2l-3-3c-1 0-2-2-3-2h-1v-2l-3-1c0-2 0-2 2-3l-1-2v-3h2z" class="d"></path><path d="M563 314h-1c1-2 0-5 1-6l2 1 1-1c0-1 0-1 1-1 2 0 2-2 3-2s1 0 2-1h1l1 1v2c-2 3-4 5-5 8-1 0-1 0-2-1h-1c-1 1-2 0-3 0z" class="O"></path><path d="M548 290h2s0 1 1 2c1-1 2-1 3-1v1h1 1v-1c2 0 2 1 3 2h0 0v1c-2 1-2 0-4-1v1 1c-1 0-1 0-1 2h1v1c-1 1-1 1 0 2 0 1 0 1 2 1h1l2 2c1 2 2 3 4 4h-1c-1 0-1 0-3-1 0-1-1-3-2-4l-1 1h0c1 1 2 3 2 4l-1 1c1 1 1 1 1 3-1 0-1 1-1 1h-1c-1 0-1 1-2 1v-1c0-2 1-3 1-4v-3c-1 0-2 1-3 2-2 0-2-2-3-3s-2-1-2-1l-1-1c-2-2-3-3-3-5-1 0-1-1-1-1l1-1 1 1 2-2h-3c-1 0-2-1-2-1 2-1 3-1 5-2 1 0 1 0 1-1z" class="Y"></path><path d="M548 290h2s0 1 1 2l-2 3 1 1c-1 1-1 2-3 2-1 0-2-1-2-1h-1c-1 0-1-1-1-1l1-1 1 1 2-2h-3c-1 0-2-1-2-1 2-1 3-1 5-2 1 0 1 0 1-1zm28 12c0 1 0 1 1 2l1-1v2 1l-1 3c-1 0-2 0-3 1s-2 2-2 4c-1 1-1 1-2 1-1 1 0 2 1 3 0 1-1 3 0 5h0c0 3-1 4-2 6h-1-1c-1 0-1-1-2-2-1 0-1-1-2-1v-1c-1 0-1 0-2 1-1 0-1-1-2-1h-2c-2-1-3 1-4 1-1-2-2-4-2-7h0l1-1 1 1h2 1c1-2 2-3 4-4 0 1 0 1 1 1v-1c1 1 1 0 1 1h1 1l-1-2c1 0 2 1 3 0h1c1 1 1 1 2 1 1-3 3-5 5-8v-2l2-3z" class="M"></path><path d="M563 314c1 0 2 1 3 0l3 4c-1 0-2 1-3 1s-4-2-5-3v-1c1 1 1 0 1 1h1 1l-1-2z" class="B"></path><path d="M551 319c1 2 2 3 4 4l1-2h1v1l1 1h1c1-1 1-1 2-1l1 1 2-1v2c0 1 1 1 2 1l2 2-1 1-1-1h-1c-1 0-1-1-2-1v-1c-1 0-1 0-2 1-1 0-1-1-2-1h-2c-2-1-3 1-4 1-1-2-2-4-2-7z" class="K"></path><path d="M576 302c0 1 0 1 1 2l1-1v2 1l-1 3c-1 0-2 0-3 1s-2 2-2 4c-1 1-1 1-2 1-1 1 0 2 1 3 0 1-1 3 0 5h0c0 3-1 4-2 6h-1-1c-1 0-1-1-2-2h1l1 1 1-1-2-2c0-3 2-4 4-7h-1l-3-4h1c1 1 1 1 2 1 1-3 3-5 5-8v-2l2-3z" class="f"></path><path d="M576 302c0 1 0 1 1 2l-1 4h0l-2-1v-2l2-3z" class="M"></path><defs><linearGradient id="AK" x1="490.193" y1="436.956" x2="435.034" y2="463.222" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#3c0908"></stop></linearGradient></defs><path fill="url(#AK)" d="M454 371c2 2 3 3 5 4 2-1 1-1 2-1 0 3 1 5 2 7v1c1 0 2 1 3 2 0 1 1 2 1 3 1 1 1 2 2 2v1l1 2v1c1 1 1 2 1 2l1 4 1 1v1c0 1 1 2 1 2 2 1 3 4 3 6 3 7 4 15 6 22 1 3 1 6 3 9 0 4 1 7 2 10v1h1l1 2c1 2 1 3 1 5l2 7h2c1 1 0 0 0 1v3l2 1c0 2 0 5 2 7v3c0 2-1 5 0 7l3-6h0l-4 13c-2 6-1 11-1 17 1 5 1 11 2 16l3 13v2h0l-1 1 1 11c1 2 1 7 1 8 1 2 1 4 1 6v6h-1c0-2 0-3-1-5 0-1-1-2-1-3v-1h-2c0-1 0-3-1-4v1c0 1-1 1-2 2-2-2-1-5-1-7s-1-2-1-3l-1 1c-1 0-1 0-1-1v-1h-1v-2l-2-1c-1-4-3-8-5-12v-2l-1-3c1 0 1 0 1-1v-1-1h-1v2h0l-2-3-1-1h-1v1h-2c-1-1-1-1-2-1v-1 1h-1c-1 0-1 0-2 1l-1-2-4-2-3 4-4 5c-1 1-2 2-2 3l-1 1-1 1h-1-1l-7-11c-3-3-5-7-7-10-3-4-6-9-8-13-3-4-5-9-8-14-1-1-2-3-2-4-2-2-3-5-5-7l-2-5c0-1 0-2-1-3v-1l2-1s-1-1-1-2c-2-2-4-3-4-6h2l-2-3c0-2-1-3-1-4l1-1-2-3-1 1h-1-1c0-1 0-2-1-3v-1c0-1 1-2 1-3 0-2 1-5 1-7l1-1h0v-1l1-1c0 2 1 3 2 3l2-1-1-1h3 0l2-1s1 0 1-1c2 0 3-1 4-1h1l1-3 1 1-1 2c1 2 1 1 2 2l3-3c2-1 3-1 5-3 3-3 3-8 7-11l1 1-2 3c2 0 2-1 3-2 1-3 4-6 4-8 2-5 1-11 1-17 0-1 0-4 1-6v-4c1 1 1 2 2 3v-1c0-3 0-6 1-9l2-7z"></path><path d="M470 470c0-1 0-2 1-3 2 0 2 0 3 1 0 3 0 2 1 4 1 1 1 2 1 3-1-2-2-3-4-4l-2-1z" class="c"></path><path d="M471 451l1-2h1c2 1 3 2 3 4v1 1l-1-1-2-2c-1 0-2 1-3 2 0-1 0-1 1-2v-1h0z" class="H"></path><path d="M485 489v-4-2h3c1 2 1 3 1 5 1 1 1 2 1 4l-2-2-1 1v1 2c0-1-1-1-1-2s0-2-1-3z" class="c"></path><path d="M465 471h1 0c-2 2-2 5-1 8h1c1 0 1 0 2 1v-1-1l1-2v-4l1 1c-1 2-1 6-2 8 0 1 1 3 1 4l-1 1c-2-2-3-4-4-5-1-2-1-3-1-6l2-4z" class="E"></path><path d="M432 451l1 1h1c0 1 1 2 1 2 0 1-1 2-1 3l1-1h1c2 1 2 3 2 5 1 0 1 0 1 1 1 1 1 3 2 4s1 1 1 2 1 1 1 2h0l-2 2c-1-1-1-2-2-4 0 0 0-1-1-1-1-1-1 0-2 0 0-1 0-2-1-3l-2 1 1-1-1-2v-1h-2v-2c0-1 0-2-1-3h2v-5z" class="n"></path><path d="M432 451l1 1h1c0 1 1 2 1 2 0 1-1 2-1 3l1-1v4h-3v-4-5z" class="H"></path><path d="M470 470l2 1c2 1 3 2 4 4l2 4-2 5 1 1-3 3h0c0 1-1 2-2 3 0-1 1-3 0-4v-2-1h-1v-3h0-3c1-2 1-6 2-8v-3z" class="b"></path><path d="M470 470l2 1-1 2v5c1 0 1 1 1 1 0 1 0 1-1 2h0-3c1-2 1-6 2-8v-3z" class="Q"></path><path d="M472 471c2 1 3 2 4 4l2 4-2 5 1 1-3 3s0-1-1-1c1-1 0-3 1-4s1-1 1-2h0l1-2c0-2-1-3-1-4h-3l-1-2 1-2z" class="H"></path><path d="M454 371c2 2 3 3 5 4 2-1 1-1 2-1 0 3 1 5 2 7v1h-1s-1 1-2 1c-5 2-7 6-9 11 0-1-1-1 0-2v-2-2-1c0-3 0-6 1-9l2-7z" class="P"></path><path d="M454 371c2 2 3 3 5 4 2-1 1-1 2-1 0 3 1 5 2 7v1h-1c-1-1-1-2-3-3h0v-1c0-1-1-2-1-2-1-1-1-1-2 0s-2 1-4 2h0l2-7z" class="L"></path><path d="M451 387l2-3v-1c1-2 1-2 3-2v-1l2-2 1 2v1l-2 1v1l2-1 1 1c-5 2-7 6-9 11 0-1-1-1 0-2v-2-2-1z" class="AR"></path><path d="M463 475c0 3 0 4 1 6 1 1 2 3 4 5l1-1c0-1-1-3-1-4h3 0v3h1v1 2c1 1 0 3 0 4 1-1 2-2 2-3h0l3-3 4-6-3 7 4 10c1 2 1 4 2 6v1l-1-1-2-2c0-3-1-7-3-9-1-1-1-2-2-2 0 1-1 1-1 2s0 1-1 2h0-1l-1 2h0-1c0-1-1-2-1-3h-1c-1 0-5-11-8-12v3l-1 1-1-2c0-1 1-3 2-4l2-3z" class="W"></path><path d="M474 493h0c1-1 1-1 1-2s1-1 1-2c1 0 1 1 2 2 2 2 3 6 3 9l2 2 1 1v-1c0 1 0 2 1 4v3 1h0c0-1 0-1-1-2 0-1-1-1-2-2-1 1-1 2-2 4h-2v2h0c-2-1-3-3-4-4l1-4-1-1-1 4h-2l1-4c1-3 1-6 2-10z" class="R"></path><path d="M484 503l-2 1c-1-1-1-1-2-1s0 0-1 1v1-3l-2-1c0-2 0-3 1-4l1-1v1c1 1 1 2 2 3l2 2 1 1z" class="b"></path><path d="M461 483v-3c3 1 7 12 8 12h1c0 1 1 2 1 3h1 0l1-2h1c-1 4-1 7-2 10-2 0-2 0-4 1v-1h-2l-4-6c0-2-3-5-4-7l1-1c-1-1-1-1-2-3l2-4 1 2 1-1z" class="I"></path><path d="M461 483v-3c3 1 7 12 8 12v1c1 2 1 4 0 5h0l-3-6-5-9zm-2-1l1 2c3 6 7 12 8 19h-2l-4-6c0-2-3-5-4-7l1-1c-1-1-1-1-2-3l2-4z" class="R"></path><path d="M449 385c1 1 1 2 2 3v2 2c-1 1 0 1 0 2 0 4-3 17-1 20 2-1 2-3 5-4v1c-5 10-16 17-22 27-1 2-4 5-6 6v1h-1-1c1-1 2-2 3-2l-1-1c-2 1-2 1-3 3h0-1l-1-1 2-2 5-5c3-3 5-6 7-9 2-2 4-3 4-6 2 0 2-1 3-2 1-3 4-6 4-8 2-5 1-11 1-17 0-1 0-4 1-6v-4z" class="z"></path><path d="M474 403c2 1 3 4 3 6 3 7 4 15 6 22 1 3 1 6 3 9 0 4 1 7 2 10v1h1l1 2c1 2 1 3 1 5l2 7h2c1 1 0 0 0 1v3l2 1c0 2 0 5 2 7v3c0 2-1 5 0 7l3-6h0l-4 13c-2 6-1 11-1 17 1 5 1 11 2 16v1h-1l-2-15c-1-6 0-11-1-17-6-31-11-63-21-93z" class="V"></path><path d="M488 451h1l1 2c1 2 1 3 1 5l2 7 1 5c0 5 2 11 1 16l-1-1c0-2-1-6-2-8l-4-26z" class="c"></path><path d="M493 465h2c1 1 0 0 0 1v3l2 1c0 2 0 5 2 7v3c0 2-1 5 0 7 0 2-1 4-2 6 0 0 0 1-1 2l-1-1-1-9 1 1c1-5-1-11-1-16l-1-5z" class="k"></path><path d="M493 465h2c1 1 0 0 0 1v3l2 1c0 2 0 5 2 7v3l-2-2c0-1 0-3-1-4v-1c-1-1-1-2 0-3l-1-1-1 1-1-5z" class="Q"></path><path d="M426 435l3-3c2-1 3-1 5-3 3-3 3-8 7-11l1 1-2 3c0 3-2 4-4 6-2 3-4 6-7 9l-5 5-2 2 1 1h1 0c1-2 1-2 3-3l1 1c-1 0-2 1-3 2h1 1l1 1h0c0 2 0 2 1 3v1l1 1 2-2v2 5h-2c1 1 1 2 1 3v2h2v1l1 2-1 1v1c0-1-1-2-2-3h-2c-1 1-1 3-1 4-1-1-2-1-3-2l-1-1c-1 1-1 1-1 2l-2-2h-1v1c-2 1-2-2-3-2-1-1-3-1-3-1h-1l-2-3c0-2-1-3-1-4l1-1-2-3-1 1h-1-1c0-1 0-2-1-3v-1c0-1 1-2 1-3 0-2 1-5 1-7l1-1h0v-1l1-1c0 2 1 3 2 3l2-1-1-1h3 0l2-1s1 0 1-1c2 0 3-1 4-1h1l1-3 1 1-1 2c1 2 1 1 2 2z" class="u"></path><path d="M422 442c1-1 1-2 2-3l5-5 1 1c-1 0-1 0-1 1v1l-5 5h-2zm3 6l1-1 1 1h0c-1 1-1 1-1 2l4 2v3l-1-3-1 1c-1 1-2 1-3 2l-1 1v2c-1 0-1-2-2-3 0-3 3-4 4-6l-1-1z" class="Q"></path><path d="M424 456l1-1c1-1 2-1 3-2l1-1 1 3v1c1 1 1 2 1 3v2h2v1l1 2-1 1v1c0-1-1-2-2-3h-2c0-2-1-2-2-3l1-2-1-1c-1-1-1-1 0-1l-1-1-2 1z" class="W"></path><path d="M415 436l2-1s1 0 1-1c2 0 3-1 4-1h1l1-3 1 1-1 2c1 2 1 1 2 2-1 1-3 2-3 3-1 1-1 2-3 2l-3 3v1c-1-1-1-1-1-2-1-1 0-1-1-2v-1l1-1-1-2z" class="b"></path><path d="M420 440c0-2-1-2 0-3 1-2 1-2 2-2h1v3c-1 1-1 2-3 2z" class="R"></path><path d="M422 442h2l-2 2 1 1h1 0c1-2 1-2 3-3l1 1c-1 0-2 1-3 2h1 1l1 1h0c0 2 0 2 1 3v1l1 1 2-2v2 5h-2v-1-3l-4-2c0-1 0-1 1-2h0l-1-1-1 1-1 1c-1 1-1 2-2 3s-2 2-3 2v-2c0-1-1-1-1-2 0-2 1-4 2-5s1-2 2-3z" class="s"></path><path d="M411 454c1 0 1-1 3 0v-3c2 2 0 3 3 3h0c1 1 2 1 2 3-1 1-2-1-3 1l1 1h0c1-1 1-1 2-1v2c2 0 2 2 4 2v-3h1 0v2c1 1 1 0 1 1-1 1-1 1-1 2-1 1-1 1-1 2l-2-2h-1v1c-2 1-2-2-3-2-1-1-3-1-3-1h-1l-2-3c0-2-1-3-1-4l1-1z" class="j"></path><path d="M411 454l3 4v1c0 1 1 2 2 3h2l1 2h1v1c-2 1-2-2-3-2-1-1-3-1-3-1h-1l-2-3c0-2-1-3-1-4l1-1z" class="V"></path><path d="M407 438l1-1h0v-1l1-1c0 2 1 3 2 3l2-1c-1 3-1 4 1 6v1c1 1 1 2 1 3 1 1 1 1 1 2v1c-1 1-2 1-3 1h-4l-1 1h-1-1c0-1 0-2-1-3v-1c0-1 1-2 1-3 0-2 1-5 1-7z" class="Q"></path><path d="M406 445l1 2 1 1v-1c0-1 1-1 1-2l2 1v-2l1 1v1c0 1 1 1 1 2v1h1l1 1-2 1h-4l-1 1h-1-1c0-1 0-2-1-3v-1c0-1 1-2 1-3z" class="AT"></path><path d="M471 451h0v1c-1 1-1 1-1 2-1 6-3 11-5 17l-2 4-2 3c-1 1-2 3-2 4l-2 4c1 2 1 2 2 3l-1 1c1 2 4 5 4 7l4 6h2v1c2-1 2-1 4-1l-1 4h2l1-4 1 1-1 4 1 3h0 0l-2 1v1l-3 6c0 2-2 4-3 6l-3 4-4 5c-1 1-2 2-2 3l-1 1-1 1h-1-1l-7-11c0-2 0-3-1-5h-1c0-2-1-8-3-9-1-1-1 0-2-1l1-3c1-1 2-2 3-4v-1l3-5 1-1c0-2 0-4-1-5s-2-2-2-4c1 2 2 3 3 4 2 0 1 0 1-2l1-1v-2-2c1 2 2 4 3 5 2-4 3-9 5-13s5-8 7-13c2-4 3-11 6-15z" class="AQ"></path><path d="M453 493c1-2 3-5 4-7 1 2 1 2 2 3l-1 1v-1c-1 1-1 1-1 3l1 1h-1c0 1 0 1-1 1s-1 0-3-1z" class="Q"></path><path d="M459 515l2 2h1 1v1c1 0 1 0 2-1h1l-1 3c-1 1-2 3-3 5l-1-1c0-2 0-3-1-5l-1-3v-1z" class="y"></path><path d="M459 516c-2 0-3 2-4 3v1c0 1 0 1-1 2v2c-1-1-1-1-1-3v-5c1-2 2-4 4-5 0 2 0 3 2 4v1z" class="j"></path><path d="M458 505c2 4 4 8 5 12h-1-1l-2-2c-2-1-2-2-2-4v-2l-2-2 3-2z" class="I"></path><path d="M456 494c1 0 1 0 1-1h1l-1-1c0-2 0-2 1-3v1c1 2 4 5 4 7l4 6h2v1c2-1 2-1 4-1l-1 4-4 10h-1-1c-1 1-1 1-2 1v-1c-1-4-3-8-5-12-2-3-5-8-5-12 2 1 2 1 3 1z" class="z"></path><path d="M456 494c1 0 1 0 1-1h1l-1-1c0-2 0-2 1-3v1c1 2 4 5 4 7l-1 2c1 1 2 2 2 3v1c-2-1-2-1-3-3h-1v1 1c0-1-1-2 0-3 1-3-1-2-1-4l-2-1z" class="n"></path><path d="M468 504c2-1 2-1 4-1l-1 4-4 10h-1-1l3-13z" class="x"></path><path d="M448 499v-2l1 1c0 4-5 10-7 13l1 1c1 1 3 3 4 5s1 3 2 5c1-3 2-4 3-6 0 3 0 4-2 6 1 2 2 3 2 4h1v3l3 4c3-2 4-6 5-9l1 1c1-2 2-4 3-5l1-3h1l4-10h2l1-4 1 1-1 4 1 3h0 0l-2 1v1l-3 6c0 2-2 4-3 6l-3 4-4 5c-1 1-2 2-2 3l-1 1-1 1h-1-1l-7-11c0-2 0-3-1-5h-1c0-2-1-8-3-9-1-1-1 0-2-1l1-3c1-1 2-2 3-4v-1l3-5 1-1z" class="l"></path><path d="M465 520h1l4-2c-2 3-4 7-7 10-1 1-1 3-3 4v-3c1-1 1-3 2-4 1-2 2-4 3-5z" class="I"></path><path d="M471 507h2l1-4 1 1-1 4 1 3h0 0l-2 1-1-1-2 7-4 2h-1l1-3h1l4-10z" class="s"></path><path d="M413 462h1s2 0 3 1c1 0 1 3 3 2v-1h1l2 2c0-1 0-1 1-2l1 1c1 1 2 1 3 2 0-1 0-3 1-4h2c1 1 2 2 2 3v-1l2-1c1 1 1 2 1 3 1 0 1-1 2 0 1 0 1 1 1 1 1 2 1 3 2 4l2-2h0l3 6s1 1 1 2v3c2 2 3 4 3 6v2 2l-1 1c0 2 1 2-1 2-1-1-2-2-3-4 0 2 1 3 2 4s1 3 1 5l-1 1-3 5v1c-1 2-2 3-3 4l-1 3c1 1 1 0 2 1 2 1 3 7 3 9h1c1 2 1 3 1 5-3-3-5-7-7-10-3-4-6-9-8-13-3-4-5-9-8-14-1-1-2-3-2-4-2-2-3-5-5-7l-2-5c0-1 0-2-1-3v-1l2-1s-1-1-1-2c-2-2-4-3-4-6h2z" class="AQ"></path><path d="M431 498c0-1-1-2-2-2v-3c-1-1-1-2-2-3v-1-1h1l2 1c1 2 1 3 1 5h-1l1 4z" class="T"></path><path d="M425 465c1 1 2 1 3 2v1c1 1 2 2 2 4h1 0c1 1 1 0 1 1l-3 2h0v-2l-1-1c-1-2-1-2-3-4v-3z" class="j"></path><path d="M431 498l-1-4h1c3 3 7 8 8 12 0 2 1 3 2 4l-1 3c1 1 1 0 2 1 2 1 3 7 3 9h1c1 2 1 3 1 5-3-3-5-7-7-10-3-4-6-9-8-13 0-2-1-4-2-5v-1h1v-1z" class="p"></path><path d="M413 462h1l8 12c2 2 4 5 4 7 0 1-1 2-1 2-1 3-1 5-1 8-1-1-2-3-2-4-2-2-3-5-5-7l-2-5c0-1 0-2-1-3v-1l2-1s-1-1-1-2c-2-2-4-3-4-6h2z" class="Z"></path><path d="M425 483c-2-2-3-4-4-6l4 4h1c0 1-1 2-1 2z" class="j"></path><path d="M413 462h1l8 12c1 1 1 2 2 4h0v1-1a30.44 30.44 0 0 1-8-8s-1-1-1-2c-2-2-4-3-4-6h2z" class="W"></path><path d="M429 463h2c1 1 2 2 2 3v-1l2-1c1 1 1 2 1 3 1 0 1-1 2 0 1 0 1 1 1 1 1 2 1 3 2 4l2-2h0l3 6s1 1 1 2v3c2 2 3 4 3 6v2 2l-1 1c0 2 1 2-1 2-1-1-2-2-3-4 0 2 1 3 2 4s1 3 1 5l-1 1-2-2c-1-4-3-7-5-10l-8-15c0-1 0 0-1-1h0-1c0-2-1-3-2-4v-1c0-1 0-3 1-4z" class="k"></path><path d="M433 465l2-1c1 1 1 2 1 3 1 0 1-1 2 0 1 0 1 1 1 1l-1 2h1c0 2 0 3 1 4-1 1 0 1-1 1-1-1-1-2-2-3s-2-1-2-2v-1l-1-1-1 1-1-1 1-2v-1z" class="Q"></path><path d="M439 475c1 0 0 0 1-1-1-1-1-2-1-4h-1l1-2c1 2 1 3 2 4l2-2h0l3 6s1 1 1 2v3l-1-1c-1 1-2 2-2 3l-1-1c-1 0-1 0-2-1l-1-2v-1c-1-1-1-2-1-3zm46 14c1 1 1 2 1 3s1 1 1 2v-2-1l1-1 2 2c1 2 1 4 1 6l2 3c0 1 0 2 1 2s1-1 1-2c-1-1-1-3 0-5 1 6 0 11 1 17l2 15h1v-1l3 13v2h0l-1 1 1 11c1 2 1 7 1 8 1 2 1 4 1 6v6h-1c0-2 0-3-1-5 0-1-1-2-1-3v-1h-2c0-1 0-3-1-4v1c0 1-1 1-2 2-2-2-1-5-1-7s-1-2-1-3l-1 1c-1 0-1 0-1-1v-1h-1v-2l-2-1c-1-4-3-8-5-12v-2l-1-3c1 0 1 0 1-1v-1-1h-1v2h0l-2-3-1-1h-1v1h-2c-1-1-1-1-2-1v-1 1h-1c-1 0-1 0-2 1l-1-2-4-2c1-2 3-4 3-6l3-6v-1l2-1h0 0l-1-3c1 1 2 3 4 4h0v-2h2c1-2 1-3 2-4 1 1 2 1 2 2 1 1 1 1 1 2h0v-1-3c-1-2-1-3-1-4-1-2-1-4-2-6l1-1 2-2v-4z" class="W"></path><g class="n"><path d="M474 508c1 1 2 3 4 4h0v-2h2c1-2 1-3 2-4 1 1 2 1 2 2 1 1 1 1 1 2l3 6v4h0c-1 0-1 1-1 1v1l-2-1-1 2-1-1c-1 0-1-1-1-2-1-1-2-1-2-2v-2c-1 1 0 1-1 1-2-1-3-4-4-6h0l-1-3z"></path><path d="M473 512l2-1v3c2 4 4 8 5 12l1 2h-1-1v1h-2c-1-1-1-1-2-1v-1 1h-1c-1 0-1 0-2 1l-1-2-4-2c1-2 3-4 3-6l3-6v-1z"></path></g><path d="M475 520c0 1 1 2 1 3h2c1 1 0 2 1 3h1l1 2h-1-1v1h-2c-1-1-1-1-2-1v-1h-1v-1c1-1 1-2 1-3v-3z" class="Q"></path><path d="M473 513l1 6v1h1v3c0 1 0 2-1 3v1h1v1h-1c-1 0-1 0-2 1l-1-2-4-2c1-2 3-4 3-6l3-6z" class="z"></path><path d="M495 496c1 6 0 11 1 17l2 15c0 2 1 5 1 6 1 4 2 10 1 14-1-1-1-3-2-4l-1-5v-1h-1v2 1l-1-1c0-2-1-4-2-6-1-1 0-3-1-4-1-2-1-3-1-5l2 2h1v-2h-1l-1-1v-4l1-1c0-1 0-2 1-4 0-3 0-8-2-11 0-2-1-3-2-5l1-1 2 3c0 1 0 2 1 2s1-1 1-2c-1-1-1-3 0-5z" class="n"></path><path d="M485 489c1 1 1 2 1 3s1 1 1 2v-2-1l1-1 2 2c1 2 1 4 1 6l-1 1c1 2 2 3 2 5 2 3 2 8 2 11-1 2-1 3-1 4l-1 1v4l1 1h1v2h-1l-2-2-3-5v-4l-3-6h0v-1-3c-1-2-1-3-1-4-1-2-1-4-2-6l1-1 2-2v-4z" class="H"></path><path d="M487 492v-1l1-1 2 2c1 2 1 4 1 6l-1 1v-1c-1-1-1-1-1-2-1-1-1-2-2-4z" class="U"></path><path d="M492 504c2 3 2 8 2 11-1 2-1 3-1 4l-1 1v4l1 1h1v2h-1l-2-2-3-5v-4c1 2 1 3 2 5 0 1 1 2 1 3 0-1 1-1 1-2-1-1-1-1-1-2 1-1 1-2 1-3 1-3 1-7 0-10v-2-1z" class="b"></path><path d="M484 538l3 2 2-2-1-2 1-2c0-1-1-2-2-2l1-1c1 1 1 1 2 1v2 1c2 1 2 2 3 4v1c1 2 1 4 1 6 1 1 1 1 2 1h0c0 2 2 4 2 6l-1 1c1 0 1 0 2-1v-2h1v1c1 1 1 2 1 3h1v-1c1 2 1 7 1 8 1 2 1 4 1 6v6h-1c0-2 0-3-1-5 0-1-1-2-1-3v-1h-2c0-1 0-3-1-4v1c0 1-1 1-2 2-2-2-1-5-1-7s-1-2-1-3l-1 1c-1 0-1 0-1-1v-1h-1v-2l-2-1c-1-4-3-8-5-12z" class="j"></path><path d="M500 551v1c1 1 1 2 1 3h1v-1c1 2 1 7 1 8l-1 1h-1l1-2s0-1-1-1v1h-1v-1c-1-2-1-3-1-5h1v-4z" class="k"></path><defs><linearGradient id="AL" x1="262.115" y1="553.44" x2="559.886" y2="421.161" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2b1f1e"></stop></linearGradient></defs><path fill="url(#AL)" d="M189 160h3c2 2 3 5 6 7l2 5c2 0 2 0 4 1h1l1 1c1 0 3 0 4 1l1 1c-2 0-4-1-7 0 12 3 21 10 32 16l1-1c1 0 2 0 3 1 1 0 1-1 1-1 3 0 6 3 9 5 2 1 5 2 8 4 3 3 8 3 11 7-2 0-3 0-4-1-1 0-3 0-4 1h1c1 1 2 1 3 2l1 1h-1l6 4c6 3 11 5 16 9 2 3 2 5 3 8 0 5 1 9 2 13v1c2 4 4 9 5 14 2 4 4 8 5 12l2 1v5c1 3 3 6 5 8 1 2 3 5 4 7l3 3v-1h1c0-3-2-3-3-5 0-1 1-1 1-2h1c-1-1-1-1-1-2l1-1h0l1 2h0c0-3-1-5-2-8l7 8c1 1 3 3 5 4-1 0-2 0-2 1v1h-1c3 4 5 6 9 8 1 1 2 1 3 2l5 4c1 2 2 3 3 4l1 2-1-3c1 0 2 1 2 0l-1-8-1-1-1-9-1-2v-5l-1-4v-2l-1-5v-4c3 0 4 1 6 1l2 2h1 1l1-2 4 6c1 2 3 4 4 6 3 3 5 6 7 8 3 0 5 0 8-1l6 1v2c1 9 8 19 15 24l3 3c3 2 5 3 8 4 1 0 2 1 3 2s2 1 3 2c-2 2-3 1-3 4 0 1-1 2-1 3-1-1-2 0-4 0-1 1-1 1-2 3l3 3-1 1v3 3l-1 2 1 1c0 1 0 2-1 4h0c-1 2-1 3-2 5v6c-1-1-2-1-3 0h-1c-1 0-1 1-1 1h-1c-2 0-3 0-4 1s-1 1-1 2c1 2 2 2 3 4v3c0 2 0 2 1 3v2l1 4c-2 1-3 2-5 3h0 1l-3 2c1 1 2 1 3 1h1l-3 3c-1 0-2 1-3 2l-2 1c-2 1-3 2-4 3v1h-1l9 22c3 5 6 12 8 18l4 7 6 11v-2c1 1 2 1 2 3 1 1 2 1 3 1v1c0 3 2 4 4 6 0 1 1 2 1 2l-2 1v1c1 1 1 2 1 3l2 5c2 2 3 5 5 7 0 1 1 3 2 4 3 5 5 10 8 14 2 4 5 9 8 13 2 3 4 7 7 10l7 11h1 1l1-1 1-1c0-1 1-2 2-3l4-5 3-4 4 2 1 2c1-1 1-1 2-1h1v-1 1c1 0 1 0 2 1h2v-1h1l1 1 2 3h0v-2h1v1 1c0 1 0 1-1 1l1 3v2c2 4 4 8 5 12l2 1v2h1v1c0 1 0 1 1 1l1-1c0 1 1 1 1 3s-1 5 1 7c1-1 2-1 2-2v-1c1 1 1 3 1 4h2v1c0 1 1 2 1 3 1 2 1 3 1 5v4c2-2 3-2 2-4v-2l1-1h0v1s1 1 1 2 1 2 0 3c0 1 0 2 1 3v3c0 1 1 2 1 3 0 2 0 6 2 8l1-1 1-12c0-7 1-13 2-19v-7l1-1c1-1 1-2 1-4h1l1-1v-5l1 2h0l2-9h1c0 1 1 2 0 3 1 0 1-1 2-1v1c1 0 1-1 1-1h1v2 1h0c2-1 3-4 3-5v-2-1l1-1v1c0 3-1 6-2 9h1l4-9c1-3 2-6 4-8 1-1 1-2 2-2l1 3c2-2 2-2 2-6 1-1 2-1 2-3l1-1 1 1 1-1c-1-2-1-2 0-3 2 0 2 3 3 3l1-1 3 5v1h-2v2l-1 1v1c0 2 0 3-1 5-1 1-1 2-1 4l-3 5c0 2-1 3-1 4 1 2 0 4 0 6l5-4h0c3-1 5-2 7-1 1 0 2 0 2 1 1 3 0 6-1 8l2-2 1-1c1 0 1 1 1 2 2 3 3 6 3 9v5c0 2-1 3-1 5v6 5l-2 1c-1 3-2 5-4 7-1 2-2 5-4 7h1l1 1c-1 1-2 2-3 2s-1 0-1 1c-1 1-3 3-5 3h0l-1-1-1 1h-1l2-4-2 2-1 2v-2c-1 0-1 1-1 2l-1 1c0 1 0 2-1 3-3 4-5 8-9 11h3c0 2 0 3-1 5l-1 2-1 8v3l-1 9-1 8-1 2c-1 2-3 7-4 9l-1 2-2 6c0 2-1 4-3 6v1c2-1 3-3 4-4h1l1 6v8c2-2 2-6 3-9l1-1c0-1 1-3 1-4l1-4c1-1 1-2 1-3l1-1c1-3 1-6 1-9 3-3 5-7 7-9l2-2 2-4 1-1c0-1 0-1 1-2s4-6 5-7h0v-1l1-1h0l1-1v-1l1-1h0l1-1v-1l1-1h0l1-1v-1l1-1h0l1-1v-1l3-3v-1h0l1-1c0-1 0-1 1-2v-1h1c0-1 0-1 1-1 0-1 0-2 1-2 0-1 0-2 1-2 0-1 0-1 1-1 0-1 1-2 1-3 1-1 1-2 2-2 1-1 1-1 1-2 1-1 1-1 2-1l2-2h0l2-1c0-2 0-2 1-3h1c0-1 1-2 2-3h-1c2-2 3-5 5-7 2-3 6-5 8-8h1l1-1 1 1c1-2 2-2 4-3l1-1c2-2 4-5 7-6s7-3 9-5l1-1 1-1c3-3 7-5 10-8l6-6c4-5 7-11 12-16 0 0 0 1 1 1l1 1c4-2 7-6 11-10v1l-2 1 1 1c1-1 2-2 2-4h1v1h1c0 1-2 2-3 3v1 1c-1 1-2 2-3 4h0c2-1 3-3 4-4l7-5 2 1c-1 2-1 5-2 7-1 0-1 1-1 2l-1 2-2 5c-1 1-2 3-3 5-1 0-1 1-1 2-1 3-4 6-5 9s-3 5-4 8c-1 2-1 5-2 8v1c-1 2-1 4-1 7l-1 2-2 3 1 1-1 1 1 1h1v9c-1 1-1 1-2 1s-1 0-2 1h3c1 1 1 2 1 3 0 2 0 3-1 5l1-1v3c-2 2-4 5-6 7-2 3-4 5-4 9l-1 4 1 1c2 1 3 0 5 2 1 1 1 1 0 3-1 1-2 1-3 2v5l3 1-1 4h-1-2c-1 1-3 5-4 5h-1-1l-2-1h-1c0 1 0 2 1 3-2 2-2 3-2 6h0c-1 2-1 3-3 4v2c-1 1-2 0-3 0l-1-2h-1v2 2l-2 2c1 1 2 3 3 4-1 1-1 1-1 2h-5c-2 1-2 1-3 1l-2 1h0v1l-5 2v2l1 1h1v1 1l-2 2-1 1c0 1 1 2 1 3-3 0-5 0-7 2-2 1-2 2-4 2-1 1-2 2-2 4v2c-3 5-6 11-9 18-1 3-3 6-4 9l2 2c-1 1-1 1-3 2-1 1-1 2-2 4-4 7-7 16-10 24l-26 62-22 55-10 23c-1-5-4-10-6-15l-11-29-28-70-25-59-1-2-17-2c1-8 0-16 0-24v-1c0-1 3-2 4-3 0-2-2-5-3-7-1-3-2-5-4-8l-1-4-2-2c-1-3-3-6-4-9 0-1-1-1-1-2-3-7-5-14-9-21 0-1-1-2-1-3-1-3-2-6-3-8-1-3-3-6-5-9h0v-1c-1-2-2-3-2-5h0c-3-3-5-9-6-13v1h-2c-1-2-2-5-3-8s-1-3-3-5c-1 1-1 1-2 1l-1-2c-1 1-1 2-2 3-1-2-1-3-2-5h-1v3h-1c-1-1-1-2-2-3l-2-3c-2 0-2-2-3-3l-2-1-3-1-1 1 4 3-1 1c0 1-1 1-1 1l-1 1v1l1 1-1 1-1-1h0c-1-1-2-2-2-3v-1l-1-1c-1-1-1-2-2-3h-1-1c0-1 0-1-1-2v-1c-1 0-1-1-1-2s-1-2-2-3h1c-6-5-13-8-18-13l-3-4c-1-2-3-4-4-6-3-5-8-10-10-15l-1-1-6-9v-2l-1-1-3-3c-1-3-3-5-5-7v-1c2 0 6 1 8 1v-3c-4 0-6 0-10-1-1-1-1-1-2-1-2 1-3 1-5 1l-1-2v-2-2c-1 0-2-1-3-3h0l-2 1c-1-1 0-1 0-3h-3c-3 0-3 0-6 1v1h-3l-1-17c-1 2 0 3-1 4h-1v-3h-1v5l-3 4-1-9 1-9v-10-3l-1-25c1-2 0-4 0-6v-6-4l2 2v-2l1-1c-1-3-1-8-1-10 1-2 1-3 1-4v-6s1-1 1-2c1-1 1-1 3-1 1-1 1-1 2-1h0l2-2c0-2 0-3-2-4l-1-1c-2-1-2-2-3-4l-1 4c-1-1-1-2-1-3v-3c-3-2-1-3-3-6v1l-1-1c-1 0-1-1-2-1v-1c-1-1-1-2-1-3l-1 1h-2c1-1 0-1 1-1v-1l-1-1c-2-2-3-4-5-4-1-1 0-1 0-2-1 0-3-2-3-3l-1 1v2h-1l1 1c-1 1-1 2-2 2h-1l-1 2h1v2l1 1h-1 0l-2-4h-3-2c-1-1-1-1-1-2l-1 1c-1 1-2 1-2 2l-1 1c-1 0-1 0-2 1l-1-1h-2c-1 1-1 1-1 3l-1-1-1 1c-2 1-4 1-6 1-1 1-1 1-3 1v-1-3c0-2 4-6 6-7 0-1 1-2 2-3v-1l3-3-2-1-1 1-1-1c1-1 1-1 1-3-2-1-2-1-4-1-1 2-2 2-3 3h-1v-4-1-4-1c0-1 1-1 2-2h0l1 1 1-1c0-1-1-1-2-2-1 0-1-1-2-2 0-1 0-1 1-2h0c0-1 0-2-1-2 2-2 2-3 4-5h0l-1-1c-1 1-1 2-2 2-2-2-1-10-1-13-1-4 0-10 0-14v-6-5c1-5 0-10 0-14l3-2h0c0-1 1-2 1-2l-1-1h-1l-1-1c2-1 4 0 5 0v-1l-1-1-1-1v-1c-1 0-3-2-4-3 0-1 1-1 0-2v-24c-1-1-1-2-1-2v-1c-1 1-2 1-3 1h0c-1-2-1-4-2-5 0-1 0-1-1-2 0-1-1-2-2-3-3-6-10-14-16-17l-1 1h-2l-2-2-1 1c0 1 0 2-1 3v-1-2c-1-1-2-1-3-2-2-3-4-5-7-8-4-4-9-6-15-9-12-6-26-11-40-13-9-1-19-1-28-1l17-4 17-3c-1 0-3 1-3 2 2 0 3-1 5 1l14 1h7 3v-1c2 0 4-1 6-1l23 2c-1-1-2-1-4-1-3-2-7-1-10-2h-2c-2 0-4-1-7-1 4-1 9-1 13-1 2 0 3-2 5-1h7 0 0c-2 2-5 1-8 1l2 1c3 0 5-1 7-1 3 0 8 1 11 0 1-1 1-1 1-2-1-2 0-4-1-5 0 0 0-1-1-1z"></path><path d="M362 520h1l2 2c0 1 1 2 2 3h-2c-2-2-3-3-3-5z" class="AP"></path><path d="M356 526h1c1 1 2 3 3 5h-1v1c-1-2-3-4-3-6zm-47-99c1 1 3 1 3 3v5l-3-7v-1z" class="S"></path><path d="M480 667c1 2 2 5 2 7 1 1 1 2 1 4l-1-1-1-3c-1-3-1-4-2-6l1 1v-2z" class="AP"></path><path d="M555 725l1 1c0-1 0-1 1-1l-4 7-2 1v-1l4-7z" class="S"></path><path d="M384 518c1 1 2 1 3 2v1l1 1c-2 0-2 0-4-1h-1c-1-1-2-1-3-1h4v-2z" class="E"></path><path d="M311 363c3 3 5 5 7 9h-1l-1-1v1l-1-1c-1-3-3-5-4-8z" class="S"></path><path d="M304 422c1-1 1-2 2-3l3 8v1h-2l-3-6z" class="z"></path><path d="M495 695h1v-2h1c1 1 0 3 1 5h0l-1 2 1 2c-1 0-1 0-2 1l-1-3v-5z" class="S"></path><path d="M304 277c-3-4-6-6-10-9v-1l4 3c1 0 2 0 3 1v1l1-1h0l2 1v5z" class="AF"></path><path d="M472 662h1c0 1 0 1 1 2 0 1 1 3 2 4 0 2 1 3 1 4v1c1 2 1 3 2 4h-1c-1-1-1-2-2-3 0 0 0-1-1-2 0-2-1-4-2-5v-1c-1-2-1-2-1-4z" class="AP"></path><path d="M398 527h2c1 1 3 4 4 5v1h0-1 0l1 2v1l-1-2c-1-1-2-1-3-3l-2-2v-2zm-29 8c1 0 5 4 6 5l-1 1 2 1c1 2 3 4 4 5s1 3 2 4h-1v-1c-1-2-2-3-4-4-1-1-1-2-3-4-1-1-3-2-4-4h1l-2-3z" class="B"></path><path d="M604 617c1 0 1-1 2-1v1h0c-1 1-1 2-1 3h-1v2c-1 1-1 2-1 3-1 1 0 0-1 2 0 0 0 2-1 3-1-1 0-2 0-3h0c0-2 0-1 1-2v-3h-1 0c0-1 0-2 1-3h0c1 0 1-1 2-2z" class="AJ"></path><path d="M354 533c1 3 2 8 4 11v2l-2-1v-2h0l-2-3c-1-2-1-4-2-5 1-1 1-2 2-2z" class="b"></path><path d="M441 709h0c4 6 7 14 9 21l-10-20 1-1z" class="S"></path><path d="M434 644c2 3 4 15 5 19h1l-1 1-1-3-3-4c0-3-1-6-2-8h0v-1c1 1 1 2 2 3h1c-1 0-1-1-1-2l-1-2v-3z" class="B"></path><path d="M104 172l17-3c-1 0-3 1-3 2 2 0 3-1 5 1h-8c-3 0-8 1-11 0zm334 478h0l7 18h-2l-2-1c-1-3-1-6-1-8-1-3-2-6-2-9z" class="J"></path><path d="M451 622h0c3 6 7 13 8 19h0c-4-4-8-13-9-18h1v-1z" class="E"></path><path d="M559 733c0-1 0-1 1-2 4-2 8-7 12-11l1 1c-2 3-5 7-9 10-1 1-4 1-5 2z" class="S"></path><path d="M550 680h1c0 2 0 2-1 4-1 1-2 2-2 3h-1v1c-2 2-3 4-4 7h-1 0l2-5c0-2 2-4 2-5v-1l1-1c0-1 0-1 1-2s1-1 2-1z" class="E"></path><path d="M258 323l1-2 2 2v1l1-1h1c0 1 0 2-1 3l1 1s1 1 1 2h0c-2 2 0 7-1 10l-2 2v2c-1 0-1-1-2-1l1-1v-1c1 0 1-1 2-2 2-5 0-9-2-13l-2-2z" class="S"></path><path d="M440 581v-1-1-3l8 14c-1 0-1 0-1 1h1c0 1 0 2 1 3h-1l-8-13z" class="L"></path><path d="M324 387l3 5 1 1c1 3 4 5 5 8h0c0 1 0 1-1 2-1-2-3-4-4-5h-1c1 0 1 0 1-1-1-1 0-1-1-1l-1-1c0-1-1-2-1-2l-2-3v-1h0l1-2z" class="AP"></path><path d="M438 608c-2-2-2-5-4-8v2h1v1 1 1l1 1c-1 0-1 0-1 1l1 1c-1 1 0 2-1 3-1-2 0-4-1-6 0-1 0-1-1-2v-1-3h-1v-1c0-1-1-1-1-2v-2c-1-1-1-1-1-2h1l-2-4v-1-1l-1-1h0l-1-1h1s1 0 1 1l2 6 1 1v1l3 8 3 7z" class="B"></path><path d="M439 630v-3-1l-1-1-1-2-1-2c-1-1-1-2-1-4h1v2l3 6 1 1c0 1 1 3 2 5 1 1 2 2 2 4s1 3 2 4 1 2 1 4c0 0 1 0 0 1h0-1 0c-2-5-5-10-7-14zm-71-115c2-1 4 0 5-1h4l-1-2 2-1v1 1 1c1 0 1-1 3 0h0l1 1c1 1 1 2 2 3v2h-4v-1c-1 0-2-1-3-1-3-3-5-3-9-3z" class="S"></path><path d="M324 387h0c3 3 5 7 8 9l2 2c1 1 1 2 2 3 1 3 5 6 8 8 2 2 4 5 6 7-5-3-10-8-13-12l-4-3c-1-3-4-5-5-8l-1-1-3-5z" class="E"></path><path d="M394 607c1 1 1 2 2 3v1c1 1 0 1 1 2l2 5v5c1 2 2 5 3 7 0 3 2 5 3 8 1 1 1 2 2 3 0 1 1 1 2 2 0 1 1 2 1 3 1 1 2 3 1 4 0 0-1-1-1-2v-1h-1l-3-6h0l-5-11-3-8c-1-2-2-3-3-6 1 0 1 0 1-1l-1-4c0-1 0-3-1-4z" class="AI"></path><path d="M355 448h0c2 1 2 3 3 4l2 3h0c1 2 2 3 3 4 2 1 3 2 4 3h1c-1-1-2-1-2-2v-1c-1 0-1-1-1-1h0c1 0 2 1 2 2 2 3 6 7 8 9 1 2 2 4 3 5h-1v1c-1 0-4-4-5-6s-3-3-4-5l-5-4c-1 0-1-1-2-1l-6-11z" class="E"></path><path d="M315 297v1l1 2c0 2 0 4 1 6h0v1c1 1 1 3 2 4s1 3 1 4l-1 1-1-2v1h-1c-2-4-3-9-4-14l2-4z" class="P"></path><path d="M377 518h0l-2-1s-1 0-1-1h-2l1 1s1 0 2 1h0l1 1c1 0 2 0 2 1h0c-3 0-6-3-9-4 2 2 4 3 6 4l-5-2c-6-3-14 1-20-3v-1l18 1c4 0 6 0 9 3z" class="a"></path><path d="M528 682l1-1c0-1 1-3 1-4l1-4c1-1 1-2 1-3l1-1-4 17c-2 7-2 13-5 20v2c0 1-1 2-1 2v-1c0-1 1-2 1-3 0-2 0-4 1-7v-8c2-2 2-6 3-9z" class="C"></path><path d="M307 428h2l3 7 6 13h-2c-1-3-3-5-5-7v-1l1-1h0c-2-3-4-5-5-7v-1-3z" class="j"></path><path d="M561 691v1c-1 1-1 1-1 2h0l1-1 1 1-1 2c-1 0-1 1-1 1v2c-1 1-3 5-3 7h0c-1 1-1 1-1 2l-3 3v2c0 1-1 2-1 3h0-1c1-3 1-6 2-9 0-2 1-4 2-6 1-1 1-3 1-4l1 2s1-1 1-2v-1c1-1 1-2 1-3l1-1v-1c0-1 0-2 1-2v-2c1-3 1-4 2-6l-2 10z" class="S"></path><path d="M313 301c-2-7-8-13-10-20-1-2-2-4-2-5h1c1 1 2 4 3 6 2 3 5 6 7 9l1 1 2 5-2 4z" class="AR"></path><path d="M338 508l-1-4h1c0 1 1 2 1 2h1c3 1 5 6 6 9 1 2 1 4 3 6 1 1 1 2 2 4-1-1-2-2-2-3h-1c0-1-1-2-2-2-2-1-5-7-7-9l-1-3z" class="E"></path><path d="M287 376c1 2 2 3 3 5 1 4 3 7 3 11l2 4-1 1h-1l-1 1c2 1 2 0 4 1v1c0 1 0 1 1 2h-1-1 0c-2-2-4-4-5-6 1-3 0-6-1-9l-2-9v-2z" class="R"></path><path d="M307 385h1c0 1 1 1 2 2h1v1l5 5v1h1c0 1 1 1 1 2h1v2l1 1c-1 0-1 1-1 1-5-3-9-4-13-8h1l2 2c1 1 1 1 2 1l-1-1-1-1c-1-3-4-5-5-7v-1c2 2 4 4 5 6l1 1v1c1 1 1 1 2 1 0-1 0-2-1-3h0l-1-1c-1-1-2-2-3-4v-1z" class="B"></path><path d="M487 661h2c1 3 2 5 3 9 0 2 1 4 1 7 1 1 1 3 1 4v1c1 0 2 1 2 2 0 3-1 7-1 10l-8-33z" class="i"></path><path d="M316 448h2c4 9 8 19 10 28l1 1v5h0c-1-3-1-6-3-9-1-4-5-8-7-12 0-4-1-10-3-13z" class="I"></path><defs><linearGradient id="AM" x1="490.064" y1="670.805" x2="495.166" y2="668.343" xlink:href="#B"><stop offset="0" stop-color="#6f6f6e"></stop><stop offset="1" stop-color="#8b8c89"></stop></linearGradient></defs><path fill="url(#AM)" d="M489 661l-1-3 1-2 2 3c1 2 3 5 4 7l1 2c0 1 1 2 1 3v1h-1-1v2c0 1 1 2 2 2l-1 1s0 1 1 2c-1 1-1 3-1 5 0-1-1-2-2-2v-1c0-1 0-3-1-4 0-3-1-5-1-7-1-4-2-6-3-9z"></path><path d="M504 698c1-2 2-3 3-5v-1h1c1 2 1 3 2 5l1 2c0 1 0 1-1 2v-1c0-1-1-3-1-4l-6 9c-1 0-1 1-2 1v7l-2-5c-1-2-1-3 0-5 0-2-1-3-1-5h0c1 0 2 0 3 1h0c1-1 2-1 3-1z" class="B"></path><path d="M498 698c1 0 2 0 3 1h0c1-1 2-1 3-1l-4 6c-1 0-1-1-1-1 0-2-1-3-1-5h0z" class="Y"></path><path d="M330 445c4 2 7 6 12 7 1 1 2 1 4 2h0 1 0 1l1 1c1 1 2 1 2 2h-1c-1 0-3-1-4-1-1-1-2-1-3-1l-2-1c-1-1-3-2-4-2v2c1 4 2 8 4 11v1c0 1-1 1-1 2h0v-3c-2-1-2-1-3-3 0-2-1-4-2-6 0 1-1 2-1 2v-4l-1-1c0-3-2-5-3-8z" class="B"></path><path d="M329 482l1 1c2 8 7 15 10 23h-1s-1-1-1-2h-1l1 4c-1-1-3-3-3-5s-1-2-1-4c-1-1-2-3-3-4l-3-6c-1-2-2-2-2-5l3-2h0z" class="z"></path><path d="M329 482l1 1-2 6c-1-2-2-2-2-5l3-2h0z" class="n"></path><path d="M592 693l3-3c-1 4-5 8-7 12l-20 28h1c1 0 3 0 4-1h0 3 0c1 0 1 0 2-1l1 1h0c-7 2-14 3-21 5v-1h1c1-1 4-1 5-2 4-3 7-7 9-10 0-1 1-2 1-2 2-3 4-5 6-8 0-2-1 0 0-1 4-4 7-10 11-14 0-1 1-2 1-3z" class="Y"></path><path d="M306 442l-1-1 1-1c2 0 2 0 4 2 1 2 1 3 2 4v-1-2c-1 0-1-1-1-1v-1c2 2 4 4 5 7 2 3 3 9 3 13l-3-4-10-13v-2z" class="x"></path><path d="M306 442l-1-1 1-1c2 0 2 0 4 2 0 3 0 4 2 6v1c-2-1-5-5-6-7z" class="p"></path><path d="M317 315h1c2 3 5 7 6 11 2 6 5 12 7 18l-1 1 1 1 1 1v1 1l-1 1c-1-2-1-4-2-6h-1c-2-3-3-7-4-10-3-6-5-12-7-19z" class="AD"></path><defs><linearGradient id="AN" x1="498.926" y1="712.744" x2="512.476" y2="706.057" xlink:href="#B"><stop offset="0" stop-color="#383635"></stop><stop offset="1" stop-color="#4f4f4d"></stop></linearGradient></defs><path fill="url(#AN)" d="M503 705l6-9c0 1 1 3 1 4v1h2l1 1h-4c1 1 2 3 3 4-3 0-3 0-4 1l-1 1c0 2-1 4-2 6-1 3 0 5-1 8l-1 4c-1-6-2-14 0-21z"></path><path d="M508 707h-1v-1c1-2 1-3 2-4 1 1 2 3 3 4-3 0-3 0-4 1z" class="K"></path><path d="M432 593h2c1 1 1 3 3 4l-1-1c0-2-1-3-1-4v-2l3 10c1 2 3 5 3 7v3 1c1 1 2 2 2 4 0 0 1 1 1 2l2 6 7 17c1 1 2 3 3 5v1h0 0c-1 0-1-1-1-2-1-1-2-3-3-5l-6-12v-1l-1-2-2-5c0-1-1-3-2-5 0-1-1-2-1-3-1-1-1-2-2-3l-3-7-3-8z" class="C"></path><path d="M486 690c1 0 1-1 1-1 1-2 1-3 1-5l1 7c1 6 4 11 6 16 1 3 2 6 2 9l2 5 1 6h0l-1-1c-3-8-7-16-10-25-1-3-1-8-3-11z" class="B"></path><path d="M640 584l1 1-4 9c0 2-1 4-2 7l-3 6c-2 4-3 8-6 12 0 2-1 3-2 4s-2 2-2 3l-3 5v-1h0c1-3 3-6 4-8l1-1c1-2 1-2 2-3 0-1 0-2 1-3l1-2c1-2 1-2 2-3 1-3 2-6 4-9 0-1 1-2 1-2v-1c1-1 1-2 1-3s0-1 1-2h-1l-1 2v1c-1 1-2 2-2 3s1 1 0 2h-1v1l-1 1-2 2h0l1-1v-1-1-1c1 0 1-1 2-1l1-3c1-2 1-2 1-4l-2 2-2 2v1c-1 1-1 1-2 1h0-1l-6 5v1l-2 1v1h-1 0l2-3 3-3 4-4c2 0 3-1 4-2l-1-3h0v-1l4-2 3-2c1-1 2-1 3-3z" class="U"></path><path d="M634 589h1 2c0 1 0 1-1 2-2 1-4 2-5 4l-1-3h0v-1l4-2z" class="c"></path><path d="M394 597h1v-1c1 1 1 1 1 3h0c1 1 3 3 3 5 1 0 1 1 2 1v1l16 38c3 7 7 13 11 20 2 5 4 10 7 15h-1v1c-3-5-5-9-8-14 0-1 0 0-1-1l1 2h-1c-1-2-4-7-4-8h1c-1-1-1-2-2-3s-1-2-2-3h1l1 1c1 1 1 2 2 3 0-2-1-3-1-4-2-3-5-8-6-11v-2l-1-1-1-2v-1c-1-2-2-5-3-7v-1l-3-5v-1c0-1-1-2-1-2v-2l-7-13-1 1c0-1-1-2-2-2 0-2-2-5-2-7z" class="B"></path><defs><linearGradient id="AO" x1="552.056" y1="669.561" x2="571.444" y2="665.439" xlink:href="#B"><stop offset="0" stop-color="#2b2a29"></stop><stop offset="1" stop-color="#5b5958"></stop></linearGradient></defs><path fill="url(#AO)" d="M583 643l2 1c-7 9-16 19-19 30-1 5-2 13-5 17l2-10c1-1 2-6 2-8l1-6c-5 3-10 6-14 9-2 2-4 3-7 4 2-2 4-4 5-4l2-2c4-5 10-7 15-11 4-3 6-6 8-10 1-1 2-2 2-3 2-2 4-5 6-7z"></path><path d="M322 482c2 1 2 2 4 2h0c0 3 1 3 2 5l3 6c1 1 2 3 3 4 0 2 1 2 1 4s2 4 3 5l1 3v2h-2c-2-2-4-5-5-8v-1h-1c-1-1-1-2-2-2h-2v-3l-1-3c-1-3-2-6-4-9v-5z" class="W"></path><path d="M322 482c2 1 2 2 4 2h0c0 3 1 3 2 5l3 6h-1c-2-1-3-6-5-6 0 2 1 4 2 5h0v1h-1v1c-1-3-2-6-4-9v-5z" class="o"></path><defs><linearGradient id="AP" x1="156.879" y1="161.177" x2="183.111" y2="186.333" xlink:href="#B"><stop offset="0" stop-color="#463d41"></stop><stop offset="1" stop-color="#81857f"></stop></linearGradient></defs><path fill="url(#AP)" d="M153 171l23 2c4 2 11 1 16 2 5 0 10 1 15 2l1 1-1 1-63-6h3v-1c2 0 4-1 6-1z"></path><defs><linearGradient id="AQ" x1="452.865" y1="702.112" x2="489.865" y2="692.277" xlink:href="#B"><stop offset="0" stop-color="#1d1c1c"></stop><stop offset="1" stop-color="#42403f"></stop></linearGradient></defs><path fill="url(#AQ)" d="M486 690l-9 19h-1c-2-1-4-1-6-1-5-1-9-1-14-2h0c-3-1-4-2-6-3l14 2c3 0 6 1 10 1 2-2 2-5 4-7 4-7 7-13 10-21v6c0 2 0 3-1 5 0 0 0 1-1 1z"></path><path d="M296 399c3 2 5 9 7 13 1 2 2 5 3 7-1 1-1 2-2 3l3 6v3 1c1 2 3 4 5 7h0l-1 1-1-2c-1-2-3-4-4-5-2-4-5-8-7-12-1-2-1-5-1-7l-3-12h0 1 1c-1-1-1-1-1-2v-1z" class="H"></path><path d="M300 419h3l1 3 3 6v3c-3-4-5-8-7-12z" class="w"></path><path d="M303 412c1 2 2 5 3 7-1 1-1 2-2 3l-1-3h-3v-1c0-2 0-2 1-4 0-1 1-2 2-2z" class="Q"></path><path d="M301 414l1 1v1c1 1 1 2 1 3h-3v-1c0-2 0-2 1-4z" class="Z"></path><defs><linearGradient id="AR" x1="283.851" y1="248.414" x2="290.405" y2="244.397" xlink:href="#B"><stop offset="0" stop-color="#797976"></stop><stop offset="1" stop-color="#a3a4a2"></stop></linearGradient></defs><path fill="url(#AR)" d="M268 225l1-1h1c1 0 0 0 1 1h1c1 2 2 3 4 3 6 5 12 11 16 17 2 4 4 9 5 14 2 4 4 8 5 12h0l-1 1v-1c-1-1-2-1-3-1-2-7-5-16-9-23-1-2-4-5-6-7l-15-15z"></path><path d="M415 530h4c1 1 5 5 5 6l-1 1 1 1v1l-2-1c0 1 1 2 2 4l5 10h0l5 11h-1l6 11c0 1 1 2 1 2v3 1 1l-24-46s1 0 1 1h0c0-2-1-4-2-6z" class="e"></path><path d="M415 530h4c1 1 5 5 5 6l-1 1 1 1v1l-2-1c0 1 1 2 2 4l5 10h0l5 11h-1c-1-1-2-4-3-5l-13-22c0-2-1-4-2-6z" class="Y"></path><path d="M622 626h1l-2 5c-1 2-3 4-4 6l-10 18c-3 6-6 12-10 18-2 3-5 5-7 8l-10 13-7 7h0c0-1 1-1 1-2 3-3 5-7 8-10 5-8 11-16 16-25 3-4 5-9 8-13v-1c2-2 4-4 5-6l4-7c1-1 1 0 1-1 1-1 1-1 1-2h1c1-1 1-2 1-3l3-5z" class="E"></path><path d="M497 679v-1c1 1 2 1 2 2 1 1 3 3 5 4 2 2 4 2 7 2l2-1-1 2h1 1l1 2v1h-2c0 1 1 1 1 2 1 0 1 0 1 1l3 3v2h0l-6-6-2-1h-2v1h-1v1c-1 2-2 3-3 5-1 0-2 0-3 1h0c-1-1-2-1-3-1-1-2 0-4-1-5h-1v2h-1 0v-1c0-3 1-7 1-10 0-2 0-4 1-5z" class="J"></path><path d="M499 680c1 1 3 3 5 4 2 2 4 2 7 2l2-1-1 2h1 1c-1 1-2 1-3 2l-1-1c-2 0-3 0-4 1 0 0-1 0-2 1v-4h0-1v1h-1l-1-2-1 1v-2l-1-4z" class="a"></path><path d="M497 679v-1c1 1 2 1 2 2l1 4v2h0l2 3h0c-1-1-2-1-2-2-2 3-2 5-2 8v2 1c-1-2 0-4-1-5h-1v2h-1 0v-1c0-3 1-7 1-10 0-2 0-4 1-5z" class="B"></path><path d="M208 178c13 5 24 13 36 21 8 6 15 11 21 20v2 1l-35-29c-7-5-15-11-23-14l1-1z" class="AL"></path><path d="M382 591c1 2 2 3 2 5h1v2l2 5c1 1 1 2 1 3s0 0 1 1v1h0l1-1v1c1 1 1 0 1 1h0 1s1 1 1 2c1 1 1 2 2 3l1 1v-1l-1-1v-1-1h-1v-1-1h0c-1-2-2-3-2-5v-2h-1c0-1-1-2-1-2l-3-4c0-1-1-2-1-3h1c0 1 1 2 2 3v1l2 2v-1h0l-1-1-3-9-2-2v-1-1h1c0 1 1 3 2 4 0-1-1-1-1-2s-1-2-1-3l1-1v-1c1 1 2 1 2 3h1l1 3 1 2h-1c0-1-1-2-1-3l-1-1-1-2c-1 1 0 2 0 3l1 1c0 3 2 6 4 8 0 1 0 1 1 2 0 2 2 5 2 7v1l2 3h0l-3-3-1-1v3c1 1 1 3 1 4l1 4c0 1 0 1-1 1 1 3 2 4 3 6l3 8 5 11c-1-1-2-1-2-2v-1l-1-1v1c1 1 1 2 1 2 0 2 1 2 1 3l1 2 1 2c1 1 1 2 1 3 1 1 1 0 1 2h0v1s1 1 1 2h0c0 1 1 1 1 2l1 3 1 1v1c2 1 2 3 3 4s0 1 1 2c0 1 0 1 1 2v2l1 1v2l-2-2c-1-3-3-6-4-9 0-1-1-1-1-2-3-7-5-14-9-21 0-1-1-2-1-3-1-3-2-6-3-8-1-3-3-6-5-9h0v-1c-1-2-2-3-2-5h0c-3-3-5-9-6-13l-4-11z" class="S"></path><path d="M503 726l1-4c2 8 5 14 7 21l5-16h0 2c-1 5-3 10-4 15s-2 10-2 16v20 56c0 9 0 19-1 28v-25l-1-52v-21c0-6 0-12-1-18-1-7-4-13-6-20z" class="M"></path><path d="M274 358l2 2h1c4 3 7 11 10 16v2l2 9c1 3 2 6 1 9h-2-1v1h-1c0-1-1-2-2-3l-2-5-3-12c1-2 0-5-1-7-1-4-5-8-8-10h0 2l2-2z" class="AI"></path><path d="M274 358l2 2h1l1 4c-2-2-4-3-6-4l2-2z" class="m"></path><path d="M285 387h4c1 3 2 6 1 9h-2-1v-3c0-2-1-4-2-6z" class="V"></path><path d="M283 377c1 0 2 1 3 1h1l2 9h-4l-2-10z" class="Z"></path><path d="M277 360c4 3 7 11 10 16v2h-1c-1 0-2-1-3-1-1-5-2-8-5-13l-1-4z" class="W"></path><path d="M298 414c0 2 0 5 1 7 2 4 5 8 7 12 1 1 3 3 4 5l1 2v1 1s0 1 1 1v2 1c-1-1-1-2-2-4-2-2-2-2-4-2l-1 1 1 1v2l-3-4-1-1-2 2-2-2 1-1c-1-1-1-2-2-2-2-1-2-1-3 0l-2-3-1-1v-1c0-1 1-2 1-2v-1h-2c0-1 1-2 1-3 1-2 1-5 1-7l1 1c1 0 1 1 2 2l1-1-2-2v-1h3l1-3z" class="T"></path><path d="M292 428c1 0 2-1 3-1v1l1 1 1-1v-1h0c1 0 1 0 2-1 0 1 1 2 2 3l-1 2c1 1 2 2 2 4h-1v-1c-2-1-5-2-6-3h-1l-2 2-1-1v-1c0-1 1-2 1-2v-1z" class="I"></path><path d="M298 414c0 2 0 5 1 7 2 4 5 8 7 12 1 1 3 3 4 5l-2 1v-1c-2-1-4-3-5-5 0-1 0 0-1-1l-1-3c-1-1-2-2-2-3-1 1-1 1-2 1h0v1l-1 1-1-1v-1c-1 0-2 1-3 1h-2c0-1 1-2 1-3 1-2 1-5 1-7l1 1c1 0 1 1 2 2l1-1-2-2v-1h3l1-3z" class="V"></path><defs><linearGradient id="AS" x1="349.1" y1="403.165" x2="336.562" y2="348.454" xlink:href="#B"><stop offset="0" stop-color="#575756"></stop><stop offset="1" stop-color="#8b8c89"></stop></linearGradient></defs><path fill="url(#AS)" d="M331 344l16 36c0 1 1 2 1 4 1 2 3 5 5 6h1l1 1-1 1v1c2 1 2 2 3 3 1 2 2 3 3 4s1 2 2 2c2 2 6 10 7 10 1 3 2 5 2 8-1-1-3-3-3-6-1-1-1-2-2-3l-4-7c-1 1-1 1-1 2-2-1-3-3-3-4-1-1-2-2-2-3h-2v1c0 1 1 3 0 4v1 2l-1 1c-4-4-2-12-4-17-2-6-5-12-8-17l-8-19c-2-4-4-7-5-11h1c1 2 1 4 2 6l1-1v-1-1l-1-1-1-1 1-1z"></path><path d="M354 400c-1-2-1-5-2-8 4 4 7 8 10 12-1 1-1 1-1 2-2-1-3-3-3-4-1-1-2-2-2-3h-2v1z" class="B"></path><path d="M547 674l8-9 1-1 3-3c3-1 5-3 7-4l1-1c0-1 0-1 1-1 1-1 2-1 2-2 1-1 2-2 4-2l-1 1 1 1h1c-2 4-4 7-8 10-5 4-11 6-15 11l-2 2c-1 0-3 2-5 4 3-1 5-2 7-4 4-3 9-6 14-9l-1 6-1 1h0l-1-1 2-1v-2-1l-3 2c-2 0-3 1-4 2l-1 1c-1 0-1 0-2 1h-1v1l-2 1h0c-1 1-1 1-2 1l-2 2h-1c-2 1-1 0-2 1-1 0-1 1-2 1l-3 3c-1 0-1 0-2-1l2-2v-1l1-1c0-1 1-2 1-3 1-1 1-2 1-3h1c0-1 1-2 1-2v-3c0-1 1-2 1-3l1-1c0-1 0-2 1-2v-2c1-1 1-2 2-2h0c-1 3-4 10-4 13v1 1h1z" class="S"></path><path d="M547 674l8-9 1-1 3-3c3-1 5-3 7-4l1-1c0-1 0-1 1-1 1-1 2-1 2-2 1-1 2-2 4-2l-1 1 1 1c-2 2-4 3-6 5-2 1-3 2-5 2-2 2-5 4-7 5-4 5-7 10-12 13 1-2 2-3 3-4z" class="B"></path><path d="M318 474c-2-4-4-9-7-13l-7-14c-1-2-2-4-2-6l1-1 3 4 10 13 3 4c2 4 6 8 7 12 2 3 2 6 3 9l-3 2h0c-2 0-2-1-4-2-1-2-2-5-3-7l-1-1z" class="t"></path><path d="M316 457l3 4c2 4 6 8 7 12 2 3 2 6 3 9l-3 2h0c-2 0-2-1-4-2-1-2-2-5-3-7l-1-1 1-1h2c0 1 1 1 1 2l1-1-2-3v-1l-2-2-1 1c-1-1-2-1-3-2l2-1 2 1 1-1c-1-1-2-1-2-3l-2-2h-1v-1c1-1 1-1 1-2v-1z" class="N"></path><path d="M326 473c2 3 2 6 3 9l-3 2h0v-4h-1l-1-1 2-1c0-1 0-2-1-3h0l1-2z" class="AK"></path><path d="M508 707c1-1 1-1 4-1 1 3 3 6 4 10 0 3 1 7 0 11h0l-5 16c-2-7-5-13-7-21 1-3 0-5 1-8 1-2 2-4 2-6l1-1z" class="AP"></path><path d="M427 694c1 4 3 7 5 10l8 20-17-2c1-8 0-16 0-24v-1c0-1 3-2 4-3z" class="AO"></path><path d="M507 670h0c3 0 6 0 9-1h0l2 1 1-1 2-1c1-1 3-1 4-2l-1 2-2 6c0 2-1 4-3 6v1c-1 2-3 3-6 4l-2 1c-3 0-5 0-7-2-2-1-4-3-5-4 0-1-1-1-2-2v1c-1-1-1-2-1-2l1-1c-1 0-2-1-2-2v-2h1 1v-1c0-1-1-2-1-3l1 1c1 0 1 0 2 1s2 1 4 1h2c1 0 1 0 2-1z" class="J"></path><path d="M514 680c3 0 6-4 8-6 0 2-1 4-3 6v1c-1 2-3 3-6 4l-2 1v-4c1-1 1-2 3-2z" class="f"></path><path d="M504 674h0 1l1 1c3 0 6-1 9-1h1 1c1-1 2-1 3-1l1-1c-1 2-2 2-3 3s-1 1-1 2l-2 1h0-2-1-2 0-2c-2-1-4-1-5-2l1-1v-1z" class="K"></path><path d="M507 670h0c3 0 6 0 9-1h0l2 1 1-1 2-1c1-1 3-1 4-2l-1 2c-1 1-2 2-3 2-4 3-12 3-17 3h-1l-6-3v1c0-1-1-2-1-3l1 1c1 0 1 0 2 1s2 1 4 1h2c1 0 1 0 2-1z" class="P"></path><path d="M497 672l1 1h1 1 0c1 1 3 1 4 1v1l-1 1c1 1 3 1 5 2h2 0 2 1 2 0l-1 2c-2 0-2 1-3 2v4c-3 0-5 0-7-2-2-1-4-3-5-4 0-1-1-1-2-2v1c-1-1-1-2-1-2l1-1c-1 0-2-1-2-2v-2h1 1z" class="i"></path><path d="M497 672l1 1c1 2 4 6 4 7h-1c0 2 2 2 3 4-2-1-4-3-5-4 0-1-1-1-2-2v1c-1-1-1-2-1-2l1-1c-1 0-2-1-2-2v-2h1 1z" class="e"></path><path d="M270 360c3 2 7 6 8 10 1 2 2 5 1 7l3 12-1 2-1-1c-1 1-1 2-1 3l1 1h0c-1 1-1 2-1 2-1 1-1 1-2 1l-1-1c-1 1-1 2-3 2l2 2-2 1c-1-2-1-3-1-4 0-2 0-3-1-5 0-2 0-3-2-4l-1-7v-3c0-1 0-1 1-1v-1-3c0-3 0-6 1-9v-3h-1l1-1z" class="j"></path><path d="M278 370c1 2 2 5 1 7h0c-1 1-1 1-2 1 1-4-1-3-3-6h2l2-2z" class="Z"></path><path d="M277 378l-1 1c-1-1-2-3-3-4h2 0l-2-2c-1-2-1-6-1-8h1c0 2 1 4 1 6v1c2 3 4 2 3 6z" class="u"></path><path d="M269 376c0 2 1 3 1 5s1 3 1 4c1 3 1 5 2 8h0v2 3l2 2-2 1c-1-2-1-3-1-4 0-2 0-3-1-5 0-2 0-3-2-4l-1-7v-3c0-1 0-1 1-1v-1z" class="R"></path><path d="M260 208h1l4 2 6 4c6 3 11 5 16 9 2 3 2 5 3 8 0 5 1 9 2 13v1c-4-6-10-12-16-17-2 0-3-1-4-3h-1c-1-1 0-1-1-1h-1l-1 1-3-3v-1-2c0-4-4-7-6-10l1-1z" class="S"></path><path d="M260 208h1l4 2 6 4c-1 1-3 1-4 2 2 5 5 8 9 12-2 0-3-1-4-3h-1c-1-1 0-1-1-1h-1l-1 1-3-3v-1-2c0-4-4-7-6-10l1-1z" class="X"></path><path d="M260 208h1l4 2 6 4c-1 1-3 1-4 2-2-3-4-6-7-8zm186 436c1 2 3 4 3 6l-1 1-2-1c-1-2-3-3-4-5l-1-1v-1l-11-25-1-2s-1-1-1-2l-1-1c-1-1-1-2-2-4l-3-6-1-1s0-1-1-2v-1l-1-2v-1l-3-3c-1-2-2-3-2-4v-1l-1-1h0l-1-1v-2l-1-2h-1c0 1 1 2 1 4 1 0 1 1 1 2l1 2h-1v-1c-1 0-1-1-1-2h0c0-1-1-2-1-3l-3-6h0c-1-1-1-2-2-3s-2-3-2-4l-1-1s-1-1-1-2c-1 0-1-1-2-1 0-1 0-2-1-3h-1c0-1 0-2-1-3v-1c1 1 2 2 2 4l2 2 5 7v1l2 2v-1l-1-3-3-3v-1l-1-2c-1-1-2-1-3-2l1-1 1 1c1 2 0 1 1 2l2 2 1 1h0c0-1 0-1-1-2h0c-1-2-2-3-3-4v-1c3 3 4 5 6 8 1 1 2 2 3 4l1 1v2c1 1 1 1 1 2s0 1 1 2l1 2 1 1c1 6 6 11 9 17l1 1c2 2 3 5 4 8l1 1h0 1 0c1 1 1 3 2 5l6 14c2 4 5 9 7 14h0z" class="B"></path><path d="M237 288h1l1 3c0 1 0 2 1 3 0 1 0 1 1 2v-2l1-1c0-1 1-1 1-2l1-3c1 1 3 2 3 3 4 4 6 10 8 15 1 2 3 6 3 8h-2 0c-3-1-3-3-5-2v2c-1 3-5 5-8 6l1 2-1 1c-1 0-1-1-1-1-1-1-1-1-2-1l-1-1v-7c0-5 0-12-1-18 0-1-1-2 0-3h0c-1-2-1-2-1-4z" class="v"></path><path d="M243 300c3 0 3 0 5-2l-2-2v-1c2 0 2 0 3 1v1 1h0c1 2 1 2 2 3h0c0 1 0 1 1 2h0c-1 1-2 1-3 3 0 0-1 1-2 1 0 1-1 1-2 2h-1c0 1 0 1-1 2v-11z" class="t"></path><path d="M252 303v2l1 1h-1l1 1 1 1c0 1 0 2 1 3 0 1 1 2 1 3h0c-3-1-3-3-5-2v2c-1 3-5 5-8 6v-9c1-1 1-1 1-2h1c1-1 2-1 2-2 1 0 2-1 2-1 1-2 2-2 3-3z" class="g"></path><path d="M252 303v2 1c-1 0-1 1-1 2v1c0 1-2 1-3 2-1 0-2-1-3-2 1-1 2-1 2-2 1 0 2-1 2-1 1-2 2-2 3-3z" class="G"></path><path d="M237 288h1l1 3c0 1 0 2 1 3 0 1 0 1 1 2v-2l1-1c0-1 1-1 1-2v9 11 9l1 2-1 1c-1 0-1-1-1-1-1-1-1-1-2-1l-1-1v-7c0-5 0-12-1-18 0-1-1-2 0-3h0c-1-2-1-2-1-4z" class="W"></path><path d="M434 563c2 3 4 8 7 11 1 3 3 6 5 9l6 9c5 8 10 17 17 25l1 1 3 4 5 4c2 2 3 3 5 3l1 1h0-1l1 2h-1l-4-2v2c2 1 1 3 4 2h0l2-2h1l1 1c-2 2-4 4-5 6v3l-1 4-33-52h1c-1-1-1-2-1-3h-1c0-1 0-1 1-1l-8-14s-1-1-1-2l-6-11h1z" class="P"></path><path d="M468 621c1 0 2 0 2 1 2 2 4 3 6 5 0 0-1 2 0 2 0 2 1 1 0 3l-8-11z" class="a"></path><path d="M476 627c1 1 2 3 3 3h0v2c2 1 1 3 4 2h0l2-2h1l1 1c-2 2-4 4-5 6-2 0-5-5-6-7 1-2 0-1 0-3-1 0 0-2 0-2z" class="J"></path><defs><linearGradient id="AT" x1="464.064" y1="617.315" x2="470.77" y2="614.749" xlink:href="#B"><stop offset="0" stop-color="#1e1f1f"></stop><stop offset="1" stop-color="#3d3a39"></stop></linearGradient></defs><path fill="url(#AT)" d="M434 563c2 3 4 8 7 11 1 3 3 6 5 9l6 9c5 8 10 17 17 25l1 1 3 4 5 4c2 2 3 3 5 3l1 1h0-1l1 2h-1l-4-2h0c-1 0-2-2-3-3-2-2-4-3-6-5 0-1-1-1-2-1-8-9-13-21-20-31l-8-14s-1-1-1-2l-6-11h1z"></path><path d="M327 502h2c1 0 1 1 2 2h1v1c1 3 3 6 5 8h2v-2c2 2 5 8 7 9 1 0 2 1 2 2h1c0 1 1 2 2 3l3 8c-1 0-1 1-2 2 1 1 1 3 2 5l2 3c-2 1-2 1-4 1 1 2 2 4 2 7v2l-1 1c0 1 0 2-1 4l-2-1c0-2-1-5-1-8v-2c0-1-1-1-1-2v-1h0c0-2-1-2-1-2v-1-1c1-1-1-5-2-7-1-3-1-4-4-7h-2l-2-3-2-2c0-1 0-1-1-2 0-1-1-1-2-3-1-1-1-1 0-3l-1-1h-1l-2-1v-1c-1-1 0-1-1-1v-1l-1 1c-1 0-1 0-2-1l-2 1-1-1 1-1-1-2 6-3z" class="Z"></path><path d="M349 530c2 1 2 3 3 5 1 1 1 3 2 5l2 3c-2 1-2 1-4 1 0-5-3-9-3-14z" class="H"></path><path d="M349 547v-7h1c1 4 2 9 3 14 0 1 0 2-1 4l-2-1c0-2-1-5-1-8v-2z" class="b"></path><path d="M339 511c2 2 5 8 7 9 1 0 2 1 2 2h1c0 1 1 2 2 3l3 8c-1 0-1 1-2 2-1-2-1-4-3-5h0c-1-1-2-3-2-4 0-2-1-3-2-4l-6-9v-2z" class="U"></path><path d="M327 502h2c1 0 1 1 2 2l3 8 2 3c2 5 7 8 10 13v1c1 1 1 1 1 3s2 5 3 8h-1v7c0-1-1-1-1-2v-1h0c0-2-1-2-1-2v-1-1c1-1-1-5-2-7-1-3-1-4-4-7h-2l-2-3-2-2c0-1 0-1-1-2 0-1-1-1-2-3-1-1-1-1 0-3l-1-1h-1l-2-1v-1c-1-1 0-1-1-1v-1l-1 1c-1 0-1 0-2-1l-2 1-1-1 1-1-1-2 6-3z" class="Q"></path><path d="M327 502h2c1 0 1 1 2 2l3 8h-1l-2-2c0-1 0-1-1-2 0-1 0-1-1-2l-1 1v1h-3-3v-1l-1-2 6-3z" class="R"></path><path d="M519 681c2-1 3-3 4-4h1l1 6v8 8c-1 3-1 5-1 7 0 1-1 2-1 3v1c-1 2-1 3-1 4h1v2c-1 1 0 1-1 2v2h-1v1c-1 1 0-1-1 1v1c0 2-1 3-2 4h0-2c1-4 0-8 0-11-1-4-3-7-4-10-1-1-2-3-3-4h4l-1-1h-2c1-1 1-1 1-2l-1-2c-1-2-1-3-2-5v-1h2l2 1 6 6h0v-2l-3-3c0-1 0-1-1-1 0-1-1-1-1-2h2v-1l-1-2h-1-1l1-2c3-1 5-2 6-4z" class="S"></path><path d="M514 700l2 4-2 2c0-2-1-2-1-4l-1-1 2-1z" class="M"></path><path d="M508 692v-1h2l2 1 2 8-2 1h-2c1-1 1-1 1-2l-1-2c-1-2-1-3-2-5z" class="O"></path><path d="M515 689h1c1-1 1-1 1-2 1 0 2-1 3-2 0-1 0-1 1-2 1 1 1 5 0 6v2c-1 3 0 6-1 9l-1-2h-1v-2l-3-3c0-1 0-1-1-1 0-1-1-1-1-2h2v-1z" class="Y"></path><path d="M509 702h4c0 2 1 2 1 4l2-2c4 7 3 15 2 23h-2c1-4 0-8 0-11-1-4-3-7-4-10-1-1-2-3-3-4z" class="d"></path><path d="M509 702h4c0 2 1 2 1 4h-1l3 5c1 2 0 3 0 5-1-4-3-7-4-10-1-1-2-3-3-4z" class="f"></path><path d="M260 325c2 4 4 8 2 13-1 1-1 2-2 2v1l-1 1c1 0 1 1 2 1 5 2 8 6 11 10l4 4v3l-2-2-2 2h-2 0l-1 1h1v3c-1 3-1 6-1 9v3 1c-1 0-1 0-1 1v3l1 7-1-1c-2-1-2-2-3-4l-1 4c-1-1-1-2-1-3v-3c-3-2-1-3-3-6 0-1-1-2-2-3l-1-1v-2h2c0-1 0-1-1-2 1-2 1-2 1-4h2v-3l-2-2h0v-1l2-1v-1-1h-2c0 1-1 2-2 3-1-1-1-1-1-2 1-1 0-2 1-3v-2h1v1l1 1c1-2 1-2 1-4l-3-3-1 1 1 1v1c-1 0-1 1-2 1l1-1c-1-1 0-1-1-2h0v-1-1h-1l1-2c1 0 2-1 2-1l2-2-1-1v-2h0v-1-1-5c1-1 1-1 0-2v-1c1 0 1-1 2-1z" class="k"></path><path d="M266 353h0l-1-2c1-1 3 1 4 2l5 5-2 2h-2l-2-2c0-2 0-3-2-5z" class="o"></path><path d="M259 358l1-1c1 0 1 1 2 2l1 1 2-1v3l-1 1v1c1 0 1 0 1 1v1h-1c0 1-1 1-2 1v1c0 1-1 1-1 2-1 0-1 0-2-1 0-1 0-1-1-2 1-2 1-2 1-4h2v-3l-2-2h0z" class="w"></path><path d="M266 353c2 2 2 3 2 5l2 2h0l-1 1h1v3c-1 3-1 6-1 9v3 1c-1 0-1 0-1 1v3l1 7-1-1c-2-1-2-2-3-4l-1 4c-1-1-1-2-1-3v-3c-3-2-1-3-3-6 0-1-1-2-2-3l-1-1v-2h2c1 1 1 1 2 1 0-1 1-1 1-2v-1c1 0 2 0 2-1h1v-1c1-3 0-7 0-10l1-2z" class="AC"></path><path d="M265 371v2 10l-1 4c-1-1-1-2-1-3v-3c0-2 0-6 1-7v-2l1-1z" class="Z"></path><path d="M265 366v5l-1 1v2c-1 1-1 5-1 7-3-2-1-3-3-6 0-1-1-2-2-3l-1-1v-2h2c1 1 1 1 2 1 0-1 1-1 1-2v-1c1 0 2 0 2-1h1z" class="m"></path><path d="M212 238v-4h1c8 4 14 16 18 24 1 2 3 6 2 9-1 1-2 2-4 2l-2 1c-1-1-3-1-4-2v-1h-1-2v1h-4v-1c-1 0-3-2-4-3 0-1 1-1 0-2v-24z" class="G"></path><path d="M220 267v-1c0-1 0-1-1-1l1-1c1 1 2 1 3 0h1v-2h1c1 1 1 0 1 1-1 2 0 3 0 4l1 3c-1-1-3-1-4-2v-1h-1-2z" class="t"></path><path d="M231 258c1 2 3 6 2 9-1 1-2 2-4 2l-2 1-1-3c1-1 2-2 2-3l3 2v-1-1c-1-1-1-2-2-2 1-2 1-3 2-4z" class="r"></path><path d="M324 326c3 2 5 5 8 8 0 1 1 3 2 3h1l13 18 6 6h-1c-1 0-1-1-2-1h-1 0 0v3 2h0c1 2 2 5 3 7 0 1 0 2 1 3-1-1-2-2-3-1 0 2 2 5 1 8h0c0 2 1 4 0 6h1c0 1 1 1 1 2h-1c-2-1-4-4-5-6 0-2-1-3-1-4l-16-36c-2-6-5-12-7-18z" class="d"></path><path d="M351 379c-3-3-6-8-7-12 0-1 0-1-1-2 0-2-1-3-1-5h0c2 2 2 3 3 5 0 1 0 1 1 1l5 13z" class="K"></path><path d="M347 357v-1h1v-1l6 6h-1c-1 0-1-1-2-1h-1 0 0v3 2h0c1 2 2 5 3 7 0 1 0 2 1 3-1-1-2-2-3-1 0 2 2 5 1 8l-1-3-5-13-1-3v-1c-1-1-1-3 0-4 0 1 1 2 1 2h1v-3z" class="O"></path><path d="M347 357c1 2 2 4 2 6-1 2 0 5 1 7-1-2-3-7-5-7v-1c-1-1-1-3 0-4 0 1 1 2 1 2h1v-3z" class="K"></path><path d="M189 160h3c2 2 3 5 6 7l2 5c2 0 2 0 4 1h1l1 1c1 0 3 0 4 1l1 1c-2 0-4-1-7 0 12 3 21 10 32 16l1-1c1 0 2 0 3 1 1 0 1-1 1-1 3 0 6 3 9 5 2 1 5 2 8 4 3 3 8 3 11 7-2 0-3 0-4-1-1 0-3 0-4 1h1c1 1 2 1 3 2l1 1h-1l-4-2h-1l-1 1c2 3 6 6 6 10-6-9-13-14-21-20-12-8-23-16-36-21l-1-1c-5-1-10-2-15-2-5-1-12 0-16-2-1-1-2-1-4-1-3-2-7-1-10-2h-2c-2 0-4-1-7-1 4-1 9-1 13-1 2 0 3-2 5-1h7 0 0c-2 2-5 1-8 1l2 1c3 0 5-1 7-1 3 0 8 1 11 0 1-1 1-1 1-2-1-2 0-4-1-5 0 0 0-1-1-1z" class="O"></path><path d="M182 172h1c2-1 4-1 6-2h0l2 1c2 0 3 1 6 0h1c1 1 1 1 2 1 2 0 2 0 4 1h1l1 1c1 0 3 0 4 1l1 1c-2 0-4-1-7 0-8-3-16-2-25-3v-1h3z" class="P"></path><path d="M189 160h3c2 2 3 5 6 7l2 5c-1 0-1 0-2-1h-1c-3 1-4 0-6 0l-2-1h0c-2 1-4 1-6 2h-1c-3-2-7-1-11-2h0l1-1c3 0 5-1 7-1 3 0 8 1 11 0 1-1 1-1 1-2-1-2 0-4-1-5 0 0 0-1-1-1z" class="i"></path><path d="M189 160h3c2 2 3 5 6 7l2 5c-1 0-1 0-2-1h-1c-2-1-3-2-5-3 1 0 0-1 0-1v-4l-2-2s0-1-1-1z" class="B"></path><path d="M192 168h1c1-1 0-1 1-1s2 0 2 2l1 1h0l1 1h-1c-2-1-3-2-5-3z" class="AP"></path><path d="M236 192l1-1c1 0 2 0 3 1 1 0 1-1 1-1 3 0 6 3 9 5 2 1 5 2 8 4 3 3 8 3 11 7-2 0-3 0-4-1-1 0-3 0-4 1h1c1 1 2 1 3 2l1 1h-1l-4-2h-1l-1 1c-3-3-7-5-10-8-5-3-9-6-13-9z" class="P"></path><path d="M243 320c3-1 7-3 8-6v-2c2-1 2 1 5 2h0 2v3c-1 1-2 2-2 4l2 2 2 2c-1 0-1 1-2 1v1c1 1 1 1 0 2v5 1 1h0v2l1 1-2 2s-1 1-2 1c0-1 0-1 1-2v-1l1-1-1-1c-1 1-2 2-3 2l1-2c-2 0-1 1-2 1h-2l-1 1v1h1v1h-1c-1 0-1 1-2 2h2c-1 1-1 1-2 3l-1 1v1h-1v1c1 0 0 0 1 1h-1l-2 1v1h-1-2l-1-1h0l1-1c0-1-1-1-1-1 0-2 1-1 0-3h-1v-1h0v-1-4l-2-1h-1 0-1l2-2 2 1 1-2c0-2 0-2-1-4l1-1v-3-1h-2c-1 0-1-1-2-1v1c-1 0-2-1-2-1v-1h2c0-1 1-1 2-2 2-1 2-1 2-3l1 1c1 0 1 0 2 1 0 0 0 1 1 1l1-1-1-2z" class="W"></path><path d="M243 320c3-1 7-3 8-6v-2c2-1 2 1 5 2h0 2v3c-1 1-2 2-2 4l2 2 2 2c-1 0-1 1-2 1v1c1 1 1 1 0 2v5l-2-1h-1v-1h-2l-1-1 1-1h1 1c0-1 1-1 1-2 1-1 1-2 1-3 1 0 1 0 1-1l-1 1c-1-1-3-1-4-1 0 1-1 2-2 2 0 0-1-1-1-2v-1h-1v2h1c0 1-1 2-1 2v1c-1 0-1 0-2 1h0-1c0-1-1-3-2-3l-1-1h0-1l-1-1-1 1-1 2h-2c-1 0-1-1-2-1v1c-1 0-2-1-2-1v-1h2c0-1 1-1 2-2 2-1 2-1 2-3l1 1c1 0 1 0 2 1 0 0 0 1 1 1l1-1-1-2z" class="j"></path><path d="M352 544c2 0 2 0 4-1h0v2l2 1v-2l1 2h0c1 3 3 5 4 7 0 1 2 2 2 3 0 2 2 3 3 4 0 1 2 4 2 5l12 26 4 11v1h-2c-1-2-2-5-3-8s-1-3-3-5c-1 1-1 1-2 1l-1-2c-1 1-1 2-2 3-1-2-1-3-2-5h-1v3h-1c-1-1-1-2-2-3l-2-3c-2 0-2-2-3-3l-2-1c-1-2-2-3-3-4-1 0-1-1-1-2v-2l1-2 1-1c-1-1-1-2-2-2v-1-4c-1 0-2-7-2-8v-1-2c0-3-1-5-2-7z" class="H"></path><path d="M372 583c0 2 1 3 1 4 1 1 1 2 2 2-1 1-1 2-2 3-1-2-1-3-2-5 0-1 0-2 1-4z" class="b"></path><path d="M363 553c0 1 2 2 2 3 0 2 2 3 3 4 0 1 2 4 2 5 0 2 0 3 1 5 1 1 1 1 0 2h0v5 1c-1-2-1-3-1-5v-2l-1 1-2-1h0l-1-1-3-6c1-1 1-3 1-4l-1-7z" class="AC"></path><path d="M364 560v1c1 0 2 1 3 2v8l-1-1-3-6c1-1 1-3 1-4z" class="Q"></path><path d="M367 571l2 1 1-1v2c0 2 0 3 1 5v1c0 1 1 3 1 4-1 2-1 3-1 4h-1v3h-1c-1-1-1-2-2-3v-1c-1-2-1-3-1-5l1-6c-1-1 0-3 0-4z" class="U"></path><path d="M369 583c0-2 0-4 1-6 0 1 0 1 1 2 0 1 1 3 1 4-1 2-1 3-1 4h-1c0-1 0-3-1-4z" class="z"></path><path d="M367 575l-1 4 1 3c1 1 1 1 2 1 1 1 1 3 1 4v3h-1c-1-1-1-2-2-3v-1c-1-2-1-3-1-5l1-6z" class="b"></path><path d="M363 564l3 6 1 1h0c0 1-1 3 0 4l-1 6c0 2 0 3 1 5v1l-2-3c-2 0-2-2-3-3l-2-1c-1-2-2-3-3-4-1 0-1-1-1-2h1s1 1 1 2h1 2 2c0-3 1-7 0-9l-1-1 1-2z" class="W"></path><path d="M364 579v-4h1v5l1 1c0 2 0 3 1 5v1l-2-3c-2 0-2-2-3-3h1l1 1v-3z" class="k"></path><path d="M356 574h1s1 1 1 2h1 2 2c1 1 1 2 1 3v3l-1-1h-1l-2-1c-1-2-2-3-3-4-1 0-1-1-1-2z" class="H"></path><path d="M352 544c2 0 2 0 4-1h0v2l2 1v-2l1 2h0c1 3 3 5 4 7l1 7c0 1 0 3-1 4l-1 2 1 1c1 2 0 6 0 9h-2-2-1c0-1-1-2-1-2h-1v-2l1-2 1-1c-1-1-1-2-2-2v-1-4c-1 0-2-7-2-8v-1-2c0-3-1-5-2-7z" class="Q"></path><path d="M352 544c2 0 2 0 4-1h0c-1 2-1 2-1 4 0 1 0-1 0 1v3h0v1 2h-1v-1-2c0-3-1-5-2-7z" class="s"></path><path d="M358 544l1 2h0v8s-2-1-2-2 0-5-1-7l2 1v-2z" class="R"></path><path d="M359 546c1 3 3 5 4 7l1 7c0 1 0 3-1 4l-1 2 1 1c1 2 0 6 0 9h-2c-1-3-2-4-2-7v-1-5-9-8z" class="h"></path><defs><linearGradient id="AU" x1="380.286" y1="463.818" x2="383.992" y2="462.085" xlink:href="#B"><stop offset="0" stop-color="#5f5f5d"></stop><stop offset="1" stop-color="#858584"></stop></linearGradient></defs><path fill="url(#AU)" d="M354 400v-1h2c0 1 1 2 2 3 0 1 1 3 3 4 0-1 0-1 1-2l4 7c1 1 1 2 2 3 0 3 2 5 3 6l9 10c-2 0-7-6-8-7h-2l1 1-1 1c1 1 2 3 1 4l-1 1h0c5 14 14 27 19 41v2 2l2 1v1c2 7 5 12 9 18l6 7c1 2 1 4 2 6h0l2 2c2 4 7 8 11 10l-1 2-3-3c-3-3-6-6-10-8 2 7 4 13 8 19 1 2 2 4 2 6h0c0-1-1-1-1-1-6-7-10-17-14-26-2-4-5-8-7-13l-9-18-11-26-10-22c-4-8-7-15-12-22l1-1v-2-1c1-1 0-3 0-4z"></path><path d="M354 400v-1h2c0 1 1 2 2 3 0 1 1 3 3 4 0-1 0-1 1-2l4 7c1 1 1 2 2 3 0 3 2 5 3 6l9 10c-2 0-7-6-8-7h-2l1 1-1 1c1 1 2 3 1 4l-1 1h0c5 14 14 27 19 41v2 2l2 1v1c2 7 5 12 9 18l6 7c1 2 1 4 2 6h0c-4-3-8-8-11-12-4-8-7-17-10-25l-24-52-9-15c1-1 0-3 0-4z" class="S"></path><path d="M366 411c1 1 1 2 2 3 0 3 2 5 3 6l9 10c-2 0-7-6-8-7h-2l1 1-1 1c1 1 2 3 1 4l-1 1h0l-2-6c-3-3-5-8-7-13l6 6h0 0c0-2-1-3-1-4h-1l1-2z" class="Y"></path><path d="M368 424c0-2-1-3-1-4-1-2-1-1-1-2 2 2 4 3 6 5h-2l1 1-1 1c1 1 2 3 1 4l-1 1h0l-2-6z" class="C"></path><path d="M282 389l2 5c1 1 2 2 2 3h1v-1h1 2c1 2 3 4 5 6l3 12-1 3h-3v1l2 2-1 1c-1-1-1-2-2-2l-1-1c0 2 0 5-1 7 0 1-1 2-1 3-2 4-2 6-3 10l-1-1v-3h-1v-2l-2-2h0l-1 2v3l-1 1-3-4c-2-2-5-6-7-9-1 0-1-1-2-2h0l-1-2v-3h-2c0-2-1-4-2-6h0l-2-2c1-2 1-3 1-4v-6s1-1 1-2c1-1 1-1 3-1 1-1 1-1 2-1h0l2-2c1 2 1 3 1 5 0 1 0 2 1 4l2-1-2-2c2 0 2-1 3-2l1 1c1 0 1 0 2-1 0 0 0-1 1-2h0l-1-1c0-1 0-2 1-3l1 1 1-2z" class="Z"></path><path d="M271 392c1 2 1 3 1 5 0 1 0 2 1 4l1 1-1 1 1 1h0c-1 1-1 2-1 3l1 1c-1 1-1 1 0 2v1c1 0 1 1 1 2v-1l-2-1h-1v-1c-1 0-2 0-3-1 1-5 1-10 0-15h0l2-2z" class="H"></path><path d="M269 421v-2-1c-1-3-1-7 0-9 1 1 2 1 3 1v1h1c0 2-1 5 1 7l2-2v1h0v2h0l-2 3h-1-2v1c-1 0-1-1-2-2z" class="R"></path><path d="M274 418l2-2v1h0v2h0l-2 3h-1-2v-1c-1-2-1-3-1-4l1-1c1 1 1 1 1 2h1 1z" class="U"></path><path d="M282 389l2 5c-1 2-1 4-1 6 0 1-1 1 0 2 0 2 0 3 1 4v1s1 1 1 2c1 0 1 1 2 2s2 3 2 4c-1 1-1 3-2 4s-1 2-3 2v-1c-1-1-1-1-1-2v-8c0-2-1-5-1-7v-1l-1-1v4l-1 1v-1-1-5c-1 0-2 1-2 1 0-1 0-2-1-3 1 0 1 0 2-1 0 0 0-1 1-2h0l-1-1c0-1 0-2 1-3l1 1 1-2z" class="z"></path><path d="M284 420l2-6c0-2 0-2 1-3 1 1 2 3 2 4-1 1-1 3-2 4s-1 2-3 2v-1z" class="j"></path><path d="M276 419l1-1h1l1 2h0l2-2h2l-2 5c1 3 1 3 1 6h0 1v1l-1 2v3l-1 1-3-4c-2-2-5-6-7-9v-1h2 1l2-3h0 0z" class="u"></path><path d="M278 432l2-1c0 1 1 1 1 1h1v3l-1 1-3-4z" class="W"></path><path d="M276 419h0c0 1 1 2 1 3 1 1 0 2 0 3v3-1c-1-1-2-3-2-4l1-1v-3h0z" class="m"></path><path d="M276 419l1-1h1l1 2h0l2-2h2l-2 5c0 3-1 4-2 6-1 0-1-1-2-1v-3c0-1 1-2 0-3 0-1-1-2-1-3z" class="I"></path><path d="M267 395c1-1 1-1 2-1 1 5 1 10 0 15-1 2-1 6 0 9v1 2h0l-1-2v-3h-2c0-2-1-4-2-6h0l-2-2c1-2 1-3 1-4v-6s1-1 1-2c1-1 1-1 3-1z" class="j"></path><path d="M263 398s1-1 1-2c1-1 1-1 3-1l1 1c0 1 0 2-1 3v5c0 1 0 0-1 1 0 1 0 2 1 3-1 0-2 1-2 1l-1 1h0l-2-2c1-2 1-3 1-4v-6z" class="I"></path><path d="M287 411c-1-1-1-2-2-2 0-1-1-2-1-2v-1c-1-1-1-2-1-4-1-1 0-1 0-2 2 5 4 8 7 13 1 1 1 2 2 3v2c0 2 0 5-1 7 0 1-1 2-1 3-2 4-2 6-3 10l-1-1v-3h-1v-2l-2-2h0v-1h-1 0c0-3 0-3-1-6l2-5h0c0 1 0 1 1 2v1c2 0 2-1 3-2s1-3 2-4c0-1-1-3-2-4z" class="s"></path><path d="M289 415c1 2 1 4 0 7v1-3c-1 0-1 0-1-1h-1c1-1 1-3 2-4z" class="V"></path><path d="M283 418h0c0 1 0 1 1 2v1c2 0 2-1 3-2h1c0 1 0 1 1 1v3c-1 4-2 7-3 11h-1v-2l-2-2h0v-1h-1 0c0-3 0-3-1-6l2-5z" class="I"></path><path d="M283 418h0c0 1 0 1 1 2v1l-1 8h-1 0c0-3 0-3-1-6l2-5z" class="W"></path><path d="M284 394c1 1 2 2 2 3h1v-1h1 2c1 2 3 4 5 6l3 12-1 3h-3v1l2 2-1 1c-1-1-1-2-2-2l-1-1v-2c-1-1-1-2-2-3-3-5-5-8-7-13 0-2 0-4 1-6z" class="I"></path><path d="M286 397h1c1 1 1 2 1 3l2 3v2c1 1 1 2 2 4 0 2 1 3 1 6l-1 1c-1-1-1-2-2-3 1-1 0-2 0-3v-2-1l-1-1c0-1-1-2-1-3-1-2-1-4-2-6z" class="m"></path><path d="M284 394c1 1 2 2 2 3 1 2 1 4 2 6 0 1 1 2 1 3l1 1v1 2c0 1 1 2 0 3-3-5-5-8-7-13 0-2 0-4 1-6z" class="w"></path><defs><linearGradient id="AV" x1="385.112" y1="411.596" x2="376.929" y2="416.028" xlink:href="#B"><stop offset="0" stop-color="#5b5958"></stop><stop offset="1" stop-color="#737471"></stop></linearGradient></defs><path fill="url(#AV)" d="M350 363v-3h0 0 1c1 0 1 1 2 1h1l16 20 5 5h1v5 1 1c1 1 2 7 3 8l9 22c3 5 6 12 8 18h-1l1 1v1l1 1c-1 1-1 2-1 3v-1c-1-1-2-2-2-4l-6-12-1 1h-1c-1-2-3-3-4-5 0-1 0-1-1-2h0c-1-2-1-2-2-3v-1h-1c-1-1-1-2-2-2-2-1-2-3-3-4l-3-3-1 1c-1 0-5-8-7-10-1 0-1-1-2-2s-2-2-3-4c-1-1-1-2-3-3v-1l1-1-1-1c0-1-1-1-1-2h-1c1-2 0-4 0-6h0c1-3-1-6-1-8 1-1 2 0 3 1-1-1-1-2-1-3-1-2-2-5-3-7h0v-2z"></path><path d="M368 388c1 0 2 1 3 2l1 1v1 2l-2-2c-1-1-1-3-2-4z" class="i"></path><path d="M377 406c2 1 4 5 5 7v1c-3-1-5-4-6-6l1-2z" class="X"></path><path d="M364 380l2 1c2 2 3 4 5 5h0c0 2 0 3 1 4h1l-1 2v-1l-1-1c-1-1-2-2-3-2v-1h0c-2-2-2-3-4-4h-1 0c0-1 0-2 1-3z" class="d"></path><path d="M364 379c2 0 2 1 3 2l4 3h0l-1-3 5 5h1v5 1l-1-1-2-2c0-1-1-2-2-3-2-1-3-3-5-5l-2-2z" class="B"></path><path d="M358 373l3 3 3 3 2 2-2-1c-1 1-1 2-1 3h0l3 11-3-4-1-2h-1c-1 0-1-1-1-1-1-2-1-3-3-5l-1 1v-1l1-1v-2c-1-1-1-2-1-3 1-1 1-2 2-3z" class="K"></path><path d="M358 373l3 3 3 3 2 2-2-1-1-1h-1v2h-1l-1-3-1 1c0 1 0 2-1 2-1 1 0 1-1 1l-1 1v-1l1-1v-2c-1-1-1-2-1-3 1-1 1-2 2-3z" class="O"></path><path d="M350 363v-3h0 0 1c1 0 1 1 2 1h1l16 20 1 3h0l-4-3c-1-1-1-2-3-2l-3-3-3-3c-1 1-1 2-2 3 0 1 0 2 1 3v2l-1 1-2-7c-1-1-1-2-1-3-1-2-2-5-3-7h0v-2z" class="Y"></path><path d="M350 363c1-1 1-1 1-2l1 1h0c0 4 4 8 6 11-1 1-1 2-2 3 0 1 0 2 1 3v2l-1 1-2-7c-1-1-1-2-1-3-1-2-2-5-3-7h0v-2z" class="C"></path><defs><linearGradient id="AW" x1="368.789" y1="393.295" x2="360.738" y2="403.172" xlink:href="#B"><stop offset="0" stop-color="#413e3f"></stop><stop offset="1" stop-color="#686965"></stop></linearGradient></defs><path fill="url(#AW)" d="M352 382c1-3-1-6-1-8 1-1 2 0 3 1l2 7v1l1-1c2 2 2 3 3 5 0 0 0 1 1 1l2 3c1 2 2 4 4 6 3 6 7 12 9 18l2 2c0 1 1 2 1 3h-1c-1-1-1-2-2-2-2-1-2-3-3-4l-3-3-1 1c-1 0-5-8-7-10-1 0-1-1-2-2s-2-2-3-4c-1-1-1-2-3-3v-1l1-1-1-1c0-1-1-1-1-2h-1c1-2 0-4 0-6h0z"></path><path d="M359 392c2 2 3 3 5 6h-1-1l-2-3c-1-1-1-2-1-3z" class="f"></path><path d="M352 382c1-3-1-6-1-8 1-1 2 0 3 1l2 7v1h0c1 2 1 3 2 5 0 1 0 2 1 3v1c0 1 0 2 1 3 0 2 0 3 1 4 0 1 1 2 1 3-1 0-1-1-2-2s-2-2-3-4c-1-1-1-2-3-3v-1l1-1-1-1c0-1-1-1-1-2h-1c1-2 0-4 0-6h0z" class="K"></path><path d="M352 382c1-3-1-6-1-8 1-1 2 0 3 1l2 7v1h0c-1-1-1-3-3-3v2 2l2 5 1 1-1 1-1-1c0-1-1-1-1-2h-1c1-2 0-4 0-6h0z" class="M"></path><path d="M238 344v1h0v1h1c1 2 0 1 0 3 0 0 1 0 1 1l-1 1h0l1 1h2 1v-1l2-1h1c-1-1 0-1-1-1v-1h1v-1l1-1c1-2 1-2 2-3h-2c1-1 1-2 2-2h1v-1h-1v-1l1-1h2c1 0 0-1 2-1l-1 2c1 0 2-1 3-2l1 1-1 1v1c-1 1-1 1-1 2l-1 2h1v1 1h0c1 1 0 1 1 2l-1 1c1 0 1-1 2-1v-1l-1-1 1-1 3 3c0 2 0 2-1 4l-1-1v-1h-1v2c-1 1 0 2-1 3 0 1 0 1 1 2 1-1 2-2 2-3h2v1 1l-2 1v1h0l2 2v3h-2c0 2 0 2-1 4 1 1 1 1 1 2h-2v2l1 1c1 1 2 2 2 3v1l-1-1c-1 0-1-1-2-1v-1c-1-1-1-2-1-3l-1 1h-2c1-1 0-1 1-1v-1l-1-1c-2-2-3-4-5-4-1-1 0-1 0-2-1 0-3-2-3-3l-1 1v2h-1l1 1c-1 1-1 2-2 2h-1l-1 2h1v2l1 1h-1 0l-2-4h-3-2c-1-1-1-1-1-2l-1 1c-1 1-2 1-2 2l-1 1c-1 0-1 0-2 1l-1-1h-2c-1 1-1 1-1 3l-1-1-1 1c-2 1-4 1-6 1-1 1-1 1-3 1v-1-3c0-2 4-6 6-7 0-1 1-2 2-3v-1l3-3-2-1-1 1-1-1c1-1 1-1 1-3-2-1-2-1-4-1-1 2-2 2-3 3h-1v-4-1-4-1c0-1 1-1 2-2h0l1 1 1-1c1 1 1 1 2 1 1 1 2 1 3 2 1-1 1 0 1-1 1 1 1 1 3 2 0-1 1-1 2-1s1 1 2 0 3-1 5-2v1c2 0 3 1 4 1z" class="o"></path><path d="M212 344c2 1 4 2 7 2 0 0 1 0 1 1h6 2v1c-1 2-3 6-5 7l-2-1-1 1-1-1c1-1 1-1 1-3-2-1-2-1-4-1-1 2-2 2-3 3h-1v-4-1-4z" class="G"></path><path d="M220 359c2-1 3-2 4-4 1 0 0 0 1-1 2-1 4-4 7-5 0 1 0 0 1 1v-1h1c0 2 2 2 3 3 0 1 1 2 1 3 2 1 2 1 2 2 1 0 1 0 2-1v1c-1 1-1 3-1 4v3l-2 2h-3-2c-1-1-1-1-1-2l-1 1c-1 1-2 1-2 2l-1 1c-1 0-1 0-2 1l-1-1h-2c-1 1-1 1-1 3l-1-1-1 1c-2 1-4 1-6 1-1 1-1 1-3 1v-1-3c0-2 4-6 6-7 0-1 1-2 2-3z" class="t"></path><path d="M234 366l2-2-1-1 1-1-1-2c0-1 0-1 1-2h0c-1-1-2-2-2-3l-1-1v-3h1c0 1 0 1 1 2l2 3 1-1c2 1 2 1 2 2 1 0 1 0 2-1v1c-1 1-1 3-1 4v3l-2 2h-3-2z" class="T"></path><path d="M218 362c0 1 1 1 2 2v1h1c1 1 1 1 2 1l1-1v-1h-1c-1-1 0-1 0-2h2 1l1 1-2 2v1c1 0 1 1 1 2h-2c-1 1-1 1-1 3l-1-1-1 1c-2 1-4 1-6 1-1 1-1 1-3 1v-1-3c0-2 4-6 6-7z" class="N"></path><defs><linearGradient id="AX" x1="437.133" y1="567.745" x2="457.514" y2="551.617" xlink:href="#B"><stop offset="0" stop-color="#494140"></stop><stop offset="1" stop-color="#695a58"></stop></linearGradient></defs><path fill="url(#AX)" d="M415 530c-4-6-6-12-8-19 4 2 7 5 10 8 1 2 1 3 3 5 1 1 2 2 2 3 3 1 5 3 7 5 1 2 3 6 5 7 1 1 2 2 3 4h0c2 1 3 2 5 4h1c5 4 9 10 14 13 2 1 4 3 6 4 3 1 6 0 8 2l-3 2h1 0c1 1 2 1 4 2l-11 5-10 3-1-1c-3 1-4 1-5 3v1 2c-2-3-4-6-5-9-3-3-5-8-7-11l-5-11h0l-5-10c-1-2-2-3-2-4l2 1v-1l-1-1 1-1c0-1-4-5-5-6h-4z"></path><path d="M441 574l1-1s2 1 3 1c0 1 0 0 1 1s1 2 2 2c7-2 14-6 20-9h1 0c1 1 2 1 4 2l-11 5-10 3-1-1c-3 1-4 1-5 3v1 2c-2-3-4-6-5-9z" class="M"></path><path d="M469 568h0c1 1 2 1 4 2l-11 5-10 3-1-1c2 0 3-1 5-2 4-2 9-4 13-7z" class="f"></path><path d="M415 530c-4-6-6-12-8-19 4 2 7 5 10 8 1 2 1 3 3 5 1 1 2 2 2 3 3 1 5 3 7 5 1 2 3 6 5 7 1 1 2 2 3 4h0l1 4h-2c2 1 3 2 5 4 1 2 2 4 4 6 3 3 7 6 10 11 0 1 1 2 2 4-2-2-4-3-6-5-1-1-2-3-3-5-3-3-7-7-11-10l-2 1c2 1 4 2 4 4h0c-2-1-2-2-4-3l-1 1 1 2-6-6v1h0l-5-10c-1-2-2-3-2-4l2 1v-1l-1-1 1-1c0-1-4-5-5-6h-4z" class="M"></path><path d="M415 530c-4-6-6-12-8-19 4 2 7 5 10 8 1 2 1 3 3 5 1 1 2 2 2 3l6 10c3 5 5 9 9 14-2 0-4-2-4-3-3-1-4-2-5-4-2-1-3-3-4-5v-1l-1-1 1-1c0-1-4-5-5-6h-4z" class="S"></path><path d="M424 539v-1l-1-1 1-1c1 2 3 3 4 5 1 3 3 5 5 7-3-1-4-2-5-4-2-1-3-3-4-5z" class="J"></path><path d="M473 570c1-1 3-2 4-3l2 1c-1 7 3 10 5 16v2c1 1 3 2 3 4l-1 1 3 3c1 1 1 2 3 3v1l1 1h-1l2 1 1 1-2-1c-3 0-5 1-8 2h-2l-5 4c-2 2-2 3-2 5l-2 1 1 1c-1 1-1 1-2 1l-2-1h-2l1 3-1 1c-7-8-12-17-17-25l-6-9v-2-1c1-2 2-2 5-3l1 1 10-3 11-5z" class="K"></path><path d="M472 599l2-2c1 1 1 2 2 2v2l-1 2-1-1-1-1h-2c-1-1-1-1-1-2v-1c1 1 1 0 2 1h0z" class="AG"></path><path d="M477 588c1 4 1 8 3 12l-3 1h0l2-2c0-2-3-1-3-3v-1c0-3-2-3-3-5l1-1 1 1 2-2z" class="d"></path><path d="M480 600c1-1 3-3 5-3s3 1 5 1v1h-1v1h4c-3 0-5 1-8 2h-2-2 0c-4 1-6 2-9 5l-1-1c-2 1-2 1-4 1h-1c2-2 5-3 7-3 1 0 1-1 2-1l2-2 3-1z" class="AA"></path><path d="M473 570c1-1 3-2 4-3l2 1c-1 7 3 10 5 16v2c1 1 3 2 3 4l-1 1 3 3c1 1 1 2 3 3v1l1 1h-1l2 1 1 1-2-1h-4v-1h1v-1c-2 0-3-1-5-1s-4 2-5 3c-2-4-2-8-3-12l-2 2-1-1c0-1 1-2 1-3h-1c-2 0-2-2-3-2h-2 0c-1-1-1-2-2-3-2 0-3 1-6 2h-2l-2-1c-2-1-3-1-4-3-1 0-1 0-2 1h-1-1l1-1 2-1 10-3 11-5z" class="AI"></path><path d="M469 575l3 1c2 1 3 2 4 4 0 2 0 4 1 5v3l-2 2-1-1c0-1 1-2 1-3h-1l1-2c-1 0-1 0-1-1 0-2-1-2-2-3 0-1 0-2-1-2v-1l-2-1v-1z" class="AV"></path><path d="M467 578h0v-2h0c1 1 2 2 4 2v-1 1c1 0 1 1 1 2 1 1 2 1 2 3 0 1 0 1 1 1l-1 2c-2 0-2-2-3-2h-2 0c-1-1-1-2-2-3l1-1-1-2z" class="AU"></path><path d="M471 584c0-1 0-1-1-2v-2c2 1 2 2 4 3 0 1 0 1 1 1l-1 2c-2 0-2-2-3-2z" class="AY"></path><path d="M462 575c2 0 2 0 4-1h1s1-1 2-1v1h0v1 1l2 1v1c-2 0-3-1-4-2h0v2h0c-1 0-2 0-3 1-1 0-1-1-2-1v-2c-1 0-2 1-3 1-1 1-4 2-6 2-1 0-1 0-2 1h-1-1l1-1 2-1 10-3z" class="C"></path><path d="M453 579c2 0 5-1 6-2 1 0 2-1 3-1v2c1 0 1 1 2 1 1-1 2-1 3-1l1 2-1 1c-2 0-3 1-6 2h-2l-2-1c-2-1-3-1-4-3z" class="O"></path><path d="M477 567l2 1c-1 7 3 10 5 16v2c1 1 3 2 3 4l-1 1 3 3c1 1 1 2 3 3v1l1 1h-1l2 1 1 1-2-1h-4v-1h1v-1c-2 0-3-1-5-1l1-2s1 0 0-1c0-1-1-2-2-3l-4-8c-3-4-4-8-6-13l3-3z" class="L"></path><defs><linearGradient id="AY" x1="396.705" y1="464.935" x2="413.155" y2="462.585" xlink:href="#B"><stop offset="0" stop-color="#5b5b59"></stop><stop offset="1" stop-color="#757372"></stop></linearGradient></defs><path fill="url(#AY)" d="M369 412l1-1 3 3c1 1 1 3 3 4 1 0 1 1 2 2h1v1c1 1 1 1 2 3h0c1 1 1 1 1 2 1 2 3 3 4 5h1l1-1 6 12c0 2 1 3 2 4v1c0-1 0-2 1-3l-1-1v-1l-1-1h1l4 7 6 11v-2c1 1 2 1 2 3 1 1 2 1 3 1v1c0 3 2 4 4 6 0 1 1 2 1 2l-2 1v1c1 1 1 2 1 3l2 5c-2 1-3 0-5-1-1-1-4-1-5-1l-16-2-2-1v-2-2c-5-14-14-27-19-41h0l1-1c1-1 0-3-1-4l1-1-1-1h2c1 1 6 7 8 7l-9-10c0-3-1-5-2-8z"></path><path d="M406 474l2 2c-3 0-6 0-9-1h0c1-1 3-1 4-1h3z" class="AG"></path><path d="M411 468h4c0 1 1 2 1 2l-2 1v1c1 1 1 2 1 3l-4-7z" class="u"></path><path d="M406 459v-2c1 1 2 1 2 3 1 1 2 1 3 1v1c0 3 2 4 4 6h-4l-5-8v-1z" class="Z"></path><path d="M386 431h1l1-1 6 12c0 2 1 3 2 4v1c0-1 0-2 1-3l-1-1v-1l-1-1h1l4 7 6 11v1c-1 0-2 0-3-1 0 0 0 1-1 1 0-2-1-3-2-5-3-5-6-9-10-13-1-2-2-4-3-5l-1-2h1l1 1h1 0c0-2-2-4-3-5z" class="X"></path><path d="M396 441l4 7 6 11v1c-1 0-2 0-3-1l-3-6c-1-2-2-4-4-6 0-1 0-2 1-3l-1-1v-1l-1-1h1z" class="i"></path><defs><linearGradient id="AZ" x1="386.062" y1="449.513" x2="394.002" y2="446.806" xlink:href="#B"><stop offset="0" stop-color="#575553"></stop><stop offset="1" stop-color="#747473"></stop></linearGradient></defs><path fill="url(#AZ)" d="M384 435v1c3 3 4 7 7 9h1c1 2 2 4 2 6 3 6 6 11 9 16-3-3-5-6-7-9-1-1-3-3-3-4-1 0-1 0-2 1l-5-7-3-6v-1l-1-2 1-2-2-2h1 1 1z"></path><path d="M369 412l1-1 3 3c1 1 1 3 3 4 1 0 1 1 2 2h1v1c1 1 1 1 2 3h0c1 1 1 1 1 2 1 2 3 3 4 5 1 1 3 3 3 5h0-1l-1-1h-1l1 2c1 3 3 5 5 8h-1c-3-2-4-6-7-9v-1c-1-2-2-3-4-5l-9-10c0-3-1-5-2-8z" class="X"></path><path d="M379 421c1 1 1 1 2 3h0c1 1 1 1 1 2 1 2 3 3 4 5 1 1 3 3 3 5h0-1l-1-1h-1l1 2c-2-1-2-2-3-3 0-1 0-1-1-2h0v-1l1 1v-1l-5-8v-1-1z" class="q"></path><path d="M370 430h0l1-1c1-1 0-3-1-4l1-1-1-1h2c1 1 6 7 8 7 2 2 3 3 4 5h-1-1-1l2 2-1 2 1 2v1l3 6 5 7 13 17 2 2h-3c-1 0-3 0-4 1h0c-3 0-6 0-9-1l-1 1v-2-2c-5-14-14-27-19-41z" class="K"></path><path d="M404 472l-1 1-6-6v1c1 1 2 2 2 4h-1c-1 0-1 1-2 0-2-2-3-4-4-6-2-3-5-8-6-12-1-1-1-1 0-2l2 2c1-3-2-4-2-6l5 7 13 17z" class="O"></path><path d="M493 600l2 1c5 2 12 5 16 9l2 1v1 1l-3 4h2l1 1-3 3c-1 1-1 1-1 2-1 2-3 2-3 4h2l-1 1 1 1 1 1h1v1c-2 1-2 1-3 3-2 0-3 1-4 3h0v1l-9 5c-2 2-3 3-3 6h-1v1l-1 3-1 1 1 2-1 2 1 3h-2l-6-15 1-4v-3c1-2 3-4 5-6l-1-1h-1l-2 2h0c-3 1-2-1-4-2v-2l4 2h1l-1-2h1 0l-1-1c-2 0-3-1-5-3l-5-4-3-4-1-1 1-1-1-3h2l2 1c1 0 1 0 2-1l-1-1 2-1c0-2 0-3 2-5l5-4h2c3-1 5-2 8-2z" class="AG"></path><path d="M470 618h0l1 1h2c1 0 2 0 3 1h0c-1 1-1 1-1 2h-2l-3-4z" class="f"></path><path d="M475 622h1s1 0 2-1c0 1 0 2 1 2 0 1-1 2 0 3h-1l-5-4h2z" class="i"></path><path d="M469 617l1-1-1-3h2l2 1-2 1v1h0 2v1 2h-2l-1-1h0l-1-1zm25 5l2 1 1-2h0l1 1v1h-1l-3 3h-2c-1-1-1-1-2-1 0-2 0-2-1-3 1 0 2 1 3 0h0c1 1 1 0 2 0z" class="AA"></path><path d="M489 622l-2-1v-1l2-1c2-1 2-1 4-1l1 2h-1v1c1 0 1 0 1 1h0c-1 0-1 1-2 0h0c-1 1-2 0-3 0z" class="AX"></path><path d="M501 609l7 2h0c0 2 0 4 1 5-1 1-1 2-1 3l-1-1v-1h-2c-2 1-3 2-5 4l-1 1c0-2 6-6 7-8v-1h-2l-1-1h-1l-1-1v-2z" class="AV"></path><path d="M479 623h9l1 1h-2l-1 1h1v2 1h-2c-1 0-2 1-2 1-2 0-3-1-5-3h1c-1-1 0-2 0-3z" class="f"></path><path d="M479 626c2 1 5 0 8 1v1h-2c-1 0-2 1-2 1-2 0-3-1-5-3h1z" class="e"></path><path d="M485 608c3-1 6-1 10-1 2 1 4 1 6 2v2h-1-7-2-1c-1-1-1-1-2-1l-3-1h0 1-5l4-1z" class="K"></path><path d="M492 626h2l3-3-2 2-8 8-1-1h-1l-2 2h0c-3 1-2-1-4-2v-2l4 2h1l-1-2h1 0l-1-1s1-1 2-1h2v-1-2h3c1 0 1 0 2 1z" class="O"></path><path d="M490 625c1 0 1 0 2 1-1 2-3 4-5 4h-3 0l-1-1s1-1 2-1h2v-1-2h3z" class="X"></path><path d="M493 600l2 1c5 2 12 5 16 9l1 2-3 4c-1-1-1-3-1-5h0l-7-2c-2-1-4-1-6-2-4 0-7 0-10 1l-4 1-5 2c0-2 0-3 2-5l5-4h2c3-1 5-2 8-2z" class="AI"></path><path d="M490 603h1c2 0 5 0 6 1l-1 1c-2 1-5 0-7-1l1-1z" class="S"></path><path d="M478 606v1h3c1-1 2 0 3 0h0l1 1-4 1-5 2c0-2 0-3 2-5z" class="C"></path><path d="M511 610l2 1v1 1l-3 4h2l1 1-3 3c-1 1-1 1-1 2-1 2-3 2-3 4h2l-1 1 1 1 1 1h1v1c-2 1-2 1-3 3-2 0-3 1-4 3h0v1l-9 5c-2 2-3 3-3 6h-1v1l-1 3-1 1 1 2-1 2 1 3h-2l-6-15 1-4v-3c1-2 3-4 5-6l8-8 2-2h1l1-1 1-1c2-2 3-3 5-4h2v1l1 1c0-1 0-2 1-3l3-4-1-2z" class="AA"></path><path d="M500 621h2l1 1-3 3-1 1-2 1-2-2 2-2h1l1-1 1-1z" class="q"></path><path d="M501 627c1 0 1 0 2 1 0 1 0 2-1 3s-1 1-3 1v1l-2 1v-1c0-1 0-2 1-3l3-3z" class="f"></path><path d="M503 637v1l-9 5-1-1-2 1v-3l1-2h3v1l-1 1h1c1 0 3-1 4-2h0l4-1z" class="AU"></path><path d="M499 638c1-4 4-5 6-8 0-1 1-2 2-2l1 1 1 1h1v1c-2 1-2 1-3 3-2 0-3 1-4 3h0l-4 1z" class="f"></path><defs><linearGradient id="Aa" x1="495.764" y1="653.272" x2="486.089" y2="629.997" xlink:href="#B"><stop offset="0" stop-color="#6d6f6e"></stop><stop offset="1" stop-color="#8c8884"></stop></linearGradient></defs><path fill="url(#Aa)" d="M499 626c0 2-2 3-4 5-1 2-2 5-3 7l-1 2v3l2-1 1 1c-2 2-3 3-3 6h-1v1l-1 3-1 1 1 2-1 2 1 3h-2l-6-15 1-4v-3c1-2 3-4 5-6l8-8 2 2 2-1z"></path><path d="M499 626c0 2-2 3-4 5-1 2-2 5-3 7l-1 2v3l2-1 1 1c-2 2-3 3-3 6h-1v1l-1 3-1 1-3-9c3-8 6-13 12-18l2-1z" class="B"></path><path d="M490 650h0v-5-2c0-1 0-2 1-3v3l2-1 1 1c-2 2-3 3-3 6h-1v1z" class="C"></path><defs><linearGradient id="Ab" x1="522.808" y1="651.069" x2="510.588" y2="648.408" xlink:href="#B"><stop offset="0" stop-color="#605a5a"></stop><stop offset="1" stop-color="#6e716c"></stop></linearGradient></defs><path fill="url(#Ab)" d="M535 620c0 2 0 3-1 5l-1 2-1 8v3l-1 9-1 8-1 2-4 9c-1 1-3 1-4 2l-2 1-1 1-2-1h0c-3 1-6 1-9 1h0c-1 1-1 1-2 1h-2c-2 0-3 0-4-1s-1-1-2-1l-1-1-1-2c-1-2-3-5-4-7l-2-3-1-2 1-1 1-3v-1h1c0-3 1-4 3-6l9-5v-1h0c1-2 2-3 4-3 2-1 4-3 6-3l6-3c4-2 8-4 11-7 1 0 1 0 2-1h3z"></path><path d="M527 637c2 0 3-1 5-2v3l-4 4-2 2-1-1c-1 1-2 2-3 4 0 0-1 0-1 1l-1-1c1-2 3-3 5-5l1-1-2-1 3-3z" class="AG"></path><path d="M525 633c2-1 3-4 5-5 1 0 2 0 2-1h1l-1 8c-2 1-3 2-5 2l-3 3c-1 1-1 1-2 1l-6 6v-3l4-5h0l2-1c1-2 2-3 3-5z" class="d"></path><path d="M527 634c1-2 2-3 4-4v1c-1 2-3 4-4 6l-3 3c-1 1-1 1-2 1 0-3 3-5 5-7z" class="AA"></path><path d="M520 639h0l2-1c1-2 2-3 3-5l2 1c-2 2-5 4-5 7l-6 6v-3l4-5z" class="AG"></path><path d="M520 647l1 1c0-1 1-1 1-1 1-2 2-3 3-4l1 1-2 3s-1 1-1 2h1l4-5v1h0v2h1v-2l1-1c0 2 0 3-1 4l-1 2c0 1-1 2-2 2 0 1-1 1-1 2v-3c1 0 1-1 2-1l1-1-1-1c-1 2-3 1-4 2s-3 2-3 3h-1c0-1-1-1-1-1-1 2-2 3-3 4l-1 1v1c-2 3-8 7-9 11l1 1h1c-1 1-1 1-2 1h-2c-2 0-3 0-4-1s-1-1-2-1l-1-1-1-2c2-1 3-1 4-2l-2-2h1c2-2 4-5 7-6 4-2 6-7 10-9 0 2-1 4-3 6-1 1-2 2-3 4-1 1-2 3-4 4-1 1-2 2-2 3h0l5-4v1l-3 3c-1 2-3 3-4 5l3 1v-1h-1c1-2 2-3 4-5l7-10c2-2 4-4 5-6l1-1z" class="d"></path><path d="M526 644l2-2 4-4-1 9-1 8-1 2-4 9c-1 1-3 1-4 2l-2 1-1 1-2-1h0c-3 1-6 1-9 1h0-1l-1-1c1-4 7-8 9-11v-1l1-1c1-1 2-2 3-4 0 0 1 0 1 1h1c0-1 2-2 3-3s3 0 4-2l1 1-1 1c-1 0-1 1-2 1v3c0-1 1-1 1-2 1 0 2-1 2-2l1-2c1-1 1-2 1-4l-1 1v2h-1v-2h0v-1l-4 5h-1c0-1 1-2 1-2l2-3z" class="q"></path><path d="M526 644l2-2 4-4-1 9-1 8-1 2-4 9c-1 1-3 1-4 2l-2 1-1 1-2-1h0c-3 1-6 1-9 1h0-1l-1-1h1c2 0 6 1 7-1 2 1 3 0 5-1s6-3 6-5c1-2 1-2 2-3h0v-1l-2 1c1-2 2-3 3-5-1 0-2 1-2 1l-2 2v-1l2-2c0-1 1-1 1-2 1 0 2-1 2-2l1-2c1-1 1-2 1-4l-1 1v2h-1v-2h0v-1l-4 5h-1c0-1 1-2 1-2l2-3z" class="J"></path><path d="M535 620c0 2 0 3-1 5l-1 2h-1c0 1-1 1-2 1-2 1-3 4-5 5-1 2-2 3-3 5l-2 1h0c-1 1-4 5-4 5v3h-1c-4 2-6 7-10 9-3 1-5 4-7 6h-1l2 2c-1 1-2 1-4 2-1-2-3-5-4-7l-2-3-1-2 1-1 1-3v-1h1c0-3 1-4 3-6l9-5v-1h0c1-2 2-3 4-3 2-1 4-3 6-3l6-3c4-2 8-4 11-7 1 0 1 0 2-1h3z" class="AA"></path><path d="M522 631h1l-3 3-3 3-3 2h-1c1-1 4-3 4-4l1-1c0-1 3-3 4-3z" class="d"></path><path d="M503 638c2-1 3-2 5-4h1l1-1 2 1h0-1c-1 1 1 0-1 1h-1l-1 1h1c-1 2-2 3-4 4-1 0-1 1-2 1l-2 2c-4 2-9 5-10 9l-1 2h0l-1-1 1-3v-1h1c0-3 1-4 3-6l9-5z" class="i"></path><path d="M503 641c3 0 4-1 7-2 0-1 1-1 1-1v1l-1 1v1c1-1 1-1 2-1-1 3-5 5-7 6h-1c-4 4-9 8-12 13h-1l-2-3-1-2 1-1 1 1h0l1-2c1-4 6-7 10-9l2-2z" class="Y"></path><path d="M503 641c3 0 4-1 7-2 0-1 1-1 1-1v1l-1 1v1c1-1 1-1 2-1-1 3-5 5-7 6h-1c-3 0-6 5-9 6 0-1 1-2 2-2 2-2 6-5 7-7h-3l2-2z" class="M"></path><path d="M495 659l1-1c1-3 5-5 7-7s4-3 5-4l9-6 2-2h1c-1 1-4 5-4 5v3h-1c-4 2-6 7-10 9-3 1-5 4-7 6h-1l2 2c-1 1-2 1-4 2-1-2-3-5-4-7h1l1 1c1 0 1-1 2-1h0z" class="J"></path><path d="M495 659l1-1c1-3 5-5 7-7s4-3 5-4l9-6 2-2h1c-1 1-4 5-4 5-5 4-10 7-14 11-3 2-5 5-7 7h-1c2-3 5-5 7-8-2 1-4 3-6 5z" class="i"></path><path d="M317 286h0c0-3-1-5-2-8l7 8c1 1 3 3 5 4-1 0-2 0-2 1v1h-1c3 4 5 6 9 8 1 1 2 1 3 2l5 4c1 2 2 3 3 4l-1 1v1l-1 1 1 3 3 6c1 2 2 4 3 5l2 1v2s1 0 1 1c1 0 2-1 2 0h-2c0 1 0 2 1 3l1 1v2h0l8 22c2 4 3 7 6 10l-1 2c1 0 2 1 2 2v2c2 2 2 4 3 6 1 1 3 2 3 4h1v1h-1l-5-5-16-20-6-6-13-18h-1c-1 0-2-2-2-3-3-3-5-6-8-8-1-4-4-8-6-11v-1l1 2 1-1c0-1 0-3-1-4s-1-3-2-4v-1h0c-1-2-1-4-1-6l-1-2v-1l-2-5 3 3v-1h1c0-3-2-3-3-5 0-1 1-1 1-2h1c-1-1-1-1-1-2l1-1h0l1 2z" class="X"></path><defs><linearGradient id="Ac" x1="343.593" y1="313.303" x2="333.149" y2="307.134" xlink:href="#B"><stop offset="0" stop-color="#625f5e"></stop><stop offset="1" stop-color="#7e7e7c"></stop></linearGradient></defs><path fill="url(#Ac)" d="M333 310c-1 0-2-4-3-5l-1-1 1-1h0v-2l1-1 1 1c1 1 2 1 3 2l1-1 5 4c1 2 2 3 3 4l-1 1v1l-1 1 1 3-2 2-2-4-1-1c-2 0-2-2-3-3h-2z"></path><path d="M335 310c-2-2-2-3-2-6v-1h1c1 1 1 2 1 3s0 2 1 3l-1 1z" class="X"></path><path d="M335 303l1-1 5 4c1 2 2 3 3 4l-1 1v1l-1 1c-3-3-3-7-7-10z" class="O"></path><path d="M323 296l1-1c0 2 1 3 1 4 1 3 4 11 7 12h1c0 2 0 2-1 3l-1-1h0c1 3 5 11 8 12 1 0 2 1 3 2h0c0-1-3-5-3-6l-1-2-1-1c1-1 0-1 1-1-1-2-1-3-2-4l-3-3h2c1 1 1 3 3 3l1 1 2 4 2-2 3 6c1 2 2 4 3 5-1 2-1 2-1 4 0 1 0 2 1 3 0 1 0 2-1 4-4-4-6-10-11-13 1 2 1 5 3 7l-1 1v1c1 2 2 3 3 6h-1l-2-4c-1-1-2-3-3-5v-2s0 1 1 1c0-3-1-4-2-6s-3-5-4-8-6-8-6-11c0-2 0-4-1-6-1-1-1-2-1-3z" class="q"></path><path d="M333 310h2c1 1 1 3 3 3l1 1 2 4 2 5-5-6h0c-1-2-1-3-2-4l-3-3z" class="L"></path><path d="M316 295v-1h1c0-3-2-3-3-5 0-1 1-1 1-2h1c-1-1-1-1-1-2l1-1h0l1 2c0 1 0 2 1 3 2 2 3 3 4 5l1 2c0 1 0 2 1 3 1 2 1 4 1 6 0 3 5 8 6 11s3 6 4 8 2 3 2 6c-1 0-1-1-1-1v2h-2c-3-4-4-8-7-12-2-1-4-4-5-6v-1c-1-2-3-4-5-6h0c-1-2-1-4-1-6l-1-2v-1l-2-5 3 3z" class="e"></path><path d="M327 318c3 4 6 7 9 11v2h-2c-3-4-4-8-7-12v-1z" class="f"></path><path d="M315 298c2 0 11 16 12 19v1 1c-2-1-4-4-5-6v-1c-1-2-3-4-5-6h0c-1-2-1-4-1-6l-1-2z" class="i"></path><path d="M316 300l6 9c0 2 2 3 2 4h-1c-1-1 0-1-1-1-1-2-3-4-5-6h0c-1-2-1-4-1-6z" class="L"></path><path d="M316 295v-1h1c0-3-2-3-3-5 0-1 1-1 1-2h1c-1-1-1-1-1-2l1-1h0l1 2c0 1 0 2 1 3 0 3 3 4 3 7 0 4 3 8 5 12-3-2-4-3-5-6-2-3-3-5-5-7h0z" class="AB"></path><path d="M317 306c2 2 4 4 5 6v1c1 2 3 5 5 6 3 4 4 8 7 12h2c1 2 2 4 3 5l2 4h1c-1-3-2-4-3-6v-1l1-1c-2-2-2-5-3-7 5 3 7 9 11 13 1-2 1-3 1-4-1-1-1-2-1-3 0-2 0-2 1-4l2 1v2s1 0 1 1c1 0 2-1 2 0h-2c0 1 0 2 1 3l1 1v2h0l8 22c2 4 3 7 6 10l-1 2c1 0 2 1 2 2v2c2 2 2 4 3 6 1 1 3 2 3 4h1v1h-1l-5-5-16-20-6-6-13-18h-1c-1 0-2-2-2-3-3-3-5-6-8-8-1-4-4-8-6-11v-1l1 2 1-1c0-1 0-3-1-4s-1-3-2-4v-1z" class="X"></path><path d="M318 315v-1l1 2 7 7c3 5 6 9 9 14h-1c-1 0-2-2-2-3-3-3-5-6-8-8-1-4-4-8-6-11z" class="Y"></path><path d="M334 331h2c1 2 2 4 3 5l2 4c1 3 4 8 6 10l5 6v1c-6-5-10-12-14-18-1-2-2-3-3-5v-3h-1z" class="AG"></path><path d="M355 355c1 1 2 2 4 2 0 2 1 3 1 4 1 1 2 3 3 4v1h1v1l2 2 1 1v1c1 0 2 1 2 2v2c2 2 2 4 3 6l-1-1c-5-4-8-10-11-14-2-2-3-4-3-7l-2-2v-2z" class="J"></path><path d="M355 355c1 1 2 2 4 2 0 2 1 3 1 4 1 1 2 3 3 4h-1c-1 0-4-4-5-6l-2-2v-2z" class="Y"></path><path d="M354 337l8 22c2 4 3 7 6 10l-1 2v-1l-1-1-2-2v-1h-1v-1c-1-1-2-3-3-4 0-1-1-2-1-4-2 0-3-1-4-2v2l-3-3c0-3 0-5-1-7v-5-4h2 0l1-1z" class="AH"></path><path d="M355 355c0-1-2-2-2-4l1-1c0-1 0-1-1-2 0-2-1-3-1-4l1-1c1 3 2 6 4 9v2c1 1 1 2 2 3-2 0-3-1-4-2z" class="B"></path><path d="M351 342v-4h2c0 2 1 3 0 5l-1 1c0 1 1 2 1 4 1 1 1 1 1 2l-1 1c0 2 2 3 2 4v2l-3-3c0-3 0-5-1-7v-5z" class="C"></path><path d="M341 340h1c-1-3-2-4-3-6v-1l1-1c-2-2-2-5-3-7 5 3 7 9 11 13 1-2 1-3 1-4-1-1-1-2-1-3 0-2 0-2 1-4l2 1v2s1 0 1 1c1 0 2-1 2 0h-2c0 1 0 2 1 3l1 1v2h0l-1 1h0-2v4 5c1 2 1 4 1 7 0-1-1-2-1-3-1-2-2-4-3-7 0-3-1-4-3-6 0 1 0 2 1 3h0l-1 2 1 1c0 2 1 3 1 6h0c-2-2-5-7-6-10z" class="f"></path><path d="M351 342c-1-1 0-3-1-4v-1l1-1c0-2-1-4-1-6h1s1 0 1 1c1 0 2-1 2 0h-2c0 1 0 2 1 3l1 1v2h0l-1 1h0-2v4z" class="AU"></path><path d="M220 267h2 1v1 3c2 1 4 1 7 1 3 1 6 5 7 8 1 2 1 5 1 8h-1c0 2 0 2 1 4h0c-1 1 0 2 0 3 1 6 1 13 1 18v7c0 2 0 2-2 3-1 1-2 1-2 2h-2v1s1 1 2 1v-1c1 0 1 1 2 1h2v1 3l-1 1c1 2 1 2 1 4l-1 2-2-1-2 2h1 0 1l2 1v4c-1 0-2-1-4-1v-1c-2 1-4 1-5 2s-1 0-2 0-2 0-2 1c-2-1-2-1-3-2 0 1 0 0-1 1-1-1-2-1-3-2-1 0-1 0-2-1 0-1-1-1-2-2-1 0-1-1-2-2 0-1 0-1 1-2h0c0-1 0-2-1-2 2-2 2-3 4-5h0l-1-1c-1 1-1 2-2 2-2-2-1-10-1-13-1-4 0-10 0-14v-6-5c1-5 0-10 0-14l3-2h0c0-1 1-2 1-2l-1-1h-1l-1-1c2-1 4 0 5 0v-1l-1-1-1-1h4v-1z" class="G"></path><path d="M226 298c1-1 1-1 1-3v-1h1l1 1-1 2c1 1 2 1 2 2h1v1h-5v3h-1l-1-1 1-1c0-1 0-2 1-3z" class="r"></path><path d="M229 327l-1-2v-1-1-3l-2 1-1-1 1-1h3v3c1 0 2-1 2-1h1 1v2l1 1 1 1h-2-2v1l1 1h-2-1z" class="D"></path><path d="M236 313h3v7c0 2 0 2-2 3-1 1-2 1-2 2l-1-1c1-2 3-4 3-7h-1c-1-1-2-1-2-2 1 0 2 0 3 1-1-2-1-2-1-3z" class="N"></path><path d="M238 295c1 6 1 13 1 18h-3l1-1c0-2 0-2-1-3 1-1 0-1 1-2 0-1-1-1-2-1v-2l-1-1c0 1 0 1-1 2l-1-1v-2c1 0 1 0 2-1l-1-1h1v-3h1c0 1 1 1 2 2v-1-1l1-2z" class="F"></path><path d="M212 302c2 1 2 1 3 4h-1c0 1 0 2 1 3h2l2 1h1 0c1 1 1 1 2 1v1h0v1h0l1 1h1c-2 1-3 1-5 2l-3-3h-1v2c-1 1-1 1-3 1-1-4 0-10 0-14z" class="D"></path><path d="M230 327h2l-1-1v-1h2v1s1 1 2 1v-1c1 0 1 1 2 1h2v1 3l-1 1c1 2 1 2 1 4l-1 2-2-1-2 2h1 0 1l2 1v4c-1 0-2-1-4-1v-1c-2 1-4 1-5 2s-1 0-2 0-2 0-2 1c-2-1-2-1-3-2 0 1 0 0-1 1-1-1-2-1-3-2-1 0-1 0-2-1 0-1-1-1-2-2 1 0 2-1 2-2 1-1 0-1 1-2h2c0-1 1-1 2-2v1l1 1c0-1 1-2 1-2-1-2-2-2-3-3 1-2 2-1 3-2h1v2h1l1-1v-1c1-1 2-1 3-1h1z" class="g"></path><path d="M230 327h2l-1-1v-1h2v1s1 1 2 1v-1c1 0 1 1 2 1h2v1 3l-1 1c1 2 1 2 1 4l-1 2-2-1-2 2h1 0 1v2h-1v-1c-1 0-1 0-1-1h-1-1 0-1v-1-2l-1 1-1-1h0v-1l-1 2h-1v-2l1-2v-1c-1-1-1-2-2-3v-1c1-1 2-1 3-1h1z" class="AK"></path><path d="M229 327h1v2c-1 1-1 2-2 3-1-1-1-2-2-3v-1c1-1 2-1 3-1z" class="t"></path><path d="M220 267h2 1v1 3c2 1 4 1 7 1 3 1 6 5 7 8 1 2 1 5 1 8h-1c0 2 0 2 1 4h0c-1 1 0 2 0 3l-1 2v1 1c-1-1-2-1-2-2h-1v3h-1v-1h-2-1c0-1-1-1-2-2l1-2-1-1h-1v1c0 2 0 2-1 3-1 0-2-1-2-2v-1-1l-2 2-1-1c1-1 1-1 1-2-1-2-1-3-2-5v-1c-1-1-1-1-1-2h0l2 1h1c-1-1-1-1-1-2h1v-1h-2l1-2-2-2v-2-1c-1 1-1 1-2 1l-2-2h0c0-1 1-2 1-2l-1-1h-1l-1-1c2-1 4 0 5 0v-1l-1-1-1-1h4v-1z" class="v"></path><path d="M220 287c1 0 1 0 2 1l3-3v4h2 1l1 1c1 2 3 3 3 5 0 1 0 1-1 2l-1-1-1-1-1-1h-1v1c0 2 0 2-1 3-1 0-2-1-2-2v-1-1l-2 2-1-1c1-1 1-1 1-2-1-2-1-3-2-5v-1z" class="N"></path><path d="M237 288h-2c-1 0-3-3-4-4h-1-1l1-1-1-1c-1 0-1 1-2 1l-1-1h0v-1c1-2 1-1 3-2-1-1-2-2-3-2l-1-1 1-1h2c0-1 0-1 1-1 1 1 0 1 1 2h1l1 1c2 1 3 2 5 3 1 2 1 5 1 8h-1z" class="r"></path><path d="M391 476l16 2c1 0 4 0 5 1 2 1 3 2 5 1 2 2 3 5 5 7 0 1 1 3 2 4 3 5 5 10 8 14 2 4 5 9 8 13 2 3 4 7 7 10l7 11 6 7c-1 1 0 1-1 1v2c2 3 6 6 8 8 2 1 4 2 5 3 0 0-1 1-2 1-1 1-1 0-2 1h0c1 1 3 1 4 2 1 0 1 0 2 1-1 1-2 1-3 1-2-2-5-1-8-2-2-1-4-3-6-4-5-3-9-9-14-13h-1c-2-2-3-3-5-4h0c-1-2-2-3-3-4-2-1-4-5-5-7-2-2-4-4-7-5 0-1-1-2-2-3-2-2-2-3-3-5l3 3 1-2c-4-2-9-6-11-10l-2-2h0c-1-2-1-4-2-6l-6-7c-4-6-7-11-9-18v-1z" class="d"></path><path d="M408 496c2 2 5 5 5 8l1 2c-1-1-3-2-3-4l-2-2c-1-2-1-2-1-4z" class="M"></path><path d="M406 502l3 3c3 4 7 7 10 10l-6-3c-1-1-2-2-3-2l-2-2h0c-1-2-1-4-2-6z" class="Y"></path><path d="M452 550c3 2 6 3 9 5h0c2 2 4 4 7 5h0-2v1l2 1h0c1 1 3 1 4 2 1 0 1 0 2 1-1 1-2 1-3 1-2-2-5-1-8-2-2-1-4-3-6-4 2 0 3 1 5 2 1 1 3 1 5 1-5-4-11-7-15-12v-1z" class="J"></path><path d="M416 503l-14-24c2 2 4 3 5 5l9 15c1 2 3 4 4 6l1 3-1 1-4-6z" class="O"></path><path d="M435 531l-2-2c-3-2-5-5-8-8l-5-3 1-1 3 3 1-1-4-3h1v-1c-1-2-2-3-3-5l-3-6h-1l1-1 4 6 2 4 2-1 4 5 1 2c0 1 1 2 2 2 0 2 1 3 2 4v1l1 1c1 1 1 2 2 3l-1 1z" class="AA"></path><path d="M417 519l3 3 1-2a57.31 57.31 0 0 1 11 11c4 5 7 11 11 16h-1c-2-2-3-3-5-4h0c-1-2-2-3-3-4-2-1-4-5-5-7-2-2-4-4-7-5 0-1-1-2-2-3-2-2-2-3-3-5z" class="L"></path><path d="M417 519l3 3c6 5 10 10 14 17-2-1-4-5-5-7-2-2-4-4-7-5 0-1-1-2-2-3-2-2-2-3-3-5z" class="Y"></path><path d="M391 476l16 2h-2-7c1 4 4 7 5 11 1 3 4 5 5 7 0 2 0 2 1 4l2 2c0 2 2 3 3 4l3 4v1l-7-7-1 1-3-3-6-7c-4-6-7-11-9-18v-1z" class="AU"></path><path d="M391 477h3l3 6c2 3 6 7 6 10-1 0 0 1-1 0v-1l-2 3c-4-6-7-11-9-18z" class="O"></path><path d="M439 529l3 3 1-1-2-2h1c2 1 5 4 6 6 2 3 3 4 5 5 2 2 4 5 6 7v2c2 3 6 6 8 8 2 1 4 2 5 3 0 0-1 1-2 1-1 1-1 0-2 1l-2-1v-1h2 0c-3-1-5-3-7-5h0c-3-2-6-3-9-5l-3-3c-3-4-6-7-9-12-1-1-1-2 0-3l-1-3z" class="K"></path><path d="M440 532c2 1 2 2 3 4s3 3 4 5h-1l3 5v1c-3-4-6-7-9-12-1-1-1-2 0-3z" class="f"></path><path d="M461 555c-3-4-7-9-10-14-2-2-3-4-4-6h1c2 3 3 4 5 5 2 2 4 5 6 7v2c2 3 6 6 8 8 2 1 4 2 5 3 0 0-1 1-2 1-1 1-1 0-2 1l-2-1v-1h2 0c-3-1-5-3-7-5z" class="B"></path><path d="M416 499c0-1 1-2 1-2 0-2-3-5-4-7v-1c-2-3-3-6-5-9h0v-1c2 0 3 1 4 1v-1c2 1 3 2 5 1 2 2 3 5 5 7l-3-2v1c-1-1-2-2-4-3v1l1 1h-2c2 4 5 7 7 11 1 2 1 4 2 5l3 6h0c1 2 1 4 2 6s2 3 2 4c1 2 3 3 3 5 1 0 2 1 2 2 1 1 4 3 4 5l1 3c-1 1-1 2 0 3h-1c-2-1-3-2-4-4l1-1c-1-1-1-2-2-3l-1-1v-1c-1-1-2-2-2-4-1 0-2-1-2-2l-1-2-4-5-2 1-2-4 1-1-1-3c-1-2-3-4-4-6z" class="AG"></path><path d="M426 507h0l-3-6c-1-1-1-3-2-5-2-4-5-7-7-11h2l-1-1v-1c2 1 3 2 4 3v-1l3 2c0 1 1 3 2 4 3 5 5 10 8 14 2 4 5 9 8 13 2 3 4 7 7 10l7 11 6 7c-1 1 0 1-1 1-2-2-4-5-6-7-2-1-3-2-5-5-1-2-4-5-6-6h-1l2 2-1 1-3-3c0-2-3-4-4-5 0-1-1-2-2-2 0-2-2-3-3-5 0-1-1-2-2-4s-1-4-2-6z" class="X"></path><path d="M426 507c3 2 4 5 6 8 2 1 4 3 6 5 1 2 2 5 4 7s4 3 6 5v1c2 2 4 5 5 7-2-1-3-2-5-5-1-2-4-5-6-6h-1l2 2-1 1-3-3c0-2-3-4-4-5 0-1-1-2-2-2 0-2-2-3-3-5 0-1-1-2-2-4s-1-4-2-6z" class="AA"></path><path d="M319 505v-1c-1-2-1-2-1-4 1 1 1 2 2 3l1 2 1 2-1 1 1 1 2-1c1 1 1 1 2 1l1-1v1c1 0 0 0 1 1v1l2 1h1l1 1c-1 2-1 2 0 3 1 2 2 2 2 3 1 1 1 1 1 2l2 2 2 3h2c3 3 3 4 4 7 1 2 3 6 2 7v1 1s1 0 1 2h0v1c0 1 1 1 1 2v2c0 3 1 6 1 8l2 1c1-2 1-3 1-4l1-1v1c0 1 1 8 2 8v4 1c1 0 1 1 2 2l-1 1-1 2v2c0 1 0 2 1 2 1 1 2 2 3 4l-3-1-1 1 4 3-1 1c0 1-1 1-1 1l-1 1v1l1 1-1 1-1-1h0c-1-1-2-2-2-3v-1l-1-1c-1-1-1-2-2-3h-1-1c0-1 0-1-1-2v-1c-1 0-1-1-1-2s-1-2-2-3h1c-6-5-13-8-18-13l-3-4c-1-2-3-4-4-6-3-5-8-10-10-15l-1-1-6-9v-2l-1-1-3-3c-1-3-3-5-5-7v-1c2 0 6 1 8 1v-3h0l5-1h3c2 0 7 0 8-2z" class="G"></path><path d="M321 549l3 1s0 1 1 2h4c1 1 1 2 2 3h-6c-1-2-3-4-4-6z" class="D"></path><path d="M341 555c2 0 3 2 4 3l1 2c-2 1-2 1-3 2h0-2l-1 1c-1-1-2-1-2-3v-1l1 1h1l2-1c0-2 0-2-1-4zm-22-16l2-1-1-1 1-1 1 1h3l1 1 1 1c-1 1-2 1-4 1 1 1 0 1 1 1l1 1h0c1 1 1 1 1 2s0 1-1 2h-3-1l-1-1c-1 0 0 0-1-1 1-1 2-1 3-2l-1-1 1-2h-2-1z" class="N"></path><path d="M325 537c1-2 0-2 2-3l1 1v1c1 0 2 0 3-1 1 0 1 0 1-1l1-1 1 1-1 1h0c-2 2 0 6-1 7-1 0-1 0-1 1-1 0-1 0-1 1l-2-1h-1l1 1-2 2h-1c1-1 1-1 1-2s0-1-1-2h0l-1-1c-1 0 0 0-1-1 2 0 3 0 4-1l-1-1-1-1z" class="D"></path><path d="M334 534v1c1 1 0 0 0 1 1 1 1 2 2 3l1 1 1 1v1l-1 1v2c-1 2-2 3-4 4-1-1-2-2-3-2v1l-1-1-1-3-1-1h1l2 1c0-1 0-1 1-1 0-1 0-1 1-1 1-1-1-5 1-7h0l1-1z" class="AE"></path><path d="M337 545c1 0 1 1 2 2s1 1 2 1l1-1-1-1v-1-1c1 0 0 0 1-1l1 1-1 1c1 2 1 2 3 2l1 10-1 1c-1-1-2-3-4-3v-1c-1-1-1-1-1-2h-1c0 1-1 1-2 2-2-1-2-2-4-3h0v-2c2-1 3-2 4-4z" class="D"></path><path d="M331 555c0 2 0 2 1 3l1 1h1v-1h1v1l2 2v1 1l2 2h1v-2l1-1h2 0c1-1 1-1 3-2v12c-6-5-13-8-18-13l-3-4h6z" class="F"></path><path d="M341 562h2c1 1 1 1 1 2s0 1-1 1c-1 1-1 1-2 1v-4z" class="G"></path><path d="M341 526c3 3 3 4 4 7 1 2 3 6 2 7v1 1s1 0 1 2h0v1c0 1 1 1 1 2v2c0 3 1 6 1 8l2 1c0 1 0 3-1 4h0c-1-1-1 0-1-1 0 3 0 5-2 7 1 3 0 7 0 9-1 0-1-1-1-2s-1-2-2-3h1v-12l-1-2 1-1-1-10c0-3 0-9-1-11l-1-1 1-1c-1-1-2-3-3-4v-4z" class="V"></path><path d="M349 549c0 3 1 6 1 8l2 1c0 1 0 3-1 4h0c-1-1-1 0-1-1 0 3 0 5-2 7v-8c0-4 1-7 1-11z" class="u"></path><path d="M346 557c0 1 1 2 2 3v8c1 3 0 7 0 9-1 0-1-1-1-2s-1-2-2-3h1v-12l-1-2 1-1z" class="o"></path><path d="M337 523l2 3h2v4c1 1 2 3 3 4l-1 1 1 1c1 2 1 8 1 11-2 0-2 0-3-2l1-1-1-1c-1 1 0 1-1 1v1 1l1 1-1 1c-1 0-1 0-2-1s-1-2-2-2v-2l1-1v-1l-1-1-1-1c-1-1-1-2-2-3 0-1 1 0 0-1v-1-4c-1-1-1-2-1-3-1-1-1-2-1-2v-1c2 0 3 0 5-1z" class="g"></path><path d="M337 523l2 3h2v4l-2-2c-2 1-2 2-2 4v3 1c1 1 1 2 2 3l-1 2-1-1-1-1c-1-1-1-2-2-3 0-1 1 0 0-1v-1-4c-1-1-1-2-1-3-1-1-1-2-1-2v-1c2 0 3 0 5-1z" class="x"></path><path d="M353 554l1-1v1c0 1 1 8 2 8v4 1c1 0 1 1 2 2l-1 1-1 2v2c0 1 0 2 1 2 1 1 2 2 3 4l-3-1-1 1 4 3-1 1c0 1-1 1-1 1l-1 1v1l1 1-1 1-1-1h0c-1-1-2-2-2-3v-1l-1-1c-1-1-1-2-2-3h-1-1c0-1 0-1-1-2v-1c0-2 1-6 0-9 2-2 2-4 2-7 0 1 0 0 1 1h0c1-1 1-3 1-4 1-2 1-3 1-4z" class="T"></path><path d="M356 562v4 1c1 0 1 1 2 2l-1 1-1 2v2c0 1 0 2 1 2 1 1 2 2 3 4l-3-1-3-3c0-5 1-9 2-14z" class="W"></path><path d="M348 568c2-2 2-4 2-7 0 1 0 0 1 1h0c0 4-2 12 0 15l2 1 3 2 4 3-1 1c0 1-1 1-1 1l-1 1v1l1 1-1 1-1-1h0c-1-1-2-2-2-3v-1l-1-1c-1-1-1-2-2-3h-1-1c0-1 0-1-1-2v-1c0-2 1-6 0-9z" class="k"></path><path d="M319 505v-1c-1-2-1-2-1-4 1 1 1 2 2 3l1 2 1 2-1 1 1 1 2-1c1 1 1 1 2 1l1-1v1c1 0 0 0 1 1v1l2 1h1l1 1c-1 2-1 2 0 3 1 2 2 2 2 3 1 1 1 1 1 2l2 2c-2 1-3 1-5 1v1s0 1 1 2c0 1 0 2 1 3v4l-1-1-1 1c0 1 0 1-1 1-1 1-2 1-3 1v-1l-1-1c-2 1-1 1-2 3h-3l-1-1-1 1 1 1-2 1v-1c-1-2-1-3-2-4v-1c0-1 0-1-1-2l-2 2-1-1-2 2-1-1-6-9v-2l-1-1-3-3c-1-3-3-5-5-7v-1c2 0 6 1 8 1v-3h0l5-1h3c2 0 7 0 8-2z" class="t"></path><path d="M319 505v-1c-1-2-1-2-1-4 1 1 1 2 2 3l1 2 1 2-1 1 1 1 2-1c1 1 1 1 2 1l1-1v1c1 0 0 0 1 1v1l2 1h1l1 1c-1 2-1 2 0 3 1 2 2 2 2 3 1 1 1 1 1 2l2 2c-2 1-3 1-5 1h0c-1 0-1 0-2 1h-1l-1 1h0v-1h-1-1v1 1c0 1 0 1 1 2h-2l1-2-3-2h-1c-1 1-1 2-1 3l-1 1s0 1-1 1c0-1 0-1-1-2 0-1-1-3-2-4l-3-7c-1-1-2-4-3-5-1 0-3-1-3-1h-1-3v-3h0l5-1h3c2 0 7 0 8-2z" class="T"></path><path d="M320 529c-1-2-1-3-1-4l-1-1c1 0 1 0 2-1h-2c-1-1-1-1-1-3h1l-2-1-1-2-3-5c2-1 4 0 6 0s5 1 7 2c4 2 5 6 7 10-1 0-1 0-2 1h-1l-1 1h0v-1h-1-1v1 1c0 1 0 1 1 2h-2l1-2-3-2h-1c-1 1-1 2-1 3l-1 1zm26-220h1c4 2 5 5 8 7 2 3 5 5 7 8h0l1 1v1s-1 0-1 1c2 0 2 0 4 1 3 2 5 4 7 7v1l-2-1 1 2 1-1h2l2 2 2-2c1 0 2 1 2 1l1-1h1c0 1 0 1-1 2 1 1 2 2 3 2v1l2 1c2 1 3 2 4 4 1 1 1 1 1 2 2 0 3 2 5 3l1 1c1 0 1 0 2 1h1c1 0 1 0 2 1h0c-1 2-1 3-2 5v6c-1-1-2-1-3 0h-1c-1 0-1 1-1 1h-1c-2 0-3 0-4 1s-1 1-1 2c1 2 2 2 3 4v3c0 2 0 2 1 3v2l1 4c-2 1-3 2-5 3h0 1l-3 2c1 1 2 1 3 1h1l-3 3c-1 0-2 1-3 2l-2 1c-2 1-3 2-4 3v1h-1c-1-1-2-7-3-8v-1-1-5-1h-1c0-2-2-3-3-4-1-2-1-4-3-6v-2c0-1-1-2-2-2l1-2c-3-3-4-6-6-10l-8-22h0v-2l-1-1c-1-1-1-2-1-3h2c0-1-1 0-2 0 0-1-1-1-1-1v-2l-2-1c-1-1-2-3-3-5l-3-6-1-3 1-1v-1l1-1 1 2-1-3c1 0 2 1 2 0h0z" class="N"></path><path d="M349 315c3 2 5 5 7 8v2 2c-2-3-4-5-7-7h1c-1-2-1-3-1-5h0z" class="m"></path><path d="M349 320c3 2 5 4 7 7l3 3-2 2-3-1c0-1-1 0-2 0 0-1-1-1-1-1v-2c-1-3-2-5-2-8z" class="N"></path><path d="M379 336c1 0 2 1 2 1l1-1h1c0 1 0 1-1 2 1 1 2 2 3 2v1h-3c0 1 1 2 2 3l1 2h0l-2-1v1h0l2 2h-3l-2-1 1-2-2 1v-1l2-2v-1l-2 1v-1-1-1l-2 1v-1-2h0l2-2z" class="m"></path><path d="M344 309c1 0 2 1 2 0h0l3 6h0c0 2 0 3 1 5h-1c0 3 1 5 2 8l-2-1c-1-1-2-3-3-5l-3-6-1-3 1-1v-1l1-1 1 2-1-3z" class="AU"></path><path d="M342 313l1-1v-1l1-1 1 2c1 2 1 3 1 5 1 1 1 2 1 4l-1 1-3-6-1-3z" class="C"></path><path d="M356 323c2 1 3 4 5 4l-1 1c1 2 2 2 3 3h0c1 0 1 0 2 1l-1 1c1 1 2 2 4 3l1-1v1l-1 1c1 2 5 5 5 7v1c0 1 0 1 1 2h-1v2c-2-2-2-5-5-6v2h0l-1-1 1-1c0-2-3-4-3-5-1-1-1-2-2-2l-4-6-3-3v-2-2z" class="T"></path><path d="M359 330l4 6c1 0 1 1 2 2 0 1 3 3 3 5l-1 1 1 1h0v-2c3 1 3 4 5 6v-2h1c-1-1-1-1-1-2v-1l6 7h1 1l1 1-1 2 1 3-1 1v3l-1 1h-1v-1c0-1 0-2-1-2v-1l-1-1h-1l1-1-2-1c-1-1-1-1-2-1v1h-1c-1 0-1 1-2 1h-4c-1 0-1-1-1-2h-1c-1 0-1 0-2-1 0 1 1 2 1 3v1l2 1c-1 1-2 1-3 1l-8-22h0v-2l-1-1c-1-1-1-2-1-3h2l3 1 2-2z" class="D"></path><path d="M373 344l6 7h1 1l1 1-1 2 1 3-1 1v3l-1 1h-1v-1c0-1 0-2-1-2v-1l-1-1h-1l1-1v-2l-2-2c-1 0-2 1-3 1v-1l2-1v-1l-1-1v-2h1c-1-1-1-1-1-2v-1z" class="w"></path><path d="M379 351h1 1l1 1-1 2 1 3-1 1c0-1 0 0-1-1-2-2-2-3-2-6h1z" class="k"></path><path d="M385 341l2 1c2 1 3 2 4 4 1 1 1 1 1 2 2 0 3 2 5 3l1 1c1 0 1 0 2 1h1c1 0 1 0 2 1h0c-1 2-1 3-2 5v6c-1-1-2-1-3 0h-1c-1 0-1 1-1 1h-1c-2 0-3 0-4 1h-3c-2-1-2-2-3-3l-1 1c0 1 0 1 1 3-1 0-1 1-1 2-1 1-1 1-1 2l-2-1v-2c0-1-1-1-1-2h1v-3-3-3l1-1-1-3 1-2-1-1c0-1 0-2 1-3h3l-2-2h0v-1l2 1h0l-1-2c-1-1-2-2-2-3h3z" class="s"></path><path d="M392 357v-3h-1l1-1h3l2 2c-2 1-2 1-3 2h-1-1z" class="Q"></path><path d="M385 364v-1c1-1 1-2 2-3 0 2 0 4 2 5h1c0-1-1-2-1-2v-2l2 2c1 2 1 2 4 3-2 0-3 0-4 1h-3c-2-1-2-2-3-3z" class="b"></path><path d="M385 341l2 1c2 1 3 2 4 4h-2c-2 0-2 0-3 1-1 0 0 0-1 1l1 1c2 0 2 0 3 1l1 1c-2 2-3 2-5 2v-1l-1 1-2-1-1-1c0-1 0-2 1-3h3l-2-2h0v-1l2 1h0l-1-2c-1-1-2-2-2-3h3z" class="j"></path><path d="M382 357c1-1 2-2 3-2l1-1v2l1 1c0 1 1 2 0 3h0c-1 1-1 2-2 3v1l-1 1c0 1 0 1 1 3-1 0-1 1-1 2-1 1-1 1-1 2l-2-1v-2c0-1-1-1-1-2h1v-3-3-3l1-1z" class="u"></path><path d="M397 355l4 2v2 6c-1-1-2-1-3 0h-1c-1 0-1 1-1 1h-1c-3-1-3-1-4-3 0-1-1-2 0-3 0-1 1-1 1-3h1 1c1-1 1-1 3-2z" class="W"></path><path d="M362 359c1 0 2 0 3-1l-2-1v-1c0-1-1-2-1-3 1 1 1 1 2 1h1c0 1 0 2 1 2h4c1 0 1-1 2-1h1v-1c1 0 1 0 2 1l2 1-1 1h1l1 1v1c1 0 1 1 1 2v1h1l1-1v3 3h-1c0 1 1 1 1 2v2l2 1c0-1 0-1 1-2 0-1 0-2 1-2-1-2-1-2-1-3l1-1c1 1 1 2 3 3h3c-1 1-1 1-1 2 1 2 2 2 3 4v3c0 2 0 2 1 3v2l1 4c-2 1-3 2-5 3h0 1l-3 2c1 1 2 1 3 1h1l-3 3c-1 0-2 1-3 2l-2 1c-2 1-3 2-4 3v1h-1c-1-1-2-7-3-8v-1-1-5-1h-1c0-2-2-3-3-4-1-2-1-4-3-6v-2c0-1-1-2-2-2l1-2c-3-3-4-6-6-10z" class="j"></path><path d="M386 396l-1-1c-1-2-1-1-1-2 2-1 3-2 4-3 1 1 2 1 3 1h1l-3 3c-1 0-2 1-3 2z" class="Z"></path><path d="M376 391l3-1v-1c-1-1-1-1-1-2 1-1 2-1 3-1l2 2c-2 2-4 3-7 5v-1-1z" class="n"></path><path d="M383 372c0-1 0-1 1-2 0-1 0-2 1-2-1-2-1-2-1-3l1-1c1 1 1 2 3 3h3c-1 1-1 1-1 2 1 2 2 2 3 4v3c0 2 0 2 1 3v2l1 4c-2 1-3 2-5 3h0l-9 5c1-2 2-4 4-5h1l-1-1c2-2 2-3 2-5h-1v-2s-1-1-1-2v-3l-1-1c0-1 0-1-1-2z" class="s"></path><path d="M388 367h3c-1 1-1 1-1 2 1 2 2 2 3 4v3h-2l-3 3v2-10h1c0-2 0-2-1-4z" class="U"></path><path d="M388 381v-2l3-3h2c0 2 0 2 1 3v2l1 4c-2 1-3 2-5 3l-2-2v-5z" class="E"></path><path d="M362 359c1 0 2 0 3-1l-2-1v-1c0-1-1-2-1-3 1 1 1 1 2 1h1c0 1 0 2 1 2h4c1 0 1-1 2-1h1v-1c1 0 1 0 2 1l2 1-1 1h1l1 1v1c1 0 1 1 1 2v1h1l1-1v3 3h-1-1c-1 0-2-1-2-2l-1 1c0 1 1 1 1 2 1 1 2 2 2 3 0 2 1 3 2 4 0 3 1 6 1 9-2-2-3-4-6-5v-1h1l-6-6-1-1s-1-1-2-1v-1c-3-3-4-6-6-10z" class="r"></path><path d="M378 358v1c1 0 1 1 1 2v1h1l1-1v3 3h-1-1c-1 0-2-1-2-2l-1 1c0 1 1 1 1 2 1 1 2 2 2 3 0 2 1 3 2 4 0 3 1 6 1 9-2-2-3-4-6-5v-1h1l-6-6 1-1h0 1v1l1 1c1 0 2 0 3 1v1c0 1 1 1 2 2v-1c0-1-1-1-1-2v-1c0-2-1-1-3-2v-4l-1-1v-2h-1l-2-2h0l-1-1c1-1 1-1 2-1 2-1 1-2 4-2 0 1 0 1 1 1l1-1z" class="F"></path><path d="M658 554c-1 3-3 5-4 8-1 2-1 5-2 8v1c-1 2-1 4-1 7l-1 2-2 3 1 1-1 1 1 1h1v9c-1 1-1 1-2 1s-1 0-2 1h3c1 1 1 2 1 3 0 2 0 3-1 5l1-1v3c-2 2-4 5-6 7-2 3-4 5-4 9l-1 4 1 1c2 1 3 0 5 2 1 1 1 1 0 3-1 1-2 1-3 2v5l3 1-1 4h-1-2c-1 1-3 5-4 5h-1-1l-2-1h-1c0 1 0 2 1 3-2 2-2 3-2 6h0c-1 2-1 3-3 4v2c-1 1-2 0-3 0l-1-2h-1v2 2l-2 2c1 1 2 3 3 4-1 1-1 1-1 2h-5c-2 1-2 1-3 1l-2 1h0v1l-5 2v2l1 1h1v1 1l-2 2-1 1c0 1 1 2 1 3-3 0-5 0-7 2-2 1-2 2-4 2-1 1-2 2-2 4v2c-3 5-6 11-9 18-1 3-3 6-4 9l2 2c-1 1-1 1-3 2v-1l-2-1h0l-1-1c-1 1-1 1-2 1h0-3 0c-1 1-3 1-4 1h-1l20-28c2-4 6-8 7-12l-3 3v-1h-1c2-3 4-5 5-7v-1-1-1l-1-1v-1h1v-1l3-3 2-2c1-3 3-5 4-7l6-13c4-6 8-12 10-19v-1l1-1c0-1 1-3 2-4v-1l1-2-1-1-1 1h-1c0-1 1-2 2-3s2-2 2-4c3-4 4-8 6-12l3-6c1-3 2-5 2-7 0-1 3-7 4-9l-1-1-1-1 6-7c-3 2-6 5-8 7h-1v-2c1 0 1-1 2-1 2-1 3-3 5-4 4-4 6-11 9-16h0 1 0c2-2 3-4 5-6z" class="AI"></path><path d="M599 676l1 1 4-3c-3 4-5 7-8 10v-1-1l-1-1v-1h1v-1l3-3z" class="B"></path><path d="M622 642h3c-2 2-3 4-5 7-1 4-2 7-2 10l1 1c-2-1-3-3-4-4l7-14z" class="T"></path><path d="M626 626l2-3c1 2 1 3 1 5 1 0 1 1 2 1 0 2 0 1-1 2h-2c-1 1-2 3-2 4l1 1c-1 1-2 2-2 4l2 1-2 1h-3c0-2 1-4 2-6l2-10z" class="I"></path><path d="M608 679v2l1 1h1v1 1l-2 2-1 1c0 1 1 2 1 3-3 0-5 0-7 2-2 1-2 2-4 2l5-8c2-3 3-6 6-7z" class="Z"></path><path d="M615 656c1 1 2 3 4 4l-1 1v5c-2-2-2-4-2-6h0v3c0 4-5 7-3 11l2 1-2 1h0l-6 1 8-21z" class="V"></path><path d="M615 675l-2-1c-2-4 3-7 3-11v-3h0c0 2 0 4 2 6v-5c0 3 1 5 3 7 1 1 2 3 3 4-1 1-1 1-1 2h-5c-2 1-2 1-3 1z" class="o"></path><path d="M628 623c0-2 1-3 2-5 0 2-1 5 0 6 2 0 3 0 5-1v3h1l1 1h0 2 0l1 1c2 1 3 0 5 2 1 1 1 1 0 3-1 1-2 1-3 2v5l3 1-1 4h-1-2c-1 1-3 5-4 5h-1v-7l-1-1v-1c-1-2-3-1-5-1h-2l-1 1-2-1c0-2 1-3 2-4l-1-1c0-1 1-3 2-4h2c1-1 1 0 1-2-1 0-1-1-2-1 0-2 0-3-1-5z" class="T"></path><path d="M630 624c2 0 3 0 5-1v3l-1 2h-1c-1-1-2-2-3-4z" class="v"></path><path d="M641 645l-2-1v-1h1c1 0 1 0 2-1v-2l3 1-1 4h-1-2z" class="I"></path><path d="M636 632h5v1h0c-2 2-2 3-2 5 0 1-1 2-1 2l-3 2v-1c-1-2-3-1-5-1h-2l-1 1-2-1c0-2 1-3 2-4s3-3 5-3l4-1z" class="k"></path><path d="M627 636c1-1 3-3 5-3l4-1 2 3v1c-2 1-4 0-6 0v1h-3l-1 1v2l-1 1-2-1c0-2 1-3 2-4z" class="z"></path><path d="M630 640c2 0 4-1 5 1v1l1 1v7h-1l-2-1h-1c0 1 0 2 1 3-2 2-2 3-2 6h0c-1 2-1 3-3 4v2c-1 1-2 0-3 0l-1-2h-1v2 2l-2 2c-2-2-3-4-3-7l1-1-1-1c0-3 1-6 2-10 2-3 3-5 5-7l2-1 1-1h2z" class="AE"></path><path d="M630 640c2 0 4-1 5 1v1l1 1c-1 2 0 3-2 4-1 0-1-1-3-1h0c-3 2-2 0-3 0s-1 1-3 1v-3h2c1 0 1 1 1 2l2-2v-4zm-10 9c2 0 3-1 4-1v1 1 2h2v3h0l2 2h-1l-3-1h-1l-1 1c1 1 2 0 3 1h3 3c-1 2-1 3-3 4v2c-1 1-2 0-3 0l-1-2h-1v2 2l-2 2c-2-2-3-4-3-7l1-1-1-1c0-3 1-6 2-10z" class="F"></path><path d="M658 554c-1 3-3 5-4 8-1 2-1 5-2 8v1c-1 2-1 4-1 7l-1 2-2 3 1 1-1 1 1 1h1v9c-1 1-1 1-2 1s-1 0-2 1h3c1 1 1 2 1 3 0 2 0 3-1 5l1-1v3c-2 2-4 5-6 7-2 3-4 5-4 9l-1 4h0-2 0l-1-1h-1v-3c-2 1-3 1-5 1-1-1 0-4 0-6-1 2-2 3-2 5l-2 3v-1-1h0c0-1 0-1 1-2v-1h0l1-1c0-2 0-3 1-4s1-2 2-3l1-1 2-5c0-1 1-2 1-2l1-2c0-1 0-2 1-2 1-2 1-3 1-5h0c0-1 1-2 1-3l1-3c0-1 1-2 1-3l-3 6-1 1c0-1 3-7 4-9l-1-1-1-1 6-7c-3 2-6 5-8 7h-1v-2c1 0 1-1 2-1 2-1 3-3 5-4 4-4 6-11 9-16h0 1 0c2-2 3-4 5-6z" class="c"></path><path d="M645 585c1-4 3-8 5-12 0-1 1-2 2-3v1c-1 2-1 4-1 7l-1 2-2 3 1 1-1 1h-1l-1 1-1-1z" class="Ac"></path><path d="M645 585l1 1 1-1h1l1 1h1v9c-1 1-1 1-2 1s-1 0-2 1-1 1-2 1l-1 1h1l-3 3h-1v3c-1 1-3 2-3 4h-1l-1-2c2-3 3-5 4-8 2-5 3-9 6-14z" class="I"></path><path d="M649 586h1v9c-1 1-1 1-2 1s-1 0-2 1-1 1-2 1l-1 1h0-2c0-1 0-2 1-3s2-3 2-5c1-2 4-4 5-5h0z" class="AK"></path><path d="M636 609h1c0-2 2-3 3-4v8c0 2-1 6-2 7l-1 1c1 1 2 0 3 2l-1 4h0-2 0l-1-1h-1v-3c-2 1-3 1-5 1-1-1 0-4 0-6 1-4 4-7 5-11l1 2z" class="F"></path><path d="M635 610c1 3 1 7 1 10l1 1c1 1 2 0 3 2l-1 4h0-2 0l-1-1h-1v-3-13z" class="x"></path><path d="M636 609h1c0-2 2-3 3-4v8c0 2-1 6-2 7l-1 1-1-1c0-3 0-7-1-10l1-1z" class="y"></path><path d="M646 597h3c1 1 1 2 1 3 0 2 0 3-1 5l1-1v3c-2 2-4 5-6 7-2 3-4 5-4 9-1-2-2-1-3-2l1-1c1-1 2-5 2-7v-8-3h1l3-3h-1l1-1c1 0 1 0 2-1z" class="AM"></path><path d="M646 597h3c1 1 1 2 1 3l-1 1-5-2h-1l1-1c1 0 1 0 2-1zm-6 5c3 0 6 1 8 2-2 4-4 7-8 9v-8-3z" class="F"></path><path d="M340 269c3 0 4 1 6 1l2 2h1 1l1-2 4 6c1 2 3 4 4 6 3 3 5 6 7 8 3 0 5 0 8-1l6 1v2c1 9 8 19 15 24l3 3c3 2 5 3 8 4 1 0 2 1 3 2s2 1 3 2c-2 2-3 1-3 4 0 1-1 2-1 3-1-1-2 0-4 0-1 1-1 1-2 3l3 3-1 1v3 3l-1 2 1 1c0 1 0 2-1 4-1-1-1-1-2-1h-1c-1-1-1-1-2-1l-1-1c-2-1-3-3-5-3 0-1 0-1-1-2-1-2-2-3-4-4l-2-1v-1c-1 0-2-1-3-2 1-1 1-1 1-2h-1l-1 1s-1-1-2-1l-2 2-2-2h-2l-1 1-1-2 2 1v-1c-2-3-4-5-7-7-2-1-2-1-4-1 0-1 1-1 1-1v-1l-1-1h0c-2-3-5-5-7-8-3-2-4-5-8-7h-1 0l-1-8-1-1-1-9-1-2v-5l-1-4v-2l-1-5v-4z" class="h"></path><path d="M382 310l4 4-1 2c0 1 0 1-1 2v1l-4-6c2-1 1-1 2-3z" class="p"></path><path d="M345 301c1 0 1 0 3 1h1v3c1 1 3 2 4 3a57.31 57.31 0 0 1 11 11c1 2 3 3 4 5h0c2 1 3 3 5 4l1-1v2c0 1 4 4 4 5h1l1-1 1 1-2 2-2 2-2-2h-2l-1 1-1-2 2 1v-1c-2-3-4-5-7-7-2-1-2-1-4-1 0-1 1-1 1-1v-1l-1-1h0c-2-3-5-5-7-8-3-2-4-5-8-7h-1 0l-1-8z" class="AM"></path><path d="M386 314v1c1 1 2 1 2 3 2 2 5 6 7 7v-1l-1-1 1-1v-1l-1-1v-2s0-1 1-2l3 3c3 2 5 3 8 4 1 0 2 1 3 2s2 1 3 2c-2 2-3 1-3 4 0 1-1 2-1 3-1-1-2 0-4 0-1 1-1 1-2 3l3 3-1 1v3 3l-1 2 1 1c0 1 0 2-1 4-1-1-1-1-2-1h-1c-1-1-1-1-2-1l-1-1c-2-1-3-3-5-3 0-1 0-1-1-2-1-2-2-3-4-4v-2h-1l1-1c1 1 2 1 3 3h0l1-1v-2l1-1s0-1 1-1v-1l-3-3-1 1v-1h0 2 0v-1c0-1 0-1-1-2h-2-1c-1 0-1-1-1-2h1l1-2-1-1h-2l-1-1-1-1c1 0 1 0 2-1l-1-1h-1c0-1 0-1 1-1v-1-1c1-1 1-1 1-2l1-2z" class="T"></path><path d="M395 316l3 3c3 2 5 3 8 4 1 0 2 1 3 2s2 1 3 2c-2 2-3 1-3 4 0 1-1 2-1 3-1-1-2 0-4 0-1 1-1 1-2 3l3 3-1 1v3c-1 0-2 1-3 1l-1 1 1-1c0-2 0-2-1-4v-3l-1-1c-3 0-1 0-1-1s-1-2-1-2c1 0 1 0 2 1h1c0-2-2-2-2-4h1v1h1c0-1 0-2-1-3h0l1-1-1-1h-1v-1-1-2l-1-1c0-2-2-3-3-4 0 0 0-1 1-2z" class="k"></path><path d="M398 319c3 2 5 3 8 4 1 0 2 1 3 2 0 0-1 1-1 2v1c0 1-2 3-3 4l-2-4c-1-1-2-2-2-3l-1-1c-1-1-1-3-2-5z" class="AK"></path><path d="M340 269c3 0 4 1 6 1l2 2h1 1l1-2 4 6c1 2 3 4 4 6 3 3 5 6 7 8 3 0 5 0 8-1l6 1v2c1 9 8 19 15 24-1 1-1 2-1 2v2l1 1v1l-1 1 1 1v1c-2-1-5-5-7-7 0-2-1-2-2-3v-1l-4-4c-1 2 0 2-2 3l-2-2-1-2c-1 0-1-1-2-2 0-1-1-1-2-1h0-1l-1 1h0l2 1 1 3h-1-1v-2c-1 0-1 1-2 2l-2-1c-1-1-1-1-2-1s-2 0-3-1h1l1-1h-2l-1-1c0 1-1 1-2 2l1-1-1-2h-3l-1-1c-1-2-3-4-4-6-3-2-6-5-9-7l-1-2v-5l-1-4v-2l-1-5v-4z" class="G"></path><path d="M341 278c1-1 2-1 3-1h1v1l1 1v1l-1 1c0 1 0 1-1 2h-1s-1 0-1 1l-1-4v-2z" class="F"></path><path d="M342 289h1c1 1 2 1 3 2l2-2h1l1 1h0c1 2 1 4 1 5v1l3 1-2 1c-3-2-6-5-9-7l-1-2z" class="g"></path><path d="M340 269c3 0 4 1 6 1l2 2c2 1 3 3 4 4-1 2-1 2 0 4 1 1 2 1 3 2l-1 1c-1 0-2 1-3 1s-2 1-3 1l-2-2c1-1 4-2 4-4h-1v-1-1c-1 0-1 0-2-1v-2c0-2 0-2-1-2h0v-1h-2v2h0-1-2-1v-4z" class="F"></path><path d="M351 270l4 6c1 2 3 4 4 6 3 3 5 6 7 8 3 0 5 0 8-1l6 1v2c1 9 8 19 15 24-1 1-1 2-1 2v2l1 1v1l-1 1 1 1v1c-2-1-5-5-7-7 0-2-1-2-2-3v-1l-4-4c-1 2 0 2-2 3l-2-2-1-2c-1 0-1-1-2-2 0-1-1-1-2-1l-1-1h-1 0v-1h1c-1-1-1-1-1-2h0c-1-1-2-2-3-4v-1c-1-1-1 0-3-1v-1l-1-2h-9-2v-1 1l1-1h1l1-1v-3l1 1s0 1 1 1h0v-1-1c-1-2-2-2-3-3l1-1-1-2c-1-1-2-1-3-2-1-2-1-2 0-4-1-1-2-3-4-4h1 1l1-2z" class="y"></path><path d="M352 276l11 15c-2 0-5 1-7 0h0v-3l1 1s0 1 1 1h0v-1-1c-1-2-2-2-3-3l1-1-1-2c-1-1-2-1-3-2-1-2-1-2 0-4z" class="N"></path><path d="M382 310l-15-17c2 0 4-1 6-1h0 1c2-1 4-1 6 0 1 9 8 19 15 24-1 1-1 2-1 2v2l1 1v1l-1 1 1 1v1c-2-1-5-5-7-7 0-2-1-2-2-3v-1l-4-4z" class="G"></path><path d="M262 408l2 2h0c1 2 2 4 2 6h2v3l1 2h0c1 1 1 2 2 2 2 3 5 7 7 9l3 4 1-1v-3l1-2h0l2 2v2h1v3l1 1c1-4 1-6 3-10h2v1s-1 1-1 2v1l1 1 2 3c1-1 1-1 3 0 1 0 1 1 2 2l-1 1 2 2 2-2 1 1-1 1c0 2 1 4 2 6l7 14c3 4 5 9 7 13l1 1c1 2 2 5 3 7v5c2 3 3 6 4 9l1 3v3l-6 3-1-2c-1-1-1-2-2-3 0 2 0 2 1 4v1c-1 2-6 2-8 2h-3l-5 1h0c-4 0-6 0-10-1-1-1-1-1-2-1-2 1-3 1-5 1l-1-2v-2-2c-1 0-2-1-3-3h0l-2 1c-1-1 0-1 0-3h-3c-3 0-3 0-6 1v1h-3l-1-17c-1 2 0 3-1 4h-1v-3h-1v5l-3 4-1-9 1-9v-10-3l-1-25c1-2 0-4 0-6v-6-4l2 2v-2l1-1c-1-3-1-8-1-10z" class="AE"></path><path d="M299 474c0-1 1-1 1-2l1-1c1 0 2 0 3 2l-3 4c-1-1-1-1-2-3z" class="v"></path><path d="M291 472v-1l2-2 1-1c2 0 2 1 4 1h1l-2 2h-2v1c-2 0-1-2-3 0l-1 1 1 1h-1c-1-1-1-1 0-2z" class="N"></path><path d="M303 483c2-2 3-2 4-3 1 0 2 0 2 1v1h1v1c-1 1-1 1-2 1s-1 0-2 1l-3-2z" class="t"></path><path d="M319 475c1 2 2 5 3 7v5l-5-8 2-4z" class="l"></path><path d="M320 503c0-1 1-2 1-4 0-1 1-1 2-1 0 0 1 0 1 1h1 2v3l-6 3-1-2z" class="D"></path><path d="M291 476l2-2c0-1 0-1 1-1l2 2h1v-2h1l1 1c1 2 1 2 2 3 0 1 0 1-1 1l1 2v1h0l-2 2c0-1 0-2-1-3l-1 2c-2-1-4-4-6-6z" class="F"></path><path d="M290 428h2v1s-1 1-1 2v1l1 1 2 3c-2 3-2 3-2 6 1 1 1 1 2 1-1 2-1 2-1 3v2h0c-1 2-2 4-2 6-1-1-1-1-1-2v-1-1h-2c0-2 1-5 0-7v-4c0-1 0-1-1-1 1-4 1-6 3-10z" class="Z"></path><path d="M290 428h2v1s-1 1-1 2v1c0 1-1 2-1 3-2 2-1 6-2 8v-4c0-1 0-1-1-1 1-4 1-6 3-10z" class="o"></path><path d="M300 441l2-2 1 1-1 1c0 2 1 4 2 6l7 14c3 4 5 9 7 13l1 1-2 4c0-1 0-1-1-2v-1c-1-1-1-3-2-5h-2v-2h-4v-3h2l-1-1 1-2h-2l1-2-2-1c1 0 1 0 1-1v-1l-1 1h-1l1-2h-1-2l-1-1c0-1 1-2 1-2v-1h-1l1-1c0-2-1-2-1-3h-3l1 2-1 1h-1v-2c0-1 0-1 1-2 0-1-1-1 0-2l1 1v-1-1c0-1 0-2-1-4z" class="h"></path><path d="M297 482l1-2c1 1 1 2 1 3l2-2v1 1h2l3 2 1 1h-1c1 2 1 3 2 5h1c0-1 1-2 0-3l1-1h0l1-2h2l-1 2 1 1 1-1c1 1 1 0 2 2l-1 1h0l1 2h-2v1l1 3v2h1v-2h1v1c0 1 0 2 1 2v1c0 2 0 2 1 4v1c-3 0-3-1-4-2l-1-1c-1-1-3-2-4-3l-1-2c-1 1-2 1-3 0l1-1h1c-2-2-3-4-5-5h-1l-3-3c0-1 1-1 1-2l-3-4z" class="v"></path><path d="M301 483h2l3 2 1 1h-1l-1 1v-1l-1-1h-3v-1-1z" class="D"></path><path d="M283 430h0l2 2v2h1v3l1 1c1 0 1 0 1 1v4c1 2 0 5 0 7h2v1 1c0 1 0 1 1 2 0 2-1 4-1 5v12h-1l-1 1v1 2l1 1-1 1h-1c0-1-1-1-2-1l-1-11v-1c-1-3-1-5-2-8 0 0 0-1 1-2h0l-1-2 1-1v-2h-1l1-1v-4l-2-2c-1-3-2-5-4-7-1 0-1 0-1-1h1c1 0 2 1 3 3 2 1 3 2 3 4l1-1c-1-2-2-2-3-4l1-1v-3l1-2z" class="j"></path><path d="M283 430h0l2 2v2h1v3l1 2-2 1v2h0l-2-1 1-1c-1-2-2-2-3-4l1-1v-3l1-2z" class="m"></path><path d="M283 451c1 1 1 1 1 3l2 1c-1 2-1 3 0 4v5h1c1 1 1 1 1 2l-1 3c-2-1-1-1-2-3l-1-1v-1c-1-3-1-5-2-8 0 0 0-1 1-2h0l-1-2 1-1z" class="o"></path><path d="M287 464c1-2 0-3 0-5 0-1 0-3 1-4l2 4v12h-1l-1 1v1 2l1 1-1 1h-1c0-1-1-1-2-1l-1-11 1 1c1 2 0 2 2 3l1-3c0-1 0-1-1-2z" class="T"></path><path d="M290 471l1 1c-1 1-1 1 0 2v2c2 2 4 5 6 6l3 4c0 1-1 1-1 2l3 3h1c2 1 3 3 5 5h-1l-1 1c1 1 2 1 3 0l1 2c1 1 3 2 4 3l1 1c1 1 1 2 4 2-1 2-6 2-8 2h-3l-5 1h0c-4 0-6 0-10-1-1-1-1-1-2-1-1-2-1-5-1-7 2 0 4-1 7-1h0v-2c-2 1-3 1-4 1h-2-1v-7c1-2 1-4 1-5l-2-1h0-1l-1 1v-2c-1 1-1 2-1 3h0l-1-3v-7c1 0 2 0 2 1h1l1-1-1-1v-2-1l1-1h1z" class="D"></path><path d="M290 471l1 1c-1 1-1 1 0 2v2c2 2 4 5 6 6l3 4c0 1-1 1-1 2h-1v-2c-2 0-5-3-6-5 0-1-1-2-1-3-1 2 0 5 0 7l-2-1h0-1l-1 1v-2c-1 1-1 2-1 3h0l-1-3v-7c1 0 2 0 2 1h1l1-1-1-1v-2-1l1-1h1z" class="y"></path><path d="M306 497c1 1 2 1 3 0l1 2c1 1 3 2 4 3l1 1c1 1 1 2 4 2-1 2-6 2-8 2h-3l-5 1h0c-4 0-6 0-10-1 0-1 1-2 1-3h0c2-2 3 0 6 0v-1l1 1c1-1 3-1 5-1h0v-1h-3v-1-2l1-1c1 0 1 0 2-1z" class="G"></path><path d="M262 408l2 2h0c1 2 2 4 2 6h2v3l1 2h0c1 1 1 2 2 2 2 3 5 7 7 9l3 4c1 2 2 2 3 4l-1 1c0-2-1-3-3-4-1-2-2-3-3-3h-1c0 1 0 1 1 1 2 2 3 4 4 7l2 2v4l-1 1h1v2l-1 1 1 2h0c-1 1-1 2-1 2 1 3 1 5 2 8v1l1 11v7l1 3h0c0-1 0-2 1-3v2l1-1h1 0l2 1c0 1 0 3-1 5v7h1 2c1 0 2 0 4-1v2h0c-3 0-5 1-7 1 0 2 0 5 1 7-2 1-3 1-5 1l-1-2v-2-2c-1 0-2-1-3-3h0l-2 1c-1-1 0-1 0-3h-3c-3 0-3 0-6 1v1h-3l-1-17c-1 2 0 3-1 4h-1v-3h-1v5l-3 4-1-9 1-9v-10-3l-1-25c1-2 0-4 0-6v-6-4l2 2v-2l1-1c-1-3-1-8-1-10z" class="o"></path><path d="M266 425l-1-1 1-1 6 6 1 2c0 1 0 1-1 1l-3-3c-1 2-1 3-1 4h-1 0l-1 1h-1v-4c0-2 0-3 1-5z" class="k"></path><path d="M273 437l2 2h1v-1h1c1 3 1 8 1 12h0c0 2-1 3-2 4h-2v-1-1c1-2 1-5 0-7h1l-1-3c0-1-1-2-1-2v-2-1z" class="AM"></path><path d="M268 433c0-1 0-2 1-4l3 3 2 2c0 1-1 2-2 3h1v1 2s1 1 1 2l1 3h-1c1 2 1 5 0 7v1-2l-1-1c-2 1-3 2-4 4h-2v-3-1l1-1-1-1v-3c0-2 0-3 1-4s0-1 1-1h1v-1h-1-1c0-2 1-1 1-2l-1-1v-3z" class="r"></path><path d="M260 419l2 2c0 2 0 3 1 5v1c1-1 2-1 3-2-1 2-1 3-1 5v4h1l1-1h0 1v3l1 1c0 1-1 0-1 2h1 1v1h-1c-1 0 0 0-1 1s-1 2-1 4v3l1 1-1 1v1 3 10 2 6 4 5c-1 2 0 3-1 4h-1v-3h-1v5l-3 4-1-9 1-9v-10-3l-1-25c1-2 0-4 0-6v-6-4z" class="V"></path><path d="M264 467h1c0 2-1 7 1 9h1v5c-1 2 0 3-1 4h-1v-3h-1v-15z" class="h"></path><path d="M264 453v-2h3v3 10 2 6 4h-1c-2-2-1-7-1-9h-1v-14z" class="y"></path><path d="M260 419l2 2c0 2 0 3 1 5v1 4 4 7h0v1h-1v-1 1c0 1 0 2-1 4 0 2 1 15 0 16v-3l-1-25c1-2 0-4 0-6v-6-4z" class="w"></path><path d="M266 425c-1 2-1 3-1 5v4h1l1-1h0 1v3l1 1c0 1-1 0-1 2h1 1v1h-1c-1 0 0 0-1 1s-1 2-1 4v3l1 1-1 1v1h-3v2c0-4-1-7-1-11h0v-7-4-4c1-1 2-1 3-2z" class="p"></path><path d="M278 450c1-1 1-4 1-5l1-1v2c1 1 2 1 2 1l1 1-1 1h1v2l-1 1 1 2h0c-1 1-1 2-1 2 1 3 1 5 2 8v1l1 11v7l-1 1v-2l-1 1v1c-1-1-2-2-2-4 0-1 0-2-1-2v-1c0-2-1-4 0-5h0v-1h-1c-1 1-1 2-1 3v1 2h-3l-1-1c-1 0-1-1-3 0v-1c-1-1-1-2-1-3h-3v-6-2-10h2c1-2 2-3 4-4l1 1v2 1h2c1-1 2-2 2-4z" class="D"></path><path d="M269 454c1-2 2-3 4-4l1 1v2 1c-1 1-1 1-1 2v1l1-1h1v2c0 1-1 2-2 3h-4v-1-1-1-1c0-1 1-1 1-1v-1h-2l1-1zm-2 12c1 0 3-2 4-2s2 2 3 2l-1 1 1 2 2-1c1 1 1 1 1 3 0 0 0 1-1 2h1s0 1 1 2v2h-3l-1-1c-1 0-1-1-3 0v-1c-1-1-1-2-1-3h-3v-6z" class="G"></path><path d="M278 474c0-1 0-2 1-3h1v1h0c-1 1 0 3 0 5v1c1 0 1 1 1 2 0 2 1 3 2 4v-1l1-1v2l1-1 1 3h0c0-1 0-2 1-3v2l1-1h1 0l2 1c0 1 0 3-1 5v7h1 2c1 0 2 0 4-1v2h0c-3 0-5 1-7 1 0 2 0 5 1 7-2 1-3 1-5 1l-1-2v-2-2c-1 0-2-1-3-3h0l-2 1c-1-1 0-1 0-3h-3c-3 0-3 0-6 1v1h-3l-1-17v-5-4h3c0 1 0 2 1 3v1c2-1 2 0 3 0l1 1h3v-2-1z" class="AM"></path><path d="M286 486h0c0-1 0-2 1-3v2c1 1 1 2 2 3-1 1-1 1-3 2l-1 1c0-2 0-3 1-5z" class="x"></path><path d="M278 474c0-1 0-2 1-3h1v1h0c-1 1 0 3 0 5v1c1 0 1 1 1 2 0 2 1 3 2 4v-1l1-1v2l1-1 1 3c-1 2-1 3-1 5s1 4 1 7c-4-4-8-9-9-14l-6-8c2-1 2 0 3 0l1 1h3v-2-1z" class="AK"></path><g class="F"><path d="M278 474c0 1 1 3 0 5 0 2 0 3-1 5l-6-8c2-1 2 0 3 0l1 1h3v-2-1z"></path><path d="M267 476v-4h3c0 1 0 2 1 3 1 3 4 6 4 9 2 1 2 2 2 4 1 1 0 1 1 2s0 0 0 1c1 1 2 3 2 4l1 1h-1-3c-3 0-3 0-6 1v1h-3l-1-17v-5z"></path></g><defs><linearGradient id="Ad" x1="528.696" y1="620.856" x2="684.847" y2="571.013" xlink:href="#B"><stop offset="0" stop-color="#141a19"></stop><stop offset="1" stop-color="#5f2828"></stop></linearGradient></defs><path fill="url(#Ad)" d="M611 566c3-1 7-3 9-5l1-1 1-1c3-3 7-5 10-8l6-6c4-5 7-11 12-16 0 0 0 1 1 1l1 1c4-2 7-6 11-10v1l-2 1 1 1c1-1 2-2 2-4h1v1h1c0 1-2 2-3 3v1 1c-1 1-2 2-3 4h0c2-1 3-3 4-4l7-5 2 1c-1 2-1 5-2 7-1 0-1 1-1 2l-1 2-2 5c-1 1-2 3-3 5-1 0-1 1-1 2-1 3-4 6-5 9-2 2-3 4-5 6h0-1 0c-3 5-5 12-9 16-2 1-3 3-5 4-1 0-1 1-2 1v2h1c2-2 5-5 8-7l-6 7 1 1c-1 2-2 2-3 3l-3 2-4 2v1h0l1 3c-1 1-2 2-4 2l-4 4-3 3v-1l-1 1c-2 2 1-1-2 1-1 1-1 1-1 2l-3 3v1c-1 0-2 1-3 2h0-1 0c0 2-1 3-3 4h0v-1c-1 0-1 1-2 1-1 1-1 2-2 2 1-3 1-7 3-11h-2c0 2-1 3-2 4-1 4-2 8-4 11-2 6-6 13-10 18 0 1-2 3-2 3l-2-1c-2 2-4 5-6 7 0 1-1 2-2 3h-1l-1-1 1-1c-2 0-3 1-4 2 0 1-1 1-2 2-1 0-1 0-1 1l-1 1c-2 1-4 3-7 4l-3 3-1 1-8 9h-1v-1-1c0-3 3-10 4-13h1l1-1c-1-1-1-1-2-1v-1c1-1 1-2 2-2 0-2 1-3 1-4l2-4 1-4 2-2 1-2c1-1 1-2 2-3l1-1v-1c1 0 1-1 2-2l1-2c0-1 1-1 1-2 1 0 2-1 2-2l1-1 3-3c1-2 2-3 3-5v-2l1-1c1-2 2-5 4-7 0-1 1-2 2-3l1-2 4-5v-1c1-2 2-3 3-4v-1c1-1 2-2 2-3h1c1-1 1-1 1-2h0c-2 1-2 0-3 1-1 0-3 3-4 3-2 1-2 0-3 2h-1c2-2 3-5 5-7 2-3 6-5 8-8h1l1-1 1 1c1-2 2-2 4-3l1-1c2-2 4-5 7-6z"></path><path d="M640 565h1l1 2h-1l-1-2zm4-6h2c0 2-1 2-2 3v-2h1l-1-1z" class="H"></path><path d="M650 533h1l1 1c0 1-1 1-2 2h0-1v-2l1-1z" class="U"></path><path d="M600 612c0-5 2-8 4-12h0 1l-1 1v2c0 1 0 1 1 2l-2 3c0 2-1 3-2 4h-1z" class="AW"></path><path d="M659 532c0 1-2 2-3 3v1c-2 1-3 3-5 4h-1-1l8-11 1-1c2-1 3-2 5-3v1c-1 1-2 2-3 4h0c0 1-1 1-1 2z" class="AJ"></path><path d="M635 567l3-4h1c1 1 0 1 1 1 0 2 0 2-1 4-1 0-1 1-1 2-3 2-6 5-9 8-1 1-2 1-3 2l-2-1-1-1 1-2 2-2v-1l2 2h1c-1 1-3 3-2 4 3-4 5-8 8-12z" class="H"></path><path d="M600 612h1c-1 4-2 8-4 11-2 6-6 13-10 18 0 1-2 3-2 3l-2-1s2-3 3-4c4-6 7-11 10-18 1-3 3-6 4-9z" class="AS"></path><path d="M660 530c2-1 3-3 4-4l7-5 2 1c-1 2-1 5-2 7-1 0-1 1-1 2l-1 2v-4c-2 1-5 8-6 8v-1c2-4 5-8 7-11h-1c-2 0-4 2-6 4 1 0 0 1 0 2l2-2 1 1-4 4-8 6h-1c1-1 2-2 2-3 2-1 3-2 4-3v-1h1l-1-1c0-1 1-1 1-2z" class="c"></path><path d="M622 572l1 1c-2 4-5 8-7 13-2 3-4 7-7 10h-1l1-1h-1c0-3 3-5 4-8 1-1 2-3 3-4 0-1 0-1 1-2v-1h1c1-2-1 0 1-2h0v-1l1-1v-1c1 0 1-1 2-2l1-1z" class="AH"></path><path d="M663 537c1 0 4-7 6-8v4l-2 5c-1 1-2 3-3 5-1 0-1 1-1 2-1 3-4 6-5 9-2 2-3 4-5 6h0-1l11-23z" class="AC"></path><path d="M625 584l8-8c2-2 4-5 7-7l1 1c0 1 0 1-1 2 0 1 1 1 0 2l-1 1v-2l-1 1c-1 2 0 4-2 6h-1v1h1 0l1-2 1 1c-1 0-1 1-2 1-1 1-2 1-3 2h-1l-2 1-2 2c-3 1-6 3-8 6l-1-1c2-1 3-2 4-3v-1c1-1 1-2 2-3h0z" class="H"></path><path d="M603 573l2 1 1 1c-1 1-3 3-5 4 0 1 1 2 0 2 0 1-2 2-2 3h1c1-1 2-2 3-2 1-1 1 0 1-1 2-1 5-3 7-3h1c-3 2-6 4-8 6-1 1-2 2-4 2s-3 2-5 3c-7 5-10 10-15 18-1 1-2 3-2 4-1 1-1 1-1 2h-1c0 1-1 2-1 3v-2l1-1c1-2 2-5 4-7 0-1 1-2 2-3l1-2 4-5v-1c1-2 2-3 3-4v-1c1-1 2-2 2-3h1c1-1 1-1 1-2h0c-2 1-2 0-3 1-1 0-3 3-4 3-2 1-2 0-3 2h-1c2-2 3-5 5-7 2-3 6-5 8-8h1l1-1 1 1c1-2 2-2 4-3z" class="AI"></path><path d="M603 573l2 1c-1 2-4 3-6 5-2 0-5 3-7 4 0 1 0 1-1 1l-3 3v-1c0-1 5-5 6-6 2-1 3-3 3-4l1-1 1 1c1-2 2-2 4-3z" class="AW"></path><path d="M605 574l1 1c-1 1-3 3-5 4 0 1 1 2 0 2 0 1-2 2-2 3h1c1-1 2-2 3-2 1-1 1 0 1-1 2-1 5-3 7-3l-15 9h-1c0-1 1-2 1-3v-1h-2c-1 1-2 1-3 1 1 0 1 0 1-1 2-1 5-4 7-4 2-2 5-3 6-5z" class="AC"></path><path d="M605 574l1 1c-1 1-3 3-5 4l-3 3c0-1 1-3 1-3 2-2 5-3 6-5zm31 7v2h1c2-2 5-5 8-7l-6 7 1 1c-1 2-2 2-3 3l-3 2-4 2v1h0l1 3c-1 1-2 2-4 2l-4 4-3 3v-1l-1 1c-2 2 1-1-2 1-1 1-1 1-1 2l-3 3v1c-1 0-2 1-3 2h0-1 0c0 2-1 3-3 4h0v-1c-1 0-1 1-2 1-1 1-1 2-2 2 1-3 1-7 3-11h-2l2-3 1-1c1-4 5-5 7-8 1-2 1-2 2-3 1 0 1-1 2-1 1-1 1-1 1-2l1-1h0c1-1 2-2 2-3h0l1-1c1-1 1-2 1-3 1-2 2-2 3-2-1 1-2 2-1 4h0c-1 1-1 2-2 3v1c-1 1-2 2-4 3l1 1c2-3 5-5 8-6l2-2 2-1h1c1-1 2-1 3-2z" class="U"></path><path d="M615 593l3-1c-1 2-2 2-3 4s-4 4-5 5l-1 1c-1 2-3 5-4 6h-2l2-3 1-1c1-4 5-5 7-8 1-2 1-2 2-3z" class="AH"></path><path d="M621 595c-1 0-1 0-3 1h-1c3-3 6-6 9-8l1-1h2v2 1l-8 5z" class="H"></path><path d="M639 583l1 1c-1 2-2 2-3 3l-3 2-4 2c-3 0-8 6-10 8-4 3-8 9-11 13-1 1-2 3-3 4-1 0-1 1-2 1 2-5 5-10 9-14 2-2 6-5 8-8l8-5c3-2 7-4 10-7z" class="E"></path><path d="M606 616c1-1 2-3 3-4 3-4 7-10 11-13 2-2 7-8 10-8v1h0l1 3c-1 1-2 2-4 2l-4 4-3 3v-1l-1 1c-2 2 1-1-2 1-1 1-1 1-1 2l-3 3v1c-1 0-2 1-3 2h0-1 0c0 2-1 3-3 4h0v-1z" class="AH"></path><path d="M630 592l1 3c-1 1-2 2-4 2l-4 4c-2 1-4 2-5 3h0-1c5-3 9-8 13-12z" class="AJ"></path><path d="M548 517c-1-2-1-2 0-3 2 0 2 3 3 3l1-1 3 5v1h-2v2l-1 1v1c0 2 0 3-1 5-1 1-1 2-1 4l-3 5c0 2-1 3-1 4 1 2 0 4 0 6l5-4h0c3-1 5-2 7-1 1 0 2 0 2 1 1 3 0 6-1 8l2-2 1-1c1 0 1 1 1 2 2 3 3 6 3 9v5c0 2-1 3-1 5v6 5l-2 1c-1 3-2 5-4 7-1 2-2 5-4 7h1l1 1c-1 1-2 2-3 2s-1 0-1 1c-1 1-3 3-5 3h0l-1-1-1 1h-1l2-4-2 2-1 2v-2c-1 0-1 1-1 2l-1 1c0 1 0 2-1 3-3 4-5 8-9 11-1 1-1 1-2 1-3 3-7 5-11 7l-6 3c-2 0-4 2-6 3 1-2 1-2 3-3v-1h-1l-1-1-1-1 1-1h-2c0-2 2-2 3-4 0-1 0-1 1-2l3-3-1-1h-2l3-4v-1-1l-2-1c-4-4-11-7-16-9l-1-1-2-1h1l-1-1v-1c-2-1-2-2-3-3l-3-3 1-1c0-2-2-3-3-4v-2c-2-6-6-9-5-16l-2-1c-1 1-3 2-4 3-2-1-3-1-4-2h0-1l3-2c1 0 2 0 3-1-1-1-1-1-2-1-1-1-3-1-4-2h0c1-1 1 0 2-1 1 0 2-1 2-1-1-1-3-2-5-3-2-2-6-5-8-8v-2c1 0 0 0 1-1l-6-7h1 1l1-1 1-1c0-1 1-2 2-3l4-5 3-4 4 2 1 2c1-1 1-1 2-1h1v-1 1c1 0 1 0 2 1h2v-1h1l1 1 2 3h0v-2h1v1 1c0 1 0 1-1 1l1 3v2c2 4 4 8 5 12l2 1v2h1v1c0 1 0 1 1 1l1-1c0 1 1 1 1 3s-1 5 1 7c1-1 2-1 2-2v-1c1 1 1 3 1 4h2v1c0 1 1 2 1 3 1 2 1 3 1 5v4c2-2 3-2 2-4v-2l1-1h0v1s1 1 1 2 1 2 0 3c0 1 0 2 1 3v3c0 1 1 2 1 3 0 2 0 6 2 8l1-1 1-12c0-7 1-13 2-19v-7l1-1c1-1 1-2 1-4h1l1-1v-5l1 2h0l2-9h1c0 1 1 2 0 3 1 0 1-1 2-1v1c1 0 1-1 1-1h1v2 1h0c2-1 3-4 3-5v-2-1l1-1v1c0 3-1 6-2 9h1l4-9c1-3 2-6 4-8 1-1 1-2 2-2l1 3c2-2 2-2 2-6 1-1 2-1 2-3l1-1 1 1 1-1z" class="p"></path><path d="M537 555c1 0 2 0 3-1 0 2 0 3-1 4h-1c-1-1-1-2-1-3z" class="x"></path><path d="M537 552c1 1 1 0 2 1l1-1c0-1 1-1 2-2 0 1 0 2-1 2 0 1 0 2-1 2-1 1-2 1-3 1v-3z" class="I"></path><path d="M473 548c3 2 6 3 8 6l1 1 1-1c1 1 2 2 2 3h-1-1v1c-2-1-3-3-4-4-3-1-4-4-6-6z" class="T"></path><path d="M478 537l1-2v-5h0c1 0 2 0 2-1l2 3h0v-2h1v1 1c0 1 0 1-1 1l1 3-3-3v2c0 1 0 1 1 2h0v3h2l-1 1-1-1h0c0 2 1 4 1 6-1-1-1-2-1-3s-1-2-2-3h-1l-1-3z" class="Z"></path><path d="M473 533c1-1 1-1 3-2 0 0 0 1 1 1 0 2 1 3 1 5l1 3h1l1 4 1 1c0 2 1 2 0 4 0-1-1-2-1-3-1-1-2-1-2-3 0-1 0-1-1-3h-1l-1-1h0c-1 0-1 0-1-1-1-2-1-3-2-5z" class="V"></path><path d="M471 527l1 2c1-1 1-1 2-1h1v-1 1c1 0 1 0 2 1h2v-1h1l1 1c0 1-1 1-2 1h0v5l-1 2c0-2-1-3-1-5-1 0-1-1-1-1-2 1-2 1-3 2-1-1-2-2-3-4l1-2z" class="u"></path><path d="M460 546c3 3 6 7 10 9 1 0 3 2 4 2 2 1 3 2 5 3s4 1 6 2v1c-1 1-2 1-4 2l-2 3-2-1c-1 1-3 2-4 3-2-1-3-1-4-2h0-1l3-2c1 0 2 0 3-1-1-1-1-1-2-1-1-1-3-1-4-2h0c1-1 1 0 2-1 1 0 2-1 2-1-1-1-3-2-5-3-2-2-6-5-8-8v-2c1 0 0 0 1-1z" class="AA"></path><path d="M472 560l7 3c-3 2-6 3-10 5h0-1l3-2c1 0 2 0 3-1-1-1-1-1-2-1-1-1-3-1-4-2h0c1-1 1 0 2-1 1 0 2-1 2-1z" class="E"></path><path d="M481 535v-2l3 3v2c2 4 4 8 5 12l2 1v2h1v1c0 1 0 1 1 1l1-1c0 1 1 1 1 3s-1 5 1 7c1-1 2-1 2-2v-1c1 1 1 3 1 4h2v1c0 1 1 2 1 3 1 2 1 3 1 5v4c1 2 1 3 2 5l2-1 1 1c-1 1-1 1-2 1-2-1-3-1-3-3h0 0l-1 1v1c-2-2-2-4-4-7v-2c-1-1-1-2-2-3s-1-3-1-4 0-3-1-4-1-2-2-3c0-1-1-2-1-3s-1-2-2-3-1-2-2-4c0-2-1-3-2-5 1-1 1-1 0-2-1-3-2-6-3-8h-1z" class="I"></path><path d="M561 552c1 3-3 8-5 11-3 4-7 9-9 14 1 1 1 1 0 3 0 1-1 2-2 4l-1 2-4 4 1 2c-3 2-6 5-9 7-3 0-7 3-8 2h-1c-1 0-1 1-2 1l-2-1c4-2 8-3 11-6 3-2 5-5 7-8 9-10 17-21 22-33l2-2z" class="L"></path><path d="M524 601c3-2 6-3 9-5s5-4 7-6l1 2c-3 2-6 5-9 7-3 0-7 3-8 2z" class="AA"></path><defs><linearGradient id="Ae" x1="522.355" y1="595.39" x2="489.869" y2="601.476" xlink:href="#B"><stop offset="0" stop-color="#8a7475"></stop><stop offset="1" stop-color="#8c8482"></stop></linearGradient></defs><path fill="url(#Ae)" d="M484 584c7 9 15 16 26 18h0c3 1 6 0 9-1l2 1c1 0 1-1 2-1h1c1 1 5-2 8-2-7 4-13 7-19 13v-1l-2-1c-4-4-11-7-16-9l-1-1-2-1h1l-1-1v-1c-2-1-2-2-3-3l-3-3 1-1c0-2-2-3-3-4v-2z"></path><path d="M464 529l3-4 4 2-1 2c1 2 2 3 3 4 1 2 1 3 2 5 0 1 0 1 1 1h0l1 1c0 2 4 11 7 12 1 1 0 1 2 1v-1c1 2 1 3 2 5l-1 1-2-1c0-1-1-2-2-3l-1 1-1-1c-2-3-5-4-8-6l-1-2-3-3v-1l-2-2c0-2-1-3-3-4-1 0-2 0-3-1l-1-1 4-5z" class="AQ"></path><path d="M464 529l3-4 4 2-1 2c1 2 2 3 3 4 1 2 1 3 2 5 0 1 0 1 1 1l1 3c0 1 1 3 1 4h0c-3-1-6-4-7-6 0-1-1-2-1-3v-4c-1-1-1-1-1-2v-1h-2v1l-3-2z" class="j"></path><path d="M548 517c-1-2-1-2 0-3 2 0 2 3 3 3l1-1 3 5v1h-2v2l-1 1v1c0 2 0 3-1 5-1 1-1 2-1 4l-3 5c0 2-1 3-1 4-2 2-4 4-4 6-1 1-2 1-2 2l-1 1c-1-1-1 0-2-1l1-2h-1l-5 16c-2 0-1-1-3 0v1h-1v-2c-1 1-1 2-1 3v1c0-2 0-2-1-2v-1l1-1v-1c1-2 1-4 2-5h0v5h1c1 0 0 0 1 1h0 0c1-2 1-2 2-3v-3l2-7c1-1 1 0 1-1 1-1 0-2 1-2v-1-1h0c-1 0-1-1-1-1h-1c-1 1-1 1-2 0-2 3-1 6-3 9h0-2c-2 3-2 6-3 9-1 2-3 5-3 7-1 2-1 3-2 4-1 0-1 0-2-1v-2l1-1v-3s-2 1-2 2c-1 2 0 4-1 6h-2v1c0 1-1 2-1 4 0-7 1-13 2-19v-7l1-1c1-1 1-2 1-4h1l1-1v-5l1 2h0l2-9h1c0 1 1 2 0 3 1 0 1-1 2-1v1c1 0 1-1 1-1h1v2 1h0c2-1 3-4 3-5v-2-1l1-1v1c0 3-1 6-2 9h1l4-9c1-3 2-6 4-8 1-1 1-2 2-2l1 3c2-2 2-2 2-6 1-1 2-1 2-3l1-1 1 1 1-1z" class="w"></path><path d="M523 537c0 1 1 2 0 3-1 5-1 10-3 15l-1-1c0-2 0-2-1-4l1-1v-5l1 2h0l2-9h1zm4 5h0c2-1 3-4 3-5v-2-1l1-1v1c0 3-1 6-2 9h1 0c0 3-1 6-2 8-1 1-2 3-2 4v1h-1c0-2 0-4 1-6v-1h-1-1c0-1 1-2 1-3l2-4z" class="j"></path><path d="M548 517c-1-2-1-2 0-3 2 0 2 3 3 3l1-1 3 5v1h-2v2l-1 1v1c0 2 0 3-1 5-1 1-1 2-1 4l-3 5c-2 2-3 4-5 5h-1c0-1 1-4 1-5l3-3-1-1-6 6v1h-1c0-1-1-1-1-2l1-1c0-1 0-2 1-3 0-3 1-4 2-6v-1l1-3c2-2 2-2 2-6 1-1 2-1 2-3l1-1 1 1 1-1z" class="Z"></path><path d="M537 540c0-1 0-2 1-3 0-3 1-4 2-6v1 2h1c1 0 1-1 2-2l1 2-2 2c-2 1-2 3-4 4h-1z" class="u"></path><path d="M548 517c-1-2-1-2 0-3 2 0 2 3 3 3l1-1 3 5v1h-2-1c-2 2 0 4-1 6-1-1-1-2-1-2 0-3 0-8-1-10l-1 1h0z" class="W"></path><path d="M548 517h0c0 3 0 7 1 10v4l-1 1c-1 0-1 0 0-1v-3c-2 1-2 3-3 5l-1 1-1-2c-1 1-1 2-2 2h-1v-2-1-1l1-3c2-2 2-2 2-6 1-1 2-1 2-3l1-1 1 1 1-1z" class="z"></path><path d="M540 530c1 1 1 1 3 1l1-3 1 1c0 2-1 3 0 4l-1 1-1-2c-1 1-1 2-2 2h-1v-2-1-1z" class="W"></path><path d="M561 552l1-1c1 0 1 1 1 2 2 3 3 6 3 9v5c0 2-1 3-1 5v6 5l-2 1c-1 3-2 5-4 7-1 2-2 5-4 7h1l1 1c-1 1-2 2-3 2s-1 0-1 1c-1 1-3 3-5 3h0l-1-1-1 1h-1l2-4-2 2-1 2v-2c-1 0-1 1-1 2l-1 1c0 1 0 2-1 3-3 4-5 8-9 11-1 1-1 1-2 1-3 3-7 5-11 7l-6 3c-2 0-4 2-6 3 1-2 1-2 3-3v-1h-1l-1-1-1-1 1-1h-2c0-2 2-2 3-4 0-1 0-1 1-2l3-3-1-1h-2l3-4v-1c6-6 12-9 19-13 3-2 6-5 9-7l-1-2 4-4 1-2c1-2 2-3 2-4 1-2 1-2 0-3 2-5 6-10 9-14 2-3 6-8 5-11z" class="AS"></path><path d="M562 572v-3-2h0v-1c1-1 1-2 1-3l1-1 1 2c-1 3-1 4-2 6l-1 2z" class="AA"></path><path d="M544 586l1-2c1-2 2-3 2-4 1-2 1-2 0-3 2-5 6-10 9-14 0 5-3 10-5 15l-1 1c0 1 0 1-1 2 0 2-1 3-2 4-2 1-2 3-3 4l-1 1 1-4z" class="AX"></path><defs><linearGradient id="Af" x1="549.324" y1="585.654" x2="562.751" y2="587.355" xlink:href="#B"><stop offset="0" stop-color="#5d4646"></stop><stop offset="1" stop-color="#785c59"></stop></linearGradient></defs><path fill="url(#Af)" d="M565 564v8 6 5l-2 1c-1 3-2 5-4 7-1 2-2 5-4 7h1l1 1c-1 1-2 2-3 2s-1 0-1 1c-1 1-3 3-5 3h0l-1-1-1 1h-1l2-4 4-6c1-1 1-3 2-4s1-1 1-2h1l1-2c0-2 1-3 2-4v1l2-2c0-2 2-6 2-7h-1 0c0-1 1-2 1-3l1-2c1-2 1-3 2-6z"></path><path d="M565 578v5l-2 1c0-3 1-4 2-6z" class="AV"></path><path d="M541 592h1l-2 2h0c0 1 0 2-1 3l-3 5c-1 1-1 2-1 3s0 2-1 3l-3 4v1l3-3v1c2-1 3-2 4-4s3-6 5-7c0 1-1 1-1 2v4c-1 1-1 1-1 2v1c-3 4-5 8-9 11-1 1-1 1-2 1-3 3-7 5-11 7l-6 3c-2 0-4 2-6 3 1-2 1-2 3-3v-1h-1l-1-1-1-1 1-1h-2c0-2 2-2 3-4 0-1 0-1 1-2l3-3-1-1h-2l3-4v-1c6-6 12-9 19-13 3-2 6-5 9-7z" class="AG"></path><path d="M534 611c2-1 3-2 4-4s3-6 5-7c0 1-1 1-1 2v4c-1 1-1 1-1 2v1c-3 4-5 8-9 11-1 1-1 1-2 1-3 3-7 5-11 7l-6 3c-2 0-4 2-6 3 1-2 1-2 3-3v-1h-1l-1-1c1-1 3-2 4-2h2c1 0 4-2 5-2 3-1 8-5 11-7 1-2 3-5 4-7z" class="AX"></path><defs><linearGradient id="Ag" x1="529.216" y1="615.608" x2="510.234" y2="613.974" xlink:href="#B"><stop offset="0" stop-color="#5a4241"></stop><stop offset="1" stop-color="#685a57"></stop></linearGradient></defs><path fill="url(#Ag)" d="M541 592h1l-2 2h0c0 1 0 2-1 3l-3 5-11 11c-2 3-5 5-7 7l-4 3h-1l-2 2-1-1v1h-1l-1 2h-2c0-2 2-2 3-4 0-1 0-1 1-2l3-3-1-1h-2l3-4v-1c6-6 12-9 19-13 3-2 6-5 9-7z"></path><path d="M527 605c1-2 2-3 4-4l2-1c3-1 5-4 7-6 0 1 0 2-1 3h0l-9 9h0c-1-1-1-1 0-2-1 1-2 1-2 1h-1z" class="K"></path><path d="M541 592h1l-2 2h0c-2 2-4 5-7 6l-2 1c-2 1-3 2-4 4-2 1-4 2-6 4s-6 8-9 8h-2l3-4v-1c6-6 12-9 19-13 3-2 6-5 9-7z" class="AY"></path></svg>