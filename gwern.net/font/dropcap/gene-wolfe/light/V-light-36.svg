<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="78 48 880 896"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#e9e7e3}.C{fill:#fff}.D{fill:#d3d2cd}.E{fill:#bfbdb9}.F{fill:#f5f3ee}.G{fill:#e2e0da}.H{fill:#a2a09e}.I{fill:#c0bebb}.J{fill:#858482}.K{fill:#b0aeaa}.L{fill:#878784}.M{fill:#f9f9f3}.N{fill:#2a2b2b}.O{fill:#a9a8a5}.P{fill:#646464}.Q{fill:#989796}.R{fill:#464546}</style><path fill="#141618" d="M77 103h396v63c-4-2-8-3-13-4-13-2-29 2-39 10-12 8-20 21-22 35-2 15-1 28 3 42 4 16 11 32 17 47l32 77 69 168 38 95 101-286c11-30 22-59 26-90 3-20 2-40-7-59-7-16-20-27-37-33-13-4-28-5-40-1-5 1-9 3-14 6v-70h273 55 14 6v60c-11-1-21 0-31 1-8 2-15 5-22 8-22 12-40 31-53 53-7 13-13 26-18 39l-35 89-47 131-104 288-50 142h-88-19c0-1-23-56-25-62l-73-175-129-311-37-88c-7-18-15-37-24-54-9-16-21-30-36-42-18-14-44-23-67-20l-1 1 1-60z"></path><path d="M930 104h3c1 2 1 50 1 57-18-1-35 1-50 9-24 11-43 32-56 55-8 13-14 27-20 40l-25 64-41 114-121 336-47 133H470c-2-2-3-6-4-9l-9-20-34-84-77-184-86-207-48-116-17-39c-3-8-6-16-10-24-16-30-40-55-73-65-12-3-22-4-34-3-1-5 0-12 0-17v-40h394v59c-13-5-28-4-40 1-14 5-25 16-30 29-7 16-7 34-3 50 2 9 5 17 8 26l13 34 34 81 104 256 95-269 14-38c9-25 18-51 20-77 2-19 1-37-7-54s-21-29-38-35c-18-7-36-5-53 3v-66h341z" class="M"></path><path fill="#141618" d="M149 124h53 51 154 39c4 0 10-1 14 0v24c-16-2-34 2-46 11-16 10-27 27-30 46-3 16-2 32 2 48 4 14 10 27 15 41l26 62 86 209 33 81c1 2 2 4 3 7 0 1 1 4 2 5v1c0 1 0 2-2 4-1-1-1-7-3-9-2-1-4-2-6-2s-4 1-6 3c-3 3-3 6-3 10 1 5 3 9 6 12 3 2 6 3 9 3 5-1 9-3 12-7 5-6 7-16 10-23l9-27 36-99 53-148 18-53c4-11 9-23 12-35 5-20 9-42 6-63-3-22-14-45-32-59-20-15-43-19-68-15v-27h320v26c-12 0-24 3-36 7-27 12-48 32-63 57-16 25-27 54-38 82l-41 112-124 343-35 99c-6 17-11 35-18 52h-91L295 467l-59-143-24-57c-6-14-12-29-19-43s-17-27-28-38c-20-21-46-35-75-36v-26h59z"></path><path d="M325 207l7-1 1 2c-2 0-4 1-6 1-1 0-1-1-2-1v-1z" class="L"></path><path d="M247 144h2v2 7l-2-2v-7zm204 662c1-1 2-1 3-1v2c0 2 1 2-1 3l-1 1-2-2c0-2 0-2 1-3z" class="C"></path><path d="M399 640c0 5 1 11-1 16l-1-7v-7h1l1-2z" class="N"></path><path d="M466 484h4c1 2 1 2 1 4v1c-2-2-2-3-5-3-2 1-2 2-3 4h-1c-1-2 0-3 0-4 1-1 2-2 4-2z" class="C"></path><path d="M669 519c0 1 0 0 1 1 1 0 1-1 2-2h3c-3 3-7 6-9 9-1-1-1-2-1-3 0-2 3-3 4-5z" class="H"></path><path d="M276 234c-2-4-5-8-9-11 3 1 9 7 11 6l2-2c1 2 0 3-1 5-1 1-2 1-3 2z" class="B"></path><path d="M383 490c2-1 3 0 5 1v1c-1 2-1 2-2 3h-2c-2-1-2-2-3-3 1-2 1-2 2-2zM264 346h3c3 1 6 4 8 6-2 0-3-1-5-2-4-1-5-1-9 1v-1-1c1-2 2-3 3-3zm437-37c1 0 1-1 1 0 2 0 3 1 4 2 1 2 1 4 0 5 0 2-2 3-4 4h-3v-1l3-1c1-1 1-2 1-3 1-1 0-2-1-4 0 0 0-1-1-2h0zM393 412c1-1 2-1 3-1 2 0 4 2 5 3s1 1 1 3c-2-1-4-2-5-2-4 0-7 1-10 3 2-3 4-5 6-6z" class="C"></path><path d="M428 656l1 1v1c0 4 0 5 2 8l2 3h-1c-1-1-3-1-4-2-1-2-2-4-2-6s1-3 2-5zM319 485h3c1 1 2 2 2 3 0 2 0 3-2 4h-1c-1 0-2 0-3-1 0-1-1-2-1-3 1-2 1-2 2-3z" class="B"></path><path d="M504 869h2c4 3 3 8 4 12 0 1 1 3 1 4h0c-4-3-6-12-7-16z" class="C"></path><path d="M348 379c1 0 2 0 3 1s1 1 1 2c0 2-1 3-2 4s-2 0-3 0c-1-2-2-2-2-4 1-1 1-2 3-3z" class="B"></path><path d="M894 139c1 2 1 4 0 6-1 1-3 3-5 4s-4 1-6-1c-1 0-1-1-2-1v-1c2 1 3 2 5 1 2 0 3-1 4-2 1-2 1-3 1-5 2 0 2 0 3-1z" class="R"></path><path d="M695 165c-1-2-1-2 0-3 2-2 6-2 8-2v1c-1 0-1 0-1 1-2 5 0 10 1 14-2-2-6-10-5-12l1-2h0-3c-1 1 0 1 0 2l-1 1h0z" class="N"></path><defs><linearGradient id="A" x1="314.305" y1="203.892" x2="323.895" y2="208.705" xlink:href="#B"><stop offset="0" stop-color="#757372"></stop><stop offset="1" stop-color="#908f90"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M324 211v-1h-1-1-1c-4-1-7-5-10-8 2 1 4 2 7 3 2 1 4 1 6 1l1 1v1c1 0 1 1 2 1l-1 1h0l1 1h-3 0z"></path><path d="M207 210c1 0 2 0 3 1 2 2 4 7 6 10 1 0 2 1 2 2-1 0-1 0-2-1l-2-1c-2-2-3-4-5-7l-1-2c-1-1-5 2-7 1l1-2c1 0 3-1 5-1z" class="C"></path><path d="M128 145h1c2-1 5-1 6-2h1 3v1c1 1 2 3 2 4-2 1-4 1-6 1-2-1-5-3-7-4z" class="G"></path><path d="M755 341l1-2c1-1 1-3 2-4s1-2 2-3c2-3 4-6 7-8-1 1-2 3-3 4-1 4-3 12-6 13 0 1-1 1-2 1l-1-1z" class="D"></path><path d="M672 516c-1 2-2 2-3 3-1 2-4 3-4 5 0 1 0 2 1 3-3 4-5 7-7 11v-1-3-1l2-6c0-2 5-5 6-8l5-3z" class="L"></path><path d="M747 340h3l1 1-1 1c-1 0-2 1-4 2-1 1-1 3-1 5 1 2 4 2 6 4s1 3 2 5c0 1-1 1-1 2-1-1-1-4-2-5-2-2-5-3-7-6-1-1-1-3 0-5 0-2 2-3 4-4zM380 505h1 1l-5 5c-2 3-2 7-2 10v1c-1-2-2-3-2-5-1-2-1-5 0-7 2-3 4-3 7-4z" class="C"></path><path d="M261 244l2-1c1 1 1 1 2 3l-1 1h-2c-2 1-2 1-3 3-1 3 2 7 3 9-2-1-5-3-6-6-1-1-1-3 0-5s3-3 5-4z" class="B"></path><path d="M791 201c1-2 6-1 8-2 5-1 9-3 13-4l1 2c-6 2-12 4-19 6h-2c-1 0-1-1-1-2z" class="K"></path><path d="M473 513h1c2 0 4 0 5 2 2 1 3 3 3 6-1 2-2 3-4 5h0v-3c1-1 1-2 0-4-1-1-2-3-4-3s-3 1-5 2h0-1c1-1 1-2 1-2 1-2 2-3 4-3z" class="C"></path><path d="M699 369c2 0 4 0 5 1 2 1 2 2 2 4-1 2-1 2-3 3l-2 1c-1 0-2 0-3-1s-1-3-1-4c0-2 1-3 2-4z" class="G"></path><path d="M805 219v1c-3 4-8 4-12 8s-3 9-3 14v-1c-1-4-3-9-1-12 3-6 10-8 16-10z" class="B"></path><defs><linearGradient id="C" x1="704.2" y1="382.101" x2="696.25" y2="384.75" xlink:href="#B"><stop offset="0" stop-color="#a5a3a2"></stop><stop offset="1" stop-color="#c3c1ba"></stop></linearGradient></defs><path fill="url(#C)" d="M695 378l1 1c1 2 3 3 5 3s4 0 5-1v1c0 2-1 3-3 4-3 2-7 1-10 0l2-8z"></path><path d="M449 486v-1c1-5 2-10 6-13 1-1 3-2 5-2s4 1 5 3v4h0c-2-1-2-3-4-3s-4 0-5 1c-3 2-4 5-5 8v1l-2 2z" class="C"></path><path d="M557 690h0c-5-1-10-4-14-8l7 2c2 2 5 1 7 2s4 1 6 2h6 1l1-1h3c1-1 2-1 3-1-1 1-1 1-2 1-4 2-8 3-12 2-1-1-2-1-4-1h-1l-1 2z" class="E"></path><path d="M663 416c2 0 4 0 6 1s3 2 3 4l-1 1c-1-1-1-2-2-2-2-1-3-1-5-1l-3 3-1 1v1c0 2 0 3 1 4 0 1 1 2 1 2h-1c-1 0-2-1-3-3-1-1-1-4 0-6 0-2 2-4 5-5z" class="C"></path><path d="M817 206h1c-1 1-1 2-2 3-7 7-18 7-24 13v-1c5-8 18-10 25-15zM670 390c0-8 1-12 5-18-1 5-3 11 0 16 2 4 5 7 8 11-2-1-4-3-5-5l1 2c1 2 1 3 0 4h0l-1-1-2-4-1-1-1-3h0c-1-1-2-1-4-1z" class="B"></path><path d="M695 165h0c1 7 5 13 10 18 3 2 6 4 8 6 3 4 4 8 3 13 0-2-1-4-1-6-3-5-7-8-11-11-5-4-9-14-9-20z" class="I"></path><path d="M300 312v-1c4 0 7-2 11-3 3-1 7 0 11 0 2 0 5 0 8 1h-1c-1 1-2 1-3 0h-7l-3 2c4 1 7 0 10 1h1c1 0 2 0 3 1-8-1-18-3-25 0v1h-1v-1h-3l-1-1z" class="R"></path><path d="M416 713h0c6-1 10 4 14 8 2 2 4 3 5 4-2 0-3-1-5-2-3-2-6-5-10-5-2-1-4 0-6 1 0 0-1 1-2 1v-3c1-2 2-3 4-4zm43 63h0l1 1c-1 2-2 4-1 7 2 5 7 6 12 7 1 1 2 2 2 3h-1l-3-1c-5-1-9-2-12-6-2-2-2-5-2-7 1-2 2-3 4-4zm251-486c1 0 3 0 5 1 3 2 4 6 4 10v-1l-3-4c-1-2-2-3-4-3s-3 1-5 2h0c-1 2-2 2-2 4s2 3 3 4v1h0-3l-3-3c0-2 0-5 1-6 2-3 4-4 7-5z" class="C"></path><path d="M262 219c1-1 1-3 2-3 1 1 0 2 1 4-2 6-5 12-8 17-1-1-1-1 0-1v-1h-1v1c-1-1-1-2-1-3 2-5 5-9 7-14z" class="E"></path><path d="M688 385h0c3 3 8 7 10 11 2 5 1 12-1 16 0 1 0 1-1 2 0-7 1-13-3-19-1-2-3-4-4-6-1-1-2-2-1-4z" class="B"></path><path d="M687 145c4-1 6 1 9 2 5 2 9 6 13 9 2 1 5 3 6 5 0 1 1 1 1 2 2 2 3 3 4 6h-1c-3-4-6-7-9-10-7-6-14-10-23-14z" class="E"></path><path d="M674 466l1 1 2-1s1-1 2-1l4-3-1 2c1 1 2 1 3 1-8 5-17 9-26 11l11-5c-4 0-6 1-10 2l6-3c2 0 4-1 6-2l2-2z" class="D"></path><path d="M458 833c1-1 2-3 3-4l1 1h1c1 0 1 1 2 1 1 2 2 2 4 3 2 0 4-1 6-2v1c0 1 0 2-1 3-2 2-5 3-8 3-3-1-6-2-8-5v-1z" class="L"></path><path d="M228 129h1c3-1 5 0 8 3 1 1 3 3 2 5 0 1 0 2-1 3h-3c1-2 1-2 1-4 0-1-1-2-2-3h-1c-1-1-3-1-4 0-3 0-4 3-5 6-1 0-1 1-2 2v-1c-1-1 0-4 0-6 1-3 3-4 6-5zm434 308h2c2 0 3 1 4 3s1 4 1 6c-1 0-1 0-1-1l-1-1c-1-2-2-3-4-4-2 1-2 1-3 2 0 1 0 2 1 3l1 2c1 1 1 3 0 4-2 3-5 6-9 7 3-3 5-5 7-8-1-1-3-3-3-5-1-2-1-3 0-4 1-2 3-4 5-4zm85-295l3-1c1 0 3 0 4 2 1 1 1 2 1 4v1c-1 0-4-3-5-3-2 0-3 1-4 2-3 3 0 8 1 12v1c-1 2-1 4-2 6v-7c-1-4-5-6-4-11 1-3 4-5 6-6z" class="C"></path><path d="M678 458c1-3 6-5 8-8 0-1 0-1 1-1s3-3 4-4c-1 3-6 7-6 9h1 1c-2 2-4 4-6 5l-2 2v2c-1 1-3 3-5 3l-2 2c-2 1-4 2-6 2 2-3 5-5 8-8 2-1 3-2 4-4z" class="I"></path><path d="M314 177c-4-1-8-1-12 0-3 0-5 2-7 5l-3 3c1-2 1-4 2-5 2-4 6-8 10-9 2-1 5-1 8 1 1 0 1 1 2 3v1 1z" class="B"></path><path d="M321 303l7 2h1c1 1 2 1 3 1h1l5 2h1c1 0 2 1 3 1 2 0 3 1 5 1l3 1h1c1 1 2 1 2 1h1l3 4c-2 0-4-2-6-2-7-3-14-4-21-5-3-1-6-1-8-1v-1l-1-1c1 0 1 0 2-1-1-1-1 0-1-1l-1-1z" class="L"></path><path d="M688 474h0c2 0 2 0 3 1 0 1 1 2 0 4-1 1-1 1-2 1l-2-1c-2-1-4-1-6 0-7 2-10 8-13 14-1 1-1 2-2 3h0c2-7 4-16 11-20 3-2 5-2 9-1 1 0 1 0 2-1zm-314-59c7-1 9 5 14 9 3 2 5 1 8 1h0c-2 2-4 2-7 2-2 0-4-1-5-2-3-2-6-6-8-6h-5c-2 0-3 0-5-1 2-2 6-3 8-3zm95 140h4 8c8 1 9 9 15 14l6 3h0-2c-5 0-9-3-12-6-2-2-3-5-6-7-3-3-9 0-13-4z" class="C"></path><path d="M755 341l-1-1c0-2 0-9 1-11 0-1 2-2 2-2 4-3 8-5 11-8 1-2 2-4 3-5h0c0 3 0 7-3 9l-1 1c-3 2-5 5-7 8-1 1-1 2-2 3s-1 3-2 4l-1 2z" class="I"></path><path d="M660 575h1c2-1 3 0 4 1s1 1 1 3c0 1 0 1-2 2-1 0-2-1-4-1-2 1-3 2-4 3-4 5-3 14-2 20l1 5-2-4c-3-6-3-14-1-21 2-4 4-6 8-8z" class="C"></path><path d="M550 684c12 0 22-1 32-7 4-2 7-5 10-8 0 2-1 3-3 4 3 0 2-2 4 0h0l-4 3h-1l-2 1-7 4c-2 1-3 2-5 2l-5 1c-1 1-3 1-4 2h5 1c-3 2-6 0-8 2-2-1-4-1-6-2s-5 0-7-2z" class="B"></path><path d="M281 360c3 0 5 0 7 2 2 1 2 3 3 6h-1c-1-1-2-2-4-3-1-2-4-2-6-2-3 1-4 2-6 4 0 3 3 7 4 10l1 1c-1 4-3 7-5 11l1-4c2-5 1-7-2-11-1-1-2-2-2-4-1-1 0-3 1-5 2-3 5-5 9-5zm62-228c4 0 8 0 12 1 8 2 14 5 19 12-6-4-12-7-19-9-8-1-17-2-25 1-2 1-6 4-8 4 2-2 5-3 7-5 4-2 9-3 14-4zm42 178h0c4 0 7 0 10 3 5 4 6 10 7 17-3-6-4-11-9-15h-1c-1 0-3-1-4 0-2 0-3 1-4 2s-1 2-1 3c1 3 5 5 7 6h-3c-4-1-6-3-7-6-1-1-1-3-1-5 1-2 4-4 6-5zm-132 11c5 0 8 0 11 2 5 4 6 8 6 13-1-2-2-5-4-7-3-3-6-4-9-4h-1c-2 1-3 2-4 4-1 1-1 3 0 4s1 2 3 2c1 1 2 1 3 2-2 0-4 0-6-1-2 0-4-2-4-4-1-1-1-4-1-6 1-3 4-4 6-5zm225 519l6 3c12 9 7 23 8 35v1c1 3 2 5 3 7h-1c-8-7-5-23-6-32-1-6-5-10-10-14z" class="C"></path><path d="M380 161v-1c1-3 0-8 0-11 1 2 2 4 4 6h0c0 1 1 1 1 2h2v-1c1-1 2-2 3-4h4l1 2h0l-1 2h-1c-5 6-8 12-10 20l-3-15z" class="E"></path><path d="M675 537h1c1 1 2 2 3 4 2 6-6 7-8 12-2 4 5 10 7 15-1 4-2 8-5 12h-1l1-1c1-3 4-9 2-13-2-2-4-3-6-5-1-2-2-3-2-5-2-9 6-9 9-14 0-2 0-3-1-5z" class="C"></path><path d="M281 225h1 2c3-4 3-9 4-14 0-3 0-7 2-9h2c-1 13-1 24-12 33-4 3-6 4-8 8h-1c0-3 4-7 5-9 1-1 2-1 3-2 1-2 2-3 1-5l1-2z" class="E"></path><path d="M395 154c3-2 6-4 10-5 6-3 11-2 17-2-13 3-26 10-33 22-2 2-2 6-4 8 1-5 2-10 5-14 2-2 3-4 4-7l1-2zm191 572l1 1c3 5 10 4 13 9 4 4 2 9-1 13-1 1-2 3-3 5 0 4 5 8 7 10l1 1h-1c-3-2-6-4-8-7-1-3-2-7 0-10 0-2 2-4 2-6 2-5-8-7-10-11-1-2-1-3-1-5z" class="C"></path><path d="M592 617s1 0 2 1h1l-1 1-3 3c-5 6-1 10 2 15 1 3 2 6 2 9 1 7-3 11-7 17l-1-1 3-5c2-2 3-4 3-7 0-8-7-13-9-20-1-3 0-6 2-9 1-2 3-3 6-4z" class="F"></path><defs><linearGradient id="D" x1="412.945" y1="416.421" x2="428.555" y2="430.079" xlink:href="#B"><stop offset="0" stop-color="#aaaca8"></stop><stop offset="1" stop-color="#c9c5c6"></stop></linearGradient></defs><path fill="url(#D)" d="M412 470c1-4 3-8 4-11 4-8 6-18 5-27 0-6 0-12-2-18 0-1-3-8-2-9l2 2c3 5 5 10 6 16 1 2 3 8 3 10-1 2-1 3-2 3l-1 1v-2h-1c0 2-1 5-1 7v3l-1-1-1 1c-1 6-3 12-5 18-1 2-2 5-4 7z"></path><path d="M425 423c1 2 3 8 3 10-1 2-1 3-2 3l-1-13z" class="J"></path><path d="M710 159c3 3 6 6 9 10l7 14-1 1c1 4 2 8 2 12h-1c-2-8-8-19-14-25-2-3-6-6-8-9l1-1c2 1 3 1 5 2v-4z" class="C"></path><path d="M710 159c3 3 6 6 9 10l7 14-1 1c-4-8-8-15-15-21v-4z" class="N"></path><path d="M350 199c1-1 3-2 4-3 2 0 4-1 5-1-2 3-6 6-8 10-1 1-2 1-2 2l-6 3c-1 0-1 1-2 1-1-1-2-1-3 0h-1l-5 1h-10c-4-1-7-2-10-5h1c3 2 6 3 11 4h0 3l-1-1h0l1-1c2 0 4-1 6-1l-1-2c6-1 10-3 15-7h3z" class="H"></path><path d="M347 199h3c-1 2-3 3-5 5-1 0-3 1-4 2-1 0-2 1-3 1l-5 1-1-2c6-1 10-3 15-7z" class="J"></path><path d="M422 616h5c3 0 6 2 8 4 1 2 2 4 2 6s-1 3-2 4c-2 2-4 2-6 1h0c2-1 3-1 5-3 0-1 1-2 0-3 0-2-2-4-4-4-3-2-6-2-9-1-6 2-10 8-13 13-4 10-2 18-2 29l-1-1c0-2-1-4-1-6-1-9-1-18 3-26 3-6 8-11 15-13zm-93-93h0c4-1 9 1 13 3 9 6 12 18 10 28-1 5-2 10-2 15-1 4 0 8 1 12v1h0c-2-3-3-6-3-9l-1-1c-2-13 6-27-2-39-3-4-9-8-14-9h-2v-1z" class="C"></path><defs><linearGradient id="E" x1="252.89" y1="268.3" x2="249.36" y2="234.992" xlink:href="#B"><stop offset="0" stop-color="#7d7c7c"></stop><stop offset="1" stop-color="#b7b5b3"></stop></linearGradient></defs><path fill="url(#E)" d="M255 233c0 1 0 2 1 3v-1h1v1c-1 0-1 0 0 1-2 4-4 7-5 11-2 7-1 15 1 22l2 6c-4-5-8-10-9-16h0c0-3-1-8-2-12 1 0 1 0 2-1s2-3 2-5h0c0-2 1-2 3-3 1-2 1-4 4-6z"></path><path d="M230 289c2 0 4 3 5 4 4 4 9 4 14 7 3 1 5 4 8 5 6 3 12 3 17 6 4 2 6 5 9 7l-6-2h-1l5 4c-4-1-7-3-11-4-4-2-10-3-14-5s-6-6-10-9c-2-2-4-2-6-3-4-2-8-6-10-10z" class="C"></path><path d="M812 195l9-6c6-4 11-9 15-14 7-7 12-16 18-24 3-3 6-5 8-7 3-2 5-5 8-4-12 10-21 25-31 37-7 8-16 15-26 20l-1-2z" class="O"></path><path d="M380 161c-2-5-4-10-5-15 0-5 2-10 5-14l2 2c0 2 1 4 2 6 4 5 8 6 10 12h-4c-1 2-2 3-3 4v1h-2c0-1-1-1-1-2h0c-2-2-3-4-4-6 0 3 1 8 0 11v1z" class="F"></path><path d="M379 285h1c1 1 2 1 3 3 2 4-1 6-4 9 0 0-1 1-1 2-1 1 1 4 0 6-1 0-2 1-3 1-1 4 0 6 1 10-1-1-3-4-4-5s-2-3-2-5c1-1 1-1 0-2l-2 3c-1 3-1 6-2 8l-1 1c-1 0-2 0-2-1-1-2-3-4-2-6 1-11 13-12 20-18 0-2-1-4-2-6z" class="M"></path><path d="M515 721c2 0 5 0 7 1 7 2 10 7 13 13 2 3 4 6 7 8 3 1 6 1 8 0 2 0 3-1 4-2h-2c-1 0-2 0-4-1-1 0-3-2-3-3 0-2 0-4 1-5 1-2 3-2 5-2h0c2 0 3 1 4 2 2 3 3 6 2 8 0 3-1 4-3 6-3 1-6 2-9 1-9-2-12-10-17-17-2-4-5-7-10-8-1 0-2 0-3-1zM375 366h2v1l-3 1c-4 2-6 6-7 11-2 7-1 14 5 19 2 3 5 5 7 8-1 0-3-1-4-1l-6-3c-4-3-10-6-11-11-1-6 1-12 3-16 4-5 8-8 14-9z" class="F"></path><path d="M670 390c2 0 3 0 4 1h0l1 3 1 1 2 4 1 1h0c1-1 1-2 0-4l-1-2c1 2 3 4 5 5l1 1c3 5 5 10 4 16-1 8-7 14-13 18 4-6 8-14 5-22-1-4-3-7-4-10-3-4-5-7-6-12zM429 639c2-1 4-1 6 0h0c-5 1-8 3-11 6-5 6-6 12-5 18 1 12 9 19 17 26h0c-2 1-3 0-4 0-7-4-15-11-18-19-2-6-2-14 1-20s8-9 14-11z" class="C"></path><defs><linearGradient id="F" x1="340.011" y1="174.874" x2="363.486" y2="185.081" xlink:href="#B"><stop offset="0" stop-color="#666567"></stop><stop offset="1" stop-color="#8f8e8d"></stop></linearGradient></defs><path fill="url(#F)" d="M348 169h-4-1l-1-1 3-3c2 0 4 1 5 1 4 0 8 1 11 4 1 2 2 4 3 7l1 2v2c-1 3-1 4-2 7s-2 4-4 7c-1 0-3 1-5 1-1 1-3 2-4 3h-3c2-1 5-3 6-5v-1c0-1 1-2 2-3 1-2 1-7 1-9 0-1-1-2-1-2v-4c-2-4-3-5-7-6z"></path><path d="M416 737h0c1 0 3 1 4 1 9 1 15 0 21 7 9 9 10 20 7 31 0 4-2 9-2 13s2 7 5 10v1h-1c-1-1-2-2-3-4-5-6-3-14-2-21 1-4 1-9 1-13-2-9-9-16-17-19-4-1-11-2-13-5v-1zM208 228c13 7 15 21 21 33 8 16 19 26 34 35-7-2-14-5-20-9-10-7-16-19-21-30-2-7-4-15-7-21-2-3-5-6-7-8z" class="C"></path><defs><linearGradient id="G" x1="730.841" y1="176.204" x2="715.225" y2="172.048" xlink:href="#B"><stop offset="0" stop-color="#8f8e8e"></stop><stop offset="1" stop-color="#aaa"></stop></linearGradient></defs><path fill="url(#G)" d="M711 153l-3-4h1l7 7c1 1 3 2 3 4 1 0 1 1 2 1 3 2 5 8 7 12 2 5 4 10 5 15v4l-1 1c-1 1 0 4-1 5 0 3-1 5-2 7l-3-9h1c0-4-1-8-2-12l1-1-7-14h1c-1-3-2-4-4-6 0-1-1-1-1-2-1-2-4-4-6-5 1-1 2-2 2-3z"></path><path d="M711 153c2 1 3 2 4 4 1 1 1 2 2 4v1l-2-1c-1-2-4-4-6-5 1-1 2-2 2-3z" class="D"></path><path d="M726 183c0 1 1 2 1 2 0 1 1 2 2 4s1 4 2 5h0c0-4-1-7-2-10l-3-10-3-6v-1c2 2 3 4 4 6h1c2 5 4 10 5 15v4l-1 1c-1 1 0 4-1 5 0 3-1 5-2 7l-3-9h1c0-4-1-8-2-12l1-1z" class="P"></path><path d="M337 495c5 0 9 1 13 4 8 7 13 19 14 29 2 16 0 33-4 49 0 3-1 7-2 9-1-1 1-9 1-11l3-18c1-16 2-33-8-47-3-5-8-9-13-10-3 0-7 0-9 2-2 1-3 3-3 5s1 5 2 6c2 2 5 2 8 2h0c-3 1-5 1-7 0s-5-4-6-6-1-5 1-8 6-5 10-6zm214 224h1c6 1 11 4 15 8 4 5 7 13 7 20 1 6 0 12 2 19 4 12 14 20 13 34-1-1-1-3-1-4-2-12-13-20-18-31-3-11-1-22-5-32a19.81 19.81 0 0 0-11-11v-1c-3-1-6 1-8 0 2-1 3-2 5-2z" class="C"></path><path d="M281 225c0-6-7-12-5-18s11-12 16-14c3-2 7-3 10-1 4 1 7 4 9 8-1 2 0 6-1 8-1-2-1-5-2-6-3-4-7 0-10 0h-5-1-2c-2 2-2 6-2 9-1 5-1 10-4 14h-2-1z" class="M"></path><path d="M720 367c6 6 8 17 9 25 0 21-11 44-25 59-6 6-12 11-19 14-1 0-2 0-3-1l1-2c1-1 2-1 2-2l5-3c1 0 1-1 2-1 0-1 1-2 2-2 2-1 3-3 4-4 1 0 1-1 2-1v-1l2-1c1-2 2-3 3-4l3-4 2-3 1-1 4-7 1-1 1-1v-1s0-1 1-1c0-3 3-5 3-8h0c1-1 1 0 1-1 0-2 0-2-1-4 1-5 2-11 3-17 0-8 0-17-3-24-1-1-1-2-1-3z" class="C"></path><path d="M311 200v1 7c2 1 4 3 6 4 5 2 14 1 19 0l5-1c1 0 1-1 2-1l6-3c0-1 1-1 2-2l2-1c1 1 1 1 1 3l2-1h1c-2 2-3 3-5 4l-1 1 1 1v-1h2 0v1 1c-1 0-2 1-4 2l-3 2-3 2h-1c-1 1-2 2-4 2-4 1-8 2-13 2h0l-4-1c-9-1-17-4-22-11h3c1 1 2 1 3 2 1 2 3 3 5 4v-1c-1 0-2-1-3-2s-1-2-1-3h-1l1-1h1l1-1 1 1h1l-1-2c1-2 0-6 1-8z" class="D"></path><path d="M300 211h3c1 1 2 1 3 2 1 2 3 3 5 4v-1c-1 0-2-1-3-2s-1-2-1-3h-1l1-1h1l1-1 1 1h1c1 1 2 1 3 2s3 1 4 1c2 1 7 1 8 2-5 0-9 0-13-3l-3-1c3 3 5 5 9 5 1 0 2 0 3 1h4-1-8c5 2 11 3 15 3l3-1v1c-5 2-14 0-19-1-2 0-4-1-6-1l6 3c4 0 7 0 10 2l-4-1c-9-1-17-4-22-11z" class="P"></path><path d="M311 200v1 7c2 1 4 3 6 4 5 2 14 1 19 0l5-1c1 0 1-1 2-1l6-3c0-1 1-1 2-2l2-1c1 1 1 1 1 3l-2 1c-2 1-4 3-7 3-3 2-6 3-10 3v1c3 0 6 0 8-1h0c-2 2-6 2-9 2-1 0-2 0-3 1-2 1-4 1-6 0h1-4c-1-1-2-1-3-1-4 0-6-2-9-5l3 1c4 3 8 3 13 3-1-1-6-1-8-2-1 0-3 0-4-1s-2-1-3-2l-1-2c1-2 0-6 1-8z" class="K"></path><path d="M302 292l1 1c4-4 8-7 14-7 5 0 11 0 15 4 1 2 3 4 4 7 1-2 2-3 3-5 5 3 9 7 12 13 1 2 2 5 3 7h-1s-1 0-2-1h-1l-3-1c-2 0-3-1-5-1-1 0-2-1-3-1h-1l-5-2h-1c-1 0-2 0-3-1h-1l-7-2h-12-1c-1-1-3-2-4-3s-1-2-1-3v-3l-2-2h1z" class="E"></path><path d="M336 297c1-2 2-3 3-5 5 3 9 7 12 13 1 2 2 5 3 7h-1s-1 0-2-1h-1l-3-1c-2 0-3-1-5-1-1 0-2-1-3-1h-1l-5-2h-1c-1 0-2 0-3-1h-1 0l-2-2h-1c-3-1-4-1-6-1-3-1-6-4-8-6v-1c3 3 6 4 10 6-2-2-3-5-4-7 2 2 4 5 6 6h1l6 4c-2-2-5-4-6-6v-1l1 1c1 2 4 5 6 5v-1c-1-1-2-1-3-3h0c2 2 4 4 7 5v-1l-1-1 1-1 3 3v-1c-1-1-2-1-3-3h0c1 0 1 0 1-1v-2z" class="J"></path><path d="M336 297c1-2 2-3 3-5 5 3 9 7 12 13-5-2-7-4-10-8h0c3 4 5 8 9 11v1c-1-1-2-1-3-2h-1c-1-1-3-2-5-3 0 0-1-1-2-1h-1c-1-1-2-1-3-3h0c1 0 1 0 1-1v-2z" class="K"></path><path d="M693 386c-3-2-6-3-8-6-2-4-3-10-2-14 1-6 5-11 9-13 6-4 15-5 22-3h1c8 3 14 8 18 16 8 13 8 31 4 45-5 16-13 29-24 41-4 3-7 6-11 8l12-13c13-18 24-44 20-66-1-6-3-12-9-16-5-4-13-5-19-4-4 1-8 3-10 7-2 3-2 6-1 10h0l-2 8z" class="F"></path><path d="M673 493c3-2 6-4 10-5 7-2 17-4 22-11 2-3 2-6 4-9 1 7-2 13-6 18-2 2-11 10-12 11 0 0 1 0 1 1 3 1 8-1 11 1 0 3-3 4-5 6l-3 2c-6 4-14 7-20 11h-3c-1 1-1 2-2 2-1-1-1 0-1-1 1-1 2-1 3-3l-5 3c-1 3-6 6-6 8l-2 6v1 3 1c-1 2-2 4-3 7-1 0-1 0-1 1-1-2 0-3-1-4-1 4-2 9-6 12 1-7 3-14 5-21 1-6 3-12 5-18l3-12 1 1c2-5 6-8 11-11z" class="B"></path><path d="M678 499c1 0 2-1 3-1l4-3c1 0 1 1 1 2 1-1 2-1 3-2v1s0 1-1 2 0 0-2 1l-1 2h-1c-2 0-3 1-4 2s-2 1-3 2v-1c0-2 1-3 1-5z" class="G"></path><path d="M672 516c1-1 3-2 5-3 4-2 9-3 13-6 3-1 6-4 9-6v1c-1 1-3 2-5 4l1 1c-6 4-14 7-20 11h-3c-1 1-1 2-2 2-1-1-1 0-1-1 1-1 2-1 3-3z" class="O"></path><path d="M679 495c1-1 3-3 4-3-1 2-4 4-6 5-4 4-11 9-13 14 1 0 3-2 4-3 3-3 6-6 9-8l-6 6c-3 3-6 6-9 10-2 3-3 5-4 9-1 2-2 3-3 5 0 2-1 3-2 3h0c1-6 3-12 5-18l2-1c1-2 2-3 4-5 4-5 9-10 15-14z" class="P"></path><path d="M662 504c2-5 6-8 11-11 0 3-1 3-2 5 3-2 4-3 8-3-6 4-11 9-15 14-2 2-3 3-4 5l-2 1 3-12 1 1z" class="H"></path><path d="M662 504c2-5 6-8 11-11 0 3-1 3-2 5-2 3-5 5-8 8l-1 1c-1-1-1 0-1-1 1-1 1-1 1-2z" class="E"></path><defs><linearGradient id="H" x1="646.486" y1="544.094" x2="659.683" y2="534.643" xlink:href="#B"><stop offset="0" stop-color="#353437"></stop><stop offset="1" stop-color="#666764"></stop></linearGradient></defs><path fill="url(#H)" d="M677 500l1-1c0 2-1 3-1 5v1c1-1 2-1 3-2s2-2 4-2c-1 3-2 3-5 5 1 0 2-1 3-1h1l1-1c1 0 1-1 2 0-3 3-7 4-10 7-3 2-6 5-9 7v1c-1 3-6 6-6 8l-2 6v1 3 1c-1 2-2 4-3 7-1 0-1 0-1 1-1-2 0-3-1-4-1 4-2 9-6 12 1-7 3-14 5-21h0c1 0 2-1 2-3 1-2 2-3 3-5 1-4 2-6 4-9 3-4 6-7 9-10l6-6z"></path><path d="M654 542c0-4 4-12 7-15l-2 6v1 3 1c-1 2-2 4-3 7-1 0-1 0-1 1-1-2 0-3-1-4z" class="N"></path><path d="M677 500l1-1c0 2-1 3-1 5v1c-1 0-1 1-1 1-3 2-12 14-14 14 0-1 1-2 2-4h-1-1c3-4 6-7 9-10l6-6z" class="D"></path><path d="M714 324c3-5 6-10 8-16 4-15 3-34-5-48-3-5-6-8-12-9 2-3 4-5 7-6 3-2 6-3 9-2 5 2 10 6 12 10l1 4 1 5v4h-2c1 3 2 7 2 10 2 0 2 0 2 2v4l2 8v-6l2-2c0 3 0 5-2 7l-1 4h-1l-1-1c-1 1 0 6-2 7v-1c-1 1-1 2-1 3l-3 12h1l1 3c-1 1-5 6-6 6h-1c-1 0-2 1-2 1-5 5-11 9-18 11 2-1 3-2 5-4 1-1 3-5 4-6z" class="F"></path><path d="M733 289c0-5-1-9-2-13-1-9-5-17-9-24v-1l3 4v-1-1c1 1 0 1 1 1v1-1h1c3 4 4 8 6 12 1 3 2 7 2 10 2 0 2 0 2 2v4l2 8v-6l2-2c0 3 0 5-2 7l-1 4h-1l-1-1c-1 1 0 6-2 7v-1c0-2 0-3-1-5v-4z" class="J"></path><path d="M725 255v-1-1c1 1 0 1 1 1v1-1h1v1l3 6c1 3 1 6 2 9l1 2v2h0c-1-1-1-2-2-3-1-5-4-11-6-16z" class="G"></path><path d="M733 289v4c1 2 1 3 1 5-1 1-1 2-1 3l-3 12h1l1 3c-1 1-5 6-6 6h-1c-1 0-2 1-2 1-5 5-11 9-18 11 2-1 3-2 5-4 1-1 3-5 4-6h1c7-5 12-13 14-20 1-3 1-5 2-7 0-2 0-3 1-5 0-1 0-2 1-3z" class="E"></path><path d="M733 289v4c1 2 1 3 1 5-1 1-1 2-1 3l-3 12h1l1 3c-1 1-5 6-6 6h-1c-1 0-2 1-2 1 0-2 2-3 3-5 1-1 2-3 3-5-2-3 2-6 0-9 1-3 1-5 2-7 0-2 0-3 1-5 0-1 0-2 1-3z" class="R"></path><path d="M733 289v4c0 7-2 13-4 20-2-3 2-6 0-9 1-3 1-5 2-7 0-2 0-3 1-5 0-1 0-2 1-3z" class="D"></path><path d="M678 458c-3 3-7 5-10 7-3 0-5 2-7 4-3 2-5 3-8 4l9-7v-1l-19 14c12-15 28-27 41-41a79.93 79.93 0 0 0 13-13c4-6 7-12 10-18 5-11 7-21 7-33 0-2 0-7 1-9l1 1c2 0 3 0 4 1 0 1 0 2 1 3 3 7 3 16 3 24l-3 17c1 2 1 2 1 4 0 1 0 0-1 1h0c0 3-3 5-3 8-1 0-1 1-1 1v1l-1 1-1 1-4 7-1 1-2 3-3 4c-1 1-2 2-3 4l-2 1v1c-1 0-1 1-2 1-1 1-2 3-4 4-1 0-2 1-2 2-1 0-1 1-2 1l-5 3c0 1-1 1-2 2l-4 3c-1 0-2 1-2 1l-2 1-1-1c2 0 4-2 5-3v-2l2-2c2-1 4-3 6-5h-1-1c0-2 5-6 6-9-1 1-3 4-4 4s-1 0-1 1c-2 3-7 5-8 8z" class="M"></path><path d="M718 377l1-3v3c1 1 0 3 0 5 1 3-1 6 0 8-1 4-2 8-3 11 0 2-1 6-3 8h0c1-4 3-8 4-12 1-7 1-13 1-20z" class="J"></path><defs><linearGradient id="I" x1="716.104" y1="378.134" x2="725.193" y2="382.786" xlink:href="#B"><stop offset="0" stop-color="#83847e"></stop><stop offset="1" stop-color="#a19e9e"></stop></linearGradient></defs><path fill="url(#I)" d="M716 366c2 0 3 0 4 1 0 1 0 2 1 3 3 7 3 16 3 24-1 1-1 2-2 2v-3c-1 0-1 1-1 2l-1 1c-1-2 0-4 0-6h-1c-1-2 1-5 0-8 0-2 1-4 0-5v-3l-1 3c0-4-1-8-2-11z"></path><path d="M719 390h1c0 2-1 4 0 6 1 1 0 2 0 3l-1 2v2l1 1c-1 4-3 8-4 12v1c-1 3-4 8-6 11 0-1 0-2 1-3h0l1-3c1-1 0-1 0-2-2 4-5 7-7 11-2 3-6 8-9 9 2-2 4-4 5-6 4-4 9-11 10-16-2 1-4 5-5 7h-1c1-2 3-4 4-7l4-9h0c2-2 3-6 3-8 1-3 2-7 3-11z" class="H"></path><path d="M719 403l1 1c-1 4-3 8-4 12v1c-1 3-4 8-6 11 0-1 0-2 1-3h0l1-3c1-1 0-1 0-2l5-11v-2l1-1 1-3z" class="I"></path><path d="M720 396l1-1c0-1 0-2 1-2v3c1 0 1-1 2-2l-3 17c1 2 1 2 1 4 0 1 0 0-1 1h0c0 3-3 5-3 8-1 0-1 1-1 1v1l-1 1-1 1-4 7-1 1-2 3-3 4c-1 1-2 2-3 4l-2 1v1c-1 0-1 1-2 1-1 1-2 3-4 4-1 0-2 1-2 2-1 0-1 1-2 1l-5 3c0 1-1 1-2 2l-4 3c-1 0-2 1-2 1l-2 1-1-1c2 0 4-2 5-3v-2l2-2c2-1 4-3 6-5h-1-1c0-2 5-6 6-9h1c1-2 2-4 4-5 3-1 7-6 9-9 2-4 5-7 7-11 0 1 1 1 0 2l-1 3h0c-1 1-1 2-1 3 2-3 5-8 6-11v-1c1-4 3-8 4-12l-1-1v-2l1-2c0-1 1-2 0-3z" class="F"></path><path d="M720 396l1-1c0-1 0-2 1-2v3c1 0 1-1 2-2l-3 17-2 7c0-1 1-5 1-6 1-2 2-4 1-6-2 3-3 7-5 11v-1c1-4 3-8 4-12l-1-1v-2l1-2c0-1 1-2 0-3z" class="K"></path><path d="M712 420c0 1 1 1 0 2l-1 3h0c-1 1-1 2-1 3s-1 2-1 4h-1c-1 0-1 1-1 1l-1 1c-2 2-3 5-5 7l1 1 2-2v1l-2 3-1 1c-1 2-3 3-4 4-3 2-5 5-8 7l-1 1h-1l-2 2c-1 1 0 0-1 0l-1 1c-1 2-2 2-4 3v-2l2-2c2-1 4-3 6-5h-1-1c0-2 5-6 6-9h1c1-2 2-4 4-5 3-1 7-6 9-9 2-4 5-7 7-11z" class="B"></path><path d="M458 833l-1-2c-1-4-1-8 1-11 2-5 7-10 13-11 11-4 24 2 33 7 3 2 9 5 11 8 4 3 8 7 12 10 3 2 7 5 8 9l2 2 1-1c4 4 6 10 7 15v1s0 1 1 1c0 2 0 16-1 18 0 1-1 2-1 3h-1l-3 5c-1 2-1 4-1 5-1 4-3 6-6 8 0-6-1-10-3-15-3-6-6-11-11-15-6-6-13-11-18-17-4-5-7-9-10-14-2-5-5-10-8-14-3-3-7-5-12-5-2-1-4 0-6 2s-2 5-2 8h-1l-1-1c-1 1-2 3-3 4z" class="F"></path><path d="M512 834c-2-2-5-4-7-6l-1-1h0l1-1v-1h0c5 3 9 5 13 8l1 3c-1-1-3-1-4-1l-3-1z" class="G"></path><path d="M531 871v-1-1c0-1-1-2-1-3v-1-1h0c2 2 2 5 4 7h1v-1-1c1 1 1 1 1 3l1 1h0c0 3 2 7 2 10v4h-1c-1-2-3-5-3-8-1-3-3-5-4-8z" class="B"></path><path d="M530 885c1-5-1-9-3-13s-4-8-7-12l1-1 6 9v-1c0-1-1-2-1-3 2 2 3 4 5 7 1 3 3 5 4 8 0 3 2 6 3 8h1v-4l1 4c-1 2-1 4-1 5-1 4-3 6-6 8 0-6-1-10-3-15z" class="K"></path><path d="M527 868v-1c0-1-1-2-1-3 2 2 3 4 5 7 1 3 3 5 4 8 0 3 2 6 3 8h1v-4l1 4c-1 2-1 4-1 5h-1 0c0-1 0-1-1-2l-2-2c-2-7-6-13-8-20z" class="E"></path><path d="M515 824c4 3 8 7 12 10 3 2 7 5 8 9l2 2 1-1c4 4 6 10 7 15v1s0 1 1 1c0 2 0 16-1 18 0 1-1 2-1 3h-1l-3 5-1-4c0-3-2-7-2-10l2 4c0 1 0 2 1 2h0c-1-5-2-9-4-13-1-3-2-6-3-8-5-10-13-17-21-24l3 1c1 0 3 0 4 1l-1-3 1 1 1-1c-1 0-1 0-1-1-1 0-2-1-2-2 2 0 1 1 3 1 0-1-1-1-2-2v-1h0l-3-3v-1z" class="B"></path><path d="M532 851c2 2 3 4 4 6l3 6c1 1 1 4 3 5 0-2-1-4-1-5l-3-6c-2-2-4-5-5-8-3-4-6-7-9-11h1c5 6 10 13 14 20l5 7c1 3-1 12-2 16-3-9-5-18-10-26l1-1c0-1-1-2-1-3h0z" class="O"></path><path d="M512 834l3 1c1 0 3 0 4 1 3 3 7 6 10 10 1 2 3 3 3 5h0c0 1 1 2 1 3l-1 1c5 8 7 17 10 26 1-4 3-13 2-16l-5-7 1-1c0-3-2-4-3-6-1-3-2-5-2-8l2 2 1-1c4 4 6 10 7 15v1s0 1 1 1c0 2 0 16-1 18 0 1-1 2-1 3h-1l-3 5-1-4c0-3-2-7-2-10l2 4c0 1 0 2 1 2h0c-1-5-2-9-4-13-1-3-2-6-3-8-5-10-13-17-21-24z" class="R"></path><path d="M535 843l2 2c4 6 6 12 7 20l-5-7 1-1c0-3-2-4-3-6-1-3-2-5-2-8zm-20-8c1 0 3 0 4 1 3 3 7 6 10 10 1 2 3 3 3 5h0c0 1 1 2 1 3l-1 1c-5-8-11-14-17-20z" class="D"></path><path d="M314 176c1-10 1-17 10-23 7-6 18-9 27-8 8 2 17 8 22 14 6 8 8 17 6 26-2 13-9 25-20 32-11 8-26 11-39 9-7-1-16-5-20-11-1-1-1-3-2-5h0l2 1c5 7 13 10 22 11l4 1h0c5 0 9-1 13-2 2 0 3-1 4-2h1l3-2 3-2c2-1 3-2 4-2v-1-1h0-2v1l-1-1 1-1c2-1 3-2 5-4h-1l-2 1c0-2 0-2-1-3l-2 1c2-4 6-7 8-10s3-4 4-7 1-4 2-7v-2l-1-2c-1-3-2-5-3-7-3-3-7-4-11-4-1 0-3-1-5-1l-3 3 1 1h1 4l-3 1c4 1 7 1 9 5 1 2 1 7 0 10-1 4-6 8-10 9-5 3-10 3-15 2-7-3-11-9-14-16 0-1-1-2-1-3v-1z" class="F"></path><defs><linearGradient id="J" x1="361.609" y1="159.727" x2="354.565" y2="180.163" xlink:href="#B"><stop offset="0" stop-color="#4b4b4b"></stop><stop offset="1" stop-color="#666"></stop></linearGradient></defs><path fill="url(#J)" d="M345 170h0l-5 2c-1-2-1-4 0-6 1-3 4-6 7-8 4-1 9-1 13 0 5 3 9 7 11 12 3 6 3 12 1 18v1c-1 4-3 10-7 13 1-3 4-7 5-11 1-6 0-14-2-19l-4-4h-2c-2-1-4-3-7-4-1-1 0-1-1 0l1 1c5 2 8 5 10 11v1 2h0l-1-2c-1-3-2-5-3-7-3-3-7-4-11-4-1 0-3-1-5-1l-3 3 1 1h1 4l-3 1z"></path><path d="M365 179h0v-2-1c-2-6-5-9-10-11l-1-1c1-1 0-1 1 0 3 1 5 3 7 4h2l4 4c2 5 3 13 2 19-1 4-4 8-5 11s-6 7-8 8l-3 3v-1-1h0-2v1l-1-1 1-1c2-1 3-2 5-4h-1l-2 1c0-2 0-2-1-3l-2 1c2-4 6-7 8-10s3-4 4-7 1-4 2-7v-2z" class="J"></path><path d="M353 204l5-5c1-2 3-4 4-6l1-1c1 2-3 8-4 10l-2 2c0 1 0 0-1 1v1l-2 1c0-2 0-2-1-3z" class="H"></path><defs><linearGradient id="K" x1="369.725" y1="190.111" x2="358.179" y2="203.808" xlink:href="#B"><stop offset="0" stop-color="#929191"></stop><stop offset="1" stop-color="#b7b6b2"></stop></linearGradient></defs><path fill="url(#K)" d="M356 205c3-2 11-10 11-14h0c1-2 1-2 2-2 1 1 1 1 1 2-1 4-4 8-5 11s-6 7-8 8l-3 3v-1-1h0-2v1l-1-1 1-1c2-1 3-2 5-4h-1v-1z"></path><path d="M687 145c-3 0-6-1-9 1-2 0-3 2-3 4-1 2 0 5 1 7 2 2 6 4 9 6-8-2-14-4-18-10-2-4-3-9-2-13s3-7 7-9c6-4 14-3 20-1 18 5 30 18 38 34 8 15 14 32 18 48l3 16 1-1c1 4 1 8 1 11v1c0 4 1 8 1 11 0 5-1 10-3 14l-2 14c-3 12-9 28-17 38l-1-3h-1l3-12c0-1 0-2 1-3v1c2-1 1-6 2-7l1 1h1l1-4c2-2 2-4 2-7l-2 2v6l-2-8v-4c0-2 0-2-2-2 0-3-1-7-2-10h2v-4l-1-5-1-4v-1c-1-9-5-20-12-27-3-4-6-6-9-10-4-3-5-8-7-13 5 5 6 11 13 13 2 1 6 1 8 0 4-2 6-7 7-12h1c1-5 1-9 0-14-2-16-14-35-27-44h-1c6 4 11 10 15 16-1 0-1-1-2-1 0-2-2-3-3-4l-7-7h-1l3 4c0 1-1 2-2 3-4-3-8-7-13-9-3-1-5-3-9-2z" class="F"></path><path d="M739 211c1 2 1 3 2 5 0-2 0-3-1-4 1-3-1-6 1-8 2 4 1 9 2 14l1 2v1c1 1 1 3 1 4-1-2-1-3-3-3-2-2-3-8-3-11z" class="B"></path><path d="M752 227c1 4 1 8 1 11v1c0 4 1 8 1 11 0 5-1 10-3 14v-1c-2 2-2 6-2 8h-1v2l-1-1c0 1 0 1-1 2l1-13c1 1 2 1 3 1 2-2 2-5 3-8 1-8-1-18-2-26l1-1z" class="H"></path><defs><linearGradient id="L" x1="741.626" y1="243.965" x2="749.374" y2="241.035" xlink:href="#B"><stop offset="0" stop-color="#d1d2c9"></stop><stop offset="1" stop-color="#f1edec"></stop></linearGradient></defs><path fill="url(#L)" d="M742 222c2 0 2 1 3 3-1 1 0 1 0 2v1s1 1 1 2c0 2 2 4 2 6s0 3 1 5c0 1-1 3 0 5 0 1 0 3 1 5 0 1-1 3 0 4v1l1 3h0v-1c0-2 0-2 2-4-1 3-1 6-3 8-1 0-2 0-3-1 0-5-1-10-2-15v-7c0-3-1-5-2-8l-1-9z"></path><path d="M736 214c-1-3-1-5 0-7 1 1 2 3 3 4 0 3 1 9 3 11l1 9c1 3 2 5 2 8v7l-1-3c-1 1 0 4 0 6v1l1 2v1 3c-1-1-1-2-1-3l-1 1h0v-2c-1-3-1-6-2-9-1-7-4-14-7-21v-1l4 7h0l-1-3v-1c-1-4-2-7-1-10z" class="J"></path><path d="M736 214c1 3 3 9 3 12 0 2 1 5 2 7h0c1 3 1 7 1 11v-2c-1-5-4-9-4-14h0l-1-3v-1c-1-4-2-7-1-10z" class="D"></path><path d="M736 214c-1-3-1-5 0-7 1 1 2 3 3 4 0 3 1 9 3 11l1 9c1 3 2 5 2 8v7l-1-3h0c0-4-1-8-3-10h0 0c-1-2-2-5-2-7 0-3-2-9-3-12z" class="K"></path><path d="M733 252l3 8v2c1 1 1 2 1 3v1c0 1 1 2 1 4h0v-2h1v-4h-1v-1c1-1 1-3 1-4-1-1-1-3-1-5-1-2 0-5-1-8 0-1-1-2-1-3l-2-10c-1-2-1-2 0-3h0l1 3v1c0 1 1 1 1 2l1 4 1 4c1 4 1 8 2 12h0v-8h1v3 1c0 1 0 4-1 5l1 4c0 1 0 3 1 5v-1-2-5-3-2-3c-1-1-1-3-1-4v-1c-1-1-1 0 0-1v-1c1 3 1 6 2 9v2 11c0 5 0 10-1 14-1 1-1 2-1 3l-2 2v6l-2-8v-4c0-2 0-2-2-2 0-3-1-7-2-10h2v-4l-1-5-1-4v-1z" class="D"></path><path d="M737 278h1v-3c2-4 2-7 2-12 0 3 0 5 2 7h0c0-1 1-3 1-5 0 5 0 10-1 14-1 1-1 2-1 3l-2 2v6l-2-8v-4z" class="Q"></path><defs><linearGradient id="M" x1="739.916" y1="311.165" x2="734.028" y2="293.686" xlink:href="#B"><stop offset="0" stop-color="#202224"></stop><stop offset="1" stop-color="#4e4c4a"></stop></linearGradient></defs><path fill="url(#M)" d="M743 254h0l1-1c0 1 0 2 1 3v-3-1l-1-2v-1c0-2-1-5 0-6l1 3c1 5 2 10 2 15l-1 13c1-1 1-1 1-2l1 1v-2h1c0-2 0-6 2-8v1l-2 14c-3 12-9 28-17 38l-1-3h-1l3-12c0-1 0-2 1-3v1c2-1 1-6 2-7l1 1h1l1-4c2-2 2-4 2-7 0-1 0-2 1-3 1-4 1-9 1-14v-11z"></path><path d="M749 271c-1 5-2 12-5 16h0v-5c1-2 1-5 2-8 1-1 1-1 1-2l1 1v-2h1z" class="L"></path><path d="M743 254h0l1-1c0 1 0 2 1 3v-3-1l-1-2v-1c0-2-1-5 0-6l1 3c1 5 2 10 2 15l-1 13c-1 3-1 6-2 8h-1c0 2-1 4-2 7v4c-1 2-3 3-3 5l-7 15h-1l3-12c0-1 0-2 1-3v1c2-1 1-6 2-7l1 1h1l1-4c2-2 2-4 2-7 0-1 0-2 1-3 1-4 1-9 1-14v-11z" class="P"></path><path d="M762 235c1-1 1-1 1-2 1-2 3-3 4-4 5-5 10-9 17-10-1 1-1 2 0 4l-1 13c2 7 3 11 9 16 1 0 2 1 2 1 1 3-11 25-13 29s-4 8-6 11-4 4-6 7c-5 8-11 17-19 22-12 12-25 22-41 25-4 1-8 2-12 1-2 0-6-1-8-3v-1l-1-1c2-3 5-3 8-4 11-3 20-8 29-16l1-1c1 0 5-5 6-6 8-10 14-26 17-38 1 0 0 0 1 1v1h1l4-25c1-1 1-2 1-3l7-10c0-2-1-4 0-5 0-2 0-1-1-2z" class="F"></path><path d="M769 264c1-3 2-7 3-10 0-1 1-4 2-5l1 2-3 12h-1c0 1 0 2-1 3l-1 1v-3zm-14 24h0 2v-1h0l1-1v-1l1-3v-1-1c1-1 1-1 1-2v-1l1-1 1-4 2-4v-2c1-1 1-2 2-4v1c0 3-2 6-3 9l-4 16v3l2-2c-1 2-2 6-4 7-1 1-2 2-2 3l-1 2v-1c0-2 1-5 1-7v-5z" class="D"></path><path d="M763 272c1 0 0 0 1 1h0c0-1 1-1 1-2s1-2 1-2c1-1 1-2 1-2v-1c0 1 0 2 1 3 1-2 0-2 0-3s1-2 1-2v3l-8 22-2 2v-3l4-16zm-67 67c11-3 20-8 29-16-2 3-3 4-5 6-1 1-1 1-2 1-2 1-3 3-5 4l1 1h-1 0 3l3-1h0c-1 2-5 3-7 3-1 1-3 1-4 2h-1c-1 1-2 1-4 0-1 0-3 1-5 0h-2z" class="B"></path><path d="M762 235c1-1 1-1 1-2 1-2 3-3 4-4 5-5 10-9 17-10-1 1-1 2 0 4-9 2-17 11-21 19 0-2-1-4 0-5 0-2 0-1-1-2z" class="R"></path><defs><linearGradient id="N" x1="746.106" y1="312.46" x2="738.215" y2="304.833" xlink:href="#B"><stop offset="0" stop-color="#cccbc9"></stop><stop offset="1" stop-color="#f0eee3"></stop></linearGradient></defs><path fill="url(#N)" d="M729 330h-2l-1 1-1-1h0l2-2c3-2 5-5 8-8v1s-1 1-1 2c6-4 9-11 11-17 2-2 4-5 5-8v-1l1-3v-2h0l2-2c0-1 1-2 1-3v-1h1v1 1 5c0 2-1 5-1 7v1l1-2c0-1 1-2 2-3-2 6-5 12-9 17-3 3-16 17-19 17z"></path><defs><linearGradient id="O" x1="747.884" y1="306.279" x2="764.253" y2="296.136" xlink:href="#B"><stop offset="0" stop-color="#6e6c6a"></stop><stop offset="1" stop-color="#898a87"></stop></linearGradient></defs><path fill="url(#O)" d="M775 251c0-1 0-3 1-4l1-3v3l-5 18c0 3-1 5-2 8 0 4-2 8-3 11-7 19-20 38-36 49-5 5-11 7-17 10-1 0-1 0-2 1h-1c-1 0-2 1-2 1h-2c-2 1-3 1-4 0 2-1 3 0 5-1 1-1 1-2 1-3-4 2-10 5-14 3 12-2 23-7 34-14 3 0 16-14 19-17 4-5 7-11 9-17 2-1 3-5 4-7l8-22 1-1c1-1 1-2 1-3h1l3-12z"></path><path d="M729 330c3 0 16-14 19-17h0l1 1c-3 4-9 12-13 14-5 5-10 9-16 11-2 1-4 2-5 3-1 0-1 0-1 1-1 0-1 0-2 1h-1c-1 0-2 1-2 1h-2c-2 1-3 1-4 0 2-1 3 0 5-1 1-1 1-2 1-3-4 2-10 5-14 3 12-2 23-7 34-14z" class="H"></path><path d="M709 341c6-3 14-4 19-8 3-1 5-3 7-5h1c-5 5-10 9-16 11-2 1-4 2-5 3-1 0-1 0-1 1-1 0-1 0-2 1h-1c-1 0-2 1-2 1h-2c-2 1-3 1-4 0 2-1 3 0 5-1 1-1 1-2 1-3z" class="D"></path><path d="M770 273l2-2 1-3 1-1c0-1 0-2 1-2 1-1 1-2 1-3h1c0-3 1-6 2-8v-2c1-1 1-1 1-2v-3l1-1v-4c1-2 1-3 1-5l1-1c2 7 3 11 9 16 1 0 2 1 2 1 1 3-11 25-13 29s-4 8-6 11-4 4-6 7c-5 8-11 17-19 22-12 12-25 22-41 25-4 1-8 2-12 1-2 0-6-1-8-3v-1c1 0 1 0 3-1 1 0 1 0 3 1 4 2 10-1 14-3 0 1 0 2-1 3-2 1-3 0-5 1 1 1 2 1 4 0h2s1-1 2-1h1c1-1 1-1 2-1 6-3 12-5 17-10 16-11 29-30 36-49 1-3 3-7 3-11z" class="M"></path><path d="M750 322c0-2 2-4 4-6 3-3 5-6 7-9l10-18c2-4 5-8 7-12 3-6 5-13 7-19h1c-2 6-5 13-8 20l-3 6c-1 2-3 4-4 6v3c-1 0-1 0 0 1 1-2 3-3 4-5 2-3 3-5 6-7-2 4-4 8-6 11s-4 4-6 7c-5 8-11 17-19 22z" class="Q"></path><path d="M239 251c0 2 1 5 2 6 3 8 7 14 11 21 2 3 4 6 6 8 1 1 2 2 2 3-12-7-20-21-25-34-4-13-8-27-7-40 1-10 4-21 6-31 1-6 1-12 4-17 1-2 3-5 5-5 2-1 3-1 4 0 2 3 2 8 1 12h0c4-10 9-20 16-28 4-4 9-8 14-12 4-3 7-6 12-9v1c1-1 1-1 2-1-1 2-3 5-4 8-2 6 2 14 3 20v1c3-1 7-2 11 0 0 0 1 0 1 1 0 0-5 5-5 6-7 10-12 19-20 28-8 8-18 15-24 25-3 4-4 8-5 12h1c4-8 9-13 18-16l-3 10c-1-2 0-3-1-4-1 0-1 2-2 3-2 5-5 9-7 14-3 2-3 4-4 6-2 1-3 1-3 3h0c0 2-1 4-2 5s-1 1-2 1c1 4 2 9 2 12l-4-12h-1c0-1 0-2-1-3v-3h-1 0c-1 3 0 6 0 9z" class="C"></path><path d="M263 160c2-3 5-6 6-10h0c1-3 4-5 7-6l-12 18-1-2z" class="G"></path><defs><linearGradient id="P" x1="252.474" y1="235.636" x2="258.38" y2="217.317" xlink:href="#B"><stop offset="0" stop-color="#c6c6c2"></stop><stop offset="1" stop-color="#faf7f2"></stop></linearGradient></defs><path fill="url(#P)" d="M246 223c1 3-1 9-1 13v8c1-2 3-5 4-7 2-5 4-12 8-16h0c1-1 1-2 2-3 1 0 2 0 3 1-2 5-5 9-7 14-3 2-3 4-4 6-2 1-3 1-3 3h0c0 2-1 4-2 5s-1 1-2 1c0-8 0-17 2-25z"></path><defs><linearGradient id="Q" x1="251.531" y1="239.994" x2="237.427" y2="227.97" xlink:href="#B"><stop offset="0" stop-color="#dcd6d8"></stop><stop offset="1" stop-color="#fffff1"></stop></linearGradient></defs><path fill="url(#Q)" d="M246 216s1-1 1-2l1 2v-1c0 1-1 3-1 4-1 1-1 2-1 4h0c-2 8-2 17-2 25 1 4 2 9 2 12l-4-12c0-2-1-5-1-8-1-6 0-12 1-17h0c0 1 0 1 1 2 0-1 0-1 1-2v-1-1c0-1 0-1 1-2 0-1 0-2 1-3z"></path><path d="M236 233c0-1 0-2-1-2-1-1-1-1-1-2-1-2-1-4-1-6 0-8 2-15 4-23 1-7 3-13 5-20v1l-7 30v2 4c2-3 2-8 3-11l5-15v-2c0-1 1-2 1-3h1c0 1-1 2-1 3l-1 3c-1 3-2 5-1 8-1 3-3 6-3 9-2 8-3 16-3 24z" class="D"></path><path d="M281 149l3-4c0 1 1 1 1 3v2c1 0 1 1 0 2s-4 3-4 5c0 0 0 1-1 1-1 3-3 3-4 6 0 1-1 2-1 3-1 0-1 2-2 3l5-5 2 1-1 2v1l-3 3c-1 2-3 2-4 4h0c-1 2-1 3-2 4s-1 1-1 2c-1 0-2 1-2 2l-1 1-2 2c0 1-1 2-2 3l-3 3c0 1-1 1-1 2l-1 2c-1 0-1 1-2 2-2 3-4 6-6 10-1 0-1 1-1 2l-1 1v1c-1 1-1 1-1 2v1c-1 1-1 2-1 3-1 1-1 1-1 2v1 1c-1 1-1 1-1 2-1-1-1-1-1-2h0c1-9 4-17 8-25 2-1 9-14 11-17 5-8 11-15 17-22h0l-8 9c-1-1-1 0-1-1v-1c0-2 1-3 2-5 2-2 3-4 5-6 1-2 4-4 5-6z" class="M"></path><path d="M276 144l1-1h0l-1 2h1c1-3 4-5 6-8 1-2 2-4 4-5h0c-1 2-4 6-5 8v1c-1 1-1 2-1 3l-6 9c2-1 4-3 6-4-1 2-4 4-5 6-2 2-3 4-5 6-1 2-2 3-2 5v1c0 1 0 0 1 1l8-9h0c-6 7-12 14-17 22-2 3-9 16-11 17-4 8-7 16-8 25-1 5-2 11-1 17 0 3 1 6 1 8h-1c0-1 0-2-1-3v-3h-1 0c-1 3 0 6 0 9-2-5-3-12-3-18 0-8 1-16 3-24 0-3 2-6 3-9l10-20h0c1-2 1-3 2-4 0-1 0-1 1-2v-1l1-1c2-5 3-9 7-12l1 2 12-18z" class="J"></path><path d="M252 180h0c1-2 1-3 2-4 0-1 0-1 1-2v-1l1-1c2-5 3-9 7-12l1 2c-1 2-4 5-5 9l1 1c-1 2-2 3-3 5s-2 5-3 7c-4 2-9 15-10 15 0-7 7-12 8-19z" class="B"></path><path d="M270 168c0 1-3 3-3 4-3 3-6 6-9 10 1-2 3-4 3-7 0-2 2-6 4-8 5-9 11-19 17-27v1c-1 1-1 2-1 3l-6 9c2-1 4-3 6-4-1 2-4 4-5 6-2 2-3 4-5 6-1 2-2 3-2 5v1c0 1 0 0 1 1z" class="D"></path><defs><linearGradient id="R" x1="244.139" y1="230.828" x2="235.361" y2="221.172" xlink:href="#B"><stop offset="0" stop-color="#353835"></stop><stop offset="1" stop-color="#4c4b4d"></stop></linearGradient></defs><path fill="url(#R)" d="M239 209l1 1-2 5h0v4c-1 4-2 10-1 15 0 0 1 1 1 2h0c-1-5 0-12 2-18 0-2 1-4 1-7h0 1v1h1l2-6 1-3c1-2 2-5 4-5-4 8-7 16-8 25-1 5-2 11-1 17 0 3 1 6 1 8h-1c0-1 0-2-1-3v-3h-1 0c-1 3 0 6 0 9-2-5-3-12-3-18 0-8 1-16 3-24z"></path><path d="M168 126c6-1 13-1 19-1 4 0 8 0 11 1 12 3 28 21 33 31 1 1 2 4 2 6 0 1-1 1-1 2-5 1-12-3-16-5 6 9 13 18 11 30-1 7-5 12-10 15-5 4-12 4-17 3-5-2-10-5-12-10-5-8-2-16 0-24 1-3 1-6 1-8-2 5-8 22-13 24-1 0-2-1-2-2-1-2 0-3 0-5-1 1-2 1-4 1-2-1-3-2-3-3-1-2-1-4-1-6 1-10 6-22 1-32-1-3-3-5-6-8 1 1 1 2 2 2 2 4 2 9 1 14-1 3-3 5-6 7l-3 3 4 5v1c-3-1-4-1-5-3-1-3-2-6-3-10 4-1 8-2 11-3 1-2 1-4 0-6h0v-1l-1-1v2c1 2 1 3 0 5-2 1-4 2-7 2-1 1-2 1-2 1-3 0-7-3-9-4-1-2-2-5-4-6h-3-1c-1 1-4 1-6 2h-1l-3-2c-2 0-3 1-4 1-3 0-4 0-6-1v-1h1 1c9-2 14-11 24-14 3 0 8 3 11 5 0 1 0 1 1 2 0-1 0-2-1-4-3-3-6-3-10-4 3-1 6-1 9-2 3 3 5 3 9 4h2c2-2 3-3 6-3z" class="F"></path><path d="M155 141l2-1c1 1 1 2 1 4s0 4-1 5c-1 0-1-1-2-2 0-1 0-3-1-4v-1-1h1z" class="B"></path><path d="M169 132c2 0 3 1 5 2h3c3 2 7 5 9 8v1l1 2c0 1 1 2 1 3h-1 0l-3-4c-1-3-1-3-4-4l-1 1c1 1 1 2 1 4l1 1v4h0c1 2 1 7 0 8 0 2 0 3-1 5h0v-1c1-2 0-4 1-6h0c-1-2 0-5-1-7v-3c-1-4-3-9-6-11-2-1-3-1-5-3z" class="D"></path><path d="M160 129h2c2-2 3-3 6-3v1c1 1 4 1 6 2 4 1 11 6 15 9 3 3 6 8 9 9l2 2h-1l-1-1-1 1 8 13c1 1 1 2 2 4l2 12v2c0-1-1-2-1-4-2-3-1-7-2-11h-1v1c1 4 3 13 2 17-1 5-1 8-3 13 1-7 1-14 1-21 0-1-1-2-1-3v-1 5c-1 2 0 5-1 6-2-1-1-11-1-14 0-1-1-3-1-4l-1-1v-1c-1-1-2-1-2-3l-1-1c-1-1 0-1-1-1 0 1 0 0-1 1-1-2-3-6-3-8-2-3-4-6-6-8-2-3-6-6-9-8h-3c-2-1-3-2-5-2-2-2-6-3-9-3z" class="P"></path><path d="M192 150l-1-1c-1-6-7-11-11-14-2-1-3-2-4-4 2 1 5 3 8 5 2 2 4 5 7 7 2 2 4 4 5 6 4 6 7 13 9 21v5c0-1-1-2-1-3v-1 5c-1 2 0 5-1 6-2-1-1-11-1-14 0-1-1-3-1-4l-1-1v-1c-1-1-2-1-2-3l-1-1c-1-1 0-1-1-1 0 1 0 0-1 1-1-2-3-6-3-8z" class="G"></path><path d="M816 174c11-18 23-36 44-44 8-2 18-4 25-1 5 3 8 5 9 10-1 1-1 1-3 1v-1c-1-2-3-3-6-4-5-1-11 2-15 5-3-1-5 2-8 4-2 2-5 4-8 7-6 8-11 17-18 24-4 5-9 10-15 14l-9 6c-4 1-8 3-13 4-2 1-7 0-8 2-2 4-3 7-4 11 0 2-1 5-2 6l-1 1c-7 1-12 5-17 10-1 1-3 2-4 4 0 1 0 1-1 2 1 1 1 0 1 2-1 1 0 3 0 5l-7 10c0 1 0 2-1 3l-4 25h-1v-1c-1-1 0-1-1-1l2-14c2-4 3-9 3-14 0-3-1-7-1-11v-1c0-3 0-7-1-11l-1 1-3-16h0v-3c-1-5-3-10-1-16 3-14 9-25 17-37 5-6 10-12 17-16 6-4 14-7 22-5 3 1 7 4 9 7 1 3 2 7 1 10-1 4-4 6-7 8l-2 1c0 1-1 3-1 4-1 3-1 6-3 9-3 7-7 14-12 19 0 1 1 1 2 2l9-6c7-4 12-9 17-15z" class="M"></path><path d="M774 201c2-6 6-13 10-18h0c0 1 0 2-1 2 0 1 0 2-1 3v1c0 2-1 3-2 5l-1 1c-1 1-2 2-3 4l-2 2z" class="B"></path><path d="M771 206c0 3-1 5-3 8 2-1 4-2 5-3l3-2h0c-2 3-6 5-8 8 1 1 0 1 1 1h1l-1 1h0c-2 0-3 2-5 2 0-1 0-3 1-4v-1c2-4 4-7 6-10z" class="D"></path><path d="M769 218c2-3 5-6 9-7h2c0 2 0 2-1 3l-1 1h0c-3 2-5 4-7 5h-1c-2 1-4 3-5 5l-1-1 5-5h0l1-1h-1z" class="G"></path><path d="M806 192c1 0 3-1 5-2v1c-4 2-8 4-13 5-3 1-6 1-10 3-3 1-6 4-9 5 3-4 5-8 9-11 0 1 1 1 2 2l-1 1h2c5-1 10-2 15-4z" class="N"></path><path d="M826 171c1-2 3-3 4-5 2-5 5-9 8-12h1c-3 3-6 7-8 11h1c3-3 5-7 7-10l6-6c-1 3-2 4-3 6l-7 10-12 12v-1l4-5h-1zm-73 67l1 3c1 2 1 4 1 7v-5c2-3-1-21 1-27v36c0 1 0 2-1 3l-4 25h-1v-1c-1-1 0-1-1-1l2-14c2-4 3-9 3-14 0-3-1-7-1-11v-1z" class="D"></path><defs><linearGradient id="S" x1="747.967" y1="213.433" x2="748.668" y2="197.485" xlink:href="#B"><stop offset="0" stop-color="#6a696a"></stop><stop offset="1" stop-color="#898885"></stop></linearGradient></defs><path fill="url(#S)" d="M747 193l1 1 4-4c2-4 3-10 5-14 0 4-3 10-4 14s-1 7-2 11c-1 9 0 18 1 26l-1 1-3-16h0v-3c-1-5-3-10-1-16z"></path><path d="M772 193c0 2 1 3 0 5l-5 11c4-2 6-10 8-14 6-10 13-19 21-28v1c-4 5-8 9-12 15-4 5-8 12-10 18l-3 5c-2 3-4 6-6 10v1c-1 1-1 3-1 4 2 0 3-2 5-2l-5 5 1 1c0 2-5 9-5 10 0-3 1-5 1-8 0-1 1-3 1-5v-5c1-2 1-3 2-5 1-3 2-6 3-10 2-3 3-7 5-9z" class="K"></path><path d="M816 174c-1 4-4 6-6 9h1l2-2 1-1h0c2-3 6-5 8-8 3-3 5-7 8-10h0c-2 3-4 7-7 10-2 2-4 4-5 7h0l8-8h1l-4 5v1c0 1-4 4-4 4 0 2-1 3-3 4l1 2-6 3c-2 1-4 2-5 2-5 2-10 3-15 4h-2l1-1 9-6c7-4 12-9 17-15z" class="E"></path><path d="M819 181c0 2-1 3-3 4l1 2-6 3c-2 1-4 2-5 2l2-2h0c2 0 2 0 2-2 3-2 7-4 9-7z" class="Q"></path><path d="M799 189l1 1c2-1 3-2 5-3v1c0 1-1 1-1 2 2 0 5-1 6-2 0 2 0 2-2 2h0l-2 2c-5 2-10 3-15 4h-2l1-1 9-6z" class="L"></path><path d="M767 168v1c-4 9-7 19-9 29 0 4 0 9-1 12-1 6 0 15 0 21 2-3 1-10 2-13l1 1h1l5-18 1 1c-1 4-2 7-3 10-1 2-1 3-2 5v5c0 2-1 4-1 5 0 3-1 5-1 8 0-1 5-8 5-10 1-2 3-4 5-5-4 5-7 10-9 16 1 0 1-1 1-1 1 1 1 0 1 2-1 1 0 3 0 5l-7 10v-36c1-17 4-33 11-48z" class="N"></path><path d="M757 231c2-3 1-10 2-13l1 1h1c0 4 0 9-2 13-1 2 0 4-1 7l-1-1c0-2-1-5 0-7z" class="J"></path><defs><linearGradient id="T" x1="844.472" y1="164.201" x2="846.608" y2="141.447" xlink:href="#B"><stop offset="0" stop-color="#8c8c8b"></stop><stop offset="1" stop-color="#adaba8"></stop></linearGradient></defs><path fill="url(#T)" d="M845 149c5-6 9-10 16-13 2-1 4-1 6-2 1-1 3-1 4-1-8 4-14 9-20 16-13 14-21 34-40 42v-1l6-3-1-2c2-1 3-2 3-4 0 0 4-3 4-4l12-12 7-10c1-2 2-3 3-6z"></path><path d="M835 165s1 1 0 2c0 2-2 4-3 6-3 3-8 8-11 10l-4 4-1-2c2-1 3-2 3-4 0 0 4-3 4-4l12-12z" class="H"></path><path d="M767 168h0c5-11 15-23 26-27 3-1 8-2 11 0 2 1 4 3 4 5 1 2 1 5 0 7-3 3-10 4-14 4h-2-6c-1 1-3 2-4 3-5 4-9 9-13 14 0-1 1-3 2-4l1-2c1-2 2-4 2-6v-4l-7 11v-1z" class="P"></path><defs><linearGradient id="U" x1="746.048" y1="203.069" x2="796.433" y2="168.989" xlink:href="#B"><stop offset="0" stop-color="#a4a3a3"></stop><stop offset="1" stop-color="#cfcfcb"></stop></linearGradient></defs><path fill="url(#U)" d="M786 157h6 2c0 3 0 4-1 6-4 6-9 12-14 18-1 2-3 4-4 7-1 1-1 3-2 4l-1 1c-2 2-3 6-5 9l-1-1-5 18h-1l-1-1c-1 3 0 10-2 13 0-6-1-15 0-21 1-3 1-8 1-12 2-10 5-20 9-29l7-11v4c0 2-1 4-2 6l-1 2c-1 1-2 3-2 4 4-5 8-10 13-14 1-1 3-2 4-3z"></path><path d="M786 157h6 2c0 3 0 4-1 6-4 6-9 12-14 18-1 2-3 4-4 7-1 1-1 3-2 4l-1 1c-2 2-3 6-5 9l-1-1c1-3 2-6 4-9 3-7 6-14 11-20 3-4 7-8 9-12v-2l-4-1z" class="L"></path><path d="M286 312c-6-4-12-11-15-17-5-10-7-23-3-33 10-32 49-28 74-34 8-2 15-4 21-7 4-2 7-4 10-6-1 2-2 4-4 6h0v1h2c0 1 0 2-1 3s-2 2-3 2v1h1c2 1 3 2 5 4 0 2 0 3-1 4s-1 2-3 2c2 4 6 4 6 9v1c-7-1-15-3-22-4 9 4 20 8 24 19 2 5 1 12-2 18-2 4-6 9-10 13-1 1-3 4-5 5-1-2-2-5-4-7-2-3-5-4-7-7-3-3-5-6-8-9 1 2 3 4 3 6-1 1-1 2-2 2-6-1-10-5-13-8h0c-2 2-3 3-5 4-3 0-7-2-10-4l-2-1-2 1c-3 1-6 1-9 2-2 3-2 6-1 8 0 3 1 5 2 6h-1l2 2v3c0 1 0 2 1 3s3 2 4 3h1 12l1 1c0 1 0 0 1 1-1 1-1 1-2 1l1 1v1c-4 0-8-1-11 0-4 1-7 3-11 3v1l1 1-2 1c-1 1-1 2-2 3-4-1-8-3-11-5z" class="F"></path><path d="M369 222h2c0 1 0 2-1 3s-2 2-3 2h-4c0-1 1-1 1-2h1l1-1c1-1 2-1 3-2z" class="C"></path><path d="M332 245l1-1v1l6 2c2 1 4 1 6 2h1l1 1h0c4 1 9 7 12 10h-1c-2-1-2-3-5-2l-9-7c-3-3-8-4-12-6z" class="G"></path><path d="M344 251l9 7c5 6 10 15 11 22 1 2 1 4 0 7v1-1c-1-5-2-7-4-11-2-3-3-6-5-8h1v-1c0-1-1-2-2-3h0c-2-3-4-5-6-8-2-1-4-2-4-5z" class="J"></path><defs><linearGradient id="V" x1="331.658" y1="251.516" x2="339.063" y2="245.942" xlink:href="#B"><stop offset="0" stop-color="#636365"></stop><stop offset="1" stop-color="#8e8c87"></stop></linearGradient></defs><path fill="url(#V)" d="M355 268l-7-8c-4-7-12-10-19-13-4-2-7-4-11-6h-1c4-1 11 3 15 4 4 2 9 3 12 6 0 3 2 4 4 5 2 3 4 5 6 8h0c1 1 2 2 2 3v1h-1z"></path><path d="M273 266c2-7 5-14 11-18 4-3 10-4 16-3 5 1 9 4 12 8 1 3 3 6 2 10-1 2-3 5-5 6-1 1-2 1-3 0-2 0-3-1-3-2 1-2 3-1 4-3v-1l-1-2c-4 0-7 1-10 4-4 3-5 7-5 11 0 6 3 10 7 14l3 2 2 2v3c0 1 0 2 1 3s3 2 4 3h1 12l1 1c0 1 0 0 1 1-1 1-1 1-2 1l1 1v1c-4 0-8-1-11 0-4 1-7 3-11 3v1l1 1-2 1c-1 1-1 2-2 3-4-1-8-3-11-5h2c1 1 2 1 3 2h1c1 1 2 1 3 1h1c-2-1-6-4-7-6-1-1-2-3-3-4-3-2-5-4-6-7-3-3-4-6-5-10-1-2-2-4-2-7-1-4-1-10 0-14v-1z" class="D"></path><path d="M288 300c4 3 7 6 12 8 1 1 3 1 5 2 0-1 0-1 1-2h1v-1c-1-1-2-1-3-2-2 0-4-1-5-2 2 0 3 0 4 1 4 2 12 1 17 1v-1c-3-1-8 1-11-1h0 12l1 1c0 1 0 0 1 1-1 1-1 1-2 1l1 1v1c-4 0-8-1-11 0-4 1-7 3-11 3v1c-4-1-7-4-10-6l1-1c2 2 3 4 6 4 1 1 1 0 2 1h0 1l-1-1c-4-2-8-5-11-9z" class="J"></path><defs><linearGradient id="W" x1="301.672" y1="285.46" x2="280.774" y2="288.204" xlink:href="#B"><stop offset="0" stop-color="#b0ada5"></stop><stop offset="1" stop-color="#cccbc8"></stop></linearGradient></defs><path fill="url(#W)" d="M285 263c1-1 2-2 3-2 3-1 5-3 7-5 1 0 2 0 3 1-2 0-4 1-6 3h0c-3 2-4 5-6 7-1 2-1 3-2 4v1c-1 2 0 6 0 9v1c1 1 1 1 1 2l1 1c0 2 1 3 1 4 1 1 1 2 2 2l4 7 3 3h1l2 2h0c1 1 3 2 5 2 1 1 2 1 3 2v1h-1c-1 1-1 1-1 2-2-1-4-1-5-2-5-2-8-5-12-8h0l-2-2-1-2c-1-1-1-2-1-2l-1-3-1-1-1-1c0-2-1-3-1-4l-1-1v-2l-1-1c0-5 0-7 2-11l2-2c0-1 0-2 1-3h0l2-2z"></path><path d="M292 260h1l4-2v1h-1l-1 1c-4 2-6 6-8 11h0c-1 3-1 4-1 6 1-5 4-12 8-15 2-2 7-5 10-4 0 0 1 0 1 1v1l1 1c-4 0-7 1-10 4-4 3-5 7-5 11 0 6 3 10 7 14l3 2 2 2v3c0 1 0 2 1 3s3 2 4 3h1 0c3 2 8 0 11 1v1c-5 0-13 1-17-1-1-1-2-1-4-1h0l-2-2h-1l-3-3-4-7c-1 0-1-1-2-2 0-1-1-2-1-4l-1-1c0-1 0-1-1-2v-1c0-3-1-7 0-9v-1c1-1 1-2 2-4 2-2 3-5 6-7z" class="K"></path><path d="M303 297h-1c-5-1-10-9-12-13s-2-9-1-13v-1c2-4 4-6 7-8 4-2 5-3 9-2l1 1c-4 0-7 1-10 4-4 3-5 7-5 11 0 6 3 10 7 14l3 2 2 2v3z" class="N"></path><path d="M273 266c2-7 5-14 11-18 4-3 10-4 16-3 5 1 9 4 12 8 1 3 3 6 2 10-1 2-3 5-5 6-1 1-2 1-3 0-2 0-3-1-3-2 1-2 3-1 4-3v-1l-1-2-1-1v-1c0-1-1-1-1-1-3-1-8 2-10 4-4 3-7 10-8 15 0-2 0-3 1-6h0c2-5 4-9 8-11l1-1h1v-1l-4 2h-1 0c2-2 4-3 6-3-1-1-2-1-3-1-2 2-4 4-7 5-1 0-2 1-3 2 2-4 6-5 9-8 1-1 2-2 4-2h0v-1c-3 1-5 3-8 5-3 1-5 3-7 5-1 1-1 1-1 2-2 2-3 5-4 7v1l-1 4h0c-1-1-1-2 0-3v-2c-1 0-1 0-2 1v-1-1l-2 2c-1-2 0-4 0-5v-1z" class="R"></path><path d="M273 266h1v-1c1-3 3-9 6-11 2-1 3-2 6-3 2-1 4-2 7-2h0c-1 1-2 1-3 1l-1 1c-3 0-6 3-7 5 1-1 2-2 3-2 2-2 3-3 5-3v1c-3 2-6 3-8 6l1 1c2-3 4-4 7-6h1c1-1 2-1 4-1-4 3-10 4-12 10h0c-1 1-1 1-1 2-2 2-3 5-4 7v1l-1 4h0c-1-1-1-2 0-3v-2c-1 0-1 0-2 1v-1-1l-2 2c-1-2 0-4 0-5v-1z" class="I"></path><path d="M305 314v-1c7-3 17-1 25 0 28 4 61 20 78 43 5 6 8 13 12 20 17 34 29 71 23 109-4 20-12 37-24 54-13 18-28 34-37 54-6 12-9 25-11 38-1 0-3-5-3-7-7-17-5-36-2-54 2-13 6-26 12-38l34-62c2-2 3-5 4-7 2-6 4-12 5-18l1-1 1 1v-3c0-2 1-5 1-7h1v2l1-1c1 0 1-1 2-3 0-2-2-8-3-10-1-6-3-11-6-16l1-1c-8-8-19-13-31-12-3 0-6 2-9 2v-1l1-3c-1 1-2 1-3 1l-1-1v-1c-1-2-1-3 0-5 1-3 4-5 6-6l6-2c-3 0-6-1-8-2v-1c2-3 6-6 10-6 8-2 16 0 22 5v-1c-12-11-27-20-44-18-11 1-21 5-27 13s-8 17-7 26c2 13 11 27 21 34 3 3 7 4 10 6h1c3 1 6 1 10 1 2 0 5 0 7 1l1 1c0 2-2 3-4 5-5 4-13 5-20 4-2 0-4-1-6-2l2 2c4 1 8 1 12 1 6 1 11 2 17 1 5-1 10-3 14-5s7-6 10-8c0 1 1 1 1 2 1 2 1 5 0 8-2 6-5 9-11 12 2 1 7 1 9 2 0 2-1 5-2 7-4 7-12 12-20 14-7 3-18 2-26-1-6-2-11-5-17-8h-1c9 6 17 11 25 18 3 3 5 6 5 11h-1c-5-4-9-8-15-11-12-7-24-9-33-20-5-6-9-14-13-21 2 5 5 12 4 18-1 3-2 5-4 6h-1c-4-7-8-13-10-20-1-4-1-8-2-11l-1-1c0 2 0 3 1 4l1 7v3c0 2 0 2-1 3-2-2-3-4-4-6l-3-8c-4-16-1-35 7-49l-1-1c-3 3-9 12-13 13-1-1-2-1-3-2l1-1c0-2 3-4 5-6 2-4 4-7 6-11 4-7 7-15 13-21 4-6 10-10 17-13 4-2 10-3 15-5 1-1 2-2 2-3-1-1-3-1-4-1-6-1-13 1-19 3-5 1-9 3-14 3-7 1-13-2-20-3-4-1-8-2-12-2 3-4 6-10 11-13l1-1c-1-1-1-1-1-2l3-3c6-4 17-7 25-6h2c-1-2-5-3-7-5z" class="F"></path><path d="M363 464c3-1 8 0 11 0 5 0 11 0 16-2v1c-2 1-3 1-5 1-3 1-7 1-10 1 1 1 4 1 6 0h1c1 0 3 0 4-1h3v1h-3c-7 2-16 1-23-1z" class="B"></path><path d="M310 377c0-2 1-3 2-5h1l1-2c4-4 13-12 20-12-4 2-8 5-11 7-5 4-9 8-13 12zm1-44l-1-1c2 0 2 0 2-1h-1c1-1 2-1 4-1 4-1 9-1 14-1 3 0 6 0 9 1l5 1h-10-4-4c-1 1-4 0-6 0-1 0-4 1-5 1h-2l-1 1h0z" class="G"></path><path d="M295 423v1h1c0-1 0-6 1-7 1 1 1 20 0 23 0 2 0 3 1 4l1 7-2-1c-1-1-1-2-1-3-2-7-2-17-1-24z" class="B"></path><path d="M423 445v-3c0-2 1-5 1-7h1v2 17c0 1-1 4-2 5h-1c0 1-1 2-1 3s-1 2-1 3l-1-1s1-1 1-2l1-2v-3c1-1 1-2 1-3-1 0-2 1-2 2v1-4l1-1v-2l1-1-1-1c1-1 1-2 2-3zm-129 6v-1c1-1 1-7 0-9-1-1 0-4 0-5s-1-2-1-3 0-3 1-4v-9c0-1 0-2 1-3v-1-2-1c0-1 0-1 1-2h0v2c-1 3-1 6-1 10-1 7-1 17 1 24 0 1 0 2 1 3l2 1v3c0 2 0 2-1 3-2-2-3-4-4-6z" class="D"></path><path d="M377 392l1-1c1 0 1-1 2-2s0 0 2 0l1-1c0-1 1-1 3-2h0c1-1 2-1 3-1h0 6 1v1c2 0 3 1 4 2h0-4-1-5-3l-1 1c1 0 2 0 3 1-1 0-2 0-3 1h-2l-1 1h-2c-1 1-2 1-3 1l-1-1zm47 91v1c-2 4-4 7-6 11h0c2-2 4-4 5-6l1 1-1 1c0 1 0 1-1 2 0 2-4 5-4 7l-2 1c0 2-1 3-2 4l-1-1 1-1-1-1-1 1h-1l2-3h0c1-1 3-3 3-4s-1-1-1-2c-1-1 0-1 0-3h2c3-1 6-5 7-8zm-62-133v-1h-2l-1-1h-1-1c-1 0-5-1-6 0-2 0-2 0-2 1h-2c-2 0-2 0-2 1l-1-1c-1 0-1-1-1-2 2-1 5-2 8-2 1 0 1 0 2-1l2 1h3 1c1 0 1 0 2 1h2c1 0 1 0 3 1 1 0 2-1 3 0 2 0 5 0 7 1 2 0 6-1 8 0v1c2 0 3 0 4 1v-1-1l1 1c1 0 1 1 2 1 1 1 2 1 3 2 1 0 1 1 2 1v-1h0c2 1 3 3 4 4-3-1-6-3-9-4h-2c-2-1-3-1-4-1-2-1-3 0-4-1-6-1-13-1-19 0z" class="B"></path><path d="M377 435c2 0 5 0 7 1l1 1c0 2-2 3-4 5-5 4-13 5-20 4-2 0-4-1-6-2-3-1-5-2-7-4h1 1 0v-1-1c1 1 3 1 4 1l3 1 1 1h1 2c1 1 1 1 2 1h3v1h6l3-1h1c1-1 2-1 3-2v-1c-2 1-5 0-7 0h-1v1h0l-2-1c1-1 4-1 5-1h1 1c1-1 1-1 2-1v-1h-1v-1z" class="C"></path><path d="M422 466h0 1l1-6c0-1 0 0 1-1l1 1c2 1 4 3 5 6v2 1l-1-1v-1l-2 1h1c1 1 1 2 0 3v1c-3 3-3 7-5 11h0c-1 3-4 7-7 8h-2c1-2 2-5 3-7 1-1 1 0 1-1h-2 0l1-2 1-2v-2h0l-1-2c1-3 2-7 4-9z" class="Q"></path><path d="M422 466h0 1l1-6c0-1 0 0 1-1l1 1c-2 7-4 13-7 19v-2h0l-1-2c1-3 2-7 4-9z" class="G"></path><path d="M429 468c1 1 1 2 0 3v1c-3 3-3 7-5 11h0c-1 3-4 7-7 8h-2c1-2 2-5 3-7 1-1 1 0 1-1h-2 0l1-2v1c1-1 2-3 3-4l1-2h1c1 0 1-2 2-3 1-2 2-4 4-5-1 2-4 7-4 9 1-3 4-5 4-9zm-86-137c2 0 8 2 9 1-3-2-8-2-11-3l-13-3c-4 0-9 0-13 1h-3-2l-3 1h-1c2-1 4-2 6-2 3-1 6 0 9-1 2-1 8 0 11 1 1 0 3 0 5 1l3 1h2c4 0 9 2 12 4l9 4h2c2 1 3 2 4 2l3 1c1 0 2 1 2 2l3 1 1 1s1 1 2 1h0c-1 1-1 1-2 0-1 0-4-1-5-1h-1c-2-1-5-3-7-3-1-1-2-1-3-1-1-1-1-1-2-1h-1l-5-1s-1-1-2-1h-2c-1 0 0 0-1-1-2 0-5-1-7-1h-4c-3-1-10 0-13 0-2 1-5 1-8 1-1 0-2 0-3 1h-1c-2 0-4 1-5 2l-1-1h0c1-1 1-1 2-1s2-1 3-1h0c1 0 2-1 2-1h2c2-1 5 0 7-1h2 2v-1c-2 0-2 0-3 1-2 0-5-1-6 0-2 0-5 0-6 1h0-3c-1 0-1 0-2 1h-1-2 0c1-1 1-1 2-1h1c1-1 1-1 2-1h2 0l1-1h2c1 0 4-1 5-1 2 0 5 1 6 0h4 4 10z" class="D"></path><path d="M431 458h1c1-1 1-2 1-4 1 2 1 5 1 7 1 8 1 16-1 23l-1 10c-1 1-1 2-1 4-1 4-3 9-5 12 0-4 2-8 4-12-5 5-8 11-12 16h0l6-9v-1l-2-1v-1h-1v-1h-1l-1-1h0-1c0-2 4-5 4-7 1-1 1-1 1-2l1-1-1-1c-1 2-3 4-5 6h0c2-4 4-7 6-11v-1h0c2-4 2-8 5-11v-1c1-1 1-2 0-3h-1l2-1v1l1 1v-1l1-1c0-3 0-6-1-9z" class="O"></path><path d="M423 489c1-3 2-5 3-7l1-2 2-6h0c0 3-1 4-1 7-2 6-3 12-6 19h0c2-3 4-7 5-10 0-2 1-4 2-6 0-2 0-4 1-6 0-1 1-3 1-4v10c-1 3-3 7-2 10h0c0 1-1 2-1 3 0 2-3 5-4 7l-2-1v-1h-1v-1h-1l-1-1h0-1c0-2 4-5 4-7 1-1 1-1 1-2l1-1-1-1z" class="D"></path><path d="M424 504v1l-6 9h0c4-5 7-11 12-16-2 4-4 8-4 12v1c-2 2-3 4-4 6v1c-1 0-1 1-2 1l-1 1c0 2-1 2-2 3 0 1-1 2-2 2 0 2-2 3-3 5l-4 5-1 2h-1l-2 3-1 1c-1 2-2 2-2 3l-3 3c0 1-1 2-2 3v-1-1h0l-3 1-1-1c-1-1 4-6 4-7v-1l-1 1v1l-1-1v-1c3-3 4-7 7-11l1-1c1-2 2-3 3-4s1-2 2-2c0-1 1-2 2-3 0-1 1-2 1-2l1-1 1-2 2-2 1-2 1-1 5-7h1v1l2 1z" class="B"></path><defs><linearGradient id="X" x1="424.545" y1="441.614" x2="435.992" y2="440.678" xlink:href="#B"><stop offset="0" stop-color="#353536"></stop><stop offset="1" stop-color="#565656"></stop></linearGradient></defs><path fill="url(#X)" d="M420 406c14 13 18 36 18 54 1 15-2 30-7 44-1-3 5-17 4-19-1 1-1 2-1 2-1 4-2 8-3 11 0-2 0-3 1-4l1-10c2-7 2-15 1-23 0-2 0-5-1-7 0 2 0 3-1 4h-1c1 3 1 6 1 9l-1 1v-2c-1-3-3-5-5-6l-1-1c-1 1-1 0-1 1l-1 6h-1 0l1-7c1-1 2-4 2-5v-17l1-1c1 0 1-1 2-3 0-2-2-8-3-10-1-6-3-11-6-16l1-1z"></path><path d="M428 433c1 4 3 9 3 13s1 9-1 13v1l1-2c1 3 1 6 1 9l-1 1v-2c-1-3-3-5-5-6l-1-1c-1 1-1 0-1 1l-1 6h-1 0l1-7c1-1 2-4 2-5v-17l1-1c1 0 1-1 2-3z" class="K"></path><path d="M428 433c1 4 3 9 3 13v1c-1 1 0 3-1 5 0 1 0 2-1 3v3l-1 1h-1c0-1 1-2 1-3v-2-1c1-3 1-7 1-10h-1v8h-1v-7c-2 3 0 7-2 10h0v-17l1-1c1 0 1-1 2-3z" class="Q"></path><path d="M362 350c6-1 13-1 19 0 1 1 2 0 4 1 1 0 2 0 4 1h2c3 1 6 3 9 4l2 2v1c-9-6-19-8-29-8-4 0-7-1-11 0-9 2-20 4-28 10-3 2-6 6-8 9l-3 4c-1-2 5-8 6-11-9 10-15 19-15 33 0 4 0 7 1 11l1 3c0 4 2 7 4 11h-1c-1-3-3-6-4-10v-1l-2-6c-1-5 0-11 0-16 1-4 3-10 5-13v-1c-3 3-3 6-4 10-2 7-3 14-2 22v4c0 2 1 5 2 7 4 13 14 27 25 36 3 3 8 6 12 8l12 3c7 2 16 3 23 1h3c1-1 3-1 4-1-5 3-13 3-19 4-5 0-9 0-13-1h-1l-3-1c-1-1-1-1-2-1 1 1 0 1 1 1l9 3v1c-11-3-19-8-28-14a57.31 57.31 0 0 1-11-11c-5-6-8-12-11-18-4-6-6-9-7-16v-1h0c-1 3 0 8 1 11 2 13 7 25 14 36v1l-2-4c-11-16-16-36-14-55 0-6 1-12 3-17-4 4-8 8-10 12v1h-1c1-5 8-9 8-13-4 4-7 8-9 12l-1-1c3-6 9-11 13-16 4-4 8-8 13-12 3-2 7-5 11-7 1-1 1-1 2-1 4-1 7-4 11-4h1c4-2 10-3 14-3z" class="L"></path><path d="M651 494h1c1-2 1-4 0-6 0-1-1-2-1-2-2-1-3-1-4 0-2 1-2 2-2 5v1c0 1-1 0-1 0-2 0-3-1-4-2 0-2-1-4 1-6 1-2 3-3 5-4 2 0 6 0 8 1 3 3 6 7 6 11 1 3 2 8 1 11l-3 12c-2 6-4 12-5 18-2 7-4 14-5 21-2 13-2 26-1 40 1 9 3 17 3 26 1 31-7 64-33 84-14 11-31 16-49 14-21-3-46-16-59-33-5-8-10-15-13-24 0 2 1 4 2 6l4 14 3 14c1 2 2 5 2 7-17-7-30-18-38-35-3-8-5-16-6-24l-1-17c-1-9-1-19-4-26-2 7-2 13-2 20 0 8 0 16 1 24 3 15 10 30 18 43 6 9 13 18 20 26l32 35c12 12 26 24 36 39 9 13 14 30 15 46 0 7-1 15-2 22-3 15-11 32-24 40l-1 1c-1 1-2 1-3 1l-2-2-1-2-2-2c-1-2 0-7 1-9 0-1 1-2 1-3 1-2 1-16 1-18-1 0-1-1-1-1v-1c-1-5-3-11-7-15l-1 1-2-2c-1-4-5-7-8-9-4-3-8-7-12-10-2-3-8-6-11-8v-4c-6-6-13-12-18-18-9-11-15-23-21-35-8-17-14-34-19-52-1-4-2-9-2-14-5-28-3-56 8-83 1-4 3-8 5-12-3-3-9-3-13-4-13 2-25 8-34 18-6 8-10 18-11 28l-1 2h-1v7l1 7c1 4 2 8 3 11 8 18 25 34 43 42 1 1 1 3 2 5l3 10-18-9c-14-7-28-17-38-30-11-14-17-31-18-49 0-10 0-20 3-29 4-14 11-25 19-36l32-40c7-9 14-17 17-27 3-6 3-12 3-18l2-2c0 9 5 13 11 19l3 3c-9 6-14 15-21 23 3-1 6-2 9-2 12-2 20 2 29 9 3 3 8 7 11 11-8-5-13-8-22-5l-17 6h0c3 4 5 9 6 13-12-4-25-3-37 2-15 7-25 17-31 33-9 22-9 45 1 66v2c1-2-1-5-1-6-1-3-2-6-2-9-4-25 4-54 26-68 15-10 33-14 50-10s33 14 42 28c19 29 9 70 34 95 7 7 14 11 23 13l5 1c2 0 5 0 7-1h3c-2 0-3-1-5 0s-6 0-8 0l-5-2c-2 0-4-1-6-2l-3-2c-1 0-1 0-2-1-5-3-11-7-14-13h1c3 3 6 6 9 8h1c-3-2-5-5-7-8 3 2 5 5 9 6l8 3c8 1 14 2 22 1 11-4 19-7 28-15-1 0-1 0-2 1-8 6-20 12-31 12-3 1-7 1-10 0-3 0-12-1-13-3 5 0 10 2 15 1h0c-3 0-8-1-11-2h0 4l5 1c2 0 4 0 6-1h1c-3 0-6 1-9 0-2-1-4-1-6-1l1-2h1c2 0 3 0 4 1 4 1 8 0 12-2 1 0 1 0 2-1-1 0-2 0-3 1h-3l-1 1h-1-6c2-2 5 0 8-2h-1-5c1-1 3-1 4-2l5-1c2 0 3-1 5-2l7-4 2-1h1l4-3h0c-2-2-1 0-4 0 2-1 3-2 3-4 1-2 3-3 4-5 4-5 7-10 9-16 5-13 4-28 4-42-1-8-1-17 0-25 2-26 10-53 26-74 4-5 10-11 16-13z" class="F"></path><path d="M403 685h2v1l6 6c1 2 5 7 7 8h1l-1 1v-1h-1l-2-1c-2-1-4-3-5-4l-2-2-1-2c-1-1-1-2-2-3s-2-2-2-3z" class="M"></path><path d="M527 783c3 0 4 1 6 3s5 5 6 8h0l-2-1h0c1 1 1 1 2 3l1 1c2 2 3 5 5 7l-1 1-4-6c0-1-1-1-1-2l-3-3c0-1 0-1-1-2l-1-1c-1-2-3-3-4-4-1-2-2-3-3-4z" class="B"></path><path d="M651 494c-1 4-7 8-10 11-1 0-2 2-3 2-1 1-2 1-3 0 4-5 10-11 16-13z" class="E"></path><path d="M557 883h3l-4 5s-1 1-1 2h-1c0 1-1 2-1 2-1 2-2 3-3 4h1c-1 1-2 1-3 1l-2-2 3-3c1-1 3-2 4-4s3-4 4-5z" class="I"></path><path d="M429 584c3 2 8 0 12-1 1-1 3 0 5-1s6 0 8 0l1 1c2 0 3 0 4 1h0-1c-4-1-11-2-15-1l-6 2c-2 0-2 1-3 2h-2 0-1l-4 1-6 3h0v-1l3-2v-2l3-1s1-1 2-1z" class="B"></path><path d="M568 854c1 4 1 6-1 10v3c1-1 2-2 2-3l1 1c-1 1-1 2-2 3 0 0 0 1-1 2h0c-1 1-1 1-1 2s-1 1-1 2l-1 1-1 2-3 6h-3c4-6 6-13 9-19 1-4 2-7 2-10z" class="G"></path><path d="M486 627c1 10 0 21 3 31-1 0-1 0-1-1l-6-18-1-4h1l2 6h0l-1-6h1v-4c0-2 1-2 0-3v-1l1 1 1-1z" class="K"></path><path d="M411 593v-1c1 0 1 0 1-1l1-1c1-1 2-1 3-1h0c3 0 5-3 7-4h1l3-1h1l10-3h1c1-1 2-1 3-1h5c3-1 6-1 9 0v1h-2c-1-1-3-1-5-1-1 1-4 1-5 1l-15 3c-1 0-2 1-2 1l-3 1v2l-3 2-1-1v1c-4 3-9 5-12 10 0-3 2-5 3-7h0z" class="D"></path><path d="M545 804l2 4 1 1h0v-3l-1-1v-1l1 1v-1-1s0-1-1-1l1-1c3 4 5 11 7 16v1c1 2 2 4 2 6-1 1-1 2-1 3l-2-4c0-1 0-2-1-3l-2-2-2-4-5-9 1-1z" class="E"></path><path d="M538 832c3 0 4 3 5 4l6 8c-2-7-5-11-9-17h1c1 2 5 8 8 8 1 3 1 5 2 8 1 6 2 12 1 18-1-2-3-4-3-6 0-3-1-8-2-10-3-5-6-8-8-12l-1-1z" class="I"></path><path d="M504 812c9 7 19 14 27 23 3 3 6 6 7 9l-1 1-2-2c-1-4-5-7-8-9-4-3-8-7-12-10-2-3-8-6-11-8v-4z" class="N"></path><path d="M540 837h0c-1-3-3-5-5-7l-12-12s0-1-1-2l-4-4h0l1-1 19 21 1 1c2 4 5 7 8 12 1 2 2 7 2 10 0 2 2 4 3 6 0 4-2 11-4 15 0-6 2-12 0-18-1-2-1-4-1-6-1-1-1-2-2-4-1-4-4-7-5-11z" class="Q"></path><path d="M558 696c8 1 14 2 22 1h0c-1 1-2 2-3 2v1c3 0 7 0 11-1h0c1 0 2 0 3-1l6-2v1c-1 1-2 1-3 1-2 1-5 2-7 3v1h-9-7-1-4c-1-1 0-1-1-1l-4-1h-2-1l-1-1v-1h1c0-1-1-1-1-2h1z" class="D"></path><path d="M655 506h1c0 1 0 1-1 2 0 2 0 4-1 6l-2 7c-1 1-1 2-2 3-1-1 0-1 0-2-1 0-2 0-2 1l-5 17v2l-1 1c0 1 0 1-1 1 0-1 0-2 1-3v-3h0c-1 0-1 1-1 1v1l-1-1c1-5 3-10 5-15 0-2 1-3 1-4s0-1 1-2l2-3v-1l1-1v-1c0-1 2-3 3-3v-1c1 0 1-1 1-1l1-1z" class="B"></path><path d="M499 768l-6-6c-1-2-3-3-5-5-1-3-4-5-5-8v-1l1 1v-1h0c-1-1 0 0 0-2h0v-1-1l2 2c3 5 9 9 13 13v1l3 3c0 2 1 2 2 3l-2 1c0-1-1-2-2-2h0l1 1c1 1 1 2 2 4-2-1-3-1-4-2z" class="G"></path><path d="M626 564c0-3 1-6 1-9l1-1c1-5 2-9 3-13 1-1 1-2 1-2 1-3 2-5 3-7l1 1c-1 2-3 5-4 8v1 1c1 0 1-1 2-1l1-4 2-5 1 1c-5 11-7 22-8 34 0 3 1 8 0 11-1 6 0 14 0 20-2-3-1-6-1-10l-1 2v1-5h-1 1v-2-10-6c0 1 0 1-1 2v-1c0-2 0-4-1-6 0 0 0-1 0 0z" class="E"></path><path d="M577 686c1-1 2-1 4-1 2-1 4-3 6-4 2 0 3-1 5-2v-1c1 0 2-1 3-2 4-2 7-5 10-8l1-1 1-1 1-1 1-1 2-3v2 1h-1c-1 2-3 5-5 6v1 1h0v1c-3 2-6 6-9 8-1 1-1 0-2 1s-1 1-2 1l-1 1h-1c-1 0-1 0-2 1-3 1-8 5-12 5h0c-2 0-2 0-3 1h-1 0c-3 0-6 1-9 0-2-1-4-1-6-1l1-2h1c2 0 3 0 4 1 4 1 8 0 12-2 1 0 1 0 2-1z" class="B"></path><path d="M630 599c0-6-1-14 0-20 0 15 1 29 1 44v10c-1 8-3 15-6 22-2 6-4 11-8 16h0l-1-1c0-1 1-1 1-2v-1c0-1 0 0 1-1l1-2c1-3 4-7 4-9 0 0-1 1-1 2l-2 2h0c-1-1 0-3 1-4l6-18c3-9 3-21 3-30 0-2 1-6 0-8z" class="H"></path><path d="M567 837c0-5 1-8 0-13v-7c0-1 0-2-1-3v-2-2h0c1 2 2 4 2 7l1 4c0 2 1 1 1 3 1 0 1 2 1 3 0 9-1 18-3 27 0 3-1 6-2 10-3 6-5 13-9 19-1 1-3 3-4 5s-3 3-4 4l6-11v-1c1-2 2-3 3-5v-1l2-4 1-2s0-1 1-1v-1-1c0-2 1-6 2-7v-1-3c1 0 1-1 1-1v-2c1-1 1-1 1-2v-3c1-3 1-6 1-9z" class="E"></path><path d="M502 763c1 0 1 0 2-1 0-2-8-9-9-12h1l11 12c1 1 0 0 0 2l13 12h0l1-1h0v1l2 2c1 2 3 3 4 5h0c1 1 2 2 3 4 1 1 3 2 4 4l1 1c1 1 1 1 1 2s1 2 1 3h1l-1 1-1 2c-1 0-1-1-2-2-1-3-4-4-6-6-2-3-4-6-6-8-3-3-7-6-10-10-2-2-5-6-8-8h0c-1-1-2-1-2-3z" class="D"></path><path d="M553 701c-1 0-1 0-2-1-5-3-11-7-14-13h1c3 3 6 6 9 8h1c-3-2-5-5-7-8 3 2 5 5 9 6l8 3h-1c0 1 1 1 1 2h-1v1l1 1h1 2l4 1c1 0 0 0 1 1h4 1 7 9l3-1c5-1 9-4 14-6l1 1c-2 0-2 1-3 2h0c2-1 4-1 6-2h0c-2 2-5 3-7 4-1 0-1 0-2 1 2 0 4-1 5-2h1v1h-1c-1 1-3 2-5 3h-1-2l-1 1h-2c-1 0-1 1-2 1h-1-2c-2 1-5 1-6 1-2-1-3-1-4-1-1 1-8 1-9 1l-5-1-1-1h-1-2c-2-1-3-1-4-3h-3z" class="I"></path><path d="M456 589c3 1 5 1 8 2h0c5 2 10 6 12 10 7 7 8 17 10 26l-1 1-1-1v1c1 1 0 1 0 3v4h-1l1 6h0l-2-6h-1c-1-10-3-20-8-29-3-5-9-11-14-14l-3-2v-1z" class="L"></path><path d="M482 635c-1-10-4-24-10-33-2-3-6-5-8-7l6 3v1c6 5 9 11 11 18 1 6 2 12 2 18l1 6h0l-2-6z" class="D"></path><defs><linearGradient id="Y" x1="483.289" y1="609.041" x2="477.751" y2="618.391" xlink:href="#B"><stop offset="0" stop-color="#797776"></stop><stop offset="1" stop-color="#90908f"></stop></linearGradient></defs><path fill="url(#Y)" d="M470 599c3 0 3 1 5 2 0 1 0 2 1 2 0 1 1 2 2 3v-1c-1-1-2-3-2-4 7 7 8 17 10 26l-1 1-1-1v1c1 1 0 1 0 3v4h-1c0-6-1-12-2-18-2-7-5-13-11-18z"></path><path d="M626 564c0-1 0 0 0 0 1 2 1 4 1 6v1c1-1 1-1 1-2v6 10 2h-1 1v5-1l1-2c0 4-1 7 1 10 1 2 0 6 0 8 0 9 0 21-3 30l-6 18h-1c0-1 0-1-1-2h0l3-8v-2-1c1-2 1-4 1-5 2-11 2-22 1-32 0-14 0-28 2-41z" class="I"></path><path d="M506 776h1l11 11c2 1 3 3 5 5l5 5 8 11c1 1 3 5 4 5l3 2c3 4 4 7 6 11 0 1 0 2-1 3 3 9 6 20 6 29-1 4-2 9-3 12-1 5-3 9-3 14v1l3-7 5-17c0 3-1 6-2 9s-1 7-2 10c-2 5-5 9-7 13l-2-2c-1-2 0-7 1-9 0-1 1-2 1-3 1-2 1-16 1-18-1 0-1-1-1-1v-1l1-1c-2-10-5-16-11-24-5-7-12-12-18-17l1-1c8 6 15 14 22 21 1 4 4 7 5 11 1 2 1 3 2 4 0 2 0 4 1 6 2 6 0 12 0 18 2-4 4-11 4-15 1-6 0-12-1-18-1-3-1-5-2-8-5-18-20-35-33-49-3-3-8-7-10-10z" class="P"></path><path d="M540 813l3 2c3 4 4 7 6 11 0 1 0 2-1 3l-8-16z" class="O"></path><path d="M619 653h0c1 1 1 1 1 2h1c-1 1-2 3-1 4h0l2-2c0-1 1-2 1-2 0 2-3 6-4 9l-1 2c-1 1-1 0-1 1v1c0 1-1 1-1 2l1 1h0c-2 3-4 6-7 9 0 1-1 2-2 2s-1 0-2 1c-8 6-20 12-31 12-3 1-7 1-10 0-3 0-12-1-13-3 5 0 10 2 15 1h0 2 1l3-1c1-1 2-1 3-1h1c1 0 2 0 3-1l5-2h2l3-1c0-1 0-1 1-1 1-1 3-2 4-2 1-1 2-2 3-2 3-2 7-7 10-10 3-5 7-8 9-14l2-5z" class="E"></path><path d="M575 695l8-3c1 0 3 0 4-1 3-3 7-4 10-6 8-5 14-12 20-18v1c0 1-1 1-1 2l1 1h0c-2 3-4 6-7 9 0 1-1 2-2 2s-1 0-2 1c-8 6-20 12-31 12z" class="Q"></path><defs><linearGradient id="Z" x1="549.796" y1="861.815" x2="563.884" y2="858.001" xlink:href="#B"><stop offset="0" stop-color="#9fa19b"></stop><stop offset="1" stop-color="#d4cfcc"></stop></linearGradient></defs><path fill="url(#Z)" d="M555 818h1v1c1 1 1 2 1 3s1 2 1 2c0 2 1 4 1 6l1 1v3c0 1 0 2 1 3v6 1c1-3 1-9 0-11v-3c0-1-1-1-1-1v-2-2-2l-1-1v-1-1c0-1 0-1-1-1 0-2 1-5 0-6 0-1 0-1-1-2v-3h0l1 2c0 1 1 2 1 2v1 2l2 1v1h0v2c1 1 1 1 1 3h0v1 1c1 2 1 0 1 2v1 2 2c1 2 0 5 1 7 0 1 1 4 0 5v4l-1 1c0 1 1 1 0 2 0 2 0 2-1 4v5c1-2 1-3 1-4l1-1v-1l1-1-1-1 1-1v-2c0-1 0-2 1-3v-2c1-2 0-4 1-6 0 3 0 6-1 9v3c0 1 0 1-1 2v2s0 1-1 1v3 1c-1 1-2 5-2 7v1 1c-1 0-1 1-1 1l-1 2-2 4v1c-1 2-2 3-3 5v1l-6 11-3 3-1-2c2-4 5-8 7-13 1-3 1-7 2-10s2-6 2-9v-1c1-2 1-7 1-10 0-1 0-2-1-3 0-2 0-4-1-6 1-3 1-6 1-9l-1-2c0-1 0-3-1-5l-1-1 1-1 2 4c0-1 0-2 1-3 0-2-1-4-2-6z"></path><path d="M556 827c0-1 0-2 1-3 1 4 2 9 3 14 0 2 0 4-1 6h0v1 1l-3-19z" class="I"></path><path d="M555 841c1-3 1-6 1-9l-1-2c0-1 0-3-1-5l-1-1 1-1 2 4 3 19c-1 1-1 2-2 3v1c0-1 0-2-1-3 0-2 0-4-1-6z" class="H"></path><path d="M433 588c7-1 17-3 23 1v1l3 2c5 3 11 9 14 14h-2l-1-1h0l-1 1 2 4c1 0 1 1 1 2v1c1 1 1 2 2 4v2c1 2 1 3 1 5 1 1 1 3 1 4v2c1 1 1 2 1 3s0 2 1 3v2l2 7-1 1c0-1-1-2-1-4v-1c0-1-1-1-1-2l-1-6c0-1 0-2-1-3l-1-8c0-1 0-2-1-2l-1-5c-1-2-1-3-2-4h-1v1c1 0 1 0 1 1v1c1 1 1 2 1 3 0 2 1 3 1 6l2 6 1 8v3 2l-1-3v-1c-1-1-1-2-1-3v-5c-1-1-1 0-1-1 0-2 0-6-1-8v-1c-1-2-1-4-2-6l-1-1v1 2c2 6 4 14 3 21v2l2 10c1 1 1 1 0 2v-1l-1-3c-1-1-1-1-1-2h0v-1 3 1c1 3 4 9 3 12h0c0-1-1-2-1-3l-2-6-1-3c0-1 0-3-1-4v-4c-1-5-1-9-2-13v-4c-1-3-1-6-1-9-1-2 0-5-1-6v-1c-1-2-2-4-3-5v-1c-1-2-3-4-5-4 1 0 0 0 1 1h-1c-3-3-9-3-13-4h0v-2l-1-1c-4-1-11 1-14 3h0l1-1c1-1 3-2 4-4l-1-1h0z" class="E"></path><path d="M475 642v-2-3l-1-8-2-6c0-3-1-4-1-6 0-1 0-2-1-3v-1c0-1 0-1-1-1v-1h1c1 1 1 2 2 4l1 5c1 0 1 1 1 2l1 8c1 1 1 2 1 3l1 6c0 1 1 1 1 2v1c0 2 1 3 1 4l1-1-2-7v-2c-1-1-1-2-1-3s0-2-1-3v-2c0-1 0-3-1-4 0-2 0-3-1-5v-2c-1-2-1-3-2-4v-1c0-1 0-2-1-2l-2-4 1-1h0l1 1h2c5 9 7 19 8 29l1 4 6 18c0 1 0 1 1 1 2 2 2 9 2 12h0c-1-1-1-1-1-2l-1 1v-1 3c1 1 1 2 2 3h-1-1c1 1 1 1 1 2l-1-1v1 1h0l-3-3h0c-1-2-2-3-3-5h0c-1-2-2-4-2-5h0l-1-2-1-2c0-2 0-3-1-4v-3c-1-1-2-4-2-5v-1c-1-2-1-1-1-2v-3zm22-46h0l2 2 1 1c0 1 1 1 1 2l3 6c1 0 1 1 1 2 1 1 1 1 1 2l2 6v3c1 3 1 6 2 9 0 1-1 3 0 5v6c1 4 1 9 2 13v3l1 1v2 1l1 5 3 7c0 1 1 2 1 3v1l4 6v1l1 1 1 1c1 2 2 3 3 4s3 3 4 5v1c0 1 4 3 4 5l-2 1c-2-2-3-3-5-4h-1c-3-2-5-4-7-7 0-1-1-3-2-4-2-1-4-2-5-4 0-1 0 0-1-1 0-1 0-1-1-2h1 1v-1s0-1-1-1c-2-2-2-5-4-7l1-1 3 3c0 1 0 1 1 1 0-1 0-2-1-3-2-4-3-8-5-12 0-1 0-3-1-5v-3c-1-6-1-13-1-19-1-2 0-3-1-5v-1-2l-1-5c-1-1-1-2-1-3v-1c-1-1 0-1-1-2l-1-4c-1-3-3-7-5-9v-1-1l1 1h0l1-2z" class="B"></path><defs><linearGradient id="a" x1="409.723" y1="639.821" x2="428.126" y2="585.776" xlink:href="#B"><stop offset="0" stop-color="#89888a"></stop><stop offset="1" stop-color="#bab9b7"></stop></linearGradient></defs><path fill="url(#a)" d="M421 590v1h0l6-3 4-1h1 0l1 1h0l1 1c-1 2-3 3-4 4l-1 1h0c3-2 10-4 14-3l1 1v2h0c-13 2-25 8-34 18-6 8-10 18-11 28l-1 2h-1v7c-1-5-1-11-1-16 1-11 3-21 9-31 1-3 3-6 6-9h0c-1 2-3 4-3 7 3-5 8-7 12-10v-1l1 1z"></path><defs><linearGradient id="b" x1="405.253" y1="602.574" x2="417.317" y2="594.762" xlink:href="#B"><stop offset="0" stop-color="#8d8c8c"></stop><stop offset="1" stop-color="#b5b4b3"></stop></linearGradient></defs><path fill="url(#b)" d="M421 590v1h0l6-3 4-1h1 0l1 1h0l1 1c-1 2-3 3-4 4-2 0-2 0-3 1h-2c-2 1-3 1-4 2v-1c-1 0-2 1-3 1h-1l-1 1-1-1c-3 2-6 5-7 8 0 1-1 2-2 3l-2 3c1-2 2-5 3-8l1-2c3-5 8-7 12-10v-1l1 1z"></path><path d="M433 588l1 1c-1 2-3 3-4 4-2 0-2 0-3 1h-2c-2 1-3 1-4 2v-1c-1 0-2 1-3 1h-1c3-4 11-6 16-8z" class="O"></path><path d="M503 770c-1-2-1-3-2-4l-1-1h0c1 0 2 1 2 2l2-1h0c3 2 6 6 8 8 3 4 7 7 10 10 2 2 4 5 6 8 2 2 5 3 6 6 1 1 1 2 2 2l1-2 1-1h-1c0-1-1-2-1-3l3 3c0 1 1 1 1 2l4 6 5 9 2 4 2 2c1 1 1 2 1 3l-1 1 1 1c1 2 1 4 1 5l1 2c0 3 0 6-1 9 1 2 1 4 1 6 1 1 1 2 1 3 0 3 0 8-1 10v1l-5 17-3 7v-1c0-5 2-9 3-14 1-3 2-8 3-12 0-9-3-20-6-29 1-1 1-2 1-3-2-4-3-7-6-11l-3-2c-1 0-3-4-4-5l-8-11-5-5c-2-2-3-4-5-5l-11-11h-1c-3-2-5-5-7-8 1 1 2 1 4 2z" class="I"></path><path d="M503 770c-1-2-1-3-2-4l-1-1h0c1 0 2 1 2 2l2-1h0c2 3 6 7 8 10l-6-3c0-1-2-3-3-3z" class="B"></path><path d="M523 792c8 3 16 16 20 23l-3-2c-1 0-3-4-4-5l-8-11-5-5z" class="H"></path><path d="M540 799l4 6 5 9 2 4 2 2c1 1 1 2 1 3l-1 1 1 1c1 2 1 4 1 5l1 2c0 3 0 6-1 9 0-4-1-8-2-12s-4-8-6-13c-1-1-1-4-3-5-1-1-2-3-3-4v-1c1 1 2 3 4 4h0l-2-5-1-2c-1-1-1-2-2-4z" class="O"></path></svg>