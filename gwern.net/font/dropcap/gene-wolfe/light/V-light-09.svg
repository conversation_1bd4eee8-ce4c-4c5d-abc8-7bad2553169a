<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="105 78 848 864"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#b5b4b4}.C{fill:#b0afaf}.D{fill:#030202}.E{fill:#d9d8d8}.F{fill:#191818}.G{fill:#e5e5e4}.H{fill:#d6d5d5}.I{fill:#2d2c2d}.J{fill:#cfcece}.K{fill:#959494}.L{fill:#c2c1c1}.M{fill:#222221}.N{fill:#141313}.O{fill:#a6a5a5}.P{fill:#807f7f}.Q{fill:#1d1d1d}.R{fill:#0a0a0a}.S{fill:#454445}.T{fill:#8b8a8a}.U{fill:#edecec}.V{fill:#2f2e2e}.W{fill:#f5f4f4}.X{fill:#606060}.Y{fill:#676666}</style><path d="M719 148h3v2h2l-1 1-3-1-1-2z" class="I"></path><path d="M619 811c-1-1-2-3-1-5h1c0 2 1 2 2 3l-1 1-1 1z" class="Q"></path><path d="M646 741c1 0 1 0 2 1v1l-1 1h-2c0-2 0-2 1-3z" class="R"></path><path d="M744 149l6-1 1 1-1 1c-2 0-4 1-6 1v-1h1v-1h-1z" class="Q"></path><path d="M712 147l7 1 1 2-12-1c2-1 3-1 4 0v-2z" class="F"></path><path d="M608 795h1c0 3 0 6-1 8v4l-1 2v2l-1 1h-1v-2s0-1 1-1v-3h0l1-1v-1-1-2h1c-1-2 0-4 0-6z" class="G"></path><path d="M244 159c-3 1-6 2-9 2-2 1-5 2-7 2v-1c1-1 1-2 3-3l1 1c4 0 8-1 12-2v1z" class="N"></path><path d="M580 873l1-1c1-1 2-1 4-2 0 3 1 7 3 9-2-1-6-4-8-6zm145-704c2 1 4 1 6 3 1 0 2-1 3 0-1 1-1 1-1 2 0 2 0 2-1 4-1 0-2-2-3-3v-1l-1-1c0-1-1-2-2-3h0l-1-1z" class="C"></path><path d="M759 170h-2c-1-1-3-1-4-2v-1c-2-1-2-1-3-2l2-2h2l1 1c1 1 2 1 3 2s1 1 1 2v2z" class="H"></path><path d="M736 164l-9-5c1-1 4 1 5 1 8 4 16 11 25 13l1 2-1-1c-1 0-2 1-2 1-6-5-12-8-19-11z" class="M"></path><path d="M726 140h1c2 0 6 2 7 4v1h-7c-2 1-4 1-7 2l8-5h0l-2-2z" class="J"></path><path d="M291 536h0c-1 0-2-1-3-2 0-2 0-2 1-4h1c0 1 0 2 1 3v1c1 1 2 1 4 2h1 2v1h2 3 3l1-1h2 0 1l1-1h2c1-1 0-1 1-1h1 0c-6 3-13 5-20 3v-1h-3-1z" class="H"></path><path d="M750 148l7-2h1c-1 3-8 8-11 10 0-1 0-2-1-2 2-1 4-2 4-4l1-1-1-1z" class="I"></path><path d="M692 840l5 1c1 1 2 2 2 3-1 2-1 2-2 2h-2c0-2 1-2-1-4-3-1-7 0-10 1l-1-1c2 0 3-1 4-2h1 4z" class="F"></path><path d="M677 559l2 2c1 3 1 6 2 9v5c-2-3-2-7-4-9-1-1-2 0-4 0 1-3 3-5 4-7z" class="B"></path><path d="M796 192c1 1 3 0 4 1 1 0 5 0 6-1 1 0 2 0 3 1v-1h5l1 1h1c2 2 4 2 6 2h-16l-2-1h-1l-8-1 1-1z" class="H"></path><path d="M454 855l1 1c-4 6-8 11-15 13l1-1h1c3-2 6-4 8-7v-2h-2l-2 1-1 1c-1 0-2 0-3 1h-2 0l-1 1h-1 0-1-1-2c3-1 7-2 10-3 4-1 7-3 10-5z" class="C"></path><path d="M403 726v-2c0-1 0-2 1-3 0 7 2 13 7 18 3 2 6 3 10 4h0c-3 4-4 9-4 13l-1-3v-2h1v-2-1l1-1v-1c0-1-1-2-2-3v-1h-1-1c-1-1-2-1-3-2-2-2-5-5-6-9v-1l-1-1c0-1 0-2-1-3z" class="L"></path><path d="M786 163h1c1 1 2 1 4 0h2c-2 1-2 2-3 3 0 3 0 7 1 9 2 4 4 5 7 6 1 0 1 0 1 1-5-1-10-4-11-9 0-2 0-6-1-8 0 0-1-1-1-2z" class="D"></path><path d="M197 172l1 1 1 1c4 2 7 8 8 11v1c1 1 1 0 1 2h-2c-1-1-2-3-3-4-3-4-4-8-6-12z" class="E"></path><path d="M506 89l1 1-2 4h1c-3 7-10 17-18 19h-1c1 0 1-1 2-1 1-1 2-1 3-2-2 1-4 2-6 1h-1 2c10-3 15-14 19-22z" class="I"></path><path d="M251 168c-3-2-4-3-8-3-1 1 0 4 0 6s-1 5-2 7c-2 2-5 3-8 3 2-1 4-1 5-3 3-3 2-9 2-13l-4-1c3-1 6-1 9-1 1 0 2 1 4 2 0 1 1 2 2 3z" class="F"></path><path d="M754 163c4 1 8 2 12 4h-1-1l-1 1c1 1 3 2 4 3 3 2 6 4 8 6-2-1-12-6-14-5-1 1-2 1-3 3l-1-2 3-3h-1v-2c0-1 0-1-1-2s-2-1-3-2l-1-1z" class="I"></path><path d="M362 853h0c1-2 2-4 5-5 0 0 1 0 1-1-4-2-10-5-15-5-4 1-4 3-6 5l-1-1c0-2 0-2 1-4 3-2 5-2 8-3 0 1 1 1 2 2s1 1 3 1l6 3c2 0 3 2 5 1h5l-1 1c-5 0-9 4-13 6z" class="F"></path><path d="M778 169c2 2 3 4 4 5 5 8 10 13 20 14 4 1 8 1 12 2h0c-7 2-19-1-24-4-7-4-10-10-12-17z" class="M"></path><path d="M395 859c5 2 10 3 16 4l-3 1h-1c-1 0-2 0-4 1h0l-3 1c-1 1-1 1-2 1h0c-1 1-1 1-1 2s-1 1-1 1h-1c-1 1-2 1-3 2h0-5 2l2-1c1 0 1 0 2-1 0-1 1-1 1-2h1v-2l-1-1c-1-1 0-2 0-4 1-1 1-1 1-2z" class="E"></path><path d="M244 158h0c3-1 4-4 6-5 1-1 5-1 6 0 2 0 3 1 4 2 0 2 0 3-1 4 0 1-1 1-1 2v1h5l-1 1c-3 0-5 0-8-1-2-1-3-2-4-4l-1-1-5 2v-1z" class="D"></path><path d="M253 154h3c1 1 1 2 2 3 0 2 0 2-2 4-1 0-2-1-3-1-1-1-2-2-2-3 1-2 1-2 2-3z" class="U"></path><path d="M738 155h5c1 0 2-1 3-1s1 1 1 2h0c9 1 18 4 25 9 0-1 1-1 1-2v-1h1c2-2 4-4 5-7v1 1c1 1-4 6-4 8 3 2 7 4 10 7-2-1-5-2-7-3-3-2-5-2-9-2h-1v-1h3v-1c-5-4-13-6-19-7-5-1-9-1-14-2v-1z" class="F"></path><path d="M642 175c1-1 1-2 2-3 6-7 16-11 25-11 9-1 17 1 24 7l-2 1c-8-6-15-7-24-6-10 1-17 4-23 12h-2z" class="D"></path><defs><linearGradient id="A" x1="327.056" y1="535.349" x2="303.827" y2="532.268" xlink:href="#B"><stop offset="0" stop-color="#959595"></stop><stop offset="1" stop-color="#bfbdbc"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M328 527c0 2 0 3 1 5l-1 2c-2 0-4 1-5 1-3 1-6 4-8 4-1-1-2 0-3 0s-2 0-3 1h-4l-14-4h1 3v1c7 2 14 0 20-3 3-1 6-2 8-4 2-1 4-1 5-3z"></path><path d="M362 853c4-2 8-6 13-6 2 0 2 1 3 2l5 4c-1 1-2 2-3 2h0c1 1 0 1 1 0 2-1 5-1 8 0 2 1 4 3 6 4 0 1 0 1-1 2v-1l-1 1c-2 0-3 0-4-1h0c-2-1-3-1-3-3h-1c-1-1-1-1-2-1-1 1-1 0-2 0l-1 1v-1c-1 0-1 0-2 1h-1l-2 1v1l-1-1h0c1 0 1-1 2-2l1-1c1 0 1 0 2-1-2 0-5 0-7-1 1 0 2-1 3-1l1-1h1l-1-1c-1 0-2-1-3-2h-1c-1 1-1 1-2 1-1 1-1 1-2 1l-3 3h-1v1l-2-1z" class="C"></path><defs><linearGradient id="C" x1="521.618" y1="79.82" x2="505.383" y2="79.363" xlink:href="#B"><stop offset="0" stop-color="#110f0e"></stop><stop offset="1" stop-color="#2d2d2d"></stop></linearGradient></defs><path fill="url(#C)" d="M506 89c4-9 7-18 10-27l7 22h-1l-1-1h-1 0l-5-11c-2 7-4 16-9 22h-1l2-4-1-1z"></path><path d="M773 163l-2-2c-1-1-2-3-2-4s0-2 1-2c6-6 16 2 22 4 3 1 5 1 7 1 1 0 1-1 2-1 1 1 1 1 1 3-1 1-6 1-8 1h0-1v-1c-4-2-9-5-14-6v-1c-1 3-3 5-5 7h-1v1z" class="D"></path><path d="M773 162v-1c-1-2-2-3-2-4 0-2 1-2 2-3 2 0 4 1 6 1-1 3-3 5-5 7h-1z" class="U"></path><path d="M722 148c7 1 14 1 22 1h1v1h-1v1c2 0 4-1 6-1 0 2-2 3-4 4-1 0-2 1-3 1h-5v1c-5-3-10-4-15-5l1-1h-2v-2z" class="V"></path><path d="M750 150c0 2-2 3-4 4-1 0-2 1-3 1h-5c-1-1-5-2-6-3h2c3 0 7 0 10-1h0c2 0 4-1 6-1z" class="H"></path><defs><linearGradient id="D" x1="869.05" y1="248.668" x2="867.007" y2="269.741" xlink:href="#B"><stop offset="0" stop-color="#979695"></stop><stop offset="1" stop-color="#c0bfbf"></stop></linearGradient></defs><path fill="url(#D)" d="M857 246c2 2 4 3 7 5 5 4 10 10 14 16 1 0 2 1 2 1 2 1 2 2 2 4 1 1 1 0 1 1l1 2c0 1 0 2 1 3v2h1c0 1 0 2-1 3v-1h-1c-1-1-1-3-2-4v-1c-1-2-3-4-4-7l-3-3 1-1c-4-4-8-9-13-12h0c-2-2-4-2-6-2l2-1h0-2c-1-1-1-2-2-3l2-2z"></path><path d="M378 731c1 1 1 1 2 3l1 1v1c0 1 2 3 3 4h0c1 1 1 2 3 2 1 2 1 2 3 3v1c1 0 2 1 2 1l3 1c1 1 3 2 4 3h1 4v1l1-1h4c2 0 1 0 2 1h2v1h-2l-1 1c-1 0-2-1-3 0h-6c-2-1-2 0-4-2h0-1c-1-1-2-1-3-1-7-4-13-8-17-14v-1-1h2c1 1 0 1 1 0 0-1-1-3-1-4z" class="H"></path><path d="M267 161l18-4c-5-3-10-7-15-11 4 0 8 2 11 3-1 0-1 1-2 1h0l12 2h6v1l1 1c-3 1-7 3-10 4-4 1-8 1-12 2-3 0-6 1-9 1z" class="Q"></path><path d="M279 150l12 2h6v1c-2 0-4 1-5 2l-3 1c-2 0-2 0-3-1-3-1-5-3-7-5z" class="E"></path><path d="M291 152h6v1c-2 0-4 1-5 2-1-1-2-1-3-1l2-2h0z" class="B"></path><path d="M657 852l4 1h0l2-1s-1-1-1-2c1 0 2 0 3-1l5-2h2l1-1v1l1 1h1l-2 1v1l-1-1v1h-1c-2 1-3 2-4 3h-2c1 2 2 2 4 3v1 1c1 0 1 1 1 2s1 2 1 4h0c-1-1-1-1-1-2l-1-1-3 3 1 1-1 1-2-4c-1-1-6-4-6-6h0c-2-1-3-1-5-1l4-3h0z" class="J"></path><path d="M657 852l4 1h0l2-1s-1-1-1-2c1 0 2 0 3-1l5-2h2l1-1v1l1 1h1l-2 1c-2 0-6 1-8 2-1 1-1 2-2 3h-3c-1-1-2-1-3-1v1c1 1 1 1 1 2h0c-2-1-3-1-5-1l4-3h0z" class="C"></path><path d="M666 845c7-3 18-8 26-5h-4-1c-1 1-2 2-4 2l1 1-8 5h-1-1l-1-1v-1l-1 1h-2l-5 2c-1 1-2 1-3 1 0 1 1 2 1 2l-2 1h0l-4-1h0 0c0-1 1-2 2-2l1-1h0 1v-1c2-1 1 0 2-1v-1l1 1c0-2 0-2 1-3h0l1 1z" class="B"></path><path d="M676 848c-1-1-1-1-1-2s7-3 8-4l1 1-8 5z" class="X"></path><path d="M666 845c7-3 18-8 26-5h-4-1c-4 1-8 2-12 4-7 2-12 3-18 8h0 0c0-1 1-2 2-2l1-1h0 1v-1c2-1 1 0 2-1v-1l1 1c0-2 0-2 1-3h0l1 1z" class="R"></path><path d="M706 536c6 0 11 1 17-1 2-2 3-3 4-6 0 1 1 2 0 3 0 2-2 4-4 5-1 0-2 1-3 1h0c-4 1-7 1-10 2l-2 1-1-1h-1 0-1l-1-1h-1l1 1h1c2 1 3 2 5 3 1 1 3 1 4 2-3 0-7-2-11-4l-1-1-2-1c-1-1-4-3-6-4l-1-1s-1 0-1-1h-1c-1 0-1-1-2 0h0c0 1 0 1-1 2h0v1l-6 13v1c-1 0-1 1-1 2s0-1-1 1h0c0 3-1 4-1 7 1 1 0 2 1 3 0 1 0 1 1 3v2h0c1 1 1 1 1 2h1c0 1 0 1 1 1 1 1 1 2 2 4l-5-5c-1-3-1-6-2-9l-2-2 12-28h0c4 2 9 7 13 7l5 1c1 0 1 0 2 1l1-1v-1h-1c-1-1-2-1-3-2z" class="E"></path><path d="M767 171h4 1c1-1 2-1 3 0v1c-2-1-3-1-4-1l-1 1 1 1c2 1 3 2 4 4h0c-1 1-1 2-1 3 1 1 2 1 2 3h1 0c1 1 3 2 4 4h0c3 2-1-1 2 1l1 1h2l4 1 1 1c1 0 1 0 2 1v-1c1 0 2 1 3 1l-1 1c-9-2-20-6-26-12-1-2-4-3-6-4l-4-2h-1c1-2 2-2 3-3 2-1 12 4 14 5-2-2-5-4-8-6z" class="B"></path><path d="M857 252c2 0 4 0 6 2h0c5 3 9 8 13 12l-1 1h0l-3-3c-1-2-2-3-4-4h-1 0c-1 1-1 1-3 2h0c-1 1-1 1-2 1l-1 1c0 2 0 0 1 2v1l1 1v1l2 6c0 1 0 3 1 4 0 2 0 4-1 6v2 1c-1 1-1 6-1 8l-3-16c0-1 0-1-1-2v-2-1l-1-2h1c0 2 1 3 1 5l1 2v2c1 1 1 2 2 3v-3c2-4-3-17-5-21-1-2-4-6-4-8l2-1z" class="L"></path><defs><linearGradient id="E" x1="340.132" y1="581.355" x2="317.748" y2="593.931" xlink:href="#B"><stop offset="0" stop-color="#a5a4a4"></stop><stop offset="1" stop-color="#d2d1d0"></stop></linearGradient></defs><path fill="url(#E)" d="M333 574c1 0 3-1 5-1-2 4-2 9-4 13l-1 2v2c-1 0-1 1-1 2h0v1c-1 1-1 1-1 2-1 0-1 0-1 1l-2 2c0 1-1 1-1 2-3 2-6 4-10 5 4-3 8-8 9-13h1c0-3 1-6 1-8h0c1-1 1-2 1-2 1-4 2-5 4-8z"></path><defs><linearGradient id="F" x1="677.475" y1="582.877" x2="700.193" y2="591.689" xlink:href="#B"><stop offset="0" stop-color="#bfbebe"></stop><stop offset="1" stop-color="#f4f3f3"></stop></linearGradient></defs><path fill="url(#F)" d="M681 570l5 5 1 2c1 9 3 17 9 24l1 2v1l-2-1c-5-3-8-6-10-11-2-3-5-9-4-12v-5h0v-5z"></path><defs><linearGradient id="G" x1="851.593" y1="244.12" x2="857.133" y2="285.845" xlink:href="#B"><stop offset="0" stop-color="#8a8889"></stop><stop offset="1" stop-color="#bab9b9"></stop></linearGradient></defs><path fill="url(#G)" d="M843 248c3-3 7-4 11-5l3 3-2 2c1 1 1 2 2 3h2 0l-2 1-2 1c0 2 3 6 4 8 2 4 7 17 5 21v3c-1-1-1-2-2-3v-2l-1-2c0-2-1-3-1-5-3-8-11-20-17-25z"></path><path d="M341 694c9 4 17 4 26 0h2c0 2 0 4-1 5h1v1c0 7-1 14 1 21l-1 2v-2c-1-2-1-2-1-3v-3h-1c-1-1-1-1-2-1s-2-1-3-1v-1c-1 0-2-1-2-2l-1-1-1-1c-1-1-1-1-1-2v-1l-1-2h0c-1-2-1-1-1-2v-1-2h-2c-2 0-3-1-5-1h-1c-1 0-2-1-3-1h0c-1 0-2-1-3-2z" class="C"></path><path d="M485 111h1c2 1 4 0 6-1-1 1-2 1-3 2-1 0-1 1-2 1l-2 1-1 1 3 4v2l-1 1 2 6 6 9 7 7c4 4 7 9 8 14l-1 2c-1-4-2-7-5-10-5-7-10-11-19-13l1-1c1-1 1 0 1-1-3-8-7-16-8-25l7 1z" class="F"></path><path d="M486 122c-1-2-3-6-3-8h2l-1 1 3 4v2l-1 1z" class="E"></path><path d="M283 149c2-1 6 0 8-1v-5h1c0-3 1-6 3-7 2-2 3-2 5-2 0 2 0 4 1 6v1h0c2 3 5 4 7 6h0c-1 0 0 0-1 1h1 6v1h1 0l-17 5-1-1v-1h-6l-12-2h0c1 0 1-1 2-1h2z" class="G"></path><path d="M308 148h6v1h1 0l-17 5-1-1v-1h-6l-12-2h0c1 0 1-1 2-1h2 11c5 0 9 0 14-1z" class="M"></path><path d="M115 235l1 1c3 4 5 9 7 14l1-1c5-6 12-8 20-9 4 0 8 0 12 1 1 0 0 0 1 1l-2 1 1-1c-1-1-1-1-2-1l-3 1c-4 2-8 8-11 12v1c-1 0-1 0-2 1l-2 4c0 1 0 1-1 2v1c0 1-1 1-1 3v1c-1 1-1 1-1 2v1 3c-1 1-1 3-1 4v-4-3l1-3 1-3h0v-2l1-2c0-1 1-1 1-2v-1-1h-1l-1-1c-1-1-1-2-2-3 0-1-2-3-3-4h-2l-3 3h-1c-1-1-1-2-2-3v-1l-1-1c0-1-1-1-1-2l-2-4c0-1 0-1-1-2l-1-2v-1z" class="C"></path><path d="M383 803v1 2h-1v1c0 1 0 1-1 2h0v5c-1 1-1 1-1 2v1 5 1c1 1 1 2 1 2 1 1 0 1 1 1h1c1 0 2 1 2 1h1c1 1 1 0 2 1h1 1c1 0 2 0 2 1h2 1c1 1 2 1 3 1 0 1 1 1 2 1 0-1-1-2-2-3h0v-1-2l2-1v1h1 0c1 1 2 1 2 1 1-1 1-2 2-3l1-2-1-1h0l1-1 1 1h0c3 1 3 3 6 2 1-1 1-1 2 0 1 0 1 0 2 1v2 2 1c0 1 0 2 1 3 0 0 0 1 1 2 1 0 1 1 3 1v-1c0-1 0-1 1-1l-1-1c0-1 0-1 1-1v-1l-3-4c0-1 0-1-1-2h0v-1c-1-1-1-2-1-4h-1v-4h1l1-1 1 2h0 3v1c-2 0-3-1-5-1 0 3 1 5 3 8 1 2 3 5 3 8l-1 2c0 1-1 2-1 2-2 0-7-2-8-3l-2 1-9-3c1 1 1 3 1 4-1 1-1 1-2 1v-1c-2-2-5-3-8-4-4-1-9-2-14-4v-1c-2-7-1-16 3-22z" class="L"></path><path d="M402 834v-1l-4-8c5 2 11 4 16 7l-2 1-9-3c1 1 1 3 1 4-1 1-1 1-2 1v-1z" class="F"></path><path d="M680 797h1l1-1v7h-1v1 4c0 2-1 2-1 4v1 1h1 0l2-1c0-1 1-1 2-1l1-1c1 0 2-1 2-1 1 0 2-1 3-1l-2-2 1-1c1 1 2 1 3 2h3 1c1 1 1 1 2 1 2 0 3 1 4 2v1l-1-1c-1 0-2-1-3 0-3 0-6 0-8-1-3 0-7 3-10 5l-3 3h0v2s-1 1-1 2v1 2h-1v1 1l-1 1v2 1c1 1 0 1 1 1v-1c1-1 1-2 2-3l1-1c0-1 0-1 1-2 0 1 0 2-1 3-1 0-1 1-1 2v1c1-1 1-1 1-2l1-1 1-1h0c-1 2-2 4-4 6h1 1c1 0 3 0 5 1l3 1c4 0 7 2 9 4l1 2-5-1c-8-3-19 2-26 5 0-2 1-3 2-5 2-3 5-6 6-10 2-7 5-15 6-23 0-2 0-3-1-4v1h-1c1-3 1-5 2-7z" class="E"></path><path d="M680 797h1c0 3 0 7-1 10 0-2 0-3-1-4v1h-1c1-3 1-5 2-7z" class="F"></path><defs><linearGradient id="H" x1="632.742" y1="843.606" x2="686.758" y2="824.894" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2d2d2d"></stop></linearGradient></defs><path fill="url(#H)" d="M678 804h1v-1c1 1 1 2 1 4-1 8-4 16-6 23-1 4-4 7-6 10-1 2-2 3-2 5l-1-1h0c-1 1-1 1-1 3l-1-1v1c-1 1 0 0-2 1v1h-1 0l-1 1c-1 0-2 1-2 2h0l-4 3-11 6c0 4-1 8-2 12-2-3-4-6-7-8l1-1h0v-1l5-2-1-2 7-3c12-6 23-17 27-31 4-6 4-15 6-21z"></path><path d="M634 864h0v-1l5-2c1 3 1 5 1 8h-1c-1-2-3-4-5-5z" class="W"></path><defs><linearGradient id="I" x1="465.743" y1="842.621" x2="451.597" y2="876.023" xlink:href="#B"><stop offset="0" stop-color="#717070"></stop><stop offset="1" stop-color="#abaaa9"></stop></linearGradient></defs><path fill="url(#I)" d="M470 842l2 5c-5 7-9 14-15 20-1 2-8 8-8 9l-4 3h-1c3-4 4-10 6-14-3 2-9 6-13 6 1-1 2-1 3-2 7-2 11-7 15-13l-1-1h0c6-4 11-8 16-13z"></path><path d="M854 243c14-4 31-5 44 3 3 1 5 3 8 6h0v1h-1v-1c-1-1-3-2-4-3l-1-1-1-1-2-1h0c-1 0-2-1-3-1s0 0-1-1h-1-1-1v-1c-1-1-3 0-4-1h-1l2 3h1c1 2 4 4 5 6l1 1h-1 0c-3-4-7-8-11-10v1c5 6 13 12 12 21v1-3-1c-1-2-1-3-2-4v-1c-1-1-1-2-2-3v-1c-1 0-1-1-2-2-1-2-4-4-6-5-1-1-2-4-4-4h-1 0-1c1 2 0 3 0 4-1 1-1 1-1 2-1 1-1 2-2 3s-1 2-2 2l-1 2h0c-1 2 2 4 4 5 1 1 2 2 2 4l3 3h0c1 1 1 2 1 2s-1-1-2-1c-4-6-9-12-14-16-3-2-5-3-7-5l-3-3z" class="C"></path><path d="M653 710c0 1 0 1 1 2 0-8-4-18-7-25 4 3 7 7 12 10 8 4 15 4 23 1-2 2-2 1-3 2-1 0-1 1-1 1-1 0-1-1-2 0-2 0-6 1-7 0h-3 0c-1 2-2 3-2 4-1 1-2 1-2 2l-1 1c0 1-1 1-2 2v1h-1v1c-1 1-2 1-3 2l1 1h0l2 2c0 1 0 1 1 2v1l1 1-2-2-2-3h-1c-1 5-1 10-3 15h0c-1-1 0-1 0-3v-3l1-1-1-4 1-1v-4h-1l-2 2c0 1 0 1-1 2v3h0v-14-2h-1v-3l3-1 1 4 1 4z" class="B"></path><path d="M649 706l1 2v-1c1-1 2-1 2-1l1 4-3-1h0l-1 1v-2-2z" class="O"></path><path d="M648 703l3-1 1 4s-1 0-2 1v1l-1-2h-1v-3z" class="K"></path><path d="M653 807l7-4h1c0 1 1 2 1 4v1h0c4-5 8-9 12-13 2-3 3-6 5-9 3-6 7-12 14-15 3-1 6-1 9 1 2 1 3 1 3 3-1 7-7 4-11 5-3 1-5 2-7 4-3 2-5 7-5 11v1l-1 1h-1c1-4 2-7 3-11h-1c-2 2-4 5-5 8-2 5-3 10-6 15-1 2-1 3-3 4 1-3 3-5 4-8 2-3 3-7 4-10-5 6-12 12-15 19l-1 3c0-3-1-6 0-10 0-1 0-1-1-2l-5 4-1-2z" class="D"></path><path d="M682 786c2-5 8-11 13-13 2-1 4 0 6 0l2 2c0 1-2 2-3 3h-4c-6 1-9 4-13 8h-1z" class="W"></path><defs><linearGradient id="J" x1="563.78" y1="837.979" x2="582.513" y2="870.632" xlink:href="#B"><stop offset="0" stop-color="#797979"></stop><stop offset="1" stop-color="#bdbcbc"></stop></linearGradient></defs><path fill="url(#J)" d="M559 845l3-8c7 8 14 14 23 20h0c-1 0-2 0-2 2 0 1-1 3 0 5 4 3 8 4 11 6-3 1-8-5-10-3l1 3c-2 1-3 1-4 2l-1 1c-6-6-10-12-14-19-2-3-4-7-7-9z"></path><defs><linearGradient id="K" x1="374.048" y1="685.864" x2="376.245" y2="712.725" xlink:href="#B"><stop offset="0" stop-color="#9e9d9d"></stop><stop offset="1" stop-color="#bfbebe"></stop></linearGradient></defs><path fill="url(#K)" d="M379 683c1 2 1 4 1 6-1 4-1 8-1 13v2l1-1c0 7 0 13 4 19v1c1 1 1 2 2 3 2 2 2 4 4 6l1 1-2-1s-1-1-1-2c-1-1-1-1-1-2l-2-2c-1-1-1-2-2-4h0c-1-2 0 0-1-1 0-1-1-2-1-2 0-2-1-3-1-4s0-1-1-2h-1c-1-1-3 0-4 0h-2v2c1 1 1 1 1 2h0v2l1 1v1h0v1l1 1v1h0c1 1 0 1 1 2l1 2v1l1 2c0 1 1 3 1 4-1 1 0 1-1 0h-2v1 1c-3-5-4-11-7-14l1-2c-2-7-1-14-1-21v-1h-1c1-1 1-3 1-5h-2l1-1c5-2 8-6 11-10z"></path><defs><linearGradient id="L" x1="375.678" y1="707.892" x2="371.892" y2="734.118" xlink:href="#B"><stop offset="0" stop-color="#9d9c9c"></stop><stop offset="1" stop-color="#d5d4d3"></stop></linearGradient></defs><path fill="url(#L)" d="M369 700h1v8c1 5 2 10 4 15 0 2 1 5 3 6l1 2c0 1 1 3 1 4-1 1 0 1-1 0h-2v1 1c-3-5-4-11-7-14l1-2c-2-7-1-14-1-21z"></path><defs><linearGradient id="M" x1="629.125" y1="673.253" x2="639.561" y2="699.686" xlink:href="#B"><stop offset="0" stop-color="#9c9b9b"></stop><stop offset="1" stop-color="#bdbcbc"></stop></linearGradient></defs><path fill="url(#M)" d="M623 684l5-12 9 9c2 1 3 3 5 4 0 3 2 5 3 8 2 3 3 6 3 10v3-1h0c-1-2-1-7-2-8-2 0-4 2-6 2 0 1-2 1-2 3 0 1 0 0 1 1v2c0 1 0 4 1 5v2c0 1 0 1 1 2v3c0 2 1 4 0 6v2l-1 1c0 1 1 0 0 1 0 1 0 1-1 2 0 2-1 3-1 5-1 1-1 2-2 3l-1 1c-1 2-3 4-5 5-1 1-2 2-4 2l-1 1c-1 1-1 1-2 1s-1 0-2 1h-1-1-2c-1 1-1 1-1 2 1 2 1 4 0 6l-1-8c2-1 4-1 6-2h2c5-2 11-6 13-11v-1c5-7 4-17 2-26 0-4-1-9-4-13l-3-3h0l-1-1-3-4c-1-1-2-2-4-3z"></path><path d="M681 675h0c7-2 10-4 14-10 3 5 4 11 3 17-2 6-7 11-12 15l-4 1c-8 3-15 3-23-1-5-3-8-7-12-10 3 7 7 17 7 25-1-1-1-1-1-2l-1-4-1-4-3 1c0-4-1-7-3-10-1-3-3-5-3-8-2-1-3-3-5-4l1-2c1 1 2 2 3 2h0c1-2 1-5 1-7h0c2 2 4 6 6 9 5 7 15 15 25 14 7 0 13-3 18-8 1-2 2-3 3-4l-1-1h0c1-1 1-1 1-2l1-1v-3c-1 2-1 3-3 3l2-4h-2l1-6c-4 3-8 5-14 5v-1h2z" class="D"></path><path d="M694 677c0-1 1-4 2-4 1 3 0 7-2 11v1l-1-1h0c1-1 1-1 1-2l1-1v-3c-1 2-1 3-3 3l2-4z" class="G"></path><path d="M642 685c4 2 8 13 9 17l-3 1c0-4-1-7-3-10-1-3-3-5-3-8z" class="P"></path><path d="M520 83h1l1 1h1c3 8 8 16 14 22 2 2 4 5 7 6s6 1 9 0l-9 24c10-1 20 0 29 6l-2 1c-8-4-19-7-28-4s-17 11-21 20-4 19-2 29l-2-2c-1-6 0-12 0-18 2-10 4-17 12-24 2-3 5-5 8-8 3-4 5-9 7-14 1-2 2-4 2-6h-1c-5-3-8-6-12-10-6-7-11-15-14-23z" class="R"></path><path d="M328 534l5 1c3 7 7 13 8 19l-2 7c1 2 1 3 1 5l1 1c-1 1-2 1-2 3l-1 3c-2 0-4 1-5 1-2 3-3 4-4 8 0 0 0 1-1 2h0c0-1 0-2 1-2 0-1-1-2 0-2v-1-1c1-1 1-1 1-2l1-1 1-1 2-3c0-1 1-1 1-2s-1-2-1-3v-1h0c0-1-1-1-1-2-1-1 0-1-1-2 0-2-2-4-3-6l-1-2c-1-2-2-3-3-5l-1-2c-1-2-1-3-2-4v-1c-1-1-1-2-2-3h-2l-2 2-2 1-1 1c-1 0-2 1-2 1-1 1-3 2-4 2l-2 1h0c-1 0-1 0-2 1h-2c1-2 4-3 6-5 1 0 1-1 2-1h1c-2 0-4 0-5-1h0 4c1-1 2-1 3-1s2-1 3 0c2 0 5-3 8-4 1 0 3-1 5-1z" class="C"></path><path d="M337 569c1-2 2-3 2-5v-3c1 2 1 3 1 5l1 1c-1 1-2 1-2 3l-2-1z" class="O"></path><path d="M337 569l2 1-1 3c-2 0-4 1-5 1l4-5z" class="K"></path><path d="M253 171v-1c1 0 1 0 2 1h1c0-1 0 0 1-1v1l1-1c2-1 2-2 4-3 0-1 2-1 2-1 2 1 4 1 6 1 0 0 0 1-1 1h0c-1 1-1 2-2 2-1 1-2 2-2 3l2-1h1c1-1 1 0 1-1l2-2c1 0 1 0 2-1h0c2-1-1 1 2-1h0 2l-3 3-3 2h-1l-4 3-2 2c0 1 1 2 1 4-3 2-7 5-12 7h0c-2 0-3 1-4 1l-8 2h0-4l-1-1c-1 1-1 1-1 3l-1-1-2 2c-5 1-10 1-15 1h-5 0l4-1h3c1-1 5 0 8 0 0-1-1-2-1-2h-3c-1 0-2 0-3 1h-3v1c-2 0-2 0-4-1l3-1c1 0 1 0 2-1h3l2-1h2c1-1 2-1 3-1h0l5-1c1-1 2-1 4-2h1c0-1 1-1 2-2l5-3c0-1 1-2 2-2v-1l2-3c1-1 2-2 2-3l1-1h1z" class="E"></path><path d="M261 172h1c-1 1-3 2-4 3h-3-2v-1c1-1 6-1 8-2z" class="H"></path><path d="M237 191c1-1 3-1 4-2 3-1 6-2 9-2l-1 2-8 2h0-4z" class="O"></path><path d="M253 171v-1c1 0 1 0 2 1h1c0-1 0 0 1-1v1l1-1c2-1 2-2 4-3 0-1 2-1 2-1 2 1 4 1 6 1 0 0 0 1-1 1-2 0-4 1-6 2-3 1-4 2-7 2h-2-1v-1z" class="H"></path><path d="M265 173l2-1h1c1-1 1 0 1-1l2-2c1 0 1 0 2-1h0c2-1-1 1 2-1h0 2l-3 3-3 2h-1l-4 3-2 2c0 1 1 2 1 4-3 2-7 5-12 7h0c-2 0-3 1-4 1l1-2c2-1 3-1 4-2l1-1c2 0 1-1 2-2 1 0 1 1 3 0l-1-1-1-1 6-5c0-1 0-1 1-1v-1z" class="C"></path><path d="M642 640l3-9 4 2 1-1c4 1 9 3 12 6l1 1c-2 1-3 2-5 3l1 1c11 6 15 20 21 31h-1c-1-1-3-1-5-2-8-4-13-13-20-19-3-3-7-5-9-8-1-2-1-2-3-2h0v-3z" class="E"></path><path d="M649 633l1-1c4 1 9 3 12 6l1 1c-2 1-3 2-5 3l1 1c11 6 15 20 21 31h-1c-1-1-3-1-5-2-8-4-13-13-20-19-3-3-7-5-9-8 11 4 19 16 28 24v-1c-1-6-5-15-10-20-3-2-7-4-11-6l9-3-12-6z" class="D"></path><defs><linearGradient id="N" x1="654.312" y1="668.869" x2="677.497" y2="652.798" xlink:href="#B"><stop offset="0" stop-color="#bdbcbb"></stop><stop offset="1" stop-color="#efeeee"></stop></linearGradient></defs><path fill="url(#N)" d="M652 662c-3-5-9-10-14-14l4-8v3h0c2 0 2 0 3 2 2 3 6 5 9 8 7 6 12 15 20 19 2 1 4 1 5 2h1l1 1h-2v1c6 0 10-2 14-5l-1 6c-1 1-2 3-3 5-3 2-6 4-10 3-11 0-20-16-27-23h0z"></path><defs><linearGradient id="O" x1="761.768" y1="192.177" x2="765.288" y2="169.79" xlink:href="#B"><stop offset="0" stop-color="#a3a2a2"></stop><stop offset="1" stop-color="#e7e7e7"></stop></linearGradient></defs><path fill="url(#O)" d="M736 164c7 3 13 6 19 11 0 0 1-1 2-1l1 1h1l4 2c2 1 5 2 6 4 6 6 17 10 26 12l8 1h1l2 1c-4 1-7 0-11 0-9 1-19 0-28-1-2 0-4-2-6-3-6-3-12-7-17-11-3-2-7-5-9-7v-1c1-2 2-3 3-6l-2-2z"></path><path d="M755 175s1-1 2-1l1 1h1c1 2 1 3 3 4h0c-3 0-5-2-7-4z" class="I"></path><path d="M759 175l4 2c2 1 5 2 6 4l-2 2c-1-2-4-3-5-4h0c-2-1-2-2-3-4z" class="S"></path><path d="M767 183l2-2c6 6 17 10 26 12l8 1h-3c-8 0-16-3-23-5-3-2-7-3-10-6z" class="M"></path><path d="M341 567h1c1 1 2 1 3 2 2 1 2 2 3 4s1 3 3 4l16 34c2 7 6 13 8 20-4 3-7 8-9 12l-10 18c-1 2-2 4-4 5h1v-1l1-1v-1h0l1-1v-1c1-1 2-1 2-3h0l2-3 1-2 1-2 1-2 2-5c0-1 1-2 2-2v-1l2-3h-1c0-1-1-2-1-3l-1-2h0c-1-3-2-4-2-6-2-4-4-7-5-11v-1-1c-1-1-1-1-1-2v-1l-1-1h-1v-2c1-1 1-1 1-2l-2-2c-1-3-3-5-4-8l-3-6-1-1c0-1 0-1-1-2v-1h0l-2-4c-1-1-1-2-2-3v-1l-1-1c0-1-1-2-2-3 0 2-1 3-1 4h0v2l-1 2h0v2 1c-1 0-1 0-1 1v2l-1 1s-1 1-1 2l-1 1h0c0-1 0-2 1-2v-2l1-2c2-4 2-9 4-13l1-3c0-2 1-2 2-3zm131 280l19 43c4 9 7 18 12 26 3 4 6 9 10 11 2 1 4 1 6 1h-4-1-1c-4-1-6-6-8-8l-4-6-1-1c0-1 0-1-1-2v-2h-1-1c-2 0-4-2-6-3s-3-1-4-2l-1 1s-1-1-1-2l-1-2c0-1 0-2-1-2 0-1 0-2-1-3h0v-1-1l-1-1c0-1 0-2-1-3v-1h0l-1-1c-1-1-1 0-1-1-1 0-1-1-2-1 0-1-1-2-1-2-1-2 0-1-1-2 0 0-1 0-1-1s-1-1-1-2l-6-7c0-1-1-3-2-4h0v-1l-2-2c0-1 0 0-1-1-4 4-7 9-11 12h-1c0-1 7-7 8-9 6-6 10-13 15-20z" class="C"></path><defs><linearGradient id="P" x1="635.082" y1="677.533" x2="692.471" y2="666.973" xlink:href="#B"><stop offset="0" stop-color="#8b8b8b"></stop><stop offset="1" stop-color="#cdcbcb"></stop></linearGradient></defs><path fill="url(#P)" d="M633 660c2-4 3-8 5-11l14 14v-1h0c7 7 16 23 27 23 4 1 7-1 10-3 1-2 2-4 3-5h2l-2 4c2 0 2-1 3-3v3l-1 1c0 1 0 1-1 2h0l1 1c-1 1-2 2-3 4-5 5-11 8-18 8-10 1-20-7-25-14-2-3-4-7-6-9h0l-2-3-6-6-1-1h0c0-1 0-2 1-3l1 1c0-2-1-2-2-2z"></path><defs><linearGradient id="Q" x1="687.49" y1="677.348" x2="676.089" y2="688.235" xlink:href="#B"><stop offset="0" stop-color="#151513"></stop><stop offset="1" stop-color="#323031"></stop></linearGradient></defs><path fill="url(#Q)" d="M652 662h0c7 7 16 23 27 23 4 1 7-1 10-3 1-2 2-4 3-5h2l-2 4c-1 1-2 3-3 4-3 2-7 4-11 3-8-1-14-10-18-15l-8-10v-1z"></path><path d="M156 241c6 0 12 2 18 4l7 4 2 1 3 2-2 3c-8 9-16 23-15 35v2 2h-1l-1-2c-1-1 0-2 0-3-1-2-1-6-1-8l-4-2c-1-1-2-1-3-2l-1-1h-1l-1-1-1-1c-2-1 0 1-2-1h0c-2 3-4 6-5 9v1c-1 1-1 1-2 1v-3h0c0-2 0-1 1-2v-1-1l1-1v-2l1-2 1-1h0c-1-1-4-3-5-4h-2l-2-2c-1 0-1 0-2-1h-1c-1 1-1 1-1 2h0l-1 3c-1 3-1 4-1 6-1 1-1 2-1 2v1-2c1-7 3-14 6-19l2-2v-2c2-1 3-3 5-4l3-3c2-1 3-2 5-3l2-1c-1-1 0-1-1-1z" class="B"></path><path d="M173 249l1 1v1l1 1h-1-5l4-3z" class="P"></path><path d="M174 245l7 4c0 1-1 3-2 4h0l-1 2c-1-2-2-2-4-3h1l-1-1v-1l-1-1c0-1 1-2 1-4z" class="T"></path><path d="M174 251c2-1 2-2 5-1v1 2h0l-1 2c-1-2-2-2-4-3h1l-1-1z" class="K"></path><path d="M175 252c1 0 2-1 3 0l1 1-1 2c-1-2-2-2-4-3h1z" class="O"></path><path d="M164 255h2c1 0 2 1 3 2h-3c-6 6-13 12-17 19l-1 2c0-1 2-5 2-6h1c3-6 8-14 13-17z" class="C"></path><defs><linearGradient id="R" x1="167.664" y1="259.511" x2="181.649" y2="278.387" xlink:href="#B"><stop offset="0" stop-color="#767575"></stop><stop offset="1" stop-color="#aeaeae"></stop></linearGradient></defs><path fill="url(#R)" d="M181 249l2 1 3 2-2 3c-8 9-16 23-15 35v2 2h-1v-2c-1-2 0-5-1-8-1-10 5-21 11-29l1-2h0c1-1 2-3 2-4z"></path><path d="M183 250l3 2-2 3-1-5z" class="P"></path><path d="M843 248c6 5 14 17 17 25h-1 0c-1-1-1-1-1-2l-1-1c-1-1-1 0-2 0l-1 1-11 6-1 1-1 1c-1 0-1 0-2 1-3 2-8 4-11 6-1 1-3 2-4 3l-7 5-4 2v1l-6 4-2 2c-1 1 1 0-1 0-2 2-3 4-6 5-1 1-4 5-5 6-1 3-4 4-5 6-2 3-7 5-8 8l-1 1-2 2h0-1v1c-1 1-1 1-1 2l-4 8-4 12c-1 1-1 2-2 3l-1 3v1c-1 0-1 1-1 1v1l-1 1v1c-1 1-1 2-1 3h-1v1l-1 2v1c-1 1-1 2-2 4 0 1-1 2-2 3l-1 3v1c-1 1-1 2-1 3-1 1-1 2-1 3h-1v1l-1 2v1l-2 4v1l-1 1c-1 3-3 6-4 9v1l-1 1v2c-1 0-1 1-1 1 0 1-1 1-1 2s-1 2-1 3c0 0 0 1-1 1v1l-1 2c-1 2-1 3-2 5l-1 2-1 3-1 1v1l-1 1v2l-1 1v2h-1v1c0 1-1 1-1 2v1c-1 0-1 1-1 2s0 0-1 1v2c-1 0-1 1-1 2-1 0-1 1-1 1l-1 2v1l-1 2c0 1-1 2-1 2l-1 3c0 1-1 1-1 2l-1 2v2h-1v1c0 1-1 1-1 2l-1 1-1 4c-1 2-2 4-2 6h-1v1c-1 2-2 4-3 5l-1 4v1c-1 2 0 0-1 1 0 1-1 2-1 3l-1 1v2c-1 1-1 1-1 2h-1v1c-1 2-1 3-2 4l-1 2c0 1 0 2-1 2 0 1 0 2-1 3 0 1 0 1-1 2v1 1c-1 1-1 1-1 2s-1 1-1 2c-1 2-3 4-2 6 1 1 1 1 2 1h0c4 3 8 6 12 7 1 1 2 1 3 2h1v1l-1 1c-1-1-1-1-2-1l-5-1c-4 0-9-5-13-7h0l62-145 20-44c6-15 13-31 22-45 13-20 29-38 50-49z" class="B"></path><defs><linearGradient id="S" x1="510.296" y1="151.182" x2="520.321" y2="75.136" xlink:href="#B"><stop offset="0" stop-color="#d1d0d0"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#S)" d="M506 94c5-6 7-15 9-22l5 11h0c3 8 8 16 14 23 4 4 7 7 12 10h1c0 2-1 4-2 6-2 5-4 10-7 14-3 3-6 5-8 8-8 7-10 14-12 24l-1-2c-1 1 0 3-1 4-1-2-1-5-1-7l-1 1c0 2 1 5-1 7l-4-13c-1-5-4-10-8-14l-7-7-6-9-2-6 1-1v-2l-3-4 1-1 2-1h1c8-2 15-12 18-19z"></path><path d="M499 130c3 1 6 6 8 8l1 1 3 3v-1-1c2 3 3 7 5 11v1l1 2v12h0c-1 1 0 3-1 4-1-2-1-5-1-7 0-5 0-11-2-15-4-7-9-12-14-18z" class="E"></path><path d="M527 118l1-2c1 1 2 1 3 1s1 0 1-1-1-2-2-2v-1c2 1 2 2 4 2-1-2-5-5-6-6l-1-1v-2h-1l1-1c1 1 2 1 2 2 1 2 1 1 2 2 0 1 1 2 2 2l1 1c1 1 2 2 3 2l1 1s1 1 2 1 1 0 2 1v1 1l3-3h0c-1 0-1-1-2-1l-2-1c-1-1 0-1-1-1l-1-1c-2-2-4-3-6-5l1-1c4 4 7 7 12 10h1c-5 4-8 6-12 10h0c2-3 5-5 7-8l-6 4c-2 1-3 3-5 4h-1l4-4h0l-13 13c3-6 8-11 12-15h-6v-1-1z" class="G"></path><path d="M547 116c0 2-1 4-2 6-2 5-4 10-7 14-3 3-6 5-8 8-8 7-10 14-12 24l-1-2h0v-12c0-2 1-5 2-6 3-8 11-16 16-22 4-4 7-6 12-10z" class="B"></path><path d="M547 116c0 2-1 4-2 6h-2c-3 2-6 7-8 7-2 2-7 6-8 8 0 2-1 3-2 4-1 2-2 4-4 7-1 1-2 2-2 3-2 2-2 6-1 9 0 2 0 4-1 6v-12c0-2 1-5 2-6 3-8 11-16 16-22 4-4 7-6 12-10z" class="L"></path><defs><linearGradient id="T" x1="499.553" y1="148.933" x2="510.487" y2="144.135" xlink:href="#B"><stop offset="0" stop-color="#a6a5a5"></stop><stop offset="1" stop-color="#c7c6c5"></stop></linearGradient></defs><path fill="url(#T)" d="M487 119c3 2 6 5 9 8 1 1 2 3 3 3 5 6 10 11 14 18 2 4 2 10 2 15l-1 1c0 2 1 5-1 7l-4-13c-1-5-4-10-8-14l-7-7-6-9-2-6 1-1v-2z"></path><path d="M487 121c1 1 2 3 3 4l1 1c1 2 2 4 4 6v1h-1l-3-5h-3l-2-6 1-1z" class="B"></path><path d="M487 119c3 2 6 5 9 8 1 1 2 3 3 3 5 6 10 11 14 18-1-1-2-1-2-2-1-1 0-1-1-1v2l-1-1c0-1 0-2-1-3h0c-1-2-2-3-4-4-2-3-5-6-8-9h0l-1-1-4-3-1-1c-1-1-2-3-3-4v-2z" class="J"></path><path d="M527 119c-5 2-10 10-12 15h0c-3-6-7-13-13-16v-1c4-6 9-12 13-19 3 7 7 13 12 19v1 1z" class="F"></path><path d="M355 839c8-1 14 1 21 4l-3-7c-6-10-7-24-3-35 2-7 8-15 15-19 7-3 14-3 21-1 1 1 3 0 4 1 3 3 4 4 6 7 0 1 1 2 1 2v1 1c1 1 1 1 1 2-1 2 0 4-1 6v2 1h0c-1 1-1 2-1 3h-1c-1 3-3 5-5 6l-1 1c-1 0-2 1-4 1h-2-2c-1-1-2 0-2 0-2-1-4-2-5-3l-1-1c0-1-1-2-1-3s0-2 1-3h0c0-1 2-2 2-2l1-1h1 1c2 0 3 0 5 2v1c1 1 1 1 0 2 0 2-1 2-2 3h-1l2 2 2-1h1 0c1-1 1-1 2-1l2-2h0c2-2 3-4 4-6-1-2-1-2-1-3v-1c-1-1-1-2-1-3h-1v-1c-1-2-2-3-3-4h-1v-1h-2-4 0c-7 1-12 5-15 11-1 1-1 2-2 3-4 6-5 15-3 22v1c5 2 10 3 14 4 3 1 6 2 8 4v1c1 0 1 0 2-1 0-1 0-3-1-4l9 3 2-1c1 1 6 3 8 3 0 0 1-1 1-2h2c0-1 0-1 1-2 2 0 3 1 5 2 0 2 2 3 3 5l2 4 1 1v-1l-1-1 1-1c1 0 1 0 2 1s1 1 3 1l1-1h1 1l1 1v-1h2 1 3v2l-2 1-1 1v1l4-2c1-1 1-1 2-1l3-2 1-1h0c3-2 5-4 7-6l2 6c1 0 1 1 2 2-5 5-10 9-16 13h0c-3 2-6 4-10 5-3 1-7 2-10 3-8 1-16 2-23 0-6-1-11-2-16-4-2-1-4-3-6-4-3-1-6-1-8 0-1 1 0 1-1 0h0c1 0 2-1 3-2l-5-4c-1-1-1-2-3-2l1-1h-5c-2 1-3-1-5-1l-6-3c-2 0-2 0-3-1s-2-1-2-2z" class="R"></path><path d="M357 841c6 1 15 1 20 4l5 5c2 2 5 3 7 5-3-1-6-1-8 0-1 1 0 1-1 0h0c1 0 2-1 3-2l-5-4c-1-1-1-2-3-2l1-1h-5c-2 1-3-1-5-1l-6-3c-2 0-2 0-3-1z" class="B"></path><path d="M406 781c1 1 3 0 4 1 3 3 4 4 6 7 0 1 1 2 1 2v1 1c1 1 1 1 1 2-1 2 0 4-1 6v2 1h0c-1 1-1 2-1 3h-1c-1 3-3 5-5 6l-1 1c-1 0-2 1-4 1h-2-2c-1-1-2 0-2 0-2-1-4-2-5-3l-1-1c0-1-1-2-1-3s0-2 1-3h0c0-1 2-2 2-2l1-1h1 1c2 0 3 0 5 2v1c1 1 1 1 0 2 0 2-1 2-2 3h-1l2 2 2-1h1 0c1-1 1-1 2-1l2-2h0c2-2 3-4 4-6-1-2-1-2-1-3v-1c-1-1-1-2-1-3h-1v-1c-1-2-2-3-3-4h-1v-1h-2-4 0 0c3-1 6-1 8 1s6 8 6 11c-1 3-2 6-4 8s-6 4-9 4c-2 0-3-1-5-3v-4l4-2 1 1v1h-2c-1 0-1 1-2 2l1 1c1 0 2 1 3 0s1-2 1-3c0-2-1-3-2-3-2-1-3 0-4 0-2 1-2 3-3 5 0 2 1 3 3 4 3 2 6 2 9 2 4-1 7-3 9-7 3-4 3-10 2-15-1-6-5-8-10-11z" class="H"></path><path d="M372 803c3-8 7-15 15-18 6-3 12-2 18 0h-3c-6 0-12 4-16 8-5 4-7 9-9 14-1-1-1-2-1-3l-1-1-2 2h0l-1-2z" class="W"></path><path d="M466 834l2 6c-12 12-27 21-45 21h-2v-1h-5-1-2 0v-1c1-2 7-1 10-2h0c8-1 14-3 22-7 3-2 6-3 8-6 1-1 1-1 2-1l3-2 1-1h0c3-2 5-4 7-6z" class="T"></path><defs><linearGradient id="U" x1="417.486" y1="837.376" x2="367.306" y2="824.944" xlink:href="#B"><stop offset="0" stop-color="#989797"></stop><stop offset="1" stop-color="#f3f1f1"></stop></linearGradient></defs><path fill="url(#U)" d="M421 861c-2 1-10-1-13-1-12-3-24-11-31-22s-8-23-5-35l1 2h0l2-2 1 1c0 1 0 2 1 3-3 12-2 21 5 31 6 10 17 16 27 18 5 1 10 1 14 1h0c-3 1-9 0-10 2v1h0 2 1 5v1z"></path><defs><linearGradient id="V" x1="406.317" y1="852.935" x2="408.948" y2="831.484" xlink:href="#B"><stop offset="0" stop-color="#b2b0b0"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#V)" d="M441 850c-13 6-25 7-39 1-10-4-17-11-22-22l22 6c1 0 1 0 2-1 0-1 0-3-1-4l9 3 2-1c1 1 6 3 8 3 0 0 1-1 1-2h2c0-1 0-1 1-2 2 0 3 1 5 2 0 2 2 3 3 5l2 4 1 1v-1l-1-1 1-1c1 0 1 0 2 1s1 1 3 1l1-1h1 1l1 1v-1h2 1 3v2l-2 1-1 1v1l-8 4z"></path><path d="M414 832c1 1 6 3 8 3 0 0 1-1 1-2h2c1 4 2 8 3 11-1 1-2 1-3 1 0-2-1-2-1-3l-4-2h-1-1c-1-1-1-2-2-3v-1c-1-1-2-2-4-3l2-1z" class="R"></path><defs><linearGradient id="W" x1="449.358" y1="846.645" x2="424.005" y2="836.092" xlink:href="#B"><stop offset="0" stop-color="#bfbebe"></stop><stop offset="1" stop-color="#f1f0f0"></stop></linearGradient></defs><path fill="url(#W)" d="M425 833c0-1 0-1 1-2 2 0 3 1 5 2 0 2 2 3 3 5l2 4 1 1v-1l-1-1 1-1c1 0 1 0 2 1s1 1 3 1l1-1h1 1l1 1v-1h2 1 3v2l-2 1-1 1v1l-8 4h-1l-1-1-2-2h-1 0l-1-1-1-1-2 1c-3 1-4 1-8-1h1c1 0 2 0 3-1-1-3-2-7-3-11z"></path><path d="M186 252c24 17 38 44 51 70l26 54 9 22c2 3 3 6 4 8l1 3c2 3 3 7 4 10l12 26 31 69c1 2 1 3 1 5l1 1c0 2 1 4 2 7-1 2-3 2-5 3-2 2-5 3-8 4h0l4-2v-2c-1 0-1-1-1-1-1-1-1-1-1-2l-2-4s-1-1-1-2c-1-1-2-2-2-3s0 0-1-1l-1-4-3-7-1-1-5-11-1-2-9-21v-1l-2-4-1-3-2-4-1-1-2-7-1-1-6-13v-1l-1-1c0-1 0-2-1-3 0-1 0-1-1-2v-1c0-1-1-1-1-2l-1-4-1-1c-1-2-1-3-2-4v-1l-1-1c0-2-1-3-1-4l-5-10c-1-2-2-5-4-7 0-2-1-3-1-4l-3-7-3-3-10-20-2-6-4-5v-1l-1-2-4-5c-1-2-1-3-2-4l-2-3-4-5v-2l-2-2s-1-1-1-2c-1 0-2-1-2-2 0 0-1-1-1-2-1 0-2-1-2-2l-3-3c-1-2-10-11-12-13-1 0 0 1-1 0l-3-3-2-1-1-1-7-5c-1-1-3-2-5-3l-5-2-1-1c-1 0-1 0-2-1h-1v1 1 3c0 1 0 1-1 2v-2c-1-12 7-26 15-35l2-3z" class="B"></path><path d="M277 409c-1 1-3 1-5 2l-1-1 1-1c1-1 2-2 4-3l1 3z" class="H"></path><path d="M444 160c1-5 5-10 9-14 7-5 16-10 24-11h9c0 1 0 0-1 1l-1 1c9 2 14 6 19 13 3 3 4 6 5 10l1-2 4 13c2-2 1-5 1-7l1-1c0 2 0 5 1 7 1-1 0-3 1-4l1 2c0 6-1 12 0 18l2 2c-2-10-2-20 2-29s12-17 21-20 20 0 28 4l2-1h0c7 4 15 12 17 20 2 6 0 12-3 17 8-1 16-5 23 1 2 1 4 3 6 6s3 7 8 8c2-1 2-7 3-10l3-6c16-28 51-32 80-31h2v2c-1-1-2-1-4 0-23-1-49 0-66 17-8 8-13 18-14 29h9v-1-8-1c1-3 3-8 5-10h2c6-8 13-11 23-12 9-1 16 0 24 6l2-1c2 1 4 3 5 5 1 1 3 2 4 2 2 0 2 0 4-1 0-2-1-5-2-7-1 0-5-5-6-6-1 0-1-1-2-2l-3-2h0 1c0-1 0-1 1-1 3-1 8-1 11 1 2 0 4 1 5 2 3 1 6 3 9 5l3 2 2 2v1l1 1h0c1 1 2 2 2 3l1 1v1c1 1 2 3 3 3 1-2 1-2 1-4 0-1 0-1 1-2l1 1c2 2 6 5 9 7 5 4 11 8 17 11 2 1 4 3 6 3 9 1 19 2 28 1 4 0 7 1 11 0h16 35c21 0 41 0 60-8l1 30c0 5 1 11-1 15-3 6-8 12-11 20h0c-3-3-5-5-8-6-13-8-30-7-44-3-4 1-8 2-11 5-21 11-37 29-50 49-9 14-16 30-22 45l-20 44-62 145-12 28c-1 2-3 4-4 7l-20 49c-2 5 2 9-5 13l-1 1 3 3-1 1-4-2-3 9-4 8c5 4 11 9 14 14v1l-14-14c-2 3-3 7-5 11 1 0 2 0 2 2l-1-1c-1 1-1 2-1 3h0l1 1 6 6 2 3c0 2 0 5-1 7h0c-1 0-2-1-3-2l-1 2-9-9-5 12c2 1 3 2 4 3l3 4 1 1h0l3 3c3 4 4 9 4 13 2 9 3 19-2 26v1c-2 5-8 9-13 11h-2c-2 1-4 1-6 2l1 8c1 14-4 27-13 36-5 5-11 8-18 9-2 0-4 0-7-1h-2c4 3 7 5 11 7 2 1 5 3 7 5 4 2 8 3 12 5 2 1 5 2 8 2 1 0 2-2 3-3s1-3 2-5l1-1 1-1c-1-1-2-1-2-3 5 1 11 3 14 7 1 1 1 3 2 4h2l16-10 1 2 5-4c1 1 1 1 1 2-1 4 0 7 0 10l1-3c3-7 10-13 15-19-1 3-2 7-4 10-1 3-3 5-4 8 2-1 2-2 3-4 3-5 4-10 6-15 1-3 3-6 5-8h1c-1 4-2 7-3 11-1 2-1 4-2 7-2 6-2 15-6 21-4 14-15 25-27 31l-7 3 1 2-5 2v1h0l-1 1c0-1-1-1-1-1-1 0-6 0-8 1h-13c-9-1-18-4-26-8h0c-9-6-16-12-23-20l-3 8-9 28c-5 15-13 46-27 53-1 1-2 1-3 2h-1c-2 0-4 0-6-1-4-2-7-7-10-11-5-8-8-17-12-26l-19-43-2-5c-1-1-1-2-2-2l-2-6c-2 2-4 4-7 6h0l-1 1-3 2c-1 0-1 0-2 1l-4 2v-1l1-1 2-1v-2h-3-1-2v1l-1-1h-1-1l-1 1c-2 0-2 0-3-1s-1-1-2-1l-1 1 1 1v1l-1-1-2-4c-1-2-3-3-3-5-2-1-3-2-5-2-1 1-1 1-1 2h-2l1-2c0-3-2-6-3-8-2-3-3-5-3-8 2 0 3 1 5 1v-1h2c1 1 1 1 2 1h1 2c2-1 3-1 5-1-1-1-2-2-2-3l-1-1v-1c0-1 0-2-1-3v-3c-1-2-1-8-1-10l-3-4h0l-1-1c0-1-1-1-2-2v-1l-2-3c-1-1-1-2-1-3l-1-1h0l-1-1v-2h-1c0-2-1-3-1-5h0l-1-1v-3-1-4c-1-1-1-1-1-2 1-1 1-2 1-3v-4l1 3c0-4 1-9 4-13h0c-4-1-7-2-10-4-5-5-7-11-7-18-1 1-1 2-1 3v2c-1 0-1 0-2-1-2 0-5 1-6 0h0-1c-1 0-3 0-4-1v1l-1-1-1 1 2 2c1 0 2 1 2 2 1 1 1 2 0 3l-1 1-1-1c-2-2-2-4-4-6-1-1-1-2-2-3v-1c-4-6-4-12-4-19l-1 1v-2c0-5 0-9 1-13 0-2 0-4-1-6-3 4-6 8-11 10l-1 1c-9 4-17 4-26 0-4-3-7-7-8-11s-2-9 0-13c2-7 9-11 15-14l-2 6c0 1 0 2 1 3s2 2 4 2c1 0 1 0 1-1 2-1 3-3 4-5l10-18c2-4 5-9 9-12-2-7-6-13-8-20l-16-34c-2-1-2-2-3-4s-1-3-3-4c-1-1-2-1-3-2h-1l-1-1c0-2 0-3-1-5l2-7c-1-6-5-12-8-19l-5-1 1-2c-1-2-1-3-1-5-1-3-2-5-2-7l-1-1c0-2 0-3-1-5l-31-69-12-26c-1-3-2-7-4-10l-1-3c-1-2-2-5-4-8l-9-22-26-54c-13-26-27-53-51-70l-3-2-2-1-7-4c-6-2-12-4-18-4-4-1-8-1-12-1-8 1-15 3-20 9l-1 1c-2-5-4-10-7-14l-1-1c0-1-1-2-1-3-1-5 0-10 0-14v-30c5 2 11 4 16 5 13 2 27 2 40 2h42 5c5 0 10 0 15-1l2-2 1 1c0-2 0-2 1-3l1 1h4 0l8-2c1 0 2-1 4-1h0c5-2 9-5 12-7 0-2-1-3-1-4l2-2 4-3h1l3-2 3-3h-2 0c-3 2 0 0-2 1h0c-1 1-1 1-2 1l-2 2c0 1 0 0-1 1h-1l-2 1c0-1 1-2 2-3 1 0 1-1 2-2h0c1 0 1-1 1-1-2 0-4 0-6-1 0 0-2 0-2 1-2 1-2 2-4 3l-1 1v-1c-1 1-1 0-1 1h-1c-1-1-1-1-2-1v1h-1l-1-3c-1-1-2-2-2-3l1-7c1 2 2 3 4 4 3 1 5 1 8 1l1-1 2-1h2c3 0 6-1 9-1 4-1 8-1 12-2 3-1 7-3 10-4l17-5h0-1v-1c26-3 57-1 78 17 8 7 13 16 15 26v3h3 2v-2c1-5 7-12 11-14 4-3 9-3 13-2 3 1 6 2 9 4-2-6-4-13-1-20h0z" class="N"></path><path d="M607 471c2 0 3 0 4 1h-1v2h-2l-1-3z" class="M"></path><path d="M437 496h6c-1 1-2 2-4 3-1-1-1-2-2-3z" class="O"></path><path d="M232 194l2-2 1 1c0-2 0-2 1-3l1 1h4 0c-3 1-5 2-9 3z" class="B"></path><path d="M491 597l-4-2c-1 0-2 0-3-1 2-1 2 1 3 1s4 0 5-1l2 2-3 1z" class="K"></path><path d="M638 424c4-1 8-2 12-1-2 0-3 0-4 1l-9 1 1-1z" class="B"></path><path d="M616 228h1v4l-5 4h0l4-8z" class="E"></path><path d="M330 183c1-3 1-5 3-8v1c0 1-1 3-1 4 1 2 1 3 1 4l-1 2c-1 0-1 0-1-1v-1l-1 1h0v-2z" class="I"></path><path d="M438 494c2-1 4-1 7 0v1l-2 1h-6v-1l1-1z" class="B"></path><path d="M421 743l2-1c0 4-2 8-2 12h-1c-1-1 0-4-1-5 1-2 2-4 2-6h0z" class="S"></path><path d="M420 240c-1-1-4-1-5-3 1-1 1-2 2-3l3 4 4 3-1 1-3-2z" class="J"></path><path d="M589 776l-3 1-1-1 2-5c2 1 5 2 7 4l-2 1v-1h-3v1z" class="H"></path><path d="M709 188c-1 2 0 5-1 7h-4v-1c1-3 3-4 4-6h1z" class="P"></path><path d="M616 236h1v2c-2 2-4 3-7 4h-1c1-2 4-5 7-6z" class="J"></path><path d="M362 324c2 2 3 3 4 7l2 2-3 3-3-12z" class="B"></path><path d="M590 821c1 0 2 0 2 1 2 0 2 0 2 1-1 2-3 3-5 3l-2 1v-1c0-2 1-3 3-5z" class="G"></path><path d="M444 510l-3-8c1-3 3-5 5-7v1c0 2-1 3-2 5 0 3 1 6 0 9z" class="C"></path><path d="M685 357v1h1c0 6-2 12-3 18-2-7 1-13 2-19z" class="K"></path><path d="M380 673v-1l1-1c0 2 0 3 1 4 1 0 1 1 2 2l-4 12c0-2 0-4-1-6 1-3 2-7 1-10z" class="P"></path><path d="M326 520c3 4 5 10 7 15l-5-1 1-2c-1-2-1-3-1-5-1-3-2-5-2-7z" class="T"></path><path d="M334 176h1c1-2 3-3 4-4l1 2-5 6 1 1-3 3c0-1 0-2-1-4 0-1 1-3 1-4h1z" class="S"></path><path d="M639 245c3-1 9 0 12 0l-2 4-1 1-1-2-1 1-1 4v-1c0-3-1-4-3-6l-1-1h-2z" class="X"></path><path d="M552 701c0 3-1 5-2 8-1 2-2 6-5 7v-1c-1-4 5-11 7-14z" class="K"></path><path d="M696 174c5 5 7 11 8 17-2-4-5-8-9-11h2c1 0 2 1 3 1h0c-1-1-2-3-3-4l-2-3h1z" class="X"></path><path d="M345 165v2l3 1c-3 1-7 2-9 4-1 1-3 2-4 4h-1c3-5 6-8 11-11z" class="J"></path><path d="M656 177v3c-1 1 0 2-1 3l1 1c-2 3-3 5-4 8l-2 4c0-3 0-7 2-10 0-2 2-4 2-6v-1c1 0 2-1 2-2z" class="S"></path><path d="M503 605c1 1 1 1 1 2 1 1 2 1 2 2v2 13 1c-2-1 0-6-2-8h0v3 1c-1-1-1-14-1-16z" class="E"></path><path d="M323 178v3h3c2 0 2 1 4 2v2l-1 4h-1c-2-1-4-3-5-5v-6zm410-4c3 2 4 5 5 9l-1 1-2-1c-1 0-2-1-3-2s-1-1-1-2h-1c0-2 0-2-1-4 1 1 2 3 3 3 1-2 1-2 1-4z" class="K"></path><path d="M367 664c0-1 1-2 1-3 2-3 3-5 6-6l-6 12c-2 5-4 8-7 12l-1-1c2-1 3-3 4-5h0c1-3 3-6 3-9z" class="P"></path><path d="M504 621v-1-3h0c2 2 0 7 2 8v-1c1 3 1 7 1 11 0 1 0 3 1 4l-1 2c-3-6-3-14-3-20z" class="J"></path><path d="M619 432c6-4 13-7 19-8l-1 1c-2 2-3 3-5 4-4 0-8 3-13 3z" class="O"></path><path d="M635 730l1 1-1 1v1c-1 2-2 3-3 5-1 1-3 2-3 3v1c2-2 4-4 5-6l2-2v1c-2 5-8 9-13 11h-2-2 1 0c1-1 2-1 3-1h-1-1-1l-1 1-1-1 5-2c6-2 9-7 12-13z" class="D"></path><path d="M607 207v3c1 1 2 1 3 0l2-2h4c-1 2-1 4-2 6-1 1-1 3-2 4l-2-1c-2-1-4 0-5-1l1-3 1-6z" class="O"></path><path d="M606 213l1 1h3v2h2l2-2c-1 1-1 3-2 4l-2-1c-2-1-4 0-5-1l1-3z" class="K"></path><path d="M339 561l2-7 10 23c-2-1-2-2-3-4s-1-3-3-4c-1-1-2-1-3-2h-1l-1-1c0-2 0-3-1-5z" class="T"></path><path d="M658 408c1 1 3 1 5 1 3 0 7-2 10-3 0 2-2 8-3 9h-1c-2-3-13-3-17-3 2-1 3-2 5-1l1 1v-2l-1-1 1-1z" class="C"></path><path d="M417 452l-4-7c7 2 14 4 20 8l-2 1c-1 0-2 1-3 0h-3c-2-1-5-1-6-2h-1-1z" class="J"></path><defs><linearGradient id="X" x1="746.998" y1="190.038" x2="744.82" y2="178.496" xlink:href="#B"><stop offset="0" stop-color="#777677"></stop><stop offset="1" stop-color="#949493"></stop></linearGradient></defs><path fill="url(#X)" d="M741 187l-4-11 22 16c-1 0-2 1-2 1-1-1-2-1-2-2-2-1-3-1-5-1-3-2-5-4-9-3z"></path><path d="M653 446c1-1 4 0 5 0 1-2 1-3 1-4h1 0c0 2-2 10-4 11l-1 1h-1c-2 0-3-2-5-3l-1 1-1-1-4 1 2-2 1-1-1-1c1 0 2 0 3-1h1 1c1 0 2-1 3-1z" class="T"></path><path d="M653 446h2c0 1 0 1-1 2 0 0-1 2-1 3l-4-4h1c1 0 2-1 3-1z" class="P"></path><path d="M648 447h1l4 4 2 3h-1c-2 0-3-2-5-3l-1 1-1-1-4 1 2-2 1-1-1-1c1 0 2 0 3-1z" class="M"></path><path d="M646 449l3 2-1 1-1-1-4 1 2-2 1-1z" class="D"></path><path d="M607 722c2-5 4-9 6-13l8 7c-4 3-9 4-12 7-1 0-1-1-2-1z" class="L"></path><path d="M704 167c1 1 3 5 3 7l1 1 1 3-1 1c1 0 1 0 2 1l-1 1s0 1 1 2l-2 3c0 1-1 2-2 2l-1-1c-2-5-4-10-7-14 1 1 3 2 4 2 2 0 2 0 4-1 0-2-1-5-2-7z" class="T"></path><path d="M677 371v7c1 3 2 6 2 10v1c-1 4-3 9-5 14l-8 2c6-4 8-7 9-14 0-3 1-11 0-12-1-2-1-2 0-3l1 1v-2c0-2 0-2 1-4z" class="B"></path><path d="M741 187c4-1 6 1 9 3 2 0 3 0 5 1 0 1 1 1 2 2 0 0 1-1 2-1s3 2 4 3h-14c-2 0-5 0-7-1 0-2 0-5-1-7z" class="Y"></path><path d="M633 660c1 0 2 0 2 2l-1-1c-1 1-1 2-1 3h0l1 1 6 6 2 3c0 2 0 5-1 7h0c-1 0-2-1-3-2l-8-13 3-6z" class="C"></path><path d="M568 169v-1-1h3c2 0 4 0 6 1-1 3-3 10-2 13h2v1h-1c-1 0-3-1-5-2-3-2-3-7-3-11z" class="Y"></path><defs><linearGradient id="Y" x1="327.697" y1="180.427" x2="328.187" y2="171.65" xlink:href="#B"><stop offset="0" stop-color="#a8a7a7"></stop><stop offset="1" stop-color="#cac9c9"></stop></linearGradient></defs><path fill="url(#Y)" d="M329 164h0c0 2-1 3-2 4s-2 2-2 4h5l3 1c2-3 4-5 6-8l1 1c-3 3-6 6-7 9-2 3-2 5-3 8-2-1-2-2-4-2h-3v-3-1c1-2 1-4 2-6v-1c0-1 1-3 2-4h0c1-1 1-2 2-2z"></path><path d="M490 192c0 2 0 5-1 6-2 4-5 8-7 12-9 9-21 17-34 18-2 0-4 0-5-2h4c5-1 10-2 15-4 13-6 23-16 28-30z" class="E"></path><path d="M607 471l-1-1c-1-1-3-1-4-1-5 1-11 1-15 2l-6 1h1c8-5 18-9 28-7 0 3 1 4 2 7h-1c-1-1-2-1-4-1z" class="H"></path><path d="M449 814l1 1c-2 5-5 7-7 11-2 0-3 1-4 0-3-3-6-5-9-8h3c5-1 11-2 16-4z" class="J"></path><path d="M566 643c1-3 3-3 5-4h3 0c0 1 0 3 1 4 0 2 0 3 1 5-2 4-3 8-6 12-2-5-3-11-4-17z" class="P"></path><path d="M553 692c1 0 1 0 2 1h2l-1 1c-2 4-5 7-8 10l-7 8c-1-1-2-2-2-4 0-1 0-2 1-3h0c3-6 7-9 12-13h1z" class="X"></path><path d="M553 692c1 0 1 0 2 1h2l-1 1c-5 2-8 6-12 8l-3 3h-1c3-6 7-9 12-13h1z" class="B"></path><path d="M421 461c2 0 6 0 8 1h3c1 0 0 0 2 1h0c2 0 3 1 4 1 1 1 2 1 3 2h0c1 0 1 1 2 1l4 3c1 0 2 1 3 2 0 0 1 1 1 2-2-1-4-2-7-3-7-2-13-3-20-2l-3-8z" class="E"></path><path d="M503 605v-1c3 0 6 1 9 1 5 0 12-1 17-2v8-4h-1c-2 2-6 3-10 4h-6c-2 0-4-1-6-2 0-1-1-1-2-2 0-1 0-1-1-2z" class="T"></path><path d="M607 779c1-2 2-6 4-8-3 9-8 19-16 24-4 2-9 3-14 2-1 0-2-1-3-2 0-3 3-8 5-11-1 2-2 6 0 8h1 0v-1c-1-4-1-8 0-12h1c0 4 0 9 4 13 1 1 3 1 5 1 4-1 8-6 10-10l3-4z" class="H"></path><path d="M275 175c-1 2-4 3-4 5h4c3-1 5 0 7-1h0c3 0 3-1 5-2h1c1-1 1-1 3-1h1v-1c1 0 1-1 2-1h1c-2 2-5 5-8 6-2 1-4 1-6 2-3 2-8 3-12 4-4 2-8 4-13 4h-1c-1 1-2 0-3 0 5-2 9-5 12-7l1-1 1 1c1-1 2-3 3-4 2-1 4-3 6-4z" class="L"></path><path d="M423 823c-1-1-2-2-1-4 5 1 10 3 15 6-2 3-2 5-1 8v1 2l-2 2c-1-2-3-3-3-5-2-1-3-2-5-2-1 1-1 1-1 2h-2l1-2c0-3-2-6-3-8h2z" class="G"></path><path d="M421 823h2l8 10c-2-1-3-2-5-2-1 1-1 1-1 2h-2l1-2c0-3-2-6-3-8z" class="M"></path><path d="M363 678l1-1c4-5 6-12 9-17l8-16c1 3 2 5 3 7l-3 6c-1 1-1 2-1 3h0c-1 1-2 2-2 3-1 1-1 2-2 2l-1 2c-1 1-1 2-2 2l-4 6-2 3c-1-1-1-1-1-2-1 1-2 2-3 2z" class="T"></path><path d="M487 600c4 1 9 3 13 4h1l1 26c-3-3-4-8-6-12l-9-18z" class="P"></path><path d="M380 297c-6-1-7-3-10-7-4-5-8-2-14-3 0-1 0-1 1-3 2-1 7-1 10-1 4 1 9 3 12 5s5 4 7 6c-2 2-2 2-4 2l-2 1z" class="C"></path><defs><linearGradient id="Z" x1="654.487" y1="459.768" x2="643.61" y2="463.052" xlink:href="#B"><stop offset="0" stop-color="#777677"></stop><stop offset="1" stop-color="#979696"></stop></linearGradient></defs><path fill="url(#Z)" d="M643 452l4-1 1 1c3 1 5 3 7 4l-6 16c-2-2-3-4-5-5v-1c1-2 1-3 0-5-1-1-2-2-2-4h0c-1-1-2 0-4-1h4-2c-1-1-3-1-3-1 0-1 0-1 1-1 2 0 3-1 4-1l1-1z"></path><path d="M643 452l4-1c0 2 1 3 0 4h0c-1 0-1-1-1-2l-1 1-1 1 1 1h-3-2c-1-1-3-1-3-1 0-1 0-1 1-1 2 0 3-1 4-1l1-1z" class="F"></path><defs><linearGradient id="a" x1="431.868" y1="227.49" x2="432.13" y2="210.657" xlink:href="#B"><stop offset="0" stop-color="#666565"></stop><stop offset="1" stop-color="#a1a0a0"></stop></linearGradient></defs><path fill="url(#a)" d="M420 207h0 2 0c1 2 1 3 2 5l1-1c3 6 5 10 11 13 4 1 7 1 11 2h-4c1 2 3 2 5 2-6 1-14 2-19-1 0 0-6-5-6-6-2-4-3-9-3-14z"></path><path d="M362 186c3 0 6-1 8 0h2c1 2 2 3 2 6h-1c-2-1-4-1-6-1v1c3 0 6-1 8 1v1c-4 1-9 1-13 1v-1-2c-1 0-1 1-2 1v1l-12 1 2-3c3-4 8-6 12-6z" class="B"></path><path d="M362 186c3 0 6-1 8 0v1c-3 0-6 0-8-1z" class="E"></path><path d="M252 190c1 0 2 1 3 0h1c5 0 9-2 13-4 4-1 9-2 12-4 2-1 4-1 6-2-8 6-17 13-27 15h-1c-8 1-17-1-25 0h0c1 0 1 0 2-1 5 0 11-2 16-4z" class="O"></path><path d="M671 330c2-1 3-1 5-2l8 7c1 1 2 3 4 4l1 1c0 1 0 2-1 3-2 0-4-2-6-2l-1 1c1 2 4 3 6 4h1l-1 5c-2-2-5-3-7-5-3-4-6-8-10-12h-1l1-1c1 0 1-1 2-1l-1-1v-1z" class="B"></path><path d="M282 195l-13-1 24-16v1l1 1-2 6-1 3v6h2l-1 1c-1 0-1-1-2-1l-3 1c-1 0-4 0-5-1z" class="X"></path><path d="M293 179l1 1-2 6-1 3v6h2l-1 1c-1 0-1-1-2-1l-3 1c-1 0-4 0-5-1h7c0-3 0-6 1-9l1-1c0-2 1-3 2-5v-1z" class="D"></path><defs><linearGradient id="b" x1="670.331" y1="187.307" x2="677.121" y2="168.971" xlink:href="#B"><stop offset="0" stop-color="#4c4b4b"></stop><stop offset="1" stop-color="#666565"></stop></linearGradient></defs><path fill="url(#b)" d="M656 177c6-5 17-7 24-7 3 0 5 0 7 1h1c2 0 5 1 7 3l2 3c1 1 2 3 3 4h0c-1 0-2-1-3-1h-2c-7-5-15-7-24-5-6 1-11 4-15 9l-1-1c1-1 0-2 1-3v-3z"></path><path d="M680 170c3 0 5 0 7 1h1c2 0 5 1 7 3l2 3c1 1 2 3 3 4h0c-1 0-2-1-3-1-3-2-6-7-9-7-1-1 0-1-1-1l-7-2z" class="S"></path><defs><linearGradient id="c" x1="645.807" y1="469.714" x2="621.695" y2="473.206" xlink:href="#B"><stop offset="0" stop-color="#8a8989"></stop><stop offset="1" stop-color="#c0bfbf"></stop></linearGradient></defs><path fill="url(#c)" d="M613 464c1 0 2-1 3 0h2 1c1 1 1 1 2 1 0-1 0-2-1-3h2c1 0 1 0 2 2h3 1c1 0 1 0 2 1h0c1 0 2 1 3 1l1 1c1-1 1 0 1-1h2 0l1-1c4 0 8 7 10 10-1 3-2 7-4 10l-3-5c-6-10-18-14-28-16z"></path><defs><linearGradient id="d" x1="601.347" y1="226.039" x2="599.483" y2="221.488" xlink:href="#B"><stop offset="0" stop-color="#6d6c6d"></stop><stop offset="1" stop-color="#848282"></stop></linearGradient></defs><path fill="url(#d)" d="M612 218c-2 3-5 6-7 7-7 5-17 5-24 3-4 0-7-1-11-2-6-3-12-7-17-12s-11-13-10-20c0 2 1 4 2 6 5 11 14 19 25 23 4 2 7 2 10 3 3 0 5 1 7 1s3 0 4-1c6-2 10-4 14-10 1 1 3 0 5 1l2 1z"></path><path d="M446 495h1c2 1 4 1 5 3l1 1 2-2 4 2c-2 2-5 5-5 9 1 2-1 4-1 7h1v-1-2h0 1c-1 4-2 7-2 10h-1v7l-8-19c1-3 0-6 0-9 1-2 2-3 2-5v-1h0z" class="L"></path><path d="M454 508c1 2-1 4-1 7h1v-1-2h0 1c-1 4-2 7-2 10h-1c0-5 0-10 2-14z" class="D"></path><path d="M661 185c5-5 10-7 16-7 8 0 15 3 21 9 1 2 3 4 3 7v1c-2 1-8 0-11 0 2-1 3-1 4-1h1l-6-6c-2-1-4-2-7-3v-1h-4c-5-2-11 1-17 1z" class="P"></path><path d="M659 330c2 1 3 3 4 4v1l1 2c1 1 1 2 2 2l3 5c0 1 0 1 1 1v2l2 4c1 1 2 3 2 4v1l1 3c1 2 1 8 2 9v3h0c-1 2-1 2-1 4v2l-1-1c-1 1-1 1 0 3l-1 3v1c-1-4-1-8-2-12-2-11-7-23-16-31-3-3-6-5-9-8l1 1c2 0 3 1 5 2l7 7 2 3s1-2 2-3h-1c1-1 1-2 1-3s-1-2-2-3c-1-2-3-3-3-6z" class="L"></path><path d="M615 555h0l-1 3c-2 6-4 11-7 17-7 0-14 1-22 2h-1c1-2 3-3 5-4l8-6c1-2 4-3 5-4 2-3 9-6 13-8z" class="K"></path><defs><linearGradient id="e" x1="667.091" y1="417.733" x2="631.951" y2="420.34" xlink:href="#B"><stop offset="0" stop-color="#bcbbbb"></stop><stop offset="1" stop-color="#e9e8e8"></stop></linearGradient></defs><path fill="url(#e)" d="M638 415c4 0 7-1 10-1 8-1 14 0 21 2 0 3-1 7-3 9h0c-3-2-5-4-9-5v1l2 2c-3 0-8-2-12-2s-8 1-12 2c-1-1-3 0-4-1 2-2 5-4 7-7h0z"></path><defs><linearGradient id="f" x1="574.361" y1="641.202" x2="591.831" y2="605.671" xlink:href="#B"><stop offset="0" stop-color="#7a7979"></stop><stop offset="1" stop-color="#aaa9a9"></stop></linearGradient></defs><path fill="url(#f)" d="M575 643l1-1v-3h-1v-1c1-1 0-2 0-4l1-4c0-1 1-2 1-2 1-3 1-5 2-8l2-2c0-1 1-3 2-5h1c1-3 5-6 7-8s3-3 5-4l-13 30c-2 5-4 12-7 17-1-2-1-3-1-5z"></path><path d="M663 334l2 2c1 1 1 2 2 2 2 2 3 5 5 8v-2l1 1 1-1c1 0 1-1 2-1 3 3 9 7 10 12h-1l-2-4v1c0 1 1 3 2 5-1 6-4 12-2 19l-2 6c-1-4-1-11-4-14-1-1-1-7-2-9l-1-3v-1c0-1-1-3-2-4l-2-4v-2c-1 0-1 0-1-1l-3-5c-1 0-1-1-2-2l-1-2v-1z" class="B"></path><path d="M673 345l1-1c1 1 2 4 2 5-2-1-3-1-3-3v-1z" class="K"></path><defs><linearGradient id="g" x1="458.418" y1="820.601" x2="440.443" y2="824.285" xlink:href="#B"><stop offset="0" stop-color="#858384"></stop><stop offset="1" stop-color="#a3a1a1"></stop></linearGradient></defs><path fill="url(#g)" d="M452 815c0-1 1-3 3-5 2 2 6 14 8 17l-1 1c-1 0-2 1-3 1 0 1 0 0-1 1 0 1-1 2-1 3l-1 1c-1 1-1 1-2 1-3-3-5-3-9-3-3-1-4-2-6-5h-1l1-1c1 1 2 0 4 0 2-4 5-6 7-11l-1-1h1l2 1z"></path><path d="M449 814h1l2 1c-2 4-3 7-6 11h-3c2-4 5-6 7-11l-1-1z" class="M"></path><path d="M439 827l2 1c2 2 5 2 7 1 2 0 5 1 7 2 1 1 1 2 1 3-1 1-1 1-2 1-3-3-5-3-9-3-3-1-4-2-6-5z" class="R"></path><defs><linearGradient id="h" x1="463.963" y1="834.786" x2="434.588" y2="833.945" xlink:href="#B"><stop offset="0" stop-color="#a09f9f"></stop><stop offset="1" stop-color="#e2e1e1"></stop></linearGradient></defs><path fill="url(#h)" d="M437 825l1 2h1c2 3 3 4 6 5 4 0 6 0 9 3 1 0 1 0 2-1l1-1c0-1 1-2 1-3 1-1 1 0 1-1 1 0 2-1 3-1l1-1 3 7c-2 2-4 4-7 6h0l-1 1-3 2c-1 0-1 0-2 1l-4 2v-1l1-1 2-1v-2h-3-1-2v1l-1-1h-1-1l-1 1c-2 0-2 0-3-1s-1-1-2-1l-1 1 1 1v1l-1-1-2-4 2-2v-2-1c-1-3-1-5 1-8z"></path><defs><linearGradient id="i" x1="367.794" y1="198.756" x2="353.018" y2="166.141" xlink:href="#B"><stop offset="0" stop-color="#3c3c3d"></stop><stop offset="1" stop-color="#5f5d5d"></stop></linearGradient></defs><path fill="url(#i)" d="M348 168c11 0 22 2 30 9 3 3 5 6 6 10-1 2-1 5-2 7h-2-1c0-1-1-4-1-5l-3-6c-3-5-9-8-15-9-9-2-17 2-24 7l-1-1 5-6-1-2c2-2 6-3 9-4z"></path><path d="M409 712h1c2 1 2 3 2 5l11 22-1 2 1 1h0l-2 1c-4-1-7-2-10-4-5-5-7-11-7-18h0c1-4 2-7 5-9z" class="D"></path><path d="M410 712c2 1 2 3 2 5v2c0 1 0 2-1 3v2c0 1-1 2-1 3l-1 1c-1-2-2-4-2-6 0-4 0-7 3-10z" class="K"></path><path d="M412 717l11 22-1 2c-1-1-2-1-3-2-5-2-8-5-10-11l1-1c0-1 1-2 1-3v-2c1-1 1-2 1-3v-2z" class="O"></path><defs><linearGradient id="j" x1="490.745" y1="590.595" x2="492.945" y2="573.281" xlink:href="#B"><stop offset="0" stop-color="#bbbaba"></stop><stop offset="1" stop-color="#e8e7e7"></stop></linearGradient></defs><path fill="url(#j)" d="M478 579l-6-11v-1l2 1c3 4 6 6 11 8 5 3 11 6 17 8h1l12 2c-7 3-17 6-25 5h-1l-1-2h0c-1 1-2 1-3 1-4-1-6-8-7-11z"></path><path d="M478 579h1l1 1c2 2 5 6 7 7s5 0 7 2h-2c-1 0-1-1-3 0 0 1 1 1 1 2h-1l-1-2h0c-1 1-2 1-3 1-4-1-6-8-7-11z" class="B"></path><path d="M417 756c0-4 1-9 4-13 0 2-1 4-2 6 1 1 0 4 1 5h1c0 1 0 2 1 3v11c3 8 6 18 14 22 1 1 3 1 4 0 1 1 2 1 3 2h0c1-1 1-3 2-4l4 9v1h-3-1c-2 0-3-1-5-1-2-1-4-2-5-3h-1c1 1 2 2 3 2l2 2 1-1v1c1 1 3 1 4 1l-1 1c-3-1-7-3-10-5-8-6-15-18-16-28v-1-10z" class="D"></path><path d="M443 792h0c1-1 1-3 2-4l4 9v1h-3c-5-1-8-3-12-6h0c1 1 3 2 4 2s3-1 4-2h1z" class="C"></path><path d="M419 749c1 1 0 4 1 5h1c0 1 0 2 1 3v11h0v4l2 5h-1l-1-1-1-4c-1-3-1-5-2-8v-1c-1-1-1-2-1-3h0v-1-6c1-1 1-3 1-4z" class="Q"></path><path d="M505 592v1h-3c3 3 14 2 18 2 0 0 1-1 2-1h1v-1l8-1h1c1 1 1 0 2 1-1 0-1 0-1 1h0l6-2h4v3c-1 0-1 1-2 1h-1l1 1c-17 6-34 6-50 0l3-1-2-2 13-2z" class="P"></path><path d="M505 592v1h-3c3 3 14 2 18 2 0 0 1-1 2-1h1v-1l8-1h1c1 1 1 0 2 1-1 0-1 0-1 1h0c-5 2-9 3-13 4s-13 0-17-1l-9-1-2-2 13-2z" class="B"></path><path d="M661 185c6 0 12-3 17-1h4v1c3 1 5 2 7 3l6 6h-1c-1 0-2 0-4 1h-34c1-4 3-7 5-10zm-92 402c3-3 6-5 10-7 3-1 7-1 11-1 5-1 10-2 16-2-1 3-2 7-4 9l-10 3-9 5-14-7zM387 245l1-1c1 1 1 1 2 1l-4 5c-2 1-4 1-6 2s-3 2-4 4c-3 8-5 13-5 21v-2c-1 1-1 2 0 4v2 2h-1v-1 1l-3-1c-2-1-8 0-10 0v-3c2-5 3-9 6-14 5-10 13-17 24-20z" class="K"></path><path d="M387 245l1-1c1 1 1 1 2 1l-4 5c-2 1-4 1-6 2s-3 2-4 4h-1c2-3 4-5 7-7 1-2 3-2 5-4z" class="Q"></path><path d="M375 256h1c-3 8-5 13-5 21v-2c-1 1-1 2 0 4v2 2h-1v-1 1c-2-4-1-10-1-14h0c1-5 3-9 6-13z" class="R"></path><defs><linearGradient id="k" x1="450.731" y1="800.05" x2="430.881" y2="805.103" xlink:href="#B"><stop offset="0" stop-color="#a4a3a3"></stop><stop offset="1" stop-color="#c6c5c4"></stop></linearGradient></defs><path fill="url(#k)" d="M416 753l1 3v10 1c1 10 8 22 16 28 3 2 7 4 10 5h1c2 1 4 1 7 1l3 7c-8 6-21 10-31 8v-1h2c1 1 1 1 2 1h1 2c2-1 3-1 5-1-1-1-2-2-2-3l-1-1v-1c0-1 0-2-1-3v-3c-1-2-1-8-1-10l-3-4h0l-1-1c0-1-1-1-2-2v-1l-2-3c-1-1-1-2-1-3l-1-1h0l-1-1v-2h-1c0-2-1-3-1-5h0l-1-1v-3-1-4c-1-1-1-1-1-2 1-1 1-2 1-3v-4z"></path><defs><linearGradient id="l" x1="538.83" y1="686.547" x2="553.263" y2="694.792" xlink:href="#B"><stop offset="0" stop-color="#5a5959"></stop><stop offset="1" stop-color="#888687"></stop></linearGradient></defs><path fill="url(#l)" d="M531 685c1-3 0-6 1-9 2 2 4 4 7 6h0c6 3 11 4 17 2h3l2-1-3 6-1 4h-2c-1-1-1-1-2-1h-1c-2 0-6 3-7 5l-5 5-2 2h-1v-1c-2-3-4-7-5-10-1-1-1-2-1-3v-5z"></path><path d="M553 692l1-1c1 0 3-2 4-2l-1 4h-2c-1-1-1-1-2-1z" class="C"></path><path d="M539 693v3 2-1l1 1h0c-1 2-1 3 0 4l-2 2h-1v-1l2-10z" class="X"></path><path d="M531 685c1-3 0-6 1-9 2 2 4 4 7 6h0c1 4 0 8 0 11l-2 10c-2-3-4-7-5-10-1-1-1-2-1-3v-5z" class="T"></path><path d="M348 195h-11c-2 0-3 0-4-1v-3c1-4 3-6 6-8 6-5 13-7 21-5 5 1 9 3 12 8h-2c-2-1-5 0-8 0-4 0-9 2-12 6l-2 3z" class="Y"></path><defs><linearGradient id="m" x1="637" y1="480.001" x2="615.509" y2="486.299" xlink:href="#B"><stop offset="0" stop-color="#a09f9f"></stop><stop offset="1" stop-color="#d6d5d5"></stop></linearGradient></defs><path fill="url(#m)" d="M610 465c11 2 24 7 30 17 1 2 2 4 3 5l-6 14v2c-1 1-2 1-4 1-1-1-1-9-2-12-1-6-6-13-11-17-2-1-5-2-8-3-1-3-2-4-2-7z"></path><path d="M666 254c8 7 14 16 18 27 0 1 2 4 2 6v1c-1 0-2-1-3-2-3-1-6-3-10-3-3-1-9 0-11-1l-1-2v-13l1-1 4-12z" class="K"></path><path d="M422 757c0-3 1-7 1-9l2-5c2 4 3 8 5 12 3 7 6 13 8 19v1l-2-1c-1 0-1 0-1 1v3c1 1 2 1 3 1h2c1 1 3 5 2 7 0 1-1 3-2 4s-3 1-4 0c-8-4-11-14-14-22v-11z" class="B"></path><path d="M566 589c5 2 11 4 15 7-1 1-2 1-3 1-2 1-5 0-7 0-11 1-22 8-30 15l-4 4c-2 2-4 5-6 8 1-7 1-14 1-22 12-3 23-8 34-13z" class="K"></path><path d="M345 165c9-4 21-3 31 1 7 2 12 7 15 13 2 4 2 8 2 12l-1 4h-6l-2-8c-1-4-3-7-6-10-8-7-19-9-30-9l-3-1v-2z" class="G"></path><defs><linearGradient id="n" x1="427.881" y1="208.626" x2="436.74" y2="179.956" xlink:href="#B"><stop offset="0" stop-color="#c7c6c6"></stop><stop offset="1" stop-color="#f4f3f3"></stop></linearGradient></defs><path fill="url(#n)" d="M420 207v-13h-5c3-5 7-13 13-15 4-2 12 1 15 3 3 1 6 4 7 7 1 2 1 4-1 5-1 2-3 3-5 3h-2c-2-3-1-5-4-8-3 0-4 0-6 1-4 2-6 6-7 10s-1 8 0 11l-1 1c-1-2-1-3-2-5h0-2 0z"></path><defs><linearGradient id="o" x1="599.267" y1="208.68" x2="598.708" y2="180.025" xlink:href="#B"><stop offset="0" stop-color="#cbcac9"></stop><stop offset="1" stop-color="#f8f7f7"></stop></linearGradient></defs><path fill="url(#o)" d="M607 207c0-5 0-10-3-15-2-1-4-3-6-3-3 0-4 1-6 2-2 2-3 4-3 7v1c-4-1-6-2-8-5-1-1-1-3-1-4 1-2 5-7 7-8 1 0 3-1 5-1s5-1 8-2c3 0 6 1 9 3 4 4 9 12 9 18 1 0 0 1 0 2h-1l-1-1c-1 2 0 5 0 7h-4l-2 2c-1 1-2 1-3 0v-3z"></path><path d="M644 175c6-8 13-11 23-12 9-1 16 0 24 6l5 5h-1c-2-2-5-3-7-3h-1c-2-1-4-1-7-1-7 0-18 2-24 7 0 1-1 2-2 2-4 5-5 10-6 16h-7v-1c-2-7 0-13 3-19z" class="G"></path><defs><linearGradient id="p" x1="400.204" y1="362.086" x2="405.947" y2="434.199" xlink:href="#B"><stop offset="0" stop-color="#bdbbbb"></stop><stop offset="1" stop-color="#dfdede"></stop></linearGradient></defs><path fill="url(#p)" d="M421 435c-2-2-4-3-6-5l-8-5c-2-2-5-3-6-5-3-6-6-13-9-19-2-5-5-11-6-16-1-8 5-18 9-24 2 1 3 3 4 5h0c-4 10-4 17-2 27h0 0l-1-2-1-4h0c-1 1-1 1 0 2v1 2h0l2 4 3 7h0l2 3v1s1 1 1 2c1 0 1 1 2 2 0 1 1 1 1 2h1c0 1 1 2 1 2 0 1 1 2 2 2 0 1 1 2 1 2 0 1 1 2 2 2 0 2 1 2 2 3s1 2 2 3l2 2h1s1 1 1 2c1 2 2 3 3 4-1 1 0 1-1 1s-1 0-2-1z"></path><path d="M609 723c0 2 0 2 1 4 1-1 1-1 1-2 2-2 5-4 7-5 1 2 2 8 4 8 2 1 4 0 5-1 6-2 7-7 10-11 0 5-1 9-2 14-3 6-6 11-12 13l-5 2h-6c-5-1-8-3-12-6 0-2 1-4 2-6l5-11c1 0 1 1 2 1z" class="H"></path><defs><linearGradient id="q" x1="614.623" y1="517.803" x2="592.599" y2="558.626" xlink:href="#B"><stop offset="0" stop-color="#b7b6b6"></stop><stop offset="1" stop-color="#e7e6e6"></stop></linearGradient></defs><path fill="url(#q)" d="M604 514l1-1h0 2c1 1 2 1 3 2 4 7 4 20 2 27-2 4-3 10-6 13-2 3-7 6-10 9l-1-1c0-1 1-4 1-6 0-4 0-7-1-11 1-3-1-6-2-9-2-1-2-1-5-1 3-2 5-2 8-3 1-1 2-1 3-2l1 1h0l3-6c1-4 1-8 1-12z"></path><path d="M596 533l1 1v1h2v1c2-1 3-3 5-4-1 4-2 7-3 11-1 2-2 3-2 5l-1 1c0-5 0-9-3-14h0l1-2z" class="B"></path><path d="M604 514l1-1h0 2c-1 1-2 1-2 2 0 4 1 8 0 11 0 2-1 4-1 6-2 1-3 3-5 4v-1h-2v-1l-1-1c1-1 2-1 3-2l1 1h0l3-6c1-4 1-8 1-12z" class="M"></path><path d="M588 536c3-2 5-2 8-3l-1 2h0c3 5 3 9 3 14l-3 14c0-1 1-4 1-6 0-4 0-7-1-11 1-3-1-6-2-9-2-1-2-1-5-1z" class="F"></path><defs><linearGradient id="r" x1="658.128" y1="444.929" x2="646.03" y2="423.048" xlink:href="#B"><stop offset="0" stop-color="#838282"></stop><stop offset="1" stop-color="#acabab"></stop></linearGradient></defs><path fill="url(#r)" d="M650 423c4 0 13 1 16 4h0c-1 4-3 8-4 12-1 1-1 3-2 3h0-1c0 1 0 2-1 4-1 0-4-1-5 0-1 0-2 1-3 1h-1-1c-2-1-5-2-7-3v1h-3-1v-5l3-3c-1-1-5-1-7-2l-1-1c-1 0-2-1-2-2 0 0 1-1 2-1 1-1 2-1 2-2h-2c2-1 3-2 5-4l9-1c1-1 2-1 4-1z"></path><path d="M645 433l1 2c-2 1-6 3-6 6h0l-2 1v1h1c1 0 0 0 1 1-1 0-2 0-3 1v-5l3-3c-1-1-5-1-7-2l-1-1c5 2 9 1 13-1z" class="S"></path><path d="M646 424c-1 0-2 1-3 1l2 2c1 0 1 0 2 1h3v1c1 0 1 0 2 1-1 1-5 3-6 5l-1-2c-4 2-8 3-13 1-1 0-2-1-2-2 0 0 1-1 2-1 1-1 2-1 2-2h-2c2-1 3-2 5-4l9-1z" class="C"></path><path d="M645 433c1 0 2-1 3-2l-1-1c-1-1-3-1-5-1h0c2-1 5 0 8 0 1 0 1 0 2 1-1 1-5 3-6 5l-1-2z" class="D"></path><defs><linearGradient id="s" x1="590.874" y1="628.012" x2="575.621" y2="601.479" xlink:href="#B"><stop offset="0" stop-color="#828182"></stop><stop offset="1" stop-color="#dcdbda"></stop></linearGradient></defs><path fill="url(#s)" d="M591 592l11-4-6 13c-2 1-3 2-5 4s-6 5-7 8h-1c-1 2-2 4-2 5l-2 2c-1 3-1 5-2 8 0 0-1 1-1 2l-1 4c0 2 1 3 0 4v1h1v3l-1 1c-1-1-1-3-1-4h0-3c-2 1-4 1-5 4 0-12 2-22 6-33 1-1 1-3 3-5 4-5 11-10 16-13z"></path><path d="M623 684c2 1 3 2 4 3l3 4 1 1h0l3 3c3 4 4 9 4 13v1c-4 6-5 14-13 17-2-1-2 0-3-1-1-2-2-4-3-5l1-1c1-1 2-2 2-3-1-3-4-4-6-6-1-1-1-1-1-3s1-5 2-7l6-16z" class="B"></path><path d="M615 707c1-1 2-1 4-1 3 0 5-4 8-4-3 3-5 7-9 8h-2c-1-1-1-1-1-3z" class="V"></path><defs><linearGradient id="t" x1="384.274" y1="289.243" x2="357.981" y2="315.473" xlink:href="#B"><stop offset="0" stop-color="#878686"></stop><stop offset="1" stop-color="#a5a4a4"></stop></linearGradient></defs><path fill="url(#t)" d="M374 313c-4 1-9 5-13 8-2-10-4-20-5-31h1c2-1 5-1 6-1 7 1 5 6 10 9 2 1 4 0 6 0l1-1 2-1c2 0 2 0 4-2 5 4 10 6 15 9h-1c-8 1-18 6-26 10z"></path><defs><linearGradient id="u" x1="521.705" y1="564.159" x2="554.493" y2="587.13" xlink:href="#B"><stop offset="0" stop-color="#adacac"></stop><stop offset="1" stop-color="#ecebea"></stop></linearGradient></defs><path fill="url(#u)" d="M559 567c5-3 8-8 11-12 1 1 1 2 2 3h1 2c-1 2-2 3-2 5l-8 8c1 0 1 0 1 1l-3 2h1l1 1-1 2c-8 6-16 11-25 15l-6 2h0c0-1 0-1 1-1-1-1-1 0-2-1h-1l-8 1v1h-1c-1 0-2 1-2 1-4 0-15 1-18-2h3v-1c10-3 19-5 29-9l-2-2c2-1 3-2 5-2l10-5-1 1c1 0 0 0 2-1h1c2 0 8-5 10-7z"></path><path d="M547 574l-1 1c1 0 0 0 2-1h1c-4 4-11 7-15 9l-2-2c2-1 3-2 5-2l10-5z" class="M"></path><defs><linearGradient id="v" x1="548.696" y1="580.877" x2="552.804" y2="585.623" xlink:href="#B"><stop offset="0" stop-color="#b6b5b5"></stop><stop offset="1" stop-color="#d9d7d8"></stop></linearGradient></defs><path fill="url(#v)" d="M565 571c1 0 1 0 1 1l-3 2h1l1 1-1 2c-8 6-16 11-25 15l-6 2h0c0-1 0-1 1-1-1-1-1 0-2-1h-1c3-1 6-2 8-4 4-1 8-3 11-6l15-11z"></path><defs><linearGradient id="w" x1="314.831" y1="195.886" x2="312.152" y2="178.596" xlink:href="#B"><stop offset="0" stop-color="#5a595a"></stop><stop offset="1" stop-color="#868585"></stop></linearGradient></defs><path fill="url(#w)" d="M325 165l2-1c1-1 1-1 2-1v1c-1 0-1 1-2 2h0c-1 1-2 3-2 4v1c-1 2-1 4-2 6v1 6c-1 1-1 2-2 2v1c1 0 1 0 2 1 2 0 4 1 5 3 1 1 1 3 1 4h-2-12l-3-4c-1 1 0 2 0 3h0l-4 1c-5 0-10-1-15 0h-2v-6l1-3c3 0 4-2 6-4h0c0-2 0-3 2-4 1-1 0-1 1-2h0c1-1 1-2 2-3 0 1 0 2 1 2 2-1 3-2 5-4v1 1h1 0l3-3 5-4 2-1 1 1c1 0 2-1 4-1z"></path><path d="M319 184h0l1 1v1h1v1c1 0 1 0 2 1 2 0 4 1 5 3 1 1 1 3 1 4h-2c0-1-1-2-1-2-3-2-6-6-8-7h0c1-1 1 0 1-1v-1z" class="Y"></path><path d="M300 193l5-9-4 10 7 1c-5 0-10-1-15 0h-2v-6l1 1c1 1 2 1 3 3 1 1 2 1 3 1h1l1-1z" class="S"></path><path d="M320 165l1 1c1 0 2-1 4-1-1 0-1 1-1 2h0c-1 1 0 1-1 1-3 1-5 3-6 5 0 2-1 3-2 5-2 2-4 3-7 4 0-1 1-2 1-3h1l-1-1-4 5v-1c1-1 1-2 2-3v-1-2l3-3 3-3 5-4 2-1z" class="B"></path><path d="M320 165l1 1c1 0 2-1 4-1-1 0-1 1-1 2-3 0-6 1-8 3v1h-1l-2-1 5-4 2-1z" class="J"></path><path d="M309 178c1-1 5-6 7-6-1 1-2 1-2 3l3-2c0 2-1 3-2 5-2 2-4 3-7 4 0-1 1-2 1-3h1l-1-1z" class="K"></path><defs><linearGradient id="x" x1="317.405" y1="182.949" x2="322.126" y2="169.043" xlink:href="#B"><stop offset="0" stop-color="#757474"></stop><stop offset="1" stop-color="#abaaaa"></stop></linearGradient></defs><path fill="url(#x)" d="M325 165l2-1c1-1 1-1 2-1v1c-1 0-1 1-2 2h0c-1 1-2 3-2 4v1c-1 2-1 4-2 6v1 6c-1 1-1 2-2 2h-1v-1l-1-1h0c-1-1-1-2-1-3-1 1-1 1-1 2l-1-5h0-1c1-2 2-3 2-5 1-2 3-4 6-5 1 0 0 0 1-1h0c0-1 0-2 1-2z"></path><defs><linearGradient id="y" x1="295.802" y1="189.657" x2="303.802" y2="182.52" xlink:href="#B"><stop offset="0" stop-color="#6e6c6c"></stop><stop offset="1" stop-color="#898888"></stop></linearGradient></defs><path fill="url(#y)" d="M309 171v1 1h1 0l-3 3v2 1c-1 1-1 2-2 3v1 1l-5 9-1 1h-1c-1 0-2 0-3-1-1-2-2-2-3-3l-1-1 1-3c3 0 4-2 6-4h0c0-2 0-3 2-4 1-1 0-1 1-2h0c1-1 1-2 2-3 0 1 0 2 1 2 2-1 3-2 5-4z"></path><path d="M309 171v1 1h1 0l-3 3c-1 0-1 0-2 1h-1c-1 1-2 2-4 3v1c-1-1 0-2 0-3 1-1 0-1 1-2h0c1-1 1-2 2-3 0 1 0 2 1 2 2-1 3-2 5-4z" class="K"></path><path d="M292 186c3 0 4-2 6-4-1 3-3 6-1 9 1 1 1 1 2 1l1 1-1 1h-1c-1 0-2 0-3-1-1-2-2-2-3-3l-1-1 1-3z" class="Y"></path><defs><linearGradient id="z" x1="634.425" y1="517.685" x2="608.045" y2="521.777" xlink:href="#B"><stop offset="0" stop-color="#848383"></stop><stop offset="1" stop-color="#b1b0b0"></stop></linearGradient></defs><path fill="url(#z)" d="M627 487c2 7 2 15 2 22l6-2c-3 11-9 22-13 33-1 2-2 7-4 9-1 2-6 3-8 5l2-4c5-12 7-23 2-36 1 0 2-1 4-2v-1c-5 0-9 1-13 2 2-3 3-6 5-9 2-2 4-3 6-5 5-3 8-7 11-12z"></path><path d="M437 493c-1-1-1-1-2-1-1-2-9-20-9-21 7 1 15 2 22 5 3 1 8 4 11 4l-2-2h3v1c2 1 3 1 5 2 2 0 5 2 6 2l1-1c1 0 1-1 2-1h1c0-1 1-1 2-1v1c-1 0-6 3-6 3l2 1c1 1 2 2 3 2l-1 1c-6 3-12 6-16 11l-4-2-2 2-1-1c-1-2-3-2-5-3h-1-1v-1c-3-1-5-1-7 0l-1-1z" class="C"></path><path d="M437 493c5-2 10-2 15 1l3 3-2 2-1-1c-1-2-3-2-5-3h-1-1v-1c-3-1-5-1-7 0l-1-1z" class="D"></path><path d="M548 629c2-11 8-13 16-18 3-2 6-5 9-7l-3 6c-4 12-8 25-7 38 0 2 1 4 1 6 1 2 4 7 3 9v1c-3 1-9 1-12-1-2-1-4-2-5-4v-1-3h0l3-3-5-5 2-1c-3-5-3-11-2-17z" class="C"></path><path d="M550 646v1c3 2 5 3 8 4v1c-2 1-2 1-4 1l-1-1-5-5 2-1z" class="N"></path><defs><linearGradient id="AA" x1="586.944" y1="758.31" x2="607.864" y2="774.783" xlink:href="#B"><stop offset="0" stop-color="#c3c2c2"></stop><stop offset="1" stop-color="#e4e4e3"></stop></linearGradient></defs><path fill="url(#AA)" d="M594 775h1c-2-3-5-4-7-6l11-27c1 0 2 1 3 2 3 2 5 3 8 4h0 2c2 4 1 14 0 19 0 1 0 3-1 4-2 2-3 6-4 8l-1-1c-2 5-6 10-11 11-1 1-4 1-5 0s-2-3-2-4c-1-4-1-5 2-8v-1h-1v-1h3v1l2-1z"></path><path d="M610 748h2c2 4 1 14 0 19 0 1 0 3-1 4-2 2-3 6-4 8l-1-1c4-9 7-20 4-30z" class="I"></path><defs><linearGradient id="AB" x1="396.86" y1="255.896" x2="389.401" y2="296.743" xlink:href="#B"><stop offset="0" stop-color="#7d7c7c"></stop><stop offset="1" stop-color="#9d9c9c"></stop></linearGradient></defs><path fill="url(#AB)" d="M380 252l5 3c2 3 1 7 1 10-1 2-2 4-3 5 1 4 3 8 6 10l3 3c1 3 5 7 8 8 1 1 2 2 3 2v1c4 3 9 4 12 8l-4 1c-13 1-24 5-35 11l-2-1c8-4 18-9 26-10h1c-5-3-10-5-15-9-2-2-4-4-7-6s-8-4-12-5v-1l3 1v-1 1h1v-2-2c-1-2-1-3 0-4v2c0-8 2-13 5-21 1-2 2-3 4-4z"></path><path d="M370 283v-1 1h1v-2-2c-1-2-1-3 0-4v2l1 6c3 1 6 2 9 4 7 4 12 10 20 13 2 0 4 1 5 1 2 0 6 0 7 1l-2 1c-13 1-24 5-35 11l-2-1c8-4 18-9 26-10h1c-5-3-10-5-15-9-2-2-4-4-7-6s-8-4-12-5v-1l3 1z" class="D"></path><defs><linearGradient id="AC" x1="553.561" y1="676.833" x2="521.406" y2="643.222" xlink:href="#B"><stop offset="0" stop-color="#929191"></stop><stop offset="1" stop-color="#cccaca"></stop></linearGradient></defs><path fill="url(#AC)" d="M528 641c0-1 2-9 2-10h1c1 4 0 9 1 13 2 7 6 13 10 19 1 2 4 5 6 6l1 1h1l2 1c4 0 10 0 13-3 1-1 1-1 2-3l1-1c1 2-3 9-4 11s-2 4-4 5c0 1 0 2 1 3l-2 1h-3c-6 2-11 1-17-2h0c-3-2-5-4-7-6-1 3 0 6-1 9 0 0-1-1-1-2-1-3 0-6-1-10-3-11-5-20-4-32l1-1h1c-1 1 0 1-1 2v1-1c1 0 1-1 1-2l1 1z"></path><path d="M567 665l1-1c1 2-3 9-4 11s-2 4-4 5c-3 1-5 2-8 2-6 1-13-2-17-6 1 0 1 0 2 1h1c1 1 3 1 4 2h4c2-1 3-1 5-2 3 0 4-1 6-3 1-1 0-1 0-2h-3-2l-2-2 2 1c4 0 10 0 13-3 1-1 1-1 2-3z" class="K"></path><path d="M526 640h1c-1 1 0 1-1 2v1-1c1 0 1-1 1-2l1 1c-3 10-3 21 3 30 1 2 2 3 4 5 4 4 11 7 17 6 3 0 5-1 8-2 0 1 0 2 1 3l-2 1h-3c-6 2-11 1-17-2h0c-3-2-5-4-7-6-1 3 0 6-1 9 0 0-1-1-1-2-1-3 0-6-1-10-3-11-5-20-4-32l1-1z" class="D"></path><defs><linearGradient id="AD" x1="723.811" y1="193.72" x2="724.17" y2="175.256" xlink:href="#B"><stop offset="0" stop-color="#616060"></stop><stop offset="1" stop-color="#959493"></stop></linearGradient></defs><path fill="url(#AD)" d="M698 161c-1 0-1-1-2-2l-3-2h0 1c0-1 0-1 1-1 3-1 8-1 11 1 2 0 4 1 5 2 3 1 6 3 9 5l3 2 2 2v1l1 1h0c1 1 2 2 2 3l1 1v1c1 2 1 2 1 4h1c0 1 0 1 1 2s2 2 3 2l2 1 1-1c1 4 1 8 2 12h-17l-4-1c-2 1-7 1-10 1l1-6h0l-1-1h-1c2-2 2-3 2-5-1-1-1-2-1-2l1-1c-1-1-1-1-2-1l1-1-1-3-1-1c0-2-2-6-3-7-1 0-5-5-6-6z"></path><path d="M719 194c0-2 0-4 1-7l3 8-4-1z" class="M"></path><defs><linearGradient id="AE" x1="717.909" y1="179.468" x2="720.569" y2="161.183" xlink:href="#B"><stop offset="0" stop-color="#939291"></stop><stop offset="1" stop-color="#dadad9"></stop></linearGradient></defs><path fill="url(#AE)" d="M711 159c3 1 6 3 9 5l3 2 2 2v1l1 1h0c1 1 2 2 2 3l1 1v1c1 2 1 2 1 4h1c0 1 0 1 1 2v3c0-1-1-2-1-3l-1 1c-2-1-3-1-5-1l3 6 1 1c1 0 1 1 1 1h1v1c-2-1-3-2-4-4h0c-2-3-3-5-5-7v-1l-2-4c-1-2-2-4-4-6v1l-1-2c-2-2-4-3-6-5l-2-1 1-1h-1v1l-1-1h1 1c1-1 1-1 2-1h1z"></path><path d="M698 161c-1 0-1-1-2-2l-3-2h0 1c0-1 0-1 1-1 3-1 8-1 11 1 2 0 4 1 5 2h-1c-1 0-1 0-2 1h-1-1l1 1v-1h1l-1 1 2 1c2 2 4 3 6 5l1 2v-1c2 2 3 4 4 6l2 4-1 1c-1 0-1 0-2-1h-3c0-1-1-2-1-3l-1 1c-1-1-1-1-2-1s-2 0-3-1v-1c-1 1-1 1 0 2v3l-1-3-1-1c0-2-2-6-3-7-1 0-5-5-6-6z" class="H"></path><defs><linearGradient id="AF" x1="714.188" y1="177.323" x2="714.803" y2="169.291" xlink:href="#B"><stop offset="0" stop-color="#949192"></stop><stop offset="1" stop-color="#ababaa"></stop></linearGradient></defs><path fill="url(#AF)" d="M698 161c2 0 3 1 5 3l1-1 2 2c5 1 9 6 12 10-1-2-2-4-2-6v-1c2 2 3 4 4 6l2 4-1 1c-1 0-1 0-2-1h-3c0-1-1-2-1-3l-1 1c-1-1-1-1-2-1s-2 0-3-1v-1c-1 1-1 1 0 2v3l-1-3-1-1c0-2-2-6-3-7-1 0-5-5-6-6z"></path><path d="M698 161c2 0 3 1 5 3l1-1 2 2c3 4 6 6 9 10l-1 1c-1-1-1-1-2-1s-2 0-3-1v-1c-1 1-1 1 0 2v3l-1-3-1-1c0-2-2-6-3-7-1 0-5-5-6-6z" class="K"></path><defs><linearGradient id="AG" x1="385.263" y1="675.119" x2="337.089" y2="670.395" xlink:href="#B"><stop offset="0" stop-color="#7f7e7e"></stop><stop offset="1" stop-color="#d3d2d2"></stop></linearGradient></defs><path fill="url(#AG)" d="M381 657h1c1-2 2-3 2-4 3 4 6 10 7 15h-1l-3-5-3 14c-1-1-1-2-2-2-1-1-1-2-1-4l-1 1v1c1 3 0 7-1 10-3 4-6 8-11 10l-1 1c-9 4-17 4-26 0-4-3-7-7-8-11v-3h0c1 4 4 8 7 10 2 1 3 2 5 3v-1c-4-2-7-5-10-9l3-4h1l1-1v1l2-1 3 2c2 2 4 2 7 2 4-1 7-4 9-7h0c3-4 5-7 6-11 0 3-2 6-3 9h0c-1 2-2 4-4 5l1 1 1 1 1-2c1 0 2-1 3-2 0 1 0 1 1 2l2-3 4-6c1 0 1-1 2-2l1-2c1 0 1-1 2-2 0-1 1-2 2-3h0c0-1 0-2 1-3z"></path><path d="M380 673c1 3 0 7-1 10-3 4-6 8-11 10 1-1 1-2 3-3s3-2 4-4h-1c-1 1-1 2-2 2h-1c5-4 7-8 9-15z" class="F"></path><path d="M367 664c0 3-2 6-3 9h0c-1 2-2 4-4 5l1 1 1 1-3 1c-3 2-7 4-11 3-3-1-6-2-8-5l2-1 3 2c2 2 4 2 7 2 4-1 7-4 9-7h0c3-4 5-7 6-11z" class="Q"></path><path d="M333 683v-3h0c1 4 4 8 7 10 2 1 3 2 5 3v-1c4 1 9 2 13 1h1c4-1 8-2 12-5h1c1 0 1-1 2-2h1c-1 2-2 3-4 4s-2 2-3 3l-1 1c-9 4-17 4-26 0-4-3-7-7-8-11z" class="R"></path><path d="M375 631l1 2c1 3 3 7 4 10l-6 12c-3 1-4 3-6 6 0 1-1 2-1 3-1 4-3 7-6 11h0c-2 3-5 6-9 7-3 0-5 0-7-2l-3-2-2 1v-1l-1 1h-1l-3 4c3 4 6 7 10 9v1c-2-1-3-2-5-3-3-2-6-6-7-10h0v3c-1-4-2-9 0-13 2-7 9-11 15-14l-2 6c0 1 0 2 1 3s2 2 4 2c1 0 1 0 1-1 2-1 3-3 4-5l10-18c2-4 5-9 9-12z" class="D"></path><path d="M335 683c-1-2-1-5-1-8 0-5 2-7 6-11-2 5-2 9-1 13l1 1-1 1h-1l-3 4z" class="E"></path><path d="M357 665h5c2 0 3-1 4-2 0 1 0 2-1 3l-1 3-1 1v1c-1 1-2 2-2 3v1c-2 3-5 6-9 7-3 0-5 0-7-2l-3-2c0-1-1-3-1-4 0-4 0-8 2-10h1c1 1 2 3 3 4 2 1 4 2 5 1 2 0 4-2 5-4z" class="J"></path><defs><linearGradient id="AH" x1="379.515" y1="641.399" x2="358.78" y2="653.466" xlink:href="#B"><stop offset="0" stop-color="#949393"></stop><stop offset="1" stop-color="#bab8b8"></stop></linearGradient></defs><path fill="url(#AH)" d="M376 633c1 3 3 7 4 10l-6 12c-3 1-4 3-6 6 0 1-1 2-1 3-1 4-3 7-6 11h0v-1c0-1 1-2 2-3v-1l1-1 1-3c1-1 1-2 1-3-1 1-2 2-4 2h-5l1-2 2-3c3-5 5-10 8-15 2-4 5-8 8-12z"></path><path d="M362 660l2-1c2 0 2 1 4-1v-1l1 1c-1 1-1 1-1 2l-3 3h-1c-1 1-2 1-3 1l-1-1h-2l2-3h2z" class="O"></path><path d="M360 660h2c1 0 1 1 2 2-1 1-3 1-4 1h-2l2-3z" class="C"></path><path d="M588 536c3 0 3 0 5 1 1 3 3 6 2 9 1 4 1 7 1 11 0 2-1 5-1 6l1 1-12 9c-5 4-11 8-16 11-8 5-19 11-27 13l-1-1h1c1 0 1-1 2-1v-3h-4c9-4 17-9 25-15l1-2-1-1h-1l3-2c0-1 0-1-1-1l8-8c5-7 8-16 13-23v-1c1-1 1-2 2-3z" class="G"></path><defs><linearGradient id="AI" x1="542.785" y1="587.255" x2="562.867" y2="586.237" xlink:href="#B"><stop offset="0" stop-color="#8d8b8c"></stop><stop offset="1" stop-color="#c0c0bf"></stop></linearGradient></defs><path fill="url(#AI)" d="M564 577l1 1h0c-1 2-3 4-5 5l-3 3-1 1 2 1h1 0l2-1 1-1-2-1 1-1h0c2 1 4 0 7 0-8 5-19 11-27 13l-1-1h1c1 0 1-1 2-1v-3h-4c9-4 17-9 25-15z"></path><defs><linearGradient id="AJ" x1="584.996" y1="535.383" x2="573.786" y2="577.001" xlink:href="#B"><stop offset="0" stop-color="#a4a2a2"></stop><stop offset="1" stop-color="#c3c2c2"></stop></linearGradient></defs><path fill="url(#AJ)" d="M588 536c3 0 3 0 5 1 1 3 3 6 2 9-6 2-8 8-11 13-4 5-8 10-14 14l-5 5-1-1 1-2-1-1h-1l3-2c0-1 0-1-1-1l8-8c5-7 8-16 13-23v-1c1-1 1-2 2-3z"></path><defs><linearGradient id="AK" x1="639.31" y1="478.074" x2="577.094" y2="453.373" xlink:href="#B"><stop offset="0" stop-color="#989797"></stop><stop offset="1" stop-color="#f0efef"></stop></linearGradient></defs><path fill="url(#AK)" d="M617 446c3 0 8 1 11 0l-2-2h1c2-1 4-1 6-1h0c1-1 2-2 4-3h0v5h1 3v-1c2 1 5 2 7 3-1 1-2 1-3 1l1 1-1 1-2 2-1 1c-1 0-2 1-4 1-1 0-1 0-1 1 0 0 2 0 3 1h2-4c2 1 3 0 4 1h0c0 2 1 3 2 4 1 2 1 3 0 5v1c-3-2-6-3-9-4l3 2-1 1h0-2c0 1 0 0-1 1l-1-1c-1 0-2-1-3-1h0c-1-1-1-1-2-1h-1-3c-1-2-1-2-2-2h-2c1 1 1 2 1 3-1 0-1 0-2-1h-1-2c-1-1-2 0-3 0-6-1-11-2-16 0h-1c-4 1-9 3-12 6l-9 6h-1 0l1-1 1-1h-3v1h-1c-1-1-2-1-3-3h4c3-2 8-5 9-8l15-10c4-2 7-5 12-6 2-1 5 0 7-1l1-1z"></path><path d="M615 451c3 2 7 2 11 4-6 1-12 0-17-2l6-2z" class="B"></path><path d="M616 449h4 3c1 2 2 3 3 4-1 0-1 0-2-1-3-1-6-1-9-1l-6 2h-2c2-2 6-2 9-4z" class="P"></path><path d="M627 464c-1-1-2-2-3-2v-1h-1c2-1 5 0 7 0 1 1 3 1 5 2l3 2-1 1h0-2c0 1 0 0-1 1l-1-1c-1 0-2-1-3-1h0c-1-1-1-1-2-1h-1zm2-12l5-5c2 0 4-1 6 0h1c2 0 2 1 4 3l-2 2-1 1c-1 0-2 1-4 1-1 0-1 0-1 1 0 0 2 0 3 1h2-4c-1-1-10-1-12-1-4-2-8-2-11-4 3 0 6 0 9 1 1 1 1 1 2 1s2 0 3-1z" class="K"></path><path d="M617 446c3 0 8 1 11 0l-2-2h1c2-1 4-1 6-1h0c1-1 2-2 4-3h0v5h1 3v-1c2 1 5 2 7 3-1 1-2 1-3 1l1 1-1 1c-2-2-2-3-4-3h-1c-2-1-4 0-6 0l-5 5c-1 1-2 1-3 1-1-1-2-2-3-4h-3-4c-3-1-5-1-7-1 2-1 5 0 7-1l1-1z" class="F"></path><path d="M620 449h8l1 1-1 2h1c-1 1-2 1-3 1-1-1-2-2-3-4h-3z" class="Y"></path><path d="M512 611h6c4-1 8-2 10-4h1v4c0 5 0 11-1 16l-2 13-1 1c-1 12 1 21 4 32v14c-2-3-3-8-5-11l-17-35 1-2c-1-1-1-3-1-4 0-4 0-8-1-11v-13-2c2 1 4 2 6 2z" class="G"></path><path d="M511 614h3l1 1c-1 6-1 11-1 17 0 3 1 6 0 9 0 1-1 1-1 2h-1l-1-29z" class="W"></path><path d="M512 611h6c4-1 8-2 10-4h1v4c0 5 0 11-1 16l-2 13-1 1v-1c0-1 1-2 0-4h0c1-1 1-1 1-2v-1l-1 1-1 3c0-4 1-8 1-12 0-3 0-6 1-8 0-2 0-4-1-5-1 0-2 0-3 1-2 0-4-1-5 0h-1l-4-2z" class="L"></path><defs><linearGradient id="AL" x1="408.025" y1="685.239" x2="381.034" y2="717.703" xlink:href="#B"><stop offset="0" stop-color="#8c8b8b"></stop><stop offset="1" stop-color="#c3c2c1"></stop></linearGradient></defs><path fill="url(#AL)" d="M386 683l6-13c2 4 4 9 6 14 4 8 9 17 12 26l-1 2c-3 2-4 5-5 9h0c-1 1-1 2-1 3v2c-1 0-1 0-2-1-2 0-5 1-6 0h0-1c-1 0-3 0-4-1v1l-1-1-1 1 2 2c1 0 2 1 2 2 1 1 1 2 0 3l-1 1-1-1c-2-2-2-4-4-6-1-1-1-2-2-3v-1c-4-6-4-12-4-19l-1 1v-2h0v-4c1-2 1-3 1-4v-1c0-1 0-2 1-2v-1c0-3 1-4 3-6v1c0-1 1-1 2-2z"></path><path d="M384 723c1-1 2-1 3-1 1 1 1 2 0 3l-1 1c-1-1-1-2-2-3z" class="N"></path><path d="M386 726l1-1 2 2c1 1 2 1 3 3 0 1-1 1-2 2-2-2-2-4-4-6z" class="F"></path><path d="M379 702h0v-4c1-2 1-3 1-4v-1c0-1 0-2 1-2v-1c0-3 1-4 3-6v1c0-1 1-1 2-2-3 9-5 17-4 26 0 2 0 3 1 4 0 2 2 4 2 6 0 1-1 2-1 3-4-6-4-12-4-19l-1 1v-2z" class="I"></path><defs><linearGradient id="AM" x1="593.931" y1="519.21" x2="574.954" y2="500.046" xlink:href="#B"><stop offset="0" stop-color="#9d9c9c"></stop><stop offset="1" stop-color="#c8c7c7"></stop></linearGradient></defs><path fill="url(#AM)" d="M576 499h1c2-1 4-2 7-4 5-3 9-3 15 0 2 1 5 3 6 6v2 1c1 2 0 4-1 6l-7 7-1 1h1l7-4c0 4 0 8-1 12l-3 6h0l-1-1c-1 1-2 1-3 2-3 1-5 1-8 3-1 1-1 2-2 3v1c-5 7-8 16-13 23 0-2 1-3 2-5h-2-1c-1-1-1-2-2-3l8-16c1-7 2-13 1-19 1-7-2-13-5-19l2-2z"></path><path d="M594 503l-1-1v-2h-2c-2 0-3 1-4 2h0-1c1-2 1-3 2-4 0-2 1-2 2-3l2 1 1 1 2 2s0 1 1 2l-2 2z" class="B"></path><path d="M593 497c2 0 3 1 4 3h1c0 1 1 3 1 4s0 2 1 3l-1 1v3l-1 1c0 2 0 3-1 4v-3h-1c-3 2-2 5-3 7s-2 3-3 5c-1 1-1 2-2 3h-1-1 0c-1 1-1 2-2 3 0-1 0-2 1-3s0-1 1-1c0-1 0-2 1-3v-1l1-1v-1-2l1-1v-3-1l1-2c0-1 1-3 1-4s0-1 1-2l2-3 2-2c-1-1-1-2-1-2l-2-2z" class="P"></path><defs><linearGradient id="AN" x1="607.083" y1="527.195" x2="565.585" y2="543.456" xlink:href="#B"><stop offset="0" stop-color="#908f90"></stop><stop offset="1" stop-color="#e8e6e6"></stop></linearGradient></defs><path fill="url(#AN)" d="M584 531c1-1 1-2 2-3h0 1 1c1-1 1-2 2-3 1-2 2-3 3-5s0-5 3-7h1v3 1l-1 1h1l7-4c0 4 0 8-1 12l-3 6h0l-1-1c-1 1-2 1-3 2-3 1-5 1-8 3-1 1-1 2-2 3v1c-5 7-8 16-13 23 0-2 1-3 2-5h-2-1c-1-1-1-2-2-3l8-16c2-2 3-4 5-6 0-1 0-1 1-2z"></path><defs><linearGradient id="AO" x1="584.558" y1="624.522" x2="534.015" y2="647.868" xlink:href="#B"><stop offset="0" stop-color="#b8b7b8"></stop><stop offset="1" stop-color="#dbdada"></stop></linearGradient></defs><path fill="url(#AO)" d="M566 589h2c1-1 1-1 1-2l14 7 9-5-1 3c-5 3-12 8-16 13-2 2-2 4-3 5h-2l3-6c-3 2-6 5-9 7-8 5-14 7-16 18-1 6-1 12 2 17l-2 1 5 5-3 3h0v3l-2-2c-1 1-2 2-2 3-1 1-1 2 0 3 1 2 3 4 5 4 6 2 11 1 16-1-1 2-1 2-2 3-3 3-9 3-13 3l-2-1h-1l-1-1c-2-1-5-4-6-6-4-6-8-12-10-19-1-4 0-9-1-13h-1c2-6 4-10 8-14l-1-1 4-4c8-7 19-14 30-15 2 0 5 1 7 0 1 0 2 0 3-1-4-3-10-5-15-7z"></path><path d="M546 627l2 2c-1 6-1 12 2 17l-2 1c-3-5-3-14-2-20z" class="D"></path><path d="M566 589h2c1-1 1-1 1-2l14 7 9-5-1 3c-5 3-12 8-16 13-2 2-2 4-3 5h-2l3-6c-3 2-6 5-9 7-8 5-14 7-16 18l-2-2c0-12 13-13 19-19v-1-1l11-7h-4c-12 0-26 9-34 18l-1-1 4-4c8-7 19-14 30-15 2 0 5 1 7 0 1 0 2 0 3-1-4-3-10-5-15-7z" class="F"></path><defs><linearGradient id="AP" x1="621.71" y1="489.589" x2="544.576" y2="482.361" xlink:href="#B"><stop offset="0" stop-color="#878686"></stop><stop offset="1" stop-color="#d0cece"></stop></linearGradient></defs><path fill="url(#AP)" d="M525 453c3 1 5 4 7 6l9 10c1 1 3 2 4 3l3-1c4-1 11 0 15 1 2 0 4-1 6 0 1 2 2 2 3 3h1v-1h3l-1 1-1 1h0 1c-1 1-2 1-2 2 11-4 22-6 35-4h2c6 1 10 4 12 9l2 3c-5 7-11 12-16 19h0c-1-5-3-8-8-11-3-2-7-3-11-2-5 1-9 4-13 6v1l-2 2c-2-3-4-5-6-7l-8-4c-1-1-3-1-5-2h0l1-1-12-5v-1-1c-1-1-2-1-3-2-1-2-3-3-5-5s-3-4-5-6h1c0-1-1-2-1-2v-1-1l-5-6c-1-1-1-2-1-3v-1z"></path><path d="M550 478l10 6-1 1c-3 0-7-2-9-4 0-1 0-1-1-2v-1h1z" class="F"></path><defs><linearGradient id="AQ" x1="549.331" y1="475.784" x2="549.669" y2="487.216" xlink:href="#B"><stop offset="0" stop-color="#8a8789"></stop><stop offset="1" stop-color="#a1a2a0"></stop></linearGradient></defs><path fill="url(#AQ)" d="M531 467h1c2 3 4 5 7 6 2 1 3 2 5 3h1c2 2 3 4 5 5 2 2 6 4 9 4l1-1 1 1c-2 2-2 0-2 3h0c1 1 2 1 2 2 2 1 5 1 6 3l1 1-8-4c-1-1-3-1-5-2h0l1-1-12-5v-1-1c-1-1-2-1-3-2-1-2-3-3-5-5s-3-4-5-6z"></path><path d="M526 457c-1-1-1-2-1-3l25 24h0-1v1c1 1 1 1 1 2-2-1-3-3-5-5h-1c-2-1-3-2-5-3-3-1-5-3-7-6 0-1-1-2-1-2v-1-1l-5-6z" class="Q"></path><path d="M532 467c0-1-1-2-1-2v-1-1l14 13h-1c-2-1-3-2-5-3-3-1-5-3-7-6z" class="B"></path><defs><linearGradient id="AR" x1="382.513" y1="255.366" x2="437.63" y2="299.987" xlink:href="#B"><stop offset="0" stop-color="#898888"></stop><stop offset="1" stop-color="#b1b0af"></stop></linearGradient></defs><path fill="url(#AR)" d="M395 244c3 0 6 0 8 1h1c3 1 5 2 7 3l5 18 1 4c1-2-1-9-2-12 1 1 2 1 2 2l11 9c-1 1-2 1-2 2 1 1 2 3 3 4-4 3-5 6-6 10-2 10 0 19 6 28l1 1c-1 0-2-1-3-1-3-3-6-7-10-9h0c-1-3-2-6-4-8-3-3-6-3-10-2v-1c-1 0-2-1-3-2-3-1-7-5-8-8l-3-3c-3-2-5-6-6-10 1-1 2-3 3-5 0-3 1-7-1-10l-5-3c2-1 4-1 6-2l4-5h0l5-1z"></path><path d="M417 260l11 9c-1 1-2 1-2 2-2-1-4-4-5-5-1-3-3-4-4-6z" class="D"></path><path d="M385 255l3-1 1 1c-1 2 0 5-1 7l1 1h0l1-3c-1 3-1 5 0 7h-1c0-1 0-2-1-2-2 2-3 3-4 5l5 10c-3-2-5-6-6-10 1-1 2-3 3-5 0-3 1-7-1-10z" class="S"></path><path d="M390 245v5 10l-1 3h0l-1-1c1-2 0-5 1-7l-1-1-3 1-5-3c2-1 4-1 6-2l4-5h0z" class="I"></path><path d="M390 245v5h-1 0v3c-2 0-2 1-4 1 0-2 0-3 1-4l4-5h0z" class="X"></path><path d="M395 244c3 0 6 0 8 1h1c3 1 5 2 7 3l5 18 1 4v5c-1 2 0 4-1 5 0 1 0 3-1 5v1h0c-1-1-1-2-1-2l-1-1c0 1 0 2 1 4h0l-1 1h-1c0-1 0-1-1-1-1-1-2-1-3-2h-1l-1-1-3-3-1-1-3-3-4-6v-10c0-1 1-1 1-2v-11c0-2 0-3-1-4z" class="B"></path><path d="M396 248l3 3-2-2v7c1 4 0 8 0 12 0 3 2 6 2 9l-4-6v-10c0-1 1-1 1-2v-11z" class="C"></path><defs><linearGradient id="AS" x1="396.931" y1="255.458" x2="422.025" y2="272.886" xlink:href="#B"><stop offset="0" stop-color="#807f7f"></stop><stop offset="1" stop-color="#a4a4a3"></stop></linearGradient></defs><path fill="url(#AS)" d="M395 244c3 0 6 0 8 1h1c3 1 5 2 7 3l5 18 1 4v5c-1 2 0 4-1 5 0 1 0 3-1 5v1h0c-1-1-1-2-1-2l-1-1c0-1-1-2-1-3l-2-3v-1l-1-5c-1-3-2-7-3-10 0-1 0-3-1-5-1-1-4-4-6-5l-3-3c0-2 0-3-1-4z"></path><path d="M395 244c3 0 6 0 8 1h1c-1 2-1 4-1 5l1 1c1 2 3 3 3 5v1l1 7c0 2 1 4 1 7-1-3-2-7-3-10 0-1 0-3-1-5-1-1-4-4-6-5l-3-3c0-2 0-3-1-4z" class="X"></path><path d="M403 245h1c-1 2-1 4-1 5l1 1c-2 0-3-1-5-2v-3h2c1-1 1-1 2-1z" class="Y"></path><defs><linearGradient id="AT" x1="484.22" y1="199.665" x2="430.869" y2="190.325" xlink:href="#B"><stop offset="0" stop-color="#b3b2b1"></stop><stop offset="1" stop-color="#dad9d8"></stop></linearGradient></defs><path fill="url(#AT)" d="M469 167c2 0 5 0 7 2 5 2 9 8 10 13 2 7 0 14-3 20-6 10-16 18-27 21-6 2-10 1-15-1l-6-3c-4-3-7-8-7-13 0-4 1-8 3-11 2-2 3-2 6-2 2 1 1 4 2 7 1 0 1 1 2 1s3 0 5-1a10.85 10.85 0 0 0 8-8c0-1 1-2 1-2l2-1c2 0 4-1 6-2 5-4 4-12 4-18 0-1 1-1 2-2z"></path><path d="M662 282c2 1 8 0 11 1 4 0 7 2 10 3v3c5 5 7 12 7 19 1 10 1 20-1 30v1h-1c-2-1-3-3-4-4l-8-7h0c-3-4-6-6-10-9-14-9-28-13-44-17 1 0 2-1 3-1 5-1 10-3 15-6 3-3 6-5 10-8 3-2 7-4 12-5z" class="K"></path><path d="M662 290v2c0-1 1-1 1-1l1-1c-1 3-5 9-8 9-1 1-3 0-4-1h1c4-1 6-5 9-8z" class="Q"></path><path d="M662 290c2-2 6-3 9-2 5 0 10 3 13 7 1 1 2 3 2 5l-1 1-2-2c-1-2-2-3-3-5-5-3-10-4-16-4l-1 1s-1 0-1 1v-2z" class="N"></path><path d="M662 282c2 1 8 0 11 1 4 0 7 2 10 3v3c-5-3-11-7-18-6-7 2-15 5-19 12-1 0-1 0-1 1-4 3-9 5-14 7l15 4c15 6 27 14 37 25 2 2 4 4 6 7h-1c-2-1-3-3-4-4l-8-7h0c-3-4-6-6-10-9-14-9-28-13-44-17 1 0 2-1 3-1 5-1 10-3 15-6 3-3 6-5 10-8 3-2 7-4 12-5z" class="R"></path><path d="M683 299l2 2 1-1c1 2 1 4 1 7 1-1 1-2 2-2v1c0 1 1 1 1 2 1 10 1 20-1 30v1c-2-3-4-5-6-7-10-11-22-19-37-25h0 0l1-1c4 0 7-1 11-2 2 0 4 0 7-1s6 0 9-1c3 0 6 0 10-1l-1-1v-1z" class="C"></path><path d="M685 301l1-1c1 2 1 4 1 7h-1c0-2-1-4-1-6z" class="R"></path><defs><linearGradient id="AU" x1="545.086" y1="196.886" x2="601.906" y2="196.055" xlink:href="#B"><stop offset="0" stop-color="#b4b2b2"></stop><stop offset="1" stop-color="#d9d9d8"></stop></linearGradient></defs><path fill="url(#AU)" d="M544 187c1-6 3-12 7-15 3-3 8-4 12-3l1 1c1 2 1 4 2 6 0 4 2 9 7 11h4v1c0 4 0 7 2 10s6 4 9 4c1 0 2 0 3-1s1-2 1-4c1-1 1-2 2-3 2 0 3 0 5 1s4 4 4 7c1 5-1 11-4 15s-9 7-14 8h-3l-2 1c-3-1-6-1-10-3-11-4-20-12-25-23-1-2-2-4-2-6 0-1 0-2-1-3 0-2 1-3 2-4z"></path><path d="M544 187c0 3 1 6 1 9 3 8 8 14 14 19 7 5 14 8 23 10l-2 1c-3-1-6-1-10-3-11-4-20-12-25-23-1-2-2-4-2-6 0-1 0-2-1-3 0-2 1-3 2-4z" class="D"></path><defs><linearGradient id="AV" x1="375.859" y1="339.239" x2="467.424" y2="392.632" xlink:href="#B"><stop offset="0" stop-color="#b1b0b0"></stop><stop offset="1" stop-color="#e4e3e2"></stop></linearGradient></defs><path fill="url(#AV)" d="M379 337c5-3 11-7 17-6 1 4 0 8 0 12 11-4 21-4 32-4 16 3 28 9 38 22 2 3 3 6 4 10-1 3-3 4-6 5v2c3 3 5 3 9 4h2c0 1 1 2 0 4-2 1 0 2-3 2-2 0-2 1-4 2-1 0 0 0-1 1v-1c1-1 2-1 3-2v-1l-1-1c-3 0-6 0-8 1h-1c-1 0-2 0-3 1-3 1-4 1-6 5v1h-2l2-7c3-9 1-16-4-24-4-7-12-14-21-16-8-2-17-1-24 4-11 7-16 19-18 31-5-10-11-22-14-33 2-5 5-9 9-12z"></path><path d="M475 382c0 1 1 2 0 4-2 1 0 2-3 2 1-1 1-2 1-3v-3h2z" class="I"></path><defs><linearGradient id="AW" x1="322.38" y1="192.398" x2="333.486" y2="145.925" xlink:href="#B"><stop offset="0" stop-color="#d4d3d3"></stop><stop offset="1" stop-color="#faf9fa"></stop></linearGradient></defs><path fill="url(#AW)" d="M315 149c25-2 54 0 73 16 10 7 14 18 16 30h-9c1-8 0-15-5-22-7-8-16-11-26-12-9-1-16 0-24 5l-1-1c-2 3-4 5-6 8l-3-1h-5c0-2 1-3 2-4s2-2 2-4h0v-1c-1 0-1 0-2 1l-2 1c-2 0-3 1-4 1l-1-1-2 1-5 4-3 3h0-1v-1-1c-2 2-3 3-5 4-1 0-1-1-1-2-1 1-1 2-2 3h0c-1 1 0 1-1 2-2 1-2 2-2 4h0c-2 2-3 4-6 4l2-6c0-2 1-4 2-5v-3l-1 2h-1c-1 0-1 1-2 1v1h-1c-2 0-2 0-3 1h-1c-2 1-2 2-5 2h0c-2 1-4 0-7 1h-4c0-2 3-3 4-5h0l6-4c-5 2-12 6-16 10 0-2-1-3-1-4l2-2 4-3h1l3-2 3-3h-2 0c-3 2 0 0-2 1h0c-1 1-1 1-2 1l-2 2c0 1 0 0-1 1h-1l-2 1c0-1 1-2 2-3 1 0 1-1 2-2h0c1 0 1-1 1-1-2 0-4 0-6-1 0 0-2 0-2 1-2 1-2 2-4 3l-1 1v-1c-1 1-1 0-1 1h-1c-1-1-1-1-2-1v1h-1l-1-3c-1-1-2-2-2-3l1-7c1 2 2 3 4 4 3 1 5 1 8 1l1-1 2-1h2c3 0 6-1 9-1 4-1 8-1 12-2 3-1 7-3 10-4l17-5z"></path><path d="M264 166h3c2-1 3-1 5-1h9l-3 2h-1-2 0c-3 2 0 0-2 1h0c-1 1-1 1-2 1l-2 2c0 1 0 0-1 1h-1l-2 1c0-1 1-2 2-3 1 0 1-1 2-2h0c1 0 1-1 1-1-2 0-4 0-6-1z" class="B"></path><path d="M309 165v1 1c-2 1-3 2-4 4l-2 2c-1 1-1 2-2 3h0c-1 1 0 1-1 2-2 1-2 2-2 4h0c-2 2-3 4-6 4l2-6c0-2 1-4 2-5v1h1c2 0 4-2 5-4s5-5 7-7z" class="T"></path><path d="M330 155h1 2 2v-1c3 0 9 0 12 1-2 0-4 0-6 1v1h1c-1 0-2 1-3 1h-3v1c-1 0-1 0-2 1h-1l-2 2-2 1c-1 0-1 0-2 1l-2 1c-2 0-3 1-4 1l-1-1c3-3 8-5 10-10z" class="G"></path><defs><linearGradient id="AX" x1="271.968" y1="181.364" x2="281.374" y2="160.123" xlink:href="#B"><stop offset="0" stop-color="#c1c0c3"></stop><stop offset="1" stop-color="#e2e0de"></stop></linearGradient></defs><path fill="url(#AX)" d="M281 165l6-3c2 0 4 0 6-1l1 1h1 0v1l-4 2-5 3h-1l-1 1c-1 1-2 1-3 2h0c-5 2-12 6-16 10 0-2-1-3-1-4l2-2 4-3h1l3-2 3-3h1l3-2z"></path><defs><linearGradient id="AY" x1="312.68" y1="175.414" x2="317.124" y2="156.423" xlink:href="#B"><stop offset="0" stop-color="#a9a7a8"></stop><stop offset="1" stop-color="#d9d8d7"></stop></linearGradient></defs><path fill="url(#AY)" d="M309 165c2-1 3-2 5-3 3-2 6-4 10-5h2l4-2c-2 5-7 7-10 10l-2 1-5 4-3 3h0-1v-1-1c-2 2-3 3-5 4-1 0-1-1-1-2l2-2c1-2 2-3 4-4v-1-1z"></path><path d="M309 171c1-1 2-2 4-3 1 0 2-1 2-1l2-2 1 1-5 4-3 3h0-1v-1-1z" class="C"></path><defs><linearGradient id="AZ" x1="630.186" y1="251.495" x2="641.801" y2="295.488" xlink:href="#B"><stop offset="0" stop-color="#777576"></stop><stop offset="1" stop-color="#979696"></stop></linearGradient></defs><path fill="url(#AZ)" d="M618 244l1 1c0 1 0 2 1 4v-1c2-1 3-1 5-1l2-1h1 1c1 0 2 0 3-1 1 0 2 0 3 1l2-1 1 2h0c1-1 1-1 1-2h2l1 1c2 2 3 3 3 6v1l1-4 1-1 1 2 1-1 2-4 5 3c3 0 4 1 6 3 1 1 3 2 4 3l-4 12-1 1v13l1 2c-5 1-9 3-12 5-4 3-7 5-10 8-5 3-10 5-15 6-1 0-2 1-3 1s-3 0-3 1c-1 0-1 2-1 3l-1 1v-5c-4 1-5 1-8 4-2 2-3 4-5 6 2-4 4-8 5-13v-1c1-4 1-9 0-14v-1c-1-3-3-5-6-7 1-1 2-2 3-4l8-9c1-1 3-3 3-5 0-1 0-1 1-2l-1-3c-1-2-1-3 0-5 1-1 1-2 1-3v-1z"></path><path d="M661 267v13h0c-1-3-1-11 0-13z" class="T"></path><path d="M626 285c-1 1-2 2-3 2v1l-1 1c-2 1-4 3-6 5 1-1 0-1 1-2h0c-1 0-1 0-2 1v-1l2-2v-1 1c3-2 6-4 9-5z" class="P"></path><path d="M617 300c2-3 4-6 6-7 2 0 4-1 6 0-2 3-8 4-9 8h5c-1 0-2 1-3 1s-3 0-3 1c-1 0-1 2-1 3l-1 1v-5-2z" class="N"></path><path d="M656 248c3 0 4 1 6 3 1 1 3 2 4 3l-4 12c-2-3-2-5-1-8-1-2-1-4-2-6-1-1-1-1-1-2 0 0-1-2-2-2z" class="M"></path><path d="M658 250c1 1 4 3 4 4 1 1 0 3-1 4-1-2-1-4-2-6-1-1-1-1-1-2z" class="K"></path><path d="M645 253l1-4 1-1 1 2c-1 2-2 4-2 6v1c0 5 2 12 1 17-1 2-3 3-5 4l2-3c3-3 2-10 1-14v-3l-2 6c-1 2-1 3-2 5-2 5-7 10-11 13h0l-1-1c3-3 6-5 9-9 3-3 4-7 5-12 0-2 1-5 2-7z" class="S"></path><path d="M618 244l1 1c0 1 0 2 1 4v-1c2-1 3-1 5-1l2-1h1 1c1 0 2 0 3-1 1 0 2 0 3 1l2-1 1 2h0c1-1 1-1 1-2h2l1 1c2 2 3 3 3 6v1c-1 2-2 5-2 7-1 5-2 9-5 12-3 4-6 6-9 9l1 1c-1 1-4 2-4 3-3 1-6 3-9 5v-1 1l-2 2v1c1-1 1-1 2-1h0c-1 1 0 1-1 2h0c-1 1 0 1-1 2s-1 0-1 1h0l2-1c0 1-1 2-1 3 1 0 1 0 2 1v2c-4 1-5 1-8 4-2 2-3 4-5 6 2-4 4-8 5-13v-1c1-4 1-9 0-14v-1c-1-3-3-5-6-7 1-1 2-2 3-4l8-9c1-1 3-3 3-5 0-1 0-1 1-2l-1-3c-1-2-1-3 0-5 1-1 1-2 1-3v-1z" class="K"></path><path d="M618 244l1 1c0 1 0 2 1 4v-1c2-1 3-1 5-1-3 1-4 2-6 4h0c0 2 0 5-1 6v-1l-1-3c-1-2-1-3 0-5 1-1 1-2 1-3v-1z" class="F"></path><path d="M627 246h1 1c1 0 2 0 3-1 1 0 2 0 3 1-3 1-6 4-7 7l-1 1-1 7-1-1c1-2 2-8 2-10l-2 1h-1c2-2 3-3 3-5z" class="I"></path><path d="M625 251l-1 2c-4 6-6 14-8 21h0c0-2 0-3 1-5 1-5 2-10 4-15l3-3h0 1z" class="P"></path><path d="M639 245h2l1 1c2 2 3 3 3 6v1c-1 2-2 5-2 7-1 5-2 9-5 12-3 4-6 6-9 9l1 1c-1 1-4 2-4 3-3 1-6 3-9 5v-1s0-1 1-2c0-3 3-7 2-11v-1l5-15 1 1 1-7 1-1c1-3 4-6 7-7l2-1 1 2h0c1-1 1-1 1-2z" class="B"></path><path d="M617 289s0-1 1-2c0-3 3-7 2-11v-1l5-15 1 1c-2 9-4 18-8 27 4-2 8-5 11-7l1 1c-1 1-4 2-4 3-3 1-6 3-9 5v-1z" class="Y"></path><path d="M639 245h2l1 1c2 2 3 3 3 6v1c-1 2-2 5-2 7-1 5-2 9-5 12v-1c0-1 0-2 1-4 1-1-1-8-2-10 0-3-1-7 1-10h0c1-1 1-1 1-2z" class="K"></path><path d="M642 246c2 2 3 3 3 6v1c-1 2-2 5-2 7-2-4-1-9-1-14z" class="P"></path><defs><linearGradient id="Aa" x1="405.743" y1="401.037" x2="429.462" y2="358.699" xlink:href="#B"><stop offset="0" stop-color="#bab9b9"></stop><stop offset="1" stop-color="#eeeded"></stop></linearGradient></defs><path fill="url(#Aa)" d="M395 361c5-6 11-11 19-11 9-1 18 2 25 8 6 5 10 13 11 21 0 7-2 13-5 19-5 5-9 8-16 10h-5l3 1h-2s1 1 2 1h0c-6 0-10-1-14-4-1 0-1 0-2 1l1 1c1 0 1 1 1 2v1c-1-1-2-2-2-3h-1-1c-1-1-1-3-2-4-1 0-1 0-2 1 1 0 1 1 0 1h0l-2-3 1-1c-1-1-1-2-2-3s-2-1-2-3l-3-3c-2-10-2-17 2-27h0c-1-2-2-4-4-5z"></path><path d="M397 380c1 3 3 4 3 8h-1c-1 1 0 2 0 3l3 6v2c-1-1-2-1-2-3-2-6-3-10-3-16z" class="O"></path><path d="M406 402l-1-1c-4-8-2-21 1-29 1-2 3-4 6-5s5 0 8 2c0 0-1 1-1 2-3-2-5-3-8-2-2 1-4 3-5 6-3 6-3 17-1 24l1 3z" class="D"></path><path d="M420 369c4 3 6 7 9 11 3 3 5 5 8 6l-1 1-1 1c-1-1-2-1-3-1l1 1h-1l-3-2c-3-5-6-11-10-15 0-1 1-2 1-2z" class="Q"></path><path d="M399 366c4-5 8-10 14-11 7-2 15 0 21 4 5 4 9 9 10 15 1 3 1 8 0 11-2 1-3 1-5 1h-1l-3-3v-4c2 2 5 3 7 4 0-6-1-14-5-19s-9-8-16-8c-5-1-10 0-14 4-6 5-10 12-10 20 0 6 1 10 3 16l-3-3c-2-10-2-17 2-27z" class="F"></path><path d="M406 402l-1-3c-2-7-2-18 1-24 1-3 3-5 5-6 3-1 5 0 8 2 4 4 7 10 10 15l-2-2h0c-1-2-2-3-3-5l-1-1v1c-1 1-2 1-3 2l-3 3c-1 1-1 1-1 2h1v1c-2 0-3-1-5 0-2 2-3 5-3 7-1 1 0 2 0 3 1 3 2 6 4 9-1 0-1 0-2 1l1 1c1 0 1 1 1 2v1c-1-1-2-2-2-3h-1-1c-1-1-1-3-2-4 0-1-1-1-1-2z" class="L"></path><defs><linearGradient id="Ab" x1="424.432" y1="406.731" x2="421.836" y2="349.666" xlink:href="#B"><stop offset="0" stop-color="#c6c5c4"></stop><stop offset="1" stop-color="#f0efef"></stop></linearGradient></defs><path fill="url(#Ab)" d="M395 361c5-6 11-11 19-11 9-1 18 2 25 8 6 5 10 13 11 21 0 7-2 13-5 19-5 5-9 8-16 10h-5l3 1h-2s1 1 2 1h0c-6 0-10-1-14-4-2-3-3-6-4-9 0-1-1-2 0-3 0-2 1-5 3-7 2-1 3 0 5 0v-1h-1c0-1 0-1 1-2l3-3c1-1 2-1 3-2v-1l1 1c1 2 2 3 3 5h0l2 2 3 2h1l-1-1c1 0 2 0 3 1l1-1 1-1h1 1c2 0 3 0 5-1 1-3 1-8 0-11-1-6-5-11-10-15-6-4-14-6-21-4-6 1-10 6-14 11h0c-1-2-2-4-4-5z"></path><path d="M420 396h2c1 1 1-1 2 0-1 1-1 2-1 4 1 1 1 3 3 4h2c-1 0-3 0-4 1-2-3-4-5-6-7l2-2z" class="F"></path><path d="M409 397c0-1-1-2 0-3 0-2 1-5 3-7 2-1 3 0 5 0v1c-2 1-4 2-4 3 2 2 5 3 7 5l-2 2c2 2 4 4 6 7h0 3l1 2c0 1 1 1 1 1h-5l3 1h-2s1 1 2 1h0c-6 0-10-1-14-4-2-3-3-6-4-9z" class="C"></path><path d="M409 397h1c2 4 3 7 7 9 2 1 4 1 7 2l3 1h-2s1 1 2 1h0c-6 0-10-1-14-4-2-3-3-6-4-9z" class="D"></path><path d="M409 397c0-1-1-2 0-3 0-2 1-5 3-7 2-1 3 0 5 0v1c-2 1-4 2-4 3 2 2 5 3 7 5l-2 2c-1-1-2-2-3-2v2h0c-1-1-1-2-2-4-1 0-1 0-2 1l-1 2h-1z" class="Q"></path><path d="M403 294c4-1 7-1 10 2 2 2 3 5 4 8h0c4 2 7 6 10 9 1 0 2 1 3 1l3 4 6 5 2 1c0 1 1 1 2 2l2 1 10 7c1 1 2 3 3 3s3 0 4 1h0v2l1 2c1 0 1 0 1-1l1 1c0 1 2 2 3 3l2 2h0 0v-2c-1-2-1-5-3-8 0-2 0-4-1-6 1-1 1-3 1-4v-1c1-1 2-1 3-1l1 1c0 4-1 9 1 13 2 5 7 11 10 16 2 3 4 6 5 10l1 5s-1 1-2 1c-1 1-3 3-4 3h-1l-1 1v2l-3 2-1 1c-1 0-1 0-1 2h-2c-4-1-6-1-9-4v-2c3-1 5-2 6-5-1-4-2-7-4-10-10-13-22-19-38-22-11 0-21 0-32 4 0-4 1-8 0-12-6-1-12 3-17 6v-1c0-1 0 0-1-1-4 3-7 7-9 11v1l-3-9c0-2 3-5 4-7l1-1-3 3-2-2c-1-4-2-5-4-7v-2l14-8c11-6 22-10 35-11l4-1c-3-4-8-5-12-8z" class="U"></path><path d="M487 365l1 5s-1 1-2 1c0-2 0-4 1-6z" class="H"></path><path d="M410 320c2 0 5-1 7 0h0l-18 2c-1 0-2 0-3 1h0c-1 0-2 0-3 1h0l-2-1c4-1 7-2 11-2 2-1 4 0 7-1h1zm-37 9c-1 4-6 6-6 10l1 1c1-1 2-1 3-2 1 0 2-1 3-1l2-2c1-1 2-1 3-2 2-2 5-3 7-4 1-1 0-1 1-1h1 1l1-1h2 0c-5 2-10 4-14 8-4 3-7 7-9 11v1l-3-9c0-2 3-5 4-7l1-1c0-1 1-1 2-1z" class="E"></path><path d="M469 359c2 1 3 2 4 2l1 1 1 2c1 1 1 1 2 0h1l-1-2h0c2 3 3 5 3 9v1c0 2-1 2-2 3-2-1-1-2-1-3h-2c-2-2-2-4-3-7l-3-6z" class="L"></path><path d="M417 304c4 2 7 6 10 9 1 0 2 1 3 1l3 4 6 5h0-2-1c-1-1-1-1-2-1l-1-1c-1 0-2-1-3-1h0v-1c-1-1-2-3-3-4s-4-2-6-3v-1c-1-1-2-2-3-2-2 1-4 1-6 2h0-4c-2 1-4 1-6 1 4-2 11-1 14-4 1-1 1-3 1-4z" class="E"></path><path d="M427 313c1 0 2 1 3 1l3 4c-1 0 0 0-1-1h-1-1c-1-1-2-3-3-4z" class="C"></path><defs><linearGradient id="Ac" x1="398.529" y1="325.449" x2="406.217" y2="345.122" xlink:href="#B"><stop offset="0" stop-color="#151414"></stop><stop offset="1" stop-color="#343333"></stop></linearGradient></defs><path fill="url(#Ac)" d="M392 327c10-2 18-3 28 0-4 0-16 1-18 4-2 2-2 6-2 8 11-3 24-4 35-2h-3 0l8 2 5 2v1c-3 0-7-2-10-3-2 0-4-1-7 0-11 0-21 0-32 4 0-4 1-8 0-12-6-1-12 3-17 6v-1c0-1 0 0-1-1 4-4 9-6 14-8z"></path><path d="M428 339c3-1 5 0 7 0 3 1 7 3 10 3v-1l-5-2-8-2h0 3c12 2 24 9 31 18l3 4 3 6c1 3 1 5 3 7h2c0 1-1 2 1 3h2v2l-3 2-1 1c-1 0-1 0-1 2h-2c-4-1-6-1-9-4v-2c3-1 5-2 6-5-1-4-2-7-4-10-10-13-22-19-38-22z" class="F"></path><defs><linearGradient id="Ad" x1="389.591" y1="330.066" x2="387.93" y2="304.465" xlink:href="#B"><stop offset="0" stop-color="#cac8c9"></stop><stop offset="1" stop-color="#ecebea"></stop></linearGradient></defs><path fill="url(#Ad)" d="M403 294c4-1 7-1 10 2 2 2 3 5 4 8h0c0 1 0 3-1 4-3 3-10 2-14 4l-9 1-1 1c-1 0-2 0-3 1h-1-1l-3 3c2 0 5 1 7 0s3-1 4-1h3c1-1 2-1 3-1h2c1-1 3-1 5 0 3 0 5-1 8 0h2c1 0 0 0 1 1h2c1 0 2 1 3 1l1 1h2l-1 1c-3-2-7-3-10-4h-1c-10-1-19 2-29 3 1 0 4 0 6 1 1-1 3-1 5-1-1 1-2 1-3 1-5 1-9 4-15 4-2 1-4 2-5 4-1 0-1 1-1 1-1 0-2 0-2 1l-3 3-2-2c-1-4-2-5-4-7v-2l14-8c11-6 22-10 35-11l4-1c-3-4-8-5-12-8z"></path><path d="M403 294c4-1 7-1 10 2 2 2 3 5 4 8h0c0 1 0 3-1 4-3 3-10 2-14 4l-9 1c4-3 12-4 17-5 2-1 4-1 5-3v-3c-3-4-8-5-12-8z" class="Q"></path><defs><linearGradient id="Ae" x1="570.109" y1="515.321" x2="532.103" y2="549.852" xlink:href="#B"><stop offset="0" stop-color="#a3a2a2"></stop><stop offset="1" stop-color="#dbd9d9"></stop></linearGradient></defs><path fill="url(#Ae)" d="M560 490l8 4c2 2 4 4 6 7 3 6 6 12 5 19 1 6 0 12-1 19l-8 16c-3 4-6 9-11 12-2 2-8 7-10 7h-1c-2 1-1 1-2 1l1-1-10 5c-2 0-3 1-5 2v-48-6c0-2 0-3 1-4-2-7-3-11-2-18h0l1-2h0c1-1 1-3 2-4 3 1 4 0 7 0h0l1 1h-1l1 1 2-1h10c0-1-1-1-2-1l1-1c2-1 3-2 4-5h3 0v-1-1-1z"></path><path d="M543 557c1-1 1-1 1-2h0 1c1 6 2 11 5 17l9-8-1 3h1c-2 2-8 7-10 7h-1c-2 1-1 1-2 1l1-1v-1c0-2-1-5-2-7-1-3-1-6-2-9zm11-33l3-6 1 1h0c-1 5-2 12-6 15-2 1-5 1-7 2-1 4-1 8-1 12-1-4 0-8 0-12 0 1-1 1-1 2l-1-3-1-1c0-1 2-1 3-2l2 1c2 0 3-1 4-1 3-2 4-5 4-8z" class="F"></path><path d="M544 532l2 1s0 1-1 1h-2l-1 1-1-1c0-1 2-1 3-2z" class="R"></path><path d="M534 499c3 1 4 0 7 0h0l1 1h-1l1 1 2-1 1 2c-3 1-6 3-7 6-1 2 0 5 1 7 1 3 4 6 6 6 1 1 2 1 3 1 3-3 3-8 7-9 4 1 8 6 10 10l-7-4h0l-1-1-3 6v-3h0-2c-1 1-2 2-2 3h-4-1c-1 0-2 0-3-1-6-4-7-10-9-16l-1-1v-1h-1 0l1-2h0c1-1 1-3 2-4z" class="I"></path><path d="M552 521c1-3 2-5 3-7 1 1 3 2 4 3l-1 2-1-1-3 6v-3h0-2z" class="L"></path><path d="M534 499c3 1 4 0 7 0h0l1 1h-1l1 1c-2 1-4 2-7 4-1 0-1 1-2 2l-1-1v-1h-1 0l1-2h0c1-1 1-3 2-4z" class="C"></path><path d="M535 505v-1h0v-2c2 0 3-1 4-1s1-1 2-1l1 1c-2 1-4 2-7 4z" class="J"></path><path d="M544 500h10c11 5 18 10 22 22 1 1 1 2 1 3v1h1v-6h1v3-3c1 6 0 12-1 19l-8 16c-3 4-6 9-11 12h-1l1-3c7-7 11-15 14-23 1-4 3-9 2-13 0-7-5-15-10-19-6-6-12-8-20-7l-1-2z" class="R"></path><path d="M531 505h1v1l1 1c2 6 3 12 9 16 1 1 2 1 3 1h1 4c0-1 1-2 2-3h2 0v3c0 3-1 6-4 8-1 0-2 1-4 1l-2-1c-1 1-3 1-3 2-4-1-7-4-9-7 0-2 0-3 1-4-2-7-3-11-2-18z" class="G"></path><path d="M532 527c0-2 0-3 1-4 3 5 5 7 11 9-1 1-3 1-3 2-4-1-7-4-9-7z" class="N"></path><defs><linearGradient id="Af" x1="578.322" y1="514.279" x2="555.303" y2="494.631" xlink:href="#B"><stop offset="0" stop-color="#c8c6c6"></stop><stop offset="1" stop-color="#efeeee"></stop></linearGradient></defs><path fill="url(#Af)" d="M560 490l8 4c2 2 4 4 6 7 3 6 6 12 5 19v3-3h-1v6h-1v-1c0-1 0-2-1-3-4-12-11-17-22-22 0-1-1-1-2-1l1-1c2-1 3-2 4-5h3 0v-1-1-1z"></path><path d="M560 490l8 4c2 2 4 4 6 7 3 6 6 12 5 19v3-3h-1v6h-1v-1h1c0-7-1-14-4-20s-8-10-14-12h0v-1-1-1z" class="Q"></path><path d="M532 527c2 3 5 6 9 7l1 1 1 3c0-1 1-1 1-2 0 4-1 8 0 12l1 7h-1 0c0 1 0 1-1 2 1 3 1 6 2 9 1 2 2 5 2 7v1l-10 5c-2 0-3 1-5 2v-48-6z" class="E"></path><path d="M543 538c0-1 1-1 1-2 0 4-1 8 0 12l1 7h-1 0c0 1 0 1-1 2-1-6-1-13 0-19z" class="R"></path><path d="M654 809l5-4c1 1 1 1 1 2-1 4 0 7 0 10l1-3c3-7 10-13 15-19-1 3-2 7-4 10-1 3-3 5-4 8 2-1 2-2 3-4 3-5 4-10 6-15 1-3 3-6 5-8h1c-1 4-2 7-3 11-1 2-1 4-2 7-2 6-2 15-6 21-4 14-15 25-27 31l-7 3c-9 2-18 3-27 3-19-2-36-12-48-27 1-3 2-7 4-10 0-1 0-2 1-4v-2c2-1 2-4 3-6l5-12 4 4c3 1 8 4 9 7v8c-1 1-2 3-3 4-1 2-2 3-2 5h1c3 0 6-2 9-3 1-1 2-1 3-3-2-2-3-2-5-3h-1v-8l23 9-24 14h1 0c7-2 14-8 19-11 2-2 5-3 7-5 1-2 3-4 4-6l-1-3 1-1c-1-1-2-1-2-3 5 1 11 3 14 7 1 1 1 3 2 4h2l16-10 1 2z" class="E"></path><path d="M637 817l16-10 1 2h-1c-1 2-2 3-2 5h-1v-1c-2-1-10 4-12 5l-1-1z" class="N"></path><path d="M648 823h1c2 3-2 7-1 10 4-4 9-6 14-10-2 4-9 11-13 12-2 1-3 1-4 1v-2l4-10c-5 3-9 7-14 9-2 1-3 1-4 1 6-3 11-7 17-11z" class="I"></path><path d="M580 805c3 1 8 4 9 7v8l-1-1v-2-5l-3-3-1-1h-1l-3-2v11 6 1l-3 3h-1l-1 1c1 1 2 3 4 3 1 1 1 2 2 3 2 1 5 3 6 4h-1c0 1 0 1 1 2l2 1c2 2 4 3 7 4l1 1h0c-1 0-2-1-3-1-4-2-8-4-10-7s-5-5-8-8c-3-2-6-5-8-9v-2c3 2 6 5 9 7h0c5-4 1-16 3-20v-1z" class="J"></path><path d="M567 825c0-1 0-2 1-4 2 4 5 7 8 9 3 3 6 5 8 8s6 5 10 7h-1c-3-1-5 0-7-1-1 0-2-1-3-1h-1s-1-1-2-1l-9-9c0-1-1-1-1-2-2-2-3-4-3-6zm1-6c2-1 2-4 3-6l5-12 4 4v1c-2 4 2 16-3 20h0c-3-2-6-5-9-7z" class="B"></path><path d="M628 845l13-3c1-1 1-1 2-1 5 0 8-4 13-6h1c2 0 4-4 5-6 1-1 2-3 3-4 0-1 1-1 2-1-5 11-13 20-25 25h-1c-1 0-1-1-1 0h-1-3 0-2-1-1l-1-1h-3 0-4-1c1-1 3-1 4-2l1-1z" class="L"></path><path d="M619 806c5 1 11 3 14 7 1 1 1 3 2 4h2l1 1c-2 0-4 1-5 1-1 2-2 4-3 5s-3 2-3 2c-3 2-5 3-8 4l-1 1c-1 0-2 1-3 1 0-1 0-1 1-2h0c1-1 1-2 2-2l1-2 3-4 3-4v-2c-1-1-2-2-4-3l-1-3 1-1c-1-1-2-1-2-3z" class="U"></path><path d="M619 806c5 1 11 3 14 7 1 1 1 3 2 4h2l1 1c-2 0-4 1-5 1-1 2-2 4-3 5s-3 2-3 2c-3 2-5 3-8 4l11-9c2-2 1-3 1-5-2-4-6-5-10-7-1-1-2-1-2-3z" class="F"></path><defs><linearGradient id="Ag" x1="610.194" y1="853.713" x2="612.695" y2="838.994" xlink:href="#B"><stop offset="0" stop-color="#989797"></stop><stop offset="1" stop-color="#d4d3d2"></stop></linearGradient></defs><path fill="url(#Ag)" d="M610 845h2l1-1 3-1h2c2-1 3-2 5-2 1-1 1-2 3-2v1c0 1-1 3-1 4 1 1 1 1 3 1l-1 1c-1 1-3 1-4 2h1 4 0 3l1 1h1 1 2 0 3 1c0-1 0 0 1 0h1c-8 4-17 6-25 6-12 0-26-4-35-12h1c1 0 2 1 3 1 2 1 4 0 7 1h1c1 0 2 1 3 1h11 0l2-1z"></path><path d="M608 846h0l2-1c0 2 0 2 2 2l-1 1c-1 1-3 1-5 1l-1-1c3-2-1 1 2-1l1-1z" class="B"></path><defs><linearGradient id="Ah" x1="607.379" y1="860.822" x2="621.288" y2="814.595" xlink:href="#B"><stop offset="0" stop-color="#888787"></stop><stop offset="1" stop-color="#d4d3d2"></stop></linearGradient></defs><path fill="url(#Ah)" d="M667 824l1-2v2c0 1 1 2 1 2 0 1-1 2-1 2 0 1 1 2 1 3 0-1 0-1 1-2 0-2 1-3 2-4-4 14-15 25-27 31l-7 3c-9 2-18 3-27 3-19-2-36-12-48-27 1-3 2-7 4-10 0 2 1 4 3 6 0 1 1 1 1 2l9 9c1 0 2 1 2 1 9 8 23 12 35 12 8 0 17-2 25-6 12-5 20-14 25-25z"></path><path d="M667 824l1-2v2c-6 14-17 23-30 28-14 6-30 7-43 1-7-4-13-9-19-14-2-1-4-3-5-6h0l9 9c1 0 2 1 2 1 9 8 23 12 35 12 8 0 17-2 25-6 12-5 20-14 25-25z" class="D"></path><path d="M592 288c1-1 1-1 1-2 3-3 6-7 10-10 3 2 5 4 6 7v1c1 5 1 10 0 14v1c-1 5-3 9-5 13 2-2 3-4 5-6 3-3 4-3 8-4v5l1-1c0-1 0-3 1-3 0-1 2-1 3-1 16 4 30 8 44 17 4 3 7 5 10 9h0c-2 1-3 1-5 2v1l1 1c-1 0-1 1-2 1l-1 1-4-4 3 4 8 9c-1 0-1 1-2 1l-1 1-1-1v2c-2-3-3-6-5-8-1 0-1-1-2-2l-2-2c-1-1-2-3-4-4 0 3 2 4 3 6 1 1 2 2 2 3s0 2-1 3h1c-1 1-2 3-2 3l-2-3-7-7c-2-1-3-2-5-2l-1-1c-12-6-22-8-35-6l12 3c4 0 7 0 11 2h1v12c-8-3-15-4-24-4h-8c-7 1-13 3-19 6-9 5-20 14-23 25 0 2-1 3 0 4l1 1c1 2 0 2 0 3-3 2-4 2-7 2h-1c-1 0-1 0-1 1 1 3 1 6 3 9 2 1 4 1 7 0h2 0c0 2 0 3-1 4l-2 1h0l-3 1-1 2h-1c-1 1-1 0-2 1v1c-8-8-11-17-12-28v-6c1-5 4-9 7-14 1 0 2-3 3-4l6-9-2-1c1-2 1-4 1-6v-1h1v-2-1l2-3c2 2 3 3 4 6l1-1c0-2 2-5 3-7l3-9c1-2 1-8 3-9h1v3h1l1-3v-1c1 0 2 0 4 1h2 1 0l-3-2c-1 0-1-1-2-2l-1-2-1-2v-2h1 2v-1h1c3-2 5-2 9-3l1-1-1-2z" class="G"></path><path d="M560 329c1 4 1 7 0 10l-2-1c1-2 1-4 1-6v-1h1v-2z" class="Q"></path><path d="M548 374l-1-1c4-4 6-9 7-14v5c0 2-1 4 0 6h0v3c-1 0-1 1-1 2-1 0-2 0-3-1h-1-1zm45-52h2l1 1h1c1-1 1-1 2-1h0c1-1 2-1 4-1l-17 9c-1 0-2 0-3 1h-1v-1c1-1 1-2 2-2h1l1-1 2-2 5-3z" class="H"></path><defs><linearGradient id="Ai" x1="578.861" y1="311.926" x2="566.761" y2="318.789" xlink:href="#B"><stop offset="0" stop-color="#6e6e6f"></stop><stop offset="1" stop-color="#9e9c9a"></stop></linearGradient></defs><path fill="url(#Ai)" d="M567 330c0-2 2-5 3-7l3-9c1-2 1-8 3-9h1v3h1l-1 5c-2 8-4 16-8 24l-1-1c0-1 1-2 2-3v-3c-1 0-1 0-1 1l-3 6c0-2 2-4 2-7h-1z"></path><path d="M605 297l2 2-1 2c-1 1-2 3-4 4-3 4-7 11-11 13h-1v-1l5-4v-1c-1-1-2-1-3-1-2 0-4-1-6-1-2-1-2-2-3-3s-2-1-4-1v2c0 2-1 3-1 4l-1 1 1-5 1-3v-1c1 0 2 0 4 1h2c3 1 8 0 11-2v-1c3-1 6-3 9-5z" class="L"></path><path d="M605 297l2 2-1 2c-1 1-2 3-4 4v-1l1-1v-3c-2 1-2 3-3 4l-3 1c-4 1-8 2-12 1h-2l-4-1v-1c1 0 2 0 4 1h2c3 1 8 0 11-2v-1c3-1 6-3 9-5z" class="O"></path><defs><linearGradient id="Aj" x1="605.971" y1="307.317" x2="587.57" y2="318.253" xlink:href="#B"><stop offset="0" stop-color="#949293"></stop><stop offset="1" stop-color="#c5c4c2"></stop></linearGradient></defs><path fill="url(#Aj)" d="M609 284c1 5 1 10 0 14v1c-1 5-3 9-5 13-4 4-7 7-11 10l-5 3-2 2-1 1h-1c-1 0-1 1-2 2v1h-1l-1-1 5-4c1-2 3-3 4-4v-1c-1 1-3 2-4 3l2-3c1-2 2-3 3-3h1c4-2 8-9 11-13 2-1 3-3 4-4l1-2h0v-2l1-1v-3c1-3-1-6 1-9z"></path><path d="M609 284c1 5 1 10 0 14v1c-1 5-3 9-5 13-4 4-7 7-11 10l-5 3c1-2 4-3 6-5 5-4 11-11 12-19l1-2h0v-2l1-1v-3c1-3-1-6 1-9zm15 45c4 0 7 0 11 2h1v12c-8-3-15-4-24-4h-8c-7 1-13 3-19 6l-2-1h0c3-2 6-3 9-4s6-2 9-4h6c9 0 17 1 25 3 0-3-1-5-1-7l-7-3z" class="V"></path><path d="M592 288c1-1 1-1 1-2 3-3 6-7 10-10 3 2 5 4 6 7v1c-2 3 0 6-1 9v3l-1 1v2h0l-2-2c-3 2-6 4-9 5v1c-3 2-8 3-11 2h1 0l-3-2c-1 0-1-1-2-2l-1-2-1-2v-2h1 2v-1h1c3-2 5-2 9-3l1-1-1-2z" class="T"></path><path d="M592 291c1 1 2 1 2 2s0 2-2 2l-5 1c-2 0-4 0-5-1v-1h1c3-2 5-2 9-3zm13-5l1 1v4 1c-1 1-1 1-1 2 0 2-2 4-3 5h-1-1c0 1-1 1-1 1 0-2 0-4 1-5l3-3c1-2 2-4 2-6z" class="I"></path><path d="M603 276c3 2 5 4 6 7v1c-2 3 0 6-1 9v3l-1 1v2h0l-2-2c2-2 1-3 1-6v-4l-1-1c-1-3-1-7-2-10z" class="S"></path><path d="M606 291c0 3 1 4-1 6-3 2-6 4-9 5v1c-3 2-8 3-11 2h1 0l-3-2c-1 0-1-1-2-2l-1-2-1-2v-2h1 2c1 1 3 1 5 1h1c3 1 8 1 10 4h1s1 0 1-1h1 1c1-1 3-3 3-5 0-1 0-1 1-2v-1z" class="B"></path><path d="M606 291c0 3 1 4-1 6-3 2-6 4-9 5-5 1-9 1-13-1h3c4 2 8 1 12-1h1s1 0 1-1h1 1c1-1 3-3 3-5 0-1 0-1 1-2v-1z" class="Q"></path><path d="M581 301h2c4 2 8 2 13 1v1c-3 2-8 3-11 2h1 0l-3-2c-1 0-1-1-2-2z" class="C"></path><path d="M560 363c2-3 4-6 7-9 8-10 21-17 34-18-3 2-6 3-9 4s-6 2-9 4h0l2 1c-9 5-20 14-23 25 0 2-1 3 0 4l1 1c1 2 0 2 0 3-3 2-4 2-7 2h-1c-1 0-1 0-1 1 1 3 1 6 3 9 2 1 4 1 7 0h2 0c0 2 0 3-1 4l-2 1h0l-3 1-1 2h-1c-1 1-1 0-2 1v1c-8-8-11-17-12-28v-6l1 1c1 3 2 5 2 7l1 1h0v-1h1 1c1 1 2 1 3 1 0-1 0-2 1-2v-3c1-2 2-4 4-7h2z" class="Q"></path><path d="M559 393l6-2v1l-2 3-3 1c-1-1-1-1-1-3z" class="O"></path><path d="M551 389l1-1-1-1v-1l1 1h1l1 1h-1c0 2 2 5 4 5 1 1 1 1 2 0 0 2 0 2 1 3l-1 2c-2-3-7-6-8-9z" class="K"></path><path d="M554 370c1-2 2-4 4-7h2l-5 12h-2c0-1 0-2 1-2v-3zm-10 2v-6l1 1c1 3 2 5 2 7l1 1h0v-1h1 1v1c1 1 2 1 3 2 0 3-1 7 0 9v1h-1l-1-1v1l1 1-1 1c1 3 6 6 8 9h-1c-1 1-1 0-2 1v1c-8-8-11-17-12-28z" class="J"></path><path d="M548 385l1-1h3v1l1 1v1h-1l-1-1v1l1 1-1 1c-1-1-2-2-3-4z" class="H"></path><path d="M556 399l-2-2-2-3c-1-1-2-2-2-3-1-2-2-4-2-6 1 2 2 3 3 4 1 3 6 6 8 9h-1c-1 1-1 0-2 1z" class="T"></path><defs><linearGradient id="Ak" x1="632.647" y1="336.384" x2="639.066" y2="309.029" xlink:href="#B"><stop offset="0" stop-color="#b6b5b5"></stop><stop offset="1" stop-color="#d7d6d5"></stop></linearGradient></defs><path fill="url(#Ak)" d="M618 306c0-1 0-3 1-3 0-1 2-1 3-1 16 4 30 8 44 17 4 3 7 5 10 9h0c-2 1-3 1-5 2v1l1 1c-1 0-1 1-2 1l-1 1-4-4 3 4 8 9c-1 0-1 1-2 1l-1 1-1-1v2c-2-3-3-6-5-8-1 0-1-1-2-2l-2-2c-1-1-2-3-4-4-4-1-6-7-10-9-5-2-10-3-15-4-6-1-13-1-18 0s-9 2-13 4c-2 0-3 0-4 1h0c-1 0-1 0-2 1h-1l-1-1h-2c4-3 7-6 11-10 2-2 3-4 5-6 3-3 4-3 8-4v5l1-1z"></path><path d="M668 334l8 9c-1 0-1 1-2 1l-1 1-1-1v-2l-1-1c0-2-1-3-2-4s-1-2-1-3z" class="C"></path><path d="M617 307l1-1c7 3 15 5 23 8l-14-3c-3-1-6-1-8-2l-2-2z" class="D"></path><defs><linearGradient id="Al" x1="645.434" y1="324.079" x2="648.043" y2="307.071" xlink:href="#B"><stop offset="0" stop-color="#cbcaca"></stop><stop offset="1" stop-color="#f2f1f1"></stop></linearGradient></defs><path fill="url(#Al)" d="M618 306c0-1 0-3 1-3 0-1 2-1 3-1 16 4 30 8 44 17 4 3 7 5 10 9h0c-2 1-3 1-5 2v1l1 1c-1 0-1 1-2 1l-1 1-4-4h0l-2-1 1-1-1-1-1-1c-2-2-4-4-7-5v1l-1-1 1-1h-2c-2 0-2-1-3-2 1 0 1 0 2 1h1l1-1-2-2h-3-1l-4-2h-1c-1 0-1-1-2 0-8-3-16-5-23-8z"></path><path d="M665 330c1 0 1 0 2-1 0-1-1-1-2-2v-1l1 1c2 0 4 1 5 3v1l1 1c-1 0-1 1-2 1l-1 1-4-4h0z" class="C"></path><path d="M444 160c1-5 5-10 9-14 7-5 16-10 24-11h9c0 1 0 0-1 1l-1 1c9 2 14 6 19 13 3 3 4 6 5 10l1-2 4 13 1 4-1 19-1 3h-1c-1 4-3 9-4 13v2c-2 2-4 6-5 9-8 14-15 32-29 42h-3l2-3v-1l4-4c-2 0-4 2-6 1-2 1-3 1-4 1-1 1-3 1-4 1-3 1-5 2-8 2-1 1-2 1-3 1-1 1-2 2-3 2-2 1-5 2-7 3-3 3-4 8-6 12l-1-1-6-8-11-9c0-1-1-1-2-2l-2-6h0c0-1-1-2-1-3 0-3 0-6 2-8 2 0 3 0 5 1 0 0 0-1 1-2l3 2 1-1-4-3 1-2-4-4 2-13c2 2 4 6 6 8h1 1 1 1c5 3 13 2 19 1 13-1 25-9 34-18 2-4 5-8 7-12 1-1 1-4 1-6v-10l-1-4h0c0-1-1-3-1-4-4-7-8-9-15-11-3 0-7 1-9 4v1c0 3-1 6-1 9l-1 2c-1 2-2 4-3 5-2 1-4 1-6 0-1 0 0 0-1-1s-1-2-2-3v-2c-2-3-3-5-3-9l-1-1h-1v-1-2c0-1 0-2 1-4 0-1 0-3 1-4l1-1c0-1 1-2 2-3 0-1 0-1 1-2-1 0-1 1-2 2l-3 4c-1 1-1 2-2 3h0z" class="U"></path><path d="M498 164l-3-8c2 2 4 4 6 5h1-2l-1 1v1l-1 1z" class="M"></path><path d="M489 170l3 12h-2l-1-4c1-1 1-2 0-3v-5z" class="I"></path><path d="M500 168c1 3 1 5 2 8v9h-1v-1c-1-5-2-11-1-16z" class="C"></path><path d="M498 164l1-1v-1l1-1c0 2 1 4 2 6 1 1 2 5 1 7v-1l-1 3c-1-3-1-5-2-8l-2-4z" class="I"></path><path d="M462 153c1-2 1-3 3-4 3-1 8-1 11 0h0v3c-5-2-9-1-14 1z" class="H"></path><path d="M490 182h2c0 3 0 7-1 10 0 6-4 14-9 18h0c2-4 5-8 7-12 1-1 1-4 1-6v-10z" class="S"></path><path d="M447 169h0c1 3 1 5 3 8 1 0 2 0 3-1h0c0-2-1-3-2-4v-3c-1-1 1-9 2-11 0-2 1-3 2-5 0 0 0-1 1-1-1 2-1 3-1 4l-1 2v1l1 1v1c-1 1-1 2-2 4h0c0 4 0 8 1 11-1 1-2 2-4 2h0c-2-3-3-5-3-9z" class="J"></path><path d="M462 153c5-2 9-3 14-1 4 3 7 7 10 12-1 0-3-3-4-4s-3-2-4-3c-2-2-4-3-6-3-5-1-9 1-13 4-1 1-3 2-4 3h0v-1c2-3 5-5 7-7z" class="F"></path><path d="M444 160c1-5 5-10 9-14 7-5 16-10 24-11h9c0 1 0 0-1 1l-1 1h-8-1c-9 2-19 8-24 15-4 6-5 10-5 16h-1v-1-2c0-1 0-2 1-4 0-1 0-3 1-4l1-1c0-1 1-2 2-3 0-1 0-1 1-2-1 0-1 1-2 2l-3 4c-1 1-1 2-2 3h0z" class="M"></path><defs><linearGradient id="Am" x1="440.287" y1="223.549" x2="443.783" y2="240.474" xlink:href="#B"><stop offset="0" stop-color="#020101"></stop><stop offset="1" stop-color="#272626"></stop></linearGradient></defs><path fill="url(#Am)" d="M482 210h0c-9 10-20 19-33 21-3 1-9 1-11 2 3 2 6 3 9 4h0l1 1c1 0 2 0 3 1 3 2 9 0 12-1l2 1c-4 2-9 3-13 2-9 0-20-7-26-14h1 1 1c5 3 13 2 19 1 13-1 25-9 34-18z"></path><path d="M502 176l1-3v1c1 4 1 8 1 12 0 5-1 10-3 15-6 17-19 31-36 38h0l-2-1c-3 1-9 3-12 1-1-1-2-1-3-1 2 0 6-1 8 0 11-3 21-9 29-18 9-9 17-22 16-35h1v-9z" class="V"></path><path d="M502 176l1-3v1c1 4 1 8 1 12h-1v-3h0c0 1 0 3-1 4v-2-9z" class="M"></path><path d="M456 238c5 0 9-2 13-3h1c-2 2-4 3-7 3-3 1-9 3-12 1-1-1-2-1-3-1 2 0 6-1 8 0z" class="X"></path><path d="M459 158c4-3 8-5 13-4 2 0 4 1 6 3 1 1 3 2 4 3s3 4 4 4c1 1 3 4 3 6v5c1 1 1 2 0 3h0c0-1-1-3-1-4-4-7-8-9-15-11-3 0-7 1-9 4v1c0 3-1 6-1 9l-1 2c-1 2-2 4-3 5-2 1-4 1-6 0-1 0 0 0-1-1s-1-2-2-3v-2h0c2 0 3-1 4-2-1-3-1-7-1-11h0c1-2 1-3 2-4h0c1-1 3-2 4-3z" class="Y"></path><path d="M456 171c0-1 0-3 1-5l9-3c3-1 5-1 7 0-3 0-7 1-9 4v1c-4 0-6 1-8 3z" class="P"></path><path d="M456 171c2-2 4-3 8-3 0 3-1 6-1 9l-1 2h-1c-3-3-4-4-5-8z" class="C"></path><path d="M455 161h0c1-1 3-2 4-3 1 1 1 1 0 2h1c-2 1-4 3-5 5h0c0 3 0 7 1 10h0c1 2 3 3 4 4h1 1c-1 2-2 4-3 5-2 1-4 1-6 0-1 0 0 0-1-1s-1-2-2-3v-2h0c2 0 3-1 4-2-1-3-1-7-1-11h0c1-2 1-3 2-4z" class="P"></path><path d="M450 180l3-2 1 1c-1 1-1 3-2 4-1-1-1-2-2-3z" class="K"></path><path d="M455 161h0c1-1 3-2 4-3 1 1 1 1 0 2h1c-2 1-4 3-5 5h0c-1 2-2 4-1 7v1l1 1c0 1 1 2 1 3v1l-2-2c-1-3-1-7-1-11h0c1-2 1-3 2-4z" class="X"></path><defs><linearGradient id="An" x1="462.236" y1="182.048" x2="465.811" y2="247.666" xlink:href="#B"><stop offset="0" stop-color="#c5c3c3"></stop><stop offset="1" stop-color="#f0f0ef"></stop></linearGradient></defs><path fill="url(#An)" d="M509 158l4 13 1 4-1 19-1 3h-1c-1 4-3 9-4 13v2c-2 2-4 6-5 9-8 14-15 32-29 42h-3l2-3v-1l4-4c-2 0-4 2-6 1-2 1-3 1-4 1-1 1-3 1-4 1-3 1-5 2-8 2-1 1-2 1-3 1-1 1-2 2-3 2-2 1-5 2-7 3-3 3-4 8-6 12l-1-1-6-8-11-9c0-1-1-1-2-2l-2-6h0c0-1-1-2-1-3 0-3 0-6 2-8 2 0 3 0 5 1 0 0 0-1 1-2l3 2 1-1-4-3 1-2-4-4 2-13c2 2 4 6 6 8h1c6 7 17 14 26 14 4 1 9 0 13-2h0c17-7 30-21 36-38 2-5 3-10 3-15 0-4 0-8-1-12 1-2 0-6-1-7-1-2-2-4-2-6h2c1 0 1 0 3 1h0c1 0 1 0 2 1l1-1v-2l1-2z"></path><path d="M496 225v1l-2 3c-1 1-2 3-2 4h0c-2 2-6 7-8 7-3 0-3 1-5 2l-1-1s1-1 2-1h1v-2h0c6-4 10-8 15-13z" class="I"></path><path d="M481 238v2h-1c-1 0-2 1-2 1l1 1c2-1 2-2 5-2-9 7-21 12-33 11h0v-1h-6c13 0 24-5 36-12z" class="F"></path><path d="M502 161c1 0 1 0 3 1h0c1 0 1 0 2 1h1v1c1 2 2 5 2 8l-1 7c0-3 0-5-1-7h-1c1 3 2 6 1 9v1c0 2-1 3-2 4h0l-1 1c0 2 0 3-1 5h0v4c-1 2-1 3-2 4l-1 1c2-5 3-10 3-15 0-4 0-8-1-12 1-2 0-6-1-7-1-2-2-4-2-6h2z" class="J"></path><path d="M505 187l1-21c1 2 1 4 1 6 1 3 2 6 1 9v1c0 2-1 3-2 4h0l-1 1z" class="B"></path><path d="M492 233c0 2-2 4-3 5-3 7-9 13-14 19l-3 3v-1l4-4c-2 0-4 2-6 1-2 1-3 1-4 1-1 1-3 1-4 1-3 1-5 2-8 2-1 1-2 1-3 1-2-3-5-6-8-9l1-1h7 0c12 1 24-4 33-11 2 0 6-5 8-7z" class="G"></path><defs><linearGradient id="Ao" x1="509.635" y1="209.808" x2="483.649" y2="206.544" xlink:href="#B"><stop offset="0" stop-color="#151616"></stop><stop offset="1" stop-color="#4a4846"></stop></linearGradient></defs><path fill="url(#Ao)" d="M509 158l4 13 1 4-1 19-1 3h-1c-1 4-3 9-4 13v2c-2 2-4 6-5 9-8 14-15 32-29 42h-3l2-3 3-3c5-6 11-12 14-19 1-1 3-3 3-5h0c0-1 1-3 2-4l2-3v-1c8-12 11-25 13-39 1-4 2-9 1-14 0-3-1-6-2-8v-1h-1l1-1v-2l1-2z"></path><path d="M421 236c2 2 4 5 6 7 4 3 12 6 18 7h6v1h-7l-1 1c3 3 6 6 8 9-1 1-2 2-3 2-2 1-5 2-7 3-3 3-4 8-6 12l-1-1-6-8-11-9c0-1-1-1-2-2l-2-6h0c0-1-1-2-1-3 0-3 0-6 2-8 2 0 3 0 5 1 0 0 0-1 1-2l3 2 1-1-4-3 1-2z" class="L"></path><path d="M448 263h0c-2 1-5 2-7 3-3 3-4 8-6 12l-1-1c1-1 1 0 1-1 0-2 0-5 1-7 3-5 7-5 11-6h1z" class="Q"></path><path d="M440 252c0-1 0-1-1-2h1 0 3v1 1c3 3 6 6 8 9-1 1-2 2-3 2h0c0-3-7-8-8-11z" class="R"></path><path d="M421 236c2 2 4 5 6 7 4 3 12 6 18 7h6v1h-7l-1 1v-1-1h-3 0-1c1 1 1 1 1 2-3-4-9-4-14-6-2-1-5-3-7-4 0 0 0-1 1-2l3 2 1-1-4-3 1-2z" class="D"></path><path d="M517 440c0 2 1 3 1 5 0 1 0 2 1 3v1s0 2 1 3l1 1c0 1 1 1 2 2h1c0-1 0 0-1-1 1-1 1-1 2-1v1c0 1 0 2 1 3l5 6v1 1s1 1 1 2h-1c2 2 3 4 5 6s4 3 5 5c1 1 2 1 3 2v1 1l12 5-1 1h0c2 1 4 1 5 2v1 1 1h0-3c-1 3-2 4-4 5l-1 1c1 0 2 0 2 1h-10l-2 1-1-1h1l-1-1h0c-3 0-4 1-7 0-1 1-1 3-2 4h0l-1 2h0c-1 7 0 11 2 18-1 1-1 2-1 4v6l-1-4h0l-1 5-1 48c-5 1-11 2-16 1h-5l-5-1c0 1 0 1-1 2-6-2-12-5-17-8-5-2-8-4-11-8v-2c0-1-2-2-3-3-2-1-3-3-3-4l-6-13c-2-2-4-6-5-9v-10h-1c1-2 0-4 1-6h0c0-1 1-2 1-2l1-2h-1l-2 5-1 2h0v1c-1 1-1 1-1 2l-1-2v-3c0-3 1-6 2-10h-1 0v2 1h-1c0-3 2-5 1-7 0-4 3-7 5-9 4-5 10-8 16-11l1-1 19-10c11-7 17-18 20-30h-1v-2c0-1 1-2 1-4l2-1z" class="R"></path><defs><linearGradient id="Ap" x1="542.048" y1="467.956" x2="517.952" y2="463.044" xlink:href="#B"><stop offset="0" stop-color="#9d9c9d"></stop><stop offset="1" stop-color="#cccbca"></stop></linearGradient></defs><path fill="url(#Ap)" d="M517 440c0 2 1 3 1 5 0 1 0 2 1 3v1s0 2 1 3l1 1c0 1 1 1 2 2h1c0-1 0 0-1-1 1-1 1-1 2-1v1c0 1 0 2 1 3l5 6v1 1s1 1 1 2h-1c2 2 3 4 5 6s4 3 5 5c1 1 2 1 3 2v1l-5-3c-9-4-15-12-19-21-2-5-3-10-4-16l-1 6h-1v-2c0-1 1-2 1-4l2-1z"></path><path d="M526 457l5 6v1 1s1 1 1 2h-1c-2-1-3-3-4-5-2-1-1-3-1-5z" class="O"></path><path d="M475 489h1c8-3 17-7 24-12v1c-1 6-1 17 1 23-5-6-11-11-19-11-2-1-4-1-6 0h0l-1-1zm29-14c2-4 5-7 7-11 2-3 4-7 5-11 1 5 3 10 6 14 2 3 6 7 7 11v2c-3-2-5-3-8-4h-1c-3-1-8-1-11 0 0 1-1 1-1 2-1 0-2 1-3 2h-1 0l-2 1c1-2 1-4 2-6z" class="K"></path><path d="M504 475c4-2 11-2 15-1h3 0c3 1 5 3 7 4v2c-3-2-5-3-8-4h-1c-3-1-8-1-11 0 0 1-1 1-1 2-1 0-2 1-3 2h-1 0l-2 1c1-2 1-4 2-6z" class="O"></path><defs><linearGradient id="Aq" x1="536.322" y1="478.249" x2="541.928" y2="493.138" xlink:href="#B"><stop offset="0" stop-color="#838282"></stop><stop offset="1" stop-color="#a4a3a4"></stop></linearGradient></defs><path fill="url(#Aq)" d="M530 503l1-2c2-7 2-15 1-23 5 3 11 6 16 8 2 1 5 1 7 2h0c2 1 4 1 5 2v1 1 1h0-3c-1 3-2 4-4 5l-1 1c1 0 2 0 2 1h-10l-2 1-1-1h1l-1-1h0c-3 0-4 1-7 0-1 1-1 3-2 4h0l-1 2-1-2z"></path><path d="M555 488c2 1 4 1 5 2v1 1 1c-7-2-13-2-20 1-3 2-4 3-6 5-1 1-1 3-2 4 0-3 1-5 3-7 5-5 13-7 20-8z" class="D"></path><path d="M540 494c7-3 13-3 20-1h0-3c-1 3-2 4-4 5l-1 1c1 0 2 0 2 1h-10l-2 1-1-1h1l-1-1h0c-3 0-4 1-7 0 2-2 3-3 6-5z" class="U"></path><path d="M540 494c1 2 0 3 3 4h1l1 1c-1 0-2 0-3 1h0l-1-1h0c-3 0-4 1-7 0 2-2 3-3 6-5z" class="E"></path><path d="M500 529l1-2c0 2-1 4 0 6l1 48 1 1c0 1 0 1-1 2-6-2-12-5-17-8v-1h-1c-1-2 1-7 2-9 4-11 4-20 3-31 3-1 5-1 7-3 1-1 2-3 4-3z" class="C"></path><path d="M500 529l1-2c0 2-1 4 0 6l1 48 1 1c0 1 0 1-1 2-6-2-12-5-17-8v-1c5 2 10 5 15 6v-52z" class="D"></path><path d="M475 488v1l1 1h0l-1 2c8-1 14 0 20 5 4 4 6 9 6 14 1 6-1 13-5 18-2 2-6 4-9 4-2 0-4-1-6-2-1-1-2-3-2-5-1-2-2-4-2-6h3 0 2l1 1c2 1 2 1 4 0 3 0 4-2 5-5 2-2 3-5 2-8s-5-5-7-6c-5-2-9 0-13 1l-1-1c-6 3-10 7-14 11l-1 1-1 1c-1-1-1-2-1-4l-1 1h-1 0v2 1h-1c0-3 2-5 1-7 0-4 3-7 5-9 4-5 10-8 16-11z" class="G"></path><path d="M480 520h0l2 3c-1 0-1 0-2-1l-1 1h0v3c-1-2-2-4-2-6h3z" class="J"></path><path d="M475 488v1l1 1h0l-1 2c-8 2-14 7-18 15 0 1-1 3-1 4l-1 1h-1 0v2 1h-1c0-3 2-5 1-7 0-4 3-7 5-9 4-5 10-8 16-11z" class="N"></path><defs><linearGradient id="Ar" x1="487.723" y1="506.362" x2="491.765" y2="522.029" xlink:href="#B"><stop offset="0" stop-color="#0b0a08"></stop><stop offset="1" stop-color="#2b2b2b"></stop></linearGradient></defs><path fill="url(#Ar)" d="M473 502c4-2 9-4 14-2 2 1 4 2 7 4h1v-2c1 0 2 0 2 2 1 5 1 11-2 15-2 3-5 5-8 5-2 1-3 0-5-1l-2-3h2l1 1c2 1 2 1 4 0 3 0 4-2 5-5 2-2 3-5 2-8s-5-5-7-6c-5-2-9 0-13 1l-1-1z"></path><defs><linearGradient id="As" x1="468.905" y1="505.852" x2="491.387" y2="565.724" xlink:href="#B"><stop offset="0" stop-color="#a5a4a3"></stop><stop offset="1" stop-color="#d8d8d7"></stop></linearGradient></defs><path fill="url(#As)" d="M474 503c4-1 8-3 13-1 2 1 6 3 7 6s0 6-2 8c-1 3-2 5-5 5-2 1-2 1-4 0l-1-1h-2 0-3l-2-5-1 1c0 3 3 14 5 17 1 1 2 2 4 2 1 0 3 1 3 1s1 1 1 2v12c0 8-3 15-6 22l-7-6c0-1-2-2-3-3-2-1-3-3-3-4l-6-13c-2-2-4-6-5-9v-10h-1c1-2 0-4 1-6h0c0-1 1-2 1-2l1-2h-1l-2 5-1 2h0v1c-1 1-1 1-1 2l-1-2v-3c0-3 1-6 2-10l1-1c0 2 0 3 1 4l1-1 1-1c4-4 8-8 14-11l1 1z"></path><path d="M481 550l2 1c1 1 1 1 1 3-1 2-1 2-3 2-1 0-1 0-2-1v-1c1-2 1-3 2-4z" class="D"></path><path d="M474 516c-1 0-1 0-1 1v2h-1c0-2-1-2 0-4 1-1 3-2 4-3h2c3 2 4 5 4 7v1h-2 0-3l-2-5-1 1z" class="M"></path><path d="M475 515c1-1 0-1 2-1 1 1 3 4 3 6h-3l-2-5z" class="C"></path><path d="M459 513c4-4 8-8 14-11l1 1c-7 4-14 10-16 19l-1 5h-1c1-2 0-4 1-6h0c0-1 1-2 1-2l1-2h-1l-2 5-1 2h0v1c-1 1-1 1-1 2l-1-2v-3c0-3 1-6 2-10l1-1c0 2 0 3 1 4l1-1 1-1z" class="D"></path><path d="M455 512l1-1c0 2 0 3 1 4l1-1 1-1-6 12v-3c0-3 1-6 2-10z" class="O"></path><path d="M508 478c0-1 1-1 1-2 3-1 8-1 11 0h1c3 1 5 2 8 4 2 8 0 15 0 23h1l1 2h0c-1 7 0 11 2 18-1 1-1 2-1 4v6l-1-4h0l-1 5-1 48c-5 1-11 2-16 1h-5l-5-1-1-1-1-48c0-8 2-15 3-23 0-3-2-7-2-11v-18l2-1h0 1c1-1 2-2 3-2z" class="W"></path><path d="M508 478c0-1 1-1 1-2 3-1 8-1 11 0-1 0-1 0-2 1-1 2-1 6-1 8l-1 21h0v-28 1h1v-1-1-1c-1 1-2 1-4 1-1-1-2 0-3 1l-1 1-1-1z" class="G"></path><path d="M502 481l2-1h0 1c1-1 2-2 3-2l1 1-1 104-5-1-1-1-1-48c0-8 2-15 3-23 0-3-2-7-2-11v-18zm19-5c3 1 5 2 8 4 2 8 0 15 0 23h1l1 2h0c-1 7 0 11 2 18-1 1-1 2-1 4v6l-1-4h0l-1 5-1 48c-5 1-11 2-16 1h6c1-1 0-1 1-1h1l1-1 1-10v-34c0-7 1-15 0-22v-5c-1-1-1-2-1-4-1-9-1-20-1-30z" class="E"></path><path d="M529 503h1l1 2h0c-1 7 0 11 2 18-1 1-1 2-1 4v6l-1-4h0l-1 5c0-10-1-21-1-31z" class="F"></path><path d="M492 354c0-4 1-7 3-11-2 7-3 14 1 21 1 3 4 5 6 8l2 1c1 3 1 6 0 9-1 1 0 1-1 2 1 1 0 4 1 5 0-5 1-8 1-12l1-1h0 1l1 8c1 10-3 46 3 52 3 2 4 2 7 2h1 1c1 0 2-2 3-2v-2c1-4 1-10 1-14l1-21v-19h0c2 3 4 6 6 8 0 8-2 49 0 54h1v1h-1c-2-1-1-2-1-4-1-4-1-8-1-12l1-1c-1-2-1-4-1-6h1c0-6 1-11 0-16-1-4-1-9 0-13 0-2 0-2-1-4l-2-2s-1-1-1-2v1c1 3 0 5 0 8-1 8-1 17-1 25-1 4 0 8-1 12 0 3 1 7-1 9-1 1-4 1-6 2h0 0l-2 1c0 2-1 3-1 4v2h1c-3 12-9 23-20 30l-19 10c-1 0-2-1-3-2l-2-1s5-3 6-3v-1c-1 0-2 0-2 1h-1c-1 0-1 1-2 1l-1 1c-1 0-4-2-6-2-2-1-3-1-5-2v-1h-3l-6-4c0-1-1-2-1-2-1-1-2-2-3-2l-4-3c-1 0-1-1-2-1h0c-1-1-2-1-3-2-1 0-2-1-4-1h0c-2-1-1-1-2-1h-3c-2-1-6-1-8-1h0l-4-9h1 1c1 1 4 1 6 2h3c1 1 2 0 3 0l2-1 16 10h0c-6-5-12-10-19-13-5-3-11-5-17-6l-10-20c1 0 5 3 6 4 3 2 7 5 11 7h1c1 1 1 1 2 1s0 0 1-1c-1-1-2-2-3-4 0-1-1-2-1-2h-1l-2-2c-1-1-1-2-2-3s-2-1-2-3c-1 0-2-1-2-2 0 0-1-1-1-2-1 0-2-1-2-2 0 0-1-1-1-2h-1c0-1-1-1-1-2-1-1-1-2-2-2 0-1-1-2-1-2v-1l-2-3h0l-3-7-2-4h0v-2-1c-1-1-1-1 0-2h0l1 4 1 2h0 0l3 3c0 2 1 2 2 3s1 2 2 3l-1 1 2 3h0c1 0 1-1 0-1 1-1 1-1 2-1 1 1 1 3 2 4h1 1c0 1 1 2 2 3v-1c0-1 0-2-1-2l-1-1c1-1 1-1 2-1 4 3 8 4 14 4h0c-1 0-2-1-2-1h2l-3-1h5c7-2 11-5 16-10l2 1 2-5h2v-1c2-4 3-4 6-5 1-1 2-1 3-1h1c2-1 5-1 8-1l1 1v1c-1 1-2 1-3 2v1c1-1 0-1 1-1 2-1 2-2 4-2 3 0 1-1 3-2 1-2 0-3 0-4 0-2 0-2 1-2l1-1 3-2v-2l1-1h1c1 0 3-2 4-3 1 0 2-1 2-1l-1-5c-1-4-3-7-5-10 1-1 2-1 3-1l-3-5h1l2 3h0c0-2 0-3 1-4l1-1-1 2v5h1c1 0 1-1 2-2l1 1 2 1z" class="G"></path><path d="M471 448h0c2-1 2-1 3-2l1-1c2 0 3 1 3 3 1 1 1 2 1 3-1 2-3 4-4 5l1-3c1-2 0-3 0-4-2-1-3 0-4 0l-1-1z" class="D"></path><path d="M421 431c10 10 20 18 33 24 4 2 9 4 13 4s5-1 7-3v2c1 0 8-1 10-1l1 1h-1c-2 0-3 0-5 1-5 1-10 3-16 1-11-4-21-9-30-17-3-2-6-5-9-8-1-1-2-2-3-4z" class="V"></path><defs><linearGradient id="At" x1="487.994" y1="488.148" x2="493.271" y2="456.226" xlink:href="#B"><stop offset="0" stop-color="#828282"></stop><stop offset="1" stop-color="#b4b1b1"></stop></linearGradient></defs><path fill="url(#At)" d="M514 447h1c-3 12-9 23-20 30l-19 10c-1 0-2-1-3-2l-2-1s5-3 6-3v-1c12-6 20-18 29-27l2-2v-1l1 1c-1 3-3 4-4 7l1 3c0 1 0 1 1 2 1-1 1-1 1-2l2-3 1-1v-1-1c0-1 1-2 1-2v-1c1-2 1-4 2-5z"></path><path d="M492 470h0l5-4c0 2 0 2-1 4-2 1-3 2-4 3h-1v-1c1 0 1-1 1-2z" class="C"></path><path d="M506 453c0 3-7 11-9 13l-5 4h0c-5 5-11 12-18 15h-1 0l-2-1s5-3 6-3v-1c12-6 20-18 29-27z" class="F"></path><path d="M492 354c0-4 1-7 3-11-2 7-3 14 1 21 1 3 4 5 6 8l2 1c1 3 1 6 0 9-1 1 0 1-1 2h0c-1 6 0 12 0 17v26c0 5 0 11-2 15v1-1c0-2 0-3-1-4s-2-2-3-1c-2 0-7 4-8 6v1c0 1 1 2 1 2-1 0-2-1-2-2 0-2 2-5 1-6-2 0-4 0-5 2-1 0-1 2-2 4h-1c-1-2-1-2-1-4 2-2 4-3 6-4h7c-2-5-4-11-7-16-2-4-5-9-7-13l1-2v-2c1-2 4-5 5-7v-1c-3 2-5 5-8 6 0-1 0-1-1-2l-1-2v-2l1-2-1-7c1-2 0-3 0-4 0-2 0-2 1-2l1-1 3-2v-2l1-1h1c1 0 3-2 4-3 1 0 2-1 2-1l-1-5c-1-4-3-7-5-10 1-1 2-1 3-1l-3-5h1l2 3h0c0-2 0-3 1-4l1-1-1 2v5h1c1 0 1-1 2-2l1 1 2 1z" class="D"></path><path d="M482 349h1l2 3h0c0-2 0-3 1-4l1-1-1 2v5h1c1 0 1-1 2-2l1 1 2 1v5h-2c3 3 4 7 6 10 2 2 5 5 6 7 1 1 1 2 0 3 0 3-3 5-5 6-1 0-3 0-4-1-1 0-1-2-1-2-1 0-1-2-1-3v-8 1 2l-2 1-1-5-1-5c-1-4-3-7-5-10 1-1 2-1 3-1l-3-5z" class="H"></path><path d="M485 354l3 6 3 8v2c2 2 5 3 7 5-1 0-2 0-3-1s-2-1-2-2l-2-1v1 2l-2 1-1-5-1-5c-1-4-3-7-5-10 1-1 2-1 3-1z" class="D"></path><path d="M488 370l1 5 2-1v-2-1 8c0 1 0 3 1 3 0 0 0 2 1 2 1 1 3 1 4 1-2 0-3 0-4 1l-2-1-3 6c-1 2-2 4-3 5v-1c-3 2-5 5-8 6 0-1 0-1-1-2l-1-2v-2l1-2-1-7c1-2 0-3 0-4 0-2 0-2 1-2l1-1 3-2v-2l1-1h1c1 0 3-2 4-3 1 0 2-1 2-1z" class="L"></path><path d="M480 377h0c0 1 0 0 1 1 1 2 1 4 2 6 0 2 0 4 1 5l-3 2h-1c-2-2 0-7-1-10l-2-2 3-2z" class="C"></path><path d="M476 380l1-1 2 2c1 3-1 8 1 10-2 2-3 3-4 5l-1 1v-2l1-2-1-7c1-2 0-3 0-4 0-2 0-2 1-2z" class="T"></path><path d="M476 380h0c1 1 1 2 2 3 0 2 0 4-1 5v2h0c0 1 0 2-1 3l-1-7c1-2 0-3 0-4 0-2 0-2 1-2z" class="P"></path><defs><linearGradient id="Au" x1="492.354" y1="380.689" x2="484.545" y2="384.61" xlink:href="#B"><stop offset="0" stop-color="#141616"></stop><stop offset="1" stop-color="#2f2b2b"></stop></linearGradient></defs><path fill="url(#Au)" d="M489 375l2-1v-2-1 8c0 1 0 3 1 3 0 0 0 2 1 2 1 1 3 1 4 1-2 0-3 0-4 1l-2-1-3 6h-1s-1-1-2-1-1 1-2 1h-2l3-2s1 0 2-1c3-3 3-9 3-13z"></path><path d="M481 391h2c1 0 1-1 2-1s2 1 2 1h1c-1 2-2 4-3 5v-1c-3 2-5 5-8 6 0-1 0-1-1-2l-1-2 1-1c1-2 2-3 4-5h1z" class="I"></path><path d="M476 399c2-1 3-3 4-4 2-2 4-2 6-3h1l-2 3c-3 2-5 5-8 6 0-1 0-1-1-2z" class="S"></path><path d="M488 391l3-6 2 1h0c2 1 5 0 8 0 1 11 0 23 0 35 0 3 0 12-1 14h-4c-2-6-5-12-8-18-2-4-5-8-8-12v-2c1-2 4-5 5-7 1-1 2-3 3-5z" class="H"></path><path d="M487 400c1 0 2 0 4 1v4c-1 1-1 2-3 2-1 0-2 0-4-1v-4c1-1 1-1 3-2z" class="F"></path><defs><linearGradient id="Av" x1="412.415" y1="407.664" x2="470.793" y2="415.17" xlink:href="#B"><stop offset="0" stop-color="#c4c3c3"></stop><stop offset="1" stop-color="#f0f0ef"></stop></linearGradient></defs><path fill="url(#Av)" d="M472 388c3 0 1-1 3-2l1 7-1 2v2l1 2c1 1 1 1 1 2 3-1 5-4 8-6v1c-1 2-4 5-5 7v2l-1 2-1-2h-1c-5 3-10 5-16 4l-3-1-1 1c-2 4-3 9-3 14 1 6 3 11 5 17 1 2 2 5 4 7 3 2 5 1 8 1l1 1c1 0 2-1 4 0 0 1 1 2 0 4l-1 3h-1c-2 2-3 3-7 3s-9-2-13-4c-13-6-23-14-33-24 0-1-1-2-1-2h-1l-2-2c-1-1-1-2-2-3s-2-1-2-3c-1 0-2-1-2-2 0 0-1-1-1-2-1 0-2-1-2-2 0 0-1-1-1-2h-1c0-1-1-1-1-2-1-1-1-2-2-2 0-1-1-2-1-2v-1l-2-3h0l-3-7-2-4h0v-2-1c-1-1-1-1 0-2h0l1 4 1 2h0 0l3 3c0 2 1 2 2 3s1 2 2 3l-1 1 2 3h0c1 0 1-1 0-1 1-1 1-1 2-1 1 1 1 3 2 4h1 1c0 1 1 2 2 3v-1c0-1 0-2-1-2l-1-1c1-1 1-1 2-1 4 3 8 4 14 4h0c-1 0-2-1-2-1h2l-3-1h5c7-2 11-5 16-10l2 1 2-5h2v-1c2-4 3-4 6-5 1-1 2-1 3-1h1c2-1 5-1 8-1l1 1v1c-1 1-2 1-3 2v1c1-1 0-1 1-1 2-1 2-2 4-2z"></path><path d="M445 398l2 1c-4 5-9 10-16 11h-4 0c-1 0-2-1-2-1h2l-3-1h5c7-2 11-5 16-10z" class="F"></path><path d="M441 432v-3l1 1h0c0 1 0 2 1 3v1c0 1 0 1 1 2v-2-2c2 5 3 8 7 11 1 0 2 1 3 2v1c-9-5-19-10-26-17h0c2 1 5 3 7 5 2 1 5 3 8 4l-2-6zm15-24l1 1c-2 4-3 9-3 14v-1c-1-1-1 0-1-1-1 1-1 4-1 6 1 1 1 2 1 3v1c0 1 0 2 1 4v2h1v2 1c1 1 1 2 2 3s1 2 2 3c-1 0-2 0-3-1-2-3-3-8-4-12-2-9-1-17 4-25z" class="D"></path><path d="M441 432c-1-7-1-16 4-22 2-2 5-3 7-5-6 7-9 12-9 21 0 2 0 4 1 6v2 2c-1-1-1-1-1-2v-1c-1-1-1-2-1-3h0l-1-1v3z" class="N"></path><path d="M456 445c1 1 2 1 3 1-1-1-1-2-2-3s-1-2-2-3v-1-2h-1v-2c-1-2-1-3-1-4v-1c0-1 0-2-1-3 0-2 0-5 1-6 0 1 0 0 1 1v1c1 6 3 11 5 17 1 2 2 5 4 7 3 2 5 1 8 1l1 1c-6 2-13 0-18-3v-1h2z" class="M"></path><path d="M472 388c3 0 1-1 3-2l1 7-1 2v2l1 2c1 1 1 1 1 2 3-1 5-4 8-6v1c-1 2-4 5-5 7v2l-1 2-1-2h-1c-5 3-10 5-16 4l-3-1-1 1-1-1 1-1-2-3v-1c2 2 3 3 5 3 3 1 7 1 10-1 3-1 4-5 5-8-3-1-7-2-9-4l1-2h0c1-1 0-1 1-1 2-1 2-2 4-2z" class="N"></path><path d="M467 391c1-1 0-1 1-1 2-1 2-2 4-2 2 2 2 2 2 4l-7-1h0z" class="J"></path><path d="M472 388c3 0 1-1 3-2l1 7-1 2c-1-1-1-1-1-3s0-2-2-4z" class="Q"></path><defs><linearGradient id="Aw" x1="446.148" y1="428.247" x2="439.253" y2="449.925" xlink:href="#B"><stop offset="0" stop-color="#cecccd"></stop><stop offset="1" stop-color="#f6f6f6"></stop></linearGradient></defs><path fill="url(#Aw)" d="M421 431c0-1-1-2-1-2h-1l-2-2c-1-1-1-2-2-3s-2-1-2-3c-1 0-2-1-2-2 0 0-1-1-1-2-1 0-2-1-2-2 0 0-1-1-1-2h-1c0-1-1-1-1-2-1-1-1-2-2-2 0-1-1-2-1-2v-1l-2-3h0l-3-7-2-4h0v-2-1c-1-1-1-1 0-2h0l1 4 1 2h0 0l3 3c0 2 1 2 2 3s1 2 2 3l-1 1 2 3h0c1 0 1-1 0-1 1-1 1-1 2-1 1 1 1 3 2 4h1v1c1 1 1 1 1 3 1 1 2 2 3 4 4 4 7 9 12 13h2c7 7 17 12 26 17 5 3 12 5 18 3 1 0 2-1 4 0 0 1 1 2 0 4l-1 3h-1c-2 2-3 3-7 3s-9-2-13-4c-13-6-23-14-33-24z"></path><path d="M520 188c-2-10-2-20 2-29s12-17 21-20 20 0 28 4c7 5 14 12 16 20 1 4 0 8-2 12l-3 3c-2 2-3 3-5 4v-1h-2c-1-3 1-10 2-13-2-1-4-1-6-1h-3v1 1c-1-3-3-3-5-4-5-1-9-1-14 3h0c-3 4-4 7-7 8-3 12-2 21 5 31 8 13 21 21 36 24l13 2c-3 2-7 4-11 5l1 1-2 2h0v1h1c13-4 24-15 31-27l1-2c0 4 0 7-1 10-1 7-7 14-11 19-3 2-6 4-10 5l1 1c8-1 14-4 21-7 1 1 1 2 1 3v1c0 1 0 2-1 3-1 2-1 3 0 5l1 3c-1 1-1 1-1 2 0 2-2 4-3 5l-8 9c-1 2-2 3-3 4-4 3-7 7-10 10 0 1 0 1-1 2l1 2-1 1c-4 1-6 1-9 3h-1v1h-2-1v2l1 2 1 2c1 1 1 2 2 2l3 2h0-1-2c-2-1-3-1-4-1v1l-1 3h-1v-3h-1c-2 1-2 7-3 9l-3 9c-1 2-3 5-3 7l-1 1c-1-3-2-4-4-6 0-1-1-1-1-1-2-1-4-2-6-2-6-2-12-4-17-10h-1l1 2-1 2c-3-8-4-15-5-22l-2-1v-5c-1-1-1-2-1-3v-8c1-3 1-6 2-9l-1-8v-4-1-14c0-5 0-9-2-14-1 1-3 0-4 1h0c-1 0-1-3-2-4v-2l-1-7c0-3-2-7-2-10l2-1-2-6-1-12 2 2z" class="U"></path><path d="M579 157c1 2 3 3 3 5 1 2 1 4 1 6l-1 1-1-4c0-3-1-5-2-8z" class="E"></path><path d="M577 181c-1-5 3-11 4-16l1 4v1l-2 7h-1v2l2-1h1c-2 2-3 3-5 4v-1z" class="C"></path><path d="M542 176c1-5 3-10 6-14 0 1 1 1 2 1l-2 4v1h1c-3 4-4 7-7 8z" class="V"></path><path d="M537 228c5 4 10 8 15 11l-1 1 1 1h-2l-1-1h-1c-3-1-6-4-8-6h-1c-1-2-2-4-2-6z" class="S"></path><path d="M577 157l-1-3 3 3c1 3 2 5 2 8-1 5-5 11-4 16h-2c-1-3 1-10 2-13l1-2c1-3-1-4-1-7l-3-1 2-2 1 1z" class="I"></path><path d="M576 156l1 1 1 2h-1l-3-1 2-2z" class="V"></path><defs><linearGradient id="Ax" x1="561.417" y1="151.953" x2="563.602" y2="159.61" xlink:href="#B"><stop offset="0" stop-color="#0e0d0e"></stop><stop offset="1" stop-color="#2c2b2c"></stop></linearGradient></defs><path fill="url(#Ax)" d="M548 162c2-4 7-9 12-10 6-2 11 1 16 4l-2 2c-4-3-8-4-13-3s-9 4-11 8c-1 0-2 0-2-1z"></path><path d="M551 225c3 0 6 3 9 5 4 3 14 7 20 6v-1c2 0 3 0 4 1h2 3 0c1-1 2-1 2-2h1v-1h-1-4c-2-1-3-1-4-1v-1l13 2c-3 2-7 4-11 5h-1c-13 0-24-5-33-13z" class="E"></path><defs><linearGradient id="Ay" x1="572.687" y1="242.917" x2="564.305" y2="251.592" xlink:href="#B"><stop offset="0" stop-color="#1f1c1d"></stop><stop offset="1" stop-color="#353535"></stop></linearGradient></defs><path fill="url(#Ay)" d="M548 240h1l1 1h2l-1-1 1-1c9 6 20 10 31 10h-1v1h0c-4 1-8 1-13 0h0c-8-1-15-5-21-10z"></path><path d="M550 163c2-4 6-7 11-8s9 0 13 3l3 1c0 3 2 4 1 7l-1 2c-2-1-4-1-6-1h-3v1 1c-1-3-3-3-5-4-5-1-9-1-14 3h0-1v-1l2-4z" class="S"></path><path d="M563 165c3 0 2 0 4 1h3v-1h-1c2-1 6 1 9-1v2l-1 2c-2-1-4-1-6-1h-3v1 1c-1-3-3-3-5-4z" class="X"></path><defs><linearGradient id="Az" x1="528.372" y1="223.648" x2="536.696" y2="220.051" xlink:href="#B"><stop offset="0" stop-color="#0c0c0b"></stop><stop offset="1" stop-color="#434242"></stop></linearGradient></defs><path fill="url(#Az)" d="M518 186l2 2c2 14 8 28 17 40 0 2 1 4 2 6h1c3 7 7 12 12 18l-3 1c-9-10-14-22-22-33l2 7h-1c-1 1-3 0-4 1h0c-1 0-1-3-2-4v-2l-1-7c0-3-2-7-2-10l2-1-2-6-1-12z"></path><path d="M521 204l5 16c0 2 2 5 2 7-1 1-3 0-4 1h0c-1 0-1-3-2-4v-2l-1-7c0-3-2-7-2-10l2-1z" class="O"></path><defs><linearGradient id="BA" x1="531.056" y1="204.365" x2="582.763" y2="202.608" xlink:href="#B"><stop offset="0" stop-color="#272625"></stop><stop offset="1" stop-color="#5b5a5b"></stop></linearGradient></defs><path fill="url(#BA)" d="M584 242h-1c-9 2-21-4-28-9-12-9-21-21-24-36-2-13-2-28 6-40-4 11-5 21-4 33 1 15 6 25 18 35 9 8 20 13 33 13h1l1 1-2 2h0v1z"></path><path d="M540 234c2 2 5 5 8 6 6 5 13 9 21 10h0c5 1 9 1 13 0h0 7l-9 10-9-3c-1 1-1 1-2 1 0 1 0 1-1 1h-1c-1 0-2-1-3 0-1 0-2 1-3 1 2 2 2 2 3 4h-1-1c-2 0-3-2-4-3v-1l-1 1c-2-1-6-6-8-8l3-1c-5-6-9-11-12-18z" class="G"></path><path d="M552 252c3 4 8 8 11 12h-1c-2 0-3-2-4-3v-1l-1 1c-2-1-6-6-8-8l3-1z" class="S"></path><path d="M596 248c8-1 14-4 21-7 1 1 1 2 1 3v1c0 1 0 2-1 3-1 2-1 3 0 5l1 3c-1 1-1 1-1 2 0 2-2 4-3 5l-8 9c-1 2-2 3-3 4-4 3-7 7-10 10 0 1 0 1-1 2l1 2-1 1c-4 1-6 1-9 3h-1v1h-2-1v2l1 2c-1-1-2-1-3-2 0-4-1-8-3-12v-1l2-2c-1-2-2-3-2-6-1-2-3-4-4-6-1-1-2-1-3-1l-3-5c-1-2-1-2-3-4 1 0 2-1 3-1 1-1 2 0 3 0h1c1 0 1 0 1-1 1 0 1 0 2-1l9 3 9-10h-7v-1h1c4-1 8-1 12-2l1 1z" class="E"></path><path d="M617 258c0 2-2 4-3 5l-8 9c-1 2-2 3-3 4-4 3-7 7-10 10 0 1 0 1-1 2v-2c2-2 3-6 4-9 0 0 1 0 1-1 2 0 4-3 6-4l14-14z" class="N"></path><path d="M583 249c4-1 8-1 12-2l1 1c-3 1-4 4-6 7-3 2-5 5-7 7 4 0 10 1 13 6 2 2 2 4 2 6l-1 2c0 1-1 1-1 1v-5c-2-8-10-7-16-11v-1l9-10h-7v-1h1z" class="R"></path><defs><linearGradient id="BB" x1="593.967" y1="280.286" x2="558.179" y2="268.761" xlink:href="#B"><stop offset="0" stop-color="#cccccb"></stop><stop offset="1" stop-color="#f4f3f3"></stop></linearGradient></defs><path fill="url(#BB)" d="M567 259h1c1 0 1 0 1-1 1 0 1 0 2-1l9 3v1c6 4 14 3 16 11v5c-1 3-2 7-4 9v2l1 2-1 1c-4 1-6 1-9 3h-1v1h-2-1v2l1 2c-1-1-2-1-3-2 0-4-1-8-3-12v-1l2-2c-1-2-2-3-2-6-1-2-3-4-4-6-1-1-2-1-3-1l-3-5c-1-2-1-2-3-4 1 0 2-1 3-1 1-1 2 0 3 0z"></path><path d="M574 276c4 5 6 10 9 15 2-1 3-1 5-2v1 1c1 0 2 0 3-1h0v-4h1v2l1 2-1 1c-4 1-6 1-9 3h-1v1h-2-1v2l1 2c-1-1-2-1-3-2 0-4-1-8-3-12v-1l2-2c-1-2-2-3-2-6z" class="L"></path><path d="M576 282c0 3 1 5 2 8 0 2 0 3 2 5h-1v2l1 2c-1-1-2-1-3-2 0-4-1-8-3-12v-1l2-2z" class="C"></path><defs><linearGradient id="BC" x1="584.738" y1="284.097" x2="582.61" y2="258.21" xlink:href="#B"><stop offset="0" stop-color="#a9a8a8"></stop><stop offset="1" stop-color="#d0cfcf"></stop></linearGradient></defs><path fill="url(#BC)" d="M567 259h1c1 0 1 0 1-1 1 0 1 0 2-1l9 3v1c6 4 14 3 16 11v5c-1 3-2 7-4 9h-1c-1-4-4-9-7-13-1-2-3-5-6-7-3-3-7-5-11-7z"></path><defs><linearGradient id="BD" x1="563.085" y1="278.056" x2="540.756" y2="310.811" xlink:href="#B"><stop offset="0" stop-color="#adacab"></stop><stop offset="1" stop-color="#d4d4d3"></stop></linearGradient></defs><path fill="url(#BD)" d="M529 227l-2-7c8 11 13 23 22 33 2 2 6 7 8 8l1-1v1c1 1 2 3 4 3h1 1l3 5c1 0 2 0 3 1 1 2 3 4 4 6 0 3 1 4 2 6l-2 2v1c2 4 3 8 3 12 1 1 2 1 3 2l1 2c1 1 1 2 2 2l3 2h0-1-2c-2-1-3-1-4-1v1l-1 3h-1v-3h-1c-2 1-2 7-3 9l-3 9c-1 2-3 5-3 7l-1 1c-1-3-2-4-4-6 0-1-1-1-1-1-2-1-4-2-6-2-6-2-12-4-17-10h-1l1 2-1 2c-3-8-4-15-5-22l-2-1v-5c-1-1-1-2-1-3v-8c1-3 1-6 2-9l-1-8v-4-1-14c0-5 0-9-2-14h1z"></path><path d="M534 277h1c-2 9-4 22 1 29 1 3 3 4 4 6h-2-1l1 2-1 2c-3-8-4-15-5-22-1-6 0-11 2-17z" class="D"></path><path d="M571 285h1c1 2 1 3 2 6 0 1 0 1 1 2v-3-1l-1-2v-1-1c2 4 3 8 3 12 1 1 2 1 3 2l1 2c1 1 1 2 2 2l3 2h0-1-2c-2-1-3-1-4-1v1l-1 3h-1v-3h-1c-2 1-2 7-3 9l-3 9c-1 2-3 5-3 7l-1 1c-1-3-2-4-4-6 0-1-1-1-1-1-2-1-4-2-6-2-6-2-12-4-17-10h2c6 4 11 6 18 8 3 1 6 1 9 2v1l5-18-3-2c0-3 2-4 4-6 1-2 0-3-1-5-1-1-5 1-6 2 2-2 4-3 6-5 0-1-1-3-1-4z" class="N"></path><path d="M579 304h-2l-7-3c2-1 4-1 5-3 1 0 2-1 2-1 1 1 2 1 3 2l1 2c1 1 1 2 2 2l3 2h0-1-2c-2-1-3-1-4-1z" class="E"></path><path d="M529 227l-2-7c8 11 13 23 22 33 2 2 6 7 8 8l1-1v1c1 1 2 3 4 3h1 1l3 5c1 0 2 0 3 1 1 2 3 4 4 6 0 3 1 4 2 6l-2 2v1 1 1l1 2v1 3c-1-1-1-1-1-2-1-3-1-4-2-6h-1c0 1 1 3 1 4l-4-4c-3 1-7-1-10-3-9-4-14-12-17-22l-6 17h-1c-2 6-3 11-2 17l-2-1v-5c-1-1-1-2-1-3v-8c1-3 1-6 2-9l-1-8v-4-1-14c0-5 0-9-2-14h1z" class="G"></path><path d="M534 240c1 1 3 5 4 7h-1l-1 2-3-3h-2l3-6z" class="O"></path><path d="M533 246l2-2c1 1 2 2 2 3l-1 2-3-3z" class="J"></path><path d="M537 247h1l2 6 1 3h0c0-1 0-1-1-2l-2 2-2 1c-2-2-2-2-2-4 0-1 1-2 1-2l1-2 1-2z" class="V"></path><path d="M537 247h1l2 6c-1 1-2 1-3 0s-1-1-1-2h-1l1-2 1-2z" class="F"></path><path d="M531 246h2l3 3-1 2s-1 1-1 2h-1c-1 2-2 4-3 7v-4-1c1-3 1-6 1-9z" class="B"></path><path d="M530 256l1-1v-5h1c0 1 1 2 1 3-1 2-2 4-3 7v-4z" class="C"></path><path d="M534 253c0 2 0 2 2 4l2-1c-3 4-5 7-7 12h0l-1-8c1-3 2-5 3-7h1z" class="M"></path><path d="M529 227c1 4 3 9 5 13l-3 6c0 3 0 6-1 9v-14c0-5 0-9-2-14h1z" class="F"></path><path d="M541 256c3 11 8 19 18 25-3-5-5-10-5-16 2 0 2 1 4 2h0-2c-1 1 0 4 0 6 1 2 2 6 4 8 2 0 4 1 6 2v-2c0-2 1-3 1-4 1 3 2 6 1 8-3 1-7-1-10-3-9-4-14-12-17-22l-6 17h-1l4-12c1-3 3-6 3-9h0z" class="N"></path><path d="M557 261l1-1v1c1 1 2 3 4 3h1 1l3 5c1 0 2 0 3 1 1 2 3 4 4 6 0 3 1 4 2 6l-2 2v1 1 1l1 2v1 3c-1-1-1-1-1-2-1-3-1-4-2-6h-1c0 1 1 3 1 4l-4-4c1-2 0-5-1-8 0 1-1 2-1 4 0-3 0-8-3-10v-1h0c0-4-3-7-6-9z" class="Q"></path><path d="M567 277v-2c2 2 2 4 3 7l1 3c0 1 1 3 1 4l-4-4c1-2 0-5-1-8z" class="C"></path><path d="M567 269c1 0 2 0 3 1 1 2 3 4 4 6 0 3 1 4 2 6l-2 2c-2-6-4-10-7-15zm-29-13l2-2c1 1 1 1 1 2 0 3-2 6-3 9l-4 12c-2 6-3 11-2 17l-2-1v-5c-1-1-1-2-1-3v-8c1-3 1-6 2-9h0c2-5 4-8 7-12z" class="B"></path><path d="M513 171c2-2 1-5 1-7l1-1c0 2 0 5 1 7 1-1 0-3 1-4l1 2c0 6-1 12 0 18l1 12 2 6-2 1c0 3 2 7 2 10l1 7v2c1 1 1 4 2 4h0c1-1 3 0 4-1 2 5 2 9 2 14v14 1 4l1 8c-1 3-1 6-2 9v8c0 1 0 2 1 3-2 17-1 35-1 52 0 10 1 20-1 29h0c-1 3-3 3-4 5 0 1 1 5 1 6h0v19l-1 21c0 4 0 10-1 14v2c-1 0-2 2-3 2h-1-1c-3 0-4 0-7-2-6-6-2-42-3-52l-1-8h-1 0l-1 1c0 4-1 7-1 12-1-1 0-4-1-5 1-1 0-1 1-2 1-3 1-6 0-9l-2-1c-2-3-5-5-6-8-4-7-3-14-1-21-2 4-3 7-3 11l-2-1-1-1c-1 1-1 2-2 2h-1v-5l1-2-1 1c-1 1-1 2-1 4h0l-2-3h-1l3 5c-1 0-2 0-3 1-3-5-8-11-10-16-2-4-1-9-1-13l-1-1c-1 0-2 0-3 1v1c0 1 0 3-1 4 1 2 1 4 1 6 2 3 2 6 3 8v2h0 0l-2-2c-1-1-3-2-3-3l-1-1c0 1 0 1-1 1l-1-2v-2h0c-1-1-3-1-4-1s-2-2-3-3l-10-7-2-1c-1-1-2-1-2-2l-2-1-6-5-3-4-1-1c-6-9-8-18-6-28 1-4 2-7 6-10-1-1-2-3-3-4 0-1 1-1 2-2l6 8 1 1c2-4 3-9 6-12 2-1 5-2 7-3 1 0 2-1 3-2 1 0 2 0 3-1 3 0 5-1 8-2 1 0 3 0 4-1 1 0 2 0 4-1 2 1 4-1 6-1l-4 4v1l-2 3h3c14-10 21-28 29-42 1-3 3-7 5-9v-2c1-4 3-9 4-13h1l1-3 1-19-1-4z" class="W"></path><path d="M505 292v7c1 4 2 7 0 11-1 3 0 8-2 11v-13-14l1-1v2l1-3z" class="E"></path><path d="M507 258c2 4 1 8 2 12 0-3-1-11 1-13v-2l1-1v-3-1c0-2 1-4 1-6v-1c1-2 1-2 1-3 1-2 0-3 1-4 0 4 0 8-1 12 0 4 0 7-2 11 0 1 0 1-1 1v8c0 5 0 10-1 16v1l-1 1c-1-2 0-5-1-6v-3l1-6c-2 4 0 9-1 13-1-1 0-2-1-4s0-6 0-9h1c-1-4-1-9 0-13zm13-42l1-1 1 7v2c0 6 1 12 1 18v8c1 2 1 3 2 4v1 5c1 2 0 3 1 5 1 6 1 12 0 18v1c0-4 0-9-1-13v-6c-1 1-1 2-1 3v21 3h-1l-1-41c1-2 0-11 0-14h0v-4c-1-1 0-3 0-4v-4l-1-1v-3-2l-1-1v-2z" class="G"></path><path d="M524 268v-2c0-2-1-6 0-7v1l1 1v4c-1 1-1 2-1 3z" class="U"></path><path d="M514 215c1 7 0 14 0 21-1 1 0 2-1 4 0 1 0 1-1 3v1c0 2-1 4-1 6v1 3l-1 1v2c-2 2-1 10-1 13-1-4 0-8-2-12h0c1-2 0-3 1-4 0-3 1-6 1-9v-1c1-2 1-4 1-6l1-4v-1c0-1 1-1 1-2l1-11v-1c1-1 1-2 1-4z" class="H"></path><path d="M522 224c1 1 1 4 2 4h0c1-1 3 0 4-1 2 5 2 9 2 14v14 1 4l1 8c-1 3-1 6-2 9v-4c1-1 0-4 0-5l1-2c-1-1-1-3-1-5-1-3 0-7 0-10h-1-2l-1-1h-2v-8c0-6-1-12-1-18z" class="L"></path><path d="M493 249l1 2 2-1c1 1 0 3 1 4v-1l2 2c-1 1-1 1-2 1l1 1v1c1 1 2 2 2 4 0 1 0 0 1 1h1v1c0 1 0 1 2 3v3c0-2 0-4 1-7 0 5 0 10-1 14 0 2 1 4 1 6 1 2-1 3 0 5 0 2 0 3-1 4h1l-1 3v-2l-1 1c-1-4-1-8-3-12 0-2-2-4-2-7h0c-2-4-6-10-7-15 1-2-1-4 0-7 0-1 1-2 2-4z" class="H"></path><path d="M502 263v1c0 1 0 1 2 3v3l-1 11c-1-1-1-2-1-3-1-1 0 0 0-1v-14z" class="E"></path><path d="M498 275l1-1c0 1 0 2 1 3 0 1 0 2 1 3h0l1 3c1 2 2 6 2 9h0 1l-1 3v-2l-1 1c-1-4-1-8-3-12 0-2-2-4-2-7z" class="J"></path><path d="M499 267v-4c0-2-1-3-1-5 1 1 2 2 2 4 0 1 0 0 1 1h1v14l-3-9v-1z" class="O"></path><path d="M493 249l1 2 2-1c1 1 0 3 1 4v-1l2 2c-1 1-1 1-2 1l1 1v1c0 2 1 3 1 5v4 1c-1 1-1 2-1 3 1 2 2 3 2 5l1 3v1h0c-1-1-1-2-1-3-1-1-1-2-1-3l-1 1h0c-2-4-6-10-7-15 1-2-1-4 0-7 0-1 1-2 2-4z" class="L"></path><path d="M493 249l1 2 2-1c1 1 0 3 1 4v-1l2 2c-1 1-1 1-2 1l1 1v1c0 2 1 3 1 5v4c-2-5-4-10-8-14 0-1 1-2 2-4z" class="S"></path><path d="M494 251l2-1c1 1 0 3 1 4v1l-4-3 1-1z" class="F"></path><path d="M500 282c2 4 2 8 3 12v14 13l1 52-2-1v-55c-1 1-1 2-2 3v1c-3 3-5 5-8 7-5 5-8 10-10 16h0c-1 1-2 1-3 1 3-10 8-15 16-22 0-2 1-5 0-7l2-7 2-6c1-5 2-11 1-16l-1-3 1-2z" class="I"></path><path d="M500 287h0v1l1 1c2 7 1 17 0 23-1-3-1-5-1-8v-1h-1c1-5 2-11 1-16z" class="B"></path><path d="M499 303h1v1c0 3 0 5 1 8-1 4-2 8-6 11 0-2 1-5 0-7l2-7 2-6z" class="J"></path><path d="M493 312l1-1c0-2 1-2 2-3l1 1-2 7c1 2 0 5 0 7-8 7-13 12-16 22 1 0 2 0 3-1 0 2-1 3 0 5l3 5c-1 0-2 0-3 1-3-5-8-11-10-16-2-4-1-9-1-13h2l-1-2v-1c2-1 6-2 8-2 3-1 6-3 8-4 0-1 0-1 1-2h-1-1l4-3 2 1v-1z" class="N"></path><path d="M479 345c1 0 2 0 3-1 0 2-1 3 0 5l-1-1c-2 0-2-1-3-2l1-1z" class="F"></path><path d="M491 312l2 1v-1c1 1 0 2-1 3-3 5-7 9-13 10-2 1-4 0-6 1l-1-2v-1c2-1 6-2 8-2 3-1 6-3 8-4 0-1 0-1 1-2h-1-1l4-3z" class="U"></path><path d="M491 312l2 1c-2 1-3 2-5 4 0-1 0-1 1-2h-1-1l4-3z" class="N"></path><path d="M495 316c1 2 0 5 0 7-8 7-13 12-16 22l-1 1c-4-6-5-9-5-16l5-1c7-2 13-6 16-12l1-1z" class="E"></path><path d="M500 320c1-1 1-2 2-3v55c-2-3-5-5-6-8-4-7-3-14-1-21-2 4-3 7-3 11l-2-1-1-1c-1 1-1 2-2 2h-1v-5l1-2-1 1c-1 1-1 2-1 4h0l-2-3h-1c-1-2 0-3 0-5h0c2-6 5-11 10-16 3-2 5-4 8-7v-1z" class="J"></path><path d="M500 320c1 3 0 4 0 6 0 3 1 6-1 9l-3 5h-1c-1 0-1 1-2 1l-1 1c-2 1-4 3-5 5h0l-1 1c-1 1-1 2-1 4h0l-2-3h-1c-1-2 0-3 0-5h0c2-6 5-11 10-16 3-2 5-4 8-7v-1z" class="G"></path><defs><linearGradient id="BE" x1="494.564" y1="212.945" x2="522.469" y2="237.584" xlink:href="#B"><stop offset="0" stop-color="#a09e9f"></stop><stop offset="1" stop-color="#cbcac9"></stop></linearGradient></defs><path fill="url(#BE)" d="M513 171c2-2 1-5 1-7l1-1c0 2 0 5 1 7 1-1 0-3 1-4l1 2c0 6-1 12 0 18l1 12 2 6-2 1c0 3 2 7 2 10l-1 1c0-1 0-2-1-3v-1-1-1l-1-2 1-1c-1-1-1-1-1-2v-1c-1-1-1-2-1-3v-1c-1-1 0-2 0-3v-1c0-1 0-1-1-1h-1v7 1c-1 1 0 5-1 7v1 4c0 2 0 3-1 4v1l-1 11c0 1-1 1-1 2v1l-1 4c0 2 0 4-1 6v1c0 3-1 6-1 9l-2-2c0 2 0 5-1 7 1 2 0 3 0 4-1 3-1 5-1 7v-3c-2-2-2-2-2-3v-1h-1c-1-1-1 0-1-1 0-2-1-3-2-4v-1l-1-1c1 0 1 0 2-1l-2-2v1c-1-1 0-3-1-4l-2 1-1-2 1-3 1-1 8-19 4-14v-2c1-4 3-9 4-13h1l1-3 1-19-1-4z"></path><path d="M495 245l8-19 1 1c0 2-2 4-2 6v7 12c-1-2 0-4 0-6-1-2-1-4-3-5h0l-4 4z" class="I"></path><path d="M512 197c-1 10-4 20-8 30l-1-1 4-14v-2c1-4 3-9 4-13h1z" class="N"></path><path d="M511 222c2-4 2-8 3-11v4c0 2 0 3-1 4v1l-1 11c0 1-1 1-1 2v1l-1 4c0 2 0 4-1 6v1c0 3-1 6-1 9l-2-2c0 2 0 5-1 7-1-2 0-8 0-10 0-1 0-1 1-2v-1 2c1 0 1 0 1-1v-3c0-2 0-4 1-6h0s1 1 1 2v-3l2-15z" class="E"></path><path d="M495 245l4-4h0c2 1 2 3 3 5 0 2-1 4 0 6 2 3 1 6 1 9 0 2 1 4 1 6-2-2-2-2-2-3v-1h-1c-1-1-1 0-1-1 0-2-1-3-2-4v-1l-1-1c1 0 1 0 2-1l-2-2v1c-1-1 0-3-1-4l-2 1-1-2 1-3 1-1z" class="B"></path><path d="M494 246v1l3 3c4 4 4 8 4 13-1-1-1 0-1-1 0-2-1-3-2-4v-1l-1-1c1 0 1 0 2-1l-2-2v1c-1-1 0-3-1-4l-2 1-1-2 1-3z" class="M"></path><path d="M513 171c2-2 1-5 1-7l1-1c0 2 0 5 1 7 1-1 0-3 1-4l1 2c0 6-1 12 0 18l1 12 2 6-2 1c0 3 2 7 2 10l-1 1c0-1 0-2-1-3v-1-1-1l-1-2 1-1c-1-1-1-1-1-2v-1c-1-1-1-2-1-3v-1c-1-1 0-2 0-3v-1c0-1 0-1-1-1h-1v7 1c-1 1 0 5-1 7v1c-1 3-1 7-3 11 1-5 1-9 1-14 1-4 2-8 2-12v-2-2l-1 2h0l1-19-1-4z" class="L"></path><path d="M519 198l2 6-2 1v-7zm-5-23c1 1 1 2 1 3v1l1 1v1c0 1 0 3-1 5h0v3l-1 7v-2-2l-1 2h0l1-19z" class="B"></path><defs><linearGradient id="BF" x1="469.455" y1="273.37" x2="487.624" y2="313.207" xlink:href="#B"><stop offset="0" stop-color="#afaead"></stop><stop offset="1" stop-color="#dad9d8"></stop></linearGradient></defs><path fill="url(#BF)" d="M473 263c14-10 21-28 29-42 1-3 3-7 5-9l-4 14-8 19-1 1-1 3c-1 2-2 3-2 4-1 3 1 5 0 7 1 5 5 11 7 15h0c0 3 2 5 2 7l-1 2 1 3c1 5 0 11-1 16l-2 6-1-1c-1 1-2 1-2 3l-1 1v1l-2-1-4 3h1 1c-1 1-1 1-1 2-2 1-5 3-8 4-2 0-6 1-8 2v1l1 2h-2l-1-1c-1 0-2 0-3 1v1c0 1 0 3-1 4 1 2 1 4 1 6 2 3 2 6 3 8v2h0 0l-2-2c-1-1-3-2-3-3l-1-1c0 1 0 1-1 1l-1-2v-2h0c-1-1-3-1-4-1s-2-2-3-3l-10-7-2-1c-1-1-2-1-2-2l-2-1-6-5-3-4-1-1c-6-9-8-18-6-28 1-4 2-7 6-10-1-1-2-3-3-4 0-1 1-1 2-2l6 8 1 1c2-4 3-9 6-12 2-1 5-2 7-3 1 0 2-1 3-2 1 0 2 0 3-1 3 0 5-1 8-2 1 0 3 0 4-1 1 0 2 0 4-1 2 1 4-1 6-1l-4 4v1l-2 3h3z"></path><path d="M461 277l1-1c1-1 1 0 1-1h0v3l-4 13 7 1c-2 1-7 1-8 4 0 2 8 5 6 8-1 0-1-1-2-1 0-1-6-5-7-6 0-1 1-5 1-7l5-13zm15-10c0-1 0-2 1-3l1 1 1 1-6 15c8-3 12-10 15-18 0-1 1-4 2-5l1 2c1 5 5 11 7 15h0c0 3 2 5 2 7l-1 2 1 3c1 5 0 11-1 16l-2 6-1-1c-1 1-2 1-2 3l-1 1v1l-2-1c2-3 5-6 6-9 1-4 2-7 2-11 0-12-4-20-9-31-1 5-3 11-6 15-5 5-12 9-19 10l-1-1c1-2 1-4 1-6l1 2v2h2c2-1 4-1 4-3 2-2 5-10 4-13z" class="D"></path><path d="M470 263h3l-2 2c2 1 3 1 5 2h0c1 3-2 11-4 13 0 2-2 2-4 3h-2v-2l-1-2s-1-1-1-2l-1 1v-3h0c0 1 0 0-1 1l-1 1c1-3 3-6 4-8l5-6z" class="E"></path><path d="M464 277c1-2 2-4 3-7 1 3 0 4-1 7v4l-1-2s-1-1-1-2z" class="J"></path><path d="M470 263h3l-2 2c-1 2-3 3-4 5-1 3-2 5-3 7l-1 1v-3h0c0 1 0 0-1 1l-1 1c1-3 3-6 4-8l5-6z" class="Q"></path><defs><linearGradient id="BG" x1="454.822" y1="313.687" x2="487.054" y2="320.933" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#252424"></stop></linearGradient></defs><path fill="url(#BG)" d="M453 305c3-2 6-2 9-2 1 0 1 1 2 1l-4 2c1 5 3 11 5 16l1-1c4-1 8-1 12-2 3-1 6-3 9-4h1 1c-1 1-1 1-1 2-2 1-5 3-8 4-2 0-6 1-8 2v1l1 2h-2l-1-1c-1 0-2 0-3 1v1c0 1 0 3-1 4 1 2 1 4 1 6l-1 1-4-5-3-9c-1-2-1-4-2-5l-2-9c0-1 0-3-1-4 0 0 0-1-1-1h0z"></path><path d="M457 319v-1-4c-1-2-2-6-2-8l1-1c1 3 2 5 3 7 1 4 3 8 4 12 1 3 2 5 3 7s1 4 1 6l-1 1-4-5-3-9c-1-2-1-4-2-5z" class="B"></path><path d="M473 263c14-10 21-28 29-42 1-3 3-7 5-9l-4 14-8 19-1 1-1 3c-1 2-2 3-2 4-1 3 1 5 0 7l-1-2c-1 1-2 4-2 5-3 8-7 15-15 18l6-15-1-1-1-1c-1 1-1 2-1 3h0c-2-1-3-1-5-2l2-2z" class="G"></path><path d="M470 256c2 1 4-1 6-1l-4 4v1l-2 3-5 6c-1 2-3 5-4 8l-5 13c0 2-1 6-1 7 1 1 7 5 7 6-3 0-6 0-9 2h0c1 0 1 1 1 1 1 1 1 3 1 4l2 9c1 1 1 3 2 5l3 9 4 5 1-1c2 3 2 6 3 8v2h0 0l-2-2c-1-1-3-2-3-3l-1-1c0 1 0 1-1 1l-1-2v-2h0c-1-1-3-1-4-1s-2-2-3-3l-10-7-2-1c-1-1-2-1-2-2l-2-1-6-5-3-4-1-1c-6-9-8-18-6-28 1-4 2-7 6-10-1-1-2-3-3-4 0-1 1-1 2-2l6 8 1 1c2-4 3-9 6-12 2-1 5-2 7-3 1 0 2-1 3-2 1 0 2 0 3-1 3 0 5-1 8-2 1 0 3 0 4-1 1 0 2 0 4-1z" class="B"></path><path d="M455 297c1 1 7 5 7 6-3 0-6 0-9 2l-1-1h-5v-1c2-1 3-1 4-2 2-1 2-2 3-3l1-1z" class="J"></path><path d="M430 314l-1-1c-6-9-8-18-6-28 1-4 2-7 6-10v1l2 2h-2c0 1-1 2-1 3l1 1v1c-1 3-1 6-1 8 1 1 2 2 3 4 0 1 1 2 2 3v1h-1c3 2 4 3 7 4l5 1c-2 1-4 0-6 0-4-1-6-5-10-5-1 0-2-1-2-1 0 7 3 14 8 19l6 6 1 1-2-1-6-5-3-4z" class="M"></path><path d="M429 276l2 2h-2c0 1-1 2-1 3l1 1v1c-1 3-1 6-1 8 1 1 2 2 3 4 0 1 1 2 2 3v1h-1c-1 0-5-2-5-4l-1-1c-1-4 0-8 1-12 0-3 1-4 2-6z" class="T"></path><path d="M428 269l6 8 1 1c2 3 3 7 4 11l1 1v2l-1 1h0c1 1 3 1 4 1l1-1h2 1 2c1 1 1 1 2 1 1-1 2-5 3-7v1c1 1 1 2 1 3l1-1h0c0 2-1 6-1 7l-1 1c0-1 0-1-1-1h-3-1l-2 1c-3 1-8 0-11 0 0 2 0 2 2 3h0l1 2c-3-1-4-2-7-4h1v-1c-1-1-2-2-2-3-1-2-2-3-3-4 0-2 0-5 1-8v-1l-1-1c0-1 1-2 1-3h2l-2-2v-1c-1-1-2-3-3-4 0-1 1-1 2-2z" class="K"></path><path d="M454 288c1 1 1 2 1 3l1-1h0c0 2-1 6-1 7l-1 1c0-1 0-1-1-1h-3-1l-2 1c-3 1-8 0-11 0 0 2 0 2 2 3h0l1 2c-3-1-4-2-7-4h1v-1-1l1 1 1-1h3 5c2-1 4-1 6-2 1 1 3 1 5 1v-1c1-2 0-5 0-7z" class="C"></path><path d="M428 269l6 8 1 1c2 3 3 7 4 11-3-3-6-8-8-11l-2-2v-1c-1-1-2-3-3-4 0-1 1-1 2-2z" class="N"></path><defs><linearGradient id="BH" x1="441.485" y1="302.777" x2="454.897" y2="342.253" xlink:href="#B"><stop offset="0" stop-color="#c4c3c2"></stop><stop offset="1" stop-color="#ecebea"></stop></linearGradient></defs><path fill="url(#BH)" d="M453 305c1 0 1 1 1 1 1 1 1 3 1 4l2 9c1 1 1 3 2 5l3 9 4 5 1-1c2 3 2 6 3 8v2h0 0l-2-2c-1-1-3-2-3-3l-1-1c0 1 0 1-1 1l-1-2v-2h0c-1-1-3-1-4-1s-2-2-3-3l-10-7v-4h-1c-1-3-13-17-13-18l7 2c2 1 4 1 6 1 1 0 2 0 3-1l3-1c2 0 2 0 3-1z"></path><path d="M447 307l1 1h3v1c-3 0-5 1-7-1 1 0 2 0 3-1z" class="L"></path><path d="M462 333l4 5 1-1c2 3 2 6 3 8-3-1-5-3-6-6-1-2-1-3-2-6z" class="H"></path><defs><linearGradient id="BI" x1="444.511" y1="271.189" x2="472.738" y2="274.469" xlink:href="#B"><stop offset="0" stop-color="#d6d5d4"></stop><stop offset="1" stop-color="#fafafa"></stop></linearGradient></defs><path fill="url(#BI)" d="M470 256c2 1 4-1 6-1l-4 4v1l-2 3-5 6c-1 2-3 5-4 8l-5 13h0l-1 1c0-1 0-2-1-3v-1c-1 2-2 6-3 7-1 0-1 0-2-1h-2-1-2l-1 1c-1 0-3 0-4-1h0l1-1v-2l-1-1c-1-4-2-8-4-11 2-4 3-9 6-12 2-1 5-2 7-3 1 0 2-1 3-2 1 0 2 0 3-1 3 0 5-1 8-2 1 0 3 0 4-1 1 0 2 0 4-1z"></path><path d="M454 287c0-1 1-3 1-4 2-4 4-7 6-10 1-2 2-3 4-4-1 2-3 5-4 8l-5 13h0l-1 1c0-1 0-2-1-3v-1z" class="B"></path><defs><linearGradient id="BJ" x1="449.953" y1="286.393" x2="449.655" y2="258.616" xlink:href="#B"><stop offset="0" stop-color="#a3a3a3"></stop><stop offset="1" stop-color="#d6d4d4"></stop></linearGradient></defs><path fill="url(#BJ)" d="M451 261c1 0 2 0 3-1 3 0 5-1 8-2 1 0 3 0 4-1 1 0 2 0 4-1l-1 1c-3 2-6 4-8 6-3 2-6 6-9 9h0c0-2 5-6 6-9-7 7-16 16-16 27v1h3 1 0v2h-2l-1 1c-1 0-3 0-4-1h0l1-1v-2l-1-1c-1-4-2-8-4-11 2-4 3-9 6-12 2-1 5-2 7-3 1 0 2-1 3-2z"></path><defs><linearGradient id="BK" x1="670.002" y1="403.239" x2="515.052" y2="407.885" xlink:href="#B"><stop offset="0" stop-color="#b6b4b4"></stop><stop offset="1" stop-color="#f7f7f6"></stop></linearGradient></defs><path fill="url(#BK)" d="M530 288v5l2 1c1 7 2 14 5 22l1-2-1-2h1c5 6 11 8 17 10 2 0 4 1 6 2 0 0 1 0 1 1l-2 3v1 2h-1v1c0 2 0 4-1 6l2 1-6 9c-1 1-2 4-3 4-3 5-6 9-7 14v6c1 11 4 20 12 28v-1c1-1 1 0 2-1h1l1-2 3-1h0l2-1c1-1 1-2 1-4h0-2c-3 1-5 1-7 0-2-3-2-6-3-9 0-1 0-1 1-1h1c3 0 4 0 7-2 0-1 1-1 0-3l-1-1c-1-1 0-2 0-4 3-11 14-20 23-25 6-3 12-5 19-6h8c9 0 16 1 24 4v-12c5 0 13 6 17 9 11 10 17 26 18 40 0 7-1 15-5 20-2 4-5 6-8 8l-1 1 1 1v2l-1-1c-2-1-3 0-5 1 4 0 15 0 17 3h1l-1 1c-7-2-13-3-21-2-3 0-6 1-10 1h0c-2 3-5 5-7 7 1 1 3 0 4 1-7 2-12 5-17 9v1l1-1c5 0 9-3 13-3h2c0 1-1 1-2 2-1 0-2 1-2 1 0 1 1 2 2 2l1 1c2 1 6 1 7 2l-3 3h0c-2 1-3 2-4 3h0c-2 0-4 0-6 1h-1l2 2c-3 1-8 0-11 0l-1 1c-2 1-5 0-7 1-5 1-8 4-12 6l-15 10c-1 3-6 6-9 8h-4c-2-1-4 0-6 0-4-1-11-2-15-1l-3 1c-1-1-3-2-4-3l-9-10c-2-2-4-5-7-6-1 0-1 0-2 1 1 1 1 0 1 1h-1c-1-1-2-1-2-2l-1-1c-1-1-1-3-1-3v-1c-1-1-1-2-1-3 0-2-1-3-1-5h0 0c2-1 5-1 6-2 2-2 1-6 1-9 1-4 0-8 1-12 0-8 0-17 1-25 0-3 1-5 0-8v-1c0 1 1 2 1 2l2 2c1 2 1 2 1 4-1 4-1 9 0 13 1 5 0 10 0 16h-1c0 2 0 4 1 6l-1 1c0 4 0 8 1 12 0 2-1 3 1 4h1v-1h-1c-2-5 0-46 0-54-2-2-4-5-6-8 0-1-1-5-1-6 1-2 3-2 4-5h0c2-9 1-19 1-29 0-17-1-35 1-52z"></path><path d="M602 398l-2-3h-1c2-1 2-2 4-2l-1 5z" class="G"></path><path d="M603 393c0-1 1-2 2-4l1 2c-1 1-2 2-2 4l1 2c0 1 1 3 1 4l-1 1-1-1-1-1c0-1-1-1-1-2l1-5z" class="L"></path><path d="M563 395h0c-2 2-3 2-4 5 1 3 4 5 7 6l3 1h1c-3 1-4 1-7-1h0-2c-1 0-3-2-3-3l-2-3v-1c1-1 1 0 2-1h1l1-2 3-1z" class="V"></path><path d="M605 397l1-1c2-1 2-1 4-1h0c2 1 4 0 6 0-3 3-5 6-9 6h-1c0-1-1-3-1-4z" class="I"></path><path d="M605 397l1-1c2-1 2-1 4-1-2 2-3 3-3 6h-1c0-1-1-3-1-4z" class="E"></path><path d="M539 436c4-1 8-1 11 2 2 1 2 2 3 4-1 2-1 2-2 2-1-1-1-1-1-2s-1-3-2-4h-7c1 1 1 2 2 3s2 2 2 4v1h-1v-1c-1-2-3-4-5-6v-1l1-1-1-1z" class="D"></path><path d="M613 388c2-1 5 0 7 2 1 1 1 2 2 3h-1c0 2 0 2-2 4l-1-1-2 2h0c1-1 2-2 2-4v-1l-2 2c-2 0-4 1-6 0 2-2 4-3 5-5l-2-2z" class="V"></path><path d="M606 391l4-3c1-1 1-2 3-2v2l2 2c-1 2-3 3-5 5h0c-2 0-2 0-4 1l-1 1-1-2c0-2 1-3 2-4z" class="G"></path><path d="M582 464v-1l15-11c3-2 7-5 10-6h10l-1 1c-2 1-5 0-7 1-5 1-8 4-12 6l-15 10z" class="N"></path><path d="M588 432v-1c2-6 1-14-3-20-1-2-3-3-4-5h0c3 1 7 5 9 8 2 5 3 12 1 16v2l-1-1v1l-2 2v-2z" class="V"></path><path d="M616 398h0l2-2 1 1c-2 4-5 8-9 9-4 2-8 2-11 0h0 0v-1c-4-1-6-3-8-6h0c3 2 5 4 9 5 2 1 5 1 7 1l1-1c3-1 4-4 7-6h1z" class="B"></path><path d="M560 457v1h2l1 1h6l3-1h1c0-1 1-1 2-1l4-2c1-1 1-1 2-1 0 1-1 2-2 2s-1 0-1 1h-2l-1 1-3 1h0c-1 1-3 1-4 1l-2 1h-1c-1 0-2 1-3 0h-5-1c-1 1-3 1-5 1-1-1-1-1-1-3l-2-1c0-1 0-1 1-1h3 7 1z" class="H"></path><path d="M578 426c0 1 1 3 0 4v1 2l-1 1v1 1l-1 1c-1 2-2 5-3 7-1 1-1 1-1 2h1v1c1 0 1 0 2-1l3-1v1c-6 3-12 6-19 3h-2c-1 0-1 1-1 2 0 2 0 3 1 4h-1c-2-1-3-3-3-5 0-1 0-3 1-4 3-1 6 2 9 3 1 0 3 0 4-1 6-4 9-15 11-22z" class="N"></path><path d="M553 404l1 1h1l-8 13c-3 5-6 11-8 17v1l1 1-1 1c-2-1-3-1-6-1 4-5 6-11 9-16 4-6 8-11 11-17zm23 4c5 7 6 14 5 22l-3 12c-1 1-2 3-3 4s-1 1-2 1v-1h-1c0-1 0-1 1-2 1-2 2-5 3-7l1-1v-1-1l1-1v-2-1c1-1 0-3 0-4 0-6-1-10-2-16l-1-1 1-1z" class="F"></path><path d="M612 419l1 1c-10 10-21 19-35 26v-1l-3 1c1-1 2-3 3-4 6 0 6-8 10-10v2l2-2v-1l1 1c-1 2-2 4-2 6 8-5 16-12 23-19z" class="D"></path><path d="M588 432v2c-1 1-1 4-3 5-1 3-5 4-7 6l-3 1c1-1 2-3 3-4 6 0 6-8 10-10z" class="J"></path><path d="M619 397c2-2 2-2 2-4h1v5c-2 5-6 9-11 10-5 2-10 2-15 0-7-3-9-8-12-14l1-2c4 6 7 12 14 14h0c3 2 7 2 11 0 4-1 7-5 9-9z" class="M"></path><path d="M591 383c1-2 2-3 4-4h1l1 1-1 4h0c2 0 4-1 5-2 5-6 8-15 17-15 2 0 3 0 5 1 5 5 4 13 5 19 0 4-1 8-2 13-4 7-8 14-13 20l-1-1c6-7 14-16 14-26l1-4c0-5 0-14-4-18-1-1-2-2-4-2-8 0-13 13-18 17-1 1-3 1-5 0-1-1-2-2-2-3h-1c0 1-1 2-1 3-2 0-2 0-3-1-1-2-1-2-1-4l2 3h1v-1z" class="I"></path><path d="M584 394c-3-7-4-13-4-19 1-7 5-14 9-18 6-6 14-10 22-11l1 1h0 4c1 1 2 1 3 1s3 1 4 1c2 1 5 2 7 4l2 1c1 1 2 3 4 4h-1c-1-1-2-2-4-3 0 0-1-1-2-1v-1l-6-3-2-1h-2c-1 0-2 0-3-1h0c-1 1-5 0-5 1h5c-10 0-17 3-23 9s-11 14-10 23c0 4 1 8 2 11l-1 2z" class="N"></path><path d="M611 346c8 0 16 3 22 8 10 9 15 23 15 37 1 7 1 14-3 21h7c4 0 15 0 17 3h1l-1 1c-7-2-13-3-21-2-3 0-6 1-10 1 0-2 2-3 3-4 2-3 4-5 4-9 3-12-1-31-9-41-5-7-12-11-20-12h-5c0-1 4 0 5-1h0c1 1 2 1 3 1h2l2 1 6 3v1c1 0 2 1 2 1 2 1 3 2 4 3h1c-2-1-3-3-4-4l-2-1c-2-2-5-3-7-4-1 0-3-1-4-1s-2 0-3-1h-4 0l-1-1z" class="R"></path><defs><linearGradient id="BL" x1="586.758" y1="378.643" x2="595.131" y2="366.282" xlink:href="#B"><stop offset="0" stop-color="#302d30"></stop><stop offset="1" stop-color="#3f413f"></stop></linearGradient></defs><path fill="url(#BL)" d="M591 384v-1c-2-1-3-1-3-3 0-6 1-13 6-18s12-8 19-8c6 0 11 2 15 7 3 3 6 7 7 11 1 1 1 1 1 2 2 2 2 9 1 12-2 17-16 34-28 46-3 2-6 5-8 7l-1 1c2-3 5-5 7-8 12-11 24-25 27-42 0-5 1-10 0-15h-1v-2c-1-5-5-11-9-14-5-3-10-4-15-3h-1-2c-5 2-10 6-13 10-2 4-3 12-2 16v1 1z"></path><path d="M636 331c5 0 13 6 17 9 11 10 17 26 18 40 0 7-1 15-5 20 1-1 1-2 1-3l1-1 1-3v-1-2-1c1-1 1-1 1-2-2-1-3-3-4-5s-3-5-4-7l-5-5c-7-8-16-15-20-26h0c-3 0-5-1-7-2-1 0-3 0-4-1h-4c-1 0-1-1-1-1-3-1-7 1-11 0-2 0-4 0-6-1h8c9 0 16 1 24 4v-12z" class="O"></path><defs><linearGradient id="BM" x1="626.269" y1="415.821" x2="568.134" y2="401.792" xlink:href="#B"><stop offset="0" stop-color="#d4d3d3"></stop><stop offset="1" stop-color="#fcfbfb"></stop></linearGradient></defs><path fill="url(#BM)" d="M591 383v-1c-1-4 0-12 2-16 3-4 8-8 13-10h2 1c5-1 10 0 15 3 4 3 8 9 9 14v2h1c1 5 0 10 0 15-3 17-15 31-27 42-2 3-5 5-7 8-1 0-2 1-3 1 0 1-1 2-2 2l-1 1-3 3c-1 0-1 1-2 1s-1 1-2 2c-1 0-1 1-2 1h0c-1 1-1 1-2 1l-2 2c-1 0-1 0-2 1l-4 2c-1 0-2 0-2 1h-1l-3 1h-6l-1-1h-2v-1c-1-1-2-1-3-2s-1-2-1-4c0-1 0-2 1-2h2c7 3 13 0 19-3 14-7 25-16 35-26 5-6 9-13 13-20 1-5 2-9 2-13-1-6 0-14-5-19-2-1-3-1-5-1-9 0-12 9-17 15-1 1-3 2-5 2h0l1-4-1-1h-1c-2 1-3 2-4 4z"></path><path d="M628 387v-1c1-1 1-3 1-4-1-2-1-3 0-4h0l1 5v1c1 3 1 8 0 11-1 1 0 2-2 3h0l-2 2c1-5 2-9 2-13z" class="B"></path><path d="M530 288v5l2 1c1 7 2 14 5 22l1-2-1-2h1c5 6 11 8 17 10 2 0 4 1 6 2 0 0 1 0 1 1l-2 3v1 2h-1v1c0 2 0 4-1 6l2 1-6 9c-1 1-2 4-3 4-3 5-6 9-7 14v6c1 11 4 20 12 28l2 3c0 1 2 3 3 3h2 0c3 2 4 2 7 1s6-1 7-4v-3h1c0 2 1 2 0 3 0 2-2 3-3 4l1 1-1 1-2-1c-1 0-3 1-4 1-6 0-10-1-14-4h-1l-1-1c-3 6-7 11-11 17-3 5-5 11-9 16 0 1-1 1-1 1v4h-1c-2-5 0-46 0-54-2-2-4-5-6-8 0-1-1-5-1-6 1-2 3-2 4-5h0c2-9 1-19 1-29 0-17-1-35 1-52z" class="R"></path><path d="M530 319l1-5 2 7 2 3c-2 2-2 8-2 11 0 1 1 2 1 3l2 1v1 2c2 4 2 9 2 14 0 4-2 6-4 10l-1 1-4 4c1-9 1-18 1-27v-25z" class="H"></path><path d="M530 319l1-5 2 7c-2 3 0 14-1 19v-2-2-1c-1 1-1 2-1 3 0 2 0 4-1 6v-25z" class="C"></path><path d="M536 339c2 2 5 4 7 5h3c1 2 0 3 2 4h3l-2 3h1l-1 2v1-1s1 0 1-1h1c-3 5-6 9-7 14v6l-1-2h-1v4c0 4 1 7 1 11-2 0-7 2-9 1-3 0-5-5-5-8-1 0-1-1-1-2h0c0-3 4-6 6-8l-1-1 1-1c2-4 4-6 4-10 0-5 0-10-2-14v-2-1z" class="E"></path><path d="M540 352c1 6 0 9-3 14l-3 2-1-1 1-1c2-4 4-6 4-10h1l1-1v-3z" class="F"></path><path d="M536 339c2 2 5 4 7 5 1 3 2 6 2 9h-1v-1c0-2 0-3-1-5l-1-1c-1-2-3-3-5-4l3 10v3l-1 1h-1c0-5 0-10-2-14v-2-1z" class="D"></path><path d="M549 351h1l-1 2v1-1s1 0 1-1h1c-3 5-6 9-7 14v6l-1-2h-1v4c0-1 0-3-1-4-3 3-7 6-11 7h-1c4-2 10-6 12-9 3-6 4-12 8-17z" class="F"></path><path d="M532 438v-49c4 0 8-1 12-2 2 7 5 11 9 17-3 6-7 11-11 17-3 5-5 11-9 16 0 1-1 1-1 1z" class="E"></path><path d="M543 401h2c2 0 2 1 3 2 0 1 1 2 0 2 0 2-1 2-2 3-2 0-2 0-4-1-1-1-1-1-1-3 1-2 1-2 2-3z" class="I"></path><defs><linearGradient id="BN" x1="540.039" y1="298.318" x2="548.085" y2="339.158" xlink:href="#B"><stop offset="0" stop-color="#bababa"></stop><stop offset="1" stop-color="#ecebeb"></stop></linearGradient></defs><path fill="url(#BN)" d="M530 293l2 1c1 7 2 14 5 22l1-2-1-2h1c5 6 11 8 17 10 2 0 4 1 6 2 0 0 1 0 1 1l-2 3v1 2h-1v1c0 2 0 4-1 6l2 1-6 9c-1 1-2 4-3 4h-1c0 1-1 1-1 1v1-1l1-2h-1l2-3h-3c-2-1-1-2-2-4h-3c-2-1-5-3-7-5l-2-1c0-1-1-2-1-3 0-3 0-9 2-11l-2-3-2-7-1 5c0-3 0-6 1-8l-1-18z"></path><path d="M530 319c0-3 0-6 1-8 1 2 1 6 2 8 2 5 8 8 12 11l6 9 2-1c1-1 2 0 4 1h0l1-1 2 1-6 9c-1 1-2 4-3 4h-1c0 1-1 1-1 1v1-1l1-2h-1l2-3v-3c-2-10-9-15-16-21l-2-3-2-7-1 5z" class="M"></path><path d="M551 339l2-1c1-1 2 0 4 1h0l-4 6c0-2-1-4-2-6z" class="U"></path><path d="M538 314l-1-2h1c5 6 11 8 17 10 2 0 4 1 6 2 0 0 1 0 1 1l-2 3v1 2h-1l-9-3c-6-3-10-6-13-12l1-2z" class="F"></path><path d="M538 314l-1-2h1c5 6 11 8 17 10 2 0 4 1 6 2-1 0-1 1-1 1-2 1-4 1-5 1-6-1-12-5-15-9l-2-3z" class="G"></path><path d="M535 324c7 6 14 11 16 21v3h-3c-2-1-1-2-2-4h-3c-2-1-5-3-7-5l-2-1c0-1-1-2-1-3 0-3 0-9 2-11z" class="U"></path><path fill="#fafafa" d="M869 200h29c4 0 9-1 13 0v20l-1-1c-23-8-55-6-78 4-22 10-39 29-51 49-14 23-23 49-34 74l-56 130-166 403c-2-5-4-10-5-15l-14-35-34-83-95-223-40-92-40-92c-10-22-19-45-31-66-11-18-25-34-42-44-9-5-18-9-27-11-24-7-50-4-73 2-1-2 0-4 0-6l-1-14h290v22l-4-1c-11-5-25-2-35 2-14 6-26 19-31 34-7 18-6 36-1 55 7 26 19 51 31 76l28 61 96 198 33 67 20 42 113-266 30-75c11-32 23-65 27-99 2-20 2-40-6-59-6-15-18-30-34-36-14-6-36-6-51 0-1-6 0-14-1-21h241z"></path><path d="M290 213c-1-2-2-4-3-5h10c-3 2-5 0-7 2v2 1z" class="L"></path><path d="M330 371h0c2 1 2 1 2 2l-1 2c-1 0-1 0-3-1v-2l2-1z" class="M"></path><path d="M313 330c1-2 2-4 4-5s5-1 8 0l-1 1c-2 0-5 0-7 2l-2 2h-2z" class="V"></path><path d="M336 383c2 0 2 0 3 1s2 2 2 3c-1 1-1 2-2 3h-1c-2 0-3-1-4-2v-2c0-1 1-2 2-3z" class="I"></path><path d="M337 384h1c0 2 0 3-1 5h-1v-1c0-2 0-2 1-4z" class="P"></path><path d="M242 220l-6-12h10l7 16c1 0 2 3 2 3h0c-2-1-2-3-3-5 0-1 0 0-1-1l-3-7-6 6z" class="B"></path><path d="M451 577h2l32 66h0c-1 0-1-2-1-3-2-2-3-5-4-7s-2-5-3-6l-1-1c0-1 0-1-1-2 0-1 0 0-1-1v-1c-1-2-3-5-3-7-1-1-1-2-1-2-1-2-2-4-3-5-1-2-1-3-2-5v1l1 4 9 18c0 2 2 4 2 6h0 0l-26-55z" class="E"></path><path d="M414 509c2 1 3 6 4 9l12 28 8 19c2 5 4 12 6 17 1 1 1 2 1 3l-1-1s0-1-1-2l-2-5-10-25-10-25c-3-5-6-12-7-18z" class="U"></path><path d="M275 208h1v1c1 1 1 2 2 3 1 0 1 0 1 1l2 4h0 1v1l1 1h0v1c0 1 0 2 1 3 0 2 2 2 3 4h0 1v1 3h1c2 2 1 6 3 8v1c-1 1-2 1-3 1l1 1v1l1 1c1 2 1 3 2 5s3 5 4 8h-1 0c-2-3-3-7-6-10 0-1 0-1-1-2v-1c0-4-4-9-5-13-1-3-3-6-4-9h2l1-1c-1-1-1-2-2-3h-2v-1c-1-1-1-3-2-5 0-1-2-3-2-4z" class="E"></path><path d="M285 227h1l2 2-1 2h-1l-2-2 1-2z" class="I"></path><path d="M338 247l-2 3c-2-5-5-10-7-15l-10-19c0-2-3-6-3-7v-1h3v2c4 4 6 10 8 16 4 7 8 13 11 21h0z" class="E"></path><path d="M522 827c2 2 3 5 4 9 2 3 2 6 4 8l-5 12-7-19 2-2c0-4 0-5 2-8z" class="B"></path><path d="M319 208h5l11 24c2 4 4 8 5 12l-2 3h0c-3-8-7-14-11-21-2-6-4-12-8-16v-2z" class="W"></path><path d="M271 209h1c1-1 1-1 3-1 0 1 2 3 2 4 1 2 1 4 2 5v1h2c1 1 1 2 2 3l-1 1h-2c1 3 3 6 4 9 1 4 5 9 5 13v1 1 1 1l2 2v1c0 2 2 3 2 5-3-4-6-11-8-16-4-5-8-12-10-18 0-3-1-7-1-9-1-1-2-3-3-4z" class="J"></path><path d="M274 213c1 0 1 0 2 1 1 2 0 5 1 8 0 1 2 2 3 4 1 1 1 3 2 4 1 3 3 6 3 10-4-5-8-12-10-18 0-3-1-7-1-9z" class="H"></path><path d="M337 340c3 1 6 10 7 13l13 30 5 12c1 2 3 4 4 6l21 47h-1l-14-27-21-48c-5-11-11-22-14-33z" class="U"></path><path d="M451 577c-4-7-7-15-10-22l-13-27-19-41-13-26 1-1 6 12 18 39 32 66h-2z" class="G"></path><path d="M309 296c4 1 6 2 8 5 1 2 1 4 1 7-1 2-2 4-4 5s-4 1-7 0c-2-1-4-2-5-4v-3l-1-1h0c1-2 1-4 2-5 2-3 4-3 6-4z" class="I"></path><path d="M304 305c0-1 0-3 1-4 1-2 2-3 4-3s4 0 5 2c2 1 2 2 2 4v2h0c-2 0-5-2-6-3l-1-1h-1l-1-1-1 1c-1 1-1 1-1 3h-1z" class="G"></path><path d="M304 305h1c0-2 0-2 1-3l1-1 1 1h1l1 1c1 1 4 3 6 3h0 0c0 2-2 3-3 4-1 0-4 1-6 0s-2-3-3-5z" class="B"></path><path d="M373 470l-1-1c-1-2-2-5-1-8 1-2 3-4 5-4 2-1 5-1 7 0s3 4 4 7c0 3 0 6-3 8-2 1-4 1-6 1s-4-1-5-3z" class="F"></path><path d="M374 462c1-2 2-4 5-4 1-1 3 0 4 1 2 1 2 2 3 4 0 1 0 1-1 2h0c-2 0-3-1-4-2v-1c-2-1-4-1-5 0h-2z" class="G"></path><path d="M374 462h2c1-1 3-1 5 0v1c1 1 2 2 4 2h0c0 2-1 3-3 4v1h-1c-2 0-3 0-4-1-2 0-3-1-3-3-1-2-1-3 0-4z" class="C"></path><path d="M377 469h0v-3c1-2 1-2 3-2 1 1 0 4 2 6h-1c-2 0-3 0-4-1z" class="P"></path><path d="M255 212l-2-4h13 4c1 0 1 1 1 1 1 1 2 3 3 4 0 2 1 6 1 9-1-1-1-3-2-4h-2c-3 2-4 4-8 5-1 0-1 0-2 1l-6-12z" class="L"></path><path d="M255 212c2-2 6 0 9-2h2 0v1c-2 1-3 1-3 3v1h0c1 1 1 2 2 3h2c2-1 3-2 3-3v-2h1c1 1 1 2 1 4l-1 1c-3 2-4 4-8 5-1 0-1 0-2 1l-6-12z" class="B"></path><path d="M714 230l8-14c2-2 3-6 5-8h8l-1 1c-5 9-9 20-13 29-1 3-4 6-4 9-2-1-5-7-6-9h0c1-2 3-5 3-8z" class="G"></path><path d="M324 208h4c2 0 3 1 5 1v1h1v-1h0c1 0 1 1 1 1 1 4 4 7 5 10l2 2c0 1 1 2 1 3-1 0-1 1 0 1 0 2 1 3 1 4 1 1 1 1 1 2h0c1 1 1 2 2 3-2 2-5 5-7 9-1-4-3-8-5-12l-11-24z" class="J"></path><defs><linearGradient id="BO" x1="325.541" y1="344.324" x2="316.278" y2="327.93" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#302e2e"></stop></linearGradient></defs><path fill="url(#BO)" d="M324 326l1-1c3 2 5 3 6 7 2 3 1 6 0 9-1 2-3 4-4 4-4 1-8 1-11-1s-4-5-4-8c-1-2 0-4 1-6h2l2-2c2-2 5-2 7-2z"></path><path d="M314 336l4 1 1 1c2 2 5 1 8 3l-1 1c-2 1-5 1-7 0-3-1-4-3-5-6z" class="O"></path><path d="M314 336v-4c1 1 2 2 4 1h0 1c0 2 0 2 2 4l1-1h2l1 1c1 2 2 2 3 3l-1 1c-3-2-6-1-8-3l-1-1-4-1z" class="C"></path><path d="M315 330l2-2c2-2 5-2 7-2 3 2 5 3 6 6v3c0 2-1 3-2 5-1-1-2-1-3-3l-1-1h-2l-1 1c-2-2-2-2-2-4h-1 0c-2 1-3 0-4-1l1-2z" class="W"></path><path fill="#fafafa" d="M319 333c0-1 1-1 2-1l1 3v1l-1 1c-2-2-2-2-2-4z"></path><path d="M322 335v-3c1 0 2 0 3 1l5 2c0 2-1 3-2 5-1-1-2-1-3-3l-1-1h-2v-1z" class="J"></path><path d="M804 208l1 1 1-1h9 23 66c1 1 1 3 1 4-2 1-3 0-5 0l-11-1c-23-2-45 0-66 10v-1c1 0 2-1 3-1 1-1 1-1 2-1l1-1h1c2-1 0 0 1-1h2l2-1c1 0 2 0 2-2l-2-5h-9c-5 1-12 1-17 1-3 2-1 3-2 5s-2 3-4 4c0-1-1-1-2-1-2-1-3-3-4-5l1-1s0-1 1-2 3-1 5-1z" class="B"></path><path d="M805 214l-2 2c-2 0-2 0-3-2 0-2 0-2 1-4l1 2v2h0 1 2z" class="H"></path><path d="M801 210c1 0 1-1 3-1 1 1 1 2 2 3l-1 2h-2-1 0v-2l-1-2z" class="G"></path><path d="M518 810h0l-3-6c0-1-1-2-1-3l-2-7c-1-1-2-3-3-5h0 0-1l-2-4-2-5h-1v-1c0-1-1-2-1-2l-1-1v-2c-1-1-1-3-2-4l-1-4c-1-2-2-4-3-7l-6-14c0-1-1-2-1-3-1-1 0-1-1-2h0l-9-21-17-39-15-32c-3-6-6-12-8-17h1l29 61 4 8 10 21c1 3 3 6 4 9h0l5 12c3 5 5 12 8 17l-1 2c2 4 4 9 6 14l5 10 9 25h0z" class="H"></path><path d="M498 761c-3-4-4-9-6-13-2-5-5-10-6-15h0c-1-2-1-2 0-3l5 12c3 5 5 12 8 17l-1 2z" class="J"></path><path d="M311 208h2l14 30 6 12c1 0 2 2 2 3 0 0-1 3-2 4l-3 9v1h0l-1-2-29-57h11z" class="H"></path><defs><linearGradient id="BP" x1="181.29" y1="199.552" x2="188.617" y2="241.551" xlink:href="#B"><stop offset="0" stop-color="#9e9c9c"></stop><stop offset="1" stop-color="#c9c8c8"></stop></linearGradient></defs><path fill="url(#BP)" d="M129 208h100l15 28-2-1-14-10c-13-9-28-13-43-14-7-1-13-1-20-1-12 1-24 1-36 4v-6z"></path><defs><linearGradient id="BQ" x1="278.149" y1="229.601" x2="341.972" y2="265.643" xlink:href="#B"><stop offset="0" stop-color="#c8c7c6"></stop><stop offset="1" stop-color="#f2f2f1"></stop></linearGradient></defs><path fill="url(#BQ)" d="M297 208c2 3 3 6 5 10l7 14 11 23c3 5 7 11 9 17s0 15 1 22l-40-81v-1-2c2-2 4 0 7-2z"></path><path d="M429 572h2l2 2h1 0c0-1-1-2-1-3l-1 1h0c-1-2 0-4 0-5l10 25 7 16c2 5 5 9 6 14-2 0-4 0-6 1-1 0-2 1-2 2l-1 4s-1-1-2-1l-21-44v-2c-1-1-1-2-1-3 0-2 0-4 2-5 1-2 3-2 5-2z" class="E"></path><path d="M429 573c2 0 3 1 4 2 1 2 1 3 1 5l-1 1c-1 0-1-1-2-1 1-1 1-2 1-3-1-2-1-2-3-3v-1z" class="M"></path><path d="M423 582c-1-1-1-2-1-3 0-2 0-4 2-5 1-2 3-2 5-2v1 1c2 1 2 1 3 3 0 1 0 2-1 3 1 0 1 1 2 1-1 2-2 3-3 3-3 0-5 0-7-2z" class="V"></path><path d="M431 580h0s-1 1-2 1-2-1-3-2-1-2-1-3c2-2 2-2 4-2 2 1 2 1 3 3 0 1 0 2-1 3z" class="E"></path><defs><linearGradient id="BR" x1="376.781" y1="195.878" x2="357.901" y2="236.591" xlink:href="#B"><stop offset="0" stop-color="#adabab"></stop><stop offset="1" stop-color="#d5d4d4"></stop></linearGradient></defs><path fill="url(#BR)" d="M328 208h78v6c-16-1-32 0-46 9-4 3-9 8-13 12-1-1-1-2-2-3h0c0-1 0-1-1-2 0-1-1-2-1-4-1 0-1-1 0-1 0-1-1-2-1-3l-2-2c-1-3-4-6-5-10 0 0 0-1-1-1h0v1h-1v-1c-2 0-3-1-5-1z"></path><defs><linearGradient id="BS" x1="283.764" y1="220.006" x2="283.734" y2="302.994" xlink:href="#B"><stop offset="0" stop-color="#c8c8c7"></stop><stop offset="1" stop-color="#f2f1f1"></stop></linearGradient></defs><path fill="url(#BS)" d="M271 218c0 4 3 8 5 12l20 42c4 8 9 15 12 24h1c-2 1-4 1-6 4-1 1-1 3-2 5-1-3-2-5-3-8l-11-21-26-52c1-1 1-1 2-1 4-1 5-3 8-5z"></path><defs><linearGradient id="BT" x1="666.985" y1="190.821" x2="695.638" y2="242.662" xlink:href="#B"><stop offset="0" stop-color="#b4b3b2"></stop><stop offset="1" stop-color="#e3e3e3"></stop></linearGradient></defs><path fill="url(#BT)" d="M711 238c-4-5-10-10-16-14-18-14-40-13-61-10v-6h6 13 70l-2 6c-1 1-1 2-1 3s0 0-1 2c0 0 0 1-1 2h0l-4 9h0c0 3-2 6-3 8h0z"></path><path d="M711 238v-1c-1-3 8-20 10-23-1 1-1 2-1 3s0 0-1 2c0 0 0 1-1 2h0l-4 9h0c0 3-2 6-3 8z" class="W"></path><path d="M499 759l10 23c1 1 1 1 1 2 0 0 1 1 1 2 1 1 1 1 1 2l3 6 2 3 2 5 2 5 2 3c0-1 1-1 1-2s0-1 1-2v-3h1l-1-4v1c0-4 0-6 1-9h1c0-1 0-1 1-2 2-2 3-3 7-3h3v1c4 1 6 3 8 6 0 1 1 2 1 3l2 2-4 9-15 37c-2-2-2-5-4-8-1-4-2-7-4-9l-1-2c-1-1-2-4-3-5l-3-8c-3-6-7-12-9-18-1-3-3-6-4-9s-3-5-3-8l7 15c1 1 1 2 2 2 1 2 1 4 2 5l6 13c1 1 2 3 3 5 0-1 0-2-1-3s0-3 0-4h0l-9-25-5-10c-2-5-4-10-6-14l1-2z" class="L"></path><path d="M529 804c-1-2-2-3-2-5-1-3-1-6 1-8 1-3 3-4 6-4l1 1h1c-3 1-4 1-6 3 0 1-1 2-1 3-1 1-1 3 0 5s3 3 5 5c-2 0-3 0-4-1l-1 1z" class="Q"></path><path d="M534 787h4c4 1 6 3 8 6 0 1 1 2 1 3l2 2-4 9-2-2c-2 1-4 2-6 2-3 0-6-1-8-3l1-1c1 1 2 1 4 1-2-2-4-3-5-5s-1-4 0-5c0-1 1-2 1-3 2-2 3-2 6-3h-1l-1-1z" class="B"></path><path d="M537 798l3-1 1 2s1 0 2-1h1l-1 3c-2 0-4 0-6-1v-2z" class="L"></path><path d="M536 788c2 1 4 1 5 2 1 3 1 4 0 7h-1l-3 1v-3c-2 1-6-1-8-1 0-1 1-2 1-3 2-2 3-2 6-3z" class="W"></path><path d="M534 787h4c4 1 6 3 8 6 0 1 1 2 1 3l2 2-4 9-2-2c-2 1-4 2-6 2-3 0-6-1-8-3l1-1c1 1 2 1 4 1h1 2c2 0 4-1 6-3l1-3h-1c-1 1-2 1-2 1l-1-2h1c1-3 1-4 0-7-1-1-3-1-5-2h-1l-1-1z" class="N"></path><path d="M541 790c3 3 3 4 3 7v1h-1c-1 1-2 1-2 1l-1-2h1c1-3 1-4 0-7z" class="G"></path><path d="M546 793c0 1 1 2 1 3l2 2-4 9-2-2h0c3-3 3-8 3-12z" class="O"></path><path d="M387 464c1 2 1 3 2 5 1 3 2 7 4 11l16 37c2 5 5 12 8 18v2 1l2 1 13 28c0 1-1 3 0 5h0l1-1c0 1 1 2 1 3h0-1l-2-2h-2c-2 0-4 0-5 2-2 1-2 3-2 5 0 1 0 2 1 3v2c-1-2-2-3-3-5-1-3-2-7-4-10l-9-20c-1-2-3-5-4-7-1-3-2-5-3-8l-20-46c-2-5-6-12-7-18 1 2 3 3 5 3s4 0 6-1c3-2 3-5 3-8z" class="G"></path><path d="M425 562l1 1v1l-2 2h0-1c0-2 1-3 2-4z" class="W"></path><path d="M417 538v2c-1 3-3 4-5 5-2 0-3 0-5-1-1-1-2-2-2-4s0-3 2-4c1-2 2-2 4-2 3 0 5 1 6 3v1z" class="Y"></path><path d="M412 542c-1 0-1 1-2 1s-1 0-1-1c-1-1-1-1-1-2l1-1 3 3z" class="V"></path><path d="M409 539l-1-1c1-2 1-2 3-3 1 0 2 0 3 1s1 3 1 4-1 1-1 2h-2l-3-3zm37 90l1-4c0-1 1-2 2-2 2-1 4-1 6-1l2 4c3 2 4 7 6 10l8 19 8 18 7 16 8 17 8 17 5 10 2 4 3 6 2 4 7 12v2c1 1 1 1 1 2l1 1 4 10c2 3 4 6 7 9v-1l-1-2c0-1 0-1-1-2l-2-4c0-1 0-2-1-2v-2l-1-1-1-2v-1c-1-2-2-3-2-5-1-1-1-2-2-2v-1c0-1-1-3-2-4v-1l-1-1 1-1v-1l7 16c1 2 5 7 5 9l-1 1 1 2c1 1 1 1 1 2l1 3 1-1v1s1 1 1 2c1 0 1 0 1 1h-3c-4 0-5 1-7 3-1 1-1 1-1 2h-1c-1 3-1 5-1 9v-1l1 4h-1c0-1-1-1-1-2s-1-2-1-3l-3-6c-1-2-2-5-3-7v-1l-4-7-2-5c-2-3-4-8-5-12-1-2-2-6-4-8v-1l-3-6-1-3c-5-10-9-20-14-31-2-4-5-9-6-14-2-1-3-3-3-5-4-5-6-12-9-18-1-3-3-5-3-8 0-1-1-3-2-4-1-2-2-4-3-5l-7-16-7-13c1 0 2 1 2 1z" class="H"></path><path d="M461 662c1-2 2-3 4-3s3 1 4 2c0 1 1 2 0 4-1 1-1 1-3 1-1 1-2 0-3 0 0-1-1-3-2-4zm14 30c1-1 2-2 5-2 1 0 2 1 3 2v3c-2 2-3 2-5 2-2-1-3-3-3-5z" class="I"></path><path d="M479 691h1l1 1c0 2 0 2-1 3h-1c-1-1-1-1-1-3l1-1z" class="K"></path><path d="M446 629l1-4c0-1 1-2 2-2 2-1 4-1 6-1l2 4c0 2-1 4-3 5s-4 1-5 0c-1 0-2-1-3-2z" class="I"></path><path d="M451 624h3l1 1c0 2 0 2-1 4l-3-1c-1-1-1-1-1-2l1-2z" class="H"></path><defs><linearGradient id="BU" x1="379.411" y1="526.103" x2="390.166" y2="521.3" xlink:href="#B"><stop offset="0" stop-color="#c3c2c1"></stop><stop offset="1" stop-color="#f1f1f0"></stop></linearGradient></defs><path fill="url(#BU)" d="M242 220l6-6 3 7c1 1 1 0 1 1 1 2 1 4 3 5h0c3 4 5 8 7 12 2 6 5 11 8 16l12 25c3 5 5 9 7 14l4 8c1 1 1 3 2 5s2 3 3 6 3 6 4 9c1 1 1 3 2 4l1 2c1 1 1 2 1 3v1h1 0v3l176 406 1 1c0 2 1 3 1 4l1 2 1 2c3 5 5 10 7 15 2 4 4 8 5 12h0c0 3 2 5 3 8s3 6 4 9c2 6 6 12 9 18l3 8c1 1 2 4 3 5l1 2c-2 3-2 4-2 8l-2 2-91-214-19-42-6-14h0l-1-2-1-3-11-26c0-1 0-1-1-2h0l-1-2-4-10h0l-1-2-1-2c0-1 0-2-1-2 0-4-2-7-4-9l-9-23-13-28c0-3-2-6-3-8 0-2-1-3-1-3l-3-6-2-4v-2c-1-3-3-6-4-8v-1c-1-1-1-2-1-2-1-2-1-3-2-4v-1l-2-3c0-1 0-1-1-2l-9-20-7-18-4-7-17-40-8-18c-5-12-10-24-17-35l-9-15c-3-4-6-9-9-13-5-8-9-17-13-26z"></path><defs><linearGradient id="BV" x1="715.663" y1="516.817" x2="568.767" y2="428.861" xlink:href="#B"><stop offset="0" stop-color="#cfcece"></stop><stop offset="1" stop-color="#f4f3f3"></stop></linearGradient></defs><path fill="url(#BV)" d="M765 208h12l-2 8-1 1 1 1h0c3-3 4-7 5-10h14c3 0 7-1 10 0-2 0-4 0-5 1s-1 2-1 2l-1 1c1 2 2 4 4 5 1 0 2 0 2 1 2-1 3-2 4-4s-1-3 2-5c5 0 12 0 17-1h9l2 5c0 2-1 2-2 2l-2 1h-2c-1 1 1 0-1 1h-1l-1 1c-1 0-1 0-2 1-1 0-2 1-3 1v1h0c-4 2-8 4-11 7-7 5-14 11-20 18-15 18-26 39-35 61l-17 39-14 33-9 21c-2 6-5 12-7 17l-19 43-53 127-23 54-8 18c-2 3-3 7-5 9 0 1 0 2-1 3 0 3-2 6-3 9l-7 18-22 51-12 30c-3 6-5 13-8 19l-2-2c0-1-1-2-1-3-2-3-4-5-8-6v-1c0-1 0-1-1-1 0-1-1-2-1-2v-1l-1 1-1-3c0-1 0-1-1-2l-1-2 1-1c0-2-4-7-5-9l-7-16v1l-1 1-2-5c-1-1 0 0-1-2h0v-1c-1-1-2-3-2-4v-2c-1-1-1-2-2-3 0-1 0 0 1-1 1 1 1 2 2 4 0 1 1 2 2 3l1 3 1 1v1c1 1 1 0 1 1v1l1 2c1 1 0-1 1 1l1 3 2 5c1 1 0-1 1 1v2l5 9c1 3 4 7 5 10 1 2 1 3 2 4l1 1h0c1 1 2 2 3 2 1-1 1-2 2-2h1v-2h-1v-1l-1-2v-1h0l-1-3-3-7-1-1c-1-2-1-3-2-4v-1l-1-3-2-4-1-3-2-4c-1-2-2-5-3-7l-1-1v-1h-1v-2-1c-2-3-4-6-5-10l-1-2-1-2-3-7-1-1v-1l-1-1c0-1-1-2-1-3s0-1-1-2l-13-28-4-8-9-20-3-5c0-2-1-4-2-6-1-3-3-6-4-10h0c0-2-2-4-2-6l-9-18-1-4v-1c1 2 1 3 2 5 1 1 2 3 3 5 0 0 0 1 1 2 0 2 2 5 3 7v1c1 1 1 0 1 1 1 1 1 1 1 2l1 1c1 1 2 4 3 6s2 5 4 7c0 1 0 3 1 3h0l65 132 129-304 15-35v-1c1-1 1-1 1-2s1-2 1-3c1-3 3-7 4-10v-1l1-2c0-1 1-2 1-4 1-1 2-2 2-4 1-3 2-5 3-9l3-7 2-5 1-4 2-4 5-16c0-1 0-2 1-3l2-8v-1c0-1 1-1 1-2v-2l1-3v-1s1-1 1-2v-2c0-1 1-1 1-2v-3l1-2v-3l1-6c1-1 1-3 1-4 1-2 0-4 0-5 1-2 1-5 1-6 1-2 0-4 0-5v-14c-1-1 0-3 0-5-1-1-1-3-1-4v-1c0-1 0-2-1-2v-2l-1-2v-1-2l-1-1v-1c0-1 0-2-1-3l-8-18h0l1-1c3-5 6-12 8-18 1-2 2-3 2-5l1-1c3-5 4-11 8-15v1 1c-1 2-2 4-3 7h1c2-2 3-6 4-9 2-1 10-1 12 0h6 3 1 3z"></path><path d="M758 208h3c-3 5-5 11-8 15v-1c1-4 3-10 5-14zm4 0h3c-2 6-4 12-7 17 0-4 1-8 2-11 1-2 2-4 2-6h0z" class="W"></path><path d="M773 224h6c3 1 6 3 7 6 1 2 1 5 0 7-1 3-3 5-7 6-1 0-4 1-5 0-3 0-5-2-7-4-1-2-2-5-1-7 1-4 4-6 7-8z" class="M"></path><path d="M770 228c1-1 2-1 3-2 2-1 5-1 7 0l-4 1c-1 1-1 2-1 3-2-1-3 0-5-2z" class="G"></path><path d="M775 230c0-1 0-2 1-3l4-1c2 1 4 2 5 5 0 2 0 4-1 5h-1c-1 1-2 1-3 1-3-1-4-4-5-7z" class="U"></path><path d="M770 228c2 2 3 1 5 2 1 3 2 6 5 7 1 0 2 0 3-1h1 0c-1 2-3 4-5 4-2 1-4 1-6 0s-4-3-4-5c0-3 0-5 1-7z" class="C"></path></svg>