<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="72 32 360 652"><!--oldViewBox="0 0 480 752"--><style>.B{fill:#3d3d3d}.C{fill:#171717}.D{fill:#1f1e1e}.E{fill:#2b2a2a}.F{fill:#222}.G{fill:#252526}.H{fill:#b9b8b9}.I{fill:#7b7a7b}.J{fill:#363535}.K{fill:#323131}.L{fill:#424243}.M{fill:#908f8f}.N{fill:#6c6b6c}.O{fill:#828182}.P{fill:#494848}.Q{fill:#a8a7a8}.R{fill:#535253}.S{fill:#2e2d2e}.T{fill:#0f0f0f}.U{fill:#9b999a}.V{fill:#959495}.W{fill:#1b1b1b}.X{fill:#4c4d4c}.Y{fill:#737273}.Z{fill:#898788}.a{fill:#b0afaf}.b{fill:#5e5d5e}.c{fill:#595859}.d{fill:#666566}.e{fill:#626262}.f{fill:#b6b4b5}.g{fill:#090909}.h{fill:#cac9ca}.i{fill:#555}.j{fill:#9f9e9f}.k{fill:#d5d3d4}.l{fill:#f2f1f2}.m{fill:#e6e5e5}.n{fill:#fff}.o{fill:#030303}.p{fill:#ececec}</style><path d="M241 679s1 0 2-1l-1 3c-1 0-1-1-1-2z" class="G"></path><path d="M266 84v-6h1v6h-1z" class="l"></path><path d="M241 676h1l2 2h-1c-1 1-2 1-2 1v-2h0v-1z" class="F"></path><path d="M178 492l1 1c0 1 0 2 1 3h-1-1v-4z" class="Z"></path><path d="M394 84l1-7h0c1 2 0 5 0 7h-1z" class="l"></path><path d="M134 146h1c1 0 2 1 3 2v1c-1 0-1 0-2-1-1 0-1-1-2-2z" class="Q"></path><path d="M155 347c-1 0-1-1-2-2h1 2v1l1 1v1c-1-1-1-1-2-1z" class="h"></path><path d="M178 432v-6h1v2c1 1 1 1 1 3-1 0-1 1-2 1z" class="M"></path><path d="M178 488v1h2c0 1 0 3-1 4l-1-1c0-1-1-3 0-4z" class="O"></path><path d="M153 364l1-1h2v1c1 1 1 1 2 1 0 1 0 1-1 1s-3-1-4-2z" class="a"></path><path d="M174 440h1c-1 1 0 3-1 4v1c0 2-1 3-2 5h-1c1-1 1-2 1-3 1-1 1-2 1-3v-2c1-1 1-1 1-2z" class="l"></path><path d="M178 500c0-1 1-1 1 0h1l-1 1v1 1 2c-1 0-1 0-1 1-1-1 0-5 0-6z" class="O"></path><path d="M160 367h1c-1 1-2 3-3 5-1 0-2 1-3 2 1-3 2-5 5-7z" class="H"></path><path d="M285 414h0 1c2 1 3 4 4 6h0l-1-1s-1-1-2-1v2c-1-1-1-2-1-3v-1c-1-1-1-1-1-2z" class="l"></path><path d="M214 625c2 1 3 2 5 2v2l-8-3c1 0 2-1 3-1z" class="N"></path><path d="M155 374c1-1 2-2 3-2l-1 4-3 6c0-3 1-5 1-8z" class="f"></path><path d="M251 649c1 4 3 7 4 10l-1 1c-1-1-1-3-2-5 0-1-1-2-1-3l-1 2v-2c0-1 0-2 1-3z" class="F"></path><path d="M174 574l3-3c0 3-1 3-2 5 0 2-1 7-3 8h0l2-9v-1z" class="p"></path><path d="M348 276l3-9 1 2v3l-1 3c-1 1-2 1-3 1z" class="a"></path><path d="M178 318h1v1h1v1 2 1h0c1 1 1 2 0 3 0 0-1 0-2 1v-9z" class="V"></path><path d="M180 496h2 0c0 1 1 1 1 2h0c-1 0-1 0-1 1v1h-2-1c0-1-1-1-1 0v-4h1 1z" class="R"></path><path d="M178 496h1v4c0-1-1-1-1 0v-4zm0-27l1 3 2 2h-1l-1 1c0 1 1 1 2 1l-1 2v1h0-1v5h-1v-15z" class="I"></path><path d="M148 351l-2-2c4 0 9 1 12 2h1l-1 1v1h0c-2 0-3 0-5-1h0c-1-1-3-1-5-1z" class="H"></path><path d="M155 347c1 0 1 0 2 1 1 0 3 1 3 1 2-1 4 0 6 1-1 0-1 1-2 2h-1c0-1 0-1-1-1-3-1-5-2-7-4z" class="b"></path><path d="M180 489h3c0 1-1 3 0 4 0 1 0 2-1 3h0-2c-1-1-1-2-1-3 1-1 1-3 1-4h0z" class="i"></path><path d="M286 325c2 0 3-1 4-1l10-2s-1 1-2 1l1 1c-4 0-7 2-10 3l-1-1-1-1h-1z" class="Q"></path><path d="M158 363c1 0 6 0 7 1h0l-4 3h-1c-1 0-2-1-3-1 1 0 1 0 1-1-1 0-1 0-2-1v-1h2z" class="m"></path><path d="M148 351c2 0 4 0 5 1h0c2 1 3 1 5 1h0 1c-1 1-3 0-5 1h-8c0-1 0-1 1-2l1-1z" class="Q"></path><path d="M148 351c2 0 4 0 5 1h0-6l1-1z" class="l"></path><path d="M178 318v-8l-3-3c-3-2-6-4-9-7h0l9 6c1 0 2 2 3 3h1v2h0c1 2 1 4 1 6v1h-1-1z" class="j"></path><path d="M124 105l1-1 1 1h1s1 0 1 1c2 1 4 2 5 2 1 1 2 1 2 1 1 1 2 1 3 2l1-1c0 2 1 2 2 3s1 1 2 1l1-1v1 1c0 1 1 1 1 2l-21-12z" class="V"></path><path d="M180 318h2l1 1h1 0 2v5c-1 0-1 0-2-1h-4 0v-1-2-1h-1v-1h1z" class="O"></path><path d="M184 319h0 2v5c-1 0-1 0-2-1h-4 0v-1h3c1-1 1-2 1-3z" class="I"></path><path d="M274 314v-1c1 0 1-1 2-1 1 1 2 1 2 3h1 1 1c0 1 2 1 3 1h8c-2 0-5 1-8 1h-4-6l-1-1h-1l-1-1c2 0 4 1 6 0-1-1-2-1-3-1z" class="O"></path><path d="M250 654l1-2c0 1 1 2 1 3 1 2 1 4 2 5l-3 6-1-1h0v-11z" class="W"></path><path d="M125 591l-16 2 9-3 11-4 1 1c0 1-1 2-2 3-1 0-2 1-3 1h0z" class="S"></path><path d="M156 345l7 2c3 0 5 1 8 2h5-3v1c-2 1-5 0-7 0-2-1-4-2-6-1 0 0-2-1-3-1v-1l-1-1v-1z" class="a"></path><path d="M147 586h1c0 1 1 1 1 2l-15 19v-1l6-9c1-1 3-3 3-4v-2h0c0-1 1-2 2-3l2-2z" class="J"></path><path d="M71 85c5-1 11 0 16 0 4 0 8-1 12 0-2 1-4 1-7 1-1 1-1 0-2 1h-8c-3-3-7-2-11-2z" class="h"></path><path d="M172 478c1 2 1 6 0 8v4 1c-1 2 0 3-1 5v5c-1 3 0 6-1 9 0 1 0 2-1 4 0 1 1 2 0 4v2h-1c0-1 0-4 1-5v-3-1-4c1-1 0-4 0-5l1-1v-4c1-1 0-5 1-6v-3l1-10z" class="p"></path><path d="M184 484c1-1 0-2 1-3v4l1 6c-1-1-2-1-3-2h-3 0-2v-1c0-1 1-2 1-2s-1 0-1-2h1 3 2z" class="d"></path><path d="M182 484h2l1 1h-2c-2 0-4 1-5-1h1 3z" class="D"></path><path d="M184 484c1-1 0-2 1-3v4 2c-1 1-1 1-2 1v-3h2l-1-1z" class="R"></path><path d="M183 488c1 0 1 0 2-1v-2l1 6c-1-1-2-1-3-2h-3l-1-1h4z" class="J"></path><path d="M351 275l-1 4c0 1-1 2 0 3 0 1-1 1-1 2 0-1-1-1-1-1l-3 2s-1 1-1 2c0 0 0 1-1 2 0 0-1 1-1 2h0v1l-3 3c-1 2-2 2-3 2 3-4 6-9 9-14 1-2 2-4 3-7 1 0 2 0 3-1z" class="U"></path><path d="M230 684c0-1 0-3 1-5 0 1-1 3 0 4l1 1c2 0 5 0 8 1l-5 8c-1-3-4-6-5-9z" class="X"></path><path d="M300 322c8-3 14-6 21-10-1 1-1 2-3 3-3 2-5 4-8 6-2 1-4 1-5 2-2 1-4 1-6 1h0l-1-1c1 0 2-1 2-1z" class="U"></path><path d="M235 46h0c1 4 1 9 1 13v19c0 1 1 3 0 5v1c-1 0-1 0-1-1V46z" class="a"></path><path d="M285 455c1 1 1 2 2 3 0 0 0 1 1 2 0 1 0 2 1 3v2c0 1 1 1 1 1 0 1 1 2 1 3v-1-2h1c0 1 1 3 1 4v3c1 1 1 2 1 4 1 1 1 2 1 3h0c-1 0-1-1-2-2l-1-5c-1-1-1-3-2-4h-1v1c-1 0-1-1-2-1v-3-1-1c-1-1 0-1-1-2-1 0-1-2-1-3-1-1-1-2 0-3v-1z" class="l"></path><path d="M111 602l2 1h-1c0 1-1 2-2 2h-2c-2 1-5 3-7 4s-2 2-3 3c1-1 2-1 2-1h1c1-1 1-1 2-1l1-1h1 0c1-1 3-2 4-2h0l1-1h1 2c-10 4-20 10-30 12l28-16z" class="S"></path><path d="M183 489c1 1 2 1 3 2 0 4 0 8-1 12 0 1-1 1-2 1l-1 1h-3v-2-1-1l1-1h2v-1c0-1 0-1 1-1h0c0-1-1-1-1-2h0 0c1-1 1-2 1-3-1-1 0-3 0-4z" class="L"></path><path d="M181 502h2v1c-1 1-2 0-4 0v-1h2z" class="e"></path><path d="M182 500h2 0v1h-2l-1 1h0-2v-1l1-1h2z" class="b"></path><path d="M286 462c1 1 0 1 1 2v1 1 3c1 0 1 1 2 1v5 3c1 1 1 3 1 4h-1v-3c0-1 0-1-1-1v-6h-1v5l1 1c0 2-1 5 0 7 1 1 0 4 0 6 1 1 0 2 0 4 1 1 1 2 1 3 1 2 0 4 0 5s1 1 1 1c0 1-1 3 0 4v3c-2-2-1-7-2-9v-5c-1-1 0-2 0-3-1-2-1-4-1-6v-3c-1-2 0-4 0-6 0-1-1-2-1-2v-15h0z" class="p"></path><path d="M178 328c-2-2-7-1-9-1-2-1-4-1-5-1h-10c-2-1-4 0-6-1l1-1h2l10 2c5 0 12 1 17 1h0v1h4v2c1 2 2 1 1 4v4c-1 0-2-1-2-1-1 0-2 1-3 1v-2h0c0-3 1-6 0-8z" class="M"></path><path d="M79 90l-16-4c-2 0-6 0-7-2h0l5 1h10c4 0 8-1 11 2h-1v1h-6c2 1 3 1 5 2h-1z" class="V"></path><path d="M61 85h10c4 0 8-1 11 2h-1c-4 0-7-1-11-1-3-1-6-1-9-1z" class="H"></path><path d="M166 542l1 1v1c0 1-1 2-1 2-2 2-5 7-6 9l-9 12-3 4c-1 1-3 2-4 2 8-9 16-20 22-31z" class="c"></path><path d="M180 323h4c1 1 1 1 2 1v3c1 1 0 2 1 3h0l1 3 1 2-1 1h0l-2 2h-1-1v-5l-1 1c1-3 0-2-1-4v-2h-4v-1c1-1 2-1 2-1 1-1 1-2 0-3z" class="G"></path><path d="M188 336c-1-1-1-2-2-2 0-2 0-2 1-4l1 3 1 2-1 1z" class="b"></path><path d="M180 323h4c1 1 1 1 2 1v3h-1c-1 0-5-1-6 0l-1 1v-1c1-1 2-1 2-1 1-1 1-2 0-3z" class="O"></path><path d="M182 328h3v1c0 3 0 6 1 8v1h-1-1v-5l-1 1c1-3 0-2-1-4v-2z" class="I"></path><path d="M162 351c1 0 1 0 1 1v2c0 1 0 2 1 3h-1v-1c-1-1-27 0-29 0-3 0-11 1-12-1l24-1h8c2-1 4 0 5-1h-1v-1l1-1s1 1 2 1l1-1z" class="e"></path><path d="M162 351c1 0 1 0 1 1v2h-9c2-1 4 0 5-1h-1v-1l1-1s1 1 2 1l1-1z" class="M"></path><path d="M178 328c1 2 0 5 0 8h0c-1-1 0-3 0-4h-16-6-35c-4 0-9 1-12 0h-3l1-1h71v-3z" class="l"></path><defs><linearGradient id="A" x1="387.212" y1="88.005" x2="384.933" y2="82.661" xlink:href="#B"><stop offset="0" stop-color="#6f6d6f"></stop><stop offset="1" stop-color="#838180"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M394 84v2c-1 1-2 1-3 2-2 1-4 2-5 3h-2v1h0l-4-1h-2v-3-2h0l-1-1h2l15-1z"></path><path d="M378 88c3-1 7 1 10 0 2 0 3-2 5-2h1c-1 1-2 1-3 2-2 1-4 2-5 3h-2v1h0l-4-1h-2v-3z" class="O"></path><path d="M380 91c0-1 0-1 1-2 2 1 1 2 3 2v1h0l-4-1z" class="I"></path><path d="M183 464v1h2v1h-1c0 1 0 1-1 1v1c1 0 1 0 1 1l-1 1c0 1 0 1 1 1l1-1v3 8c-1 1 0 2-1 3h-2-3v-5h1 0v-1l1-2c-1 0-2 0-2-1l1-1h1 2v-2c-1-2-3-1-4-2 1-1 2 0 2-1s0-1-1-2v-1l3-2z" class="b"></path><path d="M180 474c1 0 2 0 3 1l-2 1c-1 0-2 0-2-1l1-1z" class="J"></path><path d="M180 479c1 0 3 0 4 1v1h-3c-1 0-1-1-1-2h0z" class="L"></path><path d="M179 484v-5h1c0 1 0 2 1 2 1 1 1 2 1 3h-3z" class="N"></path><path d="M177 520h0v-1l1 1-3 9-3 7c0 1-1 2-1 3l1 1c-1 2-3 4-4 6l-1 1-1-1s1-1 1-2v-1l-1-1v-1h1l7-18c0-1 1-3 1-4 1 1 1 1 2 1z" class="L"></path><path d="M166 541c1 0 2 0 3 1-1 1-1 2-2 3v-1-1l-1-1v-1z" class="B"></path><path d="M175 519c1 1 1 1 2 1l-2 5c0-1 0-2-1-2 0-1 1-3 1-4z" class="d"></path><defs><linearGradient id="C" x1="167.139" y1="531.846" x2="174.774" y2="531.544" xlink:href="#B"><stop offset="0" stop-color="#4a4645"></stop><stop offset="1" stop-color="#626267"></stop></linearGradient></defs><path fill="url(#C)" d="M174 523c1 0 1 1 1 2l-3 10-1 1c0 2-1 3-2 4-1 0-1 0-1 1h-1l7-18z"></path><path d="M291 557c1 2 2 3 3 5 0 2 2 3 2 4 1 1 1 2 1 3v1c2 3 5 7 8 10l7 6 2 2c0 1 0 1-1 1l-1 1h0l15 19v1l-12-16c-3-3-4-5-7-8h0c0-3-4-6-6-8-4-5-9-10-11-15v-1l-1-1h-1-3s1-2 2-2c1-1 1 0 3-2z" class="G"></path><path d="M297 570c-2-2-3-4-4-5 0-2-1-3-1-4h-1l1-1s1 1 1 2c1 2 2 3 3 4s1 2 1 3v1z" class="K"></path><path d="M141 360c4 0 8 0 12 1l-1 2-6-1c-2 0-4 1-6 3-6 5-10 12-10 20-1 8 1 16 7 23 4 5 10 8 17 8 3 1 7 0 9-1h1l-1 1c-6 2-12 2-18-1-6-2-12-8-14-14-3-8-3-18 0-25s8-12 15-15l-5-1z" class="M"></path><path d="M166 364c3-1 11-1 14 0h3-4c-1 1-1 2-1 2h0v-1l-4 1-3 1c-5 2-10 6-13 10-2 3-3 6-4 9v-4l3-6 1-4c1-2 2-4 3-5l4-3h1z" class="P"></path><path d="M171 367v-1h0 1 0l1-1 1 1-3 1z" class="L"></path><path d="M166 364c3-1 11-1 14 0-3 0-7-1-11 0-1 1-4 3-6 4l-6 8 1-4c1-2 2-4 3-5l4-3h1z" class="Z"></path><path d="M324 587c3 0 7 6 9 4h3 1s0 1 1 1c1-1 1-1 2 0h4c-2-1-4-1-6-2h0c4 1 8 2 12 4-5 0-12-2-17-3 8 7 17 13 26 18l9 5c3 1 5 2 7 4-5-2-11-4-16-6-2-1-4-2-5-3 2 0 3 2 6 2h0 0c1 1 5 2 6 2l-16-8c-1-1-3-3-5-3 0-1-1 0-2-1-2-1-5-3-7-4-3-1-6-3-8-5v-1l3 2v-1c-1-1-5-3-7-5h0z" class="E"></path><path d="M182 609c3 0 9-5 11-7s3-4 5-5c-3 6-7 10-12 14l1 1 1 1c-2 2-4 5-6 6l-1 1c-2-2-4-6-6-8 3-1 5-2 7-3z" class="N"></path><path d="M186 611l1 1 1 1c-2 2-4 5-6 6v-1c1-2 2-4 4-7z" class="J"></path><defs><linearGradient id="D" x1="254.053" y1="618.759" x2="258.127" y2="626.051" xlink:href="#B"><stop offset="0" stop-color="#1c1b1c"></stop><stop offset="1" stop-color="#403f40"></stop></linearGradient></defs><path fill="url(#D)" d="M259 615c2 2 3 4 5 6 0 1 1 2 2 3l-3 1-9 3-3 1c-1-1-1-2-1-4v-1-1-1-3h1 0 2c1-1 2-1 3-1 1-1 1-2 3-2h0v-1z"></path><defs><linearGradient id="E" x1="248.738" y1="621.815" x2="253.342" y2="625.885" xlink:href="#B"><stop offset="0" stop-color="#0a0e0c"></stop><stop offset="1" stop-color="#272426"></stop></linearGradient></defs><path fill="url(#E)" d="M250 619h1 0 2c-1 2-1 6 0 8l1 1-3 1c-1-1-1-2-1-4v-1-1-1-3z"></path><path d="M259 615c2 2 3 4 5 6 0 1 1 2 2 3l-3 1v-1c-1-2-3-5-4-7-1 0-2 1-3 1 1-1 1-2 3-2h0v-1z" class="W"></path><defs><linearGradient id="F" x1="161.486" y1="574.715" x2="167.76" y2="580.334" xlink:href="#B"><stop offset="0" stop-color="#343332"></stop><stop offset="1" stop-color="#4d4c4f"></stop></linearGradient></defs><path fill="url(#F)" d="M167 574c3-2 6-4 9-5 2-1 3-1 5-1l-4 3-3 3c-4 4-9 7-13 10-2 0-5 2-6 3v1 1h-1c-1 0-3-2-4-2h1c1-1 1-2 1-2l2-2h1l3-3c1-2 3-3 4-5h1 1l2-1h1z"></path><path d="M163 575h1l2-1h1c0 1-1 2-2 2l-4 4c-1 1-3 2-4 3-1 0-1 1-3 1v-1h1l3-3c1-2 3-3 4-5h1z" class="B"></path><path d="M183 334l1-1v5h1c1 2 2 2 3 3v1c1 1 3 1 5 3 1 1 3 1 4 3h-1v1h-2c-1 0-2-1-4-1h-8-5l1-2v-5l-1-1 1-2c1 0 2-1 3-1 0 0 1 1 2 1v-4z" class="N"></path><path d="M178 341c0-1 1-1 1-2 2 0 2 1 3 1l-1 1v1h-1c-1 0-1 0-2-1z" class="B"></path><path d="M182 340c1 0 2 1 2 1l1 1c1 1 3 2 3 3-2-1-5-2-7-3v-1l1-1z" class="X"></path><path d="M182 340c1 0 2 1 2 1v1c-1-1-2-1-3-1h0l1-1z" class="P"></path><path d="M180 342h1c2 1 5 2 7 3 3 1 6 1 8 3v1h-2c-1 0-2-1-4-1-4-1-7-3-10-6z" class="D"></path><path d="M178 341h0c1 1 1 1 2 1 3 3 6 5 10 6h-8-5l1-2v-5z" class="M"></path><path d="M97 358h-1c-1 0-1 0-1-1 0 0 1-1 2-1h4c7-1 14-2 21-1 1 2 9 1 12 1 2 0 28-1 29 0v1c-2 1-5 1-7 1h-13-31c-4 0-9-1-14 0h-1z" class="H"></path><path d="M356 366v1 11 5c4 1 9 0 13 0 2 0 4 0 6 1-3 0-8 1-11 0h-9-3v1h0v2c0 2 0 3-1 4v-2h-1v-6h-1l-1-3v-6c1-1 1-2 2-3v-4h2 4v-1z" class="c"></path><path d="M348 374c1-1 1-2 2-3 0 2 1 5 0 7-1 1-1 2 0 2v1 2h-1l-1-3v-6z" class="I"></path><path d="M356 383c-1 0-1-1-2-2l-2-2 2-1v-2-6h2v-3 11 5z" class="N"></path><path d="M272 149l1-2s1 0 1 1h5 0c0 1 0 2 1 3v1 14c1 0 1 0 2 1h-2c-2 1-5 1-7 0h0v-3l-1-15z" class="H"></path><path d="M279 148h0c0 1 0 2 1 3v1h-1c-1 0-1-1-2-2h1c0-1 1-1 1-2z" class="V"></path><path d="M179 505h3l1-1c0 1 1 2 1 3 0 0 1 0 1 1h-1c1 1 1 0 2 1 0 1-1 1-2 1v1c-1 2-1 5-2 6-2 9-6 16-10 23l-1-1c0-1 1-2 1-3l3-7 3-9-1-1v1h0c-1 0-1 0-2-1 1-3 2-7 2-10l1-2h1 0l-1-1c0-1 0-1 1-1z" class="R"></path><path d="M183 509c0 1 1 1 1 1v1c-1 2-1 5-2 6v-2c0-1 1-1 0-2 0-1 0-2 1-4z" class="L"></path><path d="M177 509l1-2c1 1 2 1 3 1h4-1c1 1 1 0 2 1 0 1-1 1-2 1 0 0-1 0-1-1h-4-2z" class="C"></path><path d="M179 505h3l1-1c0 1 1 2 1 3 0 0 1 0 1 1h-4c-1 0-2 0-3-1h1 0l-1-1c0-1 0-1 1-1z" class="J"></path><path d="M214 604h3 0v2l-11 16-1 1h0l-5 11c-2-1-4-2-6-4 3-2 5-5 6-8l4-5h0l8-11c1-1 2-1 2-2z" class="e"></path><path d="M212 606l1 1c0 1 0 2-1 3-2 3-5 6-8 9v-2l8-11z" class="O"></path><path d="M282 582c1 1 2 1 3 3l1 1c1 2 4 5 6 6l-7 12-2-3c-2-1-3-3-5-4l-1-3h0c0-1 1-2 2-3l3-9z" class="D"></path><path d="M285 585l1 1c-2 2-2 6-2 9 0 1-1 2-1 2 0 2 1 3 0 4-2-1-3-3-5-4 4-3 4-9 7-12z" class="T"></path><path d="M282 582c1 1 2 1 3 3-3 3-3 9-7 12l-1-3h0c0-1 1-2 2-3l3-9z" class="G"></path><defs><linearGradient id="G" x1="232.019" y1="675.251" x2="219.459" y2="670.533" xlink:href="#B"><stop offset="0" stop-color="#535253"></stop><stop offset="1" stop-color="#6a696a"></stop></linearGradient></defs><path fill="url(#G)" d="M231 663h1 0v12 2l-1 1v1c-1 2-1 4-1 5l-12-20c1 0 1 0 1-1h10 2z"></path><path d="M230 665h0c-3 1-6 0-8 1h-1l-1-1h0 10 0z" class="I"></path><path d="M231 663h1 0v12 2l-1 1v-9c-1-1-1-1-1-2l1-2h-1 0c-2-1-4 0-6-1 2 0 3 0 5-1h0 2z" class="J"></path><path d="M289 561h1l1 1v1c2 5 7 10 11 15 2 2 6 5 6 8h0c-8-4-15-9-21-17l-2-2h-1c0-1-1 0-1 0l-1-1 1-1h2c0-1 1-2 1-4h3z" class="R"></path><path d="M289 561h1l1 1c-1 0-1 0-2 1-1 0-1 1-2 1 0 1 1 3 1 3 0 1-1 2-1 2l-2-2h-1c0-1-1 0-1 0l-1-1 1-1h2c0-1 1-2 1-4h3z" class="P"></path><path d="M286 561h3l-2 2v2h-1v1 1h-1-1c0-1-1 0-1 0l-1-1 1-1h2c0-1 1-2 1-4z" class="E"></path><path d="M291 563c2 5 7 10 11 15h-1c-4-3-7-5-10-8l-3-3 1-2c1-1 1-2 2-2z" class="d"></path><path d="M164 357h0v4 1h-1c-1 1-3 1-5 1h-2-2l-1 1-1-1 1-2c-4-1-8-1-12-1l-44-2h1c5-1 10 0 14 0h31 13c2 0 5 0 7-1h1z" class="D"></path><path d="M153 361c2 0 8 0 10 1-1 1-3 1-5 1h-2-2l-1 1-1-1 1-2z" class="j"></path><path d="M217 606h1s0 1 1 1h0v13 1 1 3 2c-2 0-3-1-5-2-1 0-2 1-3 1l-4-1-2-2h0l1-1 11-16z" class="g"></path><path d="M219 607h0v13c-1-1-1-1-2-1v-1c-1-1-4-1-5-1 1-2 1-3 3-4 0-1 1-2 2-3l1-1s0-1 1-2z" class="W"></path><path d="M206 622v1h2c1-1 2-3 2-4l1-1c2 0 4 0 6 1 1 0 1 0 2 1v1 1 3 2c-2 0-3-1-5-2-1 0-2 1-3 1l-4-1-2-2h0l1-1z" class="E"></path><path d="M207 625c2-1 2-2 4-1h2 0l1 1c-1 0-2 1-3 1l-4-1z" class="P"></path><path d="M213 624c0-1-1-1-1-3 1 0 5 1 7 1v3 2c-2 0-3-1-5-2l-1-1z" class="B"></path><path d="M174 149c1-1 3-1 4-1l1 1h-1c0 1 1 1 1 1v1 1h1v1h-1l1 1c1 0 1 2 1 2h0l1 3 2 8 1 8c-1 1-2 1-2 1-2 0-3 1-4 0h1c0-1-1-4-1-5s-1-1-2-2c-1-6-3-12-5-18 0-2 0-2 2-2h0z" class="Q"></path><path d="M181 156h0v2h-1l-1-1 2-1z" class="a"></path><path d="M181 156l1 3 2 8c-1 0-1-1-2-2 0-1 0-2-1-3v-2-1h-1v-1h1v-2z" class="M"></path><path d="M276 561l1 2h0c1-1 1-1 2-1 1 2 0 3 2 4v-1s0-1 1-2l1 1v1l-1 1 1 1s1-1 1 0c-1 1-1 2-2 3l12 16c-1 1-1 3-2 5v1c-2-1-5-4-6-6l-1-1c-1-2-2-2-3-3l-2-2c-1-2-2-4-4-7l3 3c0 1 1 2 2 3v-1c-1-4-4-7-6-10v-1 1l1 1v-1l-2-6c1 0 1 0 1 1h1v-2z" class="P"></path><path d="M275 568v-1 1l1 1v-1l9 11h-1l-2-1h-1c-1-4-4-7-6-10z" class="Z"></path><path d="M276 573l3 3c0 1 1 2 2 3v-1h1l2 1h1l1 2 3 3 1 3c1 1 1 3 2 4v1c-2-1-5-4-6-6l-1-1c-1-2-2-2-3-3l-2-2c-1-2-2-4-4-7z" class="i"></path><path d="M289 584l1 3h-1l-1-2c1 0 0 0 1-1z" class="e"></path><path d="M282 578l2 1h1l1 2 3 3c-1 1 0 1-1 1s-2-2-3-3h-1s-1-1-1-2l-1-2z" class="N"></path><path d="M282 578l2 1h1l1 2h-1l-2-1-1-2z" class="O"></path><path d="M178 432l1 1c1 0 5 1 6 1v-1c1 0 1 0 2 1 0 2 0 4 1 6v2c-1 1-1 2-1 4h0l-1 8h1v2l-1 1h-1v1-1c-1 1-1 2-1 3s-1 3-1 4l-3 2v1c1 1 1 1 1 2s-1 0-2 1c1 1 3 0 4 2v2h-2l-2-2-1-3v-5-20c0-3 1-7 0-11h0v-1z" class="d"></path><path d="M185 442c1 1 1 3 1 4h1 0l-1 8h1v2l-1 1h-1v1-1c1-5 0-10 0-15z" class="D"></path><path d="M186 454h1v2l-1 1h-1c0-1 1-2 1-3z" class="b"></path><path d="M185 434v-1c1 0 1 0 2 1 0 2 0 4 1 6v2c-1 1-1 2-1 4h-1c0-1 0-3-1-4v-8z" class="g"></path><defs><linearGradient id="H" x1="181.258" y1="448.329" x2="176.658" y2="447.516" xlink:href="#B"><stop offset="0" stop-color="#787277"></stop><stop offset="1" stop-color="#838582"></stop></linearGradient></defs><path fill="url(#H)" d="M178 433l3 1-1 1c-1 5 0 10 0 15v4c1 1 0 2 0 3s0 4-1 6c0 0-1 0-1 1v-20c0-3 1-7 0-11z"></path><path d="M177 169c1 1 2 1 2 2s1 4 1 5h-1c1 1 2 0 4 0l2 1v5l1 3-1 12c-1 1 0 2 0 3-1 1 0 8-1 9 1 0 1 0 1 1h-1c0 1-1 1-1 2v5c-1 1-1 1-1 2h-1-2-1v-2l1-32c-1-2-1-5-1-7h0c-1-2-1-6-1-9z" class="f"></path><path d="M185 177v5c0-1-1-1-2-2v-1c1-1 1-1 2-1v-1z" class="j"></path><path d="M179 185v7c0 1 1 2 0 3v1 1s0-1 1-1v-4 4 11c0 2-1 6 0 8l-1 1-1 1 1-32z" class="H"></path><path d="M152 114c1 1 2 1 2 1l1 2h0c1 1 2 2 3 2h0l1 1v1h1l2 2 5 6c0 1 1 1 1 2 1 0 1 1 2 2 1 2 2 3 3 5 0 0 1 1 1 2s0 1 1 2l1 1h0c1 2 2 3 2 4v1c-1 0-3 0-4 1h0c-2 0-2 0-2 2-2-3-3-5-4-8-3-6-8-13-14-18l-9-8h0c0-1-1-1-1-2 2 0 3 0 5 1 0-1 1-1 2-1s1 0 1-1h0z" class="U"></path><path d="M169 138h3l-2 2h-2v-1l1-1z" class="Q"></path><path d="M175 142l1 1h0c1 2 2 3 2 4v1c-1 0-3 0-4 1l1-1 2-2h-1-2 0 0 0l-1-1c1 0 2-1 2-1-1-1-2 0-4 0v-1c1 0 3-1 4-1z" class="Z"></path><path d="M152 114c1 1 2 1 2 1l1 2h0c1 1 2 2 3 2h0l1 1v1h1l2 2 5 6c0 1 1 1 1 2-1 0-2 1-3 1h-1l2-2h0-1s-1 1-2 1c0 0-1 0-1 1h0l-3-3c0-1-1-1-2-2-2-3-5-5-7-7l-3-2v-1h2v-1c0-1 1-1 2-1s1 0 1-1h0z" class="a"></path><path d="M152 114c1 1 2 1 2 1l1 2h0c1 1 2 2 3 2h0c-1 1-2 1-2 1-2-1-3-1-5-1 0 0-1 0-1 1l-3-2v-1h2v-1c0-1 1-1 2-1s1 0 1-1h0z" class="Q"></path><path d="M152 117c1-1 2-1 3 0h0c-1 1-2 1-3 1v-1z" class="f"></path><path d="M152 114c1 1 2 1 2 1l1 2c-1-1-2-1-3 0l-1-1v-1c1 0 1 0 1-1h0z" class="I"></path><path d="M159 121h1l2 2 5 6h-3-1c0-1 1-1 1-2h-1l-2 2h0-1v-1h1c0-2-1-1-2-1l-1-1h2v-1h-1l-1-1 2-1h-1-2c0 1-1 1-1 1h-1v-1c1 0 2-1 2-1h1l1-1z" class="H"></path><path d="M179 219h2 1c0-1 0-1 1-2v-5c0-1 1-1 1-2h1c0-1 0-1-1-1 1-1 0-8 1-9 0-1-1-2 0-3v22s-1 0-2 1h0c1 0 2 0 2 1l1 1c0 1-1 3 0 5 0 4 0 27-1 29h-5c-1 0-1 0-1 1h-1v-6-17-9-5h-2v-1h2 1z" class="a"></path><path d="M179 219h2 1c0-1 0-1 1-2v-5c0-1 1-1 1-2h1c0-1 0-1-1-1 1-1 0-8 1-9 0-1-1-2 0-3v22s-1 0-2 1h0c1 0 2 0 2 1l1 1c0 1-1 3 0 5 0 4 0 27-1 29h-5c0-1 0-4 1-5v4h0 1v-1-2c0 1 0 1 1 1v-9c0-7 1-16-1-23l-1-1h-1-1v-1z" class="U"></path><path d="M273 167c2 1 5 1 7 0v29 10 3h-4-3c-1-4 0-9 0-14v-18c0-3-1-7 0-10z" class="f"></path><path d="M276 209s1-1 1-2c1-1 0-4 0-5l1-1c0 2 0 5 1 7v-1-2l1 1v3h-4z" class="H"></path><defs><linearGradient id="I" x1="185.74" y1="388.635" x2="176.962" y2="389.619" xlink:href="#B"><stop offset="0" stop-color="#5f5f5f"></stop><stop offset="1" stop-color="#828082"></stop></linearGradient></defs><path fill="url(#I)" d="M183 364c2 0 3 0 4 1h-2l1 1v2 3 4c-1 1-1 3-1 4 0 3 1 27 0 28h-3-2-1v2 2 6c0 1 0 2-1 3v-9-24-12c0-3-1-6 0-8v-1h0s0-1 1-2h4z"></path><path d="M183 364c2 0 3 0 4 1h-2l1 1v2 3 4c-1 1-1 3-1 4 0-2 0-7-2-9l-2 1c-1 0-2 0-3-1v-3-1h0s0-1 1-2h4z" class="F"></path><path d="M183 364c2 0 3 0 4 1h-2l1 1v2c-1-1-6-1-8-2 0 0 0-1 1-2h4z" class="g"></path><path d="M350 279l1-1c-1 6-2 11-2 16v6l-1 2-1-1h0c0-1-2-2-2-3h-1l-1 1c1 1 1 1 1 2l-4 3h0l-1-1-6 6c-9 8-18 14-29 18l-6 3c-1-1-2-1-3-1v1h-1 0c0-1-1-1-2-1l1-1h-3c0-1-1-1-1-1 3-1 6-3 10-3h0c2 0 4 0 6-1 1-1 3-1 5-2 3-2 5-4 8-6 2-1 2-2 3-3 6-5 11-9 15-15 1 0 2 0 3-2l3-3v-1h0c0-1 1-2 1-2 1-1 1-2 1-2 0-1 1-2 1-2l3-2s1 0 1 1c0-1 1-1 1-2-1-1 0-2 0-3z" class="O"></path><path d="M345 285l1 1c0 1 0 1-1 2-1 2-2 2-3 4v-1h0c0-1 1-2 1-2 1-1 1-2 1-2 0-1 1-2 1-2z" class="Z"></path><path d="M350 279l1-1c-1 6-2 11-2 16v6l-1 2-1-1h0c0-1-2-2-2-3h-1l-1 1c1 1 1 1 1 2l-4 3h0l-1-1c1-2 2-3 4-5 3-4 5-9 6-14 0-1 1-1 1-2-1-1 0-2 0-3z" class="g"></path><defs><linearGradient id="J" x1="188.513" y1="289.198" x2="175.395" y2="287.429" xlink:href="#B"><stop offset="0" stop-color="#787777"></stop><stop offset="1" stop-color="#a7a6a7"></stop></linearGradient></defs><path fill="url(#J)" d="M180 256h5c1 2 0 6 0 9v6l1 38v9l-2 1h0-1l-1-1h-2v-1c0-2 0-4-1-6h0v-2c-1-2-1-3-1-4v-11-10-27h1c0-1 0-1 1-1z"></path><path d="M178 294l2 1v9c-1 0-1 0-2 1v-11z" class="j"></path><path d="M180 256h5c1 2 0 6 0 9h-2c0-2-1-5 0-7 0 0 1 0 1-1h-1-1-3 0c0-1 0-1 1-1z" class="O"></path><defs><linearGradient id="K" x1="329.335" y1="583.305" x2="320.309" y2="594.195" xlink:href="#B"><stop offset="0" stop-color="#1b1a1a"></stop><stop offset="1" stop-color="#393839"></stop></linearGradient></defs><path fill="url(#K)" d="M290 550c1 1 4 4 4 5 7 10 16 21 26 29 1 1 2 3 4 3h0c2 2 6 4 7 5v1l-3-2v1c2 2 5 4 8 5 2 1 5 3 7 4 1 1 2 0 2 1 2 0 4 2 5 3l16 8c-1 0-5-1-6-2h0 0c-3 0-4-2-6-2-3 1-27-13-32-16-3-1-6-2-9-4 1 0 1 0 1-1l-2-2-7-6c-3-3-6-7-8-10v-1c0-1 0-2-1-3 0-1-2-2-2-4-1-2-2-3-3-5v-1c0-2-1-4-1-6z"></path><path d="M294 555c7 10 16 21 26 29 1 1 2 3 4 3h0c2 2 6 4 7 5v1l-3-2c-1-1-2-2-4-2h0l2 2 1 1v1c-2-1-3-3-5-4-8-6-16-14-22-22-2-2-3-5-4-7-1-1-2-3-2-4v-1z" class="g"></path><path d="M231 628h1v5 1 29h0-1-2-10c0 1 0 1-1 1h-1l-2-6c1-1 2-3 3-5l1-2c1-2 0-5 0-7v-14h1l1-1c1-1 3 0 4 0h3c2 0 3 0 3-1z" class="I"></path><path d="M222 635h10c-3 1-6 1-9 1-1 0-1 0-2 1 1 1 2 0 3 1-1 1-2 0-3 1 1 1 1 1 1 2-1 1-1 2-1 3l1 1-1 1h1v1h-1v1l1 1h-1c0 1-1 1-2 2 1-2 0-5 0-7 1-2 1-5 1-8h0c1-1 1-1 2-1z" class="Z"></path><path d="M229 637h1c0 1-1 1-1 2v5l1 1h-2-2-3 0l1-1c-1 0-2 0-2-1 0-2 1-1 2-2l-1-1-1-1c3 0 5 0 7-1v-1z" class="Y"></path><path d="M222 635h-1l11-1v29h0v-7c0-2-1-4 0-6 0-1 0-2-1-3h-1c-1-2-4 0-6-2h2 2 2l-1-1v-5c0-1 1-1 1-2h-1-2c-2 0-2 1-4-1 3 0 6 0 9-1h-10z" class="R"></path><path d="M218 653c1 1 2 1 3 1 2 0 8 1 9-1h-1l-8-1h0 8l2-2c-2-1-7 0-8 0v-1c2 0 5 1 7 0v-1c-2-1-5 0-7-1h7 1c1 1 1 2 1 3-1 2 0 4 0 6h-2s1 0 1-1h-1c-2 0-9-1-11 0v7 1c0 1 0 1-1 1h-1l-2-6c1-1 2-3 3-5z" class="P"></path><path d="M217 664l1-2v-1c0-1 0-2-1-3h0c1-1 1-2 2-3h0v7 1c0 1 0 1-1 1h-1z" class="D"></path><defs><linearGradient id="L" x1="228.466" y1="630.813" x2="219.814" y2="631.664" xlink:href="#B"><stop offset="0" stop-color="#6f6f6f"></stop><stop offset="1" stop-color="#8b898a"></stop></linearGradient></defs><path fill="url(#L)" d="M231 628h1v5 1l-11 1h1c-1 0-1 0-2 1h0c0 3 0 6-1 8v-14h1l1-1c1-1 3 0 4 0h3c2 0 3 0 3-1z"></path><path d="M225 629h3l1 1h-4v-1z" class="b"></path><path d="M231 628h1v5l-4-1 2-1v-1h-1l-1-1c2 0 3 0 3-1z" class="R"></path><path d="M219 655c2-1 9 0 11 0h1c0 1-1 1-1 1h2v7h-1-2-10v-1-7z" class="I"></path><path d="M230 656h2v7h-1-2-10v-1h1c4-2 7 0 11-2h0c-4-1-7-1-10-1h-1c4 0 8 0 11-1v-1h-2-9c1-1 2-1 3-1h7z" class="B"></path><path d="M220 662c3 0 9-1 11 1h-2-10v-1h1z" class="Z"></path><path d="M186 222h0v-2l2-1 2 2 2 1 2 1 3 1-1 1c-1 0-2-1-3-1h-3c3 3 6 6 8 10l1 4c1 2 1 4 1 6l-1 2c-1 6-3 12-7 17h-1v1c-2 1-3 4-4 6l-2 1v-6c0-3 1-7 0-9 1-2 1-25 1-29-1-2 0-4 0-5z" class="G"></path><path d="M189 250v2c0 1 1 2 1 3l-1 1-2-2c1-1 1-2 2-3v-1zm-2-12v-1c0-1 1-3 1-5 1 1 2 3 3 4v-2c1 1 1 3 2 4v1s0 1-1 2v-2c0-1-1-1-1-2h-1c-1 0-2 1-3 1z" class="J"></path><path d="M189 259c0 1 0 2-1 3v1l1 1h0c1-1 2-1 2-1v1c-2 1-3 4-4 6v-1c-1-1 0-4 0-6l1-1-1-1 2-2z" class="N"></path><path d="M186 222h0v-2l2-1 2 2 2 1 2 1 3 1-1 1c-1 0-2-1-3-1h-3l-2-2-1 1 1 1c1 2 2 4 2 7v3-1h-1c0-2-2-4-3-4v-2c-1-2 0-4 0-5z" class="C"></path><path d="M187 238c1 0 2-1 3-1h1c0 1 1 1 1 2v2c1-1 1-2 1-2 0 1 1 6 1 7h-1c-1 2 0 4-1 6l-2 3c0-1-1-2-1-3v-2c0-1-1-4-1-5l-1-1v-6z" class="L"></path><path d="M190 241c-1-1-1-2-1-3v-1l1 1c1 0 1 1 2 1v2l-1 1v-1h-1z" class="P"></path><path d="M190 241h1v1 1s1 0 1 1v5h-1c0-2-1-3-1-4v-3-1z" class="c"></path><path d="M188 224l-1-1 1-1 2 2c3 3 6 6 8 10l1 4c1 2 1 4 1 6l-1 2c-1 6-3 12-7 17h-1s-1 0-2 1h0l-1-1v-1c1-1 1-2 1-3l1-1h0-3 0l2-2 1-1 2-3c1-2 0-4 1-6h1c0-1-1-6-1-7v-1c-1-1-1-3-2-4 1 0 0-1 0-1l-1-2c0-3-1-5-2-7z" class="I"></path><path d="M196 246v2h0c0 2 0 3-1 5 0 1-1 3-1 4l-2 2h0v-1-1l1-1c1-2 0-4 1-6 1-1 1-2 2-4z" class="V"></path><path d="M195 243l1-2v-1h0l1-1 1 2h0c1 1 1 2 1 2l1 1-1 2v-1c-1 1-1 3-2 3h0-1 0v-2h0v-3h-1z" class="e"></path><path d="M195 243l2-1c1 2 1 4 0 5h0v1h-1 0v-2h0v-3h-1z" class="V"></path><path d="M196 246h1v1 1h-1 0v-2h0z" class="U"></path><path d="M191 233l1-1h0l1 1c1 2 2 4 4 6h0l-1 1h0v1l-1 2h1c-2 3-2 6-4 9 1-2 0-4 1-6h1c0-1-1-6-1-7v-1c-1-1-1-3-2-4 1 0 0-1 0-1z" class="R"></path><path d="M191 233l1-1h0l1 1v5c-1-1-1-3-2-4 1 0 0-1 0-1z" class="B"></path><defs><linearGradient id="M" x1="188.981" y1="232.631" x2="198.104" y2="233.33" xlink:href="#B"><stop offset="0" stop-color="#2e2e2f"></stop><stop offset="1" stop-color="#525150"></stop></linearGradient></defs><path fill="url(#M)" d="M188 224l-1-1 1-1 2 2c3 3 6 6 8 10l1 4c1 2 1 4 1 6l-1-1s0-1-1-2h0l-1-2h0c-2-2-3-4-4-6l-1-1h0l-1 1-1-2c0-3-1-5-2-7z"></path><path d="M188 224l-1-1 1-1 2 2c3 3 6 6 8 10l1 4c1 2 1 4 1 6l-1-1s0-1-1-2v-1c0-6-5-13-10-16z" class="Y"></path><defs><linearGradient id="N" x1="287.281" y1="283.157" x2="265.848" y2="277.456" xlink:href="#B"><stop offset="0" stop-color="#9a9999"></stop><stop offset="1" stop-color="#b6b5b5"></stop></linearGradient></defs><path fill="url(#N)" d="M280 248h0c1 2 0 4 0 6v12 31c0 4 1 8 0 11v7h-1-1c0-2-1-2-2-3-1 0-1 1-2 1v1l-2-2 1-2-1-50 1-11c2 0 5 0 7-1h0z"></path><path d="M273 310c1 0 1 0 1-1h1c1 0 2 1 2 1 0 1 2 0 2 1v1l1 1v-4-1 7h-1-1c0-2-1-2-2-3-1 0-1 1-2 1v1l-2-2 1-2z" class="M"></path><path d="M276 312l1-1c1 0 1 0 2 1v3h-1c0-2-1-2-2-3z" class="U"></path><path d="M185 379c0-1 0-3 1-4 1 3 1 5 3 8 3 5 8 10 14 12h2 0c-2 2-5 3-7 5-2 1-3 3-5 5-2 4-4 7-5 11l-1 2v5 11c-1-1-1-1-2-1v1c-1 0-5-1-6-1l-1-1h0c1 0 1-1 2-1 0-2 0-2-1-3v-2h-1v-6c1-1 1-2 1-3v-6-2-2h1 2 3c1-1 0-25 0-28z" class="o"></path><path d="M185 422s0-1 1-1h0c1 3 0 6 0 8 0-2 1-4 0-6h1 0v11c-1-1-1-1-2-1v1c-1 0-5-1-6-1l-1-1h6c1 1 1 1 2 0v-3c0-1-1-1-2-2h0 1c1-1 1-3 1-4l-1-1z" class="C"></path><path d="M179 426h2 0 1 2v1h0c1 1 2 1 2 2v3c-1 1-1 1-2 0h-6 0c1 0 1-1 2-1 0-2 0-2-1-3v-2z" class="X"></path><path d="M179 428l3-1c1 1 1 3 2 4v1h-6 0c1 0 1-1 2-1 0-2 0-2-1-3z" class="N"></path><path d="M180 407h2 1c1 1 1 1 2 1 1 2 1 8 0 10h0c0 1 1 2 1 3-1 0-1 1-1 1l1 1c0 1 0 3-1 4h-1v-1h-2-1 0-2-1v-6c1-1 1-2 1-3v-6-2-2h1z" class="d"></path><path d="M179 409h1v2h-1v-2z" class="Y"></path><path d="M179 411h1v1l1 4c-1 1-2 1-2 1v-6z" class="I"></path><path d="M184 426h1l-1-1h-2v-3c-1 0-2 0-3-1h4l-1 1h0 3l1 1c0 1 0 3-1 4h-1v-1z" class="b"></path><path d="M180 407h2 1c1 1 1 1 2 1 1 2 1 8 0 10h0c0 1 1 2 1 3-1 0-1 1-1 1h-3 0l1-1h2c-1-1-2-1-3-1 0-1 1-1 2-2-2 0-3 0-5-1h5 0v-1c-1-1-1-2-1-3 1-2 0-2 0-3v-2h-2-1v-1z" class="i"></path><path d="M189 383c3 5 8 10 14 12h2 0c-2 2-5 3-7 5-2 1-3 3-5 5-1-3-4-4-5-7-1-4 0-7 0-10 0-2 0-4 1-5z" class="C"></path><defs><linearGradient id="O" x1="238.844" y1="645.499" x2="254.844" y2="639.174" xlink:href="#B"><stop offset="0" stop-color="#484748"></stop><stop offset="1" stop-color="#616060"></stop></linearGradient></defs><path fill="url(#O)" d="M243 605h5c1-1 1-1 2-1v-1 16 3 1 1 1c0 2 0 3 1 4-1 2-1 18 0 20h0c-1 1-1 2-1 3v2 11h0v1h-5 0-3 0c-1-1 0-3-1-4-1-2 0-3 0-4v-5-22h0c1-1 2-1 3-1 0-2-1-1-2-2v-1h1c1 0 1 0 1-1h-2l1-1c-1-1-1-1-1-2v-2-4-2h0 0l1-1c-1-1-1-2-1-4 0 0 1 0 1-1h0 0v-2c-1 0 0 0 0-1v-1z"></path><path d="M247 624h3v1l-3-1z" class="i"></path><path d="M245 663v-1h4v1h-4z" class="c"></path><path d="M243 627h5v1h-5l-1-1h1z" class="e"></path><path d="M245 660h4v1h-1-4l1-1zm-1-28h3c-1 1-1 1-2 1v1h3v1c-2 0-2 0-4 1v2h0l-1 1c0-2 0-4 1-5h-1v-1l1-1z" class="R"></path><path d="M242 648c1-2 0-7 0-10v-3-2h0 1v1h1c-1 1-1 3-1 5v10h5l-1 1h-4 0c0-1 0-2-1-2z" class="X"></path><path d="M250 652v2 11h0v1h-5 0-3c0-1 1-2 1-3h2 4 1v-11z" class="B"></path><path d="M245 666l-1-1 1-1c2 0 4 0 5 1h0v1h-5 0z" class="R"></path><path d="M241 631l3 1-1 1h-1 0v2 3c0 3 1 8 0 10v4h1c0 1 0 1-1 1 0 1 1 2 1 2 0 2 0 4-1 6 1 1 1 1 1 2s-1 2-1 3h0c-1-1 0-3-1-4-1-2 0-3 0-4v-5-22z" class="G"></path><path d="M243 605h5c1-1 1-1 2-1v-1 16 3 1 1h-3-3v-2-6l-2-1h0 0l1-1c-1-1-1-2-1-4 0 0 1 0 1-1h0 0v-2c-1 0 0 0 0-1v-1z" class="d"></path><path d="M250 622v1h-3v-1h3z" class="c"></path><path d="M244 610h5l-1 1h-4v-1z" class="Y"></path><path d="M243 606c1 0 5-1 6 0-1 1-2 2-2 3h-4 0v-2c-1 0 0 0 0-1z" class="N"></path><path d="M274 513c1-1 1-2 1-4 1-1 1-1 2-1-1-1-1-1-1-2h0-1v-2h2v1s1 0 1 1l2-1c0 1 0 2 1 3h0c3 24 16 46 32 64 4 4 7 9 12 12 3 3 9 3 13 6h0c2 1 4 1 6 2h-4c-1-1-1-1-2 0-1 0-1-1-1-1h-1-3c-2 2-6-4-9-4-2 0-3-2-4-3-10-8-19-19-26-29 0-1-3-4-4-5-1-4-4-7-6-10-5-8-8-17-10-27z" class="B"></path><path d="M99 85h19 0c-2 1-5 0-7 0v3c-1 0-4 0-4 1v2c3 1 9 0 13 0-3 1-8 0-10 1l2 1c5 1 10 3 14 5 1 1 2 1 3 2 2-1 3-1 4-1l1 1h0c0 1 1 2 2 2l1 1h1l-1 1 3 1c0 1 2 2 2 2 1 1 2 1 4 2 0 1 1 1 2 2h0l1 1 2 2h1c0 1 0 1-1 1s-2 0-2 1c-2-1-3-1-5-1v-1-1l-1 1c-1 0-1 0-2-1s-2-1-2-3l-1 1c-1-1-2-1-3-2 0 0-1 0-2-1-1 0-3-1-5-2 0-1-1-1-1-1h-1l-1-1-1 1c-1-1-3-2-5-2 0 0-1-1-2-1l-6-3h-2l-7-3-11-3c-4-1-8-1-12-3h1c-2-1-3-1-5-2h6v-1h1 8c1-1 1 0 2-1 3 0 5 0 7-1z" class="f"></path><path d="M96 91h0c1-1 4-2 6-2 1 0 1-1 2-1v-1c1 1 2 1 2 1v1h-3s-1 0-2 1c-1 0-3 1-5 1z" class="Q"></path><path d="M133 102l2 1c-2 0-2 0-3 1 0 1 2 1 3 2h-1c1 1 1 1 2 1h0c-2 0-3-1-4-1s-1-1-1-1c-1 0-1 0-1-1s2-2 3-2z" class="H"></path><path d="M129 100c2-1 3-1 4-1l1 1h0c0 1 1 2 2 2l1 1h1l-1 1-2-1-2-1c-1-1-3-2-4-2z" class="C"></path><path d="M125 101h1s1 0 2-1h1v1l-4 2c1 0 3 0 4 1l-1 2c0-1-1-1-1-1h-1l-1-1-1 1c-1-1-3-2-5-2v-1h1c1 1 2 0 3 1h2v-1h0v-1z" class="H"></path><path d="M100 86h1 1c-4 1-6 2-9 3-1 0-3 1-4 0h-3c-1 0-2 0-3-1 6 0 12-1 17-2z" class="k"></path><g class="Q"><path d="M111 99h0c1 0 3 0 4 1h2-1v-1h-1l-1-1h1 0c2 1 5 1 7 2l2 2v-1h1v1h0v1h-2c-1-1-2 0-3-1h-1v1s-1-1-2-1l-6-3z"></path><path d="M110 95c-1-1-2-1-4-1v-1h3c1 1 2 1 3 1v-1c5 1 10 3 14 5h-1-2l-1 1v-1c-3 0-4-1-6-1l-1 1c-1 0-3-1-5-2v-1h0z"></path></g><path d="M110 95c2 0 4 1 6 2l-1 1c-1 0-3-1-5-2v-1h0z" class="k"></path><path d="M101 90l1 1h-2v1c1 1 5 0 7 0h3l2 1v1c-1 0-2 0-3-1h-3v1c2 0 3 0 4 1h0v1s-1 0-1 1h0c-1-1-1-1-2-1h-2c-1 0-2 0-3-1h-2l-4-1h0v-1c1 0 1 0 2-1h-2v-1c2 0 4-1 5-1z" class="H"></path><path d="M142 107c1 1 2 1 4 2 0 1 1 1 2 2h0l1 1 2 2h1c0 1 0 1-1 1s-2 0-2 1c-2-1-3-1-5-1v-1-1l-1 1c-1 0-1 0-2-1s-2-1-2-3l-1 1c-1-1-2-1-3-2h2s0 1 1 1c0 0 1-1 2-1h0l-1 2h1c1 0 1 1 1 1l2-1h-1v-1l2-1c-1-1-2-1-2-2z" class="Q"></path><path d="M143 111l1-1c0 1 1 1 2 2h1v1c-2 0-4 0-6-1l2-1z" class="H"></path><path d="M144 114c1-1 3 0 4-1h0c1 0 2 1 3 1h1c0 1 0 1-1 1s-2 0-2 1c-2-1-3-1-5-1v-1z" class="f"></path><path d="M274 391h7v37c0 5-1 12 0 16 0 2 1 3 2 4l2 7v1c-1-4-3-8-4-12l-1 25c1 1 1 1 2 1l-2 1h-4c-1-1-2-1-3-1l-1-1h1v-18-9-46h0v8h0c1-2 1-3 1-4 0-3 1-7 0-9z" class="e"></path><path d="M273 469v-8c0-1 0-2 1-2h0v4 6h-1z" class="Y"></path><path d="M273 470l-1-1h1 0c2 1 5 0 7 0 1 1 1 1 2 1l-2 1h-4c-1-1-2-1-3-1z" class="E"></path><path d="M187 365c3 0 6 0 9 1 1 0 3 1 5 1h3c1 0 1 1 2 0h0v-1c1 1 2 1 3 2h1l2 2h0l2 1c1 0 1 1 1 1v8l-1 2 1 1v7l-1 1c-2 0-2 0-4 1v1l-3 2h-2 0-2c-6-2-11-7-14-12-2-3-2-5-3-8v-4-3-2l-1-1h2z" class="o"></path><path d="M201 367h3c1 0 1 1 2 0h0v-1c1 1 2 1 3 2h1l2 2-11-3z" class="f"></path><path d="M196 380l3-3h1v3c1 0 1 0 2 1h-4l-2-1z" class="W"></path><path d="M211 381h-1v-2h0c0-1 0-1 1-2 1 1 0 1 0 3h4l-1 2 1 1h-2c-1 0-2-1-2-2z" class="C"></path><path d="M211 381c1-1 1-1 2 0l1 1 1 1h-2c-1 0-2-1-2-2z" class="F"></path><path d="M204 381c-2-3-1-5 0-9 0 5 0 8 3 12 1 1 2 1 2 2h2c-1 1-2 1-3 1-2-2-3-4-4-6z" class="S"></path><path d="M202 381h2c1 2 2 4 4 6 1 0 2 0 3-1v1l-3 3s-1 0-1-1c-3-2-6-5-9-8h4z" class="C"></path><path d="M213 383h2v7l-1 1c-2 0-2 0-4 1v1l-3 2h-2 0l1-1v-1h-2l2-1c1 0 1 0 1-1l-1-1 1-1c0 1 1 1 1 1l3-3h0l2-2 1-1-1-1z" class="B"></path><path d="M213 383h2v7l-1 1c-2 0-2 0-4 1v1l-3 2-1-1c1 0 1 0 1-1 2-1 5-3 6-5v-1h-2l2-2 1-1-1-1z" class="C"></path><path d="M206 390c-3-1-5-2-7-4-6-4-8-9-9-17 1 2 1 3 2 5s3 4 4 6l2 1c3 3 6 6 9 8l-1 1z" class="R"></path><path d="M186 375v-4c3 9 9 17 17 22h1 2v1l-1 1h-2c-6-2-11-7-14-12-2-3-2-5-3-8z" class="b"></path><path d="M353 262l3-12v84l19 1c5 0 9 0 13 1h-32v4c1 1 1 1 2 1h-2 0l-3-1h-4 0-1v-4-1h0c-2 1-3 1-5 1h0s0-1 1-1c1-1 2-3 3-5h1c0-1 1-2 0-3h-1l-1-1c-4 4-10 3-16 4h-2l1-2h1 2c1-1 0-1 1-1 2-2 5-1 7-2 0-1 1-2 2-3 1-2 3-3 4-4v-1-1h2c0-2 0-2-1-3 0-1-1-3-2-4-1-2-2-2-2-3-1-1-2-1-3-2l4-3c0-1 0-1-1-2l1-1h1c0 1 2 2 2 3h0l1 1 1-2v-6c0-5 1-10 2-16l-1 1 1-4 1-3v-3l-1-2 2-5z" class="V"></path><path d="M351 267l2-5v3c0 1 0 3-1 4l-1-2z" class="j"></path><path d="M352 272v3c0 1 0 3-1 3l-1 1 1-4 1-3zm-3 22v1c1 7 0 14 0 21 0 3 0 7 1 10v2c-1 1-1 1 0 2 0 1-1 3-1 4h2c1 1 1 1 0 2 0 0-1 0-2 1v2h1 0c-1 0-1-1-1-1 1-1 2 0 3-1v-1-3h1 0 0v-2c1 1 1 2 1 3l1 1c-2 2-2 3-2 5h-4 0-1v-4-1h0c-2 1-3 1-5 1h0s0-1 1-1c1-1 2-3 3-5h1c0-1 1-2 0-3h-1l-1-1s2-2 2-3v-8h1v-15-6z" class="Z"></path><path d="M346 326s2-2 2-3v-8h1v20c-1-1-1-3-1-5 0-1 1-2 0-3h-1l-1-1z" class="B"></path><path d="M347 330h1c0 2 0 4 1 5v5h-1v-4-1h0c-2 1-3 1-5 1h0s0-1 1-1c1-1 2-3 3-5z" class="K"></path><path d="M340 304l4-3c0-1 0-1-1-2l1-1h1c0 1 2 2 2 3h0l1 1 1-2v15h-1v8c0 1-2 3-2 3-4 4-10 3-16 4h-2l1-2h1 2c1-1 0-1 1-1 2-2 5-1 7-2 0-1 1-2 2-3 1-2 3-3 4-4v-1-1h2c0-2 0-2-1-3 0-1-1-3-2-4-1-2-2-2-2-3-1-1-2-1-3-2z" class="C"></path><path d="M346 318c0 3 0 5-3 7-2 2-8 3-11 3 1-1 0-1 1-1 2-2 5-1 7-2 0-1 1-2 2-3 1-2 3-3 4-4z" class="L"></path><defs><linearGradient id="P" x1="197.302" y1="367.808" x2="197.81" y2="350.041" xlink:href="#B"><stop offset="0" stop-color="#bbb9ba"></stop><stop offset="1" stop-color="#e6e6e6"></stop></linearGradient></defs><path fill="url(#P)" d="M218 343h2v2c1 3 1 7 2 10l1 1h3l1 1h3v2c1 1 1 2 2 3l3 5h0v2h-5c-2 0-7 1-9 0l-2 1v1l-1 1-1 4h-1l-1-3v-1s0-1-1-1l-2-1h0l-2-2h-1c-1-1-2-1-3-2v1h0c-1 1-1 0-2 0h-3c-2 0-4-1-5-1-3-1-6-1-9-1-1-1-2-1-4-1h-3c-3-1-11-1-14 0h-1 0c-1-1-6-1-7-1 2 0 4 0 5-1h1v-1-4h0c-1-1-1-2-1-3v-2h1c1-1 1-2 2-2 2 0 5 1 7 0v-1h3c1 0 1 0 1-1h5 8c2 0 3 1 4 1h2v-1h1c1 0 3 1 4 1 2 0 4 0 5 1l1-1h0l1-1c2 0 5-3 6-5h4z"></path><path d="M208 359h2c1 1 1 0 2 0v1h6-2c-1 0-2 0-3 1-1 0-2 1-3 1s-2 0-2-1v-2zm10-16h2v2c1 3 1 7 2 10l1 1h3l1 1h0s-1 0-1 1h0c-2 0-4 0-6-1v-3h-3v-1c1 0 1-1 2-1-3 0-7 0-9-1v-2 1l-1 1-2-2 1-1c2 0 5-3 6-5h4z" class="k"></path><path d="M218 343h2v2c-1 2-3 1-5 2-1 0-3 1-5 2v1l-1 1-2-2 1-1c2 0 5-3 6-5h4z" class="p"></path><path d="M166 364v-1l1-1c2 0 4-1 6 0h6c1-1 0-2 1-4v-2h1v1l1 1c0 2-1 2-1 4 1 1 2 0 4 0 1 1 2 1 3 1v-7h1c1 0 1 0 1 1v3l-1 3c1 1 4 0 5 0h2c1 1 3 0 5 1h2l1-1s1 0 1-1l1 1v3 1h0c-1 1-1 0-2 0h-3c-2 0-4-1-5-1-3-1-6-1-9-1-1-1-2-1-4-1h-3c-3-1-11-1-14 0zm66-2l3 5h0v2h-5c-2 0-7 1-9 0l-2 1v1l-1 1-1 4h-1l-1-3v-1s0-1-1-1l-2-1h0l-2-2v-1l1 1 1-1v-1h1c0 1 1 2 1 3l1-1v-1c1 0 2-1 2-2v-2l1 1c3-1 7 0 10-1h3l1-1z" class="H"></path><path d="M228 366c1-1 2-1 3 0h0v1h-3v-1z" class="h"></path><path d="M212 370h7v1l-1 1-1 4h-1l-1-3v-1s0-1-1-1l-2-1z" class="F"></path><path d="M232 362l3 5h0v2h-5l2-1h0c0-1 0-1-1-2h0c-1-1-2-1-3 0-2 0-5-1-7-1s-3 1-4 0v-2l1 1c3-1 7 0 10-1h3l1-1z" class="Q"></path><path d="M177 348h5 8c2 0 3 1 4 1h2v-1h1c1 0 3 1 4 1 2 0 4 0 5 1-2 1-3 2-5 3-6 1-13 0-19 0h-18c0 3 1 6 0 8v-4h0c-1-1-1-2-1-3v-2h1c1-1 1-2 2-2 2 0 5 1 7 0v-1h3c1 0 1 0 1-1z" class="R"></path><path d="M177 348h5 8c2 0 3 1 4 1s3 0 4 1h-1-14c-3 0-6 1-10 0h0 0v-1h3c1 0 1 0 1-1z" class="Q"></path><path d="M333 309l6-6 1 1h0c1 1 2 1 3 2 0 1 1 1 2 3 1 1 2 3 2 4 1 1 1 1 1 3h-2v1 1c-1 1-3 2-4 4-1 1-2 2-2 3-2 1-5 0-7 2-1 0 0 0-1 1h-2-1l-1 2h-5 0l-2-1-1 1c-6 4-13 7-19 8h-1 1c0-1 1-1 1-1h0 0 0v-1h-1c-2 1-5 2-8 3-4 0-7 1-11 1-5 0-11 2-15 0-2 0-4 0-5-1h0-4c-2 0-2-1-4-2l-3-3v-1c1 0 2-1 3-1v2h0 2c1-1 4-3 5-4 4-5 7-9 10-15l1 1h1l1 1h6v8h1 5 1l1 1 1 1s1 0 1 1h3l-1 1c1 0 2 0 2 1h0 1v-1c1 0 2 0 3 1l6-3c11-4 20-10 29-18z" class="g"></path><path d="M292 329c1 0 2 0 2 1h0 1v-1c1 0 2 0 3 1-3 0-6 1-9 2 1-2 2-2 3-3z" class="M"></path><path d="M325 322l1 1c-1 2-2 3-4 4h0c-1 2-5 3-7 4l-2-1 6-3c1-1 2-1 3-2l3-3z" class="B"></path><path d="M325 322l2-1c2-3 4-5 6-7 0 3-2 5-4 7l4-4v2c0 1-1 2-2 2h0c-2 1-3 2-4 3s-3 3-5 3h0c2-1 3-2 4-4l-1-1z" class="E"></path><path d="M271 315l1 1h1l-1 6c-1 4-2 6-5 8-1 0-2 1-3 1 1-2 5-3 6-5v-1c1-1 1-3 1-4v-1c0-1 0-2 1-3h0 0l-3 3v3c0 1-1 2-1 2-1 0-1-1-2 0-1 2-2 4-4 5h-1c4-5 7-9 10-15z" class="F"></path><path d="M286 325h1l1 1 1 1s1 0 1 1h3l-1 1c-1 1-2 1-3 3h-1c-3 0-6 1-9 1l-15-1h0 5 4c1-1 3-1 4-1s2 1 2 0h1v1l1-1c2 0 2 0 3-1h2s1 0 1-1c-1-1-1-1-1-2h0v-2z" class="N"></path><path d="M288 326l1 1s1 0 1 1h3l-1 1c-1 1-2 1-3 3h-1c-1-2 0-4 0-6z" class="U"></path><path d="M333 309l6-6 1 1h0c1 1 2 1 3 2 0 1 1 1 2 3 1 1 2 3 2 4 1 1 1 1 1 3h-2v1c0-1-1-2-1-3-1-1-2-2-2-3l-1-1h-1c-2 1-5 5-6 7l-2 2v-2h0l2-2c1-2 4-6 7-6v-1h0 0c0-1-1-2-3-3-1 2-3 4-6 4h0z" class="F"></path><path d="M322 327c2 0 4-2 5-3s2-2 4-3l-6 6-4 2-1 1c-6 4-13 7-19 8h-1 1c0-1 1-1 1-1h0 0 0v-1h-1c1-1 1-1 1-2h2l2-1c1 0 2 0 2-1 1-1 2-1 2-1 1 0 2-1 3-1l2 1c2-1 6-2 7-4z" class="O"></path><path d="M302 334h2 2v1c-1 0-3 0-4 1h-1c1-1 1-1 1-2z" class="M"></path><path d="M310 331c1 0 2-1 3-1l2 1c-3 1-6 2-9 2 1 0 2 0 2-1 1-1 2-1 2-1z" class="X"></path><path d="M333 319l2-2c1-2 4-6 6-7h1l1 1c0 1 1 2 2 3 0 1 1 2 1 3v1c-1 1-3 2-4 4-1 1-2 2-2 3-2 1-5 0-7 2-1 0 0 0-1 1h-2-1l-1 2h-5 0l-2-1 4-2 6-6h0c1 0 2-1 2-2z" class="D"></path><path d="M325 327h4c1 0 2-2 4-3v1l-3 3h-1l-1 2h-5 0l-2-1 4-2z" class="T"></path><path d="M343 311c0 1 1 2 2 3 0 1 1 2 1 3v1c-1 1-3 2-4 4-1 1-2 2-2 3-2 1-5 0-7 2-1 0 0 0-1 1h-2l3-3 1-1c2-2 3-5 5-8 1-2 2-4 4-5z" class="K"></path><path d="M341 319c0 1 0 1-1 2 0 1-1 1-1 1h0c0-2 1-2 2-3z" class="J"></path><path d="M343 311c0 1 1 2 2 3-2 1-3 3-4 5-1 1-2 1-2 3h0c-1 2-3 2-4 3-1 0-1 0-1-1 2-2 3-5 5-8 1-2 2-4 4-5z" class="B"></path><path d="M273 316l1 1h6v8h1 5v2h0c0 1 0 1 1 2 0 1-1 1-1 1h-2c-1 1-1 1-3 1l-1 1v-1h-1c0 1-1 0-2 0s-3 0-4 1h-4-5v-1c1 0 2-1 3-1 3-2 4-4 5-8l1-6z" class="M"></path><path d="M273 316l1 1v6h-1l-1-1 1-6z" class="j"></path><path d="M281 325h5v2h-1c-1 0-2 0-2 1h-3l2-2-1-1z" class="H"></path><path d="M273 323h1l-1 1c0 1 1 1 1 2s-1 3-2 4h-2l-1 2h-5v-1c1 0 2-1 3-1h0c1 0 2 0 3-1h1c1 0 2-1 3-2l-1-1v-3h0z" class="U"></path><path d="M272 322l1 1h0v3l1 1c-1 1-2 2-3 2h-1c-1 1-2 1-3 1h0c3-2 4-4 5-8z" class="a"></path><path d="M256 334c-1 0-2 1-3 1h-1c2 1 3 1 5 2l2-2c2-1 15 1 18 1 11-1 22-3 33-5 0 0-1 0-2 1 0 1-1 1-2 1l-2 1h-2c0 1 0 1-1 2-2 1-5 2-8 3-4 0-7 1-11 1-5 0-11 2-15 0-2 0-4 0-5-1h0-4c-2 0-2-1-4-2l-3-3v-1c1 0 2-1 3-1v2h0 2z" class="D"></path><defs><linearGradient id="Q" x1="282.328" y1="336.633" x2="282.72" y2="339.391" xlink:href="#B"><stop offset="0" stop-color="#908f90"></stop><stop offset="1" stop-color="#adabac"></stop></linearGradient></defs><path fill="url(#Q)" d="M302 334c0 1 0 1-1 2-2 1-5 2-8 3-4 0-7 1-11 1-5 0-11 2-15 0-2 0-4 0-5-1h0l4-1h-7s-2 0-2-1h3c3 0 7 1 10 1h7c8 0 17-2 25-4z"></path><defs><linearGradient id="R" x1="304.343" y1="436.55" x2="333.202" y2="374.747" xlink:href="#B"><stop offset="0" stop-color="#444"></stop><stop offset="1" stop-color="#636263"></stop></linearGradient></defs><path fill="url(#R)" d="M273 396v-26c10 0 21 3 31 7 8 4 15 10 22 16l10 12 6 8c1 1 1 3 2 4 2 3 3 7 5 10 1 1 1 0 2 0v-1c-1 0-1 1-2 1v-4-1l-1-28v-4-9-1l1 3h1v6h1v2c1-1 1-2 1-4v-2h0v-1h3 9-8v15 4h10 4l-14 1v62c-1-2-1-4-2-7l-5-17c-9-25-25-48-50-59-6-2-12-4-18-5v11c1 1 4 1 5 1h0c-2 0-4 0-5 1h-7c1 2 0 6 0 9 0 1 0 2-1 4h0v-8h0z"></path><path d="M350 389h1v2l-1 1v-3z" class="X"></path><path d="M274 389h6v1c-1 1-4 1-6 0v-1z" class="K"></path><path d="M351 426v-1c1 0 1 1 1 1 1 1 1 2 1 4h0s-1 0-1 1c0-2 0-3-1-4v-1z" class="B"></path><path d="M273 383v-11l1 1v7 1c1 2 0 5 0 6v-5l-1 1z" class="I"></path><path d="M273 383l1-1v5 2 2c1 2 0 6 0 9 0 1 0 2-1 4h0v-8-13z" class="Z"></path><path d="M350 421v-6l1-1v-2h1v1c1 0 1 0 1 1v2c0 1-1 1-1 1 0 1 0 2-1 3l1 1h0c0 1 0 2 1 3 0 1 0 1-1 2 0 0 0-1-1-1v1c-1 0-1 1-2 1v-4-1l1-1z" class="P"></path><path d="M350 421v5l-1-3v-1l1-1z" class="R"></path><path d="M218 558v-1-1h1v5 1 6 4 1 5 7h0v1 1h1c1-1 2-1 3-1h2v1c2 0 3 0 5 1v-2c0-2 0-3 2-4v10 15 1 1 4 5 10h-1c0 1-1 1-3 1h-3c-1 0-3-1-4 0l-1 1h-1v-1-2-2-3-1-1-13h0c-1 0-1-1-1-1h-1v-2h0-3c0 1-1 1-2 2l-8 11h0l-4 5v-1c-1 0-2-1-2-1-3-2-6-4-8-7l-2-1v1l-1-1-1-1c5-4 9-8 12-14 2-2 4-5 6-8l5-9 1-5c1-2 2-3 2-5l1-1c0-2 2-3 3-4 1-2 2-4 2-7z" class="M"></path><path d="M203 614h1c-1 1-1 2-2 3v-2-1h1z" class="c"></path><path d="M231 606l1 1v1h-9v-1c3 0 5 0 8-1h0z" class="K"></path><path d="M213 602c1-2 2-3 3-5l1 1c-1 1-1 2-2 3l-1 1h-1z" class="e"></path><path d="M216 597h0 0c2-3 2-5 3-8v5c-1 1-1 2-1 3l-1 1h0l-1-1zm-14 11l2 2c1 0 2 0 2-1l-2 5h-1-1c1-2 0-3 0-4v-2z" class="L"></path><path d="M231 614l1-1v5h-1l-8-1h5c1 0 3 0 4-1-3 0-5-1-8-1h-1 8v-1z" class="B"></path><path d="M232 609v4l-1 1-1-1c-2-1-6 0-8-1 2 0 5 1 7 0 1 0 1 0 2-1v-1h-11v-1h12z" class="K"></path><path d="M213 602h1v2h0c0 1-1 1-2 2l-8 11h0l9-15z" class="X"></path><path d="M219 594v6 7h0c-1 0-1-1-1-1h-1v-2h0-3 0v-2l1-1c1-1 1-2 2-3h0l1-1c0-1 0-2 1-3z" class="S"></path><path d="M217 598v4c-1 0-1-1-2-1 1-1 1-2 2-3h0z" class="B"></path><path d="M214 602l1-1c1 0 1 1 2 1 0 1 1 1 0 2h0-3 0v-2z" class="L"></path><path d="M226 592c2 0 3-1 5-1l1 1v15l-1-1v-1h-4-2-1c3-1 6-1 9-2h-8l-1-1h5c1 0 2 0 3-1h-1c-1 0-1-1-2-1v-1h2v-1h-2v-1l2-1h0v-1c-1 0-2 0-2-1h-1l-1-1h0c-2 0-2 0-3-1h2z" class="P"></path><path d="M224 592h2 0c1 1 4 0 5 1-1 1-2 1-3 1l-1-1h0c-2 0-2 0-3-1z" class="V"></path><path d="M227 593l1 1h1c0 1 1 1 2 1v1h0l-2 1v1h2v1h-2v1c1 0 1 1 2 1h1c-1 1-2 1-3 1h-5c1-1 1-4 1-5v-1-1-1c1 0 1 0 2-1z" class="O"></path><path d="M232 582v10l-1-1c-2 0-3 1-5 1h-2-1-3v2h0s0 1-1 1v-8h1c1-1 2-1 3-1h2v1c2 0 3 0 5 1v-2c0-2 0-3 2-4z" class="j"></path><path d="M223 586h2v1h-5c1-1 2-1 3-1z" class="Z"></path><path d="M232 582v10l-1-1h0c-2-1-6-1-8-2h3c0-1 0-1-1-2 2 0 3 0 5 1v-2c0-2 0-3 2-4z" class="c"></path><path d="M225 587c2 0 3 0 5 1v1h-4c0-1 0-1-1-2z" class="M"></path><path d="M231 618h1v10h-1c0 1-1 1-3 1h-3c-1 0-3-1-4 0l-1 1h-1v-1-2-2-3-1h1c3 1 8 1 11 0v-1h-11v-1h10c1 0 1 0 1-1z" class="L"></path><path d="M219 621h1c0 1 1 1 1 1 3 0 6 0 9 1h1l-1 1h-5 0c1 1 2 0 4 1h-10v-3-1z" class="Z"></path><defs><linearGradient id="S" x1="225.897" y1="629.735" x2="222.092" y2="623.748" xlink:href="#B"><stop offset="0" stop-color="#807f7f"></stop><stop offset="1" stop-color="#9a999a"></stop></linearGradient></defs><path fill="url(#S)" d="M229 625s1 0 2 1c-1 1-6 0-7 1l7 1c0 1-1 1-3 1h-3c-1 0-3-1-4 0l-1 1h-1v-1-2-2h10z"></path><path d="M218 558v-1-1h1v5 1 6 4 1 5 7h0v1 1c0 1-2 3-2 4l-6 11-2 4c-2-1-3-2-5-3l4 3c0 1-1 2-2 3 0 1-1 1-2 1l-2-2v2c0 1 1 2 0 4h1-1v1 2l-2 4c-1 0-2-1-2-1-3-2-6-4-8-7l-2-1v1l-1-1-1-1c5-4 9-8 12-14 2-2 4-5 6-8l5-9 1-5c1-2 2-3 2-5l1-1c0-2 2-3 3-4 1-2 2-4 2-7z" class="W"></path><path d="M200 604c1 0 2 0 3 1s1 1 0 2c-1 0-2-2-3-2v-1z" class="E"></path><path d="M212 597h-2 0v2h-1l-1-1c-1 1 1 2 1 3 0 0-1 0-1 1h0c-1-1-2-3-3-5 0-2 0-3 2-4 2 0 2 3 4 3h0 0c0-1-1-2-1-2 1 0 2 0 2 1v1 1z" class="S"></path><path d="M188 612c1 0 1 0 2-1 1 0 1-1 2-2 1 1 1 1 2 1v-1h2v-1c1 1 0 1 1 0v-2c2 0 4 1 5 2v2c0 1 1 2 0 4h1-1v1 2l-2 4c-1 0-2-1-2-1-3-2-6-4-8-7l-2-1z" class="E"></path><path d="M195 612v-2c1 0 2 0 3 1s3 2 4 3h1-1v1 2c-2 0-5-2-6-3l-1-2z" class="B"></path><path d="M198 611c1 1 3 2 4 3h1-1v1c-2 0-4-2-5-3l1-1z" class="i"></path><path d="M190 613l2-1v-1-1l3 2 1 2c1 1 4 3 6 3l-2 4c-1 0-2-1-2-1-3-2-6-4-8-7z" class="e"></path><path d="M198 620v-1c0-1-1-1-1-2s-1-2-2-3h1c1 1 4 3 6 3l-2 4c-1 0-2-1-2-1z" class="N"></path><path d="M218 558v-1-1h1v5 1 6 4 1 5 7h0v1 1c0 1-2 3-2 4l-6 11c0-2 0-3 1-5v-1-1c0-1-1-1-2-1 0 0 1 1 1 2h0 0c-2 0-2-3-4-3v-2l1-1v-1-1c-1-1-1-1-2-1l-2 2h0l5-9 1-5c1-2 2-3 2-5l1-1c0-2 2-3 3-4 1-2 2-4 2-7z" class="B"></path><path d="M208 590l1 2v1l-2-2 1-1z" class="K"></path><path d="M216 587c0 1-1 1-1 2-1 1 0 1-1 2h0c-1-2-2-3-3-4v-1h1c0 1 1 2 2 3l1-1-1-3 1-1c0-1 0-2 1-4h1c0 1 0 1-1 2l2 3h1 0v1 1h-3z" class="R"></path><path d="M216 587v-2-1c1 0 1 1 2 1h0 1 0v1 1h-3z" class="X"></path><path d="M218 558v-1-1h1v5 1 6 4 1h0l-1-1c-2 0-2 1-3 2l1 1c-1 2-2 3-3 4-2 3-2 4-3 8-1 0-1 0-2-1v1l1 1c0 1 0 1-1 2v-1-1c-1-1-1-1-2-1l-2 2h0l5-9 1-5c1-2 2-3 2-5l1-1c0-2 2-3 3-4 1-2 2-4 2-7z" class="G"></path><path d="M218 558v-1-1h1v5 1c0 1-1 2-1 3h-1v2l-1 2c-1 0-1 0-1 1v-1l1-4c1-2 2-4 2-7z" class="C"></path><path d="M212 570l1-1c0-2 2-3 3-4l-1 4c-1 1-2 3-2 4-2 2-3 5-4 7l1-5c1-2 2-3 2-5z" class="I"></path><path d="M213 573h0c1-1 2-2 4-3h1v1c-3 2-3 3-5 6 0 0-1 1-1 2-1 1-1 2-2 3s-1 1-2 3h0v1 1l1 1c0 1 0 1-1 2v-1-1c-1-1-1-1-2-1l-2 2h0l5-9c1-2 2-5 4-7z" class="S"></path><path d="M214 520c-1-1 0-2 0-3h0c2-1 3 0 4 0h5c1-1 2 0 3 0s2 0 3 1h0v2h0c1 0 1 1 1 1l-1 1c1 0 1 0 1 1h1v9l1 2v9 4 1h-2v-1l-2-1c-1 1-4 0-6 0l-1 1v1l-1 1-1-1v2c2 1 4 0 6 0l1 2h-7 0v3 1h-1v1 1c0 3-1 5-2 7-1 1-3 2-3 4l-1 1c0 2-1 3-2 5l-1 5-5 9c-2 3-4 6-6 8-2 1-3 3-5 5s-8 7-11 7l3-3 1-2v-1l-7-12-2-4c0-1 0-1-1-2v-1c0-1 2-2 2-3l6-7c2-3 3-5 5-7l1-1h1c1-1 1-2 2-3 0-1 1-1 2-2h1c2-3 4-7 6-10 0-1 2-5 3-6l2-3c3-6 4-12 6-18 0-1 1-3 1-4z" class="o"></path><path d="M214 520c-1-1 0-2 0-3h0l2 3c0 1 1 2 1 2h0l2 2h-1c-1 1-1 2-1 4-1 2-1 4-2 6-1-2 0-5-1-7v-1-1l-1-1c0-1 1-3 1-4z" class="O"></path><path d="M214 520c-1-1 0-2 0-3h0l2 3c0 1 1 2 1 2-1 1-1 2-2 2-1-1 0-2 0-4h-1z" class="B"></path><path d="M219 527h11v1h0c-2-1-8 0-11 0v7 1 2 10l-1-1-1-1c0-2-2-2-3-4l1-1c0 1 0 1 1 1l-1-1v-1c2-2 2-3 2-5 1-1 1-2 2-3v-5z" class="e"></path><path d="M230 523h1v9h-1v-1-1-2h0v-1h-11v-3h6c1-1 3-1 5-1z" class="Q"></path><path d="M230 523h1v9h-1v-1-1-2h0v-1-1c-1-1-3 0-5-1 2 0 4 1 5 0v-1h-5c1-1 3-1 5-1z" class="S"></path><defs><linearGradient id="T" x1="213.758" y1="540.286" x2="209.48" y2="536.493" xlink:href="#B"><stop offset="0" stop-color="#727273"></stop><stop offset="1" stop-color="#8a8a88"></stop></linearGradient></defs><path fill="url(#T)" d="M209 543c0-2 1-4 2-6 1-4 2-7 3-11v1c1 2 0 5 1 7l-3 9c-2 3-3 6-5 9 0-1 1-2 1-3h-1c2-2 2-4 2-6z"></path><path d="M213 524l1 1v1c-1 4-2 7-3 11-1 2-2 4-2 6-2 4-4 10-7 14h0c-1 0-1 1-2 2 0 2-1 2-1 3h-1c1-1 1-2 2-3s1-2 1-3v-1c1-1 1-2 1-3v-1c0-1 2-5 3-6l2-3c3-6 4-12 6-18z" class="a"></path><path d="M218 517h5c1-1 2 0 3 0s2 0 3 1h0v2h0c1 0 1 1 1 1l-1 1c1 0 1 0 1 1-2 0-4 0-5 1h-6l-2-2h0s-1-1-1-2l-2-3c2-1 3 0 4 0z" class="U"></path><path d="M219 517c2 0 3 1 5 1h0l-1 1h-3l1-1h0-2v-1z" class="Q"></path><path d="M214 517c2-1 3 0 4 0h1v1h2 0l-1 1v1c1 1 5-1 5 1h-5c-1 1-2 0-2 0l-1 1h0s-1-1-1-2l-2-3z" class="H"></path><path d="M214 517c2-1 3 0 4 0h1v1c-1 0-1 0-2 1l-1 1-2-3z" class="f"></path><path d="M219 536v-1-7c3 0 9-1 11 0v2 1 1h1l1 2v9 4 1h-2v-1l-2-1c-1 1-4 0-6 0l-1 1v1l-1 1-1-1v2-2-10-2z" class="M"></path><path d="M223 534h4v2h1c0 1 0 1-1 1h0s-1 0-1 1c0 0-1 0-2 1-1 0-1-1-2-1h-3v-2c1 0 1 1 2 1s3-1 4-1h0 1v-1l-3-1z" class="U"></path><defs><linearGradient id="U" x1="227.302" y1="534.431" x2="234.022" y2="543.855" xlink:href="#B"><stop offset="0" stop-color="#333235"></stop><stop offset="1" stop-color="#565855"></stop></linearGradient></defs><path fill="url(#U)" d="M230 532h1l1 2v9 4 1h-2v-1l-2-1h1c0-1 1-1 1-1v-2c-1-1 0-1 0-3v-8z"></path><path d="M219 538h3c1 0 1 1 2 1h3v1c-2 0-6 0-6 1h3 2v1c-2 0-4 0-5 1h0 5l1 1c-2 1-3 0-5 1h-1l-1 1h2l-1 1v1l-1 1-1-1v2-2-10z" class="Q"></path><path d="M219 536v-1-7c3 0 9-1 11 0v2 1h-5 0c-1 0-3 0-4 1h5c1 0 1 0 2 1h0c-2 0-5 0-7 1h0 2l3 1v1h-1 0c-1 0-3 1-4 1s-1-1-2-1z" class="H"></path><path d="M230 530v1h-5-4l-1-1h3 7z" class="a"></path><defs><linearGradient id="V" x1="202.78" y1="550.104" x2="210.012" y2="559.376" xlink:href="#B"><stop offset="0" stop-color="#120f12"></stop><stop offset="1" stop-color="#262927"></stop></linearGradient></defs><path fill="url(#V)" d="M207 552c2-3 3-6 5-9h0 1v1c0-1 0-1 1-1l3 3 1 1 1 1v2c2 1 4 0 6 0l1 2h-7 0v3 1h-1v1 1c0 3-1 5-2 7-1 1-3 2-3 4l-1 1v-3h-1-1v1c-1 0-1 0-2 1l-2-3h0l-1-1h-2c-1 1-1 3-3 3v-2h-1c3-5 7-10 8-14z"></path><path d="M203 565c1-1 1-2 2-2l2 2h-2-2z" class="Z"></path><path d="M207 565l3 3c-1 0-1 0-2 1l-2-3h0l-1-1h2z" class="I"></path><path d="M214 543l3 3 1 1 1 1v2c2 1 4 0 6 0l1 2h-7 0-1v-2h-1v-1h-1c-2-1-3-2-4-4 2 1 3 1 4 3h0c1 0 1 0 1-1-2-1-2-2-3-3v-1zm-6 13c1 1 2 2 2 3 0 2 2 2 2 3-1 1-1 3-1 4l-6-4c2-2 2-4 3-6z" class="Y"></path><path d="M211 567c0-1 1-2 1-3 2-3 5-7 5-10 0-1 0-2 1-2v2 4c0 3-1 5-2 7-1 1-3 2-3 4l-1 1v-3h-1z" class="M"></path><path d="M208 556c1 0 1 0 1-1 1-1 2-4 3-5l2 2h0c1 1 1 2 2 2-1 2-2 3-2 5-2-1-3-3-5-3 1 1 2 3 4 4 0 1 0 2-1 2 0-1-2-1-2-3 0-1-1-2-2-3z" class="O"></path><path d="M209 543c0 2 0 4-2 6h1c0 1-1 2-1 3-1 4-5 9-8 14h1v2 3c-1 0-2 1-3 2v2c-1 1-1 1-2 1l-1-1-1 1-1-1-4 6c-1 1-2 2-2 3-2 2-5 6-7 7l-2-4c0-1 0-1-1-2v-1c0-1 2-2 2-3l6-7c2-3 3-5 5-7l1-1h1c1-1 1-2 2-3 0-1 1-1 2-2h1c2-3 4-7 6-10v1c0 1 0 2-1 3v1c0 1 0 2-1 3s-1 2-2 3h1c0-1 1-1 1-3 1-1 1-2 2-2h0c3-4 5-10 7-14z" class="Q"></path><defs><linearGradient id="W" x1="178.091" y1="572.244" x2="193.872" y2="573.708" xlink:href="#B"><stop offset="0" stop-color="#342d31"></stop><stop offset="1" stop-color="#535856"></stop></linearGradient></defs><path fill="url(#W)" d="M190 566h1c1-1 1-2 2-3 0-1 1-1 2-2h1l-18 25-1 1c0-1 0-1-1-2v-1c0-1 2-2 2-3l6-7c2-3 3-5 5-7l1-1z"></path><path d="M209 543c0 2 0 4-2 6h1c0 1-1 2-1 3-1 4-5 9-8 14h1v2 3c-1 0-2 1-3 2v2c-1 1-1 1-2 1l-1-1-1 1-1-1-4 6c-1 1-2 2-2 3-2 2-5 6-7 7l-2-4 1-1 1 1c2-3 5-5 7-8 3-2 5-5 7-8 4-4 7-9 9-14 3-4 5-10 7-14z" class="I"></path><path d="M199 566h1v2 3c-1 0-2 1-3 2v2c-1 1-1 1-2 1l-1-1-1 1-1-1 7-9z" class="F"></path><path d="M200 568c2 0 2-2 3-3h2l1 1h0l2 3c1-1 1-1 2-1v-1h1 1v3c0 2-1 3-2 5l-1 5-5 9c-2 3-4 6-6 8-2 1-3 3-5 5s-8 7-11 7l3-3 1-2v-1l-7-12c2-1 5-5 7-7 0-1 1-2 2-3l4-6 1 1 1-1 1 1c1 0 1 0 2-1v-2c1-1 2-2 3-2v-3z" class="L"></path><path d="M193 585h1v1c-1 1-1 1-2 1 0-1-1-1-1-1l2-1z" class="c"></path><path d="M196 584l1-2v-1l1 2 1 1c-1 1-1 1-2 1l-1-1z" class="e"></path><path d="M201 572h2c0 1 1 1 1 2l-1 1h0l-3-2 1-1z" class="Y"></path><path d="M197 573c1 0 2 0 3 1v1c1 1 2 1 2 2l-1 1c-1-1-1-2-2-3h0-2v-2z" class="X"></path><path d="M197 575h2 0c1 1 1 2 2 3v1h0l-2-1c-1-1-3-2-4-2 1 0 1 0 2-1z" class="J"></path><path d="M198 581v-2l1-1 2 1 1 1c-1 1-1 2-2 2h0l-2-1z" class="I"></path><path d="M197 579l1 2 2 1-2 1-1-2v1l-1 2v1h-1v-2-3l2-1z" class="S"></path><path d="M194 580h1v3c-1 0-1 0-2 1v1l-2 1v1-3h0c1-2 1-3 3-4z" class="J"></path><path d="M193 576l1-1 1 1c1 0 3 1 4 2l-1 1v2l-1-2-2 1h-1v-3l-1-1z" class="N"></path><path d="M194 577c1 1 2 1 3 2l-2 1h-1v-3z" class="D"></path><path d="M200 568c2 0 2-2 3-3h2l1 1h0c0 1 0 1-1 1l3 3h0c0 1 0 1-1 2 0 2-2 4-4 6h0c0-2 1-3 2-4 0-1 0-2-1-2h-1-2c0-1 0-1-1-1v-3z" class="O"></path><path d="M204 569c1 0 2 1 3 2 0 0-1 0-1 1-1-1-2-1-2-2v-1z" class="J"></path><path d="M200 568c2 0 2-2 3-3h2l1 1h0c0 1 0 1-1 1h-1 0l-1 1 1 1v1l-1-1h-1c1 1 1 2 2 3h-1-2c0-1 0-1-1-1v-3z" class="P"></path><path d="M188 581l4-6 1 1 1 1v3c-2 1-2 2-3 4h0v3c0 1 1 2 1 3 1 1 2 2 2 3l-1 1c0 1-1 2-2 2-2-4-3-8-5-12 0-1 1-2 2-3z" class="i"></path><path d="M191 590h1c1 1 2 2 2 3l-1 1c-1-1-2-2-2-4z" class="C"></path><path d="M191 584v3c0 1 1 2 1 3h-1c-1-1-1-2-2-3l1-1 1-2z" class="F"></path><path d="M188 581l4-6 1 1 1 1v3c-2 1-2 2-3 4h0l-1 2-1 1c-1-2-1-4-1-6z" class="g"></path><defs><linearGradient id="X" x1="189.343" y1="590.358" x2="181.664" y2="596.244" xlink:href="#B"><stop offset="0" stop-color="#0e0e0e"></stop><stop offset="1" stop-color="#303030"></stop></linearGradient></defs><path fill="url(#X)" d="M186 584c2 4 3 8 5 12l-5 7-7-12c2-1 5-5 7-7z"></path><path d="M211 567h1v3c0 2-1 3-2 5l-1 5-5 9c-2 3-4 6-6 8-2 1-3 3-5 5s-8 7-11 7l3-3 1-2v-1l5-7c1 0 2-1 2-2l1-1 13-19c1-2 2-4 3-7h1z" class="U"></path><path d="M209 575v-2c0-1 2-2 3-3 0 2-1 3-2 5h-1z" class="M"></path><path d="M209 575h1l-1 5-5 9c-2 3-4 6-6 8-2 1-3 3-5 5s-8 7-11 7l3-3c1 0 2-1 2-2 2-1 5-3 7-5 1-3 3-4 5-6v-1c4-5 9-11 10-17z" class="O"></path><defs><linearGradient id="Y" x1="408.81" y1="163.195" x2="404.266" y2="84.64" xlink:href="#B"><stop offset="0" stop-color="#575657"></stop><stop offset="1" stop-color="#6f6e6f"></stop></linearGradient></defs><path fill="url(#Y)" d="M394 84h1 38c-3 2-6 2-9 3-11 3-22 8-26 21-2 9-3 17-4 26h7c-2 1-5 1-7 1v31c-2-1-2 0-3-1-1 0-2 0-3-1h-2v1 2h-1v5l-1 1h-1c-1-1-2-2-3-2-1 1-2 1-2 2-1 1-2 1-3 2l-1-1 2-3s1-1 1-2c2-3 3-9 3-13 0-2 0-4 1-6 0-1-2-2-2-2-2-2-3-4-5-6-1 0-1-1-2-1-1-1-3-1-4-2l-6-3-2-1c1 0 1 0 1-1-1 0-2 0-3-1h-1l1-1c-2-2-5-4-6-6v-1l-1-2v-1c1 0 1 0 2-1h0v-3l-2-2-2-1 2-1c-1-2-4-3-5-3v-2h0v-2l4 2h0l-2-3h0c2 1 3 2 6 3 0-1 0-2 1-3l2-5-1-1v1h0c0-1 0-2 1-3 0-1 1-2 3-2h0l1-1 2-3h4 11c2 0 4 1 6 0h0v-1h2c1-1 3-2 5-3 1-1 2-1 3-2v-2h0z"></path><path d="M386 149v-1c1 0 3-1 4-2 0 1-2 3-3 4s-1 3-1 5v10h0v2h-1v-7-11h1z" class="Z"></path><path d="M385 110h0c1 1 1 3 1 4v2 24l1 1-1 2v-1l-1-1v1c0-3 1-7 0-10v-9-4-2-6-1h0z" class="Y"></path><path d="M385 110h0c1 1 1 3 1 4v2c-1 2 0 6-1 8h0v-14z" class="M"></path><path d="M387 98v-2c1-1 2-2 3-2h0c0 1-2 2-2 3h1s1 0 1-1h1c-1 1-3 2-3 3v1c2-2 4-4 6-4 0 2-3 3-5 4-2 2-3 4-3 7v7c0-1 0-3-1-4h0 0l-1-1c-1 0-1 2-2 3-2 2-5 5-8 6h-1 0c3-2 5-3 8-6 1-1 3-3 3-5 1-1 1-5 1-6v-2-2h1l1 1z" class="I"></path><path d="M385 99v-2h1l1 1h0v2c0 1 0 1-1 2h0c0-1 0-1-1-1h0v-2z" class="Y"></path><path d="M391 89c1-1 3-2 4-2v1l1 1c-2 2-4 3-6 5-1 0-2 1-3 2v2l-1-1h-1v2-7h-1v-1h2c1-1 3-2 5-3v1z" class="N"></path><path d="M391 89c1-1 3-2 4-2v1c-2 2-4 5-7 6h-1v-2c0-1 1-1 2-2 1 0 1-1 2-1z" class="Y"></path><path d="M378 131v-3h0c1 1 2 2 2 4 1 1 2 3 2 5 1 1 1 2 3 2v-7c1 3 0 7 0 10v-1l1 1v1 6h-1v11 7 5l-1 1h-1c-1-1-2-2-3-2-1 1-2 1-2 2-1 1-2 1-3 2l-1-1 2-3s1-1 1-2c2-3 3-9 3-13 0-2 0-4 1-6 0 1 1 1 2 2v-1c-2-2-4-4-6-7l-4-3c0-1 1-2 1-2v-2c1 0 1 1 2 1l2-2h0c1-1 1-4 0-5z" class="T"></path><path d="M374 139h0c1 1 1 2 3 2v3l-4-3c0-1 1-2 1-2z" class="F"></path><path d="M385 132c1 3 0 7 0 10v-1l1 1v1 6h-1v11c-1-2 0-5 0-7v-14-7z" class="N"></path><path d="M374 118c3-1 6-4 8-6 1-1 1-3 2-3l1 1v1 6 2 4 9 7c-2 0-2-1-3-2 0-2-1-4-2-5 0-2-1-3-2-4h0v3c1 1 1 4 0 5h0l-2 2c-1 0-1-1-2-1v2s-1 1-1 2c-1-1-2-2-3-2l1-2h0 3c-1-2-2-3-3-4-2-1-3-2-4-4v-2c0-3 0-4 2-6l2-3h2 0 1z" class="W"></path><path d="M370 124v-2c1 0 2-1 2-1h2c1-1 2-1 3-1v1c-2 2-5 1-7 3z" class="X"></path><path d="M381 120c1-1 2-1 4-1v4c-2 0-2 1-3 1-1-1-1-2-2-2v-1l1-1z" class="T"></path><path d="M378 128v-2l-2-2h2c0-1 0-1 1-2v1c1 1 0 2 1 3v2c0 1 0 1 1 2s2 4 2 5-1 1-1 2c0-2-1-4-2-5 0-2-1-3-2-4z" class="E"></path><path d="M385 111v6 2c-2 0-3 0-4 1h-1l1-1-1 1h-1c1-2 3-6 3-7v-1c1-1 2-1 3-1z" class="C"></path><path d="M385 117v2c-2 0-3 0-4 1h-1l1-1c1-1 2-2 4-2z" class="S"></path><path d="M374 118c1 2 1 1 3 2-1 0-2 0-3 1h-2s-1 1-2 1v2c-1 1-2 2-2 5 0 0 1 1 1 2l2 1v1h0c-2-1-3-2-4-4v-2c0-3 0-4 2-6l2-3h2 0 1z" class="B"></path><path d="M371 128v-1c1-1 3 0 4-1h0 1c1 1 2 2 2 3v2c1 1 1 4 0 5h0l-2 2c-1 0-1-1-2-1v2s-1 1-1 2c-1-1-2-2-3-2l1-2h0 3c-1-2-2-3-3-4h0v-1l-2-1c1-1 1-2 2-3z" class="K"></path><path d="M371 128c0 1 0 2 1 3l-1 1h0l-2-1c1-1 1-2 2-3zm4 4h1l-1 2h-2v-1s1-1 2-1z" class="F"></path><path d="M374 131h-1v-2c1-1 2-2 3-2v3l-2 1z" class="W"></path><path d="M376 126c1 1 2 2 2 3v2c1 1 1 4 0 5h0l-1-1v1c-1 0-1 0-2-1v-1h0l1-2h-1l-1-1 2-1v-3-1z" class="E"></path><path d="M375 134c1 0 1-1 2-1v2 1c-1 0-1 0-2-1v-1z" class="C"></path><path d="M350 109h0l-2-3h0c2 1 3 2 6 3l2 1c1 2 2 2 4 2 1 1 2 1 3 0l1 1h3l-1 1 1 1h5v1c0 1 0 1-1 2h0l-2 3c-2 2-2 3-2 6v2c1 2 2 3 4 4 1 1 2 2 3 4h-3 0l-1 2c1 0 2 1 3 2l4 3c2 3 4 5 6 7v1c-1-1-2-1-2-2s-2-2-2-2c-2-2-3-4-5-6-1 0-1-1-2-1-1-1-3-1-4-2l-6-3-2-1c1 0 1 0 1-1-1 0-2 0-3-1h-1l1-1c-2-2-5-4-6-6v-1l-1-2v-1c1 0 1 0 2-1h0v-3l-2-2-2-1 2-1c-1-2-4-3-5-3v-2h0v-2l4 2z" class="X"></path><path d="M364 113h3l-1 1 1 1h-3c-1-1-1-1-2-1 1 0 2 0 2-1z" class="B"></path><path d="M364 116c1 0 2 1 4 0h1c0 1 0 1-1 1l-1 1c-2 1-3 3-5 4h0-2v-1l1-1c1 0 2 0 3-2h-1 1v-1-1z" class="K"></path><path d="M354 115c2 1 4 2 7 1h3v1 1h-1l-2 1c-2 1-5 0-7-1h-1l-2-2h2l1-1z" class="C"></path><path d="M353 116l1 2h-1l-2-2h2z" class="J"></path><path d="M371 118h0l-2 3c-2 2-2 3-2 6v2c-1 0-2 1-3 1s-1 1-2 1v-1h1v-1h-2l-1-1c1 0 3-4 3-5l3-3h1c1-2 2-2 4-2z" class="D"></path><path d="M364 127h-1v-2h2c0 1 0 2-1 2z" class="T"></path><path d="M365 125h1 0c1 2 1 2 0 4-1 0-1 0-2-1v-1c1 0 1-1 1-2z" class="E"></path><path d="M371 118h0l-2 3c0-1 0-1 1-2h-2c-1 1-1 1-1 2-1 1-2 2-4 2l3-3h1c1-2 2-2 4-2z" class="G"></path><path d="M350 109h0l-2-3h0c2 1 3 2 6 3l2 1c1 2 2 2 4 2 1 1 2 1 3 0l1 1c0 1-1 1-2 1 1 0 1 0 2 1-3 0-8 0-11-1v1h1l-1 1h-2l-2-1 2-1c-1-2-4-3-5-3v-2h0v-2l4 2z" class="S"></path><path d="M346 107l4 2v1c1 0 2 1 3 1v1h-1c-2 0-4-1-6-3h0v-2z" class="c"></path><path d="M350 109h0l-2-3h0c2 1 3 2 6 3l2 1c1 2 2 2 4 2 1 1 2 1 3 0l1 1c0 1-1 1-2 1l-10-2h1v-1c-1 0-2-1-3-1v-1z" class="L"></path><path d="M353 118h1c2 1 5 2 7 1l2-1h1c-1 2-2 2-3 2l-1 1v1h2 0c-1 2-1 3-2 5v1l1 1h2v1h-1v1c1 0 1-1 2-1s2-1 3-1c1 2 2 3 4 4 1 1 2 2 3 4h-3 0l-1 2c1 0 2 1 3 2l4 3c2 3 4 5 6 7v1c-1-1-2-1-2-2s-2-2-2-2c-2-2-3-4-5-6-1 0-1-1-2-1-1-1-3-1-4-2l-6-3-2-1c1 0 1 0 1-1-1 0-2 0-3-1h-1l1-1c-2-2-5-4-6-6v-1l-1-2v-1c1 0 1 0 2-1h0v-3z" class="C"></path><path d="M360 121v1h2 0c-1 2-1 3-2 5v-2h-1c0 1-1 1-1 1-2 0-2 0-3-1 0-1 0-2 1-3 1 0 2 0 4-1z" class="F"></path><g class="P"><path d="M360 121v1l-4 3h-1c0-1 0-2 1-3 1 0 2 0 4-1z"></path><path d="M353 118h1c2 1 5 2 7 1l2-1h1c-1 2-2 2-3 2l-1 1c-2 1-3 1-4 1v-1h-1l-2 1h-1l-1 1v-1c1 0 1 0 2-1h0v-3z"></path></g><path d="M355 121v-1h6l-1 1c-2 1-3 1-4 1v-1h-1z" class="G"></path><path d="M352 125l6 6c3 2 7 3 11 5l2 1-1 2c1 0 2 1 3 2l4 3c2 3 4 5 6 7v1c-1-1-2-1-2-2s-2-2-2-2c-2-2-3-4-5-6-1 0-1-1-2-1-1-1-3-1-4-2l-6-3-2-1c1 0 1 0 1-1-1 0-2 0-3-1h-1l1-1c-2-2-5-4-6-6v-1z" class="b"></path><path d="M369 136l2 1-1 2h0c-1-1-3-2-4-3h2 1z" class="X"></path><path d="M384 92h1v7 2c0 1 0 5-1 6 0 2-2 4-3 5-3 3-5 4-8 6h-2 0c1-1 1-1 1-2v-1h-5l-1-1 1-1h-3l-1-1c-1 1-2 1-3 0-2 0-3 0-4-2l-2-1c0-1 0-2 1-3l2-5-1-1v1h0c0-1 0-2 1-3 0-1 1-2 3-2h0l1-1 2-3h4 11c2 0 4 1 6 0h0z" class="T"></path><path d="M371 110l2-2c1 0 1 0 2 1-1 0-2 1-3 2 0-1-1-1-1-1z" class="d"></path><path d="M370 101l1-1c1 0 1 1 2 1l-3 3s0-1-1-1l2-1-1-1z" class="S"></path><path d="M372 104s0 1 1 1v2h-1-2-1-2 1l1-1c1 0 2-1 3-2z" class="D"></path><path d="M356 110v-1-2-2h1v4c1 1 2 1 2 2l1 1c-2 0-3 0-4-2z" class="S"></path><path d="M373 108c3-3 5-7 5-11h-1l1-1c1 3 0 6-1 8 0 2-1 4-2 5-1-1-1-1-2-1z" class="b"></path><path d="M370 104h0c-1 1-1 1-3 1-1-1-1-2-1-4 0 0 0-1 1-2s2-1 4-1v1c-1 0-2 0-3 1-1 0-1 0-1 2 1 0 2-1 3-1l1 1-2 1c1 0 1 1 1 1z" class="F"></path><path d="M360 109c-1-3-1-6 0-9v-1c1 1 0 3 0 4 0 2 0 3 1 5 0 1 2 3 3 3 1 1 3 1 4 0l3-1s1 0 1 1l-2 1c-1 0-2 1-3 1h-3l-1-1c-1 1-2 1-3 0l-1-1c1-1 1-1 1-2z" class="N"></path><path d="M360 109l3 3c-1 1-2 1-3 0l-1-1c1-1 1-1 1-2z" class="D"></path><path d="M370 112v1c-1 0-2 0-2 1h4c1 0 3-2 4-3s2-3 4-5c0 1-2 3-1 3 0 0 1-1 1-2 1-1 1-1 1-2 1 0 0 1 0 2-1 1-3 3-3 5h3c-3 3-5 4-8 6h-2 0c1-1 1-1 1-2v-1h-5l-1-1 1-1c1 0 2-1 3-1z" class="E"></path><path d="M352 132h3c1 1 2 0 2 1h1c1 1 2 1 3 1 0 1 0 1-1 1l2 1 6 3c1 1 3 1 4 2 1 0 1 1 2 1 2 2 3 4 5 6 0 0 2 1 2 2-1 2-1 4-1 6 0 4-1 10-3 13 0 1-1 2-1 2l-2 3 1 1c1-1 2-1 3-2 0-1 1-1 2-2 1 0 2 1 3 2h1l1-1v-5h1v-2-1h2c1 1 2 1 3 1 1 1 1 0 3 1v2h11c-4 1-8 1-11 1v6c1 1 2 1 4 1l-4 1v24c1 0 6 0 7 1h-7l-1 56c-1-4-1-7-2-11-2-8-4-17-8-25l-6-16h-1l-3-7c-5-9-11-18-18-26-12-13-27-23-44-28h-2c1-1 4-6 4-7l1-1v-1h2l5 2c1 0 2 0 3 1l5 2c2 0 3 0 5 1h1c3-3 8-7 13-8 1-1 3-1 4-2z" class="c"></path><path d="M373 192c-1 0-2 1-3 1v-1c1-1 2-1 3-2h1l-1 2z" class="N"></path><path d="M386 199c1-1 2-1 3-2l1 1c0 1-1 1-1 2l-2 1-2-2h1z" class="Y"></path><path d="M374 190h1l1 1c-1 1-1 2-2 2h-1v-1l1-2zm-47-46c1 0 1 0 1 1 1 1 1 2 1 4h-1c-1-2-1-3-1-5z" class="X"></path><path d="M386 207c-1 1-2 2-3 2h-1v-2c0-1 0-1 1-2l1 1c1 0 1 1 1 1h1z" class="L"></path><path d="M329 141c2 0 3 0 5 1h1v1h1l2-2v4 1l-9-5z" class="G"></path><path d="M385 167h1v6 3c0 1 1 1 1 2l-2 1-1-1 1-6v-5zm-8 39c1 1 1 2 2 3 0 1 0 1 1 2v-1c1 1 3 6 3 8v4l-6-16zm8-18v-7c1 1 1 2 2 2 0 1 0 1-1 1-1 2 0 12 0 14v1h-1l2 2h-1c-1 2 3 5 0 6h0c-1-1-1-2-1-3v-5-11z" class="d"></path><path d="M313 138l1-1v-1h2l5 2c1 0 2 0 3 1l1 1h-1c0 1 2 3 3 4-3-1-4-3-6-3h-5 0c0-1 0-1-1-1h-1c-2 1-3 3-3 5h-2c1-1 4-6 4-7z" class="N"></path><g class="C"><path d="M374 174l1 1c1-1 2-1 3-2 0-1 1-1 2-2 1 0 2 1 3 2h1l1-1-1 6v1c1 3 1 7 1 9v11 5c0 1 0 2 1 3h0-1s0-1-1-1l-1-1c1 0 1 0 1-1l-7-13c-2-4-4-8-6-11h-1l-1-2c0-1 1-1 1-1 1-1 2-1 2-2l2-1z"></path><path d="M374 174l1 1c1-1 2-1 3-2 0-1 1-1 2-2 1 0 2 1 3 2h1l1-1-1 6v1 1l-1 1v1h0l-1-1c0-1-1-2-2-2v-1h-1c-3-1-5 1-8 2h0-1l-1-2c0-1 1-1 1-1 1-1 2-1 2-2l2-1z"></path></g><path d="M383 173h1l1-1-1 6v1c-1 0-1-1-1-2-1-1-2-2-2-3l2-1z" class="F"></path><path d="M370 180c1-2 4-4 6-4s3 0 4 1 2 2 3 4v1h0l-1-1c0-1-1-2-2-2v-1h-1c-3-1-5 1-8 2h0-1z" class="B"></path><path d="M384 179c1 3 1 7 1 9v11 5c-1-1 0-6-1-7 0-1-2-1-3-3s-2-7-4-9h0 0 0v-1c0-2 1-2 2-3h2 1l1 1h0v-1l1-1v-1z" class="G"></path><path d="M379 181h2v2 2h-1 0c0-1 0-1-1-1v-1h1v-1l-1-1z" class="J"></path><path d="M379 181l1 1v1h-1l-2 1h0c0-2 1-2 2-3z" class="L"></path><path d="M352 132h3c1 1 2 0 2 1h1c1 1 2 1 3 1 0 1 0 1-1 1l2 1 6 3c1 1 3 1 4 2 1 0 1 1 2 1 2 2 3 4 5 6 0 0 2 1 2 2-1 2-1 4-1 6 0 4-1 10-3 13 0 1-1 2-1 2l-2 3-2 1c0 1-1 1-2 2 0 0-1 0-1 1-1-1-1-2-2-3l-3-5c-6-7-12-13-18-18-3-1-4-4-7-5l-1-1v-1-4l-2 2h-1v-1c3-3 8-7 13-8 1-1 3-1 4-2z" class="C"></path><path d="M368 150c1 2 2 3 2 5-1 1-1 1-1 2v1l-1-8z" class="F"></path><path d="M366 147c0 1 1 2 2 3l1 8c-1 4-2 7-3 11v-7h0c1-4 1-10-1-14h0l1-1z" class="J"></path><path d="M366 141l1-1c2 1 3 2 5 4 1 1 3 3 3 5h-1c1 4 1 7 1 10 0 4-1 8-3 12 0-1-1-2-1-3s1-2 2-4c0-2 0-4 1-6v1-3c1-1 1-1 0-2v-2s0-1-1-2c0-1-1-1-1-2s-1-2-2-3l-3-3v-1h-1z" class="S"></path><path d="M366 141l1-1c2 1 3 2 5 4 1 1 3 3 3 5h-1c-2-2-4-6-7-8h-1z" class="R"></path><path d="M372 144c0-1 0-1-1-2h3c2 2 3 4 5 6 0 0 2 1 2 2-1 2-1 4-1 6 0 4-1 10-3 13 0 1-1 2-1 2l-2 3-2 1c0 1-1 1-2 2 0 0-1 0-1 1-1-1-1-2-2-3l2-1 3-3c2-4 3-8 3-12 0-3 0-6-1-10h1c0-2-2-4-3-5z" class="E"></path><path d="M372 175c0-2 1-3 2-4v1c1 0 1 0 1-1h0 1l-2 3-2 1z" class="B"></path><path d="M369 174h2 0v1c-1 0-1 1-1 2h0s-1 0-1 1c-1-1-1-2-2-3l2-1z" class="F"></path><path d="M374 149h1c1 3 3 8 2 11l-1-1h-1c0-3 0-6-1-10z" class="e"></path><path d="M375 159h1l1 1-3 9c-1 2-3 4-3 5h-2l3-3c2-4 3-8 3-12z" class="d"></path><path d="M352 132h3c1 1 2 0 2 1h1c1 1 2 1 3 1 0 1 0 1-1 1l2 1 6 3c1 1 3 1 4 2 1 0 1 1 2 1h-3c1 1 1 1 1 2-2-2-3-3-5-4l-1 1c-1 0-3-1-3-1h-9c-4 1-8 3-11 5l3-3v-1c-2 1-5 3-7 3l-1 1v-4l-2 2h-1v-1c3-3 8-7 13-8 1-1 3-1 4-2z" class="S"></path><path d="M346 141c2-1 4-2 7-2-1 1-6 3-7 3v-1z" class="D"></path><path d="M351 136c1-1 2-1 3-2 2 0 4 0 6 1l2 1h-11z" class="P"></path><path d="M363 140h-1c1-2 4 0 6-1 1 1 3 1 4 2 1 0 1 1 2 1h-3c1 1 1 1 1 2-2-2-3-3-5-4l-1 1c-1 0-3-1-3-1z" class="B"></path><path d="M352 132h3c1 1 2 0 2 1h1c1 1 2 1 3 1 0 1 0 1-1 1-2-1-4-1-6-1-1 1-2 1-3 2-2 0-4 1-6 2h-1l2-2h0c1-1 1-1 2-1v-1c1-1 3-1 4-2z" class="d"></path><path d="M352 132h3l-9 4h0c1-1 1-1 2-1v-1c1-1 3-1 4-2z" class="R"></path><path d="M335 142c3-3 8-7 13-8v1c-1 0-1 0-2 1h0l-2 2h1c-2 2-5 3-6 6l-1 1v-4l-2 2h-1v-1z" class="c"></path><path d="M352 151v-1c1-1 2-4 4-5 1-1 4 0 6 0s3 1 4 2l-1 1h0c2 4 2 10 1 14h0v-1h-1c0 3-2 6-1 9-6-7-12-13-18-18 1 0 2 1 3 2s2 1 3 1c0 0 0-1 1-1-1-1-1-2-1-3z" class="F"></path><path d="M356 151l1 1v1h-1v-2z" class="G"></path><path d="M358 160l1-1v1 1h-1v-1h0z" class="K"></path><path d="M352 151l1-2h1v1s0 1 1 1c-1 1-1 2 0 3 0 0 1 1 2 1 1 1 2 0 3 0h0c-1 1-1 1-2 1-1 1-2 0-3-1-1 0-2-1-2-1-1-1-1-2-1-3z" class="E"></path><defs><linearGradient id="Z" x1="240.24" y1="553.272" x2="229.449" y2="552.358" xlink:href="#B"><stop offset="0" stop-color="#8b8a8a"></stop><stop offset="1" stop-color="#b5b3b4"></stop></linearGradient></defs><path fill="url(#Z)" d="M227 510c1 0 2 1 3 1h0l2 2v3h1c1 0 2 0 2 1 1 0 1 1 2 1 1 1 1 2 2 2v3-7l1 4 1-1h0l-1-1c1 0 1-1 2-1h1 2 1 3l1 1h1l1 1v1h-1l1 1-1 1s-1 1-1 2c-1 1-1 2-1 3 1 1 1 1 1 2h-1 0 0l1 1v1 1 1l1 7-1 5 1 7-1 1 1 1-1 1v1 1l1 1c0 2 0 6-1 8v2h0v2 1 1 13 18 1c-1 0-1 0-2 1h-5v1c0 1-1 1 0 1v2h0 0c0 1-1 1-1 1 0 2 0 3 1 4l-1 1h0 0v2 4 2c0 1 0 1 1 2l-1 1h2c0 1 0 1-1 1h-1v1c1 1 2 0 2 2-1 0-2 0-3 1h0v22 5c0 1-1 2 0 4 1 1 0 3 1 4h0 3 0 5v-1l1 1-1 1-1 2c-1 1-2 4-3 5l-1 1c0 1-1 2-1 3l-2-2h-1v1h0v2c0 1 0 2 1 2l-2 4c-3-1-6-1-8-1l-1-1c-1-1 0-3 0-4v-1l1-1v-2-12-29-1-5-10-5-4-1-1-15-10c-2 1-2 2-2 4v2c-2-1-3-1-5-1v-1h-2c-1 0-2 0-3 1h-1v-1-1h0v-7-5-1-4-6-1-5-1-3h0 7l-1-2c-2 0-4 1-6 0v-2l1 1 1-1v-1l1-1c2 0 5 1 6 0l2 1v1h2v-1-4-9l-1-2v-9h-1c0-1 0-1-1-1l1-1v-4c-1-1-2 0-3-1 1-1 2-1 3-1-1-2 0-2 0-3-1-1-2-1-3-2z"></path><path d="M227 510c1 0 2 1 3 1h0l2 2v3c-1 6 0 13 0 18l-1-2v-9h-1c0-1 0-1-1-1l1-1v-4c-1-1-2 0-3-1 1-1 2-1 3-1-1-2 0-2 0-3-1-1-2-1-3-2zm7 31v-2h3v4c-2 2-2 6-3 9v9 21 51 22c0 3 1 8 0 11l1 1v1c-1 1 0 2-1 3-1 2 0 6 0 8h-1V577v-24c0-4 0-9 1-12z" class="B"></path><path d="M234 541v-2h3v4c-1 0-1 0-2 1h0v1h0-1v-4z" class="D"></path><path d="M232 548v-1 35c-2 1-2 2-2 4v2c-2-1-3-1-5-1v-1h-2c-1 0-2 0-3 1h-1v-1-1h0v-7-5-1-4h0 1 1c2-1 3 0 5 0 1 1 4 0 5 1l1-1v-9-11z" class="e"></path><path d="M220 568h1c2-1 3 0 5 0 1 1 4 0 5 1-1 1-3 1-4 1h-5 1 0v-1c-1 0-2 0-3-1h0z" class="U"></path><path d="M219 568h1 0c1 1 2 1 3 1v1h0-1-2v1c3 0 9-1 11 1-1 1-5 0-7 0h-5v-4h0z" class="Q"></path><path d="M219 572h5c-1 0-3 0-5 1 3 0 9 0 12 1v1c-2 1-10-1-11 1h3c3 0 5 0 8 1h-1c-2 1-5 1-7 1h-4v-5-1z" class="j"></path><path d="M219 578h4-1l1 1h8l-1 1h-1l1 1c0 1-1 1 0 2v3 2c-2-1-3-1-5-1v-1h-2c-1 0-2 0-3 1h-1v-1-1h0v-7z" class="V"></path><path d="M222 585h3v1h-2l-1-1z" class="j"></path><path d="M219 585h1 2l1 1c-1 0-2 0-3 1h-1v-1-1z" class="Q"></path><path d="M223 579h8l-1 1h-7-2 2c-1-1-1-1-2 0h0l-1-1h3 0z" class="U"></path><path d="M223 580h7-1l1 1c0 1-1 1 0 2v3 2c-2-1-3-1-5-1v-1c1 0 3 0 4-1-1-1-3 0-4-1v-1h3 0c1-1 1-1 1-2-1 0-2 1-3 0-1 0-1 0-1-1h-2z" class="Y"></path><path d="M228 546l2 1v1h2v11 9l-1 1c-1-1-4 0-5-1-2 0-3-1-5 0h-1-1 0v-6-1-5-1-3h0 7l-1-2c-2 0-4 1-6 0v-2l1 1 1-1v-1l1-1c2 0 5 1 6 0z" class="J"></path><path d="M219 550v-2l1 1h4 7v1l-2 1c0-1-3-1-4-1-2 0-4 1-6 0z" class="f"></path><defs><linearGradient id="a" x1="227.113" y1="548.749" x2="222.591" y2="544.951" xlink:href="#B"><stop offset="0" stop-color="#a09c9e"></stop><stop offset="1" stop-color="#b4b7b4"></stop></linearGradient></defs><path fill="url(#a)" d="M228 546l2 1v1h-5-4v-1l1-1c2 0 5 1 6 0z"></path><path d="M219 561h7 2v1h2v1l1 1v1h-2c-1 0-1 0-1 1h3v1c-1 0-2 1-3 1h-2c-2 0-3-1-5 0h-1-1 0v-6-1z" class="V"></path><path d="M222 562c2 0 5 0 6 1h0c-1 0-2 1-3 1h-4 0v-1l1-1z" class="j"></path><path d="M219 561h7c-1 1-3-1-4 1l-1 1v1h0l1 1-1 1-1-1c-1 1-1 2-1 3h0v-6-1z" class="U"></path><path d="M222 565c1 0 3 0 4 1h2 3v1c-1 0-2 1-3 1h-2c-2 0-3-1-5 0h-1-1c0-1 0-2 1-3l1 1 1-1z" class="H"></path><path d="M225 550c1 0 4 0 4 1h1l1 1c-1 1-2 1-3 1l-8 1h0 11v1c-1 1-2 1-3 1h-8v1h12c-3 3-6 2-10 3 3 0 6-1 9 0v1h-3-2-7v-5-1-3h0 7l-1-2z" class="Q"></path><path d="M225 550c1 0 4 0 4 1h1l-4 1-1-2z" class="b"></path><path d="M237 539c1 1 1 2 1 4 0 1-1 2-1 4v4l-2 10c-1 7 0 14 0 21v6 1 1h3v31h0c-1 10 0 21 0 31v8c-1 2 0 4-1 6 0-1-1-1-2-2v-3c0-2 0-4-1-6v-22-51-21-9c1-3 1-7 3-9v-4z" class="I"></path><path d="M239 516l1 4 1-1h0l-1-1c1 0 1-1 2-1 0 1 0 2-1 3l1 1-1 1h1c-1 2-1 4-1 5-1 3 0 7 0 10v7 4c0 3-1 7 0 10l-1 1v2 5l1 39v7 2 1 1c1 1 0 13 0 15h0 0v22 5c0 1-1 2 0 4 1 1 0 3 1 4h0 3 0 5v-1l1 1-1 1-1 2c-1 1-2 4-3 5l-1 1c0 1-1 2-1 3l-2-2h-1v1h0v2c0 1 0 2 1 2l-2 4c-3-1-6-1-8-1l-1-1c-1-1 0-3 0-4v-1l1-1v-2 5h1v-1h1c0-2-1-6 0-8 1-1 0-2 1-3v-1l-1-1c1-3 0-8 0-11 1 2 1 4 1 6v3c1 1 2 1 2 2 1-2 0-4 1-6v-8c0-10-1-21 0-31h0v-31l1-67v-7z" class="T"></path><path d="M241 631h0 0v22 5c0 1-1 2 0 4 1 1 0 3 1 4h0 3 0v1h-2c-1 0-1 0-1 1h0c0 2-1 4-1 5-1-2 0-4 0-6v-2-4h-1v-3l1-7c0-6-1-13 0-20z" class="D"></path><path d="M232 677v-2 5h1v-1h1v1c1 1 3 1 4 1l1-1v3h-7-1c-1-1 0-3 0-4v-1l1-1z" class="B"></path><path d="M233 679h1v1 2h-1v-2-1z" class="J"></path><path d="M232 677c0 1-1 5 0 6h-1c-1-1 0-3 0-4v-1l1-1z" class="G"></path><path d="M241 673c0-1 1-3 1-5h0c0-1 0-1 1-1l-1 1 2 2-1 1v1c1 1 2 1 3 1v1l-1 1c0 1-1 2-1 3l-2-2h-1v-1-2z" class="E"></path><path d="M241 675h2c0 1 0 1-1 1h-1v-1z" class="B"></path><path d="M243 675h2c0 1-1 2-1 3l-2-2c1 0 1 0 1-1z" class="J"></path><path d="M250 665l1 1-1 1-1 2c-1 1-2 4-3 5v-1c-1 0-2 0-3-1v-1l1-1-2-2 1-1h2v-1h5v-1z" class="B"></path><path d="M249 669c-1 0-2 0-4-1v-1h5l-1 2z" class="e"></path><path d="M238 621l1 59-1 1c-1 0-3 0-4-1v-1c0-2-1-6 0-8 1-1 0-2 1-3v-1l-1-1c1-3 0-8 0-11 1 2 1 4 1 6v3c1 1 2 1 2 2 1-2 0-4 1-6v-8c0-10-1-21 0-31h0z" class="R"></path><path d="M237 669h1v3h-1l-2-1c0-1 2-1 2-2z" class="e"></path><path d="M246 517h3l1 1h1l1 1v1h-1l1 1-1 1s-1 1-1 2c-1 1-1 2-1 3 1 1 1 1 1 2h-1 0 0l1 1v1 1 1l1 7-1 5 1 7-1 1 1 1-1 1v1 1l1 1c0 2 0 6-1 8v2h0v2 1 1 13 18 1c-1 0-1 0-2 1h-5v1c0 1-1 1 0 1v2h0 0c0 1-1 1-1 1 0 2 0 3 1 4l-1 1h0 0v2 4 2c0 1 0 1 1 2l-1 1h2c0 1 0 1-1 1h-1v1c1 1 2 0 2 2-1 0-2 0-3 1h0c0-2 1-14 0-15v-1-1-2-7l-1-39v-5-2l1-1c-1-3 0-7 0-10v-4-7c0-3-1-7 0-10 0-1 0-3 1-5h-1l1-1-1-1c1-1 1-2 1-3h1 2 1z" class="B"></path><path d="M244 572h-2v-1c3 0 6-1 8 0v1c-1 1-4 0-6 0z" class="H"></path><path d="M250 557h-8v-1c2-1 6 0 8 0v1z" class="j"></path><path d="M243 566h7v2c-1 0-6 0-8-1l1-1z" class="U"></path><path d="M250 555h0c-1 1-6 0-8 0v-1c2-2 6-1 9 0l-1 1zm0 15h-6c-1 0-1 0-2-1h0c3-1 6-1 8-1v2z" class="V"></path><path d="M243 566h0l-1-1v-1c0-2 1-4 0-5v-1c2-1 6 0 9 0 0 2 0 6-1 8h-7z" class="N"></path><path d="M242 550h0v-4-5-4h0c-1-3 1-7 0-10h0 0 1 1v1h0c-1 1-1 3-1 4l1 1v2l-1 1 1 1-1 1h1l-1 2 1 1-1 1 1 1h0c-1 0-1 1-1 1 1 1 1 1 2 1s4 1 5 0h0l1 7-1 1c-1 0-5-1-7-1h-1v-1c1 0 1 0 2-1h0-2z" class="d"></path><path d="M244 546h1 5v1c-2 0-4 1-6 0v-1zm-1 6h-1v-1c3 0 6 0 9 1l-1 1c-1 0-5-1-7-1zm-1-2l1-1c2-1 4-1 7 0h0v1h-6-2z" class="V"></path><path d="M245 535h0l-1-1c0-1 0-1 1-2l1-1c1 1 2 1 4 1v1l1 7-1 5h0c-1 1-4 0-5 0s-1 0-2-1c0 0 0-1 1-1h0l-1-1 1-1-1-1 1-2h-1l1-1s0-1 1-1h-1v-1h1z" class="O"></path><path d="M245 537h4v1h-4v-1z" class="V"></path><path d="M245 535c2 0 4 0 5 1h-5-1v-1h1z" class="M"></path><path d="M251 540l-1 5h0c-1-2-2-1-4-2h4v-1c-1-1-2-1-3-1h0c1-1 2-1 4-1zm-5-23h3l1 1h1l1 1v1h-1l1 1-1 1s-1 1-1 2c-1 1-1 2-1 3 1 1 1 1 1 2h-1 0 0l1 1v1 1c-2 0-3 0-4-1l-1 1c-1 1-1 1-1 2l1 1h0-1v1h1c-1 0-1 1-1 1l-1-1 1-1v-2l-1-1c0-1 0-3 1-4h0v-1h-1-1l1-2h-1c0-1 0-1 1-2l-1-1h-1l1-1-1-1c1-1 1-2 1-3h1 2 1z" class="I"></path><path d="M249 521c1 0 2 0 2-1l1 1-1 1s-1 1-1 2c-1-1-3-1-4-1v-1c1 0 2 0 3-1h0z" class="O"></path><path d="M246 517h3l1 1h1l1 1v1h-1c0 1-1 1-2 1s-4 1-5 0v-3l1-1h1z" class="Z"></path><path d="M244 572c2 0 5 1 6 0v13 18 1c-1 0-1 0-2 1h-5c0-1-1-2 0-2v-2h0c-1-2 0-3 0-4 0-3-1-5-1-7h1c-1-2-1-4-1-5 1-2 0-5 0-7 1 0 1-1 0-1v-1h1 1l-1-1-1-1h0l1-1 1-1z" class="N"></path><path d="M245 602h4v1h-1c-2 0-2 0-3-1z" class="Y"></path><path d="M244 596v-1c2-1 2-1 4 0h2c-1 1-4 1-6 1zm0-7v-1l1-1h4v1l-3 1h-2z" class="I"></path><path d="M242 577h2v2h-1 1c-1 1-1 2-1 2l1 1v2 1h0l-1 1v3h1 2l3-1v-1l1 1v5h-2l1 1-1 1c-2-1-2-1-4 0v1 1l-1 1v3h0c-1-2 0-3 0-4 0-3-1-5-1-7h1c-1-2-1-4-1-5 1-2 0-5 0-7 1 0 1-1 0-1z" class="b"></path><path d="M249 587l1 1v5h-2c-1 1-3 1-4-1h4v-1h-4v-1c1 0 1-1 2-1l3-1v-1z" class="Y"></path><path d="M244 572c2 0 5 1 6 0v13h0v1h-1-4c-1 0-1 0-1-1h0v-1-2l-1-1s0-1 1-2h-1 1v-2h-2v-1h1 1l-1-1-1-1h0l1-1 1-1z" class="I"></path><path d="M244 576h6v1h-6s0-1-1-1h1z" class="M"></path><path d="M242 577v-1h1c1 0 1 1 1 1l1 1-1 1 1 1-1 1v1h4c-2 1-2 1-3 2h2 2v1 1h-4c-1 0-1 0-1-1h0v-1-2l-1-1s0-1 1-2h-1 1v-2h-2z" class="Y"></path><path d="M245 586v-1h4v1h-4z" class="O"></path><path d="M244 572c2 0 5 1 6 0v13h0c0-2 1-8 0-9h-6l-1-1-1-1h0l1-1 1-1z" class="i"></path><path d="M243 573c2 0 5 0 7 1v1h-7l-1-1h0l1-1z" class="M"></path><path d="M346 326l1 1h1c1 1 0 2 0 3h-1c-1 2-2 4-3 5-1 0-1 1-1 1h0c2 0 3 0 5-1h0v1 4h1 0 4l3 1h0v14h-1-14l-1 1h-1s0 1-1 0c-2 0-5 1-7 1h-7c-2-1-5-2-8-1h0c-3 1-8 0-10 1-1 0-2-1-3-1s-5 1-6 1l-2-1c-1 0-1 1-2 1l1 1c-1 1-2 2-2 3-1 1-1 2-1 3h-6-1v-1l-1 2-17 2c-1 0-4 1-5 0-1 1-2 1-2 1h-1c-2 1-4 1-6 0-1-2-6 0-8-1l-1-1c0-1-2-1-3-1l-2 1v1h-1v-1l-1 1h-1 0l1-2 1-4 1-6v-1-3-4c1-4 0-9 1-13h1v-2c-1 0-1-1-1-1v-1c2 0 3 0 4 1v-1c1 1 2 1 3 1s3 1 4 1h1v1 1l3 3c2 1 2 2 4 2h4 0c1 1 3 1 5 1 4 2 10 0 15 0 4 0 7-1 11-1 3-1 6-2 8-3h1v1h0 0 0s-1 0-1 1h-1 1c6-1 13-4 19-8l1-1 2 1h0 5 2c6-1 12 0 16-4z" class="T"></path><path d="M256 344c2 0 4 0 7 1 0 0 0 1-1 1h-2v1l-2-1-2-2z" class="B"></path><path d="M237 366l2-5 2 2c0 1 0 1-1 2l-2 1v1h-1v-1z" class="Q"></path><path d="M241 334l1-1 2 4c1 0 1 0 2 1l1 1h-1 0c-1-1-2-1-3-2l-1 1h0c0-1 0-1 1-1-1-1-1-1-2-1v-2z" class="E"></path><path d="M240 332l2 1-1 1h-1l-1 1v4c0 2-1 5 0 8v1h0c-1 3 1 4-1 6v-3-4c1-4 0-9 1-13h1v-2z" class="C"></path><path d="M243 330c1 1 2 1 3 1s3 1 4 1h1v1 1l3 3c2 1 2 2 4 2l-1 1c-2 0-3-2-5-3 0 0-1 0-1-1h-1c0 1 0 1 1 2l1 2h-1c-1 0-2-2-3-3v-1c-1-2-3-3-5-5v-1z" class="G"></path><path d="M263 345l18 1-2 1h-2c-2 1-3 1-5 2v1h-2-4l-4-1-2-2h0 0v-1h2c1 0 1-1 1-1z" class="J"></path><path d="M272 350c-1-1-1-1-1-2 1 0 1-1 2 0l1-1h1c1-1 2 0 4 0h-2c-2 1-3 1-5 2v1z" class="B"></path><path d="M260 347l1-1 9 3v1h-4l-4-1-2-2z" class="D"></path><path d="M302 336v1h0 0 0s-1 0-1 1h-1 1c-4 2-8 2-11 3-5 0-10 1-14 1-7 0-13-1-19-2l1-1h4 0c1 1 3 1 5 1 4 2 10 0 15 0 4 0 7-1 11-1 3-1 6-2 8-3h1z" class="I"></path><path d="M242 347v-1c0-2 0-3 1-4 2-1 5 0 6 1v5l-1 4v1 1c-2 1-3 1-4 1h-2-1c0-3 0-5 1-8z" class="h"></path><path d="M242 350h5v-1l-1-1v-1l3 1-1 4v1 1c-2 1-3 1-4 1h-2-1c0-3 0-5 1-8v3z" class="Q"></path><path d="M242 347v3c0 1-1 2 0 3l6-1v1 1c-2 1-3 1-4 1h-2-1c0-3 0-5 1-8z" class="Y"></path><path d="M244 355h-1l1-1c1 0 2-1 4-1v1c-2 1-3 1-4 1z" class="Z"></path><path d="M346 326l1 1h1c1 1 0 2 0 3h-1c-1 2-2 4-3 5-1 0-1 1-1 1-1 1-2 1-3 1-4 1-9 3-13 4l-9 2s1-1 2-1h0s0-1 1-1 2 0 3-1h-5l-1-1h-3c1-1 2-1 3-1 1-1 1 0 2-1 1 0 3-1 5-1l1-1h1c1 0 2 0 3-1h0c2-1 3-1 5-2h3v-1c1 1 2 1 2 0h-1c-2 0-3 1-5 1-3 1-6 0-9 2h0v-1l-1 1c0 1-1 1-2 2h0v-1h0 0l-6 1c1-1 4-2 6-2 0-1 1-1 1-2-2 0-2-1-3-2l1-1 2 1h0 5 2c6-1 12 0 16-4z" class="E"></path><path d="M343 331l1 1c-1 0-1 1-2 1l-1 1h-3c-1 0-2 0-3-1l8-2z" class="Z"></path><path d="M328 330h2v1s0 1 1 1h0c-3 0-5 1-7 1-1-1-1-1-1-3h5z" class="B"></path><path d="M346 326l1 1h1c1 1 0 2 0 3h-1l-2 1h-1l-1-1c-3 1-8 1-12 2-1 0-1-1-1-1v-1c6-1 12 0 16-4z" class="P"></path><path d="M347 327h1c1 1 0 2 0 3h-1l-2 1h-1l-1-1c2-1 3-2 4-3z" class="W"></path><defs><linearGradient id="b" x1="326.818" y1="339.221" x2="323.22" y2="334.935" xlink:href="#B"><stop offset="0" stop-color="#807e80"></stop><stop offset="1" stop-color="#939493"></stop></linearGradient></defs><path fill="url(#b)" d="M345 331l2-1c-1 2-2 4-3 5-1 0-1 1-1 1-1 1-2 1-3 1-4 1-9 3-13 4l-9 2s1-1 2-1h0s0-1 1-1 2 0 3-1h-5l-1-1 17-6c1 1 2 1 3 1h3l1-1c1 0 1-1 2-1l-1-1h0 2z"></path><path d="M341 334l1-1c0 1-1 2-2 2h0-1c-1 0-2 1-3 1-3 2-6 2-9 5l-9 2s1-1 2-1h0s0-1 1-1 2 0 3-1 3-1 5-2 6-3 8-3 3-1 4-1z" class="b"></path><path d="M345 331l2-1c-1 2-2 4-3 5-1 0-1 1-1 1-1 1-2 1-3 1-4 1-9 3-13 4 3-3 6-3 9-5 1 0 2-1 3-1h1 0c1 0 2-1 2-2 1 0 1-1 2-1l-1-1h0 2z" class="X"></path><path d="M249 343h2c1 0 3 0 4 1h1l2 2 2 1h0 0l2 2 4 1h4 2-1c1 1 1 1 2 1-1 0-1 1-1 1l9 1h6c-1 1-1 2-1 3 1 1 3 1 4 2v-1-1h1s0 1 1 1h1l1 1c-1 1-2 2-2 3-1 1-1 2-1 3h-6-1v-1l-1 2-17 2c-1 0-4 1-5 0-1 1-2 1-2 1h-1c-2 1-4 1-6 0-1-2-6 0-8-1l-1-1c0-1-2-1-3-1 1-1 1-1 1-2l-2-2 2-4c1 0 1-1 1-2h2c1 0 2 0 4-1v-1-1l1-4v-5z" class="f"></path><path d="M257 361h1v1h-1v-1z" class="H"></path><path d="M260 347h0l2 2h-4l1-1h1v-1z" class="Q"></path><path d="M270 350h2-1c1 1 1 1 2 1-1 0-1 1-1 1-2 0-4-1-6-2h4z" class="L"></path><path d="M255 344h1l2 2c-1 1-2 1-3 1-2 0-3 1-4 2h-1v-1h1c1 0 1-1 2-1 1-1 2-2 2-3z" class="H"></path><path d="M251 343c1 0 3 0 4 1 0 1-1 2-2 3-1 0-1 1-2 1h-1v-2l1-3z" class="k"></path><path d="M249 343h2l-1 3c0 3 0 7-2 9h0c-1 2-5 2-7 2 1 0 1-1 1-2h2c1 0 2 0 4-1v-1-1l1-4v-5z" class="S"></path><path d="M252 368c3 0 6-2 9-2l5-1h0 1c1-1 2 0 3-1h0c1 0 1 0 2-1 1 0 3 1 3 0h4c-2 1-4 1-6 1-1 1-3 1-5 2-2 0-5 0-7 1-1 1-2 1-2 1h-1c-2 1-4 1-6 0z" class="Q"></path><path d="M281 353h6c-1 1-1 2-1 3 1 1 3 1 4 2v-1-1h1s0 1 1 1h1l1 1c-1 1-2 2-2 3-1 1-1 2-1 3h-6-1v-1l-1 2-17 2c-1 0-4 1-5 0 2-1 5-1 7-1 2-1 4-1 5-2 2 0 4 0 6-1h0c1 0 2-1 3 0h1v-1c1-1 0-1 1-2v-1-2h0c0-1 1-2 1-2v-1h-2c-1 0-1-1-2-1z" class="j"></path><path d="M281 353h6c-1 1-1 2-1 3 1 1 3 1 4 2v-1 1c-1 1-1 1-3 2h-1-1v-3h1l-1-1c0 1 0 1-1 1v3-1-2h0c0-1 1-2 1-2v-1h-2c-1 0-1-1-2-1z" class="Q"></path><path d="M290 357v-1h1s0 1 1 1h1l1 1c-1 1-2 2-2 3-1 1-1 2-1 3h-6-1v-1h0 0l3-3c2-1 2-1 3-2v-1z" class="V"></path><path d="M284 363h1c1-1 4-1 5-1 0 0 1-1 2-1-1 1-1 2-1 3h-6-1v-1h0 0z" class="I"></path><path d="M343 336h0c2 0 3 0 5-1h0v1 4h1 0 4l3 1h0v14h-1-14l-1 1h-1s0 1-1 0c-2 0-5 1-7 1h-7c-2-1-5-2-8-1h0c-3 1-8 0-10 1-1 0-2-1-3-1s-5 1-6 1l-2-1c-1 0-1 1-2 1h-1c-1 0-1-1-1-1h-1v1 1c-1-1-3-1-4-2 0-1 0-2 1-3h-6l-9-1s0-1 1-1c-1 0-1 0-2-1h1v-1c2-1 3-1 5-2h2l2-1 2-1 7-1 18-3 4-1 3-1h3l1 1h5c-1 1-2 1-3 1s-1 1-1 1h0c-1 0-2 1-2 1l9-2c4-1 9-3 13-4 1 0 2 0 3-1z" class="g"></path><path d="M317 347v2c-1 1-2 1-2 1h-3-6l-6-1c4-1 7-1 11-1 1 0 4-1 5 0h1v-1z" class="E"></path><path d="M317 347c0-1 0-2 1-3h1l-1 2c0 1 0 2 1 3 3 0 6-3 9-4l1 1c-4 1-7 3-10 6v2h0-3-4c1 0 1 0 2-1-2 0-4-1-6-2 2 0 4 1 6 0l1-1s1 0 2-1v-2z" class="B"></path><path d="M317 354h1v-2h0 0c1 1 1 1 1 2h0-3 1z" class="P"></path><path d="M314 353h1l1-1 1-1v3h-1-4c1 0 1 0 2-1z" class="T"></path><path d="M314 351h3l-1 1-1 1h-1c-2 0-4-1-6-2 2 0 4 1 6 0z" class="E"></path><path d="M272 350v-1c2-1 3-1 5-2v1c1 1 2 0 4 0l10-1c0 1-2 1-3 1h0c1 0 2 0 3 1h2 7l6 1h6 3l-1 1c-2 1-4 0-6 0l-21-1-14 1c-1 0-1 0-2-1h1z" class="J"></path><path d="M272 350v-1c2-1 3-1 5-2v1c1 1 2 0 4 0v1c4 0 8 0 12 1h-6l-14 1c-1 0-1 0-2-1h1z" class="R"></path><defs><linearGradient id="c" x1="309.632" y1="360.504" x2="276.871" y2="344.129" xlink:href="#B"><stop offset="0" stop-color="#12100f"></stop><stop offset="1" stop-color="#363639"></stop></linearGradient></defs><path fill="url(#c)" d="M273 351l14-1 21 1c2 1 4 2 6 2-1 1-1 1-2 1l-25-1h-6l-9-1s0-1 1-1z"></path><path d="M287 353l25 1h4 3 2c1 1 3 1 5 1h5 3 0 7l-1 1h-1s0 1-1 0c-2 0-5 1-7 1h-7c-2-1-5-2-8-1h0c-3 1-8 0-10 1-1 0-2-1-3-1s-5 1-6 1l-2-1c-1 0-1 1-2 1h-1c-1 0-1-1-1-1h-1v1 1c-1-1-3-1-4-2 0-1 0-2 1-3z" class="a"></path><path d="M315 339h3l1 1h5c-1 1-2 1-3 1s-1 1-1 1h0c-1 0-2 1-2 1l-14 3c-4 0-8 1-13 1l-10 1c-2 0-3 1-4 0v-1h2l2-1 2-1 7-1 18-3 4-1 3-1z" class="N"></path><path d="M308 341l4-1v1c-1 1-2 1-4 1v-1z" class="I"></path><path d="M315 339h3l1 1c-2 0-4 1-7 1v-1l3-1z" class="V"></path><path d="M343 336h0c2 0 3 0 5-1h0v1c-1 0-2 2-4 2-3 1-6 6-7 8-2 2-3 5-3 8h0v1h-3-5c-2 0-4 0-5-1h-2 0v-2c3-3 6-5 10-6l-1-1c1-1 3-2 4-3 2-1 4-2 6-2 1-1 2-1 3-1 0-1 1-1 1-2h-2c1 0 2 0 3-1z" class="F"></path><path d="M331 354c1 0 1-1 2-1s1 0 1 1v1h-3v-1z" class="P"></path><path d="M329 351c1 0 1 1 1 1 0 1 0 1-1 2h2v1h-5v-1h2c0-1 1-2 1-3z" class="K"></path><path d="M329 349v-1c1 1 1 0 1 1l-1 2c0 1-1 2-1 3h-2c0-2 1-3 3-5z" class="T"></path><path d="M331 344c2-1 5-3 7-3-1 2-5 3-7 4 1 1 1 1 2 1h0l-3 3c0-1 0 0-1-1v1l-1-2c1-1 1-1 3-2v-1z" class="W"></path><path d="M331 344v1c-2 1-2 1-3 2l1 2c-2 2-3 3-3 5v1c-2 0-4 0-5-1h-2 0v-2c3-3 6-5 10-6 0-1 1-1 2-2z" class="T"></path><path d="M328 347l1 2c-2 2-3 3-3 5v1c-2 0-4 0-5-1h3v-1h0c0-2 3-4 4-6z" class="J"></path><path d="M334 354c0-3 1-6 3-8 1-2 4-7 7-8 2 0 3-2 4-2v4h1 0 4l3 1h0v14h-1-14-7 0v-1h0z" class="I"></path><path d="M348 340h1 0 4l3 1c-2 1-5 1-7 1v13h-7l-1-3-1-1s1-1 0-1c0-1 1-3 1-5 1-1 2-2 3-2h1c1-1 0-1 1-1h2c1-1 1-1 0-2z" class="E"></path><path d="M343 350c1 0 1 0 2 1v1h-1-1v-2zm2-7h2l1 3h-2v2c-2 0-2 0-3-1 0-1 0-2 1-2v-2h1z" class="B"></path><path d="M334 354c0-3 1-6 3-8 1-2 4-7 7-8 2 0 3-2 4-2v4c1 1 1 1 0 2h-2c-1 0 0 0-1 1h-1c-1 0-2 1-3 2 0 2-1 4-1 5 1 0 0 1 0 1l1 1 1 3h-8 0v-1h0z" class="g"></path><path d="M334 354h3c0-1 1-1 1-2l-1-1c0-2 2-4 4-6 0 2-1 4-1 5 1 0 0 1 0 1l1 1 1 3h-8 0v-1h0z" class="W"></path><path d="M187 454l1-4c4 6 10 9 17 11 1 1 5 2 7 2l2 1 1 1v3 3h1v-1-3l1-1v5h1 2v-1-3h1l1 2c0-1 0-1 1-1v-1h-1v-1-2h2 7c0 1 1 2 1 3h0 2 0l-1 1h1v2h0v1h-4c1 0 1 0 2 1v6h-1l1 1v4 1h1 1 5 1 6c-1 1-4 0-6 1l1 1h0c-2 1-3 0-4 0s-1 1-2 1c0 0 0-1-1-1h-4c-1 2-1 3-1 5l-1 5v1 5h-1c1 2-1 4 1 6l1 1h1v2h0c-1 0-2-1-3-1 1 1 2 1 3 2 0 1-1 1 0 3-1 0-2 0-3 1 1 1 2 0 3 1v4s0-1-1-1h0v-2h0c-1-1-2-1-3-1s-2-1-3 0h-5c-1 0-2-1-4 0h0c0 1-1 2 0 3 0 1-1 3-1 4-2 6-3 12-6 18l-2 3c-1 1-3 5-3 6-2 3-4 7-6 10h-1c-1 1-2 1-2 2-1 1-1 2-2 3h-1l-1 1c-2-1-3-2-4-3h-1l-3 4c-2 0-3 0-5 1-3 1-6 3-9 5h-1l-2 1h-1-1c-1 2-3 3-4 5l-3 3h-1l-2 2s0 1-1 2h-1l-1 1c0-1-1-1-1-2h-1l-2 2c-1 1-2 2-2 3-4 0-7 2-10 4l-20 11h-2-1l-1 1h0c-1 0-3 1-4 2h0-1l-1 1c-1 0-1 0-2 1h-1s-1 0-2 1c1-1 1-2 3-3s5-3 7-4h2c1 0 2-1 2-2h1l-2-1 12-9 2-2h0 0c1 0 2-1 3-1 1-1 2-2 2-3l-1-1h1c2-1 5-3 7-5 2-3 5-5 7-8 1 0 3-1 4-2l3-4 9-12c1-2 4-7 6-9l1 1 1-1c1-2 3-4 4-6 4-7 8-14 10-23 1-1 1-4 2-6v-1c1 0 2 0 2-1-1-1-1 0-2-1h1c0-1-1-1-1-1 0-1-1-2-1-3 1 0 2 0 2-1 1-4 1-8 1-12l-1-6v-4-8-3l-1 1c-1 0-1 0-1-1l1-1c0-1 0-1-1-1v-1c1 0 1 0 1-1h1v-1h-2v-1c0-1 1-3 1-4s0-2 1-3v1-1h1l1-1v-2h0z" class="g"></path><path d="M185 525h1v2l-1 1h-1c1-1 1-2 1-3z" class="G"></path><path d="M194 503l-1-1c-1 0-2-2-2-3h2c0 1 1 2 1 2h1 0l-1 2zm-43 73l2-3h2c0 1-3 4-4 5v-2z" class="C"></path><path d="M190 471v-1c-1-1-2-2-2-3 1-1 2 0 3 0v1 3h-1z" class="K"></path><path d="M191 468l1 1-1 3-1 1h0c-1 0-2 0-2-1l-1-1h1 1 1 1v-3z" class="W"></path><path d="M191 527s0-1-1-1l-1-1c-1-3-3-6-2-9h0c0 1 1 2 1 2 0 3 2 6 4 8h0v1h-1z" class="G"></path><path d="M147 582c0 2-2 4-2 6h-1v-1h-3c2-2 4-3 6-5z" class="B"></path><path d="M191 528l1 1v1 1c-1 0-2 1-2 1-2 1-4 1-5 0-1 0-1 0-2-1h6c1-1 1-1 2-3z" class="R"></path><path d="M189 512c1 0 2 2 3 2l1 2v3 4h0 0c-1-2 0-5-2-7-1-1-2-2-2-4zm-12 36h0v-2c1 0 2 1 3 1s1 0 2 1h-2 0-2c1 1 3 2 4 3h2c1 1 1 2 2 2l-2 1c-2-3-5-4-7-6z" class="C"></path><path d="M151 576v2h-1 0c1 2-2 6-3 8l-2 2h0c0-2 2-4 2-6 0-1 1-3 2-3 1-1 1-2 2-3z" class="G"></path><path d="M187 509c1 0 1-1 2-2l1 1c0 1 2 3 3 4v2h-1c-1 0-2-2-3-2l-2-2v-1z" class="W"></path><path d="M187 509c1 0 1-1 2-2l1 1h-1c0 1 0 1 1 2-1 0-1 0-2-1h-1z" class="T"></path><path d="M125 591c2 0 4-1 5-2-1 3-6 6-9 8v-1l-1 1h0l3-3v-1l2-2z" class="K"></path><path d="M111 602l12-9v1l-3 3h0l1-1v1l-8 6-2-1z" class="L"></path><path d="M129 586h1c2-1 5-3 7-5 0 1-1 2-2 3h1c-2 2-4 4-6 5-1 1-3 2-5 2h0 0c1 0 2-1 3-1 1-1 2-2 2-3l-1-1z" class="P"></path><path d="M155 573c1-1 1-2 2-2 0 2-3 6-5 9-1 1-3 4-4 6h-1c1-2 4-6 3-8h0 1c1-1 4-4 4-5z" class="i"></path><path d="M184 543c-1 0-2 0-3 1-1 0-2-1-3 0h0l-1-1h0c1 0 1 0 2-1h-2 0l1-1c3 0 5-3 7-4v2h0l1 1v1h-2v2z" class="C"></path><path d="M185 457h1c1 1 1 2 0 3 0 1 0 2-1 3v10-3l-1 1c-1 0-1 0-1-1l1-1c0-1 0-1-1-1v-1c1 0 1 0 1-1h1v-1h-2v-1c0-1 1-3 1-4s0-2 1-3v1-1z" class="R"></path><path d="M144 573c1 0 3-1 4-2-3 5-8 10-12 13h-1c1-1 2-2 2-3 2-3 5-5 7-8z" class="L"></path><path d="M187 454h0v1h1l3 3c0 1 3 3 3 4v6c0 1 0 2-1 3l-1 2h0l-1-1 1-3-1-1v-1c0-1 1-2 2-2-1-1-1-3-1-4-1-1-3-3-4-5h-1v-2z" class="B"></path><path d="M193 465c0 1-1 3-1 4l-1-1v-1c0-1 1-2 2-2z" class="G"></path><path d="M210 478l2 1v3 2h6 2v1h-5l-1 1-1 3h0c-1-1-2-1-3-1 1-1 0-3 0-4v-6z" class="D"></path><path d="M191 491l1 1h0l1 1 1-1c1 1 2 2 3 2l3 2c-2 1-2 1-4 1v1c-1 1 0 1-1 3v-2h0l-1-1h-1l-1-2v-1c-1-1-1-3-1-4z" class="C"></path><path d="M194 492c1 1 2 2 3 2-1 1-1 1-1 2l-3-3 1-1z" class="K"></path><path d="M196 496c0-1 0-1 1-2l3 2c-2 1-2 1-4 1 1 0 1 0 1-1h-1z" class="P"></path><path d="M191 491c0-1 0-3 1-4l2-2v-1c-1 1-1 1-2 1 0-1 1-1 2-1h0c0-1 1-1 2-2h0c-2 0-4 0-6 1 0-1 3-1 4-1 1-1 6 0 6 1 1 0 2 1 2 1l-1 1c-1 0-4-1-5 0-2 1-2 3-3 5v1l1 1-1 1-1-1h0l-1-1z" class="F"></path><path d="M141 587h3v1h1 0c-1 1-2 2-2 3-4 0-7 2-10 4 0-1 0-2 1-3s3-2 4-3c1 0 2-1 3-2z" class="S"></path><path d="M141 587h3v1c-2 1-4 3-6 3v-2c1 0 2-1 3-2z" class="X"></path><path d="M196 497c2 0 2 0 4-1 1 1 1 2 1 3s0 2-1 3h-1-1v1 1 1l-1 1h-2v-1c0-1-1-2-1-2l1-2h0c1-2 0-2 1-3v-1z" class="K"></path><path d="M194 503l1-2 1 1v2h0l1 1h1l-1 1h-2v-1c0-1-1-2-1-2z" class="E"></path><path d="M196 497c2 0 2 0 4-1 1 1 1 2 1 3s0 2-1 3c-1-1-1-2-1-3-1-1-2-1-3-1h0v-1z" class="R"></path><path d="M168 546v1l-11 16v-1c1-2 3-3 3-4-2 1-4 4-6 6 0 2-2 2-3 3l9-12c1-2 4-7 6-9l1 1 1-1z" class="J"></path><path d="M152 580h1c4-4 6-8 8-12 1-1 2-3 3-4 1-2 1-4 3-5v1c0 1-1 3-2 4-2 5-5 9-7 12l-3 5c-1 0-1 1-2 1l1 1h1-1l-2 2s0 1-1 2h-1l-1 1c0-1-1-1-1-2 1-2 3-5 4-6zm39-108l1 1h0 2v-1l3 1h0l2 1v1l1 1c2 0 4 1 6 3v3 1c-3-2-5-3-7-4-1-1-3-2-4-3-2-1-4-1-5-3h0l1-1z" class="F"></path><path d="M194 472l3 1h0c-1 1-2 1-3 1v-1-1z" class="B"></path><path d="M210 471s0 1 1 2c-1 1-1 2-2 4h0 0v4h-1v-1h-2c0 1 0 2 1 3h-1v-1-3c-2-2-4-3-6-3l-1-1v-1l-2-1h0 8 1v-2h1l1 1 2-1z" class="C"></path><path d="M208 480c-1-1-2-2-2-3v-2c1 1 2 2 3 2h0 0v4h-1v-1z" class="L"></path><path d="M197 473h8v2 1l-1-1c-1-1-4-1-5-1l-2-1h0z" class="P"></path><path d="M210 471s0 1 1 2c-1 1-1 2-2 4-1 0-2-1-3-2 1-1 1-2 2-3l2-1z" class="b"></path><path d="M184 511v5c-1 1-1 3-1 3 0 3-1 4-1 7-1 2-2 3-3 5l-5 9c-1 2-2 4-3 5s-1 2-2 2h0-1v-1c1-2 3-4 4-6 4-7 8-14 10-23 1-1 1-4 2-6z" class="W"></path><path d="M201 499h1v1c-1 1-1 2-1 3l2 1v2c0 1-1 3-1 5l-2 5v2l-1-1v-1h-1 0-2 0 0c0 1 0 1-1 1v2 1c-1-1-1-2-2-4l-1-2h1l1 1v-1c0-1 1-2 0-3 0-2-1-3-2-5 2 1 3 3 4 3s2-2 2-3h0-1l1-1v-1-1-1h1 1c1-1 1-2 1-3z" class="E"></path><path d="M199 502h1c0 2-1 2-1 3l-1 1h0 0-1l1-1v-1-1-1h1z" class="W"></path><path d="M201 503l2 1v2c0 1-1 3-1 5l-2 5v2l-1-1v-1-2l2-11z" class="O"></path><path d="M199 514c0 1 0 2 1 2v2l-1-1v-1-2z" class="I"></path><path d="M176 549l-1-1c-1 0-2-1-2-1 1 0 2 0 4 1 2 2 5 3 7 6l2-1 3 3 1-1c0-1 1-1 1-1l1-1c1-1 1-2 2-3v3c0 1-1 2-1 3l-3 5v2c-1 0-1 1-2 1s-2-1-3 0h-1l1-1v-1l-1 1h0l-1-2-3-6-2-2v-1h-1c-1-1-1-2-1-3z" class="K"></path><path d="M190 555h0v3h-1v-2h0l1-1z" class="B"></path><path d="M184 554l2-1 3 3h0l-1 1-4-3z" class="D"></path><path d="M182 556c3 2 4 4 7 4l1 1v2c-1 0-1 1-2 1s-2-1-3 0h-1l1-1v-1c0-1-1-2-1-3-1-1-1-2-2-3z" class="G"></path><path d="M185 562h1v-1c1 0 1 0 2 1l1 1h1c-1 0-1 1-2 1s-2-1-3 0h-1l1-1v-1z" class="X"></path><path d="M176 549c2 1 4 2 5 3l-1 2c1 0 2 1 2 2 1 1 1 2 2 3 0 1 1 2 1 3l-1 1h0l-1-2-3-6-2-2v-1h-1c-1-1-1-2-1-3z" class="W"></path><path d="M202 484c3 1 4 2 5 5v3 1 1l-1 3-1 2-2-1-1 1h-1c0-1 0-2-1-3l-3-2c-1 0-2-1-3-2l-1-1v-1c1-2 1-4 3-5 1-1 4 0 5 0l1-1z" class="T"></path><path d="M197 487v1 2s-1 0-1 1v-1c-1-1-1-2 0-3h1z" class="W"></path><path d="M198 492c3 0 4 1 6 3l1 1 1 1-1 2-2-1h1v-1c-2-3-3-3-6-5z" class="R"></path><path d="M202 484c3 1 4 2 5 5v3 1 1l-1 3-1-1-1-1c-2-2-3-3-6-3 0 0-1-1-2-1 0-1 1-1 1-1v-2-1-1c1 0 1 1 2 1h2c1 0 1 1 2 1 0 0 1-1 1-2-1 0-2-1-3-1l1-1z" class="K"></path><path d="M197 488h1l1 1c-1 0-1 1-2 1v-2z" class="B"></path><path d="M199 489h1l1 1h0-2v-1z" class="G"></path><path d="M204 489h3v3 1 1l-1 3-1-1-1-1v-1-3c0-1 0-1-1-1v-1h1z" class="B"></path><path d="M204 491c0-1 0-1-1-1v-1h1l3 1v1c-1 1-2 0-3 0z" class="b"></path><path d="M205 496v-3h2v1l-1 3-1-1z" class="P"></path><path d="M207 492l1 1c1-1 1-1 2-1v1l1 2-1 1v1h0c1 1 1 2 2 2v1l1 1v1l-1 1c0 1-1 1-2 2 1 0 0 1 1 1l-6 9c0 1 0 3-1 4v1l-1-1-2 4-1 1v1 1 1-8-1-2l2-5c0-2 1-4 1-5v-2l-2-1c0-1 0-2 1-3v-1l1-1 2 1 1-2 1-3v-1-1z" class="g"></path><path d="M210 505c1 0 0 1 1 1l-6 9c0 1 0 3-1 4v1l-1-1c0-1 0-2 1-3 1-3 4-10 6-11z" class="F"></path><path d="M207 492l1 1c1-1 1-1 2-1v1l1 2-1 1v1h0c1 1 1 2 2 2v1c-1 0-2 0-2-1-1 1-1 1-1 2h-2c0 1 0 2-1 3-1 2-2 3-2 5 0 1-1 1-2 2 0-2 1-4 1-5v-2l-2-1c0-1 0-2 1-3v-1l1-1 2 1 1-2 1-3v-1-1z" class="C"></path><path d="M207 492l1 1c1-1 1-1 2-1v1l-2 2c0-1 0-1-1-1h0v-1-1z" class="W"></path><path d="M203 498l2 1-1 2c0 2 0 3-1 5v-2l-2-1c0-1 0-2 1-3v-1l1-1z" class="N"></path><path d="M203 498l2 1-1 2s-1 0-2-1v-1l1-1z" class="c"></path><path d="M178 553l2 2 3 6 1 2h0l1-1v1l-1 1-3 4c-2 0-3 0-5 1-3 1-6 3-9 5h-1l-2 1h-1-1c0-2 3-6 5-8 1-2 2-4 3-7 1-1 2-3 3-5l2-1h0l2-1h1z" class="S"></path><path d="M175 563l1-2h1c1 2 1 2 0 3h0c-1 0-1-1-2-1z" class="B"></path><path d="M175 554h0l2-1c1 2 3 4 3 5l-1 2-1-1-1-2c0-1-1-2-2-3z" class="C"></path><path d="M177 564l-1 2c2 0 3-2 4-3 0-1 0-1 1-1v1c0 1-1 1-1 2h0c-1 1-3 2-4 3-1 0-2 0-2 1-3 1-5 3-8 5l-2 1h-1c3-3 6-6 8-9 1 0 3-3 4-3s1 1 2 1z" class="P"></path><path d="M175 554c1 1 2 2 2 3l1 2c-2 1-4 4-6 3h0c-1 1-2 1-2 3l1 1c-2 3-5 6-8 9h-1c0-2 3-6 5-8 1-2 2-4 3-7 1-1 2-3 3-5l2-1z" class="G"></path><path d="M175 554c1 1 2 2 2 3-1 1-1 2-2 2v-3h-1l-1-1 2-1z" class="E"></path><path d="M175 559c1 0 1-1 2-2l1 2c-2 1-4 4-6 3l3-3z" class="J"></path><path d="M187 454l1-4c4 6 10 9 17 11 1 1 5 2 7 2l2 1 1 1v3c-2 1-4 2-5 3l-2 1-1-1h-1v2h-1-8l-3-1v1h-2l1-2c1-1 1-2 1-3v-6c0-1-3-3-3-4l-3-3h-1v-1z" class="g"></path><path d="M191 458c2 1 6 2 8 3l3 3c-1 0-4-1-5-1v1c1 0 4 1 4 2v1c-1-1-2-1-4-1v1c-1 2 0 2-2 3l-1 1s1 1 0 1v1h-2l1-2c1-1 1-2 1-3v-6c0-1-3-3-3-4z" class="T"></path><path d="M195 470v-1c0-1 0-2 1-3l1 1c-1 2 0 2-2 3z" class="E"></path><path d="M193 516c1 2 1 3 2 4v-1-2c1 0 1 0 1-1h0 0 2 0 1v1l1 1v1 8-1-1-1l1-1 2-4 1 1c-2 2-2 5-3 7 0 3-1 5-2 7 0 2-1 6-2 7-2 2-3 3-4 5l-1 1 1 1c0 1-1 2-1 3l2-1h0c-1 1-1 2-2 3l-1 1-1-2-1 1-3-2-1-1h-1-1v-1c0-1 2-1 3-2h1 0l1-1v-1c-2 0-3 1-4 1v-1c1-1 3-1 4-2v-1c-1-1-3 0-4 1v-2h2v-1l-1-1h0v-2c1 0 3-1 4-1 1-2 3-3 3-4v-1-1-1l-1-1v-1h1v-1s0-2 1-3h0 0v-4-3z" class="D"></path><path d="M198 516h1v1l1 1v1h0l-1 2h-1v-5z" class="Y"></path><path d="M185 537c1 0 3-1 4-1v1c-1 1-2 2-4 2h0v-2z" class="G"></path><path d="M200 519l-2 11v-9h1l1-2z" class="R"></path><path d="M191 527h1 0c1 3 2 3 0 6 0 2-2 3-3 4v-1c1-2 3-3 3-4v-1-1-1l-1-1v-1z" class="B"></path><path d="M203 519l1 1c-2 2-2 5-3 7 0 3-1 5-2 7 0 2-1 6-2 7-2 2-3 3-4 5h0c0-1 0-1-1-1s-2 1-3 2v-1c3-2 6-6 8-10s3-9 4-13l2-4z" class="i"></path><path d="M189 546v1c1-1 2-2 3-2s1 0 1 1h0l-1 1 1 1c0 1-1 2-1 3l2-1h0c-1 1-1 2-2 3l-1 1-1-2-1 1-3-2-1-1h-1v-1c1-1 3-2 5-3z" class="B"></path><path d="M185 550c0-1 0-1 1-1 1-1 1-1 2-1v2h0l2-1 1 1-1 1h-1v-1c-1 0-2 0-3 1l-1-1z" class="E"></path><path d="M192 547l1 1c0 1-1 2-1 3l2-1h0c-1 1-1 2-2 3l-1 1-1-2-1 1-3-2c1-1 2-1 3-1v1h1l1-1c1-1 1-2 1-3z" class="G"></path><path d="M239 484h1 6c-1 1-4 0-6 1l1 1h0c-2 1-3 0-4 0s-1 1-2 1c0 0 0-1-1-1h-4c-1 2-1 3-1 5l-1 5v1 5h-1-4 0-3-7v-1l-1-1v-1c-1 0-1-1-2-2h0v-1l1-1-1-2v-1h2 0c0-2-1-2-2-4 1 0 2 0 3 1h0l1-3 1-1h5v-1c4 0 8 1 12 0h1 1 5z" class="H"></path><path d="M212 495c2 0 3 1 4 1v1l-1 1c-1 0-2-1-3-1v-2z" class="k"></path><path d="M223 497l1-1c0-1 0-2 1-2l1 2h0v3c0 1 0 1-1 1h-9c3-1 5 0 8-1 0-1 0-1-1-2z" class="Q"></path><path d="M221 495c1 1 1 1 1 2v1c-2-1-4 0-5-1h-1c0-1 1-2 1-2 1-1 2-1 4 0h0z" class="G"></path><path d="M216 500h9 0c-1 0-2 1-3 1h0c1 0 4 0 5 1h-4 0-3-7v-1l-1-1h4z" class="B"></path><path d="M210 488c1 0 2 0 3 1h0v2l-1 4v2 2c-1 0-1-1-2-2h0v-1l1-1-1-2v-1h2 0c0-2-1-2-2-4z" class="S"></path><path d="M221 487c2 2 3 4 4 7h0c-1 0-1 1-1 2l-1 1c0-1 0-2-1-3l-1 1h0l-2-2v-2c0-2 1-2 1-3l1-1z" class="M"></path><path d="M220 488c1 1 2 2 1 4h0v-1h-2c0-2 1-2 1-3z" class="U"></path><path d="M219 491h2v1h0l1 2-1 1h0l-2-2v-2z" class="h"></path><path d="M214 486l1-1h5l1 2-1 1c0 1-1 1-1 3v2h-1v-2c-2-1-3-1-5 0v-2l1-3z" class="V"></path><path d="M214 486l1-1h5l1 2-1 1c0 1-1 1-1 3v2h-1v-2-1c1-1 0-2 0-3-1-1-1 0-2 0v-1h-2z" class="I"></path><path d="M239 484h1 6c-1 1-4 0-6 1l1 1h0c-2 1-3 0-4 0s-1 1-2 1c0 0 0-1-1-1h-4c-1 2-1 3-1 5l-1 5v1 5h-1c-1-1-4-1-5-1h0c1 0 2-1 3-1h0c1 0 1 0 1-1v-3h0l-1-2h0c-1-3-2-5-4-7l-1-2v-1c4 0 8 1 12 0h1 1 5z" class="C"></path><path d="M221 487c2 0 3 0 4 1 0 1 1 2 1 4-1 1 0 3 0 4l-1-2h0c-1-3-2-5-4-7z" class="F"></path><path d="M224 464h7c0 1 1 2 1 3h0 2 0l-1 1h1v2h0v1h-4c1 0 1 0 2 1v6h-1l1 1v4 1c-4 1-8 0-12 0h-2-6v-2-3l-2-1v-1h-1 0c1-2 1-3 2-4-1-1-1-2-1-2 1-1 3-2 5-3v3h1v-1-3l1-1v5h1 2v-1-3h1l1 2c0-1 0-1 1-1v-1h-1v-1-2h2z" class="i"></path><path d="M228 476v-1c1 1 2 2 2 3h-4l1-2h1z" class="H"></path><path d="M221 481v2c1 0 2-1 3-1 1 1 3 0 5 0 1 1 2 1 3 1v1h-9-3c1-1 1-2 1-3h0z" class="f"></path><path d="M222 472h3v1 1 1l2 1-1 2h-1 0c-1 0-3 0-4-1 1-1 2-1 2-2h-1v-2-1z" class="H"></path><path d="M225 475l2 1-1 2h-1s-1 0-1-1 1-1 1-2z" class="n"></path><path d="M222 472h3v1c-1 1-2 2-3 2v-2-1z" class="k"></path><path d="M217 472h3 0v1 4l-1 1-3-1h0c-1 1-1 1-2 1v-2h1v-1c-1-1-1-2-1-3h3z" class="h"></path><path d="M217 472h3 0v1c-1 1-2 1-3 2h-1 0-1c-1-1-1-2-1-3h3z" class="k"></path><path d="M220 472v1c-1 1-2 1-3 2h-1 0l1-3h3z" class="m"></path><path d="M225 472h7v6h-2c0-1-1-2-2-3v1h-1l-2-1v-1-1-1z" class="h"></path><path d="M225 474h2s0 1 1 2h-1l-2-1v-1z" class="m"></path><path d="M232 472v6h-2c0-1-1-2-2-3 1 0 2 1 3 1h1v-4z" class="Q"></path><path d="M210 471c1-1 3-2 5-3v3h1c0 1 0 1 1 1h-3c0 1 0 2 1 3v1h-1v2h-2l-1-1h-1-1 0c1-2 1-3 2-4-1-1-1-2-1-2z" class="D"></path><path d="M210 471c1-1 3-2 5-3v3h1c0 1 0 1 1 1h-3l1-1-1-1c-1 1-2 1-3 3-1-1-1-2-1-2z" class="N"></path><path d="M212 482c0-1 0-2 1-3 2 0 5-1 7 0-1 1-1 1-1 2h1c0 1 0 2-1 3h-1-6v-2z" class="H"></path><path d="M215 479h5c-1 1-1 1-1 2h-1-1-2v-2z" class="k"></path><path d="M215 479v2h2 1v1 1c-1-1-2-1-3-1s-1 1-2 0v-2-1h2z" class="l"></path><path d="M221 481v-2c3-1 7 0 10 0h1v4c-1 0-2 0-3-1-2 0-4 1-5 0-1 0-2 1-3 1v-2z" class="k"></path><path d="M231 479h1v4c-1 0-2 0-3-1-2 0-4 1-5 0h1 1 4v-1h-1v-1h2 0v-1z" class="H"></path><path d="M224 464h7c0 1 1 2 1 3h0 2 0l-1 1h1v2h0v1h-4c1 0 1 0 2 1h-7-3-2-3c-1 0-1 0-1-1v-1-3l1-1v5h1 2v-1-3h1l1 2c0-1 0-1 1-1v-1h-1v-1-2h2z" class="M"></path><path d="M232 468h1 1v2h0v1h-4-4c2-1 4 0 6 0v-3h0z" class="O"></path><path d="M216 470v-3l1-1v5h1 2v-1-3h1l1 2h1l-1 2h4 0 4c1 0 1 0 2 1h-7-3-2-3c-1 0-1 0-1-1v-1z" class="B"></path><path d="M224 464h7c0 1 1 2 1 3h0 2 0l-1 1h-1l-1 1h0c-1-1-1-1-2-1l-1 1c-1 0-1-1-2-1-1 2-1 1-3 1h-1c0-1 0-1 1-1v-1h-1v-1-2h2z" class="H"></path><path d="M224 464h7c0 1 1 2 1 3-1 0-2 0-3-1h-1l-2-1c-2 0-3 0-4 1v-2h2z" class="U"></path><path d="M227 502c1 2-1 4 1 6l1 1h1v2h0c-1 0-2-1-3-1 1 1 2 1 3 2 0 1-1 1 0 3-1 0-2 0-3 1 1 1 2 0 3 1v4s0-1-1-1h0v-2h0c-1-1-2-1-3-1s-2-1-3 0h-5c-1 0-2-1-4 0h0c0 1-1 2 0 3 0 1-1 3-1 4-2 6-3 12-6 18l-2 3c-1 1-3 5-3 6-2 3-4 7-6 10h-1c-1 1-2 1-2 2-1 1-1 2-2 3h-1l-1 1c-2-1-3-2-4-3 1-1 2 0 3 0s1-1 2-1v-2l3-5c0-1 1-2 1-3v-3h0l-2 1c0-1 1-2 1-3l-1-1 1-1c1-2 2-3 4-5 1-1 2-5 2-7 1-2 2-4 2-7 1-2 1-5 3-7v-1c1-1 1-3 1-4l6-9c-1 0 0-1-1-1 1-1 2-1 2-2l1-1h7 3 0 4z" class="o"></path><path d="M213 511h0l1 2v1h-2c0-1 0-2 1-3z" class="a"></path><path d="M214 513h3 1v1h1l1 1h-2-2l-1 1-3-1v-1h2v-1z" class="H"></path><path d="M216 515h-1c1-1 1-1 2-1s1 0 1 1h-2z" class="h"></path><path d="M204 519l1 1 1-1c1-2 1-4 3-4 0 1-2 5-3 7-1 4-1 8-1 13h-1v4h0l-1-3v3l-2-1c0-1 0-1-1-2l-1-2c1-2 2-4 2-7 1-2 1-5 3-7v-1z" class="C"></path><path d="M218 510h9c1 1 2 1 3 2 0 1-1 1 0 3-1 0-2 0-3 1 1 1 2 0 3 1v4s0-1-1-1h0v-2h0c-1-1-2-1-3-1s-2-1-3 0h-5c-1 0-2-1-4 0h0l1-1 1-1h2 2l-1-1h-1v-1h-1-3l-1-2h0 1c1-1 3-1 4-1z" class="a"></path><path d="M213 511h1c2 0 5 1 7 1v1l-1 1c-1-1-1-1-2-1h-1-3l-1-2h0z" class="H"></path><path d="M213 511c2 0 3 1 5 1v1h-1-3l-1-2z" class="h"></path><path d="M218 510h9c1 1 2 1 3 2 0 1-1 1 0 3-1 0-2 0-3 1 1 1 2 0 3 1v4s0-1-1-1h0v-2h0c-1-1-2-1-3-1s-2-1-3 0h-5c-1 0-2-1-4 0h0l1-1 1-1h2 2 9c-1 0-1-1-1-1l-1-1c0-1-2-2-2-2-2-1-5-1-7-1z" class="i"></path><path d="M227 502c1 2-1 4 1 6-3 1-7 0-10 1h-2l-3 1-1-1v-2l-1-1c-1 0 0-1-1-1 1-1 2-1 2-2l1-1h7 3 0 4z" class="Q"></path><path d="M219 506l1-2h1 0v2 1l-2-1z" class="H"></path><path d="M215 504h2 1l-1 2 1 1h-3 0-2c0-1 2-2 2-3z" class="h"></path><path d="M212 503v2c1-1 1-1 1-2l2 1c0 1-2 2-2 3h2 0-1v1l1 1h0l1-1v1l-3 1-1-1v-2l-1-1c-1 0 0-1-1-1 1-1 2-1 2-2z" class="H"></path><path d="M212 503v2c0 1 0 4 1 5l-1-1v-2l-1-1c-1 0 0-1-1-1 1-1 2-1 2-2z" class="T"></path><defs><linearGradient id="d" x1="225.948" y1="505.57" x2="221.445" y2="508.55" xlink:href="#B"><stop offset="0" stop-color="#878586"></stop><stop offset="1" stop-color="#9e9d9e"></stop></linearGradient></defs><path fill="url(#d)" d="M227 502c1 2-1 4 1 6-3 1-7 0-10 1 0-1 0-2 1-3h0l2 1v-1h1v-2c-1-1-1-2-2-2h3 0 4z"></path><path d="M220 502h3 3c0 1 0 1-1 2s-1 2-3 2h0v-2c-1-1-1-2-2-2z" class="U"></path><path d="M199 534l1 2c1 1 1 1 1 2l2 1v-3l1 3h0v-4h1c-1 3-1 5 1 7h1l-2 3c-1 1-3 5-3 6-2 3-4 7-6 10h-1c-1 1-2 1-2 2-1 1-1 2-2 3h-1l-1 1c-2-1-3-2-4-3 1-1 2 0 3 0s1-1 2-1v-2l3-5c0-1 1-2 1-3v-3h0l-2 1c0-1 1-2 1-3l-1-1 1-1c1-2 2-3 4-5 1-1 2-5 2-7z" class="K"></path><path d="M205 535c-1 3-1 5 1 7h1l-2 3-1-1v-3-6h1z" class="T"></path><path d="M194 550c1 0 2-1 2-2v-2c1-1 3-3 5-4-1 2-3 4-4 6 0 1 0 3-1 4l-2 1v-3z" class="B"></path><path d="M194 553l2-1c0 1-1 2 0 3v1c-1 2-2 4-3 5h-1v-2c1-1 1-2 1-3s1-2 1-3z" class="d"></path><path d="M190 561l3-5c0 1 0 2-1 3v2h1l-3 5-1 1c-2-1-3-2-4-3 1-1 2 0 3 0s1-1 2-1v-2z" class="e"></path><path d="M201 542l1-2h1c0 1 1 3 0 4-1 4-4 9-7 12v-1c-1-1 0-2 0-3 1-1 1-3 1-4 1-2 3-4 4-6z" class="M"></path><path d="M174 85c3 0 6 0 8-1 1 0 2 1 2 1 2 0 7 0 9-1h0l1 1c4 0 8 0 12-1V73c1 4 0 8 1 12 2 0 9 0 11-1v-1c1 0 1 1 1 2h7c0-1 1-1 1-1 3 1 6 1 9 1h17v-1l1-2c0 1 0 2 1 3h7c1 0 3 0 4-1h1v1h8c4 0 8 0 11-1l1-1c0 1 0 1 1 2h24 13c1 0 4 0 5-1l2 1h13 34-2l1 1h0v2 3h2l4 1c-2 1-4 0-6 0h-11-4l-2 3-1 1h0c-2 0-3 1-3 2-1 1-1 2-1 3h0v-1l1 1-2 5c-1 1-1 2-1 3-3-1-4-2-6-3h0l2 3h0l-4-2v2h0v2c1 0 4 1 5 3l-2 1 2 1 2 2v3h0c-1 1-1 1-2 1v1l1 2v1c1 2 4 4 6 6l-1 1c0-1-1 0-2-1h-3c-1 1-3 1-4 2-5 1-10 5-13 8h-1c-2-1-3-1-5-1l-5-2c-1-1-2-1-3-1l-5-2h-2v1l-1 1c0 1-3 6-4 7l-6-2c-5-1-12-2-16-1-1 0-2 1-3 2-2 2-3 4-4 7-1-1-1-2-1-3h0-5c0-1-1-1-1-1l-1 2v-3c-1-3-2-5-5-7-2-1-5-2-7-3h-2v4 5c-2 0-4-1-6-1h2l3 2c1 0 1 0 1 1l1 12c-1 1-2 1-3 2h-6-5c-2 0-4 0-5-1l-1-3-1-19h-6v10c0-1 0-2-1-3 0 3 0 6-1 8v3c0 2-1 2-1 3h-2c-2 0-3-1-5-1 0 1-1 0-1 0-1 0-2-1-3-1-2 0-3 0-4-1v-1h-2v-2c1-2 1-6 1-8h-1c-1-1-1-2-2-3-1-2-3-3-4-5l-2-2c0-1-1-1 0-2-1-1-1 0-2 0l-3-3h-1l3 3-2 2 2 2h1 1 1c0 1 0 2-1 3 1 0 1 0 1 1v3 1c-1 1-1 4-2 5v1c-1 2-1 3-1 4h0l-2 4h0c-1 0-2 0-3 1h-2v-1h0-3l-1-1-1 1v1h-1c-1-1-2-3-4-3v1h-1v-1c1-1 1-1 2-1 0 0 0-1-1-1h-1c-1 1-1 2-1 4l-1-3h0s0-2-1-2l-1-1h1v-1h-1v-1-1s-1 0-1-1h1l-1-1v-1c0-1-1-2-2-4h0l-1-1c-1-1-1-1-1-2s-1-2-1-2c-1-2-2-3-3-5-1-1-1-2-2-2 0-1-1-1-1-2l-5-6-2-2h-1v-1l-1-1h0c-1 0-2-1-3-2h0l-1-2s-1 0-2-1h0-1l-2-2-1-1h0c-1-1-2-1-2-2-2-1-3-1-4-2 0 0-2-1-2-2l-3-1 1-1h-1l-1-1c-1 0-2-1-2-2h0l-1-1c-1 0-2 0-4 1-1-1-2-1-3-2-4-2-9-4-14-5l-2-1c2-1 7 0 10-1-4 0-10 1-13 0v-2c0-1 3-1 4-1v-3c2 0 5 1 7 0h0c3-1 5 0 7 0h12 27 7 2v-1c1 0 1 1 1 1z" class="o"></path><path d="M346 95h1c0 1-1 1-1 2v1h-1v-3h1z" class="T"></path><path d="M259 107c2 0 3 0 4 1h1c-1 1-3 0-4 0l-1-1z" class="P"></path><path d="M330 100h4l1 1c-1 0-1 1-1 1h-2l-2-2h0zm-122-5c1 0 3 1 5 1v1h-1c-2 0-3-1-5-2h1z" class="B"></path><path d="M197 95h4v2h-2c-1 0-1 0-2-1h0v-1zm-64 4c0-2-1-4 0-5h0l2 4-1 2h0l-1-1z" class="D"></path><path d="M263 97l6-2-5 5c0-1-1-2-1-3z" class="e"></path><path d="M260 108h-9v-1h8l1 1z" class="B"></path><path d="M175 95c2 0 6 0 7 1l1 2h-5c-1-2-1-2-3-3z" class="K"></path><path d="M325 98c2 0 4 1 5 2h0l2 2v1c-1 0-2-1-4-1l-2-3-1-1z" class="C"></path><path d="M196 107c-1 0-1-4-1-5v-3-1c-1-1 0-2 0-3h2v1c0 1-1 2-1 3-1 2 0 5 0 7v1z" class="T"></path><path d="M135 98l1 2c1 1 2 0 3-1 2-2 4-4 7-5l-6 5-3 3h1 1c0 1 0 1 1 1h-2-1l-1-1c-1 0-2-1-2-2l1-2z" class="S"></path><path d="M356 101v-1l1 1-2 5c-1 1-1 2-1 3-3-1-4-2-6-3v-1s1 1 2 1h0 2c1-1 1-2 2-3l2-2z" class="D"></path><path d="M350 95h0 1v2c-1 2-1 4 0 5s1-1 2-2c0 0 1 0 1-1l1 1-2 2v1h-2l-1 1-1 1c0-1-1-2-1-2v-4c0-2 1-2 2-4z" class="W"></path><path d="M324 104c-1-1-2-3-2-4-1-2-2-3-3-5l4 1 1 1 1 1 1 1v2h-1v2h0v2h0 0l-1-2v1z" class="C"></path><path d="M324 97l1 1 1 1v2h-1v2h0v2h0 0l-1-2-2-4 1-1 1 1v-2z" class="S"></path><path d="M264 100l-2 1c-4 1-9 0-12-1 2-1 4 0 7-1 2 0 4-1 6-2 0 1 1 2 1 3z" class="N"></path><path d="M338 97l10 8v1h0l2 3h0l-4-2-6-4 1-1c-1-2-2-3-3-4v-1z" class="B"></path><path d="M334 100h1v-1c-1-1-4-3-6-3v-1h1 0c2 1 3 2 5 2h1l-1-2h0 0c1 0 3 1 3 2v1c1 1 2 2 3 4l-1 1c-2-1-4-2-5-2l-1-1z" class="C"></path><path d="M197 96h0c1 1 1 1 2 1h2c0 1-1 2-1 2v1h-1l1 2c0 1-1 3-1 4v1 2c-1-1-2-2-2-3h-1c0-2-1-5 0-7 0-1 1-2 1-3z" class="B"></path><path d="M197 96h0c1 1 1 1 2 1h2c0 1-1 2-1 2v-1c-2 1-2 1-4 1 0-1 1-2 1-3z" class="E"></path><path d="M197 106c-1-1-1-2 0-3 0-1 1-2 1-3l1-1h0l1 1h-1c-1 2-1 5-2 6z" class="L"></path><path d="M197 106c1-1 1-4 2-6l1 2c0 1-1 3-1 4v1 2c-1-1-2-2-2-3z" class="J"></path><path d="M253 85v-1 1 6c-3 0-6 1-9 0v-6h9 0z" class="f"></path><path d="M267 85h8v6c-1 0-7 1-8 0 0-1 0-2-1-2v-1h0v-3h1z" class="Q"></path><path d="M263 112l1-3c2-4 4-9 7-12-1 3-2 6-4 9 3-2 5-6 8-9-2 5-6 10-9 15 0 1-2 3-2 3-1 2-3 4-4 5l-1-1 1-1c1-1 3-2 3-4h-1c0-1 0-2 1-2z" class="c"></path><path d="M275 85c4 0 8 0 11-1l1 1-1 6c-3 0-7 1-11 0v-6z" class="U"></path><defs><linearGradient id="e" x1="337.992" y1="105.607" x2="341.037" y2="102.931" xlink:href="#B"><stop offset="0" stop-color="#504d4e"></stop><stop offset="1" stop-color="#626362"></stop></linearGradient></defs><path fill="url(#e)" d="M335 101c1 0 3 1 5 2l6 4v2h0v2c1 0 4 1 5 3l-2 1c-1 0-2-1-2-2l-6-3-1-1-4-3h0c-1-1-3-2-4-3v-1h2s0-1 1-1z"></path><path d="M332 102h2c2 1 4 2 7 4h-3-2 0c-1-1-3-2-4-3v-1z" class="F"></path><path d="M336 106h2 3l5 3h0v2c-2-1-5-4-6-4l1 1-1 1-4-3z" class="K"></path><path d="M340 109l1-1-1-1c1 0 4 3 6 4 1 0 4 1 5 3l-2 1c-1 0-2-1-2-2l-6-3-1-1z" class="B"></path><path d="M253 84l1-2c0 1 0 2 1 3h7c1 0 3 0 4-1h1v1h-1v3h0v3h-1c0 1-2 0-2 0h-9v-4c0-1 0-1-1-2v-1z" class="a"></path><path d="M268 114l6-9c3-3 7-5 11-7-2 2-5 4-7 6-1 1-1 2-1 3-1 1-2 1-2 3h0l-3 3c-2 3-4 6-7 9-1 1-2 2-4 3-1 0-2-1-3 1l-1-1c-2-1-4-1-6-2 1-1 2-1 3-2 2-1 4-1 6-3h0l-1 1 1 1c1-1 3-3 4-5v1c-1 3-4 4-6 5h1 0 2v-1c1 0 1 0 2-1s3-2 3-4c1 0 2-1 2-1z" class="N"></path><path d="M263 122c6-5 9-12 15-18-1 1-1 2-1 3-1 1-2 1-2 3h0l-3 3c-2 3-4 6-7 9h-2z" class="B"></path><path d="M264 115v1c-1 3-4 4-6 5h1 0 2v-1c1 0 1 0 2-1s3-2 3-4c1 0 2-1 2-1-1 3-4 7-8 8-1 0-3 0-4 1 1 1 2 0 3 0 2 0 3-1 4-1h2c-1 1-2 2-4 3-1 0-2-1-3 1l-1-1c-2-1-4-1-6-2 1-1 2-1 3-2 2-1 4-1 6-3h0l-1 1 1 1c1-1 3-3 4-5z" class="E"></path><path d="M140 99l1 1-1 1s0-1 1-1h1c2-1 3-2 4-3s2-3 3-3h3c2 0 4-1 6-1h0 4 1c3 1 7 2 11 2h1c2 1 2 1 3 3h-7c-1 0-2 1-3 1-2 1-5 1-7 1s-5 1-7 1l-3 3c-1 0-2 1-3 1 0 1-1 1-1 2-1 1 0 2-1 2-2-1-3-1-4-2 0 0-2-1-2-2l-3-1 1-1h2c-1 0-1 0-1-1h-1-1l3-3z" class="C"></path><path d="M139 102l8-3-2 2 1 1-5 1h-1c-1 0-1 0-1-1z" class="Y"></path><defs><linearGradient id="f" x1="170.092" y1="93.852" x2="163.924" y2="99.489" xlink:href="#B"><stop offset="0" stop-color="#565959"></stop><stop offset="1" stop-color="#736d6f"></stop></linearGradient></defs><path fill="url(#f)" d="M152 97c2-1 4-2 6-2h6l9 1 1-1h1c2 1 2 1 3 3h-7-7-5 0l1-1c-3 1-5 1-8 0z"></path><path d="M174 95h1c2 1 2 1 3 3h-7-7c2-1 6 0 9 0v-2l1-1z" class="B"></path><path d="M147 99l5-2c3 1 5 1 8 0l-1 1h0 5 7c-1 0-2 1-3 1-2 1-5 1-7 1s-5 1-7 1l-3 3c-1 0-2 1-3 1 0 1-1 1-1 2-1 1 0 2-1 2-2-1-3-1-4-2 0 0-2-1-2-2l-3-1 1-1h2 1l5-1-1-1 2-2z" class="G"></path><path d="M144 104c1-2 3-1 5-1l1-1h1l-1 1c-2 1-4 1-6 1z" class="E"></path><path d="M147 99l5-2c3 1 5 1 8 0l-1 1h0l-13 4-1-1 2-2z" class="O"></path><path d="M140 103h1c1 0 2 0 3 1 2 0 4 0 6-1l1 1c-1 0-2 1-3 1 0 1-1 1-1 2-1 1 0 2-1 2-2-1-3-1-4-2 0 0-2-1-2-2l-3-1 1-1h2z" class="T"></path><path d="M140 105c0 1 1 0 2 1h1 0 2c1 0 2 0 3-1 0 1-1 1-1 2-1 1 0 2-1 2-2-1-3-1-4-2 0 0-2-1-2-2z" class="C"></path><defs><linearGradient id="g" x1="249.649" y1="126.86" x2="225.009" y2="102.479" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2a2a2a"></stop></linearGradient></defs><path fill="url(#g)" d="M215 105h2c1 0 1 1 2 2v1s1 0 1 1l1-1c1 2 0 5 0 6v6h8 11 9v-6h0v-2h1s0-1 1-1c2 0 5-1 8-1 0 0 1 0 2 1 1 0 1 0 2 1-1 0-1 1-1 2h1c0 2-2 3-3 4h0c-2 2-4 2-6 3-1 1-2 1-3 2-5-1-11-1-15-1h-12-4-1c-1-1-3 0-5 0l-1-1h3l1-1v-2c-1-2-2-5-3-7l-1-1c1-2 2-3 2-5h0z"></path><path d="M219 108s1 0 1 1l1 1c-1 1-2 1-3 1s-1-1-1-1l1-2h1z" class="C"></path><path d="M249 112h1s0-1 1-1c2 0 5-1 8-1 0 0 1 0 2 1 1 0 1 0 2 1-1 0-1 1-1 2h1c0 2-2 3-3 4h0c0-1 0-2 1-3v-3c-2-2-9 1-11 1h0l-1 1h0v-2z" class="D"></path><path d="M215 105h2c1 0 1 1 2 2v1h-1l-1 2-1 1 3 6h0l1 1c1 1 1 1 0 2 0 0 0 1-1 0-1 0-2 1-3 1l1-1v-2c-1-2-2-5-3-7l-1-1c1-2 2-3 2-5h0z" class="X"></path><path d="M217 105c1 0 1 1 2 2v1h-1 0-1c-1 0-1 0-2 1 0-2 1-3 2-4z" class="B"></path><path d="M214 111h0l4 6h1 0l1 1c1 1 1 1 0 2 0 0 0 1-1 0-1 0-2 1-3 1l1-1v-2c-1-2-2-5-3-7z" class="S"></path><path d="M219 117h0c1 1 1 2 1 3h-1c-1-1-1-2-1-3h1z" class="i"></path><path d="M286 84l1-1c0 1 0 1 1 2h24 13c1 0 4 0 5-1l1 1v7h-44c0-2 1-5 0-7h0l-1-1z" class="V"></path><path d="M275 110c0-1 0-2 1-2 0-1 1-1 1-1 0-1 0-1 1-1h0v1h0c2-1 3-2 5-4 1 0 1 0 2-1h1c1-1 2-1 3-1 0 1-1 1-2 1-2 1-4 2-5 3-1 0-2 1-2 2h0c1 0 2-1 3 0 0 0 0 1-1 1l1 1c-2 2-4 5-4 7v1c-1 4-1 8-1 12h-3l-1 1h-2-4c-2 1-6 2-8 3l-1 1c0 1 0 1 1 2h0-2v4c-1-3 0-6 0-9v-3-2c1-2 2-1 3-1 2-1 3-2 4-3 3-3 5-6 7-9l3-3z" class="T"></path><path d="M269 125h0c-1 1-1 1-1 2s0 1-1 1c0 1-1 0-1 0-1 0-1 1-2 1v-1c2 0 3-2 5-3zm-6 4l-1 1v-1-1-1l2-2v2l-1 2h0z" class="W"></path><path d="M268 130l1-2h2c0 1 1 1 2 1h0c1-2 0-4-1-5h0c0-1 0-2 1-2h0 1c0 2 0 4 1 6v1l-1 1h-2-4z" class="G"></path><path d="M282 108l1 1c-2 2-4 5-4 7-2 3-2 6-2 10h-1v-1c0-1-1-3 0-5 1-4 4-8 6-12z" class="D"></path><path d="M264 125c3-2 4-5 7-7h1c0-1 1-2 2-2h0v1c-1 2-2 3-3 5l-2 3c-2 1-3 3-5 3v1h-1 0l1-2v-2z" class="G"></path><path d="M201 100c0-1 1-2 2-3h2c2 1 6 1 8 2h1v2h0l2-1v1h-1c0 1-1 2-1 4h1c0 2-1 3-2 5l1 1c1 2 2 5 3 7v2l-1 1h-3-1-2l-3-2s-1 0-1 1c-2-2-3-4-4-6-2-1-2-3-3-5v-2-1c0-1 1-3 1-4s0-1 1-2z" class="b"></path><path d="M205 115v-2h1 2c0 2-1 2-2 3h-1v-1h1-1z" class="N"></path><path d="M210 105l1 1h0v3h0v2h-1v-1l-1-1v-1c1-1 1-2 1-3z" class="c"></path><path d="M210 121h2c0-2 1-2 1-3l-1-1v-1h1c2 0 2 2 3 3l1-1v2l-1 1h-3-1-2z" class="P"></path><path d="M199 106l2 1 1-1h3c1 0 1-1 2-1h1v1l2-1h0c0 1 0 2-1 3v1l1 1c-1 1-1 2-2 3h0-2-1v2h1-1l-3-3c0-1-1-2-1-2l-2-3v-1h0z" class="Y"></path><path d="M205 108h2v1h-1-2l1-1z" class="I"></path><path d="M206 109h1c0 2 1 3 1 4h0-2v-4z" class="O"></path><path d="M210 105h0c0 1 0 2-1 3v1l-2-2s0-1 1-2v1l2-1z" class="e"></path><path d="M203 109v-2h1l1 1-1 1h2v4h-1v2h1-1l-3-3c0-1-1-2-1-2l2-1z" class="M"></path><path d="M203 109c0 1 0 1-1 3 0-1-1-2-1-2l2-1z" class="I"></path><path d="M204 109h2v4h-1v2c-1-2 0-4-1-6z" class="Y"></path><path d="M201 100c0-1 1-2 2-3h2c2 1 6 1 8 2h1v2h0l2-1v1h-1c0 1-1 2-1 4h1c0 2-1 3-2 5v-5h0l-2 1h0l-1-1h0l-2 1v-1h-1c-1 0-1 1-2 1h-3l-1 1-2-1h0c0-1 1-3 1-4s0-1 1-2z" class="S"></path><path d="M201 100c1 0 2 0 2-1 2 1 3 2 5 2-2 0-4 1-6 1-1 1-2 3-3 4h0c0-1 1-3 1-4s0-1 1-2z" class="B"></path><path d="M202 102c2 0 4-1 6-1 1 1 2 1 3 1l-1 1v1 1l-2 1v-1h-1v-1c0-1-1-1-2-1s-2 0-2-1h-1z" class="P"></path><path d="M202 102h1c0 1 1 1 2 1s2 0 2 1v1c-1 0-1 1-2 1h-3l-1 1-2-1c1-1 2-3 3-4z" class="e"></path><path d="M203 102c0 1 1 1 2 1 0 1-1 1-2 1v-2z" class="R"></path><path d="M212 101h1c1 1 1 0 2 0 0 1-1 2-1 4h1c0 2-1 3-2 5v-5h0l-2 1h0l-1-1h0v-1-1l1-1 1-1z" class="B"></path><path d="M212 101l1 1c0 1-1 3-2 4l-1-1h0v-1-1l1-1 1-1z" class="R"></path><path d="M330 84l2 1h13 34-2l1 1-1 6c-3 0-6 0-9-1l-1 1h-36c0-2 1-5 0-6v-1l-1-1z" class="M"></path><path d="M367 87v-2h10l1 1-1 6c-3 0-6 0-9-1l-1 1v-5z" class="Z"></path><path d="M367 87l1-2v1 1 4l-1 1v-5z" class="O"></path><path d="M223 97v-1l6-1h4c1 2 1 5 1 8l-1 16c0 1-2 1-4 1h-8v-6c0-1 1-4 0-6l-1 1c0-1-1-1-1-1v-1c-1-1-1-2-2-2h-2 0-1c0-2 1-3 1-4h1v-1l-2 1h0v-2h-1c1 0 2-1 3-1h4l3-1z" class="n"></path><path d="M220 98l3-1-1 12c0 1 0 4-1 5 0-1 1-4 0-6l-1 1c0-1-1-1-1-1v-1c-1-1-1-2-2-2h-2 0-1c0-2 1-3 1-4h1v-1l-2 1h0v-2h-1c1 0 2-1 3-1h4z" class="E"></path><path d="M217 101c1 0 2 1 2 1 1 1 1 3 1 4l-1 1c-1-1-1-2-2-2h-2c0-2 2-3 2-4z" class="L"></path><path d="M213 99c1 0 2-1 3-1h4c-1 1-2 1-2 2 0 0 0 1-1 1 0 1-2 2-2 4h0-1c0-2 1-3 1-4h1v-1l-2 1h0v-2h-1z" class="F"></path><defs><linearGradient id="h" x1="245.909" y1="110.968" x2="232.054" y2="107.755" xlink:href="#B"><stop offset="0" stop-color="#a4a3a4"></stop><stop offset="1" stop-color="#c4c3c3"></stop></linearGradient></defs><path fill="url(#h)" d="M234 94c3 0 6 0 8 1 2 0 4 0 6 1v3 7c0 2 1 4 1 6v2h0v6h-9-11c2 0 4 0 4-1l1-16c0-3 0-6-1-8h-4c2-1 3-1 5-1z"></path><path d="M240 99c1 1 2 1 2 2v1h1v1l-1 1c0 1 2 2 1 4h-1c-1 2 0 3-1 4h-1 1 0v-3h0-2-1-1v-1h3c-1-2-1-2-2-3v-2h-2-1c1-1 2-1 3-1s1 1 2 1l1-2c0-1 0-1-1-2z" class="a"></path><path d="M234 94c3 0 6 0 8 1 2 0 4 0 6 1v3 7c0 2 1 4 1 6v2h0v6h-9 2l1-1c1 0 2-1 2-2 0-2-1-1-2-3v-2h-2 0c1-1 0-2 1-4h1c1-2-1-3-1-4l1-1v-1h-1v-1c0-1-1-1-2-2h-1l-1 1s-1 0-1-1c-1-1-2-1-3-1v-2-2z" class="M"></path><path d="M243 102v-1h0c0 1 1 1 1 2l-1 1c1 1 2 2 3 2v1c-1 0-2 1-3 1 1-2-1-3-1-4l1-1v-1z" class="j"></path><path d="M241 112c1-1 0-2 1-4l1 1h2 0v1c-1 1-1 3-2 4v-2h-2 0zm-7-16c1 0 1 1 2 1 2 0 5-1 7 0 0 2 1 2 0 4h0 0v1h-1v-1c0-1-1-1-2-2h-1l-1 1s-1 0-1-1c-1-1-2-1-3-1v-2z" class="Q"></path><path d="M242 95c2 0 4 0 6 1v3 7c0 2 1 4 1 6v2h0v6h-9 2c1 0 3 0 4-1v-1c1 0 1 0 1-1h0v-4h0v-5-3-3l-1-4-1-1s0-1-1-1l-2-1z" class="I"></path><path d="M183 98v3h0v2 5 1l-1-1h-9c-3 1-5 3-7 3l-2 1-2 1c-1 0-2 1-3 1v-1c-1 0-1 1-1 1-2 0-2 1-4 1 0 0-1 0-2-1h0-1l-2-2-1-1h0c-1-1-2-1-2-2 1 0 0-1 1-2 0-1 1-1 1-2 1 0 2-1 3-1l3-3c2 0 5-1 7-1s5 0 7-1c1 0 2-1 3-1h7 5z" class="T"></path><path d="M149 112c1-1 2-1 3-1 1 1 0 2 0 3h0-1l-2-2z" class="G"></path><path d="M159 113c0-1 0-2 1-3 2-5 5-6 10-7-1 1-2 1-3 2l1 1-4 3-1 2 1 1-2 1c-1 0-2 1-3 1v-1z" class="Y"></path><path d="M163 109h1l-1 2c-1 1-1 1-2 1 0-2 1-2 2-3z" class="C"></path><path d="M163 109c1-2 2-3 4-4l1 1-4 3h-1z" class="D"></path><path d="M182 105v-1c1 0 1-1 1-1v5 1l-1-1h-9c-3 1-5 3-7 3l-2 1-1-1 1-2 4-3c3-2 6-2 9-2 2 0 3 0 5 1z" class="C"></path><path d="M182 105v-1c1 0 1-1 1-1v5c-2-1-4-1-6-1h4c0-1 0-1 1-2z" class="F"></path><path d="M177 104c2 0 3 0 5 1-1 1-1 1-1 2h-4-4c1 0 3-1 4-2v-1z" class="X"></path><path d="M177 104v1c-1 1-3 2-4 2-3 1-5 2-7 4l-2 1-1-1 1-2 4-3c3-2 6-2 9-2z" class="V"></path><path d="M183 98v3h0c0 1-1 1-1 2v1h-1 0c0-1-1-1-1-1h-1 0c-1 1-3 0-4 0-1 1-2 1-3 1l2-1v-1h-1v-1h0c-1 1-2 1-3 1-1 1-2 0-4 0-3 1-6 2-8 4h0v2l-2 2c-1 0-2 1-3 1h-5 0c-1-1-2-1-2-2 1 0 0-1 1-2 0-1 1-1 1-2 1 0 2-1 3-1l3-3c2 0 5-1 7-1s5 0 7-1c1 0 2-1 3-1h7 5z" class="g"></path><path d="M158 106v2l-2 2-2-1c2 0 3-1 4-3z" class="D"></path><path d="M151 104l3-3c2 0 5-1 7-1-6 2-10 4-13 9v2c-1-1-2-1-2-2 1 0 0-1 1-2 0-1 1-1 1-2 1 0 2-1 3-1z" class="B"></path><g class="h"><path d="M118 85c3-1 5 0 7 0h12 27 7 2v-1c1 0 1 1 1 1h-1l1 7h-9v-6c-1 2 0 4 0 6h-33-8c-2 0-3 0-4-1-4 0-10 1-13 0v-2c0-1 3-1 4-1v-3c2 0 5 1 7 0h0z"></path><path d="M174 85c3 0 6 0 8-1 1 0 2 1 2 1 2 0 7 0 9-1h0l1 1c4 0 8 0 12-1V73c1 4 0 8 1 12 2 0 9 0 11-1v-1c1 0 1 1 1 2h7c0-1 1-1 1-1 3 1 6 1 9 1h17 0-9v6c-1 1-3 0-5 0h-12v-6h0v6c-2 0-8 1-8 0-1 0 0-1-1-1v1l-1 1h-3l-8-1v-6c-1 1-1 5-1 7h-8c-1 0-3 0-4-1v-1 1c-1 1-8 1-10 1v-6-1 7h-9v-7h0z"></path></g><path d="M182 96l1 1h1c1-2 2-1 4-2h2l2 1v1l1 7c0 2 2 5 3 7v-4-1h1c0 1 1 2 2 3 1 2 1 4 3 5 1 2 2 4 4 6 0-1 1-1 1-1l3 2h2 1l1 1c2 0 4-1 5 0h1 4v1c-4 0-9 1-12 3v2l1 5h0v1 2l-1 1h-1c0 1-1 2 0 3v1l-1 1c-1-2-3-3-4-5l-2-2c0-1-1-1 0-2-1-1-1 0-2 0l-3-3h-1l3 3-2 2-6-5h-1c-3-4-5-7-7-11l-2-5v-3h0v-2-1-5-2h0v-3l-1-2z" class="D"></path><path d="M189 100c1 1 1 4 2 5h-1l-1 2c0-2-1-5 0-7z" class="O"></path><path d="M206 137l-2-2c0-1-1-1 0-2 0 0 1 1 2 1 2 0 4 1 4 2l1 1c0 1-1 2 0 3v1l-1 1c-1-2-3-3-4-5z" class="B"></path><path d="M189 107l1-2h1c3 9 6 17 13 22 1 1 1 2 1 2v1c-1 0-2-1-3-1-8-5-11-13-13-22z" class="M"></path><defs><linearGradient id="i" x1="200.875" y1="130.234" x2="184.833" y2="118.737" xlink:href="#B"><stop offset="0" stop-color="#848183"></stop><stop offset="1" stop-color="#9d9d9e"></stop></linearGradient></defs><path fill="url(#i)" d="M183 101c1-1 1-1 2-1 0 0 1 0 1 1 1 2 0 5 1 7 0 8 4 17 11 22l3 3-2 2-6-5h-1c-3-4-5-7-7-11l-2-5v-3h0v-2-1-5-2h0z"></path><path d="M183 110c0-1 0-3 1-3 0 0 1 1 1 2v1h-2z" class="H"></path><path d="M183 110h2c1 2 2 5 2 7l-2 2-2-5v-3-1z" class="a"></path><path d="M192 97l1 7c0 2 2 5 3 7v-4-1h1c0 1 1 2 2 3 1 2 1 4 3 5 1 2 2 4 4 6 0-1 1-1 1-1l3 2h2 1l1 1c2 0 4-1 5 0h1 4v1c-4 0-9 1-12 3v2l1 5c-1 0-2 0-3-1-1 0-3-1-5-2v-1s0-1-1-2c-7-5-10-13-13-22-1-1-1-4-2-5v-2l1 1c0-1 1-1 2-2z" class="E"></path><path d="M204 127l7 5h-1c-1 0-3-1-5-2v-1s0-1-1-2z" class="Y"></path><path d="M212 128c-2 0-4-2-5-3-2-1-5-3-6-5 0-1-1-1-2-2-1-2-2-3-3-5-1-1-1-2-2-2l1-1 1 1c0 2 2 3 2 4 1 2 3 4 4 5s1 1 2 1l1 2h1c1 0 1 1 2 1h0 1c0-1 1 0 2-1-2-1-4-2-5-3 0-1 1-1 1-1l3 2h2 1l1 1c2 0 4-1 5 0h1 4v1c-4 0-9 1-12 3v2z" class="K"></path><path d="M212 121h1l1 1h-2v-1z" class="G"></path><path d="M173 108h9l1 1v2h0v3l2 5c2 4 4 7 7 11h1l6 5 2 2h1 1 1c0 1 0 2-1 3 1 0 1 0 1 1v3 1c-1 1-1 4-2 5v1c-1 2-1 3-1 4h0l-2 4h0c-1 0-2 0-3 1h-2v-1h0-3l-1-1-1 1v1h-1c-1-1-2-3-4-3v1h-1v-1c1-1 1-1 2-1 0 0 0-1-1-1h-1c-1 1-1 2-1 4l-1-3h0s0-2-1-2l-1-1h1v-1h-1v-1-1s-1 0-1-1h1l-1-1v-1c0-1-1-2-2-4h0l-1-1c-1-1-1-1-1-2s-1-2-1-2c-1-2-2-3-3-5-1-1-1-2-2-2 0-1-1-1-1-2l-5-6-2-2h-1v-1l-1-1h0c-1 0-2-1-3-2h0l-1-2c2 0 2-1 4-1 0 0 0-1 1-1v1c1 0 2-1 3-1l2-1 2-1c2 0 4-2 7-3z" class="C"></path><path d="M185 143c1 1 2 2 2 4l2 2c0 1 1 1 1 2l1 1c-3-1-5-6-7-8l1-1z" class="D"></path><path d="M191 138v-1l-1 1v3h0l-1 1c0-2-1-2 0-4 0-1 1-1 2-2 0-1 1 0 2 0s1 1 2 1h-2c0 1 0 1 1 1l-2 1-1-1z" class="S"></path><path d="M185 155c2 1 3 3 5 3l-1 1v1h-1c-1-1-2-3-4-3v1h-1v-1c1-1 1-1 2-1 0 0 0-1-1-1h1z" class="W"></path><path d="M192 146c1 1 2 1 3 2h0l1 1h1 0l-1 1-2 2h1 0l-1 3-1 1s-1 0-1-1v-1c0-1 0-1-1-2l-1-1c0-1-1-1-1-2 1 0 1 1 2 1h0l1-1v-3z" class="K"></path><path d="M189 149c1 0 1 1 2 1h0l2 1 1 1h-1c-1 0-2 0-3-1 0-1-1-1-1-2z" class="W"></path><path d="M192 146c1 1 2 1 3 2-1 1-1 2-2 3l-2-1 1-1v-3z" class="F"></path><path d="M180 125l1-1 1 1c3 1 3 2 4 4l1 2c1 1 2 1 3 1v1h0c-2 1-3 2-4 3v1h-1v-2-2c-1-3-2-5-5-8z" class="W"></path><path d="M177 140h0c0-1-1-1-1-2h2l1 1c1 0 1 0 1 1 1-1 0-3 0-5-2-2-4-5-5-7l3 3c2 2 3 3 4 5 0 2 0 3 1 5 0 1 1 2 2 2l-1 1c-1-1-2-2-3-2-2 0-3-1-4-2z" class="G"></path><path d="M177 140c1 0 1 0 3 1 1 0 1 0 2 1h-1c-2 0-3-1-4-2z" class="S"></path><path d="M176 143c1 0 2 0 2 1h2c2 2 4 5 5 7 0 0-2-1-2-2-1 0 0-1-2-1-1 1-1 2 0 4h0v3l1-1h0 1c0-1 0-1-1-1v-2c1 1 2 3 3 4h-1-1c-1 1-1 2-1 4l-1-3h0s0-2-1-2l-1-1h1v-1h-1v-1-1s-1 0-1-1h1l-1-1v-1c0-1-1-2-2-4z" class="F"></path><path d="M196 150l1-1v1h2 0c1 1 0 1 1 1h0v2c1-1 1-2 1-3h1v1c-1 2-1 3-1 4h0l-2 4h0c-1 0-2 0-3 1h-2v-1h0v-2l-1-1 1-1 1-3h0-1l2-2z" class="N"></path><path d="M193 156l1-1c1 1 2 1 2 1-1 1-1 2-2 3h0v-2l-1-1z" class="G"></path><path d="M195 152l2 2-1 2s-1 0-2-1l1-3z" class="C"></path><path d="M196 150l1-1v1h2 0l-2 4-2-2h0-1l2-2z" class="E"></path><path d="M196 150v1 1h-1-1l2-2z" class="F"></path><path d="M193 130l6 5 2 2h1 1 1c0 1 0 2-1 3 1 0 1 0 1 1v3 1c-1 1-1 4-2 5h-1c0 1 0 2-1 3v-2h0c-1 0 0 0-1-1h0-2v-1h0-1l-1-1h0c-1-1-2-1-3-2h-1c-1 0-2-1-2-2h0 2v-1c1-1 1-2 2-2v-1h-2v-2l1 1 2-1c-1 0-1 0-1-1h2c-1 0-1-1-2-1 1 0 1-1 1-1l-1-2h1v1l2-1c-2 0-3-1-3-2v-1z" class="W"></path><path d="M201 145v-4c1-1 1-1 2-1 0 2-1 3-2 5z" class="G"></path><path d="M189 144c1 0 1 1 2 1s2 0 3 1l2 1c0 1 0 1-1 1h0c-1-1-2-1-3-2h-1c-1 0-2-1-2-2h0zm6-7c1 0 2 0 3 1h1v1h-1c-1 0-3 1-4 2h0v-1-2c-1 0-1 0-1-1h2z" class="L"></path><path d="M194 146h1c0-1 1-1 2-2v-1h1c0-1 1-2 2-3 0 2-2 5-2 6v2l-1 1h-1l-1-1c1 0 1 0 1-1l-2-1z" class="J"></path><path d="M203 140c1 0 1 0 1 1v3 1c-1 1-1 4-2 5h-1c0 1 0 2-1 3v-2h0c-1 0 0 0-1-1h0c1-2 1-3 2-5s2-3 2-5z" class="c"></path><path d="M173 108h9l1 1v2h0v3l-1-1v1c-2 1-5 2-7 2h-3c1 3 6 4 8 5 1 0 2 0 3 1l1 1-3 1-1 1c3 3 4 5 5 8v2c-1-1-3-6-3-6h-1c-2-1-4-3-5-3-2-1-4-2-6-2-2-1-4-1-6-1h-2 0l-2-2h-1v-1l-1-1h0c-1 0-2-1-3-2h0l-1-2c2 0 2-1 4-1 0 0 0-1 1-1v1c1 0 2-1 3-1l2-1 2-1c2 0 4-2 7-3z" class="E"></path><path d="M180 125h0c-1 0-3-2-4-3s-3-2-4-3c1 0 2 1 3 1l1 1h1 0 3c1 0 2 0 3 1l1 1-3 1-1 1z" class="F"></path><path d="M159 113v1c1 0 2-1 3-1 0 0 0 1-1 1-1 1-2 1-3 2h0 0 1 0c2 1 4 0 6 0h0c-2 1-6 2-7 3-1 0-2-1-3-2h0l-1-2c2 0 2-1 4-1 0 0 0-1 1-1z" class="C"></path><path d="M159 120s1-1 2-1c2-1 4-1 6-2h3c0 1 0 2-1 3h0c2 0 5 2 6 3v1c-2 0-3-1-4-1s-1 0-1 1c-2-1-4-1-6-1h-2 0l-2-2h-1v-1z" class="T"></path><path d="M160 121h3c0 1 0 1 1 2h-2 0l-2-2z" class="S"></path><path d="M163 121c3 0 5 1 8 2-1 0-1 0-1 1-2-1-4-1-6-1-1-1-1-1-1-2z" class="B"></path><path d="M173 108h9l1 1v2h0v3l-1-1v1c-2 1-5 2-7 2l-1-1h-6v-1c-1 0-3 0-4 1h-3 0c-1 0-1 1-2 1h0-1 0 0c1-1 2-1 3-2 1 0 1-1 1-1l2-1 2-1c2 0 4-2 7-3z" class="g"></path><path d="M174 115h0l8-2v1c-2 1-5 2-7 2l-1-1z" class="D"></path><path d="M173 108h9l1 1v2l-1-1c-1-1-4-1-6-1h0l-3-1z" class="T"></path><path d="M224 122h12c4 0 10 0 15 1 2 1 4 1 6 2l1 1v2 3c0 3-1 6 0 9v5c-2 0-4-1-6-1h2l3 2c1 0 1 0 1 1l1 12c-1 1-2 1-3 2h-6-5c-2 0-4 0-5-1l-1-3-1-19h-6v10c0-1 0-2-1-3 0 3 0 6-1 8v3c0 2-1 2-1 3h-2c-2 0-3-1-5-1 0 1-1 0-1 0-1 0-2-1-3-1-2 0-3 0-4-1v-1h-2v-2c1-2 1-6 1-8h-1c-1-1-1-2-2-3l1-1v-1c-1-1 0-2 0-3h1l1-1v-2-1h0l-1-5v-2c3-2 8-3 12-3v-1z" class="a"></path><path d="M236 134h1 1v1c-1 0-1 1-2 0v-1zm-3-9v10c1 1 0 1 0 2h-1c-1 2 0 4-1 6h0l-1-2v-2c0-1 1-2 1-2l1-1h1v-9-2z" class="f"></path><path d="M249 151l2-2c1 2 2 1 2 3l-1 1c0 2-1 2 0 3v1h1l1 1h-1c-1 0-2 0-2 1h0c0 1 0 1-1 2h-5c1-1 1-2 2-2h2v-1c0-1-1-1-2-2v-5h2z" class="V"></path><path d="M249 159v-1c0-1-1-1-2-2v-5h2c1 1 1 1 1 3l1 1c0 1 0 2-1 3v1h-1z" class="H"></path><path d="M251 143l1 1h2l3 2c1 0 1 0 1 1l1 12c-1 1-2 1-3 2h-6c1-1 1-1 1-2h0c0-1 1-1 2-1h1l-1-1h-1v-1c-1-1 0-1 0-3l1-1c0-2-1-1-2-3h0v-1l1-1v-1c0 1 0 1-1 0h0v-3z" class="O"></path><path d="M252 153c1 1 1 2 1 3h-1c-1-1 0-1 0-3z" class="B"></path><path d="M251 143l1 1h2c0 2-1 3-2 4l-1 1v-1l1-1v-1c0 1 0 1-1 0h0v-3z" class="U"></path><path d="M257 146c1 0 1 0 1 1l1 12c-1 1-2 1-3 2 0-1 1-1 1-1l-1-2v-1s0-1 1-2h0c0-1-1-1-1-1l-1-1c-1-1-1-1-1-2 2-1 2-3 3-5z" class="I"></path><defs><linearGradient id="j" x1="230.557" y1="121.43" x2="216.415" y2="135.289" xlink:href="#B"><stop offset="0" stop-color="#c8c7c8"></stop><stop offset="1" stop-color="#f9f8f8"></stop></linearGradient></defs><path fill="url(#j)" d="M212 128v-2c3-2 8-3 12-3 3 0 7-1 10 0 0 1 0 1-1 2v2 9h-1-2l-4-1h0l-2-1h0c-2 0-4 0-5-1 0 0-1 1-1 0h-5 0l-1-5z"></path><path d="M230 136l1-1v-2c1-2 1-3 1-5h0l1-1v9h-1-2z" class="h"></path><path d="M236 122c4 0 10 0 15 1 2 1 4 1 6 2l1 1v2 3c0 3-1 6 0 9v5c-2 0-4-1-6-1l-1-1v3h0l-1 1c-1-2-2 1-3 1l-1-1 1-1v-2-1l-2-2 1-1c0-1-1-1-1-2-1 0-2 1-3 1v-1c0-1 0-3-1-4 0-1-1-1-1-2 0 0-1-1-1-2-1-2-2-4-3-7v-1z" class="j"></path><path d="M249 127c1-1 2-2 3-2h1v1c-1 1-2 1-3 1h-1z" class="Z"></path><path d="M249 127h1v2c0 1-1 1-2 1h0c0-1 1-2 1-3z" class="Y"></path><path d="M236 123c1 1 2 1 3 1 1 1 1 1 1 2 1 1 2 0 2 1-1 1-2 1-3 2h0v1c-1-2-2-4-3-7z" class="M"></path><path d="M257 125l1 1v2 3c0 3-1 6 0 9v5c-2 0-4-1-6-1l-1-1c-1 0-2-1-3-2h0v-1h1l1 1c1 1 2 1 3 1v1l1-1h2v-1-2c0-1 0-4-1-5 0 0 0-1-1-1 1 0 1-1 1-1 1-2-1-4 0-6 1 0 2 0 2-1z" class="Z"></path><path d="M248 124c1-1 2-1 3 0h2c-2 0-3 1-4 2-1 0-2 2-3 3h0v1c1 1 2 1 3 1v1c1 0 2 1 2 1 1 1 0 3 0 4h-1s0-1-1-1v-1l-1 1 1 1h-1-1c-1 0-1 0-1-1h0-2l-3-3v-1c0-1-1-2-1-3l2-1v1h0 1v-2h2s1 0 2-1c0 0 0-1 1-1h1l-1-1z" class="Q"></path><defs><linearGradient id="k" x1="230.361" y1="147.196" x2="219.343" y2="148.168" xlink:href="#B"><stop offset="0" stop-color="#c9c8c9"></stop><stop offset="1" stop-color="#ececec"></stop></linearGradient></defs><path fill="url(#k)" d="M213 133h5c0 1 1 0 1 0 1 1 3 1 5 1h0l2 1h0l4 1h2l-1 1s-1 1-1 2v2l1 2-1 1c0 1 0 1 1 1 0 3 0 6-1 8v3c0 2-1 2-1 3h-2c-2 0-3-1-5-1 0 1-1 0-1 0-1 0-2-1-3-1-2 0-3 0-4-1v-1h-2v-2c1-2 1-6 1-8h-1c-1-1-1-2-2-3l1-1v-1c-1-1 0-2 0-3h1l1-1v-2-1z"></path><g class="h"><path d="M224 156c1-1 2-2 2-3 1 0 2 0 2 1s-1 1-3 2h-1z"></path><path d="M214 155c1 0 3 1 5 0 1 1 4 1 5 1h0 1l1 1 4-1c0 2-1 2-1 3h-2c-2 0-3-1-5-1 0 1-1 0-1 0-1 0-2-1-3-1-2 0-3 0-4-1v-1z"></path></g><path d="M224 156h1l1 1c-1 0-1 0-2-1h0zm0-22l2 1h0l4 1h2l-1 1s-1 1-1 2v2h-1c1 1 1 1 1 2l-1 1h-1-1s-1 0-1-1c-1-1-1-3-1-4s0-1-1-2c0 1-1 1-1 2v-2c0-2 0-2 1-3z" class="k"></path><path d="M226 135l4 1h2l-1 1s-1 1-1 2l-1-1c-1 0-1 1-2 0 0-1-1-2-1-3z" class="H"></path><path d="M213 133h5c0 1 1 0 1 0 1 1 3 1 5 1h0c-1 1-1 1-1 3v2h-1v2c0 1 0 1-1 2h-1v-3h-1v2 1 3 3c0 1 1 5 0 5v1c-2 1-4 0-5 0h-2v-2c1-2 1-6 1-8h-1c-1-1-1-2-2-3l1-1v-1c-1-1 0-2 0-3h1l1-1v-2-1z" class="l"></path><path d="M211 137h1l1-1v9h-1c-1-1-1-2-2-3l1-1v-1c-1-1 0-2 0-3z" class="K"></path><path d="M213 133h5c0 1 1 0 1 0 1 1 3 1 5 1h0c-1 1-1 1-1 3v2h-1v2c0 1 0 1-1 2h-1v-3h-1v2 1l-1-3h-2c-1 0-1-1-2-2h0 1c1 1 2 1 4 1v-1h0c-2-1-4-3-6-4v-1z" class="m"></path><path d="M289 101h1c2-1 5-2 7-3 6-2 13-3 19-2 2 2 3 3 4 5v1c1 0 2 0 2 1 1 0 0 1 1 1h1v-1l1 2h0 0v-2h0v-2h1v-2l2 3c2 0 3 1 4 1 1 1 3 2 4 3h0l4 3 1 1 6 3c0 1 1 2 2 2l2 1 2 2v3h0c-1 1-1 1-2 1v1l1 2v1c1 2 4 4 6 6l-1 1c0-1-1 0-2-1h-3c-1 1-3 1-4 2-5 1-10 5-13 8h-1c-2-1-3-1-5-1l-5-2c-1-1-2-1-3-1l-5-2h-2v1l-1 1c0 1-3 6-4 7l-6-2c-5-1-12-2-16-1-1 0-2 1-3 2-2 2-3 4-4 7-1-1-1-2-1-3h0-5c0-1-1-1-1-1l-1 2v-3c-1-3-2-5-5-7-2-1-5-2-7-3h0c-1-1-1-1-1-2l1-1c2-1 6-2 8-3h4 2l1-1h3c0-4 0-8 1-12v-1c0-2 2-5 4-7l-1-1c1 0 1-1 1-1-1-1-2 0-3 0h0c0-1 1-2 2-2 1-1 3-2 5-3 1 0 2 0 2-1z" class="T"></path><path d="M315 109h0-8c3-1 6-2 9-1l-1 1z" class="C"></path><path d="M315 109l1-1c2 0 4 1 5 3v1h-1c-1-1-2-3-4-3h-1z" class="F"></path><path d="M311 123h1c1 2 2 4 2 6 0 1-1 3-1 5l-1-1c0-1 1-2 1-3 0-3-1-4-2-7z" class="P"></path><path d="M309 119l2 4c1 3 2 4 2 7l-2-2c-1-1-1-2-1-2v-1c0-2-1-4-2-5l1-1z" class="G"></path><path d="M304 118c2 0 3 0 4 2 1 1 2 3 2 5v1l-2-3c-1-1-2-1-3-2l-1-1h2l-2-1v-1z" class="B"></path><path d="M306 120c1 0 1 1 2 2v1c-1-1-2-1-3-2l-1-1h2z" class="S"></path><path d="M314 120l2 2v1c1 0 2 1 2 1 1 1 1 2 1 3l-1 2h-1-1v-1c0-2-1-3-1-5-1-1-1-2-1-3z" class="G"></path><path d="M316 128s2-1 2-2c0 0-1 0-1-1l1-1c1 1 1 2 1 3l-1 2h-1-1v-1z" class="K"></path><path d="M304 114c5 1 6 4 8 8v1h-1l-2-4-1 1c-1-2-2-2-4-2v-2h1l-1-1v-1z" class="L"></path><path d="M304 118v-2h1c1 1 3 2 4 3l-1 1c-1-2-2-2-4-2z" class="D"></path><path d="M302 106l6-3c3-1 6-1 9-1l1 1v1c-6-1-13 1-18 5h0-1-1c1-1 3-2 4-3z" class="L"></path><path d="M293 112v-1c5-7 11-11 20-12 1 0 2 0 4 1h0c-6 1-12 2-16 5-2 1-3 2-4 4-3 3-5 6-7 10l-1-1 1-2h0l-2-2 2-2h1l1-1v1h0 1z" class="R"></path><path d="M292 111v1h0 1c-1 1-2 2-3 4h0l-2-2 2-2h1l1-1z" class="D"></path><path d="M297 117c0-1 1-1 1-2 3-1 4-2 6-1v1l1 1h-1v2 1l2 1h-2c-1 0-2 0-4 1-1 1-3 3-4 5 0 1 0 3-1 4h-2 0l-1-3c0-3 1-7 2-10l1 1h0l1-1h1z" class="C"></path><path d="M304 115l1 1h-1v2 1c-1-1-1-1-2-1v-1c-2 1-3 0-4 0 2-1 3-2 6-2z" class="S"></path><path d="M297 117c0-1 1-1 1-2 3-1 4-2 6-1v1c-3 0-4 1-6 2-1 1-2 2-3 4h-1c0-2 2-3 3-4z" class="X"></path><path d="M294 117l1 1h0l1-1h1c-1 1-3 2-3 4h1l-1 4c0 1-1 2-2 2 0-3 1-7 2-10z" class="S"></path><path d="M289 101h1c2-1 5-2 7-3 6-2 13-3 19-2 2 2 3 3 4 5l-1 1c0-2-1-3-2-4-2-1-4 0-6-1-4 0-10 2-13 4 0 1-1 1-1 2l-3 3c-1 2-2 3-2 5l-1 1h-1c2-4 3-7 6-10-1 1-3 1-4 2h0c-2 0-7 2-8 3l-1 2-1-1c1 0 1-1 1-1-1-1-2 0-3 0h0c0-1 1-2 2-2 1-1 3-2 5-3 1 0 2 0 2-1z" class="G"></path><path d="M290 119c2-4 4-7 7-10 1-2 2-3 4-4v1h1c-1 1-3 2-4 3h1 1c-2 3-4 5-6 8-1 3-2 7-2 10l1 3h0l2 1h-1-1-6c-3 1-6 1-9 0-2 0-4 1-6 0l1-1h-1 2l1-1h3c3 0 6 1 9 0h1 1l-1-5c1-2 1-4 1-6l1 1z" class="P"></path><path d="M274 130c5-1 11-1 16 0h3 0l2 1h-1-1-6c-3 1-6 1-9 0-2 0-4 1-6 0l1-1h-1 2z" class="I"></path><path d="M290 119c2-4 4-7 7-10 1-2 2-3 4-4v1h1c-1 1-3 2-4 3-2 2-4 5-5 8-1 2-2 4-2 7v6h-1v-2l-1 1-1-5c1-2 1-4 1-6l1 1z" class="F"></path><path d="M289 118l1 1v9l-1 1-1-5c1-2 1-4 1-6z" class="L"></path><path d="M296 126c1-2 3-4 4-5 2-1 3-1 4-1l1 1c1 1 2 1 3 2l2 3s0 1 1 2l2 2c0 1-1 2-1 3h-1-1l-3-1-6-1c-1-1-2-1-2-2h0c-2-1-2-1-3-3z" class="K"></path><path d="M302 124s1 0 1 1v2c-1 1-1 2-3 2 0-2 0-4 2-5z" class="T"></path><path d="M303 127c1 0 1 1 1 1h2 0c0 1 0 1 1 2 1 0 1 0 1-1v2h-2c-1-1-2-1-3-1s-2-1-3-1c2 0 2-1 3-2z" class="W"></path><path d="M296 126c1-2 3-4 4-5 2-1 3-1 4-1l1 1c-1 1-2 1-4 2h0v-1h0c-2 1-3 3-3 5 0 1 1 1 1 2h0c-2-1-2-1-3-3z" class="B"></path><path d="M305 121c1 1 2 1 3 2l2 3s0 1 1 2l2 2c0 1-1 2-1 3h-1c0-1-1-2-1-2 0-1-1-1-2-2 0 1 0 1-1 1-1-1-1-1-1-2h0-2s0-1-1-1v-2c0-1-1-1-1-1l-1-1c2-1 3-1 4-2z" class="T"></path><path d="M303 125l1-1v1l2 3h-2s0-1-1-1v-2z" class="E"></path><path d="M305 121c1 1 2 1 3 2l2 3s0 1 1 2c-1-1-1-1-1-2-2 0-2-1-4-1-1-1-1-1-2 0v-1l-1 1c0-1-1-1-1-1l-1-1c2-1 3-1 4-2z" class="C"></path><path d="M283 109l1-2c1-1 6-3 8-3h0c1-1 3-1 4-2-3 3-4 6-6 10l-2 2 2 2h0l-1 2c0 2 0 4-1 6l1 5h-1-1c-3 1-6 0-9 0 0-4 0-8 1-12v-1c0-2 2-5 4-7z" class="E"></path><path d="M282 118l2 1h0v1h-1-2 0l1-2z" class="B"></path><path d="M283 120h1v-1l3 1v1 1c-1 0-3-1-4-2z" class="P"></path><path d="M285 117h2v3l-3-1h0l-2-1 1-1h2 0 0z" class="K"></path><path d="M287 121h1v3l1 5h-1-1c0-1 0-3-1-4 0-1 0-1-1-2 1 0 1 0 2-1h0v-1z" class="C"></path><path d="M288 114l2 2h0l-1 2c0 2 0 4-1 6v-3h-1v-1-3h-2l3-3z" class="G"></path><path d="M283 109l1-2c1-1 6-3 8-3h0c1-1 3-1 4-2-3 3-4 6-6 10l-2 2-3 3h0-1c-1-1-1-2 0-3 1-3 4-4 6-7l-3 2c-2 1-3 2-4 4l-1 1-3 3v-1c0-2 2-5 4-7z" class="C"></path><path d="M319 102l1-1v1c1 0 2 0 2 1 1 0 0 1 1 1h1v-1l1 2h0l1 2-1 1v1c0 1 1 1 2 2v1c-1 2 0 3 0 5h2v1c1 0 2 1 1 2v2c-1 0-1 0-1 1l-1-1-1 1v1c-1 3-2 7-4 10l-2 2v2l-5-2-1-1h1l1-2 3-3c-1 0-1 0-2-1h0l1-2c0-1 0-2-1-3 0 0-1-1-2-1v-1l-2-2c-1-3-3-5-5-8h-2l1-1h5c1 1 3 1 4 2h0l5 2h1v-1c-1-4-2-8-4-12z" class="E"></path><path d="M318 117h0-3v-1l1-1 1 1 1 1z" class="J"></path><path d="M321 119v1c-1 0-3-1-4-1 0-1 1-2 2-2l2 2z" class="B"></path><path d="M317 122l-1-1 1-1 1 1h0 2v1 1l-3-1z" class="K"></path><path d="M318 115s1 0 1 1v1c1 0 2 1 3 1l1-1v2 1l-2-1-2-2h0-1l-1-1 1-1z" class="D"></path><path d="M327 117h2v1c1 0 2 1 1 2v2l-1-1c-1-1-1-2-2-4z" class="W"></path><path d="M316 135h1c1-1 1-1 1-2l1 1s1 1 0 2h1 1v2l-5-2-1-1h1z" class="F"></path><path d="M318 121v-1h4l2 2v1h-2v1c-1 0-2-1-2-1v-1-1h-2z" class="G"></path><path d="M309 112h-2l1-1h5v3c-2 0-3-2-4-2z" class="D"></path><path d="M318 115l-1-1h1l-1-1h0l5 2h1v-1 3l-1 1c-1 0-2-1-3-1v-1c0-1-1-1-1-1z" class="K"></path><path d="M324 111v-1c1 1 2 3 2 4 0 2-1 4 1 7l-1 1h-1v-3h-1v-8z" class="W"></path><path d="M317 122l3 1s1 1 2 1h1c0 1 0 1-1 2h0c0 2-1 2-2 3v1c-1 0-1 0-2-1h0l1-2c0-1 0-2-1-3 0 0-1-1-2-1v-1h1z" class="C"></path><path d="M319 102l1-1v1c1 0 2 0 2 1 1 0 0 1 1 1h1v-1l1 2h0l1 2-1 1c-1 0-1 0-1 1h-1l1 2v8h0-1v-2-3c-1-4-2-8-4-12z" class="B"></path><path d="M320 102c1 0 2 0 2 1 1 0 0 1 1 1h1v-1l1 2h0l1 2-1 1c-1 0-1 0-1 1h-1c-1-3-1-5-3-7z" class="D"></path><defs><linearGradient id="l" x1="288.383" y1="129.351" x2="284.037" y2="149.349" xlink:href="#B"><stop offset="0" stop-color="#585758"></stop><stop offset="1" stop-color="#7d7d7d"></stop></linearGradient></defs><path fill="url(#l)" d="M296 126c1 2 1 2 3 3h0c0 1 1 1 2 2l6 1 3 1h1 1l1 1v1h2l1 1h-2v1l-1 1c0 1-3 6-4 7l-6-2c-5-1-12-2-16-1-1 0-2 1-3 2-2 2-3 4-4 7-1-1-1-2-1-3h0-5c0-1-1-1-1-1l-1 2v-3c-1-3-2-5-5-7-2-1-5-2-7-3h0c-1-1-1-1-1-2l1-1c2-1 6-2 8-3h4 1l-1 1c2 1 4 0 6 0 3 1 6 1 9 0h6 1 1l-2-1h2c1-1 1-3 1-4z"></path><path d="M293 131h1v1c0 1 0 1-1 1v-1-1z" class="L"></path><path d="M307 135h2l-1 2h-1v-1-1z" class="Y"></path><path d="M260 133l1 1c1 0 2-1 3 0h2l-2 1c-1 0-2-1-3 0l-1 1c-1-1-1-1-1-2l1-1z" class="I"></path><path d="M295 131c4 0 7 1 11 2-2 0-5 1-7 0-1 0-1-1-1-1h-4v-1h1z" class="d"></path><path d="M296 126c1 2 1 2 3 3h0c0 1 1 1 2 2l6 1 3 1h1 1l1 1v1l-7-2c-4-1-7-2-11-2l-2-1h2c1-1 1-3 1-4z" class="F"></path><path d="M284 144h-2v1-1l2-2 1-1c3-2 8-2 12-1l-1 1h2s1-1 1-2h0l2 2h2v1s1 0 2 1c1 0 1 1 3 0h-1s-1 0-2-1h-1c2-1 3 0 4 0 1-1 2-1 3-2 0-1 1-2 2-2 0 1-3 6-4 7l-6-2c-5-1-12-2-16-1-1 0-2 1-3 2z" class="P"></path><defs><linearGradient id="m" x1="273.797" y1="135.526" x2="268.818" y2="129.792" xlink:href="#B"><stop offset="0" stop-color="#6b6b6c"></stop><stop offset="1" stop-color="#817f81"></stop></linearGradient></defs><path fill="url(#m)" d="M268 130h4 1l-1 1c2 1 4 0 6 0 3 1 6 1 9 0l-2 1c-2 1-5 0-6 0v1h0 0-4c-3 1-6 1-9 1h-2c-1-1-2 0-3 0l-1-1c2-1 6-2 8-3z"></path><defs><linearGradient id="n" x1="337.714" y1="130.546" x2="336.625" y2="117.335" xlink:href="#B"><stop offset="0" stop-color="#141414"></stop><stop offset="1" stop-color="#323132"></stop></linearGradient></defs><path fill="url(#n)" d="M325 105h0v-2h0v-2h1v-2l2 3c2 0 3 1 4 1 1 1 3 2 4 3h0l4 3 1 1 6 3c0 1 1 2 2 2l2 1 2 2v3h0c-1 1-1 1-2 1v1l1 2v1c1 2 4 4 6 6l-1 1c0-1-1 0-2-1h-3c-1 1-3 1-4 2-5 1-10 5-13 8h-1c-2-1-3-1-5-1l-5-2c-1-1-2-1-3-1v-2l2-2c2-3 3-7 4-10v-1l1-1 1 1c0-1 0-1 1-1v-2c1-1 0-2-1-2v-1h-2c0-2-1-3 0-5v-1c-1-1-2-1-2-2v-1l1-1-1-2z"></path><path d="M336 120h1l1 1c-1 1-1 1-2 1h-1c0-1 1-1 1-2z" class="B"></path><path d="M341 122h2v1h-1c0 1 1 1 1 2h-1l-2-2c1 0 1-1 1-1z" class="G"></path><path d="M345 123c1 0 1 0 1 1 1 0 0 1 1 2 1-1 0-3 0-4 0 1 1 2 2 4 1 1 2 2 3 4v1l-5-4c-2 1-4 3-6 6v1c-1 0-1 0-2-1 0-1-1-2-2-3-1 0-2-1-2-2v-1l5 5c2-2 5-5 6-7-2 0-2-1-3-2h0v-1c1 0 1 1 2 1z" class="L"></path><path d="M335 115l8 4c2 0 3 1 4 1v1h0v1c0 1 1 3 0 4-1-1 0-2-1-2 0-1 0-1-1-1s-1-1-2-1h-2c-1-1-2-1-3-2l1-1-4-2-1-2h1z" class="R"></path><path d="M339 119c2 0 5 2 6 4-1 0-1-1-2-1h-2c-1-1-2-1-3-2l1-1z" class="D"></path><path d="M350 120h1c1 0 1 0 1-1 0 1 0 1 1 2h0c-1 1-1 1-2 1v1l1 2v1c1 2 4 4 6 6l-1 1c0-1-1 0-2-1h-3s0-1 1-1h-1v-1c-1-2-2-3-3-4-1-2-2-3-2-4v-1h0v-1h2 1z" class="E"></path><path d="M350 120h1c1 0 1 0 1-1 0 1 0 1 1 2h0c-1 1-1 1-2 1v1l1 2v1c-2-2-3-4-5-5v-1h2 1z" class="N"></path><path d="M341 110l6 3c0 1 1 2 2 2l2 1 2 2v3c-1-1-1-1-1-2 0 1 0 1-1 1h-1-1-2c-1 0-2-1-4-1l-8-4 1-2h0 1l2-3v1h2v-1z" class="M"></path><path d="M339 110v1c1 2 5 4 5 5l-8-3h0 1l2-3z" class="B"></path><path d="M339 111h2c1 2 2 3 4 4 1 1 2 1 3 1 1 1 2 3 4 3 0 1 0 1-1 1h-1c-2-1-4-2-6-4 0-1-4-3-5-5z" class="L"></path><path d="M341 110l6 3c0 1 1 2 2 2l2 1 2 2v3c-1-1-1-1-1-2-2 0-3-2-4-3-1 0-2 0-3-1-2-1-3-2-4-4v-1z" class="Z"></path><path d="M325 105h0v-2h0v-2h1v-2l2 3c2 0 3 1 4 1 1 1 3 2 4 3h0l4 3 1 1v1h-2v-1l-2 3h-1 0l-1 2h-1l1 2 4 2-1 1h-1-1c-1-1-5-3-7-3h-2c0-2-1-3 0-5v-1c-1-1-2-1-2-2v-1l1-1-1-2z" class="D"></path><path d="M332 105l2 1h1c1 2 2 2 2 4-1-1-3-2-4-2l-1-1v-2z" class="E"></path><path d="M336 106h0l4 3 1 1v1h-2v-1h-2c0-2-1-2-2-4h1z" class="Y"></path><path d="M329 111l7 2h0l-1 2h-1c-2-1-4-2-5-3v-1z" class="c"></path><path d="M328 106h3s1 0 1 1l1 1h0-1l-3 3c-1-1-1-4-1-5z" class="T"></path><path d="M328 102c2 0 3 1 4 1 1 1 3 2 4 3h-1-1l-2-1v2c0-1-1-1-1-1h-3c0-1-1-3-1-4h0 1z" class="K"></path><path d="M328 106c0-1-1-3-1-4h0c2 2 3 2 5 3v2c0-1-1-1-1-1h-3z" class="D"></path><path d="M325 105h0v-2h0v-2h1v-2l2 3h-1 0c0 1 1 3 1 4s0 4 1 5h0v1c1 1 3 2 5 3l1 2 4 2-1 1h-1-1c-1-1-5-3-7-3h-2c0-2-1-3 0-5v-1c-1-1-2-1-2-2v-1l1-1-1-2z" class="B"></path><path d="M327 112c2 3 4 4 7 5h1l4 2-1 1h-1-1c-1-1-5-3-7-3h-2c0-2-1-3 0-5z" class="T"></path><path d="M327 124v-1l1-1 1 1 1-1c1 2 3 4 4 5h1v1c0 1 1 2 2 2 1 1 2 2 2 3 1 1 1 1 2 1v-1c2-3 4-5 6-6l5 4h1c-1 0-1 1-1 1-1 1-3 1-4 2-5 1-10 5-13 8h-1c-2-1-3-1-5-1l-5-2c-1-1-2-1-3-1v-2l2-2c2-3 3-7 4-10z" class="C"></path><path d="M323 134h3c0 1-1 1-1 2v2l-1 1c-1-1-2-1-3-1v-2l2-2z" class="D"></path><path d="M327 124v-1l1-1 1 1 1-1c1 2 3 4 4 5h1v1c0 1 1 2 2 2l-2 1h-1c-1 0-1 1-2 0-1 1-1 2-2 2s-1-1-2-1c-1 1-1 1-2 1 1-2 2-3 2-5 1-2 0-3-1-4z" class="W"></path><path d="M329 123l1-1c1 2 3 4 4 5h1v1c0 1 1 2 2 2l-2 1h-1c-1 0-1 1-2 0 0 0 1-3 1-4-1-2-4-2-4-4z" class="E"></path><path d="M337 130c1 1 2 2 2 3 1 1 1 1 2 1-1 1-2 2-3 2-2 1-4 2-6 2 0 1-2 1-3 1l-2-2 3-4c1 0 1-1 2-2 1 1 1 0 2 0h1l2-1z" class="G"></path><path d="M339 133c1 1 1 1 2 1-1 1-2 2-3 2-2 1-4 2-6 2h-1v-1h3c1 0 3-1 4-1v-1-1l1-1z" class="B"></path><path d="M355 355h1v5 6 1h-4-2v4c-1 1-1 2-2 3v6 1 9 4l1 28v1 4c1 0 1-1 2-1v1c-1 0-1 1-2 0-2-3-3-7-5-10-1-1-1-3-2-4l-6-8-10-12c-7-6-14-12-22-16-10-4-21-7-31-7v26 46 9 18h-1l1 1c1 0 2 0 3 1h4l1 37h0c-1-1-1-2-1-3l-2 1c0-1-1-1-1-1v-1h-2v2h1 0c0 1 0 1 1 2-1 0-1 0-2 1 0 2 0 3-1 4 2 10 5 19 10 27 2 3 5 6 6 10 0 2 1 4 1 6v1c-2 2-2 1-3 2-1 0-2 2-2 2 0 2-1 3-1 4h-2v-1l-1-1c-1 1-1 2-1 2v1c-2-1-1-2-2-4-1 0-1 0-2 1h0l-1-2v2h-1c0-1 0-1-1-1l2 6v1l-1-1v-1 1c2 3 5 6 6 10v1c-1-1-2-2-2-3l-3-3c2 3 3 5 4 7l2 2-3 9c-1 1-2 2-2 3h0l1 3c2 1 3 3 5 4l2 3h0c3 4 6 6 10 8-1 2-3 6-5 8-2-2-6-5-7-7v-1h-1l-2 1c-3 3-6 6-9 8 1 3 3 6 6 9-2 1-4 3-6 4-1-3-3-5-4-8 0-1-1-2-1-2-1-1-2-2-2-3-2-2-3-4-5-6v1h0c-2 0-2 1-3 2-1 0-2 0-3 1h-2 0-1v-16-18-13-1-1-2h0v-2c1-2 1-6 1-8l-1-1v-1-1l1-1-1-1 1-1-1-7 1-5-1-7v-1-1-1l-1-1h0 0 1c0-1 0-1-1-2 0-1 0-2 1-3 0-1 1-2 1-2l1-1-1-1h1v-1l-1-1h-1l-1-1h-3-1-2-1c-1 0-1 1-2 1l1 1h0l-1 1-1-4v7-3c-1 0-1-1-2-2-1 0-1-1-2-1 0-1-1-1-2-1h-1v-3l-2-2v-2h-1l-1-1c-2-2 0-4-1-6h1v-5-1l1-5c0-2 0-3 1-5h4c1 0 1 1 1 1 1 0 1-1 2-1s2 1 4 0h0l-1-1c2-1 5 0 6-1h-6-1-5-1-1v-1-4l-1-1h1v-6c-1-1-1-1-2-1h4v-1h0v-2h-1l1-1h0-2 0c0-1-1-2-1-3h-7-2v2 1h1v1c-1 0-1 0-1 1l-1-2h-1v3 1h-2-1v-5l-1 1v3 1h-1v-3-3l-1-1-2-1c-2 0-6-1-7-2-7-2-13-5-17-11l-1 4h0-1l1-8h0c0-2 0-3 1-4v-2c-1-2-1-4-1-6v-11-5l1-2c1-4 3-7 5-11 2-2 3-4 5-5 2-2 5-3 7-5h2l3-2v-1c2-1 2-1 4-1l1-1v-7l-1-1 1-2v-8 1l1 3h1l1-4 1-1v-1l2-1c2 1 7 0 9 0h5v-2h1l1-1v1h1v-1l2-1c1 0 3 0 3 1l1 1c2 1 7-1 8 1 2 1 4 1 6 0h1s1 0 2-1c1 1 4 0 5 0l17-2 1-2v1h1 6c0-1 0-2 1-3 0-1 1-2 2-3l-1-1c1 0 1-1 2-1l2 1c1 0 5-1 6-1s2 1 3 1c2-1 7 0 10-1h0c3-1 6 0 8 1h7c2 0 5-1 7-1 1 1 1 0 1 0h1l1-1h14z" class="g"></path><path d="M286 545l2 3h-1l-1-1v-2z" class="D"></path><path d="M265 540v-5c0 1 1 2 1 4l-1 1z" class="C"></path><path d="M344 368h2l1 1-1 1c-1 0-1 0-2-1l1-1h-1z" class="D"></path><path d="M277 545h1c1 1 1 1 1 3l-2-2v-1z" class="F"></path><path d="M256 482l1 1h2c-1 1-3 1-5 1l2-2z" class="c"></path><path d="M258 411c0-1-1-1-1-2-1 0 0-1 0-2h0c1 1 2 2 2 3 0 0-1 0-1 1z" class="F"></path><path d="M273 529l-2-2c1-1 3-1 4-2l-2 4z" class="E"></path><path d="M349 365c0 1 1 1 1 2v4c-1 1-1 2-2 3l1-9z" class="N"></path><path d="M331 371c-1-2-2-5-2-7 1 1 2 2 2 3v4z" class="D"></path><path d="M321 373h2c0 1 0 2 1 2l-1 1c-2 0-2 0-3-1v-1l1-1z" class="F"></path><path d="M244 511c0 1 1 1 1 2v1c-1 0-1 0-2 1h0-1v-1c0-1 1-2 2-3z" class="e"></path><path d="M331 371v-4c1 0 2 2 2 2v1l1 2h-2-1v-1h0zm-77 169c0-1-1-3-1-4h1c1 2 2 4 2 6h-1v1c0-1-1-2-1-3z" class="G"></path><path d="M257 502l1 1c1 1 1 4 1 5l-1 1h-1l1-1v-1c-1-1-2-1-2-2 1-1 1-2 1-2v-1z" class="X"></path><path d="M273 442v9c-1-2 0-5 0-7-2 0-3 1-4 1 0-1 1-3 2-4 0 1 1 2 1 2l1-1z" class="L"></path><path d="M268 479c0 1 1 1 1 1 2 2 1 4 1 6h-1c-1-2-1-4-1-6v-1z" class="D"></path><path d="M249 496c1 1 1 2 2 2h3c0 1 0 1 1 2h-7 2v-1-1c-1 0-1-1-1-2z" class="j"></path><path d="M242 491c1 3 1 14 0 16-1 0-1-1-2-2v-1h2v-3-10z" class="N"></path><path d="M254 529v-2c0-1 1-2 2-2l1 4c-1 0-2 1-3 2v-2z" class="c"></path><path d="M254 529l-1-2c0-3 0-5 2-7h0l1 5c-1 0-2 1-2 2v2z" class="E"></path><path d="M271 505l1 1c-2 2-4 5-6 7v1c-1 0 0-1 0-1 0-3 3-6 5-8z" class="C"></path><path d="M261 367c1 1 4 0 5 0-3 1-6 3-10 4l-3-1c2 0 4-1 6-2 0 0 1 0 2-1z" class="V"></path><path d="M288 548c1 2 1 2 0 3-2 3-2 7-3 10l-2-2c2-4 4-7 4-11h1zm57-132v-2h0c0-1 0-1-1-2 0 1 0 1-1 1 0-1 0-1-1-1l1-1v1h2 0c0-1 1-2 1-2 1 0 1 0 2 1h0c-1 1-1 1 0 2-1 1 0 3-2 3h-1z" class="S"></path><path d="M339 371l2-1c1 0 0 1 2 1h0s0 1 1 1l1-1h1v2l-1-1-1 1-2 2c-2 0-1-1-3 0h0l1-1v-1c-1-1-1-1-1-2h0z" class="W"></path><path d="M275 525c0 1 1 0 1 1 1 1 1 2 2 3l-3 4c0-1-1-3-2-4l2-4z" class="K"></path><path d="M256 511c1 0 2 0 3 1v3c-1 1-3 1-4 1h-6-4l-2-1h0c1-1 1-1 2-1v-1c1 0 1 1 2 2h0 1c2 0 7 1 8 0s1-2 1-3l-1-1z" class="c"></path><path d="M245 513c1 0 1 1 2 2h0-4c1-1 1-1 2-1v-1z" class="Y"></path><path d="M254 497l1-1h2 1l1 5-4-1c-1-1-1-1-1-2h-3c1 0 2 0 3-1z" class="I"></path><path d="M245 516h4 6v1l-1 1h-3-1l-1-1h-3-1-2c1 0 1-1 2-1z" class="D"></path><path d="M245 516h4 0c-1 1-2 0-3 1h-1-2c1 0 1-1 2-1z" class="C"></path><path d="M249 517h6l-1 1h-3-1l-1-1z" class="O"></path><path d="M251 518h3c-1 3-2 5-3 7-1 3 0 5-1 8v-1-1-1l-1-1h0 0 1c0-1 0-1-1-2 0-1 0-2 1-3 0-1 1-2 1-2l1-1-1-1h1v-1l-1-1z" class="N"></path><path d="M254 540c0 1 1 2 1 3v1s-1 1 0 2h0c1 0 1 1 2 2-1 1-2 1-2 2v-1l-1 1c-1-1-1-2-1-2v-3c0-2 0-4 1-5zm15 1c-1-2-1-3-2-4v-1h2l1 1v2 1l1-1h-1l2-2c0 1 1 1 1 2v2s1 2 2 2v1c1 0 1 1 1 1v1h0c-1-2-3-3-3-5-1 0-1-1-1-1l-3 1z" class="D"></path><path d="M314 375l3 3 1-1c1 1 1 1 2 1 1-1 3 0 4-1l1-1c0 1-2 3-3 4-4 0-6 0-10-3h0c1 0 2 0 2 1h1c1 0 1 0 1 1h1 1c-1 0-1 0-1-1-2-1-3-2-3-3z" class="F"></path><path d="M269 541l3-1s0 1 1 1c0 2 2 3 3 5h0l2 3h-1l-2-1c-1-1-1-2-2-3h-1 0l-3-4zm-11-130c0-1 1-1 1-1 2 4 4 7 6 11v4l-3-5h0c0-3-3-7-4-9z" class="K"></path><path d="M252 511h4l1 1c0 1 0 2-1 3s-6 0-8 0c0 0 0-1 1-1 0-1 1-1 1-1 2-1 3 1 4 0s1-1 1-2h-3z" class="N"></path><path d="M339 375h0c2-1 1 0 3 0l2-2 1-1 1 1c-1 1-1 1-2 3h-2s-1 0-1 1 1 2 2 3h-1c-1 0-2-1-2-2s-2-2-3-3l1-1 1 1z" class="G"></path><path d="M314 375c-1-2-2-3-2-4v-5l1-1c1 1 1 2 1 3 1 4 1 7 4 9l-1 1-3-3z" class="K"></path><path d="M264 480h1 0c1 1 0 2 0 3 1 3 0 6 0 10h-1c-1-3-1-10 0-13z" class="D"></path><path d="M334 372l-1-2v-1s1 1 1 2h3 2 0c0 1 0 1 1 2v1l-1 1-1-1-1 1-1-1c-1 0-1 1-2 1h-2c0-1 1-2 2-2v-1z" class="S"></path><path d="M334 371h3 2 0c0 1 0 1 1 2v1l-1 1-1-1c-1 0-1-1-2-1 0-1-1-2-2-2z" class="C"></path><path d="M259 420c-1-1-2-3-2-4 1-1 1-1 0-2v-1h1c1 2 3 4 4 7h0v1c0 1 0 1-1 2h0l-2-3z" class="B"></path><path d="M257 573c-3-5-3-9-4-14v-6l4 10c-1-1-2-3-3-5v1 2l1 1c0 2 1 4 2 6v5z" class="i"></path><path d="M355 361l1-1v6 1h-4-2c0-1-1-1-1-2h-1c0-1 0-1 1-2 1 0 1 1 1 1 2 0 4 0 5-1v-2h0z" class="B"></path><path d="M341 395c0-1 0-2-1-3 0-1 0-1 1-2l-1-1 1-1c-1-1-2-2-4-3l-1-1h-2v-1c1 0 1 0 2 1 3 1 6 3 7 6 0 1 1 1 1 2v2c0-1 0-1-1-2 0 2 1 2-1 4h0l-1-1z" class="K"></path><path d="M335 395v-1l-1-1c1 0 3-2 4-3v-1h0v-1-1c1 1 1 1 1 2h0c-1 1-1 1-1 2 0 0-1 2-2 2h0v1l1-1h1l-1 1c1 0 2-1 2 0 1 0 1 1 2 1v1h0c-1 1-2 1-3 2-1 0-1 1-2 1h0-1l-1 1v-1-1s0-1-1-1l1-1v2c2 0 3-1 4-1v-1c-1 0-2-1-3-1h1-1z" class="J"></path><path d="M244 511c2-1 6 0 8 0h3c0 1 0 1-1 2s-2-1-4 0c0 0-1 0-1 1-1 0-1 1-1 1h-1 0c-1-1-1-2-2-2 0-1-1-1-1-2z" class="O"></path><path d="M247 493h2 0l1 1c0 1-1 1-1 2s0 2 1 2v1 1h-2-3v-3l2-4z" class="k"></path><path d="M249 493l1 1c0 1-1 1-1 2s0 2 1 2v1 1c-1-1-1-2-1-3l-1-1h0c0-2 0-2 1-3h0z" class="H"></path><path d="M247 493h2c-1 1-1 1-1 3h0c-1 0-2 0-3 1l2-4z" class="U"></path><path d="M326 393h0c3 2 6 6 8 9 1 0 1 1 2 1v-1h3 0c1-1 2-2 3-2 1-1 1 0 2-1l1 1c-1 0-1 1-2 1l-1 1c-1 0-2 1-4 2 0 0-1 1-2 1l-10-12z" class="J"></path><path d="M254 591c-1-2 0-2 0-4h-1v-1h0v-2c1-1 0-2 1-3 0-1 0-3-1-4 1-2 0-3 0-5 0-1 0-2 1-3h0c1 1 1 2 1 3l2 4h-1 0-2c0 1 0 1 1 1v1 1l-1 2v2c0 2-1 2 0 4v1 3z" class="D"></path><path d="M254 576c1-2 0-3 1-4l2 4h-1 0-2z" class="J"></path><path d="M260 425c0 2 0 3 1 4h1v4c1 0 1 0 2 1-1 1-3 3-4 5h0l-1 1c-1 1-2 2-3 2 1-1 3-3 3-4v-3c1 0 0-1 0-1h0l1-1v-4-4z" class="C"></path><path d="M261 429h1v4s-1 1-1 2v-6z" class="L"></path><path d="M253 502h4v1s0 1-1 2c0 1 1 1 2 2v1l-1 1-8-1c1 0 1-1 1-2h0 3c1-2 1-2 0-4z" class="N"></path><path d="M248 490c1 0 2 0 3 1l1 1v1h0c1 0 1 0 2 1v3c-1 1-2 1-3 1s-1-1-2-2c0-1 1-1 1-2l-1-1h0-2l1-3z" class="Z"></path><path d="M248 490c1 0 2 0 3 1h-2v1 1h0-2l1-3z" class="O"></path><path d="M252 493c1 0 1 0 2 1v3c-1 1-2 1-3 1s-1-1-2-2c0-1 1-1 1-2 1 0 1 1 2 2l1-1c-1-1-1-1-1-2z" class="o"></path><path d="M312 378c-3 0-4-2-7-4-2-2-4-3-6-5 1 0 3 2 4 2s1 0 2-1c-1-1-4-3-4-4h1c2 2 4 4 6 7h0l4 5zm19 7h1 1c2 1 2 1 2 3v1l-1 1h0l1-1c0-1 1-1 1-1v1l-1 1h0c-1 1-2 2-3 2s-2-2-3-3v-1l1 1v-1c-1 0-1-1-1-1 1-1 1-2 2-2z" class="D"></path><path d="M330 388v-1l2-2c1 1 1 1 2 1 0 1 0 1 1 1-1 1-1 1-1 2l-1 1c-2 0-2-1-3-2z" class="B"></path><path d="M330 388c1 0 2-1 3-1v2h1l-1 1c-2 0-2-1-3-2z" class="J"></path><path d="M251 479c2 0 5 0 7-1 1 2 1 4 1 5h-2l-1-1-2 2h-3l-1-2v-3h1z" class="N"></path><path d="M255 479h2l1 1-1 1-1 1s-1 0-2-1c1 0 1-1 1-2z" class="V"></path><path d="M251 479h4c0 1 0 2-1 2-1 1-1 1-2 1v-1h-1v-2z" class="Q"></path><path d="M251 479h0v2h1v1c1 0 1 0 2-1 1 1 2 1 2 1l-2 2h-3l-1-2v-3h1z" class="e"></path><path d="M285 543c0 1 1 1 1 2v2l1 1c0 4-2 7-4 11v1h-1l2-7h0l-2 2-1-1c1-3 2-5 4-8v-3z" class="C"></path><path d="M282 555l2-2h0l-2 7h1v-1l2 2-2 3-1-1c-1 1-1 2-1 2v1c-2-1-1-2-2-4-1 0-1 0-2 1h0l-1-2 1-1 2-2h0c-1-1-1-1-1 0h-1v-1c1 0 2 0 3 1h1c0-1 0-2 1-3h0z" class="J"></path><path d="M279 558c0 1 1 1 1 2v1c-1 1-2 0-2 0l-1-1 2-2z" class="S"></path><path d="M282 560h1v-1l2 2-2 3-1-1h-1c0-1 0-2 1-3z" class="G"></path><path d="M283 559l2 2-2 3-1-1c1-1 0-1 1-3v-1z" class="B"></path><path d="M249 508h-5l1-6h8c1 2 1 2 0 4h-3 0c0 1 0 2-1 2z" class="O"></path><path d="M250 506h-1l-1-1c-1-1-1-1-1-2l2-1c0 1 1 2 2 3l-1 1h0z" class="M"></path><path d="M272 512l-1 3c-3 5-5 12-3 18h0c0 1 0 2-1 2h-1c0-2-1-4-1-7 0-7 2-12 7-17v1z" class="D"></path><path d="M283 547h-1 0c-1-1-1-2-1-3-3-2-5-6-4-9 0-1 1-2 2-2h0c1 3 3 9 5 10h1v3h-1c-1 0-1 1-1 1zm61-155l1 2c1 1 1 2 1 2v2c1 1 1 1 0 1l1 1h0c-1 0-1 0-2 1-1 0-2 1-3 1-1 1-2 2-3 2s-1 1-2 1c1 1 1 1 2 1v-1h1c1 0 4-2 4-2 1-1 2-1 3-1h1c-1 1-1 2-2 2l-2 2c-2 0-2 1-4 1l1 1v2h0c0 1 1 3 2 4l1-1h0v2s0 1 1 1h0 1l-1 1h-1c-1-1-1-3-2-4l-6-8c1 0 2-1 2-1 2-1 3-2 4-2l1-1c1 0 1-1 2-1v-3c0-1 0-2-1-3v-2z" class="E"></path><path d="M325 376c1-1 1-2 1-3-1-1 0-2-1-3 0-3-1-5-2-7h1c2 3 4 8 4 12-1 2-2 4-4 5-1 1-2 1-3 1-4 1-6-1-9-3l-4-5h0l4 4h0c4 3 6 3 10 3 1-1 3-3 3-4z" class="J"></path><path d="M262 420l3 5c1 3 2 6 0 9 0 1-1 2-2 3l-3 2h0c1-2 3-4 4-5-1-1-1-1-2-1v-4h-1c-1-1-1-2-1-4-1-1-1-3-1-5l2 3h0c1-1 1-1 1-2v-1z" class="R"></path><path d="M262 425c1 2 3 6 2 8v1c-1-1-1-1-2-1v-4c0-1-1-2 0-4z" class="F"></path><path d="M259 420l2 3 1 2c-1 2 0 3 0 4h-1c-1-1-1-2-1-4-1-1-1-3-1-5z" class="S"></path><path d="M257 605v-1c-3-3-5-8-4-12h1l2 4h1 1v1l2-1h0c1 1 1 1 2 1v2 2l-2 2 1 1v1c-1 0-1-1-2-1s-1 0-2 1z" class="P"></path><path d="M257 596h1v1c1 1 2 4 4 4l-2 2c-1-2-2-5-4-7h1z" class="C"></path><path d="M258 597l2-1h0c1 1 1 1 2 1v2 2c-2 0-3-3-4-4z" class="F"></path><path d="M272 545h1c1 1 1 2 2 3l2 1h1c0 1 1 1 2 2v1h1c1-2 1-3 2-5 0 0 0-1 1-1h1c-2 3-3 5-4 8l1 1h0c-1 1-1 2-1 3h-1c-1-1-2-1-3-1 0-2-1-3-2-4-1-2-3-4-4-6 0-1 1-1 1-2h0z" class="R"></path><path d="M275 548l2 1h1c0 1 1 1 2 2v1h1v1h-1l-1-1c-2-1-3-2-4-4z" class="J"></path><path d="M272 545h0l7 11c1 0 2-1 2-2l1 1h0c-1 1-1 2-1 3h-1c-1-1-2-1-3-1 0-2-1-3-2-4-1-2-3-4-4-6 0-1 1-1 1-2z" class="D"></path><path d="M249 487s1-1 1-2h2l2 1 2-1v1l2 6v4h-1-2l-1 1v-3c-1-1-1-1-2-1h0v-1l-1-1c-1-1-2-1-3-1v-1l1-2z" class="i"></path><path d="M249 487s1-1 1-2h2v2h-3zm2 2h3c0 1 0 2 1 3h3v4h-1-2l-1 1v-3c-1-1-1-1-2-1h0v-1l-1-1c-1-1-2-1-3-1v-1c1 1 2 0 3 0z" class="d"></path><path d="M248 489c1 1 2 0 3 0h2c0 1 0 2-1 3l-1-1c-1-1-2-1-3-1v-1z" class="N"></path><path d="M250 472h5v1l1 2v1 2h1v-1h1v1c-2 1-5 1-7 1h-1 0c-1 0-2 0-3-1h-4c0-1 0-1 1-3l2-2h4 1l-1-1z" class="E"></path><path d="M256 475v1 2h-5 0v-2h2c1-1 2-1 3-1z" class="b"></path><path d="M250 472h5v1l1 2c-1 0-2 0-3 1h-2v-3l-1-1z" class="Q"></path><path d="M253 476v-3h2l1 2c-1 0-2 0-3 1z" class="O"></path><path d="M246 473h4c0 1 0 2-1 2 0 0-1 0-2 1h0c1 1 3 1 3 2h-3-4c0-1 0-1 1-3l2-2z" class="I"></path><path d="M256 396h0c1 1 2 1 2 2 6 5 10 11 11 18 1 2 1 5 1 7l-3-6c-2-4-4-8-6-11-1-2-4-5-5-7v-3z" class="E"></path><path d="M257 576h1l1 2v2c1 1 2 2 2 4h0v1h0c0 2-2 3-3 4v1l1 1-2 1v1 1 2h-1l-2-4h1s0-1-1-1v-3-1c-1-2 0-2 0-4v-2l1-2v-1-1c-1 0-1 0-1-1h2 0 1z" class="G"></path><path d="M257 576h1l1 2v2c1 1 2 2 2 4h0v1l-1 1-2 2h-1 0l1-1c0-1 1-2 2-3v-1h-1l-2 3h-1c0-1 1-2 1-3-1 0-1 0-1-1h0v-1h-1-1l1-2v-1-1c-1 0-1 0-1-1h2 0 1z" class="J"></path><path d="M257 576h1l-1 1-1 1c0 1 1 1 1 2h0-1 0c0-1-1-1-1-1v-1-1c-1 0-1 0-1-1h2 0 1z" class="B"></path><path d="M265 421v4h1v3c1-1 0-2 0-4 0-1 0-3-1-4h1v-1c1 2 1 3 2 5 0 1 0 4 1 5s0 5 0 6c-2 7-7 11-11 16-1-1-1-4 0-6 2-3 6-5 7-9h0c1 0 1-1 1-1h0-1v1l-2 1c1-1 2-2 2-3 2-3 1-6 0-9v-4z" class="F"></path><defs><linearGradient id="o" x1="265.081" y1="540.218" x2="275.347" y2="558.587" xlink:href="#B"><stop offset="0" stop-color="#1d1c1c"></stop><stop offset="1" stop-color="#525253"></stop></linearGradient></defs><path fill="url(#o)" d="M265 540l1-1 5 8c1 2 3 4 4 6 1 1 2 2 2 4v1h1c0-1 0-1 1 0h0l-2 2-1 1v2h-1c0-1 0-1-1-1-1-1-7-12-8-13l1-1c0-2-1-5-2-8z"></path><path d="M255 414c0 5 0 55-1 57h0c1 1 1 0 1 2h0v-1h-5l1-1h0 1v-5-8-36l2-1c1-2 0-4 1-7z" class="c"></path><defs><linearGradient id="p" x1="304.669" y1="364.344" x2="301.645" y2="354.169" xlink:href="#B"><stop offset="0" stop-color="#898989"></stop><stop offset="1" stop-color="#aeabac"></stop></linearGradient></defs><path fill="url(#p)" d="M293 357c1 0 1-1 2-1l2 1c1 0 5-1 6-1s2 1 3 1c2-1 7 0 10-1h0l-1 1v2h0v3c-2 1-21 2-24 2h0c0-1 0-2 1-3 0-1 1-2 2-3l-1-1z"></path><path d="M271 391l-1 1c-1 2-2 3-2 6v2h-1c-2-1-5-3-6-5 0-1 0-1-1-2h0l1-1-3-3v-1h3c0-1-1-1-1-2v-2c2 0 3-1 4-2l1 1h1c1-1 3-1 4-2v-1l1 1c1 0 0 1 0 2v2h0c0 1 0 1-1 2h-2 1s1 0 1 1h1v3z" class="C"></path><path d="M270 388h1v3c-1 1-2 1-3 1s-1 1-2 0l1-2c-1 0-1-1-2-1 2-1 4-1 5-1z" class="W"></path><path d="M255 543v-1h1l4 9c1 2 2 3 2 6h1l1 1s1 0 1 1l1-1 3 5c0 1 0 1-1 2v1l-1 2v-1h2v1c-1 1-2 2-3 2l-1 1v1c1-1 1-1 2-1 1-1 2-2 4-3-1 2-3 5-6 6 0 0 0-1-1-1-1-1-1-2-2-4h0c-1-2-2-4-3-5v-1l1-1c0-1 0-1-1-2v1h0v1l-1-1c-2-2-2-4-3-6l3-3v-1l-2 2s-1 1-2 1c0-2 3-3 4-5h-1c0 1-1 2-2 2h-1l1-1c0-1 1-1 2-2-1-1-1-2-2-2h0c-1-1 0-2 0-2v-1z" class="B"></path><path d="M263 557l1 1s1 0 1 1l1-1 3 5c0 1 0 1-1 2s-3 3-4 3c1-2 3-3 4-6-2 1-4 3-6 4h0c0-1 1-1 2-2s2-1 2-3h-1c-1 1-2 3-4 3v-1l4-3v-1h-1c-1 0-1 1-2 2-1-1 1-2 1-4z" class="C"></path><path d="M243 470v1h0c-1 0-1 0-2 1h2 3v1l-2 2c-1 2-1 2-1 3h4c1 1 2 1 3 1h0v3l1 2h-5-6-1-5-1-1v-1-4l-1-1h1v-6c-1-1-1-1-2-1h4 4 1 0c1 0 3 0 4-1z" class="J"></path><path d="M240 484v-1-4h2v2 1h0l-1 1c0 1-1 1-1 1z" class="a"></path><path d="M240 476v-1-3h1 2 3v1l-2 2c-1 2-1 2-1 3h-3 0v-2z" class="V"></path><path d="M240 476c0-1 0-1 1-2h2v1c-1 1-1 1-2 1h-1z" class="H"></path><path d="M243 474l1 1c-1 2-1 2-1 3h-3 0v-2h1c1 0 1 0 2-1v-1z" class="U"></path><path d="M233 484v-5h6v4 1h-5-1z" class="f"></path><path d="M234 484c1-1 1-1 1-2 2 0 3 1 4 1v1h-5z" class="Z"></path><path d="M237 475h0c1 1 1 2 2 2-1 1-5 1-6 1v-6h6v3h-2z" class="Q"></path><path d="M239 472v3h-2 0c0-1-1-2-1-2-1 0-1 0-1-1h4z" class="f"></path><path d="M242 479h8v3l1 2h-5-6 0s1 0 1-1l1-1h0v-1-2h0z" class="Q"></path><path d="M242 479h0l1 1v1l-1 1v-1-2z" class="f"></path><defs><linearGradient id="q" x1="246.722" y1="484.866" x2="243.537" y2="481.176" xlink:href="#B"><stop offset="0" stop-color="#717171"></stop><stop offset="1" stop-color="#898887"></stop></linearGradient></defs><path fill="url(#q)" d="M250 482l1 2h-5-6 0s1 0 1-1l1-1c2-1 6 0 7 1h1v-1z"></path><path d="M331 371v1h1 2v1c-1 0-2 1-2 2h2c1 0 1-1 2-1l1 1c1 1 3 2 3 3s1 2 2 2h1 0l4 4c1 0 1-2 1-3v9 4l1 28v1 4c1 0 1-1 2-1v1c-1 0-1 1-2 0-2-3-3-7-5-10h1c1 1 2 3 3 5v1-6-1-4c-1-1 0-1 0-1v-5 1l-1 1c0 1-1 1-1 1h-1c0 1 0 1-1 1l-1 1c0-1 0-1-1-2 2-1 3-3 5-3l1-1v-3c-1-2 0-5 0-7-1 0-1-1-1-1-1-2-1-3-3-5l-1-1c-1-2-5-6-8-6h0-4c-1-1-2-1-4 0-1-1 0-1 0-2h-3c2-1 3-3 4-5l-1 3-1 1v1l1-1v1l1-1v-1l3-4v-3z" class="D"></path><path d="M348 394c-1-1-2-3-3-5-3-4-7-8-13-8h-5l3-1c2-1 4 0 7 1s6 3 8 7c1 1 2 3 3 4v-2 4z" class="J"></path><defs><linearGradient id="r" x1="254.414" y1="546.429" x2="274.13" y2="547.014" xlink:href="#B"><stop offset="0" stop-color="#696969"></stop><stop offset="1" stop-color="#868586"></stop></linearGradient></defs><path fill="url(#r)" d="M254 531c1-1 2-2 3-2 3 6 5 14 9 20 1 1 7 12 8 13l2 6v1l-1-1v-1 1c2 3 5 6 6 10v1c-1-1-2-2-2-3l-3-3c-2-3-5-6-7-10l-3-5-5-11c-3-5-5-10-7-16z"></path><path d="M261 547c4 3 6 9 8 13l6 8c2 3 5 6 6 10v1c-1-1-2-2-2-3l-3-3c-2-3-5-6-7-10l-3-5-5-11z" class="d"></path><path d="M355 355h1v5l-1 1c-4-1-7 0-11 0h-4-1 0l-18 1c-1 0-4 1-5 0h-1v-3h0v-2l1-1h0 0c3-1 6 0 8 1h7c2 0 5-1 7-1 1 1 1 0 1 0h1l1-1h14z" class="Z"></path><path d="M341 355h14v1c-1 0-2 1-3 2-1 0-2 1-2 1h-2c-2 0-6 0-8 1-1-1 0-3-1-4h0 1l1-1z" class="M"></path><path d="M341 355h14v1c-4 0-7 1-10 1h-3c-1 0-1 1-2 0v-1l1-1z" class="H"></path><path d="M316 356h0c3-1 6 0 8 1h7c2 0 5-1 7-1 1 1 1 0 1 0h0v2 1c-3 1-5 1-8 1-1 0-10 0-10 1h-1c-1 0-2 1-3 0h-1v-4h-1l1-1h0z" class="V"></path><path d="M316 356h0c3-1 6 0 8 1h7c2 0 5-1 7-1 1 1 1 0 1 0h0v2h-1c-2 0-3 0-5 1h-8c-2 0-4-2-6-1h0c-1 0-2 0-3-2h0z" class="Q"></path><path d="M269 563c2 4 5 7 7 10s3 5 4 7l2 2-3 9c-1 1-2 2-2 3l-1-1-5-6c-1-2-2-4-4-6l-6-11 1-1h0c1 2 1 3 2 4 1 0 1 1 1 1 3-1 5-4 6-6-2 1-3 2-4 3-1 0-1 0-2 1v-1l1-1c1 0 2-1 3-2v-1h-2v1l1-2v-1c1-1 1-1 1-2z" class="W"></path><path d="M277 579l1 1c-1 1-2 3-4 4h-1c1-1 1-2 1-2 0-1 2-3 3-3z" class="B"></path><path d="M269 579h0 1c1-1 3-3 5-4h0c-1 2-4 4-4 6v2l-3-3 1-1z" class="L"></path><path d="M268 580c-1-2-2-4-3-5l5-4h1c1 1-3 3-4 5 1 1 2 1 2 3l-1 1z" class="P"></path><path d="M275 575l2 1v1h0v2c-1 0-3 2-3 3 0 0 0 1-1 2h0c-1 0-1-1-2-1v-2c0-2 3-4 4-6z" class="C"></path><path d="M280 580l2 2-3 9c-1 1-2 2-2 3l-1-1v-1-1c1-1 1-2 1-2v-1c-1-2 2-6 2-8h1z" class="T"></path><path d="M273 470c1 0 2 0 3 1h4l1 37h0c-1-1-1-2-1-3l-2 1c0-1-1-1-1-1v-1h-2v2h1 0c0 1 0 1 1 2-1 0-1 0-2 1 0 2 0 3-1 4 0-1 0-2-1-3l-1 2v-1l2-2-1-39z" class="i"></path><path d="M241 486h1v4 1 10 3h-2v1c1 1 1 2 2 2 0 1-1 2-1 3l-1 3c-1 1-1 2-1 3v7-3c-1 0-1-1-2-2-1 0-1-1-2-1 0-1-1-1-2-1h-1v-3l-2-2v-2h-1l-1-1c-2-2 0-4-1-6h1v-5-1l1-5c0-2 0-3 1-5h4c1 0 1 1 1 1 1 0 1-1 2-1s2 1 4 0h0z" class="I"></path><path d="M237 507v-1h-1v-3h1v2h1c0 1 1 1 1 1-1 1-1 1-2 1zm1 1c1 0 2 0 3 1v1l-1 3-1-1-1-1c1-1 1-1 1-2l-1-1z" class="N"></path><path d="M239 512v-2h2l-1 3-1-1z" class="b"></path><path d="M240 505c1 1 1 2 2 2 0 1-1 2-1 3v-1c-1-1-2-1-3-1l-1-1c1 0 1 0 2-1h1v-1z" class="c"></path><path d="M230 508c1-1 3-2 5-2 1 3 0 8 3 10l1 1v3c-1 0-1-1-2-2-1 0-1-1-2-1 0-1-1-1-2-1h-1v-3l-2-2v-2h-1l1-1z" class="V"></path><path d="M230 509c1 0 1-1 2-1s2 0 2 1c1 2 1 3 1 5-1-1-1-3-1-4h-2v3l-2-2v-2z" class="M"></path><path d="M232 513v-3h2c0 1 0 3 1 4v2h-2-1v-3z" class="O"></path><path d="M241 486h1v4 1 10 3h-2v1 1h-1s-1 0-1-1h-1v-2-8-4l-1-3v1l-1-1v-1c1 0 1-1 2-1s2 1 4 0h0z" class="Z"></path><path d="M239 503v-1l1 1 2-2v3h-2v-1h-1z" class="Y"></path><path d="M239 503h1v1 1 1h-1s-1 0-1-1h0l1-2z" class="P"></path><path d="M241 486h1v4h0l-1-1c-2 0-3-1-5-1v1l-1-1v-1c1 0 1-1 2-1s2 1 4 0h0z" class="Y"></path><path d="M237 491v-1c1 0 2 1 2 2h1v-1c1 1 1 1 1 2 0 2 1 6 0 8h-1l-1 1v-1c-1 1-1 3-1 4h0-1v-2-8-4z" class="U"></path><path d="M237 491v-1c1 0 2 1 2 2 0 0-1 1-1 2l-1 1v-4z" class="a"></path><path d="M240 501c-1-1-1-2-1-2-1 0-1 1-2 1v-2c0-1 1-1 2-1 0-2 1-3 1-4h1c0 2 1 6 0 8h-1z" class="Q"></path><path d="M230 486c2 1 3 1 4 1 1 2 1 3 1 5h0c1 3 0 5 0 8v6c-2 0-4 1-5 2l-1 1-1-1c-2-2 0-4-1-6h1v-5-1l1-5c0-2 0-3 1-5z" class="k"></path><path d="M229 506v-2h1 1c0 1 0 2-1 3l-1-1h0z" class="n"></path><path d="M227 502h1v-5-1c0 3 0 7 1 10h0l1 2-1 1-1-1c-2-2 0-4-1-6z" class="E"></path><path d="M230 486c2 1 3 1 4 1 1 2 1 3 1 5 0-1-2-1-2-2-1-1 0-1-2-2-1 0-2 2-2 3 0-2 0-3 1-5z" class="H"></path><path d="M231 489c1 0 1 1 1 1s0 1 1 1c0 2 0 6-1 8v1c-2-3 0-8-1-11z" class="m"></path><path d="M235 492c1 3 0 5 0 8v6c-2 0-4 1-5 2l-1-2 1 1c1-1 1-2 1-3v-1c1 0 1-1 2-2 1-2 0-4 1-6l1-1v-2z" class="h"></path><path d="M235 500v6c-2 0-4 1-5 2l-1-2 1 1c1-1 1-2 1-3 1 0 2 0 4-1v-3z" class="a"></path><defs><linearGradient id="s" x1="282.545" y1="583.67" x2="265.202" y2="590.311" xlink:href="#B"><stop offset="0" stop-color="#444443"></stop><stop offset="1" stop-color="#777778"></stop></linearGradient></defs><path fill="url(#s)" d="M255 562l-1-1v-2-1c1 2 2 4 3 5l4 7 6 11c2 2 3 4 4 6l5 6 1 1h0l1 3c2 1 3 3 5 4l2 3h0c3 4 6 6 10 8-1 2-3 6-5 8-2-2-6-5-7-7v-1h0c-1-2-2-3-3-4l-4-5-3-3-6-9-2 2h-1c-1 1-2 2-4 3h0l-2 1v-1h-1v-2-1-1l2-1-1-1v-1c1-1 3-2 3-4h0v-1h0c0-2-1-3-2-4v-2-1l-2-4v-5c-1-2-2-4-2-6z"></path><path d="M267 581c2 2 3 4 4 6v2c-1-1-4-5-4-6v-2z" class="d"></path><path d="M285 604c-1 1-2 0-2 0l-2-2h0 0 1 0l-3-3c0-1-1-1-1-2s-1-1-2-2l1-1 1 3c2 1 3 3 5 4l2 3h0z" class="N"></path><path d="M255 562h0c1 0 1 0 1 1s0 2 1 3c0 1 0 2 1 2 1 2 1 4 2 6 1 5 4 9 7 13v1c1 1 1 2 1 2v1l2 2c1 1 1 2 2 3s2 3 3 4v1h0c1 1 1 1 1 2l-3-3-6-9-2 2h-1c-1 1-2 2-4 3h0l-2 1v-1h-1v-2-1-1l2-1-1-1v-1c1-1 3-2 3-4h0v-1h0c0-2-1-3-2-4v-2-1l-2-4v-5c-1-2-2-4-2-6z" class="e"></path><path d="M259 577l5 9-1 1-4 4-1-1v-1c1-1 3-2 3-4h0v-1h0c0-2-1-3-2-4v-2-1z" class="K"></path><path d="M264 586l3 5-2 2h-1c-1 1-2 2-4 3h0l-2 1v-1h-1v-2-1-1l2-1 4-4 1-1z" class="J"></path><path d="M263 587v1l-6 5v-1l2-1 4-4zm1 5v1c-1 1-2 2-4 3h0l-2 1v-1s0-1 1-1c2-1 3-2 5-3z" class="D"></path><path d="M264 586l3 5-2 2h-1v-1c-1-1 0-1-1-2 0 1 0 1-1 1 0-1 1-1 1-2v-1-1l1-1z" class="G"></path><path d="M267 591l6 9 3 3 4 5c1 1 2 2 3 4h0-1l-2 1c-3 3-6 6-9 8 1 3 3 6 6 9-2 1-4 3-6 4-1-3-3-5-4-8 0-1-1-2-1-2-1-1-2-2-2-3-2-2-3-4-5-6-1-3-4-7-5-10-1-1-1-1 0-2 2 0 2 2 4 3h0l-1-1c1-1 1-1 2-1s1 1 2 1v-1l-1-1 2-2v-2-2c-1 0-1 0-2-1 2-1 3-2 4-3h1l2-2z" class="R"></path><path d="M268 616h1 0l1 1c0 1 0 1-1 2h0l-1-3z" class="L"></path><path d="M259 609h0l1 1c1 1 1 1 1 2 1 1 4 4 4 6h-1c-1-2-5-6-5-9z" class="d"></path><path d="M280 608c1 1 2 2 3 4h0-1l-2 1h-1l1-1h-1-1 0c1-1 1-2 1-2l-1-2h2z" class="S"></path><path d="M275 611l2-1v1c-1 2-3 3-5 4v1h-2-1 0c1 0 0 0 1-1 0 0 0-1 1-1l2-2h0l2-1z" class="P"></path><path d="M257 605c1-1 1-1 2-1s1 1 2 1l3 5 4 6 1 3h-1c-1 0-1-1-2-2l-3-5c-1-2-3-3-4-5 0-1 0-1-1-1l-1-1z" class="e"></path><path d="M273 600l3 3 4 5h-2l1 2-1-1-1 2v-1l-2 1-2 1h0l-2 2c-1 0-1 1-1 1-1 1 0 1-1 1h0 0-1l-4-6c3-2 6-4 8-6v-2h0l1-2z" class="S"></path><path d="M273 605c1 0 1 0 2 2-2 0-4 3-6 4 0 1 0 0-1 1h0-1v-1c2-1 4-4 6-6h0z" class="F"></path><path d="M275 607c0 1-2 4-3 5h0 1 0l-2 2c-1 0-1 1-1 1-1 1 0 1-1 1l-1-4c1-1 1 0 1-1 2-1 4-4 6-4z" class="B"></path><path d="M273 600l3 3 4 5h-2l1 2-1-1-1 2v-1l-2 1-2 1h-1 0c1-1 3-4 3-5-1-2-1-2-2-2h0l-1-1v-2h0l1-2z" class="W"></path><path d="M272 602h1l1 1h0c-1 1-1 1-1 2h0l-1-1v-2h0z" class="E"></path><path d="M275 611c0-2 1-2 2-3h1l1 2-1-1-1 2v-1l-2 1z" class="K"></path><path d="M267 591l6 9-1 2h0v2c-2 2-5 4-8 6l-3-5v-1l-1-1 2-2v-2-2c-1 0-1 0-2-1 2-1 3-2 4-3h1l2-2z" class="T"></path><path d="M266 596v1l1-1v1c0 1-1 2-2 3-1 0-2 1-3 1v-2c1-1 2-2 4-3z" class="W"></path><path d="M264 593h1c1 1 1 2 1 3-2 1-3 2-4 3v-2c-1 0-1 0-2-1 2-1 3-2 4-3z" class="K"></path><path d="M261 604h1c1 0 2-1 3-2h1c1-1 2-1 3-2h0c-1 3-5 3-6 5l1 2h1c1 0 1-1 2-1 1-1 3-3 4-3l1-1v2c-2 2-5 4-8 6l-3-5v-1z" class="W"></path><path d="M251 377v-5l1 1h1 1v-1c1 0 1 0 1 1v7 34c-1 3 0 5-1 7l-2 1v36 8 5h-1 0l-1 1 1 1h-1-4v-1h-3-2c1-1 1-1 2-1h0v-1c-1 1-3 1-4 1h0-1-4v-1h0v-2h-1l1-1h0-2 0c0-1-1-2-1-3h1-4v-1h11 2c2-1 5-1 7-1h1l-2-2c1-1 1-2 1-2v-1c1 0 1 0 1-1l-1-1v-1l1-1c-1 0-1 0-1-1h0 0c1-1 0-2-1-2l1-1v-1c0-1 0-2 1-4h-1v-1-1-1-2l1-1-1-1v-2-1l1-1s-1 0-1-1v-1-1c-1-1 0-2 0-3h2v-2h-1s-2-1-2-2 1-1 1-3h0-1v-1l1-1v-1-1-3-3-1l-1-1 2-1 2 1v-3l-1-17 1-11z" class="H"></path><path d="M252 413h-1v-19 5c1-1 1-2 1-3-1-1-1-2 0-3v16h0v4z" class="k"></path><path d="M249 407l2 1-1 1v16h0-1s-2-1-2-2 1-1 1-3h0-1v-1l1-1v-1-1-3-3-1l-1-1 2-1z" class="S"></path><path d="M249 407l2 1-1 1-2 1v-1l-1-1 2-1z" class="D"></path><defs><linearGradient id="t" x1="263.912" y1="392.126" x2="242.897" y2="404.303" xlink:href="#B"><stop offset="0" stop-color="#4c4d47"></stop><stop offset="1" stop-color="#6e6c73"></stop></linearGradient></defs><path fill="url(#t)" d="M254 373v-1c1 0 1 0 1 1v7 34c-1 3 0 5-1 7l-2 1v-9-4c1-3 0-6 0-10v-22-1-3h1 1z"></path><path d="M254 373v-1c1 0 1 0 1 1v7c0-1 0-4-1-5v1c0 1-1 1-2 1v-1-3h1 1z" class="L"></path><path d="M252 373h1 1v2c-1 1-1 1-2 1v-3z" class="J"></path><path d="M250 425l1 7v27c1 0 1 0 1-1v8 5h-1 0l-1-1-1-1v-2-2l-1-1h2v-1c-2-1-7 0-9 0 2-1 5-1 7-1h1l-2-2c1-1 1-2 1-2v-1c1 0 1 0 1-1l-1-1v-1l1-1c-1 0-1 0-1-1h0 0c1-1 0-2-1-2l1-1v-1c0-1 0-2 1-4h-1v-1-1-1-2l1-1-1-1v-2-1l1-1s-1 0-1-1v-1-1c-1-1 0-2 0-3h2v-2h0z" class="F"></path><path d="M251 432v27c1 0 1 0 1-1v8 5h-1 0l-1-1 1-38z" class="j"></path><path d="M239 463h2c2 0 7-1 9 0v1h-2l1 1v2 2l1 1 1 1-1 1 1 1h-1-4v-1h-3-2c1-1 1-1 2-1h0v-1c-1 1-3 1-4 1h0-1-4v-1h0v-2h-1l1-1h0-2 0c0-1-1-2-1-3h1-4v-1h11z" class="o"></path><path d="M237 465l9-1-1 2h0c-1 0-1 0-2 1h0v-2h-4-2z" class="B"></path><path d="M232 464h1l4 1h2v6h0-1-4v-1h0v-2h-1l1-1h0-2 0c0-1-1-2-1-3h1z" class="Z"></path><path d="M234 470h1v-2h1c0 1 1 2 2 3h-4v-1z" class="N"></path><path d="M233 464l4 1h2v6h0v-3c-2-1-2-2-3-3-1 0-2 0-3-1z" class="I"></path><path d="M246 464h0 2l1 1v2 2l1 1 1 1-1 1 1 1h-1-4v-1h-3-2c1-1 1-1 2-1h0v-1c-1 1-3 1-4 1v-6h4v2h0c1-1 1-1 2-1h0l1-2z" class="X"></path><path d="M249 469l1 1 1 1-1 1 1 1h-1-4v-1h-3-2c1-1 1-1 2-1h0v-1h1v1h1c2 0 3 0 4-2z" class="C"></path><path d="M243 472h7l1 1h-1-4v-1h-3z" class="O"></path><path d="M239 465h4v2h0c1-1 1-1 2-1h0c0 1 1 3 0 5l-1-1c0-1 0-1-1-2v2c-1 1-3 1-4 1v-6z" class="d"></path><path d="M220 424s1 0 2 1h0 4 5c2 0 3 0 4 1l14-1h1v2h-2c0 1-1 2 0 3v1 1c0 1 1 1 1 1l-1 1v1 2l1 1-1 1v2 1 1 1h1c-1 2-1 3-1 4v1l-1 1c1 0 2 1 1 2h0 0c0 1 0 1 1 1l-1 1v1l1 1c0 1 0 1-1 1v1s0 1-1 2l2 2h-1c-2 0-5 0-7 1h-2-11v1h4-1-7-2c-1 0-1 0-2-1h0v-39z" class="a"></path><path d="M230 443c1 0 1-1 2-1v1c1 0 1 0 2 1h-1v1h-1v-1s-2 0-2-1h0z" class="f"></path><path d="M237 445v-1h-2-1v-2c1-1 2-1 3-1 0 1 0 2 1 3l-1 1z" class="H"></path><path d="M240 450c0-1 0 0 1-1-2 1-3 0-4 1h-2l-1-1h0c1-1 3-1 4-1v1h2 2 0l-1 1h-1z" class="U"></path><path d="M243 447h1v1h0 4v1l-1 1c1 0 2 1 1 2h0 0c0 1 0 1 1 1l-1 1v1l1 1c0 1 0 1-1 1v1-1c-1 1-2 1-3 1h0-1v-2c-1 0-2 1-3 1v-1-1h0s-1-1-2-1c0 0 0 1 0 0-2 0-3 0-4 1h-1c0-1 3-2 4-2s1 0 1-1h-1c-1 0-2 1-4 0l1-1c2 0 3 0 5-1h1l1-1h0-2-2v-1c1 0 2 0 3-1h2z" class="M"></path><path d="M243 447h1v1h0c0 1-2 0-3 0v-1h2z" class="Z"></path><path d="M238 452c1 0 2 0 3-1l1 1h0c-2 1-2 1-4 1 1 0 1 0 1-1h-1z" class="j"></path><path d="M244 455c0-1-1-1-1-2h0c0-1 1-1 2-1h3 0c0 1 0 1 1 1l-1 1c-1 0-3 0-4 1z" class="O"></path><path d="M244 455c1-1 3-1 4-1v1l1 1c0 1 0 1-1 1v1-1c-1 1-2 1-3 1h0-1v-2c-1 0-2 1-3 1v-1-1h3z" class="Y"></path><path d="M244 456h0c2 0 2-1 3 0l1 1c-1 1-2 1-3 1h0-1v-2z" class="M"></path><defs><linearGradient id="u" x1="241.779" y1="464.692" x2="236.17" y2="456.081" xlink:href="#B"><stop offset="0" stop-color="#77787a"></stop><stop offset="1" stop-color="#a19f9d"></stop></linearGradient></defs><path fill="url(#u)" d="M235 455c1-1 2-1 4-1 0 1 0 0 0 0 1 0 2 1 2 1h0v1 1c1 0 2-1 3-1v2h1 0c1 0 2 0 3-1v1s0 1-1 2l2 2h-1c-2 0-5 0-7 1h-2-11v1h4-1-7c-1 0-2 0-3-1h0l1-1h0 6 2c2-1 2-2 2-4h1v-3h2 0z"></path><path d="M242 459h2c0 1 0 1-1 2-1-1-1-1-1-2z" class="M"></path><path d="M244 458h1 0c1 0 2 0 3-1v1s0 1-1 2v-1h0-3-2l-1 1-1-1c2-1 2-1 4-1z" class="I"></path><path d="M222 462c6 0 12 0 17 1h-11v1h4-1-7c-1 0-2 0-3-1h0l1-1z" class="C"></path><path d="M235 455c1-1 2-1 4-1 0 1 0 0 0 0 1 0 2 1 2 1h0v1 1c1 0 2-1 3-1v2c-2 0-2 0-4 1v1c-1 0-3 1-4 0v-1h-1 0v-1h-2v-3h2 0z" class="U"></path><path d="M235 457v-1c2 0 3 0 4-1l1 1c-2 2-3 1-5 1z" class="V"></path><path d="M235 455c1-1 2-1 4-1 0 1 0 0 0 0 1 0 2 1 2 1h-2 0c-1 1-2 1-4 1v1 1h-2v-3h2 0z" class="H"></path><path d="M248 427c0 1-1 2 0 3v1 1c0 1 1 1 1 1l-1 1v1 2l1 1-1 1v2 1 1 1h1c-1 2-1 3-1 4h-4 0v-1h-1-2c-1 1-2 1-3 1v-1h0-3l-1-1c1-1 2-1 3-1l1-1c-1-1-1-2-1-3l2-2h-1c-1-1-1-1-1-3 0 0 1-1 2-1h1l-1-1h-1v-1-1c-1 0-2 1-3 1h-1v-1-2h-1l-2-2h-2l19-1z" class="O"></path><path d="M239 434c1-1 2-1 4-1l-1 2h-3 1l-1-1z" class="H"></path><path d="M238 428c1 0 3 0 3 1v1h-1-2v-2z" class="M"></path><path d="M231 428h4 3v2h2 1 3 2l-1 1c-1 1-2 1-3 1s-2 1-4 1v-1c-1 0-2 1-3 1h-1v-1-2h-1l-2-2z" class="U"></path><path d="M231 428h4l-2 1h0c1 0 2 0 3 1v1s-1 1-2 1v-2h-1l-2-2z" class="Q"></path><path d="M243 433h2 0c0 1-1 1-2 1-1 2 0 4 0 5-1 1-2 1-3 1h0c0 1 0 1 1 1h2c0 1 0 2-1 3h0v1h1s1 0 1-1h4 1c-1 2-1 3-1 4h-4 0v-1h-1-2c-1 1-2 1-3 1v-1h0-3l-1-1c1-1 2-1 3-1l1-1c-1-1-1-2-1-3l2-2h-1c-1-1-1-1-1-3 0 0 1-1 2-1h3l1-2z" class="U"></path><path d="M242 435l-1 3h-1-1v-3h3z" class="V"></path><path d="M243 447c-1 0-1-1-1-1-1-1-1 0-2-1h1 0-1c0-1 1-1 2-1v1h4 0c0 1-1 2-2 2h-1z" class="M"></path><path d="M242 445h1s1 0 1-1h4 1c-1 2-1 3-1 4h-4 0v-1c1 0 2-1 2-2h0-4z" class="I"></path><path d="M220 424s1 0 2 1h0 4 5c2 0 3 0 4 1l14-1h1v2h-2l-19 1h2l2 2h1v2 1h1 1c-1 1-1 1-2 1v1h2l-2 2h-2c0 2 1 3 0 5-1 0-1 1-2 1h0c0 1 2 1 2 1v1h0c-1 1-1 1-2 1v1h1c0-1 1 0 1 0v1c0 1 0 2 1 3h0c-1 1-1 1-1 2h0v2h0v3c0 2 0 3-2 4h-2-6 0l-1 1h0c1 1 2 1 3 1h-2c-1 0-1 0-2-1h0v-39z" class="h"></path><path d="M223 430h1 2v2h-3v-2z" class="m"></path><path d="M227 428c1 0 1 0 2 1h0l2 1c1 1 1 2 0 4h1c0 1 0 1-1 1h0-2-1l-1-1h1s0-1-1-1h0v-1l3-1v-1c-1 0-2-1-3-2z" class="H"></path><path d="M228 434h3v1h0-2-1l-1-1h1z" class="n"></path><path d="M229 435h2l1 1c1 0 1 0 2-1h2l-2 2h-2c0 2 1 3 0 5-1 0-1 1-2 1v-1c0-1 0-1-1-2l1-1-2-1c2 0 3-1 4-2-1 0-2 1-3 0v-1z" class="H"></path><path d="M225 428h4 2l2 2h1v2 1h1 1c-1 1-1 1-2 1v1c-1 1-1 1-2 1l-1-1h0c1 0 1 0 1-1h-1c1-2 1-3 0-4l-2-1h0c-1-1-1-1-2-1h-2z" class="f"></path><path d="M230 443c0 1 2 1 2 1v1h0c-1 1-1 1-2 1v1h1c0-1 1 0 1 0v1c-1 0-2 1-2 1 0 1 0 2-1 2v1c1 1 0 1 0 1-1 2 0 4 0 6s-1 1-1 2v1h-6v-1c1 0 3 0 4-1 0 0 1 0 1-1v-1c1 0 1 0 2-1 0 0 0-1-1-2h0c1-2 0-5 2-6l-1-1h0v-1c0-1 0-1 1-2 0 0 1 0 1-1h0-1v-1z" class="H"></path><path d="M228 462v-1c0-1 1 0 1-2s-1-4 0-6c0 0 1 0 0-1v-1c1 0 1-1 1-2 0 0 1-1 2-1 0 1 0 2 1 3h0c-1 1-1 1-1 2h0v2h0v3c0 2 0 3-2 4h-2z" class="f"></path><path d="M220 424s1 0 2 1h0 4 5c2 0 3 0 4 1l14-1h1v2h-2l-19 1h-4-3v1c1 1 1 1 0 2l-1 1 2 2-2 1h1 1c0 1-1 2-1 2 0 1 1 1 1 2 0 0-2 2-1 2 0 2 1 1 0 3v1h0l-1 1h1 1c0 1-1 2-1 3h1c0 1-1 1-1 2h1c0 1-1 1-1 2 0 0 1 0 0 1 0 1 0 1 1 2l-1 1c1 2 0 2 0 4v1h0l-1 1h0c1 1 2 1 3 1h-2c-1 0-1 0-2-1h0v-39z" class="R"></path><path d="M222 445h-2v-2h1s0 1 1 1h0v1h0z" class="L"></path><path d="M222 428h-1v1l-1 1v-4h1v1c1 1 2 0 4 1h-3z" class="P"></path><path d="M222 457c1 2 0 2 0 4v1h0l-1 1h0c1 1 2 1 3 1h-2c-1 0-1 0-2-1h0v-3l1-1-1-1 2-1z" class="B"></path><path d="M235 426l14-1h1v2h-2l-19 1h-4c-2-1-3 0-4-1v-1h14z" class="T"></path><defs><linearGradient id="v" x1="245.968" y1="382.589" x2="237.305" y2="389.482" xlink:href="#B"><stop offset="0" stop-color="#8e8c8d"></stop><stop offset="1" stop-color="#acaaab"></stop></linearGradient></defs><path fill="url(#v)" d="M240 365c1 0 3 0 3 1l1 1c2 1 7-1 8 1 2 1 4 1 6 0h1c-2 1-4 2-6 2l3 1h-1v2c0-1 0-1-1-1v1h-1-1l-1-1v5l-1 11 1 17v3l-2-1-2 1 1 1v1 3 3 1 1l-1 1v1h1 0c0 2-1 2-1 3s2 2 2 2l-14 1c-1-1-2-1-4-1h-5-4 0l1-2h-1c0-1 0-1 1-1h0c-1-1-1-1-2-1l2-2c-1 0-1 0-2-1 1 0 1 0 1-1h-1l1-1 1-1c-1-1 0-1-1-3h0v-1-4h0v-1-1h0v-1h1c-1-1-1-1-1-2h1l-1-2v-1l1-2h-1c0-1 0-1 1-1v-1c-1-1-1-1-1-2h-1v-2h-1c-1 2 1 5 0 7v-8h1l1-1v-2h-1l2-1c0-2-1-1-2-2l2-2-1-1h-1c-1 0-1 0-1-1v-5-2h0l-1-2v-1l2-1c2 1 7 0 9 0h5v-2h1l1-1v1h1v-1l2-1z"></path><path d="M242 381c1-1 1-1 3-1v2 1l-1-1-1 1 2 1-1 1h-3v-2l2-2h0-1z" class="U"></path><path d="M244 378h5l-1 1h0-1-1c1 2 1 2 0 4 0 1 0 2 1 3l-1-1v-2h0v-1l-1 1v-1-2c-2 0-2 0-3 1 0 0-1-1-2-1 1-1 3-1 4-1v-1zm-8 11h2 3 4c1-1 1-2 2-2 0 1 0 1-1 2h-1l1 1 1 1h-3-11c1-1 1-1 1-2h2z" class="M"></path><path d="M238 380h2c1 0 2 1 2 1h1 0l-2 2v2-1h-1-1v1c0 1 0 1-1 2-1 0-2 1-2 2h-2c0 1 0 1-1 2h-4c1 0 1-1 1-2h3l1-2v-2c-1-1-1-1-2-1 1-1 1-2 2-2 1-1 2 0 4 0v-2z" class="a"></path><path d="M238 387c0-1-1-1-2-2l3-3h0l1 1v1h-1v1c0 1 0 1-1 2z" class="U"></path><path d="M222 387c1 2 0 2 0 4h2l1-2 1 1h0 1l3-1c0 1 0 2-1 2h4 11 4 1 1v1 1h-6-10-4-2-6-1v-2h-1c-1 2 1 5 0 7v-8h1l1-1v-2z" class="T"></path><path d="M222 387c1 2 0 2 0 4h2l1-2 1 1h0 1l3-1c0 1 0 2-1 2h4 11 4c-3 1-6 0-9 0h-18-1c-1 2 1 5 0 7v-8h1l1-1v-2z" class="X"></path><path d="M222 391h2l1-2 1 1h0 1l3-1c0 1 0 2-1 2s-5 1-7 0z" class="H"></path><path d="M249 377h1 1l-1 11 1 17h-2v-1c-2-1 0-2-1-4 0 0 0-1-1-2l1-1v-4h-4 6v-1-1h-1-1-4 3l-1-1-1-1h1c1-1 1-1 1-2v-1c-1-1-1-2-1-3 1-2 1-2 0-4h1 1 0l1-1v-1z" class="K"></path><path d="M247 386c-1-1-1-2-1-3 1-2 1-2 0-4h1 1c-1 1-1 2-1 3h1s0 1 1 1c-1 1-2 2-1 3h1c0 1-1 1-1 2v1h-1l1 1c0 1 0 1 1 1h-1-4 3l-1-1-1-1h1c1-1 1-1 1-2v-1z" class="I"></path><defs><linearGradient id="w" x1="236.997" y1="379.058" x2="223.146" y2="381.613" xlink:href="#B"><stop offset="0" stop-color="#bab9b9"></stop><stop offset="1" stop-color="#dcdadc"></stop></linearGradient></defs><path fill="url(#w)" d="M236 378h2 1c0 1-1 1-1 2v2c-2 0-3-1-4 0-1 0-1 1-2 2 1 0 1 0 2 1v2l-1 2h-3l-3 1h-1 0l-1-1-1 2h-2c0-2 1-2 0-4h-1l2-1c0-2-1-1-2-2l2-2-1-1v-3h13 1z"></path><path d="M231 385l2 1v1l-1 1c-1 0-1 0-2-1l1-2z" class="N"></path><path d="M236 378h2 1c0 1-1 1-1 2h-2c-1 0-1 0-2-1h1v-1h1z" class="Q"></path><path d="M223 382l2-2h1c1 0 1 1 1 1l1-1h2v1l-2 2h0-1c0-1 1-1 0-2s-1 1-1 1c-1 0-1 1-2 1l-1-1z" class="l"></path><path d="M223 382l1 1c1 0 1-1 2-1 0 0 0-2 1-1s0 1 0 2h1 0c1 0 3 1 4 1v1h-1l-1 2c0 1 0 0-1 1l1 1c-1 0-2 0-3 1h-1 0l-1-1-1 2h-2c0-2 1-2 0-4h-1l2-1c0-2-1-1-2-2l2-2h0z" class="n"></path><path d="M244 393h4v4l-1 1c1 1 1 2 1 2 1 2-1 3 1 4v1h2v3l-2-1-2 1c1 0 0-1 0-1h-3-5c0 1-1 1-2 1-1-1-1-1-2 0 0-1 1-1 1-2 1 0 1-1 1-1v-1c-1 1-2 1-3 1v-1c1-1 1-1 2-1v-1h-1c0-3 0-5-1-8-1 0-3 0-4-1h4 10z" class="M"></path><path d="M245 402c1 0 1 0 2-1v-2-1c1 1 1 2 1 2 1 2-1 3 1 4v1h2v3l-2-1-2 1c1 0 0-1 0-1h-3l-1-1c1-1 3 0 4-2l-1-1h-2-1l2-1z" class="I"></path><path d="M249 405h2v3l-2-1-1-1h0l1-1z" class="F"></path><path d="M244 393h4v4l-1 1v1 2c-1 1-1 1-2 1-1-1-1-2-1-3 0 0 1-1 1-2l-2-2c0-1 0-1-1-1h-3c-2 0-3 0-5-1h10z" class="Z"></path><path d="M230 393h4c2 1 3 1 5 1h1s1 1 2 1c1 1 1 3 1 5h-1v1 1c-1 1-1 0-2 1h1 0l2 1v1c-1 1-3 0-4 1v1c0 1-1 1-2 1-1-1-1-1-2 0 0-1 1-1 1-2 1 0 1-1 1-1v-1c-1 1-2 1-3 1v-1c1-1 1-1 2-1v-1h-1c0-3 0-5-1-8-1 0-3 0-4-1z" class="Q"></path><path d="M240 365c1 0 3 0 3 1l1 1c2 1 7-1 8 1 2 1 4 1 6 0h1c-2 1-4 2-6 2l3 1h-1v2c0-1 0-1-1-1v1h-1-1l-1-1v5h-1-1v1h-5v1c-1 0-3 0-4 1h-2c0-1 1-1 1-2h-1-2-1-13v3h-1c-1 0-1 0-1-1v-5-2h0l-1-2v-1l2-1c2 1 7 0 9 0h5v-2h1l1-1v1h1v-1l2-1z" class="B"></path><path d="M252 370h1l3 1h-1v2c0-1 0-1-1-1v1h-1-1l-1-1v5h-1-1v-1h-4l4-1v-1c0-1-1-2-3-3 2 0 3 1 4 2 1-1 1-2 2-3z" class="W"></path><path d="M235 369h3-1v1c2 0 5-1 7 1h-21v-1c-1 0-2 0-2-1 2 1 7 0 9 0h5z" class="G"></path><path d="M240 365c1 0 3 0 3 1l1 1c2 1 7-1 8 1 2 1 4 1 6 0h1c-2 1-4 2-6 2h-1c-1 1-1 2-2 3-1-1-2-2-4-2h-2c-2-2-5-1-7-1v-1h1-3v-2h1l1-1v1h1v-1l2-1z" class="j"></path><path d="M240 365c1 0 3 0 3 1l1 1h-3-3v-1l2-1z" class="h"></path><path d="M238 369c4 0 9 0 14 1-1 1-1 2-2 3-1-1-2-2-4-2h-2c-2-2-5-1-7-1v-1h1z" class="T"></path><defs><linearGradient id="x" x1="242.668" y1="372.424" x2="236.392" y2="376.175" xlink:href="#B"><stop offset="0" stop-color="#555455"></stop><stop offset="1" stop-color="#767375"></stop></linearGradient></defs><path fill="url(#x)" d="M230 372c3-1 6-1 9 0 1 1 4 0 4 0 1 0 2 2 3 3l-6 1h-8-5-4l-1-1c1-1 1-2 1-3 2-1 5 0 7 0z"></path><path d="M222 375c1-1 1-2 1-3 2-1 5 0 7 0l1 2c2 0 2 0 3 1 0 0-1 0-2 1h-5-4l-1-1z" class="O"></path><path d="M226 373c2 0 3 0 4 1l1 1h-2-6 0c1-2 2-2 3-2z" class="a"></path><path d="M219 370l2-1c0 1 1 1 2 1v1h-1v4l1 1h4 5 8 5 4v1 1h-5v1c-1 0-3 0-4 1h-2c0-1 1-1 1-2h-1-2-1-13v3h-1c-1 0-1 0-1-1v-5-2h0l-1-2v-1z" class="g"></path><path d="M238 378h6v1c-1 0-3 0-4 1h-2c0-1 1-1 1-2h-1z" class="V"></path><path d="M219 370l2-1c0 1 1 1 2 1v1h-1v4l1 1h4-4l-1 1c2 1 3 0 5 0 3 0 6 0 9 1h-1-13v3h-1c-1 0-1 0-1-1v-5-2h0l-1-2v-1z" class="G"></path><path d="M219 370l2-1c0 1 1 1 2 1v1h-1c-1 1-1 2-2 2l-1-2v-1z" class="C"></path><path d="M220 373h0v5h2v3h-1c-1 0-1 0-1-1v-5-2z" class="B"></path><path d="M222 393h6 2c1 1 3 1 4 1 1 3 1 5 1 8h1v1c-1 0-1 0-2 1v1c1 0 2 0 3-1v1s0 1-1 1c0 1-1 1-1 2 1-1 1-1 2 0 1 0 2 0 2-1h5 3s1 1 0 1l1 1v1 3 3 1 1l-1 1v1h1 0c0 2-1 2-1 3s2 2 2 2l-14 1c-1-1-2-1-4-1h-5-4 0l1-2h-1c0-1 0-1 1-1h0c-1-1-1-1-2-1l2-2c-1 0-1 0-2-1 1 0 1 0 1-1h-1l1-1 1-1c-1-1 0-1-1-3h0v-1-4h0v-1-1h0v-1h1c-1-1-1-1-1-2h1l-1-2v-1l1-2h-1c0-1 0-1 1-1v-1c-1-1-1-1-1-2z" class="H"></path><path d="M235 408h-3 0v-1c1 0 3-1 4-1 0 1-1 1-1 2h0z" class="f"></path><path d="M222 393h6c1 1 2 1 3 2v2h0-1c0 1 0 1 1 2h0l-1 1h0 3v1c-1 0-2 1-2 1l-1-1h-3v-1c1 0 2 0 2-1v-5h-1-2-3v1c-1-1-1-1-1-2z" class="h"></path><g class="n"><path d="M222 405c2 1 3 1 4 1 1 1 1 0 2 0h5c-3 1-8 4-11 3v-3-1z"></path><path d="M228 394h1v5c0 1-1 1-2 1v1h3l1 1-1 1h2v1h-2c-2 1-2 2-4 2 0-1 0-3-1-4h0v-1c2-1 1-3 2-4v-1c1-1 1-1 1-2z"></path></g><path d="M226 406c0-1 0-3-1-4h0v-1l1 1v2h4c-2 1-2 2-4 2z" class="m"></path><path d="M223 395v-1h3 2c0 1 0 1-1 2v1c-1 1 0 3-2 4v1h0c1 1 1 3 1 4h0c-1 0-2 0-4-1h0v-1h1c-1-1-1-1-1-2h1l-1-2v-1l1-2h-1c0-1 0-1 1-1v-1z" class="p"></path><path d="M226 394h2c0 1 0 1-1 2 0-1-1-1-1-2z" class="m"></path><path d="M228 409h4c-1 1-2 1-2 2v4 2 1 1c0 1 0 1-1 2l2 2-2 2h-3-4 0l1-2h-1c0-1 0-1 1-1h0c-1-1-1-1-2-1l2-2c-1 0-1 0-2-1 1 0 1 0 1-1h-1l1-1 1-1c-1-1 0-1-1-3h0v-1c1-1 4-1 6-2z" class="n"></path><path d="M223 423v1c2 0 3 0 5-1 2 0 1-1 1-2l2 2-2 2h-3-4 0l1-2z" class="h"></path><path d="M222 411c1-1 4-1 6-2l1 1c0 1 0 1-1 2h-2c-1 0-1 2-4 0h0v-1z" class="p"></path><defs><linearGradient id="y" x1="246.007" y1="416.086" x2="229.938" y2="415.951" xlink:href="#B"><stop offset="0" stop-color="#868485"></stop><stop offset="1" stop-color="#bebdbd"></stop></linearGradient></defs><path fill="url(#y)" d="M244 407h3s1 1 0 1l1 1v1 3 3 1 1l-1 1v1h1 0c0 2-1 2-1 3s2 2 2 2l-14 1c-1-1-2-1-4-1h-5 3l2-2-2-2c1-1 1-1 1-2v-1c2 0 3-1 4-2v-1c-1 0-1 1-2 0 0-1 2-1 3-2l-1-1-2-1v-1c1-1 2 0 3-1v-1h0c1-1 1-1 2 0 1 0 2 0 2-1h5z"></path><path d="M240 417l1-1 3-1v2c-2-1-3 0-4 0z" class="M"></path><path d="M238 409h3l-1 1h3 0c-1 1-1 1-2 1h0c-1-1-2-1-3-2z" class="U"></path><path d="M240 412l-3-2v-1h1c1 1 2 1 3 2h0s-1 0-1 1z" class="Q"></path><path d="M240 417c1 0 2-1 4 0l1 1h0v1c-1 0-1 0-2 1h-1v-1l-1 1h0v-1l-1-2z" class="V"></path><path d="M244 415h1l-1-1c0-1 0-2 1-3 0 2 1 2 1 4-1 1-1 1-2 1 1 1 3 1 4 1v1l-1 1v1s-1-1-2 0h-1-1c1-1 1-1 2-1v-1h0l-1-1v-2z" class="Z"></path><path d="M248 409v1 3 3 1c-1 0-3 0-4-1 1 0 1 0 2-1 0-2-1-2-1-4h1l2-2z" class="I"></path><defs><linearGradient id="z" x1="245.486" y1="409.228" x2="232.48" y2="410.263" xlink:href="#B"><stop offset="0" stop-color="#939293"></stop><stop offset="1" stop-color="#b9b7b8"></stop></linearGradient></defs><path fill="url(#z)" d="M244 407h3s1 1 0 1h-3c-1 1-2 1-3 1h-3-1v1l3 2c-2 1-2 1-4 1h0c0-1 0 0-1-1v-1 2l-1-1-2-1v-1c1-1 2 0 3-1v-1h0c1-1 1-1 2 0 1 0 2 0 2-1h5z"></path><defs><linearGradient id="AA" x1="245.079" y1="426.089" x2="235.268" y2="419.437" xlink:href="#B"><stop offset="0" stop-color="#858486"></stop><stop offset="1" stop-color="#a09f9e"></stop></linearGradient></defs><path fill="url(#AA)" d="M241 419v1h0l1-1v1h1 1 1c1-1 2 0 2 0h1 0c0 2-1 2-1 3s2 2 2 2l-14 1c-1-1-2-1-4-1v-1c1-1 2-1 3-1s2 0 2-1c1 0 2-1 3-1h1c1-1 1-2 1-2z"></path><path d="M242 420h1 1v2h-1l-1-1v-1z" class="U"></path><path d="M216 376h1l1-4 1-1 1 2h0v2 5c0 1 0 1 1 1h1l1 1-2 2c1 1 2 0 2 2l-2 1h1v2l-1 1h-1v8c1-2-1-5 0-7h1v2h1c0 1 0 1 1 2v1c-1 0-1 0-1 1h1l-1 2v1l1 2h-1c0 1 0 1 1 2h-1v1h0v1 1h0v4 1h0c1 2 0 2 1 3l-1 1-1 1h1c0 1 0 1-1 1 1 1 1 1 2 1l-2 2c1 0 1 0 2 1h0c-1 0-1 0-1 1h1l-1 2c-1-1-2-1-2-1v39h0c1 1 1 1 2 1v2 1h1v1c-1 0-1 0-1 1l-1-2h-1v3 1h-2-1v-5l-1 1v3 1h-1v-3-3l-1-1-2-1c-2 0-6-1-7-2-7-2-13-5-17-11l-1 4h0-1l1-8h0c0-2 0-3 1-4v-2c-1-2-1-4-1-6v-11-5l1-2c1-4 3-7 5-11 2-2 3-4 5-5 2-2 5-3 7-5h2l3-2v-1c2-1 2-1 4-1l1-1v-7l-1-1 1-2v-8 1l1 3z" class="X"></path><path d="M212 457h3v3h-1c-2-1-2-2-2-3z" class="J"></path><path d="M210 457c1 1 1 1 2 1 0 2 1 3 0 4l-3-1h2 0 1-1c-1-2-1-2-1-4z" class="c"></path><path d="M206 454c1 1 3 2 4 3 0 2 0 2 1 4l-1-1h0c-2-2-3-4-4-6z" class="e"></path><path d="M197 444c-1-3-3-6-3-9 0-2-1-4-1-6s1-4 1-6c0 1 0 2 1 3l-1 1v3c1 1 1 1 1 3h-1c0 1 1 1 1 1 0 2 1 2 1 3 1 2 1 3 2 5 0 1 0 1-1 2z" class="I"></path><path d="M197 413c0-2 2-3 3-4v-1c0-1 1-1 2-2l2 1c-1 2-1 3-1 4l-2 4-1-2h-1-2z" class="c"></path><defs><linearGradient id="AB" x1="212.305" y1="459.297" x2="200.706" y2="453.459" xlink:href="#B"><stop offset="0" stop-color="#636162"></stop><stop offset="1" stop-color="#7f7e7f"></stop></linearGradient></defs><path fill="url(#AB)" d="M200 449l6 5c1 2 2 4 4 6h0l1 1h1-1 0-2c-2-1-6-4-8-5 0-2-1-4-3-5 1 0 2 1 2 1l1 1v-2l-1-1v-1z"></path><path d="M209 447h1 2 1v1h0c0 2 1 4 0 5l-1 1v1 1h0-1c-2-1-3-2-4-2l-2-2c-2-1-3-3-5-4l2-1v1s2 1 3 1l1-1 2-1h1z" class="c"></path><path d="M212 455l-2-1v-1h2 0v-2h1l-1-1 1-2c0 2 1 4 0 5l-1 1v1z" class="X"></path><path d="M206 448l2-1 2 1c0 1 0 2-1 3s-2 1-2 3l-2-2c1 0 1 0 1-1h0c1 0 2-2 3-2v-1h-3z" class="d"></path><path d="M200 448l2-1v1s2 1 3 1l1-1h3v1c-1 0-2 2-3 2h0c0 1 0 1-1 1-2-1-3-3-5-4zm-13-25v-5l1 3s1 0 1 1v1h0c1 1 0 2 0 3 1 2 1 3 2 4l1-1v5c1 4 2 7 4 10 0 1 3 4 4 5v1l1 1v2l-1-1s-1-1-2-1c-1-1-2-3-3-4v3c-3-3-5-6-7-10-1-2-1-4-1-6v-11z" class="I"></path><path d="M191 430l1-1v5 1h-1v-5z" class="O"></path><path d="M187 423v-5l1 3s1 0 1 1v1 4 7c1 4 2 7 4 10 0 1 1 2 2 3v3c-3-3-5-6-7-10-1-2-1-4-1-6v-11z" class="i"></path><path d="M188 440c2 4 4 7 7 10v-3c1 1 2 3 3 4 2 1 3 3 3 5 2 1 6 4 8 5l3 1v1c-2 0-6-1-7-2-7-2-13-5-17-11l-1 4h0-1l1-8h0c0-2 0-3 1-4v-2z" class="D"></path><path d="M195 447c1 1 2 3 3 4 2 1 3 3 3 5l-6-6v-3z" class="N"></path><path d="M188 442l3 6c3 5 9 11 14 13h0 0c-7-2-13-5-17-11l-1 4h0-1l1-8h0c0-2 0-3 1-4z" class="i"></path><path d="M188 442l3 6c-1 0-1 0-1-1h-1v1h-1v-2h-1 0c0-2 0-3 1-4z" class="P"></path><path d="M215 390h1 1v1h-1-1c0 1 0 1-1 2 1 2 1 4 1 6h-1c-2 1-6 3-7 5-1-1-2-1-4-1h0c-2 2-3 4-5 6-4 5-6 14-6 20l-1 1c-1-1-1-2-2-4 0-1 1-2 0-3h0v-1c0-1-1-1-1-1l-1-3 1-2c1-4 3-7 5-11 2-2 3-4 5-5 2-2 5-3 7-5h2l3-2v-1c2-1 2-1 4-1l1-1z" class="M"></path><path d="M188 416h1c1-1 1-2 1-4h1c-1 3-2 8-2 11h0v-1c0-1-1-1-1-1l-1-3 1-2z" class="N"></path><path d="M206 397c0 1 0 2-1 3h-1c-2 1-3 3-5 5v-1l2-2c-1-1-1 0-2 1 0 0-1 0-1 1-1 1-3 4-4 5l2-4 3-3c2-2 5-3 7-5z" class="U"></path><path d="M210 393c0 1-3 2-3 3l-1 1c-2 2-5 3-7 5l-3 3h-1l4-4-1-1c2-2 5-3 7-5h2l3-2z" class="I"></path><path d="M198 400l1 1-4 4h1l-2 4c-1 0-2 1-2 2s-1 1-1 1h-1c0 2 0 3-1 4h-1c1-4 3-7 5-11 2-2 3-4 5-5z" class="O"></path><path d="M215 390h1 1v1h-1-1c0 1 0 1-1 2 1 2 1 4 1 6h-1c-2 1-6 3-7 5-1-1-2-1-4-1h0c0-1 1-1 1-2h0v-1h1c1-1 1-2 1-3l1-1c0-1 3-2 3-3v-1c2-1 2-1 4-1l1-1z" class="Z"></path><path d="M215 390h1 1v1h-1-1c0 1 0 1-1 2h-1l-6 3c0-1 3-2 3-3v-1c2-1 2-1 4-1l1-1z" class="E"></path><path d="M213 393h1c1 2 1 4 1 6h-1c-2 1-6 3-7 5-1-1-2-1-4-1 2-2 5-4 7-5s3-2 4-3l-1-2z" class="B"></path><path d="M197 413h2 1l1 2c-1 4-1 8 0 13h0c1 2 2 5 3 6 1 2 2 3 3 4l4 3 2 2v3h-2l1 1h-2-1-1l-2 1-1 1c-1 0-3-1-3-1v-1l-2 1-1-1-2-3h0c1-1 1-1 1-2-1-2-1-3-2-5 0-1-1-1-1-3 0 0-1 0-1-1h1c0-2 0-2-1-3v-3l1-1c-1-1-1-2-1-3v-2h2l-1-1h-1l3-7z" class="O"></path><path d="M198 442v-2c1 0 1 1 2 1v2h1s1-1 2-1v1l-2 1v1h0c-1 0-1 1-2 2l-2-3h0c1-1 1-1 1-2z" class="V"></path><path d="M201 444v1h0c-1 0-1 1-2 2l-2-3h2v1c1-1 1-1 2-1z" class="I"></path><path d="M201 444l2-1c0 1 0 1 2 2h2 0l-2 1v1c2-1 3-1 4 0h-1l-2 1-1 1c-1 0-3-1-3-1v-1l-2 1-1-1c1-1 1-2 2-2h0v-1z" class="M"></path><path d="M207 438l4 3 2 2v3h-2l1 1h-2-1c-1-1-2-1-4 0v-1l2-1c1 0 1 0 1-1h-1v-1l2-1 1-1h0l-1-1-2 1h0v-3z" class="R"></path><path d="M207 445c1 0 1 0 1-1h-1v-1l2-1v1h2v3c-1 0-1 0-2 1-1-1-2-1-4 0v-1l2-1z" class="b"></path><path d="M196 429l1-1c0-1 0-2 1-3 0 0 1 1 1 2v2h-1c0 1 0 3 1 4l-1 1h1l1 1h2v1l-1 1-1-1v1l-1 1c-1 0-1 0-2-1h-1c0-1-1-1-1-3 0 0 1 0 1-1 1-1 0-3 0-4z" class="I"></path><path d="M195 434s1 0 1-1c0 1 1 1 1 2 0 0-1 0-1 1 1 0 1 1 1 1h-1c0-1-1-1-1-3z" class="M"></path><path d="M197 413h2 1l1 2c-1 4-1 8 0 13h0c1 2 2 5 3 6 1 2 2 3 3 4v3h0l2-1 1 1h0l-1 1h-1-2c1-1 1-1 0-2h-2v-2-1c0-1-1-1-1-1-1-1-1-1-1-2l-2-2c1-1 1-1 1-2l-2-1v-2c0-1-1-2-1-2-1 1-1 2-1 3l-1 1c0 1 1 3 0 4 0 1-1 1-1 1s-1 0-1-1h1c0-2 0-2-1-3v-3l1-1c-1-1-1-2-1-3v-2h2l-1-1h-1l3-7z" class="N"></path><path d="M198 415v-1l1-1h1c0 1-1 2-1 3l-1-1z" class="I"></path><path d="M195 420v-1h2v1h0c-1 2 0 2 0 4-1 1-1 3-1 5 0 1 1 3 0 4 0 1-1 1-1 1s-1 0-1-1h1c0-2 0-2-1-3v-3l1-1c-1-1-1-2-1-3v-2h2l-1-1z" class="O"></path><path d="M207 404c1-2 5-4 7-5h1v2 6h0v18h0v1 4 14-1h-2l-2-2-4-3c-1-1-2-2-3-4-1-1-2-4-3-6h0c-1-5-1-9 0-13l2-4v-2c1-2 3-3 4-5z" class="T"></path><path d="M212 428l3-2v4 3c-1-1-1-1-2-1s-1 0-2-1v-1c0-1 0-1 1-2z" class="D"></path><path d="M212 428c1 1 1 2 1 3h-1l-1-1c0-1 0-1 1-2z" class="B"></path><path d="M214 407h1 0l-1 3h0c-2 0-3 1-4 2s-1 2-2 2v-3c1-1 1-1 2-1v-1c1-2 2-2 4-2z" class="C"></path><path d="M214 407c-1 1 0 1-1 2-1 0-2 1-3 1v-1c1-2 2-2 4-2z" class="K"></path><path d="M203 424h0c-1-3 0-6 0-9 1 7 2 13 5 19v-1c-3-1-4-6-5-9z" class="J"></path><path d="M203 428c-1-2-1-2 0-4 1 3 2 8 5 9v1c2 1 4 3 6 5l1-6v-3 14-1h-2l-2-2-4-3c-1-1-2-2-3-4-1-1-2-4-3-6h2z" class="i"></path><path d="M211 439c1 1 2 2 3 2l1 2h-2l-2-2v-2z" class="C"></path><path d="M203 428c0 3 3 7 6 9 0 1 2 2 2 2v2l-4-3c-1-1-2-2-3-4-1-1-2-4-3-6h2z" class="W"></path><defs><linearGradient id="AC" x1="211.821" y1="399.07" x2="199.226" y2="423.616" xlink:href="#B"><stop offset="0" stop-color="#292928"></stop><stop offset="1" stop-color="#494849"></stop></linearGradient></defs><path fill="url(#AC)" d="M207 404c1-2 5-4 7-5h1v2c-3 1-6 4-7 6-1 1-2 3-3 5-1 1-1 2-2 3 0 3-1 6 0 9h0c-1 2-1 2 0 4h-2 0c-1-5-1-9 0-13l2-4v-2c1-2 3-3 4-5z"></path><path d="M215 407v18h0s-1 0-1-1c-2 1-4 2-5 4h0l-1 1c-1-1-1-3-1-4l-1-1c0-2 0-3-1-5 0 0 1-1 1-2s1-2 2-3c1 0 1-1 2-2s2-2 4-2h0l1-3z" class="E"></path><path d="M208 424l1-1 1 1 1-1v1l-2 2h-1c0-1-1-1-1-1l-1-1h2z" class="C"></path><path d="M205 419c1 0 2 0 3 1h1c-1 1-2 1-2 2h0c1 1 1 1 1 2h-2c0-2 0-3-1-5z" class="B"></path><path d="M206 417c0 1 1 1 2 2h4 0l-1 2-2-1h-1c-1-1-2-1-3-1 0 0 1-1 1-2z" class="J"></path><path d="M210 412c2 1 3 1 4 1l-2 2c-1 0-1 1-2 1l-1 1c2 1 3 1 4 2v1l-1-1h0-4c-1-1-2-1-2-2s1-2 2-3c1 0 1-1 2-2z" class="B"></path><path d="M209 417h-1v-1-1c1 0 1 1 2 1l-1 1z" class="G"></path><path d="M216 376h1l1-4 1-1 1 2h0v2 5c0 1 0 1 1 1h1l1 1-2 2c1 1 2 0 2 2l-2 1h1v2l-1 1h-1v8c1-2-1-5 0-7h1v2h1c0 1 0 1 1 2v1c-1 0-1 0-1 1h1l-1 2v1l1 2h-1c0 1 0 1 1 2h-1v1h0v1 1h0v4 1h0c1 2 0 2 1 3l-1 1-1 1h1c0 1 0 1-1 1 1 1 1 1 2 1l-2 2c1 0 1 0 2 1h0c-1 0-1 0-1 1h1l-1 2c-1-1-2-1-2-1v39h0c1 1 1 1 2 1v2 1h1v1c-1 0-1 0-1 1l-1-2h-1v3 1h-2-1v-5l-1 1v3 1h-1v-3-3-5-3h-3v-1h0v-1-1l1-1c1-1 0-3 0-5h0v-1h-1l-1-1h2v-3h2v1-14-4-1h0v-18h0v-6-2c0-2 0-4-1-6 1-1 1-1 1-2h1 1v-1h-1-1v-7l-1-1 1-2v-8 1l1 3z" class="H"></path><path d="M216 445v-15c0-2 1-2 2-3v14c-1-1-1-2-1-2-1 2 0 4-1 6z" class="V"></path><path d="M215 443v1 13h-3v-1h0v-1-1l1-1c1-1 0-3 0-5h0v-1h-1l-1-1h2v-3h2z" class="B"></path><path d="M216 445c1-2 0-4 1-6 0 0 0 1 1 2l-1 23c0 2 0 6 1 7h-1v-5l-1 1v3-5c0-2-1-6 0-7v-3c0-3-1-7 0-10z" class="M"></path><path d="M216 455v-2h1c0 4 1 9 0 13l-1 1v3-5c0-2-1-6 0-7v-3z" class="O"></path><path d="M214 393c1-1 1-1 1-2h1v9 24s-1 0-1 1v-18h0v-6-2c0-2 0-4-1-6z" class="a"></path><path d="M216 376h1l1-4 1-1 1 2h0v2c-1 0-2 0-2 1-1 1 0 2 0 3-1 4 0 8 0 11l-1 10h-1v-9h1v-1h-1-1v-7l-1-1 1-2v-8 1l1 3z" class="U"></path><path d="M215 372v1l1 3c-1 3 0 8 0 12l1 1-1 1h-1v-7l-1-1 1-2v-8z" class="a"></path><path d="M218 390c0-3-1-7 0-11 0-1-1-2 0-3 0-1 1-1 2-1v5c0 1 0 1 1 1h1l1 1-2 2c1 1 2 0 2 2l-2 1h1v2l-1 1h-1v8c1-2-1-5 0-7h1v2h1c0 1 0 1 1 2v1c-1 0-1 0-1 1h1l-1 2v1l1 2h-1c0 1 0 1 1 2h-1v1h0v1 1h0v4 1h0c1 2 0 2 1 3l-1 1-1 1h1c0 1 0 1-1 1 1 1 1 1 2 1l-2 2c1 0 1 0 2 1h0c-1 0-1 0-1 1h1l-1 2c-1-1-2-1-2-1v39h0c1 1 1 1 2 1v2 1h1v1c-1 0-1 0-1 1l-1-2h-1v3 1h-2c-1-1-1-5-1-7l1-23v-14-8-17-12z" class="k"></path><path d="M220 463c1 1 1 1 2 1v2 1h1v1c-1 0-1 0-1 1l-1-2h-1v3c-1-1 0-5 0-7z" class="P"></path><path d="M220 380c0 1 0 1 1 1h1l1 1-2 2c1 1 2 0 2 2l-2 1h1v2l-1 1h-1v-10z" class="L"></path><path d="M220 398c1-2-1-5 0-7h1v2h1c0 1 0 1 1 2v1c-1 0-1 0-1 1h1l-1 2v1l1 2h-1c0 1 0 1 1 2h-1v1h0v1 1h0v4 1h0c1 2 0 2 1 3l-1 1-1 1h1c0 1 0 1-1 1 1 1 1 1 2 1l-2 2c1 0 1 0 2 1h0c-1 0-1 0-1 1h1l-1 2c-1-1-2-1-2-1v-26z" class="P"></path><path d="M222 399s-1 0-2 1v-3h2 1l-1 2z" class="B"></path><path d="M198 130h1l3 3c1 0 1-1 2 0-1 1 0 1 0 2l2 2c1 2 3 3 4 5 1 1 1 2 2 3h1c0 2 0 6-1 8v2h2v1c1 1 2 1 4 1 1 0 2 1 3 1 0 0 1 1 1 0 2 0 3 1 5 1h2c0-1 1-1 1-3v-3c1-2 1-5 1-8 1 1 1 2 1 3v-10h6l1 19 1 3c1 1 3 1 5 1h5 6c1-1 2-1 3-2l-1-12c0-1 0-1-1-1l-3-2h-2c2 0 4 1 6 1v-5-4h2c2 1 5 2 7 3 3 2 4 4 5 7v3l1 15v3h0c-1 3 0 7 0 10v18c0 5-1 10 0 14h3 4 2c-1 1-1 1-2 1v6 15 17h0 0c-2 1-5 1-7 1l-1 11 1 50-1 2 2 2c1 0 2 0 3 1-2 1-4 0-6 0-3 6-6 10-10 15-1 1-4 3-5 4h-2 0v-2c-1 0-2 1-3 1v-1h-1c-1 0-3-1-4-1s-2 0-3-1v1c-1-1-2-1-4-1v1s0 1 1 1v2h-1c-1 4 0 9-1 13v4 3 1l-1 6-1 4-1 2-3-5c-1-1-1-2-2-3v-2h-3l-1-1h-3l-1-1c-1-3-1-7-2-10v-2h-2-4c-1 2-4 5-6 5l-1 1h0l-1 1c-1-1-3-1-5-1-1 0-3-1-4-1-1-2-3-2-4-3-2-2-4-2-5-3v-1c-1-1-2-1-3-3h1l2-2h0l1-1-1-2-1-3h0c-1-1 0-2-1-3v-3-5h-2l2-1v-9l-1-38 2-1c1-2 2-5 4-6v-1h1c4-5 6-11 7-17l1-2c0-2 0-4-1-6l-1-4c-2-4-5-7-8-10h3c1 0 2 1 3 1l1-1-3-1-2-1-2-1-2-2-2 1v2h0l-1-1c0-1-1-1-2-1h0c1-1 2-1 2-1v-22l1-12-1-3v-5l-2-1s1 0 2-1l-1-8-2-8c0-2 0-3 1-4h1c1 0 1 1 1 1-1 0-1 0-2 1v1h1v-1c2 0 3 2 4 3h1v-1l1-1 1 1h3 0v1h2c1-1 2-1 3-1h0l2-4h0c0-1 0-2 1-4v-1c1-1 1-4 2-5v-1-3c0-1 0-1-1-1 1-1 1-2 1-3h-1-1-1l-2-2 2-2-3-3z" class="T"></path><path d="M230 272v3h-5c1 0 2 0 2-1 1 0 1-1 2-1v-1h1z" class="J"></path><path d="M204 234c1 0 1 1 1 2 0 0 0 1-1 1l-1 1h-1l-1-1 3-3z" class="R"></path><path d="M204 233c1 0 1-1 2-1v-2 8h-3l1-1c1 0 1-1 1-1 0-1 0-2-1-2v-1z" class="P"></path><path d="M198 263v1c0 2-2 3-3 5l-2 2c0-1 1-2 1-3 1 0 0 0 0-1l-4 3c0-1 2-4 3-4 2 0 3-1 5-3z" class="G"></path><path d="M194 334l-4-4c-1-2-2-5-2-7 1 1 2 3 2 4 1 2 3 4 5 6h1l-2 1z" class="B"></path><path d="M229 209l1 1c1 1 0 4 0 6h-1-2v-2c1-1 1-3 1-4l1-1z" class="b"></path><path d="M229 209l1 1h-1c0 2-1 5 0 6h-2v-2c1-1 1-3 1-4l1-1z" class="X"></path><path d="M196 333c2 1 4 3 7 4v1l-2 1v1l-3-3c-1-1-3-2-4-2v-1l2-1z" class="S"></path><path d="M198 263c1-1 1-1 3-1 0 0 0 1 1 2-4 3-7 6-9 10l-1 1c0-1 1-3 1-4l2-2c1-2 3-3 3-5v-1z" class="B"></path><path d="M231 145c1 1 1 2 1 3v10c0 1 0 2-1 3l-4-2h2c0-1 1-1 1-3v-3c1-2 1-5 1-8z" class="f"></path><path d="M203 293v7s1 0 1 1c1 1 1 2 2 4 0-1-2-3-2-4 1-1 0-2 0-3h1c0 2 0 3 1 5 0 1 1 2 1 3l2 2h-1l-3-3s0-1-1-1 0 1-1 2c-1-1-1-3-2-5 0-2 1-5 2-8z" class="F"></path><path d="M203 288c2-2 3-5 6-6-1 3-3 5-5 7 0 1-1 3-1 4-1 3-2 6-2 8l-1-7 3-6z" class="L"></path><path d="M188 333c2 2 5 4 8 5-2-2-5-4-6-6l4 3c1 0 3 1 4 2l3 3 1 1h-2c-1-1-1-1-2-1-4-1-7-2-10-4h0l1-1-1-2z" class="X"></path><path d="M199 238c1 0 1 0 2 1l1 1c1 3 0 5-1 8l-3 9c-2 3-4 7-7 7h0v-1h1c4-5 6-11 7-17l1-2c0-2 0-4-1-6z" class="G"></path><path d="M202 258l3-3v2c2 0 4 0 5 1 3 0 5 0 8 1h-4-3 0c-1 1-3 1-4 2l-5 3c-1-1-1-2-1-2-2 0-2 0-3 1-2 2-3 3-5 3 2-3 5-5 8-7l1-1z" class="R"></path><path d="M202 258h4 0c-1 1-3 3-5 3v-2l1-1z" class="N"></path><path d="M186 338l2-2c3 2 6 3 10 4 1 1 1 2 2 3h-2v1h-2 1c0 1 0 1-1 1h-3c-2-2-4-2-5-3v-1c-1-1-2-1-3-3h1z" class="C"></path><path d="M188 341c3 1 6 2 10 2v1h-2 1c0 1 0 1-1 1h-3c-2-2-4-2-5-3v-1z" class="f"></path><defs><linearGradient id="AD" x1="220.295" y1="280.264" x2="214.678" y2="274.343" xlink:href="#B"><stop offset="0" stop-color="#3d3c3d"></stop><stop offset="1" stop-color="#565555"></stop></linearGradient></defs><path fill="url(#AD)" d="M218 271c3-1 7-1 10 0l1 1v1c-1 0-1 1-2 1 0 1-1 1-2 1-6 1-11 3-16 7-3 1-4 4-6 6 3-7 8-11 15-14 1-1 2-1 3-2v-1h-3z"></path><path d="M218 271c3-1 7-1 10 0l1 1v1c-4 0-7 0-11 1 1-1 2-1 3-2v-1h-3z" class="U"></path><path d="M230 253v9c1 1 1 5 0 7v2 1h-1l-1-1 1-1v-1l-2-2h1l1-1c0-1-1-2-2-3s-3-2-4-2h-3 2v-1h-1l-1-1 1-1c0-1-1-1-1-2-1-1-2-3-3-3s-2 1-4 0h17z" class="F"></path><path d="M220 256c0-1 1-1 1-1 3 1 6 0 8 1l1 2c-2 1-3 1-5 0-1 0-2 0-3 1l-1 1-1-1 1-1c0-1-1-1-1-2z" class="L"></path><path d="M221 260l1-1c1-1 2-1 3-1 2 1 3 1 5 0 0 3-1 5 0 8h-1c0-1-1-2-2-3s-3-2-4-2h-3 2v-1h-1z" class="i"></path><path d="M190 311l2 3 1 1 3 4 1-1v-3c1 2 2 3 4 5h0l1 1 1 1 2 2c1 1 4 3 6 4l1 2-1 1c-1 0-1 0-2 1 1 0 2 1 2 2l-3 3h-2v-2c-2-1-5-3-7-4-1 0-2-1-3-2-1 0-2-2-3-3 0-1-1-1-2-2 0-1 0-2-1-3 0 0-1-1-1-2v-1h0 1v-1-2-1-3z" class="D"></path><path d="M190 315c1 1 1 2 1 4h0c-1 0-1 0-1-1v-1-2z" class="C"></path><path d="M193 315l3 4 1-1v-3c1 2 2 3 4 5h0l1 1 1 1 2 2c1 1 4 3 6 4l1 2-1 1c-1 0-1 0-2 1-1-2-3-2-5-3v-1c-5-3-10-7-11-13z" class="Y"></path><path d="M202 321l1 1c1 1 1 2 1 3h-1c-1 0-2 0-3-2 1 0 1-1 2-2z" class="O"></path><path d="M197 315c1 2 2 3 4 5h0l1 1c-1 1-1 2-2 2-2-1-3-2-4-4l1-1v-3z" class="M"></path><path d="M205 324c1 1 4 3 6 4l1 2-1 1c-1 0-1 0-2 1-1-2-3-2-5-3 0-1 1-1 1-1 1 0 1 1 2 0v-1c0-1-1-1-1-1-1-1-1-2-1-2z" class="N"></path><defs><linearGradient id="AE" x1="221.397" y1="254.816" x2="214.901" y2="239.203" xlink:href="#B"><stop offset="0" stop-color="#a7a6a6"></stop><stop offset="1" stop-color="#dcdbdb"></stop></linearGradient></defs><path fill="url(#AE)" d="M206 242c3-1 6-1 9-1h15v12h-17l-8-1v-1c0-2 0-6 1-9h0z"></path><path d="M205 251c0-2 0-6 1-9h0v6c1 0 1-1 2-1l1-1h0 0 2 0c-2 1-4 3-5 5h-1z" class="n"></path><path d="M206 242h0 2c1 2 1 3 0 5-1 0-1 1-2 1v-6h0z" class="m"></path><path d="M232 261l1-4h3l1 1c-1 2-3 1-4 3h1l1 1v2c0 2 0 2 1 4-1 0-2 0-3 1l1 1h0 0l1-1h3v44h-3v-1h2v-4c-2 0-3 0-4 1 0 1-1 2-1 3v-3-1c-1-7 0-14 0-21v-26z" class="H"></path><path d="M234 261l1 1v2h-1c-1-1 0-2 0-3z" class="h"></path><defs><linearGradient id="AF" x1="201.678" y1="287.245" x2="190.384" y2="308.272" xlink:href="#B"><stop offset="0" stop-color="#1b1a1b"></stop><stop offset="1" stop-color="#343434"></stop></linearGradient></defs><path fill="url(#AF)" d="M193 274c1 0 1 0 1 1v1h1c1-1 2-1 3-1 0 0-1 1-1 2l-2 4h1v2c0 1-1 2 0 4h1c0 3-1 4-1 7v5c1 1 0 2 1 4v1 2c1 1 1 2 2 2h0v1h-1 0v3l1 1h-4l-1-1v1h2v1c0 1 0 1 1 1h0v3l-1 1-3-4-1-1-2-3c-3-4-2-10-2-15 0-3-1-6 0-9 0-1 0-2 1-3 0-2 1-4 2-6 0-1 1-2 1-3l1-1z"></path><path d="M195 298h1v-4 5 3 1h0c-1-2-1-3-1-5z" class="L"></path><path d="M196 287h1c0 3-1 4-1 7v4h-1c0-1-1-3-1-5s1-4 2-6z" class="b"></path><path d="M193 274c1 0 1 0 1 1v1h1c1-1 2-1 3-1 0 0-1 1-1 2l-2 4c-1 3-2 5-2 8-1 1-1 4-1 5 0-1-1-1-1-2v-2c-1 0-1-1-2-1 0-1 0-1 1-3-1 0-1-1-1-2 0-2 1-4 2-6 0-1 1-2 1-3l1-1z" class="f"></path><path d="M193 274c1 0 1 0 1 1v1l-2 2h-1c0-1 1-2 1-3l1-1z" class="k"></path><path d="M191 290c0-2 1-3 1-4v-1c0-3 2-6 5-8l-2 4c-1 3-2 5-2 8-1 1-1 4-1 5 0-1-1-1-1-2v-2z" class="I"></path><path d="M188 287c0-1 0-2 1-3 0 1 0 2 1 2-1 2-1 2-1 3 1 0 1 1 2 1v2c0 1 1 1 1 2v1l1 9c1 2 1 3 1 5 1 0 2 2 2 2l-1 1v1l-1-1v1h2v1c0 1 0 1 1 1h0v3l-1 1-3-4-1-1-2-3c-3-4-2-10-2-15 0-3-1-6 0-9z" class="Z"></path><path d="M188 287c0-1 0-2 1-3 0 1 0 2 1 2-1 2-1 2-1 3 1 0 1 1 2 1v2c0 1 1 1 1 2l-2 1v-1c-1-1-1-2-2-3h0v-4zm2 19h0v-1-1c-1-1-1-6 0-7v-1l2-1 1 9v-1l-2 2-1-1v2z" class="M"></path><path d="M190 304v-1h1 2l-2 2-1-1z" class="j"></path><path d="M193 303v1c1 2 1 3 1 5 1 0 2 2 2 2l-1 1v1l-1-1v1h2v1c0 1 0 1 1 1h0v3l-1 1-3-4-1-1v-2c0-1-1-2-1-3v-1c-1 0-1-1-1-2v-2l1 1 2-2z" class="U"></path><path d="M224 295c1 1 3 4 3 6v2h1s1-1 1-2c0 2 0 4 1 6v2h0-1c2 2 2 3 1 5v3h0c1 1 0 2 0 3h-1c-1 3-2 5-5 7-1 1-2 1-3 3h0 1v2c-1 2-3 3-4 5-2 1-2 3-3 4s-2 1-2 2h1c-1 2-4 5-6 5l-1 1h0l-1 1c-1-1-3-1-5-1-1 0-3-1-4-1-1-2-3-2-4-3h3c1 0 1 0 1-1h-1 2v-1h2c-1-1-1-2-2-3 1 0 1 0 2 1h2l-1-1v-1l2-1v-1h3 2l3-3c0-1-1-2-2-2 1-1 1-1 2-1l1-1-1-2h0 1v-2l1-1c0-1-2-2-3-3l2-1h0c1 1 2 2 2 3l1 1s1 0 1 1v1h1c1 0 2 1 3 0 1 0 2-1 3-1v-1c0-2 2-4 3-6 1-1 2-3 2-4v-2c0-2-2-4-3-5s-1-3-1-4l-1-2v-3c0-1 0-3 1-4z" class="P"></path><path d="M214 331c2-2 3-3 5-4 0 2-3 5-4 6l-1-2z" class="O"></path><path d="M213 325c1 0 1 1 1 1 0 2-1 3-1 4h-1l-1-2h0 1v-2l1-1z" class="E"></path><path d="M224 295c1 1 3 4 3 6v2h1s1-1 1-2c0 2 0 4 1 6v2h0-1c2 2 2 3 1 5v3h0c1 1 0 2 0 3h-1 0c-2 0-3 4-5 5 0-1 2-3 2-3 1-2 4-6 4-8 0-1-1-1-1-1 0 1-1 1-1 2v-2c0-2-2-4-3-5s-1-3-1-4l-1-2v-3c0-1 0-3 1-4z" class="K"></path><path d="M224 295c1 1 3 4 3 6v2h1c1 2 1 5 0 6h0c-1-2-3-4-4-6v1l-1-2v-3c0-1 0-3 1-4z" class="d"></path><path d="M223 299h1v4 1l-1-2v-3z" class="R"></path><path d="M214 331l1 2-2 1 1 1h0l-2 2 1 1 3-4h1v1l-1 1c-1 0-2 1-2 2v1c-1 1-3 2-3 4-1 0-1 1-2 1l4-1h1c-1 2-4 5-6 5l-1 1h0l-1 1c-1-1-3-1-5-1-1 0-3-1-4-1-1-2-3-2-4-3h3c1 0 1 0 1-1h-1 2v-1h2c-1-1-1-2-2-3 1 0 1 0 2 1h2l-1-1v-1l2-1v-1h3 2l3-3 3-3z" class="J"></path><path d="M214 331l1 2-2 1c-4 3-7 7-11 10h-4v-1h2c-1-1-1-2-2-3 1 0 1 0 2 1h2l-1-1v-1l2-1v-1h3 2l3-3 3-3z" class="I"></path><path d="M198 340c1 0 1 0 2 1h2 1l-2 2h-1c-1-1-1-2-2-3z" class="S"></path><path d="M206 337h2l-5 4h-1l-1-1v-1l2-1v-1h3z" class="G"></path><path d="M213 343h1c-1 2-4 5-6 5l-1 1h0l-1 1c-1-1-3-1-5-1-1 0-3-1-4-1-1-2-3-2-4-3h3c1 0 1 0 1-1h-1 2 4 7l4-1z" class="k"></path><path d="M200 346c3 1 6 1 8 2l-1 1h0 0s0-1-1 0h0c-2 0-5-2-6-3z" class="a"></path><path d="M193 345h3l4 1c1 1 4 3 6 3h0c1-1 1 0 1 0h0l-1 1c-1-1-3-1-5-1-1 0-3-1-4-1-1-2-3-2-4-3z" class="Q"></path><path d="M223 302l1 2c0 1 0 3 1 4s3 3 3 5v2c0 1-1 3-2 4-1 2-3 4-3 6v1c-1 0-2 1-3 1-1 1-2 0-3 0h-1v-1c0-1-1-1-1-1l-1-1c0-1-1-2-2-3h0l-2 1c1 1 3 2 3 3l-1 1v2h-1 0c-2-1-5-3-6-4l-2-2-1-1-1-1h0c-2-2-3-3-4-5h0c-1 0-1 0-1-1v-1h-2v-1l1 1h4 0 5l-1-1h3l-1-1 1-1c-2-1-2-3-3-4 1-1 0-2 1-2s1 1 1 1l3 3h1 0 1 1c2 0 3-1 4-1 2 0 2 1 2 2 1 0 1 1 2 1v-2l1-1s0-1-1-2h1v-1l2 1c1-1 1-2 1-3z" class="b"></path><path d="M206 312c1 0 1 0 2 1-1 1-2 2-4 2l1-1v-1h-1l-1-1h3z" class="X"></path><path d="M218 316c3 0 5-1 8-3l1 2c-1 0-2 1-2 1-2 0-4 1-6 1-4 0-7-1-10-4l9 3z" class="E"></path><path d="M197 315c-1 0-1 0-1-1v-1h-2v-1l1 1h4 0 5 1v1l-1 1c0 1-1 1-1 2v2c-1 0-2 0-2 1-2-2-3-3-4-5h0z" class="B"></path><path d="M197 315c-1 0-1 0-1-1v-1h-2v-1l1 1h4 0 5 1v1c-3 0-6 0-8 1z" class="i"></path><path d="M228 313v2c0 1-1 3-2 4-1 2-3 4-3 6h-2c-2 0-4-4-5-6 3 0 6-1 9-3h0s1-1 2-1l1-2z" class="D"></path><path d="M223 302l1 2c0 1 0 3 1 4s3 3 3 5l-1 2-1-2c-3 2-5 3-8 3l1-2h1v-1c1 0 1 0 1-1l-1-1-1-1v-2l1-1s0-1-1-2h1v-1l2 1c1-1 1-2 1-3z" class="R"></path><path d="M220 305c1 1 2 2 2 3l3 5h-1c-1 1-1 1-2 0v-2c-1-1-1-2-2-4 0 0 0-1-1-2h1z" class="S"></path><path d="M223 302l1 2c0 1 0 3 1 4s3 3 3 5l-1 2-1-2h-1 0l-3-5c0-1-1-2-2-3v-1l2 1c1-1 1-2 1-3z" class="N"></path><path d="M220 304l2 1v1h1l1 2h-2c0-1-1-2-2-3v-1z" class="I"></path><path d="M224 308c0 1 0 2 1 3l1 1v1h-1 0l-3-5h2z" class="Y"></path><path d="M203 306c1-1 0-2 1-2s1 1 1 1l3 3h1 0 1 1c2 0 3-1 4-1 2 0 2 1 2 2 1 0 1 1 2 1l1 1 1 1c0 1 0 1-1 1v1h-1c-2 0-4 0-6-1-1 0-2-1-3-2-1 1-2 1-2 2-1-1-1-1-2-1l-1-1 1-1c-2-1-2-3-3-4z" class="E"></path><path d="M206 310l1 1c1 0 2-1 3 0-1 1-2 1-2 2-1-1-1-1-2-1l-1-1 1-1zm7 3c1 0 1 0 2-1 0-1-1-1-1-2v-2c1 0 1 1 2 2l1-1c1 0 1 1 2 1l1 1 1 1c0 1 0 1-1 1v1h-1c-2 0-4 0-6-1z" class="L"></path><path d="M217 309c1 0 1 1 2 1l1 1v2c-1-1-2-1-2-2l-1-1v2h-1v-2l1-1z" class="K"></path><path d="M206 316c1-1 1-1 3-2v1c2 1 3 2 5 3v1c2 3 4 6 6 8-1 1-2 0-3 0h-1v-1c0-1-1-1-1-1l-1-1c0-1-1-2-2-3h0l-2 1c1 1 3 2 3 3l-1 1v2h-1 0c-2-1-5-3-6-4l-2-2-1-1-1-1h2v-1l3-3z" class="B"></path><path d="M211 326v-1c0-1-1-2-2-2h0v-1h1c1 1 3 2 3 3l-1 1h-1z" class="K"></path><path d="M206 316c1-1 1-1 3-2v1h1v3c-1 1-2 2-2 4h-1v-3-1l-1-2z" class="X"></path><path d="M206 316c1-1 1-1 3-2v1h1c-1 2-2 3-3 4v-1l-1-2z" class="K"></path><path d="M206 316l1 2c-1 1-1 1-1 2 0 2 2 4 3 5l1 1h1 1v2h-1 0c-2-1-5-3-6-4l-2-2-1-1-1-1h2v-1l3-3z" class="L"></path><path d="M232 243h0c0-6 1-13 1-19v-39l1-36v-6-3l1-1h1c0 1 0 1 1 1v3 14 24 18 18c0 2 1 5 0 7 1 8 1 17 1 26 0 3-1 6 0 9v10h-3l-1 1h0 0l-1-1c1-1 2-1 3-1-1-2-1-2-1-4v-2l-1-1h-1c1-2 3-1 4-3l-1-1h-3l-1 4v-18z" class="H"></path><path d="M237 224c1 8 1 17 1 26 0 3-1 6 0 9v10h-3l-1 1h0 0l-1-1c1-1 2-1 3-1-1-2-1-2-1-4v-2l-1-1h-1c1-2 3-1 4-3l-1-1h-3l-1 4v-18c1 3 1 7 1 10l4 1v-19-11h0z" class="Q"></path><path d="M215 281c4-2 8-3 12-3 1 0 2-1 3 0h0c0 2 1 4 0 6h-1c1 4 0 9 0 14v3c0 1-1 2-1 2h-1v-2c0-2-2-5-3-6-1 1-1 3-1 4v3c0 1 0 2-1 3l-2-1v1h-1c1 1 1 2 1 2l-1 1v2c-1 0-1-1-2-1 0-1 0-2-2-2-1 0-2 1-4 1h-1-1 0l-2-2c0-1-1-2-1-3-1-2-1-3-1-5h-1c0-3 1-5 3-8l2-3 6-6z" class="K"></path><path d="M225 285h0c-1-1-1-2 0-3s2-1 2-2c1 0 1-1 2-1v1l-1 1-3 4z" class="G"></path><path d="M218 287c0 1 1 1 2 1h1v2s-1 0-1 2h-1v-1h-1l-1-2 1-2z" class="e"></path><path d="M218 286c2-2 3-4 5-5l1 1h0c-1 0-1 1-2 1 1 2 0 3 0 5h-1-1c-1 0-2 0-2-1v-1z" class="b"></path><path d="M218 286c2 0 2 0 3 1v1h-1c-1 0-2 0-2-1v-1z" class="N"></path><path d="M217 289l1 2h1v1h1v5c-1 0-2 1-3 2l-2 3s0-1-1-1c2-2 2-4 2-6l1-6z" class="d"></path><path d="M219 292h1v5c-1 0-2 1-3 2 0-2 1-5 2-7z" class="R"></path><path d="M228 281h1c0 1 0 1-1 2-2 2-5 8-5 11l1 1c-1 1-1 3-1 4v3c0 1 0 2-1 3l-2-1h0-1-2l2-2c0-1 1-3 1-4l5-13 3-4z" class="M"></path><path d="M219 302c1 1 1 1 2 1 1-1 1-2 1-2 0-1 0-1 1-2h0v3c0 1 0 2-1 3l-2-1h0-1-2l2-2z" class="O"></path><path d="M217 299c1-1 2-2 3-2v1c0 1-1 3-1 4l-2 2h2 1 0v1h-1c1 1 1 2 1 2l-1 1v2c-1 0-1-1-2-1 0-1 0-2-2-2-1 0-2 1-4 1h-1-1c1-2 2-2 2-4h0c0-1 0-1-1-2h1c1 0 1 0 2 1 0-1 1-1 1-2 1 0 1 1 1 1l2-3z" class="X"></path><path d="M215 306c0-1 1-2 2-2h2 1c-1 0-2 0-3 1l-2 2c-1 0-2 1-4 1v-1c1-1 1-1 2-1h2 0z" class="b"></path><path d="M215 307l2-2c1-1 2-1 3-1h0v1h-1c1 1 1 2 1 2l-1 1v2c-1 0-1-1-2-1 0-1 0-2-2-2z" class="L"></path><path d="M217 299c1-1 2-2 3-2v1c0 1-1 3-1 4l-2 2c-1 0-2 1-2 2-1 0-1 0-2-1v-1c1 0 2 0 2-2h0l2-3z" class="J"></path><path d="M229 280c1 1 0 2 1 4h-1c1 4 0 9 0 14v3c0 1-1 2-1 2h-1v-2c0-2-2-5-3-6l-1-1c0-3 3-9 5-11 1-1 1-1 1-2h-1l1-1z" class="R"></path><path d="M229 298l-1-4v-1-3s-1-1-1-2c0-2 1-3 2-4 1 4 0 9 0 14zm-14-17c1 0 3 0 4-1 0 0 2-1 3-1v1c-1 0-2 2-3 3l-1 1h0l-2 4v2l-1 5v1h-1l-2-2c0-2 0-2-2-2l-2-1-1-1 2-3 6-6z" class="L"></path><path d="M207 290l2-3v2c1 0 3 1 3 2-1 0-3-2-4-1v1l-1-1z" class="J"></path><path d="M213 286l2-2c1 0 1-1 3 0l-2 4v2-1h-1-2v-3z" class="b"></path><path d="M213 286l1 2h2v2-1h-1-2v-3z" class="R"></path><path d="M213 289h2 1v1l-1 5v1h-1l-2-2c1-2 1-4 1-5z" class="b"></path><path d="M207 290l1 1 2 1c2 0 2 0 2 2l2 2h1v-1h1 0c0 2 0 4-2 6 0 1-1 1-1 2-1-1-1-1-2-1h-1c1 1 1 1 1 2h0c0 2-1 2-2 4h0l-2-2c0-1-1-2-1-3-1-2-1-3-1-5h-1c0-3 1-5 3-8z" class="S"></path><path d="M209 297h1l1 1-1 1h-1-2v-1l2-1z" class="X"></path><path d="M207 301l1-1h1l1 1h1l1-1c-1 1-1 1-1 2h-1c1 1 1 1 1 2h0-1c-1-1-2-1-3-3z" class="B"></path><path d="M207 301c1 2 2 2 3 3h1c0 2-1 2-2 4h0l-2-2c0-1-1-2-1-3l1-2z" class="L"></path><path d="M210 292c2 0 2 0 2 2l2 2v1c-1-1-2-1-2-2l-1 1c-1 0-2 0-3-1v-2-1h0 1 1z" class="B"></path><path d="M210 292c2 0 2 0 2 2l2 2v1c-1-1-2-1-2-2h0c-1-2-2-2-3-3h1z" class="J"></path><path d="M215 295h1 0c0 2 0 4-2 6 0 1-1 1-1 2-1-1-1-1-2-1 0-1 0-1 1-2l-1-1c1 0 1 0 1-1s0-1-1-2l1-1c0 1 1 1 2 2v-1h1v-1z" class="P"></path><defs><linearGradient id="AG" x1="230.828" y1="225.586" x2="212.555" y2="219.938" xlink:href="#B"><stop offset="0" stop-color="#b6b4b5"></stop><stop offset="1" stop-color="#d9d8d9"></stop></linearGradient></defs><path fill="url(#AG)" d="M206 208l2-1h1v2h2c2 1 7 1 9 1h8c0 1 0 3-1 4v2h-1-2l-1 1c2 0 6-1 7 1v15c0 1 0 4-1 5h-3-12-8v-8c0-4 0-8 2-12h0l1-1h-2l-1-1c0-1 1-2 0-4h0c0-2 0-3-1-4h1z"></path><path d="M216 220h3l2 2h-3v1l-1-1c-1-1-1-1-1-2z" class="V"></path><path d="M212 216h1s0-1 1-1h0l1-1 1 1 1 1v2h-6l1-1v-1h0z" class="P"></path><path d="M219 212l1-1v5h1v-1l1 1h2 0l-1 1c2 0 6-1 7 1-1-1-11-1-13 0v-2-1l1 1h1v-4z" class="B"></path><path d="M220 210h8c0 1 0 3-1 4v2h-1-2 0-2l-1-1v1h-1v-5-1z" class="d"></path><path d="M226 216c0-2 0-4 1-6h0v4 2h-1z" class="I"></path><path d="M206 208l2-1h1v2h2c2 1 7 1 9 1v1l-1 1v4h-1l-1-1v1l-1-1-1-1-1 1h0c-1 0-1 1-1 1h-1 0v1l-1 1h-3l1-1h-2l-1-1c0-1 1-2 0-4h0c0-2 0-3-1-4h1z" class="d"></path><path d="M216 212v-1h1v4 1l-1-1v-3z" class="V"></path><path d="M212 216c0-1 0-3 1-4h2 1v3l-1-1-1 1h0c-1 0-1 1-1 1h-1z" class="Y"></path><path d="M209 209h2c2 1 7 1 9 1v1l-1 1v-2h-1 0-1-5v6 1l-1 1h-3l1-1h1l-1-8z" class="J"></path><path d="M206 208l2-1h1v2l1 8h-1-2l-1-1c0-1 1-2 0-4h0c0-2 0-3-1-4h1z" class="g"></path><defs><linearGradient id="AH" x1="225.869" y1="232.995" x2="215.951" y2="232.972" xlink:href="#B"><stop offset="0" stop-color="#c0bec0"></stop><stop offset="1" stop-color="#e9e8e8"></stop></linearGradient></defs><path fill="url(#AH)" d="M215 223v-2l1-1c0 1 0 1 1 2l1 1v-1h3c3 2 5 11 9 11 0 1 0 4-1 5h-3-12c0-1 0-1 1-1h0v-1c1-1 1-2 2-2v-1c1-1 2-3 2-4-1 0-2 1-3 1-1-1-1-2-1-4h0c-1-1 0-1 0-2v-1z"></path><path d="M215 223v-2l1-1c0 1 0 1 1 2l1 1h1l-2 2h0 4 0v1c1 0 1 0 1 1v5h-1 0s0-1-1-1c0-1 0-2-1-3h-1c-1 1-1 1-2 1l-1-3h0c-1-1 0-1 0-2v-1z" class="k"></path><path d="M221 222c3 2 5 11 9 11 0 1 0 4-1 5h-3c1-1 1-1 2-1-2-4-3-7-6-10 0-1 0-1-1-1v-1h0-4 0l2-2h-1v-1h3z" class="a"></path><path d="M208 218h5l2 5v1c0 1-1 1 0 2h0c0 2 0 3 1 4 1 0 2-1 3-1 0 1-1 3-2 4v1c-1 0-1 1-2 2v1h0c-1 0-1 0-1 1h-8v-8c0-4 0-8 2-12z" class="l"></path><path d="M214 226h0v3h-1 0c0-1 0-2 1-3z" class="k"></path><defs><linearGradient id="AI" x1="223.783" y1="291.725" x2="196.223" y2="285.761" xlink:href="#B"><stop offset="0" stop-color="#0b0a0b"></stop><stop offset="1" stop-color="#343333"></stop></linearGradient></defs><path fill="url(#AI)" d="M218 259h2l1 1h1v1h-2 3c1 0 3 1 4 2s2 2 2 3l-1 1h-1l2 2v1l-1 1c-3-1-7-1-10 0h3v1c-1 1-2 1-3 2-7 3-12 7-15 14l-3 6 1 7c1 2 1 4 2 5s1 3 3 4l-1 1 1 1h-3l1 1h-5 0l-1-1v-3h0 1v-1h0c-1 0-1-1-2-2v-2-1c-1-2 0-3-1-4v-5c0-3 1-4 1-7h-1c-1-2 0-3 0-4v-2h-1l2-4c0-1 1-2 1-2-1 0-2 0-3 1h-1v-1c0-1 0-1-1-1 2-4 5-7 9-10l5-3c1-1 3-1 4-2h0 3 4z"></path><path d="M199 301c-1-3-1-6-1-9l1 4h0v1c0 1 1 1 1 2v2h-1z" class="O"></path><path d="M199 308c1 1 3 2 3 4h1l1 1h-5 0l-1-1v-3h0 1v-1z" class="K"></path><path d="M199 313l-1-1v-3h0 1l1 1-1 3h0z" class="G"></path><path d="M200 299v-5l1 7c1 2 1 4 2 5s1 3 3 4l-1 1c-3-4-5-6-6-10h1v-2z" class="N"></path><defs><linearGradient id="AJ" x1="210.533" y1="284.508" x2="205.416" y2="278.621" xlink:href="#B"><stop offset="0" stop-color="#959597"></stop><stop offset="1" stop-color="#c9c7c8"></stop></linearGradient></defs><path fill="url(#AJ)" d="M198 292l3-8c4-7 9-11 17-13h3v1c-1 1-2 1-3 2-7 3-12 7-15 14l-3 6v5c0-1-1-1-1-2v-1h0l-1-4z"></path><defs><linearGradient id="AK" x1="218.733" y1="260.863" x2="217.223" y2="269.345" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#343334"></stop></linearGradient></defs><path fill="url(#AK)" d="M220 261h3c1 0 3 1 4 2s2 2 2 3l-1 1h-1c-3 0-5 1-8 1-1 0-2 1-3 1l-6 2s-1 1-2 1h-2l-1 1h0c-1 0-1 0-1-1l1-1c1 0 2-1 2-2h0l1-1c0-1 0-2 1-3s3-1 4-2l5-1 2-1z"></path><path d="M220 264c3 0 5 0 7 1l1 2h-1c-3 0-5 1-8 1 0-2 1-2 2-3l-1-1z" class="B"></path><path d="M220 264l1 1c-1 1-2 1-2 3-1 0-2 1-3 1l-6 2s-1 1-2 1h-2l-1 1h0c-1 0-1 0-1-1l1-1c1 0 2-1 2-2 1 0 3-1 4-2l9-3z" class="X"></path><path d="M206 272c2-1 3-2 5-3 0 0 1 0 1-1h0c2 0 3 0 4 1l-6 2s-1 1-2 1h-2z" class="L"></path><path d="M218 259h2l1 1h1v1h-2l-2 1-5 1c-1 1-3 1-4 2s-1 2-1 3l-1 1h0c0 1-1 2-2 2l-1 1c0 1 0 1 1 1-2 2-5 5-6 7l-1 1v1c-1 0-1 1-1 2l-1-1v-2h-1l2-4c0-1 1-2 1-2-1 0-2 0-3 1h-1v-1c0-1 0-1-1-1 2-4 5-7 9-10l5-3c1-1 3-1 4-2h0 3 4z" class="H"></path><path d="M218 259h2l1 1h1v1h-2l-2 1-5 1c1-1 1-1 2-1v-1c-1 0-3-1-4-2h0 3 4z" class="N"></path><path d="M211 259c2 0 2 0 3 1 1 0 2-1 2 0 1 0 1 0 2 1v1l-5 1c1-1 1-1 2-1v-1c-1 0-3-1-4-2h0z" class="Q"></path><path d="M202 264l5-3c0 3-5 6-7 8l-6 6c0-1 0-1-1-1 2-4 5-7 9-10z" class="p"></path><path d="M209 265c-1 1-1 2-1 3l-1 1h0c0 1-1 2-2 2l-1 1c0 1 0 1 1 1-2 2-5 5-6 7l-1 1v1c-1 0-1 1-1 2l-1-1v-2h-1l2-4c0-1 1-2 1-2 3-4 7-8 11-10z" class="B"></path><path d="M232 309v3c0-1 1-2 1-3 1-1 2-1 4-1v4h-2v1h3 0l1 2v7l1-1h0l-1 1c0 1 1 1 1 2v2h0c-1 1-1 1-1 2 1 1 4 1 4 2v1c-1-1-2-1-4-1v1s0 1 1 1v2h-1c-1 4 0 9-1 13v4 3 1l-1 6-1 4-1 2-3-5c-1-1-1-2-2-3v-2h-3l-1-1h-3l-1-1c-1-3-1-7-2-10v-2h-2-4-1c0-1 1-1 2-2s1-3 3-4c1-2 3-3 4-5 3-2 5-2 9-3v-1c1-6 0-13 1-19z" class="a"></path><path d="M238 351v3 1s0-1-1-1h-2c0-1 1-1 2-1h0c1-1 1-2 1-2z" class="V"></path><path d="M237 353c-1-1-1-1-1-2l1-2c0-1 1-1 0-2 0-1 1-1 1-1v1 4s0 1-1 2h0z" class="j"></path><path d="M237 354c1 0 1 1 1 1l-1 6c-1-1-2-4-1-5 0-1 1-1 1-2h0z" class="H"></path><path d="M238 313l1 2v7l1-1h0l-1 1c0 1 1 1 1 2v2h0c-1 1-1 1-1 2 1 1 4 1 4 2v1c-1-1-2-1-4-1v1s0 1 1 1v2h-1c-1 4 0 9-1 13v-1c1-3 0-8 0-11v-22z" class="D"></path><path d="M231 329v-1 17 9c1 3 3 9 5 11l-1 2-3-5c-1-1-1-2-2-3v-2h-3l-1-1h-3l-1-1c-1-3-1-7-2-10v-2h-2-4-1c0-1 1-1 2-2s1-3 3-4c1-2 3-3 4-5 3-2 5-2 9-3z" class="W"></path><path d="M227 337l2-2h0v8-2l-1-1c0-1 0-1-1-3h0z" class="F"></path><path d="M227 337c-1 2-1 3-2 4h-1c0-2 2-5 3-6h1c0 1-1 2-1 2h0z" class="g"></path><path d="M231 329s-1 0-1 1c-1 1-4 3-6 4-2 2-3 4-5 5 0 1-3 3-3 3-1 1 0 1 0 1h2-4-1c0-1 1-1 2-2s1-3 3-4c1-2 3-3 4-5 3-2 5-2 9-3z" class="b"></path><path d="M229 341v2 4 4c0 1 1 2 0 3l-1 1v1c1 0 1 0 2 1h-3l-1-1h-3l-1-1c-1-3-1-7-2-10v-2l1 1c1-1 0-1 1-2h5l2-1z" class="H"></path><path d="M229 351c0 1 1 2 0 3l-1 1v1c1 0 1 0 2 1h-3l-1-1h-3l-1-1h1v-3l1 1c0 2 3 0 4 1h0 1v-3z" class="L"></path><path d="M220 343l1 1c1 0 1 2 1 2 0 2 1 4 1 6v3h-1c-1-3-1-7-2-10v-2z" class="J"></path><path d="M221 344c1-1 0-1 1-2h5l1 1c-1 1-2 2-3 4h-1l-2-1s0-2-1-2z" class="k"></path><path d="M222 346v-1l1-1c0 1 1 1 2 2h0l-1 1-2-1z" class="n"></path><defs><linearGradient id="AL" x1="213.58" y1="166.495" x2="177.277" y2="212.888" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#232223"></stop></linearGradient></defs><path fill="url(#AL)" d="M198 130h1l3 3c1 0 1-1 2 0-1 1 0 1 0 2l2 2c1 2 3 3 4 5 1 1 1 2 2 3h1c0 2 0 6-1 8v2h2v1c1 1 2 1 4 1 1 0 2 1 3 1 0 0 1 1 1 0v1c2 0 3 1 4 1 2 1 4 1 6 2l-1 8v4h0v12l-1 21h-3c1 1 2 1 3 1l-1 1-1 1h-8c-2 0-7 0-9-1h-2v-2h-1l-2 1h-1c1 1 1 2 1 4h0c1 2 0 3 0 4l1 1h2l-1 1h0c-2 4-2 8-2 12v2c-1 0-1 1-2 1h-3c-1 0-1-1-3-1v2c-2-4-5-7-8-10h3c1 0 2 1 3 1l1-1-3-1-2-1-2-1-2-2-2 1v2h0l-1-1c0-1-1-1-2-1h0c1-1 2-1 2-1v-22l1-12-1-3v-5l-2-1s1 0 2-1l-1-8-2-8c0-2 0-3 1-4h1c1 0 1 1 1 1-1 0-1 0-2 1v1h1v-1c2 0 3 2 4 3h1v-1l1-1 1 1h3 0v1h2c1-1 2-1 3-1h0l2-4h0c0-1 0-2 1-4v-1c1-1 1-4 2-5v-1-3c0-1 0-1-1-1 1-1 1-2 1-3h-1-1-1l-2-2 2-2-3-3z"></path><path d="M204 162h1c0 1 0 3-1 4l-1-2 1-2z" class="K"></path><path d="M192 209v-2c0-2 1-2 2-3 0 2-1 3-2 5z" class="F"></path><path d="M188 189c1 1 2 1 3 2v1c-1 1 0 3-1 4h-1c0-1 0-1-1-2 1-1 0-4 0-5z" class="E"></path><path d="M193 195c1 2 1 3 1 4h1l-1 2c-1 1-1 2-3 2 0-1 1-2 1-3s1-3 1-5z" class="S"></path><path d="M191 203c0 2-2 6-3 7 0-1 0-2 1-4s1-4 2-7l1 1c0 1-1 2-1 3z" class="J"></path><path d="M208 168v-1c1-1 2 0 3-1l1-1v2 2l-2 11c0-1 1-2 0-4 0-1-1-3-1-5h1c0-2-1-2-2-3z" class="E"></path><path d="M196 185v1h0l2-2c0 1 0 2-1 3h0c-1 1-2 2-2 4 0 0 0 1-1 1 0 1-1 2-1 3 0 2-1 4-1 5l-1-1h0c1-3 1-5 0-8-1-1-2-1-3-2l-1-2h0l5 4 1 1h0c1-2 2-4 3-7z" class="b"></path><path d="M200 183c2 7-2 11-5 16h-1c0-1 0-2-1-4 0-1 1-2 1-3 1 0 1-1 1-1 0-2 1-3 2-4h0c1-1 1-2 1-3l1-1v3c1-1 1-2 1-3z" class="P"></path><path d="M199 183v3h0c1 2 0 4-1 5s-2 2-2 3l-2-2c1 0 1-1 1-1 0-2 1-3 2-4h0c1-1 1-2 1-3l1-1z" class="i"></path><path d="M199 183v3h0c-1 1-1 3-2 4h0v-3c1-1 1-2 1-3l1-1z" class="Y"></path><defs><linearGradient id="AM" x1="201.674" y1="165.29" x2="196.189" y2="183.765" xlink:href="#B"><stop offset="0" stop-color="#373737"></stop><stop offset="1" stop-color="#6e6d6d"></stop></linearGradient></defs><path fill="url(#AM)" d="M203 164l1 2c-2 5-4 10-4 15v2c0 1 0 2-1 3v-3l-1 1-2 2h0v-1l1-2-1-6-1-1c0-1 0-2-1-2v-1c0-1 0-1 1-2h0 2 1l1-2 2-4 1 1 1-2z"></path><path d="M200 181v2c0 1 0 2-1 3v-3h-1l2-2z" class="c"></path><path d="M196 177l2 1c0 1-1 3-1 5l-1-6z" class="X"></path><path d="M201 165l1 1c-1 2-2 8-4 10l-1-1-2 1c0-1 0-2-1-2v-1c0-1 0-1 1-2h0 2 1l1-2 2-4z" class="d"></path><path d="M199 169v4l-1-2h0l1-2z" class="X"></path><path d="M195 171h0 2 1 0l1 2c-1 0-2 1-2 2h0l-2 1c0-1 0-2-1-2v-1c0-1 0-1 1-2z" class="L"></path><path d="M198 171l1 2c-1 0-2 1-2 2 0-1-1-1-1-2l2-2z" class="R"></path><path d="M194 204l7-9c2-4 5-8 9-10v1 3l-1 4v4 4l-1 6-2 1h-1c-1 0-2 0-3 1h0c-1 0-1 0 0 1h-2 0c-1-1-2-1-3-1l-1 1c-1 0-1 0-2-1l2-1h0l2-2h-2l-2 3c-1 1-2 2-4 2 1 0 1-1 2-2 1-2 2-3 2-5z" class="X"></path><path d="M196 206l4-6 2 2c-1 2-2 3-3 4h-1 0-2z" class="T"></path><path d="M200 200c3-3 6-9 10-11l-1 4v4 4l-2 2c-1-1-1-1-2-1l-1 1v-1c1-1 2-2 3-4h-1c-1 1-2 3-4 4h0l-2-2z" class="g"></path><path d="M202 202h0c2-1 3-3 4-4h1c-1 2-2 3-3 4v1l1-1c1 0 1 0 2 1l2-2-1 6-2 1h-1c-1 0-2 0-3 1h0c-1 0-1 0 0 1h-2 0c-1-1-2-1-3-1l-1 1c-1 0-1 0-2-1l2-1h0l2-2h0 1c1-1 2-2 3-4z" class="E"></path><path d="M204 202v1l1-1c1 0 1 0 2 1l2-2-1 6-2 1c0-2 1-3 1-4h0v-1c-3 0-2 1-4 2l-1-1 2-2z" class="C"></path><path d="M198 206l2 1h2 1c-1 1-1 1-2 1l1 1c-1 0-1 0 0 1h-2 0c-1-1-2-1-3-1l-1 1c-1 0-1 0-2-1l2-1h0l2-2h0z" class="P"></path><path d="M198 206l2 1c-1 1-2 1-4 1l2-2h0z" class="B"></path><path d="M196 208h2l-1 1-1 1c-1 0-1 0-2-1l2-1z" class="e"></path><path d="M198 130h1l3 3c1 0 1-1 2 0-1 1 0 1 0 2l2 2c1 2 3 3 4 5 1 1 1 2 2 3h1c0 2 0 6-1 8v2h2v1h-2c-1 2 0 7 0 9l-1 1c-1 1-2 0-3 1v1c-1-2 0-3-1-4 0-1-1-2-1-3 0 0 0-1-1-1h0v2h-1l-1 2-1 2-1-1-2 4-1 2h-1l1-2c0-1 1-2 1-3l-1-1c0 1 0 1-1 1 1-2 1-4 2-6v-1l2-4h0c0-1 0-2 1-4v-1c1-1 1-4 2-5v-1-3c0-1 0-1-1-1 1-1 1-2 1-3h-1-1-1l-2-2 2-2-3-3z" class="C"></path><path d="M203 162h0v-3h1v3h0l-1 2-1 2-1-1c0-1 1-2 2-3z" class="J"></path><path d="M204 144v3h1v1l-1 1h0l-1 2h0v3l-1 1h-1 0c0-1 0-2 1-4v-1c1-1 1-4 2-5v-1z" class="D"></path><path d="M202 151c1 2 1 2 1 3-1 1-1 1-2 1 0-1 0-2 1-4z" class="S"></path><path d="M198 165c1-1 1-1 1-2h0c1-2 1-3 2-4 1 1 1 2 2 3-1 1-2 2-2 3l-2 4-1 2h-1l1-2c0-1 1-2 1-3l-1-1z" class="F"></path><path d="M198 130h1l3 3c1 0 1-1 2 0-1 1 0 1 0 2l2 2c1 2 3 3 4 5 1 1 1 2 2 3h1c0 2 0 6-1 8v-4c-1 0-1 1-2 1h-1s-4 5-4 6l-1 1h-1c0-2 1-2 2-3 0-1 1-2 2-4h0v-4c0-1-1-2-1-3s-1-2-2-2c0-1 0-1-1-1 1-1 1-2 1-3h-1-1-1l-2-2 2-2-3-3z" class="E"></path><path d="M208 144l1 1h0c1 0 1 2 1 2h-1c-1-1-1-2-1-3z" class="F"></path><path d="M201 133c1 1 1 2 1 4h-1l-2-2 2-2z" class="X"></path><path d="M206 137c1 2 3 3 4 5 1 1 1 2 2 3v3c-1 0-1 0-2-1 0 0 0-2-1-2h0l-1-1c-1-2-2-3-3-4v-1c0-1 0-1 1-2z" class="g"></path><path d="M196 206h2l-2 2h0l-2 1c1 1 1 1 2 1l1-1c1 0 2 0 3 1h0 2c-1-1-1-1 0-1h0c1-1 2-1 3-1 1 1 1 2 1 4h0c1 2 0 3 0 4l1 1h2l-1 1h0c-2 4-2 8-2 12v2c-1 0-1 1-2 1h-3c-1 0-1-1-3-1v2c-2-4-5-7-8-10h3c1 0 2 1 3 1l1-1-3-1-2-1-2-1-2-2c-1-1-1-1-1-3 0 0 1-1 1-2 1-1 1-2 2-3 2 0 3-1 4-2l2-3z" class="E"></path><path d="M190 221c1-1 1-1 1-2l2-2v-1c0-1 0-1 1-1h1v2h2 3l-3 3h-2c-1 0-2 0-3 2h0l-2-1z" class="B"></path><path d="M198 214l-1 1c-1 0-2-1-3-1-1-1-2 0-3 0v-1c1-1 2-2 3-4 1 1 1 1 2 1l1-1c1 0 2 0 3 1h0c1 1 1 1 1 2s-1 1-2 2v1l-1-1z" class="R"></path><path d="M197 209c1 0 2 0 3 1l-2 1-1 2v-2l-2 2-1-1 2-2 1-1z" class="B"></path><path d="M200 210h0c1 1 1 1 1 2s-1 1-2 2v1l-1-1-1-1 1-2 2-1z" class="C"></path><path d="M202 209c1-1 2-1 3-1 1 1 1 2 1 4h0c1 2 0 3 0 4 0 3-1 5-3 6 0 1 0 1-1 1-1 1-3 1-5 1l-3-1-2-1h0c1-2 2-2 3-2h2l3-3c1-1 1-1 2-3-1 0-2 1-3 0 1-1 2-1 2-2s0-1-1-2h2c-1-1-1-1 0-1h0z" class="R"></path><path d="M204 214l1 1c-1 1-1 3-3 4v1l-1-1h0c1-1 1-2 2-3l1-2z" class="L"></path><path d="M196 222c1 0 2 0 3 1 1 0 2 0 3-1v1c-1 1-3 1-5 1l-3-1c0-1 1-1 2-1z" class="c"></path><path d="M202 214h0l1 2c-1 1-1 2-2 3h0l1 1h-1l-1-1c-2 0-3 1-5 2h1v1c-1 0-2 0-2 1l-2-1h0c1-2 2-2 3-2h2l3-3c1-1 1-1 2-3z" class="P"></path><path d="M202 209c1-1 2-1 3-1 1 1 1 2 1 4h0v2h-2 0l-1 2-1-2h0c-1 0-2 1-3 0 1-1 2-1 2-2s0-1-1-2h2c-1-1-1-1 0-1h0z" class="b"></path><path d="M202 209c1-1 2-1 3-1 1 1 1 2 1 4-1-1-2-2-4-3z" class="C"></path><path d="M202 210c1 0 1 1 2 2h0v2h0l-1 2-1-2h0c-1 0-2 1-3 0 1-1 2-1 2-2s0-1-1-2h2z" class="J"></path><path d="M202 214v-2h2v2h0l-1 2-1-2z" class="d"></path><path d="M202 223c1 0 1 0 1-1 2-1 3-3 3-6l1 1h2l-1 1h0c-2 4-2 8-2 12v2c-1 0-1 1-2 1h-3c-1 0-1-1-3-1v2c-2-4-5-7-8-10h3c1 0 2 1 3 1l1-1c2 0 4 0 5-1z" class="D"></path><path d="M204 223h1v1c-1 1-3 3-5 3-1 1-2 1-3 0l-2-1v-1l2 1c2 0 5-1 7-3z" class="R"></path><path d="M182 159c0-2 0-3 1-4h1c1 0 1 1 1 1-1 0-1 0-2 1v1h1v-1c2 0 3 2 4 3h1v-1l1-1 1 1h3 0v1h2c1-1 2-1 3-1h0v1c-1 2-1 4-2 6 1 0 1 0 1-1l1 1c0 1-1 2-1 3l-1 2h-2 0c-1 1-1 1-1 2v1c1 0 1 1 1 2l1 1 1 6-1 2c-1 3-2 5-3 7h0l-1-1-5-4h0l1 2c0 1 1 4 0 5v1c0 1 0 1-1 1 0 1 1 1 0 2v1c0 1 0 1-1 1h0c-1-4 1-11 0-15l-1-3v-5l-2-1s1 0 2-1l-1-8-2-8z" class="g"></path><path d="M184 158v-1c2 0 3 2 4 3h1v-1l1-1 1 1c0 1 1 2 2 3 0 2 0 3 1 4-1 1-1 2-1 3h0v2c-3-5-7-8-9-13z" class="B"></path><path d="M190 158l1 1c0 1 1 2 2 3 0 2 0 3 1 4-1 1-1 2-1 3h0c-1-1-2-3-3-4s-3-3-3-5c0 1 4 4 5 5h0l-2-3 1-1c-1-1-2-1-3-1h1v-1l1-1z" class="F"></path><path d="M194 159v1h2c1-1 2-1 3-1h0v1c-1 2-1 4-2 6 1 0 1 0 1-1l1 1c0 1-1 2-1 3l-1 2h-2 0c-1 1-1 1-1 2l-1-2v-2h0c0-1 0-2 1-3-1-1-1-2-1-4-1-1-2-2-2-3h3 0z" class="c"></path><path d="M194 166l1 2h0l-1 1c1 0 2-1 3-2v1c-1 1-2 2-2 3-1 1-1 1-1 2l-1-2v-2h0c0-1 0-2 1-3z" class="J"></path><path d="M194 159v1h2c1-1 2-1 3-1h0v1 1c-2 0-2 1-3 2-1-1-1-2-2-2v1h-1c-1-1-2-2-2-3h3 0z" class="d"></path><path d="M187 171l-1-4c1 1 4 3 4 4 2 1 3 2 4 4v-1c1 0 1 1 1 2l1 1 1 6-1 2c-1 3-2 5-3 7h0l-1-1 1-1c0-2-2-4-2-6-1-2-1-3-2-5s-2-3-2-5v-3z" class="D"></path><path d="M187 171l-1-4c1 1 4 3 4 4v1c0 2 2 5 2 8-1-1-2-4-3-6 0 0 0-1-1-1 0-1 0-2-1-2z" class="E"></path><path d="M190 171c2 1 3 2 4 4 0 3 1 6 0 8v1c-1-1-1-3-2-4 0-3-2-6-2-8v-1z" class="J"></path><path d="M212 165c0-2-1-7 0-9h2c1 1 2 1 4 1 1 0 2 1 3 1 0 0 1 1 1 0v1c2 0 3 1 4 1 2 1 4 1 6 2l-1 8v4h0-2c-2 0-3 2-4 2-1 1-2 1-2 2h0-2c-2 2-5 5-8 6-1 1-1 1-2 1h-1v-1-4l2-11v-2-2z" class="n"></path><path d="M222 159c2 0 3 1 4 1 2 1 4 1 6 2l-1 8v4h0-2c-2 0-3 2-4 2-1 1-2 1-2 2h0-2c1-1 2-3 3-3h1l-1-1-1 1v-1l1-1c1 0 1-1 1-2v-3c-1 0-1-1-2-1v-2h0v1h1c0-1 1-1 1-2 0 0 0-1 1-1h0v-1c0-1-1-1-1-1-2-1-2-1-3-2z" class="f"></path><path d="M222 159c2 0 3 1 4 1 1 1 1 3 0 5v1c-1 0-1 0-1 1l1-1s1 1 2 1c1 1 1 2 3 3v4h0-2 0l1-1h0c0-1 1-2 0-2l-1-1s-1-2-2-2l-1 1h-1c0-1-1-2-1-3s1-1 1-2c0 0 0-1 1-1h0v-1c0-1-1-1-1-1-2-1-2-1-3-2z" class="H"></path><path d="M224 173c1 0 1-1 1-2v-3c-1 0-1-1-2-1v-2h0v1h1c0 1 1 2 1 3h1l1-1c1 0 2 2 2 2l1 1c1 0 0 1 0 2h0l-1 1h0c-2 0-3 2-4 2-1 1-2 1-2 2h0-2c1-1 2-3 3-3h1l-1-1-1 1v-1l1-1z" class="h"></path><path d="M212 167h0v2h1c1 0 1-1 1-1v-1h2c1 0 1 0 2 1 0-1 1-1 2-1 0 1 1 1 1 2l2 2v2h1v-2 2l-1 1v1l1-1 1 1h-1c-1 0-2 2-3 3-2 2-5 5-8 6-1 1-1 1-2 1h-1v-1-4l2-11v-2z" class="l"></path><path d="M210 180l2-11c0 2-1 4 0 5l1 1h0 1l1 1c-1 0-2 1-3 2s-1 4-2 6v-4zm11-11l2 2v2h1v-2 2l-1 1v1l1-1 1 1h-1c-1 0-2 2-3 3-2 2-5 5-8 6h0c0-1 3-3 5-4 0 0 1-1 1-2h0l4-4-2-5z" class="m"></path><path d="M231 174v12l-1 21h-3c1 1 2 1 3 1l-1 1-1 1h-8c-2 0-7 0-9-1h-2v-2h-1l1-6v-4-4l1-4v-3-1h0 1c1 0 1 0 2-1 3-1 6-4 8-6h2 0c0-1 1-1 2-2 1 0 2-2 4-2h2z" class="h"></path><path d="M225 192h4 0c-1 1-3 1-4 2v1l-1 1c-1-1-3-2-4-3h1c1-1 2-1 4-1z" class="H"></path><path d="M211 207h12 4c1 1 2 1 3 1l-1 1-1 1h-8c-2 0-7 0-9-1h-2v-2h2z" class="C"></path><path d="M211 196l1-2v1h1c1-1 3-1 4-1h2c1 1 0 3 0 4v2l2 1v1c-1 1-1 1-2 1-1 1-1 3-2 4h-6-2-1l1-6v-4l1-1h1z" class="m"></path><path d="M211 196c2 0 4-1 6-2v2c0 2-2 3-2 4-1 1-1 2-1 2-1 1-1 1-2 0-1 0-1 0-2-1 0-2-1-2 0-4v-1h1z" class="l"></path><defs><linearGradient id="AN" x1="217.663" y1="178.696" x2="222.914" y2="191.657" xlink:href="#B"><stop offset="0" stop-color="#b5b3b4"></stop><stop offset="1" stop-color="#d5d5d5"></stop></linearGradient></defs><path fill="url(#AN)" d="M231 174v12h-1v2 2c0 1 0 1-1 2h-4c-2 0-3 0-4 1h-1v1l1 1c-1 1-1 3-2 3 0-1 1-3 0-4h-2c-1 0-3 0-4 1h-1v-1l-1 2h-1l-1 1v-4l1-4v-3-1h0 1c1 0 1 0 2-1 3-1 6-4 8-6h2 0c0-1 1-1 2-2 1 0 2-2 4-2h2z"></path><path d="M225 192l-1-2 1-1c1-1 2-1 3-1s2 1 2 2 0 1-1 2h-4z" class="k"></path><path d="M221 178h2c-1 0-2 0-2 1v1s-1 0-1 1c1 0 1 1 1 1l1 2h1c0-1 1 0 2-1v1h-2v2h-1-1c-1 0-1 1-2 1-1 1-2 1-4 2 0 0-2 0-2 1-1 1-3 2-4 3l1-4v-3-1h0 1c1 0 1 0 2-1 3-1 6-4 8-6z" class="H"></path><path d="M260 136c2 1 5 2 7 3 3 2 4 4 5 7v3l1 15v3h0c-1 3 0 7 0 10v18c0 5-1 10 0 14h3 4 2c-1 1-1 1-2 1v6 15 17h0 0c-2 1-5 1-7 1l-1 11 1 50-1 2 2 2c1 0 2 0 3 1-2 1-4 0-6 0-3 6-6 10-10 15-1 1-4 3-5 4h-2 0v-2c-1 0-2 1-3 1v-1h-1c-1 0-3-1-4-1s-2 0-3-1c0-1-3-1-4-2 0-1 0-1 1-2h0v-2c0-1-1-1-1-2l1-1h0l-1 1v-7l-1-2h0v-44-10c-1-3 0-6 0-9 0-9 0-18-1-26 1-2 0-5 0-7v-18c1-2 0-5 0-8v-19-6l1-1c-1-6-1-13-1-19 0-2 1-6 0-7h0c0 1 1 3 1 4v5c1 4-1 8 1 11 0 1-1 2 0 3v2 6h0c0-2 0-6 1-8h1 0c-1-1-1-1-2-1v-4l1 3c1 1 3 1 5 1h5 6c1-1 2-1 3-2l-1-12c0-1 0-1-1-1l-3-2h-2c2 0 4 1 6 1v-5-4h2z" class="o"></path><path d="M268 140v1c-1 1-2 1-3 1h-2c2-1 2-2 5-2z" class="J"></path><path d="M267 179h1 0v1c-1 1-2 2-3 2l-1-1v-1c1 0 2 0 3-1h0z" class="F"></path><path d="M240 215v-5h1c0 2 1 3 0 5h-1z" class="c"></path><path d="M256 258c3-1 6-1 9-1l-1 1v1h-1 0l-1-1h-6zm9-79h-1-1v-5l3-2c1 1-1 1-1 2h1v1h0c-1 1-1 2-2 2v2h1z" class="T"></path><path d="M266 175h1v2l-2 2h-1v-2c1 0 1-1 2-2h0z" class="D"></path><path d="M241 210h2 0v6h-2l-1-1h1c1-2 0-3 0-5z" class="T"></path><path d="M270 193c1 1 1 1 1 2h0c0 2-4 5-5 6v-1c1-1 3-2 3-4h0c-1 0-1 0-2 1h0c1-1 2-3 3-4z" class="D"></path><path d="M241 217h1c0 1-1 1-1 2h0v6 1l1-1h0v6h0c0 2 0 4-1 5v1h1-1-1v-14-5l1-1z" class="a"></path><path d="M273 248c-1 0-1 0-2-1-2-2-1-9-1-11v-1c1 1 1 1 2 1h1v12zm-31 25h0c0-2 2-3 4-5 1 0 2-1 3-2-1-2-2-2-3-3v-1h1l3 3v2l-2 4v1l-1 3h-1l-4-2z" class="C"></path><path d="M268 168c2 0 2 0 3 1v1l-1-1c-1 4-1 7 0 10v6h0v-6-2c1-1 1-1 1-2 2 3-1 9 0 12h0 0c-2 2-4 3-7 5 1-3 5-4 6-6-2-2 0-5-1-7s-1-5 0-7v-2h0l-6 1v-1h0c1-1 3-1 5-2z" class="D"></path><path d="M241 255h12c-3 1-5 2-7 4v1h-1c-1 3-3 5-5 7 0-4 1-8 1-12z" class="G"></path><path d="M256 258h6l1 1h0v1c0 2 1 3 0 5h-5-1l1 1c-1 0-2 1-3 1-1-1-1 0-2 0-1-1-2-1-3-2 1-1 0-3 0-5 1 0 2 0 3-1h0l3-1z" class="D"></path><path d="M261 259c-1 2-3 4-3 6h-1-1c-1 0-2-1-2-2h2l-1-2h1c1 0 4-1 5-2z" class="K"></path><path d="M261 259h1 1 0v1c0 2 1 3 0 5h-5c0-2 2-4 3-6z" class="L"></path><path d="M250 265c1-1 0-3 0-5 1 0 2 0 3-1h0c-1 2 0 2 0 4h1c0 1 1 2 2 2h1l1 1c-1 0-2 1-3 1-1-1-1 0-2 0-1-1-2-1-3-2zm-3-55h3 1 2c1 0 3 0 4 1v1h0c1 1 1 4 0 5 1 0 1 0 1 1l-16-1h-1c1 0 1 0 2-1h0v-6h0 1 2 1z" class="C"></path><path d="M247 210c0 2 0 5-1 6h0c-1-2 0-4 0-6h1z" class="N"></path><path d="M251 210l1 1c0 1 0 3 1 4-1 0-1 1-1 1h-1c0-2-1-4 0-6z" class="B"></path><path d="M243 210h1c0 2 1 5 0 6h0-1 0v-6h0z" class="c"></path><path d="M251 210h2c1 0 2 0 2 1 1 1 1 3 1 4l-1 1h-1l-1-1h0c-1-1-1-3-1-4l-1-1h0z" class="G"></path><defs><linearGradient id="AO" x1="264.487" y1="248.418" x2="240.536" y2="245.089" xlink:href="#B"><stop offset="0" stop-color="#717071"></stop><stop offset="1" stop-color="#8e8c8d"></stop></linearGradient></defs><path fill="url(#AO)" d="M241 241l24 1v3 7h-24v-11z"></path><path d="M248 245c1 0 2 1 3 1v1c0 1 0 1-1 2-1-1-2-1-2-1h-1c0 1-1 1-2 2v-1-2c1-2 2-2 3-2z" class="I"></path><path d="M273 209h3 4 2c-1 1-1 1-2 1v6 15 17h0-7v-12-26-1z" class="a"></path><path d="M276 222h1l1 1v2h-2v-3z" class="U"></path><path d="M273 209h3 4 2c-1 1-1 1-2 1h-7v-1z" class="L"></path><path d="M267 139c3 2 4 4 5 7v3l1 15v3h0c-1 3 0 7 0 10v18c-1 0-1 0-1-1l-1 1c0-1 0-1-1-2 1 0 2 0 2-1v-2-2h0s0-1-1-1c-1-3 2-9 0-12 0 1 0 1-1 2v2 6h0v-6c-1-3-1-6 0-10l1 1v-1c-1-1-1-1-3-1h-2 0-2c-2-5-2-12-2-18-1-1-1-2-1-4s1-3 2-4h2c1 0 2 0 3-1v-1l-1-1z" class="T"></path><path d="M270 166v-2h1v1l-1 1z" class="F"></path><path d="M271 164h2v3l-3-1 1-1v-1z" class="D"></path><path d="M262 150c-1-1-1-2-1-4s1-3 2-4h2c-1 2-3 3-3 5 2 1 3 4 4 5l1-1v-1h1c0 1 1 2 1 3s-1 2-1 3c-1 2-1 4-1 6l-1 1v-1c-1-1-1-2-2-3-1-3-1-6-2-9z" class="F"></path><defs><linearGradient id="AP" x1="265.066" y1="229.799" x2="239.59" y2="224.537" xlink:href="#B"><stop offset="0" stop-color="#767676"></stop><stop offset="1" stop-color="#bdbbbc"></stop></linearGradient></defs><path fill="url(#AP)" d="M242 217l16 1h5c1 2 2 7 1 9v8l1 2c-2 1-20 1-23 0h-1v-1c1-1 1-3 1-5h0v-6h0l-1 1v-1-6h0c0-1 1-1 1-2z"></path><path d="M264 227v8-1c-1-1 0-1 0-2v-5z" class="Y"></path><path d="M253 224h0v2c0 1-1 1-2 2v2 1l-1 1c0-1 0-1 1-2-1-1 0-2-1-3 0-1 2-3 3-3z" class="a"></path><path d="M242 225v-1h0v-2c0-1 0-1 1-2h0c2 1 2 1 3 2-1 1-1 2-2 2h-1c0 1-1 1-1 1h0z" class="V"></path><path d="M242 225s1 0 1-1c1 0 2 1 2 1h1c0 1 1 1 1 2 1 1 1 5 1 7-1 1-1 1-1 2-2-1-3-1-5-1v1 1h-1v-1c1-1 1-3 1-5h0v-6z" class="f"></path><defs><linearGradient id="AQ" x1="259.589" y1="183.975" x2="238.636" y2="185.979" xlink:href="#B"><stop offset="0" stop-color="#7e7d7e"></stop><stop offset="1" stop-color="#b5b4b5"></stop></linearGradient></defs><path fill="url(#AQ)" d="M239 157l1 3c1 1 3 1 5 1h5 6c1-1 2-1 3-2v2l3 47h-7c-4 0-10 0-14-1h-1v-28l-1-9c0-2 0-6 1-8h1 0c-1-1-1-1-2-1v-4z"></path><path d="M241 207h7c0-1 0-3 1-4h1 0c0 2-2 2 0 4h5v1c-4 0-10 0-14-1z" class="M"></path><path d="M258 161h1l3 47h-7v-1h4c1-1 1-1 1-2l1-2c0-2 0-2-1-3h-2 0c-1-1-1-2 0-3v1s1-3 1-4c0-2-2-3-1-6 0 0 0-1-1-1h-1c0-1-1-1-1-2l1-1 1 1c0-3 0-4 1-6-1 0-1-1-1-1v-7-2c1-2 1-7 1-8z" class="I"></path><defs><linearGradient id="AR" x1="252.037" y1="168.284" x2="236.696" y2="179.358" xlink:href="#B"><stop offset="0" stop-color="#a09e9f"></stop><stop offset="1" stop-color="#bcbbbc"></stop></linearGradient></defs><path fill="url(#AR)" d="M239 157l1 3c1 1 3 1 5 1h5 6c1-1 2-1 3-2v2h-1c-2 0-3 0-4 1 0 1-1 3-1 5 0 0-1 0-1 1h-1v3l-1 1c0 1 0 1-1 1h0c1 1 1 2 3 2h0v1 1h2c0 1-2 1-2 2v1 1c-1 0-1 1-1 2v1h-1-1c-1 0-1-1-2-1v2h-3 0-3 0l-1-1c1 0 1-1 0-1v-4h0l-1-9c0-2 0-6 1-8h1 0c-1-1-1-1-2-1v-4z"></path><path d="M244 185h1c0-1 1-1 1-2 0 0 0-1 1-1 0 0 1 1 2 1l1 1h-1c-1 0-1-1-2-1v2h-3 0z" class="a"></path><path d="M239 157l1 3c1 1 3 1 5 1h5 6c1-1 2-1 3-2v2h-1c-2 0-3 0-4 1 0 1-1 3-1 5 0 0-1 0-1 1-1-1-1-2-2-3h-1c-2-1-2-1-3-3h-6 1 0c-1-1-1-1-2-1v-4z" class="V"></path><path d="M239 157l1 3c1 1 3 1 5 1h5 6c1-1 2-1 3-2v2h-1c-2 0-3 0-4 1-4 0-9 1-13 0h0c-1-1-1-1-2-1v-4z" class="b"></path><path d="M251 183c-1-1-1-1-2-1-1-1 1-2 1-3v-1l-1 1c1-1 1-1 1-2-1-1-2 0-3-1 0 0-1-1-1-2h0v-4h2v-1c1 0 2 0 3-1v3l-1 1c0 1 0 1-1 1h0c1 1 1 2 3 2h0v1 1h2c0 1-2 1-2 2v1 1c-1 0-1 1-1 2z" class="j"></path><path d="M265 257c2 0 3 2 4 4 2 1 2 3 3 5v-6h0l1 50-1 2 2 2c1 0 2 0 3 1-2 1-4 0-6 0-3 6-6 10-10 15-1 1-4 3-5 4h-2 0v-2c-1 0-2 1-3 1v-1h-1c-1 0-3-1-4-1s-2 0-3-1c0-1-3-1-4-2 0-1 0-1 1-2h0v-2c0-1-1-1-1-2l1-1h0l-1 1v-7l-1-2h0v-44-10h0c1 3 1 7 1 9-1 1 0 1 0 2h0v-2-1h1 0c0 2 0 4-1 6v1 2 1c1 0 1 0 1 1h3 1c1 1 2 1 2 2l1-1-4-1v-1l-1-1c-1-1-1-2 0-3l4 2h1l1-3v-1l2-4v-2h0c1 1 2 1 3 2 1 0 1-1 2 0 1 0 2-1 3-1l-1-1h1 5c1-2 0-3 0-5v-1h1v-1l1-1z" class="C"></path><path d="M243 295h0l-1 3h0l-1-1 2-2z" class="F"></path><path d="M243 284h1 0v2 2h-1 0v-4z" class="G"></path><path d="M266 302v1c1 1 1 2 2 3h-1l-1 1v3c0-2 0-4-1-6l1-2z" class="B"></path><path d="M263 260c0 1 1 1 1 1v1c0 1 1 2 1 3v1l1 1s-1 0-1-1l-2-1c1-2 0-3 0-5z" class="K"></path><path d="M261 323c0-4 1-5 3-8 0 3-1 5-3 8zm-19-4v-2h1s0 1 1 1c1-1-1-2 0-4 1 0 2 1 3 0h0v1h-3c1 2 1 2 1 4h-3z" class="F"></path><path d="M249 329h-1c-1 0-2-1-2-1 1-2 3-3 5-4-1 1-1 3-2 4v1z" class="B"></path><path d="M262 292c2 3 3 7 4 10l-1 2c0-1-1-2-1-4l1 1h0c-1-2-2-4-2-5h0c-1-1-1-3-1-4zm8 8c1 2 0 7 0 9 0 1-1 1-2 2 0-2 0-3-1-5h1l1 1c0 1 0 1 1 1h0c0-1-1-1 0-2v-6z" class="E"></path><path d="M251 324c1-1 1-2 2-2s1 1 2 2c-2 2-4 3-6 5v-1c1-1 1-3 2-4h0z" class="Y"></path><path d="M265 257c2 0 3 2 4 4l3 6c-1 0-1 0-2-1-2-2-5-5-6-8l1-1z" class="F"></path><path d="M257 297c0-4-1-6-3-9-1-1-2-3-2-4 4 4 9 12 9 18 1 3 1 6 0 8-1-2 0-4-1-6l-1-1v3c0-3 0-6-2-9z" class="I"></path><path d="M241 279h0 2c1 0 3 1 4 1v3 1h1c2 2 4 3 4 6-2 0-2-1-3-2l-1-1-1-1c-1-2-2-2-3-2h-1c-2-1-2-1-3-2s-1-1-1-2l2-1z" class="g"></path><path d="M241 279h0 2c1 0 3 1 4 1v3c-3-1-5-1-6-4z" class="K"></path><path d="M266 310v-3l1-1c1 2 1 3 1 5-2 7-6 13-11 18-1 1-2 1-3 3-1 0-2 1-3 1v-1c4-1 7-6 10-9 2-3 3-5 3-8l2-5z" class="X"></path><path d="M259 306v-3l1 1c1 2 0 4 1 6-1 5-3 10-6 14-1-1-1-2-2-2 3-5 5-10 6-16z" class="Z"></path><path d="M272 312l2 2c1 0 2 0 3 1-2 1-4 0-6 0-3 6-6 10-10 15-1 1-4 3-5 4h-2 0v-2c1-2 2-2 3-3h0c2 0 3-3 5-4h1 0c0-1 2-3 3-3 0 0 1-1 1-2l5-8z" class="U"></path><path d="M257 329h0c2 0 3-3 5-4h1 0c0-1 2-3 3-3-2 3-5 6-7 9-2 1-4 2-5 3h0v-2c1-2 2-2 3-3z" class="E"></path><path d="M250 265h0c1 1 2 1 3 2l1 1h0c-1 2-2 3-2 5s-1 4-1 6l-1 1c0 2-1 3-1 4 1 1 2 2 3 4 0 0 0 1 1 1v2h0c2 1 2 3 3 4 0 1 1 2 1 3h-1c0-1-1-3-2-5h0s0-1-1-1c0 0-1 1-1 2l-4-5 1-1c1 1 1 2 3 2 0-3-2-4-4-6h-1v-1-3h0v-1l-4-1v-1l-1-1c-1-1-1-2 0-3l4 2h1l1-3v-1l2-4v-2z" class="D"></path><path d="M248 275v3c1 0 2 1 3 1l-1 1-1 2h-1v-7z" class="K"></path><path d="M242 273l4 2c1 1 1 1 1 2l-1 1-3-1-1-1c-1-1-1-2 0-3z" class="P"></path><path d="M250 265h0c1 1 2 1 3 2l1 1h0c-1 2-2 3-2 5s-1 4-1 6c-1 0-2-1-3-1v-3-3-1l2-4v-2z" class="B"></path><path d="M250 265h0c1 1 2 1 3 2l1 1h0c-1 0-2 0-3 1 0 1-1 2 0 3v1l-1 1c-1 0-1 0-2-1v-2l2-4v-2z" class="P"></path><defs><linearGradient id="AS" x1="261.037" y1="290.5" x2="265.357" y2="287.159" xlink:href="#B"><stop offset="0" stop-color="#4c4c4b"></stop><stop offset="1" stop-color="#777778"></stop></linearGradient></defs><path fill="url(#AS)" d="M253 281c0-1-1-1-1-2 1-1 1-2 1-3 3 0 5 2 7 4 6 5 9 12 10 20v6c-1 1 0 1 0 2h0c-1 0-1 0-1-1l-1-1c-1-1-1-2-2-3v-1c-1-3-2-7-4-10-1-1-2-3-3-5l-1-1-2-3-3-2z"></path><path d="M267 297c1 3 2 7 2 10l-1-1c-1-1-1-2-2-3 1-1 1-5 1-6z" class="M"></path><path d="M253 281c1-2 2-2 1-4h1l1 2v1 3l-3-2z" class="Z"></path><defs><linearGradient id="AT" x1="259.109" y1="294.568" x2="264.961" y2="288.158" xlink:href="#B"><stop offset="0" stop-color="#909192"></stop><stop offset="1" stop-color="#b0aeaf"></stop></linearGradient></defs><path fill="url(#AT)" d="M256 283v-3-1c1 0 2 2 2 2 4 5 8 10 9 16 0 1 0 5-1 6v-1c-1-3-2-7-4-10-1-1-2-3-3-5l-1-1-2-3z"></path><path d="M258 284l2 2-1 1-1-1v-2z" class="Q"></path><path d="M256 283v-3-1c1 0 2 2 2 2h-1c0 1 1 2 1 3v2l-2-3z" class="V"></path><path d="M258 265h5l2 1-1 1c2 2 6 5 7 8h-1l-1-1v2l2 4-3-3v1c1 1 1 1 1 2 1 4 1 7 2 11l-4-8c-2-3-3-6-7-8h0c-1 1-1 1-3 0v1c-1-1-1-1-2-1s-2-1-3-2c0-2 1-3 2-5h0l-1-1c1 0 1-1 2 0 1 0 2-1 3-1l-1-1h1z" class="K"></path><path d="M258 273c-2 0-2 0-3-1l2-2c0 1 1 2 3 3h0 0-2z" class="D"></path><path d="M254 268c1 0 1-1 1 0h2c1 0 2 1 3 1 2 2 7 3 9 5h0v2c-3-3-5-3-8-5-2 0-3-1-4-1l-2 2c1 1 1 1 3 1l2 2c-1 1-1 1-3 0v1c-1-1-1-1-2-1s-2-1-3-2c0-2 1-3 2-5h0z" class="T"></path><path d="M258 265h5l2 1-1 1c2 2 6 5 7 8h-1l-1-1h0c-2-2-7-3-9-5-1 0-2-1-3-1h-2c0-1 0 0-1 0l-1-1c1 0 1-1 2 0 1 0 2-1 3-1l-1-1h1z" class="K"></path><path d="M258 265h5l2 1-1 1c-2 0-4-1-6-1l-1-1h1zm-8 32h0c0-3-1-5-2-7 1 2 2 3 3 5h0 1v-1c0-1 1-2 1-2 1 0 1 1 1 1h0c1 2 2 4 2 5h1v1 1-2-1c2 3 2 6 2 9-1 6-3 11-6 16-1 0-1 1-2 2 1-2 1-3 2-4l1-1c-1 1-2 1-2 1h-1c-2 2-3 3-5 4h0c-1 1-2 1-3 1h0c0-1 1-1 2-2 0-1-1-1-1-2-1 0-1-1-2-2h3c0-2 0-2-1-4h3v-1h0c-1-1-4-3-4-5 0-3 1-5 3-6 1-2 3-4 4-6z" class="E"></path><path d="M255 303v1-2s0 1-1 1l-1-4 1-3 1 7z" class="D"></path><path d="M247 307l1 1v2 1c-2-1-2-2-3-3l2-1z" class="F"></path><path d="M242 319h3c1 0 2 0 3 1-1 1-3 1-4 1s-1-1-2-2z" class="D"></path><path d="M256 307c0 1 1 1 1 2-1 2-1 4-2 6h-1c1-3 1-5 2-8z" class="P"></path><path d="M246 324l-1-1c1-1 2-1 3-2h1c1-1 3-3 3-4 0 0-1 0-1-1v-1h1c1 1 1 1 2 1v1l-3 3c-2 2-3 3-5 4zm8-31c1 2 2 4 2 5 1 3 2 8 1 11 0-1-1-1-1-2l-1-4-1-7v-3z" class="B"></path><path d="M257 297c2 3 2 6 2 9-1 6-3 11-6 16-1 0-1 1-2 2 1-2 1-3 2-4l1-1c-1 1-2 1-2 1h-1l3-3v-1c-1 0-1 0-2-1h2 1c1-2 1-4 2-6 1-3 0-8-1-11h1v1 1-2-1z" class="J"></path><path d="M247 314c-1-1-4-3-4-5 0-3 1-5 3-6-1 2-1 2-1 5 1 1 1 2 3 3h0 2c1-1 2-2 2-3h1 1c-1 1-1 1-1 2-1 1 0 2-1 4h-4-1 0z" class="K"></path><path d="M250 297c1 1 2 6 2 8 0 1-1 2-2 3h-1v-1c0-1-1-1-2-1v1l-2 1c0-3 0-3 1-5s3-4 4-6z" class="L"></path><path d="M247 306c1 0 1-1 1-1 0-1 1-2 2-2h0c1 1 1 1 1 2h0l-2 2c0-1-1-1-2-1z" class="K"></path></svg>