<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="59 52 655 620"><!--oldViewBox="0 0 760 752"--><style>.B{fill:#a7a6a7}.C{fill:#bbb9ba}.D{fill:#c9c8c9}.E{fill:#d4d3d4}.F{fill:#b0afb0}.G{fill:#878687}.H{fill:#9a9999}.I{fill:#797979}.J{fill:#e6e5e5}.K{fill:#d0cfcf}.L{fill:#a1a0a1}.M{fill:#adadac}.N{fill:#919091}.O{fill:#b5b4b4}.P{fill:#c5c4c4}.Q{fill:#8d8c8c}.R{fill:#838182}.S{fill:#959495}.T{fill:#9e9d9e}.U{fill:#c0bfbe}.V{fill:#343334}.W{fill:#3f3f40}.X{fill:#c2c0c2}.Y{fill:#eee}.Z{fill:#5c5b5b}.a{fill:#626161}.b{fill:#dedcde}.c{fill:#4b4a4a}.d{fill:#020202}.e{fill:#2c2c2c}.f{fill:#d9d8d9}.g{fill:#757475}.h{fill:#696869}.i{fill:#454445}.j{fill:#6d6c6c}.k{fill:#515051}.l{fill:#7e7d7d}.m{fill:#717070}.n{fill:#202120}.o{fill:#e2e2e1}.p{fill:#666}.q{fill:#2b2b2b}.r{fill:#393939}.s{fill:#272626}.t{fill:#575656}.u{fill:#1c1c1c}.v{fill:#191818}.w{fill:#f8f7f8}.x{fill:#0e0d0d}</style><path d="M129 589l-1-1h4l2 1h-5z" class="D"></path><path d="M208 407h2l2-1c-1 1 0 2-1 2s-1 0-1 1c-1 0-1 1-2 1v-1-2z" class="w"></path><path d="M666 639c1-1 3-1 4 0h0c-1 1-2 1-3 1l-1-1h0z" class="b"></path><path d="M689 611c2 0 4 0 5 1h-7v-1h-1 3z" class="K"></path><path d="M659 639h7 0l1 1h-7 0 2v-1h-3z" class="D"></path><path d="M570 655h6c1 0 0 0 1 1h-5-6c1-1 3-1 4-1z" class="Y"></path><path d="M180 605c1 0 3 0 4 1h0c-1 1-2 1-3 1l-2-1 1-1z" class="P"></path><path d="M602 551c1 0 1 1 2 1h2l3 4-1-1c-1-1-1-1-3-1 0 0-3-2-3-3z" class="Z"></path><path d="M677 636h1 5c1 0 3 0 4 1h0-5-9-1l5-1z" class="J"></path><path d="M65 589h6c1 2 5 1 7 2H63l2-2z" class="U"></path><path d="M537 648l11-1h0l-4 2h-6 2c-1 0-2-1-3-1h0z" class="J"></path><path d="M94 567h7 2c-2 1-5 0-7 0l-1 1h-2-4c2-1 3-1 5-1z" class="f"></path><path d="M59 569c2 0 4-2 6-2l-1 3h-6l1-1z" class="X"></path><path d="M61 589h4l-2 2h0c-3 0-7 0-9-1 2 0 4 0 7-1h0z" class="b"></path><path d="M142 594l9 1h0c-1 1-2 1-4 1h-1c-1 0-1-1-2-1h-1 1 0s-1-1-2-1zm-51-20l6 1v1h-7v-1c-1-1-4 0-6 0 2-1 5-1 7-1z" class="F"></path><path d="M161 595c2-1 5-1 8-1h3c1 1 1 1 2 1s1 0 2 1h-10c-1-1-3-1-5-1z" class="K"></path><path d="M476 650h7 0l3 1h0-2c-3 0-9 1-11-1h3z" class="J"></path><path d="M605 554c2 0 2 0 3 1l1 1 5 3 1 2c-2-1-4-3-6-3-2-1-3-3-4-4z" class="c"></path><path d="M148 589c2 0 3 0 5 1h1l1 1h-2l-9-1v-1h3 1z" class="G"></path><path d="M677 614c3 1 6 1 9 1v1c-3 0-5 1-8 1-1-1-1-1-2-3h1z" class="C"></path><path d="M709 530c0 1 1 1 1 2 0 4-3 7-4 10l1-5 2-7z" class="P"></path><path d="M545 603c4-2 10-2 14-1 1 0 1 0 1 1h-3-11-1z" class="L"></path><path d="M53 572c-4 0-8-1-12-2h6l12-1-1 1h0c-3 0-6-1-8 0h-1c1 1 3 1 4 1v1z" class="o"></path><path d="M544 604l1-1h1 11v1h-4c-2 0-4 1-6 1h0-1v-1h-2z" class="b"></path><path d="M157 589c2 0 4 1 6 1 1 1 2 1 4 2l-14-1h2l-1-1c1 0 2-1 3-1z" class="B"></path><path d="M154 590c1 0 2-1 3-1l1 1v1h-3l-1-1z" class="T"></path><path d="M126 574v1c-2 1-4 1-5 1h-1-1-1-5v1c-2-1-3-1-4 0h0c-1 0-3-1-4-1h8c4 0 9-1 13-2z" class="J"></path><path d="M569 557c0 4 1 7 3 10 0 1 0 1 1 2l-2-2v-1c-1 1-1 2-1 3h-1v3-3c-1-4 0-8 0-12z" class="b"></path><path d="M614 559c3 1 6 3 9 4 0 1 1 2 1 2l1 1h1l-9-3c0-1-2-2-2-2l-1-2z" class="Z"></path><path d="M570 649h-2 0c-2 0-3 0-4 1-5 1-11 0-16 0h0l1-1c3 1 9 1 12 0 2 0 4-1 6-1h1 1l1 1z" class="Y"></path><path d="M604 646c5 0 11 1 15 0 2 1 5 0 7 1 1 0 3 0 5 1h-8-2v-1h-3c-4-1-9 0-13 0l-1-1z" class="J"></path><path d="M674 552l9-13c0 4-4 9-7 13h-2z" class="i"></path><path d="M95 602h3v-1c1-1 2-1 3-1 5-1 9-1 13-1l-3 1h-1c-1 1-2 1-3 1l-12 1z" class="o"></path><path d="M707 490l1 1 1-2h0c1 5 2 9 1 14l-2-11-1-2z" class="K"></path><path d="M73 565h3 2 1-1 1c2 0 4 1 6 1 3 0 6 0 9 1-2 0-3 0-5 1l-14-2-2-1z" class="J"></path><path d="M666 593l8-4c0 1 0 2-1 3-2 2-5 2-7 3v-1-1z" class="X"></path><path d="M424 649h0c3-1 5-1 8-1s10 0 13 2h-5-10c-2 0-4 0-6-1z" class="Y"></path><path d="M512 629v-1h7c4 1 8 0 12 0 0 1 0 1-1 1l-2 1-16-1z" class="B"></path><path d="M145 563c8-1 14 0 22 2h1l-1 1-1-1-4-1c-4-1-10-1-14 0h-2 0 1c-1-1-1 0-2-1h0z" class="N"></path><path d="M145 588c1 0 2 0 4-1 1 0 4 0 5 1-2 0-5 0-6 1h-1-3v1h-4c-2 0-4 0-6-1l-2-1h7c1 0 2 1 4 0h1 1z" class="B"></path><path d="M129 589c-3 0-6 1-8 0h-5v-1h5c1 1 2 1 4 1l-1-1h-7v-1c2 1 4 1 5 0 3 0 6 1 9 1h13-1c-2 1-3 0-4 0h-7-4l1 1z" class="K"></path><path d="M702 476c3 5 5 9 5 14l1 2h-2v-1l-1-4c0-2-1-3-2-5 0-2-1-4-1-6z" class="B"></path><path d="M57 545l-6-1c6-1 12 0 18 1h4c1 0 1 1 2 1v1c-4-1-8-1-12-2-2-1-4 0-6 0z" class="C"></path><path d="M605 647c4 0 9-1 13 0h3v1h-8-14c1-1 4-1 6-1z" class="M"></path><path d="M618 647h3v1h-8-2l-1-1c1 0 3 1 3 0h1 4z" class="B"></path><path d="M568 643h-18v-1c7 0 13-1 21-1 0 1 1 1 2 1h0l-5 1z" class="C"></path><path d="M665 559v3l-7 3-6 2-1-1c5-2 9-4 14-7z" class="Z"></path><path d="M685 610h2l2 1h-3 1v1h-9-9c2-2 4-2 6-2h10z" class="b"></path><path d="M648 600c3-1 6-3 9-3h0c-2 1-3 2-5 2l-3 1c0 1-1 1-1 1h3 0c1 0 1 0 2-1h1c2 1 6-1 9 0v1h1 0l-6 1c0-1-2 0-2 0-3 0-6 0-9-1h0l1-1z" class="J"></path><path d="M141 598h0c4-1 10-1 14-1l-1 1c2 1 5 0 7 1-6 0-13 0-20 1h-1l1-2z" class="U"></path><path d="M65 554l19 1c0 1 1 2 2 2v1h-3-11 4c2-1 4-1 5-1h2l-3-2h-1l1 1-1 1-1-1v-1h-1v1 1l-1-1c1-1 1 0 0-1 0-1-2 0-3 0-2-1-5 0-8-1h0zm83 10c4-1 10-1 14 0l4 1-1 1h0l3 3h-1c-1 0-2 0-3-1l-4-1v-1s-1-1-2-1-1 1-1 1h-3l1-1h2-2c-2-1-4-1-7-1z" class="K"></path><path d="M160 566h3l1 1h0v1l-4-1v-1z" class="C"></path><path d="M126 574h3c1 0 1 0 2 1h-2v1 1l-1 1c-3 0-5 1-7 2-3 0-7 1-10 1 1-2 4-1 5-2v-1c1 0 1 0 2 1h0 1 0 1v-1s1 0 1-1l1 1-1 1c1 0 2-1 3-1s1 0 2-1l-1-1c-1 1-3 1-4 0 1 0 3 0 5-1v-1z" class="K"></path><path d="M531 639h5c5-1 10-2 15-1h-3v1c1 1 2 1 4 1-3 0-5 0-7 1v-1c1 0 0 0 1-1h-1-2v1h-1-5c0 1-1 1-2 1h-4v-1l1-1h-1z" class="E"></path><path d="M181 367c1-3 3-7 2-11 0-3 0-7 1-10 0-1-1-3 0-4l1 1c0 5-2 11 0 16l1 4h0l-1-1h-1l-2 6c-1 0-1 0-1-1z" class="S"></path><path d="M569 635h3 2 2c1 1 2 1 3 1l-15 2c-2 0-4 0-6 1h-5l-1 1c-2 0-3 0-4-1v-1h3c6 0 11 0 17-1l2-1c1 0 1 0 1-1h-2z" class="U"></path><path d="M151 595h10c2 0 4 0 5 1l-11 1c-4 0-10 0-14 1h0c-1 0-3 1-5 1v-1h1l5-1c1 0 3 0 4-1h1c2 0 3 0 4-1z" class="H"></path><defs><linearGradient id="A" x1="635.819" y1="570.39" x2="626.301" y2="562.4" xlink:href="#B"><stop offset="0" stop-color="#444244"></stop><stop offset="1" stop-color="#656664"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M623 563c6 2 11 4 17 4h5 0c-2 1-3 1-5 1l-1 1h1c-1 1-1 0-2 0h-2 0c-4 0-7-2-10-3h-1l-1-1s-1-1-1-2z"></path><path d="M187 589l5 1c3 0 7 1 11 1l-1 1c-2-1-7 0-10 0-2 1-4 0-6 0l-5 1c1 0 2-1 2-1l1-1c1-1 1-1 2-1l1-1z" class="K"></path><path d="M187 589l5 1h0c-1 1-2 1-3 1 0 0 0 1-1 1h-2l-5 1c1 0 2-1 2-1l1-1c1-1 1-1 2-1l1-1z" class="F"></path><defs><linearGradient id="C" x1="659.212" y1="634.929" x2="664.479" y2="628.55" xlink:href="#B"><stop offset="0" stop-color="#d1c7cc"></stop><stop offset="1" stop-color="#e0e6e3"></stop></linearGradient></defs><path fill="url(#C)" d="M665 631h4l7 1h1l1 1h-3l-12-1h-9c0-1-1 0-2-1 5-1 9 0 13 0z"></path><path d="M635 628s1 0 1 1h2 1 2c1 0 3 1 4 0h1v-1c3 1 6 1 8 2 3 1 8 0 11 1-4 0-8-1-13 0 1 1 2 0 2 1l-1 1c-2-1-4 0-6-1l1-1h-2l-11-2v-1z" class="Y"></path><path d="M646 631h6c1 1 2 0 2 1l-1 1c-2-1-4 0-6-1l1-1h-2z" class="L"></path><path d="M85 397c0-2 1-4 1-5 1-2 1-4 2-6 0 4 0 7 1 11 0 2 2 6 2 9h-1l-1-2h-1c-1-2-2-4-1-5v-2l-1 1c-1 0-1-1-1-1z" class="C"></path><path d="M68 567c1 0 1 0 1-1-4-1-8-1-12-1H44h3c3-1 6-1 9-1 6 0 11 0 17 1l2 1c1 0 1 1 1 1-1 1-1 1-2 1s-2-1-3-1h-3z" class="H"></path><path d="M633 631l14 1c2 1 4 0 6 1-3 0-6 1-9 1s-7-1-10-1h-8s1 0 1-1h1 1 1v-1h1 0 2z" class="C"></path><path d="M696 471c1-1 1-2 0-3 2 2 5 6 6 8 0 2 1 4 1 6 1 2 2 3 2 5l1 4-2 1-1-6c0-3-2-6-3-9l-4-6z" class="Q"></path><path d="M571 641l13 1c3 0 7-1 11-1 2 0 4 0 7-1l3-1c-1 1-1 2-2 2h0 1c2 1 4 0 6 0-4 1-8 1-12 1h-6l-10 1h-14l5-1h0c-1 0-2 0-2-1z" class="K"></path><path d="M129 598h1c1-1 1-1 2 0 0 0 1 0 1-1h1 2 2v-1h-6l-2-2-1 1h0-2c1-1 2-1 3-1h12 0c1 0 2 1 2 1h0-1 1c1 0 1 1 2 1-1 1-3 1-4 1l-5 1c-2 0-4 0-6 1l-2-1z" class="E"></path><path d="M142 594h0c1 0 2 1 2 1h0-1 1c1 0 1 1 2 1-1 1-3 1-4 1-1-1-1-1-2-1-1-1-2-1-2-2h4z" class="C"></path><path d="M665 559c3-2 6-4 9-7h2v1c0 1-1 1 0 2h2c-2 0-2 1-3 2-2 1-3 1-4 2-2 1-4 3-6 3v-3z" class="V"></path><path d="M689 462c3 3 5 5 7 9l4 6c1 3 3 6 3 9l-2-1c-1-2-1-3-2-4 0-1-1-2-1-3l-1-1c-1-2-2-2-2-4h0v-1c0-1-1-3-2-5-1-1-3-3-4-5z" class="W"></path><path d="M523 368c6 5 12 10 17 16h-1 0 0c-1-1-2-1-2-1-2-1-3-3-5-4 0-1-1-2-2-2l-7-7v-2z" class="T"></path><path d="M590 526l3 8c3 7 7 13 13 18h-2c-1 0-1-1-2-1-4-4-8-9-10-15-1-2-1-4-2-6v-4z" class="p"></path><path d="M68 504v2c1 2 0 3 0 5v1h-1c0 1-1 1-1 1v2c-1-1-1 1-2 1-2-2-2-7-2-10l1-1v1l1 1c0 1 0 1 1 2 1-1 0-2 0-3l1-1 1 1v2 1 2c1-2 1-3 1-5v-2z" class="Y"></path><path d="M66 513h-2l1-1v-2h0 1l1 2c0 1-1 1-1 1z" class="P"></path><path d="M103 567c7-1 14-2 20-2-1 1-2 1-3 1-2 2-6 2-9 2-5 1-11 1-16 0l1-1c2 0 5 1 7 0z" class="X"></path><path d="M67 560v-1c-4-1-9-1-13-2l18 1h11 3v1h-2c-4 1-9 1-13 2h-2v-1h-2z" class="j"></path><path d="M165 610h0l1 1h-1l-10 1-3 1c-3 1-7 1-10 1-3-1-16 0-17-1l2-1h8c2-1 4 0 6 0 1-1 2-1 3 0h1 0c2 1 3 0 5 0v1h1 1l2-1h1 2 0l1-1c-1 0-3 1-3 0h8l2-1z" class="K"></path><defs><linearGradient id="D" x1="674.2" y1="615.602" x2="682.298" y2="619.73" xlink:href="#B"><stop offset="0" stop-color="#c5c6c6"></stop><stop offset="1" stop-color="#eae8e9"></stop></linearGradient></defs><path fill="url(#D)" d="M686 615h3 2v1h-1l-1 1c-3 1-7 2-10 2-1 0-4 1-5 1v1h-1-3v-1c1-1 2-1 3 0h0v-1l-1-2h6c3 0 5-1 8-1v-1z"></path><path d="M537 383s1 0 2 1h0 0 1c6 8 12 14 16 23h-1c-1-1-3-3-3-4-3-4-6-11-11-14l-1-2c-1-1-2-3-3-4z" class="Q"></path><path d="M519 636h-14c1-1 3-1 5-3v1h1c2-2 9-1 12-2l3 1v2h-1 0c-2 1-4 1-6 1z" class="B"></path><path d="M519 636l1-2h3c0 1 1 1 2 1h0 0c-2 1-4 1-6 1z" class="M"></path><path d="M594 633c1 0 3 0 4-1l13 1c2 0 4 0 6 1-7 0-15 0-23 1-4 1-8 1-12 1 1-1 2-1 4-1 1 0 2 0 4-1 0 0 1 0 1-1h3z" class="o"></path><path d="M175 385c0-2 1-3 1-5 2-4 3-9 5-13 0 1 0 1 1 1 0 2-1 4-1 6-1 3-1 6-1 10h-2l-1 1h-2z" class="L"></path><path d="M177 385l-1-1h0l3-9v1c0 2 0 7-1 8l-1 1z" class="J"></path><path d="M531 633h2 5 1c4 0 7 0 10 1h4 8 0l-20 1c-5 0-11 1-16 0h0 1 3l2-2z" class="E"></path><path d="M529 635l2-2 1 1h0c1 0 2 0 3 1h0-6z" class="D"></path><path d="M520 368l3 2 7 7c1 0 2 1 2 2 2 1 3 3 5 4 1 1 2 3 3 4l-1 1h0l-2-1c-1-1-2-2-3-2 0-1-1-2-2-3 0 0-1-1-1-2h-2l-3-3v-1c2 1 3 3 5 4-2-4-6-7-9-9-1-1-2-2-2-3z" class="g"></path><path d="M532 379c2 1 3 3 5 4 1 1 2 3 3 4l-1 1h0l-2-1c-1-1-2-2-3-2 1 0 2 0 2-1l-4-5h0z" class="p"></path><path d="M83 581h24-1-5l9 1h0c-4 0-9-1-13 0l1 1-1 1h4-5l-1 1c-1-1-4-1-6-1s-2 0-4-1c0-1-1-1-2-2z" class="F"></path><defs><linearGradient id="E" x1="592.254" y1="650.264" x2="586.711" y2="644.022" xlink:href="#B"><stop offset="0" stop-color="#d8d1d5"></stop><stop offset="1" stop-color="#e0e4e0"></stop></linearGradient></defs><path fill="url(#E)" d="M569 648h1c2-1 4 0 6-1h8c1 0 2-1 3-1 6 0 11 1 17 0l1 1c-2 0-5 0-6 1h-7l-15 1c-2 0-5-1-7 0l-1-1z"></path><path d="M128 578l4-1v2h1l1 2h-4c-1 0-4 0-6 1h-1c-4-1-9 0-13 0l-9-1h5 1 4c3 0 7-1 10-1 2-1 4-2 7-2z" class="Q"></path><path d="M128 578l4-1v2c-1 1-3 1-5 1h-2-4c2-1 4-2 7-2zm543-125c5-1 13 5 17 8 1 0 1 1 1 1 1 2 3 4 4 5 1 2 2 4 2 5v1l-4-6c0-1-2-3-4-4s-4-3-7-5c-1-1-2-1-3-1l-6-3v-1z" class="M"></path><path d="M638 480c1 2 3 4 5 5h0 1c1 1 3 2 4 3 3-1 5-2 7-4-1 1-1 3-3 4-1 2-3 3-5 2-5 0-8-2-11-6 1 0 0 0 1-1 0 0 0-1-1-2l1 1 1 1c1 1 1 2 3 3h0l-1-1c0-1-1-2-2-3v-2zm26 121c5 0 9 0 14 1-6 1-11 1-17 3h5c-2 1-6 1-9 1-1 0-2 0-3-1h-3c2-1 5-1 7-1s3 0 4-1h-2c-3 0-8 1-10-1 1-1 6 0 8 0h0l6-1z" class="B"></path><path d="M638 599c2 0 4-1 6-1v1c1 0 2 0 3 1h1l-1 1h0c3 1 6 1 9 1 0 0 2-1 2 0h0c-2 0-7-1-8 0 2 2 7 1 10 1h2c-1 1-2 1-4 1s-5 0-7 1h-4-9c1 0 1-1 2-1h1c1-1 4-1 5-1v-1h-4c-1 0-2-1-3-1l1-1c-1 0-1-1-2-1z" class="C"></path><path d="M638 599c2 0 4-1 6-1v1c1 0 2 0 3 1h-7c-1 0-1-1-2-1z" class="I"></path><path d="M161 584h1l-1-1 1-1c2 0 4 1 6 2h1 0v1c3 1 7 1 11 2 2 1 5 1 7 2l-1 1c-1 0-1 0-2 1l-1 1s-1 1-2 1c-3-1-6 0-9-1h2v-1h-1c2 0 4 0 6-2h0l-4-2-4-1-10-2z" class="M"></path><path d="M53 571c4 0 9 1 13 1l7 1c1 1 2 1 3 1l1 1h7c2 0 5-1 6 0v1H74c-7 1-15 2-22 1h0l24-2-23-3v-1z" class="N"></path><path d="M651 623c2 0 9 0 10 1h3c3 0 5 0 7 1 1 1 3 0 4 1 3 0 6 0 9 1l-17-1h-13c-4 0-8 1-13 1 1-1 1-1 2-1h2c1-1 2-2 4-2h2v-1z" class="M"></path><path d="M547 611h3c-1 0-3 0-5 1-1 0-1 0-2 1h0l-1 1v1h0c1 0 3 0 4 1 0 0 1 0 1 1h-1-2l-1 1h-2c-1 1-1 1-2 1l-4 1c1-1 2-2 3-2h0 2v-1h3 1 0l-1-1c0 1-3 0-4 0v1h-2c-3-1-7-1-10-1l7-4h4c2-1 6-1 9-1z" class="K"></path><path d="M547 611h3c-1 0-3 0-5 1-1 0-1 0-2 1h0l-1 1v1h0c-1 0-2 1-3 1h0-6v-1l5-3c2-1 6-1 9-1z" class="X"></path><path d="M539 616c-1-1-1-1-2-1v-1h1c2-1 3-1 5-1l-1 1v1h0c-1 0-2 1-3 1z" class="I"></path><path d="M145 588c0-1 1-1 1-1h6c2 0 4-1 6-1 2 1 3 1 5 2 1 0 3 1 4 1h3c1 1 4 1 5 0l1-1-1-1 4 2h0c-2 2-4 2-6 2h1v1h-2-5c-2-1-3-1-4-2-2 0-4-1-6-1-1 0-2 1-3 1h-1c-2-1-3-1-5-1 1-1 4-1 6-1-1-1-4-1-5-1-2 1-3 1-4 1z" class="J"></path><path d="M154 588l9 1v1c-2 0-4-1-6-1-1 0-2 1-3 1h-1c-2-1-3-1-5-1 1-1 4-1 6-1z" class="Q"></path><path d="M163 589l10 2h1v1h-2-5c-2-1-3-1-4-2v-1z" class="H"></path><path d="M137 338c2 0 3-1 5 0 3 1 6 3 9 5h-1c-1 1-2 1-3 1h-1-3l-1 1c-1 0-1 0-2 1h-1l-1 1c0-1-1-1-1-2v-1-1c-1-1-2-1-2-3l2-2z" class="J"></path><path d="M137 338c2 0 3-1 5 0 3 1 6 3 9 5h-1c-3-2-7-4-10-5v1l3 2h0-1-1l1 1c-1 1 0 0-1 0h0c-1-1-2-2-3-2h0l-1-2z" class="B"></path><path d="M135 340l2-2 1 2h0c1 0 2 1 3 2h0c1 0 0 1 1 0h3 0l-2 2h0l-1 1c-1 0-1 0-2 1h-1l-1 1c0-1-1-1-1-2v-1-1c-1-1-2-1-2-3z" class="H"></path><path d="M142 345l-3-1h-1c0-1-1-1-1-3 1 0 2 0 3 1h1 0c1 0 0 1 1 0h3 0l-2 2h0l-1 1z" class="O"></path><path d="M71 582c4-1 8-1 12-1 1 1 2 1 2 2 2 1 2 1 4 1s5 0 6 1h-3l-2 1c-8-2-17-3-26-3-2 1-4 1-6 1h-5c-1 0-2-1-4-1h-9 0 1c3-1 6-1 9-1 7 0 14 1 21 0z" class="B"></path><path d="M71 582c4-1 8-1 12-1 1 1 2 1 2 2-2 0-7 0-9-1h-5z" class="C"></path><path d="M113 550c1 1 1 1 2 0 1 1 2 2 3 2h3 3l-2 1h0l2 1c2 0 4 1 5 1 1-1 2-1 3-1l7 1-1 1c-1 0-4-1-5 0v1h0c-1 1-3 1-4 1h-6-1l-8-1h7c1-1 2-1 3-1h1 0c-1-1-2-1-3-1h0-1-4v-1h-7v-1h0l2-1h-3 3l1-2z" class="J"></path><path d="M122 558v-1h1 5c-1 0-2 0-2 1h-3-1z" class="f"></path><path d="M132 554l7 1-1 1c-1 0-4-1-5 0v1h0c-1 1-3 1-4 1h-6 3c0-1 1-1 2-1 2 0 3-2 4-3z" class="X"></path><path d="M113 550c1 1 1 1 2 0 1 1 2 2 3 2h3 3l-2 1h0l2 1c2 0 4 1 5 1h0-2c-3-1-7-1-10-1h-7v-1h0l2-1h-3 3l1-2z" class="O"></path><path d="M113 550c1 1 1 1 2 0 1 1 2 2 3 2h3 3l-2 1h0l2 1c-1 0-2-1-4-1-3 0-5 0-8-1h-3 3l1-2z" class="K"></path><path d="M143 344h3c-1 1-2 1-3 2 2 4 6 8 5 13h-1c-2-3-4-4-8-5-1 0-2 0-4-1h1v-2l-1-1c1-1 1-1 1-3h0 1c0 1 1 1 1 2l1-1-1-1 1-1h1c1-1 1-1 2-1l1-1z" class="G"></path><path d="M136 351v-1l2-1 1 1c0 2 1 2 1 3l-1 1c-1 0-2 0-4-1h1v-2z" class="F"></path><path d="M139 350l3 3c1 0 2 0 3 1v-1l-1-1h1c1 1 2 2 2 4v3c-2-3-4-4-8-5l1-1c0-1-1-1-1-3z" class="X"></path><path d="M131 599c2-1 4-1 6-1h-1v1c2 0 4-1 5-1l-1 2-3 1-7 1c-2 0-4 0-5 1h-1l-2 1h-1c-3-3-10-2-14-3 1 0 2 0 3-1h1l3-1 15-1 2 1z" class="O"></path><path d="M131 599c2-1 4-1 6-1h-1v1c2 0 4-1 5-1l-1 2-3 1c-1-1-1 0-1-1v-1c-1 0-2 0-2 1h-1c0-1-1-1-2-1z" class="T"></path><path d="M94 394c0-2 1-4 0-6l-1 1c-1-1-1-1-1-2s-1-2-1-3v-1l-1-1c0-1-1-1-1-1v-2c-1 0 0 0-1-1v-1h0c1-1 2-1 2-2v-1c1 0 1-1 1-2 1-2 2-4 4-5v13c0 4 0 10-1 14z" class="w"></path><path d="M104 556c3 1 7 1 10 1l8 1h1 6c2 0 4 0 6 1h1v1h0c-2 0-5 1-8 1-2 0-5-1-7-1v1c-1 1-3 1-5 1-2-1-4-1-6-1s-3-1-5-1l3-1c-2-1-4-2-7-2h2v-1h-2 3z" class="l"></path><path d="M108 559l13 1v1c-1 1-3 1-5 1-2-1-4-1-6-1s-3-1-5-1l3-1z" class="D"></path><path d="M526 640c-9 0-18 1-27 2-6 0-12-1-17 1-1 1-1 1-2 1h-1l-1 2-1-1 1-1v-1h2 0 3c1-1 0-1 1-1h1c1-1 3 0 4 0l5-1h3 4 1 0v-1h2v-1c1 1 2 1 3 0-1 0-2-1-3-1-1 1-3 0-5 1h-1-1c-1 1-5 1-6 1-1-1-3-1-4-1h-10l-1 1c0 1-1 2-2 2h0-1c-1 0-2 1-3 0l1-1-1-1c-1 0-1 0-2 1h0c-2 0-4 1-5 0h-1c-1 1-3 0-4 0h-1 1c1-1 1-1 2-1h1 0 1l2 1c2 0 4-1 5-2h0c4 0 8-1 11-1 2 0 4 0 5 1 5 1 11 0 16-1h3c2 0 4 2 7 2 6 0 13-1 19-1v1h-1-3z" class="E"></path><path d="M532 621v1-1h3l3-1h2l1-1h1 2 1c4-2 9-1 12-1l1 1c0 1-1 2-1 2h-2c1 1 1 1 2 1l-1 1h-1l-7-1-1 1h-2c-2 0-5 0-7 1l-4 1-3 1c-4 1-8 1-12 2h-7v1h-9c2-1 4-1 5-1 2 0 3-1 4-1s0 1 1 0h1 1 2l1-1h2c1-1 4 0 5-1h0c2-2 6 0 8-2h0 0v1h0 2l1-1h2 0 2v-1h2 5c1-1 2-1 3-1v-1h1 0 2 1 0 1 1c1 0 1 0 1-1h-3c0 1-2 0-3 0h-3-1-2c-1 1-1 0-2 0-1 1-1 1-2 1 0 1 0 0-1 0l-1 1h-1-2v1h-3-1-2-2c-2 1-3 1-4 1h-1 0v-1h2c1-1 2 0 3 0 1-1 1-1 2-1h2z" class="b"></path><path d="M548 622c1 0 2 0 3-1s3 0 4 0c1 1 1 1 2 1l-1 1h-1l-7-1z" class="D"></path><defs><linearGradient id="F" x1="118.761" y1="605.692" x2="82.751" y2="600.57" xlink:href="#B"><stop offset="0" stop-color="#8d8b8b"></stop><stop offset="1" stop-color="#c0bfc0"></stop></linearGradient></defs><path fill="url(#F)" d="M95 602l12-1c4 1 11 0 14 3-4 1-8 0-12 0s-7 0-11 1c-2 0-4 0-7 1v-1c-2 0-3 0-5-1h-5c-2 1-1 1-2 0-1 0-3-1-4-1h0c2-1 7 0 9 0l11-1z"></path><path d="M65 554c-3 0-5 0-7-1 10 0 21 0 31 1 3 0 5 0 7 1h3c2 0 3 1 5 1h-3 2v1h-2c3 0 5 1 7 2l-3 1c-7-2-14-1-21-1h2v-1h-3 3v-1c-1 0-2-1-2-2l-19-1z" class="G"></path><path d="M84 555c2 0 9-1 10 1-2 1-6 1-8 2v-1c-1 0-2-1-2-2zm521 84c1 0 2 0 3-1s1-1 2-1c4 0 9 1 13 1l34 1h2 3v1h-2 0l-50 1c-2 0-4 1-6 0h-1 0c1 0 1-1 2-2z" class="C"></path><path d="M657 639h2 3v1h-2-9c2-1 4-1 6-1z" class="F"></path><path d="M70 587h13c4 0 7 1 11 1 3 1 5 1 8 1 1 0 2 0 3 1 2 1 11-1 12 0-2 2-12 0-15 0s-7 1-10 1H78c-2-1-6 0-7-2h-6-4c2 0 4-2 6-2h2 1z" class="B"></path><path d="M61 589c2 0 4-2 6-2h2 1v1c4 0 17-1 20 1h-6-13-6-4z" class="Y"></path><path d="M86 604c2 1 3 1 5 1v1c3-1 5-1 7-1 4-1 7-1 11-1l-3 1h-5v1h0c2-1 2 0 3 0h1c1 0 2 1 3 1l2 1h3v2l-11-1c-2 0-4 0-6-1-7 1-13 0-20 0H62c8-1 16-1 24-3v-1z" class="D"></path><path d="M96 608c-1 0-1-1-2-1-1 1-2 1-4 1v-1c5-1 8-2 13 0 2 0 4 1 6 1h1 3v2l-11-1c-2 0-4 0-6-1z" class="H"></path><path d="M58 584c2 0 4 0 6-1 9 0 18 1 26 3l2-1h3l1-1h5 24 2l-2 1h-2 0c-1 1-2 1-4 1h-1c0 1-1 1-2 1h-1-5c-1 0-5 1-6 0H94h-1l-3-1h-1-1l-1-1h-2-1-1c-1 0-1 0-2-1h-3-1-6c-1 0-1 0-2 1h-4c-2 0-4 1-6 1v-2h-1z" class="Y"></path><path d="M95 585l1-1c1 1 4 1 5 1v1h2-13l2-1h3z" class="O"></path><path d="M101 584h24-3c-1 1-1 1-2 1-1 1-3 1-5 1-3 1-8 1-12 0h-2v-1c-1 0-4 0-5-1h5z" class="K"></path><path d="M101 585h11s3 0 3 1h0c-3 1-8 1-12 0h-2v-1z" class="B"></path><path d="M576 456c0 1 1 2 1 2 1 1 1 3 2 5 1 4 1 8 2 12 0 4 2 8 2 12 2 13 3 26 7 39v4c-1-2-1-5-2-8-2-4-3-8-4-12v-5c-1-1-1-1-1-2l-2-13v-4c0-1 0-2-1-3v-7l-3-12c0-1-1-1-1-2v-2-4z" class="t"></path><path d="M71 427c0-3 3-8 5-11l6-12v2c0 2-1 4-2 6h0l1 1v2c-1 2-1 5-1 7v4c1-1 2-1 2-1 0 1 0 2 1 2h-2c-1 0-2 0-3 1 0 1-1 1-2 1h-1l-2 3c-1-2-2-3-2-5z" class="E"></path><path d="M80 412h0l1 1v2c-1 2-1 5-1 7 0-1 0-2-1-3 0 1 0 2-1 2v-1h-1v1h-1c0-1 0-2 1-2 1-3 1-5 3-7z" class="D"></path><path d="M80 426c1-1 2-1 2-1 0 1 0 2 1 2h-2c-1 0-2 0-3 1 0 1-1 1-2 1h-1l-2 3c-1-2-2-3-2-5l1 1c1-1 2-1 3-1 1-1 1-1 2-1h0 1 2z" class="F"></path><path d="M80 426c1-1 2-1 2-1 0 1 0 2 1 2h-2c-3 0-5 0-7 2-1-1-1-1-2-1 1-1 2-1 3-1 1-1 1-1 2-1h0 1 2z" class="R"></path><path d="M530 639h1 1l-1 1v1h4c1 0 2 0 2-1h5 1v-1h2 1c-1 1 0 1-1 1v1h-3-1l-1 1c2 0 2 0 3 1l-10 1c-3 0-6 0-9 1h-1l-4 1 18 2h0c1 0 2 1 3 1h-2-13c-7 0-15 1-22-1h0 3c1 1 2 0 4 0 1 1 1 1 2 1v-1-1h0-4 0-1-3c0-1 2-1 3-1 6 0 13 0 19-3h0v-3h3 1v-1z" class="K"></path><path d="M526 640h3 1c-1 1-2 2-4 3v-3z" class="J"></path><defs><linearGradient id="G" x1="552.255" y1="411.387" x2="556.044" y2="409.563" xlink:href="#B"><stop offset="0" stop-color="#5f5d5e"></stop><stop offset="1" stop-color="#868686"></stop></linearGradient></defs><path fill="url(#G)" d="M539 388l1-1 1 2c5 3 8 10 11 14 0 1 2 3 3 4h1c1 2 1 3 2 5l4 9c2 5 4 9 5 13-1 0-2-1-2-2h0c-3-5-5-8-7-13 0-1-1-3-2-4-2-5-5-11-9-15l-1 1-2-4-2-4h-1c0-1 0-1-1-2v-1c-1 0-1-1-1-2z"></path><path d="M539 388l1-1 1 2h1v2c0 1 1 1 1 2h-1-1c0-1 0-1-1-2v-1c-1 0-1-1-1-2z" class="Z"></path><path d="M540 608v-1h1c0-2 1-2 3-3h2v1h1 0c2 0 4-1 6-1v1h4 2v2h4v1s-1 1-1 2h-4-2l-2 1h-4-3c-3 0-7 0-9 1h-4c2-1 4-3 6-4z" class="o"></path><path d="M558 607h1v1l-1 1h-1c0-1 0 0-1 0l1 1h-1l-2 1h-4-3s0-1 1-1h1c1 0 1-1 2-1 2 0 3 0 4-1h1 2v-1z" class="E"></path><path d="M540 608v-1h1c0-2 1-2 3-3h2v1h1 0c2 0 4-1 6-1v1h4 2v2h4v1s-1 1-1 2h-4-2 1l-1-1c1 0 1-1 1 0h1l1-1v-1h-1-4c-2 0-10 2-12 1v-1l-2 1z" class="C"></path><path d="M547 605c2 0 4-1 6-1v1h4 0c-2 1-4 2-6 1s-3 1-4-1z" class="P"></path><path d="M540 608v-1h1c0-2 1-2 3-3h2v1h1 0c1 2 2 0 4 1l-9 1-2 1z" class="J"></path><path d="M204 409c0-1 0-1 1-2h0c1 2 1 4 2 6v-6h1v2 1c1 0 1-1 2-1 0 1 0 2 1 4v12 7 2h-1 0l-1 1-2-2v-1l1-1c-1-1-1-1-1-2v-6-4h0-2v-1c-1 0-1-1-1-1-1-1-2-2-2-3h-1v-2c0-1 1-2 2-2h1v-1z" class="b"></path><path d="M208 410c1 1 1 4 0 5 0-1 0-2-1-3 0-1 0-2 1-3v1z" class="D"></path><path d="M207 419l2 1v5l-2-2v-4z" class="P"></path><path d="M207 423l2 2v3h0c1 1 0 3 0 4s0 2 1 2l-1 1-2-2v-1l1-1c-1-1-1-1-1-2v-6z" class="C"></path><path d="M205 419v-1c-1 0-1-1-1-1-1-1-2-2-2-3h-1v-2c0-1 1-2 2-2h1v-1c0 2 0 5 1 8 1 1 2 1 4 1h1l1 1h0c-1 1-1 1-2 1l-2-1h0-2z" class="Y"></path><path d="M95 367v-1c1-1 0-5 1-6 1 1 1 7 0 9-1 9 0 18-1 27 0 6-1 12-1 18h1 0c1-1 1-1 2-1v3h0c1 2-3 8-2 10h2s0 1 1 1l-1 1v6c-1-1 0-2-1-3l-2 1h-2c0-2-1-3-1-5h-3-3-1v-1c1-1 1-1 2-1s1-1 2 0h0 1l1-1h1c1 0 1 0 2-1-2-2 0-6 0-8l-1-3h1v-2c1-5 1-10 1-16 1-4 1-10 1-14v-13z" class="C"></path><path d="M93 423l1-5c1-1 0-2 0-3h1v2c-1 2-1 4-2 6v2c2-1 3-7 4-9h0c1 2-3 8-2 10h2s0 1 1 1l-1 1v6c-1-1 0-2-1-3l-2 1h-2c0-2-1-3-1-5h-3-3-1v-1c1-1 1-1 2-1s1-1 2 0h0 1l1-1h1c1 0 1 0 2-1z" class="g"></path><path d="M91 427h2 0 1v1 2h2v1l-2 1h-2c0-2-1-3-1-5z" class="G"></path><path d="M123 346v-15c-2-1-3-1-4-3h0c2 0 4 2 6 3l4 4c1 3 1 4 1 7h1 1 0l3-2c0 2 1 2 2 3v1 1c0 1 1 1 1 2l1 1-1 1c0-1-1-1-1-2h-1 0c0 2 0 2-1 3l1 1v2h-1l-1-2c-3 0-7-2-9-3-1-1-1-1-1-2h-1z" class="k"></path><path d="M128 336c1 1 1 2 1 3s0 3 1 5l1 1h0-2c-1-2 0-5-1-6s-1-1-2-1l2-2z" class="F"></path><path d="M124 344v-1h1c1-1 0-1 0-2 0-2 1-4 1-6 0-1 0-1-1-2v-2l4 4-1 1-2 2c1 0 1 0 2 1s0 4 1 6c-2 0-3-1-4-1v-1l-1 1z" class="T"></path><path d="M132 342h0l3-2c0 2 1 2 2 3v1 1c0 1 1 1 1 2l1 1-1 1c0-1-1-1-1-2h-1 0c0 2 0 2-1 3v-1c-1-1-1-2-2-3 0-1-1-2-1-3l1-1h-1z" class="R"></path><path d="M133 346c1-2 0-2 1-3 1 1 1 2 2 3v1c0 2 0 2-1 3v-1c-1-1-1-2-2-3z" class="h"></path><path d="M123 346v-15c-2-1-3-1-4-3h0c2 0 4 2 6 3v2c1 1 1 1 1 2 0 2-1 4-1 6 0 1 1 1 0 2h-1v1h0c3 2 8 3 9 5h0c-2 0-4-1-6-2-1 0-2-1-3-1h0-1z" class="M"></path><path d="M182 368l2-6h1l1 1h0c2 3 3 6 4 9l-1 1c1 2 1 3 2 4 0 2 1 5 1 7h0-2-5-1-4c0-4 0-7 1-10 0-2 1-4 1-6z" class="C"></path><path d="M183 379v-5l1 1v3l-1 1z" class="f"></path><path d="M183 379l1-1v4 1l-1-1v-3z" class="J"></path><path d="M181 374s1 0 1 1c1 1-1 6 1 7l1 1v-1 2h-4c0-4 0-7 1-10z" class="b"></path><path d="M185 374c2-1 0-3 1-4 2 1 2 2 3 3 1 2 1 3 2 4 0 2 1 5 1 7h0-2-5 0v-10z" class="D"></path><path d="M185 374c2 3 1 6 2 9-1 1-1 1-2 1v-10z" class="E"></path><path d="M559 610h2v2h3c-1 1-1 2-2 2 1 0 1 0 2 1s2 1 2 2l1 1h0c1 1 2 1 3 1l-1 1h-4c-2-1 0 0-1 0l-1-1-2 1c1 1 1 1 2 1v1h-5c0-1 0-1-1-1h0s1-1 1-2l-1-1c-3 0-8-1-12 1h-1-2-1l-1 1h-2l-3 1h-3v1-1c1-1 2-1 3-1l4-1c1 0 1 0 2-1h2l1-1h2 1c0-1-1-1-1-1-1-1-3-1-4-1h0v-1l1-1h0c1-1 1-1 2-1 2-1 4-1 5-1h4l2-1h2 1z" class="X"></path><path d="M550 611h4 0v1c1 0 2 1 2 2 0 0 0 1-1 1 1 1 2 1 3 1v-1c1 0 2 1 3 1v2c-1 0-2-1-3-1h-2c-1 0-2-1-3-1h-1c-2-1-5-1-7-1h-3v-1l1-1h0c1-1 1-1 2-1 2-1 4-1 5-1z" class="H"></path><path d="M543 613c2-1 4 0 6 0 1 1 1 1 1 0h3l1 1c-3 1-6 0-9 1h-3v-1l1-1h0z" class="h"></path><path d="M559 610h2v2h3c-1 1-1 2-2 2 1 0 1 0 2 1s2 1 2 2l1 1h0c1 1 2 1 3 1l-1 1h-4c-2-1 0 0-1 0l-1-1c-1 0-1-1-2-1v-2c-1 0-2-1-3-1v1c-1 0-2 0-3-1 1 0 1-1 1-1 0-1-1-2-2-2v-1h0l2-1h2 1z" class="Q"></path><path d="M565 617h1l1 1h0c1 1 2 1 3 1l-1 1c-1 0-3 0-3-1-1-1 0-1-1-2z" class="I"></path><path d="M558 615v-1h2 2c1 0 1 0 2 1s2 1 2 2h-1c-1 0-1 0-2-1h1l-1-1c-1 1-4 0-5 0z" class="j"></path><path d="M559 610h2v2h3c-1 1-1 2-2 2h-2-2v1l-1-1v-2h2v-2z" class="G"></path><defs><linearGradient id="H" x1="667.439" y1="613.179" x2="674.599" y2="604.887" xlink:href="#B"><stop offset="0" stop-color="#857f85"></stop><stop offset="1" stop-color="#a6a9a4"></stop></linearGradient></defs><path fill="url(#H)" d="M651 605h3c1 1 2 1 3 1 3 0 7 0 9-1 5 1 11 1 16 1h1 2l1 1h0c0 1 0 2 1 3h-2-10c-2 0-4 0-6 2-1 0-2 0-3-1v-1h-6c-2 1-4 1-6 1h0s-1-1-2-1c-4 1-10-1-14-1l1-1c3 0 6-1 8 0h2c0-1 1-1 2-1v-1c-1 1-1 1-2 1s-2-1-2-2h4z"></path><path d="M682 609l3 1h-10c-2 0-4 0-6 2-1 0-2 0-3-1v-1h8 7l1-1z" class="B"></path><path d="M682 606h1 2l1 1h0c0 1 0 2 1 3h-2l-3-1c-4-1-9-1-14-1v-1c4 0 9 1 14-1z" class="Y"></path><defs><linearGradient id="I" x1="644.794" y1="613.643" x2="655.518" y2="603.439" xlink:href="#B"><stop offset="0" stop-color="#3e3c3e"></stop><stop offset="1" stop-color="#5f5f5e"></stop></linearGradient></defs><path fill="url(#I)" d="M639 608c3 0 6-1 8 0h1l14 1s-1 1-2 1c-2 1-4 1-6 1h0s-1-1-2-1c-4 1-10-1-14-1l1-1z"></path><path d="M651 605h3c1 1 2 1 3 1 3 0 7 0 9-1 5 1 11 1 16 1-5 2-10 1-14 1h-3-1l1 1h0-16c0-1 1-1 2-1v-1c-1 1-1 1-2 1s-2-1-2-2h4z" class="G"></path><path d="M651 605h3c1 1 2 1 3 1h2c-1 1-5 1-7 1h-1v-1c-1 1-1 1-2 1s-2-1-2-2h4z" class="j"></path><path d="M178 581s1-1 2-1l14 2 7 2c1-1 2-1 3 0h2c-1 1-3 6-3 7-4 0-8-1-11-1l-5-1c-2-1-5-1-7-2-4-1-8-1-11-2v-1l18 1c-2-1-5-1-7-1-1-1-2-1-3-1 1 0 2 0 3-1l-1-1h-1z" class="w"></path><path d="M194 582l7 2c-1 0-1 1-2 1-1-1-3-1-4 0l-1 1h1 0 1c2 0 6 0 7 1-1 1-3 0-4 0h-1c-2-1-4-1-7-1 1 0 2 0 2-1s-2-1-3-2h4v-1z" class="J"></path><path d="M178 581s1-1 2-1l14 2v1h-4c1 1 3 1 3 2s-1 1-2 1-3 0-4-1c-2-1-5-1-7-1-1-1-2-1-3-1 1 0 2 0 3-1l-1-1h-1z" class="P"></path><path d="M193 419h6 6 2 0v4 6c0 1 0 1 1 2l-1 1v1l2 2 1-1h0c0 1 0 2-1 3-1-1-2-1-3-1h-1l-1 1c-5-3-8-6-11-10 1-2 1-3 2-4l-1-1c0-1-1-2-1-3z" class="O"></path><path d="M193 419h6l1 1v2l1-1v3s-1 0-2 1l-1-1v2c-1-1-1-3-2-5-1 0-1 0-2 1 0-1-1-2-1-3z" class="C"></path><path d="M193 427c1-2 1-3 2-4 1 2 1 4 3 6l1 1c1 3 3 4 6 6l-1 1c-5-3-8-6-11-10z" class="S"></path><g class="D"><path d="M198 426v-2l1 1c1-1 2-1 2-1l1 1c0 1 0 6 1 7 1-1 0-2 1-3h1l1 2 1 1v1l2 2 1-1h0c0 1 0 2-1 3-1-1-2-1-3-1h0c-1-1-1-1-2-1 0-1-1-1-2-2h0c-1-1-1-1-1-3v-3c-1-1-2-1-3-1z"></path><path d="M205 419h2 0v4 6c0 1 0 1 1 2l-1 1-1-1-1-2h-1c-1 1 0 2-1 3-1-1-1-6-1-7l-1-1v-3l-1 1v-2l-1-1h6z"></path></g><path d="M201 421l1-1h0v3h1v1h-1v1l-1-1v-3z" class="U"></path><path d="M207 419h0v4 6c0 1 0 1 1 2l-1 1-1-1v-3c0-2-1-6 1-9z" class="M"></path><path d="M203 423c1 0 1-1 1-2h1c0 2-1 5 1 7v3l-1-2h-1c-1 1 0 2-1 3-1-1-1-6-1-7v-1h1v-1z" class="O"></path><path d="M84 559c7 0 14-1 21 1 2 0 3 1 5 1s4 0 6 1c2 0 4 0 5-1v-1c2 0 5 1 7 1-7 1-15 3-23 3-5 0-9-1-14-1-7 1-14-1-21-2-2 0-4 1-6 1h-1c-1 1-1 1-2 1v-1l-1 1-12-1H37h-6v-1h2 8 1 3c2-1 4-1 6-1l1 1 15-1h2v1h2c4-1 9-1 13-2z" class="b"></path><path d="M52 561l15-1h2v1c-4 0-8 1-12 1-2 0-4 0-5-1z" class="Z"></path><path d="M121 560c2 0 5 1 7 1-7 1-15 3-23 3-5 0-9-1-14-1l-7-1c2-1 5-1 7-1 9-1 17 2 25 1 2 0 4 0 5-1v-1z" class="L"></path><path d="M635 620h9 26 0v1h3 1 2c0 1-2 1-3 1-3 0-7 0-10 1h0l1 1h-3c-1-1-8-1-10-1v1h-2c-2 0-3 1-4 2h-2c-1 0-1 0-2 1h-2-7-10l-13-1c1-1 4-1 5-1 2 0 3-1 5-1s6 1 7-1h-2-2c1-1 3-1 4-1 4-2 9-1 12-1l1-1h-4z" class="g"></path><path d="M629 624h7c-3 1-7 1-10 2h-4v-1h2c2-1 4-1 5-1z" class="L"></path><path d="M624 623h3l1-1c2-1 6 0 8 0l-6 1c0 1 0 0-1 1-1 0-3 0-5 1h-2v1h4-4v1l-13-1c1-1 4-1 5-1 2 0 3-1 5-1s6 1 7-1h-2z" class="Q"></path><path d="M670 620h0v1h3 1 2c0 1-2 1-3 1-3 0-7 0-10 1h0l1 1h-3c-1-1-8-1-10-1s-4 0-5-1h15c-1-1-3-1-5-1h-12v-1h26z" class="C"></path><path d="M79 543h3v1c1 1 1 1 1 2 2 1 4 1 6 1h1l10 2c3 0 5 0 8 1h5l-1 2h-3 3l-2 1h0v1c-1 0-3-1-5-1l-7-1v1c-1-1-1-1-2-1h-1-1-6c-1 0-1-1-2-1 0-1-1 0-2 0v1h-2-1-2c-2-1-4-1-6-1l-12-2c-3 0-7 0-11-1h13l-4-1c-1 0 0 0-1-1h3v-1h-4c2 0 4-1 6 0 4 1 8 1 12 2v-1c-1 0-1-1-2-1h-4c3-1 7-1 10-2z" class="E"></path><path d="M98 552h0c-3-1-6-1-10-1h0v-1h5l12 2h4 3l-2 1h0v1c-1 0-3-1-5-1l-7-1z" class="P"></path><path d="M90 547l10 2c3 0 5 0 8 1h5l-1 2h-3-4l-12-2h-4c-2 0-4-1-6-2h4l3-1z" class="J"></path><path d="M85 397s0 1 1 1l1-1v2c-1 1 0 3 1 5h1l1 2h1l1 6 1 3c0 2-2 6 0 8-1 1-1 1-2 1h-1l-1 1h-1 0c-1-1-1 0-2 0s-1 0-2 1v1h1-1-1c-1 0-1-1-1-2 0 0-1 0-2 1v-4c0-2 0-5 1-7v-2l-1-1h0c1-2 2-4 2-6v-2l1-3 2-4z" class="K"></path><path d="M83 401c0 1 1 3 0 4h0l-1 1v-2l1-3z" class="D"></path><path d="M86 398l1-1v2c-1 1 0 3 1 5h0v1h0c0 1 0 2 1 2v6l-1-1v3c0-2 0-3-1-4h0v-1l-2-2v5c0-4-1-11 1-15z" class="F"></path><path d="M85 413v-5l2 2v1h0c1 1 1 2 1 4 0 1 0 1-1 2l-1 1 1 1c-2 1-1 4-1 6-1 0-1 0-2 1v1h1-1-1c-1 0-1-1-1-2l1 1v-2h0c1 0 1-1 1 0h1c-1-2 0-4-1-6v-3l1-2z" class="L"></path><path d="M87 411h0c1 1 1 2 1 4 0 1 0 1-1 2-1-2 0-4 0-6z" class="P"></path><path d="M82 406l1-1-1 9-1 3c1 1 2 0 3 0v-2h0v3c1 2 0 4 1 6h-1c0-1 0 0-1 0h0v2l-1-1s-1 0-2 1v-4c0-2 0-5 1-7v-2l-1-1h0c1-2 2-4 2-6z" class="C"></path><path d="M88 404h1l1 2h1l1 6 1 3c0 2-2 6 0 8-1 1-1 1-2 1h-1l-1 1h-1 0c-1-1-1 0-2 0 0-2-1-5 1-6l-1-1 1-1c1-1 1-1 1-2v-3l1 1v-6c-1 0-1-1-1-2h0v-1h0z" class="Q"></path><path d="M89 413h2c1 3 0 6 0 9h-1v-3c0-1-1-4-1-6z" class="F"></path><path d="M88 404h1l1 2c1 3 1 5 1 7h-2 0v-6c-1 0-1-1-1-2h0v-1h0z" class="P"></path><path d="M88 415v-3l1 1h0c0 2 1 5 1 6v3h1c0 1-1 1-1 2l-1 1h-1c-1-1-1 0-2 0 0-2-1-5 1-6l-1-1 1-1c1-1 1-1 1-2z" class="B"></path><path d="M88 415v-3l1 1h0c0 2 1 5 1 6v3h-1v-4l-1 1v3h-1v-3l-1-1 1-1c1-1 1-1 1-2z" class="N"></path><path d="M538 624c2-1 5-1 7-1-1 0-2 0-2 1 1 1 2 1 4 2l1 1c2 0 5 0 7 1 1 1 1 1 2 1 2 0 2 1 3 2 2 1 4 1 6 2 1 0 1-1 2-1h5c0 1-1 2-1 3h-3 2c0 1 0 1-1 1l-2 1v-1c-2-2-4-2-7-2h0-8-4c-3-1-6-1-10-1h-1-5-2l-2 2h-3v-2l-3-1h3c1 0 2-1 3-1 1 1 1 1 1 0v-1h-2l2-1c1 0 1 0 1-1-4 0-8 1-12 0 4-1 8-1 12-2l3-1 4-1z" class="O"></path><path d="M538 633v-2h2v1h0l-1 1h-1z" class="B"></path><path d="M543 629h-1v-1h2l1 1-1 1-1-1z" class="D"></path><path d="M543 626h4l1 1h-2c-1 0-1 0-2 1l-1-2z" class="B"></path><path d="M531 626l3-1 1 1v1h-3v-1h-1z" class="X"></path><path d="M530 629l4 1h1v1c-1 1-2 1-2 1h-3c-1 1-3 0-4 0 1 0 2-1 3-1 1 1 1 1 1 0v-1h-2l2-1z" class="f"></path><path d="M538 624c2-1 5-1 7-1-1 0-2 0-2 1 1 1 2 1 4 2h-4-4c-1-1-1-1-1-2z" class="L"></path><path d="M553 634c-1 0-2-1-3-1s-2-1-2-1c-1 0-1 0-2-1h-2-1 0v-1h8l3 1h4 2c2 1 4 1 6 2 1 0 1-1 2-1h5c0 1-1 2-1 3h-3 2c0 1 0 1-1 1l-2 1v-1c-2-2-4-2-7-2h0-8z" class="T"></path><path d="M551 630l3 1h4l-1 1h-5l-1-1v-1z" class="Q"></path><path d="M561 634c2 0 6-1 8 1h2c0 1 0 1-1 1l-2 1v-1c-2-2-4-2-7-2h0z" class="b"></path><defs><linearGradient id="J" x1="497.193" y1="332.006" x2="494.55" y2="365.729" xlink:href="#B"><stop offset="0" stop-color="#868484"></stop><stop offset="1" stop-color="#9d9d9d"></stop></linearGradient></defs><path fill="url(#J)" d="M470 329h1c1 1 2 2 4 2h0 0 1c1 1 3 3 5 4 2 2 4 5 7 7 5 5 12 10 18 14s12 7 17 12v2l-3-2-1-1-1-1h-1s0-1-1-1h-1v-1h-1l-1-1c-2 0 1 1-1 0h0c-2-1-3-2-4-2-1 1-1 1-1 2h-1c-1-1-2-1-3-2l-1-1c-1 0-4-1-5-1h0-1c-1-1-1-2-2-3h0 0c-2-1-2-1-2-2h0 1c1-1 1-1 1-2l-1-1h-1c0-1 1-1 0-2-2-2-4-3-7-5l-1-1s-1 0-2-1l-4-4h-1v1c0 1 0 1-1 2 0-1-1-1-2-2 0-1-1-2-2-3l-3-3c0-2 1-1 2-2l-1-1v-1z"></path><path d="M496 354h2v1l-1 1c0-1-1-1-1-2z" class="Q"></path><path d="M123 346h1c0 1 0 1 1 2l-1 1v58l2 2h1v5h0-3c0-1 0-1-1-1l1-1c0-1 0-1-1-2l-2 1c-1-1 0-2 0-3h0-2c-1-1-2-6-2-7h-1c-1-1-1-3-2-4v-1-1c-1-1-1-2-1-3 1-2 1-4 1-6 1-1 3-5 3-5l1 1v2l1 1 1-1v-1c0-1 1-3 2-4h0l1-1c1-1 0-5 0-7v-25z" class="U"></path><path d="M121 408h2v2l-2 1c-1-1 0-2 0-3z" class="K"></path><path d="M120 384c0 1 0 2 1 3 1-1 0-4 1-5v-1l1-1v3 7 1l-3-3h0l-1-3 1-1z" class="J"></path><path d="M116 401c-1-1-1-3-2-4v-1-1c-1-1-1-2-1-3 1-2 1-4 1-6 1-1 3-5 3-5l1 1v2l1 1 1 3h0l3 3v1h-1c0-1 0-1-1-2h0c-1 1-1 1-1 2-1 1 0 1-1 2-1-1-1-2-3-2h-1c1 1 1 1 1 2h-1c0 1 0 1 1 2h0l-1 1 2 4h-1z" class="I"></path><path d="M118 382v2 1 3h0c0 1 1 2 1 2l-1 1v-1l-2 1-1-1v-4c1-1 2-3 3-4z" class="P"></path><path fill="#fff" d="M116 396h2 1l4-4c0 5-1 10 0 15l-2 1h-2c-1-1-2-6-2-7l-2-4 1-1z"></path><defs><linearGradient id="K" x1="510.094" y1="381.478" x2="512.093" y2="361.257" xlink:href="#B"><stop offset="0" stop-color="#878687"></stop><stop offset="1" stop-color="#a09f9f"></stop></linearGradient></defs><path fill="url(#K)" d="M497 359c1 0 4 1 5 1l1 1c1 1 2 1 3 2h1c0-1 0-1 1-2 1 0 2 1 4 2h0c2 1-1 0 1 0l1 1h1v1h1c1 0 1 1 1 1h1l1 1 1 1c0 1 1 2 2 3 3 2 7 5 9 9-2-1-3-3-5-4h0c-1-1-1-2-2-2v1l-1 1h-1-3l-1-2h-1-3-1v1l-2-1c-1 1-2 1-3 2 0 1 0 1 1 2s2 2 2 3c1 1 1 1 1 2-1-1-2-1-3-2 0-1 0-1-1-1h0l-2-1c-1 0-1-1-1-1 0-1-1-1-1-2h0-2c-1-1-1 0-2 0 0-2-1-2-1-3h-1c0-1 1-2 1-2v-2h1v-1c-1-1-2-2-2-3-1 0-2-1-3-2s-3-2-4-2l-1-1 1-1h0c1 1 1 1 2 1h0c1 1 1 2 2 2s2 1 2 1l1 1h1c0-1-1-1-1-1v-1c0-1-1-1-1-2v-1h0z"></path><path d="M514 370c1 1 2 1 3 2l-2 1-2-2 1-1z" class="G"></path><path d="M517 372c1-1 2-2 3-2l1 2-1 1h0c-1 0-2-1-3-1z" class="B"></path><path d="M501 372v-2c2 0 3 0 3-1 1 0 1 0 2-1l3 3 1 1-1 1h-2v2h-1v-1h1v-1h-1l-1 1c0-1 0-1 1-2 1 0 1 0 2-1-1 0-1 0-1-1h-3l-1 2h-2z" class="G"></path><path d="M512 363h0c2 1-1 0 1 0l1 1h1v1h1c1 0 1 1 1 1h1l1 1 1 1c0 1 1 2 2 3l-1 1-1-2c-1 0-2 1-3 2h0c-1-1-2-1-3-2h0c0-1-1-2-2-2v-1s-1 0-1-1l-1-1h1 0 1l-1-1 1-1z" class="H"></path><path d="M512 368l1-1h0c1 1 2 1 3 1h1c-1 1-2 1-3 2 0-1-1-2-2-2z" class="B"></path><path d="M491 361l-1-1 1-1h0c1 1 1 1 2 1h0c1 1 1 2 2 2s2 1 2 1l1 1h1c0-1-1-1-1-1v-1c0-1-1-1-1-2v-1h0c0 1 1 1 1 2 1 1 2 0 2 1s0 1-1 2h2c2 1 2 2 3 3 1 0 1-1 2 0h1c0 1 1 1 2 2h0v2l-3-3c-1 1-1 1-2 1 0 1-1 1-3 1v2h0l-2-1h0v-2h1v-1c-1-1-2-2-2-3-1 0-2-1-3-2s-3-2-4-2z" class="N"></path><path d="M154 573l2 2v1l11 3c1 1 2 1 3 1 2 1 2 1 3 1h1 1 1 1 1 1l1 1c-1 1-2 1-3 1 1 0 2 0 3 1 2 0 5 0 7 1l-18-1h0-1c-2-1-4-2-6-2l-1 1 1 1h-1-2-6-9l-15-1-2 1h-2-24-4l1-1-1-1c4-1 9 0 13 0h0c4 0 9-1 13 0h1c2-1 5-1 6-1h4l-1-2h1 6v-2l2 1 1 1c1-1 2-1 2-1 1 1 2 1 4 1 0-1-1-2 0-3h4l-2-2c1 0 2 0 3-1z" class="L"></path><path d="M98 583l-1-1c4-1 9 0 13 0h9c-7 2-14 1-21 1z" class="D"></path><path d="M129 583h13c3 0 5 1 7 0 4 0 7 0 10 1h-6-9l-15-1z" class="f"></path><path d="M134 579h6 4c2 2 5 2 7 2l1 1h-4c-1 0-2 0-3-1-1 1-1 1-2 1h-2v-1h-3 0-4l-1-2h1z" class="G"></path><defs><linearGradient id="L" x1="170.864" y1="588.748" x2="155.747" y2="570.64" xlink:href="#B"><stop offset="0" stop-color="#c1bec2"></stop><stop offset="1" stop-color="#eaeae8"></stop></linearGradient></defs><path fill="url(#L)" d="M154 573l2 2v1l11 3c1 1 2 1 3 1 2 1 2 1 3 1h1 1 1 1 1 1l1 1c-1 1-2 1-3 1-2 0-4-1-6-1s-5 0-8-1c-2 0-5-1-7-1-2-1-3-1-4-1h-3c0-1-1-2 0-3h4l-2-2c1 0 2 0 3-1z"></path><path d="M154 573l2 2v1h-3l-2-2c1 0 2 0 3-1z" class="B"></path><defs><linearGradient id="M" x1="564.638" y1="461.163" x2="576.555" y2="454.433" xlink:href="#B"><stop offset="0" stop-color="#292929"></stop><stop offset="1" stop-color="#4b4b4b"></stop></linearGradient></defs><path fill="url(#M)" d="M559 434c1 0 1-1 1-1 0-1 0-2 1-3h1v2l2 1v-1h1c0 1 1 2 2 2l3 8c1 2 2 3 2 5 1 3 3 6 4 9v4 2c0 1 1 1 1 2l3 12v7h0c0 1 1 2 0 2v1h0c-1-1-1-2-1-3 0 0 0 5-1 6l-2-13h-1 0c-2-3-2-7-3-10l-2-10-1 2c0-2-1-3-1-4h-1c-1-1-1-2-1-3 0-2-1-4-1-5s0-1-1-1v1c0 2 0 3-1 4v-2c0-1-1-3-1-4l1-1v-1c-1 0-1 0-2 1 0-1 0-3-1-4s-1-2-1-4v-1z"></path><path d="M560 435h0l1 1v2h-1v-3z" class="i"></path><path d="M566 451c1 1 2 1 3 3 1 0 1 1 1 2h0l-1 2c0-2-1-3-1-4h-1c-1-1-1-2-1-3z" class="V"></path><path d="M154 566h3s0-1 1-1 2 1 2 1v1l4 1c1 1 2 1 3 1h1c1 1 3 2 3 3h0-1v1h-1s-1 0-2-1v1 1c-1 1-1 0-2 0h-1c-1 0-1 1-1 2l5 3h-1l-11-3v-1l-2-2c-1 1-2 1-3 1l2 2h-4c-1 1 0 2 0 3-2 0-3 0-4-1 0 0-1 0-2 1l-1-1-2-1v2h-6-1-1v-2l-4 1 1-1v-1-1h2 2l1-1c0-1 0 0-1-1h-2c1-1 4-2 6-2h0l1-2 16-3z" class="F"></path><path d="M133 573c1 0 2 0 3-1 0 1 0 1 1 1l-3 1c0-1 0 0-1-1z" class="U"></path><path d="M149 569v-2c1 0 1 0 2 1h1c1 0 2 0 3 1h-5-1z" class="B"></path><path d="M143 574c1-1 3-1 5-1h0v1c-1 0-2 0-3 1l-2-1z" class="P"></path><path d="M151 574l2 2h-4c-1 1 0 2 0 3-2 0-3 0-4-1 1-1 1-1 3-1-1-1-1-1-1-2h2c1 0 1-1 2-1z" class="E"></path><path d="M150 569h5l3 1v1c-1 0-2 0-3 1-1-1-3-1-4-1h-1l2-1h0l-2-1z" class="p"></path><path d="M137 573c1 0 4-1 6 0v1l2 1h1c-1 1-2 1-4 1h0c-2 0-4-1-7-1h-1-1l1-1 3-1z" class="H"></path><path d="M149 569h1l2 1h0l-2 1h1c2 1 2 1 3 2-1 1-2 1-3 1h0c0-1 0-1 1-2-2-1-5 0-6 0-2-1-4 0-6-1h2l7-2z" class="L"></path><path d="M149 569h1l2 1h0l-2 1c-2-1-6 0-8 0l7-2z" class="Z"></path><path d="M131 575h2 1 1c3 0 5 1 7 1h0 0v2l-2-1v2h-6-1-1v-2l-4 1 1-1v-1-1h2z" class="B"></path><path d="M133 577c1 0 2 0 3 1l-2 1h-1-1v-2h1z" class="H"></path><path d="M128 578l1-1v-1-1c2 1 5 0 5 1l-1 1h-1l-4 1z" class="O"></path><path d="M136 578v-1c2-1 3 0 4 0v2h-6l2-1z" class="N"></path><path d="M160 567l4 1c1 1 2 1 3 1h1c1 1 3 2 3 3h0-1v1h-1s-1 0-2-1v1 1c-1 1-1 0-2 0h-1c-1 0-1 1-1 2l5 3h-1l-11-3v-1l-2-2c-1-1-1-1-3-2 1 0 3 0 4 1 1-1 2-1 3-1v-1h1v-2l-2-1h0 3z" class="G"></path><path d="M151 571c1 0 3 0 4 1h1v3l-2-2c-1-1-1-1-3-2z" class="N"></path><path d="M158 570h1c1 1 2 1 3 1-1 2 0 3 0 5-1-1-3-1-3-3h1c0-2-1-1-2-2v-1z" class="S"></path><path d="M162 571c1 1 2 1 3 2h0 2v1c-1 1-1 0-2 0h-1c-1 0-1 1-1 2h-1c0-2-1-3 0-5z" class="O"></path><path d="M160 567l4 1c1 1 2 1 3 1h1c1 1 3 2 3 3h0-1v1h-1s-1 0-2-1v1h-2 0c-1-1-2-1-3-2-1 0-2 0-3-1v-2l-2-1h0 3z" class="P"></path><path d="M160 567l4 1c1 1 2 1 3 1v1c-3-1-5-2-8-2l-2-1h0 3z" class="G"></path><path d="M124 407l15-2 1 1v4c0 2 0 4 1 6v2 1 4h0v2c-1-1-1-1-2-1v2h-1-1l-1 2-2-2c0 1-1 2-2 2 0 1 0 1-1 2l-1-1h-2-1-1v-3l1-1v-1l-2-1v-3c0-1-2-2-2-4h0 0l-2 1c0-1-1-2-1-3v-1-2h1 0l2-1c1 1 1 1 1 2l-1 1c1 0 1 0 1 1h3 0v-5h-1l-2-2z" class="D"></path><path d="M135 418h0-1c0-1 0-2 1-3 0-1 0-2 1-3v-1s0-1 1-2v2l1 1c-1 1-1 3-2 4v2h-1z" class="C"></path><path d="M135 418h1l1 1v1 2c0 1 0 1 1 2v2h-1l-1 2-2-2c0 1-1 2-2 2l3-10z" class="I"></path><path d="M134 426c1-1 1-2 2-2 0 0 1 1 1 2l-1 2-2-2z" class="h"></path><path d="M137 420v2c0 1 0 1 1 2v2h-1c0-1-1-2-1-2l1-4z" class="t"></path><path d="M130 418c0 1-1 4-1 5 2-1 2-5 3-7-1-1-1-2-1-3l1-5h1c0 2-1 4-1 6v1c2-2 2-6 3-8h0c-1 5-2 9-4 13 0 3-1 5-2 8l-1 1h-1-1v-3l1-1h1v-4-2c0-1 1-2 2-2v1z" class="M"></path><path d="M140 406v4c0 2 0 4 1 6v2 1 4h0v2c-1-1-1-1-2-1v2h-1v-2c-1-1-1-1-1-2v-2-1l-1-1v-2c1-1 1-3 2-4 0-2 0-5 2-6z" class="I"></path><path d="M136 416l2 1v-1s0-1 1-2h0c1 2 0 3 0 5l1 1c0 1 1 2 1 3h0v2c-1-1-1-1-2-1v2h-1v-2c-1-1-1-1-1-2v-2-1l-1-1v-2z" class="a"></path><path d="M138 424v-4h0c1 1 1 2 1 4v2h-1v-2z" class="j"></path><path d="M127 414h1c1-2-1-3 0-5 1 0 1 1 2 2v4c1 1 0 2 0 3v-1c-1 0-2 1-2 2v2 4h-1v-1l-2-1v-3c0-1-2-2-2-4h0 0l-2 1c0-1-1-2-1-3v-1-2h1 0l2-1c1 1 1 1 1 2l-1 1c1 0 1 0 1 1h3 0z" class="O"></path><path d="M123 410c1 1 1 1 1 2l-1 1c1 0 1 0 1 1 0 2 1 4 1 6l2-4h1c0 1 0 2-1 2 0 2-1 4 0 6l-2-1v-3c0-1-2-2-2-4h0 0l-2 1c0-1-1-2-1-3v-1-2h1 0l2-1z" class="M"></path><path d="M120 414v-1-2h1c1 1 1 3 2 5l-2 1c0-1-1-2-1-3z" class="H"></path><path d="M556 590c1-1 2-1 3-1h0 4 0 4v-1c-2 0-3 1-5-1h0 1 0c1 0 4 0 5-1h9c1 0 2 0 3 1 1 0 5 0 6-1 0 0 1 0 1 1 3 1 5 3 8 4 1 1 3 1 4 2 1 0 1 1 1 1-3 0-5-1-9-1-2 0-5 1-7 0h-2c-1 1-2 1-3 2v3h-2-1c-1 1-2 0-3 1h6 5c-2 1-7 1-8 0h-1-1c-1 0-3 1-5 0v1h1c1 0 2 1 2 1-2 0-3 0-5-2h-1c-3 0-5 0-8-1-2 0-8 1-10 0l1-2c0-1 0-3 2-3v-2c1-1 2-1 3-1 1-1 1 0 2 0z" class="E"></path><path d="M558 595h1l1 1-1 1h-2v-1l1-1z" class="P"></path><path d="M549 596c0-1 0-3 2-3v-2c1-1 2-1 3-1 1-1 1 0 2 0h0c-1 0-2 0-3 1h0c1 1 1 1 2 1h1v1h-1c-2 0-3 1-5 2l-1 1z" class="J"></path><path d="M568 589h1c1 0 8 0 9 1l-1 1h0-4-1c-2 0-3 0-4-1v-1z" class="X"></path><path d="M556 590c2 0 5 0 7-1 1 0 3 0 4 1h-1 0c-1 0-2 1-3 1h0c-2 0-6 1-8 0 1 0 1-1 1-1z" class="P"></path><defs><linearGradient id="N" x1="637.231" y1="626.226" x2="657.886" y2="612.013" xlink:href="#B"><stop offset="0" stop-color="#5e5d5c"></stop><stop offset="1" stop-color="#767677"></stop></linearGradient></defs><path fill="url(#N)" d="M638 609c4 0 10 2 14 1 1 0 2 1 2 1h0c2 0 4 0 6-1h6v1c1 1 2 1 3 1h9l-4 1c1 1 2 1 3 1h-1c1 2 1 2 2 3h-6l1 2v1h0c-1-1-2-1-3 0h0-26-9-3c0-1 1-1 1-2-1 0-2 0-3-1h-1v-1c2 0 3 0 4-1h0c-3 0-6 0-8-1 2 0 4-1 6-2h-2c-1-1-1 0-1-1l1-1h-3 4c2-1 6-1 8-1z"></path><path d="M633 615h7 0 2 8l4-1c-3 2-4 1-6 2-2 0-4 1-6 1l-1-1c-1 0-2 0-2 1h-9-1v-1c2 0 3 0 4-1z" class="R"></path><path d="M639 613h11 3c0 1 1 1 1 1l-4 1h-8-2 0-7 0c0-1 0-1 1-1s3-1 5-1z" class="B"></path><defs><linearGradient id="O" x1="663.97" y1="614.374" x2="674.498" y2="617.071" xlink:href="#B"><stop offset="0" stop-color="#7c7b7b"></stop><stop offset="1" stop-color="#9e9b9c"></stop></linearGradient></defs><path fill="url(#O)" d="M662 614h6 1c2 0 5 1 7 0 1 2 1 2 2 3h-6l1 2v1h0c-1-1-2-1-3 0h0c-2-1-3-1-5-1-1 0-2 0-4-1l1-1h-1v-1h2v-1c-2 0-3 0-4-1h3z"></path><path d="M662 617h2c1 1 1 0 2 0l3 1h-4v1c-1 0-2 0-4-1l1-1z" class="Q"></path><path d="M669 618l3-1 1 2v1h0c-1-1-2-1-3 0h0c-2-1-3-1-5-1v-1h4z" class="T"></path><defs><linearGradient id="P" x1="658.99" y1="608.468" x2="663.065" y2="615.407" xlink:href="#B"><stop offset="0" stop-color="#787677"></stop><stop offset="1" stop-color="#a4a3a3"></stop></linearGradient></defs><path fill="url(#P)" d="M660 610h6v1c1 1 2 1 3 1h9l-4 1c1 1 2 1 3 1h-1c-2 1-5 0-7 0h-1-6-5-3s-1 0-1-1h-3 4v-1-1c2 0 4 0 6-1z"></path><path d="M653 613c1 0 3 0 4 1h0-3s-1 0-1-1z" class="H"></path><path d="M638 609c4 0 10 2 14 1 1 0 2 1 2 1h0v1 1h-4-11c-2 0-4 1-5 1s-1 0-1 1c-3 0-6 0-8-1 2 0 4-1 6-2h-2c-1-1-1 0-1-1l1-1h-3 4c2-1 6-1 8-1z" class="k"></path><path d="M654 611h0v1 1h-4-11v-1h4c2 1 6 1 8 0 0 0 0-1 1-1h2z" class="m"></path><path d="M630 610h7l-5 1v1h2-3-2c-1-1-1 0-1-1l1-1h-3 4z" class="R"></path><path d="M631 612h3 5v1c-2 0-4 1-5 1s-1 0-1 1c-3 0-6 0-8-1 2 0 4-1 6-2z" class="L"></path><defs><linearGradient id="Q" x1="615.044" y1="635.084" x2="612.39" y2="623.868" xlink:href="#B"><stop offset="0" stop-color="#b7b6b6"></stop><stop offset="1" stop-color="#dddcdd"></stop></linearGradient></defs><path fill="url(#Q)" d="M604 626h5l13 1h10 7c-1 0-3 1-4 1h0v1l11 2h2l-1 1-14-1h-2 0-1v1h-1-1-1c0 1-1 1-1 1-3 1-6 1-9 1-2-1-4-1-6-1l-13-1c-1 1-3 1-4 1h-3c0 1-1 1-1 1-2 1-3 1-4 1-2 0-3 0-4 1h-3c-1 0-2 0-3-1h-2-2c0-1 1-2 1-3h-5c-1 0-1 1-2 1-2-1-4-1-6-2-1-1-1-2-3-2-1 0-1 0-2-1 5-1 9 0 15 0v1h5c3 0 7 0 10-1h12l6-2h1z"></path><path d="M633 631l-7-2h9l11 2h2l-1 1-14-1z" class="l"></path><path d="M593 631h1c5 0 11-2 17-2h5c-3 2-6 1-8 2l-1 1h-2-7c-1 1-3 1-4 1h-3c0 1-1 1-1 1-2 1-3 1-4 1-2 0-3 0-4 1h-3c-1 0-2 0-3-1h-2-2c0-1 1-2 1-3h7 0l13-1z" class="L"></path><path d="M583 633h11-3c0 1-1 1-1 1-2 1-3 1-4 1v-1h-3v-1h0z" class="D"></path><path d="M573 632h7l4 1h-1 0v1h3v1c-2 0-3 0-4 1h-3c-1 0-2 0-3-1h-2-2c0-1 1-2 1-3z" class="O"></path><path d="M573 632h7l4 1h-1 0c-3 0-7 1-9 2h-2c0-1 1-2 1-3z" class="G"></path><path d="M555 628c5-1 9 0 15 0v1h5c3 0 7 0 10-1h12v1h1s-1 1-2 1c-1 1-2 1-3 1l-13 1h0-7-5c-1 0-1 1-2 1-2-1-4-1-6-2-1-1-1-2-3-2-1 0-1 0-2-1z" class="w"></path><path d="M555 628c5-1 9 0 15 0v1c-2 0-3 0-4 1v2c1 1 11 0 14 0h0-7-5c-1 0-1 1-2 1-2-1-4-1-6-2-1-1-1-2-3-2-1 0-1 0-2-1z" class="f"></path><path d="M140 600h1l2 2c7 1 14 1 20 1 5-1 13 0 17 2l-1 1 2 1c-3 0-5 1-7 1-6 0-11-1-17 0-1 1-1 0-2 1h-5v1c-1 0-2-1-3 0h-4-30v-2h-3l-2-1c-1 0-2-1-3-1h-1c-1 0-1-1-3 0h0v-1h5l3-1c4 0 8 1 12 0h1l2-1h1c1-1 3-1 5-1l7-1 3-1z" class="w"></path><path d="M163 603c5-1 13 0 17 2l-1 1h0c-2-1-4-1-6 0h-1v-1c0-1 2 0 4-1h-19c2 0 5 0 6-1z" class="o"></path><path d="M140 600h1l2 2c7 1 14 1 20 1-1 1-4 1-6 1h-35l2-1h1c1-1 3-1 5-1l7-1 3-1z" class="K"></path><path d="M173 606c2-1 4-1 6 0h0l2 1c-3 0-5 1-7 1-6 0-11-1-17 0-1 1-1 0-2 1h-5v1c-1 0-2-1-3 0h-4-30v-2c8 0 16 1 24 1 2 0 5 0 6-1h4c3 0 6 0 8-1 5 0 10-1 15 0h6v-1h-3z" class="C"></path><path d="M559 602c3 0 6 1 9 2 2 0 4 0 6 1l24-1h0c1 1 2 1 3 1v1c2 1 4 0 6 1h-13v1 1h3 2v1c-2 0-5 0-6 1-1 0-2 0-2 1h-1l3 1-1 1v1l-1 2h-2c2 0 3 0 4 1l-1 2h0-4l-1-1h-8l-1-1h-3c-1 0-1-1-2-1-2 1-4 1-6 1l-1-1c0-1-1-1-2-2s-1-1-2-1c1 0 1-1 2-2h-3v-2h-2-1 4c0-1 1-2 1-2v-1h-4v-2h-2-4v-1h4v-1h3c0-1 0-1-1-1z" class="F"></path><path d="M553 604h4c3 0 8 0 11 2h-1 0c-2 0-6-1-8-1h-2-4v-1z" class="N"></path><path d="M570 605l2 2c0 1 1 1-1 2-1 1-1 1-2 1h1c1 0 0 0 1 1-2-1-4-1-6-2h1c0-1 1-1 1-2h1 2 0l-1-1 1-1z" class="X"></path><path d="M579 614h13v1l-1 2h-2-4l1-1h0c-1-1-2-1-3-1s-3-1-4-1z" class="G"></path><path d="M589 617l2-2h1l-1 2h-2z" class="l"></path><path d="M572 607l5 2c3 1 6 3 10 3h1v1c-5 2-12-2-17-2-1-1 0-1-1-1h-1c1 0 1 0 2-1 2-1 1-1 1-2z" class="J"></path><path d="M561 610c2 0 4 1 6 1 1 1 3 1 5 2l6 1-1 1c-1 0-3-1-4 0l-1 1h-2l-1 1h4c-2 1-4 1-6 1l-1-1c0-1-1-1-2-2s-1-1-2-1c1 0 1-1 2-2h-3v-2z" class="k"></path><path d="M564 612l4 2v1h-1-3c-1-1-1-1-2-1 1 0 1-1 2-2z" class="I"></path><path d="M568 614c2 0 3 0 5 1l-1 1h-2l-1 1c-1 0 0 0-1-1h1v-1h0-2 0 1v-1z" class="j"></path><path d="M564 615h3 0 2 0v1h-1c1 1 0 1 1 1h4c-2 1-4 1-6 1l-1-1c0-1-1-1-2-2z" class="a"></path><path d="M578 614h1c1 0 3 1 4 1s2 0 3 1h0l-1 1h4c2 0 3 0 4 1l-1 2h0-4l-1-1h-8l-1-1h-3c-1 0-1-1-2-1h-4l1-1h2l1-1c1-1 3 0 4 0l1-1z" class="F"></path><path d="M578 614h1c1 0 3 1 4 1s2 0 3 1h0l-1 1h-11c-2-1-3-1-4-1h2l1-1c1-1 3 0 4 0l1-1z" class="R"></path><defs><linearGradient id="R" x1="580.654" y1="611.848" x2="589.521" y2="598.751" xlink:href="#B"><stop offset="0" stop-color="#a19b9d"></stop><stop offset="1" stop-color="#b8bbb9"></stop></linearGradient></defs><path fill="url(#R)" d="M574 605l24-1h0c1 1 2 1 3 1v1c2 1 4 0 6 1h-13v1 1h3 2v1c-2 0-5 0-6 1-1 0-2 0-2 1h-1-2-1c-4 0-7-2-10-3l-5-2-2-2h0 4z"></path><path d="M583 608c3-1 8-1 11-1v1 1h3 2v1c-2 0-5 0-6 1-1 0-2 0-2 1h-1-2-1c-4 0-7-2-10-3v-1c1-1 5 0 6 0z" class="R"></path><path d="M587 612v-1c0-1 0-1 1-1s2 1 3 2h-1-2-1z" class="g"></path><path d="M583 608c3-1 8-1 11-1v1 1h3-5c-3 0-6 0-9-1z" class="H"></path><path d="M72 500l1 4c0-1 1 0 1-1v6h1c0-1 0-1 1-2v1c0 2 0 3-1 4 0 2-1 3-1 5 0 1-1 3-1 5 0 3-2 7-2 10-1 2-1 3-1 5h1c0-1 0-1 1-2v2h1 2c1 0 1 1 2 1h1 5 1s2 1 3 1c0-1 0-1 1-1 1 1 1 1 2 1h0c1-1 1-1 2-1s1 0 2 1l1-1h2c1-1 3-1 4-2l1 2v1h0l1 1h1c1 0 2-1 3-1 2 0 3 1 5 2h1-4v1h1c1 1 1 2 2 3-2-1-3-1-5-1-3 0-5 1-8 1h-3c-2 1-3 1-4 2l-3-1v1c-2 0-4 0-6-1 0-1 0-1-1-2v-1h-3c-1-1-1-1-3-1-2-1-5 0-7-1-1-1-3 0-4-1h-2-1c-2-1-4-1-5-1-2 0-4-1-6-1h-1v-1h7c1-3 1-7 2-10h0l2 2c1 1 0 1 1 1 1-2 1-3 2-4l1 1c0-1 0-1 1-1 1-1 2-3 2-4 0 0-1-1-2-1 1-2 1-4 0-6v-2s1 0 1-1h1v-1c0-2 1-3 0-5v-2l-1-2 1-1c2 2 2 5 2 7v2c2-3 2-7 2-10z" class="o"></path><path d="M59 527h0l2 2c-1 1-1 4-2 6v-7-1h0z" class="w"></path><path d="M67 532v-1c0-1 1-2 1-3s0-1 1-1v3l-1 1c1 0 1 1 2 1h1c-1 2-1 3-1 5h1c0-1 0-1 1-2v2h1 2c1 0 1 1 2 1h1 5 1s2 1 3 1c0-1 0-1 1-1 1 1 1 1 2 1v1l-12-1-12-1c0-2 1-4 1-6z" class="l"></path><path d="M67 532v-1c0-1 1-2 1-3s0-1 1-1v3l-1 1c1 0 1 1 2 1 0 1-1 3-1 5h-1-1c0-2 1-3 0-5z" class="D"></path><path d="M72 500l1 4c0-1 1 0 1-1v6h1c0-1 0-1 1-2v1c0 2 0 3-1 4 0 2-1 3-1 5 0 1-1 3-1 5 0 3-2 7-2 10h-1c-1 0-1-1-2-1l1-1v-3h0c0-1 1-1 2-2 0-1-1-2 0-3v-1c-2-1 1-7 1-9l-1-1v2l-1-1v-2c2-3 2-7 2-10z" class="X"></path><path d="M71 521c0-2 1-4 2-6v1 3c-1 2-1 4-2 6 0 2-1 4-1 7-1 0-1-1-2-1l1-1v-3h0c0-1 1-1 2-2 0-1-1-2 0-3v-1z" class="Y"></path><path d="M97 538c1-1 3-1 4-2l1 2v1h0l1 1h1c1 0 2-1 3-1 2 0 3 1 5 2h1-4v1h1c1 1 1 2 2 3-2-1-3-1-5-1-3 0-5 1-8 1h-3c-2 1-3 1-4 2l-3-1v1c-2 0-4 0-6-1 0-1 0-1-1-2v-1h-3c-1-1-1-1-3-1-2-1-5 0-7-1h5l1-1h-1-2 3c1 0 2-1 3-1l12 1v-1h0c1-1 1-1 2-1s1 0 2 1l1-1h2z" class="S"></path><path d="M107 539c2 0 3 1 5 2h-6c-1 0-2 1-4 1h-1-2c1 0 2 0 3-1 0-1 0-1 1-1h1c1 0 2-1 3-1z" class="O"></path><path d="M112 541h1-4v1h1c1 1 1 2 2 3-2-1-3-1-5-1h-1v-3h6z" class="R"></path><path d="M82 544c1 0 2 0 3 1 2 1 5-1 8-1 1 0 2 1 3 1-2 1-3 1-4 2l-3-1v1c-2 0-4 0-6-1 0-1 0-1-1-2z" class="C"></path><defs><linearGradient id="S" x1="96.329" y1="539.877" x2="95.308" y2="537.29" xlink:href="#B"><stop offset="0" stop-color="#6b6669"></stop><stop offset="1" stop-color="#767a76"></stop></linearGradient></defs><path fill="url(#S)" d="M97 538c1-1 3-1 4-2l1 2v1h0l1 1h1-1c-1 0-1 0-1 1-1 1-2 1-3 1h-3 0c-1-1-2-1-4-1h-2v-1-1h0c1-1 1-1 2-1s1 0 2 1l1-1h2z"></path><path d="M102 539h0l1 1h1-1c-1 0-1 0-1 1-1 1-2 1-3 1h-3 0c-1-1-2-1-4-1h-2c4-1 8-1 12-2z" class="D"></path><path d="M78 539l12 1v1h2c2 0 3 0 4 1h0c-3 1-6 1-9 1l-2 2c-1-1-2-1-3-1v-1h-3c-1-1-1-1-3-1-2-1-5 0-7-1h5l1-1h-1-2 3c1 0 2-1 3-1z" class="Y"></path><path d="M82 543c1 0 3-1 5 0l-2 2c-1-1-2-1-3-1v-1z" class="M"></path><path d="M132 564l13-1h0c1 1 1 0 2 1h-1 0 2c3 0 5 0 7 1h2-2l-1 1-16 3-1 2h0c-2 0-5 1-6 2h2c1 1 1 0 1 1l-1 1h-2c-1-1-1-1-2-1h-3c-4 1-9 2-13 2h-8c-2 1-6 0-8 0v-1l-6-1c-2 0-5 0-7 1h-7l-1-1c-1 0-2 0-3-1l-7-1c-4 0-9-1-13-1-1 0-3 0-4-1h1c2-1 5 0 8 0h0 6l1-3h3 3c1 0 2 1 3 1s1 0 2-1c0 0 0-1-1-1l14 2h4 2c5 1 11 1 16 0 3 0 7 0 9-2 1 0 2 0 3-1l9-1z" class="w"></path><path d="M128 572c3-1 7-3 10-3l-1 2h0c-2 0-5 1-6 2-2 0-2 0-3-1zm-31 3c5 1 11 0 16 0v1h-8c-2 1-6 0-8 0v-1z" class="E"></path><path d="M76 574c3-1 7-1 10 0h5c-2 0-5 0-7 1h-7l-1-1z" class="Y"></path><path d="M128 572c1 1 1 1 3 1h2c1 1 1 0 1 1l-1 1h-2c-1-1-1-1-2-1h-3c-4 1-9 2-13 2v-1c5-1 10-1 15-3z" class="D"></path><defs><linearGradient id="T" x1="74.42" y1="567.87" x2="66.07" y2="569.117" xlink:href="#B"><stop offset="0" stop-color="#878686"></stop><stop offset="1" stop-color="#a19e9e"></stop></linearGradient></defs><path fill="url(#T)" d="M65 567h3 3c1 0 2 1 3 1l1 1c-1 1-2 1-2 1h-9l1-3z"></path><path d="M76 570h0c2-3 9-1 12-1h1 0c1 1 1 1 2 1h1v1h0l-16-1z" class="F"></path><path d="M75 566l14 2h4c-1 0-2 0-4 1h-1c-3 0-10-2-12 1h0-3s1 0 2-1l-1-1c1 0 1 0 2-1 0 0 0-1-1-1z" class="Q"></path><path d="M115 568l1 1h4l7-1h1c-8 2-16 3-25 4h-5c1-1 1-1 2-1 2 0 3 0 6-1l5-1c1 0 3 0 4-1z" class="F"></path><path d="M93 568h2c5 1 11 1 16 0v1l-5 1c-3 1-4 1-6 1-1 0-1 0-2 1-2 0-4-1-6-1h0v-1h-1c-1 0-1 0-2-1h0-1 1c2-1 3-1 4-1z" class="R"></path><path d="M89 569c1 0 2 0 3 1 4 1 10 0 14 0-3 1-4 1-6 1-1 0-1 0-2 1-2 0-4-1-6-1h0v-1h-1c-1 0-1 0-2-1h0z" class="B"></path><defs><linearGradient id="U" x1="144.341" y1="566.444" x2="136.822" y2="561.757" xlink:href="#B"><stop offset="0" stop-color="#8c8a8c"></stop><stop offset="1" stop-color="#a8a8a6"></stop></linearGradient></defs><path fill="url(#U)" d="M132 564l13-1h0c1 1 1 0 2 1h-1 0l-10 2c-3 1-5 2-8 2h-1l-7 1h-4l-1-1c-1 1-3 1-4 1v-1c3 0 7 0 9-2 1 0 2 0 3-1l9-1z"></path><path d="M123 565l9-1c-1 1-1 1-2 1-5 1-10 3-15 3-1 1-3 1-4 1v-1c3 0 7 0 9-2 1 0 2 0 3-1z" class="B"></path><path d="M572 601s-1-1-2-1h-1v-1c2 1 4 0 5 0h1 1c1 1 6 1 8 0h-5-6c1-1 2 0 3-1h1 2v-3c1-1 2-1 3-2h2c2 1 5 0 7 0 4 0 6 1 9 1 1 0 2 1 3 1 1 1 1 1 2 1 1 1 2 1 2 2h1c2 1 4 1 5 2 2 1 3 1 5 1 1 0 2-1 3-1v1c2 1 5 1 7 1h0c1-1 1-1 2-1v-1l-1-1h-1l3-1c1 0 1 1 1 2 1 0 3 0 4-1h2c1 0 1 1 2 1l-1 1c1 0 2 1 3 1h4v1c-1 0-4 0-5 1h-1c-1 0-1 1-2 1h9c0 1 1 2 2 2s1 0 2-1v1c-1 0-2 0-2 1h-2c-2-1-5 0-8 0l-1 1c-2 0-6 0-8 1h-4l-9 1-1-1c-1 0-3 1-4 1v-1h-3c-1-1-2-1-3-1-2 0-2 0-4-1h0c-1 1-1 1-2 1h-1-2-3v-1-1h13c-2-1-4 0-6-1v-1c-1 0-2 0-3-1h0c3 0 3 0 4-2v-1c-10 0-20 1-30 0z" class="P"></path><path d="M588 594h1c0 1 0 1-1 2h-1v-1l1-1z" class="U"></path><path d="M628 599l3-1c1 0 1 1 1 2 1 0 3 0 4-1h2c1 0 1 1 2 1l-1 1c1 0 2 1 3 1h4v1c-1 0-4 0-5 1h-1c-1 0-1 1-2 1-4 0-8 0-12 1l-19 1c-2-1-4 0-6-1v-1c-1 0-2 0-3-1h16l-3-2c1 0 2 1 3 2h2l-2-1 1-1v-1h-2v-1c2 1 3 1 5 1 1 0 2-1 3-1v1c2 1 5 1 7 1h0c1-1 1-1 2-1v-1l-1-1h-1z" class="D"></path><path d="M628 599l3-1c1 0 1 1 1 2 1 0 3 0 4-1h2c1 0 1 1 2 1l-1 1c-4 1-7 1-10 1 1 1 2 1 3 1s2 0 2 1h-7c-4-1-9 0-13 1h-13c-1 0-2 0-3-1h16l-3-2c1 0 2 1 3 2h2l-2-1 1-1v-1h-2v-1c2 1 3 1 5 1 1 0 2-1 3-1v1c2 1 5 1 7 1h0c1-1 1-1 2-1v-1l-1-1h-1z" class="B"></path><path d="M628 599l3-1c1 0 1 1 1 2l-3-1h-1z" class="h"></path><path d="M613 600c2 1 3 1 5 1h2v1h-5v-1h-2v-1z" class="L"></path><path d="M638 605h9c0 1 1 2 2 2s1 0 2-1v1c-1 0-2 0-2 1h-2c-2-1-5 0-8 0l-1 1c-2 0-6 0-8 1h-4l-9 1-1-1c-1 0-3 1-4 1v-1h-3c-1-1-2-1-3-1-2 0-2 0-4-1h0c-1 1-1 1-2 1h-1-2-3v-1-1h13l19-1c4-1 8-1 12-1z" class="j"></path><path d="M594 608h8c-1 1-1 1-2 1h-1-2-3v-1z" class="B"></path><path d="M602 608h5 2 6c2 1 4 0 5 1l-4 1c-1 0-3 1-4 1v-1h-3c-1-1-2-1-3-1-2 0-2 0-4-1z" class="R"></path><path d="M609 610c0-1 0-1 1-1 2 1 4 0 6 0v1c-1 0-3 1-4 1v-1h-3z" class="N"></path><defs><linearGradient id="V" x1="632.816" y1="611.454" x2="629.439" y2="605.899" xlink:href="#B"><stop offset="0" stop-color="#3c363c"></stop><stop offset="1" stop-color="#4e504d"></stop></linearGradient></defs><path fill="url(#V)" d="M628 607c4 0 8 0 11 1l-1 1c-2 0-6 0-8 1h-4l-9 1-1-1 4-1c3 1 6-1 8-2z"></path><path d="M638 605h9c0 1 1 2 2 2s1 0 2-1v1c-1 0-2 0-2 1h-2c-2-1-5 0-8 0-3-1-7-1-11-1l-2-1c4-1 8-1 12-1z" class="g"></path><path d="M537 387l2 1h0c0 1 0 2 1 2v1c1 1 1 1 1 2h1l2 4 2 4 1-1c4 4 7 10 9 15 1 1 2 3 2 4 2 5 4 8 7 13h0-1v1l-2-1v-2h-1c-1 1-1 2-1 3 0 0 0 1-1 1v1c0 2 0 3 1 4s1 3 1 4c1-1 1-1 2-1v1l-1 1c0 1 1 3 1 4-1 0-1-1-1-2h0-2s-1-1-2-1h-1c-1-1-1-2-2-3v-2l-1 1v-1h-1c-1-1-2-4-3-7l1 1h-1 0c0 1 1 2 1 3-1 0-2-1-2-2v-1h-1v1c1 0 1 1 1 1h0v1h0c-1-1-2-4-2-5-1-2-1-4-2-6 0-1-1-1-1-2v-2c-1-1-1-2-1-3h0 0 0l1-1c0-2-1-3-3-5v-2l1-1c0-3-4-5-5-8-1-2-1-3-3-4v-1h-1v-1-1h-1c0-1-1-2-2-2h-1c1-1 1-1 1-2v-2h3v-1c1 0 1 1 2 1 0 1 0 1 1 1v-1h1 0v-2z" class="t"></path><g class="a"><path d="M540 397h1l1-1c0 1 1 1 2 1l2 4h-1-1v1h-1l-3-5z"></path><path d="M537 387l2 1h0c0 1 0 2 1 2v1c1 1 1 1 1 2h1l2 4c-1 0-2 0-2-1l-1 1h-1l-1 1h-1l2 2h-1l-3-2h0c1 1 2 2 2 4h-1c-1-2-1-3-3-4v-1h-1v-1-1h-1c0-1-1-2-2-2h-1c1-1 1-1 1-2v-2h3v-1c1 0 1 1 2 1 0 1 0 1 1 1v-1h1 0v-2z"></path></g><path d="M541 393h1l2 4c-1 0-2 0-2-1l-1 1h-1l-1 1-1-1v-1c1 0 2 0 3-1l-1-2h1z" class="j"></path><path d="M537 387l2 1h0c0 1 0 2 1 2v1c1 1 1 1 1 2h-1c-1-1-2-2-3-4h0v-2z" class="g"></path><path d="M530 391l1 1c1 0 1-1 2-1l1 1 1-1 1 1v-1h1c0 1 1 1 1 2v1l-1-2-2 1h0-2 0v1c0 1 0 1 1 1v-1c2 1 3 3 4 4l2 2h-1l-3-2h0c1 1 2 2 2 4h-1c-1-2-1-3-3-4v-1h-1v-1-1h-1c0-1-1-2-2-2h-1c1-1 1-1 1-2z" class="I"></path><path d="M550 422v-1c0-1 0-1-1-2h0 1l1 1v-1-1h0l-1-1h1v-1h1c0 1 1 2 1 3 1 0 1-1 1-2 0 0 1 2 2 3 1 2 1 3 3 5 1-1-2-4-2-6h1c2 5 4 8 7 13h0-1v1l-2-1v-2h-1c-1 1-1 2-1 3 0 0 0 1-1 1-1-3-1-6-2-8l-2-2h0v2 1l-1-1v-1h-1c0 1 0 2 1 3h0l-1 1-1-3c0-1-2-4-2-4z" class="c"></path><defs><linearGradient id="W" x1="553.529" y1="441.717" x2="557.745" y2="428.29" xlink:href="#B"><stop offset="0" stop-color="#252526"></stop><stop offset="1" stop-color="#424241"></stop></linearGradient></defs><path fill="url(#W)" d="M553 429l1-1h0c-1-1-1-2-1-3h1v1l1 1v-1-2h0l2 2c1 2 1 5 2 8v1c0 2 0 3 1 4s1 3 1 4c1-1 1-1 2-1v1l-1 1c0 1 1 3 1 4-1 0-1-1-1-2h0-2s-1-1-2-1h-1c-1-1-1-2-2-3h1v-2l-1-2-1-5-1-1v-3z"></path><path d="M558 438l-1-1c0-1-1-2-1-3 0 0 0-1-1-1v-4l1 1 1 3h0l1 5z" class="c"></path><path d="M557 433v-3c1 1 1 2 1 3s1 2 1 2c0 2 0 3 1 4s1 3 1 4c1-1 1-1 2-1v1l-1 1c0 1 1 3 1 4-1 0-1-1-1-2h0-2s-1-1-2-1v-2c-1-2-1-3-1-4h0 1v-1l-1-5z" class="e"></path><path d="M541 413v-2l1-1c1 2 3 4 4 6 1 1 2 2 3 4 0 0 0 1 1 2 0 0 2 3 2 4l1 3v3l1 1 1 5 1 2v2h-1v-2l-1 1v-1h-1c-1-1-2-4-3-7l1 1h-1 0c0 1 1 2 1 3-1 0-2-1-2-2v-1h-1v1c1 0 1 1 1 1h0v1h0c-1-1-2-4-2-5-1-2-1-4-2-6 0-1-1-1-1-2v-2c-1-1-1-2-1-3h0 0 0l1-1c0-2-1-3-3-5z" class="s"></path><path d="M550 426l1 1 1-1 1 3v3l1 1c-1 0-1 1-2 1 1 1 1 2 1 3h0c-1-3-3-6-4-10l1-1z" class="W"></path><path d="M551 427l1-1 1 3v3l1 1c-1 0-1 1-2 1 0-2 0-5-1-7z" class="q"></path><path d="M546 416c1 1 2 2 3 4 0 0 0 1 1 2 0 0 2 3 2 4l-1 1-1-1-1 1c0-2-2-5-3-8v-3z" class="i"></path><path d="M549 420s0 1 1 2c0 0 2 3 2 4l-1 1-1-1c0-1-1-2-1-3s0-1-1-2c0-1 0-1 1-1z" class="V"></path><path d="M541 413v-2l1-1c1 2 3 4 4 6v3l-1 1 3 9c1 1 1 2 1 4-2-5-3-10-6-14h0 0 0l1-1c0-2-1-3-3-5z" class="r"></path><path d="M107 387c1-2 1-5 2-7v-1c1 2 0 4 1 5v3l2 8c1 2 2 3 2 5l2 5h1l-1-4h1c0 1 1 6 2 7h2 0c0 1-1 2 0 3h0-1v2 1c0 1 1 2 1 3l2-1h0 0c0 2 2 3 2 4v3 1 2h-1v1h-2 0c-1-1 0-1 0-1h-2c-1 0-1-1-2-1h0-2 0-5-10-4v1h-2c-1-2 3-8 2-10h0v-3l2-4 8-22z" class="U"></path><path d="M101 425c2-1 4-1 6-1v-6l1-1h0v4 2h1v-1s0 1 1 1l1 2h-10z" class="C"></path><path d="M109 422v-1c1-2-1-6 1-7h0l1-1c1 1 1 2 1 3l1 4v2l-1 1c0-1-1-1-1-2h-1v2c-1 0-1-1-1-1zm-3-19h1v8 3l-1 1v4c0 1 0 3-1 4 0-3 0-8 1-10-1-1-2-2-2-3 1-1 1-5 1-7h1z" class="B"></path><path d="M105 403h1v10c-1-1-2-2-2-3 1-1 1-5 1-7z" class="f"></path><path d="M99 409c1 0 1 0 1 1h0l1 1v3c0 1-1 4-2 5l-1 2v1 1l-1 1v1 1h-2c-1-2 3-8 2-10h0v-3l2-4z" class="M"></path><path d="M107 387c1 1 1 2 1 4v1c-1 1-1 3-1 5h0c-1 1-2 1-2 2l1 2c-1 1-1 1-2 1h0l1 1c0 2 0 6-1 7h-1v-1c1-1 1-2 1-3v-1l-3 6-1-1h0c0-1 0-1-1-1l8-22z" class="O"></path><path d="M107 387c1-2 1-5 2-7v-1c1 2 0 4 1 5v3l2 8c1 2 2 3 2 5l2 5h1l-1-4h1c0 1 1 6 2 7h2 0c0 1-1 2 0 3h0-1v2 1c0 1 1 2 1 3l2-1h0 0c0 2 2 3 2 4v3 1 2h-1v1h-2 0c-1-1 0-1 0-1h-2c-1 0-1-1-2-1v-2c-1-1-2-3-2-4l-1-3v-1c-1-2-1-3-1-4l-1 1 1 1v1 2l1 1v4c1 0 1 1 1 2h-1c-1-1-1-4-2-5 0-5-1-10-3-14h-1c0-1 0-1-1-2v-1c-1-1-1-2-1-4s0-4 1-5v-1c0-2 0-3-1-4z" class="B"></path><path d="M108 401v-9c1 4 1 8 2 12h-1c0-1 0-1-1-2v-1z" class="E"></path><path d="M113 412c0-4-1-7-2-10-1-2-1-4-1-6 0-1 0-3-1-4v-3l1-1v2 1c1 1 1 2 1 3 0 2 0 5 1 7 0 2 1 5 2 7v3l-1 1z" class="K"></path><path d="M119 420c-1-2-2-4-2-6 0-4-2-9-3-12v-2l2 5h1l-1-4h1c0 1 1 6 2 7h2 0c0 1-1 2 0 3h0-1v2 1c0 1 1 2 1 3 1 1 1 1 1 2l-1 1c-1-2-2-6-3-7h-1l2 6v1z" class="R"></path><path d="M116 401h1c0 1 1 6 2 7h2 0c0 1-1 2 0 3h0-1v2 1l-1-3c-1-2-2-3-3-6h1l-1-4z" class="L"></path><path d="M119 419l-2-6h1c1 1 2 5 3 7l1-1c0-1 0-1-1-2l2-1h0 0c0 2 2 3 2 4v3 1 2h-1v1h-2 0c-1-1 0-1 0-1h-2c0-2 0-4-1-6v-1z" class="Q"></path><path d="M124 425v-1l-1-1c-1-1-1-1-1-2 1-1 1-1 3-1v3 1l-1 1z" class="I"></path><path d="M119 419c1 2 2 4 4 5v1h1l1-1v2h-1v1h-2 0c-1-1 0-1 0-1h-2c0-2 0-4-1-6v-1z" class="h"></path><path d="M122 426c1-1 1-1 2 0h0v1h-2 0c-1-1 0-1 0-1z" class="k"></path><path fill="#fff" d="M164 560c1 0 2-1 3 0h1 1 0c1 0 1 0 2 1h1c1 0 2 0 3 1l3 1h4l12 2c2 0 4 1 6 1h3s1 0 1 1h3l-1 1c2 0 3 0 4-1l-2 11h0l-2 6h-2c-1-1-2-1-3 0l-7-2-14-2c-1 0-2 1-2 1h-1-1-1-1-1c-1 0-1 0-3-1-1 0-2 0-3-1h1l-5-3c0-1 0-2 1-2h1c1 0 1 1 2 0v-1-1c1 1 2 1 2 1h1v-1h1 0c0-1-2-2-3-3l-3-3h0l1-1 1 1 1-1h-1 5 1 1c-1-2-2-2-4-2h0l-2-2h0l-4-1z"></path><path d="M183 567c3 0 5 1 8 0h4c1 0 2 0 4 1h0l-1 1c-1 0 0 0-1 1l1 1h1c1 0 3 1 5 1-6 0-11 0-16-1v-2c-2 0-3-1-5-1 0-1-1 0-2-1h0 2z" class="C"></path><path d="M195 567c1 0 2 0 4 1h0l-1 1c-1 0 0 0-1 1l1 1c-2 0-3-1-4-1v-1h2 1l1-1-3-1zm-27-6l6 2c0 1 1 2 2 3h1 0 3 3 0v1h-2 0c1 1 2 0 2 1 2 0 3 1 5 1v2l-18-4-3-1 1-1h-1 5 1 1c-1-2-2-2-4-2h0l-2-2h0z" class="D"></path><path d="M168 561l6 2c0 1 1 2 2 3h1 0c1 0 2 0 3 1h0c-3 1-9-1-12-2h-1 5 1 1c-1-2-2-2-4-2h0l-2-2h0z" class="Y"></path><path d="M166 565l1 1 3 1h-1-2s1 1 2 1c1 1 1 1 1 2 1 1 5 2 7 2l7 2c2 1 3 2 5 2-2 0-5 0-7-1l-1 2-3-1-9-3h1v-1h1 0c0-1-2-2-3-3l-3-3h0l1-1z" class="b"></path><path d="M169 573h1v-1h1c1 0 1 1 2 1s9 2 9 2l-1 2-3-1-9-3z" class="C"></path><path d="M164 560c1 0 2-1 3 0h1 1 0c1 0 1 0 2 1h1c1 0 2 0 3 1l3 1h4l12 2c2 0 4 1 6 1h3s1 0 1 1h3l-1 1c-1 0-6 0-7 1v2h-1l-1-1c1-1 0-1 1-1l1-1h0c-2-1-3-1-4-1h-4c-3 1-5 0-8 0v-1h0-3-3 0-1c-1-1-2-2-2-3l-6-2-4-1z" class="E"></path><path d="M164 560c1 0 2-1 3 0h1 1 0c1 0 1 0 2 1h1c1 0 2 0 3 1l3 1h4l12 2h-1c-6 1-12-1-19-2l-6-2-4-1z" class="B"></path><path fill="#fff" d="M167 573v-1c1 1 2 1 2 1l9 3 3 1 1-2c2 1 5 1 7 1 6 1 13 1 19 2h0l-2 6h-2c-1-1-2-1-3 0l-7-2-14-2c-1 0-2 1-2 1h-1-1-1-1-1c-1 0-1 0-3-1-1 0-2 0-3-1h1l-5-3c0-1 0-2 1-2h1c1 0 1 1 2 0v-1z"></path><path d="M182 575c2 1 5 1 7 1 6 1 13 1 19 2h0-8c-6 0-13 0-19-1l1-2z" class="B"></path><path d="M167 573v-1c1 1 2 1 2 1l9 3c-2 0-7-1-8 0 1 1 2 1 3 2 2 0 5 1 7 2-1 0-2 1-2 1h-1-1-1-1-1c-1 0-1 0-3-1-1 0-2 0-3-1h1l-5-3c0-1 0-2 1-2h1c1 0 1 1 2 0v-1z" class="E"></path><path d="M163 576c0-1 0-2 1-2h1v1c1 1 3 1 5 2l-1 1h-2 1l1 1h0 1c1 0 2 0 3 1l4 1h-1-1-1-1c-1 0-1 0-3-1-1 0-2 0-3-1h1l-5-3z" class="U"></path><path d="M85 427h3 0c-1 1 0 2 0 3l-1 1v4 3 4c-1 1 0 3 0 4v7c1 2 0 3 0 5 0 1 0 2-1 3h0 0l-1-1c0 1 0 1-1 1 1 2 0 3 0 5v1c0 2-1 5 0 7v1l1 3h-1-1l-6 1c-1 1-1 1-1 2l1 1c0 2-1 2-2 4v13c0 2 0 3 1 5v4-1c-1 1-1 1-1 2h-1v-6c0 1-1 0-1 1l-1-4v-5l1-15v-48l2-3h1c1 0 2 0 2-1 1-1 2-1 3-1h2 1 1z" class="Y"></path><path d="M87 458c0 1 0 2-1 3h0 0l-1-1v-2h2z" class="K"></path><path d="M80 444h1c1 2 0 9 0 12h-1-1 0c0-4 0-8 1-12z" class="a"></path><path d="M84 461c1 2 0 3 0 5v1c0 2-1 5 0 7v1l1 3h-1c-1-1-1-2-2-3v-1-2-5c0-2 1-4 2-6z" class="O"></path><g class="b"><path d="M76 454v1c0 1 0 1 1 2v1c1 2 2 3 2 5v1h-1c0-1 0-1-1-1v3h-1v1l-1-1v-5c0-3 0-5 1-7z"></path><path d="M76 467c1 0 2 0 3 1h0c-2 0-3 0-4 1 1 1 1 1 2 1h1v1c-1 0-3 0-3 1 0 0 1 1 2 1h2v1h-3v1h3 0c0 1-1 1 0 2s1 1 3 1c1-1 0-2 0-3 1 1 1 2 2 3h-1l-6 1c-1 1-1 1-1 2l1 1c0 2-1 2-2 4v13c-1-5 0-9 0-14 0-1-1-3 0-5 0-1 1-1 1-2h-1v-2-2-1-1-2-2h1v-1z"></path></g><path d="M85 427h3 0c-1 1 0 2 0 3l-1 1v4 3h-2-4-1-1-1c-1 0-1 1-2 1-1 5-1 11 0 15-1-1 0-3-1-5 0-2-1-6 0-8h0v-2l-1-1h1v-5-3l1-1c1 0 2 0 2-1 1-1 2-1 3-1h2 1 1z" class="E"></path><path d="M85 427h3 0c-1 1 0 2 0 3l-1 1-1-3-2-1h1z" class="F"></path><path d="M83 431h2c0 1 1 5 0 6v1h-4-1c0-1 0-1-1-1 1-1 1-1 2-1h1 1v-3-2z" class="C"></path><path d="M79 437v1h-1l-1-1v-3c1-1 0-2 1-3 2-1 4 0 5 0v2 3h-1-1c-1 0-1 0-2 1z" class="M"></path><path d="M73 432l2-3h1l-1 1v3 5h-1l1 1v2h0c-1 2 0 6 0 8 1 2 0 4 1 5-1 2-1 4-1 7v5l1 1v1h-1v2 2 1 1 2 2h1c0 1-1 1-1 2-1 2 0 4 0 5 0 5-1 9 0 14 0 2 0 3 1 5v4-1c-1 1-1 1-1 2h-1v-6c0 1-1 0-1 1l-1-4v-5l1-15v-48z" class="U"></path><path d="M447 309v-2c1 0 1 0 2-1h1v1s0 1 1 1v1l2 3h1c0-2-3-4-2-5h0c2 3 6 7 7 10 1 1 1 2 3 3 0 0 0 1 1 1l3 3h-1c0 1 0 0-1 1 2 0 3 1 4 0l1 1 2 3h-1v1l1 1c-1 1-2 0-2 2l3 3c1 1 2 2 2 3 1 1 2 1 2 2 1-1 1-1 1-2v-1h1l4 4c1 1 2 1 2 1l1 1c3 2 5 3 7 5 1 1 0 1 0 2h1l1 1c0 1 0 1-1 2h-1 0c0 1 0 1 2 2h0 0c1 1 1 2 2 3h1v1c0 1 1 1 1 2v1s1 0 1 1h-1l-1-1s-1-1-2-1-1-1-2-2h0c-1 0-1 0-2-1h0l-1 1 1 1c0 1 1 1 0 2h0l-2-1c-1-1-1-1-1-2h1v-1l-2-1-1 1 2 1v1c-1 0-1 0-2 1h0v2h-1c0 1-1 2-2 2l-1-1h1v-1l-1-2h1c-1-2-2-2-3-4 0-1-1-1-2-2-1-2-1-3-3-4h0c0 1-1 1-2 1l-1-1-1-1c0-1 0-1-1-2l-3-3-1-3h-2c0-1-1-2-2-3h-1c0-1-1-2-2-3 0-1 0-1-1-2h1c0-2-3-3-3-5h1v1h1v-1c0-1-1-2-1-3h0l-1-1-4-5-1-2v-3-1c0-1 0-1-1-1 0-1-1-2-2-4h0l-1-1z" class="I"></path><path d="M473 345l-1-1v-1l3 3h0-1l-1-1z" class="G"></path><path d="M475 346v-1h1l1 1v1 1c-1-1-2-1-2-2h0z" class="m"></path><path d="M456 326v-2c1 0 2 1 2 2l1 1-1 1v-1h-1l-1-1z" class="h"></path><path d="M470 329c0-1-2-3-3-3h-1-2l1 1h-1v-1h-1c-1-1-1-2-2-3v-1c1 1 2 1 3 2v1c2 0 3 1 4 0l1 1 2 3h-1z" class="R"></path><path d="M448 310c2 0 5 5 5 6h0c0 1 1 1 1 1l-1 1c0-1 0 0-1-1l-1 2v-3-1c0-1 0-1-1-1 0-1-1-2-2-4h0z" class="G"></path><path d="M463 331c1 2 2 3 3 4 1 2 5 7 4 8h-1v-1c0-1-1-1-1-1l-3-3c-1-2 0-4-3-5v-1l1-1z" class="h"></path><path d="M458 326l1-1 1 1c0 1-1 1-1 2h1v-1c2 1 3 3 3 4h0l-1 1v-1h-2v-1l-1 1v1c1 0 1 1 2 1h0c1 2 2 3 3 5v1l1 1c2 1 3 5 6 6 1 0 1 0 2-1l1 1h1c0 1 1 1 2 2 0 0 1 0 1 1v1c-1 0-1 0-2-1v1 1h-1v1h0c0 1-1 1-2 1l-1-1-1-1c0-1 0-1-1-2l-3-3-1-3h-2c0-1-1-2-2-3h-1c0-1-1-2-2-3 0-1 0-1-1-2h1c0-2-3-3-3-5h1v1h1v-1c0-1-1-2-1-3h0 1v1l1-1-1-1z" class="j"></path><path d="M462 340h0c-1-2-2-3-2-4h0c2 1 4 5 6 7h-2c0-1-1-2-2-3z" class="R"></path><path d="M475 346c0 1 1 1 2 2 0 0 1 0 1 1v1c-1 0-1 0-2-1v1h-2v-1c-1 1-1 1-2 1l-1-1v-1c1 0 2-1 3 0l1-1-1-1h1z" class="I"></path><path d="M477 346l-1-1h0c-1-1-2-1-3-2 1-1 0-2 0-3 2 0 3 3 4 4h1c1 2 2 1 3 3h1c1-1 1-2 1-2l1 1 1-1-3-3c1 1 2 1 2 1l1 1c3 2 5 3 7 5 1 1 0 1 0 2h1l1 1c0 1 0 1-1 2h-1 0c0 1 0 1 2 2h0 0c1 1 1 2 2 3h1v1c0 1 1 1 1 2v1s1 0 1 1h-1l-1-1s-1-1-2-1-1-1-2-2h0c-1 0-1 0-2-1h0l-1 1 1 1c0 1 1 1 0 2h0l-2-1c-1-1-1-1-1-2h1v-1l-2-1-1 1 2 1v1c-1 0-1 0-2 1h0v2h-1c0 1-1 2-2 2l-1-1h1v-1l-1-2h1c-1-2-2-2-3-4 0-1-1-1-2-2-1-2-1-3-3-4v-1h1v-1-1c1 1 1 1 2 1v-1c0-1-1-1-1-1v-1-1z" class="R"></path><path d="M497 360c0 1 1 1 1 2v1c-1-1-2-2-2-3h1z" class="H"></path><path d="M483 353c1-1 1-2 2-3 2 1 3 3 5 4 0 1 0 1 1 1 0 1 1 1 2 2l1 1v1h-1c0-1-1 0-2 0s-1 0-1-1v-1c-2-3-3-4-5-4h-2z" class="I"></path><defs><linearGradient id="X" x1="479.333" y1="356.922" x2="483.975" y2="353.158" xlink:href="#B"><stop offset="0" stop-color="#686768"></stop><stop offset="1" stop-color="#7e7c7d"></stop></linearGradient></defs><path fill="url(#X)" d="M477 347c2 2 3 3 4 5 1 0 1 1 1 1h1 2l-1 1 1 1c0 2 0 2 1 4l2 1v1c-1 0-1 0-2 1h0v2h-1c0 1-1 2-2 2l-1-1h1v-1l-1-2h1c-1-2-2-2-3-4 0-1-1-1-2-2-1-2-1-3-3-4v-1h1v-1-1c1 1 1 1 2 1v-1c0-1-1-1-1-1v-1z"></path><path d="M483 362h2l-1 1 1 1c0 1-1 2-2 2l-1-1h1v-1l-1-2h1z" class="m"></path><path d="M513 375v-1h1 3 1l1 2h3 1l1-1v-1c1 0 1 1 2 2h0v1l3 3h2c0 1 1 2 1 2 1 1 2 2 2 3 1 0 2 1 3 2v2h0-1v1c-1 0-1 0-1-1-1 0-1-1-2-1v1h-3v2c0 1 0 1-1 2h1c1 0 2 1 2 2h1v1 1h1v1c2 1 2 2 3 4 1 3 5 5 5 8l-1 1v2h-1c-1-1-1-1-2-1-1-1-1-3-3-4 0 1-1 1-1 2l2 2c0 1 0 1 1 2v2c1 1 1 1 1 2v3 2h1c0 1 1 1 1 2h1 0c0 1 1 1 1 2-1 0-2-1-4-1v-1h-1c-1 0-1 0-2-1l-1-1c0-1 0-1-1-2s-1-2-2-3l-2-2c0 1 0 1-1 2 0-1-1-2-1-2l1-2-2-1-1 1-1-2c-1-1-1-2-2-3 0-1 0-1-1-2l-4-5v-1l-1-1v1l-2-4-1 1h0c-1-1-1-1-2-1l-1 2v1h-1v-1l1-2-1-1v-1h-1c0 1 0 1-1 2v-1l-1 1-1-1 1-2c0-1-1-1-1-2h-1 0c0 1-2 2-2 3v2c-1-1-2-2-2-3v-1l-1-1c1-1 0-1 1-1v-1-1c0-2-1-2-1-3l1-1c1 2 1 2 1 3 1 1 1 1 2 1 1-1 0 0 1 0s1 1 1 2c0 0 1 1 2 1 0-1 0-1 1-2v-1h-1l1-1h1 0c1 0 1 1 1 0s0-1-1-2l-1-1v-1h2c0 1 1 2 1 2v1l2 1-1-3h1 0l-1-2c0-1 0-1-1-2 0-1-1-2-2-3s-1-1-1-2c1-1 2-1 3-2l2 1z" class="h"></path><path d="M512 393h0c1 1 1 1 1 3l-2-2 1-1zm4 1v-1h1c1 0 1 0 1 2l-1 1-1-2zm5 5h1v-1h1c1 2 2 3 2 5l-2-1c0-1-1-1-2-2v-1z" class="a"></path><path d="M500 389c0-2-1-2-1-3l1-1c1 2 1 2 1 3 0 2 1 3 1 4l-1 1h0c0 1 0 1-1 1v-1l-1-1c1-1 0-1 1-1v-1-1z" class="g"></path><path d="M512 383c0-1 0-1-1-2 0-1-1-2-2-3s-1-1-1-2c1-1 2-1 3-2l2 1v1h-1c0 1-1 1-2 1 0 1 0 1 1 2h0c1 3 5 5 5 7v1 1c-1 0-1 1-2 0h-1l-1-3h1 0l-1-2z" class="R"></path><path d="M526 398l-1-1-1-1v-3l1 1c2-2 1-1 2 0h2c1 1 1 2 2 3s2 2 3 4l1 2h1c0-2-2-3-2-5 2 1 2 2 3 4 1 3 5 5 5 8l-1 1v2h-1c-1-1-1-1-2-1-1-1-1-3-3-4 0 1-1 1-1 2l2 2c0 1 0 1 1 2v2c1 1 1 1 1 2v3c-1 0-1-1-2-2 0-1 0-2-1-3s-2-2-3-4h1c-1-1-1-1-1-2l-5-10c0-1 0-1-1-2z" class="c"></path><path d="M532 405h1c1 0 1 1 2 1l-2 2c0-1-1-2-1-3z" class="W"></path><path d="M526 398l-1-1-1-1v-3l1 1c2-2 1-1 2 0h2c1 1 1 2 2 3 0 2 3 4 3 7h-1c0-3-2-5-4-7h-1v-1l-1 1 1 1h0-2z" class="Z"></path><path d="M526 398h2 0l-1-1 1-1v1c1 2 2 3 2 6 1 1 2 1 2 2s1 2 1 3v1l1 1 2 2c0 1 0 1 1 2v2c1 1 1 1 1 2v3c-1 0-1-1-2-2 0-1 0-2-1-3s-2-2-3-4h1c-1-1-1-1-1-2l-5-10c0-1 0-1-1-2z" class="k"></path><path d="M538 418l-3-3c0-1-1-2-2-3l1-1 1 2 1-1c0 1 0 1 1 2v2c1 1 1 1 1 2z" class="i"></path><path d="M516 400c0-1-1-1-1-2l1-1 1 1h0c0-1-1-2-1-2l-1-1c0-1-1-2 0-2l1 1 1 2 1 1c1 0 1 0 2 1s1 0 1 1v1c1 1 2 1 2 2l2 1c0 1 1 1 1 2l2-1c1 1 1 1 1 2s1 2 1 3l2 3c1 2 2 3 3 4s1 2 1 3c1 1 1 2 2 2v2h1c0 1 1 1 1 2h1 0c0 1 1 1 1 2-1 0-2-1-4-1v-1h-1c-1 0-1 0-2-1l-1-1c0-1 0-1-1-2s-1-2-2-3l-2-2c0 1 0 1-1 2 0-1-1-2-1-2l1-2-2-1-1 1-1-2c-1-1-1-2-2-3 0-1 0-1-1-2l-4-5v-1l-1-1z" class="k"></path><path d="M518 397c1 0 1 0 2 1s1 0 1 1v1l-1 1c-1-2-1-3-2-4h0z" class="Z"></path><path d="M523 402l2 1c0 1 1 1 1 2l1 1c-1 3 1 5 2 7h0c-1 0-1 0-2-1v-1c-1-1-1-2-1-3-1-2-2-3-3-4v-2z" class="t"></path><path d="M526 405l2-1c1 1 1 1 1 2s1 2 1 3l2 3c1 2 2 3 3 4s1 2 1 3c1 1 1 2 2 2v2h1c0 1 1 1 1 2h1 0c0 1 1 1 1 2-1 0-2-1-4-1v-1h-1c-1 0-1 0-2-1l-1-1c0-1 0-1-1-2s-1-2-2-3c0-2-1-3-2-5s-3-4-2-7l-1-1z" class="W"></path><path d="M534 418h1c1 1 1 1 1 3h0c-1 0-2-1-2-1v-2z" class="k"></path><path d="M526 405l2-1c1 1 1 1 1 2s1 2 1 3v1l1 3h0c-2-2-3-5-4-7l-1-1z" class="Z"></path><path d="M513 375v-1h1 3 1l1 2h3 1l1-1v-1c1 0 1 1 2 2h0v1l3 3h2c0 1 1 2 1 2 1 1 2 2 2 3 1 0 2 1 3 2v2h0-1v1c-1 0-1 0-1-1-1 0-1-1-2-1v1h-3c-1 0-1 0-1 1h-1-2c0 1 1 1 1 2h-1l-1-1c-2-2-4-4-6-7-1-2-4-3-5-5l-1 1-2-1c-1-1-1-1-1-2 1 0 2 0 2-1h1v-1z" class="G"></path><path d="M529 380c0 1 0 0-1 1 0-1-1-1-1-2-1 1-1 1-2 1 0-1-1-1-1-2h0c1-1 1-1 2-1l3 3z" class="S"></path><path d="M519 384l1-1 1 1h1v-1h-1c-1-1-1-2-2-2s-2-1-2-1c0-2 1-2 1-3h1l2 1v1c1 1 3 2 4 4 2 0 2-1 3 0 2 0 3 2 4 3 1 0 1 0 0-1 0 0 0-1-1-1l1-1 3 3c0 1 1 2 1 3v1c-1 0-1 0-1-1-1 0-1-1-2-1v1h-3c-1 0-1 0-1 1h-1-2c0 1 1 1 1 2h-1l-1-1c-2-2-4-4-6-7z" class="I"></path><path d="M529 386h1v1 1h-2-1v-1l2-1z" class="h"></path><path d="M523 384h1c1 0 1-1 2-1 0 0 1 1 2 1v1h-4c-1 0-1 0-1-1z" class="m"></path><path d="M602 608h0c2 1 2 1 4 1 1 0 2 0 3 1h3v1c1 0 3-1 4-1l1 1 9-1h3l-1 1c0 1 0 0 1 1h2c-2 1-4 2-6 2 2 1 5 1 8 1h0c-1 1-2 1-4 1v1h1c1 1 2 1 3 1 0 1-1 1-1 2h3 4l-1 1c-3 0-8-1-12 1-1 0-3 0-4 1h2 2c-1 2-5 1-7 1s-3 1-5 1c-1 0-4 0-5 1h-5-1l-6 2h-12c-3 1-7 1-10 1h-5v-1c-6 0-10-1-15 0-2-1-5-1-7-1l-1-1c-2-1-3-1-4-2 0-1 1-1 2-1h2l1-1 7 1h1l1-1c-1 0-1 0-2-1h2 0c1 0 1 0 1 1h5v-1c-1 0-1 0-2-1l2-1 1 1c1 0-1-1 1 0h4l1-1c-1 0-2 0-3-1h0c2 0 4 0 6-1 1 0 1 1 2 1h3l1 1h8l1 1h4 0l1-2c-1-1-2-1-4-1h2l1-2v-1l1-1-3-1h1c0-1 1-1 2-1 1-1 4-1 6-1v-1h1c1 0 1 0 2-1z" class="Y"></path><path d="M593 618h1c2 0 4 0 6 1s5 0 8 0l-16 1h0l1-2z" class="H"></path><path d="M599 623h8 2c-1 1-3 1-4 2 0 0-1 0-1 1h-1c-2 0-3 0-4-1l1-1-1-1z" class="T"></path><path d="M594 618v-1l12-1h2 3 0c-2 1-3 1-5 1v1c1 0 2 0 4 1 2 0 3 0 5-1h2 3c-4 1-9 2-12 1-3 0-6 1-8 0s-4-1-6-1z" class="I"></path><path d="M625 614c2 1 5 1 8 1h0c-1 1-2 1-4 1v1h1c1 1 2 1 3 1 0 1-1 1-1 2-1 0-2 0-3-1h0v-1c-2-1-6 0-8 0h-1-3-2c-2 1-3 1-5 1-2-1-3-1-4-1v-1c2 0 3 0 5-1h0 3l11-2z" class="h"></path><path d="M621 618c2 0 6-1 8 0v1h0c1 1 2 1 3 1h3 4l-1 1c-3 0-8-1-12 1-1 0-3 0-4 1h2 2c-1 2-5 1-7 1s-3 1-5 1c-1 0-4 0-5 1h-5c0-1 1-1 1-1 1-1 3-1 4-2h-2c5-1 10-1 15-3 1 0 2 0 3-1l-1-1h-3z" class="B"></path><path d="M575 624l24-1 1 1-1 1c1 1 2 1 4 1l-6 2h-12c-3 1-7 1-10 1 1 0 2-1 3-1 0 0 1-1 2-1h-4c1-1 2-1 3-1l-1-1-1-1s-1 1-2 1v-1z" class="S"></path><path d="M580 627c1-1 3 0 5-1h6c-1 0-2 0-3 1h0c-1 0-1 0-2 1h-1c-3 1-7 1-10 1 1 0 2-1 3-1 0 0 1-1 2-1z" class="P"></path><path d="M599 625c1 1 2 1 4 1l-6 2h-12 1c1-1 1-1 2-1h0c1-1 2-1 3-1l8-1z" class="E"></path><defs><linearGradient id="Y" x1="592.001" y1="617.046" x2="627.363" y2="609.848" xlink:href="#B"><stop offset="0" stop-color="#898889"></stop><stop offset="1" stop-color="#cac8c9"></stop></linearGradient></defs><path fill="url(#Y)" d="M602 608h0c2 1 2 1 4 1 1 0 2 0 3 1h3v1c1 0 3-1 4-1l1 1 9-1h3l-1 1c0 1 0 0 1 1h2c-2 1-4 2-6 2l-11 2h-3-3-2l-12 1v1h-1c-1-1-2-1-4-1h2l1-2v-1l1-1-3-1h1c0-1 1-1 2-1 1-1 4-1 6-1v-1h1c1 0 1 0 2-1z"></path><path d="M617 611l9-1h3l-1 1c0 1 0 0 1 1l-11 1c-1 0-2-1-3-1l2-1z" class="Q"></path><defs><linearGradient id="Z" x1="594.247" y1="616.099" x2="606.416" y2="607.542" xlink:href="#B"><stop offset="0" stop-color="#767476"></stop><stop offset="1" stop-color="#8f8e8d"></stop></linearGradient></defs><path fill="url(#Z)" d="M602 608h0c2 1 2 1 4 1 1 0 2 0 3 1h3v1c1 0 3-1 4-1l1 1-2 1c1 0 2 1 3 1h-5c-2 0-5 1-8 0h-1c-1 0-3 0-4 1h-3c-1-1-3-1-4-1l-3-1h1c0-1 1-1 2-1 1-1 4-1 6-1v-1h1c1 0 1 0 2-1z"></path><path d="M612 611c1 0 3-1 4-1l1 1-2 1h-3c-1 0-1 0-2-1h2z" class="I"></path><path d="M602 608h0c2 1 2 1 4 1 1 0 2 0 3 1h3v1h-2-5-12c1-1 4-1 6-1v-1h1c1 0 1 0 2-1z" class="L"></path><path d="M573 617c1 0 1 1 2 1h3l1 1h8l1 1h4c-5 2-11 1-16 2h-1-2c-1 1-1 1-2 1 2 0 2 0 4 1v1c1 0 2-1 2-1l1 1 1 1c-1 0-2 0-3 1h4c-1 0-2 1-2 1-1 0-2 1-3 1h-5v-1c-6 0-10-1-15 0-2-1-5-1-7-1l-1-1c-2-1-3-1-4-2 0-1 1-1 2-1h2l1-1 7 1h1l1-1c-1 0-1 0-2-1h2 0c1 0 1 0 1 1h5v-1c-1 0-1 0-2-1l2-1 1 1c1 0-1-1 1 0h4l1-1c-1 0-2 0-3-1h0c2 0 4 0 6-1z" class="D"></path><path d="M573 617c1 0 1 1 2 1h3l1 1c-3 0-6 0-8 1 1 1 2 1 2 1v1h-6-2c0 1-1 1-2 1s-1 0-2-1h3l-1-1c-1 0-1 0-2-1l2-1 1 1c1 0-1-1 1 0h4l1-1c-1 0-2 0-3-1h0c2 0 4 0 6-1z" class="H"></path><path d="M557 621h0c1 0 1 0 1 1h5v-1l1 1h-3c1 1 1 1 2 1s2 0 2-1h2c1 1 2 1 3 1 1 1 3 1 5 1v1c1 0 2-1 2-1l1 1 1 1c-1 0-2 0-3 1h0-3v-1c-2-1-4-1-6-1h-2-1c-1-1-4-1-6-1-1 1 0 0-1-1h-2 1l1-1c-1 0-1 0-2-1h2z" class="B"></path><path d="M570 623c1 1 3 1 5 1v1c1 0 2-1 2-1l1 1 1 1c-1 0-2 0-3 1h0-3v-1c-2-1-4-1-6-1h3v-1c-2 0-3 0-4-1h4z" class="l"></path><path d="M548 622l7 1h2c1 1 0 2 1 1 2 0 5 0 6 1h1 2c2 0 4 0 6 1v1h3 0 4c-1 0-2 1-2 1-1 0-2 1-3 1h-5v-1c-6 0-10-1-15 0-2-1-5-1-7-1l-1-1c-2-1-3-1-4-2 0-1 1-1 2-1h2l1-1z" class="S"></path><path d="M570 628h1c1-1 3-1 4-1-1 1-3 1-5 1z" class="B"></path><path d="M576 627h4c-1 0-2 1-2 1-1 0-2 1-3 1h-5v-1h0c2 0 4 0 5-1h1 0z" class="C"></path><path d="M548 622l7 1h2c1 1 0 2 1 1 2 0 5 0 6 1h1 2c2 0 4 0 6 1v1h-4c-1-1-2-1-3 0h-8-2 0c-2-1-3-1-5-2 0 0-1 0-1-1h0c-1-1-2-1-3-1l1-1z" class="h"></path><path d="M548 622l7 1h2c1 1 0 2 1 1 2 0 5 0 6 1h1c-1 1-2 1-3 1v-1h-3c-3 0-6 0-9-1h0c-1-1-2-1-3-1l1-1z" class="T"></path><path d="M84 467l1 1 1-1 1-1v1c1 0 1 1 2 1h0 2l1 3v1l1 1v3h3v28 4 6l1 1h1v6 2 1h1s0-1 1-1h0v-1h1v1c1 0 1 1 2 1v1l1 1 1 1v1h1 0s0 1-1 1h0-1-2v2 3 1l1 1h-2c-1 1-3 1-4 2h-2l-1 1c-1-1-1-1-2-1s-1 0-2 1h0c-1 0-1 0-2-1-1 0-1 0-1 1-1 0-3-1-3-1h-1-5-1c-1 0-1-1-2-1h-2-1v-2c-1 1-1 1-1 2h-1c0-2 0-3 1-5 0-3 2-7 2-10 0-2 1-4 1-5 0-2 1-3 1-5 1-1 1-2 1-4v-4c-1-2-1-3-1-5v-13c1-2 2-2 2-4l-1-1c0-1 0-1 1-2l6-1h1 1l-1-3v-1c-1-2 0-5 0-7z" class="C"></path><path d="M83 502c1 2 1 4 2 5h1v1 3l-1-1c-2-3-2-5-2-8z" class="f"></path><path d="M83 537c0-1 1-2 1-3h1l1 1v-1l2 1-1 2c-1 0-2-1-3 1h-1v-1z" class="E"></path><path d="M80 519l1-1 1-5h0c1 1 1 2 2 3v1c-1 0-1 0-2 1l-1 2c0-1 0-1-1-1z" class="K"></path><path d="M88 535h0v-2l1 1 1 1-1 2c1 0 1 1 2 1h1c-1 0-1 0-2 1h0c-1 0-1 0-2-1-1 0-1 0-1 1-1 0-3-1-3-1 1-2 2-1 3-1l1-2z" class="P"></path><path d="M80 509l1 1v2c-1 1-1 3-1 4l-1 6c-1 1-1 0-1 1v-2c0-2 1-5 1-8 0-1 0-2 1-4z" class="K"></path><path d="M84 527c1-1 1-3 2-5 0 2 0 4 1 5 0 2 0 5 1 7h-1c-1-2-1-4-1-5-1 1-1 2-1 3-1 1-1 1-2 1 0-2 1-4 1-6z" class="L"></path><path d="M74 517s0 1 1 1c0 1-1 2-1 3h0v5s0 1-1 2h1l1-1 1 1c0 1-1 3 0 4v-1c1-2 1-3 1-6 0-1 0-2 1-4v2c0 1 0 0 1 1 1-1 1-4 1-5 1 0 1 0 1 1l-1 5v4l-1 1v2h1c0-1 0-1 1-2h0v-2l1-6h1c0 2 0 3-1 4v3c-1 1 0 2-1 2v2 1c2-2 1-3 2-4v-1c0-1 0-1 1-2h0c0 2-1 4-1 6l-1 4h1v1h-5-1c-1 0-1-1-2-1h-2-1v-2c-1 1-1 1-1 2h-1c0-2 0-3 1-5 0-3 2-7 2-10 0-2 1-4 1-5z" class="b"></path><path d="M78 538c0-1 0-2 1-3h1v1c1 1 1 1 2 1h0 1v1h-5z" class="D"></path><path d="M72 537c0-1 0-2 1-3v-1c0-1 0-1 1-2h0c1 1 1 2 0 3 0 1 0 1-1 2v1h-1z" class="J"></path><path d="M78 493c0-2 0-6 1-7 0-1 1-1 1-1 1 1 2 1 2 2v3c0 3 1 5 0 8l1 2c0 3 1 10-1 12h-1c0-2 1-4 0-5-1 1-1 1-1 2-1 2-1 3-1 4 0 3-1 6-1 8-1 2-1 3-1 4 0 3 0 4-1 6v1c-1-1 0-3 0-4l-1-1-1 1h-1c1-1 1-2 1-2v-5h0c0-1 1-2 1-3s0-2 1-3c0-1 0-3 1-4 1-6 1-12 1-18z" class="Q"></path><path d="M77 511v1c1-1 1-2 1-3h1c0 4-2 7-2 11-1 3-1 5-1 7v1l-1-1-1 1h-1c1-1 1-2 1-2v-5h0c0-1 1-2 1-3s0-2 1-3c0-1 0-3 1-4z" class="E"></path><path d="M78 493c0-2 0-6 1-7 0-1 1-1 1-1 1 1 2 1 2 2v3c0 3 1 5 0 8-1 2-1 5-2 7v-6-1h-2v-5z" class="I"></path><path d="M75 499v-13c1-2 2-2 2-4l-1-1c0-1 0-1 1-2l6-1h1l2 5v21 3h-1c-1-1-1-3-2-5v-2l-1-2c1-3 0-5 0-8v-3c0-1-1-1-2-2 0 0-1 0-1 1-1 1-1 5-1 7 0 6 0 12-1 18-1 1-1 3-1 4-1 1-1 2-1 3-1 0-1-1-1-1 0-2 1-3 1-5 1-1 1-2 1-4v-4c-1-2-1-3-1-5z" class="Y"></path><path d="M94 505l1-1h1v4 6l1 1h1v6 2 1h1s0-1 1-1h0v-1h1v1c1 0 1 1 2 1v1l1 1 1 1v1h1 0s0 1-1 1h0-1-2v2 3 1l1 1h-2c-1 1-3 1-4 2h-2l-1 1c-1-1-1-1-2-1h-1c-1 0-1-1-2-1l1-2-1-1v-2c-1-1-1-2-1-4v-1c-1 0-1 0-1-1l-1-4v-5l1-1c0-2 0-3-1-5v-3l1-1c1 2 1 3 2 5h1 2v3l1-1v-1-4h1v-4z" class="L"></path><path d="M88 528l2 1c0 2 0 3-1 4l1 1v1l-1-1v-2c-1-1-1-2-1-4zm-1-10c2 1 2 2 2 4v2 1h-1c0-2-1-4-1-7z" class="F"></path><path d="M86 517l1 1h0c0 3 1 5 1 7l-1 1-1-4v-5z" class="T"></path><path d="M92 524v3s-1 0-1 1 0 2-1 3v-3c1-1 1-3 1-5s0-1-1-2v-2-1c1-1 1-1 1-2v-2h1v3 7z" class="N"></path><path d="M97 522h0v3 3 5c0 2-1 3 0 5h0-2l-1 1c-1-1-1-1-2-1h-1c-1 0-1-1-2-1l1-2v-1c0-1 0-2 1-3 1 2 0 4 0 7l2-5v-2-1-1h1v1l1-3v-1l1 1c1-1 1-4 1-5z" class="E"></path><path d="M97 522h0v3 3 5c0 2-1 3 0 5h0-2c1-2 1-5 1-8l-1 1v2c0 1-1 2-1 3v1h-1 0c0-1 1-1 1-2-1-2 0-3 0-5l1-3v-1l1 1c1-1 1-4 1-5z" class="R"></path><defs><linearGradient id="a" x1="98.778" y1="516.01" x2="91.276" y2="507.323" xlink:href="#B"><stop offset="0" stop-color="#adacaf"></stop><stop offset="1" stop-color="#d9d8d7"></stop></linearGradient></defs><path fill="url(#a)" d="M94 505l1-1h1v4 6 3c1 1 1 3 1 5h0c0 1 0 4-1 5l-1-1v1h-1v-3c-1-1-1-2-1-3 0 1 0 2-1 3v-7-2l1-1v-1-4h1v-4z"></path><path d="M93 521v-7h1v1c1 3 0 6 0 9-1-1-1-2-1-3z" class="N"></path><path d="M96 517c1 1 1 3 1 5h0c0 1 0 4-1 5l-1-1c1-2 1-7 1-9z" class="j"></path><path d="M92 515l1-1v-1-4h1v5 1-1h-1v7c0 1 0 2-1 3v-7-2z" class="B"></path><path d="M96 514l1 1h1v6 2 1h1s0-1 1-1h0v-1h1v1c1 0 1 1 2 1v1l1 1 1 1v1h1 0s0 1-1 1h0-1-2v2 3 1l1 1h-2c-1 1-3 1-4 2h0c-1-2 0-3 0-5v-5-3-3h0 0c0-2 0-4-1-5v-3z" class="C"></path><path d="M97 525h2v1l-2 2v-3z" class="M"></path><path d="M100 531h2v3c-1 0-1 0-2-1v-2z" class="P"></path><path d="M100 531l1-4h0l1 1v1 2h-2z" class="E"></path><path d="M96 514l1 1v7h0 0c0-2 0-4-1-5v-3z" class="m"></path><path d="M102 528v-3h1v2h0l2 2h-1-2v-1z" class="H"></path><path d="M98 535l1-1 2 2 1-1 1 1h-2c-1 1-3 1-4 2l1-3z" class="B"></path><path d="M97 533v-1-1h1c1 1 0 2 0 4l-1 3h0c-1-2 0-3 0-5z" class="D"></path><path d="M84 467l1 1 1-1 1-1v1c1 0 1 1 2 1h0 2l1 3v1l1 1v3h3v28h-1l-1 1v4h-1v4 1l-1 1v-3h-2-1c-1-2-1-3-2-5l-1 1v-1-3-21l-2-5h1l-1-3v-1c-1-2 0-5 0-7z" class="K"></path><path d="M90 511c1-3 0-6 0-9h1l1 7v3h-2v-1z" class="M"></path><path d="M92 509h0v2h1l-1-1v-1h1c0-1 0-2-1-3 0-1 0-1 1-2h0l1 1v4h-1v4 1l-1 1v-3-3z" class="U"></path><path d="M86 504l1-3 1-1c2 3 0 8 2 11v1h-1c-1-2-1-3-2-5l-1 1v-1-3z" class="C"></path><path d="M84 467l1 1 1-1 1-1v1c1 0 1 1 2 1h0 2l1 3v1l1 1v3c1 1 1 3 0 4v2h-1c-1 0-3 0-4 1h-2l-2-5h1l-1-3v-1c-1-2 0-5 0-7z" class="P"></path><path d="M84 475c1 1 2 0 3 0 0 2 0 2-1 3l-1 1v-1l-1-3z" class="f"></path><path d="M84 467l1 1 1-1 1-1v1c1 0 1 1 2 1h0 2l1 3v1 3h-3-2c-1 0-2 1-3 0v-1c-1-2 0-5 0-7z" class="Y"></path><path d="M87 473h-1 0l-1-1c1 0 2-1 2-2h0v3z" class="J"></path><path d="M87 467c1 0 1 1 2 1h0v1 1 2 3h-2v-2-3-3z" class="D"></path><path d="M89 468h2l1 3v1 3h-3v-3-2-1-1z" class="J"></path><path d="M126 536h0v1l1 1c1 0 1 0 2 1h0 3 2l4-1h1 8 3c1 0 2 0 3-1 1 1 5 1 6 0 2 0 5 1 7 0l3 1v1h1 10 0 5c3 0 7 1 10 0h10 1 3 2 0l-1 26v2c-1 1-2 1-4 1l1-1h-3c0-1-1-1-1-1h-3c-2 0-4-1-6-1l-12-2h-4l-3-1c-1-1-2-1-3-1h-1c-1-1-1-1-2-1h0-1-1c-1-1-2 0-3 0v1c-2-1-5-1-7-1h-8-4-2-5-1-1 0v-1h-1c-2-1-4-1-6-1 1 0 3 0 4-1h0v-1c1-1 4 0 5 0l1-1-7-1c-1 0-2 0-3 1-1 0-3-1-5-1l-2-1h0l2-1h-3-3c-1 0-2-1-3-2-1 1-1 1-2 0h-5c-3-1-5-1-8-1l-10-2h-1v-1l3 1c1-1 2-1 4-2h3c3 0 5-1 8-1 2 0 3 0 5 1-1-1-1-2-2-3h-1v-1h4-1c-2-1-3-2-5-2h3l1-1h0 4l1 1v-1h1 3 1 1 3l1-1v-1z" class="Y"></path><path d="M200 566c3 0 6-1 9-1h1v2c-1 1-2 1-4 1l1-1h-3c0-1-1-1-1-1h-3z" class="J"></path><path d="M176 549c3 0 7-1 10-1 2 0 2 1 4 1l6 1-14 1h-1c-1-1-2-1-3-1h0l-2-1z" class="P"></path><path d="M170 554h7 6c2 0 4 0 6 1 5 1 11-1 15 1h-20l4-1c-6-1-12 0-18-1z" class="D"></path><path d="M133 556c2 0 6-1 8 0h0c1 1 2 1 3 1h0l1 1h1c-1 0-2 1-3 1v1h-5-1-1 0v-1h-1c-2-1-4-1-6-1 1 0 3 0 4-1h0v-1z" class="E"></path><path d="M137 557h7l-4 3h-2-1l1-1c1 0 1 0 1-1h0l-2-1z" class="C"></path><path d="M133 557h4l2 1h0c0 1 0 1-1 1l-1 1h-1 0v-1h-1c-2-1-4-1-6-1 1 0 3 0 4-1z" class="B"></path><path d="M157 560h-1-1v-1-1c3 0 7 0 10 1 1 1 4 0 5 0 2 0 3 1 4 1h6c3 1 6 1 9 1h20c-5 2-12 1-17 1h-2c-2 0-6-1-8 1h0-4l-3-1c-1-1-2-1-3-1h-1c-1-1-1-1-2-1h0-1-1c-1-1-2 0-3 0v1c-2-1-5-1-7-1z" class="K"></path><defs><linearGradient id="b" x1="186.452" y1="534.747" x2="208.813" y2="545.564" xlink:href="#B"><stop offset="0" stop-color="#cfcecf"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#b)" d="M205 539h1 3c1 1 2 2 2 4h-1c-1 1-2 1-3 0h-3-2-1 0c-1-1-2-1-3-1h0-1l-1-1h-14l1-1h1v-1h-4 5c3 0 7 1 10 0h10z"></path><defs><linearGradient id="c" x1="164.912" y1="550.966" x2="162.763" y2="559.589" xlink:href="#B"><stop offset="0" stop-color="#9f9c9e"></stop><stop offset="1" stop-color="#c2c2c0"></stop></linearGradient></defs><path fill="url(#c)" d="M139 555c5 0 12 0 18-1 0 0-1 0-1-1h4c4 0 7 1 10 1 6 1 12 0 18 1l-4 1h-43c-2-1-6 0-8 0 1-1 4 0 5 0l1-1z"></path><path d="M196 541l1 1h1c-2 1-2 1-4 1-3 1-7 0-10 1h-2c-2 0-5 0-7 1h-10 4 0c-4 0-7 0-11 1l1-1h0-4l3-1v-1c0-1 0-1 1-2h10 1l2 1 1 1c1 0 1-1 2-1h3c1 0 2 0 4-1h14z" class="o"></path><path d="M196 541v1c-3 2-9 1-13 1l-8 1h-3c1-1 3-1 4-1s1-1 2-1 2 0 4-1h14z" class="B"></path><path d="M159 541h10 1l2 1 1 1c1 0 1-1 2-1h3c-1 0-1 1-2 1s-3 0-4 1h3c-5 1-11 0-17 0v-1c0-1 0-1 1-2z" class="X"></path><path d="M169 541h1l2 1 1 1c1 0 1-1 2-1h3c-1 0-1 1-2 1s-3 0-4 1h-6v-1s1 0 1-1h0l2-1z" class="O"></path><path d="M138 547h2 14v1c3 0 7 1 11 1h7 4l2 1h0c1 0 2 0 3 1h1c-4 1-7 1-10 0h-8-15c-5 0-10 1-15 1l4-1c1-1 0-1 0-2h-1-1-5l-4-1c2 0 9 1 11-1z" class="E"></path><path d="M138 547h2 14v1h-2s1 0 2 1c-5 0-12 0-17-1v1h-1-5l-4-1c2 0 9 1 11-1z" class="U"></path><path d="M154 548c3 0 7 1 11 1h7 4l2 1h0c-5 0-9 1-14 0-4 0-7-1-10-1-1-1-2-1-2-1h2z" class="N"></path><path d="M182 545h2c1-1 2-1 3-1l1 1c1 0 1 0 2-1h4 1l1-1v2h-3c0 1 0 1-2 1 0 1-1 0-2 0v2h1v-1h1 0 2 0c1-1 3-1 4-1 0-1 0-1 1-2h2 1c-1 1-2 1-2 1-1 0-1 1-1 2-3 0-6 0-8 2-2 0-2-1-4-1-3 0-7 1-10 1h-4-7c-4 0-8-1-11-1v-1h0c1 0 3-1 4-1 4-1 7-1 11-1h3v1h5l5-1z" class="f"></path><path d="M182 545c1 0 5 0 6 1h0l-15 1h-2c1-1 4-1 6-1l5-1z" class="O"></path><path d="M169 545h3v1h5c-2 0-5 0-6 1h2c-2 0-5 0-6 1h0l5 1h0-7c-4 0-8-1-11-1v-1h0c1 0 3-1 4-1 4-1 7-1 11-1z" class="D"></path><path d="M171 547h-3-2c-1 0-2 0-2-1 1-1 6 0 8 0h0 5c-2 0-5 0-6 1z" class="U"></path><path d="M99 545c3 0 5-1 8-1 2 0 3 0 5 1h0 1 2l1 2h2c1-1 2 0 3 0h1c2 0 3 1 5 1l4 1h5 1 1c0 1 1 1 0 2l-4 1c-3 0-7 1-10 0h-3-3c-1 0-2-1-3-2-1 1-1 1-2 0h-5c-3-1-5-1-8-1l-10-2h-1v-1l3 1c1-1 2-1 4-2h3z" class="B"></path><path d="M115 550h5 0l-2 2c-1 0-2-1-3-2z" class="C"></path><path d="M116 547h2c1-1 2 0 3 0h1c2 0 3 1 5 1l4 1h-16c1 0 2 0 3-1h2 0c-1 0-2 0-3-1h0-1-1 1z" class="D"></path><path d="M136 549h1 1c0 1 1 1 0 2l-4 1c-3 0-7 1-10 0h-3c0-1 0-1 1-2 1 0 2 0 3 1-1 0-1 0-1 1h1c1-1 2-2 3-2 3-1 5 1 8-1z" class="O"></path><path d="M99 545c3 0 5-1 8-1 2 0 3 0 5 1h0 1 2l1 2h-1 1 1 0c1 1 2 1 3 1h0-2c-1 1-2 1-3 1-8 0-16 0-23-2 1-1 2-1 4-2h3z" class="J"></path><path d="M99 545c3 0 5-1 8-1 2 0 3 0 5 1h0 1 2l1 2h-1c-5-1-11 0-16-2z" class="O"></path><defs><linearGradient id="d" x1="121.878" y1="549.458" x2="174.997" y2="531.774" xlink:href="#B"><stop offset="0" stop-color="#d4d2d5"></stop><stop offset="1" stop-color="#f8f7f7"></stop></linearGradient></defs><path fill="url(#d)" d="M126 536h0v1l1 1c1 0 1 0 2 1h0 3 2l4-1h1 8 3c1 0 2 0 3-1 1 1 5 1 6 0 2 0 5 1 7 0l3 1v1h1 10 0 4v1h-1l-1 1c-2 1-3 1-4 1h-3c-1 0-1 1-2 1l-1-1-2-1h-1-10c-1 1-1 1-1 2v1l-3 1h4 0l-1 1c-1 0-3 1-4 1h0-14-2c-2 2-9 1-11 1s-3-1-5-1h-1c-1 0-2-1-3 0h-2l-1-2h-2-1 0c-1-1-1-2-2-3h-1v-1h4-1c-2-1-3-2-5-2h3l1-1h0 4l1 1v-1h1 3 1 1 3l1-1v-1z"></path><path d="M132 539h2c0 1 0 1 1 2l1 1c-3 0-6 0-9-1h4v-1c0-1 0-1 1-1z" class="U"></path><path d="M139 538h8c-2 1-8 0-8 2v2h-3l-1-1c-1-1-1-1-1-2l4-1h1z" class="K"></path><path d="M159 537c2 0 5 1 7 0l3 1v1h1 0l-20-1c1 0 2 0 3-1 1 1 5 1 6 0z" class="U"></path><path d="M180 539h0 4v1h-1l-1 1c-2 1-3 1-4 1h-3c-1 0-1 1-2 1l-1-1-2-1c2 0 6 1 8-1h0l-8-1h0 10z" class="f"></path><path d="M134 544c3 1 6 0 9 0 4 0 9 1 12 0 0-1 0-1-1-2h0c1-1 4-1 5-1-1 1-1 1-1 2v1l-3 1h-17s-1 0-2 1h-3l1-2z" class="K"></path><path d="M138 545h17 4 0l-1 1c-1 0-3 1-4 1h0-14l-2-1v-1z" class="Y"></path><path d="M126 536h0v1l1 1c1 0 1 0 2 1h0 3c-1 0-1 0-1 1v1h-4-1-5-2v1c-2 0-4-1-6-1h-1c-2-1-3-2-5-2h3l1-1h0 4l1 1v-1h1 3 1 1 3l1-1v-1z" class="H"></path><path d="M126 536h0v1l1 1c1 0 1 0 2 1h0c-1 0-3 0-4 1-1-1 0-2 0-2l1-1v-1z" class="h"></path><path d="M122 538h3s-1 1 0 2h-4c-2-1-3-1-5-1v-1h1 3 1 1z" class="a"></path><path d="M111 538h0 4l1 1c2 0 3 0 5 1l5 1h-5-2v1c-2 0-4-1-6-1h-1c-2-1-3-2-5-2h3l1-1z" class="K"></path><path d="M113 541c2 0 4 1 6 1h0c2 1 4 1 5 1l10 1-1 2h3c1-1 2-1 2-1v1l2 1h-2c-2 2-9 1-11 1s-3-1-5-1h-1c-1 0-2-1-3 0h-2l-1-2h-2-1 0c-1-1-1-2-2-3h-1v-1h4z" class="L"></path><path d="M133 546h3c1-1 2-1 2-1v1l2 1h-2c-2 0-6 0-8-1h3z" class="b"></path><path d="M119 542c2 1 4 1 5 1l-1 2h0l2 1h-3v-1l-1 1h-1-2v-1l-1-1h0 3c1 1 0 1 1 0 0-1-1-1-2-2z" class="N"></path><path d="M124 543l10 1-1 2h-3-5l-2-1h0l1-2z" class="l"></path><path d="M113 541c2 0 4 1 6 1h0c1 1 2 1 2 2-1 1 0 1-1 0h-3 0c-1 1-3 1-5 1h0c-1-1-1-2-2-3h-1v-1h4z" class="I"></path><path d="M101 425h10 5 0 2v2h2c0 1 0 2-1 3 0 1 0 1-1 1v1l1 1c-1 1-1 2-1 3v9h0l-1 9c1 2 2 5 2 7 1 3 0 7 0 10 1 2 0 5 0 7l1 19v4h0c0-1 0-3 1-4v1-6c1 3 1 5 1 8 0 1-1 3 0 4v1 14h0c0 2 0 4 1 6h1v2c0 1 1 2 1 2v4s0 1 1 2h0v1 1l-1 1h-3-1-1-3-1v1l-1-1h-4 0l-1 1h-3c-1 0-2 1-3 1h-1l-1-1h0v-1l-1-2h2l-1-1v-1-3-2h2 1 0c1 0 1-1 1-1h0-1v-1l-1-1-1-1v-1c-1 0-1-1-2-1v-1h-1v1h0c-1 0-1 1-1 1h-1v-1-2-6h-1l-1-1v-6-4-28h-3v-3l-1-1v-1l-1-3h-2 0c-1 0-1-1-2-1v-1l-1 1-1 1-1-1v-1c0-2 1-3 0-5 1 0 1 0 1-1l1 1h0 0c1-1 1-2 1-3 0-2 1-3 0-5v-7c0-1-1-3 0-4v-4-3-4l1-1c0-1-1-2 0-3h0 3c0 2 1 3 1 5h2l2-1c1 1 0 2 1 3v-6l1-1c-1 0-1-1-1-1v-1h4z" class="D"></path><path d="M100 457h0l-1-1c0-1 0-7 1-8h0v7 2z" class="U"></path><path d="M96 508c1-2 1-5 1-7h0l1 14h-1l-1-1v-6z" class="R"></path><path d="M100 445v-3c0-2-1-3-1-4l2-2 1 1v2 3 13h-1-1v-7-3z" class="E"></path><path d="M100 445c1 1 1 1 1 2v4 2 2h-1v-7-3z" class="f"></path><path d="M101 425h10 5 0 2v2h2c0 1 0 2-1 3 0 1 0 1-1 1v1c-1-1-1-1-2-1-1-1-1-2-1-4v-1h-1c-1 1-1 2-2 2h0 0c0-1-1-1-1-2-1 0-1 1-2 2-1-1-1-1-1-2-1 1-1 1-1 2v1h-1v-3c-1 0-1 0-1 1h-1l-1-1h-1v1c-2 0-1-1-2-1l-1 2h0l-1-1c-1 0-1-1-1-1v-1h4z" class="h"></path><path d="M118 427h2c0 1 0 2-1 3 0 1 0 1-1 1v1c-1-1-1-1-2-1-1-1-1-2-1-4 0 1 0 2 1 2v1c1-1 1-2 1-3h1zm-27 16h-1c1-2 3-4 5-5h2c1 11 0 22 0 33v22c0 3 1 6 0 8h0c0 2 0 5-1 7v-4-28-16c1-2 0-4 0-6 1-3 1-6 0-9h0l-1-2h1c0-1 1-1 0-3v-1c-2 0-4 3-5 4z" class="B"></path><path d="M101 455h1 0 1c1 0 2 0 2 1h0 1l1 1-1 2v3h-2v1c0 1 0 1 1 2h-1l-1 1h0c1 1 1 1 0 2h0-1-1c-1 1-1 3-1 5 0 1 1 1 1 2v29h-1v-47-2h1z" class="o"></path><path d="M96 445h0c1 3 1 6 0 9 0 2 1 4 0 6v16h-3v-3l-1-1v-1l-1-3h-2 0v-2 1l1-1c0-3 0-5 1-8 0-1 1-2 1-3h0c1-1 1-2 1-4l-1-1h0c1 0 1 0 1-1 2-1 2-2 3-4z" class="E"></path><path d="M94 453h2v1c0 2 1 4 0 6v-5c-1 0-1 0-2-1v-1z" class="K"></path><path d="M96 445h0c1 3 1 6 0 9v-1h-2v1c0 2-1 9 0 10h1v1c0 1 0 0-1 1l-1 1v1c-1-1-1-5-1-6v-2-2h-1c0-1 1-2 1-3h0c1-1 1-2 1-4l-1-1h0c1 0 1 0 1-1 2-1 2-2 3-4z" class="D"></path><path d="M94 453v-3h1l1 1v2h-2zm8-16c0-1 0-2-1-3 1 0 1 0 2-1h-1-1c0-1 0-2 1-3h3 2v-2c1 1 0 1 2 2l1-1v-1h1v2c1 1 6 3 6 3v1l-2 1v1 1c-1 1 0 2-1 3l1 1c0 1 0 1 1 2 0 1-1 1-1 2h-3v-1-1-1h-1c-1 0-2 0-3-1h-2-1c-1 1-2 1-3 1h0v-3-2z" class="J"></path><path d="M105 441v-5h2c1 1 1 1 1 2 0 2-1 2-2 3h-1z" class="S"></path><path d="M88 427h3c0 2 1 3 1 5h2l2-1c1 1 0 2 1 3v-6l1-1 1 1h0v1 1h0v1c0 1 0 1-1 2h1v2c-1 1-1 2-2 3h-2c-2 1-4 3-5 5h1c1-1 3-4 5-4v1c1 2 0 2 0 3h-1l1 2c-1 2-1 3-3 4 0 1 0 1-1 1h0l1 1c0 2 0 3-1 4h0c0 1-1 2-1 3-1 3-1 5-1 8l-1 1v-1 2c-1 0-1-1-2-1v-1l-1 1-1 1-1-1v-1c0-2 1-3 0-5 1 0 1 0 1-1l1 1h0 0c1-1 1-2 1-3 0-2 1-3 0-5v-7c0-1-1-3 0-4v-4-3-4l1-1c0-1-1-2 0-3h0z" class="X"></path><path d="M85 460l1 1h0c1 1 1 1 1 3h-1l-1-1c-1 1 0 2 0 3h-1c0-2 1-3 0-5 1 0 1 0 1-1z" class="f"></path><path d="M92 450l1 1c0 2 0 3-1 4h0c0 1-1 2-1 3-1 3-1 5-1 8l-1 1v-1-8-1c0-3 1-5 3-7z" class="b"></path><path d="M91 443c1-1 3-4 5-4v1c1 2 0 2 0 3h-1l1 2c-1 2-1 3-3 4 0 1 0 1-1 1h0c-2 2-3 4-3 7-1-2-1-3-1-4 1-1 1-1 2-1v-2l-1-1v-1-1c0-2 0-2 2-4z" class="U"></path><path d="M89 449h2v-1c1-2 3-4 4-5l1 2c-1 2-1 3-3 4 0 1 0 1-1 1h0c-2 2-3 4-3 7-1-2-1-3-1-4 1-1 1-1 2-1v-2l-1-1z" class="N"></path><path d="M88 427h3c0 2 1 3 1 5h2l2-1c1 1 0 2 1 3v-6l1-1 1 1h0v1 1h0v1c0 1 0 1-1 2h1v2c-1 1-1 2-2 3h-2c-2 1-4 3-5 5h1c-2 2-2 2-2 4v1 1l1 1-1-1-1 1-1-1v-3c0-1 1-2 1-3v-3c0-1 1-2 1-2v-2c1-1 1-1 1-2-1 0-1-1-2-1h0l1-1h1c0-2 0-3-1-4l-1-1h0z" class="L"></path><path d="M96 431c1 1 0 2 1 3-2 3-6 5-8 8v-1l2-2v-2h-1c1-1 1 0 1-1 1-1 1-1 2-3l-1-1h2l2-1z" class="a"></path><path d="M92 432h2v1c1 0 0 0 1 1h0c-1 1-1 0-1 1-1 2-2 3-3 4v-2h-1c1-1 1 0 1-1 1-1 1-1 2-3l-1-1z" class="N"></path><path d="M97 434v-6l1-1 1 1h0v1 1h0v1c0 1 0 1-1 2h1v2c-1 1-1 2-2 3h-2c-2 1-4 3-5 5h1c-2 2-2 2-2 4v1 1l1 1-1-1-1 1-1-1v-3c0-1 1-2 1-3h1v-1c2-3 6-5 8-8z" class="F"></path><path d="M105 441h1 2c1 1 2 1 3 1h1v1 1 1h3c0-1 1-1 1-2h1v1s0 1 1 1l-1 9h0v9h0-1v4h-1c-1 0-1 1-2 1s-1 0-1-1c-2 0-4 2-6 1l-2-1v-2h1c-1-1-1-1-1-2v-1h2v-3l1-2-1-1h-1 0c0-1-1-1-2-1h-1 0v-13h0c1 0 2 0 3-1z" class="w"></path><path d="M106 453h2c1 0 1 0 1 1v1h-3v-2z" class="B"></path><path d="M111 442h1v1 1 1h3 1v1h-2v1h-3-2c-2 0-4 1-6 0h0v-1h2l1-1c1-1 4-1 5-1v-1-1z" class="Y"></path><path d="M116 443h1v1s0 1 1 1l-1 9h0v9h0l-1-2-1-1 1-2c-2-1-1-3-1-5h-1v-1h1c-1-1-1-2-1-3 0 0 1 0 2-1-1-1-1-1-2-1h0v-1h2v-1h-1c0-1 1-1 1-2z" class="b"></path><path d="M116 443h1v1s0 1 1 1l-1 9h0v-6c-1-1-2-1-3-1h0v-1h2v-1h-1c0-1 1-1 1-2z" class="K"></path><path d="M117 454c1 2 2 5 2 7 1 3 0 7 0 10 1 2 0 5 0 7l1 19v4h0c0-1 0-3 1-4v1-6c1 3 1 5 1 8 0 1-1 3 0 4v1 14h0c0 2 0 4 1 6h1v2c0 1 1 2 1 2v4s0 1 1 2h0v1 1l-1 1h-3-1-1-3-1v1l-1-1h-4 0l-1 1h-3c-1 0-2 1-3 1h-1l-1-1h0v-1l-1-2h2l-1-1v-1-3-2h2 1 0c1 0 1-1 1-1h0-1v-1l-1-1-1-1v-1c-1 0-1-1-2-1v-7c-1-1-1-2 0-4 0-1-1-3 0-5h0c-1-1-1-2-1-2v-1h1v-29c0-1-1-1-1-2 0-2 0-4 1-5h1 1 0c1-1 1-1 0-2h0l1-1v2l2 1c2 1 4-1 6-1 0 1 0 1 1 1s1-1 2-1h1v-4h1 0v-9h0z" class="Y"></path><path d="M108 496h1c1 3 1 7 0 9-1 1 0 1-1 0 0-2-1-7 0-9z" class="c"></path><path d="M117 517h1v5l-1 3h-2v-1c-1-1-1-1-1-2h-1c0-1 0-1 1-2h3v-3z" class="f"></path><path d="M110 471c1 1 1 1 1 2h0v4 9h0-4c1 0 1 0 2-1v-1-8-1c0-1 1-1 1-1v-1h0v-2z" class="G"></path><path d="M107 486c0-3-1-13 1-16 1 0 1 0 2 1v2h0v1s-1 0-1 1v1 8 1c-1 1-1 1-2 1z" class="a"></path><path d="M101 512v3h1 6c-1 1-5 1-6 1v1h1v1c-1 1 0 2 0 3 1 1 1 1 2 1s2-1 3-1v2c0 1 0 1 1 2h1c1-1 1-2 3-2v-1c0 2 0 6-1 7h-1v-1c-1 1-1 1-1 2h-1 0v-2l-1 1h-1l-1-1h0-1v-1l-1-1-1-1v-1c-1 0-1-1-2-1v-7c-1-1-1-2 0-4z" class="E"></path><path d="M117 454c1 2 2 5 2 7 1 3 0 7 0 10 1 2 0 5 0 7l1 19c-2 6-1 14-2 20h-1v-26-28-9h0z" class="B"></path><path d="M117 454c1 2 2 5 2 7 1 3 0 7 0 10v7-5l-2 2h1v1 1 6c0 2 0 6-1 8v-28-9h0z" class="O"></path><path d="M113 522h1c0 1 0 1 1 2v1h2l1-3c0 4 0 9-2 12v1 1h0v2h-1-4 0l-1 1h-3c-1 0-2 1-3 1h-1l-1-1h0v-1l-1-2h2l-1-1v-1-3-2h2 1 0c1 0 1-1 1-1l1 1h1l1-1v2h0 1c0-1 0-1 1-2v1h1c1-1 1-5 1-7z" class="S"></path><path d="M106 534v-3h2v1 3 1h-1-1v-1-1z" class="j"></path><path d="M111 536l1-1c0-1-1 0 0-1l1 1c0 1 0 1 1 2h0l2-2v1h0v2h-1-4v-2z" class="a"></path><path d="M106 528l1 1h1v1h-1 0c0-1 0-1-1-1-1 1 0 3-1 4-1 0-2-2-3-2v-2h2 1 0c1 0 1-1 1-1z" class="M"></path><path d="M113 522h1c0 1 0 1 1 2-1 2-1 4-1 6l1-1c0 2-1 4-2 6 0-2 1-4 0-6h0c-1 1-1 2-1 3h-1v-3h1c1-1 1-5 1-7z" class="O"></path><path d="M118 522c0 4 0 9-2 12-1-2 0-4 0-6l-1 1-1 1c0-2 0-4 1-6v1h2l1-3z" class="U"></path><path d="M108 535v-1c1-1 2-3 3-4-1 2 0 4-1 6h1v2h0l-1 1h-3c-1 0-2 1-3 1h-1l-1-1h0v-1l-1-2h2s0-1 1-1c0-1 0-1 1-2 0 1 0 1 1 1h0v1 1h1 1v-1z" class="I"></path><path d="M102 539c1-1 4-2 5-2s3 1 4 1l-1 1h-3c-1 0-2 1-3 1h-1l-1-1z" class="J"></path><path d="M121 492c1 3 1 5 1 8 0 1-1 3 0 4v1 14h0c0 2 0 4 1 6h1v2c0 1 1 2 1 2v4s0 1 1 2h0v1 1l-1 1h-3-1-1-3-1v1l-1-1h1v-2h0v-1-1c2-3 2-8 2-12v-5c1-6 0-14 2-20v4h0c0-1 0-3 1-4v1-6z" class="Q"></path><path d="M116 536l1-1c1-4 2-8 2-12h1v4l-1 6c0 2-1 3-2 4h-1v-1h0z" class="O"></path><path d="M122 519h0c0 2 0 4 1 6h1v2c0 1 1 2 1 2v4s0 1 1 2h0v1 1l-1 1h-3-1-1c-1-2 0-4 1-6v-2-4c0-1 1-1 1-2v-5z" class="S"></path><path d="M123 527h1v1h-1-1v-1h1z" class="L"></path><path d="M123 535h3v1 1l-1 1h-3c0-1 0-1 1-1h0v-2z" class="G"></path><path d="M124 527c0 1 1 2 1 2v4s0 1 1 2h0-3v-1l1-2v-5z" class="g"></path><path d="M121 532h0 0v3c0 1 1 2 0 3h-1c-1-2 0-4 1-6z" class="L"></path><defs><linearGradient id="e" x1="141.451" y1="486.53" x2="207.7" y2="524.491" xlink:href="#B"><stop offset="0" stop-color="#323132"></stop><stop offset="1" stop-color="#69696a"></stop></linearGradient></defs><path fill="url(#e)" d="M165 480h46v9 8 2 40h0-2-3-1-10c-3 1-7 0-10 0h-5 0-10-1v-1l-3-1c-2 1-5 0-7 0-1 1-5 1-6 0-1 1-2 1-3 1h-3-8-1l-4 1h-2-3 0c-1-1-1-1-2-1l-1-1v-1h0v-1h0c-1-1-1-2-1-2v-4s-1-1-1-2v-2h-1c-1-2-1-4-1-6h0v-14-1c-1-1 0-3 0-4 0-3 0-5-1-8 0-3 0-9 2-12h5 6 6 13 12z"></path><path d="M145 496h1c1 1 1 1 1 2v1h-1-1v-1s-1-1-1-2h1z" class="e"></path><path d="M204 527h1v-4h0c1 2 1 4 1 6-1 1-1 2-1 3v5h-1v-10z" class="m"></path><path d="M203 528l1-1h0v10h-1v-6-3z" class="N"></path><path d="M195 506c1 2 1 4 1 6h-4c1 0 1-1 2-1h0v-1-2c1 0 1-1 1-2z" class="B"></path><path d="M194 491l4 1c1 0 1 1 2 1 0 2 1 3 2 4l-1 1h-1c0-1-2-4-4-5h0c-1 0-2-1-2-2z" class="c"></path><path d="M207 522c0-1 1-1 1-2v18h1c-1 1-1 1-2 1v-1-16z" class="S"></path><path d="M177 489c3 0 5 0 8 1h1c0 1 1 1 2 2h0c0 2 3 4 4 5 1 2 2 3 2 5l-1-1-1-2c-3-5-8-8-15-10z" class="Z"></path><path d="M186 486c5 1 10 3 14 5 2 1 3 2 4 3-1 0-2-2-4-2v1c-1 0-1-1-2-1l-4-1c-2-1-3-1-4-2h-1l-3-3z" class="p"></path><path d="M207 522c0-3 0-7-1-10 0-3-1-6-1-8v-2h1c0 2 1 3 2 5v2c0 3 1 8 0 11 0 1-1 1-1 2z" class="I"></path><path d="M192 499l1 2 1 1c0 2 1 3 1 4s0 2-1 2v2c-2 0-2 0-3-2 0-1-1-2-2-3v2c-1 0-1-2-2-3v1l-1-1v-1l2 1v-2h1v1l3 1v-5z" class="E"></path><path d="M193 501l1 1c0 2 1 3 1 4s0 2-1 2l-1-2v-3-1-1z" class="C"></path><defs><linearGradient id="f" x1="197.463" y1="528.433" x2="204.093" y2="536.269" xlink:href="#B"><stop offset="0" stop-color="#656765"></stop><stop offset="1" stop-color="#7c7a7c"></stop></linearGradient></defs><path fill="url(#f)" d="M200 526v-4 3c1 0 1 1 1 1 1 0 1 1 1 2h1 0v3 6h1 1v-5c0-1 0-2 1-3v-2c2 2-1 9 1 11v1h-1-1-10c0-2 0-2 2-3v1h1 0v-3-1c1-1 1-1 2-1-1-2 0-4 0-6z"></path><path d="M203 528v3c-1 0-1 0-2-1l2-2h0z" class="R"></path><path d="M198 534v-1c1-1 1-1 2-1v6h-1c-1-1 0-2-1-4z" class="Q"></path><path d="M198 534c1 2 0 3 1 4h1l1 1c1-1 3-1 4 0h-10c0-2 0-2 2-3v1h1 0v-3z" class="R"></path><path d="M210 498s1 0 1-1v2 40h0-2-3 1c1 0 1 0 2-1v-22c0-5 1-11 0-15 0-1 0-2 1-3z" class="D"></path><path d="M140 501h1c1 0 1 0 2 1 0 2-2 3-1 5h0c1-1 1-1 1-2s1-1 1-2v-1h0c1 0 1 0 1 1l1-1c0 1-1 3-1 4h2c-1 0-1 1-1 1h0c1 0 1 0 2 1v1h0 0c-1 4-1 8-1 12h0l1 2h-1v1 5c1 1 1 1 0 2h1 2l-1 2-1 1v1h1c1-1 1-1 2-1h2v1h0c1 1 1 1 0 2s-2 1-3 1h-3-8-1l-4 1h-2-3 0c-1-1-1-1-2-1l-1-1v-1h0v-1h0c-1-1-1-2-1-2v-4s0 1 1 1v1l1-1v-3h1v-3c0-2 1-5 1-8s1-8 3-11h0c1 1 1 1 1 2h1c0-2 0-3 1-5-1 2-1 3-1 5 1 0 1 1 1 1l-1 2h0 1l1 1v-3-1h-1v-1h1v-1h0l1-1 2-2 1-1z" class="W"></path><path d="M138 517c-1-1-1-1-1-2h1l1 1v3c0-1 0-1-1-2z" class="c"></path><path d="M140 502c1 1 1 1 1 2 0 2-1 3-1 4v1-2c-1 1-1 2-1 3v-4l1-2s0-1-1-1l1-1zm4 1v-1h0c1 0 1 0 1 1l1-1c0 1-1 3-1 4s0 3-1 4v-1-5l1-1h-1z" class="V"></path><path d="M137 504l2-2 1-1v1l-1 1h0c-1 2-1 5-2 7v2c0 2 0 2-1 3v-5h0c0-1 0-1 1-2v-2-2z" class="q"></path><path d="M142 524v-4l1-1v-2 3h1l1-1v-1l2-2-1-1s-1-1-2-1l-2-3 1-3v-1c0 2 0 3 1 4s1 1 2 1c1-1 1-2 2-3h0 0c-1 4-1 8-1 12h0l1 2h-1v1c-1 0-2 0-3 1 1 1 2 1 2 1v1l-3 1v-1c0-1 0-2-1-3z" class="c"></path><path d="M142 524c1 1 1 2 1 3v1l3-1v-1s-1 0-2-1c1-1 2-1 3-1v5c1 1 1 1 0 2h1 2l-1 2-1 1v1h1c1-1 1-1 2-1h2v1h0c1 1 1 1 0 2s-2 1-3 1h-3-8-1c0-1 0-2-1-3h-1l1-1v-1c1-1 1-2 2-2v1h1 0v-6h0 1c1 0 1-1 1-2z" class="t"></path><path d="M140 532h1v2h-1v-2h0z" class="Z"></path><path d="M140 535c1 0 1 0 1 1v1h-1v-1-1z" class="c"></path><path d="M137 534v-1c1-1 1-2 2-2v1 2l1 1v1l-1 2h-1c0-1 0-2-1-3h-1l1-1z" class="a"></path><path d="M147 529c1 1 1 1 0 2h1 2l-1 2-1 1v1h1c1-1 1-1 2-1h2v1h0c1 1 1 1 0 2s-2 1-3 1h-3c1-3-1-6 0-9z" class="G"></path><path d="M153 534v1h0c1 1 1 1 0 2h-1c0-1 0-2 1-3z" class="l"></path><path d="M132 505h0c1 1 1 1 1 2h1l1 1c-2 2-2 2-2 4l1 1v3s1 1 1 2v4c0 1-1 2-1 2l1 1h1 0v-1-5l2-2c1 1 1 1 1 2v1h-2v1l2 1c0 1 0 2-1 3h1c1 2 0 4 0 6-1 0-1 1-2 2v1l-1 1h1c1 1 1 2 1 3l-4 1h-2-3 0c-1-1-1-1-2-1l-1-1v-1h0v-1h0c-1-1-1-2-1-2v-4s0 1 1 1v1l1-1v-3h1v-3c0-2 1-5 1-8s1-8 3-11z" class="k"></path><path d="M133 529l1-1h1v3 1l-1 2v-2l-1-3z" class="i"></path><path d="M132 529h-1v-4c0-1 0-1 1-2h1v1l-1 1c0 1 1 2 1 2v1l-1 1z" class="c"></path><path d="M133 528v1l1 3v2 3h-1v-4l-1-1v-3l1-1zm-7 7l4-4 1-1v1 3l-1 1h-1c-1 1-1 1-2 3l-1-1v-1h0v-1h0z" class="a"></path><path d="M129 516v4h1v-2 9l-1 1s0 1-1 1v1c0 1-1 2-2 3h0-1v-4s0 1 1 1v1l1-1v-3h1v-3c0-2 1-5 1-8z" class="W"></path><path d="M135 531c1 1 1 1 2 3l-1 1h1c1 1 1 2 1 3l-4 1h-2-3 0c-1-1-1-1-2-1 1-2 1-2 2-3h1v1h1 0c0-1 0-1 1-1 1 1 0 1 1 2h1v-3l1-2v-1z" class="Z"></path><path d="M135 531c1 1 1 1 2 3l-1 1c0 1 0 2-1 3v-6-1z" class="j"></path><path d="M132 505h0c1 1 1 1 1 2h1l1 1c-2 2-2 2-2 4v7 2h-1-1c0-1 0-3 1-4l1-11c-2 4-2 8-3 12v2h-1v-4c0-3 1-8 3-11z" class="V"></path><path d="M152 499c1-2 2-4 4-5 6-5 13-6 21-5 7 2 12 5 15 10v5l-3-1v-1h-1v2l-2-1v1l1 1v-1c1 1 1 3 2 3v-2c1 1 2 2 2 3h-1 0-2-1-7c-1 1-2 1-3 1h-1-1v1 1c1 0 1 0 2-1v1c1 0 1 0 2 1-4 0-9 1-13 3-1 1-9 4-9 3l-4 2-5 3-1-2h0c0-4 0-8 1-12h0l1-5 1-2 2-3z" class="w"></path><path d="M171 492c3-1 4-1 7 1 1 0 3 1 3 2 0 0-1 0-1 1h-2v1c0 1-1 2-1 2l-1 1-1 1h-3c-1 1 0 1-1 0v-3l1-1c0-1-1-2-2-3 0-1 1-2 1-2z" class="K"></path><path d="M177 499c-1 0-1 0-1-1v-2l2 1c0 1-1 2-1 2z" class="O"></path><defs><linearGradient id="g" x1="181.993" y1="507.524" x2="180.574" y2="498.704" xlink:href="#B"><stop offset="0" stop-color="#c5c4c5"></stop><stop offset="1" stop-color="#e8e8e8"></stop></linearGradient></defs><path fill="url(#g)" d="M176 500c0 1 0 1 1 1 1-1 2-2 4-2 0 0 0 1 1 1s2 0 4 1v2 1l1 1v-1c1 1 1 3 2 3v-2c1 1 2 2 2 3h-1 0-2-1-7l-1-1 1-1v-1-1h-2-3l-1 1c0-1 0-1-1-1h-3v-1h3 1 1v-1c-1-1-2 0-3 0v-1h3l1-1z"></path><path d="M168 492h3s-1 1-1 2c1 1 2 2 2 3l-1 1v3c1 1 0 1 1 0v1c-1 0-2 0-3 1-1 0-2 0-3-1h-2-2v-1h-1c-1-1-2-3-1-4 0-1 1-2 2-2h0c1-1 2-1 2-1 1 0 1 0 2 1v1l1-1v-2l1-1z" class="E"></path><path d="M162 501h1c1-1 1-2 1-3h0c1 1 1 2 2 3-1 1-3 0-4 1v-1z" class="D"></path><path d="M166 496l1-1v-2l1-1-1 1 1 1v2h0c0 1 1 2 1 2 0 1 0 2-1 3 0 0-1 0-1-1h0c0-1-1-2-1-3v-1h0z" class="J"></path><path d="M168 492h3s-1 1-1 2c1 1 2 2 2 3l-1 1v3c1 1 0 1 1 0v1c-1 0-2 0-3 1 1-1 1-2 1-4h0v-1h0 0c0-2 0-3-1-4h-1v2h0v-2l-1-1 1-1z" class="o"></path><path d="M162 495c1 0 1 1 2 2v1c0 1 0 2-1 3h-1-1c-1-1-2-3-1-4 0-1 1-2 2-2zm-12 7l2-3v4l1 1v2h0c1-1 0-1 1-3 1 0 1 1 1 1l3-3v-1c1 0 1 0 2 1h0c0 1 1 1 1 1l1 1c1 1 2 1 4 1h1 3 3c1 0 1 0 1 1l1-1h3 2v1 1l-1 1 1 1c-1 1-2 1-3 1h-1-1v1 1c1 0 1 0 2-1v1c1 0 1 0 2 1-4 0-9 1-13 3-1 1-9 4-9 3l-4 2-5 3-1-2h0c0-4 0-8 1-12h0l1-5 1-2z" class="C"></path><path d="M148 509l1-5v6c1 1 2 0 2 1 0 2-2 2-2 3v4c-1-2 0-3 0-4v-1h-1v-4h0z" class="B"></path><path d="M148 509v4h1v1c0 1-1 2 0 4v2c1 0 1-3 2-4v1l1 2c-1 0-1 0-2 1s-2 1-3 1h0c0-4 0-8 1-12z" class="T"></path><path d="M151 517c1-1 1-1 2-1 0-1 1-2 1-3v-1h1c0 1 0 2 1 2h1 0c1-1 1-2 2-4h0c0 1 0 2 1 3h1s-1 0-1 1l-1-1-1 1 1 1c0-1 1-1 1-1 1 0 1 0 2-1h0 2c-2 1-5 2-7 3h-1c-1 1-3 2-4 3h0l-1-2z" class="B"></path><path d="M171 511h1s0-1 1-1v1l2-1v1c1 0 1 0 2-1v1c1 0 1 0 2 1-4 0-9 1-13 3-1 1-9 4-9 3l-4 2-5 3-1-2c1 0 2 0 3-1s1-1 2-1h0c1-1 3-2 4-3h1c2-1 5-2 7-3l2-1h1 0l1 1c0-1 0-1 1-2 0 1 0 1 1 1v-1h1z" class="H"></path><path d="M152 519h0c1-1 3-2 4-3v1l-1 1c1 1-1 0 1 0h1l-4 2-5 3-1-2c1 0 2 0 3-1s1-1 2-1z" class="N"></path><path d="M150 502l2-3v4l1 1v2h0c1-1 0-1 1-3 1 0 1 1 1 1l3-3v-1c1 0 1 0 2 1h0c0 1 1 1 1 1l1 1c1 1 2 1 4 1h1 3 3c1 0 1 0 1 1l1-1h3 2v1 1l-1 1 1 1c-1 1-2 1-3 1h-1-1v1l-2 1v-1c-1 0-1 1-1 1h-1c0-1 0 0-1-1l-1 1v-1c-3 0-1-1-2-1s-2-1-3-1h-1c-1-1-1 0-2 0l-1 1c-1-1-1-2-1-4l-1 1v3h0c-1 0-1 0-1-1-1-1-2-1-3 0v4c-1-1-1-3-1-5l-1-1v4h-1c-1-2 0-5-1-7v-1z" class="b"></path><path d="M158 506c0-1 0-4 1-5 1 1 0 3 0 4l-1 1z" class="D"></path><path d="M154 508v-3h1v2h1c0-1 0-2 1-2 0 1 0 1 1 1v3h0c-1 0-1 0-1-1-1-1-2-1-3 0z" class="K"></path><path d="M174 505l1-1h3 2v1 1l-1 1 1 1c-1 1-2 1-3 1h-1-1v1l-2 1v-1c-1 0-1 1-1 1h-1c0-1 0 0-1-1l-1 1v-1c-3 0-1-1-2-1s-2-1-3-1h-1c-1-1-1 0-2 0 0-1 0-1 1-1h1 2l1 1v-2h1v2c1-1 1-1 1-2v-1c1 1 1 1 1 2h0c1 0 1-2 2-3h2 0c1 0 1 0 1 1z" class="U"></path><g class="D"><path d="M167 508c1-1 1-1 1-2v-1c1 1 1 1 1 2h0c1 0 1-2 2-3h2v1l-1 1c0 1 0 3-1 4v-1s-1 0-2-1v-1h-1v2h-1v-1z"></path><path d="M174 505l1-1h3 2v1 1l-1-1h-1c-1 1-1 2-1 4h-1l-1-1h-2v-1-2-1h0c1 0 1 0 1 1z"></path></g><path d="M173 504c1 0 1 0 1 1v3l-1-1v-2-1h0z" class="E"></path><path d="M165 480h46v9 8c0 1-1 1-1 1-2-1-4-3-6-4-1-1-2-2-4-3-4-2-9-4-14-5l-10-1h0c-13-2-28 1-39 9 0 2-2 3-3 5-1 1-2 4-2 6-2 3-3 8-3 11s-1 6-1 8v3h-1v3l-1 1v-1c-1 0-1-1-1-1s-1-1-1-2v-2h-1c-1-2-1-4-1-6h0v-14-1c-1-1 0-3 0-4 0-3 0-5-1-8 0-3 0-9 2-12h5 6 6 13 12z" class="Y"></path><path d="M200 482c1 0 3 0 4 1h0l-1 2c-1 0-1-1-2-1h-1v-2z" class="D"></path><path d="M203 485c-1 0-1 1-2 1h-2c-1 0-3 0-4-1v-2h0l1-1h4v2h1c1 0 1 1 2 1zm-3 6h3c1 1 2 0 3 0h1v1c1 1 2 1 3 1h1l-1-1c0-1 0-1-1-1l2-2v8c0 1-1 1-1 1-2-1-4-3-6-4-1-1-2-2-4-3z" class="J"></path><path d="M137 494c0 2-2 3-3 5-1 1-2 4-2 6-2 3-3 8-3 11s-1 6-1 8v3h-1v3l-1 1v-1c-1 0-1-1-1-1s-1-1-1-2v-2h-1c-1-2-1-4-1-6h1 1v-2c1-2 0-3 1-5 1-3 2-6 4-9 2-4 5-6 8-9z" class="c"></path><path d="M129 503l2 1c0 1-1 1-1 2-1 1-2 4-2 6s-1 4 0 6c0 1-1 3-2 4h1v4h-2v-5c-1 1-1 3-1 4h-1c-1-2-1-4-1-6h1 1v-2c1-2 0-3 1-5 1-3 2-6 4-9z" class="r"></path><path d="M123 519h1v-2c1-2 0-3 1-5v9c-1 1-1 3-1 4h-1c-1-2-1-4-1-6h1z" class="X"></path><path d="M153 480h12l-6 2c-3 0-6 0-8 1-1 1-2 0-2 1h-1c-2 0-5 1-6 1s-2-1-3 0c0-1-1-2-2-2v1l-1 1-1 1c0-1 0-1-1-2v-1c-1 1-1 1-2 1h-1s-1 2-1 3l2 1v3 1c-1 0-1 0-2 1h1v1c-1 1-1 1-2 1 0 2-1 4-1 5l-1 1c0 1-1 2-2 3h-1c-1 2-1 5-1 7v3 5h-1 0v-14-1c-1-1 0-3 0-4 0-3 0-5-1-8 0-3 0-9 2-12h5 6 6 13z" class="J"></path><path d="M130 487l2 1v3c-1 1-1 1-2 1v-2l-1-1 1-2z" class="U"></path><path d="M121 492c0-3 0-9 2-12h5 6c-3 1-6 1-9 1h0c-1 0-1 0-1 1-1 2-1 7 0 9 0-1 1-3 2-4 1 1 2 1 3 2 0 1 0 2-1 3v1c1 1 2 1 3 1-1 1-1 1-2 1 0 2-1 4-1 5l-1 1c0 1-1 2-2 3h-1c-1 2-1 5-1 7v3 5h-1 0v-14-1c-1-1 0-3 0-4 0-3 0-5-1-8z" class="E"></path><path d="M125 494c1-1 0 0 1 0s2 0 2-1c1 1 2 1 3 1-1 1-1 1-2 1-1 1-1 2-1 3-1 0 0 0 0 1-1 1-1 1-1 2l-1 1s-1 0-1 1h-1c0-3 0-7 1-9z" class="H"></path><path d="M125 494c1-1 0 0 1 0s2 0 2-1c1 1 2 1 3 1-1 1-1 1-2 1-1 1-1 2-1 3-1 0 0 0 0 1-1 1-1 1-1 2l-1 1c0-2 0-3 1-4 0-1 0-1 1-2h-1c-1 0-1-1-2-2z" class="C"></path><path d="M121 492c0-3 0-9 2-12h5-5l-1 39h0v-14-1c-1-1 0-3 0-4 0-3 0-5-1-8z" class="T"></path><path d="M187 508h1 2 0 1c1 2 1 2 3 2v1h0c-1 0-1 1-2 1h4v1l1 3v4l2 1c1 2 1 3 1 5s-1 4 0 6c-1 0-1 0-2 1v1 3h0-1v-1c-2 1-2 1-2 3-3 1-7 0-10 0h-5 0-10-1v-1l-3-1c-2 1-5 0-7 0-1 1-5 1-6 0 1-1 1-1 0-2h0v-1h-2c-1 0-1 0-2 1h-1v-1l1-1 1-2h-2-1c1-1 1-1 0-2v-5-1h1l5-3 4-2c0 1 8-2 9-3 4-2 9-3 13-3-1-1-1-1-2-1v-1c-1 1-1 1-2 1v-1-1h1 1c1 0 2 0 3-1h7z" class="U"></path><path d="M178 522s1 0 2 1l-1 2-1 1v-4h0z" class="M"></path><path d="M170 521h-2v-1h0 2c0-1 0-1 1-1s1 0 1-1l1 1v1l-3 1z" class="F"></path><path d="M173 520h4 2 0 2v1 1h-3 0c-3-1-5 1-8-1l3-1z" class="D"></path><path d="M165 522h1l1 1-1 1c1 0 3 1 3 0h2 0l-1 3h1c1 0 1 0 2-1h0 2 3 0l1-1c0 1-1 1-1 2h1v2c1 1 2 1 3 1-1 0-2 0-3-1-1 0-1 0-2 1h-2l-1 1 1 1h-2l-1-1c0 1 0 0-1 1h-3c1-1 1-1 1-3v-1l1-1h0 0-1c0-1 0-1-1-1-1-1-2 0-3 0 0-1 0-1 1-2h0l-1-2z" class="C"></path><path d="M176 528h1v1l-1 1s-1 0-1-1l1-1zm-6-1c1 1 2 1 3 1l-2 2s-1-1-2-1v-1l1-1z" class="U"></path><path d="M165 522l1 2h0c-1 1-1 1-1 2 1 0 2-1 3 0 1 0 1 0 1 1h1 0 0l-1 1v1c0 2 0 2-1 3h-3l-1-2h-1-2-3-1v-1-2l1 1c1-1 1-2 2-3h0c1-1 1-1 2-1l1 1h1v-1-1l1-1z" class="F"></path><path d="M165 528v1h0-2c0-1 0-1 1-2h1v1z" class="H"></path><path d="M161 525c1 0 1 0 1 1v1h-2v-1l1-1z" class="B"></path><path d="M165 526c1 0 2-1 3 0 1 0 1 0 1 1-1 0-2 1-3 1h-1v-1-1z" class="D"></path><path d="M153 520v4h1v-2h1l1 1h-1v3c2-1 3-1 5-1h0c-1 1-1 2-2 3l-1-1v2 1h1-2v2h-1c-1 0-2-1-3-1h-1l-1 1v-1h-2-1c1-1 1-1 0-2v-5-1h1l5-3z" class="F"></path><path d="M147 523c1 1 1 2 1 2v1 1h2c0 1 0 2 1 3-1 0-1 1-1 2v-1h-2-1c1-1 1-1 0-2v-5-1z" class="H"></path><path d="M150 527s1-1 1-2h0 2 0l-1 2h1c1 0 2-1 2-1 2-1 3-1 5-1h0c-1 1-1 2-2 3l-1-1v2 1h1-2v2h-1c-1 0-2-1-3-1h-1l-1 1c0-1 0-2 1-2-1-1-1-2-1-3z" class="B"></path><path d="M154 528h0 2v1h-2v-1z" class="Q"></path><path d="M158 530h3 2 1l1 2h1 1v1c-1 1-1 1-1 2l-1 1c-1 0-2 1-2 1h-4c-1 1-5 1-6 0 1-1 1-1 0-2h0v-1h-2c-1 0-1 0-2 1h-1v-1l1-1 1-2v1l1-1h1c1 0 2 1 3 1h1v-2h2z" class="R"></path><path d="M161 530h2 1l1 2h1 1v1c-1 1-1 1-1 2l-2-2-1 1v1h0c-1 0-1-1-2-1 0 0-1 0-1 1l-2-1c0 1 0 1-1 1 0-1 0-2 1-3 1 1 2 1 3 1h0v-3z" class="H"></path><path d="M161 530h2v3h-2v-3z" class="B"></path><path d="M158 530h3v3h0c-1 0-2 0-3-1-1 1-1 2-1 3-1 0-2 1-2 1l-2-1h0v-1h-2c-1 0-1 0-2 1h-1v-1l1-1 1-2v1l1-1h1c1 0 2 1 3 1h1v-2h2z" class="Q"></path><path d="M149 533h6 0l-2 2v-1h-2c-1 0-1 0-2 1h-1v-1l1-1z" class="H"></path><path d="M182 530c2-1 4 0 6-1v1 1 1c0 1-1 1-1 2h1v2c3 0 6-1 9 0h0c-2 1-2 1-2 3-3 1-7 0-10 0h-5 0-10-1v-1l-3-1c-2 1-5 0-7 0h4s1-1 2-1l1-1c0-1 0-1 1-2v-1h-1-1 3 3c1-1 1 0 1-1l1 1h2l-1-1 1-1h2c1-1 1-1 2-1 1 1 2 1 3 1z" class="B"></path><path d="M166 536c1-1 2-2 4-2 0 0 0 1 1 1h0c-2 1-3 1-5 1z" class="H"></path><path d="M176 535v-1l2 1v-1-1-1h2v1l-1 1h0c1 0 1 1 2 2h-3c-1-1-1-1-2-1z" class="T"></path><path d="M176 535c1 0 1 0 2 1h3 7c3 0 6-1 9 0h0c-2 1-2 1-2 3-3 1-7 0-10 0h-5 0-10-1v-1l-3-1c-2 1-5 0-7 0h4s1-1 2-1h1c2 0 3 0 5-1 2 0 3 1 5 0z" class="Q"></path><path d="M180 537h6l-1 2h-5 0v-2z" class="H"></path><path d="M166 537h14v2h-10-1v-1l-3-1z" class="F"></path><path d="M187 508h1 2 0 1c1 2 1 2 3 2v1h0c-1 0-1 1-2 1h4v1l1 3v4l2 1c1 2 1 3 1 5s-1 4 0 6c-1 0-1 0-2 1v1 3h0-1v-1h0c-3-1-6 0-9 0v-2h-1c0-1 1-1 1-2v-1-1-1c-2 1-4 0-6 1-1 0-2 0-3-1v-2h-1c0-1 1-1 1-2l1-2c-1-1-2-1-2-1h3v-1l1-3h-1v-1-1h-2c-1-1-2-1-3-1-2 1-3 2-5 1l-1 1h0c-1 0-2 0-3 1h-1-2s-1 0-1-1c-2 1-3 2-4 2h0c2-2 4-2 6-3l1-1c4-2 9-3 13-3-1-1-1-1-2-1v-1c-1 1-1 1-2 1v-1-1h1 1c1 0 2 0 3-1h7z" class="E"></path><path d="M187 508h1 2 0 1c1 2 1 2 3 2v1h0c-1 0-1 1-2 1h-3c-3-1-7-1-10 0-1-1-1-1-2-1v-1c-1 1-1 1-2 1v-1-1h1 1c1 0 2 0 3-1h7z" class="F"></path><path d="M187 508h1 2 0 1c1 2 1 2 3 2v1h0c-1 0-1 1-2 1h-3l1-1h-2c-1-1 0-2-1-3z" class="C"></path><path d="M196 513l1 3v4l2 1c1 2 1 3 1 5s-1 4 0 6c-1 0-1 0-2 1v1 3h0-1v-1h0c-3-1-6 0-9 0v-2h-1c0-1 1-1 1-2v-1-1-1c-2 1-4 0-6 1-1 0-2 0-3-1v-2h-1c0-1 1-1 1-2l1-2c-1-1-2-1-2-1h3v-1l1-3h1 1v1l-2 2 1 1c1 0 1 0 2-1v-2l1-1c1 0 1 0 1 1h0c2-1 1 0 3 0v-2h0 1v2c1 1 0 1 1 1v3h0v1 2c1 0 2 1 2 1 1 1 1 1 0 2l-1 1h0l1 1c1 1 1 2 2 3v-21h0z" class="O"></path><path d="M197 516v4l2 1c1 2 1 3 1 5s-1 4 0 6c-1 0-1 0-2 1v1 3h0-1v-1h0c-1-2 0-5 0-7v-13z" class="g"></path><path d="M180 523c1 0 1 0 2 1v1c0 2-1 1-1 3h1l2-2v-2h0c1-1 2-1 3-1h1 0c-1-1-2 0-2-1l2-3c0 1 1 2 2 2l-1 2s1 0 2 1v5h-1-2c-2 1-4 0-6 1-1 0-2 0-3-1v-2h-1c0-1 1-1 1-2l1-2z" class="P"></path><path d="M187 526h1v1h-1-2v-1h2z" class="C"></path><path d="M189 373l1-1c1 4 4 9 3 13h0c-1 2-1 4-1 6v14 12c0 1 0 1 1 2 0 1 1 2 1 3l1 1c-1 1-1 2-2 4 3 4 6 7 11 10l1-1h1c1 0 2 0 3 1v1h1c1 1 1 3 1 4v2l1 25v1l-1 4v25h0v-2-8-9h-46-12-13-6-6-5c-2 3-2 9-2 12v6-1c-1 1-1 3-1 4h0v-4l-1-19c0-2 1-5 0-7 0-3 1-7 0-10 0-2-1-5-2-7l1-9h0v-9c0-1 0-2 1-3l-1-1v-1c1 0 1 0 1-1 1-1 1-2 1-3h-2v-2h0c1 0 1 1 2 1h2s-1 0 0 1h0 2v-1h1v-2-1l2 1v1l-1 1v3h1 1 2l1 1c1-1 1-1 1-2 1 0 2-1 2-2l2 2 1-2h1 1v-2c1 0 1 0 2 1v-2h0v-4-1-2c-1-2-1-4-1-6l2 7 1 1c4 0 9 0 13-1l1 1h0l9-1c1 0 3-1 5-1h1l5-1h0v-8-14c0-2 0-4-1-6-1-1-1-1-1-2h2l1-1h2 4 1 5 2 0c0-2-1-5-1-7-1-1-1-2-2-4z" class="w"></path><path d="M205 436h1c1 0 2 0 3 1v1l-1 1c-2 0-3-1-4-2l1-1z" class="T"></path><path d="M135 464c0 1 0 1 1 2v-1c1 1 1 1 1 2h-11-1c2 0 5 0 7-1l3-1v-1z" class="U"></path><path d="M182 416h0c2 0 4 0 6 1v1h0c-2 0-5 1-6 0v-2z" class="D"></path><path d="M209 438h1c1 1 1 3 1 4v2l-2-2c0-1 1-2 0-3h-1l1-1z" class="J"></path><path d="M186 412c-1 1-3 0-4 0-1-3-1-7 1-10 0 2 0 8 1 9h1v1h1z" class="R"></path><path d="M179 442h-1l1 1-1 1c-3-1-8-3-10-6v-1c2 2 3 2 5 3 1 0 3 1 4 1h1l1 1zm10-11v1c0 1 0 1-1 1 2 1 4 1 6 1l-1 1c-1 0-2 1-3 1-1 2-2 3-4 4l1-2v-1c1-2 1-3 0-5 1 0 1 0 2-1zm3 6h1v2h2c1 0 2 0 3 1-2 0-4 1-5 1-2 0-2 0-3 1h0c-2 1-3 2-5 2h0c0-1 2-1 3-2s3-2 4-4v-1z" class="J"></path><path d="M141 440l2 1c2 1 7 0 10 0h0c-4 1-9 0-12 2 0 1 0 2 1 2 0 1 1 1 2 2v1h-2 0l1-1h0s-1-1-2-1-2 0-2-1v-1-1-1l2-2z" class="D"></path><path d="M135 438l1 1c1 0 1 0 2-1v2h3l-2 2v1 1 1c0 1 1 1 2 1-1 0-2 0-3 1l-2-1-1-1v-7z" class="B"></path><path d="M138 440h3l-2 2v1c-1 0-1 0-1 1h0l-1 1-1-1c1-1 1-1 1-2v-2h1z" class="C"></path><path d="M183 402c0-1 0-1 2-1 0 0 1 0 1 1 1 2 1 8 0 10h-1v-1h-1c-1-1-1-7-1-9z" class="Z"></path><path d="M208 453c1 5 1 9 2 14l-11-1 1-1 1 1c1-1 2-1 3-2v-2h1v-2c0-1 0-1 1-2h0v3l1 1v-1c2-2 1-5 1-8z" class="B"></path><path d="M175 448c0-1 0-2-1-3h-2v-1c1 0 2 1 3 1h0c1 0 2 1 3 1h1l3 3c1 2 0 5 0 6v5c0 1 1 1 1 2v2s0 1-1 1v1s0 1-1 1c1-2 1-7 0-9v-5c0-1-1-3-2-3-1-1-2-2-4-2z" class="J"></path><path d="M204 449c1-1 2-1 3 0s1 3 1 4h0c0 3 1 6-1 8v1l-1-1v-3h0c-1 1-1 1-1 2v2h-1v-4-5h-1c0-2 1-3 1-4z" class="Z"></path><path d="M204 449c0 1-1 2-1 4h1v5 4 2c-1 1-2 1-3 2l-1-1c-1-2 0-5 0-8 0-1 0-3 1-4 0-2 1-4 3-4z" class="S"></path><path d="M200 465c-1-2 0-5 0-8v6 1c1-1 1-1 1-2 1 0 1 1 1 1l1-1v-1c0-1 0-2 1-3v4 2c-1 1-2 1-3 2l-1-1z" class="F"></path><path d="M204 449c0 1-1 2-1 4h1v5c-1 1-1 2-1 3v1c-1 0-1-3-1-4s-1-1-1-1v-2h0v-2c0-2 1-4 3-4z" class="p"></path><path d="M148 459c0-2 0-3 1-5 0 0 1-1 2-1s1 0 2 1 1 2 1 4v4 2h-1-5v-5z" class="Q"></path><path d="M152 455v1c0 1 0 2 1 2-1 1-1 1-2 1v3l-1 1c-1-1-1-1-1-2l1-1c0-1-1-1-2-2 1-1 3-2 4-3z" class="g"></path><path d="M153 454c1 1 1 2 1 4v4l-2 1h-2 0l1-1v-3c1 0 1 0 2-1-1 0-1-1-1-2v-1-1h1zm32-70h5 2l-1 1c-1 1-1 1-2 1l-1-1-1 1-1-1c-1 1 0 1-1 1v2h0c-1-1-1-1-1-2h-1c-1 1-1 1-1 2h0c-1 0-2-1-2-2h-1c-1 2-1 3-1 5l1 1-1 1h0l1 1s0 1-1 1l1 2-1 1h0v1c-1 2 0 6 0 8 0 1 0 2-1 3v-17c0-2 0-4-1-6-1-1-1-1-1-2h2l1-1h2 4 1z" class="G"></path><path d="M171 427l3-3v1c-5 8-10 15-20 16h-1c-3 0-8 1-10 0l1-1-1-1h1v-2c1 1 1 1 2 1h0 0l1 1h3l1 1c1 0 2 0 3-1 2 0 4 0 6-1s4-3 6-5 4-3 5-6z" class="I"></path><path d="M144 439v-2c1 1 1 1 2 1h0 0l1 1h3c-2 1-4 0-6 0z" class="S"></path><path d="M138 447c1-1 2-1 3-1s2 1 2 1h0l-1 1h0v1c1 1 3 2 3 4v2h0c1 2-1 4 1 6l-1 1v2c1 0 1 1 2 0v-2h0c0-1 1-2 1-3v5 1h-3l-1-3v-5l-1-2c0-1-1-2-2-2-1-1-1 0-3 0h-1-1v-3l1-1-1-3 2 1z" class="f"></path><path d="M136 446l2 1h3l1 1c-2 0-4 0-5 1l-1-3z" class="C"></path><path d="M138 447c1-1 2-1 3-1s2 1 2 1h0l-1 1h0l-1-1h-3z" class="T"></path><path d="M136 450h2 1c-1 1-1 2-2 3h-1v-3z" class="U"></path><path d="M136 453h1 1l-1 2 1 1v5c-1 1 0 2 0 3 1 1 2 0 3 1 1 0 2-1 3-1v-2l1 3h3v-1h5 1v-2-4c1 2 0 6 1 8v1h-18c0-1 0-1-1-2v-5-1-6z" class="D"></path><path d="M181 453v5c1 2 1 7 0 9l-10-1v-1-10l1 3 1 1c0 1-1 1-1 1v1h5v-1c1 1 1 0 2 0h0c0-1 1-2 1-2l1-1v-4z" class="M"></path><path d="M181 461v3h-2c1-1 1-2 2-3z" class="B"></path><path d="M181 453v5h0v1 2c-1 1-1 2-2 3h0-2-1c-1 0-2 0-2 1l-1-1v-2l-1-1h5v-1c1 1 1 0 2 0h0c0-1 1-2 1-2l1-1v-4z" class="N"></path><path d="M138 453c2 0 2-1 3 0 1 0 2 1 2 2l1 2v5 2c-1 0-2 1-3 1-1-1-2 0-3-1 0-1-1-2 0-3v-5l-1-1 1-2z" class="R"></path><path d="M140 459h0l2-2v1h0c0 1 1 1 1 2s-1 1-1 1c0 1 1 2 0 2-1 1-2 1-3 0 0-2 0-3 1-4z" class="j"></path><path d="M138 453c2 0 2-1 3 0 1 0 2 1 2 2l1 2c-1 0-1 1-2 1v-1l-2 2h0 0c-1-1-2-2-3-4l1-2zm37-5c2 0 3 1 4 2 1 0 2 2 2 3v4l-1 1s-1 1-1 2h0c-1 0-1 1-2 0v1h-5v-1s1 0 1-1l-1-1-1-3 1-2c0-2 1-3 2-4h0l1-1z" class="g"></path><path d="M179 455c0 1 0 1-1 2h-2c-1 0-2 0-3 1v-1-2l1 1h1 2 0c1 0 1-1 2-1h0z" class="j"></path><path d="M171 455l1-2v3c0 1 1 1 1 1v3h2v-1h2 1l1 1h0c-1 0-1 1-2 0v1h-5v-1s1 0 1-1l-1-1-1-3z" class="G"></path><path d="M174 453v-3c2 0 2-1 4-1l1 1-1 1-1 1v1h1l1-1 1 1c0 1 0 1-1 2h0c-1 0-1 1-2 1h0-2-1l-1-1c0-1 0-1 1-2z" class="p"></path><path d="M174 453h1v3h-1l-1-1c0-1 0-1 1-2z" class="t"></path><path d="M177 393v17c1-1 1-2 1-3 0-2-1-6 0-8v17 1l-1 2h1 0c0 2-2 5-4 6v-1l-3 3h-1 0l-1-1v-1l1-1v-1h-1-1c0-1 0-2 1-3h0v-3h-3c1 0 3-1 5-1h1l5-1h0v-8-14z" class="M"></path><path d="M172 416l5-1c-1 3-2 5-4 7h0c0-1 1-5-1-6h0 0z" class="C"></path><path d="M166 417c1 0 3-1 5-1h1 0c0 3 0 8-2 10h-1v-1l1-1v-1h-1-1c0-1 0-2 1-3h0v-3h-3z" class="D"></path><path d="M189 373l1-1c1 4 4 9 3 13h0c-1 2-1 4-1 6v14 12c0 1 0 1 1 2 0 1 1 2 1 3l1 1c-1 1-1 2-2 4-4-8-2-17-3-25v-2-1c0-2 0-2-1-3v-1h1v-1h-1c-1-1 1-2 1-3-1-1 0-3-1-4h-2v1c0-1-1-1-1-2h-1c1 0 0 0 1-1l1 1 1-1 1 1c1 0 1 0 2-1l1-1h0c0-2-1-5-1-7-1-1-1-2-2-4z" class="B"></path><path d="M187 387l1-1h3c0 1-1 4-1 5-1-1 0-3-1-4h-2zm-62 49h5 2c1 0 1 0 2 1 1 0 1 0 1 1h0v7l1 1 1 3-1 1v3 6 1 5 1c-1-1-1-1-1-2v1l-3 1-3-1h3 1c0-1 0-2 1-3v-1c0-2 1-6 0-8-1 0-1 0-2-1 1-1 2-2 2-4-2-1-7 0-8 0l-1-1h2v-1h1 1v-2h1 1v-2h-1l-1 1v-2l1-1h-1v-1h-4-1v-1-1l1-1z" class="E"></path><path d="M128 446h1v-2h1c1 1 1 2 0 3h-3v-1h1z" class="M"></path><path d="M125 436h5 2c1 0 1 0 2 1 0 1 0 1-1 2h-2v-2c-1 0-1 1-2 2h-3c-1-1-1-1-1-2v-1z" class="J"></path><path d="M130 440c1-1 2-1 3-1v2l-1 1c2 2 1 3 1 5h-1-1 0l1-1-1-1v-1-2h-1l-1 1v-2l1-1z" class="M"></path><path d="M135 445l1 1 1 3-1 1v3 6 1 5 1c-1-1-1-1-1-2-1-6-1-13 0-19z" class="O"></path><path d="M178 441l-2-3c-1-1-1-3-1-4 1-3 3-5 5-6s4-1 6 0c2 0 3 2 3 3-1 1-1 1-2 1 1 2 1 3 0 5v1l-1 2h0c-1 1-3 2-4 2h-3l-1-1z" class="E"></path><path d="M178 436l-1 1h0l-1-1c0-2 1-3 2-4h1l-1 3v1z" class="J"></path><path d="M178 436v-1l1 1h0c1 0 2 0 2 2l1-1h0 2c1 0 1 0 2 1h1l-1 2h0-2c-1 0-2 1-2 0-3 0-3-2-4-4z" class="R"></path><path d="M182 437h0 2c1 0 1 0 2 1l-2 1c-1-1-1-1-2-1l-1 1v-1l1-1z" class="m"></path><path d="M179 432c1-1 2-2 4-2s2 1 4 2c1 2 1 3 0 5v1h-1c-1-1-1-1-2-1h-2 0l-1 1c0-2-1-2-2-2h0l-1-1 1-3z" class="I"></path><path d="M179 436c0-2 1-3 3-4 1 0 2 0 3 1h0v1h-1-2c-1 1-1 2 0 3l-1 1c0-2-1-2-2-2z" class="a"></path><path d="M123 465c-1-3 0-6 0-9 0-2 0-8 1-9h1l1 1c1 0 6-1 8 0 0 2-1 3-2 4 1 1 1 1 2 1 1 2 0 6 0 8v1c-1 1-1 2-1 3h-1-3c-2 1-4 0-6 0z" class="J"></path><path d="M130 456c1 0 1 0 2 1-1 2 0 4-1 6h-2c-1-1-1-4-1-5l2-2z" class="B"></path><path d="M141 423c1 1 2 2 2 3-1 2 0 4 0 6 1 0 2 0 3 1l1 1h0v3l-1 1h0c-1 0-1 0-2-1v2h-1l1 1-1 1-2-1h-3v-2c-1 1-1 1-2 1l-1-1h0c0-1 0-1-1-1-1-1-1-1-2-1h-2-5 1c0-1-1-1-2-1v-1h0c1-1 1-2 2-2h0 1l2-2h2c1-1 1-1 1-2 1 0 2-1 2-2l2 2 1-2h1 1v-2c1 0 1 0 2 1v-2z" class="Z"></path><path d="M134 431s0-1 1-1c0-1 0-1 1-1 0 1 0 1 1 2 0 1 1 1 1 2-1 0-2 0-3-1l-1-1z" class="m"></path><path d="M135 432c1 1 2 1 3 1v2c-1 1-1 2-1 3h1c-1 1-1 1-2 1l-1-1h0v-4-2z" class="I"></path><path d="M141 423c1 1 2 2 2 3-1 2 0 4 0 6 1 0 2 0 3 1l1 1h0v3l-1 1h0c-1 0-1 0-2-1v2h-1l1 1-1 1-2-1h-3v-2h-1c0-1 0-2 1-3 0 0 0 1 1 2h0l1-1 2-2h0v-1-2h-2c-1-1-1-2-1-2 1-1 1 0 2 0v-4-2z" class="h"></path><path d="M143 432c1 0 2 0 3 1h-1c0 1-1 2-2 2 1 0 1 1 1 2l-1 1v-1h-1c0-1 0-1 1-2v-3z" class="I"></path><path d="M146 433l1 1h0v3l-1 1h0c-1 0-1 0-2-1v2h-1v-1l1-1c0-1 0-2-1-2 1 0 2-1 2-2h1z" class="Q"></path><path d="M134 426l2 2v1c-1 0-1 0-1 1-1 0-1 1-1 1l1 1v2 4c0-1 0-1-1-1-1-1-1-1-2-1h-2-5 1c0-1-1-1-2-1v-1h0c1-1 1-2 2-2h0 1l2-2h2c1-1 1-1 1-2 1 0 2-1 2-2z" class="K"></path><path d="M134 431l1 1v2s0-1-1-1h-3-1l-1 1h-5c1-1 1-2 2-2h0 4 0 1c1 0 2-1 3-1z" class="C"></path><path d="M134 426l2 2v1c-1 0-1 0-1 1-1 0-1 1-1 1-1 0-2 1-3 1h-1 0-4 1l2-2h2c1-1 1-1 1-2 1 0 2-1 2-2z" class="a"></path><path d="M189 446h3c2 0 4 2 5 4 0 1 1 5 0 7v10h-6-6c0-1-1-1-1-1l1-1c0-1-1-2-1-3h0 0v-1c0-3 0-7 1-10h0l1-2c0-2 2-3 3-3z" class="E"></path><path d="M196 460l1 6h-1-1c0-1-1-1-1-2h1l1-1v-3z" class="H"></path><path d="M196 452c1 3 1 5 0 8v3l-1 1h-1v-1l-1 1-1-2c1 0 1 0 1-1h1l1-1c0-1 1-2 1-3-1-2-1-3 0-5z" class="G"></path><path d="M195 466c-3 1-7 0-10 0 0-4 0-8 1-11v6l1 2h-1v1c1 1 1 0 3 0h0 1v1h1c0-1 1-1 2-1l1-1v1c0 1 1 1 1 2z" class="M"></path><path d="M186 455v-2c0 1 0 2 1 3v3c1-1 1 0 1-1h1c1 1 1 2 2 3v-3c1 1 1 3 1 4l1 2c-1 0-2 0-2 1h-1v-1h-1 0c-2 0-2 1-3 0v-1h1l-1-2v-6z" class="L"></path><path d="M186 455v-2c0 1 0 2 1 3v3c1-1 1 0 1-1 0 2 1 4 1 6h-1-1v-1l-1-2v-6z" class="Q"></path><path d="M190 448h1l2 2c0 1 0 2-1 3h-2v3h1v2 3c-1-1-1-2-2-3h-1c0 1 0 0-1 1v-3c-1-1-1-2-1-3l1-1c1-2 2-3 3-4z" class="g"></path><path d="M190 448h1l2 2c0 1 0 2-1 3h-2-1v2h-1v-2h0c1-1 1-1 1-2 1-1 1-1 1-2v-1z" class="i"></path><path d="M191 448c1 1 2 1 3 1l2 3c-1 2-1 3 0 5 0 1-1 2-1 3l-1 1h-1c0 1 0 1-1 1 0-1 0-3-1-4v-2h-1v-3h2c1-1 1-2 1-3l-2-2z" class="j"></path><path d="M191 456h1c0 1 0 1 1 2v-1h1v3 1h-1c0 1 0 1-1 1 0-1 0-3-1-4v-2z" class="R"></path><path d="M155 466h2v-1-1-4c-1-1-1-3-1-4h1c-1-1 0-1-1-1h0 1-2 0l-1-1 1-2c-2 0-2-1-2-2h0l3-3h2s1 0 2-1h1c0-1 1-1 2-1 4 0 6 4 8 7 1-1 1-2 0-3h1v1l1-1h1c-1 1-2 2-2 4l-1 2v10 1l-13 1h-3v-1z" class="J"></path><path d="M162 449c1-1 1-1 2 0 1 0 2 1 2 3 1 2 1 5 1 7-1-1-1-1-2-3l1-1c0-1 0-1-1-2h-2 0c1-1 1-1 1-2l-1-2h-1z" class="G"></path><path d="M159 453v-1c0-1 1-3 3-3h1l1 2c0 1 0 1-1 2h0c0 1-1 2-1 3l-1 1c0 1 0 2-1 4v-3c0-1-1-1-1-1v-3-1z" class="I"></path><path d="M162 456l-1-1h-1c0-1 0-3 1-3 0-2 0-2 2-3l1 2c0 1 0 1-1 2h0c0 1-1 2-1 3z" class="h"></path><path d="M159 453v1 3s1 0 1 1v3c0 1 1 2 2 3h1 4s0 1 1 1c1 1 2 0 3 0v1l-13 1c0-4-1-7 0-11 0-1 0-2 1-3z" class="C"></path><path d="M159 453v1 3s1 0 1 1v3c0 1 1 2 2 3h1c-1 1-2 1-3 1-1-1-1-2-2-2v-7c0-1 0-2 1-3z" class="B"></path><defs><linearGradient id="h" x1="161.145" y1="453.89" x2="165.548" y2="462.841" xlink:href="#B"><stop offset="0" stop-color="#666565"></stop><stop offset="1" stop-color="#8a8988"></stop></linearGradient></defs><path fill="url(#h)" d="M163 453h2c1 1 1 1 1 2l-1 1c1 2 1 2 2 3v5h-4-1c-1-1-2-2-2-3 1-2 1-3 1-4l1-1c0-1 1-2 1-3z"></path><path d="M140 410l2 7 1 1c4 0 9 0 13-1l1 1h0l9-1h3v3h0c-1 1-1 2-1 3h1 1v1l-1 1v1l1 1h0 1c-1 3-3 4-5 6s-4 4-6 5-4 1-6 1c-1 1-2 1-3 1l-1-1h-3l-1-1h0l1-1v-3h0l-1-1c-1-1-2-1-3-1 0-2-1-4 0-6 0-1-1-2-2-3h0v-4-1-2c-1-2-1-4-1-6z" class="F"></path><path d="M157 433h1v4h-1v-1-3z" class="T"></path><path d="M164 427h1v5h-1v-3l-1-1 1-1z" class="X"></path><path d="M149 428c1 1 1 2 0 3v2c0 1 1 3 0 4 0 1-1 1-1 1l-1 1-1-1h0l1-1v-3h0v-2l1-1c0-2 0-2 1-3z" class="N"></path><path d="M147 434v-2l1-1v3h-1 0z" class="B"></path><path d="M148 434v4l-1 1-1-1h0l1-1v-3h1z" class="T"></path><path d="M143 418c4 0 9 0 13-1 0 3 0 5-1 7l-1 1v-7h-1v8h-1v-8h-3v4 2c-2-2-1-4-1-6h-5 0z" class="D"></path><path d="M140 410l2 7 1 1h0 5c0 2-1 4 1 6v-2h0v-3h1c0 3 1 7-1 9-1 1-1 1-1 3l-1 1v2l-1-1c-1-1-2-1-3-1 0-2-1-4 0-6 0-1-1-2-2-3h0v-4-1-2c-1-2-1-4-1-6z" class="H"></path><path d="M141 419c1 2 1 3 2 4l2 2v1h-2c0-1-1-2-2-3h0v-4z" class="S"></path><path d="M140 410l2 7 1 1h0 5c0 2-1 4 1 6v-2h0v4l-1 1-1-1s-1 0-1 1l-1-1c0-1 0-2 1-2-1-1-1-2-2-2l-1 1c-1-1-1-2-2-4v-1-2c-1-2-1-4-1-6z" class="B"></path><path d="M166 417h3v3h0c-1 1-1 2-1 3h1l-1 3h-1-2 0-2v2 1h0v6c-1-2-1-3-1-5l-2-1c-1 0-1 0-1-1v-2-1h-1 0v2c0 1-1 2-1 2h-1c0-1-1-2-2-2v-2l1-1c1-2 1-4 1-7l1 1h0l9-1z" class="X"></path><path d="M166 420h1v3h0l-1-1v-1-1z" class="E"></path><path d="M160 429h0v-2h0c1 0 2 1 2 2v1l-2-1z" class="O"></path><path d="M157 418h0v3h0c1-1 1-2 1-3l1 1v5h0 1v-5l1-1c1 2-1 5 1 6l-2 2h-1v-1h-1 0v2c0 1-1 2-1 2h-1c0-1-1-2-2-2v-2l1-1c1-2 1-4 1-7l1 1z" class="C"></path><path d="M156 417l1 1c0 1 0 3-1 5v3l-1-1v-1c1-2 1-4 1-7z" class="B"></path><path d="M125 423l2 1v1l-1 1v3h1 1 2l1 1h-2l-2 2h-1 0c-1 0-1 1-2 2h0v1c1 0 2 0 2 1h-1l-1 1v1 1h1 4v1h1l-1 1v2l1-1h1v2h-1-1v2h-1-1v1h-2-1c-1 1-1 7-1 9 0 3-1 6 0 9 2 0 4 1 6 0l3 1c-2 1-5 1-7 1h1l-2 1v1h3 0c2-1 6 0 8 0h0 2c1 0 3-1 4 0h1 1c1-1 7 0 9 0h1 4 3 2 2 3 1 0 3 0 2 1c1 0 1 0 2-1l1 1h6 2 4 14 9v1l-1 4v25h0v-2-8-9h-46-12-13-6-6-5c-2 3-2 9-2 12v6-1c-1 1-1 3-1 4h0v-4l-1-19c0-2 1-5 0-7 0-3 1-7 0-10 0-2-1-5-2-7l1-9h0v-9c0-1 0-2 1-3l-1-1v-1c1 0 1 0 1-1 1-1 1-2 1-3h-2v-2h0c1 0 1 1 2 1h2s-1 0 0 1h0 2v-1h1v-2-1z" class="Y"></path><path d="M189 469h14 9v1h-3v1 1l-1 1-1-1v-2h-1v2l-1 1 1 2 4 1h0c-2 1-4 0-5 0-1-1-1-4-1-6h-2v1c0 1 0 2-1 3 1 0 1 1 2 1-1 1-2 1-3 1h-2-1s-1 0-1-1v-1c0-1 0-3-1-4-1 0-2 0-2 1v1c0 1 0 1-1 2 0 1 0 1-1 1-1-1 0-3 0-4l-1-1s-1 0-1-1z" class="M"></path><path d="M196 475h1v-5h2c1 2 1 4 1 5v1h-2-1s-1 0-1-1z" class="J"></path><path d="M124 478h62c8 1 17 0 24 1h1v-5 25h0v-2-8-9h-46-12c4-1 9 0 13 0 1-1 3 0 5-1h5c0-1-7 1-9 0-4-1-9 0-13 0h-21c-3 0-6 0-9-1z" class="T"></path><path d="M183 469h2 4c0 1 1 1 1 1l1 1c0 1-1 3 0 4-1 1-3 1-4 1-4 0-7-1-11 0l1-3v-4h6z" class="f"></path><path d="M185 469h4c0 1 1 1 1 1v1c-1 0-1 0-1-1h-1c0 1-1 4-2 4v-2l-1-3z" class="B"></path><path d="M183 469h2l1 3-1 3c-1 0-1 0-2-1v-1-4z" class="J"></path><path d="M177 469h6v4 1h-1c0-1-1-2-1-3-1-1-1-1-2-1 0 1 0 2-1 2l-1 1v-4z" class="N"></path><path d="M137 469c1 0 3-1 4 0h1 1c1-1 7 0 9 0h0v6l-1 1-7-1c-1 1-3 1-4 1l-8-1h0 1c1-2 1-4 2-6h0 2z" class="K"></path><path d="M143 469l1 1c-1 1-1 3-2 3h0c-1-2-1-2 0-4h1z" class="N"></path><path d="M151 473l-2-3 1-1h2c0 1 0 3-1 4h0z" class="L"></path><path d="M145 474h0c1-2 0-3 0-4 1-1 2-1 3-1v1c0 2-1 3-2 4h-1z" class="Q"></path><path d="M152 469v6l-1 1-7-1 1-1h1c1 0 3 1 4 0l1-1h0c1-1 1-3 1-4z" class="U"></path><path d="M135 469h0 2l1 1h0c-1 1-1 1-1 2 1-1 2-1 2-2l1-1v1 6l-8-1h0 1c1-2 1-4 2-6z" class="P"></path><path d="M173 469h1c1 0 1 0 2-1l1 1v4l-1 3h-1-8-12-4l1-1v-6h0 1 4 3 2 2 3 1 0 3 0 2z" class="E"></path><path d="M155 475h0l1-1c2 0 2 0 4 1-2 1-3 0-5 0z" class="U"></path><path d="M162 469v4h-1-1v-4h2z" class="N"></path><path d="M160 475h0c2 0 5-1 7 0v1h-12v-1c2 0 3 1 5 0z" class="X"></path><path d="M157 469c-1 1-1 3-2 4-1 0-1-1-2-2v-2h4z" class="Q"></path><path d="M167 469l-1 4h-1c-1-1-1-1-1-2-1-1-1-1 0-2h3z" class="H"></path><path d="M173 469h1c1 0 1 0 2-1l1 1v4l-1 3h-1v-1l-1-1c0-1 0-1 1-2 0-1 0-2-1-3h-1z" class="J"></path><path d="M125 423l2 1v1l-1 1v3h1 1 2l1 1h-2l-2 2h-1 0c-1 0-1 1-2 2h0v1c1 0 2 0 2 1h-1l-1 1v1 1h1 4v1h1l-1 1v2l1-1h1v2h-1-1v2h-1-1v1h-2-1c-1 1-1 7-1 9 0 3-1 6 0 9 2 0 4 1 6 0l3 1c-2 1-5 1-7 1h1l-2 1v1h3 0c2-1 6 0 8 0h0 0c-1 2-1 4-2 6h-1 0l-1 1-7-1-1 1v2h1c3 1 6 1 9 1h21c4 0 9-1 13 0 2 1 9-1 9 0h-5c-2 1-4 0-5 1-4 0-9-1-13 0h-13-6-6-5c-2 3-2 9-2 12v6-1c-1 1-1 3-1 4h0v-4l-1-19c0-2 1-5 0-7 0-3 1-7 0-10 0-2-1-5-2-7l1-9h0v-9c0-1 0-2 1-3l-1-1v-1c1 0 1 0 1-1 1-1 1-2 1-3h-2v-2h0c1 0 1 1 2 1h2s-1 0 0 1h0 2v-1h1v-2-1z" class="G"></path><path d="M120 433v1h1v10h-1c0-2 0-3-1-5 0-2 0-4 1-6z" class="h"></path><path d="M118 445h0c1 2 1 4 1 6 0 3 1 7 0 10 0-2-1-5-2-7l1-9z" class="B"></path><path d="M119 430v2l1 1c-1 2-1 4-1 6 1 2 1 3 1 5-1 2-1 4-1 7 0-2 0-4-1-6v-9c0-1 0-2 1-3l-1-1v-1c1 0 1 0 1-1z" class="Q"></path><path d="M122 473v-11-17c1 1 1 2 1 3 0 4-2 15 0 17 2 0 4 1 6 0l3 1c-2 1-5 1-7 1h-2c-1 2-1 4-1 6z" class="F"></path><path d="M125 467h1l-2 1v1h3-1c0 2 0 4-1 6h-1 0l-1 1v2c-1-1-1-4-1-5 0-2 0-4 1-6h2z" class="P"></path><path d="M124 475v-4-2h2c0 2 0 4-1 6h-1z" class="H"></path><path d="M125 423l2 1v1l-1 1v3h1 1 2l1 1h-2l-2 2h-1 0-3c-1 0-1 1-2 2h-1v-1l-1-1v-2c1-1 1-2 1-3h-2v-2h0c1 0 1 1 2 1h2s-1 0 0 1h0 2v-1h1v-2-1z" class="Z"></path><path d="M119 432c1 0 2-1 3-1h1v1c-1 0-1 1-2 2h-1v-1l-1-1z" class="c"></path><path d="M122 427h2v3h0 3 0c-2 1-4 1-5 0h-1l1-3z" class="R"></path><path d="M125 423l2 1v1l-1 1v3h1 1 2l1 1h-2-2-3 0v-3-1h1v-2-1zm2 46c2-1 6 0 8 0h0 0c-1 2-1 4-2 6h-1 0l-1 1-7-1h0 1c1-2 1-4 1-6h1 0z" class="B"></path><path d="M125 475h2c1-2 1-3 1-5h1v1s0 1 1 1h0l1-2v6l-7-1h0 1z" class="X"></path><path d="M127 469c2-1 6 0 8 0h0 0c-1 2-1 4-2 6h-1 0l-1 1v-6l-1-1h-3z" class="E"></path><path d="M132 475v-6h3c-1 2-1 4-2 6h-1z" class="N"></path><path d="M124 434v1c1 0 2 0 2 1h-1l-1 1v1 1h1 4v1h1l-1 1v2l1-1h1v2h-1-1v2h-1-1v1h-2-1c-1 1-1 7-1 9 0 3-1 6 0 9-2-2 0-13 0-17 0-1 0-2-1-3 1-3 0-8 2-11z" class="C"></path><path d="M124 439h1v4l-1-1v-3z" class="M"></path><path d="M127 446l-1-1v2h-1c-1-1-1-2-1-3h1 0 1c0-1 1-2 1-3h0c1 1 1 2 1 3v1 1h-1z" class="T"></path><path d="M212 394h0l-1-24v-80l1-45-1-32c0-16 0-32-3-48-3-13-9-28-18-38-11-11-30-15-44-15-15-1-32 4-43 14-6 6-8 12-8 19 0 5 0 8 4 12 1-3 3-5 6-6 2-2 6-2 9-1 2 1 4 4 5 6 2 4 1 8-1 11-1 4-5 7-9 9-4 1-10 0-14-2s-8-6-9-11c-3-11 0-24 6-35 9-14 23-21 39-25 23-6 48-3 72-2l87 2 35-1 29-3c9-1 19-1 28-1 12 0 24 0 35 2 29 3 56 11 81 26 3 2 5 4 8 6 17 14 31 33 36 54 3 12 5 26 4 39 0 20-7 43-18 61-7 12-18 22-30 31-7 5-14 10-22 14h-1 0 0c-2 0-3-1-4-2l-2-3-1-1c-1 1-2 0-4 0 1-1 1 0 1-1h1l-3-3c-1 0-1-1-1-1-2-1-2-2-3-3-1-3-5-7-7-10h0c-1 1 2 3 2 5h-1l-2-3v-1c-1 0-1-1-1-1v-1h-1c-1 1-1 1-2 1v2l1 1h0c1 2 2 3 2 4 1 0 1 0 1 1v1 3l1 2 4 5 1 1h0c0 1 1 2 1 3v1h-1v-1h-1c0 2 3 3 3 5h-1c1 1 1 1 1 2 1 1 2 2 2 3h1c1 1 2 2 2 3h2l1 3 3 3c1 1 1 1 1 2l1 1 1 1c1 0 2 0 2-1h0c2 1 2 2 3 4 1 1 2 1 2 2 1 2 2 2 3 4h-1l1 2v1h-1l1 1c1 0 2-1 2-2h1v-2h0c1-1 1-1 2-1v-1l-2-1 1-1 2 1v1h-1c0 1 0 1 1 2l2 1h0c1-1 0-1 0-2 1 0 3 1 4 2s2 2 3 2c0 1 1 2 2 3v1h-1v2s-1 1-1 2h1c0 1 1 1 1 3 1 0 1-1 2 0h2 0c0 1 1 1 1 2 0 0 0 1 1 1l2 1h0c1 0 1 0 1 1 1 1 2 1 3 2l1 2h0-1l1 3-2-1v-1s-1-1-1-2h-2v1l1 1c1 1 1 1 1 2s0 0-1 0h0-1l-1 1h1v1c-1 1-1 1-1 2-1 0-2-1-2-1 0-1 0-2-1-2s0-1-1 0c-1 0-1 0-2-1 0-1 0-1-1-3l-1 1c0 1 1 1 1 3v1 1c-1 0 0 0-1 1l1 1v1c0 1 1 2 2 3v-2c0-1 2-2 2-3h0 1c0 1 1 1 1 2l-1 2 1 1 1-1v1c1-1 1-1 1-2h1v1l1 1-1 2v1h1v-1l1-2c1 0 1 0 2 1h0l1-1 2 4v-1l1 1v1l4 5c1 1 1 1 1 2 1 1 1 2 2 3l1 2 1-1 2 1-1 2s1 1 1 2c1-1 1-1 1-2l2 2c1 1 1 2 2 3s1 1 1 2l1 1c1 1 1 1 2 1h1v1c2 0 3 1 4 1 0-1-1-1-1-2h0-1c0-1-1-1-1-2h-1v-2-3c0-1 0-1-1-2v-2c-1-1-1-1-1-2l-2-2c0-1 1-1 1-2 2 1 2 3 3 4 1 0 1 0 2 1h1c2 2 3 3 3 5l-1 1h0 0 0c0 1 0 2 1 3v2c0 1 1 1 1 2 1 2 1 4 2 6 0 1 1 4 2 5h0v-1h0s0-1-1-1v-1h1v1c0 1 1 2 2 2 0-1-1-2-1-3h0 1l-1-1c1 3 2 6 3 7h1v1l1-1v2c1 1 1 2 2 3h1c1 0 2 1 2 1h2 0c0 1 0 2 1 2v2c1-1 1-2 1-4v-1c1 0 1 0 1 1s1 3 1 5c0 1 0 2 1 3h1c0 1 1 2 1 4l1-2 2 10c1 3 1 7 3 10h0 1l2 13c1-1 1-6 1-6 0 1 0 2 1 3h0v-1c1 0 0-1 0-2h0c1 1 1 2 1 3v4l2 13c0 1 0 1 1 2v5c1 4 2 8 4 12 1 3 1 6 2 8s1 4 2 6c2 6 6 11 10 15 0 1 3 3 3 3 1 1 2 3 4 4 2 0 4 2 6 3 0 0 2 1 2 2l9 3c3 1 6 3 10 3h0 2c1 0 1 1 2 0h-1l1-1c2 0 3 0 5-1h0c1-1 4-1 6-1l1 1 6-2 7-3c2 0 4-2 6-3 1-1 2-1 4-2 1-1 1-2 3-2h-2c-1-1 0-1 0-2v-1c3-4 7-9 7-13l4-8c0-1 2-9 2-10 1-10 0-21-4-30-4-10-12-18-21-22-5-2-12-3-16-1-1 1-3 1-4 2h1c-1 1-2 1-3 2-1 2-2 4-2 7s2 4 3 6h0c-2-1-4-3-5-5v2c1 1 2 2 2 3l1 1h0c-2-1-2-2-3-3l-1-1-1-1c1 1 1 2 1 2-1 1 0 1-1 1l-1-2c-2-3-3-6-2-10 1-2 2-5 3-7 1-1 1-1 1-2 3-5 9-9 15-11 3 0 6-1 9-1s6 1 10 2v1l6 3c1 0 2 0 3 1 3 2 5 4 7 5s4 3 4 4l4 6h0c0 2 1 2 2 4l1 1c0 1 1 2 1 3 1 1 1 2 2 4l2 1 1 6 2-1v1h2l2 11 1 6c0 5 1 10 0 15-1 1-1 3-2 4v2l-2 7-1 5c0 1-1 2-1 4h1c0-1 1-2 2-3l-2 5c-1 3-3 5-3 8s-4 8-6 9c-1 1-1 2-1 3l1-1v1l-6 7c-1 1-2 1-3 2-3 2-7 6-10 8h1c1 0 3-2 4-2-2 3-6 4-9 6l-8 4v1 1l-7 1s-2 0-2 1c-3 0-6 2-9 3h-1c-1-1-2-1-3-1v-1c-2 0-4 1-6 1h-2c-1 1-3 1-4 1 0-1 0-2-1-2l-3 1h1l1 1v1c-1 0-1 0-2 1h0c-2 0-5 0-7-1v-1c-1 0-2 1-3 1-2 0-3 0-5-1-1-1-3-1-5-2h-1c0-1-1-1-2-2-1 0-1 0-2-1h1 1l1 1h0c1 1 3 2 4 2h0v1h1 2s1 0 2 1h0 3v-1h-2c-1-1-1 0-2 0h0l-1-1h-1 0c-1 0-2 0-3-1l-6-3s-1 0-1-1l-2-1c-1-1-3-2-5-3l-6-6c-1-1-2-2-3-2h-1l-2-2c-1-1-2-2-3-4h-2c-3-1-4-4-5-6-1-1-1-1-1-2-2-3-3-6-3-10l-2-6c-1 5-2 10-4 15v-9l-3 14c-4 14-15 24-27 33l-8 5c-2 2-5 4-8 5-6 4-12 6-19 8-7 3-15 6-24 9-13 3-28 5-42 7l-10 2c-23 3-47 3-70 2l-79-3-41 1-50 1c-20 2-40 4-60 3-4 0-8 0-12-1s-8-2-11-6c-2-3-3-6-2-10 1-5 4-8 8-12 0 6 2 9 6 13 7 6 17 6 26 6 6-1 12-3 18-5 15-5 28-11 38-23 3-5 6-9 9-14l1-1c0-1 2-6 3-7l2-6h0l2-11v-2l1-26v-40h0v-25l1-4v-1l-1-25v-2c0-1 0-3-1-4h-1v-1c1-1 1-2 1-3h1v-2-7-12c-1-2-1-3-1-4s0-1 1-1 0-1 1-2c-1-1-1-3-1-4 0-2 0-5 1-8z" class="x"></path><path d="M381 638h4v1h-1c-2 0-2 0-3-1z" class="e"></path><path d="M366 311h1c-1 1-1 2-2 3l-1-1 2-2zm55 105c0 2 1 3 0 5h0l-1-4h1v-1z" class="d"></path><path d="M249 285h3c0 1 0 2-1 2l-2-2z" class="p"></path><path d="M482 209l3 2c-1 0-2 1-3 1l-1-1 1-2z" class="R"></path><path d="M245 241h0c1-1 1-1 3-1v2h-2l-1-1z" class="o"></path><path d="M225 254l1 1v2 1h-2l1-4z" class="C"></path><path d="M473 573c1 0 1-1 1 0l1 1-1 1h0l-1-1v-1z" class="v"></path><path d="M217 619l1-1c1 0 2 1 3 2h-1-1c-1 0-1 0-2-1z" class="h"></path><path d="M524 282h2c0 1 0 1-1 2h-1c-1-1-1-1-2-1v-1h2z" class="B"></path><path d="M241 514c1 1 1 1 1 3h-2l-1-2c1-1 1-1 2-1z" class="D"></path><path d="M665 582c0-1 1-1 1-1l1-1h1l1 1c0-1 0-1 1-1l1-1v1c-1 1-4 1-5 3l-1-1z" class="d"></path><path d="M284 258l2 3c-1 1-1 2-2 2h0c-1-1 0-3 0-5z" class="a"></path><path d="M230 600c0 1 1 1 2 2v-1l2 3c-1 0-2-1-2-1l-2-3z" class="t"></path><path d="M439 410c-2 0-3-1-4-2h1l1 1v-1-1h2v1 2zm-59-122c2 2 2 2 2 4h0l-2-1c-1 0-1-1-2-2h1 1v-1z" class="q"></path><path d="M521 189c1 0 2-1 3 0 1 0 1 1 2 2-2 0-3 0-5-2z" class="H"></path><path d="M512 292h3 1c-1 2-1 2-2 2s-1 0-2-1v-1z" class="R"></path><path d="M271 509c0 1 1 2 1 3h-4v-2h2l1-1z" class="G"></path><path d="M285 615c0-2-1-2-1-3l1-1c1 1 1 2 1 3s0 2-1 2v-1z" class="B"></path><path d="M530 574v1l-1 1h1c-1 1-1 2-3 2l-1-1 1-1v-1h-2 3c1 0 1 0 2-1z" class="n"></path><path d="M498 189h4v1c-1 1-3 1-4 1v-2z" class="F"></path><path d="M237 631l1 1h1l1 1c-1 1-2 2-4 2h0c0-2 1-3 1-4z" class="Z"></path><path d="M472 308v2c1 0 1 0 1 1v1c0 1 0 2 1 3l-3-3v-1c0-1 0-2 1-3z" class="g"></path><path d="M380 115h7l-3 1v1c-2 0-3-1-5-1 0-1 1-1 1-1z" class="l"></path><path d="M693 479l1 1 1-1v2 2 1l-3-4 1-1z" class="W"></path><path d="M655 586l-1 1h0c-1 0-2-1-3 0h-1c-1 0-3 1-4 0h1c1-1 2-1 3-2 2 1 3 1 5 1z" class="d"></path><path d="M237 469l1 1c0 1 1 2 0 4h-1v-5z" class="G"></path><path d="M479 597h2l1 1-5 2 1-1-2-1c1 0 2 0 3-1z" class="C"></path><path d="M418 390l-1 6v3c-1-1-1-3-1-4v-2h0l1-1 1-2z" class="n"></path><path d="M175 624h1v-1 1l1 2-1 1-1 2-1-1c0-1 1-2 1-4z" class="d"></path><path d="M245 469c1-1 2-3 3-3 1 1 1 2 1 3l-2 1s-1-1-2-1z" class="p"></path><path d="M278 316c0 2-1 4-2 5h-1v-4c1 0 2-1 3-1z" class="L"></path><path d="M273 488l2-2h1l1 2-2 2h-1v-1l-1-1z" class="N"></path><path d="M225 611l1 1h1l-1 1c0 1 0 1-1 2-1-1-2-1-3-2l3-2z" class="S"></path><path d="M245 241v-1c0-1 0-2 1-3h1l1 1v2c-2 0-2 0-3 1h0z" class="K"></path><path d="M501 196c1-2 2-3 4-4v1c0 1-1 1-2 2h1v3c-1-1-2-1-3-2z" class="c"></path><path d="M210 434h1v-2 10c0-1 0-3-1-4h-1v-1c1-1 1-2 1-3z" class="E"></path><path d="M438 579c2-1 5 0 8-1h0l1-1 2 2s-1 0-2 1c-1-1-2-1-3-1h-6z" class="e"></path><path d="M470 454l2 3v1c0 2-1 3-1 5 0-1 0-1-1-1v2c-1-3 0-7 0-10z" class="v"></path><path d="M468 141h0c0-2 2-3 4-3 0 1 0 1-1 2s-1 1-2 3c0-1-1-1-1-2z" class="T"></path><path d="M460 148v-1c0 1 1 1 1 1 0 1 0 2-1 3l-1 1c-1-1-1-1-1-2s1-1 2-2z" class="g"></path><path d="M212 603h4l-1 4-3-2c-1-1 0-1 0-2z" class="R"></path><path d="M267 122c1 0 2-1 3-2-1 2-1 4-3 5 0 0-1 1-2 1 0-2 1-2 2-4z" class="I"></path><path d="M514 578h1s1 0 1-1c0 0 0-1 1-1 0 0 0-1 1-1h0l1 1c1 0 1 1 1 1-2 1-2 1-4 1-1 0-1 1-2 1v-1z" class="e"></path><path d="M226 570v-2c1 3 1 7 1 10h-1v-4c-1-1 0-2 0-3h0v-1z" class="L"></path><path d="M507 234l2-2h0c1 0 2 1 3 2-1 1-2 1-3 1h0-1l-1-1z" class="E"></path><path d="M221 594h0c0 2 2 4 1 5h-2c0-1-1-2-1-4h1 0v-1l1 1v-1z" class="G"></path><path d="M342 636h4 4v1h-11v-1h3z" class="X"></path><path d="M249 228h2 1c0 2 0 2-1 3l-1 1-1-2v-2z" class="o"></path><path d="M691 572v3c-1 1-2 1-3 2 0 0-1 0-1-1l4-4z" class="R"></path><path d="M248 226v3 1c0 1-1 1-1 1-1 0-1 0-2-1 0-2 1-3 2-4h1z" class="E"></path><path d="M286 346v-1c1-2 0-4 0-7l-1-1c1 0 1 0 2 1 1 2 1 5 1 7l-2 1zm-27-111l1-1c1 1 2 1 3 1 0 1 0 2-1 3h-1c-1-1-2-2-2-3z" class="J"></path><path d="M279 502l1-1c1-1 2-2 3-2l-3 7-1-1v-3z" class="h"></path><path d="M278 404c1-2 2-3 3-5-1 3-1 6-3 8h0c-1 0-1-1-2-1h0l2-2z" class="v"></path><path d="M242 628h1 1l-4 5-1-1h-1l-1-1 1-1h2c1 0 1-1 2-2z" class="p"></path><path d="M176 627h4l1 1-2 2h-2c0-1-1-1-2-1l1-2z" class="F"></path><path d="M485 273h0l1 1v5 2h-1c-1-2-1-6 0-8z" class="G"></path><path d="M224 460c2-2 4-3 4-5 1 0 2 0 2 1v1h-2v1l-1 1c0 1 0 1-1 2h-1l-1-1z" class="Q"></path><path d="M515 585l1-1v1 2c-1 1-2 1-3 2v1l-1-1h-1l2-2c1 0 1 0 2-1h-1l1-1z" class="d"></path><path d="M237 518h4l-1 1c0 1 0 1-2 2v1h-1v-1c-1 0-1 0-2-1 1 0 2-1 2-2zm276 91c1 0 1-1 2 0 0 1-1 2-2 3-1 0-2 1-3 1v-1l2-2h-1 0l2-1z" class="M"></path><path d="M479 208l3 1-1 2 1 1-1 1c-1 0-2-1-3-2h1v-3z" class="N"></path><path d="M257 278l3 2c-1 1-1 2-2 2h-3l1-1h0c0-1 0-1-1-1v-1h1l1-1z" class="I"></path><path d="M286 211c1 1 2 2 2 4l-2 2h-2-1 1c1-1 2-1 3-1l-1-1c-1-1 0-1-1-1l1-2-1-1h1z" class="G"></path><path d="M478 228c0 1 1 3 0 4-2 0-3 0-5-1 1 0 2-1 3-1 0-1 1-1 2-1v-1z" class="H"></path><path d="M245 231h2l1 1v4c-1 0-2-1-2-2-1-1-1-1-1-3z" class="f"></path><path d="M490 307c1 1 1 2 1 3h1c0 2 0 4-1 5-1 0-2 0-2 1l-1-1 1-1c2-2 2-4 1-7z" class="C"></path><path d="M501 584h1 1l-1 1c1 1 1 1 1 2s0 1-1 1l-3-1v-1h1c0-1 0-1 1-2z" class="u"></path><path d="M236 169c1 1 1 2 1 3-1 2-2 4-4 5l-1 1c0-1 1-2 2-3 0-2 1-3 2-3v-1-2z" class="j"></path><path d="M430 567h1c1 0 1 1 2 1h0 4 1l-1 1c-3 0-3 0-5 1 0 1-1 1-1 1-1-1-1-1-1-2v-1-1z" class="e"></path><path d="M219 605c2 0 5 1 6 2l2 1c-2 0-5 0-7-1-1-1-1-1-1-2z" class="G"></path><path d="M553 494v-3h0c3 2 3 7 5 9v1 1 2h0 0v-1l-1-1-2-6v-1l-1 1h0l-1-2z" class="d"></path><path d="M346 636c3-1 7 0 9-2l1 1v1c-2 1-4 1-6 1v-1h-4z" class="F"></path><path d="M275 242l2 1c0 2-1 3-3 5h0l-1 1c1-2 1-3 1-5h0l1-1v-1z" class="N"></path><path d="M255 219h2v2c-1 0-1 1-2 1l-2 1v-2c0-1 1-2 2-2z" class="D"></path><path d="M269 487l4-4 1 1-1 2v2h0l1 1-2 2-1-1c0-1 1-2 1-3v-1l-3 1z" class="a"></path><path d="M175 629c1 0 2 0 2 1h2l2 2-3 1c-1 0-1-1-2-1-1-1-1-2-1-3z" class="P"></path><path d="M701 485l2 1 1 6v1c-1 0-1-1-2-2 0-2-1-4-1-6z" class="n"></path><path d="M504 608h5c1 0 3 0 4 1l-2 1h0 0-3c-2 0-3-1-4-2h0z" class="d"></path><path d="M256 275h1 0c2-1 3 0 5 0h0c-2 2-4 2-6 2h-1v-1h-1 0c1-1 1-1 2-1zm-7 10v-1l3-3h1l1 2c0 1-1 2-2 2h-3z" class="g"></path><path d="M264 251c1 1 1 1 2 3h0c-1 1-2 1-3 1-1-1-1-2-2-3 1-1 2-1 3-1z" class="F"></path><path d="M391 631v1l1 1h-1c-2 2-5 2-7 2 0 0-1 0-1-1 2-1 5-2 8-3z" class="Z"></path><path d="M254 276h1v1h1l1 1-1 1h-1v1c1 0 1 0 1 1h0l-1 1-2-2v-2h0c1 0 1-1 1-2z" class="G"></path><path d="M502 232c-1-1-3-1-3-2v-1h6c0 1-1 2-2 2l-1 1z" class="P"></path><path d="M505 229c1 1 2 1 3 2l-2 3c-1 0-1 0-1-1h-2l-1-1 1-1c1 0 2-1 2-2z" class="D"></path><path d="M275 242s-1 0-1-1 1-2 1-3l1-1c1 1 1 1 1 2v4l-2-1z" class="C"></path><path d="M235 612c0-1 1-2 2-2 1 2 2 3 1 6v-1c-1 0-2 0-3 1v-1l1-1c0-1 0-1-1-2z" class="k"></path><path d="M246 445c1 0 1 0 2 1h-1c-2 1-4 1-6 2h1 0-1 0l-2-1c2-1 4-2 6-2h1z" class="G"></path><path d="M225 174s1 1 2 1v3h1v2h-3v-2-2c-1-1-1-1 0-2z" class="B"></path><path d="M261 489l1-2 2-2h0v-2c1 1 1 4 2 5l-1 1c-1-1-2 0-4 0z" class="h"></path><path d="M183 628c1 0 3-1 4 1h1l-2 1h-2c-1 1-2 1-3 0 0-1 0-1 1-2h1z" class="P"></path><path d="M269 245c1-1 2-3 4-3 1 1 0 1 0 3 0 1-1 2-2 3 0-1 0-2-1-3h-1z" class="U"></path><path d="M240 201c1 2 0 5-1 6v1c-1-1-1-2-1-3s-1-2-1-3h3v-1z" class="R"></path><path d="M183 626c-1-1-1-1-1-2 1-1 3-2 5-3v1 3c-1-1 0-1-1-1s-2 1-3 2z" class="B"></path><path d="M219 323l-1 2h0c-1-4 0-7 1-11h1c0 2-1 4-1 5v4z" class="k"></path><path d="M493 600l5-1c-2 2-3 3-5 4h0-3l3-3z" class="h"></path><path d="M267 223l-1 1c-1 0-3-1-4-2 1-1 2-2 2-3h2v2c1 0 1 1 1 2z" class="P"></path><path d="M369 109c2 2 4 3 6 4h0l-4 1-3-3c1 0 1 0 1-2z" class="S"></path><path d="M340 630c0-2 1-4 2-5s1-1 2-1c1 1 1 2 0 3h0l-2 2-1 1h-1z" class="T"></path><path d="M236 459v3h-2v1h-1v1 1c-1 2-1 3-1 5v1c-1-3-1-7 0-10l2 1c0-1 0-2 1-2l1-1z" class="G"></path><path d="M181 107c2 0 3 0 5 1h0l-1 2h-1c-2 0-3-1-4-2v-1h1z" class="O"></path><path d="M516 579c1 0 2-1 3-1h0 1c1 0 1 0 2 1v-1 1h3l1-1h1c-1 1-1 1-2 1 0 1-1 0-2 0 0 0-1 0-1 1h-1l-1 1c-1-1-1-1-2-1h-2v-1z" class="s"></path><path d="M242 448h3c1-1 1-1 2-1l1 1h-1-2c-1 1-1 1-2 1-1 1-1 1-1 2l-1-1-1-1-2 1v-1l1-2 2 1h0 1z" class="B"></path><path d="M260 240l1 1 1 1c1 0 2 2 2 3s0 1-1 1c0 1-1 1-2 2v-1h0v-4-1c-1 0-1-1-1-2z" class="D"></path><path d="M419 633c3-1 8-4 11-3l-5 3-2 1c-1 0-2 0-4-1z" class="I"></path><path d="M271 525v-1h1c1 3 1 7 1 10h0c-2-1-2-7-2-9z" class="B"></path><path d="M540 498c0 1 0 2 1 3v2c1 2 1 5 1 6h-1 0c-1-1-1-5-2-7h1v-4z" class="q"></path><path d="M498 140c1-1 2-1 3 0 1 2 2 4 2 7h-3l1-2h1v-1l-1-1-1 1-1-1h0c1-1 1-1 1-2l-2-1z" class="F"></path><path d="M247 226l2-2c0-1 1-1 2-1s1 0 1 1v3h-2l-1-1h-1-1z" class="D"></path><path d="M264 230h1v1c1-1 1-1 1-2h1 0c0 1 1 2 2 2l-1 1v3c-1 0-1-2-2-2h-2c-1-1 0-1 0-3z" class="Y"></path><path d="M268 245v1l-1 1v1 3s-1 0-1-1c-1 0-3-1-4-2l1-1c1 0 2 0 2-1 1 0 2 0 3-1z" class="P"></path><path d="M469 164h1c0-2 0-2 1-3l2 2c-1 1-2 2-2 4-1 0-1 2-1 2h-1c0-2 0-3-1-4l1-1z" class="L"></path><path d="M280 470h0 1c1 0 2-1 3-1v5h-2-2c0-1 1-2 1-3l-1-1z" class="H"></path><path d="M263 509v2h2l-6 9v-1l4-10z" class="a"></path><path d="M269 237v-1-1-1c0-1 0-2 1-3 2 1 3 3 4 5l-4-1c-1 1-1 1-1 2z" class="E"></path><path d="M260 200c2 0 3-1 5-1 2-2 3-4 4-6 0 1-1 2-1 3l-3 6-1-1h-3l-1-1z" class="F"></path><path d="M218 331c1 0 2 1 3 2 1 2 1 2 1 4v1h-1c-1-1-1-2-2-2v-1l-1-2c-1-1 0-2 0-2z" class="R"></path><path d="M429 633c2 0 6 0 8-1s3-3 6-3v1c-2 0-2 1-3 2-1 2-8 2-11 2v-1z" class="p"></path><path d="M284 336h1v1 12c-1-4-3-9-1-13z" class="J"></path><path d="M500 144l1-1 1 1v1h-1l-1 2c1 2 1 2 0 3l-2 1v-1l2-6z" class="X"></path><path d="M274 314l1-1h1c-1 2-1 3-1 4v4h-1-1 0v-4c0-1 0-2 1-3z" class="d"></path><path d="M230 207s0-1 1-1c0 0 0 1 1 1h1c1 0 2 1 3 2v2c-1 1-2-1-3-1l-3-3h0z" class="K"></path><path d="M235 166c-1 1-3 4-5 4l-1-1c0-1 1-1 1-2 1-1 3-3 5-3l-1 1 1 1z" class="F"></path><path d="M457 615c1 0 2-1 3-2 0 0 1 0 2-1h1c0 1-1 2-1 3v2h-2 0 0c-2-1-2-1-3-2z" class="H"></path><path d="M262 223c2 2 2 2 2 4v2h0c-1 0-1 1-2 1 0-1-1-2-2-2v-1h1c1-1 1-2 1-4z" class="Y"></path><path d="M225 218c-2 0-3 0-5-1h0c1-2 3-3 6-4l-1 2c0 1 0 1 1 1l-1 2z" class="B"></path><path d="M469 326h1l-1-1h0v-1-1l6 8h0 0c-2 0-3-1-4-2l-2-3z" class="p"></path><path d="M249 376c-1-1-2-1-2-2l-2 1-1-1 1-1c0-1 1-1 2-2 2 0 2 1 3 2 0 0-1 1-1 2h1l-1 1h0z" class="g"></path><path d="M264 227l2-1c1 0 1 0 3 1v4c-1 0-2-1-2-2h0-1c0 1 0 1-1 2v-1h-1v-1h0v-2z" class="b"></path><path d="M412 624c2-1 2-1 4-1 1 1 2 2 2 4v1c-1-1-3-1-4-2-1 0-2-2-2-2z" class="g"></path><path d="M340 630h1l1-1 2-2h0l2 4c-2 0-4 1-6 1v-2z" class="S"></path><path d="M418 628c0 1-1 1-1 2l-3-1c-1 0-2-1-3-3 0-1 0-1 1-2 0 0 1 2 2 2 1 1 3 1 4 2z" class="a"></path><path d="M416 395c0 1 0 3 1 4v-3 8 5c-1 1-1 2-1 2v-2-1c-1-4-1-9 0-13z" class="v"></path><path d="M286 346l2-1c0 5-1 9-3 13h0c0-4 0-9 1-12z" class="f"></path><path d="M503 180c0-1-2-3-3-4s-1-2-1-4h1v1h1l2 4h0c0 1 1 2 2 2v1h-2z" class="l"></path><path d="M241 185c0 2 1 3 2 4l2 2v-3 4 4h0l-3-4 1-1v-1l-1-1c-1-1-1-2-1-4z" class="p"></path><path d="M445 611s0-1 1-1c0 0 1 0 1 1l1 1c0 1 1 2 1 2 0 2-1 2-2 4v-1-1-2c-1 0-1 0-2-1h0l-1 1c0-1 0-2 1-3z" class="g"></path><path d="M445 611s0-1 1-1c0 0 1 0 1 1v1h-2v-1z" class="I"></path><path d="M476 221h2c0 1 1 1 1 2-1 1-1 1-1 2-3 1-3 1-5 1 1-1 2-2 2-4l1-1z" class="B"></path><path d="M248 543l1 1c-2 2-3 4-4 7-1 1-1 2-1 3l-1 2c0-1 0-1-1-2l6-11z" class="L"></path><path d="M231 354c-1 1-2 1-3 1v-3c1-2 4-3 6-3v2c-2 1-3 1-4 2 1 1 0 1 1 1z" class="B"></path><path d="M212 394l-1 31v-12c-1-2-1-3-1-4s0-1 1-1 0-1 1-2c-1-1-1-3-1-4 0-2 0-5 1-8z" class="E"></path><path d="M232 535c0 1 0 1 1 1l1-1 1 1v1-1c1 1 1 1 1 2-1 1-2 2-3 2-1 1 0 1-1 1l-1-1v-3l1 1v-1-2zm-6-23l1 1c0 1-1 2 0 3l-1 2-2-1v1h-2v-1c1-2 3-4 4-5z" class="O"></path><path d="M269 245h1c1 1 1 2 1 3v1c-1 1-2 2-4 2v-3-1l1-1v-1h1z" class="Y"></path><path d="M267 248h1 1l2 1c-1 1-2 2-4 2v-3z" class="E"></path><path d="M386 626h2 0c-3 3-6 5-10 7v-1-1h2c1-1 1-1 1-2 2 0 3-1 5-2h0v-1z" class="I"></path><path d="M219 207c1 1 1 2 2 2l1 1-1 1h1 4c1 0 1 0 2 1h-1c-3 1-8 0-10-2h0 1c0-1 1-2 1-3z" class="H"></path><path d="M245 469c1 0 2 1 2 1l2-1-1 5v-1h-1c-2 1-2 2-2 4-1-3-1-5 0-8z" class="G"></path><defs><linearGradient id="i" x1="218.076" y1="500.784" x2="217.593" y2="494.765" xlink:href="#B"><stop offset="0" stop-color="#656767"></stop><stop offset="1" stop-color="#7b7b7b"></stop></linearGradient></defs><path fill="url(#i)" d="M218 494l1 1c0 2 1 5 1 7v2c-2-3-3-5-4-8l2-2z"></path><path d="M266 113c2 1 3 3 3 6l1 1c-1 1-2 2-3 2v-2l1-1c-1-2-1-3-2-4l-1 1-1-1c1-1 1 0 1-1l1-1h0z" class="G"></path><path d="M458 426c1 1 1 1 1 2v4 2c-1 1 0 2-1 3v2c0 1 0 1-1 2v-1h0v-1c1-1 0-3 1-4 0-1-1-2 0-2v-2 1h0v1 1c-1 2 0 3-1 5l-1-2 1-6 1-5z" class="u"></path><path d="M650 585c2 0 4 0 5-1h3c2 0 5-1 7-2l1 1c-2 0-3 0-4 1-2 1-5 2-7 2s-3 0-5-1z" class="v"></path><path d="M233 464v-1h1v-1h2c0 2 2 5 2 8l-1-1-2-1c0-1-1-2-1-3v-1h-1z" class="l"></path><path d="M232 603s1 1 2 1l4 4-1 1c0-1-1-1-1-1h0l-1 1v-1l-1-1h0c-1 0-2 1-2 1h-1-2-1c1-1 4-1 5-2 0-1-1-2-2-2l1-1z" class="k"></path><path d="M414 320v-1-1h0v-5h1v-4h0c1-2 1-4 0-5l1-1c0 2 0 5-1 7 0 2 1 2 1 4 0 1 0 4-1 5l-1 1h0z" class="n"></path><path d="M235 612c1 1 1 1 1 2l-1 1v1c1-1 2-1 3-1v1 2c-1 1-1 1-3 1 0-1-1-1-1-2-1-2 0-3 1-5z" class="g"></path><path d="M231 347l-1 1c-2 0-3-1-5-2h0c-2-1-3-4-4-6h1 2c-1 0-1 0-2 1h1c0 1 1 2 1 3h1 1c1 1 2 1 4 1v1h0c0 1 0 1 1 1z" class="S"></path><path d="M270 408h2c1-1 3-1 4-2h0c1 0 1 1 2 1h0c0 1-1 2-2 3h-1l-1 4c0-2 1-3 0-5l-1 1-1-1h-1l-1 1v-1-1z" class="n"></path><path d="M503 147c-1 1-1 5-3 6l-1 1c0-1-2 0-3 0h0v-1l2-3v1l2-1c1-1 1-1 0-3h3z" class="L"></path><path d="M234 349c1-1 3-2 5-2-1 2-2 3-3 5-2 0-4 2-5 2s0 0-1-1c1-1 2-1 4-2v-2z" class="O"></path><path d="M469 612h1c1-2 7-4 9-4 1 1 1 2 2 3l-2 1c-1-1-2-2-3-2-2 0-5 2-6 3l-1-1z" class="l"></path><path d="M512 288l2 1 2 1c0 1 1 1 0 2h-1-3c-1-1-1 0-2-1v-1l1-1v-1h1z" class="Q"></path><path d="M259 235c0-1-1-2-1-3h-1c1-1 1-2 2-3h0 1c2 2 3 3 3 6-1 0-2 0-3-1l-1 1z" class="f"></path><path d="M375 113c2 1 3 1 5 2 0 0-1 0-1 1-2 0-4-1-6 0h-7c3-1 6-1 9-2v-1h0z" class="I"></path><path d="M472 308s0-1 1-1h2c1 1 2 2 3 4h-1 0c-1 0-2 0-3 1h-1v-1c0-1 0-1-1-1v-2z" class="S"></path><path d="M472 308s0-1 1-1h2c-2 1-1 1-2 2v2c0-1 0-1-1-1v-2z" class="R"></path><path d="M166 104h1c2 1 4 3 5 4l-1 1h-2c-1 0-2-2-3-3v-2z" class="D"></path><path d="M237 450c0-1 0 0 1-1v1l2-1 1 1 1 1h-1v2h-1c-1 0-3 0-4-1l-3 2c1-1 1-2 1-3v1h1v-1l2-1z" class="e"></path><path d="M259 253s-1-1-1-2 1-1 1-2l1-1v1c2 0 3 1 4 2-1 0-2 0-3 1 1 1 1 2 2 3h-2l-2-2z" class="U"></path><path d="M691 572c2-1 3-4 5-6l-1-1 2-2c0 1 1 1 0 2s-1 2-1 3l1-1v1l-6 7v-3z" class="Q"></path><path d="M462 163h2c1 1 1 1 2 1h1l1 1c1 1 1 2 1 4l-2-1c-2-1-4-2-5-5z" class="l"></path><path d="M522 205l2-1h1 0c0 2 0 3-2 5l-3 2c0-1 0-1-1-2h1v-1c1 0 1-1 2-1v-2z" class="S"></path><path d="M221 429v-1-1-3h4s1 0 1 1 0 2-1 3c-1 0-2 1-3 1h1c-1 0-1 1-2 1v-1z" class="p"></path><path d="M462 615c1 0 1-1 2-2 2-1 4-2 5-2-1 0-1 1-2 2l-2 1v1c2-1 3-2 4-3l1 1c-2 2-3 4-6 4h-2v-2z" class="Q"></path><path d="M214 116v-1l1-1v-1c2 4 3 7 5 11-3-2-5-5-6-8z" class="F"></path><path d="M220 531l1-1v1c0 1 0 2 1 3h1v1l1-1h1c0 2 0 3-1 4-2 0-3 0-4-1v-3h0c-1-1 0-2 0-3z" class="C"></path><path d="M176 624c1-1 3-3 5-4v1c1 1 1 2 0 3 0 1 0 1-1 2h-3l-1-2z" class="F"></path><path d="M273 610s0-1 1-1c1-3 4-4 7-5l-2 4c-1 0-2 1-3 2h-3z" class="G"></path><path d="M383 634c0 1 1 1 1 1 2 0 5 0 7-2 1 1 1 1 2 3-3 0-9 0-10 1-1 0-2-1-2-1v-1c1 0 1-1 2-1z" class="u"></path><path d="M546 469l2 2 1 1v1h0l1 1h0v1c1 1 1 2 2 3 1 2 3 4 4 6-2-2-4-5-6-6v-1h0v-1h-1c-1-1-1-2-2-3h0l-2-3 1-1z" class="s"></path><path d="M223 264c-1-1-2-3-2-5-2-3-3-6-5-9 4 3 6 7 7 11v2 1z" class="b"></path><path d="M255 245v-1c1 0 1 0 2 1l1 3c-1 1-1 2-2 2h-2c0-1-1-1-1-2s1-2 2-3z" class="f"></path><path d="M550 497c1 2 1 3 2 5v2c1 0 0 1 0 1 1 1 1 2 1 3h0v2h0v1l-1-1-1-2v-1-2l-1-1c0-1 1 0 0-1h0v-2-1c-1-1-1-1-1-2h1v-1z" class="e"></path><path d="M223 436h1v5c1 2 1 4 2 5-1 1-1 0-2 0l-1-1c-2-3 0-2-1-4v-1c0-2 0-3 1-4z" class="p"></path><path d="M246 208c4-4 8-6 14-8l1 1c-5 2-10 4-14 7h-1z" class="H"></path><path d="M447 627c1 2 2 4 2 6-1 1-2 0-3 0s-3-2-3-3v-1c1 0 2 0 2-1 1 0 1 0 2-1z" class="R"></path><path d="M274 623c1 1 2 1 3 1 1 1 1 2 1 3-1 1-1 2-2 2l-2-1c-1-1-2-1-2-2 1-1 1-2 2-3z" class="N"></path><path d="M253 499l1 2c1 4 1 7 1 11h0-1l-3-10h2v-3z" class="Q"></path><path d="M225 147c1-1 1 0 2 0 2 1 2 4 2 6h0c-2 0-2 0-3-1-1-2-2-3-1-5z" class="R"></path><path d="M513 577v-1l2-1c-1 0-1 0-2-1l-2 2h-1l1-1h-2-2c-1 0-1 1-2 1s-1 0-2 1c-1 0-1 0-2 1h0c1-2 3-2 5-3 2 0 3-1 5-1h1 5c3-1 4 0 6 1h1l1 1v1c-2 1-3 1-4 0h-1l1-1c1 0 0 1 1 0v-1h0-1 0c-1-1-1-1-2-1h-2 0l-1 1v-1c0 1 0 1-1 2l-1 1h-1z" class="s"></path><path d="M241 514c0-1 0-1-1-1l-1-1c1 0 1-1 1-1h2l1 1 1-1v1l1 1 1 1c-1 1-1 2-2 3h-2c0-2 0-2-1-3z" class="X"></path><path d="M425 498h1c1-2 1-5 2-8 0 4 0 8 1 11h-1-3v1l-1-1c1 0 1-1 1-2v-1z" class="J"></path><path d="M506 165c0-1 0-3 1-4 1-2 4-4 6-5l1 1h-1c0 1 0 2-1 3s-4 2-5 4v1h-1z" class="B"></path><path d="M511 512v3 2c-1 1-1-1-1 1 0 1 1 2 0 3v1c-1 1-1 1-1 2v1c0 1 0 1-1 2-1-1-1-2-1-3v-2c1-1 1-1 1-2h0c0-1 1-3 0-4h0c1-1 0-4 1-6v1 9l1-1c0-2-1-6 1-7z" class="v"></path><path d="M216 436v-1c1-2 1-3 3-4h0v-2-1c-1-1-1-2 0-3h0c0 1 0 2 1 3l1 1v1l1 1c-1 2-2 2-3 2 0 1-1 2-2 3h-1z" class="G"></path><path d="M261 255h-2c-2 0-5 0-7-1-1-1-1-1-1-2 1-1 1-2 3-2v2h1c1-1 2 0 3 1h1l2 2z" class="B"></path><path d="M276 181c1 2 1 2 2 3 1 0 1 0 2 1-1 2-5 4-7 5l-1-2c2-2 4-3 4-7z" class="H"></path><path d="M231 450l2 1-2 7s0 1-1 2h-1c0 1 1 2 1 2v1h-1c0-1-1-2-1-2 0-1 0-1 1-2l-1-1v-1h2v-1c0-1-1-1-2-1l1-2h0c1-1 1-2 2-3z" class="F"></path><path d="M274 248c1 1 2 1 3 2 0 1 0 2-1 2-2 1-3 2-5 2v-1-2c1 0 1-2 2-2l1-1z" class="M"></path><path d="M437 416c1 2 0 3 0 5 1 0 1-1 2-2v6h0c-1 1 0 2-1 3h0v1c-1-1 0-3 0-4l-1-1v4 3c-1-1 0-6 0-8-1-1 0-3 0-5h-1c0 3 0 5-1 8h0v3 1h0v-1-3c1-2 0-3 0-5 1-2 1-3 2-5z" class="v"></path><path d="M214 129h1c3 0 5 1 6 3 0 1 0 2 1 3v2l-3-3-3-3c-1 0-1 0-2-1v-1z" class="Q"></path><path d="M230 388h5c-2 1-5 1-7 4l-1 1s-1 1-2 1h0c-1-1-1-2 0-3 1-2 2-3 5-3z" class="N"></path><path d="M473 226c2 0 2 0 5-1v3 1c-1 0-2 0-2 1-1 0-2 1-3 1 0 0-1 0-1-1-1-1 0-3 1-4z" class="O"></path><path d="M500 172c2-1 4-1 6 0 1 1 1 2 2 2 0 1-1 1-2 2l-2-1-1 2-2-4h-1v-1z" class="M"></path><path d="M501 173h2 0l1 1s1 0 1 1h-1l-1 2-2-4z" class="C"></path><path d="M274 236v1c-1 1-1 1-2 1l1 1v1c-1 1-2 2-4 3l-1-1c1-2 1-3 3-4-1 0-1-1-2-1 0-1 0-1 1-2l4 1z" class="f"></path><path d="M217 485l2 1c-1 1-1 2-1 4h1v1c-1 1-1 2-1 3l-2 2v-4c-1-3-1-5 1-7z" class="l"></path><path d="M216 492h1c0-1 1-1 1-2l1 1c-1 1-1 2-1 3l-2 2v-4z" class="m"></path><path d="M266 115c1 1 1 2 2 4l-1 1v2c-1 2-2 2-2 4h-4l-1-1h1c1 0 2-1 3-1 1-2 1-3 1-5v-1h0l1-3z" class="Q"></path><path d="M266 115c1 1 1 2 2 4l-1 1c0 1-1 2-1 3h-1v-4h0v-1h0l1-3zm-15 387c-1-5-3-9-6-13 3 3 6 6 8 9v1 3h-2z" class="H"></path><path d="M245 477c0-2 0-3 2-4h1v1c0 4-1 7-3 10-1 2-3 3-4 5 0-2 3-5 4-7l1-1c0-1-1-4-1-4z" class="N"></path><path d="M276 304c1 3 3 8 1 12h1c-1 0-2 1-3 1 0-1 0-2 1-4l-1-1c-1-1-2-2-2-4 1 0 1 1 1 1h1 0 1v-2-3z" class="S"></path><path d="M257 443h1c1-1 1-4 2-6v7c0 3-2 6-3 9l-1-6h1v-2c1-1 0-1 0-1v-1z" class="I"></path><path d="M572 567v-1-5c1 2 1 4 2 6 2 3 4 6 6 8h-2c-3-1-4-4-5-6-1-1-1-1-1-2z" class="C"></path><path d="M318 634c3 0 6 1 9 2h15-3v1h-5c-7 0-15-1-22-2h5l1-1z" class="j"></path><path d="M224 460l1 1h1c1-1 1-1 1-2l1-1 1 1c-1 1-1 1-1 2 0 0 1 1 1 2h1c0 1-1 1-1 2l-1-1c-1 0-1 0-2-1v1h-1-2l-1-1v-1l2-2z" class="R"></path><path d="M191 113l-3 1-2-2-1-1 3-3 4 2v2l-1 1z" class="P"></path><path d="M256 447c-1-2-1-3-1-5l4-11v5c-1 2-1 5-2 7v1s1 0 0 1v2h-1z" class="B"></path><path d="M183 626c1-1 2-2 3-2s0 0 1 1l2 1c1 0 1 0 2 1 0 1 0 1-1 1h0c-1 0-2 1-2 1h0-1c-1-2-3-1-4-1h-1l1-2z" class="M"></path><path d="M183 626c1-1 2-2 3-2s0 0 1 1l2 1c-1 0-2 1-3 0h-1c-1 0-2 1-2 2h-1l1-2z" class="C"></path><path d="M513 156l3-2h0c1-2 1-3 1-5h1v3l-1 1 2 1c0 1 1 0 0 1-1 2-4 4-7 5 1-1 1-2 1-3h1l-1-1z" class="L"></path><path d="M216 595h1v-1-1h1l-1-1c0-1 0-1 1-2h0c2 0 3 1 5 0l1 1-2 2s-1 0-1 1v1l-1-1v1h0-1-1c-1 1-1 3-1 4v-2h-1v-1h-1l1-1z" class="N"></path><path d="M174 628v2 2h-1s-2 0-2-1c-1 0-2-2-2-3 2-1 4-4 6-4 0 2-1 3-1 4z" class="O"></path><path d="M541 216h3c0 1-1 2-1 3h-1c0 2-1 3-3 4l-2-1c1 0 0-1 0-2 1-2 2-3 4-4z" class="M"></path><path d="M541 216h3c0 1-1 2-1 3h-1-1l-1 1h0c0-1 0-2 1-2l1-1-1-1z" class="H"></path><path d="M432 414c-1-2 0-4-1-6h1v-2-1h1v2-1c1 1 1 1 0 2 1 0 1 0 1-1v-2l1 1v-1c0 2 1 4 0 7h0c0-1 0-2-1-3h0v3h1l-1 1v-1c-1 0 0 3-1 3l-1-1z" class="n"></path><path d="M233 454l3-2c1 1 3 1 4 1l-1 1h-2-2v3 2h1l-1 1c-1 0-1 1-1 2l-2-1c0-2 0-5 1-7z" class="I"></path><path d="M242 626c3 0 5-1 7-2 1-1 3-2 4-2v2c-1 3-6 3-9 4h-1l-1-2z" class="m"></path><path d="M221 526c2 1 3 2 4 4l-1 1 1 1v2h-1l-1 1v-1h-1c-1-1-1-2-1-3v-1l-1 1c0-2 0-3 1-5z" class="B"></path><path d="M224 534l-2-1v-3h2v1l1 1v2h-1z" class="P"></path><path d="M271 295c2 3 4 6 5 9v3 2h-1c0-1-1-2-1-3h0c0-1 0-1-1-1 0-2-1-4-2-5l-1-2 1-3z" class="B"></path><path d="M274 306v-1c1 0 1 1 2 2v2h-1c0-1-1-2-1-3z" class="F"></path><path d="M276 630c1 0 2-1 4 0h1l1 1s1 0 1-1h3l2 1c-1 0-2 0-2 1l1 1c-3 0-8 0-12-1 1-1 1-1 1-2z" class="h"></path><path d="M219 433c1 1 1 1 2 1 0-1 0-1 1 0h2c1 0 2 1 2 2h2v-1l1 1c-1 1-1 1-1 2-2 0-3-1-4-2h-1-1s-1 0-2 1c-1 0-2 1-4 1v-2h1c1-1 2-2 2-3z" class="l"></path><path d="M221 239c-2-2-3-4-4-7v-9c1 1 2 3 3 4l-1 1c0 2 1 3 1 5s0 4 1 6z" class="I"></path><path d="M233 531c1-1 0-1 1-2 0 0 1 0 1-1 0 0-1-1 0-1h1c1 0 1 0 2 1l1 1v1h-1-2v1l2-1v1c0 1 0 1-1 2h-1v1 1l-3-4z" class="P"></path><path d="M238 178v-1l1-1c0 1 2 2 2 3l-1 1 1 4v1c0 2 0 3 1 4l1 1v1l-1 1c-3-4-4-9-4-14z" class="N"></path><path d="M238 178v-1l1-1c0 1 2 2 2 3l-1 1 1 4-1-2c-1-2-1-3-2-4z" class="L"></path><path d="M508 214c-4-3-9-3-14-2-1 0-4 1-5 0 1-1 2-1 4-2 5-1 11 0 16 3l-1 1z" class="X"></path><path d="M222 561l4 6c2-3 5-7 9-9-3 4-7 7-9 10v2c-1 0-2-1-2-2-1 0-2-5-3-6l1-1z" class="C"></path><path d="M504 608s0-1 1-1c0 0 2 0 3-1 2 0 5-2 7-2h2v1c0 1 0 2-2 2-1 1-4 1-6 1h-5z" class="X"></path><path d="M220 534c-1 1-1 2-2 2-1-1-1-3-1-4 0-3 0-5 2-7 1 0 2 1 2 1-1 2-1 3-1 5 0 1-1 2 0 3h0zm35-175l1 1c3 3 9 5 13 6l2 3-12-5c-2-1-3-2-4-5zm-22 105h1v1c0 1 1 2 1 3l2 1v5c-3-1-4-1-5-3v-1c0-2 0-3 1-5v-1z" class="S"></path><path d="M233 464h1v1c0 1 1 2 1 3v1h-2v-4-1z" class="T"></path><path d="M422 568c1 0 1 0 2 1h0 0 0 1l1-1v1h1v1c1-1 1-1 2 0h-1c0 1-1 1-1 2-1 0-1 1-1 1l-1 1h1l1 1c0-1 0-1 1-2 0 0 1 0 1-1h2 1-1c0 1-1 2-2 2s-2 1-3 1l-2-2c-1 0-1 0-1-1l1-1h-1c-1 0-1 1-2 1v-1l2-2-1-1z" class="q"></path><path d="M229 176s1 0 2-1c1 0 2-2 2-3v-1-1c1-1 2-1 3-1v2 1c-1 0-2 1-2 3-1 1-2 2-2 3-1 1-2 1-4 2v-2h-1v-3l2 1z" class="N"></path><path d="M227 175l2 1h1 1v1h0c-1 0-2 1-3 1h-1v-3z" class="O"></path><path d="M460 617h0c0 2 2 3 3 5l4 7c-2-1-4-2-5-4-2-1-2-3-3-4v-1c0-1 0-2 1-3z" class="S"></path><path d="M459 620c1 0 2 0 2 1 1 1 1 2 1 2v1 1c-2-1-2-3-3-4v-1z" class="H"></path><path d="M181 632c1 0 2 0 3-1 2-1 4-1 6-2h1c-1 1-1 2-2 2-1 1-2 1-3 1-1 2-3 5-4 5s-3 1-3 0h-1v-1h3c0 1 1 0 2 0h0c-1-1-4-1-4-2s0-1-1-1l3-1z" class="F"></path><path d="M264 507l1-1h2l3 2 1 1-1 1h-2v2c-1 0-1 0-2-1h-1-2v-2h0l1-2z" class="l"></path><path d="M264 507c1 1 1 1 1 2s1 1 1 2h-1-2v-2h0l1-2z" class="I"></path><path d="M268 510c-1 0-2 0-2-1s0-1-1-2v-1h2l3 2 1 1-1 1h-2z" class="U"></path><path d="M516 196v-1-1h0 1 1v-1h1v1c0 3 2 7 2 10 0 1 0 1 1 1v2c-1 0-1 1-2 1v1h-1-1c0-1 1-1 1-1l1-1h0-1v-1h2v-1c-1-1-2-1-2-2v-1-1c-1 1-1 1-2 1v-1h2v-1c-1-2-2-3-3-4z" class="h"></path><path d="M241 184l-1-4 1-1c2 2 3 4 4 6v3 3l-2-2c-1-1-2-2-2-4v-1z" class="M"></path><path d="M245 185v3 3l-2-2c0-1 0-2-1-3l1-1 1 1 1-1z" class="H"></path><path d="M258 421l-1-1h1c5 5 8 10 10 16h-1v-1l-1 2c-1-6-5-10-8-16z" class="F"></path><path d="M152 105c4 0 9 0 12 2 1 1 1 2 1 3h-3c-4-1-6-3-10-5z" class="D"></path><path d="M210 150h2v1h0c-1 1 0 3 0 3l1 2c0 3-1 6-1 10-2-6-3-11-2-16z" class="T"></path><path d="M245 551c1 0 4-4 5-5 4-3 7-5 11-6l-3 4h0 0c-2 1-4 1-5 2-4 1-7 5-9 8 0-1 0-2 1-3z" class="G"></path><path d="M217 619c1 1 1 1 2 1h1 1l7 5c-2-1-8 0-10-2-1-1-2-1-2-3l1-1z" class="k"></path><path d="M505 503c1 0 1 1 1 2 1 1 0 2 0 3 1-1 1-1 1-2h0 1c1 1 1 1 0 2h0 0c0 1 0 2-1 3v1 3 1c-1 1-1 2-1 3v1l-1 1v1c0-2 1-3 0-5v1h0v-5-1-1h0v-2-1c1-1 1-2 0-4v-1z" class="e"></path><path d="M544 507c1 1 1 4 2 6v-2-3l1 5 1 1c-1 0-1 1 0 2h-1c0 1 0 1-1 2v-2c-1 0-1 0-1 1h-1v-2l-1-1c0-1-1-4 0-4h1v-1-2z" class="u"></path><path d="M546 508l1 5c0 1 0 2-1 3v-3-2-3zm-2 7l-1-1c0-1-1-4 0-4h1v-1 1c0 2 1 4 1 6h-1v-1z" class="e"></path><path d="M446 142l1-3c1 1 1 1 2 1 1 1 2 1 3 1h0l1 1v4c-1 0-2 0-3-1-2-1-3-1-4-3z" class="S"></path><path d="M678 585h1c1 0 3-2 4-2-2 3-6 4-9 6l-8 4c-2 0-3 1-5 1h0l6-3c1 0 2-1 3-1h0c1-1 1-1 1-2l-1 1h-1-1c-1 1-1 1-2 1l7-3c1-1 3-2 5-2z" class="e"></path><path d="M272 346h0c2 3 3 6 5 9 0 2 3 5 3 7-3-2-5-6-7-9-1-1-1-2-2-3 0-2 0-3 1-4z" class="J"></path><path d="M446 142v-4c2-4 4-5 7-7l-2 6c-1 2 0 3 1 4h0c-1 0-2 0-3-1-1 0-1 0-2-1l-1 3z" class="G"></path><path d="M253 248c-1-1-3-2-3-4-1-1-1-1-1-2h0v-3c1 0 2 0 3 1h0l-1 1h1v-1h2c1 1 1 2 0 3h-1 0c-1-1-2-1-2 0 2 0 2 0 4 2-1 1-2 2-2 3z" class="J"></path><path d="M283 511h0c2 0 2 0 3 1 0 2 0 2-1 3-1 2-3 3-6 4v-1c2-1 5-3 6-5h-1c-1 0-2 1-3 2l-6 5c2-4 5-7 8-9z" class="C"></path><path d="M494 502h1c0-2 0-4 2-5 0 2-2 4-1 5l1-1h1v3h0v2-1h1c0 1 0 2-1 3h-1c0-1 0-2-1-3h0v2h0c0 1 0 3-1 4 0 1 0 1-1 1h0v-4-2-2 1-1h1v-2h-1z" class="s"></path><path d="M372 292h2c1 1 2 2 2 3 1 1-1 3-1 4l-1 1c-1 0-1 1-2 0-1-2-1-6 0-8z" class="B"></path><path d="M704 492l2-1v1l1 6v3l-1-1v-2-2l-1 2h-1c0 2 0 4-1 6v-3c-1-3-1-7-1-10 1 1 1 2 2 2v-1zm-251-93c1 1 1 1 2 1h1l-2 7h0c-1 2-1 3-1 4-1 1-1 1 0 2-1 1-1 1-2 1h0v-6h0v-1c0-2 1-4 1-6l1-2z" class="q"></path><path d="M488 498l1-2v-2c0-1 1-2 1-3l1-1h0v2c-1 1-1 1-1 2v1l1-1v6h1v-1c0-1 1-1 1-2 0 1 0 2-1 3 0 1-1 2-1 4h0-1 1v-2h0v-1h-1v-5 2 2c-1 1 0 1 0 2l-1 1 1 1c-1 1-1 2-1 3s0 1-1 2c0-1 1-3 0-4v2-6-3h0z" class="e"></path><path d="M459 433c1-1 1-3 2-4v1c1 2 0 5 0 7h1l1-9v-3 3 4 3c-1 2-1 3-1 4h1v-2h0c0 2 1 3 0 5v-2h-1v1c0 1 0 2-1 3v-1 1h-1c0 1 1 0 0 1v1-3-2c0-1 0-4 1-4 0-1 0-2-1-2l1-1v-2c-1 1-1 0-1 1v1 2h0c0 1 0 3-1 3v-4-1-1z" class="n"></path><path d="M218 205h1l3 1h1c2 0 4 0 5 1h2 0 0c-2 1-4 0-5 2l1 2h-4-1l1-1-1-1c-1 0-1-1-2-2l-1-2z" class="F"></path><path d="M236 487l1-1c1 1 1 1 1 2v2l-1 1h1 1v1h-1c0 2 0 2-1 3s-1 1-2 1l-1-1c1-3 0-3-1-6l1-1h0 2v-1z" class="G"></path><path d="M236 488v2h-1c-1-1-1-1-1-2h0 2z" class="N"></path><path d="M236 196c1-2-1-3-1-4 2 0 2 1 3 2 1 2 2 5 2 7v1h-3c-1 0-1-1-1-2l-2-2c1-1 1-1 2-1v-1z" class="T"></path><path d="M194 626c1 0 2 0 3 1 2 0 3 0 3 2 0 1 0 2-1 3h-3c-1 0-2-1-3-2s-1-2-1-3l2-1z" class="I"></path><path d="M255 223c1 0 2-1 3-1s1 1 2-1v-1l-1 1h-1v-2c1-1 2-2 3-1h2c0 2-1 3-2 4l1 1c0 2 0 3-1 4h-1v1c-1-1-1-2-1-3-1-1-1-2-3-2h-1z" class="U"></path><path d="M215 148c0 1 1 1 1 2h1c0 1 0 1 1 2 1 0 1 4 0 5v1l-1 1c0-1-1-1-1-2-1-1-2-3-2-4v-2l-1-1 2-2z" class="H"></path><path d="M215 148c0 1 1 1 1 2-1 2 0 3 0 6v1c-1-1-2-3-2-4v-2l-1-1 2-2z" class="X"></path><path d="M246 242h2c0 1 1 3 2 4 0 1 1 1 1 2 1 1 1 1 1 2l-1 1c-1 0-2-1-3-1-2-2-4-5-4-8h1l1 1v-1z" class="M"></path><path d="M255 512c1 1 1 2 1 3 1 3 0 6 0 9 1-2 1-4 3-5v1c0 1-1 3-2 4h0c-1 1-2 3-3 4v-1c0-2 1-3 1-5h-1v1h-1c1-2 1-4 1-5h1 0l-1-6h1 0z" class="j"></path><path d="M488 579h9c-1 1-1 2-2 3h0v-1h-2c0 1 0 1 1 2h-5c-1-1-1-1-1-2h-2-1l1-1-1-1h1 2z" class="V"></path><path d="M486 579h2 1 3v1h-5l-1 1h-1l1-1-1-1h1z" class="W"></path><path d="M488 581h5c0 1 0 1 1 2h-5c-1-1-1-1-1-2z" class="k"></path><path d="M633 597c3 1 5 1 7 0h5l1-1h1c3-1 3 0 5 0-2 1-5 2-8 2-2 0-4 1-6 1h-2c-1 1-3 1-4 1 0-1 0-2-1-2 1-1 2-1 2-1z" class="Z"></path><path d="M278 391h1v1l1 4c0 1 1 2 1 3-1 2-2 3-3 5l-1-2h0c0-1-1-1-1-2h1v-3-1h0c0-2 1-4 1-5z" class="N"></path><path d="M278 391h1v1 4h-2 0c0-2 1-4 1-5z" class="H"></path><path d="M280 396c0 1 1 2 1 3-1 2-2 3-3 5l-1-2h0c1-1 1-2 1-3 1-1 1 0 2 0v-3z" class="I"></path><path d="M220 194c-1-1-1-4-2-5-1-2-1-5-1-7v-1c1 2 1 4 2 6 1 3 3 4 5 5h1c-1 0-2 0-3 1 1 1 3 4 5 5h0l1 2c-1 0-2-1-3-2 0-1-1-1-2-2v-1c-1-1-2-2-3-2v1z" class="L"></path><path d="M284 258h0c-1-3-3-6-5-7l1-1 2 2h1 0l-2-2v-1c1 0 3 1 3 2 1 2 1 5 1 7h1c0-3 0-6-1-8-1 0-1-1-2-2h0 1c2 2 3 5 3 7l-1 1v3 2l-2-3z" class="C"></path><path d="M230 535v3l1-1v3l1 1-3 2v1c-2-2-4-3-4-5l1-1v-2l2 1v-1-1h2z" class="M"></path><path d="M230 538l1-1v3l1 1-3 2v-3c1-1 0-1 1-2z" class="C"></path><path d="M228 549l1-1h1 6l-3 3h-1l-1 1h-2v-1l-1 1c-2 1-2 4-4 6v-1c-1-2 2-6 4-8z" class="H"></path><path d="M228 549l1-1h1l2 2v1c-2 0-3 0-4-1v-1z" class="K"></path><path d="M523 181c1-1 1-1 3-1s4 0 5 1v1c0 2-2 2-4 3l-2 1-2-2v-1h1v-1h-1-1l1-1zM226 512c2-1 3-2 4-2 2 0 2 1 3 2v1h0c-1 1-2 3-3 4l-1-1c0 1 0 1-1 1l-1-1h0c-1-1 0-2 0-3l-1-1z" class="L"></path><path d="M227 516c1-1 1-2 3-3 0 0 0 1 1 1v1h-1l-1 1c0 1 0 1-1 1l-1-1z" class="E"></path><path d="M391 631c2 0 4-1 6 0 1 0 3 1 4 1-1 0-1 1-1 2-1 0-1 1-1 1h-3c-1 0-2 0-3 1-1-2-1-2-2-3h1l-1-1v-1z" class="p"></path><path d="M392 633c2-1 4 0 6 1l1 1h-3c-1 0-2 0-3 1-1-2-1-2-2-3h1z" class="e"></path><path d="M231 191v-2h0l5 7v1c-1 0-1 0-2 1 0 0 0-1-2-1h0l-3-3v-1-1h0l1 1 1-2z" class="B"></path><path d="M231 191h1v4h-1c-1 0-1-1-1-1s-1 0-1-1v-1h0l1 1 1-2z" class="F"></path><path d="M393 636c1-1 2-1 3-1v1h4l1 2c-5 2-13-1-18-1 1-1 7-1 10-1z" class="h"></path><defs><linearGradient id="j" x1="276.046" y1="114.239" x2="282.513" y2="123.641" xlink:href="#B"><stop offset="0" stop-color="#aeafaf"></stop><stop offset="1" stop-color="#d7d4d5"></stop></linearGradient></defs><path fill="url(#j)" d="M279 123l-3-9c-1-1-1-3-1-5 1 3 2 5 5 7h0c0 1-1 1 0 2l3 3c1 1 0 2 0 4h-1v-1c-1 0-2-1-3-1z"></path><path d="M707 537l-1 5c0 1-1 2-1 4h1c0-1 1-2 2-3l-2 5c-2 1-2 2-3 3h0-1c-1 0-3 3-4 4h-1c2-1 3-3 4-4s1-3 1-5c1-1 2-2 2-4l3-5z" class="m"></path><path d="M270 598c4-5 7-10 14-12h0c0 2-1 2-2 3h-1c-3 2-6 6-9 9h0-2z" class="L"></path><path d="M517 222c1-1 1-1 2-1 3 4 6 12 6 17v5h-1c0-5-1-11-3-15-1-2-2-4-4-6z" class="C"></path><path d="M252 139c3 6 2 11 1 18-1-3-2-6-2-9-1-3 0-6 1-9z" class="B"></path><path d="M225 192l4 2 3 3-1 1v-1l-1 1 2 2-1 1-1-1h0l-1 1-1-1-1-2h0c-2-1-4-4-5-5 1-1 2-1 3-1z" class="K"></path><path d="M475 154l-2-4c-1 1-2 1-3 1l-2-2c-1-2-1-6 0-8 0 1 1 1 1 2h1v2h0c0 1 1 2 1 2 1 0 1 0 2 1 1 0 1 0 2-1h0l-1 1h0v1c0 1 2 1 2 2 0 0-1 0 0 1v1l-1 1z" class="N"></path><path d="M269 487l3-1v1c0 1-1 2-1 3l-1 1c-1 0-1 0-2 1-1 0-2 1-2 1-1 1 0 1-1 1 0 0-1 0-1 1l-1-1v-1h0v-2h1l1-2 1-1h1s1-1 2-1h0z" class="G"></path><path d="M401 632h10c-4 2-7 4-10 6l-1-2h-4v-1h3s0-1 1-1c0-1 0-2 1-2z" class="g"></path><path d="M241 250h0c2 0 2 0 3 1 0 1 0 1-1 2-2 0-4 1-6 2-2 0-5 2-7 3l-1 1c0-3 5-5 6-7l1-1 2 1c1-1 2-1 3-2z" class="H"></path><path d="M449 122c-4-2-8-4-13-5 5-1 12 0 16 2 0 1-1 1-1 2-1 1-1 1-2 1z" class="N"></path><path d="M405 119l-1-1h-1v-1c3-1 8 0 11 1h3 0c1 0 2 1 3 1l1 1h-1 0-1c-1-1-2-1-3-1h0l2 2h-1 0-2c0-1 0-1-1-1h-1-3v1-1c-1 0-3 1-4 0l-1-1z" class="l"></path><path d="M264 236v-2h1l3 3v4c0 1-1 2-1 3-1 0-1 0-1 1-1-1-2-1-2-2l-2-2 1-1v-1c0-1 1-2 1-3z" class="X"></path><path d="M263 240h1c1 0 1-1 1-1 1 0 1 1 1 1l1 2c-1 0-2 0-3 1l-2-2 1-1z" class="b"></path><path d="M264 236v-2h1l3 3-1 1h-2c-1 0-1-1-1-2z" class="J"></path><path d="M703 551h0c1-1 1-2 3-3-1 3-3 5-3 8s-4 8-6 9c1-1 0-1 0-2 1-1 1-2 2-4l-1-1 5-7z" class="M"></path><path d="M250 373c1-1 2-2 3-2h1c0 1 1 4 1 5-1 1 0 2-2 2h-1-1-2l1 1h-3l-1-1h1l2-2h0l1-1h-1c0-1 1-2 1-2z" class="l"></path><path d="M219 632l23-6 1 2h-1c-4 2-7 2-11 3l-10 2h0c0-1 1-1 1-1 1 0 2 0 3-1-1 0-1 0-2 1h-3-1z" class="Q"></path><path d="M444 614l1-1h0c1 1 1 1 2 1v2 1 1c1 1 2 2 4 2v1h-2c-2-1-4-2-6-4 0 0 0-1-1-1s-4 0-5 1c-1 0-2 0-2 1h-1c-1 0-2 0-3-1v-1c3 0 5-1 8-1h4l1-1z" class="I"></path><path d="M221 364h0c1 1 1 2 2 2 0 1 0 1-1 1l1 1h0v1 1 1 4c1 1 3 3 5 4h-1c-2-1-4-4-5-6l-1-1h-1c-1 1-3 1-3 0v-1l1 1 2-2v-1c-1-2-2-3-2-5h3 0z" class="p"></path><path d="M423 634c1 0 1 0 1 1l7 2c-2 0-7 0-9-1v-1l-1 1c-3 1-5 1-7 1-3 0-5 1-8 1 4-2 8-3 12-4l1-1c2 1 3 1 4 1z" class="k"></path><path d="M472 287c-1 1-1 2-2 3l2 2 2 1c2 0 3-1 4-2 0 0 0-1 1-1h0c0 2-1 3-2 4s-2 2-4 2c-1 0-2-1-3-2s-1-2-1-4 1-3 2-4c0 0 0 1 1 1z" class="H"></path><path d="M695 481c1-1 0-1 1-1h1s1-1 1-2c0 1 1 2 1 3h-1v-1 6c2 3 2 6 2 9h0c-2-1-2-5-2-7h-1v3 3l-2-10v-1-2z" class="u"></path><path d="M234 126l1-1h2 1l-1-1h1v1c1 0 2 1 3 1v-3h1v1c1 1 1 2 2 2h1c1 0 1-1 1-2h1c0 1 0 3 2 3v1c1 0 2 0 3-1h1l2 1h-1c-6 1-13 1-19-2h-1z" class="O"></path><path d="M361 622c0-1 0-1 1-1 1 1 1 1 1 2v1l-1 1-6 6h0c-1 1-1 1-2 1v-1h0c1-4 4-8 7-9z" class="R"></path><path d="M661 594c2 0 3-1 5-1v1 1l-7 1s-2 0-2 1c-3 0-6 2-9 3h-1c-1-1-2-1-3-1v-1c3 0 6-1 8-2 2 0 5-1 6-1 1-1 2-1 3-1z" class="G"></path><path d="M255 359c0-1 0-3 2-3 0-1 1-1 1-1 1 1 2 2 3 2 3 2 6 2 9 4-1 1-1 1-2 1l-1 1-8-3c0-1-1-2-2-3 0 1-1 2-1 3l-1-1z" class="M"></path><path d="M219 605l1-1c1-1 9-1 11 0 1 0 2 1 2 2-1 1-4 1-5 2h-1l-2-1c-1-1-4-2-6-2z" class="N"></path><path d="M262 118h1v-1-1c1 0 1 1 2 2v1c0 2 0 3-1 5-1 0-2 1-3 1h-1c0-2-3-3-3-5l1-1c2 0 3 0 4-1z" class="d"></path><path d="M384 104h1c1 1 2 1 3 2 0 1 1 2 0 2 0 2-2 4-3 4h-1c-1 0-3-2-3-3 1-1 2-4 3-5z" class="g"></path><path d="M583 579c2 1 3 1 4 2h1c1 0 1 0 2 1h0l2 1 2 2c1 1 1 1 2 1s2 1 3 1c1 1 1 1 2 1 1-1 1-1 2 0h1l3 3h-3c-2-1-2-1-4-1-1-1-2-2-4-3v1l-1 1-6-6c-1-1-2-2-3-2h-1l-2-2z" class="d"></path><path d="M231 608h1s1-1 2-1h0l1 1v1c-1 2-2 3-4 4h0c-2 1-4 2-6 2 1-1 1-1 1-2l1-1h-1l-1-1c2-1 3-2 4-3h2z" class="L"></path><path d="M231 608h1s1-1 2-1h0l1 1v1c-1 2-2 3-4 4h0v-3h-1l-1 1v2h-1v-2c1-1 1-1 3-1v-2zm149 17l6 1v1h0c-2 1-3 2-5 2 0 1 0 1-1 2h-2v1 1h-1l-1 1v-1h0c-1-1-1-2-1-3l2-2h0l2-1 1-2z" class="S"></path><path d="M379 627v2c-1 1-1 1-2 1 0 0-1 0-1 1v1h0c0 1 0 1 1 1h0l-1 1v-1h0c-1-1-1-2-1-3l2-2h0l2-1z" class="g"></path><path d="M473 312h1c1 2 2 4 4 4 3 1 8 0 10-1l1 1c0-1 1-1 2-1v1h0c-1 0-1 0-1 1h0l-4 1c-5 1-8 0-12-3-1-1-1-2-1-3z" class="R"></path><path d="M232 229c0-1 0-2 1-3 0-2 1-6 2-8 1 0 1-1 1-1 1-2 3-4 4-6 1 2-1 5-2 7h0v1l-5 11v-1h-1z" class="G"></path><path d="M231 527v-1h1c0-1 1-2 1-3 0 0-1 0-1-1 1-1 2-1 2-2h1c1 1 1 1 2 1v1c0 1 0 2 1 3v3c-1-1-1-1-2-1h-1c-1 0 0 1 0 1 0 1-1 1-1 1-1 1 0 1-1 2-1-1-1-3-2-4z" class="O"></path><defs><linearGradient id="k" x1="256.893" y1="361.82" x2="271.915" y2="363.084" xlink:href="#B"><stop offset="0" stop-color="#bdbbbb"></stop><stop offset="1" stop-color="#e5e3e4"></stop></linearGradient></defs><path fill="url(#k)" d="M256 360c0-1 1-2 1-3 1 1 2 2 2 3l8 3c2 1 4 2 6 4h-1l-3-1c-4-1-10-3-13-6z"></path><path d="M479 439v2l1-1c1 2 0 2 0 3 1 2 1 5 1 7l-1 4 1 1h0c-1 3-1 6-2 9v1h0c0-3 1-6 0-8h1v-1-1h-1v-2 1h0-1v-1h-1v2h0v-4h0l1 1v-3-1h0c1-1 1-8 1-9z" class="s"></path><path d="M479 441l1-1c1 2 0 2 0 3 0 2 0 3-1 5v-1-6z" class="i"></path><path d="M457 615h0c-1 1-3 1-4 1v-1c2-1 4-2 5-4 1-1 3-2 4-4 0 1 3 3 3 3 2-1 2-2 4-2v1c0 1-1 1-3 1h0c-1 2-1 2-3 2h-1c-1 1-2 1-2 1-1 1-2 2-3 2z" class="I"></path><path d="M223 572l1-1v1c-1 3-3 6-4 9s-1 5-1 8c-1 0-1 1-1 1h0c-1 1-1 1-1 2l1 1h-1v1 1h-1v-1c1-1 1-1 1-2-1-1-1-1-2-1v1c-1-1-3-1-3-3l2-2c1 0 2 1 3 2 2-6 3-11 6-17z" class="G"></path><path d="M283 406c0 1 0 2 1 3h0c-1 6-3 12-5 17h4c-1 1-1 2-2 3h-1-3l-1-1c0-1 1-2 2-3l2-6c1-5 2-9 3-13z" class="N"></path><path d="M452 119c4 2 8 5 9 9h-1c-3 1-8-4-11-6 1 0 1 0 2-1 0-1 1-1 1-2z" class="G"></path><path d="M517 209c-2-2-4-4-4-7 0 0 0-1 1-1h3v1c1 0 1 0 2-1v1 1c0 1 1 1 2 2v1h-2v1h1 0l-1 1s-1 0-1 1h1c1 1 1 1 1 2l-1 1-1-1c0-1 0-1-1-2z" class="T"></path><path d="M239 515h0l-1 1s-1 0-2-1c0 0-1-1-1-2v-2-2c1-1 2-2 3-2 3 0 4 2 6 4l-1 1-1-1h-2s0 1-1 1l1 1c1 0 1 0 1 1-1 0-1 0-2 1z" class="F"></path><path d="M524 523l1 1v2 4h1v-3-1c1 0 2 1 3 2v4h0c0-1-1-1-1-2v4h-1v-2l-1-1v5 1h0-1v-2-1c-1-2 0-5 0-7l-1 6h0c-1 1-1 1 0 2h-1-1v-2c0-1-1-2 0-3h0c0-1 0-1 1-1v2h0v-1-1h1l-1-5 1-1z" class="u"></path><path d="M229 158c3 0 6 0 8 2 1 1 3 3 5 4h-4 0 0l-5-1c-1 1-2 0-3 0 0-1 0-2-1-3v-2z" class="B"></path><path d="M237 160c1 1 3 3 5 4h-4 0 0l-5-1h4v-1l-2-1c-1 0-1 1-2 0h-1l1-1 3 1 1-1z" class="T"></path><path d="M254 527v1c1-1 2-3 3-4h0 1l1 1c-4 4-5 10-7 14-1 2-2 3-3 5l-1-1c2-2 5-12 6-16z" class="a"></path><path d="M222 389h2 0c-2 2-3 7-5 10-1 2-2 3-3 5l-1 1v-1l1-5c1-4 2-8 6-10z" class="T"></path><defs><linearGradient id="l" x1="249.632" y1="465.83" x2="257.654" y2="452.456" xlink:href="#B"><stop offset="0" stop-color="#878787"></stop><stop offset="1" stop-color="#a0a1a2"></stop></linearGradient></defs><path fill="url(#l)" d="M252 448c5 6 6 14 5 22v1h-1c0-3 0-5-1-7v-3c-1-5-2-8-5-12l2-1z"></path><path d="M235 457v-3h2c1 2 2 3 4 5l1 1h0c1 1 2 2 2 3 1 0 1 0 2 1-1 0-1 1-2 1h0v-2h0l-2 2h0c-1-1 0-1 0-2-1 0-1 0-1 1l-1 1c-1-1-1-1-1-2l1-1h-1l-1 1v-1h0c-1-2-2-3-3-5z" class="C"></path><path d="M508 214h2c-1 3-3 6-5 8-5 3-10 3-15 3 2-1 4-1 6-2 6-1 9-5 12-9z" class="Q"></path><path d="M273 610h3l-1 2c-1 1-2 1-2 2v-1c-1 1-1 1-2 1-4 3-9 6-14 7h0l1-4 1 1-1 1h1l8-5c2-1 4-2 6-4z" class="a"></path><path d="M229 477c4 1 5 4 7 7 1 1 0 2 0 3v1h-2 0-2l-3-5v-3c1-1 1-1 1-2l-1-1z" class="R"></path><path d="M238 164h4c4 4 7 9 7 15v1 7h-1v-6-3c-1-4-3-9-6-11-2-2-5-1-7-1l-1-1 1-1h3z" class="D"></path><path d="M232 197c2 0 2 1 2 1l2 2c0 1 0 2 1 2 0 1 1 2 1 3l-1 1c0 1 1 2 0 2l-1-1-3-3h-2c-1-1-1-2-2-3h0 0l1-1h0l1 1 1-1-2-2 1-1v1l1-1h0z" class="F"></path><path d="M232 197h0l2 2-2 1h0l2 2v1h-1c-1-1-2-2-3-2h-1 0 0l1-1h0l1 1 1-1-2-2 1-1v1l1-1z" class="P"></path><path d="M232 197c2 0 2 1 2 1l2 2c0 1 0 2 1 2 0 1 1 2 1 3l-1 1c0 1 1 2 0 2l-1-1h0c0-1-1-5-1-6-1 0-1-1-1-2l-2-2z" class="g"></path><path d="M192 110v-3h3 0 0c-1 2-2 3-1 4 0 1 0 2 2 2 1 1 2 2 4 2l3-1h1c-2 1-3 1-5 2-1 1-2 2-2 3l-3-5h-1 0v1c-1 1-2 1-4 1-1 0-2 0-3-1s-1-1-1-2l1 1c1 1 2 1 3 1s1-1 2-1v-1l1-1v-2z" class="S"></path><path d="M357 104c3 1 6 3 9 5v-1c1-1 2-3 3-4 2-1 4-1 5 0s2 1 2 2v1c-2-1-5-1-7-1v3h0c0 2 0 2-1 2-2-1-5-1-7-1l-1-1 2-2h-1c-2-1-3-1-5-2 0 0 1 0 1-1z" class="B"></path><path d="M222 540c2 0 3 0 3 1 1 1 3 4 2 5 0 2-1 2-2 3-2 0-2 0-3 1h-1v-4-3l1-1v-2z" class="E"></path><path d="M220 502c1 0 1 1 2 0h3c0 2 0 2 1 4h0 1 1l-1 1c-1 1-2 2-4 3 0 1-1 2-1 4 0 1-1 2-1 3h1v1c-1 1-1 1-1 2 1 1 0 2 0 4-1-1-1-2-2-3v-1c0-3 1-9 3-11 1-1 1-1 1-2v-1h1v1 1c1-1 1-1 1-2l-1-1h-1-2l-1-1v-2z" class="h"></path><path d="M241 453c2 0 5-1 7 0 1 0 2 1 2 2 1 1 1 2 0 3h-1v-1c-1 1 0 2-2 2l-1-1-1 1-2-2h-1l-1-1-2-2 1-1h1z" class="H"></path><path d="M245 453h1v1c0 1 1 1 1 2-1 1-1 1-2 1v-4z" class="C"></path><path d="M220 233c0-2-1-3-1-5l1-1c3 5 5 11 5 16v3h0c-1-2-3-4-4-7-1-2-1-4-1-6z" class="B"></path><path d="M220 233l1 3c1 2 3 4 3 7h1v3h0c-1-2-3-4-4-7-1-2-1-4-1-6z" class="G"></path><path d="M554 442c1 0 1 1 1 1v1c1 1 1 2 1 3v3h1v3l1 1c0 1 1 2 1 3h0 0c0 1 0 2 1 3v2c1 0 0 1 1 2 0 3 2 6 2 9h0v1h0c1 1 1 2 1 3l1 1v1 1c0 1 0 0 1 1v1 1c1 1 1 1 1 2v2h0v3-1 1l-1-2c-2-6-3-13-6-19v-1h0l1 2h0v1-2-2-1l-1-2v-1-1c-1-1-1-3-2-4v-1-2c-1-1-2-2-2-4h0c-1-1-1-3-1-4v-1c0-1 0-2-1-4z" class="v"></path><path d="M476 578h8 2v1h-1l1 1-1 1h1 2c0 1 0 1 1 2-2 0-5-1-7 0h-2c-2 1-4 1-6 1 1-1 1 0 1-1h2v-1h-1c0-1 1-1 1-2s-1-2-1-2z" class="c"></path><path d="M484 578h2v1h-1l1 1-1 1h-3-1v-1h3v-2z" class="r"></path><path d="M477 582h1c0-1 1-1 2-1h1 1 3 1 2c0 1 0 1 1 2-2 0-5-1-7 0h-2c-2 1-4 1-6 1 1-1 1 0 1-1h2v-1z" class="t"></path><path d="M486 581h2c0 1 0 1 1 2-2 0-5-1-7 0h-2 0c1-2 4-1 6-1v-1z" class="c"></path><path d="M510 291c1 1 1 0 2 1v1h0c-1 2-2 5-3 7-3 7-10 15-18 17h-1 0c0-1 0-1 1-1h0l6-4c8-4 11-12 13-21z" class="H"></path><path d="M711 509c0 5 1 10 0 15-1 1-1 3-2 4v-1h0l-2 5c0-3 1-6 1-9v-13 1l1-1c0 2 0 5 1 7 1-1 1-6 1-8z" class="S"></path><path d="M481 611v1c1-1 0-2 0-4l-2-1 1-1c0-1 0-1 1 0 2 1 3 3 3 4 0 2-1 3-1 4 1 0 2-3 2-4s-1-2-2-4l1-1v1c1 1 2 3 3 3h2v1h0c-1 0-1 0-2 1h0c-1 2-1 3-3 4h0c-1 0-2 2-3 2-1-2-1-2-1-4l-1-1 2-1z" class="L"></path><path d="M258 524c3-3 7-4 11-3 5 1 8 3 10 7 3 3 4 8 3 12 0 1 0 1-1 2v-2c1-5-1-10-5-14-1-1-2-2-3-2h-1-1v1-2c-1 0-1-1-2-1-4 0-7 0-10 3l-1-1z" class="G"></path><path d="M474 210c-1 2-3 4-3 7h0c-1 1-1 1-1 2s0 1-1 1v1h2l1-1c1-1 2-1 3-2 0 2 1 2 1 3l-1 1-3 3v1l-1 1h-1 0c-2-4-1-9 0-12 0-3 2-4 4-5z" class="I"></path><path d="M475 218c0 2 1 2 1 3l-1 1-3 3v1h-1v-1c-1-1 0-2 0-4 1 0 1 0 1-1h0c1-1 2-1 3-2z" class="F"></path><defs><linearGradient id="m" x1="256.268" y1="213.688" x2="266.467" y2="205.37" xlink:href="#B"><stop offset="0" stop-color="#787879"></stop><stop offset="1" stop-color="#969495"></stop></linearGradient></defs><path fill="url(#m)" d="M247 218c3-3 6-4 10-6 3-2 7-7 10-8l1 1c-1 0-1 1-1 1-2 3-4 4-6 6-1 1-4 3-5 3h0c-3 0-7 3-9 4v-1z"></path><path d="M520 181c0-1-1-3 0-5s3-4 5-5c2 0 2 1 3 1 0 1 0 2-1 3-1 2-2 4-5 5h0l-2 1z" class="L"></path><path d="M224 340h2c1 2 3 2 5 2 1 1 2 0 3 0h2 0v4c-2 1-4 0-5 1-1 0-1 0-1-1h0v-1c-2 0-3 0-4-1h-1-1c0-1-1-2-1-3h-1c1-1 1-1 2-1z" class="E"></path><path d="M529 532v1h1c0-1 1-2 0-3h3c1 2 1 5 2 8 0 0 0 3-1 3v2 1h-1v-2h0c0-1 0-1-1-2s-1-1-3-2v-6h-1v1 3-2-4c0 1 1 1 1 2h0z" class="d"></path><path d="M457 418c0-1 0-2 1-2 0-1 0-3 1-5v-3h1c1 1-1 4-1 5v1l-2 10 1 1h1l-1 1-1 5-1 6v-1c0-2 0-4-1-5h0 0c-1 2 0 5-1 6v3c-1 1 0 3-1 4v-5c1-3 0-6 1-9 1-1 1-2 1-3h-1c1-1 0-1 1-2h1v-2h1v-1h0v-1c0-1 1-2 1-3h-1z" class="n"></path><path d="M457 431h0c-1-1 0-5 0-7l1 1h1l-1 1-1 5z" class="e"></path><path d="M227 451l2-2 1 1c0 1-1 2-1 3h0c-2 2-3 4-5 5l-1 1c-1 0-2 0-3-1s-1-2-1-3c1-2 5-2 7-4h1 0z" class="R"></path><path d="M227 451l2-2 1 1c0 1-1 2-1 3h0c-2 2-3 4-5 5h-1v-1c1-2 2-4 4-4v-2z" class="H"></path><path d="M223 572c-3-2-3-8-5-11 0-2-1-4-2-6-1-3-1-5-2-7l8 13-1 1c1 1 2 6 3 6 0 1 1 2 2 2v1h0l-1-1c0 1 0 1-1 2v-1l-1 1z" class="h"></path><defs><linearGradient id="n" x1="411.327" y1="124.382" x2="413.999" y2="120.201" xlink:href="#B"><stop offset="0" stop-color="#767676"></stop><stop offset="1" stop-color="#8f8e8e"></stop></linearGradient></defs><path fill="url(#n)" d="M421 127c-3 0-8-1-11-3h0-1c-2-1-4-4-4-5l1 1c1 1 3 0 4 0v1-1h3 1c1 0 1 0 1 1h2 0v1c1 0 2 0 2 1-1 0-1 0-2-1h-1v1c1 0 1 1 2 1v1c-1-1-2-1-3-1h-1c1 0 3 1 4 1l-1 1h0 1 2l1 1z"></path><path d="M260 125l1 1c4 7 7 13 7 21 0 2 0 4-1 6 0 1 0 2-1 4l-1-1 1-3c2-9-2-20-7-27l1-1z" class="K"></path><path d="M494 496c0-2 1-3 1-4h1c1 1 1 2 1 3l1 1 1-1v1 3l1 1c0 1-1 3 0 4 1-1 1 0 1-1s0-2 1-2h0v3s0 1 1 1c1-1 1-1 1-2v1h0v1c0 1 0 1-1 1l-2-2v1l-1-1s-1-1-1-2v2l-1 1v-1h0v-3h-1l-1 1c-1-1 1-3 1-5-2 1-2 3-2 5h-1 0v1c0 1 0 1-1 1v1h0c0-1 1-3 1-4v-1-2-1-1h0z" class="e"></path><path d="M226 211l-1-2c1-2 3-1 5-2h0l3 3h-1c1 1 2 2 2 3-1 1-1 1-2 1l-1 1c-2 2-4 2-6 3l1-2c-1 0-1 0-1-1l1-2h3 1l1-1h-3c-1-1-1-1-2-1z" class="I"></path><path d="M229 213h1c0 1-3 2-4 3-1 0-1 0-1-1l1-2h3z" class="F"></path><path d="M479 223h1c0-1 1-2 2-2v2c1-1 2-2 2-3h1c0 1 0 1 1 2h1c-1 4-3 7-7 10 0-3-1-6-1-9z" class="B"></path><path d="M706 492h2l2 11 1 6c0 2 0 7-1 8-1-2-1-5-1-7l-1 1v-1l-1-9v-3l-1-6z" class="l"></path><path d="M707 498v-1c1 0 1 2 1 3 1 3 1 7 1 10l-1 1v-1l-1-9v-3z" class="L"></path><path d="M515 519h0c1-1 1-2 1-3v-1l1 1c0 1-1 3-1 5h1c0 3-1 5-1 7h1v-2c1 1 1 2 1 3h1v-1h1v1h2c0 1 0 1-1 1l-1 1-1-1h0 0-1c0-1 0-1-1-2h0v1 1 1l-1 1h0c0 1 0 1-1 1v-2c0-1 0-1-1-1h0c-1-1-1-2 0-3l-1 1-1 1v-1-1c0-2 0-3-1-4v-1l1 1v5l2-8c0-1 1 0 1-1z" class="d"></path><path d="M515 519h0c1-1 1-2 1-3v-1l1 1c0 1-1 3-1 5l-1 6v2h-1l1-10z" class="e"></path><path d="M242 539c0 1-1 2-2 3l-1 2c-1 1-3 2-4 2-2 0-2 1-3 1h-2c-1-1-1-1-1-2l3-3h1l1-1c1-1 2-1 3-1s1 0 2-1c1 0 1 0 2 1l1-1z" class="C"></path><path d="M233 542c-1 1-1 1 0 2h0c-1 0-2 0-2 1h1 1 0c1-1 1-2 2-2h0c0 2-1 2 0 3-2 0-2 1-3 1h-2c-1-1-1-1-1-2l3-3h1z" class="B"></path><path d="M524 259h1c1-1 4-3 5-4s2-1 3-1h1s1 1 1 2c-1 2-1 4-3 5v1l-2 1h0-1c-1 0-3-2-3-3l-1 1s0-1-1-2z" class="T"></path><path d="M683 539l4-8c0 1 0 1-1 2v2 3s-1 0-1 1v1c-1 1 0-1-1 1h0v1c-1 0-1 1-1 1l-1 1c0 1-1 2-1 2l-1 1h0v1l-2 2-1 1-1 1c1 0 2-1 3-2h0l1-1v1c1-1 1-2 2-3v-1h1v-1 2c-1 1-1 2-2 3l-2 2h2 0l4-4c1-1 1-3 2-3 0 1-1 2-2 3v1c-2 1-3 3-4 4s-2 2-3 2h-2c-1-1 0-1 0-2v-1c3-4 7-9 7-13z" class="e"></path><path d="M676 553s1-1 2-1v1h3c-1 1-2 2-3 2h-2c-1-1 0-1 0-2z" class="r"></path><path d="M277 488h0c3-1 5-2 8-4-1 1-1 4-2 4l-1 2c-2 0-2 1-3 1h0l-1 1s-1 0-2 1l-1 1h-1c-1 0-1 1-2 1h-2v-2l-1 1-1-1 2-2 1-1 1 1 2-2v1h1l2-2z" class="I"></path><path d="M270 493c1-1 6-3 7-2 1 0 2-1 3-2l2-1h1l-1 2c-2 0-2 1-3 1h0l-1 1s-1 0-2 1l-1 1h-1c-1 0-1 1-2 1h-2v-2z" class="B"></path><path d="M283 217c-5-2-2-9-4-12l-1-1 1-1c2 3 5 4 7 8h-1l1 1-1 2c1 0 0 0 1 1l1 1c-1 0-2 0-3 1h-1z" class="S"></path><path d="M285 214h-1l-3-3v-3-1c1 1 2 2 3 2-1 1-1 2-1 3h0l1-1h1l1 1-1 2z" class="d"></path><path d="M496 131c2 1 3 1 4 2v1h-1 0c1 1 2 0 3 1l-1 3v2c-1-1-2-1-3 0-2-1-3-1-4-1l-1-1c-1-1-1-2-1-3l2-1v1h1l-1-1c1-2 1-2 2-3z" class="U"></path><path d="M494 139c-1-1 0-2 0-3h2v1 1h0l1-1h2 0c1 1 1 1 2 1h0v2c-1-1-2-1-3 0-2-1-3-1-4-1z" class="K"></path><path d="M226 140c1 1 3 2 4 3 2 2 7 3 9 5 4 2 7 5 10 8l1 1v2c-2-2-4-5-7-7-5-4-11-6-18-8 1 0 1-1 1-2h0v-1h-1 1v-1h0z" class="L"></path><path d="M405 293c0 1-1 1-1 2h-2c1-1 1-1 1-2v-1h0v-1c0-2 0-4 1-6v-1h0v-2c1-1 1-2 1-3l1 1-1 1 1 1s0 1 1 1l-2 1v1s0 1 1 1l1-1h0v2h0 1v-1s0-1 1-1v1h1l2-4v4l-1 2v1 1h-3l-1 1h-1c-1 1-1 1-1 2z" class="s"></path><path d="M405 285s0 1 1 1v1l-1 1h-1c0-1 1-2 1-3z" class="V"></path><path d="M229 516l1 1c-1 2-1 4-2 5v1h-2v3c0 1-1 2-2 2v-1c0-1 1-1 1-2h0-2c-1 0-1-1-2-1 0-2 1-3 0-4 0-1 0-1 1-2h2v-1l2 1 1-2h0l1 1c1 0 1 0 1-1z" class="D"></path><path d="M222 518h2v-1l2 1 2 2-1 1v-1l-1-1-1 1h-3-1c0-1 0-1 1-2z" class="U"></path><path d="M221 520h1 3s-1 2 0 2v3h0-2c-1 0-1-1-2-1 0-2 1-3 0-4z" class="F"></path><path d="M219 632h1 3c1-1 1-1 2-1-1 1-2 1-3 1 0 0-1 0-1 1h0c-3 1-6 2-10 2-2 1-4 1-6 2-7 1-12 1-19 0h0 10c5-1 9-2 13-3h0c3-1 7-2 10-2z" class="p"></path><path d="M219 632h1 3c1-1 1-1 2-1-1 1-2 1-3 1 0 0-1 0-1 1h0c-3 1-6 2-10 2h-1-1-2c0 1-1 1-2 1h-1c-1 0-2 1-3 0 0 1-1 1-2 1l3-1c3-1 5 0 7-2h0 0c3-1 7-2 10-2z" class="g"></path><path d="M214 116c-1-2-2-4-2-7l2-2c2 0 3 0 5 1 1 1 2 2 3 4 0 1 0 1-1 2s-2 1-3 1l-2-2c1-1 0-2 0-3-1 1-1 2-1 3v1l-1 1v1z" class="C"></path><path d="M163 631c2 0 4 1 6 2 1 0 3 0 4 1v1l1-1h1 2v1c-3 1-8 4-11 4l-1-1c2-1 4-1 5-2 1 0 2-1 4-2-3 0-7 3-9 2-1-1-1-1-2-1h-2c-2-1-6-2-7-3 2-1 5 1 7 2h1v-1c0-1 0-1 1-2z" class="m"></path><path d="M273 266c2 0 4-1 6-1 0 0 1-1 1 0 2 0 4 3 4 5 1 2 1 5 0 6-1 2-3 5-6 6h-1l4-4c1-1 2-3 2-5-2-4-6-5-9-6l-1-1z" class="R"></path><path d="M215 592v-1c1 0 1 0 2 1 0 1 0 1-1 2v1l-1 1h1v1h1v2l-1 1 1 1-1 2h-4c1-2 3-3 3-6h-1l-2 1c-1 1-2 2-4 2v-1h-2v-1c2-2 3-3 6-4 1-1 2-1 3-1v-1z" class="S"></path><path d="M208 599h0v-1h4c-1 1-2 2-4 2v-1z" class="M"></path><path d="M255 464c1 2 1 4 1 7h1v2 3h0 0c0 2-1 2 0 4-1 4-1 8-2 12 0 3 0 6-1 9l-1-2v-1c1-1 0-5 1-6 0-10 1-19 1-28z" class="j"></path><path d="M256 471h1v2h-1 0v-2z" class="l"></path><path d="M271 300c1 1 2 3 2 5 1 0 1 0 1 1h0c0 1 1 2 1 3h0-1s0-1-1-1c0 2 1 3 2 4l1 1h-1l-1 1c-1 1-1 2-1 3l-2-6-2-3c-1-3-2-4-4-6v-1l3 2s1-1 1-2l1 1v1l1 1v-4z" class="I"></path><path d="M265 301l3 2v1h1 0c0 2 2 4 3 6 1 1 1 3 2 4-1 1-1 2-1 3l-2-6-2-3c-1-3-2-4-4-6v-1z" class="e"></path><path d="M209 614c-1 0-2 1-3 2s-1 3-1 4c1 1 1 2 3 3 1 1 2 0 4 0h0c0 1-1 1-1 2-2 0-3 0-5-1-1-1-2-3-2-5-1-1 0-4 1-5s2-1 3-2h1 0v-1-1-1c1 0 2 0 3 1 0 1-1 1-1 2h3v-1l-1-1h1c1 1 3 1 4 2l1 1-1 1h-2-1c-1 1-1 0-2 0h-3-1z" class="Q"></path><path d="M266 221l1-1c2 0 3 0 4 1s1 2 2 3l-1 1h1c2 1 3 3 3 6 1 1 1 3 0 4l-1 1c-1-2-2-3-3-5h-1v-1c0-1 0-1 1-1v-1h-2c-1-2-1-2 0-4l-2-2-1 1c0-1 0-2-1-2z" class="O"></path><path d="M272 231h-1v-1c0-1 0-1 1-1l3 1v1h-3z" class="E"></path><path d="M223 429c1 0 3 1 5 2 1 0 2 1 3 1 4 2 9 5 12 8h-2c-1-2-4-3-6-4-1-1-4-3-6-2v2l-1-1v1h-2c0-1-1-2-2-2h-2c-1-1-1-1-1 0-1 0-1 0-2-1 1 0 2 0 3-2l-1-1c1 0 1-1 2-1z" class="j"></path><path d="M228 436c-1-1-1-2-2-2s-2-1-2-1l-1-1 1-1c1 1 2 1 2 2h2l1 1-1 1v1z" class="G"></path><path d="M266 437l1-2v1h1c1 3 1 6 2 9l-1 1 1 1-8 20-1-1c2-6 5-12 6-18 0-3-1-7-1-11z" class="B"></path><path d="M434 628c3 0 8 1 11-1 1-1-1 0 1-1h0l1 1c-1 1-1 1-2 1 0 1-1 1-2 1-3 0-4 2-6 3s-6 1-8 1h-4l5-3v-1h-2l1-1h1 1 3z" class="d"></path><defs><linearGradient id="o" x1="296.22" y1="521.017" x2="285.361" y2="526.589" xlink:href="#B"><stop offset="0" stop-color="#bab6b7"></stop><stop offset="1" stop-color="#e2e4e5"></stop></linearGradient></defs><path fill="url(#o)" d="M290 509h1l1 2v7 14c0 1 1 4 0 6l-1 1-1 1v-31z"></path><defs><linearGradient id="p" x1="247.895" y1="368.377" x2="242.691" y2="352.445" xlink:href="#B"><stop offset="0" stop-color="#4b4b4c"></stop><stop offset="1" stop-color="#7a7a7b"></stop></linearGradient></defs><path fill="url(#p)" d="M251 348v3-1c1-1 2-1 3-2-3 4-6 8-8 12s-3 7-4 11c-1 2-1 3-3 4 0-2 1-3 1-5 2-8 6-15 11-22z"></path><path d="M286 604v-1c3-1 4-5 5-8 0 5 0 9-3 13l-4 3h-1c0-1 0-2-1-3h-3l2-4h5z" class="F"></path><path d="M281 604h5c-1 1-3 2-4 4h-3l2-4z" class="S"></path><path d="M410 387v-3-2l1-1v-3l-1 1v1h0v1 1h0l-1 1h0v1 1h-1c-1-1 0-5 0-7-1-1-1-2-1-3s0-2 1-2 1 0 1 1h1 0v-1-5c1 0 1-1 1-2v-1s0-1 1-2c-1 2-1 4-1 6-1 2-1 2 0 4h0l2-10c0 1 0 2 1 3v1l-4 19v1z" class="v"></path><path d="M409 376h1c1 1 0 2 0 3l-2 4c0-1 0-3 1-4v-1-2z" class="u"></path><path d="M520 181l2-1 1 1-1 1h1 1v1h-1v1l2 2h-1c0 1 0 1 1 1-1 1-4 1-5 2h-1v-1c-1 1-2 1-4 1h-1l-1-1c0-1 1-2 1-3h1l2-1h0 1c1-1 1-1 1-2l1-1z" class="N"></path><path d="M518 184c1 0 1-1 2 0h1l1 1c-1 1-1 1-1 2v1c-1 0-1 0-2 1v-1-3l-1 1v-2h-1 0 1z" class="B"></path><path d="M517 184h1v2l1-1v3c-1 1-2 1-4 1h-1l-1-1c0-1 1-2 1-3h1l2-1z" class="T"></path><path d="M517 184h1v2l-1 1c-1 0-1 0-2-1v-1l2-1z" class="U"></path><path d="M356 105c-3 0-7-2-10-2-1 0-2 1-2 2-1 0-1 1-1 2h0l2-2c1 0 3-1 4 0 1 0 2 0 2 1l-1 1s-1 1-2 1c-1 1-4 1-6 1v-1c-1-1 0-3 1-4v-1h-1l-1 1c-1 1-1 2-1 3h0v1c-1-1-1-1-1-2-1-1-1-1 0-2s2-2 3-2h1c5-2 9 1 14 2 0 1-1 1-1 1z" class="b"></path><path d="M451 621c2-1 3-1 5-2-1 1-5 3-5 5 0 1 0 1 1 1 2-1 3-2 4-3l3-2v1c-1 2-3 3-5 4l-1 1v2c1 2 1 3 1 5l-3-6c0-1-1-2-1-3l-4 2h0c-2 1 0 0-1 1-3 2-8 1-11 1h0 0v-2h-1c1-1 1-1 2-1v1l1-1v-1h-1l1-1c0 1 1 2 2 2 1 2 3 2 4 1h1 3l5-5z" class="G"></path><path d="M686 466h1l3 3c0 1 1 1 1 2v1h1c0-1-1-3-1-5l4 6h0c0 2 1 2 2 4l1 1c0 1-1 2-1 2h-1c-1 0 0 0-1 1v-2l-1 1-1-1-1 1c0-1-1-2-1-4-1 0 0 0-1-1 0-1 0-2-1-3l-3-6z" class="t"></path><path d="M691 467l4 6h0c0 2 1 2 2 4l1 1c0 1-1 2-1 2h-1c-1 0 0 0-1 1v-2l-1 1-1-1 1-2c-1-2-1-3-2-5 0-1-1-3-1-5z" class="n"></path><path d="M695 473c0 2 1 2 2 4l1 1c0 1-1 2-1 2h-1c-1 0 0 0-1 1v-2l-1 1c0-1 0-1 1-2h0v-5z" class="V"></path><path d="M491 230h5 0l6 3s1 0 1 1 1 2 1 3h-4-1-1v-1h-2c-1-1-2-2-3-2-2-1-4-1-5-2 1-1 2-1 3-2z" class="b"></path><path d="M488 232c1-1 2-1 3-2l1 2c1 0 1-1 2 0 0 0-1 1-1 2-2-1-4-1-5-2zm14 1s1 0 1 1 1 2 1 3h-4l1-1 1-1c-1 0-2 0-3-1 1-1 2 0 3-1z" class="E"></path><path d="M475 195c-3-3-7-7-9-10-3-4-2-9-1-14 1 4 1 9 3 13 2 2 6 4 7 7v3 1z" class="S"></path><path d="M326 617h4c2 0 3 1 4 2s1 3 1 4c-1 1-1 2-2 3-2-2-3-6-5-7 0-1-1-1-2-1-2 1-3 2-5 4 0 1-1 2-1 3 1 0 1 0 2 1h2l3-1h0c-2 1-3 2-4 2h-1c-1 0-1 0-2 1 0-1 0-1-1-2-1 0-1 2-2 3-1 0-3 1-4 2 1-2 3-2 3-4 2-4 6-8 10-10z" class="N"></path><path d="M512 234l1 1c1 1 1 2 1 2l1 1c3 1 3 4 4 6l-1 2h1v5h-1l-2-2c0-1-1-1-1-3h0v-2h1v-2l-1 1h-1c0-1-1-1-2-2v-2c-1 0-1 0-2-1s-1-1-1-3h0c1 0 2 0 3-1z" class="P"></path><path d="M485 211c1 1 3 2 3 4v3h-1l1 1-1 3h-1c-1-1-1-1-1-2h-1c0 1-1 2-2 3v-2c-1 0-2 1-2 2h-1 0c0-1-1-1-1-2 2-1 4-2 5-4v-2c-1-1-2-1-2-2l1-1c1 0 2-1 3-1z" class="T"></path><path d="M483 215v-1c1 0 3-1 4 0l-2 2-1 1h-1v-2z" class="M"></path><path d="M263 287h0l6-8c0 2 1 4 2 5 0 1-1 3-1 4l-2 3c-1 1-2 2-4 2l-1-2h-1v-3l1-1z" class="B"></path><path d="M263 287l2 2h1l-1-1h0l2-2h1 0c1 1 1 2 2 2l-2 3c-1 1-2 2-4 2l-1-2h-1v-3l1-1z" class="X"></path><path d="M535 217c0-4 0-8 1-12v-4c-1-1-2-2-3-2h-2v-1c1-2 2-3 4-4 1 0 1 0 2 1s2 4 2 5c0 4-2 7-3 11 0 2 0 4-1 7v-1z" class="G"></path><path d="M234 126l1 1c3 2 10 3 15 3 3 0 9 1 11 3l1 1v1l-2-1c-4-2-6-3-10-3h-1v2c-2 1-5 1-7 1-2-1-6-4-8-6v-2h0z" class="C"></path><path d="M210 614h3c1 0 1 1 2 0h1 2c0 1-1 2-1 3-2 2-3 4-7 5h-2c-1-1-2-2-2-3v-1c0-1 1-2 2-3l1-1h1z" class="B"></path><path d="M211 617h0c1-1 1 0 1-1l1 1h0c0 1 1 1 1 2h-1 0 0-1-3 0c0-1 0-1 1-2h1z" class="k"></path><g class="S"><path d="M210 614h3c1 0 1 1 2 0h1 2c0 1-1 2-1 3h-2 0-1l1-2h0-2v2l-1-1c0 1 0 0-1 1h0c0-1 0-2-1-3z"></path><path d="M209 614h1c1 1 1 2 1 3h-1c-1 1-1 1-1 2-1-1-1-1-3-1 0-1 1-2 2-3l1-1z"></path></g><path d="M209 614h1c1 1 1 2 1 3h-1-1l-1-2 1-1z" class="M"></path><defs><linearGradient id="q" x1="224.153" y1="595.907" x2="232.664" y2="584.8" xlink:href="#B"><stop offset="0" stop-color="#575856"></stop><stop offset="1" stop-color="#8a888d"></stop></linearGradient></defs><path fill="url(#q)" d="M225 589l-2-7c0-2-1-4 0-6h1v7c2-1 1-6 2-9v4h1v8c1 5 3 10 5 15v1c-1-1-2-1-2-2-2-3-4-7-5-11z"></path><path d="M219 314l3-6c6-7 16-12 25-13 4 0 9-1 12 2h0-1c-9-1-19 0-27 5-5 3-9 7-11 12h-1z" class="S"></path><path d="M483 129c2-1 5-2 7-1 1 1 2 1 3 2l3 1c-1 1-1 1-2 3h-3c-1-1-2-1-2-3h-1c-3 0-6 2-8 3l-1 1h-1c0-1-1-1 0-2 0-2 3-3 5-4z" class="U"></path><path d="M483 129c2-1 5-2 7-1h-3v1c-1 1-2 1-3 1l-1-1z" class="M"></path><path d="M493 130l3 1c-1 1-1 1-2 3h-3c-1-1-2-1-2-3h0c1 0 2 0 4-1z" class="E"></path><path d="M265 177h0c1 0 3 1 4 2-2 2-4 4-6 7 2 2 3 4 5 4s3-1 4-2l1 2c-1 0-2 1-3 2-3 0-5 0-7-2-1-1-3-3-3-4-1-3 2-6 4-8l1-1z" class="N"></path><path d="M265 177h0c1 0 3 1 4 2-2 2-4 4-6 7v1h-1c0-2 0-4 1-6 1-1 1-2 1-3l1-1z" class="L"></path><path d="M277 459c1 0 1-1 1-1h1c2 1 3 2 3 4 1 2 1 4 1 6l1 1c-1 0-2 1-3 1h-1 0-1c-1 0-2 0-2-1-1-1-2-2-1-3v-1h0c0-1 1-2 1-4h1l-1-2z" class="B"></path><path d="M277 459c1 0 1-1 1-1h1c0 1-1 1-1 2 1 1 1 2 1 3l-2 2v1h-1v-1h0c0-1 1-2 1-4h1l-1-2z" class="Q"></path><path d="M278 468v-1h1c1-1 1-1 3-1v1c1 0 1 1 1 1l1 1c-1 0-2 1-3 1h-1 0-1c-1 0-2 0-2-1l1-1z" class="M"></path><path d="M278 468v-1h1c1-1 1-1 3-1v1s0 1-1 1-2 0-2 1h-1v-1z" class="O"></path><defs><linearGradient id="r" x1="363.357" y1="632.973" x2="363.243" y2="638.34" xlink:href="#B"><stop offset="0" stop-color="#717173"></stop><stop offset="1" stop-color="#888687"></stop></linearGradient></defs><path fill="url(#r)" d="M357 631h3l-2 1c1 1 2 1 3 1l8 2 4 1c0 1 0 2 1 3h-3c-3 0-7 0-10-1-1 0-3-2-4-3v-4z"></path><path d="M361 633l8 2 4 1c0 1 0 2 1 3h-3-1c-1-2-7-4-9-4h-1v-1l1-1z" class="g"></path><path d="M224 572c1-1 1-1 1-2l1 1c0 1-1 2 0 3-1 3 0 8-2 9v-7h-1c-1 2 0 4 0 6l2 7c-1 2-1 3-3 4h0l2-2-1-1c-2 1-3 0-5 0 0 0 0-1 1-1 0-3 0-5 1-8s3-6 4-9zm249-356v-2c0-1 2-3 3-4l2 1c1 1 2 2 3 2 0 1 1 1 2 2v2c-1 2-3 3-5 4h-2c0-1-1-1-1-3l-2-2z" class="d"></path><path d="M589 550h0v-1-1-1l1 1c0 1 1 1 2 2-1 1-1 1-2 1h1c2 2 3 5 5 8s6 6 9 9c3 4 6 5 10 8h-1-1 0-1 0l-2-1c0-1-1-1-2-1v-1l-4-2c-3-2-5-5-7-7-1-1-2-2-2-3l-1-2v-1c-1 0-1 0-2-1l-2-4c0-1-1-2-1-3z" class="v"></path><path d="M511 284v-2c1 0 2 1 3 1h3l4-1h1 0v1c1 0 1 0 2 1h1v2l-1 1c-1 1-2 2-4 2v1h-4l-2-1v-1c0-1 1-1 1-2l-2-2h-2 0z" class="i"></path><path d="M524 284h-1c-2 1-3 1-5 0h0c1-1 3-1 4-1s1 0 2 1z" class="F"></path><path d="M515 286h2v1l2-1 1 1v2c1 0 1-2 2-2h2 0c-1 1-2 2-4 2v1h-4l-2-1v-1c0-1 1-1 1-2z" class="H"></path><path d="M536 230c1 0 1 0 1 1-1 8-4 15-9 22-1 1-2 3-3 4s-2 1-3 1h0c1-5 2-10 2-15h1v-5c2 5 1 12-1 17v1c7-8 10-17 12-26z" class="O"></path><path d="M469 582h3v1h-1c1 1 2 1 3 1 2 0 4 0 6-1h2c2-1 5 0 7 0h5c0 1 0 1-1 2-1 0-2 1-4 1h-4-1c-2 0-4 1-7 1h0c-1-1-2-1-3-1h-1v-1h-1 0l-1-1h-3l-2-2h3z" class="Q"></path><path d="M474 586v-1l7-1v1h4v1h-1c-2 0-4 1-7 1h0c-1-1-2-1-3-1z" class="j"></path><path d="M482 583c2-1 5 0 7 0h5c0 1 0 1-1 2-1 0-2 1-4 1h-4-1 1v-1h-4v-1h6c1 0 0 0 1-1h-6z" class="a"></path><path d="M485 586h4l-1-2c2 0 3 0 5 1-1 0-2 1-4 1h-4z" class="c"></path><path d="M388 275c0 1 0 6 1 6 0 1 1 2 2 3h-1c-2-1-2-3-5-1l-1-1c0 1-1 1-1 2h0l1 2h0c1 1 1 2 1 3v1l-1 1-1 1h-1c0-2 0-2-2-4v-1-1c1 0 1-1 0-1l-1 2h-1c0-1 0-1-1-1h-1v1h0v-2c1-1 0-1 0-2h2-1l1-1h2 1 0 1 2v-1c1-1 1-1 1-2-1 1-2 1-3 1-1-1-1-1-2-1h-2 0 1 3c1 1 1 0 2 0 1-1 1-1 2-4v4 1c1-2 2-3 2-5z" class="e"></path><path d="M380 288l1-1h0c1 1 2 2 3 4l-1 1h-1c0-2 0-2-2-4z" class="V"></path><path d="M242 554c1 1 1 1 1 2-2 4-3 9-4 14-1 10 0 21 4 30-1 1-1 1-2 1-6-14-5-32 1-47z" class="F"></path><path d="M283 477l-2 2c-2 1-6 2-8 2-2-1-4-3-5-5-2-2-2-6-2-8l1 2h0 1c0-1 1-1 2-2l-1 3c0 2 1 4 2 5s2 1 4 2h0c2-1 3-3 5-4h2 2c-1 1-1 2-1 3z" class="h"></path><path d="M280 474h2 2c-1 1-1 2-1 3h-2c-2 1-4 2-6 2v-1c2-1 3-3 5-4z" class="l"></path><path d="M284 474c-1 1-1 2-1 3h-2c0-1 0-2 1-3h2z" class="g"></path><path d="M539 482l1-1 6 12c0-1 0-3-1-4 0-2-2-3-2-4h1c1 1 2 3 3 4 1 3 1 6 3 9h-1v-1c-1 2 0 5 0 6l1 1v3h1c-1 1 0 1 0 2v2h0c-1-1-1 0-1-1v-1l-1 2h0c0-2-1-4-1-6v-1h0-1v-1c0-1-1-2-1-3h-1l1-1h-1v-1-2-2c-1-1-1 0-1-1 0-2 0-2-1-3l-3-8h-1z" class="n"></path><path d="M249 180c0 2 0 5 1 8v1h1 0l-7 19h1 1 1c-4 3-6 7-9 11v-1h0c1-2 3-5 2-7 3-5 4-9 6-15 1-2 1-4 2-6v-9 6h1v-7z" class="B"></path><path d="M511 188h0l1 1h1v-1l1 1v1h1c1 0 2 0 2 1 2-2 3-1 4-2 2 2 3 2 5 2 1 1 0 2 0 4-1 1-1 1-3 1h-1l-1-1c-1 0-1-1-2-2h-1v1h-1-1 0v1 1c-1-1-1-1-2-1l-1 1c0-1-1-1-2-2v-1h0v-5z" class="N"></path><path d="M511 188h0l1 1v2c1 1 2 1 2 2v1l-3-1h0v-5z" class="B"></path><path d="M270 336h0c1 1 2 3 3 4 3 4 6 7 8 12 0 2 1 5 0 7-3-1-5-8-6-11-1-1-2-3-3-4-1-2-3-5-2-8z" class="F"></path><path d="M219 481h0c1 2 1 2 0 4h1 1l1 1h-1c0 4 0 7 1 10l1 1h1s1 0 1 1v1h0 1 0c1-1 1-1 2-1h0l1-1s1 0 1 1c-1 1-3 2-4 3l-1 1h-3c-1 1-1 0-2 0 0-2-1-5-1-7l-1-1c0-1 0-2 1-3v-1h-1c0-2 0-3 1-4l-2-1 2-4z" class="a"></path><path d="M219 486h1v1 1c0 3 0 5 1 7h-2l-1-1c0-1 0-2 1-3v-1h-1c0-2 0-3 1-4z" class="N"></path><path d="M221 495v1 1c0 1 1 2 1 3l1 1h1v-1l-2-1v-3l1 1h1s1 0 1 1v1h0 1 0c1-1 1-1 2-1h0l1-1s1 0 1 1c-1 1-3 2-4 3l-1 1h-3c-1 1-1 0-2 0 0-2-1-5-1-7h2z" class="R"></path><path d="M525 526c1-2 1-6 2-8l2 2h0c1 1 1 1 2 1h1 0 0l1 1h0v2 2c1 0 1 0 2 1v1h0v2h0v1 7c-1-3-1-6-2-8h-3c1 1 0 2 0 3h-1v-1-4c-1-1-2-2-3-2v1 3h-1v-4z" class="s"></path><path d="M532 521h0c0 2 1 5 0 7h-1v-2c0-2 0-4 1-5z" class="u"></path><defs><linearGradient id="s" x1="451.892" y1="139.185" x2="469.531" y2="134.167" xlink:href="#B"><stop offset="0" stop-color="#838484"></stop><stop offset="1" stop-color="#9f9d9f"></stop></linearGradient></defs><path fill="url(#s)" d="M453 142h2c1 1 2 0 2-1 1-3 0-9 4-11 2-2 7-1 9 0 3 1 6 3 7 6h0-1c-2-3-4-4-8-5-2 0-5 0-7 1-1 1-1 3-1 5v7c-2 1-5 2-7 2v-4z"></path><path d="M265 177c1-3 3-5 5-6 3 1 5 3 8 5 1 0 2 0 3 1 0 2 0 6-1 7v1c-1-1-1-1-2-1-1-1-1-1-2-3v-2c0-1-1-1-2-2l-4-4c0 2 0 3-1 5v1c-1-1-3-2-4-2h0z" class="B"></path><path d="M430 554v1 2h0c0 1 0 2 1 3 1 2 2 3 3 4-1 0-1 0-1-1-1 0-2-1-2-2-1 0-1 0-1 1s-2 3-3 3h-1l-1 1h-1 0c-1 1-1 1-2 0-1 0-1 0-2 1h0-1 0v1h-2l-1 1c-1 0-1 0-1-1h0c0-1 1-1 0-1h-1v1c-1 0-1 0-1 1v1l-1 1h1v1c-1 1 0 2 0 3-1-1-1-3-1-4 0-3 2-5 4-7l1-1c3-1 6-1 9-1 2-3 3-5 4-8z" class="V"></path><path d="M274 267c3 1 7 2 9 6 0 2-1 4-2 5l-4 4h0c-1-1-1-3-1-4l-2-11z" class="d"></path><path d="M231 417v-2h-3c-1-1-3-3-4-5 0-2 2-5 3-8v5l2 2c4-2 7-2 11-2-1 1-1 2-2 2h-1v1c-1 1-2 1-2 2h-1c-1 0-1 1-2 1h-1c0 1 1 3 0 4z" class="R"></path><path d="M244 112l3-1h4c-1 1-2 1-3 3h2 2v2h3c1 1 1 1 1 2l1 1h1l-1 1c0 2 3 3 3 5l-1 1c-2-2-3-5-3-7-7-6-15-3-21 1-2 2-4 4-7 4-2 0-4-1-6-3h0c1 0 2 1 4 1 5 2 9-3 13-7 2-1 3-2 5-3z" class="L"></path><path d="M244 112l3-1h4c-1 1-2 1-3 3h0-4v-2z" class="X"></path><path d="M281 589l1 1 3-3c1-1 1-1 2-1-1 2-2 3-3 5l-1 1c-1 2-4 4-6 6s-4 5-8 7h-2v-1c0-1 2-4 3-6h2 0c3-3 6-7 9-9z" class="I"></path><path d="M267 605v-1c0-1 2-4 3-6h2v2c-1 2-3 3-4 4l-1 1z" class="S"></path><path d="M509 213l2 1v-1c-2-2-5-3-7-4-4-2-9-6-11-11-1-3 0-6 1-8h1c-1 3-1 8 1 10h0c0-1-1-2-1-3s0-2 1-3h4 0c-1 1-1 1-3 1 0 1 0 4 1 5 1 4 7 7 10 9 1 2 4 3 4 5v1h2l1 1s1 0 1 1c-1 0-1 1-2 2l-4-5h-2 0l1-1z" class="H"></path><path d="M426 621c0-1 1-3 1-4 2-1 3-1 4-1v1c1 1 2 1 3 1h1l-1 2h1c0 1 1 2 1 3l-1 1h1v1l-1 1v-1c-1 0-1 0-2 1h1v2h0 0-3c-1-1-3-1-4-2-1-2-1-3-1-5z" class="G"></path><path d="M427 622l6-1c0 1-1 2-2 2h-3-1v-1z" class="L"></path><path d="M434 620c-1 0-4 1-5 0 0-1 0-1-1-2v-1h3c1 1 2 1 3 1h1l-1 2zm1 0c0 1 1 2 1 3l-1 1h-2c0 1 0 1-1 1h-1-2l-1-1v-1h3c1 0 2-1 2-2l2-1z" class="S"></path><path d="M426 621l1 1v1h1v1l1 1h2 1c1 0 1 0 1-1h2 1v1l-1 1v-1c-1 0-1 0-2 1h1v2h0 0-3c-1-1-3-1-4-2-1-2-1-3-1-5z" class="I"></path><path d="M413 289h0v1c1 2 0 4 1 5v1c-1 1-1 2-1 3s0 2-1 3l1 1-1 1c-1 0-1 0-1-1v-3c-1 2-1 4-1 6s0 3-1 4v1 1 2c-1-1 0-2 0-3h-1c-1-2 0-4 0-5h0c1 1 0 2 1 4v-5c0-1 0-3 1-5h0v-1h1c1-2 1-2 0-4-1 1-1 2-2 2-1 1-1 1-1 2s0 1-1 1v1h-1s0-1-1-2h1l-1-1v2c0 2-3 3-3 5l2 1-1-1h0 2v1l-1 1h0 1v1h1 0v-2 1c1 1 1 0 1 1 0 0 0 1-1 1h-1s-1 0-1-1h-1c-1 1-2 2-3 4v-1h0-1l1-1v-1c0-1 0-2 1-3h0v-1c1-1 1-2 2-3 0 0 0-1 1-1v-1c0-1 0-2 1-3 1 0 1 0 2 1h-1c0 1 0 1 1 2 0-1 0-2 1-3 1-2 3-2 3-5h0v-1h-1-2c-1 1-1 1-1 2h-1v-1l-1 1c0-1 0-1 1-2h1l1-1h3c0 3 1 5 1 8v3-1c1-2 0-5 0-8 0-1 0-2 1-3h0z" class="u"></path><path d="M418 121h2 1l1 1h2c3 1 5 2 6 5 1 0 1 0 2 1 2 1 3 2 4 4s2 5 3 7h0l-1-1v1c-2-2-3-5-5-7-3-4-8-4-12-5l-1-1h-2-1 0l1-1c-1 0-3-1-4-1h1c1 0 2 0 3 1v-1c-1 0-1-1-2-1v-1h1c1 1 1 1 2 1 0-1-1-1-2-1v-1h1z" class="G"></path><path d="M245 539c1-1 1-1 3-1v2c-1 2-2 5-4 6 0 1-1 1-2 2-3 3-5 5-9 6h-4l-1-2 1-1v1h2l1-1h1l3-3v-1h2v1h2s0-1-1-2c0-1 1-2 2-3 0-1 1-1 2-2l2-2z" class="L"></path><path d="M241 543c0-1 1-1 2-2v1c1 0 1 0 2 1-1 0-1 1-2 1 0 1-1 0-1 0l-1-1z" class="b"></path><path d="M245 539h1c0 1-1 3-1 4h0c-1-1-1-1-2-1v-1l2-2z" class="D"></path><defs><linearGradient id="t" x1="226.47" y1="279.657" x2="221.382" y2="279.235" xlink:href="#B"><stop offset="0" stop-color="#b1b1b2"></stop><stop offset="1" stop-color="#d8d8da"></stop></linearGradient></defs><path fill="url(#t)" d="M224 258h2c-3 10-5 22-2 33 1 4 3 8 3 12-8-10-6-24-5-36 0-1 0-2 1-3v-1-2c1-1 1-2 1-3z"></path><path d="M261 489c2 0 3-1 4 0l-1 2h-1v2h0v1l1 1-1 4v9c-2-1-3-2-4-3l-1-1c-1-4 0-9 1-13 1 0 2-1 2-2z" class="j"></path><path d="M259 491c1 1 1 2 1 3-1 1-1 1-1 2h1 0c0-1 1-1 1-1 0 1 0 2 1 3v1c0 1 0 1-1 2v-2c0-1-1-1-1-2-1 1-1 2-1 3l1 1v1l1 1v1 1l-2-1v1l-1-1c-1-4 0-9 1-13z" class="a"></path><path d="M261 489c2 0 3-1 4 0l-1 2h-1v2h0v1l1 1-1 4h-1v-1c-1-1-1-2-1-3 0 0-1 0-1 1h0-1c0-1 0-1 1-2 0-1 0-2-1-3 1 0 2-1 2-2z" class="I"></path><path d="M263 494l1 1-1 4h-1v-1c-1-1-1-2 0-2 0-1 0-1 1-2z" class="N"></path><path d="M493 184l1 1c-6 2-14 4-17 10-2 2-3 6-2 9 1 2 2 3 4 4v3h-1l-2-1c-1 1-3 3-3 4v2l2 2c-1 1-2 1-3 2l-1 1h-2v-1c1 0 1 0 1-1s0-1 1-2h0c0-3 2-5 3-7 0-6-2-10 1-15v-1c4-6 11-8 18-10z" class="B"></path><path d="M473 216l2 2c-1 1-2 1-3 2l-1 1-1-1c1-1 2-3 3-4z" class="X"></path><path d="M280 140h1c0-1 1-2 1-3h0l1-1v1 2 1c0-1 1-2 2-2-3 5-7 11-12 14l1 1h-1v1c-4 2-6 4-9 7-1 1-3 3-4 5-2 2-3 5-5 7v-5 3l1-1c2-6 6-10 9-14l1 1h0c2-2 3-3 6-5 3-3 6-7 8-12z" class="F"></path><path d="M247 218v1c-4 2-7 6-9 10-3 4-6 9-8 13-2 5-3 10-4 15v-2l-1-1c0-4 1-8 3-12l4-13h1v1l-2 7c1-1 1-1 1-2 3-7 9-13 15-17z" class="M"></path><path d="M232 496h0c1-1 1-1 1-2l1 1 1 1c1 0 1 0 2-1l1 1c1 1 1 3 0 4-2 2-5 4-7 5-1 0-1 0-2 1h-1-1-1 0c-1-2-1-2-1-4l1-1c1-1 3-2 4-3 1 0 1-1 2-1v-1z" class="Q"></path><path d="M232 496h0c1-1 1-1 1-2l1 1 1 1v2c-2 0-4 2-7 2l-1 1h-1c1-1 3-2 4-3 1 0 1-1 2-1v-1z" class="V"></path><path d="M227 501l1-1c0 1 0 1 1 2h1l1 1v2h0c-1 0-1 0-2 1h-1-1-1 0c-1-2-1-2-1-4l1-1h1z" class="n"></path><path d="M227 501l1-1c0 1 0 1 1 2h1l1 1v2h-2c-1-1-1-1-2-1h-1v-1h1v-2z" class="a"></path><path d="M235 496c1 0 1 0 2-1l1 1c1 1 1 3 0 4-2 2-5 4-7 5h0v-2l-1-1h0 1s0 1 1 1c0-1 1-1 2-2s1-2 2-2c1-1 1-1 1-2h0-1l-1 1v-2z" class="j"></path><path d="M681 552c0-2 2-3 3-4v-1l1-1 2-2h2 0 1 0c0 1 0 2-1 3h0c-1 1-1 2-2 3v-1c-1 0 0 0-1 1l-1 1h-1c-1 2-3 4-4 5s-2 2-3 2h0c-2 0-5 3-6 4-3 1-5 3-8 4-1 1-3 2-5 2h-1c1-1 1-1 2-1s1-1 2-1v-1l-3 1v-1l7-3c2 0 4-2 6-3 1-1 2-1 4-2 1-1 1-2 3-2 1 0 2-1 3-2s2-3 4-4v-1c1-1 2-2 2-3-1 0-1 2-2 3l-4 4h0z" class="n"></path><path d="M510 613h-4c0 1 0 1-1 1h-2-3c-1-1-2-2-2-3s0-1-1-2h-1 0c2-1 2-4 5-4 3-2 7-3 11-4l2 2c-3 2-7 2-11 4h0 0c0-1 1-2 2-2l1-1h0-1c-2 0-3 1-5 3v1c-1 1-1 2 0 4 1 1 3 2 5 3-1-1-1-2-2-2l-2-2c0-1 0-2 1-3h0 2c1 1 2 2 4 2h3 0 1l-2 2v1z" class="C"></path><path d="M220 194v-1c1 0 2 1 3 2v1c1 1 2 1 2 2 1 1 2 2 3 2l1 1h0-1l3 3 2 3h-1c-1 0-1-1-1-1-1 0-1 1-1 1h-2c-1-1-3-1-5-1h-1l-3-1h-1c0-1-1-2-1-3h1 3c0-1 1-2 1-2 0-1-1-2-2-2v-4z" class="P"></path><path d="M219 205h0v-1h2c0 1 0 1 1 2l-3-1z" class="E"></path><path d="M225 198c1 1 2 2 3 2l1 1h0-1l3 3c-1 1-2 0-2 2h-1s-1 0-1-1h0c-1 0-2-1-2-2h-2v-1h1v-1h1v-1-1-1z" class="U"></path><path d="M219 481l2-2 6-4 2 2 1 1c0 1 0 1-1 2v3l3 5h2 0 0l-1 1c1 3 2 3 1 6l-1-1c0 1 0 1-1 2h0c0-2 1-4 0-5 0-1 0-2-1-3v-1c-1 0-1-1-1-2l-2-2-1 1-5 2-1-1h-1-1c1-2 1-2 0-4h0z" class="i"></path><path d="M225 481c1-1 1-3 1-4h1v1h2v1l-1 1s0-1-1 0h1v2h-1c0 1-1 1-2 1v-2z" class="L"></path><path d="M219 481l2-2 6-4 2 2 1 1-1 1v-1h-2v-1h-1c0 1 0 3-1 4h-1v2c-1 1-1 0-2 1l-1 1h-1-1c1-2 1-2 0-4h0z" class="G"></path><path d="M270 468c1-1 2-3 3-3 1-1 2-1 3-1v1 1c-1 1 0 2 1 3 0 1 1 1 2 1h1l1 1c0 1-1 2-1 3-2 1-3 3-5 4h0c-2-1-3-1-4-2s-2-3-2-5l1-3z" class="d"></path><path d="M506 165h1l1 5h1c1 0 2-1 3 0 1 0 1 0 1 1 1-1 1-1 1-2 0-2 2-4 3-5 2 4 4 8 2 12h-1c-1 0-1 0-1-1l-1 1h-1l-1-1c0 1 0 1-2 2h0-1-1l-1-1h-3c1-1 2-1 2-2h1c0-1 0-3-1-4-1-2-1-3-2-5z" class="L"></path><defs><linearGradient id="u" x1="274.866" y1="612.387" x2="279.351" y2="619.059" xlink:href="#B"><stop offset="0" stop-color="#6f6e6f"></stop><stop offset="1" stop-color="#848484"></stop></linearGradient></defs><path fill="url(#u)" d="M279 608h3c1 1 1 2 1 3h1 1l-1 1c0 1 1 1 1 3v1c-1 2-3 3-5 3-3-1-5-2-7-5 0-1 1-1 2-2l1-2c1-1 2-2 3-2z"></path><path d="M283 611h1 1l-1 1c0 1 1 1 1 3h-1c-1 1-2 1-4 2l-1-1 1-1c1 0 1-1 2-1 1-1 1-2 1-3z" class="T"></path><path d="M279 608h3c1 1 1 2 1 3s0 2-1 3c-1 0-1 1-2 1h0-2c-2 0-2-2-3-3l1-2c1-1 2-2 3-2z" class="d"></path><path d="M262 288v3h1l1 2c2 0 3-1 4-2l1 1 2 3-1 3 1 2v4l-1-1v-1l-1-1c0 1-1 2-1 2l-3-2h0c-1-1-2-1-3-2v-1c0-1-1-1-2-1-1-2 0-3 0-5 0-1 1-2 2-4z" class="H"></path><path d="M262 288v3h1 0v2c-1 1-1 1-2 1 0 0-1-1-1-2s1-2 2-4z" class="O"></path><path d="M262 299l1-1v-3s0-1 1-1c1 1 0 4 0 5h1c1 0 0-3 1-4v-1 3h1c0 1 0 1 1 2h0c1 1 2 1 2 2v1l-1-1c0 1-1 2-1 2l-3-2h0c-1-1-2-1-3-2z" class="M"></path><path d="M265 301l1-1h0c1-1 1-1 2-1 1 1 2 1 2 2v1l-1-1c0 1-1 2-1 2l-3-2h0z" class="S"></path><path d="M266 294c1-1 2-1 3-2l2 3-1 3 1 2v4l-1-1v-1-1c0-1-1-1-2-2h0c-1-1-1-1-1-2h-1v-3z" class="C"></path><path d="M268 299v-2-1l1-1c1 1 1 2 1 3l1 2v4l-1-1v-1-1c0-1-1-1-2-2h0z" class="D"></path><path d="M419 391h1l2-10v5c-1 3-1 7 0 10l1-14h1c0 5-1 10-1 15v5c-1 1 0 6 0 6v4 5c0-2 0-6-1-8v-5-1-3c-1 2-1 3-1 5v2c-1 1 0 4 0 6v3 1h-1c-1-7-2-14-1-22 0-2-1-3 0-4z" class="u"></path><path d="M283 426c2 1 2 2 3 4 2 3 2 7 1 11-1 2-2 4-4 6h0c-1 0-2 0-2 1-1 0-2-1-2-1 2-2 3-5 3-7v-1-6c0-2-1-3-2-4h1c1-1 1-2 2-3z" class="I"></path><path d="M282 440h1s0 1 1 1v-1h1v1c-1 1-1 1-1 2s-1 2-1 4h0c-1 0-2 0-2 1-1 0-2-1-2-1 2-2 3-5 3-7zm1-14c2 1 2 2 3 4v2 3h-1-1-1c1 1 3 2 3 3l-1 1c0-1-1-1-1-2-1 1 0 2-2 2v-6c0-2-1-3-2-4h1c1-1 1-2 2-3z" class="G"></path><path d="M228 276l-1-1c-1 0-1-2-2-2 0-3 2-5 3-7s2-3 3-5c3-3 8-5 12-5 5 0 9 1 13 5v1c-4-3-8-5-13-5s-10 4-13 7h0c-1 2-2 3-2 5h1l1-1c1 0 2 1 2 2 1 0 2 1 3 1h0 1c0 2 0 3-1 4l-7 1z" class="Q"></path><path d="M232 270c1 0 2 1 3 1h0 1c0 2 0 3-1 4l-7 1c1-1 1-2 1-3v-3l2 1c1 0 1-1 1-1z" class="C"></path><path d="M232 270c1 0 2 1 3 1h0l-1 1s0 1-1 2h-2 0v-1h2c0-2-1-1-2-2 1 0 1-1 1-1z" class="S"></path><path d="M491 559c0 1 1 2 1 3l-2 2h-1c-1 1-2 1-3 2l-1 1s-1 1-2 1v-1h1v-1h1c1-1 2-1 2-2 1 0 3-1 3-1 1 0 1-1 2-1v-1c-1-1-1 0-2 0h-1l-1 1h-1s-1 0-2 1c-1-1-1 0-3 1h0c0 1 0 1-1 1h1c0 1 0 1 1 1v1h-1l1 1h0v1l1-1 1 1h-1l-1 1h0-1c0 1-1 1-1 1-1 1-1 0-1 0 0-1 0-1-1-1h-1v1l1 1h0c0 1 1 1 1 2h0v1h0-3v-1h0c0-1-1-1-1-2v-2-1-1h0 0c0-1 1-2 2-2h0l1-1c1-1 0 1 1 0l1-1v-1c-1 0-2 0-2 1-1 0-2-1-3 0h-1l-1 1c-1 0-2 1-2 2h0 1v-1l1-1 2 1c-1 1-1 1-2 1l-4 2v1h0c0 1 1 3 0 3v3h-1v-3-1-3s1 0 1-1l1-1c1-2 3-4 5-4h1c3-1 5-1 8-1 1-1 2-1 3-1 1-1 1-1 3-2z" class="n"></path><path d="M476 568h0c2-1 5-2 6-2h0c0 1-1 1-2 2-1 0-3 1-4 2v2c2-1 1-1 2-2s4-2 5-3l-3 3 1 1s0-1 1-1h1 0-1c0 1-1 1-1 1-1 1-1 0-1 0 0-1 0-1-1-1h-1v1l1 1h0c0 1 1 1 1 2h0v1h0-3v-1h0c0-1-1-1-1-2v-2-1-1h0z" class="v"></path><path d="M520 581v1h1c0-1 1-1 2-1h0 1v1h0c1 0 1-1 2-1h1c1 1 2 2 3 2l-1-1c0-2 0-1 1-2l-1-1c1 0 1-1 1-1h1c0 1 1 1 2 1s1-1 1-1h1 0v1l-1 1c-1 1-2 0-2 0-1 0-2 0-2 1v1c1 0 1 0 1 1l-1 1-3-1-1-1-1 1h-1-1l-1 1h0 0l-1-1h-1 0c-1 1-1 1-2 1 0 0-1-1-1-2 1 0 2 0 2-1h0-1c-1 0-1 1-2 1v1c-1 1-2 1-3 2l-1 2h-2v-1c0-1 0-1-1-1l-1-1h0-1c-1-1-1-1-1-2l1-1 1-1h0c-1 1-2 1-3 2v2l-1-1v1c-1-1-1-1-2-1 0-1 0-1 1-2h2c1 0 1-1 2-2h0 1 1l1-1h-1v1l-3-3h1l1 1h0c1 1 1 1 2 0 0 0 1 0 1-1 1 0 2 1 2 1h1v1h-1c-1 0-1 0-2-1v1c1 0 1 0 1 1h0 1l1 1-2 2-2 1 1 1h1l1-1c0-1 0-1 1-1 0-1 0-1 1-1h0l1-1v-1h0v1h2c1 0 1 0 2 1z" class="d"></path><path d="M280 116c4 3 6 6 7 11 0 3-1 8-2 11-1 0-2 1-2 2v-1-2-1l-1 1h0c0 1-1 2-1 3h-1c1-6 1-12-1-17 1 0 2 1 3 1v1h1c0-2 1-3 0-4l-3-3c-1-1 0-1 0-2z" class="X"></path><path d="M238 608l7 4c1 1 3 2 3 2h1l3 3h1l4 4h0c5 3 9 5 14 7 2 1 3 1 5 2 0 1 0 1-1 2-6-2-13-4-18-8-3-3-6-5-9-7-4-3-8-4-11-8l1-1z" class="l"></path><path d="M545 474v-3l-1-1h0v-1l1 1 2 3h0c0 1 0 1 1 2v2h0c1 1 1 1 1 2h0c1 0 1 1 1 1l1 1h0v1c1 2 2 5 3 7l5 13c1 0 0 0 0 1l-1-2v-1h0c-2-2-2-7-5-9h0v3h-1v1l1 1v1 1l1 1h0v1h0v1h1v1 2h0v1c1 0 1 0 1-1v-1-2c-1 0 0 0-1-1v-1c1 1 1 2 1 2v2c1 1 1 2 1 3h-1 0 0l-1 1-1-1v1c-1-1 0-2-1-3v-1l-1-1v-1-2c0-2-1-4-2-6l-1-3h0l-1-2c0-1 0-2-1-3v-1h0 1c0 1 0 1 1 1h1v2h2 1 0c-1-1-1-3-2-4-1 0-1-1-1-1v-1c-1-2-3-5-5-7z" class="u"></path><path d="M306 622v-1c1-2 2-3 4-3 1 0 3 0 4 1s2 4 2 5c0 2-1 3-2 4-3 3-11 4-15 5l19 1-1 1h-5c-8 0-16-1-25-2l-1-1c0-1 1-1 2-1 8 0 15 0 23-3 2 0 3-2 4-4-1-1-1-3-2-4s-2-1-3-1c-2 0-3 1-4 2v1z" class="I"></path><path d="M265 539c4 0 7 0 10 2 2 1 3 2 4 3-1 1 0 1-1 2h0-1c-1 1-2 1-4 1-2-1-4-1-6-3h-1-1-7 0l3-4c1 0 3-1 4-1z" class="L"></path><path d="M268 542h1c1 0 1-1 2-1h1 0l-2 2c1 0 2 0 3-1 1 0 2 0 2 1l1 1v1c-1 0-1 1-2 1h-1v-1c-1-1-2-1-3-2-1 0-2 1-3 0l1-1h0z" class="C"></path><path d="M265 539c4 0 7 0 10 2 2 1 3 2 4 3-1 1 0 1-1 2v-1c-1-1-1-1-2-1l-1-1c0-1-1-1-2-1-1 1-2 1-3 1l2-2h0-1c-1 0-1 1-2 1h-1v-2h0l-3-1z" class="Q"></path><path d="M232 516l2-2 3 4c0 1-1 2-2 2h-1c0 1-1 1-2 2 0 1 1 1 1 1 0 1-1 2-1 3h-1v1h-1v2l2 2c0 1 0 1 1 2h0v1 1h-1 0v2 1l-1-1-1 1v-3h-2v1l-1-1h0c-1-1-1-1-1-2l1-1 1-1c-1 0-1 0-1-1l-1 1h-1v1l-1-1 1-1h0c0-2 1-2 1-4v-3h2l1 3h1v-2h0c-1 0-1-1-1-1 0-2 2-5 3-7z" class="L"></path><path d="M230 535v-2c0-1 0-1 1-1h0l2 1h0v1 1h-1 0v2 1l-1-1-1 1v-3z" class="D"></path><path d="M230 524c-1 0-1-1-1-1 0-2 2-5 3-7l2 2c0 2-2 2-3 4 0 1-1 1-1 2z" class="O"></path><path d="M247 111c4-1 7-1 11-1 2 1 4 1 6 2 1 0 1 0 2 1h0l-1 1c0 1 0 0-1 1l1 1 1-1-1 3h0c-1-1-1-2-2-2v1 1h-1c-1 1-2 1-4 1h-1l-1-1c0-1 0-1-1-2h-3v-2h-2-2c1-2 2-2 3-3h-4z" class="S"></path><path d="M257 118l2-2c1 0 2 1 3 1v1c-1 1-2 1-4 1h-1v-1z" class="C"></path><path d="M258 112c1 0 2 1 3 1h2v1l-1 1h-4l-1 1v2 1l-1-1c0-1 0-1-1-2h-3v-2h0c3 0 5 1 8 1 0-2-1-2-2-3z" class="F"></path><path d="M251 111c2 0 5 1 7 1 1 1 2 1 2 3-3 0-5-1-8-1h0-2-2c1-2 2-2 3-3z" class="E"></path><path d="M503 181h4c1 0 1 0 2 1v1h-1v1c-1 1 0 1-1 1l-1 1-1 1h0c1 1 1 1 2 0h1c1 0 1 0 2 1h1v5h0v1h-1 0-2v-1l1-1c0-1 0-2-1-3-2 0-3 1-6 1v-1h-4v-4h-4l-1-1h2c1-2 3-2 5-3h1 2z" class="H"></path><path d="M501 181h2l-1 1c-1 0-2 1-3 2l1-3h1z" class="B"></path><path d="M499 184c1-1 2-2 3-2 0 1-1 2 0 3-1 1-2 1-3 1v-2z" class="O"></path><path d="M506 186l-2-1c-1 0-1 0-1-1v-1h1c1 0 2 0 3 1v1l-1 1z" class="F"></path><path d="M479 158v-2c1 0 2 0 3 1l1 2c2 1 2 1 2 3h-1c-1 0-1 0 0 1l1 1c1 0 1 0 1 1s0 1-1 2c0 1-1 1-2 1l-1-2h-1l-1 1c0 2-1 2-2 4h-2c-1-1-1-2-2-4 0-1 0-3 1-4s1-1 2-3l-3 1v-2h1c1 0 2 0 3-1h1z" class="H"></path><path d="M477 160h1 2 0l-1 1c1 0 2 0 3 1h-1v1c-1 1-2 2-3 2v1h-1 0c0-1-1-1-1-2h0c-1-1-1 0-1-1 1-1 1-1 2-3z" class="S"></path><path d="M479 158v-2c1 0 2 0 3 1l1 2c2 1 2 1 2 3h-1c-1 0-1 0 0 1l1 1h-2v-2h-1c-1-1-2-1-3-1l1-1h0-2-1l-3 1v-2h1c1 0 2 0 3-1h1z" class="k"></path><path d="M474 159h1c1 0 2 0 3-1h1 1c0 1 1 1 2 1 1 1 1 1 1 2h-2l-1-1h0-2-1l-3 1v-2z" class="B"></path><path d="M278 447l1 1h-1c-4 4-7 8-10 12-1 2-2 4-3 5 2 0 3-2 5-2 3-2 5-1 7-4l1 2h-1c0 2-1 3-1 4h0v-1c-1 0-2 0-3 1-1 0-2 2-3 3s-2 1-2 2h-1 0l-1-2v-1c-4 4-7 8-9 13-1-2 0-2 0-4h0c1-1 1-3 2-4h0c0-2 1-4 2-6l1 1-2 4c1-1 2-3 3-4 3-5 7-11 11-16 1-1 2-3 4-4z" class="G"></path><path d="M442 387v5c1 1 1 2 1 3 1-1 1-1 1-2h1l-1 2h-1v1h0c-1 0-1-1-1-1 0 1 0 2 1 4 0-1 1-1 0-1v-1h1v2l1 1v1h0c2 2 1 3 2 4-1 0-1 1-2 1 0 0 0-1-1-2v-1c-1-1 0-2 0-3h-1v2 1c-1 1 0 1-1 2v4l-1 3h1c0 1 0 1-1 2v4 1 1c-1 1 0 2 0 4v-2-1l1 1h0 0c0-2 0 1 0-2v-1-4-1h0 1v2-2c1 0 0-2 0-2v-1h1 1l1 1v-1-2h0c0-1 1-1 1-2h1l-1 1v1 1c-1 1-1 1-1 2h-1c-1 0 1 1 0 0h-1v1h0 0v2h0v1h0c-1 2-1 4-2 6 0 1 1 3 0 4v2c-1 1-1 2-1 3v1c0-1-1-3 0-4 0-1 0-2 1-3v-1c-1 0-1 0-1 1l-1-1v1 1l-1-1h0v-6c-1 1-1 2-2 2 0-2 1-3 0-5 1-1 0-4 1-4v1h1c-1-2 0-2 0-3h0v-2-1h1v1 2h0 1v-2-1c0-1 1-2 1-3v-2c0-1 1-2 0-3v-1-2c0-3-2-6 0-9z" class="n"></path><path d="M439 408c1 1 1 1 1 2v6h-1v-6h0v-2z" class="u"></path><path d="M261 242c0 2 0 3-1 4h-1c0-1-1-2-2-3v-1h1-2c-2-1-1-4-2-6 0-1-1-2-2-3 0 1 1 2 1 3v2l-4-1v-4c1-1 1 0 2 0 0 0 0-1 1-2h1v-2c0-2-1-2 0-3 0-1 1-2 2-3h1c1 1 1 2 2 4h-1-2v1h2c1 1 1 1 1 2-1 0-1 1-1 2h-2v2l1-1c2 1 3 4 4 5v2c0 1 0 2 1 2z" class="J"></path><path d="M491 150l2 1 1 2c1 0 1 0 1-1h1v1 1h0c1 0 3-1 3 0l1 1c0 1-1 2-1 3h-2s0 1-1 1c-2 3-5 3-9 3h-2c0-2 0-2-2-3l-1-2c1 0 1 0 2 1l1-1-2-2c1 0 2 1 4 2 1-1 1-2 2-3 1 0 1-1 1-2s1-1 1-2z" class="m"></path><path d="M485 157c2 1 3 2 4 4h-1-2v-1c-1-1-1-2-2-2l1-1z" class="T"></path><path d="M491 150l2 1-1 1 1 2v1h0c-1 0-1 1-2 1 0 1-1 1-1 1-1-1-1-2-1-3 1 0 1-1 1-2s1-1 1-2z" class="C"></path><path d="M435 618c0-1 1-1 2-1 1-1 4-1 5-1s1 1 1 1c2 2 4 3 6 4h2 0l-5 5h-3-1c-1 1-3 1-4-1-1 0-2-1-2-2s-1-2-1-3h-1l1-2z" class="d"></path><defs><linearGradient id="v" x1="217.275" y1="167.485" x2="229.587" y2="165.916" xlink:href="#B"><stop offset="0" stop-color="#8f8d8e"></stop><stop offset="1" stop-color="#aeadaf"></stop></linearGradient></defs><path fill="url(#v)" d="M225 180c-2-1-3-1-5-2-2-2-3-5-3-8s1-6 4-9c2-3 5-3 8-3v2c1 1 1 2 1 3-3 0-6-1-8 0-1 2-1 4-1 6 1 2 2 3 4 5-1 1-1 1 0 2v2 2z"></path><path d="M432 373l1-3c1 3-1 16-1 19 0 1 0 2 1 3v6h-1c0-1 0-1-1-1l1-2-1 1h0v-3h-1c0 1 0 4 1 6v3h1v-2h0l1 1v-1c0 1 1 3 0 4h0v-2l-1 1-1 1v-1l-1-2v-1h0v-1-1 2h0c0 2 1 7 0 8-1-1 0-3 0-4-1-1 0-2-1-3v-2h0 0v5 3c0-1-1-1-1-2h0v-1-2c0-2 0-3-1-5v-1-4c1 0 0-1 0-2v-1h0v-2l1-1c1 3-1 8 0 11h1v-7-2c0-4 1-9 1-13h1v4l1 1v-7z" class="q"></path><path d="M432 398v-9c0 1 0 2 1 3v6h-1z" class="V"></path><path d="M229 436v-2c2-1 5 1 6 2 2 1 5 2 6 4-3 0-7-1-9 2-1 1-2 2-2 4l-1 2h3c0 1 0 1-1 2h0c-1 1-1 2-2 3 0-1 1-2 1-3l-1-1-2 2h0-1 0l1-1c1 0 1 0 2-1l-1-1h0c0-1-1-1-2-2s-1-3-2-5v-5c1 1 2 2 4 2 0-1 0-1 1-2z" class="d"></path><path d="M270 361c4 3 6 6 9 9 2 1 2 3 3 5 2 7 3 13 3 20 0 5 0 10-1 14-1-1-1-2-1-3 1-5 1-11 1-16v-1l-1-1c0-5-3-13-7-16-2-1-3-2-5-3l-2-3 3 1h1c-2-2-4-3-6-4l1-1c1 0 1 0 2-1z" class="L"></path><path d="M273 367c1 1 3 3 3 5-2-1-3-2-5-3l-2-3 3 1h1z" class="P"></path><path d="M210 150c0-2 1-4 2-6 2-3 4-4 8-5 2 0 4-1 6 1h0v1h-1 1v1h0c0 1 0 2-1 2l-2 1-2 1c-2 1-3 3-4 4h-1c0-1-1-1-1-2l-2 2 1 1v2c-1 1-1 1-1 3l-1-2s-1-2 0-3h0v-1h-2z" class="S"></path><path d="M215 143c1 0 2-1 2-1h2 2l-4 2-2-1z" class="L"></path><path d="M222 144c0-1 1-2 1-2h3c0 1 0 2-1 2l-2 1-1-1z" class="F"></path><path d="M215 143l2 1-2 2v1 1h0l-2 2 1 1v2c-1 1-1 1-1 3l-1-2s-1-2 0-3h0v-1c0-1 0-2 1-4 0-1 1-1 1-1 1-1 1-1 1-2z" class="B"></path><path d="M217 144l4-2 1 2 1 1-2 1c-2 1-3 3-4 4h-1c0-1-1-1-1-2h0v-1-1l2-2z" class="D"></path><path d="M215 147c1 0 2 0 3-1l1-1 2 1c-2 1-3 3-4 4h-1c0-1-1-1-1-2h0v-1z" class="C"></path><path d="M258 263c4 0 8 0 12 1h1c-1-2-1-4-2-5-1-2-1-4-3-6h0v-1c1 0 2 1 2 1v1c0-1 0-1 1-2l2-1v2l-1 1h0c1 1 1 4 2 6 0 2 1 4 1 6l1 1 2 11c0 1 0 3 1 4 0 4 2 7 2 11-5-8-6-19-8-27-4-2-7-2-11-1-2 0-5 2-6 4l-2 2-1-1 1-2c1-2 4-4 6-5z" class="G"></path><defs><linearGradient id="w" x1="469.93" y1="455.876" x2="475.183" y2="431.879" xlink:href="#B"><stop offset="0" stop-color="#161616"></stop><stop offset="1" stop-color="#343434"></stop></linearGradient></defs><path fill="url(#w)" d="M474 430h1v3c1 0 1-1 1-2 0 2 0 7 1 9v3c1 0 1-1 1-2h0v3h0c-1 0-1 0-1 1 0-1 0-1-1-1h0v3s-1 1-1 2c1 1 1 1 0 2v2 2 1h-2v1l1 1v-1h0v2 1-1c0-1 0 0-1-1h0v-3l-1 3v-1l-2-3v-1-3h0v-2c1-1 1-2 1-3s0-3 1-4v-4-4h1v1l1-1v-3z"></path><path d="M475 453h0l-1 2h0v-3c0-2 0-4-1-5h1c1-2-1-5 1-6 1 0-1 4 0 5 2-1 0-4 2-6v3c1 0 1-1 1-2h0v3h0c-1 0-1 0-1 1 0-1 0-1-1-1h0v3s-1 1-1 2c1 1 1 1 0 2v2zm-18-59c0-5 0-10-1-14v-1h0 1l1 11h0 1v-3c1 2 1 4 0 6v4c1 2 0 5 0 7-1 3-2 5-3 8 1 1 1 1 1 2v1c0 1 0 1-1 1v3h0v-2l-1 3v3c-1 2 0 3-1 4v1c0 1-1 2-1 4h0v1 1-3-3-1-1-6c0-1 1-4 1-5v-1-2c1 0 1-1 1-2l-1-1h1v-5 1c-1 1-1 1-1 2h0 0l2-7c0-2 1-4 1-6z" class="s"></path><path d="M454 417h1c0 1 0 3-1 4v3 2h-1v-2h1c-1-2 0-5 0-7z" class="e"></path><path d="M459 387c1 2 1 4 0 6v4c1 2 0 5 0 7l-3 8-1 5h-1c1-3 1-6 1-9 1-2 1-4 1-5 2-5 2-9 2-13h0 1v-3z" class="V"></path><path d="M433 366h1v-2h1v3h1v-2h1c0 1 0 2 1 3-1 0-1 0-1 1-1 1-1 5-1 7v6 3c-1 1-1 2-1 2 1 2 1 5 1 7l-1 10h0-1 0v-4-2h-1v-6c-1-1-1-2-1-3 0-3 2-16 1-19l-1 3c0-3 0-5 1-7z" class="i"></path><path d="M433 388c0-1 1-3 1-3 1-4 0-9 0-13h1v2 8 3h1c-1 1-1 2-1 2v9h-1c0-2 1-7-1-8z" class="r"></path><path d="M435 387c1 2 1 5 1 7l-1 10h0-1 0v-4-2h-1v-6-4c2 1 1 6 1 8h1v-9z" class="n"></path><path d="M500 283h-1c0-3-2-6-3-8v-2c2 0 3-1 5 0l2 2c1 1 1 1 2 3h0-2l3 3c1 1 2 1 3 2v1h2 0 2l2 2c0 1-1 1-1 2v1l-2-1h-1-1c-1 0-2-1-3-1 0 0-1 0-2-1v1l-1-1-1-1c-1 0-2-1-3-2z" class="T"></path><path d="M505 286l-1-1c1 0 1-1 2 0 1 0 3 2 4 3-1 0-2-1-3-1 0 0-1 0-2-1z" class="d"></path><path d="M509 284h2 0 2l2 2c0 1-1 1-1 2v1l-2-1c0-2-1-2-3-3h-1v-1h1z" class="M"></path><path d="M500 283h-1c0-3-2-6-3-8v-2c2 0 3-1 5 0l2 2c1 1 1 1 2 3h0-2l-1-1h-1l1 1v1c-2 0-3-1-4-3v1c0 1 1 2 1 2h1l1 1c0 2 0 2-1 3z" class="B"></path><path d="M269 179v-1c1-2 1-3 1-5l4 4c1 1 2 1 2 2v2c0 4-2 5-4 7-1 1-2 2-4 2s-3-2-5-4c2-3 4-5 6-7zm398 396h1 0 1c-1 1-2 1-3 2l1 1h-1v1h0l1 1h-2c-5 2-10 3-15 4-4 1-9 2-13 1-2-1-5-1-7-2v-1c1 0 2 0 3-1 0 1 3 1 4 2h2c2 0 6 1 8-1-2-1-7 0-8 0h0 7c1-1 1-1 2-1h1c-1-1-2-1-2-1h3 1c1 0 2-1 4-1 3 0 6-2 9-3h0c2 0 2-1 3-1z" class="d"></path><path d="M662 578h1v1c-3 1-6 1-10 2-1 1-3 1-5 1h0v-1l14-3zm-12 6h-1v-1c4-2 9-1 13-2 1-1 2-1 3-1-5 2-10 3-15 4z" class="s"></path><path d="M230 446c0-2 1-3 2-4 2-3 6-2 9-2h2c3 2 6 5 9 8l-2 1-2-3c-1-1-1-1-2-1h-1c-2 0-4 1-6 2l-1 2c-1 1-1 0-1 1l-2 1v1h-1v-1c-1-1-1-1-1 0l-2-1h0c1-1 1-1 1-2h-3l1-2z" class="d"></path><path d="M235 445c2 0 2-1 4-1 2 1 4 0 6 1-2 0-4 1-6 2l-1 2c-1 1-1 0-1 1v-2h0c0-1 1-1 1-1v-1c-1 0-2 0-2-1h-1z" class="k"></path><path d="M231 446v1c0-1 1-1 1-1 1-1 2-1 3-1h1c0 1 1 1 2 1v1s-1 0-1 1h0v2l-2 1v1h-1v-1c-1-1-1-1-1 0l-2-1h0c1-1 1-1 1-2h-3l1-2h1z" class="Q"></path><path d="M232 448l2-1h0 1v2 1h2l-2 1v1h-1v-1c-1-1-1-1-1 0l-2-1h0c1-1 1-1 1-2z" class="I"></path><path d="M230 446c0-2 1-3 2-4 2-3 6-2 9-2h2c3 2 6 5 9 8l-2 1-2-3c-1-1-1-1-2-1-3-3-6-4-11-4-1 1-2 1-3 2s-1 2-1 3h0-1z" class="M"></path><path d="M504 175l2 1h3l1 1h1 1 0 1c2 0 3-1 4 0 1 0 1 1 2 2v3h0c0 1 0 1-1 2h-1 0l-2 1h-1c0 1-1 2-1 3v1h-1l-1-1h0-1c-1-1-1-1-2-1h-1c-1 1-1 1-2 0h0l1-1 1-1c1 0 0 0 1-1v-1h1v-1c-1-1-1-1-2-1h-4-2c0-1 1-1 2-1h2v-1c-1 0-2-1-2-2h0l1-2z" class="Q"></path><path d="M512 179v1l3 3h-3c0-1 0-1-1-2v-2h1z" class="M"></path><path d="M513 177c2 0 3-1 4 0 1 0 1 1 2 2v3h-2l-1 1h-1l-3-3v-1l1-2z" class="O"></path><path d="M463 157c0-1 0-1 1-2 2-2 5-2 8-1 1 0 2 1 3 1h0c1 0 3 0 4-1h1 0c1 1 1 1 3 1l2 2-1 1c-1-1-1-1-2-1-1-1-2-1-3-1v2h-1c-1 1-2 1-3 1h-1v2l-1 2-2-2c-1 1-1 1-1 3h-1l-1 1-1-1h-1c-1 0-1 0-2-1h-2v-3c0-1 1-2 1-3z" class="l"></path><path d="M471 161c1-1 2-1 3-2v2l-1 2-2-2z" class="F"></path><path d="M473 157h3c-1 1-1 1-2 1s-1 1-2 2c-1 0-2 1-3 1h-1v-1c1-1 2-2 3-2l2-1z" class="C"></path><path d="M463 157h1v1h0c2-1 3-2 4-2 2 0 3 0 5 1l-2 1c-1 0-2 1-3 2v1h1 0-1v-1-2-1h0c-1 1-4 3-4 3h-1-1c0-1 1-2 1-3z" class="L"></path><path d="M462 160h1 1s3-2 4-3h0v1 2 1h1c0 1-1 2 0 3h0l-1 1-1-1h-1c-1 0-1 0-2-1h-2v-3z" class="G"></path><path d="M468 158v2 1h1c0 1-1 2 0 3h0l-1 1-1-1c-1-1-1-2-2-3 1-1 1-2 3-3z" class="H"></path><path d="M530 473c2 1 6 4 7 6 1 1 2 2 2 3h1l3 8c1 1 1 1 1 3 0 1 0 0 1 1v2l-1-3v-2h-1c-1 1 0 2 0 3v1 1h0 0c1 1 1 2 1 3 1 1 1 3 1 4l1 3v2 3 2c-1-2-1-5-2-6v-2c0-1 0 0-1-1v-1h0v-1c0-1 0 0-1-1v-1l-1-3v-4c-1 1-1 2-1 2h0v-2c-1 0-1 0-1-1-1 1-1 2 0 2v1 1l-1-1-5-15 1-1c-1-2-2-4-4-6z" class="q"></path><path d="M539 492h-1v-2l-1-2 1-1v1c1 1 1 2 2 3v1 1c-1 0-1 0-1-1z" class="V"></path><path d="M530 473c2 1 6 4 7 6 1 1 2 2 2 3h1l3 8-1 1v4h0v1-1c-1-1-1-3-1-4-2-4-4-8-7-12-1-2-2-4-4-6z" class="W"></path><path d="M227 484l1-1 2 2c0 1 0 2 1 2v1c1 1 1 2 1 3 1 1 0 3 0 5v1c-1 0-1 1-2 1 0-1-1-1-1-1l-1 1h0c-1 0-1 0-2 1h0-1 0v-1c0-1-1-1-1-1h-1l-1-1c-1-3-1-6-1-10h1l5-2z" class="L"></path><path d="M229 497h-1v-1h2l2 1c-1 0-1 1-2 1 0-1-1-1-1-1zm-2-13l1-1 2 2c0 1 0 2 1 2l-2 1h-5l1-2h1c0-1 0-2 1-2z" class="I"></path><path d="M227 484l1-1 2 2-3 2h-1v-1c0-1 0-2 1-2z" class="F"></path><path d="M231 487v1c1 1 1 2 1 3 1 1 0 3 0 5v1l-2-1h1v-1h-1c-1-2-1-3-1-5 1-1 1-1 0-2l2-1z" class="N"></path><path d="M231 487v1 2h-2c1-1 1-1 0-2l2-1z" class="H"></path><path d="M221 486v1c1 1 1 2 1 4h0c1 0 1 0 2-1s3-1 4 0v4c-1 1-2 2-3 4 0-1-1-1-1-1h-1l-1-1c-1-3-1-6-1-10z" class="Q"></path><path d="M223 495h1c1 1 1 1 1 2h-2v-2z" class="S"></path><path d="M221 364v-4c-1-1 0-3 0-4h0v1c0 1 0 2 1 3h0c0 1 0 1 1 2 1 0 2-1 3-1 1 2 1 3 1 4l1 1v1 2h2v-1l1 1v2c0 3 2 7 4 9v1c0 1 0 1-1 2 1 1 1 3 1 5-1-2-1-3-1-4-2-1-3-3-4-4 0 0 0-1-1-1h-1c-2-1-4-3-5-4v-4-1-1-1h0l-1-1c1 0 1 0 1-1-1 0-1-1-2-2h0z" class="l"></path><path d="M226 372h0c1 0 1 0 1-1l1 1h1c2 1 2 3 3 5h0l2 7c-2-1-3-3-4-4 0 0 0-1-1-1h-1c-2-1-4-3-5-4v-4c1 1 2 1 3 1z" class="d"></path><path d="M226 372h0c1 0 1 0 1-1l1 1h1c-1 1 0 3 0 4v2h0c-2-1-2-4-3-5v-1z" class="p"></path><defs><linearGradient id="x" x1="495.157" y1="290.718" x2="485.643" y2="294.253" xlink:href="#B"><stop offset="0" stop-color="#908f90"></stop><stop offset="1" stop-color="#c4c4c5"></stop></linearGradient></defs><path fill="url(#x)" d="M499 267c2 0 3-1 5-2l1 1c-2 1-5 2-6 3-8 4-12 11-11 19 1 7 5 15 4 22h-1c0-1 0-2-1-3 0-4-2-9-4-13-2-3-6-8-9-9-2 0-4 1-5 2-1 0-1-1-1-1 1-1 2-1 3-2 4 0 6 2 9 4-2-3-4-5-5-7-2-3-4-7-3-9v-1c0-1 0-1 1-2v1h-1c0 2 0 3 1 4h0c1 6 7 9 8 14 0 1 0 1 1 2h1l-1-9h1v-2c2-5 8-9 13-12z"></path><path d="M414 285v-1 5l1 1v-5h1l2 1c0 1 0 3 1 4v-4c1 1 2 5 2 6v1h0v6l-1 1c0 3 0 7-1 9 1 1 0 2 0 3v1h-1s-1-1-1-2l-2-1c1-2 1-5 1-7 0-1 0-1-1-2v2c0 1-1 1-1 2s-1 2-1 3v1c0 2 0-1 0 1v1l-1-1c1-1 1-1 1-2v-2l1-1v-1-2l1-1c-1 0 0 0 0-1s-1-2-1-3 0-2-1-3v-5-1c1-1 1-2 1-3z" class="n"></path><path d="M416 303h1v8l-2-1c1-2 1-5 1-7z" class="V"></path><path d="M414 285v-1 5l1 1v-5h1v1c-2 3 0 7-1 10-1-3-1-5-2-7v-1c1-1 1-2 1-3z" class="e"></path><path d="M416 285l2 1c0 1 0 3 1 4v1c-1 3-1 5-1 8-2-4-2-9-2-13v-1z" class="V"></path><path d="M419 290v-4c1 1 2 5 2 6v1h0v6l-1 1c0 3 0 7-1 9-1-6 1-12 0-18v-1z" class="W"></path><path d="M420 300v-7h1 0v6l-1 1z" class="e"></path><path d="M459 387v-3h1v1l1 15c0 1-1 3-1 4v4l1-1h0v-3h1v2c0 1 0 2 1 3 0 1-1 2-1 3h0v-2 1h0c0 1 0 1-1 1l1 1v1 5l1-1h1 1c-1 5 0 10-1 14h0l-1-1h1c-1-1-1-2-1-3v-3 3l-1 9h-1c0-2 1-5 0-7v-1c-1 1-1 3-2 4v-1-4c0-1 0-1-1-2l1-1h-1l-1-1 2-10v-1c0-1 2-4 1-5h-1v3c-1 2-1 4-1 5-1 0-1 1-1 2h1l-1 1v-2l-1 1v3 1-1-2h0v-3c1 0 1 0 1-1v-1c0-1 0-1-1-2 1-3 2-5 3-8 0-2 1-5 0-7v-4c1-2 1-4 0-6z" class="q"></path><path d="M462 414v5h-1v-5h1z" class="W"></path><path d="M459 414c0 1 1 2 0 3v1 7h-1l-1-1 2-10z" class="r"></path><path d="M437 369c0 1 0 2 1 3 0-1 0-4 1-5 0 2 1 6 1 8 0 3-2 11-1 13 1-2 1-4 1-6 1-3 1-6 1-9 1 0 1 0 1 1v1 5c0 1-1 3-1 4v3h1c-2 3 0 6 0 9-1-1-1-2-1-2v1c-1 1-1 3-1 4v3h0c1-1 0-3 1-3h0v3s0 1-1 1h-1c0 1 1 2-1 3h-1c-1 0-1-1-1-2h0s0 1-1 1v-1h0l1-10c0-2 0-5-1-7 0 0 0-1 1-2v-3-6c0-2 0-6 1-7z" class="q"></path><path d="M436 394h1c0 3 0 7-2 10l1-10z" class="r"></path><path d="M438 406v-9c1-2 1-6 2-8v6h1c-1 1-1 3-1 4v3h0c1-1 0-3 1-3h0v3s0 1-1 1h-1c0 1 1 2-1 3z" class="s"></path><path d="M437 369c0 1 0 2 1 3 0-1 0-4 1-5 0 2 1 6 1 8h-1v5c-1 2 0 5-2 7v-5h-1v-6c0-2 0-6 1-7z" class="V"></path><path d="M494 474l2 1c1-1 3-2 4-3h1c0 1 0 1-1 1-1 1-2 2-2 3-1 1-1 1-2 1-1 1-3 3-4 5 0 1 0 2-1 3v1c-1 1-1 1-1 2 0 0-1 1-1 2v1c0 2-1 4-1 7h0c0 1-1 1-1 2h0v1c0 1 0 2-1 3h-1-1-1-1v-2c1-1 1-1 0-2 1 0 1 0 1-1s0-1 1-2v-3h0v-2h0l1-2c-1 0-1 0-1-1v-3l1-1c0-1 0-1 1-2v-1l1-1v-1l-2 1c1-1 2-2 3-2h1c0-1 1-2 2-3h0c1-1 2-2 3-2z" class="e"></path><path d="M484 497v-3h0v-2h0l1-2c-1 0-1 0-1-1v-3l1-1c0-1 0-1 1-2v2c-1 2-2 3-2 5l3-5c0 2-1 4-2 6 0 2-1 4 0 6 0 2-1 3-1 4h-1l1-4z" class="n"></path><path d="M485 497v-2c1-4 3-8 5-11h0c-1 3-3 7-3 11 1-1 1 0 1-1s0-2 1-2v-1c0 2-1 4-1 7h0c0 1-1 1-1 2h0v1c0 1 0 2-1 3h-1-1-1-1v-2c1-1 1-1 0-2 1 0 1 0 1-1s0-1 1-2l-1 4h1c0-1 1-2 1-4z" class="u"></path><path d="M494 474l2 1c1-1 3-2 4-3h1c0 1 0 1-1 1-1 1-2 2-2 3-1 1-1 1-2 1-1 1-3 3-4 5 0 1 0 2-1 3v1c-1 1-1 1-1 2 0 0-1 1-1 2v1 1c-1 0-1 1-1 2s0 0-1 1c0-4 2-8 3-11 1 0 1-1 1-1v-1l-2 1h0l1-3h0l-1 1-1-1 3-3v-1h0c1-1 2-2 3-2z" class="r"></path><path d="M367 624c4-1 7-1 11 0l2 1-1 2-2 1h0l-2 2c0 1 0 2 1 3h0v1h-2c-2 1-2 1-4 1h0 0-1l-8-2c-1 0-2 0-3-1l2-1h-3c1-1 3-2 4-3 2-2 3-3 6-4z" class="N"></path><path d="M378 624l2 1-1 2-2 1c-1 0-1 0-1-1 1-2 0-2 2-3z" class="T"></path><path d="M360 631l14 3c-2 1-2 1-4 1h0 0-1l-8-2c-1 0-2 0-3-1l2-1z" class="d"></path><path d="M367 624v1h3c2 1 1 2 3 2h1c0 1 0 1-1 1h-2-3-7c2-2 3-3 6-4z" class="I"></path><path d="M652 452c3 0 6-1 9-1s6 1 10 2v1l6 3c1 0 2 0 3 1 3 2 5 4 7 5s4 3 4 4c0 2 1 4 1 5h-1v-1c0-1-1-1-1-2l-3-3h-1l-2-2h-1v1c1 2 3 4 4 7-2-1-3-3-4-4l-6-7v1h0l-3-1v1c-3-2-5-4-8-5h0v-1c-3-3-6-1-9-3h0c-2 0-3 0-5-1z" class="Z"></path><path d="M683 465a30.44 30.44 0 0 1-8-8l4 2 1 1c2 1 3 2 4 4h-1v1z" class="W"></path><path d="M661 451c3 0 6 1 10 2v1h0c2 1 4 2 5 4h-1c-1 0-2-2-5-2h0c-1-1-2-2-3-2-1-1-1-1-2-1-1-1-2-1-4-1v-1z" class="R"></path><path d="M652 452c3 0 6-1 9-1v1c2 0 3 0 4 1 1 0 1 0 2 1 1 0 2 1 3 2h0c1 1 2 1 2 2-1 0-3-1-4-2s-1-1-2-1l-1-1c-3-1-6-1-8-1s-3 0-5-1z" class="C"></path><path d="M496 477l1 1h0c1 0 2 0 2 2l1-1h0 1l-2 3v2h1v1c1 2 1 2 3 2v4c-1 1-2 3-2 4h-1v1l-1-1h0l-1 1-1-1c0-1 0-2-1-3h-1c0 1-1 2-1 4h-1v1h0c0 1-1 1-1 2v1h-1v-6l-1 1v-1c0-1 0-1 1-2v-2h0l-1 1c0 1-1 2-1 3v2l-1 2c0-3 1-5 1-7v-1c0-1 1-2 1-2 0-1 0-1 1-2v-1c1-1 1-2 1-3 1-2 3-4 4-5z" class="V"></path><path d="M242 279c1-1 3-2 4-2h0l1-1h-1v-4h1l3 3 1 1v-1-2l-1-1-3-3c1 0 2 0 2 1h2 0l1 1 1 1v1 1h2l1 1c-1 0-1 0-2 1h0c0 1 0 2-1 2h0-1c0 1 0 2-1 3h-1c0 1-1 1-1 1l-3 1c-1 1-3 2-4 3h-1-1 0-2c-1 1-2 1-3 1v-1h-1c0 1-1 1-2 1v-1c1 0 1-1 2-2h0 0c-1 0-2-1-3-1l-1 1c0-1-1-2-1-2 1-1 1-1 3-1v-1h-2c1-1 2-1 3 0h1 1 2s1-1 2-1 1-1 2-1l1 1z" class="h"></path><path d="M234 280h1 2s1-1 2-1 1-1 2-1l1 1-5 3c-1 0-1 1-2 1-1-1-1-2-2-1l-1 1-1-1c1-1 1-1 2-1h0l1-1zm1 6c1 0 1 0 1-2 1 0 2 0 2-1 2 0 3-1 5-1 1-1 1-1 2-1 1-1 1 0 1-1 2-1 4-2 5-3h1v1c0 1 0 2-1 3h-1c0 1-1 1-1 1l-3 1c-1 1-3 2-4 3h-1-1 0-2c-1 1-2 1-3 1v-1z" class="L"></path><path d="M246 283h0c0-1 1-1 1-2h1l1 1-3 1z" class="F"></path><path d="M595 589l1-1v-1c2 1 3 2 4 3 2 0 2 0 4 1h3c2 0 3 2 6 2 1 0 2 0 3 1l7 2 1 1h3c0-1 1-1 1-2 2 1 4 1 5 2 0 0-1 0-2 1l-3 1h1l1 1v1c-1 0-1 0-2 1h0c-2 0-5 0-7-1v-1c-1 0-2 1-3 1-2 0-3 0-5-1-1-1-3-1-5-2h-1c0-1-1-1-2-2-1 0-1 0-2-1h1 1l1 1h0c1 1 3 2 4 2h0v1h1 2s1 0 2 1h0 3v-1h-2c-1-1-1 0-2 0h0l-1-1h-1 0c-1 0-2 0-3-1l-6-3s-1 0-1-1l-2-1c-1-1-3-2-5-3z" class="K"></path><path d="M624 597h3c0-1 1-1 1-2 2 1 4 1 5 2 0 0-1 0-2 1l-3 1c-1 0-2 0-3-1-3 0-5-1-7-2 1 0 3 0 4 1h2z" class="r"></path><path d="M600 590c2 0 2 0 4 1h3c2 0 3 2 6 2 1 0 2 0 3 1l7 2 1 1h-2c-1-1-3-1-4-1s-3-1-5-1c1 1 2 1 2 2-1 0-3 0-5-1l-5-2h0c-2-1-3-3-5-4z" class="V"></path><path d="M254 394h1l2 3c2 0 2 1 4 0 1 1 2 2 3 2v1c-2 0-3-2-5-1v1c1 1 1 2 2 3l1 1v2c1 1 2 2 2 3h1l1-1 1 1h1c1-1 1-1 2 0v1l1-1h1l1 1 1-1c1 2 0 3 0 5h0v2h0v2 2c0 1 0 1-1 2s-1 1-2 1c-4-2-7-7-9-10l-1-2c0-1-2-3-2-4h0l2-2c0-1-2-2-2-3-1-1-2-1-2-2s0-2-1-3l-1-1c0-1 0-1-1-2z" class="G"></path><path d="M261 407h1v1 1c-1 0-1-1-2-1l1-1z" class="F"></path><path d="M272 414h1c1 2 0 1-1 2v1h-1c-1 0-1-1-1-1l2-2z" class="N"></path><path d="M267 414h0c0-1 0-2-1-3l1-2h0c1 0 1 0 1 1v4l-1 1v-1z" class="H"></path><path d="M266 417c1-1 1 0 1-1l-1-2h1v1h1v2l1 1h2v1c2 0 2 0 3-1v2l-2 1h-2v-1c-1 0-1 0-1-1-2 0-2-1-3-2z" class="I"></path><path d="M262 413v-1c1 0 1 0 1-1l1 1c0 2 1 4 2 5s1 2 3 2c0 1 0 1 1 1v1h2l2-1c0 1 0 1-1 2s-1 1-2 1c-4-2-7-7-9-10z" class="Z"></path><defs><linearGradient id="y" x1="398.918" y1="117.684" x2="402.645" y2="105.021" xlink:href="#B"><stop offset="0" stop-color="#938e92"></stop><stop offset="1" stop-color="#a6a9a8"></stop></linearGradient></defs><path fill="url(#y)" d="M389 115l11-4c7-3 14-6 21-3l4 1-3 1c-3-1-5-1-7 0 0 1-1 2 0 3h-1c-2 1-4 0-6 1 1 1 2 2 3 2h-8-1-1l-1 1-1-1c-5 2-10 2-15 1v-1l3-1h2z"></path><path d="M389 115v1h2c2-1 4 0 6-2 1 0 2-1 3-1 1-1 4-2 5-2 0 1-3 2-4 3l1 1-1 1h0l-1 1-1-1c-5 2-10 2-15 1v-1l3-1h2z" class="G"></path><path d="M421 108l4 1-3 1c-3-1-5-1-7 0 0 1-1 2 0 3h-1c-2 1-4 0-6 1 1 1 2 2 3 2h-8-1-1 0l1-1 2-1s1 0 1-1h1c4-2 8-5 13-5h2z" class="g"></path><path d="M415 110c0 1-1 2 0 3h-1c-2 1-4 0-6 1l-1-1 8-3z" class="D"></path><path d="M232 270c0-1-1-2-2-2l-1 1h-1c0-2 1-3 2-5h0c3-3 8-7 13-7s9 2 13 5c1 0 0 0 1 1h1c-2 1-5 3-6 5l-1 2h0l-1-2v-1c0-1-3-5-4-5-1-1-2-1-3-1v1c1 0 1 1 2 1 0 1 1 1 1 2-2 0-3-1-4-1h-1-2l-1-1c-1 1-1 1-1 2h-1v1h-2c1 2 3 3 4 4l1 1h-1-3c-1 0-2-1-3-1z" class="d"></path><path d="M234 266s-1 0-1-1v-1c2 0 3-2 4-2 2 0 3 0 5 2h-1-2l-1-1c-1 1-1 1-1 2h-1v1h-2z" class="Q"></path><path d="M242 264c1 0 2 1 4 1 0-1-1-1-1-2-1 0-1-1-2-1v-1c1 0 2 0 3 1 1 0 4 4 4 5v1l1 2h-2c0-1-1-1-2-1l3 3 1 1v2 1l-1-1-3-3h-1v4h1l-1 1h0c-1 0-3 1-4 2l-1-1c-1 0-1 1-2 1s-2 1-2 1h-2-1-1c-1-1-2-1-3 0l-1-1h-2v-1c3 0 6-2 8-2h2s1 0 1-1c1 0 1 0 2-1-2 1-4 1-5 1 1-1 1-2 1-4h-1 0 3 1l-1-1c-1-1-3-2-4-4h2v-1h1c0-1 0-1 1-2l1 1h2 1z" class="S"></path><path d="M235 276h2v1s0 1-1 1c-1 1-1 1-3 1h0c1-1 2-2 2-3h0z" class="B"></path><path d="M236 266c2-1 2-1 4-1h1c1 1 1 1 1 2h0c1 0 1 0 2 1h-1c1 2 1 2 1 4-1 1-2 1-3 2h-1c-2 1-4 1-5 1 1-1 1-2 1-4h-1 0 3 1l-1-1c-1-1-3-2-4-4h2z" class="Q"></path><path d="M236 266c2-1 2-1 4-1l-1 1h-1v1 3c-1-1-3-2-4-4h2z" class="G"></path><path d="M239 271c1 0 1 0 2 1h0v-2h1 0c1-1 0-1-1-2h2c1 2 1 2 1 4-1 1-2 1-3 2h-1c-2 1-4 1-5 1 1-1 1-2 1-4h-1 0 3 1z" class="B"></path><path d="M449 353h1 1c0 1 0 2 1 3v2 1 1l1-1c2 4 2 9 4 13 0 2 1 5 2 7v2 1c1-1 1-2 1-3-1 0-1-1-1-1 0-1 0-1 1-2 0-1 0-1 1-2v2c0 1-1 1-1 2v1c1 0 1 1 2 2v-1h1v1c-1 1 0 1 0 2v1l-1-1v-1h-1c-1 2 1 4-1 6v-3-1h-1v3 3h-1 0l-1-11h-1 0v1c1 4 1 9 1 14 0 2-1 4-1 6h-1c-1 0-1 0-2-1l1-1c-1-1-1-2-2-3l-1-4h1v3h1v-3-1l1 1v2 1l1-1v-3-1h-1c0-4-1-8-1-11-1-4-4-10-3-13v-1c0-1-1-3-2-4s-1-1-1-2c0-2 0-2 1-3v1h0 1v-3z" class="c"></path><path d="M449 353h1 1c0 1 0 2 1 3v2 1 1c1 1 1 2 1 3v1h0c0 2 1 3 1 5v-1c-1-1-1-2-1-4v-1h0c0-1-1-1-1-2h0c0-1-1-3-1-4h-1c0 1 0 3 1 4 0 2 0 3 1 5 0 2 1 6 1 8h-1l-2-9v-1c0-1-1-3-2-4s-1-1-1-2c0-2 0-2 1-3v1h0 1v-3z" class="g"></path><path d="M454 389c0-4-1-8-1-11-1-4-4-10-3-13l2 9c1 2 2 3 2 5 1 3 1 7 2 10v5h1c0 2-1 4-1 6h-1c-1 0-1 0-2-1l1-1c-1-1-1-2-2-3l-1-4h1v3h1v-3-1l1 1v2 1l1-1v-3-1h-1z" class="s"></path><path d="M456 389v5h1c0 2-1 4-1 6h-1v-3c0-1 1-2 1-3v-5z" class="V"></path><path d="M306 622v-1c1-1 2-2 4-2 1 0 2 0 3 1s1 3 2 4c-1 2-2 4-4 4-8 3-15 3-23 3l-2-1h-3c0 1-1 1-1 1l-1-1 6-4c5-3 13-5 19-4h0z" class="d"></path><path d="M281 630l6-4c5-3 13-5 19-4 2 2 4 3 5 5h0c-2 0-3-2-4-3-5-3-14 1-18 3l-3 3h-3c0 1-1 1-1 1l-1-1z" class="m"></path><path d="M636 465c1-1 1-1 1-2 3-5 9-9 15-11 2 1 3 1 5 1h0c3 2 6 0 9 3v1h0c-3-1-5-1-8-2l-1 1h3c3 1 5 2 8 4-2-1-4-1-6-2-4-1-7-1-11 0 2 0 5 0 8 1 1 0 1 0 2 1h1 0-1c-1-1-2-1-2-1h-8c-2 0-4 0-5 1 1 1 2 0 4 0-1 1-4 2-5 2l1 1h1 0-1c0 1-1 1-2 2-2 0-3 2-5 4-1 3-3 6-1 10v1 2c1 1 2 2 2 3l1 1h0c-2-1-2-2-3-3l-1-1-1-1c1 1 1 2 1 2-1 1 0 1-1 1l-1-2c-2-3-3-6-2-10 1-2 2-5 3-7z" class="R"></path><path d="M641 462h0v-1c0-1 1-2 2-2h2v1l-1 1h-1l-2 1z" class="S"></path><path d="M639 469h0c-1 0-1 0-2-1h1c1-3 4-5 7-6l1 1h1 0-1c0 1-1 1-2 2-2 0-3 2-5 4z" class="h"></path><path d="M633 472l1 2h0c0-1 0-2 1-3 0-1 1-2 2-4 0 2-1 4-2 6 0 3 1 5 1 8 1 1 1 2 1 2-1 1 0 1-1 1l-1-2c-2-3-3-6-2-10z" class="O"></path><path d="M636 465c1-1 1-1 1-2 3-5 9-9 15-11 2 1 3 1 5 1h0c3 2 6 0 9 3v1h0c-3-1-5-1-8-2-1-1-7 0-9 0-1 1-1 1-2 1-1 1-2 2-4 3-1 0-2 1-2 2v1h0c-2 1-3 2-5 3z" class="M"></path><path d="M445 364h1c0 3 0 5 1 7h1v-1c0-1-1-3-1-4h1l4 13c1 3 0 7 2 10h1v1 3l-1 1v-1-2l-1-1v1 3h-1v-3h-1 0v4 1 1 3 1 1h0c-1 1-1 3-1 4h-1v-3h1v1-1c1-1 0-2 0-3h0v3h0s-1-1-1-2c0 0 0-3-1-3s-1-1-1-1h0l-1 1h0v1 1h-1s0-1 1-1c0-1 0-1-1-2l2-2 1 2h1c0-1-1-2-1-3h1v-1l-1-1v-1c-1 1-1 1-1 2-1-1-1-1-1-2h-1v2h-1c0 1 0 1-1 2 0-1 0-2-1-3 1-1 1-3 1-5 1 0 1-1 1-2h-1c0-2 0-3 1-5v-7-5c1-2 1-2 1-4z" class="q"></path><path d="M452 391c-1-2-1-4-1-6 2 2 1 5 2 6v3h-1v-3z" class="V"></path><path d="M451 383h-1v9h-1l-1-1v-1c1 0 1-1 1-1v-5l2-1z" class="W"></path><path d="M446 387c0-1 0-2 1-2v-1-5l1-1v-2l1 1c0 1-1 4-1 5h1 2v1l-2 1v5s0 1-1 1-1 0-1 1c-1-1 0-2 0-3l-1-1z" class="V"></path><path d="M445 364h1c0 3 0 5 1 7h1v-1c1 2 1 3 2 4v2c0 1 0 1 1 2v3 1h-2-1c0-1 1-4 1-5l-1-1v2l-1 1v5 1c-1 0-1 1-1 2-1 1-1 2-1 3 0 2 0 2-1 3 0 1 0 1-1 2 0-1 0-2-1-3 1-1 1-3 1-5 1 0 1-1 1-2h-1c0-2 0-3 1-5v-7-5c1-2 1-2 1-4z" class="c"></path><path d="M449 382v-3h1c0 1 0 2 1 2v1h-2z" class="r"></path><path d="M444 384l2-11v-1l1 1v2h0l1-1 1 1v2l-1-1v2l-1 1v5 1c-1 0-1 1-1 2-1 1-1 2-1 3 0 2 0 2-1 3 0 1 0 1-1 2 0-1 0-2-1-3 1-1 1-3 1-5 1 0 1-1 1-2v-1z" class="W"></path><path d="M444 384h1c0 2 0 4-1 6h1c0 2 0 2-1 3 0 1 0 1-1 2 0-1 0-2-1-3 1-1 1-3 1-5 1 0 1-1 1-2v-1z" class="V"></path><path d="M403 342c0-1 0-2 1-3v-1-1l1-1c0-1 1-3 1-4v-2h1v-2-2-2-1c1-1 1-1 1-2v-1-1l3-8v-2-1-1-1c1 1 1 3 0 4v1 3c-1 1-1 0 0 1h0 0v1l1-1v-2c1-1 1-1 2 0v3h-1v-1h-1v4 1l-1 1c1 0 0 0 1 1 0 1-1 0 0 1h0c1-2 1-2 2-3h0l1-1c0 4-1 6-2 10 0 1 0 1-1 3v1c0 2-1 3-2 5 0 2-1 5-1 7 0 1 0 1 1 2v1h1 0l-2 10c-1 1-1 2-1 3s0 2-1 3h0c-1-3 0-7 0-10v-1-4c-1 1-1 2-2 3 0-1 0-1 1-2v-1c-1-2 0-4 0-6v-2c1-1 0 1 1 0v-5h0v1c-1 1-1 0-1 1v2h-1v1h0c-1 1-2 5-3 6v-2l1-3z" class="v"></path><path d="M410 316h0 1c0 2-1 6-2 7h-1c0-2 1-5 2-7z" class="s"></path><path d="M408 337l2-9 1-1v4c0 2-1 4-2 7h0l-1-1v1-1z" class="e"></path><path d="M408 337v1-1l1 1h0c0 2-1 5-2 8v1l-1 2c-1-2 0-4 0-6h0l2-6z" class="n"></path><path d="M403 342c2-5 3-10 5-14v5c-1 2-3 5-3 8-1 1-2 5-3 6v-2l1-3z" class="s"></path><path d="M246 525c0-1 0-1-1-2 0-2 0-3 1-5 0-1 0-1 1-2h0c0-1 1-2 2-2h2c1 1 2 2 3 4 0 1 0 3-1 5h1l-2 3c-2 1-3 0-5 1v1c2 0 3 0 4 1 0 2 0 3-1 4l-3 3c-1 0-3 2-4 2s-1 0-2-1h0c0 1 0 1 1 2l-1 1c-1-1-1-1-2-1-1 1-1 1-2 1 0-2 0-2 1-4 0-1 1-2 1-3l1-1 5-4h0c-2 0-3 2-4 3l-1-4-1-1v-1c-1-1-1-2-1-3v-1c2-1 2-1 2-2l1-1c1 1 2 0 3 1v1 1 2 4h1 1v-1-1h0z" class="S"></path><path d="M239 526h1 2 1v1c-1 0-1 1-2 1l-1-1-1-1z" class="B"></path><path d="M238 522v-1c1 2 3 3 4 4v-2l2 2c0 1 0 1-1 1h-1-2-1v-1c-1-1-1-2-1-3z" class="O"></path><path d="M238 521c2-1 2-1 2-2l1-1c1 1 2 0 3 1v1 1 2h-1c-1 0-1-2-2-1l1 1v2c-1-1-3-2-4-4z" class="K"></path><path d="M246 525c0-1 0-1-1-2 0-2 0-3 1-5 0-1 0-1 1-2h0c0-1 1-2 2-2h2c1 1 2 2 3 4 0 1 0 3-1 5l-1-1h0c0-1-1-3-1-3l-1 1v1h0-1v-2h0l-1 1c0 1 1 1 1 2h2 0l-1 2c1 0 1 0 1 1h-1 0v-1c-1 0-1 0-2 1 0 0-1 0-2 1v-1z" class="D"></path><path d="M276 428l1 1h3c1 1 2 2 2 4v6 1c0 2-1 5-3 7h-1c-2 1-3 3-4 4-4 5-8 11-11 16-1 1-2 3-3 4l2-4 8-20-1-1 1-1c1-2 1-4 2-6l4-11z" class="d"></path><path d="M276 428l1 1-7 18-1-1 1-1c1-2 1-4 2-6l4-11z" class="L"></path><path d="M250 157c-1-3-2-5-3-7-1-3-3-5-6-8-9-10-22-17-35-21-1-1-5-2-6-3 0-1 0-1 1 0 4 0 8 2 12 3 2 1 4 3 6 3 1 0 3 1 4 2 4 3 8 5 12 8 2 2 4 3 5 4 3 3 5 6 7 8 3 5 5 11 7 16 0 2 1 4 1 6v5 3c1 0 1-1 1-1 3-3 5-6 9-8 5-2 10 2 16 1 2 0 4-1 5-3s1-4 0-6c0-2-1-3-3-4-3-2-7-2-10-1v-1h1c2-1 6-1 8 0 3 1 4 4 5 6s1 5-1 8c-1 2-3 3-5 4-2 0-3-1-5-1-2-1-5-2-7-3-6 0-11 7-13 11-3 3-4 7-5 11h0-1v-1c2-8 4-14 3-22-1-2-2-4-3-7v-2z" class="C"></path><path d="M290 509v-31c0-11-1-21 1-31 0 2-1 7 0 8h1c0 4-1 9-1 12v1c1 1 1 1 1 2v2c0 1 1 2 1 3h0-1v2 2 7 2 2 3h0v6 1c3 1 6 1 9 1h2c1 3 0 8 0 11v1 4c-1 0-1 0-1-1-1 0-1-1-2-1v-1c-1-2-3-5-6-6-1 0-2 0-3 1h-1z" class="o"></path><path d="M426 356v5l-1 1c1 2 1 3 1 5-1 2-1 5-2 7l2 1h0 1c-1-3-1-6-1-9 1 2 1 4 2 7 0-3 1-6 1-8s0-3 1-5v1 4c1-1 1-2 2-3v1c-1 4-1 8-1 12h-1c0 4-1 9-1 13v2 7h-1c-1-3 1-8 0-11l-1 1c0 1 0 1-1 1h0v5c-1 3 0 6-1 9v1h0c-1-1 0-2 0-4h0v-1c-1-1 0-3 0-5h0v-2l-1 1h0c-1 2 0 4-1 5 0-5 1-10 1-15h-1l-1 14c-1-3-1-7 0-10v-5l-2 10h-1l1-8v-3-1l1-1v-1c-1 1 0 1-1 2v2c-1 1-1 3-1 4l-1 5-1 2c0-6 1-12 3-18v1c2-1 2-5 2-6v-1c0-1 1-2 2-3 0 0 1-7 2-9z" class="V"></path><path d="M426 356v5l-1 1v4l-1 6h-1c0-2 0-4 1-7 0 0 1-7 2-9zm3 32l-1-1c0-5 0-12 1-17v-4h1v9c0 4-1 9-1 13z" class="i"></path><path d="M508 189c1 1 1 2 1 3l-1 1v1h2 0 1c1 1 2 1 2 2l1-1c1 0 1 0 2 1s2 2 3 4v1h-2-3c-1 0-1 1-1 1 0 3 2 5 4 7 1 1 1 1 1 2l1 1v1c-1 1-2 2-4 3l-1-1h-2v-1c0-2-3-3-4-5-3-2-9-5-10-9-1-1-1-4-1-5 2 0 2 0 3-1v1h1v1c1 1 2 1 3 2l2-1v-1-4c1-1 2-2 2-3z" class="d"></path><path d="M517 209c1 1 1 1 1 2l1 1v1c-1 1-2 2-4 3l-1-1h-2v-1l2-2c1-2 1-2 3-3z" class="N"></path><path d="M508 189c1 1 1 2 1 3l-1 1v1h2 0 1c1 1 2 1 2 2v1l-3 3-1-1v-1l-1 1c-1 0-2-1-2-2v-1-4c1-1 2-2 2-3z" class="R"></path><path d="M511 194c1 1 2 1 2 2v1h-1c-1 0-2-1-2-1v-2h1z" class="O"></path><path d="M292 477h5c2 0 3 0 5 1h0 0c1 0 1 1 1 2v1h0l1 1c1 2-1 5 2 5 0 1 0 1 1 1v1h0-1c-1 2-1 4-2 5v1c-1 0-2 1-3 1h-1l1 2h-1l2 2 1 1h-2c-3 0-6 0-9-1v-1-6h0v-3-2-2-7-2z" class="D"></path><path d="M292 490l2-2c1 0 1 1 1 1l1 2c-1 0-1 1-2 1h-1l-1 1v-3z" class="E"></path><path d="M297 477c2 0 3 0 5 1h0 0c1 0 1 1 1 2v1c-1-1-3 0-4 0h-2v-1c1 0 1 0 2-1 0-1-1-1-2-2z" class="X"></path><path d="M297 477c2 0 3 0 5 1h0v1l-1 1c-1-1-1-1-2-1h0c0-1-1-1-2-2z" class="K"></path><path d="M292 477h5c1 1 2 1 2 2-1 1-1 1-2 1l-1-1c-2 0-1 6-2 8h0c-1-2 0-6 0-8h-2v-2z" class="f"></path><path d="M303 481l1 1c1 2-1 5 2 5 0 1 0 1 1 1v1c-1-1-2-1-2-1l-1 2-1-1h-1s-1 0-1-1l-1 1-1 1h-1v-1h-1-1c-1-1-1-2-1-3h2s0 1 1 1h4c1-2 1-4 1-6z" class="F"></path><path d="M302 489c0-1 1-1 1-2h1l1 1-1 2-1-1h-1z" class="B"></path><path d="M305 488s1 0 2 1h0-1c-1 2-1 4-2 5v1c-1 0-2 1-3 1h-1c-1-1 0-2-1-4-1 0-1-1-1-2h1l1-1 1-1c0 1 1 1 1 1h1l1 1 1-2z" class="C"></path><path d="M300 489c1 0 2 1 2 1v1c-1 1-1 1-1 3v2h-1c-1-1 0-2-1-4-1 0-1-1-1-2h1l1-1z" class="D"></path><path d="M297 489h1v1c0 1 0 2 1 2 1 2 0 3 1 4l1 2h-1l2 2 1 1h-2c-3 0-6 0-9-1v-1-6l1 1h1 0 1 1c0-2 1-3 1-5z" class="U"></path><path d="M301 501l-1-1v-2l2 2 1 1h-2z" class="M"></path><path d="M297 489h1v1c0 1 0 2 1 2-1 1-2 2-2 3 0 0 0 1 1 1l-1 1h-2c-1-1-1-1-2-1h0c0 1 0 2-1 3v-6l1 1h1 0 1 1c0-2 1-3 1-5z" class="E"></path><path d="M451 567v6h6v-1c1 0 1 0 2 1 1 0 2-1 3-1 3 1 5 0 7 0v1 3h1l1 1h3l2 1s1 1 1 2-1 1-1 2h1v1h-2c0 1 0 0-1 1-1 0-2 0-3-1h1v-1h-3l-2-1h-2-7c0 1 0 1-1 2h-3c-1 1-1 1-2 1h-1-1c-1 0-3 0-5 1h0c-1 0-2 0-3-1h3v-1c-2-1-4-1-6-1v-1h5l1-1-1-1c1 0 2 0 3 1 1-1 2-1 2-1l-2-2c1-1 2-1 3-2l1-5v-3z" class="j"></path><path d="M453 578h3v1c0 1 0 1-1 1h-2v-1-1z" class="t"></path><path d="M449 579c1-1 3 0 4 0v1h-6c1-1 2-1 2-1z" class="W"></path><path d="M447 577c1-1 2-1 3-2v2c1 1 2 1 2 1h1v1c-1 0-3-1-4 0l-2-2z" class="c"></path><path d="M469 576h1l1 1-2 2h-2l-2-2h4v-1z" class="Q"></path><path d="M452 584l1-1c1-1 1-1 1-2 2-1 3 0 4 0 0 1 0 1-1 2h-3c-1 1-1 1-2 1z" class="I"></path><path d="M445 583h0l2-2c1 2 2 2 4 3h-1c-1 0-3 0-5 1h0c-1 0-2 0-3-1h3v-1z" class="l"></path><path d="M469 573v3 1h-4c-1 1-1 1-2 1v-2h-1c1-1 5 0 6-1l1-1v-1z" class="L"></path><path d="M451 574l1 1 10 1h1v2c-1-1-2 0-4 0h0 0-1c-1-1-4-1-6-1-1-1-1-2-1-3z" class="S"></path><path d="M474 577l2 1s1 1 1 2-1 1-1 2h1v1h-2c0 1 0 0-1 1-1 0-2 0-3-1h1v-1h-3l-2-1h-2c1-1 4 0 6-1l1-1h0l1-1 1-1z" class="I"></path><path d="M474 577l2 1s1 1 1 2-1 1-1 2h-4l-1-1c1-1 2-1 2-2h-1 0l1-1 1-1z" class="t"></path><path d="M451 567v6h6v-1c1 0 1 0 2 1 1 0 2-1 3-1 3 1 5 0 7 0v1 1l-1 1c-1 1-5 0-6 1l-10-1-1-1v-4-3z" class="U"></path><path d="M457 573v-1c1 0 1 0 2 1 1 0 2-1 3-1 3 1 5 0 7 0v1 1c-2 1-6 1-8 1v-2c-2 0-2 0-3 1h0-1v-1h0z" class="D"></path><path d="M275 494l1-1c1-1 2-1 2-1l1-1h0c1 0 1-1 3-1l1 1c0 1-1 2-1 2v2l1 1c1 1 0 2 0 3-1 0-2 1-3 2l-1 1v3l1 1c-1 2-1 3-2 3-1 1-2 1-2 2l-4 1c0-1-1-2-1-3l-1-1-3-2h-2l-1 1-1 2v-1-9l1-4c0-1 1-1 1-1 1 0 0 0 1-1 0 0 1-1 2-1 1-1 1-1 2-1l-2 2 1 1 1-1v2h2c1 0 1-1 2-1h1z" class="k"></path><path d="M270 503c2-1 2 0 3-1l1 1 2 1c-1 0-2 1-3 1s-2 0-3-1h1l-1-1z" class="I"></path><path d="M264 498h1c1-1 2-1 3-2v-1h1c0 1 0 2-1 3 0 1-1 2-1 3l-2 1h0v1h-1v-5z" class="Q"></path><path d="M265 494c1 0 0 0 1-1 0 0 1-1 2-1 1-1 1-1 2-1l-2 2 1 1 1-1v2h2-3-1v1c-1 1-2 1-3 2h-1c0-1 1-3 1-4z" class="S"></path><path d="M264 495c0-1 1-1 1-1 0 1-1 3-1 4v5 1c2 0 2-1 4 0 1 1 2 2 2 3v1l-3-2h-2l-1 1-1 2v-1-9l1-4z" class="e"></path><path d="M277 497c1-1 2-2 4-2 0 2 0 4-2 6l-3 3-2-1c0-1 0-1 1-1 0-1 1-1 1-2 1-1 1-2 1-3z" class="N"></path><path d="M279 502v3l1 1c-1 2-1 3-2 3-1 1-2 1-2 2-1 0-2-1-3-1-1-1-2-1-2-2 1-1 1-1 2-1 2-1 3 0 4-1 0-2 1-2 1-3l1-1z" class="m"></path><path d="M273 507h2c2 0 2-1 3 0 0 1-1 1-2 1 0 1 0 1-1 1s-1 0-2 1c-1-1-2-1-2-2 1-1 1-1 2-1z" class="T"></path><path d="M275 494l1-1c1-1 2-1 2-1l1-1h0c1 0 1-1 3-1l1 1c0 1-1 2-1 2l-1 2c-2 0-3 1-4 2 0 1 0 2-1 3 0 1-1 1-1 2-1 0-1 0-1 1l-1-1c-1 1-1 0-3 1 0 1 0 1-1 1v-1s1-1 1-2c-1 0-1 1-2 1v-4c1-1 1-2 1-3h3c1 0 1-1 2-1h1z" class="G"></path><path d="M275 494l1-1c1-1 2-1 2-1l1-1h0c1 0 1-1 3-1l1 1c0 1-1 2-1 2l-1 2c-2 0-3 1-4 2 0 1-1 1-2 2v-1c-1 1-1 1-2 1l1-1c0-1 1-1 1-2 1-1 0-1 0-2h0z" class="H"></path><path d="M493 581h2v1 4c0 1 0 1 1 2v1c1 1 2 2 2 3-1 1-1 1-2 1s-2 1-3 1l-3 1-3 1c-1 0-3 2-5 2l-1-1h-2c-1 1-2 1-3 1h-2c-1 0-2 0-3 1l-1-1h1c-1-1-1-1-2-1l-1-1v-1c1-1 1-1 2-1v-1c-1 0-1-1-2-1v-2l1-1v-1l8-1h0c3 0 5-1 7-1h1 4c2 0 3-1 4-1 1-1 1-1 1-2-1-1-1-1-1-2z" class="I"></path><path d="M485 594c1-1 3-1 4-1 1 1 1 1 1 2l-3 1c0-1-1-2-2-2z" class="N"></path><path d="M485 594c1 0 2 1 2 2-1 0-3 2-5 2l-1-1h-2c1 0 1-1 2-1v-1-1h2 2z" class="L"></path><path d="M495 589h1c1 1 2 2 2 3-1 1-1 1-2 1s-2 1-3 1l-1-1c0-1 1-1 1-2h0c-1 0-3 1-4 0-3-1-8 1-11 0l8-1h6 3v-1z" class="i"></path><path d="M493 591h1c1 0 1 1 2 1v1c-1 0-2 1-3 1l-1-1c0-1 1-1 1-2h0z" class="Z"></path><path d="M469 589l1 1v1c1 1 2 1 3 1 2 0 7-1 9 1-1 0-2 1-3 2h0c-2 1-3 1-5 1-1 0-2 1-3 2-1-1-1-1-2-1l-1-1v-1c1-1 1-1 2-1v-1c-1 0-1-1-2-1v-2l1-1z" class="N"></path><path d="M470 593h3 2v1h-5v-1z" class="B"></path><path d="M475 594h2c0 1 1 1 2 1-2 1-3 1-5 1-1 0-2 1-3 2-1-1-1-1-2-1l-1-1v-1c1-1 1-1 2-1h5z" class="G"></path><path d="M493 581h2v1 4c0 1 0 1 1 2v1h-1v1h-3-6c2 0 4 0 6-1h-1c-5 0-10 1-15 1h-4c-1 0-2-1-3-1v-1l8-1h0c3 0 5-1 7-1h1 4c2 0 3-1 4-1 1-1 1-1 1-2-1-1-1-1-1-2z" class="k"></path><path d="M493 581h2v1 4c0 1 0 1 1 2v1h-1c-1-1-3-2-5-2-4-1-9 1-14 0h1c3 0 5-1 7-1h1 4c2 0 3-1 4-1 1-1 1-1 1-2-1-1-1-1-1-2z" class="V"></path><path d="M493 581h2v1 4c-1 0-1 1-3 1v-1h-3c2 0 3-1 4-1 1-1 1-1 1-2-1-1-1-1-1-2z" class="W"></path><path d="M401 294l-1 1h1c1-1 0-1 1-1 1 1 0 2 0 3-1 1-1 2-1 3h0l-1 1v1h-1c0 1 0 1-1 1l-1 1v1c-1 1-2 2-3 4v-1c0-1 2-3 2-5 1-1 1-2 2-3-1 0-2 1-2 2l-1 1h0c-1 0-1 1-2 1v1l-1-1c1 0 1 0 1-1h0l1-1h-1 0-1l-2 2-1-1c1 0 1 0 1-1v-1h-2l-1-1h0l-2-2h-2c-1-1-1-2-3-3h0 0c-1 0-1-1-1-1h0c-1-1-1-1-1-2 0 0-1-1-1-2-1-1-2-1-3-2 0-2-1-2-1-4 1-1 2-3 4-4v2c-1 0-1 1-2 1 0 1-1 1-1 2 0 0 0 1 1 1 0 1 0 2 1 2h1l1 1h0v1c0 1 1 1 1 2v1h1c0 1 0 1 1 2l1 1v1h1c0 1 0 0 1 1 0-1-1-1 0-2v-1 1l-1-1v-2h0 0c-1 0-1-1-1-1h0 1l1-1 1-1v-1c0-1 0-2-1-3h0l-1-2h0c0-1 1-1 1-2l1 1c3-2 3 0 5 1h1l2 3 2 1 1 1c1 0 2-3 3-3v1c-1 1-1 2 0 3l2 2c-1 0-1 1-1 2h1z" class="n"></path><path d="M399 290l2 2c-1 0-1 1-1 2 0 3-3 4-5 7v-1h1c1-1 1-2 1-4h-1c-1 1-1 2-1 3l-1 1h-1c-1-1-1-2-1-3s0 0-1-1l1-1c1-1 1-2 1-3h1c0-1 1-1 1-1 0 1-1 2-1 3l2 2 2-5h1v-1z" class="q"></path><path d="M385 290v-1c0-1 0-2-1-3h0l-1-2h0c0-1 1-1 1-2l1 1c3-2 3 0 5 1h1l2 3 2 1 1 1c1 0 2-3 3-3v1c-1 1-1 2 0 3v1h-1l-2 5-2-2c0-1 1-2 1-3 0 0-1 0-1 1h-1c0 1 0 2-1 3l-1 1c0 1 0 2-1 2l-1-1h0-1v1h-1l2-6c-2 1-3 3-4 5h0v-1c0-1 1-3 1-4h0c-1 1-2 2-3 4l2-6z" class="r"></path><path d="M389 294h2v1l-1 1h-1v-2z" class="q"></path><path d="M522 258h0v1h2c1 1 1 2 1 2l1-1c0 1 2 3 3 3h1 0 4 0c0 2-3 5-4 6-1 0-2 0-2 1h-1 0 2c0 1-1 1-2 2h-4c-1 1-2 5-3 7 2 1 3 2 4 3h-2 0-1l-4 1h-3c-1 0-2-1-3-1v2h-2v-1c-1-1-2-1-3-2l-3-3h2 0c-1-2-1-2-2-3l-2-2 1-1 5-3h1c1 1 2 3 3 4 0 1 1 3 2 4s1 1 2 1c3-1 6-4 8-7l-3-4c-2 1-3 2-4 2-1 1-1 0-1 0h0c0-2 1-4 2-4l1-1v-1-1-1c1 0 1 0 1-1 1-1 2-2 3-2z" class="Q"></path><path d="M522 258h0v1h2c1 1 1 2 1 2l1 1v1-1h-3v1h0v2h1v-1h0v-1h1v3h-2l-1-1c0-2 0-2-2-2 0-1 0-1-1-1h-1v-1c1 0 1 0 1-1 1-1 2-2 3-2z" class="B"></path><path d="M501 273l1-1 5-3h1l-1 3h1c1 1 1 3 2 5h1v1h-2v2c1 1 1 1 1 3h-1c-1-1-2-1-3-2l-3-3h2 0c-1-2-1-2-2-3l-2-2z" class="F"></path><path d="M505 278c0 1 0 1 1 2l1-1h0v-4l-1-1c1 0 2 1 2 2 1 1 1 1 1 2h0v2c1 1 1 1 1 3h-1c-1-1-2-1-3-2l-3-3h2z" class="W"></path><path d="M273 154c3-1 7-1 10 1 2 1 3 2 3 4 1 2 1 4 0 6s-3 3-5 3c-6 1-11-3-16-1-4 2-6 5-9 8 0 0 0 1-1 1v-3c2-2 3-5 5-7 1-2 3-4 4-5 3-3 5-5 9-7z" class="d"></path><path d="M557 445h1c1 0 2 1 2 1h2 0c0 1 0 2 1 2v2c1-1 1-2 1-4v-1c1 0 1 0 1 1s1 3 1 5c0 1 0 2 1 3h1c0 1 1 2 1 4l3 15h0l-1-1c0 1-1 1-1 2v1l1 2 1 6h0c0 3 1 6 1 10v1c1 1 1 1 1 2h0v3 3 1 5l5 20c-1 1-1 1-1 2-2-4-2-9-3-13l-2-10c0-1 0-4-1-5v-1-6-1h-1c1-2-1-7-1-9v-1l-1-1v-2c-1-1-1 0-1-1s0-1-1-2v-1c0-1-1-1-1-2v-1c0-1 0-3-1-5v-1h0l-1-2h0l-2-2c0-1 0-1-1-1v-1-2h0l-1-1v-1c0-1-1-2-1-2v-3l-1-3v-1c0-1-1-3-1-4z" class="n"></path><path d="M564 446c1 1 1 2 1 3-1 3-1 5 0 7v1h-1c-1-1 0-2 0-3 0-2-1-3-1-4h0c1-1 1-2 1-4z" class="e"></path><path d="M561 462l1 1c1-1 1-2 2-3 0 1 0 2-1 3v1h0c3 3 3 8 4 12v2-1c0-1-1-1-1-2v-1c0-1 0-3-1-5v-1h0l-1-2h0l-2-2c0-1 0-1-1-1v-1z" class="v"></path><path d="M567 454h1c0 1 1 2 1 4l3 15h0l-1-1c0-1-1-3-1-4-1-5-3-10-3-14z" class="W"></path><path d="M291 509c1-1 2-1 3-1 3 1 5 4 6 6v1c1 0 1 1 2 1 0 1 0 1 1 1v-4-1 16c0 4 1 7 0 12h4v-2l1 1h0c1 1 2 1 3 1h0-11-1l-5-1h-3l1-1c1-2 0-5 0-6v-14-7l-1-2z" class="P"></path><path d="M299 523c0-1 0-2 1-3 1 1 1 1 1 2v17h-1c-1-1 0-2 0-4l-1-12z" class="m"></path><path d="M291 509c1-1 2-1 3-1 3 1 5 4 6 6v1c1 2 1 4 1 7 0-1 0-1-1-2-1 1-1 2-1 3 0-3 0-6-1-9h-1c-1 1 0 1-1 1h0c0-1-1-2-2-2h0v-1h0c-1 0-1 0-2-1l-1-2z" class="g"></path><path d="M294 512l2-1h1c0 1 1 2 1 3h-1c-1 1 0 1-1 1h0c0-1-1-2-2-2h0v-1z" class="T"></path><defs><linearGradient id="z" x1="302.314" y1="517.671" x2="293.663" y2="535.352" xlink:href="#B"><stop offset="0" stop-color="#a7a4a7"></stop><stop offset="1" stop-color="#c7c8c6"></stop></linearGradient></defs><path fill="url(#z)" d="M296 515c1 0 0 0 1-1h1c1 3 1 6 1 9l1 12c0 2-1 3 0 4v1h-1c-2-4-1-10-2-15 0-3 0-6-1-10z"></path><defs><linearGradient id="AA" x1="300.032" y1="519.258" x2="288.364" y2="530.6" xlink:href="#B"><stop offset="0" stop-color="#858584"></stop><stop offset="1" stop-color="#b0adb1"></stop></linearGradient></defs><path fill="url(#AA)" d="M292 511c1 1 1 1 2 1h0v1h0c1 0 2 1 2 2h0c1 4 1 7 1 10 1 5 0 11 2 15l-5-1h-3l1-1c1-2 0-5 0-6v-14-7z"></path><path d="M297 525c1 5 0 11 2 15l-5-1h1 1c2-2 0-11 1-13h0v-1z" class="H"></path><defs><linearGradient id="AB" x1="287.226" y1="521.276" x2="298.921" y2="532.675" xlink:href="#B"><stop offset="0" stop-color="#616266"></stop><stop offset="1" stop-color="#7e7c7a"></stop></linearGradient></defs><path fill="url(#AB)" d="M292 518v-1c0-1 0-1 1-2h1c1 2 0 4 1 6v18h-1-3l1-1c1-2 0-5 0-6v-14z"></path><path d="M421 299l1 14c1 3 1 5 1 7s0 2-1 3v2l-1-2c-1 1-2 3-2 5l-1 7v4l-3 13c0 3 0 5-1 8-1 1-1 2-1 3l-2 10h0c-1-2-1-2 0-4 0-2 0-4 1-6v-2c-1 0-1 1-1 2h0v-2-4c0-2 1-4 1-6v-4c1-1 1-2 0-3 0 1 0 3-1 4h0 0-1v-1c-1-1-1-1-1-2 0-2 1-5 1-7 1-2 2-3 2-5v-1c1-2 1-2 1-3 1-4 2-6 2-10 1-1 1-4 1-5 0-2-1-2-1-4l2 1c0 1 1 2 1 2h1v-1c0-1 1-2 0-3 1-2 1-6 1-9l1-1z" class="s"></path><path d="M418 313h1v1 4l-1-1v-4z" class="W"></path><path d="M415 329c0-2 0-5 2-8h0v3l-1 6-1-1z" class="v"></path><path d="M415 329l1 1c0 1 0 2-1 3l-3 18v-4c1-1 1-2 0-3 0 1 0 3-1 4h0 0l3-11c0-1 1-3 0-4 0-2 0-2 1-4z" class="n"></path><path d="M422 313c1 3 1 5 1 7s0 2-1 3v2l-1-2c-1 1-2 3-2 5l-1 7v4l-3 13h0l-1-1c0-1 1-3 1-3l2-9v-4c1-1 1 0 1-1h0v-3-2c0-3 1-6 2-8 0-2 0-3 1-4h1 0v-4z" class="V"></path><path d="M307 499h0v-2s0-1 1-2v-1c1-1 1-2 2-3v1 1c-1 0-1 2-2 3 1 2 0 3 1 4h3s0 1-1 1l1 38c-1-1-2-1-3-1l-1 1h0l-1-1v2h-4c1-5 0-8 0-12v-16c0-3 1-8 0-11l-1-1-2-2h1l-1-2h1c1 0 2-1 3-1h-1l1 1c0 2 1 3 1 5l1-2h1z" class="J"></path><path d="M306 499h1v22c-2-6-2-14-2-20l1-2z" class="F"></path><path d="M309 538v-5l1-9v-7-9c0-2-1-4 0-6 0-1 1-1 1-1l1 38c-1-1-2-1-3-1z" class="U"></path><path d="M304 495h-1l1 1c0 2 1 3 1 5 0 6 0 14 2 20-1 5-1 12 0 17v2h-4c1-5 0-8 0-12v-16c0-3 1-8 0-11l-1-1-2-2h1l-1-2h1c1 0 2-1 3-1z" class="S"></path><path d="M304 495h-1v3l-1 2-2-2h1l-1-2h1c1 0 2-1 3-1z" class="F"></path><path d="M303 498l-1 2-2-2h1 2z" class="L"></path><path d="M483 484l-1 3h0v2 1c0-1 1-2 1-2v2 1l-1 1-1 1v1c-1 1-1 1 0 3 0-1 1-2 1-3 1 1 0 2 0 3l-1 1h1v1l-1 1v1h0 0c0 2-1 3-1 5h0v-1h-1v-1h0v-1-3h-1c0 2 0 3-1 4l-1 1v-1l1-4v-2-1l1-1h0v-1l1-1s0-1-1-1v1l-1 1h0v-2c1-1 1-3 2-5h0-1c-1 2-2 4-2 7v1h-1v1l-2 2h0c0-2 0-5 1-7 2-2 2-4 3-6l5-11c1-1 1-2 2-2v-3h0v-1 1l-1 2c0 1-1 2-1 3-2 3-4 5-5 8 0 2-1 3-2 5v-1c1-4 3-7 4-11 1-2 1-4 2-5s1-2 1-4h0v-1-1c1 0 1 0 1-1v-2c-1-1 0-1 0-2l-1 2h-1 0-1v1 1 1c0 1-1 1-1 2v1 1 1l-1 1v1h0-1v-2s1-1 1-2h0v-3-2c1-2 1-3 1-5h-2l-1-1-1-1v-2c1-2 0-4 1-5v1c1 1 1 3 0 5h0c1 1 0 1 1 1v-1l1 1c0 1 0 0 1 1h0c1 2 0 5 0 8h0v-1c1-3 1-6 2-9h0v-2c0 1 1 1 1 1 0 1-1 6 0 7v-1-1c1 0 1 1 2 1l1 1c0 1 1 1 1 2-1 1-1 2-1 3h1 0c0-1 1-2 1-3h1v1h1c0-1 1-2 1-3v-3c2 2 0 5 3 6 0-2 1-3 1-4l1-1 1 1c1 2 0 3-1 5s-4 7-6 8h1l-1 1v-1l-1-2-3 5c1 0 1 1 2 1l1 2c-1 0-2 1-3 2 0 1 0 1-1 2h0v1h1c-1 0-1 0-1 1v1 1l-1-3h0z" class="u"></path><path d="M488 471c1-1 1-2 2-3h1c0 2-2 3-2 5h1l-1 1v-1l-1-2z" class="s"></path><path d="M485 476c1 0 1 1 2 1l1 2c-1 0-2 1-3 2 0 1 0 1-1 2h0v1h1c-1 0-1 0-1 1v1 1l-1-3h0c-1 1-3 6-4 8 0-3 1-8 3-10l3-6z" class="V"></path><path d="M302 475h2c1 0 2 1 4 0 0-1 1 0 2 0h3 0v1h2c0-1 0-1 1-1h0c1 0 2 0 2 1v1h1v-1h3l1 1h0c1 1 1 1 1 2-1 1-1 3 0 4h0v3 1c1 1 1 2 1 3v1h-1v-1h-1c0 1 0 1 1 2v1l-1 2-5-5h0-2-1-1c0 1-1 1-2 2s-1 2-2 4c0 1 0 1 1 2h0c1 0 0 0 1 1v1h-3c-1-1 0-2-1-4 1-1 1-3 2-3v-1-1c-1 1-1 2-2 3v1c-1 1-1 2-1 2v2h0-1l-1 2c0-2-1-3-1-5l-1-1h1v-1c1-1 1-3 2-5h1 0v-1c-1 0-1 0-1-1-3 0-1-3-2-5l-1-1h0v-1c0-1 0-2-1-2v-1h-1c0-1 0-1 1-2z" class="D"></path><path d="M309 500c0-1 0-1 1-2l2 1v1h-3z" class="M"></path><path d="M321 486h2c0 2 0 2-1 3h-1v-3z" class="E"></path><path d="M317 483v-1-2h2v3h-2z" class="b"></path><path d="M306 487v-2h-1l2-2v5c-1 0-1 0-1-1zm10-12c1 0 2 0 2 1v1h-2c0 1 0 1-1 0h0v-1c0-1 0-1 1-1h0z" class="B"></path><path d="M304 494c1-1 1-3 2-5h1c0 1-1 2-1 4h1v1h-1-2z" class="M"></path><path d="M309 488v2l-1 1v-1c0-2-1-4 0-6l1 1v-1h-1v-1l2-2-1 7z" class="U"></path><path d="M310 477l3 1c1 0 4 0 5 1h-1c-2 1 0 1-3 1 0 0-1-1-1-2l-1 1h-2 0v-2z" class="b"></path><path d="M306 494h1l-1 3v2l-1 2c0-2-1-3-1-5l-1-1h1v-1h2z" class="L"></path><path d="M306 494h1l-1 3-1-1c0-1 1-1 1-2z" class="N"></path><path d="M319 477v-1h3l1 1c-1 2-1 4-1 6l-1-2c-1-1-1-1-1-2s0-1-1-2z" class="H"></path><path d="M324 487c1 1 1 2 1 3v1h-1v-1h-1c0 1 0 1 1 2v1l-1 2-5-5h0-2v-1c1 0 1-1 2-1v2c2 0 3 2 4 3l1-3c0-1 0-2 1-2v-1z" class="U"></path><path d="M310 479h2c0 1 0 2 1 3v5c-2 0-3 1-4 1l1-7c0-1 0-1 1-1l-1-1z" class="K"></path><path d="M312 479l1-1c0 1 1 2 1 2 3 0 1 0 3-1h1 1v1h-2v2 1 3 2c-1 0-1-1-2 0v1h-1-1v-2-5c-1-1-1-2-1-3z" class="J"></path><path d="M302 475h2c1 0 2 1 4 0 0-1 1 0 2 0h3 0v1h-2l-1 1v2 1h-2l-1 1v2l-2 2h1v2c-3 0-1-3-2-5l-1-1h0v-1c0-1 0-2-1-2v-1h-1c0-1 0-1 1-2z" class="O"></path><path d="M302 477h6c-1 1-1 1-1 2v1h-2-2c0-1 0-2-1-2v-1z" class="B"></path><path d="M305 480h-1c1-1 2-1 3-1v1h-2z" class="T"></path><path d="M302 475h2c1 0 2 1 4 0 0-1 1 0 2 0h3 0v1h-2l-3 1h-6-1c0-1 0-1 1-2z" class="L"></path><path d="M395 254l6 3h0l7 1h2c-1 1-2 1-2 2 1 2 2 3 4 4v1c1 0 2 0 3 1v1h-1c1 1 2 2 2 3l2 3c2 1 4 3 5 5h-2c0-1-1-2-2-3v1c0 1 1 2 2 3h0c0 1 0 1-1 2 1 1 1 2 1 4v1h0v3c0 1 0 0 1 1v2l-1 1h0v-1c0-1-1-5-2-6v4c-1-1-1-3-1-4l-2-1h-1v5l-1-1v-5 1c-1 1-1 2-1 4h0c-1 1-1 2-1 3 0 3 1 6 0 8v1-3c0-3-1-5-1-8v-1-1l1-2v-4l-2 4h-1v-1-2-2l1-1v-2c0-1-1-1-1-2-2 0-2 1-3 1h-1v-3l-1 1h0v-2h0l-1-4-1-3-1-3c-1-2-3-5-6-7v-1-1z" class="V"></path><path d="M412 264v1c1 0 2 0 3 1v1h-1c-1 0-1 1-2 1v-1c-1 0-1-1-2-1v-1l2-1z" class="r"></path><path d="M403 262c2-1 3-1 4 0l1 1c1 1 0 1 0 3l1 1 2 1h-1c-1 1-1 2-1 3v1 1-1h-1l-2 2-2-1h0v-1 1h2l1-1-1-1h2c-2-1-2-3-3-4 1-1 1-2 1-3-1 0-2-1-2-2h-1z" class="W"></path><path d="M409 267v1c0 1 0 1-1 1 0-1-1-2-1-3h1l1 1zm-14-13l6 3h0v1c0 2 2 3 2 4h1c0 1 1 2 2 2 0 1 0 2-1 3 1 1 1 3 3 4h-2l1 1-1 1h-2v-1 1l-1-4-1-3-1-3c-1-2-3-5-6-7v-1-1z" class="c"></path><path d="M412 268c1 0 1-1 2-1 1 1 2 2 2 3l2 3c2 1 4 3 5 5h-2c0-1-1-2-2-3v1c0 1 1 2 2 3h0c0 1 0 1-1 2 1 1 1 2 1 4v1h0v3c0 1 0 0 1 1v2l-1 1h0v-1c0-1-1-5-2-6v4c-1-1-1-3-1-4l-2-1-1-1c0-1 0-3-1-4h0c-1-1-1-2 0-3-2-1-2-2-3-3v-4h0 1c0 1 0 2 1 2h1v-2c-1 0-1-1-2-1v-1z" class="i"></path><path d="M418 275h1 0v1c0 1 1 2 2 3h0c0 1 0 1-1 2v-2h-2v-1-3z" class="t"></path><path d="M416 270l2 3c2 1 4 3 5 5h-2c0-1-1-2-2-3h0-1l-1-1-2-2h0c0-1 1-1 1-2z" class="k"></path><defs><linearGradient id="AC" x1="538.114" y1="455.507" x2="547.674" y2="410.595" xlink:href="#B"><stop offset="0" stop-color="#1d1c1d"></stop><stop offset="1" stop-color="#414142"></stop></linearGradient></defs><path fill="url(#AC)" d="M534 410c0-1 1-1 1-2 2 1 2 3 3 4 1 0 1 0 2 1h1c2 2 3 3 3 5l-1 1h0 0 0c0 1 0 2 1 3v2c0 1 1 1 1 2 1 2 1 4 2 6 0 1 1 4 2 5h0c0 1 0 2 1 2 0 2 2 3 2 4l2 3h-1c0-1 0-1-1-2h0c-1-1-1-2-2-3v1 1c0 1 1 2 0 3h0c0 1 0 1 1 2v-1 1 1 2c0 1 0 1 1 1v2c0-1 0-1-1-1l2 4c0 1 1 2 1 3h1v1 1c1 0 1 1 1 1 0 1 0 1 1 1l1 3v1h1v1c0 1 0 1 1 2v2c1 1 1 1 1 2l-1-1h-1v-1c-1-3-3-6-5-9 0-1-1-2-1-3-1-1-1-1-1-2h0l-1 1v1c1 1 0 0 0 1h0l-3-5v-1-1c-1 0-1-1-1-2s-1-1-1-2v2-1l-4-6v-1c-1-1-1-2-1-3-1 0-1-1-1-1v-1c-1-1 0-2-1-3v-1 1h1 0v-3h0c-1-3-1-5-3-8h0 1c2 0 3 1 4 1 0-1-1-1-1-2h0-1c0-1-1-1-1-2h-1v-2-3c0-1 0-1-1-2v-2c-1-1-1-1-1-2l-2-2z"></path><path d="M544 440s0 1 1 2v3c0-1-1-2-1-3-1-1-1-1 0-2z" class="V"></path><path d="M538 418c0-1 0-1-1-2v-2c4 4 5 10 6 15 1 1 2 3 2 4h-1l-2-6c0-1-1-1-1-2h0-1c0-1-1-1-1-2h-1v-2-3z" class="c"></path><path d="M419 328c0-2 1-4 2-5l1 2v3h1c0 1 1 4 2 5s0 4 0 6 0 3-1 5c0 1 0 2-1 4v2c-1 2-1 4-2 6v1h0c-1 3-1 5-1 7l-6 23c0 2 0 3-1 5-1 0-1-1-2-1 0-1 0-2-1-2v-1-1-1l4-19v-1c-1-1-1-2-1-3s0-2 1-3c1-3 1-5 1-8l3-13v-4l1-7z" class="V"></path><path d="M419 328c0-2 1-4 2-5l1 2v3h1c0 1 1 4 2 5s0 4 0 6 0 3-1 5c0 1 0 2-1 4-1-4 2-7 1-10-1 0-1-1-2-2 0 1 0 2-1 3l-1-1c1-1 1-1 1-2 1-2 1-4 1-6-1-1-1-2-1-4l-1 2h-1z" class="s"></path><path d="M420 328l1-2c0 2 0 3 1 4 0 2 0 4-1 6 0 1 0 1-1 2l1 1c-2 6-3 12-5 18 0 3-2 7-2 10v-1c-1-1-1-2-1-3s0-2 1-3c1-3 1-5 1-8l3-13v-4l1-7h1z" class="u"></path><path d="M420 328l1-2c0 2 0 3 1 4 0 2 0 4-1 6 0 1 0 1-1 2l-1 5v1h-1v-4c2-4 2-8 2-12z" class="r"></path><path d="M410 386l1-1c1-1 1-3 1-3l3-14 2-10c1-4 2-8 4-11h0c0 4-2 10-3 14l1 1 2-6h0v1c-1 3-1 5-1 7l-6 23c0 2 0 3-1 5-1 0-1-1-2-1 0-1 0-2-1-2v-1-1-1z" class="u"></path><path d="M419 362c0 2-2 4-2 6-1 3-1 8-2 11l-1-1c0-1 0-2 1-4v-4l3-9 1 1z" class="e"></path><path d="M425 109c7 3 11 7 14 14 2 5 6 12 4 17l-1 1c-1 0-2-1-4-2v-1l1 1h0c-1-2-2-5-3-7s-2-3-4-4c-1-1-1-1-2-1-1-3-3-4-6-5h-2l-1-1h-1-2l-2-2h0c1 0 2 0 3 1h1 0 1l-1-1c-1 0-2-1-3-1l-6-2c-1 0-2-1-3-2 2-1 4 0 6-1h1c-1-1 0-2 0-3 2-1 4-1 7 0l3-1z" class="H"></path><path d="M414 113v1 1c2 1 3 1 4 1 3-1 4 0 6 0s2 0 4 1h0l1 1 1 1v1s1 1 1 2c1 1 2 2 3 2l1 2 3 3h0 0l-3-3c-1 0-1-1-2-1h-1c-3-2-6-4-10-4v-1h-2 1l-1-1c-1 0-2-1-3-1l-6-2c-1 0-2-1-3-2 2-1 4 0 6-1z" class="G"></path><path d="M424 116c2 0 2 0 4 1h0l1 1 1 1v1c-2-1-4-2-6-4z" class="B"></path><path d="M425 109c7 3 11 7 14 14v2c-2 0-4-2-5-3s-2-2-4-3l-1-1-1-1h0c-2-1-2-1-4-1s-3-1-6 0c-1 0-2 0-4-1v-1-1h1c-1-1 0-2 0-3 2-1 4-1 7 0l3-1z" class="F"></path><path d="M418 116l1-2h1 2 0c1 1 2 1 3 1 2 0 3 1 5 2-1 0-1 0-1 1l-1-1h0c-2-1-2-1-4-1s-3-1-6 0z" class="U"></path><path d="M415 110c2-1 4-1 7 0 1 0 3 1 4 1 1 1 4 2 4 4h0-1c-2-1-3-1-5-1-1 0-3-1-4-2-1 0-2 0-3 1-1 0 0 1-2 0-1-1 0-2 0-3z" class="E"></path><path d="M426 336c1 2 1 4 2 5h1l1-1h0 0l1 1v2-2c1-1 1-2 2-3h1l1 2v1h0v5 1 3 2h-1v1c1 0 1 0 1-1v2 1c-1 1-1 5-1 5l-1 6c-1 2-1 4-1 7v7l-1-1v-4c0-4 0-8 1-12v-1c-1 1-1 2-2 3v-4-1c-1 2-1 3-1 5s-1 5-1 8c-1-3-1-5-2-7 0 3 0 6 1 9h-1 0l-2-1c1-2 1-5 2-7 0-2 0-3-1-5l1-1v-5c-1 2-2 9-2 9-1 1-2 2-2 3v1c0 1 0 5-2 6v-1c-2 6-3 12-3 18l-1 1h0 0v-1-1-1-2c1-1 1-1 1-2h-1l1 1-1 1v2l-1 1h0v1c0 2-2 4-2 6v1 6l-1 1c0-4-1-10 1-13v-1c1-2 1-3 1-5l6-23c0-2 0-4 1-7h0v-1c1-2 1-4 2-6v-2c1-2 1-3 1-4 1-2 1-3 1-5l1-3z" class="W"></path><path d="M429 350s1 0 1 1v3h0l-1-2v-2h0zm-3 6l1-1c0 2 0 3 1 5-1 1-1 1-2 1v-5z" class="e"></path><path d="M430 361l1-1v-4l1-1v7c-1 1-1 2-2 3v-4z" class="c"></path><path d="M434 342l1-1h0v5 1 3 2h-1v-10z" class="k"></path><path d="M425 343c1 1 1 4 1 6v1c0 1 0 1-1 2h-1c0-3 1-6 1-9z" class="s"></path><path d="M433 338h1l1 2v1h0 0l-1 1-1-1v5 7 1c-1-1-1-3-1-4v-5l-1-4c1-1 1-2 2-3z" class="c"></path><path d="M432 363c0-1 1-3 1-4v-4c1 1 1 3 1 5l-1 6c-1 2-1 4-1 7v7l-1-1v-4c0-4 0-8 1-12z" class="k"></path><g class="V"><path d="M425 343l1-1h0l1 1c1 2 1 4 1 7h1v2h-4c1-1 1-1 1-2v-1c0-2 0-5-1-6z"></path><path d="M430 340h0 0l1 1v2-2l1 4v5c-1-1-1-1-2-1v2c0-1-1-1-1-1h0-1c0-3 0-5-1-7v-1h2v-1l1-1z"></path></g><path d="M430 340v2c-1 2 1 4 0 6 0 0-1 1-1 2h0-1c0-3 0-5-1-7v-1h2v-1l1-1z" class="i"></path><path d="M416 387c0-3 1-6 2-9 1-4 1-9 3-13 0-2 1-4 1-6 0-1 0-2 1-3 0 2 0 2-1 3v5l-2 10c-2 6-3 12-3 18l-1 1h0 0v-1-1-1-2c1-1 1-1 1-2h-1l1 1-1 1v2l-1 1h0v1c0 2-2 4-2 6v1 6l-1 1c0-4-1-10 1-13v-1c1-2 1-3 1-5h2z" class="v"></path><path d="M413 392c1-2 1-3 1-5h2l-2 5-1 1v-1z" class="e"></path><path d="M465 581h2l2 1h-3l2 2h3l1 1h0 1v1h1c1 0 2 0 3 1l-8 1v1l-1 1v2c1 0 1 1 2 1v1c-1 0-1 0-2 1v1l1 1-3-1c-1 0-1 0-1-1l-5 1h-1-4-3-3 0c1-1 2-1 3-1h-5l-5-1h-6-3s0 1-1 1h0c0-2 0-2 1-3l1-1-1-1v-2h2v-1-1l-1-1h4 3 1v-1c1 1 2 1 3 1h0c2-1 4-1 5-1h1 1c1 0 1 0 2-1h3c1-1 1-1 1-2h7z" class="L"></path><path d="M459 593h-1c0-1 1-1 1-2h1l1 1-2 1z" class="S"></path><path d="M462 588c1 0 2 1 3 0v2c-2 0-2 0-4-1l1-1zm3 7h3v1l1 1-3-1c-1 0-1 0-1-1h0z" class="N"></path><path d="M465 588h4v1l-1 1h-3v-2z" class="g"></path><path d="M455 588l5-1c-1 1-1 1-1 2l-1 1c-1-1-2-2-3-2z" class="H"></path><path d="M473 586h1c1 0 2 0 3 1l-8 1h-4c-1 1-2 0-3 0h1 1v-1h-3c3-1 8-1 12-1z" class="T"></path><path d="M461 592h1l1 1c1 1 1 1 2 1v1h0l-5 1h-1-4-3-3 0c1-1 2-1 3-1h3c1 0 2 0 4-2l2-1z" class="F"></path><defs><linearGradient id="AD" x1="453.225" y1="590.994" x2="432.947" y2="596.022" xlink:href="#B"><stop offset="0" stop-color="#8b8a8a"></stop><stop offset="1" stop-color="#c1c0bf"></stop></linearGradient></defs><path fill="url(#AD)" d="M440 588h3 0c-1 1 0 1-1 1h2c1 1 1 1 2 1h7 0l-2 1v1c1 0 1 1 2 1h1c0 1 0 1 1 2h-3-5l-5-1h-6-3s0 1-1 1h0c0-2 0-2 1-3l1-1-1-1v-2h2 1 1 3z"></path><path d="M440 588h3 0c-1 1 0 1-1 1l-1 1c0 1-1 2-2 3h-1-2c-1 0-2 0-3-1l1-1-1-1v-2h2 1 1 3z" class="C"></path><path d="M440 588h3 0c-1 1 0 1-1 1l-1 1h-4v-1c1 0 2 0 3-1z" class="B"></path><path d="M435 588h1c0 1-1 1-1 1l1 2c1 0 1 1 2 2h-2c-1 0-2 0-3-1l1-1-1-1v-2h2z" class="D"></path><path d="M465 581h2l2 1h-3l2 2h3l1 1h0 1v1c-4 0-9 0-12 1h-1l-5 1h-4-1c-1 0-1 0-2 1h-4-2c1 0 0 0 1-1h0-3-3-1-1v-1-1l-1-1h4 3 1v-1c1 1 2 1 3 1h0c2-1 4-1 5-1h1 1c1 0 1 0 2-1h3c1-1 1-1 1-2h7z" class="G"></path><path d="M446 586c1 0 2-1 3 0l-1 1h-6c1-1 1 0 1-1h3z" class="j"></path><defs><linearGradient id="AE" x1="440.715" y1="583.606" x2="438.392" y2="587.771" xlink:href="#B"><stop offset="0" stop-color="#909191"></stop><stop offset="1" stop-color="#aeabac"></stop></linearGradient></defs><path fill="url(#AE)" d="M438 585h3 1v-1c1 1 2 1 3 1l1 1h-3c-2 0-4 1-6 2h-1-1v-1-1l-1-1h4z"></path><path d="M465 581h2l2 1h-3l2 2h3l1 1h0l-10 1c-2 0-4 1-6 0-1 0-2-1-3-1s-2 0-3-1h1 1c1 0 1 0 2-1h3c1-1 1-1 1-2h7z" class="T"></path><path d="M464 583l1-1h1l2 2h-3l-1-1z" class="N"></path><path d="M465 581h2l2 1h-3-1l-1 1h-7c1-1 1-1 1-2h7z" class="G"></path><path d="M264 375l1 1c2 0 4 0 6 1v2h2c1 0 3 2 4 4 1 1 2 3 2 5h-1c0 1-1 2-1 3h1c0 1-1 3-1 5h0v1 3h-1c0 1 1 1 1 2h0l1 2-2 2c-1 1-3 1-4 2h-2v1c-1-1-1-1-2 0h-1l-1-1-1 1h-1c0-1-1-2-2-3v-2l-1-1c-1-1-1-2-2-3v-1c2-1 3 1 5 1v-1c-1 0-2-1-3-2-2 1-2 0-4 0l-2-3v-1c0-1-1-1-2-2v-1l1-1v-1-1c1-1 1-2 2-2 0 1 0 1 1 1v1h1 3c1 1 2 1 4 2l1-1h0l2-2v-1c1 0 1 0 2 1h1v-1c0-3-2-5-4-6l-2-2-1-2z" class="W"></path><path d="M268 386l2 1c0 1 0 1-2 1h-2l2-2z" class="K"></path><path d="M273 392l2-1c1-1 1-1 2 0-1 2-1 4-2 5l-2-1 1-1c-1-1 0-1-1-1v-1h0z" class="D"></path><path d="M272 384l2 2c0 1 0 1-1 2 1 0 1 1 2 1-1 1-1 1-2 1h0v2h0v1c1 0 0 0 1 1l-1 1h0c-2-2-3-3-4-5 2-2 2-3 3-6z" class="C"></path><path d="M270 408c-1 0-3-1-4-2l8-6c0-1 1-1 1-2h1v2c0 1 1 1 1 2h0l1 2-2 2c-1 1-3 1-4 2h-2 0z" class="T"></path><path d="M270 408l1-2h-1-1 0 0-1v-1c1 0 1-1 2-1h2 0c1 0 1 0 1 1 1-1 2-1 3-2 0-1 0-1 1-1l1 2-2 2c-1 1-3 1-4 2h-2 0z" class="B"></path><path d="M265 376c2 0 4 0 6 1v2h2c1 0 3 2 4 4 1 1 2 3 2 5h-1c0 1-1 2-1 3h0c-1-1-1-1-2 0l-2 1v-2h0c1 0 1 0 2-1-1 0-1-1-2-1 1-1 1-1 1-2l-2-2v-2l-3-3-3-3h-1z" class="H"></path><path d="M253 390l1-1v-1-1c1-1 1-2 2-2 0 1 0 1 1 1v1h1 3c1 1 2 1 4 2l3 1c0 1 1 1 1 2l1 1c0 1 1 1 1 2l-1 1h-1 0 3c1 0 2 1 2 2l-1 1h1c-1 1-1 1-2 1l-3 3c-1 0-3 1-4 2h-2c0 1 1 1 2 1 0 1 1 1 1 2l-1 1h-1c0-1-1-2-2-3v-2l-1-1c-1-1-1-2-2-3v-1c2-1 3 1 5 1v-1c-1 0-2-1-3-2-2 1-2 0-4 0l-2-3v-1c0-1-1-1-2-2v-1z" class="T"></path><path d="M258 387h3l-1 1c1 1 1 2 1 2-1 1-1 1-2 1l-2-1c0-1 1-2 1-3z" class="M"></path><path d="M261 387c1 1 2 1 4 2l3 1c-1 1-1 1-2 1l-1 1h-1l-1-3s-1 1-2 1c0 0 0-1-1-2l1-1z" class="D"></path><path d="M268 390c0 1 1 1 1 2l1 1c0 1 1 1 1 2l-1 1h-1c-1-1-2-2-3-2l-3-1v-1h1 1l1-1c1 0 1 0 2-1z" class="B"></path><path d="M268 390c0 1 1 1 1 2l-1 1c-1 0-1-1-2-1h-1l1-1c1 0 1 0 2-1z" class="K"></path><path d="M253 390c1 1 3 1 4 1 1 1 1 1 2 1l4 2h1c1 1 1 1 2 1h1v1h0l-1 1c-1 0-1-1-2-1 0 0 0 1-1 1v-1l-1 1h0c-1 0-1-1-2-1l-2-1c0 1 0 1-1 2l-2-3v-1c0-1-1-1-2-2v-1z" class="M"></path><path d="M260 396l2-2v1l1 1-1 1h0c-1 0-1-1-2-1z" class="f"></path><path d="M257 397c1-1 1-1 1-2l2 1c1 0 1 1 2 1h0l1-1v1c1 0 1-1 1-1 1 0 1 1 2 1l1-1c1 1 2 1 3 1v1h-2c1 1 2 1 2 2h-3-1v3l-1 1h-1v-2-1c-1 1-1 1-1 2h-1 0 0c-1-1-1-1-1-2l-2-1v-1c2-1 3 1 5 1v-1c-1 0-2-1-3-2-2 1-2 0-4 0z" class="K"></path><path d="M519 213c2-1 6-3 8-3 3 1 6 4 8 7v1c1 1 1 2 2 2h0c0 1 1 2 0 2 0 1 0 2 1 2 0 2 0 5-1 7 0-1 0-1-1-1-2 9-5 18-12 26v-1c2-5 3-12 1-17 0-5-3-13-6-17-1 0-1 0-2 1l-3-3c1-1 1-2 2-2 0-1-1-1-1-1 2-1 3-2 4-3h0z" class="d"></path><path d="M516 217c1 1 2 3 3 4-1 0-1 0-2 1l-3-3c1-1 1-2 2-2z" class="L"></path><path d="M534 223h1c0 1 1 3 0 4v1c-1 1-1 2-3 4h-1c0-1-1-2 0-3 0-3 1-4 3-6z" class="O"></path><path d="M519 213c2-1 6-3 8-3 3 1 6 4 8 7v1c1 1 1 2 2 2h0c0 1 1 2 0 2 0 1 0 2 1 2 0 2 0 5-1 7 0-1 0-1-1-1 0-2 1-4 0-5 0-2 0-2-1-3-2-5-4-7-7-10l-1 1 1 1v2l-2 2h-1c-2-1-5-3-6-5z" class="B"></path><path d="M527 213l1 1v2l-1-1h-1v-2h1z" class="M"></path><path d="M243 402c1 1 2 3 3 4l3 3c3 3 7 7 9 10v1h-1l1 1-1 1c-1 3-5 8-8 8-5 2-11 0-15-3-2-2-3-3-3-6v-4c1-1 0-3 0-4h1c1 0 1-1 2-1h1c0-1 1-1 2-2v-1h1c1 0 1-1 2-2l7 2c-2-2-4-4-5-6v-1h1z" class="d"></path><path d="M249 409c3 3 7 7 9 10v1h-1l1 1-1 1-1-1v-1c-1-2-5-6-8-7-2 0-5 0-7 1h-3v-1l2-1h1c2 0 6 0 7-2l1-1z" class="G"></path><path d="M231 417c1-1 0-3 0-4h1c1 0 2 1 2 1 1 1 1 1 2 1l1 1v2l1 1v3l-2 2h0-1 0-1c-2-1-1-1-2-2l-1-1v-4z" class="L"></path><path d="M234 414c1 1 1 1 2 1l1 1v2h-1-1-1 0v-1-1h0v-1-1zm4 5v3l-2 2h0-1 0c-1-1-1-2-1-2v-1h0c1-1 2 0 3 0 1-1 0-1 1-2z" class="G"></path><path d="M243 402c1 1 2 3 3 4l3 3-1 1c-1 2-5 2-7 2h-1-1 0c-1 0-1 0-1-1-1 1-2 2-2 3l1 2-1-1c-1 0-1 0-2-1 0 0-1-1-2-1 1 0 1-1 2-1h1c0-1 1-1 2-2v-1h1c1 0 1-1 2-2l7 2c-2-2-4-4-5-6v-1h1z" class="I"></path><path d="M238 411c1-1 3-2 5-2 1 0 4 1 5 1-1 2-5 2-7 2h-1-1 0c-1 0-1 0-1-1z" class="x"></path><path d="M246 428h3c2 0 4-3 6-5 0-1 0-1 1-2h0l1 1c-1 3-5 8-8 8-5 2-11 0-15-3-2-2-3-3-3-6l1 1c1 1 0 1 2 2h1 0 1 1c1 0 2-1 3-1 2 2 4 4 6 5z" class="N"></path><path d="M237 424c1 0 2-1 3-1 2 2 4 4 6 5h-1-1c-1 1-2 0-3 0-1-1-2-2-2-3h0c-1-1-1-1-2-1z" class="R"></path><path d="M478 135h1l1-1c2-1 5-3 8-3h1c0 2 1 2 2 3h3l1 1h-1v-1l-2 1c0 1 0 2 1 3l1 1c1 0 2 0 4 1l2 1c0 1 0 1-1 2h0l1 1-2 6-2 3v-1h-1c0 1 0 1-1 1l-1-2-2-1c0 1-1 1-1 2s0 2-1 2c-1 1-1 2-2 3-2-1-3-2-4-2-2 0-2 0-3-1h0-1c-1 1-3 1-4 1v-1l1-1v-1c-1-1 0-1 0-1 0-1-2-1-2-2v-1h0l1-1h0c-1 1-1 1-2 1-1-1-1-1-2-1 0 0-1-1-1-2h0v-2h-1c1-2 1-2 2-3s1-1 1-2l4-2h1 0l1-1z" class="Z"></path><path d="M489 136h1c1 1 2 2 2 4 0 1 0 1-1 1 0 0-1-1-2-1h0 0c-1-1-2-1-2-1-1-1-1-1 0-2 0 0 1-1 2-1z" class="X"></path><path d="M487 137s1-1 2-1l1 1s-1 1-1 2c-1 0-2-1-2-1v-1z" class="K"></path><path d="M488 146c2 0 3 0 5 1v1h-1v1h1 2c0 1 1 2 1 3h-1c0 1 0 1-1 1l-1-2-2-1-1-1c-1-1-1-1-1-2l-1-1z" class="D"></path><path d="M491 134c-2 1-4 1-5 2h-1-1l-1-1c-1 1-1 1-2 1v-1c1-1 2-2 4-2l3-2h1c0 2 1 2 2 3zm8 9l-3 7c0-1-1-1-1-2-1-1-1-1-2-1 0-1-1-1-1-2 0-2 0-3 2-5h1c1 1 0 1 1 2l1-1h1l1 2h0z" class="b"></path><path d="M487 139s1 0 2 1h0 0c1 1 1 2 2 2v2c-1 1-3 2-4 2h-1c0-2 1-2 1-4 0 0-1 1-2 1 0 2-1 2 0 4l-2 2c-1-2-1-1-2-1l-1-2 2-1c-1 0-1-1-1-2-1 0-1 0 0-1h0c1 0 1 0 2-1 0-1 1-1 2-1l1 1h1v-2z" class="E"></path><path d="M482 145c0 1 1 1 2 1v-1c0-1-1-1-1-2h0 2c0 2-1 2 0 4l-2 2c-1-2-1-1-2-1l-1-2 2-1z" class="B"></path><path d="M476 145l2 2h2v1h1c1 0 1-1 2 1 0 1 0 2-2 2h0l1 2c-1 0-1 0-2 1h0-1c-1 1-3 1-4 1v-1l1-1v-1c-1-1 0-1 0-1 0-1-2-1-2-2v-1h0l1-1h0c0-1 1-1 1-2z" class="F"></path><path d="M478 147h2v1 3h-1 0v-1c0-1-1-2-1-3z" class="D"></path><path d="M476 152l1-1h0 1 3l1 2c-1 0-1 0-2 1h0-1c-1 1-3 1-4 1v-1l1-1v-1z" class="B"></path><path d="M486 148c1-1 2-1 2-2l1 1c0 1 0 1 1 2l1 1c0 1-1 1-1 2s0 2-1 2c-1 1-1 2-2 3-2-1-3-2-4-2-2 0-2 0-3-1 1-1 1-1 2-1h1c1-1 2-3 2-4v-1h1z" class="F"></path><path d="M485 148h1c1 0 1 0 2 1v1c-1 0-1 1-2 1l-1-3z" class="E"></path><path d="M472 138l4-2h1c1 1 3 1 4 1 2 1 3 2 4 3-1 0-2 0-2 1-1 1-1 1-2 1h0l-1-1c-2 0-4 2-7 0h0-1l-1-1c1-1 1-1 1-2z" class="X"></path><path d="M481 137c2 1 3 2 4 3-1 0-2 0-2 1-1 1-1 1-2 1h0l-1-1c-2 0-4 2-7 0h0 2c1-1 2-1 2-1 1 0 1 0 2-1h0l1 1 1-1v-2z" class="P"></path><path d="M471 140l1 1h1 0c3 2 5 0 7 0l1 1c-1 1-1 1 0 1 0 1 0 2 1 2l-2 1 1 2h-1v-1h-2l-2-2c0 1-1 1-1 2h0 0c-1 1-1 1-2 1-1-1-1-1-2-1 0 0-1-1-1-2h0v-2h-1c1-2 1-2 2-3z" class="U"></path><path d="M471 140l1 1h1 0v3 1s0 1 1 1h0c0-1 1-1 1-2h1v1c0 1-1 1-1 2h0 0c-1 1-1 1-2 1-1-1-1-1-2-1 0 0-1-1-1-2h0v-2h-1c1-2 1-2 2-3z" class="B"></path><path d="M471 140l1 1c-1 1-1 2-1 3s0 1-1 1h0v-2h-1c1-2 1-2 2-3z" class="F"></path><defs><linearGradient id="AF" x1="472.727" y1="448.942" x2="467.832" y2="409.745" xlink:href="#B"><stop offset="0" stop-color="#1c1c1c"></stop><stop offset="1" stop-color="#3d3c3d"></stop></linearGradient></defs><path fill="url(#AF)" d="M460 385v3c2-2 0-4 1-6h1v1l1 1c0 1 1 1 1 2s0 3-1 4h0c1 0 2-2 2-3l1 1c1 3 2 6 2 9h1v-1-1c1 0 2 2 2 3v1 1 2c0 1 0 2 1 3 1 2 1 5 1 7 0 3 0 5 1 7 0 2 0 3 1 5s0 5 1 7c0 1 0 2-1 2v-3h-1v3l-1 1v-1h-1v4 4c-1 1-1 3-1 4v-5-1h0c1-1 1-2 0-2v4c-1 1-1 1 0 2-1 1-1 3-1 4v1 1c-1 1-1 1-1 3l-1-1c0-1 0-2-1-3v1h0v-4l-1-1c0 2 1 4 0 5h0v-5h0v-1-2h0c1-1 0-5 0-6-1-1-2-1-2-3h0c1-4 0-9 1-14h-1-1l-1 1v-5-1l-1-1c1 0 1 0 1-1h0v-1 2h0c0-1 1-2 1-3-1-1-1-2-1-3v-2h-1v3h0l-1 1v-4c0-1 1-3 1-4l-1-15z"></path><path d="M472 433v-7c1 1 0 2 0 3h1 0v3 1h-1z" class="V"></path><path d="M473 429l1 1v3l-1 1v-1-1-3zm-6 3v5l1-1v-3h0v5c0 1 0 4-1 5h0v-11zm2-24h0l1-1h0v2c1 2 1 3 1 5v5l-1-1v-1c0-2 0-4-1-5l-1-1c1-1 1-1 1-2v-1zm-5-4v-2-3 1l1 2 1 1v2c0 1 1 2 1 4s0 4-1 6v1c0-1 0-2-1-4 0 2 1 2 1 3v6h0l1-1h0c0 2 1 4 0 6s-1 6-1 8c-1 0-1-1-2-2 1-4 0-9 1-14h-1-1l-1 1v-5-1l-1-1c1 0 1 0 1-1h0v-1 2h0c0-1 1-2 1-3l1-5z" class="e"></path><path d="M464 404c1 2 0 5 0 7 0 1-1 1-1 1h-1c0-1 1-2 1-3l1-5z" class="n"></path><path d="M460 385v3c2-2 0-4 1-6h1v1l1 1c0 1 1 1 1 2s0 3-1 4h0c1 0 2-2 2-3l1 1c1 3 2 6 2 9h1v-1-1c1 0 2 2 2 3v1 1 2c0 1 0 2 1 3 1 2 1 5 1 7v2h-1c0 1 0 2-1 3v-3c0-2 0-3-1-5v-2h0l-1 1h0v-2h0v2l-1 1v-1-3 1 1l-1 2c0-2-1-3-1-4v-2l-1-1-1-2v-1 3 2l-1 5c-1-1-1-2-1-3v-2h-1v3h0l-1 1v-4c0-1 1-3 1-4l-1-15z" class="r"></path><path d="M448 315s1 1 1 2c1 0 1-1 1-1v-1h1v1 3l1 2 4 5 1 1h0c0 1 1 2 1 3v1h-1v-1h-1c0 2 3 3 3 5h-1-1v1h0c1 1 1 3 1 4s0 2 1 2h-1 0c0-1-1-2-2-3h-2v1c1 0 0 1 0 2 1 1 2 0 1 2h0c-2-2-2-4-3-6 0-1 0-1-1-2-1 2-1 5 0 7v4l1 6h0-1 0-1-1v3h-1 0v-1c-1 1-1 1-1 3 0 1 0 1 1 2s2 3 2 4v1c-1 3 2 9 3 13 0 3 1 7 1 11-2-3-1-7-2-10l-4-13h-1c0 1 1 3 1 4v1h-1c-1-2-1-4-1-7h-1c-1-7-2-13-4-19-2-3-2-7-2-10 0-1 0-2 1-4v-8-2h0v-2l1 1c0 1 0 1 1 2h1v-1h0c-1-1-1-2-1-3 1 1 2 2 2 4h0 1l1-1c-1-1-1-3-1-4 2 0 2-1 3-2z" class="Z"></path><path d="M452 332h1l1 1v2h-1c-1-1-1-1-1-3z" class="g"></path><path d="M449 336v2l1 1v3h0c-1 0 0 0-1-1 0-1 0-1-1-2 1-1 1-1 0-2l1-1z" class="c"></path><path d="M451 353c0-1-1-2-1-3-1-1-2-4-1-5h1c0 1 0 1 1 2l1 6h0-1z" class="W"></path><path d="M451 322l4 6c0 1-1 2-1 3h-1c0-1-1-3-2-4v-4h0v-1z" class="t"></path><path d="M443 334v-6c0-1 0-1 1-2h0v5h0c0 4 1 7 1 10 1 3 1 4 2 6l1 5h-1v-3c-1-1-1-2-1-3-2-3-2-8-3-12z" class="i"></path><path d="M448 315s1 1 1 2c1 0 1-1 1-1v-1h1v1 3l1 2c0 1 0 1-1 1l-1-1h0v2h0c-1 0-1-1-2-2h0c-1 0-1 1-2 1v-1c-1-1-1-3-1-4 2 0 2-1 3-2z" class="p"></path><path d="M451 315v1 3l1 2c0 1 0 1-1 1l-1-1c0-1 0-2-1-3v-1c1 0 1-1 1-1v-1h1z" class="g"></path><path d="M440 321h0v-2l1 1c0 1 0 1 1 2h1v-1h0c-1-1-1-2-1-3 1 1 2 2 2 4h0 1l1-1v1c0 1-1 2-1 3v1c1 0 2-1 2-1 2 2 0 4 1 7l1 4-1 1h0-1-1v-2c1-1 1-2 1-3l-1-1-1 2c0-1 0-1-1-2h0v-5h0c-1 1-1 1-1 2v6c-1 2-1 3-1 5v-4h-1 0l-1-1v-2c2-3 1-6 1-9 0-1 0-1-1-2z" class="k"></path><path d="M448 337c-1-2-1-3-1-4s1-1 1-1l1 4-1 1h0z" class="i"></path><path d="M440 321c1 1 1 1 1 2 0 3 1 6-1 9v2l1 1h0 1v4c0-2 0-3 1-5 1 4 1 9 3 12 0 1 0 2 1 3v3h1c1 0 1 1 1 1v3h-1 0v-1c-1 1-1 1-1 3 0 1 0 1 1 2s2 3 2 4v1c-1 3 2 9 3 13 0 3 1 7 1 11-2-3-1-7-2-10l-4-13h-1c0 1 1 3 1 4v1h-1c-1-2-1-4-1-7h-1c-1-7-2-13-4-19-2-3-2-7-2-10 0-1 0-2 1-4v-8-2z" class="Z"></path><path d="M448 352c1 0 1 1 1 1-1 1-1 1-2 1v-2h1z" class="c"></path><path d="M441 335h1v4c1 2 2 4 2 7 0 1 1 1 1 2 1 1 1 3 1 4h-1c-2-5-3-11-4-17z" class="i"></path><path d="M440 321c1 1 1 1 1 2 0 3 1 6-1 9v2c0 7 2 12 4 18 0 2 1 5 1 7 1 2 1 3 1 5h-1c-1-7-2-13-4-19-2-3-2-7-2-10 0-1 0-2 1-4v-8-2z" class="W"></path><path d="M431 306l1 1c1 0 1 2 1 3h1v-1-2c0-1 0-1 1-2v3h1v-1c1 1 1 0 1 1h1v-1h1 0v1h0c1 1 1 2 2 3h1c0-1 0-1 1-2v1c1-1 1-1 2-1 1 1 0 1 0 2s1 1 1 1l1 1c0 1 0 1 1 2-1 1-1 2-3 2 0 1 0 3 1 4l-1 1h-1 0c0-2-1-3-2-4 0 1 0 2 1 3h0v1h-1c-1-1-1-1-1-2l-1-1v2h0v2 8c-1 2-1 3-1 4 0 3 0 7 2 10 2 6 3 12 4 19 0 2 0 2-1 4v5 7c-1 2-1 3-1 5h1c0 1 0 2-1 2 0 2 0 4-1 5v-5h-1v-3c0-1 1-3 1-4v-5-1c0-1 0-1-1-1 0 3 0 6-1 9 0 2 0 4-1 6-1-2 1-10 1-13 0-2-1-6-1-8-1 1-1 4-1 5-1-1-1-2-1-3s0-1 1-1c-1-1-1-2-1-3h-1v2h-1v-3h-1v2h-1l1-6s0-4 1-5v-1-2c0 1 0 1-1 1v-1h1v-2-3-1-5h0v-1l-1-2c-1-2 0-6 1-9-1-1-1-3-2-4v-2c0-3-1-6-1-9v-2c-1-2-1-4-1-6z" class="k"></path><path d="M439 335l-1-1v-1-6h1v4h1c-1 2-1 3-1 4z" class="i"></path><path d="M437 347c0-2 0-6 1-7 1 2 0 6 0 8l3 12-1 1c0-1-1-2-1-3l-1 1-2-9c0-1 1-2 1-3z" class="V"></path><path d="M437 347c0 2 1 4 1 6s1 3 1 5l-1 1-2-9c0-1 1-2 1-3z" class="Z"></path><path d="M435 329v-1-4l1-1v-5c1 1 1 1 1 2v2h0v1c1 1 0 1 0 2-1 3 0 6 0 9s-1 6-1 9v1c-1 1-1 1-1 2v1-1-5h0v-1l-1-2c-1-2 0-6 1-9z" class="r"></path><path d="M435 347v-1c0-1 0-1 1-2v4 2l2 9v9c-1-1-1-2-1-3h-1v2h-1v-3h-1v2h-1l1-6s0-4 1-5v-1-2c0 1 0 1-1 1v-1h1v-2-3z" class="q"></path><path d="M436 357l1 8h-1s0-1-1-2c0-2 0-4 1-6z" class="t"></path><path d="M435 350c1 2 1 5 1 7-1 2-1 4-1 6v-8-1-2c0 1 0 1-1 1v-1h1v-2z" class="c"></path><path d="M435 355v8c1 1 1 2 1 2v2h-1v-3h-1v2h-1l1-6s0-4 1-5z" class="V"></path><path d="M443 372v-6l-2-2 1-2c1-3-3-13-3-16l1-1v1l1-1c2 6 3 12 4 19 0 2 0 2-1 4v5 7c-1 2-1 3-1 5h1c0 1 0 2-1 2 0 2 0 4-1 5v-5h-1v-3c0-1 1-3 1-4v-5-1c0-1 0-1-1-1 1-1 1-1 2-1z" class="r"></path><path d="M441 373c1-1 1-1 2-1v7 2l-1 1v2h-1c0-1 1-3 1-4v-5-1c0-1 0-1-1-1z" class="c"></path><path d="M440 346l1-1c2 6 3 12 4 19 0 2 0 2-1 4l-1-3c0-1 1-3 0-4l-3-15zm-3-38h1v-1h1 0v1h0c1 1 1 2 2 3h1c0-1 0-1 1-2v1c1-1 1-1 2-1 1 1 0 1 0 2s1 1 1 1l1 1c0 1 0 1 1 2-1 1-1 2-3 2 0 1 0 3 1 4l-1 1h-1 0c0-2-1-3-2-4 0 1 0 2 1 3h0v1h-1c-1-1-1-1-1-2l-1-1v2h0v2s0 1-1 2l-1-1s0-1 1-1v-5-1h-1 0v-1c0-1 0-2-1-3 0 1 0 2-1 2h0v-2c1-2 1-3 1-5z" class="Z"></path><path d="M445 309c1 1 0 1 0 2s1 1 1 1l1 1c0 1 0 1 1 2-1 1-1 2-3 2l-1-1-1-1h-1c0-2 1-2 1-3v-2c1-1 1-1 2-1z" class="p"></path><path d="M444 316v-3h1v2h1l1-2c0 1 0 1 1 2-1 1-1 2-3 2l-1-1z" class="m"></path><path d="M419 272h2c1 1 1 2 2 3h1v4l2 1h2l1 1c1 1 1 1 1 2 1 2 0 3 1 6h-1l-1-1v2l1 1s0 1 1 1h0v2h1c0 1 0 2 1 3v2c-2 0-2-1-3-1v-1c0 1 0 2-1 3 0 1 1 3 1 3l1 3h0c0 2 0 4 1 6v2c0 3 1 6 1 9v2c1 1 1 3 2 4-1 3-2 7-1 9h-1c-1 1-1 2-2 3v2-2l-1-1h0 0l-1 1h-1c-1-1-1-3-2-5l-1 3c0-2 1-5 0-6s-2-4-2-5h-1v-3-2c1-1 1-1 1-3s0-4-1-7l-1-14v-6l1-1v-2c-1-1-1 0-1-1v-3h0v-1c0-2 0-3-1-4 1-1 1-1 1-2h0c-1-1-2-2-2-3v-1c1 1 2 2 2 3h2c-1-2-3-4-5-5l1-1z" class="i"></path><path d="M424 293c1 2 2 6 1 8-1-3-1-5-2-8h1z" class="p"></path><path d="M423 328l1-2 1-1c1 1 1 2 1 3l-1 5c-1-1-2-4-2-5z" class="n"></path><path d="M430 317v-1c-1-2 0-4-1-7 0 0 0-1 1-1h0l1 6c0 3 1 6 1 9-1-2-2-4-2-6z" class="c"></path><path d="M423 302c2 3 1 6 2 8v1c0 2-1 3 0 5v2c0 2 1 4 0 6h-1v-4-6c-1-4-1-8-1-12z" class="e"></path><path d="M422 292l1 5c0 2-1 4 0 5 0 4 0 8 1 12v6h-1c0-2 0-4-1-7l-1-14v-6l1-1z" class="c"></path><path d="M431 314h1c0 3 1 6 1 9v2 3l-1-3h-1v4h0l-1 2v-3-1-1-4h0c-1-1-1-2-2-2v-1l1-2h1c0 2 1 4 2 6 0-3-1-6-1-9z" class="V"></path><defs><linearGradient id="AG" x1="431.561" y1="298.813" x2="423.745" y2="296.39" xlink:href="#B"><stop offset="0" stop-color="#545852"></stop><stop offset="1" stop-color="#726d73"></stop></linearGradient></defs><path fill="url(#AG)" d="M424 285h0c2 1 1 3 2 5 1 1 1 2 2 3 0 2 1 3 2 4h0c0 1 0 2-1 3 0 1 1 3 1 3l1 3h-2c-1-2-1-3-2-5-2-5-2-11-3-16z"></path><path d="M422 292v-2c-1-1-1 0-1-1v-3h0v-1c1 1 1 2 2 4v4c1 3 1 5 2 8s1 7 0 10h0v-1c-1-2 0-5-2-8-1-1 0-3 0-5l-1-5z" class="W"></path><path d="M430 331l1-2h0v-4h1l1 3v-3c1 1 1 3 2 4-1 3-2 7-1 9h-1c-1 1-1 2-2 3v2-2l-1-1h0 0l-1 1h-1c-1-1-1-3-2-5l-1 3c0-2 1-5 0-6l1-5s0 1 1 1c0-1-1-1 0-1v-1c1 0 0 0 1-1l1 1h0c1 1 1 2 1 2v-1 3z" class="q"></path><path d="M426 328s0 1 1 1c0-1-1-1 0-1v-1 3h-1c0 2 1 5 0 6l-1 3c0-2 1-5 0-6l1-5z" class="V"></path><path d="M429 327h0c1 1 1 2 1 2v-1 3c0 3-1 5-2 8l1-12zm4-2c1 1 1 3 2 4-1 3-2 7-1 9h-1c1-3 0-7 0-10v-3z" class="W"></path><path d="M419 272h2c1 1 1 2 2 3h1v4l2 1h2l1 1c1 1 1 1 1 2 1 2 0 3 1 6h-1l-1-1v2l1 1s0 1 1 1h0v2h1c0 1 0 2 1 3v2c-2 0-2-1-3-1v-1h0c-1-1-2-2-2-4-1-1-1-2-2-3-1-2 0-4-2-5h0l-1-3h-1c0 2 0 2 1 3 0 2 1 4 1 6v2h-1v-4c-1-2-1-3-2-4 0-2 0-3-1-4 1-1 1-1 1-2h0c-1-1-2-2-2-3v-1c1 1 2 2 2 3h2c-1-2-3-4-5-5l1-1z" class="Z"></path><path d="M429 290l1 1s0 1 1 1l-1 1h-1l-1-2 1-1z" class="I"></path><path d="M419 272h2c1 1 1 2 2 3h1v4l2 1h2l1 1c1 1 1 1 1 2 1 2 0 3 1 6h-1l-1-1v2l-1 1c0-2-1-3-1-5 0 0 0-1-1-2 0 0-1-1-1-2v-1h0c-1-1-2-2-2-3-1-2-3-4-5-5l1-1z" class="m"></path><path d="M426 280h2l1 1c1 1 1 1 1 2-2-1-2-1-4-3z" class="g"></path><path d="M471 402h1v-4h0c1 0 1 1 1 1v1c1-1 1-1 1-2h0v-1c1 1 1 1 1 2v2c-1 2 1 3 1 5h1v-1 1l1-1v1c0 3 2 6 3 10v1c1-1 1-2 1-3v-1c-1-2-1-4-2-6h0 0l2 2 1 2v2 1l1 1 1-1c0 2 0 3 1 4 0 1 1 3 1 5l1-3h0 0v8c1 1 1 3 1 5v1l1 1c-1 5-1 9-1 15l1-5 1-1c1 2 0 4 0 6-1 2-1 4-1 7h0c0 1-1 2-1 2l1 2c0 1-1 2-1 3h-1v-1h-1c0 1-1 2-1 3h0-1c0-1 0-2 1-3 0-1-1-1-1-2l-1-1c-1 0-1-1-2-1v1 1c-1-1 0-6 0-7 0 0-1 0-1-1v2l-1-1 1-4c0-2 0-5-1-7 0-1 1-1 0-3l-1 1v-2-1l-1 3h0c0 1 0 2-1 2v-3c-1-2-1-7-1-9-1-2 0-5-1-7s-1-3-1-5c-1-2-1-4-1-7 0-2 0-5-1-7-1-1-1-2-1-3z" class="e"></path><path d="M482 424l1 3h1c-1 1 0 1 0 2s1 3 1 4c-1 3-1 7-2 9v-4c-1-2 0-3-1-4v-6-4z" class="i"></path><path d="M481 453v-1h1v4c1 1 1 1 2 1h1v-2-3h0v-1h1c0 1 1 2 1 3h-1c0 2 0 3 1 4h0v4h1v-3h1 0l1 2c0 1-1 2-1 3h-1v-1h-1c0 1-1 2-1 3h0-1c0-1 0-2 1-3 0-1-1-1-1-2l-1-1c-1 0-1-1-2-1v1 1c-1-1 0-6 0-7 0 0-1 0-1-1z" class="s"></path><path d="M482 414v-1c-1-2-1-4-2-6h0 0l2 2 1 2v2 1l1 1 1-1c0 2 0 3 1 4v3 4 1 2c-1 2-1 3-1 5h0v-5l-1 1c0-1-1-1 0-2h-1l-1-3c0-2 0-5-1-7 1-1 1-2 1-3z" class="W"></path><path d="M481 417c1-1 1-2 1-3 1 4 2 8 2 13h-1l-1-3c0-2 0-5-1-7z" class="t"></path><path d="M486 418c0 1 1 3 1 5l1-3h0 0v8c1 1 1 3 1 5v1c-1 5-1 9-1 14-1 1-1 0-3 1v-1l1-15v-5-2-1-4-3z" class="W"></path><path d="M486 418c0 1 1 3 1 5l1-3h0 0v8c-1 2 0 6-2 8h0v-3-5-2-1-4-3z" class="c"></path><path d="M471 402h1v-4h0c1 0 1 1 1 1v1c1-1 1-1 1-2h0v-1c1 1 1 1 1 2v2c-1 2 1 3 1 5h1v-1 1l1-1v1c0 3 2 6 3 10h-1v1l-1 1c0 2 0 4 1 5 1 4 1 9 1 12 0 1 0 1-1 2l1 1c0 1-1 1-1 2-1-1-1-1-1-2v-4l-1-11h-1 0c1 1 1 3 0 3 0 1 0 2 1 3v2 5 5c0 1 0 2-1 2v-3c-1-2-1-7-1-9-1-2 0-5-1-7s-1-3-1-5c-1-2-1-4-1-7 0-2 0-5-1-7-1-1-1-2-1-3z" class="i"></path><path d="M474 419c1-2 0-5 0-7l-1-8h0c1 1 1 3 2 4 0 4 2 7 1 10v2l1 1v-1c1 1 1 2 1 3h-1 0c1 1 1 3 0 3 0 1 0 2 1 3v2 5 5c0 1 0 2-1 2v-3c-1-2-1-7-1-9-1-2 0-5-1-7s-1-3-1-5z" class="r"></path><path d="M316 490h2 0l5 5 1 1v7l1 1v6c1 3 1 7 0 9v2 9 10h-2-4-5v-1h-2l-1-38c1 0 1-1 1-1v-1c-1-1 0-1-1-1h0c-1-1-1-1-1-2 1-2 1-3 2-4s2-1 2-2h1 1z" class="Y"></path><path d="M317 513h1c0 2 0 4-1 6-1-1 0-4 0-6z" class="E"></path><path d="M316 497l1 1c1 1 1 2 1 3h-2c-1 1 0 2-1 2v1c0-3 0-4 1-7z" class="U"></path><path d="M314 539c1 0 1 0 2-1 0-2-1-6 0-8l1-1h1l1 3c-1 1-1 1-1 2v1 3s1 1 1 2h-5v-1z" class="L"></path><path d="M315 504v-1c1 0 0-1 1-2h2v3h0v9h-1c-1 0-1-1-2-1v-8z" class="T"></path><path d="M316 490h2 0l5 5 1 1v7l1 1v6c1 3 1 7 0 9v2 9 10h-2-4c0-1-1-2-1-2v-3-1c0-1 0-1 1-2 0 0 0 2 1 3v1 1c1 1 1 1 2 1h0c-1-2 0-5 0-7v-6-6h0v-6h1c-1-1-1-1-1-2h0v1c-1-3 0-5-1-7 0-3 1-8-1-10-1-1-1-1-1-2h1c0-1-1-1-2-2l1 2c-2 2 0 7-1 10v1h0v-3c0-1 0-2-1-3l-1-1h0c-1-1-1-1-1-2 1-1 0-1 0-2h-1l1-1-1-1-2 1c1-1 2-1 2-2h1 1z" class="f"></path><path d="M318 535h0c1 1 2 2 2 3 1 1 1 1 3 1v1h-4c0-1-1-2-1-2v-3z" class="K"></path><path d="M316 490h2 0l-1 1c1 1 1 1 1 2v1c0 1 0 2-1 2l-1 1c-1-1-1-1-1-2 1-1 0-1 0-2h-1l1-1-1-1-2 1c1-1 2-1 2-2h1 1z" class="E"></path><path d="M315 493v-1h1v1c1 0 1 1 2 1 0 1 0 2-1 2l-1 1c-1-1-1-1-1-2 1-1 0-1 0-2z" class="D"></path><path d="M324 496v7l1 1v6c1 3 1 7 0 9v2 9 10h-2v-1-4-8l1-31z" class="L"></path><path d="M324 503l1 1v6h0 0l-1 1v-8z" class="R"></path><path d="M379 246c1-1 1-1 2-1 5 1 9 5 14 9h0v1 1c3 2 5 5 6 7l1 3 1 3 1 4h0v2h0v3c0 1 0 4-1 4v3c0 1-1 2-1 3h1l-1 1v2s-1 1-1 2v1h-1c0-1 0-2 1-2l-2-2c-1-1-1-2 0-3v-1c-1 0-2 3-3 3l-1-1-2-1-2-3c-1-1-2-2-2-3-1 0-1-5-1-6 0 2-1 3-2 5v-1-4-5h-2 0v1c0-4-1-8-2-12l-2-8c0-1-1-3-1-5z" class="x"></path><path d="M401 263l1 3c0 1 0 2-1 3l-1-4 1-2z" class="H"></path><path d="M394 256h1c3 2 5 5 6 7l-1 2c-2-4-4-6-6-9z" class="B"></path><path d="M379 246c3 1 5 1 7 3h0c-2 0-3-1-5-2 0 1-1 1-1 2h1 0c1 0 1 1 1 1h2v1c0 1 1 3 1 5v1l1-1v4h0c-1-2-1-4-2-6-1-1-2-2-2-3h-1-1c0-1-1-3-1-5z" class="q"></path><path d="M380 251h1 1c0 1 1 2 2 3 1 2 1 4 2 6v1c-2 0-1-1-2-2h-2l-2-8z" class="c"></path><path d="M379 246c1-1 1-1 2-1 5 1 9 5 14 9h0v1 1h-1c-1 0-6-6-8-7-2-2-4-2-7-3z" class="S"></path><path d="M382 259h2c1 1 0 2 2 2v-1h0c1 2 1 4 2 5 1 3 1 7 0 10h0c0 2-1 3-2 5v-1-4-5h-2 0v1c0-4-1-8-2-12z" class="W"></path><path d="M386 270h0c0-3-1-5-1-8h0c1 0 1 0 1 1s1 3 1 5c1 2 1 4 1 7h0c0 2-1 3-2 5v-1-4-5z" class="k"></path><path d="M402 266l1 3 1 4h0v2h0v3c0 1 0 4-1 4v3c0 1-1 2-1 3h1l-1 1v2s-1 1-1 2v1h-1c0-1 0-2 1-2l-2-2c-1-1-1-2 0-3v-1c-1 0-2 3-3 3l-1-1c0-1 0-2 1-3s1-2 2-3v2h1c1-2 1-4 2-5h0l-1-5 1 1v-6h0 0c1-1 1-2 1-3z" class="s"></path><path d="M402 266l1 3 1 4h0v2l-2 9c-1-3 0-8 0-12 0-1 0-2-1-3h0c1-1 1-2 1-3z" class="I"></path><path d="M402 266l1 3v3h-1c0-1 0-2-1-3h0c1-1 1-2 1-3z" class="N"></path><path d="M488 399c2 3 3 6 5 9v-2c0-1-1-1-1-3h0c2 1 2 3 3 5l1-1v1l-1 1v1c1 1 1 1 1 2h0l1-1 3 9c1 2 1 4 2 6h1l-1 1 1 3v3l1 1c1 1 1 5 1 7-1 0-1 1-2 1v-1h0v-1c-1 1-1 5-1 7 0 1 0 1 1 2 0 2 1 4 1 6h-1c-1-1-1-2-1-3v-2h-1 0c0 1 0 1-1 2v1h-1v-1h-1v3l-1 1h0v1 1c0 1-1 1-1 2h0l-1-1-1 1c0 1-1 2-1 4-3-1-1-4-3-6v3l-1-2s1-1 1-2h0c0-3 0-5 1-7 0-2 1-4 0-6l-1 1-1 5c0-6 0-10 1-15l-1-1v-1c0-2 0-4-1-5v-8h0 0l-1 3c0-2-1-4-1-5-1-1-1-2-1-4l-1 1-1-1v-1-2l-1-2c1 0 0 0 1-1 0-1 0-1 1-2 0-1-1-2-1-4h1v1c1 0 1-1 2-1v-2h1c0 1 1 1 1 2s0 1 1 2v-1c0-1 0-2-1-3v-1z" class="V"></path><path d="M490 458l1-1v-3h1v3h1v-3h1v3l1 1v-2 3s1 0 1 1l-1-1-1 1c0 1-1 2-1 4-3-1-1-4-3-6z" class="e"></path><path d="M490 435v-1c0-2-1-4 1-6 1 2 0 6 0 8 1-2 1-3 1-5l1-1c1 2 0 8-1 10v3h0c0 3 0 4-1 7h0c0-2 1-4 0-6l-1 1-1 5c0-6 0-10 1-15z" class="W"></path><path d="M488 399c2 3 3 6 5 9 1 1 2 2 2 4h-1l2 4v1c0 1 1 2 1 3 0 3 1 6 1 9l-1 1v-5c-1-5-3-9-4-13-1-1-1-2-2-3h0v1c1 3 6 15 5 17h-1l-1-5-2-6h0c-1-3-2-7-3-8h-1-1v-4l-1-1h-1-1c1 0 1-1 2-1v-2h1c0 1 1 1 1 2s0 1 1 2v-1c0-1 0-2-1-3v-1z" class="c"></path><path d="M482 409c1 0 0 0 1-1 0-1 0-1 1-2 0-1-1-2-1-4h1v1h1 1l1 1v4h1 1c1 1 2 5 3 8h0 0c-1 2 1 5 1 6l1 4v3h-1c0-1 0-1-1-2v-1-3c-1-2-1-4-2-5 0-2 0-3-1-4-1 1 0 2 0 3v1c1 2 2 5 1 7 0-1 0-1-1-1 0-2 0-3-1-4h0 0 0l-1 3c0-2-1-4-1-5-1-1-1-2-1-4l-1 1-1-1v-1-2l-1-2z" class="i"></path><path d="M485 414c0-1-1-3-1-4h1 0c0 1 0 1 1 2 0 1 0 3 1 4 0 1 1 2 1 4h0l-1 3c0-2-1-4-1-5-1-1-1-2-1-4z" class="k"></path><path d="M493 408v-2c0-1-1-1-1-3h0c2 1 2 3 3 5l1-1v1l-1 1v1c1 1 1 1 1 2h0l1-1 3 9c1 2 1 4 2 6h1l-1 1 1 3v3l1 1c1 1 1 5 1 7-1 0-1 1-2 1v-1h0v-1c-1 1-1 5-1 7 0 1 0 1 1 2 0 2 1 4 1 6h-1c-1-1-1-2-1-3v-2h-1 0c0 1 0 1-1 2v-3h-1c0 1-1 1-1 1l-1-1v-3-2c1-1 1-2 1-3v-1-1h-1v2h0l-3 6h0c0-3 0-7 1-9s0-5 2-7h0v4 1l1-1c0-2 0-3-1-5l1-1c0-3-1-6-1-9 0-1-1-2-1-3v-1l-2-4h1c0-2-1-3-2-4z" class="W"></path><path d="M501 430l1 1v4c-1-1-1-1-1-2h0v-3z" class="r"></path><path d="M498 440l1-1c0 1 0 1 1 2v4h-1v-2c0-1-1-1-1-2v-1z" class="c"></path><path d="M493 408v-2c0-1-1-1-1-3h0c2 1 2 3 3 5l1-1v1l-1 1v1c1 1 1 1 1 2h0l3 9c0 1 1 2 1 3-1 0-2-4-2-5-1-2-2-5-3-7h0c0-2-1-3-2-4zm-238-32h2 0c3-2 4-2 7-1l1 2 2 2c2 1 4 3 4 6v1h-1c-1-1-1-1-2-1v1l-2 2h0l-1 1c-2-1-3-1-4-2h-3-1v-1c-1 0-1 0-1-1-1 0-1 1-2 2v1 1l-1 1v1c1 1 2 1 2 2v1h-1c1 1 1 1 1 2l1 1c1 1 1 2 1 3s1 1 2 2c0 1 2 2 2 3l-2 2h0c0 1 2 3 2 4l-1 1c1 1 1 2 1 3h-2v1h1v1l-1 2h-1c-2-3-6-7-9-10l-3-3c-1-1-2-3-3-4h-1c-3-4-5-9-7-14 0-2 0-4-1-5 1-1 1-1 1-2v-1c1-1 2-3 4-4 1 0 2-1 3 0l1-1h1l3 3h-1l1 1h3l-1-1h2 1 1c2 0 1-1 2-2z" class="t"></path><path d="M258 381l3 6c-2-1-3-3-5-4 1-1 1-2 2-2z" class="H"></path><path d="M252 395h0c-1-1-1-1-1-2 1 0 1-1 1-1l1-1c1 1 2 1 2 2v1h-1c-1 1-1 1-2 1z" class="B"></path><path d="M248 395h0c1 0 1 0 1-1 2 1 4 6 5 8h-2c0-1-1-1-1-2l-1 1-1-1c1-1 1-1 1-2s-1-2-2-3z" class="T"></path><path d="M251 382c1 0 2 1 2 2 1 0 1 2 1 3-1 1-1 2-1 3-1-1-1-1-1-2h-1v-6z" class="M"></path><path d="M249 380l2 2v6h1c0 1 0 1 1 2l-1 2c-1 0-1 0-2-1 0-1 0-4-1-5v-3c0-1-1-2-1-2l1-1z" class="C"></path><path d="M250 379l-1-1h2 1 1c1 1 3 1 4 1l1 2c-1 0-1 1-2 2h-1l-5-4z" class="B"></path><path d="M255 376h2 0c3-2 4-2 7-1l1 2h-2l-1-1-3 2v1c1 1 2 0 3 1l-3 2h0v-1c0-1-1-1-1-2v-2h-1v2c-1 0-3 0-4-1 2 0 1-1 2-2z" class="Q"></path><path d="M262 380c1 0 2 1 3 1v1h0 0c0 1 1 1 1 2-1 1-1 1-1 2 1 0 2 0 3-1v1l-2 2h0c-1 0-2-1-3-1 1-1 1-1 1-2h0l-2 1h0c-1-2-2-3-3-4l3-2z" class="U"></path><path d="M262 380c1 0 2 1 3 1v1h0-1c-1-1-1-1-2-1h-1v1c1 0 1 1 2 1 0 0 0 1 1 1v1l-2 1h0c-1-2-2-3-3-4l3-2z" class="C"></path><path d="M247 381l1-1h1l-1 1s1 1 1 2v3c1 1 1 4 1 5-1 1-1 0-2 0-2-2-2-3-2-5v-1c0-2 0-3 1-4z" class="M"></path><path d="M252 395c1 0 1 0 2-1 1 1 1 1 1 2l1 1c1 1 1 2 1 3s1 1 2 2c0 1 2 2 2 3l-2 2h0l-1-1c-2-2-3-4-4-7-1-1-1-1-1-2h2c-1-1-1-1-2-1h-1v-1z" class="T"></path><path d="M258 404l2 1h1l-2 2h0l-1-1v-2z" class="N"></path><path d="M254 399c2 1 3 2 4 5v2c-2-2-3-4-4-7z" class="I"></path><path d="M262 380c-1-1-2 0-3-1v-1l3-2 1 1h2l2 2c2 1 4 3 4 6v1h-1c-1-1-1-1-2-1-1 1-2 1-3 1 0-1 0-1 1-2 0-1-1-1-1-2h0 0v-1c-1 0-2-1-3-1z" class="P"></path><path d="M265 377l2 2h-1c-2 0-2-1-3-2h2z" class="B"></path><path d="M241 393v-1c1 0 2 0 3 1 0 0 1 1 1 2l1-1-1-1v-1c1 0 1 0 2 1v-1c1 0 1 0 1 1v1 1c1 1 2 2 2 3s0 1-1 2l1 1 1-1c0 1 1 1 1 2h2c0 1 1 2 1 2l-2 1h0l-1 2c-2-1-3-2-4-3l-1-1c-2-2-4-5-5-9l-1-1z" class="S"></path><path d="M241 393v-1c1 0 2 0 3 1 0 0 1 1 1 2l1-1-1-1v-1c1 0 1 0 2 1v-1c1 0 1 0 1 1v1 1c1 1 2 2 2 3s0 1-1 2l1 1 1 1s1 1 1 2h0c-1-1-3-2-4-3v-2-3l-1 1h-1c1 1 1 1 1 3h-1c0-1-1-2-1-3h1v-1h-1c-1-1-2-1-3-2l-1-1z" class="X"></path><path d="M239 376c1 0 2-1 3 0l1-1h1l3 3h-1l1 1v2c-1 1-1 2-1 4v1h-1v4s-1 1-2 1h1 0-3c-1 1-1 1 0 2l1 1c1 4 3 7 5 9l1 1c1 1 2 2 4 3l1-2h0l2-1c2 3 3 6 5 8 1 1 1 2 1 3h-2v1h1v1l-1 2h-1c-2-3-6-7-9-10l-3-3c-1-1-2-3-3-4h-1c-3-4-5-9-7-14 0-2 0-4-1-5 1-1 1-1 1-2v-1c1-1 2-3 4-4z" class="d"></path><path d="M255 404c2 3 3 6 5 8 1 1 1 2 1 3h-2v1h1v1l-1 2c-1-1-1-1-1-2-1-1 0-5 0-7-1 0-1-1-2-1l-1-1c-1-1-2-1-3-1l1-2h0l2-1z" class="G"></path><path d="M239 378c2 0 3-1 5-1 0 0 1 1 2 1l1 1v2c-1 1-1 2-1 4v1h-1v4s-1 1-2 1c-1-1-2-2-3-4 1 0 0-3 0-4s0-2 1-4h1c-1 0-1 0-2-1h-1z" class="B"></path><path d="M241 379l2 1v2h-1l-1 1 2 2-1 1h0-1l-1 1c1 0 0-3 0-4s0-2 1-4z" class="N"></path><path d="M239 378c2 0 3-1 5-1 0 0 1 1 2 1l1 1v2c-1 1-1 2-1 4v1h-1l-1-3-1-1v-2l-2-1h1c-1 0-1 0-2-1h-1z" class="x"></path><path d="M244 377s1 1 2 1l1 1v2c-1 1-1 2-1 4v1h-1l-1-3c1-1 2-1 2-2v-1l-2-2v-1z" class="v"></path><defs><linearGradient id="AH" x1="244.393" y1="386.248" x2="236.123" y2="388.913" xlink:href="#B"><stop offset="0" stop-color="#8d8e8e"></stop><stop offset="1" stop-color="#a9a9aa"></stop></linearGradient></defs><path fill="url(#AH)" d="M239 376c1 0 2-1 3 0l1-1h1l3 3h-1c-1 0-2-1-2-1-2 0-3 1-5 1-1 1-2 2-2 5 0 4 1 7 3 11 1 3 2 5 3 8h-1c-3-4-5-9-7-14 0-2 0-4-1-5 1-1 1-1 1-2v-1c1-1 2-3 4-4z"></path><defs><linearGradient id="AI" x1="475.19" y1="304.402" x2="417.879" y2="287.159" xlink:href="#B"><stop offset="0" stop-color="#646363"></stop><stop offset="1" stop-color="#8d8c8d"></stop></linearGradient></defs><path fill="url(#AI)" d="M410 258h4l9 3h0l2 1h1 1c2 2 5 3 7 6 2 2 4 5 6 7 4 7 7 15 11 22v2s2 2 2 3c5 7 10 14 16 21v1 1h0l1 1h-1l-1-1c-1 1-2 0-4 0 1-1 1 0 1-1h1l-3-3c-1 0-1-1-1-1-2-1-2-2-3-3-1-3-5-7-7-10h0c-1 1 2 3 2 5h-1l-2-3v-1c-1 0-1-1-1-1v-1h-1c-1 1-1 1-2 1v2l1 1h0c1 2 2 3 2 4 1 0 1 0 1 1h-1v1s0 1-1 1c0-1-1-2-1-2-1-1-1-1-1-2l-1-1s-1 0-1-1 1-1 0-2c-1 0-1 0-2 1v-1c-1 1-1 1-1 2h-1c-1-1-1-2-2-3h0v-1h0-1v1h-1c0-1 0 0-1-1v1h-1v-3c-1 1-1 1-1 2v2 1h-1c0-1 0-3-1-3l-1-1h0l-1-3s-1-2-1-3c1-1 1-2 1-3v1c1 0 1 1 3 1v-2c-1-1-1-2-1-3h-1v-2h0c-1 0-1-1-1-1l-1-1v-2l1 1h1c-1-3 0-4-1-6 0-1 0-1-1-2l-1-1h-2l-2-1v-4h-1c-1-1-1-2-2-3h-2l-1 1-2-3c0-1-1-2-2-3h1v-1c-1-1-2-1-3-1v-1c-2-1-3-2-4-4 0-1 1-1 2-2z"></path><path d="M447 309l1 1h0v1l-1-1v-1z" class="j"></path><path d="M433 279c0-1 0-1 1-2 1 0 1 0 2 1v1h-1-2z" class="N"></path><path d="M462 320c1 0 4 2 4 3 1 1 1 2 2 2-1 1-2 0-4 0 1-1 1 0 1-1h1l-3-3c-1 0-1-1-1-1z" class="m"></path><path d="M427 270c1 0 1 1 2 1 0 1 1 2 1 2 1 1 1 1 1 2h-1c-2-1-3-2-4-3v-1l1-1zm11 14l1 1s0 1 1 1v1l-1 2c-1-1-1-1-2-1h0l-1 1v-1c0-1 0-2 1-2h1v-1-1z" class="I"></path><path d="M436 291h2 1v-1h1 2 0v1l-3 1v2h3v2h-1v-1h-1v1-1h-2c-1-1-1-1-1-3h-1v-1z" class="g"></path><path d="M427 270v-1-2h2c0 1 1 2 1 3h1l3 3h-1l-1-1h0c-1 1-1 1-2 1 0 0-1-1-1-2-1 0-1-1-2-1z" class="N"></path><path d="M427 262c2 2 5 3 7 6h-1c-1-1-2-1-3-1l6 6 1 2v1l-1-1-2-2-3-3h-1c0-1-1-2-1-3h-1c-1-1-2-1-3-2v-1c-2 0-3 0-5 1-1-1-1-1-1-2l1-1c2 0 1 1 3 0h2 1 1z" class="I"></path><path d="M420 265c2-1 3-1 5-1v1c1 1 2 1 3 2h1-2v2 1l-1 1v1c0 1 0 1-1 2-1 0-2-1-3-1l2 2h1c2 1 2 1 3 3h0v2h-2l-2-1v-4h-1c-1-1-1-2-2-3v-1c1 0 2 2 4 2h0v-1c-1-2-1-2-2-2v-1s1 0 1-1c-1-1-2-2-4-3z" class="R"></path><path d="M429 281l1-1-2-1c1-1 2-1 3 0 1 0 1 1 1 1h0l1-1h2c0 2 1 2 1 3 1 1 2 1 2 2h0v1 1h-1c-1 0-1 1-1 2v1h0v1 1 1 1c-1-1-1-1-2-1v1h-1l-1-1v1l-1-1h0c-1 0-1-1-1-1l-1-1v-2l1 1h1c-1-3 0-4-1-6 0-1 0-1-1-2z" class="G"></path><path d="M436 282c1 1 2 1 2 2h0v1h-1-2-1v1c-1-1-1-1-2-1h-1c0-1 1-2 1-2h1c1 0 1 0 2-1v1l1-1z" class="m"></path><path d="M436 292h1c0 2 0 2 1 3h2v1l-1 2c-1 0-1 0-1-1l-2 4c-1 1-1 1 0 2v1c1 0 1-1 2-1v1l1 1c2 1 3 1 5 1l1-1s-1 1-2 1c1 1 1 1 2 1h-1l1 2c-1 0-1 0-2 1v-1c-1 1-1 1-1 2h-1c-1-1-1-2-2-3h0v-1h0-1v1h-1c0-1 0 0-1-1v1h-1v-3c-1 1-1 1-1 2v2 1h-1c0-1 0-3-1-3l-1-1h0l-1-3s-1-2-1-3c1-1 1-2 1-3v1c1 0 1 1 3 1v-2c-1-1-1-2-1-3h-1v-2l1 1v-1l1 1h1v-1c1 0 1 0 2 1v-1z" class="h"></path><path d="M443 309c-1-1-1-1-1-3h1c0 1 1 1 1 1l1 2c-1 0-1 0-2 1v-1z" class="I"></path><path d="M430 303c1 0 2 1 2 1v1c1 0 1-1 1-1v-3l1-1v2 3h1c-1 1-1 1-1 2v2 1h-1c0-1 0-3-1-3l-1-1h0l-1-3z" class="a"></path><path d="M410 258h4l9 3h0l2 1h-2c-2 1-1 0-3 0l-1 1c0 1 0 1 1 2 2 1 3 2 4 3 0 1-1 1-1 1v1c1 0 1 0 2 2v1h0c-2 0-3-2-4-2v1h-2l-1 1-2-3c0-1-1-2-2-3h1v-1c-1-1-2-1-3-1v-1c-2-1-3-2-4-4 0-1 1-1 2-2z" class="m"></path><path d="M419 271c1-1 1-1 2-1v1 1h-2v-1h0z" class="I"></path><path d="M414 263c1 1 2 1 2 2l-1 1v-1 1c-1-1-2-1-3-1v-1h1l1-1z" class="t"></path><path d="M416 265c1 1 1 1 1 2 1 1 2 1 3 3l-1 1h0v1l-1 1-2-3c0-1-1-2-2-3h1v-1-1 1l1-1z" class="a"></path><path d="M410 258h4l9 3h0-2-2-1-2v1h1c0 1 0 1 1 2h0c-2 0-3-2-4-1h0l-1 1h-1v1-1c-2-1-3-2-4-4 0-1 1-1 2-2z" class="t"></path><path d="M410 258h4c-1 1-2 1-2 2h-1l-1 1c1 1 2 1 4 2l-1 1h-1v1-1c-2-1-3-2-4-4 0-1 1-1 2-2z" class="W"></path><path d="M452 562l15-63c1 2 0 4 0 6l-1 11v8c1-1 1-5 2-6 0 4 0 9 1 13h1l1 5v-5c1 1 2 4 2 5 0 2 1 3 1 5 1 0 1 1 1 1 1 1 2 3 3 3v-2h1l4 11v-1-5c1 3 3 7 5 9l3 2c-2 1-2 1-3 2-1 0-2 0-3 1-3 0-5 0-8 1h-1c-2 0-4 2-5 4l-1 1c0 1-1 1-1 1v3c-2 0-4 1-7 0-1 0-2 1-3 1-1-1-1-1-2-1v1h-6v-6c0-2 1-3 1-5z" class="w"></path><path d="M458 560l8-1v1l-1 1c-2 0-5 0-7-1h0zm2-16v-1h14v1h-7-7z" class="Y"></path><path d="M462 555h7c2 0 9 0 10 1h-1l-15 1c-1 0-1 0-2-1l1-1z" class="J"></path><path d="M469 568v-1c1-2 3-5 6-6 2-1 4-1 6 0 1 0 2 1 4 1h0c-3 0-5 0-8 1h-1c-2 0-4 2-5 4l-1 1c0 1-1 1-1 1v-1z" class="c"></path><path d="M451 567c0-2 1-3 1-5h0c1 2 1 3 0 5v2c2 1 4 1 5 0 2 0 3 1 4 0 1 0 5 0 6-1h2v1 3c-2 0-4 1-7 0-1 0-2 1-3 1-1-1-1-1-2-1v1h-6v-6z" class="Y"></path><path d="M484 233s0-1 1-1c1-1 1 0 2 0h1c1 1 3 1 5 2 1 0 2 1 3 2h2v1h1 1 4c0-1-1-2-1-3l3 1 1-1 1 1h1c0 2 0 2 1 3s1 1 2 1v2c1 1 2 1 2 2h1l1-1v2h-1s-1 0-2 1c1 1 1 1 1 2s-2 0-2 2h0c1 0 2-1 3-1 0 1 1 2 1 3h0v1c1 1 1 1 1 0l1 1v5h-1v1c-3 3-7 4-11 4-2 1-6 3-7 2h-2-1 1c1 1 3 0 4 1h-1c0 1-1 1-1 1-5 3-11 7-13 12v-5l-1-1h0c-1-2 0-4 0-5 0-3 0-6 1-8 1-1 1 0 1-1v-2c-1 0-1 0-2 1h0c-3-1-5-2-6-4v-3l1-1v-3-1c-1-1-2-5-1-7 0-1 0 1 0-1 1-1 1-1 1-2 1-1 3-2 4-3z" class="h"></path><path d="M499 256h2c0 1 0 1 1 2-1 1-1 1-3 2h0v-1-1-2h0z" class="M"></path><path d="M516 253l1 1c-1 1-2 2-3 4l-3-2 1-1c2 0 3-1 4-2z" class="E"></path><path d="M496 254v-1l1-1 2 1h0 2c0 1 0 1 1 2l-1 1h-2l-1 1c-1-1-1 0-1-1s0-1-1-2z" class="K"></path><path d="M488 239h2l-2 2c-1 1-3 2-5 2h0-1l-1-3h2 1 0c2-1 2-1 3 0l1-1z" class="D"></path><path d="M481 240h2 1l-1 1v2h0-1l-1-3z" class="O"></path><path d="M488 239l-2-1c0-1 1-1 2-2l1-2c2 0 2 0 4 1 0 1-1 2-1 3-1 0-1 1-2 1h-2z" class="E"></path><path d="M480 250c1 0 1 0 2 1v1h1l1-1c1 1 2 1 3 1 1 1 2 1 3 2l-1 1s-1 1-2 0h0-2v1c-1 0-3-2-4-3l-1-2h0v2l-1-1v-1l1-1z" class="L"></path><g class="C"><path d="M484 244c2-1 2-1 4-1 1 1 2 1 3 2s3 1 4 2l-1 1-1-1h-3c-1 1-1 1-3 0h-1-1c-1-1-1-2-1-3z"></path><path d="M495 247l1 1v1 1h0l1-1 2 2v2l-2-1-1 1v1c-1-1-2 0-3-1s-1-1-1-3h1c0-1 0-2 1-2l1-1z"></path></g><path d="M495 247l1 1v1 1h-2v-2l1-1z" class="P"></path><path d="M484 233s0-1 1-1c1-1 1 0 2 0l1 1c0 1-1 2-2 3l-3 4h-2v-2-1-1h-1c1-1 3-2 4-3z" class="D"></path><path d="M480 236c1-1 3-2 4-3-1 2-1 3-3 5v-1-1h-1z" class="l"></path><path d="M480 236h1v1 1 2l1 3h1v1h1c0 1 0 2 1 3h1 1v1h-2 0v1c-1 0-1 0-1 1h-1c0-2 0-2-1-4 0-1 0-1-1-2h0l-1 1h1v2h-1v-1c-1-1-2-5-1-7 0-1 0 1 0-1 1-1 1-1 1-2z" class="S"></path><path d="M487 247c2 1 2 1 3 0h3l1 1c-1 0-1 1-1 2h-1c0 2 0 2 1 3s2 0 3 1 1 1 1 2 0 0 1 1h0l-1 1c-1-1-2-1-3-2h0-1v-1c-2-1-3-2-4-3v-1h0v-1h-1v1h-2c-1-1-1-1-1-2v-1h0 2v-1z" class="L"></path><path d="M489 260v-3c1-1 2-1 3-2 0 2-1 2 1 3h2l1 1-1 1c1 1 1 1 2 1v-1l2 1v2c-1 1-1 1-2 1v1h-1 0c-1-1-1 0-2 0-2-1-4-2-6-4 0-1 0-1 1-1z" class="M"></path><path d="M489 260l1-1v-2h1v2l2 2 1 1c1 0 2 0 3 2l-1 1c-1-1-1 0-2 0-2-1-4-2-6-4 0-1 0-1 1-1z" class="j"></path><path d="M516 253h0 2v5h-1v1c-3 3-7 4-11 4-1 0-1 0-2-1h0l-2 1v-1-3l1-1h2c2-1 2-1 4-1l2-1 3 2c1-2 2-3 3-4l-1-1z" class="G"></path><path d="M504 262l-2 1v-1-3l1 1c2 1 3 1 5 1-2 0-3 1-4 0v1z" class="B"></path><path d="M509 257l2-1 3 2c-2 2-3 2-6 3-2 0-3 0-5-1l-1-1 1-1h2c2-1 2-1 4-1z" class="D"></path><path d="M502 259l1-1h2c2-1 2-1 4-1v2h-1c-1 0-2-1-3 0h-2v1l-1-1z" class="C"></path><path d="M487 268c1-2 0-5 0-7h1c2 2 4 3 6 4 1 0 1-1 2 0h0 1c1 1 3 0 4 1h-1c0 1-1 1-1 1-5 3-11 7-13 12v-5l-1-1h0c-1-2 0-4 0-5h2z" class="d"></path><path d="M485 268h2l-1 6-1-1h0c-1-2 0-4 0-5z" class="I"></path><path d="M503 234l3 1 1-1 1 1h1c0 2 0 2 1 3s1 1 2 1v2c1 1 2 1 2 2h1l1-1v2h-1s-1 0-2 1c1 1 1 1 1 2s-2 0-2 2h0c1 0 2-1 3-1 0 1 1 2 1 3h0l-2 1c-1 0-2 0-2-1h-1c1 1 1 1 1 2v1c-1 0-2 1-3 1 0 0-1 0-2 1h0c-1 0-1-1-1-1h-1c0 1 0 1-1 1h-1l1-2c-2-1-5-4-6-5-1-2-2-3-4-4-1-1-4-1-5-3 1-1 3-2 4-4h0l1-2h0l2 1 1 1h1v-1h1 1 4c0-1-1-2-1-3z" class="F"></path><path d="M501 246l1 1v1h-1c-1 0-2 0-2-1s1-1 2-1z" class="b"></path><path d="M512 254h0c-1 0-1-1-2-1h-1c-1 0-1-1-2-1l1-1h3c1 1 1 1 1 2v1z" class="J"></path><path d="M507 249c1-1 2-1 3-1 1 1 1 1 1 2-1 0-1 1-2 1s-2 0-3-1l1-1z" class="I"></path><path d="M495 243h2 4v1 1c-1 1-1 0-2 0s-1 1-2 1l-2-2v-1z" class="o"></path><path d="M493 238l1-2h0l2 1c0 2-1 2-1 4h-3v-1h1l1-1-1-1z" class="b"></path><path d="M496 237l1 1h1v1 1l1 1 1 1 1 1h0-4-2l1-1v-1h-1c0-2 1-2 1-4z" class="C"></path><path d="M504 237h1l-1 1h-3c-1 1-1 0-2 1v1c1 1 2 1 3 2h0l1 1h0v2h2c0 1 0 1-1 1v1 2h1c0 1-1 1-2 2h1v1h-1c-1-1 0-4 0-5-1-1-1-2-2-3v-1h0l-1-1-1-1-1-1v-1-1h-1 1v-1h1 1 4z" class="l"></path><path d="M514 243h1l1-1v2h-1s-1 0-2 1c1 1 1 1 1 2s-2 0-2 2h0c1 0 2-1 3-1 0 1 1 2 1 3h0l-2 1c-1 0-2 0-2-1l-1-1c0-1 0-1-1-2-1 0-2 0-3 1-1-1-1 0-1-1h0l-2-1v-1c1 0 1 0 1-1h-2v-2c1 1 2 1 3 1h0l1-1h1c1 0 1 1 2 2h0c0-1 0 0 1-1l1 1 1-1s1 0 1-1z" class="E"></path><path d="M510 245c0-1 0 0 1-1l1 1 1-1c0 1 0 1-1 2h-2v-1z" class="b"></path><path d="M506 244l1-1h1c1 0 1 1 2 2-1 1-2 1-3 1 0-1-1-1-1-2z" class="H"></path><path d="M503 234l3 1 1-1 1 1h1c0 2 0 2 1 3s1 1 2 1v2c1 1 2 1 2 2s-1 1-1 1l-1 1-1-1c-1 1-1 0-1 1h0c-1-1-1-2-2-2h-1l-1 1h0c-1 0-2 0-3-1h0l-1-1h0c-1-1-2-1-3-2v-1c1-1 1 0 2-1h3l1-1h-1c0-1-1-2-1-3z" class="K"></path><path d="M499 240c2-1 2 0 3-1h1 0l1 1v2h-2 0c-1-1-2-1-3-2z" class="f"></path><path d="M508 235h1c0 2 0 2 1 3s1 1 2 1v2c1 1 2 1 2 2s-1 1-1 1l-1 1-1-1c-1 1-1 0-1 1h0c-1-1-1-2-2-2h-1l-1 1h0c-1 0-2 0-3-1h0l-1-1h2 0c2-1 4-1 6-1v-2l-2-1v-1c-1 0-1 0-1-1l1-1z" class="L"></path><path d="M508 243l1-1h2 1c0 1 0 2 1 2l-1 1-1-1c-1 1-1 0-1 1h0c-1-1-1-2-2-2z" class="P"></path><path d="M279 547l1-1 1 1c4 4 4 23 4 29-1 3-1 6-1 9l-6 3c-2 1-5 4-6 6l-2 2c-1 2-3 5-4 7l-1 1-3-1c0 3-1 6-1 8-1 3-1 5-2 7l-1-1-1 4-4-4h-1l-3-3h-1c-1-2-3-5-4-8-1-1-2-3-3-5 1 0 1 0 2-1l1 2c0-7 3-13 6-19 1-2 2-4 3-5h1c1 0 2 0 4 1h2c0-1 1-1 1-2 1 0 2-1 2-2 1 0 2 1 2 0 1-1 2-2 3-2 0 0 1-1 2-1s1 0 2-1h1c4-5 5-12 7-18h0c0-1 0-2 1-3-1-1-1-2-2-3h0z" class="d"></path><path d="M268 585c1-2 4-6 5-6h1l-4 7-2 3-3 3 2-4c1-1 1-2 1-3h0z" class="B"></path><path d="M272 585c1-2 2-5 4-6h1c1 1 1 1 1 2-1 2-1 3-3 4v1l-2-1h0-1z" class="C"></path><path d="M257 596v1c-1 2-5 4-7 4h-1v-1c1-2 5-4 8-4z" class="h"></path><path d="M243 600l1 2s0 1 1 1l1 4c2 3 4 7 7 10h-1l-3-3h-1c-1-2-3-5-4-8-1-1-2-3-3-5 1 0 1 0 2-1z" class="Q"></path><path d="M243 600l1 2s0 1 1 1l1 4v2c0-1-1-2-2-3h0c-1-1-2-3-3-5 1 0 1 0 2-1z" class="H"></path><path d="M262 596h1l2-3h0v1c-1 1-1 1-1 2l1 1c-1 2-3 4-3 6 0 3-1 6-1 8-1 3-1 5-2 7l-1-1 4-21z" class="I"></path><path d="M270 586h0l2-1h1 0c-1 1-1 2-1 3l-4 4c-1 2-2 3-3 5l-1-1c0-1 0-1 1-2v-1h0l-2 3h-1c1-4 3-8 6-11h0c0 1 0 2-1 3l-2 4 3-3 2-3z" class="G"></path><path d="M270 572c1 0 1 0 2-1h1c-1 2-2 4-4 5-3 1-6 3-7 5-1 1-4 3-5 3h-1c2-2 3-2 4-5 0-1 1-1 1-2 1 0 2-1 2-2 1 0 2 1 2 0 1-1 2-2 3-2 0 0 1-1 2-1z" class="m"></path><path d="M275 586v-1l1 1h-1l1 1 1-1c1 1 1 1 1 2-2 1-5 4-6 6l-2 2c-1 2-3 5-4 7l-1 1-3-1c0-2 2-4 3-6s2-3 3-5l4-4c0-1 0-2 1-3l2 1z" class="h"></path><path d="M271 590l1 1h0v3l-2 2-2-2 3-4z" class="B"></path><path d="M275 586v-1l1 1h-1l1 1 1-1c1 1 1 1 1 2-2 1-5 4-6 6v-3h0l-1-1 3-3c1 0 1 0 1-1z" class="T"></path><defs><linearGradient id="AJ" x1="265.605" y1="600.882" x2="268.679" y2="594.813" xlink:href="#B"><stop offset="0" stop-color="#9a989c"></stop><stop offset="1" stop-color="#b6b4b5"></stop></linearGradient></defs><path fill="url(#AJ)" d="M268 594l2 2c-1 2-3 5-4 7h-1v-4h0c1-2 2-3 3-5z"></path><path d="M254 578c1 0 2 0 4 1h2c-1 3-2 3-4 5h1c-3 2-6 3-8 6-2 2-3 5-3 7-1 2-1 4-1 6-1 0-1-1-1-1 0-7 3-13 6-19 1-2 2-4 3-5h1z" class="R"></path><path d="M254 578c1 0 2 0 4 1h2c-1 3-2 3-4 5l-6 2c0-1 1-2 2-4 0-1 1-2 2-3v-1z" class="d"></path><path d="M281 547c4 4 4 23 4 29-1 3-1 6-1 9l-6 3c0-1 0-1-1-2l-1 1-1-1h1l2-2c1-3 3-6 4-9 1-5 1-10 1-16 0-4-1-8-2-12z" class="M"></path><path d="M258 544h7 1 1c2 2 4 2 6 3 2 0 3 0 4-1h1s0 1 1 1h0 0c1 1 1 2 2 3-1 1-1 2-1 3h0c-2 6-3 13-7 18h-1c-1 1-1 1-2 1s-2 1-2 1c-1 0-2 1-3 2 0 1-1 0-2 0 0 1-1 2-2 2 0 1-1 1-1 2h-2c-2-1-3-1-4-1h-1c-1 1-2 3-3 5-3 6-6 12-6 19l-1-2c-4-9-5-20-4-30 1-5 2-10 4-14l1-2c2-3 5-7 9-8 1-1 3-1 5-2h0z" class="d"></path><path d="M259 567c4-4 6-9 10-13v2c-1 1-2 3-3 4v1l-1 1s-1 2-2 2v1l-2 2h-2z" class="Q"></path><path d="M257 545c2 0 5 0 7 1 2 0 5 2 7 2 1-1 1-1 2 0h0l-2 1c-1 2-3 3-4 4l-10-8z" class="M"></path><path d="M252 552h4c0 1 1 1 1 2 0 2 0 3-1 5-3 3-8 3-12 3 1-1 2-3 4-4h1c1 0 3 0 4-1 1 0 2-1 2-2s0-1-1-2c-3 0-7 3-10 4 2-3 5-4 8-5z" class="I"></path><path d="M259 567h2l2-2v-1c1 1 1 1 2 1h0l1 2-4 4c-1 0-1-1-2-1v1h1c-1 1-1 2-2 2h-1v1h1l-1 2-1 1h0c1 1 1 1 1 2-2-1-3-1-4-1h-1 0v-1l-3-1-6-4-2-2c3-3 8-2 12-1h2c1 0 2-1 3-2z" class="H"></path><path d="M258 574h1l-1 2-1 1h0c1 1 1 1 1 2-2-1-3-1-4-1h-1 0v-1l-3-1h4v1c1-2 2-2 4-3z" class="I"></path><path d="M279 547h0 0c1 1 1 2 2 3-1 1-1 2-1 3h0c-2 6-3 13-7 18h-1c-1 1-1 1-2 1s-2 1-2 1c-1 0-2 1-3 2 0 1-1 0-2 0 0 1-1 2-2 2 0 1-1 1-1 2h-2c0-1 0-1-1-2h0l1-1 1-2h-1v-1h1c1 0 1-1 2-2h-1v-1c1 0 1 1 2 1l4-4-1-2h0c-1 0-1 0-2-1 1 0 2-2 2-2l1-1v-1c1-1 2-3 3-4v-2c2-3 4-5 7-6h2l1-1z" class="i"></path><path d="M266 569l3-1-3 3h-2l2-2z" class="j"></path><path d="M258 579c0-1 0-1-1-2h0l1-1h1l-1 1h1 2c0 1-1 1-1 2h-2z" class="t"></path><path d="M266 569c1-1 1-2 2-3 1 0 3-1 4-1l-3 3-3 1z" class="g"></path><path d="M264 571h2c-1 1-2 2-3 4-2 0-3 1-4 1h-1l1-2c1 0 2-1 3-2l2-1z" class="a"></path><path d="M272 557c1-1 2-3 2-3 1-1 2-1 2-1 1 0 2-1 2-2-1 2-1 5-3 7 0 1-1 2-1 3h-3c0-1 1-2 1-3l1-1h-1z" class="H"></path><defs><linearGradient id="AK" x1="272.486" y1="570.495" x2="276.404" y2="557.631" xlink:href="#B"><stop offset="0" stop-color="#888"></stop><stop offset="1" stop-color="#abaaab"></stop></linearGradient></defs><path fill="url(#AK)" d="M270 572l3-6 5-10c0-1 0-3 1-4l1 1c-2 6-3 13-7 18h-1c-1 1-1 1-2 1z"></path><path d="M272 557h1l-1 1c0 1-1 2-1 3h3c-1 1-2 3-2 4-1 0-3 1-4 1-1 1-1 2-2 3l-2 2-2 1c-1 1-2 2-3 2h-1v-1h1c1 0 1-1 2-2h-1v-1c1 0 1 1 2 1l4-4-1-2c1-1 2-2 3-2 2-2 2-4 4-6h0z" class="R"></path><path d="M265 565c1-1 2-2 3-2 0 0 1 0 1 1l-2 1c-1 1-1 1-1 2l-1-2z" class="N"></path><path d="M276 548h2c1 1 1 1 1 2h1 0l-2 1c0 1-1 2-2 2 0 0-1 0-2 1 0 0-1 2-2 3h0c-2 2-2 4-4 6-1 0-2 1-3 2h0c-1 0-1 0-2-1 1 0 2-2 2-2l1-1v-1c1-1 2-3 3-4v-2c2-3 4-5 7-6z" class="F"></path><path d="M276 548v3h0c-1 1-3 3-4 3h-3c2-3 4-5 7-6z" class="B"></path><defs><linearGradient id="AL" x1="508.648" y1="469.27" x2="491.527" y2="444.372" xlink:href="#B"><stop offset="0" stop-color="#171617"></stop><stop offset="1" stop-color="#464646"></stop></linearGradient></defs><path fill="url(#AL)" d="M495 395h-1c0-2-2-3-2-5h0c0-1-1-2-1-2-1 0-1-1-1-1v-1l1 1h1c0 1 0 2 1 2 0 1 1 2 1 3 1 1 1 2 3 3v-1c-1-2-2-4-3-5v-1c1 1 1 1 2 1l1-1v1c1 0 1 1 1 1 0 1 0 1 1 1v1c-1 0-1 0-1-1l-1 1c0 1 1 1 1 2l2-1v1c0 1 1 2 2 3v-2c0-1 2-2 2-3h0 1c0 1 1 1 1 2l-1 2 1 1 1-1v1c1-1 1-1 1-2h1v1l1 1-1 2v1h1v-1l1-2c1 0 1 0 2 1h0l1-1 2 4v2s-1 1-2 1c0 0 1 1 1 2s-1 1 0 3l3 8c1 1 1 0 1 1h-1c0 1 1 2 1 2v1l1 1c-1 1-1 3-1 4h0v-3h0 0-2v1h-1c0 2 1 5 2 7v2c1 1 1 1 1 2l-1 2-1 8c0 2-1 3-1 5 0 1 0 3-1 4v3c-1 1 0 1-1 2l-1-1v1s0 1-1 1v1c0 1-2 3-3 4h1 1s0 1 1 1c-2 3-5 4-7 6-1 0-1 0-1 1h-1l-1-1 1-1c-1 0-2 0-2-1-3 1-5 2-7 4-1 0-2 1-3 2h0c-1 1-2 2-2 3h-1l-1-2c-1 0-1-1-2-1l3-5 1 2v1l1-1h-1c2-1 5-6 6-8s2-3 1-5h0c0-1 1-1 1-2v-1-1h0l1-1v-3h1v1h1v-1c1-1 1-1 1-2h0 1v2c0 1 0 2 1 3h1c0-2-1-4-1-6-1-1-1-1-1-2 0-2 0-6 1-7v1h0v1c1 0 1-1 2-1 0-2 0-6-1-7l-1-1v-3l-1-3 1-1h-1c-1-2-1-4-2-6l-3-9-1 1h0c0-1 0-1-1-2v-1l1-1v-1h0l-2-2c1-1 2-1 2-2h0 1c0-1 1-2 1-3h0-1v2h0-1v-2l-1-1v-2-1-1z"></path><path d="M508 445c0-2 0-2 1-3 0 1 0 1 1 2-1 0-1 0-2 1z" class="W"></path><path d="M508 431v1l-2 2c0 5 0 11-2 16 0-2 0-7-1-8 1 0 1-1 2-1 0-2 0-6-1-7 1-1 2-2 4-3z" class="c"></path><defs><linearGradient id="AM" x1="506.774" y1="444.848" x2="517.35" y2="433.197" xlink:href="#B"><stop offset="0" stop-color="#393839"></stop><stop offset="1" stop-color="#555556"></stop></linearGradient></defs><path fill="url(#AM)" d="M511 439v-1c1-2 1-5 1-7-1-1-1-2-1-3-1-4-2-8-2-12-1-1-1-2 0-4l1 5h1l1 4c0 1 0 2 1 2 0 4 1 7 2 11h1v5c-1 0-1 0-1-1-1 1-1 3-1 4l-1 8c-1 0-1 1-1 1 0 1 0 2-1 3v-2h0c-1-1-1-2-1-3 0 0 0-1-1-1l-1-3c1-1 1-1 2-1-1-1-1-1-1-2l1-3h1z"></path><path d="M511 452c0-1-1-4 1-5v4c0 1 0 2-1 3v-2h0z" class="i"></path><path d="M511 439v-1c1-2 1-5 1-7-1-1-1-2-1-3-1-4-2-8-2-12-1-1-1-2 0-4l1 5v1c0 1 1 2 1 3 3 7 1 13 1 20v5h-1v-2h-1c-1-1-1-1-1-2l1-3h1z" class="V"></path><path d="M510 439h1v5h-1c-1-1-1-1-1-2l1-3z" class="i"></path><path d="M497 403l1 1v-1l1-1v1c0 1 0 1-1 2 0 0 0 1-1 2h1 1v1c1-1 2-2 3-2s2 0 2 1l2-1v1 1h-1c1 0 1 0 2 1l1 1s1 1 1 2c-1 2-1 3 0 4 0 4 1 8 2 12 0 1 0 2 1 3 0 2 0 5-1 7v1h-1c-1 0-1 0-1-1h0c-1-2-1-4-1-6h0v-1c-2 1-3 2-4 3l-1-1v-3l-1-3 1-1h-1c-1-2-1-4-2-6l-3-9-1 1h0c0-1 0-1-1-2v-1l1-1v-1h0l-2-2c1-1 2-1 2-2h0 1z" class="k"></path><path d="M504 415v-1h1v1c1 0 1-1 2-1 0 1 1 3 1 4v3c1 1 2 4 2 5 0 2-1 4-1 5s0 1-1 1h0v-1-1h-1c-1-1-1-2-1-4-1 0-1-1-1-1v-3c1-2 1-3 1-4l-1-1v3-3c-1 0-1-1-1-2z" class="c"></path><path d="M508 418v3c1 1 2 4 2 5 0 2-1 4-1 5s0 1-1 1h0v-1-1l-2-7 3 5c0-2-1-4-1-7v-1h-1c0-1 1-2 1-2z" class="V"></path><path d="M503 426c1-2 1-2 1-3-1 0-1-1-2-1 0-1-1-2-1-3 0-2-1-3-2-6h1c0 1 1 3 1 3 1-1 1-1 0-2l1-1c1 1 1 1 1 2h1c0 1 0 2 1 2v3-3l1 1c0 1 0 2-1 4v3s0 1 1 1c0 2 0 3 1 4h1v1c-2 1-3 2-4 3l-1-1v-3l-1-3 1-1z" class="W"></path><path d="M504 415c0 1 0 2 1 2v3h-1v-1c-1-1-1-2-1-4h1z" class="Z"></path><path d="M503 426h1v4h1c1-1 0-2 1-4 0 2 0 3 1 4h1v1c-2 1-3 2-4 3l-1-1v-3l-1-3 1-1z" class="r"></path><path d="M495 395h-1c0-2-2-3-2-5h0c0-1-1-2-1-2-1 0-1-1-1-1v-1l1 1h1c0 1 0 2 1 2 0 1 1 2 1 3 1 1 1 2 3 3v-1c-1-2-2-4-3-5v-1c1 1 1 1 2 1l1-1v1c1 0 1 1 1 1 0 1 0 1 1 1v1c-1 0-1 0-1-1l-1 1c0 1 1 1 1 2l2-1v1c0 1 1 2 2 3v-2c0-1 2-2 2-3h0 1c0 1 1 1 1 2l-1 2 1 1 1-1v1c1-1 1-1 1-2h1v1l1 1-1 2v1h1v-1l1-2c1 0 1 0 2 1h0l1-1 2 4v2s-1 1-2 1c0 0 1 1 1 2s-1 1 0 3l3 8c1 1 1 0 1 1h-1c0 1 1 2 1 2v1l1 1c-1 1-1 3-1 4h0v-3h0 0-2v1h-1c0 2 1 5 2 7v2c1 1 1 1 1 2l-1 2-1 8c0 2-1 3-1 5 0 1 0 3-1 4v-5c1-3 0-6 1-8 1-3 1-5 0-8h0v1h-1c-1-4-2-7-2-11-1 0-1-1-1-2l-1-4h-1l-1-5c0-1-1-2-1-2l-1-1c-1-1-1-1-2-1h1v-1-1l-2 1c0-1-1-1-2-1s-2 1-3 2v-1h-1-1c1-1 1-2 1-2 1-1 1-1 1-2v-1l-1 1v1l-1-1c0-1 1-2 1-3h0-1v2h0-1v-2l-1-1v-2-1-1z" class="Z"></path><path d="M495 395h1c1 1 1 1 1 2h-1l-1-1v-1zm18 3l1-1 2 4v2s-1 1-2 1v-1c-1-1-1-1-1-2 1 0 1-1 1-2l-1-1h0z" class="k"></path><path d="M510 408c1 1 2 2 2 3 1 2 2 4 2 7l2 6c0 2 1 5 2 7v2c1 1 1 1 1 2l-1 2c0-2-1-5-2-7-2-7-3-15-6-22z" class="V"></path><path d="M510 408v-1h0l-2-2v-1c0-1-1-1-1-2l1-1c0 1 1 2 2 3 0-1 0-1 1-1v2l1 1s1 1 1 2 0 2 1 3l3 7c-1 0-1 1-1 2v1c1 1 1 2 1 3h-1l-2-6c0-3-1-5-2-7 0-1-1-2-2-3z" class="t"></path><path d="M514 403v1s1 1 1 2-1 1 0 3l3 8c1 1 1 0 1 1h-1c0 1 1 2 1 2v1l1 1c-1 1-1 3-1 4h0v-3h0 0-2v1c0-1 0-2-1-3v-1c0-1 0-2 1-2l-3-7c-1-1-1-2-1-3s-1-2-1-2l2-3z" class="W"></path><path d="M514 403v1s1 1 1 2-1 1 0 3l3 8c1 1 1 0 1 1h-1l-5-10c0-1-1-2-1-2l2-3z" class="a"></path><path d="M511 417c0-1-1-2-1-4h1l3 11v1c1 2 1 4 2 5h0c1 2 2 5 2 7l-1 8c0 2-1 3-1 5 0 1 0 3-1 4v-5c1-3 0-6 1-8 1-3 1-5 0-8h0v1h-1c-1-4-2-7-2-11-1 0-1-1-1-2l-1-4z" class="W"></path><defs><linearGradient id="AN" x1="553.486" y1="486.444" x2="516.827" y2="423.082" xlink:href="#B"><stop offset="0" stop-color="#161516"></stop><stop offset="1" stop-color="#3f3f3f"></stop></linearGradient></defs><path fill="url(#AN)" d="M516 400l1 1v1l4 5c1 1 1 1 1 2 1 1 1 2 2 3l1 2 1-1 2 1-1 2s1 1 1 2c1-1 1-1 1-2l2 2c1 1 1 2 2 3s1 1 1 2l1 1c1 1 1 1 2 1h1v1h-1 0c2 3 2 5 3 8h0v3h0-1v-1 1h-1-1v-2c-1 0-1-1 0-2h-1c0 1 1 1 0 3h0-1v-1l-1-1c0 1 1 3 1 4 1 0 1 1 1 1v1l1 1c1 1 1 2 2 3l-1 1c-1 0-1 0-2-1v2c1 0 1 1 1 1 1 2 1 3 2 4 0 1 1 1 1 2 1 1 2 2 2 3l2 2h0l1 2h0c-1 0-2-1-2-2l-2-2h0l6 11h0c-1-1-10-14-11-16l-3-6c-1-1-1 0-1-1s0-1-1-2h0v-1 1h0c0 1 1 3 1 4 2 4 4 9 6 12l1 1 3 4 4 6-1 1-1-1v1h0l1 1v3c2 2 4 5 5 7v1s0 1 1 1c1 1 1 3 2 4h0-1-2v-2h-1c-1 0-1 0-1-1h-1 0v1c1 1 1 2 1 3l1 2h0l1 3c1 2 2 4 2 6v3c-1-2-1-3-2-5v1c-2-3-2-6-3-9-1-1-2-3-3-4h-1c0 1 2 2 2 4 1 1 1 3 1 4l-6-12-1 1c0-1-1-2-2-3-1-2-5-5-7-6 2 2 3 4 4 6l-1 1-1-3c-1-1-2-2-3-4h-3 0-1l1 2h-1c-1 0-2-1-3-2h-1c0-1-1-1-1-2-1 0-3 1-5 0-1 0-1 1-2 1s-1-1-2-1c-2 0-4 2-6 3 0 0-2 0-2-1-2 0-1 0-1-2h-1v-1c0 1 1 1 2 1l-1 1 1 1h1c0-1 0-1 1-1 2-2 5-3 7-6-1 0-1-1-1-1h-1-1c1-1 3-3 3-4v-1c1 0 1-1 1-1v-1l1 1c1-1 0-1 1-2v-3c1-1 1-3 1-4 0-2 1-3 1-5l1-8 1-2c0-1 0-1-1-2v-2c-1-2-2-5-2-7h1v-1h2 0 0v3h0c0-1 0-3 1-4l-1-1v-1s-1-1-1-2h1c0-1 0 0-1-1l-3-8c-1-2 0-2 0-3s-1-2-1-2c1 0 2-1 2-1v-2-1z"></path><path d="M520 422l1 3v2 1l-2-2c0-1 0-3 1-4z" class="r"></path><path d="M537 429h-1v1h0-1l-1-1c0-1-1-2 0-3v1-1c1 0 1 1 2 2 0-1 0-1 1-2h0l-1 1v1l1 1z" class="V"></path><path d="M545 474c2 2 4 5 5 7h-1c-1 0-2-1-2-2-1-2-2-3-2-4v-1z" class="q"></path><path d="M541 456h0c-2-1-3-3-4-6-3-3-4-8-6-12h0c1 1 2 3 3 4v1c1 1 1 2 2 3h0c1 0 1 1 1 1 1 2 1 3 2 4 0 1 1 1 1 2 1 1 2 2 2 3l2 2h0l1 2h0c-1 0-2-1-2-2l-2-2h0z" class="v"></path><path d="M540 481h0c-1-2-2-3-3-4h0c1 0 1 0 1 1l1 1v-3c1 1 1 2 2 3l1-1c1 1 1 1 1 2 1 3 3 5 4 8l3 9v1c-2-3-2-6-3-9-1-1-2-3-3-4h-1c0 1 2 2 2 4 1 1 1 3 1 4l-6-12z" class="V"></path><path d="M537 429l-1-1v-1l1-1c2 3 2 5 3 8h0v3h0-1v-1 1h-1-1v-2c-1 0-1-1 0-2h-1c0 1 1 1 0 3h0-1v-1l-1-1c0 1 1 3 1 4 1 0 1 1 1 1v1l1 1c1 1 1 2 2 3l-1 1c-1 0-1 0-2-1s-2-3-3-6c0 0 0-1-1-1 0-1 0-1 1-1 1 1 1 2 1 2h1c0-1-1-3-2-5l2 1c0-1 1-1 1-2l1 1c1-2 0-3 0-4z" class="e"></path><path d="M516 400l1 1v1l4 5c1 1 1 1 1 2 1 1 1 2 2 3l1 2 1-1 2 1-1 2s1 1 1 2c1-1 1-1 1-2l2 2c1 1 1 2 2 3h0c-1 1-1 2 0 3v1h-1l-1-1h0v2h-1c0-1-1-4-2-5v3c-1 0-1 0-1 1 0-1 0-1-1-2h0l-1-1h0 0c-1-1-1-2-2-3h-1c-1 3 1 4-1 6l-1-3-1-1v-1s-1-1-1-2h1c0-1 0 0-1-1l-3-8c-1-2 0-2 0-3s-1-2-1-2c1 0 2-1 2-1v-2-1z" class="i"></path><path d="M525 422v-2l1-1v2c1 1 1 2 2 3-1 0-1 0-1 1 0-1 0-1-1-2h0l-1-1zm-6-10c0-1-1-1-1-2v-1h0c1 1 2 2 2 3s1 1 1 2c0 0 1 1 1 2 1 1 1 1 1 2h-1c-1-1-2-2-2-3-1-1-1-1-1-2v-1h0z" class="r"></path><path d="M516 400l1 1v1l4 5c1 1 1 1 1 2 1 1 1 2 2 3l1 2v1l-2-2c-1 0-1 0-2 1 0-1-1-1-1-2s-1-2-2-3h0v1c0 1 1 1 1 2v2h-1c0-2-1-4-2-6l-1 1c-1-2 0-2 0-3s-1-2-1-2c1 0 2-1 2-1v-2-1z" class="Z"></path><path d="M516 450h1c0 2-1 5 0 7 0 0 1 1 1 2h1v1c1-1 1-3 2-5v5h1c0 1 0 1 1 2h0c0 1 0 1 1 2 0 2 1 4 3 5h0 1 0l3 3h0-1l-1 1h1c2 2 3 4 4 6l-1 1-1-3c-1-1-2-2-3-4h-3 0-1l1 2h-1c-1 0-2-1-3-2h-1c0-1-1-1-1-2-1 0-3 1-5 0-1 0-1 1-2 1s-1-1-2-1c-2 0-4 2-6 3 0 0-2 0-2-1-2 0-1 0-1-2h-1v-1c0 1 1 1 2 1l-1 1 1 1h1c0-1 0-1 1-1 2-2 5-3 7-6-1 0-1-1-1-1h-1-1c1-1 3-3 3-4v-1c1 0 1-1 1-1v-1l1 1c1-1 0-1 1-2v-3c1-1 1-3 1-4z" class="n"></path><path d="M518 464h1v1c-1 1-2 2-3 2h-1l3-3z" class="v"></path><path d="M501 470v1h1c0 2-1 2 1 2 0 1 2 1 2 1 2-1 4-3 6-3 1 0 1 1 2 1s1-1 2-1c2 1 4 0 5 0 0 1 1 1 1 2h1c1 1 2 2 3 2h1l-1-2h1 0 3c1 2 2 3 3 4l1 3 5 15 1 1h1v1 1 4h-1c1 2 1 6 2 7v2h-1v-1-1h0v1h-1v-1h-1v2c1 1 1 1 1 2-1 3 0 7-1 10l1 2h-1v-3h-1 0v-1h-1-1c-1 1 0 4-2 4l1-1c0-1 0-2-1-2l-1-1h0 0-1c-1 0-1 0-2-1h0l-2-2c-1 2-1 6-2 8v-2l-1-1-1 1h0l-1-1c0 1 0 1 1 1 0 1 0 2-1 3l1 1v1h0l-1-2h0c-1 0-1 0-2-2l-1 3v1h-1c0-1 0-2-1-3v2h-1c0-2 1-4 1-7h-1c0-2 1-4 1-5l-1-1v1c0 1 0 2-1 3h0c0 1-1 0-1 1l-2 8v-5l-1-1h0c-1-1-1-2 0-3v-1l1-1h0c1-3 0-5 0-8h0v-1c0 1 0 1-1 2h0v2c-2 1-1 5-1 7l-1 1v-9-1-4-1c0 1 0 1-1 1h-1 0c0 1 0 1-1 2 0-1 1-2 0-3 0-1 0-2-1-2h-1 0c0 1 0 1-1 2-1 0-1-1-1-1v-3h0c-1 0-1 1-1 2s0 0-1 1c-1-1 0-3 0-4l-1-1v-3-1h0l1 1v-1h1c0-1 1-3 2-4v-4c-2 0-2 0-3-2v-1h-1v-2l2-3h-1 0l-1 1c0-2-1-2-2-2h0l-1-1c1 0 1 0 2-1 0-1 1-2 2-3 1 0 1 0 1-1h-1c-1 1-3 2-4 3l-2-1c2-2 4-3 7-4z" class="r"></path><path d="M515 491h-1c-1-1-1-2-1-3l1-1c1 1 1 2 1 3v1z" class="W"></path><path d="M517 477c1 0 1 0 1 1s-1 3-1 5h-1c0-2 1-4 1-6z" class="i"></path><path d="M503 491c0 1-1 2 0 3h1v2h-1v-1c-1 0-1 1-2 0 0-1 1-3 2-4z" class="W"></path><path d="M505 475h1v1c-1 1-2 2-2 3-1 0-1-1-2-2h0 2v-2h1z" class="i"></path><path d="M504 480h1v1c-1 1-1 2-1 3v1h-1v-1c-1-1-1-2-1-3v-1l2 1v-1z" class="q"></path><path d="M515 493v1c0 1 0 3 1 4h0c1 3-1 9 1 12h-1c-1-1 0-3 0-5l-1-1c0-2 1-4 0-5v-6z" class="e"></path><path d="M515 490c1-1 1-4 2-5 0 4-1 8-1 13h0c-1-1-1-3-1-4v-1-2-1z" class="q"></path><path d="M512 509c1 2 1 5 1 7v1c0 1-1 2-1 3v1-2c1 0 0 0 1 1 0 1 0 2-1 3l-1-1h0c-1-1-1-2 0-3v-1l1-1h0c1-3 0-5 0-8z" class="n"></path><path d="M510 482c1 2 0 3 2 4h0v-2h1v2l-1 1v2h-1v-1c0-1 0 0-1-1 0-2 0-3-1-5 0 5-1 9-1 14h-2v-1h0v-4c1-2 1-4 2-6 0-1 0-1 1-2 0 0 1 0 1-1z" class="W"></path><path d="M509 474c1 0 2-1 3 0 0 1-1 2-1 4l-1 4c0 1-1 1-1 1 0-1 1-3 1-5h0c-1 1-3 5-3 7 0 0 0 1-1 2 0 2 0 3-1 5 0 1 0 1-1 1v-2c1-3 2-7 3-10 1-2 2-4 2-7z" class="c"></path><path d="M501 470v1h1c0 2-1 2 1 2 0 1 2 1 2 1 2-1 4-3 6-3 1 0 1 1 2 1s1-1 2-1c2 1 4 0 5 0l-3 3c-1 0-1 0-2-1l-1 1-1-1s0 1-1 1c-1-1-2 0-3 0s-3 0-4 1h-1-2-1c0 1 0 1-1 1h-1l-2 2h0l-1-1c1 0 1 0 2-1 0-1 1-2 2-3 1 0 1 0 1-1h-1c-1 1-3 2-4 3l-2-1c2-2 4-3 7-4z" class="q"></path><path d="M519 504c0 1 0 2 1 3-1 2-1 5-1 7l-1 1v2l-1 4h-1c0-2 1-4 1-5l-1-1v1c0 1 0 2-1 3h0c0 1-1 0-1 1l-2 8v-5c1-1 1-2 1-3v-1h1c-1-2 0-5 0-7 1-3 1-6 1-8l1 1c0 2-1 4 0 5h1c0-1 0-2 1-3 0-2 0-2 1-3z" class="s"></path><path d="M537 508c-1-4-1-8-1-13-1-2-2-4-2-7-1-4-2-6-4-10-1 0-1-1-1-1v-1l2 3 1-2 1 3 5 15 1 1h1v1 1 4h-1c1 2 1 6 2 7v2h-1v-1-1h0v1h-1v-1-3h-1v2h-1z" class="u"></path><path d="M538 495l1 1h1v1 1 4h-1l-2-6h1v-1z" class="V"></path><path d="M532 477l1 3 5 15v1h-1c-1-3-2-7-3-10 0-2-1-5-3-7l1-2z" class="i"></path><path d="M529 480h1c2 3 2 5 2 8 1 2 1 4 2 6v4c0 1 1 2 1 4l-1 6c0 1 0 3-1 5v7 1c1-2 1-5 1-7l2-2 2 2c0-2 0-3-1-4v-1-1h1v-2h1v3h-1v2c1 1 1 1 1 2-1 3 0 7-1 10l1 2h-1v-3h-1 0v-1h-1-1c-1 1 0 4-2 4l1-1c0-1 0-2-1-2l-1-1h0 0-1c-1 0-1 0-2-1 1-1 1-10 1-12 1 0 1 0 1-1v-3c2-2 2-2 2-4 0-6-1-14-4-20z" class="q"></path><path d="M521 479l-1-1v-2-1l2 1c1 0 1 1 2 2h0c0 1 0 1 1 1v1-1-2c1 1 3 3 4 3 3 6 4 14 4 20 0 2 0 2-2 4v3c0 1 0 1-1 1 0 2 0 11-1 12h0l-2-2c-1 2-1 6-2 8v-2l-1-1-1 1h0l-1-1c0 1 0 1 1 1 0 1 0 2-1 3l1 1v1h0l-1-2h0c-1 0-1 0-2-2l-1 3v1h-1c0-1 0-2-1-3v2h-1c0-2 1-4 1-7l1-4v-2l1-1c0-2 0-5 1-7-1-1-1-2-1-3v-4c0-3 0-9 2-12 1 3 1 5 1 8v3s1 0 1 1l1-1v-4c-1-2 0-5-1-8-2 0 0 0-2 1 0-2 1-3 1-4h0l-1-1v-4z" class="e"></path><path d="M524 523v-3h1v4l-1-1zm-5-23v1c1 0 1 0 1 1 1 1 0 3 0 5-1-1-1-2-1-3v-4z" class="n"></path><path d="M522 512h1c1 0 1 1 1 2-1 0-1 1-1 2h0c-1 1 0 1-1 1l-1-1 1-4zm3-19h1c0 3 1 6 1 9-1-1-1-2-1-3-1-2-1-3-1-4 1-1 0-1 0-2z" class="r"></path><path d="M517 526l1-6h1c1 2 0 4 0 6 1-1 1-2 1-2v-3-1l1 1v5l1 1 1 1v1h0l-1-2h0c-1 0-1 0-2-2l-1 3v1h-1c0-1 0-2-1-3z" class="n"></path><path d="M521 479l-1-1v-2-1l2 1c1 0 1 1 2 2h0c0 1 0 1 1 1v1c1 3 3 7 2 10l-1 1c0-3 0-6-2-8 0 1 1 3 1 5h-1v-1c-1 0-1 0-1-1s0-1-1-2h0l-1-1v-4z" class="W"></path><path d="M521 479h0 1v-1c1 2 1 3 1 4-1 1-1 1-1 2l-1-1v-4z" class="i"></path><path d="M525 479v-2c1 1 3 3 4 3 3 6 4 14 4 20 0 2 0 2-2 4v3c0 1 0 1-1 1 0 2 0 11-1 12h0c-1-6 1-12 1-19-1 0-1-1-2-2 0-1 0-2-1-3v-4c-1 0-1-1-1-1l1-1c1-3-1-7-2-10v-1z" class="W"></path><path d="M532 500c0 1 0 0 1 0 0 2 0 2-2 4v-3l1-1zm-7-20v-1c1 1 2 2 2 4 1 3 2 6 0 10v3-4c-1 0-1-1-1-1l1-1c1-3-1-7-2-10z" class="V"></path><path d="M531 501c-1-2-1-3-1-4 0-3-2-13-2-15 1 1 1 3 2 4l2 11v3l-1 1zm38-43l1-2 2 10c1 3 1 7 3 10h0 1l2 13c1-1 1-6 1-6 0 1 0 2 1 3h0v-1c1 0 0-1 0-2h0c1 1 1 2 1 3v4l2 13c0 1 0 1 1 2v5c1 4 2 8 4 12 1 3 1 6 2 8s1 4 2 6c2 6 6 11 10 15 0 1 3 3 3 3 1 1 2 3 4 4 2 0 4 2 6 3 0 0 2 1 2 2l9 3c3 1 6 3 10 3h0 2c1 0 1 1 2 0h-1l1-1c2 0 3 0 5-1h0c1-1 4-1 6-1l1 1 6-2v1l3-1v1c-1 0-1 1-2 1s-1 0-2 1h1c2 0 4-1 5-2 3-1 5-3 8-4 1-1 4-4 6-4h0c1 0 2-1 3-2s3-3 4-5h1l-2 2h1v1h0 1 0 0l1-1 1-1h0l-2 3h1l-1 1h1l1-1 3-4v1c-2 1-3 3-4 5s-3 3-4 5h-1c-1 1 0 1-1 1-1 1-4 4-5 4 0 1-1 1-2 2s-2 2-3 2h0v1c-1 0-1 0-2 1-1 0-2 1-3 1l-1 1h3c-1 0-1 1-3 1h0c-3 1-6 3-9 3-2 0-3 1-4 1h-1-3s1 0 2 1h-1c-1 0-1 0-2 1h-7 0c1 0 6-1 8 0-2 2-6 1-8 1h-2c-1-1-4-1-4-2-1 1-2 1-3 1v1c-1 0-3-2-4-2l-11-5c-4-3-7-4-10-8-3-3-7-6-9-9s-3-6-5-8h-1c1 0 1 0 2-1-1-1-2-1-2-2l-1-1v1 1 1h0c-1-1-2-3-2-4-2-4-3-7-5-11l-2-7h0c-2-3-2-8-3-11v-3l-2-13-1-5c0-1 0-1-1-2v-1c0-4-1-7-1-10h0l-1-6-1-2v-1c0-1 1-1 1-2l1 1h0l-3-15z" class="q"></path><path d="M645 567c2 0 3 0 4 1l-1 1c-1 0-1 0-2-1l-1-1h0z" class="W"></path><path d="M645 567c1-1 4-1 6-1l1 1-3 1c-1-1-2-1-4-1z" class="c"></path><path d="M642 576l-1-1v-1h-1v-1h3c1 1 3 1 4 2-1 1-1 1-2 1h-3z" class="r"></path><path d="M645 576h1 5 0l1 1c1 0 3 0 4-1h1c5 0 8-3 13-5h0v1c-1 0-1 0-2 1-1 0-2 1-3 1l-1 1h3c-1 0-1 1-3 1h0c-3 1-6 3-9 3l-1-1c-3-1-6-2-8-1-1 1-1 1-2 1-3-1-6-1-8-1-1 0-2-1-3-1 0 0 0 1-1 0h1v-1l-1 1v-1h1c2 1 7 1 9 1h0 3z" class="u"></path><path d="M682 557h1c-2 2-5 4-7 6-1 1-2 2-3 2l-9 5c-1 1-2 2-4 2l-4 2h-4v-3h-2 0c-1 0-2-1-3-1h0 1 4c2 1 4 2 5 1 2-1 3-2 4-2h1c-1 1-2 1-3 2 0 0-1 0-1 1 3 0 5-3 7-4 3-1 6-2 9-4 2-2 5-5 8-7z" class="V"></path><path d="M609 558c2 0 4 2 6 3 0 0 2 1 2 2l9 3c3 1 6 3 10 3h-1c0 1-9 1-10 1l-1-1h-1l-1-1c-1 0-1 0-2-1h0c0-1-3-2-4-2-2-1-6-4-6-6l-1-1z" class="e"></path><path d="M609 558c2 0 4 2 6 3 0 0 2 1 2 2 1 1 2 2 3 2l5 3h0c-2 0-3-1-5-1 0-1-3-2-4-2-2-1-6-4-6-6l-1-1z" class="W"></path><path d="M577 514h0l1 1h0v-5c-1-2-2-6 0-8 0 2 0 4 1 6v3 1h1c1 1 1 2 1 3-1 1-1 2 0 2l3 10v2c0 1 1 3 2 4 0 1 0 1 1 2l-1 1h0c0-1 0-1-1-2v-2h-1v2h-1 0v-1c0-1-1-1-1-2s-1-2-2-3h0 0c-2-3-2-8-3-11v-3zm33 53l-2-2h0c2 0 5 3 6 4 3 1 6 2 8 4 1 0 3 2 4 2h1l-1-2h1c1 1 2 1 3 1v1h0c-2 0-2 1-3 1 2 2 4 2 7 3 1-1 1-1 2-1l1 1 1 1v-1-1h2l1 1s1-1 1 0c2 0 3 1 5 1 0 0 1 0 2 1h-1c-1 0-1 0-2 1h-7 0c1 0 6-1 8 0-2 2-6 1-8 1h-2c-1-1-4-1-4-2-1 1-2 1-3 1v1c-1 0-3-2-4-2l-11-5c-4-3-7-4-10-8h2c0 1 1 2 2 2l1 1c1 0 1 0 1-1l-2-1s-2-1-2-2h1v-1l1 2 1-1z" class="u"></path><path d="M609 569s-2-1-2-2h1v-1l1 2 1-1 9 5h-1l2 1v1l-4-3-6-3-1 1h0z" class="s"></path><defs><linearGradient id="AO" x1="619.694" y1="583.37" x2="617.514" y2="566.756" xlink:href="#B"><stop offset="0" stop-color="#161417"></stop><stop offset="1" stop-color="#282926"></stop></linearGradient></defs><path fill="url(#AO)" d="M605 568h2c0 1 1 2 2 2l1 1c1 0 1 0 1-1 2 1 4 2 5 3 1 0 2 1 3 1l2 2h1v1c1 0 1 0 2 1 2 1 6 3 9 3-1 1-2 1-3 1v1c-1 0-3-2-4-2l-11-5c-4-3-7-4-10-8z"></path><path d="M569 458l1-2 2 10c1 3 1 7 3 10h0 1l2 13c1-1 1-6 1-6 0 1 0 2 1 3h0v-1c1 0 0-1 0-2h0c1 1 1 2 1 3v4l2 13c0 1 0 1 1 2v5c-1-1-1-2-1-3s-1-3-2-4v2h0c1 3 1 7 1 9-1 0-1-1-1-2v3c0-1 0-2-1-3h-1v-1-3c-1-2-1-4-1-6-2 2-1 6 0 8v5h0l-1-1h0l-2-13-1-5c0-1 0-1-1-2v-1c0-4-1-7-1-10h0l-1-6-1-2v-1c0-1 1-1 1-2l1 1h0l-3-15z" class="s"></path><path d="M575 487h1c0 1 0 3 1 5l-1 1c-1-2-1-4-1-6z" class="V"></path><path d="M575 476h0c0 2 0 4 1 5-1 0-2 0-3-1 0-1 0-2 1-3l1 1v-2z" class="q"></path><path d="M574 487h1c0 2 0 4 1 6h0v1 2c0 1 1 0 0 1v2c-1-1-2-5-2-6h1c-1-2-1-4-1-6z" class="e"></path><path d="M572 483h1c0 1 0 3 1 4 0 2 0 4 1 6h-1c0 1 1 5 2 6 0 1 0 2-1 2l-1-5c0-1 0-1-1-2v-1c0-4-1-7-1-10z" class="q"></path><path d="M575 476h1l2 13c1-1 1-6 1-6 0 1 0 2 1 3h0v-1c1 0 0-1 0-2h0c1 1 1 2 1 3v4l2 13c0 1 0 1 1 2v5c-1-1-1-2-1-3s-1-3-2-4v2h0c1 3 1 7 1 9-1 0-1-1-1-2v3c0-1 0-2-1-3h-1v-1c1-1 1-2 1-3 0 0-1 0-1-1h0c-1-4 0-9 0-13 0-1 0-2-1-3 0-1-1-2-1-3 0-2 0-5-1-7-1-1-1-3-1-5z" class="W"></path><path d="M581 490v3h0l-1-1c0-2 0-4 1-6v4z" class="e"></path><path d="M458 340c0-1 0-3-1-4h0v-1h1c1 1 1 1 1 2 1 1 2 2 2 3h1c1 1 2 2 2 3h2l1 3 3 3c1 1 1 1 1 2l1 1 1 1c1 0 2 0 2-1h0c2 1 2 2 3 4 1 1 2 1 2 2 1 2 2 2 3 4h-1l1 2v1h-1l1 1c1 0 2-1 2-2h1v-2h0c1-1 1-1 2-1v-1l-2-1 1-1 2 1v1h-1c0 1 0 1 1 2l2 1h0c1-1 0-1 0-2 1 0 3 1 4 2s2 2 3 2c0 1 1 2 2 3v1h-1v2s-1 1-1 2h1c0 1 1 1 1 3 1 0 1-1 2 0h2 0c0 1 1 1 1 2 0 0 0 1 1 1l2 1h0c1 0 1 0 1 1 1 1 2 1 3 2l1 2h0-1l1 3-2-1v-1s-1-1-1-2h-2v1l1 1c1 1 1 1 1 2s0 0-1 0h0-1l-1 1h1v1c-1 1-1 1-1 2-1 0-2-1-2-1 0-1 0-2-1-2s0-1-1 0c-1 0-1 0-2-1 0-1 0-1-1-3l-1 1c0 1 1 1 1 3v1 1c-1 0 0 0-1 1l1 1-2 1c0-1-1-1-1-2l1-1c0 1 0 1 1 1v-1c-1 0-1 0-1-1 0 0 0-1-1-1v-1l-1 1c-1 0-1 0-2-1v1c1 1 2 3 3 5v1c-2-1-2-2-3-3 0-1-1-2-1-3-1 0-1-1-1-2h-1l-1-1v1s0 1 1 1c0 0 1 1 1 2h0c0 2 2 3 2 5h1v1 1 2l1 1v2h1 0v-2h1 0c0 1-1 2-1 3h-1 0c0 1-1 1-2 2l2 2h0l-1 1c-1-2-1-4-3-5h0c0 2 1 2 1 3v2c-2-3-3-6-5-9v1c1 1 1 2 1 3v1c-1-1-1-1-1-2s-1-1-1-2h-1v2c-1 0-1 1-2 1v-1h-1c0 2 1 3 1 4-1 1-1 1-1 2-1 1 0 1-1 1l-2-2h0 0c1 2 1 4 2 6v1c0 1 0 2-1 3v-1c-1-4-3-7-3-10v-1l-1 1v-1 1h-1c0-2-2-3-1-5v-2c0-1 0-1-1-2v1h0c0 1 0 1-1 2v-1s0-1-1-1h0v4h-1v-2-1-1c0-1-1-3-2-3v1 1h-1c0-3-1-6-2-9l-1-1c0 1-1 3-2 3h0c1-1 1-3 1-4s-1-1-1-2v-1c0-1-1-1 0-2v-1h-1v1c-1-1-1-2-2-2v-1c0-1 1-1 1-2v-2c-1 1-1 1-1 2-1 1-1 1-1 2 0 0 0 1 1 1 0 1 0 2-1 3v-1-2c-1-2-2-5-2-7-2-4-2-9-4-13l-1 1v-1-1-2c-1-1-1-2-1-3h0 1 0l-1-6v-4c-1-2-1-5 0-7 1 1 1 1 1 2 1 2 1 4 3 6h0c1-2 0-1-1-2 0-1 1-2 0-2v-1h2c1 1 2 2 2 3h0 1c-1 0-1-1-1-2z" class="h"></path><path d="M472 362v-1-1h2c-1 1-1 2-2 2z" class="Z"></path><path d="M479 362l1 1h0c-1 0-2 0-2-1s0 0 1-1v1z" class="I"></path><path d="M479 361c-1-1-1-1-1-2v-1c1 1 2 1 2 2s0 1-1 2v-1z" class="Z"></path><path d="M486 386c1-1 1-1 2-1l2 2h-3l-1-1z" class="m"></path><path d="M481 371l3 3c0 1 0 2 1 2-1 0-1 0-1 1h0c-1 0-1-1-2-1v-2c1-1-1-2-2-3h1z" class="g"></path><path d="M474 370l3 3c-1 1-1 1-2 1-1-1-2-2-2-3l1-1z" class="Z"></path><path d="M494 383c1 0 1 0 2 1v1c1 1 2 2 2 3l-1 1v-1l-1-1c0-1-2-2-2-4z" class="m"></path><path d="M470 361v-1c1 1 1 1 1 2s2 3 2 3v2c-1-1-1-1-1-2-1-1-1-2-3-3h0l1-1z" class="a"></path><path d="M481 382l1-1c1 0 3 0 4 1h1l-1 1c-2-1-2-1-4 0l-1-1z" class="j"></path><path d="M481 371c-1-1-2-1-3-2l1-1c2 0 2 0 4 1l-2 2z" class="Z"></path><path d="M474 360l1-1c0 1 1 1 1 1-1 1-1 2-2 2v2h1 0c0 1 1 1 1 2h-1s-1-1-1-2h-1l-1-2c1 0 1-1 2-2z" class="a"></path><path d="M472 352l1 1c1 0 2 0 2-1h0c2 1 2 2 3 4v1l-2-2-2 1v-1c-1-1-2-2-2-3h0z" class="g"></path><path d="M496 387c-2 0-3-3-4-4-1 1-1 0-2 1l-1-1v-1c1-1 1-1 2-1l1-1 1 2s0 1 1 1c0 2 2 3 2 4z" class="a"></path><path d="M457 371c-1-3-2-8-2-10h1l2 10h1v2c0 1 0 1-1 2 0-1 0-3-1-4z" class="c"></path><path d="M483 369c1 2 2 3 3 4 0 1 1 1 2 2h0c-1 1-2 2-3 2v-1c-1 0-1-1-1-2l-3-3h0l2-2z" class="a"></path><path d="M458 340c0-1 0-3-1-4h0v-1h1c1 1 1 1 1 2 1 1 2 2 2 3h1c1 1 2 2 2 3h2l1 3 3 3c1 1 1 1 1 2l1 1h0-1l-1 1h1c0 1 1 2 1 3h-1c0-1-1-2-2-2 0-2-2-3-3-4l-1 1v1h0c-2-1-2-1-4-1h1l1-1h0c0-1 1-1 1-1v-1h-1s0 1-1 1l-1-1-2 1 1-1h-1l2-2c0 1 0 1 1 0h0l-2-2c-1-1-1-2 0-3l-2-1z" class="l"></path><path d="M470 353l-1-1v-1h1l1 1-1 1z" class="p"></path><path d="M466 346h1c1 1 1 2 1 3h-1-1v-3z" class="a"></path><path d="M457 360c-1 0-1 0-1-1h0c-1-1-1-1-1-2h0c1 0 1 0 1 1 1 0 1 1 1 1 1-1 1-1 2-1v-2h1c0 1 0 1 1 1 0 2 1 3 1 4l1 2c1-2 1-2 3-2h0 1c2 1 2 1 2 2s0 1 1 2v1c1 1 1 1 1 2v1h0c-1-1-2-2-2-3-1-1-1-1-1-2h-1c0 1 1 2 1 3l2 2h-4c1 1 1 1 1 2l-2 1-1-2h-1v2h-1l-1-1v1-1h-1c0 2 0 2 1 3-1 1-1 1-1 2-1 1-1 1-1 2 0 0 0 1 1 1 0 1 0 2-1 3v-1-2c-1-2-2-5-2-7v-1c1 1 1 3 1 4 1-1 1-1 1-2v-2h-1c2-3-1-8-1-11z" class="Z"></path><path d="M460 359c1 2 2 3 2 5h0-1v-2c-1-1-1-1-1-3z" class="a"></path><path d="M466 369s-1 0-1-1v-1h1 1 1l2 2h-4z" class="j"></path><path d="M460 359v-1l1-1c0 2 1 3 1 4l1 2c1 1 1 0 1 1l-1 1c-1 0-1-1-1-1 0-2-1-3-2-5zm-3 1h0c1 1 1 2 2 4 0 2 1 4 1 6 1 0 0-3 2-3v5l-1-1v1-1h-1c0 2 0 2 1 3-1 1-1 1-1 2-1 1-1 1-1 2 0 0 0 1 1 1 0 1 0 2-1 3v-1-2c-1-2-2-5-2-7v-1c1 1 1 3 1 4 1-1 1-1 1-2v-2h-1c2-3-1-8-1-11zm12 3c2 3 3 5 5 7l-1 1c0 1 1 2 2 3l3 3c0 1 1 2 2 3 1 0 1 1 1 2l1 1c2-1 2-1 4 0l-1 1 1 2 1 1 2 3 3 5v1-2l3 3v2l1 1v2h1 0v-2h1 0c0 1-1 2-1 3h-1 0c0 1-1 1-2 2l2 2h0l-1 1c-1-2-1-4-3-5h0c0 2 1 2 1 3v2c-2-3-3-6-5-9v1c1 1 1 2 1 3v1c-1-1-1-1-1-2s-1-1-1-2h-1-1v-2h1 0c-1-1-1-2-1-2v-1l-2-1v-2c-1-1-2-2-2-3 0 0-1-1-1-2l-1-1c-1-1-1-2 0-3h0c-1 0-1-1-2-1v-2l-3-6c-1 0-1-1-1-1-1-1-1-1-1-2-2 0-2-1-2-2l-2-2c0-1-1-2-1-3h1c0 1 0 1 1 2 0 1 1 2 2 3h0v-1c0-1 0-1-1-2v-1c-1-1-1-1-1-2z" class="t"></path><path d="M480 384c1 1 2 1 3 2l1 1h-1-1 0v1c-1-1-1-2-2-4z" class="a"></path><path d="M480 387l-1-1c-1-1-1-2 0-3l1 1h0c1 2 1 3 2 4 0 1 1 2 1 3v1c-1-1-2-2-2-3 0 0-1-1-1-2z" class="h"></path><path d="M489 390h-1c0 1 0 1-1 1-1-1-1-2-1-3-1 0-2-1-2-1v-2l1-1 1 2 1 1 2 3z" class="Z"></path><path d="M488 399v-1-1l1 1 2-1s1 1 1 2h0c0 1 1 2 1 2 0 1 1 3 1 4l2 2h0l-1 1c-1-2-1-4-3-5h0c0 2 1 2 1 3v2c-2-3-3-6-5-9z" class="a"></path><path d="M486 359l1-1 2 1v1h-1c0 1 0 1 1 2l2 1h0c1-1 0-1 0-2 1 0 3 1 4 2s2 2 3 2c0 1 1 2 2 3v1h-1v2s-1 1-1 2h1c0 1 1 1 1 3 1 0 1-1 2 0h2 0c0 1 1 1 1 2 0 0 0 1 1 1l2 1h0c1 0 1 0 1 1 1 1 2 1 3 2l1 2h0-1l1 3-2-1v-1s-1-1-1-2h-2v1l1 1c1 1 1 1 1 2s0 0-1 0h0-1l-1 1h1v1c-1 1-1 1-1 2-1 0-2-1-2-1 0-1 0-2-1-2s0-1-1 0c-1 0-1 0-2-1 0-1 0-1-1-3l-1 1c0 1 1 1 1 3-1-1-1-2-2-2 0-1-2-3-2-4-1-2-3-3-3-5h1c0 1 1 1 1 2h0 1c-1-1-1-1-1-2h-1c-1-1-2-2-3-2s-1 0-2 1l-1-2h0c1-1 1-1 1-2-1-1-3-3-4-3-1-1-1-2-2-2v-1h-3v-1c1 0 1 0 1-1h1l1 1c1 0 2-1 2-2h1v-2h0c1-1 1-1 2-1v-1l-2-1z" class="I"></path><path d="M489 362l2 1c0 1-1 1-1 1h-1v-2zm-3 0c1 0 2 1 2 1 0 1-1 1-1 1h-1v-2zm12 19h1l-2 2-1-1v-1h0 2z" class="m"></path><path d="M490 371c1-2 2-2 3-3l1 1-2 2h-2z" class="j"></path><path d="M500 380v1c1 1 2 1 2 2v1 1c-1-1-2-2-2-3l-1-1c0-1 0 0 1-1z" class="G"></path><path d="M504 376c0 1 1 1 1 2 0 0 0 1 1 1l-1 1-3-2c1 0 1-1 2-2z" class="R"></path><path d="M508 380h0c1 0 1 0 1 1 1 1 2 1 3 2l1 2h0-1l-3-3s-1-1-1-2z" class="Q"></path><path d="M496 372l1 1c0 1 0 1-1 2l1 2c-1 0-1 0-2-1h0c-1 0-2-1-2-2 2 0 2 0 3-2z" class="R"></path><path d="M485 368h-1v-1c1 0 1 0 2-1l2 1v-1c1 0 2 0 3-1l1 2c-1 0-2 1-3 1l-1 1v1c-1 0-2-1-3-2z" class="j"></path><path d="M485 368l2-1h1v2 1c-1 0-2-1-3-2z" class="p"></path><path d="M500 377v-1l2 2 3 2 1 1-2 2c-2 0-1-1-2-2h-2v-1c-1 1-1 0-1 1h-1v-3h1l1-1z" class="m"></path><path d="M500 377l2 2-1 1-1-1h0v1c-1 1-1 0-1 1h-1v-3h1l1-1z" class="R"></path><path d="M495 363c1 1 2 2 3 2 0 1 1 2 2 3v1h-1v2s-1 1-1 2l-1-3c-1 0-2-1-3-1h0c0-1-1-2-2-2 1-2 2-3 3-4z" class="G"></path><path d="M466 369h4c0 1 0 2 2 2 0 1 0 1 1 2 0 0 0 1 1 1l3 6v2c1 0 1 1 2 1h0c-1 1-1 2 0 3l1 1c0 1 1 2 1 2 0 1 1 2 2 3v2l2 1v1s0 1 1 2h0-1v2h1v2c-1 0-1 1-2 1v-1h-1c0 2 1 3 1 4-1 1-1 1-1 2-1 1 0 1-1 1l-2-2h0 0c1 2 1 4 2 6v1c0 1 0 2-1 3v-1c-1-4-3-7-3-10v-1l-1 1v-1 1h-1c0-2-2-3-1-5v-2c0-1 0-1-1-2v1h0c0 1 0 1-1 2v-1s0-1-1-1h0v4h-1v-2-1-1c0-1-1-3-2-3v1 1h-1c0-3-1-6-2-9l-1-1c0 1-1 3-2 3h0c1-1 1-3 1-4s-1-1-1-2v-1c0-1-1-1 0-2v-1h-1v1c-1-1-1-2-2-2v-1c0-1 1-1 1-2v-2c-1-1-1-1-1-3h1v1-1l1 1h1v-2h1l1 2 2-1c0-1 0-1-1-2z" class="k"></path><path d="M465 382l2 2v1l-2-1v-2z" class="i"></path><path d="M465 384l2 1-1 1v2l-1-1h0v-3z" class="c"></path><path d="M479 399c2 0 2 0 2 1v2l-1 1-1-4z" class="a"></path><path d="M476 393h1l1 2c0 1 1 3 1 4h-1c0-1-1-2-1-4l-1-2z" class="i"></path><path d="M477 395v1h-1s0-1-1-1c0 0 0 1-1 1v-1c1 0 1-1 1-1 0-1 0-1-1-2l1-1c0 1 1 2 1 2l1 2z" class="Z"></path><path d="M461 374c-1-1-1-1-1-3h1v1c0 1 1 2 1 3s0 1 1 1v3c-1-1-1-2-2-3v-2z" class="a"></path><path d="M469 378l2 2 2 2h0-1l-1 1-1-1-1 1v-3h1c0-1 0-1-1-2zm15 24l-1-1c0-1 0-2 1-3h1v2h1v2c-1 0-1 1-2 1v-1z" class="Z"></path><path d="M485 395v1c-1 1-2 1-2 2h0-1-2v-4h0v-2c2 0 2 2 3 2l2 1z" class="i"></path><path d="M461 376c1 1 1 2 2 3s1 2 1 3h1v2 3h0c0 1-1 3-2 3h0c1-1 1-3 1-4s-1-1-1-2v-1c0-1-1-1 0-2v-1h-1v1c-1-1-1-2-2-2v-1c0-1 1-1 1-2z" class="t"></path><path d="M466 369h4c0 1 0 2 2 2 0 1 0 1 1 2 0 0 0 1 1 1l3 6v2c1 0 1 1 2 1h0c-1 1-1 2 0 3l1 1c-1 1-2 1-2 2h-1c0-1-1-3-2-4h0v2l1 1h-1c-1-2-2-4-2-6h0l-2-2-2-2v-1l-1-1-2 1-1-1c0-1 0-1 1-1v-1c0-1-1-1-1-2l2-1c0-1 0-1-1-2z" class="a"></path><path d="M475 380l1-1c0 1 0 0 1 1h0v2c-1 0-1 0-2-1v-1z" class="Z"></path><path d="M475 381s-1-1-2-1h-1l1-2h1 0c0 1 1 1 1 2v1z" class="j"></path><path d="M220 314c2-5 6-9 11-12 8-5 18-6 27-5l-1 1c3 1 6 2 8 4s3 3 4 6l2 3 2 6v4c-1 5-2 8-5 11v1l18-14c-1 2-5 5-7 7-6 5-13 9-18 15-3 2-5 5-7 7-1 1-2 1-3 2v1-3c-5 7-9 14-11 22 0 2-1 3-1 5v1c-2 1-3 3-4 4-2-2-4-6-4-9v-2l-1-1v1h-2v-2-1c0-1 0-3 1-5 0-2 2-3 4-5 0 0 0-1 1-1 1-1 2-2 2-3h0c1-2 2-3 3-5v-1l1-1h-2-1v-1c0-1 0-3 1-4 0 0 1-1 2-1l-1-1c-2 1-5 4-7 3h-1s-1-1-2 0c-1 0 0 0-1-1v-2h-1s0 1-1 1l-1-1c0-1 1-1 1-2-2 0-2 1-3 0v-2c-2-1-2-2-3-4v-2c0-1 1-1 2-2h-1-1-1l1-2h-1v-1-4c0-1 1-3 1-5z" class="d"></path><path d="M228 367h5c1 0 1 0 1 1s-2 2-3 3v-2l-1-1v1h-2v-2z" class="Q"></path><path d="M236 352h1c0 1-1 2-2 3-2 3-3 9-7 11 0-1 0-3 1-5 0-2 2-3 4-5 0 0 0-1 1-1 1-1 2-2 2-3z" class="G"></path><path d="M252 334h0c2 0 4-1 6-2l-10 8h-1v-1h0v-1h0l-2 1c-2-1-2-1-4-1h1c2-1 2-1 3-3v2h0l1 1c1-1 1-3 3-3 1 0 2-1 3-1z" class="T"></path><defs><linearGradient id="AP" x1="271.347" y1="323.678" x2="270.941" y2="314.771" xlink:href="#B"><stop offset="0" stop-color="#777779"></stop><stop offset="1" stop-color="#8f8f8f"></stop></linearGradient></defs><path fill="url(#AP)" d="M271 311l2 6v4c-1 5-2 8-5 11v1c-1 0-1 1-2 1 2-3 4-6 5-9 1-4 0-6-2-10l1-1h1v-3z"></path><path d="M224 319c0-2 1-2 1-4 2-2 4-2 6-3 1 1 2 1 2 2v1c-2 1-5 3-6 5h0-2 0l-1-1z" class="B"></path><path d="M233 310c5-1 8-1 11 1 1 1 2 1 2 2 1 0 2 2 3 4h-1v-1c-1 0-2-1-2-1l-1-1h1l-1-1h-2-1s-1 0-2 1h0-2-2 0c-1-1-1-2-3-2h0c-1-1 0-1-1-1l1-1z" class="F"></path><path d="M233 312c-1-1 0-1-1-1l1-1 1 1c1 0 2 0 3 1 1 0 2 1 3 2h-2-2 0c-1-1-1-2-3-2h0z" class="H"></path><path d="M245 339l2-1h0v1h0v1h1 2v1c-1 2-3 5-4 5h-2l-1-1c1-2 3-3 4-4-2 0-5 3-7 4h-2-1v-1c0-1 0-3 1-4 0 0 1-1 2-1l1-1c2 0 2 0 4 1z" class="N"></path><path d="M241 338c2 0 2 0 4 1-1 1-1 1-2 1-2 1-3 2-4 3h0c0 1 0 1-1 2h-1v-1c0-1 0-3 1-4 0 0 1-1 2-1l1-1z" class="X"></path><path d="M249 317l3 1c1 1 2 2 2 3 2 2 3 5 3 8-1 1-1 2-3 3h0-2 0c-3-4-4-8-3-13v-2z" class="F"></path><path d="M252 332h1c1-2 1-3 1-4h1l2 1c-1 1-1 2-3 3h0-2z" class="G"></path><path d="M254 321c2 2 3 5 3 8l-2-1h1v-1l-1-2c-1-1-1-3-1-4z" class="T"></path><path d="M249 317l3 1v5l1 1h0-1c0-1-1-1-2-1l-1-1 1-1c0-1 0 0-1-1v-1-2z" class="P"></path><path d="M257 298c3 1 6 2 8 4s3 3 4 6l2 3v3h-1l-1 1c-4-3-9-6-11-10-1-2-2-4-1-6v-1z" class="X"></path><path d="M263 306c0-1 0-2-1-2l1-1c1 1 0 2 1 2h1v1 2c0 1 0 1-1 1h0c-1-1-1-2-1-2v-1h0z" class="O"></path><path d="M265 306c1 1 3 2 3 3l1-1 2 3v3h-1c-1-1-1-2-1-2l-3-3c0-1 0-1-1-1v-2z" class="L"></path><path d="M257 299v1l1 1c1 2 2 3 3 4l2 1h0v1s0 1 1 2h0c1 0 1 0 1-1 1 0 1 0 1 1l3 3s0 1 1 2l-1 1c-4-3-9-6-11-10-1-2-2-4-1-6z" class="S"></path><path d="M251 348c2-3 5-6 7-9 4-4 5-10 4-16 0-3-2-5-5-7-2-1-5-2-8-1h0l-1-1 1-1c2-1 5 0 6 0 3 1 6 3 7 6 3 5 1 12 0 18 1-1 3-2 4-3 1 0 1-1 2-1l18-14c-1 2-5 5-7 7-6 5-13 9-18 15-3 2-5 5-7 7-1 1-2 1-3 2v1-3z" class="R"></path><path d="M233 312h0c2 0 2 1 3 2h0 2 2 0c1-1 2-1 2-1h1 2l1 1h-1l1 1s1 1 2 1v1h1 0v2c-1 5 0 9 3 13h0 2-1c0 1-1 1-1 1v1c-1 0-2 1-3 1-2 0-2 2-3 3l-1-1h0v-2c-1 2-1 2-3 3h-1l-1 1-1-1c-2 1-5 4-7 3h-1s-1-1-2 0c-1 0 0 0-1-1v-2h-1s0 1-1 1l-1-1c0-1 1-1 1-2-2 0-2 1-3 0v-2c-2-1-2-2-3-4v-2c0-1 1-1 2-2h-1-1-1l1-2c0-2 1-4 2-5h2l1 1h0 2 0c1-2 4-4 6-5v-1h1l-1-2z" class="u"></path><path d="M225 331c0 2 1 3 1 4v1c-1 0-1 0-1-1h-1v-3l1-1z" class="K"></path><path d="M241 324c1-1 1-1 3-1 1 1 2 1 2 2l-1 2-1-1-2-1-1-1z" class="D"></path><path d="M244 330l3-3h1v2 2h-1c-1 0-2 0-3-1z" class="O"></path><path d="M239 330c2 1 3 1 3 2 1 1 1 3 0 4v-1c-1 0-2-1-3 0h-2l2-2c-1 0-1 0-2-1v-1l2-1h0 0z" class="F"></path><path d="M238 320c1 0 2 1 3 1 0-1 0-1-1-2v-1h0c2 1 2 2 3 3-2 1-4 2-6 2h-1c-1 0-1 0-2 1v-1l2-3v1c1-1 2-1 2-1z" class="O"></path><path d="M245 314l1 1s1 1 2 1v1l-1 7-3-3v-3c1-2 1-2 1-4z" class="E"></path><path d="M220 330v-2c0-1 1-1 2-2h0c2 2 2 3 2 4l1 1-1 1v3h1l-2 1v-2c-2-1-2-2-3-4z" class="D"></path><path d="M220 330v-2c0-1 1-1 2-2h0c2 2 2 3 2 4h-1l-1-1c0 1-1 1-2 1h0z" class="K"></path><path d="M244 330c1 1 2 1 3 1h1 0l1-1 3 3c-2 1-3 1-4 2-1 0-2 1-3 2v-2h-1v-3h0l-1-1 1-1z" class="B"></path><path d="M244 330c1 1 2 1 3 1h1 0c0 1 0 1-1 1 0 1-1 1-1 2h-1 0c0-1 1-1 1-2h-1-1 0l-1-1 1-1z" class="P"></path><path d="M220 324c0-2 1-4 2-5h2l1 1h0 2l-2 3s0 1-1 1c0 1-1 2-1 2h-1 0-1-1-1l1-2z" class="C"></path><path d="M235 330c1 1 1 1 1 2v1c-2 2-5 2-7 2l-3-3c0-2 2-3 3-4 0-1 0-1 1-1 0 1 1 2 1 3-1 0-1 0-1 1s1 1 1 2h1 0v-2c1 0 2 0 3-1z" class="K"></path><path d="M233 321v4h-3s0 1-1 1c0 0-1 0-1 1s-1 2-2 3h-1v-3c0-1 1-1 2-1l-1-1c0-1 1-3 1-4h1c1-1 2-2 2-3 2 1 3 1 4 1h1c-1 1-1 2-2 2z" class="C"></path><path d="M237 335h2c1-1 2 0 3 0v1c-1 1-2 1-3 2-2 1-5 4-7 3h-1s-1-1-2 0c-1 0 0 0-1-1v-2c0-1 1-1 1-2h6 0l2-1z" class="P"></path><path d="M228 338c0-1 1-1 1-2h6c0 1-1 2-1 3l-1 1v-2h0c0 1 0 2-1 2h-1v1s-1-1-2 0c-1 0 0 0-1-1v-2z" class="O"></path><path d="M231 340l-1-2v-1h3v1h0c0 1 0 2-1 2h-1z" class="E"></path><path d="M241 324l1 1 2 1 1 1c-1 0-1 1-2 2l-1 1h-3 0 0-1-3c-1 1-2 1-3 1v2h0-1c0-1-1-1-1-2s0-1 1-1h1v-1c-1 0-1-1-1-2s1-1 3-1c1-1 1-2 3-2h2 2z" class="B"></path><path d="M232 329c-1 0-1-1-1-2s1-1 3-1l1 1c0 1 0 1-1 2h-2z" class="D"></path><path d="M241 324l1 1 2 1 1 1c-1 0-1 1-2 2l-1-1s0-1-1-2c-1 1 0 2-1 3h0c-1 0-1-1-1-1v-1c0-1 0-1-1-1 0 0-1-1-1-2h2 2z" class="P"></path><path d="M233 312h0c2 0 2 1 3 2h0 2 2 0c1-1 2-1 2-1h1 2l1 1h-1c0 2 0 2-1 4v3l-1-1v1c-1-1-1-2-3-3h0v1c1 1 1 1 1 2-1 0-2-1-3-1 0 0-1 0-2 1v-1l-2 3c0-1-1-1-1-2 1 0 1-1 2-2h-1c-1 0-2 0-4-1 1 0 2-1 3-2v-1-1h1l-1-2z" class="h"></path><path d="M233 316h1 1l-1 2h1v1h-1c-1 0-2 0-4-1 1 0 2-1 3-2z" class="B"></path><path d="M236 320v-2-2c2 0 3-1 5-1-1 1-2 2-2 3h0-1v2s-1 0-2 1v-1z" class="P"></path><path d="M241 315h3 0 0c-1 2-1 4-1 5v1c-1-1-1-2-3-3h0v1c1 1 1 1 1 2-1 0-2-1-3-1v-2h1 0c0-1 1-2 2-3z" class="E"></path><path fill="#fff" d="M291 447v-80c8-1 17-1 26-1 19 0 37 0 56 3l18 3c3 8 1 17 4 24v-6l1 7 2 1 1 1c0 1 0 1 1 2l1 1c0 1-1 4 0 5 0 2 2 3 2 4l1 1c0 1 0 2 1 3 0 1 0 3 1 5 0 1 0 3 1 4v1s0 1 1 1v3c2 2 2 3 2 5h0c0 3 0 7 2 9v1c1 0 1 2 2 3 2 4 3 9 3 13 1 1 2 1 2 2h0c0 3 3 6 2 9l1 6s-1 1-1 2c-2 1-4 4-4 6-1 1-1 1-2 1s-4 0-5 1h-1s-1 1-2 1l-3 3c0-1-1-1-1-2h-1-1c-3 0-5 1-6 3s-2 3-2 5c0 1 0 1 1 2-1 1-2 1-3 1-4 2-7 4-9 7-1 2-1 4-3 6-1-1-1-2-2-3v-9h-1v-3h-3v1h-1v-1c-1 0-1 0-1-1v2h-8-18c-2 0-6 0-7-1v-1-4h0c-1 1-2 3-3 4h-1c0-1 0-2-1-2v-1-3h0l-3-3s1 0 1-1h0c-2 0-5 0-7-1v-3h0c-1-1-1-3 0-4 0-1 0-1-1-2h0l-1-1h-3v1h-1v-1c0-1-1-1-2-1h0c-1 0-1 0-1 1h-2v-1h0-3c-1 0-2-1-2 0-2 1-3 0-4 0h-2c-1 1-1 1-1 2h1v1h0 0c-2-1-3-1-5-1h-5v-2h1 0c0-1-1-2-1-3v-2c0-1 0-1-1-2v-1c0-3 1-8 1-12h-1c-1-1 0-6 0-8z"></path><path d="M295 454c0-2 1-5 2-7h1c0 2 1 4 0 6v-1 1l-2 2h0 0v-1h0-1z" class="K"></path><path d="M417 460c1 1 2 1 2 2h0c0 3 3 6 2 9h0v-2h0l-1 1h-1c-1-4-1-6-2-10z" class="B"></path><path d="M298 447h3c1 0 1 0 1 1h-1v2c0 1-1 2-2 3h-1c1-2 0-4 0-6z" class="w"></path><path d="M408 426v3c2 2 2 3 2 5h0l-1-1c-1 2-1 5-2 8v-12l1-3z" class="u"></path><path d="M401 413c1 1 2 1 4 2 0 1 0 3 1 5 0 1 0 3 1 4v1s0 1 1 1l-1 3-3-6v-4l-3-6z" class="S"></path><path d="M396 397l2 1 1 1c0 1 0 1 1 2l1 1c0 1-1 4 0 5 0 2 2 3 2 4l1 1c0 1 0 2 1 3-2-1-3-1-4-2 0-2-1-5-2-7 0-2 0-6-2-7v1h0c-1-1-1-2-1-3z" class="n"></path><path d="M334 465c1-1 2-1 3-1-1 2-1 3-1 5v2h-1l-1 1c-1 1-1 2-2 2v-3-7h1l1 1z" class="E"></path><path d="M334 465c1-1 2-1 3-1-1 2-1 3-1 5h-3v-3c1 0 1 0 1 1h0 1c0-1 0-1-1-2z" class="F"></path><path d="M298 453h1c1-1 2-2 2-3v-2h1v2h0c0 1 1 1 1 2s-1 2-1 3c-1 1-1 1-1 2 0 0 0 1-1 1 0 1 0 1-1 2 1 1 1 1 1 2h-1l-3-1h0-1c0 1 0 3-1 4l-1-1v-4c1 1 1 2 1 3v1h0v-2h0v-2c1 0 1 0 1-1v-3-2h1 0v1h0 0l2-2v-1 1z" class="D"></path><path d="M298 453v3 1l-1 1c-1-1-1-2-1-3h0l2-2z" class="Y"></path><path d="M303 443c1 2 1 3 1 4v1 4h1l1-1c0 3 0 11-2 13l-1 1-1-1h-3 0l-2-1v1h-1v-3h0l3 1h1c0-1 0-1-1-2 1-1 1-1 1-2 1 0 1-1 1-1 0-1 0-1 1-2 0-1 1-2 1-3s-1-1-1-2h0 1v-7z" class="J"></path><path d="M303 443v-24-11-3c1 1 1 6 1 8 3-1 5-1 7-1 0 1 0 2-1 2l2 2c-1 0-1 0-2 1-1 0-2 1-4 1-1 1 0 0 0 1l-1 1h-1v6c0 8 1 15 0 22v-1c0-1 0-2-1-4z" class="C"></path><path d="M304 413c3-1 5-1 7-1 0 1 0 2-1 2h-1v1l-1-1-1 1v1c0 1-1 0-2 1v1c-1-1-1-4-1-5z" class="K"></path><path d="M311 412h2s1 0 1 1c3 1 6 2 9 4 0 1 0 2 1 3l1 2c-4-1-9-3-12-3s-5 1-8 1l1-1c0-1-1 0 0-1 2 0 3-1 4-1 1-1 1-1 2-1l-2-2c1 0 1-1 1-2z" class="E"></path><defs><linearGradient id="AQ" x1="320.849" y1="460.649" x2="310.731" y2="466.485" xlink:href="#B"><stop offset="0" stop-color="#a19f9f"></stop><stop offset="1" stop-color="#cbcbcc"></stop></linearGradient></defs><path fill="url(#AQ)" d="M313 453c1-4 1-7 2-11 0 2 1 5 1 7s1 5 2 6c2 6 6 13 7 19-1 0-1 0-2-1v-1h-1c0 2 0 3 1 4h-1-3v1h-1v-1c0-1-1-1-2-1h0-1v-2s1 0 1-1c0 0 0-1 1-1h0c0-1 1-1 1-2s-1-2-1-3l-1-2c0-1 0 0 1-1h0s1-1 0-2c0-1 0-4-1-5-1 0-1 0-1-1v-1h-1l-1-1z"></path><path d="M316 475c1-1 1-2 2-2l1 1 1-1c0 1 0 1 1 1 1-1 0-1 0-2h1c0 2 0 3 1 4h-1-3v1h-1v-1c0-1-1-1-2-1z" class="X"></path><path d="M313 453l1 1h1v1c0 1 0 1 1 1 1 1 1 4 1 5 1 1 0 2 0 2h0c-1 1-1 0-1 1l1 2c0 1 1 2 1 3s-1 1-1 2h0c-1 0-1 1-1 1 0 1-1 1-1 1v2h1c-1 0-1 0-1 1h-2v-1h0-3l1-1v-1h-1v-2-2c-1-1-1-5-1-7l4-9z" class="D"></path><path d="M311 463c0 1 1 2 1 3-1 1-1 2-1 3v1 3h0-1v-2-2l1-6z" class="E"></path><path d="M313 453l1 1c0 1-1 2-1 3-1 2-2 4-2 6l-1 6c-1-1-1-5-1-7l4-9z" class="B"></path><g class="O"><path d="M314 454h1v1c0 1 0 1 1 1 1 1 1 4 1 5 1 1 0 2 0 2h0c-1 1-1 0-1 1v-1c-1 2-1 3-1 5h-1v-2c0-2 1-4 0-7v-2h-1c0-1 1-2 1-3z"></path><path d="M314 466v2h1c0-2 0-3 1-5v1l1 2c0 1 1 2 1 3s-1 1-1 2h0c-1 0-1 1-1 1 0 1-1 1-1 1v2h1c-1 0-1 0-1 1h-2v-1h0-3l1-1v-1h0v-1-1h1c1-1 1-3 2-5z"></path></g><path d="M314 466v2h1c0-2 0-3 1-5v1l1 2c-1 1-2 2-2 3 1 1 0 2 0 3l-2 1s-1 0-1-1h-1v-1h1c1-1 1-3 2-5zm-22-11c1-2 0-5 1-7h1c0 4-1 9-1 12v4l1 1c1-1 1-3 1-4h1v3h1v-1l2 1h0 3l1 1 1-1c2-2 2-10 2-13v-1c1 3 1 9 0 12 0 1 1 1 2 2l1-2c0 2 0 6 1 7v2 2h1v1l-1 1c-1 0-2-1-2 0-2 1-3 0-4 0h-2c-1 1-1 1-1 2h1v1h0 0c-2-1-3-1-5-1h-5v-2h1 0c0-1-1-2-1-3v-2c0-1 0-1-1-2v-1c0-3 1-8 1-12z" class="X"></path><path d="M291 467l1 1c1 1 3 0 3 2v1c-1 1-2 1-3 1v-2c0-1 0-1-1-2v-1z" class="E"></path><path d="M296 475v-1c0-1 0-3 1-5h2v1 2c-1 2-2 3-3 3h0z" class="D"></path><path d="M294 465c1-1 1-3 1-4h1v3h1v-1l2 1h0 3l1 1h-1c-1 2-1 3-1 5v-5c-2 1-2 0-3 1-1 0-1 1-2 1h-2v-2h0z" class="C"></path><path d="M306 451v-1c1 3 1 9 0 12l-1 2c-1 1-1 2-1 3v3s-1 0-1-1c0 0 1-1 0-2-2 3 0 6-1 8-1 1-1 1-1 2h1v1h0 0c-2-1-3-1-5-1h-5v-2h1 3 0c1 0 2-1 3-3v-2c1 2 1 4 2 6v-6c0-2 0-3 1-5h1l1-1c2-2 2-10 2-13z" class="O"></path><path d="M305 464l1-2c0 1 1 1 2 2l1-2c0 2 0 6 1 7v2 2h1v1l-1 1c-1 0-2-1-2 0-2 1-3 0-4 0h-2c1-2-1-5 1-8 1 1 0 2 0 2 0 1 1 1 1 1v-3c0-1 0-2 1-3z" class="P"></path><path d="M305 464l1-2c0 1 1 1 2 2h0c-1 1-1 2-1 2h-1c0-1-1-1-1-2z" class="E"></path><path d="M302 475c1-2-1-5 1-8 1 1 0 2 0 2 0 1 1 1 1 1v4h1v-3l2 1 1-1h2v2h1v1l-1 1c-1 0-2-1-2 0-2 1-3 0-4 0h-2z" class="U"></path><path d="M373 463h1l-1 10c1 3 1 5 2 8 1 4 4 8 4 12-1 3-2 5-2 8h-1v-3h-3v1h-1v-1c-1 0-1 0-1-1l-2-1c-1 0-3 0-3-1v-7c0-5 0-11 1-16l1-1v-5-2h4v-1h1z" class="C"></path><path d="M371 495c2 0 2 1 3 2l1 1h-3c-1 0-1 0-1-1v-2z" class="T"></path><path d="M370 480h0v4c1-1 1-2 1-3 0 3 1 7 0 10h-1v-2h0v-9z" class="D"></path><path d="M372 493h0c1-2 1-5 1-7h1v2h1v-2h1c1 3 1 6 1 9v1c-2 0-4-1-6-2h0l1-1z" class="K"></path><path d="M373 463h1l-1 10v-3h0l-1 2-1 9c0 1 0 2-1 3v-4-7c0-1-1-1-1-2h0-1v-5-2h4v-1h1z" class="E"></path><path d="M368 466h0l1-1 1 2h0v2s-1 1-1 2h-1v-5z" class="C"></path><path d="M373 463h1l-1 10v-3h0l-1 2v-5l1-1c-2-1-2-1-3-1h-1l-1 1h0v-2h4v-1h1z" class="D"></path><path d="M368 471h1 0c0 1 1 1 1 2v7h0v9h0v2h1c0 1 0 1 1 2l-1 1h0v1 2l-2-1c-1 0-3 0-3-1v-7c0-5 0-11 1-16l1-1z" class="H"></path><path d="M370 489v2h1c0 1 0 1 1 2l-1 1h0v1 2l-2-1c1-2 1-5 1-7z" class="X"></path><path d="M368 471h1 0c0 1 1 1 1 2v7h0v9c-1-2-1-5-1-7l-2-2v5l1 1h0l-2 2c0-5 0-11 1-16l1-1z" class="B"></path><path d="M368 471h1 0c0 1 1 1 1 2v7h0v-5h-2v-2l-1-1 1-1z" class="P"></path><path d="M344 436c3-5 6-10 8-16-1-1-2-2-2-3 0-2 1-3 2-4s1-7 1-9h0v9c1 0 2 0 3 1s1 2 1 3c0 2-1 2-2 3h0v1c2 4 4 7 6 11 4 7 9 13 12 21 1 1 3 5 2 6 0 1-1 3-1 4h-1-1v1h-4c-1 0-3-1-4-1h-4l-11-1c-2 1-4 1-7 1-2 0-4 0-5 1-1 0-2 0-3 1l-1-1h-1l-1-3c1-8 5-14 10-20l3-5z" class="B"></path><path d="M350 431c0-1 0-2 1-4 0 0 1-1 1-2l1-2c0 2 0 3-1 5l-2 3z" class="C"></path><path d="M341 441l3-5c0 4-3 9-5 11 0-1 0-1 1-2 0-1 1-2 1-4z" class="O"></path><path d="M353 414c2 0 2 0 3 1v2c0 1 0 1-2 3h-1l-1-2c0-1-1-1 0-2 0-1 0-2 1-2z" class="w"></path><path d="M344 441l2-2h0c0 1-1 3-1 4-1 4-2 7-4 10v2l2-3c0-1 1-2 1-4 1 2 0 3 1 4 1 2 0 3-1 5l1 2h-4 0-1-2-2l1-1c0-1 0-1-1-2h0c2-2 3-5 4-7l2-4c0-2 1-3 2-4z" class="E"></path><path d="M341 459v-1c1-1 2-1 2-2v-1h1v2h0l1 2h-4z" class="b"></path><path d="M344 441c0 4-2 8-4 11 0 2-1 3-2 4 0-2 2-5 3-7s1-3 1-4c0-2 1-3 2-4z" class="M"></path><path d="M342 445c0 1 0 2-1 4s-3 5-3 7c0 1-1 2-1 2 0-1 0-1-1-2h0c2-2 3-5 4-7l2-4z" class="P"></path><path d="M350 431l2-3c0 10-2 19-3 29v1h-1v1c-1-1-1-1-1-2-1 1-1 1-1 2h-1l-1-2c1-2 2-3 1-5-1-1 0-2-1-4 1-2 2-4 2-6s2-4 2-6v-2l2-3z" class="D"></path><path d="M345 452l3-8c1-3 1-7 2-9l1 1-3 20 1 1v1h-1v1c-1-1-1-1-1-2-1 1-1 1-1 2h-1l-1-2c1-2 2-3 1-5z" class="J"></path><path d="M341 441c0 2-1 3-1 4-1 1-1 1-1 2l-1 1c1 0 1 1 2 1-1 2-2 5-4 7h0c1 1 1 1 1 2l-1 1h2 2 1 0 4 1c0-1 0-1 1-2 0 1 0 1 1 2v-1h1 0 1v-2l1-1 1 1v2h1v1h7c2 1 4 0 6 1h2 1c0 1 0 0-1 1l4 1h1v1h-1v1h-4c-1 0-3-1-4-1h-4l-11-1c-2 1-4 1-7 1-2 0-4 0-5 1-1 0-2 0-3 1l-1-1h-1l-1-3c1-8 5-14 10-20z" class="C"></path><path d="M364 463h8v1h-4c-1 0-3-1-4-1z" class="E"></path><path d="M342 462h9 0-1-1c-2 1-4 1-7 1v-1z" class="O"></path><path d="M342 462v1c-2 0-4 0-5 1-1 0-2 0-3 1l-1-1-1-1c2-1 7-1 9-1h1z" class="B"></path><path d="M338 448c1 0 1 1 2 1-1 2-2 5-4 7h0v1c-1 0-2 1-3 1l-1-1c3-2 4-6 6-9z" class="F"></path><path d="M349 458h1v-2l1-1 1 1v2h1v1h7c2 1 4 0 6 1h2 1c0 1 0 0-1 1h-19c-2 0-6 1-7 0v-1h1 2l4-1v-1z" class="o"></path><path d="M353 422c1 1 1 4 1 6l1 2c0 2 0 3 1 5v2 1c2 1 2 2 3 4 0 0 1 1 0 2l2 7c0 2 0 4 1 6v1c-1 1-1 1-2 1h0-7v-1h-1v-2l-1-1-1 1v2h-1 0v-1c1-10 3-19 3-29 1-2 1-3 1-5v-1z" class="Y"></path><path d="M354 428l1 2c0 2 0 3 1 5v2 1 3 1c-1 1-1 2-1 3l-1-4v-13z" class="K"></path><path d="M354 441l1 4 1 3c0 1-1 4 0 5v5h-2l-1-1c0-5 0-11 1-16z" class="f"></path><path d="M354 458c1-2 1-3 1-4l1-1v5h-2z" class="Y"></path><path d="M356 438c2 1 2 2 3 4 0 0 1 1 0 2l2 7c0 2 0 4 1 6v1c-1 1-1 1-2 1h0-7v-1-1l1 1h2v-5c-1-1 0-4 0-5l-1-3c0-1 0-2 1-3v-1-3z" class="P"></path><path d="M360 459v-1-4c0-3-1-5-1-7v-2-1l2 7c0 2 0 4 1 6v1c-1 1-1 1-2 1z" class="o"></path><path d="M357 446c-1-1-1-2-1-3h1c1 3 1 6 2 10 0 2 1 3 0 5-1 1-1 0-2 0h-1v-5c-1-1 0-4 0-5v2c1 0 1-3 1-4z" class="f"></path><path d="M357 457l1-1c0 1 1 1 1 2-1 1-1 0-2 0v-1z" class="b"></path><path d="M357 446v6c1 2 0 3 0 5v1h-1v-5c-1-1 0-4 0-5v2c1 0 1-3 1-4z" class="K"></path><defs><linearGradient id="AR" x1="368.94" y1="427.276" x2="359.499" y2="456.974" xlink:href="#B"><stop offset="0" stop-color="#a29f9f"></stop><stop offset="1" stop-color="#c5c4c5"></stop></linearGradient></defs><path fill="url(#AR)" d="M353 422c1-1 1-1 2-1 2 4 4 7 6 11 4 7 9 13 12 21 1 1 3 5 2 6 0 1-1 3-1 4h-1v-1h-1l-4-1c1-1 1 0 1-1h-1-2c-2-1-4 0-6-1h0c1 0 1 0 2-1v-1c-1-2-1-4-1-6l-2-7c1-1 0-2 0-2-1-2-1-3-3-4v-1-2c-1-2-1-3-1-5l-1-2c0-2 0-5-1-6z"></path><path d="M369 460c1 0 2 0 3 1v1l-4-1c1-1 1 0 1-1z" class="b"></path><path d="M355 430c0 1 1 1 1 2 1 0 2 0 2 1 2 3 4 6 5 9v1 1l5 13v2 1h-2c-2-1-4 0-6-1h0c1 0 1 0 2-1v-1c-1-2-1-4-1-6l-2-7c1-1 0-2 0-2-1-2-1-3-3-4v-1-2c-1-2-1-3-1-5z" class="O"></path><path d="M360 441h1c1 4 1 8 2 12l1 5v1c-1-1-1 0-1-1l-3-17z" class="b"></path><path d="M360 441h0c-1-1-2-3-3-5 0-1 1-2 1-3 2 3 4 6 5 9v1 1 2l1 6-1 1c-1-4-1-8-2-12h-1z" class="X"></path><path d="M363 444l5 13v2 1h-2c-2-1-4 0-6-1h0c1 0 1 0 2-1v-1s0 1 1 1h0c0 1 0 0 1 1v-1l-1-5 1-1-1-6v-2z" class="K"></path><path d="M364 452c1 2 1 4 1 6h-1l-1-5 1-1z" class="D"></path><path d="M349 462l11 1h4c1 0 3 1 4 1v2 5l-1 1c-1 5-1 11-1 16v7c0 1 2 1 3 1l2 1v2h-8-18c-2 0-6 0-7-1v-1-4h0c-1 1-2 3-3 4h-1c0-1 0-2-1-2v-1-3h0l-3-3s1 0 1-1h0c-2 0-5 0-7-1v-3h0c-1-1-1-3 0-4 0-1 0-1-1-2h0l-1-1h1c-1-1-1-2-1-4h1v1c1 1 1 1 2 1v1c1 0 3 0 4 1h3v-2c1 0 1-1 2-2l1-1h1v-2c0-2 0-3 1-5 1-1 3-1 5-1 3 0 5 0 7-1z" class="o"></path><path d="M350 475c1-1 1-2 2-3 1 0 2 1 2 2v1h-4z" class="N"></path><path d="M363 479v16h-5 3v-1c0-1 0-1 1-1 1-2 0-4 0-6s0-5 1-8z" class="T"></path><path d="M362 475c1 1 1 3 1 4-1 3-1 6-1 8s1 4 0 6c-1-3-1-6-1-9 1-2 0-4 1-5v-4z" class="Q"></path><path d="M358 490v-11c0-2 0-4 1-5s1-1 2-1 1 0 1 2c-1 0-1 0-2 1l-1 3c-2 3 0 8-1 11h0z" class="B"></path><path d="M349 462l11 1h-1c-2 0-6 0-7 1v1 2h-1 0v-3h0c-1 0-2 0-3 1-1-1-1-1-2-1-2 0-3 1-4 2v-1c1 0 1-1 2-1h0-2c-1 1-2 2-2 4v1c-1 2-1 5 0 7 0 2-1 4-1 6v-8c-1-3 0-6 1-8l-1-1c-2 1-1 5-2 7l-1 1v-2-2c0-2 0-3 1-5 1-1 3-1 5-1 3 0 5 0 7-1z" class="b"></path><path d="M358 490h0c1-3-1-8 1-11l1-3c1-1 1-1 2-1h0v4c-1 1 0 3-1 5 0 3 0 6 1 9-1 0-1 0-1 1v1h-3v-5z" class="G"></path><path d="M361 494c-1-1-1-1-2-1l1-1v-3c-1-2 0-3 0-5v-8c1 2 1 5 1 8s0 6 1 9c-1 0-1 0-1 1z" class="I"></path><path d="M350 475h4l1 20h-5c-1-6-1-14 0-20z" class="R"></path><path d="M340 481c1-3 1-5 2-9 2 2 3 3 3 5l1 18c-2 1-4 1-5 1-1-1-1-3-1-4v-11z" class="Q"></path><path d="M342 479l1-1h1v1c-1 4 0 7 0 10v4s-1 1-2 1v-1c1-2 0-4 0-6v-8z" class="j"></path><path d="M342 479v8c0 2 1 4 0 6v1c0 1-1 1-1 2-1-1-1-3-1-4v-11l1-1h1v-1z" class="R"></path><path d="M336 473l1-1c1-2 0-6 2-7l1 1c-1 2-2 5-1 8v8 4c-1 2 0 5 0 7v1c0 1-1 2-1 3v-4h0c-1 1-2 3-3 4h-1c0-1 0-2-1-2v-1-3h0l-3-3s1 0 1-1h0c-2 0-5 0-7-1v-3h0c-1-1-1-3 0-4 0-1 0-1-1-2h0l-1-1h1c-1-1-1-2-1-4h1v1c1 1 1 1 2 1v1c1 0 3 0 4 1h3v-2c1 0 1-1 2-2l1-1h1v2z" class="P"></path><path d="M335 489h1v3h-1c-1-1-1-2 0-3z" class="O"></path><path d="M335 477h1v-3h1s1 0 2 1l-2 1v7c0 1-1 2-2 3v-4-5z" class="C"></path><path d="M334 472l1-1h1v2h-1v4 5 4c-1 0-1 0-1 1l-1-2-1-1 1-1c0-2-1-4 0-5v-2h-1v-2c1 0 1-1 2-2z" class="M"></path><path d="M334 472v13h-1 0l-1-1 1-1c0-2-1-4 0-5v-2h-1v-2c1 0 1-1 2-2z" class="P"></path><path d="M322 472h1v1c1 1 1 1 2 1v1c1 0 3 0 4 1h3 1v2c-1 1 0 3 0 5l-1 1 1 1 1 2h-3 0c-2 0-5 0-7-1v-3h0c-1-1-1-3 0-4 0-1 0-1-1-2h0l-1-1h1c-1-1-1-2-1-4z" class="C"></path><path d="M326 485l2-1v-2h1c1 1 0 2 1 4h-2c-1 0-1-1-2-1h0z" class="B"></path><path d="M330 486c1-1 1-2 1-2h1l1 1 1 2h-3 0l-1-1z" class="T"></path><path d="M324 483v1c1 0 1 1 1 1h1 0c1 0 1 1 2 1h2 0l1 1c-2 0-5 0-7-1v-3z" class="S"></path><path d="M329 476h3-1l1 1-1 1v4h-1c-1-1-1-4-1-6h0z" class="P"></path><path d="M332 476h1v2c-1 1 0 3 0 5h-1-1v-1-4l1-1-1-1h1z" class="O"></path><path d="M322 472h1v1c1 1 1 1 2 1v1c-1 2 0 4 0 6 0 1 0 1-1 2-1-1-1-3 0-4 0-1 0-1-1-2h0l-1-1h1c-1-1-1-2-1-4z" class="T"></path><path d="M422 477c1 3 1 5 1 8l1-6c0-1 0-1 1-1 1 6 1 13 0 20v1c0 1 0 2-1 2l1 1v-1h3 1c2 8 2 15 2 23 0 10 1 21-1 30-1 3-2 5-4 8-3 0-6 0-9 1l-1 1c-2 2-4 4-4 7 0 1 0 3 1 4h0l2 3h0 2c1 0 6 1 7 1h2 9 3 6l1 1-1 1h-5v1c2 0 4 0 6 1v1h-3v1h-1-3-4l1 1v1 1h-2v2l1 1-1 1c-1 1-1 1-1 3h0c1 0 1-1 1-1h3 6l5 1h5c-1 0-2 0-3 1h0 3 3 4 1l5-1c0 1 0 1 1 1l3 1c1 0 1 0 2 1h-1l1 1c1-1 2-1 3-1h2l2 1-1 1-19 6h-1c-2 1-4 1-6 2l-12 3c-10 2-21 4-31 5-6 1-12 2-18 2h-18l-21-2c-15-1-38-3-48-15-3-4-4-8-6-13-4-12-6-25-7-38v-10l1-1h3l5 1h1 11 0c-1 0-2 0-3-1l1-1c1 0 2 0 3 1h2v1h5 4 2v-10-9-2c1-2 1-6 0-9v-6l-1-1v-7l-1-1 1-2v-1c-1-1-1-1-1-2h1v1h1v-1c0-1 0-2-1-3v-1c2 1 5 1 7 1h0c0 1-1 1-1 1l3 3h0v3 1c1 0 1 1 1 2h1c1-1 2-3 3-4h0v4 1c1 1 5 1 7 1h18 8v-2c0 1 0 1 1 1v1h1v-1h3v3h1v9c1 1 1 2 2 3 2-2 2-4 3-6 2-3 5-5 9-7 1 0 2 0 3-1-1-1-1-1-1-2 0-2 1-3 2-5s3-3 6-3h1 1c0 1 1 1 1 2l3-3c1 0 2-1 2-1h1c1-1 4-1 5-1s1 0 2-1c0-2 2-5 4-6 0-1 1-2 1-2z" class="w"></path><path d="M416 526h2v1h-1l-1-1zm-18 52h-8c-1 0-3 0-4-1h11 0l1 1zm23-39c0-1 1-2 2-3v1h1 0v2h-3z" class="Y"></path><path d="M400 606c1 0 3 0 4-1 2 0 7 1 9 1-2 0-5 1-8 0h-5z" class="o"></path><path d="M303 557c-1 1-4 1-6 1h0l-1-1h0c1-1 2 0 3 0h5-1z" class="Y"></path><path d="M394 603h4v2l2 1h-7c0-1 1-2 1-3z" class="E"></path><path d="M300 545c2-1 3-1 5 0h0l1 1c0-1 0-1 1-1l-1 1h-2-1s-1 1-2 1h0-1c-1 0-2 0-3-1v-1h3zm97 39l3-1h4c2 1 4 1 6 0h8c2 0 4 0 5 1h-7-8c-2 0-5 1-7 0h-4z" class="J"></path><path d="M402 545h2c1 0 4 0 4-1l1 1c1 0 5-1 6 0 4 0 9 0 13 1-2 0-4 1-7 1 1 0 1-1 2-1h0c-2 0-5-1-6 0-3 0-5-1-8-1h-7 0z" class="Y"></path><path d="M421 479h0 0v1c0 1 0 3-1 4-1 2 0 2-2 3-1 0-1-1-1-2 0-2 2-5 4-6z" class="f"></path><path d="M364 554h13l2 1c-3 0-6 0-10 1h-6l-1-1h-7c3-1 6-1 9-1zm32 15l2 1c1 0 2 0 3 1h8c-3 1-8 0-11 1l-10 1v-1c2 0 3 0 5-1 1 0 2 0 3-1v-1z" class="J"></path><path d="M398 570c1-1 1-1 2-1h1 6c1 0 2 0 3 1h0l-1 1h-8c-1-1-2-1-3-1z" class="U"></path><path d="M413 575l2 3h-17l-1-1h0l15-1 1-1zm-91 6h1l1-1c1-1 5-1 6-1 1-1 3 0 4 0h10l-22 2z" class="J"></path><path d="M358 583l10-2c-1 2-1 2-2 2v1l-12 1c1-1 2-1 3-2h1z" class="E"></path><path d="M338 555h-5c-3 0-7 0-10-1h-3c-2-1-3 0-5-1h4 2c1 1 3 0 4 0h9 6 0c-1 0-3 0-4 1h0 2v1z" class="F"></path><path d="M304 557c3 0 6-1 9-1 5 0 10 1 15 2h-6l-19-1h1z" class="l"></path><path d="M352 579h8c3 1 6 1 8 1h1c1 1 2 1 4 1l-1 1h-2c-1 0-1-1-2-1l-10 2c0-1 1-2 0-3l-6-1z" class="B"></path><path d="M310 542h5l-8 1c4 2 10 1 14 1l-8 1h0-4-1c-5-2-9-2-14-2v-1h16z" class="P"></path><path d="M325 562l-25 1v-1l18-1h10l1 1h-4z" class="B"></path><path d="M363 572h3c7 0 15 1 22 0v1l-25 1v-2z" class="X"></path><path d="M384 569c2-2 9-2 12-2h3c3 1 5 1 8 2h-6-1c-1 0-1 0-2 1l-2-1c-3-1-8 0-12 0z" class="D"></path><path d="M351 569c4 0 8 0 12 1h6v1c-1 0-2 0-3 1h-3l-8-1-4-2z" class="E"></path><path d="M386 605h0c0 1-1 1-2 1s-3 1-4 2h-1-2c-3 0-3 0-6-1h-2 0-2v-1l19-1z" class="K"></path><path d="M355 571l8 1v2h-11v-1l-1-1c-1 1-2 1-2 1l-1-1v-1h3 4z" class="B"></path><path d="M402 545h7v1h-1v1h1l1 1c-2 0-3 0-4 1h-11c1 0 2 0 2-1h-2 0-3v-1h2 0c2-1 4 1 7-1 0 0 1 0 1-1z" class="P"></path><path d="M388 545l13 1c-3 2-5 0-7 1h0-2v1h3 0 2c0 1-1 1-2 1h-2-5-2l1-1h-1c-1 0-2 0-3-1 1-1 2-1 4-1h1v-1z" class="O"></path><path d="M340 588c3 1 6 2 9 2h10c-7 1-14 1-21 1h0c-1-1-3-1-4-2h1l1-1h4z" class="R"></path><path d="M360 579c7-1 15-2 22-1h0c-1 1-1 1-2 1l-1 1h-11c-2 0-5 0-8-1zm49-34c3 0 5 1 8 1 1-1 4 0 6 0h0c-1 0-1 1-2 1-1 1-3 2-5 2h-10c1-1 2-1 4-1l-1-1h-1v-1h1v-1z" class="b"></path><path d="M409 545c3 0 5 1 8 1-3 2-7-1-9 1v-1h1v-1z" class="E"></path><path d="M394 565c2-1 3-1 5-1-1 1-2 1-3 1h0c-1 1-1 1-2 1h0-2 4l1 1h2-3c-3 0-10 0-12 2-2 0-4-2-6-2-1 0-1-1-2-1 2-1 4-1 5-1 5 0 9 1 13 0z" class="J"></path><path d="M414 507v-2c0-2 1-3 2-4s2-2 4-2h0v1l-1 1c-1 2-2 4-1 7v3 1h-2c-1-1-1-3-2-5z" class="o"></path><path d="M414 507h1v-1c0-2 0-3 1-4l1-1c1 1 0 0 0 1s-1 3-1 4 1 2 1 3 0 2 1 3v-1 1h-2c-1-1-1-3-2-5z" class="Y"></path><path d="M315 565c5-1 10 0 15 1l-16 1-13-1c5-1 10-1 14-1z" class="H"></path><path d="M471 599c1-1 2-1 3-1h2l2 1-1 1-19 6c0-1 1-1 2-2h1l4-1 1-1h1c1 0 1 0 2-1l-1-1-2 1h-1c-1 1-1 1-2 1 0 0-1 0-2 1h-1 0-2v1l-1-1s4-1 5-2c3-1 6-1 9-2z" class="D"></path><path d="M409 515c2-1 4-3 6-2h1c1 1 2 1 4 1v-1h1v1c-1 2-5 2-5 4v1l-1-1c-1-1 0-1 0-2-1 0-2 1-3 1h-2c-1 1-2 1-3 2h-2c0-1 1-1 2-2 1 0 1 0 2-1v-1z" class="J"></path><path d="M367 607c0 1 0 1-1 1l2 1c-1 0-1 1-1 1h-2c-1 0-3-1-5-1l-22 1c4-1 8-1 12-2l17-2v1z" class="E"></path><defs><linearGradient id="AS" x1="328.263" y1="548.688" x2="325.604" y2="544.421" xlink:href="#B"><stop offset="0" stop-color="#c2bdc4"></stop><stop offset="1" stop-color="#d7d8d2"></stop></linearGradient></defs><path fill="url(#AS)" d="M309 545h4c3 0 15 0 16 1h7l-4 1c-2 1-3 1-5 1h-3s0 1-1 1h0-1-3c-1-1-3-1-5-1h-9c1 0 2-1 3-1 2 1 4 0 6 0 1 0 4 1 5 0s1 0 2 0l-1-1h-3-2-4 0c-1-1-2-1-2-1z"></path><path d="M322 549c-1 0-1-1-2-2h2 3v1h-2v1h-1z" class="X"></path><path d="M408 589c3-1 6 0 9-2h1 1 0c-2-1-3-1-5-1-3 0-7 1-10 1-5 1-9 1-14 2h-3c-1 1-1 0-1 0 1-1 1 0 2 0 4-1 8-1 13-2l6-1 1-1h2c1-1 3 0 5 0h0-2v1h5c1 0 1 0 2-2l2 1s-1 0-1 1h0 1v1h-4c1 1 1 1 1 2h-11z" class="Y"></path><path d="M406 601h4c-1 1-2 1-3 2h0v1 1c3 0 7-1 10-1h2 4c-1 1-3 1-4 1l-6 1c-2 0-7-1-9-1-1 1-3 1-4 1h0l-2-1v-2h1l7-2z" class="P"></path><path d="M402 604c2-1 3 0 5 0v1h-4 0l-1-1z" class="C"></path><path d="M406 601h4c-1 1-2 1-3 2h0v1c-2 0-3-1-5 0l-1 1-1-1v-1h-1l7-2z" class="E"></path><defs><linearGradient id="AT" x1="322.191" y1="552.171" x2="319.059" y2="546.135" xlink:href="#B"><stop offset="0" stop-color="#bab3b7"></stop><stop offset="1" stop-color="#c2c7c3"></stop></linearGradient></defs><path fill="url(#AT)" d="M305 548h9c2 0 4 0 5 1h3 1 0c4 1 7-1 10 1h1 0-1c-4 1-9 1-13 1-3 0-5-1-8 0h0c0-1 0 0 1-1h-3c-2-2-7-1-10-1h-4c2-1 7-1 9-1z"></path><path d="M386 605c3-1 6-1 8-2 0 1-1 2-1 3-1 0-2 0-3-1h-2l-1 1h2c0 1-1 1-1 2-1 0-1 0-1 1l-1-1-1 1h0 3 0l-11 1h-7l-1-3h0 2c3 1 3 1 6 1h2 1c1-1 3-2 4-2s2 0 2-1h0zm-61-23c1 0 2 1 3 1 1-1 1-1 2-1 0 0 0 1 1 1l2 1h6c1 0 1 1 2 1 1 1 2 1 4 1h-3-1l1 1c1 0 1 0 2 1h2 0c1 1 2 2 3 2-3 0-6-1-9-2-1 0-5-1-6-1-4 1-6 1-10 1-2 0-4 0-6-1h1c3-1 5-1 8-1h6l2 1 2-2c-1-1-5-1-6-1-2-1-5 1-8-1h0l2-1z" class="f"></path><path d="M377 554h0c0-1-1-1-2-2 0 0-1 0-1-1h0c1 0 2-1 3-1h9c-3 0-5 0-8 1h0-3c1 1 2 1 3 1h6 4 0c3-1 6 0 8-1h4 12l1 1h-5-8c-2 0-3 1-5 0h0-1v1c1 0 3 1 4 0 1 0 1 0 1 1h-8c-4-1-8 1-12 1l-2-1z" class="E"></path><path d="M388 552h4v1h-4v-1z" class="K"></path><path d="M421 601l5-1 1 1c1 1 1 1 0 2l-4 1h-4-2c-3 0-7 1-10 1v-1-1h0c1-1 2-1 3-2h2 9z" class="F"></path><path d="M421 601l5-1 1 1c1 1 1 1 0 2l-4 1h-4-2c1-1 4-1 5-2h2v-1h-3z" class="C"></path><path d="M335 596h3c1 1 1 1 2 1h0l-2 2-1 1h0c-1 0-2 1-3 1l-1 1h-18v-1c2 0 5 1 7 0v-1c-2 0-4-1-7-1 1-1 4 0 5 0 4 0 9 0 12-1 1-1 2-1 3-2z" class="H"></path><path d="M335 596h3c1 1 1 1 2 1h0l-2 2c-2-1-2-1-4 0-1 0-1-1-2-1 1-1 2-1 3-2z" class="M"></path><path d="M318 587c2 1 4 1 6 1 4 0 6 0 10-1 1 0 5 1 6 1h-4l-1 1h-1c1 1 3 1 4 2h0l-28-1h-9c4-1 7-1 11-1 2-1 4-1 6-2z" class="F"></path><path d="M315 599l-1 1c0-1 0-1-1-1s-3 0-4-1l-1-1c-1 0 0 0-1-1h3l12-1c2 0 5 1 7 1h6c-1 1-2 1-3 2-3 1-8 1-12 1-1 0-4-1-5 0z" class="J"></path><path d="M336 546h5 5c-1 0-2 1-3 1-2 1-6 1-8 2h3 3 6c2 0 4-1 6 0h0c-1 1-2 1-4 1 2 1 3 1 4 2-4 0-8 1-13 1h-6c3 0 6 0 8-1h2 1 3c1-1 1-1 2-1h-2-1c-1 0-2-1-3-1h-7c-1 1-3 0-4 0h1 0-1c-3-2-6 0-10-1 1 0 1-1 1-1h3c2 0 3 0 5-1l4-1z" class="E"></path><path d="M336 546h5 0-2c-1 0-1 1-2 1s-2 1-2 1c-1 0-1 0-1 1h0c1 0 2 0 2 1h-2 0-1c-3-2-6 0-10-1 1 0 1-1 1-1h3c2 0 3 0 5-1l4-1z" class="K"></path><path d="M353 552c2 0 4-1 6-2 2 0 5 0 7 1 2-1 4-1 6 0h1c0 1-1 2-2 3h-7 0c-3 0-6 0-9 1h-17v-1h-2 0c1-1 3-1 4-1h0c5 0 9-1 13-1z" class="C"></path><path d="M366 551c2-1 4-1 6 0v1c-4 0-7-1-10 0h-1 0v-1h5z" class="T"></path><path d="M328 558l1-1h12c7 0 15 0 22 1h7l20-1 17 1c4-1 10-1 14 0h-26l-12 1c-5 0-9-1-14 0h-1-2-8-18c-2 0-7 0-8-1h-9-1 6z" class="b"></path><path d="M368 580h11l17 1 3 1h0 5v1h-4l-3 1h-3c-5 1-11 0-16 0h-12v-1c1 0 1 0 2-2 1 0 1 1 2 1h2l1-1c-2 0-3 0-4-1h-1z" class="P"></path><path d="M396 581l3 1h0 5v1h-4l-3 1h-3c-3 0-8 1-11 0 3-1 7-1 10-1 1-1 2-1 3-2z" class="M"></path><path d="M352 574c-3 1-7 0-10 1h-4c-7 1-14 1-21 1-5 1-10 1-15 1 1-1 1 0 2 0l31-3c3 0 8 1 10-1h1c-8-3-17-2-24-2h-14l1-1h23c6 0 12-1 19-1l4 2h-4-3v1l1 1s1 0 2-1l1 1v1z" class="C"></path><path d="M426 600h8 0c1 1 2 2 4 2h2 3v1h-4c0 1 1 1 2 1h13c-7 2-13 2-20 4-2 0-4 0-6 1h-5c-5 1-11 1-16 1v-1l16-1h6l5-1-7-1c-3 0-6 0-8-1 1 0 3 0 4-1l4-1c1-1 1-1 0-2l-1-1z" class="K"></path><path d="M427 603h1c2 0 4-1 5 0l1 1c-1 1-1 1-2 1s-4 0-5 1c-3 0-6 0-8-1 1 0 3 0 4-1l4-1z" class="o"></path><path d="M344 579h8l6 1c1 1 0 2 0 3h-1c-1 1-2 1-3 2-3 0-6 1-9 1-2 0-3 0-4-1-1 0-1-1-2-1h-6l-2-1c-1 0-1-1-1-1-1 0-1 0-2 1-1 0-2-1-3-1l-2 1c-1 0-5 0-6-1 1-1 3 0 5-1l22-2z" class="N"></path><path d="M325 582c4-1 8 0 12 0s8 0 11 2l9-1c-1 1-2 1-3 2-3 0-6 1-9 1-2 0-3 0-4-1-1 0-1-1-2-1h-6l-2-1c-1 0-1-1-1-1-1 0-1 0-2 1-1 0-2-1-3-1z" class="X"></path><path d="M337 582c4 0 8 0 11 2-3 0-6 0-8-1-1 0-1-1-2-1h-1z" class="L"></path><path d="M419 589h3v1l-4 1c-2 1-4 2-6 2h-1-4l-8 2-14 1c-5 0-10 0-15 1 1-1 2-1 3-1h0c-1 0-2 0-3-1l11-1c0 1 1 1 1 1 0-1 1-1 1-1 1-1 2-1 3-2 7-2 15-2 22-3h11z" class="D"></path><path d="M399 595c1-1 1-2 2-3h2c2-1 3-1 5-2v1h2c-1 0-1 0 0-1l1 1h4 3c-2 1-4 2-6 2h-1-4l-8 2z" class="J"></path><path d="M405 529c1 0 1 1 1 0h1c3-1 4-2 7-2-3 1-5 2-6 4-1 0-1 0-1 1s-2 2-3 3c1 0 2-1 3-2 0 0 1-1 2-1 1-1 2-2 3-1 1 0 2 1 3 1v2h0c-1 0-2 1-3 1 1 1 1 1 2 1 0-1 0-1 1-1l1-1c1-1 2-1 3-1h0l1 1v-1c2 0 2 0 3 1v1 1c-1 1-2 2-2 3h-1-4c-1 0-2 1-4-1v1l-1 1h-1c-1-1-1-1-2-1h0l-1 1h-2-1-1-1-2-1-2c2-4 4-7 7-10l1-1z" class="b"></path><path d="M405 540v-2c2-1 2-1 4-1h-1-1v1h1 1-2l1 1-1 1h-2zm-5 0c1-2 3-4 5-5h2c0 1-1 1-2 2s-1 1-2 3h0-1-2z" class="K"></path><path d="M346 596c1 0 4-1 6 0h4c5 0 10-1 14-1 1 1 2 1 3 1h0c-1 0-2 0-3 1 5-1 10-1 15-1h-1c-4 1-8 1-12 1h-1c1 1 3 1 4 1h2c1 0 1 0 2 1h1 0c-6 0-13 0-18 1-3 1-5 0-8 1l-21 1 1-1c1 0 2-1 3-1h0l1-1 2-2h0c-1 0-1 0-2-1h8z" class="J"></path><path d="M338 599l2-2c1 1 2 1 3 1h1v1c-2 0-4 1-6 2h-4c1 0 2-1 3-1h0l1-1z" class="B"></path><path d="M357 596h5l1 1-1 1c-2 1-5 1-8 1-1 0-2 0-3-1h1 3c1 0 3 0 4-1-1 0-1 0-2-1z" class="f"></path><path d="M338 596h8c1 0 3 1 4 1v1c-1 1-4 1-6 1v-1h-1c-1 0-2 0-3-1h0c-1 0-1 0-2-1z" class="O"></path><path d="M379 539c1 0 3 1 4 0h0 2 7l5 1h2 1 2 0c-1 1-1 1-2 1h0-3 1c1 0 2 0 3 1 1 0 1 1 2 1h1 5-2s0 1-1 1v-1h-1c-1 1-2 1-3 0-1 0-2-1-3-1v1h0 0c1 1 2 2 3 2h0 0c0 1-1 1-1 1l-13-1h-4l-4 1c-5 0-10-1-15-1l-1-1v-1c2-1 7 0 9 0-3-1-5-1-8-1-1 0-3 1-4 0l1-1h7 3c2 1 4 1 6 1h0c-1-1 0-2 1-3z" class="J"></path><path d="M386 545c1 0 2 0 3-1h11l1 1h1 0 0c0 1-1 1-1 1l-13-1h-4 2z" class="E"></path><path d="M373 543c-3-1-5-1-8-1-1 0-3 1-4 0l1-1h7 3c2 1 4 1 6 1h13c1 0 3-1 5 0-1 1-2 1-4 1s-5-1-8 0h-11z" class="D"></path><path d="M373 543h11 0c3 1 5 0 7 0v1h-3c0 1-1 0-2 1h-2l-4 1c-5 0-10-1-15-1l-1-1v-1c2-1 7 0 9 0z" class="Y"></path><path d="M365 545c5 0 10 1 15 1l4-1h4v1h-1c-2 0-3 0-4 1 1 1 2 1 3 1h1l-1 1h2 5l-7 1h-9c-1 0-2 1-3 1h0c0 1 1 1 1 1 1 1 2 1 2 2h0-13 0 7c1-1 2-2 2-3h-1c-2-1-4-1-6 0-2-1-5-1-7-1-2 1-4 2-6 2-1-1-2-1-4-2 2 0 3 0 4-1h0c-2-1-4 0-6 0h-6-3-3c2-1 6-1 8-2 1 0 2-1 3-1l12-1h7z" class="X"></path><path d="M384 545h4v1h-1c-2 0-3 0-4 1-2 0-4 0-6-1h3l4-1z" class="B"></path><path d="M358 545h9 0l7 2c-7 1-14 1-21 2-2-1-4 0-6 0h-6-3-3c2-1 6-1 8-2 1 0 2-1 3-1l12-1z" class="J"></path><path d="M318 561c1 0 2-1 3-1h1 1 3 0 2 1 0 1 0 1 1 0l38 3c3 0 8-1 11 0h2 24 10 0l-1 1h0-17c-2 0-3 0-5 1-4 1-8 0-13 0-1 0-3 0-5 1h-11l-11-1c-4-1-7-1-10-2h-10c-3-1-6 0-9-1h4l-1-1h-10z" class="D"></path><path d="M370 563c3 0 8-1 11 0h2c-1 0-2 0-3 1h-5c-2-1-4-1-5-1z" class="b"></path><path d="M381 565h5v-1c2 0 6 0 8 1-4 1-8 0-13 0z" class="K"></path><defs><linearGradient id="AU" x1="442.753" y1="586.57" x2="405.481" y2="578.97" xlink:href="#B"><stop offset="0" stop-color="#9d9b9d"></stop><stop offset="1" stop-color="#e5e5e4"></stop></linearGradient></defs><path fill="url(#AU)" d="M415 578h2c1 0 6 1 7 1h2 9 3 6l1 1-1 1h-5v1c2 0 4 0 6 1v1h-3v1h-1-3-4l1 1v1 1h-2l-5 1c-2 0-4 1-6 1v-1h-3c0-1 0-1-1-2h4v-1h-1 0c0-1 1-1 1-1 1-1 0-1 1-1-1-1-3-1-5-1h-8c-2 1-4 1-6 0v-1h-5 0c4 0 10-1 15-1 1 0 1-1 2-1l-1-2z"></path><path d="M419 580c1 0 3 0 4 1-2 1-2 1-3 1v-1l-1-1z" class="E"></path><path d="M424 579h2v1h6 0v1h0l1 1c-2-1-3 0-5-1l-4-2z" class="M"></path><path d="M415 578h2c0 1 1 1 2 2l1 1v1c-2 0-4 0-6-1 1 0 1-1 2-1l-1-2z" class="b"></path><path d="M426 579h9c2 0 3 1 4 1v1h-4l1 1h0-3l-1-1h0v-1h0-6v-1z" class="S"></path><path d="M438 579h6l1 1-1 1h-5v1h-3 0l-1-1h4v-1c-1 0-2-1-4-1h3z" class="R"></path><path d="M418 583h4c4 0 8 0 11 1 2 0 3 0 5 1h-4l1 1v1 1h-2l-5 1c-2 0-4 1-6 1v-1h-3c0-1 0-1-1-2h4v-1h-1 0c0-1 1-1 1-1 1-1 0-1 1-1-1-1-3-1-5-1z" class="D"></path><path d="M424 588v-1c1 0 2 0 3-1 2 0 5-1 7-1l1 1v1 1h-2l-5 1c-2 0-4 1-6 1v-1h-3c0-1 0-1-1-2h4v1h2 0z" class="O"></path><path d="M419 589c0-1 0-1-1-2h4v1h2 0c2-1 4-1 6-1-2 1-5 2-8 2h-3z" class="f"></path><defs><linearGradient id="AV" x1="459.543" y1="605.761" x2="445.358" y2="593.541" xlink:href="#B"><stop offset="0" stop-color="#9b9898"></stop><stop offset="1" stop-color="#b4b4b5"></stop></linearGradient></defs><path fill="url(#AV)" d="M432 595c1 0 1-1 1-1h3 6l5 1h5c-1 0-2 0-3 1h0 3 3 4 1l5-1c0 1 0 1 1 1l3 1c1 0 1 0 2 1h-1l1 1c-3 1-6 1-9 2-1 1-5 2-5 2l-3 1h-13c-1 0-2 0-2-1h4v-1h-3-2c-2 0-3-1-4-2h5l1 1h0 0l1-1c-1 0-2-1-3-1h0c-1-1-2-1-2-2l-1 1h-2c-1 0-1-1-1-1 1-1 2-1 3-1h0v-1h-3z"></path><path d="M455 596h4 1l1 1v1h-1l-1 1h-4-9c3-1 5 0 8 0v-1h1v-2z" class="H"></path><path d="M459 596h1l1 1v1h-1l-1 1-1-1c0-1 0-1 1-2z" class="Q"></path><path d="M460 596l5-1c0 1 0 1 1 1l3 1c1 0 1 0 2 1h-1c-3 1-6 1-9 1l-1-1h1v-1l-1-1z" class="B"></path><path d="M460 596l5-1c0 1 0 1 1 1v1c-1 0-1 0-1 1h-4v-1l-1-1z" class="T"></path><defs><linearGradient id="AW" x1="446.285" y1="600.663" x2="443.227" y2="593.777" xlink:href="#B"><stop offset="0" stop-color="#928f90"></stop><stop offset="1" stop-color="#b5b4b5"></stop></linearGradient></defs><path fill="url(#AW)" d="M442 594l5 1h5c-1 0-2 0-3 1h0 3 3v2h-1v1c-3 0-5-1-8 0h-1c-2-1-3-1-5-1l-1-1h-2l-1-1h5c1-1 1-1 1-2z"></path><path d="M452 596h3v2h-1-1l-1-2z" class="B"></path><path d="M432 595c1 0 1-1 1-1h3 6c0 1 0 1-1 2h-5l1 1h2l1 1 2 2v1c0 1 1 0 2 1h3 8c2-1 3-2 5-2 1 0 2 1 2 1-1 1-5 2-5 2l-3 1h-13c-1 0-2 0-2-1h4v-1h-3-2c-2 0-3-1-4-2h5l1 1h0 0l1-1c-1 0-2-1-3-1h0c-1-1-2-1-2-2l-1 1h-2c-1 0-1-1-1-1 1-1 2-1 3-1h0v-1h-3z" class="U"></path><path d="M401 489h1 1c0 1 1 1 1 2l3-3c1 0 2-1 2-1h1c1-1 4-1 5-1h0c-1 1-2 1-3 1h0c-1 1-2 1-2 1l-1 1c2 0 1 0 3 1 0 0 1-1 2-1v1h1 0 3 0c-1 0-2 0-3 1h0l-3 1c-1 0-1 0-2 1h0c-1 0-1 1-1 1 1 1 2 0 3 0l-1 1c-2 1-3 1-4 2v1l-1 1v1h0c0 1 0 1-1 1h0l-1 2c1 0 1-1 2-1 0-1 0-3 1-3 1 1 0 2-1 3v3h0c-2 0-3 0-5 1-1 1-1 1-2 3v1h0-1l-3 3c1-2 1-4 2-5v-1-1l2-1v-1h-1c-1 1-2 0-3 1l-3 1h0l1-1 2-2h-1c-1 0-2 1-3 1l1-1-1-2v-1c1 0 2 0 3-1-1-1-1-1-1-2 0-2 1-3 2-5s3-3 6-3z" class="Y"></path><path d="M400 496h1v6 1l-2-2c1-2 0-3 1-5z" class="f"></path><path d="M397 497c1 0 1-1 2-1 0-2 1-3 2-4l1 1-1 1c0 1 0 1-1 2h0c-1 2 0 3-1 5h0c-1 1-1 1-2 1h-1c0-1 1-1 1-1v-2l-1-1c1 0 1-1 1-1z" class="o"></path><path d="M400 496h0c-1 2 0 3-1 5h0l-1-1c0-1 0-2 1-2v-1c1-1 0-1 1-1z" class="J"></path><path d="M401 489h0c-1 1-1 1-2 1-2 0-3 2-4 4-1 1-1 2-1 3l1 1h0v-1c0-2 2-4 4-6h2 0c-2 2-4 3-4 5v1s0 1-1 1v1l-1 1c0 1 0 1-1 1h0c0-1 0-1 1-1h0v-1h-1c-1-1-1-1-1-2 0-2 1-3 2-5s3-3 6-3z" class="D"></path><path d="M404 503c0 1 0 1-1 1v-1c-1-1-1-2 0-3l1-1h0v-2-2h0c1-2 1-2 2-3s2-3 4-3l1 1c1 0 2 0 4 1l-3 1c-1 0-1 0-2 1h0c-1 0-1 1-1 1 1 1 2 0 3 0l-1 1c-2 1-3 1-4 2v1l-1 1v1h0c0 1 0 1-1 1h0l-1 2z" class="o"></path><defs><linearGradient id="AX" x1="345.023" y1="535.303" x2="344.614" y2="544.803" xlink:href="#B"><stop offset="0" stop-color="#cdcdcd"></stop><stop offset="1" stop-color="#f3f1f3"></stop></linearGradient></defs><path fill="url(#AX)" d="M370 533h0c0 2 0 5 1 6s4 1 5 0h3c-1 1-2 2-1 3h0c-2 0-4 0-6-1h-3-7l-1 1c1 1 3 0 4 0 3 0 5 0 8 1-2 0-7-1-9 0v1l1 1h-7l-12 1h-5-5-7c-1-1-13-1-16-1h0l8-1c-4 0-10 1-14-1l8-1h-5 2c1-1 1-1 2-1 1-1 3 0 4 0h1 0c1-1 6 0 7 0-1-1-1 0-2 0s-1-1-2 0c-1 0-1 0-1-1-3 1-7 0-10 0h0c-1 0-2 0-3-1l1-1c1 0 2 0 3 1h2v1h5 4 2 2c2 0 4 0 6-1h16 1 7 8 2l1-2 2-2v-2z"></path><path d="M309 538c1 0 2 0 3 1h2v1h-3c-1 0-2 0-3-1l1-1z" class="B"></path><path d="M355 541v-1c1 0 3 0 4 1v1h0-38-4c2 0 3-1 5-1h5 3 4 5c2 1 6 1 8 1 2-1 5 0 7 0 0-1 1-1 1-1z" class="E"></path><path d="M321 544h21c3 0 8 1 11 0s8-1 11 0l1 1h-7l-12 1h-5-5-7c-1-1-13-1-16-1h0l8-1z" class="B"></path><path d="M391 500v1l1 2-1 1c1 0 2-1 3-1h1l-2 2-1 1h0l3-1c1-1 2 0 3-1h1v1l-2 1v1 1c-1 1-1 3-2 5l3-3h1l-1 3c-1 1 0 2 0 3h0c0-1 1-1 1-1 1 0 1-1 1-1h1c0-1 0-1 1-1h0v2 1c0-1 1-1 2-1l2-1c0-1 1-2 2-2 0 1-1 1-1 2v1h2 0v1c-1 1-1 1-2 1-1 1-2 1-2 2-1 0-3 2-4 2-1 1-1 0-2 1l-2 2h-1-1l-1 1-1 2c-1-1-2-1-2-2l-2-1c0-1 0-2-1-3v-1c0-1 0-1 1-1-2-1-2-1-4 0l-1-4h-1c0 1-1 1 0 2l1 1-1 1h-1-1v-3c0-1-1-1-1-1l-1 1v2s1 0 1 1h-2l-1 1v-3-7c1 1 1 2 2 3 2-2 2-4 3-6 2-3 5-5 9-7z" class="J"></path><path d="M395 515l1-1c1 1 1 2 1 3v1h-1v-1l-1-1v-1z" class="f"></path><path d="M391 525h0c1-1 2-1 2-2h0l1 2-1 2c-1-1-2-1-2-2z" class="E"></path><path d="M395 516l-1 1h-1v-1c1-2 1-3 1-5h1v1 2 1 1zm0-11c0 1 0 1-1 1-2 2-2 3-3 5h-1v-2c1-1 1-2 2-3l3-1z" class="K"></path><path d="M391 515l1 1v1h1v2h-1l-2-1c0 1 0 1-1 1 0 1 0 1-1 2v-1c0-1 0-1 1-1-2-1-2-1-4 0l-1-4c1 1 1 1 2 1h0 2 1l1-1 1 1v-1zm15-1c0-1 1-2 2-2 0 1-1 1-1 2v1h2 0v1c-1 1-1 1-2 1-1 1-2 1-2 2-1 0-3 2-4 2-1 1-1 0-2 1l-2 2h-1-1c0-1 1-2 2-3 0-1 1-3 2-5 0 0 1-1 1 0h0c0 1-1 2-1 3 2 0 3-3 5-4l2-1z" class="f"></path><path d="M406 514c0-1 1-2 2-2 0 1-1 1-1 2v1h2c-2 1-4 3-7 3 1-1 4-3 4-4z" class="J"></path><path d="M391 500v1l1 2-1 1c1 0 2-1 3-1h1l-2 2-1 1h0c-1 1-1 2-2 3v2h1l1 1v-1l1-1h0v2c0 1-1 2-2 3v1l-1-1-1 1h-1-2 0c-1 0-1 0-2-1h-1c0 1-1 1 0 2l1 1-1 1h-1-1v-3c0-1-1-1-1-1l-1 1v2s1 0 1 1h-2l-1 1v-3-7c1 1 1 2 2 3 2-2 2-4 3-6 2-3 5-5 9-7z" class="f"></path><path d="M377 517v-7c1 1 1 2 2 3h0s0 1-1 1v5l-1 1v-3z" class="Y"></path><path d="M391 501l1 2-1 1c1 0 2-1 3-1h1l-2 2-1 1v-1h0 0c-2 0-4 2-5 2-1-2 3-3 4-4v-2z" class="J"></path><path d="M383 515c0-1 0-2-1-2v-1h1 3v-2c0-1 1-2 2-2s0 1 0 2 1 2 2 2c0 1 0 1 1 1l1-1v-1l1-1h0v2c0 1-1 2-2 3v1l-1-1-1 1h-1-2 0c-1 0-1 0-2-1h-1z" class="K"></path><defs><linearGradient id="AY" x1="407.985" y1="607.927" x2="403.597" y2="589.483" xlink:href="#B"><stop offset="0" stop-color="#d6d5d6"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#AY)" d="M428 589l5-1v2l1 1-1 1c-1 1-1 1-1 3h0 3v1h0c-1 0-2 0-3 1 0 0 0 1 1 1h2l1-1c0 1 1 1 2 2h0c1 0 2 1 3 1l-1 1h0 0l-1-1h-5 0-8l-5 1h-9-2-4l1-1c-3-1-8 0-11 0h-10c-1 0-5 0-6-1h0-1c-1-1-1-1-2-1h-2c-1 0-3 0-4-1h1c4 0 8 0 12-1h1l14-1 8-2h4 1c2 0 4-1 6-2l4-1c2 0 4-1 6-1z"></path><path d="M428 589l5-1v2l1 1-1 1c-1 1-1 1-1 3-1-1-3-1-4-1h-2 1v-1h-2c2 0 5-1 6-1 0 0 1-1 1-2-2-1-2-1-4-1z" class="f"></path><path d="M428 594v-1h0 1c1-1 2-1 4-1-1 1-1 1-1 3-1-1-3-1-4-1z" class="D"></path><path d="M386 600l1-1h0c5 1 10 1 14 0 4 0 8 1 11 0h0c0 1 0 1-1 1l1 1h-2-4l1-1c-3-1-8 0-11 0h-10z" class="o"></path><path d="M396 600c4-1 8 0 11 0h4l1 1h-2-4l1-1c-3-1-8 0-11 0z" class="J"></path><path d="M373 499v-1h3v3h1v9 7 3l1-1h2c0-1-1-1-1-1v-2l1-1s1 0 1 1v3h1 1l1-1-1-1c-1-1 0-1 0-2h1l1 4c2-1 2-1 4 0-1 0-1 0-1 1v1c1 1 1 2 1 3l2 1c0 1 1 1 2 2l1-2 1-1h1 1l2-2c1-1 1 0 2-1 1 0 3-2 4-2h2c1-1 2-1 3-2h2c1 0 2-1 3-1 0 1-1 1 0 2l1 1v2c0 1-1 1-1 1l-1 1h3 2c-2 1-5 1-7 1l-2 1c-2 1-3 3-5 4l-1 1c-3 3-5 6-7 10l-5-1h-7-2 0c-1 1-3 0-4 0h-3c-1 1-4 1-5 0s-1-4-1-6h0v-7-6l1-1v1h0l1-20h1v-1z" class="b"></path><path d="M394 528c1-1 3-2 5-3-2 2-4 3-5 5-1 1-1 3-2 4 2-2 4-5 7-6h0 1-1c-2 2-4 4-5 6s-2 3-2 5h-7 1 2l-1-2-1-3h0c0-1 0-2-1-3v-1l1-2c0 1 1 1 1 1 1-1 0 0 1 0v4h2c1 0 1-1 2-1l2-4z" class="M"></path><path d="M386 528c0 1 1 1 1 1 0 1 0 3 1 5v3h-1l-1-3h0c0-1 0-2-1-3v-1l1-2z" class="C"></path><path d="M385 519c2-1 2-1 4 0-1 0-1 0-1 1v1c1 1 1 2 1 3l2 1c0 1 1 1 2 2l1-2 1-1h1l-3 4h1 0l-2 4c-1 0-1 1-2 1h-2v-4c-1 0 0-1-1 0 0 0-1 0-1-1l-1 2v1l-1-1c0-2 1-5 0-6h0v-2h1 1v-1l-1-2z" class="K"></path><path d="M390 528v-3h1c0 1 1 1 2 2l-1 1h-1-1z" class="D"></path><path d="M386 528c0-1 0-2-1-3v-1c0-1 0-1 1-1l1 1v3c1 1 1 1 1 2-1 0 0-1-1 0 0 0-1 0-1-1z" class="P"></path><path d="M395 524h1l-3 4h1 0l-2 4c-1 0-1 1-2 1h-2l2-1h0v-3-1h1 1l1-1 1-2 1-1z" class="C"></path><path d="M399 528l3-3c2-1 4-2 7-3 1 0 2-1 3-1s1 0 2-1h1 0v1c0 1-1 1-2 2l-1 1-2 1c-2 1-3 3-5 4l-1 1c-3 3-5 6-7 10l-5-1c0-2 1-3 2-5s3-4 5-6h1-1z" class="E"></path><path d="M394 534h0c2 0 3-2 5-4 1 0 4-3 5-3 0 2-4 3-5 6h1c1-1 2-3 4-3-3 3-5 6-7 10l-5-1c0-2 1-3 2-5z" class="C"></path><path d="M383 515h1l1 4 1 2v1h-1-1v2h0c1 1 0 4 0 6l1 1v-1 1c1 1 1 2 1 3h0l1 3 1 2h-2-1-2 0c-1 1-3 0-4 0h-3c-1 0-2 1-3 0l1-1s1-1 2-1v1h1v-1h2v-2h1v-5c1-1 1-1 1-2v-6c-1-1-1-2-1-3s-1-1-1-1v-2l1-1s1 0 1 1v3h1 1l1-1-1-1c-1-1 0-1 0-2z" class="P"></path><path d="M381 522l1-1 1 1c0 1-1 2-1 3s0 2-1 3v-6z" class="E"></path><path d="M385 530v1c1 1 1 2 1 3h0l1 3 1 2h-2-1-2 0c-1 1-3 0-4 0h-3c-1 0-2 1-3 0l1-1s1-1 2-1v1h1v-1h2v-2h1v1h1v-4h1v3h1v-1c0-2 0-2 1-3 0 1 0 1 1 1v-1-1z" class="B"></path><path d="M386 534l1 3 1 2h-2c-1 0-1-1-1-1 0-1 1-3 1-4z" class="H"></path><path d="M385 530v1 4c-1-1-1 0-1-1h-1c0-2 0-2 1-3 0 1 0 1 1 1v-1-1z" class="U"></path><path d="M373 499v-1h3v3h1v9 7 3l1-1h2c0 1 0 2 1 3v6c0 1 0 1-1 2v5h-1v2h-2v1h-1v-1c-1 0-2 1-2 1l-1 1c1 1 2 0 3 0-1 1-4 1-5 0s-1-4-1-6h0v-7-6l1-1v1h0l1-20h1v-1z" class="C"></path><path d="M377 533l1-2v2 2l-1-1v-1z" class="O"></path><path d="M377 524v-1l3 3v1h0-1v6h-1v-2l-2-2v-1h0v-4h1z" class="K"></path><path d="M376 524h1c0 1 0 2-1 4v-4z" class="f"></path><path d="M372 523v3c1-1 0-1 0-1 1-3 2-4 2-7v-1h1v3c1 1 1 3 1 4v4h0v1l2 2-1 2-1 1-2 1h-2c0-2-1-4 0-7v-1c-1-1-1-2 0-4z" class="D"></path><path d="M376 529c0 1 0 2-1 3h-1v-1c0-2 0-2 1-3h1v1z" class="U"></path><path d="M373 499v-1h3v3h1v9 7 3l1-1h2c0 1 0 2 1 3v6c0 1 0 1-1 2v-3-1l-3-3v1h-1c0-1 0-3-1-4v-3h-1v1c0 3-1 4-2 7 0 0 1 0 0 1v-3c0-1 0-2-1-3l1-20h1v-1z" class="o"></path><path d="M373 499v-1h3v3 7-2l-1-3v-2c0-1-1-1-2-2z" class="Y"></path><path d="M377 501v9 7c-2-2-1-6-1-9v-7h1z" class="P"></path><path d="M324 486c2 1 5 1 7 1h0c0 1-1 1-1 1l3 3h0v3 1c1 0 1 1 1 2h1c1-1 2-3 3-4h0v4 1c1 1 5 1 7 1h18 8v-2c0 1 0 1 1 1v1h1v1h-1l-1 20h0v-1l-1 1v6 7 2l-2 2-1 2h-2-8-7-1-16c-2 1-4 1-6 1h-2v-10-9-2c1-2 1-6 0-9v-6l-1-1v-7l-1-1 1-2v-1c-1-1-1-1-1-2h1v1h1v-1c0-1 0-2-1-3v-1z" class="Y"></path><path d="M341 513h2c0 1-1 2-1 3l-1 1c-1-2 0-1 0-2v-2z" class="o"></path><path d="M370 506h-2l-1-1v-3h3v4z" class="b"></path><path d="M342 505c1 1 3 2 4 3l1-1v1s1 1 2 1l-1 1h-2l-1-1h-1l-1 1-1-1h0v-4z" class="J"></path><path d="M352 502h5c1 1 2 0 3 0 0 0 0 1 1 1l1-1 1 1v2h-1 0c0-1 0-1-1-2v2l-1 1c0-1 0-2-1-3h0c-1 1-1 1-1 3h0l-1-3h-1c0 1-1 2-1 2v1c-2 0-2 0-3-1v-3z" class="C"></path><path d="M334 501c3 0 7-1 10-1-1 1-2 1-3 1h-1c0 1 0 1 1 1h5 1c2 0 4-1 5 0v3l-2 4h0v-2c0-1 0-2-1-2h-1s-1 1-1 2l-1 1c-1-1-3-2-4-3v4h-1c-1-1-1-2-1-3h-3v-1h-1 0-2v-3-1z" class="K"></path><path d="M336 505c-1-1 0-2 0-3h1v1 2h0-1 0z" class="T"></path><path d="M337 502h6c0 1 0 2-1 2v1 4h-1c-1-1-1-2-1-3h-3v-1h0v-2-1z" class="U"></path><path d="M337 503c1 0 1-1 2 0 1 0 1 0 1 1s0 1 1 2h-1-3v-1h0v-2z" class="E"></path><path d="M337 503c1 0 1-1 2 0 0 1-1 1-2 2v-2z" class="B"></path><path d="M336 505h1v1c0 1-1 3-1 5v8h-8v-1h-1 0c0-2-1-5 0-7h1v1c1 0 1-1 2-1 0-1 1-1 1-1v-1c2 0 3-1 4-2l1-1v-1z" class="X"></path><path d="M331 509c2 0 3-1 4-2v4 1l-1-2h-3v-1z" class="J"></path><path d="M328 518h-1 0c0-2-1-5 0-7h1v1 4c1-1 2-3 3-4l1 1c0 1 0 2-1 3h0c-1 0-1 1-2 1h-1v1z" class="M"></path><path d="M365 512h0c1-1 2-1 3-1h0v-1-1h0 2c-1 0 0 1 0 1 1 3 0 6 0 8v2 6c-1 0-1-1-1-1 0-2 0-5 1-6l-2-2v1c1 1 1 1 0 3h0c0 1 0 2-1 2s-2 0-3-1c-1 0-2-1-2-2-1-1-1-5 0-7 0-1 0-1 1-2l2 1z" class="J"></path><path d="M365 516h1c0-1 1-1 1-2h1v2 1h0c-1 1-2 1-2 1l-1-1v-1z" class="f"></path><path d="M362 513c0-1 0-1 1-2l2 1h0c1 2 0 2 0 4v1l1 1s1 0 2-1c0 2 0 3-1 5v1c-1-1-1-2-1-3h-1l-1 2c-1 0-2-1-2-2-1-1-1-5 0-7z" class="D"></path><path d="M362 513c0-1 0-1 1-2l2 1h0v1c-1 0-1 0-1 1h-1v-1h-1z" class="E"></path><path d="M329 527c2-3 4-5 7-6 1-1 2-1 3-2 5-1 9-1 13 0-1 1-1 1-2 1h-5l-2 1-4 1c-3 2-5 4-7 7l-3 6v3 1c1 1 2 0 4 0-2 1-4 1-6 1h-2v-10-9-2h1c0 2 1 3 2 4 0 1 0 2-1 2 0 1 0 2 1 3 0-1 1-1 1-1z" class="D"></path><path d="M327 540c0-2-1-6 0-8 0 1 1 2 2 3h0v3 1c1 1 2 0 4 0-2 1-4 1-6 1z" class="B"></path><path d="M325 519h1c0 2 1 3 2 4 0 1 0 2-1 2 0 1 0 2 1 3 0-1 1-1 1-1l-2 5h0-1v-3-2c-1-2-1-4-1-6v-2z" class="E"></path><path d="M324 486c2 1 5 1 7 1h0c0 1-1 1-1 1l3 3h0v3 1c1 0 1 1 1 2h1c1-1 2-3 3-4h0v4 1c1 1 5 1 7 1h18 8v-2c0 1 0 1 1 1v1h1v1h-1l-1 20h0v-1l-1 1v-2c0-2 1-5 0-8 0 0-1-1 0-1v-3-4-1c-3-1-6-1-9-1h-17c-3 0-7 1-10 1v1 3h2 0v1l-1 1c-1 1-2 2-4 2v1s-1 0-1 1c-1 0-1 1-2 1v-1h-1c-1 2 0 5 0 7h0 1v1h-2-1c1-2 1-6 0-9v-6l-1-1v-7l-1-1 1-2v-1c-1-1-1-1-1-2h1v1h1v-1c0-1 0-2-1-3v-1z" class="O"></path><path d="M333 494v1c1 0 1 1 1 2h1c1-1 2-3 3-4h0v4 1c1 1 5 1 7 1h0c-2 1-6-1-9 0 0 1-1 0-1 0-1 0-1-1-1-2l-1-3z" class="C"></path><path d="M328 511l-2-2 1-1v-1h-1v-5h4l4-1v1 3h2 0v1l-1 1c-1 1-2 2-4 2v1s-1 0-1 1c-1 0-1 1-2 1v-1z" class="Y"></path><path d="M330 502l4-1v1 3h2 0v1c-2 0-2 0-2-2h-3v-1h-3-1v1-1c1-1 2-1 3-1z" class="o"></path><path d="M328 511l-2-2 1-1v-1h-1v-5h4c-1 0-2 0-3 1v1l2 2h-2v1 2h4v1s-1 0-1 1c-1 0-1 1-2 1v-1z" class="K"></path><path d="M324 486c2 1 5 1 7 1h0c0 1-1 1-1 1l3 3h0v3l1 3-1 1v2h2v1h-5c-1 0-4 0-5 1v2l-1-1v-7l-1-1 1-2v-1c-1-1-1-1-1-2h1v1h1v-1c0-1 0-2-1-3v-1z" class="B"></path><path d="M330 488l3 3h-2 0c-1 0-1 1-1 1l-1 1h0v-2-1l1-2z" class="M"></path><path d="M324 486c2 1 5 1 7 1h0c0 1-1 1-1 1-1 0-2 0-3 1h0-2v2-1c0-1 0-2-1-3v-1z" class="F"></path><path d="M324 493l1-1h1v2c0 1 0 2 1 3v1h-2v2c3 0 4-1 7 0v-2h1v2h2v1h-5c-1 0-4 0-5 1v2l-1-1v-7l-1-1 1-2z" class="Q"></path><path d="M352 519c3 1 5 3 8 4 2 1 3 4 5 5h0v2c1 1 1 1 1 2l2 5-1 2h-2-8-7-1-16c-2 0-3 1-4 0v-1-3l3-6c2-3 4-5 7-7l4-1 2-1h5c1 0 1 0 2-1z" class="Z"></path><path d="M347 535v-3h1l1 1h1v1c-1 1-2 1-3 1z" class="m"></path><path d="M353 523l1 1v1l-1 1h0c-1 0-1-1-2-1s-1 0-1-1h2l1-1z" class="a"></path><path d="M339 531v1h1v-2l1-2h1v1c-1 1-1 2-1 3-1 1-1 1-2 0h0v-1z" class="h"></path><path d="M339 522c0 1-1 3-2 4s-3 2-4 5h0l-1-2c2-3 4-5 7-7z" class="a"></path><path d="M342 532v-1c1 0 1-1 0-2 1 0 1-1 2-1h0 1l-1 1v2h2v3l-3 1c-1-1-1-2-1-3z" class="h"></path><path d="M336 534v-4-1h1v4h1v-1-3h1v2 1h0c1 1 1 1 2 0h0 1c0 1 0 2 1 3l3-1v1 4h3-16c-2 0-3 1-4 0v-1-3l3-6 1 2h0 1v1c1 0 1 1 1 1l1 1z" class="g"></path><path d="M343 535l3-1v1h-1v3l-1 1v-3h-1 0v-1z" class="p"></path><path d="M342 532c0 1 0 2 1 3v1s0 1-1 1l-1-1v-4h1z" class="a"></path><path d="M333 531h0 1v1c1 0 1 1 1 1l1 1v2h-1v-3l-1 2v2c-2 0-3 1-4 1v-2h2c0-1 1-2 1-3v-2z" class="h"></path><path d="M352 519c3 1 5 3 8 4 2 1 3 4 5 5h0v2c1 1 1 1 1 2l2 5-1 2h-2-8-7-1-3v-4h1 0c1 0 2 0 3-1v-1h-1l2-1v-2h1l1-1c-2 0-2-1-2-2h-1l-2 1c0-1-1-1-1-2h1c1 0 4 0 5 1l1 1v-1l-1-1 1-1v-1l-1-1-3-1c-1 0-1 1-1 1h-4l-1-2h-1l2-1h5c1 0 1 0 2-1z" class="p"></path><path d="M353 529h1v1 1h-1l-1-1 1-1z" class="k"></path><path d="M354 524l2 1 1 1-1 1-2-2v-1zm0 14h-1v-1c0-2-1-3 0-4h1v2 2 1zm5-6v-1c-1-1-1-1-1-2h1l1 1h0c0 1 0 2 1 3v1c-1 1 0 1-1 1 0-1-1-2-1-3z" class="Z"></path><path d="M356 534v-1l1 1v4-1h-3v-2l2-1z" class="I"></path><path d="M354 535l2-1 1 3h-3v-2z" class="a"></path><path d="M362 531l1 1c0 1 0 2 1 3 0 0 0-1 1-1v5h-8 3 3c0-2-1-6-1-8z" class="L"></path><path d="M365 534v-2h1l2 5-1 2h-2v-5z" class="P"></path><path d="M359 532c0 1 1 2 1 3v4h-3-7 4v-1-1h3v1h1 1v-6h0 0z" class="G"></path><path d="M352 519c3 1 5 3 8 4 2 1 3 4 5 5h0v2c1 1 1 1 1 2h-1v2c-1 0-1 1-1 1-1-1-1-2-1-3l-1-1c-1-2-2-4-4-6h-2l-2-1-1-1-3-1c-1 0-1 1-1 1h-4l-1-2h-1l2-1h5c1 0 1 0 2-1z" class="U"></path><path d="M360 523c2 1 3 4 5 5h0v2h-1c-1 0-1 0-1-1-2-2-3-4-3-6z" class="D"></path><path d="M345 520c5 0 9 2 13 5h-2l-2-1-1-1-3-1c-1 0-1 1-1 1h-4l-1-2h-1l2-1z" class="m"></path><path fill="#fff" d="M384 271h0c-2 4-4 4-7 6-1 1-3 2-4 3s-2 3-3 4c-3 4-2 8-3 12-1 1-2 3-3 4-2 2-3 4-4 7-2 1-7-1-8 0 2 0 5 0 7 1 1 0 1 1 1 2 0 2 2 3 1 5h1c1 0 3 4 5 4l1 1h1c1 0 1 1 2 1s1 1 2 0c3 0 5 1 8 3 2 2 4 4 6 7 1-1 1-1 1-2h0c1 2 1 5 1 7s1 3 1 5c-7 2-15 4-22 5-6 1-12 3-19 4-13 1-27 2-40 1-6 0-12 0-18-1v-13-20-63l-1-77c0-20-1-41 2-62h20c11-1 21-1 32 0 15 0 29 3 43 7 17 5 33 12 46 24 6 5 11 12 14 19 3 4 6 8 8 13 3 7 4 15 6 22 2 11 4 21 4 32 2 23 1 46-14 65-4-7-7-15-11-22-2-2-4-5-6-7-2-3-5-4-7-6h-1-1l-2-1h0l-9-3h-4-2l-7-1h0l-6-3h0c-5-4-9-8-14-9-1 0-1 0-2 1 0 2 1 4 1 5l2 8c1 4 2 8 2 12z"></path><path d="M404 195h1c1 0 1 0 2 1h1l1 1h0 2 1c1 1 1 1 1 2l1 1v1c-2 1-2 0-4-1h-1 0c-1-1-2-1-2-1l-1-1c-1-1-1-2-2-3z" class="w"></path><path d="M413 242c4 2 7 8 9 12 1 3 2 6 5 8h-1-1l-2-1h0v-1c-1-2-2-3-4-4-1-2-2-4-3-5-2-3-2-6-3-9z" class="W"></path><path d="M408 258c1-3 1-5 2-7 0-3 0-7 2-9h1c1 3 1 6 3 9 1 1 2 3 3 5 2 1 3 2 4 4v1l-9-3h-4-2z" class="d"></path></svg>