<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="45 45 488 632"><!--oldViewBox="0 0 588 752"--><style>.B{fill:#454445}.C{fill:#313031}.D{fill:#040404}.E{fill:#535253}.F{fill:#141414}.G{fill:#393839}.H{fill:#1b1b1c}.I{fill:#242424}.J{fill:#bdbdbd}.K{fill:#f5f4f5}.L{fill:#c6c5c6}.M{fill:#3e3d3e}.N{fill:#2b2a2b}.O{fill:#e6e4e6}.P{fill:#0f0e0e}.Q{fill:#e4e3e2}.R{fill:#7c7c7c}.S{fill:#8c8b8c}.T{fill:#a3a3a3}.U{fill:#929191}.V{fill:#6d6d6d}.W{fill:#fff}</style><path d="M474 531c1-1 1-2 2-3 3-3 8-4 12-4 3 1 5 1 8 3h-3c-5-2-8-1-12 1-2 1-4 3-6 5h0c0-1 0-1 1-2h-1l-1 1v-1z" class="D"></path><path d="M484 608l-4-8c3 2 6 6 8 10 1 2 0 4 0 6 0 1 0 1-1 1v-1c0 1 0 1-1 2-1-2-1-4-3-5 1-2 1-3 1-5z" class="P"></path><path d="M484 608v1s0 1 1 1c1 1 2 5 2 6s0 1-1 2c-1-2-1-4-3-5 1-2 1-3 1-5z" class="G"></path><path d="M510 612h1s10-9 13-10c5-1 10-1 15-1-1 0-3 1-4 1-3 2-8 3-12 4h-4c-3 2-6 8-10 8 0-1 0-1 1-2z" class="E"></path><path d="M478 523h3c1-1 2 0 3 0h6 2c-2 0-6 0-7 1h3c-4 0-9 1-12 4-1 1-1 2-2 3v-1c-1 0-2 1-3 2h0l-1 2v-2-1h-2v-1h0c1 0 1 0 1-1 3-3 5-5 9-6z" class="J"></path><path d="M478 523h2c-1 1-1 1-2 1l-1 1h0c-2 1-6 4-7 6v1-1h-2v-1h0c1 0 1 0 1-1 3-3 5-5 9-6z" class="O"></path><path d="M501 640c6-1 12 0 18-1 5 0 9-2 13-4-2 2-4 4-6 5-1 1-5 2-6 3h-1v-1c-2 0-3 1-5 2v1c-2-2-7-1-9 0l-3 1 2-2v-4h-3z" class="H"></path><path d="M342 598l6-5c0 1-1 1-1 2h0 2 0c-1 2-3 4-4 6v-1h-1l-2 1-1 1s0 1-1 1-1 1-2 2c-2 1-4 1-6 2l-1 1c-1 0-3 1-4 2-1 0-1 1-2 1h-1s-2 2-2 3h0c0 1-1 1-1 1 0 1 0 2-1 3v1c0 1 0 1-1 2 0 1 0 4 1 5v1 2l1 1c0 2 1 3 2 4l1 1c1 1 2 2 4 2h1c1 1 2 0 3 1h2c1 0 2-1 3 0l-1 1h-5 0-2v-1h-1c-1 0-1 0-2-1-3-1-5-4-6-7-1-1-1-1-1-2-1 0 0-1-1-2v-5l1-1v-2c0-1 1-1 1-2v-1l1-1c1-1 2-3 4-4 1-1 3-2 4-3 3-1 6-1 7-3h1 0 0c0-3 2-3 3-5 1-1 1-1 2-1zm170-57c1-1 1-1 1-2l1-3c1-2 2-3 2-5v-1c1 0 0-1 1-1v-2l1-1v-1h0c0-2 1-4 0-6v-2c0-1-1-3-2-4-1 0-1-1-2-1l1-1v1h1c1 0 2 2 2 3l1 1h0c0 1 0 1 1 2 0 1-1 4-1 5v2 1 1c-1 0-1 2-1 3h1c1-2 2-3 2-5 1-1 2-3 3-4v-1h0v-1c1-1 1-2 1-3-1-2-2-4-2-6v-1c1 1 1 2 1 2 1 1 1 2 1 2v1c0 1 1 3 1 4l-1 2c0 2-1 4-2 5s-2 3-2 4c-1 1 0 2 0 2h1c-2 3-3 6-5 8s-2 5-3 7v2l-1-1c-1-2-1-4-1-6z" class="K"></path><path d="M433 651c4 1 7 1 10 2h2c1 1 1 1 2 1l2 1c5 1 10 3 14 6 3 1 5 4 7 6h-2 0l3 5v1l-1-2c-1-1-2-1-2-2l-2-2-1-1-2-2c-2-1-5-2-8-3 0-1-1-1-2-1v-1c-1 0-2 0-3-1h-2c-1-1-2-2-3-2h-2c-1-1-1-1-2-1h-1-1c-2-1-4-2-6-4z" class="Q"></path><path d="M468 667c-1 0-2-2-3-3-3-1-5-3-8-5h-2c-2-1-3-1-4-2h-2c-1-1-2-1-2-2h0 2c5 1 10 3 14 6 3 1 5 4 7 6h-2 0z" class="K"></path><path d="M378 605c1 2 1 3 2 5v1c1 2 1 3 2 5-1-1-1-2-2-2v1h0c-1 6 0 14-1 20v-8-14c-1 2-1 3-1 5-1 3-1 6-1 9-1 1 0 2 0 3-1 3-1 6-2 9-1 4-3 7-4 10l-1 3h0c-1 1-1 2-1 3v2h-1v3c1 1 0 2 1 3 0 1 0 2 1 2l1 4 1 1c0 1 3 3 4 3l2 1h1l-1 1c-1 0-1 0-2-1-1 0-2-1-2-1-2-1-3-2-4-4-1-1-2-2-2-4l-1-1v-1-1c-1-1 0-5 0-6 1-2 1-4 2-6 1-3 2-5 3-7l2-5v-1-2c1-1 1-2 1-4h0v-2c1-2 1-4 1-6v-3c1-1 0-3 0-4h1s-1-2 0-2v-1c0-3 1-5 1-8z" class="L"></path><path d="M478 539c-1 1-2 3-3 4 1-4 1-7 3-10 1-2 4-4 6-5s5-1 8-1l2 1s1 0 1 1c2 1 3 1 5 2l-1 1c-1 0-4-1-5-1l1 1c3 0 3 0 5 2h-4c-2-1-4-1-6-1-1 0-4 1-5 2-2 1-6 2-7 4h0z" class="D"></path><path d="M490 533h4v-1c-1-1-2-1-4-1s-6 1-7 2-1 1-2 1h0c2-3 5-4 8-4 1 0 3 0 5 1l1 1c3 0 3 0 5 2h-4c-2-1-4-1-6-1z" class="F"></path><path d="M509 614c4 0 7-6 10-8h4c0 1 0 2-1 2l-4 3c5-1 10-1 15-5-1 4-2 5-5 8-1 1-3 1-4 1-4 0-7 1-11 2-1 1-4 2-5 2h-1s-1 0-1-1c1-1 3-3 3-4z" class="D"></path><path d="M509 614c4 0 7-6 10-8h4c0 1 0 2-1 2-2 0-11 7-12 9l-2 2h-1s-1 0-1-1c1-1 3-3 3-4z" class="O"></path><path d="M479 638c0 1-1 1-1 2-3 3-6 6-10 8-2 0-4 1-5 2 2 1 7-2 9-3 2 0 3-1 5-2 4-2 9-5 13-7 4-1 9-3 14-3 4-1 9-1 14-3 2-1 5-2 7-4l6-6c1-1 1-3 3-4-1 1-1 2-2 3-2 4-4 7-8 9-8 7-20 5-30 8l-12 6-11 5c-1 0-1 1-2 1-2 1-4 1-6 1-5 0-9 2-13 1 0 0 1 0 1-1h2 0c2 0 3-1 4-1 5-2 10-4 14-7 4-2 6-8 7-12 1 1 1 2 1 4h0c0 1-1 2-1 2l1 1h0 1-1z" class="I"></path><path d="M376 598c0-1 0-2 1-3h0l1 1v1l4 6c1 2 3 3 4 5l-1 1c1 2 2 4 3 7 0 1 2 3 3 4h0v2l-3-3c-1 0-1 1-2 1 1 1 2 1 2 2 1 2 6 5 6 5 0 2 1 2 1 3l-1-1c-1-1-2-1-3-1v1c1 2 2 4 4 6s4 4 7 6v2 1c2 2 2 4 3 7 2 3 4 6 5 9 4 7 4 15 12 19h-1c0 1 0 1 0 0-6-1-11-17-13-22-2-4-5-8-6-12-1-2-2-3-3-4-1-3-4-4-5-6-5-6-9-12-12-19-1-2-1-3-2-5v-1c-1-2-1-3-2-5l-2-7z" class="R"></path><path d="M383 615v-1l3 3 2 2c-1 0-1 1-2 1h0c-2-1-2-3-3-5z" class="Q"></path><path d="M386 620h0c1 1 2 1 2 2 1 2 6 5 6 5 0 2 1 2 1 3l-1-1c-1-1-2-1-3-1v1l-1-2c-1-1-4-4-4-5v-2z" class="O"></path><path d="M378 597l4 6c1 2 3 3 4 5l-1 1c1 2 2 4 3 7 0 1 2 3 3 4h0v2l-3-3-2-2-3-3v1h0c-2-6-4-11-5-18z" class="J"></path><path d="M382 603c1 2 3 3 4 5l-1 1c1 2 2 4 3 7 0 1 2 3 3 4h0v2l-3-3-2-2c0-1 0-1 1-2-1 0-1-1-2-2 0-2-2-5-2-7-1-1-2-2-1-3z" class="V"></path><path d="M502 598c-2-3-4-5-7-7-4-4-8-7-14-9v-1c4 1 8 1 12 3-3-3-5-5-9-6-3-1-6-1-10-2 7 0 12 0 18 2v-1c-2-1-4-2-5-3-5-3-8-4-13-5 3 0 6 0 9 1l6 3 2-1c3 2 5 4 7 7 6 8 9 16 9 26 0 0 0-1-1-2h0c0 2 0 7 1 8l-1 1v-1c-1-2-1-4-2-5 0-3-1-6-2-8h0z" class="D"></path><path d="M491 572c3 2 5 4 7 7 6 8 9 16 9 26 0 0 0-1-1-2v-6c-2-10-8-18-17-24l2-1z" class="Q"></path><path d="M529 614l2-1v1c-1 2-3 4-5 5-1 1-2 2-3 2 1 1 3 0 4 0l2-1c-4 4-9 7-15 8l-12 3c-4 1-6 4-10 6h0-2c4-4 7-8 10-13h1c1-1 2-2 2-3l3-3c0 1 1 1 1 1h1c1 0 4-1 5-2 4-1 7-2 11-2 1 0 3 0 4-1h1z" class="F"></path><path d="M503 621l3-3c0 1 1 1 1 1-2 3-4 6-7 9l-6 6c0 1-1 2-2 3h0-2c4-4 7-8 10-13h1c1-1 2-2 2-3z" class="L"></path><path d="M529 614l2-1v1c-1 2-3 4-5 5-1 1-2 2-3 2l-1 1h-1 0s-1 1-1 2c-1 0-2 0-3 1h-2c-2 0-3 0-4-1v-1c1-1 2-1 3-1h0c1-1 1-1 2-1h-1c1 0 1 0 2-1h-2 0l1-1h0l8-4c1 0 3 0 4-1h1z" class="I"></path><path d="M517 622c1 0 3-1 4 0 0 0-1 1-1 2-1 0-2 0-3 1-2 0-4 0-5-1l1-1c1 0 2-1 4-1z" class="C"></path><path d="M529 614l2-1v1c-1 2-3 4-5 5-1 1-2 2-3 2l-1 1h-1 0c-1-1-3 0-4 0v-1h1c4-2 7-5 11-7zm-17-9c3-7 7-15 13-19 4-3 9-6 14-7-1 1-2 3-3 4-2 1-4 2-4 4l5-2 1 1c-1 4-5 6-8 8l7-3c-3 4-6 7-10 9-3 1-5 2-7 3-4 2-8 6-10 9-1 1-1 1-1 2s-2 3-3 4l-3 3 3-6c2-4 4-7 6-10z" class="D"></path><path d="M526 590h1c-2 3-4 5-6 7h-1l1-1c0-3 3-5 5-6z" class="B"></path><path d="M450 652c4 1 8-1 13-1l-3 2h1 5c6 0 11 1 17 4 3 1 6 3 9 5 2 0 4 1 5 1 2 0 3 0 4 1-4 1-8 1-13 1 1 1 3 2 4 3-3-1-5-2-8-4 1 2 2 4 4 5h-1c-4 0-7-2-10-3l6 6c-1-1-3-1-4-2s-2-2-3-2l3 3v1c-3-1-6-2-9-5-2-2-4-5-7-6-4-3-9-5-14-6l-2-1c-1 0-1 0-2-1 2 0 3 0 5-1z" class="D"></path><defs><linearGradient id="A" x1="472.858" y1="656.849" x2="474.846" y2="662.9" xlink:href="#B"><stop offset="0" stop-color="#2b2b2a"></stop><stop offset="1" stop-color="#454447"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M469 657c1 0 4 0 5 1s5 4 5 5h-2l-1 1c-2-1-6-6-7-7z"></path><path d="M450 652c4 1 8-1 13-1l-3 2h1 5c-4 0-9 1-13 1-2 0-5-1-6 0-1 0-1 0-2-1 2 0 3 0 5-1z" class="O"></path><path d="M520 643c5 0 9-2 13-5-2 5-4 8-8 12-11 9-25 7-39 4-5-2-11-4-16-4 3-1 8-1 11-3 7-2 12-7 20-7h3v4l-2 2 3-1c2-1 7-2 9 0v-1c2-1 3-2 5-2v1h1z" class="D"></path><path d="M502 646l3-1c2-1 7-2 9 0-1 0-3 0-3 1h7c-5 2-11 2-17 1v-1h1z" class="M"></path><path d="M379 635c1-6 0-14 1-20h0c1 8 4 15 10 20 3 3 6 5 8 8 1 1 2 3 3 5 3 7 5 14 8 20 0 2 2 5 2 7-2-2-4-3-5-4-2-3-4-5-6-8v1c1 2 2 5 4 7 1 2 2 4 4 6-4-2-8-3-11-6-2-2-4-5-6-6v2h-1c-4-6-9-13-10-21-1-4-1-7-1-11z" class="D"></path><path d="M399 653c-1-1-1-2-1-3l1-1 3 9c-1-1-2-3-3-3v-1-1z" class="B"></path><path d="M386 650l9 16c-3-3-7-6-8-9s-1-5-1-7z" class="M"></path><path d="M394 652h1c1 1 2 1 4 1v1 1h-1c0 2-1 4 0 5s1 1 1 2l1 1v1c-1-1-1-1-1-2-2-2-4-4-6-7h0 0c1-1 1-2 1-3z" class="F"></path><path d="M394 652h1c1 1 2 1 4 1v1c-1 0-2 1-3 1h-2-1 0 0c1-1 1-2 1-3z" class="I"></path><path d="M389 650c-1-1-2-5-2-6l3 3c1 0 2 0 2 1h0c3 0 3 0 5-1 1 0 1 1 2 2l-1 1c0 1 0 2 1 3-2 0-3 0-4-1h-1c0 1 0 2-1 3h0c-2-2-3-3-4-5z" class="C"></path><path d="M389 650l1-1 1 1 1-1c1 1 2 2 2 3s0 2-1 3h0c-2-2-3-3-4-5z" class="B"></path><path d="M402 641h3c0 1 1 1 1 1h0c3 1 5 3 8 3 4 1 9 0 12-1h1c1 1 5 1 6 1 0 1 1 1 2 1-3 1-5 1-8 1h-5l-1 1c-1 0-2 0-2 1h0 0-1l1 2c6 2 13 4 19 7 5 3 9 6 14 9 4 2 9 3 13 6 1 0 1 1 2 2-3-1-6-2-8-3-3-1-4-2-6-3 2 2 4 4 6 7l-7-3 2 5-8-7 4 9c-2 0-4-1-6-1-3-1-6-3-8-5s-5-3-7-5l3 7c-3-3-5-5-9-7-3-2-6-3-9-6-2-2-5-7-6-11 0 0-1 0-1-1h-1-1c-1-3-1-5-3-7v-1-2z" class="D"></path><path d="M435 663l1-1c2 0 5 3 6 5-1 0-1 0-1 1l1 2c-1 0-1 0-1-1h0c-2-3-4-5-6-6z" class="F"></path><path d="M437 667c0-1-5-3-5-5h1l2 1c2 1 4 3 6 6h0l-4-2z" class="B"></path><path d="M437 667s-1 0 0 1c0 0 2 3 3 3l-6-3c-4-3-7-7-10-10 1 1 2 1 2 1 1 1 2 1 2 1 1 0 2 1 3 1s1 0 2 1h-1c0 2 5 4 5 5z" class="F"></path><path d="M403 643c5 3 8 7 12 11 2 2 5 4 6 6-2-2-5-3-7-4l3 4c-4-2-6-5-9-8 0 0-1 0-1-1h-1-1c-1-3-1-5-3-7l1-1z" class="K"></path><path d="M402 641h3c0 1 1 1 1 1h0c3 1 5 3 8 3 4 1 9 0 12-1h1c1 1 5 1 6 1 0 1 1 1 2 1-3 1-5 1-8 1h-5l-1 1c-1 0-2 0-2 1h0 0-1l1 2c-2-1-4-1-5-2-2-1-5-2-7-4-1 0-2-2-4-2l-1 1v-1-2z" class="J"></path><path d="M406 642c3 1 5 3 8 3 4 1 9 0 12-1h1c1 1 5 1 6 1 0 1 1 1 2 1-3 1-5 1-8 1h-5c-3 0-6 0-8-1s-7-2-8-4z" class="H"></path><path d="M476 522c-4 1-8 2-9 6-2 2-1 9 0 11 1 3 4 6 6 7-5-2-12-4-15-10-1-3-1-8 0-12 1-3 4-5 7-7 6-2 12-2 18-1 4 0 6 3 10 5l-5-5c-3-3-7-6-7-11v-1 1h1c1 5 4 8 8 12l3 3 4 4v-1c-1-1-8-10-8-12s0-3-1-5h1c1 3 4 6 6 8-1-3-2-6-2-10 0 1 1 3 2 5 1 3 3 6 5 8l-4-9c7 5 14 10 16 19 0 4-1 9 0 14 0 2 0 4 1 6-1 1 0 1-1 1h-1l-1-3h-1c0-2 0-2-1-3v-1c-1-1-2-3-2-4l-1 1c0 1 1 1 1 3-1-2-2-2-2-4-1-1-3-2-4-3-2-2-2-2-5-2l-1-1c1 0 4 1 5 1l1-1c-2-1-3-1-5-2 0-1-1-1-1-1l-2-1h1 3c-3-2-5-2-8-3h-3c1-1 5-1 7-1h-2-6c-1 0-2-1-3 0h-3c-4 1-6 3-9 6 0 1 0 1-1 1h0c1-4 5-6 8-8h1-1z" class="D"></path><path d="M465 524l1-1h1l-4 8h0c0-2 0-4 2-7z" class="E"></path><path d="M463 531h-1-2c-1 0-1-1-2-1h0c0-1 0-1 1-2 0-1 0-2 1-2 2-2 3-2 5-2-2 3-2 5-2 7z" class="H"></path><path d="M503 522h0c1 0 4 1 5 2 2 2 2 5 2 8h-1c-2-1-5-3-5-5-1-2-1-4-1-5z" class="M"></path><path d="M476 522c2-1 3-1 5-1 1 0 4 0 5-1h1s1 1 2 0l-2-1-1 1h-2c1-1 2-1 2-2l3 1 5 5-2-1h-2-6c-1 0-2-1-3 0h-3c-4 1-6 3-9 6 0 1 0 1-1 1h0c1-4 5-6 8-8h1-1z" class="S"></path><path d="M492 523l2 1c8 4 15 11 17 20l1 4h-1l-1-3h-1c0-2 0-2-1-3v-1c-1-1-2-3-2-4l-1 1c0 1 1 1 1 3-1-2-2-2-2-4-1-1-3-2-4-3-2-2-2-2-5-2l-1-1c1 0 4 1 5 1l1-1c-2-1-3-1-5-2 0-1-1-1-1-1l-2-1h1 3c-3-2-5-2-8-3h-3c1-1 5-1 7-1z" class="Q"></path><path d="M493 527h3c1 1 2 2 3 2 1 1 2 2 3 2 3 4 7 8 8 13v1h-1c0-2 0-2-1-3v-1c-1-1-2-3-2-4l-1 1c0 1 1 1 1 3-1-2-2-2-2-4-1-1-3-2-4-3-2-2-2-2-5-2l-1-1c1 0 4 1 5 1l1-1c-2-1-3-1-5-2 0-1-1-1-1-1l-2-1h1z" class="P"></path><path d="M489 610l-6-12c-5-3-9-1-14 0 3-2 6-4 10-4 1 0 4 1 5 1-2-3-7-4-11-5h9s-1 0-2-1c-4-1-9-1-13 0l-6 3c1-2 3-3 4-4 4-3 11-4 16-3 8 2 15 8 20 15 3 7 3 14 0 21l-1 3c-3 5-6 9-10 13h0l-6 3c1-1 3-2 4-4-3 1-5 4-8 6-2 2-5 3-7 4 2-2 5-4 6-8h1c1-3 2-5 3-8v-4c0-5-1-12-4-16-1-3-3-4-5-6 5 2 7 4 9 9 2 1 2 3 3 5 1-1 1-1 1-2v1c1 0 1 0 1-1 0-2 1-4 0-6h1z" class="D"></path><path d="M493 611l-4-10c2 2 4 3 5 6 1 1 1 1 1 3-1 0-1 1-2 1z" class="B"></path><path d="M487 616v1c1 0 1 0 1-1 0 3 0 6 2 9h1c0 1 0 2-1 3 0-1 0-1-1-1-1 1-1 2-2 2v-3c0-3-1-5-1-8h0c1-1 1-1 1-2z" class="C"></path><path d="M489 610c2 4 3 11 2 15h-1c-2-3-2-6-2-9 0-2 1-4 0-6h1z" class="B"></path><path d="M495 605h0c-1-1-1-2-2-3 0-4-3-6-5-8 5 2 11 5 12 11 1 3 0 8-1 11-1-1-1-3-2-4-1 0-2 0-2 1v2h-1c0-1-1-3-1-4 1 0 1-1 2-1 0-2 0-2-1-3l1-2z" class="I"></path><path d="M495 605c0-1-1-2 0-3v-1c1 1 2 4 3 5 0 1 0 3-1 4h0c-1-1-1-3-2-5z" class="B"></path><path d="M371 568v-1c1-3 1-8 1-11v-6c2 6 1 12 1 18 1 2 0 5 1 7v3 3c1 1 1 3 2 4 1 3 1 7 1 10h0c-1 1-1 2-1 3l2 7c0 3-1 5-1 8v1c-1 0 0 2 0 2h-1c0 1 1 3 0 4v3c0 2 0 4-1 6v2h0v-3c-1 1-1 1-1 2-1 5-3 10-6 15 0-3 1-7 1-10-3 8-6 17-5 26-1-2-3-6-4-7h0c-2-5-1-10-1-15-1 3-2 7-2 10-4-15-3-29 4-43 4-5 7-11 9-18v-6l1-14z" class="D"></path><path d="M375 613l1 1v2c0 1 1 3 0 4v3c0 2 0 4-1 6v2h0v-3c-1 1-1 1-1 2l1-17z" class="K"></path><path d="M376 598l2 7c0 3-1 5-1 8v1c-1 0 0 2 0 2h-1v-2l-1-1c0-1 1-4 1-6v-9h0z" class="J"></path><path d="M374 578v3c1 1 1 3 2 4 1 3 1 7 1 10h0c-1 1-1 2-1 3h0c-1-6-2-13-2-20z" class="U"></path><path d="M366 609c1-3 3-6 5-9v-1c0 2 0 4-1 6 0 2-1 3-1 5 0 1 1 5 0 6s-1 3-1 4c0-2 1-6 0-7 0-1-1-1-1-1 0-1 0-2-1-3z" class="F"></path><path d="M362 613h0c0 3 1 6 0 8h-1c0 1 0 2-1 3h-1 0c-1-1-1-2-1-3 0-3 2-6 4-8z" class="B"></path><path d="M366 609c1 1 1 2 1 3 0 0 1 0 1 1 1 1 0 5 0 7l-4 9c0-3 1-8 0-10h-1c1-1 1-3 1-4 1-2 2-4 2-6z" class="N"></path><path d="M522 531c3-3 6-5 10-4 3 0 5 1 6 3 2 2 3 5 2 7 0 6-5 8-8 12-2 3-3 5-4 8l5-6-22 52 1 2c-2 3-4 6-6 10l-3 6c0 1-1 2-2 3h-1l1-3c3-7 3-14 0-21 0-1 0-1 1-2h0c1 2 2 5 2 8 1 1 1 3 2 5v1l1-1c-1-1-1-6-1-8h0c1 1 1 2 1 2 0-10-3-18-9-26-2-3-4-5-7-7l-1-1c1 0 2 1 3 1l9 3-4-4c2 1 3 2 5 3l1-2c-1-1-2-2-4-3h1l2 1 1-1c-1-1-1-3-1-4-2-1-4-2-6-2s-3 1-5 0h0c2-2 4-2 7-2h0c0-1-1-1-2-1-3-1-7 0-10 3l-3 3c2-5 5-8 9-10 3-1 9-2 12-1v1c3 1 4 3 6 5l-1-3c-3-3-8-5-12-7-2 0-4-1-6-2h-2c3-1 6-1 9-1-6-2-12-2-18 0 4-3 7-4 12-5h-5c-5-1-8 2-12 6 1-2 2-4 4-5v-1-1h-1v-2l-1-1h0c1-2 5-3 7-4 1-1 4-2 5-2 2 0 4 0 6 1h4c1 1 3 2 4 3 0 2 1 2 2 4 0-2-1-2-1-3l1-1c0 1 1 3 2 4v1c1 1 1 1 1 3h1l1 3h1c1 0 0 0 1-1l1 1v-2c1-2 1-5 3-7s3-5 5-8z" class="D"></path><path d="M503 565c3 1 5 2 7 5h0c-2 0-2-1-4-1h0c2 1 3 2 4 4h0-1l-6-3h0l1-1c-1-1-1-3-1-4z" class="Q"></path><path d="M517 558c0-2 0-6 2-8 1 2-1 6-1 8v24h0c-1-3 0-6-1-8v-5c0-3-1-6-2-9h0 1 0 1v-2z" class="O"></path><path d="M520 535c0 1 1 1 1 2-4 5-6 13-5 20 0 1 0 1 1 1v2h-1 0-1 0c-1-4-2-9-4-12h1c1 0 0 0 1-1l1 1v1-1c1-1 1-3 2-4 1-4 2-6 4-9z" class="L"></path><path d="M485 535h0c-2 2-4 4-6 7 6-3 12-6 19-3 3 1 6 3 8 6v1c-4-4-8-7-15-7-3 1-8 2-11 5v-1-1h-1v-2l-1-1h0c1-2 5-3 7-4z" class="Q"></path><path d="M490 533c2 0 4 0 6 1h4c1 1 3 2 4 3 0 2 1 2 2 4 0-2-1-2-1-3l1-1c0 1 1 3 2 4v1c1 1 1 1 1 3l-3-3c-2-1-5-3-7-4-1-1-3-1-4-2-2-1-5-2-8-1h-2 0c1-1 4-2 5-2z" class="C"></path><path d="M504 572c6 5 8 11 8 18l1 3c2-6 2-12 0-18-1-1-2-3-2-4s0-2-1-3h0c4 6 7 14 5 21l-5 17v-1l1-2 1 2c-2 3-4 6-6 10l-3 6c0 1-1 2-2 3h-1l1-3c3-7 3-14 0-21 0-1 0-1 1-2h0c1 2 2 5 2 8 1 1 1 3 2 5v1l1-1c-1-1-1-6-1-8h0c1 1 1 2 1 2v5c1-1 1-2 1-2 0-4 2-8 2-11 1-3 1-6 1-10 0-6-3-9-8-13l1-2z" class="J"></path><path d="M491 572l-1-1c1 0 2 1 3 1l9 3-4-4c2 1 3 2 5 3 5 4 8 7 8 13 0 4 0 7-1 10 0 3-2 7-2 11 0 0 0 1-1 2v-5c0-10-3-18-9-26-2-3-4-5-7-7z" class="D"></path><path d="M491 572l-1-1c1 0 2 1 3 1l9 3c1 1 2 2 3 4 0 1 1 1 2 2l1 3h0 0-1l-2-2v1c-2-1-4-3-4-5v-2l-1-1-1 1v2l-1 1c-2-3-4-5-7-7z" class="F"></path><path d="M520 535c2-2 6-6 9-7 2 0 4 0 6 1s3 3 4 5c0 3 0 6-2 8-3 5-7 7-11 10-2 2-4 5-6 7 1-4 3-7 5-11 2-3 5-5 6-9l-5 4c2-3 5-6 8-9-4 1-7 3-11 5l3-3c1-1 4-3 4-4-3 1-6 2-9 5 0-1-1-1-1-2z" class="W"></path><path d="M93 458c-2-3-4-5-6-8-4-6-8-13-11-20-7-13-13-27-17-42-2-7-3-14-4-20-3-22-4-44-3-65 5-65 28-136 80-180 18-15 40-28 64-35 3-2 7-2 11-3 29-8 60-10 90-7 12 1 24 4 35 7 4 1 7 2 11 3 24 8 47 21 65 37 7 7 13 14 19 21 9 10 16 23 22 35 23 48 26 101 21 153 0 22-5 43-12 63-5 17-13 34-23 49-3 4-6 9-9 12l-8 10c-2 2-3 3-4 5 2 1 4 2 5 4v1h0c2 3 3 6 4 9-3-3-5-5-8-6l5 5c2 3 3 7 5 10-2-1-3-3-6-4l4 4c1 2 1 4 1 6 1 2 1 8 0 10h0l-2 6h0l-1-2c0 1 0 3-1 4-1 4-2 7-3 10-1-4-2-6-4-10h-1c1 3 2 5 2 8 2 11-4 23 0 34 2 5 6 12 11 15l6 3 2 1c-4-1-7-3-11-4-2-1-5-2-8-3-4-2-8-6-10-10l-2-2c1 11 3 22 12 30 4 4 7 5 12 6-3 2-9 0-12-2-3-1-6-4-9-6 3 3 5 7 8 10 5 4 12 7 18 9 5 1 10 1 14 1 4-1 8-3 12-3 8-2 18 1 22 8 4 5 4 11 2 17l-2 6h0l-1-1s1-1 1-2h0c0-2 0-3-1-4-1 4-3 10-7 12-4 3-9 5-14 7-1 0-2 1-4 1h0-2c0 1-1 1-1 1-2 1-3 1-5 1h-2c-3-1-6-1-10-2l-11-2c-1 1-2 0-3 0h0 0c0-1 1-1 2-1l1-1h5c3 0 5 0 8-1-1 0-2 0-2-1-1 0-5 0-6-1h-1c-3 1-8 2-12 1-3 0-5-2-8-3h0s-1 0-1-1h-3c-3-2-5-4-7-6s-3-4-4-6v-1c1 0 2 0 3 1l1 1c0-1-1-1-1-3 0 0-5-3-6-5 0-1-1-1-2-2 1 0 1-1 2-1l3 3v-2h0c-1-1-3-3-3-4-1-3-2-5-3-7l1-1c-1-2-3-3-4-5l-4-6v-1l-1-1c0-3 0-7-1-10-1-1-1-3-2-4v-3-3c-1-2 0-5-1-7 0-6 1-12-1-18v6c0 3 0 8-1 11v1l-1-1c-1 7 0 16-2 23-3 7-7 14-12 20-5 7-10 12-10 22 0 4 2 8 2 12-5-11-4-24-1-35 1-2 2-4 2-6-3 4-6 8-8 12v1c-1-4 2-11 4-15 1-2 3-4 4-6h0-2 0c0-1 1-1 1-2l-6 5 3-6c-3 2-5 4-8 5-7 3-16 3-22 8-5 4-6 9-7 15v-5c2-14 17-15 28-20 3-2 5-4 8-6l-1-1c-4 2-8 4-12 4h-1c-1 0-3 1-4 0h-2c-8 1-14 1-22-1 2-1 5-1 7-2 3-1 5-4 8-6 2-1 4-3 7-5l-1-1c-5 2-10 5-16 6-2 1-4 1-6 1 2-1 4-2 6-4 2-1 4-2 6-4-5 2-9 2-13 5v-1c8-5 14-14 23-19l6-3-1-1c-5 2-10 4-15 5-1 1-3 2-5 1 4-1 10-4 13-7h0v-1c-6 2-12 4-18 2l-1-1c-2 4-6 8-10 10-2 1-5 2-7 2l-1-3c0 1 0 1-1 1-1 1-4 1-5 1-1-2-1-2-1-4l-3 3h0c-1-1-1-2-1-3 0-7 7-6 11-8 1 0 3-1 4-3 4-4 3-8 3-14l-2 8-1-7c-9 1-19 1-28 1-44-1-88-13-124-38a210.67 210.67 0 0 1-38-35c-2-3-5-6-5-9z" class="D"></path><path d="M352 593c0 1 0 3 1 3h0l-2 2h-1c1-2 1-3 2-5z" class="G"></path><path d="M276 286c-1-1-1-5-1-6h1c0 2 2 2 2 4-1 1-2 1-2 2z" class="P"></path><path d="M387 536c1 1 1 1 1 2l-1 1 1 1h-1c-1-1-2-3-4-4h4 0z" class="N"></path><path d="M388 516c-1-2-3-5-4-7 1 0 4 3 5 4-1 1-1 2-1 3z" class="I"></path><path d="M402 492c3 0 6 0 8 1-1 0-2 0-2 1h0l-6-1h0 0v-1h0 0z" class="E"></path><path d="M313 284c1 1 2 1 2 2v1c-1 1 0 3 0 5h0v2l-2-2h0v-1-1l1-1c0-1 0-1-1-2h1s0-1 1-1c-1 0-1-1-2-2z" class="P"></path><path d="M405 477c2 0 6 1 8 3h-9c1 0 2-1 3-1v-1l-2-1zm-43 66h1c-1-1-1-2-1-2v-5l1 1c1 2 1 4 0 7v1h-1v-2z" class="I"></path><path d="M278 301c2 1 4 2 7 4h0c-2 0-5 0-7-1 1-1 0-2 0-3z" class="F"></path><path d="M371 409l5-7-4 9c0-1 0-1-1-2h0z" class="C"></path><path d="M402 526c1 1 1 4 1 6-1-1-3-2-4-4l3-2z" class="P"></path><path d="M325 478l3-2s-1 0-1-1h0c3-1 6 1 8 2h-5c-1 1-3 1-5 1z" class="F"></path><path d="M298 303h-1c-1 0-3-3-3-4 2 0 4 0 6 1l-1 1h0c0 1-1 1-1 2z" class="P"></path><path d="M271 287h0v1 1c0 3-3 4-5 5h0v-1c1-2 2-3 4-4h0l1-2z" class="M"></path><path d="M402 515l4 5h0-2 0l1 2h-1c-1-1-2-3-3-4h1v-3z" class="C"></path><path d="M357 545c0 2 0 2 2 4 0 4-1 8-2 12v-8c0-1 1-3-1-5 1-1 1-2 1-3z" class="E"></path><path d="M321 555s1-1 2-1v-1h-2l1-1h1c2 1 6 0 8 0 1 1 2 1 3 1h-1c-4 1-7 0-11 2 0 0 0 1-1 1v-1z" class="H"></path><path d="M389 513c1 0 2 1 2 1l-1 1c1 2 3 5 4 6l-2-1h-1l-3-4c0-1 0-2 1-3z" class="N"></path><path d="M391 520h1l2 1 1 2 1 2h0c0 2 0 3 1 4l-1 1-1-1-4-9z" class="I"></path><path d="M292 534c1 1 3 4 4 5v4h-1v-1-1l-2 2-1-9z" class="G"></path><path d="M315 470c2-1 3-2 5-2h0c-1 1-1 1-2 1-1 1-1 1-2 1 1 0 1 0 2-1l2 1c-2 1-5 2-8 4v-1h-1l1-1s2-2 3-2z" class="O"></path><path d="M376 411c1-2 3-4 4-5l1 1c3-2 4-4 6-6 0 2-3 5-4 6-1 0-1 0-2 1l-3 3h-2z" class="I"></path><path d="M304 294c3-1 5 0 7 2 2 1 3 2 4 4l-6-4-1 1c-1-1-3-3-4-3z" class="H"></path><path d="M243 320l1-1 1 1c-2 2-3 4-4 5h-2v-1c0-2 2-3 4-4z" class="C"></path><path d="M263 453l1 1v2c-1 4-1 8-1 12-1-2 0-5-1-7-1-1-1-2-1-3v-1l2-4z" class="L"></path><path d="M254 330c-1 0-5 1-6 0 0-1 1-1 1-2v-1h7v1c-1 0-3 0-4 1h1v1h1z" class="M"></path><path d="M302 556l1-4 6 1c1 0 2 0 2 1l-6 1h-2v1 1l-1-1z" class="K"></path><path d="M412 412h0c2 0 4 1 5 3 1 1 2 3 2 4-2-2-5-4-7-6v-1z" class="H"></path><path d="M280 486l1-1 2 1c-2 2-4 5-4 8l-1-1c0-1 0-4-1-6v-1l1 1v1h1l1-2z" class="V"></path><path d="M309 470h0c2 0 4-1 5-1l1 1c-1 0-3 2-3 2l-1-1c-2-1-6 3-8 3v-1l6-3z" class="H"></path><path d="M399 522c1 1 2 2 3 4l-3 2-3-3h0l-1-2 3 1 1-2z" class="N"></path><path d="M340 531v1c-1 1-1 2-3 3l-1 1h0l-1-1c-2 1-4 2-6 2 1-1 3-1 4-2h-2v-1l7-1 2-2z" class="C"></path><path d="M400 473c4 0 8 0 11 2h0-4l-12 1 5-3z" class="E"></path><path d="M324 551l1 1c1 0 2-1 3-1 3-1 5-2 8-4l1 1c-2 1-4 2-5 3l1 1c2 0 3 0 5-1-2 1-3 1-4 2-1 0-2 0-3-1-2 0-6 1-8 0l1-1z" class="I"></path><path d="M301 551c0 2-1 4-3 5h-1c-1 0-2-1-2-2v-3l1 1c2 0 3 0 5-1z" class="F"></path><path d="M388 506c2 1 3 2 5 3h2v1l-4-1c1 1 2 3 3 5-2-1-4-2-5-4-1-1-1-2-1-4z" class="P"></path><path d="M370 416v3h1c1 0 1 1 2 1l-5 9c0-3 2-7 1-10h-1 0l2-3z" class="I"></path><path d="M313 465l-12 7c3-4 7-6 12-8 1 0 1 0 0 1z" class="N"></path><path d="M269 313c4-1 7-1 10 1 1 0 1 1 2 2-1 0-2-1-4-1-1 0-2-1-3 0-2-1-4-1-5-2z" class="I"></path><path d="M301 297c0 1 1 2 2 2 3 1 4 0 7 0v-1c1 1 1 1 1 2-1 0-3 0-3 1l3 3-12-6v-1l1 1 1-1z" class="N"></path><path d="M374 575v-4l1-1 2 14c-1 0-1 0-1 1-1-1-1-3-2-4v-3-3z" class="S"></path><path d="M336 355c3-4 5-7 9-9h0c-3 4-6 7-8 12l-1-1c0-1 1-1 0-2zm23 219v1c0 3-1 5-3 7-2 0-3 2-4 3h-1c2-4 5-7 8-11z" class="B"></path><defs><linearGradient id="C" x1="368.053" y1="411.458" x2="370.452" y2="416.751" xlink:href="#B"><stop offset="0" stop-color="#454443"></stop><stop offset="1" stop-color="#5c5c5a"></stop></linearGradient></defs><path fill="url(#C)" d="M371 409c1 1 1 1 1 2l-1 2c0 1-1 2-1 3l-2 3h-1v-1c0-1 1-3 1-5l3-4z"></path><path d="M404 480c-4 0-8 1-12 0 4 0 9-3 13-3l2 1v1c-1 0-2 1-3 1z" class="B"></path><path d="M320 546c-3 0-7 1-10 1-1 0-3-1-4-2l1-1 9 1h4v1z" class="O"></path><path d="M330 345v2l1 1 3-7c0 2-1 6-1 8h1 0c1-2 2-3 3-5l-3 7c-1 0-1 0-2-1h0c-1 0-2-1-2-2h0c0 1-1 2-1 2 0-2 0-3 1-5z" class="B"></path><path d="M384 567c2 2 2 5 4 7 1-1 1 0 1-1l1 1c0-1 1-2 1-2v4h0l-1-1h0l-1 1 2 3v1h1l-1 1c-1-2-3-7-5-8 0 1 0 1-1 2 0-3-1-5-1-8z" class="F"></path><path d="M394 439l17 3h-2c0 1 0 1-1 1-4-2-9-2-14-3v-1h0z" class="E"></path><path d="M293 295c-1 0-4-2-5-2h7c-2-1-3-2-4-3s-3-2-4-2c2 0 6 0 7 1v1l2 2c0 1 0 1-1 1 0 1-1 1-2 1h-1 0l1 1h0z" class="P"></path><path d="M352 593c2-2 5-5 8-7-2 4-4 8-7 10h0c-1 0-1-2-1-3z" class="B"></path><path d="M382 529c0-1-1-2-1-3l-1-2c-1-1-1-2-2-3v-1-1s0-1-1-2v-1c1 4 4 9 7 12 0 1 1 1 2 1h0c0 3 1 5 3 8h-1v1l-1 1 1-1c0-1 0-1-1-2-1-2-3-5-5-7z" class="F"></path><path d="M343 588l2-2c2-1 3-3 4-5l5-7c0 3-1 5-3 7-1 2-2 3-4 4-1 1-2 3-3 3v1l-1-1z" class="E"></path><path d="M402 493c-3 0-6 0-9-1-2-1-7-3-8-5l3 2c5 1 9 1 14 3h0 0v1h0 0z" class="M"></path><path d="M272 330h0c1 0 2 1 3 2v1c1 1 2 2 3 4v1-1c-2-1-6-1-7-2 1-1 1-1 1-2l-2-2h0l2-1z" class="F"></path><path d="M309 506c2 1 4 2 7 1 1 0 3-1 4-1l-2 3s-1 0-2 1h-3c-2 0-4-1-4-2-1-1-1-1 0-2z" class="K"></path><path d="M284 479h1 0c1-2 3-4 5-4l-7 11-2-1-1 1c1-1 2-3 2-5l2-2z" class="R"></path><path d="M385 411c2-1 3-3 5-4l1-1c0 2-4 6-5 8-1 1-2 2-4 2h-1v-2c1-1 3-2 4-3z" class="E"></path><path d="M382 416l-3 3h-1l1-2s0-1-1-1c3-4 6-8 9-11-1 2-2 4-2 6h0c-1 1-3 2-4 3v2h1zM271 306h-1v-1-1c0-1 1-1 1-1 2-2 3-2 6-2h1c0 1 1 2 0 3-1 0-2 0-3 1l-4 1z" class="B"></path><path d="M452 626l1 1h1c2 2 3 4 3 7h0c1 3 0 7-1 9-1 1-2 2-3 2h0l3-6c0-3 1-5 0-7 0-2-1-4-3-5v1h-1v-2z" class="E"></path><path d="M253 305h1v10 1c-1-1-1-1-2-1-1-1-1-4-1-5 0-2 0-3 2-5z" class="W"></path><path d="M357 412c0-2 1-4 2-5h0c-1 6-1 11-1 17v2c-1 0-1-1-1-1v-3c-1-3-1-6 0-10z" class="Q"></path><path d="M366 408c1-2 3-4 4-5 0 1-1 3 0 4v2h1 0l-3 4c-1 1-1 2-2 3 0-3 0-5 1-8h-1z" class="F"></path><path d="M375 368v1c0 5 4 9 4 13h-2 0-1v-6c-1-1-1-3-2-3l-2 1v-3c0 1 1 1 1 2h0v-3h0l2-2z" class="E"></path><path d="M397 565c1 3 2 6 1 10h0l-1-1v1 2h-1l-1-1h0 0v-2c0-3 1-6 2-9z" class="I"></path><path d="M372 541v-3l1-6c0 2 2 6 2 8l-1 1 1 1v1l1 2c0 1 0 2 1 3h0l1-1h0v-1h0v-2h0c-1-1-1 0-1-1v-1c1 1 2 4 2 5h0c-1 1-1 2-2 2l-1 1c0-3-2-8-4-9z" class="F"></path><path d="M371 413c2 0 4-5 5-6s2-2 2-3c0 2-3 5-3 7l1 1h-1c0 1 0 1-1 2h-2l-1 3h0 1c1 0 2 0 3 1l-2 2c-1 0-1-1-2-1h-1v-3c0-1 1-2 1-3z" class="M"></path><path d="M385 432c2-1 5-1 7 0h1v1h1s1 0 1 1c-1 1-2 1-4 1h1l-1-1c-4-1-6 0-10 1v-1c1-1 3-1 4-2h0z" class="F"></path><path d="M394 486c2 0 4 0 6 1 1 1 4 2 6 3-4 1-12-1-15-3v-1c1 1 2 1 3 0h0z" class="C"></path><path d="M471 620c2 0 3 1 4 3 1 3 1 6 1 9h0-1c0 1-1 3-1 4l-1-1 1-2c1-5-1-9-3-13zm-90-257c3 2 5 4 7 7h0c-1 1-1 1-2 1-2-1-3-2-4-2v-2c-1-1-2-1-3-2v-1l1 1c1 0 1 0 1-1l-1-1h1z" class="B"></path><path d="M274 324l1-1c-1-1-6-2-7-3 2 0 6 1 8 2s4 1 6 3c1 1 3 2 4 4-1-1-5-1-5-2h0c-1-2-6-3-7-3z" class="H"></path><path d="M263 453v-1a19.81 19.81 0 0 1 11-11l1-1c-5 5-9 9-11 16v-2l-1-1z" class="E"></path><path d="M274 324c1 0 6 1 7 3h0c-8-2-17-2-25 1v-1l18-3z" class="G"></path><path d="M303 524c1-1 2-1 3 0l2 1 3 2 4 3c-2 0-3 1-5 1l1-1c-1-1-2-1-3-2-1 0-1 0-2-1h0 0l-3-3z" class="S"></path><path d="M303 524c1-1 2-1 3 0l2 1 3 2-1 1c-1 0-3-1-4-1l-3-3z" class="L"></path><defs><linearGradient id="D" x1="359.738" y1="426.562" x2="364.886" y2="417.97" xlink:href="#B"><stop offset="0" stop-color="#242324"></stop><stop offset="1" stop-color="#474646"></stop></linearGradient></defs><path fill="url(#D)" d="M363 415h1v4h2c0 1 0 1-1 2 0 1 0 1-1 2h-1v2c0 2-1 4-1 6-1-6 0-10 1-16z"></path><path d="M306 315l10 2c1 1 4 1 6 2l1 1-1 1-15-4-1-1h-1l1-1z" class="L"></path><path d="M449 644c2-2 3-5 4-7h1l1 1 1 1-3 6h0c-1 1-3 1-5 1h0c0-1 1-2 1-2z" class="I"></path><path d="M449 644c2-2 3-5 4-7h1c0 3-1 5-3 7h-1-1z" class="C"></path><path d="M276 286c0-1 1-1 2-2 0 3 0 6-2 7-2 2-7 3-9 4l1-1c2-2 6-5 8-8z" class="B"></path><defs><linearGradient id="E" x1="294.19" y1="280.843" x2="297.116" y2="287.196" xlink:href="#B"><stop offset="0" stop-color="#363937"></stop><stop offset="1" stop-color="#4c474a"></stop></linearGradient></defs><path fill="url(#E)" d="M291 283c-2-1-4-1-6-2h1c3 0 7 0 10 2 3 1 6 3 9 5-3 0-11-4-14-5z"></path><path d="M323 320v-1-1c1-2 1-4 0-6h0 0c2 2 2 3 2 5-1 4-3 7-5 10v1c-2-2-3-4-5-6 1 1 4 1 5 0 1 0 1-1 2-1l1-1z" class="O"></path><path d="M394 521c-1-1-3-4-4-6l1-1 8 8-1 2-3-1-1-2z" class="G"></path><path d="M260 293h0v3 2l-4 4-1-1h-1-1c2-4 4-6 7-8z" class="N"></path><path d="M384 544c-4-6-8-13-9-20 4 5 7 12 10 18l-1 1v1z" class="E"></path><path d="M320 327h1 1c0-1 1-2 2-2s2 0 2 1c0 3-4 7-6 9 1-3 1-5 0-7v-1z" class="U"></path><path d="M352 516c2-4 4-7 5-11 1-2 1-3 1-4h0c0 7-2 15-6 20 0-1 0-3 1-4l-1-1z" class="I"></path><path d="M376 411h2l3-3c1-1 1-1 2-1-2 4-6 7-8 11-1-1-2-1-3-1h-1 0l1-3h2c1-1 1-1 1-2h1v-1z" class="E"></path><path d="M337 314c-5-2-10-3-15-4 0 0 14-1 15-2-1 1-1 2-1 4 0 1 1 1 1 2z" class="L"></path><path d="M329 350s1-1 1-2h0c0 1 1 2 2 2h0c1 1 1 1 2 1l-3 9c0-1-1-4-2-6v-4zm-34 201l5-9v1c0 1 0 2-1 3h1c1-1 1-2 2-3h0c0 1 0 4-1 5v3c-2 1-3 1-5 1l-1-1z" class="E"></path><path d="M307 282c-3-1-6-2-10-3-3-1-7-2-10-3h-1 0v-1c5 1 11 1 15 3 3 1 5 2 8 3l-1 1h-1z" class="C"></path><path d="M383 601l1 1c1 1 1 2 2 2 3 5 7 9 12 11l-1 1-7-5-4-3c-1-2-3-3-4-5 0-1 0-1 1-2z" class="O"></path><path d="M308 319c2 0 3 2 4 3h0l1 1h0l-6 3-2-2c1-1 1-2 1-3v-1c1 0 1-1 2-1h0z" class="H"></path><path d="M283 495h0c-1 3 0 9-1 12h-4c1-4 2-9 5-12z" class="Q"></path><path d="M337 358l-4 9v-3c1-8 2-15 8-21-2 3-5 8-5 12 1 1 0 1 0 2l1 1z" class="G"></path><path d="M261 303v1c-2 4-3 8-5 11l-1-10c1-1 4-1 6-2z" class="W"></path><path d="M446 641l1 1c-5 4-14 6-20 7h-6 1c-1 1-2 0-3 0h0 0c0-1 1-1 2-1l1-1h5c3 0 5 0 8-1 4-1 8-3 11-5z" class="T"></path><path d="M394 514c-1-2-2-4-3-5l4 1c2 2 5 3 7 5v3h-1l-7-4zm0-28c-1 0-4-1-5-2 3 0 6-1 9-1 2 0 3 1 5 1 2 1 4 2 7 3h-10c-2-1-4-1-6-1z" class="B"></path><path d="M242 332c3 1 6 1 9 1 5-1 10 0 15 2 1 0 3 0 4 1-5 0-10-1-14-2-4 0-11 1-14-1v-1z" class="M"></path><path d="M400 597c4 3 8 8 13 11 3 3 6 4 10 6h-1c-10-2-17-9-22-17z" class="E"></path><path d="M324 551c6-3 12-7 17-10l-5 6c-3 2-5 3-8 4-1 0-2 1-3 1l-1-1z" class="M"></path><path d="M253 329c6-1 12-2 19-1 3 0 6 1 9 2 2 1 4 3 6 4h-1l-1-1c-1-1-3-1-5-2s-4-2-7-2c-4-1-11 0-15 0l-4 1h-1v-1z" class="B"></path><path d="M346 448h1v1c0 1 2 2 3 3 2 5 3 10 5 15l-1 1c-2-7-5-13-9-19l1-1z" class="R"></path><defs><linearGradient id="F" x1="330.962" y1="349.719" x2="325.233" y2="353.641" xlink:href="#B"><stop offset="0" stop-color="#1d1d1e"></stop><stop offset="1" stop-color="#424142"></stop></linearGradient></defs><path fill="url(#F)" d="M329 354v1 5l-1-1c-1-3-2-8-1-11v-1c0-2 2-3 3-5 0 0 0-1 1-2v1c0 1 0 3-1 4h0c-1 2-1 3-1 5v4z"></path><path d="M395 507c7 3 14 5 18 13h0-1 0l-3-3c-3-4-9-6-14-8h-2l2-1v-1z" class="B"></path><path d="M405 554h1c2 4 5 9 7 13-2 0-4 0-6-2-2-3-2-8-2-11z" class="C"></path><path d="M366 408h1c-1 3-1 5-1 8 1-1 1-2 2-3 0 2-1 4-1 5v1h1 0c0 2-2 5-4 6h0-1v-2h1c1-1 1-1 1-2 1-1 1-1 1-2h-2v-4h-1c0-2 2-5 3-7z" class="N"></path><path d="M364 415c1-1 1-1 2-1v5h-2v-4z" class="E"></path><path d="M385 526c-1-1-1-5-2-7v1l4 4c2 2 2 5 4 6 1 0 1 0 1-1v-1h1-1v-1h0v-1l3 3h0l1 1 1-1v2c0 2 2 4 1 7h-1l-1-4-1-3-1-2-1 1-1 1h-1l-2-2c-1-1-1-1-2-1 0-1-1-2-2-2h0z" class="P"></path><path d="M459 616h1c1 0 2 0 3 1h0c3 2 6 6 7 8 2 4 2 7 1 11v1h-1c0-4-1-8-2-12 0-2-1-2-2-4-1-1-2-2-3-2-1-1-2-1-2-2-1 0-1 0-1-1h-1zm-76-80c-2-3-4-7-6-10l-1-2v-1h1l4 4v1h0l1 1c2 2 4 5 5 7h0-4z" class="M"></path><path d="M371 463c-1 1-3 4-4 5-1 0-1-1-2-1 0 0 0 1-1 0v-1c0-4 3-7 6-9h2 1c-2 1-4 3-4 5h0c1 1 1 1 2 1z" class="G"></path><path d="M309 470c5-3 9-4 15-6v1c1 0 1 1 2 1h1v-1h3c0 1 0 1 1 1h3 0 1c1 1 2 1 3 3l-1-1c-5-2-12-2-17 0-2 0-3 1-5 2l-1-1c-1 0-3 1-5 1h0z" class="C"></path><defs><linearGradient id="G" x1="385.516" y1="571.957" x2="374.084" y2="562.55" xlink:href="#B"><stop offset="0" stop-color="#242726"></stop><stop offset="1" stop-color="#3e3b3c"></stop></linearGradient></defs><path fill="url(#G)" d="M377 553h0c0 2 0 4 1 6l1 1c1 4 7 21 6 23-1-3-2-6-3-8l-4-11c-1-2-2-4-2-6l1-1v-4z"></path><defs><linearGradient id="H" x1="394.87" y1="492.018" x2="404.119" y2="498.782" xlink:href="#B"><stop offset="0" stop-color="#262527"></stop><stop offset="1" stop-color="#494849"></stop></linearGradient></defs><path fill="url(#H)" d="M406 499c-2-1-4-2-6-2-2-1-5-1-8-2-2-1-3-1-5-3 4 1 7 2 11 2 5 1 10 1 15 4-2-1-4 0-6 0l-1 1z"></path><defs><linearGradient id="I" x1="293.914" y1="289.134" x2="297.972" y2="284.151" xlink:href="#B"><stop offset="0" stop-color="#0d0d0e"></stop><stop offset="1" stop-color="#363635"></stop></linearGradient></defs><path fill="url(#I)" d="M291 283c3 1 11 5 14 5h2v1h-1-1 0c3 2 5 3 8 6l-6-3c-5-3-11-5-17-6-1-1-4-1-5-2 0 0-1 0-1-1h1l2 1c2 0 3 0 4-1z"></path><path d="M392 443c2-1 5-1 7 0 1 1 1 2 2 3h-5-5l-9 1c2-2 7-4 10-4z" class="N"></path><path d="M401 393h0c0 1 0 4-1 6-1 4-4 11-9 13h0c0-3 2-6 4-9 3-3 5-6 6-10z" class="V"></path><defs><linearGradient id="J" x1="415.096" y1="499.307" x2="423.051" y2="501.12" xlink:href="#B"><stop offset="0" stop-color="#a19f9f"></stop><stop offset="1" stop-color="#c2c3c5"></stop></linearGradient></defs><path fill="url(#J)" d="M408 494h0c0-1 1-1 2-1 4 2 8 3 10 5 1 2 2 3 4 4h0c1 2 1 8 0 10 0-3-1-5-2-8-4-5-8-8-14-10z"></path><path d="M312 494l1-1c1 1 3 2 3 3 1 2 1 4 1 6h-2c-1 0-4 0-5-1-1-2 0-4 0-5s1-2 2-2z" class="N"></path><defs><linearGradient id="K" x1="339.476" y1="543.995" x2="341.018" y2="549.492" xlink:href="#B"><stop offset="0" stop-color="#2e2f2e"></stop><stop offset="1" stop-color="#474648"></stop></linearGradient></defs><path fill="url(#K)" d="M337 548l3-1c2-1 4-2 5-4 2-2 3-5 5-7 0 1-1 2-1 3h-1v2h0v-1c1 0 1-1 2-1h0 0l-6 9-6 3c-2 1-3 1-5 1l-1-1c1-1 3-2 5-3z"></path><defs><linearGradient id="L" x1="387.259" y1="495.844" x2="387.658" y2="508.415" xlink:href="#B"><stop offset="0" stop-color="#2a2a2b"></stop><stop offset="1" stop-color="#4b4a4b"></stop></linearGradient></defs><path fill="url(#L)" d="M388 506c-2-1-5-3-7-6-1-2-3-6-3-9 4 7 10 13 17 16v1l-2 1c-2-1-3-2-5-3z"></path><defs><linearGradient id="M" x1="400.856" y1="411.195" x2="402.78" y2="418.463" xlink:href="#B"><stop offset="0" stop-color="#70746f"></stop><stop offset="1" stop-color="#8e8a90"></stop></linearGradient></defs><path fill="url(#M)" d="M410 403l1 1c-3 2-2 5-4 8-1 4-6 8-10 9-2 1-4 1-6 1 2-1 3-1 4-2 8-3 10-12 15-17z"></path><path d="M352 516l1 1c-1 1-1 3-1 4l-6 9c-1 1-2 2-4 3-4 3-9 6-14 8 2-2 5-4 8-5h0l1-1c2-1 2-2 3-3v-1h0l3-2c2-1 7-6 8-9v-1-1c0 1-1 1-2 1v1l3-4z" class="M"></path><path d="M363 545c0 1 0 3 1 4 1-2 0-4 1-6h1c1 3 0 9-1 12-1 1-3 7-4 7 1-1 1-3 1-5 0-4-1-8 0-12h1z" class="G"></path><defs><linearGradient id="N" x1="411.881" y1="502.551" x2="417.343" y2="499.731" xlink:href="#B"><stop offset="0" stop-color="#888786"></stop><stop offset="1" stop-color="#a2a0a4"></stop></linearGradient></defs><path fill="url(#N)" d="M406 499l1-1c2 0 4-1 6 0 3 1 7 3 8 7v1 1l-2-2h0v1c-2 0-3-1-4-2-2-2-6-4-9-5z"></path><defs><linearGradient id="O" x1="265.17" y1="432.363" x2="263.153" y2="443.649" xlink:href="#B"><stop offset="0" stop-color="#989796"></stop><stop offset="1" stop-color="#bbbabd"></stop></linearGradient></defs><path fill="url(#O)" d="M260 437l1 1c3-3 6-5 10-6l1 1c-1 2-4 2-4 4-4 2-8 5-9 9v3-4c-1 0-1-1-1-1v-1 1l-1-1 3-6z"></path><path d="M376 585c0-1 0-1 1-1 2 6 4 13 8 18h-1l-1-1c-1 1-1 1-1 2l-4-6v-1l-1-1c0-3 0-7-1-10z" class="J"></path><path d="M378 596v-1c2 2 3 5 5 6-1 1-1 1-1 2l-4-6v-1z" class="K"></path><defs><linearGradient id="P" x1="327.978" y1="533.528" x2="331.61" y2="529.064" xlink:href="#B"><stop offset="0" stop-color="#2b2b2b"></stop><stop offset="1" stop-color="#4a494a"></stop></linearGradient></defs><path fill="url(#P)" d="M331 535v1h-1-1v1c-1 0-2 0-3-1h0 1c1-1 2 0 3-1-1 0-2-1-2-1l-1-1-1-1c-1 0-1 0-1-1-1 1-1 1-2 1s-1 0-2 1c-1 0-2 1-3 1 0 0 0-1 1-1 1-1 2-2 4-2 3 0 5-1 8-3 0 1-1 2-1 2h0c1 0 2 1 3 0h2v1s-1 0-1 1c1 0 2-1 3-1v1h0l1 1-7 1v1z"></path><path d="M376 382h1 0 2c0 5-2 7-5 10h0-1l1-1-1-1h-2v-2h-2 0c1-1 1-2 2-2 0-1 1-1 1-1l2-2 1 1c1 0 1-1 1-2z" class="J"></path><path d="M287 506c1-6 4-13 8-18l1 1c-1 2-3 4-4 7h0c0 1-1 2 0 3l1 1c1-1 2-1 2-2v-1h1v3c-1 1-1 2-1 3 1 0 1 0 2 1v-1h1v3h-2c-1-1-1-2-2-3s-2-1-2-1l-1-1h0c-2 1-2 4-3 5h-1z" class="L"></path><path d="M395 500h0l-7-4 7 2c3 2 13 4 14 6h-1-1v-1h-2v1h1l1 1h-1l1 1 1 1h1c-2 0-4 1-6 1-2-1-5-3-6-4 1 0 2 1 4 1 1 0 2 0 3-1-1-1-2-2-4-2h-1c0-1-1-1-1-1-2 0-2 0-3 1l-1-1 1-1z" class="C"></path><path d="M302 508c2 1 2 3 3 4-1 1-1 2-1 3v1h0c1 2 2 4 4 6l-1 1c0 1 1 1 1 2l-2-1c-1-1-2-1-3 0h0c-2-1-3-7-3-9l1 2 1-1c-1-3 0-5 0-8z" class="H"></path><path d="M302 508c2 1 2 3 3 4-1 1-1 2-1 3v1h0v2 2h0c-1 0-1-1-2-2v-1-1c-1-3 0-5 0-8z" class="F"></path><path d="M408 443c1 0 1 0 1-1h2c1 1 4 2 5 4v3h0l-1-1c0-1-1-1-2-2 1 2 2 4 3 5 0 1-2 5-2 6h0l-1-1c-3 0-5 2-7 4-3 2-7 4-9 7h0 0c1-3 3-5 5-6l9-6c1 0 2-1 2-1 1 0 1-2 1-2-1-4-3-6-5-9h-1z" class="V"></path><path d="M298 303c0-1 1-1 1-2h0l1-1c6 4 12 7 18 13l-20-10z" class="M"></path><defs><linearGradient id="Q" x1="383.678" y1="498.227" x2="401.703" y2="503.134" xlink:href="#B"><stop offset="0" stop-color="#181818"></stop><stop offset="1" stop-color="#404041"></stop></linearGradient></defs><path fill="url(#Q)" d="M397 504c-5-1-11-3-13-7l-1-1c-1-1-1-2-2-4 4 5 8 6 14 8l-1 1 1 1c1-1 1-1 3-1 0 0 1 0 1 1h1c2 0 3 1 4 2-1 1-2 1-3 1-2 0-3-1-4-1z"></path><path d="M356 531c0 2-2 6-3 8-1 3-4 6-4 9-2 1-3 3-5 4-4 2-10 4-14 6h-1l-1-1 7-2c3-2 8-5 11-8 5-4 7-10 10-16z" class="Q"></path><defs><linearGradient id="R" x1="266.273" y1="318.457" x2="280.306" y2="315.722" xlink:href="#B"><stop offset="0" stop-color="#0a080a"></stop><stop offset="1" stop-color="#383939"></stop></linearGradient></defs><path fill="url(#R)" d="M282 320l1 2c-1 1-1 0-2-1-5-3-9-6-15-5h-2v-1l2-1c1-1 2-1 3-1 1 1 3 1 5 2 1-1 2 0 3 0 2 0 3 1 4 1 1 1 3 2 4 4h-1 0-2z"></path><path d="M274 315c1-1 2 0 3 0 2 0 3 1 4 1 1 1 3 2 4 4h-1 0-2l-1-1c-2-2-5-3-7-4z" class="G"></path><defs><linearGradient id="S" x1="301.821" y1="527.654" x2="309.608" y2="530.771" xlink:href="#B"><stop offset="0" stop-color="#99989a"></stop><stop offset="1" stop-color="#b4b4b3"></stop></linearGradient></defs><path fill="url(#S)" d="M310 531c-2 0-5 0-7-1-3-1-8-3-10-6h0 4 3l6 3h0c1 1 1 1 2 1 1 1 2 1 3 2l-1 1z"></path><path d="M283 474h1c0 1-1 2-1 3h0 2 0l-1 2-2 2c0 2-1 4-2 5l-1 2h-1v-1l-1-1v1l-2 2h0v-3c1-3 3-7 5-10h1c1 0 1-1 2-2z" class="S"></path><path d="M278 487c1-1 1-3 2-5 1 0 1-1 2-2v1c0 2-1 4-2 5l-1 2h-1v-1z" class="U"></path><path d="M312 328h1c2 0 4 2 5 3v2c-1 2-2 3-3 4h-1c-1 0-3 0-4-1s-1-3-1-4c0-2 2-3 3-4z" class="C"></path><path d="M163 183c2 3-2 9-1 13 0-1 0-1 1-2 0-1-1-1 0-1 0-1 0-3 1-3v-5l1-1h0 0l-3 15c-1 7-2 14-4 20-1-12 3-24 5-36z" class="U"></path><path d="M309 281c1 1 3 2 4 3s1 2 2 2c-1 0-1 1-1 1h-1c1 1 1 1 1 2l-1 1v1 1h0c-3-1-4-2-7-3h1v-1h0c-1-1-3-4-3-5h2 1v-1h1l1-1z" class="H"></path><path d="M307 288c-1-1-3-4-3-5h2c2 1 5 2 6 5l-1 1h-2l-2-1z" class="B"></path><path d="M271 306l-6 2 2-2c1-1 1-1 1-2-1 0-1 1-2 0l3-3c1 0 2 0 3-1h-1c-2 0-4-1-6 0-1 1-2 1-4 2 2-2 5-4 7-5s5-2 8-1l-1 1v1c3-1 5 0 7 1-2 1-6 0-8 1v1h3c-3 0-4 0-6 2 0 0-1 0-1 1v1 1h1z" class="H"></path><path d="M332 501c0-3-2-5-3-6-4-5-8-5-14-6h0c4-1 7 0 10 1 3 2 7 4 8 8 0 1 0 2 1 3v-3 5l1 1c-1 4-5 10-9 12l-3 1c0-1 1-1 2-2 3-2 7-5 7-9v-2-3z" class="O"></path><path d="M305 324l-4-5c0-1-1-2-1-2v-4l1-1 1 1 1-1c1 0 2 1 4 1 3 0 6 0 9 2v1c-4 0-9-2-13-3l2 2h1l-1 1h1l1 1h0l1 2h0c-1 0-1 1-2 1v1c0 1 0 2-1 3z" class="U"></path><path d="M384 544v-1l1-1c4 6 8 11 10 18-1 0-1 1-2 1-1-1-2-1-3-1l-6-16z" class="R"></path><path d="M268 498h0c0-1 0-1 1-2v-1-1h2l1 1h0v-1 1c1 2 1 4 1 6h1v-2 3 5h-1v1h0c-1-2-2-3-3-4-1 0-2-1-2-1v1h-1v1l-1-11c0 1 1 2 1 2v2c0 1 0 3 1 4h0v-1-1-2z" class="P"></path><path d="M268 498h0c0-1 0-1 1-2v-1-1h2l1 1-1 1c0 1 1 3 1 4v5c-1-2-2-5-4-7zm199 129h1v5c-2 6-2 9-7 13h0c-2 2-4 2-5 3s-1 1-2 1c-1 1-2 1-3 2h-1-2v1c-1 0-3 0-3-1h0v-1h2 2c0-1 1-1 1-1h1l2-1 6-3 4-4h-1v-1c2-4 1-7 1-11h0c2 1 1 5 2 6h1v-4c0-1 0-1 1-2v-2zm-101-41c0 2-1 4-1 5-1 4-4 7-7 11-2 3-4 5-7 8h0 0c-1-1 0-2 0-3 2-6 8-10 12-16 1-1 2-3 3-5z" class="M"></path><path d="M316 545c1 0 1 0 1-1 2 0 7 0 8 1l3-1c0 2-5 7-7 8s-7 2-10 2c0-1-1-1-2-1h5c1 0 2-1 3-1s3-3 4-4c-2 0-4 1-7 2h-3-3c-2 0-3 0-4-1 1 0 1 0 1-1h2c2 0 6 1 8 0h0c1 0 2-1 3-1s2 0 3-1h-1v-1h-4z" class="G"></path><path d="M361 531h0v2 2 2l1-1h1v1l-1-1v5s0 1 1 2h-1v-1h0c-2 1-2 4-3 5v2c-2-2-2-2-2-4 0 1 0 2-1 3 2 2 1 4 1 5l-3 1h-1l3-7c1-2 1-4 0-5 0-1 1-3 2-4v1c1-1 1-1 1-2 0 0 0-1 1-2v-1-1c0-1 1-1 1-2z" class="H"></path><path d="M356 548c2 2 1 4 1 5l-3 1c0-2 2-4 2-6z" class="S"></path><path d="M357 545l3-8-1 10v2c-2-2-2-2-2-4z" class="R"></path><path d="M289 480h2v1l-1 1h1 0 1 1c0 1 0 1-1 2l-4 11c-1 1-1 2-1 3l-1-1h0c0-2 1-3 0-4h-1l-1 1c-1 0 0 1-1 1h0c1-2 1-4 2-6 1-3 2-6 4-9z" class="H"></path><path d="M289 480c1 2 0 4-1 6-1 1-2 3-3 3h0c1-3 2-6 4-9z" class="G"></path><path d="M288 495l-1-1c0-3 0-5 2-8 0-1 1-2 2-3l1 1-4 11z" class="B"></path><path d="M471 620c-1-1-4-3-4-4 4 1 7 2 9 6 2 3 2 5 2 9-1 4-3 10-7 12-4 3-9 5-14 7-1 0-2 1-4 1h0c5-3 11-4 16-8 2-2 5-4 6-7 0-1 1-3 1-4h0c0-3 0-6-1-9-1-2-2-3-4-3z" class="O"></path><path d="M308 522c0 1 1 2 2 2h1c1 1 2 1 3 1 7 1 14-2 20-4-4 4-7 5-12 7-2 0-3 0-5 1 0 1-1 1-2 1l-4-3-3-2c0-1-1-1-1-2l1-1z" class="I"></path><path d="M294 289l6 2 4 3c1 0 3 2 4 3h1l1 1v1c-3 0-4 1-7 0-1 0-2-1-2-2l-1 1-1-1v1l-6-3h0l-1-1h0 1c1 0 2 0 2-1 1 0 1 0 1-1l-2-2v-1z" class="C"></path><path d="M296 292c1 0 2 0 3 1-1 1-2 0-2 1 1 1 3 2 4 3l-1 1-1-1v1l-6-3h0l-1-1h0 1c1 0 2 0 2-1 1 0 1 0 1-1z" class="H"></path><path d="M300 291l4 3c1 0 3 2 4 3h1-1c-1 1-3 1-4 0-1 0-4-3-5-5l1-1z" class="B"></path><path d="M399 605h2c-2-2-3-4-4-6l7 6c1 1 2 3 4 3 0 0 0 1 1 1 1 1 2 1 2 3h-1 0c0-1-1-2-1-2h-1v1c1 1 1 1 2 1h1c0 1 0 1 1 2v2c-1 0-1 0-2-1h0l-3-1c0-1-1-1-1-1s-1 0-1-1h0 0l-5-4c-1-1-3-3-4-3l-3-6 6 6z" class="N"></path><path d="M396 605l-3-6 6 6c3 3 6 5 10 7l-1 1c-1 0-2-1-3-1l-5-4c-1-1-3-3-4-3z" class="M"></path><path d="M272 494c-1-5 0-11 1-17 0-1 0-3 1-4 0 1 0 1 1 2 0 2-2 9 0 11h0v3h0l2-2c1 2 1 5 1 6l1 1c-1 3-2 7-3 10v-6c1-2 1-4 1-6l-1-1c-1 1-1 3-1 4s-1 3-1 4v2h-1c0-2 0-4-1-6v-1z" class="C"></path><path d="M320 468c5-2 12-2 17 0l1 1c5 3 9 7 11 13-1-1-2-3-3-4-4-4-11-8-17-9-3 0-6 0-9 1l-2-1c-1 1-1 1-2 1 1 0 1 0 2-1 1 0 1 0 2-1h0z" class="K"></path><path d="M325 478c2 0 4 0 5-1h5c3 2 6 4 7 7-2-2-4-5-6-6h-1v1h0c2 1 3 2 3 3v1l1 1c0 1-1 2-1 3v1h0l1 1h0v1h0 0c-1-2-2-3-4-4-2-2-6-4-9-6-1 0-3-1-5-2h4z" class="C"></path><path d="M326 480c1-1 3-1 4-1 2 0 4 1 5 2s1 2 1 3c0 0 0 1-1 2-2-2-6-4-9-6z" class="E"></path><defs><linearGradient id="T" x1="395.687" y1="572.269" x2="389.208" y2="564.346" xlink:href="#B"><stop offset="0" stop-color="#323131"></stop><stop offset="1" stop-color="#626162"></stop></linearGradient></defs><path fill="url(#T)" d="M395 560l2 5c-1 3-2 6-2 9v2h0c0 1 0 1-1 2 0 1 0 1-1 2v-1c-1 0-1 0-1-1l-1-1v-1h0v-4h0c1-5 0-8-1-12 1 0 2 0 3 1 1 0 1-1 2-1z"></path><path d="M391 576h1v-1c1 0 1-1 2-1v3h-1c-1 0-2 0-2-1h0z" class="C"></path><defs><linearGradient id="U" x1="369.805" y1="546.006" x2="379.059" y2="552.252" xlink:href="#B"><stop offset="0" stop-color="#353437"></stop><stop offset="1" stop-color="#646260"></stop></linearGradient></defs><path fill="url(#U)" d="M372 541c2 1 4 6 4 9l1 3v4l-1 1c-1-2-1-4-2-5l1 12v5l-1 1v4c-1-2 0-5-1-7 0-6 1-12-1-18v6c0 3 0 8-1 11v1l-1-1v-5l2-13v-8z"></path><path d="M287 506h1c1-1 1-4 3-5h0l1 1s1 0 2 1 1 2 2 3h2l2 2h2c0 3-1 5 0 8l-1 1-1-2c-1-3-2-2-4-3-1-1-1-2-2-2 0-1 0-1-1-1 0 1 0 2-1 2 0-1-1-3-2-4 0-1-1-1-1-1l-1 1h-1v-1z" class="I"></path><path d="M319 423h1c2 1 3 2 5 3l1-1 2 1v-1h0l1 1c1 0 1 0 1 1 1 2 3 3 4 5 4 5 9 10 12 16l-1 1c-4-7-9-14-16-18-4-3-9-5-13-7l1-1h2z" class="T"></path><path d="M385 526h0c1 0 2 1 2 2 1 0 1 0 2 1l2 2h1l1-1 1-1 1 2 1 3 1 4h1c0 2 1 5 1 7-1-1-1-3-2-3-2-1-3-3-4-3h-1v-2c0-1 0-1-1-1v-1h0c0 1 0 1-1 1 0 1 0 2-1 2h0l-1-1h1c-2-3-3-5-3-8h0l-1-3z" class="C"></path><path d="M395 537l-2-1c-1 0-1-1-1-2h0l2 1v-1c0-1-1-2 0-3h1l1 3-2-1v1c0 1 0 2 1 3z" class="M"></path><path d="M395 537c-1-1-1-2-1-3v-1l2 1 1 4v1h-1l-1-2z" class="B"></path><path d="M386 529h1c1 1 2 2 3 4 0 0 0 1-1 1l1 2-1 1h0c-2-3-3-5-3-8z" class="G"></path><path d="M352 568c1-1 3-4 4-5h0l1 1c-1 2-2 5-4 8-3 5-7 10-11 13-2 2-4 4-6 5-1 0-5 1-5 2h-1c-1 0-3 1-4 0h-2 0c7-2 14-5 19-10 4-4 6-9 9-14z" class="K"></path><path d="M376 550l1-1c1 0 1-1 2-2 2 6 5 10 7 15 2 4 2 8 3 11 0 1 0 0-1 1-2-2-2-5-4-7l-5-13v6l-1-1c-1-2-1-4-1-6h0l-1-3z" class="G"></path><defs><linearGradient id="V" x1="265.949" y1="464.654" x2="273.123" y2="460.662" xlink:href="#B"><stop offset="0" stop-color="#181819"></stop><stop offset="1" stop-color="#353435"></stop></linearGradient></defs><path fill="url(#V)" d="M264 467c1-3 2-4 3-7 1-2 2-5 4-7h0v1c-1 0-2 2-2 3l1-1h1l-1 1h1c1 1 2 2 2 3s-1 0-1 1l2-2v4h0v3l-2 4s-1 4-2 4v-8c-1 2-2 4-2 6 0 0 0 1-1 1h0v2-1c-1-1-1-1 0-2v-1-1-3c0-1 0-2-1-2v2h-1-1z"></path><path d="M272 470l-1-1c0-2 0-5 1-6h1c0 1 1 2 1 3l-2 4z" class="E"></path><path d="M372 431s0-1 1-1c1-4 6-7 10-8 1-1 3-2 5-3l5-3 1 1c-1 0-1 1-2 2 1 0 3-2 4-2 0-1 1-1 1-1 1 0 2-1 2-1h1c-1 2-2 3-4 4h0l-1 1c-1 1-2 1-4 2 2 0 4 0 6-1v2h0c-3 0-6 1-8 1-7 1-12 3-17 7z" class="H"></path><path d="M338 363c3-5 7-8 12-9 6-2 15-2 21 2 4 1 7 4 10 7h-1c-3-2-7-4-10-6-7-2-14-3-21-1-4 2-7 4-10 8l-1-1z" class="W"></path><path d="M346 563l1-1h0v2c1 0 4-9 6-10h1l3-1v8 3l-1-1h0c-1 1-3 4-4 5l-5-2-4 4c1-2 3-4 3-6v-1z" class="T"></path><path d="M356 554h1v2c0 3-2 6-4 8h-1c-1 0-1-1-2-1 2-3 4-5 5-8l1-1z" class="Q"></path><defs><linearGradient id="W" x1="381.597" y1="451.449" x2="383.688" y2="456.85" xlink:href="#B"><stop offset="0" stop-color="#3e3e3e"></stop><stop offset="1" stop-color="#676767"></stop></linearGradient></defs><path fill="url(#W)" d="M373 457c1-2 3-3 5-4 7-4 16-7 25-5-2 0-5 0-7 1-7 1-14 5-20 10-2 1-4 2-5 4h0c-1 0-1 0-2-1h0c0-2 2-4 4-5z"></path><path d="M385 602h2 0c1-2-1-5-2-7 3 3 5 6 8 8 1 1 2 2 3 2s3 2 4 3l5 4h0-1l-1-1c-1 0-3 0-4 1h1 1 1v2h-1 0c-3-1-5-2-7-4-1 0-2-2-3-2h-1l-1-2c-1 0-1-1-2-1 0-1-1 0-1-1-1 0-1-1-2-2h1z" class="C"></path><path d="M331 528h1l9-7c-1 3-3 5-5 7 2 0 3-2 4-2l9-9-3 5 3-2v-1c1 0 2 0 2-1v1 1c-1 3-6 8-8 9l-3 2h0l-2 2-1-1h0v-1c-1 0-2 1-3 1 0-1 1-1 1-1v-1h-2c-1 1-2 0-3 0h0s1-1 1-2z" class="E"></path><path d="M325 508c-1 2-3 4-5 5 1-2 4-5 4-8 1-3 0-7-2-10-1-1-2-2-3-2h-1 0c2-1 4-1 5 0 4 2 6 3 8 7l1 1h0v3h0-1c0 1 0 1-1 1-2 2-3 3-5 3z" class="T"></path><path d="M323 493c4 2 6 3 8 7v1c-1 1-2-1-3-2h-1c-1-1-2-2-3-4l-1-1v-1z" class="L"></path><path d="M325 508h0v-1c1-1 1-2 1-3s0-3 1-4c0 1 1 2 1 2 1 1 2 1 3 1l1-2h0v3h0-1c0 1 0 1-1 1-2 2-3 3-5 3z" class="F"></path><path d="M283 474c0-1 0-1 1-2l1-1c5-7 13-13 20-16h1c2 0 5 0 7-1h0c2 1 4 0 6 1-1 0-2 0-2 1h-2-5c-1 0-1 1-2 1-2 1-3 1-4 3-4 0-10 3-12 5l-1 1c0 1-2 2-2 3-1 0-1 1-1 1l-1 1-1 2c1-1 3-2 4-3h2l1-1h0l-5 5c-1 1-2 2-2 3h-1 0-2 0c0-1 1-2 1-3h-1zm73 57v-1c1-3 1-5 2-7l1-2s0-1 1-1h1c1-1 0-3 1-4h0c1 3 0 6 0 8 0 1 0 2-1 3v1 3c0 1-1 1-1 2v1 1c-1 1-1 2-1 2 0 1 0 1-1 2v-1-1-1-2 2h-1v2c-1 2-1 3-2 4l-1 1c-1 2-3 4-4 5h-1c0-3 3-6 4-9 1-2 3-6 3-8z" class="I"></path><defs><linearGradient id="X" x1="389.554" y1="422.152" x2="391.83" y2="426.897" xlink:href="#B"><stop offset="0" stop-color="#969595"></stop><stop offset="1" stop-color="#bebebf"></stop></linearGradient></defs><path fill="url(#X)" d="M397 423c4-2 6-4 8-7 1-1 2-3 3-4 0 5-3 8-6 11-1 1-3 1-4 2-4 1-9 1-13 2-7 1-14 6-18 12-4 5-5 10-7 16 0-2 0-5 1-7 3-7 6-12 11-17 5-4 10-6 17-7 2 0 5-1 8-1h0z"></path><defs><linearGradient id="Y" x1="314.635" y1="442.964" x2="313.944" y2="437.543" xlink:href="#B"><stop offset="0" stop-color="#292829"></stop><stop offset="1" stop-color="#474747"></stop></linearGradient></defs><path fill="url(#Y)" d="M296 447c2-2 3-2 5-3h3l-2-2c0-1-1-1-2-1v-1c1 0 2-1 4-2 8-1 19 0 26 5-3 0-7-2-11 0h-2 0-1-1-1 0-1-1 2 1c2 0 4 0 6 2-6-1-13-2-19 0l-6 2z"></path><path d="M321 351c-1-5 0-11 2-16 1-3 5-8 8-9 4-2 8-1 12 0 2 1 3 2 5 4h-1 0c-3-2-7-4-11-4-3 1-6 3-8 6-4 5-5 13-4 19 1 7 4 13 5 19l-1 1c-3-6-5-13-7-19v-1z" class="K"></path><defs><linearGradient id="Z" x1="314.427" y1="451.215" x2="329.219" y2="469.833" xlink:href="#B"><stop offset="0" stop-color="#111"></stop><stop offset="1" stop-color="#2e2d2e"></stop></linearGradient></defs><path fill="url(#Z)" d="M315 456h2c0-1 1-1 2-1l10 2-1 1h0v1l6 2c2 1 5 1 7 2h2v-1c-1 0-1-1-2-2v-2c1 1 2 1 2 2h0c1 2 1 1 1 3 0 1-1 1-1 1l1 1h-2 0c-2-2-5-2-8-2h-1l1 1h-1-2-1 0c-2 0-4 0-6 1v-1l6-1c-4 0-9 0-13 1-1 0-2 1-4 1 1-1 1-1 0-1 3-3 9-3 13-4-1 0-3 0-4-1-6 0-12 1-18 1 1-2 2-2 4-3 1 0 1-1 2-1h5z"></path><path d="M315 456h2c0-1 1-1 2-1l10 2-1 1h0v1c-2-1-4-1-6-2-3 0-5-1-7-1z" class="G"></path><path d="M356 395s1 0 1-1l1 1-1 2c1 0 2 1 3 1 2 1 5-2 7-3l2-1c1-1 1-1 1-2-1 0-1 0-3-1h0-1v-1c1-1 2-2 3-2h0 2v2h2l1 1-1 1h1l-1 1c-1 1-3 2-4 2-3 2-5 5-6 7-1 0-1 1-2 1 0 2-1 3-2 4h0c-1 1-2 3-2 5-1 4-1 7 0 10h-1-1c0-1-1-2-1-3s-1-3-1-4h1v-2c-2-4-1-9 0-13v2l1-1c0-1 0-2 1-4v-2z" class="N"></path><path d="M356 395c1 1 1 1 1 2v3l1 1c1 0 2-1 3-1 0 2-3 4-3 6 1 0 1-1 1-1 1-1 1-2 2-2h0c0 2-1 3-2 4h0c-1 1-2 3-2 5h-1c-1-1-1-5 0-6v-3-1c0-2 1-4 0-5v-2z" class="E"></path><path d="M356 397c1 1 0 3 0 5v1 3c-1 1-1 5 0 6h1c-1 4-1 7 0 10h-1-1c0-1-1-2-1-3s-1-3-1-4h1v-2c-2-4-1-9 0-13v2l1-1c0-1 0-2 1-4z" class="R"></path><path d="M410 403c1-1 2-1 3-2-1-3-3-6-5-9-5-7-10-14-15-20-3-3-6-5-9-8 3 1 6 3 8 6 5 4 8 8 11 13 6 8 12 17 16 26 6 16 8 31 0 47l-1 2v1c-1 1-2 3-3 3 1-3 3-6 4-9 5-13 4-24 1-37-1-1-2-2-2-4-2-3-3-6-4-9l-1-1-2 2-1-1z" class="F"></path><path d="M385 432h0c-1 1-3 1-4 2v1c-3 2-6 3-9 6h-1l1 1h0c1 0 1 0 1 1-6 5-12 11-13 19-1 4 0 7 0 11-2-2-1-5-1-6s-1-2 0-3v-2h0v-2c-1-1-1-2-1-3v2 2h0v5c-1 1-1 2-1 3v5c-1-1-1-3-2-4 0-1-1-1-1-2l1-1h0l1-7v7l4-22v4l1-1c-1 2-1 5-1 7 2-6 3-11 7-16h1c0 1 1 0 1 0 5-4 10-6 16-7z" class="I"></path><path d="M276 425h1c2-1 3-1 5-1 0 1 0 2-1 2l-1 1v-1 2h-1 0c0 1 1 1 1 3v-1c1 0 1 2 1 3 0 0-1 1-2 1h0 2l1-1h0c-7 4-15 10-20 16 0 1 0 1-1 2 1-3 3-6 5-9-1 0-2 0-2-1 1 0 1-1 1-1l3-3c0-2 3-2 4-4l-1-1c-4 1-7 3-10 6l-1-1c4-5 11-8 16-12z" class="C"></path><path d="M271 432c1 0 1-1 2 0h0c0 2 1 3 1 4l-8 5c0-1 0-1-1-1l3-3c0-2 3-2 4-4l-1-1z" class="S"></path><path d="M276 425v5c0 1 1 2 2 4l-4 2c0-1-1-2-1-4h0c-1-1-1 0-2 0-4 1-7 3-10 6l-1-1c4-5 11-8 16-12z" class="E"></path><path d="M292 496c1-1 2-1 3-1 1-2 2-4 4-5h0c-1 2-2 3-3 5v1l1-1c1 0 2 1 2 1v2c1 0 2-2 3-3 0 1-1 3-1 4 1 0 1 0 1-1h1c0 1 0 1 1 1l2-5c0 3-1 7 1 10l1 1 1 1c-1 1-1 1 0 2 0 1 2 2 4 2h3c1-1 2-1 2-1-2 3-4 4-7 5-2 0-5-1-6-2s-1-3-3-4h-2l-2-2v-3h-1v1c-1-1-1-1-2-1 0-1 0-2 1-3v-3h-1v1c0 1-1 1-2 2l-1-1c-1-1 0-2 0-3z" class="J"></path><path d="M372 374l2-1c1 0 1 2 2 3v6c0 1 0 2-1 2l-1-1-2 2s-1 0-1 1c-1 0-1 1-2 2-1 0-2 1-3 2v1h1 0c2 1 2 1 3 1 0 1 0 1-1 2l-2 1c-2 1-5 4-7 3-1 0-2-1-3-1l1-2-1-1c0 1-1 1-1 1v2c-1 2-1 3-1 4l-1 1v-2c1-4 2-8 4-10s6-4 8-5c4-3 5-6 6-11z" class="D"></path><path d="M371 386c-2 0-3 1-5 2 2-2 4-4 5-6 1-1 1-3 2-3 0 2 1 2 1 4l-2 2s-1 0-1 1z" class="T"></path><path d="M277 343c3-3 6-5 10-7s8-3 11-7c1-1 3-5 3-6h0l-3 4c-1 2-3 3-6 4v-1c1-1 3-1 4-2 2-2 4-6 4-8-2 2-3 5-6 5l-1-1c3-2 5-4 6-6 1 1 1 3 2 4l1 1c2 6-1 12-3 18 2-1 3-3 5-4 2 2 5 3 8 4l-1 1c-5-1-10-1-14 2-2 1-5 4-5 7-1 3 0 6 1 9l2 2-2-2c-2-1-3-4-3-7 0-8 8-14 10-22-6 5-14 6-20 12l-1 1c-1-1-1-1-2-1z" class="K"></path><path d="M381 435c4-1 6-2 10-1l1 1h-1 0l-3 3h-1 1 1c2 1 4 0 5 1h0v1c-9 0-17 4-23 10-5 5-8 10-10 17 0 2-1 5-1 7v-1c0-4-1-7 0-11 1-8 7-14 13-19 0-1 0-1-1-1h0l-1-1h1c3-3 6-4 9-6z" class="W"></path><path d="M372 441h0 1c2-1 5-2 7-3 3 0 7-1 9-2 1 0 1 0 2-1l-3 3h-1 1 1c2 1 4 0 5 1h0c-7 0-14 1-21 4 0-1 0-1-1-1h0l-1-1h1z" class="N"></path><path d="M381 435c4-1 6-2 10-1l1 1h-1 0c-1 1-1 1-2 1-2 1-6 2-9 2-2 1-5 2-7 3h-1 0c3-3 6-4 9-6z" class="B"></path><path d="M311 473h1v1l-6 8c4-3 9-4 14-2 7 1 12 6 15 11 2 4 2 8 1 12l-1 1-1-1v-5 3c-1-1-1-2-1-3-1-4-5-6-8-8h1c-1-1-6-4-6-5 0 0 0-1-1-2-7-1-13-1-20 4l-3 2-1-1c4-5 11-10 16-15z" class="K"></path><path d="M319 483c7 3 12 6 14 13l1 2v3c-1-1-1-2-1-3-1-4-5-6-8-8h1c-1-1-6-4-6-5 0 0 0-1-1-2z" class="R"></path><path d="M346 563v1c0 2-2 4-3 6l4-4 5 2c-3 5-5 10-9 14-5 5-12 8-19 10h0c-8 1-14 1-22-1 2-1 5-1 7-2 3-1 5-4 8-6 2-1 4-3 7-5 6-2 11-6 16-10 2-1 4-3 6-5z" class="D"></path><path d="M330 577h2c1 0 2 2 2 3s0 2-1 3c-1 2-5 2-7 2-1 1-1 0-2 0l-1-3c2-2 4-4 7-5z" class="B"></path><defs><linearGradient id="a" x1="410.709" y1="405.247" x2="456.669" y2="414.067" xlink:href="#B"><stop offset="0" stop-color="#717274"></stop><stop offset="1" stop-color="#979696"></stop></linearGradient></defs><path fill="url(#a)" d="M414 473c-4-3-8-4-13-3-3 0-6 1-9 2l3-3h0c2 1 7-2 9-3 3-1 6-1 8-2l-2 5h0c3-2 4-4 6-6l9-10c21-25 33-58 40-90l5-29c0 22-5 43-12 63-5 17-13 34-23 49-3 4-6 9-9 12l-8 10c-2 2-3 3-4 5z"></path><path d="M349 548h1c1-1 3-3 4-5l1-1c1-1 1-2 2-4v-2h1v-2 2 1 1c-1 1-2 3-2 4 1 1 1 3 0 5l-3 7c-2 1-5 10-6 10v-2h0l-1 1c-2 2-4 4-6 5-5 4-10 8-16 10l-1-1c-5 2-10 5-16 6-2 1-4 1-6 1 2-1 4-2 6-4 2-1 4-2 6-4-5 2-9 2-13 5v-1c8-5 14-14 23-19l6-3h1c4-2 10-4 14-6 2-1 3-3 5-4z" class="D"></path><path d="M329 561c2 0 4 0 5 1h0c-1 2-4 4-5 6-1 0-2 0-3 1h0c-1 0-1 0-1-1h1l-2-2h0c0-1 1-2 2-3l1-1c1-1 2-1 2-1z" class="G"></path><path d="M329 558h1c1 1 5 1 7 1h1 1 0 0l-7 8c-1 3-2 6-5 6h-2c1-1 1-1 2-1 1-1 2-2 2-3 1 0 1-1 1-1h-1c1-2 4-4 5-6h0c-1-1-3-1-5-1h-2 0c-2 1-3 1-4 2h0v-1-1l6-3z" class="H"></path><path d="M356 542c1 1 1 3 0 5l-3 7c-2 1-5 10-6 10v-2h0l-1 1c-2 2-4 4-6 5-5 4-10 8-16 10l-1-1 6-3c10-6 18-13 23-23 2-3 4-6 4-9z" class="O"></path><defs><linearGradient id="b" x1="285.633" y1="455.645" x2="329.368" y2="483.512" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#3c3b3c"></stop></linearGradient></defs><path fill="url(#b)" d="M302 445c6-2 13-1 19 0 5 1 9 3 14 6 3 1 8 5 10 9 1 2 4 3 5 6-1-1-3-2-4-4 0 0 0-1-2-1v-1h-1c0-1-1-1-2-2v2c1 1 1 2 2 2v1h-2c-2-1-5-1-7-2l-6-2v-1h0l1-1-10-2c-2-1-4 0-6-1h0c-2 1-5 1-7 1h-1c-7 3-15 9-20 16l-1 1c-1 1-1 1-1 2-1 1-1 2-2 2h-1c-2 3-4 7-5 10h0c-2-2 0-9 0-11-1-1-1-1-1-2 0-2 0-4 1-6 1-6 9-14 14-17 2-1 5-2 7-3l6-2z"></path><path d="M341 458c-5-4-11-6-17-8-1 0-3-1-5-1v-1l3 1h1 1 1c0 1 1 1 1 1 1 0 2 1 3 1 2 0 4 2 6 3 1 0 2 0 3 1h0 0l-3-3c-1 0-2 0-2-1h-1c-3-1-5-2-8-3h-1c-1 0-2-1-3-1h-1 2c2 0 4 0 6 1 1 1 6 3 8 3 3 1 8 5 10 9 1 2 4 3 5 6-1-1-3-2-4-4 0 0 0-1-2-1v-1h-1c0-1-1-1-2-2z" class="P"></path><path d="M302 445c6-2 13-1 19 0 5 1 9 3 14 6-2 0-7-2-8-3-2-1-4-1-6-1-5-1-11-1-17-1h-2 0v-1z" class="L"></path><path d="M296 447l6-2v1h0 2c-5 2-9 3-13 6-2 1-3 2-4 3-6 5-11 13-12 20-1-1-1-1-1-2 0-2 0-4 1-6 1-6 9-14 14-17 2-1 5-2 7-3z" class="K"></path><path d="M296 447l6-2v1h0 2c-5 2-9 3-13 6 0-2 2-2 3-3s1 0 2-1h2v-1c-3 0-5 3-8 3h-1c2-1 5-2 7-3z" class="Q"></path><path d="M93 458h0c3 3 6 6 8 9 4 5 8 9 12 12 6 5 11 10 17 15 25 17 54 30 84 36 16 3 34 4 50 5h16c2 0 5-1 6-1 1 1 2 4 2 5-9 1-19 1-28 1-44-1-88-13-124-38a210.67 210.67 0 0 1-38-35c-2-3-5-6-5-9z" class="U"></path><path d="M323 411h0c3-2 5-2 7-3 2 0 5 0 6 1h2 1l-1 1v1c5 1 9 3 12 8l3 6v2l2-1v1c1 0 1 0 1 1v-3-3h1v3s0 1 1 1v-2c2 7 2 14 2 21l-4 22v-7l-1 7h0c-2-5-3-10-5-15-1-1-3-2-3-3v-1h-1c-3-6-8-11-12-16-1-2-3-3-4-5 0-1 0-1-1-1l-1-1h0l-5-4h0 0l-1-2h0v2c-2-1-3-3-5-4-1-1-3-2-4-3h0c2 0 3 1 4 2h1l-1-1 1-1c1 1 2 1 2 3h0v1l1-1h-1v-2h0v-1-1c1 0 2-1 3-2z" class="D"></path><path d="M346 439c1-1 1-2 1-3h1c0 1 1 2 2 3h0v-2c2 2 2 6 2 9l-1-3c-2-2-4-3-5-4z" class="C"></path><path d="M333 418h0 1c3 0 5 4 8 4v-1l2 2 2 2v3c-2-3-5-5-8-7-1-1-3-1-4-2v-1c-1 1-2 1-2 0h0 1z" class="H"></path><defs><linearGradient id="c" x1="343.805" y1="429.977" x2="343.716" y2="436.924" xlink:href="#B"><stop offset="0" stop-color="#2d2d2e"></stop><stop offset="1" stop-color="#525151"></stop></linearGradient></defs><path fill="url(#c)" d="M346 439c-2-3-6-6-7-9l1-1h3c4 1 5 5 7 8v2h0c-1-1-2-2-2-3h-1c0 1 0 2-1 3z"></path><path d="M347 418c1 1 1 1 3 1l3 6c-1 0-2-1-3-1h0v1c-1 1-2 0-2 0 1 2 1 5 4 7v6l-6-10v-3l-2-2c-1-1-1-2-1-4 1 1 1 1 2 1v-1h0l2-1z" class="G"></path><path d="M347 418c1 1 1 1 3 1l3 6c-1 0-2-1-3-1h0v1c-1 1-2 0-2 0-1 0-1-1-1-2h0l1 1v-1-1c-1-2-1-2-3-3h0l2-1z" class="C"></path><path d="M357 422v3s0 1 1 1v-2c2 7 2 14 2 21l-4 22v-7-22c-1-4-2-7-3-11l2-1v1c1 0 1 0 1 1v-3-3h1z" class="S"></path><path d="M355 426v1c1 0 1 0 1 1 1 5 1 9 1 14 0 3 0 5-1 7h0c0-3 1-8 0-11-1-4-2-7-3-11l2-1zm-27-1l-5-4h0 0l-1-2h0v2c-2-1-3-3-5-4-1-1-3-2-4-3h0c2 0 3 1 4 2h1l-1-1 1-1c1 1 2 1 2 3h0v1l1-1 1 1v-1c1 0 2 0 3-1 4-1 5 0 8 2h-1l-3-1 3 6c3 7 9 12 13 18 2 3 4 8 5 11-1-1-3-2-3-3v-1h-1c-3-6-8-11-12-16-1-2-3-3-4-5 0-1 0-1-1-1l-1-1h0z" class="J"></path><path d="M328 425l-3-6h0c1-1 1-1 1-2h1c1 1 1 2 2 3v1c-1-1-2-1-2-2h-1c1 2 4 5 5 7s4 5 5 6l8 9c0 1 0 1 1 0 2 3 4 8 5 11-1-1-3-2-3-3v-1h-1c-3-6-8-11-12-16-1-2-3-3-4-5 0-1 0-1-1-1l-1-1z" class="V"></path><path d="M323 411h0c3-2 5-2 7-3 2 0 5 0 6 1h2 1l-1 1v1c5 1 9 3 12 8-2 0-2 0-3-1l-2 1h0v1c-1 0-1 0-2-1 0 2 0 3 1 4l-2-2v1c-3 0-5-4-8-4h-1 0c-3-2-4-3-8-2-1 1-2 1-3 1v1l-1-1h-1v-2h0v-1-1c1 0 2-1 3-2z" class="L"></path><path d="M323 411h0c3-2 5-2 7-3 2 0 5 0 6 1h2 1l-1 1v1c-1 0-2-1-3-1h-3v1h-6c-1 1-1 2-2 2 0 0-1 0-1 1l-1-1c1 0 2-1 2-2h-1z" class="T"></path><path d="M336 409h2 1l-1 1v1c-1 0-2-1-3-1-2-1-4 0-6-1h7z" class="J"></path><path d="M322 418c1-3 2-3 4-4 4-2 7 0 10 1 1 1 3 2 4 3v1l2 2v1c-3 0-5-4-8-4h-1 0c-3-2-4-3-8-2-1 1-2 1-3 1v1z" class="D"></path><path d="M335 410c1 0 2 1 3 1 5 1 9 3 12 8-2 0-2 0-3-1l-2 1h0v1c-1 0-1 0-2-1 0 2 0 3 1 4l-2-2-2-2v-1c-1-1-3-2-4-3h0 0c0-2-2-3-4-4v-1h3z" class="F"></path><path d="M340 414c2 1 3 1 4 2 1 0 2 1 3 2l-2 1c-1-2-3-3-5-5z" class="I"></path><path d="M341 417l-3-4h0c1 0 1 1 2 1 2 2 4 3 5 5h0v1c-1 0-1 0-2-1 0 2 0 3 1 4l-2-2-2-2 1-2z" class="B"></path><path d="M341 417l2 2c0 2 0 3 1 4l-2-2-2-2 1-2z" class="N"></path><path d="M163 183c1-2 1-5 2-7 2-8 5-16 8-23 7-18 18-33 35-43 2-2 5-4 8-5 7-3 14-5 21-7 10-2 20-2 30-2 7 0 14 0 21 2s14 4 21 7c4 2 8 5 12 7 8 7 15 14 21 22 1 3 3 5 4 8h0c-4-2-6-8-9-11-3-2-5-5-8-8-8-7-19-13-30-17-9-3-19-4-28-4-10 0-19 0-28 1-5 1-11 2-15 3-11 4-22 10-31 18-8 7-15 17-19 27l-13 33h0 0l-1 1v5c-1 0-1 2-1 3-1 0 0 0 0 1-1 1-1 1-1 2-1-4 3-10 1-13z" class="S"></path><path d="M391 622v-2h0c-1-1-3-3-3-4-1-3-2-5-3-7l1-1 4 3 7 5 1-1c9 4 21 8 31 7 5-1 10-3 15-4 3-1 6-1 9-1 1 0 4-1 5-1l1 1c2 2 4 4 5 7v1c-2-1-3-3-5-4s-7-2-9-2h-1 1c3 1 5 1 7 4 1 2 2 5 3 8h0c-2-3-4-7-7-8-1 1 1 3 1 4h-1l-1-1-4-5c2 6 4 12 1 17 0 2-1 3-2 4l-1-1c-3 2-7 4-11 5-1 0-2 0-2-1-1 0-5 0-6-1h-1c-3 1-8 2-12 1-3 0-5-2-8-3h0s-1 0-1-1h-3c-3-2-5-4-7-6s-3-4-4-6v-1c1 0 2 0 3 1l1 1c0-1-1-1-1-3 0 0-5-3-6-5 0-1-1-1-2-2 1 0 1-1 2-1l3 3z" class="W"></path><path d="M426 644l7-3c7-3 9-8 12-15 0 4-1 8-2 12 3-1 4-7 5-9 0 4 1 8-2 12-3 2-7 4-11 5-1 0-2 0-2-1-1 0-5 0-6-1h-1zm-35-22v-2h0c-1-1-3-3-3-4-1-3-2-5-3-7l1-1 4 3 7 5c4 2 8 5 12 6 2 0 4 1 6 2h3l1 1c-1 0-3 1-4 1-7 1-13 2-20-2l-4-2z" class="D"></path><path d="M409 622c2 0 4 1 6 2h3l1 1c-1 0-3 1-4 1 0-1 0-1-1-1-4 0-8-1-11-4 2 1 4 2 6 1z" class="J"></path><path d="M391 622v-2h0c-1-1-3-3-3-4-1-3-2-5-3-7l1-1 4 3v2c1 2 4 3 4 5-1 1-2-1-3 0 0 2 3 2 4 4v2l-4-2z" class="C"></path><path d="M388 622c7 6 13 9 22 8 5-1 9-2 14-5v1l-1 1h1l9-2h0l-1 1h0c2 1 4 0 5-1s4-3 4-3l-1 2c1 0 1 1 2 1v1c-2 4-4 7-7 10-1 2-5 4-7 5-1 1-3 2-4 2-2 0-3 1-5 1s-4 0-5 1c-3 0-5-2-8-3h0s-1 0-1-1h-3c-3-2-5-4-7-6s-3-4-4-6v-1c1 0 2 0 3 1l1 1c0-1-1-1-1-3 0 0-5-3-6-5z" class="F"></path><path d="M442 626h0c-3 2-6 2-10 2 3-1 6-3 8-4 1 0 1 1 2 1v1zm-48 1l16 9v1c-5-1-10-4-15-7h0c0-1-1-1-1-3z" class="Q"></path><path d="M410 636c9 2 16 3 25 0-1 2-5 4-7 5-1 1-3 2-4 2-1-1-4-1-5-1h-1c-1-1-1-2-2-3-1 0-2-1-3-1l-3-1v-1z" class="J"></path><path d="M416 639l8 1c1 0 3 1 4 1-1 1-3 2-4 2-1-1-4-1-5-1h-1c-1-1-1-2-2-3z" class="R"></path><path d="M391 629v-1c1 0 2 0 3 1l1 1h0c5 3 10 6 15 7l3 1c1 0 2 1 3 1 1 1 1 2 2 3h1c1 0 4 0 5 1-2 0-3 1-5 1s-4 0-5 1c-3 0-5-2-8-3h0s-1 0-1-1h-3c-3-2-5-4-7-6s-3-4-4-6z" class="K"></path><path d="M407 641h0c-3-1-7-4-10-7h4s0 1 1 1c2 2 5 4 8 5-1 1-1 1-2 1h-1z" class="V"></path><path d="M395 630c5 3 10 6 15 7l3 1c-1 1-1 2-3 2-3-1-6-3-8-5-1 0-1-1-1-1l-3-1-3-3z" class="N"></path><path d="M413 638c1 0 2 1 3 1 1 1 1 2 2 3h1c1 0 4 0 5 1-2 0-3 1-5 1-3 0-6 0-8-1s-3-1-4-2h1c1 0 1 0 2-1 2 0 2-1 3-2z" class="M"></path><path d="M411 643v-1s0-1 1 0h7c1 0 4 0 5 1-2 0-3 1-5 1-3 0-6 0-8-1z" class="I"></path><path d="M339 364c3-4 6-6 10-8 7-2 14-1 21 1 3 2 7 4 10 6l1 1c0 1 0 1-1 1l-1-1v1c1 1 2 1 3 2v2c-1-1-4-2-6-1l-1 1v-1l-2 2h0v3h0c0-1-1-1-1-2v3c-1 5-2 8-6 11-2 1-6 3-8 5s-3 6-4 10-2 9 0 13v2h-1c0 1 1 3 1 4s1 2 1 3h1v3 3c0-1 0-1-1-1v-1l-2 1v-2l-3-6c-3-5-7-7-12-8v-1l1-1h-1-2c-1-1-4-1-6-1-2 1-4 1-7 3h0c-1 1-2 2-3 2v-1c1-1 1-2 2-3h0 0c1-2 2-3 3-3 0-1-1-1-1-1-1 1-3 1-4 2 1-2 2-4 4-6 3-3 6-4 10-4 0 0-1-1-2-1h-1c1 0 2-1 2-1 1 0 1 0 1-1 1-2 1-4 1-6 3 3 5 8 9 12 0 1 1 1 2 2h0l-4-10-3-6h-1l-1-1c0-1 0-2-1-3-2-6-1-14 2-19l1 1z" class="D"></path><path d="M349 376v1l5-7c-1 2-2 4-2 5v1 1h-2l-1 1v-1-1z" class="G"></path><path d="M345 377c0-3 2-5 4-8-1 2-2 5-2 7h0c0 1 0 2-1 3v-1h0l-1-1z" class="B"></path><path d="M375 368c0-1 1-1 1-2-1-1-3-1-5-2 3 0 7-1 9 1l-1-1v1c1 1 2 1 3 2v2c-1-1-4-2-6-1l-1 1v-1z" class="G"></path><path d="M347 376c1-2 2-4 4-5-1 2-2 3-2 5v1 1l1-1h2c0 1-1 2-1 3-2 0-2 1-3 2v1h-1-1v-4c1-1 1-2 1-3z" class="E"></path><path d="M349 377v1l1-1h2c0 1-1 2-1 3-2 0-2 1-3 2 0-1 0-3 1-5zm-6-12h0c-2 6-3 12-2 18h-1v-1c-1-3-1-7-1-10s2-6 4-7z" class="B"></path><path d="M357 379v-2c0-1 2-2 3-2h1l4-2v6h0v1c-2 1-3 2-5 2 1 0 3-1 3-2 1-1 1-2 0-2 0-1-1-2-2-2-1 1-2 2-4 3z" class="F"></path><path d="M352 376l3-4c0 2-1 4-1 6l4-5c-1 2-2 5-3 6-1 3-3 5-5 8 0-3 0-5 1-7 0-1 1-2 1-3v-1z" class="E"></path><path d="M357 379c2-1 3-2 4-3 1 0 2 1 2 2 1 0 1 1 0 2 0 1-2 2-3 2-2 1-4 2-5 2h0c0-1 1-3 2-5z" class="B"></path><path d="M338 363l1 1c-2 5-3 10-2 16 1 2 1 4 2 6h-1l-1-1c0-1 0-2-1-3-2-6-1-14 2-19z" class="L"></path><defs><linearGradient id="d" x1="344.177" y1="389.389" x2="346.291" y2="378.033" xlink:href="#B"><stop offset="0" stop-color="#353334"></stop><stop offset="1" stop-color="#505151"></stop></linearGradient></defs><path fill="url(#d)" d="M345 377l1 1h0v1 4h1 1c-2 4-2 7-2 10 0 2 0 3 1 5h0v1h0l1 1 1 4h0c1 1 1 2 0 3-1-1-2-3-3-5l-4-10c1 0 2 2 3 3 0-6-1-12 0-17v-1z"></path><path d="M333 395c1 0 1 0 1-1 1-2 1-4 1-6 3 3 5 8 9 12 0 1 1 1 2 2h0c1 2 2 4 3 5l3 6c-2-2-3-4-4-6-3-4-8-8-14-10 0 0-1-1-2-1h-1c1 0 2-1 2-1z" class="O"></path><path d="M324 405c3-3 7-3 11-3 2 1 4 0 5 2h0-2v1l1 1v1h-3 0c-1 0-1 1-1 1h-5c-2 1-4 1-7 3h0c-1 1-2 2-3 2v-1c1-1 1-2 2-3h0 0c1-2 2-3 3-3 0-1-1-1-1-1z" class="B"></path><path d="M325 406c4-2 6-2 10-1v1c-2 0-5 0-7 1-2 0-3 1-4 2-1 0-1 0-2 1v-1h0 0c1-2 2-3 3-3z" class="D"></path><path d="M324 405c3-3 7-3 11-3 2 1 4 0 5 2h0c-2 0-3-1-4-1l-1 1 1 1-1 1v-1c-4-1-6-1-10 1 0-1-1-1-1-1z" class="C"></path><path d="M340 404h0c1 1 3 1 4 2l2 1c3 4 5 8 8 12 0 1 1 2 1 3h1v3 3c0-1 0-1-1-1v-1l-2 1v-2l-3-6c-3-5-7-7-12-8v-1l1-1h-1-2c-1-1-4-1-6-1h5s0-1 1-1h0 3v-1l-1-1v-1h2z" class="U"></path><path d="M344 406l2 1c0 1 0 2-1 3-1-1-2-2-2-3l1-1zm-6 3c5 1 9 4 13 8v1c1 0 1 1 1 2 2 1 2 4 3 6l-2 1v-2l-3-6c-3-5-7-7-12-8v-1l1-1h-1z" class="L"></path><path d="M346 407c3 4 5 8 8 12 0 1 1 2 1 3h1v3 3c0-1 0-1-1-1v-1c-1-2-1-5-3-6 0-1 0-2-1-2l1-1h0l-1-1c0-1-3-4-4-4-1-1-1-2-2-2 1-1 1-2 1-3z" class="T"></path><path d="M353 415c-1-2-2-5-2-8v-10l3-8c4-5 11-6 15-11 2-2 3-4 2-7v-1h0l1 1v3c-1 5-2 8-6 11-2 1-6 3-8 5s-3 6-4 10-2 9 0 13v2h-1z" class="K"></path><path d="M165 184l13-33c4-10 11-20 19-27 9-8 20-14 31-18 4-1 10-2 15-3 9-1 18-1 28-1 9 0 19 1 28 4 11 4 22 10 30 17 3 3 5 6 8 8 3 3 5 9 9 11h0 1c0 3 2 5 3 7l5 9c2 4 3 8 4 11l5 16c2 7 4 14 5 21 6 32 7 64 6 97 0 17 0 34-2 51-3-1-7-3-10-4h-8v-1c5 0 10 0 15 1-2-3-6-2-8-4 2 0 4 1 6 1-4-2-9-3-13-4l13 1c-4-2-8-2-12-4l-4-2 8 1c-3-1-9-4-10-7h-1l1-1c-2-5-5-10-11-12l-3-2 10 2c-3-2-6-4-9-5 0-1-1-1-1-2 0-2 0-3 1-4h1c-1-2-5-3-8-4l-6-3c3 1 7 1 11 2-1-1-3-2-4-3-1 0-2 0-4-1-1 0-2-1-3-2l6 2-4-4c-2-1-4-2-5-3l7 3c-3-4-6-5-8-8l5 3c-9-10-20-15-33-17-4-1-9-1-12-2-3-2-5-4-7-6l-5-3c-2-2-5-4-7-4-1 1-1 2-1 3 1 2 2 4 3 7 2 4 3 9 3 14-6 6-14 9-19 16-1 2-2 4-2 5-1 3-1 6-2 9s-3 6-4 9c-2 2-2 5-4 6 0 1 1 2 1 3l3 3c1 2 5 6 8 6 1 1 2 1 4 1 4 1 9 0 13-1 2-1 5-2 7-1 1 0 2 1 2 2 1 1 2 3 2 5 1 0 2-1 2-2 1 0 1 0 2 1l1-1c6-6 14-7 20-12-2 8-10 14-10 22 0 3 1 6 3 7l2 2-2-2c-1-3-2-6-1-9 0-3 3-6 5-7 4-3 9-3 14-2l1-1c2 0 5 1 7 3 1 2 1 4 1 7h1v1c2 6 4 13 7 19l1-1c2 5 4 9 7 12h0c1 1 1 2 1 3l1 1h1l3 6 4 10h0c-1-1-2-1-2-2-4-4-6-9-9-12 0 2 0 4-1 6 0 1 0 1-1 1 0 0-1 1-2 1h1c1 0 2 1 2 1-4 0-7 1-10 4-2 2-3 4-4 6 1-1 3-1 4-2 0 0 1 0 1 1-1 0-2 1-3 3h0 0c-1 1-1 2-2 3v1 1 1h0v2h1l-1 1v-1h0c0-2-1-2-2-3l-1 1 1 1h-1c-1-1-2-2-4-2h0c1 1 3 2 4 3 2 1 3 3 5 4v-2h0l1 2h0 0l5 4v1l-2-1-1 1c-2-1-3-2-5-3h-1-2l-1 1c-7-1-14-3-21-3-4 1-9 1-13 3-2 0-3 0-5 1h-1c-5 4-12 7-16 12l-3 6 1 1v-1 1s0 1 1 1v4 4l2 5c0 1 0 2 1 3 1 2 0 5 1 7 0 1 0 3 1 5v-6h1l1 27 1 11 1 5-19-1c-24-3-44-15-59-34-14-18-21-41-26-63-8-35-11-70-11-105 0-30 1-59 5-88 2-6 3-13 4-20l3-15z" class="W"></path><path d="M254 277l1 1c0 2 0 9-1 12v-1-12z" class="K"></path><path d="M259 453v-1c-1 0-1 0-1-1-1-2-1-5-1-8l1 1v-1 1s0 1 1 1v4 4z" class="L"></path><path d="M255 388c2 1 4 3 6 4 2 2 5 4 8 5l-1 1c-1-1-3 0-5 0h0c-4-1-7-6-8-10z" class="J"></path><path d="M255 388c-2-3-2-6-3-10 4 5 8 9 13 12-1 0-4-1-5-2h-1c1 1 1 2 2 4h0c-2-1-4-3-6-4zm5 34l1-1c2-2 5-5 8-5 1 1 2 1 3 2v1l1 1c1 0 1 0 2 1h-1l-1 2c-1 0-3 1-4 1-4 2-7 5-11 5h-1c1-3 2-5 3-7z" class="D"></path><path d="M267 422v-1c2-3 3-1 5-1h1c1 0 1 0 2 1h-1c-3 2-7 3-11 4 1-1 3-2 4-3h0z" class="U"></path><path d="M260 422l1-1c2-2 5-5 8-5 1 1 2 1 3 2v1l1 1h-1c-2 0-3-2-5 1v1c-1-1-1-1-1-3-1 0-2 0-3 1 0 0 0 1-1 1 0 1-1 1-2 1z" class="R"></path><path d="M269 416c5-1 10 1 15 2 7 0 14 0 22 1 1 1 3 1 5 2 3 0 5 2 8 2h-2l-1 1c-7-1-14-3-21-3h0 2 0 2c1 0 3-1 4 0h2c-2-1-2 0-3 0l-13-1c-4 0-8 1-12 2-1 0-2 1-4 1l1-2h1c-1-1-1-1-2-1l-1-1v-1c-1-1-2-1-3-2z" class="P"></path><path d="M289 420c9-2 19 0 28 3l-1 1c-7-1-14-3-21-3h0 2 0 2c1 0 3-1 4 0h2c-2-1-2 0-3 0l-13-1z" class="L"></path><path d="M261 392c-1-2-1-3-2-4h1c1 1 4 2 5 2 2 0 3 1 5 2 2 0 3 0 5 1 4 1 8 0 11 1h3v-1c1 1 2 1 2 1 1 0 1 0 1-1-1 0-2-1-3-2 1 1 3 1 4 2 5 2 10 4 13 9 1 1 2 3 4 4l8 8-1 1 1 1h-1c-1-1-2-2-4-2h0c1 1 3 2 4 3 2 1 3 3 5 4v-2h0l1 2h0 0l5 4v1l-2-1-1 1c-2-1-3-2-5-3h-1c-3 0-5-2-8-2-2-1-4-1-5-2h0v-1c-2-1-4-1-6-1-1-1-2-1-3-1-2 0-4 0-6-1-10 0-19-2-28-7-7-4-9-10-12-18l3 3c5 6 12 9 20 10l-11-5c2 0 4-1 5 0l1-1c-3-1-6-3-8-5h0z" class="D"></path><path d="M303 405c0-2-2-4-3-6 1 1 3 2 5 3h1c1 1 2 3 4 4l8 8-1 1c-3-2-5-5-7-7-2-1-4-2-7-3z" class="K"></path><path d="M294 404l3 1 10 5h-3c-2-1-3 0-5 0h-9-4 1c1-1 1-3 1-3v-2c-1 0-1 0 0-1h6z" class="G"></path><path d="M291 415h12c2 0 4-1 6 0 1 0 2 1 3 1 3 2 6 3 8 5 2 1 4 3 6 4l-1 1c-2-1-3-2-5-3h-1c-3 0-5-2-8-2-2-1-4-1-5-2h0v-1c-2-1-4-1-6-1-1-1-2-1-3-1-2 0-4 0-6-1z" class="J"></path><path d="M261 392c-1-2-1-3-2-4h1c1 1 4 2 5 2 2 0 3 1 5 2 2 0 3 0 5 1 4 1 8 0 11 1h3v-1c1 1 2 1 2 1 1 0 1 0 1-1-1 0-2-1-3-2 1 1 3 1 4 2 5 2 10 4 13 9h-1c-2-1-4-2-5-3 1 2 3 4 3 6l-5-2-3-1h0c-2 0-4-1-6 0h1 1 2l1 1c1 0 3 1 3 2l-3-1h-6c-1 1-1 1 0 1v2l-2-1s0-1-1-1c0-1-1-1-2 0l1-1 2-1h0c-4-1-8-1-12-2h0l-6-3 1-1c-3-1-6-3-8-5h0z" class="N"></path><path d="M269 397c2 0 4 1 6 2v1l-1 1-6-3 1-1z" class="M"></path><path d="M286 400c3 0 6 1 9 2h0c-2 0-4-1-6 0h1 1 2l1 1c1 0 3 1 3 2l-3-1c-4-2-9-3-14-4h6z" class="P"></path><path d="M296 397l3 3h-2l-1 1v1h0l2 1-3-1c-3-1-6-2-9-2v-1h4c2-1 4 0 6-2z" class="E"></path><path d="M261 392c-1-2-1-3-2-4h1c1 1 4 2 5 2 2 0 3 1 5 2 2 0 3 0 5 1h-2l-1 1c2 1 4 2 6 2-6 0-12 0-17-4z" class="J"></path><path d="M289 391c1 1 3 1 4 2 5 2 10 4 13 9h-1c-2-1-4-2-5-3 1 2 3 4 3 6l-5-2-2-1h0v-1l1-1h2l-3-3c-3-1-6-2-10-3h3v-1c1 1 2 1 2 1 1 0 1 0 1-1-1 0-2-1-3-2z" class="L"></path><path d="M279 344l1-1c6-6 14-7 20-12-2 8-10 14-10 22 0 3 1 6 3 7l2 2-2-2c-1-3-2-6-1-9 0-3 3-6 5-7 4-3 9-3 14-2l1-1c2 0 5 1 7 3 1 2 1 4 1 7h1v1c2 6 4 13 7 19l1-1c2 5 4 9 7 12h0c1 1 1 2 1 3l1 1h1l3 6 4 10h0c-1-1-2-1-2-2-4-4-6-9-9-12 0 2 0 4-1 6 0 1 0 1-1 1 0 0-1 1-2 1h1c1 0 2 1 2 1-4 0-7 1-10 4-2 2-3 4-4 6 1-1 3-1 4-2 0 0 1 0 1 1-1 0-2 1-3 3h0 0c-1 1-1 2-2 3v1 1 1h0v2h1l-1 1v-1h0c0-2-1-2-2-3l-8-8c-2-1-3-3-4-4-3-5-8-7-13-9-1-1-3-1-4-2l-7-1-12-1h-1c-4-2-8-5-10-9-3-6-3-13-1-18v7c1 3 3 6 4 9l-3-9c3 3 4 7 8 9-4-6-5-9-6-16 3 6 4 10 9 15-1-2-2-3-3-5-3-6-2-14 1-21-1 5-1 9 0 13 1 3 2 5 4 7-1-2-2-4-2-6l-1-1c0-4 0-8 1-12l4 13c0-2-1-4-1-6 0-6 2-11 6-15z" class="D"></path><path d="M276 357c2 1 1 7 1 9h0 0c-2-3-2-7-1-9z" class="G"></path><path d="M317 403l2 7c1 1 1 1 1 2v1 1 1c-1-2-2-2-2-4l-3-6c1 1 2 1 2 2v1-2-3z" class="Q"></path><path d="M321 351v1h0c0 3 0 6 1 9-1 1-1 1-2 1-1-1-1-1-2-1h0c2-2 1-5 2-8v-2h1z" class="V"></path><path d="M312 341c2 0 5 1 7 3 1 2 1 4 1 7v2c-1-2-1-4-2-6-2-3-5-4-7-5l1-1z" class="L"></path><path d="M316 369h-1c-3 0-5 2-6 4-2-2-3-4-4-5h10l1 1z" class="J"></path><path d="M311 400c-2-2-3-5-3-7 0-1 0-3 1-4h1v3h1v-1c1-1 2-1 3-1v3l-1 2h0v-3h0 0l-1 3h0v-2h-1v7z" class="H"></path><path d="M314 390h1c0-1 2-2 3-2h0 0c-1 1-2 3-2 4-1 4 0 8 1 11v3 2-1c0-1-1-1-2-2-1-4-1-7-1-12v-3z" class="O"></path><path d="M303 377c0-2-1-4-2-7 1 2 2 4 4 5h1s1 1 1 2c-1 2-2 5-2 8h0-1c-1-1-1-2-2-3v-1c-1-1-1-2-1-3l1-1c0 1 1 1 1 1h0 1l-1-1z" class="J"></path><path d="M294 389l-15-5h8c1 1 2 1 4 1 3 0 7 3 9 5l1 1h-1c-1 1-4-1-6-2z" class="S"></path><path d="M311 400v-7h1v2h0l1-3h0 0v3h0l1-2c0 5 0 8 1 12l3 6c-2-1-4-2-5-5l-2-6z" class="C"></path><path d="M288 382c1 0 2 0 3-1 0 0-3-2-4-2l7 2v-1c-1 0-2-1-3-1v-1c2 0 4 2 6 3 1 1 2 2 3 2 1 1 2 2 3 4l-1-5c1 1 1 2 2 3h1 0c0 3 1 7 1 11-1-1-2-2-2-4-1 0-1-1-1-2s-1-2-2-3c0-1-1-2-1-3-2-1-1 0-2 0-2-1-3-3-5-3v1h0-5z" class="E"></path><path d="M302 382c1 1 1 2 2 3h1v4h-1c-1-1-1-1-1-2l-1-5z" class="T"></path><path d="M270 389v-1c-1-1-2-1-2-2 1-1 8-1 9 0 2 0 3 0 4 2 0 1 0 1 1 2h0l-12-1z" class="G"></path><path d="M300 383l-6-6h1 2 0c0-1 0-2-1-3h0v-3h2 0l3 3c0 1 1 3 2 3h0l1 1h-1 0s-1 0-1-1l-1 1c0 1 0 2 1 3v1l1 5c-1-2-2-3-3-4z" class="S"></path><defs><linearGradient id="e" x1="314.601" y1="361.898" x2="322.88" y2="369.126" xlink:href="#B"><stop offset="0" stop-color="#535254"></stop><stop offset="1" stop-color="#6e6e6e"></stop></linearGradient></defs><path fill="url(#e)" d="M322 361l1 4c1 2 3 4 4 7-4-3-7-6-12-7-2 0-4 0-6-1 2 0 4 0 6-1l1-1 2-1c1 0 1 0 2 1 1 0 1 0 2-1z"></path><path d="M318 361c1 0 1 0 2 1l1 2h0 0c-2-1-4-1-6-1h0l1-1 2-1z" class="H"></path><path d="M329 370c2 5 4 9 7 12h0c1 1 1 2 1 3l1 1h1l3 6 4 10h0c-1-1-2-1-2-2-1-2-2-4-4-6-1-3-3-5-5-8 0-1 0-2-1-3l-6-12 1-1z" class="J"></path><path d="M319 410h1v-2c-2-2-1-7-1-10 0 1 1 3 1 4h1v-1c0-1 1-2 1-2 3-3 8-4 11-4 0 0-1 1-2 1h1c1 0 2 1 2 1-4 0-7 1-10 4-2 2-3 4-4 6 1-1 3-1 4-2 0 0 1 0 1 1-1 0-2 1-3 3h0 0c-1 1-1 2-2 3 0-1 0-1-1-2z" class="R"></path><defs><linearGradient id="f" x1="292.385" y1="386.561" x2="301.131" y2="385.319" xlink:href="#B"><stop offset="0" stop-color="#939292"></stop><stop offset="1" stop-color="#b3b1b1"></stop></linearGradient></defs><path fill="url(#f)" d="M288 382h5 0v-1c2 0 3 2 5 3 1 0 0-1 2 0 0 1 1 2 1 3v1c1 0 1 1 1 2h-2c-2-2-6-5-9-5-3-1-6-3-9-4 2 0 4 0 6 1z"></path><path d="M326 386h0c1 0 2 0 3 1 1 2 1 4 1 7-2 0-3 0-5 1-1 0-2-1-3-1-1-2-1-3-1-5 1-2 3-2 5-3z" class="N"></path><path d="M315 368c5 0 10 2 13 7l4 5c0 1 1 2 2 3s1 2 1 3l-4-4 1 4-7-2h3c-1-1-2-1-3-1h0c1-2 2-4 3-5-1-1-2-3-2-4-3-4-6-4-10-5h0l-1-1z" class="T"></path><defs><linearGradient id="g" x1="294.788" y1="353.107" x2="302.896" y2="351.675" xlink:href="#B"><stop offset="0" stop-color="#131213"></stop><stop offset="1" stop-color="#313131"></stop></linearGradient></defs><path fill="url(#g)" d="M304 359c-2-2-9-6-10-7 0-1 0-2 1-2 1-2 2-3 4-4 2 0 2 0 4 1 1 3 1 6 1 9v3z"></path><defs><linearGradient id="h" x1="302.666" y1="397.115" x2="305.302" y2="395.425" xlink:href="#B"><stop offset="0" stop-color="#787777"></stop><stop offset="1" stop-color="#9c9b9b"></stop></linearGradient></defs><path fill="url(#h)" d="M301 387c1 1 2 2 2 3s0 2 1 2c0 2 1 3 2 4 1 3 2 7 4 10-2-1-3-3-4-4-3-5-8-7-13-9l1-1h1l-1-3c2 1 5 3 6 2h1l-1-1h2c0-1 0-2-1-2v-1z"></path><defs><linearGradient id="i" x1="312.572" y1="380.906" x2="320.447" y2="375.928" xlink:href="#B"><stop offset="0" stop-color="#1a191a"></stop><stop offset="1" stop-color="#504f4f"></stop></linearGradient></defs><path fill="url(#i)" d="M316 373c1 0 2 0 3 1 2 1 3 2 3 4 1 1 0 3 0 4-1 2-3 3-4 3h-3c-1 0-3-2-3-3-1-2-1-3 0-5 0-2 2-3 4-4z"></path><defs><linearGradient id="j" x1="306.393" y1="358.846" x2="314.56" y2="351.773" xlink:href="#B"><stop offset="0" stop-color="#1d1d1d"></stop><stop offset="1" stop-color="#373636"></stop></linearGradient></defs><path fill="url(#j)" d="M304 356c0 1 0 2 1 3 1-1 1-2 1-4 1-2 2-5 5-7 1 0 1 0 2 1 2 2 2 5 2 7 0 1 0 2-1 3v1c1 1 2 1 3 1h1 0l-2 1c-3 0-9 1-12 0 0-1 6 0 8-2-3 0-5 0-8-1v-3z"></path></svg>