<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="61 42 492 620"><!--oldViewBox="0 0 596 752"--><style>.B{fill:#9c9b9c}.C{fill:#4c4c4d}.D{fill:#333}.E{fill:#797879}.F{fill:#8d8c8d}.G{fill:#c8c7c9}.H{fill:#8f8f90}.I{fill:#2b2b2b}.J{fill:#bebcbe}.K{fill:#605f60}.L{fill:#b4b4b4}.M{fill:#7a797a}.N{fill:#1e1e1e}.O{fill:#a8a8a8}.P{fill:#484749}.Q{fill:#1e1d1e}.R{fill:#0d0d0d}.S{fill:#252324}.T{fill:#676668}.U{fill:#515051}.V{fill:#d3d2d4}.W{fill:#dad9da}.X{fill:#b8b6b8}.Y{fill:#434243}.Z{fill:#383738}.a{fill:#6c6c6c}.b{fill:#161516}.c{fill:#ebeaec}.d{fill:#eeeeef}</style><path d="M239 112h0c-1-2-3-3-4-5h1c1 2 2 1 4 2h0c0 2 1 3 2 4-1 0-1-1-2-2v1h-1z" class="c"></path><path d="M377 480c1 3 2 7 2 11l-2-1v-5-1-1h1c-1-1-1-2-1-3z" class="F"></path><path d="M370 455c1 2 2 5 3 8h-2v-1l-2-6h1v-1z" class="D"></path><path d="M118 259v-2c1-3 2-7 3-10v5c0 1-1 3-1 4s-1 3-2 4v-1z" class="V"></path><path d="M100 295v3l-1 1v3l-1 4c-1 1-1 2-2 4 0-5 2-10 4-15z" class="G"></path><path d="M122 253l1 1-1 1v3l-1 2-3 3v-1-3 1c1-1 2-3 2-4l2-3z" class="c"></path><path d="M121 247l2-16v4 4l-2 13v-5z" class="G"></path><path d="M113 449c3 2 4 5 5 8v4l1 2v3l-6-17z" class="N"></path><path d="M376 531l1-6h0v4s0-1 1-1v-4l1-1-4 19c0-4 1-8 1-11z" class="B"></path><path d="M223 96l-5-1c3-2 6-3 10-4v1h-1 1c-1 2-2 2-3 2v1h1c-1 1-1 1-2 1h-1z" class="I"></path><path d="M241 118v-1c2 2 2 3 3 5h0v3c0 1 0 1-1 1h-1v-1h0-1c-1-1-1-2-2-3v-2c-1 0 0 0 0-1v1h1c0 1 0 1 1 2h1v1l1-1h-1c0-2-1-3-1-4z" class="V"></path><path d="M250 120c1 0 2 0 2 1v1c1 1 1 2 2 3v1c0-1 0-2-1-3 0 0 0-1-1-1v-1-1-2h0c1 1 1 2 2 3h0c0 4 4 13 1 15 0-3-1-8-2-11-1-2-2-3-3-5z" class="c"></path><path d="M259 133c-1-3-1-5-2-7v-2l1 1v3s1 0 1 1v1l1 1v-1l-1-2c1 0 1 0 2 1v1 1c0 3 2 5 1 8h-1c0-1-1-3-2-3v-3z" class="G"></path><path d="M372 111c5 3 12 6 19 5h0c-1 1-3 2-5 2s-4-1-6 0l-8-5v-2z" class="C"></path><path d="M269 133c1 0 1 0 2-1l1-1h1c3-1 8-1 11 0-5 1-10 2-14 5-1-1-1-2-1-3z" class="Q"></path><path d="M105 555c-1-1-3-3-4-3-2-1-6-2-7-3 1 0 4 0 6 1 5 2 8 6 10 11 2 4 1 7 0 11h0-1 0c1-6 0-13-4-17z" class="V"></path><path d="M68 646c-2 0-5 0-7-1 1-1 2-2 3-2 2-3 6-3 9-3-1 1-2 2-2 3l-3 3zm37-91c4 4 5 11 4 17h0 1c-1 2-2 3-3 4v-7-1c1-3 0-6-1-8h0l1 1v1-2-1-1c-1-1-1-1-2-3h0z" class="d"></path><path d="M279 189c1 1 5 2 6 3s1 1 2 1c1 1 2 2 4 2l11 7c4 3 7 6 10 9v1c-7-7-15-13-24-17-4-1-8-3-11-4 1 0 1-1 2-2z" class="F"></path><path d="M242 113c-1-1-2-2-2-4h0c2 1 6 4 7 6h0c1 2 2 3 3 5 0 1 1 3 1 4v1-1h-1v-2h0c-1 0-1-1-1-1v1c0 1 0 2 1 3h-1v-1-1h0c-1-1-1-2-2-2 0-1-1-2-2-3l-1-3c-1 0-1-1-2-2z" class="d"></path><path d="M247 121c1 0 1 1 2 2h0v1 1h1c-1-1-1-2-1-3v-1s0 1 1 1h0v2h1v1-1c3 4 3 10 2 15h-1c-2-2-1-5-1-7l-1-2c0-1 0-3-1-4h0l-1-3s0-1-1-1v-1z" class="W"></path><path d="M96 310c1-2 1-3 2-4l-2 13c-1 2-1 5-1 7-1 2-1 3-1 4-1 6-1 12-1 17 0 3 1 5 0 7v-8c0-12 0-25 3-36z" class="J"></path><path d="M273 190c1-1 2-2 4-2v1h2c-1 1-1 2-2 2h-1 0v2c0 1 0 1-1 1 3 3 6 5 9 7a30.44 30.44 0 0 1 8 8c1 1 2 2 3 4h1l-1 1c-1-2-3-4-5-6-1-2-4-4-5-5l-1-1c-3-2-7-5-11-7v1c-1-1-1-3-1-4h0l1-2z" class="B"></path><path d="M277 189h2c-1 1-1 2-2 2h-1v-1-1h1z" class="a"></path><path d="M269 199c-1 0-1 0-2-1l5-6c0 1 0 3 1 4 0 1-1 2-1 3 3 3 6 6 8 9 5 8 8 17 12 25h0c-6-10-9-24-19-31l-3-3h-1z" class="F"></path><path d="M125 182c0 2 1 6-1 9v6 4 5h0l-1 1v9 7h1l-1 1c-1 3 0 8 0 11v-4c0-12-1-24-1-36 1-4 1-9 3-13z" class="O"></path><path d="M134 156l-1 5v1c0 1-1 3-1 3-2 4-3 8-3 11-1 3-2 5-2 8-1 2-2 4-2 7 1 1 0 8-1 10h0v-4-6c2-3 1-7 1-9 2-9 5-17 9-26z" class="B"></path><path d="M402 91v-2h3 8 23c1 1 0 1 0 2h-5-10c-6 0-13 1-19 0h0z" class="N"></path><path d="M116 267h1c0-1 1-1 1-2l1 1-1 1v1h0l-1 1c-8 10-14 21-18 33v-3l1-1v-3c4-10 10-20 16-28zm116-151l1 1h2c1 1 2 2 3 4 0 1 1 2 1 3 1 1 1 2 1 3v1l1 2v4c0 1-1 2-1 2 0 2 0 3 1 5l-2-1s-1-1-1-2v-1c-1-2-1-4-1-6 0-1 0-3-1-3v-2-1c-1-2-2-5-3-7-1 0-1-1-1-2z" class="J"></path><defs><linearGradient id="A" x1="248.116" y1="138.976" x2="239.187" y2="131.274" xlink:href="#B"><stop offset="0" stop-color="#adaeab"></stop><stop offset="1" stop-color="#c7c4cb"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M241 125h1 0v1h1c1 0 1 0 1-1 2 3 2 9 2 13v2l-2 7v-2c-1-1-1-1-1-3h0c-1 0-1 1-1 1-1 0-1-1-1-2-1-2-1-3-1-5 0 0 1-1 1-2h0v3c1 1 0 3 1 4h0c0-2 1-6 0-8v-3l-1-3v-2z"></path><path d="M472 641h25 18 9-3v1h2-33-1l-18 1-4-1h0v-1h-1 2 4z" class="G"></path><path d="M468 641h4 11 20v1c-5 0-10-1-14 0l-18 1-4-1h0v-1h-1 2z" class="K"></path><path d="M253 326l12 14c4 3 8 6 10 10h-2 0-1v1l-1 1c-3-6-8-11-12-16-1-1-1-1-1-3l-5-5v-2z" class="W"></path><g class="F"><path d="M258 333l12 13c1 1 2 2 3 4h-1v1l-1 1c-3-6-8-11-12-16-1-1-1-1-1-3z"></path><path d="M273 350h2l9 9c8 8 17 15 26 22h-3 1v1l-5-4c-1-1-7-5-8-6-7-6-14-13-20-19v-1l-2-2z"></path></g><path d="M288 92c5-1 11-1 16-1 14 0 27 1 39 5h-2c-1 1-1 1-2 1-2-1-5-2-8-2-9-2-19-2-29-2-2-1-5 0-7-1h0-7z" class="N"></path><defs><linearGradient id="C" x1="241.643" y1="125.727" x2="247.557" y2="121.345" xlink:href="#B"><stop offset="0" stop-color="#dad7da"></stop><stop offset="1" stop-color="#fdfdfe"></stop></linearGradient></defs><path fill="url(#C)" d="M239 112h1v-1c1 1 1 2 2 2 1 1 1 2 2 2l1 3c1 1 2 2 2 3v1c1 0 1 1 1 1l1 3c0 3 1 7 0 10v3 2l-1 3h-1v-1-1-2h-1v-2c0-4 0-10-2-13v-3h0c-1-2-1-3-3-5v1h0l-2-3c0-1 0-1 1-1h0c0-1 0-1-1-2z"></path><path d="M246 138h2l-1 5v-1-2h-1v-2z" class="R"></path><path d="M244 125v-3c2 5 5 11 4 16h0-2c0-4 0-10-2-13z" class="Q"></path><defs><linearGradient id="D" x1="112.769" y1="653.438" x2="102.055" y2="637.593" xlink:href="#B"><stop offset="0" stop-color="#49484a"></stop><stop offset="1" stop-color="#807f7f"></stop></linearGradient></defs><path fill="url(#D)" d="M103 643h7c1 0 3 0 5 1h2s1 0 1-1c2 1 8 1 10 3 2 1 13-1 14 1h-4-8-39v-1c2-1 5 0 7-1 1-1 2-1 3-1 0-1 1-1 2-1z"></path><path d="M339 97c1 0 1 0 2-1h2c11 3 20 8 29 15v2c-3-1-6-2-9-4l-6-3c-1 0-2-1-3-1-1-1-2-2-4-2-1 0-2-1-3-1l2-1c1 1 2 1 3 1-1-1-2-2-3-2s-2-1-3-2h1c-1 0-2-1-3 0-1 0-3-1-5-1z" class="S"></path><path d="M264 137v1c1 0 1-1 2-1v-4c-1 0-1 0-1-1v-1c1 1 2 5 4 7l-1 3-1 2c-1 4-3 13-1 17l1 1c-2 0-2-1-3-2v-1l-1-1c-1 1 0 4 0 5-5-8-1-16 1-25z" class="B"></path><path d="M223 225l-1-1 2-1h0v-1c2-1 5 0 7 0 6 0 12-1 17-3 2 0 5-1 7-2 4-3 7-6 11-8h0v1l-3 3h0c0 1-1 1-1 1v1c-1 0-1 0-2 1l-1 1-1 1h-1c-2 2-5 3-8 4l-3 1c-1 1-1 0-2 0 0 1-2 1-3 1s-2 1-3 1h-15z" class="d"></path><path d="M192 103l1-2c8 0 14 0 21 2 4 1 8 3 11 5l3 3 2 2c1 1 2 2 2 3s0 2 1 2c1 2 2 5 3 7v1 2c1 0 1 2 1 3h0v6c-1-1-1-1-1-2-1-1-1-5-2-7-1-3-2-6-4-8-5-9-12-14-22-16-3-1-6-1-8-1h-8z" class="V"></path><defs><linearGradient id="E" x1="375.103" y1="509.82" x2="379.127" y2="510.137" xlink:href="#B"><stop offset="0" stop-color="#7c7b7b"></stop><stop offset="1" stop-color="#939192"></stop></linearGradient></defs><path fill="url(#E)" d="M377 490l2 1c1 10 1 21 0 32l-1 1v4c-1 0-1 1-1 1v-4h0l-1 6v-3-2h-1v-3 2c-1-4-1-9 0-13 0-3 0-9-1-12v-4c-1-2-1-3 0-5 0 3 1 5 1 7v1c1 0 1 1 1 2h0c0-2 1-3 1-5 1-2 0-4 0-6z"></path><path d="M208 104c10 2 17 7 22 16 2 2 3 5 4 8 1 2 1 6 2 7v2h-1l-2-1c0-2-1-3-1-5l-1 1c-1 0-1-2-2-3h0c-2-4-3-7-6-11l-1-2c-2-2-3-4-5-5-1-1-2-1-3-1-2-2-4-3-6-4l2-1h-3l1-1z"></path><path d="M218 109c2 2 5 5 6 7h-1-1c-2-2-3-4-5-5 0-1 0-1 1-2z" class="W"></path><path d="M210 105c3 1 6 2 8 4-1 1-1 1-1 2-1-1-2-1-3-1-2-2-4-3-6-4l2-1z" class="V"></path><path d="M224 116c4 4 7 10 8 15l-1 1c-1 0-1-2-2-3h0c-2-4-3-7-6-11l-1-2h1 1z" class="J"></path><defs><linearGradient id="F" x1="332.63" y1="407.077" x2="335.494" y2="404.364" xlink:href="#B"><stop offset="0" stop-color="#797978"></stop><stop offset="1" stop-color="#979696"></stop></linearGradient></defs><path fill="url(#F)" d="M308 382v-1h-1 3l16 13c13 12 25 25 34 39 2 3 6 8 6 12v1c-2-2-3-6-5-8-5-7-10-14-16-21-7-8-14-16-22-23-5-4-11-7-15-12h0z"></path><path d="M268 141h2c1 0 2-1 2-1h1c1 0 1-1 1-1h1l7-2h4c2-1 4-1 7-1 11-2 23-1 34 2 22 7 47 23 59 44 1 3 3 6 4 9 5 11 8 22 9 33 1 3 1 5 1 7 1 2 1 5 1 7h0c-1 0-1 0-1-1v-2l-1-7c-1-7-3-16-5-23-7-22-20-39-38-52-9-6-19-11-29-14-7-2-15-2-22-2-9 0-18 0-26 2-4 1-8 3-12 4l1-2z" class="W"></path><path d="M103 639l6 2 9 2c0 1-1 1-1 1h-2c-2-1-4-1-5-1h-7c-1 0-2 0-2 1-1 0-2 0-3 1-2 1-5 0-7 1v1l-17-1h-6l3-3c0-1 1-2 2-3h8c7-1 13-1 19 0 2 0 2 0 3-1z" class="X"></path><path d="M109 641l9 2c0 1-1 1-1 1h-2c-2-1-4-1-5-1h-7-13l-8-1c-3 0-5 1-7 1 1-1 1-1 2-1h1c1-1 3-1 4-1h4c4 0 7 1 10 1h9 4v-1z" class="L"></path><defs><linearGradient id="G" x1="82.77" y1="648.736" x2="78.888" y2="641.386" xlink:href="#B"><stop offset="0" stop-color="#858384"></stop><stop offset="1" stop-color="#a8a8a8"></stop></linearGradient></defs><path fill="url(#G)" d="M75 643c2 0 4-1 7-1l8 1h13c-1 0-2 0-2 1-1 0-2 0-3 1-2 1-5 0-7 1v1l-17-1c-1 0-1 0-1-1v-1c1-1 1-1 2-1z"></path><path d="M281 174h0c5 1 11 0 16 0 9 0 20 0 29 3 1 0 3 0 5 1-1 1-13-2-15-2-12-1-24-1-35 0l-1 5h4l9 1c9 0 19 3 27 6 2 1 4 1 5 2-5-1-9-3-13-4-3-1-7-2-10-3-1 1-2 1-2 1h-3-2-1c7 1 13 2 20 4h-3c-10-3-22-4-32-3v1c3 1 5 1 8 2 4 2 7 3 11 5 3 1 6 3 9 4 0 1 0 1 1 1l6 3c2 1 4 3 6 4h-1c-1 0-2-1-3-2h-1l-16-8s0 1-1 1h0-2v-1h-2l-1-1h-2c0-1 0-1-1-1h0-3c-1 0-1 0-2-1s-5-2-6-3h-2v-1c-2 0-3 1-4 2l2-4c2-3 4-8 4-11h0c1 0 1 0 1-1h1z" class="B"></path><path d="M294 184c-5 0-10 0-14-1l-1-1h1c7-1 14 0 22 1-1 1-2 1-2 1h-3-2-1zm-17 4c1-1 2-1 3 0h1c4 1 7 2 10 3 2 1 3 2 4 2 2 1 3 1 4 2 0 0 0 1-1 1h0-2v-1h-2l-1-1h-2c0-1 0-1-1-1h0-3c-1 0-1 0-2-1s-5-2-6-3h-2v-1z" class="c"></path><path d="M222 219c3-1 7 0 10 0 5 0 11 0 15-2 5-1 9-4 13-7 1-1 3-2 5-3h0c1 1 1 1 1 2h0c-4 2-7 5-11 8-2 1-5 2-7 2-5 2-11 3-17 3-2 0-5-1-7 0v1h0l-2 1 1 1c0 3-1 6-1 9 0 7-1 14 0 21 0 6 1 12 2 18 1 2 2 5 2 7 2 6 4 11 7 17 2 3 4 6 5 10-1-1-2-2-2-3-1-2-3-4-4-5-2-3-3-6-5-8l-2-6c0-3-1-7-3-10l-3-18c-1-5-3-11-1-17v-3c-1-4 0-13 1-17h1v-1h-4c2-1 4-1 6 0h0z" class="V"></path><path d="M216 219c2-1 4-1 6 0h0c-4 5-4 13-4 18-1-4 0-13 1-17h1v-1h-4z" class="E"></path><path d="M219 257c-1-5-3-11-1-17v3 1c1 1 1 3 1 4 0 3 0 7 1 11v1c1 5 2 12 4 17 0 1 0 1 1 2v1-1c0 1 1 1 1 1 2 6 4 11 7 17 2 3 4 6 5 10-1-1-2-2-2-3-1-2-3-4-4-5-2-3-3-6-5-8l-2-6c0-3-1-7-3-10l-3-18z" class="G"></path><path d="M246 75c1-2 2-3 3-4l-1 19c-1 4 0 10-1 14v2 2c-1-2-3-3-5-3l-11-6-8-3h1c1 0 1 0 2-1h-1v-1c1 0 2 0 3-2h-1 1v-1c6-5 13-10 18-16z" class="E"></path><path d="M246 75c1 1 0 3-1 4 0 1-1 2-2 3-5 4-10 7-15 10v-1c6-5 13-10 18-16z"></path><path d="M242 93c1 0 2-1 3-2v1 2c-1 1-4 3-5 4h2 1c1-1 1-1 1-2h1c1 1 0 3 1 5v2l1 1v2 2c-1-2-3-3-5-3l-11-6h1v-1c1-1 4-1 6-2 1-1 3-2 4-3z" class="C"></path><path d="M243 98c1-1 1-1 1-2h1c1 1 0 3 1 5v2l1 1v2 2c-1-2-3-3-5-3l3-3-1-1h0c0 1-1 1-1 1h-2l1-1 2-1v-2h-1z" class="a"></path><g class="R"><path d="M242 93c1 0 2-1 3-2v1 2c-1 1-4 3-5 4h-1 0c1 1 2 1 2 1 1 1 0 2 0 3l-6-2c0-1-2-1-3-1v-1c1-1 4-1 6-2 1-1 3-2 4-3z"></path><path d="M237 88c3-2 6-5 9-7-1 2 0 6-1 8l-3 3-1 1h1c-1 1-3 2-4 3-2 1-5 1-6 2v1h-1l-8-3h1c1 0 1 0 2-1h-1v-1c1 0 2 0 3-2 3-1 6-2 9-4z"></path></g><path d="M237 88h0c-2 2-4 3-6 4h1l2-1c1 0 2 0 3-1-1 1-1 1-2 1l-6 3-3 1h-1v-1c1 0 2 0 3-2 3-1 6-2 9-4z" class="N"></path><path d="M237 90h0c2 0 5-3 6-3v1l-2 2-4 2c-1 1-3 2-4 2s-2 1-3 1l-2 1v1c2 1 7-1 9-2l4-2h1c-1 1-3 2-4 3-2 1-5 1-6 2v1h-1l-8-3h1c1 0 1 0 2-1l3-1 6-3c1 0 1 0 2-1z" class="D"></path><defs><linearGradient id="H" x1="356.134" y1="604.744" x2="378.609" y2="560.126" xlink:href="#B"><stop offset="0" stop-color="#b7b6b7"></stop><stop offset="1" stop-color="#d6d6d5"></stop></linearGradient></defs><path fill="url(#H)" d="M342 601c9-5 16-10 23-17 15-14 25-30 31-49 1-3 2-6 2-10 1-1 1-4 2-6v1 1l-1 6-1 1v2c-1 2-1 4-1 5 0 0-1 1-1 2h0c0 1-1 3-1 4h0c-1 1-1 2-1 2 0 1-1 2-1 2 0 1 0 1-1 2v1 1l-1 2c-1 0-1 1-1 1 0 1-1 2-1 3l-1 1h0c0 1-1 2-1 2-1 2-2 3-3 4v1h1c0 1 0 2-1 3h0v2h0v2c2-3 5-6 6-9 1-1 1-2 2-3h0c0-1 0-1 1-2v-2h0l2-3h0v-1-1l1-1c0-1 0-1 1-1v-1c0-1 0-2 1-3v-2h0c1-1 1-2 1-3 1 0 0 0 0-1h1v-2l1-1v-2-1h0v-1c0-1 1-1 1-2h0v-1-2c1-2 0-3 0-5h1c0-2 0-3 1-4-2 16-5 31-13 45-4 8-10 15-16 21-4 4-7 8-11 11-3 3-7 6-11 8-2 2-4 3-7 4-2 2-5 3-7 4h0 0c-1 0-1 0-1-1h0l3-2c-1-1-3 0-5 0h-3l2-1 1-1 2-1h1c1-1 2-2 3-2z"></path><g class="B"><path d="M339 603c4 0 7-1 10-3v1c1 0 1 0 1 1h1c0-1 1-1 2-1-2 2-4 3-7 4h0l-2 1 1-1v-1s0-1-1-1l-2 1h-6l2-1h1z"></path><path d="M336 604h6l2-1c1 0 1 1 1 1v1l-1 1 2-1h0c-2 2-5 3-7 4h0 0c-1 0-1 0-1-1h0l3-2c-1-1-3 0-5 0h-3l2-1 1-1z"></path></g><path d="M335 605c4 1 6-1 9-1v1c-1 0-2 1-3 1-1-1-3 0-5 0h-3l2-1z" class="H"></path><defs><linearGradient id="I" x1="431.562" y1="644.874" x2="431.696" y2="639.446" xlink:href="#B"><stop offset="0" stop-color="#8a898a"></stop><stop offset="1" stop-color="#a6a5a5"></stop></linearGradient></defs><path fill="url(#I)" d="M365 640l10-2 1 1h8 17c2 0 6-1 9 0l46 1c3 0 8 0 12 1h-2 1v1h0l4 1c-17 2-35 2-51 2-9 0-19 1-27 0h-7c-2-2-16-2-19-2-1 0-2-1-4-1h3c-1-1-1-1-1-2z"></path><path d="M365 640l10-2 1 1h8 17c2 0 6-1 9 0h-20c-4 0-9 0-14 1-2 0-5 0-7 1l-3 1c-1-1-1-1-1-2z" class="G"></path><path d="M369 641c3 0 7 1 10 1 7 0 12 0 19 1-1 1 0 1-1 1h2c-1 1-5 1-6 1h-7c-2-2-16-2-19-2-1 0-2-1-4-1h3l3-1z" class="T"></path><defs><linearGradient id="J" x1="229.772" y1="208.618" x2="231.896" y2="218.323" xlink:href="#B"><stop offset="0" stop-color="#898889"></stop><stop offset="1" stop-color="#a6a4a5"></stop></linearGradient></defs><path fill="url(#J)" d="M250 204c5-2 9-5 14-8 1 2 3 3 5 5h0c-1 2 0 4-1 5h-1-1c1 3 2 6 3 10 0 1 0 2 1 3v4 3 7c-1 2 0 4-1 6-1-1 0-2 0-3v-10c0-2 0-3-1-5l-2-11v-1c0-1 0-1-1-2h0c-2 1-4 2-5 3-4 3-8 6-13 7-4 2-10 2-15 2-3 0-7-1-10 0h0c-2-1-4-1-6 0-3 1-9 0-13-1h-2l-4-1h0v-1h2c3 0 7 1 10 1 5 1 14-1 18-3l5-1v-1h2v-1h2c1 0 1-1 1-2 0 0-1 0-2 1h-1c-2 0-3 0-4-1l1-1h-1c2-1 3-1 4-2s2-1 3-1 2-1 2-2l2-1c1 2 2 2 4 2l2-1 1 1-1 1h2 0l1-1z"></path><defs><linearGradient id="K" x1="245.08" y1="200.702" x2="259.085" y2="213.316" xlink:href="#B"><stop offset="0" stop-color="#8d8b8b"></stop><stop offset="1" stop-color="#aaaaab"></stop></linearGradient></defs><path fill="url(#K)" d="M250 204c5-2 9-5 14-8 1 2 3 3 5 5h0c-1 2 0 4-1 5h-1-1 0l-1-3h0c0-1-1-2-1-3h-1c-2 2-6 5-9 6-2 1-5 3-8 4v1h0-2c0 1 0 1-1 1-2 1-9 1-11 1v-1h2v-1h2c1 0 1-1 1-2 0 0-1 0-2 1h-1c-2 0-3 0-4-1l1-1h-1c2-1 3-1 4-2s2-1 3-1 2-1 2-2l2-1c1 2 2 2 4 2l2-1 1 1-1 1h2 0l1-1z"></path><path d="M241 202c1 2 2 2 4 2l2-1 1 1-1 1h2c-1 1-2 1-3 1l-1 1h-1l-3 1-4 1h0s-1 0-2 1h-1c-2 0-3 0-4-1l1-1h-1c2-1 3-1 4-2s2-1 3-1 2-1 2-2l2-1z" class="B"></path><path d="M241 202c1 2 2 2 4 2l2-1 1 1-1 1-7 1c-2 1-7 0-9 2h-1c2-1 3-1 4-2s2-1 3-1 2-1 2-2l2-1z"></path><path d="M309 319v-1c2-1 5 0 8 0h17 44l71-1h38 60c0 1-1 1-1 2h-8-4c-1 0-1 0-1 1h-13c-3 0-6 1-9 0l-7 1v-1h-4c-6 0-12 1-18 1-5-1-10-2-15-2-1 0-2 1-3 1-1 1-3 0-4 0s-1-1-2-1h-5-10-15c-4 0-8 1-12 1-3 0-7-1-11-1-5 1-9 1-13 2h-14c-2 0-4-1-6 0h0c-4-1-7-1-11-1s-8 1-13 1c-6 0-12 0-18-1-3 0-5-1-8-1s-9 1-11 0c2 0 5 1 7 0 1 0 4-1 6 0h3c2 1 3 0 4 1h5 9 0l-1-1h-1c-6-1-13 0-19 0-4 0-9-1-14 0h-1 0z" class="S"></path><path d="M500 320c4-1 8-1 11-1l-2 1h2l-7 1v-1h-4z" class="R"></path><path d="M192 103h8c2 0 5 0 8 1l-1 1h3l-2 1c2 1 4 2 6 4 1 0 2 0 3 1 2 1 3 3 5 5l1 2c3 4 4 7 6 11h0c1 1 1 3 2 3l1-1c0 2 1 3 1 5-2-1-4-1-6-1l-1-1h0c-2-1-3-2-4-3-1 0-1 0-1 1-5-4-10-7-15-9h0l-1-1c-1-1-2-1-3-1s-1 0-2-1h-2v-1c-2-1-5 0-6-2h2c-4-1-7-1-11-1h-3-1v-1h3c0-1 0-1-1 0h-1l-1-1h-4c1-1 2-1 3-2h-4c1-1 3-1 4-2 1 0 2-1 3-2l1-1h2c3 0 3-1 5-3h0l1 1c1-1 2-1 2-2z" class="B"></path><path d="M187 112v-2h3c3 1 7 0 10 0 0 1 1 1 1 1 6 0 12 3 16 7v1c3 2 5 5 7 8l-6-6c-6-4-15-8-22-8-3 0-8 1-11-1h2z"></path><path d="M187 112v-2h3c3 1 7 0 10 0 0 1 1 1 1 1-1 0-1 0-1 1-4 0-9 1-13 0z" class="H"></path><path d="M201 111c6 0 12 3 16 7v1c-6-4-11-6-17-7 0-1 0-1 1-1z" class="F"></path><path d="M211 109c1 0 2 1 3 1s2 0 3 1c2 1 3 3 5 5l1 2c3 4 4 7 6 11-2 0-5-5-7-7-1-2-3-3-5-4-4-4-10-7-16-7 0 0-1 0-1-1-3 0-7 1-10 0 6-2 13-1 20 0l1-1z"></path><path d="M211 109c1 0 2 1 3 1s2 0 3 1c2 1 3 3 5 5l1 2c-4-4-8-6-13-8l1-1z" class="F"></path><defs><linearGradient id="L" x1="196.012" y1="115.857" x2="189.571" y2="105.557" xlink:href="#B"><stop offset="0" stop-color="#9c989a"></stop><stop offset="1" stop-color="#d7d7d8"></stop></linearGradient></defs><path fill="url(#L)" d="M192 103h8c2 0 5 0 8 1l-1 1h3l-2 1c2 1 4 2 6 4-1 0-2-1-3-1l-1 1c-7-1-14-2-20 0h-3v2h-2c-2 0-4 1-6 2h-4c1-1 2-1 3-2h-4c1-1 3-1 4-2 1 0 2-1 3-2l1-1h2c3 0 3-1 5-3h0l1 1c1-1 2-1 2-2z"></path><path d="M178 110h5c-2 1-3 2-5 2h-4c1-1 3-1 4-2z" class="L"></path><path d="M182 107h2c3 0 3-1 5-3h0l1 1-2 2c-2 1-3 1-5 3h-5c1 0 2-1 3-2l1-1z" class="O"></path><path d="M192 103h8c2 0 5 0 8 1l-1 1h3l-2 1c2 1 4 2 6 4-1 0-2-1-3-1-6-2-11-2-17-2-2 0-3-1-6 0l2-2c1-1 2-1 2-2z" class="b"></path><path d="M208 106c-3 0-5-1-8-1s-6 1-8 1h0c4-2 10-3 15-1h3l-2 1z" class="J"></path><path d="M182 115c12-2 25 0 36 8 4 3 7 7 10 11l-1 1-1-1h0c-2-1-3-2-4-3-1 0-1 0-1 1-5-4-10-7-15-9h0l-1-1c-1-1-2-1-3-1s-1 0-2-1h-2v-1c-2-1-5 0-6-2h2c-4-1-7-1-11-1h-3-1v-1h3z"></path><path d="M194 117c3 0 6 1 8 2 7 2 15 6 20 12h0c-1 0-1 0-1 1-5-4-10-7-15-9h0l-1-1c-1-1-2-1-3-1s-1 0-2-1h-2v-1c-2-1-5 0-6-2h2z" class="O"></path><path d="M271 352l1-1v-1h1 0l2 2v1c6 6 13 13 20 19 1 1 7 5 8 6l5 4h0c4 5 10 8 15 12 8 7 15 15 22 23 6 7 11 14 16 21 2 2 3 6 5 8l4 9v1h-1l2 6v1l-1-1c2 5 3 11 4 17 1 2 2 4 2 6-1 0-5-13-6-16-6-14-15-29-26-39l-4-5s-1 0-1-1c-3-5-5-10-8-15-3-4-7-7-11-10-9-6-18-10-26-17-3-2-5-4-7-6-1-2-3-5-5-6l-3-3v1h-1c-1 0-1 0-2 1 0 0-1-1-1-2l-3-6h1c0-1 0-2-1-3h1l-2-6z"></path><path d="M320 399h0v-1c3 2 6 4 9 7 5 5 9 13 11 20 0 0-1 0-1-1-3-5-5-10-8-15-3-4-7-7-11-10z" class="F"></path><path d="M271 352l1-1v-1h1 0l2 2v1c6 6 13 13 20 19 1 1 7 5 8 6l5 4h0c4 5 10 8 15 12 8 7 15 15 22 23 6 7 11 14 16 21 2 2 3 6 5 8l4 9v1h-1l2 6v1l-1-1c-1-3-3-6-4-9-5-10-11-19-17-28l-9-10c-7-8-14-15-22-22-9-7-19-12-28-20-7-6-11-13-17-20 1 5 4 9 7 13l3 3c3 3 5 7 8 9 5 5 10 8 16 11 4 3 9 5 13 9v1h0c-9-6-18-10-26-17-3-2-5-4-7-6-1-2-3-5-5-6l-3-3v1h-1c-1 0-1 0-2 1 0 0-1-1-1-2l-3-6h1c0-1 0-2-1-3h1l-2-6z" class="E"></path><path d="M302 380h-1c-1-1-3-2-4-3-9-6-16-14-22-24h0c6 6 13 13 20 19 1 1 7 5 8 6 0 1 0 1 1 2 1 0 2 1 2 2-1-1-3-2-4-2z"></path><path d="M303 378l5 4h0c4 5 10 8 15 12 8 7 15 15 22 23 6 7 11 14 16 21 2 2 3 6 5 8l4 9v1h-1c-3-8-7-14-12-21-2-4-5-7-7-11-3-3-6-6-9-10a188.83 188.83 0 0 0-20-20c-6-5-13-9-19-14 1 0 3 1 4 2 0-1-1-2-2-2-1-1-1-1-1-2z" class="R"></path><defs><linearGradient id="M" x1="140.181" y1="214.515" x2="116.319" y2="223.428" xlink:href="#B"><stop offset="0" stop-color="#7d7e7d"></stop><stop offset="1" stop-color="#a4a2a3"></stop></linearGradient></defs><path fill="url(#M)" d="M133 162h1v7l1 1h4-1c-1 2-3 5-3 7h0l2-2c1-2 3-5 6-5 0 2 2 2 3 3s1 2 1 2c1 2 1 3 1 5-1 3-3 6-5 9h0c1 1 2 1 2 2h1c-1 1-2 0-2 0-2 1-6 13-7 15-2 5-4 10-3 16h-1v4c1 10 1 20-6 28l-4 5c-1 1-2 1-2 2v-1l1-2v-3l1-1-1-1-2 3c0-1 1-3 1-4l2-13v-4c0-3-1-8 0-11l1-1h-1v-7-9l1-1h0v-5h0c1-2 2-9 1-10 0-3 1-5 2-7 0-3 1-5 2-8 0-3 1-7 3-11 0 0 1-2 1-3z"></path><path d="M123 239c0-1 0-2 1-2 0 1 0 2-1 3 0 1 1 2 0 3 1-1 1-1 1-2v-1c1 0 1 1 1 1l-2 6c0 3-1 4-1 6l-2 3c0-1 1-3 1-4l2-13z" class="B"></path><path d="M123 247l1-1 1 1s1 1 2 1l-1 1c0 1-3 5-3 6l1 1-2 2v-3l1-1-1-1c0-2 1-3 1-6z" class="G"></path><path d="M125 241v-1c1 0 2 1 3 1h0c0 2-1 6-1 7-1 0-2-1-2-1l-1-1-1 1 2-6z" class="L"></path><path d="M132 221c1 1 0 4 1 5 1 10 1 20-6 28l-4 5c-1 1-2 1-2 2v-1l1-2 2-2c8-9 7-24 8-35z"></path><path d="M133 162h1v7l1 1h4-1c-1 2-3 5-3 7h0c0 1 0 2-1 3-1 2-2 3-3 5 0 1-1 2-1 3h-1c1-2 1-3 2-5v-1l-1-1-1 3c0 2-1 5-2 8v-7-1c0-3 1-5 2-8 0-3 1-7 3-11 0 0 1-2 1-3z" class="K"></path><path d="M133 162h1v7h-1c-1 3-1 6-3 9v-3c2-3 2-6 2-10 0 0 1-2 1-3z" class="D"></path><path d="M130 178c2-3 2-6 3-9h1l1 1h4-1c-1 0-2 0-2 1-1 1-2 3-2 4-2 2-3 4-4 6l-1 3v-1l1-5z" class="R"></path><path d="M130 181c1-2 2-4 4-6 0-1 1-3 2-4 0-1 1-1 2-1-1 2-3 5-3 7h0c0 1 0 2-1 3-1 2-2 3-3 5 0 1-1 2-1 3h-1c1-2 1-3 2-5v-1l-1-1z" class="K"></path><path d="M135 177l2-2c1-2 3-5 6-5 0 2 2 2 3 3s1 2 1 2c1 2 1 3 1 5-1 3-3 6-5 9h0c1 1 2 1 2 2h1c-1 1-2 0-2 0-2 1-6 13-7 15-2 5-4 10-3 16h-1v4c-1-1 0-4-1-5v-5-4-10c1-9 5-17 10-25h-2c-3 4-5 10-7 15 0 1-1 2-1 3v-3s0-2-1-2v-1c1-1 1-2 0-4 1-2 2-3 3-5 1-1 1-2 1-3z" class="E"></path><path d="M132 192c1-5 3-10 6-15 1-1 2-3 4-4 1-1 1 0 2 0h1c-2 1-3 2-5 4-3 4-5 10-7 15 0 1-1 2-1 3v-3z"></path><path d="M142 177l1-1c2 0 2 0 3 1 0 2 0 3-1 4-1 3-3 6-4 8l-6 12c-1 2-2 4-2 6s0 3-1 5h0v-10c1-9 5-17 10-25z"></path><path d="M250 120h0c1 2 2 3 3 5 1 3 2 8 2 11 3-2-1-11-1-15l2 5v-1-2h0 1v1 2c1 2 1 4 2 7v3c1 0 2 2 2 3h1c1-3-1-5-1-8v-1c1 3 2 5 3 7-2 9-6 17-1 25 0-1-1-4 0-5l1 1v1c1 1 1 2 3 2 4 2 12 8 14 12v1h-1c0 1 0 1-1 1h0c0 3-2 8-4 11l-2 4-1 2h0l-5 6c1 1 1 1 2 1 6 8 13 16 14 26h0c-1-1-1-1-1-2-2-8-7-17-13-22-2-2-4-3-5-5-5 3-9 6-14 8l-1 1h0-2l1-1-1-1-2 1c-2 0-3 0-4-2 0 0 1 0 2-1 4-1 8-3 12-5 1-1 3-2 4-4h0l-2-1c-2 0-4-1-6-1h-2l-1-1v-6c3 1 8-2 10-3v-1c0-2-3-4-4-5h0c-2-3-4-5-5-8v-2c-2-2-2-4-3-6 0-1-1-2-1-3h0l-1-3v-5l2-7h1v2 1 1h1l1-3v-2-3c1-3 0-7 0-10h0c1 1 1 3 1 4l1 2c0 2-1 5 1 7h1c1-5 1-11-2-15 0-1-1-3-1-4z" class="R"></path><path d="M272 172v-1c2 0 2 1 3 2 0 1-1 1-2 2 0-1-1-2-1-3zm-4 1l1 2c0 1-1 1-2 1h0l-2-2c1 0 2-1 3-1z" class="d"></path><path d="M272 192c-1-1-1 0-2 0v-1h1c-2 0-2-1-3-2-2-1-2-2-2-4l-1 1v-1l3-3v1c0 1-1 1-1 2v2c0 1 1 2 2 3h3c0-1 0-1 1-2v-6h1v-2h0 1l-1 5 1 1-2 4-1 2z" class="b"></path><path d="M265 165c1 0 2-1 3-1h1v1c2 3 5 5 7 8h-1c-1-1-1-2-3-2v1c-1 0-1-1-2-1-2-2-3-4-5-6z" class="G"></path><defs><linearGradient id="N" x1="261.093" y1="161.712" x2="263.568" y2="174.138" xlink:href="#B"><stop offset="0" stop-color="#bebcbd"></stop><stop offset="1" stop-color="#f1f1f2"></stop></linearGradient></defs><path fill="url(#N)" d="M258 161c3 5 7 8 10 12-1 0-2 1-3 1-2-3-5-5-7-9v-4z"></path><path d="M263 162c0-1-1-4 0-5l1 1v1c1 1 1 2 3 2 4 2 12 8 14 12v1h-1c0 1 0 1-1 1h0v-1c-1 0-2-1-3-1-2-3-5-5-7-8v-1h-1c-1 0-2 1-3 1l-2-3z" class="L"></path><path d="M269 164c3 2 9 6 10 9v1c-1 0-2-1-3-1-2-3-5-5-7-8v-1z" class="d"></path><defs><linearGradient id="O" x1="244.084" y1="196.103" x2="260.767" y2="200.455" xlink:href="#B"><stop offset="0" stop-color="#818080"></stop><stop offset="1" stop-color="#a9a7a9"></stop></linearGradient></defs><path fill="url(#O)" d="M243 201h0c4 1 8-1 11-3l5-4s1-1 2-1h1c0 1 1 1 1 3-2 1-4 3-7 4-2 1-5 2-7 4h1l-1 1h0-2l1-1-1-1-2 1c-2 0-3 0-4-2 0 0 1 0 2-1z"></path><defs><linearGradient id="P" x1="251.251" y1="148.795" x2="261.757" y2="154.134" xlink:href="#B"><stop offset="0" stop-color="#8e8c8e"></stop><stop offset="1" stop-color="#b4b6b5"></stop></linearGradient></defs><path fill="url(#P)" d="M255 136c3-2-1-11-1-15l2 5v-1-2h0 1v1 2c1 2 1 4 2 7v3l-1 5v1c0 4-2 8-1 13h0c0 2 1 4 1 6v4c-6-9-3-18-3-29z"></path><path d="M259 133v3l-1 5h0c0-2 0-5-1-8h2z" class="V"></path><path d="M256 126v-1-2h0 1v1 2c1 2 1 4 2 7h-2l-1-7z" class="d"></path><path d="M249 126h0c1 1 1 3 1 4l1 2c0 2-1 5 1 7h1c-1 4-3 10-3 14 1 4 2 7 3 10 3 5 7 10 12 14-1 0-2 1-2 1l-5 2v-1c0-2-3-4-4-5h0c-2-3-4-5-5-8v-2c-2-2-2-4-3-6 0-1-1-2-1-3h0l-1-3v-5l2-7h1v2 1 1h1l1-3v-2-3c1-3 0-7 0-10z" class="B"></path><path d="M246 140h1v2 1 1h1l1-3v3 2c-1 0-1 1-1 1v5 3c-1 3 0 6 1 9-2-2-2-4-3-6 0-1-1-2-1-3h0l-1-3v-5l2-7z" class="F"></path><path d="M247 142v1 1h1v1 1c0 1-1 1-1 2v1c-1-1-1-1-1-2l1-5z" class="B"></path><path d="M246 140h1v2l-1 5c-1 3-1 6-1 8h0l-1-3v-5l2-7z"></path><path d="M249 166c0-2-1-6 0-8 1 1 1 3 1 4 1 3 2 4 4 6h0c-1-1-1-3-2-4l1-1c3 5 7 10 12 14-1 0-2 1-2 1l-5 2v-1c0-2-3-4-4-5h0c-2-3-4-5-5-8z" class="J"></path><path d="M254 174h1c1 0 2 3 4 2h0c-1-1-1-1-1-2 2 1 3 3 5 4l-5 2v-1c0-2-3-4-4-5z" class="G"></path><path d="M324 419c1 0 1 0 2 1h0l-3-4v-1l-3-3-7-5 1-1 4 3h1l13 10c2 2 4 4 7 5 0 1 1 1 1 1l4 5c11 10 20 25 26 39 1 3 5 16 6 16 0-2-1-4-2-6-1-6-2-12-4-17l1 1v-1 1h2l2 7 2 10c0 1 0 2 1 3h-1v1 1 5c0 2 1 4 0 6 0 2-1 3-1 5h0c0-1 0-2-1-2v-1c0-2-1-4-1-7-1 2-1 3 0 5v4c1 3 1 9 1 12-1 4-1 9 0 13v-2 3h1v2 3c0 3-1 7-1 11-1 4-2 9-4 14-3 9-8 19-15 27-4 6-9 11-15 16v1l1 1c-1 0-2 1-3 2h-1l-2 1-1 1-2 1c-1 1-3 2-4 2-2 1-3 2-4 2h-1l5-4h0c6-4 11-10 17-15 1-1 4-4 5-6v-1c1-1 1-2 2-3 2-4 4-9 6-13l3-6c2-4 3-9 4-13 2-8 3-16 4-24 0-5 0-10-1-15 1 0 1 1 1 2h0c1-2 1-5 1-8-1-12-5-23-10-34l-1-1c0-2-2-5-3-7-4-12-10-21-19-30l-14-13z"></path><path d="M339 599h2v1l1 1c-1 0-2 1-3 2h-1c-1 0-1 0-2-1l3-3z" class="c"></path><path d="M329 606l7-4c1 1 1 1 2 1l-2 1-1 1-2 1c-1 1-3 2-4 2-2 1-3 2-4 2h-1l5-4z" class="J"></path><path d="M354 583h2 0c-4 6-9 11-15 16h-2c3-4 6-6 9-9 2-2 4-4 6-7z" class="L"></path><defs><linearGradient id="Q" x1="375.658" y1="555.719" x2="351.141" y2="573.333" xlink:href="#B"><stop offset="0" stop-color="#939291"></stop><stop offset="1" stop-color="#acabae"></stop></linearGradient></defs><path fill="url(#Q)" d="M369 551v-1c1 0 1-1 1-2 2 2 1 6 1 8-3 9-8 19-15 27h0-2c7-9 12-21 15-32z"></path><path d="M371 504c1 4 1 7 1 11 0 13-2 25-6 38-1 3-1 6-2 9l-9 18-4 5v-1c1-1 1-2 2-3 2-4 4-9 6-13l3-6c2-4 3-9 4-13 2-8 3-16 4-24 0-5 0-10-1-15 1 0 1 1 1 2h0c1-2 1-5 1-8z" class="G"></path><defs><linearGradient id="R" x1="367.185" y1="534.119" x2="378.657" y2="541.514" xlink:href="#B"><stop offset="0" stop-color="#78797a"></stop><stop offset="1" stop-color="#969393"></stop></linearGradient></defs><path fill="url(#R)" d="M372 491c0 1 0 2 1 3v2 1c1 1 1 2 1 3h0c1 3 1 9 1 12-1 4-1 9 0 13v-2 3h1v2 3c0 3-1 7-1 11-1 4-2 9-4 14 0-2 1-6-1-8 0 1 0 2-1 2v1c4-15 5-31 4-46l-1-14z"></path><path d="M324 419c1 0 1 0 2 1h0l-3-4v-1l-3-3-7-5 1-1 4 3h1l13 10c2 2 4 4 7 5 0 1 1 1 1 1l4 5v1l6 7-1 1c-1-1-1-1-1-2-1-1-4-4-6-5v1c-3-3-5-5-8-7 0 1 2 2 3 3l6 6c7 8 11 17 15 27 1 2 3 5 3 8l-1-1c0-2-2-5-3-7-4-12-10-21-19-30l-14-13z" class="C"></path><path d="M318 409h1l13 10c2 2 4 4 7 5 0 1 1 1 1 1l4 5v1c-5-3-8-8-13-10-1 0-1 0-2-1h0 0c-2-1-4-3-4-4l-7-7z" class="B"></path><path d="M325 416c1 0 2 1 3 1 1 2 2 3 3 4-1 0-1 0-2-1h0 0c-2-1-4-3-4-4zm19 14c11 10 20 25 26 39 1 3 5 16 6 16 0-2-1-4-2-6-1-6-2-12-4-17l1 1v-1 1h2l2 7 2 10c0 1 0 2 1 3h-1v1 1 5c0 2 1 4 0 6 0 2-1 3-1 5h0c0-1 0-2-1-2v-1c0-2-1-4-1-7-1 2-1 3 0 5v4h0c0-1 0-2-1-3v-1-2c-1-1-1-2-1-3 0-4-2-9-3-13-5-18-15-31-27-45v-1c2 1 5 4 6 5 0 1 0 1 1 2l1-1-6-7v-1z" class="M"></path><path d="M350 438l2 3 2 2-1 1-4-5 1-1z" class="T"></path><path d="M373 463l2 7v-1c-3-1-3-4-4-6h2z" class="U"></path><path d="M135 154v-4-7c0-10 1-19 3-29 0-2 0-4 1-6 2-3 1-5 2-8 1-2 2-5 3-7l3-9c4-8 7-18 15-22h4c3 2 6 7 9 10l4 5c1 2 2 3 3 5l9 12c1 2 2 4 2 6l-2 2-2 2h0c-2 2-2 3-5 3h-2l-1 1c-1 1-2 2-3 2-1 1-3 1-4 2h4c-1 1-2 1-3 2h4l1 1h1c1-1 1-1 1 0h-3c-4 0-16 6-19 8l-5 4c-4 2-7 5-9 9l-2 2c0 1 0 1-1 2h1c-3 4-5 9-7 14-1 2-2 4-3 5l-1 2 1-5 1-2z" class="B"></path><path d="M154 74c1-2 2-4 4-6l1 1v1c-1 1-2 2-2 3l-3 1z" class="C"></path><path d="M158 68c2-2 4-3 7-4 1 1 2 1 3 3l3 3h-6c-1 0-3 1-4 1-2 1-3 2-4 2h0c0-1 1-2 2-3v-1l-1-1z"></path><path d="M159 69c1-1 4-3 7-3 1 0 1 1 2 1h-1c-3 1-5 1-8 3v-1z" class="K"></path><path d="M140 111c1-6 3-12 5-18 1 0 1 1 1 1l-1 5c0 2-1 4 0 6-1 1-1 3-1 4-1 1-1 2-1 2v4 6h0c-1-3-1-6-1-9v1 9h-1v-7h-1s0 1-1 2l1-6z" class="I"></path><path d="M140 111l-1 6-2 27v1l1-1h1 0c2-3 4-6 7-8l-2 2c0 1 0 1-1 2h1c-3 4-5 9-7 14-1 2-2 4-3 5l-1 2 1-5 1-2c1-2 1-4 1-6 0-3 1-6 0-9v-9c0-1 1-1 1-2 0-2 0-4 1-6h0v-2s0-1 1-1c0-3 0-5 1-8z" class="E"></path><path d="M139 117c1-1 1-2 1-2h1v7h1c0 3 0 6 1 9 1 1 1 3 1 3 0 2-2 4-3 5s-2 3-3 5l-1 1v-1l2-27zm32-47c1 1 2 1 2 2l5 6c-2 1-6-1-8-2-3 0-6 0-9 1-2 0-3 1-5 2l-2 2h0l-1 1v-1h0v-1c-2 1-3 5-4 7 0-5 2-9 5-13l3-1h0c1 0 2-1 4-2 1 0 3-1 4-1h6z"></path><path d="M171 70c1 1 2 1 2 2-2 0-5-1-7-1l-1-1h6z" class="U"></path><path d="M154 78c3-2 5-3 9-4 2-1 8-1 10 0 1 1 1 1 2 1-1 1-3 0-4 0-5 0-11 0-15 4l-2-1z" class="C"></path><path d="M157 73h0c1 0 2-1 4-2 1 0 3-1 4-1l1 1c-4 1-10 3-12 7h-1 1l2 1h0l-2 2h0l-1 1v-1h0v-1c-2 1-3 5-4 7 0-5 2-9 5-13l3-1z" class="P"></path><path d="M161 77c3-1 6-1 9-1 2 1 6 3 8 2 1 1 2 3 3 5l5 7c1 2 2 4 4 6v1 4l1 1-2 2h0c-2 2-2 3-5 3h-2l-1 1-1-1-2 2-1-1c2-2 4-4 4-6 2-5 1-10-1-14-2-3-5-6-9-6-6-2-11 0-16 3l1 1-3 2c-1 2-3 3-4 5h0c-2 2-3 3-4 6l1-5s0-1-1-1l4-6c1-2 2-6 4-7v1h0v1l1-1h0l2-2c2-1 3-2 5-2z"></path><path d="M156 79c2-1 3-2 5-2h3v1c-2 0-4 1-6 2-1 0-2 1-4 1h0l2-2z" class="K"></path><path d="M190 97v4l1 1-2 2h0c-2 2-2 3-5 3h-2l2-1c3-2 6-5 6-9z" class="B"></path><path d="M161 77c3-1 6-1 9-1 2 1 6 3 8 2 1 1 2 3 3 5-1-1-2-2-4-3h0c-4-2-8-3-13-2v-1h-3z" class="C"></path><path d="M150 89c3-4 9-8 14-9s10 0 13 3c4 3 6 7 6 11 1 4 1 9-2 13h-1l-2 2-1-1c2-2 4-4 4-6 2-5 1-10-1-14-2-3-5-6-9-6-6-2-11 0-16 3l1 1-3 2c-1 2-3 3-4 5h0c-2 2-3 3-4 6l1-5c1-1 3-4 4-5z" class="T"></path><path d="M150 89h1l4-4 1 1-3 2c-1 2-3 3-4 5h0c-2 2-3 3-4 6l1-5c1-1 3-4 4-5z" class="Y"></path><path d="M146 107l1-2 1 4v-2h1c1 5 3 9 7 12 0 0 1 0 1 1l9-5c3-1 5-2 7-2l1-1h4c-1 1-2 1-3 2h4l1 1h1c1-1 1-1 1 0h-3c-4 0-16 6-19 8l-5 4c-4 2-7 5-9 9-3 2-5 5-7 8h0-1c1-2 2-4 3-5s3-3 3-5c0 0 0-2-1-3-1-3-1-6-1-9v-9-1c0 3 0 6 1 9h0v-6-4s0-1 1-2c0-1 0-3 1-4v3-3l1 1v1z" class="X"></path><path d="M148 109v-2h1c1 5 3 9 7 12 0 0 1 0 1 1l9-5c3-1 5-2 7-2-2 1-4 2-7 4-2 2-6 5-9 5l-4 4c-2-2-3-4-4-7l-2-6 2-1-1-3z" class="B"></path><path d="M147 113l2-1c1 5 4 8 8 10l-4 4c-2-2-3-4-4-7l-2-6z"></path><path d="M146 107l1-2 1 4 1 3-2 1 2 6c-1 0-1 0-2 1 2 3 3 5 5 7l-7 6c-1-4-2-8-2-12v-6-4s0-1 1-2c0-1 0-3 1-4v3-3l1 1v1z"></path><path d="M146 107l1-2 1 4 1 3-2 1 2 6c-1 0-1 0-2 1-1-3-2-9-2-12v-3l1 1v1z" class="T"></path><path d="M146 107l1-2 1 4 1 3-2 1-1-6z" class="R"></path><path d="M155 85c5-3 10-5 16-3 4 0 7 3 9 6 2 4 3 9 1 14 0 2-2 4-4 6l1 1 2-2 1 1c-1 1-2 2-3 2-1 1-3 1-4 2l-1 1c-2 0-4 1-7 2l-9 5c0-1-1-1-1-1-4-3-6-7-7-12h-1v2l-1-4-1 2v-1l-1-1v3-3c-1-2 0-4 0-6 1-3 2-4 4-6h0c1-2 3-3 4-5l3-2-1-1z"></path><path d="M173 106h0c-1 1-3 2-5 2-3 0-5-1-6-2 4 1 8 0 11-1v1z" class="N"></path><path d="M163 102l-1-1c-1-1-2-3-1-4 0-1 2-3 3-4s2-1 4-1c1 0 2 1 2 2 1 1 1 3 0 4 0 1 0 1-1 1h0v-3c0-1 0-1-1-2-1 0-2-1-3 0-1 0-2 1-2 3 0 0 0 1-1 2v1l1 2h0z" class="E"></path><defs><linearGradient id="S" x1="163.495" y1="102.433" x2="175.278" y2="100.023" xlink:href="#B"><stop offset="0" stop-color="#5e5c5e"></stop><stop offset="1" stop-color="#868686"></stop></linearGradient></defs><path fill="url(#S)" d="M163 88c1-1 4-1 6-1 3 1 5 2 6 5s1 7 0 9v1c-2 2-4 3-7 3-2 0-4-1-5-2v-1h0l-1-2v-1c1-1 1-2 1-2v2c1 1 3 3 4 3h1 2c1 0 2-1 3-2 1-2 2-4 1-5 0-2-1-4-2-5-3-2-6-2-9-2z"></path><defs><linearGradient id="T" x1="177.328" y1="101.166" x2="162.26" y2="81.935" xlink:href="#B"><stop offset="0" stop-color="#4c4c4d"></stop><stop offset="1" stop-color="#6b6a6a"></stop></linearGradient></defs><path fill="url(#T)" d="M157 85c3-2 7-3 11-2 3 0 6 2 9 5 2 2 3 5 2 8 0 3-1 7-4 9 0 1-1 1-2 1v-1l1-1c1-1 2-1 1-2v-1c1-2 1-6 0-9s-3-4-6-5h1c-2-1-5-1-7-1h-3-1 0v1h-1v-1h0-1 1l-1-1z"></path><path d="M153 102c0-2 0-4 1-6s1-2 3-2v1c-1 3-1 5 0 8s4 5 6 7c4 1 9 1 12-1l2-1 1 1 2-2 1 1c-1 1-2 2-3 2-1 1-3 1-4 2l-1 1c-2 0-4 1-7 2-3-2-6-2-8-4-3-3-5-5-5-9z" class="K"></path><path d="M156 86l1-1 1 1h-1 1 0v1h1v-1h0 1 3c2 0 5 0 7 1h-1c-2 0-5 0-6 1-1 2-2 2-3 3s-2 2-3 4v-1c-2 0-2 0-3 2s-1 4-1 6c0 4 2 6 5 9 2 2 5 2 8 4l-9 5c0-1-1-1-1-1-4-3-6-7-7-12h-1v2l-1-4-1 2v-1l-1-1v3-3c-1-2 0-4 0-6 1-3 2-4 4-6h0c1-2 3-3 4-5l3-2z"></path><path d="M148 99l1 1v7h-1v2l-1-4c1-1 1-4 1-6z" class="H"></path><path d="M153 88v4h0 1c-3 3-4 5-5 8l-1-1c0-2 1-4 1-6h0c1-2 3-3 4-5z" class="T"></path><path d="M145 99c1-3 2-4 4-6 0 2-1 4-1 6s0 5-1 6l-1 2v-1l-1-1v3-3c-1-2 0-4 0-6zm11-13l1-1 1 1h-1 1 0v1h1v-1h0 1 3c2 0 5 0 7 1h-1c-2 0-5 0-6 1-1 2-2 2-3 3s-2 2-3 4v-1c-2 0-2 0-3 2s-1 4-1 6v-3-1c-1 3-1 5 0 8 2 5 4 7 9 9h-1-2c-2-1-5-3-6-6-2-3-2-8-1-12 0-1 1-3 2-5h-1 0v-4l3-2z" class="U"></path><path d="M160 86h3 0c-3 2-6 5-8 7h-1c0-1 1-1 0-2 2-2 4-3 6-5z" class="K"></path><path d="M156 86l1-1 1 1h-1 1 0v1h1v-1h0 1c-2 2-4 3-6 5l-1 1h0v-4l3-2z" class="M"></path><path d="M258 360c-2-2-4-4-5-7-2-3-4-7-5-11-1-1-3-3-3-4v-1l6 5c1 0 2 0 3 1 4 4 9 7 12 11h0c2 2 4 5 6 7h0l3 6c0 1 1 2 1 2 1-1 1-1 2-1h1v-1l3 3c2 1 4 4 5 6 2 2 4 4 7 6 8 7 17 11 26 17 4 3 8 6 11 10 3 5 5 10 8 15-3-1-5-3-7-5l-13-10h-1l-4-3-1 1 7 5 3 3v1l3 4h0c-1-1-1-1-2-1l14 13c9 9 15 18 19 30 1 2 3 5 3 7-3-3-4-7-6-9-1-2-3-4-4-5l-1 1h0-1c-1-2-2-2-4-2-9-12-22-22-35-29h-1-1l-1-1-1 1-9-6c-4-4-8-8-12-13-3-2-6-8-7-11l-2-3v-1l1-1c0-2-1-3-1-4-1-2-1-3-2-4 0-2-1-4-2-6-2-4-5-8-9-12l-4-4z"></path><path d="M273 375v-1h1c1 5 2 11 5 16 2 3 6 6 9 8 5 3 10 5 16 8 9 5 17 11 23 20 2 3 4 7 6 11-3-3-5-7-8-11-2-4-7-8-11-11-7-6-15-9-23-14-4-2-7-4-10-7l12 16h0c-1 0-1 0-2-1s-2-1-3-3l-3-3v-1c-1 0-1-1-1-1-1-1 0-1-1-1l-1-1c0-1-1-1-2-2h0c0-1-1-1-1-2-1-1-1-1-2-1v1l-2-3v-1l1-1c0-2-1-3-1-4-1-2-1-3-2-4 1-3 0-5 0-7z" class="O"></path><path d="M279 368v-1l3 3c7 10 14 20 23 28l14 11h-1l-4-3-1 1 7 5 3 3v1l3 4h0c-1-1-1-1-2-1-5-6-10-9-16-14-13-10-24-22-32-36 1-1 1-1 2-1h1z" class="H"></path><path d="M278 368h1l3 5c1 2 2 3 3 4 1 2 3 4 4 6h-1c-2-3-5-5-7-8 0-1-1-3-2-4 0-1-1-2-1-3z" class="T"></path><path d="M258 360c-2-2-4-4-5-7-2-3-4-7-5-11-1-1-3-3-3-4v-1l6 5c1 0 2 0 3 1 4 4 9 7 12 11h0c1 3 4 6 5 8 2 3 3 8 3 12h-1v1c0 2 1 4 0 7 0-2-1-4-2-6-2-4-5-8-9-12l-4-4z"></path><path d="M251 342c1 0 2 0 3 1 4 4 9 7 12 11h0c1 3 4 6 5 8 2 3 3 8 3 12h-1v1c-1 0-1-3-1-3 0-3-1-6-2-8-4-9-12-16-19-22z" class="F"></path><defs><linearGradient id="U" x1="317.212" y1="423.404" x2="312.001" y2="431.353" xlink:href="#B"><stop offset="0" stop-color="#989898"></stop><stop offset="1" stop-color="#b6b5b5"></stop></linearGradient></defs><path fill="url(#U)" d="M277 395v-1c1 0 1 0 2 1 0 1 1 1 1 2h0c1 1 2 1 2 2l1 1c1 0 0 0 1 1 0 0 0 1 1 1v1l3 3c1 2 2 2 3 3s1 1 2 1h0c5 4 10 8 15 11 3 1 6 3 9 5 9 5 17 12 25 19 2 3 6 7 8 10l-1 1h0-1c-1-2-2-2-4-2-9-12-22-22-35-29h-1-1l-1-1-1 1-9-6c-4-4-8-8-12-13-3-2-6-8-7-11z"></path><path d="M296 419c3 0 4 1 7 2 2 1 4 3 6 4h-1-1l-1-1-1 1-9-6z" class="H"></path><path d="M277 395v-1c1 0 1 0 2 1 0 1 1 1 1 2h0l1 2 1 1 2 2v1l1 1s1 0 1 1c-2-1-4-3-5-5 0 0 0-1-1-1 0 1 3 5 4 6v1c-3-2-6-8-7-11z" class="X"></path><path d="M282 370c2 1 4 4 5 6 2 2 4 4 7 6 8 7 17 11 26 17 4 3 8 6 11 10 3 5 5 10 8 15-3-1-5-3-7-5l-13-10-14-11c-9-8-16-18-23-28zm-7 22l2 3c1 3 4 9 7 11 4 5 8 9 12 13l9 6 1-1 1 1h1 1c13 7 26 17 35 29 2 0 3 0 4 2h1 0l1-1c1 1 3 3 4 5 2 2 3 6 6 9l1 1c5 11 9 22 10 34 0 3 0 6-1 8h0c0-1 0-2-1-2 1 5 1 10 1 15-1 8-2 16-4 24-1 4-2 9-4 13l-3 6c-2 4-4 9-6 13l-1-1h-1l3-6c3-5 4-11 5-16 3-18 2-35-5-51-1-4-3-8-5-12-3-4-6-8-9-13-3-3-5-6-8-9-1-1-2-3-3-3l-1-1h-1c-1-1-3-3-4-5-2-2-4-4-7-5l-9-6-9-6-6-3c-2-2-5-3-8-3 2-1 4-2 5-4 2-4 0-9-1-12v-1c0-1-1-2-1-3 0 0 0-1 1-1l-3-5c-1-1-1-2-2-3l-1-1h0-2c-2-2-3-6-5-9v-1c0-1 0-3 1-5l-1-2v-2z"></path><path d="M284 410l2 3-1 2c-1-1-1-2-2-3l1-2z" class="B"></path><path d="M286 413c2 3 4 6 7 9 0 1 0 1-1 2l-3-4h-1 0l-3-5 1-2z" class="O"></path><path d="M276 396c1 3 2 5 4 7 1 2 2 5 4 7l-1 2-1-1h0-2c-2-2-3-6-5-9v-1c0-1 0-3 1-5z" class="J"></path><path d="M293 422l11 7c2 1 5 3 7 5h-2l-1-1v1l-2-1-1 1h0c-4-3-9-6-13-10 1-1 1-1 1-2z" class="H"></path><defs><linearGradient id="V" x1="353.4" y1="484.295" x2="367.969" y2="482.825" xlink:href="#B"><stop offset="0" stop-color="#bebac1"></stop><stop offset="1" stop-color="#d3d4d3"></stop></linearGradient></defs><path fill="url(#V)" d="M349 456l1-1c1 1 3 3 4 5 2 2 3 6 6 9l1 1c5 11 9 22 10 34 0 3 0 6-1 8h0c0-1 0-2-1-2 0-11-3-21-7-30-3-7-7-13-10-19v-1l-3-4z"></path><path d="M349 456l1-1c1 1 3 3 4 5l-1-1c-1 0-1-1-2-2l1 3-3-4z" class="X"></path><path d="M337 456c4 7 8 14 10 22 2 4 3 8 4 12 1 3 3 6 5 9 3 7 6 14 8 22 0 2 1 4 1 5v7c0 8-1 15-2 23-1 2-1 4-2 6 0-5 2-10 2-15 1-8 1-18-1-26-1-5-3-11-5-16l-5-10c-1-1-2-3-2-5-1-3-2-5-2-8-3-9-7-17-11-25v-1z" class="G"></path><path d="M305 425l1-1 1 1h1c4 3 7 7 10 10 7 6 14 13 19 21v1c-1 0-4-4-5-5 7 12 12 24 17 37l-1 1c-2-3-2-6-4-9l-5-12c-5-13-13-23-22-32l-12-12z" class="B"></path><path d="M321 444c2 4 5 8 6 12 1 3 2 5 2 8 0 1 0 3 1 4 1 2 4 4 6 6s3 4 5 6c1 2 3 4 4 6 16 23 20 51 14 77-1 6-4 13-7 17h-1l3-6c3-5 4-11 5-16 3-18 2-35-5-51-1-4-3-8-5-12-3-4-6-8-9-13-3-3-5-6-8-9-1-1-2-3-3-3l-1-1c0-1-2-2-2-3-1 0 0-1 0-1 1 0 1 1 2 2 0-3 0-5-1-8-1-4-4-9-6-13v-2z" class="V"></path><path d="M288 420h1l3 4c4 4 9 7 13 10h0l1-1 2 1v-1l1 1h2c4 2 9 7 10 10h0v2c2 4 5 9 6 13 1 3 1 5 1 8-1-1-1-2-2-2 0 0-1 1 0 1 0 1 2 2 2 3h-1c-1-1-3-3-4-5-2-2-4-4-7-5l-9-6-9-6-6-3c-2-2-5-3-8-3 2-1 4-2 5-4 2-4 0-9-1-12v-1c0-1-1-2-1-3 0 0 0-1 1-1h0z"></path><path d="M288 420h0l3 5c-1 0-2 1-2 2l-1-3c0-1-1-2-1-3 0 0 0-1 1-1z" class="G"></path><path d="M314 455c4 2 9 6 12 10 0 0-1 1 0 1 0 1 2 2 2 3h-1c-1-1-3-3-4-5h0c-1-3-7-5-9-7v-2z" class="L"></path><path d="M305 434l1-1 2 1v-1l1 1h2c4 2 9 7 10 10h0v2h-1c1 1 2 4 3 6 2 3 4 9 3 12-1-1-1-4-1-5-3-11-11-19-20-25z" class="B"></path><path d="M308 434v-1l1 1h2c4 2 9 7 10 10h0v2h-1c-2-3-4-5-6-7-1-2-4-3-6-5z" class="M"></path><path d="M288 424l1 3c0-1 1-2 2-2 3 5 5 10 8 15 1 2 3 4 4 6 1 0 2 1 2 2l9 7v2c2 2 8 4 9 7h0c-2-2-4-4-7-5l-9-6-9-6-6-3c-2-2-5-3-8-3 2-1 4-2 5-4 2-4 0-9-1-12v-1z"></path><path d="M304 450l1-1v1h1l-1-1v-1l9 7v2c-3-2-7-5-10-7z" class="G"></path><path d="M291 425c3 5 5 10 8 15 1 2 3 4 4 6 1 0 2 1 2 2v1l1 1h-1v-1l-1 1-2-2c-7-6-10-14-13-21 0-1 1-2 2-2z" class="L"></path><path d="M309 425c13 7 26 17 35 29 2 0 3 0 4 2h1 0l3 4v1c0 3 2 5 3 7l3 7c5 12 7 25 7 39 0 4 1 8 0 12 0-1-1-3-1-5-2-8-5-15-8-22-2-3-4-6-5-9-1-4-2-8-4-12-2-8-6-15-10-22-5-8-12-15-19-21-3-3-6-7-10-10h1z"></path><path d="M344 454c2 0 3 0 4 2h1 0l3 4v1c0 3 2 5 3 7l3 7c5 12 7 25 7 39 0 4 1 8 0 12 0-1-1-3-1-5h0c1-4 0-8 0-13-2-20-7-38-20-54z" class="B"></path><path d="M103 639c-15-6-29-17-36-32-4-10-6-23-2-33 2-6 7-12 13-14 6-3 13-4 19-1 5 2 7 5 10 10v7c-2 5-4 8-9 10-2 1-3 3-5 4l-1-1-1-3-1-1-1 1h0c0 3-1 7 0 9l-1-1-1 1c-2 0-4 1-6 3s-2 5-2 8c1 3 2 5 3 8l3 4h1c4 3 6 7 11 9h0c2 1 4 3 6 4s6 3 8 3c1 1 2 1 4 1h0c1 0 2 0 3 1h1v1h0c5 0 9 1 13 1h1l2 1h-3-1l19 1h-3 3v1h-3 3l1 1h9l18-1c7 0 14 1 21 0 3-1 6-1 8-3h2l-1 1h1 0c-2 2-5 3-7 4h-2c3 0 8-2 10-4 3-2 5-7 4-10v-1-5h0c2 1 5 1 6 0h0v1c-1 0-2 2-3 3s-1 2-2 3v1 2l1 1h0v2c1 2 2 4 4 5h1c1 1 2 1 3 1-2 1-4 0-5 0-2 1-4 0-5 1h-2-4-2c0 1 0 1 1 1 3 1 8-1 11-1h9 5l16-1h0v-1h1c8 2 16 1 24 1l51-2c8 0 16 0 23 1 2 0 4 0 6 1 4 0 8-2 12-2 0 1 0 1 1 2h-3c2 0 3 1 4 1 3 0 17 0 19 2h-33l-211 2c-1-2-12 0-14-1-2-2-8-2-10-3l-9-2-6-2z" class="N"></path><path d="M119 637c-1 0-2-1-3-1h0 2 1v1z" class="b"></path><path d="M70 590l1 4c0 1-1 2-1 3 0-1-1-2-1-4l1 1v-4z" class="I"></path><path d="M353 642c4 0 8-2 12-2 0 1 0 1 1 2h-3c-3 1-5 0-8 1h-8l6-1z" class="B"></path><path d="M363 642c2 0 3 1 4 1 3 0 17 0 19 2h-33c3 0 7 1 9 0h2 3c-1 0-4-1-5-1 0 0 0-1-1-1h-6c3-1 5 0 8-1z" class="P"></path><path d="M69 584c-1 2 0 4-1 6v4-4c-1-6 0-13 3-19 4-5 9-9 15-11h4c0 1-1 1-2 1h0c-4 2-5 4-7 8 0 1-1 2-1 4h-1c-1-1-2-1-4-1-2 1-4 4-5 6 0 2-1 4-1 6z"></path><path d="M88 561c1 0 4 0 5 1h0 0c1 1 1 1 2 1 2 0 5 2 6 5 1 2 2 5 1 7 0 3-2 4-4 5h0c0 1 0 1-1 2s-3 2-5 2c0 1-1 0-2 0 0 0-2-1-3-2h-1c-1-1-2-3-4-3l-1-4h-1-1v-1-1h1c0-2 1-3 1-4 2-4 3-6 7-8z"></path><path d="M85 575c0-2 0-4 1-6l6-3c2 0 4 1 5 2s2 2 2 4-1 4-3 5c0 1-1 1-2 1 2-2 4-3 4-5s0-3-1-4c-2-1-3-1-5-1s-4 1-5 2-1 3-1 4c0 2 2 4 3 5h-1c-2-1-2-2-3-4h0z" class="C"></path><path d="M88 561c1 0 4 0 5 1h0c-3 0-6 1-8 3s-2 5-1 8v2h1 0c1 2 1 3 3 4h1c2 2 4 2 7 2l2-1c0 1 0 1-1 2s-3 2-5 2c0 1-1 0-2 0 0 0-2-1-3-2h-1c-1-1-2-3-4-3l-1-4h-1-1v-1-1h1c0-2 1-3 1-4 2-4 3-6 7-8z" class="D"></path><path d="M87 581c3 2 6 2 10 1h0c-1 1-3 2-5 2 0 1-1 0-2 0 0 0-2-1-3-2v-1z" class="R"></path><path d="M79 573h1c0-2 1-3 1-4l1 3c0 4 3 7 5 9v1h-1c-1-1-2-3-4-3l-1-4h-1-1v-1-1z" class="Q"></path><path d="M69 584c0-2 1-4 1-6 1-2 3-5 5-6 2 0 3 0 4 1v1 1h1 1l1 4c2 0 3 2 4 3h1c1 1 3 2 3 2l2 1-1 1-1-1-1 1h0c0 3-1 7 0 9l-1-1-1 1c-2 0-4 1-6 3s-2 5-2 8l3 8-1 1v1c-1-1-2-2-2-3h-1 0 0c-1-1-2-2-2-4a24.56 24.56 0 0 1-5-15l-1-4v4l-1-1v-9z"></path><path d="M86 582h1c1 1 3 2 3 2l2 1-1 1-1-1-1 1-3-4z" class="S"></path><path d="M79 575h1 1l1 4c-2 0-4 0-6-1v-2c1-1 1-1 3-1z" class="I"></path><path d="M79 575h1 1c-2 1-3 1-4 2l-1 1v-2c1-1 1-1 3-1z" class="C"></path><path d="M69 584c0-2 1-4 1-6 1-2 3-5 5-6 2 0 3 0 4 1v1c-1-1-3-1-4 0-4 3-5 10-5 14v2 4l-1-1v-9zm9 29c-1-3-3-7-3-10-1-4-1-9 1-13 1-1 3-3 5-4 2 0 3 0 5 1l2 2v1c-1 0-1 0-2-1s-3-1-4-1c-2 0-3 1-4 2-2 3-3 8-2 11 1 6 2 9 5 14v1c-1-1-2-2-2-3h-1z" class="D"></path><path d="M155 127l5-4c3-2 15-8 19-8v1h1 3c4 0 7 0 11 1h-2c1 2 4 1 6 2v1h2c1 1 1 1 2 1s2 0 3 1l1 1h0c5 2 10 5 15 9l1 2h-4-1l-3 1c-2 3-4 3-7 4-1 0-2 1-3 2-1 0-1 0-1 1v1h0c0 1-1 1-2 2v3 1c1 0 1 1 1 1l-1 2-1-1c-1-2-1-3-1-5 0-1 2-2 2-3-1 0-2 1-3 2l-6 9h-2c0 1-1 2-2 2h-1c-1 1-1 2-2 4l-2 3c-2 2-4 5-5 8h0c1 1 0 2 0 3-1 2-2 4-4 5h-1-4c-3-2-3-3-5-6v-2c0-1-1-2-1-3 0-2 2-9 1-12-1 2-3 5-4 7v2c0 1-1 2-1 3l-4 12c-1 2-3 5-3 8h0l-1-1c-2 4-2 9-3 13 0 0-1 0-1-1s0-1-1-2c0 3-2 6-2 8-1 4-3 7-5 11-1 2-3 4-4 7 0 1 0 1-1 1v-2c-1-6 1-11 3-16 1-2 5-14 7-15 0 0 1 1 2 0h-1c0-1-1-1-2-2h0c2-3 4-6 5-9 0-2 0-3-1-5 0 0 0-1-1-2s-3-1-3-3c-3 0-5 3-6 5l-2 2h0c0-2 2-5 3-7h1-4l-1-1v-7h-1v-1l1-2c1-1 2-3 3-5 2-5 4-10 7-14h-1c1-1 1-1 1-2l2-2c2-4 5-7 9-9z"></path><path d="M152 171c1 0 2-1 4-1 0 1 0 1-1 2-1 0-2 1-3 1h0l-2 1-2-2c1 0 3 0 4-1z" class="J"></path><path d="M150 174l2-1c-1 3-3 4-4 7 0-2 0-3-1-5 1 0 2-1 3-1h0 0z" class="P"></path><g class="B"><path d="M195 129c3 0 6 0 9 1l1 1h-12l1-1h4-1l-2-1zm-50 32h1v3c1 2 1 5 3 7h3c-1 1-3 1-4 1h0c-3-4-3-7-3-11z"></path><path d="M206 130h1l1-1 6 3v2l-9-3-1-1h2z"></path></g><path d="M146 197c1-2 2-4 2-6 1-4 2-8 4-11 1 1 0 2 0 4l-1 3c-2 4-2 9-3 13 0 0-1 0-1-1s0-1-1-2z" class="H"></path><defs><linearGradient id="W" x1="154.382" y1="169.143" x2="157.756" y2="178.95" xlink:href="#B"><stop offset="0" stop-color="#949495"></stop><stop offset="1" stop-color="#b0aeb0"></stop></linearGradient></defs><path fill="url(#W)" d="M160 163v2c0 1-1 2-1 3l-4 12c-1 2-3 5-3 8h0l-1-1 1-3c0-2 1-3 0-4l8-17z"></path><path d="M193 131c-10 0-18 2-25 8-7 5-13 13-14 22 0 3 0 7-1 10-1-2-1-5 0-8 0-8 5-18 11-24 9-7 20-10 31-10l2 1h1-4l-1 1z" class="X"></path><path d="M192 138c5-1 10-3 16-2-3 1-7 1-10 2-8 2-15 7-21 13-2 3-5 8-4 12 0 1 0 1 1 2 4-1 7-5 10-8l7-8c7-7 13-13 23-14-2 3-4 3-7 4-1 0-2 1-3 2-1 0-1 0-1 1v1h0c0 1-1 1-2 2v3 1c1 0 1 1 1 1l-1 2-1-1c-1-2-1-3-1-5 0-1 2-2 2-3-1 0-2 1-3 2l-6 9h-2c0 1-1 2-2 2h-1l1-2c-3 2-5 7-8 10-2 1-4 2-6 2-1 0-1 0-2-1s-1-3-1-5c1-7 9-14 15-18h0c2-1 4-2 6-4z" class="L"></path><path d="M188 154l1-1 1-1v1h1s0 1-1 1c0 1-1 2-2 2h-1l1-2z" class="J"></path><path d="M164 156l2-2c6-8 16-15 26-16-2 2-4 3-6 4h0c-6 4-14 11-15 18 0 2 0 4 1 5s1 1 2 1c2 0 4-1 6-2 3-3 5-8 8-10l-1 2c-1 1-1 2-2 4l-2 3c-2 2-4 5-5 8h0c1 1 0 2 0 3-1 2-2 4-4 5h-1-4c-3-2-3-3-5-6v-2c0-1-1-2-1-3 0-2 2-9 1-12z"></path><path d="M164 171h0c1-1 1-2 1-3 0 3 0 6 3 8 1 1 2 2 4 2 1 0 3-1 4-3 1-1 2-3 2-4 1 1 0 2 0 3-1 2-2 4-4 5h-1-4c-3-2-3-3-5-6v-2z" class="B"></path><defs><linearGradient id="X" x1="172.397" y1="146.151" x2="180.37" y2="161.682" xlink:href="#B"><stop offset="0" stop-color="#828181"></stop><stop offset="1" stop-color="#acacaf"></stop></linearGradient></defs><path fill="url(#X)" d="M164 156l2-2c6-8 16-15 26-16-2 2-4 3-6 4h0c-2 0-4 2-6 3-3 2-5 5-8 8-2 3-5 7-6 11 0 1 0 4-1 4 0 1 0 2-1 3h0c0-1-1-2-1-3 0-2 2-9 1-12z"></path><path d="M140 166c-1-2 0-7 0-8 2-11 10-22 19-28 11-7 24-9 37-7 6 2 12 4 17 7 1 1 3 2 4 4l-3 1v-1-2l-6-3-1 1h-1c-3-1-5-3-8-3-9-2-19 0-27 3-10 5-19 11-23 22-1 4-2 8-2 12v-3h-1c0 4 0 7 3 11h0l2 2h0 0c-1 0-2 1-3 1 0 0 0-1-1-2s-3-1-3-3c-3 0-5 3-6 5l-2 2h0c0-2 2-5 3-7h1 1l2-2h-2v-2z"></path><path d="M141 165c0 1 0 1 1 1h2 0v1c-1 1-1 1-2 1h-2v-2l1-1z" class="J"></path><path d="M140 166c-1-2 0-7 0-8 2-11 10-22 19-28 11-7 24-9 37-7 6 2 12 4 17 7 1 1 3 2 4 4l-3 1v-1-2l-6-3-1 1h-1c-3-1-5-3-8-3-9-2-19 0-27 3-10 5-19 11-23 22-1 4-2 8-2 12v-3h-1c0-9 5-17 11-23 11-10 24-13 38-13 4 0 7 1 10 2-8-4-21-5-29-2-12 3-22 10-29 20-3 6-6 13-5 20l-1 1z" class="O"></path><path d="M155 127l5-4c3-2 15-8 19-8v1h1 3c4 0 7 0 11 1h-2c1 2 4 1 6 2v1h2c1 1 1 1 2 1s2 0 3 1l1 1h0c5 2 10 5 15 9l1 2h-4-1c-1-2-3-3-4-4-5-3-11-5-17-7-13-2-26 0-37 7-9 6-17 17-19 28 0 1-1 6 0 8v2h2l-2 2h-1-4l-1-1v-7h-1v-1l1-2c1-1 2-3 3-5 2-5 4-10 7-14h-1c1-1 1-1 1-2l2-2c2-4 5-7 9-9z"></path><path d="M146 136c2-4 5-7 9-9v1 2h0c-1 1-3 2-4 3-2 2-3 4-5 6-3 4-5 9-7 13-2 5-4 10-4 14 0 1 1 2 1 2 1 1 3 1 4 0h0 2l-2 2h-1-4l-1-1v-7h-1v-1l1-2c1-1 2-3 3-5 2-5 4-10 7-14h-1c1-1 1-1 1-2l2-2z" class="T"></path><path d="M155 127l5-4c3-2 15-8 19-8v1h1 3c4 0 7 0 11 1h-2c1 2 4 1 6 2v1h2c1 1 1 1 2 1s2 0 3 1l1 1h0c-15-5-29-5-43 2-3 1-5 3-8 5v-2-1z" class="B"></path><path d="M155 127l5-4c3-2 15-8 19-8v1h1 3c-8 2-16 4-23 9-2 1-3 2-5 3v-1z" class="P"></path><path d="M421 91h10 5v32 50c0 6 2 58 1 58l-33 1c0-9-2-18-4-27l-2-8c0-1-1-3-1-3 0-3-2-4-2-7l1 1c-1-1-1-2-1-3h0 1 1c2 0 3-2 4-3l2-2c1-2 2-4 2-7 1-1 1-1 1-2h1l4-2c6-1 9-1 13 2h2 1 0 1l-4-4h4c2 0 3 0 4-1 2-2 2-3 2-6h0c0-1-1-2-1-2-3-4-5-6-9-8-3-2-7-3-10-4-1-1-2-1-3-2 0-1-1-1-2-1v-1h0c-2-1-5-2-6-4-2-2-5-5-6-8s0-5 1-7 3-3 5-4c5-1 9-1 13 2l-1-2c-1-2-6-3-9-3h0 1v-1h-3l-5 2-1-2c-5 3-8 6-15 4-1 0-2-1-3-1 2-1 4 0 6 0s4-1 5-2c2-1 4-2 6-4 5-6 5-13 5-21h0c6 1 13 0 19 0z" class="R"></path><path d="M423 93h1c3 1 7 4 8 7 1 2 1 6 0 8v-1-1c0-1 0-3-1-4v-2c-2-4-4-5-8-7z" class="C"></path><path d="M424 206c1 0 2 0 3 1-2 1-1 3-2 5h-3c-1-2 1-3 1-5l1-1zm3-75h0c4-3 5-10 6-15l1-8v2l-1 6v5c-1 4-3 7-5 10v3s0 1-1 1c-3 3-6 5-10 6h-2 0v-1c3 0 7-2 10-4 1-1 3-2 3-4l-1-1z" class="I"></path><path d="M428 134h2 0c1-1 1-2 2-3l1-1v-2c1-1 1-1 1-2l1 1s0 1-1 2c-1 2-1 4-2 6-1 5-5 10-9 13h-1 0c4-4 8-9 10-14-1 0-2 1-3 2l-1 1-1-2c1 0 1-1 1-1z" class="b"></path><path d="M427 135l1 2 1-1c-4 5-8 8-14 8h-4c0-1-1-1-2-1v-1c2 0 4 1 7 1l-1-1h0l2-1c4-1 7-3 10-6z" class="D"></path><path d="M427 135l1 2c-4 3-7 5-12 6l-1-1h0l2-1c4-1 7-3 10-6z" class="R"></path><path d="M404 115h4c5 1 8 2 11 6 1 2 2 4 2 7 1 1 1 3 1 3 0 1 1 1 1 1 2 0 3 0 4-1l1 1h-1c-1 1-2 1-4 1 0 0-2-1-3-1-1-1-1-6-1-8l-3-3-1-2c-1-2-6-3-9-3h0 1v-1h-3z" class="D"></path><path d="M428 171c4 4 6 10 6 17 0 1-1 3-1 4l-1 1v-10l-6-9v-1-2h1 0 1z" class="U"></path><path d="M427 171h0c3 4 4 7 5 12l-6-9v-1-2h1z" class="I"></path><path d="M432 193l1-1c-1 5-2 8-6 11l-3 1h0v2l-1 1c-3 0-6 0-9-1-6-2-9-5-11-11v-1h1l2 4c2 3 6 5 10 6s7 0 11-3c3-2 4-5 5-8z" class="K"></path><path d="M414 206c2 0 5 0 7-1 1 0 1 0 2-1h1v2l-1 1c-3 0-6 0-9-1z" class="P"></path><path d="M424 204h0 1c0 1 1 1 2 1 2 0 4 1 6 3 1 2 1 5 1 7 0 4-2 7-5 9-3 3-8 3-11 2-4-1-7-4-10-7-1-1-2-3-2-5h0 0c2 4 7 9 11 11 2 1 7 0 10-1 2-1 5-4 5-7 1-1 1-5 0-6-1-2-3-3-5-4-1-1-2-1-3-1v-2z" class="Z"></path><path d="M403 138c-2-2-5-5-6-8s0-5 1-7 3-3 5-4c5-1 9-1 13 2l3 3c0 2 0 7 1 8 1 0 3 1 3 1 2 0 3 0 4-1h1c0 2-2 3-3 4-3 2-7 4-10 4v1h0 2l-2 1h0l1 1c-3 0-5-1-7-1h0c-2-1-5-2-6-4z"></path><path d="M405 122c2-1 3-1 6-1 1 1 2 2 2 3h1l-1 1c-1 0-2-1-3-2-2-1-4 0-6 1l1-2z" class="I"></path><path d="M403 138v-1c-1-1-2-3-2-5h0c-1-2-1-4 0-6 1-1 2-3 4-4l-1 2c-2 2-3 4-3 7 1 3 2 6 4 7 4 3 6 2 10 2v1h0 2l-2 1h0l1 1c-3 0-5-1-7-1h0c-2-1-5-2-6-4z" class="Q"></path><path d="M402 91h0c6 1 13 0 19 0v1l3 1h-1c4 2 6 3 8 7v2c1 1 1 3 1 4v1 1c-1 3-4 6-7 7-3 2-7 2-10 1h-2c-1-1-1-1-2-1h-2c-1-1-3-1-5-1h-3v-1h-1l-2 2c-5 3-8 6-15 4-1 0-2-1-3-1 2-1 4 0 6 0s4-1 5-2c2-1 4-2 6-4 5-6 5-13 5-21z"></path><path d="M401 113c4-1 8 0 12 1h7-1-5-2l1 1c1 0 1 0 2 1h-2c-1-1-1-1-2-1h-2c-1-1-3-1-5-1h-3v-1z" class="S"></path><path d="M423 93c4 2 6 3 8 7-2-1-3-2-5-3-1-1-3-1-4-2h-5c0-1 4-1 5-1 0 0 1 0 1-1z" class="N"></path><path d="M431 102c1 1 1 3 1 4v1 1c-1 3-4 6-7 7-3 2-7 2-10 1-1-1-1-1-2-1l-1-1h2 5 1c3-1 6-2 9-5 2-2 2-4 2-7z" class="D"></path><defs><linearGradient id="Y" x1="394.538" y1="95.328" x2="402.232" y2="113.73" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2d2d2d"></stop></linearGradient></defs><path fill="url(#Y)" d="M402 91h0c6 1 13 0 19 0v1l3 1h-1c0 1-1 1-1 1-1 0-5 0-5 1-3 0-5 1-7 2l-1 1h3 2 1 0c2 1 4 2 5 4h0v1c1 1 1 1 1 2l-1 1v-1c-1-3-2-4-5-6h-4c-1 1-3 2-4 3-3 3-5 7-7 11l-2 2c-5 3-8 6-15 4-1 0-2-1-3-1 2-1 4 0 6 0s4-1 5-2c2-1 4-2 6-4 5-6 5-13 5-21z"></path><path d="M410 97c-2 1-3 2-4 3v-1c5-5 9-7 15-7l3 1h-1c0 1-1 1-1 1-1 0-5 0-5 1-3 0-5 1-7 2z" class="I"></path><path d="M411 169c6-1 9-1 13 2h2v2 1l6 9v10c-1 3-2 6-5 8-4 3-7 4-11 3s-8-3-10-6l-2-4h-1v1l-1-1c-1-3-2-8-1-11 1 0 0-1 0-1l2-2c1-2 2-4 2-7 1-1 1-1 1-2h1l4-2z"></path><path d="M406 171h1l4-2 4 1c-3 1-6 1-7 4l-1 1c-1 0-1-1-2-2 1-1 1-1 1-2z" class="Y"></path><path d="M411 169c6-1 9-1 13 2h2v2 1c-4-4-6-4-11-4l-4-1z" class="C"></path><path d="M403 180v6c-1 3 0 5 1 8h-1v1l-1-1c-1-3-2-8-1-11 1 0 0-1 0-1l2-2z" class="Y"></path><path d="M422 194c-1 1-1 2-3 2-3 1-6 1-9-1-1 0-3-2-4-3 0-1-1-3-1-4h-1v-3-3h1v5h1c0 2 2 2 3 4h0l-1 1 6 3h0 6l2-1z" class="Q"></path><path d="M409 191c3 2 6 3 10 3 0-1 1-1 1-1h1c1-1 1-2 2-3h0 0c0-2 0-3-1-4 0-1-1-1-2-2-2 0-8 1-9 0-1 0-1-1-2-1-1-1-2-3-2-5v-1c0 1 0 1 1 2 0 1 1 2 2 3 5 2 11-2 14 4v5c-1 1-1 2-2 3h0l-2 1h-6 0l-6-3 1-1z" class="U"></path><path d="M221 132c0-1 0-1 1-1 1 1 2 2 4 3h0l1 1c2 0 4 0 6 1l2 1h1v-2c0 1 0 1 1 2v-6h0c0 2 0 4 1 6v1c0 1 1 2 1 2l2 1c0 1 0 2 1 2 0 0 0-1 1-1h0c0 2 0 2 1 3v2 5l1 3h0c0 1 1 2 1 3 1 2 1 4 3 6v2c1 3 3 5 5 8h0c1 1 4 3 4 5v1c-2 1-7 4-10 3v6l1 1h2c2 0 4 1 6 1l2 1h0c-1 2-3 3-4 4-4 2-8 4-12 5-1 1-2 1-2 1l-2 1c0 1-1 2-2 2s-2 0-3 1-2 1-4 2h1l-1 1c1 1 2 1 4 1h1c1-1 2-1 2-1 0 1 0 2-1 2h-2v1h-2v1l-5 1c-4 2-13 4-18 3h0v-1c1-1 3 0 3-1h-2-1c-5-1-10-2-15-5-3-2-5-5-8-7-1-2-3-4-4-6v-1h1l-2-2v-1c-1-1-1-3-3-5v-1-2h-1v-2h-1v1c-1-2-2-2-2-4l-1-1h1c2-1 3-3 4-5 0-1 1-2 0-3h0c1-3 3-6 5-8l2-3c1-2 1-3 2-4h1c1 0 2-1 2-2h2l6-9c1-1 2-2 3-2 0 1-2 2-2 3 0 2 0 3 1 5l1 1 1-2s0-1-1-1v-1-3c1-1 2-1 2-2h0v-1c0-1 0-1 1-1 1-1 2-2 3-2 3-1 5-1 7-4l3-1h1 4l-1-2z"></path><path d="M195 155h1l1 2h0-1l-2 1h-1c1-1 1-2 2-3z" class="D"></path><path d="M199 146c0 2 0 3 1 5l1 1 1 3c-1 0-2-1-3-1 0-3-1-5 0-8z" class="B"></path><path d="M186 164h1c-1 3-2 6-2 9-1-1-1-3-1-5l2-4z" class="X"></path><path d="M215 182c5-1 9-2 14-2-1 1-1 0-1 1h1s1 0 1 1c-2 0-3 0-5 1h0c-1 0-1 0-2-1-2 0-5 1-6 1h-1c0-1 0-1-1-1z" class="H"></path><path d="M215 182c1 0 1 0 1 1-2 2-5 3-6 5-1 1-2 2-3 4l-2-1c2-4 6-7 10-9z" class="L"></path><path d="M229 180l13-1c0 1-1 2-1 3h-3v-1l-8 1c0-1-1-1-1-1h-1c0-1 0 0 1-1z" class="X"></path><path d="M216 183h1c1 0 4-1 6-1 1 1 1 1 2 1h0c-3 0-6 1-9 2l-6 3c1-2 4-3 6-5z" class="M"></path><path d="M221 176h4v1c3 0 6 0 8 1-5 0-10 0-16 1h-1v-1c1-1 3-1 5-2h0z" class="X"></path><path d="M199 154c1 0 2 1 3 1 1 2 2 4 4 5 1 2 4 4 4 5h-1c-4-2-8-7-10-11z" class="H"></path><path d="M189 173v1c-1 2-1 3-2 4 1 1 2 2 3 2h2v3h4c-2 1-6 1-8-1-1 0-2-1-2-3s1-4 3-6z" class="E"></path><path d="M184 168c0 2 0 4 1 5 0 6 1 10 4 16l-1 1-3-5c-1-4-2-7-2-11v-3c0-1 1-2 1-3z" class="L"></path><path d="M190 154h2l-5 10h-1c0-2-1-3-1-4 1-2 1-3 2-4h1c1 0 2-1 2-2z" class="H"></path><path d="M225 176h15c3 1 7 3 10 4-1 1-3 1-4 0 0-1-1-1-2-1-3-1-7-1-11-1-2-1-5-1-8-1v-1z" class="J"></path><path d="M189 189h0c1 1 2 3 3 5 4 4 9 7 14 7h2l-1 1h-1-1 0s-1 0-1 1c-1-1-2-1-4-2-1 0-2-1-3-2h-1l-1-1c-2-1-4-4-5-5l-1-1s-1-1-1-2l1-1z" class="E"></path><path d="M192 180c10-1 19-3 29-4-2 1-4 1-5 2v1h1l-11 3c-3 0-7 1-10 1h-4v-3z" class="H"></path><path d="M202 167h1 2 1c-1 1-1 1-2 1h0c0 1-1 1-1 2-1 1-1 1-1 2l3 3c3 1 8 0 12-1 3 1 7 0 10 1h0c-2 0-3 0-4 1h-1-1 0c-10 1-19 3-29 4h-2c-1 0-2-1-3-2 1-1 1-2 2-4v-1c4-4 8-5 13-6z"></path><path d="M202 167h1 2 1c-1 1-1 1-2 1h0c-6 1-11 2-15 6v-1c4-4 8-5 13-6z" class="B"></path><path d="M210 165c2 1 4 1 6 2 3 0 6-1 9-1 4 0 9 1 13 2h1 1c2 1 5 2 7 4h0l3 2c1 2 2 4 4 6h-1l-1-1-1 1h0-1c-3-1-7-3-10-4h-15-4 1 1c1-1 2-1 4-1h0c-3-1-7 0-10-1-4 1-9 2-12 1l-3-3c0-1 0-1 1-2 0-1 1-1 1-2h0c1 0 1 0 2-1h-1-2-1c1-1 7 0 8-2z"></path><path d="M217 174c-4 1-9 2-12 1l-3-3c0-1 0-1 1-2v1h0c1 1 2 2 3 2 2 1 4 1 6 0h9c-2 1-3 1-4 1z" class="M"></path><path d="M221 173c5 0 10 0 15 1 1 1 2 1 4 2h-15-4 1 1c1-1 2-1 4-1h0c-3-1-7 0-10-1 1 0 2 0 4-1z" class="Z"></path><path d="M210 165c2 1 4 1 6 2 3 0 6-1 9-1 4 0 9 1 13 2h1 1c2 1 5 2 7 4h0l3 2c1 2 2 4 4 6h-1l-1-1-1 1c-8-8-19-10-29-11-4 0-7-1-11-1h-5l-3 3h0v-1c0-1 1-1 1-2h0c1 0 1 0 2-1h-1-2-1c1-1 7 0 8-2z" class="F"></path><path d="M245 155h0c0 1 1 2 1 3 1 2 1 4 3 6v2c1 3 3 5 5 8h0c1 1 4 3 4 5v1c-2 1-7 4-10 3v6h0c-2-1-3 0-5 0-3 1-7 2-11 4l-4 2c-6 4-13 6-21 7l1-1v-1h0 0c-1 0-1-1-2-1-1-2-1-6-1-8l2 1c1-2 2-3 3-4l6-3c3-1 6-2 9-2 2-1 3-1 5-1l8-1v1h3c0-1 1-2 1-3l4 1c1 1 3 1 4 0h1 0l1-1 1 1h1c-2-2-3-4-4-6v-1c0-1-2-3-2-4 0-2-1-3-1-5-1-2-3-6-2-9z"></path><path d="M245 155h0c0 1 1 2 1 3 1 2 1 4 3 6v2c1 3 3 5 5 8h0c1 1 4 3 4 5v1c-2 1-7 4-10 3h-1c-1-1-2-1-3-1-6 0-16 2-20 5v1l-1-1c1 0 1-1 2-2 2-2 9-3 13-3h3c0-1 1-2 1-3l4 1c1 1 3 1 4 0h1 0l1-1 1 1h1c-2-2-3-4-4-6v-1c0-1-2-3-2-4 0-2-1-3-1-5-1-2-3-6-2-9z" class="W"></path><path d="M245 155h0c0 1 1 2 1 3 1 2 1 4 3 6v2c1 3 3 5 5 8h0c1 1 4 3 4 5l-4-4-1-1c0 2 2 3 3 5h-1l-5-6c0-1-2-3-2-4 0-2-1-3-1-5-1-2-3-6-2-9z" class="M"></path><path d="M247 183h1v6h0c-2-1-3 0-5 0-3 1-7 2-11 4l-4 2-1-1h0l-1-2-1-1h0-3l-5 1-1-1c4-1 7-1 10-2 4-1 7-3 11-4 3 0 8 0 10-2z" class="G"></path><path d="M222 191c2 0 3 0 5-1 6-1 11-4 17-5 1 0 2 0 3 1h0c-1 1-3 1-4 2-3 1-6 1-10 3-2 0-4 2-6 3h0l-1-2-1-1h0-3z" class="R"></path><path d="M208 201v-1h0 0c-1 0-1-1-2-1-1-2-1-6-1-8l2 1c0 1-1 2 0 3 3-1 6-3 9-4h0l1 1 5-1h3 0l1 1 1 2h0l1 1c-6 4-13 6-21 7l1-1z"></path><path d="M208 201v-1h0 0c-1 0-1-1-2-1-1-2-1-6-1-8l2 1c0 1-1 2 0 3 3-1 6-3 9-4h0l1 1c-2 1-9 3-10 5l3 3c4-1 8-2 11-3 2-1 4-1 6-3h0l1 1c-6 4-13 6-21 7l1-1z" class="B"></path><path d="M185 160c0 1 1 2 1 4l-2 4c0 1-1 2-1 3v3c0 4 1 7 2 11l3 5c0 1 1 2 1 2l1 1c1 1 3 4 5 5l1 1h1c1 1 2 2 3 2 2 1 3 1 4 2 0-1 1-1 1-1h0 1 1c8-1 15-3 21-7l4-2c4-2 8-3 11-4 2 0 3-1 5 0h0l1 1h2c2 0 4 1 6 1l2 1h0c-1 2-3 3-4 4-4 2-8 4-12 5-1 1-2 1-2 1l-2 1c0 1-1 2-2 2s-2 0-3 1-2 1-4 2h1l-1 1c1 1 2 1 4 1h1c1-1 2-1 2-1 0 1 0 2-1 2h-2v1h-2v1l-5 1c-4 2-13 4-18 3h0v-1c1-1 3 0 3-1h-2-1c-5-1-10-2-15-5-3-2-5-5-8-7-1-2-3-4-4-6v-1h1l-2-2v-1c-1-1-1-3-3-5v-1-2h-1v-2h-1v1c-1-2-2-2-2-4l-1-1h1c2-1 3-3 4-5 0-1 1-2 0-3h0c1-3 3-6 5-8l2-3z" class="a"></path><path d="M206 202h0c6 2 13-1 19-3l1 1h-1l-1 1h-1l-3 1s-1 0-1 1h-1-1-2c-1 1-1 1-2 1-1 1-6 0-8 0l-1-1c0-1 1-1 1-1h0 1z" class="M"></path><path d="M241 193h2l1 1c-1 0-2 0-3 1-5 1-10 3-15 5l-1-1c5-3 10-5 16-6z" class="B"></path><path d="M185 160c0 1 1 2 1 4l-2 4c0 1-1 2-1 3v3c0 4 1 7 2 11v2 1 1h0c-1-1-1-2-2-3-1-4-2-7-1-11v-8c1-1 1-2 1-4l2-3z" class="F"></path><path d="M228 195l4-2c4-2 8-3 11-4 2 0 3-1 5 0v1c-2 1-4 2-7 3-6 1-11 3-16 6-6 2-13 5-19 3h0 1c8-1 15-3 21-7z"></path><path d="M178 171h0c1-3 3-6 5-8 0 2 0 3-1 4-2 5-2 9-2 14 0 4 2 7 3 10h-1v-1h-1 0-1 1v1 1 1h0c-1-1-1-3-3-5v-1-2h-1v-2h-1v1c-1-2-2-2-2-4l-1-1h1c2-1 3-3 4-5 0-1 1-2 0-3z" class="P"></path><path d="M248 189h0l1 1h2c2 0 4 1 6 1l2 1h0c-1 2-3 3-4 4-4 2-8 4-12 5-1 1-2 1-2 1l-2 1c0 1-1 2-2 2s-2 0-3 1-2 1-4 2h-5 0l-11 2h-4v-1h3-2-1c1-1 1-1 1-2 1-1 4-1 6-2l10-2-1-1h0l6-3 3-1 4-2h2v-1c1-1 2-1 3-1l-1-1h-2c3-1 5-2 7-3v-1z" class="Y"></path><path d="M241 196l1-1h1c1 0 3 0 4-1h1 2l-12 5c-2 1-5 1-7 2-1 1-2 1-4 2l-1-1h0l6-3 3-1 4-2h2z" class="O"></path><path d="M248 189h0l1 1h2c2 0 4 1 6 1-1 1-4 3-4 3h-3-2-1c-1 1-3 1-4 1h-1l-1 1v-1c1-1 2-1 3-1l-1-1h-2c3-1 5-2 7-3v-1z" class="F"></path><path d="M248 189h0l1 1h2v1c-1 1-1 1-3 1l-2 1h0-1l-1 1-1-1h-2c3-1 5-2 7-3v-1z" class="H"></path><defs><linearGradient id="Z" x1="246.294" y1="201.558" x2="245.83" y2="192.623" xlink:href="#B"><stop offset="0" stop-color="#9b9b9d"></stop><stop offset="1" stop-color="#b8b6b5"></stop></linearGradient></defs><path fill="url(#Z)" d="M257 191l2 1h0c-1 2-3 3-4 4-4 2-8 4-12 5-1 1-2 1-2 1l-2 1h-2c-1-1-2-1-4-1h-1v-1c7-1 15-4 21-7 0 0 3-2 4-3z"></path><path d="M233 202c2 0 3 0 4 1h2c0 1-1 2-2 2s-2 0-3 1-2 1-4 2h-5 0l-11 2h-4v-1h3-2-1c1-1 1-1 1-2 1-1 4-1 6-2v1c2 0 6-2 8 0 1-1 3-1 4-2s2-2 3-2h1z"></path><path d="M213 209c1 0 12-2 13-3 0 1 0 1-1 2h0l-11 2h-4v-1h3z" class="E"></path><path d="M217 205v1c2 0 6-2 8 0-3 0-5 1-8 2h-6v1h-1c1-1 1-1 1-2 1-1 4-1 6-2z" class="K"></path><path d="M233 202c2 0 3 0 4 1h2c0 1-1 2-2 2s-2 0-3 1-2 1-4 2h-5c1-1 1-1 1-2 2 0 3 0 4-1h4c0-2 0-2-1-3z" class="F"></path><path d="M181 193h0v-1-1-1h-1 1 0 1v1h1c4 6 9 10 15 14l6 3c-1 1-2-1-3 1 2 0 6 2 8 1h5l11-2h0 5 1l-1 1c1 1 2 1 4 1h1c1-1 2-1 2-1 0 1 0 2-1 2h-2v1h-2v1l-5 1c-4 2-13 4-18 3h0v-1c1-1 3 0 3-1h-2-1c-5-1-10-2-15-5-3-2-5-5-8-7-1-2-3-4-4-6v-1h1l-2-2v-1z"></path><path d="M230 209h0c1 1 2 1 4 1h1c1-1 2-1 2-1 0 1 0 2-1 2h-2v1h-2v1l-5 1c-1-1-2 0-4 0v-1c3-1 5-2 7-4z" class="M"></path><path d="M209 215h5c3-1 6-2 9-2v1c2 0 3-1 4 0-4 2-13 4-18 3h0v-1c1-1 3 0 3-1h-2-1z" class="U"></path><defs><linearGradient id="a" x1="214.15" y1="206.607" x2="219.588" y2="214.261" xlink:href="#B"><stop offset="0" stop-color="#595a5a"></stop><stop offset="1" stop-color="#7b7a7a"></stop></linearGradient></defs><path fill="url(#a)" d="M225 208h5 1l-1 1h0-1c-1 0-3 2-5 2-6 3-14 3-21 1l1-1h3c1 1 2 0 3 0 2 0 4 1 5 0l6-1c2 0 3-1 4-1v-1h0z"></path><path d="M194 207h0c1-2-3-3-4-5l2 1v-1c2 1 3 3 5 4l1-1 6 3c-1 1-2-1-3 1 2 0 6 2 8 1h5l11-2v1c-1 0-2 1-4 1l-6 1c-1 1-3 0-5 0-1 0-2 1-3 0h-3l-1 1c-3-1-6-3-9-5z" class="P"></path><path d="M192 203v-1c2 1 3 3 5 4l1-1 6 3c-1 1-2-1-3 1-1-1-2-1-3-2-3-1-4-2-6-4z" class="K"></path><path d="M181 193h0v-1-1-1h-1 1 0 1v1h1c4 6 9 10 15 14l-1 1c-2-1-3-3-5-4v1l-2-1c1 2 5 3 4 5h0c-5-3-9-6-12-11h1l-2-2v-1z" class="U"></path><path d="M221 132c0-1 0-1 1-1 1 1 2 2 4 3h0l1 1c2 0 4 0 6 1l2 1h1v-2c0 1 0 1 1 2v-6h0c0 2 0 4 1 6v1c0 1 1 2 1 2l2 1c0 1 0 2 1 2 0 0 0-1 1-1h0c0 2 0 2 1 3v2 5l1 3c-1 3 1 7 2 9 0 2 1 3 1 5 0 1 2 3 2 4v1l-3-2h0c-2-2-5-3-7-4h-1-1c-4-1-9-2-13-2-3 0-6 1-9 1-2-1-4-1-6-2 0-1-3-3-4-5-2-1-3-3-4-5l-1-3 1-2s0-1-1-1v-1-3c1-1 2-1 2-2h0v-1c0-1 0-1 1-1 1-1 2-2 3-2 3-1 5-1 7-4l3-1h1 4l-1-2z"></path><path d="M214 138h7c-5 1-10 2-14 5-2 1-3 2-5 3l-1 3v-1-3c1-1 2-1 2-2h0c2-2 5-3 8-4 1 0 2-1 3-1z" class="H"></path><path d="M228 153l5-6 5 3c-1 3-3 5-6 7v-2l1-3h0c-2 1-3 1-5 1z" class="F"></path><path d="M233 142h5c3 4 3 6 4 10 0 4 1 6 1 9 1 1 1 3 1 4-2-4-3-8-3-12-1-2-1-4-3-6-1-2-3-4-5-5z" class="L"></path><path d="M224 136c2 0 4 0 6 1l6 3c0 1 1 1 2 2h-5c-5-2-7-4-12-4h-7c3-2 7-2 10-2z" class="J"></path><path d="M224 136c2 0 4 0 6 1l6 3c-1 0-2 0-3-1-2 0-7-1-9-3z" class="G"></path><path d="M221 132c0-1 0-1 1-1 1 1 2 2 4 3h0l1 1c2 0 4 0 6 1l-1 1h-2c-2-1-4-1-6-1-3 0-7 0-10 2-1 0-2 1-3 1-3 1-6 2-8 4v-1c0-1 0-1 1-1 1-1 2-2 3-2 3-1 5-1 7-4l3-1h1 4l-1-2z" class="W"></path><path d="M202 150c2 5 6 10 11 13 9 4 18 0 27 5h-1-1c-4-1-9-2-13-2-3 0-6 1-9 1-2-1-4-1-6-2 0-1-3-3-4-5-2-1-3-3-4-5l-1-3 1-2z" class="G"></path><defs><linearGradient id="b" x1="249.909" y1="169.18" x2="233.734" y2="136.69" xlink:href="#B"><stop offset="0" stop-color="#afaeae"></stop><stop offset="1" stop-color="#d2d0d1"></stop></linearGradient></defs><path fill="url(#b)" d="M236 135c0 1 0 1 1 2v-6h0c0 2 0 4 1 6v1c0 1 1 2 1 2l2 1c0 1 0 2 1 2 0 0 0-1 1-1h0c0 2 0 2 1 3v2 5l1 3c-1 3 1 7 2 9 0 2 1 3 1 5 0 1 2 3 2 4v1l-3-2c-1-2-2-5-3-7 0-1 0-3-1-4 0-3-1-5-1-9-1-4-1-6-4-10-1-1-2-1-2-2l-6-3h2l1-1 2 1h1v-2z"></path><path d="M242 143s0-1 1-1h0c0 2 0 2 1 3v2 5-2c-1-1-2-3-2-4v-3z" class="B"></path><defs><linearGradient id="c" x1="222.215" y1="140.279" x2="223.986" y2="153.357" xlink:href="#B"><stop offset="0" stop-color="#858485"></stop><stop offset="1" stop-color="#afafaf"></stop></linearGradient></defs><path fill="url(#c)" d="M206 147v-1c2-1 5-2 7-3s4-2 7-3h4c4 1 9 5 12 7-3-1-6-2-9-4l-5 10c0 1-1 2-2 3l-2-9c0-1 0-3-1-4l-7 3c1 1 1 0 2 1 0 0 1 1 1 2 0 0-1-1-2-1-1-1-1-1-2-1h-3z"></path><defs><linearGradient id="d" x1="214.047" y1="145.935" x2="225.638" y2="159.668" xlink:href="#B"><stop offset="0" stop-color="#bebdbc"></stop><stop offset="1" stop-color="#e3e1e3"></stop></linearGradient></defs><path fill="url(#d)" d="M206 147h3c1 0 1 0 2 1 1 0 2 1 2 1 1 3 3 6 5 7s4 1 6 0c2 0 3-2 4-3 2 0 3 0 5-1h0l-1 3v2c-1 1-3 1-4 2-4 2-9 2-12 0-5-2-8-7-10-12z"></path><path d="M118 457v1c3 4 5 8 7 12 1 3 2 5 4 8 1 4 3 9 5 13v-4c-1-3-1-6 1-8 1-3 2-4 5-5 2-1 3 0 5 0h-1l1-1-1-1 1-1 2 1c1 0 2 0 2-1l2 1c1 0 12 2 12 2h1c1 1 3 1 4 1l3-1-1-1h0c3 0 5-1 8-2l5-3c0 1-1 2-2 3v1h0v1c0 1-1 1-1 2l1 1c-2 4-5 8-7 12-5 9-8 18-10 28l-1 8c-2 9-1 20 1 29 1 3 1 6 3 8l-1 1c1 4 3 8 5 11 5 10 12 20 21 26v-1l1-1c1 1 3 3 5 4h2c1 0 3 1 3 2h-1l2 2h-1l1 1h0c-2 1-5-2-7-3l-1 1c-1-1-3-2-4-3-6-5-13-11-17-17h0c-3-3-3-5-5-8 0-1-1-3-2-4 0-1-1-2-1-3h0c1 3 2 6 4 8v2 1c0-1 0-1-1-2l-3-6c-1-2-1-3-1-4v1l-1 1c2 5 4 10 6 16 0 2 1 4 1 5-3 5-9 7-13 10l1 1-2 1h1v1l-2 1c-2 1-3 2-5 3 0 1-1 2-2 3l-1 1c1 0 1 0 1 1-1 2-1 3-1 5-3 7-8 12-16 15-2 1-4 2-7 2h-1c-1 0-2 0-3 1h-3-1c-1-1-2-1-3-1h0c-2 0-3 0-4-1-2 0-6-2-8-3s-4-3-6-4h0c-5-2-7-6-11-9h-1l-3-4c-1-3-2-5-3-8 0-3 0-6 2-8s4-3 6-3l1-1 1 1c-1-2 0-6 0-9h0l1-1 1 1 1 3 1 1c2-1 3-3 5-4 2 2 3 4 4 6 4 6 9 8 16 10 6 1 12 1 18-1h0c1-1 2-1 3-2h1l5-4h1v-1c2-1 4-3 5-4s2-1 3-1c3-8 3-15 2-22-2-14-8-28-13-42l-5-14-19-45v-3l-1-2v-4z" class="I"></path><path d="M109 616v-1c-1-1-2-1-3-2h1 4c0 1 0 1 1 1l1 1-1 1-1-1-1 2-1-1zm52-134h1-1c0-1-1-1-1-1h-1c-1-1-1-1-2-1s-1-1-2-1l-7-3h0 1c1 0 2 0 2 1 1 0 2 0 3 1h2v1h2c2 1 4 2 6 2h1l2 1h0 2l1-1c1 0 2 0 3-1s1-1 2-1c0 1-1 2-2 3h0c-1 0-3 1-4 2h-4l-1-1c-1 0-2 0-3-1z" class="R"></path><path d="M164 556c-1-2-1-5-2-8-1-6-2-12-1-18 0-5 1-11 2-16 0 0 0 1 1 2l-1 8c-2 9-1 20 1 29 1 3 1 6 3 8l-1 1-2-6z" class="G"></path><path d="M163 514c2-9 5-16 8-24 3-5 5-10 9-15l1 1c-2 4-5 8-7 12-5 9-8 18-10 28-1-1-1-2-1-2z" class="V"></path><path d="M147 521c-2-3-2-8-3-11 0-1 2-3 3-3 3-2 7-3 11-2h0l-2 2c-3 1-7 5-8 8h0c-1 2 0 4-1 6zm36-53c0 1-1 2-2 3v1h0c-3 1-4 4-7 6-1 1-2 2-4 2h0-5c-1 0-1 0-2-1h-1-1l-3-1c-1 0-1 0-2-1h-1c-1 0-2-1-4-2h-1-1l-2-2h-2l-1-1 1-1 2 1c1 0 2 0 2-1l2 1c1 0 12 2 12 2h1c1 1 3 1 4 1l3-1-1-1h0c3 0 5-1 8-2l5-3z"></path><path d="M170 473c3 0 5-1 8-2-2 2-6 4-9 4h-1l3-1-1-1h0z" class="C"></path><path d="M147 472c1 0 2 0 2-1l2 1c1 0 12 2 12 2h1c1 1 3 1 4 1h1c-7 1-16 0-22-3z" class="T"></path><path d="M95 606c1 1 2 1 2 2l1 1s0 1 1 1v-1c1 0 5 3 6 4-2 2-4 4-4 7v3 1c0 2 2 4 3 5-3-1-6-3-9-6-1-1-2-3-3-5-2-2-2-5-2-7 1-2 3-4 5-5h0z"></path><path d="M95 606c1 1 2 1 2 2l1 1s0 1 1 1c-1 0-1 1-2 1s-1 0-2-1v-2-2h0z" class="D"></path><path d="M82 614l-3-8c0-3 0-6 2-8s4-3 6-3h1l-1 1h1v1c-1 1-2 1-3 2v1c1 1 2 1 4 1 0-1 1-1 1-2v-5c0 2 1 4 3 6 1 1 2 3 2 4l-1 1 1 1h0c-2 1-4 3-5 5 0 2 0 5 2 7 1 2 2 4 3 5v2l2 2c-5-2-7-6-11-9h-1l-3-4z"></path><path d="M94 605l1 1h0c-2 1-4 3-5 5 0 2 0 5 2 7 1 2 2 4 3 5v2l-4-5c-2-3-3-6-2-9 1-2 3-5 5-6zm-12 9l-3-8c0-3 0-6 2-8s4-3 6-3h1l-1 1c-2 1-5 2-5 4-2 4-1 10 1 13l3 5h-1l-3-4z" class="C"></path><defs><linearGradient id="e" x1="96.86" y1="583.335" x2="145.578" y2="605.409" xlink:href="#B"><stop offset="0" stop-color="#bfbdbe"></stop><stop offset="1" stop-color="#f2f1f2"></stop></linearGradient></defs><path fill="url(#e)" d="M98 586c2 2 3 4 4 6 4 6 9 8 16 10 6 1 12 1 18-1h0c1-1 2-1 3-2h1l5-4h1v-1c2-1 4-3 5-4s2-1 3-1c-3 8-9 16-17 20-9 3-20 4-28 1-6-2-12-7-14-13-1-2-2-5-2-7 2-1 3-3 5-4z"></path><path d="M158 484c-4-2-9 1-14 2-1-1-2-2-3-4s-1-5 0-7c3-1 6 2 9 3l5 2c1 1 1 1 2 1h1c1 1 1 1 2 1l1 1v-1c1 1 2 1 3 1l1 1h4c1-1 3-2 4-2-2 3-5 7-6 10-3 5-4 10-5 15-4 13-4 25-3 39 1 3 2 8 5 10l2 6c1 4 3 8 5 11 5 10 12 20 21 26v-1l1-1c1 1 3 3 5 4h2c1 0 3 1 3 2h-1l2 2h-1l1 1h0c-2 1-5-2-7-3l-1 1c-1-1-3-2-4-3-6-5-13-11-17-17h0c-3-3-3-5-5-8 0-1-1-3-2-4 0-1-1-2-1-3h0c1 3 2 6 4 8v2 1c0-1 0-1-1-2l-3-6c-1-2-1-3-1-4v1l-1 1c2 5 4 10 6 16 0 2 1 4 1 5-3 5-9 7-13 10l1 1-2 1h1v1l-2 1c-2 1-3 2-5 3 0 1-1 2-2 3l-1 1c1 0 1 0 1 1-1 2-1 3-1 5-3 7-8 12-16 15-2 1-4 2-7 2h-1c-1 0-2 0-3 1h-3-1c-1-1-2-1-3-1h0c-2 0-3 0-4-1-2 0-6-2-8-3s-4-3-6-4h0l-2-2v-2c3 3 6 5 9 6-1-1-3-3-3-5v-1h0c0-1 0-1 1-2 0-2 5-4 7-5l1 1 1-2 1 1 1-1c7 1 14-1 20-2 3-1 6-1 8-2h2 0-1c1-2 2-3 3-5 5-5 9-9 12-16 2-6 2-11 2-18 0-11-4-23-7-34-1-3-1-7-3-9v-1-2l-1-1c-1-1-1-3-1-4 1-2 0-4 1-6h0c1-3 5-7 8-8h0c1 0 3 0 4-1v-1h-1c-1-1-3-1-5-1-5 0-8 1-11 5-1-5-3-9-5-14-1-3-3-6-3-10-1-4 1-7 4-10 0 3 0 6 1 9 4 8 13-1 18 1v-1z"></path><path d="M155 526h1 1l-1 2c-1 0-1 0-2-1l1-1z" class="Z"></path><path d="M156 489h1 0v2s-1 0-1 1c-1-1 0-2 0-3z" class="S"></path><path d="M152 607v1c0 1-1 2-2 3l-1 1v-2c0-1 2-2 3-3z" class="Z"></path><path d="M156 528h-1l-4 1-1-1 1-1c1-1 2-1 4-1l-1 1c1 1 1 1 2 1z" class="N"></path><path d="M142 496h2 0l-2 3h-2c0-1 0-2 1-3h1z" class="P"></path><path d="M124 616h1c1 1 1 1 1 2l-1 1h0c-1 1-1 1-2 0 0-2 0-2 1-3zm-14 1l1-2 1 1 4 2h0c-1 1-4 1-6 0v-1z" class="D"></path><path d="M158 484c3 1 5 3 6 6 1 0 1 1 1 2l-1 1c-1-1-1-2-2-3-1-2-2-4-4-5v-1z" class="C"></path><path d="M152 607h0c2-2 4-4 7-6l1 1-2 1h1v1l-2 1c-2 1-3 2-5 3v-1z" class="I"></path><path d="M147 521c1-2 0-4 1-6h0c1-3 5-7 8-8h0v1h-1c-3 2-4 4-6 7v14-1-2l-1-1c-1-1-1-3-1-4z" class="Q"></path><path d="M124 628c1-2 2-4 5-6 1-1 1-3 2-4 2-3 4-6 8-5 1 0 2 0 3 1 2 2 2 5 2 7-1 3-4 7-7 9s-7 4-11 4c-1 0-7 0-8-1h2c1-1 4 0 6 0 4-1 8-2 11-5 3-2 5-5 5-8 0-1 0-3-1-4s-2-1-3-1-3 0-4 2c-1 1-4 4-4 5 1 0 2 0 3 1-1 1-2 1-3 1l-1-1-3 4-2 2v-1z" class="I"></path><path d="M101 623h0c0-1 0-1 1-2 0 2 1 5 3 6 2 2 4 3 7 3 2-1 5-2 7-4 1-2 1-3 2-4h1c0 1-1 3-2 4-3 3-5 3-8 5 3 0 7 1 10-2l2-1v1c0 1-1 1-1 2v1c1 1 2 1 3 1-2 0-5-1-6 0h-2c1 1 7 1 8 1h0-6c2 1 5 0 6 1h-1c-1 0-2 0-3 1h-3-1c-1-1-2-1-3-1h0c-2 0-3 0-4-1-2 0-6-2-8-3s-4-3-6-4h0l-2-2v-2c3 3 6 5 9 6-1-1-3-3-3-5v-1z" class="S"></path><path d="M133 623c-1-1-2-1-3-1 0-1 3-4 4-5 1-2 3-2 4-2s2 0 3 1 1 3 1 4c0 3-2 6-5 8-3 3-7 4-11 5-1 0-2 0-3-1v-1c0-1 1-1 1-2l2-2 3-4 1 1c1 0 2 0 3-1z"></path><path d="M133 623h1v1c-1 1-2 1-2 1-1 0-2-1-2-1 1 0 2 0 3-1z" class="N"></path><path d="M126 627h1c1 0 1-1 2-1 0 1 0 1-1 2s-3 2-5 3c0-1 1-1 1-2l2-2z" class="Q"></path><path d="M159 546c1 3 2 8 5 10l2 6c1 4 3 8 5 11 5 10 12 20 21 26v-1l1-1c1 1 3 3 5 4h2c1 0 3 1 3 2h-1l2 2h-1l1 1h0c-2 1-5-2-7-3l-1 1c-1-1-3-2-4-3-6-5-13-11-17-17h0c-3-3-3-5-5-8 0-1-1-3-2-4 0-1-1-2-1-3h0c1 3 2 6 4 8v2 1c0-1 0-1-1-2l-3-6c-1-2-1-3-1-4v1l-1 1-1-2h0c0 2-1 3 0 5v8 1 1l-1 1v1 1 1l-1 3c-1 1-1 2-2 3h0v-1-1l1-3c0-1 0-1 1-1h0v-2-1-1c1-2 0-6 0-8 0-1 1-2 0-3v-5c0-1 0-1-1-1v-1-3c-1-1-1-2-1-3-1-5-1-9-1-13z" class="R"></path><path d="M175 584c1 0 2 1 3 2 3 4 7 8 12 12 1 1 3 3 4 3h0l-2-2v-1l1-1c1 1 3 3 5 4h2c1 0 3 1 3 2h-1l2 2h-1l1 1h0c-2 1-5-2-7-3l-1 1c-1-1-3-2-4-3-6-5-13-11-17-17z" class="N"></path><path d="M200 601c1 0 3 1 3 2h-1l2 2h-1c-2-1-4-2-5-4h2z" class="C"></path><path d="M273 400c1-2 0-5 1-7l1 1 1 2c-1 2-1 4-1 5v1c2 3 3 7 5 9h2 0l1 1c1 1 1 2 2 3l3 5c-1 0-1 1-1 1 0 1 1 2 1 3v1c1 3 3 8 1 12-1 2-3 3-5 4 3 0 6 1 8 3l6 3 9 6 9 6c3 1 5 3 7 5 1 2 3 4 4 5h1l1 1 2 2-1 1-4-3c1 1 6 8 8 10 3 6 6 12 8 19h-1c2 5 3 11 4 16 1 3 1 5 1 8-1-2-1-5-2-7 0-4-2-8-3-12h-1l-1 1c0-1 0-2-1-3 0-1-1-2-2-3l-1-1v-1c0-1-1-1-1-2l-5-7h1c-2-3-5-5-7-8-1-5-3-11-7-15v1c-7-10-19-10-29-13h0c-8-2-15-2-23-2-1 1-3 1-4 1h0c-4 1-9 2-12 3-6 1-12 4-18 7-9 5-16 10-23 18-2 2-5 5-7 8-4 6-7 14-9 21-1 2-1 4-2 5h0c-1-2 0-3-1-5l-1 2c-1 0-2 1-3 2-2 3-4 7-6 10-2 4-4 9-5 13h-1c-2 8-1 16 1 25 0 1 0 1-1 2l-2-8c-1 1-1 1-1 2 1 3 2 6 3 10 2 3 2 7 4 10v1c2 4 5 7 8 10 1 2 3 4 5 5 1 2 2 3 4 4l-1 1v1c-9-6-16-16-21-26-2-3-4-7-5-11l1-1c-2-2-2-5-3-8-2-9-3-20-1-29l1-8c2-10 5-19 10-28 2-4 5-8 7-12l-1-1c0-1 1-1 1-2v-1h0v-1c1-1 2-2 2-3l-5 3c-3 1-5 2-8 2l1-1s1 0 2-1h1l1-1c1 0 1-1 1-1 7-3 12-8 17-13 2-1 4-3 6-4v-3c1 0 1-1 2-1h0c2-1 3-4 5-5h0v1l2 1 10-6c1 0 2-1 3-1l18-8c8-3 17-4 24-10 3-3 8-7 9-12 1-3 1-6 1-8z"></path><path d="M246 433h1l2 2v1h-2 0l1 2-1 1v-1c-1-1-2-2-2-4l1-1z" class="H"></path><path d="M207 452h1c1 1 2 2 2 3l-2 1h0c-1 0-2-1-3-2 0-1 1-1 2-2z" class="T"></path><path d="M245 441h3c2-1 5 0 8-1 1-1 2-1 2-1 1 0 2 1 3 1h0-1-2 0c-2 1-4 1-5 3-10 1-18 3-27 7l-6 2h-1v-1c7-5 17-8 26-10z" class="V"></path><defs><linearGradient id="f" x1="236.083" y1="445.118" x2="224.061" y2="430.502" xlink:href="#B"><stop offset="0" stop-color="#b0afaf"></stop><stop offset="1" stop-color="#d0cfd0"></stop></linearGradient></defs><path fill="url(#f)" d="M273 400c1-2 0-5 1-7l1 1 1 2c-1 2-1 4-1 5v1c0 1 1 3 0 5 0 1-2 3-1 4-3 7-9 13-16 16-5 2-11 3-17 5-20 7-37 17-51 33 0 0-3 4-4 4l-3 5c-1 1-1 2-2 2l-1-1c0-1 1-1 1-2v-1h0v-1c1-1 2-2 2-3l-5 3c-3 1-5 2-8 2l1-1s1 0 2-1h1l1-1c1 0 1-1 1-1 7-3 12-8 17-13 2-1 4-3 6-4v-3c1 0 1-1 2-1h0c2-1 3-4 5-5h0v1l2 1 10-6c1 0 2-1 3-1l18-8c8-3 17-4 24-10 3-3 8-7 9-12 1-3 1-6 1-8z"></path><path d="M183 468l1-1c1 0 1 1 0 1 0 1-1 2-1 3v1l-1 1h0c1-1 2-1 2-2 1-1 1-2 2-2l-3 5c-1 1-1 2-2 2l-1-1c0-1 1-1 1-2v-1h0v-1c1-1 2-2 2-3z" class="J"></path><path d="M206 443h0v1l2 1-9 7v-3c1 0 1-1 2-1h0c2-1 3-4 5-5z" class="D"></path><path d="M275 402c2 3 3 7 5 9h2 0l1 1c1 1 1 2 2 3l3 5c-1 0-1 1-1 1 0 1 1 2 1 3v1c1 3 3 8 1 12-1 2-3 3-5 4-1 0-2 0-3-1h-2c-1-1-4-1-5-1-4-1-8 0-11 0-2 0-2 0-4-1 4-2 7-3 9-7 3-4 4-8 5-12 0-3 1-6 1-8-1-1 1-3 1-4 1-2 0-4 0-5z"></path><path d="M282 415c2 2 3 3 5 6 0 1 1 2 1 3v1l-1-1h-1s-1-1-1-2l-3-7z" class="F"></path><path d="M280 411h2 0l1 1c1 1 1 2 2 3l3 5c-1 0-1 1-1 1-2-3-3-4-5-6l-2-4z" class="V"></path><path d="M274 411c-1-1 1-3 1-4 0 6-1 12-2 18-1 3-2 5-4 7h0l-1-1c3-4 4-8 5-12 0-3 1-6 1-8z" class="O"></path><path d="M269 432l-3 3c5-2 8-5 10-9 3-5 3-11 2-15-1-1-1-2-1-3 1 2 2 5 3 7 0 7-2 11-6 16 1 3 3 5 6 7 1 1 4 1 5 1 2-1 3-2 3-3 1-4-1-8-2-12h1l1 1c1 3 3 8 1 12-1 2-3 3-5 4-1 0-2 0-3-1h-2c-1-1-4-1-5-1-4-1-8 0-11 0-2 0-2 0-4-1 4-2 7-3 9-7l1 1z" class="J"></path><path d="M263 439l6-3c1-2 2-3 4-4 1 2 3 5 6 6 0 1 2 1 2 2h0-2c-1-1-4-1-5-1-4-1-8 0-11 0z"></path><path d="M245 441l14-3c2 1 2 1 4 1 3 0 7-1 11 0 1 0 4 0 5 1h2c1 1 2 1 3 1 3 0 6 1 8 3l6 3 9 6 9 6c3 1 5 3 7 5 1 2 3 4 4 5h1l1 1 2 2-1 1-4-3c1 1 6 8 8 10 3 6 6 12 8 19h-1c2 5 3 11 4 16 1 3 1 5 1 8-1-2-1-5-2-7 0-4-2-8-3-12h-1l-1 1c0-1 0-2-1-3 0-1-1-2-2-3l-1-1v-1c0-1-1-1-1-2l-5-7h1c-2-3-5-5-7-8-1-5-3-11-7-15v1c-7-10-19-10-29-13h0c-8-2-15-2-23-2h-3c-4-1-8 0-12-1h-7c-5 0-10 0-15 2l-1-1v-1h0c9-4 17-6 27-7 1-2 3-2 5-3h0 2 1 0c-1 0-2-1-3-1 0 0-1 0-2 1-3 1-6 0-8 1h-3z"></path><path d="M336 499h1v-1c2 2 3 4 4 6h-1l-1 1c0-1 0-2-1-3 0-1-1-2-2-3z" class="O"></path><path d="M292 444l6 3-1 1-7-3c0-1 1-1 2-1h0 0z" class="d"></path><path d="M298 447l9 6-1 1-9-6 1-1z" class="W"></path><path d="M330 488c2 2 4 4 5 7 1 0 2 2 2 3v1h-1l-1-1v-1c0-1-1-1-1-2l-5-7h1z" class="B"></path><path d="M279 440h2c1 1 2 1 3 1 3 0 6 1 8 3h0 0c-1 0-2 0-2 1-4-2-8-3-12-3v-1h2 0l-1-1h0z" class="c"></path><path d="M245 441l14-3c2 1 2 1 4 1 3 0 7-1 11 0 1 0 4 0 5 1h0l1 1h0-2v1l-10-1h-7l-3-1h0 2 1 0c-1 0-2-1-3-1 0 0-1 0-2 1-3 1-6 0-8 1h-3z" class="G"></path><path d="M274 439c1 0 4 0 5 1h0l1 1h0-2v1l-10-1-1-1-1 1c-1 0-2-1-3-1 2-1 4 0 5 0 2-1 4-1 6-1z" class="W"></path><path d="M258 440l3 1h-1c7 1 13 3 19 5 3 1 5 3 8 4 10 4 22 5 29 14v1 1c-7-10-19-10-29-13h0 1l-1-1v-1c-1 0-3 0-4-1v-1c-2-1-5-2-7-3-8-3-15-4-23-3 1-2 3-2 5-3z" class="X"></path><path d="M307 453l9 6c3 1 5 3 7 5 1 2 3 4 4 5h1l1 1 2 2-1 1-4-3c1 1 6 8 8 10 3 6 6 12 8 19h-1l-3-9c-4-8-9-15-15-21-2-2-6-5-8-7-2-3-6-6-9-8l1-1z" class="J"></path><path d="M316 459c3 1 5 3 7 5 1 2 3 4 4 5h1l1 1 2 2-1 1-4-3c0-1-5-6-6-6-2-1-4-3-5-4l1-1z" class="W"></path><path d="M253 443c8-1 15 0 23 3 2 1 5 2 7 3v1c1 1 3 1 4 1v1l1 1h-1c-8-2-15-2-23-2h-3c-4-1-8 0-12-1h-7c-5 0-10 0-15 2l-1-1v-1h0c9-4 17-6 27-7z"></path><path d="M226 450c6-1 11-2 17-1h-4 0c1 0 2 1 3 1-5 0-10 0-15 2l-1-1v-1z" class="J"></path><path d="M243 449c3 0 7 1 10 1 2 0 5-1 7-1 4-1 9-1 13 0 3 0 7 0 10 1 1 1 3 1 4 1v1l1 1h-1c-8-2-15-2-23-2h-3c-4-1-8 0-12-1h-7c-1 0-2-1-3-1h0 4z" class="G"></path><path d="M220 452l6-2h0v1l1 1c5-2 10-2 15-2h7c4 1 8 0 12 1h3c-1 1-3 1-4 1h0c-4 1-9 2-12 3-6 1-12 4-18 7-9 5-16 10-23 18-2 2-5 5-7 8-4 6-7 14-9 21-1 2-1 4-2 5h0c-1-2 0-3-1-5l-1 2c-1 0-2 1-3 2-2 3-4 7-6 10-2 4-4 9-5 13h-1c-2 8-1 16 1 25 0 1 0 1-1 2l-2-8c-1 1-1 1-1 2 1 3 2 6 3 10 2 3 2 7 4 10v1c2 4 5 7 8 10 1 2 3 4 5 5 1 2 2 3 4 4l-1 1v1c-9-6-16-16-21-26-2-3-4-7-5-11l1-1c-2-2-2-5-3-8-2-9-3-20-1-29v2 1l1-2c2-8 4-16 8-24 1-2 1-4 3-5 4-7 10-13 16-20 4-5 9-10 14-14 0-1 1-1 1-2 4-3 8-6 13-9v1h1z"></path><path d="M169 548l1 7c-1 1-1 1-1 2l-1-7h1v-2z" class="H"></path><path d="M220 452l6-2h0v1l1 1-7 2v-2z" class="B"></path><path d="M219 451v1h1v2c-5 2-10 6-15 10v-2c0-1 1-1 1-2 4-3 8-6 13-9z" class="G"></path><path d="M175 496c4-7 10-13 16-20 4-5 9-10 14-14v2c-11 9-21 21-30 33v-1z" class="W"></path><path d="M168 550c-1-3-1-7-1-10 1-18 6-39 19-52 4-5 9-8 13-12s8-8 11-13l3 3v1h-1l-1-1c-2 0-3 2-3 3-3 2-5 5-7 7-5 5-11 9-15 14-13 15-19 38-17 58v2h-1z" class="O"></path><path d="M164 525c2-8 4-16 8-24 1-2 1-4 3-5v1c-1 4-3 7-5 11-5 12-5 24-4 36 0 5 0 11 2 15 1 4 3 7 4 10s2 6 3 8c4 8 11 16 17 21v1c-9-6-16-16-21-26-2-3-4-7-5-11l1-1c-2-2-2-5-3-8-2-9-3-20-1-29v2 1l1-2z" class="H"></path><path d="M163 524v2 1l1-2c-1 7 0 13 0 20 1 6 1 12 4 18h0l-1-2c-2-2-2-5-3-8-2-9-3-20-1-29z" class="R"></path><path d="M249 450c4 1 8 0 12 1h3c-1 1-3 1-4 1h0c-4 1-9 2-12 3-6 1-12 4-18 7-9 5-16 10-23 18-2 2-5 5-7 8-4 6-7 14-9 21-1 2-1 4-2 5h0c-1-2 0-3-1-5l-1 2c-1 0-2 1-3 2-2 3-4 7-6 10-2 4-4 9-5 13h-1c1-8 5-14 9-20 2-3 4-6 5-9 3-5 4-10 6-14s4-7 7-10c8-10 19-20 31-26 6-3 12-4 19-7z" class="O"></path><path d="M230 462c0-1 0-1-1-1s-1 0-2 1l-8 5-3 3c-2 1-3 3-5 4v1c-1 0-9 8-9 9h-1l-3 3c-1 2-2 4-3 5v1c0 1-1 3-2 4-1 2-2 5-3 7l-1-1 2-6 1-1c0-1 1-2 1-3 1-2 3-4 4-6 1-1 2-3 3-3 0-1 1-2 2-2 0-2 2-3 3-4l4-4 4-4c1-1 2-2 3-2l1-1c1-1 2-2 4-3l7-4c1-1 2-2 4-2 1 0 2-1 3-1l4-2 9-2h0c4-1 6-1 10-1h2 0c-4 1-9 2-12 3-6 1-12 4-18 7z" class="F"></path><path d="M166 569v-1c0 1 0 2 1 4l3 6c1 1 1 1 1 2v-1-2c-2-2-3-5-4-8h0c0 1 1 2 1 3 1 1 2 3 2 4 2 3 2 5 5 8h0c4 6 11 12 17 17 1 1 3 2 4 3l1-1c2 1 5 4 7 3h0l-1-1h1l-2-2h1c3 2 12 10 15 10h1 2 1l22 6 1-1c3 0 6 1 10 2l4-1v-1h0c4 1 8 2 13 2h3c1 0 5-1 6 0l1 1 5-1c1 0 1 0 2 1-2 0-4 0-6 1l1 1c2-1 7-2 9-1v1h5c2-1 4-1 6-2v1l1-1c1-1 2-1 4-2h0 8 1c7 4 12 7 19 10 5 2 10 3 15 4-1 0-2 1-3 1h5l-1 1h-1l1 1h-8 3l1 1c-2 1-4 0-6 1h4 0l-1 1c2 0 4 0 6-1v1h-3c-2 1-4 0-5 1l-1-1h-7v1c2 0 4-1 6 0h1 4 0 3 0c-1 1-2 1-3 1-7-1-15-1-23-1l-51 2c-8 0-16 1-24-1h-1v1h0l-16 1h-5-9c-3 0-8 2-11 1-1 0-1 0-1-1h2 4 2c1-1 3 0 5-1 1 0 3 1 5 0-1 0-2 0-3-1h-1c-2-1-3-3-4-5v-2h0l-1-1v-2-1c1-1 1-2 2-3s2-3 3-3v-1h0c-1 1-4 1-6 0h0v5 1c1 3-1 8-4 10-2 2-7 4-10 4h2c2-1 5-2 7-4h0-1l1-1h-2c-2 2-5 2-8 3-7 1-14 0-21 0l-18 1h-9l-1-1h-3 3v-1h-3 3l-19-1h1 3l-2-1h-1c-4 0-8-1-13-1h0v-1h3c1-1 2-1 3-1h1c3 0 5-1 7-2 8-3 13-8 16-15 0-2 0-3 1-5 0-1 0-1-1-1l1-1c1-1 2-2 2-3 2-1 3-2 5-3l2-1v-1h-1l2-1-1-1c4-3 10-5 13-10 0-1-1-3-1-5-2-6-4-11-6-16l1-1z"></path><path d="M184 623l2 1-1 2v4h0c-1 0-1 0-2-1 0-2 1-4 1-6z" class="C"></path><path d="M212 626c0-1 1-1 1-2 1 4 1 9-1 12l-3 3h-1l1-1h-2c1 0 3-1 3-3 3-3 3-6 2-9z" class="Z"></path><path d="M332 638h11 4 0l-1 1h-25-15c6-1 13-1 20-1 1 0 5 1 6 0z" class="D"></path><path d="M298 636c5 2 11 1 17 1l17 1c-1 1-5 0-6 0-7 0-14 0-20 1-4 0-6-1-10-2l2-1z" class="Z"></path><path d="M184 623c2-2 4-3 7-3s5 1 8 3c2 2 2 4 2 7-1 4-3 7-7 9-3 1-7 1-10 0h5c2 0 5-1 7-2 2-2 4-4 4-7 0-2 0-5-2-6-1-2-3-3-6-3v1 1c-2-1-4 0-6 1h0l-2-1z" class="D"></path><path d="M165 570l1-1c1 5 4 9 6 13 1 3 2 5 3 7 3 6 10 13 16 17v1h0c-2-1-3-1-4-2-6-6-10-12-15-19l1 5h0-1 0c0-1-1-3-1-5-2-6-4-11-6-16z" class="S"></path><path d="M192 622v-1c3 0 5 1 6 3 2 1 2 4 2 6 0 3-2 5-4 7-2 1-5 2-7 2h-3c1-1 2-1 3-1 0 0 1-1 2-1 3-2 5-4 6-7 0-2 0-4-1-5s-2-3-4-3z"></path><path d="M181 611c1-1 4-3 6-4 1 0 2 0 2 1h5c6 1 12 4 16 9 2 2 3 5 3 7 0 1-1 1-1 2v-1c-1-2-1-3-2-5-2-5-7-8-12-10-6-2-12 0-17 3v-2z" class="D"></path><path d="M219 625v1s1 0 1-1c1 0 1-1 3-1 1-1 2-1 4 0l-3 1c-2 1-4 3-4 4-1 2-1 5-1 7 1 1 3 4 5 4 1 1 3 1 4 0 2 0 3-3 4-5v-4h0l1 1c0 3-1 7-4 9-1 1-4 1-6 0-2 0-3 0-5-2-1-2-1-3-2-5h0c-1-2 0-4 1-6 0-1 1-2 2-3z" class="Q"></path><path d="M181 611v2c-3 2-6 6-6 10-1 3 0 7 2 9 2 3 5 5 8 5s5-1 7-2c2-2 3-4 4-6 0-1 0-3-1-4s-2-2-3-2v-1c2 0 3 2 4 3s1 3 1 5c-1 3-3 5-6 7-1 0-2 1-2 1-1 0-2 0-3 1h3-5c-1 0-4-1-5-1-2-1-5-3-6-5h0v-5-5c1-5 3-9 8-12z" class="C"></path><path d="M173 628c1 1 1 2 2 3 1 2 3 4 6 6h0c2 1 5 1 8 1-1 0-2 0-3 1h3-5c-1 0-4-1-5-1-2-1-5-3-6-5h0v-5z" class="b"></path><path d="M219 625l3-3c-2 1-6 1-8 1v-1c3-1 5-1 7-1h5c2 0 4 0 6 1 1 0 2 0 3 1h0c3 2 8 2 9 5v3c2-1 3-6 6-4 1 0 2 1 2 1 0 1 0 2-1 2-1 1-2 1-3 0l-1-1c-1 0-2 1-2 2-1 2-1 3 0 5 0 2 1 3 3 4l1 1h-1-1c-5-2-4-7-5-12-1-1-3-3-4-3-6-1-10 0-15 3 4-4 7-5 12-5-2-1-5-1-8 0-2-1-3-1-4 0-2 0-2 1-3 1 0 1-1 1-1 1v-1z" class="I"></path><path d="M172 591h0 1 0c1 3 3 6 4 9h1c1 2-1 8-2 10l-1 1v-1l-1-1c-1-3-3-5-6-7-3-1-6 0-9 1h-1l2-1-1-1c4-3 10-5 13-10z"></path><path d="M172 591h0 1 0c1 3 3 6 4 9h1c1 2-1 8-2 10l-1 1v-1-1c2-6 0-11-2-16l-13 9-1-1c4-3 10-5 13-10z" class="D"></path><path d="M304 621v1 2c2 4 8 7 12 9 9 3 20 3 29 3h3l1 1c-2 1-4 0-6 1h-11l-17-1c-6 0-12 1-17-1l-2 1c-3-1-5-4-8-6v-1l2 2h1c0-3 1-6 3-8l-1-1h0 5c2-1 4-1 6-2z"></path><path d="M293 623h5v1h0c0 2 0 2-1 3-1 0-2-1-3-2-1 1-1 2-1 4-1 1 0 3 1 4s2 2 4 3l-2 1c-3-1-5-4-8-6v-1l2 2h1c0-3 1-6 3-8l-1-1h0z" class="I"></path><path d="M293 623h5v1h-1v2l-3-2-1-1h0z" class="b"></path><path d="M305 621c1-1 2-1 4-2h0 8 1c7 4 12 7 19 10 5 2 10 3 15 4-1 0-2 1-3 1h5l-1 1h-1l1 1h-8c-9 0-20 0-29-3-4-2-10-5-12-9v-2l1-1z"></path><path d="M305 621c1-1 2-1 4-2h0 8 1c7 4 12 7 19 10 5 2 10 3 15 4-1 0-2 1-3 1-4-1-9-2-13-3-2-1-5-3-7-4-3-2-9-7-13-7-3 0-6 0-9 1v1c-1 0-1-1-2-1z" class="I"></path><path d="M305 621c1 0 1 1 2 1v1 2c0 2 4 4 6 5 12 6 27 5 40 5h-1l1 1h-8c-9 0-20 0-29-3-4-2-10-5-12-9v-2l1-1z" class="D"></path><defs><linearGradient id="g" x1="253.751" y1="633.651" x2="243.617" y2="606.272" xlink:href="#B"><stop offset="0" stop-color="#202020"></stop><stop offset="1" stop-color="#3b393b"></stop></linearGradient></defs><path fill="url(#g)" d="M203 605h1l-2-2h1c3 2 12 10 15 10h1 2 1l22 6 1-1c3 0 6 1 10 2l4-1v-1h0c4 1 8 2 13 2h3c1 0 5-1 6 0l1 1 5-1c1 0 1 0 2 1-2 0-4 0-6 1l1 1c2-1 7-2 9-1v1h0l1 1c-2 2-3 5-3 8h-1l-2-2v1c-3-1-6-4-9-5h-1c-3-1-8 0-11 0h-12c-6-1-13-3-19-5-5-1-10-2-15-4s-10-5-15-8l-10-5 1-1c2 1 5 4 7 3h0l-1-1z"></path><path d="M284 623c2-1 7-2 9-1v1h0c-3 0-7 1-10 1-1 0-4 0-5-1h6z" class="N"></path><path d="M283 624c3 0 7-1 10-1l1 1c-2 2-3 5-3 8h-1l-2-2c-2-1-4-2-6-4-1 0-1-1-3-1v-1h4z"></path><path d="M245 618c3 0 6 1 10 2l4-1v-1h0c4 1 8 2 13 2h3c1 0 5-1 6 0l1 1 5-1c1 0 1 0 2 1-2 0-4 0-6 1-1-1-9 0-11 0-8 0-16 0-24-1-1-1-3-1-4-2l1-1z" class="E"></path><path d="M259 618h0c4 1 8 2 13 2h3c1 0 5-1 6 0l1 1h-8c-6 0-13 0-19-1l4-1v-1z" class="Y"></path><path d="M159 603c3-1 6-2 9-1 3 2 5 4 6 7l1 1v1l-1 5c-3 9-4 18-13 22-3 2-7 3-11 3h-3 3v-1h-3 3l-19-1h1 3l-2-1h-1c-4 0-8-1-13-1h0v-1h3c1-1 2-1 3-1h1c3 0 5-1 7-2 8-3 13-8 16-15 0-2 0-3 1-5 0-1 0-1-1-1l1-1c1-1 2-2 2-3 2-1 3-2 5-3l2-1v-1z"></path><path d="M167 620v-1c1 0 2 0 3 1l-1 3h-3v-1c0-1 0-1 1-2zm-16-4c2 0 3 1 4 2 1 2 2 4 2 6 0 3-3 6-5 8-1 1-3 2-4 3v-1c3-2 6-5 7-8 1-1 1-4 0-5 0-2-3-3-4-4v-1z" class="C"></path><path d="M150 611l1 1c2-3 3-3 6-4 4 0 7 0 10 2l-1 1c-3-2-7-2-11-1-2 1-3 3-5 6h1v1l-1 1h-1c0-2 0-3 1-5 0-1 0-1-1-1l1-1z" class="N"></path><path d="M166 611l1-1 1 1c1 1 3 3 4 5h0s1 0 1-1l1 1c-3 9-4 18-13 22-3 2-7 3-11 3h-3 3v-1h-3 3c6-1 11-2 15-7 3-3 6-9 5-14 0-4-1-5-4-8z" class="D"></path><path d="M159 603c3-1 6-2 9-1 3 2 5 4 6 7l1 1v1l-1 5-1-1c0 1-1 1-1 1h0c-1-2-3-4-4-5l-1-1c-3-2-6-2-10-2-3 1-4 1-6 4l-1-1c1-1 2-2 2-3 2-1 3-2 5-3l2-1v-1z"></path><path d="M159 603c3-1 6-2 9-1 3 2 5 4 6 7l1 1v1l-1 5-1-1c0-2 1-4 0-6-1-3-3-4-5-5-3-2-6-1-9 0v-1z" class="Y"></path><path d="M151 617c1 1 4 2 4 4 1 1 1 4 0 5-1 3-4 6-7 8v1c-4 2-8 3-12 3l-1 1-2-1h-1c-4 0-8-1-13-1h0v-1h3c1-1 2-1 3-1h1c3 0 5-1 7-2 8-3 13-8 16-15h1l1-1z"></path><path d="M132 638l4-1v1l-1 1-2-1h-1z" class="Q"></path><path d="M136 637c4 0 8-2 12-3v1c-4 2-8 3-12 3v-1z" class="S"></path><path d="M126 635c3 0 5-1 7-2 8-3 13-8 16-15h1l2 1c0 3-3 5-5 7-6 6-14 10-23 10h-2c1-1 2-1 3-1h1z" class="D"></path><path d="M164 156c1 3-1 10-1 12 0 1 1 2 1 3v2c2 3 2 4 5 6h4l1 1c0 2 1 2 2 4v-1h1v2h1v2 1c2 2 2 4 3 5v1l2 2h-1v1c1 2 3 4 4 6 3 2 5 5 8 7 5 3 10 4 15 5h1 2c0 1-2 0-3 1v1h0c-3 0-7-1-10-1h-2v1h0l4 1h2c4 1 10 2 13 1h4v1h-1c-1 4-2 13-1 17v3c-2 6 0 12 1 17l3 18c2 3 3 7 3 10l2 6c2 2 3 5 5 8 1 1 3 3 4 5 0 1 1 2 2 3l15 19v2l5 5c0 2 0 2 1 3 4 5 9 10 12 16l2 6h-1c1 1 1 2 1 3h-1 0c-2-2-4-5-6-7h0c-3-4-8-7-12-11-1-1-2-1-3-1l-6-5c0-2-4-5-5-6l-1-1h-2c-4-5-9-11-11-16-2-4-4-7-5-11 0-1-1-2-1-3l-3-6h2 1l-2-4c-1-1-1-2-1-3l-10-19h-1c-1-2-3-4-4-5-5-5-9-8-15-11-2 0-3-1-4-2-2-1-4-1-6-1-14-2-28 0-41 5-3 1-6 3-8 5l-1-1 11-10-1-2-10 10c-1 2-2 3-4 3l4-5c7-8 7-18 6-28v-4h1v2c1 0 1 0 1-1 1-3 3-5 4-7 2-4 4-7 5-11 0-2 2-5 2-8 1 1 1 1 1 2s1 1 1 1c1-4 1-9 3-13l1 1h0c0-3 2-6 3-8l4-12c0-1 1-2 1-3v-2c1-2 3-5 4-7z"></path><path d="M201 218h2c4 1 10 2 13 1h4v1h-1 0c-6 2-13 2-20 1-1 0-3-1-5-1h-1 0l2-2c2 0 5 1 6 0z" class="M"></path><path d="M161 176v-1-2h1v8 7c2 13 11 23 20 31 5 5 10 9 15 13l6 3c3 2 7 5 11 6 1-1 1 0 1-1 0-2 0-4-2-6-2-3-6-6-10-8l1-1c3 1 6 3 8 6 2 2 5 4 4 8 0 2 0 2-1 3h-2c-4-1-8-4-11-6l-9-6c-9-6-18-14-24-23s-10-21-8-31z" class="B"></path><path d="M164 156c1 3-1 10-1 12 0 1 1 2 1 3v2 6c-1 1-1 3-1 4v-6l-1 4v-8h-1v2 1l-1-2c-1 2-1 4-1 7-1 8 0 15 3 23 5 11 17 22 28 28 9 5 21 9 26 19v2c-5-10-18-15-28-21-3-1-5-3-8-5-14-11-25-27-23-46 1-2 1-4 1-5 1-3 2-5 2-8h-1c0-1 1-2 1-3v-2c1-2 3-5 4-7z" class="M"></path><path d="M163 168c0 1 1 2 1 3v2 6c-1 1-1 3-1 4v-6-5-1c-1-1-1-2 0-3z" class="F"></path><path d="M162 181l1-4v6c0 12 6 23 15 30l1 2c0-1 1-1 2 0 1 0 3 1 4 1 2 1 6 2 8 3v1h0 1c3 2 8 3 10 5l-1 1c4 2 8 5 10 8 2 2 2 4 2 6 0 1 0 0-1 1-4-1-8-4-11-6l-6-3c-5-4-10-8-15-13-9-8-18-18-20-31v-7z"></path><path d="M179 215c0-1 1-1 2 0 1 0 3 1 4 1 2 1 6 2 8 3v1h0 1c3 2 8 3 10 5l-1 1-10-5c-5-2-10-3-14-6z" class="E"></path><path d="M159 168h1c0 3-1 5-2 8 0 1 0 3-1 5-2 19 9 35 23 46 3 2 5 4 8 5 10 6 23 11 28 21v-2c1 2 1 6 2 7l1-1 3 18c2 3 3 7 3 10l2 6v1l-1-2h0l-17-32c-3-5-6-11-9-15s-7-6-10-9l-2 2c-4-4-8-6-12-9-11-9-19-19-22-33v-2h1v-2c-1-1-1-3-1-3h-1l-1 1c0-3 2-6 3-8l4-12z"></path><path d="M152 188c0-3 2-6 3-8v10c-1-1-1-3-1-3h-1l-1 1z" class="E"></path><path d="M220 275l1-1 1 1h0c2 3 3 7 3 10-2-3-3-7-5-10z" class="M"></path><path d="M216 251c1 2 1 6 2 7l1-1 3 18h0l-1-1-1 1h0l-3-15-1-7v-2z" class="a"></path><path d="M154 194v-2h1c2 14 10 25 21 33 4 3 9 7 14 9l-2 2c-4-4-8-6-12-9-11-9-19-19-22-33z" class="L"></path><path d="M190 234c3 3 7 5 10 9s6 10 9 15l17 32h0l1 2v-1c2 2 3 5 5 8 1 1 3 3 4 5 0 1 1 2 2 3l15 19v2c-2-1-3-3-5-4-1 0-3-2-4-3l-12-15-9-13-6-6-10-19h-1c-1-2-3-4-4-5-5-5-9-8-15-11-2 0-3-1-4-2h1 2c2 1 5 2 7 3s4 3 6 4c2 0 3 0 4-1h0c-3-9-9-15-15-20l2-2z"></path><path d="M232 299c1 1 3 3 4 5 0 0-1 1 0 2v1c0 1 0 1 1 1v2c-2-2-4-5-5-8v-3z" class="H"></path><path d="M226 290h0l1 2v-1c2 2 3 5 5 8v3c-3-3-5-7-7-11 1 0 1 0 1-1z" class="E"></path><defs><linearGradient id="h" x1="240.971" y1="317.986" x2="247.434" y2="315.045" xlink:href="#B"><stop offset="0" stop-color="#a2a3a0"></stop><stop offset="1" stop-color="#c5c1c5"></stop></linearGradient></defs><path fill="url(#h)" d="M236 304c0 1 1 2 2 3l15 19v2c-2-1-3-3-5-4-4-4-8-9-11-14v-2c-1 0-1 0-1-1v-1c-1-1 0-2 0-2z"></path><path d="M190 234c3 3 7 5 10 9s6 10 9 15l17 32c0 1 0 1-1 1l-16-30c-4-8-7-15-14-21l5 7c3 4 5 8 7 13 0 1 1 3 1 5s0 2-1 3h-1c-1-2-3-4-4-5-5-5-9-8-15-11-2 0-3-1-4-2h1 2c2 1 5 2 7 3s4 3 6 4c2 0 3 0 4-1h0c-3-9-9-15-15-20l2-2z" class="H"></path><path d="M187 252c4-1 12 9 16 5h2c1 3 3 7 2 9 0 1 0 1-1 2-1-2-3-4-4-5-5-5-9-8-15-11zm-23-79c2 3 2 4 5 6h4l1 1c0 2 1 2 2 4v-1h1v2h1v2 1c2 2 2 4 3 5v1l2 2h-1v1c1 2 3 4 4 6 3 2 5 5 8 7 5 3 10 4 15 5h1 2c0 1-2 0-3 1v1h0c-3 0-7-1-10-1h-2v1h0l4 1c-1 1-4 0-6 0l-2 2v-1c-2-1-6-2-8-3-1 0-3-1-4-1-1-1-2-1-2 0l-1-2c-9-7-15-18-15-30 0-1 0-3 1-4v-6z"></path><path d="M188 212c4 2 7 3 11 4h-2v1h0c-4-1-7-2-10-3 0-1 0-1 1-1h0 0v-1z" class="B"></path><path d="M193 210h1c5 3 10 4 15 5h1 2c0 1-2 0-3 1-5 0-12-2-17-5l1-1z" class="E"></path><path d="M169 181v-1h1c1 6 2 12 5 17l-1 1c-1-1-1-1-1-2-1-2-2-3-2-5-1-3-2-6-2-10z" class="T"></path><path d="M178 205l1-1c3 4 6 5 9 8v1h0 0c-1 0-1 0-1 1-4-2-7-4-10-8l1-1z" class="F"></path><path d="M169 179h4l1 1c0 2 1 2 2 4v-1h1v2h1v2 1c2 2 2 4 3 5v1l2 2h-1v1c1 2 3 4 4 6 3 2 5 5 8 7h-1l-1 1-6-3c-5-4-10-10-12-16-1-3-3-8-2-12h-1-1-1v1c-1-1-1-1 0-2z" class="I"></path><path d="M172 180h1c1 4 0 7 3 10v1 3h0c-1-1-1-2-2-3v1c-1-3-3-8-2-12z" class="C"></path><defs><linearGradient id="i" x1="174.488" y1="197.547" x2="188.498" y2="201.53" xlink:href="#B"><stop offset="0" stop-color="#626463"></stop><stop offset="1" stop-color="#7e7b7d"></stop></linearGradient></defs><path fill="url(#i)" d="M176 190c2 2 2 4 3 6 2 4 5 7 8 10h0v1l-1 1c-5-4-10-10-12-16v-1c1 1 1 2 2 3h0v-3-1z"></path><path d="M176 184v-1h1v2h1v2 1c2 2 2 4 3 5v1l2 2h-1v1c1 2 3 4 4 6 3 2 5 5 8 7h-1l-1 1-6-3 1-1v-1h0c0-2-2-3-2-4-1-1-2-2-2-3l-3-3c-1-2-1-5-3-7l-2-2c1-1 0-2 1-3z" class="K"></path><path d="M187 206c2 2 3 3 6 4l-1 1-6-3 1-1v-1z" class="M"></path><path d="M164 173c2 3 2 4 5 6-1 1-1 1 0 2 0 4 1 7 2 10 0 2 1 3 2 5 0 1 0 1 1 2l1-1c1 2 2 4 3 5 0 1 1 2 1 2l-1 1-1 1c3 4 6 6 10 8 3 1 6 2 10 3l4 1c-1 1-4 0-6 0l-2 2v-1c-2-1-6-2-8-3-1 0-3-1-4-1-1-1-2-1-2 0l-1-2c-9-7-15-18-15-30 0-1 0-3 1-4v-6z"></path><path d="M177 206c-5-4-7-10-9-16-1-2-3-10-2-12l1 1c1 2 1 3 1 5 1 4 2 8 4 13l1-1c0 1 0 1 1 2l1-1c1 2 2 4 3 5 0 1 1 2 1 2l-1 1-1 1z" class="B"></path><path d="M173 196c0 1 0 1 1 2l1-1c1 2 2 4 3 5 0 1 1 2 1 2l-1 1c-2-2-4-5-6-8l1-1z" class="C"></path><path d="M164 179c0 10 2 16 7 24 1 2 3 4 5 6 5 5 12 8 19 9l-2 2v-1c-2-1-6-2-8-3-1 0-3-1-4-1-1-1-2-1-2 0l-1-2c-9-7-15-18-15-30 0-1 0-3 1-4z" class="B"></path><path d="M217 287l6 6 9 13 12 15c1 1 3 3 4 3 2 1 3 3 5 4l5 5c0 2 0 2 1 3 4 5 9 10 12 16l2 6h-1c1 1 1 2 1 3h-1 0c-2-2-4-5-6-7h0c-3-4-8-7-12-11-1-1-2-1-3-1l-6-5c0-2-4-5-5-6l-1-1h-2c-4-5-9-11-11-16-2-4-4-7-5-11 0-1-1-2-1-3l-3-6h2 1l-2-4c-1-1-1-2-1-3z"></path><defs><linearGradient id="j" x1="224.566" y1="306.529" x2="217.568" y2="295.291" xlink:href="#B"><stop offset="0" stop-color="#a6a7a7"></stop><stop offset="1" stop-color="#cfccce"></stop></linearGradient></defs><path fill="url(#j)" d="M220 294l5 12c0 2 0 3 1 5h0v2 1c-2-4-4-7-5-11 0-1-1-2-1-3l-3-6h2 1z"></path><path d="M241 329c8 8 16 14 24 22 3 2 5 4 7 7 1 1 1 2 1 3h-1 0c-2-2-4-5-6-7h0c-3-4-8-7-12-11-1-1-2-1-3-1l-6-5c0-2-4-5-5-6l1-2z" class="L"></path><path d="M217 287l6 6 9 13 12 15c1 1 3 3 4 3 2 1 3 3 5 4l5 5c0 2 0 2 1 3-6-6-13-11-18-17-8-9-15-20-23-29h0c-1-1-1-2-1-3z" class="M"></path><path d="M225 306c4 8 9 15 15 22l1 1-1 2-1-1h-2c-4-5-9-11-11-16v-1-2h0c-1-2-1-3-1-5z" class="B"></path><path d="M239 330v-1l1-1 1 1-1 2-1-1z" class="O"></path><path d="M153 187h1s0 2 1 3v2h-1v2c3 14 11 24 22 33 4 3 8 5 12 9 6 5 12 11 15 20h0c-1 1-2 1-4 1-2-1-4-3-6-4s-5-2-7-3h-2-1c-2-1-4-1-6-1-14-2-28 0-41 5-3 1-6 3-8 5l-1-1 11-10-1-2-10 10c-1 2-2 3-4 3l4-5c7-8 7-18 6-28v-4h1v2c1 0 1 0 1-1 1-3 3-5 4-7 2-4 4-7 5-11 0-2 2-5 2-8 1 1 1 1 1 2s1 1 1 1c1-4 1-9 3-13l1 1h0l1-1z"></path><path d="M151 187l1 1c-2 5-2 9-3 14-1-1-1-1-1-2 1-4 1-9 3-13z" class="L"></path><path d="M146 197c1 1 1 1 1 2s1 1 1 1c0 1 0 1 1 2 0 4 0 8 1 13v-1l-1 1c1 1 1 1 0 2 0-2-1-4-1-6 0-1 0-3-1-4v2c0-1-1-1-1-1v-3-1c-1 1-1 1-1 2l-1-1c0-2 2-5 2-8z" class="F"></path><path d="M146 205v-1c1 0 1 0 1 1v1 1 2c0-1-1-1-1-1v-3z" class="O"></path><path d="M149 217c1-1 1-1 0-2l1-1v1l3 9c1 4 6 14 4 18-1 1-2 3-3 3-3 0-5-1-6-2-2-1-3-3-5-4-1 3-2 6-5 9l-1-2c4-4 5-10 6-15 0 2 0 3 1 4v2c1 2 2 3 4 5 2 1 3 2 5 1 1 0 2-1 3-1 2-3-3-13-4-16l-3-9z" class="W"></path><path d="M153 187h1s0 2 1 3v2h-1v2l-1-1v1 4c1 10 3 21 7 30 1 1 1 3 2 5 3 4 9 10 14 12 2 0 4 1 6 0 1 0 1-1 1-2 1-2 1-4 0-5-2-3-7-8-10-9-2 0-3 1-4 2h-1v-1c1-1 3-2 5-2 3 0 6 3 8 6 2 2 4 5 4 8 0 1-1 3-2 4s-4 1-6 0c-6 0-11-5-14-9-8-12-13-36-10-50z" class="H"></path><path d="M147 207c1 1 1 3 1 4 0 2 1 4 1 6l3 9c1 3 6 13 4 16-1 0-2 1-3 1-2 1-3 0-5-1-2-2-3-3-4-5v-2c-1-1-1-2-1-4l1-8v-7c0-1 1-3 1-4l1-4s1 0 1 1v-2z"></path><path d="M144 216h2v2l-1 7v-3h-1v1-7z" class="W"></path><path d="M145 212l1-4s1 0 1 1v4l-1 5v-2h-2c0-1 1-3 1-4z" class="J"></path><path d="M145 212l1-4s1 0 1 1v4l-1-2h0l-1 1z" class="B"></path><path d="M144 223v-1h1v3l-1 10c-1-1-1-2-1-4l1-8z" class="c"></path><path d="M145 206c0-1 0-1 1-2v1 3l-1 4c0 1-1 3-1 4v7l-1 8c-1 5-2 11-6 15l-10 10c-1 2-2 3-4 3l4-5c7-8 7-18 6-28v-4h1v2c1 0 1 0 1-1 1-3 3-5 4-7 2-4 4-7 5-11l1 1z"></path><path d="M144 205l1 1c-1 5-4 9-6 13l-3 6c-2 5 0 12-2 18-1 3-2 5-3 8-1 1-4 4-4 5-1 2-2 3-4 3l4-5c7-8 7-18 6-28v-4h1v2c1 0 1 0 1-1 1-3 3-5 4-7 2-4 4-7 5-11z" class="X"></path><path d="M267 97c6-3 14-5 21-5h7 0c2 1 5 0 7 1 10 0 20 0 29 2 3 0 6 1 8 2 2 0 4 1 5 1 1-1 2 0 3 0h-1c1 1 2 2 3 2s2 1 3 2c-1 0-2 0-3-1l-2 1c1 0 2 1 3 1 2 0 3 1 4 2 1 0 2 1 3 1l6 3c3 2 6 3 9 4l8 5c1 0 2 1 3 1 7 2 10-1 15-4l1 2 5-2h3v1h-1 0c3 0 8 1 9 3l1 2c-4-3-8-3-13-2-2 1-4 2-5 4s-2 4-1 7 4 6 6 8c1 2 4 3 6 4h0v1c1 0 2 0 2 1 1 1 2 1 3 2 3 1 7 2 10 4 4 2 6 4 9 8 0 0 1 1 1 2h0c0 3 0 4-2 6-1 1-2 1-4 1h-4l4 4h-1 0-1-2c-4-3-7-3-13-2l-4 2h-1c0 1 0 1-1 2 0 3-1 5-2 7l-2 2c-1 1-2 3-4 3h-1-1 0c0 1 0 2 1 3l-1-1c0 3 2 4 2 7-3-6-5-11-7-16-5-9-13-17-20-24-16-13-36-23-57-25-6-1-12-1-17 0l-12 2c-3-1-8-1-11 0h-1l-1 1c-1 1-1 1-2 1-4-9-10-17-18-22l-4-3v-2c6-1 12-5 17-8l3-1z"></path><path d="M281 111h2s1 0 1 1v1c-1 1-1 1-3 1h-1c-1-1-2-1-3-2 2-1 3-1 4-1z" class="I"></path><path d="M312 121l1-1c4-1 10-2 15-1h-4 0c1 1 4 1 6 1l-1 1c1 1 1 1 2 1-4-1-8-1-12-1h-6-1z" class="C"></path><path d="M406 158c1 5 1 8 0 13 0 1 0 1-1 2 0 3-1 5-2 7l-2 2c-1 1-2 3-4 3h-1l1-1c1-1 3-2 4-4 2-4 6-12 4-17v-1-2c0-1 0-1 1-2zm-94-37h-1v-1c3-2 7-2 10-3 7 0 14 1 21 3 11 2 21 6 29 13l-1 1c-12-10-27-14-42-15-5-1-11 0-15 1l-1 1z" class="E"></path><path d="M288 128c-3 1-7 1-10 0s-7-3-8-7c-2-2-2-6-1-9 1-2 2-5 4-6 3-1 6 0 9 1l2 2-1 1c0-1-1-2-2-3-2 0-5 0-7 2-2 1-2 3-2 6 0 4 1 6 4 8 2 2 5 2 9 2 1-1 2-1 3-2v1h0v1c1 0 1 0 2 1-1 0-2 0-3 1l1 1z" class="I"></path><path d="M288 125c-1 0-2 0-3 1-3 1-7 0-10-2-2-1-4-4-4-6v-1s0-1 1-2c0 4 1 6 4 8 2 2 5 2 9 2 1-1 2-1 3-2v1h0v1z" class="D"></path><defs><linearGradient id="k" x1="256.943" y1="122.065" x2="290.017" y2="102.683" xlink:href="#B"><stop offset="0" stop-color="#161515"></stop><stop offset="1" stop-color="#343434"></stop></linearGradient></defs><path fill="url(#k)" d="M266 99l1 1c7-2 13-3 19 0 4 2 7 6 8 9 1 4 1 8-1 11l-3 4 1 1-1 1c-1-1-1-1-2-1v-1h0v-1c2-3 4-5 4-9 0-3-1-6-2-8-3-3-7-5-10-6-7 0-14 3-20 6-3 1-6 2-9 5l-4-3v-2c6-1 12-5 17-8 1 1 1 1 2 1z"></path><path d="M296 129c1-1 2-1 4-2 1-1 4-1 6-1h1v-1-1c0-1 1-1 2-2v1 1 1h0c2-1 3 0 4 0l5 1c3 1 6 1 8 2 7 2 15 5 22 8 2 1 5 3 7 4 4 2 7 4 11 7 6 6 11 12 17 18 2 3 5 5 7 9 3 3 5 7 7 10l-1 1h-1 0c0 1 0 2 1 3l-1-1c0 3 2 4 2 7-3-6-5-11-7-16-5-9-13-17-20-24-16-13-36-23-57-25-6-1-12-1-17 0z" class="Q"></path><path d="M267 97c6-3 14-5 21-5h7 0c2 1 5 0 7 1h-7c5 1 9 3 11 7 2 3 2 6 2 9s-3 6-4 9c-1 1-6 4-6 5h0s0 1 1 1l-11 4-1-1c1-1 2-1 3-1l1-1-1-1 3-4c2-3 2-7 1-11-1-3-4-7-8-9-6-3-12-2-19 0l-1-1c-1 0-1 0-2-1l3-1z"></path><path d="M264 98l3-1s1 1 2 1c-1 1-2 1-3 1s-1 0-2-1zm34 12c0 1 1 1 1 2v2h-1 0c-1-1-1-2 0-4z" class="I"></path><path d="M299 99h1c1 1 1 1 1 3 0 1 0 1-1 1h-1c-1 0-2 0-2-1 0-2 1-2 2-3z" class="S"></path><path d="M306 106h0c1 1 1 2 1 3h0 0 1c0 3-3 6-4 9-1 1-6 4-6 5h0s0 1 1 1l-11 4-1-1c1-1 2-1 3-1l1-1c4-2 8-4 11-7 3-4 4-7 4-12z" class="C"></path><defs><linearGradient id="l" x1="288.759" y1="91.074" x2="286.958" y2="106.321" xlink:href="#B"><stop offset="0" stop-color="#1d1c1d"></stop><stop offset="1" stop-color="#4f4f50"></stop></linearGradient></defs><path fill="url(#l)" d="M267 97c6-3 14-5 21-5h7 0c2 1 5 0 7 1h-7c5 1 9 3 11 7 2 3 2 6 2 9h-1 0 0c0-1 0-2-1-3h0c0-2 0-4-1-5h0c-2-2-3-3-5-4-5-2-11-1-15-1h-9c-2 0-5 1-7 2-1 0-2-1-2-1z"></path><path d="M344 117l2 1c17 4 33 12 47 22 5 3 9 9 14 13 2 3 6 5 9 8 3 2 6 4 10 5h5c1-1 1-2 2-3v-2l1-1c0 3 0 4-2 6-1 1-2 1-4 1h-4l4 4h-1 0-1-2c-4-3-7-3-13-2l-4 2h-1c1-5 1-8 0-13-1 1-1 1-1 2-1 1-3 2-4 3-2 1-5 1-7 0-3-2-6-4-8-7v-1h0c-1 1-1 2-2 3-2 1-4-1-5-2-16-12-27-30-48-34-1 0-1 0-2-1l1-1c-2 0-5 0-6-1h0 4c15 1 30 5 42 15l1-1c5 4 9 9 13 15 1 2 2 5 3 7 2 2 3 4 6 5 2 1 4 2 7 2 1-1 3-2 3-3 0-3 0-5-2-7-2-3-4-7-7-9-2-2-6-4-8-6-13-8-27-15-42-19v-1z" class="H"></path><path d="M406 158c-1-2-2-4-3-7 4 4 7 8 12 11 4 3 8 5 12 9h-1-2c-4-3-7-3-13-2l-4 2h-1c1-5 1-8 0-13zm-78-39c15 1 30 5 42 15 6 5 13 13 15 20-1 1-1 2-2 3-5 0-16-14-20-17-4-4-7-7-11-10-7-4-14-8-22-10-2 0-5 0-6-1h0 4zm-26-26c10 0 20 0 29 2 3 0 6 1 8 2 2 0 4 1 5 1 1-1 2 0 3 0h-1c1 1 2 2 3 2s2 1 3 2c-1 0-2 0-3-1l-2 1c1 0 2 1 3 1 2 0 3 1 4 2 1 0 2 1 3 1l6 3c3 2 6 3 9 4l8 5c1 0 2 1 3 1 7 2 10-1 15-4l1 2 5-2h3v1h-1 0c3 0 8 1 9 3l1 2c-4-3-8-3-13-2-2 1-4 2-5 4s-2 4-1 7 4 6 6 8c1 2 4 3 6 4h0v1c1 0 2 0 2 1 1 1 2 1 3 2 3 1 7 2 10 4 4 2 6 4 9 8 0 0 1 1 1 2h0l-1 1v2c-1 1-1 2-2 3h-5c-4-1-7-3-10-5-3-3-7-5-9-8-5-4-9-10-14-13-14-10-30-18-47-22l-2-1v1c-5 0-9-1-13-2-8-1-14 1-21 3-4 2-8 4-11 5-1 0-1-1-1-1h0c0-1 5-4 6-5 1-3 4-6 4-9s0-6-2-9c-2-4-6-6-11-7h7z"></path><path d="M352 108c4 0 7 1 10 2h0c1 1 2 2 3 2h-1-2s-9-2-10-3h2 1 0c-1 0-2-1-3-1z" class="P"></path><defs><linearGradient id="m" x1="374.51" y1="114.515" x2="370.215" y2="118.282" xlink:href="#B"><stop offset="0" stop-color="#282a25"></stop><stop offset="1" stop-color="#3e3b40"></stop></linearGradient></defs><path fill="url(#m)" d="M362 110c9 3 17 9 24 15v1c-7-6-15-10-24-14h2 1c-1 0-2-1-3-2h0z"></path><path d="M325 115c3-1 5-1 8-1 10 0 20 3 30 6 3 1 7 2 10 4l-1 1-1-1c-4-2-23-8-25-7v1l-2-1h-1-1c-1 0-2 0-2-1h-1c-2 0-3 0-4-1s-8 0-10 0z" class="C"></path><path d="M298 123c9-4 17-8 27-8 2 0 9-1 10 0s2 1 4 1h1c0 1 1 1 2 1h1 1v1c-5 0-9-1-13-2-8-1-14 1-21 3-4 2-8 4-11 5-1 0-1-1-1-1z" class="U"></path><path d="M352 109c-9-1-18-1-27 1l-9 3c-1 0-3 1-4 1l-3-3v-4c2-4 5-7 8-9 8-4 24 1 32 3l-2 1c-6-2-13-3-19-4-5 0-10 1-14 4-2 2-4 4-4 7 0 1 0 2 1 3 1 0 1 1 2 0 3 0 7-2 9-3 8-3 17-3 25-2 2 0 4 0 5 1 1 0 2 1 3 1h0-1-2z" class="a"></path><path d="M352 108c-1-1-3-1-5-1-8-1-17-1-25 2-2 1-6 3-9 3-1 1-1 0-2 0-1-1-1-2-1-3 0-3 2-5 4-7 4-3 9-4 14-4 6 1 13 2 19 4 1 0 2 1 3 1 2 0 3 1 4 2 1 0 2 1 3 1l6 3c3 2 6 3 9 4l8 5c1 0 2 1 3 1 7 2 10-1 15-4l1 2c-3 5-5 8-3 14l1 3-11-9c-7-6-15-12-24-15-3-1-6-2-10-2z"></path><path d="M399 117l5-2h3v1h-1 0c3 0 8 1 9 3l1 2c-4-3-8-3-13-2-2 1-4 2-5 4s-2 4-1 7 4 6 6 8c1 2 4 3 6 4h0v1c1 0 2 0 2 1 1 1 2 1 3 2 3 1 7 2 10 4 4 2 6 4 9 8 0 0 1 1 1 2h0l-1 1v2c-1 1-1 2-2 3h-5c-4-1-7-3-10-5-3-3-7-5-9-8-5-4-9-10-14-13-14-10-30-18-47-22v-1c2-1 21 5 25 7l1 1 1-1c6 3 11 5 17 8 2 2 5 4 7 5-2-4-7-7-11-11v-1l11 9-1-3c-2-6 0-9 3-14z"></path><path d="M399 117l5-2h3v1h-1 0c3 0 8 1 9 3l1 2c-4-3-8-3-13-2-2 1-4 2-5 4s-2 4-1 7 4 6 6 8c1 2 4 3 6 4h0v1c1 0 2 0 2 1 1 1 2 1 3 2 3 1 7 2 10 4 4 2 6 4 9 8 0 0 1 1 1 2h0l-1 1v2l-1-2c0-2-4-5-6-6-5-4-11-5-17-8-7-4-13-10-20-14-5-3-11-5-17-8l1-1c6 3 11 5 17 8 2 2 5 4 7 5-2-4-7-7-11-11v-1l11 9-1-3c-2-6 0-9 3-14z" class="I"></path><path d="M329 470c1 0 2 2 3 3 3 3 5 6 8 9 3 5 6 9 9 13 2 4 4 8 5 12 7 16 8 33 5 51-1 5-2 11-5 16l-3 6h1l1 1c-1 1-1 2-2 3v1c-1 2-4 5-5 6-6 5-11 11-17 15h0l-5 4v1h1v1c-2 1-4 2-5 2h-1v1 1h-2l-3 1-9 1-1 3c-2 1-4 1-6 2h-5v-1c-2-1-7 0-9 1l-1-1c2-1 4-1 6-1-1-1-1-1-2-1l-5 1-1-1c-1-1-5 0-6 0h-3c-5 0-9-1-13-2h0v1l-4 1c-4-1-7-2-10-2l-1 1-22-6h-1-2-1c-3 0-12-8-15-10 0-1-2-2-3-2h-2c-2-1-4-3-5-4-2-1-3-2-4-4-2-1-4-3-5-5-3-3-6-6-8-10v-1c-2-3-2-7-4-10-1-4-2-7-3-10 0-1 0-1 1-2l2 8c1-1 1-1 1-2-2-9-3-17-1-25h1c1-4 3-9 5-13 2-3 4-7 6-10 1-1 2-2 3-2l1-2c1 2 0 3 1 5h0c-2 8-2 16-2 24 1 6 1 13 4 19h0c0 1 1 1 1 1 0-1 0-2 1-4 1-1 2-2 4-3 1 1 3 1 4 2 6 6 12 12 19 17l1-1 2-1 1 2c5 4 9 7 15 9l2 2c5 1 11 3 16 3s10 1 14-1h1c1 1 5 0 6 0 2 0 4-1 5-1 6-2 13-3 19-6 6-1 12-4 18-8 7-4 14-13 18-21 1-2 1-5 2-8h-1c0-2 1-4 1-6 1-5 2-9 1-13 1-1 0-2 1-3v-3c0-3-1-6-3-8v-1l1-1h1c1 4 3 8 3 12 1 2 1 5 2 7 0-3 0-5-1-8-1-5-2-11-4-16h1c-2-7-5-13-8-19-2-2-7-9-8-10l4 3 1-1-2-2z"></path><path d="M220 581l3 1h-1v1h0 0l-3-1 1-1z" class="J"></path><path d="M234 589h1c1 0 1 0 1 1v1c0 1 0 2-1 3 0 0-1 0-2-1v-1c0-1 0-2 1-3z" class="K"></path><path d="M234 589h1c1 0 1 0 1 1h0c-1 1-1 1-2 0v-1z" class="T"></path><path d="M223 582c5 2 11 4 17 5l-2 1-16-5h0 0v-1h1z" class="X"></path><path d="M223 568l1 2c5 4 9 7 15 9l2 2c-5-1-11-4-15-7-2-1-4-3-6-4l1-1 2-1z" class="Z"></path><path d="M195 566l1-1c7 8 14 12 24 16l-1 1c-10-3-17-8-24-16z" class="G"></path><path d="M240 587c13 3 27 8 41 9h0c-2 0-5 1-8 1-5-1-10-2-15-4l-20-5 2-1z" class="L"></path><path d="M340 543c1 1 1 1 1 2-1 3-1 6-3 9-5 12-15 23-27 30-10 7-23 11-36 8-2-1-4-1-5-3-1-1-1-3 0-4 0 0 1-1 1-2h1c-1 2-1 3-1 5 1 2 2 2 4 3 8 2 19 0 27-3 4-2 8-4 12-7 13-8 23-23 26-38z" class="F"></path><path d="M340 539l1-2c0 1 0 3-1 5v1c-3 15-13 30-26 38-4 3-8 5-12 7-8 3-19 5-27 3-2-1-3-1-4-3 0-2 0-3 1-5 1 1 5 0 6 0 2 0 4-1 5-1 6-2 13-3 19-6 6-1 12-4 18-8 7-4 14-13 18-21 1-2 1-5 2-8z"></path><path d="M342 499l3 9c4 15 3 29-3 44-1 4-2 8-4 11-11 20-30 30-51 37-6 1-13 3-19 3-4 1-10 0-14-1h-4l-1 1c-3-1-5-2-8 0 0 1-1 1-1 2 0 2 1 3 2 4v1 1l-1-2c-2-1-2-2-2-4s1-3 3-4h0c-1-1-2-1-3-1-6-2-12-4-18-7-11-5-23-15-29-26s-8-24-8-36c0-7 1-14 3-20l1-2c1 2 0 3 1 5h0c-2 8-2 16-2 24 1 6 1 13 4 19h0c0 1 1 1 1 1l3 5 1 2-1 1h-1c10 15 24 25 41 30 13 4 29 4 43 2 1 0 4 0 6-1 3-2 7-2 10-3 6-2 11-5 16-8 12-6 23-17 28-30 2-3 3-7 4-10l-1-1c0-1 0-1-1-2v-1c1-2 1-4 1-5l-1 2h-1c0-2 1-4 1-6 1-5 2-9 1-13 1-1 0-2 1-3v-3c0-3-1-6-3-8v-1l1-1h1c1 4 3 8 3 12 1 2 1 5 2 7 0-3 0-5-1-8-1-5-2-11-4-16h1z" class="V"></path><path d="M191 557c0 1 1 1 1 1l3 5 1 2-1 1h-1 0c-1-2-3-7-3-9z" class="B"></path><defs><linearGradient id="n" x1="337.661" y1="528.503" x2="345.747" y2="524.587" xlink:href="#B"><stop offset="0" stop-color="#64626b"></stop><stop offset="1" stop-color="#7a7b77"></stop></linearGradient></defs><path fill="url(#n)" d="M342 514c2 9 2 18 1 27-1 1-1 3-1 4v1l-1-1c0-1 0-1-1-2v-1c1-2 1-4 1-5l-1 2h-1c0-2 1-4 1-6 1-5 2-9 1-13 1-1 0-2 1-3v-3z"></path><path d="M173 536c1-4 3-9 5-13 2-3 4-7 6-10 1-1 2-2 3-2-2 6-3 13-3 20 0 12 2 25 8 36s18 21 29 26c6 3 12 5 18 7 1 0 2 0 3 1h0c-2 1-3 2-3 4s0 3 2 4l1 2 1 1h0c1 1 1 1 1 2 2 1 4 1 6 2v-1h1-1l1-1c2 1 5 2 7 2l1 1v1h0v1l-4 1c-4-1-7-2-10-2l-1 1-22-6h-1-2-1c-3 0-12-8-15-10 0-1-2-2-3-2h-2c-2-1-4-3-5-4-2-1-3-2-4-4-2-1-4-3-5-5-3-3-6-6-8-10v-1c-2-3-2-7-4-10-1-4-2-7-3-10 0-1 0-1 1-2l2 8c1-1 1-1 1-2-2-9-3-17-1-25h1z"></path><path d="M173 561h1l3 9c-3-2-4-4-5-7 1-1 1-1 1-2z" class="M"></path><path d="M173 561c-2-9-3-17-1-25h1c-1 9-1 16 1 25h-1z" class="B"></path><path d="M169 557c0-1 0-1 1-2l2 8c1 3 2 5 5 7 3 9 9 17 16 23 4 3 7 5 10 8 5 4 10 9 16 12h-1c-3 0-12-8-15-10 0-1-2-2-3-2h-2c-2-1-4-3-5-4-2-1-3-2-4-4-2-1-4-3-5-5-3-3-6-6-8-10v-1c-2-3-2-7-4-10-1-4-2-7-3-10z" class="O"></path><path d="M189 593h0c-2-4-5-6-8-10-2-2-3-5-5-8h1c4 6 8 12 13 17 3 3 7 6 10 9h-2c-2-1-4-3-5-4-2-1-3-2-4-4z" class="K"></path><path d="M222 613c-2-1-4-2-7-4-7-4-13-11-18-18-4-6-7-12-10-19-6-14-9-30-6-45 0-3 1-6 2-9 1-1 1-3 2-4 0 1-1 3-1 4-4 15-4 30 1 45 5 14 12 30 24 40 4 3 9 6 14 9-2-2-4-4-6-7s-4-6-4-9c1-1 2-2 3-2 6-3 20 15 26 18h1 0c1 1 1 1 1 2h0c-4-1-7-4-10-7-4-3-13-14-18-12-1 1-1 1-1 2 0 4 3 9 6 11 6 6 15 8 24 10l-1 1-22-6z" class="B"></path><path d="M245 618c-9-2-18-4-24-10-3-2-6-7-6-11 0-1 0-1 1-2 5-2 14 9 18 12 3 3 6 6 10 7h0c2 1 4 1 6 2v-1h1-1l1-1c2 1 5 2 7 2l1 1v1h0v1l-4 1c-4-1-7-2-10-2z"></path><path d="M251 614c2 1 5 2 7 2l1 1v1h0l-9-2v-1h1-1l1-1z" class="L"></path><path d="M329 470c1 0 2 2 3 3 3 3 5 6 8 9 3 5 6 9 9 13 2 4 4 8 5 12 7 16 8 33 5 51-1 5-2 11-5 16l-3 6h1l1 1c-1 1-1 2-2 3v1c-1 2-4 5-5 6-6 5-11 11-17 15h0l-5 4v1h1v1c-2 1-4 2-5 2h-1v1 1h-2l-3 1-9 1-1 3c-2 1-4 1-6 2h-5v-1c-2-1-7 0-9 1l-1-1c2-1 4-1 6-1-1-1-1-1-2-1l-5 1-1-1c-1-1-5 0-6 0h-3c-5 0-9-1-13-2v-1l-1-1c-2 0-5-1-7-2l-1 1h1-1v1c-2-1-4-1-6-2 0-1 0-1-1-2h0l-1-1v-1-1c-1-1-2-2-2-4 0-1 1-1 1-2 3-2 5-1 8 0l1-1h4c4 1 10 2 14 1 6 0 13-2 19-3 21-7 40-17 51-37 2-3 3-7 4-11 6-15 7-29 3-44l-3-9c-2-7-5-13-8-19-2-2-7-9-8-10l4 3 1-1-2-2z"></path><path d="M341 560h2c1 0 1 1 2 2 0 1 0 1-1 2h0c-2 0-3-1-4-2 0-1 1-1 1-2z" class="E"></path><g class="B"><path d="M300 604c-3 1-8 1-11 1-6 0-12 0-17-1 3-1 7 0 10-1h16l-2 1h4z"></path><path d="M333 588h2c-8 8-17 12-27 15-3 0-5 1-8 1h0-4l2-1c14-2 25-6 35-15z"></path></g><path d="M313 604c6-2 11-4 17-7 2-2 5-3 7-5 4-4 8-9 12-15v2c-2 4-4 7-7 10-7 7-17 13-27 16l-2 1v-2z" class="J"></path><path d="M351 512c1 1 2 1 2 3h0l1 2v2c5 18-1 41-10 57-2 4-6 9-9 12h-2l5-6c12-16 18-36 16-56-1-4-2-9-3-13v-1z" class="O"></path><path d="M329 470c1 0 2 2 3 3 3 3 5 6 8 9 3 5 6 9 9 13 2 4 4 8 5 12 7 16 8 33 5 51-1 5-2 11-5 16l-3 6h1l1 1c-1 1-1 2-2 3v1c-1 2-4 5-5 6-6 5-11 11-17 15h0l-5 4v1h1v1c-2 1-4 2-5 2h-1v1 1h-2l-3 1-9 1-1 3c-2 1-4 1-6 2h-5v-1c-2-1-7 0-9 1l-1-1c2-1 4-1 6-1-1-1-1-1-2-1l-5 1-1-1c-1-1-5 0-6 0h-3c-5 0-9-1-13-2v-1l-1-1c-2 0-5-1-7-2l-1 1h1-1v1c-2-1-4-1-6-2 0-1 0-1-1-2h0l-1-1v-1-1c-1-1-2-2-2-4 0-1 1-1 1-2 3-2 5-1 8 0l1-1h4v1c1 1 3 1 5 1 7 2 14 3 21 4h8c9 0 17-1 25-4v2l2-1c10-3 20-9 27-16 3-3 5-6 7-10v-2l2-3c4-9 5-17 6-27s1-20-3-30l-1-2h0c0-2-1-2-2-3-4-14-12-28-21-39l1-1-2-2z"></path><path d="M242 609c3 3 6 4 9 5l-1 1h1-1v1c-2-1-4-1-6-2 0-1 0-1-1-2h0l-1-1v-1-1z" class="O"></path><path d="M331 472c4 5 6 9 9 14 6 8 11 18 14 28l-1 1h0c0-2-1-2-2-3-4-14-12-28-21-39l1-1z" class="J"></path><path d="M353 515l1-1c2 6 4 12 5 18 0 4 0 9-1 14 0 10-2 20-7 30l-2 3v-2l2-3c4-9 5-17 6-27s1-20-3-30l-1-2z" class="L"></path><path d="M249 603l1-1h4v1c1 1 3 1 5 1 7 2 14 3 21 4h8c9 0 17-1 25-4v2c-5 2-10 3-16 3-2 1-5 1-7 1-3 1-7 0-10 0-11-1-21-4-31-7z" class="J"></path><path d="M288 608c9 0 17-1 25-4v2c-5 2-10 3-16 3-2 1-5 1-7 1h-4v-1h4c1 1 0 0 1 0s3 1 4 0h3c0-1 1-1 1-1 1 0 1 1 1 0-4 0-8 2-12 0h0z" class="G"></path><path d="M351 580h1l1 1c-1 1-1 2-2 3v1c-1 2-4 5-5 6h-2-1c-3 2-6 6-9 8-2 1-4 3-5 4l-1 1c-1 0-2 1-3 1l-2 2h-1c-1 1-2 1-3 2h-1l-2 1-1 1h-1c-1 1-1 1-2 1h-1c-1 0-1 0-2 1h-1-1c-1 0-2 0-2 1h0-2-2l-1 1h-1-2c0 1-1 1-2 1h-3c-1 0-1 0-2 1h-1-4c-3 1-7 1-11 1v-1h1c9 0 18-3 27-4 10-2 20-6 28-12 3-2 6-5 9-8 2-2 4-4 6-5 3-3 4-6 6-8z" class="H"></path><path d="M275 617h-1v1c4 0 8 0 11-1h4 1c1-1 1-1 2-1h3c1 0 2 0 2-1h2 1l1-1h2 2 0c0-1 1-1 2-1h1 1c1-1 1-1 2-1h1c1 0 1 0 2-1h1l1-1 2-1h1c1-1 2-1 3-2h1l2-2c1 0 2-1 3-1l1-1c1-1 3-3 5-4 3-2 6-6 9-8h1 2c-6 5-11 11-17 15h0l-5 4v1h1v1c-2 1-4 2-5 2h-1v1 1h-2l-3 1-9 1-1 3c-2 1-4 1-6 2h-5v-1c-2-1-7 0-9 1l-1-1c2-1 4-1 6-1-1-1-1-1-2-1l-5 1-1-1c-1-1-5 0-6 0h-3c-5 0-9-1-13-2v-1l-1-1 17 1z" class="a"></path><path d="M344 591h2c-6 5-11 11-17 15h0l-5 4v1h1v1c-2 1-4 2-5 2l-1-2c-12 5-25 6-38 8-1-1-5 0-6 0 1-1 8-1 10-2h3 1c4 0 10-2 14-3h1 2c1-1 1-1 2-1 2-1 4-1 6-2 2 0 4-2 7-2 0-1 1-1 1-1l1-1c1 0 2-1 3-2l11-8c1-1 2-2 2-3 2-1 3-2 5-4z" class="O"></path><path d="M319 612c1 0 2-1 3-1 2-2 5-4 7-5h0l-5 4v1h1v1c-2 1-4 2-5 2l-1-2z" class="P"></path><path d="M281 620c13-2 26-3 38-8l1 2h-1v1 1h-2l-3 1-9 1-1 3c-2 1-4 1-6 2h-5v-1c-2-1-7 0-9 1l-1-1c2-1 4-1 6-1-1-1-1-1-2-1l-5 1-1-1z" class="Z"></path><path d="M319 614v1 1h-2l-3 1c-1 0-1 0-1-1l6-2z" class="M"></path><path d="M287 620l19-3 7-1c0 1 0 1 1 1l-9 1-9 2c-2 0-5 1-7 1-1-1-1-1-2-1z" class="K"></path><path d="M289 621c2 0 5-1 7-1l9-2-1 3c-2 1-4 1-6 2h-5v-1c-2-1-7 0-9 1l-1-1c2-1 4-1 6-1z" class="Q"></path><path d="M547 317h1c1 2 0 41 1 48-14-2-28-3-41 3-3 1-6 3-9 5-5 4-9 8-12 14-6 15-6 29-6 44v62c-2 29-8 59-24 84-7 10-15 19-23 28-5 5-11 10-17 14-13 10-27 15-42 19l-10 2c-4 0-8 2-12 2-2-1-4-1-6-1 1 0 2 0 3-1h0-3 0-4-1c-2-1-4 0-6 0v-1h7l1 1c1-1 3 0 5-1h3v-1c-2 1-4 1-6 1l1-1h0-4c2-1 4 0 6-1l-1-1h-3 8l-1-1h1l1-1h-5c1 0 2-1 3-1-5-1-10-2-15-4-7-3-12-6-19-10 8 1 14-3 21-6 2-1 4-1 6-2 1 0 2-1 2-2l1 1 1-1 3-1 1 2 1-1c1-1 2-1 3-3l2-1h0 3c1 0 2-2 3-2 2-2 5-3 7-5 2 0 2-1 4-1h0c2-2 4-3 6-5 1-1 3-2 4-3 1 0 1-1 2-2l-1-1c2 0 3-2 4-2 0-1 1-1 1-2h1 0v-1l1-3c2-3 6-5 8-8h1 1c0 1 0 1 1 1l2-2v1l7-7h2l7-7c0-2 2-4 3-5l6-9c4-5 6-11 9-18 5-11 10-23 11-35l1-1v1l6-3c4-3 8-6 11-10 0 0 1-1 1-2 1-1 2-3 3-4 1-3 1-5 1-7h0 1v-7c1-3 0-11-1-15s-3-9-5-13h2v1h1l-1-3c1 0 1 0 2-1 0 2 2 10 3 11h0c0-1-1-2-1-3-1-4-2-9-3-12 0-1 0-2-1-3 0-3-1-7-2-10s-3-5-4-8c0 0 0-1-1-1-1-2-1-4-2-5 1 1 3 2 5 2h1 1c5 1 7-2 11-4v1-3l1-3c2-2 3-8 3-11 0-2 0-6-2-7-1-1-2-3-2-4h-1 0-1c-1 0-2-3-2-3v-2l-4-3-1-1h-1l-2-2c-3-1-5-2-8-4h0c-1-1-4-3-5-4-2-2-4-5-6-8l2 2h0v-2c0-1-1-1-1-2-1-1-2-2-2-3s0-2 1-3l-1-1c0-1 0-2 1-3 2-3 5-5 10-6 1 0 3 1 4 0 1 0 2-1 3-1 5 0 10 1 15 2 6 0 12-1 18-1h4v1l7-1c3 1 6 0 9 0h13c0-1 0-1 1-1h4 8c0-1 1-1 1-2z" class="R"></path><path d="M470 485h6v1l-1 1h-1l-4-2zm-94 144h1l-4 1h3 2c-1 0-7 1-8 1h-2 0l1-1h-1c3-1 6-1 8-1zm117-266h0l1 1v2c1 1 1 1 1 2h-1c-1 0-2 0-3-1v-1c0-1 1-2 2-3z" class="I"></path><path d="M441 583c0 2-2 4-3 6l-6 7c-2 3-4 6-7 8v1l-1 1-1 1v-2c8-6 12-14 18-22z" class="S"></path><path d="M487 387v-1-2s1-1 1-2c2-2 3-4 5-6l2-3h1c-2 2-4 3-4 5l5-5h0 2c-5 4-9 8-12 14z" class="D"></path><path d="M417 619l1-2h1c0-1-1 0 0-1 1-2 3-2 5-4l5-5c1-1 2-2 3-2h2c-5 5-11 10-17 14z" class="Z"></path><defs><linearGradient id="o" x1="474.647" y1="475.832" x2="472.885" y2="462.367" xlink:href="#B"><stop offset="0" stop-color="#2f2e2d"></stop><stop offset="1" stop-color="#48494b"></stop></linearGradient></defs><path fill="url(#o)" d="M475 463h0 1v-7 6h1c0 3 0 6-2 9-1 4-5 9-8 12h-1c1-2 3-4 4-5s0-1 0-2c0 0 1-1 1-2 1-1 2-3 3-4 1-3 1-5 1-7z"></path><path d="M474 425c0 2 2 10 3 11 1 8 2 18 0 26h-1v-6c1-3 0-11-1-15s-3-9-5-13h2v1h1l-1-3c1 0 1 0 2-1z" class="C"></path><path d="M359 605h3c1 0 2-2 3-2 2-2 5-3 7-5 2 0 2-1 4-1h0c0 2-1 3-2 4l2-1v1c-1 0 0 1 0 2l-1 3c1 3 2 6 4 8 2 1 4 2 6 2 10 2 20-2 29-7l1 1c2-2 5-4 8-5v2l1-1v2c-5 4-11 8-16 11-3 2-5 3-8 4-7 3-13 4-20 5l-3 1h-1c-2 0-5 0-8 1h1l-1 1h0 2l1 1c-3 0-5 0-8 1h-10-1c-5-1-10-2-15-4-7-3-12-6-19-10 8 1 14-3 21-6 2-1 4-1 6-2 1 0 2-1 2-2l1 1 1-1 3-1 1 2 1-1c1-1 2-1 3-3l2-1h0z"></path><path d="M365 608h1l1 1h-1-2 0l1-1z" class="Z"></path><path d="M352 608l1 2c-1 1-3 1-5 1v-1l1-1 3-1z" class="S"></path><path d="M374 601l2-1v1c-1 0 0 1 0 2l-1 3h0c-1 1-1 2-1 3-1-3-1-5 0-8z" class="a"></path><path d="M339 617c1-2 2-3 3-4h3l1 1c-1 0-1 1-2 1-2 1-3 1-4 3h0c0-1 0-1-1-1z" class="N"></path><path d="M374 609c0-1 0-2 1-3h0c1 3 2 6 4 8 2 1 4 2 6 2l-1 1-3-1c-3-1-5-4-7-7z" class="E"></path><path d="M392 619c-4 1-7 1-10 1-7-1-13-2-17-8 1 1 2 2 4 3 4 2 9 3 14 3 3 1 6 0 9 1z" class="M"></path><path d="M372 622c-4-3-8-8-14-8-2 0-3 0-5 2s-2 5-2 8h-1s0-1-1-2c0-2 1-5 2-6 2-2 3-3 6-4 7 0 11 5 15 9v1zm-33-5c1 0 1 0 1 1 0 3 1 5 4 7 6 6 16 5 24 5h1l-1 1h0c-3 1-7 1-10 0-7 0-13-2-18-7-1-3-1-4-1-7z" class="D"></path><defs><linearGradient id="p" x1="409.582" y1="607.17" x2="392.609" y2="620.927" xlink:href="#B"><stop offset="0" stop-color="#3d3e3d"></stop><stop offset="1" stop-color="#616061"></stop></linearGradient></defs><path fill="url(#p)" d="M385 616c10 2 20-2 29-7l1 1c-1 1-6 3-6 5l-12 3c-2 1-3 1-5 1-3-1-6 0-9-1h4c-1 0-2 0-3-1l1-1z"></path><path d="M415 610c2-2 5-4 8-5v2c-4 3-7 6-11 8-9 6-18 11-29 10-4 0-8-1-11-3h0v-1c3 1 5 2 8 3 9 1 19-2 27-7 1-1 2-1 2-2 0-2 5-4 6-5z" class="I"></path><path d="M464 320c1 0 2-1 3-1 5 0 10 1 15 2 6 0 12-1 18-1h4v1l7-1c3 1 6 0 9 0h13l2 1c4 1 8 4 9 8 1 2 1 4 2 6 0 3 0 6-1 9-1 5-3 9-8 13-5 3-14 4-20 3h-1-2c-7 0-14 0-21 1-3 1-5 2-7 4-1 1-2 2-2 4l-1-1v1c-1-1-2-3-2-4h-1 0-1c-1 0-2-3-2-3v-2l-4-3-1-1h-1l-2-2c-3-1-5-2-8-4h0c-1-1-4-3-5-4-2-2-4-5-6-8l2 2h0v-2c0-1-1-1-1-2-1-1-2-2-2-3s0-2 1-3l-1-1c0-1 0-2 1-3 2-3 5-5 10-6 1 0 3 1 4 0z"></path><path d="M535 321c4 1 8 4 9 8 1 2 1 4 2 6 0 3 0 6-1 9-1 5-3 9-8 13-5 3-14 4-20 3h-1 0c0-1-1-1-2-1-2-3-3-6-3-9h0c1-1 1-2 1-3 1-2 3-5 5-6h1c1 0 2-1 3 0h1c1 0 2 1 3 2s2 2 2 4l-3 3h-1c0-2 1-2 2-3v-2c0-1-1-2-3-3-2 0-4 0-5 1s-3 2-3 3c-2 3-2 5-1 8h0c0 2 1 3 2 4 3 0 6 0 9-1 4-2 7-6 8-10v-1c1-2 1-3 0-5s-3-3-5-4c-4-1-5 0-8 1h-1c1-1 2-1 3-2s4-1 6 0h0c3 1 5 3 7 5h2c1 0 1-1 2-1v-1h1 0c0 1 0 1-1 2-1 2-2 1-4 1 0 3 0 4-1 7-3 5-7 8-12 10 3 0 6 0 10-1 5-1 9-5 11-10 3-5 3-12 2-17-2-4-5-7-9-9v-1z" class="T"></path><path d="M510 344c1-4 2-9 5-13 2-3 6-5 10-6 3 0 7 1 10 3 2 2 3 5 4 8v3h-1v1c0-3 0-4-1-7h0c-1-3-4-4-6-5-3-2-6-2-8-1-4 2-7 4-8 8-2 3-2 6-3 9-1 6-5 11-11 13-1 1-3 1-4 1s-1 1-1 1h5c5 0 9-1 13 0 1 0 2 0 2 1h0-2c-7 0-14 0-21 1-3 1-5 2-7 4-1 1-2 2-2 4l-1-1v1c-1-1-2-3-2-4h-1 0-1c-1 0-2-3-2-3v-2l-4-3-1-1v-1c-1 0-1 0-2-1h0 0l2 1v-1c9 2 19 4 28 1 4-2 8-5 10-10v-1z" class="C"></path><path d="M476 357c2-1 6 0 8 1h1c0 1 1 2 1 2h0c-2 0-8-1-10-3z" class="P"></path><path d="M501 359c5 0 9-1 13 0 1 0 2 0 2 1h0-2-6-10v-1h3z" class="F"></path><path d="M473 357h3c2 2 8 3 10 3h0 4v1c-2 1-6 4-7 6v1 1c-1-1-2-3-2-4h-1 0-1c-1 0-2-3-2-3v-2l-4-3z"></path><path d="M477 360c2 2 3 3 4 5h-1 0-1c-1 0-2-3-2-3v-2z" class="M"></path><path d="M489 334l5-5c7-5 16-6 24-7 5-1 12-1 17 0 4 2 7 5 9 9 1 5 1 12-2 17-2 5-6 9-11 10-4 1-7 1-10 1 5-2 9-5 12-10 1-3 1-4 1-7 2 0 3 1 4-1 1-1 1-1 1-2h0v-3c-1-3-2-6-4-8-3-2-7-3-10-3-4 1-8 3-10 6-3 4-4 9-5 13v1c-1 0-2 2-3 2-1 2-3 3-5 4h0-2c-4 1-8-1-11-3-2-2-4-4-4-7-1-3 2-5 4-8v1z"></path><path d="M489 334c-2 2-3 5-2 8 0 2 3 5 5 6 3 2 6 2 10 2 2-1 4-3 5-5s1-4 1-6l-3-3h0c-1-1-3-1-4-1s-2 1-2 1l-1 1c-1 0-3 2-3 4 0 1 0 1 1 2s2 1 4 1c1 0 2 0 2-1 1-1 1-1 1-2s-1-2-1-2v-1c1 0 1 0 2 1 0 1 1 2 0 3 0 1-2 2-3 3s-4 1-5 0c-2 0-3-2-4-3 0-2 0-5 1-6 1-2 3-4 5-4 3-1 6 0 9 1 2 1 3 3 4 5 0 1 0 2-1 3v3 1c-1 0-2 2-3 2-1 2-3 3-5 4h0-2c-4 1-8-1-11-3-2-2-4-4-4-7-1-3 2-5 4-8v1z" class="Y"></path><path d="M464 320c1 0 2-1 3-1 5 0 10 1 15 2 6 0 12-1 18-1h4v1l7-1c3 1 6 0 9 0h13l2 1v1c-5-1-12-1-17 0-8 1-17 2-24 7l-5 5v-1c-2 3-5 5-4 8 0 3 2 5 4 7 3 2 7 4 11 3h2 0c2-1 4-2 5-4 1 0 2-2 3-2-2 5-6 8-10 10-9 3-19 1-28-1v1l-2-1h0 0c1 1 1 1 2 1v1h-1l-2-2c-3-1-5-2-8-4h0c-1-1-4-3-5-4-2-2-4-5-6-8l2 2h0v-2c0-1-1-1-1-2-1-1-2-2-2-3s0-2 1-3l-1-1c0-1 0-2 1-3 2-3 5-5 10-6 1 0 3 1 4 0z"></path><path d="M474 326c3 3 5 7 6 11h0-1l-1-4-1 1c1 1 1 2 1 3h0c-1-4-3-7-5-10l1-1z" class="Y"></path><path d="M460 320c1 0 3 1 4 0 2 0 3 0 5 1s4 3 5 4v1l-1 1c-2-2-3-4-6-5 0-1-2 0-2 0-5-1-8-1-11 2-2 2-3 4-4 6l-1-1c0-1 0-2 1-3 2-3 5-5 10-6z" class="T"></path><path d="M452 338c2 3 4 6 7 8 3 4 8 6 13 8v1l-2-1h0 0c1 1 1 1 2 1v1h-1l-2-2c-3-1-5-2-8-4h0c-1-1-4-3-5-4-2-2-4-5-6-8l2 2h0v-2z" class="E"></path><path d="M511 320c3 1 6 0 9 0h13l2 1v1c-5-1-12-1-17 0-8 1-17 2-24 7l-5 5v-1-1c-2 0-6 6-8 7 1-2 3-4 4-5l5-5c4-3 8-4 12-6-5 0-10 3-15 5-2 1-5 1-8 0-1-1-2-2-2-4 0-1 0-2 1-3v1 1 1c0 1 1 2 2 3 4 2 10-2 14-3 3-1 7-2 10-3l7-1z" class="N"></path><path d="M465 322s2-1 2 0c3 1 4 3 6 5 2 3 4 6 5 10h0c0-1 0-2-1-3l1-1 1 4h1 0v2c-1 3-4 7-7 8-3 2-7 1-10 0-2-1-3-3-4-5-2-3-1-6 0-9 1-2 2-4 4-5s4-1 6 0l1 4h-1c-1 0-1-1-2-2v-1c-1-1-2 0-4 1-1 1-3 4-3 6 0 3 1 5 2 7 1 1 3 2 5 2 1-1 2-1 3-2 2-2 3-6 3-9s-2-7-4-9c-1-1-3-2-4-3h0z" class="C"></path><path d="M467 322c3 1 4 3 6 5 2 3 4 6 5 10l-1 1v1 1c-2 3-5 4-7 5l1-1c2-3 4-8 3-12 0-3-4-8-7-10h0 0zm3 154c0 1 1 1 0 2s-3 3-4 5h1 0c-4 4-9 5-13 10 4-2 8-4 12-1 4 2 7 7 8 11 1 2 1 5 1 7h0v-2c-1 1-1 2-1 3v-4c0 7-1 14-2 22 0 3-1 6-1 8h-1v-2c-1 5-2 11-4 16v-3c-1-1-1-2-1-3v4l-1-2v1h0c-1 1-1 3-2 4l-3 11c0 1-1 1-1 2l-1-1v1h-1s-1 0-1 1c0 2-3 7-4 8h-1c-1 2-2 6-4 7-1-1 0 0 0-1h-1 0c0 1-1 2-2 2h0-1l-1 1c-6 8-10 16-18 22-3 1-6 3-8 5l-1-1c-9 5-19 9-29 7-2 0-4-1-6-2-2-2-3-5-4-8l1-3c0-1-1-2 0-2v-1l-2 1c1-1 2-2 2-4 2-2 4-3 6-5 1-1 3-2 4-3 1 0 1-1 2-2l-1-1c2 0 3-2 4-2 0-1 1-1 1-2h1 0v-1l1-3c2-3 6-5 8-8h1 1c0 1 0 1 1 1l2-2v1l7-7h2l7-7c0-2 2-4 3-5l6-9c4-5 6-11 9-18 5-11 10-23 11-35l1-1v1l6-3c4-3 8-6 11-10z"></path><path d="M435 561h1c1 2 1 2 0 3h-1v-3z" class="N"></path><path d="M440 553c1 0 2 1 2 1l-1 1-2-1 1-1z" class="Y"></path><path d="M436 552c1 0 3 0 4 1l-1 1-2-1h-1v-1z" class="C"></path><path d="M429 554c2-2 4-2 7-2v1h1c-2 0-4 1-7 2l-1-1z" class="Z"></path><path d="M400 586h1c1 0 2 0 3 1v2l-1 1-1-1c-1-1-2-2-2-3z" class="N"></path><path d="M442 554c2 2 4 4 4 6v1-1s0-1-1-1v1c-1-2-2-3-4-5l1-1z" class="P"></path><path d="M448 516h1c2 1 2 2 3 3l-2 2c-1 0-2-1-3-1 0-1 1-2 1-4z" class="C"></path><path d="M438 540h0l1 1h1c1 1 2 1 2 2s-1 2-1 2c-1 1-2 1-3 0 0-1-1-4 0-5z" class="N"></path><path d="M450 565h1v4c-1 3-2 5-4 8 0-4 2-9 3-12z" class="Q"></path><path d="M451 546c2 7 2 12 0 19h-1c2-5 1-12 0-17h1v-2z" class="C"></path><path d="M467 525v5l1-1-3 16v4l-1-2v1h0l3-23z" class="D"></path><path d="M416 563l7-7c-1 0-1 2 0 3-3 2-5 4-7 6-1 1-2 2-4 3h0c1-2 3-3 4-5z" class="S"></path><path d="M438 540c1-1 3-2 5-2s3 1 5 2l3 6v2h-1c0-2-1-4-3-6s-6-2-9-2h0z" class="Y"></path><path d="M429 554l1 1v1l-1 1c-1 1-2 4-1 6 0 1 2 4 3 5 3 1 7-1 9-1h1-1c-1 1-3 2-5 2s-4 0-6-2c-1-2-2-4-2-6 0-3 1-5 2-7zm22 15l3-10 1-1c-1 6-3 11-5 16-1 2-2 6-4 7-1-1 0 0 0-1h-1 0c0 1-1 2-2 2h0c1-2 3-3 4-5 2-3 3-5 4-8z" class="I"></path><path d="M457 499h0c-1 0-1-1-2-1-1-1-1-1 0-2 1 0 2-1 4 0 4 1 7 4 9 7 2 4 3 8 3 12l-1 1c-1-6-2-13-7-17-2-2-4-2-7-2h0l2 1h-1v1z" class="C"></path><path d="M454 559c2-8 3-20-2-27-1-2-3-3-5-4h-2c0 1 1 1 1 1 0 1 0 2-1 3h-2l-1-1c0-2 1-3 2-4v-1c4 1 7 2 10 5 5 8 3 19 1 27l-1 1z" class="D"></path><path d="M407 570l7-7h2c-1 2-3 3-4 5-6 5-12 10-19 14v-1l1-3c2-3 6-5 8-8h1 1c0 1 0 1 1 1l2-2v1z" class="U"></path><path d="M404 570c0 1 0 1 1 1l2-2v1l-3 3c-2 1-4 3-6 4 1-2 4-4 5-6v-1h1z" class="Z"></path><path d="M456 565l1-2c3-11 7-23 5-35 0-3-1-6-2-8-1-3-5-5-7-6-1 0-3 1-4 0 1 0 1 0 1-1h5c3 1 5 5 7 7 1 3 1 6 2 9 1 12-3 24-7 35v1h-1z" class="U"></path><path d="M445 560v-1c1 0 1 1 1 1v1-1c2 5 0 13-2 18l-2 4-1 1c-6 8-10 16-18 22-3 1-6 3-8 5l-1-1c2-1 4-2 7-4l-1-1 1-1c2 0 3-1 4-2 2-2 3-4 5-6h0c1 0 2-2 3-3 3-4 6-8 8-12 4-7 6-13 4-20z" class="Y"></path><path d="M458 498l-2-1h0c3 0 5 0 7 2 5 4 6 11 7 17l1-1c1 6 0 14-1 20-1 5-2 11-4 16v-3c-1-1-1-2-1-3l3-16-1 1v-5-6c-1-5-2-10-4-14-1-3-3-5-6-6v-1h1z"></path><path d="M468 513c1 6 0 11 0 16l-1 1v-5-6l1-6z" class="C"></path><path d="M457 499v-1h1c4 1 6 6 8 9 1 2 1 4 2 6l-1 6c-1-5-2-10-4-14-1-3-3-5-6-6z" class="Y"></path><path d="M471 515c1 6 0 14-1 20-1 5-2 11-4 16v-3c3-10 5-22 4-32l1-1z" class="D"></path><path d="M470 476c0 1 1 1 0 2s-3 3-4 5h1 0c-4 4-9 5-13 10 4-2 8-4 12-1 4 2 7 7 8 11 1 2 1 5 1 7h0v-2c-1 1-1 2-1 3v-4c-1-5-4-10-7-13-1-1-1-1-2-1l-1-1h-4c-2 0-4 1-6 2l-7 24-3 6c-1 1-2 3-3 5-2 3-3 8-5 12-3 6-8 13-13 18-1-1-1-3 0-3 0-2 2-4 3-5l6-9c4-5 6-11 9-18 5-11 10-23 11-35l1-1v1l6-3c4-3 8-6 11-10z" class="Q"></path><path d="M409 594c-2-2-2-6-1-9 1-4 4-11 8-13 2-2 6-2 9-1 1 0 2 1 3 1 3 3 4 6 4 9 0 4 0 9-2 13v1c-2 2-3 4-5 6v-2c1 0 1-1 1-1 1-2 2-4 2-6 0-3-2-5-4-7l-1 1h0c2 2 3 3 3 6 1 2 0 4-2 6h0c-2 2-4 3-6 4-2-1-3-1-4-1 1 0 1 0 2-1h-1c-1 0-1 0-2-1h-1l-4-4 1-1z"></path><path d="M425 571c1 0 2 1 3 1l-1 1c-1-1-3-1-4-1l-1-1h3zm-5 2c0 1 1 1 1 3h-1-3c0-2 0-2 1-3h2z" class="C"></path><path d="M423 586h0c-3-1-5 0-8 1h-1v-1c1-1 2-1 3-2 2 0 5 0 7 1l-1 1h0z" class="P"></path><path d="M427 573l1-1c3 3 4 6 4 9 0 4 0 9-2 13v1c-2 2-3 4-5 6v-2c1 0 1-1 1-1 4-4 5-9 5-14 0-4-1-8-4-11z" class="E"></path><path d="M420 598c0-1 1-1 1-2l1-1v-1c0-1 0-2 1-4h0c1 1 1 2 1 4h-1l-1 3c1 1 1 1 2 1-2 2-4 3-6 4-2-1-3-1-4-1 1 0 1 0 2-1h-1c-1 0-1 0-2-1h-1l-4-4 1-1c3 2 5 5 8 5 1 0 2 0 3-1h0 0z" class="b"></path><defs><linearGradient id="q" x1="410.919" y1="576.13" x2="417.084" y2="583.393" xlink:href="#B"><stop offset="0" stop-color="#424242"></stop><stop offset="1" stop-color="#5d5d5e"></stop></linearGradient></defs><path fill="url(#q)" d="M409 594c-2-2-2-6-1-9 1-4 4-11 8-13 2-2 6-2 9-1h-3l1 1h-3v1h-2c-2 0-3 1-4 2-3 4-5 11-5 16 1 2 2 4 4 5 2 2 4 2 7 2h0 0c-1 1-2 1-3 1-3 0-5-3-8-5z"></path><path d="M423 586h0l1-1c2 2 4 4 4 7 0 2-1 4-2 6 0 0 0 1-1 1v2c-1 1-2 2-4 2l-1 1 1 1c-3 2-5 3-7 4-9 5-19 9-29 7-2 0-4-1-6-2-2-2-3-5-4-8l1-3c0-1-1-2 0-2v-1l-2 1c1-1 2-2 2-4 2-2 4-3 6-5 1-1 3-2 4-3 1 0 1-1 2-2 3-1 5-1 8 0 1 0 1 0 2 1 3 2 7 3 10 7l4 4h1c1 1 1 1 2 1h1c-1 1-1 1-2 1 1 0 2 0 4 1 2-1 4-2 6-4h0c2-2 3-4 2-6 0-3-1-4-3-6z"></path><path d="M389 602s-1-1-1-2c1-1 2-4 4-5 1 0 2 0 2 1h0c-1 1-2 1-3 1-1 1-1 2-1 3-1 0 0 1 0 1l-1 1z" class="K"></path><path d="M407 608l9-3v1c-2 1-5 2-7 3-1-1-1-1-2-1z" class="a"></path><path d="M409 609c-5 1-10 0-14 0-7-2-13-6-17-12 3 1 5 3 7 5 7 5 14 6 22 6 1 0 1 0 2 1z" class="E"></path><path d="M388 587c3-1 5-1 8 0 1 0 1 0 2 1l-1 1h0c1 0 1 0 1 1h-1c2 1 3 2 4 4v1h-1c-1-2-3-4-6-5-4 0-8 1-12 3-3 3-5 6-6 10 0-1-1-2 0-2v-1l-2 1c1-1 2-2 2-4 2-2 4-3 6-5 1-1 3-2 4-3 1 0 1-1 2-2z" class="T"></path><path d="M388 587c3-1 5-1 8 0 1 0 1 0 2 1l-1 1h0c1 0 1 0 1 1h-1c-4-1-7-1-11-1 1 0 1-1 2-2z" class="I"></path><path d="M423 586h0l1-1c2 2 4 4 4 7 0 2-1 4-2 6 0 0 0 1-1 1v2c-1 1-2 2-4 2l-1 1-4 2v-1h0l4-2-3 1c-5 1-10 1-15 1-3 0-6 0-8-1-2 0-3-1-5-2l1-1 1 1c3 2 6 2 8 1h1c1-2 1-6 0-8h1v-1h0c1 4 1 6 0 10 6 0 11 0 17-2 2-1 4-2 6-4h0c2-2 3-4 2-6 0-3-1-4-3-6z" class="C"></path><path d="M398 588c3 2 7 3 10 7l4 4h1c1 1 1 1 2 1h1c-1 1-1 1-2 1 1 0 2 0 4 1-6 2-11 2-17 2 1-4 1-6 0-10h0c-1-2-2-3-4-4h1c0-1 0-1-1-1h0l1-1z"></path><defs><linearGradient id="r" x1="408.596" y1="593.594" x2="401.494" y2="595.359" xlink:href="#B"><stop offset="0" stop-color="#303031"></stop><stop offset="1" stop-color="#4a494a"></stop></linearGradient></defs><path fill="url(#r)" d="M398 588c3 2 7 3 10 7l4 4h1c1 1 1 1 2 1h1c-1 1-1 1-2 1-7-2-11-7-16-11 0-1 0-1-1-1h0l1-1z"></path><path d="M264 451c8 0 15 0 23 2h0c10 3 22 3 29 13v-1c4 4 6 10 7 15 2 3 5 5 7 8h-1l5 7c0 1 1 1 1 2v1l1 1c1 1 2 2 2 3 1 1 1 2 1 3v1c2 2 3 5 3 8v3c-1 1 0 2-1 3 1 4 0 8-1 13 0 2-1 4-1 6h1c-1 3-1 6-2 8-4 8-11 17-18 21-6 4-12 7-18 8-6 3-13 4-19 6-1 0-3 1-5 1-1 0-5 1-6 0h-1c-4 2-9 1-14 1s-11-2-16-3l-2-2c-6-2-10-5-15-9l-1-2-2 1-1 1c-7-5-13-11-19-17-1-1-3-1-4-2-2 1-3 2-4 3-1 2-1 3-1 4 0 0-1 0-1-1h0c-3-6-3-13-4-19 0-8 0-16 2-24 1-1 1-3 2-5 2-7 5-15 9-21 2-3 5-6 7-8 7-8 14-13 23-18 6-3 12-6 18-7 3-1 8-2 12-3h0c1 0 3 0 4-1z"></path><path d="M332 540c0-1 0-1 1-2h1v1c-1 1-1 1-2 1z" class="T"></path><path d="M334 538c1 0 2 1 2 2s-1 1-1 2c-1 0-2-1-3-1v-1c1 0 1 0 2-1v-1z" class="K"></path><path d="M264 483c1 1 2 2 3 4v2c-1 0-1 1-3 1v-1c0-1 1-1 1-2s0-2-1-2l-1-1 1-1z" class="B"></path><path d="M314 561h2c1 1 2 2 2 4h-1-2l-1-1c-1-1 0-2 0-3z" class="E"></path><path d="M332 497c1 0 2 0 3 1l1 1c1 1 2 2 2 3 1 1 1 2 1 3v1c0-1-1-1-2-2l-5-7z" class="F"></path><path d="M246 466v3c-4 1-7 3-11 5h-1c2-2 4-3 6-4h-1c1-2 5-3 7-4z" class="N"></path><path d="M218 540l2 6c1 0 1 1 1 1 0 1 1 2 1 3h0v1c1 1 1 2 1 2h0l-1-1c-2-3-4-6-5-10v-1c0-1 0-1 1-1z" class="X"></path><path d="M337 504c1 1 2 1 2 2 2 2 3 5 3 8v3c-1 1 0 2-1 3-1-6-2-11-4-16z" class="E"></path><path d="M213 522c1 2 0 4 1 6v1-2-3c0 1 0 4 1 5 0 4 1 8 3 11-1 0-1 0-1 1v1c-3-6-5-13-4-20z" class="V"></path><path d="M278 473c4-1 10 0 14 2 2 2 3 3 3 6 1 2 0 4-1 6-1 1-2 2-4 3 0 0-1 0-2-1h1c2-1 3-1 4-2 1-2 1-3 1-4 0-3-1-5-3-6-3-3-7-3-12-3l-1-1zm25 95h1v1h0c-1 1-3 2-4 3-3 2-6 4-9 5l-10 3h-1c-1 0-1 0-1-1h3c1 0 1 0 2-1 2 0 7-2 9-3 0-1 1-2 1-3l9-4z" class="B"></path><path d="M221 547c2 4 4 8 6 11 6 8 14 13 22 17l-1 1c-11-5-21-13-26-24l1 1h0s0-1-1-2v-1h0c0-1-1-2-1-3z" class="L"></path><path d="M291 577c1 1 7-1 8-2h1c0 1 1 1 2 0v1h-1 1c-6 3-13 4-19 6-1 0-3 1-5 1-1-1-3-1-5-1v-1h-5 0c4-1 9 0 13-1l10-3z" class="M"></path><path d="M278 485c1 2 1 4 1 6 0 3-1 5-4 7-2 2-5 3-9 3-3-1-5-2-7-5-2-2-3-5-2-8 0-2 1-4 3-5h3 1l-1 1c-2 0-2 0-3 1-2 1-2 3-2 5 0 3 1 5 3 7s5 3 8 2c2 0 6-2 7-4 2-2 2-5 2-8l-1-1c0-1 0-1 1-1z" class="c"></path><defs><linearGradient id="s" x1="311.484" y1="480.569" x2="336.052" y2="492.573" xlink:href="#B"><stop offset="0" stop-color="#656765"></stop><stop offset="1" stop-color="#9a9899"></stop></linearGradient></defs><path fill="url(#s)" d="M317 477l12 11 5 7c0 1 1 1 1 2v1c-1-1-2-1-3-1-2-3-4-6-7-9-4-4-8-7-13-11h2 3z"></path><path d="M224 498v1c-1 2-3 3-4 5h1c1-1 2-3 3-3v1c-2 2-3 4-5 7-4 6-4 13-4 20-1-1-1-4-1-5v3 2-1c-1-2 0-4-1-6v-3c1-9 5-15 11-21z" class="G"></path><path d="M288 467c2 0 3 1 4 2h1c4 2 8 5 12 8 1 1 1 2 2 2 5 4 9 9 13 13 1 0 0 0 1 1 0 1 1 2 2 3h0l1 2c2 2 3 5 4 7h-1c0-1-1-1-1-2-1-1-2-3-3-5l-3-5s-1 0-1-1c-1 0 0 0-1-1 0-1-10-10-12-10h0 0c1 1 1 2 1 3h-1-1c0-2 0-3-1-4v-1c0-1-5-5-6-5l-1-1h0c-1-1-4-2-5-3-2 0-3-1-4-2h-1l1-1z" class="Q"></path><path d="M339 539h1c-1 3-1 6-2 8-4 8-11 17-18 21-6 4-12 7-18 8h-1 1v-1c-1 1-2 1-2 0h-1c6-1 12-3 17-6 9-5 16-13 20-23 2-2 2-5 3-7z" class="F"></path><path d="M254 464c9-4 19-3 29-2 1 1 3 2 4 3-3-1-6-2-9-1h0c2 1 5 1 8 2l2 1-1 1c-3-1-6-1-9-2-2-1-2-1-4-1h-6v-1h-4 1-11z" class="D"></path><path d="M290 462c7 1 16 5 22 10h0l5 5h-3-2c-7-5-15-11-25-12-1-1-3-2-4-3l5 1h2 2c0 1 0 1 1 0h0 0c-2 0-2-1-3-1h0z" class="a"></path><path d="M290 462c7 1 16 5 22 10 1 2 3 3 4 5-6-5-13-9-19-12-3-1-6-1-9-2h2 2c0 1 0 1 1 0h0 0c-2 0-2-1-3-1h0z" class="B"></path><defs><linearGradient id="t" x1="318.125" y1="492.59" x2="291.539" y2="506.309" xlink:href="#B"><stop offset="0" stop-color="#c0bfc0"></stop><stop offset="1" stop-color="#ededee"></stop></linearGradient></defs><path fill="url(#t)" d="M271 510c2-3 4-7 7-10 7-6 14-8 23-8s17 4 23 11c0 0 0 1-1 1h0c-7-6-15-10-25-10-7 0-15 3-20 8-3 3-5 7-7 10v-2z"></path><path d="M234 474h1c-5 4-11 9-15 15-6 8-9 18-11 28-1 10-1 20 3 29 1 2 2 5 3 8l-2-1 1 1-1 1-6-11-1-2 1-4c-1-1-1-2-1-3h1v-5c0-3 0-7 1-10 1-11 4-21 10-30 4-6 10-12 16-16z" class="E"></path><path d="M207 538c1 5 3 10 6 15l1 1-1 1-6-11-1-2 1-4z" class="L"></path><path d="M213 553l2 1c9 13 21 23 37 26 7 2 14 2 21 2 2 0 4 0 5 1-1 0-5 1-6 0h-1c-4 2-9 1-14 1s-11-2-16-3l-2-2c-6-2-10-5-15-9l-1-2-3-2v-3c-2-2-5-5-7-8l1-1-1-1z" class="B"></path><path d="M220 563c4 3 8 6 11 10l-6-4-1 1-1-2-3-2v-3z" class="E"></path><path d="M239 579c2 1 4 1 7 2 2 0 8 0 10 2h0l1 1c-5 0-11-2-16-3l-2-2zm15-115h11-1 4v1c-16 1-33 10-44 22l-2 3c0 2 1 2 1 3h2c0-1 1-2 1-2l4-4c10-7 25-15 37-12 4 1 8-2 11-2l1 1c5 0 9 0 12 3 2 1 3 3 3 6 0 1 0 2-1 4-1 1-2 1-4 2h-1c1 1 2 1 2 1-4 0-6-1-9-4-1-1-2-2-3-2v1c-1 0-1 0-1 1 0 0 0-1-1-1-1-2-2-4-4-5-3-2-6-2-8-2-7 2-11 8-14 14h0c0-1 1-4 2-5 4-6 7-9 14-11h-9c-9 2-20 6-26 12-2 2-5 6-7 6-1 0-2-1-3-3v-1c1-2 3-3 4-5 7-7 14-12 24-16-1 0-1-1-1-1l-2 1v-3l8-2z" class="H"></path><path d="M254 464h11-1c-5 1-10 3-15 5-1 0-1-1-1-1l-2 1v-3l8-2z" class="S"></path><path d="M279 474c5 0 9 0 12 3 2 1 3 3 3 6 0 1 0 2-1 4-1 1-2 1-4 2-6 0-8-5-12-9-2-1-4-2-6-4l8-2zm-15-23c8 0 15 0 23 2h0c10 3 22 3 29 13v-1c4 4 6 10 7 15 2 3 5 5 7 8h-1l-12-11-5-5h0c-6-5-15-9-22-10h0c1 0 1 1 3 1h0 0c-1 1-1 1-1 0h-2-2l-5-1c-10-1-20-2-29 2l-8 2c-2 1-6 2-7 4h1c-2 1-4 2-6 4-6 4-12 10-16 16-6 9-9 19-10 30-1 3-1 7-1 10v5h-1c0 1 0 2 1 3l-1 4 1 2 6 11c2 3 5 6 7 8v3l3 2-2 1-1 1c-7-5-13-11-19-17-1-1-3-1-4-2-2 1-3 2-4 3-1 2-1 3-1 4 0 0-1 0-1-1h0c-3-6-3-13-4-19 0-8 0-16 2-24 1-1 1-3 2-5 2-7 5-15 9-21 2-3 5-6 7-8 7-8 14-13 23-18 6-3 12-6 18-7 3-1 8-2 12-3h0c1 0 3 0 4-1z"></path><path d="M197 550c2 0 3 1 5 2l-1 1c-1-1-3-1-4-2v-1z" class="a"></path><path d="M208 482h1c0 1 1 2 1 3h-1-2c-1 0-1 0-1-1s1-2 2-2z" class="B"></path><path d="M191 557v-1c1-2 2-4 2-6 2-1 3 0 4 0v1c-2 1-3 2-4 3-1 2-1 3-1 4 0 0-1 0-1-1h0z" class="E"></path><path d="M238 467c-1-1-2-2-2-4-1 0-1 0-1-1 1 0 1 0 1-1h3c0 2 0 3 1 5l-2 1h0z" class="K"></path><path d="M204 514v6h1c1-1 0-2 1-3v18c0 1 0 2 1 3l-1 4c-2-9-4-19-2-28z" class="G"></path><path d="M312 472c-5-6-10-11-17-14-2-1-5-1-6-3h0c2 0 5 1 7 2 3 2 6 4 8 5 6 5 11 12 17 17-1-5-3-9-5-13v-1c4 4 6 10 7 15 2 3 5 5 7 8h-1l-12-11-5-5z" class="O"></path><path d="M202 552h0c-5-9-11-17-13-28v-7h1c0 4 0 8 1 12 2 5 5 9 7 13 5 8 10 16 17 22l3 2 1-1 1 1 3 2-2 1-1 1c-7-5-13-11-19-17l1-1z" class="P"></path><path d="M219 565l1 1 3 2-2 1-3-3 1-1z" class="S"></path><path d="M219 565c-9-8-14-19-19-29-6-12-9-26-5-38v4c0 2-1 5 0 7 0 13 5 25 11 36l1-1 6 11c2 3 5 6 7 8v3l-1-1z" class="C"></path><defs><linearGradient id="u" x1="230.127" y1="464.515" x2="206.808" y2="520.153" xlink:href="#B"><stop offset="0" stop-color="#bcbabb"></stop><stop offset="1" stop-color="#ebecef"></stop></linearGradient></defs><path fill="url(#u)" d="M238 467h0v1l-1 1c0 1 0 1 1 1h-1c-2 3-6 4-9 6-11 8-20 19-22 34v7c-1 1 0 2-1 3h-1v-6-7c1-7 3-13 7-18 4-6 11-11 17-15l10-7z"></path><path d="M240 466c6-2 11-6 17-8 9-4 18-6 27-4h1c-11-1-22 2-32 7 3 0 7-1 10-2 9-1 19 0 27 3h0c1 0 1 1 3 1h0 0c-1 1-1 1-1 0h-2-2l-5-1c-10-1-20-2-29 2l-8 2c-2 1-6 2-7 4h-1c-1 0-1 0-1-1l1-1v-1l2-1z" class="J"></path><path d="M239 470h1c-2 1-4 2-6 4-6 4-12 10-16 16-6 9-9 19-10 30-1 3-1 7-1 10v5h-1v-18-7c2-15 11-26 22-34 3-2 7-3 9-6h1 1zm32 42c2-3 4-7 7-10 5-5 13-8 20-8 10 0 18 4 25 10h0c1 0 1-1 1-1 7 6 10 16 9 25 0 12-9 23-17 31l-8 7c-1 1-2 2-4 3h0v-1h-1l6-5c-7 3-12 4-19 3h-3c-1-1-1-1-2-1h-1c1-3 4 0 6-2h-1c-3 1-4 0-6-1-2 0-3 0-5-1h0l-1-1h0l4 1-3-3v-1s-3-3-4-3c-3-3-4-7-5-12 0 0-1 0-1-1v-2-3c0-4-2-8-1-12 1-2 1-3 2-5 0-2 1-5 2-7z"></path><path d="M305 529c1-1 2 0 2 0 1 1 1 1 1 2l-1 1h-2v-3z" class="N"></path><path d="M271 532c-1-3-2-7-1-11v1 1 4l1-1v-1c0 2 1 4 1 6l-1 1z" class="F"></path><path d="M272 531c1 2 2 4 4 6h-1 0-1c-2-1-2-3-3-5l1-1z" class="E"></path><path d="M313 542h1c1 0 1 1 2 1 0 2 0 2-1 3h0c-1 0-2-1-3-1 0-2 0-2 1-3z" class="Q"></path><path d="M319 525c1 0 1 0 2 1 0 1 1 1 1 3h-2-2c-1-1-1-1-1-2l2-2z" class="P"></path><path d="M285 541c2 0 5 0 7-1l2 1c-3 1-6 2-9 2l-1-2h1z" class="K"></path><path d="M276 537c3 2 5 3 8 4l1 2c-5-1-8-2-11-6h1 0 1z" class="a"></path><path d="M289 515c2 0 4 1 5 1l5 2v2h-1l-1-1c-3-2-6-2-8-1h-1c0-1 1-2 1-3z" class="H"></path><path d="M278 557c5 4 9 5 15 6h-3-1c-3 1-4 0-6-1-2 0-3 0-5-1h0l-1-1h0l4 1-3-3v-1z" class="B"></path><path d="M287 515c3-2 3-2 7-1 3 0 7 3 8 6h1-2 0l-2-2-5-2c-1 0-3-1-5-1h-2z" class="F"></path><path d="M286 499v2c-6 3-12 8-14 15-1 3-1 6-1 9v1l-1 1v-4-1-1c0-2 1-4 1-6 3-8 8-12 15-16zm1 16h2c0 1-1 2-1 3h1c-1 0-2 1-2 1-2 2-3 4-3 6-1 2 0 4 1 5s3 2 4 2c2 0 4-1 5-2 0-1 1-2 1-4-1-1-2-1-3-2-1 0-2 0-4 1h0 0v-2h3c2 0 3 0 4 2 1 1 1 2 1 3-1 1-2 3-3 3-1 1-4 2-6 1-2 0-3-2-4-4-2-3 0-6 1-9 1-2 2-3 3-4z" class="B"></path><path d="M299 518l2 2h0 2l1 4c0 4 0 10-4 13-1 2-4 3-6 4l-2-1s2 0 2-1c0 0 1 0 1-1h-2l3-1c3-2 4-5 5-8 0-4-1-6-3-9h1v-2z" class="L"></path><path d="M299 518l2 2h0 2l1 4-1-1h0c-2-1-3-2-4-3v-2z" class="B"></path><defs><linearGradient id="v" x1="283.518" y1="552.427" x2="287.101" y2="545.848" xlink:href="#B"><stop offset="0" stop-color="#a6a4a6"></stop><stop offset="1" stop-color="#c5c7c3"></stop></linearGradient></defs><path fill="url(#v)" d="M268 536c1 1 2 2 2 4s3 5 4 6c6 4 12 6 19 5h2 4v1c1 0 1-1 1-1v1c-3 2-9 3-13 2-6-1-14-5-17-11-1 0-1 0-1-1 0 0-1 0-1-1v-2-3z"></path><path d="M286 499c5-2 10-2 15-1 3 1 7 3 8 6 1 0 1 1 1 1v1h-1c-1-1-1-1-1-2l-3-3c-8 1-15 1-21 7-4 5-6 10-8 15l-1 1c0-3 2-7 3-10 4-6 9-11 16-13h9v-1h-1-4c-6 0-9 1-14 4 0-1 2-2 3-3h-1v-2z" class="T"></path><path d="M293 551c7-2 12-4 16-10 3-5 4-10 2-15-1-5-4-10-8-12-2-2-5-2-8-2 3-1 7 0 10 1 4 3 8 7 9 12 2 6 0 12-3 17-2 4-6 8-11 10v-1s0 1-1 1v-1h-4-2z" class="J"></path><path d="M286 501h1c-1 1-3 2-3 3 5-3 8-4 14-4h4 1v1h-9c-7 2-12 7-16 13-1 3-3 7-3 10l1-1v2l1-1v7h0c0 2 1 3 2 4l-1 1c2 2 4 4 7 5h-1c-3-1-5-2-8-4-2-2-3-4-4-6 0-2-1-4-1-6 0-3 0-6 1-9 2-7 8-12 14-15z"></path><path d="M276 523v2l1-1v7h0c0 2 1 3 2 4l-1 1c-1-1-2-2-2-3-1-2-2-6-1-9l1-1z" class="C"></path><path d="M323 504h0c1 0 1-1 1-1 7 6 10 16 9 25 0 12-9 23-17 31l-8 7c-1 1-2 2-4 3h0v-1h-1l6-5c-7 3-12 4-19 3h-3c-1-1-1-1-2-1h-1c1-3 4 0 6-2h3c3 0 6 0 9-1 9-1 14-8 19-15 3-5 4-13 3-19-2-6-6-13-12-16-5-3-10-5-16-3-5 1-9 3-11 7-1 2-3 4-4 6-2 3-3 6-2 9s2 5 5 7c3 1 6 1 9 0h0 2c0 1-1 1-1 1 0 1-2 1-2 1-2 1-5 1-7 1-3-1-5-3-7-5l1-1c-1-1-2-2-2-4h0v-7c1-5 6-10 10-14 5-4 11-5 17-4 7 1 13 5 17 11 6 8 7 16 5 26 2-4 4-9 5-14 1-9-3-18-8-25z" class="G"></path><path d="M224 498c6-4 12-6 20-6l3 1c5 1 10 5 13 9 4 5 5 10 6 15 0 1 0 3 1 5 1-4 2-8 4-12v2c-1 2-2 5-2 7-1 2-1 3-2 5-1 4 1 8 1 12v3 2c0 1 1 1 1 1 1 5 2 9 5 12 1 0 4 3 4 3v1l3 3-4-1h0l1 1h0c2 1 3 1 5 1 2 1 3 2 6 1h1c-2 2-5-1-6 2h1c1 0 1 0 2 1h3c7 1 12 0 19-3l-6 5-9 4c0 1-1 2-1 3-2 1-7 3-9 3-1 1-1 1-2 1h-3c0 1 0 1 1 1h1c-4 1-9 0-13 1-6 0-14-3-20-5l1-1c-8-4-16-9-22-17-2-3-4-7-6-11 0 0 0-1-1-1l-2-6c-2-3-3-7-3-11 0-7 0-14 4-20 2-3 3-5 5-7v-1c-1 0-2 2-3 3h-1c1-2 3-3 4-5v-1z"></path><path d="M256 535h2c1 0 2 1 2 2h0l-2 1c-1 0-2-1-3-1l1-2z" class="b"></path><path d="M236 497h1 1c0 1 0 4-1 5h-1c-1 0-1 0-2-1v-2c1-1 1-2 2-2z" class="E"></path><path d="M247 537l1 1c-2 2-6 4-9 5-2 0-3-1-4-2h0 0c5 1 8-1 12-4z" class="X"></path><path d="M232 540c0-2-1-4 0-6 0-3 2-5 4-6s4-2 5-1c2 0 3 1 4 2 0 1 0 1-1 2-1 0-1-1-1-1-1-1-1-2-2-2-1-1-3 0-4 0-2 1-3 3-4 5-1 3 0 6 0 9h0c-1-1-1-1-1-2z" class="V"></path><path d="M225 517c1-3 3-5 5-7 4-3 9-3 13-2-2 0-8 1-10 3-3 1-6 4-8 6z" class="P"></path><path d="M220 528c0-1 0-3 1-3v-1l2-2-1 4c0 4 0 8 1 13v3h-1c0-1 0-2-1-4 0-4-1-6-1-10z" class="D"></path><path d="M220 528c0-1 0-3 1-3v-1l2-2-1 4h0v5c-1-1-1-2-2-3z" class="R"></path><path d="M249 575c6 2 12 4 18 5 4 0 8 0 12-1 0 1 0 1 1 1h1c-4 1-9 0-13 1-6 0-14-3-20-5l1-1z" class="O"></path><path d="M248 519v-1c3 1 5 3 6 6v3c1-1 0-4 0-6v-1c1 0 1 1 2 2 0 4 0 9-3 12-2 2-3 3-5 4l-1-1c2-2 5-4 6-8 0-2 0-5-1-6h0c-1-2-3-3-4-4z" class="B"></path><path d="M243 508c5 2 9 4 11 8 1 2 1 4 2 6-1-1-1-2-2-2v1h0c-1-4-4-7-7-8-4-2-9-2-13-1l-1-1c2-2 8-3 10-3z" class="K"></path><path d="M253 562h-3l-8-3v-1c-5-2-9-8-12-12-3-6-3-13-1-19 1-4 4-7 8-9 3-1 8-2 11 0v1c-4-1-8-1-11 1-4 2-7 6-8 10-1 5 1 12 4 17 1 2 2 3 4 4-3-3-5-7-5-11 0 1 0 1 1 2h0 0c1 2 1 3 2 4 2 6 8 10 14 13l5 2h0v1h0-1z" class="E"></path><path d="M282 564c-3-1-7-3-10-4-3-2-6-3-8-6h0c-2 2-5 4-8 4s-5-1-6-3c-2-1-3-3-2-6 0-2 1-4 3-5 2-2 5-2 7-2s4 2 5 4c2 2 2 4 1 7 4 4 8 6 13 7l1 1h0c2 1 3 1 5 1 2 1 3 2 6 1h1c-2 2-5-1-6 2h-1l-1-1z" class="J"></path><path d="M255 543c2 0 3 0 4 1 2 0 3 2 4 4s0 3 0 5c-2 2-3 3-6 4-1 0-3-1-4-1-2-1-3-2-4-4 0-2 0-4 1-5 1-2 3-3 5-4z"></path><defs><linearGradient id="w" x1="244.906" y1="549.437" x2="261.15" y2="497.916" xlink:href="#B"><stop offset="0" stop-color="#c1bfc1"></stop><stop offset="1" stop-color="#e6e5e6"></stop></linearGradient></defs><path fill="url(#w)" d="M224 498c6-4 12-6 20-6l3 1c5 1 10 5 13 9 4 5 5 10 6 15 0 1 0 3 1 5 1-4 2-8 4-12v2c-1 2-2 5-2 7-1 2-1 3-2 5-1 4 1 8 1 12v3 2c0 1 1 1 1 1 1 5 2 9 5 12 1 0 4 3 4 3v1l3 3-4-1c-3-2-4-4-6-6-4-7-6-14-7-21-1-10 0-21-6-29-4-5-9-8-14-9-8 0-14 2-20 7v-1c-1 0-2 2-3 3h-1c1-2 3-3 4-5v-1z"></path><path d="M234 512c4-1 9-1 13 1 3 1 6 4 7 8h0c0 2 1 5 0 6v-3c-1-3-3-5-6-6-3-2-8-1-11 0-4 2-7 5-8 9-2 6-2 13 1 19 3 4 7 10 12 12v1c2 1 5 2 8 3h3 1 0v-1c7 2 13 3 20 3h8l1 1h1 1c1 0 1 0 2 1h3c-3 0-7 1-10 1-13 1-26 1-38-5-9-4-15-14-19-23-1-5-1-9-1-13l1-4v-2l2-3c2-2 5-5 8-6l1 1z"></path><path d="M233 511l1 1c-4 2-7 5-9 9v-1h-2l2-3c2-2 5-5 8-6z" class="B"></path><path d="M254 561c7 2 13 3 20 3h8l1 1h-9c-7 0-14 0-21-3h1 0v-1z" class="F"></path><path d="M223 520h2v1c-3 8-2 16 2 23 4 9 12 16 21 19 3 1 7 2 10 3h19c2 0 6 0 8-1 1 0 1 0 2 1h3c-3 0-7 1-10 1-13 1-26 1-38-5-9-4-15-14-19-23-1-5-1-9-1-13l1-4v-2z" class="H"></path><path d="M223 539c4 9 10 19 19 23 12 6 25 6 38 5 3 0 7-1 10-1 7 1 12 0 19-3l-6 5-9 4c0 1-1 2-1 3-2 1-7 3-9 3-1 1-1 1-2 1-3-1-6 0-9 0-2 0-4-1-5-1-6-1-13-3-18-6-11-6-24-17-28-30h1v-3z"></path><defs><linearGradient id="x" x1="281.763" y1="578.108" x2="280.805" y2="574.659" xlink:href="#B"><stop offset="0" stop-color="#7a7a7c"></stop><stop offset="1" stop-color="#9d9d9d"></stop></linearGradient></defs><path fill="url(#x)" d="M273 577c3 0 7-1 10-1 4-1 8-2 11-4 0 1-1 2-1 3-2 1-7 3-9 3-1 1-1 1-2 1-3-1-6 0-9 0-2 0-4-1-5-1h2c1-1 2-1 3-1z"></path><path d="M222 542h1c2 3 3 7 5 10s5 5 7 7c5 6 11 10 18 13 6 3 13 5 20 5-1 0-2 0-3 1h-2c-6-1-13-3-18-6-11-6-24-17-28-30z" class="H"></path><defs><linearGradient id="y" x1="443.15" y1="387.889" x2="245.559" y2="518.003" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#232223"></stop></linearGradient></defs><path fill="url(#y)" d="M309 319h0 1c5-1 10 0 14 0 6 0 13-1 19 0h1l1 1h0-9-5c-1-1-2 0-4-1h-3c-2-1-5 0-6 0-2 1-5 0-7 0 2 1 8 0 11 0s5 1 8 1c6 1 12 1 18 1 5 0 9-1 13-1s7 0 11 1h0c2-1 4 0 6 0h14c4-1 8-1 13-2 4 0 8 1 11 1 4 0 8-1 12-1h15 10 5c1 0 1 1 2 1-5 1-8 3-10 6-1 1-1 2-1 3l1 1c-1 1-1 2-1 3s1 2 2 3c0 1 1 1 1 2v2h0l-2-2c2 3 4 6 6 8 1 1 4 3 5 4h0c3 2 5 3 8 4l2 2h1l1 1 4 3v2s1 3 2 3h1 0 1c0 1 1 3 2 4 2 1 2 5 2 7 0 3-1 9-3 11l-1 3v3-1c-4 2-6 5-11 4h-1-1c-2 0-4-1-5-2 1 1 1 3 2 5 1 0 1 1 1 1 1 3 3 5 4 8s2 7 2 10c1 1 1 2 1 3 1 3 2 8 3 12 0 1 1 2 1 3h0c-1-1-3-9-3-11-1 1-1 1-2 1l1 3h-1v-1h-2c2 4 4 9 5 13s2 12 1 15v7h-1 0c0 2 0 4-1 7-1 1-2 3-3 4 0 1-1 2-1 2-3 4-7 7-11 10l-6 3v-1l-1 1c-1 12-6 24-11 35-3 7-5 13-9 18l-6 9c-1 1-3 3-3 5l-7 7h-2l-7 7v-1l-2 2c-1 0-1 0-1-1h-1-1c-2 3-6 5-8 8l-1 3v1h0-1c0 1-1 1-1 2-1 0-2 2-4 2l1 1c-1 1-1 2-2 2-1 1-3 2-4 3-2 2-4 3-6 5h0c-2 0-2 1-4 1-2 2-5 3-7 5-1 0-2 2-3 2h-3 0l-2 1c-1 2-2 2-3 3l-1 1-1-2-3 1-1 1-1-1c0 1-1 2-2 2-2 1-4 1-6 2-7 3-13 7-21 6h-1-8 0c-2 1-3 1-4 2l-1 1v-1l1-3 9-1 3-1h2v-1-1h1c1 0 3-1 5-2v-1h-1v-1h1c1 0 2-1 4-2 1 0 3-1 4-2h3c2 0 4-1 5 0l-3 2h0c0 1 0 1 1 1h0 0c2-1 5-2 7-4 3-1 5-2 7-4 4-2 8-5 11-8 4-3 7-7 11-11 6-6 12-13 16-21 8-14 11-29 13-45 1-14 1-28 1-42v-34c0-8 0-16-1-24-1-15-5-29-17-39-15-13-36-13-55-13h-24l1-45z"></path><path d="M430 422c1 0 2-1 2 0 1 0 1 0 1 1h-1l-2 1v-1-1z" class="N"></path><path d="M429 519l1 1 1-1-1 4v4h-1v-8z" class="M"></path><path d="M414 426l-1-1v-1c0-1 1-1 2-1 2 0 2 0 3 1h-1v2h0l-1-1-2 1z" class="G"></path><path d="M411 453c2 1 4 2 7 2h3 0-2l-2 1c-3 0-4 0-7-1l1-2z" class="B"></path><path d="M409 445h1l1 1c-1 2-2 4-1 6 0 0 0 1 1 1l-1 2c0-1-1-2-2-3-1-3 0-5 1-7z" class="G"></path><path d="M314 617l3-1 1 1-6 1h0l1 1h4 0-8 0c-2 1-3 1-4 2l-1 1v-1l1-3 9-1z" class="R"></path><path d="M406 531v-1c0-1 1-3 2-4s2-2 3-2c2-1 4-1 6 0h-1c-1 1-3 1-5 2h1c-1 1-3 1-3 2l-3 3z" class="Z"></path><path d="M404 392h1c2 5 4 10 8 14h1l3 3v1c1 1 1 2 2 2 0 1 1 1 1 1v1c-2-1-4-3-6-4-4-6-7-11-10-18z" class="T"></path><path d="M428 508c2 1 2 2 2 5l-1-2c-1 0-1 2-2 3-2 4-6 8-11 7-2 0-5-2-7-3v-1c2 1 4 2 6 2h1c3 1 5 0 7-1 3-2 5-7 5-10zm-24-116l-1-2c-2-4-3-10-6-12-3-3-7-4-10-7 2 1 4 1 5 2 2 1 4 3 5 4h1l1 1v-1c2 3 3 6 4 9 1 2 2 4 2 6h-1z" class="C"></path><path d="M328 326h1c1 1 2 2 2 3 1 1 1 3 0 4-1 2-2 4-4 4-1 1-2 1-4 0h-2c-1-1-2-2-2-4 1-1 2-2 2-3 1-1 3-1 4-1 1 1 2 1 2 2h0c-1 0-3-1-4-1-1 1-2 1-2 2s-1 2 0 3 2 1 3 1c1 1 3 0 4 0 1-1 2-2 2-4 1-2 0-4-2-6z" class="E"></path><path d="M390 365c-2-1-4-3-6-5-4-2-8-3-12-3h-3c-1 1-1 2-2 4v-1c-1 0-1 0-1-1l3-3h4c5 0 8 1 12 4 1 1 2 2 3 2l1 1h0l1 2z" class="K"></path><path d="M352 606c0-1 0-1 1-1v-1c3-2 7-4 10-7 3-2 5-5 8-7h0l-3 3v1l10-9h0l-10 10c-1 0-2 1-3 2 0 1-2 1-2 2l-11 7z" class="N"></path><path d="M363 599v1c-1 2-4 3-5 5h1l-2 1c-1 2-2 2-3 3l-1 1-1-2-3 1c0-1 2-2 3-3h0l11-7z" class="R"></path><path d="M352 608c2-1 4-2 5-2-1 2-2 2-3 3l-1 1-1-2z" class="b"></path><path d="M338 608h0c0 1 0 1 1 1h0 0c-5 4-13 5-20 7v-1-1h1c1 0 3-1 5-2l13-4z" class="H"></path><defs><linearGradient id="z" x1="326.746" y1="609.516" x2="339.439" y2="606.822" xlink:href="#B"><stop offset="0" stop-color="#5c5a5c"></stop><stop offset="1" stop-color="#767575"></stop></linearGradient></defs><path fill="url(#z)" d="M333 606h3c2 0 4-1 5 0l-3 2-13 4v-1h-1v-1h1c1 0 2-1 4-2 1 0 3-1 4-2z"></path><path d="M429 527h1c-1 3-2 4-1 7l-10 18-1-1c4-8 9-16 11-24z" class="E"></path><path d="M409 424h1l1 1c0 1 1 2 2 2h0 0v-1h1l2-1 1 1h0v-2h1c1 1 1 2 1 3s0 2-1 3-2 1-4 1-3-1-4-2-1-3-1-5z" class="V"></path><path d="M414 426l2-1 1 1c-1 2-1 2-2 2s-2 0-2-1h0 0v-1h1z"></path><path d="M335 349v-7c1-7 5-12 11-16v1h1 0c-3 2-8 5-8 9v2 1h1 2c-1 1-1 1-2 1-1 1-1 2-2 3h-2s0 1-1 1v5z" class="a"></path><path d="M409 424c1-2 3-4 5-5s5 0 7 0c1 1 3 3 3 4 1 3 1 6 0 8s-3 3-4 5c-1 0-2 1-2 2h0c-1 1-1 2-2 2-2 2-4 4-5 6l-1-1h-1c3-7 12-9 13-16 1-2 1-4 0-6-2-1-3-2-5-2-2-1-3 0-4 1s-2 2-2 3l-1-1h-1z" class="H"></path><path d="M312 329l1 1 1-1h1 0c0 1 0 2-1 3v5c2 2 4 4 7 5 3 2 9 4 12 7v1h-1c-5-4-10-5-14-8l-6-6c3 8 7 16 15 20v1c2 1 3 1 5 2 1 0 2 0 3 1v1l-2-1c-8-2-14-7-18-13-3-6-4-12-3-18z" class="U"></path><path d="M435 473c2-1 4-2 7-1 2 1 3 2 4 5l1 1v1c3 7-1 18-4 24-2 3-3 5-4 7-1 3-2 6-3 8-1 1-2 2-3 2 2-2 3-5 4-8 0-1 0-3 1-4h0c2-5 7-10 7-15 0-2-1-6-2-7-2-2-4 0-6 1-2-1-2-1-3-2v-1c1 1 2 1 3 1s2-1 3-1 2 0 2 1c2 1 3 2 4 5h0 0c1-3 1-7 0-11h0v-1c-1-2-2-4-4-5-1-1-3 0-5 0-1 1-1 0-2 0z" class="C"></path><path d="M346 326c7-4 15-5 22-3 8 2 13 6 18 11h0l-1 1h0c-1-1-2-2-3-2l-4-3c-2-1-4-2-5-2l-3-1-5-2h0-4c-5 0-9 0-14 2h0-1v-1z" class="K"></path><path d="M347 327c5-2 9-2 14-2h4 0l5 2 3 1c1 0 3 1 5 2l4 3c1 0 2 1 3 2v1h0 0c-1-1-2-2-4-1-9-6-18-10-29-7-5 1-10 5-12 10h-1l1 1h-1v-1-2c0-4 5-7 8-9z" class="P"></path><defs><linearGradient id="AA" x1="330.453" y1="337.611" x2="318.113" y2="322.69" xlink:href="#B"><stop offset="0" stop-color="#393839"></stop><stop offset="1" stop-color="#585757"></stop></linearGradient></defs><path fill="url(#AA)" d="M312 329c1-2 3-5 6-7 3-1 8-1 10 1 2 0 5 3 6 5 0 3 0 5-1 8-1 1-2 3-4 3-2 1-5 0-7-1l-1-1h2c2 1 3 1 4 0 2 0 3-2 4-4 1-1 1-3 0-4 0-1-1-2-2-3h-1v1l-2-2c-4 0-7 2-11 4h0-1l-1 1-1-1z"></path><path d="M315 329c2-2 2-4 5-5h3c1 1 2 1 3 1-4 0-7 2-11 4h0z" class="Q"></path><path d="M409 518l-1-1c0-7-1-16 4-22 2-3 7-6 11-6 3 0 7 0 10 2l-1 1h-8-1c-3 0-8 2-10 5-1 2-2 5-2 8l2 2v-3h2c0 1 0 3 1 4 2 1 6 1 8 1v1c-3 1-6 0-9-1l-5-2h0c0 3-2 7-1 10v1z" class="I"></path><path d="M415 504c0-2 1-3 2-4s4-1 5 0c2 0 4 3 5 5l1 3c0 3-2 8-5 10-2 1-4 2-7 1h-1c-2 0-4-1-6-2-1-3 1-7 1-10h0l5 2c3 1 6 2 9 1v-1c-2 0-6 0-8-1-1-1-1-3-1-4z"></path><path d="M413 504v3l-2-2c0-3 1-6 2-8 2-3 7-5 10-5h1 8l1-1c3 3 4 5 5 9v8c-1 1-1 3-1 4-1 3-2 6-4 8 0 2-1 3 0 5v1l-4 8c-1-3 0-4 1-7v-4l1-4-1 1-1-1 1-6c0-3 0-4-2-5l-1-3c-1-2-3-5-5-5-1-1-4-1-5 0s-2 2-2 4h-2z"></path><path d="M413 504c1-3 2-4 4-5s4-1 6 0c3 1 6 5 7 8 1 4 1 8 1 12l-1 1-1-1 1-6c0-3 0-4-2-5l-1-3c-1-2-3-5-5-5-1-1-4-1-5 0s-2 2-2 4h-2z" class="E"></path><path d="M433 491c3 3 4 5 5 9v8c-1 1-1 3-1 4-1 3-2 6-4 8 0 2-1 3 0 5v1l-4 8c-1-3 0-4 1-7v-4l1 1h0c1-1 1-3 1-4 2-6 5-13 4-19 0-4-2-6-4-9l1-1z" class="T"></path><defs><linearGradient id="AB" x1="376.59" y1="603.368" x2="374.745" y2="582.648" xlink:href="#B"><stop offset="0" stop-color="#000002"></stop><stop offset="1" stop-color="#3d3838"></stop></linearGradient></defs><path fill="url(#AB)" d="M398 556c2 1 4 2 5 4 1 4-2 8-4 11l-3 5c-1 1-2 1-2 2l-1 3v1h0-1c0 1-1 1-1 2-1 0-2 2-4 2l1 1c-1 1-1 2-2 2-1 1-3 2-4 3-2 2-4 3-6 5h0c-2 0-2 1-4 1-2 2-5 3-7 5-1 0-2 2-3 2h-3 0-1c1-2 4-3 5-5v-1c0-1 2-1 2-2 1-1 2-2 3-2l10-10c6-4 11-13 15-19 1-1 1-4 3-5l2-4v-1z"></path><path d="M382 592v-1h0-1c2-2 5-3 6-5l1 1c-1 1-1 2-2 2-1 1-3 2-4 3z" class="R"></path><path d="M368 595c3-1 4-3 6-4l1 1h0l-16 13h0-1c1-2 4-3 5-5v-1c0-1 2-1 2-2 1-1 2-2 3-2z" class="S"></path><path d="M398 557c2 1 3 2 3 4 1 3-2 7-3 10-6 9-15 14-24 20-2 1-3 3-6 4l10-10c6-4 11-13 15-19 1-1 1-4 3-5l2-4zm14-31h-1c2-1 4-1 5-2h1c2 1 5 4 6 6 1 5-2 13-4 17 0 2-2 4-2 5l1-1 1 1c-3 5-6 9-9 13-1 2-2 3-3 4l-2 2c-1 0-1 0-1-1h-1-1c-2 3-6 5-8 8 0-1 1-1 2-2l3-5c2-3 5-7 4-11-1-2-3-3-5-4h-1v-1s2-5 3-5l3-9c1-3 4-7 3-10l3-3c0-1 2-1 3-2z"></path><path d="M411 528h1 1v3l-1 1c-1-1-2-1-3-2l2-2z" class="C"></path><path d="M410 559l-1-4c-1-2-1-3-3-4-1 0-2 0-3 2l-1-1c0-1 0-2 1-2 0-1 1-1 1-1 2 0 3 0 4 1s2 3 2 5v3 1z" class="P"></path><path d="M412 526h-1c2-1 4-1 5-2h1c2 1 5 4 6 6 1 5-2 13-4 17 0 2-2 4-2 5l1-1 1 1c-3 5-6 9-9 13-1 2-2 3-3 4l-2 2c-1 0-1 0-1-1h-1-1c3-4 5-8 8-11v-1c3-4 4-8 4-12-1-2-1-4-3-5-1-1-1-1-3-1v1c-1 1-2 1-3 0 0-1 0-1 1-2s2-1 3 0c2 0 4 1 5 3 2 4 0 9 0 13 3-7 7-13 7-21 1-2 0-4-1-6-3-2-5-2-8-2z" class="K"></path><path d="M404 570c1-1 2-3 3-5 2-2 5-5 5-8l1-1v1c0 2-3 6-4 8h1c-1 2-2 3-3 4l-2 2c-1 0-1 0-1-1z" class="Y"></path><path d="M419 455h2 0c2 1 5 0 8 1v1h0l8 3h2c1-1 1-1 2-1 0 1 1 1 1 2-1 1-1 1-1 2 7 6 11 14 11 24v2c-1 12-6 24-11 35-3 7-5 13-9 18l-6 9c-1 1-3 3-3 5l-7 7h-2l-7 7v-1c1-1 2-2 3-4 3-4 6-8 9-13l10-18 4-8v-1c-1-2 0-3 0-5 1 0 2-1 3-2 1-2 2-5 3-8 1-2 2-4 4-7 3-6 7-17 4-24v-1l-1-1c-1-3-2-4-4-5-3-1-5 0-7 1h0c-1 3-1 6-3 9s-4 4-7 5h0c-3 0-6 0-9-1s-6-4-8-6v-1c-1-2-1-3-1-5 0-3 1-7 3-9 0-2 2-4 4-5 1-1 2-2 4-3 1 0 1 0 2-1l-1-1z"></path><path d="M433 520c1 0 2-1 3-2l-3 8v-1c-1-2 0-3 0-5z" class="U"></path><path d="M419 455h2 0c2 1 5 0 8 1v1h0l8 3h2c1-1 1-1 2-1 0 1 1 1 1 2-1 1-1 1-1 2 7 6 11 14 11 24v2c-1 12-6 24-11 35-3 7-5 13-9 18l-6 9c-1 1-3 3-3 5l-7 7h-2c8-9 16-18 21-29l6-12c4-10 8-20 9-30 2-9-1-18-7-25-2-4-8-7-13-8s-10 0-14 3c-3 2-5 6-6 9 0 3 0 6 2 8s5 4 8 4 5 0 7-2c3-2 4-5 4-8 0-2 0-3-2-5-1-2-3-3-6-3-2 0-4 1-5 2s-2 2-2 3 1 2 1 2c1 1 2 1 3 1h1v-1l1-1s1-1 2 0c1 0 2 0 3 1l-1 1c-1-1-1-1-2-1s-2 0-3 1h1 1 1v1h-2c-3 0-5 0-6-2-1-1-1-2 0-3 0-2 3-4 5-5s4 0 6-1c2 1 5 4 6 7 1 1 1 2 1 3h1c-1 3-1 6-3 9s-4 4-7 5h0c-3 0-6 0-9-1s-6-4-8-6v-1c-1-2-1-3-1-5 0-3 1-7 3-9 0-2 2-4 4-5 1-1 2-2 4-3 1 0 1 0 2-1l-1-1z" class="F"></path><path d="M441 459c0 1 1 1 1 2-1 1-1 1-1 2-1 0-3-2-4-3h2c1-1 1-1 2-1z" class="b"></path><path d="M418 457l1 1c-2 2-4 3-6 5 0-1 1-2 1-3 1-1 2-2 4-3z" class="P"></path><path d="M414 460c0 1-1 2-1 3-2 2-3 4-4 6 0-1 0-2 1-3 0-1 1-1 1-2l-1 1h0c0-2 2-4 4-5z" class="Y"></path><path d="M419 455h2 0c2 1 5 0 8 1v1h0c-4 0-7 0-10 1l-1-1c1 0 1 0 2-1l-1-1z" class="b"></path><path d="M421 464c2-1 4 0 6-1 2 1 5 4 6 7 1 1 1 2 1 3h1c-1 3-1 6-3 9s-4 4-7 5h0c-3 0-6 0-9-1s-6-4-8-6v-1c-1-2-1-3-1-5 0-3 1-7 3-9h0l1-1c0 1-1 1-1 2-1 1-1 2-1 3v3c0 3 0 6 3 8 2 3 6 5 9 5s5-1 7-3c3-2 4-5 4-8s-1-5-3-7c-3-3-5-3-8-3z" class="I"></path><path d="M340 339l-1-1h1c2-5 7-9 12-10 11-3 20 1 29 7 2-1 3 0 4 1h0 0v-1h0l1-1 4 4c5 7 9 13 12 22-1 1-1 2-1 3 0 2 1 4 1 7 1 2 1 5 2 7v4c1 3 1 6 2 10 2 5 4 10 8 15h-1c-4-4-6-9-8-14 0-2-1-4-2-6-1-3-2-6-4-9v1l-1-1-4-7-4-5-1-2h0c-6-7-13-10-20-15h-1c-1 4-3 8-7 11-5 2-11 2-16 1-3-1-5-3-7-6-1-1-2-3-3-5v-5c1 0 1-1 1-1h2c1-1 1-2 2-3 1 0 1 0 2-1h-2z"></path><path d="M352 346h1 1l-2 2-1 1v-1l-1-2h2z" class="K"></path><path d="M396 369l3 8v1l-1-1-4-7h1l1 1v-2z" class="a"></path><path d="M381 335c2-1 3 0 4 1h0 0v-1h0c1 1 3 2 4 4 1 1 3 2 3 4v1c-3-3-7-6-11-9z" class="C"></path><path d="M335 349v-5c1 0 1-1 1-1h2v6c0 1 1 5 1 5h-1c-1-1-2-3-3-5zm50-14l1-1 4 4c5 7 9 13 12 22-1 1-1 2-1 3-2-7-5-13-9-19v-1c0-2-2-3-3-4-1-2-3-3-4-4z" class="E"></path><path d="M343 340v2 1s0 1 1 1c1 1 1 2 1 3s2 3 3 3c1 1 3 1 4 1s2-1 3-2v-2c0-1 1-2 0-3l-1-1c-2 0-3 0-4 1 0-1 0-1 1-2h3c2 1 3 2 3 4 1 2 2 5 1 8l-3 3 1-1c1-2 1-3 1-5v-1-3h-1c0 2 0 3-2 4s-3 2-5 1h-2 0-1c-2-2-3-3-4-6 0-2 0-4 1-6z" class="C"></path><path d="M342 346c1 1 2 1 2 1s1 2 1 3c1 0 1 1 1 2-2-2-3-3-4-6z" class="U"></path><path d="M361 337c1-1 1-1 2-1 3 2 5 6 5 10v2c-1 4-3 8-7 11-5 2-11 2-16 1-3-1-5-3-7-6h1c1 1 1 2 2 2 3 3 8 4 12 4 4-1 8-2 10-5 3-3 4-6 3-9 0-4-2-7-5-9z" class="O"></path><path d="M343 340c3-4 6-7 11-7 7-1 16 5 21 9 4 3 7 7 10 10l-1 1c-6-7-12-13-20-17h-1c-1 0-1 0-2 1-4-2-7-3-11-1-3 1-5 2-6 5l-1 2v-1-2z" class="H"></path><path d="M363 336h1c8 4 14 10 20 17l1-1c3 5 6 9 9 14 0 1 1 2 2 3v2l-1-1h-1l-4-5-1-2h0c-6-7-13-10-20-15h-1v-2c0-4-2-8-5-10z"></path><path d="M369 348l1-1c4 3 8 5 12 8 3 2 5 5 9 7-1-1-7-8-7-9l1-1c3 5 6 9 9 14 0 1 1 2 2 3v2l-1-1h-1l-4-5-1-2h0c-6-7-13-10-20-15z" class="E"></path><path d="M443 429l1-1c-1-1-2-2-2-3-1-2-1-4 0-5 1-2 2-3 4-4 5-1 11 2 15 4s6 5 9 8c2 4 4 9 5 13s2 12 1 15v7h-1 0c0 2 0 4-1 7-1 1-2 3-3 4 0 1-1 2-1 2-3 4-7 7-11 10l-6 3v-1l-1 1v-2c0-10-4-18-11-24 0-1 0-1 1-2 0-1-1-1-1-2-1 0-1 0-2 1h-2c-3-1-5-2-8-3h0v-1c-3-1-6 0-8-1h0 0-3c-3 0-5-1-7-2-1 0-1-1-1-1-1-2 0-4 1-6s3-4 5-6c1 0 1-1 2-2h0c0-1 1-2 2-2 2 0 5-2 7-3 4-2 9-2 14-3h2v-1z"></path><path d="M425 450h2c1 0 1-1 2 0h0l-1 2c-2 0-3-1-4-1h0l1-1z" class="K"></path><path d="M418 455c5-1 10-1 15 1 2 0 3 1 4 2 1 0 2 1 2 2h-2c-3-1-5-2-8-3h0v-1c-3-1-6 0-8-1h0 0-3z" class="D"></path><path d="M461 443c2 2 4 3 5 6 0 4-1 9-4 12-1 2-4 5-6 5h-4v-1h1c4-1 7-3 9-6s3-6 2-10c-1-3-2-4-4-5l1-1z" class="G"></path><path d="M453 488c2-5 8-9 9-15 1-2 1-5 0-7h0v-1c1 1 1 2 2 3 1 4-1 7-3 11 1-1 3-3 4-5v1c-2 3-5 6-7 9h1v2l-6 3v-1z" class="E"></path><path d="M420 436c2 0 5-2 7-3 4-2 9-2 14-3 1 0 2 1 4 1-2 0-3 1-4 1-5 1-10 2-15 4-3 1-7 2-10 4 1 0 1-1 2-2h0c0-1 1-2 2-2z" class="B"></path><path d="M441 433c2 0 5-1 7 0h4c7 3 11 6 14 12 4 8 4 18 1 26 0 1-1 3-2 4v-1c1-4 2-7 2-12 1-7 0-16-5-21-4-5-9-6-15-7-4 0-9 1-12 4-2 1-3 3-3 5s1 5 2 6h2-1c-2 0-3-1-3-2-1-2-1-4-1-6 2-4 6-5 9-7-5 1-10 3-14 7-1 1-3 3-3 5s1 3 2 4l-1 1c-1-2-2-3-2-5 0-1 1-3 2-4 4-5 10-9 17-9z" class="F"></path><path d="M452 433c3 0 8 3 10 4 3 2 5 4 6 6h1c3 3 4 8 5 13h1c-1 2-1 4 0 7 0 2 0 4-1 7-1 1-2 3-3 4 0 1-1 2-1 2-3 4-7 7-11 10v-2h-1c2-3 5-6 7-9 1-1 2-3 2-4 3-8 3-18-1-26-3-6-7-9-14-12z"></path><path d="M469 443c3 3 4 8 5 13v2h-1c-1-5-2-10-5-15h1z" class="U"></path><defs><linearGradient id="AC" x1="474.566" y1="458.788" x2="472.083" y2="471.142" xlink:href="#B"><stop offset="0" stop-color="#504f50"></stop><stop offset="1" stop-color="#686869"></stop></linearGradient></defs><path fill="url(#AC)" d="M474 456h1c-1 2-1 4 0 7 0 2 0 4-1 7-1 1-2 3-3 4 0 1-1 2-1 2-3 4-7 7-11 10v-2c2-2 4-3 7-5 6-6 7-12 7-21h1v-2z"></path><path d="M441 459c-1-1-1-2-1-3-1-3 0-6 2-8 3-4 7-7 11-7 3-1 6 0 8 2l-1 1c2 1 3 2 4 5 1 4 0 7-2 10s-5 5-9 6h-1v1h4-5c-4 0-7-2-9-5 0-1-1-1-1-2z"></path><path d="M441 459c-1-1-1-2-1-3-1-3 0-6 2-8 3-4 7-7 11-7 3-1 6 0 8 2l-1 1c-3-2-6-2-10-1-2 1-4 3-6 5 3 0 6-1 8 2 1 1 1 2 1 3l-3 3h-2v-1c1 0 3-1 4-2-1-2-1-3-2-3-2-2-4-2-6-1-2 2-3 4-3 7 1 3 2 5 5 6 2 2 5 2 7 3h-1v1h4-5c-4 0-7-2-9-5 0-1-1-1-1-2z" class="O"></path><path d="M443 429l1-1c-1-1-2-2-2-3-1-2-1-4 0-5 1-2 2-3 4-4 5-1 11 2 15 4s6 5 9 8c2 4 4 9 5 13s2 12 1 15v7h-1 0c-1-3-1-5 0-7h-1c-1-5-2-10-5-13h-1c-1-2-3-4-6-6-2-1-7-4-10-4h-4c-2-1-5 0-7 0v-1c1 0 2-1 4-1-2 0-3-1-4-1h2v-1z"></path><path d="M451 417c1 0 1 1 2 1v2h-1-1-1v-1-1l1-1z" class="D"></path><path d="M472 439c-2-3-3-5-5-7-5-4-11-2-16-2 4-1 9-3 12-2 5 2 8 6 9 11h0z" class="P"></path><path d="M472 439l2 3 1-1c1 4 2 12 1 15v7h-1 0c-1-3-1-5 0-7h0 0v-4c-1-5-1-9-3-13h0z" class="D"></path><path d="M443 430c9 0 17 3 24 10l2 3h-1c-1-2-3-4-6-6-2-1-7-4-10-4h-4c-2-1-5 0-7 0v-1c1 0 2-1 4-1-2 0-3-1-4-1h2z" class="E"></path><path d="M372 321h0c2-1 4 0 6 0h14c4-1 8-1 13-2 4 0 8 1 11 1 4 0 8-1 12-1h15 10 5c1 0 1 1 2 1-5 1-8 3-10 6-1 1-1 2-1 3l1 1c-1 1-1 2-1 3s1 2 2 3c0 1 1 1 1 2v2h0l-2-2c2 3 4 6 6 8 1 1 4 3 5 4h0c3 2 5 3 8 4l2 2h1l1 1 4 3v2s1 3 2 3h1 0 1c0 1 1 3 2 4 2 1 2 5 2 7 0 3-1 9-3 11l-1 3v3-1c-4 2-6 5-11 4h-1-1c-2 0-4-1-5-2 1 1 1 3 2 5 1 0 1 1 1 1 1 3 3 5 4 8s2 7 2 10c1 1 1 2 1 3 1 3 2 8 3 12 0 1 1 2 1 3h0c-1-1-3-9-3-11-1 1-1 1-2 1l1 3h-1v-1h-2c-3-3-5-6-9-8s-10-5-15-4c-2 1-3 2-4 4-1 1-1 3 0 5 0 1 1 2 2 3l-1 1c-1-2-3-4-3-6 0-3 1-4 3-6-1 0-3 1-4 1-2 0-7 1-9 0h0-1c-3-1-7-2-9-4v-1s-1 0-1-1c-1 0-1-1-2-2v-1l-3-3c-4-5-6-10-8-15-1-4-1-7-2-10v-4c-1-2-1-5-2-7 0-3-1-5-1-7 0-1 0-2 1-3-3-9-7-15-12-22 0-1 0-1 1-2l-1-2v-5l1-1c0-1 0-1 1-2h0c1-2 3-4 4-4 1-1 2-1 2-1h-6c-2 1-3 1-5 0h-3c-1 1-2 1-4 0h-8 0z"></path><path d="M414 356c0-1 0-1 1-2h1c0 1-1 2-1 2h-1z" class="N"></path><path d="M416 354c1 0 1 1 2 0l1-1v1l-1 2c-1 1-1 1-2 1s-1 0-2-1h1s1-1 1-2z" class="S"></path><path d="M448 400h1 0c1 5 0 8-3 12l-2 1c-2 2-4 3-7 4l-1-1h0c4-1 8-3 10-6s2-6 2-10z" class="B"></path><path d="M461 397c1 1 1 2 1 3-1-1-2-1-3-2l-2 2c-1 1-1 3 0 5 1 3 4 7 6 10h0c-3-3-9-9-8-14 0-1 0-1 1-2 1-2 3-2 5-2z" class="C"></path><path d="M434 392c3-1 4-1 7 0 2 1 3 1 5 2h1c1 2 2 4 2 6h0-1c-1-1-2-3-3-4-3-3-6-3-9-3-1-1-1-1-2-1z" class="T"></path><path d="M463 415c3 3 8 8 9 13h-2c-3-3-5-6-9-8 1-1 1-1 2-1 1 1 3 2 4 3l-4-7h0z" class="U"></path><path d="M404 381l1-1h0 1c1 9 3 22 11 28v1l-3-3c-4-5-6-10-8-15-1-4-1-7-2-10z" class="B"></path><path d="M417 408c5 4 9 8 16 8h3l1 1c-2 1-5 1-7 1h-1c-3-1-7-2-9-4v-1s-1 0-1-1c-1 0-1-1-2-2v-1-1z" class="F"></path><path d="M417 410c2 1 3 2 5 4 2 1 5 2 7 4-3-1-7-2-9-4v-1s-1 0-1-1c-1 0-1-1-2-2z" class="a"></path><path d="M434 392c1 0 1 0 2 1-3 1-5 2-6 4s-1 4 0 5c0 2 1 3 3 4 1 1 3 1 5 0 1 0 2-1 3-2v-3l-1-1c-1-1-2-1-3 0s-1 2 0 3h-1c-1-1-1-2-1-3 1-1 2-2 3-2s2 1 3 2 1 2 1 3 0 2-1 2c-1 2-3 3-5 3-1 0-3 0-4-1l-1-1-3-3c0-2-1-4 0-5 1-3 3-5 6-6z" class="H"></path><path d="M428 379h9c2 1 5 2 7 3-6-2-12-3-19-1-4 2-7 6-9 10-1 6 0 10 1 16h0c-1-1-1-2-2-3-1-5-1-12 2-17 2-4 7-7 11-8z" class="F"></path><defs><linearGradient id="AD" x1="442.997" y1="412.851" x2="459.849" y2="421.336" xlink:href="#B"><stop offset="0" stop-color="#282829"></stop><stop offset="1" stop-color="#414142"></stop></linearGradient></defs><path fill="url(#AD)" d="M437 417c3-1 5-2 7-4 1 1 1 1 2 1 5-2 10 1 14 3 1 0 3 1 3 2-1 0-1 0-2 1-4-2-10-5-15-4-2 1-3 2-4 4-1 1-1 3 0 5 0 1 1 2 2 3l-1 1c-1-2-3-4-3-6 0-3 1-4 3-6-1 0-3 1-4 1-2 0-7 1-9 0h0c2 0 5 0 7-1z"></path><path d="M431 331c0-1 0-1 1-2s2-1 4-1c4-1 7 2 11 5-2-4-5-6-9-9-3-2-8-3-11-2-2 0-4 1-5 3v3c1 3 7 6 10 7h0c-3 0-7-1-9-4-1-1-2-3-2-5 0-1 1-3 2-4 3-2 7-2 10-1 5 1 11 5 14 9l1 1s0-1 1-2l1 1c-1 1-1 2-1 3s1 2 2 3c0 1 1 1 1 2v2h0l-2-2s-2-2-2-3c-2-2-5-4-7-6-3-1-5-1-7 0-1 1-2 1-2 3v-1h-1z" class="M"></path><path d="M441 405v2c-2 2-4 4-7 4-2 0-4-1-6-3s-3-4-3-7 1-6 3-8c3-2 6-3 10-3 3 0 6 1 9 4h-1c-2-1-3-1-5-2-3-1-4-1-7 0s-5 3-6 6c-1 1 0 3 0 5l3 3 1 1c1 1 3 1 4 1 2 0 4-1 5-3z" class="D"></path><path d="M463 415c-2-3-5-7-6-10-1-2-1-4 0-5l2-2c1 1 2 1 3 2 0 1 1 2 2 3l3 6c3 6 4 11 5 17l1 3h-1v-1c-1-5-6-10-9-13z"></path><path d="M447 383c-2-2-5-3-7-4-6-2-11-1-18-1-4 0-8-3-12-1h0 0l1-1c2-2 5 0 7 0-2-3-3-6-3-9 1-2 2-4 4-6 4-2 9-2 13-2-1-1-5-3-6-4-3-3-4-5-4-9 0-2 0-5 1-7 2 1 4 2 5 4 1 0 6-1 7-1 5 0 11 3 15 5v1h0c-5-3-14-6-19-4l-6 3 3-3c-1-2-3-3-5-4 0 4-1 8 2 11 3 5 9 7 13 10a46.21 46.21 0 0 1 17 17l-1 1c-4-6-8-11-15-15-1-2-3-3-5-3-5-1-10-1-14 1-1 1-3 3-3 5-1 2 0 5 1 7l2 2c10 1 19-1 27 5v2z" class="F"></path><path d="M432 332c0-2 1-2 2-3 2-1 4-1 7 0 2 2 5 4 7 6 0 1 2 3 2 3 2 3 4 6 6 8 1 1 4 3 5 4h0l-1 1 1 1-6-3-4-2c-4-3-9-6-14-9-1-1-3-2-5-4-1-1-1-1-1-3h1v1z"></path><path d="M431 331h1v1c1 1 3 3 5 4 5 4 12 9 18 12l6 2h0l-1 1 1 1-6-3-4-2c-4-3-9-6-14-9-1-1-3-2-5-4-1-1-1-1-1-3z" class="H"></path><path d="M447 381c-8-6-17-4-27-5l-2-2c-1-2-2-5-1-7 0-2 2-4 3-5 4-2 9-2 14-1 2 0 4 1 5 3 7 4 11 9 15 15l1-1h0c4 7 5 14 14 16h2v1l3 1 2-2c2-1 3-2 5-4v3-1c-4 2-6 5-11 4h-1-1c-2 0-4-1-5-2 1 1 1 3 2 5 1 0 1 1 1 1 1 3 3 5 4 8s2 7 2 10c1 1 1 2 1 3 1 3 2 8 3 12 0 1 1 2 1 3h0c-1-1-3-9-3-11-1 1-1 1-2 1-1-6-2-11-5-17l-3-6c-1-1-2-2-2-3s0-2-1-3l-1-1-10-10-3-3v-2z"></path><path d="M454 379l1-1h0c4 7 5 14 14 16h2v1l3 1c-2 0-4 0-6-1-8-2-11-10-14-16z" class="B"></path><path d="M447 381c0-1-11-10-12-11s-2-1-3-1c-2-1-5 0-7 1s-3 2-4 3h0c2-3 4-5 7-6 3 0 5 0 7 2 4 2 8 6 11 9 5 4 11 9 15 15 2 2 3 5 4 7 2 4 4 8 5 12 2 4 2 9 4 13-1 1-1 1-2 1-1-6-2-11-5-17l-3-6c-1-1-2-2-2-3s0-2-1-3l-1-1-10-10-3-3v-2z" class="E"></path><path d="M450 386h2c1 0 5 4 6 5 2 2 2 2 2 5l-10-10z" class="C"></path><path d="M390 334c0-1 0-4 1-5 1-3 4-6 8-7 4-2 10-1 14 1 3 2 6 5 7 9s1 8-1 11c-4 7-10 14-12 21-1 4-1 8-2 12 1 1 1 3 1 4h-1 0l-1 1v-4c-1-2-1-5-2-7 0-3-1-5-1-7 0-1 0-2 1-3-3-9-7-15-12-22 0-1 0-1 1-2l-1-2z"></path><path d="M405 331c-2 2-4 4-8 4-1 0-1-1-2-2h0l2 1h1c2-1 4-3 6-4h2v1h-1z" class="D"></path><path d="M394 340l2 2h0c1 1 1 1 2 1h2s0 1 1 1c1-1 3 0 4 0h0 0v1c-2 1-3 1-5 1-1-1-3-1-4-2l-2-3v-1z" class="P"></path><path d="M406 330c2 1 3 2 3 4 1 2 0 4 0 6v-1c2 0 3 0 4 1 1 0 3 2 3 3v1c-1-1-1 0-1-1l-3-3c-3 2-5 3-7 4h0 0c-1 0-3-1-4 0-1 0-1-1-1-1h-2c-1 0-1 0-2-1h1 3c3 0 5 0 6-2 2-1 2-2 2-4 0-3-1-3-3-5h1v-1z" class="C"></path><path d="M390 334c0-1 0-4 1-5 1-3 4-6 8-7 4-2 10-1 14 1 3 2 6 5 7 9s1 8-1 11c-4 7-10 14-12 21-1 4-1 8-2 12 1 1 1 3 1 4h-1 0l-1 1v-4c-1-2-1-5-2-7 0-3-1-5-1-7 0-1 0-2 1-3l3 11c0-3 0-7 1-10 2-5 6-9 9-14 2-3 4-6 4-11 1-3 0-6-3-8-3-4-7-6-12-6-3 0-6 1-9 3-2 3-3 5-4 8 0 3 1 5 3 7h0v1c-2-1-3-3-3-5l-1-2z" class="M"></path><path d="M404 377v-4c1 1 1 2 1 3 1 1 1 3 1 4h-1 0l-1 1v-4z" class="F"></path><path d="M455 378a46.21 46.21 0 0 0-17-17c-4-3-10-5-13-10-3-3-2-7-2-11 2 1 4 2 5 4l-3 3 6-3c5-2 14 1 19 4h0v-1h1l4 2 6 3-1-1 1-1c3 2 5 3 8 4l2 2h1l1 1 4 3v2s1 3 2 3h1 0 1c0 1 1 3 2 4 2 1 2 5 2 7 0 3-1 9-3 11l-1 3c-2 2-3 3-5 4l-2 2-3-1v-1h-2c-9-2-10-9-14-16h0z"></path><path d="M471 394l3-1 2 1-2 2-3-1v-1z" class="B"></path><path d="M440 348v1h1c-2 2-4 4-5 6v-3c1-2 2-3 4-4z" class="C"></path><path d="M468 385c-2 1-3 1-5 1-2-1-3-1-4-2 0-1 0-1 1-1 0-1 1-1 2-1h0l-1 1v1l2 1c1 0 2 0 4-1h0l1 1z" class="P"></path><path d="M481 387h1l-1 3c-2 2-3 3-5 4l-2-1c3-2 5-3 7-6z" class="X"></path><path d="M440 348c4-1 6 1 10 3 1 1 3 3 3 5v3l-1 1-1-1c1-1 1-2 1-3-1-2-2-4-4-5s-4-2-7-2h-1v-1z" class="P"></path><path d="M461 352l-1-1 1-1c3 2 5 3 8 4l2 2h1l1 1 4 3v2c-5-5-11-7-16-10z" class="B"></path><path d="M451 347l4 2-1 1c0 1 2 2 3 2 1 1 2 3 3 4 1 2 1 4 1 6-2 2-4 3-6 4 1-2 3-3 4-5s0-3-1-5a30.44 30.44 0 0 0-8-8h0v-1h1z" class="D"></path><path d="M467 384c4-3 7-9 8-14 0-5-1-8-4-12l-1-1c3 2 5 4 6 7 1 6-1 13-4 17-1 2-2 3-4 4l-1-1z" class="C"></path><defs><linearGradient id="AE" x1="483.907" y1="372.785" x2="480.374" y2="373.965" xlink:href="#B"><stop offset="0" stop-color="#6e6e6e"></stop><stop offset="1" stop-color="#838283"></stop></linearGradient></defs><path fill="url(#AE)" d="M481 365c0 1 1 3 2 4 2 1 2 5 2 7 0 3-1 9-3 11h-1c3-8 1-15-2-22h1 0 1z"></path><path d="M451 359l1 1 1-1c-1 1-1 2-1 3 0 2 1 4 2 5 3 3 6 4 9 4s5-1 6-2l3-3 1 1h0l1 1c0 1 0 1-1 1-1 2-4 4-5 4-4 1-9 1-12-1s-5-4-5-6c-1-3-1-5 0-7z" class="D"></path><path d="M127 256l10-10 1 2-11 10 1 1c2-2 5-4 8-5 13-5 27-7 41-5 2 0 4 0 6 1 1 1 2 2 4 2 6 3 10 6 15 11 1 1 3 3 4 5h1l10 19c0 1 0 2 1 3l2 4h-1-2l3 6c0 1 1 2 1 3 1 4 3 7 5 11 2 5 7 11 11 16h2l1 1c1 1 5 4 5 6v1c0 1 2 3 3 4 1 4 3 8 5 11 1 3 3 5 5 7l4 4c4 4 7 8 9 12 1 2 2 4 2 6 1 1 1 2 2 4 0 1 1 2 1 4l-1 1v1 2l-1-1c-1 2 0 5-1 7 0 2 0 5-1 8-1 5-6 9-9 12-7 6-16 7-24 10l-18 8c-1 0-2 1-3 1l-10 6-2-1v-1h0c-2 1-3 4-5 5h0c-1 0-1 1-2 1v3c-2 1-4 3-6 4-5 5-10 10-17 13 0 0 0 1-1 1l-1 1h-1c-1 1-2 1-2 1l-1 1h0l1 1-3 1c-1 0-3 0-4-1h-1l-12-2-2-1c0 1-1 1-2 1l-2-1-1 1 1 1-1 1h1c-2 0-3-1-5 0-3 1-4 2-5 5-2 2-2 5-1 8v4c-2-4-4-9-5-13-2-3-3-5-4-8-2-4-4-8-7-12v-1c-1-3-2-6-5-8-3-6-5-13-7-20l-9-38h0c-1-3-1-6-2-9-1-9-2-18-2-28 1-2 0-4 0-7 0-5 0-11 1-17 0-1 0-2 1-4 0-2 0-5 1-7l2-13 1-4c4-12 10-23 18-33l1-1h0v-1l1-1-1-1c0 1-1 1-1 2h-1l2-4 3-3v1c0-1 1-1 2-2 2 0 3-1 4-3z"></path><path d="M96 319v3h0l2-7 1 1c0 3-2 7-1 11h0v2l-1 2h-2v-5c0-2 0-5 1-7z" class="U"></path><path d="M107 419l9 21-2-2h0-1 0c-3-5-5-11-7-16h0 1v-2-1zm69-157c7 5 11 10 13 19h-2v1l-3-9v-1c-2-5-7-7-11-9l3-1z" class="V"></path><path d="M99 397v-5c0-2-1-3 0-5v1 2h0v1 1c0 1 1 2 2 3h0l3 12 2 9c0 1 1 2 1 3v1 2h-1 0c-1-1-2-5-2-6-2-7-4-13-5-19z" class="G"></path><path d="M187 287c1-1 1-2 1-4h1c1 9-3 19-9 26-3 4-7 8-11 10 1 0 1-1 1-2-1 1-1 1-2 1l3-2c9-7 15-17 16-29z" class="c"></path><path d="M199 288c-1-8-4-16-10-22-4-4-9-7-14-9 0-1 1-2 2-3h1c0 1-1 2-2 2l1 1c2 1 5 2 7 4 4 3 9 7 12 11 3 5 5 10 7 14 3 5 5 9 8 12 0 1-1 1-1 1-3-3-5-6-7-10-1-3-2-6-4-9l2 8h-2z" class="O"></path><defs><linearGradient id="AF" x1="152.317" y1="267.144" x2="158.207" y2="253.645" xlink:href="#B"><stop offset="0" stop-color="#959593"></stop><stop offset="1" stop-color="#bdbdc0"></stop></linearGradient></defs><path fill="url(#AF)" d="M136 259c8-5 18-5 28-3 3 1 7 2 10 4l2 2-3 1-4-2c-11-4-21-5-32 0v-1h-1v-1z"></path><path d="M168 318c1 0 1 0 2-1 0 1 0 2-1 2-5 3-10 6-16 7-5 1-11 0-17 0-15 0-29 7-34 22-1-1-1-1-2-1 5-14 18-23 33-24 7 0 15 1 22 0 5-1 9-3 13-5z" class="d"></path><defs><linearGradient id="AG" x1="196.263" y1="283.262" x2="187.092" y2="280.829" xlink:href="#B"><stop offset="0" stop-color="#909393"></stop><stop offset="1" stop-color="#b9b6b8"></stop></linearGradient></defs><path fill="url(#AG)" d="M175 260c3 0 9 5 11 7 7 8 10 18 9 28 0 12-5 21-13 28-10 9-22 15-36 14 1-1 5-1 7-1 10-2 19-6 27-12 3-3 6-6 9-10 3-5 4-12 5-18v-1l-1-1c0 7-2 12-5 17-7 10-18 19-30 22-2 0-6 1-8 0-1-1-1-2-2-3l3-3h0l-2 3 3 3h4c8-2 14-5 19-9 7-6 13-13 16-22 1-3 1-7 1-10 1-10-3-20-10-27-2-1-5-3-7-5h0z"></path><path d="M127 256l10-10 1 2-11 10 1 1h-1l-1 2h2l-6 6c-8 9-12 19-17 29-2 6-5 13-6 20l-1-1-2 7h0v-3l2-13 1-4c4-12 10-23 18-33l1-1h0v-1l1-1-1-1c0 1-1 1-1 2h-1l2-4 3-3v1c0-1 1-1 2-2 2 0 3-1 4-3z" class="L"></path><path d="M127 256l10-10 1 2-11 10-3 3c-3 1-4 3-6 4l3-4c0-1 1-1 2-2 2 0 3-1 4-3z" class="G"></path><path d="M117 269c2-2 4-3 6-5-1 1-3 3-4 5-3 3-6 8-8 12-4 7-8 15-10 22l-2 8c0 2-1 3-1 4l-2 7h0v-3l2-13 1-4c4-12 10-23 18-33z"></path><defs><linearGradient id="AH" x1="112.029" y1="390.182" x2="98.794" y2="366.366" xlink:href="#B"><stop offset="0" stop-color="#8a8c8a"></stop><stop offset="1" stop-color="#b3b1b3"></stop></linearGradient></defs><path fill="url(#AH)" d="M105 405c-2-4-3-10-4-14-2-11 1-22 6-31 2-4 5-7 7-11 2-3 3-6 5-9 3-4 7-6 12-7s7 0 11 3l-1-2-1-1c-1-2-3-2-4-2-8-2-18 5-24 9-1 2-3 3-4 5-2 2-3 5-4 8-2 6-4 13-5 20h0c1-11 3-25 11-32 2-2 5-4 8-6 5-3 10-6 17-6 2 0 4 1 5 3 1 0 1 1 2 1v5l-1 1-1-1c0-1 0-2-1-3s-2-1-3-1c-4-1-9 0-12 3-2 1-3 2-4 4-2 2-3 5-4 8l-5 7c0 1-1 2-1 3h0c0 1-1 1-1 2l1 1c-7 10-7 19-5 30l2 7-1 1c-1-1-1-3-2-3 1 3 1 5 2 8h-1z"></path><path d="M128 259c2-2 5-4 8-5 13-5 27-7 41-5 2 0 4 0 6 1 1 1 2 2 4 2 6 3 10 6 15 11 1 1 3 3 4 5h1l10 19c0 1 0 2 1 3l2 4h-1-2c0-2-1-4-2-5-1-2-1-3-2-4-2-5-5-11-8-15-7-10-18-17-31-19-15-2-33 1-46 10h-2l1-2h1z" class="W"></path><defs><linearGradient id="AI" x1="90.386" y1="363.376" x2="102.454" y2="365.997" xlink:href="#B"><stop offset="0" stop-color="#898989"></stop><stop offset="1" stop-color="#b5b4b5"></stop></linearGradient></defs><path fill="url(#AI)" d="M94 330c0-1 0-2 1-4v5h2l1-2c0 1 0 1 1 1l-2 11v11c0 1 0 3-1 4v2h1l2-11h0 1 0c1 0 1 0 2 1-3 9-4 18-3 28v9c0 3 1 6 2 10h0c-1-1-2-2-2-3v-1-1h0v-2-1c-1 2 0 3 0 5v5l-1-6h-1 0c-1-3-1-6-2-9-1-9-2-18-2-28 1-2 0-4 0-7 0-5 0-11 1-17z"></path><path d="M94 330c0-1 0-2 1-4v5h2c-1 2-1 3-1 5v6h-1v1l-1-13z" class="K"></path><path d="M98 329c0 1 0 1 1 1l-2 11v11c0 1 0 3-1 4-1-4 0-8 0-13h-1 0v-1h1v-6c0-2 0-3 1-5l1-2z" class="H"></path><path d="M97 391h1l1 6c1 6 3 12 5 19 0 1 1 5 2 6 2 5 4 11 7 16h0 1 0l2 2c2 3 4 7 7 10 3 4 7 7 10 10l1 1 3 2 2 2c1 0 2 1 3 1l3 2 6 2h0l1 1s-1 0-1 1l-2-1c0 1-1 1-2 1l-2-1-1 1 1 1-1 1h1c-2 0-3-1-5 0-3 1-4 2-5 5-2 2-2 5-1 8v4c-2-4-4-9-5-13-2-3-3-5-4-8-2-4-4-8-7-12v-1c-1-3-2-6-5-8-3-6-5-13-7-20l-9-38z"></path><path d="M137 470c-2-1-5-2-6-4v-1c-2 1-2 3-4 4l-1-1v-2c1-1 2-2 4-2 1 1 3 2 4 3 1 0 2 1 3 2v1z" class="I"></path><path d="M118 447c4 5 9 11 14 15l1 1h-2c-4-2-6-6-10-9h0 0 1 0c-1-1-1-2-2-3h0c-1-2-2-3-2-4z" class="Z"></path><path d="M113 438h1 0l2 2c2 3 4 7 7 10 3 4 7 7 10 10l-1 2c-5-4-10-10-14-15 0-1-1-2-2-3l-3-6z" class="d"></path><path d="M133 460l1 1 3 2 2 2c1 0 2 1 3 1l3 2 6 2h0l1 1s-1 0-1 1l-2-1c0 1-1 1-2 1l-2-1-1 1 1 1-1 1-7-4v-1c-1-1-2-2-3-2-1-1-3-2-4-3l1-1h2l-1-1 1-2z" class="Q"></path><path d="M137 463l2 2c-1 1-1 1-2 1l-3-2 3-1z" class="L"></path><path d="M133 460l1 1 3 2-3 1-1-1-1-1 1-2z" class="W"></path><path d="M139 465c1 0 2 1 3 1l3 2s-1 1-2 1h1l-1 1-3-1-3-3c1 0 1 0 2-1z" class="X"></path><path d="M140 469c1-1 2-1 3 0h1l-1 1-3-1z" class="H"></path><g class="B"><path d="M145 468l6 2h0l1 1s-1 0-1 1l-2-1c0 1-1 1-2 1l-2-1-2-1 1-1h-1c1 0 2-1 2-1z"></path><path d="M144 469c2 1 4 1 5 2 0 1-1 1-2 1l-2-1-2-1 1-1z"></path></g><path d="M137 261c11-5 21-4 32 0l4 2c4 2 9 4 11 9v1c-6 1-13-4-19-7-7-3-14-4-22-2l-1-1c-8 3-13 7-17 15l-1 3v7h0v-1c0 1 1 2 1 3v2l1-2 1 5c0 2 2 3 2 6 1 1 1 2 2 3v1 1c-4-2-8-2-12 0-9 4-13 12-15 20-3 7-4 14-5 21l-2 11h-1v-2c1-1 1-3 1-4v-11l2-11c-1 0-1 0-1-1v-2c2-10 5-20 11-29 2-4 6-7 8-11 3-5 4-11 7-16 2-5 7-9 12-12v1h1v1z"></path><path d="M123 290c-1-3 0-6 1-9v7c0 1-1 2-1 2z" class="M"></path><path d="M115 295c2-1 4-2 7-2v1c-1 1-4 1-6 2l-1-1z" class="B"></path><path d="M124 288h0v1l2 7c0 1 0 1-1 2l-2-5v-3s1-1 1-2z" class="H"></path><path d="M124 288h0v1 3l-1 1v-3s1-1 1-2z" class="F"></path><path d="M124 288v-1c0 1 1 2 1 3v2l1-2 1 5c0 2 2 3 2 6 1 1 1 2 2 3v1c-3-2-4-4-6-7 1-1 1-1 1-2l-2-7v-1z" class="G"></path><path d="M111 297c2-1 3-1 4-2l1 1c-7 3-10 11-13 17-2 5-3 11-4 17-1 0-1 0-1-1v-2c2-10 5-20 11-29 1 0 2 0 2-1z" class="B"></path><path d="M142 263c10-3 18-1 26 3 6 2 10 5 16 6v1c-6 1-13-4-19-7-7-3-14-4-22-2l-1-1z" class="J"></path><path d="M109 298c2-4 6-7 8-11 3-5 4-11 7-16 2-5 7-9 12-12v1h1v1c-6 3-10 8-13 14-2 4-4 9-6 13-1 2-3 3-4 5-1 1-2 3-3 4 0 1-1 1-2 1z" class="B"></path><path d="M143 264c8-2 15-1 22 2 6 3 13 8 19 7l3 9v-1h2v2h-1c0 2 0 3-1 4-1 12-7 22-16 29l-3 2c-4 2-8 4-13 5-7 1-15 0-22 0-15 1-28 10-33 24h0-1 0c1-7 2-14 5-21 2-8 6-16 15-20 4-2 8-2 12 0 1 0 2 0 2 1 3 2 6 4 10 4h1c9 1 16-1 23-7l1-2c2-3 4-7 4-11 0-10-4-15-10-22-3-1-5-3-8-3-4-1-7-1-11-1v-1z"></path><path d="M156 314c0-1 0-1 1-2h1l1 1-1 1h0-2 0z" class="C"></path><path d="M187 282v-1h2v2h-1c0 2 0 3-1 4v-5z" class="W"></path><path d="M159 313c1 2 0 2 0 4h0c-2 0-2-1-3-2v-1h0 2 0l1-1z" class="I"></path><path d="M175 298h2s1 1 1 2l-2 2h0c-1-1-2-2-3-2 1-1 1-2 2-2z" class="S"></path><path d="M162 269c4 1 7 5 10 8 3 4 4 10 3 15s-3 9-7 12h-1l1-2c2-3 4-7 4-11 0-10-4-15-10-22z" class="C"></path><path d="M99 347c1-7 2-14 5-21 2-8 6-16 15-20 4-2 8-2 12 0 1 0 2 0 2 1-6-1-10-3-16 1s-10 12-12 19v1c4-8 11-16 20-18 4-2 8-2 12 1 2 1 4 2 4 5 1 1 0 3-1 4-1 2-2 2-3 2h-1c2-1 4-2 4-4 1-1 0-3-1-4-1-2-4-4-7-4-6 0-13 3-18 8-6 5-11 13-13 20 0 3 0 6-1 9h-1 0z" class="X"></path><path d="M124 281l1-3c4-8 9-12 17-15l1 1v1c4 0 7 0 11 1 3 0 5 2 8 3 6 7 10 12 10 22 0 4-2 8-4 11l-1 2c-7 6-14 8-23 7h-1c-4 0-7-2-10-4 0-1-1-1-2-1v-1-1c-1-1-1-2-2-3 0-3-2-4-2-6l-1-5-1 2v-2c0-1-1-2-1-3v1h0v-7z"></path><path d="M169 285c1 1 1 3 1 4-2 0-4-1-6-2 2-1 3-2 5-2z" class="Q"></path><defs><linearGradient id="AJ" x1="137.617" y1="265.259" x2="157.219" y2="307.958" xlink:href="#B"><stop offset="0" stop-color="#9f9d9f"></stop><stop offset="1" stop-color="#cac9ca"></stop></linearGradient></defs><path fill="url(#AJ)" d="M124 281l1-3c4-8 9-12 17-15l1 1v1c4 0 7 0 11 1 3 0 5 2 8 3 6 7 10 12 10 22 0 4-2 8-4 11l-1 2c-7 6-14 8-23 7h-1c-4 0-7-2-10-4 0-1-1-1-2-1v-1-1c-1-1-1-2-2-3h2 0 1c1 2 3 3 5 4 4 2 7 3 11 1 4-1 8-3 10-7 2-3 2-7 1-10-2-6-4-9-9-12-4-2-8-2-12-1-1 0-1 0-1-1 4-1 8-2 11 0 5 2 10 7 12 12 1 4 1 8-1 12-1 4-4 6-8 8-1 0-2 0-3 1 7-1 13-2 18-8 2-3 3-6 4-11 0-1 0-3-1-4-2-7-5-11-12-15-5-3-11-4-17-2s-10 5-12 10c-2 4-2 8-2 12l-1 2v-2c0-1-1-2-1-3v1h0v-7z"></path><path d="M143 311h4c2-1 4-1 6-2 2 0 4 0 6-1 3-1 6-4 9-6l-1 2c-7 6-14 8-23 7h-1z" class="O"></path><path d="M127 295v-2h0c-1-1 0-5 0-6 2-5 5-9 10-12 0 1 0 1 1 1 4-1 8-1 12 1 5 3 7 6 9 12 1 3 1 7-1 10-2 4-6 6-10 7-4 2-7 1-11-1-2-1-4-2-5-4h-1 0-2c0-3-2-4-2-6z"></path><path d="M127 295v-2h0c-1-1 0-5 0-6 2-5 5-9 10-12 0 1 0 1 1 1-4 2-8 6-9 10-1 2-1 4-1 6 2-4 3-7 8-9 2-1 6-2 9-1s4 3 5 5 1 4 0 6-3 3-4 4c-2 1-4 0-6-1-1 0-2-1-2-2v-3h0c0 1 1 4 3 5h4c2-1 3-2 4-4v-5c-1-2-3-3-4-4-3-1-7 0-10 2-3 1-5 4-5 7-1 3 0 6 2 8v1h-1 0-2c0-3-2-4-2-6z" class="B"></path><path d="M106 405c-1-3-1-5-2-8 1 0 1 2 2 3l1-1c2 7 6 14 10 20 6 12 14 22 26 28 3 2 6 2 10 3 1 0 4 1 6 0 1 0 3-1 5-1 4-2 6-5 9-9h0 0l-2-1-1-1h4v-1c1 0 3 1 4 0h2c3 1 7 0 10-2 1-1 2-2 4-3v-1l1 1 1-1h0 1 0l1 1h0l6-21v3c1 3 0 5 0 8l-2 11-2 10h1 0l1-2v2c0 1 0 1-1 2l1 1-1 2c-1 0-1 1-2 1v3c-2 1-4 3-6 4-5 5-10 10-17 13 0 0 0 1-1 1l-1 1h-1c-1 1-2 1-2 1l-1 1h0l1 1-3 1c-1 0-3 0-4-1h-1l-12-2c0-1 1-1 1-1l-1-1h0l-6-2-3-2c-1 0-2-1-3-1l-2-2-3-2c2 0 5 0 6 1h4c0-1 1-1 1-2s-1-3-2-4-4-2-6-3c-3-2-5-4-8-7-7-8-14-17-19-26-2-5-4-10-5-15h1z"></path><path d="M174 461v1l2-2v4h0c-1-1-2-1-2-2v-1z" class="C"></path><path d="M200 443h1 0l1-2v2c0 1 0 1-1 2l-2 2c0-2 1-3 1-4z" class="S"></path><path d="M179 455c2 0 5 0 7 1h0 0c-1 0-2 0-2 1-2-1-3-1-4-1l-1-1z" class="C"></path><path d="M174 461h0c0-3 3-5 5-6l1 1c-2 1-3 2-4 4l-2 2v-1z" class="K"></path><path d="M199 447l2-2 1 1-1 2c-1 0-1 1-2 1v3c-2 1-4 3-6 4v-1c1-1 2-3 3-3 1-2 2-4 3-5z" class="Z"></path><path d="M197 431h0l1 1-5 20v-1c1-7 1-13 4-20z" class="E"></path><path d="M186 456l2-1h-1c-4-2-6-4-8-7-1-2-1-5 0-8l1 5c1 3 3 7 6 8 2 1 3 2 5 1 1-1 1-2 2-3v1c0 1-1 4-3 4-2 1-4 1-6 1 0-1 1-1 2-1h0 0z" class="U"></path><path d="M131 445c3 2 5 5 8 8 1 1 4 1 5 2s2 3 2 5c0 1 0 2-1 3h-3s-1 0-2-1h4c0-1 1-1 1-2s-1-3-2-4-4-2-6-3c-3-2-5-4-8-7l2-1z" class="O"></path><path d="M151 470c9 2 17 2 25-1 0 0 0 1-1 1l-1 1h-1c-1 1-2 1-2 1l-1 1h0l1 1-3 1c-1 0-3 0-4-1h-1l-12-2c0-1 1-1 1-1l-1-1z" class="X"></path><path d="M163 474l7-1 1 1-3 1c-1 0-3 0-4-1h-1z" class="Y"></path><path d="M105 405h1c4 15 14 29 25 40l-2 1c-7-8-14-17-19-26-2-5-4-10-5-15z" class="L"></path><path d="M178 437h2c3 1 7 0 10-2 1-1 2-2 4-3v-1l1 1 1-1c-1 3-5 6-8 8-4 2-8 2-12 1l-2 2-1-2h0l-2-1-1-1h4v-1c1 0 3 1 4 0z" class="B"></path><path d="M170 438h4 4l-2 2-2 2-1-2h0l-2-1-1-1z" class="W"></path><path d="M142 463l1 1 2 1 1 1h0 2c1 1 2 0 3 1h6 0c0-1 1 0 1 0v-1h1c3-1 7-3 8-5v-1l1-1c1-2 3-4 3-7h1l1-4v-3h1v2c-1 2 0 4-1 5v1c-1 1-1 1-1 2v1c-1 1-2 3-3 4-1 2-2 4-4 5h-1c-1 1-2 2-4 2 0 1-1 0-1 1h-1-1c-1 1-3 0-4 0h-4c-1-1-1-1-2-1h-2v-1h-2-1c-1 0-2-1-3-1l-2-2-3-2c2 0 5 0 6 1s2 1 2 1z" class="S"></path><defs><linearGradient id="AK" x1="156.32" y1="453.912" x2="158.009" y2="439.922" xlink:href="#B"><stop offset="0" stop-color="#a8a7a8"></stop><stop offset="1" stop-color="#c8c8ca"></stop></linearGradient></defs><path fill="url(#AK)" d="M173 440l1 2c-2 5-8 9-13 11-4 1-18 1-21-1-1 0-1 0-1-1h1c2 1 8 1 10 0h0l-1-1h4c1 0 4 1 6 0 1 0 3-1 5-1 4-2 6-5 9-9h0z"></path><path d="M106 400l1-1c2 7 6 14 10 20 6 12 14 22 26 28 3 2 6 2 10 3h-4l1 1h0c-2 1-8 1-10 0h8c-2-1-5-1-8-3-10-5-17-15-23-25-4-8-8-15-11-23z" class="B"></path><path d="M201 288l-2-8c2 3 3 6 4 9 2 4 4 7 7 10 0 0 1 0 1-1 3 4 5 7 7 11 0-7-2-13-3-20 1 1 2 3 2 5l3 6c0 1 1 2 1 3 1 4 3 7 5 11 2 5 7 11 11 16h2l1 1c1 1 5 4 5 6v1c0 1 2 3 3 4 1 4 3 8 5 11 1 3 3 5 5 7-1 0-2 0-2-1-2-1-4-2-5-3h-1c2 3 5 4 7 7-4-2-8-4-12-5-2-1-5-1-7-2l-6 1-8 2c-2 1-4 1-5 2-3 1-7 3-10 3h-10c-6 0-13-3-18-6-2 0-3 0-5-2h0l-1-1 1-1c-6-4-11-7-18-9-9 0-18 0-26 3-9 5-16 11-20 21-2 6-2 12-2 17v6c2 6 4 11 8 17l4 4c0 1 1 1 1 2v2c2 2 5 4 7 6 3 2 7 4 10 7 1 1 2 1 3 2 3 2 8 4 13 5s9 2 15 2l2 1h0 0c-3 4-5 7-9 9-2 0-4 1-5 1-2 1-5 0-6 0-4-1-7-1-10-3-12-6-20-16-26-28-4-6-8-13-10-20l-2-7c-2-11-2-20 5-30l-1-1c0-1 1-1 1-2 6-7 15-11 23-16l6-3v1l-1 1c2 0 5-1 6-1l3-1c8-1 16-2 23-6 5-2 10-5 14-8 2-3 6-5 8-8 9-10 9-18 7-30h2z"></path><path d="M144 341l3-1c1 1 1 1 2 1-1 1-2 1-3 1-1-1-1-1-2-1z" class="J"></path><path d="M130 429c1 0 1-1 2 0h0c-2 1-3 2-4 3h-1l-1-1h0c2-1 3-2 4-2z" class="U"></path><path d="M196 347l8 1c-1 1-2 1-4 1l-5-1 1-1z" class="c"></path><path d="M138 435h0c1 0 2 1 3 1 0 2 0 2-1 3h-1l-1-1c-1-1-1-2-1-3h1z" class="C"></path><path d="M184 346l10 1h2l-1 1c-4 1-9-1-13-1l2-1z" class="W"></path><path d="M108 377l1 1c0 3 0 6 1 8v6 1-1l-1 1c-1-5-1-10-1-16zm62-33l14 2-2 1-15-3h0 3z" class="G"></path><path d="M156 437c5 1 9 2 15 2l2 1h0 0-1-1-1c-5 2-10-1-14-3z" class="Y"></path><path d="M204 348c12 0 24-3 35 2h-2c-11-4-25 1-37-1 2 0 3 0 4-1z" class="G"></path><path d="M131 348c3-2 6-3 10-4h8c7-1 14-1 21 0h-3 0-6v1h-3c-9 0-18 0-26 3h-1z" class="B"></path><path d="M147 340c8-1 16-2 23-6 5-2 10-5 14-8l1 1c-5 3-9 6-14 8-5 3-11 5-17 6-2 0-4 1-5 0-1 0-1 0-2-1z" class="G"></path><path d="M193 320v3c-1 5-1 9 1 13 1 3 3 4 5 6l-2 1h0l-3-3c-3-4-4-9-3-15-2 2-4 4-5 6s-1 5 0 7c1 3 5 5 8 6h0c-3-1-6-1-8-4-2-2-2-4-1-7 0-3 2-5 5-7 0-1 0-1 1-2 0-1 1-2 2-4z" class="C"></path><path d="M108 377c1-2 1-5 2-8 4-11 12-17 21-21h1c-9 5-16 11-20 21-2 6-2 12-2 17-1-2-1-5-1-8l-1-1z" class="J"></path><defs><linearGradient id="AL" x1="116.326" y1="402.182" x2="112.896" y2="403.66" xlink:href="#B"><stop offset="0" stop-color="#a9a9a8"></stop><stop offset="1" stop-color="#c8c9c9"></stop></linearGradient></defs><path fill="url(#AL)" d="M110 392c2 6 4 11 8 17l4 4c0 1 1 1 1 2v2-1h-1c-1-1-1-1-3-1-5-7-8-13-10-22l1-1v1-1z"></path><path d="M199 288h2c2 11 1 19-6 28-3 4-6 8-10 11l-1-1c2-3 6-5 8-8 9-10 9-18 7-30z" class="F"></path><defs><linearGradient id="AM" x1="213.805" y1="316.13" x2="198.208" y2="312.783" xlink:href="#B"><stop offset="0" stop-color="#29282a"></stop><stop offset="1" stop-color="#4f4f50"></stop></linearGradient></defs><path fill="url(#AM)" d="M193 320c3-3 5-7 9-8 3-1 7-1 10 0 1 1 2 2 3 4 0 1 1 1 1 2-1-1-2-1-3-2l-2-1c0-1-1-1-1-1-1 0-1 0-2 1h0c-4-1-6-1-9 1-2 2-4 5-6 7v-3z"></path><path d="M110 359c6-7 15-11 23-16l6-3v1l-1 1c2 0 5-1 6-1s1 0 2 1c-14 3-27 9-36 20l-1-1c0-1 1-1 1-2z" class="V"></path><path d="M119 415c2 0 2 0 3 1h1v1c2 2 5 4 7 6 3 2 7 4 10 7 1 1 2 1 3 2 0 2 5 5 6 7 2 1 3 3 4 4 2 2 3 5 6 6h0v1h-2c-1-1-9-11-11-13-3-3-7-5-11-7-3-2-5-5-8-7a30.44 30.44 0 0 1-8-8z" class="H"></path><path d="M215 289c1 1 2 3 2 5l3 6c0 1 1 2 1 3v2h-1v3h0v1c-1 1-1 3 0 5v7c1 8 0 22-8 25-3 1-6 1-9 0-2 0-4-1-6-3l2-1c2 2 4 3 7 4 3 0 5 0 7-2 5-4 5-11 6-17l-1-1h-1v-2l1-3v-1c0-1-1-2-2-2 0-1-1-1-1-2h3v-6c-2-4-6-8-8-11 0 0 1 0 1-1 3 4 5 7 7 11 0-7-2-13-3-20z" class="E"></path><path d="M218 321h1v6l-1-1h-1v-2l1-3z" class="N"></path><path d="M218 316l1 5h-1v-1c0-1-1-2-2-2 0-1-1-1-1-2h3z" class="Q"></path><path d="M221 305v-2c1 4 3 7 5 11 2 5 7 11 11 16h2l1 1c1 1 5 4 5 6v1c0 1 2 3 3 4 1 4 3 8 5 11 1 3 3 5 5 7-1 0-2 0-2-1-2-1-4-2-5-3-2-2-3-3-5-4l-1 1-4-3c-4-3-10-7-12-12-4-6-5-13-7-19 0-4-2-7-2-11h0v-3h1z"></path><path d="M237 330h2l1 1c1 1 5 4 5 6v1c0 1 2 3 3 4 1 4 3 8 5 11 1 3 3 5 5 7-1 0-2 0-2-1-1-1-1-2-2-2-1-2-2-3-3-4l-6-12c-2-4-5-8-8-11z" class="F"></path><path d="M220 308h0v-3h1c1 5 2 11 4 16 1 4 2 9 4 13 4 7 10 13 17 18l-1 1-4-3c-4-3-10-7-12-12-4-6-5-13-7-19 0-4-2-7-2-11z" class="B"></path><path d="M208 315h0c1-1 1-1 2-1 0 0 1 0 1 1l2 1c1 1 2 1 3 2 1 0 2 1 2 2v1l-1 3v2h1l1 1c-1 6-1 13-6 17-2 2-4 2-7 2-3-1-5-2-7-4s-4-3-5-6c-2-4-2-8-1-13 2-2 4-5 6-7 3-2 5-2 9-1z"></path><path d="M208 315h0c1-1 1-1 2-1 0 0 1 0 1 1l2 1c1 1 2 1 3 2 1 0 2 1 2 2v1l-1 3v-3c-1-1-1 0-2 0v3c0-1 0-2-1-3-1-3-3-5-6-6z" class="D"></path><path d="M215 324v-3c1 0 1-1 2 0v3 2c-1 2-2 4-4 6-2 1-3 1-5 1 3-2 5-3 6-6v-1c0-1 0-1 1-2z" class="U"></path><path d="M196 328l-1-2 1-1h0c1 0 2 0 2-1 2-2 2-2 4-3v-1h3 0 1c1 1 2 2 2 3 1 1 1 1 1 2l-1 1v-1l-1-1h0c0-1-1-2-2-3-1 0-2 0-4 2-1 0-1 2-1 4 1 0 2 2 3 2h0 1c1 0 2 0 2-1h1c-1 1-1 2-2 2l-1 1h-1c-1 0-3-1-4-2h-1l-2-1z" class="N"></path><path d="M214 326v1c-1 3-3 4-6 6 2 0 3 0 5-1-1 2-3 3-5 4-3 0-5 0-8-2-2-1-4-3-4-6l2 1h1c1 1 3 2 4 2h1 2c4-1 6-2 8-5z" class="S"></path><path d="M214 326v1c-1 3-3 4-6 6 0 0-1 0-2 1-1 0-4-1-5-2h0l-3-3h1c1 1 3 2 4 2h1 2c4-1 6-2 8-5z" class="M"></path><path d="M161 345v-1h6l15 3c4 0 9 2 13 1l5 1c12 2 26-3 37 1h2c3 1 5 3 8 5h0c0-1-1-2-2-2l1-1c2 1 3 2 5 4h-1c2 3 5 4 7 7-4-2-8-4-12-5-2-1-5-1-7-2l-6 1-8 2c-2 1-4 1-5 2-3 1-7 3-10 3h-10c-6 0-13-3-18-6-2 0-3 0-5-2h0l-1-1 1-1c-6-4-11-7-18-9h3z"></path><path d="M231 356c3-1 5-1 8-1 1 0 1 0 2 1h-3l-6 1-1-1z" class="G"></path><path d="M205 362c5 0 9-1 13-3s9-3 13-3l1 1-8 2c-2 1-4 1-5 2-3 1-7 3-10 3-1-1-2 0-3-1h4 1l1-1h0c-2 0-3 0-5 1h-2v-1z" class="W"></path><path d="M239 350c3 1 5 3 8 5h0c0-1-1-2-2-2l1-1c2 1 3 2 5 4h-1c2 3 5 4 7 7-4-2-8-4-12-5-2-1-5-1-7-2h3c-1-1-1-1-2-1 3 0 5 1 8 1-3-2-6-4-10-6h2z" class="F"></path><path d="M161 345c10 2 17 9 26 14 5 2 9 2 14 3h4v1h2c2-1 3-1 5-1h0l-1 1h-1-4c1 1 2 0 3 1h-10c-6 0-13-3-18-6-2 0-3 0-5-2h0l-1-1 1-1c-6-4-11-7-18-9h3z" class="L"></path><path d="M176 354l5 4c-2 0-3 0-5-2h0l-1-1 1-1z" class="D"></path><path d="M199 364c-1-1-2-1-3-1-2-1-3-1-5-2h1c1 1 1 1 2 1h1 1 1v1c1-1 2-1 4-1h4v1h2c2-1 3-1 5-1h0l-1 1h-1-4c1 1 2 0 3 1h-10z" class="V"></path><path d="M257 363c-2-3-5-4-7-7h1c1 1 3 2 5 3 0 1 1 1 2 1l4 4c4 4 7 8 9 12 1 2 2 4 2 6 1 1 1 2 2 4 0 1 1 2 1 4l-1 1v1 2l-1-1c-1 2 0 5-1 7 0 2 0 5-1 8-1 5-6 9-9 12-7 6-16 7-24 10l-18 8c-1 0-2 1-3 1l-10 6-2-1v-1h0c-2 1-3 4-5 5h0l1-2-1-1c1-1 1-1 1-2v-2l-1 2h0-1l2-10 2-11c0-3 1-5 0-8v-3l-6 21h0l-1-1h0-1 0l-1 1-1-1v1c-2 1-3 2-4 3-3 2-7 3-10 2h-2c2-5 4-10 7-14 2-3 4-7 7-10l2-4c2-2 4-5 5-8 1-1 2-3 2-4 1-3 2-7 3-10 1-7 3-14 8-19v-1c1-1 2-1 3-2l1-1 3-3h0c1-1 3-1 5-2l8-2 6-1c2 1 5 1 7 2 4 1 8 3 12 5z"></path><path d="M250 366c2 0 4 1 6 2h-2l-4-2z" class="R"></path><path d="M240 407c0 1 1 2 1 3h1c2 0 2 0 3 1 0 1 0 1-1 2h-2l-2-2v-4z" class="Q"></path><path d="M271 380l4 11v1 2l-1-1c-1 2 0 5-1 7 0-5 0-12-2-16v-4z" class="M"></path><path d="M244 364c1 0 3-1 4-1 2 0 5 2 7 3l6 5c1 1 3 2 3 3-2-2-4-3-6-4l-2-2c-2-1-4-2-6-2h-5c0-1-1-1-1-2z" class="D"></path><path d="M262 368l2 2c3 3 5 6 7 10v4l-6-9c-1 0-1 0-1-1s-2-2-3-3c1-1 1-2 1-3z" class="Z"></path><path d="M262 368l2 2c0 1 0 2 1 3v2c-1 0-1 0-1-1s-2-2-3-3c1-1 1-2 1-3z" class="b"></path><path d="M256 416v1s-1 1-2 1c-1 2-2 3-4 5-1 0-3 2-5 3-1 1-2 1-3 1l-12 5c2-3 6-4 9-5l6-4 1-1h1 1 0c2 0 6-5 8-6z" class="I"></path><path d="M228 398c1-3 2-6 4-8 3-6 8-11 15-12 3-1 7-1 10 1 1 1 1 2 2 2h-2 0c-4-2-8-3-12-1-6 1-11 8-14 13 0 2-1 3-1 5h-1-1z" class="C"></path><path d="M257 363c-2-3-5-4-7-7h1c1 1 3 2 5 3 0 1 1 1 2 1l4 4c4 4 7 8 9 12 1 2 2 4 2 6 1 1 1 2 2 4 0 1 1 2 1 4l-1 1-4-11c-2-4-4-7-7-10l-2-2-5-5z" class="L"></path><path d="M225 399h0c0-2 0-4 1-6 0-7 2-14 5-21 3-5 8-6 13-8 0 1 1 1 1 2-5 0-9 2-12 5-5 7-5 17-6 25 1-3 1-5 2-7 4-8 9-16 18-19 3-1 7-1 10 0l1 1h-2c-6-1-11 1-16 4-6 5-10 13-12 20-1 2-2 4-2 6h0 0c0-1 0-1-1-2z" class="Z"></path><defs><linearGradient id="AN" x1="245.473" y1="412.605" x2="267.484" y2="389.263" xlink:href="#B"><stop offset="0" stop-color="#1d1c1d"></stop><stop offset="1" stop-color="#393939"></stop></linearGradient></defs><path fill="url(#AN)" d="M257 379c5 3 8 8 9 13 1 6-1 15-4 21-2 4-7 8-12 10 2-2 3-3 4-5 1 0 2-1 2-1v-1l2-3c4-6 5-12 4-19l1-1h1c-1-5-3-9-7-12h0 2c-1 0-1-1-2-2z"></path><path d="M230 398c0-2 1-3 1-5 3-5 8-12 14-13 4-2 8-1 12 1 4 3 6 7 7 12h-1l-1 1c-2-3-4-6-7-7-4-1-9-1-13 1s-8 6-10 9h-1l-1 1z"></path><path d="M231 397c2-4 6-8 11-10 4-2 8-3 13-2a30.44 30.44 0 0 1 8 8l-1 1c-2-3-4-6-7-7-4-1-9-1-13 1s-8 6-10 9h-1z" class="C"></path><path d="M232 397c2-3 6-7 10-9s9-2 13-1c3 1 5 4 7 7 1 7 0 13-4 19l-2 3c-2 1-6 6-8 6h0c1-2 3-4 4-6 1-4 2-9 1-13-2-3-4-5-7-6s-5-1-8 0c-7 3-9 9-11 15h0v-4h-1c0-2 1-4 0-6v-1h0 1c0-1 0-2 1-3h0 1 1l1-1h1z"></path><path d="M257 397h1c1 0 2 0 3 1 0 1-1 2-2 2 0 1-1 1-2 1h-1c0-2 0-2 1-4zm-26 0h1c-3 4-4 7-5 11h-1c0-2 1-4 0-6v-1h0 1c0-1 0-2 1-3h0 1 1l1-1z" class="I"></path><path d="M227 412c2-6 4-12 11-15 3-1 5-1 8 0s5 3 7 6c1 4 0 9-1 13-1 2-3 4-4 6h-1-1c2-3 4-5 5-8 1-4 1-8-1-12-2-2-4-3-7-4-3 0-7 1-9 3v1h2c3 1 3 3 4 5v4c-2 8-6 15-12 21-2 2-5 3-7 5v1c-1 0-2 1-3 1l-10 6-2-1v-1c11-11 17-26 20-41 1 2 0 4 0 6h1v4h0z"></path><path d="M226 408h1v4h0l-1 5s0-1-1-1c0-3 1-5 1-8z" class="R"></path><path d="M236 402c3 1 3 3 4 5v4c-2 8-6 15-12 21-2 2-5 3-7 5v1c-1 0-2 1-3 1 2-3 6-5 9-7 3-3 5-7 8-10 3-5 5-11 4-17-1-1-2-1-3-2h-2 0l2-1z" class="C"></path><path d="M227 412c2-6 4-12 11-15 3-1 5-1 8 0s5 3 7 6c1 4 0 9-1 13-1 2-3 4-4 6h-1-1c2-3 4-5 5-8 1-4 1-8-1-12-2-2-4-3-7-4-3 0-7 1-9 3v1h2l-2 1h0 2c-1 1-3 0-4 1-1 2-1 4-2 5-2 3-2 7-3 10-3 9-8 17-16 23h-1c3-4 7-6 10-10s4-10 6-15l1-5z" class="D"></path><path d="M238 356c2 1 5 1 7 2 4 1 8 3 12 5l5 5c0 1 0 2-1 3l-6-5c-2-1-5-3-7-3-1 0-3 1-4 1-5 2-10 3-13 8-3 7-5 14-5 21-1 2-1 4-1 6h0c1 1 1 1 1 2v1c-3 15-9 30-20 41h0c-2 1-3 4-5 5h0l1-2-1-1c1-1 1-1 1-2v-2l-1 2h0-1l2-10 2-11c0-3 1-5 0-8v-3l-6 21h0l-1-1h0-1 0l-1 1-1-1v1c-2 1-3 2-4 3-3 2-7 3-10 2h-2c2-5 4-10 7-14 2-3 4-7 7-10l2-4c2-2 4-5 5-8 1-1 2-3 2-4 1-3 2-7 3-10 1-7 3-14 8-19v-1c1-1 2-1 3-2l1-1 3-3h0c1-1 3-1 5-2l8-2 6-1z"></path><path d="M221 375h1c2 0 3 1 3 2v2h-3s-1 0-1-1v-3z" class="I"></path><path d="M225 399c1 1 1 1 1 2v1c-3 15-9 30-20 41h0c9-8 13-20 16-31 1-4 2-9 3-13z" class="K"></path><defs><linearGradient id="AO" x1="222.587" y1="356.402" x2="236.293" y2="363.214" xlink:href="#B"><stop offset="0" stop-color="#171614"></stop><stop offset="1" stop-color="#3d3c41"></stop></linearGradient></defs><path fill="url(#AO)" d="M238 356c2 1 5 1 7 2h-1-6c-3 0-7 2-10 3-3 2-6 4-10 5h0l2-2h-1c-2 0-5 3-7 4v-1c1-1 2-1 3-2l1-1 3-3h0c1-1 3-1 5-2l8-2 6-1z"></path><path d="M217 377c1-1 1-3 3-5-3 7-3 13-4 20l-2 9c-3 15-4 32-12 45l-1-1c1-1 1-1 1-2 2-5 4-9 5-14l3-15 4-21h0v-2c0-1 1-3 1-4 0-3 1-6 2-10z" class="Y"></path><path d="M218 366c-7 5-9 12-11 21h0c1 2 0 4 0 5 2-4 4-7 6-11 2-3 3-7 5-10 3-4 7-7 11-9 4-1 9-1 14 0 1 0 3 0 5 1-6 0-13-1-18 1h-1c-5 1-6 4-9 8-2 2-2 4-3 5-1 4-2 7-2 10 0 1-1 3-1 4v2h0l-4 21-3 15c-1 5-3 9-5 14v-2l-1 2h0-1l2-10 2-11c0-3 1-5 0-8v-3-7 1c-1-1-1-1-1-2s1-3 1-4l2-12v-4c2-7 6-14 12-17h0z" class="I"></path><path d="M214 391c-1 0-3 0-5 1h0-1l6-10c1-2 1-3 2-5h1c-1 4-2 7-2 10 0 1-1 3-1 4zm-12 50c1-6 3-12 3-18 1-3 1-6 1-9 0-5 0-11 1-16 1-1 1-3 2-4 1-2 3-2 4-2l1 1-4 21-3 15c-1 5-3 9-5 14v-2z"></path><path d="M212 368c2-1 5-4 7-4h1l-2 2c-6 3-10 10-12 17v4l-2 12c0 1-1 3-1 4s0 1 1 2v-1 7l-6 21h0l-1-1h0-1 0l-1 1-1-1v1c-2 1-3 2-4 3-3 2-7 3-10 2h-2c2-5 4-10 7-14 2-3 4-7 7-10l2-4c2-2 4-5 5-8 1-1 2-3 2-4 1-3 2-7 3-10 1-7 3-14 8-19z"></path><path d="M195 426c1 1 1 2 0 3v-1c0 1-1 1-1 1-2 1-4 2-6 1l-3-3 1-1c1 1 2 2 3 2 3 1 4-1 6-2z" class="X"></path><path d="M195 426c4-8 1-17 5-24 0 2-1 5-1 7l-1 13c0 1 0 3-1 5 0 1-1 3-1 4h0l-1 1-1-1 1-2c1-1 1-2 0-3z" class="G"></path><path d="M203 403c0 1 0 1 1 2v-1 7l-6 21h0l-1-1h0-1c0-1 1-3 1-4 1-2 1-4 1-5v1h1c1-1 1-3 2-5 1-5 1-10 2-15z" class="C"></path><path d="M132 348c8-3 17-3 26-3 7 2 12 5 18 9l-1 1 1 1h0c2 2 3 2 5 2 5 3 12 6 18 6h10c3 0 7-2 10-3h0l-3 3-1 1c-1 1-2 1-3 2v1c-5 5-7 12-8 19-1 3-2 7-3 10 0 1-1 3-2 4-1 3-3 6-5 8l-2 4c-3 3-5 7-7 10-3 4-5 9-7 14-1 1-3 0-4 0v1h-4l1 1c-6 0-10-1-15-2s-10-3-13-5c-1-1-2-1-3-2-3-3-7-5-10-7-2-2-5-4-7-6v-2c0-1-1-1-1-2l-4-4c-4-6-6-11-8-17v-6c0-5 0-11 2-17 4-10 11-16 20-21z"></path><path d="M163 428h2c1 1 0 2 0 3h-2l-1-1 1-2z" class="b"></path><path d="M190 412l3-4v1h1l-2 4c-1 0-1-1-2-1z" class="J"></path><path d="M163 422h2c1 1 1 1 1 2l-2 2h-1c-1-1-1-2-2-2 1-2 1-2 2-2z" class="Z"></path><path d="M170 398v1c1 0 2 0 3-1h1c-2 3-5 4-8 5v-1h1v-1l-2-1 5-2z" class="B"></path><path d="M146 419h3v1c1 2 0 3-1 4l-1 1c-1-1-3-2-2-4 0-1 0-2 1-2z" class="Q"></path><path d="M148 431v-1c-2-1-4-2-5-3 3 1 6 2 9 4 3 0 6 1 8 2l-2 2c-3-2-7-2-10-4z" class="I"></path><path d="M169 435l8 1c1-4 3-9 5-12 2-5 5-8 8-12 1 0 1 1 2 1-3 3-5 7-7 10-3 4-5 9-7 14-1 1-3 0-4 0-2 0-5 0-6-1h-1l2-1zm1-65c4 2 6 4 8 7 2 4 2 9 1 13-1 3-3 6-5 8h-1c-1 1-2 1-3 1v-1c3-3 6-7 7-11s0-8-2-11-4-4-8-5c1 0 2-1 3-1z" class="L"></path><defs><linearGradient id="AP" x1="151.63" y1="383.624" x2="166.59" y2="387.187" xlink:href="#B"><stop offset="0" stop-color="#969596"></stop><stop offset="1" stop-color="#b2b1b3"></stop></linearGradient></defs><path fill="url(#AP)" d="M156 378l-1 1c0 1-1 3 0 4 0 3 2 4 4 5 1 1 3 1 4 0 1 0 2-1 2-2 0-2 0-2-1-3-1 0-2 0-3 1v2h0s-1 0-1-1c-1-1 0-1 0-2 1-1 1-2 3-2 1 0 2 0 3 1s2 2 2 4c0 1-1 3-2 4-2 1-4 1-6 1s-4-1-6-3c-1-2-2-5-2-7 0-4 3-6 6-9 3-2 8-2 12-2-1 0-2 1-3 1-4 0-7 1-10 4-1 1-2 1-2 2 0 0 0 1 1 1z"></path><path d="M132 357c3-3 10-8 14-8 10-2 21 0 29 6l1 1h0c-8-3-17-6-25-4-2 2-4 4-4 7 0 2 1 4 2 5l-1 1-1-1c-1-2-1-4-1-5v-1c1-2 1-3 3-5h0 0 1c-2-1-3 0-4 1l-1-1v-1c-1 0-5 1-6 2-1 0-5 4-5 4l-2-1z" class="Y"></path><defs><linearGradient id="AQ" x1="141.134" y1="403.645" x2="158.378" y2="390.036" xlink:href="#B"><stop offset="0" stop-color="#959496"></stop><stop offset="1" stop-color="#adadaa"></stop></linearGradient></defs><path fill="url(#AQ)" d="M132 357l2 1c-5 5-8 11-8 18s4 13 9 18c6 6 14 8 23 8 2 0 5-1 7-2l2 1v1h-1v1h0c-2 1-4 1-7 2-9 0-17-2-23-8-6-5-11-12-11-20-1-8 2-14 7-20z"></path><defs><linearGradient id="AR" x1="152.279" y1="436.688" x2="156.003" y2="430.114" xlink:href="#B"><stop offset="0" stop-color="#a8a7a8"></stop><stop offset="1" stop-color="#dedcdd"></stop></linearGradient></defs><path fill="url(#AR)" d="M123 415c7 7 15 13 25 16 3 2 7 2 10 4l2-2c3 1 6 2 9 2l-2 1h1c1 1 4 1 6 1v1h-4l1 1c-6 0-10-1-15-2s-10-3-13-5c-1-1-2-1-3-2-3-3-7-5-10-7-2-2-5-4-7-6v-2z"></path><path d="M160 433c3 1 6 2 9 2l-2 1h1c1 1 4 1 6 1v1h-4l-12-3 2-2z" class="F"></path><defs><linearGradient id="AS" x1="137.134" y1="414.622" x2="174.92" y2="416.526" xlink:href="#B"><stop offset="0" stop-color="#212121"></stop><stop offset="1" stop-color="#3e3d3d"></stop></linearGradient></defs><path fill="url(#AS)" d="M133 408c1 0 2 0 3 1 2 2 7 4 9 5h2v-1c8 3 15 4 23 2 2 0 5-1 7-1v1c-2 1-4 2-6 2h-2l1 1c-1 0-4-1-5 0l1 1c3 0 6 0 9-1 2 0 3 0 4-1h1 1c0-1 1 0 2-1-5 4-12 5-18 5-3 0-5-1-7-1-3-1-6-1-8-2-6-2-13-6-17-10z"></path><path d="M133 408c-5-6-10-13-12-21-1-3-1-7-1-11 1-9 5-15 12-21 3-4 7-6 12-7-2 1-5 3-7 4-4 2-8 5-10 9-3 5-5 13-5 18 1 10 4 17 12 24s18 10 28 9c4 0 7-2 9-4s2-5 3-6l1 1c0 2-2 5-3 6-4 4-10 5-15 5-5-1-11-3-15-5-3-1-6-3-9-5 4 4 9 7 14 9v1h-2c-2-1-7-3-9-5-1-1-2-1-3-1z" class="C"></path><path d="M149 364c-1-1-2-3-2-5 0-3 2-5 4-7 8-2 17 1 25 4 2 2 3 2 5 2 5 3 12 6 18 6h10c3 0 7-2 10-3h0l-3 3-1 1c-1 1-2 1-3 2v1c-5 5-7 12-8 19-1 3-2 7-3 10 0 1-1 3-2 4-1 3-3 6-5 8h-1v-1-1h0c-3 3-6 6-10 9-1 1-2 0-2 1h-1-1c-1 1-2 1-4 1-3 1-6 1-9 1l-1-1c1-1 4 0 5 0l-1-1h2c2 0 4-1 6-2v-1c3-2 7-4 9-7 4-4 5-8 5-13 0-7-5-14-10-18-5-5-11-7-18-7-5 1-9 3-12 7-2 3-2 7-1 10s3 5 5 6l3 3c1 0 3 1 4 1h0c2 1 4 0 5 0 3 0 5-2 6-4 1-3 2-7 1-10 0-1-1-2-2-3-1-3-5-4-7-4-4-1-6 1-9 3-1 0-1-1-1-1 0-1 1-1 2-2 3-3 6-4 10-4 4 1 6 2 8 5s3 7 2 11-4 8-7 11l-5 2c-2 1-5 2-7 2-9 0-17-2-23-8-5-5-9-11-9-18s3-13 8-18c0 0 4-4 5-4 1-1 5-2 6-2v1l1 1c1-1 2-2 4-1h-1 0 0c-2 2-2 3-3 5v1c0 1 0 3 1 5l1 1 1-1z"></path><path d="M145 373l2-1h0 1 2c-1 1-2 2-3 2l-6 1-1-1c2-1 3-1 5-1z" class="a"></path><path d="M145 353l1 1c-3 1-5 4-6 7 0 2 0 5 1 7l-1 1c-1-2-2-5-1-8 1-4 3-6 6-8zm-5 21l1 1c-1 0-2 1-2 2-1 1-1 3 0 4 2 5 5 4 8 5v1 1c-2-1-5-1-7-2-1-2-3-5-3-7s1-4 3-5z" class="C"></path><defs><linearGradient id="AT" x1="149.088" y1="391.366" x2="153.305" y2="382.353" xlink:href="#B"><stop offset="0" stop-color="#707170"></stop><stop offset="1" stop-color="#919093"></stop></linearGradient></defs><path fill="url(#AT)" d="M150 374l1 2c-2 3-2 7-1 10s3 5 5 6l-1 1c0 1 0 1-1 1h0c-2-1-4-5-5-8-1-4 0-8 2-12z"></path><path d="M157 375h1c3-2 6-2 9-1s5 3 7 5c2 3 2 7 1 10s-3 7-6 8c-3 2-7 2-10 1-2-1-4-2-6-4h0c1 0 1 0 1-1l1-1 3 3c1 0 3 1 4 1h0c2 1 4 0 5 0 3 0 5-2 6-4 1-3 2-7 1-10 0-1-1-2-2-3-1-3-5-4-7-4-4-1-6 1-9 3-1 0-1-1-1-1 0-1 1-1 2-2z" class="K"></path><path d="M155 392l3 3h-1c-1 0-2 0-4-1 1 0 1 0 1-1l1-1z" class="M"></path><path d="M198 389c1 3 1 5 1 7v2l1 1c-1 0-1 2-1 2-1 3-3 6-5 8h-1v-1-1h0c-3 3-6 6-10 9-1 1-2 0-2 1h-1-1c-1 1-2 1-4 1-3 1-6 1-9 1l-1-1c1-1 4 0 5 0 5-1 10-2 14-5 6-3 11-10 13-17v2l1-1v-1c1-2 0-4 0-7z" class="P"></path><path d="M199 398l1 1c-1 0-1 2-1 2-1 3-3 6-5 8h-1v-1-1c3-1 4-7 6-9z" class="G"></path><path d="M150 374c4-4 7-6 13-7 7 0 15 5 20 9 5 5 9 11 9 18 0 6-2 10-5 14-3 3-6 5-10 7v-1c3-2 7-4 9-7 4-4 5-8 5-13 0-7-5-14-10-18-5-5-11-7-18-7-5 1-9 3-12 7l-1-2z" class="H"></path><path d="M149 364c3 2 5 2 7 2 2-1 4-1 5-2 7-1 12 0 18 1 2 1 5 2 7 3v1c0 1 2 2 3 3 5 5 8 11 9 17 0 3 1 5 0 7v1l-1 1v-2c1-7-1-13-5-19s-10-10-18-11c-5-1-12-1-17 1-2 1-5 3-7 5h-2-1 0l-2 1-5-4 1-1c1 1 1 2 2 3 1 0 2 1 3 1 2-1 9-4 10-5h-2c-2 0-4-1-5-2h-1l1-1z" class="M"></path><path d="M146 354c1-1 2-2 4-1h-1 0 0c-2 2-2 3-3 5v1c0 1 0 3 1 5l1 1h1c1 1 3 2 5 2h2c-1 1-8 4-10 5-1 0-2-1-3-1-1-1-1-2-2-3-1-2-1-5-1-7 1-3 3-6 6-7z"></path><path d="M149 364c-1-1-2-3-2-5 0-3 2-5 4-7 8-2 17 1 25 4 2 2 3 2 5 2 5 3 12 6 18 6h10c3 0 7-2 10-3h0l-3 3-1 1c-1 1-2 1-3 2v1c-5 5-7 12-8 19-1 3-2 7-3 10 0 1-1 3-2 4 0 0 0-2 1-2l-1-1v-2c0-2 0-4-1-7-1-6-4-12-9-17-1-1-3-2-3-3v-1c-2-1-5-2-7-3-6-1-11-2-18-1-1 1-3 1-5 2-2 0-4 0-7-2z"></path><path d="M159 357h0c2 0 3-1 4-1-1 1-1 2-2 3-1 0-1 0-2-1v-1z" class="S"></path><path d="M159 357c0-1 0-1 1-2s2-1 3-1l1 1-1 1c-1 0-2 1-4 1h0z" class="D"></path><path d="M194 366c1-1 1-1 1 0 1 0 1 0 2 1l-2 2c-1 0-2-1-2-2s0-1 1-1z" class="a"></path><path d="M212 367v1c-5 5-7 12-8 19-1 3-2 7-3 10 0 1-1 3-2 4 0 0 0-2 1-2l-1-1v-2c2-4 4-10 3-14-2-7-10-10-16-13v-1l11 5c4 1 8-1 11-3-3 3-6 4-10 4 2 3 4 5 5 8 1-3 2-7 4-9 1-1 1-1 1-2s3-3 4-4z" class="J"></path><path d="M186 369c6 3 14 6 16 13 1 4-1 10-3 14 0-2 0-4-1-7-1-6-4-12-9-17-1-1-3-2-3-3z"></path></svg>