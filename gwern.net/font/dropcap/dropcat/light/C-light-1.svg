<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="44 32 432 636"><!--oldViewBox="0 0 512 752"--><style>.B{fill:#2b2a2b}.C{fill:#383737}.D{fill:#d6d5d5}.E{fill:#454545}.F{fill:#595859}.G{fill:#dfdede}.H{fill:#f2f1f1}.I{fill:#242425}.J{fill:#3c3c3c}.K{fill:#323132}.L{fill:#282828}.M{fill:#ebeaea}.N{fill:#1b1a1b}.O{fill:#676666}.P{fill:#cac9c9}.Q{fill:#535254}.R{fill:#d0cfcf}.S{fill:#201f20}.T{fill:#4f4f4f}.U{fill:#4b4b4b}.V{fill:#414041}.W{fill:#bfbebe}.X{fill:#616061}.Y{fill:#141313}.Z{fill:#e5e4e5}.a{fill:#6d6c6c}.b{fill:#7a7979}.c{fill:#727172}.d{fill:#070707}.e{fill:#888788}.f{fill:#b5b3b4}.g{fill:#9f9e9e}</style><path fill="#fff" d="M257 0h255v752H0V0h250 7z"></path><path d="M235 363l3-3-1 2c1 1 1 1 2 1l-2 1-2-1z" class="Q"></path><path d="M383 658h1c0 1 1 3 0 4h-1v1c0-1-1-3 0-5zM247 227l1 1c0 3 0 5-1 8h-1c0-3 1-6 1-9z" class="H"></path><path d="M92 227l2-8c1 1 1 2 0 3h1 0c0 1-1 2-1 3v1c-1 1-1 1-2 1z" class="I"></path><path d="M299 33v-1l1-4c0 1 0 3 1 4 1 2 2 3 2 4l-1 1c0-1-1-1-1-2v-2h-1-1z" class="H"></path><path d="M44 505h1c2-1 9 0 11 1-3 1-9 0-12-1z" class="Z"></path><path d="M102 713h8c1 0 2 0 3 1h-2c-2 0-8 1-11 0l1-1h1z" class="H"></path><path d="M208 552h3l-2 3c-1 1-2 2-4 2 1-1 2-3 3-5z" class="R"></path><path d="M235 363l2 1c-2 2-4 4-5 7h-1c1-3 2-5 4-8z" class="F"></path><path d="M26 466h3c3-1 5-1 8-1l-4 1h1c1 1 5 0 7 0 0 1 0 1 1 1h2l-18-1z" class="D"></path><path d="M152 593c0-1 1-1 1-1l2 1c2 0 3 1 4 2h-2c0 1 1 1 1 2-2-1-4-3-6-4z" class="B"></path><path d="M299 33h1l-2 6c1 0 1 0 2-1v1h2l-6 3c1 0 1-1 1-2 1-2 2-5 2-7z" class="M"></path><path d="M248 482l1 1h0l1 2c1 2 2 4 1 6 0 0-1 0-1-1-2-2-2-6-2-8z" class="O"></path><path d="M112 147l5-8-1 3c0 2-3 7-4 8v-3z" class="H"></path><path d="M355 358c3 1 4 3 6 6v1c-2-2-4-4-6-5v-2z" class="P"></path><path d="M239 407l2 2h0c0 1 0 2-1 3 0 2 0 4-1 6v-4c0-2-1-4 0-7z" class="K"></path><path d="M243 704h9v2c-1 1-5 0-7 0h-1c2 0 4 0 5-1-2 0-4-1-6-1z" class="M"></path><path d="M59 259h2 1 0v-1c0-2 0-2 1-3h-1l-5 2h-2c2-1 3-1 5-2 2 0 4-1 6-2 0 1-1 1-1 2-1 1-2 5-3 5-1-1-2-1-3-1z" class="R"></path><path d="M322 264v-1h0c-1-2-1-3-3-3v-1c1-1 1-2 2-2 1 1 2 4 2 5v1c1 2 2 5 2 8-1-3-2-5-3-7z" class="S"></path><path d="M88 108h1 0c-2 1-4 2-6 2-3 1-5 2-9 2 0 0-1 0-1-1h0c5 0 10-2 15-3zm235 194v-7 1l1 2v1 2c0 1 1 2 1 3 1 2 1 4 0 6-1-3-2-5-2-8z" class="H"></path><path d="M118 152v4c-1 3-3 7-5 10h0l5-14z" class="g"></path><path d="M64 671c0 2 0 3 1 4 1 4 2 6 6 9l-2 1c-2-1-4-1-6-2h0-1l-1-1h1c2 1 4 0 6 1h0 0v-1l-1-1v-1l-1 1h0-1s0-1 1-1c-1-3-2-5-2-8v-1z" class="R"></path><path d="M205 540v3c1 0 1-2 2-3h0 0v6c0 1-1 2-1 4h0c0 1-1 2-2 2l1-2v-3c0-3-1-5 0-7z" class="f"></path><path d="M107 623h1c1 1 3 2 4 4 1 1 2 3 3 4 0 1 1 3 2 3 1 2 1 3 0 5v1l-3-9c-2-3-4-5-7-8z" class="P"></path><path d="M250 51l1 1h1 3v3h1 1c1 0 4 1 5 0h4c-5 1-10 2-15-1-1 0-1-1-2-2l1-1z" class="R"></path><path d="M112 147v3c-1 1-1 2-1 3-1 2-2 4-3 5h-1v1 1c-2 2-3 4-5 6 1-2 3-4 4-7 1-1 2-3 3-5 0-1 1-2 1-3l2-4z" class="M"></path><path d="M371 66c3-1 8 0 10 1 1 1 2 0 3 1h3c0 1 1 1 1 1h0v1c-1 0-4-1-5-2h-4-1c-2-1-5-1-7-1v-1z" class="H"></path><path d="M244 299l6 7c1 2 1 4 0 7h0c0-1 0-2-1-4-1-1-1-2-1-3l-3-3v-1c0-1-1-2-1-3h0z" class="P"></path><path d="M450 410c1 0 1-1 1-1h1c1-1 3-1 4-1h1 2-1c-1 0-2-1-3 0h-2c-1 1-2 1-3 1h0v-1h1l1-1v1l2-1h5c1 1 3 1 4 1l1 1h-5l-9 1z" class="G"></path><path d="M392 651l2-1 1 1c5 1 10 5 13 9h0l-1-1-3-3c-1 1 0 2 0 3v-1c-2-4-7-5-11-6-1 0 0 0-1-1h0z" class="Z"></path><path d="M454 403v-1h1c1 0 1 1 2 2h0c2 1 5 2 6 4-1 0-3 0-4-1h-5 3c0-1-1-1-1-1h1 1 1c-1-1-5-1-6-1-1-1-1-1-2-1l1-1h0c0-1 1 0 2 0z" class="P"></path><path d="M242 662v-1c5-5 14-4 21-4h0c1 1 1 1 2 1h-1 0c-6-1-16 0-22 4z" class="G"></path><path d="M127 113c1 4-1 10-3 14-1 0-1 0-1 1v-5c1-1 1-2 2-3h0c1-2 1-5 2-7zm284-76h1v5c0 4 0 8-2 13v-2h0 0v-2c1 0 0-2 1-3 0-1 0-1-1-2h1v-5h-1c1-1 1-3 1-4z" class="M"></path><path d="M239 353c1 0 4-2 5-2l3 1c-3 1-8 3-10 5v1h0c-2 0-3 1-4 2 2-3 3-5 6-7z" class="K"></path><path d="M380 655c-7 2-14 4-21 4l29-6h1l-1 1c-2 0-4 0-6 1h-2z" class="G"></path><path d="M221 267h1c0 1 0 2 1 3 0 1 1 1 2 2h0 2v1h0c-1 1-3 1-3 2v1c-1 1-1 1-1 3 0-2 0-3-1-4v1c0-1-1-1-1-2v-2c1 0 0-1 0-1 1-2 0-3 0-4z" class="N"></path><path d="M225 272h0 2v1h0c-1 1-3 1-3 2v1c-1 1-1 1-1 3 0-2 0-3-1-4h0l3-3z" class="Y"></path><path d="M236 383h2l-1 1-1 4-3 12v-8c-1-1 2-7 3-9z" class="O"></path><path d="M267 222c4 3 7 7 12 9l-2 1c-4-2-6-3-9-6 0-1-1-1-1-2v-2z" class="T"></path><path d="M240 242c0-6 1-12 1-18 1-3 0-6 1-9 1 6 1 12-1 18 0 1 1 3 0 4v5h-1zm113 454c-2 1-1 1-2 2-1-1-3-4-4-6-1 0-1-1-2-2l-1-2h0 0 1l1 1h1c1 1 5 5 6 7z" class="H"></path><path d="M463 408c1 0 3 0 4 1v1l-11 1-3 1c-1-1-1-1-2-1h0 1v-1c-2 1-3 1-4 1 1-1 1-1 2-1l9-1h5l-1-1z" class="R"></path><path d="M452 411c1-1 3-1 4-1v1l-3 1c-1-1-1-1-2-1h0 1z" class="P"></path><path d="M200 669c2 0 3 0 4-1 3-1 6-1 9-2 0-1 0-1 1-1h1l-1-1h4c-2 1-3 2-6 3h-3l1 1h1c-2 1-4 0-5 1-5 0-9 3-14 4h0 0l8-4z" class="D"></path><path d="M420 516v-12h0c2 8 1 16 1 24 0-1 0-2-1-3v2-6-5z" class="W"></path><path d="M390 487c0-1 0-1 1-3v1c-1 1-1 2-1 2 2 1 2 1 3 2h1 0l1 1h0-1 0c1 1 1 2 2 3v1c0 1 1 2 1 3v1h-1v7c-1-1-1-3-1-4 0-3-1-5-1-8 0-1 0-2-1-3-1-2-2-2-3-3z" class="H"></path><path d="M97 530h1c0 1 0 2 1 3l1 2c1 1 1 2 2 4 0 1 1 3 2 4v2h0l-1-1v1c-2-1-2-3-3-4s-2-2-2-3c0 0-1-2-1-3 1 1 1 3 3 4 0 0 0 1 1 1h0c-1-2-1-4-2-5 0-1 0-1-1-2h0c0-1-1-2-1-3z" class="D"></path><path d="M244 706c-6 0-12-4-16-9 0 0 3 3 4 3 3 2 7 3 11 4 2 0 4 1 6 1-1 1-3 1-5 1z" class="G"></path><path d="M53 280c-2 1-5 2-7 2-2-1-4-3-5-4-1-2-1-3-1-4 2-3 5-5 8-7-1 1-3 2-4 3-1 2-2 5-2 8 1 1 2 2 4 3 2 0 5-1 7-2v1z" class="D"></path><path d="M51 571h17c8 0 15 2 22 5 1 1 3 1 4 2-1 0-2 0-3-1-4-1-7-3-11-4-9-2-19-1-29-1v-1z" class="P"></path><path d="M340 348c2 0 4 1 6 1v-1c2 1 4 2 6 4l-8-3c2 2 4 3 7 5-2-1-7-3-8-2-1 0-3-1-4-2l-1-1c1 0 1-1 2-1z" class="b"></path><path d="M106 116v1c-2 5-6 8-11 11-1 1-2 2-4 3 1-2 3-4 5-6 1-1 3-2 5-3l5-5v-1z" class="D"></path><path d="M190 672c1-1 2-1 3-2 2-1 5-2 7-2v1l-8 4h0 0l-1 1c-1 0-1 1-2 1-2 1-4 2-6 4h-1l3-3c1 0 1 0 1-1 2 0 3-1 4-2h0 0v-1z" class="H"></path><path d="M107 109c-1 1-2 3-1 4-2 2-3 3-5 4l-2 1h0l-5 2h0-1c2-1 3-1 4-2l3-3h-2l9-6z" class="W"></path><path d="M116 95h1c-2 5-5 10-8 14l-3 4c-1-1 0-3 1-4 4-4 7-8 9-14z" class="g"></path><path d="M109 109c0 3-1 6-2 8h-1v-1-2l-2 2h0c-3 3-7 6-10 9l-1-1c2-1 7-4 7-6h-1 0l2-1c2-1 3-2 5-4l3-4z" class="M"></path><path d="M86 662c-1-4-1-6 1-10h0c1-2 3-4 5-5h0 0c-3 3-5 7-5 12 0 3 0 6 2 9 1 2 3 5 6 7h-1l-2-1c-3-3-6-8-6-12z" class="D"></path><path d="M394 456h1 1c1-2 0-4 1-6v4c1 2 1 5 2 7v4h-2c-1-2-2-6-3-9z" class="g"></path><path d="M227 36c2-4 7-9 12-11h1c3 0 6-1 9 0l-1 1c-7 0-13 2-19 8l-1 2h-1z" class="G"></path><path d="M320 232c1 6 1 12 1 19-1 1-1 2-2 3-1-1 0-5 0-6v-3c1-4 0-9 1-13z" class="W"></path><path d="M183 692v-1c-2-1-3-2-4-4v-5c1 0 1 0 1-1 1 0 1-1 2-1-1 1-2 2-2 3-1 1 0 2 0 3v1c0 1 1 2 2 3 2 1 8-1 11-2l8-2h0c-1 1-3 1-4 2l-13 5h-1v-1z" class="D"></path><path d="M119 52h-1c-2 0-3-1-5-1v-1-1c1 0 2 1 3 1h1s1 0 2 1h1s1 1 2 1h0v-1c-1 0 0 0-1-1h-1-1 0l-1-1h-1c0 1-2 0-2 0l-1-1h3c3 1 4 2 7 3 2 1 4 3 6 4h-1l1 1s0 1 1 1c-4-2-8-4-12-5h0z" class="M"></path><path d="M244 418h1c-1 2-1 3-1 4h1v-2h1v8-2c-2 0-1 0-1 1-1 0-1 1-2 1 0 2 0 3-1 5v-7c0-2-1-4 0-5h1l1-3z" class="G"></path><path d="M107 96c-3 0-4 2-7 3h-1c0 1-1 1-1 1h-2c-1 1-4 2-5 2h-2-1c-1 1-4 1-6 2h-1-1c-3 0-6 1-8 0 1-1 6 0 8 0 6-1 12-3 18-5 2-1 5-2 7-3 3-2 5-5 8-7-1 3-3 4-5 6-1 1 0 1-1 1z" class="Z"></path><path d="M33 461l1 1c1 0 3 0 4 1-1 0-3 0-5 1h0 7v1h-3c-3 0-5 0-8 1h-3c-1 0-3 2-5 2 4-3 8-5 12-7z" class="g"></path><path d="M242 662h0s0 1-1 1c-1 3-1 5-1 7 1 2 2 3 3 3 1 1 2 1 3 2-2 0-3-1-4-2v1c-1 0-2-1-3-2-1-2 0-4 0-7 0-1 1-1 1-2 1-1 1-2 3-3h0c1-1 2-2 3-2s1 0 1-1l1 1c1-1 2-1 2-1 2 0 12-1 13 0-7 0-16-1-21 4v1z" class="H"></path><path d="M175 21c1 0 1-1 1 0 0 0-1 1-1 2-1 1 1 3 1 4h0v1l-1-1v-1h0 0l-1-1v-1c0 1 0 1-1 1 1 1 1 3 2 4h0c1 1 1 2 2 2 1 1 1 1 2 1l2 1c1 0 1 1 2 1l-1 1h1 0-3c-2 0-6-4-7-6h0c-1-1-1-2-1-3 0-2 1-3 3-5z" class="G"></path><path d="M420 464l1-1-1-1c0-1 0-1-1-2h0c0-2 0-4 1-5l1-1c2 1 4 8 4 10h-5z" class="H"></path><path d="M419 544c1-5 1-11-1-16l1-1v1-2-1c1-1 0-4 0-5 1-1 1-2 1-4v5 6-2c1 1 1 2 1 3 1 5 0 12 0 17-1-2-1-4-1-6 0 2 0 4-1 5h0z" class="R"></path><path d="M330 59v-1 1l-1-1 1-1h-1 1l2-2h0c1 0 2-1 3-2h1c0-1 1-1 1-1l1-1c1 0 1 0 2-1h0c4-2 6-6 9-8h0 0c-2 3-5 7-7 8s-3 2-4 3c0 1-1 1-2 2-1 0-1 1-2 1s-1 1-1 1c1-1 4-2 5-3 1 0 2-1 2-1 1 0 1 0 1-1h2c-2 2-5 3-8 5-2 0-3 2-5 2z" class="H"></path><path d="M224 77h0c-3-1-7 0-11 0h1c2 0 4-1 6-1h1c2-1 3 0 5 0 0-1 1-1 2-1-1 0-1 0-1-1h0c-2-1-8 0-10 0l-1 1-13 1c10-3 20-2 29-2h-2v1h2 0c-1 0-3 0-4 1h2 1 0c-2 1-4 1-6 1h-1z" class="R"></path><defs><linearGradient id="A" x1="79.774" y1="493.01" x2="70.08" y2="492.867" xlink:href="#B"><stop offset="0" stop-color="#c1c2bf"></stop><stop offset="1" stop-color="#ebe8ed"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M81 501c-3-3-5-7-8-10l-3-3-4-3h0c2 0 3 1 4 2 0-1-1-2-1-2 2 2 5 4 7 5h0c2 3 3 6 5 8v1 2z"></path><path d="M133 604c2 1 2 4 3 6 1 1 2 3 2 4v7l1 1s0 1 1 1h0c0 2 0 3-1 4h0v3h0c-1 2 0 4-1 5h0v-7-2c-1-2 0-3 0-4-1-1-1-2-1-4 0 0-1-1-1-2-1 0 0-1 0-1-1-2-1-3-1-5-1-1 0-3-1-5-1 0-1-1-1-1z" class="P"></path><defs><linearGradient id="C" x1="244.73" y1="492.831" x2="243.781" y2="496.309" xlink:href="#B"><stop offset="0" stop-color="#585556"></stop><stop offset="1" stop-color="#6f7070"></stop></linearGradient></defs><path fill="url(#C)" d="M229 496v-1c6-2 14-2 20-2l1 1 2 1h1v1h-3 0l-1-1h-7c-2 0-6 0-8 1h-3-2z"></path><path d="M60 322c0-1 1-2 2-3v-1-1h0c3-3 5-8 7-12 1-1 2-2 2-4 1-1 2-5 3-6v10c-1-1-1-1-1-2h0v-4c0 2-1 3-2 4 0 1-1 3-1 4h0c-2 6-6 12-10 15z" class="Z"></path><path d="M113 89c1-1 2-3 2-4h2c-4 8-12 15-20 18-5 3-11 5-17 5-1 0-2 0-3-1h0c3 0 5 0 8-1h2 2l1-1h2s1 0 1-1h1s0-1 1-1h0v-1h1l1-1v1l1-1h0c1 0 2 0 3-1l1 1c1-1 5-4 6-5h-1c1 0 0 0 1-1 2-2 4-3 5-6z" class="R"></path><path d="M109 67c4 1 6 3 8 7 4 6 3 12 1 19l-1 2h0-1c1-1 1-3 1-4 1-4 1-7 1-11l-1 5h-2c2-3 2-6 2-10 0-1-1-1-1-2-1-2-2-4-5-5-1 0-1 0-2-1h0z" class="f"></path><path d="M227 49c2-5 5-8 9-10h0 1l-2 2v-1h-1v1s-1 0-1 1c-1 0-2 1-3 2h0v1 1l-1 1v1h0c-1 1-1 1-1 2s0 2-1 3c0 0-1 0-1 1 1 0 1 1 1 2v2h0c1 1 1 2 1 2h1v1h0v1l3 3v-1-1h0c2 0 4 3 6 4l-1-2h0c2 2 4 3 6 4v1c-3-1-6-2-9-4-1 0-2-1-3-2-3-3-5-6-5-11 0-1 1-3 1-4z" class="D"></path><path d="M388 659v-1s-1 0-1-1l3-3 4 3c0 2 1 3 0 4s-2 1-3 2c-1 0-2-1-2-1-1-1-1-2-1-3z" class="G"></path><path d="M390 656h1l1 1v1h-1l-1-1v-1z" class="P"></path><path d="M234 496c2-1 6-1 8-1 2 1 3 1 5 1l3 1 1 2c-1 0-1 0-1 1l-4-2c-3-1-6-1-10-1-2 0-3 1-5 1h0v-1l-5 1c1-1 2-1 3-2h2 3z" class="a"></path><path d="M234 496c2-1 6-1 8-1 2 1 3 1 5 1h-9-4z" class="U"></path><path d="M250 205l10 10-3 3c-1-1-1-2-2-2-1 1 0 2-1 4v-1h-1c0-5-2-9-3-14z" class="S"></path><path d="M52 316h0c-3 1-8 1-11 0-2-1-5-3-6-5s-1-6-1-8c1-2 2-3 4-4 1 0 2 0 4 1h0-2c-1 0-3 1-4 2-1 2-1 4-1 7 1 2 2 4 4 5 3 2 10 1 13 0v1 1z" class="D"></path><path d="M106 615v-1h0c8 7 13 17 14 28v3 6h-1v-2c0-3-1-6-2-9v-1c1-2 1-3 0-5-1-4-2-8-5-12l-1-1c0-1-1-2-2-3l-3-3z" class="W"></path><path d="M59 259c1 0 2 0 3 1-1 1-2 2-2 3v3c-1 0-1 1-1 2h0c0 2-2 6-3 8 0 2-2 3-3 4v-1h0c3-3 4-7 5-10-3 0-5 3-8 4 1-1 2-3 3-4 2-2 4-3 5-5 0-1 2-2 2-3-1 0-1 0-2 1h-1 0c-1 0-2 1-3 0 1 0 2 0 3-1h0 1c0-1 1-1 1-2z" class="M"></path><path d="M389 417c1-3 1-8 2-11 0-3 1-7 0-9 0-1-2-3-2-4h0c1 1 3 3 4 5 1 1 1 3 1 4 0 2-1 5 0 6l-3 8-2 1h0z" class="W"></path><path d="M390 32h4c4 0 9 2 12 5 1 1 2 3 2 4h0-1c-1-4-4-5-7-7h-1c-2-1-4-1-6-1-5 1-10 1-15 3l-3 1h-2c-1 1-3 1-4 2-4 1-8 4-12 6h-1c1-1 1-1 2-1 2-2 5-4 8-5 2-1 4-2 5-2 6-2 13-4 19-5z" class="G"></path><path d="M310 673h1l2 2 1 1 1 1h0c0 1 1 1 1 1v1h1v1s0 1 1 1c-1 1-1 1 0 2v4 2 4h0-1c0-2-1-4-1-6l-3-6c0-1-1-2-1-3s-1-3-2-4h0v-1zm68-627h3c5-1 12-5 17-1 1 1 2 3 3 5v1c-1 0 0 1 0 2h-1c-2-1-3-4-4-6-2 0-4-1-5-1h-1l-1-1v1h-3s-1-1-2 0c-1 0-4 1-6 1v-1z" class="H"></path><path d="M98 115h2l-3 3c-1 1-2 1-4 2h1 0 0c-5 3-13 3-19 3 2 0 3-1 4-1h1c5-1 10-4 15-6 1 0 2-1 3-1z" class="M"></path><path d="M434 48l-1-2c1-1 1-1 1-2-1-1-1-6-1-7s0-2-1-3v-1c1 0 0-2 0-3s0-3-1-4c0-1-1-4-2-6l-1 1c0-4-4-10-2-14l1-2c0 2 0 5 1 8 0 2 2 4 2 6h0v1c1 3 2 5 2 7 1 3 1 6 2 8v2c1 1 0 3 1 4 0 2 0 5-1 7z" class="H"></path><path d="M83 179h1l4-3c2-2 5-6 7-7 0 2-3 4-4 5 0 1 0 2-1 2-1 1-1 1-1 2h1 0l-4 4h0l-2 2-5 3-1-1c1-1 3-1 4-2l3-3h0-2 0c-1 1-2 2-4 3s-3 1-5 2c-1 0-2 1-4 2-1 0-1 0-1-1l5-2c3-1 6-4 9-6z" class="M"></path><path d="M228 36v1c2-1 3-3 5-4s3-3 6-3c2-1 5-2 8-3h2s1 0 1 1h1c-1 1-3 1-4 2-1-1-1-1-2-1v1c0-1-1-1-1-1h-1c0 1 0 1-1 1-5 1-11 4-13 9-2 2-4 6-3 9 0 1 0 1 1 1 0 1-1 3-1 4-2-3-1-10-1-13 1-1 1-2 2-4h1z" class="H"></path><path d="M82 199c2 0 3-1 5-1h0 1 1c-2 2-4 4-7 5h0c-1 2-4 3-6 3h0c1-1 3-2 5-3h-1c-2 0-3 1-4 0h-1-2 0c-4-1-8 0-12-1 4 0 9 0 12-1h4 0c0-1 1-1 2-1s2-1 3-1z" class="M"></path><path d="M82 199c2 0 3-1 5-1h0 1 1c-2 2-4 4-7 5h0l1-1v-1 1h-1l1-1c-2-1-5 0-6 0h0 0c0-1 1-1 2-1s2-1 3-1z" class="G"></path><path d="M70 623c3 0 7-1 10 0h3c4 2 10 2 14 5l-15-2h-1v1h-1v1c-2 0-5 0-6-1h-3-3l1-1c1 0 4 0 5 1 2 0 4 0 6-1h-2v-1h-2c-1 1-2 1-3 1 2-2 6-1 8-1v-1h-1c-2-1-6 0-8 0l-2-1z" class="M"></path><path d="M43 542c-1 1-3 0-4 1-1 0-2 0-3 1h-1-1c-1 1-2 1-3 0h0 2l1-1h0 1 1 0c1-1 1-1 2-1s1 0 1-1h5c1-1 3 0 5 0v-1c1 0 4 0 5 1h4c-2 0-3-1-4-1h-1c-1-1-2-1-3 0h-6 0c0-1 11-2 12-1h1 2c0 1 1 0 2 0 1 1 2 0 2 1h2c1 0 2 1 3 1 0 0 1 0 2 1 1 0 1 0 2 1h1c0 1-1 0 0 0l1 1h1c1 1 1 2 2 2-4-1-8-5-13-6h-4l4 1-3 1c-6-1-12-1-18 0z" class="H"></path><path d="M252 232c0-3 0-7-1-10 0-4-3-15-2-17h0 1c1 5 3 9 3 14l1 8-1 2c0 1 0 2-1 3z" class="e"></path><path d="M80 61v-1-1-1h1v-1c0-1 3-4 4-5h0c-1 1-2 2-2 3v1h1-1v1h0c1-1 2-3 3-5s5-4 7-5c-2 2-4 3-5 5-2 3-4 8-5 12 0 1 0 1-1 2v-3l-2 1c0-1-1-1-1-1l-1 1c-1-1-1-2-1-3-1-2 0-3 0-4 1 1 1 1 1 2s1 3 2 3v-1z" class="G"></path><path d="M70 397h0c2 1 1 2 1 3v1 3s0 1-1 1v1 1c-1 0 0 1-1 1v2c-1 1-2 4-2 5s0 1-1 1c-1 2-1 3-2 4h0l1-1v-1-1l1-1v-1-2-5c0 1-1 1-1 2v1h0l-1-1c1 0 1 0 1-1v-1c1-2 1-4 2-5v-1l1-1c0-1 1-2 2-4z" class="R"></path><path d="M347 689c-4-6-8-12-14-17 1 0 1 0 1-1l1 1c2 3 4 4 6 6h1c1 1 2 3 4 5 1 2 2 3 3 5 1 1 1 3 2 5 3 4 7 7 10 11h0c-2-1-4-3-6-5l-2-3c-1-2-5-6-6-7z" class="G"></path><path d="M318 307c0-1-1-2 0-3h1v-1h0c1-1 0-1 1-2h1l1 2h0c0-2 1-3 0-5v-1c1 2 0 3 1 5h0c0 3 1 5 2 8h0l-2-2v1c0 1 1 3 2 5 1 1 2 3 3 5-1-1-2-2-4-3h0 0c-3-2-4-7-5-11h-1v2z" class="E"></path><path d="M324 316c-3-4-4-9-4-14l3 7h0c0 1 1 3 2 5 1 1 2 3 3 5-1-1-2-2-4-3z" class="N"></path><path d="M78 485c3 7 6 14 8 22 1 3 2 5 3 8-2-3-3-6-5-9-1-2-2-4-3-5v-2-1c-2-2-3-5-5-8h1l1 1h0l-1-4 1-2z" class="W"></path><path d="M133 582c-1 0-1-1-2-1l-1-1h0c-1-1-2-2-3-4-2-2-4-5-6-8-1-1-2-2-2-3s-1-2-2-4c-2-3-3-6-4-9l13 18c0 3 1 4 3 6 1 1 3 4 4 6zm261-180v2c1 0 1-1 1-1l-1-1 1-1c0-1 1-3 1-4s-2-5-3-6l-1-1h1c2 2 4 5 4 8l1 2v-5c1-3 1-7-1-10-2-2-4-3-7-3h-1c-2 0-5 2-6 3h0c0-1 1-2 2-3h2l1-1c2 0 5 0 6 1 3 2 5 4 5 7 2 7-1 13-5 19h0c-1-1 0-4 0-6z" class="R"></path><path d="M37 465h3 5c1 0 2 1 3 1h2c1 0 1 0 2 1 1 0 2 1 3 2 4 2 7 6 11 8l7 8-1 1c-3-1-6-8-9-8l-3-3c-3-3-8-7-13-8v-1h-2c-3-1-8 0-12 0l4-1z" class="Z"></path><path d="M152 593c-4-2-7-6-10-9 2 1 4 3 7 3h0c5 3 13 3 19 4h5c-2 0-6 0-8 1h-4v1h2 0c-1 1-3 0-4 0-1-1-2 0-4 0l-2-1s-1 0-1 1z" class="T"></path><path d="M76 47v1c-1 1-2 3-3 5h0v5h0v1 1c2 2 3 4 4 6h1v-1-1l1-1s1 0 1 1h-1c0 1 0 1 1 1 1 1 1 2 2 2v1c-3 0-4-1-6-2h-2c-2-2-3-4-4-6v-1-1c-1-4 3-8 6-11z" class="Z"></path><path d="M63 361c2-2 3-5 5-8 1-4 2-7 2-11 1 1 1 1 1 3v1c0 4-2 9-4 12-1 2-2 3-3 6-1 4-1 8-2 12 0 6-1 11-3 17h0v-2-3c1-1 1-1 1-2 1-2 0-4 1-6 0-4 0-8 1-11 0-2 1-6 1-8zm325 292c0-1 2-1 2-1 1 0 3 1 4 2 2 1 5 4 6 6 0 2 0 5-1 6-1 2-4 4-6 5h-2c1-1 2-1 3-1l4-4c1-2 1-3 1-5 0-1 0-1-1-1 0-1 0-2-1-2-1-1-2-2-3-2h0v1l-4-3-3 3c0 1 1 1 1 1v1c-1 1-1 2-1 3s1 2 2 3 1 1 3 1 2 0 3-2l1-1 1 1-2 2-2 1h-3l-1-1c-1 0-2-1-2-2h-1c-1-2 0-5 0-7 0-1 0-1-1-1h0c1-1 1 0 2 0l1-1h0c-1 0-2-1-4 0h-1c-1 0-2 1-3 1v-1h2c2-1 4-1 6-1l1-1h-1z" class="M"></path><defs><linearGradient id="D" x1="463.959" y1="401.967" x2="452.965" y2="404.12" xlink:href="#B"><stop offset="0" stop-color="#b1afb0"></stop><stop offset="1" stop-color="#d1d0d1"></stop></linearGradient></defs><path fill="url(#D)" d="M454 403c-1-1-3-1-4-2h2v-2h0-2v-1h-1 0-3-2c-1 0-1 0-1 1h-1-2 2 0c-1 1-4 1-5 1h-1c1-1 1 0 2-1h1c2-1 3-1 5-2 5 0 9 1 13 3s8 5 11 9l-1 1v-1c-1-1-3-1-4-1-1-2-4-3-6-4h0c-1-1-1-2-2-2h-1v1z"></path><path d="M390 487c1 1 2 1 3 3 1 1 1 2 1 3 0 3 1 5 1 8 0 1 0 3 1 4 0 1 0 2 1 3 0 1 2 3 2 5 1 2 3 5 3 7h-1v-1c-1-2-2-4-3-5 0-2-2-4-3-6-1-6-3-11-5-16-1-2-1-3 0-5z" class="D"></path><path d="M234 8h0c2 0 5 0 8 1 4 1 7 1 11 4h-1s-1 0-1-1l-4-2h-2 0l3 2c1 0 1 0 2 1h-1l1 1h0l3 3h0 0c-5-5-10-6-17-6-2 0-3 0-5 2v1l2 2h1c1 1 1 1 1 2-1 1-1 1-2 1-2-1-3-2-4-3h0l5 2s-4-3-4-4h-1c0-1 0-2-1-2 0-1 0-2 1-3h0v1c-1 1-1 1 0 2 0-1 1-2 2-2h1c0-1 1 0 2 0 3-1 6-1 8-1h-1-1c-1 0-1 0-1-1h-6 1z" class="R"></path><path d="M247 403l1-1c1-1 1-2 1-2 1-2 2-3 4-4-1 1-2 3-1 4l-5 12c-1 1-2 3-2 4l-1 2-1 3h-1c1-7 2-12 5-18z" class="S"></path><path d="M218 664c3-1 6-1 8-2 2 0 3-1 4 0-2 1-4 2-6 4 0 1-2 3-3 3-2 1-2 1-4 3h0v-1h-1c1-1 1-2 2-2h0v1c1 0 1-2 2-2h1l1-1h-1-2v1c-1 0-2 1-3 1l-1 1c0-1 0-1 1-2l1-1c-1 1-1 0-1 1h-1l-1 1c-1 0-1 0-2 1-2 0-5 2-7 3l4-4h1v1c1-1 2-1 3-2l-1-1c3-1 4-2 6-3zM335 60l32-11c4-1 7-2 11-3v1l-43 14c-3 1-6 2-9 2l-9 3h-4l9-3c2 0 5-2 6-2l1 1c2-1 5-1 6-2z" class="R"></path><path d="M138 606c5 10 6 23 3 35-1 5-4 10-7 15 1-3 2-5 3-7 0-1 1-2 1-3s1-1 1-2v-1l1-2v-1l1 1v-2c0-1 1-1 1-2v-1-1-2-1h0c1-2 1-7 0-8v-4c-1 0-1 0-2 1v2c-1 0-1-1-1-1l-1-1v-7l1-1c-1-3-1-5-1-7z" class="D"></path><path d="M139 613v9l-1-1v-7l1-1z" class="W"></path><path d="M66 477l6 6c-1-2-2-4-3-5v-1l4 4c0-2-1-3-2-4l1-1c2 3 5 7 6 9l-1 2 1 4h0l-1-1h-1 0c-2-1-5-3-7-5 0-1-1-2-1-2l-5-5c3 0 6 7 9 8l1-1-7-8z" class="P"></path><path d="M229 665h1c0 1 0 1-1 1v1c-1 1 1 3 0 5 0 1 0 1-1 2v1 1c-1 1 0 5 0 6s1 2 1 3h1c1 4 5 5 8 8h1 3-1c-1 0-1-1-1-1-2 0-3-1-4-2h-1 0c-2-1-2-2-3-3s-1-1-1-2c1 2 2 3 4 4 3 2 7 3 10 5-3 0-8 0-11-2-4-3-7-7-8-11-2-5 1-12 3-16z" class="R"></path><path d="M391 484c1-1 3-2 5-3 8 2 14 7 17 14l2 4h0l-2-4c0-1-1-2-2-3l-2-2v-1l-1-1h-1 0 0c-1-1-2-2-3-2l-1-1h0 0v1h0 0c-1 0-1-1-2-1 1 1 2 1 3 2v2h0v1h0l-1-1c-1-1-2-2-3-2v1l3 3v1c-1-2-4-5-6-5h-1-2c0-1-1-1-1-1h-1l-1-1v-1z" class="G"></path><path d="M233 660c1-1 2-1 3-1v1h0c-2 1-4 3-6 5h-1l3-3h-2c-1-1-2 0-4 0-2 1-5 1-8 2h-4l1 1h-1c-1 0-1 0-1 1-3 1-6 1-9 2-1 1-2 1-4 1v-1c-2 0-5 1-7 2-1 1-2 1-3 2l-6 3-1-1c10-5 19-8 30-11h6 2c1-1 2-1 3-1h1c1 0 1 0 1-1h2 3c1 0 1-1 2-1z" class="Z"></path><path d="M39 194c-4-1-13-6-15-9h0c4 2 8 4 13 6 2 0 3 0 5 1h4c1 2 12 2 15 2-2 1-4 1-6 1-1-1-2 0-3-1h-5-1l1 1c2 1 3 1 4 1l1 1h2c1 0 7 0 7 1-3 1-14-2-17-2-2-1-3-2-5-2zm4 348c6-1 12-1 18 0 2 1 6 1 7 3v1h1c1 1 2 1 3 2-2-1-4-2-6-2l2 1h-1c-8-3-19-4-28-2h-1 2c1-1 1-1 1 0 1-1 2-1 3-1h2 0l2-2h-2-3z" class="P"></path><path d="M381 641h0c1 0 16 0 19 1h0c2 0 3 1 4 1s3 1 4 1l1 1c1 0 1 1 2 1 1 1 2 1 3 2 0 0 1 1 2 1h0l2 2c5 3 9 9 11 14v1c-1-1-1-2-2-3v-1h0l-2-2h0c0-1-1-2-2-2-1-2-3-4-4-5-3-2-5-4-7-5-1 0-1-1-1-1-2 0-4-1-5-2-1 0-3-1-4-1-1-1-2-1-2-1h-1c-1 0-3 0-4-1h-2-5c-4 0-7 1-11 1h0 1 1 1v-1h-3v-1h4z" class="M"></path><path d="M94 578c1 0 3 2 4 2 1 1 1 1 2 1l6 4c1 0 2 1 2 2 2 1 5 3 6 5 0 1 1 2 2 3h0l1 1c0 1 1 2 2 2l-1 1s0 1 1 1h0v1l-3-3h0 0l-1 1h0v-1l-2-2c-1-2-2-3-3-4-3-2-6-5-9-7-1 0-2-1-3-2h0 1c1 0 1 1 2 1 1 1 3 2 4 2l-2-1c0-1 0-1-1-1s-1-1-2-2c0 0-1 0-2-1-1 0-3-1-4-3zM76 47c1-2 3-3 6-5h0v1c-2 2-5 3-6 6-1 1-2 2-2 3 0 0 1 0 1-1l1 1h0c-1 1-1 2-1 3 2-2 3-4 5-5h0c1 0 1 0 1 1h0c-1 1-1 1-1 2s0 3-1 4v1h0v1c0-2 1-3 2-4v2c-1 0-1 1-1 1-1 1-1 1-1 2l1 1h0v1c-1 0-2-2-2-3s0-1-1-2c0 1-1 2 0 4 0 1 0 2 1 3v1 1h-1c-1-2-2-4-4-6v-1-1h0v-5h0c1-2 2-4 3-5v-1z" class="H"></path><path d="M78 65h0c-2-2-4-6-4-8h1 0c1-1 1-2 2-2v2c0 1-1 2 0 4 0 1 0 2 1 3v1z" class="D"></path><path d="M70 307l1 1-3 7c-5 8-12 14-21 17-4 1-9 0-13-2-2-1-4-3-5-6s0-5 1-7l1-2h1c-2 3-4 5-3 8s4 5 6 7c4 2 9 1 13 0 3-1 6-3 9-5l3-3c4-3 8-9 10-15z" class="f"></path><path d="M235 374c1-1 1-1 1-2 1 0 2-1 2-1 2-2 3-4 5-5l1 1h0c-2 2-3 3-5 6-1 1-1 3-2 5 0 2-1 4-1 5-1 2-4 8-3 9l-1 3h0c-1-7 0-15 3-21z" class="L"></path><path d="M107 623c-3-2-6-3-9-5-7-2-13-4-21-5h1c5 0 9 1 13 2h0c5 1 9 3 13 5h0c0-1-2-2-3-2v-1h1s1 0 1 1c1 1 3 1 4 2h0 1v-1l-4-4h1l3 4h1l-3-3v-1l3 3c1 1 2 2 2 3l1 1c3 4 4 8 5 12-1 0-2-2-2-3-1-1-2-3-3-4-1-2-3-3-4-4h-1z" class="H"></path><path d="M79 547l1 1h2l-2-2c-1-1-2-2-3-2-2-2-5-4-7-6-2-1-4-3-6-5-1-1-3-2-4-3l-4-4v-1h1 0c2 0 2 2 4 2-2-1-4-3-5-4v-1h0v-1c1 1 2 1 2 2h0v1l4 4c4 2 6 6 10 7 1 1 2 2 3 4-1 0-2 0-2-1l-2-1h0c0 1 1 2 1 2 1 1 1 1 2 1 1 1 1 1 2 1 0 1 1 2 1 2 2 1 8 6 8 8h0c-1 0-2-1-2-2-2 0-3 0-4-2z" class="M"></path><defs><linearGradient id="E" x1="69.505" y1="356.086" x2="50.972" y2="358.508" xlink:href="#B"><stop offset="0" stop-color="#b7b6b7"></stop><stop offset="1" stop-color="#f2f0ef"></stop></linearGradient></defs><path fill="url(#E)" d="M63 361c-4 5-9 7-14 9l4-4c5-4 11-9 13-16 2-3 3-7 4-11h1l-1 3c0 4-1 7-2 11-2 3-3 6-5 8z"></path><path d="M78 258l1-2v-1h1l-6 22c-2 6-2 12-4 18-2 7-7 15-13 19-1 0-3 1-5 2v-1-1c1 0 1 0 2-1 9-4 14-12 16-21 1-3 1-7 2-10 1-8 3-16 6-24z" class="W"></path><path d="M84 12c7 0 14 0 21 1 2 0 5 0 7 1h2c-3 0-6 0-9-1h-7-5-2 1l-2-1c-1 2-5 0-6 1v1h1l-1 1h6v-1h3c1 1 4 1 6 1 3 1 6 1 9 2l3 1c-3 0-5-1-8-2-10-1-19-1-29-1-5 1-10 1-14 2l-2 1-2 1v-1h1v-1c3-1 6-1 8-2 7-1 13-2 19-3zm165 639c4 0 7 1 10 2 2 0 5 0 6 1l-6 1h0 2c-2 0-6 0-8 1-3 0-6 0-9 1l1-1h2-1-2-6-2c-2 0-3 0-5-1h-1 2v-1h-2c-2-1-5 0-6-1h3 2 1l1 1s2-1 3 0l-1 1h3c1 0 6 1 7 0h2 0-1l1-1h0c-1-3-1-1-2-2v-1c1 1 3 0 4 1h0 1 2l-1-1z" class="P"></path><path d="M108 74l-1-1c-1 0-3 1-5 1h0c1-1 3-2 5-2v-1c-1 0-2 0-3-1h0 3c1-1 1-2 2-2h-1c-1 0-1-1-1-1h-6 0l2-1h1c2-1 4 0 5 1h0c1 1 1 1 2 1 3 1 4 3 5 5 0 1 1 1 1 2h-1c0-1-1-1-1-2 0 0 0-1-1-1 0-1-1-2-2-2h0c1 2 1 3 2 5 1 3-1 8-3 11 0 0 0 1-1 1h0v-1c2-2 2-6 1-8 0-1 0-2-1-3h-2c-2 0-3 1-5 1l5-2z" class="M"></path><path d="M108 74l1-1v-1h-1c0-1 1-1 1-1 0-1 0-1-1-2h1c2 1 2 2 3 3l-1 1v-1l-1 1c0 1-1 1-2 2-2 0-3 1-5 1l5-2z" class="R"></path><path d="M326 659h1 1c0-1 1-1 1-1h1 0 3v-1c-1 0-3 1-3 0h0l1-1c1 1 2 0 3 0 8 3 19 0 26-1l8-2c1 0 2 0 4-1 1-1 2-1 3-1 3-1 5-1 8-2 2 0 9 0 10 1h0-7c-8 1-15 4-23 5-4 1-8 1-12 2s-9 1-13 1c-1 0-3 0-4 1h-6v1c3 0 4 0 6 1-3 0-6-1-8-2z" class="G"></path><path d="M182 696c2 0 4 1 6 2h0c-1 2-4 2-6 3-3 0-4 2-6 4v4c0 2 1 3 0 4h-1c-1-1-2-2-2-3-1-1-2-2-1-4 0-3 2-6 5-8h0c2-1 3-1 5-2zm52-630c3 2 6 3 9 4h0c3 1 5 1 8 1h15c2-1 4 0 6-1l7-3h0l1 1-3 1v1c-2 1-2 1-4 1h0-3c-1 1-2 0-3 0s-1 1-2 1h-4-5-1-6c-1-1-2-1-3-1h-2c-1-1-2-1-3-1v1h0c-1 1-4 0-6 0h-1-2 0c-4 1-8 0-11 0-10 0-21 2-30 5v-1h1s1 0 2-1h2c1 0 2 0 2-1h4c-1-1-2-1-3-1h0 0c1 0 2-1 4-1h2 4 0c2 1 3 1 5 0h3 2 4c1-1 3 0 4 0h1 12c-2-1-4-1-6-3h0 0 1c1 0 2 1 3 1 0 0-2-1-3-1 0 0-1-1-1-2z" class="Z"></path><path d="M335 672h1c-1-1-3-2-4-3h1l-1-1h0-1 0l-1-1h0l-1-1v1h0-1s-1-1-2-1l-2-2v-1h1v-1h0 0 2l2 1h1 0c1 0 1-1 2 0 0 0 2 0 2 1h1v1h0c2 0 3 1 5 2l8 4c2 1 4 2 6 2 1 1 3 2 4 2 2 1 3 1 5 2l-1 1c-1-1-3-1-4-2-4-1-8-3-11-5-2-1-5-2-7-3-3-1-6-3-10-4l-2-1-1 1c0-1 0-1-1-1l11 8 10 9c1 1 4 3 5 4v1l-3-3c-2-1-4-3-6-5l-1 1h-1c-2-2-4-3-6-6z" class="M"></path><path d="M260 669c3-1 7 0 10 0 9 2 18 8 23 16 1 0 1 1 1 2v-1l-9-9c-1 0-2-2-3-2-1-1-2 0-2-1h-1c1 2 3 5 5 7 1 2 3 4 4 6 0 2 0 3 1 4v2c-1-1-1-2-1-4-1-2-2-3-3-5s-5-8-8-9l-2-1h-1s-1 0-1-1c-2 0-3 0-4-1h0 2c1 0 3 1 4 1s1 1 2 1h0 1 1c-1-1-1-1-2-1l1-1h-1-2l1-1h-1-2v-1h-2-1s0-1-1-1-2 1-3 0h-4c-3 1-3 2-5 4-1-1-1-1-2 0h-1c-1 0-1 1-2 1s-1 0-1 1l1 1v-1c2 1 2 2 4 2h0c1 0 1 1 1 1h-2c-1-1-2-1-2-1-1 0-3-1-3-2v-2c2-3 7-4 10-4z" class="D"></path><defs><linearGradient id="F" x1="227.191" y1="255.106" x2="241.335" y2="250.061" xlink:href="#B"><stop offset="0" stop-color="#111"></stop><stop offset="1" stop-color="#3c3c3c"></stop></linearGradient></defs><path fill="url(#F)" d="M230 251v-1c2-1 2-2 3-3 2-2 4-3 6-4 1 1 1 1 1 2-1 0-1 1-2 1-1 1-2 2-2 3s0 3-1 4c0 2-3 7-4 9h-1c-1 0-1 0-1-1h-1c-2-3 1-5 1-8 0-1 0-1 1-2z"></path><path d="M230 251h0c1 0 1-1 2-1h0c1 4-3 7-3 11h-1c-2-3 1-5 1-8 0-1 0-1 1-2zm23 405c2-1 6-1 8-1 4 1 9 2 12 3 6 4 12 8 17 13 0 1 1 2 2 3h0-1c0 1 1 2 1 2l4 6c2 2 4 5 6 6l1 1v4h-1v-4c-1-1-3-2-4-3-2-2-4-5-5-7-7-11-15-17-27-20h1 2c1 1 2 1 3 1h1v1h2c0 1 0 1 1 1v-1h0c0-1-1-1-2-1 0-1-1-1-1-1h-1c-1 0-1-1-2-1s-2 0-3-1h-1c-4-1-9-1-13-1z" class="G"></path><path d="M64 541l-4-1h4c5 1 9 5 13 6 1 0 1 1 2 1h0c1 2 2 2 4 2 0 1 1 2 2 2 1 2 2 3 3 4 1 2 3 3 5 4l-1 1s-3-2-3-3c-4-2-8-5-13-7-1-1-3-2-4-2-1-1-2-1-3-2h-1v-1c-1-2-5-2-7-3l3-1z" class="W"></path><path d="M64 541c5 1 10 4 14 7 1 1 3 2 4 3h0c-2 0-4-2-6-3-3-1-5-3-8-3-1-2-5-2-7-3l3-1z" class="M"></path><path d="M66 253c3-1 5-2 7-4 0 1-1 3-1 4-2 4-4 7-6 11-1 6-2 12-4 17 0-5 1-11 3-16 0-2 2-4 2-7-2 3-4 6-5 9v1c-1-1 0-2 0-4-1 1-2 3-3 4h0c0-1 0-2 1-2v-3c0-1 1-2 2-3 1 0 2-4 3-5 0-1 1-1 1-2z" class="P"></path><path d="M153 707c0-1 1-1 1-2 1-4 5-10 8-13l-2 6c0 1 0 2 1 2v1 5l4 4v1c1 1 4 1 4 2v1c-5-1-7-6-11-9 1 4 3 7 5 10 1 1 3 3 3 4h-1c0-1 0-1-1-1-3-3-7-7-8-11v-1l-3 1z" class="G"></path><path d="M292 674l2 2h1c-1-1-2-3-3-4h1c3 3 9 12 13 13h0c2 0 2 0 3 1 2 2 3 3 4 6h0l-3-3h0c-2 0-4-1-6-1l-1 1-1-1c-2-1-4-4-6-6l-4-6s-1-1-1-2h1 0z" class="M"></path><path d="M67 547h1l-2-1c2 0 4 1 6 2 1 0 3 1 4 2 5 2 9 5 13 7 0 1 3 3 3 3 4 3 8 5 11 9l5 5s1 1 2 1c1 2 4 4 5 6h-1c-1-1-2-3-3-3-3-3-5-6-9-9v1c-2-1-4-3-5-4l-8-6c-7-5-14-9-22-13z" class="G"></path><path d="M378 23c7-1 13 0 19 1 1 1 3 1 3 1 1 1 2 2 2 3h1c1 0 2 2 3 3 0 1 0 1 1 2h0v1l-4-4c-2 0-3-1-5-2-6-2-15-5-22-4l-4 1c-7 2-12 5-17 11-2 2-3 4-5 6l-1-1 1-1 3-3c1-1 2-3 3-4 7-6 13-8 22-10z" class="Z"></path><path d="M164 21h3c-1 0-1 1-2 1-1 1-2 3-3 4h0v1s0 1-1 2c0 1 1 2 0 4 1 3 3 7 5 10h0l-1-1h0l-1 1h0c-3-2-4-6-6-8v-1-1l-4-6h0l2 2s1 0 1 1h0 0c-1-1-2-3-3-4 0 0 0-1-1-1v-1h1l-1-1h1l2 2c1-1 1-2 1-3 1-3 3-5 5-6h0c-2 2-4 5-4 9 0 1 1 1 1 1 1-2 4-3 5-5z" class="D"></path><path d="M164 21c-1 2-3 4-3 6v3c-1 0-1 1-1 1h0v2h0v-1h-1l-1-1v-1c-1-1-1-2-2-3l1-1 1 2c1-1 1-1 1-2 1-2 4-3 5-5z" class="P"></path><path d="M227 71h1l1-1c-1-1-2-1-3 0-1 0-1-1-1-1h-3-2c-1-1-7-1-8-2-5 0-9-1-13-3h0l2 1h2 2c1 1 1 1 2 1l1-1h0l-1-1h-1c-1-1-2-1-3-1h0v-1c3 1 6 2 10 2 1 1 4 1 5 2 0 0 1-1 2 0h1c1 0 3 1 4 1 1-1 1-1 2-1h1 0c-1-1-2-1-2-2-1 0-1 0-2-1l-1-1c3 2 7 4 11 6h0c2 2 4 2 6 3h-12-1z" class="R"></path><path d="M43 459c3-1 7 0 11 1l-2 1c1 1 3 2 4 3h0-1c-1-1-2-1-3-1h-1c1 1 1 1 2 1l2 2c-1-1-3-2-5-2h1c0 1 1 1 1 1v1s-1-1-2-1h-5-5v-1h-7 0c2-1 4-1 5-1-1-1-3-1-4-1l-1-1c3-1 7-2 10-2z" class="P"></path><path d="M33 461c3-1 7-2 10-2v1l-3 1h1c2 0 3 1 5 2h-8c-1-1-3-1-4-1l-1-1z" class="f"></path><defs><linearGradient id="G" x1="392.827" y1="135.183" x2="397.021" y2="132.378" xlink:href="#B"><stop offset="0" stop-color="#323233"></stop><stop offset="1" stop-color="#535253"></stop></linearGradient></defs><path fill="url(#G)" d="M367 108h1c1 1 2 1 2 2 1 0 1 0 2 1 0 0 1 1 2 1l2 2c1 1 2 1 2 2 1 0 2 1 3 2l1 1h1 0c1 1 2 2 4 3l5 5c2 2 4 5 7 8 3 4 7 8 9 14 1 2 2 3 2 5l-9-12c-9-13-21-24-34-34z"></path><path d="M54 484c3 0 8 1 10 3 2 1 3 2 4 3 2 1 4 3 6 4 1 1 2 3 3 4h1-1l1 1h-1c-5-5-11-10-19-12h-5c-2 0-4 2-5 3v1c-1 1-2 2-2 3 0 2 1 4 3 5h0l2-2v1h0v3h1l-1 1c0-1-1-2-2-2 0 0-1 0-1-1-2-1-4-3-4-5s0-4 1-6c3-3 6-4 9-4zm4 39c1 1 2 2 4 3h1l7 6c0 1 0 1 1 1 2 1 3 3 4 3l-2-3c-1 0-1-1-2-2s-3-4-5-5c0 0 0-1-1-1l-3-3v-1c-1 0-3-2-4-3h0c2 1 4 2 5 3s2 2 3 2c0 1 1 2 2 2 1 2 4 4 5 6s3 4 5 6c1 0 2 0 3 1s1 2 1 3l-1 1 1 2h0c-1 0-1-1-2-1l-1 1s0-1-1-1c0-1-1-1-1-1l-1-1c-1 0-1 0-2-1-1 0-1 0-2-1 0 0-1-1-1-2h0l2 1c0 1 1 1 2 1-1-2-2-3-3-4-4-1-6-5-10-7l-4-4v-1z" class="G"></path><path d="M282 64c2-2 4-4 6-5v1c-2 2-6 5-9 7h0l-7 3c-2 1-4 0-6 1h-15c-3 0-5 0-8-1h0v-1c10 3 23-1 31-5l1 1h2c2-1 3-2 4-3h1v2z" class="W"></path><path d="M282 62v2c-1 1-2 2-4 2h-1c1-1 1-1 2-1l3-3z" class="R"></path><path d="M294 658l2-1c2 0 5 1 6 1h1-1-4c1 1 3 2 4 3 3 2 6 6 9 8 2 1 4 3 6 5s4 5 5 7c4 8 6 16 11 23 1 2 2 3 4 4h2c2 1 4 2 5 4 0 1 1 3 0 3l-3 3h-1-2c-2-1-4-3-5-5h1l2 2c1 1 3 2 4 2s2-1 3-2 1-1 0-2c0-4-7-4-10-6 0-1-1-1-1-2-1 0-1-2-2-3l-6-16c-2-3-2-6-4-8h-1c-1-2-3-4-4-5l-1 1c2 2 4 4 6 7-2-1-3-4-5-4l-1-1c0-1-1-1-1-2h0c-1-1-3-3-4-3v-1h0 1c-1-1-3-2-4-3 0-1-1-2-2-3-3-3-6-5-10-6z" class="D"></path><path d="M303 658s1 1 2 1c1-1 2 0 3 0h0c1-1 3-1 4-1 4 0 9 2 13 3 4 0 7 1 11 1-1 0-2 0-3 1-1-1-3 0-4-1h-1-1c0-1-2 0-3-1h-2-2c0-1-1-1-2-1l-2-1h0-2-2c-1 0-3-1-3 0h0 0c2 2 5 3 7 5 1 0 2 1 3 1 0 0 2 1 2 2h-1c-2-1-5-3-7-4l2 2h0-1l-4-2c0 2 2 3 4 4 1 1 3 5 3 7-2-2-4-4-6-5-3-2-6-6-9-8-1-1-3-2-4-3h4 1z" class="Z"></path><path d="M444 599c3 1 6-5 9-5l-15 15c1-1 2-2 4-2 2-1 5-2 7-3 2 0 5 0 6 1h1s1 0 1 1h0l-2-1c-4-1-10 1-13 3-4 2-6 4-9 7-3 2-6 3-8 5-1 0-3 1-3 2-1-1-1-1-2 0h0v-1c1-1 4-4 6-4 1 0 1-1 2-1 2-2 4-3 6-5 1-1 1-2 1-3 3-4 5-7 9-9z" class="G"></path><path d="M82 626l15 2c7 4 16 8 19 16 0 1 1 2 1 3s1 2 1 3h0l1-1v2c-1 2-1 4-1 5-1 2-1 2-2 3v-11c-1-2-2-5-3-7-4-5-12-8-18-9-2-1-4 0-5-1h1 0l-1-1h0-3c-1-1-3-1-4-1-5-1-10-1-14-1h0c4 0 9-1 12 0 1 1 3 0 4 1 1 0 2-1 3 0h3-1l-1-1v-1h-1-2 0c-1 0-2 0-2-1h-2z" class="D"></path><path d="M117 647c0 1 1 2 1 3h0l1-1v2c-1 2-1 4-1 5h0c-1-2-1-4-1-6v-3z" class="G"></path><path d="M119 52h0c4 1 8 3 12 5 4 4 7 9 10 14 1 2 1 4 2 5h0c0 1 1 1 1 2 1 2 2 5 2 8 1 0 0 1 0 2h0 0v3c0 1 0 2 1 3 0 1-1 2 0 3v4c1 2 0 4 0 6h1v1c-1 1 0 3 0 4-1 1-1 3-1 5 0-2 0-3-1-4 0-5 1-9 1-13-1-9-2-16-5-24-2-5-4-9-9-13-6-6-15-11-24-11-2 0-4 1-5 1h-2-1 0 2c4-3 11-1 16 0 1 1 3 2 5 2h0l-5-3z" class="D"></path><path d="M153 707c-2 1-4 2-5 2 1-1 2-1 3-2s2-1 2-2l-1-1 3-3c1-2 2-3 3-4 0-1 1-2 2-3v-1c1 0 1-1 2-1h0l1-1v-1l1-1c1-1 1-1 1-2 1 0 1-1 2-1-1 2-2 3-3 5v1c-1 0-1 0-1 1-1 1-1 2-1 2h0c0 1 0 2-1 2v2h0l1-2 1-1v-1h0v3c0 1-1 1-1 2 0 0 0 1-1 1v2c1 0 1 1 1 1l3 3c1 2 2 3 3 4 1-1-1-5-1-6 1-3 2-6 4-8 4-4 8-5 12-5v1h1c-3 2-7 3-11 5-2 2-4 3-4 6-1 1-1 2 0 2l2 7v1h-2 0v-1c0-1-3-1-4-2v-1l-4-4v-5-1c-1 0-1-1-1-2l2-6c-3 3-7 9-8 13 0 1-1 1-1 2z" class="M"></path><path d="M352 684l7 5c3 2 7 3 10 4 7 2 14 3 22 1 5-1 14-4 17-9s5-14 4-19v-1h0c2 4 1 8 1 13l-3 12c-1 3-2 6-3 8h-1 0 0c1-1 1-1 1-2 0 0 1 0 1-1h0v-1-1h0c1 0 1-1 1-1v-1l-1 1h0c0-1 0-1 1-2v-2h0l1-1c0-2 1-4 1-6-4 8-7 13-16 16h-1v-1c2 0 3-1 5-2h0c1 0 1 0 2-1 1 0 4-2 4-4-1 0-1 0-1 1-1 0-1 0-2 1h-1v1h-1-1v1h-2 0-1v1h-1 2c-1 1-1 0-1 1h-1c-1 1-1 1-2 1h-1c-2-1-7 0-9 0-12 0-22-3-31-11v-1z" class="G"></path><path d="M118 573c7 10 15 21 20 33 0 2 0 4 1 7l-1 1c0-1-1-3-2-4-1-2-1-5-3-6-1-5-4-9-6-13-2-2-2-4-4-5l3 9h-1c-3-6-4-12-6-18 0-2-1-3-1-4z" class="f"></path><path d="M100 187h1v1c0 1-1 1-2 2l-7 10v1 1l1 1v-1c1-1 2-2 2-3 0 0 0-1 1-1v-1c0-1 2-2 2-3h0c1-1 1-1 1-2s1-2 1-2h1l-1 1v1c0 1-3 4-3 5l-12 21c-3 5-6 8-11 11 3-4 6-9 9-13 0-1 1-2 1-3s0-1 1-1v-1h0c1-1 1-3 2-4h1v-1h-1l-1 1c0-1 3-4 4-5l6-8c1-3 3-5 4-7zm319 357h0c1-1 1-3 1-5 0 2 0 4 1 6-1 13-5 26-12 37-1 1-1 2-2 3l-2 1h0v-2l1-1v-1c6-12 10-25 13-38z" class="D"></path><path d="M391 416v1c1-1 2-3 2-4 2-3 5-5 6-8 1-2 1-3 2-4l1 1c-1 1-1 1-1 2-1 0-1 1-1 1-1 3-4 6-5 8-5 8-8 18-9 27 0 3-1 6 0 10 1 2 3 6 2 8h1v2l2 4v1l-2-2c-1-3-4-7-5-11v-1h0c-1-3-4-19-3-21 1 0 1 2 1 2l1 7c0 2 1 3 1 5 3-9 2-18 5-27h0l2-1z" class="Z"></path><path d="M386 450c1 2 3 6 2 8h1v2l-1-1v-1c-1-1-1-1-1-2s0-1-1-1v-5z" class="H"></path><path d="M425 615c3-3 6-7 9-11 2-2 3-4 5-5 2-4 6-8 10-10h0v1l-3 3c-2 1-4 3-5 4-1 2-5 6-5 7s0 0 1 0c1-2 3-3 4-4 4-5 10-9 16-10h1c2-1 4-1 6-1v1h1c2 0 2 1 3 1v1c-2-1-3-2-6-2-2 0-4 1-6 1-1 1-1 2-2 3h-1c-3 0-6 6-9 5-4 2-6 5-9 9 0 1 0 2-1 3-2 2-4 3-6 5-1 0-1 1-2 1-2 0-5 3-6 4v1h0c1-1 1-1 2 0l-4 1c1-3 6-5 8-8h0-1 0z" class="D"></path><path d="M444 599c4-4 6-7 12-8-1 1-1 2-2 3h-1c-3 0-6 6-9 5z" class="H"></path><defs><linearGradient id="H" x1="392.865" y1="25.18" x2="395.381" y2="11.938" xlink:href="#B"><stop offset="0" stop-color="#d4d3d3"></stop><stop offset="1" stop-color="#f8f8f8"></stop></linearGradient></defs><path fill="url(#H)" d="M411 37h0c0-7-5-13-9-17-6-5-13-7-20-7-2-1-3-1-5-1-1 0-2 0-3 1h-1c-9 4-16 12-21 20h-1c5-9 13-17 22-21 5-1 9-2 14-2 1 0 2 0 3 1h1 1c1 0 1 0 2 1s3 1 5 2c5 4 9 7 11 14 1 3 2 6 2 9h-1z"></path><path d="M219 83c0 1-1 1-1 1-1 0-2 1-3 1-1 1-2 1-2 1-1 0-1 1-2 1h0-2c-1 1-2 2-4 2h-1 0 0l-3 1-1 1v-1h-1-1 0l-2-1h-1c-2 1-5 2-6 3s-2 2-3 1h0l16-7c1-1 4-3 5-3 2 0 4-2 5-3 3-1 7-1 9-2 1-1 2-1 3-1v1h6 0v1h-2-3c0 1-1 1-1 0 0 1-1 1-1 1v1c-1 1-2 1-3 2h-1 0z" class="R"></path><path d="M54 460c5 2 7 6 11 9 2 2 5 4 7 7h0l-1 1c1 1 2 2 2 4l-4-4v1c1 1 2 3 3 5l-6-6c-4-2-7-6-11-8-1-1-2-2-3-2-1-1-1-1-2-1h-2c-1 0-2-1-3-1h5c1 0 2 1 2 1v-1s-1 0-1-1h-1c2 0 4 1 5 2l-2-2c-1 0-1 0-2-1h1c1 0 2 0 3 1h1 0c-1-1-3-2-4-3l2-1z" class="G"></path><path d="M222 275c1 1 1 2 1 4l4 4c2 2 4 3 6 4 3 2 5 5 7 7 1 2 3 3 3 5h0l-3-2h0v2l-3-3c-2-1-4-1-6-2-3-1-6-3-8-6 0-1-1-3 0-4v-1c-1-1-1-4-2-5v-1l1-1v-1z" class="d"></path><path d="M430 19c3 6 4 10 4 16 2-6 1-15 7-18 3-2 5-2 9-2l3 3h-1 0c-2-2-4-3-7-2-2 0-4 1-5 3-5 5-4 13-4 20 0 4 0 9-2 14-1 3-2 6-4 9-3 4-6 8-10 10-2 2-7 5-10 5h0-1c3-2 7-5 11-8 5-4 13-13 14-21 1-2 1-5 1-7-1-1 0-3-1-4v-2c-1-2-1-5-2-8 0-2-1-4-2-7v-1z" class="D"></path><path d="M90 213c1 2 1 3 0 5v1l-4 7-1 5h-1c-3 4-5 9-9 12h0c0-1 1-1 2-2v-1h-1l-2 2v-1c1-1 2-1 2-2s2-2 2-3c-1 1-2 2-4 2h0c0 1 0 1-1 1h0-1c-4 0-7 2-10 2 2-1 5-1 7-3h0-2v-1c4-1 6-5 11-6h0c5-5 9-11 12-18z" class="W"></path><defs><linearGradient id="I" x1="85.535" y1="231.288" x2="81.589" y2="216.709" xlink:href="#B"><stop offset="0" stop-color="#9c9c9c"></stop><stop offset="1" stop-color="#d0cdcd"></stop></linearGradient></defs><path fill="url(#I)" d="M90 213c1 2 1 3 0 5v1l-4 7c-2 3-5 6-7 8h-1c-2 0-4 1-6 2 2-1 5-3 6-5h0c5-5 9-11 12-18z"></path><path d="M414 11l3 3c5 6 7 15 6 22 0 7-4 16-8 21l-6 6c-1 1-2 1-3 2-2 1-3 2-5 2 0 0-1 0-2 1h0l-1-1h0-1 0l2-1c1-1 1-1 2-1s1-1 1-1h1 1s1 0 1-1h1 1v-1c2-1 3-2 4-4 4-5 8-12 9-19s0-17-4-24c0-1-1-3-2-4z" class="H"></path><defs><linearGradient id="J" x1="389.006" y1="447.241" x2="402.5" y2="437.681" xlink:href="#B"><stop offset="0" stop-color="#aba9aa"></stop><stop offset="1" stop-color="#d9d9d9"></stop></linearGradient></defs><path fill="url(#J)" d="M394 456v-1c-2-11-1-22 3-33 1-2 2-5 4-7h0c1 3-1 5-2 7 0 1-1 1-1 2v-1h-1v1h0v1c-1 1-1 2-1 2v2s1 1 2 1v-2-1c1 0 1-1 1-1 0-1 2-3 2-5h-1 0l1-1h0c1-1 1-2 2-2h0c0 1-1 3-2 5 0 1-1 2-1 4h0s-1 0-1 1h0c0 1 0 2-1 3 0 1 1 1 0 2v1c0 1 1 2 0 3l1 1v-1-1-2h0c0-1 0-1 1-2v-1h1 0l1-2h0v-1c1-1 2-3 3-4h0c-5 9-7 19-8 30v-4c-1 2 0 4-1 6h-1-1z"></path><path d="M417 668l1-1c1 1 2 4 3 6h0c1 2 2 7 2 9l-1 7c-1 6-3 12-6 17-4 5-12 9-18 10-1 1-3 1-4 1-1 1-3 0-4 1h-5c-1 0-2 0-3-1h1c6 0 12 0 18-3 7-3 13-9 16-16 3-10 4-21 0-30z" class="H"></path><path d="M260 215l2 2h0l5 5v2c0 1 1 1 1 2l-1 1 2 3h0l-2-1c0-1-1-1-1-1-1 1 0 1 0 2l-2-1c0 1 0 1-1 1h-4 0l-1 1c-1-1-1-1-1-2h-1l-1-1v2l-1-3-1-8h1v1c1-2 0-3 1-4 1 0 1 1 2 2l3-3z" class="F"></path><path d="M253 219h1v1c1-2 0-3 1-4 1 0 1 1 2 2s1 1 2 1h0l-1 1-2 2c0 2 1 5 1 7h-1l-1-1v2l-1-3-1-8z" class="V"></path><path d="M256 229v-1c-1-4-1-7-1-11l1 1c1 1 2 1 3 1l-1 1-2 2c0 2 1 5 1 7h-1z" class="U"></path><path d="M260 215l2 2h0l5 5v2c0 1 1 1 1 2l-1 1c-1-1-3-6-4-7h-2c-1 0-1-1-2-1h0c-1 0-1 0-2-1l3-3z" class="E"></path><path d="M260 215l2 2h0c0 1 0 1 1 2h-2c-1 0-1-1-2-1v1h0c-1 0-1 0-2-1l3-3z" class="J"></path><path d="M260 220c1 0 2 2 3 3h1c1 1 1 2 1 4-1 0-1 0-1 1h-4-1-1l-1 1c1-2 0-5 0-6s1-2 2-2h1v-1z" class="O"></path><path d="M387 599v-1l1-1c1-3 3-6 5-9 2-4 3-7 4-10 1-1 1-3 1-5l1-13c1 0 1 1 1 2s1 7 0 8c0 3-1 6-2 8 0 1-1 3-1 4l-1 1h1c0-1 0-2 1-2v-1-1l1 1h-1v1c0 1-1 1 0 2 1-4 2-7 3-11 1-2 1-4 1-6 1-3 1-8 1-12h1v13l-2 6c0 2-3 10-3 11 3-2 7-10 8-13 2-5 3-10 4-15h0c1 1 0 2 0 3-1 4-1 8-3 12 0 1-1 2-1 3-5 8-10 15-15 22-1 1-3 4-4 4h0l-1-1zm-257 68c2-1 3-2 5-3 3-4 6-8 8-13h0 1l1 1v-1c0 1-1 2 0 2v1s0 1 1 1c-1 2-3 5-3 7v1l1-1v1l-3 3c-1 2-5 5-5 7h0s0 1-1 1l-1-1v1c-4 2-7 4-11 5 0-1 1-1 1-1h1c1-1 3-1 4-2 0-1 1-1 2-1 1-1 2-2 3-2 1-1 2-2 2-3h-2s-1 0-1 1c-1 0-1 1-2 1-1 1-2 1-3 2h-1c-1 0-2 0-2 1-2 0-3 0-4 1l-1-1h2 0v-1c1-1 4-2 5-3-1 0-1 1-2 1h-1 0-1-1c1-1 2-1 3-2 2-1 4-2 5-3z" class="H"></path><path d="M130 667c2-1 3-2 5-3 3-4 6-8 8-13h0 1l1 1v-1c0 1-1 2 0 2v1l-4 6v1c-1 2-3 3-5 5-1 1-2 3-3 4-3 2-7 4-10 4h0c5-3 9-6 14-10 1-1 2-2 2-3l1-1c-3 2-6 7-10 7z" class="G"></path><path d="M311 654c1-1 1-1 2 0 1 0 3-1 3 0 2 0 3 0 4 1l10 1h-6l-1-1v1h0c1 0 1 1 2 2v1h1c2 1 5 2 8 2l25 3c4 1 7 2 11 3-1 0-4-1-5-1-9-2-20-2-29-4-4 0-7-1-11-1-4-1-9-3-13-3-1 0-3 0-4 1h0c-1 0-2-1-3 0-1 0-2-1-2-1h-1c-1 0-4-1-6-1l-2 1-7-3h2c2-1 16 1 17 1h2l-1-1h-3c1 0 3 0 4-1h-1 4z" class="P"></path><path d="M133 124c1-1 1-3 2-4h1c0-1 0-1 1-2l-1-1v1-2h0v-2l1-1v1l1-1h0v2c0-1 0-1 1-2v-2-6c0-5 1-12 0-17 0-1 0-1-1-2-1-2-1-4-2-6-2-3-3-6-5-8v-1c1 1 2 3 3 4 4 6 7 14 8 21 0 7 0 14-1 21 0 2-2 6-1 8h-1v1l-1 2-1 2s0 1-1 1h2 0l-1 1h-1c0-2 1-3 1-5 1-1 1-1 1-2h0c1-1 1-1 1-2l1-1c0-1 0-4 1-5v-4c1-1 0-3 0-5v-9-4-2h-1v1 1 4c-1 3 0 8 0 11v3c-1 0-1 0-1 1v1 1l-1 3v1 1h0v2h-1v1 1c-1 2-2 3-2 5l-1 4c-1 2-2 4-4 6-1 1-3 2-3 4l-1 1-1 1c-1 1-2 3-3 5v-3c1-1 1-2 2-3l2-5h1c0-1 1-3 2-4l4-9c1-1 2-3 2-4l-1 2-1-1z" class="M"></path><path d="M124 145l1 1c2-3 4-6 6-8 1-2 2-3 3-4-1 2-2 4-4 6-1 1-3 2-3 4l-1 1-1 1c-1 1-2 3-3 5v-3c1-1 1-2 2-3z" class="G"></path><path d="M144 122h1c-1 2-2 5-3 7v1l-2 4c-1 2-2 3-3 4h-1v2s0 1-1 1l-17 21s0 1-1 2-1 3-2 4-2 2-3 4-2 4-3 7c-1-2 0-3 1-4v-1c1-1 1-2 1-3h1v-2c-1 0-1 1-1 2h-1v1c0 1 0 1-1 1v1h0c0 1-1 1-1 2l-1 1v-1s1-1 1-2c1-1 4-8 5-8h0c2-3 4-7 5-10 2-3 3-5 4-8v3c1-2 2-4 3-5l1-1v1h0c1-1 1-2 2-2l3-3c1-2 3-3 5-5 1-2 3-5 5-7l3-7z" class="D"></path><path d="M144 122h1c-1 2-2 5-3 7v1l-2 4c-1 2-2 3-3 4h-1v2s0 1-1 1l-17 21c0-1 0-2 1-2v-1c1-2 4-5 5-7 2-3 3-6 6-9 1-2 3-3 5-5s5-6 6-9l3-7z" class="W"></path><defs><linearGradient id="K" x1="116.412" y1="136.874" x2="133.391" y2="134.795" xlink:href="#B"><stop offset="0" stop-color="#a4a2a2"></stop><stop offset="1" stop-color="#cecece"></stop></linearGradient></defs><path fill="url(#K)" d="M118 152l2-8 3-9c2-4 4-8 6-11 5-9 5-19 5-28h0c1 2 1 12 0 14v5h0c0 1-1 1 0 2h-1v2h0 0v3 1c1 1 0 2-1 2l1-1 1 1 1-2c0 1-1 3-2 4l-4 9c-1 1-2 3-2 4h-1l-2 5c-1 1-1 2-2 3-1 3-2 5-4 8v-4z"></path><path d="M134 125l1-2c0 1-1 3-2 4l-4 9c-1 1-2 3-2 4h-1 0l-1 1v1-1c0-1 1-2 1-3s0-2 1-3c0-1 2-3 2-4h0 0v1l1-1h0 1v-1c1-2 2-4 3-5z" class="W"></path><path d="M388 55c0-1 1-2 2-3 1 0 2-1 3 0 1 0 2 1 2 2 1 0 1 1 1 1v2c-1 1 0 3-1 4l-2 2h0c1 0 2-2 4-2 0-1 1-1 1-1 2-2 2-4 2-7h1c0 2 0 5-1 7-2 2-6 5-9 6-6 1-14-2-21-3h-15c3-1 6 0 8 0v-1h-4 8c4 0 8-1 12 0 2 0 4 1 6 1-1-1-2-3-2-5h1c0 2 1 3 2 4h2v-1-1c-1-1-1-3 0-5z" class="Z"></path><path d="M388 55c0-1 1-2 2-3 1 0 2-1 3 0 1 0 2 1 2 2h0c-1 1 0 1 0 1 0 1 0 2-1 3h0l1 1c-1 2-2 3-3 4h-2l-1-1c-1 0-1-1-1-1v-1c-1-1-1-3 0-5z" class="D"></path><path d="M388 55h2l-1 1 1 1v1h-2v2c-1-1-1-3 0-5z" class="G"></path><path d="M388 55c0-1 1-2 2-3 1 0 2-1 3 0 1 0 2 1 2 2h0c-1 1 0 1 0 1 0 1 0 2-1 3h0c0 1-1 2-2 2v-2l-1-1v-1h1l1-1c-1-1-2 0-3 0h-2z" class="H"></path><defs><linearGradient id="L" x1="283.823" y1="661.351" x2="299.793" y2="643.171" xlink:href="#B"><stop offset="0" stop-color="#a4a4a1"></stop><stop offset="1" stop-color="#cac7cc"></stop></linearGradient></defs><path fill="url(#L)" d="M260 650c8 1 15 1 23 1l28 1c2 0 6-1 8-1 1 1 2 0 3 0 5 0 11-2 16-3h3 0c-2 1-5 2-7 2l-2 1c-2 1-2 1-4 1h-1l-1 1h-1-3-12l1 1h-4 1c-1 1-3 1-4 1h3l1 1h-2c-1 0-15-2-17-1h-2c-3-1-7-1-11-2l-17-3h1z"></path><path d="M325 70c9 0 17 3 26 5 5 2 10 4 16 6 7 1 16 1 24 0 6 0 12-2 18-4h1c-1 1-1 1-2 1l-1 1c-1 0-2 0-3 1h-1c-1 1-2 1-2 1-1 0-2 0-3 1s-2 0-3 0-1 0-2 1h-2c-1 1-2 1-3 1h1 0 3c3-1 6 0 8-1 3 0 6-1 8 0h-6c-4 1-7 1-11 2-12 1-23-1-35-4-3-1-7-2-10-3v-1h1c0 1 1 0 2 1h2s1 0 1 1l1-1c-1 0-2-1-3-1-1-1-1-2-2-2h-1-1c0-1 0 0-1-1h-2c-1-1-2-1-2-1-1-1-3-1-4-1h-1l-3-1h-3c-1-1-4-1-5 0h0-5-4v-1h9z" class="R"></path><path d="M175 639c2 2 5 5 8 7 5 3 10 7 16 8 7 3 16 3 24 3h1 1 1 1s0 1 1 1h5l1-1h2v1h0v1h0c-1 0-2 0-3 1h-3c0 1-2 0-3 0v1h-10c-1 0-2 0-3-1l-1 1c-1-1-1-1-2-1-1 1-3 0-4 1h-4c-1-1-1 0-2-1-1 0-2 0-2-1h5v-1c-1 0-2 0-3-1-3 0-5 0-8-1v-1s-1 0-1-1c-1 0-1 0-2-1 1 0 2 1 3 0h-1c-1 0-1 0-2-1-6 0-13-8-16-12v-1h0 1z" class="D"></path><path d="M204 658c4 0 7 1 11 1h10 6 2-2c-1 0-2 0-2 1h-4-7-4l-1 1c-1-1-1-1-2-1-1 1-3 0-4 1h-4c-1-1-1 0-2-1-1 0-2 0-2-1h5v-1z" class="P"></path><defs><linearGradient id="M" x1="247.216" y1="66.641" x2="234.145" y2="90.799" xlink:href="#B"><stop offset="0" stop-color="#939092"></stop><stop offset="1" stop-color="#d3d5d3"></stop></linearGradient></defs><path fill="url(#M)" d="M251 74h4 8c4-1 8-1 12-2l1 1-16 3-22 3c-8 2-15 5-22 8l-9 3c-1 1-3 2-3 1 1-1 6-3 8-4h2c1-1 1-1 2-1 2-1 3-2 5-2-1 0-1-1-1-1h-1 0 1c1-1 2-1 3-2v-1s1 0 1-1c0 1 1 1 1 0h3 2v-1h0-6v-1h1c2 0 4 0 6-1h0-1-2c1-1 3-1 4-1h0-2v-1h2c3-1 6 0 8-1 4 0 7 0 11 1z"></path><path d="M231 76h5l-2 1c-1 1-2 1-4 2h0 1-1 0v-1h0-6v-1h1c2 0 4 0 6-1z" class="W"></path><path d="M232 74c3-1 6 0 8-1 4 0 7 0 11 1-5 1-10 2-15 2h-5 0-1-2c1-1 3-1 4-1h0-2v-1h2z" class="P"></path><path d="M227 273c2 0 3 0 5 1l3 2-2 1c1 2 1 3 2 4l1 1h-1v1h1c0 1 0 1-1 1l3 3s1 0 1 1a30.44 30.44 0 0 1 8 8c0 1 1 2 1 2 1 2 4 5 4 8l-4-5 2 4v1l-6-7h0c0 1 1 2 1 3-1-1-2-2-2-3h0c0-2-2-3-3-5-2-2-4-5-7-7-2-1-4-2-6-4l-4-4c0-2 0-2 1-3v-1c0-1 2-1 3-2h0z" class="c"></path><path d="M224 276v-1c0-1 2-1 3-2-1 2-1 2 0 4l1 1c2 2 4 3 6 4v1s1 0 1 1h-1-1c-4-1-6-4-9-7v-1z" class="e"></path><path d="M227 273c2 0 3 0 5 1l3 2-2 1c1 2 1 3 2 4l1 1h-1v1h1c0 1 0 1-1 1 0-1-1-1-1-1v-1c-2-1-4-2-6-4l-1-1c-1-2-1-2 0-4h0z" class="g"></path><path d="M244 299c-1-1-1-3-2-4-3-3-6-6-9-10 2 2 4 3 5 4h1v-1a30.44 30.44 0 0 1 8 8c0 1 1 2 1 2 1 2 4 5 4 8l-4-5 2 4v1l-6-7z" class="d"></path><path d="M425 615h0 1 0c-2 3-7 5-8 8l-23 10 20-1h0-2v1h1 0c-1 1-3 0-5 1h-1-2-6c1 1 3 1 5 0h3c1 1 1 1 2 1h3l-1-1c0-1 1 0 2 0s1 0 2 1c-4 0-8 1-13 1 0 1-1 0-2 0l-1 1 1 1h0c-10-1-19 0-29 3l-12 2c-1 1-2 1-3 1l18-6c1 0 2-1 3-1 1-1 3 0 4-1 1 0 4 0 6-1l1-1h1c1-1 1-1 2-1h0c0-1 0-1 1-1 4 0 7-2 11-4 8-4 15-8 21-13z" class="P"></path><defs><linearGradient id="N" x1="410.675" y1="666.876" x2="432.308" y2="660.012" xlink:href="#B"><stop offset="0" stop-color="#cac9c9"></stop><stop offset="1" stop-color="#ededec"></stop></linearGradient></defs><path fill="url(#N)" d="M401 638h0l-1-1 1-1 1 1h2 0 1c2 0 4 1 6 2 7 4 13 9 18 15 7 10 7 20 7 32-1 3-1 8 1 11h0c1 2 2 3 3 4h0c-2-1-4-3-5-6h0v-6c-1 3-2 6-3 8-1 0-1 0-1-1s1-2 1-3v-1c1-1 1-3 1-5 2-10 0-20-5-29-6-11-16-16-27-20z"></path><path d="M120 645v7c3-8 2-14 1-21-1-3-1-6-2-9-4-12-12-20-21-28h0l4 3a30.44 30.44 0 0 1 8 8c1 2 3 3 5 6 3 4 5 10 6 16 1 2 0 6 1 9h1v1 1c1 1 0 4 1 5 0-1-1-3 0-4v-2h1v2c-1 1 0 2 0 3h0c0-1 1-3 0-4v-2-1c0-1-1-1-1-1s1 0 1-1l-1-6h0c0 2 0 3 1 4v3c1 1 0 2 1 3h1v2h1 0v-1-2-7c-1-1 0-7 0-8 2 13 1 26-8 37-2 3-5 6-9 6-3 1-7 0-9-2l-4-4c3 1 6 4 9 4h1c1-2 1-5 1-7l-1-1h0c0-1-1-2-1-3 1 1 1 2 2 2v1 2h0v5c-1 0-1 1 0 1h0 0c2-1 3-2 4-3v-2 2c-1 2-3 3-4 5l3-1c2-1 3-3 4-4s1-1 2-3c0-1 0-3 1-5h1v-6z" class="P"></path><path d="M157 617v-1c0-1-1-3 0-4 1 1 2 3 3 4h0c2 1 2 3 3 4l3 6-1 1c1 1 1 2 1 3l-1 1c1 3 2 7 2 10 1 2 1 4 1 6 1 2 3 5 5 7l1 1v1c1 1 2 1 3 2h0c-4-1-7-4-9-8l-1 1 3 3c0 1 2 3 3 4h0c1 1 2 2 3 2s2 1 2 1h3l4 1c1 0 2 1 3 0h1c1 1 2 0 3 1h1v1h0 0c-1 0-2 0-4 1 0 0-2-1-3 0h-2-4c0 1-1 1-1 1l-2 1h-1c-1 0-2 1-3 1h-1c-1 1-3 3-4 3-1 1-2 1-3 1l1-1c1 0 1 0 2-1h1l2-2c-2 0-2 1-4 1 0 0-1 1-2 1h0c4-2 9-3 13-5-4 0-9 0-12-2h1l-1-1v-1l1 1c1 0 1 0 1 1h1 3c1 0 2 0 3 1h4c2 1 6 1 9 0l1-1h0c-2 0-4 0-7-1-3 0-6-1-10-3-3-1-5-4-7-7-1-2 0-3-1-5v-5-4-4c-1-1 0-2-1-3v-2-2c0-1-1-1-1-2l-1-3c0-1-1-2-1-4 0-1-1-2-2-3l-1-2v4z" class="M"></path><path d="M160 616c2 1 2 3 3 4l3 6-1 1c0-2-1-2-2-3l-3-8z" class="f"></path><path d="M163 624c1 1 2 1 2 3 1 1 1 2 1 3l-1 1c1 3 2 7 2 10 1 2 1 4 1 6 1 2 3 5 5 7l1 1v1c1 1 2 1 3 2h0c-4-1-7-4-9-8l-1 1-4-27z" class="W"></path><defs><linearGradient id="O" x1="292.24" y1="65.289" x2="297.598" y2="73.281" xlink:href="#B"><stop offset="0" stop-color="#aaa8a9"></stop><stop offset="1" stop-color="#cecece"></stop></linearGradient></defs><path fill="url(#O)" d="M313 66h4l9-3c3 0 6-1 9-2-1 0-1 1-2 1h0c-1 1-3 1-4 1l-2 1c-2 0-3 1-5 2-1 0-1-1-2 0h-2-2v1c-2-1-3 0-5 0-1 1-1 0-2 0s-1 1-1 1c1 0 2-1 2-1 1 1 2 0 2 0h3 1c1-1 3 0 5 0v-1h2 7v1l-1 1h-1c-2 1-5 0-7 0-3 1-6 1-9 2-1 0-2-1-3 0-2 0-3 0-5 1 0 0-1-1-1 0h-2 1 1c1 1 2 1 4 1 1-2 6 0 8-1v-1c1-1 4 0 6-1h4v1h-9v1h4c-2 1-5 0-6 1-1 0-1 0-2 1h-3 0c-2-1-5-1-7-1h-12c-5 0-9 1-14 1l-1-1c9-1 16-3 24-7h2 1c1 0 3-1 4-1v1l-1 1c2 1 7-1 8 0z"></path><path d="M90 213l6-10v1c-2 5-4 10-5 15-1 1 0 3-1 5-1 4-3 8-4 11l-4 15c-1 1-1 4-2 5h-1v1l-1 2v-3h0l1-1v-2l-1 1c-1 1-2 1-2 2 0 0 0 1-1 1-1 1-3 5-3 7h0v-1-1c0-2 1-4 2-6-1 1-2 3-2 4v2l-1 3c-1 2-1 2-1 4h0c-1 0-1 1-1 1l-1 2v1 1c-1 1 0 2-1 3 0 1 0 3-1 5v5h-1v2h-1 0c1-1 1-3 1-4v-2c1-2 1-4 1-6v-3h1v-2-1c0-1 0-1 1-2 0-2 0-3 1-5 0-2 1-4 2-6 0-2 1-3 2-5l-1 1c0-1 1-3 1-4l1-1v1l-1 2 4-4c1-1 2-2 3-4 1-1 2-2 2-3 1-2 2-5 3-7v-1s1 0 1-1v-1-1c1-1 1-2 2-3h1v-1c0-1 1-2 1-3h-1c0 1-2 4-2 5-1 2-1 3-2 4l1-5 4-7v-1c1-2 1-3 0-5z" class="D"></path><path d="M254 227l1 3v1 3l-1 5c0 1-1 3-2 4 0 1-1 2-1 2-1 1-1 2-1 3l-1 1h0c-2 0-3 2-4 3l-1 1v1l-1 1 1 1-3 3h-3v-2c0-1-1-1-1-2v-4l2-3-1-2c1 0 1-1 2-1 0-1 0-1-1-2l1-1h1c4-2 7-4 10-7l1-2v-1c1-1 1-2 1-3l1-2z" class="X"></path><path d="M244 244l1 1 1-2h0c0 1 0 2 1 2-1 1-1 1-2 1h-2v1l-2-1c1 0 2-1 2-2v-1l1 1z" class="Q"></path><path d="M244 244c1-2 1-3 4-4l-1 3c0 1 0 1 1 1l-1 1c-1 0-1-1-1-2h0l-1 2-1-1z" class="C"></path><path d="M241 242c4-2 7-4 10-7-1 2-1 4-3 5-3 1-3 2-4 4l-1-1v1c0 1-1 2-2 2l-2 2-1-2c1 0 1-1 2-1 0-1 0-1-1-2l1-1h1z" class="T"></path><path d="M243 255h0l-2 2c-1 1-2 1-2 1-1-1 0-1 0-2v-1h-1 0c0-1 0-2 1-3 0-1 2-3 3-4h2c-1 1-1 1-1 2v4l1-1v1l-1 1z" class="a"></path><path d="M243 250v4c-1 1-1 2-2 3l-1-1v-1c0-2 1-3 3-5z" class="e"></path><path d="M253 238l1 1c0 1-1 3-2 4 0 1-1 2-1 2-1 1-1 2-1 3l-1 1h0c-2 0-3 2-4 3l-1 1-1 1v-4c0-1 0-1 1-2s2-1 3-2c0 1 1 1 1 1l2-2c1-2 2-4 3-7z" class="a"></path><path d="M244 248h1v4l-1 1-1 1v-4c0-1 0-1 1-2z" class="b"></path><path d="M254 227l1 3v1 3l-1 5-1-1c-1 3-2 5-3 7l-1-1h-1c-1 0-1 0-1-1l1-3c2-1 2-3 3-5l1-2v-1c1-1 1-2 1-3l1-2z" class="S"></path><path d="M254 227l1 3v1 3l-1 5-1-1v-5h-1v-1c1-1 1-2 1-3l1-2z" class="b"></path><path d="M250 0h7c7 5 13 11 14 19 0 1 0 3 1 4 1 3 0 8-2 10 0 1-1 3-1 4-1 1-4 5-4 7l5-5c0 1 0 1-1 2s-2 3-4 3l-3 3h-1l1-1-4 4h1l6-3s1 0 1-1l2-1v1c1-1 2-1 3-2h1c-2 2-3 2-4 4-1 0-2 1-2 1l-1 1h-2c-2 1-3 1-4 2h-4-3-1l-1-1 9-6h0l3-4c5-7 9-15 8-23h0c-1-3-2-3-4-5-3-4-6-8-10-10l-6-3z" class="M"></path><path d="M259 45s0 1-1 1c0 1-5 4-5 5 1-1 1-1 2-1h1 1-1v1l-1 1h-3-1l-1-1 9-6z" class="G"></path><path d="M256 51c5-1 9-3 13-5-1 1-3 2-4 3-1 0-1 1-2 1h0c-2 1-3 1-4 2h-4l1-1z" class="D"></path><defs><linearGradient id="P" x1="71.123" y1="189.306" x2="74.13" y2="201.472" xlink:href="#B"><stop offset="0" stop-color="#a3a1a1"></stop><stop offset="1" stop-color="#d1d0d1"></stop></linearGradient></defs><path fill="url(#P)" d="M61 198l9-1h3c13-3 25-12 33-23h0c-4 9-11 16-17 24h-1-1 0c-2 0-3 1-5 1-1 0-2 1-3 1s-2 0-2 1h0-4c-3 1-8 1-12 1l-3-1c-7-1-13-4-19-7 2 0 3 1 5 2 3 0 14 3 17 2z"></path><path d="M58 201h0l-1-1h1c3-1 8 0 11 0 1 0 3 0 4-1 2 0 4-1 6 0 0 0 1 0 1-1h3 1l1-1h0v1l-3 1c-1 0-2 1-3 1s-2 0-2 1h0-4c-3 1-8 1-12 1l-3-1zm46-49l5-6h1l-2 2 1 1c1-2 3-5 4-6h0 0v-1c2-2 2-4 3-5h1c0-1 1-1 1-1l-1 3-5 8-2 4c0 1-1 2-1 3-1 2-2 4-3 5-1 3-3 5-4 7-2 2-3 4-5 6-2 3-5 5-7 7-1 1-3 2-4 3l4-4h0-1c0-1 0-1 1-2 1 0 1-1 1-2 1-1 4-3 4-5-2 1-5 5-7 7l-4 3h-1l11-11c-2 1-3 2-5 4s-5 5-8 6h0l12-12h0l-2 1h-1l2-2 3-3h-2c-1 1-2 2-4 2h0c2-2 5-3 8-4 1-2 2-3 4-4 1-2 3-3 4-4h-1z" class="P"></path><path d="M92 165c3-2 6-5 9-7 0 2-3 4-5 6 0 0-2 2-3 2h0l-2 1h-1l2-2z" class="D"></path><path d="M105 152c2-1 3-2 4-3-2 3-5 6-8 9-3 2-6 5-9 7l3-3h-2c-1 1-2 2-4 2h0c2-2 5-3 8-4 1-2 2-3 4-4 1-2 3-3 4-4z" class="M"></path><path d="M299 65l1-1c6-4 10-11 13-17 2-3 4-5 4-8h0l1-1v1c0 1-1 4-2 5v1c0 1 0 0-1 1v1h0 0 0 1c1 0 1-1 2-2v1c-1 3-4 5-5 9h1l1-1h0c7-4 11-12 14-20l3-7c1-3 2-6 4-8 0-2 2-4 3-5 1 0 3 1 3 0l1-1c0 1 0 1 1 1v2c-2-1-3-1-5-1-6 7-8 17-12 25-1 3-3 6-4 9l-3 3-3 3c-1 0-1 0-1 1h-1c-1 0-2 3-4 4-1 0-1 1-2 1h0c1 0 2 0 3-1h1c3 0 6-2 9-4h0c0 1-1 1-2 2l-1 1h-1c2 1 2-1 4-1h0l-2 2c-1 0-2 1-3 1s-2 1-3 1h4 0v1l-3 1c-1 1-2 0-3 0-1 1-1 1-2 1h-1 0 3 1 1c1-1 2-1 2-1l6-2c1 0 2 0 3-1h1c1 0 1-1 2-1 0 0 1 0 2-1 2 0 3-2 5-2 3-2 6-3 8-5 3-1 6-3 8-5l1-1c1 0 1 1 1 1h0c-2 1-5 3-7 4-3 2-7 4-10 6-1 1-2 2-3 2l-1 1c1 0 2-1 3 0-1 1-4 1-6 2l-1-1c-1 0-4 2-6 2l-9 3c-1-1-6 1-8 0l1-1v-1c-1 0-3 1-4 1h-1-2z" class="G"></path><path d="M306 64h2c1 0 2 0 3-1v1l-5 1v-1z" class="P"></path><path d="M76 541l1 1s1 0 1 1c1 0 1 1 1 1l1-1c1 0 1 1 2 1h0c1 0 1 1 3 2h0c1-1 2-2 3-2 2 0 3 0 4 1 2 2 3 4 4 7l6 6 1 1-1 1c3 2 5 5 6 9 2 1 2 3 3 5h0-1v1c-1 0-2-1-2-1l-5-5c-3-4-7-6-11-9l1-1c-2-1-4-2-5-4-1-1-2-2-3-4h0c0-2-6-7-8-8 0 0-1-1-1-2z" class="R"></path><path d="M98 562c2 1 4 3 5 4s3 2 3 3h1v1h0c-2 0-3-2-4-3-2-1-3-2-5-4h0v-1z" class="D"></path><g class="H"><path d="M79 544l1-1c1 0 1 1 2 1h0c1 0 1 1 3 2h0c3 2 4 5 7 8 2 2 4 6 6 8v1l-3-3s-1-1-1-2l-8-8c-1-1-2-3-3-3-1-1-3-2-4-3z"></path><path d="M85 546c1-1 2-2 3-2 2 0 3 0 4 1 2 2 3 4 4 7l6 6 1 1-1 1c3 2 5 5 6 9h0c-2-1-2-4-5-6-1 0-3-2-4-3l-7-7v1c-3-3-4-6-7-8z"></path></g><path d="M85 546c1-1 2-2 3-2 2 0 3 0 4 1 2 2 3 4 4 7l6 6 1 1-1 1c-4-5-9-9-13-14 0-1-1-1-2-1v1l5 7v1c-3-3-4-6-7-8z" class="f"></path><path d="M107 553c1 2 2 4 2 6l2 3h0v-2s0-1-1-1c0-4-2-7-3-10-1-1-1-2-2-3v-1-1l9 22c2 1 3 3 4 6v1c0 1 1 2 1 4 2 6 3 12 6 18h1v1h0-1 0c0 1 0 1 1 2 0 4 2 8 2 12h-1c0-3-2-6-2-10-1-1-2-2-2-3-2-2-2-5-4-7l-1-1c0-2-2-2-3-3 0-1-1-2-2-3 0 0 0 1-1 1h0c-5-6-12-11-20-15h1c4 1 7 3 10 6 1 1 3 2 5 3 1 1 3 3 5 4l-2-2c-2-4-6-7-9-10v-1c4 3 6 6 9 9 1 0 2 2 3 3h1c-1-2-4-4-5-6v-1h1 0c-1-2-1-4-3-5-1-4-3-7-6-9l1-1 4 5 1-1c2 2 4 7 5 9 0-2-1-4-2-6 0-2-1-4-2-6s-2-4-2-5v-2z" class="R"></path><path d="M103 559l4 5 9 17v1l-1-1c-1-2-4-4-5-6v-1h1 0c-1-2-1-4-3-5-1-4-3-7-6-9l1-1z" class="W"></path><path d="M114 566c2 1 3 3 4 6v1c0 1 1 2 1 4 2 6 3 12 6 18h1v1h0-1 0-1l-3-8c-2-2-3-4-4-6v-3c0-2-1-4-1-5-1-3-2-5-2-8zm-42 58h-3c-1 0-2 1-3 1h-3c-1 0-2 0-3 1-7 1-14 4-20 8-5 4-9 10-13 15v1c-1 1-1 2-1 3l-1 2c0 1-1 2-1 3v1 2c-1 3-1 6-1 9 1 2 1 5 2 6 0-1 0-3 1-5v-2-3c0-1 1-1 1-2v-1 1h0l1-1v1 2c-1 0-1 1-1 2v2c0 3-1 5 0 7 1 7 6 16 11 21 3 2 5 3 8 5h-1 0-1c-8-5-15-12-19-21-1-2-1-4-1-5-1-1 0-2-1-3-1-3-1-7-1-11 0-1 1-3 0-5 0-1 0-4 1-5v4c0-1 1-2 1-4h0v-2l4-8c1-1 2-3 3-5 5-6 11-10 19-13 3-1 14-4 16-3 1 1 3 1 4 1l2 1z" class="H"></path><path d="M53 54l-4 5c-2 4-2 12 0 16 0 1 0 1-1 2 0-2-1-4-1-5-1-7 0-15 4-20 8-9 19-15 31-16 7-1 15-1 23 0 2 0 5 0 6 1h-8-9c-2 0-10 1-11 2-2 2-6 3-8 4-3 2-8 7-9 11 0 3 0 7 2 10 1 2 4 4 6 5 1 0 2 0 3 1h5 0 1v1c-2 0-5 0-7-1h-1c-3-1-6-3-8-6-3-3-2-7-1-11 0-1 0-2 1-3 0-1 1-2 2-4l1 1 3-3c-1 0-1 0-2 1l-1 1h-1c-1 0-3 1-5 2-4 1-8 3-11 6z" class="Z"></path><path d="M53 54v-1l6-6c4-3 8-4 13-6 3-1 8-4 11-3-1 1-4 1-6 1 1 1 3 0 4 0v1c-2 0-3 1-5 2h0c-1 0-1 0-2 1h-1c-1 0-1 1-2 2l-1 1h-1c-1 0-3 1-5 2-4 1-8 3-11 6z" class="D"></path><path d="M70 307h0c0-1 1-3 1-4 1-1 2-2 2-4v4h0c0 1 0 1 1 2-2 13-6 28-12 41v-1-1h1v-3h1-1 0c-1 1-1 3-2 4s-4 5-5 5h0l-3 2v-1c2-1 3-2 4-3l-1-2-1 1v1h-1s-1 0-1 1v-1l-1 1h0v-1c2-3 5-5 6-8l2-1h-1c-1 0-1 0-1 1h0l-1 1c-1 1-2 1-2 2v1c-2 1-3 3-5 4h0c2-2 6-5 7-8v-1c-3 4-7 7-11 10-1 0-2 1-3 1h0 0c6-4 11-8 15-14v-1h-1c-1 1-2 3-4 5-2 3-8 6-11 8v-1l9-6c4-4 7-8 9-12 2-2 4-5 6-8 1-2 2-3 2-5v-1h0l3-7-1-1z" class="R"></path><path d="M229 261c0 1 0 1 1 1h1c1-2 4-7 4-9 1-1 1-3 1-4s1-2 2-3l1 2-2 3v4c0 1 1 1 1 2v2h3l3-3v-1c1 0 2 0 3-1h1c1 0 2-1 2-2v2l1-1h1c-1 1-2 2-2 4-1 2-3 3-4 6l-2 2v1c-1 1-2 2-2 3l1 1c-1 0-3 1-4 1h-1s-1 1-2 1h-1l-1-1c-1-1-1-2-3-3v2 1h-1c-2 0-3 0-5 1h0c-1-1-2-1-2-2-1-1-1-2-1-3h-1v-1c1-1 2-1 3-1 2-1 3-2 4-4h0 1z" class="C"></path><path d="M241 259c-1 1-1 2-2 2-2-1-2-1-3-3 0-2 0-5 1-7v4c0 1 1 1 1 2v2h3zm-17 6c2-1 3-2 4-4l1 2c1 2 0 3 2 5v2c-1 0-1-2-2-3-1 0-1 0-2 1l-1-1 2-2h-1-3 0 0z" class="T"></path><path d="M221 267v-1c1-1 2-1 3-1h0 0 3 1l-2 2c0 1-1 1-1 1v1c-1 1-2 1-2 1-1-1-1-2-1-3h-1z" class="D"></path><path d="M226 267l1 1c1-1 1-1 2-1 1 1 1 3 2 3v1h-1c-2 0-3 0-5 1h0c-1-1-2-1-2-2 0 0 1 0 2-1v-1s1 0 1-1z" class="R"></path><path d="M234 271c0-2 0-3-1-5v-1h0l-1-1c0-2 0-3 1-4l2-3c0 2-1 4-1 5h1v-1h1c0 1 1 1 1 2h0c1 0 2-1 3-2h0 1c1-2 3-2 5-4 0-1 1-1 1-2v2l2-2v1l-1 1c0 2-1 3-3 5l1 1-2 2v1c-1 1-2 2-2 3l1 1c-1 0-3 1-4 1h-1s-1 1-2 1h-1l-1-1z" class="B"></path><path d="M244 266h-1l-3 2c1-2 2-3 3-5l1-2c2-1 3-4 4-4 0 2-1 3-3 5l1 1-2 2v1z" class="N"></path><defs><linearGradient id="Q" x1="360.78" y1="611.383" x2="369.868" y2="621.428" xlink:href="#B"><stop offset="0" stop-color="#aaa7a8"></stop><stop offset="1" stop-color="#e4e3e4"></stop></linearGradient></defs><path fill="url(#Q)" d="M406 582v1l-1 1v2h0c0 2-2 3-3 5-1 1-2 3-2 4l9-9h0c-3 3-7 7-9 10-1 2-2 3-4 4v-1 1l-1-1c0 1-1 2-2 3-1 0-2 1-3 2v2h-1c0 1-1 2-2 3-2 0-3 2-4 3l-9 6c-3 1-6 2-9 4-13 5-28 8-42 8h1s2 0 3-1l10-1c5-1 9-2 13-5l7-4h2 0l2-1v1c-1 0-1 1-2 1h0v1c2-1 5-4 7-6 0 1-1 2-2 3l-1 1 1-1h1c0-1 1-1 2-1 0 0 0-1 1-1h0c8-4 14-10 19-17l1 1h0l-9 11c3-2 5-5 8-7 1-1 2-2 4-3l4-4c4-5 7-10 11-15z"></path><defs><linearGradient id="R" x1="78.186" y1="540.707" x2="86.755" y2="529.21" xlink:href="#B"><stop offset="0" stop-color="#bdbbbb"></stop><stop offset="1" stop-color="#e7e7e7"></stop></linearGradient></defs><path fill="url(#R)" d="M100 551c-4-5-7-10-12-15-7-8-16-16-26-20-4-3-9-4-13-6 3 0 6 0 9 1 2 1 5 2 7 3 6 3 12 7 18 12 3 3 6 7 10 10 1 0 1 0 1 1l1 1c2 1 3 3 4 5 0 1 1 1 2 2h0 1l1 1v1c1 0 1 1 1 2 1 0 1 1 2 2 0 1 1 2 1 2v2c0 1 1 3 2 5s2 4 2 6c1 2 2 4 2 6-1-2-3-7-5-9l-1 1-4-5-1-1c1-2-1-2-1-4h0c-1-1-1-2-1-3z"></path><path d="M100 551c2 2 4 5 5 7 0 2 2 4 3 5l-1 1-4-5-1-1c1-2-1-2-1-4h0c-1-1-1-2-1-3z" class="G"></path><path d="M115 694c-4 0-9 1-12 2-11 1-23 0-33-4-8-3-17-8-21-16-3-7-4-14-1-21 1-3 2-7 6-9l-3 4c-3 5-5 12-3 17h0c0 2 1 4 2 6 1 0 1 0 1 1h0c3 3 6 6 10 8l1 1h1 0c2 1 4 1 6 2l2-1c3 3 6 4 10 6 3 0 6 2 9 2 5 2 11 2 16 2 3 0 6 0 9-1v1z" class="Z"></path><path d="M51 674c3 3 6 6 10 8l1 1h1 0c2 1 4 1 6 2l2-1c3 3 6 4 10 6-2 0-3-1-4-1-2 0-4 0-6-1-2 0-3-2-5-2-1 0-1-1-2-1h0c-3 0-5-2-7-3-2-2-6-5-6-8z" class="P"></path><path d="M268 226c3 3 5 4 9 6l2-1c3 2 8 3 12 4-1 1-1 2-2 2 0 1-2 2-2 2-1 1-2 2-3 2s-1-1-1-1c-2 0-5 1-7 1h0 0c1 0 1 0 2-1h1 1-3c-1 1-3 1-4 1-2 0-4 1-6 1h0c-1 0-3 0-5-1l-4 1h0-2c-1 1-1 1-2 1l-1 1-1-1c1-1 2-3 2-4l1-5v-3-1-2l1 1h1c0 1 0 1 1 2l1-1h0 4c1 0 1 0 1-1l2 1c0-1-1-1 0-2 0 0 1 0 1 1l2 1h0l-2-3 1-1z" class="Y"></path><path d="M255 230v-2l1 1h1c0 1 0 1 1 2l1-1h0 4c1 0 1 0 1-1l2 1c0-1-1-1 0-2 0 0 1 0 1 1l2 1h0c2 0 3 2 4 3-2-1-3-1-4-2h-1l1 1h0-1-2c-1 1-1 1-2 1h-1-3c-1 1-1 1-2 3-1-1 0-2-1-3-1 0-2 1-2 1v-3-1z" class="S"></path><path d="M255 230v-2l1 1h1c0 1 0 1 1 2l1-1h0 4c1 0 1 0 1-1l2 1c0-1-1-1 0-2 0 0 1 0 1 1h0c0 1 0 1 1 2h-1-1 0c-2 1-2 0-4 1 0 0-1 0-1-1-1 1-1 1-2 1-2-1-2-2-4-1v-1z" class="K"></path><path d="M254 243c3-2 6-4 9-5-1 1-2 2-3 2h1 2c0-1 1 0 1-1h2 0c1 0 1 1 2 1 0-1 1-1 1-1 2 0 5-1 7 0h5c2 0 4-1 6 0-1 1-2 2-3 2s-1-1-1-1c-2 0-5 1-7 1h0 0c1 0 1 0 2-1h1 1-3c-1 1-3 1-4 1-2 0-4 1-6 1h0c-1 0-3 0-5-1l-4 1h0-2c-1 1-1 1-2 1z" class="L"></path><path d="M289 237c0 2-4 5-6 6l1 1c-2 1-3 1-4 2l-3 3c-1 0-2 0-4 1 1 1 1 1 2 1 0 1 1 0 1 1h1v1c-1-1-2 0-3-1h-2-1-1 0v-1h-2l1 1h-1s0-1-1-1c-2 1-4 3-6 4h-1l3-3h0c-1 0-2 0-3 1h-1c-2 1-3 2-4 3-1 0-2 1-4 0l-1 1c0-2 1-3 2-4h-1l-1 1v-2c0 1-1 2-2 2h-1c-1 1-2 1-3 1v1l-1-1 1-1v-1l1-1c1-1 2-3 4-3h0l1-1c0-1 0-2 1-3 0 0 1-1 1-2l1 1 1-1c1 0 1 0 2-1h2 0l4-1c2 1 4 1 5 1h0c2 0 4-1 6-1 1 0 3 0 4-1h3-1-1c-1 1-1 1-2 1h0 0c2 0 5-1 7-1 0 0 0 1 1 1s2-1 3-2c0 0 2-1 2-2z" class="V"></path><path d="M273 243l-1 1c-1 1-6 3-8 4v-1c2-3 6-3 9-4z" class="T"></path><path d="M252 253l1-1h2c1-1 3-2 4-3 0 1-1 2-2 3h0l3-1h0l-1 2c-2 1-3 2-4 3-1 0-2 1-4 0l-1 1c0-2 1-3 2-4z" class="B"></path><path d="M272 247v1h3c2-1 3-2 4-2 1-1 2-1 2-2 1 0 2-1 2-1l1 1c-2 1-3 1-4 2l-3 3c-1 0-2 0-4 1 1 1 1 1 2 1 0 1 1 0 1 1h1v1c-1-1-2 0-3-1h-2-2v-1l3-2c-3 0-5 1-7 2l3-3 3-1z" class="F"></path><path d="M289 237c0 2-4 5-6 6 0 0-1 1-2 1 0 1-1 1-2 2-1 0-2 1-4 2h-3v-1l-3 1c1-1 2-1 3-1-1-1-1-1-1-2l1-1h1-1l1-1c1-1 2-1 3-2 2 0 5-1 7-1 0 0 0 1 1 1s2-1 3-2c0 0 2-1 2-2z" class="E"></path><path d="M289 237c0 2-4 5-6 6 0 0-1 1-2 1 0 1-1 1-2 2-1 0-2 1-4 2h-3v-1l7-3c1-1 3-2 5-3 1 0 2-1 3-2 0 0 2-1 2-2z" class="X"></path><path d="M254 243c1 0 1 0 2-1h2 0 0v1l-1-1-1 1c1 0 1 1 1 1h1c1-1 1-1 2 0h1c-1 2-6 6-8 7h0l-2 2-1 1v-2c0 1-1 2-2 2h-1c-1 1-2 1-3 1v1l-1-1 1-1v-1l1-1c1-1 2-3 4-3h0l1-1c0-1 0-2 1-3 0 0 1-1 1-2l1 1 1-1z" class="Q"></path><path d="M245 252c1-1 2-3 4-3l-1 1v1c0 1-1 2-1 2-1 0-2 0-3 1v-1l1-1z" class="X"></path><defs><linearGradient id="S" x1="311.076" y1="285.901" x2="323.869" y2="287.38" xlink:href="#B"><stop offset="0" stop-color="#454445"></stop><stop offset="1" stop-color="#5f5f5f"></stop></linearGradient></defs><path fill="url(#S)" d="M313 277h1v-1c-1-3-3-6-5-9 1 1 2 3 4 3l1-1c2-1 3-2 5-2 1 0 2 0 2-1 0 0 0 1 1 1v1 1h1c0 1 0 4 1 5 1 9-1 17 2 26h0l-2-2-1-2v-1 7h0c-1-2 0-3-1-5v1c1 2 0 3 0 5h0l-1-2h-1c-1 1 0 1-1 2h0v1h-1c-1 1 0 2 0 3l-1 1h0l-1-1h0s0 1-1 2h0v-3h0v-3l-1 4-1-1c2-6 2-11 2-17l-1-4c0-2-1-5-2-8v-3c1 1 1 2 1 3z"></path><path d="M315 306h1c1-3 1-4 3-6h0v1c-2 1-2 3-3 6h0s0 1-1 2h0v-3h0z" class="E"></path><path d="M314 285c0-2-1-5-2-8v-3c1 1 1 2 1 3 1 1 1 2 1 3 1 2 1 3 1 5 1-3 0-5 0-8h0c0 1 0 1 1 2v-1c0 1 1 1 1 2 1 1 1 3 2 4v1 3c0 3 1 6 0 9l-1 1c0 1-1 3-2 4l1-6-2 4v3l-1 4-1-1c2-6 2-11 2-17l-1-4z" class="O"></path><path d="M315 289v5c1 1 0 3 0 4 1-1 1-2 1-2v-2l1 1v-1 5c2-4 2-7 2-11 0 3 1 6 0 9l-1 1c0 1-1 3-2 4l1-6-2 4v3l-1 4-1-1c2-6 2-11 2-17z" class="F"></path><path d="M148 112h0c0 1 1 2 2 3h0v-3c1 0 1-1 2-1h2c1 1 2 1 3 1 0-1 1-2 1-3 1-1 1-2 2-3v-1c0-1 1-1 1-1 0-3 1-5 2-8v-7c1 2 0 3 0 5 1-1 1-2 1-3 0-2 1-2 1-3l1-1c1-1 2-3 3-3 1-1 2-1 2-2h1c0 1-2 2-3 3h0v1h1s0 1 1 1v1h1l3-4c-1 0-1 0-1-1s1-1 1-2h1l2-2v1l-4 4 8-5c-3 3-6 5-9 8-6 6-8 13-12 20l-4 8 3-3v1c0 1-2 2-2 3h-1v-1l-1 1v1h1c2-1 4-2 7-2 0-1 0-1 1-1 1 1 1 0 2 0l1-1h1l-2 1-3 3-13 10c-6 4-10 10-16 14 1 0 1-1 1-1v-2h1c1-1 2-2 3-4l2-4v-1c1-2 2-5 3-7h-1l2-9c1 1 1 2 1 4 0-2 0-4 1-5z" class="P"></path><defs><linearGradient id="T" x1="427.756" y1="437.843" x2="414.973" y2="426.685" xlink:href="#B"><stop offset="0" stop-color="#c7c6c7"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#T)" d="M448 411c1 0 2 0 4-1v1h-1 0c1 0 1 0 2 1-12 4-23 11-32 19l-8 8c-3 5-6 10-7 15 0 3 0 6 1 9v2h-1c-2 0-3 1-5 1 1-1 0-6 0-8 0-5 1-10 2-14 1-2 1-3 2-4l1-1h0c1-1 2-3 3-4s1-1 1-3l2-1c1-1 2-3 3-3 3-2 7-4 10-6l9-4c4-3 9-7 14-7z"></path><path d="M406 465c0-3-1-5-1-8s1-7 2-10c1-2 3-7 6-8-3 5-6 10-7 15 0 3 0 6 1 9v2h-1z" class="W"></path><defs><linearGradient id="U" x1="142.336" y1="575.527" x2="153.234" y2="650.092" xlink:href="#B"><stop offset="0" stop-color="#b4b3b3"></stop><stop offset="1" stop-color="#dfdede"></stop></linearGradient></defs><path fill="url(#U)" d="M126 570c6 8 12 16 20 22l-1 1c2 3 5 6 7 9 3 3 7 6 9 9h-1l-1 1c0 1 2 3 3 5h0l1 1 1 2h-1c-1-1-1-3-3-4h0c-1-1-2-3-3-4 0-1 0-1-1-2l-1-1c-1 0-2-2-3-3 1 3 3 8 3 11v1c1 1 1 2 1 3h-1c-1-1-1-3-1-5-1-1 0-2-1-3l-1 1v6c0 4-1 8-1 13-1 3-1 8-2 11l-2 6c-1 1-1 2-2 3-1 0 0-1 0-2v1l-1-1h-1 0c3-7 4-14 5-21 2-13-1-26-7-37l-8-11c-1-2-3-5-4-6-2-2-3-3-3-6z"></path><defs><linearGradient id="V" x1="256.427" y1="407.52" x2="256.3" y2="372.737" xlink:href="#B"><stop offset="0" stop-color="#080909"></stop><stop offset="1" stop-color="#3c3a3b"></stop></linearGradient></defs><path fill="url(#V)" d="M253 380c0-1 1-1 1-1 3-3 7-4 11-6 3-2 6-3 9-5-1 2-4 3-6 4h0c1 0 1 0 1 1-1 1-3 3-5 4l1 1c3-2 5-3 7-4l3 2c-8 4-15 8-20 15v2c-1 1-1 2-2 3h0c-2 1-3 2-4 4 0 0 0 1-1 2l-1 1v-1c-2 3-3 6-4 8-1 3-1 6-3 9 0-2 1-8 2-10s2-4 2-6l-3 6h0l-2-2c1-9 4-17 10-23 1-2 3-3 4-4z"></path><path d="M265 378c-1 0-4 3-5 3l1-1-1-1c2-1 3-2 4-2l1 1z" class="V"></path><path d="M247 402v-1c0-1 2-3 3-4l5-6v2c-1 1-1 2-2 3h0c-2 1-3 2-4 4 0 0 0 1-1 2l-1 1v-1z" class="U"></path><defs><linearGradient id="W" x1="286.984" y1="48.344" x2="302.943" y2="56.672" xlink:href="#B"><stop offset="0" stop-color="#cbcacb"></stop><stop offset="1" stop-color="#fbfbfb"></stop></linearGradient></defs><path fill="url(#W)" d="M302 39c4 0 8 1 11-2 0 0 0-1 1-1v-1c0 1 1 1 1 1-1 1-1 1-1 2-1 2-2 4-4 5-1 2-3 5-5 7-1 1-1 2-2 4-2 2-4 4-6 7-1 1-2 2-3 2-5 4-11 5-17 7v-1l3-1-1-1h0 0c3-2 7-5 9-7v-1c-2 1-4 3-6 5v-2h-1c-1 1-2 2-4 3h-2l-1-1c2-1 4-2 6-4 6-5 11-12 16-18l6-3z"></path><path d="M289 63h0 1v1h0-1-2l2-1z" class="G"></path><path d="M275 65c1-1 4-4 6-4v1c-1 1-2 2-4 3h-2z" class="D"></path><path d="M166 626c3 5 5 9 9 13h-1 0v1c3 4 10 12 16 12 1 1 1 1 2 1h1c-1 1-2 0-3 0 1 1 1 1 2 1 0 1 1 1 1 1v1c3 1 5 1 8 1 1 1 2 1 3 1v1h-5c0 1 1 1 2 1-2 0-3 1-5 0 1 1 2 0 3 0 0 1 0 1 1 1h0-1v1c1 0 4-1 5 0h3c1-1 3 0 4-1h7c1 0 2 0 3 1 0 1-1 0-2 1-1 0-3 0-4-1-1 0-4 0-5 1h-2-5c-1 1-2 1-3 1-2 0-3 1-4 1-2 0-4 1-5 2-4 1-8 3-12 5-1 1-2 1-3 2s-2 2-3 2h0c0 2-2 2-2 3-2 1-3 3-5 4h0c0-2 4-5 6-7 0 0 2-2 3-2l4-3s1-1 2-1c3-1 6-3 9-4 1 0 2 0 3-1h1 0c1 0 2-1 3-1 2-1 3-1 5-1h0 0-2c0-1-1-1-2 0v-1h-2-1-1c0-1-1-1-2-1h-1 0-1 1 1c-1 1-2 1-3 0h0-1c0-1-1 0-2 0v-1h-2 0 0-1s-1 0-1-1h-1 0c-1-1-2-1-3-2-2 0-3-1-4-2l-1-1c-2-2-4-5-5-7 0-2 0-4-1-6 0-3-1-7-2-10l1-1c0-1 0-2-1-3l1-1z" class="M"></path><path d="M170 645c2 3 4 5 6 7 2 1 5 2 6 4h-1c-3-1-7-3-9-6-1-2-2-3-2-5z" class="H"></path><path d="M188 655v-1h1l4 2c3 1 5 1 8 1 1 1 2 1 3 1v1h-5c0 1 1 1 2 1-2 0-3 1-5 0-1-1-2-1-3-1-3-1-6-2-8-4 2 1 4 2 7 2h0l-4-2z" class="R"></path><defs><linearGradient id="X" x1="166.733" y1="640.912" x2="187.333" y2="640.94" xlink:href="#B"><stop offset="0" stop-color="#b7b6b8"></stop><stop offset="1" stop-color="#d8d7d4"></stop></linearGradient></defs><path fill="url(#X)" d="M166 626c3 5 5 9 9 13h-1 0v1c3 4 10 12 16 12 1 1 1 1 2 1h1c-1 1-2 0-3 0 1 1 1 1 2 1 0 1 1 1 1 1v1l-4-2h-1v1l-5-3c-6-3-10-9-13-14-2-3-3-5-4-8 0-1 0-2-1-3l1-1z"></path><path d="M388 560c0 2 1 5 2 7 0 3-3 8-4 11 0 0 0 1-1 1-1 11-4 21-12 29l-7 7c-2 2-5 5-7 6v-1h0c1 0 1-1 2-1v-1l-2 1h0-2l-7 4c-2-1-3 0-5-1l1-1c7-5 12-13 15-21h0c0-1 0-2 1-4l1 1c-1 1-1 3-1 4l1 1c0-1 1-2 2-3l2-4v-1c0-1 0-1 1-2v-2c1 1 1 2 1 4 1-2 2-3 3-5 1-1 1-2 1-3h2 0v-1s1-1 2-1h0v-1h1v1c1-2 3-4 4-6 0-2 1-3 2-4 3-3 2-10 4-14z" class="H"></path><path d="M362 596l1 1c-1 1-1 3-1 4l1 1c0-1 1-2 2-3-1 4-4 8-7 11 1-2 4-8 3-10h0c0-1 0-2 1-4z" class="K"></path><path d="M373 586h2 0v-1s1-1 2-1h0c-3 6-7 12-11 16 0-1 1-3 1-5v-1c0-1 0-1 1-2v-2c1 1 1 2 1 4 1-2 2-3 3-5 1-1 1-2 1-3z" class="L"></path><path d="M357 619c4-4 9-6 13-10 3-2 5-4 6-6 4-6 6-13 8-19v-2c0-1 1-2 1-3h0c-1 11-4 21-12 29l-7 7c-2 2-5 5-7 6v-1h0c1 0 1-1 2-1v-1l-2 1h0-2z" class="W"></path><path d="M315 303v3h0v3h0c1-1 1-2 1-2h0l1 1h0l1-1v-2h1c1 4 2 9 5 11h0c3 4 6 7 9 10 2 1 4 2 5 4-2-1-4-3-6-4l6 7c0 1 0 1-1 2 2 1 5 6 5 7h-1 0c1 3 3 5 5 6v1c-2 0-4-1-6-1l-5-2h-7-2 0l-3-1-5-3-1-1 1-1h0 1v-1h1 1s-5-4-5-5h-1c-2-3-3-5-5-8h-1v1c-1-1-1-2-1-3v-6c1-3 2-5 3-8l2-4 1 1 1-4z" class="B"></path><g class="J"><path d="M315 306v3h0c1-1 1-2 1-2h0l1 1c-2 2-2 6-4 8 0-2 1-5 1-7 0-1 0-2 1-3z"></path><path d="M313 306l1 1c0 1 0 1-1 2v1l-3 8c0 1-1 2-1 3 0 2 1 3 1 5h-1v1c-1-1-1-2-1-3v-6l3-8 2-4z"></path></g><path d="M325 334h1l-3-3h0c1 0 3 0 4 1-2-2-4-3-6-5 3 1 5 3 8 3-2-1-4-3-6-5l6 3-5-5c1-1 4 2 5 2s1 0 2 1h1l6 7c0 1 0 1-1 2 2 1 5 6 5 7h-1c-2-4-7-4-11-7h0l-1 1c0-1-1-1-2-1 0 0-1-1-2-1z" class="S"></path><path d="M327 335c0-2 0-2-1-3h-1 2c0-1 0-1-1-2 3 1 5 2 7 2h1l2 2c1 0 1 0 1 1 2 1 5 6 5 7h-1c-2-4-7-4-11-7h0l-1 1c0-1-1-1-2-1z" class="Y"></path><defs><linearGradient id="Y" x1="326.834" y1="331.711" x2="318.195" y2="337.072" xlink:href="#B"><stop offset="0" stop-color="#232423"></stop><stop offset="1" stop-color="#434243"></stop></linearGradient></defs><path fill="url(#Y)" d="M315 334c0-1 0-2-1-3s-2-6-2-8v-1c2 4 4 8 8 10h0 1 0c1 1 3 2 4 2s2 1 2 1c1 0 2 0 2 1l1-1h0c4 3 9 3 11 7h0c1 3 3 5 5 6v1c-2 0-4-1-6-1l-5-2h-7-2 0l-3-1-5-3-1-1 1-1h0 1v-1h1 1s-5-4-5-5h-1z"></path><path d="M318 340c1 1 4 3 5 3 0-1 1-1 1-1h1-1c1 2 3 3 4 4h-2 0l-3-1-5-3-1-1 1-1h0z" class="K"></path><path d="M320 332h1 0c1 1 3 2 4 2s2 1 2 1c1 0 2 0 2 1l1-1h0c4 3 9 3 11 7h0c1 3 3 5 5 6v1c-2 0-4-1-6-1l-5-2h-7c-1-1-3-2-4-4h1c1 0 3 1 5 1h0c1 0 4 0 5 1 1 0 1 0 2-1l-1-1c-2 0-4-2-6-3-2 0-5-1-6-3l-4-4z" class="N"></path><path d="M328 346c-1-1-3-2-4-4h1c1 0 3 1 5 1h0l-2 1h1s1 0 1 1h1 1c1 0 2 1 3 1h-7z" class="L"></path><defs><linearGradient id="Z" x1="298.341" y1="255.997" x2="289.549" y2="240.699" xlink:href="#B"><stop offset="0" stop-color="#525252"></stop><stop offset="1" stop-color="#696869"></stop></linearGradient></defs><path fill="url(#Z)" d="M315 223c1 0 1 0 2 1l1 1 2 7h0c-1 4 0 9-1 13v3l-1 3-1-1h-2 0l-2 1h-4v1h-3 0c-1 1-1 1-2 1h0c-2 1-2 0-4 0h-3-2l1 1 1 1h0-1c-3 0-6 1-9 0s-7-1-11-1h-4 5v-1-1h-1c0-1-1 0-1-1-1 0-1 0-2-1 2-1 3-1 4-1l3-3c1-1 2-1 4-2l-1-1c2-1 6-4 6-6 1 0 1-1 2-2h0c1 1 2 0 3-1 2-2 7-5 10-7s7-4 11-4z"></path><path d="M301 244h3l-1 1c-1 1-2 0-3 0 0 1-1 0-1 0h1c1 0 1-1 1-1z" class="O"></path><path d="M301 244l1-1c1-1 2-1 3-1 0 1 0 1-1 2h-3z" class="F"></path><path d="M296 244c2-1 3-1 5-2 1-1 3-2 4-3-2 3-4 3-7 5-2 1-3 2-4 2-2 0-4 0-6 1l4-3h1l1-1 1 1h1z" class="U"></path><path d="M292 244c3-3 5-7 9-10l1 1c-1 2-3 3-4 5-1 1-2 2-2 3v1h-1l-1-1-1 1h-1z" class="V"></path><path d="M298 240v1c3 0 4-4 7-4 1 0 2-1 3-1l-1 1h0 1 0 1 2c-2 1-4 1-6 2-1 1-3 2-4 3-2 1-3 1-5 2v-1c0-1 1-2 2-3z" class="C"></path><path d="M315 223c1 0 1 0 2 1l1 1v3h0c0 1 0 3-1 4l-1 1c0 1-2 2-3 4l-2 1c0-1 1-1 1-2l-1 1h-2-1 0-1 0l1-1c-1 0-2 1-3 1-3 0-4 4-7 4v-1c1-2 3-3 4-5l-1-1 4-4h1 0c0 2-1 3-2 4h0c1 0 1 1 2 2 1-1 1-1 3-1 1 0 2-1 3-1h0c2-1 3-3 4-4 1 0 1 0 2-1l-1-3-3-3h1z" class="B"></path><path d="M314 223l3 3 1 3c-1 1-1 1-2 1-1 1-2 3-4 4h0c-1 0-2 1-3 1-2 0-2 0-3 1-1-1-1-2-2-2h0c1-1 2-2 2-4h0c2-2 4-4 7-6l1-1z" class="Y"></path><path d="M313 237c0 1-1 1-1 3 1 0 1-1 2-1v1l-1 1h1l-1 2h2c-1 2-3 2-4 3 2 0 3-1 5-1-1 1-3 2-4 2 1 1 2 0 3 0 0 1-2 2-3 3h0 3l-2 1h-4v1h-3c-1-1-3 0-4-1v-1h2v-1h2l2-2c-1 0-1-1-1-1v-1h-1c-1 1 0 2-1 3-1 0-1 1-2 0 1 0 1 0 2-1h0s-1 0-1-1h1v-1h-2l1-1c1-1 1-1 1-2h1c1-1 5-3 5-4l2-1z" class="a"></path><path d="M310 242h3l-1 1c-1 0-2 1-4 1h-3l1-1h0 0c2 0 3-1 4-1zm1 4c2 0 3-1 5-1-1 1-3 2-4 2l-1 1c-1 0-2 0-3 1l-1 1h-1c1-1 2-1 2-3 1 0 2 0 3-1h0z" class="e"></path><path d="M318 225l2 7h0c-1 4 0 9-1 13v3l-1 3-1-1h-2 0-3 0c1-1 3-2 3-3-1 0-2 1-3 0 1 0 3-1 4-2-2 0-3 1-5 1 1-1 3-1 4-3h-2l1-2h-1l1-1v-1c-1 0-1 1-2 1 0-2 1-2 1-3 1-2 3-3 3-4l1-1c1-1 1-3 1-4h0v-3z" class="K"></path><path d="M304 227c3-2 7-4 11-4h-1l-1 1c-3 2-5 4-7 6h-1-1c-2 2-4 3-6 5s-3 5-6 6c-1 2-3 3-5 5h-2c-3 0-5 3-8 3l3-3c1-1 2-1 4-2l-1-1c2-1 6-4 6-6 1 0 1-1 2-2h0c1 1 2 0 3-1 2-2 7-5 10-7z" class="O"></path><path d="M291 235h0c1 1 2 0 3-1l1 1c-1 2-4 5-6 6h-1c-1 0-3 2-4 3l-1-1c2-1 6-4 6-6 1 0 1-1 2-2z" class="C"></path><path d="M304 227c3-2 7-4 11-4h-1l-1 1c-3 2-5 4-7 6h-1-1c-2 2-4 3-6 5s-3 5-6 6l-2 2h0c1-2 3-3 4-4l5-6c2-2 4-3 5-6z" class="E"></path><path d="M146 592v1c2 1 3 3 5 4 2 2 7 5 10 6 1 0 2 1 3 2h-1l9 6c1 1 3 2 4 3-1 0-2-1-3-2l-7-4c-1 0-2-1-3-2h-1v-1c-1-1-3-1-4-2v-1c-2-2-5-2-6-3v1h1 0c1 1 1 1 2 1l3 3c2 0 3 1 3 3 1 0 2 1 2 1l1 1c3 2 6 6 9 8h0c2 1 2 1 3 3 0-1 0-2-1-2h0l-2-2c1 0 1 0 1 1v-1h-1c0-1 0-1-1-1l1-1c0 1 1 1 1 1l1 1s2 1 3 1c0 0 0 1 1 1l14 8 6 4c1 0 2 1 3 2 3 1 6 2 8 4l7 4c5 2 11 3 17 5h2c0 1 0 1 1 1h2 0c2 1 3 1 4 1l17 3h0-1l17 3-1 1h0c3 2 6 2 9 3 5 2 10 5 13 10-1 0-1 0-2-1v1s0 1-1 1c-1-1-2-3-4-4h0l-2-1c-1-1-3-2-3-2h0l7 7h0c0 1 1 1 1 2l1 1h0c3 5 8 10 12 14h0c-4-1-10-10-13-13h-1c1 1 2 3 3 4h-1l-2-2c-1-1-2-2-2-3-5-5-11-9-17-13-3-1-8-2-12-3h-2 0l6-1c-1-1-4-1-6-1-3-1-6-2-10-2-2 1-6 0-7-1-1 0-1 1-2 0h-2c-2-1-5 0-7 0-1-1-2-1-3-1-1-1-1-1-2-1-4-1-7-3-11-4-3-1-5-2-7-3-1-1-2-1-2-1-1 0-2-1-3-1-4-2-9-5-13-7-3-2-6-4-8-6-2-1-4-4-7-4-1 0-1-1-2-2 0 1 4 5 5 6 0 0 0 1 1 1v1h0c-1 0-2-1-2-1h-1c1 1 1 1 1 2h0l-4-4c-1 0-1 0-1-1-2 0-3-2-4-3-2-2-4-5-5-7-1-1-1-1-2-1 0 0-1-1-1-2h1c-2-3-6-6-9-9-2-3-5-6-7-9l1-1z" class="D"></path><path d="M202 632c3 1 6 2 8 4-2 0-3-1-4-1-2-1-3-2-4-3z" class="M"></path><path d="M273 658h4c2 1 4 4 7 4 2 1 3 2 4 4h0c-2-1-3-3-5-4l-1 1c2 2 7 4 8 8-5-5-11-9-17-13z" class="W"></path><path d="M261 655h-2 0l6-1h1c4 2 8 2 12 4 2 1 4 2 6 4-3 0-5-3-7-4h-4c-3-1-8-2-12-3z" class="f"></path><path d="M206 640l-1-1-5-2c-2-2-5-3-7-4-5-3-10-7-15-11h1c1 0 6 4 7 5 6 4 13 7 19 10 3 2 5 2 8 4v1c-1 0-1 0-2-1h-2c0-1-1-1-1-1v1c-1-1-2-1-2-1z" class="M"></path><path d="M361 567l1 3c1 0 2 0 3-1v2h0v-3-2h1c1 4 0 8 1 13h2v4 2 3l-1 1v1 2c-1 1-1 1-1 2v1l-2 4c-1 1-2 2-2 3l-1-1c0-1 0-3 1-4l-1-1c-1 2-1 3-1 4h0c-3 8-8 16-15 21l-1 1c2 1 3 0 5 1-4 3-8 4-13 5l-10 1c-1 1-3 1-3 1h-1v1c-1 0-4-1-5 0h0-2c0-1 0-1 1-1s1-1 2-1v-1l-5 1s1-1 2-1v-1h1c3-1 6-2 8-4h0c1 0 1 0 1-1 1 0 1-1 2-1h1c1 0 1 0 1-1h0-1 0 0c2-1 6-5 6-7 1-1 2-1 3-2 0-1 1-1 1-2h1c2-1 4-4 5-6 2-3 4-5 6-8s3-6 4-9c1-2 2-4 3-7 1-4 3-8 3-12h0z" class="Y"></path><path d="M362 596c0-1 0-3 1-4 0-2 0-7 1-8v9c0 1 0 1-1 1v1 2l-1-1z" class="S"></path><path d="M319 628c3-1 7-1 10-2 8-2 13-11 17-17l1-1c-2 8-10 14-16 19 5-1 10-3 15-6l-1 1c2 1 3 0 5 1-4 3-8 4-13 5l-10 1c-1 1-3 1-3 1h-1v1c-1 0-4-1-5 0h0-2c0-1 0-1 1-1s1-1 2-1v-1z" class="G"></path><defs><linearGradient id="a" x1="345.415" y1="353.067" x2="337.597" y2="362.512" xlink:href="#B"><stop offset="0" stop-color="#292829"></stop><stop offset="1" stop-color="#6d6c6c"></stop></linearGradient></defs><path fill="url(#a)" d="M309 327v-1h1l5 8h1c0 1 5 5 5 5h-1-1v1h-1 0l-1 1 1 1 5 3 3 1h0 2 7l5 2c-1 0-1 1-2 1l1 1c1 1 3 2 4 2l6 2 6 4v2c2 1 4 3 6 5 3 5 5 10 6 16v2h-1-1c1 1 1 2 1 3-1-1-2-3-3-3h0-1l-1-2c-2-4-7-11-12-12l-4-1h-1c-1 0-2-1-3-1-2-1-4-1-5-3h-1s-1 0-1-1h-2c-2-1-5-2-7-3-1-1-3-2-4-3h-1c-2-1-5-4-6-6l-4-4 1-1c-1-1-2-3-3-4v1l-1-1v1h-1v-1-1-5h1v1h1c0-2-1-4-1-7h1 0l1-1v1h0v-3z"></path><path d="M349 354l6 4v2c-3-1-5-3-7-4l1-2zm-21-5l3-1c1 0 2 1 3 1 2 0 3 1 4 2l1 1-11-3z" class="b"></path><path d="M313 342h1l1-1h0 0c1 0 1 1 2 1h1l5 3h0c-1 0-2 0-3-1h-1-2l-3-2c1 2 2 3 4 4v1c-2-2-4-3-5-5z" class="U"></path><path d="M341 353v-1c-1 0-1 0-1-1-1 0 0 0-1-1 1 1 3 2 4 2l6 2-1 2-7-3z" class="c"></path><path d="M326 346h0c2 1 3 1 5 2l-3 1h-1l-2-1-1 1c3 0 5 2 8 3-1 0-4-2-5-2s-2-1-3-1-2-1-3-2l-1-1s1-1 2 0h3 1z" class="O"></path><defs><linearGradient id="b" x1="332.313" y1="344.82" x2="335.272" y2="350.702" xlink:href="#B"><stop offset="0" stop-color="#575858"></stop><stop offset="1" stop-color="#706f70"></stop></linearGradient></defs><path fill="url(#b)" d="M326 346h2 7l5 2c-1 0-1 1-2 1l1 1c1 1 0 1 1 1 0 1 0 1 1 1v1l-2-1-1-1c-1-1-2-2-4-2-1 0-2-1-3-1-2-1-3-1-5-2z"></path><path d="M343 362c9 2 17 6 21 14 1 2 2 4 3 5v2h-1-1c1 1 1 2 1 3-1-1-2-3-3-3h0-1l-1-2c-2-4-7-11-12-12l-4-1-2-2h1l2 1c1 0 1 1 2 1v-1h0v-2l-5-2v-1z" class="H"></path><defs><linearGradient id="c" x1="351.239" y1="365.124" x2="354.05" y2="375.099" xlink:href="#B"><stop offset="0" stop-color="#494748"></stop><stop offset="1" stop-color="#696a68"></stop></linearGradient></defs><path fill="url(#c)" d="M348 365c5 3 10 7 13 12 1 2 3 5 4 6s1 2 1 3c-1-1-2-3-3-3h0-1l-1-2c-2-4-7-11-12-12l-4-1-2-2h1l2 1c1 0 1 1 2 1v-1h0v-2z"></path><defs><linearGradient id="d" x1="331.014" y1="352.175" x2="326.108" y2="358.155" xlink:href="#B"><stop offset="0" stop-color="#494849"></stop><stop offset="1" stop-color="#797978"></stop></linearGradient></defs><path fill="url(#d)" d="M309 327v-1h1l5 8h1c0 1 5 5 5 5h-1-1v1h-1 0l-1 1 1 1h-1c-1 0-1-1-2-1h0 0l-1 1h-1c1 2 3 3 5 5v-1c2 1 4 4 6 5 0 0 0 1 1 1 1 2 5 3 7 4 1 1 2 2 4 3 2 0 6 2 7 3v1l5 2v2h0v1c-1 0-1-1-2-1l-2-1h-1l2 2h-1c-1 0-2-1-3-1-2-1-4-1-5-3h-1s-1 0-1-1h-2c-2-1-5-2-7-3-1-1-3-2-4-3h-1c-2-1-5-4-6-6l-4-4 1-1c-1-1-2-3-3-4v1l-1-1v1h-1v-1-1-5h1v1h1c0-2-1-4-1-7h1 0l1-1v1h0v-3z"></path><path d="M343 363c-1 0-3-2-5-2-3-2-7-3-10-5 1 0 1 1 2 0l-8-5c-1 0-2-2-4-2h0v-1c2 1 4 3 6 4h1c1 2 5 3 7 4 1 1 2 2 4 3 2 0 6 2 7 3v1z" class="a"></path><defs><linearGradient id="e" x1="340.617" y1="367.982" x2="337.397" y2="361.252" xlink:href="#B"><stop offset="0" stop-color="#38363a"></stop><stop offset="1" stop-color="#50514f"></stop></linearGradient></defs><path fill="url(#e)" d="M329 358c5 2 9 6 14 8l2 2h-1c-1 0-2-1-3-1-2-1-4-1-5-3h-1l-4-3 1-1h0c-1 0-1 0-2-1l-1-1z"></path><path d="M306 342v-1-5h1v1c1 2 2 4 4 5 1 1 1 2 2 2v1h0l-1-1c-1 1 0 1 0 2 3 5 8 7 13 10 1 1 3 1 4 2l1 1c1 1 1 1 2 1h0l-1 1 4 3s-1 0-1-1h-2c-2-1-5-2-7-3-1-1-3-2-4-3h-1c-2-1-5-4-6-6l-4-4 1-1c-1-1-2-3-3-4v1l-1-1v1h-1v-1z" class="X"></path><path d="M311 346c1 2 4 6 5 6s1 1 2 2h0 1c2 1 3 2 5 3 2 2 4 3 6 4h-1c-1-1-1-1-2-1h-2c-1-1-3-2-4-3h-1c-2-1-5-4-6-6l-4-4 1-1z" class="O"></path><path d="M309 327v-1h1l5 8h1c0 1 5 5 5 5h-1-1v1h-1 0l-1 1 1 1h-1c-1 0-1-1-2-1h0 0l-1 1h-1c-1-1-3-4-4-4 0 1 2 2 2 4h0c-2-1-3-3-4-5h1c0-2-1-4-1-7h1 0l1-1v1h0v-3z" class="E"></path><path d="M308 330h0l1-1v1h0l5 8c1 1 2 2 4 2l-1 1h0c-1 0-2-1-2-1-1-1-3-2-4-3 0-1-1-1-2-2s-1-4-1-5z" class="B"></path><path d="M309 327v-1h1l5 8h1c0 1 5 5 5 5h-1-1v1h-1 0c-2 0-3-1-4-2l-5-8v-3z" class="T"></path><defs><linearGradient id="f" x1="93.354" y1="67.532" x2="86.352" y2="175.239" xlink:href="#B"><stop offset="0" stop-color="#c3c1c2"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#f)" d="M54 173h-3c1-1 3-1 4-3h1l1-1c1 0 1-1 1-1l1-1c2-1 4-3 5-4 2-1 3-2 5-3 2-2 5-2 8-4 1-1 2-1 3-2 1 0 2-1 3-1 1-1 2-2 4-2 1-1 1-2 2-2s1-1 2-1c1-1 2-1 3-2l1-1c1 0 3-1 3-2l2-2c2-1 5-4 6-6 2-4 5-7 8-11 8-13 10-27 8-41-1-7-6-14-10-18v1h0c1 0 2 0 2 1 7 4 11 11 13 19 1 5 2 11 1 17l-1 10c-1 2-1 5-2 7h0c-1 1-1 2-2 3v5l-4 7-1 1s-1 0-1 1h-1c-1 1-1 3-3 5v1h0 0c-1 1-3 4-4 6l-1-1 2-2h-1l-5 6h1c-1 1-3 2-4 4-2 1-3 2-4 4-3 1-6 2-8 4l-2 1c-2 2-3 2-6 3-9 4-19 6-29 6h0c0-1 1-1 2-1z"></path><path d="M127 103v-8-1h0c0-1 1-2 0-3v-2c0-1 0-2-1-3h0 1c1 5 2 11 1 17v2l-1-1v-1z" class="P"></path><path d="M127 103v1l1 1v-2l-1 10c-1 2-1 5-2 7-1-1-1-1 0-2 0-3 0-5 1-8l1-7z" class="D"></path><path d="M77 167h0c2-1 5-2 7-3h0l-4 2c0 1 0 1-1 2h0c1 0 2-1 3-1 1-1 3-2 4-2v-1c1 0 1 0 2-1h0c1 0 3-2 4-3l7-4c2-1 3-3 5-4h0 1c-1 1-3 2-4 4-2 1-3 2-4 4-3 1-6 2-8 4l-2 1h0 0c-2 1-4 2-5 3-4 1-6 2-9 2l4-2v-1h0z" class="Z"></path><path d="M77 167h0v1l-4 2c3 0 5-1 9-2 1-1 3-2 5-3h0 0c-2 2-3 2-6 3-9 4-19 6-29 6h0c0-1 1-1 2-1 6-1 12-3 18-5 1 0 3 0 5-1z" class="D"></path><defs><linearGradient id="g" x1="234.326" y1="547.514" x2="246.852" y2="503.33" xlink:href="#B"><stop offset="0" stop-color="#434243"></stop><stop offset="1" stop-color="#5d5d5d"></stop></linearGradient></defs><path fill="url(#g)" d="M287 478h1c0 2 0 4 1 5v2 2c-2 6-5 11-10 14l1 1-2 2s0 1-1 1l1 1c-2 2-5 4-7 4l1 1h3l1 3h-2c-1 0-1 1-2 2-4 1-9 2-13 3h-2c-3-1-6-1-9-1 1 0 1 1 1 1h-11c-2 0-4 0-5-1-3-1-10 1-12 2s-3 3-5 3c-1 1-1 1-2 1-1 1-2 1-3 2v1l-1-1c1-1 1-1 1-2-2 2-3 6-4 9h0c0 2 0 5 1 7h-1 0 0c-1 1-1 3-2 3v-3c1-6 2-12 4-17l-2-2c0-1 1-2 2-2 2-3 3-5 6-7h0c-2 1-4 2-6 4 2-4 6-9 10-11 0-1 0-1 1-2 1 0 2-1 3-2 2-1 5-3 8-3h0 0c2 0 3-1 5-1 4 0 7 0 10 1l4 2c0-1 0-1 1-1l-1-2-3-1c-2 0-3 0-5-1h7l1 1h0 3v-1h-1 4l6 2c1 0 2 1 3 1 2 1 6 1 8 1h2 1c1 0 1-1 2-1l2-1-1-1h0c1-1 2-2 3-4 2-1 3-3 3-5 0-1 1-3 2-4h0v-5z"></path><path d="M233 511c0 1 1 1 2 1l1-1h1c-1 1-2 1-3 1h0-2 1c1 1 2 1 3 1-2 1-3 0-5 0s-4-1-5-1c2 0 4-1 7-1z" class="c"></path><path d="M209 519h1c1-1 3-3 5-4-2 3-4 5-6 8l-2-2c0-1 1-2 2-2z" class="M"></path><path d="M211 524c2-1 3-2 4-3 11-7 22-4 33-3 1 0 1 1 1 1h-11c-2 0-4 0-5-1-3-1-10 1-12 2s-3 3-5 3c-1 1-1 1-2 1-1 1-2 1-3 2v1l-1-1c1-1 1-1 1-2z" class="I"></path><defs><linearGradient id="h" x1="248.644" y1="500.21" x2="247.461" y2="514.451" xlink:href="#B"><stop offset="0" stop-color="#444"></stop><stop offset="1" stop-color="#797878"></stop></linearGradient></defs><path fill="url(#h)" d="M242 507l11 2c9 1 19-2 26-8l1 1-2 2s0 1-1 1l1 1c-2 2-5 4-7 4s-4 0-5 1c-1 0-2-1-2 0-1 0-3 1-4 1s-2 0-2 1h-4l-8 1h0 4c-3 2-11 0-14-1-1 0-2 0-3-1h-1 2 0c1 0 2 0 3-1h-1l-1 1c-1 0-2 0-2-1h0c-2-1-4-1-6-1-1 0-2 1-3 0 1 0 2-1 3-1h0c-1 0-2 0-3 1h-1v1h-5s-1 0-2 1h-1 0c-2 1-4 2-6 4 2-4 6-9 10-11 5-1 10-3 14-2 3 1 7 1 10 2-1 1-2 1-3 1 0 0 1 1 2 1z"></path><defs><linearGradient id="i" x1="219.452" y1="503.553" x2="229.007" y2="510.986" xlink:href="#B"><stop offset="0" stop-color="#2f2d2e"></stop><stop offset="1" stop-color="#484a49"></stop></linearGradient></defs><path fill="url(#i)" d="M219 505c5-1 10-3 14-2 3 1 7 1 10 2-1 1-2 1-3 1 0 0 1 1 2 1h-2c-1-1-8-1-10 0h-1c-2 1-4 1-5 1l-6 3s-1 0-2 1h-1 0c-2 1-4 2-6 4 2-4 6-9 10-11z"></path><path d="M215 512c0-1 2-2 3-2 2-1 5-2 8-3 2-1 4 0 6-1 3-1 5-1 8 0 0 0 1 1 2 1h-2c-1-1-8-1-10 0h-1c-2 1-4 1-5 1l-6 3s-1 0-2 1h-1 0z" class="F"></path><defs><linearGradient id="j" x1="278.28" y1="503.455" x2="254.491" y2="486.247" xlink:href="#B"><stop offset="0" stop-color="#2a292a"></stop><stop offset="1" stop-color="#595859"></stop></linearGradient></defs><path fill="url(#j)" d="M287 478h1c0 2 0 4 1 5v2 2c-2 6-5 11-10 14-7 6-17 9-26 8l-11-2c-1 0-2-1-2-1 1 0 2 0 3-1-3-1-7-1-10-2-4-1-9 1-14 2 0-1 0-1 1-2 1 0 2-1 3-2 2-1 5-3 8-3h0 0c2 0 3-1 5-1 4 0 7 0 10 1l4 2c0-1 0-1 1-1l-1-2-3-1c-2 0-3 0-5-1h7l1 1h0 3v-1h-1 4l6 2c1 0 2 1 3 1 2 1 6 1 8 1h2 1c1 0 1-1 2-1l2-1-1-1h0c1-1 2-2 3-4 2-1 3-3 3-5 0-1 1-3 2-4h0v-5z"></path><path d="M285 487v3h0c0-1 1-1 1-2 0 3-3 6-4 8l-2 1-1-1h0c1-1 2-2 3-4 2-1 3-3 3-5z" class="C"></path><path d="M250 497c3 0 5 2 7 2-1 1-2 1-3 1-1 1-3 0-4 0 0-1 0-1 1-1l-1-2z" class="X"></path><path d="M252 495h4l6 2c1 0 2 1 3 1 2 1 6 1 8 1-1 1-4 1-5 1v1c-2 0-4 0-5-1h-2v-2c-1 0-2 0-3-1h-1l1 1h-1l1 1h-1c-2 0-4-2-7-2l-3-1c-2 0-3 0-5-1h7l1 1h0 3v-1h-1z" class="E"></path><path d="M252 495h4c0 1-1 1-2 1h-4 3v-1h-1z" class="F"></path><path d="M231 498h0c2 0 3-1 5-1 4 0 7 0 10 1 0 1-1 1-1 1 5 1 9 2 13 5l-5-1-1 1h2c-1 1-1 1-1 2-4 0-7-1-10-1-3-1-7-1-10-2-4-1-9 1-14 2 0-1 0-1 1-2 1 0 2-1 3-2 2-1 5-3 8-3h0z" class="O"></path><path d="M231 498h0c2 0 3-1 5-1 4 0 7 0 10 1 0 1-1 1-1 1-2 0-5 0-6 1h2v1h-7c1-1 2-1 3-1v-1h-4c0-1-1-1-2-1h0z" class="E"></path><path d="M223 501c2-1 5-3 8-3 1 0 2 0 2 1h4v1c-1 0-2 0-3 1-3 0-9 2-11 1v-1z" class="C"></path><defs><linearGradient id="k" x1="242.59" y1="348.53" x2="256.958" y2="318.063" xlink:href="#B"><stop offset="0" stop-color="#353535"></stop><stop offset="1" stop-color="#595859"></stop></linearGradient></defs><path fill="url(#k)" d="M262 314c5 0 11 1 15 2l4 2 1 1h-2-1v1l5 1h4 2l1 1v1c-1 1-1 2-2 2h-1l-1 2h0 1c0 1-1 2-1 2l-3 3v2h1l-2 1c-1 2-4 3-6 3-2 2-4 3-6 4-1 1-2 1-3 2-2 1-3 2-6 2h0-1c-5 1-10 3-14 6l-3-1c-1 0-4 2-5 2s-2 0-3 1c-1 0-2 1-2 2h-1v-1-1h-2 0c0-1 0-2 1-2 1-2 3-3 5-5l-1-1-5 5 7-11h-1c-4 2-5 6-7 10l1-2c0-3 1-7 2-10 1-5 5-10 8-14 2-2 3-4 5-6 2 1 8-3 11-3 2-1 3-1 5-1z"></path><path d="M264 325h1 3 1c-1 1-5 2-6 2h0v-1c-4 0-8 1-12 1h0 1c1-1 3-1 4-1h1c1-1 3 0 5 0h0c1-1 1-1 2-1h0z" class="F"></path><path d="M270 324h0c3-1 5-1 7-1-5 3-12 6-19 5h3c-1-1-2-1-3-1h5 0c1 0 5-1 6-2h-1-3c1-1 3 0 5-1z" class="a"></path><defs><linearGradient id="l" x1="285.003" y1="325.48" x2="270.863" y2="319.421" xlink:href="#B"><stop offset="0" stop-color="#626263"></stop><stop offset="1" stop-color="#828081"></stop></linearGradient></defs><path fill="url(#l)" d="M279 320l5 1h4c-1 1-1 2-2 3h-1c0-1-1-1-2-1-2-1-4 0-6 0s-4 0-7 1h0c-1-1-1-1-1-2 2 0 4-1 6-1h1c1 0 2 0 3-1z"></path><path d="M268 321l1-1h10c-1 1-2 1-3 1h-1c-2 0-4 1-6 1 0 1 0 1 1 2-2 1-4 0-5 1h-1c-4-2-9 1-13 0h0c2-2 5-2 7-2 3-1 6-2 10-2z" class="c"></path><path d="M288 321h2l1 1v1c-1 1-1 2-2 2h-1 0-3c-2 1-4 1-5 2h-1c-3 1-6 5-10 6v-2l1-1h0c-2-1-3 0-5 1h0c1-2 4-3 6-4 4-1 8-3 12-4 1 0 2 0 2 1h1c1-1 1-2 2-3z" class="Q"></path><path d="M271 327v1h2 2v-1h1 2 0c-1 1-6 3-8 3-2-1-3 0-5 1h0c1-2 4-3 6-4z" class="X"></path><path d="M283 323c1 0 2 0 2 1-1 0-6 2-7 3h0-2-1v1h-2-2v-1c4-1 8-3 12-4z" class="a"></path><defs><linearGradient id="m" x1="253.057" y1="344.854" x2="255.318" y2="327.84" xlink:href="#B"><stop offset="0" stop-color="#2c2b2d"></stop><stop offset="1" stop-color="#4d4d4d"></stop></linearGradient></defs><path fill="url(#m)" d="M265 331c2-1 3-2 5-1h0l-1 1v2l-27 10h-3l2-2c2-3 5-4 9-5 1 0 3-1 4-2h3c2 0 7-1 8-3z"></path><path d="M262 314c5 0 11 1 15 2l4 2 1 1h-2-1v1h-10l-1 1-1-1c-3 1-5 0-8 0s-6 1-9 1c0-1 3-2 4-3l-1-1h-1c1 0 4-1 5-2h0c2-1 3-1 5-1z" class="O"></path><path d="M277 316l4 2 1 1h-2-1l-8-1 8-1-2-1z" class="F"></path><defs><linearGradient id="n" x1="278.317" y1="320.85" x2="267.473" y2="318.056" xlink:href="#B"><stop offset="0" stop-color="#353335"></stop><stop offset="1" stop-color="#5e5d5e"></stop></linearGradient></defs><path fill="url(#n)" d="M267 320v-1c2 0 3-1 4-1l8 1v1h-10l-1 1-1-1z"></path><defs><linearGradient id="o" x1="250.34" y1="331.598" x2="273.582" y2="347.077" xlink:href="#B"><stop offset="0" stop-color="#3f3f3f"></stop><stop offset="1" stop-color="#5b5a5b"></stop></linearGradient></defs><path fill="url(#o)" d="M285 325h3 0l-1 2h0 1c0 1-1 2-1 2l-3 3v2h1l-2 1c-1 2-4 3-6 3-2 2-4 3-6 4-1 1-2 1-3 2-2 1-3 2-6 2h0-1c-5 1-10 3-14 6l-3-1c-1 0-4 2-5 2s-2 0-3 1c-1 0-2 1-2 2h-1v-1-1h-2 0c0-1 0-2 1-2 1-2 3-3 5-5l-1-1c2-2 4-2 5-3h1l27-10c4-1 7-5 10-6h1c1-1 3-1 5-2z"></path><path d="M261 346l1-1c2-1 4-1 6-1-2 1-3 2-6 2h0-1z" class="U"></path><path d="M277 338h0c1-1 2-1 4-2 1-1 1-2 2-2v1c-1 2-4 3-6 3z" class="F"></path><path d="M248 342c1 0 2 0 4-1 0 0 1 1 2 1-2 1-5 2-7 2-2 1-4 3-6 4l-1-1 8-5z" class="X"></path><path d="M240 347l1 1c-3 2-5 4-8 7v-1h-2 0c0-1 0-2 1-2s2-2 3-2c2-1 4-2 5-3z" class="G"></path><defs><linearGradient id="p" x1="262.479" y1="327.822" x2="271.41" y2="338.382" xlink:href="#B"><stop offset="0" stop-color="#555"></stop><stop offset="1" stop-color="#747373"></stop></linearGradient></defs><path fill="url(#p)" d="M285 325l2 1h0c-4 2-8 5-12 7-5 3-11 6-17 7-1 1-3 2-4 2s-2-1-2-1c-2 1-3 1-4 1-3 2-5 3-8 5-1 1-3 2-5 3-1 0-2 2-3 2 1-2 3-3 5-5l-1-1c2-2 4-2 5-3h1l27-10c4-1 7-5 10-6h1c1-1 3-1 5-2z"></path><path d="M237 347l3-2c1 0 1-1 1-1 1 0 2-1 3-1s2-1 4-1l-8 5c-1 1-3 2-5 3-1 0-2 2-3 2 1-2 3-3 5-5z" class="U"></path><path d="M305 316l3-2c-1 2-2 4-4 5-1 0-1 1-2 2l-1 1v2h0c1-1 2-2 2-3l1 1c-2 4-5 7-6 11l-4 12c-1 3-2 6-4 9-4 7-9 10-16 14-3 2-6 3-9 5-4 2-8 3-11 6 0 0-1 0-1 1-7 3-12 10-15 16 0 2-1 4-2 6l1-11v-3h-1l1-4 1-1h-2c0-1 1-3 1-5 1-2 1-4 2-5 2-3 3-4 5-6h0l-1-1c-2 1-3 3-5 5 0 0-1 1-2 1 0 1 0 1-1 2 1-3 3-6 4-8l2-1 2-3h-1c-1 0-2 0-3 1-1 0-1 0-2-1l1-2h2v-1l-5 2h0c1-2 3-3 5-4h0-3c2-2 7-4 10-5 4-3 9-5 14-6h1 0c3 0 4-1 6-2 1-1 2-1 3-2 2-1 4-2 6-4 2 0 5-1 6-3l2-1h-1v-2l3-3s1-1 1-2h-1 0l1-2h1c1 0 1-1 2-2v-1l-1-1h1 1 0c1 1 3 1 5 1 3-1 6-3 8-5v-1z" class="L"></path><path d="M237 384h2 0l-2 7v-3h-1l1-4z" class="H"></path><path d="M301 324h0c1-1 2-2 2-3l1 1c-2 4-5 7-6 11l-4 12c-1 3-2 6-4 9l-1 1v-1c2-2 4-7 4-10h0c1 0 1 0 1-1v-1c1-1 2-4 2-5 1-2 1-4 2-5 0-1 1-3 2-4h0v-1c-1 1-2 3-3 4v2c-1 2-3 4-4 6h0v-1h0c1-1 3-4 3-5v-2c1-1 2-2 2-3 0 0 1-1 1-2l2-2z" class="I"></path><path d="M257 359c10-4 22-7 30-16 2-2 4-5 5-7s2-3 4-5h0v2c0 1-2 4-3 5h0v1c-2 1-2 3-3 5-1 1-3 3-5 4-2 2-4 5-7 6v1 1h0 1c-2 3-5 4-8 5h0-1c-1 1-1 2-2 3l-1 1c-2 0-3 1-5 2-5 3-11 7-15 11-1 0-1 0-2-1h-2l-4 7h0-2l1-1h-2c0-1 1-3 1-5 1-2 1-4 2-5 2-3 3-4 5-6 3-1 7-4 11-5h-2 0c1-1 2-2 4-3z" class="C"></path><path d="M285 348c1-2 3-3 4-5s2-3 3-5h1v1c-2 1-2 3-3 5-1 1-3 3-5 4h0z" class="J"></path><path d="M285 348h0c-2 2-4 5-7 6v1 1h0 1c-2 3-5 4-8 5h0c-1 0-2 0-3-1 2 0 2-1 4-1v-1h-1c-2 1-4 1-6 3h0c-4 3-9 4-13 7l-3 2h-1c1-2 3-2 5-3 1-1 2-2 4-3 1-1 5-2 6-3-3 1-6 2-8 3v-1-1c3 0 5-2 8-3 8-2 16-5 22-11z" class="O"></path><defs><linearGradient id="q" x1="250.189" y1="365.481" x2="262.614" y2="372.443" xlink:href="#B"><stop offset="0" stop-color="#383838"></stop><stop offset="1" stop-color="#5d5c5d"></stop></linearGradient></defs><path fill="url(#q)" d="M265 361h0c2-2 4-2 6-3h1v1c-2 0-2 1-4 1 1 1 2 1 3 1h-1c-1 1-1 2-2 3l-1 1c-2 0-3 1-5 2-5 3-11 7-15 11-1 0-1 0-2-1h-2c1-2 3-5 6-7l3-2c4-3 9-4 13-7z"></path><path d="M265 361v1c-1 1-4 2-6 3l-3 3h-3-1c4-3 9-4 13-7zm-10 3c2-1 5-2 8-3-1 1-5 2-6 3-2 1-3 2-4 3-2 1-4 1-5 3h1c-3 2-5 5-6 7l-4 7h0-2l1-1h-2c0-1 1-3 1-5 1-2 1-4 2-5 2-3 3-4 5-6 3-1 7-4 11-5h0v1 1z" class="V"></path><path d="M248 370h1c-3 2-5 5-6 7l-4 7h0-2l1-1c2-5 5-9 10-13z" class="M"></path><path d="M255 362h0v1 1c-2 2-4 2-7 3s-6 3-8 6h-1c2-3 3-4 5-6 3-1 7-4 11-5z" class="F"></path><defs><linearGradient id="r" x1="271.121" y1="332.041" x2="279.353" y2="348.546" xlink:href="#B"><stop offset="0" stop-color="#3f3e3f"></stop><stop offset="1" stop-color="#6c6c6c"></stop></linearGradient></defs><path fill="url(#r)" d="M305 316l3-2c-1 2-2 4-4 5-1 0-1 1-2 2l-1 1v2l-2 2c0 1-1 2-1 2 0 1-1 2-2 3h0c-2 2-3 3-4 5s-3 5-5 7c-8 9-20 12-30 16-2 1-3 2-4 3h0 2c-4 1-8 4-11 5h0l-1-1c-2 1-3 3-5 5 0 0-1 1-2 1 0 1 0 1-1 2 1-3 3-6 4-8l2-1 2-3h-1c-1 0-2 0-3 1-1 0-1 0-2-1l1-2h2v-1l-5 2h0c1-2 3-3 5-4h0-3c2-2 7-4 10-5 4-3 9-5 14-6h1 0c3 0 4-1 6-2 1-1 2-1 3-2 2-1 4-2 6-4 2 0 5-1 6-3l2-1h-1v-2l3-3s1-1 1-2h-1 0l1-2h1c1 0 1-1 2-2v-1l-1-1h1 1 0c1 1 3 1 5 1 3-1 6-3 8-5v-1z"></path><path d="M241 365c3 0 4-2 7-3 1-1 3-1 5-2h0c1 0 2 0 4-1-2 1-3 2-4 3-2 1-5 2-7 3h-2c-2 1-3 1-5 1l2-1z" class="T"></path><path d="M239 366c2 0 3 0 5-1h2c2-1 5-2 7-3h0 2c-4 1-8 4-11 5h0l-1-1c-2 1-3 3-5 5 0 0-1 1-2 1 0 1 0 1-1 2 1-3 3-6 4-8z" class="J"></path><path d="M240 359c1-1 3-2 4-2 1-1 2-2 3-2 4-2 8-4 12-5 3-1 6-1 8-2 5-2 9-5 13-8 0 1 1 1 2 1-1 0-5 5-6 6l-6 3c-3 1-6 3-10 3l-6 3c-1 1-3 2-5 2-3 1-5 2-7 3v1c-1 0-2 0-3 1-1 0-1 0-2-1l1-2h2v-1z" class="F"></path><path d="M248 358l4-3c3-1 6-2 8-2l-6 3c-1 1-3 2-5 2h0-1z" class="T"></path><path d="M240 360l1-1c2 0 3-1 4-2l1 1h0 2 1 0c-3 1-5 2-7 3v1c-1 0-2 0-3 1-1 0-1 0-2-1l1-2h2z" class="J"></path><defs><linearGradient id="s" x1="246.239" y1="352.002" x2="282.819" y2="338.759" xlink:href="#B"><stop offset="0" stop-color="#272727"></stop><stop offset="1" stop-color="#535353"></stop></linearGradient></defs><path fill="url(#s)" d="M285 334c1-1 0 0 1-2l2-2h1l-2 2h2 0l2-1v1h0-1l-3 3c-1 0-2 1-3 1l-4 4c-4 3-8 6-13 8-2 1-5 1-8 2-4 1-8 3-12 5-1 0-2 1-3 2-1 0-3 1-4 2l-5 2h0c1-2 3-3 5-4h0-3c2-2 7-4 10-5 4-3 9-5 14-6h1 0c3 0 4-1 6-2 1-1 2-1 3-2 2-1 4-2 6-4 2 0 5-1 6-3l2-1z"></path><defs><linearGradient id="t" x1="240.487" y1="616.035" x2="178.251" y2="570.869" xlink:href="#B"><stop offset="0" stop-color="#3a3a3b"></stop><stop offset="1" stop-color="#737273"></stop></linearGradient></defs><path fill="url(#t)" d="M259 587l2 1h-1l1 1c-1 1-2 1-2 1l-1 1c-3 2-5 3-7 6-4 2-9 5-13 7-2 1-3 1-5 2v1h0c-2 1-5 3-8 3 1 1 1 2 2 2s2 0 3 1c2 0 4-1 5 1-1 1-4 1-5 3h1l1-1 1 1c-10 3-21 5-32 5-1 0-4-1-5 0v1c-2-1-6-2-8-3l-12-6c-1-1-3-2-4-3l-9-6h1c5 2 11 4 18 4h0l-12-6-12-6c0-1-1-1-1-2h2c-1-1-2-2-4-2 2 0 3-1 4 0 1 0 3 1 4 0h0-2v-1h4c2-1 6-1 8-1h7c1 0 2 0 3 1l13-1c2 1 4 2 6 2h4 7-2c7 0 13 0 20 1 1 1 2 0 4 1 1 0 3 0 4 1h3c6-1 12-5 17-9z"></path><path d="M181 611c2 0 4 2 5 2 1 1 2 1 3 1v-1l1 1c2 1 6 1 9 1-2 0-5 1-6 0h-2 0c1 1 2 1 2 1 2 1 3 0 5 0 1 1 4 1 5 2h-7c-1 0-2 1-4 0h-1-2 0c-2 0-3-1-4-2 2 1 4 2 7 1h0c-2-2-9-3-11-6z" class="C"></path><path d="M183 592l13-1c2 1 4 2 6 2h4l-1 1c-1 0-1-1-2 0h0c1 1 4 0 6 1h-5l-12-1c-2 0-5 0-8-1h1c2 1 4 0 6 1h5v-1l-1-1c-2 0-6 1-8 0h-4z" class="e"></path><path d="M219 600c1 0 3-1 5 0h0c1 0 2 0 3-1h3 1 1 2c3-1 5-4 8-2h2 1 0c-4 1-8 4-12 5h-3 0c1 0 2-1 3-1h-2c-3 1-8 0-11-1h-1z" class="B"></path><defs><linearGradient id="u" x1="232.301" y1="607.309" x2="211.447" y2="610.678" xlink:href="#B"><stop offset="0" stop-color="#404041"></stop><stop offset="1" stop-color="#5e5e5e"></stop></linearGradient></defs><path fill="url(#u)" d="M225 610h-1c-4 2-9 2-13 2-4 1-7 1-11 0 1 0 3-1 4-1 1-1 1-1 2-1h1c1-1 2-1 3-1v1l-7 2h0 3 2 1c1-1 3-1 4-2 1 0 3 0 5-1l4-1h1c1-1 1-1 2-1h1s1 0 1-1h0 4c2-1 4-1 7-2-2 1-3 1-5 2v1h0c-2 1-5 3-8 3z"></path><path d="M176 614c-1-1-3-2-4-3l-9-6h1c5 2 11 4 18 4h0 2c3 1 7 1 10 1-3 1-7 0-10 0h-4c-1 0-1-1-1-1h-2c2 1 3 1 4 2 2 3 9 4 11 6h0c-3 1-5 0-7-1h-2l5 3h1l-1 1-12-6z" class="L"></path><defs><linearGradient id="v" x1="198.404" y1="594.838" x2="196.907" y2="603.693" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#474647"></stop></linearGradient></defs><path fill="url(#v)" d="M155 593c2 0 3-1 4 0 1 0 3 1 4 0l56 7h1c3 1 8 2 11 1h2c-1 0-2 1-3 1h0c-4 2-8 4-12 4-8 2-16 3-24 4-3 0-7 0-10-1h-2l-12-6-12-6c0-1-1-1-1-2h2c-1-1-2-2-4-2z"></path><path d="M155 593c2 0 3-1 4 0 3 2 7 2 10 3h-2-3-1 1v1l-5-2c-1-1-2-2-4-2z" class="J"></path><path d="M158 597c0-1-1-1-1-2h2l5 2c10 3 21 6 32 7 3 1 7 1 10 1h8 5l-1 1c-8 2-16 3-24 4-3 0-7 0-10-1h-2l-12-6-12-6z" class="d"></path><defs><linearGradient id="w" x1="116.264" y1="606.138" x2="108.288" y2="693.681" xlink:href="#B"><stop offset="0" stop-color="#d8d7d7"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#w)" d="M145 653c1-1 1-2 2-3l2-6c1-3 1-8 2-11 0-5 1-9 1-13v-6l1-1c1 1 0 2 1 3 0 2 0 4 1 5h1c0-1 0-2-1-3v-1c0-3-2-8-3-11 1 1 2 3 3 3l1 1c1 1 1 1 1 2-1 1 0 3 0 4v1l1 1v6c0 3 1 6 1 9 0 12-1 26-7 37-1 2-3 4-4 6 0 1-1 1-2 2 0 1-1 2-2 2l-3 3c-2 2-4 4-7 5-6 3-12 6-19 6v-1c-3 1-6 1-9 1-5 0-11 0-16-2-3 0-6-2-9-2-4-2-7-3-10-6-4-3-5-5-6-9-1-1-1-2-1-4v-1h0c1-1 1-2 1-3s1-1 1-2h0l3-3c2-2 6-4 9-3 2 0 4 0 5 1l1 1c1 0 1 0 2 1 0 4 3 9 6 12l2 1h1c1 1 3 2 5 2 2 1 5 2 7 2 1 0 2 0 3 1 4 0 9 0 13-1s7-3 11-5v-1l1 1c1 0 1-1 1-1h0c0-2 4-5 5-7l3-3v-1l-1 1v-1c0-2 2-5 3-7-1 0-1-1-1-1v-1z"></path><path d="M83 672l1 3c-1 0-1 1-1 2 0-1 0-1-1-1v-2-1l1-1z" class="H"></path><path d="M87 679c0-2-1-3-1-5 0-1 0-2 1-3 0 2 0 3 1 5 0 2 2 4 3 6-1-1-3-2-4-3z" class="D"></path><path d="M155 639h0v3h0c1 1 1 2 1 3 0 4-2 9-3 13 0 0-1 0-1-1l1-2c1-3 3-8 2-12v-1l-1-1s0-1 1-2z" class="M"></path><path d="M82 665l1 2-1 1c0 2 1 2 0 3h-1v-1h1l-1-1h-1l-1 3h-1l1-3h-1c1-1 2-3 4-4z" class="H"></path><path d="M73 665h-1 0c1-2 4-3 6-4 2 1 4 1 5 3h0-2-1c-1 0-3 0-5 1v-1c-1 1-1 1-2 1z" class="G"></path><path d="M73 665l1-1c1-1 2-1 4-1 1 0 3 0 3 1h-1c-1 0-3 0-5 1v-1c-1 1-1 1-2 1z" class="H"></path><path d="M83 672v-1h1c2-2 1-4 2-6 0 2 0 4 1 6-1 1-1 2-1 3 0 2 1 3 1 5-1-1-2-3-3-4l-1-3z" class="G"></path><path d="M146 655v2 2c1-1 1-2 2-3 0-1 0-2 1-2v1c0 1-1 3-2 5v1c-1 1-1 1-1 2 0-1 0-1-1-1-1 1-2 3-4 4l3-3v-1l-1 1v-1c0-2 2-5 3-7z" class="M"></path><path d="M123 679c4-1 7-3 11-5v-1l1 1c-4 4-12 6-18 8-2-1-6-1-7-2 4 0 9 0 13-1z" class="D"></path><path d="M115 693c4 0 9-1 13-3 1-1 3-2 5-2 3-1 4-3 7-5 1 0 2-2 4-3h0l-3 3c-2 2-4 4-7 5-6 3-12 6-19 6v-1zm-20-18c1 1 3 2 5 2 2 1 5 2 7 2 1 0 2 0 3 1s5 1 7 2c-2 1-4 1-7 1-5-1-13-4-16-8h1z" class="H"></path><path d="M75 665c2-1 4-1 5-1l2 1c-2 1-3 3-4 4 0 2 1 4 0 5l-2-2v1l1 5c-3-3-3-4-3-7h-1c1 2 1 6 1 8-1-1-2-3-2-5-1-5 0-6 3-9z" class="G"></path><path d="M77 666l1 1h-1c-2 2-2 4-2 6h0c-1 0-1-1-1-2 0-2 2-3 3-5z" class="D"></path><defs><linearGradient id="x" x1="269.754" y1="451.601" x2="239.326" y2="467.95" xlink:href="#B"><stop offset="0" stop-color="#1d1e1e"></stop><stop offset="1" stop-color="#393838"></stop></linearGradient></defs><path fill="url(#x)" d="M298 333c1-1 2-2 3-4 0 1-1 2-1 2v4c-1 1 0 3 0 4h0 0 2v4 3 1c0 1 1 1 1 2v2c-1 2 0 4 0 6h0c-1 2-1 3-1 5 0 4-1 10-3 13h-3v-2h-1l-2 4c-2 2-3 4-4 6l-3 5h0-1l-2 1h0c-3 2-5 6-7 9-1 1-2 2-2 4h-1v-2h-1c0-1 1-3 1-4h0l-1 1h0c-3 3-5 7-7 11l-1 1h0c-1 0-1 0-1-1-2 1-2 4-3 5h0-1c0-1 0-1-1-1h-1c0 2 0 5-1 7-1 4-1 10 0 14 0 2 1 4 1 6h-1c0 4 2 9 3 12v1l-2-2c-1 1-1 0-2 1l1 2c-1 0-1-1-1-2h-1c1 4 1 8 2 11 0 2 0 5 1 6l1 4 1 1h1 1l2 2 4 4h3c1 1 3 2 5 4h0l2 1c1 1 3 3 3 5s-2 5-2 6h0c0 1 0 1-1 2h1v-1h1l1 1-2 1c-1 0-1 1-2 1h-1-2c-2 0-6 0-8-1-1 0-2-1-3-1l-6-2h-4l-2-1-1-1h4v-1l-2-1c1-2 0-4-1-6l-1-2h0l-1-1c0-5-2-10-3-14 0-3-1-6-1-9-1-10 1-21 2-31v-8h-1v2h-1c0-1 0-2 1-4h-1l1-2c0-1 1-3 2-4l5-12c-1-1 0-3 1-4h0c1-1 1-2 2-3v-2c5-7 12-11 20-15l-3-2c-2 1-4 2-7 4l-1-1c2-1 4-3 5-4 0-1 0-1-1-1h0c2-1 5-2 6-4 7-4 12-7 16-14 2-3 3-6 4-9l4-12z"></path><path d="M247 412c0 1 0 1 1 1-1 1-1 2-2 3v1l-1 1h-1l1-2c0-1 1-3 2-4z" class="L"></path><path d="M252 457l1-1v2c0 2 1 6 0 8s0 7 0 9h0c0-1-1-2-1-3 0-2 1-4 0-5v-1-3-6z" class="E"></path><path d="M245 468h1c0 1 1 2 1 4h1c0-1 1-3 0-5h0-1 0 0c1-6 0-11 0-17l1-1 2 36-1-2h0l-1-1c0-5-2-10-3-14z" class="Q"></path><path d="M251 408l2-2v3h1c-1 4-2 8-2 12v1 1c0 2-1 4-1 6l-2 2h0c-1-6 0-12 0-18 0-1 2-4 2-5z" class="O"></path><path d="M251 408l2-2v3l-2 8h-1v-3c0-1 0-1-1-1 0-1 2-4 2-5z" class="C"></path><defs><linearGradient id="y" x1="256.783" y1="448.789" x2="251.177" y2="468.078" xlink:href="#B"><stop offset="0" stop-color="#1c1b1c"></stop><stop offset="1" stop-color="#383838"></stop></linearGradient></defs><path fill="url(#y)" d="M251 442c0 1 1 2 2 2 0 2 0 3 1 5 1 0 1 1 1 2h-1c1 4 1 8 2 11 0 2 0 5 1 6l1 4h0l-1-1h0v-2h0l-1 2-1-1c-1-1-1-2-1-2 0-1-1-2-1-2 1-2 0-6 0-8v-2l-1 1h0v-6h-1v-9z"></path><path d="M252 451l1 2v5-2l-1 1h0v-6z" class="C"></path><path d="M251 442c0 1 1 2 2 2 0 2 0 3 1 5 1 0 1 1 1 2h-1c0-1 0-2-1-4l-1 1c0 1 0 3 1 5l-1-2h-1v-9z" class="L"></path><defs><linearGradient id="z" x1="258.008" y1="438.154" x2="249.384" y2="436.977" xlink:href="#B"><stop offset="0" stop-color="#343532"></stop><stop offset="1" stop-color="#6c6a6c"></stop></linearGradient></defs><path fill="url(#z)" d="M252 422l1-1v3c-1 4-1 9 1 13 0 1 0 2 1 3v3l2 7c-1 1-1 0-2 1l1 2c-1 0-1-1-1-2s0-2-1-2c-1-2-1-3-1-5-1 0-2-1-2-2l-2-11h0l2-2c0-2 1-4 1-6v-1z"></path><path d="M255 440v3 1l-1-1c0-1 0-2 1-3z" class="E"></path><defs><linearGradient id="AA" x1="253.257" y1="441.821" x2="248.673" y2="432.528" xlink:href="#B"><stop offset="0" stop-color="#383937"></stop><stop offset="1" stop-color="#5a595b"></stop></linearGradient></defs><path fill="url(#AA)" d="M251 429v5l2 10c-1 0-2-1-2-2l-2-11h0l2-2z"></path><path d="M257 406v1c-2 4-3 9-2 13 1-2 1-6 2-8 0 2 0 5-1 7-1 4-1 10 0 14 0 2 1 4 1 6h-1c0 4 2 9 3 12v1l-2-2-2-7v-3c-1-1-1-2-1-3-2-4-2-9-1-13v-3-2c0-5 2-9 4-13z" class="F"></path><path d="M253 419l1-3c1 2 0 4 0 6l1 11v6h0l-1-2h0c-2-4-2-9-1-13v-3-2z" class="a"></path><defs><linearGradient id="AB" x1="264.503" y1="405.994" x2="259.957" y2="382.952" xlink:href="#B"><stop offset="0" stop-color="#3a3a3b"></stop><stop offset="1" stop-color="#666464"></stop></linearGradient></defs><path fill="url(#AB)" d="M255 393v1h1c1-2 3-4 4-5 2-1 3-2 4-3 3-2 7-5 11-6l1 1h0c0 1-2 3-3 4-2 1-3 2-3 4-2 2-4 3-5 5-1 0-2 1-2 2l-3 4h-1c-1 0-1 1-1 1 0 2-1 4-1 5-2 4-4 8-4 13v2l-1 1v-1c0-4 1-8 2-12h-1v-3l-2 2h0l-3 5c-1 0-1 0-1-1l5-12c-1-1 0-3 1-4h0c1-1 1-2 2-3z"></path><path d="M251 408c1-4 3-6 5-9h0 1c-1 1-2 3-3 5 0 0-1 1-1 2l-2 2h0z" class="B"></path><defs><linearGradient id="AC" x1="258.837" y1="395.059" x2="263.087" y2="390.099" xlink:href="#B"><stop offset="0" stop-color="#565556"></stop><stop offset="1" stop-color="#6c6b6c"></stop></linearGradient></defs><path fill="url(#AC)" d="M268 386h0c-1 3-6 5-6 8l-2 3-2-2h0c0-3 7-7 10-9z"></path><defs><linearGradient id="AD" x1="266.511" y1="394.329" x2="269.491" y2="382.488" xlink:href="#B"><stop offset="0" stop-color="#59595c"></stop><stop offset="1" stop-color="#797876"></stop></linearGradient></defs><path fill="url(#AD)" d="M276 381c0 1-2 3-3 4-2 1-3 2-3 4-2 2-4 3-5 5-1 0-2 1-2 2l-3 4h-1c1-2 3-4 3-6 0-3 5-5 6-8h0c2-2 5-3 8-5z"></path><path d="M262 394c0 2-2 4-3 6-1 0-1 1-1 1 0 2-1 4-1 5-2 4-4 8-4 13v2l-1 1v-1c0-4 1-8 2-12h-1v-3c0-1 1-2 1-2 1-2 2-4 3-5h1 0l2-2 2-3z" class="C"></path><path d="M257 399h1 0c-1 2-2 5-3 8l-1 2h-1v-3c0-1 1-2 1-2 1-2 2-4 3-5z" class="U"></path><path d="M253 475c0-2-1-7 0-9 0 0 1 1 1 2 0 0 0 1 1 2l1 1 1-2h0v2h0l1 1h0l1 1h1 1l2 2 4 4h3c1 1 3 2 5 4h0l2 1c1 1 3 3 3 5s-2 5-2 6c0 0-1 1-2 1-2 2-6 1-8 0-2 0-3-1-5-2s-7-4-7-7v-1l-1-1c0-1 0-2-1-3h0c0-2-1-5-1-7z" class="E"></path><path d="M259 484c1 0 2 1 2 2 0-1 1-1 0-2 2 1 2 3 4 4 1 2 4 4 5 7h0-2-1l-1-1h0c-2-1-5-3-6-4v-1-1l2-1-3-3z" class="X"></path><path d="M262 487c1 3 2 4 4 6v1c-2-1-5-3-6-4v-1-1l2-1z" class="c"></path><defs><linearGradient id="AE" x1="257.298" y1="477.07" x2="253.874" y2="478.475" xlink:href="#B"><stop offset="0" stop-color="#666566"></stop><stop offset="1" stop-color="#7b7a7a"></stop></linearGradient></defs><path fill="url(#AE)" d="M253 475c0-2-1-7 0-9 0 0 1 1 1 2 0 0 0 1 1 2l1 1c1 3 2 5 3 7 0 1 1 2 1 2 0 1 1 2 1 3v1c1 1 0 1 0 2 0-1-1-2-2-2l3 3-2 1v1 1c-3-3-4-5-6-8h0c0-2-1-5-1-7z"></path><path d="M254 468s0 1 1 2l1 1c1 3 2 5 3 7 0 1 1 2 1 2 0 1 1 2 1 3-4-4-7-10-7-15z" class="T"></path><path d="M254 482c1-1 1-1 1-2h1l-1-1c2 1 3 4 4 5l3 3-2 1v1 1c-3-3-4-5-6-8h0z" class="b"></path><defs><linearGradient id="AF" x1="263.133" y1="485.225" x2="268.469" y2="479.077" xlink:href="#B"><stop offset="0" stop-color="#232223"></stop><stop offset="1" stop-color="#434343"></stop></linearGradient></defs><path fill="url(#AF)" d="M256 471l1-2h0v2h0l1 1h0l1 1h1 1l2 2 4 4h3c1 1 3 2 5 4h0l2 1c0 2 0 3 1 4h0c0 1 1 2 0 3 0 2-1 4-3 5-1-1-3-2-4-4-2-2-5-4-7-6-1-2-2-4-4-6 0 0-1-1-1-2-1-2-2-4-3-7z"></path><path d="M263 477l3 3c-2 0-3-1-5-3h2z" class="c"></path><path d="M259 473h1 1l2 2v2h-2l-2-4z" class="b"></path><path d="M275 483h0l2 1c0 2 0 3 1 4h0l-4-3s0-1 1-2z" class="Q"></path><path d="M263 475l4 4 3 2-1 1-3-2-3-3v-2z" class="e"></path><path d="M267 479h3c1 1 3 2 5 4-1 1-1 2-1 2l-5-3 1-1-3-2z" class="b"></path><defs><linearGradient id="AG" x1="298.847" y1="373.239" x2="276.644" y2="358.639" xlink:href="#B"><stop offset="0" stop-color="#393939"></stop><stop offset="1" stop-color="#585758"></stop></linearGradient></defs><path fill="url(#AG)" d="M298 333c1-1 2-2 3-4 0 1-1 2-1 2v4c-1 1 0 3 0 4h0 0 2v4 3 1c0 1 1 1 1 2v2c-1 2 0 4 0 6h0c-1 2-1 3-1 5 0 4-1 10-3 13h-3v-2h-1l-2 4c-2 2-3 4-4 6l-3 5h0-1l-2 1h0c-3 2-5 6-7 9-1 1-2 2-2 4h-1v-2h-1c0-1 1-3 1-4h0l-1 1h0c-3 3-5 7-7 11l-1 1h0c-1 0-1 0-1-1-2 1-2 4-3 5h0-1c0-1 0-1-1-1h-1c-1 2-1 6-2 8-1-4 0-9 2-13v-1c0-1 1-3 1-5 0 0 0-1 1-1h1l3-4c0-1 1-2 2-2 1-2 3-3 5-5 0-2 1-3 3-4 1-1 3-3 3-4h0l-1-1c-4 1-8 4-11 6-1 1-2 2-4 3-1 1-3 3-4 5h-1v-1-2c5-7 12-11 20-15l-3-2c-2 1-4 2-7 4l-1-1c2-1 4-3 5-4 0-1 0-1-1-1h0c2-1 5-2 6-4 7-4 12-7 16-14 2-3 3-6 4-9l4-12z"></path><path d="M269 373c1-1 1-1 2-1 0-1 1-1 2-2h1c-1 1-2 1-3 2h4c-1 1-2 2-3 2-2 1-4 2-7 4l-1-1c2-1 4-3 5-4z" class="E"></path><path d="M276 381c3-2 5-5 7-7h1c-1 1-2 2-2 3l-3 3c-2 2-4 4-7 6h2v-1c1 0 2-1 3-2s1-2 2-2h0l-3 4-6 4c0-2 1-3 3-4 1-1 3-3 3-4h0zm-1-5v-1c2 0 4-2 6-3l3-3c1-1 4-4 5-4-3 4-7 8-10 12-1 1-2 2-4 3-4 1-8 4-11 6-1 1-2 2-4 3-1 1-3 3-4 5h-1v-1-2c5-7 12-11 20-15z" class="O"></path><defs><linearGradient id="AH" x1="271.113" y1="390.008" x2="275.107" y2="393.36" xlink:href="#B"><stop offset="0" stop-color="#1e1d1e"></stop><stop offset="1" stop-color="#3b3b3b"></stop></linearGradient></defs><path fill="url(#AH)" d="M277 386c5-3 9-7 13-11 0 1-2 3-2 3 0 1 1 1 1 1s-1 1-1 2l-1-1-2 2-7 7c-1 2-3 6-5 7h0l-1 1h0c-3 3-5 7-7 11l-1 1h0c-1 0-1 0-1-1-2 1-2 4-3 5h0-1c0-1 0-1-1-1h-1c-1 2-1 6-2 8-1-4 0-9 2-13v-1c0-1 1-3 1-5 0 0 0-1 1-1h1l3-4c0-1 1-2 2-2 1-2 3-3 5-5l6-4 1 1z"></path><path d="M276 385l1 1c-5 4-10 8-13 13l-1-1v-2h0c0-1 1-2 2-2 1-2 3-3 5-5l6-4z" class="U"></path><path d="M264 399l-3 6v4c1-2 2-4 3-5 1-2 3-2 4-4 1 0 1-1 2-2 0-1 1-2 1-2 1-1 2-2 2-3 1-2 3-3 5-4-1 2-3 6-5 7h0l-1 1h0c-3 3-5 7-7 11l-1 1h0c-1 0-1 0-1-1-2 1-2 4-3 5h0-1c0-1 0-1-1-1h-1c-1 2-1 6-2 8-1-4 0-9 2-13v-1c0-1 1-3 1-5 0 0 0-1 1-1h1l3-4h0v2l1 1z" class="J"></path><path d="M259 400h1c0 2-1 3-2 5 0 1 0 1-1 2h0v-1c0-1 1-3 1-5 0 0 0-1 1-1z" class="E"></path><path d="M293 377l2-4h1v2h3c-1 1-1 1-1 2v1h0-1v1c0 1-1 1-1 2v1c1-1 1-2 2-3v1c0 1-2 6-2 7h1-1v1c0 2-1 3-1 4-1 2-3 5-3 7-1 3-2 5-2 8-1 3-1 5-2 8v-1c-1 1-2 9-2 11 0 1 0 2-1 3 0 9 1 17 3 26l-1 1 2 9c1 4 2 8 2 11l-1 5-1 5v-2c-1-1-1-3-1-5h-1v5h0c-1 1-2 3-2 4 0 2-1 4-3 5-1 2-2 3-3 4h0-1v1h-1c1-1 1-1 1-2h0c0-1 2-4 2-6s-2-4-3-5l-2-1h0c-2-2-4-3-5-4h-3l-4-4-2-2h-1-1l-1-1-1-4c-1-1-1-4-1-6-1-3-1-7-2-11h1c0 1 0 2 1 2l-1-2c1-1 1 0 2-1l2 2v-1c-1-3-3-8-3-12h1c0-2-1-4-1-6-1-4-1-10 0-14 1-2 1-5 1-7h1c1 0 1 0 1 1h1 0c1-1 1-4 3-5 0 1 0 1 1 1h0l1-1c2-4 4-8 7-11h0l1-1h0c0 1-1 3-1 4h1v2h1c0-2 1-3 2-4 2-3 4-7 7-9h0l2-1h1 0l3-5c1-2 2-4 4-6z" class="Y"></path><defs><linearGradient id="AI" x1="268.623" y1="418.397" x2="255.189" y2="426.049" xlink:href="#B"><stop offset="0" stop-color="#474648"></stop><stop offset="1" stop-color="#747473"></stop></linearGradient></defs><path fill="url(#AI)" d="M260 413c1-1 1-4 3-5 0 1 0 1 1 1h0l1-1-3 17c0 2 1 6 0 7-1 2 0 3-1 4 0 0 0-1-1-1v-1-4l-1-8h-1v-3c0-1 1-2 1-3l1-1v-2z"></path><defs><linearGradient id="AJ" x1="265.421" y1="438.148" x2="255.394" y2="435.023" xlink:href="#B"><stop offset="0" stop-color="#5b585a"></stop><stop offset="1" stop-color="#828181"></stop></linearGradient></defs><path fill="url(#AJ)" d="M258 422h1l1 8v4 1c1 0 1 1 1 1 1-1 0-2 1-4 0 3 1 5 1 7s0 4 1 6l3 9c1 2 2 4 2 6l-1-1h-1c-1-2-2-5-3-7h0c-2-3-3-7-4-10-1-2-2-4-2-5v-4c1-3 0-7 0-10v-1z"></path><path d="M263 439c0 2 0 4 1 6l3 9c-1 1 0 2 1 3h-1c0-1 0-1-1-2l-1-1h0c-2-5-3-8-2-13v2-4z" class="E"></path><defs><linearGradient id="AK" x1="264.867" y1="436.135" x2="256.528" y2="436.985" xlink:href="#B"><stop offset="0" stop-color="#393939"></stop><stop offset="1" stop-color="#6d6c6d"></stop></linearGradient></defs><path fill="url(#AK)" d="M257 412h1c1 0 1 0 1 1h1 0v2l-1 1c0 1-1 2-1 3v3 1c0 3 1 7 0 10v4c0 1 1 3 2 5 1 3 2 7 4 10h0c1 2 2 5 3 7 1 1 1 2 1 3-1 0-2-1-2-2-5-5-7-14-9-21 0-2-1-4-1-6-1-4-1-10 0-14 1-2 1-5 1-7z"></path><path d="M258 423h0c-1-1-1-3-1-4h1v3 1z" class="F"></path><defs><linearGradient id="AL" x1="283.118" y1="433.698" x2="265.368" y2="441.836" xlink:href="#B"><stop offset="0" stop-color="#202020"></stop><stop offset="1" stop-color="#454545"></stop></linearGradient></defs><path fill="url(#AL)" d="M269 405h1v1 2 3l-1 6h0 1c0 3-1 6 0 9v3h0 0c1 0 1 5 1 6 1 1 1 2 1 2 1 1 2 0 3 1h0l2 9c0 1 1 3 1 4s0 1-1 2v2c-1-1-2-2-3-4h0c0 2 1 3 2 5 0 1 2 5 3 5l-1 1v1l1 1c1 1 3 2 4 3 0 1 0 0-1 1h0v1l-3-4-3-3v-1c0-1-2-3-2-4-2-4-3-8-5-11h0c0 2 1 4 1 5 1 2 2 5 2 7h0c-1-2-2-5-3-7-5-12-5-24-2-37 1-3 1-6 2-9z"></path><path d="M270 429c1 0 1 5 1 6 1 1 1 2 1 2 1 1 2 0 3 1h0l2 9c0 1 1 3 1 4s0 1-1 2v2c-1-1-2-2-3-4h0c-1-1-1-2-2-3 0-2-1-4-1-6l-1-6v-7h0z" class="X"></path><defs><linearGradient id="AM" x1="270.45" y1="409.78" x2="284.116" y2="416.038" xlink:href="#B"><stop offset="0" stop-color="#262525"></stop><stop offset="1" stop-color="#565757"></stop></linearGradient></defs><path fill="url(#AM)" d="M276 398c2-3 4-7 7-9h0l2-1h1 0s-1 2-2 3c-2 5-4 11-5 17 0 3 0 5-1 8 0-2 0-3-1-4l-2 13v7 6h0c-1-1-2 0-3-1 0 0 0-1-1-2 0-1 0-6-1-6h0 0v-3c-1-3 0-6 0-9h-1 0l1-6v-3-2-1h-1 1l1-6 1-1v-1h0l1-1h0c0 1-1 3-1 4h1v2h1c0-2 1-3 2-4z"></path><path d="M275 432v-8c-1-3 0-8 0-11 0 2 1 4 0 6v3c0-2 1-2 1-4v-2-2c1-1 0-3 0-5 2-5 2-10 5-15 1-2 2-3 3-3-2 5-4 11-5 17 0 3 0 5-1 8 0-2 0-3-1-4l-2 13v7z" class="N"></path><path d="M272 397h0l1-1h0c0 1-1 3-1 4h1v2h1c0-2 1-3 2-4 0 1 0 2-1 2v1c-1 6-3 12-3 18v6h-1v-1 1l-1-1h0v5h0 0v-3c-1-3 0-6 0-9h-1 0l1-6v-3-2-1h-1 1l1-6 1-1v-1z" class="K"></path><path d="M270 406h0c1-1 2-4 2-5 0 3 0 7-1 10 0 2-1 4-1 6h-1 0l1-6v-3-2zm6-8c0 1 0 2-1 2v1c-1 6-3 12-3 18v-2l-1 2c0-6 2-12 3-17 0-2 1-3 2-4z" class="I"></path><defs><linearGradient id="AN" x1="276.879" y1="445.188" x2="285.332" y2="443.44" xlink:href="#B"><stop offset="0" stop-color="#0c0c0d"></stop><stop offset="1" stop-color="#2c2b2c"></stop></linearGradient></defs><path fill="url(#AN)" d="M275 425l2-13c1 1 1 2 1 4v2c0 3 0 5 1 8h0 1l1-1 1 1v1h0c1 1 2 1 3 2v3h0v-4c0 9 1 17 3 26l-1 1 2 9c1 4 2 8 2 11l-1 1-1-7h-1v3c-1-1-1-1-1-2l-1 1-3-4c-1-1-3-2-4-3l-1-1v-1l1-1c-1 0-3-4-3-5-1-2-2-3-2-5h0c1 2 2 3 3 4v-2c1-1 1-1 1-2s-1-3-1-4l-2-9v-6-7z"></path><path d="M275 425l2-13c1 1 1 2 1 4v2 1c-1 2-1 5 0 7v7h0c0-1 0-3-1-4 0-2 1-6 0-9v-1c0 1-1 5-2 6h0z" class="Y"></path><path d="M289 469c0-1-1-3-1-4h0v-1-1-2c-1-1 0-1 0-1-1-1-1-2-1-3h0c0-1-1-4-1-5l-1-2v-3h0l2 8 2 9c1 4 2 8 2 11l-1 1-1-7z" class="I"></path><path d="M282 427c1 1 2 1 3 2v3h0v-4c0 9 1 17 3 26l-1 1-2-8c0-3-1-6-1-8-2-4-3-8-2-12z" class="K"></path><defs><linearGradient id="AO" x1="279.605" y1="467.467" x2="282.667" y2="455.9" xlink:href="#B"><stop offset="0" stop-color="#302d2f"></stop><stop offset="1" stop-color="#525452"></stop></linearGradient></defs><path fill="url(#AO)" d="M274 451h0c1 2 2 3 3 4v-2c1-1 1-1 1-2 2 7 6 13 9 19l-1 1-3-4c-1-1-3-2-4-3l-1-1v-1l1-1c-1 0-3-4-3-5-1-2-2-3-2-5z"></path><defs><linearGradient id="AP" x1="293.726" y1="402.085" x2="282.17" y2="397.839" xlink:href="#B"><stop offset="0" stop-color="#3b3b3b"></stop><stop offset="1" stop-color="#717070"></stop></linearGradient></defs><path fill="url(#AP)" d="M293 377l2-4h1v2h3c-1 1-1 1-1 2v1h0-1v1c0 1-1 1-1 2v1c1-1 1-2 2-3v1c0 1-2 6-2 7h1-1v1c0 2-1 3-1 4-1 2-3 5-3 7-1 3-2 5-2 8-1 3-1 5-2 8v-1c-1 1-2 9-2 11 0 1 0 2-1 3v4h0v-3c-1-1-2-1-3-2h0v-1l-1-1-1 1h-1 0c-1-3-1-5-1-8v-2c1-3 1-5 1-8 1-6 3-12 5-17 1-1 2-3 2-3l3-5c1-2 2-4 4-6z"></path><path d="M293 377h0 0l1-1c0 1 0 2-1 3l-3 9c0-3 2-5 1-7l-2 2c1-2 2-4 4-6z" class="T"></path><path d="M257 450l2 2v-1c-1-3-3-8-3-12h1c2 7 4 16 9 21 0 1 1 2 2 2 0-1 0-2-1-3h1l1 1 1 1c2 2 3 5 5 7h1c0-2-1-3-1-4-1-2-3-4-3-6h0 0c0-2-1-5-2-7 0-1-1-3-1-5h0c2 3 3 7 5 11 0 1 2 3 2 4v1l3 3 3 4v-1h0c1-1 1 0 1-1l3 4 1-1c0 1 0 1 1 2v-3h1l1 7 1-1-1 5-1 5v-2c-1-1-1-3-1-5h-1v5h0c-1 1-2 3-2 4 0 2-1 4-3 5-1 2-2 3-3 4h0-1v1h-1c1-1 1-1 1-2h0c0-1 2-4 2-6s-2-4-3-5l-2-1h0c-2-2-4-3-5-4h-3l-4-4-2-2h-1-1l-1-1-1-4c-1-1-1-4-1-6-1-3-1-7-2-11h1c0 1 0 2 1 2l-1-2c1-1 1 0 2-1z" class="S"></path><path d="M276 471c1 1 2 3 3 4l1 3c-2-1-4-3-6-5 1 0 2 1 2 1v-1-1-1z" class="I"></path><path d="M265 472c2 2 8 6 8 9h0 1c1 0 1 1 1 2h0c-2-2-4-3-5-4-2-2-4-4-5-7z" class="E"></path><path d="M267 459h1l1 1 1 1c0 4 3 8 6 10v1 1 1s-1-1-2-1c-2-2-4-5-6-8v-3c0-1 0-2-1-3z" class="J"></path><path d="M257 450l2 2v-1c-1-3-3-8-3-12h1c2 7 4 16 9 21 0 1 1 2 2 2v3l-8-10c1 2 2 4 3 7-2-2-3-5-5-7l-1-1v1l-1-1v-1l-1-2c1-1 1 0 2-1z" class="K"></path><path d="M256 454v-2h1c0 1 1 2 1 3l-1-1v1l-1-1z" class="B"></path><defs><linearGradient id="AQ" x1="253.828" y1="453.226" x2="264.722" y2="477.315" xlink:href="#B"><stop offset="0" stop-color="#2a2a2a"></stop><stop offset="1" stop-color="#5a595a"></stop></linearGradient></defs><path fill="url(#AQ)" d="M254 451h1c0 1 0 2 1 2v1l1 1c1 5 4 13 8 17 1 3 3 5 5 7h-3l-4-4-2-2h-1-1l-1-1-1-4c-1-1-1-4-1-6-1-3-1-7-2-11z"></path><path d="M257 468c2 0 3 4 4 5h-1-1l-1-1-1-4z" class="O"></path><defs><linearGradient id="AR" x1="282.302" y1="464.586" x2="278.953" y2="489.254" xlink:href="#B"><stop offset="0" stop-color="#080809"></stop><stop offset="1" stop-color="#303030"></stop></linearGradient></defs><path fill="url(#AR)" d="M282 492c1-3 2-8 1-11 0-1 0-1-1-1v-3c0-1 0-1-1-2v-1c1 1 1 2 3 3v-1c-3-4-8-8-9-14l1-1v1l3 3 3 4v-1h0c1-1 1 0 1-1l3 4 1-1c0 1 0 1 1 2v-3h1l1 7 1-1-1 5-1 5v-2c-1-1-1-3-1-5h-1v5h0c-1 1-2 3-2 4 0 2-1 4-3 5z"></path><path d="M276 462l3 3 3 4v-1h0c1-1 1 0 1-1l3 4 1-1c0 1 0 1 1 2v-3h1l1 7 1-1-1 5-1 5v-2c-1-1-1-3-1-5h-1c-1-2-1-3-2-5-3-3-7-6-9-11z" class="N"></path><path d="M287 470c0 1 0 1 1 2v-3h1l1 7 1-1-1 5h0c0-4-2-6-4-9l1-1z" class="B"></path><defs><linearGradient id="AS" x1="256.175" y1="506.169" x2="271.657" y2="532.394" xlink:href="#B"><stop offset="0" stop-color="#414041"></stop><stop offset="1" stop-color="#666566"></stop></linearGradient></defs><path fill="url(#AS)" d="M281 513l3-3c3-3 7-7 9-11h0 1 4 1c1 0 2 0 2 1v2l2 1-2 3h0v1l-1 1c-1 1-1 1-1 2l-1 2h1l3-3c0-1 1-2 2-2h1c1 0 1 0 2-1-2 5-7 10-11 13v1c1-1 1-1 2-1h0l2-1h0c-1 1-2 2-3 4s-3 6-5 7c-3 2-7 5-10 7-1 0-4 2-4 3s0 2-1 3c1 0 2-1 3 0h2 0s-1 1-2 1v2c-1 0-3 1-4 2-2 0-4 1-5 1-5 2-10 4-15 5-2 0-5 0-8 1l-5 1h-1l-1-1c-1 0-3 0-4 1l-19 3h-3l-1-1h-4c-1 1-2 1-3 1v1h0c-2 0-3 0-4-1l2-1c2 0 3-1 4-2l2-3h-3c1-1 1-2 2-3v-1l1-2 1-5h0c-2 3-4 5-6 9 0-2 1-3 1-4v-6h1c-1-2-1-5-1-7h0c1-3 2-7 4-9 0 1 0 1-1 2l1 1v-1c1-1 2-1 3-2 1 0 1 0 2-1 2 0 3-2 5-3s9-3 12-2c1 1 3 1 5 1h11s0-1-1-1c3 0 6 0 9 1h2c4-1 9-2 13-3 3-1 6-3 9-3z"></path><path d="M247 526h4 2c1-1 4-1 6-1 0 1 0 1 1 1l-7 1h-5l-1-1z" class="Q"></path><path d="M287 522l-1-1v-1h1l4-2c1 0 1-1 2-1 2-1 3-4 5-5v1c-2 4-7 7-11 9z" class="X"></path><path d="M260 526l11-1c-8 3-16 4-24 2h1 5l7-1z" class="J"></path><path d="M225 523l6-2c2 0 4 0 6 1s3 0 5 1h-5c-2 0-4-1-6 0h-1c-1 0-2 0-3 1h3c2 0 12 0 13-1h1 3s-1 0-2 1c-3 0-6 0-9 1h0c-1 0-3 0-3-1-3 0-7 1-11 1h0l1-1h-1v-1h3z" class="X"></path><path d="M225 523l6-2c2 0 4 0 6 1h-7c-2 1-5 2-7 2h-1v-1h3z" class="Q"></path><path d="M272 516c3-1 6-3 9-3-7 6-15 9-24 9h-7l-1-1c4-1 7-1 10-2 4-1 9-2 13-3z" class="L"></path><path d="M248 518c3 0 6 0 9 1h2c-3 1-6 1-10 2l1 1h-1c-2 1-5 0-7 1-2-1-3 0-5-1s-4-1-6-1l-6 2c-2 0-2 0-4-1 5-2 12-2 17-2v-1h11s0-1-1-1z" class="F"></path><path d="M248 518c3 0 6 0 9 1h2c-3 1-6 1-10 2l1 1h-1-4c1-1 3-1 5-2h-2-10v-1h11s0-1-1-1z" class="J"></path><defs><linearGradient id="AT" x1="295.723" y1="505.45" x2="292.485" y2="526.853" xlink:href="#B"><stop offset="0" stop-color="#484847"></stop><stop offset="1" stop-color="#7d7c7e"></stop></linearGradient></defs><path fill="url(#AT)" d="M302 509c0-1 1-2 2-2h1c1 0 1 0 2-1-2 5-7 10-11 13-1 1-3 2-4 3-3 2-5 4-9 4v-1-1h-1c1-1 3-1 4-2h1c4-2 9-5 11-9v-1c0-1 0-2 1-2l-1 2h1l3-3z"></path><path d="M233 524c0 1 2 1 3 1h0l11 1 1 1c-3 0-6 1-9 1v1c3 0 5-1 8-1v1l-7 1-11 1c-2 1-4 1-5 1h-1-2l-1 1h-1-1 0l3-2h0c1-1 1-1 2-1-1-1-3 0-4 0h0c1-1 2-1 3-2 4-1 7-3 11-4z" class="a"></path><path d="M240 530c0-1 0 0-1-1-4 0-8 1-12 1v-1l12-1v1c3 0 5-1 8-1v1l-7 1z" class="X"></path><path d="M211 524c0 1 0 1-1 2l1 1v-1c1-1 2-1 3-2 1 0 1 0 2-1 2 0 3-2 5-3s9-3 12-2c1 1 3 1 5 1v1c-5 0-12 0-17 2 2 1 2 1 4 1h-3v1h1l-1 1h0c4 0 8-1 11-1-4 1-7 3-11 4-1 1-2 1-3 2h0c1 0 3-1 4 0-1 0-1 0-2 1h0l-3 2h0 1 1l1-1h2 1l-1 1-3 2v1c-1 1-4 4-7 5h-1 0c-2 3-4 5-6 9 0-2 1-3 1-4v-6h1c-1-2-1-5-1-7h0c1-3 2-7 4-9z" class="K"></path><path d="M216 533v-1c-1 0-2 1-3 1h0c1-2 3-3 5-4l1 1h0c1 0 3-1 4 0-1 0-1 0-2 1-2 0-3 1-5 2z" class="C"></path><path d="M216 533c2-1 3-2 5-2h0l-3 2h0 1 1l1-1h2c-2 1-6 2-7 4-2 0-3 1-5 2 1-2 3-4 5-5z" class="E"></path><path d="M223 532h1l-1 1-3 2v1c-1 1-4 4-7 5h-1 0c0-1 3-4 4-5 1-2 5-3 7-4z" class="J"></path><defs><linearGradient id="AU" x1="219.386" y1="529.733" x2="220.065" y2="523.135" xlink:href="#B"><stop offset="0" stop-color="#393839"></stop><stop offset="1" stop-color="#535253"></stop></linearGradient></defs><path fill="url(#AU)" d="M218 529h1l-1-1c-2 1-4 1-6 3v1-1c1-3 7-7 9-9 2 1 2 1 4 1h-3v1h1l-1 1h0c4 0 8-1 11-1-4 1-7 3-11 4-1 1-2 1-3 2l-1-1z"></path><path d="M296 519v1c1-1 1-1 2-1h0l2-1h0c-1 1-2 2-3 4s-3 6-5 7c-6-1-11 5-16 6-1-1-1-1-2-1l-6 2h-2c-7 1-14 4-22 6-4 0-8 1-12 2l-8 3-3 1h-3c-2 1-2 2-4 3-1 0-2 0-2 1h-1-3c1-1 1-2 2-3v-1l1-2 1-5h1c3-1 6-4 7-5 5-2 9-2 14-3 4 0 9-1 13-2h7c10 0 21-1 29-5 4 0 6-2 9-4 1-1 3-2 4-3z" class="B"></path><path d="M226 538c1 0 2-1 3-1 0 0 0-1 1-1h1 2s1 0 1-1h1c1 1 2 0 4 0 1 0 0 0 1-1 1 0 3 1 4 0h2 1c1 0 3 0 5 1 2 0 5-1 7-1-2 1-4 2-6 2h0c-2-1-4 0-6-1h-6c-3 1-6 1-9 2-2 0-4 1-6 1z" class="V"></path><defs><linearGradient id="AV" x1="294.402" y1="529.325" x2="276.719" y2="524.178" xlink:href="#B"><stop offset="0" stop-color="#444446"></stop><stop offset="1" stop-color="#646463"></stop></linearGradient></defs><path fill="url(#AV)" d="M296 519v1c1-1 1-1 2-1h0l2-1h0c-1 1-2 2-3 4s-3 6-5 7c-6-1-11 5-16 6-1-1-1-1-2-1l-6 2h-2c8-3 15-5 22-9 1-1 4-3 4-5 1-1 3-2 4-3z"></path><path d="M227 541c1 0 2 0 3 1h0 2c-3 1-7 2-10 2-1 0-1 1-2 1v1l3-1v1h-1l2 1-3 1h-3c-2 1-2 2-4 3-1 0-2 0-2 1h-1-3c1-1 1-2 2-3v-1l1-2v1h0l1-1 2-2 2-2h4c0-1 1-1 1-1h3 3z" class="I"></path><path d="M222 546l2 1-3 1h-3c-2 1-2 2-4 3-1 0-2 0-2 1h-1-3c1-1 1-2 2-3h1 0 2 2c2 0 3-2 5-2 0 0 1-1 2-1z" class="K"></path><path d="M227 541c1 0 2 0 3 1h0c-7 1-13 2-18 6h-2l1-2v1h0l1-1 2-2 2-2h4c0-1 1-1 1-1h3 3z" class="J"></path><defs><linearGradient id="AW" x1="237.687" y1="537.933" x2="237.43" y2="536.071" xlink:href="#B"><stop offset="0" stop-color="#505050"></stop><stop offset="1" stop-color="#6d6c6c"></stop></linearGradient></defs><path fill="url(#AW)" d="M226 538c2 0 4-1 6-1 3-1 6-1 9-2h6c2 1 4 0 6 1h0c-1 1-2 1-3 1 1 0 2 0 3 1h0l-1 1h0 0-4c-3 0-6 1-10 1h-2c-2 1-4 1-6 2-1-1-2-1-3-1h-3-3s-1 0-1 1h-4c3-2 7-3 10-4z"></path><path d="M227 541v-1-1c2 0 4 1 6 0h1c1 0 2-1 2-1h3c1 0 2 0 2-1 2 0 4 1 5 0h1 0 0c1 0 1 0 1 1h0 5l-1 1h0 0-4c-3 0-6 1-10 1h-2c-2 1-4 1-6 2-1-1-2-1-3-1z" class="T"></path><path d="M276 535c5-1 10-7 16-6-3 2-7 5-10 7-1 0-4 2-4 3s0 2-1 3c1 0 2-1 3 0h2 0s-1 1-2 1v2c-1 0-3 1-4 2-2 0-4 1-5 1-5 2-10 4-15 5-2 0-5 0-8 1l-5 1h-1l-1-1c-1 0-3 0-4 1l-19 3h-3l-1-1h-4c-1 1-2 1-3 1v1h0c-2 0-3 0-4-1l2-1c2 0 3-1 4-2l2-3h1c0-1 1-1 2-1 2-1 2-2 4-3h3l3-1 8-3c4-1 8-2 12-2 8-2 15-5 22-6h2l6-2c1 0 1 0 2 1z" class="I"></path><path d="M209 555c1 0 1 1 1 1 3 0 6-2 9-3 2 0 3-1 4-2 2 0 4-1 5-1l1 1-2 1h-2-1c-2 1-3 2-4 3-2 1-4 1-6 2h-4c-1 1-2 1-3 1v1h0c-2 0-3 0-4-1l2-1c2 0 3-1 4-2z" class="C"></path><path d="M244 542c2 1 6-1 9-1-2 2-6 2-9 3l2 1c-8 1-19 1-26 5h0c0-1 1-1 1-2l3-1 8-3c4-1 8-2 12-2z" class="E"></path><defs><linearGradient id="AX" x1="239.481" y1="551.406" x2="242.899" y2="543.235" xlink:href="#B"><stop offset="0" stop-color="#403d3f"></stop><stop offset="1" stop-color="#505351"></stop></linearGradient></defs><path fill="url(#AX)" d="M228 550l18-4c1-1 2-1 4-1h-1c1 1 2 1 4 1h0 3v1h-4l-6 2c-1 2-2 2-4 3v-1h-1-1c-1 1-2 0-2 0-4 0-8 0-11 2v-1l2-1-1-1z"></path><path d="M229 551h0c3-1 6-1 9-2l10-1c1-1 2-1 3-1h1l-6 2c-1 2-2 2-4 3v-1h-1-1c-1 1-2 0-2 0-4 0-8 0-11 2v-1l2-1z" class="K"></path><path d="M238 551l8-2c-1 2-2 2-4 3v-1h-1-1c-1 1-2 0-2 0z" class="U"></path><defs><linearGradient id="AY" x1="218.816" y1="550.18" x2="235.817" y2="559.586" xlink:href="#B"><stop offset="0" stop-color="#232021"></stop><stop offset="1" stop-color="#474a49"></stop></linearGradient></defs><path fill="url(#AY)" d="M227 553c3-2 7-2 11-2 0 0 1 1 2 0h1 1v1h-1v1h0v1c-1 0-3 0-4 1l-19 3h-3l-1-1h-4 4c2-1 4-1 6-2 1-1 2-2 4-3h1 2v1z"></path><path d="M227 553c3-2 7-2 11-2 0 0 1 1 2 0h1 1v1h-1c-1 0-2 1-3 0h-3-1l-1 1h-3c-1 0-1 0-2 1-1 0-5 1-6 0 2-1 4-1 5-1z" class="V"></path><path d="M266 536h2l6-2c1 0 1 0 2 1-4 1-7 2-10 3s-6 1-9 3h0c2 0 3 0 4 1l6-2-3 3c-1 0-4 1-4 2h0c-1 1-3 1-4 2v-1h-3 0c-2 0-3 0-4-1h1c1 0 2-1 2-1-2 0-4 0-6 1l-2-1c3-1 7-1 9-3-3 0-7 2-9 1 8-2 15-5 22-6z" class="F"></path><defs><linearGradient id="AZ" x1="256.206" y1="543.984" x2="264.328" y2="540.918" xlink:href="#B"><stop offset="0" stop-color="#717071"></stop><stop offset="1" stop-color="#878686"></stop></linearGradient></defs><path fill="url(#AZ)" d="M261 542l6-2-3 3c-1 0-4 1-4 2l-7-1 8-2z"></path><defs><linearGradient id="Aa" x1="272.941" y1="532.346" x2="274.802" y2="541.418" xlink:href="#B"><stop offset="0" stop-color="#595859"></stop><stop offset="1" stop-color="#7f7e7e"></stop></linearGradient></defs><path fill="url(#Aa)" d="M276 535c5-1 10-7 16-6-3 2-7 5-10 7-1 0-4 2-4 3h-2l-2 2h-1c-1 1-2 1-3 2l-1-1h0c-2 2-6 3-8 3l3-2h0l3-3-6 2c-1-1-2-1-4-1h0c3-2 6-2 9-3s6-2 10-3z"></path><path d="M276 539h2c0 1 0 2-1 3 1 0 2-1 3 0h2 0s-1 1-2 1v2c-1 0-3 1-4 2-2 0-4 1-5 1-5 2-10 4-15 5-2 0-5 0-8 1l-5 1h-1l-1-1v-1h0v-1h1c2-1 3-1 4-3l6-2h4c1-1 3-1 4-2h0c0-1 3-2 4-2h0l-3 2c2 0 6-1 8-3h0l1 1c1-1 2-1 3-2h1l2-2z" class="O"></path><path d="M276 539h2c0 1 0 2-1 3 1 0 2-1 3 0h2 0s-1 1-2 1c-2 0-3 1-4 1-1 1-2 1-3 1 1-1 3-1 4-1l-1-1c-1 0-2 0-3 1h-1v-1h0c2-1 3-2 4-3v-1z" class="F"></path><path d="M258 549v1l-11 3v1h1l-5 1h-1l-1-1v-1l17-4z" class="I"></path><path d="M273 545c1 0 2 0 3-1 1 0 2-1 4-1v2c-1 0-3 1-4 2-2 0-4 1-5 1-5 2-10 4-15 5-2 0-5 0-8 1h-1v-1l11-3v-1s2-1 3-1c2 0 5-2 7-2s3-1 5-1z" class="T"></path><defs><linearGradient id="Ab" x1="300.118" y1="422.264" x2="290.157" y2="514.79" xlink:href="#B"><stop offset="0" stop-color="#030303"></stop><stop offset="1" stop-color="#3f3f3f"></stop></linearGradient></defs><path fill="url(#Ab)" d="M303 357c0-2 0-3 1-5v2 2 1h1c0 1 1 4 0 6v7c1 1 1 1 1 2v1c1-2 0-5 0-7 1 5 0 10 0 15v4h1l1-1h1l1 1v4l1 1 3 6c2 5 3 9 4 14v2c-1 1 0 5 0 6h0c1 2 1 4 1 6 1 3-1 6-1 10v6 7 1l-1-1s-1-1-1-2c0 0 0-1-1-1v2c-2 0-2 2-3 4h0c1 2 3 5 4 6 1 0 1-1 1-2 0 1 0 2 1 3 0 0 1 3 1 4v2c1 1 0 2 1 4v2c1 2 0 5 1 7v4 1l-1 2c1 2 0 5 0 8 0 0 1 0 1 1-1 1-1 7 0 8 1 3 0 7 0 10v-1c0-1 0-1 1-2v-4-1-2c2-4 0-10 2-14h0v8c0 3 0 6-1 8v4 1c0 3-2 6-3 9 0 2 0 4-1 6h0c-1 1-2 3-2 5l-5 9c-2 3-3 6-5 9l-4 4-2 2h0c0-2 5-7 6-8 1-2 2-3 2-4v-1l-3 3v1l-1-1c0 1-1 2-1 2h-1v-2c-2 3-4 5-6 7h-1l-2 1 1-2c5-6 10-12 14-19 0-1 1-3 1-4s1-2 1-3h0c-1 1-2 3-3 4h0c0 1 0 1-1 2h0l-1 1c0 1 0 2-1 2h-1l4-6h-1l-3 6c-3 2-5 5-7 7h-1l-1 1h0l1-2h0-1c0 1-1 1-1 1-1 1-2 2-3 2l-1 1-10 5v-2c1 0 2-1 2-1h0-2c-1-1-2 0-3 0 1-1 1-2 1-3s3-3 4-3c3-2 7-5 10-7 2-1 4-5 5-7s2-3 3-4h0l-2 1h0c-1 0-1 0-2 1v-1c4-3 9-8 11-13-1 1-1 1-2 1h-1c-1 0-2 1-2 2l-3 3h-1l1-2c0-1 0-1 1-2l1-1v-1h0l2-3-2-1v-2c0-1-1-1-2-1h-1-4-1 0c-2 4-6 8-9 11l-3 3c-3 0-6 2-9 3 1-1 1-2 2-2h2l-1-3h-3l-1-1c2 0 5-2 7-4l-1-1c1 0 1-1 1-1l2-2-1-1c5-3 8-8 10-14v-2l1-5 1-5c0-3-1-7-2-11l-2-9 1-1c-2-9-3-17-3-26 1-1 1-2 1-3 0-2 1-10 2-11v1c1-3 1-5 2-8 0-3 1-5 2-8 0-2 2-5 3-7 0-1 1-2 1-4v-1h1-1c0-1 2-6 2-7v-1c-1 1-1 2-2 3v-1c0-1 1-1 1-2v-1h1 0v-1c0-1 0-1 1-2 2-3 3-9 3-13 0-2 0-3 1-5h0z"></path><path d="M299 453c1 2 0 3 1 5v1h-1-1c0-1-1-2 0-3v-1c-1 0 0-1 0-1v1h1v-2z" class="Y"></path><path d="M303 503l1-1c0 3-3 5-3 7h0 1l-3 3h-1l1-2c0-1 0-1 1-2l1-1v-1h0l2-3z" class="U"></path><path d="M289 487c1 0 1-2 2-3 0-1 0-2 1-3 0 6-2 10-6 15-1 1-2 3-3 4h-1 0l-2 2-1-1c5-3 8-8 10-14z" class="E"></path><path d="M287 455l1-1 3 14c1 4 3 9 1 13-1 1-1 2-1 3-1 1-1 3-2 3v-2l1-5 1-5c0-3-1-7-2-11l-2-9z" class="C"></path><defs><linearGradient id="Ac" x1="305.707" y1="413.22" x2="289.154" y2="411.033" xlink:href="#B"><stop offset="0" stop-color="#616161"></stop><stop offset="1" stop-color="#777676"></stop></linearGradient></defs><path fill="url(#Ac)" d="M303 357c0-2 0-3 1-5v2 2 1h1c0 1 1 4 0 6v5h-1v-1h0v3 1c1 0 0 3 0 3v2c1 2 1 5 0 7v3h0l1-2h0v4c-1 1 0 3-1 5v1 1-2c-1 1-1 6-2 7-1 15-3 29 3 43h-1c-2-3-3-6-6-9 2 6 3 11 6 17 0 1 2 3 2 5 0 0-1-1-1-2-3-3-4-8-6-12 0-1-1-2-1-3-1-1-1-2-1-3h0c-1 0-1 1-2 1 0-1-1-2-1-2v-1h0c0 1 0 2-1 3v2-1h-1v1c0-1 0-3-1-4-1 1-1 2-1 3v-2h-1-1c-1-7-1-14 0-21 1-3 1-5 2-8 0-3 1-5 2-8 0-2 2-5 3-7 0-1 1-2 1-4v-1h1-1c0-1 2-6 2-7v-1c-1 1-1 2-2 3v-1c0-1 1-1 1-2v-1h1 0v-1c0-1 0-1 1-2 2-3 3-9 3-13 0-2 0-3 1-5h0z"></path><path d="M295 392h1v1s0-1 1-2c0 3-1 4-2 6 0 1-1 2-1 3h-1c0-1 0-1-1-1 0-2 2-5 3-7z" class="F"></path><defs><linearGradient id="Ad" x1="304.935" y1="378.032" x2="293.132" y2="381.375" xlink:href="#B"><stop offset="0" stop-color="#272826"></stop><stop offset="1" stop-color="#4e4c4f"></stop></linearGradient></defs><path fill="url(#Ad)" d="M298 380c0-1 0-2 1-3l4-10c0 4 0 8-1 11 0 2-2 7-1 8 0 1-1 2-1 3 0 2-1 5-2 7v-2h0c1-1 0-2 1-3h0c-1 1-2 4-2 6v-3-3h0c-1 1-1 2-1 2v-1h-1c0-1 1-2 1-4v-1h1-1c0-1 2-6 2-7z"></path><path d="M303 357c0-2 0-3 1-5v2 2 1h1c0 1 1 4 0 6v5h-1v-1h0v3 1c1 0 0 3 0 3v2c1 2 1 5 0 7v3h0l1-2h0v4c-1 1 0 3-1 5v1 1-2c-1 1-1 6-2 7v-6c1-1 0-3 1-4v-3c-1 0-2-1-2-1-1-1 1-6 1-8 1-3 1-7 1-11l-4 10c-1 1-1 2-1 3v-1c-1 1-1 2-2 3v-1c0-1 1-1 1-2v-1h1 0v-1c0-1 0-1 1-2 2-3 3-9 3-13 0-2 0-3 1-5h0z" class="V"></path><defs><linearGradient id="Ae" x1="311.459" y1="409.191" x2="302.289" y2="409.935" xlink:href="#B"><stop offset="0" stop-color="#1e1e1f"></stop><stop offset="1" stop-color="#696969"></stop></linearGradient></defs><path fill="url(#Ae)" d="M304 395v-1-1c1-2 0-4 1-5v-4h0l-1 2h0v-3c1-2 1-5 0-7v-2s1-3 0-3v-1-3h0v1h1v-5 7c1 1 1 1 1 2v1c1-2 0-5 0-7 1 5 0 10 0 15v4h1l1-1h1l1 1v4l1 1 3 6c2 5 3 9 4 14v2c-1 1 0 5 0 6h0c1 2 1 4 1 6 1 3-1 6-1 10v6 7 1l-1-1s-1-1-1-2c0 0 0-1-1-1v2c-2 0-2 2-3 4h0c1 2 3 5 4 6 1 0 1-1 1-2 0 1 0 2 1 3 0 0 1 3 1 4v2c1 1 0 2 1 4v2c1 2 0 5 1 7v4l-1-8c0-3-1-7-2-10 0 2 1 5 0 7-2-4-3-8-5-12-1-3-3-6-5-8l-3-6c-6-14-4-28-3-43 1-1 1-6 2-7v2z"></path><defs><linearGradient id="Af" x1="312.448" y1="433.812" x2="308.892" y2="431.451" xlink:href="#B"><stop offset="0" stop-color="#50514d"></stop><stop offset="1" stop-color="#666567"></stop></linearGradient></defs><path fill="url(#Af)" d="M308 427c1 0 1 1 2 2h0 0 1l1 4v3c0 2 1 4 1 6 0 1 1 1 1 2v1c-1-1-1-2-2-3v-2-3h-1 0c-1-3-2-7-3-10z"></path><path d="M309 420v-2 1h1v-2 4l1-1c1 1 1 2 1 3l1 9-1 1h0l-1-4h-1 0 0c-1-1-1-2-2-2v-3c-1-1-1-2-1-3l1 1 1-2z" class="a"></path><g class="b"><path d="M310 421l1-1c1 1 1 2 1 3v4h-1l-1-6z"></path><path d="M309 420c1 3 1 6 1 9h0c-1-1-1-2-2-2v-3c-1-1-1-2-1-3l1 1 1-2z"></path></g><path d="M302 400c1-1 1-6 2-7v2c-1 3-1 6-1 8 0 10-1 19 1 29 0 2 1 5 2 7l6 11h0c1 2 3 5 4 6 1 0 1-1 1-2 0 1 0 2 1 3 0 0 1 3 1 4v2c1 1 0 2 1 4v2c1 2 0 5 1 7v4l-1-8c0-3-1-7-2-10 0 2 1 5 0 7-2-4-3-8-5-12-1-3-3-6-5-8l-3-6c-6-14-4-28-3-43z" class="L"></path><defs><linearGradient id="Ag" x1="305.991" y1="384.364" x2="311.524" y2="419.16" xlink:href="#B"><stop offset="0" stop-color="#2a292a"></stop><stop offset="1" stop-color="#777"></stop></linearGradient></defs><path fill="url(#Ag)" d="M306 385h1l1-1h1l1 1v4l1 1 3 6c2 5 3 9 4 14v2c-1 1 0 5 0 6h0c1 2 1 4 1 6 1 3-1 6-1 10v6 7 1l-1-1s-1-1-1-2c0 0 0-1-1-1v2l-1-1v-1c0-1-1-1-1-2 0-2-1-4-1-6v-3h0l1-1-1-9c0-1 0-2-1-3l-1 1v-4 2h-1v-1 2l-1 2-1-1v-1c-1-4 0-10 0-14h0v1c0-1-1-2-1-3v-6h-1v-6c1-1 0-2 0-3s1-2 1-4z"></path><path d="M306 398v-5-1c1 1 1 1 1 2h0c1 4 0 8 0 12h0v1c0-1-1-2-1-3v-6z" class="J"></path><defs><linearGradient id="Ah" x1="309.822" y1="403.456" x2="313.858" y2="413.597" xlink:href="#B"><stop offset="0" stop-color="#615f62"></stop><stop offset="1" stop-color="#787877"></stop></linearGradient></defs><path fill="url(#Ah)" d="M312 415c0-1 0-4-1-5v-9l1 3s1 1 1 2l3 17h-1l-1-5v-3h-1 0-1z"></path><path d="M314 406c1 5 3 10 3 15h1v-3c1 2 1 4 1 6v4c-1 0-1 1-1 2h0-1c0-2 0-2-1-3v-4l-3-17h1z" class="F"></path><path d="M318 418c1 2 1 4 1 6v4c-1 0-1 1-1 2-1-3-1-6-1-9h1v-3z" class="O"></path><defs><linearGradient id="Ai" x1="309.733" y1="394.779" x2="319.488" y2="415.577" xlink:href="#B"><stop offset="0" stop-color="#5b5b5a"></stop><stop offset="1" stop-color="#807f80"></stop></linearGradient></defs><path fill="url(#Ai)" d="M311 390l3 6c2 5 3 9 4 14v2c-1 1 0 5 0 6h0v3h-1c0-5-2-10-3-15-1-2-1-5-1-7l-2-9z"></path><defs><linearGradient id="Aj" x1="312.64" y1="438.648" x2="314.268" y2="422.62" xlink:href="#B"><stop offset="0" stop-color="#444344"></stop><stop offset="1" stop-color="#6d6e6d"></stop></linearGradient></defs><path fill="url(#Aj)" d="M312 415h1 0 1v3l1 5h1v4c1 1 1 1 1 3h1 0c0-1 0-2 1-2v-4c1 3-1 6-1 10v6 7 1l-1-1s-1-1-1-2c0 0 0-1-1-1v2l-1-1v-1c0-1-1-1-1-2 0-2-1-4-1-6v-3h0l1-1-1-9c0-1 0-2-1-3l1-2h0v-3z"></path><path d="M315 423h1v4 5 1h-1v-10z" class="c"></path><path d="M313 432v1c1 2 1 6 0 9 0-2-1-4-1-6v-3h0l1-1z" class="Q"></path><path d="M313 432v1 2l-1 1v-3h0l1-1z" class="X"></path><path d="M312 415h1 0 1v3s-1 1 0 2v6l-1-1c0-2 0-4-1-7h0v-3z" class="b"></path><defs><linearGradient id="Ak" x1="320.843" y1="444.944" x2="314.193" y2="431.535" xlink:href="#B"><stop offset="0" stop-color="#393739"></stop><stop offset="1" stop-color="#555655"></stop></linearGradient></defs><path fill="url(#Ak)" d="M319 424c1 3-1 6-1 10v6 7 1l-1-1c0-3-2-5-2-7v-6s0-1 1-1v-1-5c1 1 1 1 1 3h1 0c0-1 0-2 1-2v-4z"></path><path d="M316 432c1 2 0 6 0 8h-1 0v-6s0-1 1-1v-1z" class="Q"></path><defs><linearGradient id="Al" x1="328.78" y1="486.856" x2="276.391" y2="513.241" xlink:href="#B"><stop offset="0" stop-color="#2c2c2c"></stop><stop offset="1" stop-color="#525152"></stop></linearGradient></defs><path fill="url(#Al)" d="M305 443l3 6c2 2 4 5 5 8 2 4 3 8 5 12 1-2 0-5 0-7 1 3 2 7 2 10l1 8v1l-1 2c1 2 0 5 0 8 0 0 1 0 1 1-1 1-1 7 0 8 1 3 0 7 0 10v-1c0-1 0-1 1-2v-4-1-2c2-4 0-10 2-14h0v8c0 3 0 6-1 8v4 1c0 3-2 6-3 9 0 2 0 4-1 6h0c-1 1-2 3-2 5l-5 9c-2 3-3 6-5 9l-4 4-2 2h0c0-2 5-7 6-8 1-2 2-3 2-4v-1l-3 3v1l-1-1c0 1-1 2-1 2h-1v-2c-2 3-4 5-6 7h-1l-2 1 1-2c5-6 10-12 14-19 0-1 1-3 1-4s1-2 1-3h0c-1 1-2 3-3 4h0c0 1 0 1-1 2h0l-1 1c0 1 0 2-1 2h-1l4-6h-1l-3 6c-3 2-5 5-7 7h-1l-1 1h0l1-2h0-1c0 1-1 1-1 1-1 1-2 2-3 2l-1 1-10 5v-2c1 0 2-1 2-1h0-2c-1-1-2 0-3 0 1-1 1-2 1-3s3-3 4-3c3-2 7-5 10-7 2-1 4-5 5-7s2-3 3-4h0l-2 1h0c-1 0-1 0-2 1v-1c4-3 9-8 11-13v-1l2-2 2-3c0-1 0-1-1-1 0-1 1-4 2-5 0-4 1-8 2-12 0-6 0-13-2-20 0-2 0-4-1-6s-1-3-2-4l-4-6c0-1-1-1-1-2v-1h1z"></path><path d="M318 469c1-2 0-5 0-7 1 3 2 7 2 10l1 8v1l-1 2-1-1h1c0-1 0-1-1-2-1-3 1-6 0-9h0v-1-3c-1 1 0 1 0 2h-1v2c1 2 1 7 0 9v-11z" class="Y"></path><path d="M319 480c0-2 0-6 1-8l1 8v1l-1 2-1-1h1c0-1 0-1-1-2z" class="K"></path><defs><linearGradient id="Am" x1="307.116" y1="521.88" x2="289.652" y2="513.835" xlink:href="#B"><stop offset="0" stop-color="#272727"></stop><stop offset="1" stop-color="#575656"></stop></linearGradient></defs><path fill="url(#Am)" d="M311 500c1-2 1-4 2-6 1-1 1-2 2-3h0v1 2c0 3-1 5-1 8-1 3-1 6-2 9-1 4-3 8-5 13l-3 6c-3 2-5 5-7 7h-1l-1 1h0l1-2h0-1c0 1-1 1-1 1-1 1-2 2-3 2l-1 1-10 5v-2c1 0 2-1 2-1h0-2c-1-1-2 0-3 0 1-1 1-2 1-3s3-3 4-3c3-2 7-5 10-7 2-1 4-5 5-7s2-3 3-4h0l-2 1h0c-1 0-1 0-2 1v-1c4-3 9-8 11-13v-1l2-2 2-3z"></path><path d="M318 251l1-3c0 1-1 5 0 6 1-1 1-2 2-3l5 15c-1 0-2-3-3-3v-1c0-1-1-4-2-5-1 0-1 1-2 2v1c2 0 2 1 3 3h0v1 3c1 0 1 1 1 2h-1v-1-1c-1 0-1-1-1-1 0 1-1 1-2 1-2 0-3 1-5 2l-1 1c-2 0-3-2-4-3 2 3 4 6 5 9v1h-1c0-1 0-2-1-3v3c1 3 2 6 2 8l1 4c0 6 0 11-2 17l-2 4c-1 1-6 5-6 6v1c-2 2-5 4-8 5-2 0-4 0-5-1h0-1-1-2-4l-5-1v-1h1 2l-1-1-4-2c-4-1-10-2-15-2-2 0-3 0-5 1-3 0-9 4-11 3l4-4v-1h0c1-3 1-5 0-7v-1l-2-4 4 5c0-3-3-6-4-8 0 0-1-1-1-2a30.44 30.44 0 0 0-8-8c0-1-1-1-1-1l-3-3c1 0 1 0 1-1h-1v-1h1l-1-1c-1-1-1-2-2-4l2-1-3-2c-2-1-3-1-5-1v-1h-2c2-1 3-1 5-1h1v-1-2c2 1 2 2 3 3l1 1h1c1 0 2-1 2-1h1c1 0 3-1 4-1l-1-1c0-1 1-2 2-3v-1l2-2c1-3 3-4 4-6l1-1c2 1 3 0 4 0 1-1 2-2 4-3h1c1-1 2-1 3-1h0l-3 3h1c2-1 4-3 6-4 1 0 1 1 1 1h1l-1-1h2v1h0 1 1 2c1 1 2 0 3 1v1h-5 4c4 0 8 0 11 1s6 0 9 0h1 0l-1-1-1-1h2 3c2 0 2 1 4 0h0c1 0 1 0 2-1h0 3v-1h4l2-1h0 2l1 1z" class="K"></path><path d="M262 268h3c-1 1-1 2-2 3h-1l-2 1h-1c1-1 2-2 3-4z" class="Q"></path><path d="M298 274c1 3 1 5 1 8 0 2 1 6 0 7 0 0 0 1-1 1v-4-4h0 1c0-1 0-4-1-5v-3z" class="J"></path><path d="M262 271l1 1c1 0 5 1 5 2h-1v1c-1 0-2 0-3-1h-3v-1h2v-1h-3l2-1z" class="U"></path><path d="M280 311c1-1 2-1 2-1h1s1 0 1-1h1l2-2h1 0c0 1-1 2-2 2l1 1-4 2h-1c0-1 1-1 1-1v-1 1c-2 0-8 1-9 1h1c0-1 1-1 2-1h1 2z" class="B"></path><path d="M261 263l1-2h1c2 2 4 3 5 4l-3 3h-3c-3 1-6 1-10 1l3-1h0c3-1 5-3 6-5z" class="D"></path><path d="M294 307c1-1 2-1 2-2 1-1 2-2 2-4 1-1 2-2 3-4h0c0-2 0-2 1-3h0v-3c1-1 1-1 1-2v-9-3l-1-1c0-2-1-4-2-6l3 4c0-2-1-4-1-5h0c1 0 2 0 3 1l1 1v-1l2 2v-2c0 1 0 1 1 2h-1v2h0c-1 0-2-2-2-2-1 0-1 0-1-1 0 0-1-1-2-1h-1c1 1 2 2 1 3l-1 1 2 2h0c2 5 2 13 0 18-2 3-3 7-5 10l-1 1h0c-1 1-2 2-4 2z" class="C"></path><path d="M318 251l1-3c0 1-1 5 0 6 0 1 0 2-1 3h-1c0 1 0 0-1 1-2 0-4 0-6-1h0c-1 0-1 1-2 1-1-1-7-2-8-3h-1 0c-1 0-1-1-1-1l1-1c1 1 2 1 4 2h0c0-1 1-1 1-1h1l-1-1h0c1 0 1 0 2-1h0 3v-1h4l2-1h0 2l1 1z" class="E"></path><path d="M315 250h0 2l1 1-3 3h-2l2-2h0c-1 0-1 0-2-1l2-1z" class="B"></path><path d="M306 252h3v-1h4c1 1 1 1 2 1h0l-2 2c-1-1-3 0-4 0h-4l-1-1h0c1 0 1 0 2-1h0z" class="Q"></path><path d="M300 255c2 0 4 1 5 0h1 0 2 1c2 0 6 0 8-1h0 1c0 1-1 2-1 3s0 0-1 1c-2 0-4 0-6-1h0c-1 0-1 1-2 1-1-1-7-2-8-3z" class="C"></path><path d="M250 257l1-1c2 1 3 0 4 0 3 1 7 1 10 4v1h-2-1l-1 2c-1 2-3 4-6 5h0l-3 1c-2-1-2-1-4-3v-2 1c-1 1-3 2-4 4l-1 1-1-1c0-1 1-2 2-3v-1l2-2c1-3 3-4 4-6z" class="E"></path><path d="M254 262c2 0 2 0 3 1l-1 3h-1l-1-1v-3z" class="W"></path><path d="M256 260l1-1c1 0 1 0 2 1-1 1-1 2-2 3-1-1-1-1-3-1l2-2z" class="g"></path><path d="M253 259l1 1c-1 1-2 4-1 5l2 3-3 1c-2-1-2-1-4-3v-2h0c2-2 3-4 5-5z" class="G"></path><path d="M250 257l1-1c2 1 3 0 4 0 3 1 7 1 10 4v1h-2-1l-1 2h-1c-1-1-1-2-1-3-1-1-1-1-2-1l-1 1c-1-1-1-1-2-1h-1c-2 1-3 3-5 5h0v1c-1 1-3 2-4 4l-1 1-1-1c0-1 1-2 2-3v-1l2-2c1-3 3-4 4-6z" class="Y"></path><path d="M248 264v2c2 2 2 2 4 3 4 0 7 0 10-1-1 2-2 3-3 4h1 3v1h-2v1h3c1 1 2 1 3 1l1 1h0-5l4 1v1c-1 1-3-1-3 0 1 1 2 1 2 2h-5c3 1 5 2 8 3-2 0-3 0-4-1h-4c1 2 6 1 7 3-1 1-4-1-5-1-2 0-5 0-6 1-1 0-1 1-2 0h-1-3c1 0 1 0 2 1h1c-1 1-3-1-5-1h-1c-2-1-4-2-6-4v1h0l-7-6-3-2c-2-1-3-1-5-1v-1h-2c2-1 3-1 5-1h1v-1-2c2 1 2 2 3 3l1 1h1c1 0 2-1 2-1h1c1 0 3-1 4-1l1-1c1-2 3-3 4-4v-1z" class="X"></path><defs><linearGradient id="An" x1="247.094" y1="282.625" x2="244.876" y2="283.268" xlink:href="#B"><stop offset="0" stop-color="#444245"></stop><stop offset="1" stop-color="#585856"></stop></linearGradient></defs><path fill="url(#An)" d="M242 281h0l1-1h1 1c1 2 3 3 4 5h-1c-2-1-4-2-6-4z"></path><path d="M256 273l1-1h-2c-1 0-2-1-3-2h0c1 0 2 0 3 1h2c1 0 1 1 2 1h1 3v1h-2v1c-2 0-4 0-5-1zm-6 6c-1-1-2-1-3-2h-1c-1-1-1-2-2-3h0l-1-1h2l1-1c1-1 3-1 4-1l3 1c-2 0-4 0-5 2 0 0-1 0-1 1h1v2c1 0 1 1 2 2z" class="c"></path><path d="M248 264v2 4c-2 1-6 1-8 3h-6l-2 1c-2-1-3-1-5-1v-1h-2c2-1 3-1 5-1h1v-1-2c2 1 2 2 3 3l1 1h1c1 0 2-1 2-1h1c1 0 3-1 4-1l1-1c1-2 3-3 4-4v-1z" class="E"></path><path d="M248 265c0 2 0 3-2 4h-1-1c1-2 3-3 4-4z" class="L"></path><path d="M225 272c2-1 3-1 5-1 1 1 3 0 4 2l-2 1c-2-1-3-1-5-1v-1h-2z" class="B"></path><defs><linearGradient id="Ao" x1="255.556" y1="282.075" x2="253.75" y2="271.815" xlink:href="#B"><stop offset="0" stop-color="#656465"></stop><stop offset="1" stop-color="#8f8d8e"></stop></linearGradient></defs><path fill="url(#Ao)" d="M253 272l3 1c1 1 3 1 5 1h3c-1 1-4 1-4 2l4 1h-4c0 1 0 1 1 2h-1-1l2 2h1-7c-2-1-3-1-5-2h0c-1-1-1-2-2-2v-2h-1c0-1 1-1 1-1 1-2 3-2 5-2z"></path><path d="M286 270l-2-4c1 0 1 1 1 2l2 3c1-2-1-3 0-3 2 3 4 8 4 12v1c1 7-1 14-5 20-3 3-6 6-10 7l-2 1c-1 1-2 1-4 1-2 1-5 1-8 1v1c-1 0-3 1-4 0h0 1v-1l-2 2h-2c-2 1-5 2-6 4h0c1 0 1-1 3-1 1 0 3-1 4-1 0-1 0-1 1-1h0 2c1-1 2-1 3-1s3 0 4-1h2 3l1-1h1c-2 2-3 1-5 2l-6 1c-2 0-3 0-5 1-3 0-9 4-11 3l4-4v-1h0c1-3 1-5 0-7v-1l-2-4 4 5c0-3-3-6-4-8 0 0-1-1-1-2a30.44 30.44 0 0 0-8-8c0-1-1-1-1-1l-3-3c1 0 1 0 1-1h-1v-1h1l-1-1c-1-1-1-2-2-4l2-1 7 6h0l8 8c2 4 5 7 8 10 1 1 2 2 4 2l-1 1c2 2 4 3 5 5h2l2-1h0c1-1 2-1 3-2h3 1c1-1 2-1 3-2 3-3 6-6 8-10 1-5 1-9 1-14 0-2-1-3-1-5l-1-1c0-1 0-2-1-3z" class="N"></path><path d="M273 305h3 1c1-1 2-1 3-2-2 3-5 4-8 5l-2-1h0c1-1 2-1 3-2z" class="I"></path><path d="M250 306v-1l-2-4 4 5 3 6c-2 1-3 2-5 2v-1h0c1-3 1-5 0-7z" class="Q"></path><defs><linearGradient id="Ap" x1="254.119" y1="310.256" x2="254.198" y2="286.442" xlink:href="#B"><stop offset="0" stop-color="#09090a"></stop><stop offset="1" stop-color="#2d2d2c"></stop></linearGradient></defs><path fill="url(#Ap)" d="M235 276l7 6h0l8 8c2 4 5 7 8 10 1 1 2 2 4 2l-1 1c2 2 4 3 5 5h-1c-3 1-5-3-8-4 1 2 3 4 5 5s3 1 4 1h1 0c-2 1-5 1-8 1-1-1-1-2-2-3l-1-1v-1h-1c-1-1-2-4-3-5-3-5-7-11-12-15h-1c3 3 7 6 8 10a30.44 30.44 0 0 0-8-8c0-1-1-1-1-1l-3-3c1 0 1 0 1-1h-1v-1h1l-1-1c-1-1-1-2-2-4l2-1z"></path><path d="M240 284c-2 0-4-2-4-3 1-1 1 0 2 1l3 1 1-1h0l8 8c2 4 5 7 8 10 1 1 2 2 4 2l-1 1a30.44 30.44 0 0 1-8-8c-3-3-4-6-7-8-2-1-5-3-6-3z" class="c"></path><path d="M235 276l7 6-1 1-3-1c-1-1-1-2-2-1 0 1 2 3 4 3 7 6 11 14 15 22-1-1-2-4-3-5-3-5-7-11-12-15h-1c3 3 7 6 8 10a30.44 30.44 0 0 0-8-8c0-1-1-1-1-1l-3-3c1 0 1 0 1-1h-1v-1h1l-1-1c-1-1-1-2-2-4l2-1z" class="e"></path><path d="M269 283h0 1l1 1 8 4c1 0 3-2 4-2 1-1 2-3 2-4v-1c2-4 0-7 0-11h1c1 1 1 2 1 3l1 1c0 2 1 3 1 5 0 5 0 9-1 14-2 4-5 7-8 10-1 1-2 1-3 2h-1-3c-1 1-2 1-3 2h0l-2 1h-2c-1-2-3-3-5-5l1-1c-2 0-3-1-4-2-3-3-6-6-8-10l-8-8v-1c2 2 4 3 6 4h1c2 0 4 2 5 1h-1c-1-1-1-1-2-1h3 1c1 1 1 0 2 0 1-1 4-1 6-1 1 0 4 2 5 1-1-2-6-1-7-3h4c1 1 2 1 4 1z" class="C"></path><path d="M268 290c2 0 5 1 6 2-1 0-1 0-1 1h0l-5-3z" class="V"></path><path d="M250 290h0l-1-1h1v-2c1 0 3 2 4 2l-1-1c-1 0-1-1-2-1 3 0 5 1 7 2h1c1 0 6 1 6 2h-1l2 2v1l-1 1 1 1c0 1 0 4 1 4l1 1h1v1h-3v1c2 0 3 0 4-1h1 0v1h2 1l-1 1-1 1c1 0 1 0 1-1h2 0c1 0 1 0 2-1-1 2-2 2-4 2-1 1-2 1-3 2h0l-2 1h-2c-1-2-3-3-5-5l1-1c-2 0-3-1-4-2-3-3-6-6-8-10z" class="I"></path><path d="M262 302l5 5c2-1 3-1 4-2 1 0 1-1 2-1l-1 1c1 0 1 0 1-1h2 0c1 0 1 0 2-1-1 2-2 2-4 2-1 1-2 1-3 2h0l-2 1h-2c-1-2-3-3-5-5l1-1z" class="B"></path><defs><linearGradient id="Aq" x1="305.271" y1="311.964" x2="282.718" y2="287.652" xlink:href="#B"><stop offset="0" stop-color="#343434"></stop><stop offset="1" stop-color="#545353"></stop></linearGradient></defs><path fill="url(#Aq)" d="M304 276h0l-2-2 1-1c1-1 0-2-1-3h1c1 0 2 1 2 1 0 1 0 1 1 1 0 0 1 2 2 2h0v-2h1c2 4 4 8 4 13v1c0-1 0-1 1-1l1 4c0 6 0 11-2 17l-2 4c-1 1-6 5-6 6v1c-2 2-5 4-8 5-2 0-4 0-5-1h0-1-1-2-4l-5-1v-1h1 2l-1-1-4-2c-4-1-10-2-15-2l6-1c5-1 11 2 15-1 1 1 1 1 2 1 2 0 7-4 9-6 2 0 3-1 4-2h0l1-1c2-3 3-7 5-10 2-5 2-13 0-18z"></path><path d="M281 318c2 0 3 0 4 1-1 0-1 0-1 1l-4-1h2l-1-1z" class="T"></path><path d="M279 319h1l4 1v1l-5-1v-1z" class="C"></path><path d="M308 274v-2h1c2 4 4 8 4 13v1 8c-1-3 1-7-1-10v-3c-1-2-1-4-2-6-1 2 0 3 1 4 0 2 1 2 0 4v-1s0-1-1-2c0-1 0-5-2-6zm-4 2h1 0c0 2 1 3 2 5 0 4-1 9-1 13-1 0-2 1-3 2 0 1 0 2-1 3 0 2-1 3-2 5l-2 2v-1h0l1-1c2-3 3-7 5-10 2-5 2-13 0-18z" class="F"></path><path d="M313 286c0-1 0-1 1-1l1 4c0 6 0 11-2 17l-2 4c-1 1-6 5-6 6v1h-1l-1 1c-2 1-5 3-7 3h1v-2-1c3-1 7-4 9-7 3-4 7-12 7-17v-8z" class="S"></path><path d="M415 464h0 5 5c5 1 13 2 17 5 11 6 19 15 22 27 3 9 3 19 2 29-2 15-7 30-15 43-4 10-11 19-18 26-4 6-10 11-15 16-2 1-4 3-6 5-14 11-32 18-49 23-29 8-58 12-88 9-14-2-28-5-41-9-7-2-13-4-20-7-6-2-12-6-18-8v-1c1-1 4 0 5 0 11 0 22-2 32-5l-1-1-1 1h-1c1-2 4-2 5-3-1-2-3-1-5-1-1-1-2-1-3-1s-1-1-2-2c3 0 6-2 8-3h0v-1c2-1 3-1 5-2 4-2 9-5 13-7 2-3 4-4 7-6l1-1s1 0 2-1l-1-1h1c2-1 5-3 7-5l1-1c3-1 6-5 9-8h-1c0-1 2-2 3-3 2-2 4-3 6-5s3-3 4-5c2-1 5-3 6-6h-2c1-1 3-1 4-3 0-1 2-2 3-3 2-3 4-5 5-7v-1l3-3v1c0 1-1 2-2 4-1 1-6 6-6 8h0l2-2 4-4c2-3 3-6 5-9l5-9c0-2 1-4 2-5h0c1-2 1-4 1-6 1-3 3-6 3-9v-1-4c1-2 1-5 1-8h1v7 3c0 1-1 2 0 3 1 3-1 8 1 11v-4l1-1c0-1 0-1 1-2 0 1 0 2-1 3h1c1-1 1-2 2-4v-1h1c0 4 0 8-1 12l-1 9v4l1 1v4c0 9 0 17-2 25l-2 16h0c1-1 1-2 1-3l1-1v4c-1 1-1 2-1 3 0 0 0 1-1 2v1c0 1 0 1-1 2l-1 6 1 1-5 8h3 1c-3 5-6 8-10 12-2 1-2 2-4 3l-2 2h-1c-1 3-6 4-8 5-1-1-1-1 0-1v-1c-1 0-5 3-6 3v1h1 3c0-1 1 0 2-1 1 0 2 0 3-1h1c1 0 1-1 2-1h2s1 0 1 1h1 3 2c-2 2-6 3-7 5h0c-1 0-1 0-1 1h-5c-3 0-8 1-11 0-1-1-3-1-4-1h-1 0-2c-1 1-3 1-4 1l2-2c-1 0-1 0-1-1h0-1c-1 0-1 1-2 1l-1-1-11 2c-2 0-5-1-7 0l15 3c33 6 71 3 101-12 24-12 43-33 52-58 3-10 4-20 5-31 0-13-1-29-7-41-2-4-4-7-7-10-5-4-13-7-19-9-3-1-6-2-9-2 4-2 8-4 12-4 2 0 3-1 5-1h1l8-1z" class="d"></path><path d="M319 585c2-5 3-10 5-15h0c-1 8-3 16-6 23v-2s1-3 1-4h0c1-1 1-1 0-2z" class="I"></path><defs><linearGradient id="Ar" x1="424.305" y1="487.067" x2="433.342" y2="486.345" xlink:href="#B"><stop offset="0" stop-color="#080809"></stop><stop offset="1" stop-color="#262626"></stop></linearGradient></defs><path fill="url(#Ar)" d="M431 494c-1-1-1-3-2-5-2-3-5-6-6-10 0 1 2 2 3 3 2 1 4 4 6 6l3 5v1l-1-1-1-1-1 1h1v1h0l-3-3c0 1 1 2 1 2v1z"></path><defs><linearGradient id="As" x1="434.128" y1="485.598" x2="446.942" y2="486.44" xlink:href="#B"><stop offset="0" stop-color="#474646"></stop><stop offset="1" stop-color="#767576"></stop></linearGradient></defs><path fill="url(#As)" d="M436 483l-6-7c5 3 12 6 15 11 1 1 2 2 2 4v1h-2v1c-1-1-3-3-4-5 0-1-1-2-2-3h-1l-1-1c0-1-1-1-1-1z"></path><defs><linearGradient id="At" x1="427.57" y1="499.914" x2="449.882" y2="506.18" xlink:href="#B"><stop offset="0" stop-color="#1f2120"></stop><stop offset="1" stop-color="#413f41"></stop></linearGradient></defs><path fill="url(#At)" d="M431 494v-1s-1-1-1-2l3 3h0v-1h-1l1-1 1 1 1 1v-1c2 2 3 3 4 5 2 3 4 5 5 8l3 6h-1c1 1 2 3 2 4l-2-4h-1v1l2 3c0 1 0 1-1 1-2-5-6-8-9-12-2-4-4-7-6-11z"></path><path d="M433 493h-1l1-1 1 1 1 1v-1c2 2 3 3 4 5h-3 0c-1-2-2-3-3-5z" class="B"></path><defs><linearGradient id="Au" x1="431.8" y1="495.53" x2="445.71" y2="494.827" xlink:href="#B"><stop offset="0" stop-color="#302e2f"></stop><stop offset="1" stop-color="#6a6a6a"></stop></linearGradient></defs><path fill="url(#Au)" d="M432 488v-2c0-1-1-1-2-2v-1h1 1 0-1v-1h0c-1-1-1-2-2-3h1v-1h0c1 1 4 3 4 4l2 1s1 0 1 1l1 1h1v1 1h-1c0 1 1 2 2 3h0l2 3v1l3 7c1 2 2 5 3 8l-4-3c-1-3-3-5-5-8-1-2-2-3-4-5l-3-5z"></path><path d="M434 482l2 1s1 0 1 1l1 1h-1l-1 1-2-3v-1z" class="E"></path><path d="M445 501h-1c-2-2-3-5-4-8h0c1 0 1 0 2 1l3 7z" class="O"></path><defs><linearGradient id="Av" x1="452.937" y1="488.505" x2="418.269" y2="460.274" xlink:href="#B"><stop offset="0" stop-color="#59595a"></stop><stop offset="1" stop-color="#7b7b7b"></stop></linearGradient></defs><path fill="url(#Av)" d="M415 464h0 5 5c5 1 13 2 17 5v1c4 6 7 12 9 19 1 3 2 6 2 9v1h0c-1-2-1-4-2-5l-1 1v-2c-1 0-2-1-3-2 0-2-1-3-2-4 1-2-1-4-2-5-5-8-14-12-23-14-3-1-7-2-11-2l6-1v-1z"></path><path d="M290 582c1-1 1-1 2-1-1 2-3 3-4 5l1 1 3-3c-1 2-2 3-3 4s-2 2-2 3c-4 4-8 6-11 10-1 0-3 1-3 2v2l-2 2h-1c-2 2-4 4-6 5-9 7-19 13-30 16-3 1-7 2-10 2h-7c6-1 12-3 17-6l-1 1c3 0 5-1 7-2s4-2 7-3c3-2 6-5 10-7 2-2 5-5 8-7 1-1 2-3 3-3 2-2 3-5 6-5l1-1c3-2 5-5 8-8l7-7z" class="J"></path><defs><linearGradient id="Aw" x1="290.888" y1="593.032" x2="266.288" y2="596.656" xlink:href="#B"><stop offset="0" stop-color="#0f0f11"></stop><stop offset="1" stop-color="#323130"></stop></linearGradient></defs><path fill="url(#Aw)" d="M290 582c1-1 1-1 2-1-1 2-3 3-4 5l1 1 3-3c-1 2-2 3-3 4s-2 2-2 3c-4 4-8 6-11 10-1 0-3 1-3 2-2 2-3 4-5 5-2 0-3 1-4 3v-1l3-3c1-1 2-3 3-4s0-2 2-3c1 0 1-1 2-2l1-1c3-2 5-5 8-8l7-7z"></path><path d="M439 485c1 1 2 2 2 3 1 2 3 4 4 5v-1h2v-1c1 1 2 2 3 2v2l2 6c2 6 2 12 3 17 1 1 0 4 0 5l-1-1h0 0c1 3 0 6 0 9-1 0-1 2-1 2 0 1-1 2-1 3v6c0-8-1-15-6-22l1-1-1-2c1 0 1 0 1-1l-2-3v-1h1l2 4c0-1-1-3-2-4h1l-3-6 4 3c-1-3-2-6-3-8l-3-7v-1l-2-3h0c-1-1-2-2-2-3h1v-1-1z" class="T"></path><path d="M439 485c1 1 2 2 2 3 1 2 3 4 4 5 1 2 3 5 4 7h-2l1 2v2c-1-1-1-2-2-3-1-3-2-5-4-8l-2-3h0c-1-1-2-2-2-3h1v-1-1z" class="Q"></path><path d="M440 490c1 0 2 0 3 2 1 1 2 3 3 5 0 1 1 3 1 3l1 2v2c-1-1-1-2-2-3-1-3-2-5-4-8l-2-3z" class="a"></path><defs><linearGradient id="Ax" x1="446.644" y1="530.553" x2="453.212" y2="519.236" xlink:href="#B"><stop offset="0" stop-color="#1b1c20"></stop><stop offset="1" stop-color="#373534"></stop></linearGradient></defs><path fill="url(#Ax)" d="M446 517c1 0 1 0 1-1l-2-3v-1h1l2 4c0-1-1-3-2-4h1l6 14v-1c1-2 0-5-1-7 2 1 2 2 2 4h0 0c1 3 0 6 0 9-1 0-1 2-1 2 0 1-1 2-1 3v6c0-8-1-15-6-22l1-1-1-2z"></path><defs><linearGradient id="Ay" x1="445.344" y1="510.764" x2="454.347" y2="495.178" xlink:href="#B"><stop offset="0" stop-color="#494848"></stop><stop offset="1" stop-color="#7b7b7c"></stop></linearGradient></defs><path fill="url(#Ay)" d="M447 491c1 1 2 2 3 2v2l2 6c2 6 2 12 3 17 1 1 0 4 0 5l-1-1c0-2 0-3-2-4-1-5-2-11-4-16l-1-2h2c-1-2-3-5-4-7v-1h2v-1z"></path><path d="M447 500h2l3 9c0 3 1 7 2 10l1-1c1 1 0 4 0 5l-1-1c0-2 0-3-2-4-1-5-2-11-4-16l-1-2z" class="C"></path><defs><linearGradient id="Az" x1="437.093" y1="591.387" x2="396.512" y2="545.915" xlink:href="#B"><stop offset="0" stop-color="#3a393a"></stop><stop offset="1" stop-color="#6c6c6c"></stop></linearGradient></defs><path fill="url(#Az)" d="M446 536c-1-10-6-19-11-28 3 2 5 5 7 7l4 5c5 7 6 14 6 22-1 5-1 10-2 15-2 9-6 18-11 26-2 4-5 7-6 11h0c-4 6-10 11-15 16-2 1-4 3-6 5 0-1 1-2 2-3h0s1-1 2-1l-1-1c-1 1-3 2-5 3l-14 8c-1 1-3 2-4 2l2-2c-1 0-2 1-3 0h-1c-1 0-2 1-3 1 3-2 7-4 11-6 9-5 19-12 25-20 3-4 5-8 7-12h0v2c1 0 2-1 3-2 1-3 2-5 3-7 0-1 0-1 1-2h0c0-1 1-2 1-3h-1c1-1 1-1 1-2h0 0v2h0l1-1c1-3 1-7 2-10 1-7 2-15 1-22 0-3-1-7-2-11-1-3-3-6-3-10 3 4 5 9 7 13 1 2 1 4 2 6v-1z"></path><path d="M433 584c1-3 2-5 3-7 0-1 0-1 1-2h0c0-1 1-2 1-3h-1c1-1 1-1 1-2h0 0v2h0l1-1c1-3 1-7 2-10 1-7 2-15 1-22 0-3-1-7-2-11-1-3-3-6-3-10 3 4 5 9 7 13 1 2 1 4 2 6v-1c1 3 1 7 1 10h-1-1c0-2 1-5 0-7v-2-1h-1l-1-3c0-2-1-3-1-4s-1-2-2-3c2 5 2 10 3 15 1 3 1 5 1 7 1 1 1 4 1 6 0 4-1 8-2 13h0 0c0-2 0-4 1-6v-2c-1-2 0-5-1-7-1 4-1 8-2 11 0 1 0 2-1 3-1 8-4 14-8 21 0-1 1-2 1-3z" class="B"></path><path d="M442 469c11 6 19 15 22 27 3 9 3 19 2 29-2 15-7 30-15 43-4 10-11 19-18 26h0c1-4 4-7 6-11 5-8 9-17 11-26 1-5 1-10 2-15v-6c0-1 1-2 1-3 0 0 0-2 1-2 0-3 1-6 0-9h0 0l1 1c0-1 1-4 0-5-1-5-1-11-3-17l-2-6 1-1c1 1 1 3 2 5h0v-1c0-3-1-6-2-9-2-7-5-13-9-19v-1z" class="d"></path><defs><linearGradient id="BA" x1="463.644" y1="504.984" x2="449.803" y2="491.563" xlink:href="#B"><stop offset="0" stop-color="#272826"></stop><stop offset="1" stop-color="#403f42"></stop></linearGradient></defs><path fill="url(#BA)" d="M454 496h0v-4-9c4 12 8 25 6 37-1-2 1-6 0-9-1 1 0 3-1 5-1-2 0-4 0-6-1-2 0-4-1-5-2-4-1-9-3-12h-1v3z"></path><path d="M454 496v-3h1c2 3 1 8 3 12 1 1 0 3 1 5-1 0-2 0-2 1v7c-1 1-1 2-1 3v5c0-1-1-2-1-3s1-4 0-5c-1-5-1-11-3-17l-2-6 1-1c1 1 1 3 2 5h0v-1h1v-2z" class="L"></path><path d="M454 496v-3h1c2 3 1 8 3 12v1h-1v-2c-1 0-1 0-1-1v-1h0v1c0 2 1 8 0 9-1-1-1-6-2-8v-4-2-2z" class="U"></path><defs><linearGradient id="BB" x1="267.299" y1="554.777" x2="327.57" y2="556.474" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#605f5f"></stop></linearGradient></defs><path fill="url(#BB)" d="M324 494h1v7 3c0 1-1 2 0 3 1 3-1 8 1 11v-4l1-1c0-1 0-1 1-2 0 1 0 2-1 3h1c1-1 1-2 2-4v-1h1c0 4 0 8-1 12l-1 9v4c-1 9-4 17-7 25-3 7-6 14-10 20-7 11-17 21-27 29-3 1-5 3-8 4-1 1-2 1-2 2l-1-1c-2 1-7 4-8 4 2-1 4-3 6-5 1 0 2-2 3-3 7-6 13-12 18-20l6-8 11-23c4-11 7-24 9-36h0c1-2 1-4 1-6 1-3 3-6 3-9v-1-4c1-2 1-5 1-8z"></path><path d="M299 581l-1 2 2-2v-1c0 2-5 9-6 11l-1 1v-3l6-8z" class="I"></path><path d="M330 510v-1h1c0 4 0 8-1 12l-1 9v4c-1 9-4 17-7 25l-1-1c-1-2-2-5-3-8v-4c0-1 0-1-1-2v-2c1-1 1-2 0-2v4h-1l1-4v-1-1-1c1-1 1-2 1-3 0-2 1-3 1-4 0-2 1-3 1-5l1 1c0-1 1-2 2-3 1-2 3-1 5-2h0 0 1c0-2 1-4 1-6v-5z" class="a"></path><path d="M321 573c1 0 1-1 1-2v2 1c0 1 0 2-1 3 0 2-1 3-1 5 0 1-1 2-2 2l1 1c1 1 1 1 0 2h0c0 1-1 4-1 4v2c0 4-3 9-5 13 1-1 2-3 3-4v-1c1 0 1-1 2-1 0-1 1-3 2-4v-1-1c1-1 1-2 2-2v-1c2-3 2-8 4-11h0c1-1 1-2 1-3l1-1v4c-1 1-1 2-1 3 0 0 0 1-1 2v1c0 1 0 1-1 2l-1 6 1 1-5 8h3 1c-3 5-6 8-10 12-2 1-2 2-4 3l-2 2h-1c-1 3-6 4-8 5-1-1-1-1 0-1v-1c-1 0-5 3-6 3v1h1 3c0-1 1 0 2-1 1 0 2 0 3-1h1c1 0 1-1 2-1h2s1 0 1 1h1 3 2c-2 2-6 3-7 5h0c-1 0-1 0-1 1h-5c-3 0-8 1-11 0-1-1-3-1-4-1h-1 0-2c-1 1-3 1-4 1l2-2c-1 0-1 0-1-1h0-1c-1 0-1 1-2 1l-1-1-11 2c-2 0-5-1-7 0h0c-1 0-2-1-3-1l3-1 8-3c2-1 3-2 5-3 5-2 10-4 15-7 1-1 3-2 3-3l1-1c2-1 5-3 6-5 1-1 3-3 3-4h1c0-1 1-2 2-3h0c1-2 3-4 4-6l8-12c2-2 4-5 6-7v-1h1 0z" class="N"></path><path d="M266 625c2 0 3 0 5-1l1 1c-4 1-7 2-11 3h-1 0-2 0l8-3z" class="E"></path><path d="M295 623l3-3c4-3 8-6 11-10v1c-1 1-2 3-4 5-3 3-5 6-10 8v-1z" class="C"></path><path d="M295 623v1c-3 2-7 3-10 5v1h0-2c-1 1-3 1-4 1l2-2c-1 0-1 0-1-1h0-1c-1 0-1 1-2 1l-1-1c1 0 2 0 3-1 2 0 4 0 6-1 4 0 7-2 10-3z" class="Z"></path><path d="M312 625h2c-2 2-6 3-7 5h0c-1 0-1 0-1 1h-5c-3 0-8 1-11 0-1-1-3-1-4-1 2-1 4-1 5-1 5-1 9-3 14-3-1 1-2 1-3 2 3-1 7-1 10-3z" class="G"></path><path d="M326 580h0c1-1 1-2 1-3l1-1v4c-1 1-1 2-1 3 0 0 0 1-1 2v1c0 1 0 1-1 2l-1 6 1 1-5 8h3 1c-3 5-6 8-10 12-2 1-2 2-4 3l-2 2-3 1h0-1 0l4-3 1-1c0-2 2-4 3-5 1-2 3-4 4-6 3-4 5-10 6-15 2-3 2-8 4-11z" class="V"></path><defs><linearGradient id="BC" x1="299.768" y1="572.615" x2="299.286" y2="622.581" xlink:href="#B"><stop offset="0" stop-color="#0c0b0a"></stop><stop offset="1" stop-color="#3c3b3c"></stop></linearGradient></defs><path fill="url(#BC)" d="M302 599h0c1-2 3-4 4-6l8-12c2-2 4-5 6-7v-1h1 0c0 3-1 5-2 7-1 3-3 6-5 9 0 1-1 2-1 3-1 1-2 3-3 4-1 2-2 3-2 5-1 1-3 4-4 5l-9 9c-6 6-16 9-23 10l-1-1c-2 1-3 1-5 1 2-1 3-2 5-3 5-2 10-4 15-7 1-1 3-2 3-3l1-1c2-1 5-3 6-5 1-1 3-3 3-4h1c0-1 1-2 2-3z"></path><path d="M295 615l-1-1c1 0 1-1 2-1 1-3 5-7 8-7l-9 9z" class="C"></path><path d="M302 599c1 0 2-2 3-3h1c-2 3-4 5-5 8h0 0c-1 1-1 2-2 2l-5 6-9 6c-4 3-8 6-14 6-2 1-3 1-5 1 2-1 3-2 5-3 5-2 10-4 15-7 1-1 3-2 3-3l1-1c2-1 5-3 6-5 1-1 3-3 3-4h1c0-1 1-2 2-3z" class="B"></path><defs><linearGradient id="BD" x1="326.574" y1="542.144" x2="220.887" y2="601.885" xlink:href="#B"><stop offset="0" stop-color="#090909"></stop><stop offset="1" stop-color="#2c2b2c"></stop></linearGradient></defs><path fill="url(#BD)" d="M307 545c2-3 3-6 5-9l5-9c0-2 1-4 2-5-2 12-5 25-9 36 0-1-1-1-1-2-1 3-3 5-4 7s-1 5-2 7c-1 1-2 4-4 5-1 2-2 3-4 5-1 1-2 3-3 4l-3 3-1-1c1-2 3-3 4-5-1 0-1 0-2 1l-7 7c-3 3-5 6-8 8l-1 1c-3 0-4 3-6 5-1 0-2 2-3 3-3 2-6 5-8 7-4 2-7 5-10 7-3 1-5 2-7 3s-4 2-7 2l1-1c6-4 11-10 15-15-3 0-5 3-7 4-3 1-6 3-9 4l-1-1-1 1h-1c1-2 4-2 5-3-1-2-3-1-5-1-1-1-2-1-3-1s-1-1-2-2c3 0 6-2 8-3h0v-1c2-1 3-1 5-2 4-2 9-5 13-7 2-3 4-4 7-6l1-1s1 0 2-1l-1-1h1c2-1 5-3 7-5l1-1c3-1 6-5 9-8h-1c0-1 2-2 3-3 2-2 4-3 6-5s3-3 4-5c2-1 5-3 6-6h-2c1-1 3-1 4-3 0-1 2-2 3-3 2-3 4-5 5-7v-1l3-3v1c0 1-1 2-2 4-1 1-6 6-6 8h0l2-2 4-4z"></path><path d="M303 549l4-4c-1 1-1 2-2 4v-1c-1 1-1 1-2 1z" class="Y"></path><path d="M300 564l1-2c0-1 1-2 1-3 1-1 2-3 3-4s1-4 2-5l1 1-3 6c-1 2-3 5-5 8v-1z" class="N"></path><path d="M261 588c2-1 5-3 7-5l-2 2v1c-1 0-1 1-2 1-3 3-5 4-8 6-2 1-3 3-5 4 2-3 4-4 7-6l1-1s1 0 2-1l-1-1h1z" class="K"></path><path d="M286 575l18-20c-2 2-3 4-4 6s-2 3-3 4c-2 3-4 5-5 7a30.44 30.44 0 0 0 8-8v1c-2 2-5 7-8 9h-2v-1l-3 3v-1h-1z" class="C"></path><path d="M306 542v-1l3-3v1c0 1-1 2-2 4-1 1-6 6-6 8h0 0c0 1-1 1-1 2 0 2-4 5-5 6-2 2-3 4-4 6-3 2-5 5-8 7-5 5-10 10-16 14h0 0c-1 1-2 1-3 1 1 0 1-1 2-1v-1l2-2 1-1c3-1 6-5 9-8h-1c0-1 2-2 3-3 2-2 4-3 6-5s3-3 4-5c2-1 5-3 6-6h-2c1-1 3-1 4-3 0-1 2-2 3-3 2-3 4-5 5-7z" class="B"></path><defs><linearGradient id="BE" x1="267.075" y1="603.428" x2="257.184" y2="595.412" xlink:href="#B"><stop offset="0" stop-color="#3d3c3d"></stop><stop offset="1" stop-color="#616060"></stop></linearGradient></defs><path fill="url(#BE)" d="M286 575h1v1l3-3v1h2l-19 20h0c1 0 2-2 3-3l6-5h0l2-1h0l-11 12c1 0 3-2 4-3l8-8c1-1 2-4 4-4h1l-7 7c-3 3-5 6-8 8l-1 1c-3 0-4 3-6 5-1 0-2 2-3 3-3 2-6 5-8 7-4 2-7 5-10 7-3 1-5 2-7 3s-4 2-7 2l1-1c6-4 11-10 15-15h0c2-1 3-4 5-6s5-5 8-7c8-7 16-13 24-21z"></path><path d="M273 589h1c-2 3-4 4-6 6-4 3-8 7-11 10h0v-3c1 0 2-1 3-2 0-1 2-2 3-2l10-9z" class="X"></path><path d="M305 316c0-1 5-5 6-6l-3 8v6c0 1 0 2 1 3v3h0v-1l-1 1h0-1c0 3 1 5 1 7h-1v-1h-1v5 1 1h1v-1l1 1v-1c1 1 2 3 3 4l-1 1 4 4c1 2 4 5 6 6h1c1 1 3 2 4 3 2 1 5 2 7 3h2c0 1 1 1 1 1h1c1 2 3 2 5 3 1 0 2 1 3 1h1l4 1c5 1 10 8 12 12l1 2h1l1 3c0 1 1 1 1 2s-1 0-1 1l3 6c2 3 2 5 2 8-1-2-3-7-5-8 1 2 2 5 3 7-2-2-5-6-7-8 0 2 2 5 3 7l5 9c4 8 5 16 6 25v1 2l1 5h0v2 2h0c3 7 6 13 8 20 0 1 1 6 1 7-1 2 0 3 0 5 1 3 2 6 2 9l1 8c2 3 5 8 6 11 1 2 1 3 2 5 0 4 1 9 0 13 0 2-1 4-1 6 1 3 2 7 3 10 0 4-1 8-1 12l-2-9c0 7 0 13-3 19l1-6h-1 0c0 2-2 5 0 7-1 1-1 2-1 3-1-2-2-5-2-7-2 4-1 11-4 14-1 1-2 2-2 4-1 2-3 4-4 6v-1h-1v1h0c-1 0-2 1-2 1v1h0-2c0 1 0 2-1 3-1 2-2 3-3 5 0-2 0-3-1-4v-1l1-1v-3-2-4h-2c-1-5 0-9-1-13h-1v2 3h0v-2c-1 1-2 1-3 1l-1-3h0c0 4-2 8-3 12-1 3-2 5-3 7-1 3-2 6-4 9s-4 5-6 8c-1 2-3 5-5 6h-1c0 1-1 1-1 2-1 1-2 1-3 2 0 2-4 6-6 7h0 0 1 0c0 1 0 1-1 1h-1c-1 0-1 1-2 1 0 1 0 1-1 1h0c-2 2-5 3-8 4h-1l-9 3c1-2 5-3 7-5h-2-3-1c0-1-1-1-1-1h-2c-1 0-1 1-2 1h-1c-1 1-2 1-3 1-1 1-2 0-2 1h-3-1v-1c1 0 5-3 6-3v1c-1 0-1 0 0 1 2-1 7-2 8-5h1l2-2c2-1 2-2 4-3 4-4 7-7 10-12h-1-3l5-8-1-1 1-6c1-1 1-1 1-2v-1c1-1 1-2 1-2 0-1 0-2 1-3v-4l-1 1c0 1 0 2-1 3h0l2-16c2-8 2-16 2-25v-4l-1-1v-4l1-9c1-4 1-8 1-12h-1v1c-1 2-1 3-2 4h-1c1-1 1-2 1-3-1 1-1 1-1 2l-1 1v4c-2-3 0-8-1-11-1-1 0-2 0-3v-3-7h-1v-8h0c-2 4 0 10-2 14v2 1 4c-1 1-1 1-1 2v1c0-3 1-7 0-10-1-1-1-7 0-8 0-1-1-1-1-1 0-3 1-6 0-8l1-2v-1-4c-1-2 0-5-1-7v-2c-1-2 0-3-1-4v-2c0-1-1-4-1-4-1-1-1-2-1-3 0 1 0 2-1 2-1-1-3-4-4-6h0c1-2 1-4 3-4v-2c1 0 1 1 1 1 0 1 1 2 1 2l1 1v-1-7-6c0-4 2-7 1-10 0-2 0-4-1-6h0c0-1-1-5 0-6v-2c-1-5-2-9-4-14l-3-6-1-1v-4l-1-1h-1l-1 1h-1v-4c0-5 1-10 0-15 0 2 1 5 0 7v-1c0-1 0-1-1-2v-7c1-2 0-5 0-6h-1v-1-2-2c-1 2-1 3-1 5 0-2-1-4 0-6v-2c0-1-1-1-1-2v-1-3-4h-2 0 0c0-1-1-3 0-4v-4s1-1 1-2c-1 2-2 3-3 4 1-4 4-7 6-11l-1-1c0 1-1 2-2 3h0v-2l1-1c1-1 1-2 2-2 2-1 3-3 4-5l-3 2z" class="K"></path><path d="M371 461v-2h0c1 1 1 2 2 3 0 1 1 2 0 4-1 0-1-1-2-1v-1h0c0-1 1-1 0-3z" class="J"></path><path d="M393 507v1c0-1-2-4-3-5s-2-1-3-2c0-1-1-2-2-3 0-1-1-1-2-2-1 0-1-1-2-2h0c1 0 2 1 3 1 0-1-1-2 0-3 0 1 1 1 1 2s1 2 2 2c2 3 5 8 6 11zm-43-68c0-1 0-2-1-2 1 0 0 1 0 1v1c0-1 0-2-1-2l-1 4h0 0c0-1 1-4 0-5l1-3v-5c1-3 0-6 0-9 1 2 1 4 2 7v4h1c0 3-1 6-1 9h0z" class="B"></path><path d="M386 488c-1 0-1-1-1-2v-1c-1 0-1-1 0-1-1-1-1-1-1-2h0v-1c0-1-1-1-1-2v-3c0-1-1-1-1-1h-1c-1-3-2-7-3-10l-1-1v-1-2c1 2 1 3 2 4 0 1 0 2 1 4v-1h1c0 1 0 2 1 3l1 1v-2c-1-1 0-2 0-3 0 1 1 6 1 7-1 2 0 3 0 5 1 3 2 6 2 9z" class="V"></path><path d="M375 447l-8-25c2 3 5 8 6 12v1h1v1 2l1 5h0v2 2h0z" class="H"></path><path d="M351 430v-8c-1-2-1-3-1-5 1 2 2 4 2 6v5 9c-1 4-1 8-2 12 0-2 0-3-1-4l-2 11h-1v-2c0-1 1-1 1-2v-1-1c1-2 1-5 2-6v-3c1-1 1-2 1-2h0c0-3 1-6 1-9z" class="L"></path><path d="M333 393c0-3-14-19-16-23 2 1 4 3 5 5 5 6 9 12 13 18 1 2 2 3 3 5v2 2c1 0 0 2 0 2h0 0 0c0-1-1-2-1-2l-4-9z" class="I"></path><path d="M369 476c1 0 2-1 2-1v-2h0v-2 1l1-2v-1c1 2 0 3 0 4h0c0 1 1 1 1 2h0c0-1 1-2 1-2 2 1 2 2 3 4 0 1 0 1 1 2v-4h0c1 1 1 3 0 5 0 2 1 3 1 5h-1v-1-2c-1-1-1-1-1-2h0l-3 3h0c-1 1-2 2-3 2l1 2c-2-1-3-4-4-7 0 0 0-1 1-2v-2z" class="O"></path><defs><linearGradient id="BF" x1="383.89" y1="521.302" x2="386.42" y2="514.168" xlink:href="#B"><stop offset="0" stop-color="#505050"></stop><stop offset="1" stop-color="#696767"></stop></linearGradient></defs><path fill="url(#BF)" d="M380 511h1 1 1c2 2 4 5 5 8 2 2 3 5 4 8h0 1v-2h0c0-2 0-4 1-6 0-2-1-4 0-6v-1h1c0 4 1 9 0 13 0 2-1 4-1 6-1-2-3-6-4-7l-1 1h-1v-1h0c1 0 1 0 1-1h-2s0-1-1-1h-3l1-3-1-1c0-3-2-5-3-7z"></path><path d="M382 511h1c2 2 4 5 5 8l-6-8z" class="c"></path><path d="M366 474v1c1 1 1 1 1 2 1 1 1 2 1 3 1 3 2 6 4 7 1 1 1 2 1 3s2 4 2 4c-1 0-1-1-2-1 0 0 0 1 1 2h1c1 1 2 2 3 4s2 3 4 5l-2-4h0c1 1 2 2 2 3 1 1 2 1 2 2 1 2 3 3 4 5-1 0-1-1-2-1h0c-1-1-2-1-3-2l-3-3c0-1-1-1-1-2l-2-2-1 1v-1h-1c1 2 2 3 3 4 1 2 4 4 6 6 0 0 1 0 1 1h1-1 0c-1 0-1 0-2-1v1h-1-1-1c0-1 0-1-1-1h0 0c-1-2-2-3-3-4l-3-3s0 1 1 2c-2-2-4-5-5-7l-1-2s0-1 1-1v-1l1 1h1l-2-5c-1-1-1-1 0-2l-1-1h1l-2-5c-1-1-1-3-2-5l1-3z" class="B"></path><defs><linearGradient id="BG" x1="374.387" y1="487.878" x2="364.633" y2="484.694" xlink:href="#B"><stop offset="0" stop-color="#3d3d3b"></stop><stop offset="1" stop-color="#5a595b"></stop></linearGradient></defs><path fill="url(#BG)" d="M366 474v1c1 1 1 1 1 2 1 1 1 2 1 3 1 3 2 6 4 7 1 1 1 2 1 3s2 4 2 4c-1 0-1-1-2-1 0 0 0 1 1 2 0 1 1 2 1 3h-1c-2-3-4-7-5-11l-2-5c-1-1-1-3-2-5l1-3z"></path><path d="M366 474v1c1 1 1 1 1 2v5c-1-1-1-3-2-5l1-3z" class="U"></path><defs><linearGradient id="BH" x1="377.803" y1="499.822" x2="373.622" y2="506.345" xlink:href="#B"><stop offset="0" stop-color="#444542"></stop><stop offset="1" stop-color="#78777b"></stop></linearGradient></defs><path fill="url(#BH)" d="M369 494l1 1h1c3 5 5 8 9 12 1 1 2 3 3 3v1h-1-1-1c0-1 0-1-1-1h0 0c-1-2-2-3-3-4l-3-3s0 1 1 2c-2-2-4-5-5-7l-1-2s0-1 1-1v-1z"></path><path d="M368 496s0-1 1-1l1 2h0l-1 1-1-2z" class="Q"></path><defs><linearGradient id="BI" x1="361.344" y1="413.126" x2="359.352" y2="470.231" xlink:href="#B"><stop offset="0" stop-color="#010202"></stop><stop offset="1" stop-color="#2b2b2b"></stop></linearGradient></defs><path fill="url(#BI)" d="M355 424c-2-5-5-10-8-15 0 0-1-1 0-1 1 1 2 3 4 5l7 9c1 2 3 4 4 7 0 2 2 4 2 6v1c-1 0-1-1-1-2 0 1 0 2 1 3 1 2 2 3 2 5v-1c-1-1-1-3-2-3 1 5 3 10 4 15 1 2 1 4 2 5 0 1 0 2 1 3h0c1 2 0 2 0 3h0v5 2c-1 1-2 3-2 5v2c-1 1-1 2-1 2 0-1 0-2-1-3 0-1 0-1-1-2v-1l-1 3-1-4-1-2s0 2-1 3v-5c0-1-1-3-1-5v-3-4c-1-2-1-3-2-5-1-3 1-6 0-9-1-1-1-1-1-2h0 0l-2-6-1-6c0-1-1-3-1-4h0v-1h1z"></path><path d="M364 473v-4 1h1l1 1v3l-1 3-1-4z" class="V"></path><defs><linearGradient id="BJ" x1="366.014" y1="467.977" x2="371.384" y2="474.047" xlink:href="#B"><stop offset="0" stop-color="#393839"></stop><stop offset="1" stop-color="#5f5e5f"></stop></linearGradient></defs><path fill="url(#BJ)" d="M365 470c0-1 0-2 2-2l1-1c1-1 0-3 1-4v-1l1-1v1 3h0l1-1h0v5 2c-1 1-2 3-2 5v2c-1 1-1 2-1 2 0-1 0-2-1-3 0-1 0-1-1-2v-1-3l-1-1z"></path><path d="M355 429c0-1-1-3-1-4h0v-1h1c1 3 3 6 3 8 2 6 3 11 4 17l1 20h-1c0-1-1-3-1-5v-3-4c-1-2-1-3-2-5-1-3 1-6 0-9-1-1-1-1-1-2h0 0l-2-6-1-6z" class="E"></path><path d="M362 449h0-1c0-2-1-4-1-6-1-2-1-4-2-6v-4-1c2 6 3 11 4 17z" class="F"></path><defs><linearGradient id="BK" x1="353.057" y1="372.741" x2="337.671" y2="382.199" xlink:href="#B"><stop offset="0" stop-color="#171717"></stop><stop offset="1" stop-color="#5c5c5d"></stop></linearGradient></defs><path fill="url(#BK)" d="M321 357c1 1 3 2 4 3 2 1 5 2 7 3h2c0 1 1 1 1 1h1c1 2 3 2 5 3 1 0 2 1 3 1h1l4 1c5 1 10 8 12 12l1 2h1l1 3c0 1 1 1 1 2s-1 0-1 1l3 6c2 3 2 5 2 8-1-2-3-7-5-8 1 2 2 5 3 7-2-2-5-6-7-8-1-2-4-5-6-6l-20-17c-1 0-2 0-2-1v-1h0c-1-1-3-2-4-3h-1 0-1l-1-1 1-1c-2-1-4-1-4-3-2-1-4-2-5-3h1 1 0c1 0 1 0 2-1z"></path><path d="M341 367c1 0 2 1 3 1 2 1 3 2 4 4h0c-2-1-3-2-5-3-1 0-2 0-3-1l1-1z" class="U"></path><path d="M332 363h2c0 1 1 1 1 1h1c1 2 3 2 5 3l-1 1c-1-1-4-2-5-3s-2-1-3-2z" class="T"></path><defs><linearGradient id="BL" x1="354.803" y1="382.268" x2="351.86" y2="367.896" xlink:href="#B"><stop offset="0" stop-color="#17171b"></stop><stop offset="1" stop-color="#323030"></stop></linearGradient></defs><path fill="url(#BL)" d="M345 368l4 1c5 1 10 8 12 12l1 2h1l1 3s-1 0-1-1h0l-3-2c-1-1-2-2-3-2v-1h-1c0-1-1-1-1-2-1-1-2-2-3-2-1-1-3-2-4-4h0c-1-2-2-3-4-4h1z"></path><path d="M322 361c1 0 7 3 7 3l16 9h0v1c1 1 3 3 5 4 1 0 1 1 2 2 2 0 4 3 6 4-1 1-1 1-2 0-1 0-2-1-3-2s-3-2-5-3c-3-3-7-6-12-7-1 0-1-1-2-1h0c-1 0-2 0-2-1v-1h0c-1-1-3-2-4-3h-1 0-1l-1-1 1-1c-2-1-4-1-4-3z" class="L"></path><path d="M322 361c1 0 7 3 7 3v1c2 1 4 4 6 5 2 0 3 1 4 1l1 1 2 1h0-1c-1-1-2-1-3-1-1-1-1-1-2-1s-3-1-4-2h0c-1-1-3-2-4-3h-1 0-1l-1-1 1-1c-2-1-4-1-4-3z" class="V"></path><path d="M334 371h0c1 0 1 1 2 1 5 1 9 4 12 7 2 1 4 2 5 3s2 2 3 2c1 1 1 1 2 0 0 1 1 2 1 2 0 1 0 1 1 2h-1c1 2 3 4 4 5 0 1 0 2 1 2h0c1 2 2 5 3 7-2-2-5-6-7-8-1-2-4-5-6-6l-20-17z" class="N"></path><defs><linearGradient id="BM" x1="328.984" y1="428.43" x2="341.862" y2="426.676" xlink:href="#B"><stop offset="0" stop-color="#404040"></stop><stop offset="1" stop-color="#7c7c7c"></stop></linearGradient></defs><path fill="url(#BM)" d="M325 383l2 2v1c2 2 3 3 4 5 0 1 1 2 2 2l4 9c2 7 4 15 5 23 1 4 1 10 1 14 0 2-1 4 0 5-1 2-1 4-2 6v4 4c-1 0 0 2 0 3-2 2-2 6-4 9 0 1 0 2 1 3h-1c0-3-2-6-3-10v1h-1c-1-13 2-25 2-38 0-4-1-8-1-12 1-1 0-2 0-3s-1-3-1-4h1l-5-12v-3h0 0c-1 0-1-1-1-1h0v-1c0-1-1-1-1-2-1-2-2-3-2-5z"></path><path d="M342 425c1 4 1 10 1 14 0 2-1 4 0 5-1 2-1 4-2 6h0c0 3 0 7-1 11l-1-1c0-2 0-5 1-7v-4c1-1 0-3 1-5v-4c1-3 1-6 2-9 0-2-1-4-1-6z" class="F"></path><path d="M334 404h1l3 9c-1 2 0 4 0 5 0 3 1 5 0 7h0l-1-4v4 6h0c0-1 0-1-1-2v-3-7c-1-1 0-2-1-3v-3l1-1-1-2v-1-2c-1-1-1-2-1-3z" class="e"></path><defs><linearGradient id="BN" x1="345.557" y1="462.353" x2="377.848" y2="475.81" xlink:href="#B"><stop offset="0" stop-color="#050505"></stop><stop offset="1" stop-color="#424142"></stop></linearGradient></defs><path fill="url(#BN)" d="M352 428c1-1 1-4 0-5h0v-4c0 1 1 2 2 4v1 1h0c0 1 1 3 1 4l1 6 2 6h0 0c0 1 0 1 1 2 1 3-1 6 0 9 1 2 1 3 2 5v4 3c0 2 1 4 1 5v5c1-1 1-3 1-3l1 2 1 4c1 2 1 4 2 5l2 5h-1l1 1c-1 1-1 1 0 2l2 5h-1l-1-1v1c-1 0-1 1-1 1h-1l-1 1c0-1-1-2-2-2v-1h-2l-1-1c-1 1-1 2-1 3v1l-2-2c0-1 0-2-1-2 0-1 0-1-1-1h0l-1-1-1 1c-1-2-1-5-2-7 0-1 0-3-1-4l-1-6c-2-9-1-17 0-26 1-4 1-8 2-12v-9z"></path><path d="M367 496c-1-2-2-5-2-7l4 5v1c-1 0-1 1-1 1h-1z" class="U"></path><path d="M355 429l1 6v3c-1 3 0 6-1 8v4h0v-7l-1 4c0-1-1-3 0-4v-8c0-1 0-4 1-6z" class="S"></path><path d="M356 435l2 6h0 0c0 1 0 1 1 2 1 3-1 6 0 9 1 2 1 3 2 5v4 3l-1-4v-1c-1 0-1-1-1-1h0c-1-2 0-4-2-6-1-2 1-5 0-7v-1h0c-1-2 0-4-1-6v-3z" class="C"></path><path d="M352 428c1-1 1-4 0-5h0v-4c0 1 1 2 2 4v1 1h0c0 1 1 3 1 4-1 2-1 5-1 6v8c-1 1 0 3 0 4l-3 16c0 5 0 10 1 15 0 2 1 4 1 6l-1 1c0-1 0-3-1-4l-1-6c-2-9-1-17 0-26 1-4 1-8 2-12v-9z" class="I"></path><defs><linearGradient id="BO" x1="333.448" y1="474.68" x2="367.363" y2="490.576" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#363637"></stop></linearGradient></defs><path fill="url(#BO)" d="M343 444h0v-3c1 4 0 9 0 13 0 1-1 2 0 3v-1c0-1 0-2 1-3h1v1-1h1v1 4c-1 3 0 7-1 10v1c1 0 1-1 1-1v-3c-1-2-1-5 0-7v-2h1l2-11c1 1 1 2 1 4-1 9-2 17 0 26l1 6c1 1 1 3 1 4 1 2 1 5 2 7l2 3h0c1 2 2 5 3 7h0l6 11v1l-3-3-2-4c-1 0-1 0-2 1l-2-2h-1c1 1 1 1 1 2v1h0l-1-1h-1v-1h-1c0 1 0 2 1 2v1 1 2h0 0l-1-1-1-3v2l-1-1c0 1-1 1 0 2v3s-1 0-2 1c0 1 1 2 1 3h0l-1-1v-1 2c0-1 0-1-1-1v-1s0-1-1-1v-3c0-3-1-6-2-9l-4-12c-2-3-2-7-3-11-2-6-4-11-5-17h1v-1c1 4 3 7 3 10h1c-1-1-1-2-1-3 2-3 2-7 4-9 0-1-1-3 0-3v-4-4c1-2 1-4 2-6z"></path><path d="M355 508c-1 0-1-1-1-2v-2c-1 0-1-1 0-1l1 2c0-1-1-2 0-3l1 1c-1 0-1 0-1 1s1 1 1 2h-1c1 1 1 1 1 2v1h0l-1-1zm-14-58c1-2 1-4 2-6 0 3-1 6-1 9l-1 13c-1 4-1 8-1 11 0 5 1 12 2 16 2 7 5 13 6 20h-1c0-3-1-6-2-9l-4-12c-2-3-2-7-3-11-2-6-4-11-5-17h1v-1c1 4 3 7 3 10h1c-1-1-1-2-1-3 2-3 2-7 4-9 0-1-1-3 0-3v-4-4z" class="K"></path><defs><linearGradient id="BP" x1="398.41" y1="551.441" x2="371.209" y2="523.674" xlink:href="#B"><stop offset="0" stop-color="#171718"></stop><stop offset="1" stop-color="#414141"></stop></linearGradient></defs><path fill="url(#BP)" d="M354 492l1-1 1 1h0c1 0 1 0 1 1 1 0 1 1 1 2l2 2v-1c0-1 0-2 1-3l1 1h2v1c1 0 2 1 2 2l1-1h1l1 2c1 2 3 5 5 7-1-1-1-2-1-2l3 3c1 1 2 2 3 4h0 0c1 0 1 0 1 1 1 2 3 4 3 7l1 1-1 3h3c1 0 1 1 1 1h2c0 1 0 1-1 1h0v1h1l1-1c1 1 3 5 4 7 1 3 2 7 3 10 0 4-1 8-1 12l-2-9c0 7 0 13-3 19l1-6h-1 0c0 2-2 5 0 7-1 1-1 2-1 3-1-2-2-5-2-7v-1c-1-1 0-3-1-4v-1-1l-1-1c-1-3-1-7-3-10 0 0-1-1-1-2-1-2-3-5-5-8h0c-3-1-4-6-5-8l1-1c0 1 1 1 1 2s2 3 3 3h0l-8-9s-1-1-1-2c-1-1-2-2-3-4l-6-11h0c-1-2-2-5-3-7h0l-2-3z"></path><path d="M359 502c4 2 4 7 7 9h0c1 1 2 1 2 2h0c2 0 3 2 3 2l2 2v-1c0-1-1-1-1-2h0l1 1 1 2c1 1 3 3 3 4h-1 0-1-1l4 5c2 3 5 6 6 9-2-1-4-4-6-6 0-1 0-1-1-1l-8-9s-1-1-1-2c-1-1-2-2-3-4l-6-11z" class="T"></path><path d="M371 515l2 2v-1c0-1-1-1-1-2h0l1 1 1 2v1c1 1 1 1 1 2h-1c-1-1-2-3-3-5z" class="F"></path><defs><linearGradient id="BQ" x1="358.251" y1="503.239" x2="374.707" y2="506.625" xlink:href="#B"><stop offset="0" stop-color="#474747"></stop><stop offset="1" stop-color="#747374"></stop></linearGradient></defs><path fill="url(#BQ)" d="M354 492l1-1 1 1h0c1 0 1 0 1 1 1 0 1 1 1 2l2 2v-1c0-1 0-2 1-3l1 1h2v1c1 0 2 1 2 2l1-1h1l1 2c1 2 3 5 5 7-1-1-1-2-1-2l3 3c1 1 2 2 3 4h0 0c1 0 1 0 1 1 1 2 3 4 3 7l1 1-1 3c-1 0-3-1-4 0h0c-1 0-1 0-2-1 0-1-2-3-3-4l-1-2-1-1h0c0 1 1 1 1 2v1l-2-2s-1-2-3-2h0c0-1-1-1-2-2h0c-3-2-3-7-7-9h0c-1-2-2-5-3-7h0l-2-3z"></path><path d="M354 492l1-1 1 1h0c1 0 1 0 1 1 1 0 1 1 1 2l8 16h0c-3-2-3-7-7-9h0c-1-2-2-5-3-7h0l-2-3z" class="K"></path><path d="M354 492l1-1 1 1h0l2 3-1 1c0-1 0-1-1-1h0 0l-2-3z" class="B"></path><defs><linearGradient id="BR" x1="370.074" y1="495.249" x2="372.082" y2="509.181" xlink:href="#B"><stop offset="0" stop-color="#404140"></stop><stop offset="1" stop-color="#787778"></stop></linearGradient></defs><path fill="url(#BR)" d="M364 495c1 0 2 1 2 2l1-1h1l1 2c1 2 3 5 5 7h0c1 1 2 3 3 4-2 0-3-1-5 0l-1 1h0v-1c-1 0-1 0-1-1h1v-1c1-3-5-9-7-12z"></path><path d="M374 505c-1-1-1-2-1-2l3 3c1 1 2 2 3 4h0 0c1 0 1 0 1 1 1 2 3 4 3 7l1 1-1 3c-1 0-3-1-4 0h0c-1 0-1 0-2-1 0-1-2-3-3-4l-1-2-1-1c0-1-1-2-2-3s-3-4-4-6l1 1v-1h0c2 1 2 2 3 3 0 1 0 1 1 1v1h0l1-1c2-1 3 0 5 0-1-1-2-3-3-4h0z" class="b"></path><defs><linearGradient id="BS" x1="379.037" y1="521.796" x2="377.082" y2="513.251" xlink:href="#B"><stop offset="0" stop-color="#505050"></stop><stop offset="1" stop-color="#747374"></stop></linearGradient></defs><path fill="url(#BS)" d="M373 515l1-1v-1h1c2 3 5 4 8 6v-1l1 1-1 3c-1 0-3-1-4 0h0c-1 0-1 0-2-1 0-1-2-3-3-4l-1-2z"></path><defs><linearGradient id="BT" x1="303.955" y1="373.951" x2="326.421" y2="354.36" xlink:href="#B"><stop offset="0" stop-color="#313131"></stop><stop offset="1" stop-color="#737272"></stop></linearGradient></defs><path fill="url(#BT)" d="M305 316c0-1 5-5 6-6l-3 8v6c0 1 0 2 1 3v3h0v-1l-1 1h0-1c0 3 1 5 1 7h-1v-1h-1v5 1 1h1v-1l1 1v-1c1 1 2 3 3 4l-1 1 4 4c1 2 4 5 6 6h1c-1 1-1 1-2 1-1-1-2-2-3-2s-2 0-3-1-1-2-2-3c0-1-1-1-2-1 0 1 1 3 2 4 1 3 3 6 5 9 1 0 2 1 2 2h0c-3-3-5-6-7-9 0 2 2 5 3 7 1 1 1 2 2 3l-3-2h0c0 2 1 3 2 4s1 2 2 3l7 8v1c1 1 1 1 1 2 0 2 1 3 2 5 0 1 1 1 1 2v1h0s0 1 1 1h0 0v3l5 12h-1c0 1 1 3 1 4s1 2 0 3c0 4 1 8 1 12h-1v-2-4c0-2-1-4-1-5v-1-1-1s0-1-1-2v-1-1c-1-2-1-2-1-4 0 3 0 7 1 9v3 12c0 1-1 2-1 2v1l-2 9h0c-1 1-1 2-1 3v1c-1 1-1 2-1 2s-1 0-1-1v-1l-1-1v1l-2-3v-1c-1-2-2-2-4-2h0v-1-2c0-1 0-1-1-1 0-4 2-7 1-10 0-2 0-4-1-6h0c0-1-1-5 0-6v-2c-1-5-2-9-4-14l-3-6-1-1v-4l-1-1h-1l-1 1h-1v-4c0-5 1-10 0-15 0 2 1 5 0 7v-1c0-1 0-1-1-2v-7c1-2 0-5 0-6h-1v-1-2-2c-1 2-1 3-1 5 0-2-1-4 0-6v-2c0-1-1-1-1-2v-1-3-4h-2 0 0c0-1-1-3 0-4v-4s1-1 1-2c-1 2-2 3-3 4 1-4 4-7 6-11l-1-1c0 1-1 2-2 3h0v-2l1-1c1-1 1-2 2-2 2-1 3-3 4-5l-3 2z"></path><defs><linearGradient id="BU" x1="329.224" y1="389.731" x2="328.59" y2="401.558" xlink:href="#B"><stop offset="0" stop-color="#515151"></stop><stop offset="1" stop-color="#706e6f"></stop></linearGradient></defs><path fill="url(#BU)" d="M334 414c-1-8-4-14-7-21-1-2-1-4-3-6l1-1 2 2c0 1 1 1 1 2v1h0s0 1 1 1h0 0v3l5 12h-1c0 1 1 3 1 4s1 2 0 3z"></path><defs><linearGradient id="BV" x1="312.513" y1="387.691" x2="331.093" y2="403.801" xlink:href="#B"><stop offset="0" stop-color="#2f2f30"></stop><stop offset="1" stop-color="#626162"></stop></linearGradient></defs><path fill="url(#BV)" d="M314 386c2 1 3 2 4 4v-1c-1-1-1-2-1-2-1-2-2-4-2-6h1c1 0 3 5 3 5s1 0 1 1c0 2 2 4 3 6 2 2 3 6 4 8l1-1h0v-1h0c1 3 2 5 2 8-3 0-2-1-5-2 0 1 0 2-1 3 0-1 0-2-1-3l-1 2v-3c0-1-2-5-2-6s-1-2-1-3c0-3-3-6-5-9z"></path><path d="M321 399h1s1 1 1 2l-1 1h0l-1-3z" class="F"></path><path d="M305 316c0-1 5-5 6-6l-3 8v6c0 1 0 2 1 3v3h0v-1l-1 1h0-1c0 3 1 5 1 7h-1v-1h-1v5 1c-1-2-1-5-2-7 0 0 0-1-1-2v-4-1h0v2 6c0 1-1 2-1 3h-2 0 0c0-1-1-3 0-4v-4s1-1 1-2c-1 2-2 3-3 4 1-4 4-7 6-11l-1-1c0 1-1 2-2 3h0v-2l1-1c1-1 1-2 2-2 2-1 3-3 4-5l-3 2z" class="L"></path><path d="M308 330c0-3-1-5-1-8h1v2c0 1 0 2 1 3v3h0v-1l-1 1h0z" class="I"></path><g class="U"><path d="M303 328v2 6c0 1-1 2-1 3h-2 0 0c0-1-1-3 0-4v-4l1 5c1-2 1-6 2-7v-1z"></path><path d="M303 329h0c1 1 1 3 2 5v-6l2 8h-1v5 1c-1-2-1-5-2-7 0 0 0-1-1-2v-4z"></path></g><path d="M307 365v-4h0c0 1 0 1 1 1 0 1 1 1 1 2l-1 1v2h0c2 1 3 3 4 5 2 3 3 6 6 9-1 0-1 0-2-1v1h-1c0 2 1 4 2 6 0 0 0 1 1 2v1c-1-2-2-3-4-4 2 3 5 6 5 9 0 1 1 2 1 3v1c-1 0-1 1-1 2v-1h-1c0 1 1 3 0 4l-1 1c1 1 2 3 1 5h0c-1-5-2-9-4-14l-3-6-1-1v-4l-1-1h-1l-1 1h-1v-4c0-5 1-10 0-15 0-1 1-1 1-1z" class="E"></path><path d="M310 389l1-1c1 1 1 1 1 2 0 2 2 4 3 6h-1l-3-6-1-1z" class="T"></path><path d="M315 396v2-1-1-1-1h0v-1h1v2c1 0 1 1 1 2l2-2c0 1 1 2 1 3v1c-1 0-1 1-1 2v-1h-1c0 1 1 3 0 4l-1 1c1 1 2 3 1 5h0c-1-5-2-9-4-14h1z" class="Q"></path><path d="M317 405c0-3-1-6-1-9 1 2 1 3 2 4 0 1 1 3 0 4l-1 1z" class="a"></path><path d="M306 366c0-1 1-1 1-1l3 20-1-1h-1l-1 1h-1v-4c0-5 1-10 0-15zm2 1c2 1 3 3 4 5 2 3 3 6 6 9-1 0-1 0-2-1v1h-1c0 2 1 4 2 6 0 0 0 1 1 2v1c-1-2-2-3-4-4-3-6-4-12-6-19z" class="B"></path><defs><linearGradient id="BW" x1="314.719" y1="432.809" x2="327.668" y2="419.433" xlink:href="#B"><stop offset="0" stop-color="#393939"></stop><stop offset="1" stop-color="#7c7b7b"></stop></linearGradient></defs><path fill="url(#BW)" d="M318 400h1v1c0-1 0-2 1-2v-1c0 1 2 5 2 6v3l1-2c1 1 1 2 1 3 1-1 1-2 1-3 3 1 2 2 5 2 1 7 1 14 1 21v3l-2 9h0c-1 1-1 2-1 3v1c-1 1-1 2-1 2s-1 0-1-1v-1l-1-1v1l-2-3v-1c-1-2-2-2-4-2h0v-1-2c0-1 0-1-1-1 0-4 2-7 1-10 0-2 0-4-1-6h0c0-1-1-5 0-6v-2h0c1-2 0-4-1-5l1-1c1-1 0-3 0-4z"></path><path d="M322 407l1-2c1 1 1 2 1 3 0 0 0 1 1 1v4c0 1 1 2 1 2-1 0-1-1-2-2l1 7v1h-1v5-6c0-5-1-9-2-13z" class="c"></path><path d="M318 400h1v1c0-1 0-2 1-2v-1c0 1 2 5 2 6v3c1 4 2 8 2 13l-1 3v-6h0s0 1-1 1v-2l-1 1c-1-1-1-3-1-4l-1-1h0c0-1 0-1-1-2 1-2 0-4-1-5l1-1c1-1 0-3 0-4z" class="X"></path><g class="b"><path d="M318 404l4 12-1 1c-1-1-1-3-1-4l-1-1h0c0-1 0-1-1-2 1-2 0-4-1-5l1-1z"></path><path d="M323 417c-2-4-2-9-2-13h1v3c1 4 2 8 2 13l-1 3v-6h0z"></path></g><defs><linearGradient id="BX" x1="321.736" y1="435.263" x2="330.86" y2="431.483" xlink:href="#B"><stop offset="0" stop-color="#535153"></stop><stop offset="1" stop-color="gray"></stop></linearGradient></defs><path fill="url(#BX)" d="M325 420l-1-7c1 1 1 2 2 2v2c0 2 0 4 1 5h0v1s0 1 1 2v3c1 2 1 3 1 5 1-1 0-3 1-4 0 0 0-1 1-1v3l-2 9h0c-1 1-1 2-1 3v1c-1 1-1 2-1 2s-1 0-1-1v-1l-1-1c0-7 1-15 0-23z"></path><defs><linearGradient id="BY" x1="321.013" y1="416.419" x2="333.616" y2="421.182" xlink:href="#B"><stop offset="0" stop-color="#6b6a6b"></stop><stop offset="1" stop-color="#878787"></stop></linearGradient></defs><path fill="url(#BY)" d="M324 408c1-1 1-2 1-3 3 1 2 2 5 2 1 7 1 14 1 21-1 0-1 1-1 1-1 1 0 3-1 4 0-2 0-3-1-5v-3c-1-1-1-2-1-2v-1h0c-1-1-1-3-1-5v-2s-1-1-1-2v-4c-1 0-1-1-1-1z"></path><path d="M325 409h0c1 2 0 4 1 6v2h0v-2s-1-1-1-2v-4z" class="O"></path><defs><linearGradient id="BZ" x1="352.788" y1="547.583" x2="373.774" y2="539.332" xlink:href="#B"><stop offset="0" stop-color="#060607"></stop><stop offset="1" stop-color="#626262"></stop></linearGradient></defs><path fill="url(#BZ)" d="M355 508l1 1h0v-1c0-1 0-1-1-2h1l2 2c1-1 1-1 2-1l2 4 3 3v-1c1 2 2 3 3 4 0 1 1 2 1 2l8 9h0c-1 0-3-2-3-3s-1-1-1-2l-1 1c1 2 2 7 5 8h0l5 8c0 1 1 2 1 2 2 3 2 7 3 10l1 1v1 1c1 1 0 3 1 4v1c-2 4-1 11-4 14-1 1-2 2-2 4-1 2-3 4-4 6v-1h-1v1h0c-1 0-2 1-2 1v1h0-2c0 1 0 2-1 3-1 2-2 3-3 5 0-2 0-3-1-4v-1l1-1v-3-2-4h-2c-1-5 0-9-1-13h-1v2 3h0v-2c-1 1-2 1-3 1l-1-3h0l-1 1v1 1l-1 1v2c-2-1-3-4-5-5-1-1-2-1-3-2s0-11 0-13v-2-1c0-1-1-2-1-2-1-5 0-10 0-15v-2h0l1-2h0l1-1c-1-3 0-7-2-9 0-1-1-2-1-3 1-1 2-1 2-1v-3c-1-1 0-1 0-2l1 1v-2l1 3 1 1h0 0v-2-1-1c-1 0-1-1-1-2h1v1h1z"></path><path d="M373 547c1 4 0 9 0 13 0 2 1 8 0 10l-1-1v1l-1-1v-4-1c0-2 0-4 1-5 0-1 0-2 1-3v-9z" class="S"></path><path d="M371 565h0c1-1 0-2 1-3 1 1 1 3 1 5l-1 1v1 1l-1-1v-4z" class="L"></path><defs><linearGradient id="Ba" x1="350.961" y1="512.991" x2="360.34" y2="520.328" xlink:href="#B"><stop offset="0" stop-color="#383837"></stop><stop offset="1" stop-color="#5e5d5e"></stop></linearGradient></defs><path fill="url(#Ba)" d="M355 508l1 1h0v-1c0-1 0-1-1-2h1l2 2v2c0 1 1 1 0 1l1 1h0v1h0c0 1 1 1 1 2h0v1c1 1 1 1 1 2 1 1 2 4 2 6h0c0-1-1-2-1-3 0 0 0-1-1-2 0 3 2 6 2 9-2-3-2-8-3-11-1 1-1 1-1 2 1 5 3 11 3 16 0 0-1 0-1-1v-2-2c-1-1 0-3-1-4v-3h-1v-1l-1 1c-1 2 0 2 0 4v1c1 1 1 3 1 5v1 2h0l-1-1v-3l-1 1h0v-2c-1-1 0-2 0-3-1-1-1-2-1-3l-1-2c-1-3-1-4-1-7-1-2-1-3-1-4l1 1h0 0v-2-1-1c-1 0-1-1-1-2h1v1h1z"></path><path d="M360 516c-2-1-3-5-3-7l1 1c0 1 1 1 0 1l1 1h0v1h0c0 1 1 1 1 2h0v1z" class="Q"></path><defs><linearGradient id="Bb" x1="350.164" y1="547.403" x2="362.181" y2="547.251" xlink:href="#B"><stop offset="0" stop-color="#696969"></stop><stop offset="1" stop-color="#838282"></stop></linearGradient></defs><path fill="url(#Bb)" d="M351 515v-3c-1-1 0-1 0-2l1 1v-2l1 3c0 1 0 2 1 4 0 3 0 4 1 7l1 2c0 1 0 2 1 3 0 1-1 2 0 3v2h0l1-1v3l1 1h0v-2-1c0-2 0-4-1-5v-1c0-2-1-2 0-4l1-1v1h1v3c1 1 0 3 1 4v2 2c0 1 1 1 1 1 2 8 1 17 1 24-1 2-1 5-2 8h0l-1 1v1 1l-1 1v2c-2-1-3-4-5-5-1-1-2-1-3-2s0-11 0-13v-2-1c0-1-1-2-1-2-1-5 0-10 0-15v-2h0l1-2h0l1-1c-1-3 0-7-2-9 0-1-1-2-1-3 1-1 2-1 2-1z"></path><defs><linearGradient id="Bc" x1="352.972" y1="515.37" x2="350.44" y2="537.973" xlink:href="#B"><stop offset="0" stop-color="#3d3d3c"></stop><stop offset="1" stop-color="#6e6c6e"></stop></linearGradient></defs><path fill="url(#Bc)" d="M351 515v-3c-1-1 0-1 0-2l1 1v-2l1 3c0 1 0 2 1 4 0 3 0 4 1 7l1 2h-1c-1 3 0 7-1 10v5 1c1 1 0 4 0 5v2c-1-1 0-8-1-8h0v8c-1-1 0-3 0-4h-1v5c0 1-1 3-1 4h0v-2-1c0-1-1-2-1-2-1-5 0-10 0-15v-2h0l1-2h0l1-1c-1-3 0-7-2-9 0-1-1-2-1-3 1-1 2-1 2-1z"></path><defs><linearGradient id="Bd" x1="349.876" y1="511.493" x2="355.851" y2="524.634" xlink:href="#B"><stop offset="0" stop-color="#3f3e3e"></stop><stop offset="1" stop-color="#605f61"></stop></linearGradient></defs><path fill="url(#Bd)" d="M351 515v-3c-1-1 0-1 0-2l1 1v-2l1 3c0 1 0 2 1 4 0 3 0 4 1 7l1 2h-1c-1 3 0 7-1 10 0-6 0-11-1-16-1-1-1-3-2-4z"></path><defs><linearGradient id="Be" x1="346.313" y1="532.36" x2="354.553" y2="548.057" xlink:href="#B"><stop offset="0" stop-color="#575556"></stop><stop offset="1" stop-color="#717372"></stop></linearGradient></defs><path fill="url(#Be)" d="M352 528c1 7-1 14 0 21 0 1-1 3-1 4h0v-2-1c0-1-1-2-1-2-1-5 0-10 0-15v-2h0l1-2h0l1-1z"></path><path d="M365 513c1 2 2 3 3 4 0 1 1 2 1 2l8 9h0c-1 0-3-2-3-3s-1-1-1-2l-1 1c1 2 2 7 5 8h0l5 8c0 1 1 2 1 2 2 3 2 7 3 10l1 1v1 1c1 1 0 3 1 4v1c-2 4-1 11-4 14-1 1-2 2-2 4-1 2-3 4-4 6v-1h-1v1h0c-1 0-2 1-2 1v1h0-2c0 1 0 2-1 3-1 2-2 3-3 5 0-2 0-3-1-4v-1l1-1v-3-2h0c1-1 1-1 1-2v-2c1-1 0-2 1-3 1 0 1-2 1-3l1-3c1-2 0-8 0-10 0-4 1-9 0-13-1-12-4-22-8-33v-1z" class="d"></path><path d="M376 542v-1c1 2 1 3 2 5 0 1-1 3 0 4v1c-1 1-1 2-1 4v8-10c0-4-1-7-1-11z" class="K"></path><path d="M369 583h0c1-1 1-1 1-2v-2c1-1 0-2 1-3 0 2 0 5 1 7 0 1 0 4-1 5h0c-1 1-1 3-2 4 1-1 2-2 2-3h1 0c-1 2-2 3-3 5 0-2 0-3-1-4v-1l1-1v-3-2z" class="S"></path><path d="M378 546c0 1 1 2 2 3h0 0c1 1 1 3 2 5v11c0 2-1 6-1 8 1 1 0 3 1 5-1 2-3 4-4 6v-1h-1v1h0c-1 0-2 1-2 1v1h0-2c0-1 0-3 1-4v-3s0-1 1-1l2-15v-8c0-2 0-3 1-4v-1c-1-1 0-3 0-4z" class="N"></path><path d="M381 573c1 1 0 3 1 5-1 2-3 4-4 6v-1h-1c2-3 4-7 4-10z" class="W"></path><path d="M378 546c0 1 1 2 2 3h0c0 3 1 8-1 11v2c0-4 0-7-1-11v-1c-1-1 0-3 0-4z" class="L"></path><path d="M377 563v-8c0-2 0-3 1-4 1 4 1 7 1 11-1 4-2 9-3 13 0 1 0 2-1 3h0 0l2-15z" class="g"></path><path d="M369 519l8 9h0c-1 0-3-2-3-3s-1-1-1-2l-1 1c1 2 2 7 5 8h0l5 8c0 1 1 2 1 2 2 3 2 7 3 10l1 1v1 1c1 1 0 3 1 4v1c-2 4-1 11-4 14-1 1-2 2-2 4-1-2 0-4-1-5 0-2 1-6 1-8v-11c-1-2-1-4-2-5h0 0c-1-1-2-2-2-3-1-2-1-3-2-5v1l-2-8c0-1-1-3-1-4 0-2-1-4-1-6-1-2-2-3-3-5z" class="I"></path><path d="M382 554v-3c2 4 2 12 1 16 0 1-1 5-1 6h1 0c1 0 0 0 1 1-1 1-2 2-2 4-1-2 0-4-1-5 0-2 1-6 1-8v-11z" class="C"></path><defs><linearGradient id="Bf" x1="297.118" y1="514.961" x2="359.762" y2="517.419" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#3f3f3f"></stop></linearGradient></defs><path fill="url(#Bf)" d="M331 431v-1s1-1 1-2v-12-3c-1-2-1-6-1-9 0 2 0 2 1 4v1 1c1 1 1 2 1 2v1 1 1c0 1 1 3 1 5v4 2h1c0 13-3 25-2 38 1 6 3 11 5 17 1 4 1 8 3 11l4 12c1 3 2 6 2 9v3c1 0 1 1 1 1v1c1 0 1 0 1 1v-2 1l1 1h0c2 2 1 6 2 9l-1 1h0l-1 2h0v2c0 5-1 10 0 15 0 0 1 1 1 2v1 2c0 2-1 12 0 13s2 1 3 2c2 1 3 4 5 5v-2l1-1v-1-1l1-1c0 4-2 8-3 12-1 3-2 5-3 7-1 3-2 6-4 9s-4 5-6 8c-1 2-3 5-5 6h-1c0 1-1 1-1 2-1 1-2 1-3 2 0 2-4 6-6 7h0 0 1 0c0 1 0 1-1 1h-1c-1 0-1 1-2 1 0 1 0 1-1 1h0c-2 2-5 3-8 4h-1l-9 3c1-2 5-3 7-5h-2-3-1c0-1-1-1-1-1h-2c-1 0-1 1-2 1h-1c-1 1-2 1-3 1-1 1-2 0-2 1h-3-1v-1c1 0 5-3 6-3v1c-1 0-1 0 0 1 2-1 7-2 8-5h1l2-2c2-1 2-2 4-3 4-4 7-7 10-12h-1-3l5-8-1-1 1-6c1-1 1-1 1-2v-1c1-1 1-2 1-2 0-1 0-2 1-3v-4l-1 1c0 1 0 2-1 3h0l2-16c2-8 2-16 2-25v-4l-1-1v-4l1-9c1-4 1-8 1-12h-1v1c-1 2-1 3-2 4h-1c1-1 1-2 1-3-1 1-1 1-1 2l-1 1v4c-2-3 0-8-1-11-1-1 0-2 0-3v-3-7h-1v-8h0c-2 4 0 10-2 14v2 1 4c-1 1-1 1-1 2v1c0-3 1-7 0-10-1-1-1-7 0-8 0-1-1-1-1-1 0-3 1-6 0-8l1-2v-1-4c-1-2 0-5-1-7v-2c-1-2 0-3-1-4v-2c0-1-1-4-1-4-1-1-1-2-1-3 0 1 0 2-1 2-1-1-3-4-4-6h0c1-2 1-4 3-4v-2c1 0 1 1 1 1 0 1 1 2 1 2l1 1v-1-7-6c1 0 1 0 1 1v2 1h0c2 0 3 0 4 2v1l2 3v-1l1 1v1c0 1 1 1 1 1s0-1 1-2v-1c0-1 0-2 1-3h0l2-9z"></path><path d="M348 518c1 0 1 0 1 1v-2 1l1 1h0c2 2 1 6 2 9l-1 1h0l-1 2h0v2l-1 1v-1-1-3c-1-1 0-3 0-4-1-2-1-5-1-7z" class="V"></path><path d="M349 525v-5c1 2 1 4 1 6l-1-1z" class="E"></path><path d="M349 525l1 1 1 3-1 2h0l-1-6z" class="F"></path><path d="M349 534l1-1c0 5-1 10 0 15-1 6-2 14-1 21h-1c0-1-1-4 0-5v-1c0-1-1-2-1-2-1-3-1-6-1-9v-4l1-1v-2l1-1c0-1 0-2 1-2 1-1 0-7 0-8z" class="Q"></path><defs><linearGradient id="Bg" x1="347.902" y1="569.861" x2="358.197" y2="560.43" xlink:href="#B"><stop offset="0" stop-color="#525252"></stop><stop offset="1" stop-color="#6a6a69"></stop></linearGradient></defs><path fill="url(#Bg)" d="M350 548s1 1 1 2v1 2c0 2-1 12 0 13s2 1 3 2c2 1 3 4 5 5v-2l1-1v-1-1l1-1c0 4-2 8-3 12h-1l1-1h0c-1-1-2-2-3-2-1-1-2-1-3-2s-2-2-4-3v-2h1c-1-7 0-15 1-21z"></path><defs><linearGradient id="Bh" x1="328.457" y1="540.583" x2="341.102" y2="543.393" xlink:href="#B"><stop offset="0" stop-color="#555454"></stop><stop offset="1" stop-color="#8a898a"></stop></linearGradient></defs><path fill="url(#Bh)" d="M334 520v-7c0-2-1-4 0-5 1 1 0 3 0 5 1-1 2-2 3-2 2 22 3 46-3 68l-1-1c0-2 2-5 1-8v1-1-5-3l1-7c1-9 0-18 0-27 0-2 0-5-1-8z"></path><path d="M334 520c1 3 1 6 1 8 0 9 1 18 0 27l-1 7v3 5 1-1c1 3-1 6-1 8l1 1c-1 4-3 9-4 13-2 4-4 8-6 11h-1-3l5-8-1-1 1-6c1-1 1-1 1-2v-1c1-1 1-2 1-2 0-1 0-2 1-3v-4s0-1 1-1v-3c1-3 1-7 2-10v-5-1l1-3c1-3 1-7 1-11h1v-22z" class="Q"></path><defs><linearGradient id="Bi" x1="319.575" y1="588.464" x2="334.901" y2="586.943" xlink:href="#B"><stop offset="0" stop-color="#525150"></stop><stop offset="1" stop-color="#7d7c7e"></stop></linearGradient></defs><path fill="url(#Bi)" d="M334 565v5 1-1c1 3-1 6-1 8l1 1c-1 4-3 9-4 13-2 4-4 8-6 11h-1-3l5-8c1-2 2-5 3-7l6-23z"></path><defs><linearGradient id="Bj" x1="312.654" y1="508.231" x2="341.931" y2="515.052" xlink:href="#B"><stop offset="0" stop-color="#282828"></stop><stop offset="1" stop-color="#525252"></stop></linearGradient></defs><path fill="url(#Bj)" d="M318 434c1 0 1 0 1 1v2 1h0c2 0 3 0 4 2v1l2 3v-1l1 1v1c0 1 1 1 1 1s0-1 1-2v-1c0-1 0-2 1-3h0c0 3-1 6-1 8 0 5 0 9 1 14 0 6 2 13 3 19l4 19c0 4 1 7 1 11-1 0-2 1-3 2 0-2 1-4 0-5-1 1 0 3 0 5v7 22h-1c0 4 0 8-1 11l-1 3v1 5c-1 3-1 7-2 10v3c-1 0-1 1-1 1l-1 1c0 1 0 2-1 3h0l2-16c2-8 2-16 2-25v-4l-1-1v-4l1-9c1-4 1-8 1-12h-1v1c-1 2-1 3-2 4h-1c1-1 1-2 1-3-1 1-1 1-1 2l-1 1v4c-2-3 0-8-1-11-1-1 0-2 0-3v-3-7h-1v-8h0c-2 4 0 10-2 14v2 1 4c-1 1-1 1-1 2v1c0-3 1-7 0-10-1-1-1-7 0-8 0-1-1-1-1-1 0-3 1-6 0-8l1-2v-1-4c-1-2 0-5-1-7v-2c-1-2 0-3-1-4v-2c0-1-1-4-1-4-1-1-1-2-1-3 0 1 0 2-1 2-1-1-3-4-4-6h0c1-2 1-4 3-4v-2c1 0 1 1 1 1 0 1 1 2 1 2l1 1v-1-7-6z"></path><defs><linearGradient id="Bk" x1="324.078" y1="451.632" x2="327.124" y2="446.141" xlink:href="#B"><stop offset="0" stop-color="#333233"></stop><stop offset="1" stop-color="#4d4d4d"></stop></linearGradient></defs><path fill="url(#Bk)" d="M325 443l1 1v1c0 1 1 1 1 1s0-1 1-2v-1c0-1 0-2 1-3h0c0 3-1 6-1 8-1 2-1 4-2 5h-1v-2-3c0-2 0-4-1-5s-1-2-1-2l2 3v-1z"></path><path d="M323 441l2 3v7-3c0-2 0-4-1-5s-1-2-1-2z" class="C"></path><path d="M318 434c1 0 1 0 1 1v2 1h0c2 0 3 0 4 2v1s0 1 1 2 1 3 1 5c-1 2-1 5 0 7 2 4 2 8 3 11v4c1 1 1 3 1 4v14h-1v-5c-1 2 0 4 0 6h0l1 4-2 1v-12h0c0 5 0 11-1 16-1-5-1-9-1-14-1 1-1 6 0 7h0v3h-1v-8h0c-2 4 0 10-2 14v2 1 4c-1 1-1 1-1 2v1c0-3 1-7 0-10-1-1-1-7 0-8 0-1-1-1-1-1 0-3 1-6 0-8l1-2v-1-4c-1-2 0-5-1-7v-2c-1-2 0-3-1-4v-2c0-1-1-4-1-4-1-1-1-2-1-3 0 1 0 2-1 2-1-1-3-4-4-6h0c1-2 1-4 3-4v-2c1 0 1 1 1 1 0 1 1 2 1 2l1 1v-1-7-6z" class="N"></path><g class="C"><path d="M319 438c2 0 3 0 4 2v1h0c-1-1-1-1-3-2 0 1 0 2 1 3v2l-1-1v-1-2s-1-1-1-2zm1 45l1-2c1 6 0 13 0 19-1-1-1-7 0-8 0-1-1-1-1-1 0-3 1-6 0-8z"></path><path d="M318 434c1 0 1 0 1 1v2 1-1 15l-1-4v-1-7-6z"></path></g><path d="M103 222c2 0 2 0 4 2-1 1-4 3-4 5l-6 11h1 1c-1 1-2 3-2 4l-2 4c0 1-2 3-1 4 0 2-2 6-1 7v-1c1-3 3-6 4-9h1 0c1-1 1-1 1-2h1v1c-1 0-1 0-1 1 0 2-1 4-2 6-1 4-2 9-3 13-1 10-1 20 1 30 2 14 5 27 11 40h0l2 4v2h0c1 1 1 1 1 2l1 1 1 1v1l1 1v1l1 1 1 2v1h1c0 1 0 1 1 2h0v1 1c-1 0-1 0-1 1s0 1-1 2h-1v1c-1-1-2-2-2-3v-1c0-1-2-5-4-6h-1v-1h0l-1-1v-1h0 0c-1-1-1-3-2-3h-1c4 7 8 13 12 19a57.31 57.31 0 0 1 11 11v2l6 8h0c2 4 4 7 6 10 3 6 5 12 7 18 2 5 3 10 3 15v2c1 2 1 4 1 6 0 3 0 7-1 10 0 4-1 7-3 10l2 1c2-3 5-5 8-6 5-3 9-3 13-3 2 1 3 1 4 2 1 0 3 1 4 2l2 1h0c1 0 3 4 3 5l1 1v2c1 4 0 7-1 11l-1 1c0 5 1 12 2 17 2 16 4 33 10 48 2 6 5 12 8 17 2 1 3 1 4 1 1 1 2 1 4 1h0v-1c1 0 2 0 3-1h4l1 1h3c7-1 13-2 19-3 1-1 3-1 4-1l1 1h1l5-1c3-1 6-1 8-1 5-1 10-3 15-5 1 0 3-1 5-1 1-1 3-2 4-2l10-5 1-1c1 0 2-1 3-2 0 0 1 0 1-1h1 0l-1 2h0l1-1h1c2-2 4-5 7-7l3-6h1l-4 6h1c1 0 1-1 1-2l1-1h0c1-1 1-1 1-2h0c1-1 2-3 3-4h0c0 1-1 2-1 3s-1 3-1 4c-4 7-9 13-14 19l-1 2 2-1h1c2-2 4-4 6-7v2h1s1-1 1-2l1 1c-1 2-3 4-5 7-1 1-3 2-3 3-1 2-3 2-4 3h2c-1 3-4 5-6 6-1 2-2 3-4 5s-4 3-6 5c-1 1-3 2-3 3h1c-3 3-6 7-9 8l-1 1c-2 2-5 4-7 5l-2-1c-5 4-11 8-17 9h-3c-1-1-3-1-4-1-2-1-3 0-4-1-7-1-13-1-20-1h2-7-4c-2 0-4-1-6-2l-13 1c-1-1-2-1-3-1h-7-5c-6-1-14-1-19-4l-15-16c-9-9-17-20-24-31-21-36-31-77-35-118-3-34-3-68 0-103 2-24 5-47 11-70 0-1 1-1 1-1 1-1 1-3 2-4l1-5c1-2 1-4 2-6 2-3 4-5 6-7 2-1 4-2 5-4h0z" class="d"></path><path d="M111 393h0c-1-1 0-7 1-7h0v8h0l-1-1z" class="B"></path><path d="M154 471c0-1 0-2 1-3l1 2c-2 1 0 5-1 7v3c-1-3-1-6-1-9zm-53-95c-2-3-2-6-2-9 1-1 0-3 1-4 0-1 0-2 1-3h0v2c-1 1 0 2-1 4v3h1v7z" class="S"></path><path d="M127 542c2 1 4 4 6 6l-1 1c1 1 1 1 1 2-1 0 0-1-1 0l-5-9z" class="J"></path><path d="M117 449l1-2c1 0 1 1 2 1-1 2-1 4-1 5 0-1 0-2-1-2-2 1-3 5-5 7v-1c1-3 3-6 4-8z" class="E"></path><path d="M195 588c5 0 10 1 16 0 1 0 2 0 2 1l-5 1c-3 0-5 1-8 1 1-1 2-1 4-1l2-1h-1v1l-1-1h-3-1c-1-1-4 0-5-1z" class="Y"></path><path d="M101 432c-2 0-4 3-4 4 0-2 1-3 2-5 0-1 0-1 1-2v-1c0-1 1-2 1-3 1-1 1-1 1-2l1-1c0-1 0-2 1-3 0 2-1 3-1 4v3c0 2-2 4-2 6h0z" class="I"></path><path d="M206 568s1 0 1 1h0c0 1 0 1 1 1h0l1 1c-2-1-4-1-5-2-6-2-12-4-17-7 2 0 5 2 7 2 2 1 4 2 6 2 1 1 4 2 6 2z" class="L"></path><path d="M198 559c-3-1-7-3-10-5 2 1 8 4 11 3h0c2 1 3 1 4 1 1 1 2 1 4 1h0c1 0 2 1 4 1v1c-4-1-9-2-13-2zm2 32c3 0 5-1 8-1 0 1 0 1-1 2l1 1h1 2 2-7-4c-2 0-4-1-6-2h4z" class="O"></path><path d="M111 489c0-5 2-10 3-15v-4c1-1 1-1 1-2s0-2 1-3h1v2l-5 24-1-2z" class="B"></path><path d="M98 390h0v7h1v-5c1 1 0 2 1 3 0 1-1 2-1 3s1 4 1 5c0 2-1 4-1 5h0l-1-1h0v1h0v-5c-1-3-1-6-1-8s0-4 1-5z" class="S"></path><path d="M101 369v2c0-1 0-1 1-2 1 1 0 3 1 4s0 2 0 3c1 1 1 2 1 3l1 1v1c1 1 1 1 1 2h-1v1 1c0 1 1 1 1 2v-3h0l1 6h0l-6-14v-7z" class="B"></path><path d="M89 329h0v5c0 5 0 11-2 17h-1v-3-1c0-1 0-2 1-3v-1c0-2 0-3 1-4 0-4 0-7 1-10z" class="L"></path><path d="M131 445c0 1 1 2 1 2v1c1 1 1 2 1 3 1 1 1 3 1 4 0 2-1 4-1 6h1v3l-1-2v1l-2-5v-3-4c1-3 0-3 0-5v-1z" class="N"></path><path d="M114 420c2-2 2-7 4-9h0l2 6h0c-1-1-1-2-1-2-1-1-1-1-1 0l-2 4h-1c0 1 0 2-1 2 0 1-1 2-1 3h0c0 1 0 1-1 2v2h0l-1 3-1 1v1c-1 1 0 2-1 2 0 1-1 2-1 3-1 1 0 2-1 2v4s-1 1-1 2-1 4-2 5c0-1 0-2 1-3 0-1 0-2 1-3v-2-2l3-8 5-13z" class="B"></path><path d="M175 565l2-1 20 7 5 1c-2 1-6 0-9 0l-3-1 1-1-16-5z" class="F"></path><path d="M113 458s-1 1-1 2c-2 2-4 5-5 7 0-2 0-3 1-4s1-3 2-5 3-4 4-7c0-1 0-2 1-3v1h0v1l2-1h0c-1 2-3 5-4 8v1z" class="C"></path><path d="M96 385h1c1 1 1 1 1 2v1c-1 0-1 2-1 3s-1 2-1 2v1 1l-1 1v2l-3 7-1 1v-1l3-13c1-3 1-5 2-7zm42 183c1 0 6 4 7 5 5 4 11 6 17 9 2 1 5 2 7 4h-1c-3 0-5-1-7-2-8-4-18-9-23-16z" class="N"></path><path d="M123 416l-1-2c-1-2 0-3-1-5-1-1-1-2-2-3v-1h0c1 1 1 2 2 3h1v2c1 0 2 2 3 3l3 6 2 2 1 2 3 5v1h-1v1 1c-1-1-1-2-2-4-1 0-1-1-2-2-1-2-2-5-3-7-1-1-2-2-3-2z" class="K"></path><path d="M117 431c0-1 0-2 1-3h-1v-3-1l1 1v2-2h1v-6h0c0 1 0 4 1 5h-1c0 1 1 2 1 3h0 0c-1-2 0-3 0-5h1c1 1 1 3 2 4 1 2 2 3 2 5l1 2c0 1 1 1 1 2v1c1 0 1 1 1 2 0 0 1 1 1 2 1 2 2 3 2 5v1c0 2 1 2 0 5v4c-1-2-1-3-2-5 0-1 1-2 1-3-1-2-1-4-2-6 0-1 0-3-1-4-1-3-2-5-3-7 0-1-1-2-1-3-1-1-1-2-2-2v-1 2 2l-1 1c-1-1-1-2-1-3h0-1v4l-1 1h0z" class="I"></path><path d="M132 463c-1-2-2-4-1-5l2 5c0 3 2 8 1 11l1 2v1c-1 4-2 8-2 12-1 4 0 9 0 13-1-2-1-13-1-15v-1-1-5h0v-9c-1-2 0-4-1-4v-4h1z" class="L"></path><path d="M132 463c-1-2-2-4-1-5l2 5c0 3 2 8 1 11 0 2 0 4-1 6 0-6 0-11-1-17z" class="E"></path><path d="M199 563l12 6c5 3 12 4 18 4 2 0 5 0 7 1s5 0 7 0c-4 1-9 1-13 1h-5c-1-1-8-1-10-1l-13-2-5-1 3-1c2 1 5 1 6 2h3 3 0c1 0 2-1 2 0h2c0 1 0 1 1 1h5-4c-2-1-3-2-5-3h-1l-1-1h-1-1s0-1-1-1c0 0-1-1-2 0-2 0-5-1-6-2h0 3l1 1c0-1-2-1-2-2h-1l-1-1-1-1h0zm-84-182s1 1 1 2h0v2c0 1 1 2 1 4 0 1 0 1 1 2l-1 1v-1 1h0v3c1 1 2 3 3 4 2 6 6 12 10 18l3 5c1 3 2 5 4 7l-1 1s0-1-1-2v-1l-1-1v-1s-1-1-1-2h0c-1-1-1-2-2-3s-1-2-2-3c0 0-1-1-1-2l-2-3-4-6-1-3c-1-1-1-3-2-4l-1-1h0v-1h0c0-1-1-2-1-2-1 0 0 0 0 0-1 1-1 2-1 3l-1 2c0-2 1-5 1-7v-1c-1 1-1 1-1 2 0 0-1-1 0-2h0 0c-1 1-2 4-3 6v-1h0-1l1-3h0 1c0-1 0-2 1-3v-1c2-3-1-7 1-9z" class="I"></path><path d="M96 372h2c-1 2 0 3 0 4 2 1 3 6 3 8l2 3 3 6h-1c-2-2-3-6-5-8v-2c-1-1-2-5-3-7l-5 14-2 5s0-1 1-2v-1-2 1l-1 1c0 1 0 2-1 3 0-1 1-2 1-3 1-4 3-7 3-11 0-1 1-1 1-2h-1l-1 3-1 1v-1c-1 1-1 1-1 2h-1c2-3 4-8 7-10h0v-2z" class="N"></path><path d="M154 471c0 3 0 6 1 9h0c0 4 1 8 0 12l-1-9v1c-1 1 0 2 0 3v8c1 1 1 2 0 3 0 2 3 6 3 8-1-1-1-2-2-3v-1c0-1-1-2-1-2h0c0-1 0-2-1-2 0-1 0-2-1-3 0-2-1-4-1-7v-5c-1-1-1-3-1-4h1c0-2 1-3 1-5l1-1c0-1 0-1 1-2z" class="Y"></path><path d="M118 430v2c-1 5-3 9-6 13 0 1-1 2-1 3l-5 9c0-2 0-4 1-5v-1c1-1 1-1 1-2l1-1c-1 0-1 1-2 1 0-1 1-3 1-3l1-1h0v-1-1c1-1 1-2 2-3v-1c1-1 2-2 2-3v-2c1-1 1-1 1-2 0 0 0-1 1-1 1-1 1 0 1 0h1 0l1-1z" class="J"></path><path d="M118 430v2c-1 5-3 9-6 13 0-3 3-5 4-8v-1l1-5 1-1z" class="T"></path><path d="M155 480l1 3v1 2-1-14c1 1 1 3 1 4v9l1 13c0 3 1 5 1 8 1 2 1 4 1 6v-1h-1c-1-1-2-3-2-4 0-2-3-6-3-8 1-1 1-2 0-3v-8c0-1-1-2 0-3v-1l1 9c1-4 0-8 0-12z" class="B"></path><path d="M152 462s1 0 1-1c3-1 6-2 9-2-4 1-6 2-8 5-5 7-8 16-9 24v3 1c-1 2 0 6-1 7v-3h-1v2c0 1-1 3 0 3v1c0 1 1 3 0 4h0c0-2-1-3-1-5 0-8 1-16 3-23 1-2 1-4 2-7 1-1 2-3 3-4 1-2 2-3 2-5z" class="I"></path><path d="M117 449c0-1 1-2 1-3h1v1c1 0 1 1 1 1v-1-6c-1 0-1-2-1-2l1-1v-1h0v1c0 2 0 5 1 7v3c0 3 1 4 1 6 0 1 0 0 1 1 0 0 0 1 1 2v1 2h0c0 1 0 1 1 1v2 3c1 2 1 5 2 7 1 1 0 3 1 4h0c-1 1 0 2-1 2h0 0c0 1-1 1-1 1-1-1 0-3 0-4-1-1-1-3-1-4s0-1-1-1c0-1 0-2-1-3v-1h0c0-1 0-1-1-1v-2s0-1-1-1v-1-1c-1-1-1-1-1-2 0-2-1-5-1-6s0-3 1-5c-1 0-1-1-2-1l-1 2h0z" class="S"></path><path d="M120 448c2 7 5 15 5 22l-5-11c0-2-1-5-1-6s0-3 1-5z" class="T"></path><path d="M156 558c2-1 6 0 8 2l13 4-2 1 16 5-1 1 3 1h-2-3l-8-2c-2-1-5-1-7-1-1-1-2-1-2-2l-1-1h1l-2-2h-1 0c-1-1-3-2-5-2-2-1-5-3-7-4z" class="B"></path><path d="M164 560l13 4-2 1h0-2 0l-1-1h-2l-6-3v-1z" class="C"></path><path d="M173 569c-1-1-2-1-2-2l-1-1h1l-2-2h-1 0l22 7 3 1h-2-3l-8-2c-2-1-5-1-7-1z" class="b"></path><defs><linearGradient id="Bl" x1="126.761" y1="391.742" x2="124.465" y2="420.11" xlink:href="#B"><stop offset="0" stop-color="#2b2a2b"></stop><stop offset="1" stop-color="#606161"></stop></linearGradient></defs><path fill="url(#Bl)" d="M117 389c4 4 7 9 9 14v1c3 5 8 12 8 18h-1l-3-5c-4-6-8-12-10-18-1-1-2-3-3-4v-3h0v-1 1l1-1c-1-1-1-1-1-2z"></path><path d="M120 399v1c2 3 4 6 6 8 1 2 3 3 3 5 0 1 1 1 1 2v2c-4-6-8-12-10-18z" class="X"></path><defs><linearGradient id="Bm" x1="86.083" y1="247.638" x2="99.266" y2="261.443" xlink:href="#B"><stop offset="0" stop-color="#1a1a1a"></stop><stop offset="1" stop-color="#515153"></stop></linearGradient></defs><path fill="url(#Bm)" d="M98 249c-1 3-3 6-4 9-2 6-4 12-5 18-1-7 0-13 1-19 0-3 1-7 2-10 0-2 1-4 2-6 1-1 1-2 2-2v-1c1 0 3-3 3-4 1-1 1-2 2-3l1-1 1-1-6 11h1 1c-1 1-2 3-2 4l-2 4c0 1-2 3-1 4 0 2-2 6-1 7v-1c1-3 3-6 4-9h1z"></path><path d="M118 430v-4h1 0c0 1 0 2 1 3l1-1v-2-2 1c1 0 1 1 2 2 0 1 1 2 1 3 1 2 2 4 3 7 1 1 1 3 1 4 1 2 1 4 2 6 0 1-1 2-1 3 1 2 1 3 2 5v3c-1 1 0 3 1 5h-1c-1-1-1-2-1-3v-2h0v-1c0-1-1-1-1-2l-1-2h-1c1 1 1 1 1 2 1 1 0 2 0 3 1 1 0 1 1 2 0 1 0 3 1 4v1l1 2v1 2 1c1 0 1 2 0 3 0-4-1-7-2-10 0-2-2-4-2-6v-1-2c0-1 0-2-1-3 0-1 1-1 1-1l-1-1h1l1 1h0c0-1-2-6-3-8l-4-7c0-1-1-3-1-3-1 0-1 0-2-1v-2z" class="J"></path><path d="M106 441c0 2-1 3-2 4h-1v1h-1c0-1 1-3 1-4 1-2 1-3 1-5v-1c1-2 1-3 2-5 0-1 1-2 1-4l2-5c1-3 3-5 4-8 1-2 1-5 3-7 0-1 1-3 1-4v1 2h1 0c0 1 0 1 1 2 0 1 0 2 1 3 0 1 0 2 1 2v3c-1-2-2-4-2-5h-1 0c-2 2-2 7-4 9l-5 13-3 8z" class="C"></path><path d="M114 420v-1-1h0c0-2 1-4 2-6 0-1 0-3 1-4v-2h1c0 1 0 1 1 2 0 1 0 2 1 3 0 1 0 2 1 2v3c-1-2-2-4-2-5h-1 0c-2 2-2 7-4 9z" class="J"></path><path d="M91 342l1-6v3l1-1v1h0v1-1h1c0 1 1 2 1 2v-2h0v-1-2c2 7 1 13 0 20 0 2-1 5-1 7 0 1 0 1-1 2-1 2-2 4-4 6 0-2 1-4 1-7h0c0-1 1-6 1-7 0 1-1 1-1 2v-3l-1 1v5-3c0-3 0-6 1-9h0l1-8z" class="L"></path><path d="M93 358h0v3l-1-1c0-1 0-1 1-2z" class="I"></path><path d="M91 342h1c0 2 0 4-1 6v2 2l-1 1h0v-3l1-8z" class="K"></path><path d="M111 489v2c0 1 0 2-1 3v1 1h-1c1-2 2-6 1-8 1 0 0-1 1-2 0-3 2-5 1-8 0 1 0 1-1 2v-1c-1 1 0 3-1 4 0-2 1-4 1-7h-1l-3 11c0-3 1-6 1-9 0-2 1-3 1-5-1 1-3 4-4 5l3-8 6-9 3-3c0 1 0 1 1 2v2c0 1 1 1 1 2v3c0 1 0 1 1 2h0v1h0c0 1 0 4 1 5v2c1 1 1 5 0 6h0c0-1-1-3-1-4v7c-1-1 0-6-1-8v-4c-1-1 0-2-1-3 0-1-1-3-1-4v-2h-1c-1 1-1 2-1 3s0 1-1 2v4c-1 5-3 10-3 15z" class="N"></path><path d="M112 394l-1 3h1 0v1c1-2 2-5 3-6h0 0c-1 1 0 2 0 2 0-1 0-1 1-2v1c0 2-1 5-1 7-1 1-1 2-2 3l-1 5c-1 2-3 7-2 9h-1v1c0 1-1 2-1 3-1 1-1 4-2 6v2 1c-1 0-1 0-1 1v2s0 1-1 2c-1 2-1 5-2 7v5c-1 0-1 1-1 2s-1 2-1 3c0 3-1 6-1 10-1-1-1-2-1-4 1 0 1-2 1-3s0-2 1-2v-1-1c0-2 0-4 1-5v-3l1-1v-4l1-1v-2c1-1 1-2 1-3 1-1 0-2 1-3v-2-1h-1l1-2h0-1v-2c1 0 1-1 0-1v2c-1 1 0 3-1 4v1c0 1-1 2-1 3l-1 1v2h0c0 1 0 1-1 2v3l-1 1v4c-1 0 0 1-1 2s0 4-1 5c0 2 1 4 0 5v5c-1-1 0-3 0-4 0-8 1-17 4-25h0c0-2 2-4 2-6v-3c0-1 1-2 1-4l7-26 1 1z" class="K"></path><defs><linearGradient id="Bn" x1="173.142" y1="480.537" x2="168.161" y2="467.948" xlink:href="#B"><stop offset="0" stop-color="#403f41"></stop><stop offset="1" stop-color="#858584"></stop></linearGradient></defs><path fill="url(#Bn)" d="M167 458v-1l-1-1v-1c1 0 2-1 3-1h1c-1 0-1 0-2-1h1c1 1 3 0 4 0 0 1 1 1 2 1l2 1h0c1 0 3 4 3 5l1 1v2c1 4 0 7-1 11l-1 1v-1c-3 5-7 9-13 10-2 1-4 1-6 1 2-1 5-2 7-4 4-5 4-9 4-15 0-3-2-6-4-7v-1z"></path><path d="M180 462v-1h1v2 2c0 1-1 1-1 1h-1c0 1 0 1-1 2h0v-1s1-1 1-2h0 0 1v-3z" class="b"></path><path d="M171 466h0c0 2 0 4 1 6 1 1 1 3 0 4 0 1-2 4-3 4s-1 1-1 1h-1c4-5 4-9 4-15z" class="C"></path><path d="M167 458v-1l-1-1v-1c1 0 2-1 3-1h1c-1 0-1 0-2-1h1c1 1 3 0 4 0 0 1 1 1 2 1l2 1h0c1 0 3 4 3 5l1 1h-1v1 2l-1-1v1c-1 0-1 0-1-1v1c0 1 0 2-1 2h-2-4 0c0-3-2-6-4-7v-1z" class="e"></path><defs><linearGradient id="Bo" x1="142.302" y1="439.706" x2="123.431" y2="440.608" xlink:href="#B"><stop offset="0" stop-color="#1e1e1e"></stop><stop offset="1" stop-color="#515152"></stop></linearGradient></defs><path fill="url(#Bo)" d="M118 411h1c0 1 1 3 2 5h1 1c1 0 2 1 3 2 1 2 2 5 3 7 1 1 1 2 2 2 1 2 1 3 2 4v-1-1h1v-1h0c1 1 1 2 2 3s1 3 2 4c0 1 0 1 1 2l-3-7 1-1 3 11c1 3 2 6 2 10l1 6v2c0 1 0 1 1 1v-1l2 1-6 7c-2-2-2-4-2-6-1-5-2-10-4-15-2-7-5-13-9-19-1-3-3-6-5-9l-2-6z"></path><defs><linearGradient id="Bp" x1="165.752" y1="532.019" x2="156.621" y2="537.631" xlink:href="#B"><stop offset="0" stop-color="#0e0f0e"></stop><stop offset="1" stop-color="#3a393a"></stop></linearGradient></defs><path fill="url(#Bp)" d="M143 506l2 5v-1h1l1 1h0v3c1 0 1 3 2 4 0 0 0 1 1 2l1 3h1 1 0c-1-1-1-2-1-3l-3-6h0 0-1c-1-1-1-2-1-3v-1h0 0l1 1c0-1 0-3-1-4 0-1 1-3 0-4v-3l-1-1h0v-2-2-1c0 1 0 4 1 5h0v2c1 1 1 3 2 4 0 1 1 3 1 4l3 9c7 15 15 26 26 37 2 1 4 4 6 5v1h0 0c-3-2-6-3-9-5-4-2-9-5-13-9 0 0-2-1-3-2h1c-1-2-4-5-5-7s-3-4-4-7h0c-1-3-4-6-5-10-1-2-2-5-2-7l-1-1-1-7h0z"></path><path d="M145 514v-1l1 1v-3h0l2 5v1l-1 1h0c1 1 1 2 1 3h-1c-1-2-2-5-2-7z" class="L"></path><defs><linearGradient id="Bq" x1="149.825" y1="523.336" x2="164.876" y2="583.475" xlink:href="#B"><stop offset="0" stop-color="#595858"></stop><stop offset="1" stop-color="#807f7f"></stop></linearGradient></defs><path fill="url(#Bq)" d="M127 542c-2-4-5-9-6-13 0-2-1-4-1-6 1 1 1 4 3 6v-3h1v1c1 9 12 18 19 23l13 8c2 1 5 3 7 4 2 0 4 1 5 2h0 1l2 2h-1l1 1c0 1 1 1 2 2 2 0 5 0 7 1h-3l1 1-1 1h2v1l8 3h1s1 0 2 1h2-5c-3 1-9-1-12-2h-1c-9-4-19-8-27-15l-9-6c-2-2-4-4-5-6-2-2-4-5-6-6z"></path><path d="M170 569v-1h0l3 1c2 0 5 0 7 1h-3l1 1-1 1h2v1c-3-1-6-3-9-4z" class="F"></path><path d="M170 569c-5-1-9-4-14-6-2-2-4-3-6-4h1v-1c3 2 6 3 10 5 1 1 3 2 4 3 2 0 4 1 5 2h0v1z" class="O"></path><defs><linearGradient id="Br" x1="144.238" y1="576.716" x2="179.203" y2="560.528" xlink:href="#B"><stop offset="0" stop-color="#323333"></stop><stop offset="1" stop-color="#565555"></stop></linearGradient></defs><path fill="url(#Br)" d="M132 551c1-1 0 0 1 0 0-1 0-1-1-2l1-1c1 2 3 4 5 6l9 6c8 7 18 11 27 15 2 1 4 1 6 2l-1 1h0c-1-1-1-1-2 0 1 0 3 0 4 1h0l11 3h7s-1 0-2 1h1 0c1 0 3 1 4 1-1 0-1 1-2 0 0 1 0 2-1 3 0 0 1 0 1 1h1 9c0-1 1 0 2 0h-1c-6 1-11 0-16 0-4 0-7 0-10-1-2 0-3-1-4-1-9-3-19-6-27-11-6-4-10-8-14-13-3-4-6-7-8-11z"></path><defs><linearGradient id="Bs" x1="189.946" y1="588.813" x2="188.154" y2="580.261" xlink:href="#B"><stop offset="0" stop-color="#3c3b3c"></stop><stop offset="1" stop-color="#6d6c6b"></stop></linearGradient></defs><path fill="url(#Bs)" d="M169 577h1c1 0 3 1 3 1 3 1 5 1 8 1l11 3h7s-1 0-2 1h1 0c1 0 3 1 4 1-1 0-1 1-2 0 0 1 0 2-1 3 0 0 1 0 1 1h1 9c0-1 1 0 2 0h-1c-6 1-11 0-16 0-4 0-7 0-10-1h2 5c-2-1-4-1-6-1v-1h-3c-4-2-8-3-11-5h0l-1-1-3-1h-1s-1 0-1-1h0 0c1 0 2 1 3 1v-1z"></path><path d="M169 577h1c1 0 3 1 3 1 3 1 5 1 8 1l11 3h7s-1 0-2 1h1 0c1 0 3 1 4 1-1 0-1 1-2 0-4 1-9 0-13-1-2 0-5 0-7-1-3 0-6-1-8-2l-1-1-3-1h-1s-1 0-1-1h0 0c1 0 2 1 3 1v-1z" class="E"></path><path d="M169 577h1c1 0 3 1 3 1 3 1 5 1 8 1l11 3h0 0v1c-1 0-2-1-3-1-7 0-13-2-20-5z" class="O"></path><defs><linearGradient id="Bt" x1="182.749" y1="517.658" x2="134.391" y2="471.188" xlink:href="#B"><stop offset="0" stop-color="#464646"></stop><stop offset="1" stop-color="#737273"></stop></linearGradient></defs><path fill="url(#Bt)" d="M154 453c5-3 9-3 13-3 2 1 3 1 4 2 1 0 3 1 4 2-1 0-2 0-2-1-1 0-3 1-4 0h-1c1 1 1 1 2 1h-1c-1 0-2 1-3 1v1l1 1v1 1h-5c-3 0-6 1-9 2 0 1-1 1-1 1 0 2-1 3-2 5-1 1-2 3-3 4-1 3-1 5-2 7-2 7-3 15-3 23 0 2 1 3 1 5l1 7 1 1c0 2 1 5 2 7 1 4 4 7 5 10h0c1 3 3 5 4 7s4 5 5 7h-1l-1-1c-13-10-24-25-26-42 0-4-1-9 0-13 0-4 1-8 2-12s3-8 5-11l6-7c2-3 5-5 8-6z"></path><path d="M167 450c2 1 3 1 4 2-2 0-5 1-7 0l3-2z" class="X"></path><path d="M154 453c5-3 9-3 13-3l-3 2c-3 0-6 2-10 1z" class="F"></path><path d="M140 508c1 2 1 4 2 6 0 1 2 3 3 4 2 4 3 8 5 11 1 1 1 2 2 2 1 3 3 5 4 7l-2-2c-1-2-3-4-5-6-5-5-8-11-10-18 0-1 0-3 1-4z" class="J"></path><defs><linearGradient id="Bu" x1="170.951" y1="467.55" x2="128.645" y2="495.566" xlink:href="#B"><stop offset="0" stop-color="#070706"></stop><stop offset="1" stop-color="#3c3c3d"></stop></linearGradient></defs><path fill="url(#Bu)" d="M139 512l-1-2c-1-4-2-8-2-11-1-11 0-19 5-28 4-8 10-13 17-16 4 1 6 2 9 3v1h-5c-3 0-6 1-9 2 0 1-1 1-1 1 0 2-1 3-2 5-1 1-2 3-3 4-1 3-1 5-2 7-2 7-3 15-3 23 0 2 1 3 1 5l1 7 1 1c0 2 1 5 2 7 1 4 4 7 5 10h0c-1 0-1-1-2-2-2-3-3-7-5-11-1-1-3-3-3-4-1-2-1-4-2-6-1 1-1 3-1 4z"></path><path d="M152 462c0 2-1 3-2 5-1 1-2 3-3 4-1 3-1 5-2 7-2 7-3 15-3 23 0 2 1 3 1 5l1 7 1 1c0 2 1 5 2 7 1 4 4 7 5 10h0c-1 0-1-1-2-2-2-3-3-7-5-11-1-1-3-3-3-4-1-2-1-4-2-6-1-6-2-13-1-20 0-3 1-7 2-10s3-5 4-7 2-3 3-4c1-2 2-3 4-5z" class="V"></path><path d="M143 477h1 0 0c-1 2-1 3-1 5-2 7-2 14-2 22h-1c-1-9 0-18 3-27z" class="L"></path><path d="M141 478l2-1c-3 9-4 18-3 27v2c2 2 1 4 3 6h1v1l1 1c0 2 1 5 2 7 1 4 4 7 5 10h0c-1 0-1-1-2-2-2-3-3-7-5-11-1-1-3-3-3-4-1-2-1-4-2-6-1-6-2-13-1-20 0-3 1-7 2-10z" class="U"></path><defs><linearGradient id="Bv" x1="126.308" y1="413.297" x2="138.146" y2="406.77" xlink:href="#B"><stop offset="0" stop-color="#353535"></stop><stop offset="1" stop-color="#5c5b5b"></stop></linearGradient></defs><path fill="url(#Bv)" d="M111 374s-1-1-1-2l-3-6c0-1-1-1-1-2l-1-2v-1c-1 0-1-2-1-2-1-3-2-6-2-8v-1-1-1h-1l1-1h0c4 7 8 13 12 19a57.31 57.31 0 0 1 11 11v2l6 8h0c2 4 4 7 6 10 3 6 5 12 7 18 2 5 3 10 3 15v2c1 2 1 4 1 6 0 3 0 7-1 10 0 4-1 7-3 10v1c-1 0-1 0-1-1v-2l-1-6c0-4-1-7-2-10l-3-11c-2-2-3-4-4-7h1c0-6-5-13-8-18v-1c-2-5-5-10-9-14 0-2-1-3-1-4v-2h0c0-1-1-2-1-2-2-2-3-5-4-7z"></path><path d="M128 399c1 1 2 2 3 4h-1c1 1 1 3 1 4h0l-1-2c0-1 0-1-1-2l-1-4z" class="J"></path><path d="M143 418c0-1 0-2-1-3v-2c1 1 1 3 2 4v-2c2 5 3 10 3 15 0 1 0 1-1 2 0-3-1-6-1-9-1-2-1-4-2-5z" class="O"></path><path d="M147 432c1 2 1 4 1 6 0 3 0 7-1 10 0 4-1 7-3 10v1c-1 0-1 0-1-1v-2l-1-6c0-4-1-7-2-10 1-1 1-1 2 0s1 3 2 5c1 0 2 0 3 1v-2c1-3 0-9 0-12z" class="K"></path><defs><linearGradient id="Bw" x1="122.008" y1="383.804" x2="122.954" y2="403.062" xlink:href="#B"><stop offset="0" stop-color="#131111"></stop><stop offset="1" stop-color="#303132"></stop></linearGradient></defs><path fill="url(#Bw)" d="M116 383l12 16 1 4h-3c-2-5-5-10-9-14 0-2-1-3-1-4v-2h0z"></path><defs><linearGradient id="Bx" x1="132.402" y1="400.618" x2="137.346" y2="398.716" xlink:href="#B"><stop offset="0" stop-color="#484748"></stop><stop offset="1" stop-color="#7a797a"></stop></linearGradient></defs><path fill="url(#Bx)" d="M123 380l2 2v-2-1h0l6 8h0c2 4 4 7 6 10 3 6 5 12 7 18v2c-1-1-1-3-2-4v2c1 1 1 2 1 3h0v2h-1 0s-1-1-1-2 0-2-1-2v-2c0-1-1-1-1-2h0c0-2-1-4-2-7l-2-4c0-2-2-5-3-7-1-1-1-3-2-3 1 1 2 3 1 4v-1c-1-1-1-3-2-4l-6-10z"></path><defs><linearGradient id="By" x1="113.914" y1="347.73" x2="111.969" y2="380.77" xlink:href="#B"><stop offset="0" stop-color="#040304"></stop><stop offset="1" stop-color="#363737"></stop></linearGradient></defs><path fill="url(#By)" d="M111 374s-1-1-1-2l-3-6c0-1-1-1-1-2l-1-2v-1c-1 0-1-2-1-2-1-3-2-6-2-8v-1-1-1h-1l1-1h0c4 7 8 13 12 19a57.31 57.31 0 0 1 11 11v2h0v1 2l-2-2-1 1h-1c1 2 2 3 3 4 0 1 3 4 3 5-3-3-5-7-8-9l-3-3c-2-1-3-2-4-4h-1z"></path><defs><linearGradient id="Bz" x1="275.137" y1="588.267" x2="212.429" y2="549.636" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2d2d2d"></stop></linearGradient></defs><path fill="url(#Bz)" d="M294 555h2c-1 3-4 5-6 6-1 2-2 3-4 5s-4 3-6 5c-1 1-3 2-3 3h1c-3 3-6 7-9 8l-1 1c-2 2-5 4-7 5l-2-1c-5 4-11 8-17 9h-3c-1-1-3-1-4-1-2-1-3 0-4-1-7-1-13-1-20-1h-2-1l-1-1c1-1 1-1 1-2l5-1c0-1-1-1-2-1h1c-1 0-2-1-2 0h-9-1c0-1-1-1-1-1 1-1 1-2 1-3 1 1 1 0 2 0-1 0-3-1-4-1h0-1c1-1 2-1 2-1h-7l-11-3h0c-1-1-3-1-4-1 1-1 1-1 2 0h0l1-1c-2-1-4-1-6-2h1c3 1 9 3 12 2h5-2c-1-1-2-1-2-1h-1l-8-3v-1h-2l1-1-1-1h3l8 2h3 2c3 0 7 1 9 0l13 2c2 0 9 0 10 1h5c4 0 9 0 13-1 5 0 9 0 13-1-1-1-2-1-3-1l3-1c5-1 11-2 15-5v-1l3-1s0 1 1 1c6-3 13-5 19-10z"></path><path d="M189 580h3l1-1 1 1c1 1 2 1 3 1h-6c0-1-1 0-2 0v-1z" class="E"></path><path d="M265 575c2-2 5-2 7-4 3-1 5-3 8-3-2 2-7 5-9 6-1 1-2 1-3 1-1 1-2 1-3 2h0c0-1 0-1 1-2h-1z" class="S"></path><path d="M274 564s0 1 1 1c-6 4-12 6-19 8-1-1-2-1-3-1l3-1c5-1 11-2 15-5v-1l3-1z" class="B"></path><path d="M277 574h1c-3 3-6 7-9 8l-1 1c-2 2-5 4-7 5l-2-1 18-13z" class="U"></path><defs><linearGradient id="CA" x1="213.164" y1="568.403" x2="205.178" y2="578.691" xlink:href="#B"><stop offset="0" stop-color="#312f35"></stop><stop offset="1" stop-color="#62625d"></stop></linearGradient></defs><path fill="url(#CA)" d="M193 572c3 0 7 1 9 0l13 2c2 0 9 0 10 1h-12c-8 0-15-1-22-3h0 2z"></path><path d="M252 579l13-4h1c-1 1-1 1-1 2h0l-1 1h0 0c1 0 2 0 2-1h1c-1 1-3 2-4 2-5 2-10 6-16 5h0 2c0-1 0-1 1-1-2 0-3 0-5-1h1c2 0 6-1 7-2l-1-1z" class="I"></path><defs><linearGradient id="CB" x1="224.125" y1="593.2" x2="218.152" y2="583.733" xlink:href="#B"><stop offset="0" stop-color="#3f3e40"></stop><stop offset="1" stop-color="#656564"></stop></linearGradient></defs><path fill="url(#CB)" d="M252 579l1 1c-1 1-5 2-7 2h-1 0l-11 2v1l1 1v1h0c1 0 2 0 4-1h1 3 1c-1 1-5 1-6 2l-10 1c-1 1-3 0-4 1v1h2c1-1 3 0 3-1h1 2c1-1 2-1 4-1h1c2-1 3-1 4-1l2-1h1l1-1h1 2c1-1 3-1 5-1l5-2c2-1 5-1 5-3h1 0c-5 4-13 5-19 7-5 1-11 3-16 4h-13c-1 0-6 0-7 1v1h-1l-1-1c1-1 1-1 1-2l5-1c2 0 4-1 5-1l20-5 14-4z"></path><defs><linearGradient id="CC" x1="196.52" y1="579.357" x2="182.202" y2="568.338" xlink:href="#B"><stop offset="0" stop-color="#3e3d3e"></stop><stop offset="1" stop-color="#5e5d5b"></stop></linearGradient></defs><path fill="url(#CC)" d="M180 570l8 2h3 0l-1 1c1 0 3 1 5 1 4 1 9 2 14 2l-1 1h0 0-3c2 1 3 0 4 1-1 1-4 0-5 0s-3-1-3-1c1 0 2 0 3-1h1-5-1 0c0 1-1 2-1 3h7c2 0 3-1 5 0h-4-8c-1 0-3 0-4 1l-1-1-1 1h-3c-2 0-6 0-8-1-1-1-3-1-4-1 1-1 1-1 2 0h0l1-1c-2-1-4-1-6-2h1c3 1 9 3 12 2h5-2c-1-1-2-1-2-1h-1l-8-3v-1h-2l1-1-1-1h3z"></path><path d="M180 570l8 2h-2-1c-3 0-5-1-7-1l-1-1h3z" class="Q"></path><path d="M307 524h1l-4 6h1c1 0 1-1 1-2l1-1h0c1-1 1-1 1-2h0c1-1 2-3 3-4h0c0 1-1 2-1 3s-1 3-1 4c-4 7-9 13-14 19l-1 2 2-1h1c2-2 4-4 6-7v2h1s1-1 1-2l1 1c-1 2-3 4-5 7-1 1-3 2-3 3-1 2-3 2-4 3-6 5-13 7-19 10-1 0-1-1-1-1l-3 1v1c-4 3-10 4-15 5l-3 1c1 0 2 0 3 1-4 1-8 1-13 1-2 0-5 1-7 0s-5-1-7-1c-6 0-13-1-18-4l-12-6-7-5h0l6 3v-1h0-1 1v-1c4 0 9 1 13 2v-1c-2 0-3-1-4-1v-1c1 0 2 0 3-1h4l1 1h3c7-1 13-2 19-3 1-1 3-1 4-1l1 1h1l5-1c3-1 6-1 8-1 5-1 10-3 15-5 1 0 3-1 5-1 1-1 3-2 4-2l10-5 1-1c1 0 2-1 3-2 0 0 1 0 1-1h1 0l-1 2h0l1-1h1c2-2 4-5 7-7l3-6z" class="Y"></path><path d="M241 554l1 1h1c-1 0-2 1-3 0h-1-2c1-1 3-1 4-1zm30-6c1 0 3-1 5-1h0l-1 1h0c-1 1-2 1-4 0z" class="N"></path><path d="M239 559c2-1 4-2 7-2-1 1-5 2-6 3h0c0-1 0-1-1-1zm25 0c-2 1-3 2-5 3-3 1-5 1-7 2l-1-1h1l-5 1c2-1 6-2 9-3 3 0 5-2 7-2h1z" class="S"></path><path d="M253 560c-2 1-15 5-17 5v-1c6-1 11-4 17-5v1z" class="K"></path><path d="M258 568c1-1 4-2 5-2l9-3h1 1v1h0l-3 1-13 3h0z" class="O"></path><path d="M211 560h1 2c2 0 5 0 6 1 1 0 2-1 3 0h7l9-2c1 0 1 0 1 1-9 2-20 4-29 1h0v-1z" class="I"></path><path d="M288 545h0c-2 2-4 3-6 4-5 3-11 5-16 8h-1l-12 3v-1c1 0 2 0 3-1h0c-3 0-6 0-9 1-1 1-2 1-3 1 3-2 7-3 10-4 9-2 19-4 27-8 2-1 4-2 7-3z" class="C"></path><defs><linearGradient id="CD" x1="288.313" y1="548.107" x2="284.674" y2="540.671" xlink:href="#B"><stop offset="0" stop-color="#292829"></stop><stop offset="1" stop-color="#474646"></stop></linearGradient></defs><path fill="url(#CD)" d="M304 530c-3 8-13 18-21 22l-3 1c-3 1-8 2-11 4h0l1 1c-2 1-4 1-6 1h-1l2-1v-1h1c5-3 11-5 16-8 2-1 4-2 6-4h0 0l9-8c2-2 4-5 7-7z"></path><defs><linearGradient id="CE" x1="240.699" y1="558.931" x2="230.15" y2="578.274" xlink:href="#B"><stop offset="0" stop-color="#161414"></stop><stop offset="1" stop-color="#434343"></stop></linearGradient></defs><path fill="url(#CE)" d="M199 563l-7-5h0l6 3c1 1 3 1 5 2 3 1 5 2 8 2 2 1 4 1 5 1 4 0 8 1 11 2 9 2 19 1 28-1h2l1 1h0l13-3v1c-4 3-10 4-15 5l-3 1c1 0 2 0 3 1-4 1-8 1-13 1-2 0-5 1-7 0s-5-1-7-1c-6 0-13-1-18-4l-12-6z"></path><path d="M258 568l13-3v1c-4 3-10 4-15 5l-12 1 1-1h0l10-2h1c1 0 1 0 2-1h0z" class="L"></path><path d="M199 563l-7-5h0l6 3c1 1 3 1 5 2 3 1 5 2 8 2l15 5-15-1-12-6z" class="a"></path><defs><linearGradient id="CF" x1="288.666" y1="536.285" x2="297.034" y2="550.525" xlink:href="#B"><stop offset="0" stop-color="#4b4a4b"></stop><stop offset="1" stop-color="#6f6f70"></stop></linearGradient></defs><path fill="url(#CF)" d="M307 524h1l-4 6h1c1 0 1-1 1-2l1-1h0c1-1 1-1 1-2h0c1-1 2-3 3-4h0c0 1-1 2-1 3s-1 3-1 4c-4 7-9 13-14 19l-1 2 2-1h1c2-2 4-4 6-7v2h1s1-1 1-2l1 1c-1 2-3 4-5 7-1 1-3 2-3 3-1 2-3 2-4 3-6 5-13 7-19 10-1 0-1-1-1-1h0v-1h-1-1c4-2 7-5 10-7 0-1-1-1-2-1-2 0-3 1-4 1-2 1-4 1-6 2l-1-1h0c3-2 8-3 11-4l3-1c8-4 18-14 21-22l3-6z"></path><path d="M295 547l-1 2 2-1h1c2-2 4-4 6-7v2c-1 0-2 1-3 2-2 4-4 8-8 9l-1 1c1-2 3-4 4-5l3-2c-2 1-3 1-5 2v-1l2-2z" class="F"></path><path d="M280 553h0c-1 1-1 1-2 1s-1 0-1 1h2v-1c1 0 4 0 5-1h1c1 0 1-1 2-1h1c-1 1-4 3-6 4h0c0-1-1-1-2-1-2 0-3 1-4 1-2 1-4 1-6 2l-1-1h0c3-2 8-3 11-4z" class="T"></path><path d="M303 543h1s1-1 1-2l1 1c-1 2-3 4-5 7-1 1-3 2-3 3-1 2-3 2-4 3-6 5-13 7-19 10-1 0-1-1-1-1h0c5-3 12-6 17-9l1-1c4-1 6-5 8-9 1-1 2-2 3-2z" class="U"></path><path d="M144 138c13-14 28-25 44-35 33-19 74-28 112-27 11 0 22 1 33 3l24 5 17 4c4 1 8 3 13 3 11 2 24 2 36-1 2-1 5-2 8-4 2-3 2-7 2-11v-3h8v149h-8c-2-11-4-22-8-32l-3-9c0-1-2-4-2-6l-10-20c0-2-1-3-2-5-2-6-6-10-9-14-3-3-5-6-7-8l-5-5c-2-1-3-2-4-3h0-1l-1-1c-1-1-2-2-3-2 0-1-1-1-2-2l-2-2c-1 0-2-1-2-1-1-1-1-1-2-1 0-1-1-1-2-2h-1c-7-3-13-7-20-10-23-9-52-11-76-5-29 9-53 26-68 53-16 28-23 59-27 91l-2 30-1 37 1 102 3 49h0l-2-1c-1-1-3-2-4-2-1-1-2-1-4-2-4 0-8 0-13 3-3 1-6 3-8 6l-2-1c2-3 3-6 3-10 1-3 1-7 1-10 0-2 0-4-1-6v-2c0-5-1-10-3-15-2-6-4-12-7-18-2-3-4-6-6-10h0l-6-8v-2a57.31 57.31 0 0 0-11-11c-4-6-8-12-12-19h1c1 0 1 2 2 3h0 0v1l1 1h0v1h1c2 1 4 5 4 6v1c0 1 1 2 2 3v-1h1c1-1 1-1 1-2s0-1 1-1v-1-1h0c-1-1-1-1-1-2h-1v-1l-1-2-1-1v-1l-1-1v-1l-1-1-1-1c0-1 0-1-1-2h0v-2l-2-4h0c-6-13-9-26-11-40-2-10-2-20-1-30 1-4 2-9 3-13 1-2 2-4 2-6 0-1 0-1 1-1v-1h-1c0 1 0 1-1 2h0-1c-1 3-3 6-4 9v1c-1-1 1-5 1-7-1-1 1-3 1-4l2-4c0-1 1-3 2-4h-1-1l6-11c0-2 3-4 4-5-2-2-2-2-4-2h0c-1 2-3 3-5 4-2 2-4 4-6 7-1 2-1 4-2 6l-1 5c-1 1-1 3-2 4 0 0-1 0-1 1 1-7 4-15 6-22 1 0 1 0 2-1v-1c0-1 1-2 1-3h0-1c1-1 1-2 0-3 0 0 1-2 1-3 2-7 5-14 9-21l4-8 2-4 2-3c1-1 2-2 2-3l3-4 2-4c2-4 6-8 9-12 2-3 4-6 6-8 1-2 2-3 3-4l3-3 4-4z" class="d"></path><path d="M168 436l1 1v6h0l-1-7zm26-291c1-1 3-1 4-1h0l-3 3h-1v-1-1h0z" class="Y"></path><path d="M168 428l1 9-1-1-1-7 1-1z" class="N"></path><path d="M174 195h-1l1-1c0-1 1-2 1-3v-1c0-1 1-1 1-2h2 0l-2 5v-2h-1l-1 4h0z" class="S"></path><path d="M166 417c1 3 1 7 2 11l-1 1-2-11c0 1 1 2 1 3h0v-1-3z" class="I"></path><path d="M171 412c1 3 2 10 1 13-1-2-1-5-2-7 0-2 0-4-1-6h2z" class="J"></path><path d="M122 279l1-2v3c1 2 3 5 3 8h0l-1 1c-1 0-1-1-1-2h0c-1-3-1-6-2-8z" class="N"></path><path d="M165 401c1 1 1 2 2 3s2 6 2 8c1 2 1 4 1 6v-1c0-1 0-1-1-1v-3s0-1-1-2h0c-2-2-2-7-3-10zm-21-263h1c2 0 4-1 6-1h0-1v1h1 0l-7 3s-1 0-2 1h-2l4-4z" class="K"></path><path d="M161 355c1 1 1 2 2 3 0 0 0 1 1 1l4 13-6-12h0c0-2-1-3-2-4l1 1v-2z" class="C"></path><path d="M160 261h2c1 0 1 0 1 1h1c1 1 3 9 4 11-3-4-6-8-8-12z" class="I"></path><path d="M161 353v-4h0c2 2 3 4 5 8 1 2 2 3 3 6v1h0c0-1-1-2-1-3-1-1-2-1-2-3-1-1-2-2-2-3s-1-2-1-3c-1 1-1 0-1 1v1c0 1 0 2 1 2v1l1 2h0c-1 0-1-1-1-1-1-1-1-2-2-3v-2zm1-81h0c5 6 8 14 9 23v-1c-1 0 0-1-1-2v-1-2c-1 0-1-1-1-2s-1-2-1-3-1-2-1-2v-1-1c-1-1-1-1-2-1h0v1 1 1h0v2h-1v-1c0-1-1-5-1-6s-2-3-2-5h1z" class="L"></path><path d="M160 391s2 3 3 4c0 2 2 4 2 6 1 3 1 8 3 10v2c1 1 1 3 0 4v1c0 1 1 1 0 2v-3c-1-1 0-3-1-4-1-2-1-5-2-8 0-2-1-2-2-3 0-1-1-2-1-3v-1h-1c1-1 0-2 0-3 0 0 0-1-1-1v-1-1s-1 0 0-1z" class="S"></path><path d="M194 145v1 1h1l-8 8v-1c-1 0-1-1-2 0 0 0-1 1-2 1 0 1-1 1-2 2l6-5-2-1 5-4 4-2z" class="I"></path><path d="M194 145v1 1c-3 1-5 3-7 5l-2-1 5-4 4-2z" class="d"></path><path d="M412 136c3 0 5 3 7 4h0c4 6 6 13 8 20h0c-2-3-3-6-5-9 1-1-2-4-2-5-3-3-5-7-8-10z" class="T"></path><path d="M159 345l-2-4h1l1 1h0c5 5 9 12 11 19h-1 0v-2c0-1-1-3-2-4l-1 2c-2-4-3-6-5-8h0v4l-1-1v-1c0-2 0-4-1-6z" class="C"></path><path d="M152 420l1 12v-3c0 1 0 2 1 3 0-4 0-8-1-11 0-3-1-5-1-8h0c1 1 1 2 1 3 1 1 1 3 2 4h-1v3h0l-1 26h0c-1-2-1-4-1-6 1-3 1-7 1-10-1-1 0-3-1-4v-1-1 2h-1 0v-5-3s0-1 1-1z" class="I"></path><path d="M403 108c1 0 3 1 4 2l10 7c2 1 4 3 6 3 1 1 1 2 2 2l2 2h1l1 1c1 2 2 3 3 4l7 7-1 1h0l-2-3c-3-1-6-5-8-7-1 0-2-1-2-1-1 0-1-1-2-1l1-1v1h2 0c-1 0-1-1-2-2-1 0-2-1-3-2-1 0-2-2-3-3-2-1-3-2-5-3h0c-1 0-1-1-2-1h-1c1 0 1 0 1 1l-2-2c-3-1-5-3-7-4v-1h0z" class="N"></path><path d="M131 213h1c2-2 2-5 4-7l1 1c-1 1-2 2-3 4 0 0-1 1-1 2s-2 3-3 4c0 1-1 1-1 2 0 0-1 0-1 1-1 1-2 3-2 4l-1 1h0c0 1 0 1-1 2h1v1h-1c0 1 0 1-1 2 0 0 0 1 1 1h1c0-1 1-3 2-4 0-1 1-2 1-2l-5 11c-1 0-2 0-2-1v-2c1-2 1-4 2-6 1-1 2-3 2-4h-1s0-1 1-1c1-2 2-4 4-6 1-1 2-1 2-3z" class="K"></path><path d="M155 420c0 1 0 4 1 5l1-1h0v3c1 4 2 20 1 22h-1c-1-1 0-3-1-5-1-4-1-9 0-13 0-1 0-2-1-3v-1-1-1c0-1 0-1-1-1 0-1 1-2 0-2v-2h1z" class="N"></path><path d="M421 152c1 0 1-1 1-1 2 3 3 6 5 9h0c1 2 2 4 3 7 1 5 2 11 2 17-1-2-5-13-5-15h1c-1-3-2-5-2-7-2-4-3-7-5-10z" class="I"></path><path d="M150 384c1 1 2 3 3 4l2 2 2 4v2h0v1h0v1c0 1 2 5 4 6v1h1v-1-1l-1-1c0-2-1-3-2-5 0-1-1-2-1-3 3 3 4 7 5 10 1 4 3 9 3 13v3 1h0c0-1-1-2-1-3-3-7-6-15-9-22-2-3-4-6-5-9h-1l1-1c0-1-1-1-1-2z" class="B"></path><defs><linearGradient id="CG" x1="400.553" y1="110.624" x2="397.931" y2="118.133" xlink:href="#B"><stop offset="0" stop-color="#292a2a"></stop><stop offset="1" stop-color="#474545"></stop></linearGradient></defs><path fill="url(#CG)" d="M391 110c0-1 0-2-1-2l1-1-2-1c-1-1-2-2-4-2-3-3-7-4-10-6h0c8 4 16 8 23 13 2 2 5 3 6 5h0l1 1v1l8 7-1 1-5-4c-5-4-10-8-16-12z"></path><defs><linearGradient id="CH" x1="166.373" y1="215.746" x2="160.41" y2="215.324" xlink:href="#B"><stop offset="0" stop-color="#282829"></stop><stop offset="1" stop-color="#444443"></stop></linearGradient></defs><path fill="url(#CH)" d="M163 209v-1c0-1 1-2 1-3 1-3 3-5 6-8v-1c0-1 1-1 1-2l1 1c-2 1-3 4-4 5-1 2-3 3-3 5h1l2-2c1-2 2-3 3-4l-3 6h0c0 1-5 12-6 15s-2 7-3 11h0-1c1-4 2-9 3-13l-1-1 3-7v-1z"></path><path d="M175 200s0 2-1 3v17l1 10v5h0-1l-1-1c-2-11-4-19 0-30 1-1 1-3 2-4z" class="B"></path><path d="M163 395c-2-8-6-16-9-24-1-2-2-3-3-5h1c3 5 6 11 9 16 4 9 8 20 10 30h-2c0-2-1-7-2-8s-1-2-2-3c0-2-2-4-2-6z" class="F"></path><path d="M404 116h2l1 1c1 0 1 1 1 1h1c0 1 1 2 2 2h0l2 2c1 1 2 1 3 2 2 1 6 5 7 6 1 2 6 6 6 7h-2s-1 0-1 1l-3-3c-2-1-5-3-7-5-2-1-3-3-5-4l-1-1-2-2-1-1h0l5 4 1-1-8-7v-1l-1-1h0z" class="B"></path><path d="M404 116c3 3 9 5 10 9h-1l-8-7v-1l-1-1h0z" class="C"></path><path d="M413 125h1a57.31 57.31 0 0 1 11 11c-2-1-3-2-4-3l-9-7 1-1z" class="V"></path><path d="M156 193l6-6c1-1 2-2 2-3 1 0 2-1 2-1h1 0v1c0 1 0 1-1 1h1l1 1c0-1 1-2 1-2 1-1 1-2 2-2h0l-1 2-3 3-5 6v1c0 1-2 2-3 2 0 2-2 4-3 5-2 1-3 3-5 4 0-1 2-2 3-3-1 0-2 2-3 2l-1-1 2-2c0-3 2-4 3-6 1 0 2-1 2-2-1 0-7 6-8 8h-1c1-1 1-2 2-3 2 0 4-4 6-5z" class="B"></path><path d="M156 193l6-6c1-1 2-2 2-3 1 0 2-1 2-1h1 0v1c0 1 0 1-1 1h1l1 1c0-1 1-2 1-2 1-1 1-2 2-2h0l-1 2-3 3-5 6v1c0 1-2 2-3 2 2-3 6-6 8-10-3 1-5 6-8 7l-1-1c2-2 7-5 8-7-3 2-7 6-10 8h0z" class="N"></path><defs><linearGradient id="CI" x1="174.942" y1="155.509" x2="163.515" y2="142.811" xlink:href="#B"><stop offset="0" stop-color="#0e0c0f"></stop><stop offset="1" stop-color="#353635"></stop></linearGradient></defs><path fill="url(#CI)" d="M150 154h0c10-6 20-9 31-10 2 0 4 0 6 1h4 3 0l-4 2h-3v-1c-2 0-6 2-8 1-3 1-7 1-10 2l-11 3c-2 1-4 2-6 2h-2z"></path><path d="M194 145h0l-4 2h-3v-1c1-1 3-1 4-1h3z" class="I"></path><path d="M169 149h-2l9-3c0 1 0 1 1 1h2c-3 1-7 1-10 2z" class="N"></path><path d="M136 387c11 9 17 23 21 37l-1 1c-1-1-1-4-1-5-1-1-1-3-2-4 0-1 0-2-1-3h0c0 3 1 5 1 8 1 3 1 7 1 11-1-1-1-2-1-3v3l-1-12c0-1-1-3-1-4 0-2-1-5-2-7h-1c0 1 1 3 1 3l-1-1v2l-2-4c-1-2-2-3-3-5l-3-8c-1-2-3-3-4-5v-2c0-1-1-1 0-2z" class="U"></path><defs><linearGradient id="CJ" x1="143.928" y1="371.393" x2="154.31" y2="362.615" xlink:href="#B"><stop offset="0" stop-color="#2d2d2e"></stop><stop offset="1" stop-color="#464645"></stop></linearGradient></defs><path fill="url(#CJ)" d="M138 342l14 24h-1c1 2 2 3 3 5 3 8 7 16 9 24-1-1-3-4-3-4l-22-43c1-1-1-4-1-5l1-1z"></path><defs><linearGradient id="CK" x1="124.144" y1="256.494" x2="108.236" y2="258.499" xlink:href="#B"><stop offset="0" stop-color="#313233"></stop><stop offset="1" stop-color="#626061"></stop></linearGradient></defs><path fill="url(#CK)" d="M116 242c2-2 3-6 5-9v2c0 1 1 1 2 1-4 9-7 19-9 29l-1 7c0 1 0 3-1 4v8 12c-1-2-1-4-1-6v-8-22c0-5 2-9 3-14h0l2-4z"></path><defs><linearGradient id="CL" x1="132.514" y1="297.968" x2="116.629" y2="308.323" xlink:href="#B"><stop offset="0" stop-color="#272727"></stop><stop offset="1" stop-color="#636363"></stop></linearGradient></defs><path fill="url(#CL)" d="M116 271v-2-2-1c0-1 1-2 1-3v-1c0-1 1-2 1-3h0v3 1l-1 1v3c0 1 0 4-1 5v4c1 1 1 1 2 0h1v-3-4c0-2 1-4 1-6h0c0 6-1 12-1 19 0 8 2 17 4 24 4 12 9 24 15 36l-1 1c-3-6-6-11-9-16-1-4-3-7-5-11 0-2-1-4-2-6s-1-5-2-7c0-1 0-2-1-3 0-3-1-6-1-9-1-6-1-13-1-20z"></path><defs><linearGradient id="CM" x1="171.764" y1="138.402" x2="149.981" y2="168.566" xlink:href="#B"><stop offset="0" stop-color="#373837"></stop><stop offset="1" stop-color="#5a585b"></stop></linearGradient></defs><path fill="url(#CM)" d="M179 147c2 1 6-1 8-1v1h3l-5 4-7 3h0c-3 2-6 4-10 5v-1l6-3-13 3-1-1h-2-1v-1c-1 0-3 1-4 1-2 0-3 1-4 1-2 0-5-1-6 0h0c-1 1-1 1-2 1-1 1-1 0-2 1h-1 0c2-1 4-2 5-3l4-2 3-1h2c2 0 4-1 6-2l11-3c3-1 7-1 10-2z"></path><path d="M187 147h3l-5 4-7 3h0c-3 2-6 4-10 5v-1l6-3-13 3-1-1c3 0 7-1 9-3h0c-3 0-6 2-9 2l26-9h1z" class="N"></path><path d="M187 147h3l-5 4-7 3h0c2-2 4-3 6-4 1-1 2-1 2-3h1z" class="S"></path><defs><linearGradient id="CN" x1="140.716" y1="317.104" x2="150.324" y2="314.596" xlink:href="#B"><stop offset="0" stop-color="#252525"></stop><stop offset="1" stop-color="#403f3e"></stop></linearGradient></defs><path fill="url(#CN)" d="M137 311h0s1 0 1 1 1 1 1 2c1 0 1 2 2 3 1 2 2 4 4 6v1-2-2h-1v-1l-1-1v-2c-1 0-2-1-2-2v-1c-1-1-1-2-2-3 0-1-1-2-1-3-1-5-4-9-6-13-1-4-3-9-4-13 0-1-1-2 0-2h0v1c0 1 0 1 1 2v1c0 2 1 3 2 5v1c1 0 1 1 1 1h0c1-1 0-1 0-2h-1c0-1 0-3-1-4l-1-4c-1 0 0-1 0-1l2 5s1 1 1 2 1 2 2 4h0v1l1 1h0c0 1 1 1 1 2 2 1 2 3 3 5 0 1 1 2 1 3h0c0 1 1 2 1 4 0 1 1 1 2 2v-2c1 1 2 2 2 3l1 3 1 1v1l5 10c1 1 2 3 2 4v2c0 1 1 2 1 3v1c0-1-1-2-2-2-1-1-2-4-3-5l-1 1-1-1h0l2 3 2 2-1 1h0l-1-2c-5-6-10-13-13-20z"></path><path d="M142 309h1c0 1 1 0 2 0l1 3h-1v1c-1-1-2-2-3-4z" class="E"></path><path d="M145 313v-1h1l1 1v1l5 10h-1c-3-3-5-7-6-11z" class="F"></path><path d="M142 309c-1 0-2-3-2-4-2-4-4-7-6-11h1 0l5 8c0 1 1 2 1 4 0 1 1 1 2 2v-2c1 1 2 2 2 3-1 0-2 1-2 0h-1z" class="V"></path><defs><linearGradient id="CO" x1="172.298" y1="226.32" x2="158.974" y2="225.99" xlink:href="#B"><stop offset="0" stop-color="#302f30"></stop><stop offset="1" stop-color="#686868"></stop></linearGradient></defs><path fill="url(#CO)" d="M174 195h0l1-4h1v2c-7 20-18 40-14 63l2 6h-1c0-1 0-1-1-1h-2l-3-7v-1h0c-1 0-1-1-1-2 0-2 1-3 1-5v-6l1-9h1 0c1-4 2-8 3-11s6-14 6-15h0l3-6s1-1 1-2l2-2z"></path><path d="M158 231h1c-1 10-2 17 2 26h1v-1l2 6h-1c0-1 0-1-1-1h-2l-3-7v-1h0c-1 0-1-1-1-2 0-2 1-3 1-5v-6l1-9z" class="J"></path><path d="M383 119h1c1 0 2 1 2 2 2 1 4 3 6 4 1 2 5 6 6 8 0 0 1 0 1 1l3 3v-1s0-1-1-1v-1h0v-1h0c-2-2-4-4-5-6-1-1-1-2-2-2v-1s-2-1-2-2c-1 0-2 0-2-1l-7-5c-1-1-2-2-4-3-1-1-2-1-3-2l-4-3-3-2h0c4 2 7 4 11 7 3 2 7 6 11 8-2-3-5-5-7-7s-4-3-5-5c3 2 6 4 8 6l7 6c0 1 2 3 3 4 1 0 2 2 3 3 0 1 1 2 2 2v1c1 1 1 1 1 2l3 3c2 3 4 5 5 8 0 1 1 2 2 3v1l1 2 5 8v1 2h0c1 1 1 1 1 2s1 1 1 2c1 2 2 4 2 5 2 5 4 10 5 15-1-1-4-11-5-13-2-3-3-5-4-8h0 0c-1-1-1-2-1-2 0-1-1-2-1-3l-2-3-1-1c-1-2-2-4-4-7v1c0 1 0 1 1 2v2c1 1 3 3 3 5h-1v1l1 1c0 1 0 1 1 1 0 1 1 3 1 4h0 0c1 1 1 1 1 2h0l2 3h0c0 1 1 1 1 2v1 1l-10-20c0-2-1-3-2-5-2-6-6-10-9-14-3-3-5-6-7-8l-5-5c-2-1-3-2-4-3z" class="L"></path><defs><linearGradient id="CP" x1="137.224" y1="419.396" x2="148.197" y2="415.643" xlink:href="#B"><stop offset="0" stop-color="#070706"></stop><stop offset="1" stop-color="#302f30"></stop></linearGradient></defs><path fill="url(#CP)" d="M132 385c-1 0-1 0-1-1h2l3 3c-1 1 0 1 0 2v2c1 2 3 3 4 5l3 8c1 2 2 3 3 5l2 4v-2l1 1s-1-2-1-3h1c1 2 2 5 2 7 0 1 1 3 1 4-1 0-1 1-1 1v3 5h0c0 6 0 12-1 18 0 2 0 4-1 6h0l-1-2v-1h0c-1-1-1-1-1-2 1-3 1-7 1-10 0-2 0-4-1-6v-2c0-5-1-10-3-15-2-6-4-12-7-18-2-3-4-6-6-10h0v-1l1-1z"></path><path d="M132 385c-1 0-1 0-1-1h2l3 3c-1 1 0 1 0 2v2c-1-2-3-3-3-5-1 0-1 0-1-1z" class="L"></path><path d="M148 413v-2l1 1s-1-2-1-3h1c1 2 2 5 2 7l-1 1v3 2c-1-1-2-7-2-9z" class="V"></path><path d="M152 283v-2h1c0 1 1 2 1 2 1 2 3 4 3 6 1 1 2 4 3 4l7 15c2 3 3 7 4 10l-7-5c-1 0-2-1-2-1l-2-1s-3-2-3-3c-2-2-4-3-6-6l-1-2-1-1c-1 0-1-1-1-2 0 0 1 0 1-1s1-2 1-3l1 1 1-1v-1h0c0-1-1-1 0-2v-7z" class="K"></path><path d="M149 299c-1 0-1-1-1-2 0 0 1 0 1-1s1-2 1-3l1 1c0 2 3 3 4 5 1 1 1 3 1 4-1-1-2-2-3-4-1 0-2 1-3 1l-1-1z" class="V"></path><path d="M149 299l1-1v-1h1c1 0 2 1 2 2-1 0-2 1-3 1l-1-1z" class="Q"></path><path d="M156 297h-1c-1-1-2-3-2-4v-1c0-2 0-4 1-5v-2-2c1 2 3 4 3 6h-1l2 6-1 1c-1-2-1-3-2-4h-1c1 2 2 3 2 5z" class="E"></path><path d="M150 300c1 0 2-1 3-1 1 2 2 3 3 4 2 2 3 5 5 7l1 2h0l-2-1s-3-2-3-3c-2-2-4-3-6-6l-1-2z" class="F"></path><path d="M158 295l-2-6h1c1 1 2 4 3 4l7 15c2 3 3 7 4 10l-7-5c-1 0-2-1-2-1h0l-1-2 1-1v1l1 1v-1h0v-1c-1-1-2-2-2-4l-5-8c0-2-1-3-2-5h1c1 1 1 2 2 4l1-1z" class="L"></path><path d="M162 305l4 6-1 1-2-2v-1c-1-1-2-2-2-4h1z" class="J"></path><path d="M156 297c0-2-1-3-2-5h1c1 1 1 2 2 4l5 9h-1l-5-8z" class="F"></path><defs><linearGradient id="CQ" x1="167.752" y1="300.847" x2="155.462" y2="298.036" xlink:href="#B"><stop offset="0" stop-color="#3e3d3c"></stop><stop offset="1" stop-color="#646365"></stop></linearGradient></defs><path fill="url(#CQ)" d="M158 295l-2-6h1c1 1 2 4 3 4l7 15c0 1 0 1-1 1-3-4-7-9-8-14z"></path><defs><linearGradient id="CR" x1="162.605" y1="342.307" x2="152.529" y2="313.123" xlink:href="#B"><stop offset="0" stop-color="#222"></stop><stop offset="1" stop-color="#3c3b3c"></stop></linearGradient></defs><path fill="url(#CR)" d="M149 307h2 0c0-2-1-3-1-4h-1v-1l3 3h0c0-1-1-2-1-3 2 3 4 4 6 6 0 1 3 3 3 3v1l4 20c1 4 4 7 5 11-4-3-9-4-12-7-1-1-2-1-2-2v-1c0-1-1-2-1-3v-2c0-1-1-3-2-4l-5-10v-1c1-1-2-5-2-7 0-1 1-1 1-2 1 0 1 0 2 1 0 0 1 1 1 2z"></path><path d="M146 304c1 0 1 0 2 1 0 0 1 1 1 2l5 11v2c1 1 2 4 3 6l-1 1c-2-3-3-7-5-9l-2-3c-1-1-1-1-2-1v-1c1-1-2-5-2-7 0-1 1-1 1-2z" class="J"></path><path d="M149 307h2 0c0-2-1-3-1-4h-1v-1l3 3h0c0-1-1-2-1-3 2 3 4 4 6 6 0 1 3 3 3 3v1l4 20-3-3c-1-1-1-2-2-3l-5-8-5-11z" class="N"></path><path d="M149 307h2 0c0-2-1-3-1-4h-1v-1l3 3h0c4 7 5 16 9 23v1h0c-1-1-1-2-2-3l-5-8-5-11z" class="F"></path><path d="M426 166c-1-1-2-2-2-3 0 0-1-1-1-2v-1h0c-1-1-1-1-1-2h-1c0-1-1-2-1-3-1-1-2-3-3-4l-7-12s0-1-1-1c0-1-1-2-1-3-2-2-4-4-5-6-1-1-2-1-2-2v-1l1 1v-1l1 1c-1-2-2-3-4-4-3-2-6-6-8-9h0c1 1 3 4 5 5h0 1 0l1-1-6-5s-1-1-1-2h0l-13-9h0c1 0 1 0 2 1l4 2 7 5c6 4 11 8 16 12h0v1c4 4 8 8 11 13l3 3c3 4 7 6 9 10 1 1 4 3 5 5l-1 1-6-6c0-1-1-1-1-2-3-2-5-6-8-7h0c-2-1-4-4-7-4 3 3 5 7 8 10 0 1 3 4 2 5 0 0 0 1-1 1 2 3 3 6 5 10 0 2 1 4 2 7h-1l-1-3z" class="I"></path><path d="M404 123c-3-2-5-4-7-6 3 1 6 3 8 5l-1 1z" class="C"></path><path d="M410 134l-11-12c6 2 10 7 14 10h0c-2-3-6-7-9-9l1-1 22 24v1c-3-2-5-6-8-7h0c-2-1-4-4-7-4l-2-2z" class="V"></path><path d="M426 166c-2-6-5-11-8-16l-9-14c-2-3-4-5-6-8 2 2 4 3 5 5 1 1 1 1 2 1l2 2c3 3 5 7 8 10 0 1 3 4 2 5 0 0 0 1-1 1 2 3 3 6 5 10 0 2 1 4 2 7h-1l-1-3z" class="E"></path><path d="M418 147l1-1h1c0 1 3 4 2 5 0 0 0 1-1 1-1-2-2-4-3-5z" class="B"></path><path d="M408 133c1 1 1 1 2 1l2 2c3 3 5 7 8 10h-1l-1 1c-1-1-1-3-2-4s-2-1-2-2l-3-3c-1-2-2-3-3-5z" class="C"></path><defs><linearGradient id="CS" x1="128.59" y1="277.059" x2="169.336" y2="255.701" xlink:href="#B"><stop offset="0" stop-color="#040605"></stop><stop offset="1" stop-color="#393739"></stop></linearGradient></defs><path fill="url(#CS)" d="M142 295c0-4 2-8 4-12 1-4 2-9 3-13 1-5 0-11 1-16 0-9 2-19 4-27l1 1v3h1c-2 4-2 7-3 10 0 2 0 6-1 8v1 1l-1 1v2c0 2 0 4-1 5v1 1h0c1 1 1 3 1 5 0 3 1 5 2 8 1 4 2 8 4 12 2 2 4 5 5 7l-1-1-4-5c1 1 1 1 1 2l1 2c1 1 1 1 1 2-1 0-2-3-3-4 0-2-2-4-3-6 0 0-1-1-1-2h-1v2 7c-1 1 0 1 0 2h0v1l-1 1-1-1c0 1-1 2-1 3s-1 1-1 1c0 1 0 2 1 2l1 1 1 2c0 1 1 2 1 3h0l-3-3v1h1c0 1 1 2 1 4h0-2c0-1-1-2-1-2-1-1-1-1-2-1 0 1-1 1-1 2 0 2 3 6 2 7l-1-1-1-3c0-1-1-2-2-3v2c-1-1-2-1-2-2 0-2-1-3-1-4h0 1v-4s1-2 1-3z"></path><path d="M144 295c1-1 1-3 2-5 1 2 0 2 0 3h0v4h0l-2-2zm-2 0v5c0 1-1 2 0 2 0 1 1 2 1 3h0c-1 0-1 0-2 1 0-2-1-3-1-4h0 1v-4s1-2 1-3z" class="N"></path><path d="M144 299c1 1 2 3 2 5 0 1-1 1-1 2 0 2 3 6 2 7l-1-1-1-3c0-1-1-2-2-3v2c-1-1-2-1-2-2 1-1 1-1 2-1h0 1 0v-6z" class="I"></path><defs><linearGradient id="CT" x1="142.995" y1="298.141" x2="150.943" y2="304.031" xlink:href="#B"><stop offset="0" stop-color="#222121"></stop><stop offset="1" stop-color="#4c4c4d"></stop></linearGradient></defs><path fill="url(#CT)" d="M152 283v7c-1 1 0 1 0 2h0v1l-1 1-1-1c0 1-1 2-1 3s-1 1-1 1c0 1 0 2 1 2l1 1 1 2c0 1 1 2 1 3h0l-3-3v1h1c0 1 1 2 1 4h0-2c0-1-1-2-1-2-1-1-1-1-2-1 0-2-1-4-2-5v-4l2 2h0v-4h1v-1l1 1v-1h0v-1h-1 2 0 1l1 1c0-1 0-1-1-2v-1-1h0v-1c0-1 0-1 1-1v-1c0-1 1-1 1-2z"></path><defs><linearGradient id="CU" x1="150.95" y1="255.17" x2="162.799" y2="255.199" xlink:href="#B"><stop offset="0" stop-color="#373737"></stop><stop offset="1" stop-color="#595859"></stop></linearGradient></defs><path fill="url(#CU)" d="M160 217l1 1c-1 4-2 9-3 13l-1 9v6c0 2-1 3-1 5 0 1 0 2 1 2h0l1 9c0 2 1 3 2 4h-1 0c0 1 1 2 1 3-1 1 0 1 0 1v1 1s0 1 1 1h0c0 2 1 5 2 7v1c1 0 1 2 1 2v3h1c0 2 1 4 2 5v2l1 1-1 1v-1h-2c1 1 1 1 1 2v1l1 1v1h0c-1-1-4-4-5-6h0c-1-2-3-5-5-7-2-4-3-8-4-12-1-3-2-5-2-8 0-2 0-4-1-5h0v-1-1c1-1 1-3 1-5v-2l1-1v-1-1c1-2 1-6 1-8 1-3 1-6 3-10s2-9 4-14z"></path><path d="M158 262c0 2 1 3 2 4h-1 0c0 1 1 2 1 3-1 1 0 1 0 1v1 1s0 1 1 1h0c0 2 1 5 2 7v1c1 0 1 2 1 2v3h1c0 2 1 4 2 5v2l1 1-1 1v-1h-2c1 1 1 1 1 2v1l1 1v1h0c-1-1-4-4-5-6h1c0-4-3-9-4-12l6 10c-1-9-7-19-7-29z" class="L"></path><path d="M131 304c0-2-1-3-1-4h1l6 11c3 7 8 14 13 20l1 2h0l3 3c2 2 4 3 5 6h0l-1-1h-1l2 4c1 2 1 4 1 6v1l1 1v2 2l-1-1c1 1 2 2 2 4h0 0c-1-1-1-2-2-2v1h0c1 0 1 1 1 2v1h0v1 2c-3-4-4-9-7-13l-5-7c-1-1-1-2-2-3l-1 1-2-2c0-1-1-3-2-4-1-2-2-4-3-7 0-2-2-4-2-6-1-2-1-4-1-6-2-3-4-7-5-10v-1h1l-1-3z" class="V"></path><path d="M154 352h0c0-2-1-4-1-6l4 6c2 2 2 2 3 4 1 1 2 2 2 4h0 0c-1-1-1-2-2-2v1h0c1 0 1 1 1 2v1h0v1 2c-3-4-4-9-7-13z" class="L"></path><path d="M145 329s1 0 1 1h1l1 1h2l1 2h0l3 3c2 2 4 3 5 6h0l-1-1h-1l2 4v2h-1l-1-4-7-7c-1-2-2-3-4-4v-1c-1-1-1-1-1-2z" class="X"></path><path d="M154 336c2 2 4 3 5 6h0l-1-1h-1l2 4v2h-1l-1-4c0-2-2-3-3-4v-3z" class="U"></path><path d="M131 304c0-2-1-3-1-4h1l6 11c3 7 8 14 13 20h-2l-1-1h-1c0-1-1-1-1-1-3-4-6-8-8-12 0-1 0-2-1-3 0-2-1-3-2-4-1-2-1-4-3-6z" class="O"></path><defs><linearGradient id="CV" x1="148.853" y1="334.432" x2="140.423" y2="338.895" xlink:href="#B"><stop offset="0" stop-color="#151414"></stop><stop offset="1" stop-color="#2f2f2f"></stop></linearGradient></defs><path fill="url(#CV)" d="M122 279c1 2 1 5 2 8h0c0 1 0 2 1 2l1-1c1 2 1 4 2 6 1 1 3 5 3 6h-1c0 1 1 2 1 4l1 3h-1v1c1 3 3 7 5 10 0 2 0 4 1 6 0 2 2 4 2 6 1 3 2 5 3 7 1 1 2 3 2 4l2 2 1-1c1 1 1 2 2 3l5 7c3 4 4 9 7 13l3 8h0c3 5 3 11 4 17-4-8-8-16-12-23l-20-36c-2-3-3-6-4-8s-1-4-2-6l-3-8c-3-10-5-21-5-30z"></path><path d="M144 341l2 2c1 1 2 2 3 4l4 6c1 1 1 2 2 3 2 3 4 7 5 11-2-2-3-6-4-8-1 1 0 1 0 2-1-1-1-2-2-3h0c-3-5-6-9-8-14-1-1-1-2-2-3z" class="C"></path><path d="M146 343l1-1c1 1 1 2 2 3l5 7c3 4 4 9 7 13l3 8v1l-4-7c-1-4-3-8-5-11-1-1-1-2-2-3l-4-6c-1-2-2-3-3-4z" class="Q"></path><path d="M126 288c1 2 1 4 2 6 1 1 3 5 3 6h-1c0 1 1 2 1 4l1 3h-1v1c1 3 3 7 5 10 0 2 0 4 1 6 0 2 2 4 2 6 1 3 2 5 3 7-2-1-2-2-3-4 0-2-1-3-2-5v-1l-3-9c-1-5-4-10-5-15-1-2-1-4-1-6-1-3-2-6-3-8l1-1z" class="C"></path><path d="M144 141l-2 1c0 1-1 1-1 1h-1l-1 1h0 1 2l2-1c0-1 0-1 1-1l1-1h2s0 1 1 0h1 0c1 0 1-1 2-1s2-1 3-1l3-2c1 0 1-1 2-1s3 0 3-1h5s1-1 1 0h0s1 1 2 0h5v1c-5 0-9 0-13 1v1 1c-1 1-4 4-6 4 0 1-3 4-4 4 0 1-2 3-3 3l-5 4v1h2l-4 2c-1 1-3 2-5 3h0 1c1-1 1 0 2-1 1 0 1 0 2-1h0c1-1 4 0 6 0 1 0 2-1 4-1 1 0 3-1 4-1v1l-10 4c-2 1-4 1-5 2v1c-3 1-6 3-8 3l-1-1c-4 1-7 2-11 3-1 0-2 1-3 0 2-4 6-8 9-12 2-3 4-6 6-8 1-2 2-3 3-4l3-3h2c1-1 2-1 2-1z" class="I"></path><path d="M137 145h0c3 1 5 0 7-1-2 2-6 4-10 5 1-2 2-3 3-4zm9 3h0c1-1 1-1 2-1 1-1 1-1 1 0 1-1 3-1 4-1v1c0 1-2 3-3 3-1-1-1-1-3 0l-2 1c0-1 1-1 2-2l-1-1z" class="J"></path><path d="M157 143h-2c-1-1-2 0-3 0v-1c4-2 7-4 11-5v1 1c-1 1-4 4-6 4z" class="Y"></path><path d="M146 148l1 1c-1 1-2 1-2 2-1 2-4 5-6 6h-1-1c-1 0-3 0-4 1 2-2 6-4 8-6 2-1 4-3 5-4z" class="V"></path><path d="M145 151l2-1c2-1 2-1 3 0l-5 4c-5 3-9 5-14 7 2-2 5-3 7-4h1c2-1 5-4 6-6zm-2 6c-1 1-3 2-5 3h0 1c1-1 1 0 2-1 1 0 1 0 2-1h0c1-1 4 0 6 0 1 0 2-1 4-1 1 0 3-1 4-1v1l-10 4c-2 1-4 1-5 2v1c-3 1-6 3-8 3l-1-1 3-1h0l-8 1 9-4h-4c3-2 7-4 10-5z" class="U"></path><defs><linearGradient id="CW" x1="137.98" y1="251.369" x2="159.513" y2="252.507" xlink:href="#B"><stop offset="0" stop-color="#353535"></stop><stop offset="1" stop-color="#666566"></stop></linearGradient></defs><path fill="url(#CW)" d="M162 206v1 3c-1 0-1 1-1 2v1l-1 1v1-1l1-1s1-1 1-2 0-1 1-2h0v1l-3 7c-2 5-2 10-4 14h-1v-3l-1-1c-2 8-4 18-4 27-1 5 0 11-1 16-1 4-2 9-3 13-2 4-4 8-4 12 0 1-1 3-1 3v4h-1c0-1-1-2-1-3-1-2-1-4-3-5 0-4-3-8-4-12 2 2 3 5 4 7v-9-4c1-3 1-6 2-8l3-14 1-3 3-10c0-2 1-3 1-4v-2c1-1 2-4 3-6v-1c1-2 2-3 3-4s1-3 2-4c0-2 1-3 2-5 2-3 3-6 6-9z"></path><path d="M154 227c1-2 3-7 2-8v1l-1-1c1 0 1-1 1-1 0-2 1-3 2-4v-1c1 1-1 4-1 5v-1c1 0 1-1 1-1 0-1 0-1 1-2v-1s0-1 1-1v-1s0-1 1-2v-1c1 0 1-1 1-2v1 3c-1 0-1 1-1 2v1l-1 1v1-1l1-1s1-1 1-2 0-1 1-2h0v1l-3 7c-2 5-2 10-4 14h-1v-3l-1-1z" class="K"></path><defs><linearGradient id="CX" x1="165.94" y1="238.152" x2="124.258" y2="248.292" xlink:href="#B"><stop offset="0" stop-color="#323232"></stop><stop offset="1" stop-color="#5a595a"></stop></linearGradient></defs><path fill="url(#CX)" d="M151 205c0 1 0 1 1 2 0-1 0-1 1-1h0c0 1-1 1-1 2-1 0-1 1-1 1 0 1 1 1 2 1h1l3-5h-1c0-1 1-2 2-3 0-1 1-1 1-2 1-1 2-1 2-2l4-5v1h0l-3 3c0 2-1 3-2 4 0 1-1 1-1 1s0 1-1 1v1 1h0l5-7c2-3 5-6 7-10h0c2-1 4-3 5-5 0-1 1-1 2-2l-8 13-1 1c-1 2-3 4-3 6h-1v1h0l-1 1v2 1l-1 1v-1c-3 3-4 6-6 9-1 2-2 3-2 5-1 1-1 3-2 4s-2 2-3 4v1c-1 2-2 5-3 6v2c0 1-1 2-1 4l-3 10-1 3-3 14c-1 2-1 5-2 8v4 9c-1-2-2-5-4-7-2-7-4-15-4-22-1-15 2-31 9-44 3-6 7-10 11-15h1c1-2 7-8 8-8 0 1-1 2-2 2-1 2-3 3-3 6l-2 2 1 1c1 0 2-2 3-2-1 1-3 2-3 3z"></path><path d="M145 208h1c-1 3-4 7-6 9h0v-1c1-2 3-6 5-8z" class="E"></path><path d="M152 224v-1c-1-1-1-2 0-3 0-1 1-2 1-3s1-1 1-1c0-1 1-2 1-2 0-2 1-2 1-4 3-4 4-7 7-11l4-4-2 3c-1 2-3 5-3 7v1c-3 3-4 6-6 9-1 2-2 3-2 5-1 1-1 3-2 4z" class="J"></path><defs><linearGradient id="CY" x1="158.207" y1="157.22" x2="162.681" y2="176.041" xlink:href="#B"><stop offset="0" stop-color="#141313"></stop><stop offset="1" stop-color="#3b3c3d"></stop></linearGradient></defs><path fill="url(#CY)" d="M178 154l7-3 2 1-6 5c1-1 2-1 2-2 1 0 2-1 2-1 1-1 1 0 2 0v1c-2 2-3 4-5 5-6 4-11 9-17 13-3 1-6 3-9 5l-7 2c-1 1-2 1-3 1-4 2-8 4-13 5h-3c-3 1-5 2-7 2l-11 3c-1 1-1 1-2 1l-6 3 4-8 2-4 2-3c1-1 2-2 2-3l3-4 2-4c1 1 2 0 3 0 4-1 7-2 11-3l1 1c2 0 5-2 8-3v-1c1-1 3-1 5-2l10-4h1 2l1 1 13-3-6 3v1c4-1 7-3 10-5h0z"></path><path d="M151 173h1v1l-1 1c-2 1-4 1-5 1l-1-1c1-1 5-1 6-2z" class="J"></path><path d="M161 172v-1c2-1 4-3 5-3s1-1 1-1v-1l7-5c1 0 1-1 3 0-1 1-2 1-3 2v1c-4 2-9 6-13 8z" class="B"></path><path d="M136 173c-1 2-6 3-8 4 0 0-1 0-1 1 3-1 6-3 9-3v1l-1 1c-1 0-2 0-3 1-4 1-8 3-13 4-2 0-5 1-8 2l-1-1 2-3c1-1 2-2 2-3h1c1 1 4-1 6-1h1 0c1-1 4-2 6-1 2 0 6-2 8-2z" class="L"></path><path d="M114 177h1c1 1 4-1 6-1h1l-2 1v1c-2 0-5 1-8 2 1-1 2-2 2-3z" class="I"></path><path d="M136 173c-1 2-6 3-8 4 0 0-1 0-1 1 0 0-9 3-10 3v-1c1 0 3-1 4-2h-1v-1l2-1h0c1-1 4-2 6-1 2 0 6-2 8-2z" class="N"></path><defs><linearGradient id="CZ" x1="153.151" y1="168.6" x2="156.73" y2="176.164" xlink:href="#B"><stop offset="0" stop-color="#1d1d1c"></stop><stop offset="1" stop-color="#616061"></stop></linearGradient></defs><path fill="url(#CZ)" d="M181 157c1-1 2-1 2-2 1 0 2-1 2-1 1-1 1 0 2 0v1c-2 2-3 4-5 5-6 4-11 9-17 13-3 1-6 3-9 5l-7 2c-1 1-2 1-3 1-4 2-8 4-13 5h-3c-3 1-5 2-7 2h-1c1-1 2-2 3-2h0c3-1 6-2 8-3l2 1c7-4 15-6 22-10 1-1 3-1 4-2 4-2 9-6 13-8v-1c1-1 2-1 3-2l4-4z"></path><defs><linearGradient id="Ca" x1="120.251" y1="181.931" x2="121.061" y2="191.163" xlink:href="#B"><stop offset="0" stop-color="#222321"></stop><stop offset="1" stop-color="#38363a"></stop></linearGradient></defs><path fill="url(#Ca)" d="M134 178h2 0c0 1 0 1-1 2 1 0 1 0 1-1h3 0 1c0 1-1 1-2 1-2 1-3 2-5 3s-5 2-8 3h0c-1 0-2 1-3 2h1l-11 3c-1 1-1 1-2 1l-6 3 4-8 2-4 1 1v1h0c5-2 11-2 17-4l6-3z"></path><path d="M134 178h2 0c0 1 0 1-1 2 1 0 1 0 1-1h3 0 1c0 1-1 1-2 1-2-1-6 2-9 2 2-2 4-2 6-3l-1-1z" class="K"></path><path d="M125 186h0c-1 0-2 1-3 2h1l-11 3-1-1c4-2 10-3 14-4z" class="J"></path><path d="M128 181v1c-5 2-9 3-14 5l-5 2c-1 0-1-1-1-2l2-4 1 1v1h0c5-2 11-2 17-4z" class="N"></path><path d="M178 154h0l-8 6-9 6c-7 4-16 8-24 11-1 1-2 1-3 0h1l1-1v-1c-3 0-6 2-9 3 0-1 1-1 1-1 2-1 7-2 8-4v-1h0 0v-1h0l8-3c1 0 2-1 3-1 0-1 3-2 4-2s1-1 2-1l1-1v2l9-5h0 1 4v-1c4-1 7-3 10-5z" class="F"></path><path d="M154 165l9-5h0 1 4c-2 2-5 3-7 4-3 2-6 4-9 5l-1-2c1-1 2-1 3-2z" class="K"></path><path d="M147 167c0-1 3-2 4-2s1-1 2-1l1-1v2c-1 1-2 1-3 2l1 2c-6 2-11 6-17 8l1-1v-1c-3 0-6 2-9 3 0-1 1-1 1-1 2-1 7-2 8-4v-1h0 0v-1h0l8-3c1 0 2-1 3-1z" class="B"></path><path d="M147 167c0-1 3-2 4-2s1-1 2-1l1-1v2c-1 1-2 1-3 2s-3 1-4 2c-4 1-7 2-11 3h0 0v-1h0l8-3c1 0 2-1 3-1z" class="J"></path><path d="M161 158l13-3-6 3v1 1h-4-1 0l-9 5v-2l-1 1c-1 0-1 1-2 1s-4 1-4 2c-1 0-2 1-3 1l-8 3h0v1h0 0v1c-2 0-6 2-8 2-2-1-5 0-6 1h0-1c-2 0-5 2-6 1h-1l3-4 2-4c1 1 2 0 3 0 4-1 7-2 11-3l1 1c2 0 5-2 8-3v-1c1-1 3-1 5-2l10-4h1 2l1 1z" class="V"></path><path d="M163 160c2-1 3-2 5-2v1 1h-4-1 0z" class="S"></path><path d="M158 157h2l1 1c-2 1-4 2-6 2-1 1-4 1-6 2l1-1 8-4z" class="B"></path><path d="M142 164l8-3-1 1c-1 1-3 2-5 3-2 0-3 1-4 2-2 1-4 1-6 1v-1h0c2 0 5-2 8-3z" class="K"></path><path d="M157 157h1l-8 4-8 3v-1c1-1 3-1 5-2l10-4z" class="X"></path><path d="M122 169c4-1 7-2 11-3l1 1h0v1c-2 1-6 2-8 4l21-5c-1 0-2 1-3 1l-8 3h0v1h0 0v1c-2 0-6 2-8 2-2-1-5 0-6 1h0-1c-2 0-5 2-6 1h-1l3-4 2-4c1 1 2 0 3 0z" class="S"></path><path d="M117 173l2-4c1 1 2 0 3 0l1 1c-2 1-4 3-6 3z" class="J"></path><path d="M122 169c4-1 7-2 11-3l1 1h0l-11 3-1-1z" class="E"></path><path d="M121 176c5-3 10-5 15-5h0v1h0 0v1c-2 0-6 2-8 2-2-1-5 0-6 1h0-1z" class="K"></path><defs><linearGradient id="Cb" x1="109.158" y1="333.629" x2="130.707" y2="325.147" xlink:href="#B"><stop offset="0" stop-color="#1a1a1a"></stop><stop offset="1" stop-color="#4d4d4d"></stop></linearGradient></defs><path fill="url(#Cb)" d="M98 288l2-13c2-15 8-28 15-41 2-4 6-9 10-12-1 0-1 1-1 1h1c0 1-1 3-2 4-1 2-1 4-2 6-2 3-3 7-5 9l-2 4h0c-1 5-3 9-3 14v22 8c0 2 0 4 1 6v-12-8c0 1 0 3 1 3 0 2 1 2 1 4 1 1 1 2 1 2h0v-8-3c0-1 0-1 1-3 0 7 0 14 1 20 0 3 1 6 1 9 1 1 1 2 1 3 1 2 1 5 2 7s2 4 2 6c2 4 4 7 5 11 3 5 6 10 9 16 0 1 2 4 1 5h0c-1-1-2-3-2-4h0-1 1v1 1c1 0 1 1 1 1v1l1 1h0v2c3 7 3 13 5 20 1 4 4 9 7 13 0 1 1 1 1 2l-1 1c-2-5-4-10-8-14-5-7-13-13-18-20-5-4-8-11-12-16l-1-1c0-1 0-1-1-1h0c-1-1-2-4-3-5 0-1 0-3-1-4 1-1 1-1 0-1v-1c0-2-1-3-2-5-1-1-2-3-2-4-2-5-4-12-4-17v-10z"></path><path d="M125 338c2 3 2 7 5 10 0 1 1 2 1 2-1 0-2-1-3-3-1-1-2-3-2-4 0-2 0-3-1-5z" class="J"></path><path d="M117 320c1 1 3 6 3 7 1 2 2 5 3 7s1 3 2 4c1 2 1 3 1 5-2-3-2-5-3-7l-3-9c-1-2-3-5-3-7z" class="E"></path><path d="M118 311c0-2-2-6 0-8v1c1 2 1 5 2 7 0 2 1 5 0 6 0 1 1 2 0 3v-1s0-1-1-1v-3c-1-1-1-3-1-4z" class="T"></path><path d="M128 347c1 2 2 3 3 3l8 14h-1l-10-15v-2z" class="E"></path><path d="M127 330l-1 1c0-1 0-2-1-3l-1-1-2-5c0-1-1-2-1-3s0-2-1-4h0 1v-1c1 2 1 4 2 6l4 10z" class="F"></path><path d="M106 326l1-1h1v-1 1l3 6h-1 0c0 2 2 5 3 6l6 9c1 2 2 4 4 5 0 1 0 1 1 2-5-4-8-11-12-16l-1-1c0-1 0-1-1-1h0c-1-1-2-4-3-5 0-1 0-3-1-4z" class="B"></path><path d="M123 320v-2c2 3 3 7 5 9 3 5 6 10 9 16 0 1 2 4 1 5h0c-1-1-2-3-2-4h0-1 1v1 1c1 0 1 1 1 1v1l1 1h0v2c-1-1-1-2-1-2-1-1-1-3-2-3v-1c-1-3-3-6-4-9-1-2-2-5-4-6l-4-10z" class="J"></path><defs><linearGradient id="Cc" x1="112.672" y1="286.111" x2="118.458" y2="311.447" xlink:href="#B"><stop offset="0" stop-color="#2f2f2f"></stop><stop offset="1" stop-color="#505050"></stop></linearGradient></defs><path fill="url(#Cc)" d="M114 283c1 1 1 2 1 2h0v-8-3c0-1 0-1 1-3 0 7 0 14 1 20 0 3 1 6 1 9 1 1 1 2 1 3l-1 1v-1c-2 2 0 6 0 8-1 3 0 5 1 8l-1-1c0-1-1-2-1-3v-2c0-1-1-1-1-2h0v-1s0-1-1-1v-1c0-1-1-2-1-3h0v-1c-1-1-1-4-1-5 1 0 1 0 1-1 0-3-1-8 0-10 0-2-1-3 0-5z"></path><path d="M98 288l2-13c2-15 8-28 15-41 2-4 6-9 10-12-1 0-1 1-1 1h1c0 1-1 3-2 4-1 2-1 4-2 6-2 3-3 7-5 9l-2 4h0c-1 5-3 9-3 14v22 8c0 2 0 4 1 6v5c1 1 1 2 1 3 0 5 3 11 4 16 0 2 2 5 3 7l3 9c1 2 1 4 3 7 0 1 1 3 2 4v2l-8-13h0c-1-1-1-2-2-3v-1h0c-1-1-2-3-2-4v-1h-1c-1 0-1 0-2-1 0-1 0-2-1-2h-1c-2-5-4-10-5-15-1-1-1-3-1-5h0c-1-3-2-4-3-6l-3-3-1 3v-10z" class="d"></path><path d="M115 241c0 1 1 1 1 1l-2 4h0 0-1l2-5z" class="O"></path><path d="M113 246h1 0c-1 5-3 9-3 14l-1 2h0v-2l-1 2h0v-1l4-15z" class="c"></path><path d="M124 223h1c0 1-1 3-2 4-1 2-1 4-2 6-2 3-3 7-5 9 0 0-1 0-1-1 2-6 5-12 9-18z" class="Q"></path><defs><linearGradient id="Cd" x1="120.802" y1="299.031" x2="107.846" y2="299.65" xlink:href="#B"><stop offset="0" stop-color="#555455"></stop><stop offset="1" stop-color="#757576"></stop></linearGradient></defs><path fill="url(#Cd)" d="M109 261v1h0l1-2v2h0l1-2v22 8c0 2 0 4 1 6v5c1 1 1 2 1 3 0 5 3 11 4 16 0 2 2 5 3 7l3 9c1 2 1 4 3 7 0 1 1 3 2 4v2l-8-13c-12-23-17-50-11-75z"></path><defs><linearGradient id="Ce" x1="117.575" y1="186.108" x2="189.377" y2="375.978" xlink:href="#B"><stop offset="0" stop-color="#2b2b2b"></stop><stop offset="1" stop-color="#474646"></stop></linearGradient></defs><path fill="url(#Ce)" d="M146 181c1 0 2 0 3-1l7-2c3-2 6-4 9-5 6-4 11-9 17-13 0 1-1 2-1 2-1 2-3 4-4 5l1 1h0c-1 1-1 1-1 2s-2 4-3 4h0c-3 3-6 6-9 8-1 2-4 3-5 5-1 1-2 2-3 2-3 2-5 4-7 5-1 0-2 1-2 1-3 2-5 4-7 6l-3 3c-1 1-3 2-4 4l-2 2-1 3c0 2-1 2-2 3-2 2-3 4-4 6-4 3-8 8-10 12-7 13-13 26-15 41l-2 13v10c0 5 2 12 4 17 0 1 1 3 2 4 1 2 2 3 2 5v1c1 0 1 0 0 1 1 1 1 3 1 4 1 1 2 4 3 5 1 2 1 4 2 6l6 13c4 8 10 15 17 21 1 1 3 4 4 4h1 0c0-1-2-2-2-3h0c5 3 12 9 13 15l-1 1h-1l-16-9v1h-2c0 1 0 1 1 1l-1 1v1h0 0l-6-8v-2a57.31 57.31 0 0 0-11-11c-4-6-8-12-12-19h1c1 0 1 2 2 3h0 0v1l1 1h0v1h1c2 1 4 5 4 6v1c0 1 1 2 2 3v-1h1c1-1 1-1 1-2s0-1 1-1v-1-1h0c-1-1-1-1-1-2h-1v-1l-1-2-1-1v-1l-1-1v-1l-1-1-1-1c0-1 0-1-1-2h0v-2l-2-4h0c-6-13-9-26-11-40-2-10-2-20-1-30 1-4 2-9 3-13 1-2 2-4 2-6 0-1 0-1 1-1v-1h-1c0 1 0 1-1 2h0-1c-1 3-3 6-4 9v1c-1-1 1-5 1-7-1-1 1-3 1-4l2-4c0-1 1-3 2-4h-1-1l6-11c0-2 3-4 4-5-2-2-2-2-4-2h0c-1 2-3 3-5 4-2 2-4 4-6 7-1 2-1 4-2 6l-1 5c-1 1-1 3-2 4 0 0-1 0-1 1 1-7 4-15 6-22 1 0 1 0 2-1v-1c0-1 1-2 1-3h0-1c1-1 1-2 0-3 0 0 1-2 1-3 2-7 5-14 9-21l6-3c1 0 1 0 2-1l11-3c2 0 4-1 7-2h3c5-1 9-3 13-5z"></path><path d="M103 222c2-1 4-3 7-4-1 1-2 1-3 2h2c0 1-1 2-1 3l-1 1c-2-2-2-2-4-2z" class="Y"></path><path d="M111 219h1s0 1-1 2l1-1c1 0 1-1 2-1h1c-3 2-5 4-7 7l-2 3h0c0-1 1-2 1-4 1-2 3-4 4-6z" class="N"></path><path d="M174 174h-1 0l-1-1 5-6 1 1h0c-1 1-1 1-1 2s-2 4-3 4h0z" class="L"></path><path d="M106 338v-1c2 2 8 11 8 13 0 1 0 1-1 2-1-3-3-9-5-10l-2-4z" class="T"></path><path d="M107 224l1-1c-1 1-1 2-2 3-1 0-1 1-2 2 0 1 1 1 0 1-1 1-1 3-2 4-1 2-2 5-3 7h-1-1l6-11c0-2 3-4 4-5z" class="I"></path><path d="M125 377c1 2 3 3 4 4 2 0 3 1 4 2v1h-2c0 1 0 1 1 1l-1 1v1h0 0l-6-8v-2z" class="S"></path><path d="M132 210l-1-1 1-1c2-2 13-14 16-14v1c-3 2-5 4-7 6l-3 3c-1 1-3 2-4 4l-2 2z" class="K"></path><path d="M112 341c-1-1-2-2-2-3s0-1-1-1c0-1-1-3-1-4-1-1-1-2-2-3 0-2-1-3-2-5s-1-4-2-6c0-1-1-2-1-3v-2h-1s1-1 0-1c0-2 0-3-1-4v-2-1l-1-1v-3h0v-2-3c1-2-1-7 0-9v10c0 5 2 12 4 17 0 1 1 3 2 4 1 2 2 3 2 5v1c1 0 1 0 0 1 1 1 1 3 1 4 1 1 2 4 3 5 1 2 1 4 2 6z" class="L"></path><path d="M146 181l1 1c-1 1-3 2-5 4-2 1-4 2-6 4-5 3-9 6-13 10-5 5-9 10-14 15l-7 7h1c-1 2-3 3-5 4-2 2-4 4-6 7-1 2-1 4-2 6l-1 5c-1 1-1 3-2 4 0 0-1 0-1 1 1-7 4-15 6-22 1 0 1 0 2-1v-1c0-1 1-2 1-3h0-1c1-1 1-2 0-3 0 0 1-2 1-3 2-7 5-14 9-21l6-3c1 0 1 0 2-1l11-3c2 0 4-1 7-2h3c5-1 9-3 13-5z" class="Y"></path><path d="M104 195l6-3-3 3c-2 4-4 7-6 11-2 5-4 11-6 16h0-1c1-1 1-2 0-3 0 0 1-2 1-3 2-7 5-14 9-21z" class="B"></path></svg>