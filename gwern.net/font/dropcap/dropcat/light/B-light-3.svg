<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="56 42 548 632"><!--oldViewBox="0 0 652 752"--><style>.B{fill:#323132}.C{fill:#686767}.D{fill:#777677}.E{fill:#474746}.F{fill:#393839}.G{fill:#9b9a9a}.H{fill:#727172}.I{fill:#bebcbc}.J{fill:#868586}.K{fill:#a9a8a9}.L{fill:#7c7c7c}.M{fill:#d9d7d8}.N{fill:#efeeef}.O{fill:#918f90}.P{fill:#b6b4b5}.Q{fill:#c9c8c9}.R{fill:#4d4d4d}.S{fill:#545354}.T{fill:#5d5d5d}.U{fill:#282829}.V{fill:#afaeaf}.W{fill:#5a595a}.X{fill:#e3e2e3}.Y{fill:#2c2b2c}.Z{fill:#818081}.a{fill:#dddcde}.b{fill:#a1a0a1}.c{fill:#636363}.d{fill:#959495}.e{fill:#d9d8da}.f{fill:#c2c2c3}.g{fill:#1b1a1b}.h{fill:#6d6c6d}.i{fill:#3f4040}.j{fill:#4a494a}.k{fill:#575757}.l{fill:#605f60}.m{fill:#3e3d3d}.n{fill:#f9f8f8}.o{fill:#e9e7e9}.p{fill:#cfcecf}.q{fill:#fff}.r{fill:#8b8a8b}.s{fill:#69676a}.t{fill:#a5a4a4}.u{fill:#434244}.v{fill:#252424}.w{fill:#222122}.x{fill:#1f1e1e}.y{fill:#f4f3f4}.z{fill:#504e50}.AA{fill:#141414}.AB{fill:#0c0c0b}.AC{fill:#515152}.AD{fill:#d8dad6}</style><path d="M87 199l1 2s-1 1-1 2h0c0-1 0-1-1-2 0 0 1-1 1-2z" class="g"></path><path d="M155 404c1 1 1 1 2 1 0 1 0 1 1 1-1 1-2 1-3 1h-1l1-3z" class="z"></path><path d="M266 620l3 2-1 2-3-2c0-1 1-1 1-2z" class="i"></path><path d="M152 390c0-2 1-3 2-5l1 2-3 3z" class="N"></path><path d="M96 620l1 1c-1 2-1 4-1 5l-2 3c0-3 0-6 2-9z" class="U"></path><path d="M244 181l3 6h-1c0-1 0-2-1-2h-1 0c0-1-1-2-1-3l1-1z" class="C"></path><path d="M143 364l3-3h0l-1 2c0 1-1 2-2 3-1 0-2 1-2 1-1 1-1 1-2 1 0-1 3-3 3-3l1-1z" class="b"></path><path d="M181 489v1l1-1c1 0 1 1 1 1v1c1 0 1 1 1 2l-1 2h-1v-5c-1 1-2 2-4 3 1-1 2-2 3-4z" class="c"></path><path d="M244 618h0l-3-4c1 0 2 1 3 1l2 1h0v1 1c1 1 1 1 1 2l1 1h0l-3-3h-1z" class="p"></path><path d="M174 429c1 1 1 2 1 3-1 0-1 0-1 1 0 0-1 2-1 3s-1 2-2 3h-3l1-1h0c1-1 2-1 3-2v-1h0c1-1 1-2 1-4 0 0 1-1 1-2z" class="AB"></path><path d="M172 435c-1 0-2 1-3 1h0l1-1v-1c0-2 0-3 1-4 1 1 1 1 2 1 0 2 0 3-1 4h0z" class="E"></path><path d="M96 260h0v3c0 1-1 1-1 2l-1 2-1 3v2l-1 2v2h-1c0-1 0-2 1-3v-2l1-3 2-4v-2h0c0-1 1-1 1-2z" class="n"></path><path d="M399 625c0-1 2-2 3-3 0-1 0-1 1-2 0-1 1-2 2-3l1-1c0 1 0 1-1 2 0 1 0 2 1 2l-3 5h-2c0-1-1 0-2 0z" class="X"></path><path d="M231 157l8 15c-1 0-1-1-2-1-1-1-1-2-1-3h-1v-1c-1-2-2-4-3-5l-1-3c-1-1-1-1 0-2z" class="m"></path><path d="M64 562v-1c1 0 1 1 2 1l1 2c0 1 0 1 1 2v1c-2 1-3 3-4 5 1-1 3-1 5-1l1 1c-3-1-5 1-7 3h-1c1-1 1-3 1-5 1-1 3-3 4-5l-3-3z"></path><path d="M78 158h-1 0l-4-1-6-1h-4l-11-1c1-1 7-1 9 0l8 1h1 4c0 1 1 1 2 1h2v1z" class="X"></path><path d="M496 605h0c0-1 0 0 1-1v3c0 2 0 4 1 5 1 3 1 4 4 5 2 1 5 2 7 1l1-1h1v1c-1 0-1 0-1 1h-1-1-1c-2 0-7-1-8-3-3-3-4-7-3-11zM86 201c1 1 1 1 1 2h0l2 1c0 1 0 1-1 2v2c-1 0-2-1-3 0h0-1l-1-1 3-6z" class="AA"></path><path d="M133 546c-4-1-6-4-9-6l-7-6c0-1 0-1 1-2h0c1 1 1 2 1 3h1 0l6 5 1 1c2 2 4 3 6 5h0z" class="u"></path><path d="M244 185h1c1 0 1 1 1 2h1c2 3 4 6 5 10h-2l-2-4-4-8z" class="D"></path><path d="M178 631c2 0 4 0 6 1 1 1 1 1 1 2l-2 1h-2c-2 0-3-1-3-1-1-2 0-2 0-3z" class="AB"></path><path d="M145 355c1 0 1-1 2-2v2h0v1c1-1 1-1 1-2h1v1l-3 6-3 3v-2c1 0 1 0 1-1 0 0 1-1 1-2-1 0-2 1-2 1h-1l1-1c1-2 1-3 2-4zm13 43c1 0 2-2 2-3 1 1 1 1 0 2v1c-1 1-1 2-2 3h1v1h1 1 0l1 1c-1 1-1 1-1 2-1 1-2 1-3 1s-1 0-1-1c-1 0-1 0-2-1l3-6z" class="K"></path><path d="M161 402l1 1c-1 1-1 1-1 2-1 1-2 1-3 1s-1 0-1-1c1-1 2-2 4-3z" class="F"></path><path d="M151 379c2-1 3-2 3-3 0-2 1-3 2-5h0c0 1 0 2-1 3h1c0 1 1 1 1 2h1 0 0v2l-1 1c-1 1-2 1-2 2l-4 5c1-3 3-6 4-9 0 1-1 1-1 1-1 2-2 3-4 3h0l1-2z" class="G"></path><path d="M223 630v-1-2h-2l2-2h0 2 0 1c1 1 2 2 2 3v3l-1 2c-2-1-2-3-4-3z" class="y"></path><path d="M83 207l1 1h1v2 1l-2-2h-7c-6 0-12 1-18 2-3 1-7 1-10 2h0l3-1c7-2 15-3 22-4h10v-1z" class="w"></path><defs><linearGradient id="A" x1="147.065" y1="319.019" x2="140.021" y2="321.356" xlink:href="#B"><stop offset="0" stop-color="#272926"></stop><stop offset="1" stop-color="#3d3a3d"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M145 316h2v2c-1 0-1 1-1 1v1c-2 3-3 5-6 6h-1c2-2 3-5 3-8 1 0 2-2 3-2z"></path><path d="M416 629c6-2 15 0 21 1l-2 1c-5-1-11-1-17-1-2 0-4 0-6 1h-2-1-2v-1l5-1h1l1-1c1 1 1 1 2 1z" class="j"></path><path d="M149 323v1h1c0 2 0 3-1 5h0-1c-1 1-2 3-2 5-1 1-2 2-2 3-1 1-3 2-4 3 0 0 0-1 1-1 1-1 1-2 2-3 0-1 1-2 1-3s1-2 1-3c1-2 2-5 4-7z" class="s"></path><path d="M112 238h1c0-1 0-1 1-1-1 2 0 6 1 9l2 24c0 1 0 1-1 2v-4l-4-30z" class="W"></path><path d="M116 268v4c1-1 1-1 1-2v7c1 3 1 26 0 27l-1-36z" class="D"></path><path d="M148 318c1 1 1 3 1 4v1c-2 2-3 5-4 7 0 1-1 2-1 3-1 1-2 1-3 2h-1c1-2 3-3 3-5h0-1l5-10c0-1 0-1 1-2z" class="U"></path><path d="M418 630c6 0 12 0 17 1l1 1h-4-3-4-1c-1 0-2 1-4 2-1-2-3-2-5-3v-1h3z" class="N"></path><path d="M544 581c1 0 2 1 3 1h1c1 1 2 0 2 0 1 0 1 1 2 1h-3-1c2 2 5 4 7 6 5 4 7 10 9 16 1 2 2 5 1 7h0c-1-1-2-6-2-8-1-1-1-3-2-4v-1c-1-4-6-11-10-12-1-2-3-3-4-4h-4c0-1 0-1 1-2z" class="E"></path><path d="M118 143c-1-5-2-9-4-14 0-3-7-20-6-22 1 4 2 7 3 10l6 16c1 3 1 6 3 9h0l-2 1z" class="O"></path><path d="M148 346h0c0 2 0 2-1 4 0 1-1 3-2 4v1c-1 1-1 2-2 4l-2-2-2 2h-1v-1l1-1-1-2 2-2h0l1-1c1-1 2-1 3-1 2-1 2-3 3-4l1-1z" class="f"></path><path d="M140 353l2 1c0 1-2 2-2 3h-1l-1-2 2-2zm1 4l3-4h1v1 1c-1 1-1 2-2 4l-2-2z" class="N"></path><path d="M244 615c1-1 1-1 2-1 6 3 14 8 17 15 1 1 1 2 1 3 0 2 0 2 1 3l-1 1c-1-2-1-3-1-5-2-7-10-11-17-15h0l-2-1z"></path><path d="M249 200h1c0-3-2-5-2-7l2 4h2c2 4 5 9 6 14h-1l-2-5h-1-1s-1-2-2-2c0-1-1-2-2-3v-1z" class="T"></path><path d="M249 200h1c0-3-2-5-2-7l2 4c1 3 3 6 4 9h-1s-1-2-2-2c0-1-1-2-2-3v-1z" class="m"></path><path d="M104 226l1 4h0c1-1 1-1 1-3v-3s1 0 1 1l2 10c-2-1-3-1-4-2-1 1-1 1-1 2-3 8-4 15-7 23l-1 2h0l8-27v-4h-1v-3c0-1 1-1 1-1v1z" class="B"></path><path d="M103 226c0-1 1-1 1-1v1 3h-1v-3z" class="U"></path><path d="M272 626c1 0 2 0 2-1l2 1 7 3c1 1 1 1 3 2h0 0c5 3 10 8 13 14 1 1 2 3 2 4h-1c-2-7-8-12-14-16-4-2-9-4-14-7z" class="G"></path><path d="M551 587c4 1 9 8 10 12v1c1 1 1 3 2 4 0 2 1 7 2 8 0 1 0 1-1 2 0-3-2-6-3-8v-2c0-1-1-1-1-2h0c0-2-1-3-2-5h0l-3-5-1-1c-1-2-2-2-3-4z" class="f"></path><path d="M107 155c0-1 1-2 2-4-1-3-3-6-5-9-2-2-4-5-7-8-4-4-9-8-13-12h0c10 8 20 16 25 29 2-2 3-4 5-5 0 2 1 4 1 6l-1 1c0-1-1-2-1-3l-1 1c-2 2-4 3-5 4z" class="k"></path><path d="M166 410c1-1 2-5 3-5h0v1l-2 3v1 1c0 1 0 2 1 3 2-2 3-4 4-6 0 0 0 1 1 1-1 2-3 3-3 5l1 1v1h0l1-2 1 1h0 0c0 1-1 1-1 2h0l-1 2c0 1-1 2-3 3h0c0-2 1-4 2-6-1 1-2 1-4 2h0l1-2h-1l-1 1v1c0 1 0 1-1 1h-1l3-9z" class="F"></path><path d="M158 378v-2l2 3v2 1 1c0 2-1 4-3 5h0c-1 1-1 2-1 3-1 1-1 2-2 3h-1c0-1-1-2-2-2l-1-1 2-1 3-3-1-2 2-3-1-1c0-1 1-1 2-2l1-1z" class="d"></path><path d="M151 392c2-1 3-3 5-4h1c-1 1-1 2-1 3-1 1-1 2-2 3h-1c0-1-1-2-2-2z" class="N"></path><path d="M158 378v-2l2 3v2h-1c-1 1-1 2-1 4l-3 2-1-2 2-3-1-1c0-1 1-1 2-2l1-1z" class="M"></path><path d="M158 378v-2l2 3v2h-1c-1 1-1 2-1 4l-1-1v-4-1l1-1z" class="I"></path><path d="M158 378v-2l2 3v2h-1v-1h-1v-2z" class="b"></path><path d="M402 627h0c3 0 5-2 7-3 3-2 7-4 10-5 2-1 4-1 5 0 1 0 1 0 1 1-1 3-7 7-9 8h-1l1 1c-1 0-1 0-2-1l1-1c0-1 0-1 1-2h0c2-2 4-2 5-5h-1c-4 1-7 3-11 5-2 1-3 2-5 3-1 0-2 1-3 1s-1 1-2 1c-3 0-6 1-9 1l1-1c3 0 8-2 11-3z" class="h"></path><path d="M420 620l3-1 1 1c-1 3-3 4-5 5s-3 2-4 2c0-1 0-1 1-2h0c2-2 4-2 5-5h-1z" class="Q"></path><path d="M405 614l2-1 1 1h-1v2h-1l-1 1c-1 1-2 2-2 3-1 1-1 1-1 2-1 1-3 2-3 3s0 2-1 2l-1 1c1 0 1 0 2-1h1 1 1c-3 1-8 3-11 3 4-3 6-9 9-12 1-2 4-3 5-4z" class="y"></path><path d="M420 620h1c-1 3-3 3-5 5h0c-1 1-1 1-1 2l-1 1-1 1h-1l-5 1h-2-2s-1 0-2-1c1 0 2-1 3-1 2-1 3-2 5-3 4-2 7-4 11-5z" class="n"></path><path d="M416 625h0c-1 1-1 1-1 2l-1 1-1 1h-1v-1h-2l-1-1 3-2 1 1 3-1z" class="V"></path><path d="M408 627h1 0l1 1h2v1l-5 1h-2-2s-1 0-2-1c1 0 2-1 3-1s3-1 4-1z" class="t"></path><path d="M408 627h1c-1 1-2 2-3 2l-1 1h-2s-1 0-2-1c1 0 2-1 3-1s3-1 4-1z" class="L"></path><path d="M597 452h0c2-1 3-2 4-3l-1-1h-1l-1 2h0v-3c1 0 1-1 2-1 2 1 2 2 3 3 0 1 0 1-1 2-1 3-4 4-6 6l-13 11v-1h0l3-3 3-2c0-1 1-2 1-3-1 1-2 1-3 1 3-2 6-5 8-7l2-1z" class="r"></path><path d="M595 453c1 1 1 1 1 2l-1 1v1c-2 1-4 3-6 5 0-1 1-2 1-3-1 1-2 1-3 1 3-2 6-5 8-7z" class="Q"></path><path d="M174 408l2 1c0 1-1 2-1 3 0 3-1 5-2 8l1 1 1 1c-1 1-2 3-2 4l-2 3c-1 1-2 2-4 3h0v-2l1-1 3-7-6 5 3-5h0c2-1 3-2 3-3l1-2h0c0-1 1-1 1-2h0 0l-1-1-1 2h0v-1l-1-1c0-2 2-3 3-5l1-1z" class="x"></path><path d="M173 420l1 1-2 1c0-1 0-1 1-2z" class="g"></path><path d="M174 421l1 1c-1 1-2 3-2 4l-2 3c-1 1-2 2-4 3h0v-2l1-1 2-2h0c1-2 2-3 2-5l2-1z" class="U"></path><path d="M75 501l3-3v1c-5 5-8 10-10 17 0 1-1 3-1 5v1c1 2 1 4 2 5-1 3-1 5 0 8 1-6 3-13 7-17 2-1 6-5 7-5-1 2-4 3-5 6-2 0-4 4-5 6-2 3-2 6-3 9 0 3 3 8 5 11 1-1 1-2 1-3v4h0c1 0 1 1 2 1v1s1 2 1 3l-2-1c0-1-3-5-4-7-2-2-3-4-4-7l-3-9c0-9 3-19 9-26z" class="Y"></path><path d="M117 236v-2c1 2 2 3 2 4h-1c-1 2 0 5 1 7 0 2 1 4 1 5l5 33 4 24c0 4 0 7 1 10-1 0-1-2-1-3-2-5-2-11-3-16l-6-42c-1-2-4-16-4-16 1-2 1-3 1-4z" class="O"></path><path d="M119 530c3 2 5 4 8 5l12 9h1v1h-1 0c0 1-3 2-4 2-1-1-1-1-2-1-2-2-4-3-6-5l-1-1-6-5h0-1c0-1 0-2-1-3h0 0l-1-1h1 0l1-1z" class="T"></path><path d="M120 535c1 0 1-1 2-1 2 1 5 3 7 5l-3 1-6-5z" class="N"></path><path d="M129 539l6 4c1 1 3 2 4 2h0c0 1-3 2-4 2-1-1-1-1-2-1-2-2-4-3-6-5l-1-1 3-1z" class="y"></path><path d="M129 539l6 4s-1 1-2 0c-1 0-2-2-4-2-1-1-1-1-2 0l-1-1 3-1z" class="a"></path><path d="M118 220h0c1 1 1 1 2 1h0c1 0 1 0 2 1l-1 1h0c-1 1-1 1-2 1v1 2l2 3c0 1 0 2 1 3l2 3h1l1 1h0l1 2h-2c-1 0-2 1-3 1s-1 0-1 1l3 9h-1l-1-4c-1-2-2-5-3-8 0-1-1-2-2-4h0c0-2-1-4-2-6-1-1-1-2-2-2l1-1h2 0v-1l1-1c0-1 0-2 1-3z" class="AB"></path><path d="M125 239c-1-1-2-2-2-3s1 0 1 0h1l1 1h0l1 2h-2z" class="Y"></path><path d="M118 220h0c1 1 1 1 2 1h0c1 0 1 0 2 1l-1 1h0c-1 1-1 1-2 1v1 2l2 3c0 1 0 2 1 3h-1v-1c0-1-1-3-2-4h-2c-1-1-1-1-2-1v1c-1-1-1-2-2-2l1-1h2 0v-1l1-1c0-1 0-2 1-3z" class="v"></path><path d="M118 220h0c1 1 1 1 2 1h0c1 0 1 0 2 1l-1 1h0c-1 1-1 1-2 1v1 2c-1-1-1-1-1-2-1 0-1-1-1-2h0c0-1 0-2 1-3z" class="H"></path><path d="M120 221c1 0 1 0 2 1l-1 1h-1v-2z" class="r"></path><path d="M237 171c1 0 1 1 2 1 2 2 4 6 5 9l-1 1c0 1 1 2 1 3h0l4 8c0 2 2 4 2 7h-1v1c0-1-1-2-2-2v-1c-2-1-3-4-4-6v-1l-1-1c0-1 0-2-1-3l-1 1c0-1-1-2-1-3h0c1-2 0-2 0-4h-1c1 0 1-1 1-1 0-1 0-1-1-2l-2-5c1 0 1 0 1-1h0v-1z" class="R"></path><path d="M243 191l2 2h0c0-1-1-3-2-4v-1c1 1 3 4 3 5v1l3 6v1c0-1-1-2-2-2v-1c-2-1-3-4-4-6v-1z" class="F"></path><path d="M85 208h0c1-1 2 0 3 0v3l1 2c1-1 2-2 3-2l-1 1c0 2 1 4 0 5h0l-2-1-1 2c1 0 1 0 1 1-3-1-10 4-12 6l-33 24v-1c5-4 11-8 17-13 7-5 14-11 22-15l2-2-4 1c-5 0-22 9-24 9 6-4 13-7 20-9 3-1 6-2 9-4 0-1-1-2-1-3v-1-1-2z" class="Y"></path><path d="M81 219c1-1 5-2 6-3l1 1c-1 2-3 2-5 3l2-2-4 1z" class="n"></path><path d="M85 208h0c1-1 2 0 3 0v3l1 2c1-1 2-2 3-2l-1 1c0 2 1 4 0 5h0c-1-1-2-3-3-3-1-2-2-2-3-4v-2z" class="e"></path><path d="M89 213c1-1 2-2 3-2l-1 1v1h0-2z" class="X"></path><path d="M167 382h1 1c0 2-2 5-3 7l-2 4c-1-1-1-1-1-2h0l-2 3-1 1c0 1-1 3-2 3h0c-1 0-1 1-2 1 0-1 0-2 1-3h-1c-1 0-1 0-2-1l-2 2v-1l2-2c1-1 1-2 2-3 0-1 0-2 1-3h0c2-1 3-3 3-5l1 1c0-1 1-1 1-2l1 1 2-1 1 1 1-1z" class="I"></path><path d="M167 382h1 1c0 2-2 5-3 7l-2 4c-1-1-1-1-1-2 1 0 1-1 2-1v-1c1-1 2-2 2-3l-2-2 1-1 1-1z" class="Q"></path><path d="M165 382l1 1-1 1-3 6-1 1c0 1 0 1-1 1l1-2-1-1 3-6 2-1z" class="V"></path><path d="M162 382l1 1-3 6 1 1-1 2-1 1c-1 1-1 2-2 3h0-1c-1 0-1 0-2-1l-2 2v-1l2-2c1-1 1-2 2-3 0-1 0-2 1-3h0c2-1 3-3 3-5l1 1c0-1 1-1 1-2z" class="L"></path><path d="M160 389l1 1-1 2-1 1-1-1 2-3z" class="Q"></path><path d="M158 392l1 1c-1 1-1 2-2 3h0-1c-1 0-1 0-2-1 1-1 3-2 4-3z" class="e"></path><path d="M136 134c1 0 1 0 2-1-1 2-2 3-3 5-3 4-6 7-8 12-1 1-1 1-1 3l-2-2c0 1-1 2-1 3v-2c0-1 1-2 1-3s0-1-1-1h0c-1-1-1-2-2-3h-1c0-1-1-1-1-1h-1v-1l2-1c5-3 10-5 16-8z" class="g"></path><path d="M136 134c1 0 1 0 2-1-1 2-2 3-3 5-3 4-6 7-8 12-1 1-1 1-1 3l-2-2c0 1-1 2-1 3v-2c0-1 1-2 1-3s0-1-1-1v-1c0-1 2-2 3-3l1-1h1 0l1-1 7-7v-1z" class="J"></path><path d="M126 144l1-1h1 0l1-1-5 9c0 1-1 2-1 3v-2c0-1 1-2 1-3s0-1-1-1v-1c0-1 2-2 3-3z" class="v"></path><path d="M126 144v1c0 1-1 2-1 4h-1c0-1 0-1-1-1v-1c0-1 2-2 3-3z" class="B"></path><path d="M114 146v-3l1 2 3-1h1s1 0 1 1h1c1 1 1 2 2 3h0c1 0 1 0 1 1s-1 2-1 3h-1l-1 2h0c-1 2-1 3 0 4v1 2c0 1 0 2-1 2v3c-1 0-1-1-1-1v-2c0-2 0-3-2-4h0l-1-1-1 1-1-2c0-1-1-2-1-2v-1c0-2-1-2-1-3l1-1c0 1 1 2 1 3l1-1c0-2-1-4-1-6z" class="l"></path><path d="M112 151l1-1c0 1 1 2 1 3 1 2 2 4 2 5l-1 1-1-2c0-1-1-2-1-2v-1c0-2-1-2-1-3z" class="AA"></path><path d="M114 146v-3l1 2c1 1 1 2 1 3 0 2 1 5 1 7-1 0-1-2-2-3 0-2-1-4-1-6zm4-2h1s1 0 1 1h1c1 1 1 2 2 3h0c1 0 1 0 1 1s-1 2-1 3h-1l-1 2h0c-1 2-1 3 0 4v1 2c0 1 0 2-1 2v1h-1c1-4 0-8-1-13v-7z" class="AB"></path><path d="M120 145h1c1 1 1 2 2 3h0c1 0 1 0 1 1s-1 2-1 3h-1l-1 2h0c-1-1-1-3-2-5 1-1 1-3 1-4z" class="B"></path><path d="M123 148c1 0 1 0 1 1s-1 2-1 3h-1-1c1-1 0-1 1-2 0-1 1-1 1-2h0z" class="F"></path><path d="M114 622h1v1c-1 2-2 4-2 6-2 8 0 16 3 23 2 4 4 11 2 16-1 2-4 4-6 5-1 1-3 1-4 1l-3-3v-1h0c1 1 2 3 4 3 1 0 2 0 4-1 0 0 2-1 2-2 0 0-1 0-1-1-1 1-1 1-3 0 0-1 2-3 2-5 1-3 1-6 1-9-1-8-4-16-3-25 1-3 2-6 3-8z" class="AA"></path><path d="M115 655v-2h0c2 3 3 9 2 13 0 1-1 3-2 4 0 0-1 0-1-1-1 1-1 1-3 0 0-1 2-3 2-5 1-3 1-6 1-9h1z"></path><path d="M115 664c1-1 1-1 1-3v1c0 1 1 1 1 1v1 2c0 1-1 3-2 4 0 0-1 0-1-1 1-1 1-2 1-3h0l-3 4c0-3 2-5 3-6z" class="M"></path><path d="M115 655v-2h0c2 3 3 9 2 13v-2-1s-1 0-1-1v-1c0 2 0 2-1 3v-9z" class="a"></path><path d="M146 130c-1-8-4-16-6-24-1-5-2-11 1-15 1-2 3-3 5-3 6-1 13 5 18 9l-1 1-3-2-1 1c-3-3-6-5-10-6-2-1-3 0-4 0-4 6-3 13-1 19v1h-1-1c1 5 3 9 4 13v4 2h0z" class="w"></path><path d="M144 90c1-1 2-1 4-1 4 0 9 5 12 7l-1 1c-3-3-6-5-10-6-2-1-3 0-4 0 0 0-1 0-1-1z" class="X"></path><path d="M144 90c0 1 1 1 1 1-4 6-3 13-1 19v1h-1-1c0-4-1-8-1-12-1-4 0-6 3-9z" class="o"></path><path d="M82 590v1c0 1 1 2 1 2 1 2 3 2 4 3l5 1 2 1v1h-2v1c1 0 1 0 1 1-4 3-4 7-5 12v1c1-1 2-2 4-2h2l1 1c1-1 3-1 4-2h1c-6 3-11 6-16 10 0-1 1-3 2-4l2-12c-3-1-5-2-7-4l2-2-1-1c-2-1-3-1-4-3l1-1h2c0 1 1 1 1 1v-1c0-2-1-3-2-4h2z" class="k"></path><path d="M92 612h2l1 1-4 2v-1l1-2z" class="N"></path><path d="M84 600h1c0 1 1 2 1 3h-1c-1-1-2-1-3-1v-1l2-1z" class="M"></path><path d="M87 596l5 1-1 2c-2 0-1-1-3-1h0l-1-2z" class="C"></path><path d="M88 613v1c1-1 2-2 4-2l-1 2v1c-1 1-3 1-4 2l1-4zm-2-15h1l1 2h1c1 0 1 0 2 1l-2 2c0 1 0 1-1 2-1-2-3-5-2-7z" class="X"></path><path d="M288 628l3 1h0c1 0 2 1 3 1l4 1v1l2 1h0v1l1 1v1l-6-3c2 1 4 4 6 4 4 4 9 7 12 12 3 3 5 7 6 11 1 2 2 4 2 7h0v6h0l-1 4c2-3 4-4 5-7 0-1 0-3 1-4v-8c-1-1-1-2-1-3v-1c0-2-2-5-3-7l-1-1-3-3 1-1v1c2 1 3 2 4 4 3 4 4 8 4 13v4c0 1 0 3-1 5v1c0 1 0 2-1 3-1 2-4 4-6 5l-1-1c1-1 2-3 2-5v-1-6c-1-2-2-5-3-7-1-4-4-6-6-9-1-2-2-3-4-5-6-6-13-11-21-13h0c-2-1-2-1-3-2h3c1 0 1 0 2-1z" class="J"></path><path d="M288 628l3 1v2c-2-1-3-1-5-2 1 0 1 0 2-1z" class="z"></path><path d="M291 629c1 0 2 1 3 1l4 1v1s-1 0-1 1h0c-2-1-4-2-6-2h0v-2h0z" class="S"></path><path d="M242 609c2 0 3 1 5 1v-1l14 6h0l-1 1h1c1 1 2 1 3 2h0v1l2 1c0 1-1 1-1 2-2-1-3-2-5-3 3 3 7 7 7 12 0 1-1 3-2 4h0c-1-1-1-1-1-3 0-1 0-2-1-3-3-7-11-12-17-15l-6-4 1-1h1 0z" class="g"></path><path d="M263 629l1-1v1c1 0 1-1 1-1 1 1 1 3 0 5h0l-1-1c0-1 0-2-1-3zm-16-19v-1l14 6h0l-1 1h1c1 1 2 1 3 2h0v1l2 1c0 1-1 1-1 2-2-1-3-2-5-3l-1-1h0-1c0-1-2-2-3-3 0 0 0-1-1-1l-7-4z" class="N"></path><path d="M254 614c4 1 7 3 10 5l2 1c0 1-1 1-1 2-2-1-3-2-5-3l-1-1h0-1c0-1-2-2-3-3 0 0 0-1-1-1z" class="w"></path><path d="M240 610l1-1h1 0c8 5 20 10 23 19 0 0 0 1-1 1v-1l-1 1c-3-7-11-12-17-15l-6-4z" class="X"></path><path d="M542 578l10-3c11-4 23-11 31-20 1-3 3-5 4-7s2-4 2-6c1-2 2-5 3-7l2-10c1-1 1-4 2-5v-1l-3 17c0 2-1 4-2 6s-2 5-3 7-3 5-5 7c5-3 10-7 15-11l13-12v1c-6 7-13 13-21 18-1 2-4 4-6 4-2 1-6 6-8 7-4 3-8 6-13 8-2 3-3 6-6 9h0c-1 1-1 1-2 1l-2-1h1c1-2 2-3 3-5h-1c-1 1-3 1-4 2-3 0-5 1-7 2l-1-1h-2z" class="s"></path><path d="M557 575h0c0-1 1-1 1-1 1-1 2-1 3-2v1c0 2-3 6-5 7 0 0-1 0-1 1l-2-1h1c1-2 2-3 3-5z" class="n"></path><path d="M545 579c2-1 4-2 7-2 1-1 3-1 4-2h1c-1 2-2 3-3 5h-1l2 1c1 0 1 0 2-1 5 0 12-1 16 1 3 0 5 2 7 4-3 1-7 3-10 2l-6-3h0c-5 2-8 1-12-1h0c-1 0-1-1-2-1 0 0-1 1-2 0h-1c-1 0-2-1-3-1s-1 0-1-1l1-1h1 0z" class="B"></path><path d="M558 584l1-1c1-1 1-1 3-1v1c0 1-1 1-1 2-1 0-2-1-3-1z" class="n"></path><path d="M558 584h-1c-1 0-2-1-2-2 1-1 2-1 4-1 1 0 2 0 3 1-2 0-2 0-3 1l-1 1z" class="I"></path><path d="M544 579h1 0c2 1 4 1 5 2h0c-1 1-2 1-3 1s-2-1-3-1-1 0-1-1l1-1z" class="g"></path><path d="M570 586h-1c-1-1-3-2-4-3h1 6 1c-2-1-3-1-5-1h-3l-1-1h1c0-1 2 0 3 0 3 0 5 0 7 1l1 1 2 1c-1 1-2 2-3 2h-5z" class="N"></path><path d="M570 586c1-1 3-1 4-1 0 0 1 0 1 1h-5z" class="y"></path><path d="M127 242h0 0c3 2 4 5 6 7 1 2 2 3 3 5 2 5 6 10 8 16 0 1 1 2 0 3l-5 5v1c-1-2-2-4-2-6l-2-7-6-18c-1-1-2-3-2-5v-1z" class="q"></path><path d="M127 242c1 2 1 3 2 4 2 5 4 10 5 15 1 3 3 6 4 9 0 3 0 6 1 8v1c-1-2-2-4-2-6l-2-7-6-18c-1-1-2-3-2-5v-1z" class="f"></path><path d="M60 556c-1-2 1-5 1-6 1-4 1-8 0-12h-1l-1 1h0c1-2 1-6 1-8 0-4 1-7 1-11 1-3 2-6 3-8 1-5 4-10 8-14 1-2 3-4 5-5s4-2 6-1h-1-1c-1 0-1 0-2 1l-1 1c-2 1-4 3-5 5-6 6-9 12-11 21-1 8-1 15 1 23 1 5 4 9 5 13 1 3 5 7 4 9v1h-4c-1-1-1-1-1-2l-1-2c-1 0-1-1-2-1v1l-1 1c1 1 2 2 2 3l-1 1h0c0-3-4-4-4-7 0-1 1-3 2-4h-2z"></path><path d="M66 560v1h1c0 1 1 2 1 3h1l1-1c1 1 1 1 1 2h1v1h-4c-1-1-1-1-1-2l-1-2v-2z" class="AD"></path><path d="M63 563l-2-2c0-1 2-4 3-5h0v2c-1 0-1 1-1 2 1 0 1 0 2-1 0 0 1 0 1 1v2c-1 0-1-1-2-1v1l-1 1z" class="X"></path><path d="M60 556l1-1h0c0-2 1-4 2-6 1 1 1 1 1 2h0-1v1c0 1 1 1 2 2h1l-1 1v1l-1-1c-1 0-1 1-2 1h-2z" class="o"></path><path d="M136 254l1 1 7 11h0l1-1 2-1 2-3 1 1 1-2c1 0 2 0 3 1h1c1 0 2 0 3-1v1h0v2 1c1 0 1 0 2 1v1l-2 2 1 1c-1 1-4 3-5 4s-1 1-3 1h0-4c-1 1-2 1-3 2-1 0-2 3-3 4-1-1-2-1-2-1v-1l5-5c1-1 0-2 0-3-2-6-6-11-8-16z" class="G"></path><path d="M144 266h0l1-1 1 1c0 2 0 3-1 4h0l-1-4z" class="N"></path><path d="M145 265l2-1h1c1 0 1 0 2 1h1c1 1 2 1 3 1v-2h1c0 1 0 1 1 2h0c-2 1-3 2-4 3-3-1-3 2-5 3 0-2 1-3 2-4h-2c0-1 0-1 1-1l-1-1h-1l-1-1z" class="h"></path><path d="M148 264c1 0 1 0 2 1-1 0-1 1-2 0v-1z" class="D"></path><path d="M150 262l1-2c1 0 2 0 3 1h1c1 0 2 0 3-1v1h0v2 1h-1c0 1 0 1-1 2h0c-1-1-1-1-1-2h-1v2c-1 0-2 0-3-1h-1c-1-1-1-1-2-1h-1l2-3 1 1z" class="C"></path><path d="M158 261v2 1h-1c0 1 0 1-1 2h0c-1-1-1-1-1-2s1-1 2-2h0l1-1z" class="D"></path><path d="M158 261v2 1h-1v-2h0l1-1z" class="H"></path><path d="M147 264l2-3 1 1 1 1h1l1 1c-1 1-1 1-2 1h-1c-1-1-1-1-2-1h-1z" class="R"></path><path d="M158 264c1 0 1 0 2 1v1l-2 2 1 1c-1 1-4 3-5 4s-1 1-3 1h0-4l5-5c1-1 2-2 4-3 1-1 1-1 1-2h1z" class="u"></path><path d="M158 268l1 1c-1 1-4 3-5 4s-1 1-3 1l7-6z" class="t"></path><path d="M159 316v-1h1c0 1 0 2 1 3l-1 2-2 3h-1l-7 16v-1l-2 4 1 1-1 3-1 1c-1 1-1 3-3 4-1 0-2 0-3 1l-1 1h0l-2 2h-1l3-5 4-6c0-2 1-4 1-6l-1-1c0-1 1-2 2-3 0-2 1-4 2-5h1 0c1-2 1-3 1-5v-1c1 1 1 1 2 1l1-3 3-6c1 0 1 0 1 1l1-1c0 1 0 1 1 1z" class="J"></path><path d="M148 342l1 1-1 3-1 1h-1c0-2 1-3 2-5z" class="k"></path><path d="M145 343v3c-1 2-3 4-5 4l4-6 1-1z" class="O"></path><path d="M146 334c0-2 1-4 2-5h1c0 1-1 2-1 4l1 1 1-1c-1 4-3 7-5 10l-1 1c0-2 1-4 1-6l-1-1c0-1 1-2 2-3z" class="K"></path><path d="M146 334c0-2 1-4 2-5h1c0 1-1 2-1 4l1 1c-1 1-2 2-2 4l-1-1c0-1 1-2 1-3h-1z" class="d"></path><path d="M159 316v-1h1c0 1 0 2 1 3l-1 2-2 3h-1l-7 16v-1l3-5c0-2 1-4 1-5h-1c0 1-1 2-1 3l-1-1c0 1-1 2-1 3l-1 1-1-1c0-2 1-3 1-4h0c1-2 1-3 1-5v-1c1 1 1 1 2 1l1-3 3-6c1 0 1 0 1 1l1-1c0 1 0 1 1 1z" class="u"></path><path d="M153 321l1 2-3 6h-1l2-5 1-3z" class="p"></path><path d="M159 316v-1h1c0 1 0 2 1 3l-1 2-2 3h-1c1-2 1-5 2-7z" class="W"></path><path d="M156 315c1 0 1 0 1 1l-3 7-1-2 3-6z" class="X"></path><path d="M150 324v-1c1 1 1 1 2 1l-2 5h1v1c0 1-1 2-1 3l-1 1-1-1c0-2 1-3 1-4h0c1-2 1-3 1-5z" class="h"></path><path d="M150 329h1v1c0 1-1 2-1 3l-1 1-1-1 2-4z" class="P"></path><path d="M206 597l12 1c3 0 7 1 10 1l2 1h-1c3 1 6 2 8 2l2 1-1 1h2-2v1l-1-1-2 2 3 1 1 1c1 0 2 1 3 1h-1l-1 1 6 4c-1 0-1 0-2 1-1 0-2-1-3-1l3 4h0c1 2 1 3 1 4 0 5-4 9-7 12h0c2-2 3-4 5-6 1-3 1-6 0-8-1-3-3-5-5-7-2-1-3-3-5-4s-5-3-7-4c-5-2-11-4-17-5h-1l-1-1h0l1-1h-3-10s-1 0-1-1h12z" class="AB"></path><path d="M238 609c0-1 0-1 1-1s2 1 3 1h-1l-1 1-2-1z" class="e"></path><path d="M225 601l10 5 3 1 1 1c-1 0-1 0-1 1-2-1-5-3-7-4l-8-4h2z" class="p"></path><path d="M226 600h-1-1v-1h3c1 1 1 1 2 1 3 1 6 2 8 2l2 1-1 1h2-2v1l-1-1-2 2-10-5 1-1z" class="X"></path><path d="M226 600l11 4-2 2-10-5 1-1z" class="B"></path><path d="M206 597l12 1c3 0 7 1 10 1l2 1h-1c-1 0-1 0-2-1h-3v1h1 1l-1 1h-2-1c-4-1-9-2-14-3h-3-10s-1 0-1-1h12z" class="I"></path><path d="M67 522l3-8c3-5 7-8 12-10 1 0 2 1 3 1 1 1 2 3 3 5 0 2-1 4-2 5-2 3-5 3-7 3h-1v1c1-3 4-4 5-6-1 0-5 4-7 5-4 4-6 11-7 17-1-3-1-5 0-8-1-1-1-3-2-5z" class="q"></path><path d="M67 522l3-8c3-5 7-8 12-10 1 0 2 1 3 1 1 1 2 3 3 5 0 2-1 4-2 5-2 3-5 3-7 3h-1v1c1-3 4-4 5-6 1 0 1 0 2 1h0s1 0 1-1c1-2 1-4 0-6-1-1-2-2-4-2-1 0-4 1-5 2-1 2-3 3-4 5h1l1-1h1c-4 5-6 11-7 16-1-1-1-3-2-5z" class="x"></path><path d="M83 513c1 0 1 0 2 1h0s1 0 1-1c0 1-1 3-2 3-2 1-4 1-6 2v1c1-3 4-4 5-6z" class="N"></path><path d="M140 528c1-1 1-3 2-5 3-6 9-10 16-12-1 0-1 1-2 1-1 1-2 1-2 1-2 2-3 3-3 5h0-1c-2 3-4 5-5 8h0 1c1-1 1-2 1-2 1 1 0 1 0 2l-2 1v1c2 0 3 1 4 2v1c0 1 1 1 1 1v2l2 1-1 1h0v2c-3-1-4-1-7-1-1 1-2 1-3 1h-1l-1-2c-1-1-1-2-1-4v-5c-2-3-5-7-9-9-3-1-6-2-9-1s-4 3-5 5v1l-4 4v1h1l1-1c2 0 4 0 6 1s7 5 8 7c-3-1-5-3-8-5l-1 1h0-1c0-1-2-2-4-2h0c-2 0-2 0-3-1v-1c1-1 2-2 3-4s2-5 5-6c3-2 6-1 10 0 5 2 9 6 11 12l1-1z" class="U"></path><path d="M145 528c0 1 1 1 1 1v1h-3v-1l2-2v1z" class="Q"></path><path d="M119 528c2 1 7 5 8 7-3-1-5-3-8-5-1-1-3-1-5-2h4 1z" class="O"></path><path d="M140 528v1l1-1c0-1 0-1 1-1v-2c1-3 4-6 7-8h0c0 1-1 2-2 3-2 2-4 5-4 8l-2 2c-1 0-1 0-2-1l1-1z" class="M"></path><path d="M141 538v-1-1c0-2-1-2 0-3 1 0 2-1 2-2h1 3c1 0 1 0 1 1v1h-1l1 1c1-1 1-2 1-3 0 1 1 1 1 1v2l2 1-1 1h0v2c-3-1-4-1-7-1-1 1-2 1-3 1z" class="L"></path><path d="M149 531c0 1 1 1 1 1v2l2 1-1 1h0c-1 0-4 0-4-1l1-1c1-1 1-2 1-3z" class="f"></path><path d="M78 157c8 3 16 4 22 8 0 1 0 1 1 1v-1c-1 0-1-1-2-1-3-2-5-4-7-5-6-5-13-8-21-9h0 0l9 2c5 1 10 4 14 7 3 2 5 5 8 6-2-4-5-7-8-9-2-3-5-4-8-6-7-4-14-7-21-9l-1-1h0c14 4 29 11 38 22v1h1v-2l1-1c-1-1-2-3-2-4-3-5-5-9-8-13v-1c4 5 7 11 11 17l2-4c1-1 3-2 5-4 0 1 1 1 1 3v1c-1 0-2 1-2 2-1 1-2 2-2 4-2 3-4 7-6 11-1 3-2 5-2 9v1c0 1-1 2-1 2l-2 2v2c-1 1-2 1-2 2h-2c2-2 3-4 3-6 1-2 1-5 2-8 0-3 1-6 1-9-3-3-6-4-10-6l-12-3v-1z"></path><path d="M111 217c1 0 2 1 3 1h2 2 1c0 1 0 1-1 2h0c-1 1-1 2-1 3l-1 1v1h0-2l-1 1c1 0 1 1 2 2 1 2 2 4 2 6h0v2c0 1 0 2-1 4 0-2-1-2-2-3-1 0-1 0-1 1h-1c0-1-1-1-1-2-1 2 0 11 0 14v17c0 9-1 19-3 29 0 0-1 0-1 1v1h0c0-4 1-7 2-11 1-14 2-29 1-43 0-2 0-6-1-8v-1l-2-10c0-1-1-1-1-1v3c0 2 0 2-1 3h0l-1-4v-1s-1 0-1 1h-1l-1-1-1-1c1 0 1-1 2-1h0c1 0 2-1 3-1h0v-1h1 3v-1c1 0 1 0 0-1l2-2h0z" class="G"></path><path d="M102 223h0l2 2s-1 0-1 1h-1l-1-1-1-1c1 0 1-1 2-1z" class="J"></path><path d="M109 229c0-1-1-3-1-4h2v-1l3 4h0c0 1 0 2-1 3l-1-2s0-1-1-1l-1 1z" class="Y"></path><path d="M109 229l1-1c1 0 1 1 1 1l1 2c1 1 1 2 1 3l1 1c1 0 1-1 2-1 0 0 0 1 1 2 0 1 0 2-1 4 0-2-1-2-2-3-1 0-1 0-1 1h-1c0-1-1-1-1-2h-1c-1-2-1-5-1-7z" class="AA"></path><path d="M111 217c1 0 2 1 3 1h2 2 1c0 1 0 1-1 2h0c-1 1-1 2-1 3l-1 1v1h0-2l-1 1c1 0 1 1 2 2 1 2 2 4 2 6h0v2c-1-1-1-2-1-2-1 0-1 1-2 1l-1-1c0-1 0-2-1-3 1-1 1-2 1-3h0v-1c-1-1-1-3-2-4 0-1-1-1-2-1h-3v-1h3v-1c1 0 1 0 0-1l2-2h0z" class="i"></path><path d="M113 228c0 1 1 1 1 2 1 1 1 2 2 4-1 0-1 1-2 1l-1-1c0-1 0-2-1-3 1-1 1-2 1-3z" class="x"></path><path d="M111 217c1 0 2 1 3 1h2 2 1c0 1 0 1-1 2h0c-1 1-1 2-1 3l-1 1v1h0-2l-1 1c0-1 0-2-1-2l1-1h1 1 1l-1-1c-2-1-3-1-5-1h-1v-1c1 0 1 0 0-1l2-2h0z" class="O"></path><path d="M109 219h1 2 0v1l-2 1h-1v-1c1 0 1 0 0-1z" class="V"></path><path d="M111 217c1 0 2 1 3 1l-1 1h-1 0-2-1l2-2h0z" class="R"></path><path d="M114 218h2 2 1c0 1 0 1-1 2h0l-1-1-1 1c-2 0-2 0-3-1l1-1z" class="AC"></path><path d="M93 601l21-3v1l1 1h0l-1 2-3 3v1h0 1v2h0c-1 1-2 1-3 2-2 0-4 2-5 2s-1-1-1-1h-3-1c-1 1-3 1-4 2l-1-1h-2c-2 0-3 1-4 2v-1c1-5 1-9 5-12z" class="q"></path><path d="M114 599l1 1h0l-1 2-3 3v1h0 1v2h-2l-2-2 6-7z" class="m"></path><path d="M92 612h0c0-1 5-3 6-3 1-1 3-1 5-1h-2v1h2c0-1 1-1 2-1-1 1-2 1-3 2-1 0-2 0-3 1s-3 1-4 2l-1-1h-2z" class="X"></path><path d="M105 607l3-1 2 2h2 0c-1 1-2 1-3 2-2 0-4 2-5 2s-1-1-1-1h-3-1c1-1 2-1 3-1 1-1 2-1 3-2v-1z" class="M"></path><path d="M105 607l3-1 2 2h2 0c-1 1-2 1-3 2l-1-2c-1 0-2 0-3-1z" class="b"></path><path d="M146 130h0v7 4c0 1 0 1-1 1l2 1c0 1-1 1-1 3h1l-1 1h-1c-2 2-5 4-7 6l-1-1h0-1l-1 2-1-1-2 1v-2l1-3h0l-2 2v-1-2l-3 3-1-1c2-5 5-8 8-12 1-2 2-3 3-5 3-1 5-2 8-3z" class="AB"></path><path d="M134 146v1l-1 2-2 2v-1-2h0l3-2z" class="t"></path><path d="M145 142l2 1c0 1-1 1-1 3h1l-1 1h-1c-2 2-5 4-7 6l-1-1h0-1l-1 2-1-1-2 1v-2l1-3h0l1-2v-1c1-1 6-3 8-3h1s2 0 2-1z" class="H"></path><path d="M137 146c1-1 1-1 2-1v2c1-1 2-1 3-2h0c0 2-3 6-5 7h0-1l-1 2-1-1-2 1v-2l1-3 4-3z" class="r"></path><path d="M137 146h1c0 2-3 5-4 7l-2 1v-2l1-3 4-3z" class="t"></path><path d="M399 630c1 0 1-1 2-1 1 1 2 1 2 1h2 2v1h2 1 2c2-1 4-1 6-1h-3v1c2 1 4 1 5 3 2-1 3-2 4-2h1 4 3 4l-1-1 2-1c4 1 8 3 10 6h1l6 6h0l2-1c1 0 1 0 2 1v1c0 1-1 2-2 3-2-1-3-3-5-5-1-1-3-1-4-1-8-3-16-2-25-3-5 0-9 0-13-2-1 0-3-1-4-1v-1c-1-1-4 0-5-2h1 0l-2-1z" class="E"></path><path d="M454 642h0 1 2 0c0 1 0 1-1 2v1c-1-1-2-2-2-3z" class="y"></path><path d="M409 631h1 2c2-1 4-1 6-1h-3v1h-1c1 1 4 2 5 3s2 1 3 2c-2 0-3 1-4 0 0 0 0-1-1-1 0-1-4-1-5-1 0-1-1-1-2-2v-1h-1 0z" class="Q"></path><path d="M399 630c1 0 1-1 2-1 1 1 2 1 2 1h2 2v1h2 0 1v1c1 1 2 1 2 2 1 0 5 0 5 1 1 0 1 1 1 1h-5l-8-3c-1-1-4 0-5-2h1 0l-2-1z" class="N"></path><path d="M399 630c1 0 1-1 2-1 1 1 2 1 2 1h2 2v1h2 0-8 0l-2-1z" class="J"></path><path d="M437 630c4 1 8 3 10 6h1l-1 2h1v1c-4-1-9-2-14-3h-8s-2 1-3 0c0 0-1 0-1-1 0 0-1-1-2-1 2-1 3-2 4-2h1 4 3 4l-1-1 2-1z" class="e"></path><path d="M420 634c2-1 3-2 4-2h1 4 3c-1 1-2 1-3 1h-1v1c1 0 2 0 3 1h-1-1c-1-1-1-1-2-1h-3v1h1-1 0-2s-1-1-2-1z" class="p"></path><path d="M437 630c4 1 8 3 10 6h1l-1 2c-3-1-6-4-9-5-1 0-1-1-2-1l-1-1 2-1z" class="O"></path><path d="M76 542c1-3 2-5 5-6 3-2 7-3 10-5h1v1c-1 3-1 5-2 8-1 4-1 10-1 15h0l1 4h-1c4 4 8 7 12 11h0c-6-4-13-8-18-14-2-1-3-4-4-5 0-1-1-3-1-3v-1c-1 0-1-1-2-1h0v-4z" class="q"></path><path d="M83 536s2-1 3-1c-1 1-1 2-1 3-1 1-1 1-1 2-1 1 0 6-1 7-1-2 0-5 0-7h0c0-1 1-2 1-3l-1-1z" class="n"></path><path d="M76 542c1-3 2-5 5-6 3-2 7-3 10-5h1v1c-1 3-1 5-2 8-1 4-1 10-1 15h0c-1-8 0-15 2-22l-5 2c-1 0-3 1-3 1-2 1-4 3-5 5h0l1 1v1l-1-1c-1 1-1 2-1 3h1l1-1v1h0c-1 3 2 7 4 9s4 4 6 5c4 4 8 7 12 11h0c-6-4-13-8-18-14-2-1-3-4-4-5 0-1-1-3-1-3v-1c-1 0-1-1-2-1h0v-4z"></path><path d="M164 317c1 0 1 0 2-1 1 0 0-1 1-1 1 1 2 0 2 1 1 1 0 3-1 4l-1 4c-1 2-2 4-2 6l-2-1c-1 2-2 5-3 7v2l-1 1v2c-1 1-2 2-3 4s-3 4-5 6l-2 4v-1h-1c0 1 0 1-1 2v-1h0v-2c-1 1-1 2-2 2v-1c1-1 2-3 2-4 1-2 1-2 1-4h0l1-3-1-1 2-4v1l7-16h1l2-3v1l1-2 3-1v-1z" class="O"></path><path d="M148 354h0c1-1 2-2 2-4 0 0 1-1 1-2h0v-1l1 1c0 1-1 2-1 3l-2 4v-1h-1z" class="b"></path><path d="M160 321l1-2 3-1v1h-1v1c-1 2-2 4-3 5l-1-1 1-1v-2z" class="R"></path><path d="M158 337h1l1-1v2l-1 1-2 2c-2 1-3 4-5 7l-1-1c1 0 1-1 1-1 1-1 1-2 2-3 1-2 2-3 3-5l1-1z" class="Q"></path><path d="M152 348c2-3 3-6 5-7l2-2v2c-1 1-2 2-3 4s-3 4-5 6c0-1 1-2 1-3z" class="J"></path><path d="M159 326c0 1 0 2-1 3v1c-1 1-2 4-2 6-1 0-1 0-1 1l-1 2c-1 1-1 1-1 2s0 2-1 3h-1c2-6 4-10 6-15l2-3z" class="b"></path><path d="M158 337c1-3 2-5 4-7 1-4 2-8 4-11h0l-1 4c-1 2-1 4-2 6s-2 5-3 7l-1 1h-1z" class="M"></path><path d="M164 317c1 0 1 0 2-1 1 0 0-1 1-1 1 1 2 0 2 1 1 1 0 3-1 4l-1 4c-1 2-2 4-2 6l-2-1c1-2 1-4 2-6l1-4c1-1 1-2 1-3h-1c0 1 0 2-1 3h-1 0v-1-1z" class="H"></path><path d="M165 323h0 1c1-1 1-3 2-4v1l-1 4c-1 2-2 4-2 6l-2-1c1-2 1-4 2-6z" class="O"></path><path d="M160 320v1 2l-1 1v2l-2 3c-2 5-4 9-6 15l-3 6h-1c1-2 1-2 1-4h0l1-3-1-1 2-4v1l7-16h1l2-3z" class="J"></path><path d="M148 342l2-4v1c0 1 0 3-1 4l-1-1z" class="R"></path><path d="M160 320v1 2l-1 1v2l-2 3c0-2 1-4 1-6l2-3z" class="E"></path><path d="M405 614c-1 1-4 2-5 4-3 3-5 9-9 12l-1 1c3 0 6-1 9-1l2 1h0-1c1 2 4 1 5 2v1c1 0 3 1 4 1-1 1-1 1-2 1 3 3 6 5 8 9 0 2 0 3-1 5h0l-2-4c-3-6-21-10-28-12l-1-1c-2 0-4 0-6-1l-4 1 5-2c5-2 12-8 15-12l2-2h0v1c0 3-1 5-2 7 1 0 2-2 3-4 2-4 5-6 9-7z" class="AB"></path><path d="M392 635c-2-1-6-1-8-2h1c3-1 7 0 11 1h-4v1z" class="a"></path><path d="M399 630l2 1h0-1c1 2 4 1 5 2v1c1 0 3 1 4 1-1 1-1 1-2 1-3-1-7-3-10-4h-9v-1h2c3 0 6-1 9-1z" class="p"></path><path d="M378 631c5-2 12-8 15-12l2-2h0v1h0c-1 1-1 2-1 2-1 4-5 9-9 11-2 1-5 1-8 1l-4 1 5-2z" class="y"></path><path d="M396 634c3 0 8 2 11 3 4 2 6 6 8 11l-3-4c-2-2-4-3-7-4-4-2-9-3-13-5v-1h4z" class="N"></path><path d="M83 492c0 1 1 1 0 2v1c-2 2-3 3-5 4v-1l-3 3c-6 7-9 17-9 26l3 9c1 3 2 5 4 7 1 2 4 6 4 7s1 2 2 3c0 1 1 2 2 3h0c1 1 1 1 1 2h-3c0-1-2-2-3-3l-1-2-1-1c-1-1-2-3-3-5 0 0-1-1-1-2l-1-2-3-3-1 1s0-1-1-2v2 2h-1c-2-8-2-15-1-23 2-9 5-15 11-21 1-2 3-4 5-5l1-1c1-1 1-1 2-1h1 1z" class="q"></path><path d="M83 492c0 1 1 1 0 2v1c-2 2-3 3-5 4v-1l-3 3c0-3 2-5 4-7h-1l1-1c1-1 1-1 2-1h1 1z" class="I"></path><path d="M83 492c0 1 1 1 0 2v1c-1 0-2 1-3 1h0v-1-1h-1-1l1-1c1-1 1-1 2-1h1 1z" class="Q"></path><path d="M128 151l3-3v2 1l2-2h0l-1 3v2l2-1 1 1 1-2h1 0l1 1-3 3 2 3 2-3 1 1-2 2-2 2 1 2h0 1v2c1-1 2-2 3-2 1 1 1 2 1 3l-1 1-1 1v1h0l-2 1 1-1-1 1-1-1h-2v1c1 0 1 0 1 1h-1c0 1 0 1-1 1v-2h-2v-1h-1 0-4-3-1c-1 1-1 2-2 3l-2-1h-1v-3h0l-1-2 1-1h1s0 1 1 1v-3c1 0 1-1 1-2v-2-1c-1-1-1-2 0-4h0l1-2h1v2c0-1 1-2 1-3l2 2c0-2 0-2 1-3l1 1z" class="d"></path><path d="M127 167h-2v-1c1-1 1-1 2-1s1 1 2 2h-2z" class="r"></path><path d="M132 152v2l-1 2-1 1-1 3h0c1 0 1 0 1 1-1 1-1 2-2 2l-1 1v-2c0-3 3-9 5-10z" class="I"></path><path d="M120 163c1 0 1-1 1-2 0 1 1 1 1 1v3h1c0 1 0 3-1 3h-1v1c0 1-1 1-2 2h-1v-3h0l-1-2 1-1h1s0 1 1 1v-3z" class="G"></path><path d="M117 166l1-1h1s0 1 1 1l-1 1h-1v1h0l-1-2z" class="O"></path><path d="M122 165h1c0 1 0 3-1 3h-1v1l-1-1h1c1-1 1-2 1-3zm15-13l1 1-3 3-3 6-2-1h0c0-1 0-1-1-1h0l1-3 1-1 1-2 2-1 1 1 1-2h1 0z" class="L"></path><path d="M130 157h3l-3 4h0c0-1 0-1-1-1h0l1-3z" class="V"></path><path d="M135 154l1-2h1l-4 5h-3l1-1 1-2 2-1 1 1z" class="t"></path><path d="M132 154l2-1 1 1c-1 1-2 1-3 2h-1 0l1-2z" class="G"></path><path d="M128 151l3-3v2 1 1c-1 1-2 2-3 4v2c-1 1-1 1-2 1v1c-1 0-1 0-2 1s-1 3-1 4h-1v-3s-1 0-1-1v-2-1c-1-1-1-2 0-4h0l1-2h1v2c0-1 1-2 1-3l2 2c0-2 0-2 1-3l1 1z" class="s"></path><path d="M126 156c1 1 1 2 2 2-1 1-1 1-2 1l-1-1c0-1 1-1 1-2z" class="G"></path><path d="M124 151l2 2-2 2h0 0-1v-1h0c0-1 1-2 1-3z" class="d"></path><path d="M121 159h0l2-4h1 0c0 3-1 5-2 7 0 0-1 0-1-1v-2z" class="K"></path><path d="M123 152v2h0v1l-2 4h0v-1c-1-1-1-2 0-4h0l1-2h1z" class="i"></path><path d="M123 152v2h0l-1 1-1-1 1-2h1z" class="AA"></path><path d="M128 151l3-3v2c-2 1-2 3-3 4h0c-1 0-1 1-2 1-1-2 1-2 2-4h0z" class="G"></path><path d="M131 150v1 1c-1 1-2 2-3 4v2c-1 0-1-1-2-2l2-2h0c1-1 1-3 3-4z" class="V"></path><path d="M139 156l1 1-2 2-2 2 1 2h0 1v2c1-1 2-2 3-2 1 1 1 2 1 3l-1 1-1 1v1h0l-2 1 1-1-1 1-1-1h-2v1c1 0 1 0 1 1h-1c0 1 0 1-1 1v-2h-2v-1h-1 0-4-3-1c1 0 1 0 2-1h0 1 1v-1h2c0-1 0-1 1-2-1 0-1 0-2-1v-1c1 0 1-1 2-2h0l2 1 3-6 2 3 2-3z" class="K"></path><path d="M131 164l2-1c0 1 1 1 1 2h0-2c0 1-1 1-1 2h0-1c1-1 1-2 1-3z" class="r"></path><path d="M139 156l1 1-2 2-2 2h-1l2-2 2-3z" class="d"></path><path d="M130 161l2 1c-1 1-1 1-1 2l-1 1c-1 0-1 0-2-1v-1c1 0 1-1 2-2h0z" class="r"></path><path d="M137 163h1v2c1-1 2-2 3-2 1 1 1 2 1 3l-1 1-1 1v1h0l-2 1 1-1-1 1-1-1h-2v1-1l1-2h-1l-2 1h0l1-1c0-2 2-3 3-4z" class="L"></path><path d="M136 167c1 0 1-2 2-2v2l-1 1-2 1 1-2z" class="H"></path><path d="M138 167l1-1 2 1-1 1v1h0l-2 1 1-1-1 1-1-1h-2v1-1l2-1 1-1z" class="J"></path><path d="M115 522c1-2 2-4 5-5s6 0 9 1c4 2 7 6 9 9v5c0 2 0 3 1 4v1h-1c0-1-5-7-6-8l-3-3h0l3 3c3 4 5 8 8 12v1c-2 0-2-1-3-1h0c0 1 1 2 2 2v1l-12-9c-1-2-6-6-8-7s-4-1-6-1l-1 1h-1v-1l4-4v-1z" class="q"></path><path d="M115 522h2 0c1 1 2 1 3 1l1 1v2l1 2c-1-1-2-1-3-2h-1 0c-2 0-3 0-5 1l-1 1h-1v-1l4-4v-1z" class="AD"></path><path d="M113 527c2-1 3-1 5-1h0 1c1 1 2 1 3 2 2 0 2 1 4 2 0 1 0 1 1 1l1 1 2 2h1c1 1 1 1 2 1 0-2-2-4-3-5l1-1v1l1-1c3 4 5 8 8 12v1c-2 0-2-1-3-1h0c0 1 1 2 2 2v1l-12-9c-1-2-6-6-8-7s-4-1-6-1z" class="X"></path><path d="M298 631l3 1h2 4v1c3 1 5 2 7 3s3 2 4 3c3 2 6 5 7 7l1 1h0l-1 1-5-6h-1l-1 1 3 3 1 1c1 2 3 5 3 7v1c0 1 0 2 1 3v8c-1 1-1 3-1 4-1 3-3 4-5 7l1-4h0v-6h0c0-3-1-5-2-7-1-4-3-8-6-11-3-5-8-8-12-12-2 0-4-3-6-4l6 3v-1l-1-1v-1h0l-2-1v-1z" class="q"></path><path d="M298 631l3 1h2 4v1c3 1 5 2 7 3s3 2 4 3c3 2 6 5 7 7l1 1h0l-1 1-5-6h-1l-1 1c-2-2-5-6-8-6-1 0-1 0-1 1l-7-3c2 2 4 4 6 5h0v1h-1l-5-4h-1c-2 0-4-3-6-4l6 3v-1l-1-1v-1h0l-2-1v-1z" class="I"></path><path d="M298 631l3 1h2 4v1c3 1 5 2 7 3h-1c-4-2-9-1-13-3h0l-2-1v-1z" class="E"></path><path d="M132 529l-3-3h0l3 3c1 1 6 7 6 8h1v-1l1 2h1c1 0 2 0 3-1 3 0 4 0 7 1h2v1c1 0 1 1 2 1l1 4v1h0l-1 1v1c0 1 0 1-1 2v1l-1-1s-2-1-3-1v1 1l1-1c1 0 2 1 2 1 0 1-1 2-2 2h-1 0c-2-1-3-3-4-4 0-1 0-1-1-2h0v3l-1-2v1l-2 1c-1 1-3 3-4 3s-2 1-3 1l1 1h-1v-1h-1l-1 1h-1-2c-1 1-1 2-2 2h0v-2h-1c-1-1-1-1-1-2l-8 3 1 2c0 1 0 3-1 4v1c-1 2-3 5-4 6h-1l-1 1c0 1-2 1-2 2-1-1-3-2-5-3h1c1-1 2-1 2-2 1-1 2-1 3-2 2-3 4-7 4-12-1-3-3-3-5-5v-1c2 1 4 2 5 4 1 1 1 1 2 1s2 0 3-1l8-2c1 0 4-1 4-1 1 0 1 0 1-1h0c1 0 1 0 2 1 1 0 4-1 4-2h0 1v-1h-1v-1c-1 0-2-1-2-2h0c1 0 1 1 3 1v-1c-3-4-5-8-8-12z" class="c"></path><path d="M125 550h0c3-1 5-2 9-2l-2 1-7 2v-1z" class="M"></path><path d="M125 550v1c-2 1-4 2-6 2-1 0-1 0-2 1h0l1 1 1 2h-2v-4c2-2 5-2 8-3z" class="a"></path><path d="M132 549h1c3 1 7-1 10-2l1 1-2 1c-1 1-3 3-4 3 1-1 1-1 1-3l-1 1h-1c-1 0-3 1-4 0-2 0-5 2-7 2l-8 3-1-1h0c1-1 1-1 2-1 2 0 4-1 6-2l7-2z" class="F"></path><path d="M133 550c1 1 3 0 4 0h1l1-1c0 2 0 2-1 3-1 0-2 1-3 1l1 1h-1v-1h-1l-1 1h-1-2c-1 1-1 2-2 2h0v-2h-1c-1-1-1-1-1-2 2 0 5-2 7-2z" class="p"></path><path d="M126 552c2 0 5-2 7-2-1 2-4 3-5 5v1-2h-1c-1-1-1-1-1-2z" class="N"></path><path d="M117 557h2c0 1 0 3-1 4v1c-1 2-3 5-4 6h-1l-1 1c0 1-2 1-2 2-1-1-3-2-5-3h1 1c2 0 8-6 9-8 1-1 1-2 1-3z" class="y"></path><path d="M144 541h0l1 1c1 0 2 0 3 1h0-1v1h0l1 2h0c1 0 1 1 2 2v1 1l1-1c1 0 2 1 2 1 0 1-1 2-2 2h-1 0c-2-1-3-3-4-4 0-1 0-1-1-2h0v3l-1-2v1l-1-1c-3 1-7 3-10 2h-1l2-1h0 1c2 0 4-1 5-2 1 0 2 0 3-1-1-1-1-1-2-1h0v-1c1-1 2-1 3-2z" class="b"></path><path d="M148 546c1 0 1 1 2 2v1h-1c-1-1-1-2-1-3z" class="I"></path><path d="M144 537c3 0 4 0 7 1h2v1c1 0 1 1 2 1l1 4v1h0l-1 1v1c0 1 0 1-1 2v1l-1-1s-2-1-3-1c-1-1-1-2-2-2h0l-1-2h0v-1h1 0c-1-1-2-1-3-1l-1-1h0l-1-1-1-1h-1v-1c1 0 2 0 3-1z" class="L"></path><path d="M141 538c1 0 2 0 3-1v1h3l1 1h-4v1h-1l-1-1h-1v-1z" class="G"></path><path d="M144 541l2-1c1 0 3 1 4 1-1 1-1 1-1 2h-1c-1-1-2-1-3-1l-1-1z" class="M"></path><path d="M148 546h0l-1-2h0v-1h1l3 3c1 1 2 2 3 2v1 1l-1-1s-2-1-3-1c-1-1-1-2-2-2z" class="V"></path><path d="M150 541c2 1 3 2 4 4v2c-2-1-4-3-5-4 0-1 0-1 1-2z" class="f"></path><path d="M144 537c3 0 4 0 7 1h2v1c1 0 1 1 2 1l1 4v1h-1v-1l-1-1c-1-2-4-4-6-4l-1-1h-3v-1z" class="M"></path><path d="M124 604h0c1 0 1 1 1 1l-1 1c0 1 0 1-1 1 0 3 1 4 2 5v1l3 3 2 2c-1 0-2 0-3 1-1-1-1-2-2-3l-1 1c-1-2-2-3-3-5 0 1-1 1-1 2 0 0 0 1-1 2h0l-1-1v1l-1 1c0 1 0 3-1 4l-1 1h-1c1-1 1-2 1-3-3 2-7 5-10 8s-5 6-8 9c-1 1-2 2-4 2h0c1-1 1-1 1-2 1-1 0-5 0-7l2-3c0-1 0-3 1-5l-1-1c1-3 4-6 8-8 1 0 3-2 5-2 1-1 2-1 3-2h0v-2h-1 8 0c2-1 3-1 5-2z" class="Y"></path><path d="M120 610c1 1 1 1 1 2s-1 1-1 2c0 0 0 1-1 2h0l-1-1c0-1 0-1 1-1 0-1 1-3 1-4z" class="K"></path><path d="M119 608c1 1-1 3-1 5l-1 1-1-1 1-3 2-2z" class="O"></path><path d="M103 625v-1l1-1 2 1v1l-4 3v-1-1l1-1z" class="X"></path><path d="M108 621h0c2 0 3-2 4-2 0-1 1 0 1 0-2 2-4 4-7 6v-1l-2-1 2-2h2z" class="e"></path><path d="M98 619l1 2-1 3v8l-2 4c-1-3 0-7 0-10 0-1 0-3 1-5l1-2z" class="q"></path><path d="M120 610h0c1-1 1-2 2-2 0 0 0 1 1 1v-2c0 3 1 4 2 5v1l3 3 2 2c-1 0-2 0-3 1-1-1-1-2-2-3l-1 1c-1-2-2-3-3-5 0-1 0-1-1-2z" class="V"></path><path d="M120 610h0c1-1 1-2 2-2 0 0 0 1 1 1v-2c0 3 1 4 2 5v1h0c-1-1-2-2-3-2v2l3 3-1 1c-1-2-2-3-3-5 0-1 0-1-1-2z" class="O"></path><path d="M101 617h0s1 0 1 1c1 0 1 0 2-1s3-2 5-2h0l-3 3c0 1-1 2-2 2l2 1-2 2-1 1v1l-1 1v1 1l-4 4v-8l1-3-1-2c1-1 2-1 3-2z" class="e"></path><path d="M99 621l1-2h0c0 2 0 4-1 5v1l-1-1 1-3z" class="O"></path><path d="M104 620l2 1-2 2-1 1v1l-3 3h-1v-1c1-1 2-3 3-4s2-2 2-3z" class="N"></path><path d="M119 606h0-1l1 2-2 2-1 3 1 1c-1 1-2 4-4 5 0 0-1-1-1 0-1 0-2 2-4 2h0-2l-2-1c1 0 2-1 2-2l3-3h0c-2 0-4 1-5 2s-1 1-2 1c0-1-1-1-1-1h0c-1 1-2 1-3 2l-1 2-1-1c1-3 4-6 8-8 1 0 3-2 5-2 1-1 2-1 3-2h0v-2h-1 8z" class="P"></path><path d="M111 617l3-3h0c0 1-3 5-4 6h-1l-1 1h-2l-2-1c1 0 2-1 2-2h1l1-1c1 0 1-1 2-1 0 2-2 2-3 3v1c2-1 3-2 4-3z" class="X"></path><path d="M114 609h1c1-1 2-2 4-1l-2 2c-2 1-4 2-5 4 0 1-1 2-2 2l1 1c-1 1-2 2-4 3v-1c1-1 3-1 3-3-1 0-1 1-2 1l-1 1h-1l3-3h0c-2 0-4 1-5 2s-1 1-2 1c0-1-1-1-1-1h0c1-1 3-2 5-4h2c1 0 1 0 2-1 2 0 3-1 4-3z" class="Q"></path><path d="M119 606h0-1l1 2c-2-1-3 0-4 1h-1c-1 2-2 3-4 3-1 1-1 1-2 1h-2c-2 2-4 3-5 4s-2 1-3 2l-1 2-1-1c1-3 4-6 8-8 1 0 3-2 5-2 1-1 2-1 3-2h0v-2h-1 8z" class="E"></path><path d="M106 613l1-1h1v1l1-1c2-2 2-3 5-3-1 2-2 3-4 3-1 1-1 1-2 1h-2z" class="G"></path><path d="M115 622l1-1 1 1c1 0 2 1 3 2l2 6c3 3 4 5 6 8v1c1 3 3 5 6 7 2 2 4 3 6 4 5 2 12 4 14 9v4l-2 2c-3 1-6 0-9-1-1-1-2-1-3-2-6-4-14-10-18-16-3-5-4-10-6-15v-6-2h-1v-1z" class="q"></path><path d="M115 622l1-1 1 1 1 8-2 1v-6-2h-1v-1z" class="s"></path><path d="M122 630c3 3 4 5 6 8v1c1 3 3 5 6 7l-1 1-3-3c-3-4-6-9-8-14z" class="b"></path><path d="M116 631l2-1c1 4 2 8 3 11 4 8 12 15 20 20 2 1 4 2 7 3h1s1 0 1-1h1 0c1 0 1-1 1-1h-2c-1 0-1 0-2-1-1 0-2-1-2-1 0-1 0-2-1-3 0-1 0-2 1-2v-1c-1-1-2-2-4-2-3-2-6-3-9-5l1-1c2 2 4 3 6 4 5 2 12 4 14 9v4l-2 2c-3 1-6 0-9-1-1-1-2-1-3-2-6-4-14-10-18-16-3-5-4-10-6-15z" class="C"></path><path d="M146 654c3 1 6 3 7 6v3c-1 1-2 1-4 1h-1 1s1 0 1-1h1 0c1 0 1-1 1-1h-2c-1 0-1 0-2-1h3v-1-1h0l-1-1c0-1-3-3-4-3v-1z" class="y"></path><path d="M146 655c1 0 4 2 4 3l1 1h0v1 1h-3c-1 0-2-1-2-1 0-1 0-2-1-3 0-1 0-2 1-2z" class="o"></path><path d="M319 628c5 0 9 1 14 2 7 1 14 2 20 2l-1 1 1 1 3 2c1 1 4 2 5 4-1 0-1 1-2 2l1 1c3 3 6 5 8 7-3 0-6-5-9-6-2-1-3-2-5-3-2-2-4-2-7-3 4 3 8 5 12 7 4 3 8 7 13 10 2 2 4 3 7 4 2 0 4 0 6-1h2-1c-7 7-20-6-26-10l-6-5c-3-2-7-4-10-5-2-1-4-1-6-1-1 1-1 1-2 1h-1c-1 0-2 1-3 2h0c1 0 2-1 3-1s2 1 2 1c6 3 11 7 15 10l10 7c3 2 8 5 12 6l3 1c4 1 7 0 10-3 2-1 4-3 4-6 0-1-1-2-1-2-1-1-2-2-3-1-2 0-2 1-3 2s-1 2-1 3c1 1 0 1 0 1-1 0-1-1-1-2 0-2 1-3 2-4s2-1 3-1 3 0 4 1 1 2 1 4-3 5-5 7c-9 7-22-2-29-8l-9-6c-4-3-9-8-14-8-1 0-2 0-2 1s1 2 2 3l7 6c2 1 4 3 6 5 3 4 7 9 6 14 0 2-1 4-3 6-2 1-4 2-6 1-1 0-3-2-3-3v-1c1 1 2 3 3 3 1 1 3 0 4 0 2-1 3-3 4-5 1-3-1-6-2-9-2-3-5-7-8-9-3-3-7-6-10-9-2-2-3-4-4-5-3-4-7-6-12-7v-1l-3-1v-1c1 0 2-1 3-1 1 1 3 1 4 1l-2-1z" class="B"></path><path d="M330 634l4 2c-1 0-3 1-5 2v-1l1-3z" class="Q"></path><path d="M329 637l-5-5 6 2-1 3z" class="X"></path><path d="M319 628c5 0 9 1 14 2 7 1 14 2 20 2l-1 1 1 1 3 2h-1c2 1 3 2 4 3 0 1-1 1-1 2h0c-1 1-2 0-3-1-2-1-6-4-8-3-2 0-4-1-6-1l-11-3-13-2-3-1v-1c1 0 2-1 3-1 1 1 3 1 4 1l-2-1z" class="n"></path><path d="M319 628c5 0 9 1 14 2 7 1 14 2 20 2l-1 1 1 1 3 2h-1c-1 0-3-2-4-2-5-2-11-2-17-3-4-1-8-2-13-2l-2-1z" class="AA"></path><path d="M120 463h1c8 0 16 2 23 8 3 2 7 7 7 12 0 3 0 5-2 7-2 1-3 3-5 3-1-1-2-1-2-2h0 2 2c2-2 4-4 4-6 1-5-2-8-4-11l-3 6c-1 2-4 3-6 3-1 0-2 0-3-1-2-2-3-6-5-9s-6-3-9-3c-2 0-5 0-7 1-7 1-13 5-17 11-2 3-4 7-3 11 1 2 2 4 4 6 2 0 2 0 4-1v-2h0l1-1v1 2c-1 1-1 2-3 2-1 0-2 0-3-1-4-2-4-7-5-11-1-5 4-12 7-15 5-7 14-10 22-10z" class="T"></path><path d="M117 466c-1 1-1 2-2 3h0c-2 1-4 1-7 2v-1c1-1 2-2 4-2l5-2z" class="f"></path><path d="M139 480h0 1c1-1 2-2 3-4 0-1 0-2-1-3v-1l-3-3h1c2 1 3 2 5 4-1 3-1 5-4 8h0c-1-1-1-1-2-1z" class="M"></path><path d="M117 466l3-1c2 0 3 1 4 1v1h-2l-1 1c-1 0-2 0-3 1h-3 0c1-1 1-2 2-3z" class="Q"></path><path d="M129 471c1-1 1-1 2 0h0c1 2 4 3 4 5v2h1v2h2 1c1 0 1 0 2 1-1 0-2 1-3 1-4 0-5-5-6-8l-3-3h0z" class="N"></path><path d="M131 465c1 0 2 0 3 1h1c2 1 4 2 5 3h-1l3 3v1c1 1 1 2 1 3-1 2-2 3-3 4h-1 0-1l3-3v-1h-2v-2-1c-1-1-2-2-3-2v-1h-1l-1-1h0-1v-1l1-1c-1 0-2-1-3-2z" class="d"></path><path d="M134 467l3 3h1l3 3v4-1h-2v-2-1c-1-1-2-2-3-2v-1h-1l-1-1h0-1v-1l1-1z" class="Q"></path><path d="M108 471c-5 1-11 7-13 12-2 2-2 4-3 6 0-3 1-6 2-9 2-3 4-6 7-9s8-5 12-6c-4 3-9 5-12 8h0c2-1 4-2 7-3v1z" class="N"></path><path d="M120 465c4-1 8-1 11 0 1 1 2 2 3 2l-1 1v1h1 0l1 1h1v1c1 0 2 1 3 2v1 2h2v1l-3 3h-2v-2h-1v-2c0-2-3-3-4-5h0c-1-1-1-1-2 0-2-1-3-1-5-2h-6c1-1 2-1 3-1l1-1h2v-1c-1 0-2-1-4-1z" class="f"></path><path d="M120 465c4-1 8-1 11 0 1 1 2 2 3 2l-1 1v1h1 0l1 1h1v1h0 0c0 1 1 2 1 3h0l-1-1c-1-1-1-2-2-2 0 0-1 0-1-1h-2c0-1-1-1-2-2h-1l-1-1h-1l-2-1c-1 0-2-1-4-1z" class="V"></path><path d="M159 269l2-1h2v2s-1 1-2 1l2 2h0l1-1c1 1 1 2 2 1h1 1l1 1s1 0 1 1h1c-1 1-1 1-2 1l-1 2h1 1c-3 3-5 6-8 7-1 1-1 1-1 2-1 1-2 2-3 2l-1 2c-2 2-4 4-5 6-1 0-1 0-2-1h0v-1c-1 0-1 0-2 1h0c0 1-1 1-1 2-1 1-2 3-3 3l-3 3-1-1c1-1 3-2 4-4 0 0 0-1 1-2l-1-1c-2 2-4 4-7 6v-1c1-2 2-3 3-5v-3c0-1 0-1-1-2h-1c0-1 1-2 2-3h-1c0-1-1-1-1-1 0-1 1-2 1-4h0c-1 0-1 1-2 0 0 0 3-3 4-3 1-1 2-4 3-4 1-1 2-1 3-2h4 0c2 0 2 0 3-1s4-3 5-4z" class="D"></path><path d="M139 291c1-1 0-1 1-1 2-1 3-3 5-4h0v1c-1 2-2 4-4 6h-1c0-1 0-1-1-2z" class="M"></path><path d="M157 274c0-1 3-3 4-3l2 2h0l-1 2-2 1c0-1 1-2 2-2v-1h-1c-1 1-3 3-5 3h0l1-1v-1z" class="O"></path><path d="M151 274c2 0 2 0 3-1v1h2c0 1-1 2-2 2-2 0-3 2-5 3 0-1-1-1-2-2 1 0 2-1 3-2l1-1h0z" class="P"></path><path d="M151 274c2 0 2 0 3-1v1c-1 0-1 1-2 1h-2l1-1h0z" class="V"></path><path d="M159 269l2-1h2v2s-1 1-2 1-4 2-4 3h-1-2v-1c1-1 4-3 5-4z" class="f"></path><path d="M147 277c1 1 2 1 2 2l-6 6c-1 1-2 3-3 3h-1c0-1-1-1-1-1 0-1 1-2 1-4h0 0 1v1c2 0 6-5 7-7z" class="a"></path><path d="M147 274h4l-1 1c-1 1-2 2-3 2-1 2-5 7-7 7v-1h-1 0c-1 0-1 1-2 0 0 0 3-3 4-3 1-1 2-4 3-4 1-1 2-1 3-2z" class="W"></path><path d="M163 273l1-1c1 1 1 2 2 1h1 1l1 1s1 0 1 1h1c-1 1-1 1-2 1h0-4c-1 0-1 1-3 1v-1l-2 2h1c0 1 0 1-1 2-2 1-3 3-5 4l-1 1h-1l-3 2-1-1 3-3c3-1 6-5 8-7l2-1 1-2z" class="P"></path><path d="M169 274s1 0 1 1h1c-1 1-1 1-2 1h0-4l1-1h0 1l2-1z" class="I"></path><path d="M165 276h4 0l-1 2h1 1c-3 3-5 6-8 7-1 1-1 1-1 2h-1c0-1 1-1 1-2l-2-2 2-2-1-1c1-1 1-1 1-2h-1l2-2v1c2 0 2-1 3-1z" class="Q"></path><path d="M164 280v1h1c0 1-1 1-1 2h-1v-2s0-1 1-1z" class="p"></path><path d="M163 279l1-1h1c0 1-1 1-1 2-1 0-1 1-1 1v2c0 1-1 2-1 2-1 1-1 1-1 2h-1c0-1 1-1 1-2l-2-2 2-2-1-1c1-1 1-1 1-2l2 1z" class="X"></path><path d="M161 278l2 1-2 2-1-1c1-1 1-1 1-2z" class="I"></path><path d="M145 287h1c2-3 5-7 7-9 0 2-2 3-2 4l1 1-3 3 1 1 3-2h1l-3 3c-1 1-2 1-3 3v1c0 1-4 3-4 4-2 2-4 4-7 6v-1c1-2 2-3 3-5v-3h1c2-2 3-4 4-6z" class="B"></path><path d="M149 286l1 1 3-2h1l-3 3c-1 1-2 1-3 3 0-1 0-1-1-1h-1v-1l2-1c0-1 0-1 1-2z" class="i"></path><path d="M160 280l1 1-2 2 2 2c0 1-1 1-1 2h1c-1 1-2 2-3 2l-1 2c-2 2-4 4-5 6-1 0-1 0-2-1h0v-1c-1 0-1 0-2 1h0c0 1-1 1-1 2-1 1-2 3-3 3l-3 3-1-1c1-1 3-2 4-4 0 0 0-1 1-2l-1-1c0-1 4-3 4-4v-1c1-2 2-2 3-3l3-3 1-1c2-1 3-3 5-4z" class="H"></path><path d="M145 297c1 0 2-1 2-2 1-2 3-4 5-5 0 1-1 1-1 2h2 0l-3 4v-1c-1 0-1 0-2 1h0c0 1-1 1-1 2-1 1-2 3-3 3l-3 3-1-1c1-1 3-2 4-4 0 0 0-1 1-2z" class="k"></path><path d="M160 280l1 1-2 2 2 2c0 1-1 1-1 2h1c-1 1-2 2-3 2-1 1-2 1-2 1l-1 1-1-1h0v-2h0c-1 1-1 1-2 1l-1-1 3-3 1-1c2-1 3-3 5-4z" class="p"></path><path d="M159 283l2 2c0 1-1 1-1 2h1c-1 1-2 2-3 2-1 1-2 1-2 1h-1c0-2 1-2 2-4h0-2v-1c1-1 2-1 4-1v-1z" class="e"></path><path d="M159 283l2 2c0 1-1 1-1 2h-2c-1 0-1 0-1-2l2-1v-1z" class="N"></path><defs><linearGradient id="C" x1="101.839" y1="163.644" x2="113.006" y2="178.96" xlink:href="#B"><stop offset="0" stop-color="#1d1d1d"></stop><stop offset="1" stop-color="#383738"></stop></linearGradient></defs><path fill="url(#C)" d="M113 155s1 1 1 2l1 2 1-1 1 1h0c2 1 2 2 2 4v2h-1l-1 1 1 2h0v3h1l2 1h-1c-1 0-1 1-2 2l-1 2c0 2-1 3 0 5-1 1-1 3-2 4l-1 2-2 1h-1c0 1-1 1-1 2-1 0-2-1-3-1h0-2l1 1-2 1c-1 0-2-1-3 0-1 2-2 4-1 6h-1c-1 1-1 1-1 2-1 1 0 3-1 4v2h-2l-1 1h-1c-1-1-2-1-4-2l-2-1c0-1 1-2 1-2l-1-2c2-3 5-7 7-9h2c0-1 1-1 2-2v-2l2-2s1-1 1-2v-1c0-4 1-6 2-9 2-4 4-8 6-11 0-2 1-3 2-4 0-1 1-2 2-2z"></path><path d="M114 163h1l1 2v4l-1-1c0 1 0 1-1 2h0v3c-1 2-1 3-1 4v-4-2s-1-1 0-2v-4-1l1-1z" class="AC"></path><path d="M114 163h1l1 2v4l-1-1c0 1 0 1-1 2h0v-2c1-1 0-3-1-4l1-1z" class="d"></path><path d="M114 163c-1-2-1-4 0-6l1 2 1-1 1 1h0c2 1 2 2 2 4v2h-1l-1 1 1 2-1 1h-1 0v-4l-1-2h-1z" class="L"></path><path d="M117 160c0 2 1 4 0 6l1 2-1 1h-1 0v-4-1l1-1v-3z" class="B"></path><path d="M114 163c-1-2-1-4 0-6l1 2 1-1 1 1v1 3l-1 1v1l-1-2h-1z" class="k"></path><path d="M116 158l1 1v1 3l-1 1-1-5 1-1z" class="g"></path><path d="M118 168h0v3h1l2 1h-1c-1 0-1 1-2 2l-1 2c0 2-1 3 0 5-1 1-1 3-2 4l-1 2-2 1h-1c0 1-1 1-1 2-1 0-2-1-3-1 1-1 2-2 2-3l1-1v-1-3c1-1 2-1 2-2s0-1 1-2c0-1 0-2 1-4v-3h0c1-1 1-1 1-2l1 1h0 1l1-1z" class="Q"></path><path d="M118 168h0v3h1l2 1h-1c-1 0-1 1-2 2l-1 2-1-7h1l1-1z" class="u"></path><path d="M118 171h1l2 1h-1c-1 0-1 1-2 2v-3z" class="K"></path><path d="M114 170h0c1 3 1 6-1 8v2c0 1-2 2-2 3l-1 1v-3c1-1 2-1 2-2s0-1 1-2c0-1 0-2 1-4v-3z" class="H"></path><path d="M111 183h0c2-1 4-4 5-6 0 2 0 5-1 6h-1v1l1 1-1 2-2 1h-1c0 1-1 1-1 2-1 0-2-1-3-1 1-1 2-2 2-3l1-1v-1l1-1z" class="o"></path><path d="M107 189c1-1 2-2 2-3 1 1 1 2 2 2h0c0 1-1 1-1 2-1 0-2-1-3-1z" class="I"></path><path d="M101 182v2c1 1 1 1 2 1 0 0 2-2 3-2v-1c2 0 2 0 3 1 0 1 1 1 1 2l-1 1c0 1-1 2-2 3h0-2l1 1-2 1c-1 0-2-1-3 0-1 2-2 4-1 6h-1c-1 1-1 1-1 2-1 1 0 3-1 4v2h-2l-1 1h-1c-1-1-2-1-4-2l-2-1c0-1 1-2 1-2l-1-2c2-3 5-7 7-9h2c0-1 1-1 2-2v-2l2-2s1-1 1-2z" class="B"></path><path d="M107 189v-2-1c1-1 2-1 3-1l-1 1c0 1-1 2-2 3h0z" class="m"></path><path d="M88 201v-1c2-1 3-4 5-6 1 1 2 3 3 4v2c-1 2-2 3-3 4v1 1c-1-1-2-1-4-2l-2-1c0-1 1-2 1-2z" class="X"></path><path d="M91 199c1 0 1 0 3 1v1c-1 0-2 1-2 1-1 1-1 1-2 1v-1c0-1 0-2 1-3z" class="N"></path><path d="M64 541v-2c1 1 1 2 1 2l1-1 3 3 1 2c0 1 1 2 1 2 1 2 2 4 3 5l1 1 1 2c1 1 3 2 3 3h3c0-1 0-1-1-2h0c-1-1-2-2-2-3-1-1-2-2-2-3l2 1c1 1 2 4 4 5 5 6 12 10 18 14h0 1c1 1 2 2 4 3 5 4 16 6 18 14h-2v1h-2l-8-4c-2 0-4-1-6-2-6-2-11-6-16-9-1-1-3-2-5-3-1 0-2-1-4-1l-9-3v-1c1-2-3-6-4-9-1-4-4-8-5-13h1v-2z" class="y"></path><path d="M83 561l5 4c-2 1-2 0-3 2h0c-1 0-2-1-3-2l1-1v-3z" class="X"></path><path d="M113 582c3 2 6 4 9 5v1h-2l-8-4c1 0 2 0 3 1s2 1 2 1h1c-1-1-4-2-5-3v-1z" class="AD"></path><path d="M64 541v-2c1 1 1 2 1 2 3 10 9 17 17 24v-4l1-1v1 3l-1 1c1 1 2 2 3 2v1h-2c-8-6-16-15-18-25 0-1 0-1-1-2h0z"></path><defs><linearGradient id="D" x1="104.409" y1="581.64" x2="106.304" y2="571.394" xlink:href="#B"><stop offset="0" stop-color="#1d2120"></stop><stop offset="1" stop-color="#4e494d"></stop></linearGradient></defs><path fill="url(#D)" d="M113 582s-2-1-3-1c-7-4-15-7-20-14l6 3c2 1 5 3 8 4 2 2 5 3 8 4l5 3h1c-3-3-8-4-12-7-2-1-3-3-5-4h0 1c1 1 2 2 4 3 5 4 16 6 18 14h-2c-3-1-6-3-9-5z"></path><path d="M146 147c1 0 2-1 4 1h0 1 1l1 1h0v-2c1 1 1 1 1 2h3v-1c1 1 1 1 1 2 1 0 1 0 2-1v1l1 1h1v1h2 0v1l2 2h0l-1 1h-1v1c1 0 2-1 4-1v1c-1 1-2 1-3 1-1 1 0 1-1 1h3 0l-1 1c-2 0-3 1-4 1h-2v2h0 1l1 1h1l1 1 1 1-2 2c1 0 2 0 2 1l-2 1v1c-2 0-3 1-4 1-2 0-3 1-4 1h-3v1h2l-4 1h-1c0 1-1 1-2 1h0-1c-2 1-4 1-6 1l-1-1h-1-1l1-1c1 0 1 0 1-1-1 0-1-1-2-1l-1-2c0-1 0-1-1-1v-1h2l1 1 1-1-1 1 2-1h0v-1l1-1 1-1c0-1 0-2-1-3-1 0-2 1-3 2v-2h-1 0l-1-2 2-2 2-2-1-1-2 3-2-3 3-3c2-2 5-4 7-6h1z" class="d"></path><path d="M138 159c0 1 1 1 1 2l-2 2-1-2 2-2z" class="P"></path><path d="M164 157c1 0 2-1 4-1v1c-1 1-2 1-3 1-1 1 0 1-1 1h3 0l-1 1c-2 0-3 1-4 1h-2v2h0 1l1 1c-2 0-3 0-5 1l-1-1h0-2l-1-1h-1l-1 1c-1-1-2-1-3 0v-1l-1-1v-1h-1v-1h1l1 1c1 0 0-1 1-1h3c1-1 1-1 2-1s3-1 4-1h4l2-1z" class="G"></path><path d="M154 162c1-1 2-1 4-2v1l-1 1h-3z" class="P"></path><path d="M164 159h3 0l-1 1c-2 0-3 1-4 1h-2-2v-1c2 0 5 0 6-1z" class="f"></path><path d="M158 161h2v2h0 1l1 1c-2 0-3 0-5 1l-1-1h0-2l-1-1h-1l-1 1c-1-1-2-1-3 0v-1-1l1 1c2 0 1-1 3-1 1 0 1 1 2 0h3l1-1z" class="O"></path><path d="M158 161h2v2h0 0c-1 0-2 0-3-1l1-1zm-4-12h3v-1c1 1 1 1 1 2 1 0 1 0 2-1v1l1 1h1v1h2 0v1l2 2h0l-1 1h-1l-11 2v1h-3l-1-1 3-2c0-1-1-2-1-2-1 1-2 1-4 1h0l1-1c1 0 1 0 2-1 0 1 2 0 3 0l1-1c1 0 1 0 1-1s0-1-1-2z" class="K"></path><path d="M153 158l1-1c3-1 6-1 8-2h2l1 1h-1l-11 2zm5-5h1c1 0 2 0 3-1 0 1 1 1 1 1 0 1 0 1-1 1-3 0-6 2-9 2v-1s1-1 2-1 2 0 3-1z" class="P"></path><path d="M154 149h3v-1c1 1 1 1 1 2 1 0 1 0 2-1v1l1 1v1h-1 0v-1h-1c0 1-1 1-1 2-1 1-2 1-3 1s-2 1-2 1l-1 1c0-1-1-2-1-2-1 1-2 1-4 1h0l1-1c1 0 1 0 2-1 0 1 2 0 3 0l1-1c1 0 1 0 1-1s0-1-1-2z" class="b"></path><path d="M146 147c1 0 2-1 4 1h0 1 1l1 1h0v-2c1 1 1 1 1 2 1 1 1 1 1 2s0 1-1 1l-1 1c-1 0-3 1-3 0-1 1-1 1-2 1l-1 1c-3 2-5 4-8 6 0-1-1-1-1-2l2-2-1-1-2 3-2-3 3-3c2-2 5-4 7-6h1z" class="K"></path><path d="M150 151h0v1s-1 0-2 1v-1l2-1z" class="P"></path><path d="M147 150h4v1h-1l-2 1c-3 1-6 4-8 5l-1-1c2-2 5-4 8-6z" class="G"></path><path d="M138 153c2-2 5-4 7-6h0c1 1 2 1 3 1h0l-1 2c-3 2-6 4-8 6l-2 3-2-3 3-3z" class="f"></path><path d="M145 163l2-1 1 1v1c1-1 2-1 3 0l1-1h1l1 1h2 0l1 1c2-1 3-1 5-1h1l1 1 1 1-2 2c1 0 2 0 2 1l-2 1v1c-2 0-3 1-4 1-2 0-3 1-4 1h-3v1h2l-4 1h-1c0 1-1 1-2 1h0-1c-2 1-4 1-6 1l-1-1h-1-1l1-1c1 0 1 0 1-1-1 0-1-1-2-1l-1-2c0-1 0-1-1-1v-1h2l1 1 1-1-1 1 2-1h0v-1l1-1 1-1 2-2s1 0 1-1h0z" class="O"></path><path d="M149 175h-1 0v-1l4-1v1h2l-4 1h-1z" class="b"></path><path d="M155 173c-1-1-2-1-2-1h-1c1-2 3-1 4-2 2-1 3-1 4-1-1 1-1 1-1 3-2 0-3 1-4 1z" class="G"></path><path d="M145 168l2 1-2 2h0 0l1 1v1c-1 0-2 0-2-1l-2 1h0l-2-1c1 0 1 0 2-1v-1l3-1v-1z" class="J"></path><path d="M145 168l2 1-2 2h0v-1c-1 1-1 0-1 1h-1l-1-1 3-1v-1z" class="r"></path><path d="M156 164l1 1-2 1h1l-2 2c0 1 0 1 1 1h-2l-3 2h-2c-1 0-2 1-3 0l2-2 2 1s1 0 1-1c1 0 2-1 2-3 2 0 3-1 4-2z" class="G"></path><path d="M140 169l2 2c-1 1-1 1-2 1l2 1h0c1 1 3 2 4 3-2 1-4 1-6 1l-1-1h-1-1l1-1c1 0 1 0 1-1-1 0-1-1-2-1l-1-2c0-1 0-1-1-1v-1h2l1 1 1-1-1 1 2-1z" class="b"></path><path d="M139 174c1 0 1 1 2 1v1h-2-1-1l1-1c1 0 1 0 1-1z" class="M"></path><path d="M140 169l2 2c-1 1-1 1-2 1h-2v-1-1l2-1z" class="h"></path><path d="M162 164h1l1 1 1 1-2 2c1 0 2 0 2 1l-2 1v1c-2 0-3 1-4 1 0-2 0-2 1-3h1v-1c-1-1-1-1-2 0-2 0-3 1-4 1s-1 0-1-1l2-2h-1l2-1c2-1 3-1 5-1z" class="K"></path><path d="M156 166h1l-2 2h2 2c-2 0-3 1-4 1s-1 0-1-1l2-2z" class="t"></path><path d="M145 163l2-1 1 1v1c1-1 2-1 3 0l1-1h1l1 1h2 0c-1 1-2 2-4 2 0 2-1 3-2 3 0 1-1 1-1 1l-2-1-2-1v1l-3 1v1l-2-2h0v-1l1-1 1-1 2-2s1 0 1-1h0z" class="H"></path><path d="M148 164c1-1 2-1 3 0-1 1-2 1-3 1h-1-1v-1h2z" class="L"></path><path d="M141 167c1 1 1 1 2 1h1l1 1-3 1v1l-2-2h0l1-2z" class="D"></path><path d="M152 163h1l1 1h2 0c-1 1-2 2-4 2-1 0-2 0-3 1 1-1 3-2 4-3l-1-1z" class="Z"></path><path d="M149 167c1-1 2-1 3-1 0 2-1 3-2 3 0 1-1 1-1 1l-2-1-2-1c2-1 3 0 4-1z" class="J"></path><path d="M145 163l2-1 1 1v1h-2v1l-5 2-1 2v-1l1-1 1-1 2-2s1 0 1-1h0z" class="G"></path><path d="M145 163l2-1 1 1v1h-2l-1-1z" class="K"></path><path d="M195 598h10 3l-1 1h0l1 1h1c6 1 12 3 17 5h-3l1 1s0 1 1 1c5 4 9 10 10 17 0 3-1 6-3 9-1 1-2 1-3 1-2 0-3 0-4-1s-1-2-2-3c2 0 2 2 4 3h1c1 0 2 0 3-1s2-3 2-5c0-3 0-9-2-12-1 0-2-1-2-1-2-1-5-1-7-1h-11l-30-1 1-5c-3 1-5 1-7 2l-1-1h0c-4 2-9 5-12 8l-6-3 6-2c1 0 2-1 2-2l9-4-2-1 9-3 15-3z"></path><path d="M195 598h10 3l-1 1h0l1 1h1c6 1 12 3 17 5h-3l1 1s0 1 1 1c-5-3-10-4-16-5-12 0-24 2-35 6h0c-4 2-9 5-12 8l-6-3 6-2c1 0 2-1 2-2l9-4-2-1 9-3 15-3z" class="n"></path><path d="M209 600c6 1 12 3 17 5h-3l1 1s0 1 1 1c-5-3-10-4-16-5h2 1 2c0 1 0 1 1 0 1 1 3 1 4 1-1 0-5-1-6-2-1 0-2 0-3-1h-1z" class="o"></path><path d="M195 598h10 3l-1 1h0l1 1c-12-1-24-1-35 5l-2-1 9-3 15-3z" class="B"></path><path d="M158 510c0-2 1-4 2-5 2 0 4 1 5 2s2 2 2 3-1 2-1 3c0 0 2 3 3 3l4 3 3-6h1l-5 11c-1 1-2 3-3 5-1 1-2 2-2 3 0 0 0 1 1 0l8-2c0 1 1 1 1 3v2c2 1 3 1 4 2 1 2-1 6-2 8s-3 2-5 3h-5c4 2 6 3 9 7l-1 1c-1-1-1-3-2-4-4-4-13-4-18-4-1 0-1 0-2-1v-1l1-1h0v-1l-1-4c-1 0-1-1-2-1v-1h-2v-2h0l1-1-2-1v-2s-1 0-1-1v-1c2 2 3 4 5 5 1-1 0-4 1-6 0-2 1-4 1-7 1-4 2-8 2-12z" class="m"></path><path d="M159 540h1l1 1c0 1-1 2-1 3l-1-1h0v-1-2z" class="H"></path><path d="M166 535c3-2 5-2 8-3h1 1v2c-1 1-3 1-3 2h-1-1c-1 1-3 0-5-1z" class="p"></path><g class="q"><path d="M167 529v-8l-1-7 3 4 4 2c-1 3-4 7-6 9z"></path><path d="M162 506c1 1 3 2 4 3 0 1 0 1-1 2s0 3 0 5c1 4 1 8 1 13h1c-1 1-1 2-2 3h0c-1 3-3 6-4 9l-1-1 1-15c0-3 1-8 0-10v-5c0-1 0-2 1-4z"></path></g><path d="M166 529c-1-2-1-4-1-6-2-3-1-9-1-13v-1l1 1v1c-1 1 0 3 0 5 1 4 1 8 1 13z" class="e"></path><path d="M166 535c2 1 4 2 5 1h1 1 0 3v1h2l-1 1v1s1 0 1-1c1 0 1 0 1-1l1 1c0 3-1 5-3 8l-7 2h-1-3l-8-1 2-2 4-6 2-4z" class="q"></path><path d="M164 539c1 1 1 2 0 3v1l-1 1s-1 1-3 1l4-6z" class="N"></path><path d="M159 511c0-2 1-4 2-5h1c-1 2-1 3-1 4v5c1 2 0 7 0 10l-1 15h-1v2 1h0l1 1-1 1h-1-2 0v-1l-1-4c-1 0-1-1-2-1v-1h-2v-2h0l1-1-2-1v-2s-1 0-1-1v-1c2 2 3 4 5 5 1-1 0-4 1-6 0-2 1-4 1-7 1-4 2-8 2-12l1 1z" class="e"></path><path d="M158 529c0-1 1-3 1-5l1 11c0 2-1 3-1 5v2l-1 2-1-1v-2c0-2 1-3 1-5 0-1-1-2 0-3v-4z" class="N"></path><path d="M158 510l1 1-3 16h1v2h1v4c-1 1 0 2 0 3 0 2-1 3-1 5v2l1 1 1-2v1h0l1 1-1 1h-1-2 0v-1l-1-4c-1 0-1-1-2-1v-1h-2v-2h0l1-1-2-1v-2s-1 0-1-1v-1c2 2 3 4 5 5 1-1 0-4 1-6 0-2 1-4 1-7 1-4 2-8 2-12z" class="D"></path><path d="M151 536h0l1-1c1 1 2 2 2 3l-1 1v-1h-2v-2z" class="p"></path><path d="M156 527h1v2h1v4c-1 1 0 2 0 3 0 2-1 3-1 5v2-2c-1-3-2-7-1-10v-4z" class="f"></path><path d="M156 527h1v2h0v3c0 2 0 3-1 4-1-1 1-4 0-5v-4z" class="o"></path><path d="M188 374c1 0 1 0 1 1l-1 1h1v2h1 2c2-1 5-1 7-1v1h2v1l1 1h-2-1l-1 1c-1 2-1 3-2 4l-1 1v1c0 1-1 1-1 2l1 1c-1 1-1 2-2 4l-3 6h0l-1-1-1 1-1 1-1 1-2 2c0-2 0-3 1-4v-1c-1 1-3 0-4 1l-1 1-1 1h0c-1 2-2 3-2 4l-1 3-2-1-1 1c-1 0-1-1-1-1-1 2-2 4-4 6-1-1-1-2-1-3v-1-1l2-3v-1h0c-1 0-2 4-3 5v-1l-1 1c0 1-2 2-3 3h0c0-1 1-2 1-2-1-1-1-2-1-3l-1-1v-2c0-1 0-1 1-2l-1-1h0-1-1v-1h-1c1-1 1-2 2-3v-1c1-1 1-1 0-2l1-1 2-3h0c0 1 0 1 1 2l2-4h2 1c1-1 2-3 3-4l1 1v-1c0-1 1-1 0-2l1 1v1h1 0 1v1h1v-1-1c1-1 1-3 2-4v-1h2 1l3-3 1-1 1-1h1z" class="R"></path><path d="M190 383h1l-2 2c0-1 0-1 1-2z" class="E"></path><path d="M172 408l1-1h1v1l-1 1c-1 0-1-1-1-1z" class="B"></path><path d="M174 407c1 0 2-3 2-4h1-1c0 2 0 2 1 3l-1 3-2-1v-1z" class="U"></path><path d="M173 392c1 2 1 2 0 4h0v1l-1 4h-1v-1c-1-1-1 0-2 0 1-2 2-4 3-5l1-3z" class="B"></path><path d="M176 386h1c0 1 1 1 2 2h0l-1 2c-1 2-2 4-4 6 0 0 0 1-1 1v-1h0c1-2 1-2 0-4l3-6z" class="U"></path><path d="M174 396v-1c0-1 0-3 1-4 0-1 1-2 2-2l1 1c-1 2-2 4-4 6z" class="x"></path><path d="M188 388h1c-1 1-1 2-1 2l-2 3c0 2-1 3-2 5h-1l-1-1-1 3-1 1-1-1c1-1 2-2 2-4l4-6c1-2 1-2 3-2z" class="D"></path><path d="M189 385l2-2v2l-2 4-3 8c0 1 1 1 1 1v1h1-1v2l-1 1-2 2c0-2 0-3 1-4v-1c-1 1-3 0-4 1l1-3 1 1h1c1-2 2-3 2-5l2-3s0-1 1-2h-1v-1c1 0 1-1 1-1v-1z" class="W"></path><path d="M189 389l2 1c-1 1-1 2-1 3h-1v2h0l1-1 1 1 1-1h1l-3 6h0l-1-1-1 1-1 1v-2h1-1v-1s-1 0-1-1c1-2 2-5 3-8z" class="H"></path><path d="M187 398c1 0 2-2 3-2 0 1 0 1-1 2l-1 1v1l-1 1v-2h1-1v-1z" class="l"></path><path d="M193 382h2 0v1l1 1h-1v2 1c0 1-1 1-1 2l1 1c-1 1-1 2-2 4h-1l-1 1-1-1-1 1h0v-2h1c0-1 0-2 1-3l-2-1 2-4 2-3z" class="L"></path><defs><linearGradient id="E" x1="194.629" y1="383.081" x2="189.203" y2="388.196" xlink:href="#B"><stop offset="0" stop-color="#585857"></stop><stop offset="1" stop-color="#6d6d6f"></stop></linearGradient></defs><path fill="url(#E)" d="M193 382h2 0v1s-1 0-1 1l-3 6-2-1 2-4 2-3z"></path><path d="M169 398c0-1 1-2 1-2 0-1 1-2 1-3 0 1 0 1 1 2-1 1-2 3-3 5 0 1-1 2-2 3 0 1-1 3-2 4l-2 4c-1-1-1-2-1-3l-1-1v-2c0-1 0-1 1-2l-1-1h0c1-1 2-2 4-3l1-1h3z" class="L"></path><path d="M164 404c0 1-1 2 0 3h0 1l-2 4c-1-1-1-2-1-3l-1-1 3-3z" class="n"></path><path d="M166 398h3c-1 2-2 3-3 4s-2 1-2 2h0l-3 3v-2c0-1 0-1 1-2l-1-1h0c1-1 2-2 4-3l1-1z" class="E"></path><path d="M173 383l1 1v1h1 0 1v1l-3 6-1 3c-1-1-1-1-1-2 0 1-1 2-1 3 0 0-1 1-1 2h-3l-1 1c-2 1-3 2-4 3h-1-1v-1h-1c1-1 1-2 2-3v-1c1-1 1-1 0-2l1-1 2-3h0c0 1 0 1 1 2l2-4h2 1c1-1 2-3 3-4l1 1v-1c0-1 1-1 0-2z" class="G"></path><path d="M160 402c1-2 2-3 4-4l1 1c-2 1-3 2-4 3h-1z" class="I"></path><path d="M168 391l2 1c-2 2-3 4-4 6l-1 1-1-1c1-2 3-4 4-7z" class="a"></path><path d="M160 395l1-1 2-3h0c0 1 0 1 1 2-2 3-3 6-5 8h-1c1-1 1-2 2-3v-1c1-1 1-1 0-2z" class="P"></path><path d="M173 383l1 1v1h1 0 1v1l-3 6-1 3c-1-1-1-1-1-2 0 1-1 2-1 3 0 0-1 1-1 2h-3c1-2 2-4 4-6l-2-1 1-2c1-1 2-3 3-4l1 1v-1c0-1 1-1 0-2z" class="C"></path><path d="M169 389c1-1 2-3 3-4l1 1-3 6-2-1 1-2z" class="Q"></path><path d="M175 385h1v1l-3 6-1 3c-1-1-1-1-1-2s0-1 1-2 2-4 3-6z" class="O"></path><path d="M188 374c1 0 1 0 1 1l-1 1h1v2h1 2c2-1 5-1 7-1v1h2v1l1 1h-2-1l-1 1c-1 2-1 3-2 4l-1 1v-2h1l-1-1v-1h0-2l-2 3v-2h-1v-1l-1-1c-1 1-1 2-2 3v2s-1 0-1 1v1h-1-1c-1 0-1 1-2 2-1 2-3 6-5 7v-1c1-1 3-2 3-4v-1l-1 1-1 1v-1l2-3c-1-1 0-1-1-1h0c-1-1-2-1-2-2v-1-1c1-1 1-3 2-4v-1h2 1l3-3 1-1 1-1h1z" class="u"></path><path d="M186 379h2v1h-1l-1-1z" class="m"></path><path d="M183 381c1 1 1 1 1 2h-2l1-2z" class="i"></path><path d="M199 378h2v1l1 1h-2l-1-2z" class="Y"></path><path d="M190 378h2v2c-1 0-1 0-2-1v-1z" class="B"></path><path d="M195 381c0-1-1-2-1-3 1-1 2 0 3 0l1 1-3 2z" class="U"></path><path d="M198 379h0v1 1c-1 2-1 3-2 4l-1 1v-2h1l-1-1v-1h0-2c1 0 1-1 2-1l3-2z" class="AC"></path><path d="M188 374c1 0 1 0 1 1l-1 1h1v2l-6 1h-1l3-3 1-1 1-1h1z" class="a"></path><path d="M188 374c1 0 1 0 1 1l-1 1-2-1 1-1h1z" class="P"></path><path d="M179 379h2c1 1 2 1 2 2h0l-1 2c-1 1-1 2-2 3h0c0 1 0 1-1 2-1-1-2-1-2-2v-1-1c1-1 1-3 2-4v-1z" class="v"></path><path d="M177 385h2l1 1c0 1 0 1-1 2-1-1-2-1-2-2v-1zm-63-198h1c0 1 1 0 1 1-1 0-2 1-2 2v1c1 0 2 1 4 1h0c-2 1-2 3-2 5h-1c1 1 1 2 1 3v2h0c-1 1-1 1-1 2l-1 1v1h-1v2h-1l-1 1 2 2h1 0c1 0 2 0 3 1 0 0 0-1 1-1h0l1 1c1 0 2 0 4 1-1 0-1 1-2 1h0c0 1 0 1 1 2h-3l2 2h-2-1-2-2c-1 0-2-1-3-1h0l-2 2c1 1 1 1 0 1v1h-3-1v1h0c-1 0-2 1-3 1h0c-1 0-1 1-2 1l1 1 1 1h1v3h1v4l-4-3-2-1-3-3c-5 4-9 9-13 14h0c2-4 5-7 8-10 1-1 4-4 4-5l-2-2c-7 2-12 9-17 14l-13 12v-1c3-4 8-8 12-11l10-10c1-1 6-4 7-6l-2-2c0-1 0-1-1-1l1-2 2 1h0c1-1 0-3 0-5l1-1c-1 0-2 1-3 2l-1-2v-3-2c1-1 1-1 1-2 2 1 3 1 4 2h1l1-1h2v-2c1-1 0-3 1-4 0-1 0-1 1-2h1c-1-2 0-4 1-6 1-1 2 0 3 0l2-1-1-1h2 0c1 0 2 1 3 1 0-1 1-1 1-2h1l2-1z" class="g"></path><path d="M103 207c0 1 1 1 1 2-1 0-1 1-2 1 0-1 0-1 1-3z" class="Y"></path><path d="M97 224c1-1 1-1 2-1l1 1 1 1-1 1h0l-1 1h0v-1c0-1-1-2-2-2z" class="H"></path><path d="M100 226l2 1c0 1 0 1-1 2l-1 1-2-1c1-1 1-1 1-2l1-1z" class="AB"></path><path d="M100 226h0l1-1 1 1h1v3c0 1 0 1-1 1l-1-1c1-1 1-1 1-2l-2-1z" class="C"></path><path d="M100 230l1-1 1 1c1 0 1 0 1-1h1v4l-4-3z" class="g"></path><path d="M97 205v-2c1-1 0-3 1-4 0-1 0-1 1-2 0 1-1 2 0 2v2c1 0 2 0 3-1 1 1 1 1 2 1 1 1 1 1 2 1v1h1l-1 2 1 1h-1l-2 1c1-1 1-2 1-3h-1c-1-1-1-2-2-2h-3v-1l-1 1v3h0-1 0z" class="U"></path><path d="M88 208c3 0 7 0 10 1-1 1-2 1-3 1-1 1-2 1-3 1s-2 1-3 2l-1-2v-3z" class="N"></path><path d="M107 206l1-1v1h2c1 0 1 1 2 2l-1 1 2 2c-1 0-2 0-3-1l-2 1h0l1 1s-1 0-1 1h0l-1 1c-2-1-1-1-3-1h-3l-1 1v-1h-2-1c1 0 3-1 3-1 1 0 1-1 2-2 1 0 1-1 2-1 0-1-1-1-1-2h1l2-1h1z" class="E"></path><path d="M109 210l-1-2h1l1 1-1 1z" class="i"></path><path d="M110 209h1l2 2c-1 0-2 0-3-1h-1l1-1z" class="F"></path><path d="M104 207l2-1v1h1l-2 2h-1c0-1-1-1-1-2h1z" class="B"></path><path d="M105 209h0c0 1-1 1-1 2h1 1v1h1l1-1 1 1s-1 0-1 1h0l-1 1c-2-1-1-1-3-1h-3l-1 1v-1h-2-1c1 0 3-1 3-1 1 0 1-1 2-2 1 0 1-1 2-1h1z" class="T"></path><path d="M105 211h1v1h1l1-1 1 1s-1 0-1 1h0l-1 1c-2-1-1-1-3-1h-3v-1h2c1 0 1-1 2-1z" class="G"></path><path d="M110 210c1 1 2 1 3 1h1 0c1 0 2 0 3 1 0 0 0-1 1-1h0l1 1c1 0 2 0 4 1-1 0-1 1-2 1h0c0 1 0 1 1 2h-3l2 2h-2-1-2-2c-1 0-2-1-3-1 0 0 0-1-1-1l-2-1c-1 0-1 0-2 1-1-2-1-2-2-2v-1c2 0 1 0 3 1l1-1h0c0-1 1-1 1-1l-1-1h0l2-1z" class="f"></path><path d="M110 216c1-1 1-2 2-2s1 0 2 1l-1 2h2 1v-1h3 0l2 2h-2-1-2-2c-1 0-2-1-3-1 0 0 0-1-1-1z" class="I"></path><path d="M110 210c1 1 2 1 3 1h1 0c1 0 2 0 3 1 0 0 0-1 1-1h0l1 1c1 0 2 0 4 1-1 0-1 1-2 1h0c0 1 0 1 1 2h-3 0-3c-1-1-1-1-1-2-1-1-2-1-4-1h-3 0c0-1 1-1 1-1l-1-1h0l2-1z" class="W"></path><path d="M111 213h4 1 3 2v1c0 1 0 1 1 2h-3 0-3c-1-1-1-1-1-2-1-1-2-1-4-1z" class="r"></path><path d="M119 216c-1-1-1 0-2-1 1-1 2-1 4-1 0 1 0 1 1 2h-3 0z" class="B"></path><path d="M101 213h3v1c1 0 1 0 2 2 1-1 1-1 2-1l2 1c1 0 1 1 1 1h0l-2 2c1 1 1 1 0 1v1h-3-1v1h0c-1 0-2 1-3 1h0c-1 0-1 1-2 1l-1-1c-1 0-1 0-2 1l-2-3c-1-1-1-2-1-4v-1c0-2 0-1 2-2l1-1h1 2v1l1-1z" class="H"></path><path d="M100 220l5 1v1h0c-1 0-2 1-3 1h0c-1-1-2-2-2-3zm-6-3c1 1 2 1 2 2 1 1 1 1 1 2h-2c-1-1-1-2-1-4z" class="V"></path><path d="M100 218h0 1c1-1 2-1 3-1v2l1 1c-1 0-2 0-3-1l-2-1z" class="J"></path><path d="M97 221v1l2-2h1c0 1 1 2 2 3-1 0-1 1-2 1l-1-1c-1 0-1 0-2 1l-2-3h2z" class="K"></path><path d="M106 216c1-1 1-1 2-1l2 1c1 0 1 1 1 1h0l-2 2c1 1 1 1 0 1h-3-1l-1-1v-2c1 0 1-1 2-1z" class="V"></path><path d="M107 217h1v1 1c-1 0-2 0-2-1v-1h1z" class="Z"></path><path d="M108 215l2 1c1 0 1 1 1 1h0-3-1l1-2z" class="p"></path><path d="M108 217h3l-2 2c1 1 1 1 0 1h-3s1-1 2-1v-1-1z" class="I"></path><path d="M101 213h3v1c1 0 1 0 2 2-1 0-1 1-2 1s-2 0-3 1h-1 0-1 0c-2-1-4-1-5-2 0-2 0-1 2-2l1-1h1 2v1l1-1z" class="K"></path><path d="M104 214c1 0 1 0 2 2-1 0-1 1-2 1h0c-1 0-2 0-3-1 1-1 2-2 3-2z" class="M"></path><path d="M97 213h1c0 1 1 1 2 2h0c-1 1-1 2-1 3h0c-2-1-4-1-5-2 0-2 0-1 2-2l1-1z" class="f"></path><path d="M114 187h1c0 1 1 0 1 1-1 0-2 1-2 2v1c1 0 2 1 4 1h0c-2 1-2 3-2 5h-1c1 1 1 2 1 3v2h0c-1 1-1 1-1 2l-1 1v1h-1v2h-1c-1-1-1-2-2-2h-2v-1l-1 1-1-1 1-2h-1v-1c-1 0-1 0-2-1-1 0-1 0-2-1-1 1-2 1-3 1v-2c-1 0 0-1 0-2h1c-1-2 0-4 1-6 1-1 2 0 3 0l2-1-1-1h2 0c1 0 2 1 3 1 0-1 1-1 1-2h1l2-1z" class="g"></path><path d="M106 200v-2h1v1l-1 1z" class="F"></path><path d="M110 197h4v-1c1 0 1 1 1 2h-1l-2 1h-1-2c-1 0-1 0-1-1h1 1c1 1 2 1 3 0-1 0-2 0-2-1h-1z" class="Y"></path><path d="M113 195l1-1h2c0 1-1 1-2 2v1h-4c0-1-1-1-2-2h0 4 0 1z" class="i"></path><path d="M104 193l1-1 1 1c1 0 1 1 3 1h2l1 1h-4 0c0 1 0 1-1 2h-2c-1 0-1 1-1 1h-1 0l1-2h2v-1l-2-2z" class="Y"></path><path d="M114 187h1c0 1 1 0 1 1-1 0-2 1-2 2v1c1 0 2 1 4 1h0c-2 1-2 3-2 5h-1c1 1 1 2 1 3l-1-1v-1c0-1 0-2-1-2 1-1 2-1 2-2h-2l-1 1h-1 0l-1-1h-2c-2 0-2-1-3-1l-1-1-1 1v-2l2-1-1-1h2 0c1 0 2 1 3 1 0-1 1-1 1-2h1l2-1z" class="x"></path><path d="M107 189h0c1 0 2 1 3 1-1 1-2 2-3 2l-1 1-1-1-1 1v-2l2-1-1-1h2z" class="T"></path><path d="M106 190c0 1 1 2 1 2l-1 1-1-1-1 1v-2l2-1z" class="U"></path><path d="M114 187h1c0 1 1 0 1 1-1 0-2 1-2 2v1c1 0 2 1 4 1h0c-2 1-2 3-2 5h-1c1 1 1 2 1 3l-1-1v-1c0-1 0-2-1-2 1-1 2-1 2-2h-2l-1 1v-4c0-1 0-2 1-2v-1h-2l2-1z" class="v"></path><path d="M112 199l2-1h1v1l1 1v2h0c-1 1-1 1-1 2l-1 1v1h-1v2h-1c-1-1-1-2-2-2h-2v-1l-1 1-1-1 1-2h-1v-1h1v-1l-1-1 1-1v-1h1c0 1 0 1 1 1h2 1z" class="m"></path><path d="M107 198h1c0 1 0 1 1 1h2l-1 1h-2v1h0c0 1-1 1-1 1v-1l-1-1 1-1v-1z" class="B"></path><path d="M112 199l2-1h1v1c-1 1-1 2-1 3h-3c0-1 1-2 1-3h0z" class="E"></path><path d="M115 199l1 1v2h0c-1 1-1 1-1 2l-1 1v1h-1v2h-1c-1-1-1-2-2-2h-2v-1l-1 1-1-1 1-2h4c0 1 1 1 2 1 0-1 1-1 1-2s0-2 1-3z" class="B"></path><path d="M110 206v-1h3 1v1h-1v2h-1c-1-1-1-2-2-2z" class="i"></path><path d="M122 222h0l6 3 8 5c1 0 2 1 2 1 2 1 3 2 4 3 1 0 5 3 5 3l1 1h1 1l4 4h1c2 0 4 0 5-1 1 1 0 2 0 4h0v1l-1 2 1 1h0-1c0 1 0 1 1 2h0c-1 1-2 1-3 1-2 1-3 3-4 5h4c-1 1-2 2-3 4-1-1-2-1-3-1l-1 2-1-1-2 3-2 1-1 1h0l-7-11-1-1c-1-2-2-3-3-5-2-2-3-5-6-7h0 0v1c-1-1-1-1-2-1-1-1-2-1-3-2 1 0 2-1 3-1h2l-1-2h0l-1-1h-1l-2-3c-1-1-1-2-1-3l-2-3v-2-1c1 0 1 0 2-1h0l1-1z" class="q"></path><path d="M151 256v1h1l-1 1h-3-1v-1c1 0 2 0 4-1h0z" class="i"></path><path d="M149 238h1l4 4 1 1h-2c-1-1-2-1-3-2h1l-3-3h1z" class="AD"></path><path d="M153 257h4c-1 1-2 2-3 4-1-1-2-1-3-1l-1 2-1-1 1-2v-1h-2 3l1-1h1z" class="k"></path><path d="M125 239h2l10 16-1-1c-1-2-2-3-3-5-2-2-3-5-6-7h0 0v1c-1-1-1-1-2-1-1-1-2-1-3-2 1 0 2-1 3-1z" class="w"></path><path d="M160 241c1 1 0 2 0 4h0v1l-1 2 1 1h0-1c0 1 0 1 1 2h0c-1 1-2 1-3 1-2 1-3 3-4 5h-1-1v-1c2-3 4-8 5-13h-1l-1-1h1c2 0 4 0 5-1z" class="F"></path><path d="M160 245c-1 1-1 2-3 3v-2c1-1 2-1 3-1h0z" class="R"></path><path d="M159 248l1 1h0-1c0 1 0 1 1 2h0c-1 1-2 1-3 1 0-2 1-3 2-4z" class="D"></path><path d="M122 222h0l6 3 8 5c1 0 2 1 2 1 2 1 3 2 4 3 1 0 5 3 5 3l1 1 3 3h-1c-2-2-4-1-7-1-2 0-4 0-5 1h-1s-1 0-2 1c0 0 1 2 2 2v1l-3-3h0c-1 0-2 0-3-1h-1l-1-1c-1-2-2-3-3-3h0l-1-1h-1l-2-3c-1-1-1-2-1-3l-2-3v-2-1c1 0 1 0 2-1h0l1-1z" class="l"></path><path d="M143 238v-1c1 0 2 1 2 1s-1 0-2 1v-1z" class="F"></path><path d="M132 232h0c1-1 1-1 2-1 1 1 3 1 3 2h-3c0 1 0 1-1 2l-1-1v-2z" class="AA"></path><path d="M141 239c0-1-2-2-2-3 2 1 2 2 4 2v1c1-1 2-1 2-1 1 0 1 0 2-1l1 1 3 3h-1c-2-2-4-1-7-1-1 0-1 0-2-1z" class="u"></path><path d="M137 241v-1l-3-3h0v-1h1 2l1 1c0 1 1 1 2 2h1c1 1 1 1 2 1-2 0-4 0-5 1h-1z" class="W"></path><path d="M124 230c1 1 2 1 3 1 1 1 3 1 5 1v2l-1-1c1 2 2 4 3 5 0 1 0 2 1 3l-5-4-2-2-4-5z" class="U"></path><path d="M119 225c2 2 3 4 5 5l4 5 2 2-1 1 2 2 1 1h0-1-1l-1-1c-1-2-2-3-3-3h0l-1-1h-1l-2-3c-1-1-1-2-1-3l-2-3v-2z" class="W"></path><path d="M121 230c1 1 2 2 3 2 1 1 2 2 2 3v1l-1-1v1h-1l-2-3c-1-1-1-2-1-3z" class="S"></path><path d="M122 222h0l6 3 8 5-2 1c-1 0-1 0-2 1h0c-2 0-4 0-5-1-1 0-2 0-3-1-2-1-3-3-5-5v-1c1 0 1 0 2-1h0l1-1z"></path><path d="M145 549v-3h0c1 1 1 1 1 2 1 1 2 3 4 4h0 1v2h1c1-1 2 0 2 0h1c-1 0-1 0-1 1-1 1-1 2-1 2l-1-2h0c-1 1-1 1-1 2l-1 1v-3c-1 0-1 0-2 1v-2c-2 1-3 1-4 3-1 1-1 4-1 5v3h-1l1 1c1 2 3 3 5 4-1 2-4 5-5 6-1 0-1 0-2 1v1l1 1h0v1c-1 2-2 5-3 7 0 1-1 1-1 2h-1l-2-1v2l-2-1h0-2l-2 1-1-1 2-1h-1-3v-1h0c-1-9-10-12-16-16 0-1 2-1 2-2l1-1h1c1-1 3-4 4-6v-1c1-1 1-3 1-4l-1-2 8-3c0 1 0 1 1 2h1v2h0c1 0 1-1 2-2h2 1l1-1h1v1h1l-1-1c1 0 2-1 3-1s3-2 4-3l2-1v-1l1 2z" class="S"></path><path d="M144 548v-1l1 2v1s-1 1-2 1l-1-2 2-1z" class="K"></path><path d="M136 554c1 0 2-1 3-2l1 1-5 4c0-1-1-2-2-3l1-1h1v1h1z" class="e"></path><path d="M142 549l1 2-3 2-1-1c-1 1-2 2-3 2l-1-1c1 0 2-1 3-1s3-2 4-3z" class="r"></path><path d="M140 554c1-1 3-2 5-3 2 0 3 0 4 1v1c-3 0-4 0-7 2-1 0-1 0-2-1z" class="P"></path><path d="M133 554c1 1 2 2 2 3 0 0-1 1-2 1h0-1c-2 0-2-1-4-2 1 0 1-1 2-2h2 1z" class="y"></path><path d="M128 556c1 0 1-1 2-2h2l-1 2v1h2v1h-1c-2 0-2-1-4-2z" class="N"></path><path d="M132 564c1 0 1 1 2 1l-7 3c-1 1-2 1-3 2 0 1 0 1-1 1h0c1-3 4-5 6-7h2 1z" class="p"></path><path d="M140 561v1l1-1v-2l1-1c1 1 0 4 0 5v1h-1v-1c-2 1-4 2-5 3-3 1-5 3-8 4-1 1-1 1-2 1h-1c3-3 7-4 10-6h-1l6-4z" class="d"></path><path d="M142 555c3-2 4-2 7-2 0 1-1 1-1 1-2 1-3 1-4 3-1 1-1 4-1 5v3h-1v-1-1c0-1 1-4 0-5l-1 1v2l-1 1v-1l-1-1h0-1c1 0 1-1 2-1v-1c-1 0-1-1-2-2 0 0 2-1 2-2 1 1 1 1 2 1z" class="i"></path><path d="M140 554c1 1 1 1 2 1-1 1-2 2-2 3-1 0-1-1-2-2 0 0 2-1 2-2z" class="Q"></path><path d="M138 556c1 1 1 2 2 2v1c-1 0-1 1-2 1h1 0l1 1-6 4h0c-1 0-1-1-2-1l-1-1h-1l8-7z" class="N"></path><path d="M142 564v1l1 1c1 2 3 3 5 4-1 2-4 5-5 6-1 0-1 0-2 1v1c-1 0-1 1-3 1 0 0 0-1-1-1-3-2-6-4-9-7 2-1 4-1 6-2 2-2 5-3 7-5h1z" class="n"></path><path d="M118 555l8-3c0 1 0 1 1 2h1v2h0c2 1 2 2 4 2h1 0c-3 3-5 6-8 9-2 2-4 4-7 5v1l4-1c5 0 7 1 10 3l6 6c1-1 3-2 4-2v1c-1 2-2 5-3 7 0 1-1 1-1 2h-1l-2-1v2l-2-1h0-2l-2 1-1-1 2-1h-1-3v-1h0c-1-9-10-12-16-16 0-1 2-1 2-2l1-1h1c1-1 3-4 4-6v-1c1-1 1-3 1-4l-1-2z" class="q"></path><path d="M126 587h4s0 1-1 1h-3v-1z" class="H"></path><path d="M133 587l2 1h0v2l-2-1h0v-2z" class="l"></path><path d="M130 587h3v2h-2l-2 1-1-1 2-1h-1c1 0 1-1 1-1z" class="D"></path><path d="M319 609l12 1h0c4 3 9 4 11 9l1 1h-2s-1 1-2 1h2v1h0c-8 1-17 1-23 5v1h1l2 1c-1 0-3 0-4-1-1 0-2 1-3 1v1l3 1v1h-7-1c-1 0-1 0-2 1v-1h-4-2l-3-1-4-1c-1 0-2-1-3-1h0l-3-1c-1 1-1 1-2 1h-3l-7-3-2-1c0 1-1 1-2 1l-4-2 1-2-3-2-2-1v-1h0c-1-1-2-1-3-2h-1l1-1h3 3 24c2 0 5 0 7-1h4v1h1l3-1-1-1 5-1 2-1c1 0 2-1 3-1l1-1h3z" class="n"></path><path d="M301 629h1l1 1v1 1h-2c-1-1 0-1-1-1v-1-1h1z" class="M"></path><path d="M312 614l2 1h-3s0 1 1 1h-8l-2-1h1l3-1h6z" class="H"></path><path d="M298 614h4v1h1-1l2 1-13-1c2 0 5 0 7-1z" class="d"></path><path d="M296 619c1 0 3-1 4-1l2 1c-1 0-1 1-2 1-1 1-1 1-1 2l-2 1-1-1c1-1 1-2 1-2l-1-1z" class="e"></path><path d="M302 629h0c6-1 10-6 16-7h0c-1 1-3 2-5 2-3 2-6 5-10 7v-1l-1-1zm0-10h2c1 1 1 1 1 2-1 2-2 4-4 5 0 0 0-1-1-1v-1h0-1v-2c0-1 0-1 1-2 1 0 1-1 2-1z" class="Q"></path><path d="M297 623l2-1v2h1 0v1c1 0 1 1 1 1l1 1 7-4v1c-2 1-5 3-7 4h-3-2c-1 0-2 0-3-1h1l-1-2h1c1 0 1 0 1-1 1 0 1 0 1-1z" class="I"></path><path d="M287 618h1 1s1 0 2 1h5l1 1s0 1-1 2l1 1c0 1 0 1-1 1 0 1 0 1-1 1v-1h0l-1-1h0c-2-2-5 0-7-2 1-1 1-1 1-2-1 0-1 0-1-1z" class="f"></path><path d="M289 618s1 0 2 1h5l1 1s0 1-1 2c0-1-1-2-1-2h-2-1-1 0-2c-1 0-1 0-1-1l1-1z" class="I"></path><path d="M314 615c6 0 13 2 19 3 3 1 6 1 8 2 0 0-1 1-2 1-9-3-18-4-27-5-1 0-1-1-1-1h3z" class="c"></path><path d="M307 632h0c1 0 3-1 5-2 1-1 3-2 4-3 8-3 16-6 25-5h0c-8 1-17 1-23 5v1h1l2 1c-1 0-3 0-4-1-1 0-2 1-3 1v1l3 1v1h-7-1c-1 0-1 0-2 1v-1z" class="AC"></path><defs><linearGradient id="F" x1="289.282" y1="615.898" x2="275.218" y2="626.123" xlink:href="#B"><stop offset="0" stop-color="#c5c8c5"></stop><stop offset="1" stop-color="#f8f3f6"></stop></linearGradient></defs><path fill="url(#F)" d="M270 619h1-1 0l2-1v-1c4 0 9 2 13 1h2c0 1 0 1 1 1 0 1 0 1-1 2 2 2 5 0 7 2h0l1 1h0v1h-1l-1 1h-1 0l-1-1h-2c-2-1-3-1-5-1s-4-1-6-1c-2-1-4-1-5-3h0l-3-1z"></path><path d="M273 620c2 1 4 1 6 2 2 0 5 0 8 1 0 1 2 0 2 0 2 0 2 1 4 1h0l-1 2h0l-1-1h-2c-2-1-3-1-5-1s-4-1-6-1c-2-1-4-1-5-3z" class="I"></path><defs><linearGradient id="G" x1="282.736" y1="620.167" x2="278.586" y2="627.169" xlink:href="#B"><stop offset="0" stop-color="#d7dfdf"></stop><stop offset="1" stop-color="#ede5e5"></stop></linearGradient></defs><path fill="url(#G)" d="M264 619v-1c2 1 4 1 6 1l3 1h0c1 2 3 2 5 3 2 0 4 1 6 1s3 0 5 1h2l1 1h0 1l1-1 1 2h-1c1 1 2 1 3 1h2 3c-1 0-1 1-1 1h-1v1 1c1 0 0 0 1 1l-3-1-4-1c-1 0-2-1-3-1h0l-3-1c-1 1-1 1-2 1h-3l-7-3-2-1c0 1-1 1-2 1l-4-2 1-2-3-2-2-1z"></path><path d="M284 624c2 0 3 0 5 1h2l1 1h0 1l1-1 1 2h-1l-10-3z" class="L"></path><path d="M299 628h3c-1 0-1 1-1 1h-1v1 1c1 0 0 0 1 1l-3-1-4-1c-1 0-2-1-3-1h6 0c1 0 2-1 2-1z" class="a"></path><path d="M300 629v1 1c1 0 0 0 1 1l-3-1-4-1c2 0 4 0 6-1z" class="I"></path><path d="M269 622l19 6c-1 1-1 1-2 1h-3l-7-3-2-1c0 1-1 1-2 1l-4-2 1-2z" class="E"></path><path d="M319 609l12 1h0c4 3 9 4 11 9l1 1h-2c-2-1-5-1-8-2-6-1-13-3-19-3l-2-1h-6l-1-1 5-1 2-1c1 0 2-1 3-1l1-1h3z" class="n"></path><path d="M312 611c1 0 2-1 3-1v1c2 0 4 0 6 1h4l2 1h1 1 1l2 1h1l1 1h2c0 1 1 1 1 1 0 1 0 1-1 1 0-1-1-1-1-1l-2-1h0c-1 0-2-1-4-1h-1l-5-1c-1 0-2 0-3-1-2 0-8 0-10 1h-1-1l4 1h-6l-1-1 5-1 2-1z" class="a"></path><path d="M173 342l1 1h0 1 0c2 0 1 1 2 2l2-2 1-1v3c-2 1-3 2-3 4 0 0-1 1 0 2h0 1l2-3h1v1 1l1 1h0 1l-2 5c-2 2-3 5-3 7v4 1l1 1-1 1h1v2c-1 1-2 5-2 7h0c1 0 1 0 2 1-1 1-1 3-2 4v1 1h-1v-1h-1 0-1v-1l-1-1c1 1 0 1 0 2v1l-1-1c-1 1-2 3-3 4h-1-2c1-2 3-5 3-7h-1-1l-1 1-1-1-2 1-1-1c0 1-1 1-1 2l-1-1v-1-1-2l-2-3h0 0-1c0-1-1-1-1-2h-1c1-1 1-2 1-3h0c-1 2-2 3-2 5 0 1-1 2-3 3 0-1 0-1-1-1-1 1-2 1-3 2v-1h1c3-3 4-6 6-10-2 2-3 4-5 6 1-4 4-7 6-11h-1c-1 1-1 2-1 3-2 2-4 5-6 7v-1l4-6v-1c1 0 1-1 1-1l-1-1c-1 3-2 5-4 6v1c0 1-2 2-3 2h0c1-1 2-2 3-4h-1v1l-1-1h1l-1-1c0 1 0 1-1 1 0 1-1 1-2 1h0l1-1v-1c-1-1-1 0-2-1 0 0 1-1 2-1 1-1 2-2 2-3l1-2h0l3-6 2-4c2-2 4-4 5-6l2 1 2-2h1l-1 2v4h1c0-1 0-1 1-1l3-6h1l2-1 1 1h0 2l2-1z" class="Z"></path><path d="M156 374h1c1-1 2-3 2-4 2-2 3-4 5-7l1 1-2 2-3 4c0 2-2 4-2 6h-1c0-1-1-1-1-2h0z" class="K"></path><path d="M162 354c0-1 3-3 4-4 1 0 1 0 2-1h0c1-2 2-2 4-3l-4 6c-3 0-5 5-8 7v-1c1-1 2-2 2-4z" class="i"></path><path d="M150 378c3-3 5-8 7-12 1-1 2-3 3-3-1 3-3 6-4 8s-2 3-2 5c0 1-1 2-3 3 0-1 0-1-1-1z" class="Q"></path><path d="M156 371c2-2 5-6 5-9 1-1 2-2 2-3 2-2 3-4 5-6 0 0-1 2-1 3-1 2-2 3-2 4-3 5-6 9-9 14h0-1c1-1 1-2 1-3z" class="I"></path><path d="M156 351c1 1 1 1 1 2l1 1v1l2-1v1 1l-1 1c-2 2-2 4-4 5 0-1 1-2 1-2 1-1 1-2 1-2 1-1 2-1 2-2l-1-1c-2 1-3 4-4 5s-2 2-2 3l-1 1c-1 3-2 5-4 6h0c2-3 3-5 4-8 1-1 3-2 3-3 1-1 1-1 1-2 1-1 1-1 1-2-2 1-3 4-5 6h0l5-10z" class="S"></path><path d="M168 357h0c0-1 0-2 1-3h0l3-3c1-2 1-3 2-4h1c0 1 1 1 1 3-1 0-1 1-1 1v1c0 1 0 1-1 1h0l-1 2c0 1-1 2-2 3v-1c-1 1-2 1-3 1h0v-1z" class="O"></path><path d="M170 356c1-2 2-4 3-5 0 0 0 1 1 1h1c0 1 0 1-1 1h0l-1 2c0 1-1 2-2 3v-1c-1 1-2 1-3 1h0v-1l2-1z" class="D"></path><path d="M170 356v-1l1 1 3-3-1 2c0 1-1 2-2 3v-1c-1 1-2 1-3 1h0v-1l2-1z" class="j"></path><path d="M151 361h0c2-2 3-5 5-6 0 1 0 1-1 2 0 1 0 1-1 2 0 1-2 2-3 3-1 3-2 5-4 8h0v1c0 1-2 2-3 2h0c1-1 2-2 3-4h-1v1l-1-1h1l-1-1c0 1 0 1-1 1 0 1-1 1-2 1h0l1-1v-1c-1-1-1 0-2-1 0 0 1-1 2-1 3-1 4-2 5-4l1 1c0-1 1-2 2-2z" class="K"></path><path d="M143 366c3-1 4-2 5-4l1 1c-2 2-4 5-6 6v-1c-1-1-1 0-2-1 0 0 1-1 2-1z" class="o"></path><path d="M158 346l2-2h1l-1 2v4c-1 0-2 0-3 1h-1l-5 10c-1 0-2 1-2 2l-1-1c-1 2-2 3-5 4 1-1 2-2 2-3l1-2h0l3-6 2-4c2-2 4-4 5-6l2 1z" class="V"></path><path d="M154 349c1 0 2-1 3-1-1 1-2 3-3 4v-3z" class="L"></path><path d="M160 346v4c-1 0-2 0-3 1 0-2 2-4 3-5z" class="E"></path><path d="M150 355c1-2 3-4 4-6v3c-1 1-2 5-3 5v-2h-1z" class="G"></path><path d="M156 345l2 1-1 2c-1 0-2 1-3 1-1 2-3 4-4 6l-4 6h0l3-6 2-4c2-2 4-4 5-6z" class="T"></path><path d="M173 342l1 1h0 1l-3 3c-2 1-3 1-4 3h0c-1 1-1 1-2 1-1 1-4 3-4 4l-2 2v-1-1l-2 1v-1l-1-1c0-1 0-1-1-2h1c1-1 2-1 3-1h1c0-1 0-1 1-1l3-6h1l2-1 1 1h0 2l2-1z" class="B"></path><path d="M169 343h2c0 1-2 3-2 3-1 0-2 0-3 1l3-4z" class="R"></path><path d="M157 351c1-1 2-1 3-1h1l-1 1-1 1-2 1c0-1 0-1-1-2h1z" class="U"></path><path d="M165 343h1l2-1 1 1h0l-3 4c-1 1-2 2-4 2l3-6z" class="r"></path><path d="M168 358h0c1 0 2 0 3-1v1 1c0 1-1 1-1 2v1h1v1h1 0c-2 3-4 5-5 8l-1 2v1l-1 3c-1 2-2 4-3 5 0 1-1 1-1 2l-1-1v-1-1-2l-2-3h0 0c0-2 2-4 2-6l3-4 2-2c0-1 2-5 3-6z" class="AA"></path><path d="M170 362h1v1h1 0c-2 3-4 5-5 8h-3l6-9z" class="E"></path><path d="M160 370l3-4s1 1 1 2-2 3-2 5h1l1-2h3l-1 2v1l-1 3c-1 2-2 4-3 5 0 1-1 1-1 2l-1-1v-1-1-2l-2-3h0 0c0-2 2-4 2-6z" class="S"></path><path d="M158 376c0-2 2-4 2-6l2 2-2 3v1l2-1s0 1 1 1l-1 2-1-1h0l-1 2-2-3h0 0z" class="H"></path><path d="M158 376h2 1v1l-1 2-2-3h0z" class="O"></path><path d="M163 376c0-1 1-1 1-2h1l1-1v1l-1 3c-1 2-2 4-3 5 0 1-1 1-1 2l-1-1v-1-1-2l1-2h0l1 1 1-2z" class="C"></path><path d="M163 376v1c0 1-1 4-3 5v-1-2l1-2h0l1 1 1-2zm18-28v1 1l1 1h0 1l-2 5c-2 2-3 5-3 7l-2 4v-1c-2 2-1 3-3 4h0 0l-1 2-1-1v1l-1-1-2 2v-2 1 1c-1 0-1 0-2 1h0v-1l1-2c1-3 3-5 5-8h0-1v-1h-1v-1c0-1 1-1 1-2v-1c1-1 2-2 2-3l1 1 3-5h1l2-3h1z" class="J"></path><path d="M179 353h2l-3 6-1-1c1-1 1-2 1-3s1-2 1-2z" class="E"></path><path d="M181 348v1 1l1 1h0l-1 2h-2c1-1 1-1 1-2h-2l2-3h1z" class="m"></path><path d="M178 351h2c0 1 0 1-1 2 0 0-1 1-1 2l-2 2-1 4h0-2l-1 2h-1v-1h-1v-1c0-1 1-1 1-2v-1c1-1 2-2 2-3l1 1 3-5h1z" class="B"></path><path d="M170 361h1 2 0l-1 2h-1v-1h-1v-1z" class="i"></path><path d="M171 358c1-1 2-2 2-3l1 1c-1 1-1 2-3 3v-1z" class="R"></path><path d="M173 361l3-4-1 4h0-2 0z" class="D"></path><path d="M175 361l1 1-3 8h0l-1 2-1-1v1l-1-1-2 2v-2 1 1c-1 0-1 0-2 1h0v-1l1-2c1-3 3-5 5-8h0l1-2h2 0z" class="P"></path><path d="M171 371c1-1 1-1 1-2l1 1-1 2-1-1z" class="Q"></path><path d="M173 361h2l-1 2c-1 3-4 6-6 8v1 1c-1 0-1 0-2 1h0v-1l1-2c1-3 3-5 5-8h0l1-2z" class="r"></path><path d="M173 361h2l-1 2h-2 0l1-2z" class="J"></path><path d="M176 367l2-4v4 1l1 1-1 1h1v2c-1 1-2 5-2 7h0c1 0 1 0 2 1-1 1-1 3-2 4v1 1h-1v-1h-1 0-1v-1l-1-1c1 1 0 1 0 2v1l-1-1c-1 1-2 3-3 4h-1-2c1-2 3-5 3-7h-1-1l-1 1-1-1-2 1-1-1c1-1 2-3 3-5l1-3h0c1-1 1-1 2-1v-1-1 2l2-2 1 1v-1l1 1 1-2h0 0c2-1 1-2 3-4v1z" class="t"></path><path d="M171 371l1 1c0 1-1 6-2 6h-1v-2l2-4v-1z" class="p"></path><path d="M168 389c1-2 2-4 3-5v-4l1 1 1 1-1 2h1v-1c1 1 0 1 0 2v1l-1-1c-1 1-2 3-3 4h-1z" class="D"></path><path d="M167 377l2-1v2h1l-1 4h-1-1l-1 1-1-1c0-2 2-3 2-5z" class="I"></path><path d="M169 378h1l-1 4h-1-1c0-1 0-1 1-2 0 0 0-1 1-1v-1z" class="e"></path><path d="M166 374h0c1-1 1-1 2-1v-1-1 2l2-2 1 1-2 4-2 1c0 2-2 3-2 5l-2 1-1-1c1-1 2-3 3-5l1-3z" class="G"></path><path d="M166 374h0c1-1 1-1 2-1v-1-1 2l2-2 1 1-2 4-2 1v-2h-1c0 1 0 2-1 3v-1l1-3z" class="V"></path><path d="M176 367l2-4v4 1l1 1-1 1h1v2c-1 1-2 5-2 7h0c1 0 1 0 2 1-1 1-1 3-2 4v1 1h-1v-1h-1 0-1v-1l-1-1v1h-1l1-2-1-1-1-1 1-1c1-2 1-5 3-8l1-4z" class="R"></path><path d="M176 382c1 1 1 1 1 2v1 1h-1v-1h-1 0c0-1 1-2 1-3z" class="J"></path><path d="M177 379c1 0 1 0 2 1-1 1-1 3-2 4 0-1 0-1-1-2 0-1 1-2 1-3z" class="L"></path><path d="M176 373v-1c0-1 1-2 2-4l1 1-1 1c-1 2-1 4-2 7 0 0 0 1-1 1v-1c1-1 1-3 1-4z" class="Y"></path><path d="M176 367l2-4v4 1c-1 2-2 3-2 4v1l-1-2 1-4z" class="E"></path><path d="M172 379h1 0l1-2h1c0 1-1 2-1 3h1s1-1 1-2v2c0 1-1 3-2 4l-1-1v1h-1l1-2-1-1-1-1 1-1z" class="T"></path><path d="M89 555c2 4 3 6 7 8l2 1c-2-3-4-5-3-9 0-1 1-1 1-2 2 0 3-1 4 0 1 0 1 1 1 1 0 1 0 1-1 2h-3l-1 1c0 2 1 5 2 7 1 1 3 2 4 2 0 1 2 2 3 2 2 1 4 2 5 3 6 4 15 7 16 16h0v1c-2 0-3 0-5 2-3 3-4 6-6 10l-1-1v-1l-21 3c0-1 0-1-1-1v-1h2v-1l-2-1-5-1c-1-1-3-1-4-3 0 0-1-1-1-2v-1h-2c1 1 2 2 2 4v1s-1 0-1-1h-2v-1c-1-1-3-3-3-5h-1c0-1 0-2 1-3v-1c0-1-3-3-3-4v-1c1-1 2-1 3-1v-1l-3-3c-1-1-2-1-3-2h0l-1-1c-2 0-4 0-5 1 1-2 2-4 4-5v-1h4l9 3c2 0 3 1 4 1 2 1 4 2 5 3 5 3 10 7 16 9 2 1 4 2 6 2l8 4h2v-1h2c-2-8-13-10-18-14-2-1-3-2-4-3h-1c-4-4-8-7-12-11h1l-1-4h0z" class="B"></path><path d="M96 557v-2c0-1 1-1 1-1h3v1c-1 1-1 0-2 1h-1l-1 1z" class="n"></path><path d="M98 588c0-1 0-1 1-1l-1 1 2 1h1l1 1-2 1-4-2 2-1h0z" class="AD"></path><path d="M107 591h2 0 2v3l-7-2h1l-1-1h1 0 2z" class="o"></path><path d="M101 589c0-1 0-2 1-2h2l-1 2 1 1 1 1h0-1l1 1h-1l-4-1 2-1-1-1z" class="N"></path><path d="M77 579c1 0 1 0 2 1v3h0c0 1 0 2 1 2h-2 0c-1-1-1-2-1-3-1 0-2-1-2-1v-1h1l1 1v-2z" class="e"></path><path d="M79 593l1-1-3-3c1-1 1-2 2-2h0l1 3c1 1 2 2 2 4v1s-1 0-1-1h-2v-1z" class="N"></path><path d="M109 591c1-1 1-1 3-1h0c0 1 1 1 1 2h1v-1h2l2 1c-2 1-2 2-4 2h-1-2v-3h-2 0z" class="Q"></path><path d="M109 591c1-1 1-1 3-1 0 1-1 1-1 2 1 0 2 0 2 1 1 0 1 0 1 1h-1-2v-3h-2 0zm-19-32c3 4 9 7 13 10v1h-1-1c-4-4-8-7-12-11h1z" class="AD"></path><path d="M103 569c3 1 5 1 7 3l7 4c3 2 7 6 8 9v1l-1 1h0c-2-8-13-10-18-14-2-1-3-2-4-3h1v-1z" class="M"></path><path d="M82 590s-1-1 0-2h1 0c-2-2-2-5-3-7 5 4 10 6 16 9 4 2 8 3 13 5 2 0 4 0 5 1h1v2h-1l-21 3c0-1 0-1-1-1v-1h2v-1l-2-1-5-1c-1-1-3-1-4-3 0 0-1-1-1-2v-1z" class="q"></path><path d="M96 590c4 2 8 3 13 5h-3 0c-1-1-2-1-3-1-1-1-2-1-3-1h-1 0-2c-1-1-1-1-1-3z" class="y"></path><path d="M115 596v2h-1l-21 3c0-1 0-1-1-1v-1h2v-1c5 1 9 1 14 1 2-1 5-1 7-3z" class="H"></path><path d="M68 566h4l9 3c2 0 3 1 4 1 2 1 4 2 5 3 5 3 10 7 16 9 2 1 4 2 6 2l8 4v1h0l-2 3-2-1h-2v1h-1c0-1-1-1-1-2h0c-2 0-2 0-3 1h-2-2l-1-1-1-1 1-2h-2c-1 0-1 1-1 2h-1l-2-1 1-1c-1 0-1 0-1 1h0l-2 1c-3-1-7-3-10-5-2-1-3-3-5-5-2-1-3-2-4-3-2-1-4-4-7-4l-1-1c-2 0-4 0-5 1 1-2 2-4 4-5v-1z" class="n"></path><path d="M93 586v-1l-4-2h-1l-3-3c0-1 0-1 1-1h0v-1h1l3 2c1 1 2 1 2 2 1 0 1 1 2 1l2 1v1h-1s-1 0-2 1z" class="M"></path><path d="M92 582c1 0 2 0 2 1h1l3 1h0c5 1 9 4 13 5 1 0 2 1 3 2v1h-1c0-1-1-1-1-2h0c-2 0-2 0-3 1h-2-2l-1-1-1-1 1-2h-2c-1 0-1 1-1 2h-1l-2-1 1-1c-1 0-1 0-1 1l-1-1-4-1c1-1 2-1 2-1h1v-1l-2-1c-1 0-1-1-2-1z" class="f"></path><path d="M96 584c1 0 2 1 2 1 1 0 2 1 2 1h0c0 1-1 1-1 1-1 0-1 0-1 1l-1-1-4-1c1-1 2-1 2-1h1v-1z" class="N"></path><path d="M104 587h1c0 1 0 1 1 1l3 1v1h0c-1 0-2 0-2 1h-2l-1-1-1-1 1-2z" class="AD"></path><path d="M68 566h4l9 3c2 0 3 1 4 1 2 1 4 2 5 3 5 3 10 7 16 9 2 1 4 2 6 2l8 4v1h0l-2 3-2-1v-4l-28-13c-3-1-5-3-8-5-4-1-8-2-12-2v-1z" class="i"></path><path d="M116 587l4 2h0l-2 3-2-1v-4z" class="e"></path><path d="M155 547c1 1 1 1 2 1 5 0 14 0 18 4 1 1 1 3 2 4v2c-1 1-1 1-3 1 0 1 0 1 1 1v2h0l-1 4v3h0v2h0v1 1l-2-1h0c0 1 1 2 2 2 0 1-1 1-1 2l-1 1h1v3l-2 1c1 1 2 1 3 0l-1 1v1c-1 1-1 1-1 2v2h-6c-1 1-2 0-4 0-2 1-4 0-5 1l-1-1h-3 0c-4 1-7 1-11 2h-5 1c0-1 1-1 1-2 1-2 2-5 3-7v-1h0l-1-1v-1c1-1 1-1 2-1 1-1 4-4 5-6-2-1-4-2-5-4l-1-1h1v-3c0-1 0-4 1-5 1-2 2-2 4-3v2c1-1 1-1 2-1v3l1-1c0-1 0-1 1-2h0l1 2s0-1 1-2c0-1 0-1 1-1h-1s-1-1-2 0h-1v-2c1 0 2-1 2-2 0 0-1-1-2-1l-1 1v-1-1c1 0 3 1 3 1l1 1v-1c1-1 1-1 1-2z" class="R"></path><path d="M155 547c1 1 1 1 2 1l-1 1c-1 0-1 2-2 2v-1-1c1-1 1-1 1-2z" class="E"></path><path d="M166 560l2 2v2c-1 0-2-1-3-1 0 0 0-1-1-1v-1l2-1z" class="M"></path><path d="M163 571v-2h-1v-1h3 1v4l-1 2v-1l-2-2z" class="L"></path><path d="M166 568h2l1 1v1c1-1 1-1 2-1l-1 1 1 1c-1 0-1 0-2-1v1c-1 0-2 0-3-1v2h0v-4z" class="O"></path><path d="M169 569l-1 1-1-1v-1h1l1 1z" class="V"></path><path d="M160 570h2 0c1 0 1 0 1 1l2 2v1l-1 1c-1 0-1-1-2-2v1l-1-1-2 2c-1 0-1-1-3-1l2-1v1c2-1 2-3 2-4z" class="H"></path><path d="M174 569l-10-4-1-1 1-1 3 2 1-1h3 1l1 2h1v3h0z" class="e"></path><path d="M168 564h3 1l1 2v1c-2-1-4-1-6-2l1-1z" class="V"></path><path d="M171 569c1 1 2 1 3 2v1 1l-2-1h0c0 1 1 2 2 2 0 1-1 1-1 2l-1 1v-1c0-1-1-1-1-2l-1 1c-1-1-2 0-4-1h0l1-2h-1v-2c1 1 2 1 3 1v-1c1 1 1 1 2 1l-1-1 1-1z" class="I"></path><path d="M167 572l2 1v-1c2 1 3 2 4 4l-1 1v-1c0-1-1-1-1-2l-1 1c-1-1-2 0-4-1h0l1-2z" class="O"></path><path d="M166 572h0 1l-1 2h0c2 1 3 0 4 1l1-1c0 1 1 1 1 2v1h1v3l-2 1c1 1 2 1 3 0l-1 1v1c-1 1-1 1-1 2-1 0-4-2-5-3l-2-4s0-1-1-2v-1l1-1 1-2z" class="G"></path><path d="M164 576c1 0 1 0 2-1 0 2 1 3 2 4v1c-1 0-1-1-2-2h-1s0-1-1-2z" class="J"></path><path d="M170 575l1-1c0 1 1 1 1 2v1h1c-1 1-2 2-3 2h-1c0-2-1-2-2-3v-1h3 0z" class="Q"></path><path d="M169 579h1c1 0 2-1 3-2v3l-2 1c1 1 2 1 3 0l-1 1v1c-1 1-1 1-1 2-1 0-4-2-5-3l-2-4h1c1 1 1 2 2 2s1 0 1-1z" class="f"></path><path d="M162 574v-1c1 1 1 2 2 2v1c1 1 1 2 1 2l2 4c1 1 4 3 5 3v2h-6c-1 1-2 0-4 0-2 1-4 0-5 1l-1-1h-3c1 0 1 0 1-1 1-1 1-1 3-1l2-2h1c-1-1-1-1-2-1v-1c1 0 2 1 3 1s1 1 2 1l2-2c-1-2-2-5-3-7z" class="S"></path><path d="M162 587l4-4h1c0 2-1 2-1 4h0c-1 1-2 0-4 0z" class="M"></path><path d="M157 585h3 0c-1 2-3 2-4 2h-3c1 0 1 0 1-1 1-1 1-1 3-1z" class="a"></path><path d="M157 548c5 0 14 0 18 4 1 1 1 3 2 4v2c-1 1-1 1-3 1 0 1 0 1 1 1v2h0l-1 4h-1l-1-2h-1-3 0v-2l-2-2h-2v-2c-1-3-3-5-5-7l-3-2 1-1z" class="q"></path><path d="M168 562c1 1 2 1 3 2h-3 0v-2z" class="a"></path><path d="M172 564h1v-1l2-1h0l-1 4h-1l-1-2z" class="M"></path><path d="M159 551c1 0 3 0 3 1 1 0 2 2 3 3s3 2 4 3h-1c-1 0-1 0-2-1-1 0-1 0-2 1-1-3-3-5-5-7z" class="X"></path><path d="M155 554v1 2l1-1v-1c0-1-1-1-1-2 1 0 2 1 2 2v1h1c0-1-1-4-1-5 2 0 3 1 4 3l1 1c1 1 1 3 1 4h0v4l-3 7c0 1 0 3-2 4v-1c-2-1-4-3-6-4s-3-1-5-1h-1c-1-1-1-2-2-3h-1v-3c0-1 0-4 1-5 1-2 2-2 4-3v2c1-1 1-1 2-1v3l1-1c0-1 0-1 1-2h0l1 2s0-1 1-2c0-1 0-1 1-1z" class="q"></path><path d="M146 564v4c-1-1-1-2-2-3l1-1 1 1v-1z" class="X"></path><path d="M163 563l-1-1c-1-1-1-1-1-2l2-1v4z" class="y"></path><path d="M151 557v3 1c0 1-1 2-2 2s0 0-1-1c1-1 1-3 1-4h1v2l1-1-1-1 1-1z" class="o"></path><path d="M148 556c1-1 1-1 2-1v3l1 1-1 1v-2h-1c0 1 0 3-1 4v-2l1-1v-1c-1-1-1-2-1-2z" class="X"></path><path d="M148 554v2s0 1 1 2v1l-1 1-1-3h-1v4l-1-1v-3h-1c1-2 2-2 4-3z" class="M"></path><path d="M144 557h1v3l1 1v3 1l-1-1-1 1h-1v-3c0-1 0-4 1-5z" class="a"></path><path d="M142 565h1 1c1 1 1 2 2 3h1c2 0 3 0 5 1s4 3 6 4l-2 1c2 0 2 1 3 1l2-2 1 1c1 2 2 5 3 7l-2 2c-1 0-1-1-2-1s-2-1-3-1v1c1 0 1 0 2 1h-1l-2 2c-2 0-2 0-3 1 0 1 0 1-1 1h0c-4 1-7 1-11 2h-5 1c0-1 1-1 1-2 1-2 2-5 3-7v-1h0l-1-1v-1c1-1 1-1 2-1 1-1 4-4 5-6-2-1-4-2-5-4l-1-1z" class="F"></path><path d="M141 578v-1c1-1 1-1 2-1h1l1 1c-1 1-1 2-3 3v-1h0l-1-1z" class="M"></path><path d="M146 584h1c1 0 2-1 3-1v-2h1v1c-1 1-2 3-4 4v1c0-1-1-1-2-1v1l-1-1v-1c1-1 2-1 2-1z" class="X"></path><path d="M144 576l4-4h2 0l-1 2h0l-1-1 1 1h0c0 1-1 2-2 2l-2 1-1-1z" class="o"></path><path d="M141 587h0v-2c1-2 3-5 4-6s1 0 2 0v1 1h0c0 1 0 1-1 1s-2 3-4 3c0 1 0 0-1 1v1h0z" class="V"></path><path d="M151 584c0-1 1-2 2-3s4 1 5 1 1 0 2 1h-1l-2 2c-2 0-2 0-3 1 0 1 0 1-1 1h0c0-1 0-1 1-2v-1c-1 0-1 1-3 2v-2z" class="K"></path><path d="M159 583h-1 0 2-1z" class="V"></path><path d="M147 581h2v1c-1 0-2 1-3 2 0 0-1 0-2 1v1l1 1v-1c1 0 2 0 2 1l2-1 2-2v2c2-1 2-2 3-2v1c-1 1-1 1-1 2-4 1-7 1-11 2h-5 1c0-1 1-1 1-2h1 1 0v-1c1-1 1 0 1-1 2 0 3-3 4-3s1 0 1-1z" class="N"></path><path d="M158 581c-2-2-8-2-10-2h0c1-1 2-3 2-4 1-2 1-3 2-4h2l2 3c2 0 2 1 3 1l2-2 1 1c1 2 2 5 3 7l-2 2c-1 0-1-1-2-1s-2-1-3-1z" class="q"></path><path d="M160 249l1 1h1l1-1 1 1c-1 1-1 1-1 2 1 1 1 1 2 1l1 1v1h0l1 1 1 1h0l2 2v-1c2-1 3-1 4-3h1l1 1h1l2 1c0 1-1 2-1 2h2v1h0c1-1 2-1 3-2h2l1 1h0c-2 1-4 3-6 4h2l-2 1 1 1h2c1 0 1 0 1 1l3 1h0 1c-1 1-1 1-2 1l1 1h1c0 1-4 4-5 5 2-1 3-1 4-2 0 1-1 1-2 2s0 2 0 4h0-1l1 2c-1 0-1 0-2-1h0c0 1 0 2 1 2-1 2-3 3-4 4l-3 4c-2 1-3 2-5 3l1 1-4 4c0 1-1 1-1 2h0l-1 1c0 1-1 2-1 3l-1 1v1c-1 1-1 2-1 3v2 2 5 1l-3 1-1 2v-1l1-2c-1-1-1-2-1-3h-1v1c-1 0-1 0-1-1l-1 1c0-1 0-1-1-1l-3 6-1 3c-1 0-1 0-2-1v1h-1v-1-1c0-1 0-3-1-4-1 1-1 1-1 2h-1v-1s0-1 1-1v-2h-2c-1 0-2 2-3 2v-1c-1 1-2 2-4 3 1-2 3-4 4-6v-2h-3l-1 1-1-1c1 0 1-1 1-1h1l-1-1c1-1 2-2 2-3 2-2 3-4 4-6 1 0 2-2 3-3 0-1 1-1 1-2h0c1-1 1-1 2-1v1h0c1 1 1 1 2 1 1-2 3-4 5-6l1-2c1 0 2-1 3-2 0-1 0-1 1-2 3-1 5-4 8-7h-1-1l1-2c1 0 1 0 2-1h-1c0-1-1-1-1-1l-1-1h-1-1c-1 1-1 0-2-1l-1 1h0l-2-2c1 0 2-1 2-1v-2h-2l-2 1-1-1 2-2v-1c-1-1-1-1-2-1v-1-2h0v-1c-1 1-2 1-3 1h-1c1-2 2-3 3-4h-4c1-2 2-4 4-5 1 0 2 0 3-1h0c-1-1-1-1-1-2h1z" class="N"></path><path d="M171 291v-1l1-2c2-1 4-4 6-5 1 1 2 1 2 2l-3 4c-2 1-3 2-5 3 0 1-1 1-2 2h-1c1-1 1-2 2-3z" class="n"></path><path d="M159 290c1-1 1-1 3-1v-1l1 1-3 3v1h0l-8 10c-1 1-1 1-1 2h-1l-2 1c0-1 3-6 4-7h1c0-1 2-2 3-4v-1c1 0 2-1 3-2l1-1v-1h-1z" class="Q"></path><path d="M152 305c1-1 2-1 2-2h1 0v1l1 1 1-1v1h0c-1 1-1 2-1 3h2c-1 1-2 3-3 4-1 2-3 5-4 7 0 2 0 3-1 4v1h-1v-1-1c0-1 0-3-1-4-1 1-1 1-1 2h-1v-1s0-1 1-1v-2h-2c1-2 2-4 4-6 1-2 2-3 3-5z" class="X"></path><path d="M152 305l1 1h-1c0 2 0 3-1 5 0 1-2 2-2 4 0 1 0 2-1 3h0c-1 1-1 1-1 2h-1v-1s0-1 1-1v-2h-2c1-2 2-4 4-6 1-2 2-3 3-5z" class="m"></path><path d="M149 315c1-2 5-7 6-7 0 1 0 1-1 2l1 2c-1 2-3 5-4 7 0 2 0 3-1 4v1h-1v-1-1c0-1 0-3-1-4h0c1-1 1-2 1-3z" class="B"></path><path d="M149 322l2-4v1c0 2 0 3-1 4v1h-1v-1-1z" class="D"></path><path d="M151 318l3-8 1 2c-1 2-3 5-4 7v-1z" class="f"></path><path d="M159 290h1v1l-1 1c-1 1-2 2-3 2v1c-1 2-3 3-3 4h-1c-1 1-4 6-4 7l2-1-2 4 1 1c-2 2-3 4-4 6-1 0-2 2-3 2v-1c-1 1-2 2-4 3 1-2 3-4 4-6v-2h-3l-1 1-1-1c1 0 1-1 1-1h1l-1-1c1-1 2-2 2-3 2-2 3-4 4-6 1 0 2-2 3-3 0-1 1-1 1-2h0c1-1 1-1 2-1v1h0c1 1 1 1 2 1 1-2 3-4 5-6h0l2-1z" class="I"></path><path d="M150 296c1 1 1 1 2 1l-3 3c0-2 0-3 1-4z" class="h"></path><path d="M139 312h1c2-1 3-3 5-5h1l-4 7v-2h-3z" class="y"></path><path d="M144 301c1 0 2-2 3-3 0-1 1-1 1-2h0c1-1 1-1 2-1v1h0c-1 1-1 2-1 4-2 1-3 2-3 4h-1l1-1c-3 0-3 4-6 4 2-2 3-4 4-6z" class="j"></path><path d="M146 307c2-1 3-8 6-8-1 1-4 6-4 7l2-1-2 4 1 1c-2 2-3 4-4 6-1 0-2 2-3 2v-1c-1 1-2 2-4 3 1-2 3-4 4-6l4-7z" class="z"></path><path d="M142 317c1-2 2-3 4-4 1-1 1-3 2-4l1 1c-2 2-3 4-4 6-1 0-2 2-3 2v-1z" class="L"></path><path d="M164 300c0-1 0-1 1-2v-1l1-1 2-3h0c1-1 1-2 3-2-1 1-1 2-2 3h1c1-1 2-1 2-2l1 1-4 4c0 1-1 1-1 2h0l-1 1c0 1-1 2-1 3l-1 1v1c-1 1-1 2-1 3v2 2 5 1l-3 1-1 2v-1l1-2c-1-1-1-2-1-3h-1v1c-1 0-1 0-1-1l-1 1c0-1 0-1-1-1l-3 6-1 3c-1 0-1 0-2-1 1-1 1-2 1-4 1-2 3-5 4-7 1-1 2-3 3-4 2-3 3-5 6-8z" class="e"></path><path d="M164 300v1c-1 1-2 2-3 4-2 3-4 6-5 10l-3 6-1 3c-1 0-1 0-2-1 1-1 1-2 1-4 1-2 3-5 4-7 1-1 2-3 3-4 2-3 3-5 6-8z" class="S"></path><path d="M158 315c1-3 3-7 5-10 1-1 1-1 2-1v1c-1 1-1 2-1 3v2 2 5 1l-3 1-1 2v-1l1-2c-1-1-1-2-1-3h-1v1c-1 0-1 0-1-1z" class="E"></path><path d="M164 312v5 1l-3 1-1 2v-1l1-2c1-1 1-2 2-3l1-3z" class="B"></path><path d="M160 249l1 1h1l1-1 1 1c-1 1-1 1-1 2 1 1 1 1 2 1l1 1v1h0l1 1 1 1h0l2 2v-1c2-1 3-1 4-3h1l1 1h1l2 1c0 1-1 2-1 2h2v1h0c1-1 2-1 3-2h2l1 1h0c-2 1-4 3-6 4h2l-2 1 1 1h2c1 0 1 0 1 1l3 1h0 1c-1 1-1 1-2 1l1 1h1c0 1-4 4-5 5 2-1 3-1 4-2 0 1-1 1-2 2s0 2 0 4h0-1l1 2c-1 0-1 0-2-1h-1l-4 3c2-2 3-4 4-6-3 2-5 4-7 5h0l1-1-1-1h-1 0v-1-1h-1c-1 1-2 1-3 1h0 0-1-1l1-2c1 0 1 0 2-1h-1c0-1-1-1-1-1l-1-1h-1-1c-1 1-1 0-2-1l-1 1h0l-2-2c1 0 2-1 2-1v-2h-2l-2 1-1-1 2-2v-1c-1-1-1-1-2-1v-1-2h0v-1c-1 1-2 1-3 1h-1c1-2 2-3 3-4h-4c1-2 2-4 4-5 1 0 2 0 3-1h0c-1-1-1-1-1-2h1z" class="O"></path><path d="M168 257l-1 1c-1-1-1-1-1-2h1l1 1h0z" class="G"></path><path d="M181 266c1 1 1 0 1 1h4v1l1 1c-2 0-3 2-5 2v-1l1-1h0l1-1h-1-1c0-1 0-1-1-2z" class="D"></path><path d="M171 262c1 1 2 1 2 3h1v1l-3 1h0c0-1 1-1 1-2l-2 1v-1l1-1v-1-1z" class="T"></path><path d="M180 264h0l1 1h2c1 0 1 0 1 1l3 1h0 1c-1 1-1 1-2 1v-1h-4c0-1 0 0-1-1h-2l1-2z" class="C"></path><path d="M178 269l-3-3v-1c1 0 2 1 2 1 1 1 3 1 4 1v1l-2 2c-1 0-1 0-1-1z" class="p"></path><path d="M164 256h1 1l-2 2-1 1c-1 1-2 1-4 1l-1 1v-1l2-2 1 1 3-3z" class="h"></path><path d="M172 262c1-1 2-1 3-1l1 1c1-1 1-1 2-1-1 1-2 1-2 2v1l2-2 1 1h1 0 2l-2 1h0-1-1c0 1-2 1-3 0v-1s-2-1-3-1zm-12-5c1-1 2-1 3-2l1 1-3 3-1-1-2 2c-1 1-2 1-3 1h-1c1-2 2-3 3-4h3z" class="D"></path><path d="M185 258l1 1h0c-2 1-4 3-6 4h0-1l-1-1-2 2v-1c0-1 1-1 2-2 1 0 2-1 2-1 1-1 2-1 3-2h2z" class="u"></path><path d="M184 278c-1-1-1-3-2-4s-2-2-2-3 1-2 2-2h1l-1 1v1c2 0 3-2 5-2h1c0 1-4 4-5 5 2-1 3-1 4-2 0 1-1 1-2 2s0 2 0 4h0-1z" class="f"></path><path d="M157 252c1 0 2 0 3-1 1 1 2 1 3 2l1 1-1 1c-1 1-2 1-3 2h-3-4c1-2 2-4 4-5z" class="z"></path><path d="M161 254l1 1c-1 1-1 1-2 1h-1c0-1 1-1 2-2z" class="C"></path><path d="M163 253l1 1-1 1c-1 1-2 1-3 2v-1c1 0 1 0 2-1l-1-1c1 0 1 0 2-1zm11 2h1l1 1h1l2 1c0 1-1 2-1 2h2v1h0s-1 1-2 1-1 0-2 1l-1-1c-1 0-2 0-3 1 0-1-1-2-1-2l-1-1v-1c2-1 3-1 4-3z" class="S"></path><path d="M176 256h1l2 1c0 1-1 2-1 2-1 0-1 1-2 1v-1l-2-1v-1c1 1 1 1 2 1h0c0-1 0-1-1-1v-1h1z" class="l"></path><path d="M174 255h1l1 1h-1v1c1 0 1 0 1 1h0c-1 0-1 0-2-1 0 0-1 0-1 1-1 1-2 1-2 2l-1-1v-1c2-1 3-1 4-3z" class="C"></path><path d="M171 268l1 1-1 1h1 0c1-1 2-1 3-1 1-1 1 0 2 1l1-1c0 1 0 1 1 1h0v1c0 1 0 1-1 2h1c-1 2-3 4-5 5v-1h-1c-1 1-2 1-3 1h0 0-1-1l1-2c1 0 1 0 2-1h-1c0-1-1-1-1-1l-1-1h-1v-2l2-2h1l1-1z" class="t"></path><path d="M171 273l-1-1v-1h2 0c0 1-1 2-1 2z" class="V"></path><path d="M179 270v1c0 1 0 1-1 2l-2 2c0-1 0-1 1-2 0-1 0-1 1-2l1-1z" class="C"></path><path d="M171 268l1 1-1 1h1 0c1-1 2-1 3-1-1 1-1 2-2 2l-1-1h-1v-1h-1l1-1z" class="G"></path><path d="M172 271l2 1h0v1l-1 1-2 1h-1l1-2s1-1 1-2z" class="p"></path><path d="M173 274l1-1h0 1v1 2c0-1 1-1 1-1h0l2-2h1c-1 2-3 4-5 5v-1h-1c-1 1-2 1-3 1h0 0-1-1l1-2c1 0 1 0 2-1l2-1z" class="I"></path><path d="M173 274c-1 2-2 2-3 4h0-1-1l1-2c1 0 1 0 2-1l2-1z" class="K"></path><path d="M160 262c2-1 5-4 8-4l1 1c0 1 1 2 2 2v1 1 1l-1 1v1l2-1c0 1-1 1-1 2h0v1l-1 1h-1l-2 2v2h-1c-1 1-1 0-2-1l-1 1h0l-2-2c1 0 2-1 2-1v-2h-2l-2 1-1-1 2-2v-1c-1-1-1-1-2-1v-1l2-1z" class="S"></path><path d="M169 259c0 1 1 2 2 2l-2 1-1-1c0-1 0-1 1-2z" class="D"></path><path d="M171 263v1l-1 1v1h0-1l-1-1h0c1-1 2-2 3-2z" class="C"></path><path d="M164 265c1 0 2-1 3-1v1h1 0l-2 1h-1-2l1-1z" class="l"></path><path d="M160 262l1 2c2 0 3-1 4-2v1c0 1-1 1-2 2h-3c-1-1-1-1-2-1v-1l2-1z" class="T"></path><path d="M160 265h3 1l-1 1h2v1c-1 0-2 0-4 1l-2 1-1-1 2-2v-1z" class="Z"></path><path d="M160 265h3 1l-1 1h-3v-1z" class="W"></path><path d="M170 266l2-1c0 1-1 1-1 2h0v1l-1 1h-1l-2 2v2h-1c-1 1-1 0-2-1l-1 1h0l-2-2c1 0 2-1 2-1v-2h-2c2-1 3-1 4-1v-1h1l2-1 1 1h1 0z" class="t"></path><path d="M165 267h1 0c0 1 0 1-1 2 0 0-1 0-2 1v-2h-2c2-1 3-1 4-1z" class="P"></path><path d="M170 266l2-1c0 1-1 1-1 2h0v1l-1 1h-1 0c0-1 0-1 1-2-1-1-2 1-3 1v-1l-1-1 2-1 1 1h1 0z" class="Z"></path><path d="M166 270l3-1h0l-2 2v2h-1c-1 1-1 0-2-1l-1 1h0l-2-2c1 0 2-1 2-1 1-1 2-1 2-1l1 1z" class="G"></path><path d="M163 270c1-1 2-1 2-1l1 1c0 1-1 2-2 2 0-1-1-1-1-2z" class="r"></path><path d="M329 606c1 0 4 0 5 1v1c4 0 9 1 12 0h3v1h7 5c2 1 5 1 7 1l19 1c1 0 5 0 6 1s1 3 0 5c-1 3-3 5-5 6-3 2-5 5-8 6l-2 1v1l-5 2 4-1c2 1 4 1 6 1l1 1h-1v1h0 1l3 3c1 1 2 1 2 2l-1 1c-1 1-1 1-2 1-3 0-5-3-8-3-2-1-4-1-6-1l-12-3-1-1c-2 0-5-1-6 0l-1-1 1-1c-6 0-13-1-20-2-5-1-9-2-14-2h-1v-1c6-4 15-4 23-5h0v-1h-2c1 0 2-1 2-1h2l-1-1c-2-5-7-6-11-9h0c-1-1-2-1-3-2l1-2z" class="n"></path><path d="M361 609c2 1 5 1 7 1-1 0-3 1-4 1-1-1-2 0-3-1h-4v1h0 2 5c0 1 1 1 1 1l-12-2h1l7-1z" class="M"></path><path d="M334 608c4 0 9 1 12 0h3v1h7 5l-7 1h-1l12 2c2 1 4 1 6 2l-38-5v-1h1z" class="D"></path><path d="M356 609h5l-7 1h-1-9c4-1 8-1 12-1z" class="Q"></path><path d="M329 606c1 0 4 0 5 1v1h-1v1h-1c6 4 10 6 13 12 5 1 10 2 14 4 1 0 3 0 3 1l1 1h-1c-4-2-7-3-12-4h-4c1 2 1 4-1 6l-1 1-1 1c0-3 1-6 0-8-1-1-2-1-2-1h0v-1h-2c1 0 2-1 2-1h2l-1-1c-2-5-7-6-11-9h0c-1-1-2-1-3-2l1-2z" class="J"></path><path d="M353 632c3 0 7 0 10 2 6 1 8-5 11-9 1-2 5-6 8-7h1c1 2-1 7-2 9h0c0-1 0-2 1-4l1-5c-2 1-5 3-7 5-2 3-4 7-6 10l8-3v1l-5 2 4-1c2 1 4 1 6 1l1 1h-1v1h0 1l3 3c1 1 2 1 2 2l-1 1c-1 1-1 1-2 1-3 0-5-3-8-3-2-1-4-1-6-1l-12-3-1-1c-2 0-5-1-6 0l-1-1 1-1z" class="i"></path><path d="M383 633l1 1h-1v1h0 1l3 3c1 1 2 1 2 2l-1 1h-2v-1c-1-1-4-3-6-4-1-1-2-1-3-2l6-1z" class="N"></path><path d="M386 641c-2 0-5-2-7-3s-5-1-7-1l-8-1 2-1c2 0 8-1 10 0v-1l5 3 5 3v1z" class="q"></path><path d="M537 581l6 2h4c1 1 3 2 4 4s2 2 3 4l1 1 3 5h0c1 2 2 3 2 5h0c0 1 1 1 1 2v2c1 2 3 5 3 8-2-2-2-5-3-7-2-3-5-6-7-9-4-4-8-10-13-13-2-1-5-2-8-3 2 1 3 2 5 4s3 4 4 7c1 2 0 4-1 6-2 4-6 4-8 7l1 1c1 0 3 1 4 2 3 4 5 11 4 16 0 3-1 5-3 7-2 1-4 1-6 1s-3-2-4-3-1-2 0-3c0-1 0-1 1-1 2 0 3 0 4 2h1 1c1-2 1-5 0-7a10.85 10.85 0 0 0-8-8c-3-2-5-1-8-1-2 0-6-4-8-5 2 6 4 12 1 18-1 3-3 5-6 6-2 1-3 1-5 0-1-1-3-3-3-5v-2c2-2 4-2 7-3 1 0 2-1 3-2h1 1v-2c0-1 1-3 2-4-1-6-3-12-8-17-3-3-6-6-9-8-9-5-18-6-28-6h0 44 8 10 5-1l1-1h2z"></path><path d="M543 585h2l4 3h0c-1 1-1 1-2 1l-4-4z" class="M"></path><path d="M547 589c1 0 1 0 2-1l3 4c1 1 1 2 2 2 0 1 1 2 1 3h-1l-7-8z" class="n"></path><path d="M537 581l6 2h4l-1 1c-1 0-1 0-1 1h-2l-2-1c-2-1-4-1-6-2h-1l1-1h2z" class="AD"></path><path d="M538 614c-2-2-4-4-6-5-1-1-2 0-2-1 1-1 2-1 4 0s4 3 5 5l-1 1z" class="X"></path><path d="M541 593c0 3 0 5-2 8l-1 1-1-1h-1-1 0v-1s1-1 2-1 1 0 1-1l-2 1h-2c-1-1-1-2 0-3s3-1 5-1h0l-1 1 1 1h0c1-1 1-2 1-3l1-1z" class="o"></path><path d="M545 585c0-1 0-1 1-1l1-1c1 1 3 2 4 4s2 2 3 4l1 1 3 5h0c1 2 2 3 2 5h0c0 1 1 1 1 2v2l-7-9h1c0-1-1-2-1-3-1 0-1-1-2-2l-3-4h0l-4-3z" class="X"></path><path d="M512 589c2 0 3 0 4 1 1 0 1 1 1 2 1 1 0 3-2 4-1 1-2 2-3 2-3 0-5-2-6-3 2-2 4-5 6-6z" class="n"></path><path d="M513 616h0c1 2 0 4 0 6 0 3-2 6-5 8-1 0-3 1-4 0-1 0-3-2-4-3v-3c1-1 3-1 5-1 1-1 3-1 4-2h1l-1 2h-1l1 1h0c0 1-2 2-3 3-1 0-3 0-4-1h-1c2 2 3 2 5 2 1 0 2-1 2-1 2-1 4-4 4-6v-4c0-1 0-1 1-1z" class="y"></path><path d="M539 613c1 2 1 4 2 6 1 3 1 7-1 10-1 2-2 3-4 3h-4c-1-1-2-2-3-4l1-1v-1c3 1 4 3 6 3 2-1 2-2 2-4 0-4-2-7-4-10-3-3-7-5-11-5h-2c6-1 9-1 14 2 1 1 2 3 3 5 1-1 0-2 0-3l1-1zm-1-11c-3 2-6 4-10 6-2 0-5 1-7 1-3 1-5-1-7-2-2-2-4-6-6-9 1 1 2 2 3 2 1 2 5 3 6 3h1c2 1 4 1 5 1 3-1 5-1 8-2 1-1 2-2 4-2v1h0 1 1l1 1z" class="n"></path><path d="M523 604c3-1 5-1 8-2 1-1 2-2 4-2v1h0c-1 0-2 1-2 1-2 1-3 2-4 2h-1c-1 1-3 1-4 1l-1-1zm17-10c-2 0-5 0-7 1-1 1-1 4-1 5-3 2-6 3-9 3-4 0-9-1-12-3v-1c2 0 3 0 4-1 3-2 3-4 4-8h1c2-2 10-6 13-6l2 1c3 1 5 6 6 8l-1 1z" class="q"></path><path d="M189 589c3-1 6-1 9-2v1h1 3 2v1c0 2 1 5-1 7h1-5c2 1 5 0 7 1h-12c0 1 1 1 1 1l-15 3-9 3 2 1-9 4c0 1-1 2-2 2v-1l-2 1-9 3 1 1c1-1 2-1 3-1s1 1 0 1c0 1-3 3-4 4v-2c-5 3-9 3-14 2-2 0-4 0-7-1l-2-2-3-3v-1c-1-1-2-2-2-5 1 0 1 0 1-1l1-1s0-1-1-1h0c-2 1-3 1-5 2h0-8 0v-1l3-3 1-2h0c2-4 3-7 6-10 2-2 3-2 5-2h3 1l-2 1 1 1 2-1h2 0l2 1v-2l2 1h5c4-1 7-1 11-2h0 3l1 1c-2 0-3 1-5 0-1 0-2 1-4 1-3 0-7 1-10 1l-2 4c1 0 2 1 3 1 2 1 4 1 6 1 4-1 7-1 11-1 1-1 3-2 5-2l1 2 5-1 4-1 2-1h2 2l4-1h0c3 0 6-1 8-1l1-1h-1z" class="o"></path><path d="M129 607c2 1 4 1 7 1h0v1h2 0c1 1 2 1 4 1h-2c-4 0-8 0-12-2l1-1z" class="I"></path><path d="M136 608h0v1h2-4l-1-1h3z" class="Q"></path><path d="M149 605v1h1 3s0 1 1 1c-2 1-7 1-9 2-1 0-2 0-3 1-2 0-3 0-4-1 4-1 7-1 11-4z" class="D"></path><path d="M158 601v-1s0 1 1 1l1 2c1 0 1 0 2-1v1h1c1 0 1 0 1 1l-10 3c-1 0-1-1-1-1v-1h-1l1-1h3l2-2h-1l1-1z" class="W"></path><path d="M158 601v-1s0 1 1 1l1 2h-2v-1h-1l1-1z" class="j"></path><path d="M182 598l12-1c0 1 1 1 1 1l-15 3-9 3c-1 0-2 1-4 1 1-1 1-1 2-1h0l1-1c2 0 4-1 6-2h0l3-1h2 1c0-1 1 0 1 0s0-1 1-1h1 1 2-3-5l2-1z" class="e"></path><path d="M167 605c2 0 3-1 4-1l2 1-9 4c0 1-1 2-2 2v-1l-2 1c-1-1-2-1-3-2h0c3-1 7-2 10-4z" class="d"></path><path d="M157 609c2 0 4 0 6-1h0c0 1-1 1-1 2h0l-2 1c-1-1-2-1-3-2z" class="Q"></path><path d="M173 592h2l1 1c1 1 1 2 1 4l1 2c-3 1-7 2-10 3-1 0-1 0-2-1v-2-4l1-1 4-1 2-1z" class="U"></path><path d="M168 597l1 1v1h-1 0v-2z" class="x"></path><path d="M169 596v-1h1v1 3c0-1-1-1-1-1l-1-1 1-1z" class="S"></path><path d="M173 592h2c0 1-1 2-1 2l-1 1h-2v-2l2-1z" class="u"></path><path d="M167 594l4-1v2l-1 1v-1h-1v1c-1-1-2-1-3-1l1-1z" class="F"></path><path d="M139 595c2 1 4 1 6 1 4-1 7-1 11-1 1-1 3-2 5-2l1 2 5-1-1 1v4 2c1 1 1 1 2 1l-4 2c0-1 0-1-1-1h-1v-1c-1 1-1 1-2 1l-1-2c-1 0-1-1-1-1v1l-1 1h1l-2 2h-3l-1 1c-1-1-1-2-2-2l-1-1-1-3c-1 0-1 0-2 1v1h0c0-1-1-1-1-1l-1 1-1-1-1-1 1-2h-2-3c0-1 0-1 1-2z" class="W"></path><path d="M141 597l1-1h2v1h-1-2z" class="j"></path><path d="M162 596l1 1h-1v1l-1 1c0-1-1-1-2-2h0c1-1 2-1 3-1z" class="J"></path><path d="M159 597c1 1 2 1 2 2h1l-1 1h-1v1l2 1c-1 1-1 1-2 1l-1-2v-2-2z" class="I"></path><path d="M162 595l5-1-1 1v4 2c1 1 1 1 2 1l-4 2c0-1 0-1-1-1h-1v-1l-2-1v-1h1l1-1h-1l1-1v-1h1l-1-1v-1h0z" class="E"></path><path d="M163 597c1 1 1 2 1 4h-1v-1l-1-1h-1l1-1v-1h1z" class="K"></path><path d="M165 600c0-1-1-3-1-4 1-1 1-1 2 0v3 2s-1 0-1-1z" class="O"></path><path d="M162 599l1 1v1h1l1-1c0 1 1 1 1 1 1 1 1 1 2 1l-4 2c0-1 0-1-1-1h-1v-1l-2-1v-1h1l1-1z" class="u"></path><path d="M162 599l1 1v1c-1 1-1 0-3 0v-1h1l1-1z" class="V"></path><path d="M153 596h1l1 1h0 2v2 2h1l-1 1h1l-2 2h-3l-1 1c-1-1-1-2-2-2l-1-1-1-3c0-1 0-1 1-1 1-1 3-1 4-2z" class="I"></path><path d="M154 601c1 1 1 2 1 3h1-3c1 0 1-2 1-3z" class="O"></path><path d="M154 600c1 1 2 2 3 2h1l-2 2h-1c0-1 0-2-1-3v-1z" class="K"></path><path d="M149 598c1 0 2-1 3 0 0 1 1 2 0 3s-2 1-3 1l-1-3c0-1 0-1 1-1z" class="N"></path><path d="M153 596h1l1 1h0 2v2 2h1l-1 1c-1 0-2-1-3-2s-1-1-1-2l1-1-1-1z" class="C"></path><path d="M155 600l1-1h1v2c-1 0-1-1-2-1z" class="G"></path><path d="M155 597h2v2h-1l-1 1v-3z" class="d"></path><defs><linearGradient id="H" x1="148.023" y1="606.301" x2="144.055" y2="617.969" xlink:href="#B"><stop offset="0" stop-color="#cacac7"></stop><stop offset="1" stop-color="#f9f6fa"></stop></linearGradient></defs><path fill="url(#H)" d="M125 605h1c1 0 2 2 3 2v-1 1l-1 1-1-1c0 1 0 1 1 2s3 2 4 3 2 1 4 1c3 0 6 0 10-1h0l9-3h2 0c1 1 2 1 3 2l-9 3 1 1c1-1 2-1 3-1s1 1 0 1c0 1-3 3-4 4v-2c-5 3-9 3-14 2-2 0-4 0-7-1l-2-2-3-3v-1c-1-1-2-2-2-5 1 0 1 0 1-1l1-1z"></path><path d="M137 616h5c1 0 2 0 2 1-2 0-4 1-6 1-1-1-1-1-1-2z" class="a"></path><path d="M124 606c1 1 2 1 2 2s1 1 1 2l-1 1c1 0 2 1 3 2 2 1 4 1 5 3h-1c-2-1-4-2-5-2s-2-1-3-2-2-2-2-5c1 0 1 0 1-1z" class="I"></path><path d="M125 605h1c1 0 2 2 3 2v-1 1l-1 1-1-1c0 1 0 1 1 2s3 2 4 3 2 1 4 1c3 0 6 0 10-1h0l9-3v1c-1 1-1 0-2 1-1 0-3 1-4 1l-2 1h-2-1c-3 1-11 1-14-1l-3-2c0-1-1-1-1-2s-1-1-2-2l1-1z" class="P"></path><path d="M125 613v-1c1 1 2 2 3 2s3 1 5 2h1 1l1-1 1 1c0 1 0 1 1 2 2 0 4-1 6-1l7-3 1 1c1-1 2-1 3-1s1 1 0 1c0 1-3 3-4 4v-2c-5 3-9 3-14 2-2 0-4 0-7-1l-2-2-3-3z" class="h"></path><path d="M128 614c1 0 3 1 5 2h1 1l1-1 1 1c0 1 0 1 1 2-4 0-7-2-10-4z" class="p"></path><path d="M152 615c1-1 2-1 3-1s1 1 0 1c0 1-3 3-4 4v-2h0c1 0 1 0 2-1-1 0-5 2-6 2h-3c0-1 1-1 2-1 2-1 4-1 5-2h1z" class="d"></path><path d="M189 589c3-1 6-1 9-2v1h1 3 2v1c0 2 1 5-1 7h1-5c2 1 5 0 7 1h-12l-12 1-2 1h-2l-1-2c0-2 0-3-1-4l-1-1h2l4-1h0c3 0 6-1 8-1l1-1h-1z" class="B"></path><path d="M201 591l1-1h0v5h0-1v-4z" class="g"></path><path d="M182 592h0v1h0c1 1 1 2 0 3v1l-1-1c0-1 0-3 1-4z" class="v"></path><path d="M190 591h1v1c1 1 1 2 0 3l-1-1c-1-1 0-2 0-3z" class="j"></path><path d="M199 588h3l1 1v1h-1 0l-1 1h-1v2h0c-1 0-1 0-1-1-1-1-1-2 0-3v-1z" class="Y"></path><path d="M189 589c3-1 6-1 9-2v1h1v1l-10 1 1-1h-1z" class="G"></path><path d="M177 592l4-1v2h-1c0 1 0 2 1 3s0 2 1 2l-2 1h-2l-1-2c0-2 0-3-1-4l-1-1h2z" class="F"></path><path d="M181 596l-2 1-1-2c0-1 0-1 1-2h1c0 1 0 2 1 3z" class="U"></path><path d="M153 587h3l1 1c-2 0-3 1-5 0-1 0-2 1-4 1-3 0-7 1-10 1l-2 4c1 0 2 1 3 1-1 1-1 1-1 2h3 2l-1 2 1 1 1 1 1-1s1 0 1 1h0v-1c1-1 1-1 2-1l1 3 1 1c1 0 1 1 2 2h1v1h-3-1v-1c-4 3-7 3-11 4h0-2v-1h0c-3 0-5 0-7-1v-1 1c-1 0-2-2-3-2h-1s0-1-1-1h0c-2 1-3 1-5 2h0-8 0v-1l3-3 1-2h0c2-4 3-7 6-10 2-2 3-2 5-2h3 1l-2 1 1 1 2-1h2 0l2 1v-2l2 1h5c4-1 7-1 11-2h0z" class="j"></path><path d="M142 599h-1c-2 0-1-1-2-1s-2 0-3-1h0 2 3 2l-1 2z" class="F"></path><path d="M135 604c-1-1-2-1-2-2l-2-1c1-1 2-1 3-1 2 0 5 0 7 1-1 0-2 0-4 1h-2v2z" class="Q"></path><path d="M144 601l1-1s1 0 1 1h0v2l-3 2c0 1-1 1-2 1-1 1-1 1-2 1v-1h0v-1h-2l1-1c2 0 4-1 6-1 1 0 1-1 1-1l-1-1z" class="M"></path><path d="M146 601h0v-1c1-1 1-1 2-1l1 3 1 1c1 0 1 1 2 2h1v1h-3-1v-1c-4 3-7 3-11 4h0-2v-1c0-1 1-1 1-1h-6c-1-1-2-2-2-3h1 0v-2h1l2 1c1 1 1 1 2 1h0v-2h2c2-1 3-1 4-1l1 1c-1 1-3 1-4 2h-2c0 1 1 1 1 1h2v1h0v1c1 0 1 0 2-1 1 0 2 0 2-1l3-2v-2h0z" class="e"></path><path d="M146 601h0v-1c1-1 1-1 2-1l1 3 1 1c1 0 1 1 2 2h1v1h-3-1v-1-1h-1c-1 0-4 1-5 1l3-2v-2h0z" class="R"></path><path d="M150 603c1 0 1 1 2 2h1v1h-3l-1-2 1-1z" class="i"></path><path d="M129 588h1l-2 1 1 1 2-1h2 0l2 1c1 1 1 1 1 2l-1 1v1h0c0 1 0 1-1 1s-1 0-1 1l-3 3-3 3-3 2h0c-2 1-3 1-5 2h0-8 0v-1l3-3 1-2h0c2-4 3-7 6-10 2-2 3-2 5-2h3z" class="n"></path><path d="M124 603v-1h3l-3 2h0v-1z" class="e"></path><path d="M124 603v1c-2 1-3 1-5 2h0c0-1 1-1 1-1l1-2h1v1l2-1z" class="N"></path><path d="M133 589h0l2 1c1 1 1 1 1 2l-1 1v1h0c0 1 0 1-1 1s-1 0-1 1l-3 3c0-1-1-1-1-2l3-2c0-1-1-1-1-3 0 1-1 2-1 2-1 2-2 3-3 4h-1c0-1 1-2 2-3s2-2 2-3c1-1 1-2 1-3h2z" class="o"></path><path d="M133 589l2 1c1 1 1 1 1 2l-1 1v-1c-1-1-1-2-2-3z" class="Q"></path><path d="M133 596v-2c-1-1-1-1-1-2h0c1 0 1 0 2 1 0 0 1 0 1 1h0c0 1 0 1-1 1s-1 0-1 1z" class="e"></path><path d="M129 590l2-1c0 1 0 2-1 3 0 1-1 2-2 3s-2 2-2 3c-2-1-3 0-5 1s-4 2-7 3l1-2v1c2-1 5-2 6-3v-1c0-1 0-2 1-3h1 1l1 1c1-1 1-2 1-2l3-3z" class="D"></path><path d="M129 590l2-1c0 1 0 2-1 3-2 1-4 3-6 5h-1c0-1 0-1 1-2h1c1-1 1-2 1-2l3-3z" class="AD"></path><path d="M129 588h1l-2 1 1 1-3 3s0 1-1 2l-1-1h-1-1c-1 1-1 2-1 3v1c-1 1-4 2-6 3v-1h0c2-4 3-7 6-10 2-2 3-2 5-2h3z" class="N"></path><path d="M122 594c1-2 4-3 6-5l1 1-3 3s0 1-1 2l-1-1h-1-1z" class="C"></path><path d="M196 496l2-1v8c0 1 0 1-1 2l1 1c0 1-1 1-2 1-3-1-5 0-7 1-4 1-6 4-7 7v5c0 4 1 9 2 13v3 3h3v1h6c3 1 5 3 7 7h0c0 1 1 2 1 3l1 3c1 0 1 1 2 0 1 0 1-1 2-1l1 1 1 1v-2c1 0 1 1 2 1h1 7c-2 4-5 8-7 11l-2 2-5 5h-1c0 1-1 2-2 3-1 0-1 0-1 1-1 1-2 1-4 2 1 0 1 0 2 1h-1c-1 0-2 1-3 2-2 1-4 2-6 2l-5 2-8 2-3 1v-2c0-1 0-1 1-2v-1l1-1c-1 1-2 1-3 0l2-1v-3h-1l1-1c0-1 1-1 1-2-1 0-2-1-2-2h0l2 1v-1-1h0v-2h0v-3l1-4h0v-2c-1 0-1 0-1-1 2 0 2 0 3-1v-2l1-1c-3-4-5-5-9-7h5c2-1 4-1 5-3s3-6 2-8c-1-1-2-1-4-2v-2c0-2-1-2-1-3l-8 2c-1 1-1 0-1 0 0-1 1-2 2-3 1-2 2-4 3-5l5-11h-1c2-3 5-6 7-9l-1-6 1-1 1 1c0-1 0-1 1-1l1 2s1 0 2 1l2-1 6-3z" class="q"></path><path d="M169 529c1-2 2-4 3-5 0 1 1 2 0 3l-1 1h0l-2 1z" class="N"></path><path d="M178 560v6l-3 1 1-3h0v-4h2z" class="G"></path><path d="M178 555v5h-2v4h0v-2h-1 0v-2c-1 0-1 0-1-1 2 0 2 0 3-1v-2l1-1z" class="J"></path><path d="M184 539h3v1c-3 2-4 4-5 7 0 1 2 4 2 4v1l-1-1c-1-1-1-3-1-4-1-2 2-6 2-8z" class="E"></path><path d="M184 554c1 1 2 1 3 2v2h2v2c-1 1-1 2-2 3v-1-1c-1 1-2 1-2 2h-1 0v-3-6z" class="U"></path><path d="M184 563v-3c1 1 1 1 2 1h1c-1 1-2 1-2 2h-1 0z" class="F"></path><path d="M184 551c2 1 3 2 5 2l2-1h1l1 1c0 1-1 1-1 2-1 1-2 1-3 2v1h-2v-2c-1-1-2-1-3-2v-2-1z" class="AB"></path><path d="M178 576c4-4 4-8 6-13h0 1c0 1 0 2-1 3l1 1-2 4v1l-1 1-1 1-7 7h0c-1 1-2 1-3 0l2-1c2-1 3-2 5-4z" class="i"></path><path d="M184 566l1 1-2 4v1l-1 1-1 1 3-8z" class="d"></path><path d="M175 562h1v2l-1 3 3-1-2 8 2 2c-2 2-3 3-5 4v-3h-1l1-1c0-1 1-1 1-2-1 0-2-1-2-2h0l2 1v-1-1h0v-2h0v-3l1-4z" class="L"></path><path d="M175 568c1 2 1 2 1 4l-1 1v-2-3z" class="P"></path><path d="M175 562h1v2l-1 3v1 3h-1v-2h0v-3l1-4z" class="c"></path><path d="M174 574h1v2 1c1-1 1-2 1-3l2 2c-2 2-3 3-5 4v-3h-1l1-1c0-1 1-1 1-2z" class="a"></path><path d="M189 560l1 1c0 1 0 2-1 4 0 2 0 3-1 5h1v1c-1 1-2 2-4 2h-2v-1-1l2-4-1-1c1-1 1-2 1-3s1-1 2-2v1 1c1-1 1-2 2-3z" class="k"></path><path d="M185 563c0-1 1-1 2-2v1l-2 5-1-1c1-1 1-2 1-3z" class="s"></path><path d="M188 570c-1 0-3 1-4 2 1-3 2-7 5-9h0v2c0 2 0 3-1 5z" class="C"></path><path d="M196 496l2-1v8c0 1 0 1-1 2-1-2-2-3-4-3h-1c-2 0-5 1-7 2h0c-3 2-6 6-8 9h-1c2-3 5-6 7-9l-1-6 1-1 1 1c0-1 0-1 1-1l1 2s1 0 2 1l2-1 6-3z" class="F"></path><path d="M194 499c1 0 2 0 3 1v2h0-1c-1-1-1-1-2-1v-2z" class="Z"></path><path d="M188 500l2-1h4v2c-1 0-2-1-3 0h0c-1 0-1 0-2-1v1h-1v-1z" class="H"></path><path d="M196 496c1 1 1 3 1 4-1-1-2-1-3-1h-4l6-3z" class="s"></path><path d="M182 498l1-1 1 1c0-1 0-1 1-1l1 2s-1 1 0 1c0 1 0 1 1 1-1 1-1 1-2 1h0-2v2l-1-6z" class="H"></path><path d="M195 548c2 2 4 3 4 7h0l-2 4-2 5-1 3-2 2c0-1-1-1-2-1 0 0-1 1-1 2h-1c1-2 1-3 1-5 1-2 1-3 1-4l-1-1v-2-1c1-1 2-1 3-2 0-1 1-1 1-2l-1-1h-1-1l-3-3h0c1 0 2 1 2 1h1 0 1c1-2 2-2 4-2z" class="AA"></path><path d="M195 557v4h0l2-2-2 5-1-1c1-1 1-4 1-6z" class="v"></path><path d="M187 549h0c1 0 2 1 2 1h1 0c3 0 3 0 4 2h0c0 1 1 2 1 3v2c0 2 0 5-1 6l1 1-1 3-2 2c0-1-1-1-2-1l1-2c0-1 1-2 1-3 1-1 1-2 1-3 0-2 1-3 1-4-1-1-1-1-2-1 0-1 1-1 1-2l-1-1h-1-1l-3-3z" class="F"></path><path d="M191 566c1 0 2 0 3 1l-2 2c0-1-1-1-2-1l1-2z" class="c"></path><defs><linearGradient id="I" x1="191.497" y1="555.546" x2="190.804" y2="566.656" xlink:href="#B"><stop offset="0" stop-color="#1a1919"></stop><stop offset="1" stop-color="#3b3b3b"></stop></linearGradient></defs><path fill="url(#I)" d="M189 557c1-1 2-1 3-2 1 0 1 0 2 1 0 1-1 2-1 4 0 1 0 2-1 3 0 1-1 2-1 3l-1 2s-1 1-1 2h-1c1-2 1-3 1-5 1-2 1-3 1-4l-1-1v-2-1z"></path><path d="M189 557c1 1 1 2 1 4l-1-1v-2-1z" class="F"></path><path d="M187 540h6c3 1 5 3 7 7h0v2 2c0-2-1-3-2-3l-3-2v2c-2 0-3 0-4 2h-1 0-1s-1-1-2-1h0l3 3h1l-2 1c-2 0-3-1-5-2 0 0-2-3-2-4 1-3 2-5 5-7z" class="y"></path><path d="M187 549c-1 0-1-1-1-2 0 0 1-1 2-1l1-1c1 0 3 0 4 1h2v2c-2 0-3 0-4 2h-1 0-1s-1-1-2-1h0z" class="X"></path><path d="M197 559l2-4c0 6-2 11-5 16-2 2-4 3-5 6h2l-1 2c-1 0-2 1-3 2h0v1h1l-5 2-8 2-3 1v-2c0-1 0-1 1-2v-1l1-1h0l7-7 1-1 1-1v1h2c2 0 3-1 4-2v-1c0-1 1-2 1-2 1 0 2 0 2 1l2-2 1-3 2-5z" class="b"></path><path d="M187 581c0-2 1-2 1-4h1 2l-1 2c-1 0-2 1-3 2z" class="M"></path><path d="M183 581l1-1v1c1 0 1-1 3 0v1h1l-5 2v-3z" class="S"></path><path d="M183 581v3l-8 2h0v-1c1-1 2-1 3-2v1h1c2-1 3-2 4-3z" class="E"></path><path d="M190 568c1 0 2 0 2 1l-6 8-2 2c1-1 2-3 2-5h0l-2 2-2 2c1-1 1-2 2-2v-1h0-2v-2l1-1v1h2c2 0 3-1 4-2v-1c0-1 1-2 1-2z" class="R"></path><path d="M181 574l1-1v2h2 0v1c-1 0-1 1-2 2l-1 1-3 4c-1 1-2 1-3 2v1h0l-3 1v-2c0-1 0-1 1-2v-1l1-1h0l7-7z" class="P"></path><path d="M173 583c1 1 1 1 2 1v1 1h0l-3 1v-2c0-1 0-1 1-2z" class="e"></path><path d="M195 546l3 2c1 0 2 1 2 3v-2-2c0 1 1 2 1 3l1 3c1 0 1 1 2 0 1 0 1-1 2-1l1 1 1 1v-2c1 0 1 1 2 1h1 7c-2 4-5 8-7 11l-2 2-5 5h-1c0 1-1 2-2 3-1 0-1 0-1 1-1 1-2 1-4 2 1 0 1 0 2 1h-1c-1 0-2 1-3 2-2 1-4 2-6 2h-1v-1h0c1-1 2-2 3-2l1-2h-2c1-3 3-4 5-6 3-5 5-10 5-16h0c0-4-2-5-4-7v-2z" class="N"></path><path d="M201 564c0 1 0 2-1 3l1 1c-1 2-3 4-4 5l-3 1c3-3 5-6 7-10z" class="I"></path><path d="M201 550l1 3v1c1 3 1 7 0 10v1l-1 3-1-1c1-1 1-2 1-3-1-2 0-5 0-7v-1-1-5z" class="a"></path><path d="M202 554c1 3 1 7 0 10v-4c0-1-1-1-1-3 1-1 1-2 1-3z" class="AD"></path><path d="M201 568l1-3h1l-2 4h1v-1l1 1 2-2c-1 2-1 3-2 4h0c0 1-1 2-2 3-1 0-1 0-1 1-1 1-2 1-4 2 1 0 1 0 2 1h-1c-1 0-2 1-3 2-2 1-4 2-6 2h-1v-1h0c1-1 2-2 3-2l1-2 3-3 3-1c1-1 3-3 4-5z" class="W"></path><path d="M195 576c1-1 2-1 4-2l1 1c-1 1-2 1-4 2 0 0-1 1-2 1v-1l1-1z" class="F"></path><path d="M194 574l3-1-2 3-1 1c-2 1-3 1-4 2l1-2 3-3z" class="P"></path><path d="M202 568l1 1 2-2c-1 2-1 3-2 4h0c0 1-1 2-2 3-1 0-1 0-1 1l-1-1 3-5v-1z" class="Y"></path><path d="M194 577v1c1 0 2-1 2-1 1 0 1 0 2 1h-1c-1 0-2 1-3 2-2 1-4 2-6 2h-1v-1h0c1-1 2-2 3-2 1-1 2-1 4-2z" class="E"></path><path d="M202 553c1 0 1 1 2 0 1 0 1-1 2-1l1 1 1 1v-2c1 0 1 1 2 1h1 7c-2 4-5 8-7 11l-2 2-5 5h-1 0c1-1 1-2 2-4l-2 2-1-1v1h-1l2-4h-1v-1c1-3 1-7 0-10v-1z" class="Y"></path><path d="M206 552l1 1c0 3 0 7-1 10h0v1 1h-1v-1h0v-4c1-2 1-4 1-6v-2z" class="W"></path><path d="M206 564s0 1 1 1h0c0-1 0-3 1-4 0-2-1-6 0-8l2 2h2c1 1 1 1 1 3h-1-1c-1 0-1 0-1-1h-1v5h0c-1 1-1 1-1 2l1 2-5 5h-1 0c1-1 1-2 2-4 0-1 0-2-1-3h1 0v1h1v-1z" class="F"></path><path d="M210 557v-2h1v2 1c-1 0-1 0-1-1z" class="z"></path><path d="M202 553c1 0 1 1 2 0 1 0 1-1 2-1v2c0 2 0 4-1 6v4h-1c1 1 1 2 1 3l-2 2-1-1v1h-1l2-4h-1v-1c1-3 1-7 0-10v-1z" class="g"></path><path d="M203 565v-1c0-1 1-1 1-2v-1c0-2-1-3 0-5h1v1 3 4h-1c1 1 1 2 1 3l-2 2-1-1v1h-1l2-4z" class="H"></path><path d="M204 564c1 1 1 2 1 3l-2 2-1-1 2-4z" class="U"></path><path d="M391 590s1-1 2-1l1 1c-2 2-5 4-8 6-1 1-4 2-5 3 0 0 0 1 1 1l-17 3c-1 1-2 1-3 1v1h8 0l-4 1h0c-1 0-2 1-3 1h2 8l-24 1h-3c-3 1-8 0-12 0v-1c-1-1-4-1-5-1l-1 2c1 1 2 1 3 2l-12-1h-3l-1 1c-1 0-2 1-3 1l-2 1-5 1 1 1-3 1h-1v-1h-4c-2 1-5 1-7 1h-24-3-3 0l-14-6v1c-2 0-3-1-5-1h0c-1 0-2-1-3-1l-1-1-3-1 2-2 1 1v-1h2-2l1-1-2-1c-2 0-5-1-8-2h1l-2-1c13-1 27 0 40-3v2h4c1-1 1 0 2 0h0 0c-1 0-1 1-2 1h0-3v1h12 4c0 1 0 2 1 3l5-1h4c4-1 19-3 22-5v-1c0 1 0 1 1 2 2-1 3-1 4-2v-1 1c0 1 0 1-1 2 2 0 5 1 7 0h-1 0 3c2-1 3-2 4-4v3h8c12 0 24-2 36-4h0 2 3 0-1c-1 1-1 1-2 1l-1 1c4-1 8-3 12-5z" class="o"></path><path d="M307 611c1-1 3-1 5-2v2l-2 1v-1h-3z" class="v"></path><path d="M315 608c1 0 1 1 1 1l-1 1c-1 0-2 1-3 1v-2l3-1z" class="Y"></path><path d="M303 612l4-1h3v1l-5 1h0v-1h-2z" class="w"></path><path d="M350 602l9 1h-1c-1 0-1 0-2 1h0c1 0 2 0 4 1h-6c-2 0-3 0-4-1l2-1h0l-2-1z" class="Z"></path><path d="M342 603c3-1 6-1 8-1l2 1h0l-2 1h0-7l2-1h-3z" class="E"></path><path d="M359 603c1 0 4-1 5 0h1c-1 1-2 1-3 1v1h-2c-2-1-3-1-4-1h0c1-1 1-1 2-1h1z" class="G"></path><path d="M330 603h12 3l-2 1h-7-2-3 5 0c1-1 1-1 2-1h-8 0z" class="B"></path><path d="M296 613l7-1h2v1h0l1 1-3 1h-1v-1h-4-2v-1z" class="AA"></path><path d="M305 613h0l1 1-3 1h-1v-1l3-1z" class="I"></path><path d="M364 603c6-1 11-2 17-4 0 0 0 1 1 1l-17 3h-1z" class="B"></path><path d="M319 604l-1-1h0c3-1 8-1 12 0h0 8c-1 0-1 0-2 1h0-5-4c-2 0-6-1-8 0z" class="U"></path><path d="M259 611c11 5 25 4 37 2v1h2c-2 1-5 1-7 1h-24c-3-1-6-2-8-4z" class="B"></path><path d="M319 604c2-1 6 0 8 0l1 2h0 1l-1 2c1 1 2 1 3 2l-12-1h-3s0-1-1-1c2-1 3-2 4-4h0z" class="G"></path><path d="M323 606h5 1l-1 2c-3-1-6-1-9 0 1-1 2-1 3-1h1v-1z" class="W"></path><path d="M319 604v2h4v1h-1c-1 0-2 0-3 1v1h-3s0-1-1-1c2-1 3-2 4-4z" class="AC"></path><path d="M319 604c2-1 6 0 8 0l1 2h0-5-4v-2h0z" class="a"></path><path d="M237 604l1 1 6 2c1 0 2 1 3 1h2c1 0 2 1 3 1h2c1 0 1 1 2 1s2 0 2 1h1c2 2 5 3 8 4h-3-3 0l-14-6v1c-2 0-3-1-5-1h0c-1 0-2-1-3-1l-1-1-3-1 2-2z" class="F"></path><path d="M238 607s1-1 2-1 1 0 1 1c1 0 3 1 4 1h0c1 0 2 1 2 1v1c-2 0-3-1-5-1h0c-1 0-2-1-3-1l-1-1z" class="E"></path><path d="M247 608h2c1 0 2 1 3 1h2c1 0 1 1 2 1s2 0 2 1h1c2 2 5 3 8 4h-3-3 0 0c-1-1-4-2-5-3-3-1-6-3-9-4z" class="M"></path><defs><linearGradient id="J" x1="344.349" y1="608.036" x2="344.603" y2="605.135" xlink:href="#B"><stop offset="0" stop-color="#a2a09d"></stop><stop offset="1" stop-color="#c0bfc1"></stop></linearGradient></defs><path fill="url(#J)" d="M343 604h7 0c1 1 2 1 4 1h6 2 8 0l-4 1h0c-1 0-2 1-3 1h2 8l-24 1h-3c-3 1-8 0-12 0v-1c-1-1-4-1-5-1h-1 0l-1-2h4 3 2 7z"></path><path d="M361 606h5 0c-1 0-2 1-3 1h2-6 0 2v-1z" class="Q"></path><path d="M360 605h2 8 0l-4 1h-5-9-4 0l6-1h6z" class="g"></path><path d="M343 604h7 0c1 1 2 1 4 1l-6 1h0 4c-4 1-10 1-14 0-1 0-1-1-2-1 0 0-1 0-2-1h2 7z" class="s"></path><path d="M343 604h7 0c1 1 2 1 4 1l-6 1h0-2l1-1h0c-4-1-7-1-11-1h7z" class="k"></path><path d="M378 593h0 2 3 0-1c-1 1-1 1-2 1l-1 1c-4 1-7 3-11 3-7 2-15 2-22 2h-16-13c-3 0-6 2-8 3-3 1-6 1-8 2-15 3-31 5-47 2-5-1-10-2-15-4l-2-1c-2 0-5-1-8-2h1l-2-1c13-1 27 0 40-3v2h4c1-1 1 0 2 0h0 0c-1 0-1 1-2 1h0-3v1h12 4c0 1 0 2 1 3l5-1h4c4-1 19-3 22-5v-1c0 1 0 1 1 2 2-1 3-1 4-2v-1 1c0 1 0 1-1 2 2 0 5 1 7 0h-1 0 3c2-1 3-2 4-4v3h8c12 0 24-2 36-4z"></path><path d="M228 599c13-1 27 0 40-3v2h4c1-1 1 0 2 0h0 0c-1 0-1 1-2 1h0-3v1h12 4c0 1 0 2 1 3-8 1-16 1-25 0l-20-2h-3l-1 1c-2 0-5-1-8-2h1l-2-1z" class="X"></path><path d="M230 600c3 0 7 0 11 1h-3l-1 1c-2 0-5-1-8-2h1z" class="AB"></path><path d="M162 611l-6 2 6 3c3-3 8-6 12-8h0l1 1-11 6c2 1 3 2 5 3h0 0c1 1 1 2 1 3s1 1 1 2c1-1 1-1 1-2h1c1-1 1-1 3 0 1 0 1 1 2 2-1 1-2 2-3 4l1 1-1 2h0c1 2 1 3 2 4 0 1 1 2 2 3-1 2-3 3-5 4 1 1 0 2 1 2l3 1c9 0 17 1 25 6 6 4 10 10 11 16s-1 9-4 13h-1c-5-4-4-9-5-15-1-2-4-5-6-7-9-10-23-6-35-6-13 0-25-4-35-13-2-3-3-5-6-8l-2-6c-1-1-2-2-3-2l-1-1c1-1 1-3 1-4l1-1v-1l1 1h0c1-1 1-2 1-2 0-1 1-1 1-2 1 2 2 3 3 5l1-1c1 1 1 2 2 3 1-1 2-1 3-1 3 1 5 1 7 1 5 1 9 1 14-2v2c1-1 4-3 4-4 1 0 1-1 0-1s-2 0-3 1l-1-1 9-3 2-1v1z" class="u"></path><path d="M121 622l2 1c1 2 2 3 3 4l-1 1-1-1-1 1c-1-2-1-4-2-6z" class="Q"></path><path d="M118 615l1 1 1 8c-1-1-2-2-3-2l-1-1c1-1 1-3 1-4l1-1v-1z" class="e"></path><path d="M120 617v-2l1 1 2 2c0 1 1 2 2 3v1h-2v1l-2-1v-1l-1-4z" class="N"></path><path d="M120 617v-2l1 1 2 2h-2v3l-1-4z" class="X"></path><path d="M166 640c1 0 1 1 1 1 1 1 1 1 2 1h1c1-1 2-1 3-1h1c1 1 0 2 1 2l3 1h-13s0-1 1-1v-1c-1 0-1 0-2 1l-1-1v-1c1-1 2-1 3-1z" class="a"></path><path d="M153 636h0v-2c1 0 3 2 5 3 0 1 1 2 2 2l1 1 1-1h1c1 0 1 0 2 1h1c-1 0-2 0-3 1v1l1 1c1-1 1-1 2-1v1c-1 0-1 1-1 1-3 0-5 0-7-1l-5-1v-4-2z" class="Q"></path><path d="M153 638c1 1 1 2 2 2l1 1s1 0 1 1h1v1l-5-1v-4z" class="AD"></path><path d="M125 621l4 3c1 0 2 0 3 1 2 1 4 1 6 1l4 1-1 1 1 1h-2c0 1-1 1-2 1 0 1-1 2-2 2h-1s0 1-1 2h0c-3-1-7-5-8-7-1-1-2-2-3-4v-1h2v-1z" class="q"></path><path d="M125 621l4 3c2 3 6 4 9 6 0 1-1 2-2 2l1-1c-1-1-4-1-5-2-3-1-5-4-7-7v-1z" class="I"></path><path d="M129 624c1 0 2 0 3 1 2 1 4 1 6 1l4 1-1 1 1 1h-2c0 1-1 1-2 1-3-2-7-3-9-6z" class="O"></path><path d="M138 630c1 0 2 0 2-1l2 3h2c1-1 6 0 7 1s1 2 2 3v2 4c-5-2-13-4-16-8v-2h-2 1c1 0 2-1 2-2z" class="q"></path><path d="M138 630c1 0 2 0 2-1l2 3c-2 0-3 0-5 1v1-2h-2 1c1 0 2-1 2-2z" class="L"></path><path d="M119 616c1-1 1-2 1-2 0-1 1-1 1-2 1 2 2 3 3 5l1-1c1 1 1 2 2 3 1-1 2-1 3-1 3 1 5 1 7 1 5 1 9 1 14-2v2l-3 1h1l5-2h1c0 2 0 3-1 5h-1c-1 2-2 2-4 4l-1 1h0-2-2l-2-1-4-1c-2 0-4 0-6-1-1-1-2-1-3-1l-4-3c-1-1-2-2-2-3l-2-2-1-1v2l-1-1z" class="C"></path><path d="M119 616c1-1 1-2 1-2 0-1 1-1 1-2 1 2 2 3 3 5 2 2 4 4 7 5 1 0 2 0 3 1h-1c1 1 2 1 3 2h1l1 1c-2 0-4 0-6-1-1-1-2-1-3-1l-4-3c-1-1-2-2-2-3l-2-2-1-1v2l-1-1z" class="E"></path><path d="M119 616c1-1 1-2 1-2 0-1 1-1 1-2 1 2 2 3 3 5 2 2 4 4 7 5 1 0 2 0 3 1h-1c-1 0-2-1-3-1-2-1-4-1-5-3l-3-3h-1l-1-1v2l-1-1z" class="P"></path><path d="M151 617v2l-3 1c-1 0-3 1-4 2-6 1-12-1-17-3 1-1 2-1 3-1 3 1 5 1 7 1 5 1 9 1 14-2z" class="M"></path><path d="M154 618h1c0 2 0 3-1 5h-1c-1 2-2 2-4 4l-1 1h0-2-2l-2-1-4-1-1-1h3v-1c3 1 7-2 9-4l5-2z" class="n"></path><path d="M145 626l4 1-1 1h0-2-2c1-1 1-1 1-2z" class="j"></path><path d="M140 625l5 1c0 1 0 1-1 2l-2-1-4-1-1-1h3z" class="u"></path><path d="M123 628l1-1 1 1c9 10 25 15 37 17 5 1 10 0 15 0s10 0 15 1l5 2c4 1 7 4 10 7s7 10 6 15c0 3-2 5-4 7h0c-3-2-3-9-4-13-4-10-13-15-24-15-6 0-13 1-19 1-7 0-13-2-19-4-7-2-13-8-17-14-1-1-2-2-3-4z" class="n"></path><path d="M205 657h1c0 1 0 1 1 2l1 1v1s0 1 1 1v1 1c1 2 1 4 1 6h-1c-1-1 0-3-2-4 0-1 0-2-1-3 0-1 1-1 1-2-1 0-1-1-2-2v-2z" class="q"></path><path d="M126 632c1 1 1 1 2 1 2 1 3 3 5 4l1 1h1c1 2 3 3 5 3 1 1 3 2 4 3h-1c-1 0-2 0-3-1l-6-3h0c1 1 3 3 5 4 2 0 3 1 4 2-7-2-13-8-17-14zm36 13c5 1 10 0 15 0s10 0 15 1l5 2-1 1h0l1 1h0l-2-1h-2c-1-1-1-1-2-1h-2c-5-2-11-1-16-1-2 1-4 0-5 1h-8c-1-1-2 0-3-1h-3c1-1 4-1 6-1 1 0 2 1 3 0h1 5-6l-1-1z" class="a"></path><path d="M192 646l5 2-1 1h0s-1-1-2-1c-2 0-3-1-4-1l-1-1h1 2zm-69-18l1-1 1 1c9 10 25 15 37 17l1 1h6-5-1c-1 1-2 0-3 0-2 0-5 0-6 1h-2v-1h-1-1-2c-1-1-2-1-3-1l-1-1c-1-1-3-2-4-3-2 0-4-1-5-3h-1l-1-1c-2-1-3-3-5-4-1 0-1 0-2-1s-2-2-3-4z" class="N"></path><path d="M162 611l-6 2 6 3c3-3 8-6 12-8h0l1 1-11 6c2 1 3 2 5 3h0 0c1 1 1 2 1 3s1 1 1 2c1-1 1-1 1-2h1c1-1 1-1 3 0 1 0 1 1 2 2-1 1-2 2-3 4l1 1-1 2h0c1 2 1 3 2 4 0 1 1 2 2 3-1 2-3 3-5 4h-1c-1 0-2 0-3 1h-1c-1 0-1 0-2-1 0 0 0-1-1-1h-1c-1-1-1-1-2-1h-1l-1 1-1-1c-1 0-2-1-2-2-2-1-4-3-5-3v2h0c-1-1-1-2-2-3s-6-2-7-1h-2l-2-3h2l-1-1 1-1 2 1h2 2 0l1-1c2-2 3-2 4-4h1c1-2 1-3 1-5h-1l-5 2h-1l3-1c1-1 4-3 4-4 1 0 1-1 0-1s-2 0-3 1l-1-1 9-3 2-1v1z" class="z"></path><path d="M149 627h1v1h-2l1-1z" class="i"></path><path d="M154 626h0l-1 2h-2l-1-1c1-1 2-1 3-1h1z" class="e"></path><path d="M175 627l1 1-1 2v-1h-1v3h-1c-1 0-1 0-1-1 1-2 2-3 3-4z" class="Q"></path><path d="M169 618h0c1 1 1 2 1 3s1 1 1 2c1-1 1-1 1-2h1c0 1 1 2 0 4h0l-3-2-3-1c0-1 1-1 1-2h1v-2z" class="N"></path><path d="M169 620l1 3-3-1c0-1 1-1 1-2h1z" class="X"></path><path d="M170 635c0-1-1-2-2-3h0 1c2 0 3 1 4 2v1h1v2c-1 0-3-1-4-2z" class="M"></path><path d="M167 623c1 0 2 1 3 2h0c-1 0-2 0-3 1h2c0 1-3 1-4 2h-1c-1-1-1-1-2-1 1 0 2-1 2-2 1-1 2-2 3-2z" class="e"></path><path d="M166 630c1-2 6-4 8-5l-3 6h-2l-3 1s0-1-1-1l1-1z" class="M"></path><path d="M158 622l1 1v1h1v1s0 1 1 1c0 0 1 0 1 1 1 0 1 0 2 1h-11l1-2 2-1 2-3z" class="X"></path><path d="M158 622l1 1v1h1v1h-1v1h-2l-1-1 2-3z" class="M"></path><path d="M158 622c1-1 2-2 2-4h0 1c1 1 2 2 3 2v1l1-1 2 2v1c-1 0-2 1-3 2 0 1-1 2-2 2 0-1-1-1-1-1-1 0-1-1-1-1v-1h-1v-1l-1-1z" class="Q"></path><path d="M158 622c1-1 2-2 2-4h0 1c1 1 2 2 3 2h-2l-1 1v1h1 1c-1 1-2 1-3 2h0-1v-1l-1-1z" class="I"></path><path d="M146 628h2l5 1 13 1-1 1h0l-1 1v2c-1 0-3 1-4 2-3 0-5-3-8-4-2-1-5-1-7-2l1-2z" class="M"></path><path d="M153 629l13 1-1 1h0l-1 1c-1 0-2 0-3 1-1 0-1 0-2 1h-1c-2 0-2-1-3-2 1 0 2 0 2-2-1 0-1 1-2 1h-1l-1-2z" class="p"></path><path d="M162 611l-6 2 6 3c3-3 8-6 12-8h0l1 1-11 6c2 1 3 2 5 3h0v2h-1c0 1-1 1-1 2h0l-2-2-1 1v-1c-1 0-2-1-3-2h-1 0c0 2-1 3-2 4l-2 3-2 1h0-1c1-2 2-5 3-7 0 0 0-1-1-1h0-1l-5 2h-1l3-1c1-1 4-3 4-4 1 0 1-1 0-1s-2 0-3 1l-1-1 9-3 2-1v1z" class="F"></path><path d="M159 617v3l-1 1c-1 0-1-1-2-2h2l1-2z" class="K"></path><path d="M166 619h2v1c0 1-1 1-1 2h0l-2-2s0-1 1-1z" class="M"></path><path d="M166 619h-1c0-1-1-1-2 0l-2-2c1 0 1 0 2-1 2 0 2 1 4 1 0 1 1 2 1 2h-2z" class="V"></path><path d="M154 618l3-3 2 2-1 2h-2 0s0-1-1-1h0-1z" class="AD"></path><path d="M156 619h0c1 1 1 2 2 2-1 2-2 3-4 5h-1c1-2 2-5 3-7z" class="p"></path><path d="M142 627l2 1h2l-1 2c2 1 5 1 7 2 3 1 5 4 8 4 1-1 3-2 4-2h0c2-1 2-1 3-1l2 2c0 1 0 1 1 1h0v-1c1 1 3 2 4 2v-2h-1v-1-1-1h1v-3h1v1h0c1 2 1 3 2 4 0 1 1 2 2 3-1 2-3 3-5 4h-1c-1 0-2 0-3 1h-1c-1 0-1 0-2-1 0 0 0-1-1-1h-1c-1-1-1-1-2-1h-1l-1 1-1-1c-1 0-2-1-2-2-2-1-4-3-5-3v2h0c-1-1-1-2-2-3s-6-2-7-1h-2l-2-3h2l-1-1 1-1z" class="W"></path><path d="M142 627l2 1h2l-1 2-3-1-1-1 1-1z" class="I"></path><path d="M170 642l1-1c0-1 0-2-1-3v-1h0c2 0 3 1 4 2l-1 1v-1h-1l1 2c-1 0-2 0-3 1z" class="p"></path><path d="M164 634c2-1 2-1 3-1l2 2c0 1 0 1-1 2s-1 0-3 0c0-1-1-2-1-3z" class="N"></path><path d="M173 632h1v-3h1v1h0l-1 1 1 1v2s1 0 1 1 1 1 1 2-1 1-1 2h-1l1-2-2-2h0-1v-1-1-1zm-15 5h3 1l1-2h0l3 3h3l1 1c0 1-1 2-1 3-1 0-1 0-2-1 0 0 0-1-1-1h-1c-1-1-1-1-2-1h-1l-1 1-1-1c-1 0-2-1-2-2z" class="I"></path><path d="M259 525h2 1v2h1 0l1-1-2 7v1l-1-1-3 11-1 1c1 1 1 2 3 2l2-1h0v1c-2 1-3 3-4 4v1c0 2-1 4-2 6l-3 3c1 1 1 1 1 2l1 1c0 1 0 1 1 2h0c1-1 1-1 0-2l1-1c1 1 3 0 4 0s2 0 3 1h0l-1-2h0c0-1 1-1 2-1l3 3c0 1 0 2-1 3-3 0-6 1-9 3h2-1v1l2 1-5 1v1c4-2 8-3 12-4l8-2h0l-8 5-3 1-11 4c-1 0-3 1-4 2l-9 2-7 2h-2l-6 1h-8l-3 1h0-1l-2 1h0-4-1-1v2h-1-1v-1h-2-3-1v-1c-3 1-6 1-9 2h1l-1 1c-2 0-5 1-8 1h0l-4 1h-2-2l-2 1-4 1-5 1-1-2c-2 0-4 1-5 2-4 0-7 0-11 1-2 0-4 0-6-1-1 0-2-1-3-1l2-4c3 0 7-1 10-1 2 0 3-1 4-1 2 1 3 0 5 0 1-1 3 0 5-1 2 0 3 1 4 0h6l3-1 8-2 5-2c2 0 4-1 6-2 1-1 2-2 3-2h1l3-1c0-1 2-2 3-2 2-3 6-6 9-6l-1 1h2 1c1-2 5-5 7-6 4-4 8-7 12-10 1 0 2-1 3-1l2-3s1-2 2-3c3-3 6-7 8-11l3-9h2c1 0 2 0 3-1 1 0 2 0 2-1z" class="n"></path><path d="M173 592c1-1 1-1 3-2v1h0c1 0 1 0 1 1h-2-2z" class="e"></path><path d="M176 590c1 0 3-1 5-1 1 0 2 0 2 1h-2v1h0l-4 1c0-1 0-1-1-1h0v-1z" class="Q"></path><path d="M181 589l6-1h0l2 1h1l-1 1c-2 0-5 1-8 1v-1h2c0-1-1-1-2-1z" class="I"></path><path d="M202 584c2-1 5-2 7-3l23-9c0 1 1 1 2 1l-22 9-9 2h-1z" class="B"></path><path d="M262 546h0v1c-2 1-3 3-4 4v1c-3 2-5 5-8 7-3 3-7 4-11 6-5 3-9 6-14 8h-2v-1c1-2 4-3 6-4l9-6h-1s1-1 2-1c2-1 4-2 6-4 1-1 1-1 3-1 3-2 4-4 6-6l3-5c1 1 1 2 3 2l2-1z" class="R"></path><path d="M257 545c1 1 1 2 3 2-2 1-3 2-4 3h-2l3-5z" class="T"></path><path d="M245 557c1-1 1-1 3-1-1 1-3 2-3 4-2 1-4 2-6 4-5 3-10 6-14 8h-1l1 1h-2v-1c1-2 4-3 6-4l9-6h-1s1-1 2-1c2-1 4-2 6-4z" class="d"></path><path d="M237 562h1l-9 6c-2 1-5 2-6 4v1c-2 1-4 2-6 2-7 3-14 6-21 8l-1-1v-2h-1c1-1 2-2 3-2h1l3-1c0-1 2-2 3-2 2-3 6-6 9-6l-1 1h2 1v1c1 0 2-1 2-1h1l-2 2c-1 0-1 0-1 1l-2 1c2 0 2-1 3 0h1c7-3 13-9 20-12z" class="l"></path><g class="M"><path d="M201 577l1 1-3 2-2-2h1l3-1z"></path><path d="M205 579l-5 1c0 1 0 1-1 0 1-1 3-1 4-2l2 1z"></path></g><path d="M204 575h2v1l-4 2h0l-1-1c0-1 2-2 3-2z" class="Q"></path><path d="M194 580c1-1 2-2 3-2l2 2c-1 0-3 1-4 2v-2h-1zm10-5c2-3 6-6 9-6l-1 1h2 1v1c1 0 2-1 2-1h1l-2 2c-1 0-1 0-1 1l-2 1c2 0 2-1 3 0h1l-12 5-2-1 5-3h-1l-1 1v-1h-2z" class="e"></path><path d="M204 575c2-3 6-6 9-6l-1 1-4 3 1 1c1 0 2-1 3-2v1c-1 1-3 2-4 2h-1l-1 1v-1h-2z" class="O"></path><path d="M234 554c1 0 2-1 3-1v1h0c-3 2-4 5-7 7v1h0c2-1 3-2 5-2 1-1 2-2 3-2v-1h3 4c-2 2-4 3-6 4-1 0-2 1-2 1-7 3-13 9-20 12h-1c-1-1-1 0-3 0l2-1c0-1 0-1 1-1l2-2h-1s-1 1-2 1v-1c1-2 5-5 7-6 4-4 8-7 12-10z" class="N"></path><path d="M259 525h2 1v2h1 0l1-1-2 7v1l-1-1-3 11-1 1-3 5c-2 2-3 4-6 6-2 0-2 0-3 1h-4-3v1c-1 0-2 1-3 2-2 0-3 1-5 2h0v-1c3-2 4-5 7-7h0v-1l2-3s1-2 2-3c3-3 6-7 8-11l3-9h2c1 0 2 0 3-1 1 0 2 0 2-1z" class="n"></path><path d="M259 525h2 1v2h1 0l1-1-2 7v1l-1-1 1-4-2-2h-1c-1 0-1 0-2-1 1 0 2 0 2-1z" class="j"></path><path d="M259 525h2 1v2h-1-1-1c-1 0-1 0-2-1 1 0 2 0 2-1z" class="Y"></path><path d="M254 527c1 0 2 0 3-1 1 1 1 1 2 1-2 0-3 1-5 2-2 2-3 5-4 7-3 5-7 11-11 14h0s1-2 2-3c3-3 6-7 8-11l3-9h2z" class="E"></path><path d="M237 554l4-2 3-3 6-6h0c0 1-1 2-1 2-1 1-4 3-4 5l-1 1h1c1-1 1 0 2-1 1 0 1 0 2-1l1-1h1c0 1-2 2-2 2l-1 1c-1 0-2 1-3 2-1 0-4 2-4 3v1h-3v1c-1 0-2 1-3 2-2 0-3 1-5 2h0v-1c3-2 4-5 7-7z" class="e"></path><path d="M263 562c0-1 1-1 2-1l3 3c0 1 0 2-1 3-3 0-6 1-9 3h2-1v1l2 1-5 1v1c4-2 8-3 12-4l8-2h0l-8 5-3 1-11 4c-1 0-3 1-4 2l-9 2-7 2h-2l-6 1h-8l-3 1h0-1l-2 1h0-4-1-1v2h-1-1v-1h-2-3-1v-1c-3 1-6 1-9 2l-2-1h0c5-1 11-2 15-4h1l9-2 22-9c-1 0-2 0-2-1 3-2 6-3 9-5l12-6c1 1 1 1 1 2l1 1c0 1 0 1 1 2h0c1-1 1-1 0-2l1-1c1 1 3 0 4 0s2 0 3 1h0l-1-2h0z" class="Y"></path><path d="M214 585l2-1 2 1-3 1-1-1z" class="e"></path><path d="M207 586l7-1 1 1h0-1l-2 1h0-4 0l-1-1z" class="p"></path><path d="M212 582h-1c-1 1-2 1-3 2h4c-2 0-7 2-9 1v-1l9-2z" class="M"></path><path d="M204 586h3l1 1h0-1-1v2h-1-1v-1h-2-3-1v-1l6-1z" class="K"></path><path d="M204 588l2-1v2h-1-1v-1z" class="Y"></path><path d="M204 586h3l1 1h0-1-3v-1z" class="f"></path><path d="M241 576l3-1v2l-4 1-16 4v-1h1c4-3 11-4 16-5z" class="P"></path><path d="M234 573l7-3c0 1 0 1 1 2 0 1 0 1-1 2v2c-5 1-12 2-16 5l-1-1c-4 2-8 4-12 4h-4c1-1 2-1 3-2h1l22-9z" class="o"></path><path d="M242 572c0 1 0 1-1 2v2c-5 1-12 2-16 5l-1-1c3-1 8-3 12-4 2-1 4-3 6-4z" class="B"></path><path d="M276 568h0l-8 5-3 1-11 4c-1 0-3 1-4 2l-9 2-7 2h-2l-6 1h-8l-2-1c3-1 6-1 10-2l18-5h0v-2l5-1 4-1v1c1 0 2-1 3-1v1c4-2 8-3 12-4l8-2z" class="n"></path><path d="M216 584c3-1 6-1 10-2l-1 1c-1 0-1 1-2 1h-3c1 1 3 0 4 0l2 1h-8l-2-1z" class="o"></path><path d="M276 568h0l-8 5-3 1 1-1h1c1-1 2-1 3-2h1 0v-1h-3l8-2z" class="M"></path><path d="M249 574l4-1v1c1 0 2-1 3-1v1c-3 2-8 3-12 3h0v-2l5-1z" class="V"></path><defs><linearGradient id="K" x1="237.962" y1="575.76" x2="247.843" y2="559.132" xlink:href="#B"><stop offset="0" stop-color="#2c2f30"></stop><stop offset="1" stop-color="#565252"></stop></linearGradient></defs><path fill="url(#K)" d="M263 562c0-1 1-1 2-1l3 3c0 1 0 2-1 3-3 0-6 1-9 3h2-1v1l2 1-5 1c-1 0-2 1-3 1v-1l-4 1-5 1-3 1v-2c1-1 1-1 1-2-1-1-1-1-1-2l-7 3c-1 0-2 0-2-1 3-2 6-3 9-5l12-6c1 1 1 1 1 2l1 1c0 1 0 1 1 2h0c1-1 1-1 0-2l1-1c1 1 3 0 4 0s2 0 3 1h0l-1-2h0z"></path><path d="M263 562c0-1 1-1 2-1l3 3c0 1 0 2-1 3-3 0-6 1-9 3h-3v-1c4 0 7-2 11-3v-1h1v-1c-1 0-2-1-3-2h-1 0z" class="Z"></path><path d="M248 567l3-1c2 1 3 2 4 3v1l-5 1-3 1c0-1 0-1 1-2v-3z" class="o"></path><path d="M255 570h3 2-1v1l2 1-5 1c-1 0-2 1-3 1v-1l-4 1 2-2-1-1 5-1z" class="R"></path><path d="M253 573l6-3v1l2 1-5 1c-1 0-2 1-3 1v-1z" class="Q"></path><path d="M263 562h1c1 1 2 2 3 2v1h-1-1c-2 1-6 2-8 3-1 0-1 0-2-1-1 0-2-1-2-2l1-2 1 1c0 1 0 1 1 2h0c1-1 1-1 0-2l1-1c1 1 3 0 4 0s2 0 3 1h0l-1-2z" class="M"></path><path d="M241 570c2-1 5-2 7-3v3c-1 1-1 1-1 2l3-1 1 1-2 2-5 1-3 1v-2c1-1 1-1 1-2-1-1-1-1-1-2z" class="e"></path><path d="M247 572l3-1 1 1-2 2-5 1-3 1v-2h1c1 0 2 0 2-1 1 0 2 0 3-1z" class="F"></path><path d="M145 91c1 0 2-1 4 0 4 1 7 3 10 6l1-1 3 2 14 13 7 8h0v2c2 2 3 5 4 8 1 2 1 4 2 6v3 5c-1-1-1 0-1-1s0-1-1-2l-1 1 1 2c0 1 0 2 1 3v4s0 1 1 2l-1 2h-1v-1c0 1-1 2-1 3h0v5l-4 2h-1-2 0c0-1 1-1 1-2h-2c-1-1-2-1-4-1v-1h-3c-1-1-1-1-2-1h0c-1 0-1 0-2-1v-1c-2 0-3 1-4 1v-1h1l1-1h0l-2-2v-1h0-2v-1h-1l-1-1v-1c-1 1-1 1-2 1 0-1 0-1-1-2v1h-3c0-1 0-1-1-2v2h0l-1-1h-1-1 0c-2-2-3-1-4-1l1-1h-1c0-2 1-2 1-3l-2-1c1 0 1 0 1-1v-4-7-2-4c-1-4-3-8-4-13h1 1v-1c-2-6-3-13 1-19z" class="AA"></path><path d="M180 138l1 1h-1v3l-1-2v-1c1 0 1 0 1-1z" class="g"></path><path d="M148 134c2 1 2 2 3 4-1 0-1-1-2 0-1-1-1-3-1-4z" class="U"></path><path d="M187 156c-1 0-2-1-3-2s-2-3-2-5h1v1c1 2 3 4 4 6z" class="g"></path><path d="M183 149l1 1s1 0 1 1h1c1 0 1 1 1 1l1 1c0 1-1 2-1 3h0 0c-1-2-3-4-4-6v-1z" class="D"></path><path d="M155 113l1 1v1 3h-1s-1-1-2-1c-1-1-3-2-4-3 1 0 3 1 4 1v-1h0c1 0 2 0 2-1z" class="AD"></path><path d="M147 125c1 2 1 6 1 8v4h-2v-7-2l1 2v-3-2z" class="b"></path><path d="M176 143c1 1 2 1 2 1v2c0 1 0 1 1 2 0 0-1 1-1 2 1 0 1 1 1 1h-2v1l-1-1v-2h1c0-1 0-2-1-3l-1-1 1-2z" class="U"></path><path d="M185 148c0-1-1-2 0-3v-6h1l1 1v1l1 2c-1 1-1 1-1 2v1 1l-2-1v2z" class="G"></path><path d="M187 145c-1-1-1-3-1-4l1-1v1l1 2c-1 1-1 1-1 2z" class="I"></path><path d="M182 120h1l1 1c2 2 3 5 4 8h-1l-1-2h0c0 1 0 1 1 2h0l-1 1c-1-4-2-6-4-10z" class="Q"></path><path d="M170 120c0-1 0-1 1-1l1-1h1v2s0 1 1 1c1-1 1-1 2 0h-1l-1 1h-2l-1-1c-1 0-1 0-2 1h-1 0-2c0 1-1 1-1 0 1-1 3-1 5-2h0z" class="g"></path><path d="M171 121h1 3l-1 1h-2l-1-1z" class="w"></path><path d="M152 131c1 1 2 1 3 3h-1l1 1h0c0 1-1 1-1 1v1c1 0 2 1 2 1 0 1 1 1 1 1 1 1 0 2 0 3l-4-4c-1-1-1-2-1-3l1-1c-1-1-1-1-1-3z" class="v"></path><path d="M144 111c0 2 0 4 1 6 1 3 2 5 2 8v2 3l-1-2v-4c-1-4-3-8-4-13h1 1z" class="K"></path><path d="M186 130l1-1h0c-1-1-1-1-1-2h0l1 2h1c1 2 1 4 2 6v3 5c-1-1-1 0-1-1s0-1-1-2c-1-3-1-7-2-10z" class="I"></path><path d="M187 145c0-1 0-1 1-2 0 1 0 2 1 3v4s0 1 1 2l-1 2h-1v-1l-1-1s0-1-1-1l-1-3v-2l2 1v-1-1z" class="Q"></path><path d="M187 146v2 1c1 1 1 2 0 3 0 0 0-1-1-1l-1-3v-2l2 1v-1z" class="I"></path><path d="M174 130c1-1 1-1 3-1 0 0 1-1 2-1v1c-1 0-1 1-2 1v1l1 1 2-1h0 0l-1 2c1 1 2 0 3 1h-1-1v1c-1 0-2 0-2-1v-1-1c-1 1-1 1-2 1l-1 1h-3v-1h1v-1h-3 0 0c1 0 3-1 3-2h1z" class="g"></path><path d="M173 141c1 0 2 1 2 1l1 1-1 2 1 1c1 1 1 2 1 3h-1v2h-1c-1-1-1-1-3-1 0-1-1-2-2-3h2 1 0c0-1-1-2-1-3h0 1 1-1v-3z" class="W"></path><path d="M176 146c1 1 1 2 1 3h-1v2h-1c-1-1-1-1-3-1 0-1-1-2-2-3h2 1c0 1 0 2 1 2h1v-1s1-1 1-2z" class="AC"></path><path d="M160 112h3v1h-1v1c1 0 3 0 4 1-1 0-2 0-3 1 3 0 4 0 6-1-1 1-2 2-4 2v1h4 0c-1 0-2 1-3 1s-2 0-3 1h-1c0-1 0-1-1-2l-1-1v-1h1v-1h-1c0-1 0-1 1-2h0s0-1-1-1z" class="I"></path><path d="M159 117h1l1 1c1 1 1 1 1 2h1c2 1 4 0 7 0h0c-2 1-4 1-5 2 0 1 1 1 1 0h2v1s-1 1-2 1c-1-1-2-1-3-1h-2c0-1 0-2-1-3h-2c-1 0-2 0-3-1h0v-1h1l1 1v-2h2z" class="Q"></path><path d="M159 117h1l1 1c-1 1-2 1-3 1h-1v-2h2z" class="N"></path><path d="M155 108h1c1 0 2 1 2 2l1 1h0s1 0 1 1c1 0 1 1 1 1h0c-1 1-1 1-1 2h1v1h-1v1h-1-2v2l-1-1v-3-1l-1-1h-1v-1h1v-1c-1 0-1 0-1-1h1v-2z" class="X"></path><path d="M159 117h-1v-1h2v1h-1z" class="p"></path><path d="M155 108h1c1 0 2 1 2 2l1 1h0s1 0 1 1c1 0 1 1 1 1h0-1c-1 0-2-1-2-1 0-1-1-2-1-3l-1 1h-1v1c-1 0-1 0-1-1h1v-2z" class="e"></path><path d="M175 134l1-1c1 0 1 0 2-1v1 1c0 1 1 1 2 1l1 1h0-1-2v1l1 1h1c0 1 0 1-1 1v1l1 2 2 2h-1c-1-2-3-3-5-3l-1 1s-1-1-2-1v-1c-1 0-2 0-2-1h2c1 0 2 0 3-1v-1l-2-2h1v-1z" class="Y"></path><path d="M179 138h1c0 1 0 1-1 1v1c-1 0-1-1-1-2h1 0z" class="w"></path><path d="M176 137h1c0 1 0 1-1 2l1 1s-1 0-1 1l-1 1s-1-1-2-1v-1c-1 0-2 0-2-1h2c1 0 2 0 3-1v-1z" class="B"></path><path d="M160 96l3 2 14 13 7 8h0v2l-1-1h-1c-3-4-7-9-11-13l-12-10 1-1z" class="V"></path><path d="M155 135l2 2c0 1 1 1 2 2h2l3 1c0 1 1 2 2 3l-2 1c1 0 1 1 2 1 0 1 0 2 1 3h-1l-1 1c-2 0-2 0-2-2-2-2-4-3-6-5 0-1 1-2 0-3 0 0-1 0-1-1 0 0-1-1-2-1v-1s1 0 1-1h0z" class="F"></path><path d="M163 144l2 3v1c-2-1-3-2-3-3l1-1z" class="v"></path><path d="M161 139l3 1c0 1 1 2 2 3l-2 1-1-1-2-2v-2z" class="c"></path><path d="M162 145c-2-1-3-2-3-4h0 2l2 2v1l-1 1z" class="Y"></path><path d="M171 121l1 1h2l2 1h0c0 1-1 1-2 1v1h1v1c-1 1-1 1-3 1h0c-1-1-4 0-6 0v-1h-2-1l2 2-3-1h-1-1c0 1 1 1 1 2l-1 1v-1c0-1-1-1-1-1v-1l1-1h-1l1-1h0 1l2-2c1 0 2 0 3 1 1 0 2-1 2-1v-1h0 1c1-1 1-1 2-1z" class="F"></path><path d="M172 122h2l2 1h-5l1-1z" class="g"></path><path d="M163 123c1 0 2 0 3 1h-2v1c2 0 3 0 5-1h1c-1 1-2 2-4 2h-2-1l2 2-3-1h-1-1c0 1 1 1 1 2l-1 1v-1c0-1-1-1-1-1v-1l1-1h-1l1-1h0 1l2-2z" class="o"></path><path d="M160 125l1 1h0-1-1l1-1z" class="M"></path><path d="M162 136c1 1 1 2 2 2s2-1 3-1h0c1 1 3 1 4 1v1h2-2c0 1 1 1 2 1v1 3h1-1-1 0c0 1 1 2 1 3h0-1l-2-2-4-2c-1-1-2-2-2-3l-3-1h-2v-2l3 1v-2z" class="b"></path><path d="M164 140c1 1 2 1 3 1 1 2 2 3 3 4l-4-2c-1-1-2-2-2-3z" class="O"></path><path d="M162 136c1 1 1 2 2 2s2-1 3-1h0c1 1 3 1 4 1v1h-3l1 1c0 1-1 1-2 1s-2 0-3-1l-3-1h-2v-2l3 1v-2z" class="P"></path><path d="M162 136c1 1 1 2 2 2s2-1 3-1h0c1 1 3 1 4 1v1h-3c-1 0-3 1-3 1 0-1-1-1-1-2h-2v-2z" class="e"></path><path d="M148 133v1c0 1 0 3 1 4v1c1 1 3 2 3 3h0v1h2 0c1 1 1 1 3 2h0c1 2 2 3 3 4s2 0 3 2c0 0 0 1 1 1h-2v-1h-1l-1-1v-1c-1 1-1 1-2 1 0-1 0-1-1-2v1h-3c0-1 0-1-1-2v2h0l-1-1h-1-1 0c-2-2-3-1-4-1l1-1h-1c0-2 1-2 1-3l-2-1c1 0 1 0 1-1v-4h2v-4z" class="W"></path><path d="M150 143l-1 1h-1l-1-2c1-1 1 0 2 0l1 1z" class="F"></path><path d="M147 146l1-1h1l1 2 1 1h-1 0c-2-2-3-1-4-1l1-1z" class="b"></path><path d="M146 137h2c0 1 1 3 0 4h-2v-4z" class="L"></path><path d="M152 143h2 0c1 1 1 1 3 2h0c-1 0-1 0-1 1h-2c-1-1-1-2-2-3zm-3-4c1 1 3 2 3 3h0l-1 1c0 1 1 2 1 2v1c-1-1-1-2-2-3h0l-1-1v-3z" class="U"></path><path d="M150 113c-1-1-1-1-2-1 0-1-1-1-1-2l1-1h0v-1-1c-1 0-1-1-2-1 0-1-1-2 0-3h2 0v-1c-1 0-1 0-2-1 1-1 2-1 3-1h0c-1-1-1-1-2-1l1-1h1v-1s-1 0-1-1h0 2 1c1 0 1 0 2 1h-2-1c1 1 2 1 3 1l-3 1v1c1 0 1 0 2 1h-1v1h1c1 1 2 2 2 3 1 1 0 2 1 3v2h-1c0 1 0 1 1 1v1h-1v1h1c0 1-1 1-2 1h0l-3-1z" class="m"></path><path d="M150 113c1-1 1-1 2-1 0-1-1-1-2-2h1 2v-1c-1 0-2-1-3-2v-2h-1v-1h1l1-1h-2v-1l2-1v1h1c1 1 2 2 2 3 1 1 0 2 1 3v2h-1c0 1 0 1 1 1v1h-1v1h1c0 1-1 1-2 1h0l-3-1z" class="P"></path><path d="M166 143l4 2 2 2h-2c1 1 2 2 2 3 2 0 2 0 3 1h1l1 1v1l3 3-1 1c-1-1-1-1-1-2-1 0-1 0-1-1l-2 1-1-1h-1v1 1c-1-1-1-2-2-2h0c-1 1-1 1-1 2s1 1 2 1c0 1 1 1 2 2h1-3c-1-1-1-1-2-1h0c-1 0-1 0-2-1v-1c-2 0-3 1-4 1v-1h1l1-1h0l-2-2v-1h0c-1 0-1-1-1-1-1-2-2-1-3-2 1-1 2-1 3-2 0 2 0 2 2 2l1-1h1c-1-1-1-2-1-3-1 0-1-1-2-1l2-1z" class="B"></path><path d="M164 152l2-1c1 1 2 3 3 5v1l1 1c-1 0-1 0-2-1v-1c-2 0-3 1-4 1v-1h1l1-1h0l-2-2v-1z" class="z"></path><path d="M166 143l4 2 2 2h-2c1 1 2 2 2 3h-1v2 1c-1-1-2-1-2-2l-2-3c-1-1-1-2-1-3-1 0-1-1-2-1l2-1z" class="u"></path><path d="M166 143l4 2 2 2h-2v-1h-1v1l1 2h-1c-1-1-2-3-3-4-1 0-1-1-2-1l2-1z" class="W"></path><path d="M152 131v-1c0 1 1 1 1 1l1-1c-2-1-4-3-5-5 1 0 2 1 4 2h2c0-2-2-3-3-5v-1l-2-2h2l-5-4c2 0 4 2 5 2h1c1 0 2 1 2 1v1h0c1 1 2 1 3 1h2c1 1 1 2 1 3h2l-2 2h-1 0l-1 1h1l-1 1v1s1 0 1 1h-3v1c1 1 1 1 2 1v1l2 2v1l1 1h0v2l-3-1v2c-1-1-2-1-2-2l-2-2-1-1h1c-1-2-2-2-3-3z" class="L"></path><path d="M156 121s1 0 1 1l1 1v1c1 0 1 1 2 1h0l-1 1h1l-1 1v1h-1l-2-1v-2s-1 0-1-1v-1c0-1-1-1-1-2h2z" class="e"></path><path d="M158 128h1s1 0 1 1h-3v1c1 1 1 1 2 1v1l2 2v1l1 1h0v2l-3-1h0v-1h1v-1h-2l-2-2h2c-1-1-1-2-2-3-1 0-1-1-2-1l1-1c1 0 2 0 3 1v-1z" class="Q"></path><path d="M156 121c-2-1-3-2-4-3h1l2 1h0c1 1 2 1 3 1h2c1 1 1 2 1 3h2l-2 2h-1c-1 0-1-1-2-1v-1l-1-1c0-1-1-1-1-1z" class="o"></path><path d="M158 124v-1c0-1 0-1 1-1l2 1h2l-2 2h-1c-1 0-1-1-2-1z" class="n"></path><path d="M153 97c3 0 9 5 11 8 1 1 1 2 1 3l2 2h0v1h2v1c-1 0-1 0-2 1h2v1c-1 0-2 1-3 1-1-1-3-1-4-1v-1h1v-1h-3c0-1-1-1-1-1h0l-1-1c0-1-1-2-2-2h-1c-1-1 0-2-1-3 0-1-1-2-2-3h-1v-1h1c-1-1-1-1-2-1v-1l3-1c-1 0-2 0-3-1h1 2z" class="S"></path><path d="M153 98c1 1 3 2 4 4v1 1h2c0 1 0 2-1 3l3 3s-1 0-2 1h0l-1-1c0-1-1-2-2-2h-1c-1-1 0-2-1-3 0-1-1-2-2-3h-1v-1h1c-1-1-1-1-2-1v-1l3-1z" class="Q"></path><path d="M165 128l-2-2h1 2v1c2 0 5-1 6 0l-3 1h0 4c-1 1-2 1-2 2h3-1c0 1-2 2-3 2h0 0 3v1h-1v1h3v1h-1l2 2v1c-1 1-2 1-3 1h-2v-1c-1 0-3 0-4-1h0c-1 0-2 1-3 1s-1-1-2-2h0l-1-1v-1l-2-2v-1c-1 0-1 0-2-1v-1h3v1l1-1c0-1-1-1-1-2h1 1l3 1z" class="I"></path><path d="M160 129v1l1-1c0-1-1-1-1-2h1 1l3 1h1 0c0 1 0 1 1 1 0 1 0 2-1 2l-1 1v1h2v1h-2v1c2 1 6 0 8 0-2 1-3 2-6 2h0c-1 0-2 1-3 1s-1-1-2-2h0l-1-1v-1l-2-2v-1c-1 0-1 0-2-1v-1h3zm71 128h1c1-1 1-1 1-2 1 1 1 2 2 2l2 2-2 2v1c0 1-1 2 0 3l-5 5 2 2-1 1-2 3h0c-2 1-4 3-4 5l-1 1c0 1-1 2-1 3l-2-1v1c-1 0-1-1-1-1h-1l-1-1 1-1h0v-1c-2 0-3 1-5 1 0 1-1 2-2 3 0 1-1 1-1 2h-1s-1 0-1 1-2 4-2 5c-1 2-1 3-1 4v1h-1l-2-1v1c-1 1-2 2-3 4h0c0 1 0 2 1 2l1 1-3 4c0 1-1 2-1 2v1 2h0l1 1 1 1v1h0 2 1 0l-1 2-1 1c0 1-1 3-1 4l-1 2c-1 2-2 5-3 7h0c-1 2 0 3-1 5l-1 1-1 2v2h-1c0-1 0-1-1-2v1 2l-2 1h1l-1 2s-1 1-1 2v1l-2-1 1-2h-1l-3 4h0-1 0l-1-1v-1-1h-1l-2 3h-1 0c-1-1 0-2 0-2 0-2 1-3 3-4v-3l-1 1-2 2c-1-1 0-2-2-2h0-1 0l-1-1-2 1h-2 0l-1-1-2 1h-1l-3 6c-1 0-1 0-1 1h-1v-4l1-2h-1l-2 2-2-1c1-2 2-3 3-4v-2l1-1v-2c1-2 2-5 3-7l2 1c0-2 1-4 2-6l1-4c1-1 2-3 1-4 0-1-1 0-2-1-1 0 0 1-1 1-1 1-1 1-2 1v-5-2-2c0-1 0-2 1-3v-1l1-1c0-1 1-2 1-3l1-1h0c0-1 1-1 1-2l4-4-1-1c2-1 3-2 5-3l3-4c1-1 3-2 4-4-1 0-1-1-1-2h0c1 1 1 1 2 1l-1-2h1 0c0-2-1-3 0-4s2-1 2-2c-1 1-2 1-4 2 1-1 5-4 5-5h-1l-1-1c1 0 1 0 2-1h2v1h1 2v-1c1 0 2 1 2 1 1-1 2-1 3-1l1 1c1-1 2-1 3-1h0c2-1 3-1 4-1h1c2 0 3-1 4-1h1v-1h-1v-1l4-3c0-1 1-2 2-3h0 1l-1 1c1 1 2 2 3 2h1 1l2-2h1c1 0 1 1 1 1h3l2-2h0z" class="N"></path><path d="M185 304l3-3c1-2 2-2 3-2l-2 3-2 2h-2z" class="M"></path><path d="M209 288c0 1-2 4-2 5-1 2-1 3-1 4v1h-1l-2-1c1-2 3-6 5-8l1-1z" class="h"></path><path d="M165 305l1 1v-1l3-3c0 2-2 5-3 6 1-1 2-1 3-2h1 0l-1 1v3s1 0 1 1l-2 2v-1-1c0-1-1-1-1-1h-1v-3l1-1h-1l-2 4v-2c0-1 0-2 1-3z" class="b"></path><path d="M176 305l1 1c0 2 0 2-1 3l-3 3c-1 1-1 2-2 3-1-1-2-1-3-2l2-2 6-6z" class="w"></path><path d="M164 310l2-4h1l-1 1v3h1s1 0 1 1v1 1c1 1 2 1 3 2h0c-1 2-2 9-4 9l1-4c1-1 2-3 1-4 0-1-1 0-2-1-1 0 0 1-1 1-1 1-1 1-2 1v-5-2z" class="m"></path><path d="M196 298c2-3 3-7 6-9h2c0 1 0 2-1 2l-3 6v2c1 0 2-1 3-1-1 1-2 2-3 4h0l-1 1h0l-1 2c-1-1 0-2-1-3l-2 1c0-1 1-3 2-4l-1-1z" class="I"></path><path d="M200 297v2c1 0 2-1 3-1-1 1-2 2-3 4h0l-1 1h0-1c0-1 0-2 1-3 0-1 1-2 1-3zm-10-5l1-1c0-1 1-1 2-2 2-2 4-4 6-5h1 0l-1 2c-1 0-1 0-1 1v1c-1 3-4 6-6 8-1 1-2 1-3 1h-1l-1 1h-1v-1h1c0-1 1-2 2-3 0-1 1-2 1-2z" class="M"></path><path d="M187 297h0c2-1 2-1 3-2 0-2 0-1 2-2 0 1 0 1-1 2l1 1c-1 1-2 1-3 1h-1l-1 1h-1v-1h1z" class="X"></path><path d="M176 305c0-1 2-3 3-4l4-4-4 6 3-2 1-1v1c0 1 0 2 1 3l-2 2 1 1c-1 1-1 1-1 2h1v3h0v1h1c-2 2-3 4-3 6l-1-1-3 5-1-1s1-1 1-2c-1-1-1-1-1-2v1h-2c-1 0-1 0-2-1 1-1 1-2 1-3-1-1 0-2 0-3l3-3c1-1 1-1 1-3l-1-1z" class="W"></path><path d="M173 312l3-3 1 2-3 3-1 1c-1-1 0-2 0-3z" class="m"></path><path d="M179 309v-1c0-2 3-5 4-7 0 1 0 2 1 3l-2 2-1 1-2 2z" class="X"></path><path d="M174 314c1 0 1 2 1 2h1 2l-2 2v1h-2c-1 0-1 0-2-1 1-1 1-2 1-3l1-1z" class="C"></path><path d="M176 316h2l-2 2v1h-2c1-1 1-2 1-3h1z" class="H"></path><path d="M182 306l1 1c-1 1-1 1-1 2h1v3h0v1h1c-2 2-3 4-3 6l-1-1-3 5-1-1s1-1 1-2c-1-1-1-1-1-2l2-2h-2c0-1 1-1 1-2v-1c1 0 1-1 1-1 0-1 1-2 1-3l2-2 1-1z" class="C"></path><path d="M178 316l1-1v1c0 1-1 1-1 2h0l-1 2c-1-1-1-1-1-2l2-2z" class="K"></path><path d="M181 307l1 1c0 1 0 2-1 2 0 1 0 2-1 2h-2c0-1 1-2 1-3l2-2z" class="R"></path><path d="M178 318c1-1 2-2 3-2v1s0 1-1 1l-3 5-1-1s1-1 1-2l1-2z" class="P"></path><defs><linearGradient id="L" x1="171.217" y1="333.315" x2="168.236" y2="329.283" xlink:href="#B"><stop offset="0" stop-color="#292a2a"></stop><stop offset="1" stop-color="#4f4d4d"></stop></linearGradient></defs><path fill="url(#L)" d="M173 312c0 1-1 2 0 3 0 1 0 2-1 3 1 1 1 1 2 1h2v-1c0 1 0 1 1 2 0 1-1 2-1 2l1 1 3-5 1 1-1 2v1l1 1c-1 2-2 4-4 5-1 2-2 4-3 5l-2 1-4 4-3 5-3 6c-1 0-1 0-1 1h-1v-4l1-2h-1l-2 2-2-1c1-2 2-3 3-4v-2l1-1v-2c1-2 2-5 3-7l2 1c0-2 1-4 2-6 2 0 3-7 4-9h0c1-1 1-2 2-3z"></path><path d="M166 331l1-1h1l1 1-2 3c0-1-1-2-1-3z" class="d"></path><path d="M169 335c1-1 2-2 4-3l-1 2-4 4 1-3z" class="S"></path><path d="M164 337v-1h0c0-2 1-3 2-5 0 1 1 2 1 3l-3 3z" class="V"></path><path d="M173 332l7-10 1 1c-1 2-2 4-4 5-1 2-2 4-3 5l-2 1 1-2z" class="O"></path><defs><linearGradient id="M" x1="161.959" y1="349.968" x2="164.668" y2="337.305" xlink:href="#B"><stop offset="0" stop-color="#272928"></stop><stop offset="1" stop-color="#474546"></stop></linearGradient></defs><path fill="url(#M)" d="M169 335l-1 3-3 5-3 6c-1 0-1 0-1 1h-1v-4l1-2h-1l-2 2-2-1c1-2 2-3 3-4h1l1-1v1c3-1 6-4 8-6z"></path><path d="M160 341v2h1l1-1h0c0 1-1 1-1 2h0-1l-2 2-2-1c1-2 2-3 3-4h1z" class="R"></path><path d="M176 318c0 1 0 1 1 2 0 1-1 2-1 2l1 1-4 4c-2 1-2 3-4 4l-1-1h-1l2-4h0l2-3v-2c0-1 1-2 1-3 1 1 1 1 2 1h2v-1z" class="b"></path><path d="M169 326h1c0 2-1 3-2 4h0-1l2-4z" class="I"></path><path d="M175 320c0 2-2 4-3 5 0 0 0 1-1 1h-1-1 0l2-3h0v1h0 1c1-1 1-2 2-3l1-1z" class="K"></path><path d="M172 318c1 1 1 1 2 1h2l-1 1h0l-1 1c-1 1-1 2-2 3h-1 0v-1h0v-2c0-1 1-2 1-3z" class="J"></path><path d="M173 312c0 1-1 2 0 3 0 1 0 2-1 3 0 1-1 2-1 3v2l-2 3h0l-2 4-1 1c-1 2-2 3-2 5h0v1l-3 3-1 1h-1v-2l1-1v-2c1-2 2-5 3-7l2 1c0-2 1-4 2-6 2 0 3-7 4-9h0c1-1 1-2 2-3z" class="D"></path><path d="M173 312c0 1-1 2 0 3 0 1 0 2-1 3 0 1-1 2-1 3v2l-2 3v-1c0-1 1-2 1-3l2-6-1-1h0c1-1 1-2 2-3z" class="S"></path><path d="M160 336c1-2 2-5 3-7l2 1c-1 3-3 6-5 8v-2z" class="b"></path><path d="M207 266c2 0 3-1 4-1-1 2-3 4-5 5h0l1 1c1-1 1-1 2-1 0 1-2 2-2 4h0 0 1l-1 2h2l-4 5-5 5v1l-1 1h-1v-1c0-1 0-1 1-1l1-2h0-1c-2 1-4 3-6 5-1 1-2 1-2 2l-1 1v-3c-1 0-2 1-2 2l-1 1-2 2h-1v-1c1-1 3-2 3-4h0l1-1h-1c-1 0-1 0-2 1h0c-2 0-2 1-3 2 0 1 0 2-1 3h0l-2 2-1 1c-1 1 0 1-1 1-1 1-2 3-3 4v-1l1-2c1-1 1-1 1-2l-1-1s-1 0-1 1l-4 4v-1c0-1 1-1 1-2h0l2-2 1-1 1-1c3-1 5-3 7-5v-1c-2 1-3 1-4 1h-1l3-4c1-1 3-2 4-4-1 0-1-1-1-2h0c1 1 1 1 2 1l-1-2h1 0c0-2-1-3 0-4s2-1 2-2c-1 1-2 1-4 2 1-1 5-4 5-5h-1l-1-1c1 0 1 0 2-1h2v1h1 2v-1c1 0 2 1 2 1 1-1 2-1 3-1l1 1c1-1 2-1 3-1h0c2-1 3-1 4-1h1z" class="a"></path><path d="M182 291c0 1 0 2-1 3h0 0-2-1v-1c1-1 2-1 4-2z" class="X"></path><path d="M197 279c1-1 2-2 3-1h2c-1 1-1 1-1 2-3 0-4 2-6 3h0-2c1-2 2-3 4-4z" class="f"></path><path d="M183 279c1 1 1 1 2 1l1 1c0 1-1 1-2 2 1 0 1 0 1 1h-1c-2 0-4 3-5 4l-1 1h-1l3-4c1-1 3-2 4-4-1 0-1-1-1-2h0z" class="V"></path><path d="M191 281v2l-1 1v1c-1 1-2 2-3 2h-1l1-1h-1-2-1c0 1-1 1-1 1h0l2-3h1l2-1h2c0-1 1-1 1-2h1z" class="t"></path><path d="M187 283h2c0 1 0 1-1 2h-1v-2z" class="G"></path><path d="M191 281v-1-1 1c-1-1-1-1-1-2h0c1 0 2-1 3-1 0-1 0-1 1-1v1c1 0 2 1 3 1h0v1c-2 1-3 2-4 4l-3 2v-1l1-1v-2z" class="I"></path><path d="M197 278v1c-2 1-3 2-4 4l-3 2v-1l1-1c2-1 4-3 6-5h0z" class="d"></path><path d="M206 270l1 1c1-1 1-1 2-1 0 1-2 2-2 4h0 0 1l-1 2-15 12h-1l10-8c0-1 0-1 1-2h-2c-1-1-2 0-3 1v-1-1l4-2c2-1 4-3 5-5z" class="P"></path><path d="M207 266c2 0 3-1 4-1-1 2-3 4-5 5h0c-1 2-3 4-5 5l-4 2v1h0c-1 0-2-1-3-1v-1c-1 0-1 0-1 1-1 0-2 1-3 1h0c0 1 0 1 1 2v-1 1 1h-1c0 1-1 1-1 2h-2l-2 1c0-1 0-1-1-1 1-1 2-1 2-2l-1-1-1-2h1 0c0-2-1-3 0-4s2-1 2-2c-1 1-2 1-4 2 1-1 5-4 5-5h-1l-1-1c1 0 1 0 2-1h2v1h1 2v-1c1 0 2 1 2 1 1-1 2-1 3-1l1 1c1-1 2-1 3-1h0c2-1 3-1 4-1h1z" class="Q"></path><path d="M187 283v-2h0l1-1h1s0 1 1 1h0c0 1-1 1-1 2h-2z" class="I"></path><path d="M199 268c1-1 2-1 3-1h0c2-1 3-1 4-1 0 1-1 1-2 1-2 0-4 3-5 4-2 0-4 1-5 2l1-2c1-1 3-2 4-3z" class="G"></path><path d="M197 277v-1c1-2 3-3 4-4s2-3 4-4v1c-1 2-4 3-4 6l-4 2z" class="a"></path><path d="M194 273c1-1 3-2 5-2-2 1-2 2-4 3h-1c-1 2-3 2-4 3s-2 1-3 1l-1-1-1 1h0c0-2-1-3 0-4l1 1 1-1h1c0 1-1 1 0 2 1-1 2-1 3-2v-1h3z" class="t"></path><path d="M186 277l1-1h1v1h2c-1 1-2 1-3 1l-1-1z" class="I"></path><path d="M188 267h2v1h1 2v-1c1 0 2 1 2 1 1-1 2-1 3-1l1 1c-1 1-3 2-4 3l-1 2h-3v1c-1 1-2 1-3 2-1-1 0-1 0-2h-1l-1 1-1-1c1-1 2-1 2-2-1 1-2 1-4 2 1-1 5-4 5-5h-1l-1-1c1 0 1 0 2-1z" class="r"></path><path d="M195 268c1-1 2-1 3-1l1 1c-1 1-3 2-4 3-1 0-1 0-2 1h0-3c-1 1-1 1-2 1h0c1-2 2-2 3-4 0-1 0 0-1 0v-1h1 2v-1c1 0 2 1 2 1z" class="h"></path><path d="M193 268v-1c1 0 2 1 2 1l-2 1h0v-1z" class="T"></path><path d="M231 257h1c1-1 1-1 1-2 1 1 1 2 2 2l2 2-2 2v1c0 1-1 2 0 3l-5 5 2 2-1 1-2 3h0c-2 1-4 3-4 5l-1 1c0 1-1 2-1 3l-2-1v1c-1 0-1-1-1-1h-1l-1-1 1-1h0v-1c-2 0-3 1-5 1 0 1-1 2-2 3 0 1-1 1-1 2h-1s-1 0-1 1l-1 1c0-1 0-3-1-4v-1c-2 1-3 3-5 3h0l1-1v-1l-3 2v-1l5-5 4-5h-2l1-2h-1 0 0c0-2 2-3 2-4-1 0-1 0-2 1l-1-1h0c2-1 4-3 5-5h1v-1h-1v-1l4-3c0-1 1-2 2-3h0 1l-1 1c1 1 2 2 3 2h1 1l2-2h1c1 0 1 1 1 1h3l2-2h0z" class="K"></path><path d="M232 260h1s1 0 1-1l1 1v1c-1 1-2 1-3 1l-1-1 1-1z" class="O"></path><path d="M210 272v1h3c-1 1-3 2-4 3h-2l1-2 2-2z" class="f"></path><path d="M215 266h1l1 1h0 1v1l-2 2-3 3h-3v-1c1-2 4-4 5-6z" class="P"></path><path d="M218 267v1l-2 2v-1c0-1 1-1 2-2z" class="I"></path><path d="M223 260l1 1h0c1 0 2-1 3-1 0 1-1 1-2 2h0l-4 4h-1v-1h-2v1c-1 1-1 1-2 0h-1 0c1-2 3-3 4-4l2-2h1 1z" class="P"></path><path d="M222 260h1l-1 1c0 1 0 1 1 2-1 0-1 1-2 1v-1c-1 0-2 0-3 1v1 1c-1 1-1 1-2 0h-1 0c1-2 3-3 4-4l2-2h1z" class="b"></path><path d="M231 257h1c1-1 1-1 1-2 1 1 1 2 2 2l2 2-2 2h0v-1l-1-1c0 1-1 1-1 1h-1v-2h0c-1 1-1 1-2 1s-1 1-2 1v1l1 1c0 1-3 3-4 4h-1c0 1-1 1-2 2v-1l4-4h0l-1-1h0c1-1 2-1 2-2-1 0-2 1-3 1h0l-1-1h-1l2-2h1c1 0 1 1 1 1h3l2-2h0z" class="G"></path><defs><linearGradient id="N" x1="216.194" y1="265.883" x2="210.01" y2="265.804" xlink:href="#B"><stop offset="0" stop-color="#8d8c8d"></stop><stop offset="1" stop-color="#a7a5a4"></stop></linearGradient></defs><path fill="url(#N)" d="M217 257h1l-1 1c1 1 2 2 3 2h1l-2 2c-1 1-3 2-4 4h0c-1 2-4 4-5 6l-2 2h-1 0 0c0-2 2-3 2-4-1 0-1 0-2 1l-1-1h0c2-1 4-3 5-5h1v-1h-1v-1l4-3c0-1 1-2 2-3h0z"></path><path d="M217 257h1l-1 1c1 1 2 2 3 2h1l-2 2h-1v-1c-2 0-3 1-4 2v-1c0-1 1-1 1-2h0 0c0-1 1-2 2-3h0z" class="L"></path><path d="M235 262c0 1-1 2 0 3l-5 5 2 2-1 1-2 3h0c-2 1-4 3-4 5l-1 1c0 1-1 2-1 3l-2-1v1c-1 0-1-1-1-1h-1l-1-1 1-1h0v-1c-2 0-3 1-5 1 0 1-1 2-2 3 0 1-1 1-1 2h-1s-1 0-1 1l-1 1c0-1 0-3-1-4v-1c-2 1-3 3-5 3h0l1-1v-1c0-1 2-3 3-4 2-1 5-3 6-5l1-1c2-1 4-2 5-3v-1c0-1 1-2 1-2h1l-1 2h0l3-3 1 1h1l2-2h0v1 1h1l1 1 7-8z" class="I"></path><path d="M213 280c0-2 0-2 1-3 1 1 2 1 3 1h1l-2 2h-3z" class="K"></path><path d="M207 285c2 0 2 0 3-1h0l2 1h0c0 1-1 1-1 2h-1s-1 0-1 1l-1 1c0-1 0-3-1-4z" class="a"></path><path d="M212 285c0-1 0-1-1-2h-1 0l3-3h0 3l-2 2c0 1-1 2-2 3h0z" class="V"></path><path d="M218 278c1-2 2-5 4-6v1h0l1 2c-2 2-3 5-4 7v-1c-2 0-3 1-5 1l2-2 2-2z" class="Q"></path><path d="M235 262c0 1-1 2 0 3l-5 5 2 2-1 1-2 3h0c-2 1-4 3-4 5l-1 1c0 1-1 2-1 3l-2-1v1c-1 0-1-1-1-1h-1l-1-1 1-1h0c1-2 2-5 4-7l5-5 7-8z" class="D"></path><path d="M219 282v1l2-1h0v2h0v1c-1 0-1-1-1-1h-1l-1-1 1-1z" class="J"></path><path d="M222 282v-1c0-1 1-4 3-5 1-2 3-4 5-6l2 2-1 1-2 3h0c-2 1-4 3-4 5l-1 1c0 1-1 2-1 3l-2-1h0l1-2z" class="T"></path><path d="M224 278s1 0 1-1c0 0 2-3 3-3v1l1 1h0c-2 1-4 3-4 5l-1 1c0 1-1 2-1 3l-2-1h0l1-2 2-4z" class="H"></path><path d="M224 278l1 1c-1 1-1 1-1 2h1l-1 1c0 1-1 2-1 3l-2-1h0l1-2 2-4z" class="b"></path><path d="M192 299c0-1 1-1 2-2v1l-1 1c1 1 1 1 1 2l2-3 1 1c-1 1-2 3-2 4l2-1c1 1 0 2 1 3l1-2h0l1-1c0 1 0 2 1 2l1 1-3 4c0 1-1 2-1 2v1 2h0l1 1 1 1v1h0 2 1 0l-1 2-1 1c0 1-1 3-1 4l-1 2c-1 2-2 5-3 7h0c-1 2 0 3-1 5l-1 1-1 2v2h-1c0-1 0-1-1-2v1 2l-2 1h1l-1 2s-1 1-1 2v1l-2-1 1-2h-1l-3 4h0-1 0l-1-1v-1-1h-1l-2 3h-1 0c-1-1 0-2 0-2 0-2 1-3 3-4v-3l-1 1-2 2c-1-1 0-2-2-2h0-1 0l-1-1-2 1h-2 0l-1-1-2 1h-1l3-5 4-4 2-1c1-1 2-3 3-5 2-1 3-3 4-5l-1-1v-1l1-2c0-2 1-4 3-6h-1v-1h0v-3h-1c0-1 0-1 1-2l-1-1 2-2h1 2l2-2 2-3h1z" class="J"></path><path d="M182 321l1 1 1-1c1 2-1 4-1 5v1l-2-1 1-3h-1l-1-1v-1l1 1 1-1z" class="b"></path><path d="M180 321l1 1 1-1v2h-1l-1-1v-1z" class="K"></path><path d="M192 312l1 2h0c1 0 1-1 1 0v2c0 1 0 2-1 3h-2c0-1 1-2 1-3h-2l1-3 1-1z" class="D"></path><path d="M191 313c1 1 1 2 1 3h-2l1-3z" class="d"></path><path d="M184 313c1 1 1 1 1 3l-1 1c-1 1-2 2-2 4l-1 1-1-1 1-2c0-2 1-4 3-6z" class="P"></path><path d="M187 322c1-2 2-3 4-4v1l-1 2c-1 2-1 4-2 5l-1 2c-1 1-1 1-1 2l-2 1-1 2-2 1h0-1c1-2 2-4 3-5l4-7z" class="p"></path><path d="M187 322c1-2 2-3 4-4v1l-1 2c-1 2-1 4-2 5l-1 2v-2l1-1c0-1 1-1 1-3h-2z" class="I"></path><path d="M181 323h1l-1 3 2 1-6 9h0l-1 1c-1-1 0-2 0-3l-4 5c0-2 1-4 2-6 1-1 2-3 3-5 2-1 3-3 4-5z" class="P"></path><path d="M181 326l2 1-6 9c-1-1-1-1-1-2 1 0 1-1 2-2s3-4 3-6z" class="I"></path><path d="M174 333c-1 2-2 4-2 6l4-5c0 1-1 2 0 3-1 1-1 2-2 3h-1v2l-2 1h-2 0l-1-1-2 1h-1l3-5 4-4 2-1z" class="b"></path><path d="M169 343c1-2 2-3 4-4l1 1h-1v2l-2 1h-2 0z" class="L"></path><path d="M181 335c1 0 2 0 2 1l1 1c-1 2-3 4-4 5l-1 1-2 2c-1-1 0-2-2-2h0-1 0l-1-1v-2h1c1-1 1-2 2-3l1-1v2h0l2-1 2-2z" class="Y"></path><path d="M179 337c0 1 0 2-1 3h-2l1-2 2-1z" class="R"></path><path d="M177 336v2h0l-1 2h2l-1 2c1 1 1 1 2 1l-2 2c-1-1 0-2-2-2h0-1 0l-1-1v-2h1c1-1 1-2 2-3l1-1z" class="E"></path><path d="M177 336v2h0l-1 2-1 2-1-1h0c0-1 0 0-1-1h1c1-1 1-2 2-3l1-1z" class="l"></path><path d="M192 304h1c1 2-1 4-1 6h0v2l-1 1-1 3c-1 1-1 2-2 3h-1c-1 0-2 1-2 2l-1-1c1-1 1-2 0-3h0l1-1c3-4 4-9 7-12z" class="S"></path><path d="M192 310v2l-1 1-1 3c-1 1-1 2-2 3h-1c1-3 3-7 5-9z" class="Q"></path><defs><linearGradient id="O" x1="198.095" y1="309.146" x2="193.139" y2="306.486" xlink:href="#B"><stop offset="0" stop-color="#3e3e3d"></stop><stop offset="1" stop-color="#626162"></stop></linearGradient></defs><path fill="url(#O)" d="M192 304l2-2h1c0 1 0 2-1 3 0 0 1 2 2 2h0l2-2 1-2h0l1-1c0 1 0 2 1 2l1 1-3 4c0 1-1 2-1 2v1c-1 2-2 3-4 4v-2c0-1 0 0-1 0h0l-1-2v-2h0c0-2 2-4 1-6h-1z"></path><path d="M199 303h0l1 1-2 2c-1 1-1 2-2 2v-1l2-2 1-2z" class="E"></path><path d="M194 314v-1h1v-2c1-1 2-2 4-2 0 1-1 2-1 2v1c-1 2-2 3-4 4v-2z" class="J"></path><path d="M188 326c1 1 1 1 1 3v1c0 1-1 2-1 4l-1 1h1c0-1 1-2 2-2v1l-6 7-4 5v-1-3c1-1 3-3 4-5l-1-1c0-1-1-1-2-1v-1l2-1 1-2 2-1c0-1 0-1 1-2l1-2z" class="Q"></path><path d="M188 326c1 1 1 1 1 3v1c-1 1-2 3-3 4 0-2 1-3 0-4 0-1 0-1 1-2l1-2z" class="H"></path><path d="M184 337l1-2v2c0 2-4 6-5 8h0v-3c1-1 3-3 4-5z" class="G"></path><path d="M186 330c1 1 0 2 0 4l-1 1-1 2-1-1c0-1-1-1-2-1v-1l2-1 1-2 2-1z" class="i"></path><path d="M184 331c1 1 1 2 1 3h-2v-1l1-2z" class="B"></path><path d="M192 299c0-1 1-1 2-2v1l-1 1c1 1 1 1 1 2l2-3 1 1c-1 1-2 3-2 4l2-1c1 1 0 2 1 3l-2 2h0c-1 0-2-2-2-2 1-1 1-2 1-3h-1l-2 2c-3 3-4 8-7 12 0-2 0-2-1-3h-1v-1h0v-3h-1c0-1 0-1 1-2l-1-1 2-2h1 2l2-2 2-3h1z" class="E"></path><path d="M189 302l2 2h-1 0c-2 0-2 2-3 3l-1-1c1 0 1-1 0-1l1-1 2-2z" class="S"></path><path d="M185 304h2l-1 1c-1 1-2 2-3 4h-1c0-1 0-1 1-2l-1-1 2-2h1z" class="V"></path><path d="M192 299c0-1 1-1 2-2v1l-1 1c1 1 1 1 1 2l2-3 1 1c-1 1-2 3-2 4l2-1c1 1 0 2 1 3l-2 2h0c-1 0-2-2-2-2 1-1 1-2 1-3h-1l-2 2c-3 3-4 8-7 12 0-2 0-2-1-3h-1l1-1c1 0 1 1 2 1 1-3 3-6 4-9h1l-2-2 2-3h1z" class="r"></path><path d="M192 299c0 1 1 2 1 2-1 1-2 2-2 3l-2-2 2-3h1z" class="C"></path><path d="M198 312v2h0l1 1 1 1v1h0 2 1 0l-1 2-1 1h-1 0c-1 1-2 2-3 2l-1 1c-1 3-1 6-3 9h0c-1 0-2 1-2 2l-1 1v-1-1c-1 0-2 1-2 2h-1l1-1c0-2 1-3 1-4v-1c0-2 0-2-1-3 1-1 1-3 2-5v2h0c1 0 1-1 2-1 0-1 1-2 1-3 1-1 1-2 1-3 2-1 3-2 4-4z" class="t"></path><path d="M198 315v-1l1 1 1 1v1h0l-3 5-1 1h0v-1l1-1v-1l1-2v-3z" class="P"></path><path d="M202 317h1 0l-1 2-1 1h-1 0c-1 1-2 2-3 2l3-5h2z" class="i"></path><path d="M198 312v2h0v1c-4 5-5 12-8 18-1 0-2 1-2 2h-1l1-1c0-2 1-3 1-4v-1c0-2 0-2-1-3 1-1 1-3 2-5v2h0c1 0 1-1 2-1 0-1 1-2 1-3 1-1 1-2 1-3 2-1 3-2 4-4z" class="M"></path><path d="M188 326c1-1 1-3 2-5v2h0c1 0 1-1 2-1l-3 7c0-2 0-2-1-3z" class="O"></path><path d="M197 322c1 0 2-1 3-2h0 1c0 1-1 3-1 4l-1 2c-1 2-2 5-3 7h0c-1 2 0 3-1 5l-1 1-1 2v2h-1c0-1 0-1-1-2v1 2l-2 1h1l-1 2s-1 1-1 2v1l-2-1 1-2h-1l-3 4h0-1 0l-1-1v-1-1h-1l-2 3h-1 0c-1-1 0-2 0-2 0-2 1-3 3-4v1l4-5 6-7v1l1-1c0-1 1-2 2-2h0c2-3 2-6 3-9l1-1z" class="B"></path><path d="M191 339l1 1-1 1v1h-2l2-3z" class="D"></path><path d="M181 349c0-1 1-1 2-1v1l-1 2-1-1v-1z" class="Y"></path><path d="M187 345l2-3h2v2l-2 1c0-1-1 0-2 0z" class="C"></path><path d="M187 345c1 0 2-1 2 0h1l-1 2s-1 1-1 2v1l-2-1 1-2h-1l-3 4h0-1 0l1-2 4-4z" class="l"></path><path d="M191 339c1-2 3-4 5-6h0c-1 2 0 3-1 5l-1 1-1 2v2h-1c0-1 0-1-1-2l1-1-1-1z" class="C"></path><path d="M192 340l1-1v2 2h-1c0-1 0-1-1-2l1-1z" class="l"></path><path d="M190 334v1l1-1c0-1 1-2 2-2h0c-2 4-5 9-9 12 0-1 0-1 1-2v-1h-1l6-7z" class="d"></path><path d="M184 341h1v1c-1 1-1 1-1 2l-3 4h-1l-2 3h-1 0c-1-1 0-2 0-2 0-2 1-3 3-4v1l4-5z" class="G"></path><path d="M177 349c1-1 2-1 3-1l-2 3h-1 0c-1-1 0-2 0-2z" class="O"></path><path d="M236 270c1 1 1 1 1 2h0v1h1v1l1-1s0 1 1 1v-1h1l1 2v2 3c0 1 0 2 1 4 0 1 1 3 1 5l2 2s1 1 1 2h1v-1c1-1 2 0 3 0l3 11 1 2 1 4v1h0c3 5 2 8 3 12l2-7v-4-3h0l1-1h0c0 2 1 3 1 5l-2 7c0 1-1 2-1 3 0 2-1 6-1 8h1l-1 5v1l-1 3v4c-1 0-1 0-1-1v2l-1 2h-1c0-1 0-3-1-4v-1s-1-1-1-2 0-1-1-1c0-1 0-1-1-1v-2s0-1-1-2c0-1-1-1-2-2h-5v-1h0 0c-1 1-2 1-3 1s-1 0-1-1l-1 1h0 0v2l1 2h0 3 0 2l-1 1h1c-1 1-1 1-1 2l-1 2h0v3 2 3h-1l-1 1c0 2 0 3 1 5v1h-1v2h-1-2c-1 0-1 0-2 1h-1 0c-2 0-2 1-3 2l-2 3h0l-2-1-1 4v1l-2 5 5 3-1 1-7-4-1-1h0c-1 1-1 1-2 1h-1c-1 0-1-1-1-1l-2-2-5-1h-4l-5 1h-2 0c-1 0-2 0-3 1h-1l-3 2-2 1-1 1h-1l-1 1-1 1-3 3h-1-2v1c-1-1-1-1-2-1h0c0-2 1-6 2-7v-2h-1l1-1-1-1v-1-4c0-2 1-5 3-7l2-5h0l3-4h1l-1 2 2 1v-1c0-1 1-2 1-2l1-2h-1l2-1v-2-1c1 1 1 1 1 2h1v-2l1-2 1-1c1-2 0-3 1-5h0c1-2 2-5 3-7l1-2c0-1 1-3 1-4l1-1 1-2h0-1-2 0v-1l-1-1-1-1h0v-2-1s1-1 1-2l3-4-1-1c-1 0-1-1-1-2h0c1-2 2-3 3-4v-1l2 1h1v-1c0-1 0-2 1-4 0-1 2-4 2-5s1-1 1-1h1c0-1 1-1 1-2 1-1 2-2 2-3 2 0 3-1 5-1v1h0l-1 1 1 1h1s0 1 1 1v-1l2 1c0-1 1-2 1-3l1-1c0-2 2-4 4-5h0l2-3h2l2-1s1-1 1-2z" class="o"></path><path d="M213 290c0-2 0-3 1-4 1 1 0 2 0 4h-1z" class="e"></path><path d="M209 298l1 1v1 1h1 0v1l-1 3h0-1v-2l-1 1c0-2 1-4 1-6z" class="I"></path><path d="M210 300v1h1 0v1l-1 3-1-1c0-1 1-2 1-4z" class="Q"></path><path d="M209 288c0-1 1-1 1-1h1c0 3 0 4-2 6h0-2c0-1 2-4 2-5z" class="M"></path><path d="M207 293h2c-1 3-1 6-2 9v-1-1h-1v-2-1c0-1 0-2 1-4z" class="f"></path><path d="M206 297h1v2s-1 0-1 1v-2-1z" class="Q"></path><path d="M214 293v-1c1 0 1-1 1-2 1 1 2 1 2 2s-1 2-1 3l-2 2c0 1 0 1-1 2v-1h-1v-1c0-1 1-2 2-4z" class="e"></path><path d="M214 293v1c0 1-1 2 0 3 0 1 0 1-1 2v-1h-1v-1c0-1 1-2 2-4z" class="AD"></path><path d="M213 290h1l-3 6 1 1v1 1l-1 2h-1v-1-1l-1-1 1-1c0-2 1-5 3-7z" class="p"></path><path d="M210 299c0-1 1-2 1-3l1 1v1 1l-1 2h-1v-1-1z" class="a"></path><path d="M205 298h1v2h1v1 1 2l1 1v-1l1-1v2h1l-1 6c-1 0-1-1-1-1v-1l-1-2h-1c-1 1-1 2-2 3-1-2 1-5 1-8 0 0 0-1-1-1l1-3z" class="V"></path><path d="M205 302c0 2 0 2 1 4h1l1 1v2l-1-2h-1c-1 1-1 2-2 3-1-2 1-5 1-8z" class="H"></path><path d="M217 287c0-1 1-3 1-4l1 1h1s0 1 1 1v-1l2 1-3 4-3 6v1c-1 0-1-1-1-1 0-1 1-2 1-3s-1-1-2-2l1-1 1-2z" class="Q"></path><path d="M216 289h1l1-1v2c0 1-1 1-1 2 0-1-1-1-2-2l1-1z" class="M"></path><path d="M217 287c0-1 1-3 1-4l1 1h1s0 1 1 1v-1l2 1-3 4-1 1c-1-1 0-2 0-4h0c-1 0-1 0-1 1h-1z" class="P"></path><path d="M216 295s0 1 1 1v2l1 1-1 4c0 1-1 2-1 3-1 0-1 0-1-1l-1-2c-1-1-1-1-1 0l-1-1-1-1h0l1-2v-1h1v1c1-1 1-1 1-2l2-2z" class="I"></path><path d="M212 299h0c1 1 3 4 3 5v1c1-1 1-1 1-2h1c0 1-1 2-1 3-1 0-1 0-1-1l-1-2c-1-1-1-1-1 0l-1-1-1-1h0l1-2z" class="P"></path><path d="M211 301l1 1 1 1c0-1 0-1 1 0l1 2c0 1 0 1 1 1-1 2-1 4-1 5l-1 1-1 1h-1 0-2 0l-1-2 1-6h0l1-3v-1z" class="L"></path><path d="M210 305l1 1 1 7h-2 0l-1-2 1-6z" class="m"></path><path d="M211 301l1 1 1 1c0-1 0-1 1 0l1 2c0 1 0 1 1 1-1 2-1 4-1 5-1-1-2-1-2-2v-1c-3-1-1-4-2-6v-1z" class="r"></path><path d="M212 302l1 1c0-1 0-1 1 0l1 2-1-1-2 2v-4z" class="K"></path><path d="M225 281l1 1c-1 1-1 2 0 3-1 1-2 3-3 4 1 1 1 1 1 2-1 1-2 4-2 6 0 0-1 1-1 2v-3h-1l-2 3-1-1v-2-1l3-6 3-4c0-1 1-2 1-3l1-1z" class="a"></path><path d="M225 281l1 1c-1 1-1 2 0 3-1 1-2 3-3 4v1c0-1 0-2 1-4 0-1 1-2 0-4l1-1z" class="V"></path><path d="M223 289c1 1 1 1 1 2-1 1-2 4-2 6 0 0-1 1-1 2v-3h-1l3-6v-1z" class="S"></path><path d="M220 296h1v3l-1 2v2h1v4h-1l-2 4h0v1l-2 1-1 2-1-1v-2l1-1c0-1 0-3 1-5 0-1 1-2 1-3l1-4 2-3z" class="F"></path><path d="M220 301v2h1v4h-1l-2 4h0c0-3 0-5 1-8l1-2z" class="l"></path><path d="M220 301v2h1v4h-1c0-2-1-3-1-4l1-2z" class="D"></path><path d="M203 297l2 1-1 3c1 0 1 1 1 1 0 3-2 6-1 8v1l-2 1h0c-1 2-1 3-2 5v-1l-1-1-1-1h0v-2-1s1-1 1-2l3-4-1-1c-1 0-1-1-1-2h0c1-2 2-3 3-4v-1z" class="c"></path><path d="M202 304c1 2 0 6 0 8h0v-3c-1 1-2 0-2 0h-1l3-4v-1z" class="G"></path><path d="M203 297l2 1-1 3h0c-1 1-2 2-2 3v1l-1-1c-1 0-1-1-1-2h0c1-2 2-3 3-4v-1z" class="S"></path><path d="M200 302h1 1c0-1 0-1 1-1h1c-1 1-2 2-2 3v1l-1-1c-1 0-1-1-1-2h0z" class="F"></path><path d="M199 309h1s1 1 2 0v3c-1 2-1 3-2 5v-1l-1-1-1-1h0v-2-1s1-1 1-2z" class="t"></path><path d="M198 314l1-2h1c0 1 0 2-1 3l-1-1h0z" class="G"></path><path d="M204 310c1-1 1-2 2-3h1l1 2v1s0 1 1 1l1 2h0l1 2v2h-1-1c0 1 1 2 1 3l-1 1h0l-3 4v-1h0c-1 0-1-1-1-1-1-1-1-2-1-2l1-1-1-1v1l-2-1 1-2h0-1-2 0c1-2 1-3 2-5h0l2-1v-1z" class="E"></path><path d="M204 315l2-2c1 1 0 1 1 2-1 0-1 1-2 1l-1-1z" class="l"></path><path d="M210 313h0l1 2v2h-1l-2-1c1-2 1-2 2-3z" class="D"></path><path d="M204 311c0 2-1 3-2 4v2h-2 0c1-2 1-3 2-5h0l2-1z" class="S"></path><path d="M204 315l1 1c1 0 1-1 2-1v2l1-1 2 1h-1c0 1 1 2 1 3l-1 1h0l-3 4v-1h0c-1 0-1-1-1-1-1-1-1-2-1-2l1-1-1-1v1l-2-1 1-2h0c0-1 1-2 1-2z" class="h"></path><path d="M203 317h0l1 1h1c0-1 0-1 1-2 0 1 0 2 1 3 0-1 0-1 1-1-1 1-1 2-2 2v1h-1v-1l-1-1v1l-2-1 1-2z" class="D"></path><path d="M208 318l1 3h0l-3 4v-1h0c-1 0-1-1-1-1-1-1-1-2-1-2l1-1v1h1v-1c1 0 1-1 2-2z" class="J"></path><path d="M205 320v1h1v-1c1 1 1 1 1 2h0c-1 1-1 2-1 2h0c-1 0-1-1-1-1-1-1-1-2-1-2l1-1z" class="Z"></path><path d="M229 286c1 0 1 1 1 1l-2 5c1 1 1 1 1 2l-1 4v3h1c0 2-1 4-1 6l-1 1c-1 2-1 4-3 5v2c0 1-1 1-1 2v-3h-1 0c0 2-1 1-2 2h0v1h0l-1-1v-3h-1v2c-1 0-1-1-2-2l2-1v-1h0l2-4h1v-4h-1v-2l1-2c0-1 1-2 1-2 0-2 1-5 2-6s1-1 1-2l1-1v1h1 1l1-3z" class="F"></path><path d="M228 292c1 1 1 1 1 2l-1 4v-3l-1 1 1-4z" class="E"></path><path d="M228 301h1c0 2-1 4-1 6l-1 1c0-3 0-5 1-7z" class="k"></path><path d="M222 302l1-1v1 2l1-1 1 2-1 3v2h-1c-1-1-1-1-1-2v-2h0c-1 2-1 3-1 5 1 0 2 2 2 2 1 1 0 2 1 2 0 1-1 1-1 2v-3h-1 0c0 2-1 1-2 2h0v1h0l-1-1v-3h-1v2c-1 0-1-1-2-2l2-1v-1h0l2-4h1v-4l1-1z" class="j"></path><path d="M222 302l1-1v1 2l1-1 1 2-1 3v2c-2-1-1-3-1-5l-1-3z" class="D"></path><path d="M224 303l1 2-1 3h-1v-4l1-1z" class="G"></path><path d="M229 286c1 0 1 1 1 1l-2 5-1 4-2 9-1-2-1 1v-2-1l-1 1-1 1h-1v-2l1-2c0-1 1-2 1-2 0-2 1-5 2-6s1-1 1-2l1-1v1h1 1l1-3z" class="H"></path><path d="M222 297h1s1 0 1-1v2c-1 0-2-1-2 1 0 1-1 3-1 4h-1v-2l1-2c0-1 1-2 1-2z" class="J"></path><path d="M224 291c1-1 1-1 1-2l1-1v1c0 1 0 2-1 3s-1 3-1 4-1 1-1 1h-1c0-2 1-5 2-6z" class="G"></path><path d="M229 286c1 0 1 1 1 1l-2 5-1 4-2 9-1-2c1-3 1-6 2-9 0-2 1-4 1-5h1l1-3z" class="P"></path><path d="M236 270c1 1 1 1 1 2h0v1h1v1l1-1s0 1 1 1v-1h1l1 2v2 3c0 1 0 2 1 4l-1-1v3c-1 1-1 2-1 3l-2 1v1 1l-1 1h-1v1c0 2 0 6-1 7l-1 1v-1h-2v-1h-1c0 1-1 1-1 1h-1v-1l-1 1h-1v-3l1-4c0-1 0-1-1-2l2-5s0-1-1-1l-1 3h-1-1v-1l-1 1c0 1 0 1-1 2 0-1 0-1-1-2 1-1 2-3 3-4-1-1-1-2 0-3l-1-1c0-2 2-4 4-5h0l2-3h2l2-1s1-1 1-2z" class="J"></path><path d="M232 277c0 1 1 1 2 2l-1 2-1-1c-1-1-1-2 0-3zm-3 9c0-1 0-1 1-1 0-1 1-3 2-3l-1 4-1 1s0-1-1-1z" class="K"></path><path d="M236 270c1 1 1 1 1 2l-1 2c0 1-1 2-1 2v1h-1l1-5s1-1 1-2z" class="G"></path><path d="M232 280c-2 0-2 3-4 4h0c0-2 1-4 3-6v-1h1c-1 1-1 2 0 3z" class="O"></path><path d="M237 272h0v1h1v1l1-1s0 1 1 1l-1 2h0c-1 0-1 0-2 1l-2-1s1-1 1-2l1-2z" class="W"></path><path d="M237 272h0v1h1v1 1c-1 0-1 0-2-1l1-2z" class="h"></path><path d="M229 276l1 1c-1 3-2 6-4 8-1-1-1-2 0-3l-1-1c0-2 2-4 4-5z" class="b"></path><path d="M237 277c1-1 1-1 2-1l-2 9h0l-1 3v-4l-2 2h-1c0 2-1 3-1 4h0c-2 2-2 2-3 4h0c0-1 0-1-1-2l2-5 1-1 1-4 1-1 1-2 1-2v-1l2 1z" class="E"></path><path d="M231 286h0c0 2-1 3-2 5h1c0-1 1-1 1-2 1-1 1-2 2-3 0 2-1 3-1 4h0c-2 2-2 2-3 4h0c0-1 0-1-1-2l2-5 1-1z" class="R"></path><path d="M237 277c1-1 1-1 2-1l-2 9h0l-1 3v-4l-2 2c0-2 1-3 1-5 0-1 0-1 1-1l1-3z" class="c"></path><path d="M235 281l1-1 1 1v3c-1 1 0 1 0 1l-1 3v-4l-2 2c0-2 1-3 1-5z" class="d"></path><path d="M240 274v-1h1l1 2v2 3c0 1 0 2 1 4l-1-1v3c-1 1-1 2-1 3l-2 1v1 1l-1 1h-1v1h-1 0v-3-3l1-3h0l2-9h0l1-2z" class="B"></path><path d="M237 285v7c1-1 2-3 2-4h0c1-4 1-8 2-11h1v3c0 1 0 2 1 4l-1-1v3c-1 1-1 2-1 3l-2 1v1 1l-1 1h-1v1h-1 0v-3-3l1-3h0z" class="T"></path><path d="M241 289c0-2-1-4 0-6v-1l1 1v3c-1 1-1 2-1 3z" class="C"></path><path d="M234 286l2-2v4 3 3h0 1c0 2 0 6-1 7l-1 1v-1h-2v-1h-1c0 1-1 1-1 1h-1v-1l-1 1h-1v-3l1-4h0c1-2 1-2 3-4h0c0-1 1-2 1-4h1z" class="K"></path><path d="M232 290h0v4l1 1h0v1 1c-1 1-1 1-2 1v-2h0c1-2 1-4 1-6z" class="O"></path><path d="M232 290c0 2 0 4-1 6v-1l-1 2c-1-1-1-2-1-3 1-2 1-2 3-4z" class="H"></path><path d="M233 296l2 1c-1 1-1 2-1 3h-1-1c0 1-1 1-1 1v-3c1 0 1 0 2-1v-1z" class="D"></path><path d="M233 296l2 1c-1 1-1 2-1 3-1 0 0 0-1-1v-2-1z" class="d"></path><path d="M229 294h0c0 1 0 2 1 3l1-2v1h0v2 3h-1v-1l-1 1h-1v-3l1-4z" class="c"></path><path d="M231 296v2 3h-1v-1l-1-1c1-1 1-2 2-3z" class="T"></path><path d="M236 291v3h0 1c0 2 0 6-1 7l-1 1v-1h-2v-1h1c0-1 0-2 1-3 0-2 0-4 1-6z" class="c"></path><path d="M212 313h0 1l1-1v2l1 1 1-2c1 1 1 2 2 2v-2h1v3l1 1h0v-1h0c1-1 2 0 2-2h0 1v3h2l5 2c1 0 2 1 2 1v1s2 0 2 1l1 1 1 1 2-1v1c0 1 1 1 1 0 0 0 1 0 1-1 0 1 1 2 2 2l-1 1v1h0v1l2 2c-1 1-2 1-3 1s-1 0-1-1l-1 1h0-4c-2 0-4 0-7 1h-1-1-3 0l-1-1v-1c-1-1-1-1-2-1h-2l-2-1-1-2c1 0 1 0 2-1l-1-1h-2l1-2-2-2c-1 0-2 1-2 1h0-1 0l1-1c0-1-1-2-1-3h1 1v-2l-1-2h2z" class="O"></path><path d="M216 320l2-1c0 2 0 2-1 2h-1v-1z" class="V"></path><path d="M215 321c0-1 0-1 1-1v1h1l-2 2h0v-2z" class="G"></path><path d="M216 313c1 1 1 2 2 2v-2h1v3h-2v1l2 1-1 1-2 1c-1 0-1 0-1 1h-1v-1h1l1-1c-1-1-1-2-2-3v-1h1l1-2z" class="L"></path><path d="M218 323v-1l1-1s0-1 1-1v-1h0v-1h2l3 3h1c-1 0-1 0-2 1l-3-1h-1c2 1 3 1 5 2v1h-3c-1-1-2-2-3-2l-1 1h0z" class="t"></path><path d="M223 317h2l5 2c1 0 2 1 2 1v1s2 0 2 1l1 1v1h-2c-1-1-2-1-3-2-1 0-1 0-1-1-2-1-6-2-7-4h1z" class="e"></path><path d="M218 323l1-1c1 0 2 1 3 2 0 1 0 1-1 2l-1 1-1 2h-2l-2-1-1-2c1 0 1 0 2-1l-1-1c1 0 2-1 3-1h0z" class="Q"></path><path d="M218 323h0v3c-1 0-1 0-2-1l-1-1c1 0 2-1 3-1z" class="V"></path><path d="M214 326c1 0 1 0 2-1 1 1 1 1 2 1l2 1-1 2h-2l-2-1-1-2z" class="G"></path><path d="M212 313h0 1l1-1v2l1 1h-1v1c1 1 1 2 2 3l-1 1h-1v1 1l-2-2c-1 0-2 1-2 1h0-1 0l1-1c0-1-1-2-1-3h1 1v-2l-1-2h2z" class="C"></path><path d="M212 313h0 1l1-1v2l1 1h-1v1c1 1 1 2 2 3l-1 1c0-1-2-1-2-1v-3s-1-1-2-1l-1-2h2z" class="j"></path><path d="M222 324h3v-1c-2-1-3-1-5-2h1l3 1h3c1 0 3 2 5 2v1c-1 1-1 0-2 0v1h3 1l-1-2h2v-1l1 1 2-1v1c0 1 1 1 1 0 0 0 1 0 1-1 0 1 1 2 2 2l-1 1v1h0v1l2 2c-1 1-2 1-3 1s-1 0-1-1l-1 1h0-4c-2 0-4 0-7 1h-1-1-3 0l-1-1v-1c-1-1-1-1-2-1l1-2 1-1c1-1 1-1 1-2z" class="I"></path><path d="M222 327c1 1 2 1 3 0h0l-1-1v-1h2v1h1s1 1 2 1c-1 0-2 1-3 1h-1 0c-1 0-2 0-3-1z" class="V"></path><path d="M226 325h4v1h3c-2 1-3 1-4 1s-2-1-2-1h-1v-1z" class="H"></path><path d="M221 326l1 1c1 1 2 1 3 1h0 1l1 1v2c-1 0-1 0-1 1h-1-3 0l-1-1v-1c-1-1-1-1-2-1l1-2 1-1z" class="O"></path><path d="M224 330c-1 0-1 0-1-1v-1c1 0 1 0 2 1l-1 1z" class="D"></path><path d="M225 328h1l1 1v2c-1 0-1 0-1 1h-1v-1h-1v-1l1-1v-1z" class="G"></path><path d="M233 324h2l-1 1c0 1 0 1 1 3h1v1 1c-1 0-1 0-2 1-2 0-4 0-7 1h-1c0-1 0-1 1-1v-2l-1-1c1 0 2-1 3-1s2 0 4-1h1l-1-2z" class="I"></path><path d="M233 328h1l1 1h-1c-2 0-3 0-4 1h0-1-1l1-1c2 0 2 0 3-1h1z" class="G"></path><path d="M235 323l1 1 2-1v1c0 1 1 1 1 0 0 0 1 0 1-1 0 1 1 2 2 2l-1 1v1h0v1l2 2c-1 1-2 1-3 1s-1 0-1-1l-1 1h0-4c1-1 1-1 2-1v-1-1h-1c-1-2-1-2-1-3l1-1v-1z" class="V"></path><path d="M235 323l1 1-1 1h-1l1-1v-1z" class="t"></path><path d="M234 325h1 2v2c0 1 0 1-1 1h-1c-1-2-1-2-1-3z" class="e"></path><path d="M241 328h-1-1-2l1-1c0-1-1-1 0-2h1 1l1 2h0v1z" class="G"></path><path d="M243 284c0 1 1 3 1 5l2 2s1 1 1 2h1v-1c1-1 2 0 3 0l3 11 1 2 1 4v1h0c3 5 2 8 3 12l2-7v-4-3h0l1-1h0c0 2 1 3 1 5l-2 7c0 1-1 2-1 3 0 2-1 6-1 8h1l-1 5v1l-1 3v4c-1 0-1 0-1-1v2l-1 2h-1c0-1 0-3-1-4v-1s-1-1-1-2 0-1-1-1c0-1 0-1-1-1v-2s0-1-1-2c0-1-1-1-2-2h-5v-1h0 0l-2-2v-1h0v-1l1-1c-1 0-2-1-2-2 0 1-1 1-1 1 0 1-1 1-1 0v-1l-2 1-1-1-1-1c0-1-2-1-2-1v-1s-1-1-2-1l-5-2h-2c0-1 1-1 1-2v-2c2-1 2-3 3-5l1-1c0-2 1-4 1-6l1-1v1h1s1 0 1-1h1v1h2v1l1-1c1-1 1-5 1-7v-1h1l1-1v-1-1l2-1c0-1 0-2 1-3v-3l1 1z" class="f"></path><path d="M242 317c-1 0-1-1-1-1 0-1 0-2 1-3v1 3z" class="V"></path><path d="M242 314h2l-1 5v-1l-1-1v-3z" class="K"></path><path d="M241 289c0-1 0-2 1-3v4c1 0 2 1 2 2 1 4 1 9 0 14v-8c-1-1-1-2-1-2l-2-3c0 3 0 7 1 10 0 3 0 5-1 7-1-2-1-3-1-5l-1-9c0 1 0 2-1 2v-3-1l-1-1h1l1-1v-1-1l2-1z" class="G"></path><path d="M242 290c1 0 2 1 2 2-1 0-1 1-1 1v1h-1v-3-1z" class="D"></path><path d="M237 293h1l1-1v4c0 1 0 2-1 2v-3-1l-1-1z" class="C"></path><path d="M241 289c0-1 0-2 1-3v4 1c-1 1-1 2-1 3-1-1-1-2-2-3v-1l2-1z" class="Z"></path><defs><linearGradient id="P" x1="237.966" y1="290.701" x2="249.198" y2="304.143" xlink:href="#B"><stop offset="0" stop-color="#5c5b5b"></stop><stop offset="1" stop-color="#939393"></stop></linearGradient></defs><path fill="url(#P)" d="M243 284c0 1 1 3 1 5l2 2s1 1 1 2h1v2l1 3h-1v3h1v4h1v3l-1 1h0l-1 2v-3c0 1-1 2-1 3v-2h-1-1v2h0-1v2-7c1-5 1-10 0-14 0-1-1-2-2-2v-4-3l1 1z"></path><path d="M248 293v2l1 3h-1c-1-1-1-3-1-5h1z" class="O"></path><path d="M245 309c0-2 0-7 1-9h1v2h1v1-2h1v4h1v3l-1 1h0l-1 2v-3c0 1-1 2-1 3v-2h-1-1z" class="K"></path><path d="M248 301h1v4h1v3l-1 1h0l-1 2v-3-1l-1-1v-4h1v1-2z" class="O"></path><path d="M249 309v-4h1v3l-1 1h0z" class="L"></path><path d="M237 293l1 1v1 3c1 0 1-1 1-2l1 9c0 2 0 3 1 5-1 4-1 7 0 12v1c1-1 2-3 2-4l1-5v-1-2h1c0 4-1 8-2 12v1l1 1c1 1 2 2 2 3h-1l-3-3c-1 0-2-1-2-2h-1c0-2 0-2-1-3 0-1-1-1-1-1h-1 0v-5h-1v-2h-1 0c0-1-1-1-1-2v-1l-1-5 1-2v-1h2v1l1-1c1-1 1-5 1-7v-1z" class="D"></path><path d="M237 301h2v9c-1-1-1-2-1-3 0-2 0-4-1-6z" class="P"></path><path d="M237 293l1 1v1 3l1 3h-2c1 2 1 4 1 6v3c-1 0-1-1-1-1v-2c0-1 0-2-1-2 0-2 0-2-1-3l1-1c1-1 1-5 1-7v-1z" class="Z"></path><path d="M238 295v3l1 3h-2v-6h1z" class="t"></path><path d="M238 307c0 1 0 2 1 3v4c1 1 0 4 0 5l-1 1c0-1-1-1-1-1h-1 0v-5h-1v-2h0l1-1h1l1-1v-3z" class="b"></path><path d="M236 314h1v5h-1 0v-5z" class="P"></path><path d="M235 312l1-1h1l1 1v2h-1-1-1v-2h0z" class="J"></path><path d="M235 312h1v2h-1v-2h0z" class="L"></path><defs><linearGradient id="Q" x1="231.904" y1="306.199" x2="237.2" y2="308.418" xlink:href="#B"><stop offset="0" stop-color="#797779"></stop><stop offset="1" stop-color="#8f8e8e"></stop></linearGradient></defs><path fill="url(#Q)" d="M233 301h2v1c1 1 1 1 1 3 1 0 1 1 1 2v2s0 1 1 1l-1 1h-1l-1 1h0-1 0c0-1-1-1-1-2v-1l-1-5 1-2v-1z"></path><path d="M233 301h2v1c1 1 1 1 1 3 0 1 0 2-1 3-1 0 0 0-1-1l1-1c0-1-1-3-1-4h-1v-1z" class="T"></path><path d="M230 300v1h1s1 0 1-1h1v1 1l-1 2 1 5v1c0 1 1 1 1 2h0 1v2h1v5h0 1s1 0 1 1c1 1 1 1 1 3h1c0 1-1 1-1 1 0 1-1 1-1 0v-1l-2 1-1-1-1-1c0-1-2-1-2-1v-1s-1-1-2-1l-5-2h-2c0-1 1-1 1-2v-2c2-1 2-3 3-5l1-1c0-2 1-4 1-6l1-1z" class="h"></path><path d="M224 313c1 1 1 2 1 3v1h-2c0-1 1-1 1-2v-2z" class="r"></path><path d="M228 312h1v2c1-1 1-2 2-2v1l-1 2h-1c-1-2-1-2-1-3z" class="Z"></path><path d="M225 316l1-1v-1h1c0 1 0 2 1 2l2 2v1l-5-2v-1zm5-9h1c0 1 1 2 2 2v1h-1l-1-1c0 1 0 3 1 4h-1v-1c-1 0-1 1-2 2v-2h-1 0c1-2 1-3 2-5z" class="D"></path><path d="M230 300v1 2 4c-1 2-1 3-2 5v-5c0-2 1-4 1-6l1-1z" class="F"></path><path d="M231 301s1 0 1-1h1v1 1l-1 2 1 5c-1 0-2-1-2-2h-1v-4-2h1z" class="c"></path><path d="M231 301s1 0 1-1h1v1 1l-1 2-1 1-1-2v-2h1z" class="j"></path><path d="M232 313c-1-1-1-3-1-4l1 1h1c0 1 1 1 1 2h0 1v2h1v5c-1-1-2-1-3-2l-2-1 1-1v-2z" class="L"></path><path d="M234 312h0 1v2c0 1 0 2-1 3h0v-5z" class="V"></path><path d="M232 313c-1-1-1-3-1-4l1 1c1 2 1 3 1 4l-1 1v-2zm3 1h1v5c-1-1-2-1-3-2h1 0c1-1 1-2 1-3z" class="r"></path><path d="M229 316l2-1v1l2 1c1 1 2 1 3 2h0 1s1 0 1 1c1 1 1 1 1 3h1c0 1-1 1-1 1 0 1-1 1-1 0v-1l-2 1-1-1-1-1c0-1-2-1-2-1v-1s-1-1-2-1v-1l-2-2h1z" class="k"></path><path d="M235 320l4 3h1c0 1-1 1-1 1 0 1-1 1-1 0v-1c-1 0-1 0-1-1-1 0-2 0-3-1l1-1z" class="f"></path><path d="M228 316h1c2 2 4 2 6 4l-1 1c1 1 2 1 3 1 0 1 0 1 1 1l-2 1-1-1-1-1c0-1-2-1-2-1v-1s-1-1-2-1v-1l-2-2z" class="O"></path><path d="M248 292c1-1 2 0 3 0l3 11 1 2 1 4v1h0c3 5 2 8 3 12l2-7v-4-3h0l1-1h0c0 2 1 3 1 5l-2 7c0 1-1 2-1 3 0 2-1 6-1 8h1l-1 5v1l-1 3v4c-1 0-1 0-1-1v2l-1 2h-1c0-1 0-3-1-4v-1s-1-1-1-2 0-1-1-1c0-1 0-1-1-1v-2s0-1-1-2c0-1-1-1-2-2h-5v-1h0 0l-2-2v-1h0v-1l1-1 3 3h1c0-1-1-2-2-3l-1-1v-1c1-4 2-8 2-12h0v-2h1 1v2c0-1 1-2 1-3v3l1-2h0l1-1v-3h-1v-4h-1v-3h1l-1-3v-2-1z" class="Z"></path><path d="M251 318c0-3 1-6 2-9v9h-2z" class="P"></path><path d="M250 299h1c0 3 1 7 0 9v-1l-1 1v-3-6z" class="b"></path><path d="M250 308l1-1v1c0 4-1 8-1 11l-1-1v-3h-1 0v-2h-1v-1-1c0-1 1-2 1-3v3l1-2h0l1-1z" class="V"></path><path d="M247 311c0-1 1-2 1-3v3l1-2h0v6h-1 0v-2h-1v-1-1z" class="D"></path><path d="M247 311c0-1 1-2 1-3v3 4h0v-2h-1v-1-1z" class="P"></path><path d="M251 318h2v4 7c1 2 1 4 1 6v6s-1-1-1-2 0-1-1-1c0-1 0-1-1-1v-2h1c-1-2-1-3-1-4-1-4 0-9 0-13z" class="p"></path><path d="M248 292c1-1 2 0 3 0l3 11 1 2c-1 0-2 0-2 1l-1-1c-1-2 0-5-1-8 0 1-1 1-1 2v6h-1v-4h-1v-3h1l-1-3v-2-1z" class="C"></path><path d="M248 295c1 1 1 2 2 4v-3h1v1c0 1-1 1-1 2v6h-1v-4h-1v-3h1l-1-3z" class="D"></path><path d="M248 298h1v3h-1v-3z" class="r"></path><path d="M246 309h1v2 1 1h1v2h0 1v3l1 1c-1 2-1 4-1 6s0 4 1 5l1 1c0 1 0 2 1 4h-1s0-1-1-2c0-1-1-1-2-2h-5v-1h0 0l-2-2v-1h0v-1l1-1 3 3h1c0-1-1-2-2-3l-1-1v-1c1-4 2-8 2-12h0v-2h1z" class="P"></path><path d="M246 309h1v2 1l-1 1v-4z" class="G"></path><path d="M247 312v1c0 1-1 3-1 4v1l-1-1c0-1 1-3 1-4l1-1z" class="V"></path><path d="M245 317l1 1c0 1 0 4-1 6h-1v-1c1-2 1-4 1-6z" class="G"></path><path d="M246 317h1c0 1 0 2 1 2l-1 3v6h0-1c0-1-1-2-2-3 0-1 1-1 1-1 1-2 1-5 1-6v-1z" class="Q"></path><path d="M247 313h1v2h0 1v3l1 1c-1 2-1 4-1 6h-1v1l-1-4 1-3c-1 0-1-1-1-2h-1c0-1 1-3 1-4z" class="L"></path><path d="M247 313h1v2 4c-1 0-1-1-1-2h-1c0-1 1-3 1-4z" class="I"></path><path d="M248 325v-4l1-3 1 1c-1 2-1 4-1 6h-1z" class="t"></path><path d="M247 322l1 4v-1h1c0 2 0 4 1 5l1 1c0 1 0 2 1 4h-1s0-1-1-2c0-1-1-1-2-2h-5v-1h0 0l-2-2v-1h0v-1l1-1 3 3h1 1 0v-6z" class="O"></path><path d="M241 327v-1l1-1 3 3 2 2h-1 0-1-2 0l-2-2v-1h0z" class="P"></path><path d="M241 327c2 1 3 2 4 3h-2 0l-2-2v-1z" class="C"></path><path d="M253 306c0-1 1-1 2-1l1 4v1h0c3 5 2 8 3 12l2-7v-4-3h0l1-1h0c0 2 1 3 1 5l-2 7c0 1-1 2-1 3 0 2-1 6-1 8h1l-1 5v1l-1 3v4c-1 0-1 0-1-1v2l-1 2h-1c0-1 0-3-1-4v-1-6c0-2 0-4-1-6v-7-14-2z" class="h"></path><path d="M255 330c0-1 0-3 1-5h1l1 1-2 3 1 1h-2z" class="T"></path><path d="M259 324s1-1 1-2c0 2-1 6-1 8h1l-1 5-1-1h0v-2-1-1h-1l-1-1 2-3h0s1-1 1-2z" class="p"></path><path d="M258 326h0s1-1 1-2c0 3 0 5-1 7v-1h-1l-1-1 2-3z" class="D"></path><path d="M253 306c0-1 1-1 2-1l1 4v1c0 1-1 2 0 3h0l-1 1h0-1c-1-1-1-4-1-6v-2z" class="W"></path><path d="M257 330h1v1 1 2h0l1 1v1l-1 3v4c-1 0-1 0-1-1v2l-1 2h-1c0-1 0-3-1-4v-1-6c0-1 0-3 1-3v-2h2z" class="c"></path><path d="M257 330h1v1 1h-3 0v-2h2z" class="k"></path><path d="M258 334h0l1 1v1l-1 3v4c-1 0-1 0-1-1 0-3 0-6 1-8z" class="f"></path><path d="M202 319l2 1v-1l1 1-1 1s0 1 1 2c0 0 0 1 1 1h0v1l3-4h1 0s1-1 2-1l2 2-1 2h2l1 1c-1 1-1 1-2 1l1 2 2 1h2c1 0 1 0 2 1v1l1 1h0 3 1 1c3-1 5-1 7-1h4 0v2l1 2h0 3 0 2l-1 1h1c-1 1-1 1-1 2l-1 2h0v3 2 3h-1l-1 1c0 2 0 3 1 5v1h-1v2h-1-2c-1 0-1 0-2 1h-1 0c-2 0-2 1-3 2l-2 3h0l-2-1-1 4v1l-2 5 5 3-1 1-7-4-1-1h0c-1 1-1 1-2 1h-1c-1 0-1-1-1-1l-2-2-5-1h-4l-5 1h-2 0c-1 0-2 0-3 1h-1l-3 2-2 1-1 1h-1l-1 1-1 1-3 3h-1-2v1c-1-1-1-1-2-1h0c0-2 1-6 2-7v-2h-1l1-1-1-1v-1-4c0-2 1-5 3-7l2-5h0l3-4h1l-1 2 2 1v-1c0-1 1-2 1-2l1-2h-1l2-1v-2-1c1 1 1 1 1 2h1v-2l1-2 1-1c1-2 0-3 1-5h0c1-2 2-5 3-7l1-2c0-1 1-3 1-4l1-1z" class="O"></path><path d="M204 360c1-4 2-8 4-12l1 1c-2 3-3 6-4 10 0 1-1 2-1 3v1 5h-1v-1-6c1 0 1-1 1-1z" class="g"></path><path d="M203 355c1-3 2-6 4-9h0c-1 4-3 8-4 13l1 1s0 1-1 1v6l-1 1v-1-1-1-4c-1 1-1 4-1 5h-1v-1h-1v-3-2-2c1 1 1 2 2 2l2-5z" class="B"></path><path d="M199 358c1 1 1 2 2 2v3h-1c0-1 0-1-1-1v-2-2z" class="E"></path><path d="M210 336h2l1 1h-1 0c-1 1-2 2-3 4h1l1-1v1c0 1-1 2-1 3s0 0-1 1c0 0-1 1-2 1h0c-2 3-3 6-4 9l-1-1c0-3 3-5 3-8 0-2 2-4 3-6h-1 0l1-2 2-2z" class="Z"></path><path d="M210 352c2 2 1 3 1 5s-1 5-2 7v2 2h-4l-5 1h-2-1c1-1 1-2 1-3h1v-1h1v1h1c0-1 0-4 1-5v4 1 1 1l1-1v1h1v-5-1c0-1 1-2 1-3h1c0-1 1-2 1-3l1-2h2v-2z" class="U"></path><path d="M206 362v-1h1v1h-1z" class="w"></path><path d="M206 362h1c0 1 0 2-1 2 0-1 0-1-1-1l1-1z" class="g"></path><path d="M205 363c1 0 1 0 1 1v2 1l-1 1v-5z" class="x"></path><path d="M199 366v2l1 1h-2-1c1-1 1-2 1-3h1z" class="m"></path><path d="M208 364v1l1-1v2 2h-4 0l1-1v-1c0 1 0 1 1 1 1-1 0-2 1-3z" class="v"></path><path d="M210 352c2 2 1 3 1 5s-1 5-2 7l-1 1v-1l1-5h-1c0-2 0-2-1-3l1-2h2v-2z" class="g"></path><path d="M208 354h2l-1 5h-1c0-2 0-2-1-3l1-2z" class="u"></path><path d="M215 328l2 1h2c1 0 1 0 2 1v1l1 1h-1v1l1 1c0 1-1 2-2 3 0 1-1 2-1 3v1h0v1c-1 0-1 0-2 1h0c-1 0-1 0-1 1l-1 1c0 1 0 2-1 3h-1v2 1 1 1c-1 1-1 2-2 4 0-2 1-3-1-5v2h-2l-1 2c0 1-1 2-1 3h-1c1-4 2-7 4-10 0-1 1-2 1-2v-1c1-2 1-3 3-5 1 0 1 0 2-1h0l-1-1-1 1-1-1v-2h1l-1-1h1l-1-2v-1s-1-1-1-2c1-1 2-2 4-3z" class="G"></path><path d="M208 354l1-3h2l-1 1v2h-2z" class="AC"></path><path d="M212 348c1-1 1-2 2-3h1c0 1 0 2-1 3h-1v2 1-3h-1z" class="C"></path><path d="M212 348h1v3 1 1c-1 1-1 2-2 4 0-2 1-3-1-5l1-1c0-1 1-2 1-3z" class="B"></path><path d="M215 340h1c-1 1-1 2-2 3v1l-1-1c-1 1-2 3-3 4v-1c1-2 1-3 3-5 1 0 1 0 2-1h0z" class="D"></path><path d="M221 333l1 1c0 1-1 2-2 3v-1c-1 1-2 2-2 3l-1 1-1-1 2-4v-2h3z" class="r"></path><path d="M215 328l2 1h2c1 0 1 0 2 1v1l1 1h-1v1h-3v2l-2 4v1h-1l-1-1-1 1-1-1v-2h1l-1-1h1l-1-2v-1s-1-1-1-2c1-1 2-2 4-3z" class="Z"></path><path d="M214 332h0c1 1 2 1 3 1v2l-3-1c0 1-1 1-1 2l-1-2v-1l2-1z" class="J"></path><path d="M215 328l2 1v1l-3 2-2 1s-1-1-1-2c1-1 2-2 4-3z" class="u"></path><path d="M213 336c0-1 1-1 1-2l3 1h1l-2 4v1h-1l-1-1-1 1-1-1v-2h1l-1-1h1z" class="H"></path><path d="M211 331c0 1 1 2 1 2v1l1 2h-1-2l-2 2-1 2h0 1c-1 2-3 4-3 6 0 3-3 5-3 8l1 1-2 5c-1 0-1-1-2-2v2 2 3 1h-1c0 1 0 2-1 3h1 0c-1 0-2 0-3 1h-1c-1 0-1 0-1-1 0-3 1-5 1-8 0-2 0-4 1-5v-2l1-2c1-1 2-2 3-4h0c1-1 2-2 2-4l1-2h-1c1-1 1-2 3-3-1 0-1 0-2-1l3-2h0l1-1c2-1 2-1 4-3h0l1-1z" class="T"></path><path d="M207 340h1c-1 2-3 4-3 6-2 3-4 6-5 9v-3c2-1 3-6 4-8 1-1 2-3 3-4z" class="K"></path><path d="M205 346c0 3-3 5-3 8l1 1-2 5c-1 0-1-1-2-2l1-3c1-3 3-6 5-9z" class="C"></path><path d="M200 352v3l-1 3v2 2 3 1h-1v-1-6-1h-1l-1 1-1-1c1-1 1-1 1-2h1l1-3h0v1c0 1 0 1 1 2 0-2 0-3 1-4z" class="R"></path><path d="M200 352v3l-1 3v2h-1c0-2 0-3 1-4 0-2 0-3 1-4z" class="G"></path><path d="M211 331c0 1 1 2 1 2v1l1 2h-1-2l-2 2c-1 0-2 0-3 1v1l-2 2v-1l1-2c-1 0-1 0-2-1l3-2h0l1-1c2-1 2-1 4-3h0l1-1z" class="F"></path><path d="M211 331c0 1 1 2 1 2v1h-1l-1-2h0l1-1z" class="z"></path><path d="M211 334h1l1 2h-1-2c0-1 1-1 1-2z" class="C"></path><path d="M195 354c1 0 1 1 1 2s0 1-1 2l1 1 1-1h1v1 6 1c0 1 0 2-1 3h1 0c-1 0-2 0-3 1h-1c-1 0-1 0-1-1 0-3 1-5 1-8 0-2 0-4 1-5v-2z" class="u"></path><path d="M197 364l1 1v1c0 1 0 2-1 3l-1-1c1-1 1-2 1-4z" class="x"></path><path d="M197 364l-1-1c0-1 0-3 1-4h1v6l-1-1z" class="m"></path><path d="M195 354c1 0 1 1 1 2s0 1-1 2c0 4-1 8 0 12h-1c-1 0-1 0-1-1 0-3 1-5 1-8 0-2 0-4 1-5v-2z" class="v"></path><path d="M202 319l2 1v-1l1 1-1 1s0 1 1 2c0 0 0 1 1 1h0v1l3-4h1 0s1-1 2-1l2 2-1 2h2l1 1c-1 1-1 1-2 1l1 2c-2 1-3 2-4 3l-1 1h0c-2 2-2 2-4 3l-1 1h0l-3 2c1 1 1 1 2 1-2 1-2 2-3 3s-2 2-3 2c1-1 1-2 1-3h-2-1-1v-3c1-2 0-3 1-5h0c1-2 2-5 3-7l1-2c0-1 1-3 1-4l1-1z" class="t"></path><path d="M199 332c1-1 2-2 3-4v1h0c-1 3-2 5-4 7h-1c0-1 1-2 2-3v-1z" class="I"></path><path d="M199 326h1c0 2-1 4-1 6v1c-1 1-2 2-2 3h1l-1 1-1 1c-1-1 1-2 1-3 0 0-1-1-1-2h0c1-2 2-5 3-7z" class="P"></path><path d="M202 319l2 1v-1l1 1-1 1s0 1 1 2c0 0 0 1 1 1h0c-1 1-1 2-2 3 0 1 0 2-1 3l-1-1h0v-1c-1 2-2 3-3 4 0-2 1-4 1-6h-1l1-2c0-1 1-3 1-4l1-1z" class="G"></path><path d="M202 319l2 1v-1l1 1-1 1-1-1v2l-1 2-1 1-1-1c0-1 1-3 1-4l1-1z" class="O"></path><path d="M202 324h1l1 1c0 1-1 3-2 4v-1c-1 2-2 3-3 4 0-2 1-4 1-6h-1l1-2 1 1 1-1z" class="K"></path><path d="M209 321h1v2c0 2-3 3-3 6 0 2-1 3-2 4v2 1h0l-3 2c1 1 1 1 2 1-2 1-2 2-3 3s-2 2-3 2c1-1 1-2 1-3h-2c0-1 1-2 2-3 1-3 4-6 6-9l1-4 3-4z" class="D"></path><path d="M199 338c1 0 2-1 3-1 1-1 1-2 2-3h0v2h1l-3 2c1 1 1 1 2 1-2 1-2 2-3 3s-2 2-3 2c1-1 1-2 1-3h-2c0-1 1-2 2-3z" class="c"></path><path d="M200 339c1-1 1 0 2-1 1 1 1 1 2 1-2 1-2 2-3 3s-2 2-3 2c1-1 1-2 1-3 0 0 0-1 1-2z" class="m"></path><path d="M200 339v3h1c-1 1-2 2-3 2 1-1 1-2 1-3 0 0 0-1 1-2z" class="R"></path><path d="M212 320l2 2-1 2h2l1 1c-1 1-1 1-2 1l1 2c-2 1-3 2-4 3l-1 1h0c-2 2-2 2-4 3l-1 1v-1-2c1-1 2-2 2-4 0-3 3-4 3-6v-2h0s1-1 2-1z" class="L"></path><path d="M209 328c1-1 1-1 3-1v1s-1 0-1 1h-2v-1z" class="h"></path><path d="M212 320l2 2-1 2h0c-1 1-1 1-2 1v-1c1-1 1-1 1-2v-2z" class="T"></path><path d="M210 321h0s1-1 2-1v2l-3 3-1 3-1 1c0-3 3-4 3-6v-2z" class="k"></path><path d="M213 324h2l1 1c-1 1-1 1-2 1l1 2c-2 1-3 2-4 3l-1 1-1-1c1 0 1-1 2-1v-1c0-1 1-1 1-1v-1-1l-1-1c1 0 1 0 2-1h0z" class="D"></path><path d="M213 324h2l1 1c-1 1-1 1-2 1 0 0-1-1-1-2h0z" class="t"></path><path d="M208 328h1v1h2v1c-1 0-1 1-2 1l1 1h0c-2 2-2 2-4 3l-1 1v-1-2c1-1 2-2 2-4l1-1z" class="J"></path><path d="M206 335l1-2c0-1 0-2 1-3l1 1 1 1h0c-2 2-2 2-4 3z" class="h"></path><path d="M195 338v3h1 1 2c0 1 0 2-1 3 1 0 2-1 3-2h1l-1 2c0 2-1 3-2 4h0c-1 2-2 3-3 4l-1 2v2c-1 1-1 3-1 5 0 3-1 5-1 8 0 1 0 1 1 1l-3 2-2 1-1 1h-1l-1 1-1 1-3 3h-1-2v1c-1-1-1-1-2-1h0c0-2 1-6 2-7v-2h-1l1-1-1-1v-1-4c0-2 1-5 3-7l2-5h0l3-4h1l-1 2 2 1v-1c0-1 1-2 1-2l1-2h-1l2-1v-2-1c1 1 1 1 1 2h1v-2l1-2 1-1z" class="F"></path><path d="M182 359h1c0 3-1 6-1 9h0v-2l-1-1v-1c0-2 1-3 1-5z" class="W"></path><path d="M186 349l2 1-5 9h-1c1-2 2-3 3-5 0-1 1-4 1-5z" class="S"></path><path d="M179 368c1-1 1-2 2-4v1l1 1v2c-1 4-2 7-3 11v1c-1-1-1-1-2-1h0c0-2 1-6 2-7v-2h-1l1-1v-1z" class="C"></path><path d="M179 372l1-1c-1 3-1 6-3 8 0-2 1-6 2-7z" class="F"></path><path d="M179 368c1-1 1-2 2-4v1c0 2 0 4-1 6l-1 1v-2h-1l1-1v-1z" class="i"></path><path d="M183 351h0l3-4h1l-1 2c0 1-1 4-1 5-1 2-2 3-3 5 0 2-1 3-1 5-1 2-1 3-2 4v1l-1-1v-1-4c0-2 1-5 3-7l2-5z" class="B"></path><path d="M178 366l1 2v1l-1-1v-1-1z" class="v"></path><path d="M181 356v3c0 2-2 5-3 7v1-4c0-2 1-5 3-7z" class="m"></path><path d="M191 359v-2c1 1 1 1 1 2h0l2 2c0 3-1 5-1 8 0 1 0 1 1 1l-3 2-2 1-1 1h-1l-1 1-1 1c-1 0-1 1-2 1h0l-1-1v1h-1c1-3 2-7 2-10 0-1 1-2 1-3v-5h1v2h1 0l1 1h0 1 0l2 1 1-4z" class="l"></path><path d="M186 362v3h1l-1 1v1c-1-1-1-3-1-4l1-1z" class="D"></path><path d="M186 361l1 1h0 1 0v3h-1-1v-3-1z" class="j"></path><path d="M186 361l1 1c0 1 0 2-1 3v-3-1z" class="L"></path><path d="M183 367c0-1 1-2 1-3v-5h1v2h1 0v1l-1 1h0v6h-1v-2h-1z" class="R"></path><path d="M187 365h1c-1 3-1 6-1 9l-1 1-1 1c-1 0-1 1-2 1h0l-1-1c1-1 1-2 2-3 2-2 2-4 2-7l1-1z" class="B"></path><path d="M188 362l2 1 1 1c-1 3-2 5-2 9h0l-1 1h-1c0-3 0-6 1-9v-3z" class="C"></path><path d="M191 359v-2c1 1 1 1 1 2h0l2 2c0 3-1 5-1 8 0 1 0 1 1 1l-3 2-2 1h0c0-4 1-6 2-9l-1-1 1-4z" class="i"></path><path d="M191 372v-3h0 2c0 1 0 1 1 1l-3 2z" class="R"></path><path d="M191 359v-2c1 1 1 1 1 2h0c0 2-1 3-1 5l-1-1 1-4z" class="H"></path><path d="M195 338v3h1 1 2c0 1 0 2-1 3 1 0 2-1 3-2h1l-1 2c0 2-1 3-2 4h0c-1 2-2 3-3 4l-1 2v2c-1 1-1 3-1 5l-2-2h0c0-1 0-1-1-2v2l-1 4-2-1h0-1 0l-1-1h0c1-4 2-7 3-10 0-1 0-2-1-2 0-1 1-2 1-2l1-2h-1l2-1v-2-1c1 1 1 1 1 2h1v-2l1-2 1-1z" class="Z"></path><path d="M196 347c0 1 0 1 1 1v1l-1 1-1-1v-1l1-1zm-3 8l1-3h2l-1 2v2h0c-1-1-1-1-2-1z" class="H"></path><path d="M193 347l2 1v1l-1 1h-1l-1-2 1-1z" class="F"></path><path d="M201 342h1l-1 2h0-1 0c0 1-1 2-1 2-1 0-1 0-1-1v-1c1 0 2-1 3-2z" class="H"></path><path d="M193 355c1 0 1 0 2 1h0c-1 1-1 3-1 5l-2-2h0l1-4z" class="AC"></path><path d="M195 338v3h1 1 2c0 1 0 2-1 3v1l-2 2-1 1-2-1v-1h0c-1-1 0-2 0-3v-2l1-2 1-1z" class="i"></path><path d="M195 338v3h1l-1 2c-1 1-1 2-2 3h0c-1-1 0-2 0-3v-2l1-2 1-1z" class="J"></path><path d="M195 338v3h1l-1 2h-1v-4l1-1z" class="G"></path><path d="M191 341c1 1 1 1 1 2h1c0 1-1 2 0 3h0v1l-1 1 1 2h1l-3 7v2l-1 4-2-1h0-1 0l-1-1h0c1-4 2-7 3-10 0-1 0-2-1-2 0-1 1-2 1-2l1-2h-1l2-1v-2-1z" class="h"></path><path d="M190 345v2 2l-1 2c0-1 0-2-1-2 0-1 1-2 1-2l1-2z" class="m"></path><path d="M190 345v2 2c0-1-1-1-1-2l1-2z" class="j"></path><path d="M189 356v2c0 1 0 3-1 4h-1c0-2 1-4 2-6z" class="i"></path><path d="M189 358h1v1h1l-1 4-2-1h0c1-1 1-3 1-4z" class="T"></path><path d="M191 341c1 1 1 1 1 2h1c0 1-1 2 0 3h0v1l-1 1-1 2v-3h-1v-2h-1l2-1v-2-1z" class="L"></path><path d="M191 344h1l-1 3h-1v-2h-1l2-1z" class="R"></path><path d="M191 341c1 1 1 1 1 2v1h-1v-2-1z" class="S"></path><path d="M191 350l1-2 1 2h1l-3 7v2h-1v-1h-1v-2l2-6z" class="E"></path><path d="M234 331h4 0v2l1 2h0 3 0 2l-1 1h1c-1 1-1 1-1 2l-1 2h0v3 2 3h-1l-1 1c0 2 0 3 1 5v1h-1v2h-1-2c-1 0-1 0-2 1h-1 0c-2 0-2 1-3 2l-2 3h0l-2-1-1 4v1l-2 5 5 3-1 1-7-4-1-1h0c-1 1-1 1-2 1h-1c-1 0-1-1-1-1l-2-2-5-1v-2-2c1-2 2-5 2-7 1-2 1-3 2-4v-1-1-1-2h1c1-1 1-2 1-3l1-1c0-1 0-1 1-1h0c1-1 1-1 2-1v-1h0v-1c0-1 1-2 1-3 1-1 2-2 2-3l-1-1v-1h1 0 3 1 1c3-1 5-1 7-1z" class="T"></path><path d="M222 332h3 1 1v2h-1c-1-1-2-2-4-2z" class="J"></path><path d="M223 343c2-3 2-7 4-9 0 5-2 10-4 14 0 1-1 2-1 2v-1c0-1 0-2 1-3v-3z" class="K"></path><path d="M219 341c1-2 2-6 4-7h1c-1 2-2 3-2 5-1 1-1 3-2 4l-1 2c-1 2-1 4-2 6v1h-1c0-1 1-5 2-6 0-2 0-3 1-4v-1h0z" class="t"></path><path d="M234 331h4 0v2h0l-1 1h0c-2 1-2 1-2 2h-3l-1-1c-2 0-2 0-4-1h0v-2c3-1 5-1 7-1z" class="G"></path><path d="M235 332c1 0 2 0 3-1v2h0l-3-1z" class="H"></path><path d="M229 333l1-1h5c-1 1-2 1-2 1h-4z" class="K"></path><path d="M229 333h4l-1 3-1-1c-2 0-2 0-4-1 1 0 2-1 2-1z" class="O"></path><path d="M235 332l3 1-1 1h0c-2 1-2 1-2 2h-3l1-3s1 0 2-1h0z" class="b"></path><path d="M228 337l1 1c-1 1-1 3-1 5h0 0c1 0 1 0 2 1l-3 6c0 2-1 3-2 5v-3h0l-1-1c0 2-1 4-2 5h-1l7-19z" class="f"></path><path d="M228 343c1 0 1 0 2 1l-3 6c0 2-1 3-2 5v-3h0c1-1 2-3 2-4 0-2 1-3 1-5z" class="M"></path><path d="M228 337v-1c1 0 1 1 2 1s1 0 3 1v1 2 2l-1 1c0 2-1 5-2 8-1 1-1 2-1 3v2l-1 1v-1-2c-1-1 0-3 1-4h-1c0-1 0-1-1-1l3-6c-1-1-1-1-2-1h0 0c0-2 0-4 1-5l-1-1z" class="J"></path><path d="M228 343c1-1 1-2 2-4 0-1 1-2 1-2l-1 5v2c-1-1-1-1-2-1h0z" class="p"></path><path d="M230 342c1 2 1 3 0 5l-1 4h-1c0-1 0-1-1-1l3-6v-2z" class="L"></path><path d="M220 343h0c3-3 3-7 6-9l-3 8v1 3c-1 1-1 2-1 3v1l-1 4-1-1h0l-1-1-1 4c-1-1-1 0-1-1s0-2 1-3l-1-1c1-2 1-4 2-6l1-2z" class="O"></path><path d="M222 349v1l-1 4-1-1 2-4z" class="V"></path><path d="M218 352c1-3 2-5 4-7h0c0 1 0 1-1 2-1 2-1 3-2 5l-1 4c-1-1-1 0-1-1s0-2 1-3z" class="K"></path><path d="M227 350c1 0 1 0 1 1h1c-1 1-2 3-1 4v2 1l1-1c0 1-1 4-2 5l-1 4v1l-2 5 5 3-1 1-7-4-1-1 1-1c-1 0-1 0-1-1v-1l-1 1h-1c0-1-1-1-1-1l1-3c1-2 2-5 3-7 0-1 1-1 1-2 1-1 2-3 2-5l1 1h0v3c1-2 2-3 2-5z" class="h"></path><path d="M225 355c0 2-2 4-4 5 2-2 2-5 4-8v3z" class="Q"></path><path d="M223 365c0-2 1-5 3-7 0 2-1 6-2 8l-1-1z" class="Y"></path><path d="M221 358v2c-1 2-1 4-2 5 0 2-1 3-1 4 0-1-1-1-1-1l1-3c1-2 2-5 3-7z" class="U"></path><path d="M229 357c0 1-1 4-2 5l-1 4v1l-1-1c0-1 0-3 1-4 1-2 1-3 2-4h0l1-1z" class="v"></path><path d="M223 365l1 1-1 5h0c2-1 2-3 2-5l1 1-2 5 5 3-1 1-7-4-1-1 1-1h1c0-1 0-3 1-5z" class="B"></path><path d="M217 343c1-1 1-1 2-1-1 1-1 2-1 4-1 1-2 5-2 6h1v-1l1 1c-1 1-1 2-1 3s0 0 1 1l1-4 1 1h0l1 1-1 3h0l1-1h1c0 1-1 1-1 2-1 2-2 5-3 7l-1 3s1 0 1 1h1l1-1v1c0 1 0 1 1 1l-1 1h0c-1 1-1 1-2 1h-1c-1 0-1-1-1-1l-2-2-5-1v-2-2c1-2 2-5 2-7 1-2 1-3 2-4v-1-1-1-2h1c1-1 1-2 1-3l1-1c0-1 0-1 1-1h0z" class="W"></path><path d="M209 366c2 0 3 0 5 1 0-1 1-2 2-2 0 1-1 2 0 3h1s1 0 1 1h1l1-1v1c0 1 0 1 1 1l-1 1h0c-1 1-1 1-2 1h-1c-1 0-1-1-1-1l-2-2-5-1v-2z" class="g"></path><path d="M214 369c2 1 4 1 6 2-1 1-1 1-2 1h-1c-1 0-1-1-1-1l-2-2z" class="K"></path><path d="M219 352l1 1h0l1 1-1 3h0l1-1h1c0 1-1 1-1 2-1 2-2 5-3 7h-1c-1-1 1-4 1-5v-2c-1 1-1 3-1 4l-3 3c1-1 1-3 2-5 0-2 0-3 1-5 0 1 0 0 1 1l1-4z" class="J"></path><path d="M220 353h0l1 1-1 3h-1 0c-1-1 0-3 1-4z" class="K"></path><path d="M217 343c1-1 1-1 2-1-1 1-1 2-1 4-1 1-2 5-2 6h1c-1 2-2 4-2 7l-1 3h-1c0-2-1-4-1-6 1-1 1-2 1-3h0v-1-1-1-2h1c1-1 1-2 1-3l1-1c0-1 0-1 1-1h0z" class="O"></path><path d="M214 349l2-1c-1 1-1 2-1 3h-1v-2z" class="I"></path><path d="M215 345l1-1c0-1 0-1 1-1h0l-1 5-2 1v-1c1-1 1-2 1-3z" class="K"></path><path d="M213 353v-1 2h1c0-1 0-1 1-2v1c0 1 0 2-1 2v5h0l1-1-1 3h-1c0-2-1-4-1-6 1-1 1-2 1-3h0z" class="J"></path><path d="M238 333h0l1 2h0 3 0 2l-1 1h1c-1 1-1 1-1 2l-1 2h0v3 2 3h-1l-1 1c0 2 0 3 1 5v1h-1v2h-1-2c-1 0-1 0-2 1h-1 0c-2 0-2 1-3 2l-2 3h0l-2-1c1-1 2-4 2-5v-2c0-1 0-2 1-3 1-3 2-6 2-8l1-1v-2-2-1-1h2v-1c0-1 0-1 2-2h0l1-1z" class="B"></path><path d="M236 356c1 0 1 0 1 1-1 0-1 0-2 1h-1 0l2-2z" class="s"></path><path d="M237 350v2c1 0 1-1 1-1v1c0 1-1 2-2 3h0-1c1-2 2-3 2-5z" class="w"></path><path d="M238 347l2-2h2v3h-1l-1 1c-1 1-1 2-2 3v-1s0 1-1 1v-2h1v-3h0z" class="Y"></path><path d="M238 352c1-1 1-2 2-3 0 2 0 3 1 5v1h-1v2h-1-2c0-1 0-1-1-1v-1h0c1-1 2-2 2-3z" class="R"></path><path d="M236 355h1s1 0 2-1v1h1v2h-1-2c0-1 0-1-1-1v-1h0z" class="l"></path><path d="M235 343v1 1l-1 2c1 1 2 2 2 3 0 2-3 5-4 6-2 2-3 4-3 6v1h0l-2-1c1-1 2-4 2-5v-2c0-1 0-2 1-3 1-3 2-6 2-8l1-1h2z" class="C"></path><path d="M235 343v1 1l-1 1v-2h-2l1-1h2z" class="k"></path><path d="M234 347c1 1 2 2 2 3-2 0-2 0-3 1s-2 2-2 3v1h-1 0c1-4 3-5 4-8z" class="O"></path><path d="M231 354c0-1 1-2 2-3s1-1 3-1c0 2-3 5-4 6h-1l1-1-1-1z" class="D"></path><path d="M238 333h0l1 2h0 3 0 2l-1 1h1c-1 1-1 1-1 2l-1 2h0v3 2h-2l-2 2h0l-2 3c0-1-1-2-2-3l1-2v-1-1h-2v-2-2-1-1h2v-1c0-1 0-1 2-2h0l1-1z" class="R"></path><path d="M233 339v1l1-1h1 0c0 1-1 2-2 2v-2z" class="W"></path><path d="M241 337l2 1-1 2h0-1l-1-1c1-1 1-1 1-2z" class="F"></path><path d="M235 343c0-1 1-3 2-4h0v2h0v1c-1 0-2 1-2 2v-1z" class="h"></path><path d="M239 335h3 0 2l-1 1h1c-1 1-1 1-1 2l-2-1h0-3l1-2z" class="t"></path><path d="M241 337v-1h2 1c-1 1-1 1-1 2l-2-1h0z" class="H"></path><path d="M238 333h0l1 2h0l-1 2h-3v-1c0-1 0-1 2-2h0l1-1z" class="r"></path><path d="M237 341l1-2h1 1l1 1h1 0 0v3 2h-2l-2 2h0l-2 3c0-1-1-2-2-3l1-2v-1c0-1 1-2 2-2v-1z" class="D"></path><path d="M239 339h1l1 1v2c-2-1-2-1-2-3z" class="C"></path><path d="M241 340h1 0 0v3 2h-2l-2 2 1-2c1-1 1-2 2-3v-2z" class="i"></path><path d="M376 470h1c0 2 0 4-1 6 1 0 1 1 2 1v1c0 1 0 1 1 2l1 1v1 1l-3 2h-2c-1 0-1 0-1 1l-1 1h3l1 1h0l1 1h1v1h2c-1 0-1 0-2 1l-2 3c1 2 1 3 1 5 0 1 1 3 1 4 1 0 1 1 2 1v1l1 1 3 3h-3c1 1 2 1 2 2 0 2 2 5 4 6 0 1 1 1 1 1l1 1c-1 1-1 1-2 1v-1h-1c0 1 0 4 1 5v1 10 1 2 1c-1 0-1 0-2 1v2l2 7 1 3c0 2 2 4 2 6 1 0 2-1 3-2v1c-1 1-1 2-1 3h1v8c0 1 1 1 1 1l1-1v1 1h1c0 1 0 1 1 1v2h1l-4 3h3c1-1 2-1 3-2 1 0 1-1 2-1 0 1 1 1 1 2-1 3-2 6-4 8-1 2-3 4-5 6l-1 1-1-1c-1 0-2 1-2 1-4 2-8 4-12 5l1-1c1 0 1 0 2-1h1 0-3-2 0c-12 2-24 4-36 4h-8v-3c-1 2-2 3-4 4h-3 0 1c-2 1-5 0-7 0 1-1 1-1 1-2v-1 1c-1 1-2 1-4 2-1-1-1-1-1-2v1c-3 2-18 4-22 5h-4l-5 1c-1-1-1-2-1-3h-4-12v-1h3 0c1 0 1-1 2-1h0 0c-1 0-1-1-2 0h-4v-2c-13 3-27 2-40 3-3 0-7-1-10-1l-12-1c-2-1-5 0-7-1h5-1c2-2 1-5 1-7h1 1v-2h1 1 4 0l2-1h1 0l3-1h8l6-1h2l7-2 9-2c1-1 3-2 4-2l11-4 3-1 8-5h0l1-3c2-1 4-3 6-5 6-4 13-9 19-14l6-6c1-2 3-3 5-5h-1c0-2 0-2 1-3h0c1-2 3-3 5-5 3-2 6-5 8-7h4c2-1 5-3 6-5 2-1 3-2 4-4 5-3 9-6 14-10l6-6h1v2h-1l1 1c1-1 1-1 1-2h1c-1-2-2-2-3-3l1-1c3-2 6-6 8-9 3-4 5-9 7-13z"></path><path d="M249 592h1v1c-1 1-2 2-4 2 0-1 1-1 1-1 1-1 1-2 2-2z" class="R"></path><path d="M349 515l5-1v1 1c-1 1-1 2-1 2v1-3h-1v4h0c-1-2 0-3 0-5h-3z" class="AA"></path><path d="M352 520h0v-4h1v3 1h1v2l-1 1h-1v-3z" class="x"></path><path d="M308 572h0 5c-2 1-7 4-10 4h0c1-2 3-3 5-4zm-9-16l1 1-1 1c-1 5-3 7-7 10 0-2 2-4 3-5 1-3 2-5 4-7z" class="n"></path><path d="M308 572c-2 0-5 2-8 3 2-3 6-5 9-7 1 0 3 1 4 1l1 1h0l1 1h-1l-1 1h-5 0z" class="M"></path><path d="M208 587h4c-1 0-1 1-1 1l-1 3c0 1 0 3 1 3h0l1 1-1 1c-2 0-2 0-3-2h0l-1 1h-1c-1-1-1-3-1-4 0 1 0 3-1 5h1 2 1c1 0 2 1 4 1h1 3l2 1-12-1c-2-1-5 0-7-1h5-1c2-2 1-5 1-7h1 1v-2h1 1z" class="g"></path><path d="M205 589h1 1v6c-1-2-2-4-2-6z" class="j"></path><path d="M208 590h1v1h1c0 1 0 3 1 3l-1 1-2-1v-4z" class="F"></path><path d="M208 587h4c-1 0-1 1-1 1l-1 3h-1v-1h-1 0v-1h-1 0-1v-2h1 1z" class="U"></path><path d="M329 542l5 2v-1c-1-2 1-7 2-9 1-1 3-3 5-3 1 0 1 0 2 2 0 1-2 2-3 3s-2 3-2 5c-1 1-1 3-2 4v1h0-1c-2-1-5-3-7-3l-1-1h2zm-39 41h0c1-2 2-4 2-6l-3-3h-1v1h0c1 1 1 1 1 2s0 1-1 1c-2 0-2 0-3-2-1-1-1-2 0-3 0-2 2-3 3-4 2-1 4-1 5 0 2 1 3 3 4 5-1 4-4 7-7 10v-1z" class="q"></path><path d="M226 585l6-1-1 2h0c-2 1-3 2-3 4h0v3 1 1 1h-2-3l-2-1c0 1-2 1-3 1-2 0-4 0-6-1v-1 1l-1-1h0c-1 0-1-2-1-3l1-3s0-1 1-1h0l2-1h1 0l3-1h8z" class="AC"></path><path d="M214 586h1v1c-2 2-2 5-3 8h9c0 1-2 1-3 1-2 0-4 0-6-1v-1 1l-1-1h0c-1 0-1-2-1-3l1-3s0-1 1-1h0l2-1z" class="AB"></path><path d="M212 587h0l2-1-1 1-2 7h0c-1 0-1-2-1-3l1-3s0-1 1-1z" class="R"></path><path d="M226 585l6-1-1 2h0c-2 1-3 2-3 4h0v3 1 1 1h-2-3l-2-1 4-5-1-1c-1 1-2 3-3 5h-1v-1l4-5v-1h-1c-1 1-2 2-2 3-1 1-1 1-1 2l-1-1h0l1-1c1-1 1-1 1-2v-1c-1 1-2 0-3 0h-3v-1h0l3-1h8z" class="j"></path><path d="M225 590l1-1c0 2-3 4-3 6h1s0-1 1-1l3-4v3 1 1 1h-2-3l-2-1 4-5z" class="Y"></path><path d="M226 596l1-2h0l1 1v1h-2z" class="w"></path><path d="M265 574l3-1 1 1c0 1 0 1-1 2s-2 2-2 3c-2 3-3 5-5 7l-2 1c-4 1-5 5-9 6v-1h-1c-1 0-1 1-2 2l-1-1c0 1-1 2-1 2-1 1-2 1-3 1h0c1-2 3-4 5-6 0-1 1-3 1-4 2-1 3-3 3-5h0l-1-1c1-1 3-2 4-2l11-4z" class="E"></path><path d="M249 592c1-2 3-4 3-5 1-3 3-5 5-7h1l-1 2c-1 1-2 3-3 4-1 2-3 4-4 6h-1z" class="l"></path><path d="M245 595h-1 0l5-8 3-5c1 0 1-1 1-2h1l1 1c-1 1-3 3-4 5-1 3-3 5-5 7 0 1-1 2-1 2z" class="D"></path><path d="M258 580h2 1v1l-3 4s0 1 1 2c-4 1-5 5-9 6v-1c1-2 3-4 4-6 1-1 2-3 3-4l1-2z" class="m"></path><path d="M258 580h2 1l-4 2 1-2z" class="S"></path><path d="M269 574c0 1 0 1-1 2s-2 2-2 3c-2 3-3 5-5 7l-2 1c-1-1-1-2-1-2l3-4c0-1 0-2 1-2 0-1 0-1 1-2 0-1 1-2 2-2h2c1-1 1-1 2-1z" class="R"></path><defs><linearGradient id="R" x1="238.218" y1="582.079" x2="243.21" y2="592.445" xlink:href="#B"><stop offset="0" stop-color="#535253"></stop><stop offset="1" stop-color="#767577"></stop></linearGradient></defs><path fill="url(#R)" d="M241 582l9-2 1 1h0c0 2-1 4-3 5 0 1-1 3-1 4-2 2-4 4-5 6-1 0-1 0-2-1-3 1-9 1-12 1v-1-1-1-3h0c0-2 1-3 3-4h0l1-2h2l7-2z"></path><path d="M234 585h1c0 1 0 1-1 1l-1-1h1z" class="c"></path><path d="M232 584h2 0v1h-1l-1 2-1-1h0l1-2zm9-2l9-2 1 1c-3 1-7 3-10 1h0z" class="m"></path><path d="M231 592v2h0c1 0 1 0 2 1h2v-1h0c0 1 1 1 1 1h4c-3 1-9 1-12 1v-1-1 1l3-3z" class="T"></path><path d="M233 585l1 1c-1 2-3 4-3 6l-3 3v-1-1-3h0c0-2 1-3 3-4l1 1 1-2z" class="C"></path><path d="M231 586l1 1c-2 2-3 3-4 6v-3h0c0-2 1-3 3-4z" class="B"></path><path d="M314 570c0-1-1-2-2-2l-3-1-1 1c-2 1-4 3-7 4h-1c2-3 4-4 7-5l-5-3c5-1 10-2 15 1 1 0 2 2 3 4 1 4-1 7-3 11-6 8-18 13-27 15l-16 3c-1 0-1-1-2 0h-4v-2c8-2 16-7 22-13v1l-13 12c5-1 10-2 15-4 6-2 16-7 19-13 0-1 0-2 1-2v-1l-10 2 6-3c2-1 4-2 6-4h1l-1-1zm46-75h1v2h-1l1 1c1-1 1-1 1-2h1c0 1 0 1 1 2-1 0-1 0-1 1-1 1-3 2-4 3h0l-8 5-2 1c-1 1-3 2-3 3v2l-2 1-3 3c-9 5-16 13-23 19l-24 20c-6 4-12 9-18 12h0l1-3c2-1 4-3 6-5 6-4 13-9 19-14l6-6c1-2 3-3 5-5h-1c0-2 0-2 1-3h0c1-2 3-3 5-5 3-2 6-5 8-7h4c2-1 5-3 6-5 2-1 3-2 4-4 5-3 9-6 14-10l6-6z" class="n"></path><path d="M360 495h1v2h-1l1 1c1-1 1-1 1-2h1c0 1 0 1 1 2-1 0-1 0-1 1-1 1-3 2-4 3v-1c1-1 2-1 3-2h0-3c-1 2-3 2-5 2l6-6z" class="a"></path><defs><linearGradient id="S" x1="328.706" y1="522.561" x2="314.528" y2="529.101" xlink:href="#B"><stop offset="0" stop-color="#525453"></stop><stop offset="1" stop-color="#767274"></stop></linearGradient></defs><path fill="url(#S)" d="M313 532c1-2 3-3 5-5 3-2 6-5 8-7h4l-17 15h-1c0-2 0-2 1-3h0z"></path><path d="M376 470h1c0 2 0 4-1 6 1 0 1 1 2 1v1c0 1 0 1 1 2l1 1v1 1l-3 2h-2c-1 0-1 0-1 1l-1 1h3l1 1h0l1 1h1v1h2c-1 0-1 0-2 1l-2 3-2 2-2 2h0l1 1-2 2h1v2 4l1 2-1 1c-3-1-6-1-9 0l-3 1-6 1h-1l-2 1 1 1h0 1l-5 1c-3 0-6 2-8 2l3-3 2-1v-2c0-1 2-2 3-3l2-1 8-5h0c1-1 3-2 4-3 0-1 0-1 1-1-1-1-1-1-1-2-1-2-2-2-3-3l1-1c3-2 6-6 8-9 3-4 5-9 7-13z" class="N"></path><path d="M374 482l2 1c1 1 1 1 1 2h-2c-1 0-1 0-1 1h-1l-1 2h0c-3 1-5 3-8 4 3-2 5-4 8-6 1-1 1-2 2-4z" class="R"></path><path d="M376 476c1 0 1 1 2 1v1c0 1 0 1 1 2l1 1v1 1l-3 2c0-1 0-1-1-2l-2-1 2-6z" class="h"></path><path d="M378 478c0 1 0 1 1 2l-2 2c0-1 0-1 1-2v-2z" class="c"></path><path d="M379 480l1 1v1 1l-3 2c0-1 0-1-1-2l1-1 2-2z" class="i"></path><path d="M372 488h0l1-2h1l-1 1h3l1 1v1c-1 0-1 0-1 1-1 1-1 2-2 3s-1 3-2 4c-1 0-2 1-2 1l-1 1h0c0-1 0-1-1-2-1 1-2 1-3 2-2 1-3 3-6 4v-1h0c1-1 3-2 4-3 0-1 0-1 1-1 2-1 4-2 5-4l3-3v-3z" class="O"></path><path d="M373 491c0-1 0-1 1-2l2 1c-1 1-1 2-2 3l-1-1v-1z" class="f"></path><path d="M373 491v1l1 1c-1 1-1 3-2 4-1 0-2 1-2 1l-1 1h0c0-1 0-1-1-2h0c2-1 4-4 5-6z" class="P"></path><path d="M377 488h0l1 1h1v1h2c-1 0-1 0-2 1l-2 3-2 2-2 2h0l1 1-2 2h1v2l-1 2c-1 0-1 0-2-1-1 1-2 1-3 1h-1v-1h-1l5-5h-1l1-1s1-1 2-1c1-1 1-3 2-4s1-2 2-3c0-1 0-1 1-1v-1z" class="b"></path><path d="M376 490c0-1 0-1 1-1 0 3-1 4-2 6l-2 1c-1 1-1 3-3 3h-1l1-1s1-1 2-1c1-1 1-3 2-4s1-2 2-3z" class="Z"></path><path d="M373 498l1 1-2 2h1v2l-1 2c-1 0-1 0-2-1-1 1-2 1-3 1 2-2 4-4 6-7z" class="m"></path><path d="M370 504l3-3v2l-1 2c-1 0-1 0-2-1z" class="w"></path><path d="M368 497c1 1 1 1 1 2h0 1l-5 5h1v1h1c1 0 2 0 3-1 1 1 1 1 2 1l1-2v4l1 2-1 1c-3-1-6-1-9 0l-3 1-6 1h-1l-2 1 1 1h0 1l-5 1c-3 0-6 2-8 2l3-3 2-1v-2c0-1 2-2 3-3l2-1 8-5v1c3-1 4-3 6-4 1-1 2-1 3-2z" class="U"></path><path d="M351 507l1 2-2 1-1-2 2-1z" class="P"></path><path d="M368 497c1 1 1 1 1 2-1 1-2 2-3 2l-1-2c1-1 2-1 3-2zm-19 11l1 2-4 3v-2c0-1 2-2 3-3z" class="I"></path><path d="M365 499l1 2c-3 1-5 3-7 4l-1-1 1-1c3-1 4-3 6-4z" class="P"></path><path d="M351 507l8-5v1l-1 1 1 1c-2 1-5 2-7 4l-1-2z" class="K"></path><path d="M370 504c1 1 1 1 2 1l1-2v4l1 2-1 1c-3-1-6-1-9 0l-3 1-6 1h-1l-2 1 1 1h0 1l-5 1c-3 0-6 2-8 2l3-3h0c2 0 5-2 6-2 4-2 7-3 10-5 2 0 3-2 5-3h1v1h1c1 0 2 0 3-1z" class="P"></path><path d="M362 508l-1 1c1 1 2 1 3 1l-3 1-6 1h-1 0s7-3 8-4z" class="B"></path><path d="M370 504c1 1 1 1 2 1l1-2v4l1 2-1 1c-3-1-6-1-9 0-1 0-2 0-3-1l1-1 4-3h1c1 0 2 0 3-1z" class="Y"></path><path d="M274 598l22-2c6 0 12-1 18-4 9-4 18-11 21-21 2-5 3-14 0-19-3-6-8-8-14-10h8-2l1 1c2 0 5 2 7 3h1 0v-1 1c1 1 2 2 3 4 3 4 4 10 3 15-1 7-5 14-10 18-2 2-5 4-7 6-1 0-2 1-2 2h0c3 1 6 1 9 2 1 0 1 0 1-1l1 1v1h0c-1 2-2 3-4 4h-3 0 1c-2 1-5 0-7 0 1-1 1-1 1-2v-1 1c-1 1-2 1-4 2-1-1-1-1-1-2v1c-3 2-18 4-22 5h-4l-5 1c-1-1-1-2-1-3h-4-12v-1h3 0c1 0 1-1 2-1h0z" class="N"></path><path d="M291 600h3 0c0 1 0 1-1 1 1 1 1 1 2 1h-4c-1 0 0 0-1-1h1v-1z" class="X"></path><path d="M291 600v1h-1c1 1 0 1 1 1l-5 1c-1-1-1-2-1-3h6z" class="a"></path><path d="M377 494c1 2 1 3 1 5 0 1 1 3 1 4 1 0 1 1 2 1v1l1 1 3 3h-3c1 1 2 1 2 2 0 2 2 5 4 6 0 1 1 1 1 1l1 1c-1 1-1 1-2 1v-1h-1c0 1 0 4 1 5v1 10 1 2 1c-1 0-1 0-2 1v2l2 7 1 3c0 2 2 4 2 6 1 0 2-1 3-2v1c-1 1-1 2-1 3h1v8c0 1 1 1 1 1l1-1v1 1h1c0 1 0 1 1 1v2h1l-4 3h3c1-1 2-1 3-2 1 0 1-1 2-1 0 1 1 1 1 2-1 3-2 6-4 8-1 2-3 4-5 6l-1 1-1-1c-1 0-2 1-2 1-4 2-8 4-12 5l1-1c1 0 1 0 2-1h1 0-3-2 0c-12 2-24 4-36 4h-8v-3h0v-1l-1-1c0 1 0 1-1 1-3-1-6-1-9-2 2-1 4-2 5-2l6-3 11-5c-1 0-1-1-1-1-1-1-1-2 0-4l1-3v-2c2-5 3-11 4-16h0l2-1c-2-1-3-3-4-4-1-4 0-7 2-10-2-1-1-2-1-5v-5c0-1 1-2 0-4h-4c-2 0-6 2-9 2 6-3 11-4 17-5h1l1-1v-2h-1v-1-1s0-1 1-2v-1-1h-1 0l-1-1 2-1h1l6-1 3-1c3-1 6-1 9 0l1-1-1-2v-4-2h-1l2-2-1-1h0l2-2 2-2z" class="j"></path><path d="M364 562h-1l1-6v3 2 1z" class="z"></path><path d="M364 561c1 0 1 0 2 1 0 1 1 1 1 2l-2 1c-1 0-1 0-1-1v-2-1z" class="B"></path><path d="M367 554l1 1v3l-1 1c0-1-1-1-1-2s0-2 1-3z" class="S"></path><path d="M360 563v-1h1c0-1 1-2 1-4 1 1 1 4 0 6v2l-2-3z" class="u"></path><path d="M370 561l3-5c0 1-1 6-2 7 0-1-1-1-1-2z" class="K"></path><path d="M365 515h0s1 0 1 1v2 5h-1 0c0-2-1-6 0-8z" class="S"></path><path d="M375 531h0c-1 0-1 0-1-1h-1c-1-2 0-5 0-7 1 1 1 1 1 2v-2h1c0 2-1 5 0 6h0v2z" class="T"></path><path d="M377 518l2 3c1 2 1 6 0 9v-4c0-1-1-2-1-3-2-2-2-2-1-5z" class="W"></path><path d="M363 515h2c-1 2 0 6 0 8 0 1 0 1-1 1h-1v-2h-1v-2-5h1z" class="F"></path><path d="M362 520v-5h1c1 2 0 5 0 7h-1v-2z" class="T"></path><path d="M381 519c1 2 1 4 1 6-1 3-1 5-2 7-1-1-1-2-1-2 1-3 1-7 0-9l2-1v-1z" class="o"></path><path d="M347 550l1-1c1 0 2 0 2 1h1c1 1 4 1 5 2 0 1-1 1-1 2h-4c-2-1-3-3-4-4z" class="N"></path><path d="M378 556v5c-1 1-2 3-2 5-1 2-2 4-3 7l-1 1h-1v-1c3-4 3-7 4-10 0-2 1-3 1-5l2-2z" class="c"></path><path d="M379 547v5h1c0 1 1 3-1 5 0 1 0 2-1 3h1s0 1 1 1c-1 1-2 3-2 4-1 0-2 1-2 1 0-2 1-4 2-5v-5h0 0v-2h0l-1 1v-1-2h0v1h1v-1h0 1c-1-1-1-2-1-4l-1 1 2-2z" class="k"></path><path d="M375 529c1-1 1-2 2-2h1c1 1 1 2 1 3v3 2c-1 2 0 3 0 4 0 2 0 4 1 5v1l-1 2h0c-1-2-1-4-1-6-1 0-1-1-1-1 0-3-1-7-2-9v-2z" class="B"></path><path d="M358 565v-3l1 1v2h1v-2l2 3v1l-3 3-1 1-5 4c0-1 1-2 1-3l-2-2v-1h0l1-1v2h1c1-1 1-2 2-3h0l2-2z" class="U"></path><path d="M367 564l3-3c0 1 1 1 1 2s-1 3-1 4l1 2v2l-1 1-1 2-2 2-4 4h-2 0v-2c-1 1-3 1-5 2l-1-1c2-1 3-2 4-3l-2-1h1l2-2-1-2h-1l1-1 3-3 3-2 2-1z" class="I"></path><path d="M360 575c0-1 1-1 1-1 1-1 2-2 4-3 1-1 2 0 4 1-1 0-1 1-2 1h-1c-1 1-4 3-5 3h0c-1-1 0-1-1-1z" class="K"></path><path d="M362 567v1l1 1c1 0 2-1 3-2h0c0 2-3 3-4 4 0 1-1 2-2 2l-1-2h-1l1-1 3-3z" class="P"></path><path d="M358 571l1-1c1 0 2 1 3 1 0 1-1 2-2 2l-1-2h-1z" class="K"></path><path d="M360 575c1 0 0 0 1 1h0c1 0 4-2 5-3h1c-2 2-3 3-6 5-1 1-3 1-5 2l-1-1c2-1 3-2 4-3l1-1z" class="E"></path><path d="M369 572h1l-1 2-2 2-4 4h-2 0v-2c3-2 4-3 6-5 1 0 1-1 2-1z" class="V"></path><path d="M380 561l1-1v2c0 2-1 5-2 7v4c0 2-1 3-2 4s-2 3-3 4h-1l-2 1c-2 0-3 1-5 1l-2-1h0c0-1-1-1-1-2h0l4-4 2-2 1-2 1-1v2 1h1l1-1c1-3 2-5 3-7 0 0 1-1 2-1 0-1 1-3 2-4z" class="B"></path><path d="M376 566s1-1 2-1c-2 3-2 7-5 9 0 1 0 2-1 2v-1c1-1 1-1 1-2 1-3 2-5 3-7z" class="F"></path><path d="M379 569v4c0 2-1 3-2 4s-2 3-3 4l-1-1 2-5 4-6z" class="l"></path><path d="M371 571v2 1h1l1-1c0 1 0 1-1 2v1h2l1-1-2 5 1 1h-1l-2 1c-2 0-3 1-5 1l-2-1h0c0-1-1-1-1-2h0l4-4 2-2 1-2 1-1z" class="C"></path><path d="M367 576v4l-2 1-1 1c0-1-1-1-1-2h0l4-4z" class="H"></path><path d="M369 578l2 2c-2 1-3 2-5 3l-2-1h0l1-1c2 0 3-1 4-3z" class="R"></path><path d="M371 571v2 1h1l1-1c0 1 0 1-1 2l-1 1c0 1 1 1 1 2l-1 2-2-2c1 0 1-1 1-1 0-1-1-2-1-3l1-2 1-1z" class="W"></path><path d="M371 580l1-2c0-1-1-1-1-2l1-1v1h2l1-1-2 5 1 1h-1l-2 1c-2 0-3 1-5 1 2-1 3-2 5-3z" class="u"></path><path d="M373 580l1 1h-1l-2 1c0-1 1-2 2-2z" class="z"></path><defs><linearGradient id="T" x1="344.576" y1="561.065" x2="351.404" y2="566.355" xlink:href="#B"><stop offset="0" stop-color="#434243"></stop><stop offset="1" stop-color="#605f60"></stop></linearGradient></defs><path fill="url(#T)" d="M351 554h4 1v1h0l-1 1v1 7h0c1 0 1-1 1-1 1 0 1 0 1 1l1 1-2 2h0c-1 1-1 2-2 3h-1v-2l-1 1h0v1l2 2c0 1-1 2-1 3l-8 6c-1 0-1-1-1-1-1-1-1-2 0-4l1-3v-2c2-5 3-11 4-16h0l2-1z"></path><path d="M351 565v-3-1c0 1-1 3-1 4h-1c0-1 1-2 1-3 1-1 1-3 1-4h1v3l-1 4z" class="W"></path><path d="M351 554h4 1v1h0l-1 1h-1l-5-1 2-1z" class="Y"></path><path d="M352 561v2c0-1 1-1 1-1h0v1l1 1c-1 1-1 2-1 4l-1 1h0s-1 0-2 1c1-2 1-4 1-5l1-4z" class="R"></path><path d="M353 563l1 1c-1 1-1 2-1 4l-1-1c0-2 0-2 1-4z" class="T"></path><path d="M354 556h1v1 7h0c1 0 1-1 1-1 1 0 1 0 1 1l1 1-2 2h0c-1 1-1 2-2 3h-1v-2l-1 1 1-1c0-2 0-3 1-4v-8z" class="m"></path><path d="M350 570c1-1 2-1 2-1v1l2 2c0 1-1 2-1 3l-8 6c-1 0-1-1-1-1-1-1-1-2 0-4l1-3v-2l1 2h0c2-1 3-2 4-3z" class="AA"></path><path d="M345 573v1c0 1 2 1 3 1l-3 3-1-1v-1l1-3z" class="F"></path><path d="M350 570c1-1 2-1 2-1v1c-1 2-3 3-4 5-1 0-3 0-3-1v-1-2l1 2h0c2-1 3-2 4-3z" class="i"></path><path d="M364 510c3-1 6-1 9 0 4 1 6 4 8 8v1 1l-2 1-2-3c-3-2-7-4-10-4-1 0-1 1-2 1h0-2-1v5 2 1h-2-7l1-1v-2h-1v-1-1s0-1 1-2v-1-1h-1 0l-1-1 2-1h1l6-1 3-1z" class="n"></path><path d="M361 511h0c-1 1-2 1-3 1h2v1h4-6-2l-1-1 6-1z" class="a"></path><path d="M354 512h1l1 1h2 6l3 1c-1 0-1 1-2 1h0-2-1v5 2 1h-2-7l1-1v-2h-1v-1-1s0-1 1-2v-1-1h-1 0l-1-1 2-1z" class="F"></path><path d="M353 519v-1s0-1 1-2v4h-1v-1z" class="g"></path><path d="M358 514l1 1v6l-1-1v-6z" class="s"></path><path d="M361 520v2l-1-1v-6h1v5z" class="z"></path><path d="M354 512h1l1 1h2 6l3 1c-1 0-1 1-2 1h0-2-1v5h-1v-5h-1 0-1l-1-1-2 1c-1 1 1 4-1 5v-5h-1v-1h-1 0l-1-1 2-1z" class="U"></path><path d="M354 512h1l1 1h2l-5 1h0l-1-1 2-1z" class="Q"></path><defs><linearGradient id="U" x1="338.351" y1="582.087" x2="376.509" y2="586.725" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#272528"></stop></linearGradient></defs><path fill="url(#U)" d="M358 571h1l1 2-2 2h-1l2 1c-1 1-2 2-4 3l1 1c2-1 4-1 5-2v2h0 2 0c0 1 1 1 1 2h0l2 1c2 0 3-1 5-1l2-1h1c1-1 2-3 3-4l2 1c0 2-1 3-1 4l-3 1-12 3-17 3c-2 1-7 1-8 3v1l-2-1h-1 0c0 1 0 1-1 2v-1l-1-1c0 1 0 1-1 1-3-1-6-1-9-2 2-1 4-2 5-2l6-3 11-5 8-6 5-4z"></path><path d="M361 581h0l2-1c0 1 1 1 1 2h0l-2 1c0-1-1-1-1-2z" class="L"></path><path d="M358 583c0-1 0-1 1-2l1 1 1-1c0 1 1 1 1 2h-4z" class="H"></path><path d="M377 577l2 1c0 2-1 3-1 4l-3 1 2-2c0-1 1-1 0-2-1 1-2 2-3 2h-1 1c1-1 2-3 3-4z" class="Y"></path><path d="M356 580c2-1 4-1 5-2v2h0 2 0l-2 1h0l-1 1-1-1c-1 1-1 1-1 2-2 1-3 1-4 1l-3-3 2-1 2-1 1 1z" class="G"></path><path d="M355 579l1 1c-1 1-2 2-2 4l-3-3 2-1 2-1z" class="U"></path><path d="M358 571h1l1 2-2 2h-1l2 1c-1 1-2 2-4 3l-2 1-2 1-7 5c-3 2-7 3-9 6h0c0 1 0 1-1 2v-1l-1-1c0 1 0 1-1 1-3-1-6-1-9-2 2-1 4-2 5-2l6-3 11-5 8-6 5-4z" class="f"></path><path d="M359 571l1 2-2 2v-1c-1-1 0-2 1-3z" class="p"></path><path d="M357 575l2 1c-1 1-2 2-4 3l-2 1v-1h1v-1l3-3z" class="b"></path><defs><linearGradient id="V" x1="390.055" y1="522.536" x2="367.326" y2="532.335" xlink:href="#B"><stop offset="0" stop-color="#373737"></stop><stop offset="1" stop-color="#5b5a5b"></stop></linearGradient></defs><path fill="url(#V)" d="M377 494c1 2 1 3 1 5 0 1 1 3 1 4 1 0 1 1 2 1v1l1 1 3 3h-3c1 1 2 1 2 2 0 2 2 5 4 6 0 1 1 1 1 1l1 1c-1 1-1 1-2 1v-1h-1c0 1 0 4 1 5v1 10 1 2 1c-1 0-1 0-2 1v2l2 7v1h-2v-1h-1c-1 0-1 1-1 2v2h-1v1 1c0 1 0 2-1 3v1 3h-1v-2l-1 1c-1 0-1-1-1-1h-1c1-1 1-2 1-3 2-2 1-4 1-5h-1v-5h0l1-2v-1c-1-1-1-3-1-5 0-1-1-2 0-4v-2s0-1 1-1c1-2 1-4 2-7 0-2 0-4-1-6v-1c-2-4-4-7-8-8l1-1-1-2v-4-2h-1l2-2-1-1h0l2-2 2-2z"></path><path d="M373 498l2-2v2 1c-1-1-1-1-2-1zm7 47v7h-1v-5h0l1-2z" class="i"></path><path d="M385 535v-7c0 1 0 2 1 2v-3 7h0l-1 2v-1z" class="U"></path><path d="M375 498c2 1 1 3 1 4 0 2 1 3 1 4h0v-1h-1 0c0-2-1-5-1-6v-1z" class="z"></path><path d="M379 503c1 0 1 1 2 1v1l1 1 3 3h-3c-1-1-2-4-3-6z" class="E"></path><path d="M383 515h1c0 1 2 3 2 4v4h-1v-1-3l-3-3 1-1z" class="R"></path><path d="M385 549v-1c-1-1-1-2-2-3v-1c0 2 1 5 0 7 0-6 1-11 1-16 0 0 0 3 1 4v-4 1 2c-1 3-1 6 0 8h0c1 1 1 2 1 3h-1z" class="z"></path><path d="M386 534c1-1 1-1 1-2h1v3 1 2 1c-1 0-1 0-2 1v2c0-1 0-2-1-4v-2l1-2h0z" class="x"></path><path d="M386 534c1-1 1-1 1-2h1v3 1 2s0-1-1-2-1-1-1-2z" class="B"></path><path d="M386 549c0-1 0-2-1-3h0c-1-2-1-5 0-8 1 2 1 3 1 4l2 7v1h-2v-1z" class="E"></path><path d="M382 525c0 2 0 5-2 8v5h0c0-1 0-3 1-4v-1c1 1 0 2 0 3 1 2 0 4 0 5 0 0-1 0-1 1v2c-1-1-1-3-1-5 0-1-1-2 0-4v-2s0-1 1-1c1-2 1-4 2-7z" class="m"></path><path d="M381 534v-1c1 1 0 2 0 3 1 2 0 4 0 5 0 0-1 0-1 1v2c-1-1-1-3-1-5 0-1-1-2 0-4h0c0 2 1 5 2 7v-8z" class="j"></path><path d="M362 522h1v2h1c2 2 4 3 6 4 2 3 3 7 3 10 0 2-1 3-1 4-1 3-4 6-7 7-3 2-7 2-10 1h-2-2-1c0-1-1-1-2-1l-1 1c-1-4 0-7 2-10-2-1-1-2-1-5v-5c0-1 1-2 0-4h-4c-2 0-6 2-9 2 6-3 11-4 17-5h1 7 2v-1z" class="m"></path><path d="M355 534v-3c1 1 1 3 1 4h0l-1-1z" class="j"></path><path d="M351 537v-5l1 2v1-1-2c1 1 1 3 1 5h1c0-1 0-2 1-3l1 1-1 3h-2c-1 0-1-1-2-1z" class="z"></path><path d="M351 537c1 0 1 1 2 1h2c0 2 0 5-1 6v1l-1 1h2 0l-3 1 3 3h-2-2-1c0-1-1-1-2-1l-1 1c-1-4 0-7 2-10l1-1 1-2z" class="M"></path><path d="M351 542h0v-1-1h2c1 2 1 2 1 4v1l-3-3z" class="o"></path><path d="M351 537c1 0 1 1 2 1h2c0 2 0 5-1 6 0-2 0-2-1-4v-1h-3l1-2z" class="c"></path><path d="M352 547h-1c0-2-1-3-1-4l1-1 3 3-1 1h2 0l-3 1z" class="I"></path><path d="M355 546h1c1-1 1-4 1-5 1-1 2-1 2-2 0 0 0-2 1-3 0-1 0-2 1-3v11h1v-1h0v-2-1h0c0-2 0-3 1-5h4 0c0 1-1 1-1 1v7 1l-2 2c-3 1-6 1-9 0h0z" class="AC"></path><g class="j"><path d="M361 544h1v-1c0 1 0 2-1 3h0c-1-1 0-2 0-2z"></path><path d="M363 535v4h0c0 1 0 1 1 1-1 2-1 2-2 3v-2-1h0c0-2 0-3 1-5z"></path></g><path d="M370 528c2 3 3 7 3 10 0 2-1 3-1 4-1 3-4 6-7 7-3 2-7 2-10 1l-3-3 3-1c3 1 6 1 9 0l2-2v-1-7s1 0 1-1c2 1 3 1 4 0 0-1-1-3-2-4 0-1 0-2 1-3z" class="N"></path><path d="M370 528c2 3 3 7 3 10 0 2-1 3-1 4v-2-3 1c-1 2-2 6-4 7h-2l3-2c2-2 2-5 2-8 0-1-1-3-2-4 0-1 0-2 1-3z" class="X"></path><path d="M371 535c0 3 0 6-2 8l-3 2-2 1 2-2v-1-7s1 0 1-1c2 1 3 1 4 0z" class="W"></path><path d="M362 522h1v2h1c2 2 4 3 6 4-1 1-1 2-1 3l-1 2c-4 2-9-3-13-5-3-2-7-3-11-2-2 0-6 2-9 2 6-3 11-4 17-5h1 7 2v-1z" class="n"></path><path d="M362 522h1v2l-3-1h2v-1z" class="i"></path><path d="M386 549v1h2v-1l1 3c0 2 2 4 2 6 1 0 2-1 3-2v1c-1 1-1 2-1 3h1v8c0 1 1 1 1 1l1-1v1 1h1c0 1 0 1 1 1v2h1l-4 3h3c1-1 2-1 3-2 1 0 1-1 2-1 0 1 1 1 1 2-1 3-2 6-4 8-1 2-3 4-5 6l-1 1-1-1c-1 0-2 1-2 1-4 2-8 4-12 5l1-1c1 0 1 0 2-1h1 0-3-2 0c-12 2-24 4-36 4h-8v-3h0c1-1 1-1 1-2h0 1l2 1v-1c1-2 6-2 8-3l17-3 12-3 3-1c0-1 1-2 1-4l-2-1c1-1 2-2 2-4v-4c1-2 2-5 2-7h1v-3-1c1-1 1-2 1-3v-1-1h1v-2c0-1 0-2 1-2h1z" class="o"></path><path d="M389 567h2c-1 1-1 2-2 4l-1-2c0-1 1-2 1-2z" class="c"></path><path d="M378 588h2c-1 1-3 2-5 2h-1l1-1h1l2-1z" class="M"></path><path d="M398 576c1-1 2-1 3-2v3 1 1c-1-1-2-1-2-2v-1h-1z" class="a"></path><path d="M389 567h0l2-5c1-1 1-2 2-2l-2 7h-2z" class="R"></path><path d="M390 589l1 1c-4 2-8 4-12 5l1-1c1 0 1 0 2-1h1 0-3-2 0c4-1 9-1 12-4z" class="v"></path><path d="M380 585h1 0c-2 1-4 2-5 2-3 1-5 1-8 1-1 1-1 1-2 1-2 0-5 1-7 2-1 0-1 0-1-1 1-1 5-1 7-2 1 0 3-1 5-1s4 0 6-1 3-1 4-1z" class="f"></path><path d="M389 552c0 2 2 4 2 6 1 0 2-1 3-2v1c-1 1-1 2-1 3h0c-1 0-1 1-2 2l-2 5h0s-1 1-1 2l1 2-1 1-1 2c-1-1-1-1-1-2h-1c1-2 2-2 2-4 2-3 2-7 2-11v-1-4z" class="m"></path><path d="M386 572c1-1 1-1 2 0l-1 2c-1-1-1-1-1-2z" class="k"></path><path d="M380 571l2 2c0 1 0 1 1 2v-1l2-2h1c0 1 0 1 1 2-3 3-5 6-9 8h0c0-1 1-2 1-4l-2-1c1-1 2-2 2-4l1-2z" class="u"></path><path d="M395 569l1-1v1 1h1c0 1 0 1 1 1v2h1l-4 3c-1 0-2 0-3 1-2 1-5 2-7 3 4-3 6-6 8-10l1-2c0 1 1 1 1 1z" class="m"></path><path d="M395 569l1-1v1 1c0 1 0 1-1 2l-1-1-1-1 1-2c0 1 1 1 1 1z" class="s"></path><path d="M394 568c0 1 1 1 1 1s0 1-1 2l-1-1 1-2z" class="E"></path><path d="M398 581h1c1 1 1 1 1 2-1 2-3 4-5 6l-1 1-1-1c-1 0-2 1-2 1l-1-1c1 0 1 0 1-1h1v-1c-2 0-3 1-5 1v-1l2-2c1 1 1 1 2 1s1 0 1-1v-1h1 0c0-1 2-2 3-3h0 2z" class="Q"></path><path d="M398 581h1c1 1 1 1 1 2-1 2-3 4-5 6v-2c0-1 0-1 1-2 0-1 0-1 1-1v-1s1 0 1-1h1l-1-1z" class="e"></path><path d="M386 549v1h2v-1l1 3v4 1c0 4 0 8-2 11 0 2-1 2-2 4l-2 2v1c-1-1-1-1-1-2l-2-2-1 2v-4c1-2 2-5 2-7h1v-3-1c1-1 1-2 1-3v-1-1h1v-2c0-1 0-2 1-2h1z" class="T"></path><path d="M382 562l1-1v2l-3 8-1 2v-4c1-2 2-5 2-7h1z" class="L"></path><path d="M386 549v1h2v-1l1 3v4 1c-1 2-1 3-2 5 0 1-1 2-2 4 0-2 0-3 1-5v-3l-1-2h-1v6l-1 1v-2l-1 1v-3-1c1-1 1-2 1-3v-1-1h1v-2c0-1 0-2 1-2h1z" class="C"></path><path d="M382 559h2v3l-1 1v-2l-1 1v-3z" class="H"></path><path d="M386 550h2v-1l1 3v4c-2-2-2-4-3-6z" class="W"></path><path d="M240 357h1c0 1-1 1-1 2h1l-1 2 1 1c0 1 1 1 1 2l2 3 1 2 1 1h0l1-1 3 6c1 2 2 3 2 4s2 4 2 4c2 2 4 4 5 6h1l1 1v2l1 1v3c-1 0-1 0-2-1v2l1 3-1 1v2l1 1h1 0v1 1c0 1 1 1 1 2h1v2 1c3 2 4 3 4 6v3 3 1c0 1 1 1 1 1l-1 6c-1 1-2 5-2 6s0 2-1 3v3l1-1h0l-2 6 1 1v1l-1 1-1-1v2 2l-1 4h-1v1 2 2 1 2l-2-1c-1 1-1 2-1 3v1l-1 3h2v2h1l1 1-1 1-2 1v2c0 1-1 3-1 4v1l-1 2-1 1c-1 2-1 3-1 4v1c-1 3 1 5 0 7h1l-1 4c1-1 1-2 2-3h0v-2-2l1 1c1 1 0 3 0 4v1-1h2v1l-1 3c0 3-1 5-2 8v1h1c0 2 0 3-1 4 1 1 1 1 2 1l-2 2c0 1-1 4-2 5v1h-2l-3 9c-2 4-5 8-8 11-1 1-2 3-2 3l-2 3c-1 0-2 1-3 1-4 3-8 6-12 10-2 1-6 4-7 6h-1-2l1-1c-3 0-7 3-9 6-1 0-3 1-3 2l-3 1c-1-1-1-1-2-1 2-1 3-1 4-2 0-1 0-1 1-1 1-1 2-2 2-3h1l5-5 2-2c2-3 5-7 7-11h-7-1c-1 0-1-1-2-1v2l-1-1-1-1c-1 0-1 1-2 1-1 1-1 0-2 0l-1-3c0-1-1-2-1-3h0c-2-4-4-6-7-7h-6v-1h-3v-3-3c-1-4-2-9-2-13v-5c1-3 3-6 7-7 2-1 4-2 7-1 1 0 2 0 2-1l-1-1c1-1 1-1 1-2v-8l-2 1-6 3-2 1c-1-1-2-1-2-1l-1-2c-1 0-1 0-1 1l-1-1-1 1v-3h1l1-2c0-1 0-2-1-2v-1s0-1-1-1l-1 1v-1c1-2 1-3 1-5-2 2-4 3-6 4 2-2 3-4 5-6v-4c0-1-1-2 0-3v-4c1 0 1 0 1-1h-1c0-2 1-5 1-7-2 3-3 4-5 6h0c1-1 2-3 3-4 1-3 1-3 1-6h-1c-1 2-3 3-4 4 1-2 3-5 4-7-1 1-3 2-4 3h-1l4-4c0-1 0-1-1-2h0c-2 3-3 3-6 4l4-3c0-2 1-3 1-5l-1 1-1-1v-1h-1v2h0-1v-2c-1 0-1 0-2 1h0c0-1 1-1 1-2h-1l-2 1h0c1-2 2-3 3-4 2-2 4-6 4-8-1 1-2 3-4 4h0c0-1 1-2 1-3v-1c0-1 1-3 1-3 0-1 0-1 1-1 0-1 0-2-1-3 0 1-1 2-1 2-1 0-1 0-2-1h0v-1l2-3c0-1 1-3 2-4l-1-1-1-1c1-3 2-5 2-8 0-1 1-2 1-3l1-3c0-1 1-2 2-4h0l1-1 1-1c1-1 3 0 4-1v1c-1 1-1 2-1 4l2-2 1-1 1-1 1-1 1 1h0l3-6c1-2 1-3 2-4l-1-1c0-1 1-1 1-2v-1l1-1c1-1 1-2 2-4l1-1h1 2l-1-1v-1h-2v-1c-2 0-5 0-7 1h-2-1v-2h-1l1-1c0-1 0-1-1-1l1-1 2-1 3-2h1c1-1 2-1 3-1h0 2l5-1h4l5 1 2 2s0 1 1 1h1c1 0 1 0 2-1h0l1 1 7 4 1-1-5-3 2-5v-1l1-4 2 1h0l2-3c1-1 1-2 3-2h0 1c1-1 1-1 2-1h2 1z"></path><path d="M203 476h1v2 3c-1-1-1-3-1-5z" class="S"></path><path d="M201 442h2c-1 2-1 3-2 4-1-1-1-1-1-2 1 0 1-1 1-2z" class="E"></path><path d="M197 424h1c1 0 1 1 2 2l-2 2h0c-1-1-1-3-1-4h0z" class="B"></path><path d="M194 421l2 1h0l1 1v1h0c0-1-1-1-2-1l-2 3v-1c1-1 1-3 1-4z" class="g"></path><path d="M184 493l1 4c-1 0-1 0-1 1l-1-1-1 1v-3h1l1-2z" class="D"></path><path d="M204 473c1-1 2-2 4-3 0 1 0 2 1 3v4c-1-2-1-3-3-4h-2z" class="B"></path><path d="M223 540c0-1 0-2-1-2h0c0-2 0-4 1-5l2 2-2 5z" class="g"></path><path d="M200 519l1-2v1 10h-1v-4-3c0-1-1-1 0-2z" class="s"></path><path d="M187 482v3l1 1v4h1c0 1 0 1-1 2h0v-2c-1 0-1-1-2-1 1-3 1-5 1-7z" class="g"></path><path d="M215 458l1-2 1 2c0 1-2 4-2 6l-1 1h0c0-2 1-5 1-7z" class="K"></path><path d="M187 539v-2c1 0 1-1 1-2v2c2 1 3 2 5 3h-6v-1z" class="B"></path><path d="M192 523h2v5h-4c0-1 1-1 1-2h1l1-1c-1 0-1-1-1-2h0z" class="u"></path><path d="M213 448c2 2 3 1 4 2l1 1-2 5-1 2-1-3 2-2c0-2-1-2-2-3 0 0 0-1-1-2z" class="R"></path><path d="M203 453l1 1c0 3-1 5-1 7v1l-1 1v3 1h-1v-1-3-1c1-2 1-3 1-4l1-5z" class="J"></path><path d="M199 456h1v1c1-1 1-2 1-2h1v2 1c0 1 0 2-1 4v1c-1-1-1-3-1-4-1-1-1-2-1-3z" class="h"></path><path d="M186 482l1-1v-1h1v2h1v2h1v1 2 3h-1 0-1v-4l-1-1v-3h-1z" class="H"></path><path d="M196 519h4c-1 1 0 1 0 2v3h-1v3h-1v-2l-1 2c0-3 0-5-1-7l-1-1h0 1z" class="E"></path><path d="M198 525v-3-1c1 1 1 2 1 3v3h-1v-2z" class="S"></path><path d="M223 523l-1 1c-1 1-2 1-4 0-1 0-3-2-4-3-1-2 0-4 0-6l1 3c0 1 0 1 1 1 0 1 1 3 2 3 1 1 2 1 2 1 2-1 2-2 3-3h0v1l1 1-1 1z" class="o"></path><path d="M216 519c2-1 3-1 5-1l1 1 1 1c-1 1-1 2-3 3 0 0-1 0-2-1-1 0-2-2-2-3z" class="a"></path><path d="M190 445l3-2h1v-1c0-1 2-4 3-5-1 4-2 7-2 10v1h-1c0-1-1-2-1-3-1 1-1 2-1 2h-1l-1-2z" class="s"></path><path d="M212 435c1 1 1 1 1 2h0 0l1 2-1 3c-1 0-1 1-1 2v1l-1 1c-1-2-1-4 0-5v-1h0-2c-1-1-1 0-2 0h0v-1h1l4-4z" class="m"></path><path d="M213 437l1 2-1 3c-1 0-1 1-1 2v-3c0-1 1-2 1-4h0z" class="H"></path><path d="M203 534l-1-1c-1-2 0-3 0-5v-10-1-1h1c1 1 1 2 1 4h0v7c0 2-1 4-1 7z" class="x"></path><path d="M201 433l1 1 1 1v1c1-1 1-2 2-3v1h1v1l-1 2c0 1-1 1-1 2 0 0-1 1-1 2v1h-2c0 1 0 2-1 2v-4l1-5v-2z" class="D"></path><path d="M201 442h0c1-2 1-4 2-5s0-1 1-1v3s-1 1-1 2v1h-2z" class="z"></path><path d="M225 489c1 0 1 0 2 1v1c0 1 1 3 1 5h0c-2 1-1 2-1 4h-1c0 1 0 2-1 3l-1 1v-2-1-1c0-2 0-2-1-3 1-1 1-1 1-2v-2h0l1-2v-2z" class="B"></path><path d="M226 500v-5h-1c0-2 1-3 2-4 0 1 1 3 1 5h0c-2 1-1 2-1 4h-1z" class="t"></path><path d="M210 431c1-1 1-1 2-1h-1v2c-1 1-1 3-2 4l-2 3v1h0c0 2-1 2-2 3-1-1-1-2-1-3l-1 1c0-1 1-2 1-2 0-1 1-1 1-2l1-2v-1-1c2 0 3-1 4-2h0z" class="l"></path><path d="M206 435h1l-2 3v1c2-1 2-3 3-3h1l-2 3v1h0c0 2-1 2-2 3-1-1-1-2-1-3l-1 1c0-1 1-2 1-2 0-1 1-1 1-2l1-2z" class="B"></path><path d="M191 482h1s1 1 0 1h1 1 0 1 0c0 2 1 4 3 6h-1l-1 1c-1 1-1 1-2 1h-2c-1-1-1-1-3-1h1v-3-2c1 1 1 2 1 3l2-1c-1-2-1-3-2-5z" class="c"></path><path d="M192 483h1 1c0 1 0 2-1 2 0 0 0-1-1-2z" class="O"></path><path d="M193 487c0 1 0 2-1 3 0-1-1-1-1-2l2-1z" class="Z"></path><path d="M204 520c0-1 0-2 1-3 1 0 1 1 1 2-1 2-1 3-1 4l1 1c0 4-1 7-1 11v1l1 1c-1 0-1 1-2 1l-1-1v-3c0-3 1-5 1-7v-7z" class="s"></path><path d="M204 534v1h1v1l1 1c-1 0-1 1-2 1l-1-1h1v-3z" class="U"></path><path d="M205 523l1 1c0 4-1 7-1 11h-1v-1c0-4 1-7 1-11zm-29-87c0-1 0-1 1-2v1h0c-1 1-1 1-1 2h1v-1c1-1 1-2 2-2v1 1 1c-1 1-1 1-1 2l-1 1v1c0 1-1 1-1 2 0 2-1 2-2 3v1l-1 1c-1 0-1 0-2 1h0c0-1 1-1 1-2h-1l-2 1h0c1-2 2-3 3-4 2-2 4-6 4-8z" class="g"></path><path d="M190 454l1 1c1 0 2 1 3 1v3l1-1v5 2h0v2l-1-1h-1v1h-2v-2l-1-1v-2-5-3z" class="AB"></path><path d="M190 454l1 1c1 2 1 3 0 5v-3h-1v-3z" class="R"></path><path d="M192 461c0-1 0-4 2-5v3l-1 2h-1z" class="C"></path><path d="M190 457h1v3 2 2h-1v-2-5z" class="s"></path><path d="M195 458v5 2h0v2l-1-1h-1v1h-2v-2l-1-1h1v-2l1-1h1l1-2 1-1z" class="Y"></path><path d="M195 465h-2-1c1-2 1-2 3-2v2z" class="D"></path><path d="M198 436l1-1 1 1 1-1-1 5v4c0 1 0 1 1 2 0 1-1 2-1 2l-1 1h-2v-1l-1 1v-1c0-1-1-2 0-3h0v-1-1s1-2 1-3v-1-1c0-1 1-1 1-2z" class="u"></path><path d="M197 449c1-1 1-2 2-2l1 1-1 1h-2z" class="z"></path><path d="M198 436l1-1 1 1 1-1-1 5h-1v-3-1h-1z" class="Y"></path><path d="M190 523h2 0c0 1 0 2 1 2l-1 1h-1c0 1-1 1-1 2s-1 2-1 3l-1 4c0 1 0 2-1 2v2h-3v-3-3-1l1 1 1-1s0-1 1-1v-5s0-1 1-2v1h1c0-1 1-1 1-2z" class="AC"></path><path d="M185 533l1-1v4l-1-1v-2z" class="J"></path><path d="M184 533v-1l1 1v2h-1v1-3z" class="k"></path><path d="M216 526l4 1c1 2 0 4-1 7v3c0 1-1 2-1 2h0c-2 0-2-1-3-2s-1-2-1-3c1 0 1-1 1-1h1v-4-2h0v-1z" class="F"></path><path d="M216 526l4 1h-1l-1 3v3h0c0-2 0-3-1-5v-1h0-1 0v-1z" class="v"></path><path d="M218 530l1-3h1c1 2 0 4-1 7h0l-1-4z" class="Z"></path><path d="M215 533h1v-4c0 3 0 4 1 6 1 1 0 1 1 1 0 1 0 1 1 1 0 1-1 2-1 2h0c-2 0-2-1-3-2s-1-2-1-3c1 0 1-1 1-1z" class="U"></path><path d="M195 458v-1h3 0l1-1c0 1 0 2 1 3 0 1 0 3 1 4v3l-1-1v3c-1 0-1 0-1-1h-1v1h-1 0v-2l-1-1h-1 0 0v-2-5h0z" class="B"></path><path d="M195 458l1-1v3 2 3h-1 0 0v-2-5h0z" class="J"></path><path d="M196 462v3h-1v-2l1-1z" class="t"></path><path d="M198 468l-1-1v-3c1-2 1-4 1-6h0c1 2 2 6 2 9h-1-1v1z" class="D"></path><path d="M189 514c1 2 2 3 3 3l2 1 1 1h0l1 1c1 2 1 4 1 7h-1-1v-4h-1-2-2c0 1-1 1-1 2h-1v-1-1c0-1 1-2 1-4v-5z" class="T"></path><path d="M192 517l2 1c0 1-1 1-2 2l-1-1c0-1 1-1 1-2z" class="l"></path><path d="M194 518l1 1h0c0 1 0 2-1 2 0 0-1-1-2-1h0c1-1 2-1 2-2z" class="U"></path><path d="M189 519l1 1v3c0 1-1 1-1 2h-1v-1-1c0-1 1-2 1-4z" class="j"></path><path d="M193 474h1l1 1c1 0 2-1 3 0s0 3 0 5v1h-1l-1 2 3 6h-1c-2-2-3-4-3-6h0-1 0-1-1c1 0 0-1 0-1v-5l1-3z" class="r"></path><path d="M194 474l1 1c1 0 2-1 3 0s0 3 0 5v1h-1l-1 2v-8c-1 2-1 5-2 6-1-2 0-5 0-7z" class="R"></path><path d="M203 453h1c2 0 2 0 4-2h0 0c1 1 1 2 1 4 0-1 0-1 1-1 0 2 0 5-1 7v1h-1 0c-1-1-1-1-2-1h-1v1c-1 0-1 1-1 1v1h-1v-1-1-1c0-2 1-4 1-7l-1-1z" class="AA"></path><path d="M221 443l1 1c-1 2-1 2-1 4h0l1 1-3 6 1 1c0 2-2 3-1 5h0v2c-1 2-2 4-1 6h0v1l-1 1v-1-2c-1-1-1 0-1-2 0-1 0-1-1-2 0-2 2-5 2-6l-1-2 2-5 1-1c0-3 1-5 2-7z" class="C"></path><path d="M216 466c1-1 0-3 1-4 0-1 0-1 1-1v2h1c-1 2-2 4-1 6h0v1l-1 1v-1-2c-1-1-1 0-1-2z" class="O"></path><path d="M221 443l1 1c-1 2-1 2-1 4h0c-2 3-3 6-4 10l-1-2 2-5 1-1c0-3 1-5 2-7z" class="I"></path><path d="M190 445l1 2h1s0-1 1-2c0 1 1 2 1 3h1c-1 0-1 0-2 1-1 0-1 0-2 1s-2 1-2 3l1 1v3 5h-1l-1 1h-1v-1-1l-2 2h0-1c0-2 0-2 1-4 0-1-1-7 0-8s1-3 2-5v1 2h1c0-2 1-2 2-4z" class="E"></path><path d="M187 462h1v-1c-1-3-1-5-1-8l1-1 1 1h0l1 1v3 5h-1l-1 1h-1v-1z" class="s"></path><path d="M189 453l1 1v3 5h-1v-9h0z" class="F"></path><path d="M187 514h0c0-1 0-2 1-2l1 2v5c0 2-1 3-1 4v1c-1 1-1 2-1 2v5c-1 0-1 1-1 1l-1 1-1-1v1c-1-4-2-9-2-13l1-1c0-1 0-3 1-4l1-1c0 1 0 1 1 2l1-2z" class="C"></path><path d="M187 531l-1-2c0-1 0-1 1-3v5z" class="H"></path><path d="M187 522v1h-1c0-2 0-4 1-6v5z" class="k"></path><path d="M187 514h0c0-1 0-2 1-2l1 2v5c0 2-1 3-1 4l-1-1v-5l-1-1c1 0 1-1 1-2z" class="s"></path><path d="M216 421h0c1 1 2-1 3-2h0v-1h1l-2 4h1v1l1 1-4 4c1 1 1 1 2 1v1c0 2 0 2-2 4l1 1h0l-1 2-2 2-1-2h0 0c0-1 0-1-1-2l-4 4h-1l2-3c1-1 1-3 2-4v-2h1c-1 0-1 0-2 1h0-1c0-1 1-2 1-3v-1s0-1 1-1l2-3h0c1-1 2-1 3-2z" class="T"></path><path d="M212 426v1 2l1-1s0-1 1-1v1c-1 1-2 3-3 4v-2h1c-1 0-1 0-2 1 0-1 1-1 1-2 1-1 1-2 1-3z" class="C"></path><path d="M210 427s0-1 1-1h1c0 1 0 2-1 3 0 1-1 1-1 2h0-1c0-1 1-2 1-3v-1z" class="Z"></path><path d="M218 422h1v1l1 1-4 4h0v-1h0l-2 1v-1-1c2-1 3-2 4-4z" class="J"></path><path d="M218 422h1v1c-1 1-2 1-3 3h-2c2-1 3-2 4-4z" class="D"></path><path d="M214 428l2-1h0v1l-1 2-3 5-4 4h-1l2-3c1-1 1-3 2-4s2-3 3-4z" class="K"></path><path d="M216 428h0c1 1 1 1 2 1v1c0 2 0 2-2 4l1 1h0l-1 2-2 2-1-2h0 0c0-1 0-1-1-2l3-5 1-2z" class="l"></path><path d="M216 428h0c1 1 1 1 2 1v1h-1c-1 2 0 3-2 4l-1-1h1v-3l1-2z" class="R"></path><path d="M212 435l3-5v3h-1l1 1h0v1c0 1 0 1 1 2l-2 2-1-2h0 0c0-1 0-1-1-2z" class="k"></path><path d="M215 434v1c0 1 0 1 1 2l-2 2-1-2v-2l2-1z" class="D"></path><path d="M195 465h0 1v3c-1 1-1 1-2 1v-1c-1 1 0 1-1 2 0 0-1 1-2 1v2c0 1 1 1 2 1l-1 3v5h-1c1 2 1 3 2 5l-2 1c0-1 0-2-1-3v-1h-1v-2h-1v-2h-1v1l-1 1h0c-1 0-1-1-2-1v-2c1-1 1-4 1-6h-1c1 2 1 3 0 5h0v1c-1 2-1 2-3 3v-4c0-1-1-2 0-3v-4c0 1 0 2 1 3v4 1c1-2 0-4 1-6l1-1 1-1h1 1v1c1 0 3-4 4-5h0 2v-1h1l1 1v-2z" class="W"></path><path d="M191 473h-2 0v-1l1-1c0-1 0-1 1-2v1 1 2z" class="AB"></path><path d="M189 478c0-1 0-2 1-3h1v3l-1 1-1-1z" class="d"></path><path d="M186 482v-2h1v-5l1-1c1 2 0 5 1 7v1h-1v-2h-1v1l-1 1h0z" class="J"></path><path d="M189 478l1 1 1-1v4c1 2 1 3 2 5l-2 1c0-1 0-2-1-3v-1c0-2 0-4-1-6z" class="r"></path><path d="M198 506l1 1c1 2 1 4 1 6v1c-1 2-2 3-4 3v2h-1l-1-1-2-1c-1 0-2-1-3-3l-1-2c-1 0-1 1-1 2h0l-1 2c-1-1-1-1-1-2l-1 1c-1 1-1 3-1 4l-1 1v-5c1-3 3-6 7-7 2-1 4-2 7-1 1 0 2 0 2-1z" class="j"></path><path d="M198 513v2l2-1c-1 2-2 3-4 3h0c-2 0-3-1-4-2 1-1 2 0 3 1l3-3z" class="M"></path><path d="M194 509l1-1c1 0 2 1 2 2 1 1 1 2 1 3-1 1-2 1-3 2-1-1-1-1-1-2s0-1 1-2h0v2h1v-3l-1-1h-1z" class="f"></path><path d="M198 506l1 1c1 2 1 4 1 6v1l-2 1v-2c1-1 1-2 1-3s-2-2-3-3c1 0 2 0 2-1z" class="e"></path><path d="M194 509h1-1c-1 1-3 0-4 2 0 1 1 2 1 3l1 1c1 1 2 2 4 2h0v2h-1l-1-1-2-1c-1 0-2-1-3-3l-1-2c0-1 0-1 1-2 0 0 1 0 2-1h3z" class="g"></path><path d="M206 519l1 1v-2-1l1 1 1-1c2 2 2 2 3 4s1 3 3 5h1v1h0v2 4h-1s0 1-1 1c0 1 0 2 1 3h-1c-2-1-3-1-5-1h0v1c-1 1 0 2-1 3h-1v-1l-1 1-2-2c1 0 1-1 2-1l-1-1v-1c0-4 1-7 1-11l-1-1c0-1 0-2 1-4z" class="m"></path><path d="M213 531c1 1 1 1 1 2v1c0 1 0 2 1 3h-1c-2-1-3-1-5-1l2-2 1 1c1-1 1-2 1-4z" class="AA"></path><path d="M208 518l1-1c2 2 2 2 3 4l-1-1c-1 0-1 0-1 1v3c0 2 1 5-1 6v-5-5l-1-2z" class="U"></path><path d="M209 520v5 5c0 1-1 1-1 2 0 2 1 4-1 5 0-3 0-5 1-8v-7c0-1 0-2 1-2z" class="W"></path><path d="M206 537c0-2 0-4 1-6v-9h1v7c-1 3-1 5-1 8 0 1 0 3 1 3h-1v-1l-1 1-2-2c1 0 1-1 2-1z" class="x"></path><path d="M206 519l1 1v-2-1l1 1 1 2c-1 0-1 1-1 2h-1v9c-1 2-1 4-1 6l-1-1v-1c0-4 1-7 1-11l-1-1c0-1 0-2 1-4z" class="z"></path><path d="M210 524v-3c0-1 0-1 1-1l1 1c1 2 1 3 3 5h1v1h0v2 4h-1s0 1-1 1v-1c0-1 0-1-1-2h-1 0c-1-1 0-2-1-3s-1-3-1-4z" class="S"></path><path d="M212 531c0-2-1-3 0-4l1-1v5h-1 0z" class="W"></path><path d="M212 521c1 2 1 3 3 5h-1v7c0-1 0-1-1-2v-5c-1-1-1-3-1-5z" class="F"></path><path d="M215 526h1v1h0v2 4h-1s0 1-1 1v-1-7h1z" class="z"></path><path d="M215 526h1v1h0v2 4h-1v-7z" class="g"></path><path d="M221 480l1-1c0 1 0 1 1 2v1c-1 2-1 5 0 7v2h-1-2v2 1c1 1 0 2 0 3v5c-1 1-1 2-1 3h-1v1l-2 1h-1v-1h-3l-1 1v-9h1l-1-1c2-2 0-4 1-6l1-1h1v-2c1 1 1 1 1 0h1v1l1-1h-1v-2-1h-2l-1-2c1 0 4 1 5 0 1 0 2-2 2-2l1-1z" class="U"></path><path d="M216 486c1 0 1 0 2-1h1v6c-1-1-1-2-1-3h-1-1v-2zm5-6l1 1c0 2 0 4-1 5l-1 1c0-2 0-3 1-4 0-1 0-1-1-2l1-1z" class="B"></path><path d="M213 483c1 0 4 1 5 0 1 0 2-2 2-2 1 1 1 1 1 2-1 1-1 2-1 4h0-1v-3h-1l-1 1h-1-2l-1-2z" class="x"></path><path d="M217 488h1c0 1 0 2 1 3v10c0 2-1 3-1 4v1l-1-1v-1c0-1 1-2 1-4s-1-5-1-7h0v-2c-1 0-1-1-1-2l1-1z" class="R"></path><path d="M215 488h1v1c0 1 0 2 1 2v2h0c0 2 1 5 1 7s-1 3-1 4v1l1 1-2 1h-1v-1-8c0-3 0-6-1-8v-2c1 1 1 1 1 0z" class="k"></path><path d="M214 488c1 1 1 1 1 0 0 2 0 3 1 4v5 10h-1v-1-8c0-3 0-6-1-8v-2z" class="z"></path><path d="M211 497c2-2 0-4 1-6l1-1h1c1 2 1 5 1 8v8h-3l-1 1v-9h1l-1-1z" class="C"></path><path d="M212 498v2 1c0 2-1 4 0 5l-1 1v-9h1z" class="T"></path><path d="M200 400c1 0 2 1 2 2l2-2c-1 1-1 2-1 3 1 0 1 0 2-1l1 1h0c1 0 2-1 3 0h0l-3 2c-1 2-2 3-2 4-2 2-2 3-3 5 0 1 0 0-1 1h0l1 3v1c-1 2-1 4-1 5v2c-1-1-1-2-2-2h-1v-1l-1-1h0l-2-1c0 1 0 3-1 4v1c-1 1-1 4-2 5h-1-1c-1-1-2-1-3-3 0-1-1-2-2-4 0 0-1-1-1-2h1s1-1 1-2 1-3 2-4h0v-5c1-1 2-3 3-4h2l3-3 1-1c2-1 3-2 4-3z" class="C"></path><path d="M198 412v1 1l-1 1s0-1-1-1l2-2z" class="E"></path><path d="M203 405v1c0 1 0 2-1 2h-1c0-1 1-2 2-3z" class="D"></path><path d="M196 414c1 0 1 1 1 1v2 2h-1-1v-1l-1-1c0-1 1-2 2-2v-1z" class="T"></path><path d="M196 416l1 1h0v2h-1-1v-1l1-2z" class="B"></path><path d="M196 414c1 0 1 1 1 1v2h0l-1-1v-1-1z" class="i"></path><path d="M189 416h2c0-1 1-2 2-3h1l-1 2c0 1-1 1-2 1h-1v3l-1 1v-1h-1l1-3z" class="R"></path><path d="M198 412v-1-1h2l1-1h3c-2 2-2 3-3 5 0 1 0 0-1 1h0v1c-1 0-1-1-2-2v-1-1z" class="W"></path><path d="M198 413h1l1 2h0v1c-1 0-1-1-2-2v-1z" class="C"></path><path d="M190 423v-1c1 0 1-1 1-1 0-1 0-2 1-3h1v1c0 2-2 4-2 5v2l-1 1-1-1v1h-1c0-1 0-2-1-2v-4l1-2h1v1l1-1v4z" class="H"></path><path d="M189 426c0-1 0-1 1-2h1v2l-1 1-1-1z" class="D"></path><path d="M190 419v4h-1v-3l1-1z" class="k"></path><path d="M204 400c-1 1-1 2-1 3 1 0 1 0 2-1l1 1h0c1 0 2-1 3 0h0l-3 2h-1-1 0-1c-1 1-2 2-2 3h-1v-1h-1c-1 2-1 3-3 4 0 1 0 1-1 2h-1l-1 2 1-2c1-1 2-3 2-5 1-2 5-4 6-6l2-2z" class="W"></path><path d="M198 414c1 1 1 2 2 2v-1l1 3v1c-1 2-1 4-1 5v2c-1-1-1-2-2-2h-1v-1l-1-1h0l-2-1 1-2h0 1 1v-2-2l1-1z" class="E"></path><path d="M198 414c1 1 1 2 2 2l-2 2-1 1h0v-2-2l1-1z" class="S"></path><path d="M195 419h1v2h0c1 0 1-1 1-1h1v4h-1v-1l-1-1h0l-2-1 1-2h0z" class="Y"></path><path d="M195 419c1 1 1 2 1 3l-2-1 1-2z" class="v"></path><path d="M200 400c1 0 2 1 2 2-1 2-5 4-6 6 0 2-1 4-2 5h-1c-1 1-2 2-2 3h-2l-1 3-1 2v4c1 0 1 1 1 2h1v-1l1 1h1l2-2v1c-1 1-1 4-2 5h-1-1c-1-1-2-1-3-3 0-1-1-2-2-4 0 0-1-1-1-2h1s1-1 1-2 1-3 2-4h0v-5c1-1 2-3 3-4h2l3-3 1-1c2-1 3-2 4-3z" class="AB"></path><path d="M189 416l2-4c2-1 2-2 4-3 0 0 1 0 1-1 0 2-1 4-2 5h-1c-1 1-2 2-2 3h-2z" class="F"></path><path d="M187 421v4c1 0 1 1 1 2h1v-1l1 1h1l2-2v1c-1 1-1 4-2 5-1 0-1 0-2-1 0 0 0-1-1-2h-1v-2h-1c0-2 1-3 1-5z" class="R"></path><path d="M204 473h2c2 1 2 2 3 4 0 2 0 4 2 6h2l1 2h2v1 2h1l-1 1v-1h-1c0 1 0 1-1 0v2h-1l-1 1c-1 2 1 4-1 6l1 1h-1v9l1-1h3v1c-1 1-4 2-5 1h-2c-1 1-1 0-1 0-1 0-1 1-2 1-1-1-2-1-2-2h0-1c0 1-1 1-1 2s0 3-1 4c0-2 0-4-1-6l-1-1-1-1c1-1 1-1 1-2v-8h1v1h1c0-1 0-2 1-3h0c0-2 0-4 1-5h2l1-1c1-1 1-2 1-3-1-1-2-2-2-3v-3-2h-1v-1c1 0 1-1 1-2z" class="E"></path><path d="M210 493v-3h1v3h-1z" class="W"></path><path d="M204 478l1-2v1c1 2 1 4 1 7-1-1-2-2-2-3v-3z" class="U"></path><path d="M211 490h1l1-2h1v2h-1l-1 1c-1 2 1 4-1 6l1 1h-1-1v-3-2h1v-3z" class="k"></path><path d="M210 493h1v4l1 1h-1-1v-3-2z" class="s"></path><path d="M206 489c1 2 1 3 1 5v12c0 1 0 1-1 1v-1-9c0-3-1-5 0-8z" class="G"></path><path d="M202 488h2l1-1c0 1 0 1 1 2-1 3 0 5 0 8v9l-1-1v-9c-1-2-1-5-1-7h-1-1v-1z" class="F"></path><path d="M207 506h0c1 0 1 0 1-1v-3-8-5h1v1 5h1 0v3h1v9l1-1h3v1c-1 1-4 2-5 1h-2c-1 1-1 0-1 0-1 0-1 1-2 1-1-1-2-1-2-2h3c1 0 1 0 1-1z" class="S"></path><path d="M210 498h1v9h-1c-1-2 0-8 0-9z" class="H"></path><path d="M202 488v1h1 1c0 2 0 5 1 7v9l1 1v1h-3 0-1c0 1-1 1-1 2s0 3-1 4c0-2 0-4-1-6l-1-1-1-1c1-1 1-1 1-2v-8h1v1h1c0-1 0-2 1-3h0c0-2 0-4 1-5z" class="D"></path><path d="M202 488v1h1c0 4-1 8-1 13 0 1 0 3 1 5h-1c0 1-1 1-1 2s0 3-1 4c0-2 0-4-1-6 1-1 1-1 2-1v-1-7c0-1 1-3 0-4v-1c0-2 0-4 1-5z" class="U"></path><path d="M201 493v1c1 1 0 3 0 4v7 1c-1 0-1 0-2 1l-1-1-1-1c1-1 1-1 1-2v-8h1v1h1c0-1 0-2 1-3h0z" class="J"></path><path d="M198 503c1 1 2 1 2 2l1 1c-1 0-1 0-2 1l-1-1-1-1c1-1 1-1 1-2z" class="i"></path><path d="M197 387v2c0 1 1 1 2 2l-1 1-2 2v1l1 1c0 1-1 2-2 3l-1 1h0c-2 1-3 3-4 4v1c2 0 2-1 4-1h1l-3 3h-2c-1 1-2 3-3 4v5h0c-1 1-2 3-2 4s-1 2-1 2h-1 0l-1-1c-1 1-2 3-4 5h-1s0 1 1 2c-2 2-2 3-3 5h0c0 1 0 2-1 2 0 1 0 2-1 2v-1c0-1 1-3 1-3 0-1 0-1 1-1 0-1 0-2-1-3 0 1-1 2-1 2-1 0-1 0-2-1h0v-1l2-3c0-1 1-3 2-4l-1-1-1-1c1-3 2-5 2-8 0-1 1-2 1-3l1-3c0-1 1-2 2-4h0l1-1 1-1c1-1 3 0 4-1v1c-1 1-1 2-1 4l2-2 1-1 1-1 1-1 1 1h0l3-6c1-2 1-3 2-4l2-3z" class="T"></path><path d="M185 411c1-2 2-3 3-4h2c-1 1-2 3-3 4h-2z" class="m"></path><path d="M185 411h2v5h0-1l-2-1 1-4z" class="x"></path><path d="M179 422c1-1 2-3 2-5 1 0 1-1 1-1h1l-1 5c-1 1-2 3-4 5 0-2 1-3 1-4z" class="L"></path><path d="M183 416l1-1 2 1h1c-1 1-2 3-2 4s-1 2-1 2h-1 0l-1-1 1-5z" class="F"></path><path d="M183 422c0-1 0-1 1-2h1c0 1-1 2-1 2h-1 0z" class="u"></path><path d="M188 400l1-1 1 1c-1 3-4 5-5 8-2 3-4 7-5 11-1 0-1 0-1-1 0 0 0-1 1-2 1-3 2-5 3-8l3-6 1-1 1-1z" class="Z"></path><path d="M197 387v2c0 1 1 1 2 2l-1 1-2 2v1c-2 1-4 5-6 5l3-6c1-2 1-3 2-4l2-3z" class="E"></path><path d="M196 394c0-1 0-1-1-1v-1l1-1 2 1-2 2z" class="W"></path><path d="M184 404l2-2-3 6-3 8c-1 1-1 2-1 2 0 1 0 1 1 1-1 1-1 2-2 3 0 1-1 2-1 4h-1v-1l1-4c1-2 1-2 0-4 2-3 3-7 5-11l2-2z" class="C"></path><path d="M176 418l1-1c1 2 1 2 0 4l-1 4v1h1c0-2 1-3 1-4h1c0 1-1 2-1 4h-1s0 1 1 2c-2 2-2 3-3 5h0c0 1 0 2-1 2 0 1 0 2-1 2v-1c0-1 1-3 1-3 0-1 0-1 1-1 0-1 0-2-1-3 0 1-1 2-1 2-1 0-1 0-2-1h0v-1l2-3c0-1 1-3 2-4l1-4z" class="AA"></path><path d="M176 418l1-1c1 2 1 2 0 4l-1 4v-3h0l-2 5v2c0 1-1 2-1 2-1 0-1 0-2-1h0v-1l2-3c0-1 1-3 2-4l1-4z" class="F"></path><path d="M174 427v2c0 1-1 2-1 2-1 0-1 0-2-1h0l2-1c0-1 0-1 1-2z" class="B"></path><path d="M181 400c1-1 3 0 4-1v1c-1 1-1 2-1 4l-2 2c-2 4-3 8-5 11l-1 1-1 4-1-1-1-1c1-3 2-5 2-8 0-1 1-2 1-3l1-3c0-1 1-2 2-4h0l1-1 1-1z" class="AB"></path><path d="M181 400c1-1 3 0 4-1v1c-1 1-1 2-1 4l-2 2c-2 4-3 8-5 11l-1 1c0-4 2-8 4-11 1-1 1-2 1-3s-1-1-2-2h0l1-1 1-1z" class="x"></path><path d="M181 400c1-1 3 0 4-1v1c-1 1-1 2-1 4l-2 2c0-2 0-3 1-4h0v-1h0c-1 1-1 1-2 1h-2l1-1 1-1z" class="E"></path><path d="M232 408l2-1v3h1v1c1-1 1-1 2-1v1l1 3s-1 0-2 1c0 1-1 2-1 2l-1 1 1 1c-1 1-1 2-2 2l-3 2-1 1-1 2v1l1 1v1c-1 1-1 2-2 2h0c-3 3-5 8-6 12-1 2-2 4-2 7l-1 1-1-1c-1-1-2 0-4-2h-1c-1 0-1 0-2-1l1-1 1-1v-1c0-1 0-2 1-2l1-3 2-2 1-2h0l-1-1c2-2 2-2 2-4v-1c-1 0-1 0-2-1l4-4-1-1v-1h-1l2-4h1 0l4-3h0l1-1h0c2-1 2-3 4-4h1c1-1 1-1 1-2z" class="G"></path><path d="M217 435l1 1h-1c0 1 0 2-1 3v1h0l2-1v3 1h-2c0 1-1 1-1 2h-2l1-1c-1-1-1-1-1-2l1-3 2-2 1-2z" class="t"></path><path d="M218 439v3 1h-2v-1-2h0l2-1z" class="V"></path><path d="M228 427l1 1v1c-1 1-1 2-2 2h0c-3 3-5 8-6 12-1 2-2 4-2 7l-1 1-1-1c-1-1-2 0-4-2h-1c-1 0-1 0-2-1l1-1 1-1c0 2 1 1 2 3 2-1 2-3 4-5l1-1v-1h2 0c0-1 0-2 1-3 1-3 3-6 5-10l1-1z" class="C"></path><path d="M218 443l1-1v-1h2c-1 2-1 3-2 5-1 0-1 1-1 1h-1c0-1 0-1 1-2v-2z" class="t"></path><path d="M211 446l1-1c0 2 1 1 2 3l1 1c1 0 2 0 3-1 1 1 1 2 1 2l-1 1-1-1c-1-1-2 0-4-2h-1c-1 0-1 0-2-1l1-1z" class="c"></path><path d="M229 422l1 1-1 1-1 2v1l-1 1h-1c0 1-1 2-2 3s-2 5-4 6h0c-1 1-1 2-2 3 1 0 1 1 0 2v-3l-2 1h0v-1c1-1 1-2 1-3h1l-1-1h0c2-3 5-7 7-9l1-1c2-1 3-2 4-3z" class="J"></path><path d="M229 422l1 1-1 1c-1 1-2 1-3 2l-1-1c2-1 3-2 4-3z" class="d"></path><path d="M218 439c-1-2 4-8 6-10h0l-4 8c-1 1-1 2-2 3 1 0 1 1 0 2v-3z" class="K"></path><path d="M228 419c1 0 1 0 2 1l1-1s1-1 1-2h0c1 0 2-1 3-1v1l-1 1 1 1c-1 1-1 2-2 2l-3 2-1-1c-1 1-2 2-4 3l-1 1c-2 2-5 6-7 9l-1-1c2-2 2-2 2-4v-1c-1 0-1 0-2-1l4-4 4-3c2 0 3-1 4-2z" class="B"></path><path d="M229 422l5-4 1 1c-1 1-1 2-2 2l-3 2-1-1z" class="G"></path><path d="M218 429l1 1v1c1-1 1-2 1-2l1-1h-1v-1c2-1 3-1 4-1-2 2-5 6-7 9l-1-1c2-2 2-2 2-4v-1z" class="m"></path><path d="M232 408l2-1v3h1v1c1-1 1-1 2-1v1l1 3s-1 0-2 1c0 1-1 2-1 2v-1c-1 0-2 1-3 1h0c0 1-1 2-1 2l-1 1c-1-1-1-1-2-1-1 1-2 2-4 2l-4 3-1-1v-1h-1l2-4h1 0l4-3h0l1-1h0c2-1 2-3 4-4h1c1-1 1-1 1-2z" class="G"></path><path d="M235 411c1-1 1-1 2-1v1l1 3s-1 0-2 1c0 1-1 2-1 2v-1c-1 0-2 1-3 1h0c0 1-1 2-1 2l-1 1c-1-1-1-1-2-1 3-2 6-5 7-8z" class="F"></path><path d="M232 408l2-1v3h1l-2 1c-4 4-9 8-14 11h-1l2-4h1 0l4-3h0l1-1h0c2-1 2-3 4-4h1c1-1 1-1 1-2z" class="Z"></path><path d="M232 408l2-1v3h1l-2 1-1-1h-2 1c1-1 1-1 1-2z" class="H"></path><path d="M226 414l1 1c-2 1-3 3-6 4v-1h0l4-3h0l1-1h0z" class="O"></path><path d="M221 443c1-4 3-9 6-12h0v1 1l2 1 1-1 3 12-1-1-1 1c-1 1-1 2-1 3l1 1c0 1-1 1-1 2h1 1l1-1h1v2l1-1 1 1c-2 2-2 3-3 5 0 1-1 2-1 3v1l1-1c1 0 1 1 2 2l1 1-1 1-1 1c-1 1-1 1-1 2h0l-1 1c-1 0-2 1-2 3h0l-2 3c-1 2-2 5-2 7h-1v-2h-1l-1-1h0c-1-1-1-2-1-3v-1l-3-3v-1h-1v-1h0c-1-2 0-4 1-6v-2h0c-1-2 1-3 1-5l-1-1 3-6-1-1h0c0-2 0-2 1-4l-1-1z" class="C"></path><path d="M232 462v3h0-2l1-3h1z" class="H"></path><path d="M225 474l1-1v2c0 1-1 2-2 3 0-1 1-2 0-3v-1h1z" class="J"></path><path d="M222 471c0 1 0 2 1 2s1 1 1 1h1-1l-1 2h-1v-1-1-3z" class="G"></path><path d="M234 465c-1 0-1 0-1-1v-1l2-1 1 1-1 1-1 1z" class="R"></path><path d="M228 465l2-3h1l-1 3c-1 2-2 3-2 5-1 2-1 3-2 5v-2l-1 1c0-1-1-2 0-2 0-2 0-3 1-4l2-3h0z" class="K"></path><path d="M226 468l2-3h0c-1 3-2 5-2 7v1l-1 1c0-1-1-2 0-2 0-2 0-3 1-4z" class="G"></path><path d="M226 463c1 0 1 0 1 1-1 1-1 2-1 3v1c-1 1-1 2-1 4-1 0 0 1 0 2h-1s0-1-1-1-1-1-1-2h-1l2-5v-1c1-1 2-1 3-2z" class="K"></path><path d="M226 463c1 0 1 0 1 1-1 1-1 2-1 3l-3 4c0-2 1-4 1-5v-1l-1 1v-1c1-1 2-1 3-2z" class="O"></path><path d="M230 448l1 1c0 1-1 1-1 2h1 1l1-1h1v2l1-1 1 1c-2 2-2 3-3 5 0 1-1 2-1 3v1 1h-1-1l-2 3h0l-2 3v-1c0-1 0-2 1-3 0-1 0-1-1-1-1 1-2 1-3 2l1-3h0l1-3c1-2 1-3 2-4 0 0 1 0 1-1l-3-1 2-1h0c1 0 2-1 2-2v-1l1-1z" class="J"></path><path d="M232 451l1-1h1v2l-1 3c-1 0-1-1-2-1l1-3z" class="K"></path><path d="M231 454c1 0 1 1 2 1-1 2-1 4-3 5-1 1-2 3-2 5l-2 3v-1c0-1 0-2 1-3s1-3 2-4h1c0-2 0-2-1-3l2-3h0z" class="b"></path><path d="M229 457h0c1 1 1 1 1 3h-1c-1 1-1 3-2 4 0-1 0-1-1-1-1 1-2 1-3 2l1-3h0l1-3h1 0c1 0 1-1 1-1 0-1 1-1 1-1h1z" class="J"></path><path d="M226 459h0c1 0 1-1 1-1 0-1 1-1 1-1h1l-3 6-1-1c0-1 1-2 1-3z" class="E"></path><path d="M225 459h1c0 1-1 2-1 3l1 1c-1 1-2 1-3 2l1-3h0l1-3z" class="Z"></path><path d="M230 448l1 1c0 1-1 1-1 2h1 1l-1 3h0l-2 3h0-1s-1 0-1 1c0 0 0 1-1 1h0-1c1-2 1-3 2-4 0 0 1 0 1-1l-3-1 2-1h0c1 0 2-1 2-2v-1l1-1z" class="c"></path><path d="M230 448l1 1c0 1-1 1-1 2h1 1l-1 3h0-1-1c-1-1 1-2 1-3l-1-1v-1l1-1z" class="L"></path><path d="M220 456c1-2 1-6 3-7h1c-1 2-2 3-1 4v1l1-2 1 1 3 1c0 1-1 1-1 1-1 1-1 2-2 4l-1 3h0l-1 3v1l-2 5h1v3l-3-3v-1h-1v-1h0c-1-2 0-4 1-6v-2h0c-1-2 1-3 1-5z" class="u"></path><path d="M219 461l1-2 1 1v2c-1 2-2 5-3 7h0c-1-2 0-4 1-6v-2z" class="V"></path><path d="M219 471c1-2 1-3 1-4l1-1c0-2 1-3 3-4l-1 3v1l-2 5h1v3l-3-3z" class="H"></path><path d="M224 452l1 1 3 1c0 1-1 1-1 1-1 1-1 2-2 4l-1 3-1-1s0-1-1-1v-1c-1-1 1-3 1-5h0l1-2z" class="w"></path><path d="M221 443c1-4 3-9 6-12h0v1 1l2 1 1-1 3 12-1-1-1 1c-1 1-1 2-1 3l-1 1v1c0 1-1 2-2 2h0l-2 1-1-1-1 2v-1c-1-1 0-2 1-4h-1c-2 1-2 5-3 7l-1-1 3-6-1-1h0c0-2 0-2 1-4l-1-1z" class="K"></path><path d="M224 452c2-2 4-5 5-8h1c-1 2-1 3-2 3v3h1v-1 1c0 1-1 2-2 2h0l-2 1-1-1z" class="h"></path><path d="M227 452l1-5v3h1v-1 1c0 1-1 2-2 2h0z" class="G"></path><path d="M227 432v1l2 1 1-1 3 12-1-1-1-1h-1c1-1 1-1 1-2h-1l-4 6h-1c1-2 3-4 4-6 0-1 1-2 0-3h-1-1l1-1v-3h0-2l1-2z" class="C"></path><path d="M221 443c1-4 3-9 6-12h0v1l-1 2h2 0c-1 1-4 5-4 6v1l1 1c1-1 1-2 2-3v1l-3 5c1 0 1 0 0 1v2 1h0-1c-2 1-2 5-3 7l-1-1 3-6-1-1h0c0-2 0-2 1-4l-1-1z" class="V"></path><path d="M224 440h-1c1-2 2-5 3-6h2 0c-1 1-4 5-4 6z" class="L"></path><path d="M222 444c0-1 0-1 1-2v1l-1 4h0c1 0 2-2 2-2 1 0 1 0 0 1v2 1h0-1c-2 1-2 5-3 7l-1-1 3-6-1-1h0c0-2 0-2 1-4z" class="G"></path><path d="M236 452c0 2 2 3 4 5v1c1 0 2 1 3 2s1 1 2 0l1 1c2 1 3 2 5 2h0v1l1 1c1-1 2-1 2 0h1l1-1h2v5l-1 3h2v2h1l1 1-1 1-2 1v2c0 1-1 3-1 4v1l-1 2-1 1c-1 2-1 3-1 4-1-1 0-2-1-3v-2h0c-1 1-1 2-2 2-1 1-1 2-2 3l-3 3h0-3-2v-1-1h1v-1l-2-1c-2 1-3 4-5 4l-1 1v-1h-1l-1 1c-1 0-1 1-2 1l-1 1-1-1h0c0-2-1-4-1-5v-1c-1-1-1-1-2-1-1-2 0-4 0-6v-2h1c0-2 1-5 2-7l2-3h0c0-2 1-3 2-3l1-1h0c0-1 0-1 1-2l1-1 1-1-1-1c-1-1-1-2-2-2l-1 1v-1c0-1 1-2 1-3 1-2 1-3 3-5z" class="H"></path><path d="M236 463h1c0 1 1 1 0 1v1l-2-1 1-1z" class="C"></path><path d="M241 460h-2c0-1 0-2 1-2s2 1 3 2l-1 1-1-1z" class="G"></path><path d="M245 467v1h1 1l-5 4v-2l1-1c1-1 1-2 2-2z" class="D"></path><path d="M243 460c1 1 1 1 2 0l1 1c-1 1 0 1-1 2h-2-1l-1-1-1 1-1-1s0-1 1-1l1-1 1 1 1-1z" class="L"></path><path d="M245 467c1-1 1-2 2-2s1-1 2-1c0-1 0-1 1-1l1 1 1 1-5 3h-1-1v-1z" class="r"></path><path d="M236 452c0 2 2 3 4 5l-2 1c0-1 0-1-1-1h-1v-1l1-1h-1c-2 1-2 3-3 5l-1 1v-1c0-1 1-2 1-3 1-2 1-3 3-5z" class="K"></path><path d="M252 465c1-1 2-1 2 0h1c-1 1-2 3-3 3-2 2-3 3-5 4-1 1-2 2-3 2s-2 0-3 1v-1l1-2 5-4 5-3z" class="X"></path><path d="M237 464l1 1h2v1h0 1 0c0 2 2 1 1 3l-2 2h-1 0l-2 2h0l-1-2c-1 1-1 1-2 1v-1-1h-1l-2 1h-1 0c0-2 1-3 2-3l1-1h0c0-1 0-1 1-2l1-1 2 1v-1z" class="J"></path><g class="L"><path d="M235 464l2 1c-1 1-2 1-3 1l-1 1c0-1 0-1 1-2l1-1z"></path><path d="M237 464l1 1h2v1h0v1l-1-1c-1 1-1 1-2 1-1-1 0-1 0-2v-1z"></path></g><path d="M231 471c1-1 2-4 5-4l1 1c0-1 2 0 3 0l-1 1v2h0l-2 2h0l-1-2c-1 1-1 1-2 1v-1-1h-1l-2 1z" class="G"></path><path d="M237 471l2-2v2h-2z" class="O"></path><path d="M237 473v-2h2 0l-2 2z" class="J"></path><path d="M234 471l1-2 1 1v1c-1 1-1 1-2 1v-1z" class="O"></path><path d="M239 471h1v3l1 1h0c1-1 2-1 3-1s2-1 3-2v1c0 1 0 1-1 2h-1 0l-1 1v1 1c-1 0-1 1-1 2l-2 1c0 1-1 1-1 2-1 0-1-1-2-1l-1 2c-1 0-1-1-1-1-1-1-2-1-3-1l3-3-2-1v-1l3-4h0l2-2h0z" class="C"></path><path d="M239 471h1v3l-2 2h0-1c-2 0-2 0-3 2v-1l3-4h0l2-2h0z" class="L"></path><path d="M239 471h1v3l-2 2h0-1c1-2 1-3 2-5h0z" class="c"></path><path d="M247 472v1c0 1 0 1-1 2h-1 0l-1 1v1c-1 1-3 1-4 2h0c-1 0-2 2-3 2-1-1-1-2-1-2 1-2 3-2 4-3l1-1h0c1-1 2-1 3-1s2-1 3-2z" class="AC"></path><path d="M231 471l2-1h1v1 1c1 0 1 0 2-1l1 2-3 4v1l2 1-3 3c1 0 2 0 3 1 0 1 0 2 1 3l-1 2h-1 1v3c0 1-1 2-1 3l-1 1v-1h-1l-1 1c-1 0-1 1-2 1l-1 1-1-1h0c0-2-1-4-1-5v-1c-1-1-1-1-2-1-1-2 0-4 0-6v-2h1c0-2 1-5 2-7l2-3h1z" class="G"></path><path d="M230 491l1 1c0-1 0-1 2-1h0v3l-1 1c-1 0-1 1-2 1-1-2 0-3 0-5z" class="K"></path><path d="M235 488h1v3c0 1-1 2-1 3l-1 1v-1h-1v-3l2-3z" class="C"></path><path d="M228 488c1-1 1-2 2-2h0c1 2 1 3 0 5 0 2-1 3 0 5l-1 1-1-1h0c0-2-1-4-1-5v-1l1-2z" class="I"></path><path d="M233 477h1v1l2 1-3 3c1 0 2 0 3 1 0 1 0 2 1 3l-1 2c-1-1-1-2-2-3-1 0-1 0-2 1h-1c0-1 0-2 1-3 0-1 0-2-1-2l2-4z" class="J"></path><path d="M231 481l2-4v3 2l-1 1c0-1 0-2-1-2z" class="O"></path><path d="M231 471l2-1h1v1 1c1 0 1 0 2-1l1 2-3 4h-1l-2 4-1 1c-1 1-2 3-2 4h0v2l-1 2c-1-1-1-1-2-1-1-2 0-4 0-6v-2h1c0-2 1-5 2-7l2-3h1z" class="b"></path><path d="M229 474l1-1 3-3-1 5c-1 2-3 6-5 7 0 0-1 0-1 1v-2c0-2 1-5 2-7h1z" class="f"></path><path d="M228 474h1v1c0 2-1 3-1 5 0 0-1 1-1 2 0 0-1 0-1 1v-2c0-2 1-5 2-7z" class="M"></path><path d="M232 475c-1 2-2 5-2 7-1 1-2 3-2 4h0v2l-1 2c-1-1-1-1-2-1-1-2 0-4 0-6v-2h1v2c0-1 1-1 1-1 2-1 4-5 5-7z" class="P"></path><path d="M225 483v3h0c1-1 1-1 1-2h1c0 1-1 3 0 4l1-2v2l-1 2c-1-1-1-1-2-1-1-2 0-4 0-6z" class="c"></path><path d="M255 465l1-1h2v5l-1 3h2v2h1l1 1-1 1-2 1v2c0 1-1 3-1 4v1l-1 2-1 1c-1 2-1 3-1 4-1-1 0-2-1-3v-2h0c-1 1-1 2-2 2-1 1-1 2-2 3l-3 3h0-3-2v-1-1h1v-1l-2-1c-2 1-3 4-5 4 0-1 1-2 1-3v-3h-1 1l1-2c-1-1-1-2-1-3 0 0 0 1 1 1l1-2c1 0 1 1 2 1 0-1 1-1 1-2l2-1c0-1 0-2 1-2v-1-1l1-1h0 1c1-1 1-1 1-2v-1c2-1 3-2 5-4 1 0 2-2 3-3z" class="j"></path><path d="M255 484h2l-1 2-1 1v-3z" class="J"></path><path d="M255 484l2-5h1c0 1-1 3-1 4v1h-2z" class="G"></path><path d="M257 472h2v2h1l1 1-1 1-2 1v2h-1v-7z" class="V"></path><path d="M259 474h1l1 1-1 1-2 1 1-3z" class="C"></path><path d="M253 469l3-3 1 1c0 1 0 3-1 4h-1-1c0-1-1-1-1-2z" class="d"></path><path d="M254 473c1 2 0 3 0 5 0 3 0 5-1 8h0 0c-1 1-1 2-2 2v-2l1-3v-1h0l1-1 1-2c-1-2 0-4 0-6z" class="k"></path><path d="M252 482h1v1h0l-1-1h0z" class="R"></path><path d="M247 472c2-1 3-2 5-4l-1 1v1l2-1c0 1 1 1 1 2h1c-1 1-1 1-1 2 0 2-1 4 0 6l-1 2-1 1h0v1l-2-2v-1h-1-1c0-1-1-1-1-2-1 0-2 0-2-1v-2h1c1-1 1-1 1-2v-1z" class="C"></path><path d="M248 476h0c1-2 1-3 3-3 0 1 0 2-1 3v1h-1l-1-1z" class="H"></path><path d="M254 471h1c-1 1-1 1-1 2 0 2-1 4 0 6-1-1 0-2-1-3h-1l-1-1c0-2 1-2 2-2h0c1-1 1-1 1-2z" class="l"></path><path d="M247 472c2-1 3-2 5-4l-1 1v1l2-1c0 1 1 1 1 2s0 1-1 2h0c-1-1-1-1-2 0-2 0-2 1-3 3h0l-1 2c-1 0-2 0-2-1v-2h1c1-1 1-1 1-2v-1z" class="J"></path><path d="M244 477v-1l1-1h0v2c0 1 1 1 2 1 0 1 1 1 1 2h1 1v1l2 2-1 3v2c-1 1-1 2-2 3l-3 3h0-3-2v-1-1h1v-1l-2-1c-2 1-3 4-5 4 0-1 1-2 1-3v-3h-1 1l1-2c-1-1-1-2-1-3 0 0 0 1 1 1l1-2c1 0 1 1 2 1 0-1 1-1 1-2l2-1c0-1 0-2 1-2v-1z" class="b"></path><path d="M245 483c0-1 1-2 1-2v-1h1 0v2 1c-1 1-2 3-3 5h-1c0-2 3-3 3-5h-1z" class="J"></path><path d="M236 491l6-4 1 1c-1 1-2 1-3 2-2 1-3 4-5 4 0-1 1-2 1-3h0z" class="V"></path><path d="M244 477v-1l1-1h0v2c0 1 1 1 2 1 0 1 1 1 1 2h1 1v1 2l-1-1v-1-1l-2 2v-2h0-1v1s-1 1-1 2h-1c1-1 1-1 1-2-2 0-4 6-6 6 0-1 1-2 2-4h0c-3 2-5 4-5 7v1h0v-3h-1 1l1-2c-1-1-1-2-1-3 0 0 0 1 1 1l1-2c1 0 1 1 2 1 0-1 1-1 1-2l2-1c0-1 0-2 1-2v-1z" class="D"></path><defs><linearGradient id="W" x1="249.997" y1="482.625" x2="248.15" y2="488.688" xlink:href="#B"><stop offset="0" stop-color="#797a79"></stop><stop offset="1" stop-color="#908f8f"></stop></linearGradient></defs><path fill="url(#W)" d="M247 482l2-2v1 1l1 1v-2l2 2-1 3v2c-1 1-1 2-2 3l-3 3h0-3-2v-1-1h1v-1l2-1c1-1 2-1 3-3 0-1 0-2 1-3v-1h-1v-1z"></path><path d="M249 491h-1c0-2 2-3 3-5v2c-1 1-1 2-2 3z" class="L"></path><path d="M247 482l2-2v1 1l1 1c0 1-1 2-1 3l-2 1c0-1 0-2 1-3v-1h-1v-1z" class="O"></path><path d="M242 492c1-1 3-1 4-2v1l-1 1s1 0 1 1v1h-3-2v-1-1h1z" class="I"></path><path d="M205 368h4l5 1 2 2s0 1 1 1h1c1 0 1 0 2-1h0l1 1 7 4 1-1v1c1 0 1 1 2 1l1 1h-1-1v1l1 1h-1 0l-1 1 1 1c2 1 3 3 3 5v1 2l2 2c1 1 1 2 1 4h0c-1 1 0 3 0 5 1 1 2 3 1 5l1 1-1 2v2-1c-1 0-1 0-2 1v-1h-1v-3l-2 1c0 1 0 1-1 2h-1c-2 1-2 3-4 4h0l-1 1h0l-4 3h0-1-1v1h0c-1 1-2 3-3 2h0c-1 1-2 1-3 2h0l-2 3c-1 0-1 1-1 1v1c0 1-1 2-1 3h1c-1 1-2 2-4 2v1h-1v-1c-1 1-1 2-2 3v-1l-1-1-1-1 1-2c-1 0-1-1-1-2h0 0v-3h-1v-2c0-1 0-3 1-5v-1l-1-3h0c1-1 1 0 1-1 1-2 1-3 3-5 0-1 1-2 2-4l3-2h0c-1-1-2 0-3 0h0l-1-1c-1 1-1 1-2 1 0-1 0-2 1-3l-2 2c0-1-1-2-2-2-1 1-2 2-4 3l-1 1h-1c-2 0-2 1-4 1v-1c1-1 2-3 4-4h0l1-1c1-1 2-2 2-3l-1-1v-1l2-2 1-1c-1-1-2-1-2-2v-2l-2 3-1-1c0-1 1-1 1-2v-1l1-1c1-1 1-2 2-4l1-1h1 2l-1-1v-1h-2v-1c-2 0-5 0-7 1h-2-1v-2h-1l1-1c0-1 0-1-1-1l1-1 2-1 3-2h1c1-1 2-1 3-1h0 2l5-1z" class="H"></path><path d="M220 402l3-3c1 0 2 1 3 1h1c-1 0-1 1-2 2l-2 1h-1c-1-1-1-1-2-1z" class="m"></path><path d="M227 400c-1 0-1 1-2 2l-1-1v-1h2 1z" class="c"></path><path d="M227 394v-1h1v3c0 1-1 2-1 4h-1c-1 0-2-1-3-1l4-5z" class="F"></path><path d="M229 399c0 1 1 2 0 3s-1 1-2 3h-1l-5 3v-1c2-3 5-5 8-8z" class="E"></path><path d="M220 402c1 0 1 0 2 1h1c-1 2-4 4-6 5h-1c-1-1-1-1-2-1v-2l1 1c2-1 3-3 5-4z" class="S"></path><path d="M217 409c1-1 2-2 4-2v1l-2 2-5 4c-1 1-2 3-4 4l-2 2s-1-1-2-1c1-2 1-3 2-5h0l1 1 2 1 1-1h0l-1-1c0-1 0-1 1-2s1-2 1-4l1-1c1 0 1 0 2 1-1 0-1 1-2 2v1h1l2-2z" class="R"></path><path d="M217 409v2h-1-1l2-2zm-3-2c1 0 1 0 2 1-1 0-1 1-2 2v1h-1v-3l1-1z" class="T"></path><path d="M222 387c2 1 5 4 6 6h-1v1l-4 5-3 3c-2 1-3 3-5 4l-1-1c1-1 2-2 3-4 0 0-1-1-1-2 1 0 1-1 2-1l2-3 1-1 1-4h1c0-1 0-2-1-3z" class="C"></path><path d="M216 399c1 0 1-1 2-1 0 2 0 2-1 3 0 0-1-1-1-2z" class="E"></path><path d="M222 387c2 1 5 4 6 6h-1v1l-1-1-1 1v-1h0c-1 1-2 1-2 2s-1 2-1 2h-1v-1l-1-1 1-1 1-4h1c0-1 0-2-1-3z" class="T"></path><path d="M223 390h1c-1 1-1 2-1 3h0l1-2h1v2h0c-1 1-2 1-2 2s-1 2-1 2h-1v-1l-1-1 1-1 1-4h1z" class="H"></path><defs><linearGradient id="X" x1="226.145" y1="395.624" x2="230.905" y2="392.535" xlink:href="#B"><stop offset="0" stop-color="#cacac9"></stop><stop offset="1" stop-color="#f2f1f2"></stop></linearGradient></defs><path fill="url(#X)" d="M214 382v-1h3l3 1c1 1 4 2 4 3 2 0 2-1 3 0 3 1 5 3 6 5l2 2c1 1 1 2 1 4h0c-1 1 0 3 0 5 1 1 2 3 1 5l1 1-1 2v2-1c-1 0-1 0-2 1v-1h-1v-3l-2 1v-3-1h0-1-1l-2 1h-1c1-2 1-2 2-3s0-2 0-3l1-1-2-2v-3c-1-2-4-5-6-6 1 1 1 2 1 3h-1-3c1-1 2-2 2-3h-1-1-1v-2h-1 0v-1l-1-1-2-1z"></path><path d="M235 407l2 3c-1 0-1 0-2 1v-1h-1v-3h1z" class="C"></path><path d="M228 393c1 1 2 2 3 4l-1 1-2-2v-3z" class="l"></path><path d="M214 382v-1h3l3 1c-2 0-2 0-4 1l-2-1z" class="I"></path><path d="M233 403l2 4h-1l-2 1v-3-1h1v-1z" class="S"></path><path d="M217 384c2 0 4 2 5 3s1 2 1 3h-1-3c1-1 2-2 2-3h-1-1-1v-2h-1 0v-1z" class="i"></path><path d="M230 398l1-1 1 3 1 3v1h-1 0-1-1l-2 1h-1c1-2 1-2 2-3s0-2 0-3l1-1z" class="F"></path><path d="M232 400l1 3v1h-1 0-1-1c0-2 1-3 2-4z" class="E"></path><path d="M232 404l-1-1h1v1h0z" class="j"></path><path d="M224 385c2 0 2-1 3 0 3 1 5 3 6 5l2 2c1 1 1 2 1 4h0c-1 1 0 3 0 5 1 1 2 3 1 5h0v-3c0 1-1 1-1 2-1-4-2-7-4-11-1-3-3-5-6-7 0-1-1-1-1-2h-1z" class="p"></path><path d="M217 395c1 1 1 1 1 3-1 0-1 1-2 1 0 1 1 2 1 2-1 2-2 3-3 4v2l-1 1c0 2 0 3-1 4s-1 1-1 2l1 1h0l-1 1-2-1-1-1h0c-1 2-1 3-2 5l-4 15-1-1 1-2c-1 0-1-1-1-2h0 0v-3h-1v-2c0-1 0-3 1-5v-1l-1-3h0c1-1 1 0 1-1 1-2 1-3 3-5 0-1 1-2 2-4l3-2h0c-1-1-2 0-3 0h0l-1-1 2-1c2-2 4-3 6-4h2l2-2z" class="F"></path><path d="M202 413c1-1 1-3 3-4l1 1h0c-1 1-1 3-2 4l-2-1z" class="j"></path><path d="M217 395c1 1 1 1 1 3-1 0-1 1-2 1 0 1-1 1-1 1 0 1-1 1-1 2l-2 1-1 1v-1h0v-1l1-1h-1c-1 0-2 1-3 1l-1-1c2-2 4-3 6-4h2l2-2z" class="Y"></path><path d="M217 395c1 1 1 1 1 3-1 0-1 1-2 1 0 1-1 1-1 1 0 1-1 1-1 2l-1-1v-1s0 1 1 1l1-1-1-1h-1 1 0c1-1 1-2 1-2l2-2z" class="v"></path><path d="M216 399c0 1 1 2 1 2-1 2-2 3-3 4v2l-1 1c0 2 0 3-1 4s-1 1-1 2l1 1h0l-1 1-2-1-1-1 2-3h-2-1l-1-1h0c1-2 2-3 3-4h0l2-2 1-1 2-1c0-1 1-1 1-2 0 0 1 0 1-1z" class="F"></path><path d="M212 403l1 1s-1 1-1 2c-1 0-1 1-1 2h0-3c1 0 1-1 2-1l-1-1h0l2-2 1-1z" class="R"></path><path d="M209 406c1 0 1 0 2 1v1h0-3c1 0 1-1 2-1l-1-1h0z" class="c"></path><path d="M209 406l1 1c-1 0-1 1-2 1h3l-1 3h-2-1l-1-1h0c1-2 2-3 3-4z" class="C"></path><path d="M208 408h3l-1 3h-2v-3z" class="S"></path><path d="M204 414c1-1 1-3 2-4l1 1h1 2l-2 3h0c-1 2-1 3-2 5l-4 15-1-1 1-2c-1 0-1-1-1-2h0 0v-3h-1v-2c0-1 0-3 1-5v-1-2c1-1 1-2 1-3l2 1z" class="d"></path><path d="M208 411h2l-2 3h0v-1c-1 1-1 1-1 2s-1 2-2 3l1-3-1-1h0c1-1 2-2 2-3h1z" class="c"></path><path d="M205 414l1 1-1 3c0 2 0 4-1 6-1-2-1-3-1-4 0-2 1-4 2-6z" class="B"></path><path d="M203 420c0 1 0 2 1 4l-2 7c-1 0-1-1-1-2h0 0v-3h-1v-2l3-4z" class="x"></path><path d="M204 414c1-1 1-3 2-4l1 1c0 1-1 2-2 3h0c-1 2-2 4-2 6l-3 4c0-1 0-3 1-5v-1-2c1-1 1-2 1-3l2 1z" class="D"></path><path d="M202 413l2 1c-1 2-2 3-3 5h0v-1-2c1-1 1-2 1-3z" class="S"></path><path d="M230 404h1 1 0v1 3c0 1 0 1-1 2h-1c-2 1-2 3-4 4h0l-1 1h0l-4 3h0-1-1v1h0c-1 1-2 3-3 2h0c-1 1-2 1-3 2h0l-2 3c-1 0-1 1-1 1v1c0 1-1 2-1 3h1c-1 1-2 2-4 2v1h-1v-1c-1 1-1 2-2 3v-1l-1-1 4-15c1 0 2 1 2 1l2-2c2-1 3-3 4-4l5-4 2-2 5-3h1 1l2-1z" class="O"></path><path d="M205 434c1-2 0-3 1-4h1l-1 3v1h-1zm4-12c1 1 1 2 0 3v3h-2v-1c0-1 1-2 1-3l1-2z" class="d"></path><path d="M210 427v1c0 1-1 2-1 3h1c-1 1-2 2-4 2l1-3 3-3z" class="s"></path><path d="M217 417v1c0 1-1 2-1 3-1 1-2 1-3 2h0c1-3 2-4 4-6z" class="S"></path><path d="M206 419c1 0 2 1 2 1l2-2v2c0 1-1 2-1 2l-1 2c0 1-1 2-1 3l-1 1v-1l-1-1-2 9-1-1 4-15z" class="T"></path><path d="M205 426c1-1 2-2 2-3h1v1h0c0 1-1 2-1 3l-1 1v-1l-1-1z" class="J"></path><path d="M219 410l3-1v1c-1 1-1 1-2 1l1 1 1-1h0v1 1l1-1h1v1 1l1 1h0l-4 3h0-1-1v1h0c-1 1-2 3-3 2h0c0-1 1-2 1-3v-1c1-2 3-3 4-5-3 2-7 5-9 8h-2v-2c2-1 3-3 4-4l5-4z" class="H"></path><path d="M224 412v1 1l1 1h0l-4 3-1-1h-1-1l1-1 3-3 1-1h1z" class="Z"></path><path d="M224 412v1 1l1 1h0-1c-1-2 0-2 0-3z" class="L"></path><path d="M230 404h1 1 0v1 3c0 1 0 1-1 2h-1c-2 1-2 3-4 4h0l-1 1-1-1v-1-1h-1l-1 1v-1-1h0l-1 1-1-1c1 0 1 0 2-1v-1l-3 1 2-2 5-3h1 1l2-1z" class="c"></path><path d="M230 404h1v2c-1 1-2 2-3 2l-1-1h-1c0-1 0-1 1-2h-1 1 1l2-1z" class="S"></path><path d="M232 404v1 3c0 1 0 1-1 2h-1c-2 1-2 3-4 4h0l-1 1-1-1v-1-1h-1c1-1 2-2 2-3 1 0 1-1 2-1v1l1-1c1 0 2-1 3-2v-2h1 0z" class="C"></path><path d="M232 404v1l-1 1h0v-2h1 0zm-9 8c1-1 2-2 2-3 1 0 1-1 2-1v1h1 0v2c-2 0-2 1-3 2 0 1 0 1 1 1l-1 1-1-1v-1-1h-1z" class="H"></path><path d="M228 409h0v2h-2l2-2z" class="L"></path><path d="M205 368h4l5 1 2 2s0 1 1 1h1c1 0 1 0 2-1h0l1 1 7 4 1-1v1c1 0 1 1 2 1l1 1h-1-1v1l1 1h-1 0l-1 1 1 1c2 1 3 3 3 5v1 2c-1-2-3-4-6-5-1-1-1 0-3 0 0-1-3-2-4-3l-3-1h-3v1l-3-2h-4-2-1c-1 0-1 0-1 1l-1-1-1-1v-1h-2v-1c-2 0-5 0-7 1h-2-1v-2h-1l1-1c0-1 0-1-1-1l1-1 2-1 3-2h1c1-1 2-1 3-1h0 2l5-1z" class="q"></path><path d="M229 375v1c1 0 1 1 2 1l1 1h-1-1c0-1-1-1-2-2l1-1z" class="W"></path><path d="M211 378l7 2-1 1h-3v1l-3-2v-2z" class="G"></path><path d="M218 380c5 2 11 4 15 8v2c-1-2-3-4-6-5-1-1-1 0-3 0 0-1-3-2-4-3l-3-1 1-1z" class="H"></path><path d="M220 371l1 1v1 2 1h-1c-2-1-4-1-6-1l-2-1c-2 1-4 2-6 2l-1 1c2 0 4 1 6 1v2h-4-2-1c-1 0-1 0-1 1l-1-1-1-1v-1h-2v-1c1 0 1 0 1-1h1c3 0 4 0 6-2l6-1 1-1h3 1c1 0 1 0 2-1h0z" class="O"></path><path d="M218 373c1 0 1 0 2 1-2 1-2 0-4 0 1-1 1-1 2-1z" class="I"></path><path d="M201 378c2 0 4 1 6 2h-2-1c-1 0-1 0-1 1l-1-1-1-1v-1z" class="U"></path><path d="M217 372h1v1c-1 0-1 0-2 1-1 0-2 0-3-1l1-1h3z" class="G"></path><path d="M205 368h4l5 1 2 2s0 1 1 1h-3l-1 1-6 1c-2 2-3 2-6 2h-1c0 1 0 1-1 1-2 0-5 0-7 1h-2-1v-2h-1l1-1c0-1 0-1-1-1l1-1 2-1 3-2h1c1-1 2-1 3-1h0 2l5-1z" class="J"></path><path d="M213 371h0l1 1-1 1-6 1c0-1-1-1-2-1 2 0 5-1 8-2z" class="K"></path><path d="M200 374c2 0 3-1 5-1 1 0 2 0 2 1-2 2-3 2-6 2h-1s-1 0-2-1l2-1z" class="M"></path><path d="M193 374h0c1 0 4-2 6-1-1 1-4 2-5 3h0c2-1 4-2 6-2l-2 1c1 1 2 1 2 1 0 1 0 1-1 1-2 0-5 0-7 1h-2-1v-2h-1l1-1 3-2 1 1z" class="Q"></path><defs><linearGradient id="Y" x1="205.732" y1="372.788" x2="204.598" y2="368.123" xlink:href="#B"><stop offset="0" stop-color="#ada9ab"></stop><stop offset="1" stop-color="#c6c7c7"></stop></linearGradient></defs><path fill="url(#Y)" d="M205 368h4l5 1 2 2s0 1 1 1h-3l-1-1h0-6v1h-2-1c-2 0-2 1-4 1v-1c2-1 3-1 4-1s1 0 2-1c-2 0-6 1-8 1-1 0-3 2-5 3l-1-1-3 2c0-1 0-1-1-1l1-1 2-1 3-2h1c1-1 2-1 3-1h0 2l5-1z"></path><path d="M216 371s0 1 1 1h-3l-1-1h3z" class="P"></path><path d="M195 370c1-1 2-1 3-1-2 1-3 3-5 4h-1 0l-3 2c0-1 0-1-1-1l1-1 2-1 3-2h1z" class="K"></path><path d="M207 380h4l3 2 2 1 1 1v1h0 1v2h1 1 1c0 1-1 2-2 3h3l-1 4-1 1-2 3c0-2 0-2-1-3l-2 2h-2c-2 1-4 2-6 4l-2 1c-1 1-1 1-2 1 0-1 0-2 1-3l-2 2c0-1-1-2-2-2-1 1-2 2-4 3l-1 1h-1c-2 0-2 1-4 1v-1c1-1 2-3 4-4h0l1-1c1-1 2-2 2-3l-1-1v-1l2-2 1-1c-1-1-2-1-2-2v-2l-2 3-1-1c0-1 1-1 1-2v-1l1-1c1-1 1-2 2-4l1-1h1 2l1 1c0-1 0-1 1-1h1 2z" class="L"></path><path d="M194 400h0l1-1c0 1 1 0 1 1l-1 1h-1v-1z" class="h"></path><path d="M200 397l1-1h2v1l-3 3c-1 1-2 2-4 3 0-2 2-3 3-4 0-1 1-1 1-2z" class="D"></path><path d="M200 400l3-3s1 1 2 1l-1 2-2 2c0-1-1-2-2-2z" class="Y"></path><path d="M207 384l1-1h2c0 1 0 2-1 3l-3 6c1 1 1 2 1 3v1l-1 1-1 1c-1 0-2-1-2-1v-1h-2l-1 1v-2h1c1-3 3-4 4-7 1-1 1-3 2-4z" class="J"></path><path d="M207 384l1-1h2c0 1 0 2-1 3 0 0 0-1-1-1v-1h-1z" class="D"></path><path d="M206 392c1 1 1 2 1 3v1l-1 1-1 1c-1 0-2-1-2-1v-1c1-1 2-3 3-4z" class="F"></path><path d="M206 397h-2l1-1s1 0 1-1h1v1l-1 1z" class="E"></path><path d="M199 380h1 2l1 1c0-1 0-1 1-1h1c1 1 1 1 1 2h2c1 0 1 0 2 1h0-2l-1 1c-1 1-1 3-2 4h0v-3c-1 1-1 2-2 2l-3 6-3 3-1-1v-1l2-2 1-1c-1-1-2-1-2-2v-2l-2 3-1-1c0-1 1-1 1-2v-1l1-1c1-1 1-2 2-4l1-1z" class="C"></path><path d="M201 388v2l-1 1c0-1-1-1-1-2 1 0 1-1 2-1z" class="D"></path><path d="M204 380h1c1 1 1 1 1 2v1c-1-1-1-1-2-1l-1 1s1-2 1-3z" class="S"></path><path d="M199 380h1 2l1 1h0c-1 2-3 4-3 6 1-1 2-3 3-4-1 2-1 4-2 5-1 0-1 1-2 1v2c-1-1-2-1-2-2v-2l-2 3-1-1c0-1 1-1 1-2v-1l1-1c1-1 1-2 2-4l1-1z" class="k"></path><path d="M199 380l1 1-3 6-2 3-1-1c0-1 1-1 1-2v-1l1-1c1-1 1-2 2-4l1-1z" class="J"></path><path d="M207 380h4l3 2 2 1 1 1v1h0 1v2h1 1 1c0 1-1 2-2 3h3l-1 4-1 1-2 3c0-2 0-2-1-3l-2 2h-2c-2 1-4 2-6 4l-2 1c-1 1-1 1-2 1 0-1 0-2 1-3l1-2 1-1 1-1v-1c0-1 0-2-1-3l3-6c1-1 1-2 1-3h0c-1-1-1-1-2-1h-2c0-1 0-1-1-2h2z" class="C"></path><path d="M212 384l3 1v1h-4c0-1 0-2 1-2z" class="T"></path><path d="M210 392c1 0 1-1 1 0h1c0 1-2 2-1 3v1l-2 2h-1l-1-1v-1h0v-1h0c0-1 0-1 1-2 0-1 0-1 1-1h1z" class="D"></path><path d="M207 395h0c0-1 0-1 1-2 0-1 0-1 1-1h0v2c0 1-1 1-2 2h0v-1z" class="T"></path><path d="M210 383c1 0 1 0 2 1-1 0-1 1-1 2l-1 1h0c1 2 1 3 0 5h0-1c-1 0-1 0-1 1-1 1-1 1-1 2h0c0-1 0-2-1-3l3-6c1-1 1-2 1-3h0z" class="j"></path><path d="M207 380h4l3 2 2 1 1 1v1h0l-1 1h-1v-1l-3-1c-1-1-1-1-2-1-1-1-1-1-2-1h-2c0-1 0-1-1-2h2z" class="F"></path><path d="M215 385v-1h1l1 1h0l-1 1h-1v-1z" class="B"></path><path d="M219 387h1 1c0 1-1 2-2 3h3l-1 4-1 1-2 3c0-2 0-2-1-3l-2 2h-2l1-2c-1 0-2 0-2-1 1-2 2-3 3-4s1-1 2-1v1h0c1-1 2-2 2-3z" class="Z"></path><path d="M218 392c0 1-1 2-1 3l-2 2h-2l1-2 4-3z" class="m"></path><path d="M218 391l1-1h3l-1 4-1 1-2 3c0-2 0-2-1-3 0-1 1-2 1-3v-1z" class="B"></path><path d="M218 391l1-1h3l-1 4c-1-1-1-1-1-2v-1h-2z" class="E"></path><path d="M251 488c1 0 1-1 2-2h0v2c1 1 0 2 1 3v1c-1 3 1 5 0 7h1l-1 4c1-1 1-2 2-3h0v-2-2l1 1c1 1 0 3 0 4v1-1h2v1l-1 3c0 3-1 5-2 8v1h1c0 2 0 3-1 4 1 1 1 1 2 1l-2 2c0 1-1 4-2 5v1h-2l-3 9c-2 4-5 8-8 11-1 1-2 3-2 3l-2 3c-1 0-2 1-3 1-4 3-8 6-12 10-2 1-6 4-7 6h-1-2l1-1c-3 0-7 3-9 6-1 0-3 1-3 2l-3 1c-1-1-1-1-2-1 2-1 3-1 4-2 0-1 0-1 1-1 1-1 2-2 2-3h1l5-5 2-2c2-3 5-7 7-11l5-13 2-5-2-2c1-2 1-4 1-6 0-1 1-2 1-3v-1h-2l1-1-1-1v-1h0l-1-1-1-1c-2 0-3 0-5 1-1 0-1 0-1-1l-1-3c2-1 3-3 5-4 2-2 4-1 6-1v-4c0-1 0-1-1-1v-1l1-1c1-1 1-2 1-3h1c0-2-1-3 1-4l1 1 1-1c1 0 1-1 2-1l1-1h1v1l1-1c2 0 3-3 5-4l2 1v1h-1v1 1h2 3 0l3-3c1-1 1-2 2-3z" class="L"></path><path d="M230 546c1 1 1 1 1 2-2 1-3 2-3 4-1 0-1 0-1 1-1 0-1 0-1 1-1 1-1 2-2 4l-5 4 3-3 3-6c1-2 3-4 3-6l2-1z" class="v"></path><path d="M227 540v1c0 1 0 2-1 3v1s-1 1-1 2 0 1-1 2-1 2-2 4h0v1c1-1 3-5 4-5 0 1-3 6-4 7l-3 4 1 1 2-2-3 3c0 1-5 6-6 7-3 0-7 3-9 6-1 0-3 1-3 2l-3 1c-1-1-1-1-2-1 2-1 3-1 4-2 0-1 0-1 1-1 1-1 2-2 2-3h1l5-5 2-2 1 1c2-3 6-6 8-9 0-2 2-5 3-6l4-10z" class="s"></path><path d="M211 564l1 1c-2 3-7 8-10 9h-1c1-1 2-2 2-3h1l5-5 2-2z" class="o"></path><path d="M214 515c2-1 3-3 5-4 2-2 4-1 6-1 3 2 5 3 7 6l2 2 1 2v2c1 2 0 5 1 8l-5 13s-1 2-1 3l-2 1v-1s1-1 1-2l3-8c1-2 1-5 1-8 0-2 0-6-2-8-1-1-3-2-5-3h-1v1c-1 0-1 1-2 1h-1l-1-1c-2 0-3 0-5 1-1 0-1 0-1-1l-1-3z" class="n"></path><path d="M235 522c1 2 0 5 1 8l-5 13s-1 2-1 3l-2 1v-1s1-1 1-2l3-8c1-2 1-5 1-8 0 1 1 2 1 3-1 1-1 0-1 1v4-1-1c1 0 0-1 1-2v-1c1-2 1-7 1-9z" class="a"></path><path d="M225 518v-1h1c2 1 4 2 5 3 0 2 0 2-1 3 0 6-1 11-3 17l-4 10c-1 1-3 4-3 6-2 3-6 6-8 9l-1-1c2-3 5-7 7-11l5-13 2-5-2-2c1-2 1-4 1-6 0-1 1-2 1-3v-1h-2l1-1-1-1v-1h0l-1-1h1c1 0 1-1 2-1z" class="N"></path><path d="M226 517c2 1 4 2 5 3 0 2 0 2-1 3l-1-3c0-1-1-1-3-2v-1z" class="l"></path><path d="M226 526l-1 9-2-2c1-2 1-4 1-6l2-1z" class="U"></path><path d="M225 518v-1h1v1 8l-2 1c0-1 1-2 1-3v-1h-2l1-1-1-1v-1h0l-1-1h1c1 0 1-1 2-1z" class="B"></path><path d="M222 519h1c1 0 1-1 2-1-1 1-1 2-1 4l-1-1v-1h0l-1-1z" class="N"></path><path d="M242 504v1c2 2 3 3 4 5-1 0-1 1-1 1h-1v2 1c0 2 0 4-1 5v-2h-1 0 0c0 2 0 2 1 3h1v1 2c-1 1-1 2-3 3h0l-2 5h0c-1 2-2 5-3 7s-1 3-1 4v1s0 1-1 2h0c0 1-1 2-2 3v1c-1 1-2 1-3 3h-1c0-2 1-3 3-4 0-1 0-1-1-2 0-1 1-3 1-3l5-13c-1-3 0-6-1-8v-2l-1-2c1 0 1 0 2-1s1-4 1-6l1-3 1-1 1-1 1-1 1-1z" class="j"></path><path d="M239 527c1 0 1 0 2-1l-2 5h0l-1-1-2 2v-1c0-1 1-1 1-2l2-2z" class="W"></path><path d="M231 543l1-1 1 1c0 1 0 1-1 2v1l-1 1v1c0-1 0-1-1-2 0-1 1-3 1-3z" class="Y"></path><path d="M241 515c0 1 1 2 1 2 0 2 0 2 1 3h1v1 2c-1 1-1 2-3 3h0c-1 1-1 1-2 1l-2 2v-1-1c1-1 1-1 1-2s1-1 2-2 1-1 1-2c-1-2 0-4 0-6z" class="F"></path><path d="M243 520h1v1 2c-1 1-1 2-3 3h0c-1 1-1 1-2 1 1-2 3-4 4-7z" class="C"></path><path d="M238 508l1 1c0 1 1 2 0 3 0 1 0 2-1 2l1 1h1c0-1 0-3 1-4l1 1c-1 1-1 2-1 3 0 2-1 4 0 6 0 1 0 1-1 2-1 0 0-1-1-2-1 0-1 0-2 1 0 1 0 2-1 3v5c-1-3 0-6-1-8v-2l-1-2c1 0 1 0 2-1s1-4 1-6l1-3z" class="S"></path><path d="M236 519l1-2h1c0 1 0 1-1 2v3c0 1 0 2-1 3 0-2 1-3 1-5l-1-1z" class="k"></path><path d="M236 519l1 1c0 2-1 3-1 5v5c-1-3 0-6-1-8v-2l1-1z" class="s"></path><path d="M242 504v1c2 2 3 3 4 5-1 0-1 1-1 1h-1v2 1c0 2 0 4-1 5v-2h-1 0 0s-1-1-1-2 0-2 1-3l-1-1c-1 1-1 3-1 4h-1l-1-1c1 0 1-1 1-2 1-1 0-2 0-3l-1-1 1-1 1-1 1-1 1-1z" class="C"></path><path d="M242 512v5h0s-1-1-1-2 0-2 1-3z" class="u"></path><path d="M241 505c1 0 1 1 1 2-1 1-1 1-2 1h0c0-1 0-1-1-1l1-1 1-1z" class="K"></path><path d="M243 517v-7l1 1v2 1c0 2 0 4-1 5v-2z" class="j"></path><path d="M240 490l2 1v1h-1v1 1l-2 1c1 0 1 1 2 2l-3 3 1 1 2 1s0 1 1 2l-1 1-1 1-1 1-1 1-1 3c0 2 0 5-1 6s-1 1-2 1l-2-2c-2-3-4-4-7-6v-4c0-1 0-1-1-1v-1l1-1c1-1 1-2 1-3h1c0-2-1-3 1-4l1 1 1-1c1 0 1-1 2-1l1-1h1v1l1-1c2 0 3-3 5-4z" class="d"></path><path d="M227 504h0 1s0 1 1 1l-1 2c-1 1-1 1-2 1 0-1 1-2 1-4z" class="P"></path><path d="M230 502l1 1v1 1l-1-1v1c-1 1-1 2-1 3l-1-1 1-2c0-1 0-2 1-3z" class="h"></path><path d="M229 497l1-1c0 2-1 3 0 5v1c-1 1-1 2-1 3-1 0-1-1-1-1h-1 0-1c-1 0-1 0-1-1 1-1 1-2 1-3h1c0-2-1-3 1-4l1 1z" class="K"></path><path d="M228 496l1 1c0 2-1 3-2 5h0v-2c0-2-1-3 1-4z" class="Q"></path><path d="M231 502v3h1v1c-1 2-3 3-2 6h1l1 1v-1l1-1 1-1h0v3c0 1 0 1-1 2l-1 1c-2-3-4-4-7-6v-4c1 1 1 1 1 2 1 1 1 2 2 2h1c1-2 1-3 2-5v-1-1-1z" class="T"></path><path d="M233 503l2-1v1h1l-1 1c1 2 1 2 0 4v1c0 1 0 1-1 1h0l-1 1-1 1v1l-1-1h-1c-1-3 1-4 2-6v-1l1-2z" class="r"></path><path d="M233 507c1 1 1 1 1 3h0l-1 1-1 1v1l-1-1h1l-1-1c1-2 1-3 2-4z" class="L"></path><path d="M235 504c1 2 1 2 0 4v1c0 1 0 1-1 1h0 0c0-2 0-2-1-3 0-1 2-2 2-3z" class="C"></path><path d="M233 494h1v1 1c0 1 0 1 1 2l-2 5-1 2h-1v-3 1l-1-1v-1c-1-2 0-3 0-5 1 0 1-1 2-1l1-1z" class="S"></path><path d="M230 501c0-1 1-2 1-3 1 1 1 2 1 4h-1v1l-1-1v-1z" class="C"></path><path d="M232 495c0 1 0 2-1 3 0 1-1 2-1 3-1-2 0-3 0-5 1 0 1-1 2-1z" class="d"></path><path d="M240 490l2 1v1h-1v1 1l-2 1c1 0 1 1 2 2l-3 3-1 1v1h-1-1l-2 1 2-5c-1-1-1-1-1-2v-1l1-1c2 0 3-3 5-4z" class="J"></path><path d="M237 497v2 1h-1 0v-4l1 1z" class="d"></path><path d="M236 496c1-2 3-3 5-4v1 1l-2 1c0 1-1 1-2 2l-1-1z" class="f"></path><path d="M238 500l1 1 2 1s0 1 1 2l-1 1-1 1-1 1-1 1-1 3c0 2 0 5-1 6s-1 1-2 1l-2-2 1-1c1-1 1-1 1-2v-3c1 0 1 0 1-1v-1c1-2 1-2 0-4l1-1h-1v-1h1 1v-1l1-1z" class="L"></path><path d="M238 506v1h1l-1 1-1 3v1l-1-1c0-2 1-3 1-5h1z" class="t"></path><path d="M235 502h1 1v-1l1 3v2h-1v-1h-1v-1-1h-1v-1z" class="G"></path><path d="M234 513c1 1 1 2 1 4h1c-1 1-1 1-2 1l-2-2 1-1c1-1 1-1 1-2z" class="C"></path><path d="M238 500l1 1 2 1s0 1 1 2l-1 1-1 1-1 1h-1v-1-2l-1-3 1-1z" class="V"></path><path d="M251 488c1 0 1-1 2-2h0v2c1 1 0 2 1 3v1c-1 3 1 5 0 7h1l-1 4v3c-1 1-1 2-1 3-1 3-1 7-2 9h-1-1c-3 1-3 2-5 5v-2-1h-1c-1-1-1-1-1-3h0 0 1v2c1-1 1-3 1-5v-1-2h1s0-1 1-1c-1-2-2-3-4-5v-1c-1-1-1-2-1-2l-2-1-1-1 3-3c-1-1-1-2-2-2l2-1h2 3 0l3-3c1-1 1-2 2-3z" class="I"></path><path d="M241 494h2v1h2c-2 0-3 1-4 2-1-1-1-2-2-2l2-1z" class="O"></path><path d="M246 494h0 3l-1 1-2 1v-1h-1-2v-1h3z" class="J"></path><path d="M239 501l2-3c1 0 1 0 2 1l1 3 1 2c-1 0-2 0-3 1v-1c-1-1-1-2-1-2l-2-1z" class="N"></path><path d="M244 502l1 2c-1 0-2 0-3 1v-1c-1-1-1-2-1-2h3z" class="f"></path><path d="M246 496l2-1c0 1 0 1 1 2v2h0l-1 3h0v4h-3v-2h0l-1-2-1-3v-1h1c1-1 2-1 2-2z" class="L"></path><path d="M246 502l-1-2h0 2c0 1 0 1-1 2z" class="P"></path><path d="M247 500l1 2v4h-3v-2h1v-2c1-1 1-1 1-2z" class="b"></path><path d="M246 496l2-1c0 1 0 1 1 2-2 1-2 3-5 2v-1c1-1 2-1 2-2z" class="R"></path><path d="M249 499c1 2 1 3 1 6v1 3l-2 4-1 2-1-1h-1v1l-1-1v-1-2h1s0-1 1-1c-1-2-2-3-4-5 1-1 2-1 3-1h0v2h3v-4h0l1-3z" class="c"></path><path d="M247 512v-2h1v2h-1z" class="k"></path><path d="M248 512v1l-1 2-1-1h-1v1l-1-1v-1h2c1 0 1 0 1-1h1z" class="h"></path><path d="M245 504h0v2h0c1 1 1 2 2 3v1h-1c-1-2-2-3-4-5 1-1 2-1 3-1z" class="X"></path><path d="M251 488c1 0 1-1 2-2h0v2c1 1 0 2 1 3v1c-1 3 1 5 0 7l-1 1-1 3c0 2-1 3-1 5 0-1 0-1-1-2v-1c0-3 0-4-1-6h0v-2c-1-1-1-1-1-2l1-1h-3l3-3c1-1 1-2 2-3z" class="i"></path><path d="M253 495v2h-1l-1-1 2-1z" class="S"></path><path d="M251 491l2 1v3l-2 1-2 1c-1-1-1-1-1-2l1-1h0c2 0 2-1 2-3z" class="F"></path><path d="M251 488c1 0 1-1 2-2h0v2l-2 3c0 2 0 3-2 3h0-3l3-3c1-1 1-2 2-3z" class="c"></path><path d="M254 499h1l-1 4v3c-1 1-1 2-1 3-1 3-1 7-2 9h-1-1c-3 1-3 2-5 5v-2-1h-1c-1-1-1-1-1-3h0 0 1v2c1-1 1-3 1-5l1 1v-1h1l1 1 1-2 2-4v-3c1 1 1 1 1 2 0-2 1-3 1-5l1-3 1-1z" class="T"></path><path d="M250 506c1 1 1 1 1 2l-1 4v-3-3z" class="m"></path><path d="M252 503l1-3c1 2 1 4 1 6-1 1-1 2-1 3v-2c0-2 0-3-1-4z" class="C"></path><path d="M254 499h1l-1 4v3c0-2 0-4-1-6l1-1z" class="h"></path><path d="M245 515v-1h1l1 1v1h2l-1 1h-1s-1 0-1 1h0l-1-1v1-3z" class="S"></path><path d="M250 509v3c0 2-1 3-1 4h-2v-1l1-2 2-4z" class="F"></path><path d="M254 503c1-1 1-2 2-3h0v-2-2l1 1c1 1 0 3 0 4v1-1h2v1l-1 3c0 3-1 5-2 8v1h1c0 2 0 3-1 4 1 1 1 1 2 1l-2 2c0 1-1 4-2 5v1h-2l-3 9c-2 4-5 8-8 11-1 1-2 3-2 3l-2 3c-1 0-2 1-3 1-4 3-8 6-12 10-2 1-6 4-7 6h-1-2l1-1c1-1 6-6 6-7l5-4c1-2 1-3 2-4 0-1 0-1 1-1 0-1 0-1 1-1h1c1-2 2-2 3-3v-1c1-1 2-2 2-3h0c1-1 1-2 1-2v-1c0-1 0-2 1-4s2-5 3-7h0l2-5h0c2-1 2-2 3-3 2-3 2-4 5-5h1 1c1-2 1-6 2-9 0-1 0-2 1-3v-3z" class="o"></path><path d="M257 502v-1h2v1l-1 3c0 3-1 5-2 8l-1 1c-1-3 0-4 1-7 0-2 1-4 1-5z" class="f"></path><path d="M235 543c1-2 1-2 2-3h1l-1 1v1l1-2v1c-3 6-8 12-14 17 1-2 1-3 2-4 0-1 0-1 1-1 0-1 0-1 1-1h1c1-2 2-2 3-3v-1c1-1 2-2 2-3h0c1-1 1-2 1-2z" class="F"></path><path d="M249 518h1 1c-1 4-3 6-4 9-1 2-2 5-3 6-2 3-3 6-6 8v-1l-1 2v-1l1-1h-1c-1 1-1 1-2 3v-1c0-1 0-2 1-4s2-5 3-7h0l2-5h0c2-1 2-2 3-3 2-3 2-4 5-5z" class="N"></path><path d="M242 532l2-1h0 0v2c-2 3-3 6-6 8v-1l-1 2v-1l1-1h-1c1-3 4-6 5-8z" class="R"></path><path d="M249 518h1 1c-1 4-3 6-4 9-1 2-2 5-3 6v-2h0 0l-2 1c3-5 5-9 7-14z" class="k"></path><path d="M256 513v1h1c0 2 0 3-1 4 1 1 1 1 2 1l-2 2c0 1-1 4-2 5v1h-2l-3 9c-2 4-5 8-8 11-1 1-2 3-2 3l-2 3c-1 0-2 1-3 1-4 3-8 6-12 10-2 1-6 4-7 6h-1l4-4c3-2 6-4 8-6 6-6 11-12 16-18 1-3 3-5 4-7 4-7 7-14 9-21l1-1z" class="T"></path><path d="M256 518c1 1 1 1 2 1l-2 2c0 1-1 4-2 5l-1-1v-1c1 0 3-5 3-6z" class="E"></path><path d="M234 554c3-4 6-9 9-13s5-8 7-12l3-5h0v1l1 1v1h-2l-3 9c-2 4-5 8-8 11-1 1-2 3-2 3l-2 3c-1 0-2 1-3 1z" class="X"></path><path d="M253 525l1 1v1h-2l1-2z" class="u"></path><path d="M240 357h1c0 1-1 1-1 2h1l-1 2 1 1c0 1 1 1 1 2l2 3 1 2 1 1h0l1-1 3 6c1 2 2 3 2 4s2 4 2 4c2 2 4 4 5 6h1l1 1v2l1 1v3c-1 0-1 0-2-1v2l1 3-1 1v2l1 1h1 0v1 1c0 1 1 1 1 2h1v2 1c3 2 4 3 4 6v3 3 1c0 1 1 1 1 1l-1 6c-1 1-2 5-2 6s0 2-1 3v3l1-1h0l-2 6 1 1v1l-1 1-1-1v2 2l-1 4h-1v1 2 2 1 2l-2-1c-1 1-1 2-1 3v1-5h-2l-1 1h-1c0-1-1-1-2 0l-1-1v-1h0c-2 0-3-1-5-2l-1-1c-1 1-1 1-2 0s-2-2-3-2v-1c-2-2-4-3-4-5l-1-1-1 1v-2h-1l-1 1h-1-1c0-1 1-1 1-2l-1-1c0-1 0-2 1-3l1-1 1 1-3-12-1 1-2-1v-1-1c1 0 1-1 2-2v-1l-1-1v-1l1-2 1-1 3-2c1 0 1-1 2-2l-1-1 1-1s1-1 1-2c1-1 2-1 2-1l-1-3v-2l1-2-1-1c1-2 0-4-1-5 0-2-1-4 0-5h0c0-2 0-3-1-4l-2-2v-2-1c0-2-1-4-3-5l-1-1 1-1h0 1l-1-1v-1h1 1l-1-1c-1 0-1-1-2-1v-1l-5-3 2-5v-1l1-4 2 1h0l2-3c1-1 1-2 3-2h0 1c1-1 1-1 2-1h2 1z" class="C"></path><path d="M252 428c1-1 1-2 1-4v1c0 1 0 1 1 2v5l-1 1v-1l-1-4z" class="r"></path><path d="M254 422c-1 1 0 1-1 2 0 2 0 3-1 4-1-1-2-3-3-4 0-1 0-1 1-1h1c1 0 2-1 3-1z" class="o"></path><path d="M260 447c1 1 1 2 1 3h1 0 1v2 2l-1 4h-1-1-1 0c1-1 0-2 0-3s1-1 0-3l1-1v-4z" class="V"></path><path d="M262 450h1v2 2c-1 0-2-1-3-1 1-1 1-2 1-2l1-1h0z" class="M"></path><path d="M250 415l1 1c1 1 2 3 2 5h0l1 1c-1 0-2 1-3 1h-1c-1 0-1 0-1 1h0c-1-2 0-6-1-8 1-1 1-1 2-1z" class="P"></path><path d="M249 424v-5h1v4c-1 0-1 0-1 1h0z" class="f"></path><path d="M256 421l1 2v-1c0 2 1 3 2 4h1l2-1c1 2 0 3-1 5v6l-1 2h0 0v9 4l-1 1c0-2 0-5-1-7 0-3-1-6-1-10-1-4-1-9-1-14z" class="X"></path><path d="M262 425c1 2 0 3-1 5v6l-1 2h0 0c-1-4-1-8 0-12h0l2-1z" class="W"></path><path d="M260 413l1-1c1-1 2-1 3-1 3 2 4 3 4 6v3 3h-1c0-1 0-1-1-1h-2 0l-1 2-1 1-2 1h-1c-1-1-2-2-2-4v1l-1-2v-5-1c0-1 1-1 1-2l1 1 1-1h1z" class="Q"></path><path d="M266 422v-2-1-1h0c1 1 1 2 2 2h0v3h-1c0-1 0-1-1-1z" class="b"></path><path d="M259 413h1 0 1c1 0 2 0 4 1l1 1h-2-1s0-1-1-1h0c-1 1-1 2-2 2h1c1 1 2 1 3 1v2h0v1c-2 0-2 1-4 1h0 0c0-1 1-1 1-2h-1c-2-1-2-1-2-3-1-1-1-1-2-1 0-1 1-1 1-2l1 1 1-1z" class="V"></path><path d="M264 419h-3c-1-1-2-2-2-3h1 1c1 1 2 1 3 1v2z" class="e"></path><path d="M256 415c1 0 1 0 2 1 0 2 0 2 2 3h1c0 1-1 1-1 2h0 0c2 0 2-1 4-1h1 0c-1 1-1 2-1 2l-1 2-1 1-2 1h-1c-1-1-2-2-2-4v1l-1-2v-5-1z" class="o"></path><defs><linearGradient id="Z" x1="254.908" y1="432.2" x2="244.381" y2="421.135" xlink:href="#B"><stop offset="0" stop-color="#424240"></stop><stop offset="1" stop-color="#5a5859"></stop></linearGradient></defs><path fill="url(#Z)" d="M246 398l1-4v6 1c0 1 0 1 1 1-1 1-1 1-1 2l2-1v5c0 1-1 2-1 3v1c0 1-1 1-1 2l1 2h0c1 2 0 6 1 8h0c1 1 2 3 3 4l1 4v6l-2-3c0-1-1-1-2-2 0-3-2-6-2-10l-1-3c-1 0-2-1-2-1v-1-6-2-1-15l2 2-1 1 1 1z"></path><path d="M244 410c1 1 1 4 1 6 0 1 1 2 1 3 1 1 1 3 1 4l-1-3c-1 0-2-1-2-1v-1-6-2z" class="d"></path><path d="M247 404l2-1v5c0 1-1 2-1 3v1c0 1-1 1-1 2v3h0c-1-1-1-3-1-5l1-7v-1z" class="G"></path><path d="M247 404l2-1v5c0 1-1 2-1 3v1h0c0-2 0-5-1-7v-1z" class="J"></path><path d="M246 398l1-4v6 1c0 1 0 1 1 1-1 1-1 1-1 2v1l-1 7-1-1v5c0-2 0-5-1-6v-1-15l2 2-1 1 1 1z" class="L"></path><path d="M246 398l1-4v6 1c0 1 0 1 1 1-1 1-1 1-1 2v1l-1 7-1-1 1-13z" class="S"></path><path d="M266 422c1 0 1 0 1 1h1v1c0 1 1 1 1 1l-1 6c-1 1-2 5-2 6s0 2-1 3v3l1-1h0l-2 6 1 1v1l-1 1-1-1v2-2h-1 0-1c0-1 0-2-1-3v-9h0 0l1-2v-6c1-2 2-3 1-5l1-1 1-2h0 2z" class="k"></path><path d="M263 445l3-8c0 1 0 2-1 3v3l1-1h0l-2 6 1 1v1l-1 1-1-1v2-2h-1c0-2 1-3 1-5z" class="f"></path><path d="M264 448l1 1v1l-1 1-1-1 1-2z" class="S"></path><path d="M267 423h1v1c0 1 1 1 1 1l-1 6c-1 1-1 1-2 1v-2-1h0c-1 1-1 1-1 2h0c-1 1 0 1 0 2h0v2l-2 1c0-3 1-5 2-8 0-2 2-3 2-5z" class="R"></path><path d="M260 438h0 0l1-2v1c1 0 1 0 1-1 0 3 0 7 1 9 0 2-1 3-1 5h0-1c0-1 0-2-1-3v-9z" class="J"></path><path d="M266 422c1 0 1 0 1 1 0 2-2 3-2 5-1 3-2 5-2 8h-1c0 1 0 1-1 1v-1-6c1-2 2-3 1-5l1-1 1-2h0 2z" class="f"></path><path d="M264 422h1v2c-1 0-1 1-2 0h0l1-2h0z" class="G"></path><path d="M254 383c2 2 4 4 5 6h1l1 1v2l1 1v3c-1 0-1 0-2-1v2l1 3-1 1v2l1 1h1 0v1 1c0 1 1 1 1 2h1v2 1c-1 0-2 0-3 1l-1 1h-1l-1 1-1-1c0 1-1 1-1 2v1h0v3c-1-1-1-2-1-3v3-2s0 1-1 2l-3-3-1-1c-1 0-1 0-2 1h0l-1-2c0-1 1-1 1-2v-1c0-1 1-2 1-3v-5-1c1-1 1-2 2-4h0c0-1 0-1 1-1 1-2 2-3 2-4 1-1 0-2 0-3h0 0c1-1 1-2 2-3l-1-1c0-1 0-2-1-3z" class="T"></path><path d="M253 403c1-1 1-2 1-2l2 1v2c-1-1-2-1-3-1z" class="L"></path><path d="M260 389l1 1v2l-1 1v-1l-1-2v-1h1z" class="f"></path><path d="M261 392l1 1v3c-1 0-1 0-2-1v-3 1l1-1z" class="I"></path><path d="M258 401v-1h0c-1 0-1-1-1-1l1-1s0 1 1 1l1-2 1 3-1 1v1l-2-1z" class="C"></path><path d="M257 408h2v2l-1 1v2h1l-1 1-1-1h0v-5z" class="d"></path><path d="M257 391c1 1 1 2 1 3 0 2 0 3-1 4l-2-2 1-3 1-2z" class="t"></path><path d="M258 401l2 1v-1 2l1 1c-1 1-2 2-2 4h0-2l1-7z" class="L"></path><path d="M254 383c2 2 4 4 5 6v1l-1 1v3c0-1 0-2-1-3l-1 2h-2c1-1 0-2 0-3h0 0c1-1 1-2 2-3l-1-1c0-1 0-2-1-3z" class="h"></path><path d="M256 387c1 1 1 3 1 4l-1 2h-2c1-1 0-2 0-3h0 0c1-1 1-2 2-3z" class="i"></path><path d="M261 404h1 0v1 1c0 1 1 1 1 2h1v2 1c-1 0-2 0-3 1l-1 1h-1-1v-2l1-1v-2h0c0-2 1-3 2-4z" class="h"></path><path d="M262 406h-1v-1l1-1v1 1z" class="T"></path><path d="M259 410c0-1 1-2 2-2h1 0c0 1 0 1-1 2-1 0-2 1-3 1l1-1z" class="R"></path><path d="M258 411c1 0 2-1 3-1h3v1c-1 0-2 0-3 1l-1 1h-1-1v-2z" class="E"></path><path d="M254 393h2l-1 3 2 2-3 3s0 1-1 2h-2c0 1-1 4-2 5v-5-1c1-1 1-2 2-4h0c0-1 0-1 1-1 1-2 2-3 2-4z" class="b"></path><path d="M254 393h2l-1 3c-1 1-1 2-1 2-2 1-2 2-2 3v1h-1c0-2 1-3 1-4h-1c0-1 0-1 1-1 1-2 2-3 2-4z" class="H"></path><path d="M249 408c1-1 2-4 2-5h2c1 0 2 0 3 1 0 0-1 1-1 2h0c0 2 1 4 0 7v4s0 1-1 2l-3-3-1-1c-1 0-1 0-2 1h0l-1-2c0-1 1-1 1-2v-1c0-1 1-2 1-3z" class="Q"></path><path d="M252 406v-1c2 0 2 0 3 1h0-1c0 1-1 1-2 2v-2h0z" class="J"></path><path d="M250 414v1l1-1c1-1 2 1 3 1h1l-1-1c-1 0-1-1-2-1v-1h1c1 0 2 0 2 1v4s0 1-1 2l-3-3-1-1v-1z" class="I"></path><path d="M249 408c1-1 2-4 2-5h2c1 0 2 0 3 1 0 0-1 1-1 2-1-1-1-1-3-1v1c-2 2-2 5-2 7v1 1c-1 0-1 0-2 1h0l-1-2c0-1 1-1 1-2v-1c0-1 1-2 1-3z" class="D"></path><path d="M248 411l2-1v1c-1 1 0 2 0 2v1 1c-1 0-1 0-2 1h0l-1-2c0-1 1-1 1-2v-1z" class="h"></path><path d="M240 357h1c0 1-1 1-1 2h1l-1 2 1 1c0 1 1 1 1 2l2 3 1 2 1 1h0l1-1 3 6c1 2 2 3 2 4s2 4 2 4c1 1 1 2 1 3l1 1c-1 1-1 2-2 3h0 0c0 1 1 2 0 3 0 1-1 2-2 4-1 0-1 0-1 1h0c-1 2-1 3-2 4v1l-2 1c0-1 0-1 1-2-1 0-1 0-1-1v-1-6l-1 4-1-1 1-1-2-2v15 1 2l-1 1-1 1c-1-2 0-3 0-5-1 1-1 2-1 3l-2-2c0-1-1-2-1-3l-1-1c1-2 0-4-1-5 0-2-1-4 0-5h0c0-2 0-3-1-4l-2-2v-2-1c0-2-1-4-3-5l-1-1 1-1h0 1l-1-1v-1h1 1l-1-1c-1 0-1-1-2-1v-1l-5-3 2-5v-1l1-4 2 1h0l2-3c1-1 1-2 3-2h0 1c1-1 1-1 2-1h2 1z" class="H"></path><path d="M234 376v-1l1-1c1 1 1 3 2 4v2c-1-1-3-2-3-4z" class="Z"></path><path d="M234 368l1-1c1 1 1 1 1 2v5h-3v-1c1 0 1-1 1-1 1-1 1-2 0-4z" class="c"></path><path d="M238 370h1v4 7h-1v1l-1-1v-1-2l1-1v-7z" class="O"></path><path d="M238 377v4 1l-1-1v-1-2l1-1z" class="D"></path><path d="M234 368c1 2 1 3 0 4 0 0 0 1-1 1v1 2c-1 0-1 1-2 1v-1l-1-1c0-1 0-1 1-2l1-1v-2h1c1 0 1-1 1-2z" class="H"></path><path d="M231 376c1-1 1-3 1-4l1 1v1 2c-1 0-1 1-2 1v-1z" class="O"></path><path d="M236 363l1 1c0 1-1 1-2 2h1 0 2l1 1-1 1c1 0 1 1 1 1 1 1 1 1 1 2h-1v3-4h-1c-1 0-1-1-2-1 0-1 0-1-1-2l-1 1c0 1 0 2-1 2h-1c0-2 1-3 2-4 0-1 1-2 2-3z" class="Z"></path><path d="M239 367c3 5 4 8 5 14v2 2 2h0l-1-2h0l-1-4-2 1-1 3v-4-7-3h1c0-1 0-1-1-2 0 0 0-1-1-1l1-1z" class="f"></path><path d="M239 374v-3h1l1 2v2l1 6-2 1-1 3v-4-7z" class="c"></path><path d="M241 375v4l-1 1v-1-6h1v2z" class="R"></path><path d="M234 358h0c-1 1-1 2-2 3h1 0l1-1c1 1 2 2 3 2-1 0-1 1-1 1-1 1-2 2-2 3-1 1-2 2-2 4v2l-1 1c-1 1-1 1-1 2l1 1v1c-1 0-1-1-2-1v-1l-5-3 2-5v-1l1-4 2 1h0l2-3c1-1 1-2 3-2z" class="J"></path><path d="M229 369v1h0l1 3-1 1v-1c-1-1-1-3 0-4z" class="c"></path><path d="M229 370h2v3c-1 1-1 1-1 2l1 1v1c-1 0-1-1-2-1l1-1-1-1 1-1-1-3z" class="D"></path><path d="M230 367v2h1c0 1 0 2 1 3l-1 1v-3h-2 0v-1-2h1z" class="m"></path><path d="M227 362l2 1c-1 2-1 2-1 4v1c0 1 0 0-1 1v2l-1 1v-1c0-1 1-2 1-4-1-1 0-1-1-1l1-4z" class="D"></path><path d="M234 358h0c-1 1-1 2-2 3h1 0l1-1c1 1 2 2 3 2-1 0-1 1-1 1-1 1-2 2-2 3-1 1-2 2-2 4v2c-1-1-1-2-1-3h-1v-2h-1-1c0-2 0-2 1-4h0l2-3c1-1 1-2 3-2z" class="H"></path><path d="M229 363h1v4h-1-1c0-2 0-2 1-4h0z" class="r"></path><path d="M233 361l1-1c1 1 2 2 3 2-1 0-1 1-1 1-1 1-2 2-2 3-1 1-2 2-2 4v2c-1-1-1-2-1-3l1-2c-1-1-1-1-1-2 1-1 1-3 2-4z" class="B"></path><path d="M240 357h1c0 1-1 1-1 2h1l-1 2 1 1c0 1 1 1 1 2l2 3 1 2 1 1h0c1 1 2 3 2 5h-1v-1h-1v1 1h-2l1 4-1 1c-1-6-2-9-5-14l-1-1h-2 0-1c1-1 2-1 2-2l-1-1s0-1 1-1c-1 0-2-1-3-2l-1 1h0-1c1-1 1-2 2-3h1c1-1 1-1 2-1h2 1z" class="T"></path><path d="M244 367l1 2 1 1h0c1 1 2 3 2 5h-1v-1h-1v1 1h-2v-3l-1-2-1-3c1 0 1 0 2-1z" class="K"></path><path d="M245 369l1 1-1 1c-1 1 1 1-1 2l-1-2c1-1 2-1 2-2z" class="I"></path><path d="M244 367l1 2c0 1-1 1-2 2l-1-3c1 0 1 0 2-1z" class="Q"></path><path d="M240 357h1c0 1-1 1-1 2h1l-1 2 1 1c0 1 1 1 1 2l2 3c-1 1-1 1-2 1l-1-1c-1-2-3-3-4-5-1 0-2-1-3-2l-1 1h0-1c1-1 1-2 2-3h1c1-1 1-1 2-1h2 1z" class="M"></path><path d="M240 357h1c0 1-1 1-1 2h1l-1 2-3-2-2-1c1-1 1-1 2-1h2 1z" class="J"></path><path d="M237 357h2l-2 2-2-1c1-1 1-1 2-1z" class="h"></path><path d="M241 367v-1-1c-1-1-1-2-1-3h1c0 1 1 1 1 2l2 3c-1 1-1 1-2 1l-1-1z" class="a"></path><path d="M246 370l1-1 3 6c1 2 2 3 2 4s2 4 2 4c1 1 1 2 1 3l1 1c-1 1-1 2-2 3h0 0c0 1 1 2 0 3 0 1-1 2-2 4-1 0-1 0-1 1h0c-1 2-1 3-2 4v1l-2 1c0-1 0-1 1-2-1 0-1 0-1-1v-1-6l-1 4-1-1 1-1-2-2v-3c0-2-1-4-1-6h0l1 2h0v-2-2-2l1-1-1-4h2v-1-1h1v1h1c0-2-1-4-2-5z" class="Z"></path><path d="M245 380v2c1 2 1 4 0 6l-1-5v-2l1-1z" class="h"></path><path d="M244 376h2v2 4h-1v-2l-1-4z" class="G"></path><path d="M244 383l1 5v4h-1v-1c0-2-1-4-1-6h0l1 2h0v-2-2z" class="V"></path><path d="M246 375v-1h1v1h1c1 1 2 2 2 3v1h0-1c-1 0-1 1 0 1-1 1-1 1-1 2l-1 1h0c0-2 0-3-1-5v-2-1z" class="C"></path><path d="M246 375v-1h1v1h1c1 1 2 2 2 3v1c-1-1-1-1-2-1-1-1-1-1-1-2l-1-1z" class="G"></path><path d="M249 380c-1 0-1-1 0-1h1c0 2 0 8 2 9h1l-1 2c0 1 0 3-1 3l-1-1-1 1h0-1v-1-2l-1 1v-8h0l1-1c0-1 0-1 1-2z" class="W"></path><path d="M248 382c0-1 0-1 1-2 1 3 1 8 1 11l-1 1h-1v-2l-1 1v-8h0l1-1z" class="r"></path><path d="M247 383h0l1-1v8l-1 1v-8z" class="h"></path><path d="M246 370l1-1 3 6c1 2 2 3 2 4s2 4 2 4c1 1 1 2 1 3l1 1c-1 1-1 2-2 3h0 0-2l1-2h-1c-2-1-2-7-2-9h0v-1c0-1-1-2-2-3 0-2-1-4-2-5z" class="Z"></path><path d="M252 379c0 1 2 4 2 4 1 1 1 2 1 3l1 1c-1 1-1 2-2 3h0 0-2l1-2-1-3h0c0-2-1-4 0-6z" class="W"></path><path d="M252 385h1c2 1 0 3 1 5h0 0-2l1-2-1-3z" class="E"></path><path d="M253 388h1v2h0-2l1-2z" class="F"></path><path d="M252 390h2c0 1 1 2 0 3 0 1-1 2-2 4-1 0-1 0-1 1h0c-1 2-1 3-2 4v1l-2 1c0-1 0-1 1-2-1 0-1 0-1-1v-1-6-1-2l1-1v2 1h1 0l1-1 1 1c1 0 1-2 1-3z" class="B"></path><path d="M248 402c0-1 1-3 1-4h2c-1 2-1 3-2 4v1l-2 1c0-1 0-1 1-2z" class="T"></path><path d="M247 391l1-1v2 1h1 0v3c-1 1-2 2-2 4v-6-1-2z" class="E"></path><path d="M247 391l1-1v2 1h1 0v3c-1-1-1-2-1-3h-1v-2z" class="S"></path><path d="M233 376h1c0 2 2 3 3 4v1l1 1v-1h1v4l1-3 2-1 1 4c0 2 1 4 1 6v3 15 1 2l-1 1-1 1c-1-2 0-3 0-5-1 1-1 2-1 3l-2-2c0-1-1-2-1-3l-1-1c1-2 0-4-1-5 0-2-1-4 0-5h0c0-2 0-3-1-4l-2-2v-2-1c0-2-1-4-3-5l-1-1 1-1h0 1l-1-1v-1h1 1l-1-1c1 0 1-1 2-1z" class="N"></path><path d="M233 388v-1c0-2-1-4-3-5l-1-1 1-1 3 3c1 2 2 3 3 5 1 5 3 9 3 14h0-1c0-2-1-4-2-6h0c0-2 0-3-1-4l-2-2v-2z" class="I"></path><path d="M233 376h1c0 2 2 3 3 4v1l1 1v-1h1v4c1 1 2 1 2 3h-1v1c-3-4-6-7-9-11h1l-1-1c1 0 1-1 2-1z" class="T"></path><path d="M238 381h1v4c1 1 2 1 2 3h-1s-1-1-1-2 0-1-1-1v-3-1z" class="J"></path><path d="M239 385l1-3 2-1 1 4c0 2 1 4 1 6v3 15 1 2l-1 1-1 1c-1-2 0-3 0-5-1-1 0-3 0-4 0-5 0-10-2-16v-1h1c0-2-1-2-2-3z" class="R"></path><path d="M237 411v-2l1-2c0 1 1 2 1 3l2 2c0-1 0-2 1-3 0 2-1 3 0 5l1-1 1-1v6 1s1 1 2 1l1 3c0 4 2 7 2 10 1 5 2 8 4 12 1 3 2 6 3 8l1 3c1 1 1 2 2 2h1 1v1 2 2 1 2l-2-1c-1 1-1 2-1 3v1-5h-2l-1 1h-1c0-1-1-1-2 0l-1-1v-1h0c-2 0-3-1-5-2l-1-1c-1 1-1 1-2 0s-2-2-3-2v-1c-2-2-4-3-4-5l-1-1-1 1v-2h-1l-1 1h-1-1c0-1 1-1 1-2l-1-1c0-1 0-2 1-3l1-1 1 1-3-12-1 1-2-1v-1-1c1 0 1-1 2-2v-1l-1-1v-1l1-2 1-1 3-2c1 0 1-1 2-2l-1-1 1-1s1-1 1-2c1-1 2-1 2-1l-1-3z" class="n"></path><path d="M230 426h1v1h0l-1 3-1-1v-1l-1-1v-1h2z" class="k"></path><path d="M228 426h2c-1 1-1 2-1 2l-1-1v-1z" class="T"></path><path d="M229 429l1 1v3l-1 1-2-1v-1-1c1 0 1-1 2-2z" class="W"></path><path d="M244 455c2 1 4 3 6 5-1 0-2 0-3-1-2-1-2-2-3-4z" class="AD"></path><path d="M242 449c-2-4-7-11-7-16h1c0 2 1 3 2 4 0 2 0 4 1 5l3 6v1z" class="M"></path><path d="M238 437c2 3 7 8 6 11v2h-1l-1-1v-1l-3-6c-1-1-1-3-1-5z" class="X"></path><path d="M244 455v-2c2 1 3 2 4 3l2 3c1 1 3 1 3 2l1-1v-1l2 3h1l1 1v1h-2l-1 1h-1c0-1-1-1-2 0l-1-1v-1h0 2v-1c-1-1-2-1-3-2-2-2-4-4-6-5z" class="K"></path><path d="M256 462h1l1 1v1h-2l-1 1h-1c0-1-1-1-2 0l-1-1v-1c2 0 4 0 5-1z" class="l"></path><path d="M230 423l3-2 2 2h0 1v1l1 2c1 0 1 1 1 1v1c-1-1-2-1-3-2-1 0-3 0-4 1h0v-1h-1-2l1-2 1-1z" class="F"></path><path d="M236 424c-1 1-1 2-1 2-1 0-3 0-4 1h0v-1l1-1 1 1c1 0 1 0 3-2z" class="E"></path><path d="M236 423v1l1 2c1 0 1 1 1 1v1c-1-1-2-1-3-2 0 0 0-1 1-2v-1z" class="z"></path><path d="M230 423l3-2 2 2h0l-3 2-1 1h-1-2l1-2 1-1z" class="D"></path><path d="M233 445c2 2 3 6 5 8 2 3 4 5 7 7-1 1-1 1-2 0s-2-2-3-2v-1c-2-2-4-3-4-5l-1-1-1 1v-2h-1l-1 1h-1-1c0-1 1-1 1-2l-1-1c0-1 0-2 1-3l1-1 1 1z" class="k"></path><path d="M230 448c0-1 0-2 1-3 1 2 2 3 3 5l1 1-1 1v-2h-1l-1 1h-1-1c0-1 1-1 1-2l-1-1z" class="O"></path><path d="M242 423l2 6c2 7 4 13 7 20 0 1 1 4 2 5 1 0 2 1 3 2v-3l1 3c1 1 1 2 2 2h1 1v1 2 2 1 2l-2-1c-1 1-1 2-1 3v1-5-1l-1-1h-1l-2-3v1l-1 1c0-1-2-1-3-2l-2-3h1v-1-6c-1-1-2-3-2-4-2-5-4-9-6-13-1-2-3-3-3-4v-1s0-1-1-1c1-1 2-2 2-3h0 1 1 1z" class="S"></path><path d="M256 453l1 3-1 2c-1-1-2-3-3-4 1 0 2 1 3 2v-3z" class="a"></path><path d="M239 423l1 1v3h0c1 1 1 3 1 5-1-2-3-3-3-4v-1s0-1-1-1c1-1 2-2 2-3h0z" class="r"></path><path d="M249 449c1 3 2 7 5 10v1l-1 1c0-1-2-1-3-2l-2-3h1v-1-6z" class="P"></path><path d="M257 456c1 1 1 2 2 2h1 1v1 2 2 1 2l-2-1c-1 1-1 2-1 3v1-5-1l-1-1 1-1h0c0-1-1-2-2-3l1-2z" class="Q"></path><path d="M259 458h1 1v1 2-1l-1-1-1-1z" class="I"></path><path d="M258 461h1c0 1 1 1 1 2h-2l-1-1 1-1z" class="M"></path><path d="M237 411v-2l1-2c0 1 1 2 1 3l2 2c0-1 0-2 1-3 0 2-1 3 0 5l1-1 1-1v6 1s1 1 2 1l1 3c0 4 2 7 2 10 1 5 2 8 4 12 1 3 2 6 3 8v3c-1-1-2-2-3-2-1-1-2-4-2-5-3-7-5-13-7-20l-2-6h-1-1-1 0c0 1-1 2-2 3l-1-2v-1h-1 0l-2-2c1 0 1-1 2-2l-1-1 1-1s1-1 1-2c1-1 2-1 2-1l-1-3z" class="y"></path><path d="M240 413l2 3-2-1c0 1 0 2 1 3 0 1 1 2 1 4h1c0 1 0 2 1 3h0v1 3l-2-6-3-6h0c0-2 0-2 1-4z" class="N"></path><path d="M237 411v-2l1-2c0 1 1 2 1 3l2 2c0-1 0-2 1-3 0 2-1 3 0 5l1-1 1-1v6l-2-2-2-3c-1 2-1 2-1 4l-1-3-1-3z" class="C"></path><path d="M237 411v-2l1-2c0 1 1 2 1 3s1 2 1 3c-1 2-1 2-1 4l-1-3-1-3z" class="e"></path><path d="M238 414l1 3h0l3 6h-1-1-1 0c0 1-1 2-2 3l-1-2v-1h-1 0l-2-2c1 0 1-1 2-2l-1-1 1-1s1-1 1-2c1-1 2-1 2-1z" class="AC"></path><path d="M238 422l1 1c0 1-1 2-2 3l-1-2 2-2z" class="i"></path><path d="M239 417l3 6h-1-1-1 0l-1-1c1-2 0-3 1-5z" class="E"></path><path d="M236 415h1c1 2 1 4 0 5 0 1-1 2-2 3l-2-2c1 0 1-1 2-2l-1-1 1-1s1-1 1-2z" class="Z"></path><path d="M164 97l12 10h1c4 3 8 7 12 10l17 13c4 3 8 6 12 10 0 0 2 1 2 2 2 2 4 5 6 8 1 1 5 5 5 7-1 1-1 1 0 2l1 3c1 1 2 3 3 5v1h1c0 1 0 2 1 3v1h0c0 1 0 1-1 1l2 5c1 1 1 1 1 2 0 0 0 1-1 1h1c0 2 1 2 0 4h0c0 1 1 2 1 3l1-1c1 1 1 2 1 3l1 1v1c1 2 2 5 4 6v1c1 0 2 1 2 2 1 1 2 2 2 3 1 0 2 2 2 2h1 1l2 5h1c1 1 1 2 2 3 0 2 1 4 2 5 1 3 1 5 2 8s3 6 5 9c1 1 6 9 7 9h0 1c-1 1-1 1-2 1 0 1 0 2 1 3-1 1-1 1-1 3h-1l1 2-1 1h-1 0v1 2c0 1-1 2-2 2v3c1 0 2 1 2 2l1 2v1 1 2l-1 2c1 2 2 7 3 8 0 1-1 1-2 2s-1 2-2 2v1l-2 3v1l-1 2c0 1 0 2-1 3h0l-1 3h0c0 1-1 1-1 2l-1 4-1 1-1-1v2l-1 1h0l-1 1h0v3 4l-2 7c-1-4 0-7-3-12h0v-1l-1-4-1-2-3-11c-1 0-2-1-3 0v1h-1c0-1-1-2-1-2l-2-2c0-2-1-4-1-5-1-2-1-3-1-4v-3-2l-1-2h-1v1c-1 0-1-1-1-1l-1 1v-1h-1v-1h0c0-1 0-1-1-2 0 1-1 2-1 2l-2 1h-2l1-1-2-2 5-5c-1-1 0-2 0-3v-1l2-2-2-2c-1 0-1-1-2-2 0 1 0 1-1 2h-1 0l-2 2h-3s0-1-1-1h-1l-2 2h-1-1c-1 0-2-1-3-2l1-1h-1 0c-1 1-2 2-2 3l-4 3v1h1v1h-1c-1 0-2 1-4 1h-1c-1 0-2 0-4 1h0c-1 0-2 0-3 1l-1-1c-1 0-2 0-3 1 0 0-1-1-2-1v1h-2-1v-1h-2-1 0l-3-1c0-1 0-1-1-1h-2l-1-1 2-1h-2c2-1 4-3 6-4h0l-1-1h-2c-1 1-2 1-3 2h0v-1h-2s1-1 1-2l-2-1h-1l-1-1h-1c-1 2-2 2-4 3v1l-2-2h0l-1-1-1-1h0v-1l-1-1c-1 0-1 0-2-1 0-1 0-1 1-2l-1-1-1 1h-1l-1-1h0l-1-1 1-2v-1h0c0-2 1-3 0-4-1 1-3 1-5 1h-1l-4-4h-1-1l-1-1s-4-3-5-3c-1-1-2-2-4-3 0 0-1-1-2-1l-8-5-6-3h0c-1-1-1-1-2-1h0c-1 0-1 0-2-1 1-1 1-1 1-2h2l-2-2h3c-1-1-1-1-1-2h0c1 0 1-1 2-1-2-1-3-1-4-1l-1-1h0c-1 0-1 1-1 1-1-1-2-1-3-1h0-1l-2-2 1-1h1v-2h1v-1l1-1c0-1 0-1 1-2h0v-2c0-1 0-2-1-3h1c0-2 0-4 2-5h0c-2 0-3-1-4-1v-1c0-1 1-2 2-2 0-1-1 0-1-1h-1l1-2c1-1 1-3 2-4-1-2 0-3 0-5l1-2c1-1 1-2 2-2h1c1-1 1-2 2-3h1 3 4 0 1v1h2v2c1 0 1 0 1-1h1l1 2c1 0 1 1 2 1 0 1 0 1-1 1l-1 1h1 1l1 1c2 0 4 0 6-1h1 0c1 0 2 0 2-1h1l4-1h-2v-1h3c1 0 2-1 4-1 1 0 2-1 4-1v-1l2-1c0-1-1-1-2-1l2-2-1-1-1-1h-1l-1-1h-1 0v-2h2c1 0 2-1 4-1l1-1h0-3c1 0 0 0 1-1 1 0 2 0 3-1 1 1 1 1 2 1h0c1 0 1 0 2 1h3v1c2 0 3 0 4 1h2c0 1-1 1-1 2h0 2 1l4-2v-5h0c0-1 1-2 1-3v1h1l1-2c-1-1-1-2-1-2v-4c-1-1-1-2-1-3l-1-2 1-1c1 1 1 1 1 2s0 0 1 1v-5-3c-1-2-1-4-2-6-1-3-2-6-4-8v-2h0l-7-8-14-13 1-1z" class="AA"></path><path d="M215 150h1l-1 1h0v-1z" class="x"></path><path d="M189 239c0-1-1-1-2-2h0 1 0l1-1h1l-1 1h0 1l1 1-2 1z" class="g"></path><path d="M215 176v-1c0-1 1-1 1-2l1 2-1 1h-1 0z" class="U"></path><path d="M215 214c0-1 0-2 1-3l1 1c0 1-1 3-1 4v-3l-1 1zm-21 15h1l2 1h0l-5 1v-1c1 0 2 0 2-1z" class="B"></path><path d="M198 138h1c1 0 1 0 1 1-1 1-1 1-1 2l-1 1v-1l1-1c0-1-1-2-1-2z" class="g"></path><path d="M198 153h1 0l-1 4h0v-1-1l-1 1h-1l2-3h0z" class="x"></path><path d="M194 144l2-3h0c1 1 1 1 1 2l-2 2-1-1z" class="v"></path><path d="M185 242c1-2 2-2 4-3l-1 2-3 1h0z" class="w"></path><path d="M222 185l2 4-1 2c-1-2-1-3-1-6z" class="F"></path><path d="M184 230v1h2 0 3v1c-1 0-3 0-4 1h-1c0-1-1-2 0-3z" class="l"></path><path d="M207 169l1 1h-1c-1 1-2 1-2 2-1 0-1-1-2-2h0l1-1c1 0 1 0 1 1h0l2-1z" class="x"></path><path d="M204 141h0v-2-3h0c1 2 1 2 1 4v2 2c0-1 0-2-1-3zm-3-5c1 1 2 3 1 5 0 1 0 2-1 3v-8h0z" class="U"></path><path d="M215 214l1-1v3c0 1 0 2-1 3 0-1 0-1-1-2 0-1 0-2 1-3zm-29 17h0c1-1 2-1 3-2 1 1 2 1 3 1v1l-3 1v-1h-3z" class="F"></path><path d="M199 130c1 2 2 4 2 6h0l-2-2h0c-1-1-1-3 0-4z" class="Y"></path><path d="M184 233h1l-1 2h-4l-1 1c-1 1-2 1-2 1 1-1 2-2 3-2s2 0 2-1l1-1h1z" class="U"></path><path d="M216 199c-1 2-3 4-5 5h-1 0l2-2 3-3h1z" class="w"></path><path d="M190 236c1-1 3-1 5-2-1 1-2 2-2 3h-1l-1 1-1-1h-1 0l1-1zm-20 6c1 0 3-1 3-1 0 2 0 3 1 4-1 1-3 2-5 2v-1c1 0 2-1 2-1l2-1v-2h-2-1z" class="U"></path><path d="M186 228h3 2c0 1-1 1-2 1-1 1-2 1-3 2h0 0-2v-1h0l3-1h0l-1-1z" class="W"></path><path d="M220 142c2 2 4 5 6 8v2h-1c0-1 0-1-1-2v-1c0-1-1-2-2-3 0 0 0-1-1-1 0-1-1-2-1-3z" class="F"></path><path d="M214 217c1 1 1 1 1 2s-1 3-2 4h0v1c-1-1-1-1-2-1l1-2c0-1 1-2 2-4z" class="R"></path><path d="M212 221h1v2h0v1c-1-1-1-1-2-1l1-2z" class="C"></path><path d="M196 228v-1c1 0 1 0 2-1h1v1h-1 2v2 1h-3 0l-2-1h-1-1l3-1z" class="g"></path><path d="M195 229c2-1 3-1 4-1l1 1c-1 1-2 0-3 1l-2-1z" class="F"></path><path d="M206 159l1 1h1c0-1 1-1 1-1 0 1-1 3-2 4v2l-1-1h-1c1-1 1-1 1-2h0l-2-2 1-1h1z" class="B"></path><path d="M201 144c1-1 1-2 1-3 0 3-1 6 1 9h0l1 1c-1 1-1 1-2 1 0-1-1-2-1-3-1-2 0-3 0-5z" class="g"></path><path d="M220 201c0-1 1-2 1-3 0 0-1 0 0-1h0c0-2 0-4-1-6h1 1l2 4c-2 1-3 4-4 6z" class="w"></path><path d="M190 226l2 1h4v1l-3 1h1c0 1-1 1-2 1s-2 0-3-1c1 0 2 0 2-1h-2v-1l1-1z" class="Y"></path><path d="M209 151v3h1 1 0 2s1 0 1 1v3c0 1 1 2 1 4 0 1-1 2-1 2v-1-1c1-1 0 0-1-1 1 0 1 0 1-1l-1-1c0-1 0-2 1-2 0-1 0-1-1-1-1-1-2-1-3-1-1-1-1-2-1-3l-1 2v3h-1v-3c1 0 1-1 1-2l1-1z" class="x"></path><path d="M203 177l2-2 1 1-2 2v1l1-1h0 2 1c0-1 1-2 2-2v2c-1 1-1 2-2 2-2-2-2-1-5 0v-3z" class="B"></path><path d="M194 224c1 0 2-1 3-1l-1 2v1h0v1h0-4l-2-1h1v-1-1h3z" class="E"></path><path d="M196 225v1h0v1h0-4l4-2z" class="v"></path><path d="M194 224c1 0 2-1 3-1l-1 2v1-1h-3l1-1zm17-70v-2c1-2 0-3 0-5v-5c1 2 1 3 2 5v6 1h-2z" class="Y"></path><path d="M217 175c0 1 1 3 0 5v4 2l1-1h1 0v2h-2 0c-1 0-1-1-2-1 1-1 1-2 1-3v-1l1-1h-1c0-1 0-2-1-3l1-2 1-1z" class="g"></path><path d="M215 176h0 1l-1 2v1l-1-1c-1 1-1 2-2 3-1 0-1 1-2 1h-1l1-2c0-1 2-3 3-4l1 1 1-1z" class="F"></path><path d="M206 145s1 0 1-1v-5c2 3 2 8 3 12h-1l-1 1v-2-3c-1 0-2 0-3-1l1-1z" class="w"></path><path d="M208 150h1v1l-1 1v-2zm-4-9c1 1 1 2 1 3l1 1-1 1v3h1 0v3h-1v-1h-1l-1-1c0-2 1-3 1-4 0-2-1-3 0-5z" class="Y"></path><path d="M220 201c1-2 2-5 4-6h1l-1 1v3c-2 1-2 4-3 7-1-2-1-3-1-5z" class="B"></path><path d="M199 134l-1 1h-2c-1-2-1-6-1-8 1 1 2 2 4 3-1 1-1 3 0 4h0z" class="v"></path><path d="M200 162c1 0 1-1 2-2s2-2 3-4h1v2 1h-1l-1 1 2 2-3 3h-1v-2h-1c-1 0-1 0-1-1z" class="U"></path><path d="M194 144l1 1h1 2c0 2-1 4-1 5 0 3 0 4-1 6l-1-1s1 0 1-1v-1c1 0 0-2 0-3s-1-1-1-2h0-1c0 2 0 4-1 6h0c1-3 1-7 1-10z" class="x"></path><path d="M185 242h0c-1 2-3 3-6 3l-1 1c-1 0-3 1-4 2-2-1-3-1-5 0v-2 1c2 0 4-1 5-2 2-1 4-1 5-1 2-1 4-1 6-2z" class="F"></path><path d="M209 164l1-1c0-1 1-2 2-2h1c-1 2-1 3-3 5-1 1-1 3-2 4l-1-1c0-1 0-2-1-3l1-1v-2l1 1h1z" class="U"></path><path d="M209 164l1-1c0-1 1-2 2-2v1c-1 1-1 2-2 3 0-1 0 0-1-1z" class="B"></path><path d="M202 209v-1c1 0 3-1 4-1h0 3s1 0 2 1l1-1h1c-1 1-2 2-3 2l-2 1h-1l-2 1-1-1h-2v-1z" class="Y"></path><path d="M202 209v-1c1 0 3-1 4-1h0 3c-2 1-4 1-6 2h-1z" class="x"></path><path d="M203 209h4c1-1 2-1 3 0h0l-2 1h-1l-2 1-1-1h-2v-1h1z" class="F"></path><path d="M204 210c1-1 2-1 4 0h0-1l-2 1-1-1z" class="m"></path><path d="M184 119c3 3 5 8 6 12 0 1 1 2 1 3v4-2h0l-1-1c-1-2-1-4-2-6-1-3-2-6-4-8v-2z" class="Y"></path><path d="M217 189v3c1 1 1 2 1 4l-2 3h-1l-1-1h0-1v-1l1-1-1-2c1 0 1-1 1-1l2-1v-2l1-1z" class="g"></path><path d="M214 198l3-3 1 1-2 3h-1l-1-1z" class="B"></path><path d="M203 222l1-1v1h2 0l-2 3c0 1-2 1-3 2-1-1-2-2-3-2h-2l1-2 3-1h1 2z" class="U"></path><path d="M201 222h2c-1 1-1 1-2 1v-1z" class="w"></path><path d="M164 97l12 10h1l-1 1c-3-2-5-5-8-6 2 2 5 5 7 6 1 1 2 2 2 3l-14-13 1-1z" class="x"></path><path d="M190 135l1 1h0v2l1 4c0 3 0 7-1 11l-1 1v-1c0-3 0-5-1-7-1-1-1-2-1-3l-1-2 1-1c1 1 1 1 1 2s0 0 1 1v-5-3z" class="F"></path><path d="M191 153v-6-3c0-1 0-2 1-2 0 3 0 7-1 11z" class="U"></path><path d="M190 138c1 5 0 10 0 15 0-3 0-5-1-7-1-1-1-2-1-3l-1-2 1-1c1 1 1 1 1 2s0 0 1 1v-5z" class="d"></path><path d="M196 219l4-1c1 1 3 2 4 3h0l-1 1h-2-1l-3 1c-1 0-2 1-3 1 0-1-1-1-1-1h-2-1l1-1h1l1-2 3-1z" class="R"></path><path d="M192 222c2-1 3-1 4-1v1l-3 1h0-2-1l1-1h1z" class="j"></path><path d="M196 219l4-1c1 1 3 2 4 3h0l-1 1h-2-1c-1-1-1-1-2-1s-1-1-2-2z" class="u"></path><path d="M221 206c1-3 1-6 3-7v-3c1 1 1 2 2 2h0l-1 1c0 2 0 3-1 5 0 1-1 1-1 2v2c-1 3-2 5-2 8-1-1-1-1-2-1l2-9z" class="E"></path><path d="M209 213h2 0v2h0c0 1 0 2-1 3h-1l-1 1c0 1-1 2-2 3h0-2v-1h0c-1-1-3-2-4-3 2-1 4-1 6-3h1c1 0 1-1 2-2z" class="B"></path><path d="M211 215h0c0 1 0 2-1 3h-1s-1-1-1 0h-1l4-3z" class="w"></path><path d="M204 221h1l3-2c0 1-1 2-2 3h0-2v-1h0z" class="g"></path><path d="M229 163l-3-6c-1-1-1-2-2-2 0-2-2-4-3-5 0-1 0-2-1-2h1l2 3v1h2 1v-2c1 1 5 5 5 7-1 1-1 1 0 2l1 3h-1v1h-2z" class="v"></path><path d="M226 150c1 1 5 5 5 7-1 1-1 1 0 2l1 3h-1 0c-2-3-4-7-5-10v-2z" class="R"></path><path d="M205 164h1l1 1-1 1c1 1 1 2 1 3l-2 1h0c0-1 0-1-1-1l-1 1h0c0 1 0 2-1 2 0 0-2 1-3 1-1 1-2 1-4 2l2 2h-1l1 1v1h-3v1c-1 0-1 0-2-1l1-1-1-1h-1-1 0c0-1 0-1-1-2l3-1h1 0 2c1 0 1 0 2-1 2 0 3-1 4-3l2-2c1-2 2-3 2-4z" class="B"></path><path d="M205 164h1l1 1-1 1c-1 1-1 2-2 3l-1-1c1-2 2-3 2-4z" class="m"></path><path d="M190 177h6l1 1v1h-3v1c-1 0-1 0-2-1l1-1-1-1h-1-1z" class="d"></path><path d="M197 177h0c2 0 3 0 4 1 1 0 1 0 2-1v3c3-1 3-2 5 0l-3 3h0l-1 1-1 1v-1h-1c-2 0-2 0-4-1h-2c1-1 1-2 2-2v-1h-4v-1h3v-1l-1-1h1z" class="u"></path><path d="M194 180v-1h3c1 1 3 0 4 0 0 1-1 2-1 2v1c1 0 2 0 3-1h1 0c0 1 1 1 1 1v1h0l-1 1-1 1v-1h-1c-2 0-2 0-4-1h-2c1-1 1-2 2-2v-1h-4z" class="O"></path><path d="M198 183c1-1 2-1 3-1h1c0 1 2 1 2 2l-1 1v-1h-1c-2 0-2 0-4-1z" class="K"></path><path d="M188 222h1 2l-1 1h1 2s1 0 1 1h-3v1 1h-1l-1 1v1h-3-3-3v-1h-1l1-1c1-1 1-1 1-2h-5 8v-2h2 2z" class="J"></path><path d="M188 222h1 2l-1 1h1-3v-1z" class="z"></path><path d="M188 227h1v1h-3-3 0c1-1 3-1 5-1z" class="F"></path><path d="M186 222h2v1c-1 1-3 1-4 1v-2h2z" class="T"></path><path d="M188 227c0-1-1-1-1-2h0c1 0 2 0 3-1h1v1 1h-1l-1 1h-1z" class="l"></path><path d="M181 224h7c-3 1-5 2-8 2 1-1 1-1 1-2z" class="V"></path><path d="M211 223c1 0 1 0 2 1l-2 3-6 5v-1c-1 1-1 1-2 1h0c-3 1-7 3-10 5 0-1 1-2 2-3 0 0 0-1 1-1 3-2 6-3 9-5 0 0 1-1 1-2h1c1 0 2-1 3-2l1-1z" class="i"></path><defs><linearGradient id="a" x1="208.101" y1="223.659" x2="208.944" y2="229.817" xlink:href="#B"><stop offset="0" stop-color="#6c6b6c"></stop><stop offset="1" stop-color="#8a8789"></stop></linearGradient></defs><path fill="url(#a)" d="M211 223c1 0 1 0 2 1l-2 3-6 5v-1c-1 1-1 1-2 1h0c0-1 4-3 4-4v-2h1c1 0 1-2 2-2l1-1z"></path><path d="M160 237h4 3 0c-1 1-2 1-2 1h-1c1 1 1 1 1 2h1c1 0 2 1 3 1l1 1h1 2v2l-2 1v-1c-1 0-1-1-2-2h-1l-1 1c0 1-1 2-2 2l-1-1c0 1 0 1-1 2s-2 1-3 3l-1-1 1-2v-1h0c0-2 1-3 0-4h1l2-2-1-1-2-1z" class="B"></path><path d="M161 241l2-2v2h-2z" class="U"></path><path d="M160 246l3-4c1-1 2-1 3-1h0l-2 3h0c0 1 0 1-1 2s-2 1-3 3l-1-1 1-2z" class="j"></path><path d="M167 243l1-1h1c1 1 1 2 2 2v1s-1 1-2 1v2c2-1 3-1 5 0-3 1-7 2-9 5-1 0-1 0-2-1 0-1 0-1 1-2l-1-1-1 1h-1l-1-1h0c1-2 2-2 3-3s1-1 1-2l1 1c1 0 2-1 2-2z" class="AC"></path><path d="M160 249c1-2 2-2 3-3 0 1 1 2 0 2 0 1-1 2-2 2l-1-1h0z" class="F"></path><path d="M167 243l1-1h1c1 1 1 2 2 2v1s-1 1-2 1v2h-1v-1-3h0l-1-1z" class="m"></path><path d="M208 196h1v2h-1v1h1l4-2v1h1 0l1 1-3 3-2 2h0l-4 3c-1 0-3 1-4 1v1 1c-1 0-3 0-4-1 2 0 2-1 2-2h1l-1-1v-1-1-1l4-2h0 1 0l1-3h0v-1l2-1z" class="W"></path><path d="M208 196h1v2h-1c-1 1-1 2-3 3h0l1-3h0v-1l2-1z" class="O"></path><path d="M200 203l4-2v1h2 0c-2 1-3 2-4 3 1 0 1 0 2-1h2c0 1-2 2-3 2 0 0-1 1-2 1l-1-1v-1-1-1z" class="D"></path><path d="M201 207l-1-1v-1-1-1l1 1v1h0 0c1 0 2 0 2 1 0 0-1 1-2 1z" class="J"></path><path d="M213 197v1h1 0l1 1-3 3h-1v-1l-3 1-1-1v-1l1-1h1l4-2z" class="F"></path><path d="M213 197v1h1c-1 1-3 3-4 3l-1-1v-1l4-2z" class="U"></path><path d="M212 202l-2 2h0l-4 3c-1 0-3 1-4 1v1 1c-1 0-3 0-4-1 2 0 2-1 2-2h1c1 0 2-1 2-1 1 0 3-1 3-2h1 0c2 0 3-1 4-2h1z" class="Y"></path><path d="M206 162h0c0 1 0 1-1 2 0 1-1 2-2 4l-2 2c-1 2-2 3-4 3-1 1-1 1-2 1h-2 0-1c-2-1-3-2-5-2h-1v-1c1-1 2-1 2-2l1-3v1l1 1-1 1v2c0 1 0 1 1 1 1-1 3-1 4-1 0-1-1-1-1-2l1-1 3-3c1 0 2-1 2-1 0-1 0-1 1-2 0 1 0 1 1 1h1v2h1l3-3z" class="w"></path><path d="M206 162h0c0 1 0 1-1 2 0 1-1 2-2 4l-2 2c-1 0-2-1-3-1v-1h-1v-1c2-1 3-2 4-4h0 1v2h1l3-3z" class="g"></path><path d="M190 177h0 1 1l1 1-1 1c1 1 1 1 2 1h4v1c-1 0-1 1-2 2h2c2 1 2 1 4 1h1v1l-1 1c0-1 0-1-1-1v-1c-1 0-2 1-3 0-2 0-4 2-7 2l1-1h0c-1-1-3-1-4-2l-1-1h-1c-2 0-4 1-6 0 0-1 0-1-1-2h0v-2h1 3 1 0 2l1-1h3z" class="b"></path><path d="M180 178h3 1v1c-1 0-1 1-2 1h-3 0v-2h1z" class="r"></path><path d="M190 177h0 1 1l1 1-1 1c-2-1-6-1-8-1h2l1-1h3z" class="P"></path><path d="M180 182c2-2 5-2 7-2h3c0 1 1 1 1 2 1 0 4 0 5 1h2c2 1 2 1 4 1h1v1l-1 1c0-1 0-1-1-1v-1c-1 0-2 1-3 0-2 0-4 2-7 2l1-1h0c-1-1-3-1-4-2l-1-1h-1c-2 0-4 1-6 0z" class="I"></path><path d="M174 229c0-1 0-1-1-2h1l2 1c2 0 4 1 4 0h3 3l1 1h0l-3 1h0c-1 1 0 2 0 3h-1l-1 1c0 1-1 1-2 1s-2 1-3 2c-3 1-4 1-7 0h-3 0-3 2l2-2c1 0 2-1 3-1l-1-1c0-1 2-1 3-1v-1h-1l2-1v-1h0z" class="C"></path><path d="M178 234h2c1-1 2-1 3-1l-1 1c0 1-1 1-2 1 0-1-1-1-2-1z" class="E"></path><path d="M170 237c2 0 5-1 6-2-1 0-1 0-1-1 1-1 2 0 3 0s2 0 2 1c-1 0-2 1-3 2-3 1-4 1-7 0z" class="B"></path><path d="M174 230c1 1 2 1 3 1 2 0 4 1 5 1-1 1-2 1-4 1h-6l1 1-1 1h0c-1 0-2 0-3 1 0 0-1 0-1 1h-1 0-3 2l2-2c1 0 2-1 3-1l-1-1c0-1 2-1 3-1v-1h-1l2-1z" class="V"></path><path d="M174 229c0-1 0-1-1-2h1l2 1c2 0 4 1 4 0h3 3l1 1h0l-3 1c-1 0-1 0-2 1h-2c-1-1-2-1-3-1v1c-1 0-2 0-3-1v-1h0z" class="d"></path><path d="M174 229c0-1 0-1-1-2h1l2 1h1c0 1 1 1 2 1v1c-2 0-3 0-5-1h0z" class="t"></path><path d="M195 213l1-1v-1-1c1-1 0-1 0-2l4-1c0 1 0 2-2 2 1 1 3 1 4 1h2l1 1 2-1c1 1 1 1 2 1h1v1h2l-1 3h0v-2h0-2c-1 1-1 2-2 2h-1c-2 2-4 2-6 3l-4 1-3 1h-1v-1c1 0 1 0 2-1v-2h-2-1-2l-1-1-2 1-2-1v-1c1 0 2 0 2-1h3l1-1c2 1 2 1 4 1h1z" class="U"></path><path d="M205 211l2-1c1 1 1 1 2 1-1 1-3 1-4 1l-1-1h1z" class="g"></path><path d="M189 213l1-1c2 1 2 1 4 1h1 3l-6 3h-1-2l-1-1-2 1-2-1v-1c1 0 2 0 2-1h3z" class="I"></path><path d="M186 213h3l-1 1h-2c1 1 1 1 2 1l-2 1-2-1v-1c1 0 2 0 2-1z" class="P"></path><path d="M189 213l1-1c2 1 2 1 4 1h1 3l-6 3h-1-2c1-1 2-1 3-2h-1-3l1-1z" class="t"></path><path d="M195 213l1-1v-1-1c1-1 0-1 0-2l4-1c0 1 0 2-2 2 1 1 3 1 4 1h2l1 1h-1 0c-2 1-4 2-6 2h-3z" class="E"></path><path d="M203 214h0l1-1h3c0 1 1 0 2 0-1 1-1 2-2 2h-1c-2 2-4 2-6 3l-4 1-3 1h-1v-1c1 0 1 0 2-1v-2h4c1-1 3-1 5-2z" class="F"></path><path d="M199 218c2-1 5-3 7-3h0c-2 2-4 2-6 3l-4 1-3 1h-1v-1c1 0 1 0 2-1 2-1 3 0 5 0z" class="H"></path><path d="M203 214h0c-1 2-3 2-5 3l1 1c-2 0-3-1-5 0v-2h4c1-1 3-1 5-2z" class="S"></path><path d="M215 179v-1c1 1 1 2 1 3h1l-1 1v1c0 1 0 2-1 3 1 0 1 1 2 1h0 2l-2 2-1 1v2l-2 1s0 1-1 1l1 2-1 1-4 2h-1v-1h1v-2h-1l1-1-1-1h-1l-2 1c0-1-1-1-1-1l1-1v-1h0c-1 1-2 1-3 0v-1l3-2v-1c1-1 2-1 3-3l-1-1 1-1 1-1h1c1 0 1-1 2-1 1-1 1-2 2-3l1 1z" class="B"></path><path d="M205 192h0c1-1 3-2 5-2l-2 2v2h0-1l-2 1c0-1-1-1-1-1l1-1v-1z" class="S"></path><path d="M208 192v2h0-1l-2 1c0-1-1-1-1-1l1-1c1 0 2 0 3-1z" class="O"></path><path d="M209 182h1c1 0 1-1 2-1 1-1 1-2 2-3l1 1-1 1c-1 1-1 5-3 5l-1 1 2-2-1-1h-3l1-1z" class="j"></path><path d="M208 183h3l1 1-2 2c-1 1-3 3-5 3v-1c1-1 2-1 3-3l-1-1 1-1z" class="S"></path><path d="M217 187h2l-2 2-1 1v2l-2 1s0 1-1 1l1 2-1 1-4 2h-1v-1h1v-2h-1l1-1-1-1h0v-2l2-2 1-1c0-1 1-1 2-1v3c2 0 3-3 4-4z" class="R"></path><path d="M211 189c0 1 1 2 0 2l-3 3v-2l2-2 1-1z" class="s"></path><path d="M209 195s1-1 2-1c1-1 1 0 2 0h0l1 2-1 1-4 2h-1v-1h1v-2h-1l1-1z" class="E"></path><path d="M209 195s1-1 2-1c1-1 1 0 2 0l-4 4v-2h-1l1-1z" class="C"></path><path d="M223 206c0-1 1-1 1-2l1 1h0c1 0 1 1 1 1l1 1v1h2l1 2h0v1l-1 1v1c0 1 0 3 1 4v2 4c-1 0-1 1-1 2 0-1 0-2-1-3v4c-1 1-1 1-2 1h0c-1 1-1 1-2 1v3l-1-1h-1-1l1-6-1 1v-3-1l-1 1h-1v2l-1 1h-1v-2c1-3 1-5 2-8 1 0 1 0 2 1 0-3 1-5 2-8v-2z" class="H"></path><path d="M221 221v-2h0c0-1 1-1 1-2l-1 5v-1z" class="W"></path><path d="M225 212c1 2 1 3 0 6v3h-1l-1-2c0-2 1-5 2-7z" class="C"></path><path d="M223 219l1 2h1v1c-1 2-1 4-1 6v3l-1-1h-1-1l1-6h0l1-5z" class="l"></path><path d="M222 224h1v6h-1-1l1-6h0z" class="i"></path><path d="M225 218h1 0v1l1 1v-2h1v3 1 4c-1 1-1 1-2 1h0c-1 1-1 1-2 1 0-2 0-4 1-6v-1-3z" class="v"></path><path d="M225 218h1 0v1l1 1v-2h1v3l-1-1-1 1v-1 1c-1 0-1 1-1 1v-1-3z" class="Y"></path><path d="M223 206c0-1 1-1 1-2l1 1h0c1 0 1 1 1 1 0 3 0 4 1 7 1 1 1 1 1 2l-1 1c0 1 1 1 1 2h-1v2l-1-1v-1h0-1c1-3 1-4 0-6s-1-4-2-6z" class="R"></path><path d="M225 205h0c1 0 1 1 1 1 0 3 0 4 1 7 1 1 1 1 1 2l-1 1c0-2-1-3-2-5v-6z" class="s"></path><path d="M226 206l1 1v1h2l1 2h0v1l-1 1v1c0 1 0 3 1 4v2 4c-1 0-1 1-1 2 0-1 0-2-1-3v-1-3c0-1-1-1-1-2l1-1c0-1 0-1-1-2-1-3-1-4-1-7z" class="R"></path><path d="M227 210c2 0 2 1 3 1l-1 1-2-2z" class="D"></path><path d="M227 208h2l1 2h0v1c-1 0-1-1-3-1v-2z" class="h"></path><path d="M228 215c0 1 0 2 1 2 0 2-1 3 1 5v-3 4c-1 0-1 1-1 2 0-1 0-2-1-3v-1-3c0-1-1-1-1-2l1-1z" class="z"></path><path d="M221 216c0-3 1-5 2-8v5s0 4-1 4c0 1-1 1-1 2h0v2l-1 1h-1v2l-1 1h-1v-2c1-3 1-5 2-8 1 0 1 0 2 1z" class="l"></path><path d="M219 215c1 0 1 0 2 1l-1 2c0 1-1 3-1 4-1 0-1 1-2 1 1-3 1-5 2-8z" class="R"></path><path d="M189 146c1 2 1 4 1 7v1 8h0c-1 1 0 3-1 4l-1 3c0 1-1 1-2 2v1h1c2 0 3 1 5 2l-3 1c1 1 1 1 1 2h-3l-1 1h-2 0-1-3-1-2c0-1-1-2-1-2h1c1 0 1-1 1-1l-1-1s0-1 1-1h-4v-1h0c0-1 1-1 2-2s1-2 2-3h-4-3c-2 1-3 2-6 2 0-1-1-1-2-1l2-2-1-1-1-1h-1l-1-1h-1 0v-2h2c1 0 2-1 4-1l1-1h0-3c1 0 0 0 1-1 1 0 2 0 3-1 1 1 1 1 2 1h0c1 0 1 0 2 1h3v1c2 0 3 0 4 1h2c0 1-1 1-1 2h0 2 1l4-2v-5h0c0-1 1-2 1-3v1h1l1-2c-1-1-1-2-1-2v-4z" class="t"></path><path d="M180 178h-2v-1l2-2c2 0 3-1 5 1-2-1-3-1-4 0-1 0-1 1-1 1 1 1 2 1 3 1h-3z" class="P"></path><path d="M183 178c-1 0-2 0-3-1 0 0 0-1 1-1 1-1 2-1 4 0l2 1-1 1h-2 0-1z" class="f"></path><path d="M180 173l-2-1c-1 0-1 0-2-1h1c1-1 3-1 4-1 0 1 1 1 1 1 1 1 2 0 2 1 2 0 3 0 5 1h-9z" class="o"></path><path d="M187 156h0v2c0 3 1 9-1 11h-4 0l2-1v-1c-1 0-1 0-2-1h1l1-1s1-1 1-2h-3 1l4-2v-5z" class="F"></path><path d="M189 146c1 2 1 4 1 7v1 8h0c-1 1-1 1-1 2-1 1-1 2-1 3v1c-1-2 0-4 0-5 0-2 0-3-1-4v-1-2c0-1 1-2 1-3v1h1l1-2c-1-1-1-2-1-2v-4z" class="K"></path><path d="M177 164c2 0 4 1 6 1v1h-1c-2 1-3 3-4 4h5c1 0 1 1 1 2 0-1-1 0-2-1 0 0-1 0-1-1-1 0-3 0-4 1h-1c1 1 1 1 2 1l2 1h-2-4v-1h0c0-1 1-1 2-2s1-2 2-3h-4v-1c1 0 2-1 2-2h1z" class="f"></path><path d="M176 164h1l2 2h0l-1 1h0-4v-1c1 0 2-1 2-2z" class="p"></path><path d="M164 159c1 0 0 0 1-1 1 0 2 0 3-1 1 1 1 1 2 1h0c1 0 1 0 2 1h3v1c2 0 3 0 4 1h2c0 1-1 1-1 2h0 2 3c0 1-1 2-1 2l-1 1v-1c-2 0-4-1-6-1h-1c0 1-1 2-2 2v1h-3c-2 1-3 2-6 2 0-1-1-1-2-1l2-2-1-1-1-1h-1l-1-1h-1 0v-2h2c1 0 2-1 4-1l1-1h0-3z" class="G"></path><path d="M160 161h2v1h2v1h-3-1 0v-2z" class="P"></path><path d="M172 161c2 0 3 0 4 1h1v1h-4l-5 1-2 1h-2l-1-1 9-3z" class="I"></path><path d="M173 163v-1c1-1 2 0 3 0h1v1h-4z" class="Q"></path><path d="M177 162h1v1h2 0 2 3c0 1-1 2-1 2l-1 1v-1c-2 0-4-1-6-1h-1c-1 1-2 1-3 1h-2-2l-1-1 5-1h4v-1z" class="G"></path><path d="M164 159c1 0 0 0 1-1 1 0 2 0 3-1 1 1 1 1 2 1h0c1 0 1 0 2 1h3v1c2 0 3 0 4 1h2c0 1-1 1-1 2h0 0-2v-1h-1-1c-1-1-2-1-4-1 0 0 0-1-1-1-2 0-3 0-4 1-1 0-1 0-1-1l1-1h0-3z" class="r"></path><path d="M166 165l2-1 1 1h2 2c1 0 2 0 3-1 0 1-1 2-2 2v1h-3c-2 1-3 2-6 2 0-1-1-1-2-1l2-2-1-1h2z" class="P"></path><path d="M166 165l2-1 1 1h2c-2 0-3 0-5 1v1-1-1z" class="t"></path><path d="M205 183l-1 2h3v-1l1 1c-1 2-2 2-3 3v1l-3 2v1c1 1 2 1 3 0h0v1l-1 1s1 0 1 1l2-1h1l1 1-1 1-2 1v1h0l-1 3h0-1c-2 0-2 0-3-2v1l-1 1-1 1v-2c-1-1-4 0-6-1h0-2c-2 0-4 0-6-1-1 0-3 0-4-1l-1-1c-1 0-2-1-3-1l-2-1c-2-1-4-3-6-4l2-1h5 4 3v1h0v-1h1v-1-1h2 2c1 0 2-1 3-1 3 0 5-2 7-2 1 1 2 0 3 0v1c1 0 1 0 1 1l1-1 1-1 1-1z" class="M"></path><path d="M183 190h0v-1h1v-1-1h2l-1 1c1 1 5 1 7 1h1 2-1l1 1-1 2h-1-8 0l-1-1-2-1h0 1z" class="K"></path><path d="M194 189l1 1-1 2h-1-8 0l-1-1c4 0 7-1 10-2z" class="a"></path><path d="M205 183l-1 2h3v-1l1 1c-1 2-2 2-3 3v1l-3 2c-2 1-3 3-5 3h0s0-1-1-2c0 1-2 1-2 2h-1-1v-1l1-1h1 1l1-2s2-3 3-3l3-1 1-1 1-1 1-1z" class="R"></path><path d="M199 187h3c-1 2-5 4-7 5l1-2s2-3 3-3z" class="I"></path><path d="M205 188v1l-3 2c-2 1-3 3-5 3h0s0-1-1-2c3-1 6-2 9-4z" class="f"></path><path d="M171 189h5 4 3v1h-1 0l2 1 1 1h0 8l-1 1v1h1c-2 1-4 1-5 1l-2 1h-1v1c-2-1-3-1-5-1-1 0-2-1-3-1l-2-1c-2-1-4-3-6-4l2-1z" class="G"></path><path d="M185 192h8l-1 1c-1 0-1 0-3 1h-5c1-1 1-1 1-2z" class="f"></path><path d="M179 193h3c0 1 0 1 1 1 1 1 3 1 5 1l-2 1h-1v1c-2-1-3-1-5-1-1 0-2-1-3-1v-1l2-1z" class="K"></path><path d="M179 193h3c0 1 0 1 1 1h-4c0 1 1 1 1 2-1 0-2-1-3-1v-1l2-1z" class="J"></path><path d="M171 189h5c-1 1-1 1-3 1v1c2 1 3 2 5 2h1 0l-2 1v1l-2-1c-2-1-4-3-6-4l2-1z" class="D"></path><path d="M176 189h4 3v1h-1 0l2 1 1 1h0c0 1 0 1-1 2-1 0-1-1-1-1-3-1-4 0-6-3h-4c2 0 2 0 3-1z" class="P"></path><path d="M182 190l2 1 1 1c-2 0-4 0-5-1h0l2-1z" class="f"></path><path d="M176 189h4 3v1h-1-5-4c2 0 2 0 3-1z" class="Z"></path><path d="M197 194c2 0 3-2 5-3v1c1 1 2 1 3 0h0v1l-1 1s1 0 1 1l2-1h1l1 1-1 1-2 1v1h0l-1 3h0-1c-2 0-2 0-3-2v1l-1 1-1 1v-2c-1-1-4 0-6-1h0-2c-2 0-4 0-6-1-1 0-3 0-4-1l-1-1c2 0 3 0 5 1v-1h1l2-1c1 0 3 0 5-1h1c0-1 2-1 2-2 1 1 1 2 1 2h0z" class="d"></path><path d="M194 198v-1h2c0-1 0-1 1-1v2h-3z" class="K"></path><path d="M185 196h6c-1 1-2 2-4 1h-2 0v-1z" class="G"></path><path d="M194 194c0-1 2-1 2-2 1 1 1 2 1 2-1 1-2 1-3 2v-2h0z" class="e"></path><path d="M193 194h1 0v2h-3-6 1l2-1c1 0 3 0 5-1z" class="o"></path><path d="M197 194c2 0 3-2 5-3v1c1 1 2 1 3 0h0v1l-1 1h-2c-2 1-4 1-6 2v-1l1-1z" class="E"></path><path d="M207 194h1l1 1-1 1-2 1v1h0l-1 3h0-1c-2 0-2 0-3-2v1l-1 1-1 1v-2c-1-1-4 0-6-1h0-2c-2 0-4 0-6-1 3 0 6 1 9 0h3 0c1 0 2 0 3-1h-1l3-3h2s1 0 1 1l2-1z" class="L"></path><path d="M207 194h1l1 1-1 1-2 1c0-1 1-2 1-3z" class="k"></path><path d="M193 199h4c1 0 2 0 3-1 0 1 1 1 1 2l-1 1-1 1v-2c-1-1-4 0-6-1h0z" class="D"></path><path d="M202 194h2s1 0 1 1l-5 2h-1l3-3z" class="K"></path><path d="M206 198h0l-1 3h0-1c-2 0-2 0-3-2 2 0 3-1 5-1z" class="b"></path><path d="M219 171v-1s1 0 1 1v-2c0-1 1-4 2-4v-5c-1-1-3-2-3-4h0c2 2 4 5 5 7 2 2 4 4 6 5-1-2-2-3-4-5l-7-9c0-2-2-3-2-5h1c1 2 2 4 3 5l3 3c0 1 0 1 1 2 1 2 2 4 4 5v-1h2v-1h1c1 1 2 3 3 5v1h1c0 1 0 2 1 3v1h0c0 1 0 1-1 1l2 5c1 1 1 1 1 2 0 0 0 1-1 1h1c0 2 1 2 0 4h0c0 1 1 2 1 3l-1 1h-2l-1 1h-2-2l-1-1h-1c-1-1-1-2-2-3h-1v1l-1 1v1l-2-3c-1 0-1-1-1-2l-2-2v-1c-1-1-1-3 0-5l-1-3-1-2z" class="w"></path><path d="M219 171c1 0 1 1 2 1v-2l1-1c0 1 0 2 1 2v-1h1v2 1h0c-1 0-1 0-2-1v2h-1v-1h-1l-1-2z" class="B"></path><path d="M224 170v2 1c0-1-1-1-1-2l1-1z" class="F"></path><path d="M232 162c1 1 2 3 3 5v1h1c0 1 0 2 1 3v1h0c0 1 0 1-1 1-1-3-3-6-5-10v-1h1z" class="S"></path><g class="B"><path d="M230 175c0-1-1-1-1-2l-1-1-2-2v-1-1l1 1 1 1 3 3c0 1-1 0 0 1 0 1 1 1 2 2l1 1 2 4c0 1 0 2 1 4h-1s-1 0-1-1v-1l1-1c-1-2-4-5-6-7z"></path><path d="M234 176l-1-1h1c2 1 2 3 4 3 1 1 1 1 1 2 0 0 0 1-1 1h1c0 2 1 2 0 4h0c-1-1-1-1-1-2h0l-2-2-2-4v-1z"></path></g><path d="M234 176c1 2 3 3 4 6v1l-2-2-2-4v-1zm-8-3s1 1 2 1c1 1 1 1 2 1 2 2 5 5 6 7l-1 1h-2c-2 0-6-8-7-10z" class="g"></path><path d="M224 172c1 0 1 1 2 1 1 2 5 10 7 10h2v1c0 1 1 1 1 1h1c-1-2-1-3-1-4l2 2h0c0 1 0 1 1 2 0 1 1 2 1 3l-1 1h-2l-1 1h-2-2l-1-1h-1c-1-1-1-2-2-3h-1v1l-1 1v1l-2-3c-1 0-1-1-1-2l-2-2v-1c-1-1-1-3 0-5l-1-3h1v1h1v-2c1 1 1 1 2 1h0v-1z" class="m"></path><path d="M225 178c0 1 1 3 2 3-1-2-2-3-2-5 2 2 3 3 4 6h0c-1 1-1 2-1 3l1 1v-1c1 2 2 3 2 4h-1c-1-1-1-2-2-3h-1 0c-1-1-1-1 0-2-1-2-2-3-3-5l1-1z" class="W"></path><path d="M220 173h1v1l1 3c1 0 1-1 2 1h1l-1 1c1 2 2 3 3 5-1 1-1 1 0 2h0v1l-1 1v1l-2-3c-1 0-1-1-1-2l-2-2v-1c-1-1-1-3 0-5l-1-3z" class="R"></path><path d="M221 176l3 9 1 1h-1c-1 0-1-1-1-2l-2-2v-1c-1-1-1-3 0-5z" class="Y"></path><path d="M224 185l1-1c0-1 0-1-1-2 0-1 0-1-1-2l1-1c1 2 2 3 3 5-1 1-1 1 0 2h0v1l-1 1v1l-2-3h1l-1-1z" class="i"></path><path d="M229 182c1 1 2 1 3 3l2 1c1 0 1-1 2-1 0 0 0 1 1 1v-2l1-1c0 1 0 1 1 2 0 1 1 2 1 3l-1 1h-2l-1 1h-2-2l-1-1c0-1-1-2-2-4v1l-1-1c0-1 0-2 1-3z" class="l"></path><path d="M229 185h1l2 2c1 0 2 2 2 3h-2l-1-1c0-1-1-2-2-4z" class="Z"></path><path d="M238 183c0 1 0 1 1 2h-1v1h-1l-1 1v1h-1c-2-1-2-2-3-3l2 1c1 0 1-1 2-1 0 0 0 1 1 1v-2l1-1z" class="E"></path><path d="M174 167h4c-1 1-1 2-2 3s-2 1-2 2h0v1h4c-1 0-1 1-1 1l1 1s0 1-1 1h-1s1 1 1 2h2v2h0c1 1 1 1 1 2 2 1 4 0 6 0h1l1 1c1 1 3 1 4 2h0l-1 1c-1 0-2 1-3 1h-2-2v1 1h-1v1h0v-1h-3-4-5-3s-1-1-2-1h0c0-1 1-1 1-2-2 0-3-1-4-2h0c-1 0-2-1-2-2-1 0-1-1-2-1h0-1c-1 0-3-1-4-2-1 0-1-1-3-2h-1c-1-1-2-1-3-1 1 0 2 0 2-1h1l4-1h-2v-1h3c1 0 2-1 4-1 1 0 2-1 4-1v-1l2-1c3 0 4-1 6-2h3z" class="H"></path><path d="M159 181c-1-1-1-1-1-2 3-1 7 0 11 0h0 2-5v1h1 4-2c-3 0-5 2-8 1h-2 0z" class="J"></path><path d="M167 176h6l1 1 2 1h-6-5c-1 0-2 0-3-1h-4c1 0 2-1 3-1 2 0 5 1 6 0z" class="Z"></path><path d="M174 177l2 1h-6-5c1 0 2 0 2-1 2-1 5 1 7 0z" class="r"></path><path d="M154 174c3 0 6 0 8-1v1l1 1h-1v1h5c-1 1-4 0-6 0-1 0-2 1-3 1h-2c-2-1-4-1-6-2l4-1z" class="d"></path><path d="M162 173h3 1c3-1 5-1 8-1v1h4c-1 0-1 1-1 1l1 1s0 1-1 1h-1s1 1 1 2h-1l-2-1-1-1h-6-5v-1h1l-1-1v-1z" class="V"></path><path d="M174 173h4c-1 0-1 1-1 1h-8c2-1 4 0 6 0l-1-1zm-1 3h3s1 1 1 2h-1l-2-1-1-1z" class="G"></path><path d="M162 173h3 1c3-1 5-1 8-1v1l1 1c-2 0-4-1-6 0-2 0-4 1-6 1l-1-1v-1z" class="r"></path><path d="M174 167h4c-1 1-1 2-2 3s-2 1-2 2h0c-3 0-5 0-8 1h-1-3c-2 1-5 1-8 1h-2v-1h3c1 0 2-1 4-1 1 0 2-1 4-1v-1l2-1c3 0 4-1 6-2h3z" class="P"></path><path d="M174 167h4c-1 1-1 2-2 3-1 0-2-1-3 0-1 0-4-1-6-1-1 1-3 1-4 2v-1l2-1c3 0 4-1 6-2h3z" class="b"></path><path d="M176 178h1 2v2h0c1 1 1 1 1 2 2 1 4 0 6 0h1l1 1c1 1 3 1 4 2h0l-1 1c-1 0-2 1-3 1h-2-2c0-1-1-1-2-1l-1-1h0-3v-1h-4c-1-2-3-3-5-4h2-4-1v-1h5-2l1-1h6z" class="G"></path><path d="M176 178h1 2v2l-8-1h-2l1-1h6z" class="L"></path><path d="M171 180h3c1 1 1 1 2 1v1s1 0 1 1c1 1 5-1 6 0v1h-5-4c-1-2-3-3-5-4h2z" class="Z"></path><path d="M186 182h1l1 1c1 1 3 1 4 2h0l-1 1c-1 0-2 1-3 1h-2-2c0-1-1-1-2-1l-1-1h0-3v-1h5 2c1-1 1-1 1-2z" class="K"></path><path d="M181 185h4l1 1h2v1h-2-2c0-1-1-1-2-1l-1-1z" class="J"></path><path d="M159 181h2c3 1 5-1 8-1 2 1 4 2 5 4h4v1h3 0l1 1c1 0 2 0 2 1v1 1h-1v1h0v-1h-3-4-5-3s-1-1-2-1h0c0-1 1-1 1-2-2 0-3-1-4-2h0c-1 0-2-1-2-2-1 0-1-1-2-1z" class="L"></path><path d="M174 184h4v1h3-7-3c0-1 2 0 3-1h0z" class="b"></path><path d="M159 181h2c3 1 5-1 8-1 2 1 4 2 5 4h-7 0-1c-1-1-2-1-3 0h0c-1 0-2-1-2-2-1 0-1-1-2-1z" class="h"></path><path d="M182 186c1 0 2 0 2 1v1 1h-1v1h0v-1h-3-4-5-3s-1-1-2-1h0c0-1 1-1 1-2l2 1h10l1-1h2z" class="W"></path><path d="M168 189c2-2 7-1 9-1v1h3-4-5-3z" class="d"></path><path d="M182 186c1 0 2 0 2 1v1 1h-1v1h0v-1h-3-3v-1h6c-1-1-3-1-4-1l1-1h2z" class="b"></path><path d="M221 221v1 3l1-1-1 6v1l-1 4h-1v1c-1 2-2 4-4 6s-4 3-7 4h0c-2 1-4 2-6 2-1 0-2 1-3 1s-1 1-2 1c-1 1-1 1-2 1-1 1-2 1-3 2l-4 2h0l-2 1v1h0l-1 1h-2c-1 1-2 1-3 2h0v-1h-2s1-1 1-2l-2-1h-1l-1-1h-1c-1 2-2 2-4 3v1l-2-2h0l-1-1-1-1h0v-1l-1-1c2-3 6-4 9-5 1-1 3-2 4-2l1-1c3 0 5-1 6-3l3-1 1-2h0l2-1 1-1h1c3-2 7-4 10-5h0c1 0 1 0 2-1v1l6-5 2-3v-1c1 2 1 4 0 6v2c1-1 2-2 2-3h1l1-3h1l1-1v-2h1l1-1z" class="m"></path><path d="M194 242h2l2 1-3 1c0-1-1-1-1-2z" class="h"></path><path d="M189 248c2-1 3-1 5-2h1c-1 1-3 3-5 3l-1-1z" class="j"></path><path d="M190 245h3l-6 3c-1-1-1-1-2-1 2-1 4-1 5-2zm6-3c1-1 1-2 2-3 1 1 2 1 3 1l-3 3-2-1z" class="c"></path><path d="M190 245c1-1 1-1 1-2 2-1 2-1 3-1 0 1 1 1 1 2l-2 1h-3z" class="T"></path><path d="M211 231c1 0 1 0 1 1v1c-3 2-4 6-7 6v-1 1c-1 0-2 0-3 1l6-6c1 0 2-2 3-3z" class="R"></path><path d="M193 240l1-1h5-1c-1 1-1 2-2 3h-2c-1 0-1 0-3 1 0 1 0 1-1 2s-3 1-5 2l-2 1-5 1h0c0-1-1-1-2-2h2v-1l1-1c3 0 5-1 6-3l3-1h0l5-1z" class="AC"></path><path d="M192 241v1c-1 1-2 1-3 1v-1l3-1z" class="E"></path><path d="M186 244c1-1 3 0 4 0h0c-1 1-2 1-3 1l-1-1z" class="C"></path><path d="M188 241l5-1h0l-1 1-3 1h0l-1-1z" class="c"></path><path d="M178 247c1 0 3-1 5-1v2l-5 1h0c0-1-1-1-2-2h2z" class="m"></path><path d="M211 227v2h1c0 1-1 2-1 2-1 1-2 3-3 3l-6 6h-1c-1 0-2 0-3-1h1-5l-1 1-5 1h0l1-2h0l2-1 1-1h1c3-2 7-4 10-5h0c1 0 1 0 2-1v1l6-5z" class="F"></path><path d="M211 229h1c0 1-1 2-1 2-1 1-2 3-3 3h-1c1-2 3-4 4-5z" class="C"></path><path d="M207 234h1l-6 6h-1c-1 0-2 0-3-1h1l8-5z" class="D"></path><path d="M203 232h0c1 0 1 0 2-1v1c-3 3-8 6-12 6l-1-1h1c3-2 7-4 10-5z" class="J"></path><path d="M221 221v1 3l1-1-1 6v1l-1 4h-1v1c-1 2-2 4-4 6s-4 3-7 4h0c-2 1-4 2-6 2h-2-1l1-1h1l1-1v-1c1 0 2-1 2-1l-1-1c0-1 1-1 2-2s2-2 4-3c3-2 6-7 7-10l1-3h1l1-1v-2h1l1-1z" class="C"></path><path d="M221 221v1 3c0 1-1 2 0 3 0 1 0 2-1 2h0l-1 2-1-2c1-2 1-3 2-5h0v-3l1-1z" class="j"></path><path d="M222 224l-1 6v1l-1 4h-1v1c-1 0-1-1-1-1 0-1 1-2 1-3l1-2h0c1 0 1-1 1-2-1-1 0-2 0-3l1-1z" class="l"></path><path d="M219 232l1-2h0l-1 5v1c-1 0-1-1-1-1 0-1 1-2 1-3z" class="B"></path><path d="M218 230l1 2c0 1-1 2-1 3-1 1-2 3-3 4l-1-1c-1 0-1 0-2 1v-1c2-1 3-2 4-4l1-2c0-1 1-1 1-2h0z" class="T"></path><path d="M218 235s0 1 1 1c-1 2-2 4-4 6s-4 3-7 4h0c-2 1-4 2-6 2h-2-1l1-1h1l1-1v-1c1 0 2-1 2-1l-1-1c0-1 1-1 2-2s2-2 4-3h1v1h0s1-1 2-1v1c1-1 1-1 2-1l1 1c1-1 2-3 3-4z" class="Y"></path><path d="M212 239c1-1 1-1 2-1l1 1c-2 1-3 2-5 3-2 2-6 3-8 4v-1c1 0 2-1 2-1l-1-1c0-1 1-1 2-2s2-2 4-3h1v1h0s1-1 2-1v1z" class="L"></path><path d="M210 239s1-1 2-1v1c-2 2-5 3-8 5l-1-1c0-1 1-1 2-2s2-2 4-3h1v1h0z" class="S"></path><path d="M209 238h1v1h0c-2 1-3 2-5 3v-1c1-1 2-2 4-3z" class="H"></path><path d="M195 246c1 0 3-2 5-2h0v1l2-1v1 1l-1 1h-1l-1 1h1 2c-1 0-2 1-3 1s-1 1-2 1c-1 1-1 1-2 1-1 1-2 1-3 2l-4 2h0l-2 1v1h0l-1 1h-2c-1 1-2 1-3 2h0v-1h-2s1-1 1-2l-2-1h-1l-1-1h-1c-1 2-2 2-4 3v1l-2-2h0l-1-1-1-1h0v-1l-1-1c2-3 6-4 9-5 1-1 3-2 4-2v1h-2c1 1 2 1 2 2h0l5-1 2-1c1 0 1 0 2 1l-2 1 4-1 1 1c2 0 4-2 5-3z" class="L"></path><path d="M184 254l6-3h-1c-1 1-2 1-2 2h0l1 1v1h0l-1-1h0-1c-1 1-2 1-3 1h-2l3-1z" class="m"></path><path d="M185 247c1 0 1 0 2 1l-2 1c-1 1-3 1-4 2-1 0-2 0-3 1h-2l-1-1 3-2 5-1 2-1z" class="C"></path><path d="M186 254h1 0l1 1-2 1v1h0l-1 1h-2c-1 1-2 1-3 2h0v-1h-2s1-1 1-2c0 0 1 0 2-1 1 0 1 0 2-1 1 0 2 0 3-1z" class="AC"></path><path d="M186 254h1 0l1 1-2 1v1h0l-1 1h-2l1-1v-1h1l1-1v-1z" class="E"></path><path d="M186 257h0l-1 1h-2l1-1h2z" class="W"></path><defs><linearGradient id="b" x1="177.197" y1="251.784" x2="181.079" y2="255.336" xlink:href="#B"><stop offset="0" stop-color="#636162"></stop><stop offset="1" stop-color="#79797a"></stop></linearGradient></defs><path fill="url(#b)" d="M175 253c2 1 3 0 4-1l6-1h0l-1 1v2l-3 1h2c-1 1-1 1-2 1-1 1-2 1-2 1l-2-1h-1l-1-1h-1-1-1c1-1 2-1 3-2z"></path><path d="M177 256l4-1h2c-1 1-1 1-2 1-1 1-2 1-2 1l-2-1z" class="E"></path><path d="M195 246c1 0 3-2 5-2h0v1l2-1v1 1l-1 1h-1l-1 1h1 2c-1 0-2 1-3 1s-1 1-2 1c-1 1-1 1-2 1-1 1-2 1-3 2l-4 2v-1l-1-1h0c0-1 1-1 2-2h1l2-2c-3 1-5 2-7 2 2-1 4-2 5-2 2 0 4-2 5-3z" class="B"></path><path d="M178 246v1h-2c1 1 2 1 2 2h0l-3 2 1 1h2l-3 1c-1 1-2 1-3 2h1 1c-1 2-2 2-4 3v1l-2-2h0l-1-1-1-1h0v-1l-1-1c2-3 6-4 9-5 1-1 3-2 4-2z" class="r"></path><path d="M166 255v-1c0-1 4-3 6-4v1l-3 3h0c-1 1-2 1-3 1h0z" class="i"></path><path d="M172 250l4-3c1 1 2 1 2 2h0l-3 2c-1 0-2 1-2 1l-1-1v-1z" class="F"></path><path d="M173 252s1-1 2-1l1 1h2l-3 1c-1 1-2 1-3 2h1 1c-1 2-2 2-4 3v1l-2-2h0l-1-1-1-1c1 0 2 0 3-1h0l3-3 1 1z" class="S"></path><path d="M173 252s1-1 2-1l1 1-1 1h-4v-1h2z" class="l"></path><path d="M168 257v-1c1 0 2-1 3-1h1l-2 3v1l-2-2h0z" class="j"></path><path d="M228 222c1 1 1 2 1 3 1 2 0 5 1 7l1-1c1 1 0 2 1 3v4h-1v5 1c-1 1-1 1-1 2h0-1l-1 1s0-1-1-1h-1l1 1-1 2c-1 0-1 0-2-1h0s0-1-1-1c0 0-1 1-1 2l1 1h-1c0 1-1 1-1 2v2c-1 0-1 1-2 1s-1 1-2 2h0c-1 1-2 2-2 3l-4 3v1h1v1h-1c-1 0-2 1-4 1h-1c-1 0-2 0-4 1h0c-1 0-2 0-3 1l-1-1c-1 0-2 0-3 1 0 0-1-1-2-1v1h-2-1v-1h-2-1 0l-3-1c0-1 0-1-1-1h-2l-1-1 2-1h-2c2-1 4-3 6-4h0l-1-1 1-1h0v-1l2-1h0l4-2c1-1 2-1 3-2 1 0 1 0 2-1 1 0 1-1 2-1s2-1 3-1c2 0 4-1 6-2h0c3-1 5-2 7-4s3-4 4-6v-1h1l1-4v-1h1 1l1 1v-3c1 0 1 0 2-1h0c1 0 1 0 2-1v-4z" class="S"></path><path d="M211 263v1h1v1h-1c-1 0-2 1-4 1 1-1 2-2 4-3z" class="b"></path><path d="M198 262l3-1 1 1c0 1-1 1-2 2h0c-2-1-3-1-4-1l2-1z" class="B"></path><path d="M182 263h0c1 0 1 1 2 1s1 0 1 1c2-1 3-1 4-1s1 1 2 1v1h-2l-1-1h-3c-1 0-1 0-1-1h-2v-1z" class="E"></path><path d="M207 263l-6 3-1-1 5-4v2h1 1z" class="B"></path><path d="M195 264l1-1c1 0 2 0 4 1h0c-1 1-2 2-3 2h-1-2l-1 1v-2c1 0 1 0 2-1z" class="U"></path><path d="M221 248c1 0 1 0 1 1l1 1h-1c0 1-1 1-1 2v2c-1 0-1 1-2 1s-1 1-2 2h0c-1 0-2 0-3 1h0c-2 3-5 4-7 5h-1-1v-2-1h0l2-1 2-1c3-2 6-5 8-7 2-1 3-2 4-3z" class="g"></path><path d="M207 259l2-1v1c-1 1-1 2-3 1h-1 0l2-1zm-17-1l1 1c1 0 2-1 3-1h1 3c-1 1-2 1-3 3 1 1 2-1 3 1l-2 1-1 1c-1 1-1 1-2 1h-2c-1 0-1-1-2-1s-2 0-4 1c0-1 0-1-1-1s-1-1-2-1h0 0-2c2-1 4-3 6-4h1 2l1-1z" class="B"></path><path d="M186 259h1c1 1 1 1 3 1l1-1v1 1c0-1-1-1-2-1-2 0-4 1-7 3h0 0-2c2-1 4-3 6-4z" class="l"></path><path d="M189 260c1 0 2 0 2 1l2-1v1c0 1 1 1 1 2l1 1c-1 1-1 1-2 1h-2c-1 0-1-1-2-1l3-2v-1h-2l-1-1z" class="W"></path><path d="M190 258l1 1c1 0 2-1 3-1h1 3c-1 1-2 1-3 3 1 1 2-1 3 1l-2 1-1 1-1-1c0-1-1-1-1-2v-1l-2 1v-1-1l-1 1c-2 0-2 0-3-1h2l1-1z" class="G"></path><path d="M190 258l1 1c1 0 2-1 3-1h1l-2 2h0 1v1h-1v-1l-2 1v-1-1l-1 1c-2 0-2 0-3-1h2l1-1z" class="L"></path><path d="M217 244l2-3h2l-1 2h1l-4 8c-2 2-5 5-8 7l-2 1-2 1-1-1-2 1-1 1-3 1c-1-2-2 0-3-1 1-2 2-2 3-3s3-2 5-2l1-1v-2h1l3-1v-1l1-1c1 0 3-1 5-1l1-2 1-1 1-2z" class="P"></path><path d="M204 253h1v1h1 1l-3 1v-2z" class="C"></path><path d="M205 253l3-1 1 1-2 1h-1-1v-1z" class="W"></path><path d="M208 251l1-1c1 0 3-1 5-1-1 1-2 2-4 3h0l-1 1-1-1v-1z" class="S"></path><path d="M202 260l-2-1v-1c1 0 2-1 3-1s1 0 2-1h0l1 1 1-1h0l-1 1c1 1 1 1 1 2l-2 1-1-1-2 1z" class="f"></path><path d="M206 257c1 1 1 1 1 2l-2 1-1-1 2-2z" class="t"></path><path d="M217 244l2-3h2l-1 2c-2 4-4 6-7 10-1 1-2 3-4 3h0-1c3-2 6-4 8-8l-1-1 1-1 1-2z" class="C"></path><path d="M215 242l1 1v1c-1 0-1 1 0 1v1l-1 1-1 2c-2 0-4 1-5 1l-1 1v1l-3 1h-1v2l-1 1c-2 0-4 1-5 2h-3-1c-1 0-2 1-3 1l-1-1-1 1h-2-1 0l-1-1 1-1h0v-1l2-1h0l4-2c1-1 2-1 3-2 1 0 1 0 2-1 1 0 1-1 2-1s2-1 3-1c2 0 4-1 6-2h0c3-1 5-2 7-4z" class="J"></path><path d="M190 256l2-1 1 1v1l-3 1v-2zm12-3h2v2l-1 1c-1 0-2 0-3-1l2-2z" class="H"></path><path d="M200 255c-2 1-4 1-6 2v-1l1-1c2-2 5-3 7-3v1l-2 2z" class="I"></path><path d="M188 255l4-2c1-1 2-1 3-2l1 2c-1 1-3 1-4 2l-2 1v2h0l-1 1h-2-1 0l-1-1 1-1h0v-1l2-1h0z" class="AC"></path><path d="M189 259l-2-1h0c1-1 2-1 3-2v2h0l-1 1z" class="C"></path><path d="M208 246l1 1c-1 0-2 1-3 1v1h3c-1 1-3 1-4 2v1h-2-2l1-2c-2 1-5 2-6 3l-1-2c1 0 1 0 2-1 1 0 1-1 2-1s2-1 3-1c2 0 4-1 6-2z" class="c"></path><path d="M202 250h2 1c-1 1 0 1-1 1s-1 0-1 1h-2l1-2z" class="Z"></path><path d="M215 242l1 1v1c-1 0-1 1 0 1v1l-1 1-1 2c-2 0-4 1-5 1l-1 1v1l-3 1v-1-1c1-1 3-1 4-2h-3v-1c1 0 2-1 3-1l-1-1h0c3-1 5-2 7-4z" class="R"></path><path d="M208 251v1l-3 1v-1-1h3z" class="h"></path><path d="M228 222c1 1 1 2 1 3 1 2 0 5 1 7l1-1c1 1 0 2 1 3v4h-1v5 1c-1 1-1 1-1 2h0-1l-1 1s0-1-1-1h-1l1 1-1 2c-1 0-1 0-2-1h0s0-1-1-1c0 0-1 1-1 2 0-1 0-1-1-1-1 1-2 2-4 3l4-8h-1l1-2h-2l-2 3-1 2v-1c-1 0-1-1 0-1v-1l-1-1c2-2 3-4 4-6v-1h1l1-4v-1h1 1l1 1v-3c1 0 1 0 2-1h0c1 0 1 0 2-1v-4z" class="k"></path><path d="M221 230h1v3h0l-1-2v-1z" class="F"></path><path d="M224 243v-3h1c0 2 1 5 0 7h-1v-1-3z" class="D"></path><path d="M223 235l1 1v3h-1v2l-1 1-1 1h-1l1-2v-2l2-4z" class="H"></path><path d="M221 239c1 1 1 1 1 3l-1 1h-1l1-2v-2z" class="J"></path><path d="M223 241l1-1h0v3 3h-1v1s-1 1-1 2c0-1 0-1-1-1-1 1-2 2-4 3l4-8 1-1 1-1z" class="R"></path><path d="M221 248c1-1 1-3 2-3v1 1s-1 1-1 2c0-1 0-1-1-1zm0-17l1 2h0l1 2-2 4v2h-2l-2 3-1 2v-1c-1 0-1-1 0-1v-1l-1-1c2-2 3-4 4-6v-1h1l1-4z" class="B"></path><path d="M219 236v-1h1c-1 3-3 6-4 8l1 1-1 2v-1c-1 0-1-1 0-1v-1l-1-1c2-2 3-4 4-6z" class="i"></path><path d="M228 222c1 1 1 2 1 3 1 2 0 5 1 7l1-1c1 1 0 2 1 3v4h-1v5 1c-1 1-1 1-1 2h0-1l-1 1s0-1-1-1h-1c0-2 1-5-1-6v-1c0-1 1-3 1-4v-1h-1-1c-1-1-1-1 0-3v-3c1 0 1 0 2-1h0c1 0 1 0 2-1v-4z" class="L"></path><path d="M228 222c1 1 1 2 1 3 1 2 0 5 1 7v3h-1c0-2 0-5-1-8l-1 1h-1v-1h0c1 0 1 0 2-1v-4z" class="F"></path><path d="M230 232l1-1c1 1 0 2 1 3v4h-1v5-8h-1v7c-1-2-1-4-1-7h1v-3z" class="C"></path><path d="M227 246v-6-6h1v9h0c1 0 2 0 3 1-1 1-1 1-1 2h0-1l-1 1s0-1-1-1z" class="c"></path><path d="M228 243c1 0 2 0 3 1-1 1-1 1-1 2h0-1c0-1-1-2-1-3z" class="C"></path><path d="M226 227v1h1v2l-1 5v-1h-1-1c-1-1-1-1 0-3v-3c1 0 1 0 2-1z" class="E"></path><path d="M225 234v-1c0-2 0-3 1-5l1 2-1 5v-1h-1z" class="l"></path><path d="M124 169h3 4 0 1v1h2v2c1 0 1 0 1-1h1l1 2c1 0 1 1 2 1 0 1 0 1-1 1l-1 1h1 1l1 1c2 0 4 0 6-1h1 0c1 0 2 0 3 1h1c2 1 2 2 3 2 1 1 3 2 4 2h1 0c1 0 1 1 2 1 0 1 1 2 2 2h0c1 1 2 2 4 2 0 1-1 1-1 2h0c1 0 2 1 2 1h3l-2 1c2 1 4 3 6 4l2 1c1 0 2 1 3 1l1 1c1 1 3 1 4 1 2 1 4 1 6 1h2 0c2 1 5 0 6 1v2l1-1 1-1v-1c1 2 1 2 3 2h0l-4 2v1 1 1l1 1h-1l-4 1c0 1 1 1 0 2v1 1l-1 1h-1c-2 0-2 0-4-1l-1 1h-3c0 1-1 1-2 1v1l2 1 2-1 1 1h2 1 2v2c-1 1-1 1-2 1v1h1l-1 2h-1-2-1-2-2v2h-8 5c0 1 0 1-1 2l-1 1h1v1c0 1-2 0-4 0l-2-1h-1c1 1 1 1 1 2h0v1l-2 1h1v1c-1 0-3 0-3 1l1 1c-1 0-2 1-3 1l-2 2h-2-4l2 1 1 1-2 2h-1c-1 1-3 1-5 1h-1l-4-4h-1-1l-1-1s-4-3-5-3c-1-1-2-2-4-3 0 0-1-1-2-1l-8-5-6-3h0c-1-1-1-1-2-1h0c-1 0-1 0-2-1 1-1 1-1 1-2h2l-2-2h3c-1-1-1-1-1-2h0c1 0 1-1 2-1-2-1-3-1-4-1l-1-1h0c-1 0-1 1-1 1-1-1-2-1-3-1h0-1l-2-2 1-1h1v-2h1v-1l1-1c0-1 0-1 1-2h0v-2c0-1 0-2-1-3h1c0-2 0-4 2-5h0c-2 0-3-1-4-1v-1c0-1 1-2 2-2 0-1-1 0-1-1h-1l1-2c1-1 1-3 2-4-1-2 0-3 0-5l1-2c1-1 1-2 2-2h1c1-1 1-2 2-3h1z" class="AA"></path><path d="M132 207h1l1 1c-1 0-1 0-2-1z" class="g"></path><path d="M134 216c0-1-1-1-1-2h2v1l-1 1z" class="Y"></path><path d="M135 214h1s1 0 1-1l1 1v1h-3v-1z" class="w"></path><path d="M144 183h1v1l-1 1h-1v-1l1-1z" class="v"></path><path d="M143 189h1c0 1 0 1-1 2h-1v-1l1-1z" class="B"></path><path d="M139 215h1l1 1-1 1h-1l-1-1 1-1z" class="U"></path><path d="M146 183h2v1h0v1l-2-2h0z" class="v"></path><path d="M128 216h1c1-1 1-1 2-1 0 0 0 1 1 1v1h0c-1 0-3 0-4-1z" class="U"></path><path d="M134 216l1-1c1 1 1 2 2 3l-5-1h0 2v-1z" class="x"></path><path d="M166 188c1 0 2 1 2 1h3l-2 1h0c-1 0-2 0-3-1v-1z" class="v"></path><path d="M151 188l1-1 1 1c0 1-1 1-2 1l-1 1h2v1h-2s-1-1-2-1c1-1 2-2 3-2z" class="w"></path><path d="M143 195h1c1 1 2 1 3 2l-1 1h-1c-1-1-2-1-3-2 0 0 1 0 1-1z" class="B"></path><path d="M146 191h1v1l1 1h-2l1 1h0c-1 0-2-1-3-1l-1 1h-1l-1 1-1-1h-1v-1c2 0 5-1 7-2h0z" class="U"></path><path d="M158 181h1 0c1 0 1 1 2 1 0 1 1 2 2 2h0c1 1 2 2 4 2 0 1-1 1-1 2-2-1-4-2-5-3-1-2-2-2-3-4z" class="j"></path><path d="M181 217h2 2l1 1c-1 0-2 1-3 1l-1 1-5 1h0v-1c0-1 0-1-1-2h0 3c1 0 1 0 2-1h0z" class="Y"></path><path d="M181 217h2l-1 2c-1 0-2 0-3-1 1 0 1 0 2-1h0z" class="v"></path><path d="M129 207s1 0 2 1h0l1 1c3 0 5 0 7 2v1h0c-2-1-3-1-4-2h-3c-1 0-1-1-1-1h-4-3l-1-1c2 0 4 0 6-1z" class="w"></path><path d="M127 216h1c1 1 3 1 4 1l5 1h1-3v1h0-1c-3-1-6 0-9-1v-2h2 0z" class="T"></path><path d="M125 216h2v1c0 1-1 1-2 1v-2z" class="H"></path><path d="M136 197h4 0c1 0 2 0 3 1v1 1h-3c1 1 1 1 2 3v1h-1c0-1 0-2-1-2v-1h-1l-1-1h0c2-1 3-1 4-1h1v-1c-2-1-4-1-6 0l-1 1h0c0 1-1 1-1 2l-1-1c-1-1-1 0-2-1h2 0l2-2h0z" class="x"></path><path d="M159 196l2-2h1c2 1 1 2 2 3s4 2 5 2 1 1 2 1v1h-4c-1-1-2-2-2-3-2-1-3-1-5-1 0-1-1-1-1-1z" class="U"></path><path d="M153 197h2v-1c-1-2-3-1-4-2v-1h2c1 1 3 1 4 1 0 1 0 2 1 2h1s1 0 1 1l-7 2h0v-2z" class="Y"></path><path d="M175 194l2 1c1 0 2 1 3 1l1 1c-2 1-4 0-6 0-3-1-6-1-9-3h8 1z" class="v"></path><path d="M153 221h2v-1h2c3 1 6 2 9 2l3 1-5 1-7-2h-1c-1 0-2 0-3-1z" class="O"></path><path d="M191 199h2 0c2 1 5 0 6 1v2c-2 1-5 3-8 3l-1-2c2 0 4-1 5-2v-1l-4-1z" class="B"></path><path d="M145 207l-1-1v-2h-1l1-1h1l1-1c-1 0-1-1-2-1l-1 2h0v-2h1 2 1l1-1v-1h0v-1l1-1h1l1 2h0l1-1v-1h1v2c-1 0-2 1-4 2-1 1-2 2-4 3 1 0 1 0 2 1h-1c-1 1-1 1-1 2z" class="U"></path><path d="M118 211c1 0 3-1 4 0v1c1-1 2-1 3 0v1l1 1c1 1 1 1 1 2h0-2-3c-1-1-1-1-1-2h0c1 0 1-1 2-1-2-1-3-1-4-1l-1-1z" class="w"></path><path d="M121 214h0c1 1 1 1 2 1h1c0-1 1-1 2-1 1 1 1 1 1 2h0-2-3c-1-1-1-1-1-2z" class="Y"></path><path d="M177 221h0v1h6 3 0-2v2h-8c-1-1-2-1-3-1h-4l-3-1c3 0 9 0 11-1z" class="H"></path><path d="M162 213c1-1 2-1 2 0h1 0v1l1 1c0 1-2 1-3 1s-2-1-3-1c-2 0-5-1-7-2 2-1 4 0 6 0h3z" class="G"></path><path d="M160 215l-1-1c1-1 5 0 6 0l1 1c0 1-2 1-3 1s-2-1-3-1z" class="V"></path><path d="M135 201c0-1 1-1 1-2h0l1-1c2-1 4-1 6 0v1h-1c-1 0-2 0-4 1h0l1 1h1v1l-1 1h1v1h1-1c-1 0-1 0-2 1l-2-1-1-1c1-1 1-1 1-2h-1z" class="Y"></path><path d="M150 211v-1c3 0 5 0 7-1l2 2c1 1 2 1 3 2h-3c-2 0-4-1-6 0h-2c-1-1-1 0-1 0-1 0-2 0-2-1l1-1h1z" class="J"></path><path d="M149 211h1c2 0 8 0 9 2-2 0-4-1-6 0h-2c-1-1-1 0-1 0-1 0-2 0-2-1l1-1z" class="Y"></path><path d="M181 213h5 0c0 1-1 1-2 1v1l2 1c1 0 1 1 1 1l-1 1-1-1h-2-2 0c-3 0-5-2-8-2-1 0-1-1-2-1h8c1 0 1 0 2-1z" class="T"></path><path d="M181 213h5 0c0 1-1 1-2 1v1l-5-1c1 0 1 0 2-1z" class="b"></path><path d="M184 215l2 1c1 0 1 1 1 1l-1 1-1-1h-2-2v-1c1 0 3 0 3-1h0zm-24-18c2 0 3 0 5 1 0 1 1 2 2 3-1 0-3-1-4 0h-1l-6 1-1-1h-1-1v-2l7-2z" class="J"></path><path d="M162 201l-1-1h-5v-1c3-2 5-1 9-1 0 1 1 2 2 3-1 0-3-1-4 0h-1z" class="b"></path><path d="M146 205h2 1c0 1 0 1 1 1 2 0 3 0 5 1h2l-1 1h5 1l-1 1h-4c-2 1-4 1-7 1v1h-1c-1 0-2 0-3-1v-2c0-1-1-1-1-1 0-1 0-1 1-2z" class="J"></path><path d="M145 207c0-1 0-1 1-2v1c1 1 1 2 2 2 2 1 4 1 6 1h2l1-1v1c-2 1-4 1-7 1v1h-1c-1 0-2 0-3-1v-2c0-1-1-1-1-1z" class="k"></path><path d="M191 216h1 2v2c-1 1-1 1-2 1v1h1l-1 2h-1-2-1-2 0-3-6v-1l5-1 1-1c1 0 2-1 3-1l1-1s0-1-1-1l2-1 1 1h2z" class="d"></path><path d="M189 222c1-2 1-2 3-2h1l-1 2h-1-2z" class="T"></path><path d="M183 219h5v1l-2 2h0-3l3-1v-1h-4l1-1z" class="K"></path><path d="M191 216h1 2v2c-1 1-1 1-2 1s-1-1-2-1v-2h1z" class="C"></path><path d="M177 221l5-1h4v1l-3 1h-6v-1z" class="I"></path><path d="M124 196v-1-1h1l3 1h4c-1 0-1 0-1 1h0 1c1 0 1 0 1-1h1l-1 1h3v1h0l-2 2h0-2c1 1 1 0 2 1l1 1h1c0 1 0 1-1 2l1 1h-1l-1-2c-2-1 0 0-2 1h-3l-1 1-1-1v-2l2-1h-1 0-1 0c-1 0-1-1-1-2l-1-1v-1h-1z" class="g"></path><path d="M129 200h2c0 1-1 2-2 3l-1 1-1-1v-2l2-1z" class="B"></path><path d="M124 196v-1-1h1l3 1h4c-1 0-1 0-1 1-1 1-2 1-4 1h0l-1 1-1-1v-1h-1z" class="F"></path><path d="M164 207h3c0 1 1 0 1 1v1h6l-1 1 4 1h-3v1l7 1h0c-1 1-1 1-2 1h-8c-2 0-4 0-6-1h0-1c0-1-1-1-2 0-1-1-2-1-3-2l-2-2h4l1-1h1l1-1z" class="Q"></path><path d="M167 207c0 1 1 0 1 1v1h6l-1 1h-8c0-1 1-2 2-3z" class="r"></path><path d="M164 207h3c-1 1-2 2-2 3h-3 0 1c1 0 1 0 2 1-2 0-3 0-4-1v-1h3l-1-1 1-1z" class="P"></path><path d="M162 208h1l1 1h-3v1c1 1 2 1 4 1l3 1h-2l1 1h3 0l1 1c-2 0-4 0-6-1h0-1c0-1-1-1-2 0-1-1-2-1-3-2l-2-2h4l1-1z" class="b"></path><path d="M168 212h6l7 1h0c-1 1-1 1-2 1h-8l-1-1h0-3l-1-1h2z" class="V"></path><defs><linearGradient id="c" x1="154.048" y1="206.131" x2="159.694" y2="199.916" xlink:href="#B"><stop offset="0" stop-color="#959393"></stop><stop offset="1" stop-color="#b9b7b9"></stop></linearGradient></defs><path fill="url(#c)" d="M153 199h0v2h1 1l1 1 6-1h1v2 1l-1 1h-2v1c1 0 2 0 4 1l-1 1h-1-1-5l1-1h-2c-2-1-3-1-5-1-1 0-1 0-1-1h-1-2 1c-1-1-1-1-2-1 2-1 3-2 4-3 2-1 3-2 4-2z"></path><path d="M154 203h1v1 1h-3v-1c1 0 1 0 2-1z" class="J"></path><path d="M153 201h1 1-2v1s0 1 1 1c-1 1-1 1-2 1h-2l2-2 1-1z" class="d"></path><path d="M159 205h-1c-1 0-1 0-1-1l1-1c2 0 3 0 5 1l-1 1h-2-1z" class="O"></path><path d="M159 205h1v1c1 0 2 0 4 1l-1 1h-1-1-1-2v-1l1-2z" class="K"></path><path d="M153 199h0v2l-1 1-2 2h2v1h-5c-1-1-1-1-2-1 2-1 3-2 4-3 2-1 3-2 4-2z" class="H"></path><path d="M150 204h-2v-1c1 0 2-1 4-1l-2 2z" class="J"></path><path d="M167 201h4 0c1 0 2 1 4 1l1 1c1 0 1 1 2 2h2 1c-1 0-3 0-3 1h2l1 1v2h1 2c-1 1 0 1-1 1-2 1-4 1-6 1l-4-1 1-1h-6v-1c0-1-1 0-1-1h-3c-2-1-3-1-4-1v-1h2l1-1v-1-2c1-1 3 0 4 0z" class="I"></path><path d="M163 204h3v1h-4l1-1z" class="P"></path><path d="M166 204l1-1h1v1s0 1 1 1c-1 1-2 1-3 0v-1z" class="r"></path><path d="M171 201c1 0 2 1 4 1l1 1c1 0 1 1 2 2h2 1c-1 0-3 0-3 1-1 0-2 0-3-1h0-5c0-1-1-2-2-3l1-1h2z" class="p"></path><path d="M180 205h1c-1 0-3 0-3 1-1 0-2 0-3-1h5z" class="a"></path><path d="M175 202l1 1c1 0 1 1 2 2-2 0-4-1-6-1v-1c1 0 2 0 2-1h1z" class="I"></path><path d="M168 208l1-1c-1 0-1 0-1-1h5c2 1 5 1 6 1l1-1 1 1v2h1 2c-1 1 0 1-1 1-2 1-4 1-6 1l-4-1 1-1h-6v-1z" class="M"></path><path d="M174 209c1-1 5 0 7 0h1 2c-1 1 0 1-1 1-2 1-4 1-6 1l-4-1 1-1z" class="K"></path><path d="M201 200v-1c1 2 1 2 3 2h0l-4 2v1 1 1l1 1h-1l-4 1c0 1 1 1 0 2v1 1l-1 1h-1c-2 0-2 0-4-1l-1 1h-3 0-5 0l-7-1v-1h3c2 0 4 0 6-1 1 0 0 0 1-1h-2-1v-2l-1-1h-2c0-1 2-1 3-1h-1-2c-1-1-1-2-2-2 2 0 4 0 5 1 3 0 5-1 7-1 1 1 2 1 2 0l1 2c3 0 6-2 8-3l1-1 1-1z" class="d"></path><path d="M189 205c1 1 2 1 3 1 1 1 3 1 4 1-2 1-3 2-5 2v-1c0-1-1-2-2-3z" class="Q"></path><path d="M181 205c2 0 3 0 4 1 1 0 3 0 4-1 1 1 2 2 2 3v1h0-1v-1h0c-2-1-3-2-5-2-1 0-2 0-4-1z" class="M"></path><path d="M181 204c3 0 5-1 7-1 1 1 2 1 2 0l1 2c-4 0-6 0-10-1z" class="x"></path><g class="X"><path d="M181 205h0c2 1 3 1 4 1 2 0 3 1 5 2h0v1h-1-8v-2l-1-1h-2c0-1 2-1 3-1z"></path><path d="M190 209h1 0 0c1 0 2 1 3 1h1c-2 2-3 2-5 2l-1 1h-3 0-5 0l-7-1v-1h3c2 0 4 0 6-1 1 0 0 0 1-1h-2-1 8 1z"></path></g><path d="M190 209h1 0 0c1 0 2 1 3 1h1c-2 2-3 2-5 2l-1 1h-3 0-5 0c1 0 3 0 5-1 2 0 4-1 5-2h-8c1 0 0 0 1-1h-2-1 8 1z" class="f"></path><path d="M118 192l2 1h4c0 1 1 1 1 1h-1v1 1h1v1l1 1c0 1 0 2 1 2h0 1 0 1l-2 1v2l1 1v1h0 1l-1 1 1 1c-2 1-4 1-6 1h0-1c-1 1-4 1-6 1v1c1 0 1 1 2 1-1 0-1 1-1 1-1-1-2-1-3-1h0-1l-2-2 1-1h1v-2h1v-1l1-1c0-1 0-1 1-2h0v-2c0-1 0-2-1-3h1c0-2 0-4 2-5z" class="g"></path><path d="M122 203l2 1c-1 1-1 1-2 1h-2l2-2zm-8 3h1l1 1v1l-1 1s0 1 1 1 1 1 2 1c-1 0-1 1-1 1-1-1-2-1-3-1h0-1l-2-2 1-1h1v-2h1z" class="B"></path><path d="M114 206h1l1 1h-2l-1-1h1z" class="x"></path><path d="M116 202l1 1h1c1-1 1-2 2-3s1-2 2-3c1 0 1 0 1 1h1l1-1 1 1c0 1 0 2 1 2h0 1 0 1l-2 1v2l1 1v1h0 1l-1 1-2 1h-1l2-2c-1 0-1-1-2-1h-1l-2-1c-1-1-1-1-2-1h-1v2c0 1-1 1-2 1s-1 0-2 1h-1v-1l1-1c0-1 0-1 1-2z" class="w"></path><path d="M123 199h2v1h-2v-1z" class="F"></path><path d="M115 204c1-1 2-1 4-1v1l-2 1c-1 0-1 0-2 1h-1v-1l1-1z" class="Y"></path><path d="M125 203h-2l-1-1 2-1h2 0 1v2h-2z" class="B"></path><path d="M127 201v2h-2c0-1 0-1 1-1l1-1z" class="i"></path><path d="M118 192l2 1h4c0 1 1 1 1 1h-1v1 1h1v1l-1 1h-1c0-1 0-1-1-1-1 1-1 2-2 3s-1 2-2 3h-1l-1-1h0v-2c0-1 0-2-1-3h1c0-2 0-4 2-5z" class="g"></path><path d="M117 198h2 0l-1 2c-1-1-1-1-1-2z" class="Y"></path><path d="M116 200c0-1 0-2-1-3h1v1h1c0 1 0 1 1 2l-2 2v-2z" class="x"></path><path d="M120 193h4c0 1 1 1 1 1h-1v1 1h-4l-1-1c0-1 0-2 1-2z" class="Y"></path><path d="M124 169h3 4 0 1v1h2v2c1 0 1 0 1-1h1l1 2c1 0 1 1 2 1 0 1 0 1-1 1l-1 1h1 1l1 1c2 0 4 0 6-1h1c-1 2-3 4-5 6l-5 4v2c0 1-1 1-1 1-2 1-3 2-4 3-1 0-1 0-2 1h-1v-1l-1 1v2l-3-1s-1 0-1-1h-4l-2-1h0c-2 0-3-1-4-1v-1c0-1 1-2 2-2 0-1-1 0-1-1h-1l1-2c1-1 1-3 2-4-1-2 0-3 0-5l1-2c1-1 1-2 2-2h1c1-1 1-2 2-3h1z" class="g"></path><path d="M136 175l1 1h1c-1 1-1 1-1 2l-1-1v-2z" class="v"></path><path d="M129 172c2 1 5 2 7 3h0v2c-3-1-5-2-8-2l1-1c1 0 2 0 3 1 0-1 0-1-1-1s-1-1-2-1v-1z" class="B"></path><path d="M146 176h1c-1 2-3 4-5 6 0-1-1-2-1-2 1 0 1 0 1-1h1 1v-1l-4-1c2 0 4 0 6-1z" class="i"></path><path d="M118 174c1-1 1-2 2-2v1l1 1c-2 2-3 4-4 7-1-2 0-3 0-5l1-2z" class="P"></path><path d="M141 180s1 1 1 2l-5 4h0l-2-1c2-2 4-5 6-5z" class="V"></path><path d="M116 188c1 0 1 1 2 1 1 1 4 1 4 2 2 0 4-1 6-1 1 0 2 0 2 1-2 1-4 2-6 2h-4l-2-1h0c-2 0-3-1-4-1v-1c0-1 1-2 2-2z" class="X"></path><path d="M122 191c2 0 4-1 6-1 1 0 2 0 2 1-2 1-4 2-6 2 1-1 1-1 2-1 0-1 1-1 1-1h1c-2 0-4 1-5 0h-1z" class="p"></path><path d="M132 188l3-3 2 1h0v2c0 1-1 1-1 1-2 1-3 2-4 3-1 0-1 0-2 1h-1v-1l-1 1v2l-3-1s-1 0-1-1c2 0 4-1 6-2 0-1-1-1-2-1h0l4-2z" class="U"></path><path d="M128 190h0l4-2 2 1c-2 0-3 1-4 2 0-1-1-1-2-1z" class="f"></path><path d="M132 188l3-3 2 1-3 3-2-1z" class="P"></path><path d="M128 175v5c0 3-2 6-3 8 1 1 1 1 2 1-1 0-2 1-3 1-2 0-3-1-4-2s0-3 0-4c0-3 2-7 4-9l-2 5c0 2 0 6 1 8h1c2-1 3-10 4-13zm-4-6h3 4 0 1v1h2v2c1 0 1 0 1-1h1l1 2c1 0 1 1 2 1 0 1 0 1-1 1l-1 1-1-1h0c-2-1-5-2-7-3h0-5l-3 2-1-1v-1h1c1-1 1-2 2-3h1z" class="o"></path><path d="M137 173c1 0 1 1 2 1 0 1 0 1-1 1l-1 1-1-1h0 1v-1-1z" class="e"></path><path d="M123 169h1l-1 2v1h1l-3 2-1-1v-1h1c1-1 1-2 2-3z" class="M"></path><path d="M128 175c3 0 5 1 8 2l1 1c0 2-1 4-2 5-2 3-5 5-8 6-1 0-1 0-2-1 1-2 3-5 3-8v-5h0z" class="q"></path><path d="M119 216h3 3v2c3 1 6 0 9 1h1 0v-1h3l6 1 5 1 4 1c1 1 2 1 3 1h1l7 2 5-1h4c1 0 2 0 3 1h5c0 1 0 1-1 2l-1 1h1v1c0 1-2 0-4 0l-2-1h-1c1 1 1 1 1 2h0v1l-2 1h1v1c-1 0-3 0-3 1l1 1c-1 0-2 1-3 1l-2 2h-2-4l2 1 1 1-2 2h-1c-1 1-3 1-5 1h-1l-4-4h-1-1l-1-1s-4-3-5-3c-1-1-2-2-4-3 0 0-1-1-2-1l-8-5-6-3h0c-1-1-1-1-2-1h0c-1 0-1 0-2-1 1-1 1-1 1-2h2l-2-2z" class="Q"></path><path d="M155 227l-4-2 6 1h3 1c4 2 7 2 11 2l1-1h1-1c1 1 1 1 1 2h0v1l-2 1-2-1h-2c-1 0-1-1-1-1h-1-1s-1 0-1-1h0c-2 0-3 0-4-1h-5z" class="P"></path><path d="M170 230c-1 0-2-1-3-1h1c0-1 1-1 2 0v1z" class="K"></path><path d="M170 229h4 0v1l-2 1-2-1h0v-1z" class="G"></path><path d="M157 222l7 2 5 1-1 1c1 0 1 1 2 1s2-1 3 0l-1 1c-4 0-7 0-11-2h-1-3l1-1h2c-1-1-2-1-3-2v-1z" class="f"></path><path d="M157 222l7 2 5 1-1 1-8-1c-1-1-2-1-3-2v-1z" class="AD"></path><path d="M169 223h4c1 0 2 0 3 1h5c0 1 0 1-1 2l-1 1h1v1c0 1-2 0-4 0l-2-1h-1c-1-1-2 0-3 0s-1-1-2-1l1-1-5-1 5-1z" class="M"></path><path d="M169 223h4l-2 1v1h-2l-5-1 5-1z" class="V"></path><path d="M161 232c2-1 4-1 6 0h0 1c0 1 0 1-1 2h-1c-1 1-1 1-1 2-2 0-6 0-8-1h-1-1c-2 0-3 0-5-1 2-1 4 0 5-1h0c-1 0-2-1-3-1 1 0 3 1 4 0 2 0 4-1 5 0z" class="e"></path><path d="M156 235c1-1 2-1 3-1l1 1h0-3-1z" class="o"></path><path d="M161 232c2-1 4-1 6 0h0 0v1c-1 1-2 1-3 1s-3 0-4-1l1-1z" class="N"></path><path d="M138 231c0-2-2-2-3-3 2 0 4 1 6 2h1 1c3 0 6 2 9 2 1 0 2 1 3 1h0c-1 1-3 0-5 1 2 1 3 1 5 1 1 1 1 2 3 2h2l2 1 1 1-2 2h-1c-1 1-3 1-5 1h-1l-4-4h-1-1l-1-1s-4-3-5-3c-1-1-2-2-4-3z" class="p"></path><path d="M142 234l1-1h2 1c1 0 2 0 3 1h1c2 1 3 1 5 1 1 1 1 2 3 2l-6-1-2 2h-1-1l-1-1s-4-3-5-3z" class="K"></path><path d="M146 235c2 0 4 1 6 1l-2 2h-1c0-1-2-2-3-3z" class="B"></path><path d="M142 234l1-1h2 1c1 0 2 0 3 1-1 0-1 1-2 1l-1-1h-1v1h1c1 1 3 2 3 3h-1l-1-1s-4-3-5-3z" class="f"></path><path d="M152 236l6 1h2l2 1 1 1-2 2h-1c-1 1-3 1-5 1h-1l-4-4 2-2z" class="w"></path><path d="M149 220l4 1c1 1 2 1 3 1h1v1c1 1 2 1 3 2h-2l-1 1-6-1 4 2h-2c1 1 2 1 3 1s2 1 3 1h2 1s1 0 1 1h2v1c-2 0-3-1-4 1-1-1-3 0-5 0-1 1-3 0-4 0-3 0-6-2-9-2 0-1-1-1-2-2h1c1-1 2-1 2-1l-1-2h0l-2-1v-1h3c0-1 1-2 1-2h3l1-1z" class="o"></path><path d="M149 220l4 1c1 1 2 1 3 1h1v1c1 1 2 1 3 2h-2c-2-1-6-2-8-3v-1l-3 1v1l1 1h0-4l-1 1h0l-2-1v-1h3c0-1 1-2 1-2h3l1-1zm7 12l-2-1c-2-1-4-2-6-4 1 1 4 2 5 2l-2-2h1 1c1 1 2 1 3 1s2 1 3 1h2 1s1 0 1 1h2v1c-2 0-3-1-4 1-1-1-3 0-5 0z" class="P"></path><path d="M119 216h3 3v2c3 1 6 0 9 1h1 0v-1h3l6 1 5 1-1 1h-3s-1 1-1 2h-3v1l2 1h0l1 2s-1 0-2 1h-1c1 1 2 1 2 2h-1-1c-2-1-4-2-6-2 1 1 3 1 3 3 0 0-1-1-2-1l-8-5-6-3h0c-1-1-1-1-2-1h0c-1 0-1 0-2-1 1-1 1-1 1-2h2l-2-2z" class="Y"></path><path d="M143 225h0l1 2s-1 0-2 1c-1 0-1-1-2-1h-1v1h0-1v-1c0-1-1-1-2-2h-1 0c1 0 2 0 3 1h2v-1c0-1 2 0 3 0z" class="G"></path><path d="M129 224c3 1 6 3 9 4h1 0v-1h1c1 0 1 1 2 1h-1c1 1 2 1 2 2h-1-1c-2-1-4-2-6-2 1 1 3 1 3 3 0 0-1-1-2-1l-8-5 1-1z" class="Q"></path><path d="M135 219h0v-1h3l6 1 5 1-1 1h-3s-1 1-1 2h-3v1l2 1c-1 0-3-1-3 0v1h-2l1-1c-1 0-2-1-2-2h0c1 0 2 1 3 1h0v-1l-1-1c-1-1-1-2-2-3h-2z" class="H"></path><path d="M135 219h0v-1h3l6 1h-4v1c1 0 1 1 2 2h-2v1l-1-1c-1-1-1-2-2-3h-2z" class="C"></path><path d="M137 219c1 1 3 1 3 2l-1 1c-1-1-1-2-2-3z" class="i"></path><path d="M119 216h3 3v2c3 1 6 0 9 1v1c1 1 2 1 3 1v1h0-2v1h1l-1 1c-1 0-2-1-3-1h0c-1-1-2-1-3-1h-1 0-1l2 2h0l-1 1-6-3h0c-1-1-1-1-2-1h0c-1 0-1 0-2-1 1-1 1-1 1-2h2l-2-2z" class="g"></path><path d="M119 216h3 3v2h-4 0l-2-2z" class="Z"></path><path d="M121 218h0 0c1 2 4 3 6 4l2 2h0l-1 1-6-3h0c-1-1-1-1-2-1h0c-1 0-1 0-2-1 1-1 1-1 1-2h2z" class="c"></path><path d="M221 182l2 2c0 1 0 2 1 2l2 3v-1l1-1v-1h1c1 1 1 2 2 3h1l1 1h2 2l1-1h2l1-1 1-1c1 1 1 2 1 3l1 1v1c1 2 2 5 4 6v1c1 0 2 1 2 2 1 1 2 2 2 3 1 0 2 2 2 2h1 1l2 5h1c1 1 1 2 2 3 0 2 1 4 2 5 1 3 1 5 2 8s3 6 5 9c1 1 6 9 7 9h0 1c-1 1-1 1-2 1 0 1 0 2 1 3-1 1-1 1-1 3h-1l1 2-1 1h-1 0v1 2c0 1-1 2-2 2v3c1 0 2 1 2 2l1 2v1 1 2l-1 2c1 2 2 7 3 8 0 1-1 1-2 2s-1 2-2 2v1l-2 3v1l-1 2c0 1 0 2-1 3h0l-1 3h0c0 1-1 1-1 2l-1 4-1 1-1-1v2l-1 1h0l-1 1h0v3 4l-2 7c-1-4 0-7-3-12h0v-1l-1-4-1-2-3-11c-1 0-2-1-3 0v1h-1c0-1-1-2-1-2l-2-2c0-2-1-4-1-5-1-2-1-3-1-4v-3-2l-1-2h-1v1c-1 0-1-1-1-1l-1 1v-1h-1v-1h0c0-1 0-1-1-2 0 1-1 2-1 2l-2 1h-2l1-1-2-2 5-5c-1-1 0-2 0-3v-1l2-2-2-2c-1 0-1-1-2-2 0 1 0 1-1 2h-1 0l-2 2h-3s0-1-1-1h-1l-2 2h-1-1c-1 0-2-1-3-2l1-1h-1c1-1 1-2 2-2s1-1 2-1v-2c0-1 1-1 1-2h1l-1-1c0-1 1-2 1-2 1 0 1 1 1 1h0c1 1 1 1 2 1l1-2-1-1h1c1 0 1 1 1 1l1-1h1 0c0-1 0-1 1-2v-1-5h1v-4c-1-1 0-2-1-3l-1 1c-1-2 0-5-1-7 0-1 0-2 1-2v-4-2c-1-1-1-3-1-4v-1l1-1v-1h0l-1-2h-2v-1l-1-1s0-1-1-1h0l-1-1c1-2 1-3 1-5l1-1h0c-1 0-1-1-2-2l1-1h0v-1c-1-1-1-2-2-3l1-2-2-4-1-3z" class="AA"></path><path d="M246 233c0 1 0 1 1 1v-1h1v2s-1 0-1 1v-1c-2 1-1 4-4 5l1-2 1-5h1 0z" class="x"></path><path d="M240 240l1-1c1 0 1 1 2 2-1 1-1 2-2 4h-2-2l-1 1h0c0-1 1-2 1-3v-1l1-1h0c0 1 0 2 1 2s1-1 1-1v-2z" class="E"></path><path d="M238 252c1-2 1-2 3-3l1 1s0 1-1 2-2 3-3 3h-5l1-1 1-1c1-1 2-1 3-1z" class="C"></path><path d="M234 254l1-1c1-1 2-1 3-1 0 1-1 2-2 2 0 1-1 0-2 0z" class="l"></path><path d="M242 250l1 1c-1 2-3 5-4 7v3l-2 2-1 1-1 1c-1-1 0-2 0-3v-1l2-2-2-2c-1 0-1-1-2-2 0 1 0 1-1 2h-1c0-1 1-2 2-2h5c1 0 2-2 3-3s1-2 1-2h0z" class="J"></path><path d="M235 257l1-1h2c-1 2-1 2-1 4h0l2-2v3l-2 2-1 1-1 1c-1-1 0-2 0-3v-1l2-2-2-2z" class="O"></path><path d="M239 258v3l-2 2c-1-1 0-2 0-3l2-2z" class="V"></path><path d="M254 225l2-1c1 2 1 3 2 4h1 0l1 1-2 3-1 3c-1 1-2 1-3 2l-1 1v-7-4-1h1v-1h0z" class="g"></path><path d="M254 225h0c1 2 1 4 0 6h-1v-4-1h1v-1z" class="E"></path><path d="M234 243v-2l2-1v1 2h1c0 1-1 2-1 3h0c0 1-1 2-2 3h-2v3h-1v-1c-2 1-3 4-4 5 1 1 1 1 1 2l3-1-2 2h-3s0-1-1-1h-1 0l1-1h0v-1l2-2h1v-3c0-1 1-1 1-2s0-2-1-2l1-1h1 0c1 1 0 1 1 1l1-2h0c1-1 2-1 2-2z" class="h"></path><path d="M232 245h0c1 0 1 1 1 1l1 1v2h-2 0l-1-1h-1l-1 1c0-1 0-2-1-2l1-1h1 0c1 1 0 1 1 1l1-2z" class="D"></path><path d="M232 245h0c1 0 1 1 1 1l1 1-2 1h0v-3z" class="O"></path><path d="M234 243v-2l2-1v1 2h1c0 1-1 2-1 3h0c0 1-1 2-2 3v-2l-1-1s0-1-1-1c1-1 2-1 2-2z" class="G"></path><path d="M235 244l1-3v2h1c0 1-1 2-1 3-1-1-1-1-1-2z" class="P"></path><path d="M235 244c0 1 0 1 1 2h0c0 1-1 2-2 3v-2l-1-1 2-2z" class="L"></path><path d="M226 246h1c1 0 1 1 1 1 1 0 1 1 1 2s-1 1-1 2v3h-1l-2 2v1h0l-1 1h0l-2 2h-1-1c-1 0-2-1-3-2l1-1h-1c1-1 1-2 2-2s1-1 2-1v-2c0-1 1-1 1-2h1l-1-1c0-1 1-2 1-2 1 0 1 1 1 1h0c1 1 1 1 2 1l1-2-1-1z" class="B"></path><path d="M223 247c1 0 1 1 1 1h0v1l-1 1-1-1c0-1 1-2 1-2z" class="u"></path><path d="M224 258h-1c-1 0-2 0-3-1v-1c0-1 1-1 2-2v3h1l2-2v2l-1 1z" class="U"></path><path d="M226 246h1c1 0 1 1 1 1 1 0 1 1 1 2s-1 1-1 2v3h-1l-2 2 1-1v-1c1-1 1-2 1-3v-4l-1-1z" class="l"></path><path d="M222 254h0v-2l1-1 1-1s1 1 2 1v2l-1 2-2 2h-1v-3z" class="g"></path><path d="M251 243h1 0c0 1 0 1-1 1v1 4c-2 2-3 4-4 6l-1 2v2l-1-1c-1 0-1-1-1-2l-6 8h0l1-3h0v-3c1-2 3-5 4-7l-1-1c1-1 1-2 2-2v1h1l2-3h1c1-2 2-2 3-3z" class="C"></path><path d="M247 255h0c0-3 3-7 3-9 1-1 1-1 1-2v1 4c-2 2-3 4-4 6z" class="K"></path><path d="M243 251l2-2h0v1h1c1-1 2-3 3-4-1 4-3 7-5 10l-6 8h0l1-3h0v-3c1-2 3-5 4-7z" class="G"></path><path d="M244 256c0 1 0 2 1 2l1 1-1 2-1 1c1 0 1 1 1 2l-1 2v6-1h-1l-1 4h0l-1-2h-1v1c-1 0-1-1-1-1l-1 1v-1h-1v-1h0c0-1 0-1-1-2 0 1-1 2-1 2l-2 1h-2l1-1-2-2 5-5 1-1 1-1 2-2h0l-1 3h0l6-8z" class="D"></path><path d="M243 263v-2c0-1 0-1 1-2h0v1l1 1-1 1-1 1z" class="C"></path><path d="M244 262c1 0 1 1 1 2l-1 2c-1 0-1 0-2 1v-2-1l1-1 1-1z" class="S"></path><path d="M236 264c0 2-1 3-1 4-1 2-2 3-3 4l-2-2 5-5 1-1z" class="R"></path><path d="M236 270c1-1 1-2 2-3l3-4s0-1 1-1v2 1 2c1-1 1-1 2-1v6-1h-1l-1 4h0l-1-2h-1v1c-1 0-1-1-1-1l-1 1v-1h-1v-1h0c0-1 0-1-1-2z" class="d"></path><path d="M237 272c1-2 3-5 5-7v2l-3 6-1 1v-1h-1v-1z" class="C"></path><path d="M244 266v6-1h-1l-1 4h0l-1-2h-1v1c-1 0-1-1-1-1l3-6c1-1 1-1 2-1z" class="i"></path><path d="M243 271v-3h1v3h-1z" class="j"></path><path d="M257 235c0 1 0 3 1 4h1l-1 2v1c-1 2-1 6 0 8l1 1c0-2 1-2 2-2 0 0 0 1-1 1v5l2 4-1 1h-2l1 3c-1 1-1 1-1 2h-1l-1 1-2-6h-1v2l-1 1c0 1 0 1-1 2v-1h-1l-1 1c-1 0-1 0-2 1h0-1 0v-3l-1 4c0-1-1-2-1-3s0-2-1-2l1-1 1-2v-2l1-2c1-2 2-4 4-6v-4-1c1 0 1 0 1-1h0-1c0-2 0-3 1-4l1-1 1-1c1-1 2-1 3-2z" class="g"></path><path d="M249 260c0 2 1 2 2 3v-2l1 3h-1c-1 0-1-1-2-1v-1-1-1z" class="U"></path><path d="M253 255h0l1 1v1l1 2v1h-1v2l-1 1c0 1 0 1-1 2v-1l-1-3c1-2 1-4 2-6z" class="S"></path><path d="M253 263v-7h1v1l1 2v1h-1v2l-1 1z" class="b"></path><path d="M253 244h1s1-1 2-1h0v3c-1 2-1 2-2 2 0 1-1 0-2 1 0 1 0 1-1 2v-2-4c1 0 1-1 2-1h0z" class="R"></path><path d="M253 244h1s1-1 2-1h0v3c-1 1-1 1-2 1h-1v-3h0z" class="L"></path><path d="M257 235c0 1 0 3 1 4h1l-1 2v1l-2 1h0c-1 0-2 1-2 1h-1 0c-1 0-1 1-2 1v-1c1 0 1 0 1-1h0-1c0-2 0-3 1-4l1-1 1-1c1-1 2-1 3-2z" class="K"></path><path d="M256 239l2 2v1l-2 1h0v-4zm-3-1l1-1h1l-1 4c-1 0-2-1-2-2l1-1z" class="E"></path><path d="M252 239c0 1 1 2 2 2l-1 3h0c-1 0-1 1-2 1v-1c1 0 1 0 1-1h0-1c0-2 0-3 1-4z" class="T"></path><path d="M257 235c0 1 0 3 1 4h1l-1 2-2-2s0-1-1-2h0-1c1-1 2-1 3-2z" class="R"></path><path d="M247 255c1-2 2-4 4-6v2 4l-2 5h0v1 1 1c1 0 1 1 2 1l-1 1c-1 0-1 0-2 1h0-1 0v-3l-1 4c0-1-1-2-1-3s0-2-1-2l1-1 1-2v-2l1-2z" class="l"></path><path d="M248 258h0c0-2 0-4 1-5h1l1 2-2 5v-2h-1 0z" class="D"></path><path d="M246 257h1l-1 2c1 1 1 3 1 4h0l-1 4c0-1-1-2-1-3s0-2-1-2l1-1 1-2v-2z" class="F"></path><path d="M248 258h1v2h0v1 1 1c1 0 1 1 2 1l-1 1c-1 0-1 0-2 1h0-1 0v-3h0c0-2 1-3 1-5h0z" class="J"></path><path d="M248 258h1v2h0v1h-1v-3z" class="d"></path><path d="M248 266c0-2 0-3 1-4v1c1 0 1 1 2 1l-1 1c-1 0-1 0-2 1h0z" class="F"></path><path d="M259 251c0-2 1-2 2-2 0 0 0 1-1 1v5l2 4-1 1h-2l1 3c-1 1-1 1-1 2h-1l-1 1-2-6v-1l-1-2v-1l-1-1h0v-1c1 1 2 1 3 0v-1h0c2 0 2-1 3-2h0z" class="Y"></path><path d="M256 256l1 2c-1 0-2 1-2 1l-1-2c1 0 1 0 2-1z" class="i"></path><g class="T"><path d="M256 256v-2h0c1 0 1 0 2 1v-2h1l2 7h-2l-1-2c0-1 0-1-1-1v1h0l-1-2z"></path><path d="M257 258v-1c1 0 1 0 1 1l1 2 1 3c-1 1-1 1-1 2h-1l-1 1-2-6v-1s1-1 2-1h0z"></path></g><path d="M257 258h0v1c-1 1-1 1-2 1v-1s1-1 2-1z" class="E"></path><path d="M257 258v-1c1 0 1 0 1 1l1 2 1 3c-1 1-1 1-1 2h-1c0-2 0-4-1-6v-1z" class="V"></path><path d="M258 228c0-1 1-3 1-3h1v-1c1 0 0-3 1-4 0 0 0-1 1-1 1 3 1 5 2 8s3 6 5 9c1 1 6 9 7 9h0 1c-1 1-1 1-2 1 0 1 0 2 1 3-1 1-1 1-1 3h-1l1 2-1 1h-1 0v1 2c0 1-1 2-2 2h0v-1l-1-7-1 1v-1c0-1-1-1-1-2l-1-1c-1 0-1 0-1-1s0-2-1-2-1 1-1-1h-1v4s0 1-1 1h-2c1 0 1-1 1-1-1 0-2 0-2 2l-1-1c-1-2-1-6 0-8v-1l1-2h-1c-1-1-1-3-1-4l1-3 2-3-1-1h0-1z" class="AA"></path><path d="M265 237h1c0 1 1 3 1 4l-1 1-1-5z" class="w"></path><path d="M261 249c0-1 0-2 1-3l1 3s0 1-1 1h-2c1 0 1-1 1-1z" class="i"></path><path d="M258 228c0-1 1-3 1-3h1v-1c1 0 0-3 1-4 0 0 0-1 1-1 1 3 1 5 2 8-1 2-1 4-1 7 0-1-1-1-1-2h0v1c0 2-1 3-2 4h-1v1 1h-1c-1-1-1-3-1-4l1-3 2-3-1-1h0-1z" class="v"></path><path d="M260 230v-1h1v1h1v3c0 2-1 3-2 4h-1v1 1h-1c-1-1-1-3-1-4l1-3 2-3v1z" class="B"></path><path d="M260 230v-1h1v1 2h-1v-2z" class="g"></path><path d="M258 232c1 1 1 3 1 4v2 1h-1c-1-1-1-3-1-4l1-3z" class="L"></path><path d="M264 227c1 3 3 6 5 9 1 1 6 9 7 9h0 1c-1 1-1 1-2 1 0 1 0 2 1 3-1 1-1 1-1 3h-1l1 2-1 1h-1 0v1 2c0 1-1 2-2 2h0v-1l-1-7-1 1v-1c0-1-1-1-1-2l-1-1c-1 0-1 0-1-1 1-1 1-1 1-2l-1-4 1-1c0-1-1-3-1-4h-1 0c-1-1-2-2-2-3 0-3 0-5 1-7z" class="B"></path><path d="M273 246c0 1 1 2 1 4l-1 1v-3h-1c0-1 0-2 1-2z" class="AC"></path><path d="M271 250v-2c1-1 1-2 0-3h0l1-1v1l1 1c-1 0-1 1-1 2h1l-1 1v2l-1-1z" class="j"></path><path d="M267 241c0 1 0 2 1 2v2c1 2 2 4 2 5l1 1v-1l1 1v-2l1-1v3l1-1v2l1 2-1 1h-1 0v1 2c0 1-1 2-2 2h0v-1l-1-7-1 1v-1c0-1-1-1-1-2l-1-1c-1 0-1 0-1-1 1-1 1-1 1-2l-1-4 1-1z" class="E"></path><path d="M273 251l1-1v2l1 2-1 1h-1v-4z" class="W"></path><path d="M273 256v2c0 1-1 2-2 2h0v-1-2c1 0 1-1 2-1z" class="C"></path><path d="M267 246h0c1 1 1 2 1 2 1 1 1 3 2 4l-1 1v-1c0-1-1-1-1-2l-1-1c-1 0-1 0-1-1 1-1 1-1 1-2z" class="F"></path><path d="M264 227c1 3 3 6 5 9v3c1 1 1 3 1 4l-1 1s0 1-1 1v-2c-1 0-1-1-1-2s-1-3-1-4h-1 0c-1-1-2-2-2-3 0-3 0-5 1-7z" class="Y"></path><path d="M268 240c1 1 1 3 1 4 0 0 0 1-1 1v-2-3z" class="v"></path><path d="M266 237v-2h1c1 1 1 2 1 3 1 1 0 1 0 2v3c-1 0-1-1-1-2s-1-3-1-4z" class="x"></path><path d="M263 249v-4h1c0 2 0 1 1 1s1 1 1 2 0 1 1 1l1 1c0 1 1 1 1 2v1l1-1 1 7v1h0v3c1 0 2 1 2 2l1 2v1 1 2l-1 2c1 2 2 7 3 8 0 1-1 1-2 2s-1 2-2 2-2 0-2-1h-1l-1-1v-2 1l-1 1-1-1v1c-1-1-2-3-2-5l-3-6c-1-2-1-5-2-7 0-1 0-1 1-2l-1-3h2l1-1-2-4v-5h2c1 0 1-1 1-1z" class="W"></path><path d="M269 270c1 1 2 2 2 3s-1 2-1 2l-1-1-1-3 1-1z" class="C"></path><path d="M267 262h1v1l1 7-1 1v-2c0-2 0-3-1-5v-2z" class="D"></path><path d="M271 278c1-1 1-2 1-3 1 0 1 0 1 1 1 1 1 3 1 5h-2 0-1l-1-2 1-1z" class="F"></path><path d="M271 278l1 3h0-1l-1-2 1-1z" class="j"></path><path d="M271 263c1 0 2 1 2 2l1 2v1 1 2l-1 2v-2c0-2-1-4-2-6v-1-1z" class="F"></path><path d="M270 284v-4l1 1h1 0 2v2c-1 1-1 2-2 2s-2 0-2-1z" class="U"></path><path d="M267 264c1 2 1 3 1 5v2l1 3h0-1c-1 0-1-1-1-1l-2-5h1v-1-3h1z" class="c"></path><path d="M267 273v-3s1 0 1-1v2l1 3h0-1c-1 0-1-1-1-1z" class="R"></path><path d="M262 259l3 9 2 5s0 1 1 1h1 0l1 1v1 3l1 2-1-1v4h-1l-1-1v-2 1l-1 1-1-1v1c-1-1-2-3-2-5l-3-6c-1-2-1-5-2-7 0-1 0-1 1-2l-1-3h2l1-1z" class="j"></path><path d="M268 283c1-1 1-1 1-2h0v-1c0-1 0-3 1-4v3l1 2-1-1v4h-1l-1-1z" class="B"></path><path d="M259 265c0-1 0-1 1-2 1 2 2 5 3 7l2 2c2 2 2 5 3 7v2 1l-1 1-1-1v1c-1-1-2-3-2-5l-3-6c-1-2-1-5-2-7z" class="P"></path><path d="M263 270l2 2-1 2h0l-1-4z" class="W"></path><defs><linearGradient id="d" x1="263.999" y1="274.802" x2="267.503" y2="279.537" xlink:href="#B"><stop offset="0" stop-color="#636363"></stop><stop offset="1" stop-color="#79797a"></stop></linearGradient></defs><path fill="url(#d)" d="M265 272c2 2 2 5 3 7v2 1l-1 1-1-1v-1l-2-7h0l1-2z"></path><path d="M268 279v2 1l-1-1 1-2z" class="Z"></path><path d="M266 281h1l1 1-1 1-1-1v-1z" class="L"></path><defs><linearGradient id="e" x1="267.591" y1="247.467" x2="265.852" y2="263.699" xlink:href="#B"><stop offset="0" stop-color="#3f3e3f"></stop><stop offset="1" stop-color="#676666"></stop></linearGradient></defs><path fill="url(#e)" d="M263 249v-4h1c0 2 0 1 1 1s1 1 1 2 0 1 1 1l1 1c0 1 1 1 1 2v1l1-1 1 7v1h0v3 1 1h0c-1 0-1-1-2-1l-1-1v-1h-1v2h-1v3 1h-1c-1-2-2-6-3-9l-2-4v-5h2c1 0 1-1 1-1z"></path><path d="M268 250c0 1 1 1 1 2-1 1-1 1-2 1v-1-1h1v-1z" class="u"></path><path d="M265 259h0l1 1v-2c1 1-1 3 1 4v2h-1l-1-5z" class="k"></path><path d="M271 260h0v3 1 1h0c-1 0-1-1-2-1l1-1-1-1 1-1h0l1-1z" class="l"></path><path d="M271 260h0v3 1l-1-3h0l1-1z" class="B"></path><path d="M269 253l1-1 1 7v1l-1 1c-1-3-1-5-1-8z" class="U"></path><path d="M260 250h2l1 1h0v1c0 2 1 5 2 7h0l1 5v3 1h-1c-1-2-2-6-3-9l-2-4v-5z" class="H"></path><path d="M260 250h2l1 1h-1c0 2 1 6 0 7 0-2-1-4-1-6h-1v3-5z" class="E"></path><path d="M233 208l1 1 1 1 1-1c1 2 1 4 2 5v2c1 1 1 2 1 3l1-2v-1l2 1 2 2h0l2 2v2 2l1 3h1c0-1 1-1 1-2l1 1-1 1c0 2 0 3-1 5h-1v1c-1 0-1 0-1-1h0-1l-1 5-1 2v1c-1-1-1-2-2-2l-1 1v2s0 1-1 1-1-1-1-2h0l-1 1v1h-1v-2-1l-2 1v2c0 1-1 1-2 2h0l-1 2c-1 0 0 0-1-1 0-1 0-1 1-2v-1-5h1v-4c-1-1 0-2-1-3l-1 1c-1-2 0-5-1-7 0-1 0-2 1-2v-4-2c-1-1-1-3-1-4v-1l1-1v-1h0 1l1 1v-2h0l1-1z" class="H"></path><path d="M236 235c0-2 0-2 1-3h0c1 2 1 4 1 6l-1-2-1-1z" class="b"></path><path d="M236 235l1 1 1 2-1 4v1h-1v-2-1-5z" class="I"></path><path d="M238 222l2 7v1l-1 6h0c0-3 0-6-1-8v-3c0-1-1-2 0-3z" class="b"></path><path d="M238 216c1 1 1 2 1 3v4h1l1 5-1 1-2-7c0-1-1-2-1-3h0v-3h1z" class="E"></path><path d="M238 216c0 1 1 2 0 3h-1 0v-3h1z" class="B"></path><path d="M234 223c1 0 2-1 2-1 1-1 0-2 1-3h0c0 1 1 2 1 3-1 1 0 2 0 3v3h-1 0-1-1v-1l-1-4z" class="T"></path><path d="M238 225v3h-1 0-1-1v-1c1 0 1 0 2-1l1-1z" class="C"></path><path d="M235 228h1 1 0v4h0c-1 1-1 1-1 3v5l-2 1v2c-1-1-1-3-1-4 1-1 1-3 0-4v-3h0 1v-1c0 1 0 2 1 2h0v-5z" class="L"></path><path d="M241 228h0c0 3-1 7 0 9 1 0 1-1 1-2l1 1c0 1 0 1 1 2l-1 2v1c-1-1-1-2-2-2l-1 1v2h-1v-1c-1-1 0-4 0-5h0l1-6v-1l1-1z" class="H"></path><path d="M240 230v10 2h-1v-1c-1-1 0-4 0-5h0l1-6z" class="O"></path><path d="M234 228v3 1h-1 0v3c1 1 1 3 0 4 0 1 0 3 1 4 0 1-1 1-2 2h0l-1 2c-1 0 0 0-1-1 0-1 0-1 1-2v-1-5h1v-4-5l2-1z" class="Z"></path><path d="M231 243v-5h1v7l-1 2c-1 0 0 0-1-1 0-1 0-1 1-2v-1z" class="l"></path><path d="M240 216l2 1 2 2h0l2 2v2 2l1 3h1c0-1 1-1 1-2l1 1-1 1c0 2 0 3-1 5h-1v1c-1 0-1 0-1-1h0-1l-1 5c-1-1-1-1-1-2l-1-1c0 1 0 2-1 2-1-2 0-6 0-9h0l-1-5h-1v-4l1-2v-1z" class="Z"></path><path d="M242 223c-1-1-1-2-1-3 1 1 2 3 3 5v1c0 2 0 2-1 4l-1-7z" class="I"></path><path d="M240 217h1v2 1c0 1 0 2 1 3l-1 1c0-1 0-1-1-2v1h-1v-4l1-2z" class="r"></path><path d="M240 216l2 1 2 2h0l2 2v2h0c-1-1-1-2-2-2h0v4c-1-2-2-4-3-5v-1-2h-1v-1z" class="L"></path><path d="M240 223v-1c1 1 1 1 1 2 1 4 2 7 1 11 0 1 0 2-1 2-1-2 0-6 0-9h0l-1-5z" class="V"></path><path d="M244 225v-4h0c1 0 1 1 2 2h0v2l1 3h1c0-1 1-1 1-2l1 1-1 1c0 2 0 3-1 5h-1v1c-1 0-1 0-1-1h0-1l-1 5c-1-1-1-1-1-2s0-2 1-3c0-1-1-2-1-3 1-2 1-2 1-4v-1z" class="O"></path><path d="M244 226c0 2 1 5 0 7 0-1-1-2-1-3 1-2 1-2 1-4z" class="V"></path><path d="M246 225l1 3h1l-2 5h0 0v-8z" class="P"></path><path d="M248 228c0-1 1-1 1-2l1 1-1 1c0 2 0 3-1 5h-1v1c-1 0-1 0-1-1h0l2-5z" class="J"></path><path d="M233 208l1 1 1 1 1-1c1 2 1 4 2 5v2h-1v3c-1 1 0 2-1 3 0 0-1 1-2 1l1 4v1 5h0c-1 0-1-1-1-2v-3l-2 1v5c-1-1 0-2-1-3l-1 1c-1-2 0-5-1-7 0-1 0-2 1-2v-4-2c-1-1-1-3-1-4v-1l1-1v-1h0 1l1 1v-2h0l1-1z" class="H"></path><path d="M230 217h1v2h1v1h0c-1 0-1-1-1-1 0 2 0 3-1 4v-4-2z" class="s"></path><path d="M229 213l1 1 2 2v-1l1 2c-1 1-1 1-1 2h-1v-2h-1c-1-1-1-3-1-4z" class="l"></path><path d="M232 216v-1l1 2c-1 1-1 1-1 2h-1v-2-1h1 0z" class="T"></path><path d="M230 210h1l1 1v4 1l-2-2-1-1v-1l1-1v-1h0z" class="O"></path><path d="M230 210c0 2 1 3 0 4l-1-1v-1l1-1v-1z" class="L"></path><path d="M234 228c-1-2-2-8-1-9h0l1 4 1 4v1 5h0c-1 0-1-1-1-2v-3z" class="K"></path><path d="M232 209c0 2 1 4 2 5v2l1 1 1-1h1v3c-1 1 0 2-1 3 0 0-1 1-2 1l-1-4v-2l-1-2v-4-2z" class="E"></path><path d="M233 208l1 1 1 1 1-1c1 2 1 4 2 5v2h-1-1l-1 1-1-1v-2c-1-1-2-3-2-5h0l1-1z" class="x"></path><path d="M221 182l2 2c0 1 0 2 1 2l2 3v-1l1-1v-1h1c1 1 1 2 2 3h1l1 1h2 2l1-1h2l1-1 1-1c1 1 1 2 1 3l1 1v1c1 2 2 5 4 6v1c1 0 2 1 2 2 1 1 2 2 2 3 1 0 2 2 2 2h1 1l2 5h1c1 1 1 2 2 3 0 2 1 4 2 5-1 0-1 1-1 1-1 1 0 4-1 4v1h-1s-1 2-1 3c-1-1-1-2-2-4l-2 1h0v1h-1v1h-2 0v2h-1l-1-1 1-1-1-1c0 1-1 1-1 2h-1l-1-3v-2-2l-2-2h0l-2-2-2-1v1l-1 2c0-1 0-2-1-3v-2c-1-1-1-3-2-5l-1 1-1-1-1-1-1 1h0v2l-1-1h-1l-1-2h-2v-1l-1-1s0-1-1-1h0l-1-1c1-2 1-3 1-5l1-1h0c-1 0-1-1-2-2l1-1h0v-1c-1-1-1-2-2-3l1-2-2-4-1-3z" class="Z"></path><path d="M250 205l2 2-1 2h-1c0-1 0-2-1-3l1-1z" class="k"></path><path d="M250 218h1v-2l1 1c0 1 1 3 1 4h-1v-1h-1v1h-1 0c0-1 0-1-1-2l1-1z" class="P"></path><path d="M254 225v-5h0c1 1 2 3 2 4h0l-2 1h0z" class="B"></path><path d="M256 218s0 1-1 1h0c-1-1-1-2-1-4-1-2-2-3-2-5h1c0 1 0 2 1 3h0l2 5z" class="K"></path><path d="M249 214l2 1h1v2l-1-1v2h-1l-1 1c0-1-1-2-1-4l1-1h0z" class="d"></path><path d="M248 215l1-1c0 1 1 3 1 4l-1 1c0-1-1-2-1-4z" class="I"></path><path d="M250 227h1v-1h-1v-2l1-3c2 1 2 3 2 5v1h-2 0v2h-1l-1-1 1-1z" class="w"></path><path d="M254 213c2 1 1 2 3 3 1-1-3-6-3-8h1c1 2 1 5 3 6h0c0-1 0-2-1-3h1c1 1 1 2 2 3-1 1-1 1-1 2 0 2-1 3-2 4v1c-1-1-1-2-1-3l-2-5z" class="C"></path><path d="M257 221v-1c1-1 2-2 2-4 0-1 0-1 1-2 0 2 1 4 2 5-1 0-1 1-1 1-1 1 0 4-1 4v1h-1s-1 2-1 3c-1-1-1-2-2-4h0 1v-3z" class="k"></path><path d="M243 197c-1 0-2-1-2-2l-1-1 1-1 1 1h0c0-1-1-2-1-3h-1 0l1-1 2 2c1 2 2 5 4 6v1c1 0 2 1 2 2 1 1 2 2 2 3 1 0 2 2 2 2l-1 1-2-2-1 1c-1-2-2-5-4-7h0c-1-1-1-2-2-2z" class="S"></path><path d="M250 205c-1-2-3-4-3-6 1 0 2 1 2 2 1 1 2 2 2 3 1 0 2 2 2 2l-1 1-2-2z" class="l"></path><path d="M243 207l1-1c1 2 4 5 4 8l1-1h0v1h0l-1 1c-1 1-1 1 0 2 0 2 1 3 1 5 0 1 1 3 0 4 0 1-1 1-1 2h-1l-1-3v-2-2-1c0-1-1-3-1-5h0l-2-6h1s0 1 1 1c0-1-1-2-2-3z" class="K"></path><path d="M245 215c1 2 2 6 3 8l1-1c0 1 1 3 0 4 0 1-1 1-1 2h-1l-1-3v-2-2-1c0-1-1-3-1-5h0z" class="O"></path><path d="M246 221v-1c1 3 2 5 2 8h-1l-1-3v-2-2z" class="f"></path><path d="M240 188l1-1c1 1 1 2 1 3l1 1v1l-2-2-1 1h0 1c0 1 1 2 1 3h0l-1-1-1 1 1 1c0 1 1 2 2 2 0 1 1 2 1 3 2 1 2 3 3 5 1 1 1 3 2 4v2c2 1 2 2 3 4h-1l-2-1v-1h0l-1 1c0-3-3-6-4-8 0-2-6-10-6-13-1-1-1-2-2-3l1-1h2l1-1z" class="j"></path><path d="M247 208h0l2 3c2 1 2 2 3 4h-1l-2-1v-1h0l-2-5z" class="I"></path><path d="M239 189c0 1 0 1 1 2v1 1l-2-1v1c-1-1-1-2-2-3l1-1h2z" class="S"></path><path d="M244 200c2 1 2 3 3 5 1 1 1 3 2 4v2l-2-3h0-1c0-1-1-2-1-3-1-2-1-3-1-5z" class="c"></path><path d="M221 182l2 2c0 1 0 2 1 2l2 3v-1l1-1v-1h1c1 1 1 2 2 3h1l1 1h2 2c1 1 1 2 2 3 0 3 6 11 6 13l-1 1c1 1 2 2 2 3-1 0-1-1-1-1h-1l2 6h0c0 2 1 4 1 5v1l-2-2h0l-2-2-2-1v1l-1 2c0-1 0-2-1-3v-2c-1-1-1-3-2-5l-1 1-1-1-1-1-1 1h0v2l-1-1h-1l-1-2h-2v-1l-1-1s0-1-1-1h0l-1-1c1-2 1-3 1-5l1-1h0c-1 0-1-1-2-2l1-1h0v-1c-1-1-1-2-2-3l1-2-2-4-1-3z" class="H"></path><path d="M237 201c-1-1-2-3-2-4-1-2-2-2-2-4 2 3 4 5 6 8l1 4-1 1-2-5z" class="Y"></path><path d="M227 187c2 2 3 4 4 7 1 1 1 1 1 3-2-3-5-5-6-8v-1l1-1z" class="AC"></path><path d="M239 201c0-2-2-4-3-6 4 3 5 7 7 12 1 1 2 2 2 3-1 0-1-1-1-1h-1 0c-2-1-2-2-3-4l-1-4z" class="R"></path><path d="M237 201l2 5 1-1c1 2 1 3 3 4h0l2 6h0c0 2 1 4 1 5v1l-2-2h0l-2-2-2-1v1l-1 2c0-1 0-2-1-3v-2l1-1c0-1-1-1-1-2h-1l1-2-1-2c-1-1-2-3-2-5h1l1-1z" class="B"></path><path d="M238 209c2 1 3 3 3 5h0 0l-1-1h-1c0-1-1-1-1-2h-1l1-2z" class="H"></path><path d="M242 212v-1h0c1 1 2 3 3 4 0 2 1 4 1 5v1l-2-2c0-2-1-5-2-7z" class="I"></path><path d="M237 201l2 5 1 2-1 1c-1-1-1-2-2-2h0c-1-1-2-3-2-5h1l1-1z" class="c"></path><path d="M242 212c1 2 2 5 2 7h0l-2-2-2-1v1l-1 2c0-1 0-2-1-3v-2l1-1h1l1 1h0 0c1-1 1-1 1-2z" class="E"></path><path d="M239 213h1l1 1h0c1 1 1 2 1 3l-2-1c-1-1-1-2-1-3z" class="l"></path><path d="M239 213c0 1 0 2 1 3v1l-1 2c0-1 0-2-1-3v-2l1-1z" class="h"></path><path d="M221 182l2 2c0 1 0 2 1 2l2 3c1 3 4 5 6 8 0 2 2 3 3 5 0 2 1 4 2 5l1 2-1 2h1c0 1 1 1 1 2l-1 1c-1-1-1-3-2-5l-1 1-1-1-1-1-1 1h0v2l-1-1h-1l-1-2h-2v-1l-1-1s0-1-1-1h0l-1-1c1-2 1-3 1-5l1-1h0c-1 0-1-1-2-2l1-1h0v-1c-1-1-1-2-2-3l1-2-2-4-1-3z" class="L"></path><path d="M224 189l3 5h0l-1-1c-1 1-1 0-1 1-1-1-1-2-2-3l1-2z" class="E"></path><path d="M225 194c0-1 0 0 1-1l1 1h0c4 4 8 10 9 15l-1 1-1-1-1-1-6-11h0l-1 1c-1 0-1-1-2-2l1-1h0v-1z" class="R"></path><path d="M225 195l2 2-1 1c-1 0-1-1-2-2l1-1h0z" class="x"></path><path d="M227 197h1c3 2 6 8 7 12h-1l-1-1-6-11z" class="H"></path><path d="M227 197h0l6 11-1 1h0v2l-1-1h-1l-1-2h-2v-1l-1-1s0-1-1-1h0l-1-1c1-2 1-3 1-5l1-1h0l1-1z" class="F"></path><path d="M225 199l1-1c1 0 2 1 2 1v2h-1 0c-1-1-1-2-2-2z" class="R"></path><path d="M229 206l1-2 2 5h0v2l-1-1h-1l-1-2v-2z" class="Z"></path><path d="M225 199c1 0 1 1 2 2h0 1l1 2 1 1-1 2v2h-2v-1l-1-1s0-1-1-1h0l-1-1c1-2 1-3 1-5z" class="T"></path><path d="M227 201h0 1l1 2h-2-1v1-3h1z" class="s"></path><path d="M227 201h1l1 2h-2v-2z" class="c"></path><path d="M229 203l1 1-1 2v2h-2v-1c0-1 0-2-1-3v-1h1 2z" class="L"></path><path d="M229 203l1 1-1 2h-1c0-1 0-2-1-3h2z" class="h"></path><path d="M254 262v-2h1l2 6 1-1h1c1 2 1 5 2 7l3 6c0 2 1 4 2 5v-1l1 1 1-1v-1 2l1 1h1c0 1 1 1 2 1v1l-2 3v1l-1 2c0 1 0 2-1 3h0l-1 3h0c0 1-1 1-1 2l-1 4-1 1-1-1v2l-1 1h0l-1 1h0v3 4l-2 7c-1-4 0-7-3-12h0v-1l-1-4-1-2-3-11c-1 0-2-1-3 0v1h-1c0-1-1-2-1-2l-2-2c0-2-1-4-1-5-1-2-1-3-1-4v-3-2h0l1-4h1v1-6l1-2c0 1 1 2 1 3l1-4v3h0 1 0c1-1 1-1 2-1l1-1h1v1c1-1 1-1 1-2l1-1z" class="r"></path><path d="M257 285l1 1v2c-1-1-1-1-1-2v-1z" class="L"></path><path d="M254 270v1 1s0 1-1 1l-1-2h1l1-1z" class="W"></path><path d="M253 281l3 6-1 1c-1-1-1-2-1-3l-2-4h1z" class="C"></path><path d="M251 273v1h-1v1h1c1 1 2 2 2 4v2l-1-2c-1-1-1-1-1-2v-1c-1 0-1-1-2-1v-2h1 1 0z" class="Z"></path><path d="M254 272l1 4c1 2 2 4 3 5v2h0c-2-2-4-7-5-10 1 0 1-1 1-1z" class="c"></path><path d="M246 272l1-1s1 0 1 1v1h1c0-1 0-1 1-1v1h-1v2c1 0 1 1 2 1v1c0 1 0 1 1 2l1 2h0-1-1-1c0-1-1-2-1-3l-1-1c0-1-1-2-1-3h0c0-1-1-1-1-2z" class="D"></path><path d="M247 274l1-1c0 1 1 3 1 4 1 1 2 2 2 4h-1c0-1-1-2-1-3l-1-1c0-1-1-2-1-3h0z" class="t"></path><path d="M248 277l1 1c0 1 1 2 1 3h1 1l2 4c0 1 0 2 1 3h-2l-1-2h-1v1l-1-1v-1h-1c-1-1-1-2-1-3v-2-3z" class="d"></path><path d="M254 285c0 1 0 2 1 3h-2l-1-2v-1c1 1 1 1 2 0h0z" class="H"></path><path d="M248 277l1 1v4 2l1 1h-1c-1-1-1-2-1-3v-2-3z" class="l"></path><path d="M245 264c0 1 1 2 1 3v5c0 1 1 1 1 2h0c0 1 1 2 1 3v3 2c-1-2-2-2-2-4h0c-1-2-2-4-2-6v-6l1-2z" class="B"></path><path d="M247 274c0 1 1 2 1 3v3 2c-1-2-2-2-2-4l1 1v-5z" class="E"></path><path d="M252 264v1 1 1h-1l1 3c-1 1-1 1-1 3h0-1v-1c-1 0-1 0-1 1h-1v-1c0-1-1-1-1-1l-1 1v-5l1-4v3h0 1 0c1-1 1-1 2-1l1-1h1z" class="s"></path><path d="M252 264v1 1 1h-1c-1 0-1-1-2 0 0 0-1 1-1 2h-1v-3h1 0c1-1 1-1 2-1l1-1h1z" class="W"></path><path d="M252 264v1 1 1h-1l-1-2 1-1h1z" class="B"></path><path d="M261 298c-1-1 0-5 0-6v-1c-1-1-2-2-3-4l1-1 1 1h1l1 2h0c2 1 2 2 2 4v1c1 1 1 2 2 2v2h1 0c0 1-1 1-1 2l-1 4c-1-1-1-2-2-3l-2-1v-2z" class="G"></path><path d="M263 301c0-1 0-2 1-3l2 2-1 4c-1-1-1-2-2-3z" class="J"></path><path d="M243 271h1v1c0 2 1 4 2 6h0c0 2 1 2 2 4 0 1 0 2 1 3h1v1l1 1v1 4c-1 0-2-1-3 0v1h-1c0-1-1-2-1-2l-2-2c0-2-1-4-1-5-1-2-1-3-1-4v-3-2h0l1-4z" class="c"></path><path d="M242 277v-2h1c0 1 1 2 1 3-1 0-2 1-2 2v-3z" class="D"></path><path d="M242 280c0-1 1-2 2-2l2 6v1s0 1-1 1c0 1 0 2-1 3 0-2-1-4-1-5-1-2-1-3-1-4z" class="Z"></path><path d="M245 286v-1c-1 0-1-1-1-2 1 0 1 1 2 2 0 0 0 1-1 1z" class="L"></path><path d="M246 278h0c0 2 1 2 2 4 0 1 0 2 1 3h1v1l1 1v1 4c-1 0-2-1-3 0v1h-1c0-1-1-2-1-2l-2-2c1-1 1-2 1-3 1 0 1-1 1-1v-1l1 2v-2c-1-2-2-4-1-6z" class="C"></path><path d="M247 284l2 4v2h-1l-1-4h0v-2z" class="k"></path><path d="M250 285v1l1 1v1 1l-2 1v-2-1-2h1z" class="W"></path><path d="M250 285v1 1h-1v-2h1z" class="S"></path><path d="M251 288v4c-1 0-2-1-3 0v-2h1l2-1v-1z" class="H"></path><path d="M246 284l1 2h0l1 4v2 1h-1c0-1-1-2-1-2l-2-2c1-1 1-2 1-3 1 0 1-1 1-1v-1z" class="Z"></path><path d="M246 284l1 2h0c-1 1-1 3-1 5l-2-2c1-1 1-2 1-3 1 0 1-1 1-1v-1z" class="h"></path><path d="M264 278c0 2 1 4 2 5v-1l1 1 1-1v-1 2l1 1h1c0 1 1 1 2 1v1l-2 3v1l-1 2c0 1 0 2-1 3h0l-1 3h-1v-2c-1 0-1-1-2-2v-1c0-2 0-3-2-4h0l-1-2c-1-1-1-2-2-3h0c1 0 1 0 1-1l1 1h0c0-1 0-2-1-4l1-1v1l1-1v1h1v-1l1-1z" class="W"></path><path d="M268 289c0 1 0 2 1 3 0 1 0 2-1 3h-1v1h-1c0-2 0-3 1-4v-2 1c1-1 1-2 1-2z" class="R"></path><path d="M268 281v2l1 1h1c0 1 1 1 2 1v1l-2 3v1l-1 2c-1-1-1-2-1-3s0-3-1-4h0c-1 1-1 2-1 2h-1v-1h-1v2c-1-1-1-2-1-4l1 1 1-1h1l1-1 1-1v-1z" class="H"></path><path d="M267 285h1c1 0 1 2 1 3 0 0 0 1 1 1v1l-1 2c-1-1-1-2-1-3s0-3-1-4z" class="L"></path><path d="M269 284h1c0 1 1 1 2 1v1l-2 3c-1 0-1-1-1-1v-4z" class="i"></path><path d="M264 278c0 2 1 4 2 5v-1l1 1-1 1h-1l-1 1-1-1c0 2 0 3 1 4 1 2 1 6 2 8-1 0-1-1-2-2v-1c0-2 0-3-2-4h0l-1-2c-1-1-1-2-2-3h0c1 0 1 0 1-1l1 1h0c0-1 0-2-1-4l1-1v1l1-1v1h1v-1l1-1z" class="Z"></path><path d="M261 279v1l2 4v1c-1 0-1-1-2-1 0-1 0-2-1-4l1-1z" class="K"></path><path d="M259 284h0c1 0 1 0 1-1l1 1c0 2 1 3 1 5h0l-1-2c-1-1-1-2-2-3z" class="t"></path><path d="M254 262v-2h1l2 6 1-1h1c1 2 1 5 2 7l3 6-1 1v1h-1v-1l-1 1v-1l-1 1c1 2 1 3 1 4h0l-1-1c0 1 0 1-1 1h0c-1 0 0-1-1-1v-2c-1-1-2-3-3-5l-1-4v-1-1l-1 1h-1v-1l-1-3h1v-1-1c1-1 1-1 1-2l1-1z" class="V"></path><path d="M253 263l1-1v4l-1 1-1-1v-1c1-1 1-1 1-2z" class="T"></path><path d="M261 279c-1-2-2-4-2-6h0l1 2c0 1 1 2 1 3l1 1-1 1v-1z" class="b"></path><path d="M254 266l1 2c0 1-1 2-1 3h0v-1l-1-3 1-1z" class="H"></path><path d="M252 266l1 1 1 3-1 1h-1v-1l-1-3h1v-1z" class="E"></path><path d="M258 265h1c1 2 1 5 2 7l-1 1c-1-3-2-5-3-7l1-1zm-3 3l3 8v1h-1l-1-2-1 1-1-4v-1h0c0-1 1-2 1-3z" class="L"></path><path d="M254 271c1 0 1 1 2 2v2l-1 1-1-4v-1h0zm7 1l3 6-1 1v1h-1v-1l-1-1c0-1-1-2-1-3h1l-1-2 1-1z" class="O"></path><path d="M261 275l2 4v1h-1v-1l-1-1c0-1-1-2-1-3h1z" class="K"></path><path d="M255 276l1-1 1 2h1v-1c1 1 1 3 2 4 1 2 1 3 1 4h0l-1-1c0 1 0 1-1 1h0c-1 0 0-1-1-1v-2c-1-1-2-3-3-5z" class="G"></path><path d="M251 287v-1h1l1 2h2l1-1c1 2 2 4 3 5 0 0 0 1 1 1h0v2 3h0c-1 0-1 0-1 1h0 1l1-1v2l2 1c1 1 1 2 2 3l-1 1-1-1v2l-1 1h0l-1 1h0v3 4l-2 7c-1-4 0-7-3-12h0v-1l-1-4-1-2-3-11v-4-1z" class="X"></path><path d="M254 296c1 3 1 5 1 8 1 2 2 4 1 5l-1-4-1-2v-7z" class="e"></path><path d="M251 292c2 1 2 2 3 4v7l-3-11z" class="p"></path><path d="M257 297l2 3h0c1 0 1 1 1 1l2 6-1 1h0v3 4h0c-1 0-1-1-1-1-1-1-2-4-2-6-1-4-1-7-1-11z" class="Q"></path><path d="M259 300h0c1 0 1 1 1 1l2 6-1 1h0c-1-2-2-5-2-8z" class="c"></path><path d="M258 308c0-1 0-1 1-2 1 2 1 4 1 5l1 1v-1h0v4h0c-1 0-1-1-1-1-1-1-2-4-2-6z" class="I"></path><path d="M256 287c1 2 2 4 3 5 0 0 0 1 1 1h0v2 3h0c-1 0-1 0-1 1h0 1l1-1v2l2 1c1 1 1 2 2 3l-1 1-1-1v2l-1 1h0l-2-6s0-1-1-1h0l-2-3-4-9h2l1-1z" class="D"></path><path d="M261 300l2 1c1 1 1 2 2 3l-1 1-1-1c-1-1-1-3-2-4z" class="b"></path><path d="M596 457l1 1v8c1 1 3 1 4 3 1 1 1 1 0 2v1l-1-1c1-1 1-1 0-3-1 0-2 0-3-1-1 22-4 43-14 62-3 7-8 14-13 21-4 5-7 11-11 15l-4 4c4-2 10-5 14-8 2-2 4-5 6-7h0c0 2-4 4-5 6 12-7 19-19 22-33v-1-1c1-1 1-4 1-5 1 0 2-1 2-1l1-2v1 1 1c-1 1-1 4-2 5l-2 10c-1 2-2 5-3 7 0 2-1 4-2 6s-3 4-4 7c-8 9-20 16-31 20l-10 3h2l1 1h0-1l-1 1c0 1 0 1 1 1-1 1-1 1-1 2l-6-2h-2l-1 1h1-5-10-8-44 0c10 0 19 1 28 6 3 2 6 5 9 8 5 5 7 11 8 17-1 1-2 3-2 4v2h-1c0-1 0-1 1-1v-1h-1l-1 1c-2 1-5 0-7-1-3-1-3-2-4-5-1-1-1-3-1-5v-3c-1 1-1 0-1 1h0v-5c-4 2-9 3-13 4v2c1 1 3 5 3 7 3 1 7 3 7 6 1 2 1 5 0 7-2 1-3 3-4 5l-1-1v-1c-1 0-1 1-1 1v3h0c-1-4-5-6-7-9-2-5-3-12-4-18l-29 4c-2 0-4 1-6 1h-9-38l1 6h0l-2 2c-3 4-10 10-15 12v-1l2-1c3-1 5-4 8-6 2-1 4-3 5-6 1-2 1-4 0-5s-5-1-6-1l-19-1c-2 0-5 0-7-1h-5-7v-1l24-1h-8-2c1 0 2-1 3-1h0l4-1h0-8v-1c1 0 2 0 3-1l17-3c-1 0-1-1-1-1 1-1 4-2 5-3 3-2 6-4 8-6l1-1c2-2 4-4 5-6 2-2 3-5 4-8 0-1 1-2 2-2h2c0 1 0 1-1 2 1 1 1 1 1 2 1-1 1-2 2-3 0-1 1-2 1-2v-1l-1 1-1-1 1-1c1 0 1-1 2-2 1 0 3-2 4-3s2-2 2-3h1l1 1 1-1h1 1l-1-1v-1l4-4c1-1 1-2 2-3l3-5 1-2v-2-1c0-2 2-4 3-5h1 0c1-2 1-3 2-5v1c1-1 2-3 2-5l2-4c0-2 0-3 1-5 1-4 1-8 0-11v-1l2 2c2 1 2-1 2 1 0 5-1 9-3 13l-1 2c0 1 1 2 1 2v1c0 1-1 1-1 2 1 0 2-1 2-1h1l6-6c2-3 4-5 8-6 1 0 2 0 3 1v1l1-1 2-5c2-3 5-5 9-6h1c3 0 5 1 7 3 0-2-1-4 0-7h0 1v2-2h1l1-1 1 1c2-4 3-5 7-6 2-1 5-1 6-2l1-1c1 1 2 1 3 1v2c5 0 11 3 14 7 5 5 6 14 6 20-1 2-1 3-2 5l8-8 7-11c0-1 2-3 2-4 3-8 4-16 2-24l1-2c2 5 3 10 2 15h2c0-1 0-1 1-1v1h3c2 2 2 4 3 7 1 2 0 4 0 7l11-24 3-2c0-1 1-2 1-2v-1c1-1 1-1 1-2 1 0 1 0 1-1 4-6 11-12 17-18 1 0 2 0 3-1 0 1-1 2-1 3l-3 2-3 3h0v1l13-11z"></path><path d="M537 553v1 2s0 1-1 2v-2c0-1 0-2 1-3z" class="N"></path><path d="M513 571l3 1c-1 0-2 1-4 1h0-1l2-2z" class="g"></path><path d="M552 529h0c1 1 1 1 1 2-1 1-1 1-3 1 0 0-1 0-1-1 1-1 1-2 3-2z" class="n"></path><path d="M523 561c1-2 2-5 3-8h1c1 2-2 6-3 8h-1z" class="L"></path><path d="M579 482h1 0c-3 3-7 4-11 6v1h0v-1c1-2 2-4 4-5 1-1 4-1 6-1z" class="o"></path><path d="M521 562c1 0 2 0 2-1h1c-1 4-4 6-6 9l-2 2-3-1 2-1c3-3 4-5 6-8z" class="B"></path><path d="M515 570h3l-2 2-3-1 2-1z" class="w"></path><path d="M596 457l1 1c-3 2-5 5-8 7l-10 8c-2 1-3 3-5 5v-1c0-1 4-4 5-5l1-1c-1 0-1 0-1-1 1-1 2-2 4-3v1l13-11z" class="M"></path><path d="M514 554c-1-1-3-1-4-1 0-1 1-2 2-2 2-1 4-1 6 0 1 0 3 2 3 3 1 2 1 4 1 6l-1 1v-2 1h0l-1-1c-1-1-1-2-1-2-1-1-3-2-4-2l-1-1z" class="N"></path><path d="M570 489h2c2 1 3 2 3 4s0 3-1 4c0 1-2 2-3 2-2 0-3-1-4-3v-1h-1c-1 1-1 2-2 3h0c0-2 1-3 2-4 1-2 2-3 4-5z" class="n"></path><path d="M537 553c2-1 2-1 3-1 1 1 2 1 2 2 2 1 3 3 2 5 0 5-4 9-7 11-1 1-1 1-2 1v1l-1-1-1 1h-3c-1 1 0 1-1 1h-1-1c2-1 4-1 4-3h1c2-3 5-6 6-9 0-1-1-2-2-3 1-1 1-2 1-2v-2-1h0z" class="y"></path><path d="M537 553c2-1 2-1 3-1 1 1 2 1 2 2h-1c0 1 1 1 1 2h0-1c-1-1-1-1-2-1v-2h-2z" class="M"></path><path d="M446 568l12-3 3 2-36 12v-1c0-1 0-1 1-2 2-2 5-2 8-3l12-5zm-16 18h6c4 1 9 3 11 7 2 2 3 5 2 7-1 3-2 7-5 9-2 1-3 1-5 1-1-1-3-2-3-3v-3c1-1 2-1 3-1 2 0 3 2 4 4l2-2c1-2 2-4 2-6s-1-4-3-5c-6-6-16-5-23-4l-1-1 1-2c2-1 7-1 9-1z" class="q"></path><path d="M421 570h2c1 1 2 2 2 4 0 1-1 3-1 4-1 4-2 7-4 10-3 7-10 11-17 14l-2 1c-2 1-4 2-7 2-5 1-10 2-15 2-2 0-4 1-6 0h-8-2c1 0 2-1 3-1h0l4-1h0-8v-1c1 0 2 0 3-1l17-3h2l1-1c3-1 6-3 9-5l1 1h1c1-1 1-1 2-1v1c4-4 8-9 11-13 2-4 3-6 6-9l2-1h0l2-1c0-1 2-1 2-1z" class="n"></path><path d="M404 594h0c2-1 3-1 5-2l-2 2s0-1-1 0c0 0-1 1-2 1v-1z" class="N"></path><path d="M396 603c5-2 9-4 13-7h1l1-1 4-4 3-3c-1 1-1 1-1 2s-2 2-3 3h0c-2 2-3 3-5 4 0 1-1 1-1 1-1 0 0 0-1 1s-3 1-4 2v1l-2 1c-2 1-4 2-7 2l2-2z" class="f"></path><path d="M403 601v1l-2 1v-1s1-1 2-1z" class="e"></path><path d="M419 585h1c-1 1-1 1-1 2l1 1c-3 7-10 11-17 14v-1c1-1 3-1 4-2s0-1 1-1c0 0 1 0 1-1 2-1 3-2 5-4h0c1-1 3-2 3-3s0-1 1-2c0-1 0-1 1-2v-1z" class="X"></path><path d="M421 570h2c1 1 2 2 2 4 0 1-1 3-1 4-1 4-2 7-4 10l-1-1c0-1 0-1 1-2h-1c0-1 0-2 1-3 0-1 0-2 1-3 0-1 1-1 1-2-1-1-1-2-1-3h0-2 0v-3h0c0-1 2-1 2-1z" class="N"></path><path d="M419 571c0-1 2-1 2-1 0 1-1 2 0 3s2 1 2 3l-1 1c-1-1-1-2-1-3h0-2 0v-3h0z" class="a"></path><path d="M419 571h0v3h0l1 1c-1 1-1 2-2 4l-1 2c-1 2-2 4-3 5l-3 3c0 1-1 2-2 3-2 1-3 1-5 2h0 0c0-1 1-1 1-1l1-1c1-1 2-2 3-4h0l2-2c0-1 0-2 1-3h0l1-2h0c1-2 3-4 4-6 0-1 0-1-1-2h-1l2-1h0l2-1z" class="o"></path><path d="M417 572h1v2l-1 1c0-1 0-1-1-2h-1l2-1z" class="N"></path><defs><linearGradient id="f" x1="360.892" y1="596.343" x2="418.959" y2="586.029" xlink:href="#B"><stop offset="0" stop-color="#d2d1d2"></stop><stop offset="1" stop-color="#fbf9fa"></stop></linearGradient></defs><path fill="url(#f)" d="M415 573h1c1 1 1 1 1 2-1 2-3 4-4 6h0l-1 2h0c-1 1-1 2-1 3l-2 2h0c-1 2-2 3-3 4l-1 1s-1 0-1 1h0v1c-1 0-1 1-2 1l-2 2h-1l-3 2-1 1h0-1c-2 3-5 3-8 4 1 0 1 1 2 0 2 0 4-1 6-1h0l-1-1h0 3l-2 2c-5 1-10 2-15 2-2 0-4 1-6 0h-8-2c1 0 2-1 3-1h0l4-1h0-8v-1c1 0 2 0 3-1l17-3h2l1-1c3-1 6-3 9-5l1 1h1c1-1 1-1 2-1v1c4-4 8-9 11-13 2-4 3-6 6-9z"></path><path d="M370 605c4-1 8-1 11-1h1c-2 1-5 0-7 1-1 1-4 0-5 0h0z" class="M"></path><path d="M394 594l1 1h1c1-1 1-1 2-1v1l-9 4h0c-1 0-2 1-3 1h-2l1-1c3-1 6-3 9-5z" class="e"></path><path d="M592 526v-1c1-1 1-4 1-5 1 0 2-1 2-1l1-2v1 1 1c-1 1-1 4-2 5l-2 10c-1 2-2 5-3 7 0 2-1 4-2 6s-3 4-4 7c-8 9-20 16-31 20l-10 3h2l1 1h0-1l-1 1c0 1 0 1 1 1-1 1-1 1-1 2l-6-2h-2l-1 1h1-5-10-8-44 0c10 0 19 1 28 6 3 2 6 5 9 8 5 5 7 11 8 17-1 1-2 3-2 4v2h-1c0-1 0-1 1-1v-1h-1l-1 1c-2 1-5 0-7-1-3-1-3-2-4-5-1-1-1-3-1-5v-3c-1 1-1 0-1 1h0v-5c-4 2-9 3-13 4v2c1 1 3 5 3 7 3 1 7 3 7 6 1 2 1 5 0 7-2 1-3 3-4 5l-1-1v-1c-1 0-1 1-1 1-2-2-5-5-6-8-5-11-2-25-13-33-10-8-34-5-46-4 1-1 2-3 3-3 0-1 2-1 3-2 8-3 17-3 25-3 15 0 32 2 47-3 5-2 9-5 13-8 1-1 2-3 3-4 0-1 0-3-1-4 0-1-1-2-2-2s-2 0-3 1v1h0l1-1h2c1 0 1 1 2 2v3c-1 1-2 1-3 1s-2-1-3-2-2-2-2-3c0-2 1-3 2-4 2 0 3-1 5 0l1 1c1 0 3 1 4 2 0 0 0 1 1 2l1 1h0v-1 2 1c-2 3-3 5-6 8l-2 1-2 2c-5 2-10 4-16 6-2 1-5 1-6 2 5 0 10-1 15-2l16-3 12-6h-1c0 2-2 2-4 3h1 1c1 0 0 0 1-1h3l1-1 1 1v-1c8-3 14-6 19-12 2-2 4-5 6-7l1 1c-2 7-10 12-15 16v1c-1 1-5 3-6 4 1 0 2-1 3-1h1l1-1h1 0c1 0 2-1 3-1 1-1 2-1 3-1 0 0 2-1 3-1 2-1 4-2 7-3 10-5 20-12 25-23 2-4 3-8 5-12v-5z" class="n"></path><path d="M512 582h1c1 0 1 0 2-1l5-1v2h-8z" class="a"></path><path d="M520 580h8c1-1 2-1 4-1-1 1-2 1-3 1l1 1h-2 0l2 1h-10v-2z" class="Q"></path><path d="M520 576l12-6h-1c0 2-2 2-4 3h1 1c1 0 0 0 1-1h3l1-1 1 1c-2 1-3 1-5 2s-5 2-7 2h-3z" class="M"></path><path d="M552 570c-2 2-5 3-8 4-5 1-10 3-16 3 1-1 5-1 6-2 4-1 8-4 12-6v1c-1 1-5 3-6 4 1 0 2-1 3-1h1l1-1h1 0c1 0 2-1 3-1 1-1 2-1 3-1z" class="AA"></path><path d="M540 578h2 2l1 1h0-1l-1 1c0 1 0 1 1 1-1 1-1 1-1 2l-6-2h-2l-1 1h1-5l-2-1h0 2l-1-1c1 0 2 0 3-1 2 1 6 0 8-1z" class="F"></path><path d="M540 578h2 2l1 1h0-1-3s0-1-1-1z" class="H"></path><path d="M530 581c2-1 5-1 7 0h-2l-1 1h1-5l-2-1h0 2z" class="p"></path><path d="M480 603l3 1v2c1 1 3 5 3 7v1h-1c0 1 1 3 2 4l1-1c1 1 1 1 1 2v3l-1 1h0v4h-1c-1-1-1-2-2-3h-1c0-2 1-3 1-4l2-1c-1-1-1-1-2-1l-3-7c-1-3-2-5-2-8z" class="S"></path><path d="M485 624c0-2 1-3 2-5h0c1 1 0 2 0 2 0 1 1 2 1 2h0v4h-1c-1-1-1-2-2-3z" class="N"></path><path d="M483 606c1 1 3 5 3 7v1h-1l-3-6c0-1 1-1 1-2z" class="H"></path><path d="M486 614v-1c3 1 7 3 7 6 1 2 1 5 0 7-2 1-3 3-4 5l-1-1v-1l-1-1c-2-1-3-2-3-4h1c1 1 1 2 2 3h1v-4h0l1-1v-3c0-1 0-1-1-2l-1 1c-1-1-2-3-2-4h1z" class="j"></path><path d="M486 614c0 1 0 2 1 2 1 1 2 1 3 1s2 1 2 2 1 2 1 3h-2l-1-1v-2h-1c0-1 0-1-1-2l-1 1c-1-1-2-3-2-4h1z" class="a"></path><path d="M489 619h1v2l1 1h2c-1 3-2 4-4 6v-1h0v-3l-1-1h0l1-1v-3z" class="X"></path><path d="M494 593c8 6 11 14 14 23-4-1-7-2-10-5v1c-1-1-1-3-1-5v-3c-1 1-1 0-1 1h0v-5s1 0 1-1-2-3-3-4v-2z" class="S"></path><path d="M500 611c0-1-1-3 0-3l2 1 2 5c-1 0-3-1-4-2v-1z" class="M"></path><path d="M497 599l2 2c1 1 2 3 2 4h0c1 2 1 3 1 4l-2-1c-1 0 0 2 0 3l-1-1h0l-1 1v1c-1-1-1-3-1-5v-3c-1 1-1 0-1 1h0v-5s1 0 1-1z" class="g"></path><path d="M499 610c0-2 0-4 1-5h1c1 2 1 3 1 4l-2-1c-1 0 0 2 0 3l-1-1z" class="t"></path><path d="M497 607v-2l1-3h0c1 0 1 1 1 2-1 2-1 4 0 6l-1 1v1c-1-1-1-3-1-5z" class="a"></path><path d="M480 603h0c-2-8-8-15-14-18 10 1 21 1 28 8v2c1 1 3 3 3 4s-1 1-1 1c-4 2-9 3-13 4l-3-1z"></path><path d="M587 460c1 0 2 0 3-1 0 1-1 2-1 3l-3 2-3 3h0c-2 1-3 2-4 3 0 1 0 1 1 1l-1 1c-1 1-5 4-5 5v1c-5 5-8 12-11 18-2 3-3 6-5 8 3-2 6-4 8-7h0c1 1 3 3 4 3 4 2 12 2 12 8-1 2-1 3-2 4s-2 1-3 1-2-1-3-2c-1 0 0 0 0-1s1-1 2-2c0-1 0-1-1-2-1 0-3-1-4-1-3 8-7 14-15 18-3 1-6 2-10 3 5-4 12-7 16-12 3-3 5-7 8-10-3 1-6 1-8 3-4 2-7 6-10 9-2 3-5 6-7 8-5 5-11 8-16 12-3 2-6 3-8 5-4 2-8 5-12 7-10 5-21 9-31 13-2 1-4 1-6 2-3 1-6 4-9 3l-5-3-13 4 1 1-12 5c-3 1-6 1-8 3-1 1-1 1-1 2v1h0l-1-1c0-1 1-3 1-4 0-2-1-3-2-4h-2s-2 0-2 1l-2 1h0l-2 1c-3 3-4 5-6 9-3 4-7 9-11 13v-1c-1 0-1 0-2 1h-1l-1-1c-3 2-6 4-9 5l-1 1h-2c-1 0-1-1-1-1 1-1 4-2 5-3 3-2 6-4 8-6l1-1c2-2 4-4 5-6 2-2 3-5 4-8 0-1 1-2 2-2h2c0 1 0 1-1 2 1 1 1 1 1 2 1-1 1-2 2-3 0-1 1-2 1-2v-1l-1 1-1-1 1-1c1 0 1-1 2-2 1 0 3-2 4-3s2-2 2-3h1l1 1 1-1h1 1l-1-1v-1l4-4c1-1 1-2 2-3l3-5 1-2v-2-1c0-2 2-4 3-5h1 0c1-2 1-3 2-5v1c1-1 2-3 2-5l2-4c0-2 0-3 1-5 1-4 1-8 0-11v-1l2 2c2 1 2-1 2 1 0 5-1 9-3 13l-1 2c0 1 1 2 1 2v1c0 1-1 1-1 2 1 0 2-1 2-1h1l6-6c2-3 4-5 8-6 1 0 2 0 3 1v1l1-1 2-5c2-3 5-5 9-6h1c3 0 5 1 7 3 0-2-1-4 0-7h0 1v2-2h1l1-1 1 1c2-4 3-5 7-6 2-1 5-1 6-2l1-1c1 1 2 1 3 1v2c5 0 11 3 14 7 5 5 6 14 6 20-1 2-1 3-2 5l8-8 7-11c0-1 2-3 2-4 3-8 4-16 2-24l1-2c2 5 3 10 2 15h2c0-1 0-1 1-1v1h3c2 2 2 4 3 7 1 2 0 4 0 7l11-24 3-2c0-1 1-2 1-2v-1c1-1 1-1 1-2 1 0 1 0 1-1 4-6 11-12 17-18z" class="n"></path><path d="M475 533v-1c-1 0-1-1-1-1 0-1-1-2-1-3s-1-2-2-3v-1c0-1 0-1-1-2 0-1 0-2 1-3 1 1 1 1 2 1l3 5h-1l1 1h0l-1 1c1 1 1 1 1 2 0 0 0 1 1 2v1 1h0-2z" class="M"></path><path d="M501 495c1 1 2 1 3 1v2c-5-1-10 0-14 2-2 2-3 5-4 8 0 3 1 6 3 8s3 2 5 3h0l-1 1c-2-1-4-2-6-5 0 0 0-1-1-1h0c-2-2-2-6-2-8v-2h1l1-1 1 1c2-4 3-5 7-6 2-1 5-1 6-2l1-1z" class="d"></path><path d="M482 519l-1-3 3 3h0l1 1h1l-1-3h1c1 7 1 15-1 22-1 1-2 2-2 4-1 2-2 4-3 5h0-1c0-1 2-3 2-4 2-4 2-8 3-12-1-4 0-7-1-11-1 0-1-1-1-1v-1z" class="J"></path><path d="M482 519l-1-3 3 3c0 1 1 1 1 2 1 3 0 8-1 11-1-4 0-7-1-11-1 0-1-1-1-1v-1z" class="Q"></path><path d="M461 544h3c1 1 2 1 2 3s-2 4-3 6h0c1-1 1-2 1-3 0-2 1-3 1-4l-2-1h0c-3 3-4 6-8 9s-9 5-14 8l-17 8h1c1 2 1 3 1 4l19-7 1 1-12 5c-3 1-6 1-8 3-1 1-1 1-1 2v1h0l-1-1c0-1 1-3 1-4 0-2-1-3-2-4h-2s-2 0-2 1l-2 1h-1l1-1c1-1 3-2 4-2 4-1 7-3 11-4 8-4 18-8 24-14 3-2 4-4 5-7z" class="v"></path><path d="M536 517c4-5 6-11 12-14h1c0 4-1 8-4 12s-7 7-12 11l-10 8c-2 1-4 3-7 4l12-9c0-1 2-2 3-3v-2h0s0-1-1-1v-1l7-11c0 1 0 2-1 3v1l-1 1h0c1 1 1 0 1 1z"></path><path d="M536 517l-5 5c2-1 3-2 5-3 3-3 6-7 9-10-2 7-9 11-14 17v-2h0s0-1-1-1v-1l7-11c0 1 0 2-1 3v1l-1 1h0c1 1 1 0 1 1z" class="y"></path><path d="M493 507c1 0 1-1 1-2h1c0-1 1-2 2-3 2-1 2-1 3-1 5 0 8 1 12 4 4 4 7 10 8 15 0 7-2 13-6 17-2 2-4 4-7 5-8 6-17 9-26 13-2 1-5 2-7 3l-4 4h0l2-4 7-10h1c-2 4-5 6-6 10l4-4c2-4 4-8 6-11 3-4 6-8 9-13 3-6 6-12 10-18-1 1-4 2-5 2s-3 0-4-1c-1-2-1-4-1-6z"></path><path d="M497 512l1-1c-1-1-2-1-2-2h0c1-1 2-1 2-2 1 0 2-1 3-1 0 0 1 0 2-1h0v3h-1s-1 0-1 1l-2 2h0l-1 1h-1z" class="X"></path><path d="M493 507c1 0 1-1 1-2h1c0-1 1-2 2-3 2-1 2-1 3-1l3 1v2l-1-1h-2c-2 0-4 2-4 4h0c2 0 2-4 6-3v1h-2l-3 3c-1 0-1-1-2 1v1c1 0 1 1 2 2h1l1-1h0l2-2c0-1 1-1 1-1h1c0 1-1 2-1 3-1 1-1 2-2 2h-1-1c-2 0-3 0-4-1-1-2 0-3-1-5z" class="N"></path><path d="M483 504h1v2c0 2 0 6 2 8v3h-1l1 3h-1l-1-1h0l-3-3 1 3v1s0 1 1 1c1 4 0 7 1 11-1 4-1 8-3 12 0 1-2 3-2 4l-7 10-1-1-2 3c-1 0-1-1-2-2h0c1-1 3-2 4-3 6-5 8-14 8-21h-1c0 1-1 0-1 0l-2-1h2 0v-1-1c-1-1-1-2-1-2 0-1 0-1-1-2l1-1h0l-1-1h1l1-1v-4l-3-3c-2 0-7 0-9 2 0 1 0 1-1 2 0 2 0 2-1 3-1 0-2-1-3-2v-1c-1 0-1-1-2-1-5 3-10 7-12 13 3-2 7-4 11-3 2 0 4 1 5 3v3c-1 1-1 2-2 2v2 1 3h1c-1 3-2 5-5 7-6 6-16 10-24 14-4 1-7 3-11 4-1 0-3 1-4 2l-1 1h1 0l-2 1c-3 3-4 5-6 9-3 4-7 9-11 13v-1c-1 0-1 0-2 1h-1l-1-1c-3 2-6 4-9 5l-1 1h-2c-1 0-1-1-1-1 1-1 4-2 5-3 3-2 6-4 8-6l1-1c2-2 4-4 5-6 2-2 3-5 4-8 0-1 1-2 2-2h2c0 1 0 1-1 2 1 1 1 1 1 2 1-1 1-2 2-3 0-1 1-2 1-2v-1l-1 1-1-1 1-1c1 0 1-1 2-2 1 0 3-2 4-3s2-2 2-3h1l1 1 1-1h1 1l-1-1v-1l4-4c1-1 1-2 2-3l3-5 1-2v-2-1c0-2 2-4 3-5h1 0c1-2 1-3 2-5v1c1-1 2-3 2-5l2-4c0-2 0-3 1-5 1-4 1-8 0-11v-1l2 2c2 1 2-1 2 1 0 5-1 9-3 13l-1 2c0 1 1 2 1 2v1c0 1-1 1-1 2 1 0 2-1 2-1h1l6-6c2-3 4-5 8-6 1 0 2 0 3 1v1l1-1 2-5c2-3 5-5 9-6h1c3 0 5 1 7 3 0-2-1-4 0-7h0z" class="i"></path><path d="M437 538s1 1 2 1c-1 1-1 2-2 3l-1-2 1-2z" class="a"></path><path d="M439 535c0 1 1 1 1 2l-1 2c-1 0-2-1-2-1v-1c1-1 1-1 2-1v-1z" class="e"></path><path d="M472 515c3 0 4 0 7 1-1 1-2 1-2 1-1 0-2 0-2-1h-3v-1z" class="a"></path><path d="M477 520l2 6-1 1s-1 0-1-1h-1 0l-1-1h1l1-1v-4z" class="o"></path><path d="M458 520c1-1 2-1 3-1h0c1 1 1 2 2 2v2h0l-2-2-1 1v-1c-1 0-1-1-2-1z" class="X"></path><path d="M458 534c1 1 1 1 2 3v1h0v2c-2 0-3-3-3-4s0-2 1-2z" class="N"></path><path d="M455 550l1-1c3-2 3-4 4-8v3h1c-1 3-2 5-5 7 0 0 0-1 1-1v-1l-2 1z" class="M"></path><path d="M432 546c1-2 2-4 4-6l1 2c-1 1-3 4-3 5l-1 2h-1l-1-1 1-2z" class="o"></path><path d="M431 548l1 1h1c-4 4-7 8-11 12v-1l4-4c1-1 1-2 2-3l3-5z" class="X"></path><path d="M439 535c2-2 3-6 4-9 0 1 1 2 1 2v1c0 1-1 1-1 2 1 0 2-1 2-1h1l-2 2h-1l-3 5c0-1-1-1-1-2z" class="a"></path><path d="M475 516c-4 0-6 0-9 1 1-1 3-3 5-4h6 1l1 1c1 0 1 1 1 2 0-1-1-1-2-2h0-7v1h1v1h3z" class="I"></path><path d="M476 526h0 1c0 1 1 1 1 1l1-1v8h-1c0 1-1 0-1 0l-2-1h2 0v-1-1c-1-1-1-2-1-2 0-1 0-1-1-2l1-1h0zm-4-17c2 0 4-1 7 0h0c3 2 5 3 6 6v2l1 3h-1l-1-1v-1c-1-2-4-6-6-6-1-1-2 0-3 0h-4c-1 0-3 2-4 3h-1v-1c2-2 4-3 6-5z" class="a"></path><path d="M472 509c2 0 4-1 7 0h0c0 1-1 1-1 2-1 0-1-1-2-1h-3l-1-1z" class="o"></path><path d="M457 536l-2-1h-2c-3 0-4 2-7 3-2 1-4 3-6 4h-1c-1 2-2 3-3 5 0-2 3-7 5-8h1 0 1l-1-1c1-2 5-5 7-6s6-2 9-1l3 3v2h-1 0c0-2-2-3-3-4h-3c1 1 1 0 2 0 1 1 2 1 2 2-1 0-1 1-1 2z" class="X"></path><path d="M443 509v-1l2 2c2 1 2-1 2 1 0 5-1 9-3 13l-1 2c-1 3-2 7-4 9v1c-1 0-1 0-2 1v1l-1 2c-2 2-3 4-4 6v-2-1c0-2 2-4 3-5h1 0c1-2 1-3 2-5v1c1-1 2-3 2-5l2-4c0-2 0-3 1-5 1-4 1-8 0-11z" class="D"></path><path d="M479 516l3 3v1s0 1 1 1c1 4 0 7 1 11-1 4-1 8-3 12 0 1-2 3-2 4l-7 10-1-1c0-1 2-3 3-4 3-3 6-10 6-15 1-7 1-15-3-21 0 0 1 0 2-1z" class="y"></path><path d="M454 542h0c2 1 3 2 4 3-1 3-3 4-6 5l-2 1h-1c-1 2-4 3-6 4s-4 3-6 5c-1 0-2 1-3 2 3-1 5-3 7-5 5-3 9-5 14-7l2-1v1c-1 0-1 1-1 1-6 6-16 10-24 14-4 1-7 3-11 4-1 0-3 1-4 2-1-1 0-1-1-1 1-1 2-2 3-2 0-1 1-1 2-2l4-3 1-1h1l1-1 6-6h0l2-2c2-2 4-4 7-6h0c1-1 2-2 3-2l1-1c1 0 1 0 1-1h2c0-1 1-1 1-1h3z" class="q"></path><path d="M446 546l1 1c1 0 2-1 3 0h-1c1 1 1 1 1 2h1l-1 1v1h-1-1s-1 0-2-1h0c-2 0-2-1-3-2 1-1 2-1 3-2z" class="AD"></path><path d="M454 542h0c2 1 3 2 4 3-1 3-3 4-6 5l-2 1v-1l1-1h-1c0-1 0-1-1-2h1c-1-1-2 0-3 0l-1-1 1-1c1 0 2 0 2-1 1 0 2-1 3-1l2 1v1h1v-1-1c-1 0-1 0-1-1z" class="o"></path><path d="M423 562c3-3 6-6 8-9 1-3 3-4 5-6 2-1 3-3 5-5 2-1 4-3 7-4 1-1 3-2 4-2 2 0 3 0 4 2 2 1 2 4 2 7-1-1-2-2-4-3h0-3s-1 0-1 1h-2c0 1 0 1-1 1l-1 1c-1 0-2 1-3 2h0c-3 2-5 4-7 6l-2 2h0l-6 6-1 1h-1l-1 1-4 3c-1 1-2 1-2 2-1 0-2 1-3 2 1 0 0 0 1 1l-1 1h1 0l-2 1c-3 3-4 5-6 9-3 4-7 9-11 13v-1c-1 0-1 0-2 1h-1l-1-1c-3 2-6 4-9 5l-1 1h-2c-1 0-1-1-1-1 1-1 4-2 5-3 3-2 6-4 8-6l1-1c2-2 4-4 5-6 2-2 3-5 4-8 0-1 1-2 2-2h2c0 1 0 1-1 2 1 1 1 1 1 2 1-1 1-2 2-3 0-1 1-2 1-2v-1l-1 1-1-1 1-1c1 0 1-1 2-2 1 0 3-2 4-3s2-2 2-3h1l1 1 1-1h1 1z" class="N"></path><path d="M420 563c-2 2-4 3-4 5 1 0 1-1 3-2l2-2c1 0 1 0 2-1l2-1 1-1a57.31 57.31 0 0 0 11-11c1 0 2-2 3-2l5-4h1c0-1 1-1 1-1 1-1 2-2 4-2v1s-1 0-1 1h-2c0 1 0 1-1 1l-1 1c-1 0-2 1-3 2h0c-3 2-5 4-7 6l-2 2h0l-6 6-1 1h-1l-1 1-4 3c-1 1-2 1-2 2-1 0-2 1-3 2 1 0 0 0 1 1l-1 1h1 0l-2 1c-3 3-4 5-6 9-3 4-7 9-11 13v-1c2-2 3-4 5-6 0-1 1-2 2-3 0-1 1-2 2-3l-1-1c2-1 4-5 4-7 0-1 1-2 1-2v-1l-1 1-1-1 1-1c1 0 1-1 2-2 1 0 3-2 4-3s2-2 2-3h1l1 1z" class="V"></path><defs><linearGradient id="g" x1="391.436" y1="598.011" x2="389.81" y2="586.319" xlink:href="#B"><stop offset="0" stop-color="#363736"></stop><stop offset="1" stop-color="#545255"></stop></linearGradient></defs><path fill="url(#g)" d="M406 573h2c0 1 0 1-1 2 1 1 1 1 1 2 1-1 1-2 2-3 0 2-2 6-4 7l1 1c-1 1-2 2-2 3-1 1-2 2-2 3-2 2-3 4-5 6-1 0-1 0-2 1h-1l-1-1c-3 2-6 4-9 5l-1 1h-2c-1 0-1-1-1-1 1-1 4-2 5-3 3-2 6-4 8-6l1-1c2-2 4-4 5-6 2-2 3-5 4-8 0-1 1-2 2-2z"></path><path d="M406 578c0-1 1-1 1-2v-1c1 1 1 1 1 2s-1 2-2 3v-2z" class="Q"></path><path d="M406 573h2c0 1 0 1-1 2v1c0 1-1 1-1 2l-1-1v-2l1-2z" class="p"></path><path d="M405 575v2l1 1v2 1l-3 3v1h-1v-1c2-3 2-6 3-9z" class="K"></path><path d="M406 581l1 1c-1 1-2 2-2 3-1 1-2 2-2 3-2 2-3 4-5 6-1 0-1 0-2 1h-1l-1-1c3-2 5-4 7-7l5-6z" class="X"></path><path d="M276 245v-69-18c0-6 0-11-1-17-3-12-9-24-18-32-12-11-32-14-48-14v-8h165 47c19 0 38 0 56 4 13 2 25 7 36 12 3 2 7 4 11 6 5 3 10 7 14 11 22 22 29 52 29 82-1 9-1 19-3 28-4 20-14 39-29 54-11 12-27 21-42 27l-32 10c29 2 58 9 82 25 9 5 17 11 23 18 9 10 16 21 21 33 5 15 8 31 9 46l1 9-2 1c-2 2-5 5-8 7-6 6-13 12-17 18 0 1 0 1-1 1 0 1 0 1-1 2v1s-1 1-1 2l-3 2-11 24c0-3 1-5 0-7-1-3-1-5-3-7h-3v-1c-1 0-1 0-1 1h-2c1-5 0-10-2-15l-1 2c2 8 1 16-2 24 0 1-2 3-2 4l-7 11-8 8c1-2 1-3 2-5 0-6-1-15-6-20-3-4-9-7-14-7v-2c-1 0-2 0-3-1l-1 1c-1 1-4 1-6 2-4 1-5 2-7 6l-1-1-1 1h-1v2-2h-1 0c-1 3 0 5 0 7-2-2-4-3-7-3h-1c-4 1-7 3-9 6l-2 5-1 1v-1c-1-1-2-1-3-1-4 1-6 3-8 6l-6 6h-1s-1 1-2 1c0-1 1-1 1-2v-1s-1-1-1-2l1-2c2-4 3-8 3-13 0-2 0 0-2-1l-2-2v1c1 3 1 7 0 11-1 2-1 3-1 5l-2 4c0 2-1 4-2 5v-1c-1 2-1 3-2 5h0-1c-1 1-3 3-3 5v1 2l-1 2-3 5c-1 1-1 2-2 3l-4 4v1l1 1h-1-1l-1 1-1-1h-1c0 1-1 2-2 3s-3 3-4 3c-1 1-1 2-2 2l-1 1 1 1 1-1v1s-1 1-1 2c-1 1-1 2-2 3 0-1 0-1-1-2 1-1 1-1 1-2h-2c-1 0-2 1-2 2 0-1-1-1-1-2-1 0-1 1-2 1-1 1-2 1-3 2h-3l4-3h-1v-2c-1 0-1 0-1-1h-1v-1-1l-1 1s-1 0-1-1v-8h-1c0-1 0-2 1-3v-1c-1 1-2 2-3 2 0-2-2-4-2-6l-1-3-2-7v-2c1-1 1-1 2-1v-1-2-1-10-1c-1-1-1-4-1-5h1v1c1 0 1 0 2-1l-1-1s-1 0-1-1c-2-1-4-4-4-6 0-1-1-1-2-2h3l-3-3-1-1v-1c-1 0-1-1-2-1 0-1-1-3-1-4 0-2 0-3-1-5l2-3c1-1 1-1 2-1h-2v-1h-1l-1-1h0l-1-1h-3l1-1c0-1 0-1 1-1h2l3-2v-1-1l-1-1c-1-1-1-1-1-2v-1c-1 0-1-1-2-1 1-2 1-4 1-6h-1c-2 4-4 9-7 13-2 3-5 7-8 9l-1 1c1 1 2 1 3 3h-1c0 1 0 1-1 2l-1-1h1v-2h-1l-6 6c-5 4-9 7-14 10-1 2-2 3-4 4-1 2-4 4-6 5h-4c-2 2-5 5-8 7-2 2-4 3-5 5h0c-1 1-1 1-1 3h1c-2 2-4 3-5 5l-6 6c-6 5-13 10-19 14-2 2-4 4-6 5l-1 3-8 2c-4 1-8 2-12 4v-1l5-1-2-1v-1h1-2c3-2 6-3 9-3 1-1 1-2 1-3l-3-3c-1 0-2 0-2 1h0l1 2h0c-1-1-2-1-3-1s-3 1-4 0l-1 1c1 1 1 1 0 2h0c-1-1-1-1-1-2l-1-1c0-1 0-1-1-2l3-3c1-2 2-4 2-6v-1c1-1 2-3 4-4v-1h0l-2 1c-2 0-2-1-3-2l1-1 3-11 1 1v-1l2-7-1 1h0-1v-2h-1-2c0 1-1 1-2 1-1 1-2 1-3 1v-1c1-1 2-4 2-5l2-2c-1 0-1 0-2-1 1-1 1-2 1-4h-1v-1c1-3 2-5 2-8l1-3v-1h-2v1-1c0-1 1-3 0-4l-1-1v2 2h0c-1 1-1 2-2 3l1-4h-1c1-2-1-4 0-7v-1c0-1 0-2 1-4l1-1 1-2v-1c0-1 1-3 1-4v-2l2-1 1-1-1-1h-1v-2h-2l1-3v-1c0-1 0-2 1-3l2 1v-2-1-2-2-1h1l1-4v-2-2l1 1 1-1v-1l-1-1 2-6h0l-1 1v-3c1-1 1-2 1-3s1-5 2-6l1-6s-1 0-1-1v-1-3-3c0-3-1-4-4-6v-1-2h-1c0-1-1-1-1-2v-1-1h0-1l-1-1v-2l1-1-1-3v-2c1 1 1 1 2 1v-3l-1-1v-2l-1-1h-1c-1-2-3-4-5-6 0 0-2-3-2-4s-1-2-2-4l-3-6-1 1h0l-1-1-1-2-2-3c0-1-1-1-1-2l-1-1 1-2h-1c0-1 1-1 1-2h-1v-2h1v-1c-1-2-1-3-1-5l1-1h1v-3-2-3h0l1-2c0-1 0-1 1-2h-1l1-1h-2 0-3 0l-1-2v-2h0 0l1-1c0 1 0 1 1 1s2 0 3-1h0 0v1h5c1 1 2 1 2 2 1 1 1 2 1 2v2c1 0 1 0 1 1 1 0 1 0 1 1s1 2 1 2v1c1 1 1 3 1 4h1l1-2v-2c0 1 0 1 1 1v-4l1-3v-1l1-5h-1c0-2 1-6 1-8 0-1 1-2 1-3l2-7c0-2-1-3-1-5l1-1v-2l1 1 1-1 1-4c0-1 1-1 1-2h0l1-3h0c1-1 1-2 1-3l1-2v-1l2-3v-1c1 0 1-1 2-2s2-1 2-2c-1-1-2-6-3-8l1-2v-2-1-1l-1-2c0-1-1-2-2-2v-3c1 0 2-1 2-2v-2-1h0 1l1-1-1-2h1c0-2 0-2 1-3-1-1-1-2-1-3 1 0 1 0 2-1h-1z"></path><path d="M558 454l1 1v2c-1-1-1-1-1-2v-1z" class="y"></path><path d="M275 536v3l-2-1 2-2z" class="S"></path><path d="M337 304v3 1l-2-3 2-1z" class="n"></path><path d="M357 325h1c0 1-1 4-1 5l-1-1h0c0-2 0-3 1-4z" class="P"></path><path d="M356 329l1 1-2 3h0l-1-1c0-1 1-2 2-3z" class="M"></path><path d="M349 416h0l2 3-2 1-2-3h2v-1z" class="X"></path><path d="M575 438h1v3c-1 1-1 3-2 5v-1c1-2 1-5 1-7z" class="AB"></path><path d="M325 241h1c1 1 1 1 1 2l-1 1h-1c-1-1-1-2 0-3z" class="q"></path><path d="M279 296c2 0 3 0 4 1 1 0 2 0 2 1h-4v1l-1-1 1-1-2-1z" class="G"></path><path d="M316 290c1-1 1-1 2-1s1 0 2 1l-1 2s0 1-1 2v-1-2c-1-1-1-1-2-1z" class="e"></path><path d="M290 288h0c1 0 3 0 4-1 0 1 1 1 1 2-1 0-2 1-3 1-1-1-2 0-4-2h1 1z" class="d"></path><path d="M351 419c0 3 1 5 1 7l-3-6 2-1z" class="M"></path><path d="M563 473c1-1 1-1 2 0l-3 5c0-1 0-1-1-1l1-2 1-2z" class="a"></path><path d="M316 290c1 0 1 0 2 1v2 1 1c-1 0-2-1-2-1l-1-2c1-1 1-2 1-2z" class="M"></path><path d="M274 268l2-1v3 4l-2-3v-2-1z" class="Y"></path><path d="M274 268l2-1v3-1h-1v1l-1 1v-2-1z" class="w"></path><path d="M278 563h-1l-1 1h0c-1-1-2-2-2-3 1 0 2-1 3-1l1 3z" class="Q"></path><path d="M284 296l3-1 2 2c-1 0-2 0-3 1h-1c0-1-1-1-2-1l1-1z" class="j"></path><path d="M274 252h1c0-2 0-2 1-3v10c0-1 0-2-1-3v-2l-1-2z" class="i"></path><path d="M271 497c1 0 2 0 3-1 0-1 1-1 2-1h0v2h0c-1 0-2 1-3 2h0l-1-1h0c0-1-1-1-1-1z" class="b"></path><path d="M302 340v-4-6c0 3 1 5 1 8 0 1 1 2 1 3l-1 1-1-1v-1z" class="p"></path><path d="M326 289l4 9-7-9c2 1 2 1 3 2v-1-1z" class="y"></path><path d="M558 455c0 1 0 1 1 2 0 3-1 6-2 8 0-3 0-7 1-10z" class="M"></path><path d="M272 498h0l1 1h0c1-1 2-2 3-2h0 0v3h0l-1 2c-1 0-1 0-1-1-1 0-1-1-1-1h-1l-1-1 1-1z" class="T"></path><path d="M294 287c3-1 6-1 9-2v1c-1 1-5 2-6 2l-2 1c0-1-1-1-1-2z" class="K"></path><path d="M271 489c1-2 2-5 5-6v1l-1 1-2 2v1c-1 1-1 3-2 4v1c-1-1-1-1-1-2 0 0 1-1 1-2h0z" class="b"></path><path d="M273 466l2-2 1 1c0 1-1 2-1 4l-1 1v-1h-3c1-1 1-2 2-3z" class="O"></path><path d="M339 344c1 2 2 5 3 7l1 1h0l-1-1c-1 1-1 1-1 2l-3-8c1 1 1 1 2 3v-1c-1-1-1-2-1-3h0z" class="y"></path><path d="M274 356l1-1c0 3-1 6-1 9 0 1 0 2-1 3-1-1-1-1-1-2v-1c1-2 1-4 2-6v-1-1z" class="O"></path><path d="M314 463l1 5h0v2 4h0-1c-2-3-1-8 0-11z" class="N"></path><path d="M289 293c1 0 2-1 4-1v2c-1 1-3 2-4 3l-2-2 1-1h-1c1-1 1-1 2-1z" class="B"></path><path d="M314 301h0c3 0 8 5 10 7l-9-5c-1-1-1-1-1-2z" class="X"></path><path d="M548 483c0 4 0 8-1 13v-1l-1-10 1-1 1-1z" class="N"></path><path d="M304 341c2 2 5 0 7 3h-1v1h-1-1-1c0-1-1-1-1-1l-2-1-1-1 1-1z" class="I"></path><path d="M274 271l2 3h0v2c0 1 0 2 1 4h1l-2 1c-1-1-2-6-3-8l1-2z" class="B"></path><path d="M319 292c1 1 1 3 1 5l-1 1c-1 0-2 0-2-1-1 0-2-1-3-2 1 0 1 0 1-1h1s1 1 2 1v-1c1-1 1-2 1-2z" class="X"></path><path d="M534 474c4 2 6 4 8 7l-1 2c-2-5-5-6-9-8l2-1z" class="N"></path><path d="M307 435h1 2c-3 3-7 6-11 7h-1v-2c3-1 6-3 9-5z" class="M"></path><path d="M553 482h0c1 2 1 2 0 3 0 1-1 4 0 4 0-1 0-1 1-2v-1-1c0-1 1-1 1-2v4l-3 9h-1 0l2-14zm9-24c0-2 0-4 2-6h2c0 1 1 1 0 2 0 2-1 3-2 5-1-1-2-1-2-1z" class="y"></path><path d="M302 508h1c1 1 2 1 2 2 2 4 2 11 0 15l-1 4h-1v-1l1-2c1-3 1-6 1-9 0-2 0-6-1-8h-1l-1-1z" class="AB"></path><path d="M337 307l2 1c1 3 1 6 1 9v2h-1l-2-11v-1z" class="X"></path><path d="M290 286h1c1 0 2-1 4-2 2 0 3-1 6-1 1-1 4-2 5-2-4 3-9 4-14 6-1 0-1 1-2 1h-1v-1c0-1 0-1 1-1z" class="P"></path><path d="M352 331v-1c1-2 2-4 5-5-1 1-1 2-1 4h0c-1 1-2 2-2 3-1 2-1 3-2 4h-1 0-1v-2c1 0 1-1 1-2h0l1-1z" class="K"></path><path d="M352 331h1l-2 5h0-1v-2c1 0 1-1 1-2h0l1-1z" class="a"></path><path d="M570 456h0c0-1 1-2 1-3s1-2 1-4l-1 5c-1 1 0 4-1 6l-3 9-1 1-2-1c1-1 3-4 3-5h1c1-2 1-5 2-8z" class="I"></path><path d="M278 280l2-3c1-1 3-2 4-2h0l-1 2-1 2h-1c-1 1-2 2-4 3l-1 1v1c-1 1-2 1-2 2h-2v-1c1 0 1-1 2-2s2-1 2-2l2-1z" class="f"></path><path d="M271 493v-1c1-1 1-3 2-4v-1l2-2c1 1 1 2 1 4l-1 1v1l1-1v1c0 1 0 2-1 3v-1h-1c-1 1-2 1-3 1h0l-1-1h1z" class="D"></path><path d="M342 419c2 1 6 4 7 6l-1 1h0c0 1 0 1-1 2l-1-2-3-3h0c-1-1-2-1-2-2v-1c0-1 0-1 1-1z" class="p"></path><path d="M272 507l1 1c1 0 1-1 2-1h1c0 1-1 2-1 2-2 2-3 4-4 6l-2 2-1-1 1-3 3-6z" class="J"></path><path d="M272 507l1 1c1 0 1-1 2-1 0 2-3 3-4 5-1 1-1 1-2 1l3-6z" class="L"></path><path d="M288 548h0c0 1-1 2-2 3l1-1h1 1 0c-5 4-11 10-17 11l16-13z" class="a"></path><path d="M532 503l-1 8c-1 5-3 12-7 15 2-4 4-7 5-10 1-5 1-9 2-14l1 1z" class="o"></path><path d="M340 317l3 18c0-1-1-2-2-3-1-3-2-8-2-11v-2h1v-2z" class="I"></path><path d="M271 561c1 0 3 2 3 3 1 1 1 1 1 2v1l-2 1v-1c-2-1-3-2-5-3l-3-3c2 1 4 0 6 0z" class="N"></path><path d="M531 502c1-4-1-10 3-14 0 1 0 0 1 1s1 2 1 3c-1 2-2 4-2 6-1 1-1 3-2 5l-1-1z" class="q"></path><path d="M284 446c3 1 6 3 8 5v1s-1 0-2 1h0 0c-1-1-3-1-4 0-1 0-1 0-2-1v-1l1 1 1-1c-1 0-2-1-2-2v-3z" class="I"></path><path d="M546 453c-1-3-3-8-2-11 0-1 1-2 2-2h3c1 1 2 3 2 4h0l-3-1c-1 0-1 1-2 1-1 3 0 6 1 8l-1 1z" class="q"></path><path d="M564 486c2-5 4-10 6-14 1-1 2-3 3-3 2 3-3 6-3 9h0c0 1 0 1-1 1 0 1 0 1-1 2v1s-1 1-1 2l-3 2z" class="N"></path><path d="M282 446h1 0 1v3c0 1 1 2 2 2l-1 1-1-1v1c1 1 1 1 2 1l1 1-1 1h0c0 1-1 0-1 0-1 0-1-1-2-1s-1 0-1 1c-1-1-2-2-2-4v-1-1c1-1 1-2 2-3z" class="P"></path><path d="M284 452c1 1 1 1 2 1l1 1-1 1-3-2 1-1z" class="X"></path><path d="M283 446h1v3c0 1 1 2 2 2l-1 1-1-1v1l-1 1c-1 0-1-1-1-1l1-1-2-4h1l1-1z" class="p"></path><path d="M331 289c2 4 4 7 5 11 1 2 1 3 1 4l-2 1c0-1-1-3-1-4-2-3-3-7-4-11l1-1z" class="N"></path><path d="M350 444v-1 2c0 5-1 13-4 17-3 1-4 11-6 14h-1c0-2 0-3 1-4 1-4 3-9 5-12 0 1 0 1 1 1 3-4 3-12 4-17z" class="m"></path><path d="M351 332h0c0 1 0 2-1 2v2h1 0 1c0 2-1 3-1 5v5c-1 2-1 4-1 5-1 2 0 3 0 5h-1c0-7-2-16 0-22 0-1 1-1 2-2z" class="P"></path><path d="M351 332h0c0 1 0 2-1 2v2h1c0 2-1 3-1 5-1-2-1-5-1-7h0c0-1 1-1 2-2z" class="M"></path><path d="M286 453c1-1 3-1 4 0h0l2 1c1 1 3 2 3 4h0c-1 0-2-2-3-2s-2 1-2 1c0 1 1 2 1 2-2 0-8-3-9-4h0c0-1 0-1 1-1s1 1 2 1c0 0 1 1 1 0h0l1-1-1-1z" class="I"></path><path d="M286 453c1-1 3-1 4 0h0l2 1c-2 1-2 1-3 2-1 0-2 0-3-1h0l1-1-1-1z" class="a"></path><path d="M294 276c4-2 8-5 12-6l-3 3c-2 1-7 3-7 4 3 0 6-2 10-2-4 1-7 3-11 4h-1l-1 1h-2s-1-1-1-2c2 0 3-1 4-2z" class="Q"></path><path d="M272 299h0c2 0 3 0 3 1h0c0 2 0 3 1 5v9l-2-4h-1v-4c-1-1-1-3-1-4h-1v-2h1v-1z" class="k"></path><path d="M272 299c2 0 3 0 3 1h0s0 1-1 1v1c-1-1-1-2-2-3z" class="L"></path><path d="M274 310l-1-8h1c1 2 0 5 2 6v-3 9l-2-4z" class="U"></path><path d="M560 475h2l-1 2c1 0 1 0 1 1 0 2-1 4-2 6l-1-1-2 1c-1 2-1 3-2 4v-1-4c1-1 1-3 2-4h0 1l2-4z" class="V"></path><path d="M561 477c1 0 1 0 1 1 0 2-1 4-2 6l-1-1-2 1 4-7z" class="o"></path><path d="M271 499l1 1h1s0 1 1 1c0 1 0 1 1 1l1-2c1 2 0 5-1 7-1 0-1 1-2 1l-1-1 2-2h-1c-1 0-4 4-5 6h0v-3h0 0l2-3 1-1c0-2 0-2-1-3h0v-1l1-1z" class="J"></path><path d="M271 499l1 1h1s0 1 1 1l-1 2-2-2h-1 0v-1l1-1z" class="E"></path><path d="M557 484l2-1 1 1c-2 4-4 8-5 13v2l-1 4c-1-6-1-9 1-15 1-1 1-2 2-4z" class="N"></path><path d="M315 467v-1l1-1c1 1 0 4 0 6l1-1v-1c1-2 1-3 1-4l1-1 1-1c1-1 2-3 3-4 1 0 2 0 2-1h2c-2 2-4 4-6 7l-3 6c0 1 0 4-1 5l-1-1c0-3 0-6-1-8z" class="M"></path><path d="M268 564c2 1 3 2 5 3v1c-1 1-4 1-6 2l-3 1c-1 0-2 1-3 1l-2-1v-1h1-2c3-2 6-3 9-3 1-1 1-2 1-3z" class="p"></path><path d="M264 571v-1h1v-1h1l1 1-3 1z" class="M"></path><path d="M268 564c2 1 3 2 5 3-2 0-3 0-5 1-3 0-6 1-8 2h-2c3-2 6-3 9-3 1-1 1-2 1-3z" class="Y"></path><path d="M274 255l1-1v2c1 1 1 2 1 3v3l1 1c0 1 0 2-1 4l-2 1v-1l-1-2c0-1-1-2-2-2v-3c1 0 2-1 2-2v-2-1h0 1z" class="m"></path><path d="M274 255l1-1v2 2c-1-1-1-2-1-3z" class="S"></path><path d="M275 256c1 1 1 2 1 3v3c-1-1-1-3-1-4v-2z" class="j"></path><path d="M277 263c0 1 0 2-1 4l-2 1v-1l-1-2 2-1h0 1v1l1-2z" class="B"></path><path d="M273 265l2-1h0c0 1 0 2-1 3l-1-2z" class="S"></path><path d="M271 260c1 0 2-1 2-2 1 1 1 1 1 2 0 2 1 3 1 4l-2 1c0-1-1-2-2-2v-3z" class="E"></path><path d="M337 286c0-1 0-4-1-6-1 1-1 2-1 3-2-1-2-3-3-5l2-2c1-1 2-1 4-1 1 1 1 2 2 3-1 1-1 3-1 4-1 2 0 4-1 5l-1-1z" class="N"></path><path d="M270 501h0c1 1 1 1 1 3l-1 1-2 3h0 0v3h0v2h-1-1 1l-2-2c-1 0-1 0-2-1l-1 1v-1c0-2 1-3 2-4l4-3c0-1 1-2 2-2z" class="b"></path><path d="M270 501h0c1 1 1 1 1 3l-1 1-1-1v1l-1-1 1-2h1v-1z" class="G"></path><path d="M268 508h0v3h0v2h-1-1 1l-2-2 3-3z" class="h"></path><path d="M268 503v2c-1 0-1 1-2 2s-1 1-2 3h0-1l-1 1v-1c0-2 1-3 2-4l4-3zm19-228h1v1l1 1v1c-2 1-5 2-7 4-1 1-1 1-1 2l1 1-1 1h-1c-2 0-2 1-3 2h-2c1-1 1-2 1-3v-1-1l1-1c2-1 3-2 4-3h1l1-2c0 1 0 1 1 1l3-3z" class="P"></path><path d="M287 275h1v1l1 1c-2 0-3 0-5 1l3-3z" class="b"></path><path d="M268 531h1c2-3 4-9 8-11-2 4-4 7-6 10-2 2-3 5-4 7-2 3-4 6-6 8v-1c0-1 1-2 2-3v-4-1l1-1h0 1 1v-1c1-2 2-2 2-3z" class="r"></path><path d="M271 535c1-1 3-5 5-6 0 2-1 3-1 5 1 0 0 1 0 2l-2 2 2 1h-1c0 1 0 1-1 2l1 1c-1 0-2 1-3 2l-1-1c-1-1-1 0-2 0h-1 0c0-2 3-5 4-8z" class="h"></path><path d="M270 540c1 0 1-1 2-2v3l-2-1z" class="J"></path><path d="M268 543c1-2 1-2 2-3h0l2 1c-1 0-1 1-2 2-1-1-1 0-2 0z" class="S"></path><path d="M273 538l2 1h-1c0 1 0 1-1 2l1 1c-1 0-2 1-3 2l-1-1c1-1 1-2 2-2v-1c1-1 1-1 1-2z" class="B"></path><path d="M547 452c2 3 3 5 3 8 3 8 3 16 1 25 0 2-1 4-1 6v2c-1-3 0-9 1-12 0-6 0-13-1-19-1-3-3-6-4-9l1-1zM340 278c1 3 2 7 2 10-1 2-2 3-4 4l1 16-2-1v-3l1-1-1-17 1 1c1-1 0-3 1-5 0-1 0-3 1-4z" class="M"></path><path d="M331 464h0c2 1 3 2 4 4 2 6 3 11 1 17 0 1-1 1-1 2-1-4-1-9-1-13-1-3-2-7-3-10z" class="y"></path><path d="M287 275c1-1 1-2 3-2 1-1 3-2 4-2 0 1 0 1-1 2s-2 1-2 2v1c3-2 5-4 8-7-1-2-2-5-3-7 1 2 4 6 4 8-1 2-2 2-3 3s-3 2-3 3c-1 1-2 2-4 2h-1c-1 1 0 1-1 1s-2 2-3 3h-1-2c2-2 5-3 7-4v-1l-1-1v-1h-1zm-18 248c2-4 4-7 7-10h0c-1 4-4 8-6 11-1 2-3 4-3 6 1 0 1 0 1-1 2-4 4-8 8-11h0c0 2-3 4-4 6l-4 7c0 1-1 1-2 3v1h-1-1 0c0-1 1-2 1-4v-1c1-1 1-1 1-2 1-1 1-2 2-3 1 0 1-1 1-2z" class="d"></path><path d="M295 289l2-1v1h2 1c-1 1-2 1-4 1v1l1 1h-4c-2 0-3 1-4 1s-1 0-2 1h1l-1 1-3 1h-1c-1-1-1-1-1-2h1v-1h-2v-1-1l1-1 2 1 1-1c1-1 2-1 2-1h1v-1c2 2 3 1 4 2 1 0 2-1 3-1z" class="l"></path><path d="M289 292l4-1c1-1 2-1 3 0l1 1h-4c-2 0-3 1-4 1h0v-1z" class="L"></path><path d="M287 289h1v-1c2 2 3 1 4 2-2 1-4 1-6 1l-1 1-1-1 1-1c1-1 2-1 2-1z" class="P"></path><path d="M282 290l2 1 1 1 1-1c1 1 2 1 3 1v1h0c-1 0-1 0-2 1h1l-1 1-3 1h-1c-1-1-1-1-1-2h1v-1h-2v-1-1l1-1z" class="t"></path><path d="M282 290l2 1 1 1-1 1h0c-1 0-2-1-3-1v-1l1-1z" class="K"></path><path d="M342 433c1 0 1 0 1-1l1 1c0 1 1 1 2 2 0 1 1 2 1 3 1 2 1 4 1 5l2 1c-1 5-1 13-4 17-1 0-1 0-1-1v-1c3-8 1-19-3-26z" class="o"></path><path d="M271 313c2-2 0-5 2-7v4h1l2 4v1 9 5l-1 1c-1-1-1-2-1-3l-1 1-1-2v-1h1v-1-1l-1-6-1-4z" class="F"></path><path d="M273 317l-1-4h0c2 1 2 5 2 7l1 1h-1c-1-1-1-2-1-4z" class="z"></path><path d="M274 310l2 4v1 4c-2-3-2-6-3-9h1z" class="j"></path><path d="M276 315v9 5l-1 1c-1-1-1-2-1-3l-1 1-1-2v-1h1v-1-1l-1-6h1c0 2 0 3 1 4h1l-1-1 2 3v-4-4z" class="Y"></path><path d="M272 325h1v-1-1c1 2 1 3 1 4l-1 1-1-2v-1z" class="z"></path><path d="M274 320l2 3v4l-1-1c0-1 0-3-1-4v-1h1l-1-1z" class="E"></path><path d="M268 511c1-2 4-6 5-6h1l-2 2-3 6-1 3-4 10-1 1h0-1v-2h-1-2 0v-1c1 0 2-1 2-2l1-3 1-2h0c1-2 2-3 3-4h1 1v-2z" class="S"></path><path d="M264 518c1-1 1 0 2 0-1 1-1 2-1 3h0l-1 1v-1l-2-2 1-2 1 1z" class="r"></path><path d="M264 518c1-1 1 0 2 0-1 1-1 2-1 3h0c0-1-1-2-1-3z" class="Z"></path><path d="M266 513h1c0 2 0 4-1 5-1 0-1-1-2 0l-1-1h0c1-2 2-3 3-4zm-4 6l2 2v1c0 1 0 3-1 3h-1-1-2 0v-1c1 0 2-1 2-2l1-3z" class="d"></path><path d="M262 519l2 2c-1 0-1 1-1 1h-2l1-3z" class="b"></path><defs><linearGradient id="h" x1="338.173" y1="426.499" x2="345.987" y2="429.118" xlink:href="#B"><stop offset="0" stop-color="#cac9ca"></stop><stop offset="1" stop-color="#f3f3f3"></stop></linearGradient></defs><path fill="url(#h)" d="M341 421c0 1 1 1 2 2h0l3 3 1 2 1 1-2 1c-1 1-1 1-1 2 0 0 0 1 1 1v2c-1-1-2-1-2-2l-1-1c0 1 0 1-1 1l-5-6c1-2 2-4 4-6z"></path><path d="M555 497l1-1c1-1 2-4 2-6l4-7c2-5 5-10 7-15 1-3 1-6 2-9 0 3 1 6 0 8-1 3-2 6-4 9l-7 15c-1 2-3 5-4 7l-1 1v-2z" class="o"></path><path d="M314 295c0-1-1-1-1-2-1-2-1-5-1-6 1-2 3-3 4-4 2 0 4-1 5 0 3 1 4 3 5 6v1 1c-1-1-1-1-3-2-1-1-3-2-5-2-1 1-3 3-3 4v2 1h0c0 1 0 1-1 1z" class="q"></path><path d="M275 330l1-1v5c0 4 1 10 0 13-1 1-1 2-1 2-1-1-2-2-3-4-3-1-3-1-6-1h-1c2-1 4-1 4-3 2-2 3-5 4-8v-2s1 0 2-1z" class="m"></path><path d="M272 344c-1 0-1 0-1-1 1-3 2-6 2-9 1 1 1 3 1 4l-1 4c0 1-1 2-1 2z" class="C"></path><path d="M276 334c0 4 1 10 0 13-1 1-1 2-1 2-1-1-2-2-3-4v-1h0s1-1 1-2l1-4 2 2v-6z" class="U"></path><path d="M272 344s1-1 1-2l1 4h0l-2-2h0z" class="l"></path><path d="M272 344l2 2h0c1 0 1 0 2-1v2c-1 1-1 2-1 2-1-1-2-2-3-4v-1z" class="k"></path><path d="M341 420c-2 1-4 4-4 6s0 5-2 6c-1 1-1 1-3 1 0 0 0-1-1-1-1-2-1-5 0-7 0-3 2-5 4-6s4-1 7 0c-1 0-1 0-1 1z" class="y"></path><path d="M325 246h1c1 0 1 1 2 1 4 7 3 18 2 25-1-2-1-4-1-7l-2-12v1h0c-1 3-1 5-1 8-1-2-1-5-1-7-1 0-2-1-2-2 0-2 1-5 2-7z" class="n"></path><path d="M271 530v1c2-3 3-4 6-6-1 1-1 2-2 3-1 2-4 5-4 7-1 3-4 6-4 8h0 1c1 0 1-1 2 0l1 1-2 1-4 2-6 6-1-2c1-1 2-3 4-4v-1h0l-1-1c2-2 4-5 6-8 1-2 2-5 4-7z" class="k"></path><path d="M268 543c1 0 1-1 2 0l1 1-2 1-4 2c1-1 2-2 2-3v-1h1z" class="j"></path><path d="M277 394c1-5 1-10 4-15-1 5-2 10-1 15l1-5h1c1 1 0 2 0 3 0 4-1 9-2 13l-2 8h-2c2-6 1-13 1-19z" class="N"></path><path d="M545 419c2 0 3 1 4 1 1 3 2 6 2 9 2 8 3 15 4 22v6c-1-2-1-4-2-5l-4-19c-1-4-1-10-4-14z" class="q"></path><path d="M308 535h1l1 1-3 3c0 1-1 2-2 3-4 4-9 7-13 11-3 2-5 4-8 6-2 2-4 3-6 4l-1-3h1l1-1c2-1 4-2 5-3l8-7c4-2 8-5 12-8l3-3c0-1 1-2 1-3z" class="e"></path><path d="M332 455v-1c1-1 2-1 3-1 2 1 3 2 3 4v3l-1 1h-1c-3 0-5 0-8 2-4 3-5 8-8 11-1 1-1 1-2 1 0-2 2-6 3-8 2-3 5-9 9-10h2 1v-1l-1-1z" class="n"></path><path d="M338 345c-4-4-6-7-12-9-7-3-14-2-21 1 0-2-1-4 0-6 2 2 9 0 12 0s6 1 9 2c5 2 10 5 13 11h0c0 1 0 2 1 3v1c-1-2-1-2-2-3z" class="q"></path><path d="M532 475c-3-1-8-2-11 0-2 1-3 2-3 3-1 1-1 2 0 2 0 1 1 1 2 2l1-1c-1-1-1-2-1-3 1-1 1-1 3-1 0 0 1 0 1 1 1 1 2 1 1 3 0 1 0 2-1 2-2 1-4 1-5 1-2-1-4-2-4-3-1-2-1-4 0-5 0-2 2-3 3-3 5-3 11-1 16 1l-2 1z" class="n"></path><path d="M271 515h0c2-2 3-4 5-5h0c0 2-3 6-5 8 0 1-2 3-2 5 0 1 0 2-1 2-1 1-1 2-2 3 0 1 0 1-1 2v1c0 2-1 3-1 4l-1 1v1 4c-1 1-2 2-2 3v1l1 1-2 1c-2 0-2-1-3-2l1-1 3-11 1 1v-1l2-7 4-10 1 1 2-2z" class="D"></path><path d="M258 544l1 1c1-1 2-3 3-5v-1l1 1v1h0c-1 1-2 2-2 3v1l1 1-2 1c-2 0-2-1-3-2l1-1z" class="C"></path><path d="M268 516l1 1-4 11c-1 2-1 4-2 5h-1l2-7 4-10z" class="V"></path><path d="M267 330l1-1 2-5 1 4c1 0 1-1 1-2l1 2 1-1c0 1 0 2 1 3-1 1-2 1-2 1v2c-1 3-2 6-4 8 0 2-2 2-4 3h-1 0-1l-1-1 1-2v-1c0-1 1-2 1-3l3-7z" class="R"></path><path d="M271 328c1 0 1-1 1-2l1 2c0 1 0 1-1 2-1 0-1 0-1-1v-1z" class="B"></path><path d="M268 334c1-2 2-3 3-4 0 1 1 1 1 2h-1l-1 2h-1-1z" class="G"></path><path d="M268 334h1 1l1-2h1l1 1c-1 3-2 6-4 8 0 2-2 2-4 3h-1 0-1l-1-1 1-2v-1l1 2v-1h1 0l3-6v-1z" class="p"></path><path d="M263 340l1 2v-1h1l-1 3h-1l-1-1 1-2v-1z" class="i"></path><path d="M268 334h1 1l1-2h1l1 1c-1 3-2 6-4 8 0 0-1 0-2-1v-1c1-1 1-1 2-1 0-1-1-2-1-3h0v-1z" class="a"></path><path d="M330 290c0-2-1-7-2-9v4l1 5c-1-3-3-5-4-8-1-4-3-26-2-28 1 3 1 5 2 8 1 4 3 9 5 13 1 5 2 9 1 14l-1 1z" class="e"></path><path d="M311 427h1c-2 3-3 6-5 8-3 2-6 4-9 5v2h1c-4 2-10 3-15 3l-1 1h-1c-1 1-1 2-2 3v1 1l-1 1c0 1 0 4-1 5v-1h-2l-1-1h1l2-10c1-1 1-1 1-2v-2h3 1l6-1 1 1h-1 1v1l9-3c5-2 10-7 12-12z" class="AB"></path><path d="M282 441h1v1h3l-5 1h0v-1l1-1z" class="a"></path><path d="M279 441h3l-1 1v1h0-2v-2z" class="M"></path><path d="M278 449l1-2c1-1 1-1 2-1h1c-1 1-1 2-2 3v1l-2-1z" class="O"></path><path d="M283 441l6-1 1 1h-1 1v1c-1 0-3 1-4 0h-3v-1z" class="X"></path><path d="M278 449l2 1v1l-1 1c0 1 0 4-1 5v-1h-2l-1-1h1c1-1 1-1 1-2s0-2 1-4z" class="t"></path><path d="M284 445h0c2-1 4-1 5-1 4-1 7-2 9-4v2h1c-4 2-10 3-15 3z" class="a"></path><path d="M269 299l1-1 2 1v1h-1v2h1c0 1 0 3 1 4-2 2 0 5-2 7l1 4 1 6v1 1h-1v1c0 1 0 2-1 2l-1-4-2 5-1 1c1-3 1-7 1-10h0c0-1 1-2 0-3 0-1-1-1-1-2-1-1-1-2 0-3 1 0 1 0 2-1l-1-1-1-1-1-1 2-5 1-4z" class="k"></path><path d="M269 299l1-1 2 1v1h-1v2h1c0 1 0 3 1 4-2 2 0 5-2 7 0-1-1-2-1-3v-5c-1-1-2-1-2-2h0l1-4z" class="u"></path><path d="M268 317v-2-1h1c2 4 1 8 3 11v1c0 1 0 2-1 2l-1-4-2 5-1 1c1-3 1-7 1-10h0c0-1 1-2 0-3z" class="m"></path><defs><linearGradient id="i" x1="322.41" y1="451.715" x2="321.715" y2="469.834" xlink:href="#B"><stop offset="0" stop-color="#d1d1cf"></stop><stop offset="1" stop-color="#fbf9ff"></stop></linearGradient></defs><path fill="url(#i)" d="M314 463c1-3 3-6 6-9 4-3 8-3 13-2l2 1c-1 0-2 0-3 1v1l1 1c-3 0-4 1-6 2h-2c0 1-1 1-2 1-1 1-2 3-3 4l-1 1-1 1c0 1 0 2-1 4v1l-1 1c0-2 1-5 0-6l-1 1v1 1h0l-1-5z"></path><path d="M333 452l2 1c-1 0-2 0-3 1v1c-1-1-2-1-3-1v-1h1l1-1h2z" class="a"></path><path d="M259 502c1 0 2 0 2 1h0 1l1 1c0 1 0 1-1 2v1l1-2 1 1c-1 1-2 2-2 4v1l1-1c1 1 1 1 2 1l2 2h-1c-1 1-2 2-3 4h0l-1 2-1 3c0 1-1 2-2 2v1h0c0 1-1 1-2 1-1 1-2 1-3 1v-1c1-1 2-4 2-5l2-2c-1 0-1 0-2-1 1-1 1-2 1-4h-1v-1c1-3 2-5 2-8l1-3z" class="u"></path><path d="M259 502c1 0 2 0 2 1l1 1v2l-2-1v-1h0v-1l-2 2 1-3z" class="R"></path><path d="M259 513h0v-1c-1-1 0-2 1-3v-1c0 1 1 2 1 2 0 1-1 2 0 2v2 1l-1-1-1-1z" class="T"></path><path d="M261 503h1l1 1c0 1 0 1-1 2v1l1-2 1 1c-1 1-2 2-2 4v1l1-1c1 1 1 1 2 1-1 1-1 1-2 1h-2c-1 0 0-1 0-2 0 0-1-1-1-2l2-2v-2l-1-1h0z" class="h"></path><path d="M265 511l2 2h-1c-1 1-2 2-3 4h-1l-2 1 1-3v-1-2h2c1 0 1 0 2-1z" class="E"></path><path d="M259 513l1 1 1 1-1 3 2-1h1 0l-1 2-1 3c0 1-1 2-2 2 0-1 1-2 1-3 1 0 1 0 1-1s0-1-1-1v1c-2 0-2 1-4 1l2-2c1-2 1-4 1-6z" class="C"></path><path d="M256 521c2 0 2-1 4-1v-1c1 0 1 0 1 1s0 1-1 1c0 1-1 2-1 3v1h0c0 1-1 1-2 1-1 1-2 1-3 1v-1c1-1 2-4 2-5z" class="Z"></path><path d="M276 284v1c0 1 0 2-1 3h2c1-1 1-2 3-2v1c-2 2-1 2-2 3v1 1l3 1h0 2v1h-1c0 1 0 1 1 2h1l-1 1c-1-1-2-1-4-1l2 1-1 1 1 1c-1 0-1 1-2 1h-4c0-1-1-1-3-1h0l-2-1-1 1h0l-2-1h0l1-3h0c1-1 1-2 1-3l1-2v-1l2-3h2c0-1 1-1 2-2z" class="Q"></path><path d="M268 295h1v1 3l-2-1h0l1-3z" class="M"></path><path d="M277 297c1 0 1-1 2-1l2 1-1 1 1 1c-1 0-1 1-2 1-1-2-5-1-6-3h0 0 3 1z" class="b"></path><path d="M278 292l3 1h0 2v1h-1c0 1 0 1 1 2h1l-1 1c-1-1-2-1-4-1-1 0-1 1-2 1-1-1-2-1-3-1v-1c1 0 2-1 4-1h-3c1-1 1-2 3-2z" class="H"></path><path d="M278 292l3 1-1 1h-2-3c1-1 1-2 3-2z" class="M"></path><path d="M276 284v1c0 1 0 2-1 3h2c1-1 1-2 3-2v1c-2 2-1 2-2 3v1 1c-2 0-2 1-3 2h-3 0c-1 0-1 1-2 1 0-1 1-2 1-3-1-1-1-1-1-2v-1l2-3h2c0-1 1-1 2-2z" class="O"></path><path d="M272 286h2c0 1-1 2-1 3-1 1-1 2-2 3-1-1-1-1-1-2v-1l2-3z" class="M"></path><path d="M274 479c1-1 1-1 2-1h0v3c-1 2-3 3-4 5 0 1-1 1-1 2v1h0c0 1-1 2-1 2 0 1 0 1 1 2h-1l1 1h0c1 0 2 0 3-1h1v1c-2 1-4 2-5 3h1s1 0 1 1l-1 1-1 1v1c-1 0-2 1-2 2l-4 3-1-1-1 2v-1c1-1 1-1 1-2l-1-1h-1c0-1 0-2 1-2 0-2 1-4 1-5 0-2 1-5 2-7h1c1-1 2-2 2-4 0 1 0 1 1 2 0-1 1-1 1-2 1-2 2-4 4-6h0z" class="J"></path><path d="M271 494h0c1 0 2 0 3-1h1v1c-2 1-4 2-5 3h-1-2v-1c1 0 2-1 3-1l1-1z" class="V"></path><path d="M265 489h1c1 2 0 3-1 5l1 1 3-6h0c0 2 1 2 0 3-1 2-2 3-3 5h0c-1 0-2 1-2 2-1 0-1 1-2 2 0-2 1-4 1-5 0-2 1-5 2-7z" class="E"></path><path d="M271 497s1 0 1 1l-1 1-1 1v1c-1 0-2 1-2 2l-4 3-1-1-1 2v-1c1-1 1-1 1-2l-1-1h-1c0-1 0-2 1-2 1-1 1-2 2-2h1 2c1-1 2-2 4-2z" class="D"></path><path d="M264 499h1v1c0 1 0 1 1 1-2 1-3 1-4 2h-1c0-1 0-2 1-2 1-1 1-2 2-2z" class="P"></path><path d="M271 497s1 0 1 1l-1 1-1 1c-2 1-3 1-4 1s-1 0-1-1v-1h2c1-1 2-2 4-2z" class="I"></path><path d="M295 279l1 1 11-5c-1 3-5 4-7 5-1 0-1 0-1 1 2 0 5-1 7-2-2 2-4 3-6 3-3 0-7 1-9 3l-1 1c-1 0-1 0-1 1v1h-1v1h-1s-1 0-2 1l-1 1-2-1-1 1v1 1h0l-3-1v-1-1c1-1 0-1 2-3v-1h1l1-1-1-1c0-1 0-1 1-2h2 1c1-1 2-3 3-3s0 0 1-1h1c0 1 1 2 1 2h2l1-1h1z" class="K"></path><path d="M280 287h2v2c-1 1-2 1-4 1 1-1 0-1 2-3z" class="I"></path><path d="M282 289v1l-1 1v1 1h0l-3-1v-1-1c2 0 3 0 4-1z" class="C"></path><path d="M287 286c1 0 2-2 3-2h0l1 1-1 1c-1 0-1 0-1 1v1h-1v1h-1l-1-2 1-1z" class="f"></path><path d="M281 286l1-1-1-1c0-1 0-1 1-2h2 1l-1 1h1 1v1c-1 1-3 1-4 2 0 0 0 1-1 1v-1z" class="b"></path><path d="M285 287c1-1 1-1 2-1l-1 1 1 2s-1 0-2 1l-1 1-2-1v-1l1-1s1-1 2-1z" class="k"></path><path d="M285 287c1-1 1-1 2-1l-1 1 1 2s-1 0-2 1c0 0 0-1-1-1 0-1 1-1 1-2h0z" class="J"></path><path d="M349 356h1c0-2-1-3 0-5 0-1 0-3 1-5v6c0 2-1 3 1 5h0c1 0 0 0 1 1h0v1c1 1 1 1 2 3l-1 1 2 1-1 1c1 0 1 1 1 2 0 0 0 1-1 1v1 2c0 1 1 2 1 3v1 4c-1 1-2 1-2 2l-2 2-1-1v2h-2 0c0-2-1-3-2-5l1 1h0 1c0-4-1-7-1-11l1-1v-5-7z" class="Q"></path><path d="M351 371c0-2 1-4 2-5 0-2-1-4 0-5l1 2 2 1-1 1c1 0 1 1 1 2 0 0 0 1-1 1v1l-1-1-2 4-1-1z" class="AA"></path><path d="M355 365c1 0 1 1 1 2 0 0 0 1-1 1v1l-1-1c0-2 0-2 1-3z" class="F"></path><path d="M349 363v1l1 9h0l1-2 1 1v1c-1 2-1 4-1 6v3 2h-2 0c0-2-1-3-2-5l1 1h0 1c0-4-1-7-1-11l1-1v-5z" class="S"></path><path d="M349 384c0-2 1-3 1-5h1v3 2h-2 0z" class="F"></path><path d="M354 368l1 1v2c0 1 1 2 1 3v1 4c-1 1-2 1-2 2l-2 2-1-1v-3c0-2 0-4 1-6v-1l2-4z" class="e"></path><path d="M356 374v1 4c-1 1-2 1-2 2-1-1-1-1-1-2h1v-1-2h1c1-1 1-1 1-2z" class="X"></path><path d="M354 368l1 1v2c0 1 1 2 1 3s0 1-1 2h-1 0c-1-1-2-2-2-3v-1l2-4z" class="N"></path><path d="M273 460c0-1 0-1 1-1 0-1 0-1 1-1h0l1 2-4 5 1 1c-1 1-1 2-2 3h3v1l1-1 1 1v3 1 1c-1 1-1 2-2 4h0c-2 2-3 4-4 6 0 1-1 1-1 2-1-1-1-1-1-2 0 2-1 3-2 4h-1c1-5 5-12 3-16-1-2-3-2-4-3v-1c1-2 4-3 5-5v-1c1-1 1-1 1-2h2l1-1z" class="i"></path><path d="M266 468h3c1 0 1 0 1 1s-1 2-1 2h-1c-1 0-2-1-3-2l1-1z" class="a"></path><path d="M271 469h3v1c-1 2-3 6-5 8 0-1 0-3 1-4 0-1 0-1-1-2 1-1 2-1 2-2v-1z" class="K"></path><path d="M273 460c0-1 0-1 1-1 0-1 0-1 1-1h0l1 2-4 5c-1 1-1 2-2 4 0-1 0-1-1-1h-3c2-2 4-5 6-7l1-1z" class="Q"></path><path d="M268 485c0-1 1-2 1-3s0-1 1-2c1-3 3-7 6-9v1c-3 2-4 6-5 9 1 0 2-3 2-4 1-1 2-2 2-3l1 1c-1 1-1 2-2 4h0c-2 2-3 4-4 6 0 1-1 1-1 2-1-1-1-1-1-2z" class="V"></path><path d="M273 370c3-5 6-8 12-10 2 0 4 0 6 1 1 0 2 1 2 2v3c-1 1-2 2-3 2h-2l-1-1c-1 0-2 1-3 2-4 3-8 6-11 11-1 1-1 2-2 4 0 2-1 4-1 6 0 0 0 1-1 1h0v1 2l-1-8c0-2 1-5 2-8v-1c1-1 1-2 2-4 0-1 1-2 1-3z" class="n"></path><path d="M273 370v3l-1 1h0 2 0c-1 1-1 2-2 3-1-1-1-1-2 0 1-1 1-2 2-4 0-1 1-2 1-3z" class="M"></path><path d="M347 417c-2-3-4-7-8-9-3-2-7-2-10-5v2c-1 0-2-1-2-1-1-1-1-3-1-4 1-2 2-3 3-4 2-1 4-1 6-1 5 1 8 6 11 10l3 5c0 1-1 2-1 3s1 2 1 3v1h-2z" class="n"></path><path d="M348 413c-2-2-3-5-4-7-2-2-6-5-8-6h-1 0c-1-1-2 0-3 0h0-1 0-1c1 0 1-1 2-1h4l1 1c1 1 3 2 4 3 2 1 3 3 4 3l1-1 3 5c0 1-1 2-1 3z" class="P"></path><path d="M267 298l2 1h0l-1 4-2 5 1 1 1 1 1 1c-1 1-1 1-2 1-1 1-1 2 0 3 0 1 1 1 1 2 1 1 0 2 0 3h0c0 3 0 7-1 10l-3 7c0 1-1 2-1 3v1c-1 0-1-1-1-1l-1-1v1h-1 0-2v-1l1-3v-1l1-5h-1c0-2 1-6 1-8 0-1 1-2 1-3l2-7c0-2-1-3-1-5l1-1v-2l1 1 1-1 1-4c0-1 1-1 1-2z" class="a"></path><path d="M263 304l1 1-1 7c0-2-1-3-1-5l1-1v-2z" class="O"></path><path d="M261 328l5-20 1 1 1 1 1 1c-1 1-1 1-2 1-1 1-1 2 0 3 0 1 1 1 1 2 1 1 0 2 0 3h0v-1c-1 0-2 2-2 3h-1l-1 1v1c0 1-1 3-1 3h0-1v1h-1z" class="T"></path><path d="M264 324h-1l1-1v1zm2-7h0 0v2h-1c0-1 0-1 1-2z" class="s"></path><path d="M266 322c0-1 1-3 2-3v1c0 3 0 7-1 10l-3 7c0 1-1 2-1 3v1c-1 0-1-1-1-1l-1-1v1h-1 0-2v-1l1-3v-1l1-5 1-2h1v-1h1 0s1-2 1-3v-1l1-1h1z" class="O"></path><path d="M264 323l1-1h1l-4 18-1-1v1h-1 0-2v-1l1-3v-1l1-5 1-2h1v-1h1 0s1-2 1-3v-1z" class="S"></path><path d="M262 328v-1h1 0l-1 6h-2l2-5z" class="Z"></path><path d="M261 328h1l-2 5h2l-1 5v1 1h-1 0-2v-1l1-3v-1l1-5 1-2z" class="c"></path><path d="M258 339l1-3v2h0l1-1v3h-2v-1z" class="s"></path><path d="M260 333h2l-1 5v1 1h-1 0v-3h0v-4z" class="d"></path><path d="M260 333h2l-1 5v-3c-1 0-1 1-1 2v-4z" class="J"></path><path d="M558 454c1-3 2-7 5-9h3c2 1 3 3 4 5l-1 4c0 1 1 2 1 2-1 3-1 6-2 8h-1c0 1-2 4-3 5l2 1-1 3h0c-1-1-1-1-2 0l-1 2h-2l-2 4h-1 0c-1 1-1 3-2 4 0 1-1 1-1 2v1 1c-1 1-1 1-1 2-1 0 0-3 0-4 1-1 1-1 0-3h0c2-6 3-14 6-20 1-1 1-3 3-4 0 0 1 0 2 1-1 1-1 2-2 4 2-1 4-4 5-6s2-3 1-5c-1-1-1-1-2-1-1-1-3-1-4 0s-2 3-3 4l-1-1z" class="q"></path><path d="M563 467c1-4 5-8 6-13 0 1 1 2 1 2-1 3-1 6-2 8h-1c-1 0-1 1-2 2v1-1l-2 1h0z" class="g"></path><path d="M563 467h0l2-1v1-1c1-1 1-2 2-2 0 1-2 4-3 5l2 1-1 3h0c-1-1-1-1-2 0l-1 2h-2l-2 4h-1 0c2-4 3-8 6-12z" class="U"></path><path d="M560 475c1-2 1-3 2-4l1 2-1 2h-2z" class="b"></path><path d="M564 469l2 1-1 3h0c-1-1-1-1-2 0l-1-2c1 0 2-1 2-2z" class="p"></path><path d="M529 462c-6-3-11-4-18-4-4 0-6 0-10-1l2-2c1-1 3-2 5-2h1 1c2-1 4-1 6 0 7 0 14 2 20 7l2 2c6 6 9 13 10 21l-1 1-1 1v-3c-3-10-8-15-17-20z" class="N"></path><path d="M536 460l2 2c6 6 9 13 10 21l-1 1-1 1v-3c-3-10-8-15-17-20v-1c1 0 1 0 1 1h1 1 0c1 2 3 2 4 3v1c2 1 5 3 6 5 0 1 0 1 1 1 2 3 2 7 4 10 0-1 0-2-1-3v-1-2l-1-1v-1l-1-1c0-1 0-1-1-2v-1-1l-2-2c0-1-1-2-2-3-2 0-5-2-7-3v-1l1 1 3-1z" class="n"></path><path d="M536 460l2 2c-2 1-3 0-5-1l3-1z" class="e"></path><path d="M516 453c7 0 14 2 20 7l-3 1-1-1c-2-1-5-2-8-3-1 0-1 1-2 0l1-1c0 1 1 1 1 1v-2l-3-1h-1v3h-1c-3-1-6-1-9-1 1-1 3-1 4-1 2 0 5 0 6-1h-1c-1-1-2-1-3-1h0z" class="p"></path><defs><linearGradient id="j" x1="266.699" y1="490.018" x2="255.621" y2="481.278" xlink:href="#B"><stop offset="0" stop-color="#d4d3d3"></stop><stop offset="1" stop-color="#faf9fb"></stop></linearGradient></defs><path fill="url(#j)" d="M264 470c1 1 3 1 4 3 2 4-2 11-3 16-1 2-2 5-2 7 0 1-1 3-1 5-1 0-1 1-1 2h0c0-1-1-1-2-1v-1h-2v1-1c0-1 1-3 0-4l-1-1v2 2h0c-1 1-1 2-2 3l1-4h-1c1-2-1-4 0-7v-1c0-1 0-2 1-4l1-1 1-2v-1c0-1 1-3 1-4v-2l2-1 1-1c0-2 2-4 3-5z"></path><path d="M258 488h1 0v2l-2 1v-1l1-2z" class="o"></path><path d="M258 477l2-1v1c-2 3-2 6-2 9 0-1 0-2-1-3 0-1 1-3 1-4v-2z" class="D"></path><path d="M254 492c1 0 1 0 2-1h1l-2 8h-1c1-2-1-4 0-7z" class="c"></path><path d="M264 470c1 1 3 1 4 3-1 1-1 1-1 2l-1-2-2-2-1 2h0c-1 2-2 4-3 4v-1l1-1c0-2 2-4 3-5z" class="N"></path><path d="M259 488l5-11c-1 2-4 15-5 16h-1c0-1 0-2 1-3v-2z" class="P"></path><path d="M257 483c1 1 1 2 1 3v2l-1 2v1h-1c-1 1-1 1-2 1v-1c0-1 0-2 1-4l1-1 1-2v-1z" class="T"></path><path d="M257 483c1 1 1 2 1 3v2l-1 2v-4h-1l1-2v-1z" class="c"></path><path d="M266 476c1 1 1 1 0 2l-3 16v2h0c0 1-1 3-1 5-1 0-1 1-1 2h0c0-1-1-1-2-1v-1c2-5 4-10 5-15l2-10z" class="T"></path><path d="M268 473c2 4-2 11-3 16-1 2-2 5-2 7h0v-2l3-16c1-1 1-1 0-2l1-1c0-1 0-1 1-2z" class="Q"></path><path d="M310 315c2-2 7-2 10-2 4 0 9 2 12 6 3 3 4 7 6 11 0-1-1-3-1-5-3-7-5-13-10-20l-4-5-2-2v-1l1-1c2 0 5 2 6 4 6 5 9 14 11 21 0 3 1 8 2 11 1 1 2 2 2 3l5 34c0 4 1 7 1 11h-1 0c-1-4-2-9-3-13-1-5-2-9-4-14 0-1 0-1 1-2l1 1h0l-1-1c0-2-1-5-1-7-2-7-5-14-9-20-4-5-9-7-15-8-2-1-5 0-7 0v-1h0z" class="q"></path><path d="M271 430v2h1l-1 4h0c1-1 2-1 3-1v-1l1-2h2 1v1c0 1 0 3 1 4h0v4h0v2c0 1 0 1-1 2l-2 10h-1l1 1h2v1h0c-1 1-1 2-2 3l-1-2h0c-1 0-1 0-1 1-1 0-1 0-1 1l-1 1h-2c0 1 0 1-1 2v1c-1 2-4 3-5 5v1c-1 1-3 3-3 5l-1-1h-1v-2h-2l1-3v-1c0-1 0-2 1-3l2 1v-2-1-2-2-1h1l1-4v-2-2l1 1 1-1v-1l-1-1 2-6 1-2c1-3 3-7 4-10z" class="C"></path><path d="M261 459h1c1 1 1 1 1 2h-2v-2z" class="S"></path><path d="M271 445c1-1 1-1 2-1 0 2-1 3-2 4l-1-2 1-1z" class="Q"></path><path d="M271 449v1c0 1-1 2-1 3h-1c-1-1-1-1-1-2 1 0 2-1 3-2z" class="d"></path><path d="M265 449l1-1c0 1 0 1 1 1l1 1 1-1h2c-1 1-2 2-3 2 0 1 0 1 1 2h-1l-1 1c-1 1-1 1-2 1l1-2c1 0 1-1 2-1v-1h-1l-1-1h-1v-1z" class="Z"></path><path d="M265 455c1 0 1 0 2-1h2v2c-2 1-3 2-4 3l-1 1h-1c0-1 1-1 1-2v-1l1-2z" class="D"></path><path d="M263 452v-2l1 1 1-1h1l1 1h1v1c-1 0-1 1-2 1l-1 2-1 2-2 1 1-4v-2z" class="l"></path><path d="M265 450h1l1 1-1 1h-1v-2z" class="d"></path><path d="M267 440l1 1h0l1 1-1 2h1v1h2l-1 1 1 2-2 1-1 1-1-1c-1 0-1 0-1-1l-1 1-1-1 2-6 1-2z" class="O"></path><path d="M267 449v-1c1 0 2-1 3-2l1 2-2 1-1 1-1-1z" class="P"></path><path d="M267 440l1 1c0 1-1 2-2 4v3l-1 1-1-1 2-6 1-2z" class="T"></path><path d="M271 430v2h1l-1 4h0c1-1 2-1 3-1v-1l1 1v2h1v3 3l-1-2c-1 1-1 2-2 3-1 0-1 0-2 1h-2v-1h-1l1-2-1-1h0l-1-1c1-3 3-7 4-10z" class="h"></path><path d="M268 441v-2h1l1 1c-1 1-1 1-1 2l-1-1z" class="b"></path><path d="M274 434l1 1v2l-1 1v-3-1z" class="O"></path><path d="M271 432h1l-1 4h0s-1 1-2 1h0c1-2 1-4 2-5z" class="d"></path><path d="M269 444v-1c1 0 1-1 2-1 1-1 2-2 4-3l1 1v3l-1-2c-1 1-1 2-2 3-1 0-1 0-2 1h-2v-1z" class="G"></path><path d="M263 463h0c1-2 4-5 6-6v1c-1 1-1 1-1 3 0 0-1 2-2 2l1 1 2-3h1c0 1 0 1-1 2v1c-1 2-4 3-5 5v1c-1 1-3 3-3 5l-1-1h-1v-2h-2l1-3v-1c0-1 0-2 1-3l2 1v-2-1h2z" class="H"></path><path d="M258 469v-1c0-1 0-2 1-3l2 1c0 2-1 4-2 6h-2l1-3z" class="e"></path><path d="M263 463h0c1-2 4-5 6-6v1c-1 1-1 1-1 3 0 0-1 2-2 2 0 1-2 3-3 4v2c0 1-1 2-1 2h-1v-1c1-1 1-2 1-3h-1v-3-1h2z" class="K"></path><path d="M261 463h2c0 2 0 2-1 4h-1v-3-1z" class="W"></path><path d="M275 432h2 1v1c0 1 0 3 1 4h0v4h0v2c0 1 0 1-1 2l-2 10h-1l1 1h2v1h0c-1 1-1 2-2 3l-1-2h0c-1 0-1 0-1 1-1 0-1 0-1 1l-1 1h-2-1l-2 3-1-1c1 0 2-2 2-2 0-2 0-2 1-3 1 0 1-1 1-2h0c2-1 2-3 3-4v1c1-1 2-3 2-5-1-1 0-3 1-5v-3-3h-1v-2l-1-1 1-2z" class="I"></path><path d="M278 433c0 1 0 3 1 4h0c-1 1-1 1-1 2v1c-1-2 0-3 0-4v-3z" class="L"></path><path d="M279 437v4h0v2c0 1 0 1-1 2v-2c0-1 0-2 1-2l-1-1v-1c0-1 0-1 1-2z" class="r"></path><path d="M275 432h2c-1 2-1 3-1 5h-1v-2l-1-1 1-2z" class="J"></path><path d="M273 460c0-2 1-3 2-5l1 1h2v1h0c-1 1-1 2-2 3l-1-2h0c-1 0-1 0-1 1-1 0-1 0-1 1z" class="P"></path><path d="M275 458l1-1h2c-1 1-1 2-2 3l-1-2z" class="K"></path><path d="M269 458c1 0 1-1 1-2h0c2-1 2-3 3-4v1c0 2-1 3-1 5l-3 3-2 3-1-1c1 0 2-2 2-2 0-2 0-2 1-3z" class="R"></path><path d="M282 439c8-3 18-6 24-13 3-3 4-10 4-14-1-2-1-3-3-4-1 0-1 0-2 1h-1 0v-2c1-2 2-3 4-4 2 0 4 0 6 1s4 3 5 6c1 7-3 16-7 21l-2 4h-2-1c2-2 3-5 5-8h-1c-2 5-7 10-12 12l-9 3v-1h-1 1l-1-1-6 1h-1-3 0v-4h0c-1-1-1-3-1-4v-1l2 1v5h0 1l1 1z" class="q"></path><path d="M282 439c-1 0-1 0-1 1h0c4 0 8-1 11-2s6-2 8-4c5-3 9-7 11-12 1-2 2-5 3-8 0 5 0 9-2 13h-1c-2 5-7 10-12 12l-9 3v-1h-1 1l-1-1-6 1h-1-3 0v-4h0c-1-1-1-3-1-4v-1l2 1v5h0 1l1 1z" class="C"></path><defs><linearGradient id="k" x1="300.533" y1="437.543" x2="298.224" y2="431.432" xlink:href="#B"><stop offset="0" stop-color="#d9d6dd"></stop><stop offset="1" stop-color="#fafffe"></stop></linearGradient></defs><path fill="url(#k)" d="M289 440h1l8-3c5-3 9-6 12-11 1-2 2-4 3-5 0 2-1 4-2 6-2 5-7 10-12 12l-9 3v-1h-1 1l-1-1z"></path><path d="M273 384l2-6v17c1 1 1 2 1 3h1v-3-1c0 6 1 13-1 19h2l-2 4-1 3c0 1-1 3-1 4h-1 0v2c-1 1-2 2-2 4h0c-1 3-3 7-4 10l-1 2h0l-1 1v-3c1-1 1-2 1-3s1-5 2-6l1-6s-1 0-1-1v-1-3-3c0-3-1-4-4-6v-1-2h-1c0-1-1-1-1-2v-1-1h0-1l-1-1v-2l1-1-1-3v-2c1 1 1 1 2 1v-3h1l1 1v-3l1-1h0c0 2 0 3 1 4h0v2h1 1v-10l1 8v-2-1h0c1 0 1-1 1-1 0-2 1-4 1-6h2z" class="M"></path><path d="M270 390c0-2 1-4 1-6h2c-1 3-2 5-2 8v6l-1 3-1-5 1-6z" class="B"></path><path d="M275 409v6c-1 2-1 3 0 5 0 1-1 3-1 4h-1c0-3-1-5-1-7 0-3 2-6 3-8z" class="Z"></path><path d="M275 395c1 1 1 2 1 3h1v-3-1c0 6 1 13-1 19h2l-2 4-1 3c-1-2-1-3 0-5v-6-6-8z" class="E"></path><path d="M275 403c0 3 0 8 1 11v-1h2l-2 4-1 3c-1-2-1-3 0-5v-6-6z" class="c"></path><path d="M270 401l1-3-2 27s-1 0-1-1v-1-3-3c0-3-1-4-4-6v-1-2-1h3v2s0-1 1-1c0-1 1 0 1-1v-3c0-1 0-2 1-3z" class="i"></path><path d="M267 409s0-1 1-1c0-1 1 0 1-1v8c-1-2-2-4-2-6z" class="e"></path><path d="M268 386l1 8v-2-1h0c1 0 1-1 1-1l-1 6 1 5c-1 1-1 2-1 3v3c0 1-1 0-1 1-1 0-1 1-1 1v-2h-3v1h-1c0-1-1-1-1-2v-1-1h0-1l-1-1v-2l1-1-1-3v-2c1 1 1 1 2 1v-3h1l1 1v-3l1-1h0c0 2 0 3 1 4h0v2h1 1v-10z" class="k"></path><path d="M262 403c2 1 1 3 1 5 0-1-1-1-1-2v-1-1h0v-1z" class="Z"></path><path d="M262 396c1 2 0 5 0 7v1h-1l-1-1v-2l1-1 1 1v-5z" class="J"></path><path d="M260 395c1 1 1 1 2 1v5l-1-1-1-3v-2z" class="K"></path><path d="M267 407c-1-2-1-6-1-8 1 1 2 3 3 5v3c0 1-1 0-1 1-1 0-1 1-1 1v-2z" class="f"></path><path d="M268 386l1 8v-2-1h0c1 0 1-1 1-1l-1 6v6c0-1-1-2-2-4-1-1-2-2-3-4v-3l1-1h0c0 2 0 3 1 4h0v2h1 1v-10z" class="Q"></path><path d="M279 539c5-3 9-6 12-11l1-1c-1 3-2 7-3 10h0c2-4 3-7 3-11v-5c-1-1-2-3-3-3h-1v-1c3 0 5 1 7 3 2 4 2 8 0 12-2 5-5 10-9 14-4 5-10 11-16 14h-1l2 1c-2 0-4 1-6 0-1 0-2 0-2 1h0l1 2h0c-1-1-2-1-3-1s-3 1-4 0l-1 1c1 1 1 1 0 2h0c-1-1-1-1-1-2l-1-1c0-1 0-1-1-2l3-3c1-2 2-4 2-6v-1l1 2 6-6 4-2 2-1c1-1 2-2 3-2 2-2 3-3 5-3z" class="n"></path><path d="M263 557c4-1 7-4 10-7l6-5c1-1 2-3 4-4-3 4-6 6-9 9-2 2-4 5-7 6-1 1-3 2-4 3h-1c-1 0-4 1-5 0l1-2c2 0 3 1 5 0h0z" class="h"></path><path d="M274 542c2-2 3-3 5-3 0 1-2 3-4 4-2 2-4 4-6 5-2 2-5 5-7 6l-1-1h0c1 0 1-1 2-1l3-3c2-1 2-2 3-2h1v-2h-1l2-1c1-1 2-2 3-2z" class="Q"></path><path d="M269 545h1v2h-1c-1 0-1 1-3 2l-3 3c-1 0-1 1-2 1h0c0 2-1 2-1 3 1 0 1 0 2 1h1 0c-2 1-3 0-5 0h0l1-4 6-6 4-2z" class="e"></path><path d="M275 551l5-5-3 5c-2 1-3 3-5 4-2 2-4 3-6 5h1 3-1l2 1c-2 0-4 1-6 0-1 0-2 0-2 1-2 0-3 0-4-1l5-1-1-1c1-1 3-2 4-3 3-1 5-4 7-6l1 1z" class="s"></path><path d="M274 550l1 1c-3 4-7 6-11 9l-1-1c1-1 3-2 4-3 3-1 5-4 7-6z" class="p"></path><path d="M258 551l1 2-1 4h0l-1 2c1 1 4 0 5 0h1l1 1-5 1c1 1 2 1 4 1h0l1 2h0c-1-1-2-1-3-1s-3 1-4 0l-1 1c1 1 1 1 0 2h0c-1-1-1-1-1-2l-1-1c0-1 0-1-1-2l3-3c1-2 2-4 2-6v-1z" class="H"></path><path d="M258 557h0l-1 2c1 1 4 0 5 0h1l1 1-5 1c-1 0-3 1-4 0 1-1 2-2 2-3l1-1z" class="Q"></path><path d="M277 551v1c1 0 2-2 3-2 1-2 2-3 3-4l3-3h0c0 1 0 1-1 2l1 1c-4 5-10 11-16 14h-3-1c2-2 4-3 6-5 2-1 3-3 5-4z" class="a"></path><path d="M281 414c2-7 6-14 10-19 9-9 20-12 33-12 6 0 14 1 18 6 1 0 1 0 1 1l-1-1c-8-4-19-3-27-1-10 3-19 10-24 19s-6 21-10 31h-1 0v-5l-2-1h-1-2l-1 2v1c-1 0-2 0-3 1h0l1-4h-1v-2h0c0-2 1-3 2-4v-2h0 1c0-1 1-3 1-4l1-3h1c1-1 2-2 4-3z" class="q"></path><path d="M281 423v-3h1c1 2 0 3 0 5-1-1-1-1-1-2z" class="Y"></path><path d="M279 428c1-1 1-3 2-5 0 1 0 1 1 2l-2 5c0-1-1-1-1-2z" class="E"></path><path d="M275 430c0-1 0-1 1-2 1 1 2 1 3 0h0c0 1 1 1 1 2v1 2l-2-1h-1-2v-2z" class="L"></path><path d="M275 430c0-1 0-1 1-2 1 1 2 1 3 0h0c0 1 1 1 1 2v1l-1-1h-4z" class="k"></path><path d="M279 418l2 2h1-1v3c-1 2-1 4-2 5h0c-1 1-2 1-3 0l3-10z" class="e"></path><defs><linearGradient id="l" x1="273.73" y1="419.198" x2="275.309" y2="432.374" xlink:href="#B"><stop offset="0" stop-color="#1b1b1c"></stop><stop offset="1" stop-color="#474848"></stop></linearGradient></defs><path fill="url(#l)" d="M276 417h1c1-1 2-2 4-3l-2 4-3 10c-1 1-1 1-1 2v2l-1 2v1c-1 0-2 0-3 1h0l1-4h-1v-2h0c0-2 1-3 2-4v-2h0 1c0-1 1-3 1-4l1-3z"></path><path d="M271 430h0c0-2 1-3 2-4v-2h0 1c0 3-1 6-2 8h0-1v-2z" class="O"></path><path d="M310 352c2-1 3-2 3-4 0-1 0-3-1-4s-2-2-4-3h-2v-1c2-1 4-1 5 0 2 0 4 2 4 3 1 2 1 5 0 6-1 3-5 7-8 8-2 0-4 0-6-1s-4-3-5-4c-5-5-8-10-11-16-2-7-2-14 1-20 2-3 4-6 8-7s9 0 13 2c1 1 2 2 3 4h0v1c2 0 5-1 7 0-2 0-5 0-7 1h-1c-2 1-4 4-5 7-1 2-1 4-2 6v6 4 1l1 1 1 1 2 1s1 0 1 1h1 1 1v-1h1c1 1 1 1 1 3s0 3-1 4l-1 1h0z" class="N"></path><path d="M302 341l1 1 1 1c0 1 0 1-1 2l-1-2v-2z" class="M"></path><path d="M296 311c1-1 1-1 2-1l-1 1h0 1l-1 1s-1 0-1 1h-1l-1-1h0c1-1 1-1 2-1z" class="q"></path><path d="M297 349l-1-1v-1l1 1c1 0 1 0 2-1 0-1-1-1-1-2v-1l1 1 2 2h0 0v1c-1 1-1 1-2 1v2l-1-1-1-1h0z" class="X"></path><path d="M301 347c1 0 1 1 1 1 1 0 1 0 2 1h-1l1 1-2 2h-3 0c-1-1-2-2-2-3h0l1 1 1 1v-2c1 0 1 0 2-1v-1h0z" class="M"></path><path d="M304 349h0l1-1c-1-2-3-3-4-4s-2-2-2-3v-1l3 3 1 2c1-1 1-1 1-2l2 1s1 0 1 1h1 1v2h-1l-1 1-2 2c1 2 2 2 1 3h-4 0v1h0c-1 0-2-1-3-2h0 3l2-2-1-1h1z" class="I"></path><path d="M304 343l2 1s1 0 1 1h1 1v2h-1l-1 1h-1c0-1-1-1-1-1-1-1-1-2-2-2 1-1 1-1 1-2z" class="X"></path><path d="M311 344c1 1 1 1 1 3s0 3-1 4l-1 1h0c-2 2-4 3-7 2h-1 0 0v-1h0 4c1-1 0-1-1-3l2-2 1-1h1v-2h1v-1h1z" class="P"></path><path d="M311 344c1 1 1 1 1 3s0 3-1 4l-1 1h-2c1-1 2-3 2-4s1-2 0-3v-1h1z" class="Q"></path><path d="M304 313c1 0 2 0 2 1h-2c-3 1-5 2-7 5s-3 7-3 11h0v3 1c-1-2-1-5-1-7v-1-2c0-1 1-2 1-3-1 2-1 4-2 5v1h0c0 3-1 6 0 9v2l-1-1c-1-7 0-13 3-19 1-2 2-4 4-4 2-1 4-1 6-1z" class="b"></path><path d="M292 327v-3c1-4 3-8 8-10-3 3-5 6-6 11 0 1-1 3 0 5h0v3 1c-1-2-1-5-1-7v-1-2c0-1 1-2 1-3-1 2-1 4-2 5v1z" class="o"></path><path d="M307 311c1 1 2 2 3 4h0v1c2 0 5-1 7 0-2 0-5 0-7 1h-1c-2 1-4 4-5 7-1 2-1 4-2 6v6 4l-1-5-1-1v-3-6c0-1 1-2 1-3h0c0-1 0-2 1-3-1 1-1 2-2 2 0 1-1 1-2 1h0l1-2c1-2 5-4 5-6h2c0-1-1-1-2-1 2-1 2-1 3-2z" class="X"></path><path d="M243 330h0 0v1h5c1 1 2 1 2 2 1 1 1 2 1 2v2c1 0 1 0 1 1 1 0 1 0 1 1s1 2 1 2v1c1 1 1 3 1 4h1l1-2v-2c0 1 0 1 1 1v-4 1h2 0 1v-1l1 1s0 1 1 1l-1 2 1 1h1 0 1 1c3 0 3 0 6 1 1 2 2 3 3 4v1l1 1v2l-1 2-1 1v1 1c-1 2-1 4-2 6v1c0 1 0 1 1 2-1 2-1 4-2 6h1c-1 2-1 3-2 4v1c-1 3-2 6-2 8v10h-1-1v-2h0c-1-1-1-2-1-4h0l-1 1v3l-1-1h-1l-1-1v-2l-1-1h-1c-1-2-3-4-5-6 0 0-2-3-2-4s-1-2-2-4l-3-6-1 1h0l-1-1-1-2-2-3c0-1-1-1-1-2l-1-1 1-2h-1c0-1 1-1 1-2h-1v-2h1v-1c-1-2-1-3-1-5l1-1h1v-3-2-3h0l1-2c0-1 0-1 1-2h-1l1-1h-2 0-3 0l-1-2v-2h0 0l1-1c0 1 0 1 1 1s2 0 3-1z" class="u"></path><path d="M273 357c1 2-4 9-4 11v3h-1v-1c0-3 2-10 5-13z" class="b"></path><path d="M272 365c0 1 0 1 1 2-1 2-1 4-2 6h1c-1 2-1 3-2 4v1h0c-1-4 0-10 2-13z" class="Z"></path><defs><linearGradient id="m" x1="269.963" y1="377.97" x2="262.536" y2="379.693" xlink:href="#B"><stop offset="0" stop-color="#c1bfc1"></stop><stop offset="1" stop-color="#e7e7e7"></stop></linearGradient></defs><path fill="url(#m)" d="M265 390l-1-4v-7-10c0-1 1-2 1-3v-1-3h1s1 1 2 1c-3 10-1 22-1 33h-1v-2h0c-1-1-1-2-1-4h0z"></path><path d="M273 353h3l-1 2-1 1c-4 2-5 3-6 7-1 0-2-1-2-1h-1v3 1c0 1-1 2-1 3v10 7l1 4-1 1v3l-1-1h-1l-1-1v-2l-1-1c-1-2-1-3-2-5h2v-1-4h0l1 1v-2h1v-1h0c0-2 0-3 1-4h0v-2c0-1 0-3 1-4 0-3 0-4 1-6v1l2-4 1-1c2-1 3-2 5-4z" class="P"></path><path d="M260 379h0l1 1v-2h1v-1h0c0-2 0-3 1-4h0v6 3 2 6l1 1v3l-1-1h-1l-1-1v-2l-1-1c-1-2-1-3-2-5h2v-1-4z" class="p"></path><path d="M261 390l1-1c0 2 1 3 1 4h-1l-1-1v-2z" class="C"></path><path d="M258 384h2c1 2 1 4 2 5l-1 1-1-1c-1-2-1-3-2-5z" class="D"></path><path d="M259 357s1-1 1-2c1 0 1-1 2-2h0c0 1 1 1 1 2 0 0 1 0 1 1 0 0 0 1-1 2 2 0 2 0 2 2v1c-1 2-1 3-1 6-1 1-1 3-1 4v2h0c-1 1-1 2-1 4h0v1h-1v2l-1-1h0l-1-1v-2l-1-3c0-6 0-11 1-16z" class="n"></path><path d="M263 358c2 0 2 0 2 2v1c-1 2-1 3-1 6-1 1-1 3-1 4v2h0c-1 1-1 2-1 4h0v1h-1v2l-1-1h0l-1-1v-2l1 1 1-1c-1-2 0-4 0-6 0-4 1-8 2-12z" class="e"></path><path d="M255 364l1 2c0 1 0 1-1 2 0 0 0 1 1 2h1c1 1 1 2 1 3l1 3v2l1 1v4 1h-2c1 2 1 3 2 5h-1c-1-2-3-4-5-6 0 0-2-3-2-4s-1-2-2-4c1-1 1-3 1-5l-1-2v-1l1 1c0-1 0-2 1-3h0 2l1-1z" class="L"></path><path d="M258 380c0-1-1-3-1-4h1c0 1 0 1 1 2h0v3l-1-1z" class="c"></path><path d="M255 364l1 2c0 1 0 1-1 2-1-1-1-1-2-1l-1 1v-3h2l1-1z" class="J"></path><path d="M254 376v-1h0c1 1 3 4 3 5l1 1v-1l1 1v-3l1 1v4 1h-2l-1-1c0-1-1-2-1-3-1-1-2-3-2-4z" class="h"></path><path d="M257 383h3v1h-2l-1-1z" class="L"></path><path d="M252 365h0v3l2 8c0 1 1 3 2 4 0 1 1 2 1 3l1 1c1 2 1 3 2 5h-1c-1-2-3-4-5-6 0 0-2-3-2-4s-1-2-2-4c1-1 1-3 1-5l-1-2v-1l1 1c0-1 0-2 1-3z" class="a"></path><path d="M261 339l1 1s0 1 1 1l-1 2-1 1-2 7v1h0v5c-1 5-1 10-1 16 0-1 0-2-1-3h-1c-1-1-1-2-1-2 1-1 1-1 1-2l-1-2-1-5h0c0-2 0-4-1-6l-1-6v-5l2 1h0v-1c1 1 1 3 1 4h1l1-2v-2c0 1 0 1 1 1v-4 1h2 0 1v-1z" class="K"></path><path d="M258 355l-1 2c-1 0-1-1-1-1v-4h1l1 1v2z" class="Q"></path><path d="M257 344v4 3h-1v-3l-1-2h1l1-2z" class="D"></path><path d="M254 342c1 1 1 3 1 4l1 2c-1 1 0 3-2 5h-1l-1-6v-5l2 1h0v-1z" class="f"></path><path d="M261 339l1 1s0 1 1 1l-1 2-1 1-2 7v1h0v5c-1 5-1 10-1 16 0-1 0-2-1-3h-1c-1-1-1-2-1-2 1-1 1-1 1-2 2-1 1-5 1-7v-1-1c1 0 1-2 1-2v-2h0v-2h-1v-3-4-2c0 1 0 1 1 1v-4 1h2 0 1v-1z" class="d"></path><path d="M261 339l1 1s0 1 1 1l-1 2-1 1v-2-2-1z" class="J"></path><path d="M257 342c0 1 0 1 1 1v-4 1h2 0 1v2l-2 1c1 1 0 4 0 5h-1v-1l-1 1v-4-2z" class="L"></path><path d="M260 340h0 1v2l-2 1 1-3z" class="G"></path><path d="M257 342c0 1 0 1 1 1v-4 1 7l-1 1v-4-2z" class="V"></path><path d="M262 343l1 1h1 0 1 1c3 0 3 0 6 1 1 2 2 3 3 4v1l1 1v2h-3c-2 2-3 3-5 4l-1 1-2 4v-1-1c0-2 0-2-2-2 1-1 1-2 1-2 0-1-1-1-1-1 0-1-1-1-1-2h0c-1 1-1 2-2 2 0 1-1 2-1 2v-5h0v-1l2-7 1-1z" class="a"></path><path d="M265 349c-1-1-1-1-1-2 1-1 2-1 3 0-1 0-2 1-2 2z" class="M"></path><path d="M270 351l1-1c-1-1-1-1-1-2 2 1 3 3 5 3v-1l1 1v2h-3l-1-1-1-1h-1z" class="d"></path><path d="M272 352c2 0 2-1 4-1v2h-3l-1-1z" class="U"></path><path d="M262 343l1 1h1 0 1 1c-4 2-5 4-7 8h0v-1l2-7 1-1z" class="R"></path><path d="M267 347c1 0 3 1 3 1 0 1 0 1 1 2l-1 1h-1c-1-1-1 0-2 0h0c-1 2-1 3-3 4v1c0-1-1-1-1-1 0-1-1-1-1-2h0v-1c0-1 2-2 3-3 0-1 1-2 2-2z" class="G"></path><path d="M262 353c1-2 3-3 4-3l1 1c-1 2-1 3-3 4v1c0-1-1-1-1-1 0-1-1-1-1-2h0z" class="N"></path><path d="M267 351c1 0 1-1 2 0h1 1l1 1 1 1c-2 2-3 3-5 4l-1 1-2 4v-1-1c0-2 0-2-2-2 1-1 1-2 1-2v-1c2-1 2-2 3-4h0z" class="F"></path><path d="M267 351c1 0 1-1 2 0l-1 3-1 1v-4z" class="V"></path><path d="M267 351h0v4c-1 1-1 4-2 5 0-2 0-2-2-2 1-1 1-2 1-2v-1c2-1 2-2 3-4z" class="Q"></path><path d="M243 330h0 0v1h5c1 1 2 1 2 2 1 1 1 2 1 2v2c1 0 1 0 1 1 1 0 1 0 1 1s1 2 1 2v1 1h0l-2-1v5l1 6c1 2 1 4 1 6h0l1 5-1 1h-2 0c-1 1-1 2-1 3l-1-1v1l1 2c0 2 0 4-1 5l-3-6-1 1h0l-1-1-1-2-2-3c0-1-1-1-1-2l-1-1 1-2h-1c0-1 1-1 1-2h-1v-2h1v-1c-1-2-1-3-1-5l1-1h1v-3-2-3h0l1-2c0-1 0-1 1-2h-1l1-1h-2 0-3 0l-1-2v-2h0 0l1-1c0 1 0 1 1 1s2 0 3-1z" class="o"></path><path d="M249 350c0 1 1 2 2 4v1c-1 1-1 1-2 1l-2-6h2z" class="L"></path><path d="M248 348l1-1v1l3 3v2l-1 1c-1-2-2-3-2-4h-2v-1l1-1h0z" class="V"></path><path d="M248 348h0l1 2h-2v-1l1-1z" class="D"></path><path d="M251 354l1-1v6l-1 1h0l-2-4c1 0 1 0 2-1v-1z" class="d"></path><path d="M242 345c1 2 1 5 2 7v1h-3v1c-1-2-1-3-1-5l1-1h1v-3z" class="C"></path><path d="M252 359h0l1 1 1-1 1 5-1 1h-2 0l-1-5 1-1z" class="L"></path><path d="M252 359h0l1 1v3c-1 0-1-2-2-3l1-1z" class="r"></path><path d="M254 359l1 5-1 1-1-2v-3l1-1z" class="H"></path><path d="M249 346c1 1 1 2 2 2h0 1v-1l1 6c1 2 1 4 1 6h0l-1 1-1-1h0v-6-2l-3-3v-2z" class="J"></path><path d="M252 359l1-1 1 1h0l-1 1-1-1z" class="L"></path><path d="M244 352l3 6c-2-1-2-1-3-2h-1 0v1 2l-2 1v-1h-1c0-1 1-1 1-2h-1v-2h1v-1-1h3v-1z" class="Z"></path><path d="M241 355c1 1 1 1 1 3l-1 2v-1h-1c0-1 1-1 1-2h-1v-2h1z" class="s"></path><path d="M243 357v-1h0 1c1 1 1 1 3 2l3 8v1 1l1 2c0 2 0 4-1 5l-3-6-1 1h0l-1-1-1-2-2-3c0-1-1-1-1-2l-1-1 1-2v1l2-1v-2z" class="t"></path><path d="M245 360l1 1-2 1-1-1v-1h2z" class="O"></path><path d="M246 361l2 4-1 1h0c-1-1-1-2-2-3 0 0 0-1-1-1l2-1z" class="G"></path><path d="M248 365l2 1v1 1l-1 1v1-1h-1l-1-3h0l1-1z" class="J"></path><path d="M247 366h0c1 1 2 1 2 3h0-1l-1-3z" class="L"></path><path d="M243 357v-1h0 1c1 1 1 1 3 2l3 8-2-1-2-4-1-1c0-1-1-2-2-3z" class="D"></path><path d="M242 364l1-1 2 2v1c1 1 1 2 2 3h1 1v1-1l1-1 1 2c0 2 0 4-1 5l-3-6-1 1h0l-1-1-1-2-2-3z" class="G"></path><path d="M250 368l1 2-1 2c-1-1-1-1-1-2v-1l1-1z" class="d"></path><path d="M243 330h0 0v1h5c1 1 2 1 2 2 1 1 1 2 1 2v2c1 0 1 0 1 1 1 0 1 0 1 1s1 2 1 2v1 1h0l-2-1v5 1h-1 0c-1 0-1-1-2-2v2-1l-1 1h0l-1 1-3-6-1-1-1 1v-3h0l1-2c0-1 0-1 1-2h-1l1-1h-2 0-3 0l-1-2v-2h0 0l1-1c0 1 0 1 1 1s2 0 3-1z" class="D"></path><path d="M247 338c-1-1-1-2-1-3h1 1v2l-1 1h0z" class="K"></path><path d="M248 337c0 2 1 3 2 5 0 2 1 4 1 6h0c-1 0-1-1-2-2s-1-6-2-8l1-1z" class="b"></path><path d="M244 336v2h1l1 2c1-1 1-1 1-2h0c1 2 1 7 2 8v2-1l-1 1h0l-1 1-3-6-1-1-1 1v-3h0l1-2c0-1 0-1 1-2z" class="G"></path><path d="M245 338l1 2c-1 2 1 5 2 8l-1 1-3-6h1v-5z" class="C"></path><path d="M244 336v2h1v5h-1l-1-1-1 1v-3h0l1-2c0-1 0-1 1-2z" class="W"></path><path d="M244 336v2 2c-1 1-1 1-2 0h0l1-2c0-1 0-1 1-2z" class="R"></path><path d="M243 330h0 0v1h5c1 1 2 1 2 2 1 1 1 2 1 2v2c1 0 1 0 1 1 1 0 1 0 1 1s1 2 1 2v1 1h0l-2-1c-1-2-1-3-1-5-1-1-2-3-3-3-2-1-3 0-4 1h-2 0-3 0l-1-2v-2h0 0l1-1c0 1 0 1 1 1s2 0 3-1z" class="e"></path><path d="M243 330h0 0v1c-1 1-1 1-2 1v1h2c-1 1 0 1-1 1v1h-3 0l-1-2v-2h0 0l1-1c0 1 0 1 1 1s2 0 3-1z" class="K"></path><path d="M239 335c1-1 2-1 3-1h0v1h-3 0z" class="d"></path><path d="M353 414c0-1-1-2-1-3v-1h1c0-1 0-2-1-3h0c0 2 0 2-1 3-1-1-1-2-1-3-1-4-2-7-3-10 0-2-1-4-2-6-2-6-7-14-13-17-3-2-6-4-8-6-2-3-3-7-3-11 1-3 2-7 5-9 1-1 3-1 5 0 0 0 1 1 1 2v2h-5c-1 3-2 4-2 7 2 7 10 10 15 14 3 2 5 4 7 6 1 2 2 3 2 5h0 2v-2l1 1 2-2c0-1 1-1 2-2v-4s0 1 1 2l1 1v2l1 1v6c1 2 1 5 1 8-1 3 0 6 0 8s1 4 1 5h0-1c0 1 0 1 1 2h0c0 2 1 5 2 5l-1 1v2c1 6 1 11 1 17-1 1-1 3-1 5v8 1c1 1 1 2 1 3v2 2h2v2c-1 1-1 1-1 2v1h1s0 1 1 1c1 1 3 1 5 1h1l-1 7-1 2h0l-1 1v4l1 1h0c0 2 0 2-1 3v1 1c-2 3-5 7-8 9l-1 1c1 1 2 1 3 3h-1c0 1 0 1-1 2l-1-1h1v-2h-1l-6 6c-5 4-9 7-14 10-1 2-2 3-4 4-1 2-4 4-6 5h-4c-2 2-5 5-8 7-2 2-4 3-5 5-2 1-3 2-4 3h-1c-6 5-12 11-19 15h-1-1l-1 1c1-1 2-2 2-3h0l3-3c3-3 8-6 11-9 10-9 21-20 22-35 0-6-1-13-6-18-4-5-9-7-15-7-4 0-7 1-10 4-1 1-2 3-2 5 0 1 0 2 1 3 3 0 2-3 4-4l1 1c1 1 1 1 1 2 0 2-1 3-3 4-1 1-4 2-6 1-1 0-2-1-3-3s0-5 0-7c2-4 5-7 9-9 5-2 12-2 17 1 2 1 4 3 6 4 0-1 0-1 1-2h1c2 1 4 4 5 6 3 4 4 12 3 17-1 4-2 8-4 12 2-1 3-3 4-4 4-6 7-11 8-17-2 3-5 5-8 7 3-3 7-6 8-10 3-6 7-12 10-18 0-2 1-5 0-7 3-4 4-12 4-17v-2 1l-2-1c0-1 0-3-1-5 0-1-1-2-1-3v-2c-1 0-1-1-1-1 0-1 0-1 1-2l2-1-1-1c1-1 1-1 1-2h0l1-1c0 2 1 3 2 4l1 1 1-1-1-3c0-2-1-4-1-7l-2-3h0c0-1-1-2-1-3s1-2 1-3c1 2 2 4 3 5h0 1v-1z" class="q"></path><path d="M349 431h2l1 2v2h-1c-1-1-1-2-2-4z" class="N"></path><path d="M355 425c1 2 1 4 1 6h-1l-1-1c1-2 1-3 0-4l1-1z" class="h"></path><path d="M353 447l-2-5h0c1 0 2 2 2 3s1 1 1 2h0c0 1 0 2-1 3v-3z" class="AB"></path><path d="M347 428c1-1 1-1 1-2h0l1-1c0 2 1 3 2 4v2h-2l-1-2h0l-1-1z" class="M"></path><path d="M348 429h3v2h-2l-1-2z" class="a"></path><path d="M368 468v1c1 0 2 1 3 1l-1 2h0l-1 1-2 3v-1c0-2 1-5 1-7z" class="j"></path><path d="M368 469c1 0 2 1 3 1l-1 2h0c-2-1-2-2-2-3z" class="m"></path><path d="M353 414l2 11-1 1c0-2-1-4-1-5 0-2-1-4-1-6h0 1v-1z" class="k"></path><path d="M355 431h1c0 2 0 6 2 8l-1 1-1-1v1c0-2-1-3-1-5-1-1-1-1-2-1 0-1 1-1 1-2h1v-1z" class="m"></path><path d="M353 447v3c1-1 1-2 1-3 1 3 1 5 0 7v4c-1-1-1-2-1-3h-1l1-8z" class="g"></path><path d="M359 402l1 6c0 1 0 1 1 2h0c0 2 1 5 2 5l-1 1v2l-4-15s0-1 1-1z" class="U"></path><path d="M357 392h0c0 3 1 7 2 10-1 0-1 1-1 1l-4-10c1-1 1-1 1 0h1l1-1z" class="R"></path><path d="M355 388l2-2c0 1 1 3 1 4l-1 2h0l-1 1h-1c0-1 0-1-1 0l-1-4v-2l1 1v1l1-1z" class="W"></path><path d="M355 393v-2c0-1 1-1 2-2v3l-1 1h-1z" class="P"></path><path d="M353 429l1 2v1c0 1-1 1-1 2 1 4 2 9 1 13 0-1-1-1-1-2 1-3 0-7-1-10v-2l-1-2v-2l1 1 1-1z" class="U"></path><path d="M353 429l1 2-2 2-1-2v-2l1 1 1-1z" class="w"></path><path d="M354 465c1 1 1 0 1 1-2 5-4 10-7 15l-3 6v-1l5-11 4-10z" class="AB"></path><path d="M353 434c1 0 1 0 2 1 0 2 1 3 1 5 1 4 1 10 0 15v-4l-2 3c1-2 1-4 0-7h0c1-4 0-9-1-13z" class="Q"></path><path d="M354 454l2-3v4c0 2-1 4-1 7-1 1-1 2-1 3l-4 10-2-1 6-16v-4z" class="o"></path><path d="M356 375s0 1 1 2l1 1v2 2c-1 1-1 3-1 4-1-1-1-2-2-2v4l-1 1v-1l-1-1v2c-1-1-2-3-3-4-1 0-1 1-1 1v-2h2v-2l1 1 2-2c0-1 1-1 2-2v-4z" class="P"></path><path d="M356 379c0 1 0 2-1 3l-2 3v2 2c-1-1-2-3-3-4-1 0-1 1-1 1v-2h2v-2l1 1 2-2c0-1 1-1 2-2z" class="AC"></path><path d="M349 410c1 2 2 4 3 5 0 2 1 4 1 6 0 1 1 3 1 5 1 1 1 2 0 4l1 1v1h-1v-1l-1-2-1-3c0-2-1-4-1-7l-2-3h0c0-1-1-2-1-3s1-2 1-3z" class="D"></path><path d="M349 410c1 2 2 4 3 5 0 2 1 4 1 6h0c-2-1-2-4-3-6l-1 1h0c0-1-1-2-1-3s1-2 1-3z" class="O"></path><path d="M363 456h2v2c-1 1-1 1-1 2v1h1s0 1 1 1c1 1 3 1 5 1h1l-1 7c-1 0-2-1-3-1v-1c0-1-1-3-2-4s-2-2-3-2h-1 0l1-6z" class="B"></path><path d="M358 380l1 1v6c1 2 1 5 1 8-1 3 0 6 0 8s1 4 1 5h0-1l-1-6c-1-3-2-7-2-10l1-2c0-1-1-3-1-4l-2 2v-4c1 0 1 1 2 2 0-1 0-3 1-4v-2z" class="G"></path><path d="M369 473v4c-1 1-1 3-2 4-1 3-4 7-7 9h0l-1-1-4 4h-1c3-4 7-8 10-12 1-2 1-4 2-5l1-1v1l2-3z" class="J"></path><path d="M369 473v4c-1 1-1 3-2 4v-5l2-3z" class="G"></path><path d="M358 439l2-5c-1 4-1 8-1 13 0 7-1 13-4 19 0-1 0 0-1-1 0-1 0-2 1-3 0-3 1-5 1-7 1-5 1-11 0-15v-1l1 1 1-1z" class="g"></path><path d="M369 477l1 1h0c0 2 0 2-1 3v1 1c-2 3-5 7-8 9l-1 1c1 1 2 1 3 3h-1c0 1 0 1-1 2l-1-1h1v-2h-1 0-1l-1-1 1-1h-2l-5 2c1-1 1-2 2-2h1l4-4 1 1h0c3-2 6-6 7-9 1-1 1-3 2-4z" class="I"></path><path d="M370 478c0 2 0 2-1 3v1 1c-2 3-5 7-8 9v-1s2-3 3-3c2-3 4-6 6-10z" class="R"></path><path d="M352 455h1c0 1 0 2 1 3l-6 16 2 1-5 11v1l-1 2 1 1c-2 3-5 6-7 8h-1c0-1 1-2 1-3s1-3 0-5c0-3 3-6 5-9 5-7 8-17 9-26z"></path><path d="M348 474l2 1-5 11c-1 1-1 2-2 3l-1-1 6-14z" class="a"></path><path d="M342 488l1 1c1-1 1-2 2-3v1l-1 2 1 1c-2 3-5 6-7 8h-1c0-1 1-2 1-3h0 1c1-3 2-5 3-7z" class="M"></path><path d="M352 495l5-2h2l-1 1 1 1h1 0l-6 6c-5 4-9 7-14 10-1 2-2 3-4 4-1 2-4 4-6 5h-4c0-1 1-2 2-2 2-1 2-2 3-3l5-4c2-2 5-4 8-6 0-1 0-1 1-2 3-2 5-5 7-8z" class="v"></path><path d="M345 503c1 0 2-1 3-1l2 1c-2 1-4 2-5 3-2 1-3 3-5 5-1 1-3 2-4 3-2 2-3 3-5 4a30.44 30.44 0 0 1 8-8c1-1 3-2 4-3s1-2 1-2c0-1 0-1 1-2z" class="M"></path><path d="M352 495l5-2h2l-1 1 1 1c-3 3-6 5-9 8l-2-1c-1 0-2 1-3 1 3-2 5-5 7-8z" class="F"></path><path d="M358 494l1 1c-3 3-6 5-9 8l-2-1c1-2 4-3 6-4l4-4z" class="Q"></path><path d="M345 490c7-8 13-17 17-27 1 1 2 2 3 4h0c0 4-3 9-5 12-6 9-14 17-23 25l-19 19h0l20-25c2-2 5-5 7-8z"></path><path d="M354 332l1 1h0l1 1c7-1 16-1 23 0 11 0 23 0 33 2 20 3 41 13 56 25 10 8 18 18 24 28 5 9 9 19 12 29 1 6 3 12 4 19l1 15c-3 1-5 2-7 3-2 2-3 5-6 6-1-1-1-1-1-2v-1h-1c0 1 0 2 1 3 1 0 1 1 2 0 1 0 2-1 3-2l1-1 2 1c2 0 3 0 5 1 0 10-1 19-2 29-1 2-1 5-2 7-1 0-2 0-3-1l-1 1c-1 1-4 1-6 2-4 1-5 2-7 6l-1-1-1 1h-1v2-2h-1 0c-1 3 0 5 0 7-2-2-4-3-7-3h-1c-4 1-7 3-9 6l-2 5-1 1v-1c-1-1-2-1-3-1-4 1-6 3-8 6l-6 6h-1s-1 1-2 1c0-1 1-1 1-2v-1s-1-1-1-2l1-2c2-4 3-8 3-13 0-2 0 0-2-1l-2-2v1c1 3 1 7 0 11-1 2-1 3-1 5l-2 4c0 2-1 4-2 5v-1c-1 2-1 3-2 5h0-1c-1 1-3 3-3 5v1 2l-1 2-3 5c-1 1-1 2-2 3l-4 4v1l1 1h-1-1l-1 1-1-1h-1c0 1-1 2-2 3s-3 3-4 3c-1 1-1 2-2 2l-1 1 1 1 1-1v1s-1 1-1 2c-1 1-1 2-2 3 0-1 0-1-1-2 1-1 1-1 1-2h-2c-1 0-2 1-2 2 0-1-1-1-1-2-1 0-1 1-2 1-1 1-2 1-3 2h-3l4-3h-1v-2c-1 0-1 0-1-1h-1v-1-1l-1 1s-1 0-1-1v-8h-1c0-1 0-2 1-3v-1c-1 1-2 2-3 2 0-2-2-4-2-6l-1-3-2-7v-2c1-1 1-1 2-1v-1-2-1-10-1c-1-1-1-4-1-5h1v1c1 0 1 0 2-1l-1-1s-1 0-1-1c-2-1-4-4-4-6 0-1-1-1-2-2h3l-3-3-1-1v-1c-1 0-1-1-2-1 0-1-1-3-1-4 0-2 0-3-1-5l2-3c1-1 1-1 2-1h-2v-1h-1l-1-1h0l-1-1h-3l1-1c0-1 0-1 1-1h2l3-2v-1-1l-1-1c-1-1-1-1-1-2v-1c-1 0-1-1-2-1 1-2 1-4 1-6h-1c-2 4-4 9-7 13v-1-1c1-1 1-1 1-3h0l-1-1v-4l1-1h0l1-2 1-7h-1c-2 0-4 0-5-1-1 0-1-1-1-1h-1v-1c0-1 0-1 1-2v-2h-2v-2-2c0-1 0-2-1-3v-1-8c0-2 0-4 1-5 0-6 0-11-1-17v-2l1-1c-1 0-2-3-2-5h0c-1-1-1-1-1-2h1 0c0-1-1-3-1-5s-1-5 0-8c0-3 0-6-1-8v-6l-1-1v-2l-1-1c-1-1-1-2-1-2v-1c0-1-1-2-1-3v-2-1c1 0 1-1 1-1 0-1 0-2-1-2l1-1-2-1 1-1c-1-2-1-2-2-3v-1h0c-1-1 0-1-1-1h0c-2-2-1-3-1-5v-6-5c0-2 1-3 1-5 1-1 1-2 2-4z" class="q"></path><path d="M439 497v-1h1v3c-1 1-1 1-1 2h0v-4z" class="L"></path><path d="M412 456c0 2 1 4 1 6-1-1-1-2-2-2v-1c0-1 0-2 1-3z" class="E"></path><path d="M440 475c0 1 1 2 1 2v1 2c0 1-1 2-1 2l-1 1v-1c0-2 0-5 1-7z" class="H"></path><path d="M436 506h-3c-1 0-1-1-2-2 0-1 1-2 1-3 1 0 1 0 2 1 1 0 1 1 1 2v1l1 1z" class="n"></path><path d="M397 417l5 12c-1 1-1 1-1 2l-1-2-1-3c0-2-1-3-1-4s-1-3-2-3l1-2z" class="F"></path><path d="M439 486v2l1 1 1 4h-1c0 1 0 1 1 2 1 0 1-1 2 0 0 1 1 1 1 2h-1l-1-1h-2 0-1v1c-1-3-1-8 0-11z" class="G"></path><path d="M368 357c0-4 2-9 5-13h0v1c-1 2-1 3-2 5h0c0 1 0 2-1 2-1 2 0 6 0 8l2 4h0c-2-2-3-4-4-7z" class="T"></path><path d="M475 480l1 28h-1l-1-22h1v-6z" class="O"></path><path d="M371 350c0 3 1 6 2 9-1 1-1 1-1 2 1 0 1 1 1 1v1h1 1 0c1 1 1 2 1 4l-1 1c0-2-1-3-3-4l-2-4c0-2-1-6 0-8 1 0 1-1 1-2h0z" class="V"></path><path d="M415 557c0 2-1 3-2 4l-5 6h1c2-2 5-6 8-7-2 3-5 5-7 7-2 1-3 3-5 4h0l-1-2c2-1 3-3 5-5l6-7z" class="a"></path><path d="M388 409l2-5h1v3c2 2 4 5 5 8l-1 1c-1-1-2-2-2-3l-1 1v-1l-1 1v-1h-1l-2-4z" class="l"></path><path d="M388 409l2-5h1v3l-1 1c0 1 0 1 1 2l2 3-1 1v-1l-1 1v-1h-1l-2-4z" class="W"></path><path d="M424 523c2 3 3 7 2 11 0 8-5 14-9 21l-1-1c5-6 9-14 8-22 0-1 1-1 1-2v-1-2l-1-1v-1-2z" class="h"></path><path d="M390 413h1v1l1-1v1l1-1c0 1 1 2 2 3l1-1 1 2-1 2c1 0 2 2 2 3s1 2 1 4l1 3h0-1l-2-3-2-2c-1 0-1-1-2-2h0 2l-1-1-4-8z" class="C"></path><path d="M399 426v-1c-1 0-2-1-2-2l1-1c0 1 1 2 1 4z" class="L"></path><path d="M392 414l1-1c0 1 1 2 2 3l1-1 1 2-1 2c0-1 0-1-1-2h-1c-1 0-2-2-2-3z" class="S"></path><path d="M368 357c1 3 2 5 4 7 1 2 2 3 2 4-2 0-2 0-3 1h0c-1 0-1 0-2-1l-2 1h0l-3-8c1 0 2 0 3-1 0-1 1-2 1-3z" class="M"></path><path d="M364 361c1 0 2 0 3-1l-1 1v1h1 1v1 1l1 1c0 1 0 2 1 3h-1l-2 1h0l-3-8z" class="N"></path><path d="M474 486l-1-22-1-13c0-4-1-8 0-12 0-1 0-2 1-2 1-1 2-1 2 0 1 0 2 1 2 2s0 1-1 1h-1l1-1h0l-1-1h-2c-1 3 0 6 0 9l1 15 1 18v6h-1z" class="L"></path><path d="M408 461c1 0 2-1 3-1s1 1 2 2h1l3 15c-1 0-2-1-2-2s-1-2-1-3c-1 0-1 1-1 1h-1v2-1c0-1 0-2-1-3v-1c0-1-1-2-1-3l-2-2-2-2v-1l2-1z" class="E"></path><path d="M412 466l1 1c-1 1 0 5 0 6-1-2-2-4-2-6l1-1z" class="T"></path><path d="M408 461c1 0 2-1 3-1s1 1 2 2h1c-1 1-1 5-1 5l-1-1h-1c-1 0-1 1-1 1l-2-2-2-2v-1l2-1z" class="g"></path><path d="M406 463v-1l2-1v4l-2-2z" class="B"></path><path d="M354 332l1 1h0l1 1-2 6h0v3c0 1 0 2-1 3v4l1 1h1v-1c1 1 1 1 1 2l-1 1-2 4v1h0c-1-1 0-1-1-1h0c-2-2-1-3-1-5v-6-5c0-2 1-3 1-5 1-1 1-2 2-4z" class="I"></path><path d="M351 352v1h1v-5h0v6 2s1 0 1 1v1h0c-1-1 0-1-1-1h0c-2-2-1-3-1-5z" class="p"></path><path d="M351 346v-5c0 2 0 4 1 5v1 1h0v5h-1v-1-6z" class="N"></path><path d="M354 332l1 1-1 3-1 4v1 1c0 1 0 4-1 5v-1c-1-1-1-3-1-5s1-3 1-5c1-1 1-2 2-4z" class="n"></path><path d="M405 461l1 2 2 2 2 2c0 1 1 2 1 3v1c1 1 1 2 1 3h-1c0 1 0 1 1 2v1 1h-2l-1 1c-1 0-1 0-2-1h0l1-1h-1-1v-1-1h-1 0-1 0c0-1 0-2-1-3v-2l-1-1v-1-1h0v-1l2-2h0v-1-1c1 0 1-1 1-1z" class="s"></path><path d="M406 467h1v2s-1 0-1 1v1c-1 0-1-1-1-1 1-1 1-2 1-3z" class="S"></path><path d="M408 467c0 1 0 2 1 3h0c-1 1-1 1-1 2-1 0-1 1-2 2v-3-1c0-1 1-1 1-1l1-1v-1z" class="k"></path><path d="M404 464c1 1 1 2 1 3v1-1c-1 1-1 2-1 2v1h-1l-1-1v-1-1h0v-1l2-2z" class="D"></path><path d="M405 461l1 2 2 2 2 2c0 1 1 2 1 3v1c1 1 1 2 1 3h-1l-1-3h-1v-1h0c-1-1-1-2-1-3v1l-1 1v-2h-1-1c0-1 0-2-1-3h0v-1-1c1 0 1-1 1-1z" class="R"></path><path d="M404 464h0 2l2 3v1l-1 1v-2h-1-1c0-1 0-2-1-3h0z" class="l"></path><path d="M404 470v1l1 1v2h1 0c1-1 1-2 2-2 0-1 0-1 1-2v1h1l1 3c0 1 0 1 1 2v1 1h-2l-1 1c-1 0-1 0-2-1h0l1-1h-1-1v-1-1h-1 0-1 0c0-1 0-2-1-3v-2h1z" class="c"></path><path d="M407 477c0-2 0-2 1-4h1c0 1 0 1 1 2v-1l1 2h0-1-1l-1 1h-1z" class="E"></path><path d="M409 471h1l1 3c0 1 0 1 1 2v1 1h-2l-1 1c-1 0-1 0-2-1h0l1-1 1-1h1 1 0l-1-2c0-1 0-2-1-3z" class="Y"></path><path d="M377 375v1c1-1 2-3 3-4 1-2 2-4 4-5 1 0 2-1 3-1h0 3 0c2 1 3 3 4 4v2c3 2 5 3 6 7v1h-1c-1-2-2-4-4-6h-1c-1 0-1 0-2 1l-3 3c-1 2-3 3-4 5 0 1-2 2-2 3 0-2 1-3 2-4l3-3v-1l2-2 1-1c0-1 0-1 1-2h0c1-1 1-1 1-2-1-1-1-1-2-1h-1-3l-1 1c-1 0-5 2-6 2-1 1-2 2-1 4h0c-1 1-1 2-2 3h-1l-2-1c-2-1-3-5-3-8 1 1 2 3 3 4 1 0 0 1 2 2 0 0 0-1 1-2z" class="V"></path><path d="M380 373c1-1 1-3 3-4v-1c2 0 2 0 3-1h1c1 0 2 0 3 1h0c2 0 3 1 4 3h-1c-1-1-1-1-2-1h-1-3l-1 1c-1 0-5 2-6 2z" class="n"></path><path d="M381 391c5-3 14-6 19-5 3 1 6 2 8 5v2h0c1 1 1 3 0 4-1 3-4 4-6 4-2 1-5 0-7 0-1 0-3 0-4 1-2 1-3 1-5 3h0-1c-1 1-2 1-3 2v1h-1c0-1 0-1-1-1 4-4 10-7 15-8 2 0 5 1 7 1s4-1 5-2 1-3 1-4c-1-1-1-2-2-3l-1-1h-4c-1-1-2-1-4-1h-4l2-1v-1c-9 2-14 4-20 11v-1c2-3 3-4 6-6z" class="S"></path><path d="M395 387c4 0 6 0 9 2l2 2-1-1h-4c-1-1-2-1-4-1h-4l2-1v-1z" class="N"></path><path d="M395 388h6c1 1 2 1 4 2h-4c-1-1-2-1-4-1h-4l2-1z" class="f"></path><path d="M439 501c0-1 0-1 1-2v2l2 1v4c0 1 1 2 1 3 1 3 1 7 0 11-1 2-1 3-1 5l-2-2v-1h-1 0l-1 1h-2v-2c1-2 0-6 0-8 0-1 1-3 0-4v-2c-1 0 0 0 0-1l-1-1v-1c0-1 0-2-1-2 2 0 2 1 3 2v2h0c1 2 1 3 2 5v-10h0z" class="N"></path><path d="M439 506h2l-1 10c1 1 1 3 2 4h1c-1 2-1 3-1 5l-2-2v-1h-1 0l-1 1c0-1 1-3 1-4v-13z" class="H"></path><path d="M440 516c1 1 1 3 2 4h1c-1 2-1 3-1 5l-2-2v-1-6z" class="a"></path><path d="M439 501c0-1 0-1 1-2v2l2 1v4c0 1 1 2 1 3 1 3 1 7 0 11h-1c-1-1-1-3-2-4l1-10h-2v-5z" class="N"></path><path d="M439 501c0-1 0-1 1-2v2l1 5h-2v-5z" class="Z"></path><path d="M355 350l1-2c1 0 1 0 1 1 4 2 6 7 7 12 1 2 2 5 3 8h0c1 1 2 2 2 3s0 2-1 3l-1-1-1-1-1-2c-2-3-2-6-4-9h-2 0l-2-1v1c-1 0-1 1-1 2l-2-1 1-1c-1-2-1-2-2-3v-1h0 0v-1l2-4 1-1c0-1 0-1-1-2z" class="N"></path><path d="M360 358l1 4h-2 0l-2-1c1 0 2-1 2-2l1-1z" class="j"></path><path d="M355 350l1-2c1 0 1 0 1 1 4 2 6 7 7 12 1 2 2 5 3 8h0c1 1 2 2 2 3s0 2-1 3l-1-1-1-1-1-2c0-1 0-1 1-1 0-2-1-4-2-6-2-5-3-10-7-15h0l-1 1h-1z" class="h"></path><path d="M367 369h0c1 1 2 2 2 3s0 2-1 3l-1-1v-5z" class="o"></path><path d="M355 350h1l4 8-1 1c0 1-1 2-2 2v1c-1 0-1 1-1 2l-2-1 1-1c-1-2-1-2-2-3v-1h0 0v-1l2-4 1-1c0-1 0-1-1-2z" class="F"></path><path d="M353 357l2-4 1-1c0 3 0 5-1 7-1 0-1 0-2-1v-1z" class="N"></path><path d="M354 363l1-1c-1-2-1-2-2-3v-1h0 0c1 1 1 1 2 1h3 1c0 1-1 2-2 2v1c-1 0-1 1-1 2l-2-1z" class="g"></path><path d="M380 345c1 0 1-1 2-2s2-1 4-1h1c1 1 2 1 3 1l3 3c0 2 0 3-1 4-1 2-2 3-4 4l-2-2c1-2 2-2 3-3 0-2 0-2-1-4v-1c-2 0-3-1-5 0-4 4-5 16-5 21-1 2 0 4-1 6v-4h-1v3l-1-1v-1l1-1c0-2 0-3-1-4h0-1-1v-1s0-1-1-1c0-1 0-1 1-2l2-6 1-6h1c1 0 2-1 3-2z" class="T"></path><path d="M380 345l-1 5v-2l-2-1c1 0 2-1 3-2z" class="AD"></path><path d="M388 344c1 0 2 0 3 2 1 1 1 2 1 3-1 2-2 3-3 3h-1-1v-1c2-1 3-1 3-2 0-2 0-3-1-4h-1v-1z" class="M"></path><path d="M376 347h1l2 1v2l-2 8c0 1 0 3-1 4h0l-1-1v2h0-1-1v-1s0-1-1-1c0-1 0-1 1-2l2-6 1-6z" class="a"></path><path d="M376 347l1 1h0c-2 4-1 10-1 14l-1-1v-8l1-6z" class="O"></path><path d="M373 359l2-6v8 2h0-1-1v-1s0-1-1-1c0-1 0-1 1-2z" class="b"></path><path d="M374 363v-5h1v5h-1z" class="P"></path><path d="M372 364c2 1 3 2 3 4v1l1 1v-3h1v4 4c-1 1-1 2-1 2-2-1-1-2-2-2-1-1-2-3-3-4 0 3 1 7 3 8l2 1h1c1-1 1-2 2-3h0c-1-2 0-3 1-4 1 0 5-2 6-2l1-1h3 1c1 0 1 0 2 1 0 1 0 1-1 2h0 0c-4 0-8 1-11 3l-3 5-3 3h-1-1c-1 0-1-1-2-2 0 0 0 1-1 0l-1-1h-1v-3l1-1-1-2c1-1 1-2 1-3s-1-2-2-3l2-1c1 1 1 1 2 1h0c1-1 1-1 3-1 0-1-1-2-2-4h0z" class="AC"></path><path d="M374 379l2 1h-1c1 1 1 1 1 2l-1 1-1-1v-1-1-1z" class="G"></path><path d="M372 379c1 2 2 4 2 5h-1c-1 0-1-1-2-2v-1c1-1 1-1 1-2z" class="P"></path><path d="M371 369c1-1 1-1 3-1 0 2 1 3 0 4v1c-1-1-2-2-3-4z" class="N"></path><path d="M369 372c2 2 2 5 3 7 0 1 0 1-1 2v1s0 1-1 0l-1-1h-1v-3l1-1-1-2c1-1 1-2 1-3z" class="p"></path><path d="M369 377c0 1 1 3 2 4v1s0 1-1 0l-1-1h-1v-3l1-1z" class="Y"></path><path d="M380 373c1 0 5-2 6-2l1-1h3 1c1 0 1 0 2 1 0 1 0 1-1 2h0 0v-1c-5 0-9 1-12 4l-1 1h0c-1-2 0-3 1-4z" class="a"></path><path d="M432 543v1 2l-1 2-3 5c-1 1-1 2-2 3l-4 4v1l1 1h-1-1l-1 1-1-1h-1c0 1-1 2-2 3s-3 3-4 3c-1 1-1 2-2 2l-1 1 1 1 1-1v1s-1 1-1 2c-1 1-1 2-2 3 0-1 0-1-1-2 1-1 1-1 1-2h-2c-1 0-2 1-2 2 0-1-1-1-1-2-1 0-1 1-2 1-1 1-2 1-3 2h-3l4-3 2-1c2 0 2 0 4-1h0c2-1 3-3 5-4 2-2 5-4 7-7 1-2 3-4 4-6l4-4c0-2 1-3 1-4h1v2c0 2-2 4-3 7 1-1 2-3 2-4 2-2 4-5 6-8z" class="P"></path><path d="M415 564c1-2 2-2 3-3 0-1 0-1 1-1 0-1 0-2 1-2h1l1 2v1l1 1h-1-1l-1 1-1-1h-1c0 1-1 2-2 3l-1-1z" class="p"></path><path d="M415 564l1 1c-1 1-3 3-4 3-1 1-1 2-2 2l-1 1 1 1 1-1v1s-1 1-1 2c-1 1-1 2-2 3 0-1 0-1-1-2 1-1 1-1 1-2h-2c-1 0-2 1-2 2 0-1-1-1-1-2 2-1 4-1 6-3v-1c1-1 2-2 4-3l2-2z" class="f"></path><defs><linearGradient id="n" x1="399.402" y1="441.223" x2="411.887" y2="455.566" xlink:href="#B"><stop offset="0" stop-color="#474748"></stop><stop offset="1" stop-color="#605f60"></stop></linearGradient></defs><path fill="url(#n)" d="M400 446c1-2 1-4 2-6 1-1 1-2 2-3l1 1h1c1 4 2 8 4 12 0 2 2 4 2 6-1 1-1 2-1 3v1c-1 0-2 1-3 1l-2 1v1l-1-2-3-6 1-3-1-2v-1l-2-3z"></path><path d="M403 452v2c0 2 1 3 2 4 1 0 1 0 2-1 0 1 1 1 1 2h1v-2h1l1 2v1c-1 0-2 1-3 1l-2 1v1l-1-2-3-6 1-3z" class="u"></path><path d="M357 361l2 1h0 2c2 3 2 6 4 9l1 2c0 2 0 3-1 5v3l-1 1v2 1h-1-2l-1 1c0 1 0 1-1 1v-6l-1-1v-2l-1-1c-1-1-1-2-1-2v-1c0-1-1-2-1-3v-2-1c1 0 1-1 1-1 0-1 0-2-1-2l1-1c0-1 0-2 1-2v-1z" class="z"></path><path d="M356 364c0-1 0-2 1-2 0 2 1 2 2 4v5 1h-1l-1-4-1-1c0-1 0-2-1-2l1-1z" class="B"></path><path d="M359 379h1v-2c0 2 0 5 1 7h0c1-2 1-6 1-7 1 1 1 2 1 3l1 2v2 1h-1-2l-1 1c0 1 0 1-1 1v-6-2z" class="u"></path><path d="M363 380l1 2v2 1h-1v-5z" class="s"></path><path d="M355 369v-1c1 0 1-1 1-1l1 1 1 4h1v-1c1 2 1 4 1 6v2h-1v2l-1-1v-2l-1-1c-1-1-1-2-1-2v-1c0-1-1-2-1-3v-2z" class="j"></path><path d="M355 369v-1c1 0 1-1 1-1l1 1c-1 0-1 1-1 2l-1 1v-2z" class="u"></path><path d="M359 371c1 2 1 4 1 6v2h-1c0-2-1-4-1-7h1v-1z" class="F"></path><path d="M361 362c2 3 2 6 4 9l1 2c0 2 0 3-1 5v3l-1 1c1-2 0-5 0-7v-1h-1c-1 0-1-2-1-3 0-2-2-3-2-5 0-1-1-2-1-4h2z" class="B"></path><path d="M440 470l2 1h0c1 2 1 6 1 8 1 5 2 9 3 13 0 4 0 7 1 11h1l1 1-1 1c-1 1-1 3-1 4 1 1 0 1 0 2 0-2 0 0-2-1l-2-2v1c0-1-1-2-1-3v-4l-2-1v-2-3h0 2l1 1h1c0-1-1-1-1-2-1-1-1 0-2 0-1-1-1-1-1-2h1l-1-4-1-1v-2-4 1l1-1s1-1 1-2v-2-1s-1-1-1-2v-5z" class="F"></path><path d="M440 470l2 1c-1 6 1 10 2 16l-2-1c0-1-1-1-1-1 0-1 1-2 1-3l-1-1v-4s-1-1-1-2v-5z" class="K"></path><path d="M441 493l1 1v-1c0-1 0-2-1-3v-2h0l2 2c0-1 0-1 1-2h0c1 4 1 8 1 13 0 0 0-1-1-1v-3c0-1-1-1-1-2-1-1-1 0-2 0-1-1-1-1-1-2h1z" class="Q"></path><path d="M441 477v4l1 1c0 1-1 2-1 3 0 0 1 0 1 1l2 1v1h0c-1 1-1 1-1 2l-2-2h0v2c1 1 1 2 1 3v1l-1-1-1-4-1-1v-2-4 1l1-1s1-1 1-2v-2-1z" class="J"></path><path d="M440 496h0 2l1 1h1v3c1 0 1 1 1 1l1 3c1 2 0 3 0 5h1c1 1 0 1 0 2 0-2 0 0-2-1l-2-2v1c0-1-1-2-1-3v-4l-2-1v-2-3z" class="V"></path><path d="M440 496h0 2l1 1v3l-1 2v4-4l-2-1v-2-3z" class="f"></path><path d="M426 546c3-6 6-14 6-21 0-1 0-2-1-3h-2c-1-1-3-3-3-5 0-1 1-2 2-3 1 0 2 0 3 1 3 1 3 4 4 7v2l1-1h2l1-1h0 1v1l2 2-2 4c0 2-1 4-2 5v-1c-1 2-1 3-2 5h0-1c-1 1-3 3-3 5-2 3-4 6-6 8 0 1-1 3-2 4 1-3 3-5 3-7v-2h-1z" class="F"></path><path d="M429 543h1c-1 2-2 4-3 5v-2c1-1 1-2 2-3z" class="e"></path><path d="M432 538l1-1 1 1c-2 2-3 4-4 5h-1l1-4 1 1 1-2h0z" class="X"></path><path d="M440 523l2 2-2 4c0 2-1 4-2 5v-1c1-3 2-6 2-10z" class="p"></path><path d="M432 531c1 1 1 2 2 2l-2 5h0l-1 2-1-1 2-8z" class="N"></path><path d="M433 520h0c0 1 1 2 1 3 2 3 0 7 0 10-1 0-1-1-2-2 1-3 1-7 0-10l1-1z" class="X"></path><path d="M432 521l-2-2h-1l1 2-1-1c-1 0-2-2-2-3s0-1 1-2h3c1 1 2 3 2 5l-1 1z" class="n"></path><path d="M433 537c1-2 1-4 2-7 0-2 0-3 1-5 1-1 1-1 2 0s1 1 1 2c-1 3-4 8-5 11l-1-1z" class="y"></path><path d="M418 524c0-2 1-5 1-7v3l1 1c1 0 3 1 4 2v2 1l1 1v2 1c0 1-1 1-1 2 1 8-3 16-8 22-1 2-3 4-5 6 0 1-1 1-1 2 1-2 2-5 2-7l1-2c3-8 4-18 5-26v-3z" class="q"></path><path d="M418 524c0-2 1-5 1-7v3l1 1c1 0 3 1 4 2v2 1l1 1v2 1c0 1-1 1-1 2-1-3-1-6-3-9l-2-2-1 3z" class="H"></path><path d="M392 373h0c-1 1-1 1-1 2l-1 1-2 2v1l-3 3c-1 1-2 2-2 4 0 1-1 1-1 1-1 1-1 2-1 3v1c-3 2-4 3-6 6-2 1-3 3-3 5l-1-1v-1c1-1 1-3 0-4v-1-1h-1 0c0-1-2-1-3-1v-2c1-2 1-5 1-7v-1-2h1l1 1c1 1 1 0 1 0 1 1 1 2 2 2h1 1l3-3 3-5c3-2 7-3 11-3z" class="M"></path><path d="M368 381h1l1 1c1 1 1 0 1 0 1 1 1 2 2 2h1 1c-1 2-3 4-3 6 0 1-1 2-1 3v1h-1 0c0-1-2-1-3-1v-2c1-2 1-5 1-7v-1-2z" class="S"></path><path d="M369 381l1 1c0 3 2 4 1 7l-1-1c0-2-1-5-1-7z" class="s"></path><path d="M371 382c1 1 1 2 2 2h1 1c-1 2-3 4-3 6 0 1-1 2-1 3 0-1-1-1-1-2s0-2 1-2c1-3-1-4-1-7 1 1 1 0 1 0z" class="B"></path><path d="M392 373h0c-1 1-1 1-1 2l-1 1-2 2v1l-3 3c-1 1-2 2-2 4 0 1-1 1-1 1-2 1-3 4-5 5-1 1-2 2-3 2h-1c0-2 3-5 5-7 0-1 1-2 2-3 0-1 0-1 1-2h0v-1l-3 3v-3l3-5c3-2 7-3 11-3z" class="N"></path><path d="M483 504c1-7 1-15 3-21 1-3 4-8 7-9 2-1 2-1 3 0 1 0 2 1 2 2 1 2 0 4-1 5-2 4-2 9-2 13h1c2 0 3 1 5 1l-1 1c-1 1-4 1-6 2-4 1-5 2-7 6l-1-1-1 1h-1v2-2h-1z" class="W"></path><path d="M487 496v2h0c-1 1-1 2-2 2-1-2 0-6 1-9 0 1 1 1 1 1v4zm4-9h0l1 1c0 2 0 5-1 8-1 0-1 0-1-1 0-3-1-6 1-8z" class="y"></path><path d="M486 503c1-3 3-4 5-5 3-2 5-3 9-2-1 1-4 1-6 2-4 1-5 2-7 6l-1-1z" class="N"></path><path d="M493 475c1 0 2-1 3 0 1 0 1 1 1 2s0 3-1 4v1c-1 2-1 4-2 6 0 2 1 5 0 6 0 1-1 1-1 1h-1c0-3 1-5 1-8l-1-1c-1 0-2 0-3 1-1 3 0 7 0 10h-2v-1-4s-1 0-1-1v-1c0-5 3-11 7-15z" class="q"></path><path d="M496 479v-2h1c0 1 0 3-1 4h-1v1l-1-1c1 0 1-1 1-2h1z" class="M"></path><path d="M493 475c1 0 2-1 3 0 1 0 1 1 1 2h-1v2l-1-1h-2c0-1 0-1 1-2l-1-1z" class="N"></path><path d="M442 463v-1c2-5 1-12 1-17v-6c2 3 1 12 1 16s1 8 1 12c1 5 0 10 1 14 1 6 2 12 2 18h1c1-2 1-3 1-4h1c1 1 1 1 1 3-2 2-3 3-4 5 1 0 2-1 2-1 1-1 1-1 2-1 2 0 3 0 3 2 1 1 1 2 1 3 0 2-2 4-3 6l-3 9c-2 3-2 5-4 8 0 0-1 0-1 1 0 0-1 1-2 1 0-1 1-1 1-2v-1s-1-1-1-2l1-2c2-4 3-8 3-13 0-1 1-1 0-2 0-1 0-3 1-4l1-1-1-1h-1c-1-4-1-7-1-11-1-4-2-8-3-13 0-2 0-6-1-8h0l-2-1c1-2 2-4 2-7h0z" class="S"></path><path d="M442 463h0c1-1 1-1 1 0l5 40h0-1c-1-4-1-7-1-11-1-4-2-8-3-13 0-2 0-6-1-8h0l-2-1c1-2 2-4 2-7z" class="f"></path><path d="M442 463c0 1 1 2 1 2-1 1 0 1 0 2v2c0 1-1 1-1 2h0l-2-1c1-2 2-4 2-7z" class="j"></path><path d="M449 504c1-1 2-2 3-2s2 1 2 2c1 1 1 2 0 4l-2 6-3 6c-1 3-2 6-5 9v-1s-1-1-1-2l1-2c2-4 3-8 3-13 0-1 1-1 0-2 0-1 0-3 1-4l1-1z" class="o"></path><path d="M449 520v-3c1-1 1 0 1-1h0c0-2 1-3 1-4h0l1 1v1l-3 6zm0-16c1-1 2-2 3-2s2 1 2 2c0 0-1 1-2 1 0 1-1 1-2 2v3c0 2 0 4-1 5v1c0 1 0 1-1 2v1 1c-1 0-1 1-1 1 0 1-1 1-1 2v1l-1 1c-1 0-1-1-1-1 2-4 3-8 3-13 0-1 1-1 0-2 0-1 0-3 1-4l1-1z" class="N"></path><defs><linearGradient id="o" x1="365.718" y1="400.993" x2="361.873" y2="400.184" xlink:href="#B"><stop offset="0" stop-color="#444445"></stop><stop offset="1" stop-color="#605e5f"></stop></linearGradient></defs><path fill="url(#o)" d="M366 373l1 1 1 1 1 2-1 1v3 2 1c0 2 0 5-1 7v2c1 0 3 0 3 1h0 1v1 1c1 1 1 3 0 4v1l1 1c0 1-1 3-2 4s-1 4-3 5v-1c0 3-1 5-1 7l-1 7v3l-2 11h0v-3c0-6 0-11-1-17v-2l1-1c-1 0-2-3-2-5h0c-1-1-1-1-1-2h1 0c0-1-1-3-1-5s-1-5 0-8c0-3 0-6-1-8 1 0 1 0 1-1l1-1h2 1v-1-2l1-1v-3c1-2 1-3 1-5z"></path><path d="M364 393v-2-1h0l1 2c0 1 0 2-1 3v-1-1z" class="c"></path><path d="M361 396c0-2 0-2 1-3 0 1 1 1 1 2-1 3 1 7 0 11l-2-10z" class="F"></path><path d="M366 397c1-1 1-2 1-3h0 1c0-1 1 0 2 0h0l-1 3v2c-1-1-2-2-3-2z" class="u"></path><path d="M368 394c0-1 1 0 2 0h0l-1 3h0c0-1 0-1-1-2v-1z" class="B"></path><path d="M360 395l1 1 2 10-1 1c0-1 0-4-1-4h-1c0-2-1-5 0-8z" class="T"></path><path d="M360 403h1c1 0 1 3 1 4v1c1 1 2 2 2 3s-1 1-1 2v2c-1 0-2-3-2-5h0c-1-1-1-1-1-2h1 0c0-1-1-3-1-5z" class="S"></path><path d="M362 408c1 1 2 2 2 3s-1 1-1 2c0-1-1-2-1-3v-2z" class="m"></path><path d="M366 373l1 1 1 1 1 2-1 1v3 2 1c0 2 0 5-1 7l-1 1-1-2 1-1h-1 0v2 1l-1-2h0v1 2c-1 0-1-1-2-1v1c-1 1-1 1-1 3l-1-1c0-3 0-6-1-8 1 0 1 0 1-1l1-1h2 1v-1-2l1-1v-3c1-2 1-3 1-5z" class="R"></path><path d="M364 385v-1c1 2 1 3 0 5h0c-1 0-1 0-1-1l1-3z" class="D"></path><path d="M367 382v-4h1v3 2l-1-1z" class="l"></path><path d="M363 385h1l-1 3c-1-1-2-1-3-2l1-1h2z" class="c"></path><path d="M367 382l1 1v1c0 2 0 5-1 7l-1 1-1-2 1-1c0-1 1-2 1-3-1-1-1-2-1-4h1z" class="i"></path><path d="M370 394h1v1 1c1 1 1 3 0 4v1l1 1c0 1-1 3-2 4s-1 4-3 5v-1c0 3-1 5-1 7l-1 7v3l-2 11h0v-3c0-6 0-11-1-17v-2l1-1v-2c0-1 1-1 1-2 1-3 1-6 1-9 0-2 1-3 1-5 1 0 2 1 3 2v-2l1-3z" class="Y"></path><path d="M370 394h1v1 1c1 1 1 3 0 4v1l1 1c0 1-1 3-2 4s-1 4-3 5v-1c0-2 0-5 1-7l1-4v-2l1-3z" class="P"></path><path d="M367 410c0-2 0-5 1-7v6h0c0-1 1-1 1-2l1-1c-1 1-1 4-3 5v-1z" class="K"></path><path d="M375 398c6-7 11-9 20-11v1l-2 1h4c2 0 3 0 4 1h4l1 1c1 1 1 2 2 3 0 1 0 3-1 4s-3 2-5 2-5-1-7-1c-5 1-11 4-15 8-4 5-7 10-10 16-1 3-2 5-2 8l-3 9-2 12c0-1 0-2-1-3v-1-8c0-2 0-4 1-5v3h0l2-11v-3l1-7c0-2 1-4 1-7v1c2-1 2-4 3-5s2-3 2-4c0-2 1-4 3-5v1z" class="n"></path><path d="M398 392c1 1 2 1 3 2h0v1h-2c-1-1-2-2-4-2 1-1 2-1 3-1z" class="o"></path><path d="M365 424l2-2v2 3h-1c-1 1-1 3-1 4v2c-1 4 0 7-1 11v-1c0-1 1-1 0-2l1-1h0l-2 12c0-1 0-2-1-3v-1-8c0-2 0-4 1-5v3h0l2-11v-3z" class="N"></path><path d="M365 424l2-2v2l-2 3v-3z" class="X"></path><defs><linearGradient id="p" x1="381.357" y1="406.131" x2="374.395" y2="401.455" xlink:href="#B"><stop offset="0" stop-color="#cccbce"></stop><stop offset="1" stop-color="#f0eeef"></stop></linearGradient></defs><path fill="url(#p)" d="M375 398c6-7 11-9 20-11v1l-2 1h4-3l-1 1c1 1 2 1 3 1h2 1l2 2v1h0c-1-1-2-1-3-2-1 0-2 0-3 1-1 0-1 1-2 0l-4 3c-3 1-6 3-9 5-2 2-4 4-6 7h0c-1 1-2 3-3 5-1 3-3 5-4 9l-2 2 1-7c0-2 1-4 1-7v1c2-1 2-4 3-5s2-3 2-4c0-2 1-4 3-5v1z"></path><path d="M393 393h-2c0 1-1 1-1 1h-1l1-1h0 0l1-1h2 2 3c-1 0-2 0-3 1-1 0-1 1-2 0z" class="a"></path><path d="M372 402c0-2 1-4 3-5v1c-1 2-3 5-3 7-2 4-5 8-5 12h-1c0-2 1-4 1-7v1c2-1 2-4 3-5s2-3 2-4z" class="G"></path><path d="M367 417v1c1-3 3-5 5-8l1-2c0-1 1-1 1-2 1-2 4-4 6-5h0c-2 2-4 4-6 7h0c-1 1-2 3-3 5-1 3-3 5-4 9l-2 2 1-7h1z" class="e"></path><path d="M375 398c6-7 11-9 20-11v1l-2 1h4-3l-1 1c1 1 2 1 3 1h-6-2v1h-2l-1 1h-1l-2 2h0l-4 3-4 4c0 2-1 2-2 3h0c0-2 2-5 3-7z" class="X"></path><path d="M385 405h1 0c2-2 3-2 5-3v2h-1l-2 5 2 4 4 8 1 1h-2 0c1 1 1 2 2 2l2 2 2 3h1 0l1 2c0-1 0-1 1-2l4 9h-1l-1-1c-1 1-1 2-2 3-1 2-1 4-2 6l2 3-1 1h-1-1v2c-1-1-1-1-2-1-1 2 0 3-1 4l-2-2c0-1-1-3-2-4s-1-2-2-3-1-3-2-4l-1-1c-1-1-2-2-2-3h0-2c-1-2-2-3-3-4 0-2-1-3-1-5h2c0-1 1-2 2-2l-1-2c0-2 0-2 1-3l-1-2h0c-2-2-2-4-3-7l1-3 1-2h1v-1c1-1 2-1 3-2z" class="k"></path><path d="M393 420h-1c-1-1-2-2-2-4v-1l3 5z" class="c"></path><path d="M390 428c1 1 2 0 3 2v1h1 1 0v1h-2c-1-1-2-2-3-4z" class="C"></path><path d="M388 409l2 4 4 8h0l-1-1-3-5c0-1-1-2-1-3h-1l-1 2 2 2h-1v2l-1 1-1-1-1-1 1-1-2-2h1l3-5z" class="j"></path><path d="M387 414l2 2h-1v2l-1 1-1-1 1-4z" class="s"></path><path d="M387 419l1-1v-2h1l3 6c1 1 3 3 3 5v1c0 1 0 1-1 1l1 2h-1-1v-1c-1-2-2-1-3-2 0 0 0-1-1-1l-3-5c0-1 1-1 1-2v-1z" class="h"></path><path d="M385 405h1 0c2-2 3-2 5-3v2h-1l-2 5-3 5h-1l2 2-1 1 1 1 1 1v1c0 1-1 1-1 2v-1l-1 1h0c0 1 0 1-1 1l-1-1h0l-1-2h0c-2-2-2-4-3-7l1-3 1-2h1v-1c1-1 2-1 3-2z" class="f"></path><path d="M381 408h1v-1c1-1 2-1 3-2l-1 3c-1 2-2 4-2 6-1-2-1-3-2-4l1-2z" class="L"></path><path d="M380 410c1 1 1 2 2 4v6h0c-2-2-2-4-3-7l1-3z" class="G"></path><path d="M383 422h1c0-1-1-3-1-4s1-2 1-4l2 2-1 1 1 1 1 1v1c0 1-1 1-1 2v-1l-1 1h0c0 1 0 1-1 1l-1-1z" class="T"></path><path d="M386 421c-1 0-1-1-1-1v-1l1 1h1c0 1-1 1-1 2v-1z" class="C"></path><path d="M385 405h1 0c2-2 3-2 5-3v2h-1l-2 5-3 5-1-1v-3-2l1-3z" class="a"></path><path d="M400 429l1 2c0-1 0-1 1-2l4 9h-1l-1-1c-1 1-1 2-2 3-1 2-1 4-2 6l2 3-1 1h-1c0-1-1-2-1-2l-2-3-1-3-1-1c0-3-1-5-2-7v-1l2 1h0c2-1 2-2 2-3 0-2 0-2 1-3l1 1h1 0z" class="AB"></path><path d="M400 429l1 2v1h-1v1c1 1 1 1 1 2 0 2 0 4-1 5v-3c1-1 1-2 0-3h-2c-1 1 0 2-1 3h0c-1-1-2-2-2-3h0c2-1 2-2 2-3 0-2 0-2 1-3l1 1h1 0z" class="U"></path><path d="M400 429l1 2v1h-1c0-1 0-2-1-2l-2 1c0-2 0-2 1-3l1 1h1 0z" class="R"></path><path d="M401 431c0-1 0-1 1-2l4 9h-1l-1-1c-1 1-1 2-2 3-1 2-1 4-2 6v-6c1-1 1-3 1-5 0-1 0-1-1-2v-1h1v-1z" class="B"></path><path d="M383 422h0l1 1 2 3s1 1 2 1h0c1 1 2 1 2 3 0 1 1 1 1 2 1 0 0 0 1 1h0v2c1 1 0 1 1 2 0 1 2 3 2 4l1 1 1 3 2 3s1 1 1 2h-1v2c-1-1-1-1-2-1-1 2 0 3-1 4l-2-2c0-1-1-3-2-4s-1-2-2-3-1-3-2-4l-1-1c-1-1-2-2-2-3h0-2c-1-2-2-3-3-4 0-2-1-3-1-5h2c0-1 1-2 2-2l-1-2c0-2 0-2 1-3z" class="h"></path><path d="M397 445l2 3-2 1v1h0-1c-1 0-1-1-1-1v-1l1 2c1-1 1-2 1-3v-2z" class="S"></path><path d="M390 446l1-1 1 1v-1h0c1 1 2 2 2 3h0c-1 0-1-1-2-1v2c-1-1-1-2-2-3z" class="D"></path><path d="M399 448s1 1 1 2h-1v2c-1-1-1-1-2-1-1 2 0 3-1 4v-1c1-1 0-2 0-4h1 0v-1l2-1z" class="Y"></path><path d="M379 429h2c0-1 1-2 2-2 0 1 1 2 1 3s1 2 1 3h-1c0-1-1-3-2-4 0 1-1 1-1 1-1 0-1-1-2-1z" class="D"></path><path d="M396 448h-1v-1c-1-2-3-5-3-6h1l1 1 1 2c1-1 1-1 1-2l1 3v2l-1 1z" class="Z"></path><path d="M396 442l1 3v2l-1 1c0-1-1-3-1-4 1-1 1-1 1-2z" class="T"></path><path d="M379 429c1 0 1 1 2 1l5 8v1l-1-1h0-2c-1-2-2-3-3-4 0-2-1-3-1-5z" class="Z"></path><path d="M383 422h0l1 1 2 3s1 1 2 1v1c1 1 1 2 2 3v1h-1v2h-1c-1-1-1-3-3-5l-1 1c0-1-1-2-1-3l-1-2c0-2 0-2 1-3z" class="W"></path><path d="M386 426s1 1 2 1v1h-1l-1 1c-1-1-1-2-2-3h2z" class="E"></path><path d="M383 422h0l1 1 2 3h-2l-2-1c0-2 0-2 1-3z" class="B"></path><path d="M380 407c1 0 1 0 1 1l-1 2-1 3c1 3 1 5 3 7h0l1 2c-1 1-1 1-1 3l1 2c-1 0-2 1-2 2h-2c0 2 1 3 1 5 1 1 2 2 3 4h2 0c0 1 1 2 2 3l1 1c1 1 1 3 2 4s1 2 2 3 2 3 2 4l2 2c1-1 0-2 1-4 1 0 1 0 2 1v-2h1 1l1-1v1l1 2-1 3 3 6s0 1-1 1v1 1h0l-2 2v1h0v1 1l1 1v2c1 1 1 2 1 3h0-2c0 1-1 1-1 1h-2v1h2c-1 1-3 1-4 1v1l-1-1v3h0c0 1 0 1 1 2h1c0-1 0-1 1-1v3l-1-1-1 1-1 1v-1h-1c-1 0-1-1-2-2l-1-1c-2-1-5 0-8 0l-3 1h-1v-1-1l-1-1c-1-1-1-1-1-2v-1c-1 0-1-1-2-1 1-2 1-4 1-6h-1c-2 4-4 9-7 13v-1-1c1-1 1-1 1-3h0l-1-1v-4l1-1h0l1-2 1-7h-1c-2 0-4 0-5-1-1 0-1-1-1-1h-1v-1c0-1 0-1 1-2v-2h-2v-2-2l2-12 3-9c0-3 1-5 2-8 3-6 6-11 10-16z" class="C"></path><path d="M384 478c0-2 0-3 1-5l1 1c0 1 0 2 1 3h0-1v-1c-1 2-1 3-1 4l-1 1v-3z" class="D"></path><path d="M386 471l1-1c0 2 1 3 1 5 0 1 1 3 1 4v1c-1 0-2 0-3-1v-2h1v2h1c0-3-1-5-2-8z" class="Z"></path><path d="M387 466l1 2 1 3 1 2-1 1c0 1 0 1 1 2 0 0 0 1 1 2-1 0-1 1-1 1h-1c0-1-1-3-1-4 0-2-1-3-1-5v-1h0l-1-1v-1l1-1z" class="k"></path><path d="M387 466l1 2 1 3 1 2-1 1-2-5h0l-1-1v-1l1-1z" class="Z"></path><path d="M384 459h1v4c0 1 1 2 2 3l-1 1v1l1 1h0v1l-1 1-2-3c-1-3-2-5-2-8 1-1 1 0 2 0v-1z" class="D"></path><path d="M384 456l1 1 1 2c0 1 1 1 2 2h0v2h2 0v2s1 1 0 1c0 2 0 3 1 5h-2l-1-3-1-2c-1-1-2-2-2-3v-4h-1l-1-1c0-1 0-1 1-2z" class="s"></path><path d="M388 468h0c0-1 1-1 1-1l-1-3h1s0 1 1 1c0 0 1 1 0 1 0 2 0 3 1 5h-2l-1-3z" class="W"></path><path d="M386 453l2-1 1 2h0l1 2h0v3l2 5h0c-1 0-1-1-2-1h0 0-2v-2h0c-1-1-2-1-2-2l-1-2-1-1h0v-1-1h1v-1h1z" class="S"></path><path d="M385 457v-1h1s1 1 1 3h0-1l-1-2z" class="D"></path><path d="M386 459h1l2 2c0 1 0 1 1 2h-2v-2h0c-1-1-2-1-2-2z" class="Z"></path><path d="M386 453l2-1 1 2h0l1 2h-1c1 1 1 2 1 3h0c-1 0-2-1-3-2l-1-4z" class="H"></path><path d="M391 471l1 2h0 1v1h1c-1 1-1 2-1 3l1 1h0c1 0 1 0 2-1v1h0v3h0c0 1 0 1 1 2h1c0-1 0-1 1-1v3l-1-1-1 1-1 1v-1h-1c-1 0-1-1-2-2l-1-1c-2-1-5 0-8 0v-1l1-1c0-1 0-2 1-4v1 2c1 1 2 1 3 1v-1h1s0-1 1-1c-1-1-1-2-1-2-1-1-1-1-1-2l1-1-1-2h2z" class="C"></path><path d="M392 482h3 0v1h-2l-1-1z" class="H"></path><path d="M395 483l2 2-1 1v-1h-1c-1 0-1-1-2-2h2z" class="D"></path><path d="M394 478h0c1 0 1 0 2-1v1h0v3h-3v-1-1h0l1-1z" class="m"></path><path d="M391 471l1 2h0 1v1h1c-1 1-1 2-1 3l1 1-1 1h0v1l-1 1c-1-1-1-2-1-3-1-1-1-2-1-2-1-1-1-1-1-2l1-1-1-2h2z" class="D"></path><path d="M393 473v1h1c-1 1-1 2-1 3l1 1-1 1h0v1c0-1-1-3-1-5v-2h0 1z" class="R"></path><path d="M391 471l1 2v2c-2-1-2-1-2-2l-1-2h2z" class="c"></path><path d="M390 463h0c1 0 1 1 2 1h0 0l2 1h1 0c1 0 1 1 2 1v-2c1 1 1 1 2 1h0 3 0v1 1h0v1 1l1 1v2c1 1 1 2 1 3h0-2c0 1-1 1-1 1h-2v1h2c-1 1-3 1-4 1v1l-1-1h0v-1c-1 1-1 1-2 1h0l-1-1c0-1 0-2 1-3h-1v-1h-1 0l-1-2c-1-2-1-3-1-5 1 0 0-1 0-1v-2z" class="J"></path><path d="M395 472v1 3l-1-2h-1l2-2z" class="H"></path><path d="M394 474l1 2 1 1c1 0 1-1 2-1l1 1h2c-1 1-3 1-4 1v1l-1-1h0v-1c-1 1-1 1-2 1h0l-1-1c0-1 0-2 1-3z" class="c"></path><path d="M400 470v-1h0 1v2h1v-2l1 1v2c1 1 1 2 1 3h0-2c0 1-1 1-1 1h-2v-1c-1-2 1-3 1-5h0z" class="L"></path><path d="M401 471h1v1h0l-1-1z" class="Z"></path><path d="M401 476c0-1 0-2 1-3v2h0c0 1-1 1-1 1z" class="c"></path><path d="M397 464c1 1 1 1 2 1h0 3 0v1 1h0v1 1 2h-1v-2h-1 0v1c-1 0-1 1-2 1-1-2-1-2-1-4h-1l1-1v-2z" class="Z"></path><path d="M402 467c-1 0-1-1-1-1l1-1v1 1h0z" class="J"></path><path d="M397 467h1v1c1 0 1 0 2-1v1h1 1v1 2h-1v-2h-1 0v1c-1 0-1 1-2 1-1-2-1-2-1-4z" class="H"></path><path d="M390 463h0c1 0 1 1 2 1h0 0l2 1h1 0c1 0 1 1 2 1l-1 1c0 2 0 3 1 4l-1 1 1 1-1 1-1-1v-1l-2 2v-1h-1 0l-1-2c-1-2-1-3-1-5 1 0 0-1 0-1v-2z" class="E"></path><path d="M393 468l1 1 1 3-2 2v-1c0-1-1-3-1-3s1-1 1-2z" class="l"></path><path d="M395 465c1 0 1 1 2 1l-1 1c0 2 0 3 1 4l-1 1 1 1-1 1-1-1v-1l-1-3-1-1v-2c1-1 0 0 2 1v-2z" class="H"></path><path d="M394 469v-1h1l1 4 1 1-1 1-1-1v-1l-1-3z" class="t"></path><path d="M402 449v1l1 2-1 3 3 6s0 1-1 1v1 1h0l-2 2v-1h0-3 0c-1 0-1 0-2-1v2c-1 0-1-1-2-1h0-1l-2-1h0l-2-5v-3h0l-1-2v-1h1 0v-1h1l1 1h2l2 2c1-1 0-2 1-4 1 0 1 0 2 1v-2h1 1l1-1z" class="AA"></path><path d="M396 463l2 1h-1v2c-1 0-1-1-2-1h0l1-2z" class="T"></path><path d="M390 459c2 1 2 1 3 2 0 1 0 2-1 3h0l-2-5z" class="Y"></path><path d="M396 463l2-4c1 0 1-2 2-2 1-1 2 0 2 0-1 1-2 1-2 2v1l-1 2s-1 1-1 2l-2-1z" class="E"></path><path d="M402 449v1l1 2-1 3 3 6s0 1-1 1v1 1h0l-2 2v-1h0-3 0c-1 0-1 0-2-1h1c0-1 1-2 1-2l1-2v-1c0-1 1-1 2-2v1h1c-1-2-2-4-4-6v-2h1 1l1-1z" class="S"></path><path d="M402 449v1l1 2-1 3-1-2c0-1-1-2-1-3h1l1-1z" class="x"></path><path d="M402 450l1 2-1 3-1-2c1-1 1-2 1-3z" class="U"></path><path d="M402 461v-1c1 0 2 1 2 2v1 1h0l-2 2v-1h0c-1-1 0-1 0-2v-1h0v-1z" class="C"></path><path d="M400 460h1l1 1v1h0v1c0 1-1 1 0 2h-3 0c-1 0-1 0-2-1h1c0-1 1-2 1-2l1-2z" class="H"></path><path d="M402 463l-1-1h0 1v1z" class="D"></path><path d="M399 462v3h0c-1 0-1 0-2-1h1c0-1 1-2 1-2z" class="C"></path><path d="M373 442h1c0 1 1 2 1 3 1 1 2 1 2 1l1 2 1-2c0 1 1 2 1 3h0c1 1 1 2 0 3 1 0 2 0 3 1v1l1 1v1h0c-1 1-1 1-1 2l1 1v1c-1 0-1-1-2 0h0s0 1-1 1c0 2 0 5 1 6l1 9 1 2v3 1l-3 1h-1v-1-1l-1-1c-1-1-1-1-1-2v-1c-1 0-1-1-2-1 1-2 1-4 1-6h-1c0-1 2-11 2-12v-5-2c-1-1-1-1-2-1v1-2c-1-1-1-1-1-2 0-2-1-3-2-4v-1z" class="S"></path><path d="M384 456h0c-1 1-1 1-1 2l1 1v1c-1 0-1-1-2 0h0v-2h0 1v-2h1z" class="c"></path><path d="M378 453c1 1 1 3 2 4v3h-1 0c0-1 0-2-1-2v-5z" class="E"></path><path d="M373 442h1c0 1 1 2 1 3 1 1 2 1 2 1l1 2 1-2c0 1 1 2 1 3h0c1 1 1 2 0 3 1 0 2 0 3 1v1c-1 0-1-1-2-1 0 0-1 0-1-1h-1v2 1c1 1 1 1 1 2-1-1-1-3-2-4v-2c-1-1-1-1-2-1v1-2c-1-1-1-1-1-2 0-2-1-3-2-4v-1z" class="R"></path><path d="M379 446c0 1 1 2 1 3v1l-2-2 1-2z" class="T"></path><path d="M380 481c0-2 1-3 0-5-1-3 1-6 0-9-1-1-1-3-1-5 2 4 2 9 3 14h0 1l1 2v3 1l-3 1h-1v-1-1z" class="s"></path><path d="M383 476l1 2v3 1l-3 1h-1v-1h1v-3l1-1v-2h1z" class="k"></path><path d="M375 429l1 1c0 1 1 2 1 3l1-1 2 2c1 1 2 2 3 4h2 0c0 1 1 2 2 3l1 1c1 1 1 3 2 4s1 2 2 3 2 3 2 4h-2l-1-1h-1v1h0-1v1h0l-1-2-2 1h-1v1h-1v1 1-1l-1-1v-1c-1-1-2-1-3-1 1-1 1-2 0-3h0c0-1-1-2-1-3l-1 2-1-2s-1 0-2-1c0-1-1-2-1-3h-1v1l-1-2h0c1-2 1-3 0-5 1-1 1-3 1-5h0l1 2 1-2v-2z" class="c"></path><path d="M377 435c1 1 1 2 1 3h-1c-1 0-1 0-1-1v-1l1-1z" class="D"></path><path d="M375 429l1 1c0 1 1 2 1 3 1 1 2 3 3 5h-2c0-1 0-2-1-3l-1 1-1-1-1-2 1-2v-2z" class="O"></path><path d="M375 431l2 3v1l-1 1-1-1-1-2 1-2z" class="L"></path><path d="M377 434v1l-1 1-1-1 2-1z" class="H"></path><path d="M373 431h0l1 2 1 2 1 1v1c-1 1 0 3-2 4l-1-1v2 1l-1-2h0c1-2 1-3 0-5 1-1 1-3 1-5z" class="k"></path><path d="M379 446c-1-1-1-3-1-5v-1c1 2 2 3 3 5l2 4 2 3v1 1h-1v1 1-1l-1-1v-1c-1-1-2-1-3-1 1-1 1-2 0-3h0c0-1-1-2-1-3z" class="v"></path><path d="M383 449l2 3v1 1h-1v1 1-1l-1-1v-1-1c-1-1-1-2 0-3z" class="F"></path><path d="M383 452c1 1 1 2 1 3h0l-1-1v-1-1z" class="U"></path><path d="M383 438h2 0c0 1 1 2 2 3l1 1c1 1 1 3 2 4s1 2 2 3 2 3 2 4h-2l-1-1h-1v1h0-1v1h0l-1-2-2 1h-1v-1l-2-3-2-4v-1h2c0-1-1-3-2-4v-2h2z" class="l"></path><path d="M387 449v-2h0l1 2h-1z" class="D"></path><path d="M388 449c1 1 2 1 2 2v1c-2-1-2-2-3-3h1z" class="h"></path><path d="M386 445l1 1h0c-1 1-1 1-1 2h0l-2-3h2z" class="k"></path><path d="M386 449c1 1 2 2 2 3l-2 1h-1v-1l1-3z" class="d"></path><path d="M383 444c0-1-1-3-2-4v-2h2l3 7h-2l-1-1z" class="F"></path><path d="M381 445v-1h2l1 1 2 3v1l-1 3-2-3-2-4z" class="Z"></path><path d="M380 407c1 0 1 0 1 1l-1 2-1 3c1 3 1 5 3 7h0l1 2c-1 1-1 1-1 3l1 2c-1 0-2 1-2 2h-2c0 2 1 3 1 5l-2-2-1 1c0-1-1-2-1-3l-1-1v2l-1 2-1-2h0c0 2 0 4-1 5 1 2 1 3 0 5h0l1 2c1 1 2 2 2 4 0 1 0 1 1 2v2-1c1 0 1 0 2 1v2 5c0 1-2 11-2 12-2 4-4 9-7 13v-1-1c1-1 1-1 1-3h0l-1-1v-4l1-1h0l1-2 1-7h-1c-2 0-4 0-5-1-1 0-1-1-1-1h-1v-1c0-1 0-1 1-2v-2h-2v-2-2l2-12 3-9c0-3 1-5 2-8 3-6 6-11 10-16z" class="u"></path><path d="M363 454l2 2h-2v-2z" class="m"></path><path d="M369 449v1l1 3c-1 0-1 0-2 1v-1c0-1 0-3 1-4z" class="s"></path><path d="M365 458l1-1h0v2 1h-2 0c0-1 0-1 1-2z" class="W"></path><defs><linearGradient id="q" x1="366.642" y1="443.741" x2="371.293" y2="445.12" xlink:href="#B"><stop offset="0" stop-color="#252828"></stop><stop offset="1" stop-color="#423f3e"></stop></linearGradient></defs><path fill="url(#q)" d="M369 449v-1c-1-3-1-7-1-10 0-1 0-1 1-2v1c0 2 1 4 1 6v5l1 1-1 2-1-1v-1z"></path><path d="M369 437l1-1c0 2 1 4 2 5l1 2c1 1 2 2 2 4 0 1 0 1 1 2v2l-1 1c-1-3-2-6-4-9-1 1-1 4-1 5v-5c0-2-1-4-1-6z" class="C"></path><path d="M367 459v-9h1v3 1c1-1 1-1 2-1v1l1 3c0 1-1 2-1 3l1 3c-2 0-4 0-5-1-1 0-1-1-1-1h-1v-1h0 2v-1h1z" class="F"></path><path d="M366 459h1c0 1 1 1 1 2l-1 1c-1-1-1-1-2-1l-1-1h2v-1z" class="E"></path><path d="M368 454c1-1 1-1 2-1v1l1 3c0 1-1 2-1 3l-2-6z" class="H"></path><path d="M373 431c0-1-1-1-1-2 0-4 1-5 3-9 0-1 0-2 1-3 0-1 1-1 1-1 1-2 1-2 2-3 1 3 1 5 3 7h0l1 2c-1 1-1 1-1 3l1 2c-1 0-2 1-2 2h-2c0 2 1 3 1 5l-2-2-1 1c0-1-1-2-1-3l-1-1v2l-1 2-1-2h0 0z" class="W"></path><path d="M373 431v-5-1l1 1v1h0c0 2-1 3-1 4h0 0z" class="l"></path><path d="M374 426l2 2h-1 0-1l1 1v2l-1 2-1-2c0-1 1-2 1-4h0v-1z" class="h"></path><path d="M377 423h0l1-1c-1-1-1-2 0-4h0c-1-1-1-1-1-2h1c1 0 1 1 1 1 0 2 1 3 1 5l-1 1v2h0c-1 0-1-1-2-2h0z" class="c"></path><path d="M379 417c0 2 1 3 1 5l-1 1c-1 0-1-1-1-2s0-2 1-4z" class="s"></path><path d="M380 422c0 1 0 2 2 3h0l1 2c-1 0-2 1-2 2h-2c0 2 1 3 1 5l-2-2-1 1c0-1-1-2-1-3l-1-1-1-1h1 0 1v-2c1-1 1-2 1-3h0c1 1 1 2 2 2h0v-2l1-1z" class="H"></path><path d="M370 448c0-1 0-4 1-5 2 3 3 6 4 9l1-1v-1c1 0 1 0 2 1v2 5c0 1-2 11-2 12-2 4-4 9-7 13v-1-1c1-1 1-1 1-3h0l-1-1v-4l1-1h0l1-2 1-7h-1l-1-3c0-1 1-2 1-3l-1-3v-1l-1-3 1 1 1-2-1-1z" class="X"></path><path d="M371 453l1 4h-1l-1-3 1-1z" class="S"></path><path d="M371 449v4l-1 1v-1l-1-3 1 1 1-2z" class="j"></path><path d="M371 457h1c0 2 1 4 0 6h-1l-1-3c0-1 1-2 1-3z" class="C"></path><defs><linearGradient id="r" x1="378.017" y1="465.547" x2="368.631" y2="465.977" xlink:href="#B"><stop offset="0" stop-color="#242525"></stop><stop offset="1" stop-color="#524f4f"></stop></linearGradient></defs><path fill="url(#r)" d="M376 451v-1c1 0 1 0 2 1v2 5c0 1-2 11-2 12-2 4-4 9-7 13v-1-1c1-1 1-1 1-3h0l-1-1v-4l1-1h0v1h1l1 1c2-7 5-15 3-22l1-1z"></path><path d="M370 472v1h1l1 1c-1 1-1 3-2 4l-1-1v-4l1-1h0z" class="Q"></path><defs><linearGradient id="s" x1="419.431" y1="497.77" x2="410.456" y2="497.232" xlink:href="#B"><stop offset="0" stop-color="#272627"></stop><stop offset="1" stop-color="#474849"></stop></linearGradient></defs><path fill="url(#s)" d="M412 474v1-2h1s0-1 1-1c0 1 1 2 1 3s1 2 2 2c2 14 2 27 2 40 0 2-1 5-1 7v3c-1 8-2 18-5 26l-1 2c0 2-1 5-2 7 0-1 1-1 1-2 2-2 4-4 5-6l1 1-2 2-6 7c-2 2-3 4-5 5l1 2c-2 1-2 1-4 1l-2 1h-1v-2c-1 0-1 0-1-1h-1v-1-1l-1 1s-1 0-1-1v-8h-1c0-1 0-2 1-3v-1c-1 1-2 2-3 2 0-2-2-4-2-6l-1-3-2-7v-2c1-1 1-1 2-1v-1-2-1-10-1c-1-1-1-4-1-5h1v1c1 0 1 0 2-1l-1-1s-1 0-1-1c-2-1-4-4-4-6 0-1-1-1-2-2h3l-3-3-1-1v-1c-1 0-1-1-2-1 0-1-1-3-1-4 0-2 0-3-1-5l2-3c1-1 1-1 2-1h-2v-1h-1l-1-1h0l-1-1h-3l1-1c0-1 0-1 1-1h2l3-2h1l3-1c3 0 6-1 8 0l1 1c1 1 1 2 2 2h1v1l1-1 1-1 1 1v-3c-1 0-1 0-1 1h-1c-1-1-1-1-1-2h0v-3l1 1v-1c1 0 3 0 4-1h-2v-1h2s1 0 1-1h2 1 0 1v1 1h1 1l-1 1h0c1 1 1 1 2 1l1-1h2v-1-1c-1-1-1-1-1-2h1z"></path><defs><linearGradient id="t" x1="409.509" y1="501.943" x2="421.329" y2="514.466" xlink:href="#B"><stop offset="0" stop-color="#5a5a5a"></stop><stop offset="1" stop-color="#717071"></stop></linearGradient></defs><path fill="url(#t)" d="M413 499l4-2c0 5 1 12 0 18 0 0 0 1-1 1 0 0 1 3 0 3 0 1 0 1-1 1h0 0 0l-1-2v1l-1-1c-1-1-1-2-1-4l-1-1c-1-1-1-2 0-3v-1c0-1 0-2-1-3 0-2 1-5 2-6l1-1z"></path><path d="M412 509v-3c1 1 2 2 2 3v7 2 1l-1-1-1-9z" class="Z"></path><path d="M412 500l1-1c0 1-1 1-1 2 1 1 0 3 0 4v4l1 9c-1-1-1-2-1-4l-1-1c-1-1-1-2 0-3v-1c0-1 0-2-1-3 0-2 1-5 2-6z" class="S"></path><path d="M406 497l1 4 2 6v1l1-2c1 1 1 2 1 3v1c-1 1-1 2 0 3l1 1c0 2 0 3 1 4l1 1v-1l1 2c-1 2-1 3-1 4v4c-1-1 0-2-1-3l-1 1c-1 0-2 0-2-1h-1v-5h0-2v-2l-1-3h0c0-1 0-1-1-2 0-3-1-5-1-8l1-1h0c0-1 0-1 1-2h0c0-2-1-4 0-5z" class="C"></path><path d="M414 518l1 2c-1 2-1 3-1 4 0-1-1-3 0-5v-1z" class="D"></path><path d="M410 520l1 4-1 1h-1v-5 2h1v-2z" class="d"></path><path d="M409 520c1-3-1-7 0-10l1 2v8 2h-1v-2h0z" class="b"></path><path d="M409 507v1c0 1 0 1 1 2v2l-1-2c-1 3 1 7 0 10h-2v-2-5c2-1 1-4 2-6z" class="L"></path><defs><linearGradient id="u" x1="407.287" y1="510.252" x2="403.344" y2="508.952" xlink:href="#B"><stop offset="0" stop-color="#5f615d"></stop><stop offset="1" stop-color="#737074"></stop></linearGradient></defs><path fill="url(#u)" d="M406 497l1 4 2 6c-1 2 0 5-2 6v5l-1-3h0c0-1 0-1-1-2 0-3-1-5-1-8l1-1h0c0-1 0-1 1-2h0c0-2-1-4 0-5z"></path><path d="M406 497l1 4 2 6c-1 2 0 5-2 6l-1-11c0-2-1-4 0-5z" class="O"></path><path d="M415 520h0 0c1 0 1 0 1-1 1 0 0-3 0-3 1 0 1-1 1-1v3l1 8v1c-1 8-2 18-5 26-1-1-1-2-1-3 0-2 0-5-1-6v-5h1l-1-1h-1l-1-1c-1-1-1-5-1-7v-5h1 1c0 1 1 1 2 1l1-1c1 1 0 2 1 3v-4c0-1 0-2 1-4h0z" class="B"></path><defs><linearGradient id="v" x1="417.29" y1="526.279" x2="414.157" y2="520.92" xlink:href="#B"><stop offset="0" stop-color="#474749"></stop><stop offset="1" stop-color="#5f615d"></stop></linearGradient></defs><path fill="url(#v)" d="M415 520h0 0c1 0 1 0 1-1 1 0 0-3 0-3 1 0 1-1 1-1v3h0c0 3 1 9-1 10-1 0-1 1-2 1v-1h0v-4c0-1 0-2 1-4h0z"></path><path d="M415 520h0v8h-1 0v-4c0-1 0-2 1-4z" class="H"></path><path d="M408 525h1 1c0 1 1 1 2 1l1-1c1 1 0 2 1 3h0v1c1 4 0 8-1 12h-1v-2l-1-1h-1l-1-1c-1-1-1-5-1-7v-5z"></path><path d="M404 475h1 0 1v1 1h1 1l-1 1h0c1 1 1 1 2 1l1-1 3 9c1 2 1 4 2 6-2 2-3 4-3 7-1 1-2 4-2 6l-1 2v-1l-2-6-1-4c-1 1 0 3 0 5h0c-1 1-1 1-1 2h0l-1 1v-2-1h-1v-1l-1 1h-1-1c0-1-1-2-1-3v-1c0-1-1-2-2-3 0 1-1 2-1 3-1 0-2 1-2 1h-2c-1-1-1-1-1-2h1c2-1 3-3 3-6v-6h1v1l1-1 1-1 1 1v-3c-1 0-1 0-1 1h-1c-1-1-1-1-1-2h0v-3l1 1v-1c1 0 3 0 4-1h-2v-1h2s1 0 1-1h2z" class="H"></path><path d="M397 479h3 2c1 0 2-1 3-1v1c1 1 2 0 3 1h-1 0c0 1 0 1-1 1h0v-1h-1v2 1l-1-1v-2c-1 0-3 1-4 0h0c-1 1-3 1-4 1h0v-3l1 1z" class="W"></path><path d="M404 475h1 0 1v1 1l-1 1c-1 0-2 1-3 1h-2-3v-1c1 0 3 0 4-1h-2v-1h2s1 0 1-1h2z" class="I"></path><path d="M404 475h1 0 1v1l-5 1h-2v-1h2s1 0 1-1h2z" class="T"></path><path d="M407 477h1l-1 1h0c1 1 1 1 2 1l1-1 3 9-3-2h0c0 1-1 3 0 4h0c-1 1-1 1-1 2s1 2 0 2l-1-1v-2l-1-1v-1c-1-2-1-4-2-6v-2h1v1h0c1 0 1 0 1-1h0 1c-1-1-2 0-3-1v-1l1-1h1z" class="H"></path><path d="M405 482v-2h1v1c0 3 2 5 2 8v1l-1-1v-1c-1-2-1-4-2-6z" class="d"></path><path d="M407 477h1l-1 1h0c1 1 1 1 2 1l1-1 3 9-3-2h0c0 1-1 3 0 4h0c-1 1-1 1-1 2v-6c0-1-1-3-1-5-1-1-2 0-3-1v-1l1-1h1z" class="B"></path><path d="M409 491c0-1 0-1 1-2h0c-1-1 0-3 0-4h0l3 2c1 2 1 4 2 6-2 2-3 4-3 7-1 1-2 4-2 6l-1 2v-1l-2-6-1-4-1-2c0-3-2-5-1-8 0 1 1 3 2 4h0c1-1 0-1 1-2l1 1v2l1 1c1 0 0-1 0-2z" class="AA"></path><path d="M406 497l-1-2c0-3-2-5-1-8 0 1 1 3 2 4h0c1-1 0-1 1-2l1 1v2h-1c-1 1-1 2-1 3h1c0 1 1 2 0 4h0v2l-1-4z" class="U"></path><path d="M399 485c1 1 1 2 1 3h1v-2h1l1 1h0c0-2-1-3-2-4h0l-1-1h1s1 0 2-1c0 2 1 4 1 6-1 3 1 5 1 8l1 2c-1 1 0 3 0 5h0c-1 1-1 1-1 2h0l-1 1v-2-1h-1v-1l-1 1h-1-1c0-1-1-2-1-3v-1c0-1-1-2-2-3 0 1-1 2-1 3-1 0-2 1-2 1h-2c-1-1-1-1-1-2h1c2-1 3-3 3-6v-6h1v1l1-1 1-1 1 1z" class="l"></path><path d="M402 495l1-1h0c0 1 0 2 1 3l-1 1c-1-1-1-2-1-3z" class="s"></path><path d="M403 501c-1-2-1-4-3-5h1l1-1c0 1 0 2 1 3l1 4h-1v-1z" class="S"></path><path d="M403 498l1-1c0 1 1 4 2 5-1 1-1 1-1 2h0l-1 1v-2-1l-1-4z" class="H"></path><path d="M398 493c1 1 1 2 2 3l1 6h-1c0-1-1-2-1-3v-1c0-1-1-2-2-3l1-2z" class="F"></path><path d="M395 491c1-1 1-1 1-2 1 0 1 0 1 1 1 1 0 2 1 3l-1 2c0 1-1 2-1 3-1 0-2 1-2 1h-2c-1-1-1-1-1-2h1c2-1 3-3 3-6z" class="B"></path><path d="M384 482c3 0 6-1 8 0l1 1c1 1 1 2 2 2v6c0 3-1 5-3 6h-1c0 1 0 1 1 2h2s1-1 2-1c0-1 1-2 1-3 1 1 2 2 2 3v1c0 1 1 2 1 3h1 1l1-1v1h1v1 2c0 3 1 5 1 8v1h0l-1 1v-3h-1v2h-1c0 1-1 3-1 4l-1 1h0c1 1 1 1 1 2h-1l1 2v3h-1c-1 0-2 0-4-1h0c0-1-1-2-2-2v-1c-1 0-1 0-2-1-1 0-1-1-2-2l-1-1s-1 0-1-1c-2-1-4-4-4-6 0-1-1-1-2-2h3l-3-3-1-1v-1c-1 0-1-1-2-1 0-1-1-3-1-4 0-2 0-3-1-5l2-3c1-1 1-1 2-1h-2v-1h-1l-1-1h0l-1-1h-3l1-1c0-1 0-1 1-1h2l3-2h1l3-1z" class="h"></path><path d="M388 513h1l2 2v1l1 1c-2 0-3-2-4-3v-1z" class="D"></path><path d="M385 509l3 4v1c-2 0-3-2-4-3 0-1-1-1-2-2h3z" class="F"></path><path d="M395 517c-2 0-3-1-4-3-1-1-2-2-3-4-1-1-1-1-1-2h0c1 1 2 2 4 3h0c1 2 2 4 3 5h0l1 1z" class="L"></path><path d="M389 504l1 1h0l2 2h0 0l2 3h1c1 1 1 2 1 3h-1v1l-1 2h0c-1-1-2-3-3-5v-1h0c-1-1-2-3-2-4v-2z" class="E"></path><path d="M389 504l1 1h0l2 2h0c0 2 2 4 2 6h-1l-2-3h0c-1-1-2-3-2-4v-2z" class="T"></path><path d="M384 511c1 1 2 3 4 3 1 1 2 3 4 3v1h1 2v-1l-1-1 1-2 3 2 2 4v1l1 2v3h-1c-1 0-2 0-4-1h0c0-1-1-2-2-2v-1c-1 0-1 0-2-1-1 0-1-1-2-2l-1-1s-1 0-1-1c-2-1-4-4-4-6z" class="g"></path><path d="M384 511c1 1 2 3 4 3 1 1 2 3 4 3v1c-2 0-2-1-3-1 0-1 0-1-1-2h0-1-1l3 3s-1 0-1-1c-2-1-4-4-4-6z" class="w"></path><path d="M381 490h1v3 2c1 1 0 2 1 3h1l1-1s2 1 2 2h0c1 0 1 1 2 1v2 1h0c-1-1-1-1-2-1h0v-1l-1 1 1 1v2 2c-1 0-2-1-2-1 0-1-1-1-1-1 0-2-1-3-1-4v-1-1h-1s-1 1-1 2c1 2 2 3 1 5l-1-1v-1c-1 0-1-1-2-1 0-1-1-3-1-4 0-2 0-3-1-5l2-3c1-1 1-1 2-1z" class="T"></path><path d="M381 490h1v3 2c-1-1-1-2-1-3l-1 3v-1-3h-1c1-1 1-1 2-1z" class="k"></path><path d="M379 491h1v3 1 4c0 2 1 4 1 6v-1c-1 0-1-1-2-1 0-1-1-3-1-4 0-2 0-3-1-5l2-3z" class="R"></path><path d="M379 491h1v3c-1 0-1 0-2 2v3c0-2 0-3-1-5l2-3z" class="E"></path><path d="M397 495c1 1 2 2 2 3v1c0 1 1 2 1 3h1 1l1-1v1h1v1 2c0 3 1 5 1 8v1h0l-1 1v-3h-1v2h-1c0 1-1 3-1 4l-1 1h0c1 1 1 1 1 2h-1v-1l-2-4-3-2v-1h1c0-1 0-2-1-3h-1l-2-3h0 0l-2-2h0l-1-1-2-2h0c1 0 1 0 2 1h0v-1-3h0 3 2s1-1 2-1c0-1 1-2 1-3z" class="h"></path><path d="M400 510h1v2h-1v-2z" class="C"></path><path d="M392 502c2 2 3 5 4 8 1 2 1 4 2 6l-3-2v-1h1c0-1 0-2-1-3 0-2-1-4-2-6-1 0-1-1-1-2z" class="m"></path><path d="M396 498v3l2 8c0 1 1 4 1 5 0-1 0 0-1-1v-1c-1-3-3-6-3-9v-4h-1s1-1 2-1z" class="E"></path><path d="M389 502v-3h0 3 2 1v4l-3-3h0v2c0 1 0 2 1 2 1 2 2 4 2 6h-1l-2-3h0 0l-2-2h0l-1-1-2-2h0c1 0 1 0 2 1h0v-1z" class="S"></path><path d="M397 495c1 1 2 2 2 3v1c0 1 1 2 1 3s1 4 1 5h-1c0 1 0 1-1 2v-1l-1 1-2-8v-3c0-1 1-2 1-3z" class="C"></path><path d="M398 499c1 1 1 2 1 3-1 0-1-1-2-1 0-1 0-1 1-2z" class="E"></path><path d="M397 495c1 1 2 2 2 3v1h-1c-1 1-1 1-1 2h-1v-3c0-1 1-2 1-3z" class="j"></path><path d="M402 502l1-1v1h1v1 2c0 3 1 5 1 8v1h0l-1 1v-3h-1v2h-1c0-3 0-5-1-7 0-1-1-4-1-5h1 1z" class="S"></path><path d="M402 502l1-1v1h1v1l-1 1-1-2z" class="s"></path><path d="M384 482c3 0 6-1 8 0l1 1c1 1 1 2 2 2v6c0 3-1 5-3 6h-1c0 1 0 1 1 2h-3 0v3-2c-1 0-1-1-2-1h0c0-1-2-2-2-2l-1 1h-1c-1-1 0-2-1-3v-2-3h-1-2v-1h-1l-1-1h0l-1-1h-3l1-1c0-1 0-1 1-1h2l3-2h1l3-1z" class="X"></path><path d="M377 488h0c1-2 1-3 3-4 0 2 1 2 1 3l-2 1v1h-1l-1-1z" class="e"></path><path d="M380 483h1l-1 1c-2 1-2 2-3 4h0 0l-1-1h-3l1-1c0-1 0-1 1-1h2l3-2z" class="I"></path><path d="M379 490c1-1 1-1 2-1 0-1 0-1 1-1s2 0 3-1l1-1h-1s-1 0-2-1h0c1-1 1-1 2-1l1 1 1 1h0v1c1 0 1 0 0 1h0-1c0 1-1 1-2 1l-2 1h-1-2z" class="M"></path><path d="M385 484c2 0 3 0 5 1 1 1 1 3 1 4 0 2 0 3-1 4h0c-1 0-1 0-2-1h0s0-1-1-1c0-1-1-2-3-2 1 0 2 0 2-1h1 0c1-1 1-1 0-1v-1h0l-1-1-1-1z" class="K"></path><path d="M384 489c2 0 3 1 3 2 1 0 1 1 1 1-1 0-2-1-3-1v3c1 2 2 3 3 3h3c0 1 0 1 1 2h-3 0v3-2c-1 0-1-1-2-1h0c0-1-2-2-2-2l-1 1h-1c-1-1 0-2-1-3v-2-3l2-1z" class="E"></path><path d="M402 514h1v-2h1v3l1-1h0v-1c1 1 1 1 1 2h0l1 3v2h2 0v5h-1v5c0 2 0 6 1 7l1 1h1l1 1h-1v5c1 1 1 4 1 6 0 1 0 2 1 3l-1 2c0 2-1 5-2 7 0-1 1-1 1-2 2-2 4-4 5-6l1 1-2 2-6 7c-2 2-3 4-5 5l1 2c-2 1-2 1-4 1l-2 1h-1v-2c-1 0-1 0-1-1h-1v-1-1l-1 1s-1 0-1-1v-8h-1c0-1 0-2 1-3v-1c-1 1-2 2-3 2 0-2-2-4-2-6l-1-3-2-7v-2c1-1 1-1 2-1v-1-2-1-10-1c-1-1-1-4-1-5h1v1c1 0 1 0 2-1 1 1 1 2 2 2 1 1 1 1 2 1v1c1 0 2 1 2 2h0c2 1 3 1 4 1h1v-3l-1-2h1c0-1 0-1-1-2h0l1-1c0-1 1-3 1-4z" class="AA"></path><path d="M394 543c0-2 1-3 1-5h1v2c1-1 1-1 1-2h1l1 1c-1 2-1 4 0 6v3-1c-1-1-1-3-2-4l-1-2h0l-2 2z" class="i"></path><path d="M399 539c0 2 0 3 1 4h1c0 2-1 3 0 4 0 1-1 2-1 3l-1 1v-2-1-3c-1-2-1-4 0-6z" class="h"></path><path d="M399 535h1l2 2v-2h1v4c-1 3-1 6-1 9v-4l-1-1v4h0c-1-1 0-2 0-4h-1c-1-1-1-2-1-4l-1-1c0-1 0-1 1-2v-1z" class="D"></path><path d="M399 535h1l2 2c-1 0-1 0-2 1 0-1-1-1-1-2v-1zm2 12h0v-4l1 1v4 2c1 2 1 4 1 7v3 1h-1 0c-1-1-2-1-2-3v-2c-1-2-1-3-1-5l1-1c0-1 1-2 1-3z" class="J"></path><path d="M402 550c1 2 1 4 1 7 0-1 0-2-1-3v1l-1-1c0-1 0-1 1-2v-2z" class="L"></path><path d="M399 549v2c0 2 0 3 1 5v2c0 1 0 2-1 3l-1 5c0 2 0 3-1 4h-1v-1-1l-1 1s-1 0-1-1v-8l1-1v-5-1c1-1 2-1 4-2v-2z" class="AC"></path><path d="M396 553h1c1 3 0 6 0 9 0 1 0 1-1 2-1-2 0-8 0-11z" class="C"></path><path d="M399 549v2c0 2 0 3 1 5v2c0 1 0 2-1 3-1 0-1 1-1 2-1 0-1-1-1-1 0-3 1-6 0-9h-1l-1 1v-1c1-1 2-1 4-2v-2z" class="j"></path><path d="M400 558c0 2 1 2 2 3h0 1v-1c1 1 1 4 2 4v-1h1 1v1h1 1c-2 2-3 4-5 5l1 2c-2 1-2 1-4 1l-2 1h-1v-2c-1 0-1 0-1-1 1-1 1-2 1-4l1-5c1-1 1-2 1-3z" class="U"></path><path d="M401 572l3-3 1 2c-2 1-2 1-4 1z" class="M"></path><path d="M400 558c0 2 1 2 2 3h0 1c0 2 0 5-2 7h-1v-1c-1 1-1 3-2 4-1 0-1 0-1-1 1-1 1-2 1-4l1-5c1-1 1-2 1-3z" class="L"></path><path d="M388 539h1l1 1h1c0 1 1 1 2 1v2l1 1v3l1 3v3 1 5l-1 1h-1c0-1 0-2 1-3v-1c-1 1-2 2-3 2 0-2-2-4-2-6l-1-3-2-7v-2c1-1 1-1 2-1z" class="AB"></path><path d="M392 547h2l1 3h-1l-1 1h0v-2c0-1 0-1-1-2z" class="v"></path><path d="M393 551l1-1h1v3 1 5l-1 1h-1c0-1 0-2 1-3v-1l-1-5z" class="W"></path><path d="M389 539l1 1h1c0 1 1 1 2 1v2l1 1v3h-2l-3-8z" class="F"></path><path d="M391 540c0 1 1 1 2 1v2c-1-1-2-1-2-3z" class="E"></path><path d="M388 525v-1c-1-1-1-4-1-5h1v1c1 0 1 0 2-1 1 1 1 2 2 2 1 1 1 1 2 1v1c1 0 2 1 2 2h0 0c1 2 2 2 3 3h0-1v1l1 2v2 1 1 1c-1 1-1 1-1 2h-1c0 1 0 1-1 2v-2h-1c0 2-1 3-1 5v1l-1-1v-2c-1 0-2 0-2-1h-1l-1-1h-1v-1-2-1-10z" class="s"></path><path d="M393 538c0-1 0-1 1-1v1 1c-1 0-1-1-1-1z" class="k"></path><path d="M393 536h0l1 1c-1 0-1 0-1 1h-1c0-1-1-1-1-2h2z" class="l"></path><path d="M392 533v1l-1-1 1-1v-2c0-1 0-2 1-2v3l-1 2z" class="T"></path><path d="M393 531h1c0 1 0 2 1 2 0 1 0 2-1 3h-1 0c0-1 0-2-1-3l1-2z" class="H"></path><path d="M398 528l-2 2h0c-1-2-1-3-1-4l1-1c1 2 2 2 3 3h0-1z" class="D"></path><path d="M398 529l1 2v2 1 1 1c-1 1-1 1-1 2h-1c0 1 0 1-1 2v-2c1-1 1-2 1-3v-2c0-2 0-3 1-4z" class="H"></path><path d="M398 533c0-1 0-2 1-2v2h-1z" class="C"></path><path d="M398 533h1v1 1 1c-1 1-1 1-1 2h-1v-2c1-1 1-1 1-3z" class="c"></path><path d="M388 525v-1c-1-1-1-4-1-5h1v1c1 0 1 0 2-1 1 1 1 2 2 2 1 1 1 1 2 1v1c1 0 2 1 2 2h0c-1-1-2-1-2-2-1 1-1 1-1 2h-1c0-1 0-2-1-2h-1v1 4c1 3 0 6 1 8h0c0 1 1 1 1 2s0 2 1 3c-1 0-2 0-2-1h-1l-1-1h-1v-1-2-1-10z" class="z"></path><path d="M388 525h1c0 2 1 8 0 10l-1 1v-1-10z" class="l"></path><path d="M402 514h1v-2h1v3l1-1h0v-1c1 1 1 1 1 2h0l1 3v2h2 0v5h-1v5c0 2 0 6 1 7l1 1h1l1 1h-1v5c1 1 1 4 1 6 0 1 0 2 1 3l-1 2c0 2-1 5-2 7 0-1 1-1 1-2 2-2 4-4 5-6l1 1-2 2-6 7h-1-1v-1h-1-1v1c-1 0-1-3-2-4v-3c0-3 0-5-1-7v-2c0-3 0-6 1-9v-4h-1v2l-2-2h-1v-1-1-2l-1-2v-1h1 0c-1-1-2-1-3-3h0c2 1 3 1 4 1h1v-3l-1-2h1c0-1 0-1-1-2h0l1-1c0-1 1-3 1-4z" class="D"></path><path d="M406 526c1 2 2 2 2 4s0 6 1 7v3c-1 1-1 2-1 3-1-3 0-6-1-8l-1-2v-4-3z" class="E"></path><path d="M403 539h1c0 1 0 2 1 4 0 1 0 6-1 7-1 3 0 9 0 12l1 1h0v1c-1 0-1-3-2-4v-3c0-3 0-5-1-7v-2c0-3 0-6 1-9z" class="S"></path><path d="M403 542l1 1v6c-1-2-1-5-1-7z" class="D"></path><path d="M409 540v-3l1 1h1l1 1h-1v5c1 1 1 4 1 6 0 1 0 2 1 3l-1 2c0 2-1 5-2 7 0-1 1-1 1-2 2-2 4-4 5-6l1 1-2 2-6 7h-1-1v-1h-1-1 0v-12c2 2 1 8 1 11h1c1-2 0-11-1-14 0-2 0-4 1-5v-1 3h2l-1-2c0-1 0-2 1-3z" class="u"></path><path d="M407 545h2v11l-1 1h0v-7c-1-2-1-4-1-5z" class="d"></path><path d="M408 543c0-1 0-2 1-3 0 2 1 4 1 6v5c1 1 0 1 1 1 0-1 0-1 1-2 0 1 0 2 1 3l-1 2-1-1c-1 0-1 2-2 3v-1-11l-1-2z" class="U"></path><path d="M409 540v-3l1 1h1l1 1h-1v5c1 1 1 4 1 6-1 1-1 1-1 2-1 0 0 0-1-1v-5c0-2-1-4-1-6z" class="H"></path><path d="M410 538h1l1 1h-1v5c1 1 1 4 1 6-1 1-1 1-1 2 0-5-1-9-1-14z" class="g"></path><path d="M402 514h1v-2h1v3l1-1h0v-1c1 1 1 1 1 2h0l1 3v2h2 0v5h-1v5c0-2-1-2-2-4v3 4c0 1 0 1-1 1-1 2-1 3-1 5h-1v-4h-1v2l-2-2h-1v-1-1-2l-1-2v-1h1 0c-1-1-2-1-3-3h0c2 1 3 1 4 1h1v-3l-1-2h1c0-1 0-1-1-2h0l1-1c0-1 1-3 1-4z" class="h"></path><path d="M404 526c1 1 2 2 2 3h-1c-1 0-1 0-1-1v-2z" class="D"></path><path d="M405 529h1 0v4c0 1 0 1-1 1v-5z" class="J"></path><path d="M399 528l1 1c1 1 0 2 0 3v1c0 1-1 1-1 1v-1-2l-1-2v-1h1z" class="Z"></path><path d="M401 523c0 1 0 2 1 2v2c0 1 1 1 1 1v4h-1v-2l-2-1-1-1h0c-1-1-2-1-3-3h0c2 1 3 1 4 1h1v-3z" class="k"></path><path d="M402 514h1v-2h1v3 5h-1v6 2s-1 0-1-1v-2c-1 0-1-1-1-2l-1-2h1c0-1 0-1-1-2h0l1-1c0-1 1-3 1-4z" class="c"></path><path d="M401 521c0-1 0-1-1-2h0l1-1c1 1 1 3 1 4h-1v-1z" class="D"></path><path d="M405 513c1 1 1 1 1 2h0l1 3v2h2 0v5h-1v5c0-2-1-2-2-4v3h0c0-1-1-2-2-3h0v-6-5l1-1h0v-1z" class="T"></path><path d="M406 515l1 3v2h2 0v5h-1v5c0-2-1-2-2-4v-11z" class="B"></path><path d="M407 520h2 0v5h-1-1v-5z" class="m"></path><path d="M349 98c15 0 32 0 47 3 6 1 12 2 18 5 15 5 29 13 41 24 11 9 19 21 26 34 7 15 10 34 11 51 0 29-6 60-26 82l-2 2c-7 8-17 11-27 15-7 2-14 5-22 6-22 5-44 4-66 4V98z" class="q"></path></svg>