<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="46 36 544 636"><!--oldViewBox="0 0 624 752"--><style>.B{fill:#686868}.C{fill:#717071}.D{fill:#5a595a}.E{fill:#828182}.F{fill:#878687}.G{fill:#636263}.H{fill:#7d7c7e}.I{fill:#8d8c8e}.J{fill:#7b7b7a}.K{fill:#535353}.L{fill:#434344}.M{fill:#1e1f1f}.N{fill:#5e5d5e}.O{fill:#4a4b4b}.P{fill:#1d1e1e}.Q{fill:#757475}.R{fill:#6d6d6d}.S{fill:#323233}.T{fill:#a1a1a1}.U{fill:#9f9fa0}.V{fill:#969597}.W{fill:#a9a8aa}.X{fill:#2c2d2e}.Y{fill:#191819}.Z{fill:#4e4d4e}.a{fill:#c3c3c3}.b{fill:#3c3c3e}.c{fill:#383839}.d{fill:#141415}.e{fill:#dcdadc}.f{fill:#0a0a0a}.g{fill:#999998}.h{fill:#f1f1f2}.i{fill:#dadadb}.j{fill:#dededd}.k{fill:#232223}.l{fill:#292829}.m{fill:#c7c6c5}.n{fill:#fff}</style><path d="M78 401l1-3c0 1 1 2 1 2v1h-2z" class="U"></path><path d="M53 600c2 0 3 1 4 2 0 1 0 1-1 2l-3-4z" class="e"></path><path d="M436 638c1 0 2 0 2 1-1 0-2 1-4 1l-1-1 3-1z" class="M"></path><path d="M88 529c0 1 0 2 1 4l-2 8v-3-9l1 1v-1z" class="i"></path><path d="M40 585c1 1 2 1 2 2v-1c2 2 2 5 4 7l1 1c-3-1-6-6-7-9z" class="h"></path><path d="M204 122v-2l6 6-1 2-5-5v-1z" class="c"></path><path d="M123 659c0 1-1 2-2 2 0-1 1-2 1-3 0-2 1-6 2-8 1 1 1 1 1 3-1 2-2 4-2 6z" class="h"></path><path d="M124 645l1-1h0v3l2 2c-1 2-1 3-2 5 0 2-1 4-2 5 0-2 1-4 2-6 0-2 0-2-1-3 0-1 0-3 1-4l-1-1z" class="H"></path><path d="M201 116c1 1 2 3 3 4v2h-1-1 0c-1-1-1-1-1-2h-1c-1-1-1-2-1-3l2-1z" class="L"></path><path d="M130 653c1 5 3 7 6 10v1c-3-2-6-5-8-9 1 0 1-1 2-2z" class="E"></path><path d="M77 470h0c1-1 1-3 2-4v1l-1 10v2h0c-1 0-1-1 0-1 0-1 0-2-1-3 1-1 1-3 1-4v1l-1 1h0c0 1 0 2-1 3h0c-1-2 0-5 1-6z" class="h"></path><path d="M452 431h1l1-3v13l-1-2h0v10c-1-2 0-4 0-6l-1-12z" class="P"></path><path d="M73 416c1-5 3-11 5-15h2l-1 4-3 7v-1h0c-1 1-1 3-3 5z" class="a"></path><path d="M46 597c2 2 3 3 4 5 2 1 4 2 6 4h-1l-1-1h0v2l-3-2c0-1 0-1-1-1l-1-1h0-1c-1 0-2-1-2-2-1-1-1-2-2-2l1-1 3 3c1 1 2 2 3 2h0c-1-1-2-1-3-2v-1c-1-1-2-1-2-3z" class="h"></path><path d="M369 501v2c0 1-3 3-3 4v1c1-1 3-2 4-3v-1h2c-2 2-4 4-7 6-1 0-2 1-4 1h-1-1l2-1c3-3 6-5 8-9z" class="a"></path><path d="M123 330c-1 0-1 2-1 2 0 1-2 1-1 2h0c-5 2-11 4-16 4 2-1 5-2 8-3s6-3 10-5z" class="L"></path><path d="M80 650l-8-3h3 1l-3-1c-1 0-1-1-2-1v-1c1 0 2 1 2 1 1 0 2 0 3 1l3 1h-1l2 1c2 0 4 1 7 1h0c-1 1-3 1-4 1h-1-2z" class="a"></path><path d="M365 426c-1-5-2-9-2-13 2 5 3 11 4 17 0 3 1 6 0 8 0 2 0 3-1 5 0 0 0 1-1 2v-1c2-6 1-12 0-18z" class="b"></path><path d="M231 659v1h3s1 0 2-1h1 1 3c1-1 3-1 4-2h2 1l1-1v1c-1 0-1 1-2 1l-3 1h-1-2 0l-1 1h-2-3v1h-3-1c-2 1-5 1-7 2l-1-1 8-3z" class="i"></path><path d="M128 325c-1 1-2 1-3 2v-1c-4 1-8 2-12 2h0c2 0 3-1 4-1 7-1 11-5 16-9v2l-3 3v1h0c-1 1-1 1-2 1z" class="c"></path><path d="M480 610c0 1 0 1-1 2h0l-1 1h0s0 1-1 1c0 0 1 0 1 1-3 3-8 6-11 8v-1l-1 1h0l1-2h0v-1c2-1 3-3 4-5 4 0 6-2 9-5z" class="f"></path><path d="M365 445c1-1 1-2 1-2h1c0 1 0 1-1 1v2h0c0 1 0 1-1 1v1h0v2c-1 1-1 2-2 4v2c0 1-1 1 0 2h-1v4 1l-1 1 1 1v-2h0v-1l1-1v-1-3h1v-1c0-1 0-2 1-3v-1h0v-2h1v-1c0-1 1-1 0-2 1-1 2-2 2-3 0 0 0-1 1-1 0 1 0 2-1 2v2c-1 1-1 1-1 2h0c0 1-1 1-1 2h0v2c-1 1-1 1-1 2v1 1l-1 1v2c-1 0-1 1-1 1v2 1l-1 3-1 1h-1c2-8 2-16 5-23z" class="h"></path><path d="M365 476l9-22c1 1 0 4-1 6l4-4c1-2 4-4 6-4l-2 1c-6 4-9 9-11 16-2 2-3 5-5 7h0z" class="K"></path><path d="M35 561c0 5 2 8 4 12l7 12 1 2c-2 0-5-6-6-8h-1 0c-4-5-6-12-5-18z" class="U"></path><path d="M288 637c1-1 2-1 4-1l1 1h0c-1 1-1 0-1 1s-1 1-1 1h-1c-2 0-4 1-5 2-8 1-16 3-22 7l-1 2-1-2c1 1 3-1 4-1l6-3c2-1 5-2 7-3 1 0 3 0 4-1v-1c1 0 2-1 3-1s2 0 3-1z" class="N"></path><path d="M288 637c1-1 2-1 4-1l-2 2c-1 0-1 0-2-1z" class="O"></path><path d="M76 412l3-7v1l1-1c0 2-1 3-1 4l-3 6c-1 3-4 16-5 17v-1l3-9c-1-1-1-2-1-3h0c0-2-1-2 0-3 2-2 2-4 3-5h0v1z" class="K"></path><path d="M73 416c2-2 2-4 3-5h0v1c-1 4-2 7-2 10-1-1-1-2-1-3h0c0-2-1-2 0-3z" class="h"></path><path d="M298 640l1 1c-6 6-15 5-22 9-3 1-7 3-9 5-5 3-9 8-15 9 1-1 4-2 6-4 3-2 5-5 8-6l12-6c2-1 4-1 5-1 5-2 8-4 11-7l1 1-1 2h0l3-3z" class="V"></path><path d="M453 630h0v2c-4 2-10 6-15 7 0-1-1-1-2-1 0-1 2-1 2-1l1-1v-1l-2-1 6-1h0l10-3z" class="P"></path><path d="M437 634l6-1c-1 2-2 3-5 4l1-1v-1l-2-1z" class="S"></path><path d="M48 603h1 0l1 1c1 0 1 0 1 1 5 6 12 9 18 13-1 0-2 0-3-1 0 0-1-1-2-1l-6-4v1l4 3h1c1 1 2 1 2 3-1-1-3-1-4-2v-1c-1 0-2-1-2-1l-2-2-3-3c-3-1-5-4-7-6l1-1z" class="j"></path><path d="M112 658c-1 1-2 2-3 2-8 5-17 4-26 2-1 0-5-1-5-2 1-1 5 1 6 1 6 1 11 1 17 0 3-1 7-3 10-4l1 1z" class="a"></path><path d="M78 394c0-2 1-4 1-5 1-2 1-3 2-4l5-7c0-1 1-2 2-3 0 3-1 5-2 7-2 3-4 7-4 10l-1 2-1 1h-1l-2 2h0v-1l1-2z" class="e"></path><path d="M78 394h0l1-1c0-1 0-2 1-2 1 1 0 2 1 3l-1 1h-1l-2 2h0v-1l1-2z" class="h"></path><path d="M372 504c3-3 5-6 8-9 1-3 3-5 4-8l1 1c-3 6-7 11-11 16-3 2-6 5-9 7s-7 4-10 6h-1v-1l2-1v-1h0l-1-1 1-1h0 2l1-1h1 1c2 0 3-1 4-1 3-2 5-4 7-6z" class="H"></path><path d="M359 511h1 1c2 0 3-1 4-1-1 0-1 1-2 2l-3 1c-1 1-2 2-3 2h-1v-1h0l-1-1 1-1h0 2l1-1z" class="V"></path><path d="M356 512v1h1c1-1 1-1 2-1l1 1c-1 1-2 2-3 2h-1v-1h0l-1-1 1-1z" class="U"></path><path d="M80 469h1v1h0l1 2h1c-1 3-2 6-2 8-1 5-3 9-3 14v3 5c-1 2 0 3-1 5v-2-3c1-8 0-15 1-23h0v-2l1 1 1-9z" class="J"></path><path d="M80 469h1v1h0l1 2h1c-1 3-2 6-2 8h-1c-1 1-1 2-1 3h0v-5l1-9z" class="a"></path><path d="M81 470h0l1 2h1c-1 3-2 6-2 8h-1c1-3 1-6 1-10z" class="c"></path><path d="M52 615l-4-3v-1c8 8 17 13 28 16 3 1 5 2 8 3h12 3v1c-4 1-9 0-13 0-13-2-24-7-34-16z" class="Y"></path><path d="M381 453v2c-1 1-3 2-3 4l-1 2c-1 1-1 2-2 2-2 4-3 9-5 14-1 0-1 1-2 2v-1-3h0c1-2 2-4 2-6 2-7 5-12 11-16zM245 655h1 2l1-1c1 0 1 0 2-1h1s1 0 1-1h1s1 0 1-1c1 0 1 0 2-1 1 0 3-1 4-2l1 2c-1 1-1 1-2 1v1c-1 0-2 1-3 1-3 2-5 3-8 4v-1l-1 1h-1-2c-1 1-3 1-4 2h-3-1-1c-1 1-2 1-2 1h-3v-1l14-4z" class="h"></path><path d="M52 615c10 9 21 14 34 16v1c-1 0-1 1-2 1-7-1-13-3-19-6-2-2-4-4-7-6-2-1-5-3-7-5l1-1z" class="j"></path><defs><linearGradient id="A" x1="190.614" y1="155.755" x2="193.128" y2="142.091" xlink:href="#B"><stop offset="0" stop-color="#2a2a2a"></stop><stop offset="1" stop-color="#4b4a4b"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M192 131l1 1v8h0c0 1 1 1 1 1h1 1c1 2 1 5 1 7l-1 2-1-1-1 4h0v-1h-1c0 2-1 3-2 5h-1l-1-1c-1 1-1 2-2 2 1-2 2-3 3-5 3-6 2-15 2-22z"></path><path d="M195 141h1c1 2 1 5 1 7l-1 2-1-1-1 4h0v-1h-1c1-1 0-3 1-4v-1-5-1h1z" class="M"></path><path d="M194 141h1c1 1 1 3 0 4h-1v-3h0v-1z" class="P"></path><path d="M195 141h1c1 2 1 5 1 7l-1 2-1-1v-4c1-1 1-3 0-4z" class="b"></path><path d="M294 631v2l-1 1c0 1 1 1 1 1 0 2-1 3-2 4h0c-3 3-6 3-10 4l-4 2h-1l13-6h1s1 0 1-1 0 0 1-1h0l-1-1c-2 0-3 0-4 1s-2 1-3 1-2 1-3 1v1c-1 1-3 1-4 1h-1c-2 1-4 1-6 1v-2l4-1 15-5c1-1 1-1 2-1v1c1-1 1-2 2-3z" class="L"></path><path d="M275 639l15-5h0c-2 2-5 3-8 4-2 0-3 1-5 2h0c2 0 3-1 5-1v1c-1 1-3 1-4 1h-1c-2 1-4 1-6 1v-2l4-1z" class="J"></path><path d="M130 324c0 1 1 1 1 2-2 3-4 7-6 10-2 1-2 3-3 4-1 0-1 0-2 1l-1 2-1-1c0-1 1-1 1-2h0c-1 0-1 1-2 1-3 3-6 5-10 6 7-4 13-9 18-14-2-1-3 0-4 1-1-1 1-1 1-2 0 0 0-2 1-2l1-1 4-4c1 0 1 0 2-1z" class="f"></path><path d="M123 330l1-1 2 1h1v-1c0 2-1 2-2 3v1c-2-1-3 0-4 1-1-1 1-1 1-2 0 0 0-2 1-2z" class="M"></path><path d="M369 443h0v-1l1-1v1 1 1c-1 1-1 2-1 3 0 0-1 1-1 2v1l-1 3h0c-1 1-1 1-1 2v2 1c-1 6-4 13-6 19-2 3-4 7-7 10v-1c2-4 4-7 5-11 1-3 1-5 2-7h1l1-1 1-3v-1-2s0-1 1-1v-2l1-1v-1-1c0-1 0-1 1-2v-2h0c0-1 1-1 1-2h0c0-1 0-1 1-2v-2c1 0 1-1 1-2z" class="i"></path><path d="M77 430h1v7-1l1 1v2l-1 9-1 3-1 3h-1c-1 4-1 9-3 13l2-12c0-2 1-3 1-5s-1-4-1-6c2-5 2-10 3-14z" class="V"></path><path d="M75 454c0-2 1-6 2-7 0 0 0 1 1 1l-1 3-1 3h-1z" class="e"></path><path d="M78 437v-1l1 1v2l-1 9c-1 0-1-1-1-1l1-10z" class="h"></path><path d="M369 497c2-1 2-2 3-3 2-5 5-11 6-16 3-8 4-16 11-20 2 0 3-1 5-1h0c-5 2-9 5-12 10-1 5-2 9-4 13-2 7-4 15-9 21-2 4-5 6-8 9l-1-1h0-1c3-2 5-5 7-7 1-2 2-3 3-5h0z" class="J"></path><path d="M271 642c2 0 4 0 6-1h1c-2 1-5 2-7 3l-6 3c-1 0-3 2-4 1-1 1-3 2-4 2-1 1-1 1-2 1 0 1-1 1-1 1h-1c0 1-1 1-1 1h-1c-1 1-1 1-2 1l-1 1h-2-1c2-2 4-3 6-5 6-4 13-6 20-8z" class="n"></path><path d="M46 597c-1-2-4-5-5-8-2-3-3-7-4-10 1 1 2 4 3 6 1 3 4 8 7 9 2 2 4 5 6 6l3 4c3 4 9 7 14 9h-1 0l-6-3c2 4 9 5 12 7v1c-3-1-7-3-10-5l-3-3c-2-2-4-3-6-4-2-2-4-3-6-4-1-2-2-3-4-5z" class="B"></path><path d="M92 508h0 1v1c1 1 1 2 3 3-1 2-1 1-1 3h0c0 2 1 4 0 6v5 3 1c-1-1-1-1-1-2v4h0 0l-1-1c-1 4-1 8-4 11v-1c2-4 2-8 3-13 1-6-1-12 0-18v-2z" class="L"></path><path d="M93 508v1c1 1 1 2 3 3-1 2-1 1-1 3h0c0 1-1 1-1 1-1-1-2-6-1-8z" class="M"></path><path d="M94 516s1 0 1-1c0 2 1 4 0 6v5 3 1c-1-1-1-1-1-2v-12z" class="P"></path><path d="M261 642v1c4-1 7-2 10-3v2c-7 2-14 4-20 8-2 2-4 3-6 5l-14 4-8 3c-3 1-7 1-10 2 3-2 7-3 11-4 7-2 15-3 22-7 3-1 6-4 9-6-3 1-5 3-8 3l-5 2c1-2 4-2 6-3l11-6 2-1z" class="R"></path><path d="M73 605c-11-6-22-18-26-30-2-5-2-10-3-16v-4c0 2 1 4 1 6 0 4 1 9 3 13 1 3 3 6 4 8 3 5 5 8 9 12 2 1 3 2 5 3h-1s-1-1-2-1h0c2 3 6 5 9 7 3 1 6 4 10 4h2l3 1c-2 0-4-1-5 0-1 0-3-1-4-1-2-1-3-2-5-2z" class="k"></path><path d="M355 544c0-1 0-1 1-1h1l4 1h0c2 0 4 1 6 1h0c1 1 2 1 3 2s2 2 2 4c1 1 2 2 1 3 0 2 1 3 2 5 0 2 0 4-1 5v1 1l-1-1h1v-5c0-1 0-1-1-2v-1c0-2-3-5-6-7-3-1-4-1-6-3h-1l-1-1h-2 0c-2-1-4-1-6-1 2 0 2 0 4-1z" class="a"></path><path d="M88 511l1-1h0v4c1-1 1-2 2-3h0l1-1c-1 6 1 12 0 18v-5c-1 1 0 4-1 5 0 2 0 3-1 5-1 3-1 6-3 9v-1h0l2-8c-1-2-1-3-1-4v-18z" class="H"></path><path d="M88 511l1-1h0v4h0c0 2 1 5 1 7 0 4 0 8-1 12-1-2-1-3-1-4v-18z" class="e"></path><path d="M84 491c1 0 1 0 1 1l-1 3 1 1v6 6-3l-1-1v4l-1 7c0 2 0 4-1 5-1-1 0-2 0-3l-1-1c-1-1-1-1-1-2h0c-1-2-1-3-1-5l1-1c1-1 0-1 1-2h0v1c0-5 0-11 2-16v2c1-1 1-2 1-2z" class="D"></path><path d="M84 491c1 0 1 0 1 1l-1 3c-1 3-2 7-1 10l-1 1c-1-3 1-10 1-13 1-1 1-2 1-2z" class="i"></path><path d="M79 509l1-1c1-1 0-1 1-2h0v1l1 10-1-1c-1-1-1-1-1-2h0c-1-2-1-3-1-5z" class="j"></path><path d="M83 505c-1-3 0-7 1-10l1 1v6 6-3l-1-1v4-4l-1 1z" class="R"></path><path d="M123 641l1 1h0l1 1h0c1-1 0-1 1-1 0 1 0 2 1 3v1 1h1c1-1 0-2 0-3l2 2v-1c1 0 2 1 3 2h0l-1 1c2 1 4 4 5 6l-7-8-1 1c0 2 0 4 1 6-1 1-1 2-2 2l-1-6-2-2v-3h0l-1 1c-3 5-7 10-12 13l-1-1c5-2 8-6 10-10-6 6-15 10-24 12v-1c4 0 7-2 11-4 4-1 8-4 11-7 1 0 2-2 3-3l-1-1 2-2z" class="G"></path><path d="M123 641l1 1c0 1-1 2-2 2l-1-1 2-2z" class="I"></path><path d="M150 277c-2 0-5-2-6-3v-1c1 1 3 1 4 2s1 1 2 1v-1l-1-1c-2-1-3-3-4-4l-2-1 1-1h0c1 2 2 3 3 4 2 1 4 1 6 2 1 1 4 1 5 0 1 0 2-1 3-1 2 0 4-1 6-2v1c-2 1-3 2-4 2-1 1-3 1-3 2 3 0 7-2 10-4 0-1 0-1 1 0-3 3-7 4-11 5v1h2c-1 1-3 1-4 2h-3s-1 0-1 1h-1c-2-1-3-1-5-2v-1h0 2v-1z" class="a"></path><path d="M150 277l1-1v1h3 3 0c0-1 1-1 2-1 0 1 1 1 1 1v1h2c-1 1-3 1-4 2h-3s-1 0-1 1h-1c-2-1-3-1-5-2v-1h0 2v-1z" class="W"></path><path d="M150 277c1 0 2 1 2 2 1 0 2 1 2 1l-1 1c-2-1-3-1-5-2v-1h0 2v-1z" class="i"></path><path d="M98 644h-1c-6 1-13 0-19-1-10-3-18-10-25-16v-1l2 2c8 9 21 14 33 15 6 0 11-1 16-2 2-1 4-2 6-2h0l6-3h0c0 1 0 1-1 1s-2 2-4 2c-2 1-5 2-7 3-1 0-3 0-4 1h1c1 0 5-1 6-1l3-1c3-1 4-3 6-4v1s-3 2-3 3h0c-3 3-7 4-11 5 1-1 0-1 2-1v-1h2-1 0c-1 0-1 0-2 1-1 0-3 0-5-1z" class="N"></path><path d="M113 641h0c-3 3-7 4-11 5 1-1 0-1 2-1v-1h2-1 0c-1 0-1 0-2 1-1 0-3 0-5-1 5-1 10-1 15-3z" class="e"></path><path d="M92 562c-1 0-2 1-3 2-2 1-5-1-7-1-3-1-4-3-6-5v-1c-1 0-1-1-1-1h0v-1l2 2c1 3 4 4 7 5h1c2 1 3 1 5 0l-1-1c0 1-2 1-3 1-1-1-2-1-3-1l-1-1h0c-2-1-5-3-6-5-1-3-3-5-4-8-1-6 0-14 4-18h0c-1 2-2 5-2 8v1l-1 1c0 7 1 11 5 17 1 1 3 3 5 3 2 1 4 2 7 1l-1-1c1-1 2-1 3-2 1-2 2-4 2-7 0-2 0-4 1-6 0 3 1 8 0 10v2c0 1-1 1-1 2 0 2-2 3-2 4z" class="J"></path><path d="M55 627c6 4 13 9 20 11 4 1 9 2 13 2 7 1 15 1 21-2l1 1c-2 0-4 1-6 2-5 1-10 2-16 2-12-1-25-6-33-15v-1z" class="h"></path><path d="M349 548h5c4 1 12 4 15 7 1 1 2 4 1 6h0v-1c-1-2-2-3-3-5h0v1c3 4 3 9 1 13 0 2-1 4-3 5h0c3-3 3-6 3-11 0-2-3-6-5-8-4-3-7-4-12-4-3 0-6 1-9 3h0c-1 0-2 1-2 0 3-4 9-5 13-6h-4z" class="F"></path><path d="M60 543c2-11 3-21 12-29v1c-3 3-6 6-7 11-2 4-2 8-2 13 0 1-1 4 0 5h1v1h-1c0 1 0 2 1 2h0v3 1 2h0v2 1c0 1 1 4 2 6 1 1 2 3 3 5l1 1 1 2 2 1 1 2h-1c-2-2-4-5-6-8l-1-3c-1-1-2-1-2-2h-1-1l-3-15v-5 4c1 1 1 2 1 2h0v-3z" class="V"></path><path d="M63 539c0 1-1 4 0 5h1v1h-1c0 1 0 2 1 2h0v3 1 2h0v2l-1-2c-1-2-2-7-1-9 1-1 1-3 1-5zm-4 1v4c1 1 1 2 1 2h0v-3c1 6 2 11 4 17h-1-1l-3-15v-5zm48 112c0-1 2-1 3-1 2-1 4-2 5-3h1c1-1 2-1 3-1-3 3-7 6-11 7-4 2-7 4-11 4v1h0c-3 0-7 0-9-1-1 0-1 0-1-1h-1-1-1-2c-1-1 0-1-1-1h-1-2 0c-1 0-2-1-3-1h1c1 0 1-1 1 0 2 0 2 0 4-1h5v1h-5 1c1 1 4 0 5 0 1 1 5 1 6 1-1-1-1-1-2-1s-1-1-2-1l3 1h3v-1l7-1c2 0 3-1 5-1z" class="h"></path><path d="M107 652c0-1 2-1 3-1 2-1 4-2 5-3h1c1-1 2-1 3-1-3 3-7 6-11 7-4 2-7 4-11 4h-4l-1-1h1 1-2c2-2 3 0 5-1h1c1-1 2-1 3-1 2-1 5-2 6-3h0z" class="a"></path><path d="M94 657h3l1-1h1c2 0 3 0 5-1h0 1l1-1h2c-4 2-7 4-11 4h-4l-1-1h1 1z" class="n"></path><path d="M82 403c0 3 1 5 1 8-1 3-3 8-3 11l-2 8h-1c-1 4-1 9-3 14 0 2 1 4 1 6s-1 3-1 5v-2-1l-1 1c-1 0-1 1-1 1v1h-1v-1s0-1 1-2v-1-8h0l1-1v-1-1h0c1-6 2-11 3-16s2-10 4-15c1 0 2-4 2-6z" class="L"></path><path d="M73 440c1-2 1-3 2-5l2-10c1-3 1-6 3-9l-3 14c-1 4-1 9-3 14 0 2 1 4 1 6s-1 3-1 5v-2-1l-1 1c-1 0-1 1-1 1v1h-1v-1s0-1 1-2v-1-8h0l1-1v-1-1h0z" class="i"></path><path d="M210 126c4 6 8 12 14 17s13 7 20 12c-1 1-2 3-2 4-2-3-4-5-7-7-1 0-1 0-2-1l-9-3c0-1 0-1-1-1 0 1 1 2 1 3-1-1-1-1-2-1v2c-1-1-2-2-2-3s1-1 1-1c0-1-1-3-1-3-1-2-1-3-2-4-2-4-5-8-8-11l-1-1 1-2z" class="d"></path><path d="M116 633c3-2 6-3 8-6 1-1 2-3 3-3 0 1-1 2-1 3 0 2-1 4-2 6h0v2l-1 1c-1 1-3 3-3 4-1 1-2 2-4 3h-1c-1 0-1 1-2 1l-1-1c-2 1-4 2-7 3-2 0-3 0-5 1l-1-1h1 0 2c4-1 8-2 11-5h0c0-1 3-3 3-3v-1c-2 1-3 3-6 4l-3 1c-1 0-5 1-6 1h-1c1-1 3-1 4-1 2-1 5-2 7-3 2 0 3-2 4-2s1 0 1-1h0l-6 3h0l-1-1c1-1 6-3 7-3v-2z" class="V"></path><path d="M126 627c0 2-1 4-2 6-2 1-3 3-5 4l-1 1-1-1c1-1 2-1 3-2v-1h-1v1l-1-1c1 0 1-1 2-1 1-1 4-3 5-4 0-1 0-1 1-2zm-13 17c1 0 1-1 2-1h1c2-1 3-2 4-3 0-1 2-3 3-4h2 0c0 1 0 1-1 2v1 1s-1 0-1 1l-2 2 1 1c-1 1-2 3-3 3s-2 0-3 1h-1c-1 1-3 2-5 3-1 0-3 0-3 1-2 0-3 1-5 1l-7 1c1 0 2 0 3-1h2l-1-1 3-1-1-1h-1-1c-1 1-1 1-2 0v-1h3-3c2-1 5-1 7-2 3 0 6-2 9-3z" class="T"></path><path d="M102 651c1-1 3-1 4-2h2c1 0 3-1 5-1v-1h2 0c-1 1-2 2-4 2-1 1-2 1-3 1s-3 1-4 1c-1 1-3 1-4 2l-1-1 3-1z" class="e"></path><path d="M112 646h0c0 1-1 1-2 1 0 1-1 1-1 1-1 1-3 1-5 1l-3 1h-1-1c-1 1-1 1-2 0v-1h3l12-3z" class="j"></path><path d="M113 644c1 0 1-1 2-1h1c2-1 3-2 4-3 0-1 2-3 3-4h2 0c0 1 0 1-1 2v1 1s-1 0-1 1l-2 2-3 2v-1c0-1 3-2 4-4h-1c-1 1-3 3-5 4l-4 2h0l-12 3h-3c2-1 5-1 7-2 3 0 6-2 9-3zm240-121c8-3 18-5 24-12 2-3 4-6 6-10h1c-2 5-5 8-8 13l-2 3c-2 2-5 4-7 6l-1-1c-4 3-9 5-14 8-2 1-4 2-6 2 2-2 4-3 7-5-4 0-5 1-8 3v-1l3-2c2-1 4-2 5-4z" class="B"></path><path d="M353 527c1-1 3-2 5-3l12-7c1-1 3-2 5-3-3 3-6 6-9 8-4 3-9 5-14 8-2 1-4 2-6 2 2-2 4-3 7-5z" class="e"></path><path d="M374 504h0c2-1 3-2 4-3-1 2-2 3-3 5-2 2-4 6-7 8h-1l-9 6-5 3h0c-1 2-3 3-5 4l-3 2h0v-2l-2 1h0c0-1 1-1 1-2h-1v-1c1-1 3-1 4-2l4-2 1-1h-2 0-1l1-1c1 0 3-1 4-2h1c3-2 7-4 10-6s6-5 9-7z" class="a"></path><path d="M355 517v1c2-1 5-3 7-3h0l-5 3c-1 1-2 1-3 1 0 1-1 1-2 1h-2 0-1l1-1c1 0 3-1 4-2h1z" class="T"></path><path d="M357 519c3-1 5-3 7-4 1 0 2-1 3-1l-9 6-5 3h0c-1 2-3 3-5 4l-3 2h0v-2l-2 1h0c0-1 1-1 1-2h-1v-1c1-1 3-1 4-2l4-2 1-1c1 0 2 0 2-1 1 0 2 0 3-1v1z" class="g"></path><path d="M347 523l4-2-1 1h0l1 1h-2v1l-2-1z" class="W"></path><path d="M345 527l2-2c1 1 1 1 1 2l-3 2h0v-2zm7-7c1 0 2 0 2-1 1 0 2 0 3-1v1c-1 2-4 3-6 4l-1-1h0l1-1 1-1z" class="U"></path><path d="M55 623l2 1-2 1 2 2c1 0 1 0 1-1h0l1 1 2-1 4 3h0l-1 1h0c1 1 2 2 3 2 1 2 3 2 5 3 3 1 6 2 9 2 1 0 2 1 2 1h8c2 1 7 2 9 1s4-2 5-2c3-1 6-3 8-3h3v-1 2c-1 0-6 2-7 3-6 3-14 3-21 2-4 0-9-1-13-2-7-2-14-7-20-11l-2-4h0 1 1z" class="G"></path><path d="M55 623l2 1-2 1-1-2h1z" class="h"></path><path d="M105 637h1v1c-2 0-4 1-6 1-1 0-1 1-2 1-3 0-5 0-7-1-4 0-7 0-11-1l1-1c1 0 2 1 2 1h8c2 1 7 2 9 1s4-2 5-2z" class="W"></path><path d="M57 627c1 0 1 0 1-1h0l1 1 2-1 4 3h0l-1 1h0c1 1 2 2 3 2 1 2 3 2 5 3 3 1 6 2 9 2l-1 1c-4-1-8-1-11-3-2-1-2-2-3-2l-9-6z" class="i"></path><path d="M95 526c0 2 0 4 1 6s0 6 0 8v1l-1 1v2c-1 2-1 4-1 6 0 3-1 5-2 7-1 1-2 1-3 2h0c-4 0-8-2-11-5-2-3-4-6-4-10 1 0 2 4 2 5s1 2 1 3l1-1-1-3-1-1v-1c1 0 1-1 0-2h0c0-1 0-2 1-2l2 5h0c1 3 4 6 7 6 2 1 4 0 6-1 1-3 1-7 2-10-1 3-2 4-4 5h-1c1 0 1 0 2-1 3-2 3-10 3-14h0v-4c0 1 0 1 1 2v-1-3z" class="S"></path><path d="M78 551c2 2 4 5 7 6h1c1 0 3 0 5-1v1c-1 0-2 1-3 1-2 0-3 0-4-1-3-1-5-3-7-5l1-1z" class="j"></path><path d="M77 548l-1-1v-1c1 0 1-1 0-2h0c0-1 0-2 1-2l2 5h-1c2 4 5 7 9 7 1 1 2 0 4 0h0c-1 1-2 2-3 2-3 0-5-1-7-3-1-1-2-3-4-5h0z" class="m"></path><path d="M466 609h1v2c-1 1-1 2-1 4 0 0 1 0 1-1v2h1c0-1 1-3 2-3h0l1-1h1s1-2 1-3l1 1v2l-3 3c-1 2-2 4-4 5v1h0l-1 2h0l1-1v1c-2 1-4 3-6 4-2 2-5 3-8 5v-2h0c-3-1-4-1-6 0l-1-1h2c1-1 1-1 2-3 2 0 4-1 6-2 5-4 9-9 10-15z" class="V"></path><path d="M473 609l1 1v2l-3 3c-1 2-2 4-4 5 0-2 1-2 3-4h-2c1-1 2-2 2-3l1-1h1s1-2 1-3z" class="Q"></path><path d="M467 621h0l-1 2h0l1-1v1c-2 1-4 3-6 4-2 2-5 3-8 5v-2c5-3 9-6 14-9z"></path><path d="M100 646h0-1l1 1c2-1 3-1 5-1 3-1 5-2 7-3l1 1c-3 1-6 3-9 3-2 1-5 1-7 2h3-3v1c1 1 1 1 2 0h1 1l1 1-3 1 1 1h-2c-1 1-2 1-3 1v1h-3l-3-1h-1l-1-1c-1 0-4 0-5-1-7-1-14-4-20-7h0c4 1 9 4 14 5h3 1 2 1c1 0 3 0 4-1h0c-3 0-5-1-7-1l-2-1h1v-1c4 1 9 1 13 1 2 0 5-1 8-1z" class="m"></path><path d="M80 650h2c3 1 7 1 10 0l1 1h-1c-2 1-7 1-9 1l-4-2h1z" class="j"></path><path d="M87 653c2 0 5 0 8-1h4l1 1h-2c-1 1-2 1-3 1v1h-3l-3-1h-1l-1-1z" class="i"></path><path d="M111 602c1-1 2-2 4-3v1c-2 2-5 4-8 6h0c1 0 2-1 2-1h1c3-2 5-4 7-6 0 2-1 3-2 4-2 2-6 4-8 5-2 0-4 1-5 2-1 0-2 1-2 1-1 1-2 1-3 1h-9l-5-1-3-1c-2 0-3-1-4-1l-1-1h-1c0-1-1-1-1-1-1 0-2 0-2-1h0c-2-1-4-2-5-3h0c2 1 3 1 5 2h0 2c2 0 3 1 5 2 1 0 3 1 4 1 1-1 3 0 5 0l-3-1h6c-1 0-2 0-2 1 1 0 11 0 13-1 2 0 3-2 4-2h2c1-1 3-1 4-3z" class="g"></path><path d="M73 607c-1 0-2 0-2-1h0c-2-1-4-2-5-3h0c2 1 3 1 5 2h0 2c2 0 3 1 5 2 1 0 3 1 4 1 1 1 2 2 3 2s1 0 2 1h-3c0-1-1-1-2-1h0l-3-1-6-3v1z" class="m"></path><path d="M106 632c2 0 4-1 5-2 2-1 5-1 7-3l1 1c2-2 4-3 5-5 2-1 3-4 4-5h0v2c0 1 0 2-1 4h0c-1 0-2 2-3 3-2 3-5 4-8 6v1h-3c-2 0-5 2-8 3-1 0-3 1-5 2s-7 0-9-1h-8s-1-1-2-1c-3 0-6-1-9-2-2-1-4-1-5-3 6 3 11 5 18 5h4l-1-1c1-1 5 0 7 0 1-2 3-1 5-1v-1s1 1 2 1h0l3-1 1-1v-1h0z" class="c"></path><path d="M88 636c1-1 5 0 7 0h0 0c-1 1-2 1-3 2-2-1-5 0-7-1h4l-1-1z" class="U"></path><path d="M100 634s1 1 2 1h0l3-1c1 0 2 0 2 1-2 1-4 2-7 3-2 1-5 0-8 0 1-1 2-1 3-2h0 0c1-2 3-1 5-1v-1z" class="W"></path><path d="M105 634c1 0 2 0 2 1-2 1-4 2-7 3-1-1-3-1-4-2 1-1 3 0 5 0l1-1h0l3-1z" class="g"></path><path d="M106 632c2 0 4-1 5-2 2-1 5-1 7-3l1 1c2-2 4-3 5-5 2-1 3-4 4-5h0v2c-5 8-12 11-21 15 0-1-1-1-2-1l1-1v-1h0z" class="a"></path><path d="M119 586h1c0 1 0 1 1 2l1 1c1 2 2 4 3 7v1 3l-1-1c-1 1-1 3-2 4 0 1-1 2-2 3h0c-2 2-1 2-1 3 0 0-2 1-3 1 0 1-1 1-1 1l-2-1 5-3v-1h-1 0c-1 1-3 2-4 3-4 3-10 4-15 4v-1c-3 1-8 1-10 0h9c1 0 2 0 3-1 0 0 1-1 2-1 1-1 3-2 5-2 2-1 6-3 8-5 1-1 2-2 2-4l1-1 1 2c2-4 2-7 2-10-1-1-1-2-2-3v-1z" class="C"></path><path d="M118 598l1 2c-1 1-2 4-5 5h-1c-1 1-4 2-5 3h-1c2-1 6-3 8-5 1-1 2-2 2-4l1-1z" class="P"></path><path d="M100 611h3c2-1 4-1 6-2 1 0 3-1 4-1 1-1 3-2 5-3h0c2-2 3-4 4-6l1-1c0 2-1 4-2 6-1 0-1 1-1 2h0c-2 2-1 2-1 3 0 0-2 1-3 1 0 1-1 1-1 1l-2-1 5-3v-1h-1 0c-1 1-3 2-4 3-4 3-10 4-15 4v-1c-3 1-8 1-10 0h9c1 0 2 0 3-1z" class="W"></path><path d="M293 630c2 0 3 0 5 1 1 0 3 1 4 2h-1v1c0 2-1 5-2 7l-1-1-3 3h0l1-2-1-1c-3 3-6 5-11 7-1 0-3 0-5 1l-12 6v-1h1l-1-1-1 1h-1l1-1c1-1 1 0 1-1 1 0 1 0 1-1 1 0 2-1 3-2 0 0 1 0 1-1l3-1 2-1h1l4-2c4-1 7-1 10-4h0c1-1 2-2 2-4 0 0-1 0-1-1l1-1v-2l-1-1z" class="h"></path><path d="M277 645h1l4-2c1 1 1 1 2 1 0 1-1 1-1 2h0-3 0 0 0v-1l-3 2-2-1 2-1z" class="e"></path><path d="M292 639l1 1c-1 0-1 1-1 2-3 2-6 2-9 4 0-1 1-1 1-2-1 0-1 0-2-1 4-1 7-1 10-4z" class="j"></path><path d="M293 630c2 0 3 0 5 1 1 0 3 1 4 2h-1v1c0 2-1 5-2 7l-1-1-3 3h0l1-2-1-1c1 0 1-1 1-1 1-2 1-3 1-5h0c-2 3-2 6-5 8 0-1 0-2 1-2l-1-1h0c1-1 2-2 2-4 0 0-1 0-1-1l1-1v-2l-1-1z" class="C"></path><path d="M293 630c2 0 3 0 5 1h0l-1 1h0l-1-1-1 1-1 3s-1 0-1-1l1-1v-2l-1-1z" class="c"></path><path d="M296 641c1-1 2-3 2-4h0v-4-1h0 1c1 2 1 3 0 5l-1 3-3 3h0l1-2z" class="a"></path><path d="M55 623c-7-7-12-14-16-23v-1c3 7 7 13 12 17 2 2 5 4 7 5 3 2 5 4 7 6 6 3 12 5 19 6 2 1 5 1 7 1 2 1 5 0 7 0h2v1c-2 0-4-1-5 1-2 0-6-1-7 0l1 1h-4c-7 0-12-2-18-5-1 0-2-1-3-2h0l1-1h0l-4-3-2 1-1-1h0c0 1 0 1-1 1l-2-2 2-1-2-1z" class="H"></path><path d="M57 624c1 0 2 1 4 2l-2 1-1-1h0c0 1 0 1-1 1l-2-2 2-1z" class="j"></path><path d="M65 629c7 5 14 8 23 7l1 1h-4c-7 0-12-2-18-5-1 0-2-1-3-2h0l1-1z" class="a"></path><path d="M95 515h2c-1 2-1 4-1 6h2v4h1v2c2 2 1 9 1 11 1 2 1 3 1 5 0 0-1 1 0 1v6c0 1 0 1-1 1l1 4v1h-1v4h-1 0v1l-1-4c-1 1-1 3-1 5l-1-1c0-1 0-2 1-3v-2l-1-1v1h-1v-2c1-2 0-7 0-10v-2l1-1v-1c0-2 1-6 0-8s-1-4-1-6v-5c1-2 0-4 0-6h0z" class="E"></path><path d="M96 540h0l1 2h1v2c1 1 1 3 1 4-1 0-2-1-2-1-1-2-1-4-1-6v-1z" class="U"></path><path d="M97 532s1-1 1-2v12h-1l-1-2 1-8z" class="i"></path><path d="M100 549c0-2-1-6-1-8s0-5 1-5v2c1 2 1 3 1 5 0 0-1 1 0 1v6c0 1 0 1-1 1v-2z" class="m"></path><path d="M99 551v-2h1v2l1 4v1h-1v4h-1 0v1l-1-4c-1-3-1-4-1-7l2 1z" class="R"></path><path d="M99 551v-2h1v2l1 4v1h-1v4h-1c0-2 1-3 0-5v-4z" class="S"></path><path d="M95 544v-2l1-1c0 2 0 4 1 6v3c0 3 0 4 1 7-1 1-1 3-1 5l-1-1c0-1 0-2 1-3v-2l-1-1v1h-1v-2c1-2 0-7 0-10z"></path><path d="M95 515h2c-1 2-1 4-1 6h2v4c0 1 1 4 0 5 0 1-1 2-1 2l-1 8h0c0-2 1-6 0-8s-1-4-1-6v-5c1-2 0-4 0-6h0z" class="c"></path><path d="M96 521h2v4c0 1 1 4 0 5 0 1-1 2-1 2 0-3 0-7-1-11z" class="e"></path><defs><linearGradient id="C" x1="212.288" y1="132.687" x2="206.406" y2="137.108" xlink:href="#B"><stop offset="0" stop-color="#646364"></stop><stop offset="1" stop-color="#7a7b7b"></stop></linearGradient></defs><path fill="url(#C)" d="M200 120h1c0 1 0 1 1 2h0 1 1v1l5 5 1 1c3 3 6 7 8 11 1 1 1 2 2 4 0 0 1 2 1 3 0 0-1 0-1 1l-1 1v-1h-1v1h-1-1v1h-1c-2-4-6-8-8-12-2-3-3-7-4-10-2-3-3-5-3-8z"></path><path d="M204 123l5 5 1 1-1 1c-2-1-4-4-5-7z" class="D"></path><path d="M117 341c1 0 1-1 2-1h0c0 1-1 1-1 2l1 1-3 3c-3 4-5 7-9 10l-4 4h-1c-3 2-5 6-8 9-1 2-3 3-4 6v1c0 2-2 4-3 6h1l2-4c1-1 1-2 1-2 0-1 1-3 1-3l1-1c0 2-1 3-2 4l-3 7c-3 8-6 15-8 22l-1 1v-1l1-4v-1s-1-1-1-2l1-3 1-1 1-2c0-3 2-7 4-10 1-2 2-4 2-7l6-7c-4 3-8 8-14 7-1 0-3 0-4-1h1 4c5 0 8-3 11-6l7-7c-3 2-8 5-12 4 3-1 6-2 9-4 4-3 21-16 21-20z" class="M"></path><path d="M81 394l1-2c0 3-1 6-2 8 0 0-1-1-1-2l1-3 1-1z" class="W"></path><path d="M222 151v-2c1 0 1 0 2 1 0-1-1-2-1-3 1 0 1 0 1 1l9 3c1 1 1 1 2 1 3 2 5 4 7 7-1 1-1 1-1 2v1l-3 8h0c-1 1-1 2-1 2-1 1-1 2-1 3h-1c0-3-3-9-4-12l-2-4-2-3-1-1v1c-1-2-2-3-4-5z" class="L"></path><path d="M238 170h-1c-2-3-2-9-6-12 0 0 0-1-1-1h1l2 1c2 2 3 5 5 7-1 2-1 3 0 5h0z" class="J"></path><path d="M238 165v1h1c-1-2-2-5-4-6v-3h0c1 0 2 1 2 2h1c0-1 0-1 1-1 0 1 1 1 1 2 1 1 1 2 1 2l-3 8c-1-2-1-3 0-5z" class="K"></path><path d="M227 156c1 0 2 1 3 2v2c1 0 1-1 2 0 2 2 3 6 4 9 0 0 0 3 1 3-1 1-1 2-1 3h-1c0-3-3-9-4-12l-2-4-2-3z" class="E"></path><path d="M222 151v-2c1 0 1 0 2 1 0-1-1-2-1-3 1 0 1 0 1 1l9 3c1 1 1 1 2 1-1 2 0 2 0 3v1c-1 0-1-1-2-1v-1h-3c-1-1-2-1-3-2v1h-1l-2-3-1 1c0 1 3 3 3 4v1c-1-2-2-3-4-5z" class="S"></path><path d="M226 153c0-1 0-2-1-2l1-1v1c3 1 6 1 8 4h1v1c-1 0-1-1-2-1v-1h-3c-1-1-2-1-3-2v1h-1z" class="b"></path><path d="M57 602c1 0 1 0 2 1 0 0 1 1 2 1h0v1c6 2 11 6 18 8 6 1 12 1 19 0 5 0 11-1 15-4 1-1 3-2 4-3h0 1v1l-5 3-3 1-4 2h0c1 1 1 1 2 1l-3 1-7 2c-1 0-2 0-3 1-3 0-7 1-10 0h-2s-1 0-1-1h-2 0c-1 0-2 0-3 1l-2-1c-3-2-10-3-12-7l6 3h0 1c-5-2-11-5-14-9 1-1 1-1 1-2z" class="C"></path><path d="M57 602c1 0 1 0 2 1 0 0 1 1 2 1h0c2 3 7 6 10 7 1 1 2 2 4 2v1h0l-5-1c-5-2-11-5-14-9 1-1 1-1 1-2z" class="j"></path><path d="M75 617c-3-2-10-3-12-7l6 3h0 1l5 1h0v-1c7 2 13 3 20 3 4 0 7-2 11-3 1 1 1 1 2 1l-3 1-7 2c-1 0-2 0-3 1-3 0-7 1-10 0h-2s-1 0-1-1h-2 0c-1 0-2 0-3 1l-2-1z" class="i"></path><path d="M70 613l5 1h0v-1c7 2 13 3 20 3-4 1-10 1-14 0-1 0-3-1-4-1-3 0-6-1-8-2h1z" class="W"></path><path d="M160 287c1 0 3-1 4-2l9-4c2 0 4-1 5-1 1 1 1 1 2 1-3 2-5 3-8 4h1c-5 3-10 6-15 10h0c0 2-4 4-5 5l-1 1c-2 2-5 5-7 8-1 1-1 2-2 2v-1l3-3-1-1-2 2h0v-2c1 0 2-2 3-2l-1-1c0 1-1 1-1 1-2 1-3 2-4 3h0l-1-1-1 1c-1 1-6 3-7 2 10-4 19-13 27-20-2 1-3 2-5 3-4 3-7 4-11 5h-6 0 0c5-1 11-1 15-4 3-3 6-5 9-6z" class="Y"></path><path d="M172 285h1c-5 3-10 6-15 10h0c0 2-4 4-5 5l-1 1c-2 2-5 5-7 8-1 1-1 2-2 2v-1l3-3-1-1-2 2h0v-2c1 0 2-2 3-2l-1-1c0 1-1 1-1 1-2 1-3 2-4 3h0l-1-1c2-1 4-3 7-4 2-2 4-4 7-6 0 0 1-1 2-1l17-10z" class="g"></path><path d="M69 618h2c2 1 4 2 6 2l8 1c8 1 15 1 23-2 2 0 4-1 6-1 0-1 2-2 2-2 1-1 0-1 1-2h1c2-1 3-3 3-4 1-1 1-1 1-2 1-1 2-5 3-5 0 2-1 3-2 5 0 1-1 2-1 3-1 1-2 2-2 3v1c-1 3-4 4-7 6-1 0-2 1-2 1-2 0-3 1-4 1l-3 1c0 1 0 1-1 1h-1c-4 1-8 1-12 1h-4c-1 0-2-1-3-1h-1c-1 0-3-1-4-1h0c-1 0-3-1-4-1l-3-1s-1 0-1-1c-2 0-3-1-5-2 0-2-1-2-2-3h-1l-4-3v-1l6 4c1 0 2 1 2 1 1 1 2 1 3 1z" class="h"></path><path d="M375 476v-1c1-1 1-3 2-5 2-5 5-11 9-14h0c-2 3-5 6-6 9-3 5-4 11-5 16-1 6-3 11-6 16h0c-1 2-2 3-3 5-2 2-4 5-7 7h1 0l1 1-2 1-1 1h-2 0l-1 1 1 1h0v1l-2 1-1-1c1-1 0-2 0-2v-2l4-3h-3l-2 2c-1 1-3 2-4 3v-1l12-12 2-3c2-2 3-6 5-9 0 1 1 1 1 1 2-3 4-7 5-10h0c0-2 1-2 2-3z" class="D"></path><path d="M359 509h1 0l1 1-2 1-1 1h-2 0l-1 1 1 1h0v1l-2 1-1-1c1-1 1-2 1-2l1-1v-1h2c1-1 1-2 2-2z" class="Q"></path><path d="M373 479h0c0-2 1-2 2-3 0 4-1 8-3 12h-2c2-3 4-6 3-9z" class="h"></path><path d="M370 488h2c-2 5-4 8-8 12v-1c0-2 4-5 4-7 1-1 0-1 1-1l1-3z" class="e"></path><path d="M373 479c1 3-1 6-3 9l-1 3c-1 0 0 0-1 1 0 2-4 5-4 7v1c-1 1-3 3-4 5l-3 3h-3l-2 2c-1 1-3 2-4 3v-1l12-12 2-3c2-2 3-6 5-9 0 1 1 1 1 1 2-3 4-7 5-10z" class="I"></path><path d="M362 497v2h0c0 1-1 1-2 2v-1l2-3z" class="g"></path><path d="M354 508c1-1 2-3 4-4 0 0 1 0 2 1l-3 3h-3z" class="E"></path><path d="M367 488c0 1 1 1 1 1 0 1-1 3-1 4-1 2-3 4-5 6h0v-2c2-2 3-6 5-9z" class="W"></path><path d="M89 479v-2c0-1 1-1 2-1l1 2c-1 3 0 4 0 6h-1l-1 1v8h1l1-1v7h0v2l1 1c0 2 0 4 1 6h0-1v1-1h-1 0v2l-1 1h0c-1 1-1 2-2 3v-4h0l-1 1v18 1l-1-1v-5c-1-1-1-3-1-4h0l-1 1h0c-1-1-1-3 0-4v-7-2-6-6l-1-1 1-3 1-3c1-1 2-7 2-8v-3l1 1z" class="U"></path><path d="M85 517v1h0l1-11h1v6 11c-1-1-1-3-1-4h0l-1 1h0c-1-1-1-3 0-4z" class="e"></path><path d="M87 513c1-2 0-5 1-7h0v5 18 1l-1-1v-5-11z" class="B"></path><path d="M89 479v-2c0-1 1-1 2-1l1 2c-1 3 0 4 0 6h-1l-1 1v8 5c-1 2-1 4-1 6l-1 2h0l1-13c1-5 0-10 0-14z"></path><path d="M88 481v-3l1 1c0 4 1 9 0 14 0 0-1 1-1 2h-1c-1 5-1 10-1 16l-1-1v-2-6-6l-1-1 1-3 1-3c1-1 2-7 2-8z" class="X"></path><path d="M88 481v-3l1 1c0 4 1 9 0 14 0 0-1 1-1 2h-1c0-5 2-10 1-14z" class="T"></path><path d="M91 493l1-1v7h0v2l1 1c0 2 0 4 1 6h0-1v1-1h-1 0v2l-1 1h0c-1 1-1 2-2 3v-4h0l-1 1v-5l1-2c0-2 0-4 1-6v-5h1z" class="Q"></path><path d="M91 511c0-2-1-5 0-6 1 1 1 2 1 3v2l-1 1h0z" class="I"></path><path d="M90 493h1c0 3-1 8 0 11h0c-1-1-1-1-1-2v-4-5z" class="U"></path><path d="M140 307h0c1-1 2-2 4-3 0 0 1 0 1-1l1 1c-1 0-2 2-3 2v2h0l2-2 1 1-3 3v1c0 1-1 1-1 1h-1c-3 4-5 8-8 12 0 1-1 3-1 4 0 2-1 2-2 4l-4 9-5 15c-1 2-1 3-2 5h-1c1-2 1-3 1-4v-1l1-2v-3c1-1 1-2 1-3 0 1 0 1-1 1h-1c-2 2-3 6-5 8h-1c1-2 3-5 5-8 0-1 1-2 2-3s2-4 3-5l-1-1c1-1 1-3 3-4 2-3 4-7 6-10 0-1-1-1-1-2h0v-1l3-3v-2l3-3c-1-1-3 1-4 1-2 1-4 1-6 1h0 1c3-1 7-2 9-5h-1c-1 1-3 0-4 1h-2c-4 0-8 0-11-1h0 1c4 1 10 0 14-1 3-1 5-2 6-4h1z" class="F"></path><path d="M136 315h1l3-3-4 6s-1 1-1 2l-1-1-1 1v-2l3-3z" class="M"></path><path d="M133 320l1-1 1 1c-1 1-3 5-4 6 0-1-1-1-1-2h0v-1l3-3z" class="Y"></path><path d="M123 341c0-2 1-4 3-5-1 1-2 3-2 5l-1 2h1v-1c0-1 1-3 2-4 1-2 1-5 3-7l-9 23v-3c1-1 1-2 1-3 0 1 0 1-1 1h-1c-2 2-3 6-5 8h-1c1-2 3-5 5-8 0-1 1-2 2-3s2-4 3-5z" class="P"></path><path d="M367 523c-2 1-3 2-4 3h0c3-2 7-3 10-5l-2 2c-2 1-3 2-5 2-1 1-3 2-5 3v1h1l25-5c-7 4-15 4-22 5-5 2-8 3-12 5-2 1-3 2-5 3-1 0-2 2-3 2-2 0-5 3-6 5-2 2-3 5-6 8 0 0 0 2-1 2l-1-1v-1h-1c1-1 1-2 1-3s3-5 4-6c0-2 0-2 1-3 3-4 6-7 9-10 3-2 4-3 8-3-3 2-5 3-7 5 2 0 4-1 6-2 5-3 10-5 14-8l1 1z" class="C"></path><path d="M335 543l8-9v1l-5 5c-1 1-2 3-3 4v1-1c1 0 2-2 3-2l-4 6-1 1v3s0 2-1 2l-1-1v-1h-1c1-1 1-2 1-3s3-5 4-6z" class="O"></path><path d="M367 523c-2 1-3 2-4 3h0c3-2 7-3 10-5l-2 2c-2 1-3 2-5 2-1 1-3 2-5 3v1h1c-4 1-7 3-10 5-2 1-3 1-5 2-1 1-1 1-2 1l3-3h0c-2 1-3 2-5 4h0v-1c-2 1-3 2-4 3h-1l5-5v-1l-8 9c0-2 0-2 1-3 3-4 6-7 9-10 3-2 4-3 8-3-3 2-5 3-7 5 2 0 4-1 6-2 5-3 10-5 14-8l1 1z" class="T"></path><path d="M85 453c1 1 1 4 0 6 0 3 1 5 1 8h0l1 1h1v1l3 7c-1 0-2 0-2 1v2l-1-1v3c0 1-1 7-2 8l-1 3c0-1 0-1-1-1 0 0 0 1-1 2v-2c-2 5-2 11-2 16v-1h0c-1 1 0 1-1 2l-1 1c0-2 1-10-1-12v-3c0-5 2-9 3-14 0-2 1-5 2-8h-1l-1-2h0v-1h-1c0-2 1-5 2-7l1 1c0-1 1-2 1-4l1-6z" class="i"></path><path d="M84 474l1-1v7l-2 9-1-1 2-14z" class="X"></path><path d="M85 459c0 3 1 5 1 8h0c1 4 0 9-1 13v-7l-1 1c0-2 1-5 0-6v-3-3c1-1 1-2 1-3z" class="D"></path><path d="M85 453c1 1 1 4 0 6 0 1 0 2-1 3v3 3l-1 4h-1l-1-2h0v-1h-1c0-2 1-5 2-7l1 1c0-1 1-2 1-4l1-6z" class="P"></path><path d="M82 462l1 1c-1 2-1 5-2 7h0v-1h-1c0-2 1-5 2-7z" class="T"></path><path d="M86 467l1 1h1v1l3 7c-1 0-2 0-2 1v2l-1-1v3c0 1-1 7-2 8l-1 3c0-1 0-1-1-1 0 0 0 1-1 2v-2l-1-1v-2l1 1 2-9c1-4 2-9 1-13z" class="m"></path><path d="M84 491c0-1 0-1 1-2h0 0 1l-1 3c0-1 0-1-1-1z" class="e"></path><path d="M88 468v1l3 7c-1 0-2 0-2 1v2l-1-1c1-3 0-6 0-10z" class="Y"></path><path d="M370 455c0-2 0-5 2-7l-2 9c0 2 0 3-1 5-1 1-1 3-2 5-1 4-2 7-4 10v1l2-2h0 0c2-2 3-5 5-7 0 2-1 4-2 6l-1 2h-1v1c0 1-1 1-2 2v1h0c-1 1-1 2-2 2-2 4-4 6-7 9-1 1-5 5-5 6 0 2-1 2-3 4v-1h-2v-1c-1 1-2 3-4 4 0-2 2-2 3-4v-1l-3 2-1-1 1-1c1 0 1-1 2-1 0-1 1-1 1-2v-1l2-2c2-1 4-3 5-5l2-2v1c3-3 5-7 7-10 2-6 5-13 6-19 1-2 1-5 3-6l-1 1c1 1 1 2 2 2z" class="V"></path><path d="M345 500l1-1 10-10h0l-1 2v1c-1 1-5 5-5 6 0 2-1 2-3 4v-1h-2v-1z" class="F"></path><path d="M366 458c1-2 1-5 3-6l-1 1c1 1 1 2 2 2l-1 3v2c0 1-1 1-1 2l-3 8c0 1-1 3-2 4v-1l-2 4h-1c2-6 5-13 6-19z" class="M"></path><path d="M368 456v-3c1 1 1 2 2 2l-1 3c-1 0-1-1-1-2z" class="e"></path><path d="M368 456c0 1 0 2 1 2v2c0 1-1 1-1 2l-3 8c0 1-1 3-2 4v-1c2-5 3-11 5-17z" class="i"></path><path d="M360 477h1 1c0 1-1 2-2 4-3 5-7 10-12 14-2 1-3 3-4 4l-3 2-1-1 1-1c1 0 1-1 2-1 0-1 1-1 1-2v-1l2-2c2-1 4-3 5-5l2-2v1c3-3 5-7 7-10z" class="S"></path><path d="M346 493c2-1 4-3 5-5l2-2v1c-1 2-4 5-6 6v1c0 1 0 1 1 1-2 1-3 3-4 4l-3 2-1-1 1-1c1 0 1-1 2-1 0-1 1-1 1-2v-1l2-2z" class="L"></path><path d="M102 627h1 3c2-1 5-2 8-4 1 0 3-1 5-3 3-2 5-6 6-10 1 0 2-2 2-3s-1-1 0-2h0c1 1 1 1 1 2 0 2 0 5-1 7h2v2 1s0 1-1 1h0c-1 1-2 4-4 5-1 2-3 3-5 5l-1-1c-2 2-5 2-7 3-1 1-3 2-5 2h0v1l-1 1-3 1h0c-1 0-2-1-2-1h-2c-2 0-5 1-7 0-2 0-5 0-7-1 1 0 1-1 2-1v-1c4 0 9 1 13 0v-1h-3-12c1-1 2-1 2-1 4-1 8 0 11-1 1-1 2-1 2-1h3z" class="U"></path><path d="M98 633c0-1 2-1 2-1 2 0 4 0 6-1v1h0c-3 1-5 1-8 1z" class="V"></path><path d="M106 632v1l-1 1-3 1h0c-1 0-2-1-2-1h-2c-1 0-1 0-2-1h2c3 0 5 0 8-1z" class="m"></path><defs><linearGradient id="D" x1="128.683" y1="619.011" x2="119.214" y2="620.911" xlink:href="#B"><stop offset="0" stop-color="#1c1a18"></stop><stop offset="1" stop-color="#3d4143"></stop></linearGradient></defs><path fill="url(#D)" d="M127 614h2v2 1s0 1-1 1h0c-1 1-2 4-4 5-1 2-3 3-5 5l-1-1c-2 2-5 2-7 3-1 1-3 2-5 2v-1c3 0 6-1 8-3 6-3 11-8 13-14z"></path><path d="M102 627h1 3c2-1 5-2 8-4 1 0 3-1 5-3 3-2 5-6 6-10 1 0 2-2 2-3s-1-1 0-2h0c1 1 1 1 1 2-1 1-1 2-1 3 0 2-1 6-3 8-3 5-10 11-17 12h-1l-7 1v-1h-3-12c1-1 2-1 2-1 4-1 8 0 11-1 1-1 2-1 2-1h3z" class="a"></path><path d="M96 630l8-1h1l1 1-7 1v-1h-3z" class="M"></path><defs><linearGradient id="E" x1="108.064" y1="624.264" x2="113.214" y2="628.355" xlink:href="#B"><stop offset="0" stop-color="#282824"></stop><stop offset="1" stop-color="#46454b"></stop></linearGradient></defs><path fill="url(#E)" d="M127 610c0 2-1 6-3 8-3 5-10 11-17 12h-1l-1-1h-1l6-2c4-2 10-5 13-9 2-3 3-6 4-8z"></path><path d="M83 416h2v1 3c-1 1-1 3-1 4 1 1 1 2 2 4h-1c-1 1-1 2-1 3l1 1-1 8v2c1 1 1 3 1 5 0 1 0 2 1 3-1 1-1 2-1 3l-1 6c0 2-1 3-1 4l-1-1c-1 2-2 5-2 7l-1 9-1-1 1-10v-1c-1 1-1 3-2 4h0v-4c1-4 1-8 0-12l-2 8c0 1 0 2-1 3v-1l2-10 1-3 1-3 1-9v-2l-1-1v1-7l2-8 1 1 2-7z" class="T"></path><path d="M79 457v-4h1v4 3l-1-3h0z" class="j"></path><path d="M79 457h0l1 3c0 2 0 5-1 7v-1c-1 1-1 3-2 4h0v-4c2-2 2-7 2-9z" class="e"></path><path d="M80 435v1 2c-1 1-1 4-1 5 0-1 0-1 1-2v-2h0v-1h1v-1h0c0 3-1 8-2 11 0 2 0 3-1 4l-1-1 1-3 1-9 1-4z" class="X"></path><path d="M82 454c0 2 1 4 0 5s0 2 0 3c-1 2-2 5-2 7l-1 9-1-1 1-10c1-2 1-5 1-7v-3h0l1 1c1-1 1-3 1-4z" class="E"></path><path d="M84 431l1 1-1 8v2c1 1 1 3 1 5 0 1 0 2 1 3-1 1-1 2-1 3l-1 6c0 2-1 3-1 4l-1-1c0-1-1-2 0-3s0-3 0-5c0 1 0 3-1 4l-1-1c1-8 2-16 4-24v-2z" class="P"></path><path d="M84 442c1 1 1 3 1 5 0 1 0 2 1 3-1 1-1 2-1 3l-1 6c0 2-1 3-1 4l-1-1c0-1-1-2 0-3s0-3 0-5c0-1 0-2 1-3 0-3 1-7 1-9z" class="a"></path><path d="M82 454c0-1 0-2 1-3 0 2-1 7 0 8h1c0 2-1 3-1 4l-1-1c0-1-1-2 0-3s0-3 0-5z" class="V"></path><path d="M83 416h2v1 3c-1 1-1 3-1 4 1 1 1 2 2 4h-1c-1 1-1 2-1 3v2h-2l-1 4h0v1h-1v1h0v2c-1 1-1 1-1 2 0-1 0-4 1-5v-2-1l-1 4v-2l-1-1v1-7l2-8 1 1 2-7z" class="M"></path><path d="M84 424c1 1 1 2 2 4h-1c-1 1-1 2-1 3v2h-2c0-3 2-7 2-9z" class="J"></path><path d="M80 422l1 1-1 12-1 4v-2l-1-1v1-7l2-8z" class="a"></path><path d="M86 450l1-3c0 5 0 9 1 14 1 8 4 16 8 24l7 9c1 2 3 5 3 7 0 1 0 1 1 2l-1 2c-1 2-3 3-5 4s-4 0-5-1h-1v1c1 1 2 2 3 2s2 0 4-1l-2 2c-2 0-3 0-5-2 0 0 0-1-1-2h0c-1-2-1-4-1-6l-1-1v-2h0v-7l-1 1h-1v-8l1-1h1c0-2-1-3 0-6l-1-2-3-7v-1h-1l-1-1h0c0-3-1-5-1-8 1-2 1-5 0-6 0-1 0-2 1-3z" class="V"></path><path d="M92 478l3 7-3-1c0-2-1-3 0-6z" class="X"></path><defs><linearGradient id="F" x1="100.185" y1="498.607" x2="101.953" y2="493.905" xlink:href="#B"><stop offset="0" stop-color="#15151a"></stop><stop offset="1" stop-color="#2a2b28"></stop></linearGradient></defs><path fill="url(#F)" d="M98 489l1 2c2 3 6 7 5 11 0 1-1 2-1 2v-1c0-1 0-1 1-1h0l-1-1h-1 0 0c0-3-4-7-6-10l1 1 1-3z"></path><path d="M98 489l1 2v1h0-1-1l1-3z" class="S"></path><path d="M93 502c0-2 1-4 2-5l2 1 1 1 1-1h1c0 1 1 2 1 3h1 0 1l1 1h0c-1 0-1 0-1 1v1l-2 2h-3c-1-1-2-1-2-2 0-2 0-3 1-4v-1h0c-1 1-2 1-2 2-1 1-1 2 0 3 0 1 1 3 3 3 1 1 3 0 4 0 2-2 3-3 4-6 0 1 0 1 1 2l-1 2c-1 2-3 3-5 4s-4 0-5-1h-1v1c1 1 2 2 3 2s2 0 4-1l-2 2c-2 0-3 0-5-2 0 0 0-1-1-2h0c-1-2-1-4-1-6z" class="f"></path><path d="M91 484h1l3 1 3 4-1 3-1-1c2 3 6 7 6 10h0-1c0-1-1-2-1-3h-1l-1 1-1-1-2-1c-1 1-2 3-2 5l-1-1v-2h0v-7l-1 1h-1v-8l1-1z" class="W"></path><path d="M91 484h1l3 1 3 4-1 3-1-1c-2 0-2-4-4-6v3c-1-1-1-2-1-4z" class="P"></path><path d="M90 493v-8l1-1c0 2 0 3 1 4l1 1c0 1 0 3 1 4h0c0 2 3 2 3 4h0v1l-2-1c-1 1-2 3-2 5l-1-1v-2h0v-7l-1 1h-1z" class="V"></path><path d="M269 637h2 5v1l-2 1h1l-4 1c-3 1-6 2-10 3v-1l-2 1-11 6c-2 1-5 1-6 3-1 0-2 0-3 1v-1l1-1h-2 1v-1c-8 4-16 4-24 7-5 1-9 3-14 5-9 3-19 6-28 6-2 0-5 0-6-1 12 0 26-2 37-7 4-1 8-4 11-6 5-3 10-5 15-7 3-2 7-4 10-5l2 1 7-3 3 1h-1v1l6-3 10-1c1 0 1-1 2-1z" class="R"></path><path d="M257 642l1-1c2 0 5-1 6 0-1 0-2 1-3 1l-2 1c-1 0-1 0-2-1z" class="e"></path><path d="M264 641c2 0 4-2 6-2h4 1l-4 1c-3 1-6 2-10 3v-1c1 0 2-1 3-1z" class="L"></path><path d="M243 645h2 2c-6 3-13 7-20 8h0v-1c1-2 4-2 6-3 1 0 2 0 3-1h1c1 0 1-1 2-1h0 1l1-1c1 0 1 0 2-1z" class="h"></path><path d="M267 638h1 0c-3 2-7 2-9 2-1 1-2 2-3 2-2 1-4 1-6 2-1 0-2 1-3 1h-2-2l1-1c1 0 1 0 2-1h2l1-1h2l6-3 10-1z" class="e"></path><path d="M239 650c5-3 12-5 18-8 1 1 1 1 2 1l-11 6c-2 1-5 1-6 3-1 0-2 0-3 1v-1l1-1h-2 1v-1z" class="h"></path><path d="M249 640l3 1h-1v1h-2l-1 1h-2c-1 1-1 1-2 1l-1 1c-1 1-1 1-2 1l-1 1h-1 0c-1 0-1 1-2 1h-1c-1 1-2 1-3 1-2 1-5 1-6 3v1h0c-1 1-5 1-7 1-2 1-4 1-6 2 4-3 10-5 15-7l13-6 7-3z" class="i"></path><path d="M92 404h0c1-3 1-6 2-8 2-6 4-12 6-17h1c0 2-1 3-2 5l-1 4-2 7c-4 14-6 30-7 45 0 5 0 12 1 17 1 9 5 18 9 26 1 3 3 6 5 8h-1 0c-1 1 0 2 0 3l-7-9c-4-8-7-16-8-24-1-5-1-9-1-14l-1 3c-1-1-1-2-1-3 0-2 0-4-1-5v-2l1-8-1-1c0-1 0-2 1-3h1c-1-2-1-3-2-4 0-1 0-3 1-4l1-1v-2c2-2 3-7 3-9h1c0-1 1-2 1-2v-3l1 1z" class="f"></path><path d="M99 483c1 3 3 6 5 8h-1 0c-1 1 0 2 0 3l-7-9c1 0 2 1 3 1 0 2 1 3 2 4h0l1-1h-1v-1l-1-2c-1-1-1-1-1-2v-1z" class="S"></path><path d="M90 408c0-1 1-2 1-2v-3l1 1c-2 9-4 19-4 29-1 5-1 10-1 14l-1 3c-1-1-1-2-1-3 0-2 0-4-1-5v-2l1-8-1-1c0-1 0-2 1-3h1c-1-2-1-3-2-4 0-1 0-3 1-4l1-1v-2c2-2 3-7 3-9h1z" class="T"></path><path d="M85 428l1 1-1 3-1-1c0-1 0-2 1-3z" class="L"></path><path d="M89 408h1v2 1h0l-3 13c0 1 0 4-1 5l-1-1h1c-1-2-1-3-2-4 0-1 0-3 1-4l1-1v-2c2-2 3-7 3-9z" class="D"></path><path d="M127 595c1 4 3 8 3 11 1 3 1 5 1 7-1 1-1 2-2 4v-1-2h-2c1-2 1-5 1-7 0-1 0-1-1-2h0c-1 1 0 1 0 2s-1 3-2 3c-1 4-3 8-6 10-2 2-4 3-5 3-3 2-6 3-8 4h-3-1c6-1 11-4 15-7 1 0 1-1 2-1 2-2 6-8 6-11-1 0-2 3-3 5l-2 2v-1c0-1 1-2 2-3 0-1 1-2 1-3 1-2 2-3 2-5-1 0-2 4-3 5 0 1 0 1-1 2 0 1-1 3-3 4h-1c-1 1 0 1-1 2 0 0-2 1-2 2-2 0-4 1-6 1-8 3-15 3-23 2l-8-1c-2 0-4-1-6-2h-2c-6-4-13-7-18-13l3 2v-2h0l1 1h1c2 1 4 2 6 4l3 3c3 2 7 4 10 5v-1l2 1c1-1 2-1 3-1h0 2c0 1 1 1 1 1h2c3 1 7 0 10 0 1-1 2-1 3-1l7-2 3-1c-1 0-1 0-2-1h0l4-2 3-1 2 1s1 0 1-1c1 0 3-1 3-1 0-1-1-1 1-3h0c1-1 2-2 2-3 1-1 1-3 2-4l1 1v-3l2-2z" class="d"></path><path d="M54 607v-2h0l1 1h1c2 1 4 2 6 4l3 3c3 2 7 4 10 5l1 1h0c-2-1-4-2-6-2-6-2-12-6-16-10z" class="j"></path><path d="M110 616l-7 2c-1 0-1 0-1 1 5 0 9-2 14-4-3 3-11 5-16 5-6 0-12 1-18-1-2 0-4 0-5-1 1-1 2-1 3-1h0 2c0 1 1 1 1 1l2 1c3 0 7 1 10 0 1 0 1 0 1-1 4 0 7-1 11-2h0 1c1-1 1 0 2 0z" class="a"></path><path d="M120 606h0c1-1 2-2 2-3 1-1 1-3 2-4l1 1c-1 3-3 5-4 8-1 1 0 1-1 2 0 1-3 3-4 4l-2 1c-1 0-2 1-4 1-1 0-1-1-2 0h-1 0c-4 1-7 2-11 2 0 1 0 1-1 1-3 1-7 0-10 0l-2-1h2c3 1 7 0 10 0 1-1 2-1 3-1l7-2 3-1c-1 0-1 0-2-1h0l4-2 3-1 2 1s1 0 1-1c1 0 3-1 3-1 0-1-1-1 1-3z" class="F"></path><path d="M113 610l2 1-3 2-2-2 3-1z" class="m"></path><path d="M106 613h0l4-2 2 2-4 1c-1 0-1 0-2-1z" class="a"></path><path d="M96 395l1 1c-1 2-3 7-2 9v-1c1 2 0 4 0 5-1 8-1 15-1 22h1v2l1 9v1l1 2 1 4 2 3c0 3 1 6 2 8v3s1 1 1 2 1 2 2 3c-1 0-2 1-3 1l-1-1v1h1v1l1 1v-1c1 0 2 1 2 2v1h-1l-1-2c0 1 0 3-1 3l-1-3h-1 0c-2-2-2-5-4-7l-1 1-1-2h-2l-1-9v-1h-1v4c-1-5-1-12-1-17 1-15 3-31 7-45z" class="V"></path><path d="M95 405v-1c1 2 0 4 0 5-1 8-1 15-1 22 0 4 0 8-1 11h0 0v-1-6-18c0-4 2-8 2-12z" class="D"></path><path d="M89 440l1 2v-1l1-2c0-3-1-8 0-10h1c1 4-1 8 0 11l1 1v1c-1 4-2 8-2 12v-1h-1v4c-1-5-1-12-1-17z" class="E"></path><path d="M94 431h1v2l1 9v1c0 2 0 6-1 7h-1c0 2 0 3 1 5v1c-1 1-1 2-2 2 1 2 1 4 1 5h-2l-1-9c0-4 1-8 2-12h0 0c1-3 1-7 1-11z" class="g"></path><path d="M94 450l1-1v-4c0-1 0-2 1-3v1c0 2 0 6-1 7h-1z" class="W"></path><path d="M93 442h0c0 4-1 8-1 12 0 1 1 3 1 4 1 2 1 4 1 5h-2l-1-9c0-4 1-8 2-12z" class="M"></path><path d="M96 443l1 2 1 4 2 3c0 3 1 6 2 8v3s1 1 1 2 1 2 2 3c-1 0-2 1-3 1l-1-1v1h1v1l1 1v-1c1 0 2 1 2 2v1h-1l-1-2c0 1 0 3-1 3l-1-3h-1 0c-2-2-2-5-4-7l-1 1-1-2c0-1 0-3-1-5 1 0 1-1 2-2v-1c-1-2-1-3-1-5h1c1-1 1-5 1-7z" class="T"></path><path d="M95 456c0 3 0 5 1 8l-1 1-1-2c0-1 0-3-1-5 1 0 1-1 2-2z" class="J"></path><path d="M97 445l1 4c0 1 1 3 1 4v4c-2-2-2-5-2-7-1-2-1-4 0-5z" class="U"></path><path d="M98 449l2 3c0 3 1 6 2 8v3-1c-2-2-2-4-3-5v-4c0-1-1-3-1-4z" class="H"></path><path d="M220 148c0 1 1 2 2 3 2 2 3 3 4 5v-1l1 1 2 3 2 4c1 3 4 9 4 12-1 1-1 4-2 4l-1 1v5l-1 3v2c-1 0-1-1-1-1 0-1-1-1-1-2-1-1-2-2-2-4h0l-1-3h0c-1 0-1 0-2-1 0 1 0 2-1 3v-1h-2v-1-1h-1l-2 2c0-3 1-5 1-7v-1c2-2 1-7 0-9l-1-8c0-2-1-4-3-6h1v-1h1 1v-1h1v1l1-1z" class="D"></path><path d="M221 162h-1v-4l1 1 1 1c0 1-1 2-1 2z" class="O"></path><path d="M222 160c0-1 0-1 1-2l1 1v1l-1 1c0 1-1 2-1 3-1-1-1-1-1-2 0 0 1-1 1-2z" class="B"></path><path d="M218 149c1 2 2 3 3 5l1 1 1 3c-1 1-1 1-1 2l-1-1c0-4-2-6-4-10h1z" class="G"></path><path d="M222 155h1c1 1 2 2 2 3l2 1-1 1 1 1h-1c0 2 1 5 1 6h-1v-2c0-1-1-3-1-3-1-1-1-2-1-3l-1-1-1-3z" class="c"></path><path d="M225 158l2 1-1 1 1 1h-1c-1-1-1-2-1-3zm-2 13v4c0 1 1 2 1 3v1c0 1 0 2-1 3v-1h-2v-1-1h-1 0c0-2 0-3 1-4s0-2 0-3c1 0 1-1 2-1z" class="L"></path><path d="M221 180c1 0 2-1 2-2h1v1c0 1 0 2-1 3v-1h-2v-1z" class="X"></path><path d="M220 148c0 1 1 2 2 3 2 2 3 3 4 5 0 1 1 2 1 3l-2-1c0-1-1-2-2-3h-1l-1-1c-1-2-2-3-3-5v-1h1v1l1-1z" class="O"></path><path d="M221 154l1-1h1v2h-1l-1-1z" class="L"></path><path d="M224 168l1-1v-2h1v2 8c0 1 1 2 2 3l-1 1h0-1v1h0c-1 0-1 0-2-1v-1c0-1-1-2-1-3v-4l-1-1v-1h2v-1z" class="Q"></path><path d="M225 172c1 2 1 5 1 7v1h0c-1 0-1 0-2-1v-1c0-1-1-2-1-3h1c0-1 1-2 1-3z" class="K"></path><path d="M224 168c1 1 1 3 1 4s-1 2-1 3h-1v-4l-1-1v-1h2v-1z" class="N"></path><path d="M226 155l1 1 2 3 2 4c1 3 4 9 4 12-1 1-1 4-2 4l-1 1v5l-1 3v2c-1 0-1-1-1-1 0-1-1-1-1-2-1-1-2-2-2-4h0l-1-3v-1h1 0l1-1c-1-1-2-2-2-3v-8h1c0-1-1-4-1-6h1l-1-1 1-1c0-1-1-2-1-3v-1z" class="D"></path><path d="M229 177s-1-1-1-2v-1-1h1v2c1 0 1 1 1 1l-1 1z" class="Q"></path><path d="M228 178c0 2 0 4 1 5v2h1 1c-1 1-1 1-1 3h1v2c-1 0-1-1-1-1 0-1-1-1-1-2-1-1-2-2-2-4h0l-1-3v-1h1 0l1-1z" class="B"></path><path d="M230 176v6h1l1-9h1v6l-1 1v5l-1 3h-1c0-2 0-2 1-3h-1v-2h0 0c-1-2-1-2-1-4 0-1 1-1 0-2l1-1z" class="C"></path><path d="M230 183h1 1l-1 2h-1v-2z" class="H"></path><path d="M232 183h0v-3 5l-1 3h-1c0-2 0-2 1-3l1-2z" class="D"></path><path d="M226 155l1 1 2 3 2 4c1 3 4 9 4 12-1 1-1 4-2 4v-6-1c0-1 0-2-1-2v2 1h-1v-4l-2-1c-1 0-1-1-1-2l-1-2v-3l-1-1 1-1c0-1-1-2-1-3v-1z" class="X"></path><path d="M228 166l1-2 2 5-2-1c-1 0-1-1-1-2z" class="K"></path><path d="M226 155l1 1 2 3c0 1 0 1-1 2 1 1 1 2 1 3l-1 2-1-2v-3l-1-1 1-1c0-1-1-2-1-3v-1z" class="c"></path><path d="M228 161c1 1 1 2 1 3l-1 2-1-2h1v-3z" class="O"></path><path d="M357 455c1-1 1-2 1-3l2-7c1-3 1-8 0-11 0-4 0-9-1-13-2-6-5-13-9-19h0l1 1c2 2 3 4 4 6 3 5 6 8 7 14 1 1 2 2 2 3h1c1 6 2 12 0 18v1c-3 7-3 15-5 23-1 2-1 4-2 7-1 4-3 7-5 11l-2 2c-1 2-3 4-5 5h-1l1-1v-1h0c0-1-1-2-1-3l3-5c1-1 2-3 2-5l3-6c0-2 0-3 1-4s1-5 2-7l1-6z" class="U"></path><path d="M357 455h1c0 2-1 3-1 5s0 4-1 6c-1 5-2 9-4 14-1 1-1 1 0 2-1 2-4 7-6 9 0-1-1-2-1-3l3-5c1-1 2-3 2-5l3-6c0-2 0-3 1-4s1-5 2-7l1-6z" class="D"></path><path d="M362 423c1 1 2 2 2 3h1c1 6 2 12 0 18v1c-3 7-3 15-5 23-1 2-1 4-2 7-1 4-3 7-5 11l-2 2c-1 2-3 4-5 5h-1l1-1v-1h0c2-2 5-7 6-9 1-3 3-6 4-9 1-4 1-9 2-13l5-17v-11c-1 0-1-1-1-2v-2-3-2z" class="l"></path><path d="M362 423c1 1 2 2 2 3h1c1 6 2 12 0 18-3 6-3 13-5 19-1 3-2 7-3 10v1h-1c0-2 1-5 2-7l1-8 3-10c1-3 2-6 2-9 0-4-1-8-2-12v-3-2z" class="a"></path><path d="M362 423c1 1 2 2 2 3v1h0c-1-1-2-1-2-2v-2z" class="e"></path><path d="M354 508h3l-4 3v2s1 1 0 2l1 1v1c-1 1-3 2-4 2l-1 1h1 0 2l-1 1-4 2c-1 1-3 1-4 2v1h1c0 1-1 1-1 2h0l2-1v2h0v1c-3 3-6 6-9 10-1 1-1 1-1 3-1 1-4 5-4 6-1 2-2 3-2 4h-1v2c-1 1-1 2-1 2-1 0-1 0-1 1l-2-6c-1-1-1-2-1-3-1-1-1-2-1-2l1-1c0-5 3-10 6-15 2-3 5-7 9-10 1 0 2-1 3-2h0c1-2 3-3 4-4s2-1 3-2 3-2 4-3l2-2z" class="E"></path><path d="M346 520h0c1 0 2-1 3-1h1l-1 1c-2 1-5 3-7 4-1 1-2 3-4 3 0 0 1-1 1-2 2-2 5-4 7-5z" class="G"></path><path d="M325 549l1 1c2-5 4-11 8-15 1-1 2-1 3-2-3 3-4 5-6 8h-1l1 1h0c-1 2-3 4-3 6l-2 1c0 2 1 4 2 6-1 1-1 2-1 2-1 0-1 0-1 1l-2-6c-1-1-1-2-1-3-1-1-1-2-1-2l1-1c1 1 1 2 1 3v1c1 0 1 0 1-1z" class="b"></path><path d="M343 528l2-1v2h0v1c-3 3-6 6-9 10-1 1-1 1-1 3-1 1-4 5-4 6-1 2-2 3-2 4h-1v2c-1-2-2-4-2-6l2-1c0-2 2-4 3-6h0c2-3 5-7 8-9v-1c2-1 3-2 4-4z" class="C"></path><path d="M331 542c2-3 5-7 8-9h0c-1 2-3 3-4 5-2 2-4 4-5 7s-1 5-2 8v2c-1-2-2-4-2-6l2-1c0-2 2-4 3-6h0z" class="X"></path><path d="M354 508h3l-4 3v2s1 1 0 2l1 1v1c-1 1-3 2-4 2h-1c-1 0-2 1-3 1h0c-2 1-5 3-7 5 0 1-1 2-1 2l-4 4h0c0 2-1 3-2 4-2 3-4 6-5 9-1 1-1 4-2 5 0 1 0 1-1 1v-1c0-1 0-2-1-3 0-5 3-10 6-15 2-3 5-7 9-10 1 0 2-1 3-2h0c1-2 3-3 4-4s2-1 3-2 3-2 4-3l2-2z" class="O"></path><path d="M329 531v1c-2 3-3 5-4 8 3-2 4-6 6-7h0l-2 3c-1 2-2 3-2 5h0l2-2s0-1 1-2c0 0 1-2 2-2h0c-2 3-4 6-5 9-1 1-1 4-2 5 0 1 0 1-1 1v-1c0-1 0-2-1-3 0-5 3-10 6-15z" class="c"></path><path d="M354 508h3l-4 3v2s1 1 0 2l1 1v1c-1 1-3 2-4 2h-1c-1 0-2 1-3 1h0c2-2 4-3 6-4h-1s-2 0-2 1c-5 2-8 5-12 9l-1-1 7-6c1-1 1-3 2-3 2-1 3-2 5-3 1 0 1-1 2-2v-1l2-2z" class="C"></path><path d="M354 508h3l-4 3c0 1 0 0-1 1 0 1-1 2-2 2v-1c1 0 1-1 2-2v-1l2-2z" class="Q"></path><path d="M345 539c1 0 2-2 3-2 2-1 3-2 5-3h-1c0 1-1 2-1 2l-1 1h0 4c4-1 7-1 11 0h0c1 1 2 1 3 1 1 1 2 1 3 2 1 0 1 0 1 1 1 0 2 1 3 2v1l-1-1c-1-1-2-1-3-2-2-1-8-1-10-1 0 1-1 1-1 2 3 0 5 1 7 3h0c-2 0-4-1-6-1h0l-4-1h-1c-1 0-1 0-1 1-2 1-2 1-4 1-3 1-6 2-8 5 2-1 4-2 6-2h4c-4 1-10 2-13 6 0 1 1 0 2 0-5 4-7 8-7 15 0 2 1 5 1 7v2h0-1c-2-3-5-5-6-8l-3-6c-1-3-3-5-4-8v-1-2c1 0 1 0 2-1l2 6c0-1 0-1 1-1 0 0 0-1 1-2v-2h1c0-1 1-2 2-4 0 1 0 2-1 3h1v1l1 1c1 0 1-2 1-2 3-3 4-6 6-8 1-2 4-5 6-5z" class="b"></path><path d="M322 555v-2c1 0 1 0 2-1l2 6 1 5h0c-2-2-3-5-5-8z" class="f"></path><path d="M332 555v1h0c2-2 2-4 4-5 1-2 3-4 5-5 1-1 2-3 4-4v1c-3 2-5 5-8 8h1c-1 1-1 1-1 2-2 2-3 4-3 6v2c-1 1-1 2-1 2v1-3c0-1 0-1-1-1v-1 2 3c0-2-2-7 0-8v-1z" class="K"></path><path d="M331 549c0 1 0 2-1 3h1v1c0 2-1 4-2 6h0c0 3 2 5 1 7l-1-2h0l-1 1v-1l-1-1-1-5c0-1 0-1 1-1 0 0 0-1 1-2v-2h1c0-1 1-2 2-4z" class="l"></path><path d="M330 552h1v1c0 2-1 4-2 6 0-2 0-3 1-5v-2z" class="Z"></path><path d="M328 553h1c-1 5-1 7 0 11l-1 1v-1l-1-1-1-5c0-1 0-1 1-1 0 0 0-1 1-2v-2z" class="O"></path><path d="M357 540v1l3 1c3 0 5 1 7 3h0c-2 0-4-1-6-1h0l-4-1h-1c-1 0-1 0-1 1-3 0-5 0-7 1s-5 3-7 5c2-3 5-5 9-7h1 1s1 0 1-1c1 0 2 0 2-1h1l1-1h0z" class="j"></path><path d="M345 539c1 0 2-2 3-2 2-1 3-2 5-3h-1c0 1-1 2-1 2l-1 1h0 4c4-1 7-1 11 0h0c1 1 2 1 3 1 1 1 2 1 3 2 1 0 1 0 1 1 1 0 2 1 3 2v1l-1-1c-1-1-2-1-3-2-2-1-8-1-10-1 0 1-1 1-1 2l-3-1v-1h0l-1 1h-1c0 1-1 1-2 1 0 1-1 1-1 1h-1-1c-5 1-9 5-12 8h-1c3-3 5-6 8-8v-1c-2 1-3 3-4 4-2 1-4 3-5 5-2 1-2 3-4 5h0v-1-1c1 0 1-2 1-2 3-3 4-6 6-8 1-2 4-5 6-5z" class="a"></path><path d="M357 540h2 2c0 1-1 1-1 2l-3-1v-1z" class="h"></path><path d="M345 539c1 0 2-2 3-2 2-1 3-2 5-3h-1c0 1-1 2-1 2l-1 1h0 4c4-1 7-1 11 0h-1c-2 1-5 0-8 0-4 1-8 2-11 5l9-3v1c-3 1-6 2-8 3h-1v-1c-2 1-3 3-4 4-2 1-4 3-5 5-2 1-2 3-4 5h0v-1-1c1 0 1-2 1-2 3-3 4-6 6-8 1-2 4-5 6-5z" class="F"></path><path d="M345 539c1 0 2-2 3-2 2-1 3-2 5-3h-1c0 1-1 2-1 2l-1 1h0 4l-8 3h-1v-1z" class="a"></path><path d="M64 544c0-3 0-6 1-8 0-2 1-4 2-6 0-2 1-3 2-5 0-1 0-1 1-2h0c0 2-1 4-2 5v2c-1 1-1 2 0 2 0 2 0 2-1 3h0c0 5-1 10 1 15 0 4 2 7 3 11 0-1 1-2 0-3v-1c-1-1-1-2-1-3h0s0-1-1-1v-1-1c-1 0-1-2-1-3h0v-1c2 1 1 3 2 4v2l1 3h1 1c-3-5-3-10-3-15h1c0 5 1 9 3 14l3 6c3 2 7 4 11 4 2 0 2-1 3-2l1-1c0-1 2-2 2-4 0-1 1-1 1-2h1v-1l1 1v2c-1 1-1 2-1 3l1 1c0-2 0-4 1-5l1 4v-1h0 1v1c0 1 1 1 1 2 1 1 1 1 1 2v2l5 9h-3c0-1 1-1 0-2-1 1-1 2-2 2-3 1-7 1-10 1h0-2-3c-2-1-2-2-5-2-1 0-1-2-2-2-1-1-3-2-4-3l-1-1c-2-2-4-4-5-7-1-2-2-3-2-5-1 0-1-1-1-1v-1c-1-2-2-4-2-6 0-1-1-1-1-2h0c-1 0-1-1-1-2h1v-1z" class="b"></path><path d="M76 569c1 1 2 2 4 3s3 2 6 2c2 0 9-1 10-3 1 0 1-1 1-1h1c0 1-1 2-2 3-2 2-7 0-9 2 1 1 1 1 2 1 0 0 1 0 1 1h-3c-2-1-2-2-5-2-1 0-1-2-2-2-1-1-3-2-4-3l-1-1h1z" class="W"></path><path d="M64 544c0-3 0-6 1-8 0-2 1-4 2-6 0-2 1-3 2-5 0-1 0-1 1-2h0c0 2-1 4-2 5v2c-1 1-1 2 0 2 0 2 0 2-1 3h0 0c-1 4-1 8-1 12 0 6 4 14 8 19l2 3h-1c-2-2-4-4-5-7-1-2-2-3-2-5-1 0-1-1-1-1v-1c-1-2-2-4-2-6 0-1-1-1-1-2h0c-1 0-1-1-1-2h1v-1z" class="e"></path><path d="M99 561v-1h0 1v1c0 1 1 1 1 2 1 1 1 1 1 2v2l5 9h-3c0-1 1-1 0-2-1 1-1 2-2 2-3 1-7 1-10 1h0-2c0-1-1-1-1-1-1 0-1 0-2-1 2-2 7 0 9-2 1-1 2-2 2-3h-1 0c1-1 1-2 1-3v-3l1-3z" class="O"></path><path d="M97 574c1-1 1-2 2-2v2c0 1-1 2-2 2v-2z" class="B"></path><path d="M99 561v-1h0 1v1 5l-1 1c-1-2-1-2-1-3l1-3z" class="Q"></path><path d="M97 574v2l-5 1h-2c0-1-1-1-1-1 1-1 3-1 5-1 1 0 2 0 3-1z" class="T"></path><path d="M71 561c0-1 1-2 0-3v-1c-1-1-1-2-1-3h0s0-1-1-1v-1-1c-1 0-1-2-1-3h0v-1c2 1 1 3 2 4v2l1 3h1 1c0 1 1 3 2 4 2 2 7 5 9 6h5c1 0 3-1 3-2l1-1h0c0 2-2 3-3 4s-2 1-3 1-5-2-5-2c-1-1-2-1-3-2-1 0-3-1-4-2 2 2 7 5 10 6 2 1 5 2 7 1 1-1 2-3 2-4 1-2 1-4 1-7h1c0 3 0 8-2 10s-3 2-5 2-3 0-4-1c-2 0-4-1-5-2h-1l1 1c1 1 4 1 5 2 1 0 1 1 2 1h6l1-1h1c-2 2-4 2-6 2-3 0-3 0-6-1l-1-1h0c1 1 2 2 3 2v1c-2 0-3-1-5-2l-1-1c-2-1-3-2-4-3-1-2-3-4-4-6z" class="m"></path><path d="M120 349c1 0 1 0 1-1 0 1 0 2-1 3v3l-1 2v1c0 1 0 2-1 4h0c-1 3-2 6-4 8-1 3-3 5-4 7-1 1-2 6-3 7l-1 1c-1 2-1 4-2 6-3 9-5 18-6 27v9h0c0 5 0 11 1 16v2c1 1 1 3 1 4l1 1v1 1-1l-1 1v1h0l-2-3-1-4-1-2v-1l-1-9v-2h-1c0-7 0-14 1-22 0-1 1-3 0-5v1c-1-2 1-7 2-9l-1-1 2-7 1-4c1-2 2-3 2-5l1-1c0-2-1-2-1-3l-1-1-1-1c0-2 2-4 3-6l1 1c1 0 1-1 2-1h0l1-1c1-1 2-1 2-3 0 0 0-1 1-2l2-2c0-1 1-1 2-2h1c2-2 3-6 5-8h1z" class="f"></path><path d="M95 409l1 1c-1 8-1 15-1 23v-2h-1c0-7 0-14 1-22z" class="E"></path><defs><linearGradient id="G" x1="94.877" y1="393.481" x2="100.411" y2="402.417" xlink:href="#B"><stop offset="0" stop-color="#62635e"></stop><stop offset="1" stop-color="#78757e"></stop></linearGradient></defs><path fill="url(#G)" d="M98 388l1 1 1 2-4 19-1-1c0-1 1-3 0-5v1c-1-2 1-7 2-9l-1-1 2-7z"></path><path d="M98 388l1 1c0 2 0 5-2 7l-1-1 2-7z" class="B"></path><path d="M113 357h1c2-2 3-6 5-8h1c-4 7-10 14-12 22l-1-1c0-2 0-2-1-4 1-1 2-1 2-3 0 0 0-1 1-2l2-2c0-1 1-1 2-2z" class="b"></path><path d="M108 377v-1l-1 3v-1-2c1-3 2-5 4-7 1-3 1-4 3-6 1-2 2-3 3-4 0-1 1-2 2-3v1c0 1 0 2-1 4h0c-1 3-2 6-4 8-1 3-3 5-4 7-1 1-2 6-3 7-1 0-1-1 0-1h0v-1l1-2h0c0-1 1-2 1-2h-1 0z" class="X"></path><path d="M119 357c0 1 0 2-1 4h0c-1 3-2 6-4 8-1 3-3 5-4 7-1 1-2 6-3 7-1 0-1-1 0-1h0v-1l1-2h0c0-1 1-2 1-2h-1 0c1-2 2-5 3-7 0-1 1-2 2-3h0c-1 2-1 3-2 4l1 1c0-1 1-1 1-2l3-7c1-2 2-5 3-6z" class="P"></path><path d="M106 366c1 2 1 2 1 4l1 1c-1 2-2 7-4 7l-4 13-1-2-1-1 1-4c1-2 2-3 2-5l1-1c0-2-1-2-1-3l-1-1-1-1c0-2 2-4 3-6l1 1c1 0 1-1 2-1h0l1-1z" class="D"></path><path d="M106 366c1 2 1 2 1 4h-1c0 1-1 3-2 4v1c-1 0-1 0-1 1h-1 0c0-1 0-1 1-2-1-2 0-3 0-4 1-1 2-2 2-3h0l1-1z" class="O"></path><path d="M104 374v-1c0-2 0-2 1-3h1c0 1-1 3-2 4z" class="Z"></path><path d="M106 370h1l1 1c-1 2-2 7-4 7h0c-1 1-1 1-1 2l-1 1c0 1-1 3-2 3 0-2 3-7 4-9v-1c1-1 2-3 2-4z" class="S"></path><path d="M345 404c-5-12-13-21-24-28h1c7 5 15 11 20 19 5 7 8 17 11 26 0 2 1 4 2 6 1 6 1 11 2 17 0 5 0 11-2 16v2c0 2-1 4-1 6-1 1-1 2-1 4l-3 6c0 2-1 4-2 5l-3 5c0 1 1 2 1 3h0v1l-1 1h1l-2 2v1c-3 2-7 5-9 7h0c-1 1-2 1-3 1h0-1l4-6c1-2 2-3 3-4 4-4 6-12 7-17l1 1c0-2 1-3 1-5 1-4 3-9 2-13v-11c0-5 1-12 0-18h0 1 0v-3c0-5-1-10-3-15l-3-5 1-2-1-1 1-1z" class="O"></path><path d="M353 429l1-1c1 4 1 9 1 13 1 4 1 9 1 13h-1l-1-4v-4c0-5 0-12-1-17z" class="e"></path><path d="M354 450l1 4h1c-1 3-2 6-2 9-1 4-2 7-3 11-2 5-5 9-7 14h0c1-2 2-5 3-7s3-5 3-8c1-2 2-5 2-7l2-16z" class="a"></path><path d="M345 404c3 5 6 11 7 17l-2 1-3-10v1l-3-5 1-2-1-1 1-1z" class="e"></path><path d="M345 406c1 2 2 3 2 6h0v1l-3-5 1-2z" class="V"></path><path d="M349 460c1 1 1 2 1 4h1c0 1 0 1 1 2h0 0c0 2-1 5-2 7 0 3-2 6-3 8s-2 5-3 7l-3 3h0c2-3 3-6 4-9h0c0-2 0-3 1-4 0-2 1-3 1-5 1-4 3-9 2-13z" class="b"></path><path d="M347 481c-1-1-1-2-1-3 1 0 1-1 1-2 1-2 2-4 2-6h1v3h0c0 3-2 6-3 8z" class="O"></path><path d="M338 494c4-4 6-12 7-17l1 1c-1 1-1 2-1 4h0c-1 3-2 6-4 9h0l3-3h0v2c0 2-1 2-2 4h1 0l3-3v1l-1 1h1l-2 2v1c-3 2-7 5-9 7h0c-1 1-2 1-3 1h0-1l4-6c1-2 2-3 3-4z" class="D"></path><path d="M332 504c0-1 1-2 2-3v-1c1-1 2-1 3-2 0 2-1 3-2 5-1 1-2 1-3 1z" class="O"></path><path d="M344 490c0 2-1 2-2 4h1 0l3-3v1l-1 1h1l-2 2v1c-3 2-7 5-9 7h0c1-2 2-3 2-5h0c2-3 5-5 7-8z" class="S"></path><path d="M347 413v-1l3 10 2-1 1 4c1 1 1 2 1 3l-1 1c1 5 1 12 1 17v4l-2 16h0 0c-1-1-1-1-1-2h-1c0-2 0-3-1-4v-11c0-5 1-12 0-18h0 1 0v-3c0-5-1-10-3-15z" class="f"></path><path d="M352 421l1 4c1 1 1 2 1 3l-1 1-1-3c0 2 1 5 1 7 1 6 1 12 0 18 0 2 0 5-1 7l-2-36 2-1z" class="a"></path><path d="M350 428c1 7 2 14 1 21 0 5 1 10 0 15h-1c0-2 0-3-1-4v-11c0-5 1-12 0-18h0 1 0v-3z" class="g"></path><path d="M350 428c1 7 2 14 1 21 0 1 0 1-1 2 0-5 1-11 0-16v-4h0v-3z" class="W"></path><path d="M120 341c1-1 1-1 2-1l1 1c-1 1-2 4-3 5s-2 2-2 3l-5 8c-1 1-2 1-2 2l-2 2c-1 1-1 2-1 2 0 2-1 2-2 3l-1 1h0c-1 0-1 1-2 1l-1-1c-1 2-3 4-3 6l1 1 1 1c0 1 1 1 1 3l-1 1h-1c-2 5-4 11-6 17-1 2-1 5-2 8h0l-1-1v3s-1 1-1 2h-1c0 2-1 7-3 9v2l-1 1v-3-1h-2l-2 7-1-1c0-3 2-8 3-11 0-3-1-5-1-8 0 2-1 6-2 6h-1c0-1 1-2 1-4 2-7 5-14 8-22l3-7c1-1 2-2 2-4l-1 1s-1 2-1 3c0 0 0 1-1 2l-2 4h-1c1-2 3-4 3-6v-1c1-3 3-4 4-6 3-3 5-7 8-9h1l4-4c4-3 6-6 9-10l3-3 1-2z" class="C"></path><path d="M91 386c0 3-2 5-3 7-1 3-3 6-3 9l-2 9c0-3-1-5-1-8l1-2h0c0-1 0-3 1-3v2h1c0-2 1-5 2-7s2-5 4-7z" class="Z"></path><g class="G"><path d="M83 401h0c0-1 0-3 1-3v2h1c0 1-1 3-1 4h-1v-3z"></path><path d="M91 386l1-4h0c-1 0-1 0-1 1h-1 0c0-1 1-4 2-5 0-1 0-1 1-2v-1l1-2h1 0c0 1 0 1-1 1v1 1 2 1l2-3c0-1 0-2 1-2l1 2c0 1 1 1 1 2l-1 2c-1 2-3 3-3 5h-1c0-2 1-4 2-5h0v-1c-1 1-2 3-3 4v2c0 1-1 2-2 3 0 2-1 3-2 4l-3 10h-1c0-3 2-6 3-9 1-2 3-4 3-7z"></path></g><path d="M97 374l1 2h-1v1l1 1h0-1l-1-1v-1h0c0-1 0-2 1-2z" class="D"></path><path d="M120 341c1-1 1-1 2-1l1 1c-1 1-2 4-3 5s-2 2-2 3l-5 8c-1 1-2 1-2 2l-2 2c-1 1-1 2-1 2 0 2-1 2-2 3l-1 1h0c-1 0-1 1-2 1l-1-1c-1 2-3 4-3 6h-1l1-2-1-1c0 1-1 3-1 4-1 0-1 1-1 2l-2 3v-1-2c1-2 2-4 3-5 3-5 7-10 11-14 1-1 3-2 4-4 2-2 9-10 8-12z" class="c"></path><path d="M102 367c2-2 3-4 5-6s4-4 6-7l6-9 1 1c-1 1-2 2-2 3l-5 8c-1 1-2 1-2 2l-2 2c-1 1-1 2-1 2 0 2-1 2-2 3l-1 1h0c-1 0-1 1-2 1l-1-1zm-5 7c0-1 1-3 1-4l1 1-1 2h1l1 1 1 1c0 1 1 1 1 3l-1 1h-1c-2 5-4 11-6 17-1 2-1 5-2 8h0l-1-1v3s-1 1-1 2h-1c0 2-1 7-3 9v2l-1 1v-3-1h-2l-2 7-1-1c0-3 2-8 3-11l2-9h1l3-10c1-1 2-2 2-4 1-1 2-2 2-3v-2c1-1 2-3 3-4v1h0c-1 1-2 3-2 5h1c0-2 2-3 3-5l1-2c0-1-1-1-1-2l-1-2z" class="K"></path><path d="M90 393v-1c1-2 2-5 4-7v2c-1 3-3 6-4 9l-2 7-1 4h1v-1c1-2 2-7 3-8h0l-2 10c0 2-1 7-3 9v2l-1 1v-3-1h-2l-2 7-1-1c0-3 2-8 3-11l2-9h1l3-10 1 1z" class="B"></path><path d="M89 392l1 1c-3 5-4 13-4 19 0 1-1 5-1 5v-1h-2l-2 7-1-1c0-3 2-8 3-11l2-9h1l3-10z" class="Y"></path><path d="M85 402h1l-3 14-2 7-1-1c0-3 2-8 3-11l2-9z" class="e"></path><path d="M45 545h-1c0-2 0-6 1-7 0 1 0 1 1 2h0v5l1 1h-1c0 1 1 2 1 3v1l1-1h1c0-1-1-2-1-2v-1-1-4l-1-1c0-1 0-3 1-4v-3h0 0c-1 1-1 2-1 2h0c0-3 0-6 2-8 0 0 1 0 1 1-1 6-2 11-1 17 0 1 1 4 1 5h-1c0 1-1 3 0 4h0l5 19 1-1c6 12 16 20 29 25 2 0 5 1 7 1h10c1 0 1 0 1-1v-1c3 0 7-1 9-3h1v1c-2 1-4 2-6 2v1h2c1-1 5-2 5-4l1-1c0-1 1-2 1-4h1v4c-1 2-2 3-4 4-1 1-2 1-3 2 1 0 1 0 2-1 1 0 3-2 4-2 0 0-1 2-2 2-1 2-3 2-4 4h-1v1c1 0 2-1 3-2 0 1-1 1 0 2-1 2-3 2-4 3h-2c-1 0-2 2-4 2-2 1-12 1-13 1 0-1 1-1 2-1h-6-2c-4 0-7-3-10-4-3-2-7-4-9-7h0c1 0 2 1 2 1h1c-2-1-3-2-5-3-4-4-6-7-9-12-1-2-3-5-4-8-2-4-3-9-3-13 0-1 1-2 0-3v-4h0c1 2 1 3 1 4l1-1c-1-4-2-7-2-12z" class="C"></path><path d="M98 606c1 0 2-1 3-1h2 0c-4 3-9 2-13 2v-1h-2 10zm-27-11c3 1 6 3 10 4 2 0 5 1 8 1 4 1 8 0 12 0v1c-10 1-22 1-31-6h1z" class="a"></path><path d="M45 545h-1c0-2 0-6 1-7 0 1 0 1 1 2h0v5l1 1h-1c0 1 1 2 1 3v1l1-1h1c0-1-1-2-1-2v-1-1-4l-1-1c0-1 0-3 1-4v-3h0 0c-1 1-1 2-1 2h0c0-3 0-6 2-8 0 0 1 0 1 1-1 6-2 11-1 17 0 1 1 4 1 5h-1c0 1-1 3 0 4h0c-1 2 0 4 1 6 0 3 1 6 2 9 1 5 2 8 4 12 4 5 9 10 14 14h1-1l-3-1c-8-5-14-14-16-23l-4-18c-1-2-1-6-2-8z" class="h"></path><path d="M55 572c6 12 16 20 29 25 2 0 5 1 7 1 3 1 7 0 9 0-8 3-20 1-28-3-8-5-16-13-18-22l1-1z" class="j"></path><path d="M84 607h-2c-4 0-7-3-10-4-3-2-7-4-9-7h0c1 0 2 1 2 1h1 0l2 2c1 0 2 1 3 1 1 1 1 1 2 1l1-1 1 1h1v-1c5 1 10 3 14 3 3 0 5 0 7-1h5v1 1h0-2c0 1-1 1-1 1h-1v1H88h2v1h-6z" class="i"></path><path d="M88 606l-3-1c-2 0-3 0-5-1-1 0-3-1-4-2h0 1c3 1 6 1 9 2h5v-1h-1c3 0 5 0 7-1h5v1 1h0-2c0 1-1 1-1 1h-1v1H88z" class="J"></path><path d="M97 602h3v1c-1 0-3 0-5 1s-7 1-9 0h5v-1h-1c3 0 5 0 7-1z" class="e"></path><path d="M45 561c0-1 1-2 0-3v-4h0c1 2 1 3 1 4l1-1c1 6 2 13 4 19 4 9 13 17 21 22l4 2v1h-1l-1-1-1 1c-1 0-1 0-2-1-1 0-2-1-3-1l-2-2h0c-2-1-3-2-5-3-4-4-6-7-9-12-1-2-3-5-4-8-2-4-3-9-3-13z" class="h"></path><path d="M370 477c2-5 3-10 5-14-1 5-3 10-4 15-2 3-2 7-4 10s-3 7-5 9l-2 3-12 12v1c-1 1-2 1-3 2s-3 2-4 4h0c-1 1-2 2-3 2-4 3-7 7-9 10-3 5-6 10-6 15l-1 1v-4l-2 2c-1-3-1-7-1-11h-1c-1-1-1-5-1-7 2-3 3-5 4-8 1 0 3-4 4-5 2-3 5-6 7-10h0c1 0 2 0 3-1h0c2-2 6-5 9-7 0 1-1 1-1 2-1 0-1 1-2 1l-1 1 1 1 3-2v1c-1 2-3 2-3 4 2-1 3-3 4-4v1h2v1c2-2 3-2 3-4 0-1 4-5 5-6 3-3 5-5 7-9 1 0 1-1 2-2h0v-1c1-1 2-1 2-2v-1h1l1-2h0v3 1c1-1 1-2 2-2z" class="c"></path><path d="M331 512c0 1-1 1 0 3-1 0-2 2-3 2l-2 2c-3 5-6 10-6 16h-1c0-5 2-10 4-14l6-7 2-2z" class="J"></path><path d="M332 504c1 0 2 0 3-1h0c-1 2-2 3-3 5-1 1-3 3-3 4v2l-6 7v-1c0-2 1-3 2-5v-1c2-3 5-6 7-10h0z" class="b"></path><path d="M344 496c0 1-1 1-1 2-1 0-1 1-2 1l-1 1 1 1 3-2v1c-1 2-3 2-3 4 2-1 3-3 4-4v1c-1 2-6 6-6 7-2 3-4 5-7 7-1 0-2 1-2 2-2 2-3 3-4 5-2 3-2 6-4 9h0c0-2 2-7 3-9s2-3 3-5c1 0 2-2 3-2-1-2 0-2 0-3l-2 2v-2c0-1 2-3 3-4 1-2 2-3 3-5 2-2 6-5 9-7z" class="E"></path><path d="M344 496c0 1-1 1-1 2-1 0-1 1-2 1l-1 1 1 1-10 11-2 2v-2c0-1 2-3 3-4 1-2 2-3 3-5 2-2 6-5 9-7z" class="K"></path><path d="M355 492c3-3 5-5 7-9 1 0 1-1 2-2h0v-1c1-1 2-1 2-2v-1h1c-1 4-2 7-4 11l-5 5-8 8-2 2-2 1-1 1s-1 1-2 1c-1 1-4 4-4 5v2h0c-2 1-2 2-4 3s-4 3-5 5c-1 1-2 3-2 4h0c0 1-1 3-2 4-2 2-3 6-5 9 0-2 0-4 1-5 2-4 3-9 6-12 0-1 1-1 1-2 0 0 0-1 1-1h0v-1c0-1 1-2 2-2 3-2 5-4 7-7 0-1 5-5 6-7h2v1c2-2 3-2 3-4 0-1 4-5 5-6z" class="C"></path><path d="M345 501h2v1l-8 6c0-1 5-5 6-7z" class="I"></path><path d="M370 477c2-5 3-10 5-14-1 5-3 10-4 15-2 3-2 7-4 10s-3 7-5 9l-2 3-12 12v1c-1 1-2 1-3 2s-3 2-4 4h0c-1 1-2 2-3 2-4 3-7 7-9 10-3 5-6 10-6 15l-1 1v-4h-1c-1-3 1-5 2-7 1-3 2-5 4-7 1-1 2-3 3-4h-2 0c0-1 1-3 2-4 1-2 3-4 5-5s2-2 4-3h0v-2c0-1 3-4 4-5 1 0 2-1 2-1l1-1 2-1 2-2 8-8 5-5c2-4 3-7 4-11l1-2h0v3 1c1-1 1-2 2-2z" class="G"></path><path d="M360 494c1-1 2-1 3-2h0c-1 2-2 4-4 6v-1c0-1 1-1 1-2h0v-1z" class="U"></path><path d="M368 475h0v3 1c1-1 1-2 2-2-2 5-3 10-7 15h0c-1 1-2 1-3 2 0 0-1-1-2-1l5-5c2-4 3-7 4-11l1-2z" class="i"></path><path d="M363 488h0c2-1 2-3 4-5h0v1c-1 3-3 5-4 8-1 1-2 1-3 2 0 0-1-1-2-1l5-5z" class="T"></path><path d="M328 525h0c0-1 1-3 2-4 1-2 3-4 5-5 0 1-1 2-2 4h0 1v1c-1 1-1 2-2 3h0c-1 2-3 3-4 5-3 4-5 8-6 12h0v1c1-1 2-5 3-6 0-1 1-2 1-3 2-4 10-13 14-14 0-1 0 0 1 0-1 1-2 2-3 2-4 3-7 7-9 10-3 5-6 10-6 15l-1 1v-4h-1c-1-3 1-5 2-7 1-3 2-5 4-7 1-1 2-3 3-4h-2z" class="J"></path><defs><linearGradient id="H" x1="345.026" y1="502.614" x2="353.89" y2="506.801" xlink:href="#B"><stop offset="0" stop-color="#8d8c8c"></stop><stop offset="1" stop-color="#afadae"></stop></linearGradient></defs><path fill="url(#H)" d="M358 493c1 0 2 1 2 1v1h0c0 1-1 1-1 2v1c-3 5-8 8-12 12l-12 11-3 3c1-1 1-2 2-3v-1h-1 0c1-2 2-3 2-4 2-1 2-2 4-3h0v-2c0-1 3-4 4-5 1 0 2-1 2-1l1-1 2-1 2-2 8-8z"></path><path d="M348 503c0 1 0 2-1 3l-1-2 2-1z" class="I"></path><path d="M346 504l1 2-2 1v-2l1-1z" class="F"></path><path d="M345 505v2l-2 1c-2 1-2 4-4 5v-2c0-1 3-4 4-5 1 0 2-1 2-1z" class="E"></path><path d="M358 493c1 0 2 1 2 1v1c-1 0-2 1-3 1-1 2-5 6-6 6v-1h0-1l8-8z" class="I"></path><path d="M339 513v1l1-1 1 1 1-2v1c-1 1-2 1-3 2 0 2-4 4-4 5v1l-3 3c1-1 1-2 2-3v-1h-1 0c1-2 2-3 2-4 2-1 2-2 4-3z" class="F"></path><path d="M192 131c0-4 0-9 1-13 0-2 0-3 1-4s1-1 2-1c2 1 3 2 5 3l-2 1c0 1 0 2 1 3 0 3 1 5 3 8 1 3 2 7 4 10 2 4 6 8 8 12 2 2 3 4 3 6l1 8c1 2 2 7 0 9v1c0 2-1 4-1 7l2-2h1v1 1h2v1h0c-1 2-2 7-5 8h0l-1-1v1l-1 1h0c0 1-1 1-2 3h1 3c-1 1-1 2-1 2h-1c-1 0-1 1-2 1h-1c-1 0-3 0-4 1-1 0-1 1-2 1-2 1-3 1-4 1v-1c1 0 1 0 1-1h-1-1v-1h1v-1c1 0 1-1 2-2-1 0-3 0-4 1h0v-1c1-1 2 0 3-1h3c0-1 0-1 1-2h1-2l-1-1h-2l-1-1v-1c1 0 1 0 1-1h0-1-1s-1-1-1-2l1-1h2 1c0-1-1-1-2-1h-1c-1 0-1 0-2 1h-1-1-2c-1 0-2 1-3 1v-1l2-1 1-1h-1-3 0l2-1 1-2c1 0 2-1 3-2v-2h1l-1-1c0-1 1-1 1-2-1 0-1-1-2-1l2-4h1c1-1 1-1 1-3l-1-1h0l1-3 1 1c0-2 0-3 1-4h-2l1-1c-1-2-1-4-2-5-1 0-2-1-2-1v-1l-1-1h0c0-2 0-5-1-7h-1-1s-1 0-1-1h0v-8l-1-1z" class="f"></path><path d="M205 156h1 1l-1 3c-1-1-1-1-1-2v-1z" class="b"></path><path d="M211 180l3-3c0 2 0 3-1 4v1h-1v-2h-1 0z" class="Y"></path><path d="M197 148l2-2c1 0 1 0 2 1h0c1-1 0-1 1-1v3c0-1-1-1-1-2l-1 2h-2 0l-1-1z" class="M"></path><path d="M200 133h0c2 2 1 6 1 8h-1c-1-2 0-6 0-8z" class="G"></path><path d="M211 163c1 3 1 3 0 5v1l-1 1h-1v-4h1c1-1 1-2 1-3z" class="O"></path><path d="M209 160v-1h1c1 1 1 3 1 4s0 2-1 3h-1v-6z" class="D"></path><path d="M212 157h0 1c2 2 2 10 2 13h-1c0-2 0-5-1-7l-1-6z" class="Y"></path><path d="M219 164c1 2 2 7 0 9v-1h0c-1-1-1-3-1-4 1-1 1-1 1-2-1-1-1-1-2-1h0v-1c1-1 1 0 2 0z" class="k"></path><path d="M203 151l2-2h0c1 1 1 3 1 4v3h-1l-1-1c0-1-1-2-1-3v-1z" class="S"></path><path d="M204 155c1-1 1-2 1-3l1 1v3h-1l-1-1z" class="L"></path><path d="M206 153l1-1c0-2 0-5 1-7 0 3 2 7-1 11h0-1v-3z" class="S"></path><path d="M212 174l1-1 1-1h0v5l-3 3v-1c0-1 0-1-1-2l2-3h0z" class="L"></path><path d="M210 174l1-1 1 1-2 3c1 1 1 1 1 2v1h0c-1 1-1 2-3 2h0 0v-4c1-1 1-3 2-4z" class="B"></path><path d="M210 177c1 1 1 1 1 2v1h0c-1 1-1 2-3 2l2-5z" class="S"></path><path d="M198 149h0 2l1-2c0 1 1 1 1 2h0l1 2v1c0 1 1 2 1 3l1 1v1c-1-1-1-2-2-2 0 1-1 1 0 2h-2l1-1c-1-2-1-4-2-5-1 0-2-1-2-1v-1z" class="c"></path><path d="M202 149l1 2v1c0 1 1 2 1 3l1 1v1c-1-1-1-2-2-2 0 1-1 1 0 2h-2l1-1v-1-6z" class="Y"></path><path d="M221 181h2v1h0c-1 2-2 7-5 8h0l-1-1v1l-1-1h-1 0c1-1 2-2 3-4h0 1c0-1 1-2 2-3v-1z" class="M"></path><path d="M209 191l1 1-2 2c1 0 2 0 3 1h3c0 1-1 1-1 2-4 0-7-1-10 0v-1c1 0 1-1 2-2-1 0-3 0-4 1h0v-1c1-1 2 0 3-1h3c0-1 0-1 1-2h1z" class="Y"></path><path d="M203 157c-1-1 0-1 0-2 1 0 1 1 2 2 0 1 0 1 1 2l-1 1h1l1 1h0l1 1c-1 2 0 3-1 5 0 1-1 1-1 2-1 1-1 2-1 3h0l1-1c1 0 1 0 2 1v-1c1 0 1-1 1-2-1-2-1-5-1-8l1-1v6 4h1l1-1c0 1 0 2 1 3v2h0l-1-1-1 1-1-1c-1 0-2-1-3-1v1h0c-1 1-1 2-2 3v1c-1 0-1-1-1-1 0-1-1-1-2-1l1-2c0-1 0-2-1-3-1 0-1 0-2 2-1 0-1-1-2-1l2-4h1c1-1 1-1 1-3l-1-1h0l1-3 1 1c0-2 0-3 1-4z" class="P"></path><path d="M206 160l1 1h0c-1 1-2 1-3 2 0-1 0-1 1-2l1-1z" class="c"></path><path d="M209 173v-2h1v1h2v2h0l-1-1-1 1-1-1z" class="Z"></path><path d="M204 163c1-1 2-1 3-2l-1 8c-1-2-1-4-2-6z" class="C"></path><path d="M201 160l1 1c1 1 1 3 0 5v1h0-2c1-1 1-1 1-3l-1-1h0l1-3z" class="L"></path><path d="M200 167h2c-1 1-1 1-2 3l1-1c1 1 1 2 1 3h0 1v-2h1 1l-1 3h2c-1 1-1 2-2 3v1c-1 0-1-1-1-1 0-1-1-1-2-1l1-2c0-1 0-2-1-3-1 0-1 0-2 2-1 0-1-1-2-1l2-4h1z" class="S"></path><path d="M199 172c1-2 1-2 2-2 1 1 1 2 1 3l-1 2c1 0 2 0 2 1 0 0 0 1 1 1v-1c1-1 1-2 2-3h0v-1c1 0 2 1 3 1l1 1c-1 1-1 3-2 4v4h0 0c0 1 0 1-1 2v-2l-2 2c0-1-1-1-2-1h-1c-1 0-1 0-2 1h-1-1-2c-1 0-2 1-3 1v-1l2-1 1-1h-1-3 0l2-1 1-2c1 0 2-1 3-2v-2h1l-1-1c0-1 1-1 1-2z" class="O"></path><path d="M197 182l2 1 2-1s0 1 1 1c-1 0-1 0-2 1h-1-1-2l1-2z" class="c"></path><path d="M204 176l1 1h0c0 2 0 3-1 5h-1l-1-1c1-1 1-2 2-4v-1z" class="C"></path><path d="M206 173v-1c1 0 2 1 3 1l1 1c-1 1-1 3-2 4v4h0 0c0 1 0 1-1 2v-2c1-1 0-2 0-3l1-1v-1-3h0c-1-1-1-1-2-1z" class="L"></path><path d="M198 177l1 1c0-1 1-1 1-2h0v-1-1c1 1 1 1 1 2 0 2-1 2-1 4l-1-2-1 1v1c0 1-1 1-2 1 0 0 0-1-1-2 1 0 2-1 3-2z" class="K"></path><path d="M195 179c1 1 1 2 1 2 1 0 2 0 2-1v-1l1-1 1 2c-1 1-2 2-3 2l-1 2c-1 0-2 1-3 1v-1l2-1 1-1h-1-3 0l2-1 1-2z" class="N"></path><path d="M159 232c-1-2-5-2-6-4h0c3 1 5 2 7 3l2 2 5 5c1-1 1-1 1-2 2 1 3 1 4 2v-1l1-1c1 1 2 3 4 3 1-1 2-1 3-1l-1-1 4-2v-1c-1 1-2 1-3 1v-1l3-1h1v2h1v-1h0 1c0 1 5 3 5 4s0 1 1 1h-1c1 2 2 3 3 5h0c2 4 7 9 6 14h0l-1 1-1 1c1 3 1 6 0 9-1-1-1-2-2-3v4c-1 3-3 6-6 7l-10 4c-1 0-1 0-2-1-1 0-3 1-5 1l-9 4c-1 1-3 2-4 2v-1c-2 0-4 1-5 1-7 2-13 3-19 6v-1c1-1 2-1 4-2 5-2 11-3 17-5-7 0-16 0-21-4 3 1 6 2 9 2 3 1 7 0 10 0 0 0-1-1-2-1-2-1-4-1-5-2-5-1-9-4-12-8h0c4 3 8 5 12 7 2 1 3 1 5 2h1c0-1 1-1 1-1h3c1-1 3-1 4-2h-2v-1c4-1 8-2 11-5-1-1-1-1-1 0-3 2-7 4-10 4 0-1 2-1 3-2 1 0 2-1 4-2v-1c-2 1-4 2-6 2h1c2-2 4-3 5-6l1-1c0-1 0-1 1-1 0-6-2-12-4-17-1-1-1-3-2-4-2-2-2-4-4-6v-2h1c0-2 0-3-1-4z" class="J"></path><path d="M181 262c0-2-3-8-3-9l1 2c1 2 3 3 4 5-1 1-1 0-2 2z" class="P"></path><path d="M178 251c1 0 5 5 6 7 2 3 3 8 2 12-1 3-2 6-5 8h-1c2-3 4-5 5-8v-1-2c0-3 0-7-3-10-1-1-3-4-4-6h0zm1 13h1c0 3 0 7-2 9-1 2-2 3-3 4l1-1c0 1 1 1 2 1-1 1-3 1-4 1-1 1-3 2-5 2h-3c-1 0-2 1-3 1 0-1 1-1 1-1 4-1 9-3 12-6s3-7 3-10z"></path><path d="M168 245c2 0 3 0 4 1 2 1 3 2 3 4 1 0 0 0 0 1 0 0 1 1 1 2 2 3 4 7 4 11h-1c0-3-2-8-3-11-2-2-2-2-4-3v-1c-1 0-2 0-2-1h-1 0c-1-1-2-1-4-1v-1l1-1h2 0z" class="d"></path><defs><linearGradient id="I" x1="182.464" y1="271.975" x2="176.156" y2="265.388" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#27272a"></stop></linearGradient></defs><path fill="url(#I)" d="M183 260c1 4 1 9-1 13-1 2-2 4-4 4-1 0-2 0-2-1l2-2c3-4 4-7 3-12 1-2 1-1 2-2z"></path><path d="M168 236c2 1 3 1 4 2s3 1 4 2c2 2 7 3 7 6h-1 0-2 0l-1 1c2 2 4 3 5 6h0l1 1h0c-1 0-1-1-1-1-1-2-3-4-4-5-1 0-1 0-2-1l-4-4h0 2v-1-1l-1 1c-3-1-5-2-8-4 1-1 1-1 1-2z"></path><path d="M159 232c2 1 3 2 4 3l8 8c2 1 4 2 5 4h0-1l-3-3-1-1c-1 1-2 0-3 1v1h0-2l-1 1v1c2 0 3 0 4 1h0-4 0c-1-1-1-3-2-4-2-2-2-4-4-6v-2h1c0-2 0-3-1-4z" class="D"></path><path d="M159 232c2 1 3 2 4 3v2 2h0l-2-1c-1 0-1 0-1-1h1l-1-1c0-2 0-3-1-4z" class="M"></path><path d="M161 238v-2l2 1v2h0l-2-1z"></path><path d="M163 235l8 8h0c-2 0-2 0-3-1s-2-1-4-2c0 0 0-1-1-1h0v-2-2z" class="P"></path><path d="M178 280c5-2 8-4 9-9 1-4 0-10-2-14-2-2-3-4-5-6h1l1 1c1 2 3 4 5 6 3 6 2 12 0 18h0c2-2 2-4 3-6v-1c1-5-1-10-4-14v-1h0l-1-1 1-1h0l1 1c0 2 1 4 2 6s2 4 2 7c0 1-1 6 0 6 2-2 1-6 1-10-1-2-3-8-5-10h-1l-5-5h0 2 1c3 1 4 5 6 7 1 2 2 4 2 6 1 3 2 9 0 12l-1 1c0 1-1 2-1 3h0c4-4 4-9 4-15-1-3-3-6-5-9 0-1-1-2-1-3 1 0 2 2 3 3 0 1 0 2 1 3 2 1 3 4 3 6l1 3v6c-1 3-3 6-6 7l-10 4c-1 0-1 0-2-1zm-13-32h0 4 1c0 1 1 1 2 1v1c1 2 3 4 4 6 0 2 1 3 1 5 1 3 1 8-1 11l-2 2v-1h1c2-3 3-7 2-11-1-2-2-4-2-6s-2-4-3-5h-2v1h1c1 1 2 3 3 5 0 1 1 3 1 4 2 5 0 9-2 12-1 3-3 4-6 5-2 1-5 2-8 2h-1c1-1 3-1 4-2h-2v-1c4-1 8-2 11-5-1-1-1-1-1 0-3 2-7 4-10 4 0-1 2-1 3-2 1 0 2-1 4-2v-1c-2 1-4 2-6 2h1c2-2 4-3 5-6l1-1c0-1 0-1 1-1 0-6-2-12-4-17z"></path><path d="M167 271v-1h1l-1 1h0c1 0 2-1 2-1 1-2 1-3 2-5v-2c-1-1 0-2-1-3l1-1s1 1 1 2v1h0v1h1v-3c-1-1-1-1-1-2h0c1 1 2 1 2 3h0c1 2 1 5 1 6l-1 1v1s0 1-1 1v1c0 1 0 1-1 1v1l-2 2v-1h0c1-1 1-1 1-2h0c-1-1-1-1-1 0-3 2-7 4-10 4 0-1 2-1 3-2 1 0 2-1 4-2v-1z" class="Y"></path><path d="M183 233h1v2h1v-1h0 1c0 1 5 3 5 4s0 1 1 1h-1c1 2 2 3 3 5h0c2 4 7 9 6 14h0l-1 1-1 1c1 3 1 6 0 9-1-1-1-2-2-3v4-6l-1-3c0-2-1-5-3-6-1-1-1-2-1-3-1-1-2-3-3-3 0 1 1 2 1 3 2 3 4 6 5 9 0 6 0 11-4 15h0c0-1 1-2 1-3l1-1c2-3 1-9 0-12 0-2-1-4-2-6-2-2-3-6-6-7h-1-2l1-1h0 1c0-3-5-4-7-6-1-1-3-1-4-2v-1l1-1c1 1 2 3 4 3 1-1 2-1 3-1l-1-1 4-2v-1c-1 1-2 1-3 1v-1l3-1z" class="T"></path><path d="M190 246c-1 0-1-1-2 0h0c-3-2-6-3-8-6h0c2 1 3 1 5 2 1 0 1 0 2 1s3 2 3 3zm-10-8h0c1 0 2-1 3 0 1 0 3 2 3 2 0 1-1 1-1 1h-1c-1-1-2-1-3-1 0-1 0-1-1-1 0 0-1 0-1 1l-2-1c1-1 2-1 3-1z" class="f"></path><path d="M194 253l1 1 1 3c1 1 1 2 1 4h1v-1h0c1 3 1 6 0 9-1-1-1-2-2-3v4-6l-1-3c0 1 0 1 1 2v-1c0-2 0-4-1-6 0-1-1-2-1-3z" class="Q"></path><path d="M183 233h1v2h1v-1h0 1c0 1 5 3 5 4s0 1 1 1h-1c1 2 2 3 3 5h0c2 4 7 9 6 14h0l-1 1-1 1h0v1h-1c0-2 0-3-1-4l-1-3-1-1h0l-2-2v-2c-1 0-1 0-1-1l-1-2c0-1-2-2-3-3s-1-1-2-1v-1s1 0 1-1c0 0-2-2-3-2-1-1-2 0-3 0h0l-1-1 4-2v-1c-1 1-2 1-3 1v-1l3-1z" class="F"></path><path d="M194 244c-2-1-3-3-5-5l-4-3h1l5 3c1 2 2 3 3 5h0z" class="Z"></path><path d="M198 260c0-2 0-3-1-5l-1-1c0-1 1-1 1-1 0-1-1-2-1-2v-1c2 2 3 5 4 8l-1 1-1 1h0z" class="f"></path><path d="M180 238c2-1 4-1 5 0 1 0 2 1 3 2l1 1c2 1 4 4 5 7 0 1 0 1 1 1v2h0c-1-1-1-2-2-3 0 0 0-1-1-1 0-2-2-3-4-4h-1c-1-1-1-1-2-1v-1s1 0 1-1c0 0-2-2-3-2-1-1-2 0-3 0z" class="d"></path><path d="M131 613l1 1c0 1-1 2-1 3s1 2 1 3c0 3 0 5 1 8l1 1c2 2 4 4 5 6 1 3 14 9 17 10l5 1c2 0 3 1 5 1h15 9c2 0 3-1 4-1 1-1 3-1 4-2h3c1-1 3-1 5-1 2-1 5-2 7-2h0c0 1 1 1 1 1l1 1c-3 1-6 2-8 2l-1 1h1 3c1 0 0-1 2-1 0 0 1-1 2-1h0c1 0 1-1 2-1h2 0l1-1h1 1c1-1 1-1 2-1 0 0 0-1 1-1 0 0 1 0 2-1 1 0 2-1 3-1 4-4 11-6 16-7h1c1-1 2-1 3-1h0 4l1 1h2c1 1 2 0 2 1 3 0 5-1 7 0h0l3-1v1h1c2 0 6 1 8 0h0 1v1c1 1 2 1 3 1-1 0-4 0-5 1l-1 1h-2c-1 0-2 0-4 1-1 0-1 1-2 1l-10 1-6 3v-1h1l-3-1-7 3-2-1c-3 1-7 3-10 5-5 2-10 4-15 7-3 2-7 5-11 6v-1c1 0 2-1 3-1v-1l1 1c0-1 0-1 1-1 1-1 1-1 2-1h-1-1v1h-1c-1 0-1 0-2 1h0-1l-1 1h-1-1 0c-1 1-2 1-3 1-2 0-3 1-5 1l-11 2c-1 1-3 1-4 1h-3v1h-7-3-4c0-1 0 0-1-1h-1-2s-1 0-2-1h-1-1-1c-1 0-2 0-3-1l-3-1c-2-1-3-1-5-2l-3-3-1-1c-1 0-1 0-1-1-1-2-3-5-5-6l1-1h0c-1-1-2-2-3-2v1l-2-2c0 1 1 2 0 3h-1v-1-1c-1-1-1-2-1-3-1 0 0 0-1 1h0l-1-1h0l-1-1c0-1 1-1 1-1v-1-1c1-1 1-1 1-2h0-2l1-1v-2h0c1-2 2-4 2-6 0-1 1-2 1-3h0c1-2 1-3 1-4v-2c1 0 1-1 1-1 1-2 1-3 2-4z" class="T"></path><path d="M178 658h3 7c1 0 3-1 4-1l6-1s1 0 1-1h1 1c0 1-2 1-3 2h-3c-1 1-2 1-4 2-2 0-5 1-8 0h0c-2 0-4 0-5-1z" class="j"></path><path d="M169 656h8 12c3-1 6-2 10-3 2-1 5-1 8-2 2-1 5-3 8-4h0l-1 1s-1 1-2 1c-2 1-3 2-5 3-1 0-2 0-3 1-3 1-7 2-10 3s-8 2-11 1h-6c-2 1-3 0-5 1h-5c-1-1-2-1-3-1l-5-1c-1 0-3-1-5-1l-1-1v-1c2 1 3 1 5 2 3 0 7 1 11 1z" class="e"></path><path d="M199 660c2-2 5-2 7-3s3-2 5-3c2 0 4-1 5-2 6-5 13-7 19-10 1-1 2-2 3-2h1c1-1 1-1 2-1s1 0 2 1c0 0 1 0 2-1h-2l-2-1v-1c1 0 2 1 3 1s1 0 2 1c2 0 3-1 5-1h2 0-1l-2 1h-1c-1 0-2 1-3 1h-1c-1 1-1 1-2 1s-2 1-3 1c-3 1-7 3-10 5-5 2-10 4-15 7-3 2-7 5-11 6v-1c1 0 2-1 3-1v-1l1 1c0-1 0-1 1-1 1-1 1-1 2-1h-1-1v1h-1c-1 0-1 0-2 1h0-1l-1 1h-1-1 0c-1 1-2 1-3 1z" class="i"></path><path d="M249 630h0 4l1 1h2c1 1 2 0 2 1 3 0 5-1 7 0h0l-3 1h0l1 1c-2 1-2 1-3 1-3 0-5-1-7 0h-1l-4 1h0c-1-1-3-2-4-2v1c1 0 2 1 4 1l-1 1c-2 0-3-2-5-2s-6 2-8 3c-5 3-11 5-16 8h0c2-2 4-3 6-4l14-7h0c-1 0-3 0-4 1s-3 1-4 2h-1c4-4 11-6 16-7h1c1-1 2-1 3-1z" class="O"></path><defs><linearGradient id="J" x1="257.88" y1="633.814" x2="259.907" y2="629.818" xlink:href="#B"><stop offset="0" stop-color="#6c6c6f"></stop><stop offset="1" stop-color="#83817e"></stop></linearGradient></defs><path fill="url(#J)" d="M254 631h2c1 1 2 0 2 1 3 0 5-1 7 0h0l-3 1h0c-2 1-4 1-5 0-1 0-2 0-4-1h-4c-2 0-4 2-6 1l1-1h3l1-1h6z"></path><path d="M265 632l3-1v1h1c2 0 6 1 8 0h0 1v1c1 1 2 1 3 1-1 0-4 0-5 1l-1 1h-2c-1 0-2 0-4 1-1 0-1 1-2 1l-10 1-6 3v-1h1l-3-1-7 3-2-1c1 0 2-1 3-1s1 0 2-1h1c1 0 2-1 3-1h1l2-1h1 0-2c-2 0-3 1-5 1 1-2 6-2 7-3 1 0 0 0 0-1h-1 1c2-1 4 0 7 0 1 0 1 0 3-1l-1-1h0l3-1z" class="G"></path><path d="M263 634h2 2 0c-1 1-2 1-4 1h-1c-1 0-1 1-1 1h-1v-1c1 0 1 0 3-1z" class="C"></path><path d="M252 635h1c2-1 4 0 7 0v1h-4-1-2c1 0 0 0 0-1h-1z" class="g"></path><path d="M249 640c2 0 6-1 8-2s4-1 7-1c-2 1-5 0-7 2h0l-6 3v-1h1l-3-1zm-118 4c1 0 1 0 2 1 1 0 1-1 1-1 3 3 7 6 10 9l1-1c1-1 2 0 3 0l1 1h0l6 3 2 1 5 1c1 0 3 1 5 1h3 0c2-1 5 0 7-1h1c1 1 3 1 5 1h0c3 1 6 0 8 0 2-1 3-1 4-2h3 0 1c1 0 2-1 3 0h0c-1 1-2 1-3 1l-3 1-3 1-2 1h-4c0 1 0 0-1 1h-1c-1 0-1 1-2 1-1 1-3 1-4 1h-3v1h-7-3-4c0-1 0 0-1-1h-1-2s-1 0-2-1h-1-1-1c-1 0-2 0-3-1l-3-1c-2-1-3-1-5-2l-3-3-1-1c-1 0-1 0-1-1-1-2-3-5-5-6l1-1c1 2 3 4 5 5-1-3-5-5-7-8h0z" class="a"></path><path d="M167 659h3v1h-6 2l1-1z" class="j"></path><path d="M170 659h5 0 0c-1 1-1 1-2 1h-3v-1h0z" class="m"></path><path d="M155 656l2 1 5 1c1 0 3 1 5 1l-1 1h-2l-3-1c-2 0-5-1-7-2h1v-1z" class="h"></path><path d="M134 644c3 3 7 6 10 9l1-1c1-1 2 0 3 0l1 1h0l6 3v1h-1-2 0c-2-1-4-1-5-2-2-1-6-4-7-4l-1 1c2 1 4 2 5 3 2 1 8 3 9 4h0-1c-3-1-11-4-13-7-1-3-4-5-6-7 1 0 1-1 1-1z" class="S"></path><path d="M144 653l1-1c1-1 2 0 3 0l1 1h0l-1 2-4-2z" class="j"></path><path d="M149 653l6 3v1h-1-2l-4-2 1-2z" class="n"></path><path d="M131 644c1 0 1 0 2 1 2 2 5 4 6 7 2 3 10 6 13 7h1l2 2h1l3 1 5 1h5 7c2-1 4-1 7-1v1h-4-1c-1 0-1 1-2 1v1h-7-3-4c0-1 0 0-1-1h-1-2s-1 0-2-1h-1-1-1c-1 0-2 0-3-1l-3-1c-2-1-3-1-5-2l-3-3-1-1c-1 0-1 0-1-1-1-2-3-5-5-6l1-1c1 2 3 4 5 5-1-3-5-5-7-8h0z" class="j"></path><path d="M131 613l1 1c0 1-1 2-1 3s1 2 1 3c0 3 0 5 1 8l1 1c2 2 4 4 5 6 1 3 14 9 17 10l5 1c2 0 3 1 5 1h15 9c2 0 3-1 4-1 1-1 3-1 4-2h3c1-1 3-1 5-1 2-1 5-2 7-2h0c0 1 1 1 1 1l1 1c-3 1-6 2-8 2l-1 1h1 3c1 0 0-1 2-1 0 0 1-1 2-1h0c1 0 1-1 2-1h2 0l1-1h1 1c1-1 1-1 2-1 0 0 0-1 1-1 0 0 1 0 2-1 1 0 2-1 3-1h1c1-1 3-1 4-2s3-1 4-1h0l-14 7c-2 1-4 2-6 4h0c0 1-1 2-2 2l-1 1c-2 1-4 2-5 2s-1 1-1 1l-2 1h-2l-1 1v-1h0c1-1 2-1 3-1 2-1 3-2 5-3 1 0 2-1 2-1l1-1h0c-3 1-6 3-8 4-3 1-6 1-8 2-4 1-7 2-10 3h-12-8c2-1 4 0 5-1 4-1 15 1 16-1h0c-13 0-28 2-40-4-2 0-3-2-4-3-1 0-2-1-3-1 0 1 3 3 5 4h0v1 1c-1 0-2-1-3 0l-1 1c-3-3-7-6-10-9 0 0 0 1-1 1-1-1-1-1-2-1h0c2 3 6 5 7 8-2-1-4-3-5-5h0c-1-1-2-2-3-2v1l-2-2c0 1 1 2 0 3h-1v-1-1c-1-1-1-2-1-3-1 0 0 0-1 1h0l-1-1h0l-1-1c0-1 1-1 1-1v-1-1c1-1 1-1 1-2h0-2l1-1v-2h0c1-2 2-4 2-6 0-1 1-2 1-3h0c1-2 1-3 1-4v-2c1 0 1-1 1-1 1-2 1-3 2-4z" class="J"></path><path d="M133 628l1 1c2 2 4 4 5 6 1 3 14 9 17 10l5 1c2 0 3 1 5 1h15 9c2 0 3-1 4-1 1-1 3-1 4-2h3c1-1 3-1 5-1 2-1 5-2 7-2h0c0 1 1 1 1 1l1 1c-3 1-6 2-8 2l-1 1h-4l-1-1h-2l-1 1h2v1h-4c-2 0-5 1-7 1h-11-10c-5 0-10-1-15-3-2-1-3-2-5-2h-1c1 1 2 1 3 2 2 1 4 1 6 2s5 1 8 2h12c5 0 12 1 16 0 3-1 5-1 7-1h1c-1 2-5 0-7 1-3 2-8 1-11 1h-17c-4 0-7-1-10-2-2-1-4-2-7-3-1-1-2-2-5-3h0 0c-2 0-4-1-5-3h0c0-1-1-1-1-2 0 0 0-1-1-1v-1l-1-1c-2-2-2-4-2-6z" class="M"></path><path d="M131 613l1 1c0 1-1 2-1 3s1 2 1 3c0 3 0 5 1 8 0 2 0 4 2 6l1 1v1c1 0 1 1 1 1 0 1 1 1 1 2h0c1 2 3 3 5 3h0c2 2 3 4 6 5 5 2 11 4 16 4 7 1 15 0 22 0h5s0 1 1 1h1c-1 1-6 0-7 0-8-1-16 0-24 0-3-1-7-2-11-3-1 0-2-1-3-1h0c3 3 7 3 11 4 7 2 16 1 24 1h5c1 0 1 1 1 1-13 0-28 2-40-4-2 0-3-2-4-3-1 0-2-1-3-1 0 1 3 3 5 4h0v1 1c-1 0-2-1-3 0l-1 1c-3-3-7-6-10-9 0 0 0 1-1 1-1-1-1-1-2-1h0c2 3 6 5 7 8-2-1-4-3-5-5h0c-1-1-2-2-3-2v1l-2-2c0 1 1 2 0 3h-1v-1-1c-1-1-1-2-1-3-1 0 0 0-1 1h0l-1-1h0l-1-1c0-1 1-1 1-1v-1-1c1-1 1-1 1-2h0-2l1-1v-2h0c1-2 2-4 2-6 0-1 1-2 1-3h0c1-2 1-3 1-4v-2c1 0 1-1 1-1 1-2 1-3 2-4z" class="f"></path><path d="M125 636h0 1c0-1 0-2 1-3h1l-1 5h-1 0l-2 2v-1-1c1-1 1-1 1-2z" class="K"></path><path d="M124 640l2-2h0 1c0 2 0 3-1 4-1 0 0 0-1 1h0l-1-1h0l-1-1c0-1 1-1 1-1z" class="d"></path><path d="M124 640l2-2h0l-2 4h0l-1-1c0-1 1-1 1-1z" class="H"></path><path d="M131 644l-1-1c-1-2-2-5-1-6v-1h1c1 0 1-1 1-1 2 1 2 1 3 2 0 2 1 3 2 4l1-1h0c1 1 3 4 4 5h1l1 1c0 1 3 3 5 4h0v1 1c-1 0-2-1-3 0l-1 1c-3-3-7-6-10-9 0 0 0 1-1 1-1-1-1-1-2-1z" class="Y"></path><path d="M134 644v-1c-1-1-1-1-1-2l6 6 1-1c-1-1-4-2-4-4 2 1 3 3 4 4l8 5v1c-1 0-2-1-3 0l-1 1c-3-3-7-6-10-9z" class="a"></path><path d="M197 148l1 1v1s1 1 2 1c1 1 1 3 2 5l-1 1h2c-1 1-1 2-1 4l-1-1-1 3h0l1 1c0 2 0 2-1 3h-1l-2 4c1 0 1 1 2 1 0 1-1 1-1 2l1 1h-1v2c-1 1-2 2-3 2l-1 2-2 1h0 3 1l-1 1-2 1v1c1 0 2-1 3-1h2 1 1c1-1 1-1 2-1h1c1 0 2 0 2 1h-1-2l-1 1c0 1 1 2 1 2h1 1 0c0 1 0 1-1 1v1l1 1h2l1 1h2-1c-1 1-1 1-1 2h-3c-1 1-2 0-3 1v1h0c1-1 3-1 4-1-1 1-1 2-2 2v1h-1v1h1 1c0 1 0 1-1 1v1c1 0 2 0 4-1 1 0 1-1 2-1h3c-1 1-2 1-1 2h0v1c-1 0-1 0-2 1-1 0-3 1-4 1v1h-1c-2 1-5 2-7 2h-2l-1 1c2 0 3 0 4 1l3 1 1 1h1v-1h3c1 0 2-1 3 0v1h0c1 1 1 1 2 1l1 1c-1 1-2 1-3 1h-1-2s0 1-1 1h-1s0 1-1 1h-2l-3 1h-1l-1 1c0 1 0 1-1 2h-1c-2 1-2 1-4 1h-6v1c-1 0-1 1-2 1s-2 0-3 1h-1v1h1l3 2h0l-1 1h1s2 0 2 1c1 0 2 0 2 1h2l1 1c-1 0-1 0-2 1l-3 2h-1l-3 1v1c1 0 2 0 3-1v1l-4 2 1 1c-1 0-2 0-3 1-2 0-3-2-4-3l-1 1v1c-1-1-2-1-4-2 0 1 0 1-1 2l-5-5-2-2c-2-1-4-2-7-3h0c1 2 5 2 6 4 1 1 1 2 1 4h-1v2c2 2 2 4 4 6 1 1 1 3 2 4 2 5 4 11 4 17-1 0-1 0-1 1l-1 1v-9c-1-2-2-4-2-6l-14-3-2-11c-1-2-1-3-1-5v-3l-1-1c0 2 0 4 1 6l2 13c-3-1-6-2-8-4v-1c-2 4-4 8-6 11-4 8-7 15-9 23l-3 9v2c0-2 0-4 1-6 2-9 5-17 9-25l5-10c0-1 1-3 1-4v-1c-3 2-5 5-7 8-9 12-16 25-21 39h0c4-13 10-25 18-36 2-4 5-8 9-11v-1l-3-3-4 4c-7 7-13 15-19 22h0c7-10 14-18 23-26l-2-2c-2 1-5 3-7 5-8 6-15 13-22 20l-5 6-3 3v-1c7-9 15-17 24-24 4-4 8-7 13-10h0l-2-4c-2 0-5 2-7 3-7 3-14 7-21 11-8 6-17 11-25 17h0c10-8 21-15 32-22 7-3 14-8 21-9 0-1 0-4-1-5-24 4-47 13-69 23h-1c14-7 29-13 44-18 5-2 10-3 15-4l7-1c1 0 2 0 3-1 0-1-1-1-1-2-5 0-10 1-15 1-17 2-33 6-49 11h-1c21-8 42-12 64-13h1c0-1-1-6 0-7 0-2 9-6 10-8 4-2 5-5 7-8-4-4-8-7-13-10h0c4 2 7 4 10 6-3-4-8-8-13-11l-5-4c-1 0-2-1-3-2h1c1 1 2 2 3 2 8 5 14 11 21 17 0-1 1-3 1-5 0-3-8-13-10-15-1-1-4-4-4-5 1 0 2 2 2 3 4 3 8 8 10 13 1 1 2 2 3 2 0-1-2-6-3-8 0-1-1-2 0-3 0 2 1 4 2 5 0 1 0 2 1 3h0c0-3 2-6 1-9 0-2-1-3-1-5 0-1-4-9-3-10 2 4 3 8 4 13 1 2 1 5 1 7l1-1v-2c2-3 4-5 6-8 0-1 1-2 1-2-1 4-4 6-6 10v1l13-16c0 2-6 8-7 11 2-2 13-13 14-13-2 3-5 6-9 9 2 0 3-1 4-2l6-4c0-1 0-1 1-1 1-2 5-4 7-5 1-1 2-1 3-2h1 1c1 0 1-1 2-2l1 1h1c1-2 2-3 2-5h1v1h0l1-4 1 1 1-2h0z"></path><path d="M157 190h1c0 1 1 1 1 2s-1 2-2 2h-1c0-1-1-1-1-2l2-2z" class="b"></path><path d="M155 247h-1c-1 0-1-1-1-1-1 0-1 0-2-1v-3h3v1c1-1 1-1 2-1v1h-1c1 2 1 2 0 4z" class="f"></path><path d="M166 218h0c0-1-1 0-2-1 1-1 2-1 2-2 1 0 1-1 1-1 1-1 2-2 3-2h1l-2 2h0l-1 1 1 1h1l1 1s-1 1-2 1h-3z" class="d"></path><path d="M174 210h1c2 1 4-1 6-1l-2 2-5 2-1-1-1 1c1 1 1 1 2 1 0 0 1 0 1 1v1c-2 0-2 0-3 1h-1l-1-1h-1l-1-1 1-1v1c1 0 2-1 2-1h1v-2l2-2z" class="M"></path><path d="M130 217c2 1 5 2 7 4l-3 2c-1 0-2 1-3 2l-1-2c0-1-1-5 0-6z" class="n"></path><path d="M154 242v-1c2 0 3 0 4 1h2c1 1 1 2 2 3v1h0c-1 0-1 1-2 1h-2-3c1-2 1-2 0-4h1v-1c-1 0-1 0-2 1v-1z" class="P"></path><path d="M175 216h2v1h2 1v1l-3 1h-1v1c-1 0-2 1-3 0 0 0-1 0-1-1l-1 1-1-1-1 1c-1 0-2 0-3-1v-1h3c1 0 2-1 2-1h1c1-1 1-1 3-1z" class="S"></path><path d="M179 217h1v1l-3 1h-1l1-1 2-1z" class="l"></path><path d="M158 242l1-1c-3-7-7-13-13-17h0c2 0 4 2 6 4s5 5 7 8v2c2 2 2 4 4 6 1 1 1 3 2 4 2 5 4 11 4 17-1 0-1 0-1 1l-1 1v-9c-1-2-2-4-2-6h0c0-2-1-3-2-4s-2-1-3-1c1 0 1-1 2-1h0v-1c-1-1-1-2-2-3h-2z" class="T"></path><path d="M175 209c2 0 3-1 5-1s4 0 6-1l1 1h0c0 1 1 1 2 1h6v1h-3-3-3v1c0 1-1 1-1 2l-4 1c1 0 1 0 1 1-2 1-3 1-5 1h-2v-1c0-1-1-1-1-1-1 0-1 0-2-1l1-1 1 1 5-2 2-2c-2 0-4 2-6 1h-1l1-1z" class="b"></path><path d="M186 210l-5 2c1-1 3-3 4-3 0-1 3 0 4 0h6v1h-3-3-3z" class="G"></path><path d="M186 211c0 1-1 1-1 2l-4 1c1 0 1 0 1 1-2 1-3 1-5 1h-2v-1c0-1-1-1-1-1-1 0-1 0-2-1l1-1 1 1h1c2-1 3 0 4-1s1-1 2-1l1 1h0c1 1 1 0 2 0s1-1 2-1z" class="Y"></path><path d="M186 211c0 1-1 1-1 2l-4 1h-6c2-1 4-1 7-2h0c1 1 1 0 2 0s1-1 2-1z" class="B"></path><path d="M156 200c6 2 11 3 17 2-2 3-3 4-6 6l-3 3c-2 1-5 1-7 0-1 0-1 0-1-1-2-3-1-7 0-10z" class="n"></path><path d="M198 208l3 1 1 1h1v-1h3c1 0 2-1 3 0v1h0c1 1 1 1 2 1l1 1c-1 1-2 1-3 1h-1-2s0 1-1 1h-1s0 1-1 1h-2l-3 1h-1l-1 1c0 1 0 1-1 2l-1-1h-4-1-1c-3 0-6 1-8 0v-1h-1-2v-1c2 0 3 0 5-1 0-1 0-1-1-1l4-1c0-1 1-1 1-2v-1h3 3 3v-1h2 0l1-1z" class="D"></path><path d="M193 217h1c1-1 1-1 2 0 0 1 0 1-1 2l-1-1c0-1-1-1-1-1z" class="L"></path><path d="M198 216c-1 0-2 0-2-1l1-1c1 1 2 1 3 1h1l-3 1z" class="S"></path><path d="M185 213s0 1 1 1v1l1 1s-1 0-2-1h0-3c0-1 0-1-1-1l4-1z" class="M"></path><path d="M186 214c2-1 3-1 6 0h1l1 1c-2 1-5 0-7 1l-1-1v-1z" class="d"></path><path d="M180 217c1 0 3-1 5-1 3 0 5 1 8 1 0 0 1 0 1 1h-4-1-1c-3 0-6 1-8 0v-1z" class="Y"></path><path d="M186 210h3l1 1s1 0 1 1c1 1 1 1 2 1h1l-1 1h-1c-3-1-4-1-6 0-1 0-1-1-1-1 0-1 1-1 1-2v-1z" class="Z"></path><path d="M186 210h3l1 1c-1 0-2 1-3 0h-1v-1z" class="k"></path><path d="M198 208l3 1 1 1h1v-1h3c1 0 2-1 3 0v1h0c1 1 1 1 2 1l1 1c-1 1-2 1-3 1h-1-2s0 1-1 1h-1s0 1-1 1h-2-1c-1 0-2 0-3-1h0-2v-1c1 1 3 1 4 0h0-5-1c-1 0-1 0-2-1 0-1-1-1-1-1l-1-1h3 3v-1h2 0l1-1z" class="B"></path><path d="M209 210h0c1 1 1 1 2 1l1 1c-1 1-2 1-3 1h-1c0-1 0-1-1-1h0v-1h1l1-1z" class="C"></path><path d="M206 209c1 0 2-1 3 0v1l-1 1c-2 0-3-1-5-1v-1h3z" class="M"></path><path d="M198 208l3 1 1 1c-1 0-3 1-4 1h-5c1 1 4 1 5 1h1c2-1 3-1 5-1v1c-1 1-3 0-5 1h-5-1c-1 0-1 0-2-1 0-1-1-1-1-1l-1-1h3 3v-1h2 0l1-1z" class="S"></path><path d="M198 208l3 1 1 1c-1 0-3 1-4 1-2 0-4 0-6-1h3v-1h2 0l1-1z" class="M"></path><defs><linearGradient id="K" x1="201.999" y1="200.883" x2="190.991" y2="200.824" xlink:href="#B"><stop offset="0" stop-color="#414042"></stop><stop offset="1" stop-color="#595959"></stop></linearGradient></defs><path fill="url(#K)" d="M193 199c1 0 2-1 3-1 2 0 4-1 6-1v1h1 1c0 1 0 1-1 1v1c1 0 2 0 4-1 1 0 1-1 2-1h3c-1 1-2 1-1 2h0v1c-1 0-1 0-2 1-1 0-3 1-4 1v1h-1c-2 1-5 2-7 2h-2l-1 1c2 0 3 0 4 1l-1 1h0-2-6c-1 0-2 0-2-1h0l-1-1c-2 1-4 1-6 1s-3 1-5 1l1-1s1-1 2-1h1c0-1 0-1 1-2l-1-1 1-1c1 0 1 1 1 0l3 1h0c4 0 6-2 9-3v-1-1z"></path><path d="M188 205c1-1 2-1 3-2h3c-1 1-1 2-2 2h-1c-1 0-1 0-2 1l-1-1z" class="M"></path><path d="M193 199c1 0 2-1 3-1 2 0 4-1 6-1v1h1c-3 1-7 1-10 3v-1-1z" class="d"></path><path d="M195 203c1-1 2-1 4-2v1h2c1-1 2-1 3-1 2 0 3-1 5-2h0l1 1c-1 1-1 1-2 1l-2 1h-2v1h-4-4-1z" class="l"></path><path d="M175 209l1-1s1-1 2-1h1c0-1 0-1 1-2l-1-1 1-1c1 0 1 1 1 0l3 1h0c-1 1-2 1-3 2v1l7-2 1 1c-1 0-2 0-3 1-2 1-4 1-6 1s-3 1-5 1z" class="d"></path><path d="M194 203h1 1 4 4 1v1h-1c-2 1-5 2-7 2h-2l-1 1c2 0 3 0 4 1l-1 1h0-2-6c-1 0-2 0-2-1h0l-1-1c1-1 2-1 3-1 1-1 1-1 2-1h1c1 0 1-1 2-2z" class="Y"></path><path d="M196 203h4c-2 1-3 2-5 2 0 0-1-1 0-1l1-1z" class="D"></path><path d="M194 203h1 1l-1 1c-1 0 0 1 0 1l-1 1h-1c-1 1-4 1-6 2l-1-1c1-1 2-1 3-1 1-1 1-1 2-1h1c1 0 1-1 2-2z" class="L"></path><path d="M188 218h1 1 4l1 1h-1c-2 1-2 1-4 1h-6v1c-1 0-1 1-2 1s-2 0-3 1h-1v1h1l3 2h0l-1 1h1s2 0 2 1c1 0 2 0 2 1h2l1 1c-1 0-1 0-2 1l-3 2h-1l-3 1v1c1 0 2 0 3-1v1l-4 2 1 1c-1 0-2 0-3 1-2 0-3-2-4-3l-1 1v1c-1-1-2-1-4-2 0 1 0 1-1 2l-5-5-2-2h0c1-1 2 0 3 1 2-1 2-1 4-1v-1-1h-2c0 1 0 1-1 1l-2-2v-1c1-1 2-1 2-1v-1h-1c0-1 0-2 1-3h3c2 0 3 0 5-1l-1-1 1-1c0 1 1 1 1 1 1 1 2 0 3 0v-1h1l3-1c2 1 5 0 8 0z" class="d"></path><path d="M167 230c1 1 2 1 2 2l-1 1 1 1c-2-1-4-1-6-2 2-1 2-1 4-1v-1z" class="M"></path><path d="M179 224l3 2h0l-1 1h1s2 0 2 1c1 0 2 0 2 1h2l1 1c-1 0-1 0-2 1l-3 2h-1l-3 1h-1v-1c2 0 3-1 4-1-1-1-3-1-4-1l1-1h0c-2-1-4 2-6 2h0l1-1v-1c-2 1-5-1-6-1 2 0 6 0 7-1v-1h-1c2 0 3-1 4-3z" class="X"></path><path d="M187 231c-2-1-5 0-6-1l-1-1h-1c-1 0-2-1-2-1v-1h4 1s2 0 2 1c1 0 2 0 2 1h2l1 1c-1 0-1 0-2 1z" class="R"></path><path d="M160 231h0c1-1 2 0 3 1 2 1 4 1 6 2l7-1c1-1 2-2 3-2h0c1 0 3 0 4 1-1 0-2 1-4 1v1h1v1c1 0 2 0 3-1v1l-4 2 1 1c-1 0-2 0-3 1-2 0-3-2-4-3l-1 1v1c-1-1-2-1-4-2 0 1 0 1-1 2l-5-5-2-2z" class="B"></path><path d="M162 233c2 0 3 1 5 1 0 1 1 1 2 1l-1 1h0c0 1 0 1-1 2l-5-5z" class="d"></path><path d="M179 237h-1c-2-1-4-1-5-1l-1-1c0-1 5-1 6 0 0 0 1 0 1 1l1-1c1 0 2 0 3-1v1l-4 2z" class="M"></path><path d="M188 218h1 1 4l1 1h-1c-2 1-2 1-4 1h-6v1c-1 0-1 1-2 1s-2 0-3 1h-1v1h1c-1 2-2 3-4 3-1 0-2 0-3 1h0c-1-1-2-1-2-1h-1 0v-1c2-1 4-1 7-1h0c0-1-1 0-2-1h-1c-1-2-4-2-5-1s-1 2-2 2c0-1 0-2-1-2h-1v-1h3c2 0 3 0 5-1l-1-1 1-1c0 1 1 1 1 1 1 1 2 0 3 0v-1h1l3-1c2 1 5 0 8 0z" class="L"></path><path d="M188 218h1 1 4l1 1h-1c-2 1-2 1-4 1h-6v1c-1 0-1 1-2 1h-2c-1-1-2-1-3-1v-1h3 2c2 0 5-1 6-2z" class="M"></path><path d="M187 182c1 0 2 1 3 1 0 0 0-1 2-1h0 3 1l-1 1-2 1v1c1 0 2-1 3-1h2 1 1c1-1 1-1 2-1h1c1 0 2 0 2 1h-1-2l-1 1c0 1 1 2 1 2h1 1 0c0 1 0 1-1 1v1l1 1h2l1 1h2-1c-1 1-1 1-1 2h-3c-1 1-2 0-3 1v1h0c1-1 3-1 4-1-1 1-1 2-2 2v1h-1c-2 0-4 1-6 1-1 0-2 1-3 1v1 1c-3 1-5 3-9 3h0l-3-1c0 1 0 0-1 0 1-1 4-1 4-2h-1c-1 0-2 0-3 1h-1l1-1c0-1 0 0-1-1h-1-1c0-1 0-1-1-1 0-1 1-1 1-2h-1c-1 0-3-1-4 0 0 0 0 1-1 1h-2l-1 1h-3l-1-1v-1s1 0 1-1v-1h-2v-1l3-3 2-2 1 1c2-2 3-2 5-2h1c0-1 1-2 2-2v1 1c1 1 1 1 2 1s1-1 1-1c2-1 3-1 5-1l-2-1 1-1h-1c0-1 1-1 1-1 1-1 2-1 3-2z" class="d"></path><path d="M191 197c1-1 2-1 3-1v1h3c2-1 4-1 6-1v1h-1c-2 0-4 1-6 1-1 0-2 1-3 1l-1-1h1-1l-1-1z" class="l"></path><path d="M169 190c2-2 3-2 5-2l1 1c-1 0-2 1-3 1h-1c-1 0-2 1-2 1v1h1 1c0 1 0 1-1 2l-1-1c-1 0-2-1-3 0 0 0-1 1-2 1h0c1-2 3-3 5-4z" class="K"></path><path d="M178 193c-1 0-1 1-2 2-1 0-1 0-1 1-1 0-2 0-2 1-1-1-1-1-1-2h-1l-1-1c1-1 3-3 5-3l1 1 2 1z" class="Y"></path><path d="M202 187h1 1 0c0 1 0 1-1 1v1l1 1h2l1 1-1 1c-1 0-1-1-2-1 0 0-4 2-5 2h-1c-2 0-4 0-6 1v-1c0-1 0-1 1-1 1-1 2-2 4-2 1 0 2-1 4-2 0-1 0-1 1-1z" class="X"></path><path d="M180 201c4-1 7-3 11-4l1 1h1-1l1 1v1 1c-3 1-5 3-9 3h0l-3-1c0 1 0 0-1 0 1-1 4-1 4-2h-1c-1 0-2 0-3 1h-1l1-1z" class="L"></path><path d="M192 198h1-1l1 1v1 1c-3 1-5 3-9 3h0l-3-1h4c2-1 4 0 6-2 0-1 1-1 1-1h-1l-1-1c-1 0-1 0-1-1h3z" class="k"></path><path d="M180 194l1-1h0c1 0 1 0 2-1h0 2s1-1 2-1 2 0 3-1h1l4-2c2 0 3-1 5-1l1 1c-2 1-3 2-4 2-2 0-3 1-4 2-1 0-1 0-1 1v1h0c-2 0-4 0-5 1h0-3c-1 1-2 1-2 2h-1 0c-1 0-2 0-2 1h-1 0v-1c1 0 2-1 3-2 0-1 0-1-1-1z" class="P"></path><path d="M187 182c1 0 2 1 3 1 0 0 0-1 2-1h0 3 1l-1 1-2 1v1c1 0 2-1 3-1h2 1 1c1-1 1-1 2-1h1c1 0 2 0 2 1h-1-2l-1 1c0 1 1 2 1 2-1 0-1 0-1 1l-1-1c-2 0-3 1-5 1l-4 2h-1c-1 1-2 1-3 1s-2 1-2 1h-2 0c-1 1-1 1-2 1h0l-1 1-2-1-2-1c1-1 1-1 1-2l-1-2-1 1-1-1h1c0-1 1-2 2-2v1 1c1 1 1 1 2 1s1-1 1-1c2-1 3-1 5-1l-2-1 1-1h-1c0-1 1-1 1-1 1-1 2-1 3-2z" class="k"></path><path d="M198 184h1l1 1c-1 1-1 1-2 1l-1-1 1-1z" class="d"></path><path d="M191 183c1 0 2 1 2 1v1l-1 1h-2c-1 0-2 2-3 2h-1c-2 1-3 1-5 1l-1-1c2-1 3-1 5-1 1-1 2-2 3-2 1-1 2-1 3-2z" class="Y"></path><path d="M187 182c1 0 2 1 3 1 0 0 0-1 2-1h0 3 1l-1 1-2 1s-1-1-2-1c-1 1-2 1-3 2-1 0-2 1-3 2l-2-1 1-1h-1c0-1 1-1 1-1 1-1 2-1 3-2z" class="L"></path><path d="M191 183c1-1 2-1 3-1l1 1-2 1s-1-1-2-1z" class="k"></path><path d="M184 185c1 0 2-1 4 0-1 0-2 1-3 2l-2-1 1-1z" class="M"></path><path d="M197 148l1 1v1s1 1 2 1c1 1 1 3 2 5l-1 1h2c-1 1-1 2-1 4l-1-1-1 3h0l1 1c0 2 0 2-1 3h-1l-2 4c1 0 1 1 2 1 0 1-1 1-1 2l1 1h-1v2c-1 1-2 2-3 2l-1 2-2 1c-2 0-2 1-2 1-1 0-2-1-3-1-1 1-2 1-3 2 0 0-1 0-1 1h1l-1 1 2 1c-2 0-3 0-5 1 0 0 0 1-1 1s-1 0-2-1v-1-1c-1 0-2 1-2 2h-1c-2 0-3 0-5 2l-1-1c1 0 1 0 1-1h0 1l1-1v1c1-1 2-1 4-1h-7l-3 3c-1 1-1 1-2 1s-1 1-1 1h-1l-1-1h1l-1-2c1 0 1-1 2-1 0-1 0-1 1-1v-2c2-2 5-4 7-5v-1h0 0 1c-3-1-6 4-9 5-1 1-2 2-4 3h0c1-1 1-2 2-3h0l-2 2-1-1 1-1h0l1-3h0l-2 1h0c1-2 2-4 4-5l-1-1c1-1 1-2 2-3h1c0-1 1-1 1-1 2 0 3-1 4-2l6-4c0-1 0-1 1-1 1-2 5-4 7-5 1-1 2-1 3-2h1 1c1 0 1-1 2-2l1 1h1c1-2 2-3 2-5h1v1h0l1-4 1 1 1-2h0z" class="P"></path><path d="M195 149l1 1h0c0 1 0 2 1 3l-1 1v-1h-2l1-4z" class="S"></path><path d="M182 186c-1-1-2-1-3-1h-3 0l-1 1h0l-1-1h1s1-1 2-1c2 0 5-1 6 1h1l-1 1c0-1 0-1-1 0z" class="l"></path><path d="M182 186c1-1 1-1 1 0l2 1c-2 0-3 0-5 1 0 0 0 1-1 1s-1 0-2-1v-1c2 0 3 0 5-1z" class="O"></path><path d="M198 150s1 1 2 1c-1 2-1 4-2 6 0 2 0 3-1 5l-1-1c1-1 1-3 1-4h0 0l1 1v-1-2c0-1 0-3-1-4 0-1 0-1 1-1zm-21 32h0l-1-1c0-1 1-1 1-2-1 0-1 1-2 1-1 1-2 0-3 0l-1 1s-1 0-1 1h-1 0v-1h1c1-1 1-2 2-2 1-1 2-1 3-1h1 1l1 1 1 1v1l-1 1h-1z" class="l"></path><path d="M174 166c0-1 0-1 1-1 1-2 5-4 7-5 1-1 2-1 3-2h1c-2 3-7 7-10 8h-2z" class="a"></path><path d="M200 151c1 1 1 3 2 5l-1 1h2c-1 1-1 2-1 4l-1-1-1 3h0c-1 1-2 3-3 4l-2-1c1-1 2-3 2-4 1-2 1-3 1-5 1-2 1-4 2-6z" class="d"></path><path d="M201 157h2c-1 1-1 2-1 4l-1-1v-3z" class="M"></path><path d="M189 162c0-1 1-1 1-2l2 1 1-1h0c0 1 0 1 1 1l1-1v1c0 1 0 1 1 1-1 1-1 1-1 2-2 2-4 6-7 7-1 0-1 1-2 2h-1c0 1-1 1-2 2h0-2-2l-1 1-4 1h-1c-1 0-1 0-2 1h0-2 0l-2 1h0c1-2 3-2 5-3 1-1 4-1 5-2v-1c-1 0-2 0-3 1-1 0-3 1-4 2-1 0-1 0-1-1l1-1h1c1-1 2-1 3-2h0c-1 0-2 1-3 1s-1 1-2 1h0-1c1-2 3-2 5-3s5-3 8-5h0c2-1 2-2 3-3h1c1-1 2-2 3-2h0v1h1z" class="X"></path><path d="M189 162c0-1 1-1 1-2l2 1 1-1h0c0 1 0 1 1 1l1-1v1c0 1 0 1 1 1-1 1-1 1-1 2-2 2-4 6-7 7-1 0-1 1-2 2h-1c0 1-1 1-2 2h0-2-2 0v-1h2c0-1 0-2-1-2v-1c1 0 1 0 2-1h0-1v-1l3-1 2-2c1-1 3-2 4-3v-1h-1z" class="M"></path><path d="M181 174l2-1v-2s0-1 1-1 2-1 2-1c2-1 3-3 4-3 1-1 1 0 1 0h1l-2 2c-1 0-2 1-2 2v1c-1 0-1 1-2 2h-1c0 1-1 1-2 2h0-2-2 0v-1h2z" class="S"></path><path d="M196 161l1 1c0 1-1 3-2 4l2 1c1-1 2-3 3-4l1 1c0 2 0 2-1 3h-1l-2 4c1 0 1 1 2 1 0 1-1 1-1 2l1 1h-1v2c-1 1-2 2-3 2l-1 2-2 1c-2 0-2 1-2 1-1 0-2-1-3-1h-2c-2 0-3 1-5 1s-3 0-5 1h-1v-1c1-1 2-1 3-1h1l1-1v-1l-1-1-1-1c2 0 5-1 6-3 1-1 2-1 2-2h1c1-1 1-2 2-2 3-1 5-5 7-7 0-1 0-1 1-2v-1z" class="D"></path><path d="M191 181c1 0 1 0 2-1l1 1-2 1c-2 0-2 1-2 1-1 0-2-1-3-1h-2c2-1 4-1 6-1z" class="X"></path><path d="M197 171c1 0 1 1 2 1 0 1-1 1-1 2l1 1h-1l-1-1h-3l3-3z" class="l"></path><path d="M189 175v-1h1 0c2-1 5-2 6-4v-1l1 1c-2 1-3 3-5 5-1 1-2 2-4 2-1 1-3 2-4 3s-2 1-3 1v1h-3l1-1h2c4-2 5-4 8-6z" class="S"></path><path d="M185 173l1 1-1 1v1c1 0 1 0 2-1 0 0 1-1 2-1v1c-3 2-4 4-8 6h-2v-1l-1-1-1-1c2 0 5-1 6-3 1-1 2-1 2-2z" class="D"></path><path d="M178 179l6-2v1c-1 1-3 1-5 2l-1-1z" class="k"></path><path d="M194 174h3l1 1v2c-1 1-2 2-3 2l-1 2-1-1c-1 1-1 1-2 1v-1-1c-1 0-3 1-5 1 1-1 5-3 6-4h0l2-2z" class="S"></path><path d="M191 179c0-1 0-1 1-2h1 0v1 2c-1 1-1 1-2 1v-1-1z" class="L"></path><path d="M194 174h3l1 1v2c-1 1-2 2-3 2l-1 2-1-1c1-1 2-1 3-3-1 0-1 1-3 0 0 0 0-1-1-1l2-2z" class="P"></path><path d="M196 161l1 1c0 1-1 3-2 4l2 1c1-1 2-3 3-4l1 1c0 2 0 2-1 3h-1 0l-1 1c-1 1-1 1-1 2l-1-1v1c-1 2-4 3-6 4h0-1v1-1c-1 0-2 1-2 1-1 1-1 1-2 1v-1l1-1-1-1h1c1-1 1-2 2-2 3-1 5-5 7-7 0-1 0-1 1-2v-1z" class="Z"></path><path d="M196 161l1 1c0 1-1 3-2 4l2 1-3 3h-2c-1-1 2-3 3-4v-2c0-1 0-1 1-2v-1z" class="X"></path><path d="M244 155c3-5 7-11 11-17 3-4 7-9 11-13 7-7 14-14 22-20 6-4 12-7 18-10 10-5 20-10 30-12 7-2 13-3 20-4 6-1 13-2 20-2 10-1 20 0 30 1 21 2 43 6 62 18 4 2 7 5 11 9l6 5c1 1 2 2 3 2 0 1 2 1 3 0 2 0 4-1 5-3s1-5 1-7l-1-3c-2-4-5-10-9-12-2 0-3-1-5-2h51c-1 2-4 5-5 8v8 31 22 53l4 5h-78c1-1 3-3 3-4 2-4 3-10 2-14v-10c-2-13-5-27-13-38-2-4-6-7-10-11-12-11-27-17-44-17-19 1-38 9-51 23-8 9-15 23-18 35-3 11-5 23-5 34l-1 32v29l1 30-1 79c9 4 15 10 21 18l3 3c1 1 2 3 3 4l1 1-1 2 3 5c2 5 3 10 3 15v3h0-1 0c1 6 0 13 0 18v11c1 4-1 9-2 13 0 2-1 3-1 5l-1-1c-1 5-3 13-7 17-1 1-2 2-3 4l-4 6h1c-2 4-5 7-7 10-1 1-3 5-4 5-1 3-2 5-4 8 0 2 0 6 1 7h1c0 4 0 8 1 11l2-2v4s0 1 1 2c0 1 0 2 1 3-1 1-1 1-2 1v2 1c1 3 3 5 4 8l3 6c1 3 4 5 6 8h1 0v-2c1 3 3 5 4 7 9 9 24 11 37 11 4 0 9 0 14-1 15-3 29-11 40-22 5-5 10-11 12-17 3-6 4-11 5-17 2-7 3-13 4-20 1-6 1-13 1-19v-49-10h0l1 2v-13l-1 3h-1c0-4-1-8-2-12-1-6-3-12-6-17-4-9-9-19-17-25-5-4-10-7-15-9-13-6-27-7-41-7v-21-15l211-1c0 10 0 21-1 31 0 2 0 5-1 7-3-1-6-2-8-3-7-2-14 0-20 3-10 5-16 18-19 29-5 16-5 33-5 50v24 34c0 9 0 19-1 28-2 19-7 39-18 55-6 8-12 15-19 22-4 4-8 7-12 11 0-1-1-1-1-1 1 0 1-1 1-1h0l1-1h0c1-1 1-1 1-2-3 3-5 5-9 5l3-3v-2l-1-1c0 1-1 3-1 3h-1l-1 1h0c-1 0-2 2-2 3h-1v-2c0 1-1 1-1 1 0-2 0-3 1-4v-2h-1c-1 6-5 11-10 15-2 1-4 2-6 2-1 2-1 2-2 3h-2l1 1c2-1 3-1 6 0l-10 3h0l-6 1 2 1v1l-1 1s-2 0-2 1l-3 1 1 1c-11 4-22 6-34 8-15 3-30 4-45 3-11-1-22-4-32-7-2-1-5-2-7-3l-8-5c-1 0-2-1-4-1v3-3h0c0-1-1-2-2-2-1-1-3-2-4-2-2-1-3-1-5-1l1 1c-1 1-1 2-2 3v-1c-1 0-1 0-2 1l-15 5h-1l2-1v-1h-5-2c2-1 3-1 4-1h2l1-1c1-1 4-1 5-1-1 0-2 0-3-1v-1h-1 0c-2 1-6 0-8 0h-1v-1l-3 1h0c-2-1-4 0-7 0 0-1-1 0-2-1h-2l-1-1h-4 0c-1 0-2 0-3 1h-1c-5 1-12 3-16 7-1 0-2 1-3 1-1 1-2 1-2 1-1 0-1 1-1 1-1 0-1 0-2 1h-1-1l-1 1h0-2c-1 0-1 1-2 1h0c-1 0-2 1-2 1-2 0-1 1-2 1h-3-1l1-1c2 0 5-1 8-2l-1-1s-1 0-1-1h0c-2 0-5 1-7 2-2 0-4 0-5 1h-3c-1 1-3 1-4 2-1 0-2 1-4 1h-9-15c-2 0-3-1-5-1l-5-1c-3-1-16-7-17-10-1-2-3-4-5-6l-1-1c-1-3-1-5-1-8 0-1-1-2-1-3s1-2 1-3l-1-1c0-2 0-4-1-7 0-3-2-7-3-11l-2 2v-1c-1-3-2-5-3-7l-1-1c-1-1-1-1-1-2h-1v1c1 1 1 2 2 3 0 3 0 6-2 10l-1-2-1 1c-2 2-4 4-7 6h-1s-1 1-2 1h0c3-2 6-4 8-6v-1c-2 1-3 2-4 3-1-1 0-1 0-2-1 1-2 2-3 2v-1h1c1-2 3-2 4-4 1 0 2-2 2-2-1 0-3 2-4 2-1 1-1 1-2 1 1-1 2-1 3-2 2-1 3-2 4-4v-4h-1c0 2-1 3-1 4l-1 1c0 2-4 3-5 4h-2v-1c2 0 4-1 6-2v-1h-1c-2 2-6 3-9 3v1c0 1 0 1-1 1H91c-2 0-5-1-7-1-13-5-23-13-29-25l-1 1-5-19h0c-1-1 0-3 0-4h1c0-1-1-4-1-5-1-6 0-11 1-17 1-4 3-8 6-12v1c-7 10-7 20-5 33 0 3 0 5 1 8l2 9 1-1v-1c0-1 0-2-1-3 0-1 0-3-1-3v-2c-1-2-1-5-1-7s1-4 0-6c0-2-1-6 0-8 2 2 0 9 1 11v3c0 1 1 2 1 3l1-2c-1-2-1-4-1-5 1 2 1 5 2 7 1 4 2 8 4 11l1-1-3-9c-3-10-2-20 3-29h0l-3 8h2v1c-1 2-1 4-1 6v5l3 15h1 1c0 1 1 1 2 2l1 3c2 3 4 6 6 8h1l-1-2-2-1-1-2-1-1c-1-2-2-4-3-5-1-2-2-5-2-6v-1-2h0v-2-1-3c0 1 1 1 1 2 0 2 1 4 2 6v1s0 1 1 1c0 2 1 3 2 5 1 3 3 5 5 7l1 1c1 1 3 2 4 3 1 0 1 2 2 2 3 0 3 1 5 2h3 2 0c3 0 7 0 10-1 1 0 1-1 2-2 1 1 0 1 0 2h3l-5-9v-2c0-1 0-1-1-2 0-1-1-1-1-2v-1-4h1v-1l-1-4c1 0 1 0 1-1v-6c-1 0 0-1 0-1 0-2 0-3-1-5 0-2 1-9-1-11v-2h-1v-4h-2c0-2 0-4 1-6h-2c0-2 0-1 1-3-2-1-2-2-3-3v-1h1c1 1 1 2 1 2 2 2 3 2 5 2l2-2c-2 1-3 1-4 1s-2-1-3-2v-1h1c1 1 3 2 5 1s4-2 5-4l1-2c-1-1-1-1-1-2 0-2-2-5-3-7 0-1-1-2 0-3h0 1c-2-2-4-5-5-8-4-8-8-17-9-26v-4h1v1l1 9h2l1 2 1-1c2 2 2 5 4 7h0 1l1 3c1 0 1-2 1-3l1 2h1v-1c0-1-1-2-2-2v1l-1-1v-1h-1v-1l1 1c1 0 2-1 3-1-1-1-2-2-2-3s-1-2-1-2v-3c-1-2-2-5-2-8h0v-1l1-1v1-1-1l-1-1c0-1 0-3-1-4v-2c-1-5-1-11-1-16h0v-9c1-9 3-18 6-27 1-2 1-4 2-6l1-1c1-1 2-6 3-7 1-2 3-4 4-7 2-2 3-5 4-8h0 1c1-2 1-3 2-5l5-15 4-9c1-2 2-2 2-4 0-1 1-3 1-4 3-4 5-8 8-12h1s1 0 1-1c1 0 1-1 2-2 2-3 5-6 7-8l1-1c1-1 5-3 5-5h0c5-4 10-7 15-10h-1c3-1 5-2 8-4l10-4c3-1 5-4 6-7v-4c1 1 1 2 2 3 1-3 1-6 0-9l1-1 1-1h0c1-5-4-10-6-14h0c-1-2-2-3-3-5h1c-1 0-1 0-1-1s-5-3-5-4h-1 0v1h-1v-2h-1 1l3-2c1-1 1-1 2-1l-1-1h-2c0-1-1-1-2-1 0-1-2-1-2-1h-1l1-1h0l-3-2h-1v-1h1c1-1 2-1 3-1s1-1 2-1v-1h6c2 0 2 0 4-1h1c1-1 1-1 1-2l1-1h1l3-1h2c1 0 1-1 1-1h1c1 0 1-1 1-1h2 1c1 0 2 0 3-1l-1-1c-1 0-1 0-2-1h0v-1c-1-1-2 0-3 0h-3v1h-1l-1-1-3-1c-1-1-2-1-4-1l1-1h2c2 0 5-1 7-2h1v-1c1 0 3-1 4-1 1-1 1-1 2-1v-1h0c-1-1 0-1 1-2h-3c1-1 3-1 4-1h1c1 0 1-1 2-1h1s0-1 1-2h-3-1c1-2 2-2 2-3h0l1-1v-1l1 1h0c3-1 4-6 5-8h0c1-1 1-2 1-3 1 1 1 1 2 1h0l1 3h0c0 2 1 3 2 4 0 1 1 1 1 2 0 0 0 1 1 1v-2l1-3v-5l1-1c1 0 1-3 2-4h1c0-1 0-2 1-3 0 0 0-1 1-2h0c1-2 2-5 3-8v-1c0-1 0-1 1-2 0-1 1-3 2-4z"></path><path d="M230 469c0 1 1 1 2 1-1 1-2 1-3 1v-1l1-1z" class="D"></path><path d="M514 417h3l-1 2h0l-1-1s-1 0-1-1z" class="C"></path><path d="M260 579l2-1 1 1v2h0c-1-1-2-1-3-2z" class="T"></path><path d="M184 421c0-1 1-1 2-2l1 2h-3z" class="K"></path><path d="M491 327s2 1 3 0c-1 1-1 2-2 2l-2-1 1-1z" class="N"></path><path d="M351 626l2 1c-2 1-3 2-4 2 0-1 1-1 2-3z" class="F"></path><path d="M279 203l3 3h-1-1-1 0v-3z" class="B"></path><path d="M245 525l3 2 1 1h0l-3-1-1-2z" class="L"></path><path d="M227 259l1-2v3c-1 1-1 3-2 4v-1h0v-2h0c1-1 1-2 1-2z" class="Y"></path><path d="M322 126c0 1 1 1 1 2-2 0-3 1-5 0 1-1 3-1 4-2z" class="O"></path><path d="M190 379c-1 0-2 0-3-1 0-1 1-1 1-1l2 1v1z" class="J"></path><path d="M465 179l2-2 1 1h0 2c-2 1-2 2-4 1h-1z" class="F"></path><path d="M464 572l2 2h-2 0-1l-1-2h2z" class="Z"></path><path d="M284 209l2 1-2 2v2l-1-2v-2l1-1z" class="O"></path><path d="M222 469l1-1c0 2 1 3 1 5h-1c0-1-1-1-1-2v-2z" class="L"></path><path d="M149 565l1 1h0c-1 1-1 2-1 3-1 0-2 0-2-2 0-1 1-1 2-2z" class="g"></path><path d="M115 541c0 1 1 3 1 4l-1 1s-1 0-2-1l2-2v-2z" class="b"></path><path d="M307 509l1-2c1-1 1-1 2-1l-1 5-1-1-1-1z" class="E"></path><path d="M227 222c1 0 1-1 2-1h0c1 2 1 4 0 5l-2-4z" class="d"></path><path d="M241 523h1l1 1 2 1 1 2-3-1c-1-1-1-2-2-3z" class="c"></path><path d="M311 320c1-1 2-2 2-3s-1-1 0-2v1c1 1 2 1 2 3h-1c-1 0-2 1-2 1h-1z" class="g"></path><path d="M470 466l1-1v1h0v3h0v3h-1v-1-5z" class="T"></path><path d="M280 417v-7h1c1 2 0 4 0 6l-1 1z" class="b"></path><path d="M479 414c1 1 2 1 3 2h0v2c-1 0-2-1-3-2v-2z" class="I"></path><path d="M304 447c1 1 1 1 1 2s-1 2-2 3h-2c1-1 2-2 2-4l1-1z" class="d"></path><path d="M154 385c1-1 1-1 2-1 1 1 1 3 1 4l-1-1c0-1-1-1-2-1v2-3z" class="N"></path><path d="M483 530c0 2 1 4 1 5l-2-2-1 1h0v-2-1c1 1 1 0 1 0h1v-1z" class="B"></path><path d="M414 631l7 1h0-2-2v1c-2 0-3-1-4-1 0-1 1-1 1-1zm80-491l2 5h-1c-1 0-1-1-2-2v-1c1-1 1-1 1-2z" class="F"></path><path d="M295 327c0 1 1 2 1 2 0 1 1 2 1 3h0v1h-1-1v-6z" class="O"></path><path d="M107 412l1-2 1 1-1 7h0-1v-6z" class="I"></path><path d="M194 617c0-1-1-3 0-5h1v1l1 1c0 1 0 1-1 1 0 1 0 2-1 2z" class="J"></path><path d="M485 579l1 5h-2c0-1 0-1-1-1l-1 1h0l-1-1c1-1 1-1 2-1h1c1-1 1-2 1-3z" class="M"></path><path d="M295 547c0 2 0 3 2 5l-2 2v-1c-1-2-1-4 0-6z" class="E"></path><path d="M500 402h0c0-1-1-3 0-4v-1l2 3v1l-1 1h0-1z" class="B"></path><path d="M163 410c-1-2-1-3 0-5h1c1 1 1 3 0 5l-1-1v1z" class="F"></path><path d="M112 413c0 1 1 1 1 1l-1 2v2 4h-1v-3c0-2 0-4 1-6z" class="T"></path><path d="M502 400c1 1 2 2 2 3h0v2 1c-1-1-1-2-2-2 0-1 0-2-1-2h0l1-1v-1z" class="C"></path><path d="M562 347h4c-1 2-1 2-2 3l-2-2v-1zm-98 165h8l-2 2h0c-2-1-3-1-5-1h-1v-1z" class="E"></path><path d="M151 395c0-1 1-1 1-2l1 3v2l-1-1v1l-2-1h0c1-1 1-1 1-2z" class="L"></path><path d="M220 467c0-2 1-3 2-5h0l1 6-1 1-1-1v-1h-1z" class="B"></path><path d="M484 189v1h2v3l-1 2-1 1h-1c1-1 2-3 2-4-1-1-1-2-2-2l1-1z" class="R"></path><path d="M322 543v4s0 1 1 2l-1 1-1-1-1-4 2-2z" class="X"></path><path d="M321 549l1 1 1-1c0 1 0 2 1 3-1 1-1 1-2 1v2 1c-1-2-1-5-1-7z" class="P"></path><path d="M472 512l3 1 2 1h0c-2 1-5 0-7 0l2-2z" class="F"></path><path d="M216 347l2 1h0v1h0v1s-1 1-2 1c-1-1-2-1-2-2l2-2z" class="B"></path><path d="M230 367l1-1v3c-1 1-1 1-2 1v-1c-1 0-2-1-2-1h0l3-1h0z" class="O"></path><path d="M121 387c0-1 1-2 1-3 1 0 1 0 2 1l-1 1c-1 1-1 3-2 4v-3z" class="C"></path><path d="M479 186v-1l1 1h1c1 1 1 2 1 3h0c-1 0-1 1-3 1v-4z" class="R"></path><path d="M276 527h1c2-2 3-2 6-1-2 1-3 1-5 1l-1 2-1-1v-1z" class="H"></path><path d="M224 538c-2-1-3-1-4-2s-1-2 0-3l1-1c0 3 2 4 3 6z" class="B"></path><path d="M521 538c1-1 1-2 1-3l1 1c0 3-1 6-1 9 0-1 0-2-1-3v-4z" class="D"></path><path d="M101 555l2 1v6-1c-1-2-1-4-2-5v-1z" class="g"></path><path d="M292 224v-1h1l1 1h1c0-1 1-1 2-1v3c-2 0-4-1-5-2z" class="C"></path><path d="M317 447h-1v1 1c-1-1-1-1-1-2s0-2 1-3h0c1-1 1-2 1-2v5z" class="f"></path><path d="M512 417h2c0 1 1 1 1 1l1 1h0l-1 3c0-1-1-2-1-3l-1-1v1h-1v-1h0v-1z" class="B"></path><path d="M487 335c2 0 5 3 7 4h-3 0l-4-4z" class="c"></path><path d="M279 206h1 1 1l2 3-1 1v2l-2-2 1-1c-1-1-2-2-2-3h-1z" class="N"></path><path d="M504 411v-2h1c0 1 1 2 1 2 1 0 2 0 2-1l1 1c-1 1-2 2-4 2-1-1-1-1-1-2z" class="g"></path><path d="M145 396c0 1 1 1 2 1h2 0 1c0 1-1 2-2 2h-3c-1-2-1-2 0-3z" class="H"></path><path d="M297 328c1 1 2 2 2 4-1 1-2 2-4 2h-1c0-1 0-1 1-1h1 1v-1h0v-4z" class="C"></path><path d="M292 224c1 1 3 2 5 2 0 1 0 1-1 2h0c-2-1-4-2-5-3h1 1-1v-1z" class="Z"></path><path d="M496 557l1 1-1 1-4 7v-2l2-3s0-1 1-1c0-1-1-2-1-2l2-1z" class="C"></path><path d="M476 181c1 0 1 0 3 1 0 1 1 3 2 3v1h-1l-1-1v1c-1-2-1-3-3-5z" class="G"></path><path d="M252 346l1 1 1 1c0 2-2 3-1 5l-2-2 1-1-1-1c0-1 0-1 1-2v-1z" class="D"></path><path d="M227 293h0c1-1 1-2 1-3s0-1 1-2h1v1 1 3h-1c-1 1-1 0-2 0z" class="C"></path><path d="M248 421l1-1v1c1 1 1 2 2 2v1 2c0 1 0 1-1 2v-1l-2-6z" class="H"></path><path d="M287 433c-1-2-2-3-1-5 0-1 0-1 1-1h1v1 3c0 1-1 1-1 2z" class="K"></path><path d="M479 118c1 1 3 3 4 5l-1 1-1-1-3-4 1-1z" class="Z"></path><path d="M518 140l2 1s1 0 1 1c-1 1-2 1-4 1l-2-2c1 0 1 0 3-1z" class="O"></path><path d="M262 369h0 1c1 1 4 2 6 2h1v1c-3 0-6-1-8-3z" class="R"></path><path d="M515 328c3-1 5-1 8 0l-1 1h-6l-1-1z" class="V"></path><path d="M486 335h1l4 4v2h-1l-1-1h0l-3-3v-2z" class="B"></path><path d="M489 176h1v1h0c1 0 2-2 3-2h1c-1 2-2 3-4 4h-1c-1-1-1-2-1-3h1z" class="N"></path><path d="M437 599c2 1 4 3 7 3h1c-1 0-3 0-4 1-1 0-2 0-3-1h0c0-1-1-2-2-3h1z" class="C"></path><path d="M463 433l1-1v-5h1c0 2-1 3 0 5l1 1v2h0-2c0-1 0-2-1-2z" class="D"></path><path d="M478 526l1-1 1 1c1 1 1 1 2 1 0 1 1 2 1 3v1h-1c0-2-2-4-4-5h0z" class="E"></path><path d="M195 363l9-2v2h-2v1c-2 0-5-1-7-1z" class="R"></path><path d="M235 175h1c-1 3-2 6-4 10v-5l1-1c1 0 1-3 2-4z" class="b"></path><path d="M299 593h0c0 2-1 5 0 7l2 2v-1c1 0 1 1 1 1h1l-1 1h-3c-1-3 0-7 0-10z" class="C"></path><path d="M260 151v5c1 2 1 4 1 6l1 2-2-2c-1-3-1-8 0-11z" class="P"></path><path d="M215 538c2 0 4 1 7 1h-3l2 2h-3-1l-2-3z" class="O"></path><path d="M260 579c1 1 2 1 3 2h0l-2 2c-2 0-4-1-5-1 0-1 0-1 1-1h2c0-1 1-1 1-2z" class="H"></path><path d="M205 328h2c1 1 0 2-1 2-1 1-2 3-4 3h-1c2-1 2-1 2-2 1-1 2-2 2-3z" class="D"></path><path d="M466 433v-7c1 2 1 6 1 9v1c-1 0-3 0-3-1h0 2 0v-2z" class="B"></path><path d="M304 438l1 2c0 2 0 5-1 7l-1 1h0l1-4v-5-1z" class="K"></path><path d="M517 430c2-2 3-3 5-3 1 0 2 1 3 2-1 0-1 0-2 1h0c-1-1-1-1-3-1-1 0-1 1-2 2l-1-1z" class="N"></path><path d="M255 607c1 1 2 1 4 2 1 1 1 1 2 3l-7-3v-1l1-1z" class="B"></path><path d="M540 338c1-2 2-4 3-5l1 1-2 5-2 2v-3z" class="P"></path><path d="M280 632h0 7c-2 1-4 1-6 2-1 0-2 0-3-1v-1h2z" class="R"></path><path d="M163 509l1 1-1 1v6l-1 2v-1c-1-1-1-4 0-6 0-1 0-2 1-3z" class="V"></path><path d="M248 527h1 3c1 0 2 0 4 1h2c-3 1-6 1-9 0h0l-1-1z" class="K"></path><path d="M100 560v-4h1c1 1 1 3 2 5l-1 1-1 1c0-1-1-1-1-2v-1z" class="Y"></path><path d="M501 402c1 0 1 1 1 2 1 0 1 1 2 2l1 1v2h-1v2c-1-1-1-2-1-4s-1-3-3-5h1z" class="I"></path><path d="M173 562l1-1c1 0 1 1 2 1h0c2 2 3 4 5 6-3-1-6-4-8-6z" class="J"></path><path d="M234 227c0 1 1 1 1 2 1 1 3 1 5 1l-1 1h1 0-3c-1 0-3 0-4-2 0-1 0-1 1-2z" class="G"></path><path d="M161 375l1-1-1-3h0c2 1 3 3 4 5h0-1v2c-1 0-1 0-2-1v1c-1-1 0-2-1-3z" class="D"></path><path d="M471 469l1 4c-1 3 0 4 1 6l-1 1c-2-3-2-6-2-9v1h1v-3z" class="U"></path><path d="M487 544c1-2 4-5 6-5h4v1h0c-2 0-4 0-5 1h-1c-1 1-3 2-4 4v-1z" class="W"></path><path d="M457 103h1c1 2 2 5 2 7-1 1-2 1-2 2 0-2 0-4-1-6v-3z" class="S"></path><path d="M186 585h3l2 1v1l-10 2h0v-1c2 0 4-1 6-1v-1-1h-1z" class="J"></path><path d="M293 390c1-2 2-4 4-4h1c0 2-2 4-3 6h-1c0-1 1-2 2-3l-1-1-1 2h-1z" class="W"></path><path d="M293 322c1 1 4 4 4 6v4c0-1-1-2-1-3 0 0-1-1-1-2 0 0 0-1-1-1v-1h0c-1-1-2-2-2-3h1z" class="G"></path><path d="M196 614l1 1 2-1h0c0 1 0 1 1 1l-1 1h0l-1 1h0c1 0 0 0 1 1h-4s-1 0-1-1c1 0 1-1 1-2 1 0 1 0 1-1z" class="H"></path><path d="M469 533c4 2 5 5 7 8h-2 0c-1-1-1-2-1-3h0c-1-1-2-3-4-4v-1z" class="G"></path><path d="M301 440c1 1 2 2 2 3-1 2-1 4-2 5l-1-1c1-2 1-3 1-6v-1z" class="B"></path><path d="M177 399c0 4 0 7-1 11h-1v-7h0c1-2 1-3 2-4z" class="U"></path><path d="M481 512c1 1 1 1 2 1h1 3v1c-3 1-5 1-8 1l-2-1c2-1 2-1 3-1l1-1z" class="D"></path><path d="M460 475l2 9h0 0c-1-1-2-3-3-4v-3l-1-1s0-1 1-1v1l1-1zM176 590c1 0 3-1 4-2 0-1 1-1 2-1h1c1-1 2-1 4-1v1c-2 0-4 1-6 1v1h0l-3 2c-1 0-2 0-2-1h0z" class="B"></path><path d="M317 600h1v-1l2-2v1h0c0 2-1 3 0 4v2c-1 0-3-1-4-1v-1h1v-2z" class="V"></path><path d="M472 392c3-1 8-1 11-1 0 1 1 1 2 1h-1c-1 0-2 1-4 1s-5-1-8-1z" class="S"></path><path d="M222 431c1 2 1 4 2 6 0 2-1 2-1 2-1 1-1 2-1 3 0-3-1-9 0-11z" class="L"></path><path d="M280 417l1-1c1 5 3 10 2 14v-1h0c0-1 0-3-1-4v-1h0l-1-1v-1l-1-1v-4z" class="K"></path><path d="M125 392c2-2 4-4 7-5l1 1-1 1h2c-4 1-5 3-9 4v-1z" class="S"></path><path d="M538 338c1 0 1-1 1-1l1 1v3l-3 4c0-2 0-2 1-4-1-1 0-2 0-2v-1z" class="d"></path><path d="M306 513c0 1 0 2-1 3l1 1-1 3-1-1v-1h0l-2 2v-1c0-2 1-3 2-5v1l2-2z" class="E"></path><path d="M303 371c3-1 7-2 10 0-3 1-7 1-11 1l1-1z" class="C"></path><path d="M321 639h2v-1c2 1 5 1 6 2s3 1 4 1l-1 1c-1 0-2 0-2-1-3 0-6-1-9-2z" class="K"></path><path d="M406 343h0c1 1 2 3 3 4h1s0 1 1 1h0v1h-1c-1 0-2 0-3-1s-1-3-1-5z" class="H"></path><path d="M338 91c4-1 10-1 14 0h-5c0 1 0 0 1 1h-7-2-1 1v-1h-1 0z" class="a"></path><path d="M285 588h0c0 2-1 4-2 5-1 3-1 6 0 9h-1l-1-3c0-5 2-7 4-11z" class="E"></path><path d="M276 373c2 0 3-1 4-1 1 1 2 1 3 1h1v1l-2 1h-6v-2z" class="c"></path><path d="M307 137l1-1 3 2c3 2 8 0 11 2h-2c-5 1-9 0-13-3z" class="G"></path><path d="M498 526v-1c-1-2-2-4-2-6 1-1 1-1 2-1s3 1 4 1c-1 1-1 1-2 1h-1c0-1 0-1-1-1h0l-1 1h1v2h1l-1 1c0 2 1 1 1 2l-1 1z" class="V"></path><path d="M323 102h0-2-1v-1c2-2 5-3 8-4 0 1-1 2-2 3h-1l-1 1h-1v1z" class="D"></path><path d="M401 338h1c0 2-1 3 0 5 0 1-1 2 0 3h1 0v-1l1-1c1 1 1 2 1 3h-3l-1-1c-2-2-1-6 0-8z" class="H"></path><path d="M471 112c-1-4-3-7-5-10l1-1 6 9v2c-1-1-1-1-2-1v1z" class="N"></path><path d="M111 537l-1-1h0 3c1 1 2 3 2 5v2l-2-1v-1-2l-2-3v1z" class="B"></path><path d="M473 110l6 8-1 1c-2-1-3-2-4-4l-1-3v-2zm9 73s0 1 1 2c1 2 2 3 3 5h-2v-1l-1 1-1-1h0c0-1 0-2-1-3v-1h1v-2z" class="K"></path><path d="M482 185l2 4-1 1-1-1h0c0-1 0-2-1-3v-1h1z" class="H"></path><path d="M512 417v1h0v1h1v-1l1 1c0 1 1 2 1 3s0 0-1 1c-1-1-2-1-3-2s-1-2 0-3c0-1 0-1 1-1z" class="C"></path><path d="M518 354s1-1 2-1c3 1 5 1 8 1h1l-4 1c-2 0-3 1-4 1-1-1-2-1-3-2z" class="L"></path><path d="M505 407c2 1 3 1 4 1h3c0 1 0 1-1 1 0 1-1 2-2 2l-1-1c0 1-1 1-2 1 0 0-1-1-1-2v-2z" class="C"></path><path d="M188 377c2-1 3-1 4-1 3 1 6 1 9 3h-2l-2-1h-1l-1 1-5-1-2-1z" class="D"></path><path d="M210 344c1-1 2-1 4-2 1 0 2-2 4-2-3 2-5 5-8 6h-1s-1 0-1 1l-1-1h1c1 0 2-1 2-2z" class="L"></path><path d="M221 455c0 1 1 1 1 2v2 3c-1 2-2 3-2 5h0c0-1 0-1-1-2l2-10z" class="C"></path><path d="M466 518h1 3c2 1 5 2 6 4l1 1v1h-1c-3-3-6-4-10-6zM294 326c0 1 0 4-1 5 0 1-1 1-2 1s-3-1-5-2h0c2-1 3-1 5-1v1l-1 1h1 1c1-2 1-4 1-6h0 1v1z" class="B"></path><path d="M130 411l1 1c4 1 7 1 11-1 2 0 3-1 5-1-5 3-9 5-14 3-2 0-3 0-4-1l1-1z" class="C"></path><path d="M459 480c1 1 2 3 3 4h0 0c1 1 1 2 1 3-1 1-1 2-1 3v1h0v2l-1-2c1 0 0-1 0-2v1-1c0-1 0-1-1-2 0-2 0-5-1-7z" class="K"></path><path d="M538 338c1-3 1-7 3-9 1 0 1-1 3-1v1l-5 8s0 1-1 1z" class="R"></path><path d="M251 599h2v1l-1 1h0 1v1l1 1c0-1 0-2 1-2v1c0 1 0 1-1 2h-1c-2-1-3-1-4-3 1-2 1-1 2-2z" class="W"></path><path d="M516 351c3 1 6 0 9 0l1 1-6 1c-1 0-2 1-2 1l-3-1c1-1 1-1 1-2z" class="G"></path><path d="M330 425h1 0c1 0 1 0 1-1 1 3 2 7 3 10-1 1-1 1-1 2l-3-9v-1l-1-1z" class="h"></path><path d="M283 526c2 1 5 2 7 5h-1l-1-1c-1 0-1 0-2-1h0s-1 0-1-1h0-1-1-2-1c-1 0-1 1-3 1h0l1-2c2 0 3 0 5-1z" class="F"></path><path d="M493 555h3c0 1 1 1 1 2l2 2v2h-1-1c0 2-2 4-2 5l-1 1h-1c0-2 3-6 4-8h-1l1-1-1-1c-1-1-2-1-3-2z" class="G"></path><path d="M165 558l-3-3c-1 0-1-1-1-2l3-2 3 5h-1-1v1 1z" class="F"></path><path d="M438 123c0 3 2 4 2 6h-4s0-1-1-1c1-2 2-3 3-5z" class="D"></path><path d="M365 111c0 2 1 4-1 7h0c-1 1-2 2-2 3h0c0 1 0 1 1 1l-6 2c1-1 2-2 4-3 2-3 3-5 3-8v-1c0-1 0-1 1-1z" class="T"></path><path d="M532 356c3 1 4 1 6 3h1l-1 1s-1 1-1 2c-2-1-4-3-5-4v-2z" class="J"></path><path d="M312 121c1 0 2 1 3 0 0 0-1-1 0-1l1 1c1 1 1 2 2 4h1l-1 1c-1 0-1-1-2-2v1 1c-1-1-2-1-3-2l-1-3z" class="F"></path><path d="M519 546h0c0-1 1-2 1-3v6c0 4-1 8-3 11 0-1 1-3 1-5l1-9z" class="T"></path><path d="M149 577c2-1 4-1 6-1h10l1 1h0c-5 0-10 0-15 1-1 0-1-1-2-1z" class="U"></path><path d="M528 367h1c1 0 3 1 4 2h0 1v-1c-3-2-7-3-10-5h0 1c4 2 8 3 10 5 0 1 1 2 0 3h0c-2-2-5-3-7-4z" class="C"></path><path d="M485 592l1 2c-1 5-2 9-5 13v-1l2-7c1-1 2-3 2-4v-3z" class="E"></path><path d="M248 603c4 1 8 3 12 6h-1c-2-1-3-1-4-2-3-1-5-2-8-3 1-1 0-1 1-1z" class="g"></path><path d="M288 407l1-2h2c-2 4-2 6-1 10h-1-1 0c-1-1-1-3-1-4 1-1 1-3 1-4z" class="F"></path><path d="M215 388l1 1c1 2 1 6 2 7s1 1 1 2h-1v5l1 4h-1c0-1 0-4-1-5 0-5-2-9-2-14z" class="Z"></path><path d="M509 342c1 4 2 6 5 8 1 1 1 1 2 1 0 1 0 1-1 2l-2-1c-3-2-4-4-5-8 0 1 0 1 1 1v1-4z" class="H"></path><path d="M481 534l1-1 2 2c-1 4-2 10-3 13h-1c0-4 2-9 1-14z" class="F"></path><path d="M130 536v1c0 2 1 2 2 3h0c0 2 0 3 2 5h1l1 2h-1l-2-2c-2-1-4-5-4-6-1-2 0-2 1-3z" class="I"></path><path d="M263 450c3 0 6 0 9-1l1 1h0l1 1h3c-1 0-2 1-3 1h-8l-3-2z" class="S"></path><path d="M273 450h0l1 1h3c-1 0-2 1-3 1l-4-1h0l3-1z" class="O"></path><path d="M121 390l-1 5-1-6c0-2 0-7 1-8 1 0 1 0 1 1v3 2 3zm74 222h4c2 0 4 0 5-1v2c-1 1-2 1-4 1h-1l-2 1-1-1-1-1v-1z" class="N"></path><path d="M297 372c2 0 3-1 5-2 1-1 3-2 5-2v1c-2 1-3 1-5 2h1l-1 1c-2 1-4 1-6 2-1 0-3 1-4 1h-2l1-1c2 0 4-1 6-2z" class="G"></path><path d="M487 562c0-1 1-1 2-1v1h1v1 1c1 0 2-2 3-3h1l-2 3v2 1h-1c-2-2-3-2-4-5z" class="K"></path><path d="M290 415c1 3 2 5 4 8-1 1 0 1 0 2h0c-2-2-4-4-5-7l-1-3h0 1 1z" class="Q"></path><path d="M321 620v1c-5 2-11 3-17 2h0v-1h1c1 0 3 1 4 0 4 0 8-1 12-2z" class="W"></path><path d="M174 331c3 3 7 4 11 6v1c1 0 1 0 1 1-5-1-10-3-13-7l1-1z" class="I"></path><path d="M155 574c4 0 9 0 13 1l-1 1h-2-10 6c-1-1-7-1-9-1 2-1 4 0 6 0v-1h-3z" class="G"></path><path d="M310 490l1-1v1c1 1 1 1 2 1l-2 9v-4h0c0 1-1 2-1 3h-1c0-1 1-2 1-3s0-1-1-2c1-1 1-3 1-4z" class="N"></path><path d="M310 490l1-1v1c0 2 0 4-1 6 0-1 0-1-1-2 1-1 1-3 1-4z" class="F"></path><path d="M388 110c2 0 4 1 5 3h0 0-3-4-2c-1 0-1-1-2-2 2 0 4 0 7 1h0v-1h-1l-2-1h2z" class="L"></path><path d="M176 590c0 1 1 1 2 1-2 1-4 2-5 3-1 2-1 3-3 5v-1l-1-1 1-1v-1-1h0v-1c2 0 4-2 6-3z" class="K"></path><path d="M286 211c2 0 4 2 7 2-2 2-3 3-3 5v3l-1-1v-3-1-1c0-1-2-2-3-2v-2z" class="D"></path><path d="M112 413l1-1h0s1 1 1 2v2c0 2 1 5 1 7h-1v3 3c0-2 0-3-1-5v1-8-1h-1l1-2s-1 0-1-1z" class="V"></path><path d="M285 316v-1c-1-1-3-1-4-2h0l1-1c1 1 2 1 3 2 1 0 1 0 1 1 2 1 3 1 4 3 1 0 1 1 1 1 1 1 1 2 2 3h-1c-1-1-2-2-4-3l-3-3z" class="D"></path><path d="M227 195l1 1c0 1 0 1-1 1l1 1v-1c1 1 0 1 1 2 0 1-1 1-1 1l1 2h-1v1l-3-3-2-2 2-1 2-2z" class="M"></path><path d="M483 434h1c1 1 3 1 3 2v1l1 1c-1 0-1 0-1 1l-1 1c-2 0-2 0-2-1v-1s-2 0-3-1h0c1-1 2-1 2-3z" class="k"></path><path d="M134 407c-1-1-2-2-2-3-1-2-1-3-1-4 1-2 2-3 3-4 2 1 3 2 4 3h-3c-1 1-2 1-2 3v1c0 2 2 3 1 4z" class="D"></path><path d="M111 520v-3c1-5 5-9 9-11v1l-1 1c-1 0-3 2-3 3l-2 2-1 3c-1 2-1 3-2 4z" class="U"></path><path d="M206 361c5-3 9-5 13-8h1c-1 1-2 1-2 2v1c-1 1-5 3-6 4-1 0-2 1-3 1-1-1-2 0-3 0z" class="B"></path><path d="M285 180h0c-1 1-3 2-3 3v1 2l-1-1v-1c-1 1-2 2-2 3-1 2 0 4-1 6-1-3-1-6 0-8 1-3 4-4 7-5z" class="K"></path><path d="M258 569s-1-1-2-1c-1-1-1-1-1-2-1-1 0-1-1-1v-1-1l-1-1v-1c3 2 6 4 7 6v1l-2 1z" class="C"></path><path d="M250 415c3 2 8 3 9 6l-1 1-1-2v1c-1 1-2 1-3 1 0-1 0-2-1-3h0c-1-1-2-1-3-2v-2z" class="G"></path><path d="M253 419c2 0 2 1 4 2-1 1-2 1-3 1 0-1 0-2-1-3z" class="B"></path><path d="M523 380l1-1c3 6 5 13 5 20l-1-2v-1l-1 2h0v-7l-4-11z" class="W"></path><path d="M515 342c0-1 0-1 1-2 1 0 2 1 2 2 1 1 1 3 3 3 1 1 2 2 2 3-1 0-1 0-2-1-3-1-5-3-6-5z" class="H"></path><path d="M168 387h0c-1-1-2-8-2-8l1-1c1 1 1 2 2 3v1 1l2 2v1h-1c-1 0-1 1-2 1z" class="B"></path><path d="M170 327c1 0-1-4-1-5-2-4-2-6-1-10 0 1 1 2 1 3 0 3 1 7 3 10l-1 3-1-1zm-35 75l2 3-1 1h4v1c1 0 1 0 2 1h-1-1l-1 1h1v1h-1c-2-1-4-2-5-3 1-1-1-2-1-4l2 2h1c-1-1-1-1-1-2v-1z" class="K"></path><path d="M218 370s-1-1-2-1c-1-1-2-1-2-2l1-1 4 3c4 3 9 5 12 9-1 0-3-1-4-2v-1l-1-1h-1c-1 0-2 0-2-1 0 0 0-1-1-1h-1c-1-1-2-1-3-2z" class="R"></path><path d="M230 189s0 1 1 1c-1 1-1 2-1 3-1 1-1 1-2 1v-1-1-1h-1v1l-2 3h1 1l-2 2-2 1-2-1v-1c1 0 2 0 2-1 1-1 3-2 4-4 0-1 0-1 1-2l1 1h0l1-1z" class="Y"></path><path d="M230 189s0 1 1 1c-1 1-1 2-1 3l-1-1v-2h0l1-1z" class="K"></path><path d="M277 626l2 1h2 2c1 0 0 0 1 1h1 1 1 0 1 1 1c0-1 1 0 1 0-1 1-2 1-4 1-1 1-4 1-5 1s-3-1-4-2c0-1 0-1-1-2z" class="D"></path><path d="M220 523v-1h0c2 1 0 2 1 4h0c2-1 1-3 1-4v-1c2 2 2 4 1 6 0 1-1 2-2 2h-1v-2c-1 1-1 2-2 2 0-2 1-4 2-6z" class="V"></path><path d="M297 424c-1-2 1-8 2-10 0-1 1-2 1-2l2-2h-1-1-1v-1c1-1 1-1 2-1s2 0 2 1c0 2-2 3-3 5s-2 7-3 10h0z" class="I"></path><path d="M249 421h1c0-1 0-2-1-3l1-1c1 1 2 1 3 2h0c1 1 1 2 1 3-1 2-2 3-3 4v-2-1c-1 0-1-1-2-2z" class="Q"></path><path d="M194 421v-6c0-2-1-4-1-5h2v1 3l1 1v1h-1c0 2 0 4 2 6-1 1-1 2-2 4 0-2 0-3-1-5z" class="R"></path><path d="M452 96c1 1 3 3 5 4 0-1-1-2-1-3 2 1 4 2 4 4h0v1c1 0 2 1 2 2s1 1 1 2l-1 1c-2-2-2-4-3-6h-1v2h-1c-1-2-2-3-4-5l-1-1v-1z" class="M"></path><path d="M244 585l1 1c1 1 1 2 1 3l-2 2h1c-1 1-1 2-1 2l-5 2c1-2 4-3 5-5l-1-1c-1 2-2 3-3 4l-1-1h1l1-1c1-1 1-2 1-3v-1c1 0 1-1 2-1v-1z" class="J"></path><path d="M401 630c2 1 4 1 6 2 1 0 3 1 4 2-1 0-2 0-2 1h-3v-1h0c-1-1-1-1-2-1-2 0-4-1-6-2 1-1 2-1 3-1z" class="O"></path><path d="M251 590c1 1 1 1 0 2 0 2-1 3-2 3-1 1-2 1-3 0-1 0-2-1-2-2 0 0 0-1 1-2h1l1 1h2 1c0-1 0-2 1-2h0z" class="F"></path><path d="M170 427h2c4-2 8-6 11-10v1c-4 6-10 11-17 12l-1 1 1-2c1 0 1 0 2-1l2-1z" class="R"></path><path d="M277 537c0 1 0 1 1 2-2 2-4 4-5 7h-1v-1h-1c0 1-1 1-1 2v1 1l-1-1c1-1 1-2 2-3 1-4 4-6 6-8z" class="I"></path><path d="M240 498l-1-2c-2-7-1-12 3-18v3c0 1-1 2-1 3-1 1-1 2-1 3v1c-1 3 0 7 0 10z" class="C"></path><path d="M386 625c1-1 1-1 3-1 1 1 2 1 3 2l7 3c1 0 1 1 2 1-1 0-2 0-3 1l-3-2c-3-2-6-3-9-4z" class="D"></path><path d="M395 629h2 2c1 0 1 1 2 1-1 0-2 0-3 1l-3-2z" class="K"></path><defs><linearGradient id="L" x1="260.27" y1="524.372" x2="251.953" y2="527.67" xlink:href="#B"><stop offset="0" stop-color="#4a484c"></stop><stop offset="1" stop-color="#6e6c6c"></stop></linearGradient></defs><path fill="url(#L)" d="M252 525h1c3 1 5 1 8 1 2 0 4-1 6 0-1 0-3 1-4 2v-1l-5 1h-2c-2-1-3-1-4-1h0l-1-1 1-1z"></path><path d="M271 553c-2 1 0 3-1 5l-1-1v-1-2h0l-2-2v-1-2-1-1h0c1-1 1-2 2-3v1 1c-1 0 0 2 0 3v-1l1 1c0 1 0 3 1 4z" class="P"></path><path d="M221 380c3 1 5 3 7 5 2 1 4 2 5 3l-7-2c0-1-1-1-2-1h0l-2-2v-1c-1-1-2-1-2-2h1z" class="G"></path><path d="M483 123c4 5 9 11 11 17 0 1 0 1-1 2-2-7-6-12-10-18h-1l1-1z" class="U"></path><path d="M222 292h0l1-1c1-1 1-2 2-3h0c0-1 1-3 2-3h0v2c-1 1-1 1-1 2h0v1c-2 2-3 7-6 8 0-1 1-4 1-5 0 0 0-1 1-1z" class="N"></path><path d="M508 417h1l2 1c-1 1-1 2 0 3s2 1 3 2h1v1h-2 0-2v1h0l-1-1c-2-2-3-3-3-6l1-1z" class="H"></path><path d="M508 417h1l2 1c-1 1-1 2 0 3s2 1 3 2h1v1h-2s-1 0-1-1c-1 0-3-3-4-4v-1-1z" class="P"></path><path d="M155 393l1 1c-1 5-3 11-8 14h-1 0c1-1 1-1 1-2 2-1 3-2 3-4h0c1-2 1-3 2-4v-2l1-1c1 0 1-1 1-2z" class="E"></path><path d="M479 414c1-4 3-10 6-13l-2 6s-1 1-1 2-1 1-1 2h1c0-1 1-2 1-3h1l-2 8h0c-1-1-2-1-3-2z" class="U"></path><path d="M228 260c0 3 1 5 2 8-2 2-4 4-6 5l1-2c0-2 1-4 2-6l-1-1h0c1-1 1-3 2-4z" class="N"></path><path d="M168 328c1 0 2-1 2-1l1 1 3 3-1 1-1-1c1 3 4 6 7 8h1v1c-2-1-4-2-6-4l-2-2h-1c0-1-1-2-1-4v-1h-1c-1 0-1 0-1-1z" class="B"></path><path d="M224 473l1 9v8h-1c-1-4-1-8-1-12-1 2-1 2-1 4v-5c0-1 0-2 1-3v-1h1z" class="F"></path><path d="M223 474v4c-1 2-1 2-1 4v-5c0-1 0-2 1-3z" class="S"></path><path d="M276 527v1l-1 1c0 1 0 1 1 2l-1 2c0 1 0 2 1 2v2c-1-1-1-1-3-2 0 1 0 1-1 1h-1l1-3c0-3 2-5 4-6z" class="W"></path><path d="M289 405c1-2 3-4 5-4 2-1 4-1 6 0l2 1-2 3h0 0l-3-3c-2 0-4 0-5 2-1 0-1 1-1 1h-2z" class="I"></path><path d="M311 223c-2-2-5-5-7-5h-1c-3-1-5 0-8 1 1-1 2-1 3-2 2-1 6-1 8 0 3 1 5 3 6 5l-1 1zM204 613l2-2c2 0 3-1 5 0-2 1-3 3-5 4-1-1-1-1-2 0-1 0-2 1-2 2h0l-3 1c-1-1 0-1-1-1h0l1-1h0l1-1c-1 0-1 0-1-1h0 1c2 0 3 0 4-1z" class="Q"></path><path d="M154 388v-2c1 0 2 0 2 1l1 1v2l-1 4-1-1c0 1 0 2-1 2l-1 1-1-3 1-1c0-1 0-3 1-4z" class="R"></path><path d="M155 393c-1 0-1 0-1-1 1-1 1-2 3-2l-1 4-1-1z" class="I"></path><path d="M263 418l1-1s0-1-1-1c0-1-1-3-1-4v-1l1 1v1c1 2 3 5 5 7l3 3c-2-1-5-1-7-3l-1-1h1l-1-1z" class="J"></path><path d="M195 293h0 2c2 1 5 2 7 4v1c1 1 1 2 2 2l-1 1c-1-1-1-1-2-1 0 0-1 0-1-1-1-1-1-1-2-1 0-1-2-2-3-3l-2-2z" class="F"></path><path d="M438 97c1 2 2 4 2 6v1l-1 1c-2 3-3 5-6 7h-1c4-3 4-6 4-10 1 0 0 3 1 4h-1v3c1-1 1-1 1-3h0v-3c1-1 0-4 1-6h0z" class="Y"></path><path d="M298 428c2 2 5 4 8 3 2-1 6-4 7-6l2-5c0 5-2 8-6 11-2 1-4 1-6 2h-1c-1 0-1-1-1-1h0l-3-3v-1z" class="C"></path><path d="M523 430h0c1-1 1-1 2-1 1 2 1 4 0 6 0 2-1 3-2 3s-1 0-1 1c-2-1-2-1-2-2 1-3 5-3 3-7z" class="Q"></path><path d="M497 94c1 0 3 1 3 2 3 3 5 7 4 11 0 2-1 4-2 5-1 0-2 0-3 1h0-1l3-3v1h1c1-4 1-8-1-11-1-3-2-5-4-6zM295 243c2 0 3 2 4 3 3 2 5 5 7 7 0 1 0 1-1 1h-1c-2-2-3-4-5-6l-5-4 1-1z" class="T"></path><path d="M256 515c4 1 7 2 11 2 1 1 2 1 3 1l1 1h8c-7 2-17 0-24-3h2c0-1 0-1-1-1h0z" class="I"></path><path d="M183 427c0-1 1-1 1-2h1v-1l1 1v1h-1v1c0 1 0 2-1 3-1 2 0 5-2 7-1 0-3 3-4 4h-1c4-5 5-8 6-14z" class="H"></path><path d="M244 518l1-1c1 0 2 1 2 1 3 2 5 3 7 5v1h-1v1h-1 0l-1-1h-2c1-1 1-1 1-2-2-1-4-3-6-4z" class="Z"></path><path d="M236 528c2 6 8 11 10 17l-4-5c-1-1-1-2-2-3h-1-1c0-2 0-3-1-4v5c0 1-1 1-1 1v-2h1v-5c-1-2-1-2-1-4z" class="K"></path><path d="M263 429c0-1-1-1-1-2v-1c0-1 0-1-1-2 1 0 1 0 1 1 2 2 6 4 7 7 1 0 2-1 2-1h1c0 1 0 1-1 2v1h-4v-1l-1-2c0-1-1-2-2-3l-1 1z" class="O"></path><path d="M267 433c1 0 2 0 3-1h1v1 1h-4v-1z" class="D"></path><path d="M491 330c-3-1-6-2-9-2h-8-2l-1-1h12 8l-1 1 2 1 1 1h-2z" class="O"></path><path d="M215 340c3-2 5-5 8-7 0 2-3 6-5 7h0c-2 0-3 2-4 2-2 1-3 1-4 2l-5 2-1-1 1-1c4-1 7-2 10-4z" class="Y"></path><path d="M101 543v-1c1 0 1-1 1-2 1 5 2 11 1 16l-2-1-1-4c1 0 1 0 1-1v-6c-1 0 0-1 0-1z" class="W"></path><path d="M496 400h0l-4 6 1 1c0-1 1-1 1-2s0-1 1-1h0c0 2-1 4-2 6h0c-1 0-1 0-2-1 0 1 0 1-1 2h0l-1-1c1-4 4-8 7-10z" class="U"></path><path d="M458 429h2c-1 4-1 8 1 11 0 1 2 1 3 2 1 0 1-1 2-1l1 1h-1 0c-1 0-1 1-2 1s-3-1-3-1c-3-3-4-8-4-12 1 0 1-1 1-1z" class="F"></path><path d="M303 616c6 0 12 0 18-3l1 2-2 1c-1 0-2 1-3 1l-5 1c-2-1-4 0-6-1h0c-2 0-2 0-3-1z" class="B"></path><path d="M435 99l1 3c0 4 0 7-4 10-1 1-2 2-4 3v1l-2-1 1-1c3-1 5-4 6-6l1-4c0-2 0-3 1-5z" class="E"></path><path d="M215 338c2-3 5-5 7-8 2-2 2-4 4-5 0 3-1 5-3 8-3 2-5 5-8 7 0-1 1-1 1-2h1l-1-1-1 1h0z" class="Z"></path><path d="M524 167v-1c0-1 0-3-2-3-1-1-1-2-2-3-1-2-2-5-4-7l1-1c5 5 9 11 9 18v1c-1-1-1-3-2-4z" class="R"></path><path d="M456 443v-2l1 5c1 1 1 2 2 3h0c0 1 1 3 2 4 1 2 2 3 3 4 0 1 1 1 2 2v-1h0l1 1c0 1 1 1 0 2h0c-1 0-1-1-2-2-2-1-3-3-5-5-1-1-2-2-2-4 0-1-1-1-1-2h-1v-5z" class="M"></path><path d="M167 373c-4-2-7-5-9-8 0 0 2 1 3 1 2 1 4 3 6 4 2 0 4 1 5 2l-1 1-2-2v1c-1 0-1 0-2 1z" class="B"></path><path d="M248 562l1 1v1c0 2 1 5 1 7v1c0 1 1 3 1 5l-1 1c-3-4-3-11-2-16z" class="Q"></path><path d="M509 488h1l-1 1c-7 3-12 5-19 4 1-2 6-1 8-1 1 0 2-1 3-1 3-1 6-2 8-3z" class="I"></path><path d="M485 121c-2-4-3-7-5-11-1-1-3-3-4-5 4 3 8 8 10 12v1c0 1 1 1 1 2h0-1l-1 1z" class="L"></path><path d="M470 518l1-1c2 1 4 2 5 3h1l-1-1 1-1c2 2 4 5 5 9-1 0-1 0-2-1l-1-1-1 1-1-2h0v-1l-1-1c-1-2-4-3-6-4z" class="Q"></path><path d="M477 524l1-1h1c0 1 0 2 1 3l-1-1-1 1-1-2z" class="H"></path><path d="M245 463c-2 0-3 1-4 2v-1c0-1 1-2 2-2 3-3 10-5 13-4h3l-2 2c-2-1-2-1-3 0h-4c-1 1-2 1-4 2h0l-1 1z" class="I"></path><path d="M325 104c-1 1-1 3-2 4 0 1 0 1-1 1-2 0-3 0-5-2v-1c1-1 2-3 4-3h1v1c-1 0-1 0-2 1h0 3l1-2c1 0 1 0 1 1z" class="E"></path><path d="M503 580h0c0 2 0 2-1 3h1 0l1-1 1-1h0c-1 2-2 4-3 5l3-3h1c-3 5-7 9-11 13 0-2 3-5 4-8 2-2 3-5 4-8z" class="Q"></path><path d="M205 508h5c5 1 8 3 13 4 2 1 4 2 5 3 2 1 3 3 5 4l-1 1c-6-6-14-9-22-10-1 0-2 0-3-1-1 0-1 0-2-1z" class="g"></path><path d="M302 433h1v1c1 1 3 2 3 3v1c0 1 1 1 1 2v1l1 1c0 1 0 2-1 3v1c-1 0-1 1-2 3 0-1 0-1-1-2 1-2 1-5 1-7l-1-2v-2c-1-1-2-1-3-2l1-1z" class="Y"></path><path d="M302 433h1v1c1 1 3 2 3 3v1c0 1 1 1 1 2h-1v4h-1v-4l-1-2v-2c-1-1-2-1-3-2l1-1z" class="M"></path><path d="M360 630h1c2 1 3 2 5 4l-1 2c-1 3-3 4-5 6h-1c-2 2-4 1-6 1-2 1-4 1-6 0h7c3-1 6-2 8-5 1-1 2-3 1-4 0-2-1-3-3-4z" class="C"></path><path d="M49 554h0c-1-1 0-3 0-4h1l3 15 2 7-1 1-5-19z" class="n"></path><path d="M317 442l1-1c0-1-1-3 0-4 1 3 1 8 1 11h0c-1 5 0 10 0 14h-1c-1-2 0-4-1-5v-10-5z" class="I"></path><path d="M313 341c1 5 2 10 2 15l-2 1c-1 0-1 0-1-1 0-2 0-5-1-6 1-3 2-5 2-9z" class="K"></path><path d="M502 348l-2-2h1c5 5 8 11 9 19 0 1 0 1-1 1v-1c-1-4-2-8-4-11l-3-6z" class="V"></path><path d="M454 121l1 1 1-1v-1-1c1 0 2 0 3 1 0 2 0 4-1 6-1 1-1 1-2 1l-3-3 1-1h-1l-1-1c1 0 1-1 2-1z" class="R"></path><path d="M168 548v-1h1c0 3 1 7 3 9 1 0 1 1 1 1l1 1c1 2 1 3 2 4h0c-1 0-1-1-2-1l-1 1c-2-2-3-4-4-6h0 1 0c0-2-2-5-2-8z" class="T"></path><path d="M364 80h1c3 0 5 2 7 4 0 5 2 8 4 12h0c-1-1-2-2-3-4l-3-7c0-2-1-3-3-3-3-2-3 2-5 3-1-1-2-1-2-2v-1c1-1 3-1 4-2z" class="W"></path><path d="M301 302h0c4 0 8 5 9 8 1 2 1 4 2 5h-1c-4-1-4-6-5-9-1-2-3-3-4-3l-1-1h0z" class="F"></path><path d="M310 499c0-1 1-2 1-3h0v4c0 2 0 4-1 6-1 0-1 0-2 1l-1 2-1 4-2 2v-1l6-15z" class="U"></path><path d="M485 558h1 1l2-1c0 1 0 1 1 1h2 2s1 1 1 2c-1 0-1 1-1 1h-1c-1 1-2 3-3 3v-1-1h-1v-1c-1 0-2 0-2 1v-3l-2-1z" class="B"></path><path d="M490 558h2 2s1 1 1 2c-1 0-1 1-1 1h-1-1l-1-1c-1 0-1 0-1-1v-1z" class="N"></path><path d="M132 534c1-1 2-1 4-1 0 0 0 1-1 1-1 2-1 4 0 6s2 4 4 5h1 2l1 1v1c0 1-1 1-1 2l-2-1c-1-2-3-3-5-5l-3-9z" class="g"></path><path d="M229 337l2 2c0 1 0 1-1 3-2 3-6 5-9 8h-1c1-2 3-3 4-4s3-2 4-4h-1-2v-1l1-1h1c0-1 1-1 1-2l1-1z" class="C"></path><path d="M303 443l1 1-1 4h0c0 2-1 3-2 4-1 0-2 1-3 1h0 0v-1h-1l-1 1c-2 0-4 0-5-1h1c1-1 2-1 3-1l1-1c2 0 3-1 4-3l1 1c1-1 1-3 2-5z" class="b"></path><path d="M300 447l1 1c-1 3-3 3-5 4h-4c1-1 2-1 3-1l1-1c2 0 3-1 4-3z" class="C"></path><path d="M121 395h1l2-2 1-1h0v1c-1 3-3 6-2 10 2 4 4 6 7 8l-1 1c-2-1-4-3-6-6-1-3-3-8-2-11z" class="H"></path><path d="M507 376l1 1c-4 7-8 12-14 17l-1-2 2-1c1-1 1-2 2-3 5-3 7-6 10-12z" class="I"></path><path d="M130 536l2-2 3 9h0c1 3 2 4 4 5l-1 1c-1-1-1-1-2-1l-1 2-2-1v-1h1l1-1h1l-1-2h-1c-2-2-2-3-2-5h0c-1-1-2-1-2-3v-1z" class="c"></path><path d="M134 548h2l-1 2-2-1v-1h1z" class="I"></path><path d="M132 540c1 2 2 3 3 5h0-1c-2-2-2-3-2-5z" class="O"></path><path d="M203 342c4-1 7-3 11-5l1 1h0l1-1 1 1h-1c0 1-1 1-1 2-3 2-6 3-10 4l-1 1h0c-1 0-1 0-1-1 2 0 2 0 3-1h-2v-1h-1z" class="N"></path><path d="M135 543c2 2 4 3 5 5l2 1c1 1 2 2 3 2v1l-2 1c-1-1-2-2-3-2h-2v1l-3-2 1-2c1 0 1 0 2 1l1-1c-2-1-3-2-4-5h0z" class="F"></path><path d="M482 400l2-1 1 1c-5 3-11 8-12 14 0 1 0 2 1 4 1 1 3 1 4 1v1c-2 0-4-1-5-3s0-6 1-9h0a30.44 30.44 0 0 1 8-8z" class="T"></path><path d="M316 483v10c1 1 1 2 1 2l1 3v-2h1 0v3l1 1c0 2-3 7-4 9 0-1 1-3 1-5-1-4-1-8-1-12v-9z" class="P"></path><path d="M316 493c1 1 1 2 1 2l1 3v2h-1c-1-3-1-5-1-7z" class="c"></path><path d="M156 509c1 1 2 3 3 4 1 2 1 5 2 6l1-1v1l1-2v6c1 1 0 3 1 5v3l-1 1c-1-7-3-13-6-20l-2-3h1z" class="I"></path><path d="M495 328c4 0 7 1 10 3 1 1 1 1 0 2v1s-1 1-2 1c-1-1-1-1-2-1-1-1-3-2-4-3v-1c-1-1-1-2-2-2z" class="N"></path><path d="M501 334c1-1 1-2 2-2l1 1h1v1s-1 1-2 1c-1-1-1-1-2-1z" class="Q"></path><path d="M501 334c1-1 1-2 2-2l1 1c-1 0-1 1-3 1z" class="E"></path><path d="M497 330l4 1c1 0 1 1 2 1-1 0-1 1-2 2-1-1-3-2-4-3v-1z" class="J"></path><path d="M295 239h0 0 2 1c2 2 4 1 7 1 2 1 8 3 8 5h0 0c-2 0-3-1-4-1-3 1-6-2-9-3-2 0-3 0-4-1l-1-1z" class="C"></path><path d="M249 554c0-3 0-6 1-9 2-4 4-6 8-7 1-1 3-1 4-1v-1c-3 0-5 1-7 2h0c2-2 5-3 8-3v1c0 1 1 1 0 2h-1c-3 0-7 2-10 4-3 4-1 9-2 14-1-1-1-2-1-2z" class="E"></path><path d="M517 430l1 1c-1 2-2 4-1 7 1 4 4 6 6 9h-2c-1-2-3-3-4-5 0 0-1-1-1-2 0-2-2-3-2-5s2-4 3-5z" class="I"></path><path d="M288 611c0 1 1 1 2 1l1 1v-1c3 1 5 3 8 3 1 0 3 1 4 1 1 1 1 1 3 1h0c2 1 4 0 6 1-5 1-12 0-17-2h0c-3-1-6-2-8-4l1-1z" class="C"></path><path d="M172 529h1v2c-1 4-1 7-1 11 0 3-1 5 0 7 0 2 1 4 1 6h-1c-3-7-2-18 0-26z" class="T"></path><path d="M254 430c2 0 3 0 4 1 0 1 1 2 1 3-1 1-1 2-2 3h-1c-1 0-2-1-3-1-1-1-1-2-1-4l2-2z" class="G"></path><path d="M140 406c4-1 8-2 11-4 0 2-1 3-3 4 0 1 0 1-1 2h0c-1 2-3 2-5 2h-2v-1h-1l1-1h1 1c-1-1-1-1-2-1v-1z" class="N"></path><path d="M506 427l1-2c0-1-4-4-4-6l1-1 1 1c0 3 2 5 4 6h2 0l4 1c1 1 2 1 3 1h-1-1c-3 1-7 2-10 1h1l-1-1z" class="B"></path><path d="M457 480v-1h1v1c0 7-3 15-1 21v1c1 3 4 8 6 9l1 1v1c-1-1-2-1-3-2-3-4-5-9-6-14 0-2 1-5 1-8 1-3 1-6 1-9z" class="I"></path><path d="M485 555c1-1 1-1 2-1 2-1 4 0 6 0h0v1c1 1 2 1 3 2l-2 1h-2-2c-1 0-1 0-1-1l-2 1h-1-1v-1c-1-1 0-1 0-2z" class="H"></path><path d="M485 555c1-1 1-1 2-1 2-1 4 0 6 0l-1 1c-1 0-4-1-5 0-1 0-1 1-2 1v-1z" class="U"></path><path d="M172 325c3 5 9 7 13 11v1c-4-2-8-3-11-6l-3-3 1-3z"></path><path d="M204 297c5 3 9 6 11 11l-3-3-2-1-1 1v1h-1l-1-1c0-1-1-3-2-4l1-1c-1 0-1-1-2-2v-1z" class="E"></path><path d="M503 130l2 2v1l3 2c1 0 3-1 5-1v1h-1s-1 1-2 1h0c1 1 3 2 5 3l3 1c-2 1-2 1-3 1h0c-5-3-11-5-13-10h1v-1z" class="b"></path><path d="M508 344h-1c-1-3 0-6 1-9 2-3 4-6 7-7l1 1c-3 3-6 5-7 10v3 4-1c-1 0-1 0-1-1z" class="E"></path><path d="M300 541l2 1c1 4 2 6 5 8 1 1 2 1 3 1 0 0 1 1 2 1v1h0c-1 0-1 0-1 1h-1c-2 0-4-1-5-3-3-2-4-7-5-10z" class="Q"></path><path d="M305 551c1 0 2 0 3 1h2s1 1 2 1h0c-1 0-1 0-1 1h-1c-2 0-4-1-5-3z" class="g"></path><path d="M332 602h1c1 0 2-1 2-2v1c0 2 0 3 1 5 3 3 10-1 12 4v1c0-1-2-2-3-3h-9c-1 1-1 1-2 1-2-2-2-3-2-6v-1z" class="F"></path><path d="M218 356l11-9v2c-1 0-3 3-4 3l-1 1s-1 1-2 1c0 1-1 1-2 2-1 0-1 1-2 2-1 0-1 1-2 1l-6 3h0c-2 0-4 0-6 1v-2h2c1 0 2-1 3 0 1 0 2-1 3-1 1-1 5-3 6-4z" class="J"></path><path d="M182 437c2-2 1-5 2-7 1-1 1-2 1-3v-1h1v5c-1 1-1 1-1 2 1 0 1 1 1 2h-1v1 1 1h0l-4 7 1-3c1-2 2-3 2-5l-1-1c-1 4-2 8-5 11v-1c1-3 3-6 4-9z" class="F"></path><path d="M405 80c4 1 8 3 10 6s2 6 2 9c-1 2-3 4-5 5s-3 1-5 1c-1-1-3-2-3-3v-1h0c1 1 1 2 3 3 1 0 3 0 5-1 2 0 3-3 4-4 1-3 0-6-1-8-2-3-6-5-9-6h-2 0l-1-1h2z" class="T"></path><path d="M298 453c-2 1-4 1-6 1s-3 0-4-1c-1-2-2-3-2-4l2-2c1 1 2 1 3 1v2c1 1 2 1 4 1-1 0-2 0-3 1h-1c1 1 3 1 5 1l1-1h1v1h0 0z" class="Z"></path><path d="M291 452c0-1-2-1-2-2h0 2c1 1 2 1 4 1-1 0-2 0-3 1h-1z" class="D"></path><path d="M274 623h2c1 0 4 2 5 2 0-2-9-4-10-6h0c3 1 5 2 8 3 2 1 4 3 6 4 1 1 2 1 3 2h-1 0-1-1-1c-1-1 0-1-1-1h-2-2 1l-1-1c-1-1-1-1-3-1-1 0-2-1-3-2h1z" class="K"></path><path d="M167 351l3 2c2 3 7 4 10 4l2-1h1l6 1h3c-1 1-2 1-2 1h0 2c-6 2-14 1-20-2h0-1c-2-1-3-2-4-3v-2z" class="G"></path><path d="M494 327l1 1c1 0 1 1 2 2v1c1 1 3 2 4 3 1 0 1 0 2 1l-1 1v1h0c-1 0-1-1-2-1-1-1-2-2-4-2-2-2-3-3-5-4h2l-1-1c1 0 1-1 2-2z" class="B"></path><path d="M491 330h2c2 1 3 2 5 3 1 1 3 3 4 3v1h0c-1 0-1-1-2-1-1-1-2-2-4-2-2-2-3-3-5-4z" class="P"></path><path d="M225 482c2 4 2 11 3 16 0 3 1 6 2 9v1c-1-1-1-2-2-3-1-3-1-6-2-8 0-1 0-3-1-3-1-1-1-3-1-4h0 1v-8zm82-128l1 1h1 0l-6 5h0c3 0 6-4 8-6-1 1-1 2-1 2-2 3-4 4-7 5h-1c0-1 0-2 1-4h0c-1 0-1 0-1-1h-3l-2 2c-1 1-2 1-3 2 1-3 3-4 5-6 1 1 2 1 4 2 2 0 3-1 4-2z" class="B"></path><path d="M291 547l1-1v1h0c0 7-2 11-8 16l-7 5h-1c1-2 3-3 5-4 2-2 3-4 5-6 3-4 5-5 5-11z" class="F"></path><path d="M315 98h4c-7 3-13 8-15 16l-1-1c-1 0-1 1-1 2l-1-1 1-2c1-2 2-4 4-6 2-4 6-6 9-8z" class="J"></path><path d="M326 104v-1h1c0-1 0-1 1-2h1c0 3 1 6 3 8 2 1 4 1 5 1v1c-1 1-2 1-4 1-2-1-4-3-6-5 0-1-1-2-1-3z" class="C"></path><path d="M460 566c0-1-1-3-1-5 0-1 2-3 3-4l1 1c-1 1-1 1-1 3 1-2 1-3 2-4h1v1l1-1 1 1h0c-1 1-1 2-1 3 0 0-1 0-1 1h0c0 1-1 1-1 1l1 1-1 1-2-2c-1 1-1 1-2 3z" class="L"></path><path d="M519 525c2 4 1 8 2 13v4c1 1 1 2 1 3-1 1-1 2-2 4v-6c0 1-1 2-1 3h0v-7l1-1v-2-1c0-1-1-1-1-1v-4c-1-2-1-3-2-5h1 0 1z" class="E"></path><path d="M485 544l1 1 1-1v1c-4 5-5 11-4 18l-1 1c-1-5-2-10-1-14 2 0 3-4 4-6z" class="N"></path><path d="M215 362v1h-1c0 1 1 1 2 1 0 0 2 2 3 2h-1l5 3c2 1 3 2 4 3l3 2v1h-1 0c0-1-1-1-1-1l-1-1c-1-1-1-1-2-1v-1c-1 0-1 0-2-1h-1c-1-1-2-1-3-1l-4-3c-3-1-5-1-8-1 3-1 5-2 7-3h1z" class="Y"></path><path d="M263 175c4 4 7 8 9 14l-1-1c-1-1-2-2-2-3l-2 1-2-3c0-2-3-4-4-5v-1l1-1 1 1v-2z" class="N"></path><path d="M261 178v-1l1-1 1 1c2 3 4 5 6 8l-2 1-2-3c0-2-3-4-4-5z" class="E"></path><path d="M322 446c0-3 0-7-1-10-1-4-3-8-4-12v-5c2 4 4 9 5 13 0 2 1 4 1 7v8l-1-1z" class="e"></path><path d="M233 227l-1-1c0-1 0-1 1-2 4 1 8 4 12 5h-3l1 1v1l-3-1c-2 0-4 0-5-1 0-1-1-1-1-2h-1z" class="G"></path><path d="M233 227l1-1c2 1 5 3 8 3l1 1v1l-3-1c-2 0-4 0-5-1 0-1-1-1-1-2h-1z" class="E"></path><path d="M291 225l-1-1c0-3 1-6 2-7 2-3 6-5 9-6 3 0 6 1 8 2l-1 1c-3-1-5-2-9-1-2 1-4 3-5 5s-1 3-1 5h-1v1 1h1-1-1z" class="N"></path><path d="M503 335c1 0 2-1 2-1l1 1-2 2h1c0 2 0 3-1 4h-4c-2-1-3-2-3-4v-1h1l-2-2c2 0 3 1 4 2 1 0 1 1 2 1h0v-1l1-1z" class="D"></path><path d="M361 104h3v1 2l1-1c1 1 3 0 4 1-2 0-4 0-5 2l1 2c-1 0-1 0-1 1v1-1c-1-2-2-3-4-4 0 0-1-1-2-1-3 0-5 3-8 4l-1-1 1-1c3-1 5-3 8-3 1 0 1-1 2-1s1 0 1-1z" class="U"></path><path d="M361 104h3v1 2c-2 0-4 0-6-1 1 0 1-1 2-1s1 0 1-1z" class="M"></path><path d="M114 426h0c1-2 1-3 1-5v-3c0-1 0-2 1-3 1 1 1 2 1 3 0 5-2 10-1 15 0 1 1 2 1 2v3c1 1 1 2 1 3h-1 0l-1-5-1-1v2-3h-1v-1c-1-2 0-3 0-4v-3z" class="I"></path><path d="M525 184h0c0 2-1 6-1 8l-1 9c0 1 0 4-1 5h-2l-1-1v-3c1-3 3-6 3-9l3-9z" class="B"></path><path d="M320 451c0-6 0-12-2-18 0-2-1-4-2-7 3 4 4 10 5 14 0 3 0 5 1 7v-1l1 1v3c-1 1-1 4-1 6h-1c0-2 0-3-1-4v-1z" class="W"></path><path d="M408 107c0-2 1-4 3-5h1 0c1-1 3-1 4 0 1 0 3 1 3 3v2l-3 3c-1 0-2-1-3-2s-1-1-1-2h1l1 1c0-2 1-3 2-4h-3c-1 0-3 1-3 1l-1 2-1 1z" class="O"></path><path d="M461 100c4 5 7 9 6 15-1 4-2 9-6 12l-1 1-4 1-1-1h2c3-1 6-5 7-8 2-4 2-8 1-12-1-2-2-5-4-7v-1z" class="Q"></path><path d="M458 445c2 4 5 7 9 8 3 1 6 0 8-2-2 2-4 5-7 5-1 0-2 0-3-1-2-1-3-2-5-4l-1-2h0c-1-1-1-2-2-3l1-1z" class="R"></path><path d="M460 451l5 3h0v1c-2-1-3-2-5-4z" class="K"></path><path d="M284 214v-2l2-2v1 2c1 0 3 1 3 2v1 1 3l1 1-1 2v1l-3-3c-1-2-1-4-2-7z" class="C"></path><path d="M286 221c1 0 1-1 1-1 1-1 1-3 1-4h-1v-1h0s1 0 2 1h0v1 3l1 1-1 2v1l-3-3z" class="O"></path><path d="M197 354c2 0 4-1 5-2s1 0 1 0l-1 1h1c-3 2-7 4-11 5h-2 0s1 0 2-1h-3l-6-1v-1c1 0 2 1 4 0h1 4c2 0 3 0 5-1z" class="k"></path><path d="M202 353h1c-3 2-7 4-11 5h-2 0s1 0 2-1h-3c5-1 9-2 13-4z" class="R"></path><path d="M196 509c1 1 1 1 3 1h0 2c0 1-1 1-2 1-2 1-4 1-6 1-4 1-10 7-13 10-1 2-2 5-4 7v-1-1c4-8 12-14 20-18z" class="N"></path><path d="M170 598v1c-2 2-3 6-3 9 0 5 2 9 5 12 4 5 10 5 15 5h-1-1c-4 1-8 0-11-2-4-3-7-8-8-12-1-6 1-9 4-13z" class="a"></path><path d="M290 632l3-2 1 1c-1 1-1 2-2 3v-1c-1 0-1 0-2 1l-15 5h-1l2-1v-1h-5c1-1 4 0 5-1 5-1 10-3 14-4z" class="N"></path><path d="M290 632l3-2 1 1c-1 1-1 2-2 3v-1c-1 0-1 0-2 1l-15 5h-1l2-1c2 0 5-1 7-2 3-1 5-2 7-4h0z" class="P"></path><path d="M466 441c1-1 1-1 1-2h-1l-2 2c-1-1-3-3-3-4-1-2-2-4-1-6s1-4 2-6l1 1v-1 1 7c1 0 1 1 1 2h0-1l-2-2c0 1 0 3 2 4l2 1c1 0 2-1 2-1 1 1 1 2 1 3l-1 2-1-1z" class="Q"></path><path d="M187 421l1 1v-2h1c1 2 2 4 2 6l-1 1-1-1v2 2c-1-1-1-2-2-2v1h0v1l-1 1v-5-1l-1-1v1h-1c0 1-1 1-1 2 0-2 0-4 1-6h3z" class="C"></path><path d="M393 338h1c-2 3-5 9-5 13 0 1 0 1-1 2 0 0 0 1-1 2 0-2-1-2-2-3h-2-1c1-2 2-5 3-6l1-1c0-1 1-2 3-2l-3 3c-1 2-2 4-3 5l1 1c0-2 2-5 3-7h1c0 3-2 5-3 7 2-1 3-4 4-6 1-3 1-5 4-8z" class="F"></path><path d="M432 99c1 2 2 3 2 5l-1 4c-1 2-3 5-6 6l-1 1c-1 0-4 1-5 1h0v-1h0 0c-2 1-4 1-5 0h0c4 0 9-1 13-5 3-2 3-6 3-10v-1z" class="Z"></path><path d="M432 99c1 2 2 3 2 5l-1 4v-3h0c0-2-1-3-1-5v-1z" class="N"></path><path d="M359 601c1-2-1-2 1-4 2 0 4 0 6 1 1 1 2 2 2 4l1 3c-3 3-5 6-8 10v-1c1-3 3-6 5-9 1-1 1-3 1-4-1-1-1-2-2-3h-3c0 1 0 2-1 3 1 1 1 2 1 3-1 1-1 1-1 2l-1 1h-1l1-1v-3l-1-2z" class="F"></path><path d="M204 611c7-4 12-6 19-7h2 4c-3 1-10 0-12 3v1c-1 1-2 1-3 2h-3v1c-2-1-3 0-5 0l-2 2v-2z" class="B"></path><path d="M275 360c1 2 3 3 4 4 5 3 11 3 16 2v1c-4 1-7 1-11 0l-3 1-1-1c-1 0-5-2-5-3-1-2-2-2-3-4l1 1h1l1-1z" class="Z"></path><path d="M279 365l5 2-3 1-1-1-1-2z" class="l"></path><path d="M273 361h1c0 1 1 1 2 2l3 2 1 2c-1 0-5-2-5-3-1-2-2-2-3-4l1 1z" class="X"></path><defs><linearGradient id="M" x1="154.116" y1="346.489" x2="161.373" y2="348.597" xlink:href="#B"><stop offset="0" stop-color="#787779"></stop><stop offset="1" stop-color="#8e8d8d"></stop></linearGradient></defs><path fill="url(#M)" d="M157 351c-2-3-3-6-2-10h0c2 1 5 9 6 12 2 3 5 5 8 7h-1c-1 0-2-1-3-2h0l-5-5h-1c-1 0-2-1-2-2h0z"></path><path d="M457 480h0c-1-4-2-12 1-15 1-2 3-2 5-2h1c0 2 1 2 0 3s-1 1-2 1-2 0-2-1 1-1 2-1v1h1v-2h-3l-2 2c-1 2 1 7 2 9l-1 1v-1c-1 0-1 1-1 1l1 1-1 3h0v-1h-1v1z" class="J"></path><path d="M225 311c1-2 2-5 3-7 1-1 1-2 2-2v9 3h-1 0-1v-1h-1v1l-1 1c0 2 0 2-2 3 0-3 1-5 1-7z" class="B"></path><path d="M219 465c1 1 1 1 1 2l-2 3-2 4c0 1-1 1-1 2-7 8-15 13-25 16l-1-1 8-3c12-4 18-12 22-23z" class="J"></path><path d="M196 509c2-1 4-1 5-2h1l3-1c6 0 12 2 18 4 1 1 3 2 4 3-2 0-5-2-7-3-3-1-6-2-10-2h0-5l-6 2c-2 0-2 0-3-1z" class="Z"></path><path d="M306 364h2v1 1l-1-1h0l-1 1c-2 0-4 1-5 2h0l-1 1h-1-1c0 1-1 1-2 1 2-1 3-1 4-2v-1c-2 0-3 1-5 1-5 1-10 2-16 1 2 0 2 0 3-1h-1l3-1c4 1 7 1 11 0v-1c2 0 4-1 6-1 2-1 4-1 5-1z" class="M"></path><path d="M295 366c2 0 4-1 6-1 2-1 4-1 5-1-1 1-4 2-6 2-1 0-3 1-5 1v-1z" class="D"></path><path d="M171 364c-6-2-12-7-15-12-2-3-4-8-3-11 1 0 1 6 2 7 0 1 1 2 2 3h0c0 1 1 2 2 2h1l5 5h0v2h1c2 1 5 2 6 3l-1 1z" class="J"></path><path d="M346 611v-1c3 1 4 3 4 5 1 4 1 7-1 11l4-4v1l-2 3c-1 2-2 2-2 3l-5 3v-3h0l3-3c3-3 3-5 3-8-1-3-2-5-4-7z" class="T"></path><path d="M440 105h1v1c-1 3-3 4-5 6h1c-1 2-3 4-6 4h0-1c-1 1-2 2-4 2l-2-2h1 3v-1c2-1 3-2 4-3h1c3-2 4-4 6-7h1z" class="N"></path><path d="M440 105h1v1c-1 3-3 4-5 6h1c-1 2-3 4-6 4h0-1c2-1 3-2 4-3 3-2 5-4 6-8z" class="S"></path><defs><linearGradient id="N" x1="173.457" y1="573.573" x2="177.068" y2="580.441" xlink:href="#B"><stop offset="0" stop-color="#757376"></stop><stop offset="1" stop-color="#8a8b89"></stop></linearGradient></defs><path fill="url(#N)" d="M168 575c7 1 14 2 20 4 2 0 5 1 6 2-1 1-6-1-8-1-7-2-14-2-20-3h0 0l-1-1h2l1-1z"></path><path d="M281 375h3c-1 1-1 1-2 1-3 1-6 1-9 2-6 2-11 7-15 12l-1-1c2-1 3-3 5-5s5-4 7-5l-7 3-1-1c1 0 1-1 2-1l1-1c1 0 3-1 4-1 4-1 9-2 13-3z" class="K"></path><path d="M465 179h-1c-1-1-1-1-1-3l-1 2h-2v-3c1 0 1-1 2-2v-1l-1 2h-1 0c0-1 0-2 1-2 1-2 4-1 6-1v1c-1 0-1 0-2 1h-1l-1 2c-1 0-2 1-3 2h1c1 0 1-1 2-1s7-3 8-3h1l-1 1c-1 0-1 0-2 1h1c0 1-1 2-1 2h-1v1h0l-1-1-2 2z" class="E"></path><defs><linearGradient id="O" x1="174.322" y1="495.954" x2="185.27" y2="489.999" xlink:href="#B"><stop offset="0" stop-color="#4e4e4f"></stop><stop offset="1" stop-color="#696768"></stop></linearGradient></defs><path fill="url(#O)" d="M190 492c-2 1-4 1-5 3-1 0-3 1-4 1h-6c-1 0-3-2-4-3 6-1 12-1 18-2l1 1z"></path><path d="M310 135l3 1c4 2 10 1 15-1h1v1h0c-2 2-5 4-7 4-3-2-8 0-11-2l-3-2h1l1-1z" class="N"></path><path d="M465 392h7c3 0 6 1 8 1s3-1 4-1c0 1-1 1-1 2h-1c-2 1-6 2-8 1-2 1-3 1-5 1v-1h-1l-1-1h2l-2-1h0l-2-1z" class="K"></path><path d="M469 394h4c1 1 3 0 4 0 0-1 2-1 3-1 0 0 1 1 2 1-2 1-6 2-8 1-2 1-3 1-5 1v-1h-1l-1-1h2z" class="H"></path><path d="M283 411v-2c0-5 1-9 3-13 1-2 1-3 2-4h1c0 2-2 3-2 5v1c0 1 0 2-1 3 1 1 0 2-1 3l1 1h0v1l1 1c0 1 0 3-1 4v1l-1-2c0 2-1 3-1 4l-1-3z" class="B"></path><path d="M285 404l1 1h0v1l1 1c0 1 0 3-1 4v1l-1-2v-6z" class="F"></path><path d="M513 135c2 0 5 0 7 1l2 3h-2l1 1h1v1l-1 1c0-1-1-1-1-1l-2-1-3-1c-2-1-4-2-5-3h0c1 0 2-1 2-1h1z" class="K"></path><path d="M515 139c2 0 4 1 6 1h1v1l-1 1c0-1-1-1-1-1l-2-1-3-1z" class="J"></path><path d="M263 134h1c0 1-1 2-1 3l-3 5c-1 2-2 4-2 6l-3 2v1h-1c-1-1-1-2-1-3l10-14z" class="E"></path><path d="M255 150s0-1 1-1c0-1 2-6 3-7h1c-1 2-2 4-2 6l-3 2z" class="J"></path><path d="M211 415c0-5-1-8-2-13 0-1-1-3-1-4l-3-3-5-8c2 1 3 3 4 5 1 1 2 3 3 3 3 2 4 11 5 14 0 2 0 5 1 8 0 1-1 4-1 6v1c-1-1-1-7-1-9h0z" class="E"></path><path d="M397 641c1 0 1-1 2-1 4-4 11-2 15-1 4 0 9-2 12 0 1 0 2 0 3 1-3 0-7 1-10 0-5 0-9-1-14-1-1 0-3 0-4 1-1 0-3 2-3 3-1-1-1-1-1-2z" class="Q"></path><path d="M215 363h5c1 1 2 1 4 1 1 1 2 3 3 4h0s1 1 2 1v1h0c1 1 1 1 1 2v1c-1-1-2-2-3-2-1-1-2-2-4-2-1-1-3-2-4-3-1 0-3-2-3-2-1 0-2 0-2-1h1z" class="L"></path><path d="M215 363h5c1 1 2 1 4 1 1 1 2 3 3 4h0c-1 0-2-1-2-1l-1-1c-1-1-2-1-3-1-2-1-3-1-5-1-1 0-2 0-2-1h1z" class="D"></path><path d="M161 569h5c3 1 6 3 10 2l2 1c1 0 2 1 3 1h2v-2l5 4c1 1 3 2 4 3-2 0-3-1-5-2-1 0-1-2-2-2v1l1 1h0c-2 0-5-2-7-2-4-1-7-2-10-2-1 0-2-1-2-2h-6v-1h0z" class="C"></path><path d="M461 489v1-1c0 1 1 2 0 2l1 2h0l1 2 1 1c1 2 1 7 1 8h-2 0-1c-1-1-2-4-2-5 0-2-1-8 1-10z" class="G"></path><path d="M462 493v1 2l-1-1v-3-1l1 2h0z" class="D"></path><path d="M461 496h1c1 2 2 2 2 4-1 1-1 0-1 1h0v1 1h0l-1-1c0-1 0-1-1-2v-2s1-1 0-1v-1z" class="O"></path><path d="M138 399c1 0 2 1 3 1l1-1c1 1 0 3 0 3v1c-2 1-3 2-5 2l-2-3v1c0 1 0 1 1 2h-1l-2-2v-1c0-2 1-2 2-3h3z" class="Q"></path><path d="M135 402v-1h2 0 2 1 0l2 1v1c-2 1-3 2-5 2l-2-3z" class="F"></path><path d="M153 562h-1c-3 0-6 0-8 3-1 1-2 3-3 4l-1-1v-1c-1-1-1-2 0-3 2-3 6-4 9-5l1 1c2 0 3 0 5 1h0l-2 1z" class="J"></path><path d="M221 620v-1c1-1 4-3 6-4h0c-1 4 3 7 1 10-2 1-3 1-4 0-2-1-2-2-3-4v-1z" class="C"></path><path d="M239 419v-4c3-14 11-26 22-34l1 1c-5 3-9 7-12 11-4 6-7 12-8 18-1 3-1 6-1 8v-4h-1 0v2c0 1-1 2-1 2z" class="I"></path><path d="M280 421l1 1v1l1 1h0v1c1 1 1 3 1 4h0v1c0 2 0 4-1 5h-1c-1 0-2 0-3-1-1-2 1-6 1-8 1-1 1-3 1-5z" class="N"></path><defs><linearGradient id="P" x1="167.604" y1="390.768" x2="177.658" y2="394.364" xlink:href="#B"><stop offset="0" stop-color="#737272"></stop><stop offset="1" stop-color="#909191"></stop></linearGradient></defs><path fill="url(#P)" d="M168 387c1 0 1-1 2-1h1v-1l-2-2v-1-1c5 5 7 11 8 18-1 1-1 2-2 4h0c0-5 0-9-4-13-1-1-2-1-3-2v-1z"></path><path d="M244 518c2 1 4 3 6 4 0 1 0 1-1 2h2l1 1h0l-1 1 1 1h0-3-1l-3-2-2-1-1-1h-1v-2l3-3z" class="C"></path><path d="M251 524l1 1h0l-1 1 1 1h0-3v-1-1l2-1z" class="B"></path><path d="M242 523c0-1 1-2 2-3l1 2c0 1-1 1-2 2l-1-1z" class="J"></path><defs><linearGradient id="Q" x1="313.12" y1="174.797" x2="323" y2="169.625" xlink:href="#B"><stop offset="0" stop-color="#6a676d"></stop><stop offset="1" stop-color="#80817e"></stop></linearGradient></defs><path fill="url(#Q)" d="M319 163c0-1 0-1 1-1 1 9 0 18-4 26-1 0-1 0-1-1h-1l4-9c0-2 0-3-1-5h-1v-1h1c2-2 2-7 2-9z"></path><path d="M318 125c2-1 2-1 3-2h0 2 0c0-1-1-2-1-3l-2 1h-1c1-1 1-2 2-2 1-1 2-1 3 0 1 0 2 1 2 2 1 1 1 1 0 3 0 1-2 3-3 4 0-1-1-1-1-2-1 1-3 1-4 2l-2-2v-1-1c1 1 1 2 2 2l1-1h-1z" class="G"></path><path d="M322 126l1-1c1-1 2-2 2-3l-2-1v-1h0 1c1 0 1 1 2 1h0 0c1 1 1 1 0 3 0 1-2 3-3 4 0-1-1-1-1-2z" class="L"></path><path d="M250 578l1-1c0-2-1-4-1-5v-1c2 4 4 7 9 10h-2c-1 0-1 0-1 1h0l-2-1h0c0 1 2 3 3 3s2 1 4 1c1 0 2 0 3-1v-1l1 1-2 2c-1 1-3 1-5 0-4-1-6-5-8-8z" class="E"></path><path d="M517 177l1-2v-1 5c-1 3-2 5-5 6-2 1-4 2-6 1-2 0-3 0-3-2l-2-3h1c1 1 3 2 5 2 1 1 2 1 3 0 3-1 5-3 6-6z" class="D"></path><path d="M260 609c7 3 14 8 21 13 2 1 6 3 8 5-2 0-8-4-10-5-2-2-4-3-6-4s-3-2-5-3l-7-3c-1-2-1-2-2-3h1z" class="H"></path><path d="M380 96c-1-2-3-4-4-6 0-1 0-2-1-3 1-2 1-5 2-6s1 0 1-1l-1 2 1 1c0-1 1-1 2-2h1s-2 1-2 2c0 2-2 6-1 7v2h1v-4h0c0-2 0-4 1-5s3-3 5-3c1 0 2 0 3 1v1c-1 1-1-1-2-1h-1c-4 2-5 5-5 8v3c-1 1 0 2 1 2 0 1 0 1-1 2z" class="D"></path><path d="M267 131v4h0-1c-3 4-3 10-3 15-1 3-1 6-1 9h0v-4l-1 1v6c0-2 0-4-1-6v-5c0-5 3-9 3-14h0c0-1 1-2 1-3h1c1-1 2-2 2-3z" class="S"></path><path d="M234 186l1 3c0 5-1 9-1 13 0 2-1 3 0 5h0c0 2 0 4 1 6v3c0-1-1-2-1-3l-1-1v1c-1-3-1-5-1-8v-12-1c1-2 2-4 2-6z" class="N"></path><path d="M493 392l1 2c-4 4-7 7-9 12l-1 2h-1c0 1-1 2-1 3h-1c0-1 1-1 1-2s1-2 1-2h1l2-6c1-1 3-3 3-4h0c-1 1-3 2-4 3l-1-1-2 1v-1c1 0 1-1 2-1-1-1-1 0-2 0v-1c2-1 5-2 7-3h2c1 0 2-1 2-2z" class="E"></path><path d="M484 398c1-1 2-1 3-2l2 1c-1 1-3 2-4 3l-1-1-2 1v-1c1 0 1-1 2-1z" class="Q"></path><path d="M482 564l1-1c1 8 6 16 4 25l-1 6-1-2c0-1 1-3 1-5v-3l-1-5c-1-3-1-4-3-6 1 0 2-1 2-1 0-1 0-2-1-2l-1-6z" class="B"></path><path d="M486 587v-2 3h1 0c0-1-1-2 0-3v3l-1 6-1-2c0-1 1-3 1-5z" class="J"></path><path d="M245 438c2 3 5 5 8 6 4-1 7-2 9-6 1-1 2-3 1-5 0-1-1-3-2-4h-1 0-1c1-1 1-2 1-4h1c0 2 1 4 2 5s1 2 2 3h0 0c0 2 0 4-1 5v1l-3 3 1 1h-3c-2 0-4 2-6 1h-1c-1 0-3-1-4-2h0l-1 1c-1-2-2-2-2-5z" class="G"></path><path d="M162 423c2-2 3-5 4-8 1-1 1-3 2-5 0-2-1-5 1-6h0c2 4 1 10-1 14-1 3-4 6-6 8h-1c-1 1-1 0-1 1v-1h0c1-1 2-1 2-2v-1z" class="W"></path><path d="M381 94v-1c-1-2 0-5 0-7 1-1 2-3 4-4v-1l1 1-1 1c-2 1-2 3-2 4-1 3-1 6 0 8 0 3 2 7 3 10-1-2-2-3-4-4-1-1-2-3-3-3-3-4-5-8-6-13h0c2 3 3 6 5 9 1 2 2 2 3 4l1-1c-1 0-1-1-2-1 1-1 1-1 1-2z" class="O"></path><defs><linearGradient id="R" x1="306.92" y1="124.349" x2="299.451" y2="121.666" xlink:href="#B"><stop offset="0" stop-color="#747371"></stop><stop offset="1" stop-color="#908f91"></stop></linearGradient></defs><path fill="url(#R)" d="M302 115c0-1 0-2 1-2l1 1c-2 5-2 10 1 15 1 3 3 4 5 6l-1 1h-1l-1 1h0c-4-4-6-8-6-13 0-3 0-6 1-9z"></path><path d="M163 410v-1l1 1c0 4-1 10-4 14-1 0-1 1-2 1h0l-1-1c1 0 2-1 2-2v-1l-3 3c-1 1-2 1-3 1h0c3-2 6-5 7-8 0-3 0-6 1-8h1v2l1-1z" class="I"></path><path d="M257 373c6 1 12 0 18 1v1h1c-8 2-15 2-23 1 0-1 0-1 1-1h0l-1-1h1c1 0 2 0 3-1z" class="O"></path><path d="M516 427h1c0 2-2 3-4 3-3 2-6 2-9 3-1 0-3 1-4 2-1 0-2 1-3 1-2-1-2-1-3-2-2-1-2-2-3-3h0 5 12c3-1 7-2 8-4z" class="D"></path><path d="M494 434l1-1 1 1c1 0 1-1 2-1l2 2c-1 0-2 1-3 1-2-1-2-1-3-2z" class="G"></path><path d="M453 566c-1-2-4-4-3-6 0-2 1-3 2-4l4-1c-1 2-3 5-2 7 1 5 5 6 8 8h-2-1-3v-1c0-1-2-1-3-2v-1h0z" class="D"></path><path d="M453 566c2 1 5 3 7 4h-1-3v-1c0-1-2-1-3-2v-1h0z" class="Z"></path><path d="M199 516c3-1 7-3 10-3 1 0 3 1 4 1 3 1 5 2 7 3 4 1 9 4 11 7 1 2 1 3 2 5-1-2-3-4-4-5-4-4-10-7-15-8s-9 0-14 1l-1-1z" class="E"></path><path d="M217 304v-7c0-12 7-19 14-27-1 2-1 4-3 6h0c-2 1-3 3-4 4-4 6-6 11-5 19l1 3c-1 1-1 1-3 2z" class="g"></path><path d="M215 572c1 1 2 1 3 1 8 0 16-2 21-10 0-1 1-4 2-5 0 1 0 2-1 4-1 3-4 7-7 9l-9 3c-8 1-16-1-23-5v-1c4 2 7 3 11 4h2 1z" class="T"></path><path d="M228 276h0c1 0 1 0 2-1v3c1 1 1 4 2 5-1 0-2-1-2 0-2 2-2 5-4 6h0c0-1 0-1 1-2v-2h0c-1 0-2 2-2 3h0c-1 1-1 2-2 3l-1 1h0c1-4 3-7 5-11-1-1-1-1-3-1 1-1 2-3 4-4z" class="C"></path><path d="M228 276h0c1 0 1 0 2-1v3c-1 0-1 1-2 2 1 0 1 0 2 1h-1c-1 0-1 0-1-1l-1 1h0c-1-1-1-1-3-1 1-1 2-3 4-4z" class="R"></path><path d="M220 363l4-1h1c0-1 3-2 4-2-2-2-3-3-4-5 1 0 4 1 5 2v1h-1v1h1c1 1 1 1 1 2v1c0 1 0 1-1 2 1 0 1 1 1 2l-1 1h0l-3 1c-1-1-2-3-3-4-2 0-3 0-4-1z" class="L"></path><path d="M224 364h6c-1 1-2 1-2 2 1 0 1 0 2 1h0l-3 1c-1-1-2-3-3-4z" class="S"></path><path d="M268 405h1v1c1 0 2 1 3 1h1 1c1 0 1-1 2-2h1v-1c0 1 0 2 1 3 0 0 1 1 1 2-1 2-1 2-3 3h-1-1c-1 0-2 1-4 1 0-2 1-2 1-4 0 0-1 0-2-1-2-1-3-2-4-3h3z" class="E"></path><path d="M274 412c0-1 0-1-1-2h0v-1h2v-1l1 1v3h-1-1z" class="V"></path><path d="M433 597v-3h1c1 3 1 6 4 8 1 1 2 1 3 1 1-1 3-1 4-1s3-2 3-3c2 0 2-2 2-3h0c0 2 0 3-1 4l-1 1h-1v1h0l-2 2c-1 0-2 0-3 1-1 0-2-1-3-1h0c-2 2-4 2-7 3 0-1-2-2-2-4h1l1 1c1-1 2-1 3-2h1c0-1-3-4-3-5z" class="I"></path><path d="M431 635l6-1 2 1v1l-1 1s-2 0-2 1l-3 1c-1 0-3 1-4 1-1-1-2-1-3-1-3-2-8 0-12 0l1-1h2 1l1-1h2v-1c3 1 8 0 10-1z" class="L"></path><path d="M315 319c0 1-1 2-2 3-3 2-6 2-8 3-1-1-4-2-4-3s-1-2-1-3c-1-2 0-3 0-4l-2 2c1 1 1 3 1 3-1 0-1 0-2-1 0-2 1-3 2-4 1-2 3-2 4-3-1 2-3 4-2 6 0 2 2 3 3 3 2 1 3 1 5 0 0 0 1 0 1-1h1 0 1s1-1 2-1h1z" class="H"></path><defs><linearGradient id="S" x1="409.904" y1="111.093" x2="422.546" y2="118.67" xlink:href="#B"><stop offset="0" stop-color="#4a4b4b"></stop><stop offset="1" stop-color="#646365"></stop></linearGradient></defs><path fill="url(#S)" d="M408 107l1-1c0 3 0 4 1 6l6 3h0 0c1 1 3 1 5 0h0 0v1h0c1 0 4-1 5-1l2 1h-3-1l2 2c-2 0-5 1-7 1-3-1-9-3-10-5s-1-5-1-7z"></path><path d="M111 442c0 2 1 4 2 6 1 1 1 0 2 2s2 5 4 8 6 8 10 10c2 2 5 4 7 5 1 0 2 1 3 1l-1 1v-1h-2c-1-1-2-1-2-1-3-2-5-3-7-5-1-1-2-2-4-3-1-1-2-2-3-4l-3-3c-1-2-2-4-2-5l-1-1h0c-2-3-3-6-4-9l1-1z" class="O"></path><path d="M342 407l2 1 3 5c2 5 3 10 3 15v3h0-1c-1-7-4-13-6-19v-1-1-1c-1-1-1-1-1-2h0z" class="e"></path><path d="M283 411l1 3c1 3 2 5 5 6 2 5 4 7 9 9l3 3h0s0 1 1 1l-1 1c1 1 2 1 3 2v2 1c-4-7-10-9-15-14-4-4-6-8-6-14z" class="G"></path><path d="M352 91c8 0 16 3 23 6 3 2 5 5 8 7l19 12h-1c-4-2-9-5-13-8-5-3-9-8-15-10-3-2-7-3-11-4-5-1-9-2-14-2-1-1-1 0-1-1h5z" class="i"></path><path d="M174 584c7-2 13-2 19 0h1 0c2 1 6 1 7 1l2 1-1 1h-11v-1l-2-1h-3-2c1-1 2-1 4-1v-1h-5c-2 1-4 2-6 2-3 1-5 1-8 2h-1c1-1 3-1 4-2 1 0 2 0 3-1h-1z" class="Q"></path><defs><linearGradient id="T" x1="187.592" y1="591.861" x2="193.639" y2="587.545" xlink:href="#B"><stop offset="0" stop-color="#7b7b7d"></stop><stop offset="1" stop-color="#929292"></stop></linearGradient></defs><path fill="url(#T)" d="M186 590c2-1 6-1 9-2 8-1 15 0 23 2l-1 1c-10-3-21-3-30 2-3 1-4 3-6 6h-2c1-2 4-4 4-6 1-1 2-1 3-2v-1z"></path><path d="M115 486l1-1c8 5 16 7 25 8 3 0 5 1 8 0 1 0 5-1 6 0v1c-2 1-4 1-5 1h-10 0v-1h-2-1-1c-1 0-1-1-2-1h-1l-3-1h-2c-1 0-1-1-2-1-3-1-6-2-8-4-1 0-2-1-2-1h-1z" class="g"></path><path d="M194 421c1 2 1 3 1 5s0 5-1 7v1c-1 5-4 10-6 14 0 2-2 3-3 4 1-1 3-4 2-5v-2c0-1 1-2 1-3 1-1 2-3 2-5 1-2 2-4 2-6s0-3 1-5v1c0 2 0 5-1 7 0 2-1 3-1 5h0c1-3 2-5 3-8v-10z" class="C"></path><defs><linearGradient id="U" x1="505.263" y1="549.994" x2="512.106" y2="552.326" xlink:href="#B"><stop offset="0" stop-color="#646364"></stop><stop offset="1" stop-color="#7d7c7c"></stop></linearGradient></defs><path fill="url(#U)" d="M505 554h0c0-2 0-5 1-7 1 0 3-2 4-1 2 2 2 4 2 6 0 1 0 4-1 5-1-1-2-2-2-1h-1l-1 1v-1-1h-1v1h0c-1-1-1-2-1-3v1z"></path><path d="M231 633v-2c3-2 7-3 10-4 14-6 26-3 39 3-3 0-6-2-9-2-2-1-5-2-7-3-8-1-17 0-24 3-3 1-5 3-8 4h0l-1 1z" class="Q"></path><path d="M199 510l6-2c1 1 1 1 2 1 1 1 2 1 3 1-5 0-9 2-13 3-2 1-4 1-6 2-1 1-3 2-3 3-4 3-9 7-10 12 0 3-1 6-2 9h0c-1-2 1-8 2-10 1-7 9-13 15-17 2 0 4 0 6-1 1 0 2 0 2-1h-2 0z" class="C"></path><path d="M249 554s0 1 1 2c0 2 1 3 3 4v1 1l1 1v1 1c1 0 0 0 1 1 0 1 0 1 1 2 1 0 2 1 2 1h0c-2 1-4 1-7 0l-1-2v-2-1h-1v-1l-1-1c0-2 1-5 1-8z" class="D"></path><path d="M250 564v-2-1l3 2v1l-2-1v2h-1v-1z" class="C"></path><defs><linearGradient id="V" x1="176.851" y1="376.524" x2="170.294" y2="390.019" xlink:href="#B"><stop offset="0" stop-color="#747675"></stop><stop offset="1" stop-color="#9e9b9d"></stop></linearGradient></defs><path fill="url(#V)" d="M169 372c6 5 10 14 11 22v6s-1 0-1-1v-3c-1-9-6-17-12-23 1-1 1-1 2-1z"></path><path d="M102 581c1 0 1-1 2-1v1c1 0 2-1 2-2l1 1c-1 3-4 2-5 5h0 1c1-1 1-2 2-2l2-2v1c0 1-1 2-2 2h-1v1c2 0 3-1 4-2v-2h1v1c0 1-1 2-1 3v1h0c-1 0-2 1-2 2-1-1-2-2-3-1h-1s0-1-1-1h-2-6 0 7v-1h-6 0v-1l-9-3 1-1c1 1 1 1 2 1s2 1 3 1c2 1 6 1 8 1h1l1-1h1v-1z" class="R"></path><path d="M296 370c1 0 2 0 2-1h1 1l1-1h0c1-1 3-2 5-2l1-1h0l1 1c-1 0-2 1-3 1l-1 1c-1 0-2 0-3 1h0v1c-1 0-2 1-3 1h-2v1h1c-2 1-4 2-6 2l-1 1-7 2-1-1c1 0 1 0 2-1h-3 1l2-1v-1h-1c-1 0-2 0-3-1h4c4 0 8 0 12-2z" class="Y"></path><path d="M283 373h2 3 2s0 1 1 1l-1 1-7 2-1-1c1 0 1 0 2-1h-3 1l2-1v-1h-1z" class="X"></path><path d="M168 328c0 1 0 1 1 1h1v1c0 2 1 3 1 4h1v4c1 1 3 2 4 3s1 1 1 2l-3-2c1 1 2 2 3 2 1 1 1 1 2 1l6 1v1h-4c-3 0-7-3-9-5-3-2-5-6-5-10 0-1 0-2 1-3z" class="N"></path><path d="M169 329h1v1c0 2 1 3 1 4h1v4c1 1 3 2 4 3s1 1 1 2l-3-2h0l-4-4c-1-3-2-5-1-8z" class="E"></path><path d="M142 545h1 1c1 0 1 2 2 3 1 2 3 4 4 6l1 1c0 1 2 3 3 3s1 1 2 1l-1 1c-1-1-3-2-4-2h-1v-1c-2-1-4-2-7-3-2 0-3-1-5-2v-1h2c1 0 2 1 3 2l2-1v-1c-1 0-2-1-3-2 0-1 1-1 1-2v-1l-1-1z" class="I"></path><path d="M332 602c1-4 3-7 6-9 4-3 7-3 11-3-1 2-2 2-3 2s-2 0-3 1h0l1 1h0c-1 2 0 3-1 4h-3c0-1 0-2-1-3-1 1-2 2-3 4v1h-1c0 1-1 2-2 2h-1z" class="V"></path><path d="M61 563c3 6 7 12 12 16 3 3 9 6 13 7s8 2 12 2h1v1c-3 0-8 0-12-1-9-3-18-9-23-18-2-1-3-4-4-6l1-1z" class="j"></path><path d="M475 513v-1c-4-1-7-3-8-7-1-2-1-7 0-9 1-3 3-5 5-6h1c4-1 9 2 12 4v1l-3-2c-4-1-7-3-10-1-2 1-4 2-4 4-1 3-1 7 0 9s3 4 6 5l1 1 6 1-1 1c-1 0-1 0-3 1h0l-2-1z" class="R"></path><path d="M383 640c3-2 6-3 8-5 5-4 9-2 15-1v1h-1c-2 0-5-1-7 0l-1 1c-3 1-5 2-7 4l-1 1-1-1c-1 1-3 1-4 1l-1-1z" class="K"></path><path d="M390 640h-2v-1c3-1 6-4 8-4l1 1c-3 1-5 2-7 4z" class="B"></path><path d="M490 163h1l-2 2c0 1 1 1 2 1l-1 1c-1 1-1 2-2 3l-2-2h0-1l-6 3c-2 1-5 0-8 2-1 0-7 3-8 3s-1 1-2 1h-1c1-1 2-2 3-2l1-2h1c1-1 1-1 2-1v-1h0 3v-1h6c6-1 11-3 14-7z" class="X"></path><path d="M467 171h0c2 0 4 0 6 1l-10 3 1-2h1c1-1 1-1 2-1v-1z" class="R"></path><path d="M485 168l4-3c0 1 1 1 2 1l-1 1c-1 1-1 2-2 3l-2-2h0-1z" class="Q"></path><path d="M262 382l7-3c-2 1-5 3-7 5s-3 4-5 5c-1 2-3 4-4 6 0 1-1 4-2 5l-1 1v-1l1-3h0c-1 0-1 1-2 2 0-2 1-3 2-5h-1v-1h0c3-4 7-8 12-11z" class="M"></path><path d="M250 393c3-3 6-7 10-9h0c-2 3-7 7-7 11 0 1-1 4-2 5l-1 1v-1l1-3h0c-1 0-1 1-2 2 0-2 1-3 2-5h-1v-1z" class="D"></path><path d="M302 519v1c0 1-1 3-1 4v1c-1 1-2 3-2 5-1 2-1 5-1 8 0 2 1 6 1 8-1 1-1 4-1 6v-4c-1 1-1 3-1 4-2-2-2-3-2-5s1-4 1-6c1-5 1-10 2-14 1-3 2-5 4-8z" class="I"></path><path d="M120 564c-2-3-4-7-4-11 0-3 1-5 3-7 1-1 1-1 3-1 0 1 1 2 1 3 1 1 1 4 1 5 0 0-2 1-2 2h0 1c1-1 2-2 2-4s-1-4-2-6c-1 0-2-1-2-1-1-1-1-2-1-3h1c1 2 4 4 5 7 0 2 0 5-1 7-1 1-1 1-3 1l-1-1v-2-1h0c1 0 1-1 1-1 0-1 0-3-1-4 0-1 0-1-1-1h-1c-1 2-2 5-2 7 0 3 2 7 4 10v1h-1z" class="a"></path><path d="M528 367h0c-4-1-8-3-11-5-2-1-5-2-6-3v-1c3 1 5 2 8 3-2-1-5-3-7-3-2-1-2-1-3-2v-1l15 6 1 1 1 1h-1-1 0c3 2 7 3 10 5v1h-1 0c-1-1-3-2-4-2h-1z" class="O"></path><path d="M349 590c5 2 8 6 10 10v1l1 2v3l-1 1h1l1-1c0-1 0-1 1-2 0 2-1 4-2 5s-1 3-2 4h0l-1 1c1-3 1-7 1-10-1-3-2-7-5-9s-6-2-9-1l-1-1h0c1-1 2-1 3-1s2 0 3-2z" class="T"></path><path d="M283 312c5 2 13 6 16 10v1s1 1 2 1v1c1 1 6 6 6 7v1c-2-2-4-3-5-5l-11-9s0-1-1-1c-1-2-2-2-4-3 0-1 0-1-1-1-1-1-2-1-3-2h1z" class="B"></path><defs><linearGradient id="W" x1="329.024" y1="630.135" x2="329.542" y2="634.041" xlink:href="#B"><stop offset="0" stop-color="#9e9d9e"></stop><stop offset="1" stop-color="#bab9ba"></stop></linearGradient></defs><path fill="url(#W)" d="M344 629v3c-2 1-4 1-6 2-6 0-12 1-18 0-1 0-3-1-4-1h0l-1-2c8 2 14 2 21 0l1 1c2-1 5-2 7-3z"></path><path d="M119 373h1v1c1 3 3 4 5 6 0 1 1 1 1 2 1 1 3 2 4 3h1c1 0 1 1 2 1h5 2c1 0 2-1 3 0l1 1h-12c-3 1-5 3-7 5h0l-1 1-2 2h-1l1-1c0-4 3-7 6-9h1c-4-3-9-7-10-12h0z" class="F"></path><path d="M298 113c0-1 0-2 1-2v-1l1-1h1l-1 4v2-2c1 0 1-1 2-1l-1 2 1 1c-1 3-1 6-1 9 0-1-1-2-1-4v1c0 2 0 5 1 8h-1 0c-1-1-2-2-3-2v-7h-1v2-5c1-1 2-3 2-4z" class="N"></path><path d="M300 118c-1 2-1 4-1 6h-1v-6h2z" class="I"></path><path d="M300 113v2-2c1 0 1-1 2-1l-1 2c-1 1-1 3-1 4h-2l2-5z" class="U"></path><path d="M470 204c-1-1-3-3-4-5-3-3-3-6-3-10 1-3 3-7 5-9 3-2 8-3 11-2 0 1-1 1-2 1l1 1h0c1 0 1 1 2 2l2 1v2h-1c-1 0-2-2-2-3-2-1-2-1-3-1-3 0-5-1-8 1-2 1-3 4-4 7 0 4 1 8 3 11 1 1 3 2 4 3l-1 1z" class="C"></path><path d="M361 615h1c2-3 4-7 7-9h0l1 1h-1c-2 2-3 5-5 7-1 2-2 3-3 4v1c-1 0-2 1-2 2v1c-1 1-1 0-1 2-1 1-4 3-5 3l-2-1 2-3v-1s3-7 4-8l1-1c-1 3-2 4-1 7-1 1-1 1-1 2v-1c2-1 5-5 5-7v1z" class="E"></path><path d="M380 112s1 0 2-1c1 1 1 2 2 2h2l-6 2c-6 2-12 4-17 7-1 0-1 0-1-1h0c0-1 1-2 2-3 2-1 3-2 5-2 3-2 7-3 11-4z" class="C"></path><path d="M380 112s1 0 2-1c1 1 1 2 2 2h2l-6 2h-1c-1-1-3 0-4 0-1 1-1 1-3 1 2-1 5-2 7-3h0l1-1z" class="O"></path><path d="M314 286c0-1 0-2 1-3v9c0 3 0 8-2 10-1 1-2 2-3 2h-2c-1-4 6-13 6-18z" class="F"></path><path d="M224 318c2-1 2-1 2-3l1-1v-1h1v1h1 0 1c-2 4-3 7-4 11-2 1-2 3-4 5-2 3-5 5-7 8l-1-1c3-3 6-7 8-11 1-3 1-5 2-8z" class="Q"></path><defs><linearGradient id="X" x1="314.564" y1="554.96" x2="318.726" y2="569.851" xlink:href="#B"><stop offset="0" stop-color="#7f807f"></stop><stop offset="1" stop-color="#979597"></stop></linearGradient></defs><path fill="url(#X)" d="M317 550l4 9c-2 4-1 8-2 12h-1-4c0-2 2-3 2-4 3-3 1-8 1-12h-4l-3-1h1c0-1 0-1 1-1h1c1 0 3 1 4 0v-2-1z"></path><path d="M312 553h1c1 1 2 1 4 1h0c-1 1-3 1-4 1l-3-1h1c0-1 0-1 1-1z" class="W"></path><path d="M267 526c3-2 7-3 10-4 2 0 4 0 6-1s5 0 6 1 2 1 3 3h0-1c-2-2-5-3-7-3h-1 0c3 1 6 2 9 4v1c-1 0-5-2-7-3-8-2-21 3-27 8-3 3-5 6-7 9 2-7 7-10 12-13 1-1 3-2 4-2zm-46-128v1c1 0 1 1 1 2s0 2 1 3l1 5 2 9v2h1v2 1c-1 1-1 3-2 4v5c-2-5-2-11-3-16 0-2-1-4-1-6s-1-3-1-5-1-4-1-6l1 1c0 2 1 5 2 8 0 2 1 4 1 6 1 4 1 8 2 12 1-6 0-10-2-16l-2-12z" class="I"></path><path d="M489 534c-1 2-3 4-3 6 1-2 3-6 6-6 2-1 5 0 7 1v1h0-1c-1-1-2-1-3 0-1 0-1-1-2-1-2 0-5 3-6 5l-1 1v1l3-3c1-1 2-1 2-2l1-1c1 0 1 0 2 1 2-1 2-1 4 0v1h-5s-1 1-2 1c-2 1-4 3-6 5-1 2-2 6-4 6 2-6 3-11 7-16h1z" class="H"></path><path d="M254 460c1-1 1-1 3 0 1 0 2 0 3 1-6 1-10 2-14 6v1l1-1v2h-3l-2-1v2h-1l-1-1c0-1 0-2 1-3s2-2 4-3l1-1h0c2-1 3-1 4-2h4z" class="U"></path><path d="M254 460h0c-2 1-4 2-5 3h-1c-1 1-1 1-2 1l-1 1c-1 1-2 1-3 2v1h0v2h-1l-1-1c0-1 0-2 1-3s2-2 4-3l1-1h0c2-1 3-1 4-2h4z" class="V"></path><path d="M360 630h-1c-3 1-3 4-4 5h-1c0-1 0-2 1-3v-1h-1c0-1 1-2 1-2 2-2 5-2 7-2 1 0 3 1 4 2l-1 1h0c2 2 2 4 2 6 0 1-2 4-2 4-1 1-5 2-6 2h1c2-2 4-3 5-6l1-2c-2-2-3-3-5-4h-1z" class="K"></path><path d="M270 460c1 0 1 1 2 1-7 2-15 4-21 9-2 2-4 5-6 8h0l-3 6h-1c0-1 1-2 1-3v-3c1-2 2-4 3-5 6-5 13-9 20-11l5-2z" class="J"></path><path d="M361 618c1-1 2-2 4-3 1-2 2-5 5-6h1v5l-1 1v1l1 1c-1 0-1 0-1 1-5 1-8 3-12 6 0-2 0-1 1-2v-1c0-1 1-2 2-2v-1z" class="F"></path><path d="M468 536h1 0c0 1 1 1 1 1 0 1 0 1-1 1 1 1 2 1 2 2s0 1 1 2h0l-1 1s1 1 0 1-2-1-2-2v-1c-1 0-2 0-2 1-2-1-3-1-4 0-2 0-3 2-4 3s-1 3-2 3-2-1-2-2l1-2c1-1 1-2 2-3s3-2 4-2l5-2v-1h1z" class="O"></path><path d="M458 541c1-1 3-2 4-2l5-2 2 1c-2 1-3 1-5 1h0c-1 0-1 1-2 1s-2 1-3 1h-1z" class="D"></path><defs><linearGradient id="Y" x1="288.704" y1="298.563" x2="293.06" y2="304.726" xlink:href="#B"><stop offset="0" stop-color="#7e7c81"></stop><stop offset="1" stop-color="#909190"></stop></linearGradient></defs><path fill="url(#Y)" d="M299 299c0-1 1-1 2-1v1c0 1 1 0 1 1-8 5-22 6-31 4h-2l1-1c3 0 6 1 8 0h0-3 2l1-1h3c6 0 13 0 18-3z"></path><path d="M471 173c3-2 6-1 8-2l6-3h1 0l2 2c-6 3-12 6-18 8h-2v-1h1s1-1 1-2h-1c1-1 1-1 2-1l1-1h-1z" class="G"></path><defs><linearGradient id="Z" x1="213.506" y1="602.113" x2="212.838" y2="598.233" xlink:href="#B"><stop offset="0" stop-color="#646566"></stop><stop offset="1" stop-color="#7c7b7c"></stop></linearGradient></defs><path fill="url(#Z)" d="M198 603l-3-3c-1-2-1-4-2-6 2 2 4 4 6 5 5 2 12 0 17 0 6 0 11 0 17 1l-1 1-31 1c-3-1-4-3-6-4 0 1 3 3 4 4l-1 1z"></path><path d="M115 384c1-1 1-2 2-2-1 5-2 10-1 15v1l2 8 2 6c-1 1 0 2-1 3h0-1c-1-2-2-5-2-7-2-7-3-14-1-20v-1h-1c0-1 1-2 1-3z" class="B"></path><path d="M116 408l2-2 2 6c-1 1 0 2-1 3l-3-7z" class="D"></path><path d="M116 398l2 8-2 2v-2c-1-2-2-6 0-8z" class="O"></path><path d="M220 479c0-1 1-2 1-2h1v5c0 4 0 8 2 12 0 2 1 4 2 5v4 1c-1-2-1-3-1-4-1-2-2-3-2-5-1-2-1-4-2-6 0-2 0-5-1-8-1 2 0 6 0 8l1 5c1 1 1 2 2 4 0 2 0 4 1 6 1 1 1 3 1 4v-1c-3-5-6-13-7-19 0-2 0-6 1-8 1 0 1 0 1-1h0z" class="Z"></path><path d="M360 104c-3 1-6 1-8 2-4 2-5 5-6 9-1-1-2-2-2-3-1-2 0-4 1-5 3-4 8-5 13-5 1 1 4 1 5 2h-3z" class="G"></path><path d="M193 326c2-1 4-1 6-2s3-4 5-5h0v2c-1 1-3 3-5 4h0v1l2-1h1c-2 2-5 3-8 4-5 0-11-2-15-5-1-1-2-3-3-4 3 2 4 4 7 5l1-1c3 1 6 1 9 0v2z" class="L"></path><path d="M184 324c3 1 6 1 9 0v2c-3 0-6 0-10-1l1-1z" class="M"></path><path d="M481 532h0c-1-2-2-3-3-4-2-2-5-3-8-1-1 1-2 1-4 1l-1 1c-1 1-2 1-2 0-1-1-1-2-1-2 0-2 0-3 2-3 1-1 4-2 6-2 1 1 2 1 3 1 2 1 4 3 5 3 2 1 4 3 4 5 0 0 0 1-1 0v1z" class="K"></path><path d="M436 568c2-2 4-6 6-6 3-1 9 3 11 4v1c1 1 3 1 3 2v1c-2-1-3-1-5-1-4-1-8-2-13-2l-1 1h-1z" class="G"></path><path d="M442 564h1l1 1h-2-2c1 0 1-1 2-1z" class="Z"></path><path d="M230 293v1h-1c0 1 1 1 1 2v1h0v4 1h0c-1 0-1 1-2 2-1 2-2 5-3 7h-2c0 1 0 2-1 3h0c1-7 3-14 5-21 1 0 1 1 2 0h1z" class="U"></path><path d="M227 302l-1-1c0-1 1-3 1-4 1-1 1-2 2-3 0 1 1 1 1 2v1h0v4h-1v-2c-1 1-2 2-2 3z" class="R"></path><path d="M227 302c0-1 1-2 2-3v2h1v1h0c-1 0-1 1-2 2-1 2-2 5-3 7h-2c1-3 2-7 4-9z" class="Y"></path><path d="M276 600h0c2 4 5 7 8 10 1 1 2 1 3 2 2 2 5 3 8 4h0c1 2 2 3 4 4 1 0 2 1 3 1 3 1 5 1 7 1-1 1-3 0-4 0h-1v1h0c-1 0-4-1-5-1-11-5-19-11-23-22z" class="T"></path><path d="M472 473l1 3c3 6 5 10 11 12 6 3 11 3 17 3-1 0-2 1-3 1-2 0-7-1-8 1-2 0-4-1-5-1-6-3-11-7-13-12l1-1c-1-2-2-3-1-6z" class="H"></path><path d="M287 407c1-4 4-8 7-11 4-2 7-3 10-6 1-3 1-8 4-10 1-1 2-1 4-1-1 1-3 1-4 2l-1 2c-1 3 0 5-2 8-2 1-4 3-6 4-1 1-3 1-4 3-3 2-5 4-7 8v1c0 1 0 3-1 4 0 1 0 3 1 4l1 3v2h0c-3-1-4-3-5-6 0-1 1-2 1-4l1 2v-1c1-1 1-3 1-4z" class="I"></path><path d="M285 410l1 2c0 2 1 5 3 8h0c-3-1-4-3-5-6 0-1 1-2 1-4z" class="Q"></path><path d="M545 336c1 0 1 0 2-1l1 1c-3 3-5 7-7 11-1 1-2 3-3 4l-6 3h-3-1l1-1c4 0 6-5 8-8l3-4 2-2h1c0-1 1-2 2-3z" class="D"></path><path d="M202 421c0 3 0 5-1 8 0 4 0 8-1 11v1 1h-1c1 0 1-1 2-2 0-1 0-3 1-4l1-10c0 3 0 6-1 8-2 15-11 27-23 36-1 1-2 1-4 2 8-6 15-12 20-21l3-6v-1c0-2 1-4 1-6s1-4 1-6c0-3-1-7 0-10h1s0-1 1-1z" class="H"></path><path d="M238 597c-1-1-3-2-4-3-3-2-5-6-5-9 0-2 0-5 2-7 1-2 4-4 6-4 4 0 8 6 11 9h-2-1c-2-3-3-5-6-7h-4c-2 1-4 3-4 5-1 3-1 6 1 8 2 5 9 8 13 11 2 0 3 1 5 2h-1c-1 1-5-2-5-2l-4-1-2-2z" class="E"></path><path d="M234 186c1-3 2-7 4-10 1-4 3-8 5-11 4-6 7-12 10-17 0 1 0 2 1 3h1c0 1-1 2-1 3v1l-1-1 1-1h-1s-1 1-1 2h0l-7 10c-2 4-5 7-6 11-2 4-3 9-4 13l-1-3z" class="B"></path><path d="M218 206h0l1-1c2 0 2 0 3-1h0-1-1v-1c2 0 4-1 6 0v1c-1 0-2 0-2 1v1c1-1 1 0 2 0l1 1v2l2 1-1 1-1 1 1 1h0c-2-1-2-2-4-3-1-1-4-2-5-2h0c0 1 1 1 2 1 2 1 6 4 8 6 0 1 0 1-1 1-2-2-4-4-7-5v1c-2-1-2-1-4-1-1 0-2 0-2 1-1-1-1-1-1-2 0 0 2-1 3-1 0-1 0-1 1-2h1l-1-1z" class="G"></path><path d="M217 209c1 1 3 1 4 2v1c-2-1-2-1-4-1-1 0-2 0-2 1-1-1-1-1-1-2 0 0 2-1 3-1zm10 3c-2-3-5-4-8-6h4c1 1 2 1 3 2l1-1v2l2 1-1 1-1 1z" class="P"></path><path d="M460 424v-1c0-3 1-7 3-10 1-1 5-5 7-5-5 5-5 11-8 17-1 2-1 4-2 6s0 4 1 6c0 1 2 3 3 4l2-2h1c0 1 0 1-1 2-1 0-1 1-2 1-1-1-3-1-3-2-2-3-2-7-1-11v-5z"></path><path d="M199 602c-1-1-4-3-4-4 2 1 3 3 6 4l31-1v1h-3c0 1-1 1-2 2h-2-2 0c0-1-4-1-5-1-7 0-13 3-20 0l1-1z" class="c"></path><path d="M199 602c4 2 10 2 14 0h7-1l-1 1c-7 0-13 3-20 0l1-1z" class="C"></path><defs><linearGradient id="a" x1="192.931" y1="317.314" x2="205.931" y2="316.857" xlink:href="#B"><stop offset="0" stop-color="#0e0d0e"></stop><stop offset="1" stop-color="#29292b"></stop></linearGradient></defs><path fill="url(#a)" d="M200 298c1 0 1 0 2 1 0 1 1 1 1 1 1 0 1 0 2 1s2 3 2 4 0 2-1 3c1 3 1 6 0 8 0 1-1 2-1 4v1c0 2-2 3-4 4l-2 1v-1h0c2-1 4-3 5-4v-2h0c-2 1-3 4-5 5s-4 1-6 2v-2c1 0 2 0 2-1 1 0 2-1 3-1 2-2 6-6 7-9v-4h0c1-3-1-6-3-8-1-1-1-2-2-3z"></path><path d="M200 298c1 0 1 0 2 1 0 1 1 1 1 1 1 0 1 0 2 1s2 3 2 4 0 2-1 3v-3c-1-1-2-3-4-4-1-1-1-2-2-3zm184 324c3 0 5 0 7 1h1 1c4 1 7 3 10 4 4 2 8 2 11 4 0 0-1 0-1 1h-6c-2-1-4-1-6-2-1 0-1-1-2-1l-7-3 1-1c-1 0-1 0-2-1h1c-1 0-2 0-2-1h-2l-4-1z" class="Q"></path><path d="M524 507c-1 0-1-1-1-1 0-1-1-2-1-3v-1l-1-1-1-1h-1v-1c-1-1-2-1-3-2h-1c-1 0-2 0-3-1h-1-3-1c1-2 3-1 4-2l1-1h3v1h2 0c1 0 1 0 2 1h1c2 1 3 2 4 4h0l1 1s0 1 1 1l-1 1c1 1 1 2 1 3s0 1-1 2v1l-1 1v-2z" class="P"></path><path d="M524 507h0c0-1 1-1 0-1v-1-1c-1-1-1-2-2-2v-1s0-1 1-1h0l2 2h0c1 1 1 2 1 3s0 1-1 2v1l-1 1v-2z" class="d"></path><defs><linearGradient id="b" x1="278.502" y1="440.276" x2="286.923" y2="443.979" xlink:href="#B"><stop offset="0" stop-color="#4e4e4d"></stop><stop offset="1" stop-color="#6b6a6b"></stop></linearGradient></defs><path fill="url(#b)" d="M288 431v-3-1c1 2 2 4 2 5v8c-2 1-4 4-4 6-2 2-6 5-9 5h-3l-1-1c6-2 10-5 13-10 1-2 2-4 1-7 0-1 1-1 1-2z"></path><path d="M288 431v-3-1c1 2 2 4 2 5v2c-1-1-1-2-2-3z" class="B"></path><path d="M397 636l1-1c2-1 5 0 7 0 3 2 6 2 10 3l-1 1c-4-1-11-3-15 1-1 0-1 1-2 1 0 1-1 2-3 2l-4-1h-1v-1l1-1c2-2 4-3 7-4z"></path><path d="M437 339h1c1-2 3-3 4-5 5-4 9-6 15-6-1 0-1 1-2 1 1 0 2 0 3 1l-7 2c-3 0-6 3-8 5l-2 3h0c-1 0-2 1-3 2h0v-2l-3 4v-1 1c-1 2-2 3-2 5 0 0 0 1-1 1h0v-3l2-4c1-1 2-3 3-4z" class="B"></path><path d="M438 340c0-1 1-1 2-2 2-2 4-4 6-5 1-1 1-1 2-1h0c-3 2-6 4-7 8-1 0-2 1-3 2h0v-2z" class="P"></path><path d="M455 329c1 0 2 0 3 1l-7 2c-3 0-6 3-8 5l-2 3h0c1-4 4-6 7-8h0l7-3z" class="F"></path><path d="M284 232l1-1c1 1 2 3 4 5v-1c1 1 2 2 4 2 0 0 1 1 2 1v1h0 0l3 3c1 0 3 0 4 1 2 1 5 2 8 2l3 1v1h-3l-10-3c3 2 7 3 11 4v1c-2 1-3 0-5-1-4-1-7-4-11-7v1 1l-1 1-5-5c0-1-1-2-2-3s-2-3-3-4z" class="J"></path><path d="M284 232l1-1c1 1 2 3 4 5 1 2 3 5 6 6v1l-1 1-5-5c0-1-1-2-2-3s-2-3-3-4zm-73 225l1 1h0c-2 4-3 7-5 10-5 9-14 15-23 18-8 3-17 4-25 5-4 0-8 2-12 1h-2v-1h1c4 0 7 0 10-1 13-1 26-2 37-9 9-5 15-14 18-24z" class="a"></path><path d="M456 443v5c2 6 5 10 9 13 1 1 2 1 3 2s1 3 1 5c-1 2-1 6-1 8 1 1 2 3 1 5l-1 1-1-1c1-1 1-2 1-4-1 0-1 2-1 3-1-1-1-2-2-2v1c1 3 4 8 7 10h0 0 0c-1 0-1 0-2-1-3-2-8-8-6-12 0-2 1-3 2-4 1-2 2-4 2-7-1-3-3-3-5-4-5-4-8-12-9-18h2z" class="Z"></path><path d="M303 333l1 1c1 1 2 4 2 6 1 2 1 4-1 6l-1 2h1c2 0 5-4 6-6 0 2-1 3-1 5-2 2-7 5-10 6-3 0-6 2-9 3-2 0-5 0-7-1-2-2-3-4-3-7v-2c0-1 2-3 3-3 1-1 2-1 3 0 1 0 2 0 2 1l-1 1-1-2h-2c-1 1-2 1-3 3-1 1 0 3 0 4 1 2 2 3 3 4 4 1 8 1 11-1 2-1 2-2 4-3 2-2 5-3 6-6s-1-8-3-11z" class="F"></path><path d="M114 434h1v3h0c1 1 1 3 1 4 1 2 2 3 2 5 2 3 3 7 5 10 1 2 4 4 6 6 3 3 5 5 9 7 5 2 10 3 15 4h0v1h-1c-8-1-16-4-22-9-1-1-2-2-2-3-1 0-1 0-2 1 0-1-1-2-1-2l-4-4c-2-3-2-6-4-9-1-5-3-9-3-14z" class="U"></path><path d="M319 448v4c0 1 0 2 1 2h0 0v-3 1c1 1 1 2 1 4h1v1 2c0 1 0 2-1 3l-1 9c-1 2-1 4-1 6h-1c-1 1-1 2-1 3h-1l1-17v-6c1 1 0 3 1 5h1c0-4-1-9 0-14h0z" class="K"></path><path d="M319 448v4h0v14c0 2-1 5-2 7v-10-6c1 1 0 3 1 5h1c0-4-1-9 0-14h0z" class="J"></path><path d="M320 451v1c1 1 1 2 1 4h1v1 2c0 1 0 2-1 3l-1 9c-1 2-1 4-1 6h-1c0-4 1-7 2-10 0-4 1-9 0-13v-3z" class="U"></path><path d="M472 190c2 0 4 1 6 2l-3 2c1 1 2 3 3 3 2 0 3 0 5-1h0 1l1-1h0l-1 2 1 1s-1 0-1 1c-3 1-6 2-9 1s-4-2-5-4v-4c1-1 1-2 2-2z" class="D"></path><path d="M472 192h1v3c0 1 2 2 2 3-1 0-2-1-2-2-1 0-1 0-1-1v-3z" class="G"></path><path d="M470 196v-4c1-1 1-2 2-2v2h0v3l-2 1z" class="N"></path><path d="M294 580h0s1 0 1-1c1-4 4-8 4-12 1-3 2-5 2-8v-6h0l1 2c0 4 0 7-1 11 0 2-1 4-1 6-1 2 0 5 1 7h1l-1 1-1 1v1h-1v3 2h-1 0c-1-4-1-7-1-11-2 4-2 7-5 11l-1-1v1h-1c1-1 1-1 1-2l1-1v-1l1-1c0-1 0-1 1-2z" class="Q"></path><path d="M465 513c2 0 3 0 5 1l1 1c2 0 4 1 6 3l-1 1 1 1h-1c-1-1-3-2-5-3l-1 1h-3-1v1h-3v-1h3v-1c-2 1-3 0-5 0 0-1 0 0-1 0s-2 1-3 2v3c1 0 1-1 2-2h0v3c-1 0-1 0-1 1-1-1-2-1-2-1v-4c1-4 4-2 7-4h-2c-2 1-4 1-5 2-1 2-1 4-1 5v1h-1v-2c0-2 1-4 1-5 2-1 5-2 8-2h2v-1z" class="R"></path><path d="M276 207h1 1c1 2 2 4 2 7 2 4 3 8 5 11 0 2 2 4 2 5v1l2 4v1c-2-2-3-4-4-5l-1 1-1-1c0-2-2-5-3-7-1-5-3-11-4-17z" class="F"></path><path d="M432 99c-4-9-11-13-19-17-2-1-3-1-5-3 4 1 7 2 10 3 7 3 12 8 16 13 0 1 1 3 1 4-1 2-1 3-1 5 0-2-1-3-2-5z" class="B"></path><path d="M549 357c-3 0-7 0-10-1-2-1-4-1-7-2 5-1 9-3 13-4 2 0 4-1 6-1v1s-1 0-2 1l-9 2h0 2c3 0 7 0 9-2v-1c1 1 1 1 1 3 0 1 0 2-1 3-1 0-1 0-2 1z" class="D"></path><path d="M542 353c3 0 7 0 9-2v-1c1 1 1 1 1 3 0 1 0 2-1 3-1 0-1 0-2 1-1 0-2 0-2-1h0c-2-1-5 0-7-1h3-4v-1s2 0 3-1z" class="E"></path><path d="M543 355c3 0 5 0 8-2 0-1 1-1 1-1-2 2-3 3-5 4-2-1-5 0-7-1h3z" class="d"></path><path d="M362 604c0-1 0-2-1-3 1-1 1-2 1-3h3c1 1 1 2 2 3 0 1 0 3-1 4-2 3-4 6-5 9 0 2-3 6-5 7v1c0-1 0-1 1-2-1-3 0-4 1-7h0c1-1 1-3 2-4s2-3 2-5z"></path><path d="M358 613c1 0 1 0 1-1 1-1 2-3 3-4 1-2 1-4 3-5h1c0 1-2 2-2 3-3 5-5 10-7 14-1-3 0-4 1-7h0z" class="Q"></path><defs><linearGradient id="c" x1="210.301" y1="545.313" x2="190.511" y2="552.114" xlink:href="#B"><stop offset="0" stop-color="#898b8c"></stop><stop offset="1" stop-color="#ababac"></stop></linearGradient></defs><path fill="url(#c)" d="M201 569l-1-1c-4-2-8-7-8-11-1-5-1-10 2-15 2-3 6-4 10-5 1 0 3 0 4 1-1 1-2 0-4 1-3 1-7 2-9 6s-2 9-1 13 4 8 7 10v1z"></path><defs><linearGradient id="d" x1="252.146" y1="162.907" x2="264.679" y2="167.53" xlink:href="#B"><stop offset="0" stop-color="#414140"></stop><stop offset="1" stop-color="#626264"></stop></linearGradient></defs><path fill="url(#d)" d="M258 148c-2 8-2 17 3 24 0 1 1 2 2 3v2l-1-1-1 1v1h0 0v2 1c-3-6-7-12-7-19 0-2 0-5 1-7h0l-1-1c0-1 1-2 1-3v-1l3-2z"></path><path d="M307 101c1-1 2-2 4-2 1 0 1-1 2-1h2c-3 2-7 4-9 8-2 2-3 4-4 6-1 0-1 1-2 1v2-2l1-4h-1l-1 1v1c-1 0-1 1-1 2l-2 3h0l-1-1h-1v1l-2 2c0-1 0-1 1-1v-2c1-1 2-3 4-4 1-2 2-3 3-5 0-1 1-1 2-2 0-1 1-2 1-2 1-1 2-1 3-2l1 1z" class="g"></path><path d="M303 102c1-1 2-1 3-2l1 1c-2 1-4 2-5 4v1c2-1 4-4 6-5-2 3-5 5-7 8h-1l-1 1v1c-1 0-1 1-1 2l-2 3h0l-1-1h-1v1l-2 2c0-1 0-1 1-1v-2c1-1 2-3 4-4 1-2 2-3 3-5 0-1 1-1 2-2 0-1 1-2 1-2z" class="B"></path><defs><linearGradient id="e" x1="296.429" y1="630.289" x2="308.278" y2="622.069" xlink:href="#B"><stop offset="0" stop-color="#535455"></stop><stop offset="1" stop-color="#979493"></stop></linearGradient></defs><path fill="url(#e)" d="M288 618c9 6 17 11 27 13l1 2h0c1 0 3 1 4 1h-3 0c-2-1-4-1-5-1-3 0-5-1-7-2-3 0-6-3-8-4-3-2-7-5-9-7v-2z"></path><path d="M437 93c-2-3-7-5-8-9 1-1 1-1 2 0h0s0 1 1 1c2 2 6 5 8 8l2 2c1 2 2 5 2 6-1 1-1 3-1 4-1 3-3 6-6 7h-1c2-2 4-3 5-6v-1h-1-1l1-1v-1c0-2-1-4-2-6 0-1-1-2-1-4z" class="b"></path><path d="M439 95h1c2 1 2 3 2 5h0c-1 0-1-1-1-2-1-1-1-2-2-3z" class="Z"></path><path d="M437 93c1 1 2 1 2 2 1 1 1 2 2 3 0 1 0 2-1 2v4-1c0-2-1-4-2-6 0-1-1-2-1-4z" class="S"></path><path d="M225 407h1c1 5 3 10 4 16 0 3 0 6 1 10 0 2 2 4 2 6h-1l-3-9c0 4 1 6 3 10-4-2-5-3-7-6 0-2 0-1 1-3v-4c1-2 1-3 1-4v-1h1c0-1 0-2-1-3l-2-12zm74-85c5 4 11 7 14 12 1 3 2 8 3 11l-4-7c0 1 0 2 1 3 0 4-1 6-2 9 0 2-1 3-2 5h0-1l-1-1c2-2 3-3 3-5 1-2 1-4 1-6h0c0-4-2-7-4-10v-1c0-1-5-6-6-7v-1c-1 0-2-1-2-1v-1z" class="J"></path><path d="M510 365c2 7 1 15-4 21-3 5-7 9-11 14-4 4-9 9-8 16 1 4 3 8 4 13h0c-1-1-1-3-2-5-1-4-4-7-3-12 2-8 10-16 15-21 2-2 4-4 5-7 3-3 4-9 4-13-1 2-2 5-2 6l-1-1c1-4 1-7 2-11v1c1 0 1 0 1-1z" class="H"></path><path d="M234 292h1l1 1-1 1 1 1h2v1h-1c0 2 2 3 3 4-2 0-4-3-5-4l1 2c0 1 1 1 2 2l2 2 1-1c1 1 1 1 2 1-2 3 0 4 0 6v1 1 1h-2-1-1c-1 0-3-1-4-2s0-5 0-8l-1-9z" class="G"></path><path d="M237 301c1 0 3 2 4 2v2c-1 0-2-1-2-2l-1 1c1 2 4 3 4 5l-1 1c-1 1-3 0-4 0 0-1 0-1-1-1-1-1 0-2 0-4h1c0 1 1 2 1 3 1 0 2 1 3 1v-1c-2-1-3-3-5-4v-1c0-1 0-1 1-2z"></path><path d="M302 132c1 3 3 6 5 9 5 6 13 13 13 21-1 0-1 0-1 1 0 2 0 7-2 9v-1c0-4 1-7-1-11 0-1 0-2-1-3l-2-4v-1c0-2-3-4-5-5l-2-3-1-2-4-8 1-2z" class="C"></path><path d="M313 152c3 2 5 8 5 10 1 1 0 1 1 1 0 2 0 7-2 9v-1c0-4 1-7-1-11 0-1 0-2-1-3l-2-4v-1z" class="S"></path><defs><linearGradient id="f" x1="320.717" y1="89.666" x2="328.625" y2="99.802" xlink:href="#B"><stop offset="0" stop-color="#858484"></stop><stop offset="1" stop-color="#a5a5a7"></stop></linearGradient></defs><path fill="url(#f)" d="M302 101c11-6 24-10 36-10h0 1v1h-1 1l-20 6h-4-2c-1 0-1 1-2 1-2 0-3 1-4 2l-1-1c-1 1-2 1-3 2l-1-1z"></path><path d="M358 102c1-1 3-1 5-1 4 1 9 2 14 4l11 5h-2c-1 0-2-1-3 0-1 0-14-2-14-3-1-1-3 0-4-1l-1 1v-2-1h-3-1 3c-1-1-4-1-5-2z" class="Z"></path><path d="M364 105l7 1h-6l-1 1v-2z" class="Y"></path><path d="M174 357c2 1 4 2 6 2 6 2 13 0 20-2 2-1 3-2 5-4 1 0 2-2 3-2h1c0 1 1 2 0 2-1 2-2 2-3 3 1 0 2 0 2-1l1 1c-1 1-2 2-4 2-2 1-5 2-7 3h-7c-7 0-12 0-17-2l-1-1 1-1z" class="G"></path><defs><linearGradient id="g" x1="265.729" y1="424.061" x2="276.468" y2="426.366" xlink:href="#B"><stop offset="0" stop-color="#6c6b6a"></stop><stop offset="1" stop-color="#838384"></stop></linearGradient></defs><path fill="url(#g)" d="M267 415c5 4 8 6 9 13 1 3 0 6-2 8-1 2-3 4-6 5v-1l-1 1v-1s0-1 1-2h-1v-1-3h4 0 1c0-1 1-1 2-2 0-3-1-8-3-11-2-2-4-3-5-5h0l1-1z"></path><path d="M271 434h0 1c0-1 1-1 2-2v2c-2 3-3 5-6 6l-1 1v-1s0-1 1-2h-1v-1-3h4z" class="Y"></path><path d="M271 434h0 1c0-1 1-1 2-2v2c-1 0-2 1-2 1-2 1-3 3-5 2v-3h4z" class="f"></path><path d="M154 428c3-1 5-2 8-5v1c0 1-1 1-2 2h0v1c0-1 0 0 1-1h1l-3 2v1c2-1 3-2 5-3h2v1c1-1 2-1 3-2h1l-2 2h-1l1 1c-1 1-1 1-2 1l-1 2c-3 1-6 1-9 1-3 1-7 0-10 0v-1h0c0-1 5-2 6-2s1-1 2-1z" class="T"></path><path d="M146 431h0 3l1 1c1 1 4 0 6 0-3 1-7 0-10 0v-1z" class="g"></path><path d="M166 426v1c1-1 2-1 3-2h1l-2 2h-1l1 1c-1 1-1 1-2 1-3 0-5 1-8 2-1-1-1-1-2-1h0 0c1-1 2-1 3-1h0c2-1 3-2 5-3h2z" class="U"></path><path d="M226 264l1 1c-1 2-2 4-2 6l-1 2c-2 2-4 6-5 9s-5 14-7 16h-2v-1c2-2 3-4 4-7-1 3-3 6-6 7l-3-3c3-2 12-9 13-12 0-1 0-2 1-3 3-5 5-10 7-15z" class="F"></path><path d="M524 361c4 1 9 3 12 5h1v-1c-2-2-4-3-7-4l-13-5c-2-1-9-3-10-5 3 1 6 2 9 4 2 0 4 1 6 1h10v2c1 1 3 3 5 4h0v1c0 2 2 4 2 6-2-1-4-2-6-4-3-1-5-1-7-2l-1-1-1-1z" class="G"></path><path d="M537 362c-3-2-7-2-10-4h1 1 0 3c1 1 3 3 5 4h0z" class="K"></path><path d="M116 511h0 1c0-1 1-2 2-2v-1c2-1 3-2 5-3l1 1c-5 2-9 5-10 10-1 4-1 9 1 12 1 2 3 4 5 4h1l1-1c-2 0-3 0-4-1v-1h1l1 1c2 0 2-1 3-2 0 1-1 2-1 3h0c1-1 3-4 4-4l2 2c-1 1-2 3-4 4-1 1-3 0-5 0-3-1-6-3-7-6-1-2-2-5-2-7 1-1 1-2 2-4l1-3 2-2z" class="T"></path><path d="M470 466c0-3 1-6 2-8 5-9 12-16 23-19l2 1c-1 1-4 4-6 4l-1 1-1-1c-1 1-2 3-3 4v-1c0-1-1-1-1-2-1 0-1 1-2 1v2 1c-3 2-7 5-9 8h1c-2 4-3 8-4 12v-3h0v-1l-1 1z" class="F"></path><path d="M112 416h1v1 8-1c1 2 1 3 1 5 0 1-1 2 0 4l-1 1c0 2 0 3 1 4v1c1 4 2 6 3 9 0 2 1 3 1 5 1 1 2 3 3 5-1 0-2-1-2-2l-3-6c-1-3-3-5-4-8-2-5-3-13-1-19-1-1 0-3 0-4v3h1v-4-2z" class="E"></path><path d="M111 419v3h1c0 2 0 3-1 5 0 1 1 1 0 2v2c-1-2 0-6 0-8-1-1 0-3 0-4z" class="U"></path><path d="M113 425v-1c1 2 1 3 1 5 0 1-1 2 0 4l-1 1h-1v-2c1-1 1-2 1-3v-4z" class="W"></path><path d="M179 294c1-1 4-2 6-2h1c1 0 3-1 5-1 8 0 14-1 21-6 2-1 3-3 5-4v1c-3 4-10 8-14 9-2 1-5 1-6 2h-2 0l2 2v1l-3-2h-1-2c-1 1-3 0-4 0-2 1-4 2-5 2h-1c-1 0-2 1-3 1h0c-2 2-3 3-4 5l-1 1s-1 0-1 1h0l-1-1 2-4c2-2 3-3 6-5z" class="Q"></path><path d="M187 294s1 0 1-1h1c1-1 4 0 6 0l2 2v1l-3-2h-1-2c-1 1-3 0-4 0z" class="G"></path><path d="M269 185c0 1 1 2 2 3l1 1 7 14v3h0 1c0 1 1 2 2 3l-1 1c-1-1-2-1-3-2v-1h-1-1s-1-3-1-4v1l-1-3h-1v2c-1-4-2-7-3-11-2-2-2-4-3-6l2-1z" class="C"></path><path d="M270 192s1 1 1 2h1l1 3c1 2 2 4 2 6v1l-1-3h-1v2c-1-4-2-7-3-11z" class="B"></path><path d="M208 532v-1-3s-1 0-1-1c-1 0-1 0-2-1v-1c1-1 2-2 3-2 2-1 4-1 5 0l1 1h-1l-1-1c-1 0-4 1-5 1l5 1h1c2-1 4-3 5-5l1-1 1-1c1 1 2 2 2 3v1c0 1 1 3-1 4h0c-1-2 1-3-1-4h0v1l-1-1h0c-1 3-2 5-5 7-1 2-3 3-5 4l-1-1z" class="F"></path><path d="M208 532v-2h2v-1c2 0 3-2 4-3v1c0 1-1 2-2 2l1 1 1-1h0c-1 2-3 3-5 4l-1-1zm82-157h2c-4 1-6 2-9 5-1 3-2 5-3 9 1 4 1 8 1 12-2-2-5-5-5-8-1-3 0-8 2-11 1-1 2-2 2-3 1-1 2-2 3-2l7-2z" class="U"></path><path d="M280 389h0c-1-1-1-2-1-3v-1c1-1 1-1 1-2l1-1h0c0-1 1-1 2-2h0c-1 3-2 5-3 9z" class="V"></path><path d="M536 330l1-1c1 1 1 1 1 2 0 5-2 13-6 17-1 2-3 3-6 4l-1-1c2 0 3-1 4-2 3-2 3-6 3-9s0-9 1-11c1 0 2 0 3 1z" class="C"></path><path d="M532 340c0-3 0-9 1-11 1 0 2 0 3 1v4c0 1 0 1-1 2 0 1-1 5-1 5v2h-1v-2h0v1l-1-2h0z" class="B"></path><path d="M438 567c5 0 9 1 13 2 2 0 3 0 5 1h3 1 2l2 2h-2l1 2-4-1h-1c-2 0-4-1-7-1-1-1-3-1-5-1-1 0-3 1-4 1v-1h-2c-1 1-3 2-4 1h-1c0-2 1-3 1-4h1l1-1z" class="L"></path><path d="M457 572h5l1 2-4-1-2-1zm-20-4l1-1 1 1h5c-2 1-5 1-6 2l-1 1v-1-2z" class="D"></path><path d="M436 568h1v2 1c2 0 3 0 4-1h1v1h-2c-1 1-3 2-4 1h-1c0-2 1-3 1-4z" class="C"></path><path d="M446 571c3 0 9-1 11 1l2 1h-1c-2 0-4-1-7-1-1-1-3-1-5-1z" class="G"></path><path d="M171 364l1-1c-1-1-4-2-6-3h-1v-2c1 1 2 2 3 2h1c9 4 17 4 26 3 2 0 5 1 7 1l1 1c-1 0-2 1-3 1h-8l-1 1h-6c-5 0-10-1-14-3z" class="Q"></path><path d="M544 334c2-2 6-6 9-7 7 0 17 0 22 4 2 2 4 5 4 8h0c1 4 0 7-1 10v-2-4c1-3 0-7-3-9-3-3-10-5-15-5-6 0-11 3-15 7-1 1-2 2-2 3h-1l2-5z" class="J"></path><path d="M318 410v-1-1l-1-1 1-1c5 3 8 7 11 12 1 1 2 3 3 5v1c0 1 0 1-1 1h0-1l1 1c-2 3 0 6 0 9v1c-2-6-2-12-6-17-3-3-4-6-8-8l1-1z" class="e"></path><path d="M318 410c1 1 3 2 4 3 1 2 2 3 3 5 0 0 0 1 1 1 0-4-3-7-6-9-1 0-1-1-1-1 1 0 3 1 4 2 3 3 4 7 6 11 0 1 1 3 1 3l1 1c-2 3 0 6 0 9v1c-2-6-2-12-6-17-3-3-4-6-8-8l1-1z" class="L"></path><path d="M264 393c3-7 10-14 17-15h0c-2 1-4 3-7 4-4 3-7 8-7 13-1 2 0 4 0 5l1 1c0 1 2 3 3 3s2 1 3 0 2-4 3-5v-1c1 2 2 4 2 6 1 1 1 2 1 4h0l-1 1c0-1-1-2-1-2-1-1-1-2-1-3v1h-1c-1 1-1 2-2 2h-1-1c-1 0-2-1-3-1v-1h-1c-1-1-2-3-2-4-1-1-1-3-1-5v-1c0-1 0-1-1-2z" class="C"></path><path d="M248 583c1 1 3 2 4 3 3 2 11 5 11 9v1h-3c-2-1-3-2-4-3-2-2-3-3-5-4v1h0c-1 0-1 1-1 2h-1-2l-1-1h-1-1l2-2c0-1 0-2-1-3l-1-1c1-1 1-1 1-2h1 2z" class="R"></path><path d="M517 525h0c-2-5-6-7-9-11-2-2-3-3-3-5l2-2c1 0 1 1 1 2 1 1 1 2 2 2 4 6 10 10 12 16 1 3 1 6 1 9l-1-1c0 1 0 2-1 3-1-5 0-9-2-13h-1 0-1z" class="I"></path><path d="M519 525l-3-5c3 3 4 5 6 8v-1c1 3 1 6 1 9l-1-1c0 1 0 2-1 3-1-5 0-9-2-13z" class="Z"></path><path d="M302 579v1c3 0 3 2 5 0 1 0 1 2 1 3l1 1c2 1 4 3 4 6 0 1 0 3-1 4s-1 1-3 1c1-1 2-1 2-2v-1c-1 1-1 1-2 1-1-1-1-1-2-3h0c-3-3-6-5-7-8v-1l1-1 1-1z" class="P"></path><path d="M309 587h1l1 2v1h-2v-3z" class="T"></path><path d="M309 584c2 1 4 3 4 6 0 1 0 3-1 4s-1 1-3 1c1-1 2-1 2-2v-1c-1 1-1 1-2 1-1-2-2-4-3-5h2c0 1 0 1 1 2h0l2 1c0-1 0-1 1-2l-1-1c-2-2-2-2-2-4z" class="V"></path><path d="M302 579v1c3 0 3 2 5 0 1 0 1 2 1 3 0 0-2 1-3 2h-2l-2-2c0-1 0-2-1-2l1-1 1-1z" class="H"></path><path d="M301 583l1-1h2v1h-1v1h1c1 0 1 1 1 1h-2l-2-2z" class="F"></path><defs><linearGradient id="h" x1="245.856" y1="428.283" x2="238.259" y2="427.316" xlink:href="#B"><stop offset="0" stop-color="#717171"></stop><stop offset="1" stop-color="#939394"></stop></linearGradient></defs><path fill="url(#h)" d="M239 419s1-1 1-2v-2h0 1v4c0 7 0 13 3 19h1c0 3 1 3 2 5l1-1h0c1 1 3 2 4 2h1c2 1 4-1 6-1h3l3-3c0 2 0 2-1 3s-3 1-4 1l-7 2c-1 0-2-1-3 0-1-1-2-2-3-2l-2-2h0v1l-1 1c-5-7-6-16-5-25z"></path><path d="M257 389l1 1c-6 7-8 15-8 25h0v2l-1 1c1 1 1 2 1 3h-1v-1l-1 1v-1c0-1 0-1-1-3-1-1 0-3 0-5-1 0-1 0-1-1v-2h-1c0-3 1-4 1-6 1-1 1-1 1-2 1-1 1-2 2-2 1-1 1-2 2-2h0l-1 3v1l1-1c1-1 2-4 2-5 1-2 3-4 4-6z" class="N"></path><path d="M247 412v-2c1 0 1 1 1 2h1v-2h1c0 2-1 3 0 5h0v2l-1 1c1 1 1 2 1 3h-1v-1l-1 1v-1c0-1 0-1-1-3-1-1 0-3 0-5z" class="B"></path><path d="M248 412h1c0 1 0 2-1 4v-4z" class="G"></path><path d="M247 412v-2c1 0 1 1 1 2v4 2c1 1 1 1 1 2l-1 1v-1c0-1 0-1-1-3-1-1 0-3 0-5z" class="J"></path><path d="M409 328h4l-4 1c-3 1-5 3-7 4-1 0-3 2-4 3l-1 1c0 1-1 2-2 3 0 1-1 1-1 2l-1 1-1 2v2l-1 1c0 2 0 3-1 5h0c0 1 0 2-1 3-1-1-1-1-2-1 1-1 1-2 1-2 1-1 1-1 1-2 0-4 3-10 5-13h-1l-1-1 2-2h0c-1-1-1 0-2 0h0l3-3h2c1-1 2-2 3-2 3-1 7 0 9-2z" class="X"></path><defs><linearGradient id="i" x1="309.729" y1="621.385" x2="309.274" y2="617.049" xlink:href="#B"><stop offset="0" stop-color="#777678"></stop><stop offset="1" stop-color="#919090"></stop></linearGradient></defs><path fill="url(#i)" d="M320 616c1 0 3 1 3 2 0 2-1 2-2 3v-1c-4 1-8 2-12 2-2 0-4 0-7-1-1 0-2-1-3-1-2-1-3-2-4-4 5 2 12 3 17 2l5-1c1 0 2-1 3-1z"></path><path d="M320 616c1 0 3 1 3 2 0 2-1 2-2 3v-1l1-1v-1h-1c-2-1-3 0-4-1 1 0 2-1 3-1z" class="T"></path><path d="M497 415c1-1 1-1 2-1 1 1 3 2 4 2l2-1v1 1 2l-1-1-1 1c0 2 4 5 4 6l-1 2 1 1h-1c-4-1-7-2-9-5-1-1-1-2-1-3-1-2 0-3 1-5z" class="K"></path><path d="M500 422v-2h-1v-1h1 0 2c0 1-1 2-1 3h-1z" class="N"></path><path d="M497 423c1 0 2 0 3 1v-1c2 0 4 2 6 4l1 1h-1c-4-1-7-2-9-5z" class="R"></path><path d="M497 415c1-1 1-1 2-1 1 1 3 2 4 2l2-1v1 1c-2 1-4 1-5 2h-1v1h1v2c0-1 0-1-1 0h0-1v-2-1c0-1 0-2 1-3h0l-1-1-1 1v-1z" class="B"></path><path d="M578 349h0c-2 3-4 5-7 6s-7 1-10-1c-2-1-4-4-4-6-1-1-1-3 0-4s2-2 3-2l3 3v1l-2-1 1 2v1l-3-2c0 2 1 3 2 4l1 1h1c2 1 6 2 8 1s5-3 7-5v2z" class="G"></path><path d="M559 346h0-1c1-1 1-1 2-1h1l1 2v1l-3-2z" class="F"></path><path d="M209 305l1-1 2 1 3 3c2 1 1 2 1 4h0c-1-1-2-1-3-2v2c0 1 0 4-1 5l-1 1c-1-1-1-1-1-2v-1c-1 4-5 8-8 10h-1c2-1 4-2 4-4v-1c0-2 1-3 1-4 1-2 1-5 0-8 1-1 1-2 1-3l1 1h1v-1z" class="B"></path><path d="M209 305c1 1 2 1 2 2v1c-1 0-2-1-3-2h0 1v-1z" class="J"></path><path d="M210 315h0v-4c2 1 0 2 1 3l2-2c0 1 0 4-1 5l-1 1c-1-1-1-1-1-2v-1z" class="K"></path><path d="M190 378l5 1 1-1h1l2 1h2l3 2c8 7 9 20 10 30 0 1-1 3-1 5 0-6-1-11-3-17l-3-9c1 1 2 3 3 5l2 5h0c0-3-2-8-4-10-4-6-12-10-18-11v-1z" class="F"></path><defs><linearGradient id="j" x1="246.282" y1="206.055" x2="236.257" y2="221.813" xlink:href="#B"><stop offset="0" stop-color="#646463"></stop><stop offset="1" stop-color="#828282"></stop></linearGradient></defs><path fill="url(#j)" d="M234 207h0c0-3 0-6 1-8h1c1 0 2 1 3 2-1 3-1 6-2 9-1 2 0 3 0 4s1 1 1 2c1 4 4 8 7 10l2 1h1c-1 1-1 1-1 2h-1s-1-1-2-1c-6-3-10-9-11-15v-1l1 1c0 1 1 2 1 3v-3c-1-2-1-4-1-6z"></path><defs><linearGradient id="k" x1="314.325" y1="232.382" x2="289.434" y2="232.025" xlink:href="#B"><stop offset="0" stop-color="#7a7a7b"></stop><stop offset="1" stop-color="#9c9b9c"></stop></linearGradient></defs><path fill="url(#k)" d="M309 213c3 3 6 7 6 12 1 3 0 7-2 9-2 3-5 4-8 5h-7-1-2v-1c-1 0-2-1-2-1-2 0-3-1-4-2l-2-4v-1c2 2 5 5 7 6 4 1 9 1 13 0 2-1 4-4 5-6 1-3 0-5-1-7l1-1 1 2c1 0 1 0 1-1 0-3-3-7-6-9l1-1z"></path><path d="M335 99c1-1 4-2 5-2l4-1v1h3c0-1 1-1 1-1h3l-1 1 1 1-6 2h-1 1 5l-8 3c-3 1-6 3-10 2l-2-2c0-1 0-2 1-2 1-1 1-1 3-1 0 0 0-1 1-1z" class="H"></path><path d="M335 99c1-1 4-2 5-2l4-1v1h3c0-1 1-1 1-1h3l-1 1c-2 0-4 1-6 2h-3c-1 0-2 1-3 2v1c1 0 1-1 2 0-1 1-3 2-4 2l-1-1c1 0 1-1 2-1v-1l-1 1-2-2h2s1 1 2 1c1-1 3-2 4-3-2 0-5 1-7 1z" class="D"></path><defs><linearGradient id="l" x1="255.225" y1="372.409" x2="243.031" y2="378.957" xlink:href="#B"><stop offset="0" stop-color="#525152"></stop><stop offset="1" stop-color="#6c6c6c"></stop></linearGradient></defs><path fill="url(#l)" d="M263 380c-8 0-15 3-23-1-3-2-5-5-6-9h1 0c1 4 4 7 7 9-3-4-6-7-7-12 1 3 3 5 5 8 0-1-1-2-1-3 1-1 1-1 2-1 2 0 3 2 4 4 3 4 14 4 19 4l-1 1z"></path><path d="M277 529h0c2 0 2-1 3-1h1 2 1 1 0c0 1 1 1 1 1h0c1 1 1 1 2 1l1 1h1c3 5 3 11 3 16h-1 0v-1l-1 1c0-2 0-5-1-7-1-3-2-4-5-6h1c-2-2-5-2-8-1h-2-1l1-2c-1-1-1-1-1-2l1-1 1 1z" class="C"></path><path d="M276 533v-1c2-1 5-2 8-1h0c2 0 4 2 5 3v1c-1 0-2-1-3-1-2-2-5-2-8-1h-2z"></path><path d="M216 347c5-5 10-11 11-17 1-2 1-4 1-5s1-3 2-3h1c0 6 0 10-3 15h1l-1 1c0 1-1 1-1 2h-1l-1 1v1h2l-4 4c-2 1-3 2-4 4h-1v-1h0v-1h0l-2-1z" class="G"></path><path d="M298 552c0-2 0-5 1-6 1 6 2 15-2 20-2 3-6 6-10 7-1 1-2 1-3 2h-2-2v1c4-1 9-1 12-3 2-1 4-3 5-5v-1h1c-1 3-4 6-7 7l-13 4c0-1 0-2-1-3 0-1 0-2 1-3l6-3c6-2 10-6 13-11h1c-1 3-7 9-9 10-4 2-8 3-11 6v1c6-2 15-4 18-10 2-2 3-5 2-7v-6z" class="W"></path><path d="M236 577h2c2 1 4 3 6 5-1 2-1 3-2 4-1 0-2 1-4 2h-5s-1-1-1-2c-1-2-1-3 0-5s2-3 4-4z" class="F"></path><path d="M233 583c0-2 1-3 3-4 1-1 1-1 2-1l3 3v1h0v1 1h0c-1 0-1 0-1 1-1 1-2 2-4 2h-1 0c-1 0-2-1-2-2-1-1-1-1 0-2z" class="R"></path><path d="M235 587c-1 0-2-1-2-2-1-1-1-1 0-2 0 1 1 1 2 2h0l1-1h2 0l1-1h1c-1 2-2 3-4 3-1 0-1 0-1 1z" class="J"></path><path d="M233 583c0-2 1-3 3-4 1-1 1-1 2-1 0 1 0 2-1 2 0 1-2 2-2 3v2h0c-1-1-2-1-2-2z"></path><path d="M294 392c-2 2-5 6-6 8h0 0c4-5 14-11 14-19 0-1-1-2-2-3s-2-1-3-1c-3 1-5 3-6 5s-2 8-5 9h-2c-1 0-1 0-1-1 1-4 3-10 7-13h1 0c-4 4-6 8-7 13h2c2-1 2-5 3-7 1-1 2-2 2-4l6-3c2 0 4 1 5 3s1 5 0 7c-4 8-13 12-16 20v-1h0l-1-1c1-1 2-2 1-3 1-1 1-2 1-3 1-2 3-4 5-6l1-2h1l1-2 1 1c-1 1-2 2-2 3z" class="V"></path><path d="M270 366c1-1 1-1 2-1l1 1c1 1 4 2 6 3 6 1 11 0 16-1 2 0 3-1 5-1v1c-1 1-2 1-4 2-4 2-8 2-12 2h-4c-1 0-2 1-4 1s-4 0-6-1v-1h-1l2-2s1-1 2-1v-1h-1c-1 0-1 0-2 1h-1-1c1-1 1-1 2-1h0v-1z" class="Z"></path><path d="M271 369h5v1c-1 1-3 1-4 0l-1-1z" class="B"></path><path d="M271 369h0l1 1v1c2 1 3 0 6 0h6l-1 1h1 0 0-4c-1 0-2 1-4 1s-4 0-6-1v-1h-1l2-2z" class="D"></path><path d="M222 459l1-5c2 7 3 15 4 22 1 3 2 6 2 9 1 2 1 9 2 9h0v-3h0c1 3 0 5 0 7s1 6 0 7c0 0-1 0-1-1v-2c-1-1-1-2-1-3v-2c-1-4-1-8-2-12h0c-1-1-1-3-1-4h0v-1c0-1-1-2-1-2v-4-1c-1-1-1-1-1-2v-1-2c-1-1 0-1-1-1v-2-3h-1 0v-3z" class="S"></path><path d="M321 559c1 3 3 11 1 15-1 2-4 4-7 5h-2-1c-1-1-2-2-2-3-1-2-1-4 0-5 0-2 1-2 2-3 1 0 1 0 1 1v1c0 1 0 2 1 3 0 1 1 1 2 1s2-2 3-3h0c1-4 0-8 2-12z" class="C"></path><path d="M313 579h-1c-1-1-2-2-2-3-1-2-1-4 0-5 0-2 1-2 2-3 1 0 1 0 1 1v1c0 1 0 2 1 3 0 1 1 1 2 1s2-2 3-3l1-1h1c0 1 0 2-1 3-1 2-3 4-5 4l-2-4-1 1s1 2 2 2v1h-1c-1 0-1-1-2-2h0 0c0 1 1 2 2 3v1z" class="F"></path><path d="M218 407h1c1 3 2 6 2 9 1 5 1 9 2 14 1 3 2 7 3 10 0 1 0 3 1 4 0 0 1 0 1 1 2 1 3 2 2 4l-1 1c-1-1-2-2-2-3v1c1 4 7 18 5 22-1 0-2 0-2-1l-1-6c0-1-1-2-1-3s1-1 1-1c0-2-1-4-2-6 0-1-1-3-2-5 0-4 0-7-1-11-1-2-1-4-2-6-1-4-1-9-2-13l-2-11z" class="J"></path><path d="M290 440h0c2-3 2-4 5-5h1v1c0 1 0 2 1 2v-1c2 0 3 2 4 3v1c-1-1-1-1-2-1h-1l1 1c1 1 1 1 0 3h-1v1 2s0 1-1 1-2-1-3-1c-3-1-5-2-8-1 0-2 2-5 4-6z" class="N"></path><path d="M298 444l-2-2 1-1h2c1 1 1 1 0 3h-1z" class="C"></path><path d="M307 509l1 1 1 1c-1 5-3 10 0 14 2 3 5 5 6 7 1 1 1 2 1 3-1 1-2 0-4 0-2-1-3-4-5-6l3 9c3 4 5 8 7 12v1c-3-6-7-11-9-17-1-2-2-4-2-6v-6c-1-2 0-3 0-5h0l-1-1c1-1 1-2 1-3l1-4z" class="R"></path><defs><linearGradient id="m" x1="121.84" y1="413.869" x2="121.661" y2="418.951" xlink:href="#B"><stop offset="0" stop-color="#636165"></stop><stop offset="1" stop-color="#787877"></stop></linearGradient></defs><path fill="url(#m)" d="M119 415c1-1 0-2 1-3 2 3 4 4 6 6 1 0 3 1 3 1 1 0 1 1 2 1s1 0 1 1c7 0 12 1 18-4 0 2-1 2-1 3h-1c-1 0-1 1-2 1l1 1c1-1 1-1 3-1-4 3-9 4-14 3-2 0-4-1-6-1-1-1-2-1-3-1h-1l-1-1-1-1-1 1c-2-1-4-4-4-6h0z"></path><path d="M208 416v5l1-1v-3a34.53 34.53 0 0 0-5-18v-1c0-1 0-1-1-2l-3-6-4-4h1c1 0 3 2 4 4h0c2 2 4 6 5 8v1c2 4 4 9 4 13 0 2 0 4 1 6v-3h0c0 2 0 8 1 9v-1c0 4-1 8-2 13l-1-1h0c1-1 1-2 1-3h-1v2c-1 1-1 1-1 2l-1 1c0-2 0-4 1-5v-6-10z" class="K"></path><path d="M461 127l1 1 1-1c4-3 5-7 5-13h0 1c0 2 0 4-1 7h2l-1 4c-1 1-2 4-2 5l-1 1h0l-1 1v-1c0-1 1-2 1-2v-2-1h0c-5 5-9 8-17 9-3 0-8 0-10-3l-3-3h4c3 2 6 2 9 3h2 2c3 0 6-2 7-4l1-1z" class="L"></path><path d="M468 121h2l-1 4c-1 1-2 4-2 5l-1 1h0l-1 1v-1c0-1 1-2 1-2 0-1 1-3 1-3v-1c0-1 1-3 1-4z" class="Y"></path><defs><linearGradient id="n" x1="160.954" y1="568.571" x2="159.385" y2="572.459" xlink:href="#B"><stop offset="0" stop-color="#525652"></stop><stop offset="1" stop-color="#6e6a6f"></stop></linearGradient></defs><path fill="url(#n)" d="M121 564c6 5 13 6 20 6h7 7c2 0 4-1 6-1h0v1h6c0 1 1 2 2 2-4 0-7 0-10-1h-6c-1 0-2 0-3 1h-3v1c1 0 2-1 3 0 1 0 2 0 2 1h-17-2c-2 0-3-1-4-1h7 0c-1-1-3-1-5-2h0 4l-7-2c-3-1-5-3-8-5h1z"></path><path d="M131 571h4 15 9-6c-1 0-2 0-3 1h-3v1c1 0 2-1 3 0 1 0 2 0 2 1h-17-2c-2 0-3-1-4-1h7 0c-1-1-3-1-5-2h0z" class="S"></path><path d="M141 505h1c5 2 11 5 15 10 0 1 0 1 1 1v1c3 7 4 14 4 22h0c-2-2-2-7-3-10-1-1-1-3-2-5-3-8-9-15-17-18l1-1z" class="V"></path><path d="M141 505h1c5 2 11 5 15 10 0 1 0 1 1 1v1h-1c1 3 3 8 3 11-1-1-1-3-2-4-3-8-9-15-17-19z" class="P"></path><path d="M466 395c-3 0-11 5-13 7-1 1-2 3-3 3h-2c-1-1-1-2-1-3v-1-1-2c4-4 13-5 18-6l2 1h0l2 1h-2l1 1h1v1c-1 0-2 0-3-1z" class="Q"></path><path d="M149 565h5c1 0 3 0 5-1 1 0 2 1 2 1 3 0 5 0 7 1h0l7 3c1 1 3 1 4 3h-1l-2-1c-4 1-7-1-10-2h-5c-2 0-4 1-6 1h-7c1-1 3 0 5-1h-4c0-1 0-2 1-3h0l-1-1z" class="F"></path><path d="M150 566c1 1 4 1 6 1 2-1 5-1 7 0-2 1-3 1-5 1-1 1-3 1-5 1h-4c0-1 0-2 1-3z" class="O"></path><path d="M168 566l7 3c1 1 3 1 4 3h-1l-2-1c-4 1-7-1-10-2h-5c-2 0-4 1-6 1h-7c1-1 3 0 5-1 2 0 4 0 5-1 2 0 3 0 5-1l2 1h3l1-1h0-1v-1z" class="L"></path><path d="M165 568h3c2 0 3 1 4 2-1 0-3 0-4-1-1 0-2-1-3-1z" class="H"></path><path d="M168 566l7 3c1 1 3 1 4 3h-1l-2-1-4-1c-1-1-2-2-4-2l1-1h0-1v-1z" class="U"></path><path d="M407 88h1c-2-1-3-3-5-4s-5-1-7 0c-4 2-7 5-9 9-1 4 0 9 2 13h0c-3-3-5-9-4-13 0-4 2-8 5-11 4-3 11-3 15-2h-2l1 1h0 2c-3 0-8 0-9 1h2c2 0 4 1 6 1 1 0 1 0 2 1h0-2v1c2 1 4 1 5 4h-2l-1-1zm-91 515l-2-2v-3c1-2 4-4 6-4 2-1 5-1 8 1 2 2 3 4 3 7 1 3-1 6-3 8-1 3-4 4-6 5l-1-2c2-1 4-2 6-5 1-2 2-4 1-7 0-1-1-3-3-3-1-1-2-1-3-1l-2 1v-1l-2 2v1h-1v2h-1v1z" class="I"></path><path d="M316 602v-2-1-1h1v-1s1-1 2-1h0l-1 1c0 1-1 1-1 2v1 2h-1z" class="W"></path><path d="M273 361c-2-5-4-12-2-17 1-4 4-8 8-9 3-2 8-3 12-2 3 2 6 5 7 8 1 1 1 2 1 3h0c-1 2-1 2-1 4-2 2-4 4-8 5-2 0-4-1-6-2 0-1-1-1-1-3v-1h0c1 1 1 3 3 4 1 1 5 1 6 0 2-1 4-2 4-4 1-2 1-4 1-6-1-2-4-5-6-6s-7-1-9 0c-4 1-7 5-9 9-1 4-1 10 0 14 1 1 1 2 2 2l-1 1h-1z" class="B"></path><path d="M369 107c0 1 13 3 14 3 1-1 2 0 3 0l2 1h1v1h0c-3-1-5-1-7-1-1 1-2 1-2 1-4 1-8 2-11 4-2 0-3 1-5 2h0c2-3 1-5 1-7l-1-2c1-2 3-2 5-2z"></path><defs><linearGradient id="o" x1="345.971" y1="647.994" x2="352.489" y2="638.991" xlink:href="#B"><stop offset="0" stop-color="#666366"></stop><stop offset="1" stop-color="#828484"></stop></linearGradient></defs><path fill="url(#o)" d="M359 642c1 0 5-1 6-2 0 0 2-3 2-4 0-2 0-4-2-6h0l1-1h0c1 1 2 3 2 4 1 2 0 4-1 6l2-2c0 1 0 2-1 3l-1 1h1c1 0 3-2 3-4 1-1 1-2 1-3l1-1c0 2 0 3-1 5-2 5-8 6-13 8-1-1-2-1-3 0h-4c-7 0-14-3-20-4l1-1c5 1 9 2 14 2 2 1 4 1 6 0 2 0 4 1 6-1z"></path><path d="M479 178c2 0 3 1 4 1 4 2 7 4 8 8 1 2 1 5 0 7s-4 3-6 4l-1-1 1-2h0l1-2v-3c-1-2-2-3-3-5-1-1-1-2-1-2l-2-1c-1-1-1-2-2-2h0l-1-1c1 0 2 0 2-1z" class="J"></path><path d="M485 184h1c1 1 1 3 2 5-1 2-1 4-3 6h0l1-2v-3c-1-2-2-3-3-5v-1l2 1h0v-1z" class="X"></path><path d="M479 178c2 0 3 1 4 1l-1 1h1c2 1 4 3 5 4v1h0c-1-1-1-1-2-1h-1v1h0l-2-1v1c-1-1-1-2-1-2l-2-1c-1-1-1-2-2-2h0l-1-1c1 0 2 0 2-1z" class="B"></path><path d="M480 182c1-1 2 0 3 0l2 2v1h0l-2-1v1c-1-1-1-2-1-2l-2-1z" class="S"></path><path d="M489 549c0-1 1-2 2-2 3 0 7 3 9 5 0 1 1 1 1 1l-1 1 2 2c0 1 1 2 1 3 0 0 1 1 1 2v2h-1s0-1-1-1h0c0 1 1 1 2 2v2l1 1c0 1 0 2-1 4h0c-1-5-2-7-6-10h1v-2l-2-2c0-1-1-1-1-2h-3v-1l-1-1-2-1c-1-1-1-2-1-3z" class="K"></path><path d="M489 549h2 0c1 0 2 1 3 1s1 1 2 1l2 2h-1-2v1l1 1h-3v-1l-1-1-2-1c-1-1-1-2-1-3z" class="B"></path><path d="M288 611c-5-3-7-5-10-10-2-3-3-9-2-12 2-5 5-8 9-10l6-3-4 4-3 3c-3 2-6 5-7 9l1 1v-1c1-3 4-6 7-8l6-6h0c0 1-2 2-3 4-1 1-2 3-4 4-2 2-6 5-6 8 0 2 0 4 1 5v2h1v-1c-1-2-2-5 0-8 0-1 2-3 3-4h0c-2 4-3 6-3 10 1 5 4 9 8 12l3 2v1l-1-1c-1 0-2 0-2-1z" class="V"></path><path d="M172 334l2 2c2 2 4 3 6 4v-1l8 3c2 0 4 0 6 1l9-1h1v1h2c-1 1-1 1-3 1 0 1 0 1 1 1-2 0-4 1-6 1h-2c-1 1-3 1-5 0s-5-1-7-2c-1 0-3-1-5-1h-2c0-1 0-1-1-2s-3-2-4-3v-4z" class="L"></path><path d="M194 343l9-1h1v1c-3 1-6 1-9 1h2v-1h-3z" class="B"></path><path d="M180 340v-1l8 3c2 0 4 0 6 1h3v1h-2c-4 0-7-1-11-2-1-1-3-1-4-2z" class="C"></path><path d="M172 334l2 2c0 2 1 2 2 3 2 2 6 3 9 4 0 0 1 0 2 1 4 0 8 1 12 1h0l-1 1h-2c-1 1-3 1-5 0s-5-1-7-2c-1 0-3-1-5-1h-2c0-1 0-1-1-2s-3-2-4-3v-4z" class="D"></path><path d="M143 502c5 1 9 3 12 7l2 3-1 1c1 1 1 2 2 3h0c-1 0-1 0-1-1-4-5-10-8-15-10h-1l-1 1c-5-1-10-2-15 0l-1-1c-2 1-3 2-5 3v1c-1 0-2 1-2 2h-1 0c0-1 2-3 3-3l1-1v-1c0-1 3-2 5-2 5-2 11-2 17 0l1-2z" class="E"></path><defs><linearGradient id="p" x1="144.96" y1="507.984" x2="153.268" y2="505.087" xlink:href="#B"><stop offset="0" stop-color="#131518"></stop><stop offset="1" stop-color="#323130"></stop></linearGradient></defs><path fill="url(#p)" d="M143 502c5 1 9 3 12 7l2 3-1 1c-4-4-9-7-14-9l1-2z"></path><path d="M250 394h1c-1 2-2 3-2 5-1 0-1 1-2 2 0 1 0 1-1 2 0 2-1 3-1 6h1v2c0 1 0 1 1 1 0 2-1 4 0 5 1 2 1 2 1 3h-1c1 2 3 8 1 10v1l-1 1h-3c0-1 0-2-1-3-2-12 0-25 7-35z" class="C"></path><path d="M248 431s-1 0-1-1c-1 0-2-2-2-3s1-3 1-4v-4l1-1v2c1 2 3 8 1 10v1z" class="E"></path><path d="M193 370l10 2 3 1h-2-2l-1 1h2c1 0 2 1 3 2 3 1 7 5 9 8s7 8 7 12v1l1 1v4c0 1 1 2 1 4l1 1 2 12c1 1 1 2 1 3h-1v-2h-1v-2l-2-9-1-5c-1-1-1-2-1-3s0-2-1-2v-1c-1 0-1-3-1-3-1-2-3-4-4-6l-1-1c0-1-1-3-2-4-6-7-13-9-21-10v-1h0c2 0 5 0 7 1h0l1-1c-2 0-3-1-4-1h-2c-2-1-4-1-5-1l4-1z" class="G"></path><path d="M186 431l1-1v-1h0v-1c1 0 1 1 2 2v-2-2l1 1 1-1c-1 4-2 5-3 8l-3 9c-2 5-6 8-10 11-8 6-19 7-29 5-6-1-12-4-16-9-2-2-4-4-4-7v-1c4 7 9 13 17 15 8 3 20 2 28-2 6-3 11-8 14-15v-2-1-1-1h1c0-1 0-2-1-2 0-1 0-1 1-2z" class="J"></path><path d="M221 532h2c0 1 1 2 1 2 2 2 3 3 6 3 0 0 1 0 1-1l1 1c1-1 1-2 2-2l-1 4-1 1c1 1 1 1 2 1v1c-2 1-4 5-6 5h-1c-3 0-7-4-9-6h3l-2-2h3c0-1 2 0 3 0v-1h-1c-1-2-3-3-3-6z" class="N"></path><path d="M230 540h-1-1c-1 0-2 1-2 1h-1c2-3 4-2 6-3h1l-2 2h0z" class="G"></path><path d="M221 541h0 1c1 1 3 1 4 0h1s0 1 1 1 1-1 2-2h0c0 2-1 2-2 3h0l-2 1c0 1 1 1 2 1v2h-1c-3 0-7-4-9-6h3z" class="K"></path><path d="M174 341l3 2h2c2 0 4 1 5 1 2 1 5 1 7 2s4 1 5 0c1 1 2 1 3 1h0c-2 1-4 3-6 2-1 1-2 1-2 2l-1-1 2-1c-2-1-8 4-11 4-5 0-10-4-13-7h0l-1-3h0c3 3 6 6 11 7l1-1 2 1c1-1 1-1 2-1h1c-2-1-2-2-3-3h4v-1l-6-1c-1 0-1 0-2-1-1 0-2-1-3-2z" class="M"></path><path d="M185 345c2 1 4 1 6 2h0c0 1 0 1-1 1h0c-1-1-2-1-3 0h2c0 1-1 1-2 2h0c-2 0-2 0-4-1h1c-2-1-2-2-3-3h4v-1z" class="O"></path><path d="M168 346l-1-3h0c3 3 6 6 11 7l1-1 2 1c1-1 1-1 2-1 2 1 2 1 4 1-2 1-3 2-5 2-1 0-1 0-1-1h0l-1 1c-2 0-3-1-4-1-3-1-5-3-8-5zm61 1h1c1 1 1 1 1 2l-5 5c-4 3-7 6-11 8h-1c-2 1-4 2-7 3 3 0 5 0 8 1l-1 1c0 1 1 1 2 2 1 0 2 1 2 1 1 1 1 2 1 3-6-4-12-6-19-7h0c1 0 2-1 3-1l-1-1v-1h2c2-1 4-1 6-1h0l6-3c1 0 1-1 2-1 1-1 1-2 2-2 1-1 2-1 2-2 1 0 2-1 2-1l1-1c1 0 3-3 4-3v-2zm294-19c2 0 3 1 4 2 2 3 4 8 4 11-1 2-2 3-4 3-2 1-4 1-6 1s-2-2-3-3c0-1-1-2-2-2-1 1-1 1-1 2l-1-1c0-1 1-2 1-3 1-1 2-2 3-2 2 0 3 1 4 2s1 1 2 1 2-1 2-2c1 0 1-2 1-3-1-2-3-4-5-5l1-1z" class="G"></path><path d="M522 338c1 1 1 1 2 1-1 0-2 1-2 1l-1-1h-2v-1c1 0 2 0 3 1v-1z" class="D"></path><path d="M299 585v-3h1c1 3 4 5 7 8h0c1 2 1 2 2 3 1 0 1 0 2-1v1c0 1-1 1-2 2v-1h-1c0 1-1 1-1 1h-1c0 2-1 5-2 6l-1 1h-1s0-1-1-1v1l-2-2c-1-2 0-5 0-7h0l-1-6h0 1v-2z" class="E"></path><path d="M301 593h1v3c-1-1-1-2-1-3zm3 1c0 2-1 4-1 7l-1-1v-2c1-1 1-2 1-4h1z" class="V"></path><path d="M299 585h1v1l2 2v2c0 1 1 2 0 2v1h-1c-1-1-1-3-1-4 0 0-1 0-1-1v-1-2z" class="I"></path><path d="M304 594v-1-1l1 3 1-1c0-1-1-1 0-2 1 0 1 1 1 2v1h-1c0 2-1 5-2 6h-1c0-3 1-5 1-7z" class="B"></path><defs><linearGradient id="q" x1="156.538" y1="344.346" x2="163.429" y2="336.111" xlink:href="#B"><stop offset="0" stop-color="#6e6f6f"></stop><stop offset="1" stop-color="#848385"></stop></linearGradient></defs><path fill="url(#q)" d="M164 311h0l1-1 2-2c0 1-1 1-1 2-1 1 0 4-1 4h0c-4 6-5 11-5 18-1 2-1 2-1 4 1 5 2 8 5 12l3 3v2c1 1 2 2 4 3l3 1-1 1-2-1c-5-3-12-9-14-15-2-10 2-22 7-31z"></path><path d="M164 348l3 3v2l-3-3v-2z" class="D"></path><path d="M164 311h0l1-1 2-2c0 1-1 1-1 2-1 1 0 4-1 4h0c-4 6-5 11-5 18-1 2-1 2-1 4-1-7 1-17 5-23v-2z" class="Y"></path><defs><linearGradient id="r" x1="140.46" y1="420.293" x2="134.466" y2="429.455" xlink:href="#B"><stop offset="0" stop-color="#737273"></stop><stop offset="1" stop-color="#888789"></stop></linearGradient></defs><path fill="url(#r)" d="M123 421l1-1 1 1 1 1h1c4 4 9 5 16 5 1 0 3-1 5-1 1 0 4-2 5-1h0c1 0 2 0 3-1l3-3v1c0 1-1 2-2 2l1 1c-1 1-2 2-4 2v1c-1 0-1 1-2 1s-6 1-6 2h0v1h-1l-4-1c-4 0-9-2-12-4s-4-4-6-6z"></path><path d="M141 431c-1 0-1-1-2-1h0v-1h0 0v-1l1 1h4v1l1 2-4-1z" class="I"></path><path d="M144 429v1c-2 0-2 0-4-1h4z" class="T"></path><path d="M157 424l1 1c-1 1-2 2-4 2v1c-1 0-1 1-2 1s-6 1-6 2h0v1h-1l-1-2v-1c1 0 1-1 2-1 2 0 4 0 6-1s3-2 5-3z" class="V"></path><defs><linearGradient id="s" x1="257.993" y1="405.496" x2="265.704" y2="404.889" xlink:href="#B"><stop offset="0" stop-color="#656565"></stop><stop offset="1" stop-color="#7d7c7d"></stop></linearGradient></defs><path fill="url(#s)" d="M264 393c1 1 1 1 1 2v1c0 2 0 4 1 5 0 1 1 3 2 4h-3v-1c-1 0-1-1-2-2 0 5 0 9 4 13l-1 1c-1-1-2-3-3-4l-1-1v1c0 1 1 3 1 4 1 0 1 1 1 1l-1 1 1 1h-1l1 1c0 1 0 2-1 2-1-1-2-1-2-2-2-2-2-3-3-5h0c-2-7 3-16 6-22z"></path><path d="M260 414v-1-5c0 1 0 3 1 4h0c0 2 1 4 2 6l1 1h-1c-1-1-2-3-3-5z" class="H"></path><path d="M260 414c1 2 2 4 3 5l1 1c0 1 0 2-1 2-1-1-2-1-2-2-2-2-2-3-3-5l1 1 1 1v-1h0v-1-1z" class="N"></path><path d="M494 389l3-1c-1 1-1 2-2 3l-2 1c0 1-1 2-2 2h-2c-2 1-5 2-7 3-4 2-8 3-12 4l-2-1h0c-1-1-1-2-1-2l1-1h-1c-4 0-10 3-13 7-1 0-2 2-3 2h0c1-2 3-4 4-5 3-3 7-4 11-6 1 1 2 1 3 1 2 0 3 0 5-1 2 1 6 0 8-1h1c0-1 1-1 1-2h1l5-2 4-1z" class="g"></path><path d="M490 390l4-1c0 1-1 1-1 2-1 1-1 1-2 1l-1 1v-3z" class="F"></path><path d="M485 392l5-2v3h-1c-2 0-4 1-6 1 0-1 1-1 1-2h1z" class="E"></path><path d="M467 536c-1 1-2 1-4 2-5 1-6 4-9 8h-1v-1c-1-1 0-4 0-5 1 0 1-1 1-1h-1c-1-1-1-1 0-2 1-2 4-4 5-4 4-2 8-2 11 0v1c2 1 3 3 4 4h0c0 1 0 2 1 3h0l-2 1c-1-1-1-1-1-2s-1-1-2-2c1 0 1 0 1-1 0 0-1 0-1-1h0-1-1z" class="N"></path><path d="M461 534c3-1 5-1 8 0h0c2 1 3 3 4 4h0c0 1 0 2 1 3h0l-2 1c-1-1-1-1-1-2s-1-1-2-2c1 0 1 0 1-1 0 0-1 0-1-1h0-1l-1-1c-3 0-6 0-10 2 0 0 0-1 1-1 0-1 1-1 1-1l2-1z" class="L"></path><path d="M471 540v-1-1h2c0 1 0 2 1 3h0l-2 1c-1-1-1-1-1-2z" class="B"></path><path d="M461 534c3-1 5-1 8 0-1 0-1 1-1 1h-6l-1-1z" class="M"></path><path d="M239 537h1c0 2 1 4 1 5 1 2 1 7 2 8 0 8 0 14-5 20-4 3-10 5-15 6-3 0-6 0-9-1h4 6c1-1 3-1 5-2 1 0 2-1 3-1 4-2 7-5 8-8 2-5 2-8 2-13h0c-1 1-1 1-1 2 0 5-4 11-8 14-3 3-7 5-12 5h-6-1-2c6-1 12-1 18-4 4-3 8-7 9-12 0-1-1-1-1-2 0 0 0-1-1-1l1-2c0-2 1-4 1-7 1-2 0-4 0-7z" class="C"></path><path d="M520 480h1 1l-5 6c2-1 3-2 4-3l1-2h2v2c0 2 0 3-1 4-3 2-6 3-9 4-1 1-2 2-4 3s-5 1-8 2-5 4-7 5c0-1 0-2 1-2 2-2 4-3 6-4-4 1-7 2-9 6h-1c0-1 2-3 3-4 2-2 4-2 6-3l8-4c4-1 9-7 11-10z" class="W"></path><path d="M527 391v7h0l1-2v1l1 2c0 6-1 13-4 18-1 4-4 8-7 10-1 0-2 0-3-1l-4-1v-1h2 0 2v-1h-1c1-1 1 0 1-1 2 0 3-2 4-3 2-3 3-5 4-8l1-2 1-3c2-5 2-10 2-15z" class="I"></path><path d="M515 422c2 0 3-2 4-3h1v1 1c-1 1-3 3-5 3h-2 0 2v-1h-1c1-1 1 0 1-1z" class="G"></path><path d="M523 411l1-2v5c-1 2-3 5-4 7h0v-1-1h-1c2-3 3-5 4-8z" class="B"></path><path d="M515 426c4-2 6-6 10-9-1 4-4 8-7 10-1 0-2 0-3-1z" class="E"></path><path d="M187 447c-2 3-4 6-7 8-10 7-20 11-32 9-10-1-17-6-22-13-4-6-7-14-6-21 0-1 0-1 1-2v-4c1 1 1 3 1 4l1-3h1c0 2-1 4-1 5-1 5-1 11 1 15h0c2 6 7 11 12 14 8 5 18 6 27 3 11-2 18-8 24-17v2z" class="W"></path><path d="M210 344c0 1-1 2-2 2h-1l1 1c0-1 1-1 1-1h1c-1 1-3 2-3 4h1c0 1-2 2-4 3h-1-1l1-1s0-1-1 0-3 2-5 2c-2 1-3 1-5 1h-4-1v-1h-1-1c1-1 3-2 3-3 1 0 2 0 2-1l1 1c0-1 1-1 2-2 2 1 4-1 6-2h0c-1 0-2 0-3-1h2c2 0 4-1 6-1h0l1 1 5-2z" class="D"></path><path d="M204 345h0l1 1-6 1h0c-1 0-2 0-3-1h2c2 0 4-1 6-1z" class="k"></path><path d="M192 355c1-1 1-1 2-1l2-2c1 0 2 0 3-1 1 0 1 0 2-1v1s0 1-1 1l-3 1v1c-2 1-3 1-5 1z" class="L"></path><path d="M190 350l1 1c0-1 1-1 2-2l1 1c0 1-1 2-2 3h0c-1 0-1 1-2 2h-2-1v-1h-1-1c1-1 3-2 3-3 1 0 2 0 2-1z" class="F"></path><path d="M190 350l1 1c0-1 1-1 2-2l1 1c0 1-1 2-2 3h0c0-1-1-1-1-1-1-1-3 0-4 1l-1 1h-1c1-1 3-2 3-3 1 0 2 0 2-1z" class="Q"></path><path d="M260 335h5-3c3 3 6 6 7 10 0 3-1 6-2 8-2 2-4 4-6 5h-2c1-2 3-2 5-3 2-2 3-5 4-8 0-3-2-6-3-8h-1c2 2 3 5 3 7 0 3-2 5-3 7-2 2-6 4-9 4h-1 0c-3 0-6-1-8-3-1-1-1-3-1-5 0-1 1-2 2-3s1-1 3-1h-1 0l-2 2c-1 1-1 2-1 4h1c1 0 1 0 1-1 0-2 0-2 1-3l1-1 1 1-1 1s-1 1-1 2c1 1 2 3 3 4h1c4 0 6-1 8-3l2-2 2 1c0-3 0-6-1-8 0-1-1-2-1-3s-2-3-3-4z" class="B"></path><path d="M263 349l2 1-2 2h0l-2-1 2-2z" class="M"></path><path d="M248 350l1 2c1 2 1 3 3 3h1v1c-2 0-3-1-4-2-1 0-2-1-3-2v-1h1c1 0 1 0 1-1zm13 1l2 1h0c-2 2-5 3-8 3h-1l-1-1c4 0 6-1 8-3z" class="f"></path><path d="M502 519c2 1 4 2 6 2 0-1-2-1-3-2-2-1-4-2-5-5 0 0 1-1 1-2 2 1 2 2 4 3 1 1 3 2 5 3s4 3 5 6c1 1 2 2 2 4 1 2 1 4 2 6 0 0 1 0 1 1v1 2l-1 1c-1-4-2-8-5-11-2-3-7-5-12-6-1 0-2 0-3 1h1c0 1 0 2-1 3h-1l1-1c0-1-1 0-1-2l1-1h-1v-2h-1l1-1h0c1 0 1 0 1 1h1c1 0 1 0 2-1z" class="C"></path><path d="M154 385v3c-1 1-1 3-1 4l-1 1c0 1-1 1-1 2s0 1-1 2h-1 0-2c-1 0-2 0-2-1h-1c0 1-1 1-1 2 0-3-1-6-3-8-2-1-4-1-6-1h-2l1-1-1-1h12l-1-1h2 2c1 0 1 0 2 2h1 0l2-2h1 0l1-1z" class="F"></path><path d="M154 385v3c-1 1-1 3-1 4l-1 1c0 1-1 1-1 2v-4c1-2 1-3 2-5h0l1-1z" class="K"></path><path d="M147 386c1 0 1 0 2 2h-1c0 1 1 1 1 2v1h-1 0c0-1-1-1-1-1-1-2-2-2-3-3l-1-1h2 2z" class="I"></path><path d="M132 387h12c1 1 2 1 3 3-1 1-1 3-2 5-1-2-2-4-3-5-2-2-6-2-9-2l-1-1z"></path><path d="M519 181l1-1v-1l1-1c1 5 0 9-2 13-2 2-4 5-4 7-8 8-18 11-29 11-5 0-11-2-16-5l1-1c3 2 6 3 9 4 9 2 18 0 26-5 8-4 11-13 13-21z" class="G"></path><path d="M215 476c0-1 1-1 1-2l2-4 1 2c-2 4-5 8-7 12-1 1-2 1-2 2-1 1-1 2-2 3-3 3-7 6-12 7h0c0-1 1-1 1-1v-1h0c-2 0-3 1-5 2h0-3c-1 0-1 1-1 2h-3l2-2h0c-1 0-1 0-2-1 1-1 2-1 3-1 2-1 4-2 6-2 4-2 10-4 14-7 2-2 6-6 7-9z" class="R"></path><defs><linearGradient id="t" x1="236.299" y1="493.315" x2="259.942" y2="511.91" xlink:href="#B"><stop offset="0" stop-color="#757475"></stop><stop offset="1" stop-color="#919292"></stop></linearGradient></defs><path fill="url(#t)" d="M242 484l3-6v1c-1 4-2 8-2 12v5s1 1 1 2c2 5 3 7 8 10v1h-1c1 1 3 2 4 3-3 0-6-4-8-5h-1l2 2c2 2 3 3 5 4 0 0 3 1 3 2h0c1 0 1 0 1 1h-2l-4-2c-6-4-10-10-11-16 0-3-1-7 0-10v-1c0-1 0-2 1-3h1z"></path><defs><linearGradient id="u" x1="242.197" y1="481.794" x2="242.606" y2="490.519" xlink:href="#B"><stop offset="0" stop-color="#4b4b4c"></stop><stop offset="1" stop-color="#646465"></stop></linearGradient></defs><path fill="url(#u)" d="M242 484l3-6v1c-1 4-2 8-2 12v5s1 1 1 2h-1c0-1-1-2-1-3v-3 4c-1-3-1-7-1-10l1-2z"></path><path d="M383 640l1 1c1 0 3 0 4-1l1 1v1h1l4 1c2 0 3-1 3-2 0 1 0 1 1 2-1 0-2 1-3 1-5 1-10 1-15 1-6 1-12 4-18 4-1 0-1-1-2-1s-1-1-1-1c-1 0-2 0-3-1 1-1 2-1 3 0 2 0 4 0 7-1 6-1 11-3 17-5z" class="D"></path><path d="M384 641c1 0 3 0 4-1l1 1v1h1l-2 1v-1h-6-1c0 1 0 1-1 0h0l4-1z" class="O"></path><defs><linearGradient id="v" x1="140.49" y1="578.728" x2="144.883" y2="582.686" xlink:href="#B"><stop offset="0" stop-color="#898888"></stop><stop offset="1" stop-color="#a4a4a6"></stop></linearGradient></defs><path fill="url(#v)" d="M151 578c-4 1-10 4-13 8-1 2-2 4-1 7s2 7 5 8h0v1s-1-1-2-1c-2-3-4-7-4-10v-1c-1 1-1 3 0 4 0 3 2 7 4 8s4 1 5 0c1 0 1-1 2-2l-1-1c0 1-1 2-1 2h-2 0l1-1v-2c1-1 1-1 2-1l1 1c1 1 1 2 1 4 0 1-1 1-2 1-1 1-3 1-5 0-2 0-4-3-5-5-2-4-2-8-1-13v1l-1 1c-1 3-1 8 0 11s4 5 7 6c1 1 3 1 5 1l1-1h1l-1 1c-1 1-2 1-3 1-2 1-4 0-6-1-3-3-5-7-6-11 0-3 0-7 3-9 3-5 9-8 14-8 1 0 1 1 2 1z"></path><path d="M198 457c2-1 4-6 5-8h1v1c-5 9-11 17-20 22v-1c0-1 1-1 1-2-2 2-5 3-8 5-4 1-8 2-13 2s-10 1-16 0c-3-1-6-3-9-4-5-2-11-5-13-9 1-1 1-1 2-1 0 1 1 2 2 3 1 3 6 5 9 6 7 4 16 5 24 4 3 0 7-1 10-1 9-3 17-9 23-16 1 0 1 0 2-1z" class="B"></path><path d="M348 92c5 0 9 1 14 2-4 1-9 1-11 4l-1-1 1-1h-3s-1 0-1 1h-3v-1l-4 1c-1 0-4 1-5 2-1 0-1 1-1 1-2 0-2 0-3 1-1 0-1 1-1 2 0-1 0-1-1-2h-1c-1 1-1 1-1 2h-1v1h-1c0-1 0-1-1-1 0-1-1-1-1-1v-1h1l1-1h1c1-1 2-2 2-3 3 0 5-3 8-3l10-1c1 0 3 0 4-1h-9 7z" class="G"></path><path d="M336 94l10-1h3l1 1c-3 0-5 0-8 1h-1-3c0 1-1 1-2 1l1-1-1-1z" class="J"></path><path d="M317 399c3 0 9 5 11 8 0 1 1 3 2 4 1 2 2 5 3 7 2 1 2 3 3 5 2 5 3 11 2 16h0c-1-7-2-13-5-20h0l3 13h-1c-1-4-2-7-3-11-3-7-9-12-15-17v-5z" class="n"></path><defs><linearGradient id="w" x1="240.532" y1="250.434" x2="232.953" y2="272.452" xlink:href="#B"><stop offset="0" stop-color="#636465"></stop><stop offset="1" stop-color="#878386"></stop></linearGradient></defs><path fill="url(#w)" d="M233 261c1-2 1-3 2-5h0v-2h1c-1-1-1-2 0-3h0c0-1 0-1 1-1 0-1 0-2 1-2v-1l1-1h1l-1 1 1 1 2-3 1 1c-1 0-2 1-3 2l1 1v1 2l-1 2c0 2-1 3-1 5l-1 2c0 4 1 8 1 12h-1v1-1 1h-2c-1-2-1-4-1-6-1 4 1 10 1 14h0c-1-1-1-3-1-4-1-3-2-5-2-8v-9z"></path><path d="M241 250v2l-1 2c0 2-1 3-1 5l-1 2c0 4 1 8 1 12h-1v1-1c-2-8-2-16 3-23z"></path><path d="M108 560l1 1c2 2 3 4 5 6 3 3 9 5 13 6h2c1 0 2 1 4 1h2 17 3 3v1c-2 0-4-1-6 0-3 0-8 0-11 1-1 0-1 0-2 1-7 1-16 0-22-4-4-3-9-7-10-12l1-1z" class="C"></path><path d="M133 574h2 1 0l-3 1v-1z" class="H"></path><path d="M109 561c2 2 3 4 5 6 3 3 9 5 13 6h2c1 0 2 1 4 1v1c-2 0-5 0-7-1h0-2 0c-2-1-4-2-5-2-1-1-3-2-4-3l-1-1s-1 0-2-1v-1l-1-1-1-1s0-1-1-2h0v-1z" class="F"></path><defs><linearGradient id="x" x1="453.688" y1="88.886" x2="434.835" y2="93.585" xlink:href="#B"><stop offset="0" stop-color="#222020"></stop><stop offset="1" stop-color="#575758"></stop></linearGradient></defs><path fill="url(#x)" d="M431 84c5 0 10 3 14 5s7 4 10 6c2 1 5 3 6 5h0v1h-1c0-2-2-3-4-4 0 1 1 2 1 3-2-1-4-3-5-4v1l-2 1-3 2h0-1v-2l-1-3-1 1h0c1 1 1 2 1 3h-1 0c0-2 0-3-1-4h-1l-2-2c-2-3-6-6-8-8-1 0-1-1-1-1h0z"></path><path d="M445 95v-1h0 1c0 1 1 2 2 2v-1h0c1 0 3 0 4 1v1l-2 1-3 2h0-1v-2l-1-3z" class="l"></path><path d="M292 587l-2 5v1c2-1 2-3 2-5 1-1 1-2 2-3-1 5-2 13 0 18 1 1 3 3 3 5-1 1-2 1-3 1-4-1-3-5-5-8-1 0-1 1-2 1-1-1-2-1-2-2-1-2 0-5 1-7 0-2 6-13 8-14v1c-1 1-1 1-1 2l-1 1v1l-1 1c0 1 0 1-1 2h1v-1l1 1z" class="H"></path><path d="M185 495c1 1 1 1 2 1h0l-2 2h3c0-1 0-2 1-2h3 0c2-1 3-2 5-2h0v1s-1 0-1 1h0l-2 2c-6 2-12 3-18 4-4 1-8 1-12 0-1-1-2-2-2-3v-1-1-1c4 1 6 2 10 2 4-2 10 0 13-3z" class="B"></path><path d="M173 498h3 3c-2 0-3 0-4 1v1h-1c1-1 1-1 1-2h-2z" class="D"></path><path d="M162 498v-1-1c4 1 6 2 10 2h1 2c0 1 0 1-1 2-1 0-5-1-7 0h-2c-1 0-1-1-2-2h-1z" class="C"></path><path d="M402 333c2-1 4-3 7-4 1 0 2 0 3 1l2 1h1c-3 0-6 0-8 2l-2 2c1 1 1 1 2 1 3 3 7 4 8 8h0l-1-1c-2-3-5-5-8-6v1c4 2 7 4 9 8 0 1-1 1-1 2h-2l-2-5h0 1 0c-1-1-1-2-2-2-2-1-3-2-4-3v-1c0-1 0-1-1-1h0v1 2c1 0 1 0 2 1 0 0 1 0 1 1 1 2 3 3 3 6-1-1-2-3-3-5l-3-3v5l-1 1v1h0-1c-1-1 0-2 0-3-1-2 0-3 0-5h-1l-1-1c-2 3-4 7-3 11 0 2 2 5 5 7 4 2 9 3 14 2h2 0-1c-1 1-2 1-3 1-5 1-10 0-14-3-2-2-4-6-4-9-1-5 3-9 6-13z" class="J"></path><path d="M116 545h1c1-2 1-3 1-5v-2h0c-1-2-3-4-3-7 2 2 2 4 4 6 1 2 4 4 5 6 3 3 5 6 7 9 2 2 4 3 5 5 0 3-1 4-2 6h-1c0-1 0-1-1-1v1h-3c-1 0-2-1-2-2-1-1 0-2 1-3-1-5-1-10-4-14-1-2-3-3-5-5v4l1 1h-1c-1 0-4 4-4 6-2 4-1 8 1 11 1 3 2 4 4 6h-1c-2-3-4-5-6-8-1-5 0-8 2-13l1-1z" class="E"></path><path d="M128 558c0-1 0-2 1-3v-2h1v3 3l1 1c0-1 0-1 1-1 1 1 1 1 1 2l-1 1v1h-3c-1 0-2-1-2-2-1-1 0-2 1-3z" class="T"></path><path d="M512 552c1 8-1 17-5 24 0 2-1 4-2 5h0 0l-1 1-1 1h0-1c1-1 1-1 1-3h0c0-3 1-6 1-9h0c1-2 1-3 1-4l-1-1v-2c-1-1-2-1-2-2h0c1 0 1 1 1 1h1v-2c0-1-1-2-1-2 0-1-1-2-1-3l-2-2 1-1c2 3 4 6 4 10h1l-1-9v-1c0 1 0 2 1 3h0v-1h1v1 1l1-1h1c0-1 1 0 2 1 1-1 1-4 1-5z" class="B"></path><path d="M506 563c1 1 2 2 2 4-1 0-1 1-1 2-1 0-2-5-2-5v-1h1z" class="O"></path><path d="M507 557l1-1h1c0-1 1 0 2 1 0 0-1 1-1 2v1c-1 2 1 4 0 5-1-1-1-1-1-2h-1-1v-1c1-2 1-3 0-5zM322 432c1 1 1 1 1 2 1 1 2 4 2 5 0 5 1 9 0 14-1 3 0 7-2 9v7c-1 3-2 6-2 10 0 1 0 2-1 4l-1 3v1h0-1s-1 1-1 2c-1 1 0 3 0 4h1l-1 2s0-1-1-2v-10-3h1c0-1 0-2 1-3h1c0-2 0-4 1-6l1-9c1-1 1-2 1-3v-2-1c0-2 0-5 1-6v-3-8c0-3-1-5-1-7z" class="G"></path><path d="M317 480c0-1 0-2 1-3h1c-1 1-1 2-1 3l-1 3v-3z" class="B"></path><path d="M320 477c1 2 0 4 0 6l-1 3v1h0-1l2-10z" class="F"></path><path d="M322 468v1h0 1c-1 3-2 6-2 10 0 1 0 2-1 4 0-2 1-4 0-6l2-9z" class="H"></path><path d="M323 434c1 1 2 4 2 5 0 5 1 9 0 14-1 3 0 7-2 9v7h-1 0v-1-1c0-2 0-3 1-5v-1-4c1-6 2-12 1-18 0-1 0-3-1-4v-1z" class="U"></path><path d="M261 162v-6l1-1v4h0c1 4 2 6 4 9 2 2 5 4 8 4 1 0 3-1 5-1 1-2 3-3 5-3h0l1 1 1-1v-2l1 1h0 2l1 1c-1 0-3 0-3 1h0l-1 1v1c1 0 1 0 1 1l-4 4c-1 1-3 1-4 1-3 1-7 0-10-2h0c-4-3-6-7-7-11l-1-2z" class="Z"></path><path d="M278 174l-1 1c0-1-1-1-2-1-1 1-2 1-3 0h-1c-1 0-3-1-4-3l-1-1s0-1-1-1l1-1c2 2 5 4 8 4 1 0 3-1 5-1h0c0 1-1 1-2 1v1s-1 0-1 1h2z" class="D"></path><path d="M285 169l1-1v-2l1 1h0 2l1 1c-1 0-3 0-3 1h0l-1 1v1c1 0 1 0 1 1l-4 4c0-1 0-1-1-2 0 0-1 1-2 1s-1 0-2-1h0-2c0-1 1-1 1-1v-1c1 0 2 0 2-1h0c1-2 3-3 5-3h0l1 1z" class="K"></path><path d="M284 168l1 1h0l-2 3-1-1c1-1 1-2 2-3z" class="F"></path><path d="M277 173l5-2 1 1c-2 0-4 1-5 2h0-2c0-1 1-1 1-1z" class="H"></path><path d="M283 172l2-3c1 1 1 1 1 2-1 1-2 2-4 3 0 0-1 1-2 1s-1 0-2-1c1-1 3-2 5-2z" class="G"></path><path d="M285 169l1-1v-2l1 1h0 2l1 1c-1 0-3 0-3 1h0l-1 1v1c1 0 1 0 1 1l-4 4c0-1 0-1-1-2 2-1 3-2 4-3 0-1 0-1-1-2h0z" class="D"></path><path d="M372 634v-3c0-5-1-10 0-14s3-7 6-9c2-1 4 0 6 1 3 1 5 2 6 6 0 1-1 3-2 4h-2l-1-1c-3 2-3 0-6-1h-1v-1h4v1 1h2v-1c-1 0-1-1-2-1v-1h-3c-1 1-5 3-6 4v4 10l-1 1z" class="E"></path><path d="M380 610h3c2 0 5 2 6 4v1l-3 3c-2-1-3-3-4-4-1 0-2-1-2 0-3 0-6 3-7 5 1-3 2-6 4-9h3z"></path><path d="M442 95h1c1 1 1 2 1 4h0 1c0-1 0-2-1-3h0l1-1 1 3v2h1 0l3-2 2-1 1 1c2 2 3 3 4 5v3c1 2 1 4 1 6-1 2-2 3-1 5h1l1-1v4c-1-1-2-1-3-1v1 1l-1 1-1-1c-1 0-1 1-2 1l1 1h-1c0 1-1 1-1 2-1-1-2-1-2-2 1-4 5-6 7-10 0-2 0-5-2-7 0-1-2-2-3-2-3-1-4 0-6 2h-1v-5c0-1-1-4-2-6z" class="D"></path><path d="M452 122c0-1 1-2 1-3h1v2c-1 0-1 1-2 1z" class="Q"></path><path d="M446 98v2h1c0 1 0 1-1 2v1h-1v-2c1-1 1-2 1-3h0z" class="c"></path><g class="O"><path d="M447 100h0 2 3v1c1 0 1 1 1 1v1l-2-2s-1 0-1 1c-1 0-2-1-2 0h-2c1-1 1-1 1-2z"></path><path d="M453 98c2 2 3 3 4 5v3l-2-4c-1-1-1-2-3-2h0c1-1 1-1 1-2z"></path></g><path d="M452 97l1 1c0 1 0 1-1 2h0v1-1h-3-2l3-2 2-1z" class="G"></path><path d="M452 97l1 1c0 1 0 1-1 2l-2-2 2-1z" class="L"></path><path d="M185 367h6l1-1h8 0c7 1 13 3 19 7 0-1 0-2-1-3 1 1 2 1 3 2h1c1 0 1 1 1 1 0 1 1 1 2 1h1l1 1c-2 1-3 1-5 1s-4-2-6-2c1 1 4 2 6 3 1 1 3 1 5 1 1 0 2 1 4 1v1h0-1c-3-1-6-1-9-2h0l-5-3-1 2c-3-2-6-3-9-4l-3-1v-2c-6-2-12-2-18-3z" class="N"></path><path d="M200 366c7 1 13 3 19 7h1v1c-1 0-2-1-3-1-2-1-4-2-5-2l-9-3c-2 0-4-1-6-1l3-1z" class="P"></path><path d="M203 370c3 0 7 1 9 2s3 2 4 3l-1 2c-3-2-6-3-9-4l-3-1v-2z" class="Y"></path><path d="M162 306c4-5 10-10 16-12h1c-3 2-4 3-6 5-2 1-3 1-4 3-1 1-3 2-4 3 0 1-1 2-2 3 0 0 0 1-1 1l-2 2c-2 3-4 6-6 10-1 1-2 5-2 6h-1c-1 5-3 11-2 17l-1-1-1 1c-1-11 2-20 7-30 0-1 2-2 3-4h0c1-2 3-3 5-4z" class="G"></path><path d="M162 306c4-5 10-10 16-12h1c-3 2-4 3-6 5-2 1-3 1-4 3-1 1-3 2-4 3 0 1-1 2-2 3 0 0 0 1-1 1l-2 2c-2 3-4 6-6 10-1 1-2 5-2 6h-1l3-6c0-3 2-6 3-8 2-2 4-4 5-7z" class="P"></path><path d="M178 447c-5 4-11 8-17 9-10 2-20 0-28-6-3-3-7-8-8-13 0-2 0-9 2-11v1 3c0 5 3 10 6 13 5 5 13 8 20 8 9 0 18-4 24-10h1c-7 7-15 11-25 11-7 0-15-3-20-8-3-3-5-6-7-10 0 4 3 9 5 12 4 5 10 8 17 9 10 1 19-1 27-7l3-2v1z" class="a"></path><path d="M482 398c1 0 1-1 2 0-1 0-1 1-2 1v1a30.44 30.44 0 0 0-8 8h0c-1 3-2 7-1 9s3 3 5 3l2 1h1l-2 1v-1c-2 1-3 3-4 4h-3c-1-1-1-2-2-3l3-3h-1l-2 1h0l-1 1h0-1c0-1-1-2-1-4l-4 9v-1 1l-1-1c3-6 3-12 8-17 0 0 2-3 3-4 0 0 1-1 2-1 1-1 2-1 2-3l5-2z"></path><path d="M482 398c1 0 1-1 2 0-1 0-1 1-2 1-6 4-13 9-15 17v1c1 1 2 2 2 4h-1c0-1-1-2-1-4l-4 9v-1 1l-1-1c3-6 3-12 8-17 0 0 2-3 3-4 0 0 1-1 2-1 1-1 2-1 2-3l5-2z" class="W"></path><path d="M386 625c-2 1-4 3-5 4v2c1 0 2 1 3 1s2 1 3 1v1c-2 1-3 1-5 1s-5 0-7-2-1-6-1-8c1-2 3-4 5-5 5-1 10 1 14 3h-1-1c-2-1-4-1-7-1l4 1h2c0 1 1 1 2 1h-1c1 1 1 1 2 1l-1 1c-1-1-2-1-3-2-2 0-2 0-3 1z" class="E"></path><path d="M384 622l4 1c-1 0-2 0-3 1h-2c-2 1-4 2-5 4v2c2 3 3 3 6 4h0c-2 0-5 0-6-1-2-1-3-2-3-4s0-4 2-6h0v2c2-1 3-2 6-2 0 0 1 0 1-1h0z"></path><path d="M524 87h1l2 2c-1 1-1 2-2 3-4 2-6 2-10 1h0c4 0 7 0 10-2v-1c-1 1-3 1-4 2h-1c-1 0-4 0-5 1-2 0-7-1-9-1 2 2 5 1 7 3s4 2 6 3v1c-4 0-7-3-11-5h-2c-1-1-2-1-2-1 0 1 2 2 3 2 3 1 8 3 10 5h-1l-25-12c4 0 7 0 11 1 7 0 14 0 22-2z" class="N"></path><path d="M444 114l1-1c1 0 1 1 1 1 1 1 2 2 2 3-1 1-3 4-3 6 1 2 3 4 5 4 1 1 3 1 4 1h1l1 1 4-1c-1 2-4 4-7 4h-2-2c-3-1-6-1-9-3 0-2-2-3-2-6h2l3 3c0-1-1-2-1-3-1-1-3-2-4-4l2-2 1-1 1 1v-1l2-2h0z" class="B"></path><path d="M444 114l1-1c1 0 1 1 1 1h0v1h0c0 2-1 4-2 6 0-1 0-5 1-6v-1h-1z" class="c"></path><path d="M456 129l4-1c-1 2-4 4-7 4h-2-2 1v-1c-1 0-2 0-4-1v-1c3 1 7 1 10 0z" class="K"></path><path d="M443 126c0-1-1-2-1-3-1-1-3-2-4-4l2-2c0 1 1 1 1 2l1 1s0 1 1 2c0 2 2 4 4 5h0v1l-2-1v1c-1-1-2-1-2-2z" class="D"></path><path d="M377 338v-3c4-3 9-4 14-5 6-1 12-2 18-2-2 2-6 1-9 2-1 0-2 1-3 2h-2l-3 3h0c1 0 1-1 2 0h0l-2 2c0 1-1 1-2 1-2 1-3 3-5 4l-2 1v-1c-1-1-2-1-2-2v-1h-2-2v-1z" class="O"></path><path d="M384 336h4 0c0 1-1 2-2 2s-1 0-1 1h-1l-1 1h1c1 0 2-1 3-2v1c0 1-2 2-3 2v1h1l-2 1v-1c-1-1-2-1-2-2v-1h-2l5-3z" class="b"></path><path d="M377 338c3-2 5-4 8-5l5-1 1 1c-1 1-4 1-6 3h-1l-5 3h-2v-1z" class="G"></path><path d="M181 644c3-1 7 0 10-1 1 0 1 0 1-1 3 0 6-1 9-1s5-2 8-2c1-1 2-1 4-1 4-2 7-3 11-5 1 0 3-1 4-2h2c-4 2-7 4-10 5l-10 4h0 3c1-1 2-1 3-1 1-1 2-1 3-2h1c0 1 0 2-1 2h-1v1h-2v1h0 1 0c0 1-1 1-1 1-1 0-1 0-1 1l-1-1s-1 0-1-1h0c-2 0-5 1-7 2-2 0-4 0-5 1h-3c-1 1-3 1-4 2-1 0-2 1-4 1h-9-15c-2 0-3-1-5-1h0v-1c2 1 4 0 5 1h1 9c2-1 5 1 7-1h-4l-1-1h3z" class="D"></path><path d="M236 623l-1 1-1-2c-1-1-1-2 0-3 1-2 2-3 3-3l1-1c1 0 1-1 3-2l3-1h4c1-1 2 0 3 0 4 0 7 1 10 2 2 1 3 2 5 3s4 1 5 2h0c1 2 10 4 10 6-1 0-4-2-5-2h-2c-8-4-21-9-30-6-3 1-6 3-9 6h1z" class="S"></path><path d="M191 294h2 1l3 2v-1c1 1 3 2 3 3 1 1 1 2 2 3 2 2 4 5 3 8h0v4c-1 3-5 7-7 9-1 0-2 1-3 1 0 1-1 1-2 1-3 1-6 1-9 0-1 0-2-1-4-2l1-1 1 1h3v-1c1 0 3 0 4-1h-3 1c4 0 8-1 11-4s4-7 4-11l-1-1c-1-4-4-7-7-9l-3-1z" class="K"></path><path d="M185 322h2 3c2-1 4-1 7-1l-1 1h-1v1c0 1-1 1-2 1-3 1-6 1-9 0-1 0-2-1-4-2l1-1 1 1h3z" class="Q"></path><path d="M191 294h2 1l3 2v-1c1 1 3 2 3 3 1 1 1 2 2 3 2 2 4 5 3 8h0v4c-1 3-5 7-7 9-1 0-2 1-3 1v-1h1l1-1 3-3c2-1 2-3 3-5v-1s1-1 1-2 0-3-1-4h0v4h-1v-5l-1-1c-1-4-4-7-7-9l-3-1z" class="H"></path><path d="M224 200c1 1 3 3 4 5h1c-1 1-2 1-3 1s-1-1-2 0v-1c0-1 1-1 2-1v-1c-2-1-4 0-6 0v1h1 1 0c-1 1-1 1-3 1l-1 1h0l1 1h-1c-1 1-1 1-1 2-1 0-3 1-3 1 0 1 0 1 1 2h-1l-1-1-1 1-1-1c-1 0-1 0-2-1h0v-1c-1-1-2 0-3 0h-3v1h-1l-1-1-3-1c-1-1-2-1-4-1l1-1h2c2 0 5-1 7-2h1s1-1 2-1c2 0 5-1 7-1 2-1 3-1 4 0 1 0 3-1 3-1h3v-1z" class="P"></path><path d="M202 208c1 0 4 0 5-1h-1c1 0 2-1 3-1 0 1 3 2 3 3h1-1c-1 0-2 1-3 1h0v-1c-1-1-2 0-3 0v-1c-2 0-3 1-4 0z" class="O"></path><path d="M218 206l1 1h-1c-1 1-1 1-1 2-1 0-3 1-3 1 0 1 0 1 1 2h-1l-1-1-1 1-1-1c-1 0-1 0-2-1 1 0 2-1 3-1h1v-1c2 0 3-1 5-2z" class="U"></path><path d="M214 202c2-1 3-1 4 0 1 0 3-1 3-1h3v1h-2c-2 0-3 1-4 1l-1-1c-2 1-5 2-7 3-3 0-5 0-8 1-1 0-2 0-3 1 1 1 2 1 3 1 1 1 2 0 4 0v1h-3v1h-1l-1-1-3-1c-1-1-2-1-4-1l1-1h2c2 0 5-1 7-2h1s1-1 2-1c2 0 5-1 7-1z" class="K"></path><path d="M466 127v2s-1 1-1 2c-3 5-10 11-9 17h0l1-1h0c0 1 1 1 1 1 0 2 0 2 1 3v2h0v3c0 1 1 2 2 3-1 0-2-1-3-1s-1 0-1-1l-1 1c2 3 6 8 10 9h1c2 1 4 1 5 2 2 1 3 1 4 1h-6c-2 0-5-2-8-3-5-3-9-9-10-15-2-11 8-16 13-24l1-1z" class="R"></path><path d="M456 148l1-1h0c0 1 1 1 1 1 0 1 0 2-1 3v2h-1v-4-1z" class="H"></path><defs><linearGradient id="y" x1="227.742" y1="448.916" x2="220.435" y2="452.21" xlink:href="#B"><stop offset="0" stop-color="#565656"></stop><stop offset="1" stop-color="#6c6c6d"></stop></linearGradient></defs><path fill="url(#y)" d="M224 437c1 4 1 7 1 11 1 2 2 4 2 5 1 2 2 4 2 6 0 0-1 0-1 1s1 2 1 3l1 6-1 1v1h-1v3c2 5 3 12 3 17v3h0c-1 0-1-7-2-9 0-3-1-6-2-9-1-7-2-15-4-22l-1 5v-2c0-1-1-1-1-2l1-13c0-1 0-2 1-3 0 0 1 0 1-2z"></path><path d="M228 474v-3s0-1-1-1c0-1 0-3-1-5v-5h0c1 2 1 3 2 4l1-1 1 6-1 1v1h-1v3z" class="N"></path><path d="M410 89c-1-3-3-3-5-4v-1h2 0c-1-1-1-1-2-1-2 0-4-1-6-1h-2c1-1 6-1 9-1 3 1 7 3 9 6 1 2 2 5 1 8-1 1-2 4-4 4-2 1-4 1-5 1-2-1-2-2-3-3h0c-1-1-1-2-1-3 0-2 3-3 4-4v-2h0l1 1h2z"></path><path d="M404 97c-1-1-1-2-1-3 0-2 3-3 4-4v-2h0l1 1h2c1 1 1 3 1 4s-1 2-1 3c-1 1-2 1-3 2-1-1-2-1-2-1h-1 0z" class="U"></path><path d="M410 89c1 1 1 3 1 4s-1 2-1 3h-3v-1h1c0-1 0-1 1-2-1 0-2 1-2 0h0l1-1 1-1h-1v-2h2z" class="W"></path><path d="M238 239v1c0 1-1 1-1 2-1 0-1 0-1 1s-1 2-1 4c1-1 2-3 3-4 1 0 0-1 2-1v1c3-2 3-4 6-4h2l1 1c1 0 1 0 2-1 1 0 2 0 3-1h2c0 1 0 1 1 1h1 0v1 3c-1 1-3 1-4 1-2 0-4 1-6 2-1 0-2-1-2-1-2 1-4 3-5 4l-1-1c1-1 2-2 3-2l-1-1-2 3-1-1 1-1h-1l-1 1v1c-1 0-1 1-1 2-1 0-1 0-1 1h0c-1 1-1 2 0 3h-1v2h0c-1 2-1 3-2 5-1-3 0-6 0-8 0-5 0-10 5-14z" class="B"></path><path d="M258 240v3c-1 1-3 1-4 1-2 0-4 1-6 2-1 0-2-1-2-1 3-2 8-4 12-5z" class="f"></path><path d="M105 468l5 9 2 2 1-1c2 1 3 4 5 5-2 0-5-4-6-4v1c1 1 3 2 3 3l1 2-1 1h1s1 1 2 1c2 2 5 3 8 4 1 0 1 1 2 1h2l3 1h1c1 0 1 1 2 1h1 1 2v1h0c2 2 9 1 11 1 1 0 2 0 3 1h-1c-14 1-29-3-41-11v2h-1v-1-1c1-1-5-6-6-7-1-2-2-3-3-5 1 0 1-2 1-3l1 2h1v-1c0-1-1-2-2-2v1l-1-1v-1h-1v-1l1 1c1 0 2-1 3-1z" class="I"></path><path d="M115 486h0c-1-1-3-2-4-3s-2-3-3-4c-1 0-1-1-1-1h-1v-1l-1-1s-1-1-1-2v-1c1 1 1 1 2 1s3 3 4 4v-1l2 2 1-1c2 1 3 4 5 5-2 0-5-4-6-4v1c1 1 3 2 3 3l1 2-1 1z" class="E"></path><path d="M256 505l3 3 3 3c1 0 3 1 3 1h1c2 1 4 2 7 2h4c2 1 7 0 10-1v1c-1 1-2 1-3 1v1h0c-1 0-1 0-1 1h0l-1 1c-1 0-1 0-2 1h-1-8l-1-1c-1 0-2 0-3-1-4 0-7-1-11-2 0-1-3-2-3-2-2-1-3-2-5-4l-2-2h1c2 1 5 5 8 5-1-1-3-2-4-3h1l6 3c-1-1-2-2-2-3h0c1 0 2 1 4 2h1l-6-5 1-1z" class="W"></path><path d="M272 518c1 0 3 0 4-1h1 2c2-2 3-2 5-2v1h0c-1 0-1 0-1 1h0l-1 1c-1-1-2-1-4-1-1 0-1 1-2 1h0-4z" class="T"></path><path d="M272 518h4 0c1 0 1-1 2-1 2 0 3 0 4 1-1 0-1 0-2 1h-1-8l-1-1c-1 0-2 0-3-1 1 1 3 1 5 1zm-16-13l3 3 3 3c1 0 3 1 3 1v1c2 1 5 2 8 3h-2c-3-1-12-3-13-4s-2-2-2-3h0c1 0 2 1 4 2h1l-6-5 1-1z" class="g"></path><path d="M413 328c5 2 9 4 11 9 1 1 1 3 2 4 2-5 2-10 6-13 4 0 8-1 11-1h5l1 1h0l-1 1-1-1h0c-6 1-10 4-13 9l-4 6c1-5 4-9 8-12l3-3c-3 1-7 4-9 7-2 2-3 5-4 7h0c-1 1-1 2-1 3 1-1 1-2 2-3v1c-1 4-2 8-2 12-1 1 0 2 0 3-1-2-1-4-1-6 0-1-1-3-1-4 0-4 0-8-2-11s-5-5-8-6h-1l-2-1c-1-1-2-1-3-1l4-1z" class="R"></path><path d="M266 363l-3-2c2-2 4-4 5-6l4 5c1 2 2 2 3 4 0 1 4 3 5 3l1 1h1c-1 1-1 1-3 1-2-1-5-2-6-3l-1-1c-1 0-1 0-2 1v1h0c-1 0-1 0-2 1h1 1c1-1 1-1 2-1h1v1c-1 0-2 1-2 1l-2 2c-2 0-5-1-6-2h-1 0c-2-1-4-3-4-4-1-2-1-5 0-6 0-1 1-1 1-1s-1 2 0 3h3l3 3 1-1z" class="K"></path><path d="M271 362l-3-4h0c3 2 4 4 7 6 0 1 4 3 5 3l1 1h1c-1 1-1 1-3 1-2-1-5-2-6-3l-1-1c-1 0-1 0-2 1-2 0-4 2-5 1h0c1 0 3-2 4-2s1 0 2-1h0l4 1-4-3z"></path><path d="M266 363l-3-2c2-2 4-4 5-6l4 5c1 2 2 2 3 4-3-2-4-4-7-6h0l3 4c-1-1-2-2-4-3l-1 1 1 1 1 1s1 0 2 1h-2c-1 0-1 1-2 1v-1z" class="R"></path><path d="M239 273c1 3 2 6 4 9 2 5 7 9 12 12 3 3 6 4 9 6 4 1 8 2 11 3h3 0c-2 1-5 0-8 0l-1 1c-4-1-9-3-13-5-7-4-15-9-18-17-1-2-1-5-2-7v-1h2v-1 1-1h1z" class="I"></path><path d="M249 294l1-1-2-1 1-1c2 1 4 4 6 3 3 3 6 4 9 6 4 1 8 2 11 3h3 0c-2 1-5 0-8 0-7-2-14-5-21-9z" class="P"></path><path d="M239 273c1 3 2 6 4 9 2 5 7 9 12 12-2 1-4-2-6-3l-1 1 2 1-1 1-2-2c-6-5-9-10-9-18v-1 1-1h1z" class="d"></path><defs><linearGradient id="z" x1="468.047" y1="428.454" x2="457.371" y2="412.602" xlink:href="#B"><stop offset="0" stop-color="#767776"></stop><stop offset="1" stop-color="#8e8c8d"></stop></linearGradient></defs><path fill="url(#z)" d="M454 426c1-6 3-10 7-15 3-4 11-10 16-11 0 2-1 2-2 3-1 0-2 1-2 1-1 1-3 4-3 4-2 0-6 4-7 5-2 3-3 7-3 10v1 5h-2s0 1-1 1c-1 2-1 8 0 10 0 2 1 4 1 5l-1 1-1-5v2h-2v-2-13-2z"></path><path d="M458 429v-3c1-1 1-2 2-2v5h-2z" class="g"></path><path d="M454 428v-2 7c0 2 0 3 1 5 1 1 1 2 1 3h0v2h-2v-2-13z" class="D"></path><defs><linearGradient id="AA" x1="244.61" y1="480.586" x2="264.95" y2="484.253" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#232225"></stop></linearGradient></defs><path fill="url(#AA)" d="M272 461h5-1v1c-4 0-7 2-11 3-2 1-5 2-7 4-7 5-11 12-12 20 0 3 0 7 1 10 1 4 3 6 5 9-5-3-6-5-8-10 0-1-1-2-1-2v-5c0-4 1-8 2-12v-1h0c2-3 4-6 6-8 6-5 14-7 21-9z"></path><path d="M495 145h1v3c2 6 3 14 1 20-1 3-2 5-3 7h-1c-1 0-2 2-3 2h0v-1h-1-1c-1-3 1-6 3-8l-1-1 1-1c-1 0-2 0-2-1l2-2h-1c1-2 2-3 2-5v-1c0-1 1-3 1-5 0-1 0-2 1-2h0l1-1v-4z" class="J"></path><path d="M496 154h0c1 3 1 6 0 8-1-1-1-2-2-3l2-5z" class="M"></path><path d="M493 166c0 1 0 3-1 4 0 1-3 4-3 4v2h-1c-1-3 1-6 3-8 1 0 2-2 2-2z" class="R"></path><path d="M494 159c1 1 1 2 2 3l-3 4s-1 2-2 2l-1-1 1-1c-1 0-2 0-2-1l2-2c1 0 1-1 2-3v1l1-2z" class="P"></path><path d="M493 160v1c0 2-1 4-2 5-1 0-2 0-2-1l2-2c1 0 1-1 2-3z" class="F"></path><path d="M495 145h1v3c0 2-1 4 0 6l-2 5-1 2v-1c-1 2-1 3-2 3h-1c1-2 2-3 2-5v-1c0-1 1-3 1-5 0-1 0-2 1-2h0l1-1v-4z" class="I"></path><path d="M493 152c0-1 0-2 1-2h0c0 3 0 6-1 9v1c-1 2-1 3-2 3h-1c1-2 2-3 2-5v-1c0-1 1-3 1-5z" class="R"></path><path d="M446 629l1 1c2-1 3-1 6 0l-10 3h0l-6 1-6 1c-2 1-7 2-10 1v1h-2l-1 1h-1-2c-4-1-7-1-10-3h1 3c0-1 1-1 2-1-1-1-3-2-4-2h6c1 0 2 1 4 1v-1h2 2 9 4c4-1 8-2 11-3h1z" class="I"></path><path d="M429 633c5 0 10-1 15-2h0c-3 2-7 3-11 3h-12c-2 1-3 1-5 1h-7c0-1 1-1 2-1-1-1-3-2-4-2h6c1 0 2 1 4 1 4 1 8-1 12 0z" class="b"></path><path d="M407 632h6c1 0 2 1 4 1 4 1 8-1 12 0h-9c-3 1-6 1-9 1-1-1-3-2-4-2z" class="K"></path><defs><linearGradient id="AB" x1="417.252" y1="638.495" x2="419.386" y2="631.65" xlink:href="#B"><stop offset="0" stop-color="#535254"></stop><stop offset="1" stop-color="#6a6a67"></stop></linearGradient></defs><path fill="url(#AB)" d="M409 635h7c2 0 3 0 5-1h12s-1 1-2 1c-2 1-7 2-10 1v1h-2l-1 1h-1-2c-4-1-7-1-10-3h1 3z"></path><path d="M418 636h3v1h-2l-1 1h-1l1-2z" class="c"></path><path d="M418 636l1 1h0l-1 1h-1l1-2z" class="X"></path><defs><linearGradient id="AC" x1="328.517" y1="631.91" x2="329.417" y2="639.652" xlink:href="#B"><stop offset="0" stop-color="#4a4b4c"></stop><stop offset="1" stop-color="#706f70"></stop></linearGradient></defs><path fill="url(#AC)" d="M339 635v-1c2 0 3 0 5-1s3-2 5-2c-2 3-3 6-7 8-4 1-9 1-13 1-1-1-4-1-6-2v1h-2c-4-1-13-4-16-8 2 1 4 2 7 2 1 0 3 0 5 1h0 3c6 1 12 0 18 0l1 1z"></path><path d="M320 634c6 1 12 0 18 0l1 1c-1 0-2 0-4 1-2 0-5 1-8 1-1-1-3-1-4-1-2 0-4-1-6-1v-1h3z" class="O"></path><defs><linearGradient id="AD" x1="185.968" y1="365.685" x2="175.749" y2="373.41" xlink:href="#B"><stop offset="0" stop-color="#605f62"></stop><stop offset="1" stop-color="#949394"></stop></linearGradient></defs><path fill="url(#AD)" d="M152 327c0-1 1-5 2-6 2-4 4-7 6-10l2-2c-2 4-5 8-7 12-3 7-6 19-4 26 1 2 1 4 2 6 2 4 7 8 11 10 6 4 14 6 20 7h9l-4 1c1 0 3 0 5 1h2c1 0 2 1 4 1l-1 1h0c-2-1-5-1-7-1h0v1l-16-3c-5-1-14-4-18-8-1-1-2-3-3-4l6 5c2 1 5 2 8 3-1-1-4-2-5-2-4-3-8-6-11-10-5-9-3-18-1-28z"></path><path d="M482 584l1-1c1 0 1 0 1 1h2v3c0 2-1 4-1 5v3c0 1-1 3-2 4l-2 7v1c0 1-1 2-1 3-3 3-5 5-9 5l3-3v-2l1-9c0-1 1-1 1-2v1h1v-1-1h0l1-1v-1c-1 0-1-1-1-2h1v1 1l1-1v1l1 1v-7l1-1v-3c0-1 0-2 1-2h0z" class="c"></path><path d="M480 590l1-1v1c-1 1-1 3 0 4s0 3 0 4l-1 3c0-1-1-3-1-5l1 1v-7z" class="D"></path><path d="M482 584l1-1c1 0 1 0 1 1h2v3c0 2-1 4-1 5v3c0 1-1 3-2 4v-7c0-2-1-5-1-7l1-1h-1z" class="Z"></path><path d="M482 584l1-1c1 0 1 0 1 1h2v3c0 2-1 4-1 5v3c-1-4-1-7-2-11h-1z" class="P"></path><path d="M477 600v-1-1l1 2c1 2 0 5 0 7-1 1-2 2-3 4 0 0-1 0-1 1v-2l1-9c0-1 1-1 1-2v1h1z" class="D"></path><path d="M475 601c0-1 1-1 1-2v1c0 1 1 3 0 5h-1v-4z" class="L"></path><path d="M477 600v-1-1l1 2v4l-1 1v-5z" class="E"></path><path d="M297 460v-1h1c6 4 12 9 13 16h0l1 3 1 3v10c-1 0-1 0-2-1v-1l-1 1s-1-1-1-2c0-5-1-9-3-14-1-3-2-5-5-8-2-1-4-2-7-3v1c-3-1-5-2-8-3h0v-1h0c2-1 4 0 6 0l1-1c1 1 2 1 4 1z" class="M"></path><path d="M286 460c2-1 4 0 6 0 1 1 2 1 2 1 1 1 7 4 7 5h0c-2-1-4-2-7-3v1l-8-3h0v-1h0z" class="L"></path><path d="M297 460v-1h1c6 4 12 9 13 16h0v2h-1c-2-5-4-8-8-12 0-1-1-1-1-1-1 0-1 0-1-1h1 0c0-1-3-2-4-3z" class="E"></path><path d="M301 466h0c4 2 6 6 8 11 0 1 0 2 1 3 0 0 0-1 1-1l-1-2h1v-2l1 3 1 3v10c-1 0-1 0-2-1v-1l-1 1s-1-1-1-2c0-5-1-9-3-14-1-3-2-5-5-8z" class="B"></path><path d="M312 478l1 3c0 1 0 2-1 4v-1l-1-3h1v-3z" class="E"></path><path d="M311 475l1 3v3h-1v-2l-1-2h1v-2z" class="F"></path><path d="M488 534c3-4 6-4 11-4h2c-4-4-9-7-9-12v-10c0-2 0-4 2-6h1c3-2 5-3 8-4s8-1 11 0 6 4 8 7v1h0c-3-4-6-6-10-7s-7-1-10 0-7 3-9 6v2c1 0 1-1 1-1 2-3 6-5 9-6 3 0 8 0 11 2h0-2c-3-1-9-1-13 0-2 2-5 4-6 7v2h0c-1 4-1 9 2 12 1 2 2 2 3 3l3 3c2 1 4 2 6 4 5 3 9 8 9 15 2 12-3 25-10 35h-1c4-7 6-13 8-20 1-3 2-6 2-9 0-6 0-12-5-17-3-4-8-6-13-6-3 0-5 1-8 3h-1z" class="W"></path><defs><linearGradient id="AE" x1="262.619" y1="445.144" x2="261.581" y2="456.723" xlink:href="#B"><stop offset="0" stop-color="#5a5a5a"></stop><stop offset="1" stop-color="#797879"></stop></linearGradient></defs><path fill="url(#AE)" d="M244 444l1-1v-1h0l2 2c1 0 2 1 3 2 3 1 7 3 11 3l2 1 3 2h-3c1 1 2 1 3 1 5 2 10 1 15 2h3l-1 1h-6v1h2c-1 1-3 0-5 0-3 0-6 0-9 1l-4-1h-2c-5-1-12-8-15-13z"></path><path d="M263 454h0c1-1 2-1 3-1 5 2 10 1 15 2h-6c-3 2-9 0-12-1z" class="N"></path><path d="M247 444c1 0 2 1 3 2 3 1 7 3 11 3l2 1 3 2h-3c1 1 2 1 3 1-1 0-2 0-3 1h0-2c-4-1-8-3-11-6-1-1-3-3-3-4z" class="l"></path><path d="M250 448h1 0c1 1 3 1 4 2 2 1 3 2 5 3h1v1c-4-1-8-3-11-6z" class="Y"></path><path d="M305 500l4-6c1 1 1 1 1 2s-1 2-1 3c-3 8-9 14-16 17-4 2-9 4-13 3 1-1 1-1 2-1l1-1h0c0-1 0-1 1-1h0v-1c1 0 2 0 3-1v-1s-1 0 0 0c2-1 5-2 7-4h-2 0v-2c0-1 3-1 3-1l5-3h1 1v1c1 0 1-1 2-2s1-1 1-2z" class="E"></path><defs><linearGradient id="AF" x1="294.039" y1="505.296" x2="294.697" y2="514.304" xlink:href="#B"><stop offset="0" stop-color="#868683"></stop><stop offset="1" stop-color="#9f9da2"></stop></linearGradient></defs><path fill="url(#AF)" d="M301 503h1v1c1 0 1-1 2-2l1 1c-2 2-4 5-6 6l-1 1c-1 1-3 2-5 3l-4 2c0 1-1 1-1 1h-1l-4 1h0c0-1 0-1 1-1h0v-1c1 0 2 0 3-1v-1s-1 0 0 0c2-1 5-2 7-4h-2 0v-2c0-1 3-1 3-1l5-3h1z"></path><path d="M300 503h1c-3 3-5 4-9 6v-2c0-1 3-1 3-1l5-3z" class="f"></path><path d="M247 499c-1-3-1-7-1-10 1-8 5-15 12-20h0v1c1 0 1-1 1-1l1 1-1 1h0c-1 0-3 3-4 4l-1 3c-2 2-3 3-3 6-1 2-1 5-1 8v1c0 1 1 3 1 4h0l3 5c1 1 1 2 2 3l-1 1 6 5h-1c-2-1-3-2-4-2h0c0 1 1 2 2 3l-6-3v-1c-2-3-4-5-5-9z" class="B"></path><path d="M247 499h1v1c1 2 2 3 3 4h0v-1c-1-1-1-2-2-3v-1c-2-3 0-8 0-11 1 1 0 3 1 4v1c0 1 1 3 1 4h0l3 5c1 1 1 2 2 3l-1 1 6 5h-1c-2-1-3-2-4-2h0c0 1 1 2 2 3l-6-3v-1c-2-3-4-5-5-9z" class="J"></path><path d="M255 506c-1 0-1-1-2-2 0-2-1-4-2-6v-1l3 5c1 1 1 2 2 3l-1 1z" class="F"></path><path d="M191 399l-1-2h1c3 2 6 8 7 11l4 13c-1 0-1 1-1 1h-1v-1h-1v1 3c0 5 0 9-1 13 0 2-1 4-1 6s-1 3-2 5c-2 5-6 10-11 14-2 2-6 5-9 7-2 1-4 2-6 2-5 2-11 2-16 2h-1 1v-1h0c6 0 12-1 17-3 1 0 3 0 3-1 1-1 5-3 6-3 1-1 3-2 4-3 5-4 10-10 12-16 2-5 3-12 3-17 0-3-1-6 0-9h0l-1-1c-1-1-1-3-1-4v-1l-1-1v-3-1c0-1-1-3-2-4-1-2-1-5-2-7z" class="I"></path><path d="M191 399c2 1 3 3 4 5 1 3 3 5 3 7-1 2-1 2-1 3s0 1 1 1c0 0 0-2 1-2 0 1 0 3 1 5h-1v-2h-1v5l-1-1c-1-1-1-3-1-4v-1l-1-1v-3-1c0-1-1-3-2-4-1-2-1-5-2-7z" class="H"></path><path d="M313 124c-2 0-2 0-4-2h-1c1 1 1 1 1 2h0-1c-1-1-1-3-1-5 1-2 2-4 4-5 3-2 8-5 11-4 5 1 8 3 11 8 1 2 2 3 2 5s0 3 1 4c1-4 1-8 2-12l1 1c0 6-2 13-6 17-1 1-2 3-4 3h0v-1h-1c1-1 2-1 3-2 2-2 3-5 3-8 0-4-2-7-5-10-2-1-5-1-7-1-2 1-4 3-5 5l-1 2-1-1c-1 0 0 1 0 1-1 1-2 0-3 0l1 3z" class="B"></path><path d="M312 121c1-1 1-3 3-4h0v2h1 1l-1 2-1-1c-1 0 0 1 0 1-1 1-2 0-3 0z" class="H"></path><path d="M450 596c-1-1-1-2-3-3-2 0-6 0-7-3 0 0-1-1 0-2 0-1 3-3 5-4 2 0 4 0 6 1 3 2 5 7 6 10 0 1-1 3-2 4-1 3-4 5-8 6h-5c1-1 2-1 3-1l2-2h0v-1h1l1-1c1-1 1-2 1-4h0z" class="V"></path><path d="M453 588c1 3 3 6 2 9-1 2-3 5-5 5 2-2 4-4 3-8v-3-3z"></path><path d="M444 591h1 4 1l3 3v2c-1 3-3 5-5 6h-1 0v-1h1l1-1c1-1 1-2 1-4h0c0-1 0-2-1-2-1-3-3-1-5-3h0z"></path><path d="M444 591c-1 0-2 0-3-1v-2c1-1 1-2 2-2 2-1 4-1 6-1 2 1 3 2 4 3v3c-1-1-2-1-2-2-1 0-4-1-5-1 0 0-1 0-1 1 0 0-1 1-1 2h0z" class="P"></path><path d="M178 446l-3 2c-8 6-17 8-27 7-7-1-13-4-17-9-2-3-5-8-5-12 2 4 4 7 7 10 5 5 13 8 20 8 10 0 18-4 25-11 1-1 3-4 4-4-1 3-3 6-4 9z"></path><path d="M115 588c0-1 0-4-1-6s-5-4-6-7c0-1-1-3-2-4s-2-3-3-5v-1h1l1 2c1 2 1 3 2 4 3 4 7 7 10 9 1 2 3 4 5 6h1c1 0 3 1 4 1h0c2 1 4 0 5 1 0 1-1 2-2 3h-3 0-1c-1 0-1-1-2-1s-1-1-2-1h0l-1-1c-1-1-1-1-1-2h-1v1c1 1 1 2 2 3 0 3 0 6-2 10l-1-2-1 1c-2 2-4 4-7 6h-1s-1 1-2 1h0c3-2 6-4 8-6v-1c-2 1-3 2-4 3-1-1 0-1 0-2-1 1-2 2-3 2v-1h1c1-2 3-2 4-4 1 0 2-2 2-2-1 0-3 2-4 2-1 1-1 1-2 1 1-1 2-1 3-2 2-1 3-2 4-4v-4h-1z" class="S"></path><path d="M118 598c1-1 1-2 1-3v-1h-1c0 2-1 3-2 4h0c1-1 1-2 1-3 1-3 1-5 1-9h1v1c1 1 1 2 2 3 0 3 0 6-2 10l-1-2z" class="Y"></path><path d="M210 510c8 1 16 4 22 10l1-1 3 9c0 2 0 2 1 4v5h-1v2h-1c0 1-1 1-1 1-1 0-1 0-1-1l1-4c-1 0-1 1-2 2l-1-1c1-1 3-2 3-4h0c0-2 0-7-2-9-4-6-14-9-21-11h-1c-2 0-5 1-7 2-7 2-15 5-20 11-2 2-2 5-4 6 1-5 5-9 9-12v-1c0-1 2-2 3-3 2-1 4-1 6-2 4-1 8-3 13-3z" class="Q"></path><path d="M191 515l1 2-4 2v-1c0-1 2-2 3-3z" class="l"></path><path d="M210 510c8 1 16 4 22 10l1 1h-1 0c-2-4-8-6-11-7-2-1-3-1-4-2-3 0-7-1-9-1s-4 1-5 1c-4 2-8 3-11 5l-1-2c2-1 4-1 6-2 4-1 8-3 13-3z" class="P"></path><path d="M181 599c-2 2-2 6-1 9 0 1 1 3 2 4 2 1 3 2 4 2 2 0 4-1 5 0 0 1 0 1-1 2s-2 2-4 2c-3 0-5-1-7-3-3-2-5-6-5-9 0-5 3-9 6-12-3 2-4 3-6 7-1 3-1 6 0 9v1a30.44 30.44 0 0 0 8 8h-1c-4-2-7-5-8-9-2-4-1-8 1-11 2-6 6-7 12-9v1c-1 1-2 1-3 2 0 2-3 4-4 6h2z" class="I"></path><path d="M183 593c0 2-3 4-4 6s-1 4-1 7 2 6 5 7c1 1 4 2 5 2h1 0l1-1-1 1c-1 1-2 2-3 2-2 0-4 0-5-1v-1c-2-1-3-3-4-6-1-2-2-6-1-8 2-4 4-6 7-8z"></path><path d="M336 631c3-2 6-3 7-6 1-2 1-5 0-7-1-1-2-1-3-2-3 0-5 0-8 2h-1 0c-1-2 1-5 2-7 1-1 4-2 6-2 3 0 5-1 7 1v1c2 2 3 4 4 7 0 3 0 5-3 8l-3 3h0c-2 1-5 2-7 3l-1-1z" class="E"></path><path d="M344 629c0-1 1-2 1-3 1-2 1-5 0-7 0-2-2-3-3-4-3-1-6 0-9 1h-1l1-1c2-1 6-2 9-2 2 1 4 2 5 4h0 1c-1-1-2-3-3-4h0c-2-1-4-2-7-2 0 1-1 1-2 1s-1 1-3 1c1 0 1-1 1-1l3-2c2 0 7 0 9 1 2 2 3 4 4 7 0 3 0 5-3 8l-3 3z"></path><path d="M343 412c-6-10-15-19-26-23h0v-9c8 4 15 11 20 19l1-1 3 3c1 1 2 3 3 4l1 1-1 2-2-1h0c0 1 0 1 1 2v1 1 1z" class="n"></path><path d="M338 398l3 3c1 1 2 3 3 4l1 1-1 2-2-1c-1-3-3-6-5-8l1-1z" class="T"></path><defs><linearGradient id="AG" x1="155.688" y1="566.929" x2="181.28" y2="563.883" xlink:href="#B"><stop offset="0" stop-color="#68686a"></stop><stop offset="1" stop-color="#8d8d8d"></stop></linearGradient></defs><path fill="url(#AG)" d="M151 555h0c0-1 0-1-1-2-1-2-1-4 0-6 0-2 2-4 3-4 1-1 2-1 3-1l3 3v1l-2 2h-1c1-1 1-2 1-3s-1-1-2-2c-1 0-2 1-3 2-1 2-2 3-1 5 0 3 2 5 4 7 5 3 12 5 17 7-2-3-5-5-7-6v-1-1h1 1c3 4 6 7 10 10l6 5v2h-2c-1 0-2-1-3-1h1c-1-2-3-2-4-3-3-3-9-4-13-6-2 1-3 0-5-1h-4l2-1h0c-2-1-3-1-5-1l-1-1 1-1c-2 0-4-1-5-2-4-1-9-2-12-6 1 0 1 0 1-1h-1l2 1 3 2c2 1 3 2 5 2 3 1 5 2 7 3v1h1c1 0 3 1 4 2l1-1c-1 0-1-1-2-1s-3-2-3-3z"></path><path d="M224 179c1 1 1 1 2 1h0l1 3h0c0 2 1 3 2 4 0 1 1 1 1 2l-1 1h0l-1-1c-1 1-1 1-1 2-1 2-3 3-4 4 0 1-1 1-2 1v1l2 1 2 2 3 3c1 1 1 1 1 2h-1c-1-2-3-4-4-5v1h-3s-2 1-3 1c-1-1-2-1-4 0-2 0-5 1-7 1-1 0-2 1-2 1v-1c1 0 3-1 4-1 1-1 1-1 2-1v-1h0c-1-1 0-1 1-2h-3c1-1 3-1 4-1h1c1 0 1-1 2-1h1s0-1 1-2h-3-1c1-2 2-2 2-3h0l1-1v-1l1 1h0c3-1 4-6 5-8h0c1-1 1-2 1-3z" class="O"></path><path d="M214 199c2-1 4-1 6-1 1 0 3 2 4 2v1h-3s-2 1-3 1c-1-1-2-1-4 0v-1h-1c1-1 1-1 1-2h0z" class="Y"></path><path d="M223 191l1 2s-1 0-1 1l-1 1h-2-2v1h2c-2 2-4 1-6 2v1h0c0 1 0 1-1 2h1v1c-2 0-5 1-7 1-1 0-2 1-2 1v-1c1 0 3-1 4-1 1-1 1-1 2-1v-1h0c-1-1 0-1 1-2h-3c1-1 3-1 4-1h1c1 0 1-1 2-1h1s0-1 1-2h-3-1 5c2-1 3-1 4-3z" class="k"></path><path d="M224 179c1 1 1 1 2 1h0l1 3h0v3 3c-1 1-2 3-3 4l-1-2c-1 2-2 2-4 3h-5c1-2 2-2 2-3h0l1-1v-1l1 1h0c3-1 4-6 5-8h0c1-1 1-2 1-3z" class="c"></path><path d="M223 191c0-1 1-1 1-2v-1l-2 2v-1c-1-1 1-4 2-5v-2h1l2 1h0v3 3c-1 1-2 3-3 4l-1-2z" class="Y"></path><path d="M225 182l2 1h0v3h-1c-1-1-1-3-1-4z" class="S"></path><defs><linearGradient id="AH" x1="181.718" y1="331.861" x2="188.622" y2="315.121" xlink:href="#B"><stop offset="0" stop-color="#666667"></stop><stop offset="1" stop-color="#838383"></stop></linearGradient></defs><path fill="url(#AH)" d="M173 303l1-1c1-2 2-3 4-5h0c1 0 2-1 3-1h1c-2 1-4 2-6 4-3 5-4 9-3 15h0c0 5 3 9 7 12 5 4 11 5 17 4 3-1 6-2 8-3 0 1-1 2-2 3 0 1 0 1-2 2h-12c-3 0-5-1-7-1 0 1 1 1 2 1v1c1 0 2 2 3 3l-2 1v-1-1c-4-4-10-6-13-11-2-3-3-7-3-10 0-1-1-2-1-3 1-3 1-6 3-9l1 1h0c0-1 1-1 1-1z"></path><path d="M171 303l1 1h0c0-1 1-1 1-1l-4 12c0-1-1-2-1-3 1-3 1-6 3-9z" class="B"></path><path d="M160 332c0-7 1-12 5-18h0c-2 6-3 12-2 18 1 4 2 7 4 11h0l1 3h0c3 3 8 7 13 7 3 0 9-5 11-4l-2 1c0 1-1 1-2 1 0 1-2 2-3 3h1 1v1c-2 1-3 0-4 0v1h-1l-2 1c-3 0-8-1-10-4l-3-2-3-3c-3-4-4-7-5-12 0-2 0-2 1-4z" class="E"></path><path d="M185 354h1 1v1h-2v-1z" class="I"></path><path d="M163 332c1 4 2 7 4 11h0l1 3h0-1c-3-4-5-9-4-14z" class="b"></path><path d="M159 336c0-2 0-2 1-4 2 7 4 13 10 18 4 2 9 5 13 5v1h-1l-2 1c-3 0-8-1-10-4l-3-2-3-3c-3-4-4-7-5-12z"></path><path d="M164 426c2-2 3-4 5-5 2-4 4-8 4-12 0-5-3-9-6-12h1 0c2 0 3 1 4 2 2 3 2 6 2 9v3l1-1h1l-1 5c3-4 3-10 4-16 0 1 1 1 1 1v4l1 1 1 1h1l-1 1-1 3c1 0 1 0 2 1-3 7-6 12-13 16l-2 1-1-1h1l2-2h-1c-1 1-2 1-3 2v-1h-2z" class="W"></path><path d="M180 404l1 1 1 1h1l-1 1-1 3c1 0 1 0 2 1-3 7-6 12-13 16l-2 1-1-1h1l2-2h-1c-1 1-2 1-3 2v-1c0-1 1-1 2-2 1-2 3-3 4-6h0 1v3c4-4 6-11 7-17z" class="m"></path><path d="M180 404l1 1 1 1h1l-1 1-1 3c-2 6-6 11-11 15l3-4c4-4 6-11 7-17z" class="P"></path><path d="M206 373c3 1 6 2 9 4l1-2 5 3h0l-1 1h0c1 1 1 0 1 1h-1c0 1 1 1 2 2v1l2 2h0c3 3 8 5 9 9-2-1-4-4-6-5l2 2 3 3c-1 1 0 1-1 1v1h0c-4-2-6-4-9-7-3-2-5-5-7-8 0 2 2 4 3 5l6 8c2 4 4 6 5 10l-3-3-3-6 3 12h-1l-1-1c0-2-1-3-1-4v-4l-1-1v-1c0-4-5-9-7-12s-6-7-9-8c-1-1-2-2-3-2h-2l1-1h2 2z" class="R"></path><path d="M216 375l5 3h0l-1 1h0c1 1 1 0 1 1h-1l-5-3 1-2z" class="f"></path><path d="M229 391l-1 1s1 1 1 2h0c-1-1-5-4-6-5h0v-1l-2-1c-1-2-2-2-3-4l-1-1v-1c1 1 1 1 2 1l6 5c0 1 1 1 2 2l2 2z" class="G"></path><path d="M284 455c9 1 18 4 26-2 3-2 2-4 3-7 1-2 1-6-1-8-1-2-1-4-3-5l1-1c1 1 3 4 3 5 2 5 2 10 0 15-2 3-5 5-8 5-2 1-5 1-8 2h1-1v1c-2 0-3 0-4-1l-1 1c-2 0-4-1-6 0h0v1h0l-6 1c-1 0-2 1-3 0h-1v-1h1-5c-1 0-1-1-2-1l-5 2v-1h-5 0c-1-1-2-1-3-1l2-2h2v-1l4 1c3-1 6-1 9-1 2 0 4 1 5 0h-2v-1h6l1-1z" class="C"></path><path d="M261 457l4 1h10c6 0 12 0 18 1l-1 1c-2 0-4-1-6 0h0v1h0l-6 1c-1 0-2 1-3 0h-1v-1h1-5c-1 0-1-1-2-1l-5 2v-1h-5 0c-1-1-2-1-3-1l2-2h2v-1z" class="Y"></path><path d="M259 458h2c2 1 5 1 7 2l-8 1h0c-1-1-2-1-3-1l2-2z" class="H"></path><path d="M261 457l4 1h10c-2 1-5 2-7 2-2-1-5-1-7-2v-1z" class="X"></path><path d="M270 460c6-1 11 0 16 0h0v1h0l-6 1c-1 0-2 1-3 0h-1v-1h1-5c-1 0-1-1-2-1z" class="Z"></path><path d="M282 184c1-1 2-2 3-2 1-1 1-1 2 0-3 1-5 3-5 6v3c-1 1-1 2-1 3s0 1 1 2 3 2 3 3l2 1c4 2 9 1 13 0 6-2 12-8 14-13h1c0 1 0 1 1 1-4 8-8 15-17 18-3 1-6 1-9 0s-6-4-8-6-3-5-4-7c1-2 0-4 1-6 0-1 1-2 2-3v1l1 1v-2z" class="B"></path><path d="M282 200c1 0 3 1 4 2 2 1 4 2 6 2v1h-1c-1 0-1 0-1 1-3-1-6-4-8-6z" class="K"></path><path d="M314 187h1c-3 6-8 12-14 14-4 1-9 3-13 1l-3-3 2 1c4 2 9 1 13 0 6-2 12-8 14-13z" class="Y"></path><path d="M196 458c8-11 13-26 11-40-1-2-2-4-2-7-2-5-4-9-7-14-1-2-3-5-5-7-1-1-2-2-2-4l1-1c2 1 3 3 4 4 3 3 5 6 7 10 3 5 5 11 5 17v10 6c-1 1-1 3-1 5l1-1c0-1 0-1 1-2v-2h1c0 1 0 2-1 3h0l1 1c-1 3-3 7-4 11-1 2 0 5-2 7h-1c0-2 1-2 1-4v-1h-1c-1 2-3 7-5 8-1 1-1 1-2 1z" class="C"></path><path d="M207 437l1-1c0-1 0-1 1-2v-2h1c0 1 0 2-1 3h0l1 1c-1 3-3 7-4 11-1 2 0 5-2 7h-1c0-2 1-2 1-4v-1h-1c-1 2-3 7-5 8l3-7c1 0 1-1 1-1l2-2c1-4 2-8 3-10z" class="O"></path><path d="M263 429l1-1c1 1 2 2 2 3l1 2v1 3 1h1c-1 1-1 2-1 2v1l1-1v1 1l4-1c3-1 5-1 7-1 1 0 1 1 2 2v1c-1 2-5 5-7 6h-1-1c-3 1-6 1-9 1l-2-1c-4 0-8-2-11-3 1-1 2 0 3 0l7-2c1 0 3 0 4-1s1-1 1-3l-3 3-1-1 3-3v-1c1-1 1-3 1-5h0c0-1-1-3-2-4z" class="C"></path><path d="M272 441c3-1 5-1 7-1 1 0 1 1 2 2h-1c-1 0-2 0-3 1l-2 2c-2 0-5 0-6-1 1-1 3 0 5 0l1-1v-2h-3z" class="B"></path><path d="M250 446c1-1 2 0 3 0 2 0 4 1 6 1h3c3 0 6-1 8 0v1h1v1h0 2-1c-3 1-6 1-9 1l-2-1c-4 0-8-2-11-3z" class="H"></path><path d="M270 448h1v1h0 2-1c-3 1-6 1-9 1l-2-1h4c1 0 1 0 1-1h0 4z" class="B"></path><path d="M236 623h-1c3-3 6-5 9-6 9-3 22 2 30 6h-1c1 1 2 2 3 2 2 0 2 0 3 1l1 1h-1l-2-1c1 1 1 1 1 2h-1c-3-1-5-3-8-4-8-5-18-7-26-2l-12 6c-5 3-11 6-16 8-3 1-6 2-9 2-14 4-31 4-45-1l-9-3c0-1 0-1-1-1-2-1-3-2-5-3v-1l12 6c13 5 27 5 40 3 9-1 17-3 25-6 5-2 9-5 13-8 3-2 6-4 10-5s9-1 13 0c0-1-1-1-1-1-3-1-9-1-12 0h-1c-3 1-6 3-9 5z" class="C"></path><path d="M277 626c-3 0-7-3-10-5 1-1 5 2 6 2 1 1 2 2 3 2 2 0 2 0 3 1l1 1h-1l-2-1z" class="c"></path><path d="M297 424c1 1 2 1 3 1h0v-1c2-6 13-11 10-19-1-3-4-4-6-6-2-1-4-1-5-1-2 0-3 1-4 1 1-2 2-2 3-2 3-1 7 0 10 2s6 6 6 10c1 5-1 11-3 15-2 2-6 3-8 3-4 0-6 0-9-2l4 3v1c-5-2-7-4-9-9h0v-2c1 3 3 5 5 7h0c0-1-1-1 0-2l2 1h1 0z" class="E"></path><path d="M311 403v1c1 1 2 2 2 3v4c0 4-1 10-4 13-2 1-4 1-6 0h-2c0-1 2-2 2-3 3-4 8-8 9-12 0-2 0-4-1-6z"></path><path d="M457 328c5-1 9-1 13 0 5 1 11 4 16 7v2l3 3c0 1 1 3 2 3h0v1c-1 0-1 0-1 1l1 4-2-2c1 3 1 5 1 7-1 2-1 5-1 7v-3l-1-1v-6c-1-3-1-5-3-8v1h-1l-2-2h0c-1-1-2-2-3-2s-1-1-2-2h0v-1c-1-1-2-1-2-2h0l1-1c-2-1-5-2-7-2-4-1-9-1-13 0-1 0-2 0-3 1l-2-1 7-2c-1-1-2-1-3-1 1 0 1-1 2-1z" class="E"></path><path d="M458 330c8-1 16 0 22 5 2 1 4 3 5 5h0l-9-6c-2-1-5-2-7-2-4-1-9-1-13 0-1 0-2 0-3 1l-2-1 7-2z"></path><path d="M476 334l9 6h0c2 1 3 3 3 5 1 0 1 2 1 2 1 3 1 5 1 7-1 2-1 5-1 7v-3l-1-1v-6c-1-3-1-5-3-8v1h-1l-2-2h0c-1-1-2-2-3-2s-1-1-2-2h0v-1c-1-1-2-1-2-2h0l1-1z" class="I"></path><path d="M485 340c2 1 3 3 3 5 1 0 1 2 1 2 1 3 1 5 1 7-1 2-1 5-1 7v-3l-1-1v-6h1c0-2 0-4-1-6h0c-1-2-2-3-3-5h0z" class="b"></path><path d="M475 335c2 1 3 2 5 3s5 3 5 5v1h-1l-2-2h0c-1-1-2-2-3-2s-1-1-2-2h0v-1c-1-1-2-1-2-2h0z" class="G"></path><path d="M220 302v6c1 3 2 7 1 10h0c-2 8-4 14-11 18s-15 5-23 3h-1c0-1 0-1-1-1l2-1c2-1 5 0 8-1 2-1 5-2 7-3 5 0 8-2 11-5 6-7 5-16 4-24 2-1 2-1 3-2z" class="C"></path><path d="M220 308c1 3 2 7 1 10h0c-2-2-2-5-1-7v-3z" class="B"></path><path d="M61 563l-3-9c-3-10-2-20 3-29h0l-3 8h2v1c-1 2-1 4-1 6v5l3 15c3 7 10 17 17 20 1 0 1 0 2 1 2 0 4 1 5 2 3 1 5 1 8 2h0 6v1h-7 0 6 2c1 0 1 1 1 1h1c1-1 2 0 3 1-2 1-5 1-7 1v-1h-1c-4 0-8-1-12-2s-10-4-13-7c-5-4-9-10-12-16z" class="X"></path><path d="M59 545h0c-1 6 2 11 2 16-4-8-6-20-3-28h2v1c-1 2-1 4-1 6v5z" class="j"></path><path d="M98 588h1l1-1h-6l-6-1c-4-1-6-3-10-4-2-1-4-3-6-5h0c2 1 5 3 7 4 2 0 4 2 6 3s5 1 8 2h0 6 2c1 0 1 1 1 1h1c1-1 2 0 3 1-2 1-5 1-7 1v-1h-1z" class="F"></path><path d="M174 584h1c-1 1-2 1-3 1-1 1-3 1-4 2h1c3-1 5-1 8-2 2 0 4-1 6-2h5v1c-2 0-3 0-4 1h2 1v1c-2 0-3 0-4 1h-1c-1 0-2 0-2 1-1 1-3 2-4 2h0c-2 1-4 3-6 3v1c-3 1-5 3-8 5 0 1-1 2-2 3-2 4 0 11 2 15 1 2 2 3 2 5 0 0-1-2-2-3-4-6-4-11-3-18v-4s-3-1-3-2c-1-1 0-1 0-2 4-5 12-8 18-9z" class="I"></path><path d="M162 594v1c-1 1-1 1-1 2v-1c-1-1-1-1-1-2h2z" class="V"></path><path d="M162 595h1c0 1-1 2-1 3h-1v-1c0-1 0-1 1-2z" class="W"></path><path d="M160 594v1h-1c-1 0-2 0-2-1 1-1 2-3 3-3 1 1 0 1 0 2 1 0 2 0 3-1h0 1c-1 1-1 1-2 1v1h-2z" class="H"></path><path d="M162 599h0c1-2 2-3 3-5 0 0 2-2 3-2l1-1c1 0 1 0 2-1 1 0 1 0 1 1 1-1 2-1 2-1h2 0c-2 1-4 3-6 3v1c-3 1-5 3-8 5z" class="U"></path><path d="M103 494c0-1-1-2 0-3h0 1c3 4 7 7 11 10h-2-2c0 3 0 6-2 9v1c2-2 3-4 4-6v-1 1c-1 4-4 11-8 13l-1 1h-1c-1-1-2-3-4-4l-1 1h0c1 3 1 6 1 9h-1v-4h-2c0-2 0-4 1-6h-2c0-2 0-1 1-3-2-1-2-2-3-3v-1h1c1 1 1 2 1 2 2 2 3 2 5 2l2-2c-2 1-3 1-4 1s-2-1-3-2v-1h1c1 1 3 2 5 1s4-2 5-4l1-2c-1-1-1-1-1-2 0-2-2-5-3-7z" class="T"></path><defs><linearGradient id="AI" x1="109.217" y1="499.952" x2="108.522" y2="494.309" xlink:href="#B"><stop offset="0" stop-color="#151519"></stop><stop offset="1" stop-color="#2c2d2a"></stop></linearGradient></defs><path fill="url(#AI)" d="M103 494c0-1-1-2 0-3h0 1c3 4 7 7 11 10h-2-2c0 3 0 6-2 9v1h0c-1 0-1 1-2 1-1 2 1-1 0 1l-4 3h-1v-1c-1 0-2-1-3-1l1-1c1-1 3-2 4-2 2-2 4-4 5-6v-5l-1-1c-1-1-2-2-2-3l-1-1h0c0 2 2 4 3 6 0 2-1 3-1 4v-1h0v-1c-1-1-1-1-1-2 0-2-2-5-3-7z"></path><path d="M100 513h3c3-1 7-6 7-9v-3h1c0 3 0 6-2 9v1h0c-1 0-1 1-2 1-1 2 1-1 0 1l-4 3h-1v-1c-1 0-2-1-3-1l1-1zm11 24v-1l2 3v2 1l2 1-2 2c1 1 2 1 2 1-2 5-3 8-2 13 2 3 4 5 6 8h1c2 1 5 2 8 2h0l7 2h-4 0c2 1 4 1 5 2h0-7-2c-4-1-10-3-13-6-2-2-3-4-5-6l-1-1-1 1v-2c-2-6-2-10 1-14 1-2 3-3 3-4 1-2 0-3 0-4z" class="I"></path><path d="M113 542l2 1-2 2-1 2v-1c-1 1-2 2-3 4h0c0-1 1-2 1-3 1-2 2-3 3-5h0z" class="Q"></path><path d="M113 545c1 1 2 1 2 1-2 5-3 8-2 13l-1 1c0-1 0-1-1-2-1-4-1-7 1-11l1-2z" class="d"></path><path d="M108 560c-1-4-2-9 0-13 0 0 0-1 1-1-1 3-3 5-2 9 1 1 1 3 2 4 0 2 2 3 3 4 1 2 3 3 4 4h0v-1c-2-2-3-4-4-6l1-1c2 3 4 5 6 8h1c2 1 5 2 8 2h0l7 2h-4 0c2 1 4 1 5 2h0-7-2c-4-1-10-3-13-6-2-2-3-4-5-6l-1-1z"></path><path d="M120 567c2 1 5 2 8 2h0l7 2h-4-1c-3 0-8-2-11-4h1z" class="c"></path><path d="M244 628c8-3 18-1 26 1 3 1 7 1 10 3h-2-1 0c-2 1-6 0-8 0h-1v-1l-3 1h0c-2-1-4 0-7 0 0-1-1 0-2-1h-2l-1-1h-4 0c-1 0-2 0-3 1h-1c-5 1-12 3-16 7-1 0-2 1-3 1-1 1-2 1-2 1-1 0-1 1-1 1-1 0-1 0-2 1h-1-1l-1 1h0-2c-1 0-1 1-2 1h0c-1 0-2 1-2 1-2 0-1 1-2 1h-3-1l1-1c2 0 5-1 8-2 0-1 0-1 1-1 0 0 1 0 1-1h0-1 0v-1h2v-1h1c1 0 1-1 1-2 2-1 8-5 11-4l1-1c4-1 8-3 12-4z" class="N"></path><path d="M248 629h2 6c2 1 4 0 6 0-1 0-2 1-4 1h0l1 1-1 1c0-1-1 0-2-1h-2l-1-1h-4 0 0l-1-1z" class="R"></path><path d="M262 629h4 1c0 1 1 1 1 1h2v1h-2l-3 1h0c-2-1-4 0-7 0l1-1-1-1h0c2 0 3-1 4-1z" class="C"></path><path d="M262 629h4c-1 1-1 1-2 1h-1c-1 1-2 0-4 1l-1-1h0c2 0 3-1 4-1z" class="J"></path><path d="M244 628c8-3 18-1 26 1h-2-1c0-1 0-1-1-1h0-8c-2 0-4-1-6 0h-5l-1 1h0-2v-1z" class="R"></path><path d="M232 632c4-1 8-3 12-4v1c-1 0-2 0-3 1h-1l-8 4h0 1 0 1c1 0 1-1 2-1l3-1c1-1 1-1 2-1h2c0-1 2-1 2-1 1 0 1 0 2-1h1l1 1h0c-1 0-2 0-3 1h-1c-5 1-12 3-16 7-1 0-2 1-3 1-1 1-2 1-2 1-1 0-1 1-1 1-1 0-1 0-2 1h-1-1l-1 1h0-2c-1 0-1 1-2 1h0c-1 0-2 1-2 1-2 0-1 1-2 1h-3-1l1-1c2 0 5-1 8-2 0-1 0-1 1-1 0 0 1 0 1-1h0-1 0v-1h2v-1h1c1 0 1-1 1-2 2-1 8-5 11-4l1-1z" class="F"></path><path d="M217 211c2 0 2 0 4 1v-1c3 1 5 3 7 5 1 0 1 0 1-1 1 2 0 4 0 6h0c-1 0-1 1-2 1l2 4 1 2v3c1 1 1 3 1 5v1c-1 0-1 1-1 1 0 2 0 2-1 2v3c0 1 0 2-1 4v-6c0-2-1-5-2-7h0l-1 1c-1-2-1-4-2-6l-3-3 1 1 1-1c0-1-1-2-2-2v-1-1-1c-3-3-7-6-11-8 1 0 2 0 3-1l1-1 1 1h1c0-1 1-1 2-1z" class="M"></path><path d="M215 212c0-1 1-1 2-1 3 2 5 5 8 7 0 2 2 3 2 4l2 4 1 2h-1l-3-6-6-6c0-1-1-1-2-1 1 0 1 0 1 1h0l-5-3v-1h1z" class="G"></path><path d="M217 211c2 0 2 0 4 1v-1c3 1 5 3 7 5 1 0 1 0 1-1 1 2 0 4 0 6h0c-1 0-1 1-2 1 0-1-2-2-2-4-3-2-5-5-8-7z" class="X"></path><path d="M225 218v-2l1 1 2 2 1 2c-1 0-1 1-2 1 0-1-2-2-2-4z" class="P"></path><path d="M226 234v-1l-1-2c0-1 0-1-1-1 0-1 0-2-1-2v-1-1-1c-1 0-1 0-1-1h-1v-1h2 0v-2h-1l1-1c2 2 3 4 4 6 1 1 2 2 2 3s0 1 1 1v1c1 1 1 3 1 5v1c-1 0-1 1-1 1 0 2 0 2-1 2v3c0 1 0 2-1 4v-6c0-2-1-5-2-7z" class="S"></path><path d="M265 228c0 1 1 1 0 1v2s1-1 2-1c-1 2-3 4-4 5l1 1h0 1c0 1 0 2-1 3l-2 4c-1 0-2 1-3 1v1c-2-1-4-1-5-1 1 0 3 0 4-1v-3-1h0-1c-1 0-1 0-1-1h-2c-1 1-2 1-3 1-1 1-1 1-2 1l-1-1h-2c-3 0-3 2-6 4v-1c-2 0-1 1-2 1-1 1-2 3-3 4 0-2 1-3 1-4s0-1 1-1c0-1 1-1 1-2v-1c1-2 6-4 9-4l-1-1c-1 0-2-1-3-1-2-1-4-1-6-2h3 0-1l1-1 3 1v-1l-1-1h3 1 1c2 0 3 1 5 1 2 1 4 1 5 1 2-1 5-1 6-1l2-2z" class="D"></path><path d="M251 236s1 1 2 1v1h-4l2-2z" class="B"></path><path d="M257 231c2-1 5-1 6-1 0 0-2 1-3 2h-4c-2 0-5 1-7 1-1-1-1-1-2-1h5 0c2 0 4-1 5-1z" class="C"></path><path d="M246 234c3-2 8-1 10-1-1 1-2 1-3 1s-1 1-2 1v1l-2 2h-2c0 1-1 0-1 0h0s1 0 1-1h0c-1 0-2 0-3 1h-2 1c2-1 5-2 6-3v-1c-1 0-1 0-2 1l-1-1z" class="O"></path><path d="M246 229h1c2 0 3 1 5 1 2 1 4 1 5 1-1 0-3 1-5 1h0-5c-1 0-2 0-4-1v-1l-1-1h3 1z" class="J"></path><path d="M265 231s1-1 2-1c-1 2-3 4-4 5l-1 1-2 2c-1 0-1 0-2-1l-2-1h-2c1-1 3-2 5-2 2-1 4-1 6-3z" class="M"></path><path d="M256 236c2-1 4-1 6 0l-2 2c-1 0-1 0-2-1l-2-1z" class="Z"></path><path d="M263 235l1 1h0 1c0 1 0 2-1 3l-2 4c-1 0-2 1-3 1 0-1-1-3 0-4l1-2 2-2 1-1z" class="E"></path><path d="M262 238v1l-1 2h-1 0c0-1 1-2 2-3z" class="J"></path><path d="M163 509c2-2 3-4 5-5h1c-1 1-3 2-3 3s1 2 1 2c0 2 0 4 1 6h0c1-3-1-4 1-7h1c0 1-1 3-1 4 1-1 1-1 1-2 1-1 2-2 3-4 3-1 4-3 8-4-1 2-3 5-5 7v-1c0-1 2-3 2-4-1 0-2 2-3 3-6 11-8 22-7 34l1 6h-1v1c0 3 2 6 2 8h0-1 0c-3-4-4-11-5-16 0-3-1-5-1-8l1-1v-3c-1-2 0-4-1-5v-6-6l1-1-1-1z" class="E"></path><path d="M168 548l-1-3v-4h1l1 6h-1v1z" class="V"></path><path d="M208 213h1c4 2 8 5 11 8v1 1 1c1 0 2 1 2 2l-1 1-1-1 3 3c1 2 1 4 2 6l1-1h0c1 2 2 5 2 7v6c1 2 0 4 1 6 0 1 0 3-1 4h0l-1 2-1 1h0c-5 11-10 21-20 26v-1-1c8-4 12-11 16-19 1-2 2-4 3-7 1-2 1-5 1-7 0-10-2-19-9-26 1 1 2 2 3 4h-1c-3-3-4-6-8-9h0c-2-1-7-4-9-3h-4l1 1v2c-1 0-1 1-1 1-2 2-6 2-9 2-2 0-3 1-5 1v-1c2 0 4-1 6-1 2-1 5-1 7-2-1-1-2 0-3 0v-1h1c1-1 1-1 1-2l1-1h1l3-1h2c1 0 1-1 1-1h1c1 0 1-1 1-1h2z" class="E"></path><path d="M225 235l1-1h0c1 2 2 5 2 7v6c1 2 0 4 1 6 0 1 0 3-1 4h0l-1 2-1 1h0c-1 0 0-2 0-3 1-2 1-4 1-6 1-6 0-11-2-16z" class="d"></path><path d="M306 517h0c0 2-1 3 0 5v6c0 2 1 4 2 6 2 6 6 11 9 17v2c-1 1-3 0-4 0h-1 0v-1c-1 0-2-1-2-1-1 0-2 0-3-1-3-2-4-4-5-8l-2-1c-2-6-1-11 1-17h0c0-1 1-3 1-4l2-2h0v1l1 1 1-3z"></path><path d="M309 547c1 1 3 1 4 2-2 1-2 1-4 0l-2-1 2-1z" class="H"></path><path d="M313 549l1 1c-1 1 0 1-1 2h-1c-1 0-2-1-2-1-1 0-2 0-3-1 0 0 1 0 2-1 2 1 2 1 4 0z" class="T"></path><path d="M304 540c1 3 2 5 5 7l-2 1c-2-2-3-4-4-7l1-1z" class="C"></path><path d="M302 542l1-1c1 3 2 5 4 7l2 1c-1 1-2 1-2 1-3-2-4-4-5-8z" class="U"></path><path d="M302 520l2-2h0v1l1 1c-2 4-1 8-2 12v3s0-1-1-1v-9l-1-1h0c0-1 1-3 1-4z" class="H"></path><path d="M301 524l1 1v9c1 0 1 1 1 1l1 5-1 1-1 1-2-1c-2-6-1-11 1-17z" class="F"></path><path d="M129 419c4 1 9 1 13-1 4-1 9-4 11-8-4 3-8 7-13 7-5 1-11 1-14-2-2-1-4-3-4-5 1 2 2 3 4 4 3 2 9 3 13 3 7-2 12-6 16-12 5-8 4-20 2-29v-2c1 3 2 5 2 8 1 5 1 9 1 14h0 0v-1c1-4 2-11 1-16 0-1 0-3-1-4 0-2-3-7-2-8h0c2 2 2 5 3 7v1c1 1 0 2 1 3 4 9 0 22-3 31h0c-2 4-6 9-9 12-2 0-2 0-3 1l-1-1c1 0 1-1 2-1h1c0-1 1-1 1-3-6 5-11 4-18 4 0-1 0-1-1-1s-1-1-2-1z" class="B"></path><path d="M132 421c2-1 5-1 7-1s4-1 6-2c3-2 8-4 9-8h-1c3-3 4-6 5-9v-1c1 1 0 2 0 3v1c-3 5-4 9-8 13-6 5-11 4-18 4z" class="M"></path><path d="M158 404v1c0 1-2 4-2 5h1l1-1h1c-2 4-6 9-9 12-2 0-2 0-3 1l-1-1c1 0 1-1 2-1h1c0-1 1-1 1-3 4-4 5-8 8-13z" class="C"></path><path d="M545 336c4-4 9-7 15-7 5 0 12 2 15 5 3 2 4 6 3 9v4c-2 2-5 4-7 5s-6 0-8-1h-1l-1-1c-1-1-2-2-2-4l3 2 2 2c1-1 1-1 2-3 1 0 1-1 2-1 1-2 1-3 1-5l-3-6c-2-2-5-3-8-3-4 0-7 1-10 4l-1-1c-1 1-1 1-2 1z"></path><path d="M569 341c1 2 1 5-1 7 0 1-1 2-2 2h-2c1-1 1-1 2-3 1 0 1-1 2-1 1-2 1-3 1-5h0z" class="I"></path><path d="M547 335c4-3 7-5 12-4 3 0 6 2 8 4 1 2 2 4 2 6h0l-3-6c-2-2-5-3-8-3-4 0-7 1-10 4l-1-1z" class="U"></path><path d="M305 476v-1c0-1 1-1 1-1 2 5 3 9 3 14 0 1 1 2 1 2 0 1 0 3-1 4l-4 6c0 1 0 1-1 2s-1 2-2 2v-1h-1-1l-5 3s-3 0-3 1c-3 0-6 1-9 0 0 0-2-1-2-2-2 0-2-1-3-2h0c1-1 2-1 3-2 0-1 0-1 1-2l7-1c0 1 0 1 1 1v1l4-1h1c4-2 6-4 8-7 3-6 3-10 2-16z"></path><path d="M304 498c3-3 4-6 5-10 0 1 1 2 1 2 0 1 0 3-1 4l-4 6c-1-1-1-2-1-2z" class="D"></path><path d="M304 498s0 1 1 2c0 1 0 1-1 2s-1 2-2 2v-1h-1-1l-5 3c-1-1-1-1-1-2l2-1h-3l1-1c3-1 7-2 10-4z" class="C"></path><path d="M296 503c2 0 3-1 5-1h0l-1 1-5 3c-1-1-1-1-1-2l2-1z" class="F"></path><path d="M290 500l4-1h1l-6 3h5l-1 1h3l-2 1c0 1 0 1 1 2 0 0-3 0-3 1-3 0-6 1-9 0 0 0-2-1-2-2-2 0-2-1-3-2h0c1-1 2-1 3-2 0-1 0-1 1-2l7-1c0 1 0 1 1 1v1z" class="N"></path><path d="M288 503h0c1 1 2 1 3 1l1 1h-1-1-4c-1-1-1 0-2 0h-2v-1l2-1h4z" class="C"></path><path d="M278 503h6l-2 1v1h2v1h4c-2 0-4 0-5 1 0 0-2-1-2-2-2 0-2-1-3-2h0z" class="B"></path><path d="M288 506h2 0c1 0 1 0 2-1h1l1-1c0 1 0 1 1 2 0 0-3 0-3 1-3 0-6 1-9 0 1-1 3-1 5-1z" class="C"></path><path d="M290 500l4-1h1l-6 3h5l-1 1c-2-1-3-1-5 0h-4-6c1-1 2-1 3-2 0-1 0-1 1-2l7-1c0 1 0 1 1 1v1z" class="L"></path><path d="M282 499l7-1c0 1 0 1 1 1v1l-9 1c0-1 0-1 1-2z" class="l"></path><path d="M253 545c2-2 5-3 8-4-2 2-4 2-7 3 4 2 9 5 12 8 1 1 2 4 3 6 0 1 0 1-1 2l-1-2h0c0 1 0 1 1 2l1 1c0 4 0 6 1 10 0 3 2 6 1 9v5c0 4 1 8 3 12h0c3 8 7 16 14 21v2c-5-5-9-10-12-16-4-8-6-15-9-23l-3-8c-1-4-2-8-4-11-1-3-2-6-4-8-1-1-4-3-4-4v-1l6 6c3 4 5 10 7 15l2 9c1 1 1 2 2 3 0 1 0 2 1 3v-1c0-1 0-2-1-4 0 0-1 0-1-1 0-2-1-4-1-6l-3-11c0-1-1-3-1-3-2-3-4-5-6-7-1-1-3-2-4-4-1 0-1-1-1-1 0-1 0-1 1-2z" class="I"></path><path d="M252 547c0-1 0-1 1-2 0 1 0 1 1 1 3 1 8 4 10 6s2 3 3 5v1h-1 0l-2-3h0c-1-1-2-3-3-4-2-2-6-4-9-4z" class="d"></path><path d="M264 555h0l2 3s0 1 1 1v5c1 3 4 14 3 15-1 0-1-2-1-3l-4-15c0-2-1-4-1-6z"></path><path d="M476 541c0 1 0 1 1 2 0 0 0-2 1-2v7c0 3-1 6-1 9l-1 2 1 2c1 3 4 7 6 9 1 0 1 1 1 2 0 0-1 1-2 1-2-3-8-2-11-4-2-1-4-1-4-3h-1-1c-1 1-3 3-4 2v-1l-1-1c1-2 1-2 2-3l2 2 1-1-1-1s1 0 1-1h0c0-1 1-1 1-1 0-1 0-2 1-3h0l-1-1h0c1-1 1-1 2-1h3c1-3 1-6 1-8-1-2-2-4-4-6h-1 2c0 1 1 2 2 2s0-1 0-1l1-1h0l2-1h2z" class="N"></path><path d="M472 567l3 1c-1 1-1 1-2 1l-3-1 1-1h1z" class="f"></path><path d="M476 568c1 0 2 1 3 2-1 0-5-1-6-1 1 0 1 0 2-1h1z" class="M"></path><path d="M474 565c1 1 1 2 2 2v1h-1l-3-1-1-1h0l2-1h1z" class="P"></path><path d="M466 557h0c1-1 1-1 2-1 0 2-1 4-1 6 0-1 2-4 3-5 0 1 0 2-1 3s-1 2-2 3c-1 2-2 3-3 3-1 1-2 1-3 1h0l-1-1c1-2 1-2 2-3l2 2 1-1-1-1s1 0 1-1h0c0-1 1-1 1-1 0-1 0-2 1-3h0l-1-1z" class="b"></path><path d="M468 565c0-1 2-2 2-3 1-2 2-3 3-5 1 4 1 7 4 10h-1c-1 0-1-1-2-2h-1l-2 1h0l1 1h-1l-1 1-2-2v-1z" class="D"></path><path d="M468 565c2-1 4-3 5-5 0 2 0 3 1 5h-1l-2 1h0l1 1h-1l-1 1-2-2v-1z" class="M"></path><path d="M473 557c1-2-1-7 0-9 1 1 1 2 1 3h1c1 1 1 2 1 2v3l1 1-1 2 1 2-1 1 3 6c-1 0-1-1-2-1-3-3-3-6-4-10z" class="O"></path><path d="M474 551h1c1 1 1 2 1 2v3l1 1-1 2 1 2-1 1h0c-2-4-2-6-2-10v-1z" class="D"></path><path d="M476 553v3l1 1-1 2v-1l-1-2c0-1 0-2 1-3z" class="K"></path><path d="M474 551h1c1 1 1 2 1 2-1 1-1 2-1 3h-1v-4-1z" class="B"></path><path d="M221 620c-3 6-7 12-14 15-6 2-11 2-17 2-8 0-16 0-23-4s-13-9-16-17c-1-5-1-9 1-14v-1c0 2-1 4-1 7 0 5 2 11 5 15 5 7 14 11 22 12s19 1 26-1c0-1-1-1-1-1h-3c-6 1-14 0-19-2h0v-1c2 0 4 1 5 1 3 0 6 0 9-1 3 0 6-1 8-1 10-4 16-13 25-19 1 0 3-1 5-2l1 1c-3 1-6 3-7 6-2 1-5 3-6 4v1z" class="F"></path><path d="M212 629c2-2 4-4 6-7 1-2 2-4 4-6-3 5-5 11-10 15v-2z" class="f"></path><path d="M203 633c3-1 6-3 9-4v2c-2 1-5 3-8 3 0-1-1-1-1-1z" class="d"></path><path d="M203 629c1 1 1 1 2 1 1-1 3-2 4-2 1-1 3-3 5-4 1-1 2-3 3-4 0 3-3 6-5 7h-1c-1 1-3 2-5 3-1 1-2 1-3 1-2 1-5 0-7 1v1h4c-6 1-14 0-19-2h0v-1c2 0 4 1 5 1 3 0 6 0 9-1 3 0 6-1 8-1z" class="G"></path><path d="M286 461l8 3v-1c3 1 5 2 7 3 3 3 4 5 5 8 0 0-1 0-1 1v1c1 6 1 10-2 16-2 3-4 5-8 7h-1l-4 1v-1c-1 0-1 0-1-1l-7 1h-1-4 0c2-1 5-1 7-2 2 0 6 0 8-1v-1-1-1h0 0c2-1 5-2 6-4 2-3 4-7 4-11l-1 1v-1c0-2 0-4-1-5h0c-1-2-1-2-1-4l-2-1c-4-3-7-5-12-5-4 0-10 1-13 5-2 1-3 3-4 5s-1 9 0 11c2 5 5 7 9 9l3 1h0l-1 1-5-2c-3-2-6-6-7-10h0c-1-2-1-4-1-7 1-5 3-8 6-11h1c1-1 3-2 5-2l2-1 6-1z" class="J"></path><path d="M299 469c3 3 5 6 5 10 0 1-1 1-1 1h-1c0-2 1-4 0-6-1-1-1-1-2-1h0c-1-2-1-2-1-4z" class="X"></path><path d="M294 464v-1c3 1 5 2 7 3 3 3 4 5 5 8 0 0-1 0-1 1v1c-2-5-6-10-11-12z" class="d"></path><path d="M300 473c1 0 1 0 2 1 1 2 0 4 0 6h1s1 0 1-1l1 4c0 2-2 6-3 8-2 4-5 6-8 8l-4 1v-1c-1 0-1 0-1-1l-7 1h-1-4 0c2-1 5-1 7-2 2 0 6 0 8-1v-1-1-1h0 0c2-1 5-2 6-4 2-3 4-7 4-11l-1 1v-1c0-2 0-4-1-5z" class="T"></path><path d="M301 490l1 1c-2 4-5 6-8 8l-4 1v-1c-1 0-1 0-1-1 5-2 9-4 12-8z" class="S"></path><defs><linearGradient id="AJ" x1="299.109" y1="491.588" x2="296.403" y2="484.104" xlink:href="#B"><stop offset="0" stop-color="#232123"></stop><stop offset="1" stop-color="#343837"></stop></linearGradient></defs><path fill="url(#AJ)" d="M300 473c1 0 1 0 2 1 1 2 0 4 0 6h1s1 0 1-1l1 4c0 2-2 6-3 8l-1-1c1-2 3-4 3-6v-1c-1 2-2 3-2 4-2 4-6 7-10 9v-1-1-1h0 0c2-1 5-2 6-4 2-3 4-7 4-11l-1 1v-1c0-2 0-4-1-5z"></path><path d="M220 467h1v1l1 1v2c0 1 1 1 1 2v1c-1 1-1 2-1 3h-1s-1 1-1 2v-2c-3 7-7 13-13 18-5 4-10 6-15 9-6 4-13 10-16 16-1 4-2 7-3 11v-2h-1c0-4 0-7 2-10 3-9 12-17 20-21h0l2-2c5-1 9-4 12-7 1-1 1-2 2-3 0-1 1-1 2-2 2-4 5-8 7-12l-1-2 2-3h0z" class="F"></path><path d="M220 467h1v1c0 1-1 3-1 4h-1l-1-2 2-3h0z" class="O"></path><path d="M222 471c0 1 1 1 1 2v1c-1 1-1 2-1 3h-1s-1 1-1 2v-2l2-6z" class="b"></path><path d="M179 511h0c3-3 6-6 9-8h1c-1 1-3 2-4 4v1l-4 4-2-1z" class="P"></path><path d="M179 511l2 1c-2 3-5 6-6 9-1 2-1 4-2 6 0-5 3-12 6-16z" class="f"></path><path d="M189 503c3-2 6-3 9-3l-13 8v-1c1-2 3-3 4-4z" class="X"></path><path d="M219 472h1c-2 4-5 8-7 12-1 1-2 2-2 3 3-2 4-6 7-9-4 10-11 17-20 22-3 0-6 1-9 3h-1c2-2 4-3 6-4l2-1h-2 0l2-2c5-1 9-4 12-7 1-1 1-2 2-3 0-1 1-1 2-2 2-4 5-8 7-12z" class="d"></path><path d="M64 547c0 1 1 1 1 2 0 2 1 4 2 6v1s0 1 1 1c0 2 1 3 2 5 1 3 3 5 5 7l1 1c1 1 3 2 4 3 1 0 1 2 2 2 3 0 3 1 5 2h3 2 0c3 0 7 0 10-1 1 0 1-1 2-2 1 1 0 1 0 2h3v1c0 1 1 1 1 2h0l-1 1-1-1c0 1-1 2-2 2v-1c-1 0-1 1-2 1v1h-1l-1 1h-1c-2 0-6 0-8-1-1 0-2-1-3-1s-1 0-2-1l-1 1 9 3v1c-3-1-5-1-8-2-1-1-3-2-5-2-1-1-1-1-2-1-7-3-14-13-17-20h1 1c0 1 1 1 2 2l1 3c2 3 4 6 6 8h1l-1-2-2-1-1-2-1-1c-1-2-2-4-3-5-1-2-2-5-2-6v-1-2h0v-2-1-3z" class="i"></path><path d="M85 581h0c-3-1-6-2-9-4l5 2c2 0 3 1 5 1h4c1 0 2 1 3 2 2 1 7 0 9-1v1h-1l-1 1h-1c-2 0-6 0-8-1-1 0-2-1-3-1s-1 0-2-1l-1 1z" class="V"></path><path d="M64 547c0 1 1 1 1 2 0 2 1 4 2 6v1s0 1 1 1c0 2 1 3 2 5 1 3 3 5 5 7l1 1c1 1 3 2 4 3 1 0 1 2 2 2 3 0 3 1 5 2h3 2 0c3 0 7 0 10-1 1 0 1-1 2-2 1 1 0 1 0 2h3v1c0 1 1 1 1 2h0l-1 1-1-1c0 1-1 2-2 2v-1c-1 0-1 1-2 1-2 1-7 2-9 1-1-1-2-2-3-2h-4c-2-1-3-1-4-2-5-2-7-6-10-9 2 1 4 4 5 5l3 2v-1c0-2-3-3-5-5-1-2-4-3-5-5v-1c-1-2-2-3-3-5-1-3-2-5-3-8v-1-3z" class="F"></path><path d="M86 580c-2-1-3-1-4-2-5-2-7-6-10-9 2 1 4 4 5 5l3 2c3 2 7 3 10 3 1 1 2 1 3 2 2 0 7 0 10-2h0c-1 0-2 0-3 1-1 0-6 0-7-1h0 0c3 0 7 0 9-2v-1c1 0 1-1 2-2 1 1 0 1 0 2h3v1c0 1 1 1 1 2h0l-1 1-1-1c0 1-1 2-2 2v-1c-1 0-1 1-2 1-2 1-7 2-9 1-1-1-2-2-3-2h-4z" class="P"></path><path d="M469 421h0l1-1h0l2-1h1l-3 3c1 1 1 2 2 3h3c1-1 2-3 4-4v1l2-1h-1l-2-1v-1c4 1 7 2 9 5 2 1 2 4 2 6 0 1-1 2-1 3-1 0-2 1-3 0h0-1v1h-1c0 2-1 2-2 3h0c1 1 3 1 3 1v1h-1v2h0c-1 0-1 0-1 1h-2c-3 0-6 0-8-2-4-3-3-9-4-13 0-2 0-3 1-5h0l-1-1h1z" class="N"></path><path d="M481 434h2c0 2-1 2-2 3h0l-1 1-1-1h-1v-1c2 0 2-1 3-2z" class="M"></path><path d="M478 425c1-1 2-2 3-2h1 1v-1h-1 0c2 0 4 1 5 3 1 1 1 4 1 5-1 2-2 2-3 3v-4h0v-1-1c-1-1-1-2-2-2h-3-2 0zm2 13h-2c-2 1-4 0-5-1h-1v1l-1-1c-1-3-2-6-1-9 1 0 2-2 4-2 2-1 3-3 5-3h0c-1 1-3 3-4 3s-2 0-2 1c-1 1-2 3-2 4 0 2 1 3 2 4 2 2 3 2 4 2h1 1l1 1z"></path><path d="M478 425h0 2 3c1 0 1 1 2 2v1 1h0v4h0-1v1h-1-2c0-2 0-3-1-4l1-1-4-3 1-1z" class="M"></path><path d="M481 429s1 1 1 2 1 2 2 2v1h-1-2c0-2 0-3-1-4l1-1z" class="P"></path><path d="M477 426l4 3-1 1c-1-1-1-2-3-2l-1 1 1 1 1-1 1 1c1 1 1 2 1 3s-1 2-2 2c0 0-2 1-3 0-1 0-2-1-2-2-1-1-1-2 0-3 0-2 2-3 4-4zm-3 84c-3-1-5-3-6-5s-1-6 0-9c0-2 2-3 4-4 3-2 6 0 10 1l3 2v-1c3 3 5 5 6 9-1 4-1 8-3 12 0 2-2 4-2 7h0c-1 3 0 5-1 7v1c-2-3-1-7 0-10 0-2 3-5 3-7v-3c-1-1-2-3-4-4-2 0-7 4-9 5l-1-1z"></path><path d="M472 501c1 1 2 1 3 1l-1 1c0 1-1 1-2 2v-4z" class="Q"></path><path d="M482 493l3 2c2 3 4 5 4 10 0-1 0-2-1-2-2-3-4-6-7-8h-1v-1l2-1z" class="L"></path><path d="M472 501c1-1 2-4 4-5h0 1c-1 1-2 2-2 3 2-2 4-3 6-3l1 1c-1 0-2 1-3 1-2 1-3 2-4 4-1 0-2 0-3-1z" class="J"></path><path d="M485 494c3 3 5 5 6 9-1 4-1 8-3 12 0 2-2 4-2 7h0c-1 3 0 5-1 7v1c-2-3-1-7 0-10 0-2 3-5 3-7v-3c-1-1-2-3-4-4-2 0-7 4-9 5l-1-1h0v-1c-1-1-1-3-2-4 1-1 2-1 2-2h1c1-1 4-4 6-4h0 0l-1 1c0 1 1 1 1 1 3 1 6 3 8 5v1h-1 0c-1-1-3-3-4-3-4 0-7 3-9 5l-1 1 6-3c0-1 2-2 2-2h2c2 1 3 2 4 3h1 0v-3c0-5-2-7-4-10v-1z" class="D"></path><path d="M169 372v-1l2 2 1-1c2 1 5 1 6 3 4 2 7 7 9 11 3 7 3 15 1 22-2 4-3 7-5 10v-1c-3 4-7 8-11 10h-2c7-4 10-9 13-16-1-1-1-1-2-1l1-3 1-1h-1l-1-1-1-1v-4-6c-1-8-5-17-11-22z" class="I"></path><path d="M185 405v-1h1c0 2-2 6-3 9-1 1-2 3-2 4h0c2-2 3-3 4-5 0-2 1-4 2-6v1c-1 2-1 4-2 6-1 1-1 3-2 4-3 4-7 8-11 10h-2c7-4 10-9 13-16-1-1-1-1-2-1l1-3h2l1-2z" class="D"></path><path d="M182 407h2l-1 4c-1-1-1-1-2-1l1-3z" class="U"></path><path d="M169 372v-1l2 2c6 3 11 8 13 14s1 11-1 16c1 0 2 1 2 2l-1 2h-2l1-1h-1l-1-1-1-1v-4-6c-1-8-5-17-11-22z"></path><path d="M183 403c1 0 2 1 2 2l-1 2h-2l1-1v-3z" class="T"></path><path d="M436 599c0-2-1-3-1-4 0-4 1-7 3-10 3-4 7-6 11-6h4c0 1 5 3 5 3 2 0 4 1 6 2 1-1 0-1 0-1l1-1 2 1h1c1 0 3 2 4 3 3 4 4 7 5 12h0v1 1h-1v-1c0 1-1 1-1 2l-1 9-1-1c0 1-1 3-1 3h-1l-1 1h0c-1 0-2 2-2 3h-1v-2c0 1-1 1-1 1 0-2 0-3 1-4v-2h-1c0-4 0-8-1-12-1-6-5-11-10-14-3-2-7-2-11-1-3 1-5 3-7 6-1 3-1 6 0 9v2h-1z" class="V"></path><path d="M467 597l-3-5c0-1-1-2-2-4h0l-1-1h1c2 1 5 7 5 10 1 1 1 2 1 4 0 0 0 1 1 1v2h1c-1 2-1 4 0 5v3h0v1c-1 0-2 2-2 3h-1v-2c0 1-1 1-1 1 0-2 0-3 1-4v-2h-1c0-4 0-8-1-12h2z" class="D"></path><path d="M465 597h2v6c1 2 0 6 0 8v-2h-1c0-4 0-8-1-12z" class="B"></path><path d="M458 582c2 0 4 1 6 2h0c2 1 3 1 4 2l1 1 1 2c1 1 2 3 3 4l1 1-1 2h0-1c-1 3-1 6-2 8h-1v-2c-1 0-1-1-1-1 0-2 0-3-1-4h1v-2c-2-5-5-10-10-13z" class="D"></path><path d="M464 584c2 1 3 1 4 2l1 1 1 2-1-1s-1-1-2-1c0 1 1 2 2 2l-1 1h0c-2-2-3-3-4-6z" class="O"></path><path d="M468 597l1 1v3h1c0-2 1-4 2-6 0-1-1-2-1-3h1c0 1 0 1 1 1l1 1-1 2h0-1c-1 3-1 6-2 8h-1v-2c-1 0-1-1-1-1 0-2 0-3-1-4h1z" class="G"></path><path d="M468 583c1 0 3 2 4 3 3 4 4 7 5 12h0v1 1h-1v-1c0 1-1 1-1 2l-1 9-1-1c0 1-1 3-1 3h-1l-1 1h0v-1h0v-3c-1-1-1-3 0-5s1-5 2-8h1 0l1-2-1-1c-1-1-2-3-3-4l-1-2-1-1c-1-1-2-1-4-2h0c1-1 0-1 0-1l1-1 2 1h1z" class="H"></path><path d="M468 583c1 0 3 2 4 3l-1 1 1 1c0 1 1 1 1 2-1-1-2-2-4-3l-1-1c-1-1-2-1-4-2h0c1-1 0-1 0-1l1-1 2 1h1z" class="C"></path><path d="M470 604c1-2 1-5 2-8h1c0 2-1 5-1 7h1c0-2 0-5 2-6 0 1 0 1-1 2-1 2-1 7-1 10 0 1-1 3-1 3h-1l-1 1h0v-1h0v-3c-1-1-1-3 0-5z" class="L"></path><path d="M470 612c1-2 0-5 1-6h0c1 2 1 4 0 6l-1 1h0v-1z" class="J"></path><defs><linearGradient id="AK" x1="126.277" y1="487.567" x2="130.487" y2="453.956" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2b2a2b"></stop></linearGradient></defs><path fill="url(#AK)" d="M101 450v1c1 0 1 0 1 1v1l2 3v2c1 2 2 4 4 6l2 4 2 2c1 0 0 0 0 1 1 1 3 2 3 3 1 1 3 2 3 3l4 2c3 2 6 4 10 5l2 1h0 2c1 1 1 1 2 1h0l6 1c1 0 3 0 4 1h2 9c-1 1-2 1-3 2-3 1-6 1-10 1h-1v1h2-4c-10 1-19-3-28-9 0-1-2-2-3-3v-1c1 0 4 4 6 4-2-1-3-4-5-5l-1 1-2-2-5-9c-1-1-2-2-2-3s-1-2-1-2v-3c-1-2-2-5-2-8h0v-1l1-1v1-1z"></path><path d="M102 460l5 9c1 1 2 3 2 4v1c1 2 2 3 4 4l-1 1-2-2-5-9c-1-1-2-2-2-3s-1-2-1-2v-3z" class="O"></path><path d="M113 478c-2-1-3-2-4-4v-1c6 7 13 12 22 15l12 3h3-1v1h2-4c-10 1-19-3-28-9 0-1-2-2-3-3v-1c1 0 4 4 6 4-2-1-3-4-5-5z" class="V"></path><path d="M143 491h3-1v1h2-4c-1 0-2 0-3-1h0 3z" class="U"></path><defs><linearGradient id="AL" x1="452.974" y1="573.359" x2="457.933" y2="597.716" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2b2c2d"></stop></linearGradient></defs><path fill="url(#AL)" d="M451 572c3 0 5 1 7 1h1l4 1h1 0 2 1c1 1 1 2 2 2h1c0 1 1 1 2 1h1c2 0 4 0 6 2 0 1 1 2 1 3l1 1 1 1c-1 0-1 1-1 2v3l-1 1v7l-1-1v-1l-1 1v-1-1h-1c0 1 0 2 1 2v1l-1 1c-1-5-2-8-5-12-1-1-3-3-4-3h-1l-2-1-1 1s1 0 0 1c-2-1-4-2-6-2 0 0-5-2-5-3h-4c-4 0-8 2-11 6-2 3-3 6-3 10 0 1 1 2 1 4 1 1 2 2 2 3h0c-3-2-3-5-4-8 0-3 0-7 1-10 2-4 7-8 12-10h2c1-1 2-1 3-1l-1-1z"></path><path d="M451 572c3 0 5 1 7 1l-2 1h-7c1-1 2-1 3-1l-1-1z" class="C"></path><path d="M459 573l4 1h1c1 1 3 2 3 3-3-1-7-2-11-3l2-1h1z" class="B"></path><path d="M449 579c6-2 13 0 18 3l1 1h-1l-2-1-1 1s1 0 0 1c-2-1-4-2-6-2 0 0-5-2-5-3h-4z" class="E"></path><path d="M466 574h1c1 1 1 2 2 2h1c0 1 1 1 2 1h1c2 0 4 0 6 2 0 1 1 2 1 3l1 1 1 1c-1 0-1 1-1 2v3l-1 1v7l-1-1v-1c-1-6-5-12-10-17-1 0-1 0-2-1 0-1-2-2-3-3h0 2z" class="C"></path><path d="M479 589c0-1 0-2-1-3l2-2c0 1 0 4-1 5z" class="N"></path><path d="M480 582l1 1 1 1c-1 0-1 1-1 2v3l-1 1-1-1c1-1 1-4 1-5v-2z" class="G"></path><path d="M466 574h1c1 1 1 2 2 2h1c0 1 1 1 2 1h1c1 2 3 3 4 6h0v-1c-2-1-3-3-5-4h0v1c2 2 4 4 4 6h0c-2-2-4-5-7-7h0c-1 0-1 0-2-1 0-1-2-2-3-3h0 2z" class="L"></path><path d="M90 457v-4h1v1l1 9h2l1 2 1-1c2 2 2 5 4 7h0 1l1 3c1 2 2 3 3 5 1 1 7 6 6 7v1l-1 1c2 1 4 3 6 5 2 1 7 4 8 7v1c1 0 11-2 13-2 4 1 8 2 11 4 3 1 6 3 8 6h-1c-3-4-7-6-12-7-7-2-14-1-21 0v1h-3c-1 0-3-2-4-2-4-3-8-6-11-10-2-2-4-5-5-8-4-8-8-17-9-26z" class="T"></path><path d="M110 494l3 2-2 1-2-3h1z" class="S"></path><path d="M106 484l3 4c1 2 1 3 3 4v1c-2-1-4-3-6-5l1-1s0-1-1-2v-1z" class="O"></path><path d="M112 492c3 1 5 3 8 5 1 0 2 1 2 2h1c-2-1-5-3-7-3-1 0-3-2-4-3v-1z" class="B"></path><path d="M113 496c1 0 2 1 3 2v1c1 1 3 2 5 2l1 1v1c-4-2-7-3-11-6l2-1z" class="O"></path><path d="M101 478c1 1 2 3 3 4 0 0 2 1 2 2v1c1 1 1 2 1 2l-1 1c-2-2-4-5-6-8l1-2z" class="K"></path><path d="M109 494c-6-4-10-11-12-18 2 3 3 6 5 8 2 4 5 7 8 10h-1z" class="X"></path><path d="M109 488h1c0-2-2-2-2-3h0c1 0 2 1 3 2l-1 1c2 1 4 3 6 5 2 1 7 4 8 7h0l-1-1h-1c0-1-1-2-2-2-3-2-5-4-8-5-2-1-2-2-3-4z" class="F"></path><path d="M100 471h1l1 3c1 2 2 3 3 5 1 1 7 6 6 7v1c-1-1-2-2-3-2h0c0 1 2 1 2 3h-1l-3-4c0-1-2-2-2-2-1-1-2-3-3-4v-1h1c0-2-1-4-2-6z" class="e"></path><path d="M101 477h1c1 1 4 4 4 5-1 0-1 0-2-1v1c-1-1-2-3-3-4v-1z" class="I"></path><path d="M94 463l1 2 1-1c2 2 2 5 4 7h0c1 2 2 4 2 6h-1v1l-1 2c-1-3-3-6-5-9-1-2-2-5-3-8h2z" class="X"></path><path d="M96 464c2 2 2 5 4 7h0c1 2 2 4 2 6h-1l-6-12 1-1z" class="g"></path><path d="M336 432l-3-13h0c3 7 4 13 5 20v5c0 4 1 10-1 15v2c1 1 1 3 1 4-1 2-1 4-1 6-1 2-1 4-2 5 0 2-1 4-2 6 1 1 1 1 1 2h-1v1c0 2-1 4-2 5 0-2 2-3 2-5v-3h0c-1 1-2 3-2 5-1 3-3 6-5 8 0 2-1 3-1 5-1 1-2 2-2 4h0c0 2-2 3-3 5v-3-2c1-1 2-4 3-5 2-5 3-10 4-15v-1-2 1c1-1 0-1 1-1 1-3 1-6 2-9 1-4 2-8 2-12 0-5-1-10 1-15l-2-10c0-3-2-6 0-9v1l3 9c0-1 0-1 1-2v-2h1z" class="k"></path><path d="M335 432h1l1 26h-1v-13c-1-2-1-5-2-7v-2c0-1 0-1 1-2v-2z" class="i"></path><path d="M331 426v1c0 2 1 6 1 8 2 7 4 15 3 22v2l-1 1v-2-3c-1-2 0-6-1-8v-1-1l-2-10c0-3-2-6 0-9z" class="a"></path><path d="M333 445v1 1c1 2 0 6 1 8v3 2l1-1v-2c0 1 1 3 0 4-1 2 0 4-1 6l-3 14v1c0 4-2 6-3 9-2 3-3 5-4 8h-1c2-5 3-10 4-15v-1-2 1c1-1 0-1 1-1 1-3 1-6 2-9 1-4 2-8 2-12 0-5-1-10 1-15z" class="W"></path><path d="M328 481v1c1-1 1-2 1-3 1-1 1-3 1-5 1-3 2-6 2-9h1 0v1 4l-2 4v4 1c-1 0-1 1-1 2h1v1c0 4-2 6-3 9-2 3-3 5-4 8h-1c2-5 3-10 4-15v-1-2 1c1-1 0-1 1-1z" class="Q"></path><path d="M502 131c-1-5-1-9 2-14 2-3 5-4 8-5s6 0 8 1c2 2 5 5 5 8 1 2 0 5-2 7-1 3-3 4-6 4l-4 2c-2 0-4 1-5 1l-3-2v-1l-2-2v1h-1z"></path><path d="M505 132c2 0 3 1 5 1s4-1 6-1h1l-4 2c-2 0-4 1-5 1l-3-2v-1z" class="Z"></path><path d="M502 131c-1-5-1-9 2-14 2-3 5-4 8-5s6 0 8 1c2 2 5 5 5 8 1 2 0 5-2 7-1 3-3 4-6 4h-1v-1h1c3-1 5-1 6-4 1-2 2-5 1-8-1-2-3-4-5-5s-5-2-7-1c-5 1-6 5-8 9 3-1 6-2 9-1 2 0 5 5 6 6-1 1-1 2-2 2-1 1-1 1-2 0v-3s1 0 1-1h-1v-2c-2-1-2-1-4-1s-5 0-6 1c-2 2-2 5-2 7v1h-1z" class="E"></path><path d="M170 595v1l-1 1c-2 3-4 6-5 10h0c-1 3 0 7 1 10 1 4 3 8 7 10 2 1 4 1 6 2h0c2 0 4 0 6-1 6-1 13-2 19-5l-1-1c-1-2 0-4 0-5h0c0-1 1-2 2-2 1-1 1-1 2 0 2-1 3-3 5-4v-1h3c1-1 2-1 3-2v-1c2-3 9-2 12-3h-4 2c1-1 2-1 2-2h3v-1l1-1 7 3 2-1 5 2c3 1 5 2 8 3l-1 1v1c-5-1-9-2-14-1l-5 1h-1l-1-1c-2 1-4 2-5 2-2 0-4 1-5 2-5 2-10 7-14 10-2 1-4 1-6 2-6 2-12 4-19 5-2 1-4 1-6 1h-1c-2 0-4-1-6-2-3-3-7-7-8-12v-3c-1-7 2-13 7-18z" class="Q"></path><path d="M242 602l5 2 8 3-1 1c-3-2-7-3-11-4-1 0-2 0-3-1l2-1z" class="Y"></path><path d="M223 607c1 1 1 1 3 1h1c-3 1-5 3-7 4-5 3-8 7-13 10h0c1-2 3-4 5-5 2-2 5-3 6-5h0l3-3h-1v-1l3-1z" class="X"></path><path d="M233 600l7 3c1 1 2 1 3 1-3 1-6 1-10 0h-4-4 2c1-1 2-1 2-2h3v-1l1-1z" class="b"></path><path d="M220 609h1l-3 3c-3 1-6 3-8 5-3 2-5 4-7 6l-1-1h0c2-3 5-5 7-7 3-3 7-5 11-6z" class="Y"></path><path d="M229 604h4v1l1 1c-1 1-5 2-7 2h0-1c-2 0-2 0-3-1l-3 1v1c-4 1-8 3-11 6-2 2-5 4-7 7h0c-1-2 0-4 0-5h0c0-1 1-2 2-2 1-1 1-1 2 0 2-1 3-3 5-4v-1h3c1-1 2-1 3-2v-1c2-3 9-2 12-3z" class="C"></path><path d="M233 605l1 1c-1 1-5 2-7 2h0-1c-2 0-2 0-3-1 3-1 7-1 10-2z" class="c"></path><path d="M217 608l2-1 1 1c-4 2-9 3-12 6l1 1c-2 2-5 4-7 7h0c-1-2 0-4 0-5h0c0-1 1-2 2-2 1-1 1-1 2 0 2-1 3-3 5-4v-1h3c1-1 2-1 3-2z" class="D"></path><path d="M127 595h0l-3-5c5 4 6 11 8 17 1 4 3 9 6 13 2 3 5 6 8 9v1c2 1 3 2 5 3 1 0 1 0 1 1h0-1c0-1 1 0 0-1h-2l-1-1-1 1c1 1 3 1 4 2h0l1 1c3 1 7 3 10 4 8 2 16 2 24 2h5c-3 1-6 1-10 1h-3c-1 0-1 0-1 1h4-3l1 1h4c-2 2-5 0-7 1h-9-1c-1-1-3 0-5-1v1h0l-5-1c-3-1-16-7-17-10-1-2-3-4-5-6l-1-1c-1-3-1-5-1-8 0-1-1-2-1-3s1-2 1-3l-1-1c0-2 0-4-1-7 0-3-2-7-3-11z" class="K"></path><path d="M132 614c1 3 2 6 4 9 0 2 1 2 1 3 2 2 7 9 11 10 0 0 1 0 2 1l1 1c1 0 2 1 3 1 0 0 1 0 1 1h0c2 2 4 1 6 3h0c-4 0-8-3-12-4-1-1-3-1-4-2l-4-4-5-5h0c0 2 2 4 3 4 1 1 1 1 2 1l-1 1c-2-1-4-3-5-5h-1l-1-1c-1-3-1-5-1-8 0-1-1-2-1-3s1-2 1-3z" class="X"></path><path d="M127 595h0l-3-5c5 4 6 11 8 17 1 4 3 9 6 13 2 3 5 6 8 9v1c2 1 3 2 5 3 1 0 1 0 1 1h0-1c0-1 1 0 0-1h-2l-1-1-1 1c1 1 3 1 4 2h0l1 1c-2-1-5-2-7-4h0l3 3c2 1 4 2 7 3 1 1 5 2 6 3-2 0-4-1-6-1h0c0-1-1-1-1-1-1 0-2-1-3-1l-1-1c-1-1-2-1-2-1-4-1-9-8-11-10 0-1-1-1-1-3-2-3-3-6-4-9l-1-1c0-2 0-4-1-7 0-3-2-7-3-11z" class="G"></path><path d="M136 623c2 3 4 5 6 7-2-4-5-7-7-12-1-1-1-2-2-3h1c1 2 2 5 4 7 2 3 5 5 8 8 2 1 3 2 5 3 1 0 1 0 1 1h0-1c0-1 1 0 0-1h-2l-1-1-1 1c1 1 3 1 4 2h0l1 1c-2-1-5-2-7-4h0l3 3c2 1 4 2 7 3 1 1 5 2 6 3-2 0-4-1-6-1h0c0-1-1-1-1-1-1 0-2-1-3-1l-1-1c-1-1-2-1-2-1-4-1-9-8-11-10 0-1-1-1-1-3z" class="P"></path><path d="M486 117c3 3 6 6 8 9l8 11c2 3 5 5 7 8h0l6 6c1 0 2 1 2 1l-1 1c2 2 3 5 4 7 1 1 1 2 2 3 2 0 2 2 2 3v1c1 1 1 3 2 4-1 1 0 3-1 4-1 8-4 18-10 23 0-2 2-5 4-7 2-4 3-8 2-13l-1 1v1l-1 1v-1l-1-1v-5 1l-1 2c0-9-3-15-8-22-6-6-10-14-14-22-1-2-2-4-3-5v1c0 1-1 1-1 1l-6-9 1-1h1 0c0-1-1-1-1-2v-1z" class="Z"></path><path d="M521 169h1v7h-1v-7z" class="G"></path><path d="M521 169l-1-3h0c-1-1 0-1-1-2v-2 1 1l1-1h0c1 2 1 4 2 6h-1z" class="N"></path><path d="M519 180c2-3-1-8 1-11h0v1c0 3 0 6 1 8h0l-1 1v1l-1 1v-1z" class="K"></path><path d="M524 167v3h-1c0-1 0-2-1-3 0-1-1-3-2-5 0-1 0-1-1-2-1-3-2-5-4-7h0l1-1-2-2v1c-1 0-3-2-4-3 0-1-1-2-1-2v-1l6 6c1 0 2 1 2 1l-1 1c2 2 3 5 4 7 1 1 1 2 2 3 2 0 2 2 2 3v1z" class="D"></path><path d="M495 133h1s0-1-1-1l-2-5c1 1 2 1 2 2 1 2 3 4 3 6 0 1 1 2 1 2 1 3 2 5 4 7 0 0 0 1 1 1 0 1 0 1 1 2 1 0 1 1 2 2h-1v1h0c1 2 2 3 3 5-6-6-10-14-14-22z" class="L"></path><path d="M199 516l1 1c-8 2-13 7-17 14-3 6-4 13-1 20 5 16 21 21 34 29 4 3 7 5 10 8 2 2 5 3 6 5s4 3 6 4l2 2 4 1 4 3c-1 0 0 0-1 1l-5-2c-9-4-16-9-25-11l1-1c3 1 5 3 8 3-3-2-7-3-11-5-11-5-21-10-30-19-4-3-8-7-10-12-4-8-2-20 1-29v1c-2 8-3 20 0 27l2 5h1s1 0 1-1l1-1v1c1 0 1 0 2 1s2 2 3 4h1c0 2 2 3 3 4 1 0 1 1 2 2h1c1 1 3 2 4 3l6 3c1 0 1 1 1 1h2c3 3 8 4 11 6h0c1 0 3 1 4 2 1 2 3 3 5 4-3-4-7-6-10-8l-21-12c-7-5-12-12-15-20-2-5-1-11 1-16 4-9 9-15 18-18z" class="T"></path><defs><linearGradient id="AM" x1="184.481" y1="558.744" x2="190.955" y2="577.196" xlink:href="#B"><stop offset="0" stop-color="#060605"></stop><stop offset="1" stop-color="#393b3d"></stop></linearGradient></defs><path fill="url(#AM)" d="M178 561h1s1 0 1-1l1-1v1c1 0 1 0 2 1s2 2 3 4h1c0 2 2 3 3 4 1 0 1 1 2 2h1c1 1 3 2 4 3l6 3c1 0 1 1 1 1h2c3 3 8 4 11 6h0 0l4 3h0l-1 1c-1-1-2-2-4-2-3-1-6-3-8-4s-3-1-4-2h0c0 1 1 1 1 1 2 1 4 2 5 3 3 1 5 2 7 3h0c1 0 2 1 2 1h2c1 0 2 1 3 2h0-2-1l-1-1-7-3c-13-5-26-14-35-25z"></path><path d="M98 426l1 1v5h0c0 3 0 6 1 8l1 4h0v3l1 2v1 1l1 2 1 2c1 3 3 6 5 9l3 4 3 3v1l2 2 1 1h0c0 1 0 1 1 1s1 1 2 1c0 1 1 1 2 2 3 1 7 4 10 4l1 1h1 2l6 2h3c2 1 5 0 8 1 3 0 6 0 9-1h0l-1-1 8-2c1 1 3 1 4 1l1-1c3-1 5-1 7-2 4-2 9-4 12-7s6-7 8-10c6-9 9-21 11-31 1 8 1 17-1 25l-1-1c-3 10-9 19-18 24-11 7-24 8-37 9 1-1 2-1 3-2h-9-2c-1-1-3-1-4-1l-6-1h0c-1 0-1 0-2-1h-2 0l-2-1c-4-1-7-3-10-5l-4-2c0-1-2-2-3-3 0-1-2-2-3-3 0-1 1-1 0-1l-2-2-2-4c-2-2-3-4-4-6v-2l-2-3v-1c0-1 0-1-1-1v-1-1l-1-1c0-1 0-3-1-4v-2c-1-5-1-11-1-16z" class="L"></path><path d="M175 483l1 1 4-1c2-1 5-2 7-3 2-2 4-3 6-4 6-4 11-12 13-18 1-2 2-5 3-6 0 3-2 9-4 12-2 4-6 9-10 12-1 1-2 1-3 2l-1 1-2 1c-2 1-5 2-7 3l-1 1c-1 0-2 0-3 1-2 0-4 1-7 1-1 0 0 0-1 1-1 0-2-1-3 0h-4c-2 0-2-1-3 0h-6c3 0 6 0 9-1h0l-1-1 8-2c1 1 3 1 4 1l1-1z" class="P"></path><path d="M159 488c8 0 15-1 22-3 4-1 7-4 10-6l3-1c8-6 12-13 15-21 0-1 2-4 1-5v-1-2h1v2c1 2 0 4 0 6-3 10-9 19-18 24-11 7-24 8-37 9 1-1 2-1 3-2z"></path><path d="M517 385v-9c-1-4-4-9-6-13 5 5 10 10 13 16l-1 1 4 11c0 5 0 10-2 15l-1 3-1 2v-1h-1c0 1-2 3-3 3-1 1-1 0-1 0-2 0-4-1-5-1-1-1-1-1-2-3 1 0 1 0 1-1h-3c-1 0-2 0-4-1l-1-1v-1-2h0c0-2-1-3 0-4h1 2c0-1 1-1 1-1l1 1c1-1 3-2 4-3 2-4 3-8 4-11z" class="E"></path><path d="M518 403c1-1 1-3 2-4 0 3 0 6-2 9h0l-2-1c1-1 1-2 2-3v-1z" class="D"></path><path d="M522 410v-1c-1-1 1-3 1-3 0-2 1-3 1-5v-3c1 1 1 2 1 3v-3 3c0 2 0 4-1 6 0 1-1 2-1 3h-1z" class="I"></path><path d="M523 380l4 11c0 5 0 10-2 15l-1 3-1 2v-1c0-1 1-2 1-3 1-2 1-4 1-6 1-3 1-7 0-9 0-2-1-5-1-7 0-1-1-3-1-5z" class="b"></path><path d="M520 387l-1-6c2 6 2 12 1 18-1 1-1 3-2 4-1 0-2 1-3 2h-1l-1-1c2-2 4-4 5-6 2-4 2-7 2-11z" class="P"></path><path d="M518 408v1c1 0 2-2 2-3 1-2 1-3 2-4 0 2-1 4-2 6-1 1-3 2-3 4l1 1c-2 0-4-1-5-1-1-1-1-1-2-3 1 0 1 0 1-1h-3l2-2h2 0c2 0 3-1 5-2-1 1-1 2-2 3l2 1z" class="R"></path><path d="M518 404c-1 1-1 2-2 3s-1 1-3 2h0-1c1-1 2-1 3-2-1 0-2 0-3 1h0-3l2-2h2 0c2 0 3-1 5-2z" class="L"></path><path d="M517 385l1-1c1 1 1 2 2 3 0 4 0 7-2 11-1 2-3 4-5 6l1 1h1c1-1 2-2 3-2v1c-2 1-3 2-5 2h0-2l-2 2c-1 0-2 0-4-1l-1-1v-1-2h0c0-2-1-3 0-4h1 2c0-1 1-1 1-1l1 1c1-1 3-2 4-3 2-4 3-8 4-11z" class="B"></path><path d="M504 403c2 2 2 2 5 2 1 0 3-2 4-1-1 1-4 3-5 2-2 0-3 0-4-1v-2z" class="I"></path><path d="M509 399h2l-1 1 1 1-2 2h0c-1 1-2 0-3-1l1-2 1-2 1 1z" class="U"></path><path d="M509 399h2l-1 1c0 1-1 1-1 1-1 0-1 0-1-1h-1l1-2 1 1z" class="I"></path><path d="M513 404l1 1h1c1-1 2-2 3-2v1c-2 1-3 2-5 2h0-2l-2 2c-1 0-2 0-4-1l-1-1v-1c1 1 2 1 4 1 1 1 4-1 5-2h0z" class="X"></path><path d="M517 385l1-1c1 1 1 2 2 3 0 4 0 7-2 11l-1-1v-1c1-1 1-2 1-3-2 4-3 8-7 10h-1v-1h1c1 0 2-1 2-2v-1c0 1-1 2-2 2l-1-1 1-1h-2c1-1 3-2 4-3 2-4 3-8 4-11z" class="U"></path><path d="M511 399c1 0 1 0 1-1h0 1 1 0v1h-1c0 1-1 2-2 2l-1-1 1-1z" class="V"></path><path d="M317 411c4 2 5 5 8 8 4 5 4 11 6 17v-1l2 10c-2 5-1 10-1 15 0 4-1 8-2 12-1 3-1 6-2 9-1 0 0 0-1 1v-1 2c-1 3-2 6-3 8l3-18c1-1 2-6 1-7-1 2-1 7-2 10l-2 10c-1 3-2 7-3 10l-1 4-1-1v-3h0-1v2l-1-3 1-2h-1c0-1-1-3 0-4 0-1 1-2 1-2h1 0v-1l1-3c1-2 1-3 1-4 0-4 1-7 2-10v-7c2-2 1-6 2-9l1-1h0v-1c1-1 1-1 1-2 0-4-1-8-2-12l-5-16-1-2h0c-1-2-2-3-2-5v-3z" class="X"></path><path d="M320 417l1 2c0 1 0 1-1 2l-1-2c1-1 1-1 1-2z" class="e"></path><path d="M317 414h1l2 3c0 1 0 1-1 2h0c-1-2-2-3-2-5z" class="h"></path><path d="M318 487h1 0v-1c0 1 1 3 0 4s-1 2-1 3h-1c0-1-1-3 0-4 0-1 1-2 1-2z" class="Q"></path><path d="M330 439c0 2 0 4 1 5l-1 6v4c-1 0-1 0-2-1v-2l2-6v-6z" class="T"></path><path d="M321 496h-1c1-3 0-6 1-8 1-1 1-2 2-3h0l1 1c-1 3-2 7-3 10z" class="C"></path><path d="M317 411c4 2 5 5 8 8v5 1c-2-4-3-8-6-11h-1-1v-3z" class="e"></path><path d="M327 434c2 4 3 10 1 15h-1c0-4-1-8-2-12h1c1-1 1-2 1-3z" class="T"></path><path d="M325 419c4 5 4 11 6 17v8c-1-1-1-3-1-5-1-2-1-4-2-6 0-3-2-6-3-8v-1-5z" class="a"></path><path d="M321 419l6 15c0 1 0 2-1 3h-1l-5-16c1-1 1-1 1-2z" class="i"></path><path d="M331 436v-1l2 10c-2 5-1 10-1 15 0 4-1 8-2 12-1 3-1 6-2 9-1 0 0 0-1 1v-1 2c-1 3-2 6-3 8l3-18c1-1 2-6 1-7-1 2-1 7-2 10l-2 10-1-1c1-3 1-5 1-7 1-3 2-7 2-10v-8c1 1 1 2 1 3s0 1 1 1v-2c-1-4 0-8 0-11v2c1 1 1 1 2 1v-4l1-6v-8z" class="V"></path><path d="M331 436v-1l2 10c-2 5-1 10-1 15 0 4-1 8-2 12-1 3-1 6-2 9-1 0 0 0-1 1v-1c1-6 3-11 3-17-1-2-1-6 0-8v7c2-2 1-10 0-13l1-6v-8z" class="f"></path><defs><linearGradient id="AN" x1="428.69" y1="603.477" x2="431.065" y2="571.126" xlink:href="#B"><stop offset="0" stop-color="#717177"></stop><stop offset="1" stop-color="#92918f"></stop></linearGradient></defs><path fill="url(#AN)" d="M442 572c1 0 3-1 4-1 2 0 4 0 5 1l1 1c-1 0-2 0-3 1h-2c-5 2-10 6-12 10-1 3-1 7-1 10h-1v3c0 1 3 4 3 5h-1c-1 1-2 1-3 2l-1-1h-1c0 2 2 3 2 4-1 0-2-1-3-1l-1-1h-2l-1 1h-1 0c0 1-1 1-2 2-1 0-1-1-2-1l-2-1v-2h-1v1c-1 0-1-1-2-2-1-6 3-14 7-19 4-6 12-11 19-12h1z"></path><path d="M419 595h0v-2h1v-2h1c1 1 1 5 1 6l-1 1v-4h-1v11l-1 1h-1v-2c1-2-1-6 0-9h1z" class="Q"></path><path d="M418 604c1-2-1-6 0-9h1v11h-1v-2z" class="I"></path><path d="M426 589v-2c1-1 1-1 1-2 1-2 4-5 6-7 0 2-1 3-2 5-1 1-1 3-2 4-1 3-1 5-1 8 0 2 1 5 0 7h-1c1 1 1 2 1 3h-2l-1 1h-1 0c0 1-1 1-2 2-1 0-1-1-2-1l-2-1h1l1-1v-11h1v4 1c1-1 1-4 1-6h1l-1-4 1-1v1h1 0c1 0 1 0 1-1l1 1z" class="H"></path><path d="M423 593l-1-4 1-1v1h1v10c0 1 0 3-1 4h0c0-2 1-8 0-10z" class="R"></path><path d="M426 589v-2c1-1 1-1 1-2 1-2 4-5 6-7 0 2-1 3-2 5-1 1-1 3-2 4-1 3-1 5-1 8 0 2 1 5 0 7h-1 0c-1-2 0-8 0-10s1-3 1-5h0c-1 1-1 2-1 3-1 2 0 6-1 8 0 1 0 1-1 2h0v-9h0c1-1 1-1 1-2z" class="D"></path><path d="M442 572c1 0 3-1 4-1 2 0 4 0 5 1l1 1c-1 0-2 0-3 1h-2c-5 2-10 6-12 10-1 3-1 7-1 10h-1v3c0 1 3 4 3 5h-1c-1 1-2 1-3 2l-1-1h-1c0 2 2 3 2 4-1 0-2-1-3-1l-1-1c0-1 0-2-1-3h1c1-2 0-5 0-7 0-3 0-5 1-8l1 1c1-2 1-5 3-7h0c1-2 2-3 4-4h0l-1 1h1c2-2 6-3 7-6h0-3 1z" class="H"></path><path d="M436 578h1c-3 4-5 9-6 13v2h-1v-1c0-4 3-11 6-14z" class="W"></path><path d="M430 588c1-2 1-5 3-7h0c1-2 2-3 4-4h0l-1 1c-3 3-6 10-6 14v1c0 1 1 2 1 4h2 0c0 1 3 4 3 5h-1c-1 1-2 1-3 2l-1-1h-1c0 2 2 3 2 4-1 0-2-1-3-1l-1-1c0-1 0-2-1-3h1c1-2 0-5 0-7 0-3 0-5 1-8l1 1z" class="C"></path><path d="M433 597h0c0 1 3 4 3 5l-2-2c0-1-1-1-1-2-1 0-1 0-1 1h0-1v-2h2z" class="G"></path><path d="M428 595c0-3 0-5 1-8l1 1c0 1-1 3-1 4 0 2 1 5 0 7h-1v-4z" class="E"></path><path d="M492 129v-1c1 1 2 3 3 5 4 8 8 16 14 22 5 7 8 13 8 22-1 3-3 5-6 6-1 1-2 1-3 0-2 0-4-1-5-2h-1c0-1-1-2-1-3-2-3-2-5-2-9l-1-17c0-3 0-6-1-10s-4-8-6-12c0 0 1 0 1-1z"></path><path d="M491 130s1 0 1-1c2 5 5 10 7 16 1 5 1 12 1 18 1 3 1 6 2 9 0 1 2 3 3 3s3 0 4-1l3-3h0c0 3 0 5-2 7-1 1-3 3-4 3s-2-1-3-1v1h-1c0-1-1-2-1-3-2-3-2-5-2-9l-1-17c0-3 0-6-1-10s-4-8-6-12z" class="B"></path><path d="M499 169c0 1 0 3 1 3 0 1 0 2 1 3 0 2 2 3 3 3 0 1-1 2-1 2v1h-1c0-1-1-2-1-3-2-3-2-5-2-9z" class="K"></path><path d="M55 565c0-1 0-2-1-3 0-1 0-3-1-3v-2c-1-2-1-5-1-7s1-4 0-6c0-2-1-6 0-8 2 2 0 9 1 11v3c0 1 1 2 1 3l1-2c1 11 8 23 17 30 1 1 3 2 4 3 4 1 7 3 10 5 7 2 13 2 20 0l1-1s2-2 3-2c0-1 0-3 1-5l-1-1v-1l3 3c1 4-1 5-2 8 0 1-1 2-2 2-1 1-3 2-4 3-2 0-3 0-5 1-1 1-2 1-3 1-5 0-9-1-13-2-5-1-10-3-14-6-6-4-11-10-14-17l-2-5 1-1v-1z" class="i"></path><path d="M55 565s0 1 1 2c0 0 0 1 1 2v2h-1v1l-2-5 1-1v-1z" class="e"></path><path d="M97 597h0c0-1-3 0-5-1h-2c-1-1-2-1-3-1h-1c-2-1-4-2-6-2l-6-3c-1-1-2-1-2-1l-1-1c-1 0-1 0-1-1h0c2 1 3 2 5 2 2 1 4 2 5 3h2l5 2 2 1c1-1 2-1 3-1h2 10c2 0 4-2 5-2-1 1-3 2-4 3-2 0-3 0-5 1-1 1-2 1-3 1z" class="W"></path><path d="M92 594h2 0 5v1h-8-2c1-1 2-1 3-1zm-4-2v-1c-1 0-1 0-2-1-1 0 0 1-1 0h-1c-1 0-2-1-3-1-1-1-3-2-4-3-1 0-1 0-2-1-1 0-2-1-4-1-2-1-5-5-6-6v-1l4 4 1 1c2 1 3 2 4 2s2 0 2 1c1 0 1 1 2 1s2 1 2 1c1 0 3 1 3 2h3c7 2 13 2 20 0h1c-1 1-2 1-4 2h-2c2 1 4 1 5 0l2-1h1c-1 2-3 2-5 3h-6c-4 0-7 0-10-1z" class="a"></path><path d="M106 589l1-1s2-2 3-2c0-1 0-3 1-5l-1-1v-1l3 3c1 4-1 5-2 8 0 1-1 2-2 2s-3 2-5 2H94h-2c-6-1-10-3-15-5-3-1-5-2-7-5 1 1 2 2 3 2h2c1 1 3 2 4 3l9 3c3 1 6 1 10 1h6c2-1 4-1 5-3h-1l-2 1c-1 1-3 1-5 0h2c2-1 3-1 4-2h-1z" class="b"></path><path d="M399 615c-5-4-10-8-16-9h-2c2-3 6-8 8-8 2-1 5 1 6 2 6 3 9 10 13 15-2-4-4-7-6-11-1-1-2-3-3-5l1-1h0c2 3 3 5 5 8v-1c-1-2-3-5-4-7v-1c1 1 2 2 3 4 2 3 3 6 4 9 4 7 11 13 19 15 6 2 10 3 16 3 2-1 5-1 7-2-1 2-1 2-2 3h-2-1c-3 1-7 2-11 3h-4c-1-1-2-1-3-1-2 0-4-1-6-2-8-3-16-7-22-14z" class="C"></path><path d="M397 608h0c-1-1-2-2-4-3l-6-4c1-1 1-2 3-2 9 2 11 11 17 16 0 0 1 0 1 1h-1l2 2h0-1c-1 0-5-4-5-5s-1-1-1-2h-1c0-1 1-1 0-1-1-1-2-2-2-3h-1l-1 1z" class="M"></path><path d="M388 604c-1 0-2-1-3-2h1 0l12 8 1-1-2-1 1-1h1c0 1 1 2 2 3 1 0 0 0 0 1h1c0 1 1 1 1 2s4 5 5 5h1 0l-2-2h1c1 1 3 2 4 3 4 3 10 5 14 7l2 1h-2c-8-1-14-4-20-9-3-2-5-5-7-7-4-3-8-5-11-7z"></path><path d="M399 615v-1c-1 0-1-2-2-2-3-2-6-4-9-5-1-1-2-1-3-2h0-2v-1l1-1 1 1h2 1c3 2 7 4 11 7 2 2 4 5 7 7 6 5 12 8 20 9h2l-2-1 1-1c6 2 10 3 16 3 2-1 5-1 7-2-1 2-1 2-2 3h-2-1c-3 1-7 2-11 3h-4c-1-1-2-1-3-1-2 0-4-1-6-2-8-3-16-7-22-14z" class="G"></path><path d="M427 625c6 2 10 3 16 3h-5c-1 0-2 1-3 1-3 0-7-1-9-2h2l-2-1 1-1z" class="X"></path><path d="M276 533h2c3-1 6-1 8 1h-1c3 2 4 3 5 6 1 2 1 5 1 7 0 6-2 7-5 11-2 2-3 4-5 6-2 1-4 2-5 4h1c-2 2-2 4-3 6-1 1-1 3-1 4 0 6 0 12 2 17 0 1 1 3 1 5h0 0c-1-1-1-1-1-2h0l-1-1h0c-2-4-3-8-3-12 1-2 0-3 1-5v-11c0-3 1-5 0-9l-1-7c-1-1-1-3-1-4v-1-1c0-1 1-1 1-2h1v1h1c1-3 3-5 5-7-1-1-1-1-1-2h0-1v-2c-1 0-1-1-1-2h1z"></path><path d="M274 557v-3c1-2 3-3 5-4 1 0 1 0 2 1 1 0 1 0 1 1h-1l-2 2h0v-1l1-1v-1h-1l-1 1c-1 1-1 2-2 3v1s-1 1-2 1z" class="F"></path><path d="M276 533h2c3-1 6-1 8 1h-1-8v1h0l1 1 1 2-1 1c-1-1-1-1-1-2h0-1v-2c-1 0-1-1-1-2h1z" class="V"></path><path d="M278 536c1 0 1-1 3-1h1 1l3 3c2 2 1 6 1 8l-4-4 1-1 2 2v-1c0-1 0-3-1-4s-2-1-3-2c-1 0-2 1-3 2l-1-2z" class="I"></path><path d="M279 538c1-1 2-2 3-2 1 1 2 1 3 2s1 3 1 4v1l-2-2c-3 0-5 0-8 2-1 0-2 3-3 3 1-3 3-5 5-7l1-1z"></path><path d="M273 546c1 0 2-3 3-3 3-2 5-2 8-2l-1 1c-3 0-5 1-8 3-2 2-2 3-2 6 0 1 0 3 1 4h0c-1 1-1 2-1 3v3h0l-1-1-1-7c-1-1-1-3-1-4v-1-1c0-1 1-1 1-2h1v1h1z" class="E"></path><path d="M279 554l1 1c0-1 1-1 2-1l-3 3c-1 1-2 2-2 3-2 4-5 8-4 12h1v-3c0-2 1-3 2-5 0-2 2-5 4-6 1-1 1-2 2-2 1-2 3-3 3-6 0-1-1-3-2-3l-2-2c-3 1-5 2-7 4h0c1-2 3-4 5-4 1-1 3-1 4 0s3 3 3 5-1 5-3 7l-6 6v1l8-6c-2 2-4 5-7 7h-1c0 1-1 2-1 3h0 0 1c-2 2-2 4-3 6-1 1-1 3-1 4 0 6 0 12 2 17 0 1 1 3 1 5h0 0c-1-1-1-1-1-2h0l-1-1h0c-2-4-3-8-3-12 1-2 0-3 1-5v-11c0-3 1-5 0-9l1 1h0v-3h1v-1c1 0 2-1 2-1v-1c1-1 1-2 2-3l1-1h1v1l-1 1v1z" class="B"></path><path d="M274 557c1 0 2-1 2-1s0 3-1 3v2c-1-1-1-2-1-3v-1z" class="D"></path><path d="M187 294c1 0 3 1 4 0l3 1c3 2 6 5 7 9l1 1c0 4-1 8-4 11s-7 4-11 4h-1 3c-1 1-3 1-4 1v1h-3l-1-1-1 1c-2-2-4-4-5-7-2-4-1-8 1-13l1-1-1-1c2-2 4-3 6-4 1 0 3-1 5-2z"></path><path d="M182 311h1v-2l1 2c0 1-1 1-1 2l-1-2z" class="J"></path><path d="M182 311v-3l3-3c1 0 2 0 3 1 0 0 0 1-1 2h-1v-2h-1c-1 1-1 2-2 3v2h-1z" class="H"></path><path d="M177 301l3-3h1 1c-1 1-1 1-1 2-1 1-2 3-3 4-1 0-1-1-2-2l1-1z" class="N"></path><path d="M187 294c1 0 3 1 4 0l3 1c-1 0-2 1-3 1 3 1 5 3 6 5l-1 1-1-2c-2-1-2-1-4-1l-3-1c-2 0-4 0-7 2 0-1 0-1 1-2h-1-1l-3 3-1-1c2-2 4-3 6-4 1 0 3-1 5-2z" class="C"></path><path d="M180 298c3-2 5-2 9-2-2 0-3 1-5 1h2 0 1l1 1c-2 0-4 0-7 2 0-1 0-1 1-2h-1-1z" class="R"></path><path d="M187 294c1 0 3 1 4 0l3 1c-1 0-2 1-3 1h-2c-4 0-6 0-9 2l-3 3-1-1c2-2 4-3 6-4 1 0 3-1 5-2z" class="f"></path><path d="M176 302c1 1 1 2 2 2-1 4-1 7 1 10s4 5 7 6h3c-1 1-3 1-4 1v1h-3l-1-1-1 1c-2-2-4-4-5-7-2-4-1-8 1-13z" class="B"></path><defs><linearGradient id="AO" x1="198.027" y1="309.72" x2="183.472" y2="311.093" xlink:href="#B"><stop offset="0" stop-color="#4b4b4b"></stop><stop offset="1" stop-color="#6a696b"></stop></linearGradient></defs><path fill="url(#AO)" d="M191 299c2 0 2 0 4 1l1 2 1-1 1 2c1 4 1 7-1 10-2 2-4 3-7 3-2 0-4 0-6-1 0-1-1-1-1-2s1-1 1-2l2 2h4c2-1 3-4 4-6v-2c0-3-1-4-3-6z"></path><path d="M191 299c2 0 2 0 4 1l1 2 1-1 1 2v2h-1c0 1 0 3-1 4-1-1-1-4-1-5h-1v3-2c0-3-1-4-3-6z" class="L"></path><path d="M197 301l1 2v2h-1c0-1-1-2-1-3l1-1z" class="D"></path><path d="M272 468c3-4 9-5 13-5 5 0 8 2 12 5l2 1c0 2 0 2 1 4h0c1 1 1 3 1 5v1l1-1c0 4-2 8-4 11-1 2-4 3-6 4h0 0v1 1 1c-2 1-6 1-8 1l-1-1h-4 0 3v-1h-3l1-1h0l-3-1c-4-2-7-4-9-9-1-2-1-9 0-11s2-4 4-5z" class="E"></path><path d="M287 470l1-1c2 1 3 3 4 4v2l1 1c0 1 0 2-1 3h-1 0l-1-1 1-2c0-2-2-4-4-6z" class="P"></path><path d="M271 483h0v-6c0-2 1-4 3-6 2-1 5-3 7-3v1c-4 1-6 3-7 7-1 1-1 1-1 2h0c0 3 2 8 4 10-3-2-3-4-4-7l-1 1v1h-1z" class="X"></path><path d="M276 477c1-1 1-2 2-3s2-2 4-3c1-1 2-1 4 0s2 2 3 4c0 2 0 4-1 5s-2 1-3 1-2 0-2-1l-2-2v-2c-1 1-1 1-1 2s1 2 2 3l1 1-1 1c0-1 0-1-1-1h0l1 2h0c-1 0-2-1-2-2h0c-1-1-1-2-2-2 0 1 0 2 1 4-1-2-2-3-2-4s0-2-1-3z"></path><path d="M286 471c2 1 2 2 3 4h-1l-2-2c-1-1 0-1 0-2z" class="M"></path><path d="M278 480v-2c0-1 1-1 2-1v-1c1-1 1-2 2-3h1c-1 2-2 3-2 5h0v-2c-1 1-1 1-1 2s1 2 2 3l1 1-1 1c0-1 0-1-1-1h0l1 2h0c-1 0-2-1-2-2h0c-1-1-1-2-2-2z" class="D"></path><path d="M297 468l2 1c0 2 0 2 1 4h0c1 1 1 3 1 5v1l-1 3c-1 5-3 7-8 10-3 0-8 0-11-2s-5-5-6-9c-1-2-1-5 1-7 1-2 3-4 5-4-2 2-5 4-5 7h0c1 1 1 2 1 3s1 2 2 4c1 0 2 1 2 2 1 0 1 1 2 1h0l1-1h0v1l1 1c1 0 2 0 3 1l1 1c1 0 4-2 5-3 1 0 1-1 2-2 2-3 3-3 4-7 0-1 0-3-1-4 0-1-2-4-3-5 1 0 1 0 2 1 0-1-1-2-1-2z" class="M"></path><path d="M276 477h0c1 1 1 2 1 3s1 2 2 4c1 0 2 1 2 2 1 0 1 1 2 1h0l1-1h0v1l1 1c1 0 2 0 3 1l1 1h-3c-3 0-5-1-7-4-3-2-3-5-3-9z" class="H"></path><path d="M281 486c1 0 1 1 2 1h0l1-1h0v1l1 1c1 0 2 0 3 1-2 0-3 0-5-1-1 0-1-1-2-2z" class="Q"></path><path d="M289 466c3 1 6 3 7 6 1 1 1 2 1 3v1 3h0c-1 2-2 4-4 6 1-1 2-1 2-1l1 1c-1 1-1 2-2 2-1 1-4 3-5 3l-1-1c-1-1-2-1-3-1l-1-1v-1h0l-1 1h0c-1 0-1-1-2-1 0-1-1-2-2-2-1-2-1-3-1-4 1 0 1 1 2 2h0c0 1 1 2 2 2h0l-1-2h0c1 0 1 0 1 1l1-1c0 1 1 1 2 1s2-1 4-2l1-3 1 1h0 1c1-1 1-2 1-3l-1-1v-2-1c-2-2-3-4-5-5h-1v-1l2 1 1-1z" class="J"></path><path d="M295 480c-1 2-2 4-4 4-1 1-2 1-3 1l1-1c3-1 4-4 6-4z" class="L"></path><path d="M289 481l1 1-1 2-1 1h-2c-2 0-3-1-4-2l1-1c0 1 1 1 2 1s2-1 4-2z" class="P"></path><path d="M292 473v-1c-2-2-3-4-5-5h-1v-1l2 1c3 0 5 1 6 4 1 1 1 2 2 4h0 0c-1 0-1 0-2-1 0 1-1 1-1 2l-1-1v-2z" class="S"></path><path d="M293 476c0-1 1-1 1-2 1 1 1 1 2 1 0 2 0 3-1 5-2 0-3 3-6 4l1-2-1-1 1-3 1 1h0 1c1-1 1-2 1-3z" class="d"></path><path d="M290 478l1 1h0 1c0 1 0 2-2 3l-1-1 1-3z" class="X"></path><path d="M293 485c1-1 2-1 2-1l1 1c-1 1-1 2-2 2-1 1-4 3-5 3l-1-1c-1-1-2-1-3-1l-1-1v-1c3 1 7-1 9-1z" class="D"></path><path d="M285 488c3-1 6-2 8-1h1c-1 1-4 3-5 3l-1-1c-1-1-2-1-3-1z" class="R"></path><path d="M272 468c3-4 9-5 13-5 5 0 8 2 12 5 0 0 1 1 1 2-1-1-1-1-2-1 1 1 3 4 3 5 1 1 1 3 1 4-1 4-2 4-4 7l-1-1s-1 0-2 1c2-2 3-4 4-6h0v-3-1c0-1 0-2-1-3-1-3-4-5-7-6l-1 1-2-1v1h1c2 1 3 3 5 5v1c-1-1-2-3-4-4l-1 1h0c-1-1-2-1-4-1h-2 0v-1c-2 0-5 2-7 3-2 2-3 4-3 6v6h0 1c1 2 2 4 3 5l2 1c2 1 3 3 6 4h1 8 0v1 1 1c-2 1-6 1-8 1l-1-1h-4 0 3v-1h-3l1-1h0l-3-1c-4-2-7-4-9-9-1-2-1-9 0-11s2-4 4-5z" class="P"></path><path d="M280 494h1c1 0 2 0 3 1 2 0 5-1 8-1v1 1c-2 1-6 1-8 1l-1-1h-4 0 3v-1h-3l1-1h0z" class="K"></path><path d="M275 468c2 0 4-2 6-2 3-1 5-1 8 0l-1 1-2-1v1h1c2 1 3 3 5 5v1c-1-1-2-3-4-4l-1 1h0c-1-1-2-1-4-1h-2 0v-1c-2 0-5 2-7 3-2 2-3 4-3 6v6h0c1 3 2 4 4 6l2 1c1 0 1 1 2 2h0c-1 0-2 0-2-1-1 0-2 0-2-1-2-1-4-3-5-5h0v-3c-1-1-1-4 0-6h0c1-3 3-5 5-8h0z" class="L"></path><path d="M272 468c3-4 9-5 13-5 5 0 8 2 12 5 0 0 1 1 1 2-1-1-1-1-2-1 1 1 3 4 3 5 1 1 1 3 1 4-1 4-2 4-4 7l-1-1s-1 0-2 1c2-2 3-4 4-6h0v-3-1c0-1 0-2-1-3-1-3-4-5-7-6s-5-1-8 0c-2 0-4 2-6 2l-1 1c-1 0-1 0-1 1l-1 1-1 1s0 1-1 1v1l-1 2v-1c0-3 2-5 3-7h0z" class="d"></path><path d="M298 476v3c1 0 1 0 2-1-1 4-2 4-4 7l-1-1c1-1 2-3 3-4v-4z" class="C"></path><path d="M296 469c1 1 3 4 3 5 1 1 1 3 1 4-1 1-1 1-2 1v-3c-1-2-1-4-2-6v-1z" class="H"></path><path d="M297 127c1 0 2 1 3 2h0 1c0 1 1 2 1 3l-1 2 4 8 1 2c0 2 0 3 1 4h0-1 0c0 1 1 3 1 4 0 3 2 9 0 12l-1 2-1 2-1-1-1 2h-2c-2 2-4 3-7 4h0-1 0 0c-1-1-3 0-4 0 0-1 0-1-1-1v-1l-1 1c0-1 0-1-1-1v-1l1-1h0c0-1 2-1 3-1l-1-1h-2 0l-1-1v2l-1 1-1-1h0c-2 0-4 1-5 3-2 0-4 1-5 1-3 0-6-2-8-4-2-3-3-5-4-9 0-3 0-6 1-9 0-5 0-11 3-15h1l-1 2c0 5-1 13 3 17 2 3 5 5 8 5h1 1v1c2 0 3-1 4-1h3c1 0 1 1 2 1l1-1c5-4 6-11 6-18 1-3 1-8 1-12 0-1 1-1 1-2z"></path><path d="M276 163v-1l2-2 1 1c-1 1-2 2-3 2z" class="E"></path><path d="M284 166h0l-2-5c2 1 4 2 5 2v1c-1 0-1 1-1 2h-1l-1-2v2z" class="N"></path><path d="M286 159c1 0 1 1 2 1-2 1-3 1-4 1-2 0-3 1-4 2v2h0c-2-1-3-1-4-2 1 0 2-1 3-2l-1-1v-1h1v1c2 0 3-1 4-1h3z" class="B"></path><path d="M297 127c1 0 2 1 3 2h0 1c0 1 1 2 1 3l-1 2c0-1 0-2-1-2v1c-2 3-1 7 0 11h0c0 4 0 9-2 12h0c1-4 1-7 0-10 0-6-1-12-2-17 0-1 1-1 1-2z" class="K"></path><path d="M301 129c0 1 1 2 1 3l-1 2c0-1 0-2-1-2v1c-1-2-1-2-1-4h1 0 1z" class="L"></path><path d="M297 154c0-3 1-6 1-8 1 3 1 6 0 10-2 4-4 8-8 11v1l-1-1h-2 0l-1-1v2l-1 1-1-1h0v-2-2l1 2h1c0-1 0-2 1-2v-1c1 1 2 2 3 2 3-3 5-7 7-11z" class="R"></path><path d="M297 154c0-3 1-6 1-8 1 3 1 6 0 10-2 4-4 8-8 11v1l-1-1 1-1c2-1 4-5 6-7l1-3v-2z" class="S"></path><defs><linearGradient id="AP" x1="305.193" y1="138.96" x2="298.837" y2="135.625" xlink:href="#B"><stop offset="0" stop-color="#212220"></stop><stop offset="1" stop-color="#39383b"></stop></linearGradient></defs><path fill="url(#AP)" d="M300 133v-1c1 0 1 1 1 2l4 8 1 2c0 2 0 3 1 4h0-1 0c-1 0-2-1-2-1h0-1c-1-1-2-4-2-6h-1v3h0c-1-4-2-8 0-11z"></path><path d="M304 146v-3h0c1 0 1 0 1-1l1 2c0 2 0 3 1 4h0-1 0c-1 0-2-1-2-1v-1z" class="P"></path><path d="M301 141c0-1-1-2-1-3v-1c2 3 3 6 4 9v1h0-1c-1-1-2-4-2-6z" class="L"></path><path d="M300 144v-3h1c0 2 1 5 2 6h1 0s1 1 2 1c0 1 1 3 1 4 0 3 2 9 0 12l-1 2-1 2-1-1-1 2h-2c-2 2-4 3-7 4h0-1 0 0c-1-1-3 0-4 0 0-1 0-1-1-1v-1l-1 1c0-1 0-1-1-1v-1l1-1h0c0-1 2-1 3-1v-1c4-3 6-7 8-11h0c2-3 2-8 2-12z"></path><path d="M298 156h0c-2 6-4 10-9 14l-2-1h0c0-1 2-1 3-1v-1c4-3 6-7 8-11z" class="G"></path><path d="M303 163c1 1 1 2 1 3v1l-1 2h-2c-2 0-4 1-6 1h0v-1l3-1c3 0 4-3 5-5z" class="C"></path><path d="M304 147s1 1 2 1c0 1 1 3 1 4-1 1-1 2-1 3l-1 4h0-1c0-4 0-8-1-12h1 0z" class="K"></path><path d="M304 147s1 1 2 1c0 1 1 3 1 4-1 1-1 2-1 3l-2-8h0z" class="X"></path><path d="M289 170l6-1v1h0c2 0 4-1 6-1-2 2-4 3-7 4h0-1 0 0c-1-1-3 0-4 0 0-1 0-1-1-1v-1l-1 1c0-1 0-1-1-1v-1l1-1 2 1z" class="K"></path><path d="M288 171c1 0 4 0 6 1v1h-1 0 0c-1-1-3 0-4 0 0-1 0-1-1-1v-1z" class="X"></path><path d="M307 152c0 3 2 9 0 12l-1 2-1 2-1-1v-1c0-1 0-2-1-3l1-4h1 0l1-4c0-1 0-2 1-3z" class="c"></path><path d="M304 159h1v2c1 2 0 4 1 5l-1 2-1-1v-1c0-1 0-2-1-3l1-4z" class="B"></path><path d="M305 161c1 2 0 4 1 5l-1 2-1-1v-1l1-5z" class="L"></path><path d="M246 372l-3-3c-6-5-9-11-9-18-1-5 0-9 1-14v-4c1-4 4-8 7-12h-1l3-3c2-2 4-3 5-6 0-1 1-2 0-3 0-1-1-1-2-1-5 4-6 11-9 16-1-2-3-4-3-6h1l1 3c1 0 2-1 2-2 1-1 3-4 3-6h-1v-1l1 1c1-1 2-3 3-4 0-1 2-2 2-3s0-1 1-1c0 1 0 2 1 3h1c-1-1-1-2-1-3h-1c-1-2-2-2-3-3-1 0-1 0-1-1h1c2 1 4 2 5 4 1 1 1 2 1 4l-1 1c0 2-1 3-2 5 0 0-1 1-1 2 2-2 5-3 7-4 10-4 19-3 29-1h-1l-1 1h0c1 1 3 1 4 2v1l-4-2c2 2 5 4 5 7 1 2 0 5-2 7h0l1 2c-1 0-1-1-2-1v-2l-3 1v-1s1-1 2-1l1-1c0-2-1-4-3-5-2-2-8-4-12-5-3 0-6 0-9-1h-1v1c4 1 9 1 11 6l1 1h0c2 3 1 9 4 11 1-1 2-1 3-2-1-1-1-2-2-2l1-1 2 2c0 1-1 2-2 3h-2c-2 0-2-1-3-3-1-4-1-10-7-12 0-1-1-1-2-1-5-2-11 0-16 3s-10 9-10 15h0c3-6 7-11 14-14 3-1 9-2 13 0 2 1 4 3 5 5 0 2 0 4-1 5s-1 2-2 3h0c2-1 3-3 4-4v-2h1c0 3-2 5-4 6 0 1-1 1-1 1h-5c1 1 3 3 3 4s1 2 1 3c1 2 1 5 1 8l-2-1-2 2c-2 2-4 3-8 3l1-1h0c3 0 5-1 6-2 2-2 2-5 2-8s-2-6-4-8c-2-1-5-2-8-1-5 0-8 2-11 6-2 3-3 7-3 11 1 6 3 12 9 17 4 3 7 4 12 5-1 1-2 1-3 1h-1l1 1h0c-1 0-1 0-1 1-3-1-5-2-7-4z" class="C"></path><path d="M246 372c3-1 5 1 8 2h0-1l1 1h0c-1 0-1 0-1 1-3-1-5-2-7-4z" class="Z"></path><path d="M263 349c0-2 1-5 0-7-1-4-4-7-7-9 1 0 3 2 4 2 1 1 3 3 3 4s1 2 1 3c1 2 1 5 1 8l-2-1z" class="f"></path><path d="M261 313c7 0 16 0 22 4 1 1 2 3 2 4 0 2 0 4-1 5l-1 1h0c0-1 0-2 1-3 0-1-1-2-2-4-3-3-11-5-15-6-2 0-5 0-7-1h0 1zm-5 8c3 0 6 0 8 2 2 1 3 3 3 5 0 3-2 4-3 6-3 0-6-1-8-2-1-1-1-1-1-2s1-3 1-4h2c1 0 1 1 2 2 0 1-1 2-2 3h0v1l2-2c0-1 1-2 1-3-1-1-2-2-3-2s-2-1-3 1c-1 1-1 3-1 5l1 1h-2c-6-1-12 3-16 9 0 1-1 2-2 3 0-5 2-11 5-15 4-5 9-7 16-8zm5-140v-1-2h0 0c1 1 4 3 4 5l2 3c1 2 1 4 3 6 1 4 2 7 3 11l1 4c-1 7-1 12-6 17l1 1s-3 3-4 3l-2 2c-1 0-4 0-6 1-1 0-3 0-5-1-2 0-3-1-5-1 0-1 0-1 1-2h-1l-2-1c-3-2-6-6-7-10 0-1-1-1-1-2s-1-2 0-4v2h0c2-2 3-4 5-6 1-1 2-2 4-2l1-1-2-1 1-1c1 2 2 3 4 3h3c3 0 6-1 8-3 4-2 4-5 5-9 0-1 0-3-1-4 0-1 0-2-1-3s-1-2-2-3l-1-1z"></path><path d="M261 181v-1-2h0 0c1 1 4 3 4 5 0 1 1 3 1 4 1 1 1 2 1 4h0v-1h-1v1 1c0-1 0-3-1-4 0-1 0-2-1-3s-1-2-2-3l-1-1z" class="K"></path><path d="M268 224l1 1s-3 3-4 3l-2 2c-1 0-4 0-6 1-1 0-3 0-5-1-2 0-3-1-5-1 0-1 0-1 1-2 2 1 4 2 6 2 6 0 10-1 14-5z" class="U"></path><path d="M247 213h0 3l3 2c1 0 2 0 3 1 0 1 1 1 1 2s1 1 0 2c-1 0-1 1-2 1l-2-1c-2-2-4-5-7-6l1-1z" class="B"></path><path d="M257 218c0 1 1 1 0 2-1 0-1 1-2 1l-2-1h0c1-1 0-1 0-2h0c1 0 2 0 4 1v-1z" class="D"></path><path d="M266 192v-1-1h1v1c0 4-1 8-4 11s-8 3-13 3c1 1 1 3 0 4-1-1-1-1-2-1v-1h0c1-1 1-2 2-3h0 3c3 0 6-1 8-3 4-2 4-5 5-9z" class="C"></path><path d="M246 201c1 2 2 3 4 3h0c-1 1-1 2-2 3h0v1c1 0 1 0 2 1 1 0 1 1 1 1 2 1 6 1 8 3 0 1-1 2-1 3h-2c-1-1-2-1-3-1l-3-2h-3 0l-1 1c-1-1-3-1-5-1l-3 3c0-1-1-1-1-2s-1-2 0-4v2h0c2-2 3-4 5-6 1-1 2-2 4-2l1-1-2-1 1-1z" class="B"></path><path d="M250 212c1 1 1 1 2 1h1c1-1 2-1 3-1 1 1 1 1 2 1v1h0l1-1c0 1-1 2-1 3h-2c-1-1-2-1-3-1l-3-2h-3 0v-1c1 0 1 1 2 0h1z" class="b"></path><path d="M237 210v2h0c2-2 3-4 5-6 1-1 2-2 4-2 0 1 1 2 0 2v2h-1v-2c-1 0-1 1-2 1v2l-1 1h-1v1h4l1-1c1 1 3 1 4 2h-1c-1 1-1 0-2 0v1l-1 1c-1-1-3-1-5-1l-3 3c0-1-1-1-1-2s-1-2 0-4z" class="D"></path><path d="M276 462h1c1 1 2 0 3 0l-2 1c-2 0-4 1-5 2h-1c-3 3-5 6-6 11 0 3 0 5 1 7h0c1 4 4 8 7 10l5 2h3v1h-3 0 4l1 1c-2 1-5 1-7 2h0 4 1c-1 1-1 1-1 2-1 1-2 1-3 2h0c1 1 1 2 3 2 0 1 2 2 2 2 3 1 6 0 9 0v2h0 2c-2 2-5 3-7 4-1 0 0 0 0 0-3 1-8 2-10 1h-4c-3 0-5-1-7-2h-1s-2-1-3-1l-3-3-3-3c-1-1-1-2-2-3l-3-5h0c0-1-1-3-1-4v-1c0-3 0-6 1-8 0-3 1-4 3-6l1-3c1-1 3-4 4-4h0l1-1-1-1s0 1-1 1v-1h0c2-2 5-3 7-4 4-1 7-3 11-3z"></path><path d="M259 478l1 1v2l-3 1c0-1 1-3 2-4z" class="C"></path><path d="M281 499h1c-1 1-1 1-1 2-1 1-2 1-3 2h0v-1l-2-1-4-1 2-1h3 4z" class="D"></path><path d="M281 499h1c-1 1-1 1-1 2-1 1-2 1-3 2h0v-1l-2-1c1 0 2 0 2-1 1 0 2 0 3-1z" class="b"></path><path d="M272 500l-2-1c-1-1-2-1-2-3 0-1 0-2 1-3l5 2c-1 0-2 0-2 1h-2v1c1 1 2 2 3 2h1l-2 1z" class="C"></path><path d="M257 482l3-1c-1 4 0 7 1 10h0c0 5 3 8 5 12v1c-5-6-7-15-9-22z" class="E"></path><path d="M254 502h2l1 1c4 6 9 7 15 9h-6-1s-2-1-3-1l-3-3-3-3c-1-1-1-2-2-3z" class="C"></path><path d="M254 502h2l1 1-1 2 3 3h0 0l-3-3c-1-1-1-2-2-3z" class="G"></path><path d="M274 495l5 1h0 4l1 1c-2 1-5 1-7 2h0-3-1c-1 0-2-1-3-2v-1h2c0-1 1-1 2-1z" class="E"></path><path d="M272 512c7 1 14 0 20-3h2c-2 2-5 3-7 4-1 0 0 0 0 0-3 1-8 2-10 1h-4c-3 0-5-1-7-2h6z" class="I"></path><path d="M250 493v-1c0-3 0-6 1-8 0-3 1-4 3-6-2 8-3 14 1 22 0 1 0 1 1 2h-2l-3-5h0c0-1-1-3-1-4z" class="N"></path><path d="M257 477c-1 5 0 7 1 11 0 1 1 3 1 4 1 4 3 8 5 11v1c-2-1-3-3-4-4v2l-1 1-1-3c0-4 0-7-1-11 0-2-2-5-2-7s1-4 2-6v1z" class="K"></path><path d="M276 462h1c1 1 2 0 3 0l-2 1c-2 0-4 1-5 2h-1c-1 1-3 2-4 3h-2l-2 2-2 1c0-1 0-1-1-1-2 2-3 4-4 7v-1l2-5h0l1-1-1-1s0 1-1 1v-1h0c2-2 5-3 7-4 4-1 7-3 11-3z" class="Q"></path><path d="M261 470c1-2 4-3 6-4 1 0 2-1 3-2l-4 4-2 2-2 1c0-1 0-1-1-1z" class="X"></path><path d="M266 468h2c-4 4-6 10-6 15 0 2 1 3 1 5 0 1 2 4 2 4v1c-1 0-1 1-1 2l-1-1h0c-1-1-1-2-1-3h-1 0 0c-1-3-2-6-1-10v-2l-1-1 2-4c0-1 1-2 1-3l2-1 2-2z" class="H"></path><path d="M262 471l2-1c0 3-2 6-3 10v-2l-1 1-1-1 2-4c0-1 1-2 1-3z" class="G"></path><path d="M261 474v1 1 2l-1 1-1-1 2-4z" class="H"></path><path d="M260 479l1-1v2s-1 2-1 3c1 2 2 5 3 7 0 0 1 1 1 2 0 0 0 1-1 1v1c-1-1-1-2-1-3h-1 0 0c-1-3-2-6-1-10v-2z" class="c"></path><path d="M252 155h0c0-1 1-2 1-2h1l-1 1 1 1v-1l1 1h0c-1 2-1 5-1 7 0 7 4 13 7 19l1 1c1 1 1 2 2 3s1 2 1 3c1 1 1 3 1 4-1 4-1 7-5 9-2 2-5 3-8 3h-3c-2 0-3-1-4-3l-1 1 2 1-1 1c-2 0-3 1-4 2-2 2-3 4-5 6h0v-2c1-3 1-6 2-9-1-1-2-2-3-2h-1c-1 2-1 5-1 8h0 0c-1-2 0-3 0-5 0-4 1-8 1-13 1-4 2-9 4-13 1-4 4-7 6-11l7-10z"></path><path d="M242 186l1 1c0 3-1 7 2 10 1 1 3 1 4 2h-2 0-1c-1 0-2-2-3-3v1l3 4-1 1c-1-2-4-5-4-7-1-3-1-6 1-9z" class="L"></path><path d="M247 168c1 1 1 3 1 4s-1 1-1 2v3l-1 1v1c1 0 1-1 2-1h0c0 2-2 2-3 3-1 0-3 0-4-1-1-2 2-4 3-6l3-6z" class="Q"></path><path d="M252 155h0c0-1 1-2 1-2h1l-1 1 1 1c-1 3-1 8-3 11-1 4-1 9-3 12h0c-1 0-1 1-2 1v-1l1-1v-3c0-1 1-1 1-2s0-3-1-4v-1c2-4 4-8 5-12z" class="G"></path><path d="M242 186l2-2c1-2 5-3 7-3 1 0 2 1 3 2v2c0 1 0 2-1 3l-1-1h0c0-2-2-3-3-3h-2c-1 2-2 3-2 6 1 1 1 3 3 4 1 1 3 1 4 1 3 0 4-1 6-3 1-3 1-6 1-9-2-4-9-11-7-16 1 1 1 4 1 5 3 5 7 9 7 15 0 3 0 5-2 7s-5 2-7 2-4-1-5-2c-1-2-2-4-2-6s2-3 3-5c-2 1-3 2-4 4l-1-1z" class="B"></path><path d="M262 182c1 1 1 2 2 3s1 2 1 3c1 1 1 3 1 4-1 4-1 7-5 9-2 2-5 3-8 3h-3c-2 0-3-1-4-3l-3-4v-1c1 1 2 3 3 3h1 0 2c4 0 7 0 10-2s3-5 3-7l1-2c0-1-1-4-1-6z"></path><path d="M262 182c1 1 1 2 2 3s1 2 1 3c0 4 0 8-4 11s-7 3-12 3c-1 0-2 0-3-1v-1c1 0 2 1 4 1 4 1 11-2 13-6 1-1 1-4 1-5v-2l-1 1h0c0 3 0 5-2 7-2 3-6 4-9 4-2 0-3 0-5-1h2c4 0 7 0 10-2s3-5 3-7l1-2c0-1-1-4-1-6z" class="Z"></path><path d="M306 144l2 3c2 1 5 3 5 5v1l2 4c1 1 1 2 1 3 2 4 1 7 1 11v1h-1v1h1c1 2 1 3 1 5l-4 9c-2 5-8 11-14 13-4 1-9 2-13 0l-2-1c0-1-2-2-3-3s-1-1-1-2 0-2 1-3v-3c0-3 2-5 5-6-1-1-1-1-2 0-1 0-2 1-3 2v-1c0-1 2-2 3-3h0c2-2 5 0 8-1v-2-4h0 1 0c3-1 5-2 7-4h2l1-2 1 1 1-2 1-2c2-3 0-9 0-12 0-1-1-3-1-4h0 1 0c-1-1-1-2-1-4z"></path><path d="M293 187h1v1 1c-1 1-2 1-4 1 0-1-1-1-1-3h0c1 0 1 1 2 1h0c1 0 2-1 2-1zm16-23v2 3c-1 2-1 6-3 9h0c-1-1-2-1-2-2s1-1 1-2c2-3 3-7 4-10z" class="N"></path><path d="M293 177c1 1 2 1 2 2h1c1 1 2 2 3 4l-4-4c-3 1-5 1-8 3-1-1-1-1-2 0-1 0-2 1-3 2v-1c0-1 2-2 3-3h0c2-2 5 0 8-1v-2z" class="L"></path><path d="M306 148h0 1 0l1 5c2 4 1 8 1 11-1 3-2 7-4 10v-2-1l1-1h0v-3c0 1-1 2-2 3h0l1-2 1-2 1-2c2-3 0-9 0-12 0-1-1-3-1-4z" class="d"></path><path d="M293 187c0-1 0-1-1-2l-1 1c-1-1-2 0-4-1v-1l2-2c2-1 4 0 5 1s2 1 3 2c0 1 0 3-1 4s-2 2-4 3c-2 0-2 0-3-1s-2-2-2-3v-1h1 0v1l3 3 2-1h0c1 0 2-1 3-2 0-1 0-2-1-3 0-1-2-1-3-2h0c1 2 2 2 3 4l-1 1v-1h-1z" class="L"></path><path d="M303 169l1-2 1 1-1 2v1c-1 1-1 2-2 3-2 2-3 3-6 5h-1c0-1-1-1-2-2v-4h0 1 0c3-1 5-2 7-4h2z" class="Q"></path><path d="M294 173h0c0 1 1 2 1 2 1 0 4-1 5-2v-1h0v2c-1 0-3 3-4 4-2-1-2-3-3-5h1 0z" class="E"></path><path d="M303 169l1-2 1 1-1 2v1c-1 1-1 2-2 3h-1-1 0v-2l1-1 2-2z" class="G"></path><path d="M303 169l1-2 1 1-1 2v1-1l-2 1h-1l2-2z" class="Z"></path><path d="M301 169h2l-2 2-1 1h0v1c-1 1-4 2-5 2 0 0-1-1-1-2h0c3-1 5-2 7-4z" class="I"></path><path d="M306 144l2 3c2 1 5 3 5 5v1l2 4h0v1c0 1-1 1-1 2-1 1-2 2-2 3v3l-2 2v4c-1 2-1 5-2 7h-1l2-10h0v-3-2c0-3 1-7-1-11l-1-5c-1-1-1-2-1-4z" class="D"></path><path d="M306 144l2 3c1 2 2 4 2 6v1c1 1 2 2 2 4v2c-1 2-1 4-2 7h0c0 1 0 1-1 2h0v-3-2c0-3 1-7-1-11l-1-5c-1-1-1-2-1-4z" class="L"></path><path d="M314 160c0-1 1-1 1-2v-1h0c1 1 1 2 1 3 2 4 1 7 1 11v1h-1v1 1l-1 2c0 1 0 2-1 3v2h-2-1c-1 0-2 0-2-1v-2c1-2 1-4 1-6v-4l2-2v-3c0-1 1-2 2-3z" class="N"></path><path d="M314 160l-1 7h0l-1-1v-3c0-1 1-2 2-3z" class="X"></path><path d="M310 172v-4l2-2 1 1h0l-2 11v2h-1v-2h-1c1-2 1-4 1-6zm6-12c2 4 1 7 1 11v1h-1v1 1l-1 2c0 1 0 2-1 3v2h-2c0-2 1-3 1-4l1-4c0-4 0-9 1-12h1v-1z" class="S"></path><path d="M316 171v-1l1 1v1h-1v1 1-3z" class="C"></path><path d="M314 179v-1c-1-2 0-4 1-5h0v2 1c0 1 0 2-1 3z" class="b"></path><path d="M316 160c2 4 1 7 1 11l-1-1v1h0c-1-3-1-7 0-10v-1z" class="B"></path><path d="M316 173h1c1 2 1 3 1 5l-4 9c-2 5-8 11-14 13-4 1-9 2-13 0l-2-1c0-1-2-2-3-3s-1-1-1-2 0-2 1-3c2 4 4 6 7 7 4 1 9 1 13-1 6-3 9-9 12-16v-2c1-1 1-2 1-3l1-2v-1z" class="E"></path><path d="M237 538v-5c1 1 1 2 1 4h1c0 3 1 5 0 7 0 3-1 5-1 7l-1 2c1 0 1 1 1 1 0 1 1 1 1 2-1 5-5 9-9 12-6 3-12 3-18 4-4-1-7-2-11-4-3-2-6-6-7-10s-1-9 1-13 6-5 9-6c2-1 3 0 4-1-1-1-3-1-4-1 4-1 7-1 11 1l2 3h1c2 2 6 6 9 6h1c2 0 4-4 6-5v-1c-1 0-1 0-2-1l1-1c0 1 0 1 1 1 0 0 1 0 1-1h1s1 0 1-1z"></path><path d="M226 554c1 1 1 1 1 2h-2l-1-1c1-1 1-1 2-1z" class="J"></path><path d="M227 551c1 1 1 1 1 2s0 2-1 2v1c0-1 0-1-1-2 0 0 0-1-1-1h0v-1h1c0-1 0-1 1-1z" class="B"></path><path d="M222 551v-2c1 0 2 0 4 1 0 0 1 0 1 1-1 0-1 0-1 1h-1v1h0c1 0 1 1 1 1-1 0-1 0-2 1h0l-1-1c-1-1-1-2-1-3z" class="G"></path><path d="M222 551c1 0 1 0 2 1 0 0-1 1-1 2-1-1-1-2-1-3z" class="B"></path><path d="M237 538c0 4 0 8-3 11 0 0-1 0-1 1l-1-1-1-1 3-6v-1c-1 0-1 0-2-1l1-1c0 1 0 1 1 1 0 0 1 0 1-1h1s1 0 1-1z" class="D"></path><path d="M218 562v-4h0l-2 2c-2 2-5 3-8 3l-6-3c-1-2-1-5-1-7s2-3 3-4 3-1 4-1v1h-2c-1 0-3 1-3 3-1 1-2 3-1 4h0l1 2c0 2 2 3 4 3 2 1 4 1 6 0s4-3 5-4 1-3 1-4l1-1c1 0 1 0 1 1l-1 4v1 1c-1 0-1 1-1 2l-1 1z" class="I"></path><path d="M220 557h0l2-2h0c2 2 4 4 3 7 0 1 0 1-1 2h-1c0-1 0-1-1-1-1 1-1 1-3 2-1-1-1-1-1-2v-1l1-1c0-1 0-2 1-2v-1-1z" class="E"></path><path d="M204 537c4-1 7-1 11 1l2 3c2 3 5 7 4 12h0c0-1 0-1-1-1l-1 1s-1 1-1 2c-1 2-3 4-6 5-2 1-4 1-5 0-2-1-3-2-4-4h0c0-1 0-3 1-4s2-2 3-2c0 0 1 0 1-1h1c0 1 0 1 1 1l-1 1c-1 0-3 0-4 1l-1 2c0 2 0 2 1 3s2 2 4 2 4 0 6-2 3-4 3-7-1-6-3-8c-3-3-7-3-11-3 2-1 3 0 4-1-1-1-3-1-4-1z" class="H"></path><path d="M318 396l-1-1h0v-5h0c12 6 23 15 28 28l2 5v2l2 6c1 6 0 13 0 18v11c1 4-1 9-2 13 0 2-1 3-1 5l-1-1c-1 5-3 13-7 17-1 1-2 2-3 4l-4 6h1c-2 4-5 7-7 10-1 1-3 5-4 5 1-5 5-11 8-16 0-1 0-2 1-2h0v-1c1 0 1 0 1-1v-1l-1 1c0 1-1 1-2 1h0c-2 3-3 5-5 7h0c1-2 2-5 3-7-1 1-2 3-3 4 0-2 1-3 2-4 0-2 1-3 1-5 2-2 4-5 5-8 0-2 1-4 2-5h0v3c0 2-2 3-2 5 1-1 2-3 2-5v-1h1c0-1 0-1-1-2 1-2 2-4 2-6 1-1 1-3 2-5 0-2 0-4 1-6 0-1 0-3-1-4v-2c2-5 1-11 1-15v-5h0c1-5 0-11-2-16-1-2-1-4-3-5-1-2-2-5-3-7-1-1-2-3-2-4-2-3-8-8-11-8v-2l1-1h0z" class="M"></path><path d="M342 439h0v7h1v-4c1 3 0 7 0 10 1 3 1 6 0 9v2s0 1-1 1v-10-15zm6-7c1 1 0 3 0 4 0 3-1 11 1 13v11c1 4-1 9-2 13 0 2-1 3-1 5l-1-1c2-4 3-11 3-16v-5-24z" class="T"></path><path d="M345 418l2 5v2l2 6c1 6 0 13 0 18-2-2-1-10-1-13 0-1 1-3 0-4l-1 1v6h0v4c-1-1-1-1-1-2 0-3 0-6-1-9 0-2 0-4-1-6 0-2-1-3-1-5 1-1 1-2 2-3z" class="a"></path><path d="M346 441c0 1 0 1 1 2v-4h0c0 5 0 10-1 15 0 2 0 4-1 6-1 1-1 5-1 7l-2 10c-2 6-3 11-7 16 1-3 3-6 4-9s1-5 2-8c0-4 2-8 1-12 1 0 1-1 1-1v-2l1 1v-1h0c2-3 1-6 1-8s1-3 1-5v-4c0-1-1-3 0-3z" class="H"></path><path d="M340 420v1c2 4 4 11 4 16h1c0-6-2-14-4-20h0l2 4c0 2 1 3 1 5 1 2 1 4 1 6 1 3 1 6 1 9-1 0 0 2 0 3v4c0 2-1 3-1 5s1 5-1 8h0v1l-1-1c1-3 1-6 0-9 0-3 1-7 0-10 0-5 0-10-2-15 0-2-1-4-1-7z" class="O"></path><path d="M330 406c2 0 3 0 4 2 1 0 1 1 2 1 1 2 3 4 4 6l1 1-1 1v-1s-1 0-1-1l-1 1c0 1 0 1 1 2v1l1 1h0c0 3 1 5 1 7 2 5 2 10 2 15v4h-1v-7h0c-1-2 0-5-1-7-1-4-1-7-2-10-3-6-6-11-9-16z" class="e"></path><path d="M329 503c3-4 6-7 8-11 3-5 4-9 6-14 0-3 1-6 2-9v-1-2-2c1-1 1-2 1-2 0-1 0-1 1-2v1 1c0 1 0 3-1 4v4c0 1-1 1-1 1 0 3-1 5-1 7l-3 9-3 6v1c-1 1-2 2-3 4l-4 6h1c-2 4-5 7-7 10-1 1-3 5-4 5 1-5 5-11 8-16z" class="Z"></path><path d="M317 399v-2l1-1c3 2 6 4 8 7 2 2 4 4 5 6 4 6 7 12 9 19 2 11 1 21 1 32 0 2 1 4 1 6-1 5-2 9-3 13h0c-1-1-1-2-1-4 2-3 3-9 2-13v-6-8c0-10-1-20-5-29-1-1-2-3-3-4-1-2-1-3-2-4s-2-3-2-4c-2-3-8-8-11-8z" class="U"></path><path d="M318 396l-1-1h0v-5h0c12 6 23 15 28 28-1 1-1 2-2 3l-2-4h0c2 6 4 14 4 20h-1c0-5-2-12-4-16v-1h0l-1-1v-1c-1-1-1-1-1-2l1-1c0 1 1 1 1 1v1l1-1-1-1c-1-2-3-4-4-6-1 0-1-1-2-1-1-2-2-2-4-2-3-4-8-8-12-10z" class="n"></path><path d="M330 411c1 1 1 2 2 4 1 1 2 3 3 4 4 9 5 19 5 29v8 6c1 4 0 10-2 13 0 2 0 3 1 4h0c-1 6-4 12-8 17-1 2-2 3-3 4-2 3-3 5-5 7h0c1-2 2-5 3-7-1 1-2 3-3 4 0-2 1-3 2-4 0-2 1-3 1-5 2-2 4-5 5-8 0-2 1-4 2-5h0v3c0 2-2 3-2 5 1-1 2-3 2-5v-1h1c0-1 0-1-1-2 1-2 2-4 2-6 1-1 1-3 2-5 0-2 0-4 1-6 0-1 0-3-1-4v-2c2-5 1-11 1-15v-5h0c1-5 0-11-2-16-1-2-1-4-3-5-1-2-2-5-3-7z" class="D"></path><path d="M338 475c0 2 0 3 1 4h0c-1 6-4 12-8 17 0-2 3-7 4-8 1-4 2-8 3-13z" class="T"></path><path d="M263 134c6-7 11-13 17-18 6-6 14-11 22-15l1 1s-1 1-1 2c-1 1-2 1-2 2-1 2-2 3-3 5-2 1-3 3-4 4v2c-1 0-1 0-1 1l2-2v-1h1l1 1h0l2-3c0 1-1 3-2 4v5-2h1v7c0 1-1 1-1 2 0 4 0 9-1 12 0 7-1 14-6 18l-1 1c-1 0-1-1-2-1h-3c-1 0-2 1-4 1v-1h-1-1c-3 0-6-2-8-5-4-4-3-12-3-17l1-2h0v-4c0 1-1 2-2 3h-1-1z"></path><path d="M274 136v3l-1 1c-1-1-2-2-3-4h1 3z" class="H"></path><path d="M292 118l2-2v-1h1l1 1c-1 1-2 3-3 4h-2l1-2z" class="F"></path><path d="M267 131l1-1c1-1 2-1 2-1-1 2-1 4-2 5 0 1 0 2-1 3h-1l1-2h0v-4z" class="Q"></path><path d="M296 122v-2h1v7c0 1-1 1-1 2 0 4 0 9-1 12v-4-1c-1-1 0-2 0-3l1-11z" class="G"></path><path d="M295 136v1 4c0 7-1 14-6 18l-1 1c-1 0-1-1-2-1 1-1 2-1 3-2 5-5 5-14 6-21z" class="Q"></path><path d="M270 129c0-1 1-2 1-2v-1c0-1 8-9 10-10h1c-1 0-1 1-1 1v1c-4 4-9 8-8 15 0 1 1 1 1 2 1 0 2 0 3 1h0c-1 1-2 0-3 0h0-3-1c-1-2 1-5 0-7z" class="B"></path><defs><linearGradient id="AQ" x1="276.882" y1="159.256" x2="271.208" y2="144.398" xlink:href="#B"><stop offset="0" stop-color="#767576"></stop><stop offset="1" stop-color="#949595"></stop></linearGradient></defs><path fill="url(#AQ)" d="M266 137h1c0 5 0 11 4 15 1 2 3 4 6 4s5-1 8-2c-1 0-2 2-2 2l1 1v1l-1 1c-1 0-2 1-4 1v-1h-1-1c-3 0-6-2-8-5-4-4-3-12-3-17z"></path><path d="M279 159h1c1 0 2-1 3-3l1 1v1l-1 1c-1 0-2 1-4 1v-1z" class="Z"></path><path d="M278 139c3 0 6 0 8 2 1 1 3 4 2 6 0 1-1 4-2 6l-1-1c-2 0-4-1-6-1-2-2-2-4-3-6-1-1-4-2-5-3h1 0c1 1 2 1 3 2h0 1l-2-2c1-2 2-2 4-3z" class="C"></path><path d="M278 144l1-1c1 1 0 1 1 2l-1 1v1h-1c0-1-1-2 0-3z" class="H"></path><path d="M277 142l-1-1c2 0 5-1 7 0v1h1v1h-1 0-1v-1h-4-1zm3 6h2c1 0 1-1 2 0h2v1h-2l-1 1c-1 0-3-1-3-1v-1z" class="F"></path><path d="M278 144l-2-1 1-1h1 4v1h1 0c1 1 2 2 2 3h1v2h-2c-1-1-1 0-2 0h-2l-1-2 1-1c-1-1 0-1-1-2l-1 1z" class="R"></path><path d="M263 134c6-7 11-13 17-18 6-6 14-11 22-15l1 1s-1 1-1 2c-1 1-2 1-2 2-1 2-2 3-3 5-2 1-3 3-4 4v2c-1 0-1 0-1 1l-1 2h2l-1 2h1c-1 2-1 4-2 6l3-6h1c0 3-1 4-2 6-1 1-2 1-3 0-1 0-2-1-2-2-1-4 1-8 3-11h-1l1-1c1-2 1-2 2-3-1 0-2-1-3-1-3 2-6 4-9 7 0 0 0-1 1-1h-1c-2 1-10 9-10 10v1s-1 1-1 2h0s-1 0-2 1l-1 1c0 1-1 2-2 3h-1-1z" class="J"></path><path d="M290 110c2-2 5-4 7-5-1 2-3 5-4 6-1 0-2-1-3-1z" class="c"></path><path d="M471 112v-1c1 0 1 0 2 1l1 3c1 2 2 3 4 4l3 4 1 1h1c4 6 8 11 10 18v1c1 1 1 2 2 2v4l-1 1h0c-1 0-1 1-1 2 0 2-1 4-1 5v1c0 2-1 3-2 5-3 4-8 6-14 7-1 0-2 0-4-1-1-1-3-1-5-2h-1c-4-1-8-6-10-9l1-1c0 1 0 1 1 1s2 1 3 1c-1-1-2-2-2-3v-3h0v-2c-1-1-1-1-1-3 0 0-1 0-1-1h0l-1 1h0c-1-6 6-12 9-17v1l1-1h0l1-1c0-1 1-4 2-5l1-4c0-3 1-6 1-9h0z"></path><path d="M459 153c2 0 2 1 3 2-1 1-2 1-3 1v-3z" class="I"></path><path d="M462 155h0c1 1 1 2 1 3 1 1 2 3 3 4h-1c-1 0-1 0-2-1l-2-2c-1-1-2-2-2-3 1 0 2 0 3-1z" class="F"></path><path d="M481 154c1-1 1-2 2-3 1 1 4 2 5 3-1 1-2 1-3 2h-3c0-1-1-1-1-2z" class="E"></path><path d="M471 165l1-1s-2-1-3-1c-2-1-3-3-5-4 2 1 4 2 5 3 5 3 9 2 14 1 0 1 1 1 2 2-4 1-7 1-10 1-1 0-2-1-4-1z" class="L"></path><path d="M493 143c1 1 1 2 2 2v4l-1 1h0c-1 0-1 1-1 2 0 2-1 4-1 5l-3 4h-2c0-1 1-1 1-2h-1 0l-1-1 1-2c1 0 1 0 2-1h0c4-3 4-7 4-12z" class="H"></path><path d="M493 152c0 2-1 4-1 5l-3 4h-2c0-1 1-1 1-2h-1 0c3-2 5-5 6-7z" class="P"></path><path d="M465 132l1-1h0c-1 5-1 12 2 17 1 1 3 3 3 5 0 1 0 1-1 1-2 0-3-1-4-2-3-6-2-14-1-20z" class="B"></path><path d="M487 159c-2 1-4 1-6 1s-4 0-5-2c-2-2-2-3-2-5 0-1 1-2 1-2 2-1 3-1 5-1v1c-1 0 0 0 0 1s-1 1-1 2 0 1 1 2c0-1 1-1 1-2h0c0 1 1 1 1 2h3c1-1 2-1 3-2l1 1h0c-1 1-1 1-2 1l-1 2 1 1h0z" class="G"></path><path d="M478 151h0 0c0 1-1 2-1 3 1 1 1 2 2 2h0v1l-1-1h-1v1c-1 0-1 0-1-1-1-1-1-2 0-3 0-1 1-2 2-2z" class="k"></path><path d="M476 170c-1 0-2 0-4-1-1-1-3-1-5-2h-1c-4-1-8-6-10-9l1-1c0 1 0 1 1 1s2 1 3 1l2 2c1 1 1 1 2 1h1l1 1c2 1 3 1 4 2 2 0 3 1 4 1 3 0 6 0 10-1-1-1-2-1-2-2 1 0 3-1 4-2h2l3-4v1c0 2-1 3-2 5-3 4-8 6-14 7z" class="E"></path><path d="M487 161h2c-1 2-3 3-4 4-1-1-2-1-2-2 1 0 3-1 4-2z" class="Z"></path><path d="M475 166h-1c-2 1-5 0-6-1-2-1-4-2-5-4 1 1 1 1 2 1h1l1 1c2 1 3 1 4 2 2 0 3 1 4 1z" class="C"></path><path d="M471 112v-1c1 0 1 0 2 1l1 3c1 2 2 3 4 4l3 4c3 5 7 10 5 16h0c0 1-1 2-1 3-2 2-4 2-7 3-3 0-4-1-6-3-1-1-5-4-5-6 2 1 2 3 4 2h0c-1 0-1-1-1-1-1-2-2-5-3-7 0-1 1-4 2-5l1-4c0-3 1-6 1-9h0z"></path><path d="M476 134h0c1-1 2-2 2-4h2c1 1 2 2 2 3v2c-1-1-1-1-2-1v-3h-1c-1 1 0 1-1 2s-1 1-2 1z" class="C"></path><path d="M480 134c1 0 1 0 2 1-1 1-2 3-3 4-2 1-3 1-4 0v-1h1 1l1-1h0c1-1 2-2 2-3z" class="H"></path><path d="M474 115c1 2 2 3 4 4l3 4c3 5 7 10 5 16h0 0c-1 0-1 0-2 1 0 1 0 1-1 2v-1c0-1 1-1 1-2v-1-3-1c0-1 0-3-1-5s-4-4-6-6c-1-1-2-4-3-6 0-1 0-1-1-2h1z" class="G"></path><path d="M471 112v-1c1 0 1 0 2 1l1 3h-1c1 1 1 1 1 2v1c0 3 1 6 0 9 0 0 0 1-1 0-1 0-1 0-2-1h-1v-1h-1l1-4c0-3 1-6 1-9h0z" class="D"></path><path d="M471 112v-1c1 0 1 0 2 1l1 3h-1c-1-1-2-1-2-3h0z" class="R"></path><path d="M469 125h1v1c0 2-1 4 1 6 0 1 1 2 3 3 1 0 1 0 2-1 1 0 1 0 2-1s0-1 1-2h1v3c0 1-1 2-2 3h0l-1 1h-1-1v1l-1 1-1-1c0-1-1-1-1-1h-1c-1 0-1-1-1-1-1-2-2-5-3-7 0-1 1-4 2-5z" class="G"></path><path d="M484 134v1 3 1c0 1-1 1-1 2v1c1-1 1-1 1-2 1-1 1-1 2-1h0c0 1-1 2-1 3-2 2-4 2-7 3-3 0-4-1-6-3-1-1-5-4-5-6 2 1 2 3 4 2h0 1s1 0 1 1l1 1h1c1 1 4 1 5 0 2-2 3-3 4-6z" class="B"></path><path d="M484 134v1 3 1c0 1-1 1-1 2h0l-1 1c-2 1-3 1-5 1-2-1-5-2-6-5h0 1s1 0 1 1l1 1h1c1 1 4 1 5 0 2-2 3-3 4-6z" class="J"></path><path d="M495 439c4-2 8-3 13-2 3 1 6 2 8 3 0 1 1 2 1 2 1 2 3 3 4 5h2c4 8 5 17 2 26 0 2-1 5-3 7h-1-1c-2 3-7 9-11 10v-1l1-1h-1c-2 1-5 2-8 3-6 0-11 0-17-3-6-2-8-6-11-12l-1-3-1-4h0c1-4 2-8 4-12h-1c2-3 6-6 9-8v-1-2c1 0 1-1 2-1 0 1 1 1 1 2v1c1-1 2-3 3-4l1 1 1-1c2 0 5-3 6-4l-2-1z"></path><path d="M520 478v2c-2 3-7 9-11 10v-1l1-1h-1c4-3 8-6 11-10z" class="J"></path><path d="M483 448v-2c1 0 1-1 2-1 0 1 1 1 1 2v1c1-1 2-3 3-4l1 1c-2 3-3 5-3 9 0 5 1 8 4 13-1-1-2-1-2-2 0 1 0 2 1 3 2 1 4 2 7 2h0l-2-1h-1l-3-1 1-1 1 1c2-1 0-1 2-1l-1-1c-2-1-4-4-4-6v-1-1h1l-1 1c1 1 2 3 3 5 2 2 7 3 11 3-2 3-4 5-7 6-3 0-6-1-8-2-5-4-5-9-6-15l-1 1c0 5 0 10-1 15 0 2 0 3-1 4h-4-2-1l-1-3-1-4h0c1-4 2-8 4-12h-1c2-3 6-6 9-8v-1z" class="C"></path><path d="M475 462h1v5c0 1 1 3 0 4h0c0 1 0 2-1 3v-8-4z" class="L"></path><path d="M479 465l-1 2h0c0-4 0-7 2-11 0-1 1-4 3-5h0v4c-1 1-1 0 0 1l-1 1-1 1c-1 1-2 3-2 4v3zm-4-8h-1c2-3 6-6 9-8-1 2-3 4-4 6 0 1-1 2-1 3l-1 1h-1-1v-2zm-1 17h1v-2-6 8c1-1 1-2 1-3 1 1 1 2 1 2h1v-2h1v1l1-1v1h1c0 2 0 3-1 4h-4-2v-2z" class="H"></path><path d="M479 472l1-1v1h1c0 2 0 3-1 4h-4v-1l1-1 1 2c1-2 1-2 1-4z" class="D"></path><defs><linearGradient id="AR" x1="477.701" y1="465.11" x2="482.355" y2="463.874" xlink:href="#B"><stop offset="0" stop-color="#5d5f5b"></stop><stop offset="1" stop-color="#737175"></stop></linearGradient></defs><path fill="url(#AR)" d="M479 465v-3c0-1 1-3 2-4l1-1c0 5 0 10-1 15h-1v-1l-1 1v-1-6z"></path><path d="M483 448v-2c1 0 1-1 2-1 0 1 1 1 1 2v1c1-1 2-3 3-4l1 1c-2 3-3 5-3 9-1 1-1 2-1 4-1-1-1-3-1-4h-1v-2c1-1 1-1 1-2-1-1-1-1-1-2h0-1z" class="H"></path><path d="M475 457v2h1l-1 3v4 6 2h-1v2h-1l-1-3-1-4h0c1-4 2-8 4-12z" class="X"></path><path d="M475 459h1l-1 3v4 6 2h-1c-1-5-1-10 1-15z" class="E"></path><path d="M495 439c4-2 8-3 13-2 3 1 6 2 8 3 0 1 1 2 1 2 1 2 3 3 4 5h2c4 8 5 17 2 26 0 2-1 5-3 7h-1-1v-2c2-4 3-8 3-12s-1-8-5-11l-1-1c-3-1-6-2-10-1-2 0-4 2-5 4s-1 3-1 5v1c-1 0-2 0-3-1v-2-3h-1 0c0 2 0 4 1 5s2 2 3 2h1v1h0c-5-1-8-3-11-7h-1v1 1c0 2 2 5 4 6l1 1c-2 0 0 0-2 1l-1-1-1 1 3 1h1l2 1h0c-3 0-5-1-7-2-1-1-1-2-1-3 0 1 1 1 2 2-3-5-4-8-4-13 0-4 1-6 3-9l1-1c2 0 5-3 6-4l-2-1z" class="U"></path><path d="M499 460c0-2 0-3 1-4l1-3v-1c-2 2-2 3-3 4h0c0-2 1-4 2-5 3-4 6-4 10-4 3 0 4 0 6 2 2 1 4 3 4 4h0c-3-3-6-4-10-4-3 0-5 1-7 3-2 3-3 6-3 9v1h0c-1-1-1-2-1-2z"></path><path d="M499 460s0 1 1 2h0v-1c0-3 1-6 3-9 2-2 4-3 7-3 4 0 7 1 10 4h-1-1c-1-1-1-1-2-1h0v1h1v1c-3-1-6-2-10-1-2 0-4 2-5 4s-1 3-1 5v1c-1 0-2 0-3-1v-2-3h1v3z" class="W"></path><path d="M495 439c4-2 8-3 13-2 3 1 6 2 8 3 0 1 1 2 1 2 1 2 3 3 4 5 3 4 4 8 4 13-3-7-6-13-14-15-3-2-6-1-9 0-2 1-5 4-6 7l-1 2c0 3 1 6 1 8-3-2-6-4-6-8-1-4 1-7 2-10-3 4-3 6-3 11 0 1 0 2 1 3v1 1c0 2 2 5 4 6l1 1c-2 0 0 0-2 1l-1-1-1 1 3 1h1l2 1h0c-3 0-5-1-7-2-1-1-1-2-1-3 0 1 1 1 2 2-3-5-4-8-4-13 0-4 1-6 3-9l1-1c2 0 5-3 6-4l-2-1z"></path><path d="M508 437l8 3c0 1 1 2 1 2-4-2-7-3-11-3v-1h2-1l1-1z" class="a"></path><path d="M495 439c4-2 8-3 13-2l-1 1h1-2v1c-3 0-6 0-9 1l-2-1z" class="W"></path><path d="M141 312c1 1-1 3-2 4-2 3-4 6-5 9-1 2-1 2-1 3s-1 2-1 2l-6 16-3 6c-1 8 1 19 7 23 3 2 6 3 9 2 2 0 5-3 6-5s2-4 2-6l-1-1c-1-1-1-2-2-2-2-2-4 0-7 0l-1-1c-2-6 1-12 3-17 0 2-1 4 0 6s1 3 2 4h1c0-1-1-3-1-3h1c0-1 0-1 1-2 1 1 1 1 1 2l1 1h0l1-1c0-2-2-5-3-7 0-3-1-5-1-8-1-8 6-17 12-23-5 10-8 19-7 30l1-1 1 1h0c0 4 2 8 4 12 2 7 5 17-1 24l-1 2c-2 1-3 2-4 3h0v1h-2-2c-1-1-2 0-3 0h-2-5c-1 0-1-1-2-1h-1c-1-1-3-2-4-3 0-1-1-1-1-2-2-2-4-3-5-6v-1h-1-1l-1 9c-1 0-1 1-2 2 0 1-1 2-1 3l-3 14c-1 4-1 7-2 10l-1-1-1 2v6h1 0c0 5 0 10 1 14 0 3 0 7 2 10l-1 1c1 3 2 6 4 9h0c1 2 2 5 3 6 5 8 13 14 21 17 3 1 6 2 10 2 12 2 25 1 36-6v1c9-5 15-13 20-22 0 2-1 2-1 4h1c-5 13-14 20-26 27-3 1-5 2-8 2l-8 2 1 1h0c-3 1-6 1-9 1-3-1-6 0-8-1h-3l-6-2h-2-1l-1-1c-3 0-7-3-10-4-1-1-2-1-2-2-1 0-1-1-2-1s-1 0-1-1h0l-1-1-2-2v-1l-3-3-3-4c-2-3-4-6-5-9l-1-2-1-2v-1-1l-1-2v-3h0l-1-4c-1-2-1-5-1-8h0v-5l-1-1h0v-9c1-9 3-18 6-27 1-2 1-4 2-6l1-1c1-1 2-6 3-7 1-2 3-4 4-7 2-2 3-5 4-8h0 1c1-2 1-3 2-5l5-15 4-9c1-2 2-2 2-4 0-1 1-3 1-4 3-4 5-8 8-12z"></path><path d="M143 350c1 1 1 1 1 2l1 1h0l-1 1c-1 0-2-1-2-2s0-1 1-2z" class="V"></path><path d="M138 348l2 7-1 1h-1c-2-2-1-5 0-8z" class="C"></path><path d="M118 361h1l-4 8c-1 2-2 5-3 7 0 3-2 6-4 9l-1 1h0l-1-2 1-1c1-1 2-6 3-7 1-2 3-4 4-7 2-2 3-5 4-8h0z" class="N"></path><path d="M203 454h1c-5 13-14 20-26 27-1-1-2-1-2-2l3-1c8-3 16-9 21-17 1-2 2-5 3-7z" class="J"></path><path d="M148 477c12 2 25 1 36-6v1c-2 1-5 3-8 4-8 3-20 5-28 3h-1 1l-1-1 1-1z" class="W"></path><path d="M147 344l1-1 1 1h0c0 4 2 8 4 12 2 7 5 17-1 24l-1 2c-1 0-7 1-7 1l-1-1c4 0 7-2 9-6 2-2 2-4 2-7 0-9-5-16-7-25z" class="H"></path><path d="M108 443c1-1 1 0 1-1l1 1c1 3 2 6 4 9h0c1 2 2 5 3 6 5 8 13 14 21 17 3 1 6 2 10 2l-1 1 1 1h-1c-4-1-8-2-11-3-8-4-16-10-20-17-4-5-6-11-8-16z" class="m"></path><path d="M116 375c1-3 2-5 3-7 1-4 1-8 3-12 0 3-1 5-1 7-1 5 0 10 4 14 1 1 3 3 5 4h0c-1 0-2-1-3-1h-1l-4-4v-1c-1-1-1-1-2-1v-1h-1-1l-1 9c-1 0-1 1-2 2h-1v1c-1 1 0 1-1 1v1 1c0 1-1 2-1 2-1 2-1 3-1 4h-1v-1-1c1 0 1-1 1-1 0-2 1-3 1-5h1v-1-2c1-1 1-2 2-3v-2l1-1v-2z" class="J"></path><path d="M120 374c1 0 1 0 2 1v1l4 4h1c1 0 2 1 3 1h0c2 0 4 1 6 1h6 1l1 1 7-1c-2 1-3 2-4 3h0v1h-2-2c-1-1-2 0-3 0h-2-5c-1 0-1-1-2-1h-1c-1-1-3-2-4-3 0-1-1-1-1-2-2-2-4-3-5-6z" class="R"></path><path d="M98 426h0v-9c1-9 3-18 6-27 1-2 1-4 2-6l1 2h0l1-1c-2 4-3 8-4 13-2 7-3 15-3 23v7 3h-1v1h0v-5c-1 1 0 4-1 5h0v-5l-1-1z" class="S"></path><path d="M108 443v-2c-6-19-3-38 4-56 1-3 2-7 4-10v2l-1 1v2c-1 1-1 2-2 3v2 1h-1c0 2-1 3-1 5 0 0 0 1-1 1v1 1h1c0-1 0-2 1-4 0 0 1-1 1-2v-1-1c1 0 0 0 1-1v-1h1c0 1-1 2-1 3l-3 14c-1 4-1 7-2 10l-1-1-1 2v6h1 0c0 5 0 10 1 14 0 3 0 7 2 10l-1 1-1-1c0 1 0 0-1 1z" class="F"></path><path d="M107 412v1h-1c0-2 1-3 1-4v-1-2-1c1 0 0-1 1-1v-4l1-1c0-1 0-2 1-3h0v5h1c-1 4-1 7-2 10l-1-1-1 2z" class="H"></path><path d="M99 432c1-1 0-4 1-5v5h0v-1h1c1 5 1 10 3 15 3 10 9 19 17 25 12 10 28 13 42 11l13-3c0 1 1 1 2 2-3 1-5 2-8 2l-8 2 1 1h0c-3 1-6 1-9 1-3-1-6 0-8-1h-3l-6-2h-2-1l-1-1c-3 0-7-3-10-4-1-1-2-1-2-2-1 0-1-1-2-1s-1 0-1-1h0l-1-1-2-2v-1l-3-3-3-4c-2-3-4-6-5-9l-1-2-1-2v-1-1l-1-2v-3h0l-1-4c-1-2-1-5-1-8z" class="C"></path><path d="M134 480c1 0 4 1 5 2 5 1 10 1 14 1 6 1 11 0 16-1h0l-2 1c-10 3-24 3-33-3zm-13-3l2 1h1c0 1 0 1 1 1s2 1 3 1h0c0-1-1-1-1-1l-1-1h1c3 1 5 3 8 4 7 4 18 4 27 3l1 1h0c-3 1-6 1-9 1-3-1-6 0-8-1h-3l-6-2h-2-1l-1-1c-3 0-7-3-10-4-1-1-2-1-2-2z"></path><path d="M99 432c1-1 0-4 1-5v5h0c1 7 1 13 4 19 2 5 5 10 8 14 1 2 2 5 4 6s4 3 6 4c1 1 4 2 4 3l1 1s1 0 1 1h0c-1 0-2-1-3-1s-1 0-1-1h-1l-2-1c-1 0-1-1-2-1s-1 0-1-1h0l-1-1-2-2v-1l-3-3-3-4c-2-3-4-6-5-9l-1-2-1-2v-1-1l-1-2v-3h0l-1-4c-1-2-1-5-1-8z" class="M"></path><path d="M199 218l-1-1h4c2-1 7 2 9 3h0c4 3 5 6 8 9h1c-1-2-2-3-3-4 7 7 9 16 9 26 0 2 0 5-1 7-1 3-2 5-3 7-4 8-8 15-16 19v1 1c-6 3-14 5-21 5-2 0-5 1-7 3-6 2-12 7-16 12-2 1-4 2-5 4h0c-1 2-3 3-3 4-6 6-13 15-12 23 0 3 1 5 1 8 1 2 3 5 3 7l-1 1h0l-1-1c0-1 0-1-1-2-1 1-1 1-1 2h-1s1 2 1 3h-1c-1-1-1-2-2-4s0-4 0-6c-2 5-5 11-3 17l1 1c3 0 5-2 7 0 1 0 1 1 2 2l1 1c0 2-1 4-2 6s-4 5-6 5c-3 1-6 0-9-2-6-4-8-15-7-23l3-6 6-16s1-1 1-2 0-1 1-3c1-3 3-6 5-9 1-1 3-3 2-4h1s1 0 1-1c1 0 1-1 2-2 2-3 5-6 7-8l1-1c1-1 5-3 5-5h0c5-4 10-7 15-10h-1c3-1 5-2 8-4l10-4c3-1 5-4 6-7v-4c1 1 1 2 2 3 1-3 1-6 0-9l1-1 1-1h0c1-5-4-10-6-14h0c-1-2-2-3-3-5h1c-1 0-1 0-1-1s-5-3-5-4h-1 0v1h-1v-2h-1 1l3-2c1-1 1-1 2-1l-1-1h-2c0-1-1-1-2-1 0-1-2-1-2-1h-1l1-1h0l-3-2h-1v-1h1c1-1 2-1 3-1s1-1 2-1v-1h6c2 0 2 0 4-1v1c1 0 2-1 3 0-2 1-5 1-7 2-2 0-4 1-6 1v1c2 0 3-1 5-1 3 0 7 0 9-2 0 0 0-1 1-1v-2z" class="I"></path><path d="M131 361c1 0 1-2 2-2 0 1 1 2 1 3s0 1-1 2l-2-3z" class="J"></path><path d="M127 365c2 1 4 2 6 2 1 1 3 1 5 1 0 1 0 2-1 3 0 0 0 1-1 1-5-1-6-3-9-7zm93-136c-1-2-2-3-3-4 7 7 9 16 9 26 0 2 0 5-1 7-1 3-2 5-3 7-4 8-8 15-16 19v1c-5 2-10 3-15 4-4 1-8 1-13 3h-1 0l-1-1c4-2 7-2 11-2 7-1 13-3 19-6 5-3 8-7 12-12 2-4 5-9 6-13 2-10 1-21-4-29z"></path><path d="M128 355c-1 4 0 6 1 9l-1 1h0c-1-1-1-2-1-2 0-1-1-2-1-2-2-10 7-23 12-30h1l1 1c-3 6-3 15-8 20-2 1-3 2-4 3z"></path><path d="M160 298v1c-1 0-2 1-3 1v1l1 1c-6 6-11 12-16 19-3 5-5 10-9 14-3 7-8 15-8 23v1h0c1 5 2 9 7 12 2 2 4 3 7 2 0 1 0 0 1 1h0 1c1-1 2-2 2-3-1-1-1-1-2 0h0v-1l-2-2-2-2c0-1-1-1-2-2 1-1 0-2 0-3l1 1 1 1c3 0 5-2 7 0 1 0 1 1 2 2l1 1c0 2-1 4-2 6s-4 5-6 5c-3 1-6 0-9-2-6-4-8-15-7-23l3-6 6-16s1-1 1-2 0-1 1-3c1-3 3-6 5-9 1-1 3-3 2-4h1s1 0 1-1c1 0 1-1 2-2 2-3 5-6 7-8h1c-1 1-2 2-2 3l7-6c1 1 1 1 2 0z" class="E"></path><path d="M142 366l2 3c1 1 1 2 0 3-1 2-3 4-5 4h-1 0c0-1 0-2 1-3 0 1 0 0 1 1h0 1c1-1 2-2 2-3l-1-5zm-17-8l-1-1c0-4 2-9 3-12 2-5 3-9 6-13v1c0 1-1 2-2 4l1-1 1-1c-3 7-8 15-8 23z"></path><path d="M138 376h-3c-3 0-5-2-7-5s-4-8-3-12c1 5 2 9 7 12 2 2 4 3 7 2-1 1-1 2-1 3h0z" class="M"></path><path d="M135 361l1 1 1 1c3 0 5-2 7 0 1 0 1 1 2 2l-1 1v2l-1 1-2-3 1 5c-1-1-1-1-2 0h0v-1l-2-2-2-2c0-1-1-1-2-2 1-1 0-2 0-3z" class="J"></path><path d="M140 365l2 1h0l1 5c-1-1-1-1-2 0h0v-1c0-1 0-1-1-2v-3z" class="H"></path><path d="M140 365h0v-1h2c1 0 2 1 3 2v2l-1 1-2-3h0l-2-1z" class="F"></path><defs><linearGradient id="AS" x1="126.553" y1="326.782" x2="160.575" y2="311.149" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#1e2026"></stop></linearGradient></defs><path fill="url(#AS)" d="M160 298v1c-1 0-2 1-3 1v1l1 1c-6 6-11 12-16 19-3 5-5 10-9 14l-1 1-1 1c1-2 2-3 2-4v-1c0-2 3-5 4-7l9-15c2-2 4-4 5-6l7-6c1 1 1 1 2 0z"></path><path d="M178 292c5-2 9-2 13-3 5-1 10-2 15-4v1c-6 3-14 5-21 5-2 0-5 1-7 3-6 2-12 7-16 12-2 1-4 2-5 4h0c-1 2-3 3-3 4-6 6-13 15-12 23 0 3 1 5 1 8 1 2 3 5 3 7l-1 1h0l-1-1c0-1 0-1-1-2-1 1-1 1-1 2h-1s1 2 1 3h-1c-1-1-1-2-2-4s0-4 0-6c-2 5-5 11-3 17l-1-1c0 1 1 2 0 3l-1-2c0-1-1-2-1-3-1 0-1 2-2 2v2c-1 0-2-1-2-1 0-1 1-1 1-2 0 0-1-1-1-2h0v-1l-1-2c1-1 2-2 4-3 5-5 5-14 8-20l-1-1h-1c2-4 4-9 7-13 8-10 19-22 31-27l1 1h0 1z" class="C"></path><path d="M128 355c1-1 2-2 4-3 0 2 0 2-1 3l-2 2-1-2z" class="H"></path><path d="M134 354v2 3h0c0 1 0 2 1 2 0 1 1 2 0 3l-1-2c0-1-1-2-1-3 0-2 0-3 1-5z" class="B"></path><path d="M140 346l1-2h1v3c0 1 1 2 1 3h0c-1 1-1 1-1 2h-1v-3c-1-1-1-2-1-3z" class="F"></path><path d="M129 358c1 1 1 1 1 2h0 1v-1c0-1 1-1 1-2 0-2 0-3 1-4h1v1c-1 2-1 3-1 5-1 0-1 2-2 2v2c-1 0-2-1-2-1 0-1 1-1 1-2 0 0-1-1-1-2z" class="E"></path><path d="M139 345v-2-1-1c1-1 1-1 1-2v-1l1 1v2c-1 0 0 0-1 1v4c0 1 0 2 1 3v3s1 2 1 3h-1c-1-1-1-2-2-4s0-4 0-6z" class="N"></path><path d="M176 291l1 1h0 1c-12 7-24 19-33 31 0-3 4-5 4-8-4 4-7 10-10 16h0-1c2-4 4-9 7-13 8-10 19-22 31-27z" class="M"></path><path d="M199 218l-1-1h4c2-1 7 2 9 3h0c4 3 5 6 8 9 3 8 5 14 4 23 0 2 0 5-1 7-2 5-4 9-7 14-1 1-2 3-3 4-2 3-6 5-9 6-4 2-9 3-13 4-2 0-4 1-6 1-8 0-17 7-22 11l-4 3-1-1v-1c1 0 2-1 3-1v-1c-1 1-1 1-2 0l-7 6c0-1 1-2 2-3h-1l1-1c1-1 5-3 5-5h0c5-4 10-7 15-10h-1c3-1 5-2 8-4l10-4c3-1 5-4 6-7v-4c1 1 1 2 2 3l-2 4v1l1 1h0c2-2 4-4 6-5l-1 1h1c-1 1-2 3-3 4v1h0c1 1 1 0 1 1 6-3 9-7 13-12l1 2c2-4 5-9 6-14 1-9-2-19-8-26-1-2-4-4-6-5-3-3-5-3-8-4z" class="f"></path><path d="M199 218l-1-1h4c2-1 7 2 9 3h0l-1 1c1 1 1 1 2 1l4 4c0 2 1 2 2 4h-1c0-1-1-1-1-2l-2-2-1 1c-1-2-4-4-6-5-3-3-5-3-8-4z" class="X"></path><path d="M182 285l1 1h4c0-1 0-1 1-1h1c0-1 1-1 2-1l1-1h1c1 0 1-1 1 0l1-1h1c2-1 5-2 7-3 1 0 1-1 2-1l1-1h1v1h-1c-2 2-5 3-7 4-1 1-3 1-4 1l-1 1c-2 1-5 2-7 2l-1 1h0c-1 0-1 0-2 1-8 0-17 7-22 11l-4 3-1-1v-1c1 0 2-1 3-1v-1c-1 1-1 1-2 0 7-6 15-10 24-13z" class="d"></path><path d="M160 298c2-2 4-3 6-5 0 0 1-1 2-1v1c-1 1-2 2-4 3 0 1-2 1-3 2v1h1l-4 3-1-1v-1c1 0 2-1 3-1v-1z" class="S"></path><defs><linearGradient id="AT" x1="191.445" y1="285.088" x2="183.176" y2="276.709" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#222823"></stop></linearGradient></defs><path fill="url(#AT)" d="M214 265l1 2c-3 5-7 9-13 11-1 0-2 1-4 1-5 3-11 4-16 6-9 3-17 7-24 13l-7 6c0-1 1-2 2-3h-1l1-1c1-1 5-3 5-5h0c5-4 10-7 15-10h-1c3-1 5-2 8-4l10-4c3-1 5-4 6-7v-4c1 1 1 2 2 3l-2 4v1l1 1h0c2-2 4-4 6-5l-1 1h1c-1 1-2 3-3 4v1h0c1 1 1 0 1 1 6-3 9-7 13-12z"></path><path d="M196 270v-4c1 1 1 2 2 3l-2 4v1l1 1h0c2-2 4-4 6-5l-1 1h1c-1 1-2 3-3 4-6 3-12 4-18 6-3 1-5 3-9 4h-1c3-1 5-2 8-4l10-4c3-1 5-4 6-7z" class="F"></path><path d="M214 265l1 2c-3 5-7 9-13 11-1 0-2 1-4 1-5 3-11 4-16 6-9 3-17 7-24 13l-7 6c0-1 1-2 2-3 5-4 10-9 16-12 11-5 22-7 32-12 6-3 9-7 13-12z" class="V"></path><path d="M199 218c3 1 5 1 8 4 2 1 5 3 6 5 6 7 9 17 8 26-1 5-4 10-6 14l-1-2c-4 5-7 9-13 12 0-1 0 0-1-1h0v-1c1-1 2-3 3-4h-1l1-1c-2 1-4 3-6 5h0l-1-1v-1l2-4c1-3 1-6 0-9l1-1 1-1h0c1-5-4-10-6-14h0c-1-2-2-3-3-5h1c-1 0-1 0-1-1s-5-3-5-4h-1 0v1h-1v-2h-1 1l3-2c1-1 1-1 2-1l-1-1h-2c0-1-1-1-2-1 0-1-2-1-2-1h-1l1-1h0l-3-2h-1v-1h1c1-1 2-1 3-1s1-1 2-1v-1h6c2 0 2 0 4-1v1c1 0 2-1 3 0-2 1-5 1-7 2-2 0-4 1-6 1v1c2 0 3-1 5-1 3 0 7 0 9-2 0 0 0-1 1-1v-2z" class="g"></path><defs><linearGradient id="AU" x1="209.273" y1="239.001" x2="212.946" y2="231.66" xlink:href="#B"><stop offset="0" stop-color="#16151b"></stop><stop offset="1" stop-color="#303230"></stop></linearGradient></defs><path fill="url(#AU)" d="M209 229c3 4 6 7 7 11v1 1h1v4h-1l-1-1c-1-7-4-11-10-16h4z"></path><path d="M188 232h0c1-1 4 0 5 1 2 1 5 3 7 5 1 1 1 2 2 3l3 8v1h0l-2-5c-3-5-9-10-15-12v-1z" class="X"></path><path d="M206 249c0-2 0-4-1-6-2-4-5-7-9-10-1 0-2-1-4-1 0-1-2-1-2-2h0c2 0 3 0 4 1 8 5 12 10 14 19 2 7-1 13-5 20-2 1-4 3-6 5h0c1-2 2-3 3-5 2-1 3-2 4-4 3-5 4-11 2-17zm3-3c-3-6-7-11-13-15-1-1-3-2-4-2 0-1 1-2 2-2 4 0 7 2 9 4 3 3 6 6 7 10-1 1-1 1-1 3l1 1v1h-1 0z" class="Y"></path><path d="M183 233h1 3s0-1 1-1v1c6 2 12 7 15 12l2 5h0l1-1c2 6 1 12-2 17-1 2-2 3-4 4l1-1c1-1 3-5 4-6 1-3 1-8 0-11-1-2-2-4-3-5 0-1-1-2-1-3l-2-2s-1 0-2-1v1l-6-4c0-1-5-3-5-4h-1 0v1h-1v-2h-1z" class="I"></path><path d="M183 233h1 3s0-1 1-1v1l-1 1c5 2 9 4 12 8 0 0-1 0-2-1v1l-6-4c0-1-5-3-5-4h-1 0v1h-1v-2h-1z" class="c"></path><path d="M191 238l6 4v-1c1 1 2 1 2 1l2 2c0 1 1 2 1 3 1 1 2 3 3 5 1 3 1 8 0 11-1 1-3 5-4 6l-1 1c-1 2-2 3-3 5l-1-1v-1l2-4c1-3 1-6 0-9l1-1 1-1h0c1-5-4-10-6-14h0c-1-2-2-3-3-5h1c-1 0-1 0-1-1z" class="W"></path><path d="M197 242v-1c1 1 2 1 2 1l2 2c0 1-1 2-1 3-1-2-2-3-3-5z" class="k"></path><path d="M192 239c3 2 5 4 6 7l1 3c-1-2-3-4-5-5-1-2-2-3-3-5h1z" class="D"></path><path d="M194 244c2 1 4 3 5 5s1 4 2 6c0 5-1 12-3 16l-2 3v-1l2-4c1-3 1-6 0-9l1-1 1-1h0c1-5-4-10-6-14h0z" class="d"></path><path d="M201 244c0 1 1 2 1 3 1 1 2 3 3 5 1 3 1 8 0 11-1 1-3 5-4 6l2-6c1-1 1-2 1-3h-1v3c-1-2-1-6-1-8s0-3-1-5l-1-3c0-1 1-2 1-3z"></path><path d="M201 244c0 1 1 2 1 3-1 1-1 2-1 3l-1-3c0-1 1-2 1-3z" class="M"></path><path d="M205 231h0c2 0 5 4 6 6 2 3 3 6 3 9 1 1 1 2 2 3v8c0 3-2 6-2 8-4 5-7 9-13 12 0-1 0 0-1-1h0v-1c1-1 2-3 3-4 2-2 3-4 4-5 3-6 4-13 2-20h0 1v-1l-1-1c0-2 0-2 1-3-1-4-4-7-7-10h2z"></path><path d="M203 231h2c5 5 8 10 8 16v3h-1c0-3-1-6-2-9-1-4-4-7-7-10z" class="W"></path><path d="M213 247l1 2c1 1 1 2 1 3-1 5-2 10-4 14-3 4-6 7-11 9 2-2 5-3 7-6 3-3 6-9 6-15l-1-4h1v-3z" class="T"></path><path d="M199 218c3 1 5 1 8 4 2 1 5 3 6 5 6 7 9 17 8 26-1 5-4 10-6 14l-1-2c0-2 2-5 2-8v-8c-1-1-1-2-2-3h2 1v-4h-1v-1-1c-1-4-4-7-7-11h-4l-2-1-7-3c-2 0-3 1-5 2-1 1-2 2-2 3l-1-1h-2c0-1-1-1-2-1 0-1-2-1-2-1h-1l1-1h0l-3-2h-1v-1h1c1-1 2-1 3-1s1-1 2-1v-1h6c2 0 2 0 4-1v1c1 0 2-1 3 0-2 1-5 1-7 2-2 0-4 1-6 1v1c2 0 3-1 5-1 3 0 7 0 9-2 0 0 0-1 1-1v-2z" class="I"></path><path d="M217 251l2-1c0 4 0 9-2 12v-3-8z" class="M"></path><path d="M216 240l1 1c0 1 1 2 1 3l1 6-2 1v-5-4h-1v-1-1z"></path><path d="M216 246h1v5 8s0-1-1-2v-8c-1-1-1-2-2-3h2z" class="H"></path><path d="M190 220c2 0 2 0 4-1v1c1 0 2-1 3 0-2 1-5 1-7 2-2 0-4 1-6 1v1c2 0 3-1 5-1 3 0 7 0 9-2 0 0 0-1 1-1l6 3c3 2 5 5 8 7-2-4-6-7-10-9l-1-1c3 1 9 5 10 7 3 3 7 11 6 15v2c0-1-1-2-1-3h0c1-1-1-4-2-6-2-3-4-6-8-9-2-1-3-3-6-3-3-1-5-1-8 0-3 2-5 1-9 2h0c-1-1-2-1-3-2h-2c1-1 2-1 3-1s1-1 2-1v-1h6z" class="S"></path><path d="M182 222c1 0 1-1 2-1v-1h6c-2 2-5 1-7 3h-2-2c1-1 2-1 3-1z" class="D"></path><path d="M181 223c1 1 2 1 3 2h0c4-1 6 0 9-2 3-1 5-1 8 0 3 0 4 2 6 3 4 3 6 6 8 9 1 2 3 5 2 6h0l-1-1c-1-4-4-7-7-11h-4l-2-1-7-3c-2 0-3 1-5 2-1 1-2 2-2 3l-1-1h-2c0-1-1-1-2-1 0-1-2-1-2-1h-1l1-1h0l-3-2h-1v-1h1 2z" class="E"></path><path d="M182 226c1 0 1 0 2 1h2 1 0 2 2c-1 1-2 2-2 3l-1-1h-2c0-1-1-1-2-1 0-1-2-1-2-1h-1l1-1z" class="J"></path><path d="M196 225l-1-1c1 0 3-1 4-1 3 0 7 3 9 5h0l1 1h-4l-2-1-7-3z" class="Y"></path><path d="M203 228h2c1 0 2 1 3 0l1 1h-4l-2-1z" class="l"></path><path d="M427 355c0-4 1-8 2-12v-1c-1 1-1 2-2 3 0-1 0-2 1-3h0c1-2 2-5 4-7 2-3 6-6 9-7l-3 3c-4 3-7 7-8 12l4-6c3-5 7-8 13-9h0l1 1c-4 3-8 5-11 10-1 1-2 3-3 4l-2 4v3h0c1 0 1-1 1-1 0-2 1-3 2-5v-1 1l3-4v2h0c1-1 2-2 3-2h0l2-3c2-2 5-5 8-5l2 1c1-1 2-1 3-1 4-1 9-1 13 0 2 0 5 1 7 2l-1 1h0c0 1 1 1 2 2v1h0c1 1 1 2 2 2s2 1 3 2h0l2 2h1v-1c2 3 2 5 3 8v6l1 1v3c0-2 0-5 1-7 0-2 0-4-1-7l2 2-1-4c0-1 0-1 1-1v-1h0c-1 0-2-2-2-3h0l1 1h1v-2h0 3l1 1c2 2 5 4 6 6h-1l2 2-1 1c0 1 1 2 2 3 1 2 2 3 3 5v9c0 1-1 1-1 2h1v1c0 2-1 4-2 6-3 5-9 9-14 11-7 2-14 2-21 2s-14 0-21-3c-10-6-18-16-21-27 0-1-1-2 0-3z"></path><path d="M503 352c1 2 2 3 3 5l-1 1v3c0-1 0-2-1-3v-1c-1-2-1-3-2-5h1z" class="F"></path><path d="M501 352c1 2 1 3 2 5v2h0v4c0 1 0 5-1 6-1-2 0-4 0-6 0-3-1-7-2-10 0 0 1 0 1-1z" class="E"></path><path d="M427 358c0-1-1-2 0-3 3 11 9 19 18 26 9 5 18 7 28 6h9c8-2 16-5 21-12l1-1h0c1-2 1-3 1-5h-1l-1 1v-1c-1-1 1-3 1-4s0-3 1-4v-3l1-1v9c0 1-1 1-1 2h1v1c0 2-1 4-2 6-3 5-9 9-14 11-7 2-14 2-21 2s-14 0-21-3c-10-6-18-16-21-27z" class="U"></path><path d="M491 343c2 4 2 9 3 13h0c-1 3-1 5-1 8 0 4-4 9-8 12-1 1-4 3-6 3v-1l1-1-1-1c5-5 8-9 10-15 0-2 0-5 1-7 0-2 0-4-1-7l2 2-1-4c0-1 0-1 1-1v-1z" class="J"></path><path d="M489 347l2 2 1 5v3 4c-1 6-5 10-10 14l-2 2h0l-1-1c5-5 8-9 10-15 0-2 0-5 1-7 0-2 0-4-1-7z" class="Y"></path><path d="M489 347l2 2 1 5v3c-1 3-2 7-4 9 1-2 2-4 2-6v-6c0-2 0-4-1-7z" class="I"></path><path d="M491 339h0 3l1 1c2 2 5 4 6 6h-1l2 2-1 1c0 1 1 2 2 3h-1-1c0 1-1 1-1 1v1 1c1 3 1 7-1 9l-1 1c-1 0-2-1-2-2h-1 0c-1-1-1-5-1-6v-1h0c-1-4-1-9-3-13h0c-1 0-2-2-2-3h0l1 1h1v-2z" class="K"></path><path d="M491 339h0 3l1 1v1c1 1 1 2 2 3h0l-1 1v1c-1-1-2-3-3-3 0 1 1 1 1 2l-1 1-2-3h0c-1 0-2-2-2-3h0l1 1h1v-2z" class="D"></path><path d="M491 339h3l1 1v1c1 1 1 2 2 3h0c-2-1-4-3-6-5z" class="b"></path><path d="M491 343h0l2 3h0c1 1 2 3 3 4 1 3 1 9 1 13-1-2-2-5-2-7h-1c-1-4-1-9-3-13h0z"></path><path d="M495 363l1-1h0l1 2c1-1 2-1 2-2s1-5 0-6v-2c0-1 0-2-1-3v-1c0-1 0-2-1-3h0l1 1 1-1h0c1 0 2 1 2 2s1 2 2 3h-1-1c0 1-1 1-1 1v1 1c1 3 1 7-1 9l-1 1c-1 0-2-1-2-2h-1z" class="J"></path><path d="M447 328l1 1c-4 3-8 5-11 10-1 1-2 3-3 4l-2 4v3h0c1 0 1-1 1-1 0-2 1-3 2-5v-1 1l3-4v2s-1 2 0 2l-2 7c0 1 1 3 1 5 1 0 1 0 2 1 0 1 0 3 1 4 2 5 7 9 12 11 2 0 3 1 5 0h0c2-1 3 0 4 0 2 0 4 0 6-1h-1 0l-1-1c2 0 3-1 5-2l1-1h1c2-3 7-8 7-11 0-1 1-2 1-3h0l1-1v-2h1 0 0c0-2 0-4 1-6l-1-1v-1l2 2h1v-1c2 3 2 5 3 8v6l1 1v3c-2 6-5 10-10 15-1 0-2 1-4 2v1c-7 3-16 4-23 1-8-2-15-8-19-16-3-5-3-13-1-19 3-7 8-13 15-17z" class="V"></path><path d="M482 342l2 2c1 2 1 5 2 8 0 1-1 4 0 5v3c-1 3-2 6-4 9-1 1-5 4-5 5-3 2-7 4-10 4-3 1-5 1-7 1h0 1l4-1c4-1 10-3 13-7l-1-1c0-1 2-2 3-2v-1c1-1 2-2 2-3h0c0-1 1-1 1-2v-2-2c0-1 0-2-1-3l-1 4v-2c1-2 0-2 0-3l-1-1 1-1v-2h1 0 0c0-2 0-4 1-6l-1-1v-1z" class="S"></path><path d="M483 344c2 4 1 10 0 14 0-1 0-2-1-3l-1 4v-2c1-2 0-2 0-3l-1-1 1-1v-2h1 0 0c0-2 0-4 1-6z" class="R"></path><path d="M482 350h0v5l-1 4v-2c1-2 0-2 0-3l-1-1 1-1v-2h1 0z" class="X"></path><path d="M485 343c2 3 2 5 3 8v6l1 1v3c-2 6-5 10-10 15-1 0-2 1-4 2v-1l-5 2h0c0-1 2-1 3-2l2-1c1-1 2-1 2-2s4-4 5-5c2-3 3-6 4-9v-3c-1-1 0-4 0-5-1-3-1-6-2-8h1v-1z" class="F"></path><path d="M485 343c2 3 2 5 3 8v6c0 4-1 6-3 9h0c1-3 2-5 2-8 1-4 0-11-2-14v-1z" class="f"></path><path d="M484 344h1c2 3 3 10 2 14l-1 2v-3c-1-1 0-4 0-5-1-3-1-6-2-8z" class="Q"></path><path d="M438 340v2s-1 2 0 2l-2 7c0 1 1 3 1 5 1 0 1 0 2 1 0 1 0 3 1 4 2 5 7 9 12 11 2 0 3 1 5 0h0c2-1 3 0 4 0 2 0 4 0 6-1h-1 0l-1-1c2 0 3-1 5-2l1-1h1c2-3 7-8 7-11 0-1 1-2 1-3h0l1 1c0 1 1 1 0 3v2l1-4c1 1 1 2 1 3v2 2c0 1-1 1-1 2h0c0 1-1 2-2 3v1c-1 0-3 1-3 2-1 2-2 3-4 4-6 3-15 5-22 3-6-2-11-6-14-12-4-7-4-14-2-21l3-4z"></path><path d="M480 353l1 1c0 1 1 1 0 3v2l1-4c1 1 1 2 1 3v2l-2 4c-3 5-9 8-15 9-6 2-12 1-18-2-6-4-9-9-11-15 1 0 1 0 2 1 0 1 0 3 1 4 2 5 7 9 12 11 2 0 3 1 5 0h0c2-1 3 0 4 0 2 0 4 0 6-1h-1 0l-1-1c2 0 3-1 5-2l1-1h1c2-3 7-8 7-11 0-1 1-2 1-3h0z" class="E"></path><path d="M480 353l1 1c0 1 1 1 0 3v2c0 3-3 8-6 10-4 3-9 3-14 4v-1c2 0 4 0 6-1h-1 0l-1-1c2 0 3-1 5-2l1-1h1c2-3 7-8 7-11 0-1 1-2 1-3h0z" class="b"></path><path d="M480 353l1 1c-1 2-1 5-3 7 0 2-1 3-2 4l-3 3c-1 0-2 1-3 0l1-1h1c2-3 7-8 7-11 0-1 1-2 1-3h0z" class="M"></path><path d="M456 332c4-1 9-1 13 0 2 0 5 1 7 2l-1 1h0c0 1 1 1 2 2v1h0c1 1 1 2 2 2s2 1 3 2h0v1l1 1c-1 2-1 4-1 6h0 0-1v2l-1 1h0c0 1-1 2-1 3 0 3-5 8-7 11h-1l-1 1c-2 1-3 2-5 2l1 1h0 1c-2 1-4 1-6 1-1 0-2-1-4 0h0c-2 1-3 0-5 0-5-2-10-6-12-11-1-1-1-3-1-4-1-1-1-1-2-1 0-2-1-4-1-5l2-7c-1 0 0-2 0-2h0c1-1 2-2 3-2h0l2-3c2-2 5-5 8-5l2 1c1-1 2-1 3-1z" class="F"></path><path d="M456 340c5-2 9-2 13 0 2 0 3 1 3 2l1 1c-1 0-2-1-2-1l-1-1c-3-1-5-2-8-2-1 0-3 1-4 1h0c-3 1-6 3-7 6h0-1 0c1-2 2-4 3-5l3-1z" class="D"></path><path d="M456 339c4-3 9-3 14-1l2 1c0 1 0 2 1 2l-1 1c0-1-1-2-3-2-4-2-8-2-13 0v-1z" class="J"></path><path d="M458 340h0c1 0 3-1 4-1 3 0 5 1 8 2l1 1s1 1 2 1 1 1 2 2l-1 1h0c1 2 1 3 2 4h0v5h-2v-2c0-2 0-4-1-6l-1-1v-1c-1-1-4-3-6-3h0c-3-1-5 0-7 0h-2l-1 1c-1 0-1 1-2 1h-1l1-1c0-1 3-2 5-2h0v-1h-1z" class="N"></path><path d="M454 344c1 0 1-1 2-1l1-1h2c2 0 4-1 7 0h0c2 0 5 2 6 3v1l1 1-1 1 1 2h-1 0c0 2 1 4 0 6h-1c0-2 1-3 0-5s-2-3-3-4c-2-2-5-2-8-2-2 0-3 0-5 1-1 0-1 1-1 1h-1-1c1-1 1-2 2-3z" class="Q"></path><path d="M473 350l-1-2 1-1c1 2 1 4 1 6v2h2c-2 5-3 8-7 11-4 2-6 2-10 2h0c-2 0-4 0-6-1 1 0 1-1 1-1 1 0 2 0 3 1h1c0-1 0-1 1-2h1c-1-1-2-1-3-1h6l3-2c2-1 3-2 4-4s1-5 1-7c1 2 0 3 0 5h1c1-2 0-4 0-6h0 1z" class="B"></path><path d="M473 350l-1-2 1-1c1 2 1 4 1 6 0 1 0 2-1 4v-7z" class="I"></path><path d="M474 353v2h2c-2 5-3 8-7 11h-1c0-2 1-3 2-4l1-1c0-2 1-4 2-4 1-2 1-3 1-4z" class="R"></path><path d="M466 362c1 0 2 0 2 1s0 1-1 2c-1 0-2 1-3 1h0-3 0v2h-2c-2 0-4 0-6-1 1 0 1-1 1-1 1 0 2 0 3 1h1c0-1 0-1 1-2h1c-1-1-2-1-3-1h6l3-2z" class="H"></path><path d="M466 362c1 0 2 0 2 1s0 1-1 2v-1h-1v-1c-1 1-2 1-3 1l3-2z" class="Q"></path><path d="M472 339c2 1 3 2 4 3l1-1h1 1v-1c1 0 2 1 3 2h0v1l1 1c-1 2-1 4-1 6h0 0-1v2l-1 1h0c0 1-1 2-1 3 0 3-5 8-7 11h-1s0-1-1-1c-3 2-7 4-11 4v-2c4 0 6 0 10-2 4-3 5-6 7-11v-5h0c-1-1-1-2-2-4h0l1-1c-1-1-1-2-2-2l-1-1 1-1c-1 0-1-1-1-2z" class="B"></path><path d="M479 340c1 0 2 1 3 2h0v1l1 1c-1 2-1 4-1 6h0 0-1c0-1 0-2-1-3v2h-1c0-3-1-4-3-7l1-1h1 1v-1z" class="M"></path><path d="M479 340c1 0 2 1 3 2h0v1l1 1c-1 2-1 4-1 6h0v-2c0-3-1-5-3-7v-1z" class="D"></path><path d="M476 355v-5h0c-1-1-1-2-2-4h0l1-1c2 5 3 9 1 14-1 3-3 5-5 6l-1 1c-3 2-7 4-11 4v-2c4 0 6 0 10-2 4-3 5-6 7-11z" class="l"></path><path d="M456 332c4-1 9-1 13 0 2 0 5 1 7 2l-1 1h0c0 1 1 1 2 2v1h0c1 1 1 2 2 2v1h-1-1l-1 1c-1-1-2-2-4-3l-2-1c-5-2-10-2-14 1-3 1-7 4-7 7l-1 3c-1-1-1-1-1-2l3-7-1-1 2-2h1l1-1c1 0 1 0 1-1h0c1-1 2-1 3-2h1v-1h-2z" class="f"></path><path d="M468 333l2 1v-1c2 1 4 1 5 2 0 1 1 1 2 2v1h0l-9-3v-2z" class="J"></path><path d="M454 335c1 0 2 0 3-1 4-1 7-1 11-1v2c-5-1-11-1-15 3-2 0-3 1-3 2l-1-1 2-2h1l1-1c1 0 1 0 1-1z" class="V"></path><path d="M456 332c4-1 9-1 13 0 2 0 5 1 7 2l-1 1h0c-1-1-3-1-5-2v1l-2-1c-4 0-7 0-11 1-1 1-2 1-3 1h0c1-1 2-1 3-2h1v-1h-2z" class="Q"></path><path d="M469 332c2 0 5 1 7 2l-1 1h0c-1-1-3-1-5-2l-4-1h0 3z" class="E"></path><path d="M453 344h1c-1 1-1 2-2 3h1 1s0-1 1-1c2-1 3-1 5-1 3 0 6 0 8 2 1 1 2 2 3 4 0 2 0 5-1 7s-2 3-4 4l-3 2h-6c-2 0-4-1-6-2-3-2-4-4-4-7v-2l1-1c1 3 2 5 6 7h1l-2-2-3-3v-1-3-1c0-2 3-4 3-5z"></path><path d="M453 344h1c-1 1-1 2-2 3h1 1s0-1 1-1c2-1 3-1 5-1-1 1-3 1-4 2h0c-1 2-1 4-1 5l3 3h3c0-1 0-1 1-2h-1c1-1 1-1 2-1v-1h-2l-1 2v-3h1c2 0 3 0 4 1v4h0c-1 2-2 2-3 4-2 0-5 1-7 0l-2-2-3-3v-1-3-1c0-2 3-4 3-5z" class="E"></path><path d="M454 347s0-1 1-1c2-1 3-1 5-1-1 1-3 1-4 2h0-1v1c-1 1-2 2-1 4v1h0-1c0-3 0-4 1-6z" class="B"></path><path d="M453 344h1c-1 1-1 2-2 3-1 2 0 4-1 6h1c1 1 2 1 2 2 1 0 1 1 2 1 0 0 1-1 1 0h3 1c0-1 1-1 1-1 1 0 1-1 2-1h0v1h1c-1 2-2 2-3 4-2 0-5 1-7 0l-2-2-3-3v-1-3-1c0-2 3-4 3-5z" class="K"></path><path d="M451 353h1c1 1 2 1 2 2 1 0 1 1 2 1 0 0 1-1 1 0h3 1c0-1 1-1 1-1v2c-1 0-1 0-1 1h-1c-1 0-2 0-3-1h-1l-1 1-2-2s-1 0-1-1v-1c-1 0 0 0-1-1z" class="H"></path><path d="M456 332h2v1h-1c-1 1-2 1-3 2h0c0 1 0 1-1 1l-1 1h-1l-2 2 1 1-3 7c0 1 0 1 1 2v3l-1 1v2c0 3 1 5 4 7 2 1 4 2 6 2 1 0 2 0 3 1h-1c-1 1-1 1-1 2h-1c-1-1-2-1-3-1 0 0 0 1-1 1 2 1 4 1 6 1h0v2c4 0 8-2 11-4 1 0 1 1 1 1l-1 1c-2 1-3 2-5 2l1 1h0 1c-2 1-4 1-6 1-1 0-2-1-4 0h0c-2 1-3 0-5 0-5-2-10-6-12-11-1-1-1-3-1-4-1-1-1-1-2-1 0-2-1-4-1-5l2-7c-1 0 0-2 0-2h0c1-1 2-2 3-2h0l2-3c2-2 5-5 8-5l2 1c1-1 2-1 3-1z" class="I"></path><path d="M441 340h0l2-3c2-2 5-5 8-5l2 1-1 1-1-1h-1c-5 3-9 7-11 12l-1 2v-2h0v-1c-1 0 0-2 0-2h0c1-1 2-2 3-2z" class="k"></path><path d="M438 344v1h0v2 5c0 3 3 8 4 11 3 4 9 8 15 9h0c-2 1-3 0-5 0-5-2-10-6-12-11-1-1-1-3-1-4-1-1-1-1-2-1 0-2-1-4-1-5l2-7z" class="M"></path><path d="M459 370h0c-2-1-4-1-5-2h-1 0c1 1 1 2 2 2h2l-1 1-1-1c-5-1-10-4-12-8-3-5-5-10-3-15 1-5 4-9 9-12l2-2 1 1h0c-1 1-4 3-6 4-3 3-5 6-6 10v4l1 1v3c1 1 1 2 2 3 0 1 1 3 2 4h1 1l6 4c2 1 4 1 6 1h0v2z" class="P"></path><path d="M456 332h2v1h-1c-1 1-2 1-3 2h0c0 1 0 1-1 1l-1 1h-1l-2 2v1l-1 1h-1 0l1-1-1-1c0 1-1 1-1 2-1 1-2 2-2 4h-1v2c-1 1-1 4-1 5 1 1 0 2 1 3v1c1 1 2 3 3 4v2l1 1h-1-1c-1-1-2-3-2-4-1-1-1-2-2-3v-3l-1-1v-4c1-4 3-7 6-10 2-1 5-3 6-4h0l1-1c1-1 2-1 3-1z" class="H"></path><path d="M449 339l1 1-3 7c0 1 0 1 1 2v3l-1 1v2c0 3 1 5 4 7 2 1 4 2 6 2 1 0 2 0 3 1h-1c-1 1-1 1-1 2h-1c-1-1-2-1-3-1 0 0 0 1-1 1l-6-4-1-1v-2c-1-1-2-3-3-4v-1c-1-1 0-2-1-3 0-1 0-4 1-5v-2h1c0-2 1-3 2-4 0-1 1-1 1-2l1 1-1 1h0 1l1-1v-1z" class="F"></path><path d="M447 355c-1-2-1-5 0-8 0 1 0 1 1 2v3l-1 1v2z" class="M"></path><path d="M273 203v-2h1l1 3v-1c0 1 1 4 1 4 1 6 3 12 4 17 1 2 3 5 3 7l1 1c1 1 2 3 3 4s2 2 2 3l5 5 5 4c2 2 3 4 5 6h1c1 0 1 0 1-1 3 3 5 6 6 9 3 7 3 14 3 21-1 1-1 2-1 3-2 6-7 11-12 14 0-1-1 0-1-1v-1c-1 0-2 0-2 1-5 3-12 3-18 3h-3l-1 1h-2c-3-1-7-2-11-3-3-2-6-3-9-6-5-3-10-7-12-12-2-3-3-6-4-9 0-4-1-8-1-12l1-2c0-2 1-3 1-5l1-2v-2-1c1-1 3-3 5-4 0 0 1 1 2 1 2-1 4-2 6-2 1 0 3 0 5 1v-1c1 0 2-1 3-1l2-4c1-1 1-2 1-3h-1 0l-1-1c1-1 3-3 4-5-1 0-2 1-2 1v-2c1 0 0 0 0-1 1 0 4-3 4-3l-1-1c5-5 5-10 6-17l-1-4z"></path><path d="M268 271l7-1-3 2h-1 0-5c1 0 1 0 2-1z" class="D"></path><path d="M240 254h1v2c0 2-1 5-1 8v-4c-1 0-1 1-2 1l1-2c0-2 1-3 1-5z" class="I"></path><path d="M259 245h0c1 0 5-1 6 0-1 1-1 2-2 2-1 1-2 1-4 1v-3zm45 31c1 1 2 3 2 4 0 2-1 3-2 4h-1v-1h1l-1-1h0c1-2 1-3 0-5l1-1z" class="J"></path><path d="M264 239c1-1 2-1 2-1v-1l1 1c1 0 1-1 1-1 0-1 1-1 1-1 0 2-2 5-3 6l-4 1 2-4zm35 41h1v-3h-1v-1h1c0 1 1 1 1 2h1v2h-1s0 1-1 1h0l2 1h1l1 1h-1v1c0 1-2 1-3 1s-2 0-3-1-2-3-2-4h1s1 1 1 2c1 0 2-1 2-2h0z" class="C"></path><path d="M252 278c7 0 14-2 20-4l1 1c-1 1-2 2-4 3-5 2-12 4-18 1l1-1z" class="R"></path><path d="M248 246c2-1 4-2 6-2 1 0 3 0 5 1v3c-4-1-10-1-14 2-2 1-3 3-4 6v-2h-1l1-2v-2-1c1-1 3-3 5-4 0 0 1 1 2 1z" class="E"></path><path d="M241 249c1-1 3-3 5-4 0 0 1 1 2 1-3 2-5 4-7 6v-2-1z" class="d"></path><path d="M238 261c1 0 1-1 2-1v4c1 5 4 10 9 13 1 1 2 1 3 1l-1 1c-6-3-9-6-12-13 2 11 7 20 15 26 7 6 15 8 24 10l-1 1h-2c-3-1-7-2-11-3-3-2-6-3-9-6-5-3-10-7-12-12-2-3-3-6-4-9 0-4-1-8-1-12z" class="V"></path><defs><linearGradient id="AV" x1="287.296" y1="273.278" x2="291.705" y2="285.176" xlink:href="#B"><stop offset="0" stop-color="#6e6d6d"></stop><stop offset="1" stop-color="#949597"></stop></linearGradient></defs><path fill="url(#AV)" d="M281 288v-4c0-4 2-8 5-10 2-2 5-3 9-3 3 1 7 2 9 5l-1 1c1 2 1 3 0 5h0-1l-2-1h0c1 0 1-1 1-1h1v-2h-1c0-1-1-1-1-2h-1v1h1v3h-1c0-2-2-5-4-6-2 0-4 0-6 1-3 2-5 5-6 9 0 3 0 5 2 8-2-1-2-3-4-4z"></path><path d="M302 278l-3-6c1 2 3 3 4 5s1 3 0 5h0-1l-2-1h0c1 0 1-1 1-1h1v-2z"></path><path d="M255 264c2 0 3-1 5-1 1 0 1 2 2 3s1 2 3 3c1 1 2 2 3 2-1 1-1 1-2 1h5 0l1 1v1c-6 0-13 2-18-2-1-1-2-2-2-4 0-1-1-2 0-3 1 0 1 0 1-1 1 0 1 1 2 1v-1z" class="N"></path><path d="M252 265c1 0 1 0 1-1 1 0 1 1 2 1h0c1 1 2 1 3 3h0-1c0 1 1 1 1 2l2 1h-1c-2 0-3-1-4-2v-1l1 1h0c0-2-2-3-4-4z" class="K"></path><path d="M265 269c1 1 2 2 3 2-1 1-1 1-2 1h-1c-2 1-4 0-5-1l-2-1h4c1 0 2 0 3-1z" class="O"></path><path d="M255 264c2 0 3-1 5-1 1 0 1 2 2 3s1 2 3 3c-1 1-2 1-3 1h-4c0-1-1-1-1-2h1 0c-1-2-2-2-3-3h0v-1z" class="N"></path><path d="M262 266c1 1 1 2 3 3-1 1-2 1-3 1l-1-2v-1l-1-1h0 1 1z" class="K"></path><path d="M255 264l6 4 1 2h-4c0-1-1-1-1-2h1 0c-1-2-2-2-3-3h0v-1z" class="b"></path><path d="M271 288v-1c0-4 1-5 4-8h1c1 1 2 3 3 5v1c0 1 1 2 2 3h0c2 1 2 3 4 4l3 3h-1s1 1 0 1c1 1 3 1 4 2-3 0-6 0-9-1-1-1-2-1-3-2-1 0-1 0-2-1h0c-2-1-3-2-3-3h-1v-1c-1-1-1-1-2-1v-1z" class="E"></path><path d="M271 288h1c1 0 1 1 2 2s2 3 3 3c1 1 2 1 3 2v-1s0-1-1-1c-2-1-4-3-5-5h-1v-2h0c1 1 1 1 1 2 1 0 1 0 1 1 1 1 2 1 2 1l1 1c1 1 1 1 2 1 1 1 2 2 3 2 1 1 3 1 4 2s3 1 4 2c-3 0-6 0-9-1-1-1-2-1-3-2-1 0-1 0-2-1h0c-2-1-3-2-3-3h-1v-1c-1-1-1-1-2-1v-1z" class="W"></path><path d="M271 263v1h-4c-3-1-4-3-6-5 0-2 3-4 4-6l1-1c4-6 6-15 7-23 0 1 0 2 1 3v3l-1 1c1 3 1 7 1 11 1 2 1 4 0 7 0 1 0 2-1 3-1 2-2 3-4 5h1c1-1 2-1 2-2l1 1c-1 0-1 1-2 2h0z" class="C"></path><path d="M269 262c0-1 0 0-1 0h-3l-1-1h0v-1c1 0 2-2 3-3h0c0-1 1-2 1-2h1v2 1l1-2 1 1s-1 1-1 2h1l1-2 1-1v1c-1 2-2 3-4 5zm2-12l-1 1h0v-2-1s0-1 1-1v-1h-1v1c0 1-1 2-1 2-1 2-1 3-2 4h0l3-8 2-6c0-2 0-3 1-3 1 3 1 7 1 11 1 2 1 4 0 7l-2-2v-2h-1z" class="G"></path><path d="M272 246c0-2-1-3 0-4v-2h1v6-1l-1 1z" class="H"></path><path d="M272 246l1-1v1 1l1 1v-1c1 2 1 4 0 7l-2-2v-2h-1l1-4z" class="E"></path><path d="M306 253c3 3 5 6 6 9 3 7 3 14 3 21-1 1-1 2-1 3-2 6-7 11-12 14 0-1-1 0-1-1v-1c-1 0-2 0-2 1-3 0-6 1-9 1-5 0-13-2-17-5-1-2-2-4-2-6 1 0 1 0 2 1v1h1c0 1 1 2 3 3h0c1 1 1 1 2 1 1 1 2 1 3 2 3 1 6 1 9 1-1-1-3-1-4-2 1 0 0-1 0-1h1c2 0 3 1 5 1 4 1 8 0 11-2 5-4 7-9 8-15 2-10-3-18-8-25h1c1 0 1 0 1-1z" class="V"></path><defs><linearGradient id="AW" x1="264.42" y1="230.303" x2="279.668" y2="233.567" xlink:href="#B"><stop offset="0" stop-color="#6f6e6e"></stop><stop offset="1" stop-color="#969596"></stop></linearGradient></defs><path fill="url(#AW)" d="M273 203v-2h1l1 3v3c1 3 1 8 1 11 0 9 2 36-2 43 0 1-2 2-3 2h0c1-1 1-2 2-2l-1-1c0 1-1 1-2 2h-1c2-2 3-3 4-5 1-1 1-2 1-3 1-3 1-5 0-7 0-4 0-8-1-11l1-1v-3c-1-1-1-2-1-3v-2h0 0c-1 2-2 7-4 9 0 0-1 0-1 1 0 0 0 1-1 1l-1-1v1s-1 0-2 1c1-1 1-2 1-3h-1 0l-1-1c1-1 3-3 4-5-1 0-2 1-2 1v-2c1 0 0 0 0-1 1 0 4-3 4-3l-1-1c5-5 5-10 6-17l-1-4z"></path><path d="M273 203v-2h1l1 3v3 4c-1-1 0-3 0-4h-1v4-4l-1-4z" class="F"></path><path d="M269 225h1c0 1-1 3-2 4l-1 1c-1 0-2 1-2 1v-2c1 0 0 0 0-1 1 0 4-3 4-3z" class="N"></path><path d="M283 231l1 1c1 1 2 3 3 4s2 2 2 3 2 4 3 4c0 1 0 1 1 1 1 1 3 3 4 5 2 3 4 5 5 9 0 1 3 7 3 9h-1-3-1 0v1c-1-1-1-1-1-2s-1-1-1-2c-1-1-1-2-2-4s-2-4-2-6c-1 1 0 3 0 4 0 6-6 11-11 13-1 1-2 2-4 2s-5 1-7 1v-1l-1-1h1l3-2c5-2 7-6 9-10 2-5 3-10 2-14 0-4-1-7-2-11 0-1-1-2-1-4z" class="K"></path><path d="M283 231l1 1c1 1 2 3 3 4s2 2 2 3 2 4 3 4c0 1 0 1 1 1 1 1 3 3 4 5 2 3 4 5 5 9 0 1 3 7 3 9h-1-1v-2s-1 0-1-1v-3c0-1-1-1-1-2-1-2-1-4-2-5-1-2-2-3-3-5-1 0-1-1-1-2h-1c0 2 2 4 2 5h0c-2-1-4-4-5-7h0c-1 0-1-1-1-1v-1h-1s-1-1-1-2l-2-2c0-1 0-2-1-3l-1-1c0-1-1-2-1-4z" class="G"></path><path d="M284 235l1 1c1 1 1 2 1 3l2 2c0 1 1 2 1 2h0c0 1 1 2 1 3 0 2-1 3-1 5 0 1 0 3-1 5v1c0 1 0 2-1 3 0 1-1 2-2 4-3 4-5 6-9 8-2 1-3 1-4 1l-1-1h1l3-2c5-2 7-6 9-10 2-5 3-10 2-14 0-4-1-7-2-11z" class="C"></path><path d="M284 260h1v-1c1-2 1-3 1-4h0l1-2h0 1v1c-1 1-1 2-1 2v1c0 2-1 3-2 4-1 3-3 6-6 8h0c-2 2-4 3-7 3l3-2c5-2 7-6 9-10z" class="B"></path></svg>