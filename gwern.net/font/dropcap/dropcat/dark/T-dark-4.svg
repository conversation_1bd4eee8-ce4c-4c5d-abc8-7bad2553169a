<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="55 54 608 620"><!--oldViewBox="0 0 704 752"--><style>.B{fill:#242324}.C{fill:#2b2b2b}.D{fill:#323232}.E{fill:#252525}.F{fill:#383637}.G{fill:#3d3c3c}.H{fill:#d9d7d9}.I{fill:#434343}.J{fill:#c9c8c9}.K{fill:#565556}.L{fill:#c8c7c8}.M{fill:#eceaec}.N{fill:#e7e6e7}.O{fill:#bebdbd}.P{fill:#1f1f1f}.Q{fill:#989797}.R{fill:#1d1c1d}.S{fill:#2b2a2b}.T{fill:#e1e0e1}.U{fill:#e3e2e3}.V{fill:#201f21}.W{fill:#f1f0f1}.X{fill:#858484}.Y{fill:#4b4a4a}.Z{fill:#686768}.a{fill:#dddcdd}.b{fill:#959495}.c{fill:#1c1b1c}.d{fill:#d5d3d5}.e{fill:#1b1c1b}.f{fill:#a1a0a1}.g{fill:#4f4e4f}.h{fill:#484747}.i{fill:#373637}.j{fill:#161516}.k{fill:#5c5b5b}.l{fill:#767575}.m{fill:#403f40}.n{fill:#c3c2c2}.o{fill:#b1b1b0}.p{fill:#7c7b7c}.q{fill:#acabab}.r{fill:#606060}.s{fill:#b7b5b7}.t{fill:#6f6f6f}.u{fill:#f7f7f7}.v{fill:#3b3b39}.w{fill:#0b0b0b}.x{fill:#8c8c8b}</style><path d="M160 388h0l1 1v1c-1 0-1 0-2-1l1-1z" class="R"></path><path d="M234 491v-2h1s0 1 1 2h-2 0z" class="w"></path><path d="M278 299h1l1 1c-1 1-2 1-3 1h-1l1-1h0l1-1zm162 137c1 1 2 1 2 2h-3v-1l1-1z" class="B"></path><path d="M171 353l2-1c1 1 1 2 1 3-1-1-2-1-3-2z" class="a"></path><path d="M202 380c0-1 0-2 1-3 0 1 0 1 1 2v1h-1-1z" class="e"></path><path d="M467 442c1 0 2 0 2-1l2 3h-2s-1-2-2-2z" class="N"></path><path d="M170 414l1 1v1l-1 1h-1l-1-1 2-2z" class="f"></path><path d="M178 400l1 1h0l1 1-2 2c-1-1 0-1-1-1h0c-1-1-2-1-2-1h2l1-2zm268 193s1-1 2-1v1c-1 1-1 2-1 3l-1-1c-1-1-1 0-3 0l1-1h2v-1z" class="B"></path><path d="M491 602h0l2 1 1 2c-1 0-2 1-3 0v-3z" class="E"></path><path d="M474 600c1 2 1 5 0 7h0c0-1-1-2-1-3 0-2 0-3 1-4z" class="i"></path><path d="M469 509h-4-1v-1-1h1c2 1 3 1 5 2h-1z" class="Y"></path><path d="M276 294h4 0 0c-1 1-1 1-2 1 0 1 0 1-1 2 0 0-1-1-2-1 1-1 1-1 1-2z" class="B"></path><path d="M196 505l1 1c0 1 0 3 1 4h0c-1 0-1 0-2-1h0c-1-2-1-2 0-4z" class="t"></path><path d="M221 477h-1v-5h0c1 1 1 3 2 4h0c-1 0-1 1-1 1z" class="l"></path><path d="M453 466c1 1 2 1 3 2l2 1h-7v-1h1v-1l1-1z" class="b"></path><path d="M224 453h-1c0-1 0-2 1-3 0 0 0 1 1 1l1-1h0v2 1l-1 1-1-2h0v1z" class="R"></path><path d="M458 474c2 0 4 0 5 1v3c-2-1-3-3-5-4zm-41-62h2c1 1 1 2 2 2l-1 1v1-1c-1 0-1 0-1-1-1 0-1-1-2-2z" class="B"></path><path d="M188 523h1 0c1 2 1 3 1 4h-2v-4z" class="i"></path><path d="M218 438v2c1 2 1 4 0 5h-1v-3c1-1 0-3 1-4z" class="f"></path><path d="M417 398c1 1 3 2 4 3h-2c-1 0-2-1-3-2l1-1z" class="F"></path><path d="M279 284h2v1h-2c-1 1-2 1-3 1s-1 0-1-1h0l4-1z" class="P"></path><path d="M139 393l2-2v1h1l2 1c-1 1-1 2-2 2-1-1-2-2-3-2z" class="c"></path><path d="M263 330h3v1l1 1c0 1-1 1-2 1l-1-1c-1 0-1-1-1-1v-1z" class="K"></path><path d="M551 497c1-1 1-1 1-2l5 2h0-2l2 1h0 0c-2 0-4-1-6-1z" class="M"></path><path d="M197 517v3c1 0 1 0 2-1h0v4h-1c0-1-1-2-2-3 0-1 1-2 1-3z" class="s"></path><path d="M220 415l-1-1c2 1 3 3 3 6h0c-1 0-2-1-2-2v-3z" class="x"></path><path d="M556 657v-2c1-1 2-1 3-1h0 3c-1 0-2 1-3 2-1 0-2 0-3 1z" class="P"></path><path d="M446 516c1 3 3 4 5 6-1 0-2 0-4-1 0-2-1-3-1-5z" class="E"></path><path d="M437 454c1 0 2 1 3 2h0l-3 2c-1-2-1-3 0-4z" class="i"></path><path d="M185 400l3-2c0 1 0 1 1 2-2 1-5 2-7 3 1-1 1-2 3-3z" class="b"></path><path d="M182 501h0l1-3 1-1c1 2 1 2 0 4 0 1-1 2-1 2h-1v-2z" class="l"></path><path d="M464 562l1 1c1 1 1 2 2 3v1l-1 1c0-1-1-1-1-2v-1c-1-1-1-2-1-3z" class="Q"></path><path d="M236 426c1 2 3 4 3 6-1 1-2 0-2 0l-1-1v-5z" class="J"></path><path d="M218 422v-1c1-2 1-2 2-3 0 1 1 2 2 2h0v1c-1 0-1 0-2 1h-2z" class="B"></path><path d="M132 329h1c1 1 0 1 0 2v2l1 1-1 1v-1h-2c0-2 0-3 1-5z" class="d"></path><path d="M429 420l-2-2c0-1 0-2 1-3 0 0 1 0 1 1h0l2 4-1 1h0v-1h-1z" class="F"></path><path d="M143 368l2 1-4 3h-2l4-4z" class="D"></path><path d="M429 435h3c0 1 1 1 1 1h-8v-1h4z" class="X"></path><path d="M507 646c1 0 2-2 3-2v1h2 1v1h0 0-2c-1 0-2 1-3 1h-1-1l1-1z" class="S"></path><path d="M511 646h0 2 0 0-2z" class="D"></path><path d="M237 423c1 2 4 4 5 7h-3l-2-7z" class="Q"></path><path d="M194 387c1-2 2-4 3-4l1 1s-1 1-1 2-1 2-2 3c0-1 0-2-1-2z" class="B"></path><path d="M238 522h0c-1-1-1-1 0-1v-1h1 3v1h0c-1 1-1 2-2 2 0 0 0-1-2-1z" class="e"></path><path d="M235 447h1c0 1 0 1 1 2v2h0v1l1 1v2h-1v-2h-1v-3c-1-1-1-2-1-3z" class="w"></path><path d="M231 361h0c1 1 2 1 3 1v4 1 1l-3-7z" class="D"></path><path d="M195 475h0c1 2 1 2 1 3v1c-1 0-1 0-2 1v2l-1 1v-2c0-1 0-3 1-4h1v-2z" class="Z"></path><path d="M213 515v-4-1l1 1c1 1 0 1 1 1v-2l1 1-1 1s1 2 0 3l-1-1c-1 0-1 1-1 1z" class="E"></path><path d="M450 477h2v-3h1v1 1l2 1c-1 1-2 1-2 2l-2-1h-1v-1z" class="G"></path><path d="M429 420h1v1h0l1 2c-2 0-3-1-5-2h0l3-1z" class="C"></path><path d="M193 483l1-1v-2 1c1 0 1 0 1-1h1c0 2 0 5-1 7-1-2-1-3-2-4z" class="Y"></path><path d="M233 458v-3h1v1c0 1 1 3 2 5h0c-1 0-2-1-3-1v-2z" class="f"></path><path d="M460 508c1 0 1 0 2 1h0c1 0 2 1 2 2h2 1c1 1 2 2 3 4-2-1-2-1-3-1-1-2-4-5-6-5-1 0-1 0-1-1z" class="P"></path><path d="M432 504v-1c1 0 2 1 2 2 1 2 1 4 2 5h-1c-1-1-1-2-1-3-1 0-1 0-1-1s-1-1-1-2z" class="k"></path><path d="M251 396c3 2 5 4 7 6-3-1-5-2-7-5v-1z" class="X"></path><path d="M204 521l2 2v1l1-1 1 1 1 3c-2-1-2-2-3-2v2l-1-1c0-2-1-3-1-5z" class="Z"></path><path d="M472 596c1 1 2 3 2 4-1 1-1 2-1 4 0-1 0-1-1-1v-7z" class="E"></path><path d="M240 450h0l2 3c1 0 1 1 1 2s1 2 2 2h-1c-2-1-3-3-3-5l-1-2z" class="Q"></path><path d="M508 640h2c0 1 0 2-1 2s-1 0-2 1l-1 1v-4h2zM158 415h1 1c1-1 0-1 1-1v1h0l1 1c0 1 0 2-1 2l-1 1c-2-2-2-3-2-4z" class="B"></path><path d="M469 444h2l3 5v1c-1-1-1-1-1-2 0 1 0 1-1 2h0l-1-2c0-1-1-3-2-4z" class="u"></path><path d="M173 418c0-1 1-1 2-1s0-1 2-2v3h0l-2 2h-1c-1-1-1-2-1-2z" class="V"></path><path d="M454 421l3 4-1 1h0c0 1 0 1-1 1 0-1-1-1-1-2-1-1-1-2-1-3l1-1z" class="L"></path><path d="M203 514h0l1 1 1 1v1c-1 0-2-1-3 0l-1 1h-1l2-4h1 0z" class="c"></path><path d="M203 514h0l1 1 1 1h-1-2l1-1v-1z" class="D"></path><path d="M178 415c2-1 3-3 5-4v1c-1 1-1 2-3 3l-3 3v-3h1z" class="o"></path><path d="M121 385l2-10v3h1v-1 1c0 2 0 4-1 6h0c-1 0-1 0-1 1h-1z" class="M"></path><path d="M465 592c1-1 1-3 0-4h1c0 1 1 1 2 2h0v2c-1 1-1 2-1 2l-1 1v-2l-1-1h0z" class="R"></path><path d="M191 497c0-2 2-4 2-7h1c1 1 0 2 0 3 0 2-1 4-2 5l-1-1z" class="I"></path><path d="M272 226l2 1c2 0 4 1 6 2-3 0-5 0-7-1h-3l1-1 1-1z" class="N"></path><path d="M214 406c1 1 2 2 2 4h-1v-2c-2 1-1 3-3 3 0-2 1-4 2-5z" class="p"></path><path d="M223 531h0c2 2 2 2 3 4h-1v1l-2-2-2-2h0c1 0 1-1 2-1z" class="e"></path><path d="M156 414c-1 2 0 3 0 4h-3v-3c0-1 0-1 1-2v1h2z" class="P"></path><path d="M448 593h1c-1 2-1 4-1 7v1c0 1 0 1-1 1h0c-1-2-1-4 0-6 0-1 0-2 1-3z" class="f"></path><path d="M119 306v-2 2l2-1h0l2 2c-2 0-2 1-3 2h-1-2l-1-1h0 1 2v-2z" class="T"></path><path d="M266 328h0c2 2 5 1 7 2-1 1-2 1-3 0h-4-3-1c1-1 1-1 2-1 1-1 1-1 2-1z" class="b"></path><path d="M218 506h-1c-1-2-1-3-1-5v-6 1l1 4c1 2 1 4 1 5v1z" class="J"></path><path d="M177 504h1l1 1 1 1c0 2 0 1-1 2 0 1-1 2-1 3l-1-1v-6z" class="V"></path><path d="M214 482v-4h1l1 5v2 2h-1c0-2-1-3-1-5z" class="L"></path><path d="M427 461c3 0 3 0 5 1l2 1 3 1h-4c-1 0-3-1-5-2l-1-1z" class="S"></path><path d="M210 406v-2h1 1v1l1 1 1-1v1c-1 1-2 3-2 5h-2c0-1 1-1 1-2v-1-2h-1z" class="P"></path><path d="M260 547h0l1-1c0-1 0-2 1-3h1c-1 1-1 2-1 2h1v-1l1 1v1c-1 1-1 3-2 4h-1v-3h-1z" class="B"></path><path d="M446 515l1-2c2 1 3 4 4 5 0 1-1 1-1 1l-4-4z" class="D"></path><path d="M180 548l2 2c1-2 2-2 3-3h0l-2 5h0c-1 0 0 0-1-1h-4l2-3z" class="C"></path><path d="M234 537v-1c-1-1-2-1-3-2-1 0-1-1-2-1 4 0 7 2 10 4l-1 1h-3l-1-1z" class="f"></path><path d="M451 418l3 3-1 1c0 1 0 2 1 3h-3l-1-1 1-1 1 1-1-2c0-1 0-2-1-3l1-1z" class="O"></path><path d="M430 421l1-1-2-4h0 1c0 1 1 2 2 3l1 2h0v2h-2 0l-1-2z" class="k"></path><path d="M239 467h1s1 1 1 2h2v2 1 1h-1 0v-1c-1-1-1 0-1-1-1-2-2-3-2-4z" class="w"></path><path d="M188 499c1-1 1-1 3 0h0c-2 2-2 3-3 6l-2-2c1-1 1-3 2-4z" class="E"></path><path d="M239 422h1c2 2 4 5 4 7h-1l-3-3c1-2 0-2-1-4h0z" class="X"></path><path d="M201 493l1 3v6h0 0-2c1-2 0-5 0-7 0-1 1-1 1-2z" class="B"></path><path d="M161 393h1 0c0 1 0 1-1 2 0 2-1 3-3 4h-1v-2l4-4h0z" class="X"></path><path d="M258 394h1 1s0-1-1-2h0v-1h2l2 1 1 2h-1c-1 0-1 1-2 1-1 1-1 1-2 1l-1-2z" class="E"></path><path d="M227 399l1 2c1 1 2 2 2 3v1h-1v-1h-1v1h0l-2-1v-3c0-1 0-1 1-2z" class="p"></path><path d="M165 500h2v-1h1v3l-1 1s0 1-1 2l-1-1h-1c0-1 0-3 1-4h0z" class="P"></path><path d="M216 388l1-1v1c-1 1-1 2-1 3-1 2-1 5-2 6v-2c-1 1-2 2-2 3v-1c0-2 1-3 2-5l1-2c0-1 1-1 1-2z" class="L"></path><path d="M275 416c0-1-1-2-1-3v-1c2 0 4 2 4 3 1 0 1 1 1 2l-3-3h0l3 4v1c-1 0-2-1-3-2v-1h-1z" class="F"></path><path d="M265 388v-1c1 0 1 0 1 1 1 1 1 1 2 1 1 1 1 2 2 2l1 1v1l-1-1h-2-1-1c0-1 0-2-1-3-1 0 0-1 0-1z" class="w"></path><path d="M183 430l2-4c0 1-1 3 0 5 0 2-2 4-3 5h0c-1-2 0-4 1-6z" class="o"></path><path d="M463 434c2 1 4 4 5 6l1 1c0 1-1 1-2 1v-1h-1c0-2-1-3-2-5l-1-1v-1z" class="d"></path><path d="M468 440l1 1c0 1-1 1-2 1v-1l1-1z" class="T"></path><path d="M179 437c1-3 3-4 3-7h1c-1 2-2 4-1 6h0c0 1-1 1-2 1 1 2 1 2 2 3l-1 1c-1-2-2-3-2-4z" class="B"></path><path d="M496 640c-2 0-4 1-5 1v-1c1-1 3-2 5-3 1 0 3 0 4 1 0 0 0 1-1 1h-1c-1 0-1 0-2 1z" class="k"></path><path d="M498 639l-1-1h0 3s0 1-1 1h-1z" class="l"></path><path d="M201 521l2-2h0c1 1 1 2 1 2 0 2 1 3 1 5-1 0-1 0-1 1h-1v-2h0c0-1-1-2-1-4h-1z" class="F"></path><path d="M479 601v-1c1 1 1 2 2 3h1 1c-1 1-1 1-1 2s0 3 1 4c-1 0-1 1-2 1 0-3-1-6-2-9z" class="O"></path><path d="M220 415v3c-1 1-1 1-2 3v1h-1l-1-1c0-2 1-3 1-5v-1h1 1 1z" class="C"></path><path d="M175 433h1v-1l3-6 1-2c-1 2-1 4-1 5 0 2-2 4-1 6v1c-1-1-1-1-1-2h0c-1 0-1-1-2-1h0z" class="Q"></path><path d="M267 535h1c1 1 1 2 1 3h0l1 1c0 2 0 3-1 4-1-1 1-4 0-5l-1-1h-1v1c0 1 1 2 1 3h0c-2-2-2-3-3-5 1-1 1-1 2-1z" class="V"></path><path d="M418 553h0c2 1 3 6 4 8 0 0 0 1 1 2h-1l-1-1c-2-2-3-6-3-9z" class="Y"></path><path d="M231 387h1c0 1-1 1-1 2l-1 2h0v1c-1 1-1 1-1 2h0v1l-1 1c0 2 1 4 0 5l-1-2h0c0-2 0-3 1-4v-2l3-6z" class="E"></path><path d="M178 435c1-2 2-7 4-8h0v3c0 3-2 4-3 7-1-1 0-1-1-1v-1z" class="b"></path><path d="M439 589l2 2h0c-1 1-1 2-1 4l-3 6v-5c1-2 2-4 2-6v-1z" class="g"></path><path d="M180 392c2 0 2-1 3-2 2-1 3-1 5-1l-6 6c-1 0-1 0-2-1v-2zm22 51l-1-1v-2c1-1 1-1 2-1h1c1 1 1 3 1 4l-1 1h0v1c-1 0-1 0-2 1v-3h0z" class="B"></path><path d="M157 391l1 1c0 1-1 1-2 2h1 1c0 1 0 1-1 1-2 2-4 3-6 4h0l1-1c0-1 1-2 2-3 1-2 2-3 3-4z" class="x"></path><path d="M221 439c-1 0 0 5-1 6v-1-3c-1-1 0-2-1-4h0v-2h-1v-2h0v-1c1 1 1 1 1 2 1 1 1 1 1 2l1 1v-1 3zm-17-14c1 0 2-1 2 0v1h1v2 1l-1 1v1l-1-1-2-1 1-4z" class="R"></path><path d="M206 426h1v2 1l-1 1v1l-1-1c0-1 0-3 1-4z" class="o"></path><path d="M269 436h-1c-1-1-2-2-3-2-1-1-2-1-3-2h0c-1-1-1-1-2-1 0-1-1-2-2-3h1c1 1 1 2 3 2l1 1h0c0-1 1-1 1-1 1 1 1 2 2 2 0 1 1 2 2 2 0 1 0 1 1 2z" class="f"></path><path d="M196 520c1 1 2 2 2 3v2l-1 1v1l-2-2h-1l1-4 1-1z" class="q"></path><path d="M197 524v1h1l-1 1v1l-2-2v-1h2z" class="b"></path><path d="M196 520c1 1 2 2 2 3v2h-1v-1c-1-1-1-2-2-3l1-1z" class="L"></path><path d="M203 423l1-2h0c1 1 1 2 0 4l-1 4c0 2-1 3-2 4h0c-1-2 1-5 2-7v-3z" class="o"></path><path d="M500 638h7 1 0v2h0 0-2-1c-2 0-4-1-6-1 1 0 1-1 1-1z" class="Z"></path><path d="M508 638h0v2h0l-1-1v-1h1z" class="x"></path><path d="M250 377h1c2 2 4 5 6 7l-1 1v1h-1c-2-3-3-6-4-8l-1-1z" class="O"></path><path d="M471 594c1 1 1 1 1 2v7c-1 1-1 3-2 4l-1-1v-1c1-1 2-3 2-5l-1-4h1c0-1-1-1 0-2z" class="k"></path><path d="M420 500h1c1 1 2 1 3 2v3 1l1 2h-1c-1-1-1-2-2-3 0-1 0-1-1-2h0c-1-1-1-2-1-3z" class="F"></path><path d="M182 515v4h1v-3l1-4 1-1c1 0 1 1 1 3h-1v2 1c0 1-1 1-2 2 0 1-1 1-1 2s-1 1-1 1h0l-1-1h0l1-1c1-2 1-2 1-5z" class="C"></path><path d="M423 551c-2-2-3-4-5-4v-1c-1-2-1-3-1-5h1c0 2 1 2 2 3h0c1 2 3 5 3 7z" class="r"></path><path d="M267 460c1 0 1 1 2 2 2 2 3 4 4 6h0l-2-2h-1c1 1 0 1 0 2-1-1-1-2-2-3-1-2-2-3-3-4h0c1 1 2 1 3 1l-1-1h-1l1-1z" class="Q"></path><path d="M206 425l1-7c1 1 1 3 1 5v1h1v4c-1 1-1 1-2 1v-1-2h-1v-1z" class="X"></path><path d="M208 423v1h1v4c-1 1-1 1-2 1v-1c1-2 1-4 1-5z" class="c"></path><path d="M228 494h0c1 2 1 3 2 5s3 4 3 7l-1-1c-1-1-2-2-2-3l-1-1c0-2-1-4-1-7z" class="f"></path><path d="M416 424c0-1-1-1-1-1l-2-1 1-1h1c0-1-1-1-1-2v-1l2 1h0c0 2 1 3 2 4h1 1c0 1 0 1 1 1h0 2l1 1c-3 0-5-1-8-1z" class="e"></path><path d="M218 403h1c1 1 1 2 1 3h1v1c0 1 0 1-1 1h-2v2l-1-1v-1c-1-1-1-1-1-2h0v-2l1-1v1l1 1h0v-2zm208 82c1 0 2 0 3 1s3 3 5 3c0-1-1-1-1-2l1-1v1c0 1 0 1 1 2l-1 1c-1 1-1 1-2 1-2-1-4-4-6-6z" class="V"></path><path d="M466 582c-1-1 0-4-1-5 0-1 0-2-1-2v-2h1v1c1 1 1 2 1 3s0 1 1 2h1c0-1 0-1 1-2v3h0c-1 1-1 1-1 2l-1 1v-1h-1zm-275-85l1 1 2 3v-3h1c1 0 1 1 1 2h-1c0 1 1 1 1 2v1 1c-1-1-1-1-1-2l-1 1-1-1h-1c-1 0-1 0-2 1 0-1 0-3 1-4h0v-2z" class="P"></path><path d="M455 427c1 0 1 0 1-1h0l1-1c2 3 5 6 6 9v1l-3-2v-1c0-1-1-1-1-2l-4-3z" class="T"></path><path d="M491 624l1 1c1 2 3 3 4 5v1c-1 0-1 0-1-1h-2 0l1 2c1 0 2 0 2 1h-1c0-1-1-1-2-1-1-1-1-1-1-2h-1c0-1 0-2 1-3 0-1-1-2-1-3z" class="m"></path><path d="M216 527v-2h1l1 1 2 1h0c1 2 2 3 3 4-1 0-1 1-2 1h0l-3-3-2-2z" class="B"></path><path d="M213 515s0-1 1-1l1 1c1-1 0-3 0-3l1-1h0v1c0 2 0 6 1 8l-1 1h-1v-1l-1-3c-1 0-1-1-1-2z" class="D"></path><path d="M201 521h1c0 2 1 3 1 4h0l-1 2h-1s0-1-1-1v2l-3-2 1-1v-2h1 0 1v1h1c0-1 0-1-1-2l1-1z" class="Z"></path><path d="M184 439s1 0 1 1l1 1v-1c2 1 1 2 3 1h0c1 1 2 1 3 1s2-1 2-1c1 1 1 2 2 3-1 1-1 1-1 2h-1v-4h0l-1 1c0 1 0 1-1 1l-1-1-2-1c-1 0-1 1-2 1v2l-1 1c0-3-2-5-2-7z" class="E"></path><path d="M224 453v-1h0l1 2 1-1v3l1 1v1l1 1-1 1v2h-1c-2-3-2-6-2-9h0z" class="f"></path><path d="M225 454l1-1v3l1 1v1l1 1-1 1-2-6z" class="S"></path><path d="M414 500h1c0 2 1 4 1 6 0 0 0 1-1 1l-1-1h0v1h0v2l-1-1c-1-1-1-4-1-5s1-2 2-3z" class="B"></path><path d="M464 447l-3-2-3-3h1 0c3 1 4 3 6 3l1-1c1 2 3 3 3 4-1 1-1 0-2 0s-2-1-3-1z" class="i"></path><path d="M432 435h1v-1-1c1 0 2 0 2 1h2 1l2 2-1 1c-1 1-2 1-3 1 0 0-1-1-1-2h-2s-1 0-1-1z" class="E"></path><path d="M452 507h0c-1-1-2 0-3-1h-1c-1-1-2 0-2-1s0-1 1-1c1-1 4-1 6-1l2 2h-1 0l2 1h-4v1z" class="B"></path><path d="M149 364h0c1 0 1-1 2-1 1-1 3-2 4-3l-1 2c-1 0-1 0-1 1s0 1-1 2c0 1-1 2-1 2l-3 2s0-1-1-2c1-1 2-2 2-3z" class="V"></path><path d="M254 419v-1-2l1-1c-1 0 0 0 0-1h1c0 1 1 3 3 4h1c0 1 1 1 1 2l-1 1c-1-1-1-1-2-1s-1 0-1 1c-1-1-2-2-3-2z" class="K"></path><path d="M419 431c-1-1-2-1-2-2h0l12 6h-4v1h-2c0-1 0-1 1-2h0l-2-2-3-1z" class="Z"></path><path d="M171 497h1c3 2 1 3 2 5v1c1 1 1 4 0 5-1-1-1-2-1-4-1-2 0-4-1-7 0 2-1 5-2 6h0 0c0-2 0-4 1-6z" class="h"></path><path d="M124 401v1c1-2 1-3 1-5l3-6 1 1c0 1 1 2 1 2v2l-1 1h0l-1-2h0c-2 2-2 4-2 7h-1 0c-1 1 0 2-1 2h0c-1-1-1-2 0-3z" class="t"></path><path d="M267 231c1 0 3 0 4-1h1v1c2 1 5 1 7 3h-5-1c-1 0-1 0-1-1-2-1-4-1-6 0l1-2z" class="O"></path><path d="M195 403c1-1 0-1 1 0v2c0 1-2 2-2 3h1 0v1l-1 1h-1v-2c-1 1-1 1-2 0h1v-1c1 0 1-1 2-2h-1l-3 3h-1v-1c1 0 2-1 3-2 1 0 2-1 3-2z" class="V"></path><path d="M259 396c-3-1-3-4-6-6-1-1-3-2-4-3l-3-6c5 2 6 8 10 11 1 0 1 1 2 2h0l1 2z" class="x"></path><path d="M511 638c1-1 2-1 4 0 2 0 4 0 6 1-4 1-7 1-11 1h-2 0 0v-2h3z" class="K"></path><path d="M511 638c1 0 1 0 1 1v1h-4v-2h3z" class="X"></path><path d="M483 497c2 1 6 1 7 3h-15 0v-1h4c1 0 1 0 3-1 0 0 1 0 1-1z" class="J"></path><path d="M231 450h0c0-1 0-1 1-2v1c1 2 2 4 2 6h-1v3 2l-1 1h0v-1-2-1h0v-1c-1 0-1-1-1-1v-2c-1-1-1-2 0-3z" class="E"></path><path d="M232 449c1 2 2 4 2 6h-1v3l-1-7v-2z" class="b"></path><path d="M140 402h0c1-1 1-1 1-2l1-1c1 0 2-1 3 0h2 0l-1 2c-1 0-1 0-1 1h0-1c-2 1-3 3-5 4h0c1-1 1-1 1-2v-1h0v-1z" class="e"></path><path d="M271 411c1 0 1 1 2 1v1c0 1 0 2 1 3h1 1v1h0c0 2 1 2 2 3v1h-1l-3-3c-1 1-1 0-1 1l-1-1h-1 0c1 0 1 0 2-1 0 0-1-1-1-2s-1-2-1-4z" class="c"></path><path d="M466 582h1v1 4h0 1c0-1 0-2 1-3h0c0 2 0 4-1 5v1h0c-1-1-2-1-2-2h-1c1 1 1 3 0 4l-1-1v-2h0c0-2 1-2 1-4 0 0 1-2 1-3z" class="I"></path><path d="M464 589h0v2l1 1h0l-1 4-3 3v-1-2h-1c1-1 1-2 2-3s1-3 2-4z" class="l"></path><path d="M461 596s1-2 2-2l1 2-3 3v-1-2z" class="b"></path><path d="M187 490c1-1 1-1 2 0l-1 2-1 1h0c0 2-1 3-3 4l-1 1-1 3h0c-1-1-1 1-3 1h0c1-1 3-2 3-3 1 0 0-1 1-2h1l1-1v-2c0-1-1 0-1-2 1-1 2-1 3-2z" class="K"></path><path d="M187 490c1-1 1-1 2 0l-1 2-1 1v-3z" class="E"></path><path d="M267 284s1 0 1-1l2 2h2v2h0l-2 2h2v1h-1c-1 1-1 1-2 1h-3l1-1h2v-1l-1-1h1c1-1 1-1 1-2h-1l-1 1c-1-1 0-1-1-1h0v-1-1z" class="F"></path><path d="M129 400c1-1 1-3 2-5h1v2l1 1v-2l1 1 1 2c-1 1-2 3-2 4v1h0-1l-1-4-1 1-1-1z" class="p"></path><path d="M131 400c0-1 0-1 1-2l1 1v1l-1 1-1-1z" class="L"></path><path d="M452 507v-1h4l4 2c0 1 0 1 1 1 2 0 5 3 6 5h-2c-1-1-1-2-3-3 0 0-2 0-2-1-1-1-1 0-2-1-2 0-4-1-6-2h0z" class="K"></path><path d="M209 382v-1l1-1c0 2 1 2 1 2 2 2 0 2 3 3h1v2s1 0 1 1-1 1-1 2l-1 2c-1-1-1-3-1-5h0-1c0 1-1 2-1 3l-1-1 1-1v-1c0-1 0-2 1-2v-1-1h-2l-1-1h0z" class="B"></path><path d="M215 387s1 0 1 1-1 1-1 2h-1c0-1 0-2 1-3z" class="P"></path><path d="M227 387h0c-1 2-1 5-2 7l-1 3-1 1v1 4c-1 0-1 0-1-1-1 0 0-1 0-1v-1h-1v1h-1v-1c0-2 2-4 3-5l1-1 1-3v-1-1l2-2z" class="C"></path><path d="M169 387h0l-2 2-1-1 1-2c1 0 1 0 2-1s2-2 2-4l1-1c1 0 1 1 1 2h0l1 1c-1 1-1 3-3 3 0 1-1 1-2 1z" class="B"></path><path d="M217 464c1 1 1 2 1 3v2h0c0 1 0 1-1 1v1c0 2 0 4-1 6s0 4 1 6h-1l-1-5 1-1v-10h-1v-2l1-1h1z" class="C"></path><path d="M215 465l1-1v3h-1v-2z" class="B"></path><path d="M419 431l3 1 2 2h0c-1 1-1 1-1 2-3-1-4-2-6-3l-2-2h1s1 1 2 1l1-1z" class="C"></path><path d="M422 432l2 2h0c-1 1-1 1-1 2-3-1-4-2-6-3h1l2 1h2v-2z" class="K"></path><path d="M200 502h2 0c0 3-2 5-3 8v1c-1 0-1-1-1-1h0c-1-1-1-3-1-4l-1-1h1v1h1c0-1 2-3 2-4z" class="C"></path><path d="M271 302h1 1v-2c-1 0-1-1-2-1l1-1c1 1 1 1 2 1h2 2l-1 1h0l-1 1h1-1c1 1 1 1 2 1v1h0c0 1 0 1 1 1-1 0-3 0-4 1h-1l-1-2c-1-1-1 0-2-1z" class="P"></path><path d="M276 301h-1l-1-1h1 2l-1 1z" class="C"></path><path d="M268 550v-1c1 1 1 1 1 2 1 0 1-1 2-2v1c0 1 0 2-1 3v3h0c0 1-1 1-1 1h0-1 0l-2 5-1 1v-2c1-1 1-4 3-6h0v-3-2z" class="B"></path><path d="M251 550h2l1 1c1 0 1-1 1-2l1 1v2c1-1 1-2 2-3v4 1c-1 1-1 1-2 1v1c-2-2-4-4-5-6z" class="O"></path><path d="M446 516c-1-1-2-3-3-4h1l2 3 4 4s1 0 1-1l1 1c1 3 2 3 3 5h0-1s-1 1-2 1v-2l-1-1c-2-2-4-3-5-6z" class="t"></path><path d="M191 499c-1 1-1 3-1 4 1-1 1-1 2-1h1l1 1h0c0 3 1 4 0 6-2-1-3-3-4-5l-1 1h-1c1-3 1-4 3-6z" class="Z"></path><path d="M419 540l-3-3c-2-1-3-2-4-4v-1c-1-1-1-2-1-4 1 2 1 3 2 4 0 1 2 3 3 3h1 1s1 1 1 2c2 1 2 1 2 3v1c-1 0-2-1-2-1z" class="t"></path><path d="M215 467h1v10l-1 1h-1v4h0v-1c-1-2-1-2 0-3 0-3 1-8 0-9 0-1 0-2 1-2z" class="R"></path><path d="M241 385l-1-7h1v1c1 4 1 7 4 10 1 1 2 2 3 4h-1c-1-1-4-2-5-4 0-2-1-3-1-4z" class="k"></path><path d="M230 467h1c1 2 2 4 2 5-1 4 4 7 4 11h0c-1 0-2-1-2-2-3-3-5-10-5-14z" class="X"></path><path d="M427 411c-1-1-2-1-2-1-1 0-1-1-2-1h0l-2-1c-1 0-1-1-2-1l-1-1c-1 0-1-1-2-1l-2-2h1c1 0 2 1 3 2l1-1h1c0 1 1 1 2 2h-1c1 1 2 1 3 2 1 0 1 0 2 1h1v2z" class="B"></path><path d="M209 424c0-1 0-2 1-3v2h0c1-1 1-2 1-3v-1c-1-1 0-3 0-4h1c0 3 0 6 1 9-1 0-1 1-1 2-1 0-1 1-2 2h-1v-4z" class="X"></path><path d="M274 440c1-1 1-1 1 0l1 1 1 2v1c1 0 1 0 1 1h-1c0-1 0-1-1-2l-1 1v2 1l-1-1-1-1c-1-1 0-1-1-1 0-1-1-2-2-3h0c2 0 2-1 3-1h1z" class="V"></path><path d="M271 316c1 1 1 1 2 1 1-1 2-1 3-2h0v2s-1 0-1 1c-1 1 0 2-1 4l-2 1c-1-1-1-1-1-3h-2v-1l2-1v-2z" class="S"></path><path d="M271 318h3v1l-2 1v1h1l1 1-2 1c-1-1-1-1-1-3h-2v-1l2-1z" class="t"></path><path d="M512 645c0-1 0-1 1-2 2-2 3-2 6-2h1c0-1 0-1 1-1s2 1 3 2h-3l-8 3h-1z" class="B"></path><path d="M458 560c0-1 0-2 1-3h0l2 2v1c1 1 1 2 2 3s1 1 1 2h0c-1 0-2 0-2-1-1 1 0 1 0 1l-1 1c0-1-1-1-1-2v-1h-1c-1-1-1-2-1-3z" class="b"></path><path d="M459 557l2 2v1 2h-1c-1-2-1-3-1-5z" class="v"></path><path d="M265 487h4c0-1-1-1-1-2h-1v-1c-1-1 0-1-1-1 0-2-2-3-3-4h1l3 3 1 1c1 1 1 3 2 4v2h-2v1l2 2v2l-1-1-1-2c-1-1-3-2-3-4z" class="C"></path><path d="M268 569h0c1 0 2-1 2-2 1-2 2-3 3-4-1 3-3 5-4 9v2h0c-1 1-1 1-1 2h0l-2 2h0l-1-3c3-1 1-4 3-6z" class="H"></path><path d="M212 415h1c1 1 1 1 1 2h0 1v3h1v3 1h0-1c-1 0-1-1-2 0-1-3-1-6-1-9z" class="V"></path><path d="M271 418h1l1 1c1 1 4 2 5 4-1 0-1-1-2-1l1 3h0l-3-3s-1 0-2-1v1c2 1 4 4 5 6l-3-3c-2-1-3-2-5-4 1-1 1-1 2-1v-2z" class="b"></path><path d="M245 444h1c1 0 1 1 1 2 2 0 2 2 3 3 1 0 0 0 1 1s2 1 2 3h-1l-1-2v1s0 1-1 1h0l-2-2c-1-1-1-2-2-3h0c-1-2-1-3-1-4z" class="E"></path><path d="M237 432h-1c0 1 0 1-1 1 0 0-1-1-1-2 0 0-1-1-1-2 0 0-1-1-1-2v-1-2h0l1 1h1 0l-1-1v-1l1-1c0 1 1 2 1 3 1 0 1 0 1 1v5l1 1z" class="R"></path><path d="M228 411h1c0 1 1 1 2 2v-2h1v2c0 1 1 1 1 2s0 2-1 3h0c-1 0-2-1-2-2h-1c1 1 1 2 0 2s-1-1-1-2c0 0-1 0-1-1v-2h1v2h1v-1c0-1 0-1-1-2h0v-1z" class="b"></path><path d="M258 412c1-1 1-1 1-2h-1v-1h0 1c0 1 0 1 1 1s1 1 2 1v1l2 1 3 3h0c-1 1-1 1-2 0h0v1l-1 1v-1c0-1-1-2-3-2-1-1-1-2-3-3zm-81-27l1 1c2-1 2-5 4-7v2 1c-1 1-1 1-1 2s-1 1-1 2h0v2l-1 1h-3l-1 1-1 1h-1v-1h-2 1c2-1 4-4 5-5z" class="B"></path><path d="M130 336l-1 1h1l-16 2c-2 0-2 0-4-1l20-2z" class="d"></path><path d="M249 491l-1-1c-1-1-3-3-4-5 1 0 2 0 3-1l3 3h0v-2h-1l1-1 1 1h0l1 1-1 1h0c1 1 1 1 1 2l1 1-1 1c-1 0-2-1-2-1l-1 1z" class="C"></path><path d="M216 496c1 1 2 2 2 3v-2c1-1 0-2 0-3h1s1 1 1 2v2c0 1 0 1 1 2h0v2c1 1 1 2 1 3h-1 0c0-1 0-1-1-1h-1v1l-1 1v-1c0-1 0-3-1-5l-1-4z" class="c"></path><path d="M253 493h0c0-1 0 0 1-1 1 0 1 1 2 2h1l-1 1h1l1 1h0v-1h1v1c1 2 2 3 4 5 0 0 0 1-1 2l-1-1v-1c-1 0-2-1-3-2h0c-1-1-1-1-2-1l-1-1v-1c0-2-1-2-2-3z" class="D"></path><path d="M143 388c1 0 1 0 1-1 2-1 3-1 3-2h2l-4 6s-1 1-1 2l-2-1h-1v-1c0-1 0-3 1-4l1 1z" class="E"></path><path d="M142 387l1 1c-1 1 0 3-1 4h-1v-1c0-1 0-3 1-4z" class="V"></path><path d="M199 510c1 1 2 1 2 2l1-1v-1l2-1-2 5-2 4-1 1h0c-1 1-1 1-2 1v-3c0-1 2-5 2-6v-1z" class="n"></path><path d="M268 228c1 0 1-1 2-1h1l-1 1h3v1c2 1 4 1 6 3l-7-2h-1c-1 1-3 1-4 1-1 1-4 2-5 2v-1c2-1 4-2 6-4z" class="H"></path><path d="M224 397l1-3 1 1c0 1 0 2 1 4h0c-1 1-1 1-1 2v3 2h0-1s-1 0-1-1 0-1-1-1v-1-4-1l1-1z" class="R"></path><path d="M224 397l1-3 1 1c0 1 0 2 1 4h0c-1 1-1 1-1 2v-1s0-1-1-2l-1-1z" class="I"></path><path d="M151 620v1l-1 1v1c1-1 1-1 3-1-2 1-3 1-4 2h0c-2 0-3 0-5 1-1 1-2 1-3 1h-1c-1-1-1-2-2-2 5-1 8-2 13-4z" class="d"></path><path d="M105 323c3-1 6-1 9-1h10l2 2h-19c1 0 1 0 1-1h-3z" class="O"></path><path d="M176 516c0 1 0 2 1 3v-2h1v3l1-1c0-2 2-4 3-6v-2l1 1-1 3c0 3 0 3-1 5l-1 1h0l-1 2-1 1-1-1v-1h-1c1-1 1-1 0-2v-4z" class="I"></path><path d="M269 572c2-1 4-6 5-8-1 6-3 12-6 16-1 1-1 2-2 3v-5h0l2-2h0c0-1 0-1 1-2h0v-2z" class="L"></path><path d="M126 319h0 3l1 2c0 1 0 2 1 2l2 1h0v2h1v1c-1 1-2 1-3 1 0-1-1-3-1-4-1-1-3 0-4 0l-2-2c1 0 2 0 3-1l-1-2z" class="N"></path><path d="M262 430v-1l-2-1v-3c1 0 1 0 2 1h0v1c1 1 3 2 3 3h3l2 3 1-1 1 1c-1 1-2 0-3 1v2c-1-1-1-1-1-2-1 0-2-1-2-2-1 0-1-1-2-2 0 0-1 0-1 1h0l-1-1z" class="E"></path><path d="M228 421l-1 1h-1l-2-2-1-3h0c0-1 0-2-1-3h0l1-1c1 1 1 1 1 2h1v-2h0c0-1 0-1 1-1h0v1c0 2 0 2 1 3v2l1 1v2z" class="V"></path><path d="M227 418l1 1v2h-2v-2c1 0 1 0 1-1z" class="e"></path><path d="M415 524l1 1c0 2 0 3 1 5h0c0 2 0 2 1 4v1h-1-1c-1 0-3-2-3-3v-2l1-1v-3l1-2z" class="B"></path><path d="M416 531l1-1c0 2 0 2 1 4v1h-1-1l-1-2c-1 0-1 0-1-1 1 1 1 1 3 1l-1-2z" class="k"></path><path d="M415 524l1 1c0 2 0 3 1 5h0l-1 1h-1v-2c0-1 0-2-1-3l1-2z" class="C"></path><path d="M257 421l1 1h-1l-3-1v1h0l-1 1 1 1v1c-2-1-4-3-5-4v-1h1v-1l-1-1h0c2 0 3 2 4 3 0-1-3-5-3-6 1 0 2 2 4 4 1 0 2 1 3 2z" class="X"></path><path d="M150 370c1-1 2-3 4-4h0c0 1-1 2-2 2l1 1c0-1 1-1 2-1 0 0 0-1 1-1v1c-1 1-1 2 0 4l2 1c-1 1-2 2-2 3-1-1-1-2-1-3l-1-1v1h0l-1-1c0-1 0-1-1-1 0 0 0 1-1 1l-1-2z" class="G"></path><path d="M208 437c-1-1 0-1 0-1h2v1 2c-1 1-1 2-1 3 0 2 0 4-1 5h-1v1 2l-1-1h-1v-1l1-2 1-1v-5c0-1 0-2 1-3z" class="B"></path><path d="M208 437c-1-1 0-1 0-1h2v1l-1 2v-1c-1 0-1-1-1-1z" class="e"></path><path d="M161 399v-1c0-1 1-2 1-2l3-2v1 1l1 1c1 0 1 0 2-1 1 0 2 0 2-1 0 2 0 2-1 3l-2 1c-1 1-2 1-3 1h-2l-1-1z" class="i"></path><path d="M161 399c1-1 1-1 2-1l1 1v1h-2l-1-1z" class="D"></path><path d="M423 575h1l1-1v-1-1-1-1l1 1v2 1l1 1h-2 0v8h0c-1 1-1 1-1 2h-1l-2-1c0-1 0-2 1-3v-1-1-1l1-1v-2h0z" class="B"></path><path d="M448 481c-1 0-4-3-4-4h0c2 1 3 2 5 3h1 0l1 1v-1c-1 0-1-1-2-2 0-1-1-2-2-2v-1l1 1c1 0 2 0 2 1v1l4 4 1 1c0 1 0 2 1 3h2 0v1h-2c-1-1-1-2-3-2v-1c-1 0-1 0-2-1v-1c-1 0-2-1-2-2l-1 1z" class="F"></path><path d="M170 395h1c1 0 1 0 2-1h1c1-1 2 0 3 1v1c-1 1-6 3-6 4-1 1-1 1-3 1v-1l1-1v-1c1-1 1-1 1-3z" class="b"></path><path d="M170 395h1c1 0 1 0 2-1h1c1-1 2 0 3 1-1 0-1 0-2 1l-6 3v-1c1-1 1-1 1-3z" class="P"></path><path d="M260 547h1v3h1c0 2-1 3-1 5-1 1 0 1-1 1 0 1 0 2 1 2v1s-1 1-2 1l-3-4v-1c1 0 1 0 2-1v-1c1-1 1-2 2-4v-2z" class="L"></path><path d="M260 547h1v3h1c0 2-1 3-1 5-1 1 0 1-1 1 0-2-1-4 0-6h0v-1-2z" class="S"></path><path d="M484 602l1-1c0-1 0-1-1-2l1-1c0 1 1 1 1 1v2h0c0 1 0 2 1 2v1 2c-1 0-1 1-1 2h0c-1 2-2 3-4 4h-1v-2c1 0 1-1 2-1 0-1 0-1 1-2 0-1 0-2-1-3 1-1 1-1 1-2z" class="E"></path><path d="M174 383h1c1-2 1-3 2-4h0v3c-1 0-1 1-2 2h1 1v1c-1 1-3 4-5 5h-1-1c-1-1-1-1-1-3 1 0 2 0 2-1 2 0 2-2 3-3z" class="X"></path><path d="M165 423h1c-1 2-2 4-1 6 1-1 2-5 4-5v1c-1 1-2 3-2 4l1 1 3-6c0 2-2 5-1 6l-2 4-1-1v-1h-1v2h-1l-1-2v-1-3c0-2 1-4 1-5z" class="H"></path><path d="M484 602l-1-3v-9h1v3h3 0l1 1v3c0-1 0-1-1-2-1 1-1 2-1 3 1 1 1 2 1 3v1 2-1c-1 0-1-1-1-2h0v-2s-1 0-1-1l-1 1c1 1 1 1 1 2l-1 1z" class="P"></path><path d="M484 593h3 0c-1 1-1 2-2 3v1c0-2 0-3-1-4z" class="p"></path><path d="M256 414h0c0-2-3-3-3-5h1c1 1 1 2 2 3 0 0 1 0 1 1l1-1c2 1 2 2 3 3 2 0 3 1 3 2v1h-4 0-1c-2-1-3-3-3-4z" class="m"></path><path d="M172 412h1c1 0 3-1 5-2l-4 4c1 0 1 0 2-1 0 0 1-1 2-1v1l1-1 2-2h1c-1 2-2 3-3 4h-1v1h-1c-2 1-1 2-2 2s-2 0-2 1c0-1-1-2-1-2v-1l-1 1v-1l-1-1c1 0 1-1 2-2z" class="Q"></path><path d="M189 490h1v3c-1 2-2 3-2 5h0v1c-1 1-1 3-2 4l-1 1c0-1 0-2-1-3 1-2 1-2 0-4 2-1 3-2 3-4h0l1-1 1-2z" class="g"></path><path d="M187 493l1-1v2c-1 1-1 2-1 4-1 1-2 3-2 5h1l-1 1c0-1 0-2-1-3 1-2 1-2 0-4 2-1 3-2 3-4h0z" class="D"></path><path d="M448 600c1-2 0-2 1-3v-1l2-5 1-1c1 1 1 1 1 3 1 3-1 6-2 9h0c-1-1-1-2-1-3 0 0-1 2-2 2v-1z" class="I"></path><path d="M427 468l-2 1c-1 0-2-1-4-1-2-1-7-2-9-4l1-1c1 0 1 1 2 1v-1h-1c1-1 1-2 1-2 1 0 2 0 2 1s1 2 2 3c3 1 5 1 8 3z" class="i"></path><path d="M261 324h7l3 1c3 1 6 1 8 2h-6s-2-1-2 0h-2-3-4c-1-1-2-1-2-2h0l1-1z" class="f"></path><path d="M261 324h7c-1 1-2 1-2 2h-4v1c2 0 5-1 7 0h-3-4c-1-1-2-1-2-2h0l1-1z" class="b"></path><path d="M493 512v-1c0-1-3-4-5-6 2 1 3 2 4 3 2 2 6 6 8 9h1l3 3v3l-11-11z" class="L"></path><path d="M230 383c0 1 0 2 1 3v1l-3 6v2c-1 1-1 2-1 4-1-2-1-3-1-4l-1-1c1-2 1-5 2-7l1-3c1 1 1 1 1 3l1-4z" class="B"></path><path d="M228 384c1 1 1 1 1 3l-3 8-1-1c1-2 1-5 2-7l1-3z" class="L"></path><path d="M198 443c1-1 1-2 2-3h0v3l1 1 1-1h0v3c1-1 1-1 2-1v-1l-1 3h0c0 1-1 2-1 3h-2c-1 0-1 1-2 1h-1l1-4c1 0 1-1 1-2v-2h0-1z" class="G"></path><path d="M421 393c-1-1-2-1-3-2 3 0 6 3 9 4l1 1c1 0 1 0 2-1l3 3c-1 1-1 1-1 2l-1 1h0c-1-1-1-1-2-1-1-1-1-1-1-2l-2-2c-2-1-3-2-5-3z" class="J"></path><path d="M430 395l3 3c-1 1-1 1-1 2-1-1-4-3-4-4 1 0 1 0 2-1z" class="O"></path><path d="M117 309l-30-2h-1c11-1 21-1 31 1h-1 0l1 1z" class="f"></path><path d="M253 341h0c0-1 0-1 1-2 0 0 1 0 1 1h0c3 2 6 2 9 3 4 2 7 3 11 5 1 0 3 1 4 2-10-2-17-7-27-8l1-1z" class="H"></path><path d="M473 482l-1-1c-1-1-1-1-1-2h1l1 2c1 0 1 0 2-1l1 1v-1c2 0 3 2 4 3v2s0 1 1 1c2 1 3 3 4 4h-1l-1 1h0c-1-2-2-3-3-4s-2-2-4-3l-3-2z" class="X"></path><path d="M263 467v-1l-3-3v-1h1 0l2 2c0-2-1-2 0-2 2 1 3 2 4 4l1 1 2 2-1 1-2 1h-1c0-2 0-1-1-2v-1l-2-1h0z" class="S"></path><path d="M154 413c0-1 0-1 1-2h0 1l1-1 1-1c0-1 1-1 2-2v-1c1-1 1-2 3-2h0c-1 1-1 2-1 3l1 2c-1 1-2 2-4 3 0 0-1 0-1 1v2c-1-1-1-1-2-1h-2v-1zm273 149c1 4 1 8 1 11l1 1v1l-1-1v4c-1-1-1-2-1-3l-1-1v-1-2l-1-1v1 1 1 1l-1 1h-1l1-1v-4h0c0-1 1-1 1-2 0-2 1-4 2-6z" class="E"></path><path d="M464 441v-1c-1 0-1-1-2-2l-1-1-2-1-2-2h0c1 0 1 0 2 1h1l1 1s0 1 1 1h1s1 0 1-1c1 2 2 3 2 5h1v1c1 0 2 2 2 2 1 1 2 3 2 4s0 1-1 1l-1-1c0-1-2-2-3-4l-2-3z" class="b"></path><path d="M464 436c1 2 2 3 2 5l-1-2c-1 0-2-1-2-2 0 0 1 0 1-1z" class="J"></path><path d="M195 422c1 3 0 8-1 11-1 1-1 2-1 3-1 0-1 0-2 1h0-1c-1-3 0-5 1-8h0c0-2 1-4 1-6h1v4c-1 2-1 4-1 7 2-2 1-5 2-7l1-5zm27 54c0-2 0-4 1-6 1 2 0 5 1 7 0 1 0 3 1 4 1 2 1 3 1 6-1-1-2-2-2-4-1-1-1-4-2-4v3c1 1 1 2 2 4h-1c-1-1-2-3-2-4-1-1-1-3 0-3v-2s0-1 1-1zm261 111h1c0 2 1 3 3 4l1 1s1 0 1 1l-1 1-1-1h0-3v-3h-1v9l1 3c0 1 0 1-1 2 1 1 1 2 1 3-1 1-1 1-1 2-1-1-1-3-1-4s0-1 1-2h-1v-1h1v-1c-2-3-1-8-1-12 1 0 1-2 1-2z" class="X"></path><path d="M452 507c2 1 4 2 6 2 1 1 1 0 2 1 0 1 2 1 2 1 2 1 2 2 3 3 0 1 0 2 1 3h-1s0-1-1-2c0-1-1-1-3-1 0 0-2-2-2-3l-1 1h-1l3 3h-1l-2-2-1-1h-1c-1-2-2-3-3-5z" class="P"></path><path d="M225 362c1 0 1 0 2-1 0 2 1 4 1 6 2 5 3 11 2 16l-1 4c0-2 0-2-1-3v-4c0-5 0-14-3-18z" class="f"></path><path d="M436 444h2v-1c0-1-1-2-2-3v-1c2 1 4 4 6 5l1 1v1c1 1 2 3 4 3l1 2h-5 0 0c-1-2-2-3-2-4-1-1-2-1-3-2h0l-1-1v1l-1-1z" class="D"></path><path d="M441 447c2 0 3 2 5 3l1-1 1 2h-5 0 0c-1-2-2-3-2-4z" class="L"></path><path d="M460 450v-1c-3-1-5-3-7-6 0 0-1-1-1-2h1l1 2c2 1 3 3 5 3l3 2c1 0 1 0 2-1 1 0 2 1 3 1s1 1 2 0l1 1h-3v2c-2 0-4 1-6 0l-1-1z" class="s"></path><path d="M464 447c1 0 2 1 3 1s1 1 2 0l1 1h-3c-1 0-4 0-5-1 1 0 1 0 2-1z" class="C"></path><path d="M219 460c0-1 1-2 0-3v-1h1l1 1h0v-3-2l1-1 1 4h0l1 5v1c1 1 1 1 0 2h0-1c0-1 0-1-1-1v2h0-1c-1-2-2-2-2-4z" class="G"></path><path d="M241 385c0 1 1 2 1 4 1 2 4 3 5 4h1 0l2 2 1 1v1c-1-1-1-1-2-1-1 1-2 0-3 0h-2c-1-1-1-2-2-3v-1c-1-1-1-1-1-2v-1c-1-2-1-2 0-4z" class="B"></path><path d="M248 393l2 2h-3v-1-1h1z" class="R"></path><path d="M194 525h1l2 2-3 6v1 1c-1 0-2 0-2 1l-1 1c0-1-1-1-1-1v-2l4-9z" class="J"></path><path d="M457 474l-1-1h1l1 1c2 1 3 3 5 4h2v1c-1 1-1 0-2 1 0 1 1 1 2 2l-1 1c-2-1-3-2-4-2-2-2-4-4-6-5 2-1 2-1 3-2z" class="k"></path><path d="M457 474c1 1 4 3 4 5 0 0-1 1-1 2-2-2-4-4-6-5 2-1 2-1 3-2z" class="C"></path><path d="M241 351l1-1c2 0 3 1 5 2 1 1 3 2 4 3 3 2 6 4 9 5h-1c-1 0-2 0-3-1h-1l-1-1h-1c0-1-1-1-1-1-1-1-2-2-3-2v-1h-2 0c1 1 2 1 3 2h0 0c1 1 1 1 2 1 0 1 1 2 2 2s2 1 2 1c0 1 1 1 1 2l-8-5c-3-2-6-3-8-6z" class="i"></path><path d="M263 322c2-1 3-1 5-2l1-1v1h2c0 2 0 2 1 3h-1v2l-3-1h-7l-1 1c0-1-1-1-2-1h0c2-1 4-2 5-2z" class="k"></path><path d="M261 324h0c2-1 7-2 9-1h1v2l-3-1h-7z" class="P"></path><path d="M416 489h3l1 1h1c0 1 0 0 1 1h0 1 0 0c-3-2-5-5-6-8 1 2 4 4 6 5 0 0 0 1 1 1 0 1 0 0 1 0 0 1 1 2 2 3l1 1h1 0c2 1 3 1 4 2 2 0 3 1 4 1-2 1-5 0-7-1-4-2-10-3-14-6zm-153-44v-2h2l1-1c0 1 1 2 2 2v-2h0c1 0 2 1 2 2h1v1c1 0 1 1 2 2l1 1 1 1-1 1-1-1-1 1v2h0l-1-1h0v-2h-1l-5-5h-1v1h-1z" class="E"></path><path d="M270 489v-2c1 1 1 2 1 2l1 1v-2h0c1 1 2 2 3 2 1 1 1 1 2 1v1c0 2 2 4 1 6-2 0-3-2-4-2l-1-1c0-1-2-2-3-3 0-1 1-2 0-3z" class="F"></path><path d="M150 370l1 2c1 0 1-1 1-1 1 0 1 0 1 1l1 1h0v-1l1 1c0 1 0 2 1 3l-2 1-1 2h0v-1c-2-1-2 0-3 0 0-2 1-3 1-5-1 1-2 3-4 4 0-1 0-2 1-3l-1 1h-1v-1c2-1 2-2 3-3l1-1z" class="p"></path><path d="M154 373v-1l1 1c0 1 0 2 1 3l-2 1c-1 0-1 0-2-1l2-3z" class="F"></path><path d="M437 510c0-1-1-2-1-3l2 2c1 1 2 2 2 4 0 0 1 0 1 1 1 1 0 3 1 4l1 1-1 1h-1v-1h-1c0 1 0 1 1 2 0 1-1 1 0 2 0 1 1 1 1 2l-1 1h0l-2-2v-2-2c-1-3-1-7-2-10z" class="K"></path><path d="M255 368v1h2c1 0 3 3 4 3h0l1 1c1 0 2 0 3 1h0v1c-2 1-2 1-3 2h-1v-1c-2-1-3-2-5-3h0c-1-1-1-1-1-2l-2-2 2-1z" class="c"></path><path d="M469 594l1-1 1 1c-1 1 0 1 0 2h-1l1 4c0 2-1 4-2 5v1l-1-1-1 1c-1 0-1 1-1 1h-1v-2h0c0-1 0-1 1-2v-1l1-3 1-1v-1h0c0-1 0-2 1-3z" class="C"></path><path d="M469 594l1-1 1 1c-1 1 0 1 0 2h-1v2c-1-1-1-2-1-3v-1z" class="B"></path><path d="M107 324c-9 1-17 2-26 4-3 1-7 3-11 4v-1h2l12-4c7-2 14-4 21-4h3c0 1 0 1-1 1z" class="L"></path><path d="M415 449c1-1 1-1 2 0h1c1 0 1 1 2 1l2 1 1 1h1c0 1 1 1 1 1l2 1v1c1 0 1 0 2 1h-1c-1-1-3-1-4 0-2 0-2 0-4-1h-1 0c-1-1 0-1-1-1l-1-1c-1-1-1-2 0-3h0l-2-1z" class="V"></path><path d="M427 395c-1-1-2-2-4-3-1-1-2-1-3-2-3-3-7-7-10-11 7 4 14 11 20 16-1 1-1 1-2 1l-1-1z" class="a"></path><path d="M445 489c-1 0-1-1-2-1l-1-1h0-1c-2 1-6-3-8-3-1-1-2-2-4-2 0 0-1 0-1-1l-2-2c-1 0 0 0-1-1l-2-1c-1 0-2-1-3-2h0 1l5 3h1c1 0 2 1 3 1 1 1 1 1 2 1v1c0 1 1 1 2 1l4 2 3 2c1 0 2 1 2 1 2 0 2 1 2 2z" class="e"></path><path d="M432 504c0 1 1 1 1 2s0 1 1 1c0 1 0 2 1 3h1c0 1 0 1 1 1v-1c1 3 1 7 2 10h-1c0-1-1-1-1-2v-3h0c-2-1-3-2-4-3v3 1h-1l-1-2h0v-2l-1-1v-1c-1-1-1-2-1-3l1 1c0 1 0 1 1 2v-1h1v1l1-1h0c0-1-1-2-1-2v-2h-1l1-1z" class="P"></path><path d="M433 512v-1h1 2c0 2 0 3 1 4-2-1-3-2-4-3z" class="X"></path><path d="M263 445h1v-1h1l5 5h1v2h0c-1 0-1 0-2-1h0c-1 0-1 1-3 1 0 1 1 1 2 2h-1l-3-2-2-2c0-1-1-2-2-3h0 0 1c1 1 1 1 2 1h0l-1-1v-1h1z" class="D"></path><path d="M448 442l3 3v-1l-2-2h1 1c1-1 0-2-1-3v-2c1 1 1 1 1 2 2 0 2 1 4 1v1s0 1-1 1v1l-1-2h-1c0 1 1 2 1 2 2 3 4 5 7 6v1l-4-2-1 1c0-1-2-1-3-2v1l-1-2-3-3v-1z" class="F"></path><path d="M460 580v1c1 4-1 8-1 12 2-2 3-4 3-8 0 0 0-1 1-1 0 3-1 6-1 9-1 1-1 2-2 3h1v2 1 1c-1-1-1-1-1-2-1-1-1-2-1-3v-1c-1 0-1 1-1 2-1-1-2-2-2-3v-2c1 0 1 0 1-1h0c2-1 1-3 2-4 0-2 1-4 1-6z" class="X"></path><path d="M120 309h0c2 0 3 1 4 1h0v1c1 1 2 1 4 0v1c0 1-1 1-2 1h-1v1h2 1c-1 1-1 1-2 1v1h0c1 0 1 0 2 1h-1v1 1h1 1-3 0c-1 0-2-2-3-3h0-1-1l-1-1 1-2c0-1-1-3-2-4h1z" class="H"></path><path d="M217 471c2 2 1 3 2 5 0 1 1 2 1 3 1 1 0 2 0 3v1c0 1 1 2 1 4h-1c-1-1-1-2-1-3l-1-1v4h0l-2-2h0v-2h1c-1-2-2-4-1-6s1-4 1-6z" class="X"></path><path d="M182 525h0 1c1 0 1 0 1 1l1 3v-1-3h1v1c1 2 1 3 1 5 1 1 1 2 1 3-1 1-1 2-2 2s-1-1-2-2l-1 1v-3h-1v-3h0v-4z" class="D"></path><path d="M186 531h0 1c1 1 1 2 1 3h-1c-1-1-1-2-1-3z" class="Z"></path><path d="M186 526c1 2 1 3 1 5h-1 0v-4-1z" class="K"></path><path d="M182 525h0 1c1 0 1 0 1 1l-1 1c0 2 1 5 1 7l-1 1v-3h-1v-3h0v-4z" class="e"></path><path d="M502 628h-1c1 1 2 1 2 2h1c1 0 2 1 2 1v1c-1 0-1-1-2-1l-1-1-2-1h-1l-1-1-3-2-1 1v-1c-1-1-1-1 0-2 0 0-1-1-1-2-1 0-2 0-3-1v-2h1v1h1c0-1 0-1 1-2v-1c0-1 0 0 1-1 0 1 1 2 1 4v2c1 2 4 5 6 6z" class="S"></path><path d="M174 481c1 0 1-1 2-1 0 1-1 1-1 3h1c1-1 2-1 2-2 1 0 1 1 2 1 0 2-1 4-2 5l-1 1v-1c-1 1-1 2-1 3l-1 1v-1h-1-1c1-1 2-2 2-3h0l-1 1-1-1v-1l-1 1v-1-1l-1-1 3-3z" class="C"></path><path d="M174 481v2c-1 1 0 1 0 2s0 1-1 2v-1l-1 1v-1-1l-1-1 3-3z" class="F"></path><path d="M202 496v-2l1 1 1 3 1 1c1 0 1 1 1 2v2c-1 1-1 2-1 3-1 0-1 3-1 3l-2 1v1l-1 1c0-1-1-1-2-2 1-3 3-5 3-8h0v-6z" class="d"></path><path d="M204 498l1 1c1 0 1 1 1 2v2c-1 1-1 2-1 3v-3c-1-1-1-2-1-3v-2z" class="o"></path><path d="M202 496v-2l1 1 1 3v2c0 1 0 3-1 4h0l-1-2v-6z" class="N"></path><path d="M234 525l2 2h1l-1-1-3-4c2 1 3 2 4 4l1-1h-1v-2h0c1 1 2 3 3 4 2 1 3 2 4 4h-1c-1-2-3-3-5-4h-1l4 4h-1l-1-1-1 1c-2 0-3-1-4-2s-2-2-3-2h-1v-2h1 3z" class="Z"></path><path d="M231 527h-1v-2h1 3l-1 1c0 1 2 2 3 4 2 0 2-1 3 0h0l-1 1c-2 0-3-1-4-2s-2-2-3-2z" class="B"></path><path d="M271 241c2 1 4 2 7 3h0l1 1h-1c-1 1-1 1 0 2v1l-1 1h-1v2s-1 0-2-1v-1c-1 0-1 0-1-1l-1-1c0-1-1-1-1-1l-1-1-1-1h-1l2-1v-1s1 0 1-1z" class="D"></path><path d="M278 244l1 1h-1c-1 1-1 1 0 2l-3-1v-1c1-1 2 0 3 0v-1z" class="S"></path><path d="M271 241c2 1 4 2 7 3h-2c-2-1-4-1-6-1v-1s1 0 1-1zm-1 4c2 0 3 0 5 1l3 1v1l-1 1h-1v2s-1 0-2-1v-1c-1 0-1 0-1-1l-1-1c0-1-1-1-1-1l-1-1z" class="J"></path><path d="M272 247c2 0 4 1 5 2h-1v2s-1 0-2-1v-1c-1 0-1 0-1-1l-1-1z" class="D"></path><path d="M450 478h1l2 1c0-1 1-1 2-2 1 3 3 4 5 5l3 3h-1l1 1c0 1 0 1-1 1l-4-1h0-2c-1-1-1-2-1-3l-1-1-4-4z" class="s"></path><path d="M455 483c2 1 5 2 7 4l-4-1h0-2c-1-1-1-2-1-3z" class="X"></path><path d="M455 477c1 3 3 4 5 5l3 3h-1c-3-2-7-3-9-6 0-1 1-1 2-2z" class="C"></path><path d="M480 591c1-1 1-2 1-2h1c0 4-1 9 1 12v1h-1v1h-1c-1-1-1-2-2-3v1l-2-4v-1l2-6 1 1z" class="f"></path><path d="M480 591c1-1 1-2 1-2h1c0 4-1 9 1 12v1h-1c0-1 0-2-1-3v-2-3h-1s0 1-1 1c0-1 1-3 1-4z" class="P"></path><path d="M441 588c0-1 0-2 1-3 0-1 0-2 1-2v1l1 2c0 1 0 2 1 2v1 3l1 1v1h-2l-1 1-2 4c-1 1-1 1-1 2l-1-1c1-1 1-2 1-3 1 0 1-1 1-2h-1c0-2 0-3 1-4h0v-3z" class="I"></path><path d="M445 588v1 3l1 1v1h-2v-1c1-1 1-4 1-5z" class="E"></path><path d="M441 588c0-1 0-2 1-3 0-1 0-2 1-2v1 1c-1 2 0 6-2 8v-2h0v-3z" class="b"></path><path d="M182 440c0-1 1-1 1-1l1-1v1c0 2 2 4 2 7l1-1v-2c1 0 1-1 2-1l2 1h-1l-1 1v4c0 1 0 2-1 3l-1 1c0-1-2-4-2-5 0 0 1 0 1-1h-1c-1 0-1 0-2-1s-2-3-2-4l1-1z" class="Z"></path><path d="M187 443c1 0 1-1 2-1l2 1h-1l-1 1v4c0 1 0 2-1 3h0l-1-1v-2l1-1c0-2 0-3-1-4z" class="i"></path><path d="M175 355c0 1 1 1 1 1l1 1c-7 4-12 9-17 15l-5 7-2 2h-1c0-1 0-1 1-2h0l1-2 2-1c0-1 1-2 2-3 2-3 4-6 7-8 1-2 4-4 5-5 2-1 4-3 5-5z" class="s"></path><path d="M169 346c0 1 0 2 1 3h3l1 1-1 1v1l-2 1-1-1-7 3v-1l1-1c-1 0-1-1-2-1s-1 0-2-1c2-1 5-2 7-1v-3-1h2z" class="W"></path><path d="M160 351c2-1 5-2 7-1 1 0 1 0 2 1l-5 2c-1 0-1-1-2-1s-1 0-2-1z" class="R"></path><path d="M174 488l1-1h0c0 1-1 2-2 3h1 1v1l1-1c0-1 0-2 1-3v1c1 1 1 1 1 2s-1 1-1 2h0c-1 1-3 3-5 3v-1s0-1-1-1-2 1-3 1h-1-2 0l3-3-1-1c1 0 2-1 2-1l2-1h0l1 1 2-1z" class="Q"></path><path d="M169 489l2-1h0l1 1c-1 1-2 2-3 2h-1l-1-1c1 0 2-1 2-1z" class="E"></path><path d="M183 483h1v1c1 0 1 0 2-1h0l-1 4v1 2l-2 2-1 2c0 1-1 0-1 0-1 1-1 2-2 2v1h-1v-1l1-1-1-1v1h-1v-1c0-1 1-2 2-2h0l1-2 1-1v-2c1 0 1 0 1-1s1-2 1-3zm379 171l1-1v-1c-1 0-2-1-3-1h-1v-1h-1 0l1 1-1 1h-1c-2-1-4 0-6-1 0 0 0-1-1-1h-1-1c-1 0-2-1-3-1v-1c2 0 3 1 4 1h1c2 1 5 1 7 1v-1h-3c-3 0-7-1-9-3h0c1 0 2 0 4 1h1 1 1l1 1h2v-1c4 1 8 3 12 5-1 0-2 1-2 1-1 0-2 1-3 1z" class="D"></path><path d="M266 233c2-1 4-1 6 0 0 1 0 1 1 1 2 0 3 1 5 2v1c-3-1-5-2-8-2 2 1 6 2 8 3h-3c-2-1-4-1-5-1h-2-2v-1h0-3-3c2-2 4-2 6-3z" class="d"></path><path d="M213 437l1-4h0v11c0 1 0 1 1 2-1 1-1 0-2 0s-1 1-2 1v-4h-1c0 2 1 5-1 6h0c-1-1-1-1-1-2 1-1 1-3 1-5 0-1 0-2 1-3v-2-1h1l1 1h1z" class="l"></path><path d="M213 437v5h-1l-1-1 1-4h1z" class="B"></path><path d="M210 436h1l1 1-1 4c0-1-1-1-1-2v-2-1z" class="r"></path><path d="M262 426h2s0-1 1-1l-1-1v-1h1v1h1c2 0 3 1 4 2h0c2 1 3 2 3 3l1 2h0l-2-1h0c-1 0-1 0-2-1v1 1h1v1l-1 1-2-3h-3c0-1-2-2-3-3v-1h0z" class="i"></path><path d="M261 440s-1 1-2 1c-1-1-2-1-3-1v1h-1c0-1-1-1-2-2s-1-2-2-3h0v-1l-2-2v-1h1v1h1v-2h1v1c1-1 1-1 1-2h0 0c1 1 1 2 2 2h0c0 2 1 3 2 4h0c1 2 3 3 4 4z" class="P"></path><path d="M226 450h0c0-1 1-1 1-1l1-1v1h1c1 0 1 1 2 1-1 1-1 2 0 3v2s0 1 1 1v1h0v1 2 1c-1-1-1-1-2-1v2h-1c-1-1-1-2-1-3l-1-1v-1l-1-1v-3-1-2h0z" class="G"></path><path d="M226 450h0c0-1 1-1 1-1l1-1v1h1v2h-1-1l-1-1h0z" class="V"></path><path d="M416 520c0-1-1-2-2-4l1-1 2 1v-1-1-1h2c0-2 0-1-1-3v-1h0 3c0 1 1 2 1 2l-1 1v2l1 1v1l-1 3h-1v1 2h-1s-1 0-1-1l-2-1z" class="E"></path><path d="M418 521v-5l1 1c1 1 1 1 1 2v1 2h-1s-1 0-1-1z" class="K"></path><path d="M131 334h2v1 1l1 1h0l-1 1c0 2 0 3-1 5 0 1-1 3-1 4v1c1 0 1-1 2-1h0 0c0 1 0 2-1 2h2l1 1-2 1-3 1v1l-1-1-1-2c0-4 2-8 3-12l-1-1h-1l1-1c1-1 1-1 1-2z" class="N"></path><path d="M432 400c0-1 0-1 1-2l11 12c2 3 5 5 7 8l-1 1-3-2c-2-3-3-5-6-7l-5-4c-1-2-3-3-5-5l1-1z" class="H"></path><path d="M472 450h0c1-1 1-1 1-2 0 1 0 1 1 2v-1c5 7 9 15 13 22l-1 1-1-1v-1s-1-1-2-1l-2-2v-1c1 1 1 1 2 1-1-2-3-5-4-6-1-2-2-5-4-7l-3-3v-1z" class="N"></path><path d="M224 394l-1-1v-1-2c1 0 1-1 1-1l1-2v-1c1 0 1-1 0-1l-1 1v1c-1 1-1 1-1 2v1l-1 1v1 1c-1 1-1 2-2 3v-1-2h-1v2l-1-1v-1h0c0-1 0-1 1-2v-2l2-2c0-2 1-3 1-4 1-1 2-1 3-1l2 2v3l-2 2v1 1l-1 3z" class="B"></path><path d="M204 413l-1-1v1c-1 1-1 2-2 2h-1c0-1 1-2 1-2l2-5c1-1 1-1 2-1 0-1 0-1 1-1v2c1-1 1-1 2-1 0-1 0-1 1-1h1 1v2 1c0 1-1 1-1 2s0 1-1 2c-2 0-2 0-3 1h-2v-1z" class="F"></path><path d="M204 413l3-6c0 3-1 5-1 7h-2v-1z" class="l"></path><path d="M160 372c1 1 2 2 2 4 2-1 1-2 3-2-1 1-1 2-2 3 0 1-1 1-1 2h0l-1 2c0 1-1 1-1 1 0 1 0 1-1 1v1l-1-1h0c1-1 1-1 1-2l-2 2c-1 0-1-1-2-1 0-1-1-1-2-1l2-2 5-7z" class="C"></path><path d="M155 382l2-4 2 2v1l-2 2c-1 0-1-1-2-1z" class="Q"></path><path d="M262 386c1 0 2 0 2 1l1 1s-1 1 0 1c1 1 1 2 1 3h1 1 2l1 1c1 1 2 3 3 5 0 1-1 1-2 2 0 0 0-1-1-1h-1v1h0v1l-2-2h-1c0-2-1-4-2-6s-2-5-3-7z" class="R"></path><path d="M465 478h0c0-1 0-1 1-1 0-1-1-1-1-2-1 0 0 0 0-1h0l6 3c1 0 3 2 4 3-1 1-1 1-2 1l-1-2h-1c0 1 0 1 1 2l1 1c-1 1-1 1-2 1l-2-2-1 1c0 1 0 2 1 2 0 1 0 1 1 1h-3l-2-3c-1-1-2-1-2-2 1-1 1 0 2-1v-1z" class="B"></path><path d="M469 481c-1 0-1 0-2-1l-1-1 1-1c1 1 2 2 3 2h0c0-1 0-2 1-3 1 0 3 2 4 3-1 1-1 1-2 1l-1-2h-1c0 1 0 1 1 2l1 1c-1 1-1 1-2 1l-2-2z" class="D"></path><path d="M266 471h-2l1 1h-1l-3-2v1c1 1 2 1 2 2v1c-1-1-4-3-5-5s-3-4-5-6l1-1c1 1 2 2 3 4h0 1l-1-1v-2c0-1 0-1-1-2h1l6 6h0l2 1v1c1 1 1 0 1 2z" class="K"></path><path d="M138 391c0-1 0-1-1-1s-1-1-2-1l1-3 1-1v-1-1l1-1c0-1 0-2-1-3v-2h1l-1-1-1-1c1 0 1-1 2-1h1l-1 1c1 2 0 2 0 3l1 1c0 1 0 2 1 3h0c1 0 1 0 2 1h0l1-1c0 2 0 3-1 5-1 1-1 3-1 4l-2 2-1-2z" class="G"></path><path d="M139 388v-2l1-1h1l-1 3h-1z" class="C"></path><path d="M142 383l1-1c0 2 0 3-1 5-1 1-1 3-1 4l-2 2-1-2v-3h1 1l1-3 1-2z" class="Q"></path><path d="M436 576v4h0c1-1 1-1 1-2v7l-1 7h-1 0c-1-1 0-1-2-3v-2c-1 1-1 1-1 2s0 1-1 2h0c0-1-1-3 0-3v-1-1-1l1-1v-1h0c0-2 1-3 1-5h1 1v1h0l1-3z" class="D"></path><path d="M433 578l1 1v4h-2c0-2 1-3 1-5z" class="t"></path><path d="M434 585c1 0 1 0 1 1 1 0 1 0 2-1l-1 7h-1 0c0-1 0-3-1-5v-2z" class="K"></path><path d="M436 576v4h0c1-1 1-1 1-2v7c-1 1-1 1-2 1 0-1 0-1-1-1v-1c1-1 1-3 1-5l1-3z" class="g"></path><path d="M234 491l1 3c0 1 1 1 1 2h0c0 1 1 2 1 2v1c0 1 1 2 2 2s1 1 2 2h0l-1 1c0-1-1-1-1-2h-3c0 1 0 2 1 3v1h0l-1 1c0-1-1-1-1-2-2-3-3-5-4-8 0 0 0-1-1-1v-2h0c1 1 1 2 3 3 0 0 0-1-1-2l-1-3 1-1c1 1 1 3 2 4 0-2 0-3-1-5l1-1v2h0z" class="G"></path><path d="M191 466v2l2 1c0 1 0 1 1 2h0c-1 1-1 2-2 3v2h0c0 2-1 2-2 4 0 1 0 1-1 1h0v-1l-1-1h-1v-1c0 1-1 1-1 1v1h-1c0-2 2-2 2-4 0-1-1-1-1-1 0-1 0-3 1-3v-1h2c0-1 0-2 1-3 0-1 0-1 1-2z" class="F"></path><path d="M191 468l2 1v2h-1l-1 1h0l-1-1c0-1 0-2 1-3z" class="e"></path><path d="M190 471l1 1h0l1-1-2 5s0 1-1 2l-1-1c1-1 1-1 1-2 0 0 0-1 1-2h0v-1-1z" class="B"></path><path d="M188 389v-1l1 1c-2 2-3 4-5 6-1 1-5 4-6 5l-1 2h-2v-1c-1 1-1 1-3 1h0v-1c0-1 0-1-1-1 0-1 5-3 6-4v-1l3-3v2c1 1 1 1 2 1l6-6z" class="X"></path><path d="M177 395l3-3v2c1 1 1 1 2 1-1 0-1 1-2 1-1 1-2 2-3 2-1 1-3 2-4 3h-1c0-1 0-1-1-1 0-1 5-3 6-4v-1z" class="D"></path><path d="M216 511c1 1 1 1 2 1l1-1h0 1c0 4 2 7 5 9-2-3-3-6-4-9 2 3 6 8 7 11-2 0-4-1-6-3v1l1 2c-1 0-1 0-2-1l-1-2h-1v1 1h0l-2-1c-1-2-1-6-1-8v-1z" class="f"></path><path d="M216 511c1 1 1 1 2 1l1-1h0 1-1c0 3 1 5 2 7h-1c-2-1-3-3-4-6v-1zm-56-125c1-1 2-2 2-3l4-8 1-1v1h1c0 2 0 3-1 4v1l1-1h1 1 1c0 1 0 1-1 2-1 2-2 4-3 5s0 2-2 2l-1-1h0-2v-1h0-2z" class="E"></path><path d="M432 480c1 0 1 1 2 1h2v-1c1 0 2 0 3 1v-1c-2-1-4-3-6-5l-1-1c3 1 7 5 9 7h0c1 2 3 4 5 5h0c-1 1 0 1 0 2l3 3 3 2h0v1c-3-1-5-3-7-5 0-1 0-2-2-2 0 0-1-1-2-1l-3-2-4-2c-1 0-2 0-2-1v-1z" class="G"></path><path d="M181 529h1 0v3h1v3l1-1c1 1 1 2 2 2-1 2-2 3-2 4s-1 2-1 3c-2 1-2 3-3 5l-2 3c0 1-1 2-2 2h0c0-1 0-1-1-1v-1s0-1 1-1h1l1-2v1c0-2 1-3 1-4 0-2 2-7 1-8v-1-1h1v1h0 1c-1-1 0-2 0-4 0 0-1-1-1-2v-1z" class="P"></path><path d="M178 549c1-1 1-2 2-3 0-1 0-2 1-4h0c0-1 0-3 1-3 0 1 0 2 1 4-2 1-2 3-3 5l-2 3c0 1-1 2-2 2h0c0-1 0-1-1-1v-1s0-1 1-1h1l1-2v1z" class="Z"></path><path d="M526 482c-1 0-1-1-2-2-3-4-8-12-7-17h0c2 2 3 5 5 8 4 6 8 7 15 8h-3 0c1 0 1 1 2 1h-2-1c-2-1-4-1-6-2-1-1-1-1-2-1 0 1 1 1 1 2s1 2 0 3z" class="N"></path><path d="M454 577h0l1 1c0-1 0-1 1 0h1v-1c0 3 0 6 2 9-1 1 0 3-2 4h0c0 1 0 1-1 1h-1v-1-1h-1v1h-1v-7c-1-1-1-2-2-4l2-2h0 1z" class="m"></path><path d="M456 582h-1c0-1 0-2 1-3h0v3z" class="R"></path><path d="M457 577c0 3 0 6 2 9-1 1 0 3-2 4h0v-4-4h-1v-3l1 1h0v-2-1z" class="D"></path><path d="M453 577c0 2-1 4 0 5 0 1 1 2 1 2v6h-1v-7c-1-1-1-2-2-4l2-2z" class="X"></path><path d="M455 591v-6c0-1 0-1 1-2h0c0 1 1 2 1 3v4c0 1 0 1-1 1h-1z" class="b"></path><path d="M253 430l2-1 1-1c1 0 2 1 2 2 1 1 3 3 4 3l1 1h0l1 1c1 1 1 2 2 2v1c-1 1-2 1-3 1v1h-1-1c-1-1-3-2-4-4h0c-1-1-2-2-2-4h0c-1 0-1-1-2-2h0z" class="K"></path><path d="M262 440v-1-1c-1 0-3-1-3-3 1 0 1 2 3 2v-1h1l2 2 1-1v1c-1 1-2 1-3 1v1h-1z" class="V"></path><path d="M253 430l2-1 1-1v2c1 0 1 0 1 1 0 0 1 1 1 2v1c-1-1-2-1-3-2h0c-1 0-1-1-2-2h0z" class="D"></path><path d="M253 430l2-1 1-1v2 1h-1l-1-1h-1 0z" class="E"></path><path d="M264 546v3 1c1 0 2-1 2-2v-1l1 1c0 1 0 1-1 2v1h1l1-1v2 3h0c-2 2-2 5-3 6v-1c-1 1-1 1-1 2h-1v-3l-1-1-1 1v-1c-1 0-1-1-1-2 1 0 0 0 1-1 0-2 1-3 1-5 1-1 1-3 2-4z" class="D"></path><path d="M264 546v3l-1 2c-1 1-1 5-1 6h0c1-1 1-2 2-2v2c-1 1-1 1-1 2l-1-1-1 1v-1c-1 0-1-1-1-2 1 0 0 0 1-1 0-2 1-3 1-5 1-1 1-3 2-4z" class="n"></path><path d="M268 550v2 3h0c-2 2-2 5-3 6v-1c-1 1-1 1-1 2h-1v-3c0-1 0-1 1-2v1c1-1 2-3 2-4s0-2 1-3h0l1-1z" class="l"></path><path d="M422 511c0 1 1 2 1 3h1v-3c1-2 1 0 2-1l2-2v-2c-1 0-1-1-1-1v-1c1 0 1 0 1 1 1 0 1 1 1 2s0 2 1 3v1l1 1v2h0l1 2c-1 1-1 2-2 3-1-1-2-2-2-3v-1h-1v3 1h-1s-1-1-2-1c0-1-1-2-2-3l-1-1v-2l1-1z" class="F"></path><path d="M126 310h2l11 2c-1 0-1 0-2 1l1 1c1 1 1 1 2 0h1v2h0l-1-1h-2v1 1h-1-7-1-1c-1-1-1-1-2-1h0v-1c1 0 1 0 2-1h-1-2v-1h1c1 0 2 0 2-1v-1c-2 1-3 1-4 0h0l2-1z" class="p"></path><path d="M126 310h2l11 2c-1 0-1 0-2 1l1 1c1 1 1 1 2 0h1v2h0l-1-1h-2-2-7v-1c1 0 2 0 3-1h2 2c-1-1-4-1-5-1s-1 0-2 1v-1c0-1-1-1-1-1h0c-2 1-3 1-4 0h0l2-1z" class="m"></path><path d="M171 484l1 1v1 1l1-1v1l1 1-2 1-1-1h0l-2 1s-1 1-2 1c-4 2-8 2-12 3h-3c0-1 1-2 2-2l-1-1h2 2c3-1 4-2 6-3l2-1c1-1 2-2 3-2l-5 4 5-2 1-1h1l1-1z" class="J"></path><path d="M171 484l1 1v1 1l1-1v1l1 1-2 1-1-1h0l-2 1c-1-1-2 0-4 1h-1s-1 1-2 1-1 0-2-1h3c1 0 2-1 2-1 2-1 3-2 3-3l1-1h1l1-1z" class="I"></path><path d="M177 357h0c-1 1-2 3-3 4-2 2-4 5-5 7s-3 4-3 6h-1c-2 0-1 1-3 2 0-2-1-3-2-4 5-6 10-11 17-15z" class="e"></path><path d="M162 519c-1-5-1-10-2-15 0-3-2-7-2-10h0c2 0 2 0 3 2h0 1v-1h1v2 2h0c-1 1-1 1-1 2h1v-1h2 0c-1 1-1 3-1 4h1l-1 1c-1 2 0 7 0 10l-1-1c0 2 0 3-1 5z" class="U"></path><path d="M161 496h0 1v-1h1v2 2h0c-1 1-1 1-1 2h1v-1h2 0c-1 1-1 3-1 4h1l-1 1c-1 2 0 7 0 10l-1-1c0-3 0-7-1-10 0-2-1-5-1-8z" class="v"></path><path d="M197 434l-1 1h0v-1h-1v1h-1v-2h1v-2s1-1 1-2h0v-1c1-1 1-4 2-5v-2h1v1c1 0 1 1 2 1v-1c0-1 0-1 1-1h0v1l1 1v3c-1 2-3 5-2 7h0c-1 1-1 0-2 1v2h-1v-2h-1z" class="R"></path><path d="M197 434h-1c1-1 1-2 2-3v-1l1-1c0-1 0-2 1-2v-2c1-1 0-1 1-2v1 2h2c-1 2-3 5-2 7h0c-1 1-1 0-2 1v2h-1v-2h-1z" class="c"></path><path d="M114 339c-5 1-10 1-15 2-8 2-16 4-24 7-7 3-15 6-22 10h-1v-1c2-1 5-2 7-3 16-8 33-13 51-16 2 1 2 1 4 1z" class="L"></path><path d="M150 387l1 1c1 1 1 1 1 0h1 1c1 0 1 1 2 2l-1 1h1 1c-1 1-2 2-3 4-1 1-2 2-2 3l-1-1h-1-1v-1l1-1h0-1l-2 2c-2 0-2 0-4-2 3-1 5-6 7-8z" class="B"></path><path d="M417 398l-3-4c1 1 2 3 4 3 0-2-2-2-2-4 1 0 1 1 3 2l2 2h1 1l-1-1h0c-1 0-2-2-3-3h1 1c2 1 3 2 5 3l2 2c0 1 0 1 1 2 1 0 1 0 2 1v2l-1 1v-1h-3-1l-2-1h-2l-1-1c-1-1-3-2-4-3z" class="X"></path><path d="M427 403l-2-2v-1c1 0 1 1 2 0l-1-1c1 0 2 0 3 1 1 0 1 0 2 1v2l-1 1v-1h-3z" class="j"></path><path d="M458 560c0 1 0 2 1 3h1v1c0 1 1 1 1 2h-1c1 2 0 0 1 1s2 3 2 4 0 3 1 4v3c-1 2-1 3-1 6h0c-1 0-1 1-1 1 0 4-1 6-3 8 0-4 2-8 1-12v-1-1c0-1 1-1 1-2-1-3-2-6-2-10 0-1-1-2-1-4v-3z" class="E"></path><path d="M458 560c0 1 0 2 1 3h1v1c0 1 1 1 1 2h-1c1 2 0 0 1 1s2 3 2 4 0 3 1 4v3c-3-3-1-8-4-11v1c1 2 1 4 1 7 1 1 1 2 1 3l-1-1c-1-3-2-6-2-10 0-1-1-2-1-4v-3z" class="Z"></path><path d="M200 395v2h1s0-1 1-1c0-1 1-1 1-2 0 2-1 2-2 3v1 1l1 1v1 1l1 1v1h-1l-2 2 1 1c-1 1-1 2-2 2v1l-1 1v1 1c-1 0-1 1-2 1v1h-1c0 1 0 1-1 2 0-1-1-1-2-1l-1 1-1-1 2-1 1-3c1-1 3-2 4-4 1-1 1-2 2-3h1v-1h-1c0 1-1 1-1 1h-1l2-2h1v-1c-1 0-1 0-1-1h0c-1-1-2-2-3-2h0l4-4z" class="B"></path><path d="M490 497c0-2-1-3-2-4l-4-5-3-3v-1h1 1l5 7h0v-1c-2-2-3-6-3-9 0-2-2-4-3-6 1 1 2 3 4 4 1 2 3 4 3 7h0v1h0c2 4 3 9 6 12 1 1 3 2 3 3v1l-2-2-4-4h-1-1z" class="K"></path><path d="M210 524h2 0v2h2v-1h1l1 2 2 2 3 3 2 2 2 2 1 1h0-1l-1-1h-1l-1 1c0 1 0 1 1 1l3 3h0l-1 1c0-1-1-1-1-1v-1c-1 0-2-1-3-2-2-3-4-5-7-8h0c0 1 2 3 2 4-1 0-1-1-2-2-1 0-2-1-2-2-1-1-2-3-3-5l1-1z" class="c"></path><path d="M210 524c1 1 2 2 2 3v2 1c-1-1-2-3-3-5l1-1z" class="K"></path><path d="M202 380h1 1v3h1l1-1c0-1 0-1 1-2l1 1h-1v4h0l1-2c1 1 1 1 1 2v1c0 1 0 1-1 2v1l-1 1c0 1-2 4-2 5l-2 2v1l-1 2-1-1v-1-1c1-1 2-1 2-3 0 1-1 1-1 2-1 0-1 1-1 1h-1v-2c1-1 1-1 1-2 0-2 1-3 1-4l1-1c0-1 1-4 0-6h-1c0-1-1-1 0-2z" class="v"></path><path d="M194 387c1 0 1 1 1 2h-1v-1 1c0 1 0 2-1 3 0 1 0 2-1 3v-1l-2 2c-1 1-1 2-2 2l-3 2-1-1-2 2-2 1-1-1h0l-1-1c1-1 5-4 6-5 2-2 3-4 5-6v1c0 1 1 1 1 1l4-4z" class="C"></path><path d="M187 396h3c-1 1-1 2-2 2l-3 2-1-1 3-2v-1z" class="B"></path><path d="M194 387c1 0 1 1 1 2h-1v-1 1c0 1 0 2-1 3 0 1 0 2-1 3v-1l-2 2h-3v-1l2-1v-1c0-1 1-1 1-2l4-4z" class="F"></path><path d="M206 503v1c1 1 1 1 1 2l1 1 1-1c1 1 0 4 0 5 1 1 1 3 1 4v1c0 1 0 1 1 2l-1 2h-1c0-1 0-2-1-2h0-3v-1-1l-1-1-1-1h0 0-1l2-5s0-3 1-3c0-1 0-2 1-3z" class="Z"></path><path d="M208 507l1-1c1 1 0 4 0 5h0c-1 0-1 0-1 1s0 1-1 2l-2-2h0c-1 1-1 2-1 3l-1-1 1-1c1-1 1-1 2-3 1-1 1-2 2-3z" class="k"></path><path d="M206 503v1c1 1 1 1 1 2l1 1c-1 1-1 2-2 3-1 2-1 2-2 3l-1 1h0 0-1l2-5s0-3 1-3c0-1 0-2 1-3z" class="E"></path><path d="M221 436l-1-2v-1l-1-4v-1l1 1 1 1v-3h1s1 1 1 2h0 1c0-1-1-2 0-3 1 1 2 2 2 3v1 1l1 1h0v1c1 1 1 2 1 3-1 1 0 0-1 0-2 1-2 1-3 2h-1v-1 2h0l1-1c1 1 1 1 1 2 1 2 1 2 1 3-1 1-2 0-3 1h0c-1-1-1-2-1-3h0l-1-2v-3z" class="V"></path><path d="M119 279h1c-1 2-1 4 0 6v1 1 1h0 1c1 0 2-1 3-1v1h-1l-3 3h0l2-1 1 1c-1 0-1 1-2 1h2v1 1c0 1 0 1 1 2h-2v1c0 2-2 6-1 8h0l-2 1v-2 2c-2-3 1-7 1-10 0-1-2-3-3-4 0 0 1-3 1-4-1-3-1-6-1-8l2-1z" class="J"></path><path d="M149 385l2-3h1c0 2-2 4-2 5-2 2-4 7-7 8 0 1-1 2-2 2 0 1 0 2-1 2 0 1-1 1-1 2v1h1v1h0v1c0 1 0 1-1 2h0c2-1 3-3 5-4h1c-2 1-3 3-4 4 0 1-1 1-1 1-1 0-1 0-2-1v-2h0l-2 2-1-1c0-1-1-1-2-1h0v-1c0-1 1-3 2-4l-1-2h2v1h1v-1l1-1c1 1 1 1 2 1l2-2c1 0 1-1 2-2 0-1 1-2 1-2l4-6z" class="Q"></path><path d="M134 397h2v1h1v-1c1 1 1 1 1 2s-1 1-1 1l-1 1v1h-1v-3l-1-2z" class="C"></path><path d="M135 399v3h1v-1h2v2 1l-2 2-1-1c0-1-1-1-2-1h0v-1c0-1 1-3 2-4z" class="q"></path><path d="M592 489c3 1 5 1 7 2 10 5 19 12 27 19h-1l-3-1s0 1-1 1c-1-2-2-3-4-5-3-3-7-5-10-7-3-1-6-3-10-5h0l-4-2-1-2z" class="H"></path><path d="M592 489c3 1 5 1 7 2-1 1-1 1-2 1-1-1-1-2-2-1h1l2 2h-1l-4-2-1-2z" class="U"></path><path d="M541 640l-1-1c-1-1-1-1-1-2 1 0 3 1 4 2 2 0 3 1 4 1 3 2 6 3 9 5s7 4 11 5v1h3c3-1 6-2 8-3l3-1c0 1-1 1-2 2h-1l-2 1h0c0 1 1 1 1 1-1 1-2 2-4 2h-1l-2-1-1 1c-1 0-1-1-2-1-4-2-8-4-12-5l-10-3c3 0 7 1 10 2-1-1-3-2-4-2-1-1-3-1-3-1 0-1 0-1-1-2-1 0-4-1-6-1z" class="q"></path><path d="M570 652l3-1v2h-1l-2-1z" class="g"></path><path d="M573 651l3-1c0 1 1 1 1 1-1 1-2 2-4 2v-2z" class="h"></path><path d="M165 407c1-1 4-3 4-4h1v1l-1 1-1 1h0l2 1h0l-3 6c2-1 4-3 6-4l-2 3h0 1c-1 1-1 2-2 2l-2 2h-1l-1 1h-1v-2h-4 0v-1c-1 0 0 0-1 1h-1-1 0v-2c0-1 1-1 1-1 2-1 3-2 4-3l2-2z" class="X"></path><path d="M165 407c1-1 4-3 4-4h1v1l-1 1-1 1h0l2 1h-1c-2 2-4 3-5 5-1 0-1 1-3 2 1-3 4-5 4-7z" class="B"></path><path d="M246 505l-1-1-2-2c-1-1-2-2-2-3h-1l1-1 1-1c1-1 0-1 1-2l1 1 4 4h0l2 2h0l3 3 1-1v-1c2 1 3 3 4 4v1l-2-2c-1 0-1 0-2 1h0-1l2 2h-1-1v1h0c-1-1-3-2-4-3v-1l-1 1h-1l-1-2z" class="e"></path><path d="M246 505c1 0 3 1 5 0 1 0 2 1 3 2h0-1l2 2h-1-1v1h0c-1-1-3-2-4-3v-1l-1 1h-1l-1-2z" class="G"></path><path d="M443 441h0v-1l1-1 4 3h0v1l3 3 1 2v-1c1 1 3 1 3 2l1-1 4 2 1 1h-4-6v-1l-1 1h-2l-1-2c-2 0-3-2-4-3v-1l1 1h0c0-2-1-2-3-3 0-1 0-1-1-1v-2c1 0 2 1 3 1z" class="n"></path><path d="M444 446h0c0-2-1-2-3-3 0-1 0-1-1-1v-2c1 0 2 1 3 1 0 2 3 3 4 5v1c1 0 2 1 3 2l-6-3z" class="Y"></path><path d="M443 441h0v-1l1-1 4 3h0v1l3 3 1 2c-2-1-4-2-5-2-1-2-4-3-4-5z" class="C"></path><path d="M149 182c3-2 5-3 9-2 5 1 10 6 13 11 1 2 6 9 7 11h-1l-1-2h-1c1 0 1 1 1 2h0c-1-2-3-3-4-5-1-3-2-6-5-8l-6-6h-1-1c-1 0-2-1-2-1-1-1-2-1-4 0h0l-3 2-2 1-2 2h-1l-1-1c2-2 3-3 5-4z" class="T"></path><path d="M144 186c2-2 3-3 5-4v1h-1c0 1-1 1-2 2h0 2l-2 2h-1l-1-1zm125 371c-1 3-3 6-3 9 2-3 4-7 5-11v3c0 3-3 7-5 10 3-3 4-6 7-9-1 3-4 7-5 10-2 2 0 5-3 6-1-5-3-10-6-15 1 0 2-1 2-1l1-1 1 1v3h1c0-1 0-1 1-2v1 2l1-1 2-5h0 1z" class="N"></path><path d="M534 652h0c3 0 6-1 9-1 1 0 2 1 3 2h1 0l-3 1c-1 1-1 1-1 2s0 1-1 1-2 0-3 1v1c-1-1-3-1-4-1l-5 1 1-2h-1 1c-1-1-1-1-1-2 1 0 2-1 3-1h-2v-1l-2-1 2-1c2 1 2 1 3 1z" class="b"></path><path d="M543 651c1 0 2 1 3 2h-6c1-1 2-1 3-2z" class="E"></path><path d="M538 655l6-1c-1 1-1 1-1 2s0 1-1 1c0-2-2-1-4-1v-1z" class="c"></path><path d="M536 655h2v1c2 0 4-1 4 1-1 0-2 0-3 1v1c-1-1-3-1-4-1h1v-3h0z" class="Z"></path><path d="M534 652h0c3 0 6-1 9-1-1 1-2 1-3 2-2 0-4 0-7 1h0-2v-1l-2-1 2-1c2 1 2 1 3 1z" class="R"></path><path d="M533 654h0l1 1h2 0v3h-1l-5 1 1-2h-1 1c-1-1-1-1-1-2 1 0 2-1 3-1z" class="i"></path><path d="M533 654h0l1 1h2 0c-2 1-4 1-5 2-1-1-1-1-1-2 1 0 2-1 3-1z" class="Q"></path><path d="M416 419l2 1 1 1h1v-1c-1-2-2-2-3-3s-1-1-1-2l1-1c0-1-1-2-2-2-1-1-1-2-2-3l4 3c1 1 1 2 2 2 0 1 0 1 1 1v1 1c2 1 4 3 6 4s3 2 5 2h0c1 1 2 1 3 2-2 0-2 0-3 1 2 1 6 1 8 3-2 0-3-1-5-1-3-1-7-2-10-3l-1-1h-2 0c-1 0-1 0-1-1h-1-1c-1-1-2-2-2-4z" class="F"></path><path d="M431 426c-4-1-8-6-11-9h0c2 1 4 3 6 4s3 2 5 2h0c1 1 2 1 3 2-2 0-2 0-3 1z" class="q"></path><path d="M133 338h8 0-1v2h-1v1c1 1 2 1 3 1h1 1l-1 2h-1c0 1 0 2 1 3h-2c-2 1-5 2-7 2h-2c1 0 1-1 1-2h0 0c-1 0-1 1-2 1v-1c0-1 1-3 1-4 1-2 1-3 1-5z" class="F"></path><path d="M142 342h1 1l-1 2h-1c0 1 0 2 1 3h-2c-2-1-1-1-1-3h-3v-1h3 1l1-1z" class="P"></path><path d="M137 344h3c0 2-1 2 1 3-2 1-5 2-7 2h-2c1 0 1-1 1-2h0 0c1 0 0 0 1 1l1-1 1-1v-1-1h1z" class="I"></path><path d="M133 338h8 0-1c-1 0-3 0-4 1l-1 2v1l-1 1v1 1c0 1-1 1-1 2h0 0c-1 0-1 1-2 1v-1c0-1 1-3 1-4 1-2 1-3 1-5z" class="Y"></path><path d="M433 512c1 1 2 2 4 3h0v3c0 1 1 1 1 2h1v2 2h0-2v2c0 1 1 2 2 2 0 3 2 7 1 9-1-1-1-2-1-3s-1-3-2-5c0 0 0-1-1-2h-1-1-1l-1-1c-1-1 0-2-1-3 0-2 0-3-1-4 1-1 1-2 2-3h1v-1-3z" class="E"></path><path d="M434 524l1 3h-1-1c0-2 0-2 1-3z" class="B"></path><path d="M434 524h0c-1-1-2-2-2-3l1-1c2 1 2 1 2 3 1 1 1 1 2 1v2c0 1 1 2 2 2 0 3 2 7 1 9-1-1-1-2-1-3s-1-3-2-5c0 0 0-1-1-2h-1l-1-3z" class="l"></path><path d="M209 382h0l1 1h2v1 1c-1 0-1 1-1 2v1l-1 1 1 1c0-1 1-2 1-3h1 0c0 2 0 4 1 5-1 2-2 3-2 5v1 1c0 1-1 1-2 1 0-1-1-1-2-1l-1 2h-1l-1-1v-1-2-2h0c0-1 2-4 2-5l1-1v-1c1-1 1-1 1-2v-1c0-1 0-1-1-2l1-1z" class="D"></path><path d="M212 387c1 2 0 6-1 8s-3 3-5 5h0c0-2 1-7 3-9h0l1 1c0-1 1-1 1-2s1-2 1-3z" class="s"></path><path d="M499 639c2 0 4 1 6 1h1v4h-2l-2 2h0-1c-1 0-2 1-2 2h-2c-2 0-3 1-5 2 0-1 0-1-1-1h0c0-1 0-2 1-2s1-1 1-2c-1 1-2 1-2 1 0-1 1-2 2-2v-1c0-1 2-2 3-2v-1h0c1-1 1-1 2-1h1z" class="K"></path><path d="M505 640h1v4h-2v-1c-1-1-1-1-3-1h1c1-1 2-1 3-2z" class="i"></path><path d="M493 644h0c3-1 5-2 7-2h1c-3 2-6 2-8 3-1 1-2 1-2 1 0-1 1-2 2-2z" class="Q"></path><path d="M504 643v1l-2 2h0-1c-1 0-2 1-2 2h-2c-2 0-3 1-5 2 0-1 0-1-1-1h0 0l3-1c1-1 2-1 3-1 0-1 0-1 1-1 2 0 4-2 6-3z" class="E"></path><path d="M207 472c1-1 1-1 1-2h1s0-1 1-1h1l1 1h1l1-1c1 1 0 6 0 9-1 1-1 1 0 3v1 5 1 1c-1-1-1-1-2-1v1h-1l-1-1v-1h0c-1-1-1-2-1-4h0c0-2 0-3 1-5l-1-2h0c-1 1-1 1-1 2s0 1-1 2v-4l1-1c0-2 0-2-1-3z" class="f"></path><path d="M210 487h1c0-1-1-4 0-5 1 2 0 4 2 5h1v1 1c-1-1-1-1-2-1v1h-1l-1-1v-1z" class="c"></path><path d="M207 472c1-1 1-1 1-2h1s0-1 1-1h1l1 1h1v7c-1 1-1 3-1 4-1-2 0-7 0-9-1 2-1 4-2 6h0l-1-2h0c-1 1-1 1-1 2s0 1-1 2v-4l1-1c0-2 0-2-1-3z" class="R"></path><path d="M424 487h-1c-1-1-3-3-4-5h1c1-1 1 0 2 0 1 1 3 2 4 3 2 2 4 5 6 6 4 4 7 5 11 8h0c-3-1-7-1-11-2s-9-3-13-5c-1-1-4-3-5-5l2 2c4 3 10 4 14 6 2 1 5 2 7 1-1 0-2-1-4-1-1-1-2-1-4-2h0c-1-3-4-4-5-6z" class="o"></path><path d="M424 487h-1c-1-1-3-3-4-5h1c1-1 1 0 2 0v1l8 8c-2-1-4-4-6-4z" class="P"></path><path d="M456 562l1 1h1c0 2 1 3 1 4 0 4 1 7 2 10 0 1-1 1-1 2v1c0 2-1 4-1 6-2-3-2-6-2-9v1h-1c-1-1-1-1-1 0l-1-1h0v-3c-1-1-1-2-1-4v-1h1v-3l-1-1 1-1c0-1 1-1 2-2z" class="R"></path><path d="M454 566c1 1 2 2 2 3h0c0 1 0 5 1 6h1c0 1 0 1-1 2v1h-1c-1-1-1-1-1 0l-1-1h0v-3c-1-1-1-2-1-4v-1h1v-3z" class="I"></path><path d="M454 574l2 1-1 1v1h-1 0v-3z" class="h"></path><path d="M453 570l3 2v3l-2-1c-1-1-1-2-1-4z" class="t"></path><path d="M454 566c1 1 2 2 2 3h0v3l-3-2v-1h1v-3z" class="g"></path><path d="M422 461l6 3c-2-2-4-4-6-5s-3-1-4-2v-1h0c1 1 3 2 4 3 2 0 4 2 5 2l1 1c2 1 4 2 5 2 2 1 5 1 7 2h0l1 1c0 1-1 1-1 1v1h-1c-2 0-5 0-8-1h-1-3c-3-2-5-2-8-3-1-1-2-2-2-3l2 1c1-1 2-1 2-1h1 0l-1-2h0l1 1z" class="p"></path><path d="M419 463c1-1 2-1 2-1h1 0l-1-2h0l1 1c1 2 5 3 6 5l-2-1h-3c-1 0-3-1-4-2z" class="G"></path><path d="M428 462c2 1 4 2 5 2 2 1 5 1 7 2h0l1 1c0 1-1 1-1 1v1h-1c-2 0-5 0-8-1l-3-3h0 1l1 1c2 1 5 2 8 2v-1c-4 0-8-1-10-4v-1z" class="n"></path><path d="M440 466l1 1c0 1-1 1-1 1v1h-1l-1-2h1l1-1z" class="J"></path><path d="M524 642c1 0 2-1 3 0 1 0 1 1 2 1h2l1 1h-1v2h-1 0l-1 1-10 3h-3l-1-1 1-1c-1 0-3 0-5-1l2-1h0 0v-1l8-3h3z" class="B"></path><path d="M524 642c1 0 2-1 3 0 0 1-1 1-3 1l-1 1c-2 0-4 1-6 1-1 1-3 1-4 1h0v-1l8-3h3z" class="p"></path><path d="M530 646l-1 1-10 3h-3l-1-1 1-1c5-1 9-1 14-2z" class="Q"></path><path d="M122 297v-1h2c-1-1-1-1-1-2v-1-1h-2c1 0 1-1 2-1l-1-1-2 1h0l3-3v3h2 1v1h-1-1v1h2v1h-3l1 1c2 0 3 1 5 2l1 1c0 1 1 3 1 4h1l1 1h0c-1 1-3 1-4 2h0c-1 0-2 1-2 1h-3v1h1-2 0l-2-2c-1-2 1-6 1-8z" class="Z"></path><path d="M122 297l2 1v2h-1c0 1 0 1 1 1-1 1-1 1-2 1 0 1 1 1 1 2h2l-1 2v1h1-2 0l-2-2c-1-2 1-6 1-8z" class="b"></path><path d="M127 301c-1 0-2-1-2-1v-1h1v-1l-1-1h0c1 0 2 0 4 1v-1l1 1c0 1 1 3 1 4h1l1 1h-2l-1-1c-1-1-2-1-3-1z" class="E"></path><path d="M127 301c1 0 2 0 3 1l1 1h2 0c-1 1-3 1-4 2h0c-1 0-2 1-2 1h-3l1-2 1-1c-1 0-1 0-1-1h0 1l1-1z" class="r"></path><path d="M138 315h2l1 1h0 1c0 1 1 2 1 3h-1-1l1 1c1 1 1 1 1 2 0 0-1 0-2 1h0l2 1h-1l-1 1c-2 0-3 0-4 1h-3-1v-2h0l-2-1c-1 0-1-1-1-2l-1-2h-1-1v-1-1h1 1 1 7 1v-1-1z" class="Z"></path><path d="M138 315h2l1 1h0 1c0 1 1 2 1 3h-1-1 0l-1-1c-1 1-1 1-2 1-1-1 0-1 0-2v-1-1z" class="K"></path><path d="M138 315h2l1 1h0 1c0 1 1 2 1 3h-1v-1c-1 0-1-1-2-1 0-1-1-1-2-1v-1z" class="Z"></path><path d="M129 319h-1-1v-1-1l1 1h0c2 0 3 0 5 1-1 0-1 1-2 1l1 1h0l2-1h0v1c1 1 3 1 4 1h-5c-1-1-2-1-3-1h0l-1-2z" class="Q"></path><path d="M134 320c2-1 2-1 3-1l1 1h1c1 0 1 0 2-1h0l1 1c1 1 1 1 1 2 0 0-1 0-2 1-1 0-2 0-3-1-1 0-3 0-4-1v-1z" class="k"></path><path d="M130 321h0c1 0 2 0 3 1h5c1 1 2 1 3 1h0l2 1h-1l-1 1c-2 0-3 0-4 1h-3-1v-2h0l-2-1c-1 0-1-1-1-2z" class="L"></path><path d="M141 323l2 1h-1l-1 1c-2 0-3 0-4 1h-3-1v-2c2 1 6 0 8-1z" class="r"></path><path d="M196 444l1-2v3c1 0 1-1 1-2h1 0v2c0 1 0 2-1 2l-1 4v1c-1 0-1 1-2 1-1 2-2 2-2 3l-1 1v1h1v1h0-2l-1-3c0-1 1-2 1-3h-1v2h-1c-1-1 0-1-1-2l-1-1 1-1c1-1 1-2 1-3v-4l1-1h1l1 1c1 0 1 0 1-1l1-1h0v4h1c0-1 0-1 1-2z" class="K"></path><path d="M191 443l1 1c1 0 1 0 1-1 0 2 0 2-1 3h-2c0-1 0-1-1-2l1-1h1z" class="G"></path><path d="M189 444c1 1 1 1 1 2v3c0 2-2 3-1 5l1 1h-1c-1-1 0-1-1-2l-1-1 1-1c1-1 1-2 1-3v-4z" class="f"></path><path d="M209 483h0c0 2 0 3 1 4h0v1l1 1h1l-1 2c0 1 0 2 1 3l1-1c1 1 1 2 1 4h0c1 2 2 5 1 7l-1 1c-1-1-1-2-2-3 0-1-1-1-1-2h-1c-1 1-2 1-3 1h-1c0-1 0-2-1-2v-5l1-1v-4c0-1 1-1 1-2h0c0-1 0-1 1-1v-1h1v-2z" class="Q"></path><path d="M205 494c1 2 1 2 1 4l1 2h0c1 0 1-1 1-1h1c0 1 0 1 1 1-1 1-2 1-3 1h-1c0-1 0-2-1-2v-5z" class="S"></path><path d="M210 491h1c0 1 0 2 1 3l1-1c1 1 1 2 1 4h0c-1 2 0 3 0 5l-3-8c-1 1 0 2-1 3-1 0-1 0-1-1 0-2 0-4 1-5z" class="G"></path><path d="M209 483h0c0 2 0 3 1 4h0v1l1 1h1l-1 2h-1c-1 1-1 3-1 5v-1l-1-1c-1 1-1 2-1 2l-1 2c0-2 0-2-1-4l1-1v-4c0-1 1-1 1-2h0c0-1 0-1 1-1v-1h1v-2z" class="g"></path><path d="M207 496c0-2 1-4 1-6 1 2 1 4 1 5l-1-1c-1 1-1 2-1 2z" class="b"></path><path d="M210 491v-1c-2-1-2-1-2-3h0c1 0 1 0 2 1h0l1 1h1l-1 2h-1z" class="B"></path><path d="M170 510v-2h0c2 1 0 3 1 4h1 1l2 2v1l1 1h0v4c1 1 1 1 0 2 0 1 0 2-1 2l-2 2h0c-1 1-2 2-4 2 0 1 0 1-1 1h0l-1-1c1-1 1 0 1-1 0 0 1-1 1-2h-1v-3l2-12z" class="P"></path><path d="M172 512h1l2 2c0 1-1 1-1 2h-1l-1 4h-1c0-3 1-6 1-8z" class="Z"></path><path d="M173 512l2 2c0 1-1 1-1 2h-1v-4z" class="C"></path><path d="M170 510h0c0 5 0 10-1 15h-1v-3l2-12z" class="Q"></path><path d="M175 514v1l1 1h0v4c1 1 1 1 0 2 0 1 0 2-1 2l-2 2h0c-1 1-2 2-4 2 0 1 0 1-1 1h0l-1-1c1-1 1 0 1-1 3-1 3-5 3-7h1l1-4h1c0-1 1-1 1-2z" class="p"></path><path d="M175 514v1c-2 2 2 8-3 9 0 1 0 2-1 3v-2c0-1 2-2 2-3s-1-2-1-2l1-4h1c0-1 1-1 1-2z" class="G"></path><path d="M429 442h2c2 1 3 2 5 2l1 1v-1l1 1h0c1 1 2 1 3 2 0 1 1 2 2 4h-6-1l1 1c1 0 1 0 1 1-1 0-2-1-3-1s-2-1-3-1l-3-2-2-1c-1 1-2 0-3 0l-1-1h-1-1c-1 0-1-1-1-1v-1c1 0 1 0 2 1h1c1 0 2 1 2 1h1v-1h-1l-2-1c-1 0-1 0-1-1v-1l1 1h2v1h3 0 1v-1h-1v-1l1-1z" class="F"></path><path d="M429 442h2c2 1 3 2 5 2l1 1-1 1h-2c0-1-1-1-2-1h0l-3-3z" class="E"></path><path d="M438 445c1 1 2 1 3 2 0 1 1 2 2 4-2 0-4-1-6-1 0 0 0-1 1-1h2 0l-1-1c-1-1-1-2-1-3z" class="O"></path><path d="M427 448v-1h1l-1-1h2c3 0 6 2 9 3-1 0-1 1-1 1 2 0 4 1 6 1h-6-1l1 1c1 0 1 0 1 1-1 0-2-1-3-1s-2-1-3-1l-3-2-2-1z" class="n"></path><path d="M429 449v-1h0c3 1 5 2 8 2 2 0 4 1 6 1h-6-1l1 1c1 0 1 0 1 1-1 0-2-1-3-1s-2-1-3-1l-3-2z" class="D"></path><path d="M127 356h1l1 1h0c-1 3-2 5-1 7 1 1 1 0 2 0v1h1v1l-1 1c-1 1-1 1-1 3h-1 0 0-1l-1 3h-1c-1 1-1 3-1 4v1h-1v-3c0-2 1-4 1-6-5 2-9 4-13 6l-21 12-12 9c2-3 6-5 9-8 9-6 18-11 28-16 2-2 5-3 8-5 2-1 3-8 4-11z" class="u"></path><path d="M130 365h1v1l-1 1c-1 1-1 1-1 3h-1 0 0-1l-1 3h-1l1-5h0c1-1 3-2 4-3z" class="D"></path><path d="M245 343h1l2 1v1c3 2 6 3 9 4l14 7c2 1 5 2 6 4h0c-5-2-27-14-31-13h0l-2 2c2 3 14 9 18 11l7 4c2 2 7 3 8 6-1 0-3-2-4-2-2-2-4-2-6-4-3-1-5-2-7-4-3-1-6-3-9-5-1-1-3-2-4-3-2-1-3-2-5-2l-1 1v-1h-4c1 0 2 0 2-1 1-1 2-3 4-3 1-1 0-1 0-2 1 0 2-1 2-1z" class="T"></path><path d="M127 356l1-4c-5 1-11 3-16 5-11 4-23 9-34 15-6 3-12 6-17 10l-11 8c-1 1-2 2-4 2 3-2 5-4 8-6 6-5 12-9 18-12 9-6 19-11 29-14l15-5c4-2 8-3 12-5l1 2 1 1-1 4h0l-1-1h-1z" class="W"></path><path d="M448 481l1-1c0 1 1 2 2 2v1c1 1 1 1 2 1v1c2 0 2 1 3 2h2v1h-3l1 1c4 1 7 3 11 4 3 1 5 1 8 2l-1 1c0 1 1 1 2 1h1c0 1 1 1 1 1h1 3c-2 1-2 1-3 1h-4v1c-2 0-4-1-6-2-1 0-3-2-4-2-1-1-2-1-3-3 0 0-2-1-3-2-1 0-3-1-4-1l-9-4h0c-2-1-4-3-5-5l6 4h1c0-1 1-1 1-2s-1-1-1-2z" class="Q"></path><path d="M469 498h4 0c1-1 2 0 3 0v-1h1c0 1 1 1 1 1h1 3c-2 1-2 1-3 1h-4v1c-2 0-4-1-6-2z" class="q"></path><path d="M448 481l1-1c0 1 1 2 2 2v1c1 1 1 1 2 1v1 2h1l-1 1c-2-1-3-2-5-2l-1-1h1c0-1 1-1 1-2s-1-1-1-2z" class="h"></path><path d="M446 486l9 4c1 0 3 1 4 1 1 1 3 2 3 2 1 2 2 2 3 3 1 0 3 2 4 2 2 1 4 2 6 2h0c-1 0-3 1-4 1s-3 1-4 0v2h-2c0-1-2-2-3-2h0 0 3c-1-1-3-2-4-2l-9-6h0l-3-2-3-3c0-1-1-1 0-2z" class="P"></path><path d="M459 491c1 1 3 2 3 2 1 2 2 2 3 3 1 0 3 2 4 2 2 1 4 2 6 2h0c-1 0-3 1-4 1h-4v-1h1 1 1l-2-1h-2l-3-3c-1 0-1-1-3-2h0c-1-1-1-1-1-3z" class="C"></path><path d="M434 487l1 1 1 1c3 4 7 6 11 9 2 1 5 1 7 2l-4-2c-1-1-3-2-4-3 2 1 4 1 6 2h1c-1-1-2-1-3-2h2s0 1 1 1c0 1 1 1 2 1s2 1 3 1c-1-1-5-4-6-4v-1l9 6c1 0 3 1 4 2h-3 0 0c1 0 3 1 3 2-2 0-5-1-7-2-4 0-7 0-11-1h-2s-1 0-2-1h0c-4-3-7-4-11-8 1 0 1 0 2-1l1-1c-1-1-1-1-1-2z" class="O"></path><path d="M435 489h0c1 2 2 3 4 5 1 2 3 3 5 4 1 1 2 1 3 2h-2s-1 0-2-1h0c-4-3-7-4-11-8 1 0 1 0 2-1l1-1z" class="m"></path><path d="M537 479c7 0 15 0 22 1 12 2 22 4 33 9l1 2-12-4c-5-2-11-3-15-3-5-1-9-2-13-2-5-1-11-1-16-1l-3-1h2c-1 0-1-1-2-1h0 3z" class="a"></path><path d="M213 463v-1h-1l-1 1c-1 0-1 1-2 2l-1-1c0-1 0-1-1-1 1-1 1-2 2-3v-1c0-1 1-1 1-2l1-2c0-1 0-2 1-2 0-1 1-2 2-2v1 1l2-2v2c1-1 2-1 2-2h1s0 1 1 1h1v-1h1l-1 1v2 3h0l-1-1h-1v1c1 1 0 2 0 3 0 2 1 2 2 4-1 0-1 0-2 1l-2-1h0-1l-1 1v-1h-2v-1z" class="B"></path><path d="M213 463h1 0l1-2v-1c1-1 1-1 1-2v-1c0-1 0-2 1-3v3c0 1 0 2-1 3 0 1 1 1 0 2v1l1 1h0-1l-1 1v-1h-2v-1z" class="V"></path><path d="M217 457v-1h1c1 1 0 3 0 5l1-1c0 2 1 2 2 4-1 0-1 0-2 1l-2-1-1-1v-1c1-1 0-1 0-2 1-1 1-2 1-3z" class="D"></path><path d="M502 646h2 1 2l-1 1h1 1c1 0 2-1 3-1h2l-2 1c2 1 4 1 5 1l-1 1-12 5h0c-1 0-3 0-4 1h0-2l-2-1c-1-1-2-1-3-1l-2-1h1c0-1 1-1 1-2 2-1 3-2 5-2h2c0-1 1-2 2-2h1 0z" class="L"></path><path d="M502 646h2 1 2l-1 1c-1 1-3 1-5 2-3 1-5 3-8 4l-1-1c3-2 7-3 10-6h0z" class="V"></path><path d="M492 650c2-1 3-2 5-2h2c0-1 1-2 2-2h1c-3 3-7 4-10 6l1 1h-1l-2-1h1c0-1 1-1 1-2z" class="X"></path><path d="M511 646h2l-2 1c2 1 4 1 5 1l-1 1-12 5h0c-1 0-3 0-4 1l-2-1 2-1c1-1 2-1 3-2 2-1 3-3 5-4h1c1 0 2-1 3-1z" class="E"></path><path d="M511 646h2l-2 1c-1 1-1 2-2 2l-1 1h-1l-1 1h0-1c-1 1-1 1-2 1h0-1c0 1-1 1-1 1l-2 1v-1c1-1 2-1 3-2 2-1 3-3 5-4h1c1 0 2-1 3-1z" class="R"></path><path d="M477 509l-3-3h0 3l2 3c1 0 1 0 2-1l3 3c2 0 3 1 4 3l1 1 3 5c0 1 1 1 2 1 0 1-1 2 1 3l-2 1-1 1h0c-1-1-2-1-2-2l-3-3c-1-2-3-4-4-6-1 1-1 1-2 1h0l-2-2h-1c-1 0-1 0-1-1-1 0-3-1-4-2-1 0-1-1-2-2 1 0 3 1 4 1 0 0 1-1 2-1z" class="G"></path><path d="M477 509l2 1 2 3v1c1 0 1 1 2 1-1 1-1 1-2 1h0l-2-2h-1c-1 0-1 0-1-1-1 0-3-1-4-2-1 0-1-1-2-2 1 0 3 1 4 1 0 0 1-1 2-1z" class="C"></path><path d="M484 511c2 0 3 1 4 3l1 1 3 5c0 1 1 1 2 1 0 1-1 2 1 3l-2 1-1 1h0c-1-1-2-1-2-2v-1-1h0c0-1 0-2-1-3 0-1 0-2-1-3l-3-3-1-2z" class="S"></path><path d="M489 515l3 5c0 1 1 1 2 1 0 1-1 2 1 3l-2 1-1 1h0c0-4-3-7-3-11z" class="l"></path><path d="M136 301l2 1 1 1-2 1c2 1 3 2 4 3 1-1 1-2 1-2 1 0 1 1 1 1 1 1 2 2 2 3 0 0-1 1-2 1v1h-2 0-1v1h-1l-11-2h-2l-2 1h0v-1h0c-1 0-2-1-4-1h0c1-1 1-2 3-2h0 2-1v-1h3s1-1 2-1h0c1-1 3-1 4-2h1l2-2z" class="C"></path><path d="M128 308v-2h0c1 0 1 0 1 1l1 1h-2z" class="E"></path><path d="M141 307l-1 1c0-1-1-1-2-1s-1 1-2 1h-1v-2h0l-2-2h1 3c2 1 3 2 4 3z" class="Y"></path><path d="M129 308l12 3h0-1v1h-1l-11-2h3v-1h-1l-1-1z" class="b"></path><path d="M123 307h0 2l3 1h1l1 1h1v1h-3-2l-2 1h0v-1h0c-1 0-2-1-4-1h0c1-1 1-2 3-2z" class="n"></path><path d="M123 307h0l2 3h1 0l-2 1h0v-1h0c-1 0-2-1-4-1h0c1-1 1-2 3-2z" class="J"></path><path d="M273 211c1 0 1 0 2-1h2v1c-1 0-2 1-3 1 1 0 1 1 2 0h1v1c-1 1-2 1-3 1s-2 0-2 1h0 3l2-1h1c-1 1-1 1-1 2v1h-2 0c-3 1-6 1-9 3-2 1-3 2-4 3-1 2-4 4-6 5l-2 1s-1 0-1-1v-1c-2 0-3 1-5 2l1-3c6-3 12-6 17-10 3-2 5-4 7-5h0z" class="m"></path><path d="M256 228v-2c2-2 4-2 6-3-1 2-4 4-6 5z" class="K"></path><path d="M275 215h1v1c-3 1-6 1-9 2-2 1-3 2-5 3 1-1 4-5 6-5h1c1 0 2 0 2-1h1 3z" class="o"></path><path d="M198 384l1-2 3-6 1 1c-1 1-1 2-1 3-1 1 0 1 0 2h1c1 2 0 5 0 6l-1 1c0 1-1 2-1 4 0 1 0 1-1 2l-4 4h0l-3 1h0v1h-1 0c0-1 0-1-1-1l-2-1v1c-1-1-1-1-1-2 1 0 1-1 2-2l2-2v1c1-1 1-2 1-3 1-1 1-2 1-3v-1 1h1c1-1 2-2 2-3s1-2 1-2z" class="Z"></path><path d="M195 395h3c-2 2-3 3-5 4v1h0v1h-1 0c0-1 0-1-1-1 1-1 2-2 2-4 0 0 1-1 2-1z" class="V"></path><path d="M193 392h1v1 1h1v1c-1 0-2 1-2 1 0 2-1 3-2 4l-2-1v1c-1-1-1-1-1-2 1 0 1-1 2-2l2-2v1c1-1 1-2 1-3z" class="x"></path><path d="M189 399c1-1 3-2 4-3 0 2-1 3-2 4l-2-1zm9-12h0c1-1 3-4 3-4l1 1h0v2c0 1 0 1-1 1v1c-1 2-1 3-1 5l-2 2h-3v-1h-1v-1-1h-1c1-1 1-2 1-3v-1 1h1c1-1 2-2 2-3 0 0 0 1 1 1h0z" class="i"></path><path d="M198 387h0c1-1 3-4 3-4l1 1-3 6c-1-1-1-2-1-3z" class="O"></path><path d="M195 389c1-1 2-2 2-3 0 0 0 1 1 1h0c0 1 0 2 1 3l-4 4h-1v-1-1h-1c1-1 1-2 1-3v-1 1h1z" class="Q"></path><path d="M195 389c1-1 2-2 2-3 0 0 0 1 1 1v1c-2 1-3 2-4 4h-1c1-1 1-2 1-3v-1 1h1z" class="K"></path><path d="M255 371c0 1 0 1 1 2h0c2 1 3 2 5 3v1h-1c-1 0-1 0-2-1v1h0c1 1 1 1 1 2v1c1 1 3 4 3 6 1 2 2 5 3 7s2 4 2 6h1c0 2 0 3 1 4l2 8c0 2 1 3 1 4s1 2 1 2c-1 1-1 1-2 1l-1-1c0-3-1-4-1-6v-1c0-2-1-5-3-7h0-1 0v-1h1v-1h-1c-1 0-3 0-4-1-2-1-4-2-6-2-1-2-2-3-3-5 2-1 4 3 5 4 2 1 3 2 5 2h1c1 1 1 1 2 1 1-1 0-1 0-1v-1c0-1-1-3-1-4l-1-2-2-1c-1 0-1 0-2-1 0-1 1-2 1-3-3-3-5-8-8-11 0 0 1 0 2 1h0l1 1c0 1 1 1 2 1-1 0-1-1-1-1 0-1 0-2-1-2 0-2 0-2-1-4l1-1z" class="X"></path><path d="M260 387l2 2c0 1 0 2 1 3l-2-1c-1 0-1 0-2-1 0-1 1-2 1-3z" class="c"></path><path d="M245 356h1c1 0 1 0 1 1 1 1 1 1 2 1v-1l8 5c2 1 4 3 5 3 2 1 3 2 3 3l5 4c2 2 5 4 8 6v1h-1l-7-5c-1-1-2-2-3-2s-1 0-2-1l-2-1-3-2c-1-1-2-1-3-1 0-1-1-1-2-2v1 1 1l-2 1 2 2-1 1c-2-2-2-5-4-7h-2-1l-2-2h0 1v-1l1-2c0-1-1-2-2-3v-1z" class="J"></path><path d="M247 360l3 5h-2-1l-2-2h0 1v-1l1-2z" class="E"></path><path d="M253 369c-1-3-5-7-5-11 3 1 6 5 9 7s8 4 10 7c-1 0-1 0-2-1l-2-1-3-2c-1-1-2-1-3-1 0-1-1-1-2-2v1 1 1l-2 1z" class="V"></path><path d="M453 475l1 1c2 1 4 3 6 5 1 0 2 1 4 2l1-1 2 3h3c-1 0-1 0-1-1-1 0-1-1-1-2l1-1 2 2c1 0 1 0 2-1l3 2c2 1 3 2 4 3s2 2 3 4h-1c1 1 2 3 3 4h-1c-2 0-3 0-5-1s-5-3-8-4c-2-2-5-3-8-5l-3-3c-2-1-4-2-5-5l-2-1v-1z" class="o"></path><path d="M467 485h3 3c0 2 0 3 2 3 1 1 2 1 3 1 0 0 1 0 1 1h0c0 1 2 3 4 4-6-2-11-6-16-9z" class="D"></path><path d="M470 485c-1 0-1 0-1-1-1 0-1-1-1-2l1-1 2 2c1 0 1 0 2-1l3 2c2 1 3 2 4 3s2 2 3 4h-1l-3-1c0-1-1-1-1-1-1 0-2 0-3-1-2 0-2-1-2-3h-3z" class="j"></path><path d="M432 558l1-2h2l1 2c0 1 1 1 1 2v-1h1v1l1 3h-1l1 2h-3v-1h-1c0 2 0 3 1 4 0 2 0 4 1 6h1v1 3h-1 0c0 1 0 1-1 2h0v-4l-1 3h0v-1h-1v-2c0-1 1-2 1-2 0-1-2-2-2-2-1-1-1-2-1-2 0-1 0-1-1-1h-2v5l-1-1c0-3 0-7-1-11 0-1-1-4-1-6l1 1 1 2 1-1 1-1h0 0c1 1 1 2 1 2h2l-1-1z" class="E"></path><path d="M436 576l-1-1v-1c1 0 1 1 2 1v3h0c0 1 0 1-1 2h0v-4z" class="X"></path><path d="M426 556l1 1 1 2c2 1 2 4 3 6-1-1-1-1-1-3 0 0-1-1-1-2h-1c0 1 0 2 1 3v6 5l-1-1c0-3 0-7-1-11 0-1-1-4-1-6z" class="l"></path><path d="M432 558l1-2h2l1 2c0 1 1 1 1 2v-1h1v1l1 3h-1l1 2h-3v-1h-1c0 2 0 3 1 4 0 1 0 2-1 3h-1v-1c-1-2-1-3-2-5h0-1c-1-2-1-5-3-6l1-1 1-1h0 0c1 1 1 2 1 2h2l-1-1z" class="R"></path><path d="M432 562l1-1h1v2l-1 1-1-1v-1z" class="G"></path><path d="M436 558c0 1 1 1 1 2l1 3-2-1v-1-3h0z" class="B"></path><path d="M437 560v-1h1v1l1 3h-1 0l-1-3z" class="p"></path><path d="M430 557h0c1 1 1 2 1 2h2c1 1 1 1 1 2h-1l-1 1v3h-1c-1-2-1-5-3-6l1-1 1-1h0z" class="B"></path><path d="M435 452c1 0 2 1 3 1 0 1 1 2 2 2l4 4 2 1 8 5h-2l-1 1h2l-1 1v1h-1v1h-1-6-2-2v-1s1 0 1-1l-1-1h0c-2-1-5-1-7-2h4l-3-1c1-2 0-2 1-3h1l1-1v-1l3-2h0c-1-1-2-2-3-2l-2-2z" class="G"></path><path d="M451 468h-2c-2-1-4-2-5-3h1c1 0 2 1 4 1 1 0 2 0 3 1v1h-1z" class="q"></path><path d="M437 459v1 1c1 1 4 1 5 4h-1c-1 0-2-2-2-2-1 1-1 1-2 1l-3-1c1-2 0-2 1-3h1l1-1z" class="R"></path><path d="M437 458l3-2c1 1 2 1 2 2 1 2 3 2 4 4-1 0-1-1-1-1l-2-1c-1 0-1-1-1-1h-1v-1l-1 1c0 1 2 2 3 3h-1l-1-1-2-1h-1-1v-1-1z" class="B"></path><path d="M440 466c3 0 4 1 6 1 2 1 3 1 4 2h0-6-2-2v-1s1 0 1-1l-1-1h0z" class="s"></path><path d="M435 452c1 0 2 1 3 1 0 1 1 2 2 2l4 4 2 1 8 5h-2l-1 1c-1-1-3-3-5-4-1-2-3-2-4-4 0-1-1-1-2-2h0c-1-1-2-2-3-2l-2-2z" class="q"></path><path d="M247 507h1l1-1v1c1 1 3 2 4 3h-1v1h0v1 3h-2l-1-1c-1 0-2-2-3-2h0c0 1 1 2 2 3l2 1c0 1 1 1 1 2h1l1 1h1l-1 1c-1-1-1-1-2-1 0-1-1-1-1-1l-1-1c-1 0-1 1-2 1-1 1-1-1-2 0v1c1 1 1 0 1 1-2 0-3 1-4 0h0 0l-1-1h-2-1l-1-1s0-1-1-1l-3-3h1 1 0l-2-2c0-1 1-1 2-1h2 1 0v1c1 0 1 1 2 1 0 0 0-1 1-1l1 1c0-1-1-2-1-2v-1h1 1c0-1-1-1-1-2h2c1 0 2-1 3-1z" class="R"></path><path d="M243 510c0-1-1-1-1-2h2c1 0 1 1 3 1v1h-2l-1 1-1-1z" class="t"></path><path d="M492 501l2 1 2-1 2 2h2c3 7 5 15 8 22 1 3 2 7 3 10 0 2 0 2-1 4h0c-1-5-3-10-6-16v-3l-3-3c-2-4-6-7-8-11h-2v-1l1-1v-3z" class="M"></path><path d="M492 501l2 1 2 2c3 3 7 7 7 12-1-3-2-5-5-7h0l1 2h0c-2-1-5-5-6-5h-2v-1l1-1v-3z" class="V"></path><path d="M492 501l2 1 2 2h-4v-3z" class="E"></path><path d="M134 326h3c1-1 2-1 4-1l1 1v-1 2h3 1v1h2l1 1v1h1l-1 1v1h-4v1h2l-3 1h3s0 1 1 2c-1 0-3 1-4 1h0v1h-3-8l1-1h0l-1-1v-1l1-1-1-1v-2c0-1 1-1 0-2h-1l-1-1c1 0 2 0 3-1v-1z" class="Q"></path><path d="M146 328h2c-1 1-2 2-4 2v-2h2z" class="I"></path><path d="M146 327v1h-2v2h-2c-1-1-1-1-1-2l-1-1h1 5z" class="Y"></path><path d="M141 331h4v1 1h2l-3 1s0-1-1-1h-1 0l-1-2z" class="Z"></path><path d="M134 326h3c1-1 2-1 4-1l1 1v-1 2h3 1-5-1l1 1-1 1s-1 0-1-1l-1 1s-1 1-1 2l-1 1c0-1-1-1-2-1h-1c0-1 1-1 0-2h-1l-1-1c1 0 2 0 3-1v-1z" class="s"></path><path d="M140 330c1 0 1 0 1 1l1 2h0 1c1 0 1 1 1 1h3s0 1 1 2c-1 0-3 1-4 1h0v1h-3-8l1-1h0l-1-1v-1l1-1-1-1v-2h1c1 0 2 0 2 1l1-1c1 0 2-1 3-1z" class="b"></path><path d="M133 331h1c1 0 2 0 2 1l-2 2h0l-1-1v-2z" class="J"></path><path d="M137 335h1c1-1 2-1 3-1v1h-1v1 1c-2 0-3 0-5-1h0l2-1z" class="I"></path><path d="M140 330c1 0 1 0 1 1l1 2h0 1c-1 0-1 1-2 1s-2 0-3 1h-1c0-1 0-2 1-3s2-1 2-2z" class="K"></path><path d="M141 334c1 0 1-1 2-1s1 1 1 1h3s0 1 1 2c-1 0-3 1-4 1-1-1-2-1-3-2v-1z" class="F"></path><path d="M145 332h4 3 1 1v1c2 1 3 2 5 2 1 1 1 1 0 2h-3v2h2v3l2 1c-1 1-1 1-2 1-3 0-6 0-9 1v-1h0-2 0-1l1-1h-1c-1-1-1-1-2-1h0-1-1c-1 0-2 0-3-1v-1h1v-2h1 0 3v-1h0c1 0 3-1 4-1-1-1-1-2-1-2h-3l3-1h-2v-1z" class="c"></path><path d="M144 338l3 1 2 1h0c-2 1-4 1-5 0l-2-1c0-1 1 0 2-1z" class="C"></path><path d="M148 336h0l-1 1v2l-3-1c-1 1-2 0-2 1l-2 1v-2h1 0 3v-1h0c1 0 3-1 4-1z" class="f"></path><path d="M141 338h3c-1 1-2 0-2 1l-2 1v-2h1z" class="G"></path><path d="M148 336c2 1 4 1 6 1h-1v1c-1 0-1 0-2 1v1h2c-1 1-2 0-3 0h-1l-2-1v-2l1-1z" class="q"></path><path d="M154 336c1 1 1 1 2 1v2h2v3l2 1c-1 1-1 1-2 1v-1c-2-1-3-2-5-3h-2v-1c1-1 1-1 2-1v-1h1v-1z" class="L"></path><path d="M150 340c1 0 2 1 3 0 2 1 3 2 5 3v1c-3 0-6 0-9 1v-1h0-2 0-1l1-1 3-3z" class="G"></path><path d="M150 341h1c1 1 1 1 1 2v1l-2-1v-1-1z" class="B"></path><path d="M145 332h4 3 1 1v1c2 1 3 2 5 2 1 1 1 1 0 2h-3c-1 0-1 0-2-1v1c-2 0-4 0-6-1h0c-1-1-1-2-1-2h-3l3-1h-2v-1z" class="q"></path><path d="M153 332h1v1l-2 1v-2h1z" class="f"></path><path d="M147 333c2 0 2 0 3 1l-1 2c1 0 2 0 3-1h0l2 1v1c-2 0-4 0-6-1h0c-1-1-1-2-1-2h-3l3-1z" class="k"></path><path d="M152 334l2-1c2 1 3 2 5 2 1 1 1 1 0 2h-3c-1 0-1 0-2-1l-2-1h-1 0l1-1z" class="J"></path><path d="M470 509h1 0c1 1 1 2 2 2 1 1 3 2 4 2 0 1 0 1 1 1h1l2 2h0c1 0 1 0 2-1 1 2 3 4 4 6l3 3c0 1 1 1 2 2h0l2 2h0-1c0 2 2 5 2 6l-4-3h0c-2-1-4-1-5-3h0c-2 0-2-1-3-1-1-2-4-4-5-5s-2-2-2-3v-1c-1-1-2-1-4-1l-1-2 2-1v-2c-1 0-1 0-2-1 0-1-1 0-1-1-1 0-1 0-1-1h1z" class="i"></path><path d="M476 519c2 1 3 2 4 3s1 1 2 1c1 1 1 2 2 3h1v-2c2 1 2 2 3 3 0 0 1 1 2 1 0 1 1 2 1 3h0c-2-1-4-1-5-3h0c-2 0-2-1-3-1-1-2-4-4-5-5s-2-2-2-3z" class="f"></path><path d="M487 521l3 3c0 1 1 1 2 2h0l2 2h0-1c0 2 2 5 2 6l-4-3c0-1-1-2-1-3-1-1-1-3-2-4h-2l-1-1v-2h2zm-17-12h1 0c1 1 1 2 2 2 1 1 3 2 4 2 0 1 0 1 1 1l3 4h0c1 1 2 1 2 2v2h0l-1-1c-1 0-1 1-2 1-1-1-2-2-4-3v-1c-1-1-2-1-4-1l-1-2 2-1v-2c-1 0-1 0-2-1 0-1-1 0-1-1-1 0-1 0-1-1h1z" class="e"></path><path d="M473 514l2 2s1 1 1 2h0c-1-1-2-1-4-1l-1-2 2-1z" class="S"></path><defs><linearGradient id="A" x1="463.265" y1="482.879" x2="483.277" y2="502.131" xlink:href="#B"><stop offset="0" stop-color="#0f0e0e"></stop><stop offset="1" stop-color="#292a2b"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M463 485c3 2 6 3 8 5 3 1 6 3 8 4s3 1 5 1h1c-1-1-2-3-3-4h1 0l1-1h1c1 2 3 6 5 7h1 1l4 4-2 1-2-1h0c-1 0-1 0-2-1-1-2-5-2-7-3 0 1-1 1-1 1h-3-1s-1 0-1-1h-1c-1 0-2 0-2-1l1-1c-3-1-5-1-8-2-4-1-7-3-11-4l-1-1h3v-1-1l4 1c1 0 1 0 1-1l-1-1h1z"></path><path d="M475 495l5 1c-1 0-2 0-3 1h0-1c-1 0-2 0-2-1l1-1z" class="n"></path><path d="M480 496c1 1 2 1 3 1 0 1-1 1-1 1h-3-1s-1 0-1-1h0c1-1 2-1 3-1z" class="L"></path><path d="M483 491l1-1h1c1 2 3 6 5 7h1 1l4 4-2 1-2-1h0c-3-4-6-6-9-10z" class="J"></path><path d="M455 512h1l1 1 2 2h1l-3-3h1l1-1c0 1 2 3 2 3 2 0 3 0 3 1 1 1 1 2 1 2h1c-1-1-1-2-1-3h2c1 0 1 0 3 1h1l1 2c2 0 3 0 4 1v1c0 1 1 2 2 3s4 3 5 5h-1c-1 0-1 0-2-1h-3c-1-1-2-1-3-1-4-1-8-2-12-4l-2-1-5-4c0-1-3-2-3-3 1 0 3 2 4 2v-1c0-1 0-1-1-2z" class="q"></path><path d="M472 517c2 0 3 0 4 1v1c0 1 1 2 2 3s4 3 5 5h-1c-1 0-1 0-2-1h0 1l-3-2c-2 0-2-2-4-3-1 0-2 0-3-1h4 0v-1c-1 0-1 0-2-1l-1-1h0z" class="K"></path><path d="M467 514c1 0 1 0 3 1h1l1 2h0l1 1c1 1 1 1 2 1v1h0-4c-1 0-2-1-4-2h0l1 2h-1c0-1-1-2-2-3h1c-1-1-1-2-1-3h2z" class="m"></path><path d="M473 518l-1 1c-2-1-3-2-4-4h1c1 1 2 2 3 2h0l1 1z" class="c"></path><path d="M455 512h1l1 1 2 2h1l-3-3h1l1-1c0 1 2 3 2 3 2 0 3 0 3 1 1 1 1 2 1 2 1 1 2 2 2 3h1 0c1 1 2 1 3 2 0 0 1 0 1 1-1 0-2-1-3-1-2-1-3-1-4-2-2 0-3-2-5-2-1-1-3-2-4-3v-1c0-1 0-1-1-2z" class="I"></path><path d="M239 530l1 1h1l-4-4h1c2 1 4 2 5 4s3 3 5 4l1 2h1c1 1 2 1 3 1v1h-2c-1 0-3-1-5-1l-1-1v1h0v1l1 1-1 1v1c2 1 3 1 4 2h-2s1 1 2 1l1 1h0-2c-1 1-2 1-3 1h-1c-1-1 0-1-1-1-1-1-2-1-3-2l-3-1v-1l-1-1c-1 0-2-1-3-1-1-1-1-2-2-3l-3-3h0 2c1 1 2 3 4 3h0l1 1h3l1-1c1 1 1 2 2 3h0v-2c-1-1-2-2-3-2-1-1-1-1-2-1-1-1-3-2-4-3l-3-3c1 0 3 1 4 2 1 0 2 2 3 2h2v-1h0-2c-2-1-5-3-5-5 1 0 2 1 3 2s2 2 4 2l1-1z" class="l"></path><path d="M240 544l-1-3h0 3v2l1 1h0c1 0 2 0 2 1 1 0 2 1 3 1-1 1-2 1-3 1h-1c-1-1 0-1-1-1-1-1-2-1-3-2z" class="L"></path><path d="M239 530l1 1h1l-4-4h1c2 1 4 2 5 4s3 3 5 4l1 2h1c1 1 2 1 3 1v1h-2c-1 0-3-1-5-1l-1-1v1h0v1l-1 1h0c-2-1-2-1-2-2s-2-3-3-4l6 4c-1-2-2-3-3-4-2-1-3-2-4-3l1-1z" class="c"></path><path d="M455 516l5 4 2 1c-1 1 0 1 0 2-1 0-1 0-1 1s0 1 1 2h2v2h-1v3 1h-1c1 1 1 3 2 3v1h-1l-1-1-1 1c0 1 0 1 1 2l-1 1h1l-1 1c0 1-1 1-1 3h0 0c-1-1-2-3-2-4h-1l-1 1h0c-1 0-1 0-2 1l-1-2c0-1 0-1 1-2v-1c0-1 0-1-1-2h0l1-1c0-1-1-1-1-1v-2l-1-1v-2h-2v-1h2v-3 2c1 0 2-1 2-1h1 0c-1-2-2-2-3-5h3 0v-3z" class="D"></path><path d="M463 531c-1-1-2-2-2-3h0c0-1-1-1-1-2l-1-2v-1h0l2 1c0 1 0 1 1 2h2v2h-1v3z" class="F"></path><path d="M455 516l5 4c-2 1-2 1-5 1v-2h0v-3z" class="E"></path><path d="M452 523v2c1 0 2-1 2-1 0 1 0 2 1 3v2c1 1 2 1 3 2l2 3v-1-1h1v3h-1v2h0v6h0c-1-1-2-3-2-4h-1l-1 1h0c-1 0-1 0-2 1l-1-2c0-1 0-1 1-2v-1c0-1 0-1-1-2h0l1-1c0-1-1-1-1-1v-2l-1-1v-2h-2v-1h2v-3z" class="R"></path><path d="M454 533v2c1 1 1 0 1 1v2c1 0 2 0 3 1h-1l-1 1h0c-1 0-1 0-2 1l-1-2c0-1 0-1 1-2v-1c0-1 0-1-1-2h0l1-1z" class="C"></path><path d="M203 467c1 1 1 1 1 2h1v-1c1 1 1 3 1 4l-1 1h0v7c1-3 1-6 2-8 1 1 1 1 1 3l-1 1v4c1-1 1-1 1-2s0-1 1-2h0l1 2c-1 2-1 3-1 5v2h-1v1c-1 0-1 0-1 1h0c0 1-1 1-1 2v4l-1 1v5l-1-1-1-3-1-1v2l-1-3c0-3-1-6-1-10-1 1-1 4-1 6-2-3-1-7-3-10v-1c0-1 0-1-1-3 1 0 1-1 3-1 0 0 1 0 1 1 0-1 0-1 1-1 1-2 2-3 2-5l1-2z" class="H"></path><path d="M203 477v4l-1 1h-1v-2c1 0 1-1 1-2l1-1z" class="N"></path><path d="M199 479h2c0 1 0 2-1 3h-1v-1c0-1-1-1 0-2z" class="M"></path><path d="M195 475c1 0 1-1 3-1 0 0 1 0 1 1s0 2-1 3h-2c0-1 0-1-1-3z" class="W"></path><path d="M203 467c1 1 1 1 1 2h1v-1c1 1 1 3 1 4l-1 1h0v-1c-1 1-2 4-2 5l-1 1c-1-2 0-5 1-7 0-1-1-1-1-2l1-2z" class="E"></path><path d="M209 483v2h-1v1c-1 0-1 0-1 1h0c0 1-1 1-1 2v4l-1 1v5l-1-1-1-3h0c0-4 0-7 1-10h1 2c0-1 1-1 2-2z" class="Q"></path><path d="M203 495l2-1v-2h0l1 1-1 1v5l-1-1-1-3h0z" class="f"></path><path d="M468 463h0l-3-3h0l-2-2c-1-1-1-2-2-3h1 0 3v1c0 1 2 2 3 3s2 1 3 2c3 2 5 4 8 4 0 1 1 2 2 2l2 2c1 0 2 1 2 1v1l1 1 1-1c3 6 6 12 8 17s4 10 5 15h-2v-1c0-1-2-2-3-3-3-3-4-8-6-12h0v-1h0c0-3-2-5-3-7v-1c-2-2-3-5-6-7-1-1-1 0-3 0l1-1-1-1h1l1 1h0 0c0-2-3-3-4-4v-1c-1-1-2-2-3-2-1-1-2-1-4 0z" class="H"></path><path d="M486 478c4 5 6 10 9 16 0 2 2 4 2 5-3-4-6-9-8-13h0c0-3-2-5-3-7v-1z" class="C"></path><path d="M468 463h0l-3-3h0l-2-2c-1-1-1-2-2-3h1 0 3v1c0 1 2 2 3 3s2 1 3 2c3 2 5 4 8 4 0 1 1 2 2 2l2 2h-1 0c-1-1-1-1-2-1l2 2c4 3 6 6 7 9-1-2-6-8-8-8h-1c-1-1-1 0-3 0l1-1-1-1h1l1 1h0 0c0-2-3-3-4-4v-1c-1-1-2-2-3-2-1-1-2-1-4 0z" class="V"></path><path d="M150 184l3-2h0c2-1 3-1 4 0 0 0 1 1 2 1h1 1l6 6v2 1h0-2 0c-1 0-1-1-1-1l-1-1v3c1 0 2 1 2 2l1 1v2c-1-1-4-2-5-3l-1-1v2c-1-1 0-1-1-1h-2 0c-2 0-3 0-4 1-1 0-3 1-4 2v1c-1-1-3 0-4-1l2-1h0-2v-1c1-1 2-2 4-3h0c2-1 2-1 3-3l-1-1h-3 0c-1-1-1-2-2-2l2-2 2-1z" class="p"></path><path d="M148 185l2-1c1 0 2 0 3 1h-1v1c-2 0-3 1-4 3h0c-1-1-1-2-2-2l2-2z" class="s"></path><path d="M152 190c1 1 1 1 2 1v-1h0l-3-3h1 0c1 1 1 2 2 2h1c0 1 3 3 3 4-1 0-1 0-2-1-2 0-4 1-5 1v1h2c1 0 1-1 2-1s1 1 2 1v1c-2 0-3 0-4 1-1 0-3 1-4 2v1c-1-1-3 0-4-1l2-1h0-2v-1c1-1 2-2 4-3h0c2-1 2-1 3-3z" class="E"></path><path d="M150 184l3-2h0c2-1 3-1 4 0 0 0 1 1 2 1h1 1l6 6v2 1h0-2 0c-1 0-1-1-1-1l-1-1v3c1 0 2 1 2 2-1 0-2-1-3-2s0-1 0-2v-1l-2 1-2-3h-1 0v1 1c-2-1-3-3-5-4v-1h1c-1-1-2-1-3-1z" class="B"></path><path d="M158 188v-1l1 1h0 1v-2c1 1 1 1 1 2l1 1v1l-2 1-2-3z" class="c"></path><path d="M150 184l3-2h0c2-1 3-1 4 0h0v1 2 1l-1-2h-2c0 1 1 1 1 2-1 0-2 0-3-1h1c-1-1-2-1-3-1z" class="b"></path><path d="M419 537c1-1 1-1 2-1h1 1c0 1 0 1 1 2h0v1h1c0 1 0 2 1 3h1 0v-1l-1-2c0-1-1-1-1-2v-1l1 1c2 4 2 8 8 10v-2l1-1c0 1 0 2 1 2 1 1 2 2 3 4l1 2-1 1c-1 1-1 1-1 2l-1 1h1v3h-1v1c0-1-1-1-1-2l-1-2h-2l-1 2 1 1h-2s0-1-1-2h0 0l-1 1-1 1-1-2-1-1-1-2c-1-1-1-1-2-1v-2c0-2-2-5-3-7h1c0 2 1 3 2 4h0 1v-1h0v-1-1-3c-1 0-1-1-1-2-1-1-1-1-1-2v1h0v2h-2c-1 0-1 0-1-1 0 0 1 1 2 1v-1c0-2 0-2-2-3z" class="e"></path><path d="M430 557l1-1c-1-2-1-4-2-6 1 1 1 2 2 3 1 2 2 4 1 5l1 1h-2s0-1-1-2z" class="p"></path><path d="M431 553c1 0 1-1 2-1v-2h1l-1 1h1l1 5h-2l-1 2c1-1 0-3-1-5z" class="C"></path><path d="M435 556l-1-5h-1l1-1 2 2c0 1 0 3 1 4h0 1v3h-1v1c0-1-1-1-1-2l-1-2z" class="Z"></path><path d="M425 554v-1c0-2 0-2-1-4h1l1 1v1-2h0v-2h0c1 1 1 1 1 2l1 1c0 1 1 3 1 4s0 0 1 1v2h0l-1 1-1 1-1-2-1-1-1-2z" class="D"></path><path d="M427 557c1-1 1-1 2-1l1 1-1 1-1 1-1-2z" class="P"></path><path d="M154 421h2 2l1 1h1 0v-1c1 1 1 1 1 2h-1l-1 5h1c1-1 1-3 2-5l1-1h0c1 1 1 1 1 2l1-1c0 1-1 3-1 5v3l-1 3-1 1v1s-1 1-1 2v1h0l-2 2-1 1h-1c-1 0-1 0-1 1-1 1-2 2-3 2h0-1c0-1 0-2-1-2v-1l1-1v-1c0-1 1-2 2-3 0-1 0-1 1-2v-1c1 0 1 0 0-1v-2c-1 1-2 1-3 2h0l-1 2-1-1s1-1 0-2c-1 1-1 1-1 2h-1s-1 0-1-1l-1 2-1-1v-1c1-1 2-1 3-1 0 0 0-1 1-1h0l1-2h1v-1h0c1-1 1-3 1-4v-1c0-1 1-1 2-2h0z" class="U"></path><path d="M150 429h1 1c0 2 0 2-1 2h-2l1-2z" class="W"></path><path d="M162 423l1-1h0c1 1 1 1 1 2l1-1c0 1-1 3-1 5-1 0-1 1-2 1 0-2 1-4 0-6z" class="E"></path><path d="M159 435c1 0 1-1 2-2 0-1 0-1 1-2h0v2c0 1 0 1 1 1l-1 1v1s-1 1-1 2v1c0-1 0-1-1-1h-1v-3z" class="h"></path><path d="M154 421h2 2l1 1h1 0v-1c1 1 1 1 1 2h-1c-1 2-2 3-2 5l-1 1v-1c-1 0-2 0-3 1h-1l-1-1h-1 0c1-1 1-3 1-4v-1c0-1 1-1 2-2h0z" class="b"></path><g class="B"><path d="M154 421h2c-1 1-1 2-1 3s-1 2-1 3v1h-1v-1l1-6h0z"></path><path d="M158 421l1 1h1 0v-1c1 1 1 1 1 2h-1c-1 2-2 3-2 5h-1v-2s0-1-1-1h0v-1h-1c0-1 0-2 1-3h2z"></path></g><path d="M156 421h2c0 2-1 2-2 4h0v-1h-1c0-1 0-2 1-3z" class="Y"></path><path d="M155 433l1-2h1c0 1 0 1 1 2h0l1-2h1c0 1-1 3-1 4v3h1c1 0 1 0 1 1h0l-2 2-1 1h-1c-1 0-1 0-1 1-1 1-2 2-3 2h0-1c0-1 0-2-1-2v-1l1-1v-1c0-1 1-2 2-3 0-1 0-1 1-2v-1c1 0 1 0 0-1z" class="K"></path><path d="M156 440l1-1h1v1c-1 1-1 1-2 1v-1z" class="G"></path><path d="M156 440v1c1 0 1 0 2-1l1 1-1 1h-1c-1 0-1 0-1 1-1 1-2 2-3 2h0c1-1 1-3 2-4 0 0 1 0 1-1z" class="h"></path><path d="M197 526l3 2h1v1s0 1 1 1c0 0 0 1-1 1v3c-1 0-1-1-2-1h-1c-1 1-1 3-1 4-2 1-1 3-1 4-1 2 0 4-2 6 0 2-2 3-3 4h0c-1 0-2 0-2 1l-1 2h0-1l1 1-4 7c0 1-1 2-2 3 0-1-1-2-1-2v-1l-1-1 3-9h0l2-5 1-2 3-7 1-2s1 0 1 1l1-1c0-1 1-1 2-1v-1-1l3-6v-1z" class="C"></path><path d="M191 551h-1v-3c2-1 2-3 4-4v3c0 2-2 3-3 4z" class="p"></path><path d="M191 537l1-1c0-1 1-1 2-1 0 2-2 4-2 6 0 1 0 2-1 3h0l1 1c-1 0-1 1-2 1l-2 4v-2c0-1 0-2-1-3v1l-1-1 3-7 1-2s1 0 1 1z" class="O"></path><path d="M190 536s1 0 1 1-1 2-1 3c0-1 0-1-1-2l1-2z" class="d"></path><path d="M189 538c1 1 1 1 1 2-1 2-2 3-3 5v1l-1-1 3-7z" class="a"></path><path d="M197 526l3 2h1v1s0 1 1 1c0 0 0 1-1 1v3c-1 0-1-1-2-1h-1c-1 1-1 3-1 4-2 1-1 3-1 4-1 2 0 4-2 6v-3h0c-1-1-1-1-1-2l1-1c0-1 0-3 1-4l1-1c0-1-1-1-2-2v-1l3-6v-1z" class="c"></path><path d="M187 546v-1c1 1 1 2 1 3v2c-1 1-2 3-3 5 0 1-1 2-2 3h1c1 0 2-3 3-4l1 1-4 7c0 1-1 2-2 3 0-1-1-2-1-2v-1l-1-1 3-9h0l2-5 1-2 1 1z" class="J"></path><path d="M183 558v-2-2c1 0 2 0 2 1s-1 2-2 3z" class="s"></path><path d="M186 545l1 1-1 1c0 2 1 2-1 3h0c0 1 0 2-1 2h-1l2-5 1-2z" class="d"></path><path d="M430 416c3 0 3 2 6 3 2 1 3 3 4 4 1 0 1 0 2 1h0c2 1 5 2 7 2s4 2 5 2c1 1 2 2 3 2l1 1 1-1c0 1 1 1 1 2v1l3 2 1 1c0 1-1 1-1 1h-1c-1 0-1-1-1-1l-1-1h-1c-1-1-1-1-2-1h0l2 2 2 1 1 1c1 1 1 2 2 2v1c-6-5-12-8-19-10h-1c-1 0-3 0-5-1-4 0-9-1-13-3-3-1-7-1-10-3 3 0 5 1 8 1 3 1 7 2 10 3 2 0 3 1 5 1-2-2-6-2-8-3 1-1 1-1 3-1-1-1-2-1-3-2h2v-2h0l-1-2c-1-1-2-2-2-3z" class="O"></path><path d="M430 416c3 0 3 2 6 3 2 1 3 3 4 4 1 0 1 0 2 1l-1 1c1 0 1 1 2 1h0c1 0 2 0 3 1 2 0 5 1 7 3h-1c-1-1-2-2-4-2l-3-1h-1-1c-3-1-4-2-6-3l-3 1c-1-1-2-1-3-2h2v-2h0l-1-2c-1-1-2-2-2-3z" class="j"></path><path d="M433 421c1 1 2 2 4 3l-3 1c-1-1-2-1-3-2h2v-2h0z" class="f"></path><path d="M430 416c3 0 3 2 6 3 2 1 3 3 4 4 1 0 1 0 2 1l-1 1h-1c0-1-1-1-2-2 0-1-1-2-1-2-1 0-2 0-3-1l-2-2v1c-1-1-2-2-2-3z" class="S"></path><path d="M250 502c1 0 1 0 2-1 1 0 1 0 2 1h0v-2h0 1 1v-1h1l1 1c1 1 2 1 3 2l1 1c1 0 2 1 3 1 1-1 2-1 3-1l-1 1h0 0c1 1 2 1 3 2 1 0 1 1 3 1v2h-1c-1 2-2 4-2 6h-1c-1-1-1-1-3-1v1l-1 1c-1 0-1-1-2 0 1 0 3 2 3 3-2-1-3-2-5-2h-1-2-1l-1-1c0-1 0-1-1-2-1 0-2-1-3-2v-1h0v-1h1 0v-1h1 1l-2-2h1 0c1-1 1-1 2-1l2 2v-1c-1-1-2-3-4-4v1l-1 1-3-3h0z" class="I"></path><path d="M270 506c1 0 1 1 3 1v2h-1-2 1v-1l-3-1h-1v-1h3z" class="x"></path><path d="M270 509h2c-1 2-2 4-2 6h-1c-1-1-1-1-3-1v1c0-1-1-1-1-1l-1-1h0 2s1 1 2 1v-1l-3-2v-1h1c2 1 3 0 4-1z" class="J"></path><defs><linearGradient id="C" x1="251.861" y1="512.992" x2="259.668" y2="512.2" xlink:href="#B"><stop offset="0" stop-color="#19191b"></stop><stop offset="1" stop-color="#343332"></stop></linearGradient></defs><path fill="url(#C)" d="M253 510h0c1 0 2 1 2 1 0 1 1 2 2 3 0 0 0-1 1 0s3 1 5 2c1 0 3 2 3 3-2-1-3-2-5-2h-1-2-1l-1-1c0-1 0-1-1-2-1 0-2-1-3-2v-1h0v-1h1z"></path><path d="M442 518c1 0 1-1 1-1h1v2 1c1 1 1 1 1 2v1l1 2h1v-2c1 1 2 3 3 3v1h2v2l1 1v2s1 0 1 1l-1 1h0c1 1 1 1 1 2v1c-1 1-1 1-1 2l-1 1h1-1-1v2c1 1 1 2 1 3h-2v2h-1l-1-1c0-1 0-5-1-5v-1c-1 0-1 0-1-1h-1s0 1-1 1v1 2-1c-1-1-1-1-1-2s0-2-1-3v-1h-1v2l-1-1c1-2-1-6-1-9-1 0-2-1-2-2v-2h2 0l2 2h0l1-1c0-1-1-1-1-2-1-1 0-1 0-2-1-1-1-1-1-2h1v1h1l1-1-1-1z" class="c"></path><path d="M452 534c0 1 0 2 1 3l1-1v1c-1 1-1 1-1 2l-1 1h1-1-1 0 1c0-2-1-2-2-4l2-2z" class="I"></path><path d="M437 524h2 0l2 2h0 1v1 1h-1c-1 1-1 2 0 3-1-1-2-2-2-3-1 0-2-1-2-2v-2z" class="P"></path><path d="M447 534l2 7 2-1h0v2c1 1 1 2 1 3h-2v2h-1l-1-1c0-1 0-5-1-5 0-2-1-5 0-7z" class="X"></path><path d="M449 541l2-1h0v2c1 1 1 2 1 3h-2v2c0-3 0-4-1-6z" class="F"></path><path d="M439 528c0 1 1 2 2 3 0 1 0 2 1 3v1l1 1 1 1c0-1 1-1 2-2l-1-2h1l1 1c-1 2 0 5 0 7v-1c-1 0-1 0-1-1h-1s0 1-1 1v1 2-1c-1-1-1-1-1-2s0-2-1-3v-1h-1v2l-1-1c1-2-1-6-1-9zm3-10c1 0 1-1 1-1h1v2 1c1 1 1 1 1 2v1 3h1c1 2 2 3 3 5l-1 1-1-1c0-2-2-3-4-5 0-1 0-1-1-1h0c0-1-1-1-1-2-1-1 0-1 0-2-1-1-1-1-1-2h1v1h1l1-1-1-1z" class="E"></path><path d="M445 523l1 2h1v-2c1 1 2 3 3 3v1h2v2l1 1v2s1 0 1 1l-1 1h0c1 1 1 1 1 2l-1 1c-1-1-1-2-1-3l-3-3c-1-2-2-3-3-5h-1v-3z" class="b"></path><path d="M445 523l1 2h1v-2c1 1 2 3 3 3v1h2v2h-1v2c-1 0-3-4-5-5h0-1v-3z" class="k"></path><path d="M462 521c4 2 8 3 12 4l-1 1h0v3h1v-1l1 1h0v4h0c0 2 0 2-1 4v1l-1-1c-1 0-1 1-1 2l-2 2h0l2 5 1 1 1 2v1c1 0 1 1 1 1v1h0-1c0-1-1-2-1-3h-1 0v1l-1 1-2-2c-1 0-2-1-2-1h-1v-3c-1 0-1 0-1-1 1-2 0-4 0-6h-1v3h0-1c0-1 0-2-1-3h0c-1-1-1-1-1-2l1-1 1 1h1v-1c-1 0-1-2-2-3h1v-1-3h1v-2h-2c-1-1-1-1-1-2s0-1 1-1c0-1-1-1 0-2z" class="P"></path><path d="M472 539c-1 1 0 1-1 1v-3c-1-2 0-4-1-6v-1c-1-1-1-1-1-2s0 0 1-1c1 1 1 2 1 3s0 1 1 2h1 1v-3-1l1 1h0v4h0c0 2 0 2-1 4v1l-1-1c-1 0-1 1-1 2z" class="I"></path><path d="M475 533h-1v-4h1 0v4h0z" class="r"></path><path d="M463 532h0c1 1 1 2 1 3 1-1 1-1 1-2h1 1c0-2-1-5-3-7l1-1 2 2c0 2 0 4 1 6 1 1 0 2 1 4 0 1 1 2 1 4l2 5 1 1 1 2v1c1 0 1 1 1 1v1h0-1c0-1-1-2-1-3h-1 0v1l-1 1-2-2c-1 0-2-1-2-1h-1v-3c-1 0-1 0-1-1 1-2 0-4 0-6h-1v3h0-1c0-1 0-2-1-3h0c-1-1-1-1-1-2l1-1 1 1h1v-1c-1 0-1-2-2-3h1z" class="I"></path><path d="M462 538c-1-1-1-1-1-2l1-1 1 1c0 1 0 1 1 2v3h-1c0-1 0-2-1-3h0z" class="l"></path><path d="M467 541c-1-1-1-2-2-3v-2c1 0 2 0 3 1v1c0 2-1 2-1 3z" class="c"></path><path d="M469 537c0 1 1 2 1 4l2 5 1 1 1 2v1c1 0 1 1 1 1v1h0-1c0-1-1-2-1-3h-1 0c-1 0-1-2-2-2v-2h-2l-1-1v-3c0-1 1-1 1-3 1 0 1 0 1-1z" class="S"></path><path d="M531 646h5v1h-2v1h3 0-1c0 1-1 1-1 1 0 1 0 1-1 3-1 0-1 0-3-1l-2 1 2 1v1h2c-1 0-2 1-3 1 0 1 0 1 1 2h-1 1l-1 2-6 1h-3l-9-2c-1-1-4-1-5-2s-1-1-2-1h-1v-1h-1 0l12-5 1 1h3l10-3 1-1h0 1z" class="B"></path><path d="M507 656l5-2c1 0 1 1 2 0h2c-1 1-2 2-3 2l1 1v-1h2 0v1 1h0c-1 0-1 0-1-1h-1l-2 1c-1-1-4-1-5-2z" class="t"></path><path d="M521 652l1-2 5-1c2 0 3-1 4-1h5c0 1-1 1-1 1 0 1 0 1-1 3-1 0-1 0-3-1l-2 1c-1 1-2 1-3 1v-1c2-1 4-1 6-3h-1l-9 3h-1z" class="k"></path><path d="M531 651c1-1 2-2 4-2 0 1 0 1-1 3-1 0-1 0-3-1z" class="E"></path><path d="M531 646h5v1h-2v1h3 0-1-5c-1 0-2 1-4 1l-5 1-1 2c-1 0-3 2-5 2h-2c-1 1-1 0-2 0l2-1h0c2 0 4-2 5-3l10-3 1-1h0 1z" class="G"></path><path d="M515 649l1 1h3c-1 1-3 3-5 3h0l-2 1-5 2c-1-1-1-1-2-1h-1v-1h-1 0l12-5z" class="L"></path><path d="M516 650h3c-1 1-3 3-5 3h0 0c-2-1-7 1-9 2 3-3 7-4 11-5z" class="b"></path><path d="M516 656h0c3-2 6-3 10-4v1c1 0 2 0 3-1l2 1v1h2c-1 0-2 1-3 1 0 1 0 1 1 2h-1 1l-1 2-6 1h-3l-9-2 2-1h1c0 1 0 1 1 1h0v-1-1h0z" class="S"></path><path d="M531 653v1h2c-1 0-2 1-3 1l-3 1 1-2h2l1-1z" class="B"></path><path d="M516 656h0c3-2 6-3 10-4v1c-3 1-6 3-9 4h0c1 0 1 0 2 1h-3 0v-1-1h0z" class="K"></path><path d="M527 656l3-1c0 1 0 1 1 2h-1 1l-1 2-6 1h-3l-9-2 2-1h1c0 1 0 1 1 1h3c2-1 3-2 6-2h0c-1 1-3 1-4 3 2 0 4-2 6-3h0z" class="X"></path><path d="M524 660h-1c2-2 5-2 7-3h1l-1 2-6 1z" class="c"></path><defs><linearGradient id="D" x1="483.19" y1="578.291" x2="475.319" y2="586.742" xlink:href="#B"><stop offset="0" stop-color="#0f0f0f"></stop><stop offset="1" stop-color="#2e2d2e"></stop></linearGradient></defs><path fill="url(#D)" d="M472 573v-1c0-1 0 0 1-1l2 2 1 1c2 1 5 2 7 4h1l2 2v2h1l1-1 2 2c0 1 0 1 1 1v2l-1 1h0c0 1 0 2-1 2h-1v3l-1-1c-2-1-3-2-3-4h-1s0 2-1 2h-1s0 1-1 2l-1-1-2 6v1c-1-2-1-3-2-4s-1-2-1-2c-1-1-1-1-1-2-1-1-1-2-2-3h-1v-2c0-1 0-1 1-1v-1c1-2 1-7 1-9z"></path><path d="M480 582c0 1 0 1 1 1v3c-1-1 0-1-1-2-1 0 0-1 0-2z" class="E"></path><path d="M487 582l1-1 2 2c0 1 0 1 1 1v2l-1 1v-1-1s-1 0-2-1c-1 0 0-1-1-1l-1-1h1z" class="e"></path><path d="M483 587v-3h1l1 2h0 1c0 1 0 3 1 4v1c-2-1-3-2-3-4h-1z" class="r"></path><path d="M478 583c0-1 0-2 1-3 0 1 0 1 1 2 0 1-1 2 0 2 1 1 0 1 1 2 0 1 0 1 1 2l1-1h0s0 2-1 2h-1s0 1-1 2l-1-1h-1c-1-1-1-1-1-2l1-2c1-1 1-2 1-3h-1z" class="F"></path><path d="M478 586c1 0 1 1 2 2 0 1 0 1-1 2h-1c-1-1-1-1-1-2l1-2z" class="c"></path><path d="M478 583h1c0 1 0 2-1 3l-1 2c0 1 0 1 1 2h1l-2 6v1c-1-2-1-3-2-4v-2l3-8z" class="Q"></path><path d="M477 588c0 1 0 1 1 2h1l-2 6v-8z" class="D"></path><path d="M472 573h1v2c0 2 1 7 0 9v1h-1v1c1 1 1 0 2 0v1 4c-1-1-1-1-1-2-1-1-1-2-2-3h-1v-2c0-1 0-1 1-1v-1c1-2 1-7 1-9z" class="C"></path><path d="M470 584h1v1 1h-1v-2z" class="c"></path><path d="M546 632l5 2 4 1c4 1 7 1 11 2h0 6c0 1 0 2 2 2l-2 1c-1 0-2 1-4 1l-1 1c1 0 2 0 3 2h-2s0 1 1 1h3c1 0 2 1 4 1h2v1 1c-2 1-5 2-8 3h-3v-1c-4-1-8-3-11-5s-6-3-9-5l2-2 2 1v-2h-5c-1-1-2-1-3-2l1-2 2-1z" class="j"></path><path d="M556 640c2-2 5-1 7-1v3l-1-1c-2-1-3-1-6-1z" class="h"></path><path d="M578 647v1c-2 1-5 2-8 3 2-1 3-3 4-3h1c2 0 2 0 3-1z" class="B"></path><path d="M566 637h6c0 1 0 2 2 2l-2 1c-1 0-2 1-4 1l-1 1c1 0 2 0 3 2h-2s0 1 1 1h3c1 0 2 1 4 1-2 1-5 0-7 0-1-1-2-1-4-2h-3c1-1 1-1 2-1v-1h-1v-3l1-1h1 1 0l-1-1h1z" class="g"></path><path d="M567 642c1 0 2 0 3 2h-2l-1-1h-2 0c1-1 1-1 2-1z" class="Z"></path><path d="M566 637h6c0 1 0 2 2 2l-2 1-1-1c-1 0-2 0-3 1h0c-2 0-2-1-3-2h1 0l-1-1h1z" class="E"></path><defs><linearGradient id="E" x1="545.14" y1="639.124" x2="563.435" y2="631.489" xlink:href="#B"><stop offset="0" stop-color="#0f0f12"></stop><stop offset="1" stop-color="#322f2e"></stop></linearGradient></defs><path fill="url(#E)" d="M546 632l5 2 4 1c4 1 7 1 11 2h0-1l1 1h0-1-1l-1 1c-2 0-5-1-7 1 0 0-1 0-1-1h-4v-2h-5c-1-1-2-1-3-2l1-2 2-1z"></path><path d="M551 637c2 0 2 1 3 1l2 1h0-1-4v-2z" class="Y"></path><path d="M431 401h0c2 2 4 3 5 5l5 4c3 2 4 4 6 7l3 2c1 1 1 2 1 3l1 2-1-1-1 1 1 1h3c0 1 1 1 1 2l4 3-1 1-1-1c-1 0-2-1-3-2-1 0-3-2-5-2s-5-1-7-2h0c-1-1-1-1-2-1-1-1-2-3-4-4h1 0l1-1c-1 0-1 0-2-1h0c0-2-3-3-4-4h-1c0-1-1-1-2-1 0 0-1-1-2-1h0v-2h-1c-1-1-1-1-2-1-1-1-2-1-3-2h1c-1-1-2-1-2-2h-1l-1-1 8 1v-1h1 3v1l1-1v-2z" class="j"></path><path d="M445 416l2 1 3 2c1 1 1 2 1 3-2-1-4-3-6-6z" class="q"></path><path d="M419 404l-1-1 8 1c1 1 2 1 3 1 3 1 4 0 7 1 0 1 0 1-1 1h-1c-1-1-2 0-3 0-4-1-7-3-11-3h-1z" class="Q"></path><path d="M431 401h0c2 2 4 3 5 5h0c-3-1-4 0-7-1-1 0-2 0-3-1v-1h1 3v1l1-1v-2z" class="E"></path><path d="M427 409c1 1 1 1 2 1h0c1 0 2 1 3 1l1 1c1 1 0 0 1 0 1 1 1 2 2 2h0c1 2 4 4 4 5v1c0 1 0 1 1 1l2 2s0 1-1 1h0c-1-1-1-1-2-1-1-1-2-3-4-4h1 0l1-1c-1 0-1 0-2-1h0c0-2-3-3-4-4h-1c0-1-1-1-2-1 0 0-1-1-2-1h0v-2z" class="R"></path><path d="M436 406l5 4c3 2 4 4 6 7l-2-1s-1-1-2-1c-2-2-4-4-7-6l-5-2c1 0 2-1 3 0h1c1 0 1 0 1-1h0z" class="O"></path><path d="M436 406l5 4c-2 1-3-2-5-2v1l-5-2c1 0 2-1 3 0h1c1 0 1 0 1-1h0z" class="X"></path><path d="M440 419h1c0 1 1 1 1 2h1l1 1s1 0 1 1h1c1 1 2 1 4 1-1-1-1-1-1-2 1 0 2 1 2 1l-1 1 1 1h3c0 1 1 1 1 2l4 3-1 1-1-1c-1 0-2-1-3-2-1 0-3-2-5-2s-5-1-7-2c1 0 1-1 1-1l-2-2c-1 0-1 0-1-1v-1z" class="D"></path><path d="M493 512l11 11c3 6 5 11 6 16l2 11-1-3c-1-1-2-3-3-4v3l1 2c0 1 0 1-1 2 0-2-1-3-3-4h0l-2-2v-1c-1-1-1-1-2-1l-1-1c0-1 0-2-1-2v-1-1l-1-1s0-1-1-1v1c-1-2-1-5-2-8h-1 0l-2-2 1-1 2-1c-2-1-1-2-1-3-1 0-2 0-2-1l2 1c1 0 1-1 2-2 0 0-1 0-1-1 0-2-2-4-2-6z" class="B"></path><path d="M505 544l-1-1v-1c-1 0-1-1-1-1h-1c0-1 0-1-1-2v-1c-1-2-3-4-3-6v-2h0v-1l-1-1c0-1 0-1 1-2 0 0 0 1 1 2v1h0c1 1 1 2 2 3v-1h0c0-1 0-2-1-2v-3c1 1 1 2 2 3v2h0c0 1 0 1 1 2v1c0 1 1 2 1 4h0v4h1c0 1 0 1 1 1l-1 1z" class="w"></path><path d="M496 519h1c1 2 2 3 2 5v2c1 1 0 1 0 2-1-1-1-2-1-2-1 1-1 1-1 2l1 1v1h0v2c0 2 2 4 3 6v1c1 1 1 1 1 2h1s0 1 1 1v1l1 1 1 1-1 1-2-2v-1c-1-1-1-1-2-1l-1-1c0-1 0-2-1-2v-1-1l-1-1s0-1-1-1v1c-1-2-1-5-2-8h-1 0l-2-2 1-1 2-1c-2-1-1-2-1-3-1 0-2 0-2-1l2 1c1 0 1-1 2-2z" class="D"></path><path d="M495 524v3l-1 1-2-2 1-1 2-1z" class="b"></path><path d="M212 341v-1c1-1 2-1 3-1v2l1 1c1 4 3 6 7 8 1 2 3 3 3 4 1 1 2 1 3 2h0 1 1v-1c1-1 2-1 3-2l1-1h0l2-2h4v1c2 3 5 4 8 6v1c-1 0-1 0-2-1 0-1 0-1-1-1h-1v1c1 1 2 2 2 3l-1 2v1h-1 0l2 2h1 2c2 2 2 5 4 7 1 2 1 2 1 4 1 0 1 1 1 2 0 0 0 1 1 1-1 0-2 0-2-1l-1-1h0c-1-1-2-1-2-1-1 0-3-2-3-2h-1l1-2-3-3c-1-1-1-3-2-5-2-2-4-5-7-6l-2-1-2 2c-1 0-2 0-3 1l1 1 3 7c1 3 1 6 2 9 0 3-1 11-3 15-1-1 0-3 0-4 2-8 2-14-1-22 0-1-2-5-4-6l-1 1c-1 1-1 1-2 1l-5 3c-1-1-1-2-1-3l1 1h0v-1l1-1v-3h0 0l2 1c1 0 1 0 2-1 0-1 0-1-1-2l-2-1-2-3s-2-1-3-2c-3-2-4-4-5-9z" class="M"></path><path d="M238 356l-1-1s0-1 1-1l1-1c1 0 3 1 4 2l-1 1h-4z" class="D"></path><path d="M250 365c2 2 2 5 4 7 1 2 1 2 1 4l-2-3v-2c-2-2-4-3-6-6h1 2z" class="I"></path><path d="M212 341v-1c1-1 2-1 3-1v2l1 1c-1 1-2 1-2 2h0c1 0 1 0 1 1h-1c1 1 2 4 4 4v1h0 1c1 0 2 2 3 3v2l-2-3s-2-1-3-2c-3-2-4-4-5-9z" class="T"></path><path d="M244 364c4 3 6 7 9 11 1 1 1 2 3 3 0 0 0 1 1 1-1 0-2 0-2-1l-1-1h0c-1-1-2-1-2-1-1 0-3-2-3-2h-1l1-2-3-3c-1-1-1-3-2-5z" class="V"></path><path d="M243 355c0 1 1 1 2 1v1c1 1 2 2 2 3l-1 2v1h-1 0c-2-2-5-4-7-7h4l1-1z" class="G"></path><path d="M246 362c0-1-1-1-1-2-1-1-1-2-1-3h1c1 1 2 2 2 3l-1 2z" class="R"></path><path d="M251 485h1s0 1 1 1v-1c1-1 0 0 1 0v-2h0c2 1 3 2 4 4l1 1 1 1c1 0 1-1 1-1l-3-3v-1l-1-1v-1h1c0 1 0 1 1 1h2s-1-1 0-1 1 0 1 1l3 3v1c0 2 2 3 3 4l1 2 1 1v-2l-2-2v-1h2c1 1 0 2 0 3 1 1 3 2 3 3s0 1-1 1h-1c1 2 3 4 4 5h0-1l-1-1c0 2 2 3 3 4v2h1v1l-1 1v-1 1s0 1 1 1v3c0 1-1 2-1 2h-1 0c0 1-1 2-1 3h-1-1v1h-1c0-1 0-1 1-1v-2-1c1-1 1-2 1-2-1 0-3 4-3 5-1 2-2 3-2 5h-1 0c0-2 2-5 3-7 0-2 1-4 2-6h1v-2c-2 0-2-1-3-1-1-1-2-1-3-2h0 0l1-1c-1 0-2 0-3 1-1 0-2-1-3-1 1-1 1-2 1-2-2-2-3-3-4-5v-1h-1v1h0l-1-1h-1l1-1h-1c-1-1-1-2-2-2-1 1-1 0-1 1h0c-2-1-2-1-3-1l-1-1 1-1s1 1 2 1l1-1-1-1c0-1 0-1-1-2h0l1-1-1-1z" class="V"></path><path d="M262 493l1 1 1-1h0c1 0 2 1 3 1 1 1 2 1 2 3 0 1-1 1-1 2h-1v-1l-1 1c-1 0-1-1-1-1l-2-2-2-2 1-1z" class="c"></path><path d="M252 486c1 1 4 4 4 5 1 1 1 2 1 2v1h-1c-1-1-1-2-2-2-1 1-1 0-1 1h0c-2-1-2-1-3-1l-1-1 1-1s1 1 2 1l1-1-1-1c0-1 0-1-1-2h0l1-1z" class="F"></path><path d="M259 495h1v-1s-1-1-1-2h0 3v1l-1 1 2 2 2 2s0 1 1 1c0 1 0 1 1 2 1 0 0 0 1 1h-1 0-1l-1-1h-2c-2-2-3-3-4-5v-1z" class="r"></path><path d="M263 496l2 2s0 1 1 1c0 1 0 1 1 2 1 0 0 0 1 1h-1 0-1l-1-1c-1-2-3-2-4-5h2z" class="B"></path><path d="M266 499l1-1v1h1l1 1 2 2c2 1 3 3 4 5l-1 1c0-1-1-1-1-1-2 0-2-1-3-1-1-1-2-1-3-2h0 0l1-1c-1 0-2 0-3 1-1 0-2-1-3-1 1-1 1-2 1-2h2l1 1h1 0 1c-1-1 0-1-1-1-1-1-1-1-1-2z" class="Q"></path><path d="M266 499l1-1v1h0c1 2 4 4 5 6l-4-2c-1 0-2 0-3 1-1 0-2-1-3-1 1-1 1-2 1-2h2l1 1h1 0 1c-1-1 0-1-1-1-1-1-1-1-1-2z" class="V"></path><path d="M251 327c2-1 4 0 6-1 1-1 2-1 3-1 0 1 1 1 2 2h4v1c-1 0-1 0-2 1-1 0-1 0-2 1h1v1s0 1 1 1l1 1-2 1-5 2 18 5h1 0c-5 0-18-4-22-2v1c0-1-1-1-1-1-1 1-1 1-1 2h0l-1 1v1h-1l-3 1-2-1h-1-2c1-1 1-1 1-2l-3-2v-1c1 0 1-1 2 0v-2c0-1-2-1-2-1-2 0-5-1-8-1h0l-1-1h2c1 0 1-1 1-1h1v1h1v-1c0-1 2-2 3-3h0l3-2c2-1 3-1 5-1 1 1 2 1 3 1z" class="J"></path><path d="M250 335l4-2h3v1l-1 1h-6z" class="E"></path><path d="M263 331s0 1 1 1l1 1-2 1h-1-5v-1h3c1-1 1-1 3-2h0z" class="p"></path><path d="M252 329c1 1 2 1 3 1s2-1 4-1c-1 1-1 1-1 2h2l2-1h1v1h0c-2 1-2 1-3 2h-3-3l-4 2-1-1h-2-1l2-2c1 0 2-1 2-1 1-1 1-2 2-2z" class="c"></path><path d="M260 331l2-1h1v1h0c-2 1-2 1-3 2h-3-3c2-1 4-1 6-2z" class="X"></path><path d="M252 329c1 1 2 1 3 1s2-1 4-1c-1 1-1 1-1 2h-1c-3 1-6 0-9 2l1 1h-2-1l2-2c1 0 2-1 2-1 1-1 1-2 2-2z" class="K"></path><path d="M251 327c2-1 4 0 6-1 1-1 2-1 3-1 0 1 1 1 2 2h4v1c-1 0-1 0-2 1-1 0-1 0-2 1l-2 1h-2c0-1 0-1 1-2-2 0-3 1-4 1s-2 0-3-1l-1-1c-1 1-1 2-2 2l1-2 1-1z" class="p"></path><path d="M251 328c2-1 6-1 8-1 1 0 1 1 2 1v1h-2c-2 0-3 1-4 1s-2 0-3-1l-1-1z" class="E"></path><path d="M243 336c2 0 5 0 7 1 1 1 1 1 2 1h1l1 1h-1l-1 1v1h1l-1 1v1h-1l-3 1-2-1h-1-2c1-1 1-1 1-2l-3-2v-1c1 0 1-1 2 0v-2z" class="f"></path><path d="M249 342l2-1h1 1l-1 1v1h-1c-1 0-1 0-2-1z" class="U"></path><path d="M249 342c1 1 1 1 2 1l-3 1-2-1c1-1 2-1 3-1z" class="H"></path><path d="M243 336c2 0 5 0 7 1v1h0-3-1-3v-2z" class="E"></path><path d="M243 327c2-1 3-1 5-1 1 1 2 1 3 1l-1 1-1 2c1 0 1-1 2-2l1 1c-1 0-1 1-2 2 0 0-1 1-2 1l-2 2-1-1c-1 1-2 1-4 1h-2l-2-1v-1c0-1 2-2 3-3h0l3-2z" class="G"></path><path d="M243 332c1-1 2-1 3-1l1-1c1 0 2 0 3 1 0 0-1 1-2 1-2 0-3 1-5 0z" class="R"></path><path d="M244 329h2v1h-1-4l-1 1v-2h0 4z" class="B"></path><path d="M243 332c2 1 3 0 5 0l-2 2-1-1c-1 1-2 1-4 1l1-1c0-1 1-1 1-1z" class="E"></path><path d="M237 333v-1c0-1 2-2 3-3v2c1 0 1 0 1 1h0c-1 0-1 1-2 2l-2-1z" class="R"></path><path d="M243 327c2-1 3-1 5-1 1 1 2 1 3 1l-1 1h-2c-1 0-2-1-3 0 0 0-1 0-1 1h-4l3-2z" class="g"></path><path d="M577 651l2 2h0v1h0c0 2-3 3-4 4-1 0-2 1-3 1v1h1c1-1 1-1 3-1l1-1h1c0 4-5 4-6 8-1 0-2 1-3 1h-3c-2 0-3 1-6 0-1 0-4 1-6 0h-1l-8-2h-4l1-1h0c0-1 1-1 2-2 2 0 3-1 5-2 0 0 1 0 1-1v-1l1-1c1-1 1-1 2 0h2 1 0c1-1 2-1 3-1 1-1 2-2 3-2s2-1 3-1c0 0 1-1 2-1s1 1 2 1l1-1 2 1h1c2 0 3-1 4-2z" class="j"></path><path d="M567 652c1 0 1 1 2 1l1-1 2 1c-1 1-1 2-2 2-2 1-5 1-7 2 0-1 0-1 1-2 0 0 1-1 2-1s1 0 2-1h-3s1-1 2-1z" class="K"></path><path d="M569 653l1-1 2 1c-1 1-1 2-2 2l-2-2h1z" class="h"></path><path d="M562 654c1 0 2-1 3-1h3c-1 1-1 1-2 1s-2 1-2 1c-1 1-1 1-1 2h-1c-1 1-2 2-3 2s-2-1-3-1-2 1-3 1l-1-1c1 0 3-1 4-1h0c1-1 2-1 3-1 1-1 2-2 3-2z" class="Z"></path><path d="M556 658h1c2-1 3-1 5-1-1 1-2 2-3 2s-2-1-3-1z" class="C"></path><path d="M542 664l7-1h3c2-1 4-2 6-2 1-1 2 0 2 0-2 1-4 2-7 3h-1 1l10-3v1c-2 1-4 2-6 2-1 1-3 2-4 3l-8-2h-4l1-1z" class="Q"></path><path d="M542 664h0c0-1 1-1 2-2 2 0 3-1 5-2 0 0 1 0 1-1v-1l1-1c1-1 1-1 2 0h2 1c-1 0-3 1-4 1l1 1c1 0 2-1 3-1s2 1 3 1l-1 1c1 1 2-1 3 1h-1s-1-1-2 0c-2 0-4 1-6 2h-3l-7 1z" class="B"></path><path d="M576 659l1-1h1c0 4-5 4-6 8-1 0-2 1-3 1h-3c-2 0-3 1-6 0-1 0-4 1-6 0h-1c1-1 3-2 4-3 2 0 4-1 6-2v-1h1c2 0 4-1 6-1l2-1v1h1c1-1 1-1 3-1z" class="f"></path><path d="M553 667c1-1 3-2 4-3v1h2 0v1l1 1h-6-1z" class="g"></path><path d="M572 660h1c1-1 1-1 3-1l-5 5c-2 1-3 1-5 2h0l6-6z" class="c"></path><path d="M563 661h1c2 0 4-1 6-1-3 3-6 6-10 7l-1-1v-1h0-2v-1c2 0 4-1 6-2v-1z" class="C"></path><path d="M216 534c0-1-2-3-2-4h0c3 3 5 5 7 8 1 1 2 2 3 2v1s1 0 1 1l1-1c0 1 1 1 1 3h-2v-1 1c1 1 2 1 4 1h0c1-1 1-1 1-2l-2-5c2 2 4 5 6 6l1-1c1 0 1 0 2-1v1l3 1c1 1 2 1 3 2 1 0 0 0 1 1h1l1 1s1 1 2 1l-1 1c-1 1-2 1-3 1l1 2h0-2c0 1 1 1 1 2-1 1-2 0-3 1 0 1 1 1 2 1v1c-2 0-2-1-4 0 1 1 2 1 3 2h-1c-1 0-2-2-4-1 0 1 1 1 2 2-2 0-4-1-6-2h0l-1-1s-1-1-2-1c0-1-1-1-1-1v-1l-1 1h0l-1-1s0-1-1-1c-1-1-2 0-2-1 0 0-1 0-1-1-1 0-1-1-2-2h1-1c1-1 1 0 2 0h0v-1c0-1-1-2-2-3h-1l-2-2c-1-1-1-2-1-3v-1h-1c0-2 1-3 2-4-1 0-1-1-2-2z" class="G"></path><path d="M232 549c1 1 2 2 3 2h1c0 1 0 1 1 1h1l1 1h0 2v1 1c-1 1-4 0-6 0l-4-2-2-2 1-1c1 1 1 2 2 2h1l-1-3z" class="D"></path><path d="M230 543l-2-5c2 2 4 5 6 6l1-1c1 0 1 0 2-1v1l3 1c1 1 2 1 3 2 1 0 0 0 1 1h1l1 1s1 1 2 1l-1 1c-1 1-2 1-3 1h0c-1 0-1-1-2-1h-1l-2-2c-1 0-2-1-3-1 0-1 0-2-1-2h-1c-1-1-1-1-2-1l-2-1z" class="X"></path><path d="M244 547h1l1 1s1 1 2 1l-1 1h-1c-1 0-1 0-2-1-2 0-3 0-4-2h1 0 2 1z" class="g"></path><path d="M237 542v1l3 1c1 1 2 1 3 2 1 0 0 0 1 1h-1-2 0-1 0c-1 0-2-1-3-2 0 0 0-1-1-1l-1-1c1 0 1 0 2-1z" class="B"></path><path d="M216 534c0-1-2-3-2-4h0c3 3 5 5 7 8 1 1 2 2 3 2v1s1 0 1 1l1-1c0 1 1 1 1 3h-2v-1 1c1 1 2 1 4 1h0c1 1 2 2 2 3l1 1 1 3h-1c-1 0-1-1-2-2l-1 1 2 2 4 2c1 1 2 1 3 2h1v1c-3 0-3 0-5-2l-1 1c1 0 1 1 2 2h2c0 1 1 1 2 2-2 0-4-1-6-2h0l-1-1s-1-1-2-1c0-1-1-1-1-1v-1l-1 1h0l-1-1s0-1-1-1c-1-1-2 0-2-1 0 0-1 0-1-1-1 0-1-1-2-2h1-1c1-1 1 0 2 0h0v-1c0-1-1-2-2-3h-1l-2-2c-1-1-1-2-1-3v-1h-1c0-2 1-3 2-4-1 0-1-1-2-2z" class="R"></path><path d="M218 536c1 1 1 2 0 4 1 0 2 1 2 2l2 2s1 0 1 1h1v1c-1 0-1 0-2 1 2 1 3 2 4 4 1 1 2 1 2 2s1 1 1 2h0-1v1l-1-1s0-1-1-1c-1-1-2 0-2-1 0 0-1 0-1-1-1 0-1-1-2-2h1-1c1-1 1 0 2 0h0v-1c0-1-1-2-2-3h-1l-2-2c-1-1-1-2-1-3v-1h-1c0-2 1-3 2-4z" class="j"></path><path d="M164 431v1l1 2h1v-2h1v1l1 1h3 0 1c1 1 2 1 3 2h0c1 1 1 2 1 3h1c-1 1-2 3-2 4-1 2-1 3-2 4-1 2-2 3-2 5l-2 1c-2 1-3 2-5 3v-2-1c-2 1-3 2-5 2h0l2-1c-1-1-2-1-3-1v-1h-1-1l-2 2h-1c-1-1-1 0-2 0l-1-2h0v-1c1-2 1-4 3-6 1 0 2-1 3-2 0-1 0-1 1-1h1l1-1 2-2h0v-1c0-1 1-2 1-2v-1l1-1 1-3z" class="X"></path><path d="M159 445h1 1v1h2c0 1-2 2-2 3h-1l1-2h0c-1 0-3 2-4 3 1-2 3-3 2-4-1 0-5 4-6 5 0 1-1 1-1 2l5-3h0c0 1-1 1-1 2l-2 2h-1c-1-1-1 0-2 0l-1-2h0v-1 1c2-2 4-3 6-4 1-2 2-2 3-3z" class="g"></path><path d="M153 445c1 0 2-1 3-2 0-1 0-1 1-1h1v1l-3 3h0c2 0 3-3 4-2v1c-1 1-2 1-3 3-2 1-4 2-6 4v-1c1-2 1-4 3-6z" class="p"></path><path d="M164 431v1l1 2h1v-2h1v1l1 1h3l-3 7c0 2-1 3-3 3 0 1 0 1-1 1 1-1 2-2 3-2v-1-1c-2 0-2 2-4 3-1 0-1 0-2 1h-1-1v-1c-1-1-2 2-4 2h0l3-3v-1l1-1 2-2h0v-1c0-1 1-2 1-2v-1l1-1 1-3z" class="E"></path><path d="M164 431v1l1 2h1v-2h1v1h0c0 1 0 1 1 2 0 2-2 3-3 4s-2 1-2 2h-1v-1l-1-1h0v-1c0-1 1-2 1-2v-1l1-1 1-3z" class="B"></path><path d="M171 434h0 1c1 1 2 1 3 2h0c1 1 1 2 1 3h1c-1 1-2 3-2 4-1 2-1 3-2 4-1 2-2 3-2 5l-2 1c-2 1-3 2-5 3v-2-1c-2 1-3 2-5 2h0l2-1c-1-1-2-1-3-1v-1h-1 1 0c2 0 3-2 5-3 0-1 1-2 1-3l-1-1h1c1 0 1 0 1-1 2 0 3-1 3-3l3-7z" class="p"></path><path d="M175 436h0c0 2 0 3-1 5l-1-1v-2c1-1 1-2 2-2z" class="B"></path><path d="M163 449h0v1c0 1 1 1 1 1l-3 3c-1-1-2-1-3-1v-1h-1 1 0c2 0 3-2 5-3z" class="I"></path><path d="M169 447h0c-1 0-2 2-3 3v-1-2l-1-1c1 0 2-1 3-2h0 2 1c0 1-1 2-1 4h1l-1 1c0-1 0-1-1-2h0z" class="h"></path><path d="M168 444c1-1 1-2 1-3 1-2 2-4 2-6h1c1 1 0 3 0 5v1c0 1 0 2-1 3h-1-2 0z" class="E"></path><path d="M175 436c1 1 1 2 1 3h1c-1 1-2 3-2 4-1 2-1 3-2 4-1 2-2 3-2 5l-2 1c-2 1-3 2-5 3v-2-1c1 0 1-1 2-1l3-3v-2h0c1 1 1 1 1 2l1-1h-1c0-2 1-3 1-4 1-1 1-2 1-3l1-1 1 1c1-2 1-3 1-5z" class="Z"></path><path d="M169 449v2c-1 1-2 1-2 1-1 1-2 1-3 2v-1c1 0 1-1 2-1l3-3z" class="G"></path><path d="M173 440l1 1c0 3-1 5-3 7h-1c0-2 1-3 1-4 1-1 1-2 1-3l1-1z" class="P"></path><path d="M180 521l1 1h0c0 1 1 2 1 3v4h-1v1c0 1 1 2 1 2 0 2-1 3 0 4h-1 0v-1h-1v1 1c1 1-1 6-1 8 0 1-1 2-1 4v-1l-1 2h-1c-1 0-1 1-1 1l-1 2c-1 1-1 1-1 0l-1-1v1c-1 1-1 1-3 1l-1 1c0 1 0 1-1 2v-1h-1v1l1-6-1-1c0-2 0-5 1-8s0-8 0-11l1-2h0c1 0 1 0 1-1 2 0 3-1 4-2h0l2-2c1 0 1-1 1-2h1v1l1 1 1-1 1-2z" class="k"></path><path d="M168 551c1-1 2-2 2-3 1-1 1-3 2-4h1c-1 1-1 2-1 2-1 2-2 6-3 8l-1 1h0v-1-3z" class="X"></path><path d="M173 553c0-2 1-7 3-9l1 1c-1 1-1 2-1 3h1 1l-1 2h-1c-1 0-1 1-1 1l-1 2c-1 1-1 1-1 0z" class="l"></path><path d="M173 526h0l2 1 1-1c0 1 0 2-1 3h-1l-2 9h-1v-1l-2 8c0 2 0 3-1 4v1 1 3 1h0c0 1 0 1-1 2v-1h-1v1l1-6-1-1c0-2 0-5 1-8s0-8 0-11l1-2h0c1 0 1 0 1-1 2 0 3-1 4-2z" class="Z"></path><path d="M170 536c0-1 1-3 2-5 0 2-1 4-1 6l-2 8c0 2 0 3-1 4h0l-1-1c0-2 1-8 2-9s1-2 1-3z" class="f"></path><path d="M173 526h0l2 1 1-1c0 1 0 2-1 3h-1l-2 9h-1v-1c0-2 1-4 1-6-1 2-2 4-2 5-1-1 0-2 0-3l-1-1h-1l-1-1 1-2h0c1 0 1 0 1-1 2 0 3-1 4-2z" class="j"></path><path d="M173 526h0l2 1 1-1c0 1 0 2-1 3h-1 0c0-2 0-2-1-3z" class="E"></path><path d="M180 521l1 1h0c0 1 1 2 1 3v4h-1v1c0 1 1 2 1 2 0 2-1 3 0 4h-1 0v-1h-1v1c-1 2-2 4-2 6h0-1c1-2 1-4 2-6l-1-1c-1 2-1 3-2 5 0 2-1 3-2 5h-1l2-3c0-2 1-4 0-5-2 0-1 5-3 6 0-1 0-2 1-3 1-3 1-7 2-11 1-1 1-2 1-3l-1 1-2-1 2-2c1 0 1-1 1-2h1v1l1 1 1-1 1-2z" class="j"></path><path d="M176 522h1v1l1 1 1-1c0 1 0 1 1 2v2l-1-3h-1l-2 2h0l-1 1-2-1 2-2c1 0 1-1 1-2z" class="D"></path><path d="M180 521l1 1h0c0 1 1 2 1 3v4h-1l-1-2v-2c-1-1-1-1-1-2l1-2z" class="R"></path><path d="M180 521l1 1c-1 1-1 2-1 3-1-1-1-1-1-2l1-2z" class="B"></path><path d="M170 430h0c2-1 2-5 4-6 0 2-2 4-3 7 1 0 1 0 2 1 1-1 1 0 1-1v-1c0-1 1-2 1-3h1c0 2-1 4-1 6h0c1 0 1 1 2 1h0c0 1 0 1 1 2 1 0 0 0 1 1 0 1 1 2 2 4 0 1 1 3 2 4s1 1 2 1h1c0 1-1 1-1 1 0 1 2 4 2 5l1 1c1 1 0 1 1 2h1v-2h1c0 1-1 2-1 3l1 3h2 0v-1h-1v-1l1-1c0-1 1-1 2-3 1 0 1-1 2-1l-1 2c1 0 1 1 2 1h1 1 0c-1 1 0 2 0 3v1l1-2c1-1 2-2 2-4h2 2v1 1 1h-1v2h0c-1 1-1 2-2 3 0 1-1 2-2 3 0 2 1 2 1 3l-1 2c0 2-1 3-2 5-1 0-1 0-1 1 0-1-1-1-1-1-2 0-2 1-3 1h0c-1-1-1-2-1-4h0c-1-1-1-1-1-2l-2-1v-2l-1-1-1-1-1 2h-1l1-3 1-1c-1 0-1-1-1-1l-1-1h0c-1-1-1-2-1-3-1 0-1 0-1-1l-1-1s-1 1-2 1l1-2c0-2 0-3-1-4l-1 1h-1l1-2c1-2-2-5-3-7h0-1v-3h-1c0-1 0-2-1-3h0c-1-1-2-1-3-2h-1 0-3l2-4z" class="U"></path><path d="M193 456v1c1 0 1 0 2-1v2 1l-2 2h-1l1-2h0v-1h-1v-1l1-1z" class="F"></path><path d="M197 452l-1 2c1 0 1 1 2 1h1 1 0c-1 1 0 2 0 3v1c-1 1-3 4-3 5-1 1-1 2-2 3 0-2 0-3 1-4-1 0-1 0-1 1h-1c-1-1 1-3 1-5v-1-2c-1 1-1 1-2 1v-1c0-1 1-1 2-3 1 0 1-1 2-1z" class="I"></path><path d="M199 455h1 0c-1 1 0 2 0 3l-2 2c0-2 1-4 1-5z" class="S"></path><path d="M197 452l-1 2c1 0 1 1 2 1l-1 2v-2l-1-1c0 1-1 1-1 2-1 1-1 1-2 1v-1c0-1 1-1 2-3 1 0 1-1 2-1z" class="C"></path><path d="M200 458v1c-1 1-3 4-3 5-1 1-1 2-2 3 0-2 0-3 1-4 1 0 1-2 2-3l2-2z" class="R"></path><path d="M201 457c1-1 2-2 2-4h2 2v1 1 1h-1v2h0c-1 1-1 2-2 3 0 1-1 2-2 3 0 2 1 2 1 3l-1 2c0 2-1 3-2 5v-6l-3 5c0-2 1-3 1-5 0 0-1 1-1 0v-3-1c0-1 2-4 3-5l1-2z" class="P"></path><path d="M201 457l1 1c-1 2-2 3-3 4v1c1 1 1 2 0 3 0 1 0 1-1 2 0 0-1 1-1 0v-3-1c0-1 2-4 3-5l1-2z" class="F"></path><path d="M180 342l1-2h1c1 1 1 2 1 2l1 1v1c2-1 4-2 7-2l-1 2v1 1 1h1c1-1 2-2 3-2h2l1 1-2 2c1 1 1 1 2 1h-3c0 1-1 2-2 3h3-2v3h0c-1 3-3 3-4 5v1h-1v2c-3-2-6-4-10-5-3 3-5 6-8 10l-3 6-1 1-4 8c0 1-1 2-2 3v-1-1c0-1 3-4 4-5 0-2 1-4 2-5 0-2 2-4 3-6s3-5 5-7c1-1 2-3 3-4h0l-1-1s-1 0-1-1h-1c0-1 0-2-1-3v-1l1-1-1-1h-3c-1-1-1-2-1-3h0 0v-1l5-1c1-1 1-1 2-1 0 1 0 1 1 1l3-2z" class="T"></path><path d="M174 351l1-1 1 1h0 3v1h1c-1 0-2 1-2 1-2 0-2-1-4-2h0z" class="J"></path><path d="M190 353l3-1v3h0c-1 0-3 2-4 2 1-1 1-1 1-2h0c-1 0-2 1-3 2l-1 1c0-1 0-1 1-2v-1-1c-1 1-1 1-2 1h-1 0c1-1 2-2 3-2h3z" class="m"></path><path d="M180 352c2 0 3 0 4-1s2-1 3-2v1c0 1 1 1 2 2l1 1h-3c-1 0-2 1-3 2h0c0 1 0 1-1 2l-1-1c1-1 1-1 1-2h-1v1l-1-1h0v-2h-1z" class="J"></path><path d="M185 345c2-1 3-1 5-1v1 1 1h1c1-1 2-2 3-2h2l1 1-2 2c1 1 1 1 2 1h-3c0 1-1 2-2 3h3-2l-3 1-1-1c-1-1-2-1-2-2v-1c-1 1-2 1-3 2s-2 1-4 1h-1v-1h-3 0l-1-1-1 1h-1l1-1h1c2 0 4-1 7-2 1 0 2 0 3-1v-2z" class="o"></path><path d="M194 349c0 1-1 2-2 3h3-2l-3 1-1-1 2-2c1-1 2-1 3-1z" class="d"></path><path d="M191 347c1-1 2-2 3-2h2l1 1-2 2s-1-1-2-1v2l-1-1h0l-1-1h0z" class="Z"></path><path d="M185 345c2-1 3-1 5-1v1 1 1h1 0c0 1-1 1-2 2 0-1-1-1 0-1v-1c-1-1 0-1-1-1-2 1-4 3-7 3-1 0-1 1-2 1-1 1-2 1-3 1l-1-1-1 1h-1l1-1h1c2 0 4-1 7-2 1 0 2 0 3-1v-2z" class="g"></path><path d="M180 342l1-2h1c1 1 1 2 1 2l1 1v1c2-1 4-2 7-2l-1 2c-2 0-3 0-5 1v2c-1 1-2 1-3 1-3 1-5 2-7 2h-1l-1-1h-3c-1-1-1-2-1-3h0 0v-1l5-1c1-1 1-1 2-1 0 1 0 1 1 1l3-2z" class="s"></path><path d="M170 346h3c1 1 1 1 1 2h-4v1c-1-1-1-2-1-3h0 1z" class="d"></path><path d="M174 344c1-1 1-1 2-1 0 1 0 1 1 1h0c-2 1-5 1-7 2h-1 0v-1l5-1z" class="F"></path><path d="M180 342l1-2h1c1 1 1 2 1 2l1 1v1c-2-1-4 0-7 0h0l3-2z" class="C"></path><path d="M174 348l5-2c1 0 2-1 3-1h3v2c-1 1-2 1-3 1-3 1-5 2-7 2h-1l-1-1h-3v-1h4z" class="l"></path><path d="M173 349l9-3v1 1c-3 1-5 2-7 2h-1l-1-1z" class="n"></path><path d="M165 504l1 1c1 1 0 4 1 6v4h0v8 1h1v-1-1 3h1c0 1-1 2-1 2 0 1 0 0-1 1l1 1-1 2c0 3 1 8 0 11s-1 6-1 8l1 1-1 6h0l-1 3 1 2 1-1v2 1 2h1v-1h1v2c-1 3-1 6-2 9l-1 3h0c-1 6 1 11 1 16l-1 6-1 2c-1 2-3 4-4 7s-5 5-8 6h-1l5-6c3-2 7-8 7-11 1-4 0-8-1-12 0-4-1-8-1-12 0-8 1-17 2-25 0-11-1-21-2-31 1-2 1-3 1-5l1 1c0-3-1-8 0-10l1-1z" class="H"></path><path d="M167 523v1h1v-1-1 3h1c0 1-1 2-1 2 0 1 0 0-1 1l-1 2h0l-1-2 2-5z" class="Z"></path><path d="M165 575c-1-5 0-10 0-15l1 2 1-1v2 1l-1 3c-1 3-1 5-1 8z" class="q"></path><path d="M167 528l1 1-1 2c0 3 1 8 0 11-1-2 0-4 0-6h-1c-2-2-1-5-1-8l1 2h0l1-2z" class="s"></path><path d="M165 504l1 1c1 1 0 4 1 6v4h0c-1 2-1 5-1 7h-1l-1-17 1-1z" class="D"></path><path d="M167 564v2h1v-1h1v2c-1 3-1 6-2 9l-1 3h0v-1c-1 1-1 2-1 2-1-1 0-3 0-5 0-3 0-5 1-8l1-3z" class="t"></path><path d="M167 564v2h1c-1 2-1 4-2 6v-1-4l1-3z" class="K"></path><path d="M622 648v2h-1l1 1c-3 2-7 4-10 6-16 9-34 12-52 12-21 0-42-8-62-13l-16-3c-3-1-6-1-9-2 6 0 12 0 17 1h0l2 1c1 0 2 0 3 1l2 1h2 0c1-1 3-1 4-1h1v1h1c1 0 1 0 2 1s4 1 5 2l9 2h3l6-1 5-1c1 0 3 0 4 1v-1c1-1 2-1 3-1 0 2-2 3-3 4h-2l-1 1h-1l-1 1c2 1 5 1 7 2h4l8 2h1c2 1 5 0 6 0 3 1 4 0 6 0h3c1 0 2-1 3-1 1-1 1-1 2-1v1h1v-1l1-1h1l-1 2h0c3 0 5-1 7-1h1c1 0 2 0 3-1 2-1 5-1 7-2 5-1 10-3 13-5 6-2 10-5 15-9z" class="H"></path><path d="M539 659v-1c1-1 2-1 3-1 0 2-2 3-3 4h-2l-1 1h-1l-1 1c-1 0-4-1-6-2-1 0-5 0-7-1h3l6-1 5-1c1 0 3 0 4 1z" class="Q"></path><path d="M539 659v-1c1-1 2-1 3-1 0 2-2 3-3 4h-2l-1 1h-1l-1 1c-1 0-4-1-6-2h0 2c2 0 4 0 6-1 1 0 2-1 3-1z" class="F"></path><path d="M422 515c1 1 2 2 2 3 1 0 2 1 2 1h1v-1-3h1v1c0 1 1 2 2 3s1 2 1 4c1 1 0 2 1 3l1 1h1 1 1c1 1 1 2 1 2v4c1 1 0 1 0 2v1l-1 1h-1v2h-1 0c0-1-1-1-1-1 0 1 0 2 2 3v1h-1c0 1 1 2 1 2l-1 1v2c-6-2-6-6-8-10l-1-1v1c0 1 1 1 1 2l1 2v1h0-1c-1-1-1-2-1-3h-1v-1h0c-1-1-1-1-1-2h-1-1c-1 0-1 0-2 1 0-1-1-2-1-2v-1c-1-2-1-2-1-4h0c-1-2-1-3-1-5l-1-1c0-2 0-2 1-4l2 1c0 1 1 1 1 1h1v-2-1h1l1-3v-1z" class="j"></path><path d="M436 530c0 2 0 3 1 4v1 1l-1 1-1-1c0-1 0-3-1-4l2-2z" class="C"></path><path d="M435 527h1c1 1 1 2 1 2v4c1 1 0 1 0 2v-1c-1-1-1-2-1-4 0-1-1-2-2-3h1z" class="k"></path><path d="M427 524h0l1 1v1c1 1 1 1 1 2 1 1 1 1 1 2l-1 1h-1c-2-1-1-3-1-5v-2z" class="E"></path><path d="M415 524c0-2 0-2 1-4l2 1c0 1 1 1 1 1h1c1 2 2 6 2 8h-1 0c-1-2-3-3-4-6l-1 1-1-1z" class="r"></path><path d="M415 524c0-2 0-2 1-4 0 2 0 3 1 4l-1 1-1-1z" class="R"></path><path d="M425 534v-2-2c-1 0-1-1-1-2v-1h0c1 0 1 1 2 1v2c1 2 0 5 2 6 1 0 1 1 1 1 1 0 2-1 3-1 0-1 0-2-1-2v-4-1c1 1 1 3 1 4 1 1 1 1 1 2l2 2v-1l1 1h-1v2h-1 0c0-1-1-1-1-1 0 1 0 2 2 3v1h-1c0 1 1 2 1 2l-1 1v2c-6-2-6-6-8-10l-1-1v-2z" class="B"></path><path d="M425 534h0c2 1 3 5 4 7h1v-3c1 1 1 1 1 2v2h1c1-1 1 0 1-1-1-2-1-3-1-4h0 0c1 0 1 0 1 1s0 2 2 3v1h-1c0 1 1 2 1 2l-1 1v2c-6-2-6-6-8-10l-1-1v-2z" class="l"></path><path d="M253 520l1-1h-1l-1-1h-1c0-1-1-1-1-2l-2-1c-1-1-2-2-2-3h0c1 0 2 2 3 2l1 1h2v-3c1 1 2 2 3 2 1 1 1 1 1 2l1 1h1 2 1c2 0 3 1 5 2 0-1-2-3-3-3 1-1 1 0 2 0l1-1v-1c2 0 2 0 3 1h1c-1 2-3 5-3 7h0l-1 1v-2h-2c0 1-1 2-1 3h0c-1 0-1 1-1 1l-2 2c0 1 0 2-1 3 0 1-1 2-1 3-1 2-2 3-3 4v1l-1 2v1l-1 1v-1c-1-1-3-1-4-1h-1c-1 0-1-1-2-2 2 0 4 1 5 1h2v-1c-1 0-2 0-3-1h-1l-1-2c-2-1-4-2-5-4h1c-1-2-2-3-4-4l1-1c0-1-2-2-3-4 2 0 2 1 2 1 1 0 1-1 2-2h0v-1h0c1 1 2 0 4 0 0-1 0 0-1-1v-1c1-1 1 1 2 0 1 0 1-1 2-1l1 1s1 0 1 1c1 0 1 0 2 1z" class="d"></path><path d="M253 520l1-1h-1l-1-1h-1c0-1-1-1-1-2l-2-1c-1-1-2-2-2-3h0c1 0 2 2 3 2l1 1h2v-3c1 1 2 2 3 2 1 1 1 1 1 2l1 1h1 2c2 1 4 2 5 4l-8-3h-3c2 2 6 2 8 4h0c-3 0-6 0-9-2h0z" class="E"></path><path d="M242 520h0c1 1 2 0 4 0 0-1 0 0-1-1v-1c1-1 1 1 2 0 1 0 1-1 2-1l1 1s1 0 1 1c1 0 1 0 2 1h0l1 1c2 2 6 3 8 4-2 0-4 0-7-1l-1-1-1 1c2 1 6 2 8 3h-7 0c1 1 3 1 4 1v1h0c-3 1-4 1-6-1 0 0-1-1-2-1 0-1 0 0-1 0 0-1-1-2-2-2-1-2-3-3-4-4h-1v-1z" class="S"></path><path d="M242 521h1c1 1 3 2 4 4 1 0 2 1 2 2 1 0 1-1 1 0 1 0 2 1 2 1 2 2 3 2 6 1v1h-2v1h1l1 1c-1 1-1 1-2 1l-1 1 1 1h-1s0 1-1 1l-2 1h1c1 1 1 0 1 1-1 1-1 1-3 1h2v-1c-1 0-2 0-3-1h-1l-1-2c-2-1-4-2-5-4h1c-1-2-2-3-4-4l1-1c0-1-2-2-3-4 2 0 2 1 2 1 1 0 1-1 2-2h0z" class="b"></path><path d="M242 521c1 2 3 3 5 5 1 1 1 1 3 1l2 2-1 1c1 1 3 2 3 3-1 0-2-1-3-1-3-2-4-4-7-5h0-1c0-1 1-2 0-3h-1c-1 0-1-1-2-1 1 0 1-1 2-2z" class="V"></path><path d="M241 526h1c1 1 1 2 2 2 2 0 2 2 3 2 1 1 2 1 3 3v1c1 0 4 1 4 2-1 0-3 0-5-1-2 0-4-3-5-4-1-2-2-3-4-4l1-1zm-89-93h0c1-1 2-1 3-2v2c1 1 1 1 0 1v1c-1 1-1 1-1 2-1 1-2 2-2 3v1l-1 1v1c1 0 1 1 1 2h1 0c-2 2-2 4-3 6v1h0l1 2c1 0 1-1 2 0h1l2-2h1 1v1c1 0 2 0 3 1l-2 1c-1 0-2 0-3 1h0c0 1 0 2-1 3h0l-1 1h-1c-1 0-2 1-2 2h1c-1 2-1 2-2 3h0c2 0 2-1 4-2 0 1-1 2-1 2-2 2-3 2-5 3v1h-1-2-1l1-1v-3c-1-1-3-3-4-5l-5-6c1-2 1-3 1-4s0-2 1-3c0-1 0-2 1-2l3-6c1-2 2-4 3-5l1 1 1-2c0 1 1 1 1 1h1c0-1 0-1 1-2 1 1 0 2 0 2l1 1 1-2z" class="S"></path><path d="M144 454c0-2 0-6 2-8h0v1l-1 5h0v2c1 2 0 3 0 4v1l-1-1v-4z" class="f"></path><path d="M152 433h0c1-1 2-1 3-2v2c1 1 1 1 0 1v1c-1 1-1 1-1 2-1 1-2 2-2 3l-2 2c0-1-1-1-1-2 0 0 1 0 1-1h1l-1-1v-2c1-1 1 0 1-1l1-2z" class="Z"></path><path d="M152 433c1 1 1 1 1 2s-1 3-2 4l-1-1v-2c1-1 1 0 1-1l1-2z" class="B"></path><path d="M152 440v1l-1 1v1c1 0 1 1 1 2h1 0c-2 2-2 4-3 6v1h0v3h-2-1v-1h-1l-1-2h0l1-5v-1h0c1-1 2-1 2-2 1-1 1-2 2-2l2-2z" class="R"></path><path d="M146 447c1 1 1 3 1 5h-2l1-5z" class="E"></path><path d="M145 434l1 1 1-2c0 1 1 1 1 1h1v1 1c-2 2-3 4-4 7-1 1-1 1-1 2-1 1 0 1-1 1-1 3 0 7 0 9v1l1-2v4h-1c-1-2-2-2-2-5 0-1 0-1-1-2 0 1 0 2-1 3-1-2-1-5-1-7h0 0c0-1 0-2 1-2l3-6c1-2 2-4 3-5z" class="r"></path><path d="M143 440c1-2 2-5 5-6l-3 6h-2z" class="e"></path><path d="M143 440h2c-1 1-2 3-3 4 0 0-1 1-1 2h0c1-1 1-1 2-1-1 2-2 3-2 5h-1v-3h-1v-1c1-2 2-4 4-6z" class="B"></path><path d="M138 447h0 0c0 2 0 5 1 7 1-1 1-2 1-3 1 1 1 1 1 2 0 3 1 3 2 5h1l1 1v-1c0-1 1-2 0-4v-2l1 2h1v1h1 2v-3l1 2c1 0 1-1 2 0h1l2-2h1 1v1c1 0 2 0 3 1l-2 1c-1 0-2 0-3 1h0c0 1 0 2-1 3h0l-1 1h-1c-1 0-2 1-2 2h1c-1 2-1 2-2 3h0c2 0 2-1 4-2 0 1-1 2-1 2-2 2-3 2-5 3v1h-1-2-1l1-1v-3c-1-1-3-3-4-5l-5-6c1-2 1-3 1-4s0-2 1-3z" class="H"></path><path d="M139 454c1-1 1-2 1-3 1 1 1 1 1 2v3l-1 1h0c-1 0-1-1-1-1-1 0-1 0 0-1v-1z" class="U"></path><path d="M145 458c0-1 1-2 0-4v-2l1 2h1v1h1 1c0 1-1 2-1 2l1 1h1c0 2 0 3-1 4 0-1 0-1-1-1l-1 1c0 1 0 1-1 1-1-1-2-3-3-5h1l1 1v-1z" class="l"></path><path d="M145 458c0-1 1-2 0-4v-2l1 2h1v1h1 1c0 1-1 2-1 2l1 1h1-1c0 1-1 2-2 3 0-2 2-4 1-6l-1 1c0 1 0 2-1 3h-1v-1z" class="K"></path><path d="M156 452h1 1v1c1 0 2 0 3 1l-2 1c-1 0-2 0-3 1h0c0 1 0 2-1 3h0l-1 1h-1c-1 0-2 1-2 2l-2 1h0v-1c1-1 1-2 1-4h-1l-1-1s1-1 1-2h-1 2v-3l1 2c1 0 1-1 2 0h1l2-2z" class="E"></path><path d="M151 454c1 0 1-1 2 0 0 1-1 1-2 2v-1-1zm5-2h1 1v1c1 0 2 0 3 1l-2 1-1-1h-4l2-2z" class="F"></path><path d="M149 458l1-1c1 0 2 0 2 1l1 1c1-1 2-2 3-2v-1c0 1 0 2-1 3h0l-1 1h-1c-1 0-2 1-2 2l-2 1h0v-1c1-1 1-2 1-4h-1z" class="Y"></path><path d="M224 335c1-1 4 0 5-1s2-1 3-1l1 1h0c3 0 6 1 8 1 0 0 2 0 2 1v2c-1-1-1 0-2 0v1l3 2c0 1 0 1-1 2h2s-1 1-2 1c0 1 1 1 0 2-2 0-3 2-4 3 0 1-1 1-2 1l-2 2h0l-1 1c-1 1-2 1-3 2v1h-1-1 0c-1-1-2-1-3-2 0-1-2-2-3-4-4-2-6-4-7-8l-1-1h1c0-1 0-2 1-3l1-2 3-3 2 2h1z" class="K"></path><path d="M224 335c1-1 4 0 5-1s2-1 3-1l1 1h0c3 0 6 1 8 1v2h0-1-6 0l1 1v1l-1-1h-1v-2l-2 2-1-1c-1 0-1 0-2 1v-3c-2 0-2 1-4 1v-1z" class="D"></path><path d="M239 344c-1 0-2-1-3-1 0 0-1-1-2-1v-1c1 1 2 1 3 1h1c2 0 3 0 5 1h2s-1 1-2 1c0 1 1 1 0 2-2 0-3 2-4 3h0c-1-1-3-2-4-3-1 0-3-1-4-1l-3-2v-1h1l2 1c2 1 5 1 8 2v-1z" class="s"></path><path d="M229 342l2 1c2 1 5 1 8 2v-1l1 1h0-2c0 1 1 1 1 2h0-1c-1-1-2-1-3-1h0c-1 0-3-1-4-1l-3-2v-1h1z" class="X"></path><path d="M218 336l3-3 2 2h1v1c2 0 2-1 4-1v3l1 1c1 0 2 0 3 1h2 0c0 1 0 1-1 1v1c0 1-1 1-2 1l-2-1h-3 0l1 1-1 1h-4-1c-1 0-1 0-1 1h0-1c-1-1-2-2-2-3l2-1c-1-1-2 0-3 0 0-1 0-2 1-3l1-2z" class="P"></path><path d="M218 336l3-3 2 2c-1 0-1 1-2 1v1l1 1-1 1s0-1-1-1v-1c-1 1-1 1-1 2 0 0-1 0-1 1h0 1v1c-1-1-2 0-3 0 0-1 0-2 1-3l1-2z" class="c"></path><path d="M216 341c1 0 2-1 3 0l-2 1c0 1 1 2 2 3h1 0c0-1 0-1 1-1h1 4l1-1-1-1h0 3-1v1l3 2c1 0 3 1 4 1 1 1 3 2 4 3h0c0 1-1 1-2 1l-2 2h0l-1 1c-1 1-2 1-3 2v1h-1-1 0c-1-1-2-1-3-2 0-1-2-2-3-4-4-2-6-4-7-8l-1-1h1z" class="r"></path><path d="M231 345c1 0 3 1 4 1 1 1 3 2 4 3h0c0 1-1 1-2 1l-2 2h0l-4-3c1 0 3 1 4 1 1-1-1-1-1-2-1-1-2-1-3-1v-2z" class="J"></path><path d="M231 345c1 0 3 1 4 1 1 1 3 2 4 3h-1c-1-1-3-1-4-1-1-1-2-1-3-1v-2z" class="I"></path><path d="M223 350v-1c2 0 3 1 4 2l1-1 3 2v-1h0c1 1 2 2 3 2-1 1-2 1-3 2v1h-1-1 0c-1-1-2-1-3-2 0-1-2-2-3-4z" class="O"></path><path d="M222 344h4l1-1-1-1h0 3-1v1l3 2v2c-1 0-2 1-3 1 0 1-1 1-2 1v-1c-1 0-1-1-1-1h-1-1 0-2v-1l1-1v-1z" class="E"></path><path d="M222 345h3v1h-1-1v1h-2v-1l1-1z" class="B"></path><path d="M228 343l3 2v2c-1 0-2 1-3 1 0 1-1 1-2 1v-1h1 0c0-1 0-1-1-1v-1c1 0 2 0 3-1l-1-2z" class="F"></path><path d="M262 223c1-1 2-2 4-3 3-2 6-2 9-3l2 1-1 1h-3c1 1 2 1 2 1v1 1h1l2 1h0c-1 1-2 1-3 1s-1 1-1 3l-2-1-1 1h-1c-1 0-1 1-2 1-2 2-4 3-6 4v1c1 0 4-1 5-2l-1 2c-2 1-4 1-6 3h-1l-1-1h-2l-1 1c-2 1-3 2-4 4 0 1-1 2-1 2-1 0-1 0-1 1l-2-2c-1 2-1 2-2 3s-1 1-1 2v-2h0c0-1 0-2-1-2 0-1 0 0-1 0v1l-2 1v1h-1v-2-1c-1-1-1-2-1-3h0-1v-2h-1l-1 1v-1s0-1 1-2v-1-1-2h1l1-3 1-1h1 0c0 1 1 1 1 1h1l3-1s1 0 2 1l2-2-1 3c2-1 3-2 5-2v1c0 1 1 1 1 1l2-1c2-1 5-3 6-5z" class="O"></path><path d="M265 226v-1c0-1 0-1 1-1v-1h-1l1-1c1-1 2-1 3-1v1h-1v1h0l-2 2h2l-3 1z" class="J"></path><path d="M268 223c1 0 3-1 4-1h4l2 1h0c-1 1-2 1-3 1-2 0-5 0-7 1h-2l2-2z" class="L"></path><path d="M268 225c2-1 5-1 7-1-1 0-1 1-1 3l-2-1h-4c-1 0-2 1-3 1s-2 1-3 1c-1 1-1 1-1 2-1 0-2 1-2 1-1-1-2-1-2-2 1-1 3-2 5-2 1 0 2-1 3-1l3-1z" class="Y"></path><path d="M252 234c1 0 1 1 2 1l1 1c-2 1-3 2-4 4 0 1-1 2-1 2-1 0-1 0-1 1l-2-2c-1 2-1 2-2 3 1-2 2-5 4-7l3-3z" class="D"></path><path d="M252 234c1 0 1 1 2 1l-2 2c-1 0-1 1-1 1l-1 1v-1l-1-1 3-3z" class="I"></path><path d="M265 227c1 0 2-1 3-1h4l-1 1h-1c-1 0-1 1-2 1-2 2-4 3-6 4v1c1 0 4-1 5-2l-1 2c-2 1-4 1-6 3h-1l-1-1h-2l-1 1-1-1c-1 0-1-1-2-1 1-2 2-3 4-4l1-1c0 1 1 1 2 2 0 0 1-1 2-1 0-1 0-1 1-2 1 0 2-1 3-1z" class="X"></path><path d="M257 229c0 1 1 1 2 2l-1 1 1 1h0 1c-1 1-1 1-2 1v-1h-1l-1-1v-1-1l1-1z" class="k"></path><path d="M265 227c1 0 2-1 3-1h4l-1 1h-1c-1 0-1 1-2 1-3 0-4 3-6 3v-1c1 0 3-1 3-3h0z" class="L"></path><path d="M256 230v1 1l1 1h1v1l-2 1-1 1-1-1c-1 0-1-1-2-1 1-2 2-3 4-4z" class="K"></path><path d="M247 228l2-2-1 3c2-1 3-2 5-2v1c0 1 1 1 1 1l-7 9c-1 2-3 4-3 6h0c0-1 0-2-1-2 0-1 0 0-1 0v1l-2 1v1h-1v-2-1c-1-1-1-2-1-3h0-1v-2h-1l-1 1v-1s0-1 1-2v-1-1-2h1l1-3 1-1h1 0c0 1 1 1 1 1h1l3-1s1 0 2 1z" class="e"></path><path d="M240 234l1-2c1 0 2 0 3-1h2c-1 2-4 3-5 5l-1-1v-1z" class="F"></path><path d="M245 237c1-1 1-3 3-4l5-5c0 1 1 1 1 1l-7 9c-1-1-1-1-2-1z" class="G"></path><path d="M240 234v1l1 1h0c0 1 0 1-1 1v3c1 0 2 0 3-1h0l2-2c1 0 1 0 2 1-1 2-3 4-3 6h0c0-1 0-2-1-2 0-1 0 0-1 0v1l-2 1v1h-1v-2-1c-1-1-1-2-1-3h0-1v-2h-1c1-1 1-2 2-2l2-1z" class="C"></path><path d="M240 237v4 2 1 1h-1v-2-1c-1-1-1-2-1-3l2-2z" class="Q"></path><path d="M238 235h1c0 1 0 1 1 1v1l-2 2h0-1v-2h-1c1-1 1-2 2-2z" class="B"></path><path d="M247 228l2-2-1 3-2 2h-2c-1 1-2 1-3 1l-1 2-2 1c-1 0-1 1-2 2l-1 1v-1s0-1 1-2v-1-1-2h1l1-3 1-1h1 0c0 1 1 1 1 1h1l3-1s1 0 2 1z" class="K"></path><path d="M245 227s1 0 2 1c-2 0-3 1-4 1l-1-1 3-1z" class="M"></path><path d="M238 228l1-1h1 0c0 1 1 1 1 1h1l1 1c0 1-1 2-2 2-1 1-2 0-3 1 0 1-1 1-2 1v-2h1l1-3z" class="H"></path><path d="M238 228l1-1h1 0c0 1 1 1 1 1-1 1-2 2-4 3l1-3z" class="E"></path><path d="M511 535c4 15 6 29 5 44 0 4-1 8-1 11-1 4-5 19-4 22 1 2 11 8 13 9 10 6 21 10 32 11 4 0 7 0 10-1 10-1 18-4 27-9 0 1 1 1 1 2l-3 2c-8 4-16 6-25 7l-9 1h-1c0-1-1-1-2-1-1 1-2 1-3 1l-5-2c-6-1-12-3-17-5l-9-6-10-7-1-1c-1-2-1-3 0-5 2-5 3-11 3-17v-1l1-2h-1c-1-2 0-5 0-7s0-4 1-6h0c0-1-1-2-1-2l1-1v-3h0v-5-4l-7-6c-1-2-1-3-2-5v-2l1-1c2 1 3 2 3 4 1-1 1-1 1-2l-1-2v-3c1 1 2 3 3 4l1 3-2-11h0c1-2 1-2 1-4z" class="N"></path><path d="M509 548c1 1 1 2 2 3s2 5 2 7l-1-1c-1-1-2-3-3-3 0-2-1-3-1-4 1-1 1-1 1-2zm4 16c1 4 1 8 1 11 0 5-1 9-1 13h-1c-1-2 0-5 0-7s0-4 1-6h0c0-1-1-2-1-2l1-1v-3h0v-5z" class="R"></path><path d="M505 546c2 1 3 2 3 4 0 1 1 2 1 4 1 0 2 2 3 3l1 1v2l-7-6c-1-2-1-3-2-5v-2l1-1z" class="H"></path><path d="M505 546c2 1 3 2 3 4 0 1 1 2 1 4l-1-1-4-4v-2l1-1z" class="w"></path><path d="M593 622c0 1 1 1 1 2l-3 2c-8 4-16 6-25 7h1c1-1 1-1 2-1h1 1 0 1c1-1 1 0 2-1h1 2c0-1 1-1 1-1h1 0c-2 0-5 1-7 1l-3 1c-3 0-10 1-13 0 4 0 7 0 10-1 10-1 18-4 27-9z" class="L"></path><path d="M177 439v3h1 0c1 2 4 5 3 7l-1 2h1l1-1c1 1 1 2 1 4l-1 2c1 0 2-1 2-1l1 1c0 1 0 1 1 1 0 1 0 2 1 3h0l1 1s0 1 1 1l-1 1-1 3h1l1-2 1 1 1 1c-1 1-1 1-1 2-1 1-1 2-1 3h-2c1 0 1-1 1-2l-2 2c0 1-1 2-1 2v1c-1 1-1 2-2 2v1s-1 0-1 1h0c-1 1-1 1-2 1l-1-2h-1c-2 0-1 0-1-1h-1l-1-1-1-1-1-2c-1-1-1-2-2-3 0-1-1-1-2-2v-1l1-1 1-1c-1-1-1-2-3-2v-1h1c1-1 2-3 3-4l-2-2-1 1h0l1-2h0l-1-1 2-1c0-2 1-3 2-5 1-1 1-2 2-4 0-1 1-3 2-4z" class="C"></path><path d="M183 462c0-2 0-3-1-4h0l-1 1v-1h0v-1l1-1c1 0 2-1 2-1l1 1c0 1 0 1 1 1-1 1-1 2-1 3-1 0-1 1-2 2z" class="D"></path><path d="M171 469l2-2c1-2 2-3 3-4 0-1 2-2 3-2 0-1 1-1 1-2l1 1s-1 0-1 1h0v2c-1 0-2 2-2 2 0 2 2 5 0 6h-1-1v1c1 0 1 1 1 1l-2 2-1-1-1-2c-1-1-1-2-2-3z" class="c"></path><path d="M173 472l3-6h1v2c-2 2-2 4-3 6l-1-2z" class="Z"></path><path d="M186 457c0 1 0 2 1 3h0l1 1s0 1 1 1l-1 1-1 3h1l1-2 1 1 1 1c-1 1-1 1-1 2-1 1-1 2-1 3h-2c1 0 1-1 1-2l-2 2c0 1-1 2-1 2v1c-1 1-1 2-2 2v1s-1 0-1 1h0c-1 1-1 1-2 1l-1-2h-1c-2 0-1 0-1-1h-1l-1-1 2-2s0-1-1-1v-1h1 1c2-1 0-4 0-6 0 0 1-2 2-2h1c0 1 0 1-1 2v1l1 1v-1l1-1c0-1 1-2 1-3 1-1 1-2 2-2 0-1 0-2 1-3z" class="V"></path><path d="M177 473h0c1-1 2-1 3-3l1-1 1 1-2 2v1h0v1c0 1-1 2-1 3h-1c-2 0-1 0-1-1h-1l-1-1 2-2z" class="E"></path><path d="M178 477h-1c1-1 2-2 2-3h1c0 1-1 2-1 3h-1z" class="Z"></path><path d="M187 460h0l1 1s0 1 1 1l-1 1-1 3h0s0 1-1 1v1h0c0 1 0 2-1 3h-1v-2l-1 1-1-1 1-1c0-1 1-2 1-2v-1l1-2h1v-2l1-1z" class="e"></path><path d="M177 439v3h1 0c1 2 4 5 3 7l-1 2h1c-1 1-1 2-2 3v1l-2 1h0c0 4-4 5-5 8-1 1-1 2-2 2h-1l1-1 1-1c-1-1-1-2-3-2v-1h1c1-1 2-3 3-4l-2-2-1 1h0l1-2h0l-1-1 2-1c0-2 1-3 2-5 1-1 1-2 2-4 0-1 1-3 2-4z" class="D"></path><path d="M172 452h0c1 0 2-1 2-1h1v1h1v1l-1 1h-3v-2z" class="S"></path><path d="M175 454c1 1 1 1 2 1l1-1c0-1 1-1 1-2 0 0 0-1 1-1h1c-1 1-1 2-2 3v1l-2 1h0v1c-1 0-2 1-3 1v1h-1c0-1 0-2 1-3l1-1h0l-1-1c0 1-1 2-1 2l-1 1-2-2-1 1h0l1-2h0l1-2h1v2h3z" class="F"></path><path d="M172 457l1-1s1-1 1-2l1 1h0l-1 1c-1 1-1 2-1 3h1v-1c1 0 2-1 3-1v-1c0 4-4 5-5 8-1 1-1 2-2 2h-1l1-1 1-1c-1-1-1-2-3-2v-1h1c1-1 2-3 3-4z" class="G"></path><path d="M464 541h0v-3h1c0 2 1 4 0 6 0 1 0 1 1 1v3h1s1 1 2 1l2 2 1-1v-1h0 1c0 1 1 2 1 3h-1l1 3v4l1 3 2 4c-1 2-1 3-2 5v2l-2-2h0l-2-1c-1 0-2-1-2-2-1-1-1-2-1-2h-1c-1-1-1-2-2-3l-1-1v-1-1l-1-3c-1 0-1 1-2 2l-2-2h0c-1 1-1 2-1 3v3h-1l-1-1-3-3v-1l-1-2c-1-1-1-1-2-1v-2h-1c-1-1-1-1-1-3l-1-1 1-3 1 1h1v-2h2c0-1 0-2-1-3v-2h1 1-1l1-1 1 2c1-1 1-1 2-1h0l1-1h1c0 1 1 3 2 4h0 0c0-2 1-2 1-3l1-1h-1l1-1h0c1 1 1 2 1 3h1z" class="P"></path><path d="M469 553c1 0 2 1 2 2 1 1 0 1 0 2v1c-1-2-2-3-2-5z" class="f"></path><path d="M471 555h3v4l-1 1-2-2v-1c0-1 1-1 0-2z" class="k"></path><path d="M472 549h0 1c0 1 1 2 1 3h-1l1 3h-3c0-1-1-2-2-2h0c0-1 1-1 1-2 0 0-1-1-1-2l2 2 1-1v-1z" class="B"></path><path d="M458 539c0 1 1 3 2 4h0 0c0-2 1-2 1-3v5c0 2 2 6 1 8-1-1-1-2-1-3-1-2-1-3-1-5 0-1 0 0-1-1h0l-1-1c-1-1-1-2-1-4h1z" class="t"></path><path d="M455 557v-2h1 1 1v1l1 1c-1 1-1 2-1 3v3h-1l-1-1-3-3h0l1-1h1v-1z" class="Q"></path><path d="M456 555h1 1v1l1 1c-1 1-1 2-1 3v3h-1v-1c0-2 0-3-1-4h0v-3z" class="C"></path><path d="M475 562l2 4c-1 2-1 3-2 5v2l-2-2h0l-2-1 1-1s1 0 1-1h0c1 0 0-4-1-5l1-1h2z" class="f"></path><path d="M475 562l2 4c-1 2-1 3-2 5 0-2-1-6-2-8h-1l1-1h2z" class="D"></path><path d="M450 547v-2h2v1 1 1h0c1 1 1 2 1 3 0 2 0 3 1 5l1 1v1h-1l-1 1h0v-1l-1-2c-1-1-1-1-2-1v-2h-1c-1-1-1-1-1-3l-1-1 1-3 1 1h1z" class="G"></path><path d="M448 550c0 1 0 1 1 1v-1h2v2c0 1-1 1-1 1h-1c-1-1-1-1-1-3z" class="C"></path><path d="M453 558l-1-3v-4h1c0 2 0 3 1 5l1 1v1h-1l-1 1h0v-1z" class="b"></path><path d="M450 547v-2h2v1 1 1h0s0 1-1 1v1h-2v1c-1 0-1 0-1-1l-1-1 1-3 1 1h1z" class="B"></path><path d="M450 547v-2h2v1 1 1h0s0 1-1 1h0c0-1-1-1-2-2h1z" class="c"></path><path d="M457 539c0 2 0 3 1 4l1 1h0c1 1 1 0 1 1l-2 2-1 1c1 2 0 3 0 5h-1v-4c-1-2-2-3-2-4 0 0-1 0-1 1h-1v-1c0-1 0-2-1-3v-2h1 1-1l1-1 1 2c1-1 1-1 2-1h0l1-1z" class="Y"></path><path d="M458 543l1 1h0c1 1 1 0 1 1l-2 2-1-1c0-1 0-2 1-3zm-7-3h1l1 1c1 1 1 2 2 2v1l-1 1s-1 0-1 1h-1v-1c0-1 0-2-1-3v-2z" class="Z"></path><path d="M463 557c2-1 2-2 3-4h0 0 0c1 1 3 2 3 3v5c1 2 4 5 4 7 0 1-1 1-1 1l-1 1c-1 0-2-1-2-2-1-1-1-2-1-2h-1c-1-1-1-2-2-3l-1-1v-1-1l-1-3z" class="I"></path><path d="M465 563l1-1v-3c-1 0-1-1-1-1l1-1c0 1 1 2 1 3h0c0 1 1 2 1 3-1 1-1 1 0 3h-1c-1-1-1-2-2-3z" class="E"></path><path d="M467 560h1 0l1 1c1 2 4 5 4 7 0 1-1 1-1 1l-1 1c-1 0-2-1-2-2-1-1-1-2-1-2-1-2-1-2 0-3 0-1-1-2-1-3z" class="o"></path><path d="M182 208c11 5 24 5 35 7 7 2 14 5 21 6h11c2 0 4 0 5-1h1c7-4 12-8 19-12 1-1 3-3 5-4l-2 3c-1 2-3 2-4 4h0c-2 1-4 3-7 5-5 4-11 7-17 10l-2 2c-1-1-2-1-2-1l-3 1h-1s-1 0-1-1h0-1l-1 1-3 2v1h-2c0-1-1-1-1-2l2-2c1-1 1 0 1-1h1v-3c1 0 0 0 0-1h0c-1 1-2 2-3 2 1-1 1-1 1-2-1 0-2 1-3 2v-2h0 0l-2 2v-3c-1 0-2 2-2 3l-1-1c1 0 1-1 1-2h0-1c-1 0-1 0-1-1l-2 1v-1c-2 0-1 1-2 2v-2-1h-1c-1 2-2 4-2 7 0 1-1 2-2 3l-1-1-2 1c0-1 0-1-1-2-1 0-1 0-2 1h0c-1 0-1 0-2-1 0-1-1-1-1-2h0-2c-1 0-1 0-2-1-1 1-1 0-2 1 0-1-1-2-1-3v-1-1l-1-4v-1h-4 0 0-1c-1-1-1-1-2-1l-1 1-1-1-2 1c-1-2-3-3-5-4h0c0-1 0-2-1-3z" class="U"></path><path d="M236 226l3-3h0 4 8c-2 1-4 3-6 4l-3 1h-1s-1 0-1-1h0-1l-1 1-3 2v1h-2c0-1-1-1-1-2l2-2c1-1 1 0 1-1h1z" class="c"></path><path d="M200 220c0-2 0-3 1-4h0c0 1 1 2 1 2h0l1-2h1v2h0 1v-1l1-1v1h1 0 5l2 1h0 2v4c1-2 2-3 3-5-1 3-2 7-1 9 0 1-1 2-2 3l-1-1-2 1c0-1 0-1-1-2-1 0-1 0-2 1h0c-1 0-1 0-2-1 0-1-1-1-1-2h0-2c-1 0-1 0-2-1-1 1-1 0-2 1 0-1-1-2-1-3v-1-1z" class="V"></path><path d="M214 218h2v4 1 1c-1 1-1 1-1 2h-1v-1l-1-1v-3c0-1 0-2 1-3z" class="F"></path><path d="M214 218h2-1v1c0 1-1 2-2 2 0-1 0-2 1-3z" class="l"></path><path d="M212 217l2 1h0c-1 1-1 2-1 3v3l-1 1v1c-1-1-1-1-1-2h-1-1-3v-1c1 0 1-1 1-2l3 3v-1c1-2 1-2 1-4 0-1 0 0 1-1v-1z" class="F"></path><path d="M212 217l2 1h0c-1 1-1 2-1 3v3l-1 1h0v-7-1z" class="p"></path><path d="M206 217h1 0 5v1c-1 1-1 0-1 1 0 2 0 2-1 4v1l-3-3c0 1 0 2-1 2v-6z" class="t"></path><path d="M207 221c1-1 2-1 2-2h1v3 1 1l-3-3z" class="K"></path><path d="M117 280l-1-4c1-1 1-1 1-2v-2l1-1v-2s-1-1 0-2c0-1 2-2 3-3l3-8c1-2 2-3 3-4 2-2 3-5 3-8l4-18c2-13 3-27 10-40l1 1h1c1 0 1 1 2 2h0 3l1 1c-1 2-1 2-3 3h0c-2 1-3 2-4 3v1h2 0l-2 1-1 2-1 1c0 1 1 1 1 1-1 1-2 2-2 3h1c0 1-2 1-2 2h3l-3 2c-1 0-1 0-1 1l-1 2h1 1l-1 1c-2 2-2 6-2 9v1l1-1c1 1 1 1 1 2h0v4 2c0 3 1 7 1 11h0c0 1 1 2 1 3 0 0 3 2 3 3v1c-1-1-1-2-2-3l-3-3h0c0 2 1 5-1 6-1-1-2-1-2-2l-4-2v2c-1 4-4 7-5 10l-1 1h0c0 1-1 1-1 2s0 1 1 1c-1 1-1 1-2 1v1h0c0 1-1 1-1 1-1 1-1 2-1 2l-1 1h1 1c1 1 2 2 3 2l2 2c0 1-1 1-1 1v2h0c-1 0-2 0-3-1h-1c0 1 1 1 2 2h-1v1 1l-1 1-1-1h-1v1c-1 0-2 0-3 1v1l-2 1z" class="d"></path><path d="M148 189h3l1 1c-1 2-1 2-3 3h0-3c1-1 3-2 3-3-1 0-1 0-2 1h0v-1l1-1h0z" class="K"></path><path d="M144 200l-2-2h1v-1h-1-1c2-2 3-4 5-4h3c-2 1-3 2-4 3v1h2 0l-2 1-1 2z" class="O"></path><path d="M136 229h0c0 2 0 5 1 7v2l-1 1 1 2-1-1h0-1v2h-1c0-1 0-1-1-2 1-1 1-3 2-4 0-2 1-5 1-7z" class="g"></path><path d="M135 236c0 1 1 2 1 3h-2s0 1-1 1c1-1 1-3 2-4z" class="r"></path><path d="M122 266h1 1c1 1 2 2 3 2l2 2c0 1-1 1-1 1v2h0c-1 0-2 0-3-1h-1c0 1 1 1 2 2h-1v1 1l-1 1-1-1h-1c-1 0-2 0-2-2h0 2v-1c-1 0-1-1-1-1v-1h2 0c-1-1-1-2-2-3h0l2 1v-1c-1-1-1-1-1-2z" class="D"></path><path d="M138 222v1l1-1c1 1 1 1 1 2h0v4 2c0 3 1 7 1 11h0c0 1 1 2 1 3 0 0 3 2 3 3v1c-1-1-1-2-2-3l-3-3h0c0 2 1 5-1 6-1-1-2-1-2-2l-4-2c0-1 0-1 1-2h1v-2h1 0l1 1-1-2 1-1v-2c-1-2-1-5-1-7h0c0-2 0-3 1-5h0l1-2z" class="s"></path><path d="M138 222v1l1-1c1 1 1 1 1 2h0-1c-1 1-1 2-1 3l1 1c-1 1-1 2-1 3l-1-1v-6h0l1-2z" class="J"></path><path d="M137 238h0c1 2 2 3 2 4v1h0c1-1 1-2 0-4v-1c0-1 0-2-1-3l1-1c0 2 1 4 1 5v3h0c0 2 1 5-1 6-1-1-2-1-2-2l-4-2c0-1 0-1 1-2h1v-2h1 0l1 1-1-2 1-1z" class="E"></path><path d="M598 634l1 1-1 2c1 1 1 2 1 4h0c-1 2-2 3-3 4h0c-1 3-1 4-4 6 0 1 0 1 1 2v1h-1v2 1l1 2c-1 1-6 4-6 5-1 1-2 1-3 1h-1c-2 0-4 1-7 1h0l1-2h-1l-1 1v1h-1v-1c-1 0-1 0-2 1 1-4 6-4 6-8h-1l-1 1c-2 0-2 0-3 1h-1v-1c1 0 2-1 3-1 1-1 4-2 4-4h0v-1h0l-2-2s-1 0-1-1h0l2-1h1c1-1 2-1 2-2l-3 1v-1-1h-2c-2 0-3-1-4-1h-3c-1 0-1-1-1-1h2c-1-2-2-2-3-2l1-1c2 0 3-1 4-1l2-1h4v-1l1-1h0c2 1 4 1 6 0v-1c1 0 2 1 3 0 2 0 4-1 5-1s2-1 2-1l1 1c1 0 1-1 2-1z" class="j"></path><path d="M596 645c0-2 1-3 1-4v-3l1-1c1 1 1 2 1 4h0c-1 2-2 3-3 4z" class="O"></path><path d="M591 643l2-2s1 1 0 1c0 2-1 1 0 3-1 2-2 2-3 3s0 1-1 1l-1-1h0 0c1-1 2-3 3-4v-1z" class="C"></path><path d="M572 645c3 0 6 1 8-1h2 3v1c-1 0-3 1-4 2l-3 1v-1-1h-2c-2 0-3-1-4-1z" class="S"></path><path d="M592 654v2 1l1 2c-1 1-6 4-6 5-1 1-2 1-3 1h-1c4-3 7-7 9-11z" class="O"></path><path d="M579 637h0c2 1 4 1 6 0v-1c1 0 2 1 3 0 2 0 4-1 5-1s2-1 2-1l1 1c-4 2-8 3-13 4h-5v-1l1-1z" class="t"></path><path d="M582 644c4-2 8-4 12-4l-1 1-2 2h0c-1 0-1 1-1 1-2 1-3 2-5 3 0-1 0-1-1-1l1-1v-1h-3z" class="l"></path><path d="M578 658h0l2 1v1c1 0 1-1 2-1h0c0 1-4 5-5 5h-1l-1 1v1h-1v-1c-1 0-1 0-2 1 1-4 6-4 6-8z" class="m"></path><path d="M588 648l1 1-3 2h0 1 1c1 0 1-1 2 0-1 2-5 2-7 4-2 0-3 2-5 3h0-1l-1 1c-2 0-2 0-3 1h-1v-1c1 0 2-1 3-1 1-1 4-2 4-4h0v-1c1 0 2-1 4-2 1-1 4-1 5-3z" class="r"></path><path d="M591 643v1c-1 1-2 3-3 4h0 0c-1 2-4 2-5 3-2 1-3 2-4 2h0l-2-2s-1 0-1-1h0l2-1h1c1-1 2-1 2-2 1-1 3-2 4-2l-1 1c1 0 1 0 1 1 2-1 3-2 5-3 0 0 0-1 1-1h0z" class="G"></path><path d="M585 645l-1 1c1 0 1 0 1 1l-3 1h6c-3 1-6 2-9 2h-3l2-1h1c1-1 2-1 2-2 1-1 3-2 4-2z" class="Z"></path><path d="M471 448l1 2v1l3 3c2 2 3 5 4 7 1 1 3 4 4 6-1 0-1 0-2-1v1c-1 0-2-1-2-2-3 0-5-2-8-4-1-1-2-1-3-2s-3-2-3-3v-1h-3 0-1c1 1 1 2 2 3l2 2h0l3 3h0c2-1 3-1 4 0 1 0 2 1 3 2v1c1 1 4 2 4 4h0 0l-1-1h-1l1 1-1 1c-1-1-3-1-5-2h-7-7l-2-1c-1-1-2-1-3-2h-2l1-1h2l-8-5-2-1-4-4c-1 0-2-1-2-2s0-1-1-1l-1-1h1 6 0 0 5 2l1-1v1h6 4c2 1 4 0 6 0v-2h3c1 0 1 0 1-1z" class="j"></path><path d="M443 451h5 2l-1 1c0 1-1 1-2 1v-2h-4z" class="E"></path><path d="M463 460l1 1c1 2 2 2 3 3h-2l-2-1 1-2-1-1z" class="e"></path><path d="M471 448l1 2v1c-2-1-4 0-5 0v-2h3c1 0 1 0 1-1z" class="o"></path><path d="M440 455c1 0 2-1 3-1 0 0 0 1 1 1 0 1 1 2 1 2l1 1-2 1-4-4z" class="F"></path><path d="M475 454c2 2 3 5 4 7 1 1 3 4 4 6-1 0-1 0-2-1v1c-1 0-2-1-2-2-3 0-5-2-8-4-1-1-2-1-3-2s-3-2-3-3l6 3c-1-1-2-3-4-4h0c2 0 3 0 4 1l4-2z" class="X"></path><path d="M475 454c2 2 3 5 4 7h-1l-1-1c-1 0-2 0-3-1h1 1 1c-2-1-4-3-6-3l4-2z" class="E"></path><path d="M457 462c-1 0-4-3-5-3-1-1-2-2-3-4h1l1 1 2 2h2 0v-1h0 1c1 1 3 2 3 3h4l1 1-1 2 2 1h2l3 2h1v-1l-3-2c2-1 3-1 4 0 1 0 2 1 3 2v1c1 1 4 2 4 4h0 0l-1-1h-1l1 1-1 1c-1-1-3-1-5-2h-7-7l-2-1c-1-1-2-1-3-2h-2l1-1h2l-8-5-2-1 2-1-1-1h1v-1h0 1c3 3 6 5 9 7l1 1h1l-1-2z" class="O"></path><path d="M459 460h4l1 1-1 2-4-3z" class="C"></path><path d="M468 463c2-1 3-1 4 0 1 0 2 1 3 2v1h0l1 3c-2-1-3-1-5-2h0-1c-2-1-3-2-5-3h2l3 2h1v-1l-3-2z" class="G"></path><path d="M457 462c-1 0-4-3-5-3-1-1-2-2-3-4h1l1 1 2 2h2 0v-1h0 1c0 2 3 3 4 5 2 2 6 4 7 6-2 0-3-2-5-2-2-1-3-4-5-4z" class="V"></path><path d="M445 457h1v-1h0 1c3 3 6 5 9 7l1 1h1 0c2 2 5 2 7 4-4 0-7-2-11-3-3-2-5-3-8-5l-2-1 2-1-1-1z" class="F"></path><path d="M445 457h1v-1h0 1v1l2 2 1 1h-3 0-1l-2-1 2-1-1-1z" class="E"></path><path d="M508 608h1c-1 2-1 3 0 5l1 1 10 7 9 6c5 2 11 4 17 5l-2 1-1 2c1 1 2 1 3 2h5v2l-2-1-2 2c-1 0-2-1-4-1-1-1-3-2-4-2 0 1 0 1 1 2l1 1h-1-3 0-2c-1 0-1-1-2-2l-1 1c-2 1-6-1-9-2-1-1-2-1-3-2l1-1h0c-1-1-5-3-6-3h-4c0 2 5 1 7 3h-4c-1-1-2-1-3-1-2-1-3-2-4-2-1-2-3-2-5-3s-5-4-6-6v-2l2 3c2 2 5 5 7 5 0-1-2-2-2-2-2-1-3-3-4-5-2-2-2-4-3-6 1 1 2 3 3 4 1 4 4 5 6 8 1 0 2 1 3 2-1-3-3-5-5-7-1 0-1-1-1-2h0c1 1 1 2 2 3 2 0 4 4 7 4-1-1-3-2-4-3-1-2-2-3-3-5l1-1v-1h-1v-1-1h2c0-1 0-2-1-3v-1h2c1-1 1-2 1-3z" class="X"></path><path d="M510 614l10 7v2c-1 0-2-1-3-2v1c0 1 1 1 2 2s5 5 5 6h0c-2-1-4-3-6-5s-6-4-7-8l-1-3z" class="w"></path><path d="M508 608h1c-1 2-1 3 0 5v2c0 1 1 2 1 2 2 4 5 7 8 10 2 2 4 3 6 4 1 1 3 2 3 3v1c-1 0-2-1-3-2l-1-1-1 1c-4-1-8-4-10-7-1 0-1 0-1-1-1 0-4-3-5-4 0-1-1-2-1-3v-1h-1v-1-1h2c0-1 0-2-1-3v-1h2c1-1 1-2 1-3z" class="j"></path><path d="M520 621l9 6c5 2 11 4 17 5l-2 1-1 2c1 1 2 1 3 2h5v2l-2-1-2 2c-1 0-2-1-4-1-1-1-3-2-4-2 0 1 0 1 1 2l1 1h-1-3 0-2c-1 0-1-1-2-2-1 0-2 0-3-1l-2-2 1-1h0c-1-2-3-3-5-4 0-1-4-5-5-6s-2-1-2-2v-1c1 1 2 2 3 2v-2z" class="e"></path><path d="M529 634c2 1 4 3 6 4v2c-1 0-1-1-2-2-1 0-2 0-3-1l-2-2 1-1z" class="L"></path><path d="M549 638h0-2-2-1-1 0l-2-1-2-2 1-1c0 1 1 1 1 1h0c1 1 1 1 2 1v1h3 5v2l-2-1z" class="G"></path><path d="M267 522h1c0-2 1-3 2-5 0-1 2-5 3-5 0 0 0 1-1 2v1 2c-1 0-1 0-1 1h1v-1h1 1c0-1 1-2 1-3h0 1c-1 3-2 3-3 5l-1 1h1l3-3c0 2-1 2-2 3l-1 1c-3 1-7 3-10 5-1 2-2 3-2 5-3 4-4 8-6 13-1 2-3 4-4 6-1 0-2 1-2 2-3 4-4 8-7 11l-8 14-6 9c-4 7-6 16-11 23-3 4-6 8-8 12h0v-1l-1 1v-1c-2-2-3-5-3-8l-1-3c0-1-1-1-2-2l2-1 1 2v-1-1c1 0 1 0 2 1h0l1-1-1-1h1c1 0 1-1 2-1h0c1-1 0-1 0-2-1 0 0 0 0-1 1 2 2 3 4 5 1-1 1-1 2-1 0 0 1-1 1-2h0-2v-1-1c1 0 1 1 3 1h0v-1l-3-2c1-1 4 0 6 0-2-1-3-2-5-3 1 0 2 0 4 1l1-1c-1-1-2-1-4-2h4 1l-3-3c1 0 3 1 5 1l-5-4c1 1 2 2 4 2 1 0 1 0 1-1s-3-1-4-3c2 0 4 1 5 1v-1c-1 0-2-1-3-1v-1c1 1 2 1 3 1v-1c-1-1-3-1-4-3h0l4 3h1c1 0 1 0 1-1h0c-2-1-6-3-6-5h0l7 4v-1c1-2-4-4-3-6-1-2-2-2-3-3-1 0-2 0-2-1 1 0 2 0 3 1s2 1 3 1h0 1c2 0 4 0 6 1v-1c0-1-2-1-4-2-1 0-2-1-3-2h1c2 1 4 2 6 2h2l-1-1h0l2-1c1-1 2-3 2-4s1-1 1-1v-1c-1-1-4-1-5-2s-1-1-1-2c2 1 4 2 6 2-1-1-2-1-2-2 2-1 3 1 4 1h1c-1-1-2-1-3-2 2-1 2 0 4 0v-1c-1 0-2 0-2-1 1-1 2 0 3-1 0-1-1-1-1-2h2 0l-1-2c1 0 2 0 3-1l1-1c-1 0-2-1-2-1l-1-1c1 0 2 0 3-1h2 0l-1-1c-1 0-2-1-2-1h2c-1-1-2-1-4-2v-1l1-1-1-1v-1h0v-1l1 1c1 1 1 2 2 2h1c1 0 3 0 4 1v1l1-1v-1l1-2v-1c1-1 2-2 3-4 0-1 1-2 1-3 1-1 1-2 1-3l2-2s0-1 1-1h0c0-1 1-2 1-3h2v2l1-1z" class="N"></path><path d="M225 575c-1-2-2-2-3-3-1 0-2 0-2-1 1 0 2 0 3 1s2 1 3 1h0 0c1 2 3 4 5 4v1c-1 0-1 1-1 1-2-1-4-3-5-4z" class="E"></path><path d="M245 539v-1h0v-1l1 1c1 1 1 2 2 2h1c1 0 3 0 4 1v1h-1c-1 0-2-1-3-1h-1c1 1 3 2 3 3h-1-1c-1-1-2-1-4-2v-1l1-1-1-1z" class="V"></path><path d="M202 607l2-1 1 2v-1-1c1 0 1 0 2 1h0l1-1-1-1h1c1 0 1-1 2-1h0c1-1 0-1 0-2-1 0 0 0 0-1 1 2 2 3 4 5 1-1 1-1 2-1l-1 2v1c-1 1-5 7-5 9l2-2 1-1v-1h0v-1h1c0-1 1-1 1-2h0c1-1 1-1 1-2 1 0 0 0 1 1-3 4-6 8-8 12h0v-1l-1 1v-1c-2-2-3-5-3-8l-1-3c0-1-1-1-2-2z" class="E"></path><path d="M215 607v1c-1 1-5 7-5 9l2-2 1-1v-1h0v-1h1c0-1 1-1 1-2h0c1-1 1-1 1-2 1 0 0 0 1 1-3 4-6 8-8 12h0v-1c-1-3-3-6-3-9l3 5h0l1-1c0-1-2-3-2-4 1 0 2 1 3 2l4-6z" class="W"></path><path d="M498 578c1 0 0 0 1-1h1 0l3 6h0c0-1 0-2 1-3v2c1 1 1 2 2 3h1c1 2 2 3 3 4l1-1h0c0 1 1 1 1 2v1c0 6-1 12-3 17h-1c0 1 0 2-1 3h-2v1c1 1 1 2 1 3h-2v1 1h1v1l-1 1c-1-1 0-1 0-2 0 0-2-2-3-2l-3-6c-1-1-2-1-3-1h0v1 3l-2 4-2-1c0 1 0 1-1 2v-1-3l2-2s0-1 1-2c0-2 0-1 2-3l-1-1-1-2-2-1h0c-1-2-2-3-2-5h-1v-3l1-1c0-1-1-1-1-1v-3h1c1 0 1-1 1-2h0l1-1c1 1 1 1 2 1v-1h1c1-1 1-1 1-2h1 1 1l-1-1v-5h1z" class="F"></path><path d="M491 615l1-1c0-1 1-2 2-4v1 1h1l-2 4-2-1z" class="R"></path><path d="M499 599c0-1 0-2 1-3 1 1 1 2 1 3v4l-1 1-1-4v-1z" class="C"></path><path d="M494 600c0 1 0 2 1 3 1-1 1-5 1-6l1-1c0 4 0 7-1 11h0l-1-1-1-1-1-2c0-1 0-2 1-3z" class="Q"></path><path d="M498 609v-7c0-2 0-2 1-3v1l1 4 1-1c0 3 0 6 1 8v2c1 1 1 2 2 3v1h1v1l-1 1c-1-1 0-1 0-2 0 0-2-2-3-2l-3-6z" class="t"></path><path d="M499 600l1 4 1-1c0 3 0 6 1 8v2h0c-3-3-3-9-3-13z" class="V"></path><path d="M503 598c0 1 1 2 1 3h0c1 1 1 1 1 2 1 2 1 3 1 5h1s-1 1-1 2l1 1h-2v1c1 1 1 2 1 3h-2v1c-1-1-1-2-2-3v-2c-1-2-1-5-1-8v-4c1 0 1-1 2-1z" class="P"></path><path d="M506 608h1s-1 1-1 2l1 1h-2v1-1-3h1z" class="e"></path><path d="M503 598c0 1 1 2 1 3h0c1 1 1 1 1 2h-1l-1-1s-1 1-1 2c0 2 1 5 0 7-1-2-1-5-1-8v-4c1 0 1-1 2-1z" class="B"></path><path d="M498 578c1 0 0 0 1-1h1 0v2h0c-1 1-2 3-2 4h0c1 2 1 3 1 5-1 2 2 7-1 8h-1 0l-1 1c0 1 0 5-1 6-1-1-1-2-1-3-1 1-1 2-1 3l-2-1h0c-1-2-2-3-2-5h-1v-3l1-1c0-1-1-1-1-1v-3h1c1 0 1-1 1-2h0l1-1c1 1 1 1 2 1v-1h1c1-1 1-1 1-2h1 1 1l-1-1v-5h1z" class="Y"></path><path d="M492 591c1 0 1 0 2-1v1l1 1h-1l-2-1z" class="K"></path><path d="M493 586h1c1-1 1-1 1-2h1c0 1 1 2 1 4h0c-1 1-1 2-1 3h-1-1v-1h-1v-3-1z" class="j"></path><path d="M492 593h0c1 1 1 1 1 3v1c0 1 1 2 1 3-1 1-1 2-1 3l-2-1h0c-1-2-2-3-2-5 0-1 1-1 1-1h2v-3z" class="l"></path><path d="M492 596h1c-1 0-1 1-1 2-1 1-1 0-1 2l1 1h0l-1 1h0c-1-2-2-3-2-5 0-1 1-1 1-1h2z" class="L"></path><path d="M498 578c1 0 0 0 1-1h1 0v2h0c-1 1-2 3-2 4h0c1 2 1 3 1 5-1 2 2 7-1 8h-1 0c-1-2-1-4 0-5 2-2 0-2 1-4-1-1-1-1-1-2v-1h1l-1-1v-5h1z" class="C"></path><path d="M491 586c1 1 1 1 2 1v3h1c-1 1-1 1-2 1v1h0-1v1h1v3h-2s-1 0-1 1h-1v-3l1-1c0-1-1-1-1-1v-3h1c1 0 1-1 1-2h0l1-1z" class="I"></path><path d="M491 586c1 1 1 1 2 1v3h1c-1 1-1 1-2 1v1h0c-1-2-1-3-1-4l-1-1h0l1-1z" class="E"></path><path d="M488 592v-3h1c1 3 1 5 1 7 0 0-1 0-1 1h-1v-3l1-1c0-1-1-1-1-1z" class="f"></path><path d="M500 577l3 6h0c0-1 0-2 1-3v2c1 1 1 2 2 3h1c1 2 2 3 3 4l1-1h0c0 1 1 1 1 2v1c0 6-1 12-3 17h-1c0 1 0 2-1 3l-1-1c0-1 1-2 1-2h-1c0-2 0-3-1-5 0-1 0-1-1-2h0c0-1-1-2-1-3-1-1-1-2-2-2 0-1-1-2-1-3s-1-2-1-3v-2c0-2 0-3-1-5h0c0-1 1-3 2-4h0v-2z" class="K"></path><path d="M500 577l3 6h0l1 1h-1v1 1h-1v1c0 1 1 1 1 2h-1v-1c-1-1-2-2-2-4 0-1 0-1-1-1h-1 0c0-1 1-3 2-4h0v-2z" class="V"></path><path d="M499 590l1-2 2 2c0 1 0 2 2 3 1 2 1 3 2 4 2 3 2 4 2 7 0 1 0 1-1 1v3h-1c0-2 0-3-1-5 0-1 0-1-1-2h0c0-1-1-2-1-3-1-1-1-2-2-2 0-1-1-2-1-3s-1-2-1-3z" class="j"></path><path d="M504 580v2c1 1 1 2 2 3h1c1 2 2 3 3 4l1-1h0c0 1 1 1 1 2v1c0 6-1 12-3 17h-1c0-2 0-3 1-5v-2c0-2-1-3-1-4h1v-1c-1-2-2-4-3-5s-2-1-2-3c0-1-1-1-1-2v-1-1h1l-1-1c0-1 0-2 1-3z" class="C"></path><path d="M510 589l1-1h0c0 1 1 1 1 2v1l-2-2z" class="a"></path><path d="M176 202h0c0-1 0-2-1-2h1l1 2h1l4 6c1 1 1 2 1 3h0c2 1 4 2 5 4l2-1 1 1 1-1c1 0 1 0 2 1h1 0 0 4v1l1 4v1 1c0 1 1 2 1 3 1-1 1 0 2-1 1 1 1 1 2 1h2 0c0 1 1 1 1 2 1 1 1 1 2 1h0c1-1 1-1 2-1 1 1 1 1 1 2l2-1 1 1c1-1 2-2 2-3 0-3 1-5 2-7h1v1 2c1-1 0-2 2-2v1l2-1c0 1 0 1 1 1h1 0c0 1 0 2-1 2l1 1c0-1 1-3 2-3v3l2-2h0 0v2c1-1 2-2 3-2 0 1 0 1-1 2 1 0 2-1 3-2h0c0 1 1 1 0 1v3h-1c0 1 0 0-1 1l-2 2c0 1 1 1 1 2h2v-1l3-2-1 3h-1l-6 4h-1-1l-1-1v1c-1 0 0 0-1-1-1 0-1 1-1 1h-1l-1-1c0 2 0 2-1 2v-1h-1 0-2c-1-1-1 0-2 0v-1h-1-2c0-1-1 0-2 0h-1-1 0-1-2v-1-2l-3 1c-1 0-2 0-3 1h0 0l-1 1v1 1h-1 0v-1c-2 1-1 1-3 1v-1h-2l-1-2-5-7h-1c0-3-2-6-3-8s-3-5-4-7l-4-9z" class="j"></path><path d="M207 233l2-1h0v2h-2v-1z" class="F"></path><path d="M188 226h0 1c0 1 1 2 1 2h0v-1-1l6 9h-2l-1-2-5-7z" class="L"></path><path d="M188 215l2-1 1 1 1-1c1 0 1 0 2 1h1 0 0 4v1h-1v4h0c-1-1-1-3-1-4h-1v2l-1 1h-1v1h-3v-1c-1-2-2-3-3-4z" class="K"></path><path d="M191 219l1-1h1l1-2v1c1 0 1 1 1 2h-1v1h-3v-1z" class="D"></path><path d="M221 220v2c1-1 0-2 2-2v1l2-1c0 1 0 1 1 1h1 0c0 1 0 2-1 2l1 1c0-1 1-3 2-3v3c0 1-1 2-1 3l-2 1h0v-1h-1c-1 0-1 1-2 2v1-4c-1 0-2 2-3 3h0c1-3 1-5 2-7-1 1-1 2-1 3l-3 3c1-2 1-5 3-8z" class="K"></path><defs><linearGradient id="F" x1="180.094" y1="215.846" x2="185.271" y2="211.593" xlink:href="#B"><stop offset="0" stop-color="#c7c9ca"></stop><stop offset="1" stop-color="#f1eeef"></stop></linearGradient></defs><path fill="url(#F)" d="M176 202h0c0-1 0-2-1-2h1l1 2h1l4 6c1 1 1 2 1 3h0l7 15v1 1h0s-1-1-1-2h-1 0-1c0-3-2-6-3-8s-3-5-4-7l-4-9z"></path><path d="M229 224l2-2h0 0v2c1-1 2-2 3-2 0 1 0 1-1 2 1 0 2-1 3-2h0c0 1 1 1 0 1v3h-1c0 1 0 0-1 1l-2 2c0 1 1 1 1 2h2v-1l3-2-1 3h-1l-6 4h-1-1l-1-1v1c-1 0 0 0-1-1-1 0-1 1-1 1h-1l-1-1h0c1-1 1-1 1-2l-1-1h0c-1 0-1 0-2 1-1-2 1-2 1-4-1 1-1 2-2 3h-1c0-1 0-2 1-2h0c1-1 2-3 3-3v4-1c1-1 1-2 2-2h1v1h0l2-1c0-1 1-2 1-3z" class="P"></path><path d="M169 453l1 1h0l-1 2h0l1-1 2 2c-1 1-2 3-3 4h-1v1c2 0 2 1 3 2l-1 1-1 1v1h-2l-3 3h4v2c-1 1-1 1-2 1l2 2s1 0 1-1l1 1-2 2 1 1 3-2v-1c1 2 0 2 0 3h1l1 1c0 1-1 1-1 2l-2 3h-1l-1 1-1-1c-1 0-2 1-3 2l-2 1c-2 1-3 2-6 3h-2-2c-1-1-2-1-2-2l1-1c0-2-2-1-2-2s1-1 1-2c-1-1-1-1-2-1 0-1 0-2-1-3v-1c1-1 1-1 1-2-1 0-1-1-2-1h-1c1-1 1-2 2-3h-1-1l1-2c-1 0-1 0-2-1h2 1v-1c2-1 3-1 5-3 0 0 1-1 1-2-2 1-2 2-4 2h0c1-1 1-1 2-3h-1c0-1 1-2 2-2h1l1-1h0c1-1 1-2 1-3h0c1-1 2-1 3-1h0c2 0 3-1 5-2v1 2c2-1 3-2 5-3z" class="N"></path><path d="M168 477l1 1-3 3c0 1-1 2-2 2v1l-2 1c-3 2-5 2-8 2 3-2 6-2 9-4-2 0-4 1-6 1h0l1-1c3-1 4-2 6-4h1c1-1 2-1 3-2z" class="V"></path><path d="M166 473l2 2s1 0 1-1l1 1-2 2c-1 1-2 1-3 2h-1c-2 2-3 3-6 4l-1 1h0-1v-1l-2-2-1 1c3-2 8-3 11-6h1c0-1 1-2 1-3z" class="E"></path><path d="M154 481c3 0 6 0 9-2h0 1c-2 2-3 3-6 4l-1 1h0-1v-1l-2-2z" class="d"></path><path d="M169 478l3-2v-1c1 2 0 2 0 3h1l1 1c0 1-1 1-1 2l-2 3h-1l-1 1-1-1c-1 0-2 1-3 2l-2 1c-2 1-3 2-6 3h-2c3-1 8-3 9-6v-1c1 0 2-1 2-2l3-3z" class="F"></path><path d="M166 463h0c1 0 1 0 2-1h1v1s0 1 1 2l-1 1v1h-2l-3 3h4v2c-1 1-1 1-2 1 0 1-1 2-1 3h-1l-1-1v1c-1 1-2 1-4 1-1 1-3 1-4 2h-1-2v-1l1-1h0-1l-1-1 1-1h1s1-1 2-1l-1-1h-1 3c1-1 2-1 3-2 0-1 0-2 1-2h-1l2-2 4-2 1-2z" class="I"></path><path d="M160 469h2l-1 2c1 0 1-1 2-1v1l-2 2v-2c-2 1-3 2-4 2-1 1-3 2-4 2 0 0 1-1 2-1l-1-1h-1 3c1-1 2-1 3-2 0-1 0-2 1-2z" class="x"></path><path d="M152 475v1h3c2 0 3-1 4-2h2 2 0l-2 2c-2-1-5 1-6 2v1h-1-2v-1l1-1h0-1l-1-1 1-1z" class="s"></path><path d="M166 463h0c1 0 1 0 2-1h1v1s0 1 1 2l-1 1v1h-2 0c-1 0-2 0-3 1v-1l-2 1-1-1 4-2 1-2z" class="R"></path><path d="M169 453l1 1h0l-1 2h0l1-1 2 2c-1 1-2 3-3 4h-1v1c2 0 2 1 3 2l-1 1c-1-1-1-2-1-2v-1h-1c-1 1-1 1-2 1h0l-1 2-4 2-2 2h1c-1 0-1 1-1 2-1 1-2 1-3 2h-3-1v-1l1-1h-3l-2-2-1 1c-1 0-1 0-2-1h2 1v-1c2-1 3-1 5-3 0 0 1-1 1-2-2 1-2 2-4 2h0c1-1 1-1 2-3h-1c0-1 1-2 2-2h1l1-1h0c1-1 1-2 1-3h0c1-1 2-1 3-1h0c2 0 3-1 5-2v1 2c2-1 3-2 5-3z" class="B"></path><path d="M161 462c1-2 3-2 5-4l1 1c-1 0-1 1-2 1-1 1-2 1-2 2-1 0-1 0-1 1l1 1c1 0 2-1 3-1h0l-1 2c-2-1-4 1-6 2h-2v1l-1-1h0l5-4v-1z" class="I"></path><path d="M159 455h0c2 0 3-1 5-2v1 2c-1 1-3 1-4 3 0 1-1 1-2 2h-1 0-1c-1 1-2 1-4 2 1-1 1-2 2-3l1-1h0c1-1 1-2 1-3h0c1-1 2-1 3-1z" class="i"></path><path d="M151 462c0-1 1-2 2-2h1c-1 1-1 2-2 3 2-1 3-1 4-2h1l-3 3h2c0 1-1 1-1 2l6-4v1l-5 4h0l1 1v-1h2c2-1 4-3 6-2l-4 2-2 2h1c-1 0-1 1-1 2-1 1-2 1-3 2h-3-1v-1l1-1h-3l-2-2-1 1c-1 0-1 0-2-1h2 1v-1c2-1 3-1 5-3 0 0 1-1 1-2-2 1-2 2-4 2h0c1-1 1-1 2-3h-1z" class="t"></path><path d="M148 468h1 2c2 0 3 0 4-1v1c-2 1-3 2-4 3 1 0 3-1 5-1h0c0 1-1 1-2 1 0 1-1 1-2 1l1-1h-3l-2-2-1 1c-1 0-1 0-2-1h2 1v-1z" class="J"></path><path d="M626 510c7 7 13 14 17 23 2 3 3 6 4 10 5 13 7 27 7 42 0 18-6 38-18 52-3 4-6 7-9 9l-5 5-1-1h1v-2c-5 4-9 7-15 9v-1l4-2s1-2 2-2h-1c1-2 0-2 2-4v-1l-2 1h-1l3-3h0l4-4c3-1 6-2 8-4l1 1c1-1 7-5 8-5h1l5-7c0-1 0-1-1-1 0-2 1-3 2-4h-1l3-7v-1h1c1-2 1-4 2-6l-2-1v-7h1l1-1-1-6v-5h1v1-2h1c0 1 1 2 1 3l1-1v-2c1-2 1-2 1-5h1l-1-8c0-2 0-5-1-7-1-5-1-10-3-15l-2-5v-3c0-1 0-2-1-3-4-11-12-20-19-29v-1h1z" class="N"></path><path d="M652 581c0 4 0 11-2 14v-2h0v-3c0-1-1-1-2-1v-3c0 1 1 2 1 3l1-1v-2c1-2 1-2 1-5h1z" class="S"></path><path d="M645 613h0 1 1c-2 5-4 9-6 13 0-1 0-1-1-1 0-2 1-3 2-4h-1l3-7v-1h1z" class="g"></path><path d="M647 598l1 7c0-3 1-6 2-8v-1c0 6-2 11-3 17h-1-1 0c1-2 1-4 2-6l-2-1v-7h1l1-1z" class="e"></path><path d="M647 586h1v3c1 0 2 0 2 1v3h0v2 1 1c-1 2-2 5-2 8l-1-7-1-6v-5h1v1-2z" class="n"></path><path d="M636 633s0 1-1 1c-3 5-8 10-13 14s-9 7-15 9v-1l4-2s1-2 2-2h-1c1-2 0-2 2-4v-1l-2 1h-1l3-3h0l4-4c3-1 6-2 8-4l1 1c1-1 7-5 8-5h1z" class="P"></path><path d="M636 633s0 1-1 1h0c-3 3-6 5-9 6-1 0-1 0-1-1l2-1c1-1 7-5 8-5h1z" class="f"></path><path d="M615 645l5-1c-2 3-5 6-7 8h-1c1-2 0-2 2-4v-1c1 0 1 0 1-2z" class="k"></path><path d="M618 641c3-1 6-2 8-4l1 1-2 1c0 1 0 1 1 1l-6 4-5 1c0 2 0 2-1 2l-2 1h-1l3-3h0l4-4z" class="l"></path><path d="M625 639c0 1 0 1 1 1l-6 4-5 1v-1c3-2 6-3 10-5z" class="O"></path><path d="M198 343v-2h3c1-1 1-2 2-2s1 0 1-1l1 1c1 0 1 0 2 1h0c1 0 2 0 3 1v-1c1 1 1 1 2 1h0c1 5 2 7 5 9 1 1 3 2 3 2l2 3 2 1c1 1 1 1 1 2-1 1-1 1-2 1l-2-1h0 0v3l-1 1v1h0l-1-1c0 1 0 2 1 3-2 1-4 2-6 2-10 2-18 1-26-4v-2h1v-1c1-2 3-2 4-5h0v-3h2-3c1-1 2-2 2-3h3c-1 0-1 0-2-1l2-2-1-1v-1l2-1z" class="W"></path><path d="M202 353h1v1h-1v3c0 1-1 1-1 2v2c-1 1-1 3-2 4 0-2 1-4 1-6v-3-1-1h1l1-1z" class="G"></path><path d="M198 356v-1c0-1-1-1-2-1 0-1 1-1 1-2h2 0v1h2 1l-1 1h-1v1 1c-1 1-1 2-1 4l-1 2-1 1v-1c1-1 1-2 1-3-1 0-1 1-2 1 1-1 2-2 2-4h0z" class="E"></path><path d="M193 358c1 1 1 1 2 1 1-1 2-2 3-4v1h0c0 2-1 3-2 4 1 0 1-1 2-1 0 1 0 2-1 3v1l1-1c0 1 0 1-1 2 0-1-1-1-2-1h-1 0v-1h-1 0c0-1 1-1 1-2h0c-1 0-1 0-1 1l-1 1h-1v-1c1-1 1-2 2-3z" class="J"></path><path d="M202 350c-1 0-2 1-3 2h0-2c0 1-1 1-1 2 1 0 2 0 2 1v1-1c-1 2-2 3-3 4-1 0-1 0-2-1l1-1h-1c-1 1-2 2-4 3 1-2 3-2 4-5h0v-3h2l7-2z" class="F"></path><path d="M203 354l1 1h0v-2h1c1 1 1 1 1 2h1v-2c1 1 1 2 2 2 0-1 0-1 1-2h0v2h1v1h0-1-1l-1 1v5 1l-2 1h0v-3c-1 0-1 0-1 1s-1 2-1 2c-1 0 0-1-1-2-1 1-1 1-1 2h-1c0-1 0-2 1-3h-1v-2c0-1 1-1 1-2v-3h1z" class="l"></path><path d="M212 341c1 5 2 7 5 9 0 1 0 2 1 4h-1l-1-1v2c-1-1 0-2-1-3-1 1-1 3-2 4h0c-1-1-1-1-1-2l-1 1h-1v-2h0c-1 1-1 1-1 2-1 0-1-1-2-2v2h-1c0-1 0-1-1-2h-1v2h0l-1-1v-1h-1-1-2v-1c1-1 2-2 3-2v-1c4-1 8-3 10-8h0z" class="j"></path><path d="M198 343v-2h3c1-1 1-2 2-2s1 0 1-1l1 1c1 0 1 0 2 1h0c1 0 2 0 3 1v-1c1 1 1 1 2 1-2 5-6 7-10 8v1l-7 2h-3c1-1 2-2 2-3h3c-1 0-1 0-2-1l2-2-1-1v-1l2-1z" class="N"></path><path d="M207 340c1 0 2 0 3 1-2 2-3 3-5 4v-4 1l2-2z" class="r"></path><path d="M202 345c0-1 0-2 1-2 1-1 0-2 1-3l1 1v4l-3 2v-2z" class="t"></path><path d="M198 343v-2h3c1-1 1-2 2-2s1 0 1-1l1 1c1 0 1 0 2 1h0l-2 2v-1l-1-1c-1 1 0 2-1 3-1 0-1 1-1 2l-1-1-1-1h-2z" class="C"></path><path d="M198 343h2l1 1 1 1v2l-5 2c-1 0-1 0-2-1l2-2-1-1v-1l2-1z" class="X"></path><path d="M198 343h2l1 1-1 1h-1c-1 0-1 1-2 1l-1-1v-1l2-1z" class="I"></path><path d="M217 350c1 1 3 2 3 2l2 3 2 1c1 1 1 1 1 2-1 1-1 1-2 1l-2-1h0 0v3l-1 1v1h0l-1-1s-1 0-1-1c-1 1-1 2-2 2l-1-1c0 1-1 1-1 1-2 1-4 1-6 1v-1-1-5l1-1h1 1 0v-1l1-1c0 1 0 1 1 2h0c1-1 1-3 2-4 1 1 0 2 1 3v-2l1 1h1c-1-2-1-3-1-4z" class="b"></path><path d="M212 359h1 0v2h-1v-1-1z" class="K"></path><path d="M208 357l1-1h1c0 1-1 3-1 4h-1v-3z" class="q"></path><path d="M218 361c1-1 0-1 1-3 0 1 0 1 1 1v2h1l-1 1v1h0l-1-1s-1 0-1-1z" class="H"></path><path d="M217 350c1 1 3 2 3 2-2 1-1 2-2 3h0v5c-1-1-1-2-1-3-1-2 0-2 0-3h1c-1-2-1-3-1-4z" class="m"></path><path d="M218 355c1-1 0-2 2-3l2 3 2 1c-1 1-2 1-2 1-2 0-3-1-4-2z" class="C"></path><path d="M213 356h0c1-1 1-3 2-4 1 1 0 2 1 3v-2l1 1c0 1-1 1 0 3h-1c0 2-1 3-1 4h0l-1 1v-2l1-1c0-1-1-1-1-2-1 0-1 0-2 1 0-1 0-1 1-2z" class="Z"></path><path d="M437 529c1 2 2 4 2 5s0 2 1 3l1 1v-2h1v1c1 1 1 2 1 3s0 1 1 2v1-2-1c1 0 1-1 1-1h1c0 1 0 1 1 1v1c1 0 1 4 1 5l-1 3 1 1c0 2 0 2 1 3h1v2c1 0 1 0 2 1l1 2v1l3 3c-1 1-2 1-2 2l-1 1 1 1v3h-1v1c0 2 0 3 1 4v3h-1 0l-2 2h0c-1 1-1 1-1 3-1 1-1 2-1 3v8h-1v-1c-1 0-2 1-2 1l-1-1v-3-1c-1 0-1-1-1-2l-1-2v-1c-1 0-1 1-1 2-1 1-1 2-1 3v3l-2-2c-1-3-1-7-1-11v-3-1h-1c-1-2-1-4-1-6-1-1-1-2-1-4h1v1h3l-1-2h1l-1-3v-1-3h-1l1-1c0-1 0-1 1-2l1-1-1-2c-1-2-2-3-3-4-1 0-1-1-1-2 0 0-1-1-1-2h1v-1c-2-1-2-2-2-3 0 0 1 0 1 1h0 1v-2h1l1-1v-1c0-1 1-1 0-2v-4z" class="e"></path><path d="M439 559s1 0 1 1c1 2 0 3 0 5l-1-2-1-3 1-1z" class="F"></path><path d="M443 583c1-1 0-1 1-1l1 2c0 1-1 2-1 2l-1-2v-1z" class="B"></path><path d="M440 552c1 1 0 1 0 2v2l-1 1v2l-1 1v-1-3h-1l1-1c0-1 0-1 1-2l1-1z" class="I"></path><path d="M439 563l1 2c1 1 2 3 1 5h0c-1-1-1-2-2-3v-2l-1-2h1z" class="t"></path><path d="M438 575l2 1c0 3-1 10 1 12v3l-2-2c-1-3-1-7-1-11v-3z" class="K"></path><path d="M436 568c-1-1-1-2-1-4h1v1h3v2 1c0 1 0 2-1 3h0c1 1 0 2 0 3h-1c-1-2-1-4-1-6z" class="l"></path><path d="M436 565h3v2 1c0 1 0 2-1 3h0c-1-2-1-4-2-6z" class="C"></path><path d="M446 557l2 1c1 2 0 4 2 7l-1 2v-1c-1 2-1 4-1 6 1 1 1 3 1 4s-1 2-1 3l-1-2c-1-1-1-2-1-3-1-3-2-6-2-9h1 0c1-2 0-4 1-5v-1l-1-1h1 1l-1-1z" class="E"></path><path d="M446 557l2 1c1 2 0 4 2 7l-1 2v-1h0c0-1 0-1-1-2v-1l-1-3v-1h-1l-1-1h1 1l-1-1z" class="P"></path><path d="M444 565c1 1 2 4 2 5 1 2 0 4 1 5v1c1-1 1-3 1-4 1 1 1 3 1 4s-1 2-1 3l-1-2c-1-1-1-2-1-3-1-3-2-6-2-9z" class="p"></path><path d="M450 565c1 2 0 5 0 7h0c0 2 1 3 1 4s0 1-1 2c0 1 0 1 1 1-1 1-1 1-1 3-1 1-1 2-1 3v8h-1v-1c-1 0-2 1-2 1l-1-1v-3-1c-1 0-1-1-1-2 0 0 1-1 1-2v-3c1 0 1 0 1-1h0c0-1 0-2 1-3l1 2c0-1 1-2 1-3s0-3-1-4c0-2 0-4 1-6v1l1-2z" class="I"></path><path d="M447 577l1 2v1c0 1 0 1-1 1 0 2-2 6-1 8l1 1s1 1 1 2c-1 0-2 1-2 1l-1-1v-3-1c-1 0-1-1-1-2 0 0 1-1 1-2v-3c1 0 1 0 1-1h0c0-1 0-2 1-3z" class="C"></path><path d="M447 549l1 1c0 2 0 2 1 3h1v2c1 0 1 0 2 1l1 2v1l3 3c-1 1-2 1-2 2l-1 1 1 1v3h-1v1c0 2 0 3 1 4v3h-1 0l-2 2h0c-1 0-1 0-1-1 1-1 1-1 1-2s-1-2-1-4h0c0-2 1-5 0-7-2-3-1-5-2-7v-2l-1-1v-1h1l-1-1v-3-1z" class="E"></path><path d="M453 569c0-1 0-3-1-4v-2h0 1v2h0l1 1v3h-1z" class="r"></path><path d="M450 572c1-1 0-2 1-3 1 2 0 6 2 8h0l-2 2h0c-1 0-1 0-1-1 1-1 1-1 1-2s-1-2-1-4z" class="t"></path><path d="M447 549l1 1c0 2 0 2 1 3h1v2 4l1 1-1 1 1 1v7c-1 1 0 2-1 3h0c0-2 1-5 0-7-2-3-1-5-2-7v-2l-1-1v-1h1l-1-1v-3-1z" class="K"></path><path d="M447 554h2c1 1 1 2 0 3h-1v-1l-1-1v-1z" class="b"></path><path d="M437 529c1 2 2 4 2 5s0 2 1 3l1 1v-2h1v1c1 1 1 2 1 3s0 1 1 2v1-2-1c1 0 1-1 1-1h1c0 1 0 1 1 1v1c1 0 1 4 1 5l-1 3v1 3l1 1h-1v1l1 1v2l-2-1c-1-1-1-1-1-2v-1l-1 3h0c0 1 0 1-1 1v-1l-1-1c-1-1-1-2-2-2 0-1 1-1 0-2l-1-2c-1-2-2-3-3-4-1 0-1-1-1-2 0 0-1-1-1-2h1v-1c-2-1-2-2-2-3 0 0 1 0 1 1h0 1v-2h1l1-1v-1c0-1 1-1 0-2v-4z" class="D"></path><path d="M441 550v-1c1 0 1 0 1 1s0 2 1 3v4l-1-1v-4c-1-1-1-1-1-2z" class="E"></path><path d="M440 546c1 1 1 2 1 4 0 1 0 1 1 2v4c-1-1-1-2-2-2 0-1 1-1 0-2l-1-2 1-1v-3z" class="k"></path><path d="M440 546v-4h1v1l2-1 1 2c1 1 1 3 1 5l-1-1c-1 1-1 1-1 2h-1c0-1 0-1-1-1v1c0-2 0-3-1-4z" class="C"></path><path d="M444 543v-2-1c1 0 1-1 1-1h1c0 1 0 1 1 1v1c1 0 1 4 1 5l-1 3v1c-1 0-1 0-2-1 0-2 0-4-1-5v-1z" class="e"></path><path d="M437 529c1 2 2 4 2 5s0 2 1 3l1 1v-2h1v1c1 1 1 2 1 3s0 1 1 2v1 1l-1-2-2 1v-1h-1v4 3l-1 1c-1-2-2-3-3-4-1 0-1-1-1-2 0 0-1-1-1-2h1v-1c-2-1-2-2-2-3 0 0 1 0 1 1h0 1v-2h1l1-1v-1c0-1 1-1 0-2v-4z" class="G"></path><path d="M442 537v3h-1l-1-1-2 1 1-2-1-1c0-1 0-2 1-3 0 1 0 2 1 3l1 1v-2h1v1z" class="V"></path><path d="M435 544s-1-1-1-2h1c0 1 1 3 2 3v-2h0l3 3v3l-1 1c-1-2-2-3-3-4-1 0-1-1-1-2z" class="f"></path><path d="M178 551h4c1 1 0 1 1 1l-3 9 1 1v1s1 1 1 2l-3 9c-1 1-1 4-1 5-1 4-2 8-2 12 0 5 0 10-4 14v2c-1 0-1 0-1 1l-3 3-5 5s-1 1-2 1c-1 1-2 2-2 3-1 1-2 3-3 3v-1h-3c-2 0-2 0-3 1v-1l1-1v-1l3-2-1-1-1-1h0 1c3-1 7-3 8-6s3-5 4-7l1-2 1-6c0-5-2-10-1-16h0l1-3c1-3 1-6 2-9v-2h-1v1h-1v-2-1-2l-1 1-1-2 1-3h0v-1h1v1c1-1 1-1 1-2l1-1c2 0 2 0 3-1v-1l1 1c0 1 0 1 1 0l1-2v1c1 0 1 0 1 1h0c1 0 2-1 2-2z" class="S"></path><path d="M174 560c1 3 0 6-1 9l-1 2h-1c1-1 1-3 1-5s1-4 2-6z" class="l"></path><path d="M177 565v2-1l1 1c-2 4-3 9-4 13h-1v-5l1-1h0v-1c0-1 0-1 1-2v-1l1-3c0-1 0-1 1-2z" class="B"></path><path d="M169 591h1v-1h1v1c0 1 0 1 1 1v1h0c0 1 0 1-1 2v2l1 1c0 1-1 3-1 5-1-1-2-1-3-1l-1 1h-2l1-2c1-1 1 0 1-1 2-3 2-5 2-9z" class="h"></path><path d="M169 591h1v-1h1v1c0 1 0 1 1 1v1c-1 0-2 1-2 1 0 1 0 2-1 3 0 0 1 0 0 1v1l2 1c-1 1-2 1-2 1l-2-1c2-3 2-5 2-9z" class="D"></path><path d="M171 571h1v4c-1 2 0 10-2 12-1 1-1 3-1 4 0 4 0 6-2 9 0 1 0 0-1 1l1-6c1-2 0-5 2-7v-1l1-1v-2l-1-1c1-4 1-8 2-12z" class="p"></path><path d="M168 577l2-9v-5c1-1 1-2 1-3v-1h1v5l-1 1 1 1c0 2 0 4-1 5-1 4-1 8-2 12l1 1v2l-1 1v1c-2 2-1 5-2 7 0-5-2-10-1-16h0l1-3 1 1z" class="Y"></path><path d="M168 577l2-9v-5c1-1 1-2 1-3v-1h1v5l-1 1-2 15c-1 1 0 5-1 6l-1-1c0-3 1-5 1-8z" class="r"></path><path d="M178 551h4c1 1 0 1 1 1l-3 9-2 6-1-1v1-2-3c1-1 1-1 1-2h-1c0 1 0 1-1 1v-2c-1 0-1 0-2 1-1 2-2 4-2 6l-1-1 1-1v-5h-1v1c0 1 0 2-1 3v5l-2 9-1-1c1-3 1-6 2-9v-2h-1v1h-1v-2-1-2l-1 1-1-2 1-3h0v-1h1v1c1-1 1-1 1-2l1-1c2 0 2 0 3-1v-1l1 1c0 1 0 1 1 0l1-2v1c1 0 1 0 1 1h0c1 0 2-1 2-2z" class="c"></path><path d="M174 555h1v1h0c-1 1-1 1-1 0v-1z" class="B"></path><path d="M165 560l1-3v3s1 0 1 1c1 0 0 0 1 1h0l2-2v2h0v2l-1 1v2-2h-1v1h-1v-2-1-2l-1 1-1-2z" class="h"></path><path d="M180 561l1 1v1s1 1 1 2l-3 9c-1 1-1 4-1 5-1 4-2 8-2 12 0 5 0 10-4 14v2c-1 0-1 0-1 1l-3 3-5 5s-1 1-2 1c-1 1-2 2-2 3-1 1-2 3-3 3v-1h-3c-2 0-2 0-3 1v-1l1-1v-1l3-2-1-1-1-1h0 1c3-1 7-3 8-6s3-5 4-7h2l1-1c1 0 2 0 3 1 0-2 1-4 1-5 2-6 1-12 2-18 1-4 2-9 4-13l2-6z" class="a"></path><path d="M172 605v2c-1 0-1 0-1 1l-3 3-5 5s-1 1-2 1c-1 1-2 2-2 3-1 1-2 3-3 3v-1h-3c7-5 14-10 19-17z" class="S"></path><path d="M165 603h2l1-1c1 0 2 0 3 1-1 2-3 3-5 5-4 3-8 7-12 10l-1-1-1-1h0 1c3-1 7-3 8-6s3-5 4-7z" class="D"></path><path d="M597 493c4 2 7 4 10 5 3 2 7 4 10 7 2 2 3 3 4 5 1 0 1-1 1-1l3 1v1c7 9 15 18 19 29 1 1 1 2 1 3v3c-1-2-2-3-4-4l-7-6c-1-1-2-2-4-2-2-2-3-4-5-7-1 1-1 2-1 3h-1v2 1l-1 1v-2l-1-1v1h0l-1-2v-1h0-1v4 1h-1c0-1-1-1-1-2l-2-5v1h0c-1 0-2-1-2-2s0-2 1-3c0-1-1-2-2-3-1 0-1 0-1-1h-2l-1 1h-1c-1-2-1-4-4-6l-1 1h0c-1 0-1-1-1-1v-1h1 1c-1 0-1-1-1-2h0c-1-1-2-2-3-2-1-1-2 0-3 0h-1v-1h2 1 0c-1-1-2-2-3-2v-1l1-1-2-2c-2-1-4-1-6-2v-1h-2l-1-1h-1-2c0-1 0-1-1-1s-1 0-2-1h0 3s1 0 2 1h0 2l1 1h1v-1c2 1 5 3 8 3h0l-1-1v-1l2 1v-1l-2-2-1-1h1v-1l1 1h3l-2-2z" class="j"></path><path d="M610 506c1 1 1 1 1 2s1 2 2 3c-1 0-2 1-3 1-1-1-1-2-1-3l1-1v-2z" class="f"></path><path d="M604 499h0c2 1 3 1 4 3l2 4v2l-1 1c-2-2-2-4-3-6 0-2-1-3-2-4z" class="n"></path><path d="M620 530c1-1 0-2 1-3v-2h0 1 1v-1h0l1 1v1l1 1c-1 1-1 2-1 3h-1v2 1l-1 1v-2l-1-1v1h0l-1-2z" class="i"></path><path d="M621 531l1-2h1v3h-1l-1-1z" class="P"></path><path d="M617 505c2 2 3 3 4 5 1 0 1-1 1-1l3 1v1s-1 0 0 1c1 2 2 5 2 7v1h-1v2h-1l-2-3v1h-1l-1-1h0c-1-4-1-7-4-10 1-1-1-2-1-3l1-1z" class="O"></path><path d="M617 505c2 2 3 3 4 5 2 2 4 4 4 7h0s0-1-1-1c0-3-2-4-3-6v4 5h0c-1-4-1-7-4-10 1-1-1-2-1-3l1-1z" class="V"></path><path d="M625 527l-1-1v-1c3 3 7 7 10 9-1-3-3-4-3-8 1 2 2 5 4 6h1v-2h0l1 2h-1c1 1 2 1 2 2 2 1 4 4 5 5s1 0 1 1h0c1 1 1 2 1 3v3c-1-2-2-3-4-4l-7-6c-1-1-2-2-4-2-2-2-3-4-5-7z" class="H"></path><path d="M595 506v-1l1-1-2-2c-2-1-4-1-6-2v-1h-2l-1-1h-1-2c0-1 0-1-1-1s-1 0-2-1h0 3s1 0 2 1h0 2l1 1h1 0l1 1h2 0l5 3h0l3 3 1 1h1l3 3c1 0 3 2 3 3l1 1h0c1 2 4 3 5 5l2 5c1 0 1 0 1 1h0l-1 1-1-2c0-1-1-2-2-3-1 0-1 0-1-1h-2l-1 1h-1c-1-2-1-4-4-6l-1 1h0c-1 0-1-1-1-1v-1h1 1c-1 0-1-1-1-2h0c-1-1-2-2-3-2-1-1-2 0-3 0h-1v-1h2 1 0c-1-1-2-2-3-2z" class="D"></path><path d="M597 493c4 2 7 4 10 5 3 2 7 4 10 7l-1 1c0 1 2 2 1 3 3 3 3 6 4 10v2h-1l-1-1c-3-1-4-4-6-6-1 0-1 0-1-1-1-1-1-1-2-1 1 0 2-1 3-1-1-1-2-2-2-3s0-1-1-2l-2-4c-1-2-2-2-4-3h0c-2-1-3-2-5-2h-1c-1 0-2-1-3-1l-1-1h1v-1l1 1h3l-2-2z" class="B"></path><path d="M615 509h-1c0-1-1-2-2-4v-1c1 0 1 0 1 1 1 1 2 3 2 4 1 2 2 3 4 5l-2-4v-1c3 3 3 6 4 10v2h-1l-1-1c-3-1-4-4-6-6-1 0-1 0-1-1-1-1-1-1-2-1 1 0 2-1 3-1-1-1-2-2-2-3 1 0 1 0 2 1h2z" class="Q"></path><path d="M611 508c1 0 1 0 2 1h2l1 4c-1 0-2 0-2-1-1 0-1 0-1-1-1-1-2-2-2-3z" class="I"></path><path d="M131 400l1 4h1c1 0 2 0 2 1l1 1 2-2h0v2c1 1 1 1 2 1 0 0 1 0 1-1h1l1-1h1v1h1c1 0 1 0 2 1l1-1h0l1 1h-1l-1 1h0c1 0 1 0 2-1h0c1 0 1-1 1-1l1-1h1v1h1 1l-1 1c0 1-1 2-1 3-1 0-1 0-1 1h0c-1 1-1 1-2 1v1 2c1 1 1 1 1 2v1c1 1 2 1 2 3h2 0c-1 1-2 1-2 2v1c0 1 0 3-1 4h0v1h-1l-1 2h0c-1 0-1 1-1 1-1 0-2 0-3 1v1c-1 1-2 3-3 5l-3 6c-1 0-1 1-1 2-1 1-1 2-1 3s0 2-1 4c-1-2-2-3-2-5-3-6-6-12-7-18v-6l-3-4v-3h0c0-1 0-1 1-2 0 0 0-1 1-1v-3h0c1-2 2-5 3-8v-1l1-2 1-1z" class="M"></path><path d="M128 412c1 1 0 3 0 5h-1c-1 1-2 1-2 2h0c0 1-1 1-1 2v-3h0c0-1 0-1 1-2 0 0 0-1 1-1l1-1 1-2z" class="N"></path><path d="M127 425h1c1 0 1 1 1 1h2c0 1 0 2 1 3-1 0-1 1-1 2h-1v-1c-1 0-2-1-2 0l-1 1v-6z" class="J"></path><path d="M135 405l1 1c-1 1-1 1-1 3h0c-1 1-1 3-1 4h0v-2h0c-2 2-1 4-2 7h0l-2 2h0c0-2 0-4 1-6l1-4v-1h-1l1-1h1c2-1 1-1 1-2 1 0 1-1 1-1z" class="c"></path><path d="M132 418h0c1-3 0-5 2-7h0v2h0l1 1h1 0c0 3 1 7 0 9v-3l-1-1c0 2 0 3-1 4-1-1 0-1-1-2l-1 1v-1s1-1 1-2-1-1-1-1z" class="Z"></path><path d="M134 413h0l1 1h1 0l-1 1v3c0 1 0 1-1 2h0-1v-4c1-1 1-2 1-3h0z" class="V"></path><path d="M131 400l1 4h1c1 0 2 0 2 1 0 0 0 1-1 1 0 1 1 1-1 2h-1l-1 1h1c-2 2-3 7-4 8 0-2 1-4 0-5l-1 2-1 1v-3h0c1-2 2-5 3-8v-1l1-2 1-1z" class="j"></path><path d="M128 412v-1-2-2c1-1 1-2 1-3l1 1h0v1 3h1 1c-2 2-3 7-4 8 0-2 1-4 0-5z" class="B"></path><path d="M132 429h0c1 0 2 1 2 1h1l1 2 2-1c1 1 1 2 1 2 1 0 2-1 3 0h0l1 1s1-1 2-1v1c-1 1-2 3-3 5l-3 6c-1 0-1 1-1 2-1 1-1 2-1 3s0 2-1 4c-1-2-2-3-2-5-3-6-6-12-7-18l1-1c0-1 1 0 2 0v1h1c0-1 0-2 1-2z" class="b"></path><path d="M132 429h0v2c-1 0-1 1-1 2 0 0-1 0-1 1h0c1 0 2-2 3-1-1 2-2 2-2 3v2s1 0 1 1 1 2 1 3v1c1-1 1-1 1-2h0v3 3 1 1c-3-6-6-12-7-18l1-1c0-1 1 0 2 0v1h1c0-1 0-2 1-2z" class="a"></path><path d="M145 433v1c-1 1-2 3-3 5l-3 6c-1 0-1 1-1 2-1 1-1 2-1 3s0 2-1 4c-1-2-2-3-2-5v-1-1c1-1 1-1 1-2l1-1v-3l1-1c1-2 2-2 2-3 1-2 2-3 3-4l1 1s1-1 2-1z" class="L"></path><path d="M137 441l1 1h0v1h0l1-1s1-1 1-3h1 1l-3 6c-1 0-1 1-1 2-1 1-1 2-1 3-1 0-1-1-1-2 1-2 0-5 1-7z" class="B"></path><path d="M145 433v1c-1 1-2 3-3 5h-1-1c0 2-1 3-1 3l-1 1h0v-1h0l-1-1c2-2 3-5 6-7 0 0 1-1 2-1z" class="E"></path><path d="M138 404h0v2c1 1 1 1 2 1 0 0 1 0 1-1h1l1-1h1v1h1c1 0 1 0 2 1l1-1h0l1 1h-1l-1 1h0c1 0 1 0 2-1h0c1 0 1-1 1-1l1-1h1v1h1 1l-1 1c0 1-1 2-1 3-1 0-1 0-1 1h0c-1 1-1 1-2 1v1 2c1 1 1 1 1 2v1c1 1 2 1 2 3h2 0c-1 1-2 1-2 2v1c0 1 0 3-1 4h0v1h-1v-1-1h-1l-1 2h-3v-1h-1l-1 1v-1c0-1 0-2-1-3h0c0 1 0 2-1 3h0l-1-8c0 1-1 3-1 5v2h-1v-2c-1-1 0-4 0-5v-2c-1 1-1 3-1 5h0v3h-1c0-1 1-1 0-2v-1c1-2 0-6 0-9h0-1l-1-1c0-1 0-3 1-4h0c0-2 0-2 1-3l2-2z" class="e"></path><path d="M147 417v-1l1-1v3h1 1c1 1 2 1 2 3h2 0c-1 1-2 1-2 2v1c0 1 0 3-1 4h0v1h-1v-1-1h-1l-1 2h-3v-1h-1l-1 1v-1c0-1 0-2-1-3h0c1-1 1-2 1-3h1 0l1 1v-1-1h1v-1c0-1 0-2 1-3z" class="X"></path><path d="M147 417v-1l1-1v3h1l-1 4c0-1 0-3-1-5h0z" class="B"></path><path d="M149 418c1 1 1 2 1 3 1 0 1 1 1 2-1 0-1 1-2 1 0 0 0-1-1-2l1-4z" class="Q"></path><path d="M146 421l1 4-1 1c-1-1-1-1-1-2l-1 1v3l-1 1v-1c0-1 0-2-1-3h0c1-1 1-2 1-3h1 0l1 1v-1-1h1z" class="j"></path><path d="M149 418h1c1 1 2 1 2 3h2 0c-1 1-2 1-2 2v1c0 1 0 3-1 4h-1c1-2 1-4 1-5s0-2-1-2c0-1 0-2-1-3z" class="C"></path><path d="M526 479c0-1-1-1-1-2 1 0 1 0 2 1 2 1 4 1 6 2h1l3 1c5 0 11 0 16 1 4 0 8 1 13 2 4 0 10 1 15 3l12 4 4 2h0l2 2h-3l-1-1v1h-1l1 1 2 2v1l-2-1v1l1 1h0c-3 0-6-2-8-3v1h-1l-1-1h-2 0c-1-1-2-1-2-1h-3 0c1 1 1 1 2 1s1 0 1 1h2 1l1 1h2v1c2 1 4 1 6 2l2 2-1 1v1l-21-7-1 1c-1 0-2-1-3-1-3 0-5-1-7-2-2 0-3-1-5-1v1h-1l-5-2c0 1 0 1-1 2h0c-9-3-19-6-25-15 1-1 0-2 0-3z" class="c"></path><path d="M564 490c1 0 1 0 1-1l2 2v1h1c-2 0-3 0-4-1v-1z" class="x"></path><path d="M564 490h-1l-3-3c2 0 3 1 4 1h2 0 1s1 1 2 1l-1 1c-1-1-1-1-2-1h-1c0 1 0 1-1 1z" class="l"></path><path d="M537 487l1-1h1c3 0 7 0 10 1v1l-1 1-7-2h-4z" class="o"></path><path d="M549 487l1 1v-1c-2-1-4-2-7-4 5 1 10 4 13 7l-8-1 1-1v-1z" class="l"></path><path d="M563 496h1 0v-1h0 1l9 4-1 1c-1 0-2-1-3-1-3 0-5-1-7-2h0v-1z" class="X"></path><path d="M552 493l3 1 1-1 2 1h1c2 0 2 1 4 2v1h0c-2 0-3-1-5-1v1h-1l-5-2-3-1c1 0 2-1 3-1zm15-5c-2-1-6-2-7-3 1-1 2-1 4 0 1 0 4 1 6 3h0c0 1 1 2 1 2 1 1 1 2 2 3-2 1-4 0-5-1h-1v-1l-2-2h1c1 0 1 0 2 1l1-1c-1 0-2-1-2-1z" class="Q"></path><path d="M526 479c0-1-1-1-1-2 1 0 1 0 2 1 2 1 4 1 6 2h1l3 1c-2 0-3-1-4 1 0 0 0 1-1 1 3 1 5 2 7 3h-1l-1 1h4c0 1 0 2 1 3h2-5l-3-1h-1c-3-2-8-7-9-10z" class="f"></path><path d="M537 487h4c0 1 0 2 1 3h2-5l-3-1c1-1 3 0 4-1l-3-1h0z" class="K"></path><path d="M527 478c2 1 4 1 6 2h1l3 1c-2 0-3-1-4 1 0 0 0 1-1 1s-4-3-5-5h0z" class="I"></path><path d="M526 479c1 3 6 8 9 10h1l3 1h5c1 1 3 1 4 2l4 1c-1 0-2 1-3 1l3 1c0 1 0 1-1 2h0c-9-3-19-6-25-15 1-1 0-2 0-3z" class="T"></path><path d="M548 492l4 1c-1 0-2 1-3 1l-6-2h5z" class="l"></path><path d="M539 490h5c1 1 3 1 4 2h-5l-4-2z" class="Z"></path><path d="M581 487l12 4 4 2h0l2 2h-3l-1-1v1h-1l1 1 2 2v1l-2-1v1l1 1h0c-3 0-6-2-8-3-1 0-6-1-7-2l1-1h0l-2-1c-1-1-2-1-3-1v-1h2v-1h-1-1c-2 0-3-2-5-3l10 3-1-2h0 0v-1z" class="X"></path><path d="M580 493v-1c5 0 11 3 15 6v1h0l1 1h0c-3 0-6-2-8-3-1 0-6-1-7-2l1-1h0l-2-1z" class="s"></path><path d="M581 487l12 4 4 2h0l2 2h-3l-1-1v1h-1c-4-2-8-4-12-5l-1-2h0 0v-1z" class="j"></path><path d="M144 342h0c1 0 1 0 2 1h1l-1 1h1 0 2 0v1c3-1 6-1 9-1 1 0 1 0 2-1l1-1v1 1h2 0 1c0 1 1 1 2 2v1h1v3c-2-1-5 0-7 1 1 1 1 1 2 1s1 1 2 1l-1 1v1l-8 5c-1 1-3 2-4 3-1 0-1 1-2 1h0c0 1-1 2-2 3l-2 2-2-1-4 4c-2 1-4 3-6 5-1 1-3 2-3 3-1 1 0 1-1 2v-1c-2 1-3 2-4 3s-2 2-2 3c1 1 2 1 3 2l1 1-2 6c-1 1-1 3-1 5-1 1-1 2 0 3h0c1 0 0-1 1-2h0 1 1s0-1 1-1l1-1 1 1-1 2v1l-3 8h0v3c-1 0-1 1-1 1-1 1-1 1-1 2h0c-1 1-1 1-1 2l-1-1v-4h0c-3-9-3-18-1-28-2 3-4 5-6 8-3 3-6 8-9 11 3-5 6-10 9-14 2-2 4-4 6-7h0 1c0-1 0-1 1-1h0c1-2 1-4 1-6v-1c0-1 0-3 1-4h1l1-3h1 0 0 1c0-2 0-2 1-3l1-1v-1h-1v-1c-1 0-1 1-2 0-1-2 0-4 1-7h0 0l1-4v-1l3-1 2-1-1-1c2 0 5-1 7-2h2c-1-1-1-2-1-3h1l1-2z" class="d"></path><path d="M140 367l2-1 1 1c-1 1-3 2-4 3h-1l-1-1c1 0 1 0 1-1 1 0 2 0 2-1z" class="C"></path><path d="M149 364c0 1-1 2-2 3l-2 2-2-1c2-1 4-3 6-4z" class="Y"></path><path d="M158 352l2-1c1 1 1 1 2 1s1 1 2 1l-1 1-4 2 1-2-1-2h-1z" class="D"></path><path d="M123 405c1 2 1 3 1 4l1 3h0v1h0-1-1v-2c-1-1-2-1-2-2 1-1 1-3 2-4z" class="T"></path><path d="M123 387c1 1 2 1 3 2-2 3-2 7-4 9 0-4 0-7 1-11z" class="D"></path><path d="M125 412l1-2h0v2h0v3c-1 0-1 1-1 1-1 1-1 1-1 2h0c-1 1-1 1-1 2l-1-1v-4c0-1 0-1 1-2h1 1 0v-1z" class="M"></path><path d="M137 367c1-1 1-2 2-2l1-1c0-1 1-1 1-2h0 1v1h0v1h2c1 0 2-1 3-2h0l2 1-6 4-1-1-2 1h-3z" class="D"></path><path d="M125 373h1l1-3h1 0v7-2h-1v6h-2v1h-1v-4-1c0-1 0-3 1-4z" class="b"></path><path d="M126 389l1 1-2 6c-1 1-1 3-1 5-1 1-1 2 0 3h0c1 0 0-1 1-2h0 1 1s0-1 1-1l1-1 1 1-1 2v1l-3 8h0 0v-2h0l-1 2h0l-1-3c0-1 0-2-1-4 0-1 1-4-1-5v-2c2-2 2-6 4-9z" class="Q"></path><path d="M126 402h1s0-1 1-1l1-1 1 1-1 2v1l-1-1h0c-1 2-1 5-3 6 0-1 1-3 1-5-1 0-1-1-1-2h1z" class="e"></path><path d="M158 352h1l1 2-1 2-10 7-2-1h0c-1 1-2 2-3 2h-2v-1h0v-1c-1-1-1-2-1-3l17-7z" class="j"></path><path d="M147 362l2-1v-1-1h1 2l4-2h0l2-2 1-1h1l-1 2-10 7-2-1z" class="P"></path><path d="M131 365c3-3 7-4 10-6 0 1 0 2 1 3h-1 0c0 1-1 1-1 2l-1 1c-1 0-1 1-2 2 0 1 0 2-1 3 0 1 1 1 1 2h0-2-1v1l1 1-6 4h-1v-1-7h0 1c0-2 0-2 1-3l1-1v-1h0z" class="j"></path><path d="M131 365c3-3 7-4 10-6 0 1 0 2 1 3h-1 0c0 1-1 1-1 2l-1 1c-1 0-1 1-2 2 0 1 0 2-1 3 0 1 1 1 1 2h0-2-1c0-2 0-2 1-4l1-1v-2c1 0 1-1 1-1l-1-1-2 2c-1 1-1 1-2 1 0-1-1-1-1-1z" class="V"></path><path d="M163 344h1c0 1 1 1 2 2v1h1v3c-2-1-5 0-7 1l-2 1-17 7c-3 2-7 3-10 6h0-1v-1c1-1 3-2 4-3l3-1 1-1 3-1c0-1 0-2 1-3h0c1-1 1-2 1-3l1-1c1 0 2 0 3 1h1c2 0 2 0 3-1s1-1 2-1 1 0 2-1c2-1 2-1 4-1 1 0 1-1 2-2h0v-1l2-1h0z" class="U"></path><path d="M161 346h0l4 3h-5l-1-1c1 0 1-1 2-2z" class="D"></path><path d="M143 352l1-1c1 0 2 0 3 1h1c0 1 0 1 1 2-3 1-6 3-8 4 0-1 0-2 1-3h0c1-1 1-2 1-3z" class="C"></path><path d="M155 349c2-1 2-1 4-1l1 1c-3 2-8 3-11 5-1-1-1-1-1-2 2 0 2 0 3-1s1-1 2-1 1 0 2-1z" class="G"></path><path d="M144 342h0c1 0 1 0 2 1h1l-1 1h1 0 2 0v1c3-1 6-1 9-1 1 0 1 0 2-1l1-1v1 1h2l-2 1v1h0c-1 1-1 2-2 2-2 0-2 0-4 1-1 1-1 1-2 1s-1 0-2 1-1 1-3 1h-1c-1-1-2-1-3-1l-1 1h-1c-1-1-2-2-2-3-2 0-3 0-5 1l-1-1c2 0 5-1 7-2h2c-1-1-1-2-1-3h1l1-2z" class="H"></path><path d="M155 345c1 0 3 0 5 1h1c-1 1-1 2-2 2-2 0-2 0-4 1l-1-1v-1c1-1 1-1 1-2z" class="V"></path><path d="M155 345c1 0 3 0 5 1-1 0-2 0-2 1h-2-2c1-1 1-1 1-2z" class="B"></path><path d="M148 347l7-2c0 1 0 1-1 2v1l1 1c-1 1-1 1-2 1s-1 0-2 1v-3c-1-1-1-1-3-1zm-4-5h0c1 0 1 0 2 1h1l-1 1h1 0 2 0v1c-1 0-5 1-6 2-1-1-1-2-1-3h1l1-2z" class="D"></path><path d="M140 349l8-2c2 0 2 0 3 1v3c-1 1-1 1-3 1h-1c-1-1-2-1-3-1l-1 1h-1c-1-1-2-2-2-3z" class="V"></path><path d="M135 350c2-1 3-1 5-1 0 1 1 2 2 3h1c0 1 0 2-1 3h0c-1 1-1 2-1 3l-3 1-1 1-3 1c-1 1-3 2-4 3-1 0-1 1-2 0-1-2 0-4 1-7h0 0l1-4v-1l3-1 2-1z" class="R"></path><path d="M137 360l-1-1c1-1 1-2 1-3 0-2-1-2-2-4h1c1 1 2 1 2 2v5l-1 1z" class="o"></path><path d="M130 353v-1l3-1s0 1-1 2v1h1v2c-1 1-2 3-2 4 1-1 2-3 2-5 1-1 1-1 3-1v1l-1 1c-1 1-1 2-1 3h1v1c-1 0-1 1-1 1-1 1-3 2-4 3-1 0-1 1-2 0-1-2 0-4 1-7h0 0l1-4z" class="t"></path><path d="M130 353v-1l3-1s0 1-1 2v1h-1v1c0 1-1 2-2 3h0v-1l1-4z" class="h"></path><path d="M558 497v-1c2 0 3 1 5 1 2 1 4 2 7 2 1 0 2 1 3 1l1-1 21 7c1 0 2 1 3 2h0-1-2v1h1c1 0 2-1 3 0 1 0 2 1 3 2h0c0 1 0 2 1 2h-1-1v1s0 1 1 1h0l1-1c3 2 3 4 4 6h1l1-1h2c0 1 0 1 1 1 1 1 2 2 2 3-1 1-1 2-1 3s1 2 2 2h0v-1l2 5c0 1 1 1 1 2h1v-1-4h1 0v1l1 2h0l1 6 4 11h0c1-1 1-2 0-3 1 1 2 2 2 4v4 3c0 2 0 4 1 5h-1c-1 1 0 1-1 2v2-1h-1c0 2 0 3-1 5 0 1 0 1 1 2-1 3-2 6-4 8h1 1c0 1 0 1 1 2h1l-1 1-1 2c-1 2-1 3-2 5l1 1 2 2c0 1-3 3-3 4 0 2-2 4-4 5l1 1v-1 1 2h-1c-4 5-9 9-14 13-1 1-3 1-4 2-2 2-4 3-6 4 0-1-1-1-1-2 8-4 15-11 19-19 8-16 8-35 2-51-7-19-20-35-37-44-7-4-14-6-20-9v-1h0 0l-2-1h2 0 1z" class="a"></path><path d="M575 504h3 0l1 1c2 2 4 2 6 4h-1c-3-1-6-3-9-5z" class="g"></path><path d="M623 572l-1 5v3h1l-3 5c-1-3 0-7 0-11v3c1-1 1-2 1-2l2-3z" class="J"></path><path d="M622 597c0 2-2 4-4 5l1 1v-1 1 2h-1c-1 0-2 1-2 2l-7 5 6-8 6-6 1-1z" class="B"></path><path d="M558 497v-1c2 0 3 1 5 1 2 1 4 2 7 2 1 0 2 1 3 1 4 2 8 4 12 5h0l-1 1-6-2h0-3c-5-2-11-6-17-7z" class="I"></path><path d="M574 499l21 7c1 0 2 1 3 2h0-1-2v1c-3 0-6 0-9-1l-1 1c-2-2-4-2-6-4l-1-1 6 2 1-1h0c-4-1-8-3-12-5l1-1z" class="o"></path><path d="M578 504l6 2 1-1h0l12 3h-2v1c-3 0-6 0-9-1l-1 1c-2-2-4-2-6-4l-1-1z" class="l"></path><path d="M578 504l6 2h1c1 1 3 1 4 2-1 1-2 0-3 0l-1 1c-2-2-4-2-6-4l-1-1z" class="p"></path><path d="M586 508c3 1 6 1 9 1h1c1 0 2-1 3 0 1 0 2 1 3 2h0c0 1 0 2 1 2h-1-1v-1l-1 1h0v2l-2-2-1-2v1 1h-1l-1-1-1 1h1c0 1 1 2 1 3-1 0-2 1-2 1l-10-8h1l1-1z" class="j"></path><defs><linearGradient id="G" x1="617.195" y1="584.105" x2="623.374" y2="592.709" xlink:href="#B"><stop offset="0" stop-color="#110f12"></stop><stop offset="1" stop-color="#2f302d"></stop></linearGradient></defs><path fill="url(#G)" d="M623 580h1c0 1 0 1 1 2h1l-1 1-1 2c-1 2-1 3-2 5l1 1 2 2c0 1-3 3-3 4l-1 1-6 6h-1c-1 0-1 0-1-1l1-1c3-5 4-11 6-17l3-5z"></path><path d="M622 590l1 1 2 2c0 1-3 3-3 4l-1 1-6 6h-1c-1 0-1 0-1-1l1-1h0c4-3 6-7 8-12z" class="L"></path><path d="M623 591l2 2c0 1-3 3-3 4l-1 1c0-3 1-4 2-7z" class="O"></path><path d="M597 513v-1-1l1 2 2 2v-2h0l1-1v1 1s0 1 1 1h0c0 1 0 2 1 3h0c2 1 4 3 4 4v1c2 3 4 7 5 10l-1 1c0 1 0 1-1 1 1 3 4 8 3 10-1-5-3-9-6-13l-9-10-4-5s1-1 2-1c0-1-1-2-1-3h-1l1-1 1 1h1z" class="C"></path><path d="M607 523c2 3 4 7 5 10l-1 1c0 1 0 1-1 1h0c0-3-1-6-2-9-1-1-1-2-1-3z" class="b"></path><path d="M594 517s1-1 2-1c0-1-1-2-1-3h-1l1-1 1 1h1 0c1 2 2 3 3 5l2 3 1 1c1 1 2 4 3 6h0v1 1c1 1 1 2 1 2l-9-10-4-5z" class="E"></path><path d="M594 517s1-1 2-1c0-1-1-2-1-3h-1l1-1 1 1 1 1v1l1 1c0 1 1 1 1 2l2 3h-1-1c-1 0-1 0-1 1l-4-5z" class="D"></path><path d="M602 515l1-1c3 2 3 4 4 6h1l1-1h2c0 1 0 1 1 1 1 1 2 2 2 3-1 1-1 2-1 3s1 2 2 2h0v-1l2 5c0 1 1 1 1 2h1v-1-4h1 0v1l1 2h0l1 6 4 11h0c1-1 1-2 0-3 1 1 2 2 2 4v4 3c0 2 0 4 1 5h-1c-1 1 0 1-1 2v2-1h-1c0 2 0 3-1 5 0 1 0 1 1 2-1 3-2 6-4 8v-3l1-5-2 3s0 1-1 2v-3h0c0-6-1-16-4-22-1-2-2-5-3-7 1-2-2-7-3-10 1 0 1 0 1-1l1-1c-1-3-3-7-5-10v-1c0-1-2-3-4-4h0c-1-1-1-2-1-3z" class="R"></path><path d="M610 523h2l1 1c0 1-1 1-1 2 0-1-1-2-2-3z" class="B"></path><path d="M621 560h1v10s0 2 1 2l-2 3v-15z" class="O"></path><path d="M623 552h1v1 4 2l-1 1h-1-1v-1c0-1 0-2 1-3 0-2 1-3 1-4z" class="b"></path><path d="M622 538l4 11v4c1 2 1 3 1 4h-1-2v-4-1h-1v-1h1 0l-2-7v-1h1s0-1-1-2v-3z" class="B"></path><path d="M626 553c1 2 1 3 1 4h-1-2v-4l1 2 1-1v-1z" class="r"></path><path d="M623 560l1-1v6c1 2 0 3 1 5 0 1 0 1 1 2-1 3-2 6-4 8v-3l1-5c-1 0-1-2-1-2v-10h1z" class="X"></path><path d="M623 560l1-1v6 3h-1v-8z" class="L"></path><path d="M624 565c1 2 0 3 1 5 0 1 0 1 1 2-1 3-2 6-4 8v-3l2-1c0-2 0-3-1-4v-4h1v-3z" class="Q"></path><path d="M623 568h1c0 1 0 3-1 4v-4z" class="J"></path><path d="M626 546c1 1 2 2 2 4v4 3c0 2 0 4 1 5h-1c-1 1 0 1-1 2v2-1h-1c0 2 0 3-1 5-1-2 0-3-1-5v-6-2h2 1c0-1 0-2-1-4v-4h0c1-1 1-2 0-3z" class="Q"></path><path d="M627 558h1v2l-1 1-1-1v-1l1-1z" class="q"></path><path d="M624 557h2c-1 1-1 1-1 2 0 2 1 3 1 5v1c0 2 0 3-1 5-1-2 0-3-1-5v-6-2z" class="t"></path><path d="M602 515l1-1c3 2 3 4 4 6l1 2h1c1 0 1 0 1 1 1 1 2 2 2 3l3 6c1 1 1 2 2 3 2 3 4 9 3 13l-2 7-1-4-1 1c-1-2-2-5-3-7 1-2-2-7-3-10 1 0 1 0 1-1l1-1c-1-3-3-7-5-10v-1c0-1-2-3-4-4h0c-1-1-1-2-1-3z" class="n"></path><path d="M602 515l1-1c3 2 3 4 4 6l1 2h1c2 4 3 7 5 11 0 1 1 1 1 2 1 1 1 2 1 3 1 3 2 5 3 9h-1l-1-4c0-1-2-5-3-7v1 2 2h0v-1c-1-2-1-5-2-7-1-3-3-7-5-10v-1c0-1-2-3-4-4h0c-1-1-1-2-1-3z" class="E"></path><path d="M612 533c1 2 1 5 2 7v1h0v-2-2-1c1 2 3 6 3 7l1 4v4h-1l-1 1c-1-2-2-5-3-7 1-2-2-7-3-10 1 0 1 0 1-1l1-1z" class="L"></path><path d="M617 543l1 4v4h-1l-1-1v-1c-1-1-1-2 0-2v-2-1l1-1z" class="N"></path><path d="M255 236l1-1h2l1 1h1 3 3 0v1h2 2c1 0 3 0 5 1h-2v1c1 1 3 1 5 2-2 0-6-1-7 0h0c0 1-1 1-1 1v1l-2 1h1l1 1 1 1s1 0 1 1l1 1c0 1 0 1 1 1v1c1 1 2 1 2 1l1 1v1l-1 1c1 1 1 2 1 3h0c1 1 2 1 2 2h0v1c1 0 1 1 1 2h0v1h0c0 1 0 1 1 1v5c0 1 0 2-1 3 1 1 0 1 0 2-1 1-1 1-2 1s-1 1-2 1-1 1-1 1h0c1 0 3-1 4-1v1s-1 0-2 1c1 1 2-1 3 1-2 1-3 1-5 2-1 0-3 1-4 1l-3 1c0 1-1 1-1 1-1 0-2 1-3 1l-1-1-1-1-1 1-3 2c0-3-3-2-5-4h-1s-1 0-1-1h-1 0c-1 0-1-1-2-1v1h-1c-1-1-3 0-4 0s-1-1-2-2v-1l1-1v-3c1-1 0-1 0-1 0-1 2-4 2-5h-2c-1 1-1 2-2 3l-2 1v-2h0v-1l1-6v-6-3l-1 1v-1l-1 1h0l-2-2h1v-1-3-1c3-1 1-3 2-5l1-1v1 2h1v-1l2-1v-1c1 0 1-1 1 0 1 0 1 1 1 2h0v2c0-1 0-1 1-2s1-1 2-3l2 2c0-1 0-1 1-1 0 0 1-1 1-2 1-2 2-3 4-4z" class="C"></path><path d="M257 254l1-1v1 1h-1-1v-1h1z" class="B"></path><path d="M254 258l-1-1c1 0 1 0 1-1h2v2h-1-1z" class="e"></path><path d="M262 255l2-1h1l-3 3-1 1h-1v1l-1-1c1-1 2-2 2-3h1zm-6 3h1v1h2l-2 2h0 1 1v1l-1 1v-1l-2 1h-1v-1s0-1-1-1l1-1v-2h1z" class="E"></path><path d="M256 258h1v1c-1 0-1 1-2 1v-2h1z" class="K"></path><path d="M259 261c0-1 1-1 1-2 1 0 1 0 2-1h2 0 1c0 1 0 1-1 1 0 1-1 1-1 2s-1 1-2 2c2 1 1 2 2 3l-2 2h-1v-2c-1 0-2 0-3 1h-1l1-1h1v-3l1-1v-1z" class="V"></path><path d="M261 263c2 1 1 2 2 3l-2 2h-1v-2h1v-1l-1 1-1-1c1-1 2-1 2-2z" class="F"></path><path d="M250 266c1-2 2-2 2-3 1-1 1-2 2-2s1 1 1 1v1h1l2-1v1 3h-1l-1 1c-1 1-1 1-2 1 0 0-1 0-2 1-1-1-1-2-3-2h0c0-1 1-1 1-1z" class="G"></path><path d="M249 267h0c0-1 1-1 1-1h1 2c0 1 0 1 1 2 0 0-1 0-2 1-1-1-1-2-3-2z" class="D"></path><path d="M255 263h1l2-1v1 3h-1l-1 1c-1 0-1-1-2-1v-1-2h1z" class="h"></path><path d="M254 265h3v1l-1 1c-1 0-1-1-2-1v-1z" class="Z"></path><path d="M251 258l1-1c1 0 1 1 2 1h1v2l-1 1c-1 0-1 1-2 2 0 1-1 1-2 3 0 0-1 0-1 1h0c0 2 0 2-2 2h-1c0-2 1-3 2-5 0-1 1-3 2-4l1-2z" class="B"></path><path d="M248 264c0-1 1-3 2-4v1s0 1 1 1c-1 1-2 2-3 2z" class="D"></path><path d="M251 258l1-1c1 0 1 1 2 1h1v2c-1-1-1-1-3 0 0-1-1-1-1-2z" class="F"></path><path d="M269 244l1 1 1 1s1 0 1 1l1 1c0 1 0 1 1 1v1c1 1 2 1 2 1l1 1v1l-1 1c1 1 1 2 1 3h0 0c-1 0-2 0-3-1-3 1-4 2-7 3 0 1-1 1-1 2h0c0-2 1-3 0-4h0v-1l1-1h0c-2 0-3 1-4 2h-1l3-3h-1l-2 1h-1c1-1 1-1 1-2h-1s-1 0-1-1c1-1 1-1 3-2 1-2 3-2 5-3h0l-1-1h0-1v-1l3-1z" class="X"></path><path d="M266 257h2c2-1 2-2 3-3h0c1-1 1-1 2-1h2l-1-1h0l2 1h1l-1 1c1 1 1 2 1 3h0 0c-1 0-2 0-3-1-3 1-4 2-7 3 0 1-1 1-1 2h0c0-2 1-3 0-4z" class="i"></path><path d="M276 253h1l-1 1c1 1 1 2 1 3h0 0c-1 0-2 0-3-1h-1 0v-1l1-1s1 0 2-1h0z" class="E"></path><path d="M269 244l1 1 1 1s1 0 1 1l1 1c0 1 0 1 1 1v1l1 1h-1 0c-2 0-6 1-8 2 0-1-1-1-1-1 0-1 1-1 1-2h-3c1-2 3-2 5-3h0l-1-1h0-1v-1l3-1z" class="O"></path><path d="M269 244l1 1 1 1s1 0 1 1l1 1c0 1 0 1 1 1-1 1-2 1-3 1 0 0-1-2-2-3h-1 0l-1-1h0-1v-1l3-1z" class="b"></path><path d="M269 244l1 1 1 1h-4-1v-1l3-1z" class="L"></path><path d="M258 248c1-1 2-1 3-1-1-1 0-1-1-1h0v-1h1v-1c-1 0-1 1-2 1v-1c0-1 1-1 2-1 0-1 1-1 1-1v-1c-1 0-2 1-3 1h-1c1 0 1 0 1-1 4-2 10-3 14-2 1 1 3 1 5 2-2 0-6-1-7 0h0c0 1-1 1-1 1v1l-2 1h1l-3 1v1h1 0l1 1h0c-2 1-4 1-5 3-2 1-2 1-3 2 0 1 1 1 1 1h-1c-1 1 0 1-1 0v-1c1-1 0-1-1-2-2 2-4 4-6 7l-1 1-1 2c-1 1-2 3-2 4-1 2-2 3-2 5-1 2-2 3-2 5v1h-1v4c-1 1-1 1 0 2-1 0-1-1-2-2v-1l1-1v-3c1-1 0-1 0-1 0-1 2-4 2-5l1-2h0c2-3 2-6 4-8l1-1c1-1 2-3 3-4 1 0 1 0 1-1l4-4z" class="O"></path><path d="M264 247h-2l1-2h3v1h1 0c-1 1-2 1-3 1z" class="J"></path><path d="M268 244h-1-1s-1-1-2-1h0v-1c1 0 1 0 2-1h0l4 1v1l-2 1z" class="L"></path><path d="M267 246l1 1h0c-2 1-4 1-5 3-2 1-2 1-3 2 0 1 1 1 1 1h-1c-1 1 0 1-1 0v-1c1-1 0-1-1-2 2-2 3-2 6-3 1 0 2 0 3-1z" class="h"></path><path d="M255 236l1-1h2l1 1h1 3 3 0v1h2 2c1 0 3 0 5 1h-2v1c-4-1-10 0-14 2 0 1 0 1-1 1h1c1 0 2-1 3-1v1s-1 0-1 1c-1 0-2 0-2 1v1c1 0 1-1 2-1v1h-1v1h0c1 0 0 0 1 1-1 0-2 0-3 1l-4 4c0 1 0 1-1 1v-3c1-1 1 0 1-2v-1-1c-2 1-3 2-4 3h0v-3h0c1-2 3-4 3-5h-1v1l-4 4-1-1c1 0 2-1 2-2h0c0-1 0-1 1-1 0 0 1-1 1-2 1-2 2-3 4-4z" class="k"></path><path d="M258 241h-1-1l-1 1v-1c0-1 1-1 0-3 1-1 2-1 3-2 0 1 0 2-1 3h0l1 2z" class="p"></path><path d="M259 236h1 3-1l-3 3v1l-1 1-1-2h0c1-1 1-2 1-3h1z" class="Q"></path><path d="M263 236h3 0v1h2c-3 1-6 1-9 3v-1l3-3h1z" class="J"></path><path d="M254 248h1v-2h1 1l-1 1v1h2l-4 4c0 1 0 1-1 1v-3c1-1 1 0 1-2z" class="G"></path><path d="M250 246h0c2-1 2-3 5-4h0v1l-1 1h1l2-1v1c-1 1-2 1-3 3h0v-1c-2 1-3 2-4 3h0v-3h0z" class="F"></path><path d="M249 243h0c0 1-1 2-2 2l1 1 4-4v-1h1c0 1-2 3-3 5h0v3h0c1-1 2-2 4-3v1 1c0 2 0 1-1 2v3c-1 1-2 3-3 4l-1 1c-2 2-2 5-4 8h0l-1 2h-2c-1 1-1 2-2 3l-2 1v-2h0v-1l1-6v-6-3l-1 1v-1l-1 1h0l-2-2h1v-1-3-1c3-1 1-3 2-5l1-1v1 2h1v-1l2-1v-1c1 0 1-1 1 0 1 0 1 1 1 2h0v2c0-1 0-1 1-2s1-1 2-3l2 2z" class="R"></path><path d="M250 249h-1c-1 1-1 1-2 1 0-2 2-3 3-4v3h0z" class="C"></path><path d="M250 257v-2c1-1 1-2 2-2h1c-1 1-2 3-3 4z" class="F"></path><path d="M242 261c0 2 0 3-1 5l-1 1h-1c-1 1-1 1-1 2l1-6v2h1c0-2 1-3 2-4z" class="G"></path><path d="M238 270h1c1-1 1-2 2-2s1-1 2-2l1 1 1-1-1 2h-2c-1 1-1 2-2 3l-2 1v-2z" class="K"></path><path d="M245 266c0-1 0-2-1-3h0v-1c1 0 1-1 2-1v-1h-2v-1c1-1 1-2 1-4l-1-2c1-1 1-1 2-1 1 1 0 1 0 2 0 0 1 1 1 2l2 2h0c-2 2-2 5-4 8z" class="S"></path><path d="M240 244l2-1v-1c1 0 1-1 1 0 1 0 1 1 1 2h0v2h-1c0 1 0 1-1 1 0 1 0 2 1 2 0 1-1 1-1 2v3h0v3 1 1 2c-1 1-2 2-2 4h-1v-2-6-3l-1 1v-1l-1 1h0l-2-2h1v-1-3-1c3-1 1-3 2-5l1-1v1 2h1v-1z" class="B"></path><path d="M239 242v1 2h1c0 3 0 9-1 12v-3l-1 1v-1h0c1-4 0-8 0-11l1-1z" class="b"></path><path d="M236 249v-1c3-1 1-3 2-5 0 3 1 7 0 11h0l-1 1h0l-2-2h1v-1-3z" class="F"></path><path d="M274 256c1 1 2 1 3 1h0c1 1 2 1 2 2h0v1c1 0 1 1 1 2h0v1h0c0 1 0 1 1 1v5c0 1 0 2-1 3 1 1 0 1 0 2-1 1-1 1-2 1s-1 1-2 1-1 1-1 1h0c1 0 3-1 4-1v1s-1 0-2 1c1 1 2-1 3 1-2 1-3 1-5 2-1 0-3 1-4 1l-3 1c0 1-1 1-1 1-1 0-2 1-3 1l-1-1-1-1-1 1-3 2c0-3-3-2-5-4h-1s-1 0-1-1h-1 0c-1 0-1-1-2-1v1h-1c-1-1-3 0-4 0-1-1-1-1 0-2v-4h1v-1c0-2 1-3 2-5h1c2 0 2 0 2-2 2 0 2 1 3 2 1-1 2-1 2-1 1 0 1 0 2-1h1c1-1 2-1 3-1v2h1l2-2h0v-1l1-2h0-1v-1c1 0 2-1 3-1h0c0-1 1-1 1-2 3-1 4-2 7-3z" class="R"></path><path d="M274 270h-3-1c1-1 3-1 4-1v1z" class="E"></path><path d="M260 277h0l2-2 1 2-1 1c0-1-1-1-2-1z" class="D"></path><path d="M264 263h-1v-1c1 0 2-1 3-1h0v1h3v1l-4 1-1-1z" class="F"></path><path d="M263 284c2-1 5-2 7-2h1l-3 1c0 1-1 1-1 1-1 0-2 1-3 1l-1-1z" class="I"></path><path d="M266 274h3 0v1l-1 1-2 2c0-1 0 0-1-1h0c0-1 1-1 2-2l-1-1z" class="B"></path><path d="M274 268c3 0 3-1 6-1v1c-1 0-2 1-3 1s-2 0-3 1v-1-1z" class="I"></path><path d="M265 267l1-1c1 1 1 1 1 2h1v1c-1 1-3 1-4 2l1 1-2 1-1-1c1-2 3-2 4-4l-1-1z" class="B"></path><path d="M265 267l1 1c-1 2-3 2-4 4l1 1 2-1h1v1c-1 0-2 1-3 1-2 0-2 0-3-1 2-2 3-4 5-6z" class="D"></path><path d="M276 276v-1c1-1 2-1 3-1v-1h0-2-2 0c1-1 3-1 4-2-1 0-1 0-1-1 1 0 2-1 3-1 0 1 0 2-1 3 1 1 0 1 0 2-1 1-1 1-2 1s-1 1-2 1z" class="B"></path><path d="M274 268h1v-1h-3l1-1c-1-1-2-1-3 0h-2v-1c2-1 5 0 6-2l2-1h2 2v1h-2v1h2 0v2 1c-3 0-3 1-6 1z" class="S"></path><path d="M274 256c1 1 2 1 3 1h0c1 1 2 1 2 2h-2c0 1 0 1 1 1-1 1-3 2-5 2h-1c0-1 1-1 1-2-2 1-2 2-4 3v-1h-3v-1c0-1 1-1 1-2 3-1 4-2 7-3z" class="K"></path><path d="M266 261c1-1 3-1 4-1v2h-1-3v-1z" class="S"></path><path d="M274 256c1 1 2 1 3 1l1 1h0c-2 0-3 0-5 1-1 0-4 1-5 0h-1c3-1 4-2 7-3z" class="f"></path><path d="M256 267h1c1-1 2-1 3-1v2c-1 0-2 1-3 3 0 0 0 1 1 1l2 2h-1l1 1c-1 1 0 1-1 1h0l1 1c1 0 2 0 2 1l1-1 3-3 1 1c-1 1-2 1-2 2h0c1 1 1 0 1 1v1h0 0v1h0-1l-3 3-1 1-3 2c0-3-3-2-5-4h-1s-1 0-1-1h-1 0c-1 0-1-1-2-1v1h-1c-1-1-3 0-4 0-1-1-1-1 0-2v-4h1v-1c0-2 1-3 2-5h1c2 0 2 0 2-2 2 0 2 1 3 2 1-1 2-1 2-1 1 0 1 0 2-1z" class="j"></path><path d="M246 269h1l-1 2c0 1 1 1 1 2l1-1h0c1-1 1-1 1-2l1 1v1h1c0 1 0 2 1 3h1c-1 0-2 0-2 1v5h-1 0c-1 0-1-1-2-1v1h-1c-1-1-3 0-4 0-1-1-1-1 0-2v-4h1v-1c0-2 1-3 2-5z" class="E"></path><path d="M244 275h0c1 0 1 0 2-1h1v1l1 1v2h-1v1h0v2c-1-1-3 0-4 0-1-1-1-1 0-2v-4h1z" class="v"></path><path d="M243 275c1 1 1 2 1 3h1c-1 1-1 2-2 2v-1-4z" class="B"></path><path d="M244 275h0c1 0 1 0 2-1h1v1c0 1-1 1-1 2l-1 1h-1c0-1 0-2-1-3h1z" class="R"></path><path d="M266 274l1 1c-1 1-2 1-2 2h0c1 1 1 0 1 1v1h0 0v1h0-1l-3 3-1 1-3 2c0-3-3-2-5-4h-1s-1 0-1-1h-1 1v-5c0-1 1-1 2-1v1h-1 1c1 1 1 1 1 2 1 0 1-1 2-1h0v2c1-1 1-2 2-1h0v2h2l2-2 1-1 3-3z" class="F"></path><path d="M258 278v2h2l-3 3v-1-2h1v-2z" class="E"></path><path d="M625 527c2 3 3 5 5 7 2 0 3 1 4 2l7 6c2 1 3 2 4 4l2 5c2 5 2 10 3 15 1 2 1 5 1 7l1 8h-1c0 3 0 3-1 5v2l-1 1c0-1-1-2-1-3h-1v2-1h-1v5l1 6-1 1h-1v7l2 1c-1 2-1 4-2 6h-1v1l-3 7h1c-1 1-2 2-2 4 1 0 1 0 1 1l-5 7h-1c-1 0-7 4-8 5l-1-1 3-2c-1-1-1-1-1-2s1-2 2-2v-2c0-1-1-1-1-1l2-2-1-1h-1c-1 0-2 0-3 1v1c-1 0-3 1-3 2l-2-1 1-1-1-1v-3h0l1-1c1-1 2-2 3-4l2-2 3-6c1-2 2-3 2-5l1-5c-1 0-1 0-2-1v-1c-1-7 3-13 4-20h-1c-2-5-4-10-7-14 1-1 0-1 1-2h1c-1-1-1-3-1-5v-3-4c0-2-1-3-2-4 1 1 1 2 0 3h0l-4-11-1-6v-1l1 1v2l1-1v-1-2h1c0-1 0-2 1-3z" class="e"></path><path d="M640 605l1 1v1c-1 0-1 0-2 1l1-3z" class="B"></path><path d="M636 582l3-1c-1 1-1 2-1 3l-1 1s-1-1-1-2v-1zm2-7h1v4c-1-1-1-1-2-1 0-1 1-2 1-3z" class="X"></path><path d="M641 606l1 1v2h-2-1v-1c1-1 1-1 2-1v-1z" class="E"></path><path d="M644 595c0-1 0-1 1-2v2 2h1v-4-1l1 6-1 1h-1l-1-4z" class="C"></path><path d="M637 578c1 0 1 0 2 1v2l-3 1c0-1 1-3 1-4h0z" class="b"></path><path d="M641 599h1c0 2-1 5 0 6v2l-1-1-1-1v-3c0-1 1-1 1-1v-2z" class="C"></path><path d="M641 596c0-3 1-5 2-7 1 1 1 4 1 6-1-1-1-1-2-1h0c-1 1 0 1-1 2z" class="K"></path><path d="M644 577l2-2c0 1 0 4 1 5h0l1 1h-1 0l-1-1c-1 1-1 1-1 2l-1-5z" class="L"></path><path d="M645 582c0-1 0-1 1-2l1 1s-1 1 0 1v3h0v1 2-1h-1l-1-5z" class="d"></path><path d="M643 571c0-1 0-1 1-1l1 1v2c0 1 1 1 1 2l-2 2v-1-2l-1-3z" class="Q"></path><path d="M645 571v2c-1 1-1 1-1 3v-2-2l1-1z" class="f"></path><path d="M644 576c0-2 0-2 1-3 0 1 1 1 1 2l-2 2v-1z" class="b"></path><path d="M636 608v5h0c0-1 0-1 1-2h0c0 1-1 2-1 3s-1 3-1 3h-1-1c0-1 0-1 1-2l2-7z" class="i"></path><path d="M636 606c0-1 0-2 1-2 0 1-1 3-1 4l-2 7-2-1v-1s1 0 1-1c1-1 2-4 3-6z" class="f"></path><path d="M637 585l1-1c0 2-1 4-2 6 0 2-1 3-1 5-1 1 1 1 0 3l1-1v1 1s-1 0-1 1v1c-1-1-1-2-2-3l1-2 2-7s1-3 1-4z" class="E"></path><path d="M636 597l4-9v9l-1 1c0-1 0-2-1-2l-2 2v-1z" class="K"></path><path d="M637 567c1-3-2-7-1-10 1 3 1 5 2 8 1 2 2 4 2 6h-1s-1-1-1-2l-1 1v1c0-2-1-3 0-4z" class="Q"></path><path d="M640 609h2 0c1 1 1 1 2 0v4 1l-2-2c-1 1-2 2-2 3h0l-1 4-1 1c0 1 0 2-1 3v-1h0v-1c0-1 1-2 1-3v-1c1-1 1 0 1-1 0-2 1-4 2-6h1l-1-1h-1z" class="P"></path><path d="M637 571v-1l1-1c0 1 1 2 1 2h1l-1 4h-1c0 1-1 2-1 3h0c0-1 0 0-1-1l-1 1h-1c1-1 1-2 2-2 0-2 0-4 1-5z" class="x"></path><path d="M636 576c1-2 1-3 2-4v1 2c0 1-1 2-1 3h0c0-1 0 0-1-1l-1 1h-1c1-1 1-2 2-2zm-1 41l-2 6c1-1 3-6 4-7h0c0 3-2 7-4 10h0v1h-1v-1h0-1l-1-1 3-6c0-1 0-1 1-2h1z" class="q"></path><path d="M633 599v-1c1 1 1 2 2 3h0c-1 1-1 1-1 2v2 2l2-1c-1 2-2 5-3 6 0 1-1 1-1 1v-2c-1 0-1-1-2-1 1-2 2-3 2-5l1-5v-1z" class="h"></path><path d="M641 596c1-1 0-1 1-2h0c1 0 1 0 2 1h0l1 4v7l2 1c-1 2-1 4-2 6h-1v-4c-1 1-1 1-2 0h0v-2-2c-1-1 0-4 0-6h-1v-3z" class="r"></path><path d="M643 601c1 2 0 4 1 7v1h-2 0v-2-2l1 1v-5z" class="b"></path><path d="M642 599v-3h0c1 1 1 2 1 4v1 5l-1-1c-1-1 0-4 0-6z" class="p"></path><path d="M642 605v-5h1v1 5l-1-1z" class="f"></path><path d="M635 578l1-1c1 1 1 0 1 1s-1 3-1 4v1c0 1 1 2 1 2 0 1-1 4-1 4l-2 7-1 2v1 1c-1 0-1 0-2-1v-1c-1-7 3-13 4-20z" class="J"></path><path d="M633 599c-1-1-1-1-1-3h2l-1 2v1z" class="d"></path><path d="M636 589h-1-1 0l2-6c0 1 1 2 1 2 0 1-1 4-1 4z" class="Q"></path><path d="M630 610c1 0 1 1 2 1v2 1l2 1c-1 1-1 1-1 2h1c-1 1-1 1-1 2l-3 6h-1c-1 0-2 0-3 1v1c-1 0-3 1-3 2l-2-1 1-1-1-1v-3h0l1-1c1-1 2-2 3-4l2-2 3-6z" class="c"></path><path d="M621 623h2l-1 1c0 1 1 2 2 2l-2 1-1-1v-3h0z" class="R"></path><path d="M625 618c1 1 1 2 1 3-1 0-2 1-3 2h-2l1-1c1-1 2-2 3-4z" class="D"></path><path d="M632 614l2 1c-1 1-1 1-1 2h1c-1 1-1 1-1 2l-3 6h-1c-1 0-2 0-3 1v1c-1 0-3 1-3 2l-2-1 1-1 2-1c4-2 7-8 8-12z" class="x"></path><path d="M637 623c1-1 1-2 1-3l1-1 1-4h0c0-1 1-2 2-3l2 2-3 7h1c-1 1-2 2-2 4 1 0 1 0 1 1l-5 7h-1c-1 0-7 4-8 5l-1-1 3-2c-1-1-1-1-1-2s1-2 2-2v-2c0-1-1-1-1-1l2-2h1 0v1h1v-1c2-1 3-3 4-4v1z" class="c"></path><path d="M637 622v1c0 2-4 5-4 6s-1 1-1 2h-2v-2c0-1-1-1-1-1l2-2h1 0v1h1v-1c2-1 3-3 4-4z" class="s"></path><path d="M641 621h1c-1 1-2 2-2 4-3 3-7 7-11 10-1-1-1-1-1-2s1-2 2-2h2c0-1 1-1 1-2a30.44 30.44 0 0 0 8-8z" class="O"></path><path d="M640 625c1 0 1 0 1 1l-5 7h-1c-1 0-7 4-8 5l-1-1 3-2c4-3 8-7 11-10z" class="E"></path><path d="M625 527c2 3 3 5 5 7 2 0 3 1 4 2l7 6c2 1 3 2 4 4l2 5c2 5 2 10 3 15 1 2 1 5 1 7l1 8h-1c0 3 0 3-1 5v2l-1 1c0-1-1-2-1-3h-1v-1h0v-3c-1 0 0-1 0-1h0 1l-1-1h0c-1-1-1-4-1-5s-1-1-1-2v-2l-1-1c-1 0-1 0-1 1-1-4-2-7-2-10 0-1-1-2-1-3-1-1-2-3-3-4l-2-4v3 1c1 1 1 1 1 3h0c-1 3 2 7 1 10-1 1 0 2 0 4-1 1-1 3-1 5-1 0-1 1-2 2-2-5-4-10-7-14 1-1 0-1 1-2h1c-1-1-1-3-1-5v-3-4c0-2-1-3-2-4 1 1 1 2 0 3h0l-4-11-1-6v-1l1 1v2l1-1v-1-2h1c0-1 0-2 1-3z" class="j"></path><path d="M629 546c1 1 1 2 2 4l-1 1s0 1-1 2-1 3-1 4v-3c0-3 1-5 1-8z" class="B"></path><path d="M636 549c-2-1-3-3-3-5 0-1-1-4 0-5 1 1 1 3 2 5v1h0c0 1 1 1 1 2v2h0z" class="f"></path><path d="M623 532v-2h1l3 9h-2v-1c-1-1-1-1-1-2l-1-1-1-1 1-1v-1z" class="K"></path><path d="M623 532c1 1 1 2 2 4h0c-1 0 0 1-1 0l-1-1-1-1 1-1v-1z" class="m"></path><path d="M641 542c2 1 3 2 4 4l2 5h-2l-2-2c0-2 0-3-1-4h-1v-1c-1-1 0-2 0-2z" class="e"></path><path d="M623 535l1 1c0 1 0 1 1 2v1h2c1 1 1 7 2 7 0 3-1 5-1 8v-4c0-2-1-3-2-4-1-4-2-7-3-11z" class="p"></path><path d="M621 531l1 1v2l1 1c1 4 2 7 3 11 1 1 1 2 0 3h0l-4-11-1-6v-1z" class="L"></path><path d="M635 544c0-2-1-3 0-4 1 1 1 1 1 2 1 1 1 3 1 4l4 10c0 2 0 3 1 5l2 9c-1 0-1 0-1 1-1-4-2-7-2-10 0-1-1-2-1-3-1-1-2-3-3-4l-2-4 1-1h0v-2c0-1-1-1-1-2h0v-1z" class="b"></path><path d="M636 547h0c1 1 1 1 1 2s1 3 1 4c1 2 1 4 2 5-1-1-2-3-3-4l-2-4 1-1h0v-2z" class="S"></path><path d="M647 575h-1v-1-1-2c1 0 1 0 1 1h0l2 2c0-2-1-2 0-3 1 0 1 1 1 3h1v-1l1 8h-1c0 3 0 3-1 5v2l-1 1c0-1-1-2-1-3h-1v-1h0v-3c-1 0 0-1 0-1h0 1l-1-1v-5z" class="J"></path><path d="M647 575h-1v-1-1-2c1 0 1 0 1 1h0l2 2c0-2-1-2 0-3 1 0 1 1 1 3h1v-1l1 8h-1l-2-6v5 1c1 1 1 2 1 3h-1v-4c0-2-1-4-2-6v1z" class="P"></path><path d="M637 546c3 1 4 2 5 4 1 0 2 2 2 3 1 1 1 1 1 3 1 1 0 2 1 3h1v-1h0 0c1 1 1 1 1 3l1 3v1h-1c0-1-1-1-1-1l-5-3c-1-2-1-3-1-5l-4-10z" class="V"></path><path d="M631 557c-1 0-1-1-1-2 1-1 1-2 1-3h0 1c1 1 0 3 1 4s1 2 2 4v-6c1 1 1 1 1 3h0c-1 3 2 7 1 10-1 1 0 2 0 4-1 1-1 3-1 5-1 0-1 1-2 2-2-5-4-10-7-14 1-1 0-1 1-2h1l1 2c1-1 1-5 1-7z" class="D"></path><path d="M633 569c1-1 0-3 0-4 1-2 1-4 1-6l2 5-1 1c0 1 0 3 1 5h-1 0l-1-1v-1 2l-1 1v-2z" class="X"></path><path d="M631 557l1 1c1 2 1 7 0 9v1c-1-1-2-2-2-4 1-1 1-5 1-7z" class="p"></path><path d="M627 564c1-1 0-1 1-2h1l1 2c0 2 1 3 2 4l1 1v2l1-1v-2 1l1 1h0 1c-1-2-1-4-1-5l1-1 1 3c-1 1 0 2 0 4-1 1-1 3-1 5-1 0-1 1-2 2-2-5-4-10-7-14z" class="d"></path><path d="M474 525c1 0 2 0 3 1h3c1 1 1 1 2 1h1c1 0 1 1 3 1h0c1 2 3 2 5 3h0l4 3c0-1-2-4-2-6h1 1c1 3 1 6 2 8v-1c1 0 1 1 1 1l1 1v1 1c1 0 1 1 1 2l1 1c1 0 1 0 2 1v1l2 2h0l-1 1v2c1 2 1 3 2 5l7 6v4 5h0v3l-1 1s1 1 1 2h0c-1 2-1 4-1 6s-1 5 0 7h1l-1 2c0-1-1-1-1-2h0l-1 1c-1-1-2-2-3-4h-1c-1-1-1-2-2-3v-2c-1 1-1 2-1 3h0l-3-6h0-1c-1 1 0 1-1 1h-1v5l1 1h-1-1-1c0 1 0 1-1 2h-1v1c-1 0-1 0-2-1v-2c-1 0-1 0-1-1l-2-2-1 1h-1v-2l-2-2h-1c-2-2-5-3-7-4l-1-1v-2c1-2 1-3 2-5l-2-4-1-3v-4l-1-3h1 1 0v-1s0-1-1-1v-1l-1-2-1-1-2-5h0l2-2c0-1 0-2 1-2l1 1v-1c1-2 1-2 1-4h0v-4h0l-1-1v1h-1v-3h0l1-1z" class="j"></path><path d="M484 537c1 2 1 3 2 4h-2v-4z" class="B"></path><path d="M507 564c0 1 1 2 1 4 0 0-1-1-2-1v-2l1-1z" class="V"></path><path d="M501 542c1 0 1 0 2 1 0 1-1 2-2 2h-1v-1c0-1 1-2 1-2z" class="C"></path><path d="M475 533c1 1 1 2 1 4v3h0c-1-1-1-2-2-2v-1c1-2 1-2 1-4h0z" class="t"></path><path d="M492 549h1 1l1 1v1h1 0c1 0 1 1 1 1v1h-1c-1 0-1 1-1 1l-2-2c0-1 0-2-1-3h0z" class="E"></path><path d="M478 535h-1v-3-1l2 1v1l3 6c-2-1-3-2-4-4z" class="l"></path><path d="M475 529h1v-1l1-1c1 1 1 2 2 3v1 1l-2-1v1 3h1l-2 2c0-2 0-3-1-4v-4z" class="D"></path><path d="M494 528h1c1 3 1 6 2 8 0 1 1 1 1 2h0l-7-7h0l4 3c0-1-2-4-2-6h1z" class="b"></path><path d="M485 533c1 0 2 0 3 1 0 0 1 1 1 2 0 0 1 2 2 2h0c1 1 1 1 1 2h1-1l-1 1h0c-1-1-1-2-1-2h-1c1 1 1 2 1 3h-1 0 0c0-3-2-7-4-9z" class="B"></path><path d="M489 542h1c0-1 0-2-1-3h1s0 1 1 2h0c1 1 1 1 1 2h0v2l-1 1c0 1 1 2 1 3h0-1v2-2l-1-1v-2c-1-1-1-2-1-4z" class="e"></path><path d="M491 541l1-1c1 1 1 2 1 2l1 2c0 1 0 2 1 3h0v1l1 3h0-1v-1l-1-1h-1-1c0-1-1-2-1-3l1-1v-2h0c0-1 0-1-1-2z" class="C"></path><path d="M474 525c1 0 2 0 3 1h3c1 1 1 1 2 1-1 0-1 0-2 1v1h0l-1 1v3-1-1-1c-1-1-1-2-2-3l-1 1v1h-1 0l-1-1v1h-1v-3h0l1-1z" class="E"></path><path d="M493 540h1c1 1 1 1 1 2h1v1 1l1 3v1l1 2c0 1-1 1-1 2 0 0 0-1-1-1l-1-3v-1h0c-1-1-1-2-1-3l-1-2s0-1-1-2h1z" class="e"></path><path d="M484 537c-1-1-1-3-1-5h0s1 0 1 1h1c2 2 4 6 4 9h0 0v2h-1l-1-2-1-1c-1-1-1-2-2-4z" class="l"></path><path d="M504 582c1-3 1-6 1-9 0 1 1 3 1 5 0 1-1 3 0 4s1 2 1 3l1-1c-1-1-1-1-1-2-1-1 0-3 0-3 1 0 1 2 1 3v2c1 2 1 3 2 4v-5l1-1c0 2 1 4 0 6h0l-1 1c-1-1-2-2-3-4h-1c-1-1-1-2-2-3z" class="H"></path><path d="M500 570c1 0 1 0 1-1 1 1 2 1 2 3v1c0 2 1 5 1 7-1 1-1 2-1 3h0l-3-6h0c-1-2-1-4-1-6l1-1z" class="s"></path><path d="M500 570c1 0 1 0 1-1 1 1 2 1 2 3v1c-1 1-1 2-1 3v1h-1c0-1 0-5-1-6v-1z" class="v"></path><path d="M487 542l1 2h1v-2c0 2 0 3 1 4v2l1 1v2 1h-1-1 0v2c-1 0-1-1-1-1l-1-1c-1 0-1-1-1-2 0 0-1-1-1-2v-1-1l-1-1 1-1c1 0 1-1 2-2z" class="D"></path><path d="M485 548l1 1v1h1l1 1v-2h2 1v2 1h-1-1 0v2c-1 0-1-1-1-1l-1-1c-1 0-1-1-1-2 0 0-1-1-1-2zm10 14c0-1 0-1 1-1v-2h0c1 1 1 1 2 1h0l1 1v1h-1v3 1l1 1h0v2l1 1-1 1c0 2 0 4 1 6h-1c-1 1 0 1-1 1-1-1 0-3 0-4l-3-3v-1h1c2-2-1 0 1-1v-2c-1-1-1-1-1-2l-1-3h0z" class="E"></path><path d="M484 558h-1v1h-1c0-2-1-2-1-4h-1 0v2h0v1l-1-1v-1h0l-1-2c-1-1 0-4-1-6 0-1 0-2-1-4h0v-1l2 2h0c0 1 1 1 2 1 0 1 0 1 1 2v-2-2l1-1v2c0 1 1 2 1 3v1 3c0 1 0 1 1 2v2c1 1 1 2 1 2h-1z" class="C"></path><path d="M487 552l1 1s0 1 1 1v-2h0c1 1 1 1 1 2 1 1 2 0 2 1 1 0 1 2 1 2 1 2 1 3 2 5h0l1 3c0 1 0 1 1 2v2c-2 1 1-1-1 1h-1 0c-1-1-2-2-2-3s0-1-1-2c0-1 0-1-1-2h-3v-1-2-1-1h0c-1-1-1-1-1-2h0v-4z" class="I"></path><path d="M490 554c1 1 2 0 2 1 1 0 1 2 1 2 1 2 1 3 2 5h0l1 3c0 1 0 1 1 2v2c-2 1 1-1-1 1h-1 0v-1h0v-2c-1-1-1-1-1-2h-1v-2-1c-1-2-1-4-2-6-1-1-1-1-1-2z" class="D"></path><path d="M503 555c0-1-1-1 0-2h0l2 2 1-1 7 6v4 5h0v3l-1 1s1 1 1 2h0c-1 2-1 4-1 6s-1 5 0 7h1l-1 2c0-1-1-1-1-2 1-2 0-4 0-6l-1 1v5c-1-1-1-2-2-4v-2l1-1v-3c1 0 1 2 2 3v-5l1-1-4-7c0-2-1-3-1-4-1-1-2-4-3-4 0 1 0 2-1 3l-2-2c-1-2-2-4-2-6l1-1 3 6c1-2-1-3 0-5z" class="X"></path><path d="M503 555c0-1-1-1 0-2h0l2 2 1-1 7 6v4 5h0c-1-2-1-3-2-5l-1 1v1c-1-1-1-2-1-4h-1v1h-1c0-1-1-3-2-4s-1-2-2-4z" class="w"></path><path d="M481 544v-1c2 0 3 0 4 1l-1 1 1 1v1 1c0 1 1 2 1 2 0 1 0 2 1 2v4h0c0 1 0 1 1 2h0v1 1 2 1h3c1 1 1 1 1 2 1 1 1 1 1 2s1 2 2 3h0v1l3 3c0 1-1 3 0 4h-1v5l1 1h-1-1-1c0 1 0 1-1 2h-1v1c-1 0-1 0-2-1v-2c-1 0-1 0-1-1l-2-2-1 1h-1v-2l-2-2h-1c-2-2-5-3-7-4l-1-1v-2c1-2 1-3 2-5v2c1 0 1-1 1-1v-1-1h0c1 0 2 1 3 1 0-2 1-2 2-3l1-1v-4h1s0-1-1-2v-2c-1-1-1-1-1-2v-3-1c0-1-1-2-1-3v-2l-1 1z" class="D"></path><path d="M484 578v-6c1 1 1 2 1 2 0 1 0 3 1 3 1-3-1-6-1-10h0 1l1 2c-1 1 0 2 0 4 0 1 2 1 2 2v5h-1v1l-1 1h-1v-2l-2-2z" class="X"></path><path d="M487 573c0 1 2 1 2 2v5h-1v1l-1 1v-2-7z" class="B"></path><path d="M477 568c1 0 1-1 1-1v-1-1h0c1 0 2 1 3 1v4c1 1 1 1 0 2h0l1 1h0v-2h1 0v1 4 1 1c-2-2-5-3-7-4l-1-1v-2c1-2 1-3 2-5v2z" class="Q"></path><path d="M477 568c1 0 1-1 1-1v-1-1h0c1 0 2 1 3 1v4c1 1 1 1 0 2h0l-2-3c-1 0-1 1-1 1-1-1-1-1-1-2z" class="P"></path><path d="M481 544v-1c2 0 3 0 4 1l-1 1 1 1v1 1c0 1 1 2 1 2 0 1 0 2 1 2v4h0c0 1 0 1 1 2h0v1 1 2 1h0v3h0l-1-1h-1c0-1 0-3-1-4h0v2 1s0 1 1 1l-1 1h0c-1-1-1-1-1-2l-1-1 1-1v-4h1s0-1-1-2v-2c-1-1-1-1-1-2v-3-1c0-1-1-2-1-3v-2l-1 1z" class="R"></path><path d="M487 569l1-1h0 1 0c1 2 1 3 2 4 1 0 1-1 1-1-1-1-1-2-1-3s1-1 1-1h1c0 1 1 2 2 3h0v1l3 3c0 1-1 3 0 4h-1v5l1 1h-1-1-1c0 1 0 1-1 2h-1v1c-1 0-1 0-2-1v-2c-1 0-1 0-1-1l-2-2v-1h1v-5c0-1-2-1-2-2 0-2-1-3 0-4z" class="c"></path><path d="M489 575c0 1 1 2 2 3v4 2h0c-1 0-1 0-1-1l-2-2v-1h1v-5z" class="l"></path><path d="M491 582h1c0-2 0-3-1-4v-2h1c0 1 0 2 1 3v-3h1c1 2 1 4 2 6 0-2-1-5 0-6h1v2 5l1 1h-1-1-1c0 1 0 1-1 2h-1v1c-1 0-1 0-2-1v-2h0v-2z" class="K"></path><path d="M493 586l1-1c-1-1-1-3-1-5l1 1c0 1 1 2 2 2h1l1 1h-1-1-1c0 1 0 1-1 2h-1zm134-22c3 4 5 9 7 14h1c-1 7-5 13-4 20v1c1 1 1 1 2 1l-1 5c0 2-1 3-2 5l-3 6-2 2c-1 2-2 3-3 4l-1 1h0v3l1 1-1 1 2 1c0-1 2-2 3-2v-1c1-1 2-1 3-1h1l1 1-2 2s1 0 1 1v2c-1 0-2 1-2 2s0 1 1 2l-3 2c-2 2-5 3-8 4l-4 4h0l-3 3h1l2-1v1c-2 2-1 2-2 4h1c-1 0-2 2-2 2l-4 2v1c-3 2-8 4-13 5-2 1-5 1-7 2 0-1 5-4 6-5l-1-2v-1-2h1v-1c-1-1-1-1-1-2 3-2 3-3 4-6h0c1-1 2-2 3-4h0c0-2 0-3-1-4l1-2-1-1c-1 0-1 1-2 1l-1-1s-1 1-2 1-3 1-5 1c-1 1-2 0-3 0v1c-2 1-4 1-6 0h0l-1 1v1h-4c-2 0-2-1-2-2h-6 0c-4-1-7-1-11-2l-4-1c1 0 2 0 3-1 1 0 2 0 2 1h1l9-1c9-1 17-3 25-7l3-2c2-1 4-2 6-4 1-1 3-1 4-2 5-4 10-8 14-13h1v-2-1 1l-1-1c2-1 4-3 4-5 0-1 3-3 3-4l-2-2-1-1c1-2 1-3 2-5l1-2 1-1h-1c-1-1-1-1-1-2h-1-1c2-2 3-5 4-8-1-1-1-1-1-2 1-2 1-3 1-5h1v1-2z" class="R"></path><path d="M625 582c0-1 0-2-1-2l1-1h2 0l-1 1v2h-1z" class="B"></path><path d="M608 638c0-1 0-1 1-2 0-1 1-1 2-1h1l2-1c-2 1-4 3-6 4z" class="C"></path><path d="M626 565h1v1c0 2-1 4-1 6-1-1-1-1-1-2 1-2 1-3 1-5z" class="f"></path><path d="M611 620c0 1 0 2-1 3s-3 0-5 1h-1c1 0 1-1 2-1s1-1 2-1l1-1 2-1z" class="V"></path><path d="M627 579h0c1-2 1-4 2-5l1 6v-1h-1c-1 0-1 1-1 1l-1 1v-2h0z" class="h"></path><path d="M604 642c1-2 2-2 3-3v2 2l-2 2h0l-1 1v-1c-1-1-1-1-1-2h-1l2-1z" class="Q"></path><path d="M627 579v2l1-1s0-1 1-1h1v1c0 2 0 4-1 7 0 0-1 1-1 2v-3c1-1 1-1 1-2-1-1-2-1-2-2s0-1-1-2l1-1z" class="K"></path><path d="M606 631c1-1 4-3 5-3-1 2-4 3-5 4-2 2-3 3-4 5-1-1-1-2-1-3l1-2c1 0 3-1 4-1z" class="Q"></path><path d="M622 606h0c0-2 2-4 3-5 1 1 0 3 1 4 1-1 1-1 3-1l-1 3-1 2v-1h-1l-1 1v-1-1l-1-1v2h-1c0-1 0-1-1-2z" class="g"></path><path d="M602 637c1-2 2-3 4-5v3c-1 1-1 1-2 1v1c-1 2-1 3-3 3l-1 1h-1 0c0-2 0-3-1-4l1-2 2-1c0 1 0 2 1 3z" class="E"></path><path d="M601 634c0 1 0 2 1 3-1 1-2 3-3 4 0-2 0-3-1-4l1-2 2-1z" class="L"></path><path d="M631 598v1c1 1 1 1 2 1l-1 5c0 2-1 3-2 5l-3 6-1-1c0-2 1-2 2-4 1-1 1-2 2-3-1-1-2-1-2-1l1-3v1l2-1v-6z" class="Q"></path><path d="M631 604c-1 2-1 3-1 4-1-1-2-1-2-1l1-3v1l2-1z" class="C"></path><path d="M631 599c1 1 1 1 2 1l-1 5h-1v-6z" class="O"></path><path d="M626 580c1 1 1 1 1 2s1 1 2 2c0 1 0 1-1 2v3l-2 2-1 2h0l-2-2-1-1c1-2 1-3 2-5l1-2 1-1v-2z" class="Z"></path><path d="M625 583l2 2v4c-1 0-1 1-1 2l-1 2v-2l1-3c-1-1-1-2-2-3l1-2z" class="X"></path><path d="M624 585c1 1 1 2 2 3l-1 3v2h0l-2-2-1-1c1-2 1-3 2-5z" class="q"></path><path d="M622 606c1 1 1 1 1 2h1 0l-2 4-3 8c-1 1-3 2-4 3 0-2 1-4 2-6 1-4 3-8 5-11z" class="J"></path><path d="M622 606c1 1 1 1 1 2l-2 2v1h0l-1 1v1l-1 1v2c-1 0-1 0-1 1h-1 0c1-4 3-8 5-11z" class="d"></path><path d="M600 620c1 1 1 1 2 1l4-4c1-1 3-1 4-2h1c-1 1-1 2-2 2l-2 1v1c2-1 4-2 5-2 0 0 0 1-1 2v1l-2 1-1 1c-1 0-1 1-2 1s-1 1-2 1c0 0-1 0-2 1h-2c-1 0-2 1-3 1h-3c-1 1-1 1-2 1l-1-1 3-2c2-1 4-2 6-4z" class="D"></path><path d="M597 623h2v1l-1 1h-2v-1l1-1z" class="R"></path><path d="M599 629c-1 1-3 2-4 2-4 2-8 2-11 3-1 1-3 1-4 1v1l-2 1h3-2l-1 1v1h-4c-2 0-2-1-2-2h-6 0c-4-1-7-1-11-2l-4-1c1 0 2 0 3-1 1 0 2 0 2 1h1 0c1 1 4 1 6 1 3 0 6 1 9 1 6-1 12-3 17-5 3-1 7-2 10-2z" class="J"></path><path d="M572 637l8-2v1l-2 1h3-2l-1 1v1h-4c-2 0-2-1-2-2z" class="D"></path><defs><linearGradient id="H" x1="601.881" y1="626.058" x2="580.493" y2="639.628" xlink:href="#B"><stop offset="0" stop-color="#1a1919"></stop><stop offset="1" stop-color="#39383a"></stop></linearGradient></defs><path fill="url(#H)" d="M599 629c1-1 1-1 2-1h0 3 0l1 1s1 1 1 2c-1 0-3 1-4 1l-1 2-2 1-1-1c-1 0-1 1-2 1l-1-1s-1 1-2 1-3 1-5 1c-1 1-2 0-3 0v1c-2 1-4 1-6 0h0 2-3l2-1v-1c1 0 3 0 4-1 3-1 7-1 11-3 1 0 3-1 4-2z"></path><path d="M598 634v-1l4-1-1 2-2 1-1-1z" class="G"></path><path d="M624 608v-2l1 1v1 1l1-1h1v1l1-2s1 0 2 1c-1 1-1 2-2 3-1 2-2 2-2 4l1 1-2 2c-1 2-2 3-3 4l-1 1h0v3l1 1-1 1c-1 0-2 1-3 2h-4c0-2 0-4 1-6v-1c1-1 3-2 4-3l3-8 2-4h0z" class="t"></path><path d="M624 608v-2l1 1v1 1l1-1h1v1 3l-1 2-1-2c0 2-1 2-2 3l1-2c-1-1-1-1-2-1l2-4h0z" class="K"></path><path d="M625 612l-1-1 2-2h0l1 3-1 2-1-2z" class="X"></path><path d="M628 607s1 0 2 1c-1 1-1 2-2 3-1 2-2 2-2 4l1 1-2 2c-1 2-2 3-3 4l-1-1h0v-1c2-2 3-4 4-6h1l1-2v-3l1-2z" class="F"></path><path d="M615 623c1-1 3-2 4-3v2c-1 2-2 3-2 5h0l1-2h1l1-1h0l1-1v3l1 1-1 1c-1 0-2 1-3 2h-4c0-2 0-4 1-6v-1z" class="O"></path><path d="M615 624v4l1-1h1 0l1-2h1c-1 2-1 3-1 5h-4c0-2 0-4 1-6z" class="s"></path><path d="M620 624h0l1-1v3l1 1-1 1c-1 0-2 1-3 2 0-2 0-3 1-5l1-1z" class="K"></path><path d="M620 624h0l1-1v3 1h-1v-3z" class="r"></path><path d="M626 627v-1c1-1 2-1 3-1h1l1 1-2 2s1 0 1 1v2c-1 0-2 1-2 2s0 1 1 2l-3 2c-2 2-5 3-8 4l-4 4c-2 0-4 1-6 2l-1-1c0-1-1-1-2-1h0l2-2v-2-2l1-1c2-1 4-3 6-4 1-1 2-2 2-4h-1-1 4c1-1 2-2 3-2l2 1c0-1 2-2 3-2z" class="C"></path><path d="M616 639s1-1 2-1l3-3c0 1 1 1 2 1v1c-2 0-4 2-5 3h-1l-1-1z" class="h"></path><path d="M627 634s0-1 1-1c0 1 0 1 1 2l-3 2c-2 2-5 3-8 4v-1c1-1 3-3 5-3 1-1 2-1 2-2 1 0 1 0 1-1h1z" class="f"></path><path d="M626 627v-1c1-1 2-1 3-1h1l1 1-2 2s1 0 1 1v2c-1 0-2 1-2 2-1 0-1 1-1 1l-1-2v1h-2v-1c0-1 0-1 1-2h-1c0-1 0-1 1-2l1-1z" class="B"></path><path d="M630 625l1 1-2 2s1 0 1 1v2c-1 0-2 1-2 2-1 0-1 1-1 1l-1-2c0-1 1-2 2-3s1-3 1-4h1z" class="X"></path><path d="M616 639l1 1h1v1l-4 4c-2 0-4 1-6 2l-1-1c0-1-1-1-2-1h0l2-2 1-1c1 0 2 0 3-1 0-1 1-1 1-1h1c1 0 2 0 3-1z" class="Z"></path><path d="M617 640h1v1l-4 4c-2 0-4 1-6 2l-1-1c2-1 3-3 5-4h2c1 0 2-1 3-2z" class="s"></path><path d="M621 628l2 1c-2 0-3 1-4 2h0c1 1 2 1 3 1h0 1c0 1-1 1-2 1l-1 1h0c-2 2-5 4-8 6 0 0-1 0-1 1-1 1-2 1-3 1l-1 1v-2-2l1-1c2-1 4-3 6-4 1-1 2-2 2-4h-1-1 4c1-1 2-2 3-2z" class="b"></path><path d="M608 642v-1c1-1 1-1 2-1l1 1c-1 1-2 1-3 1z" class="Q"></path><path d="M620 634h0c-2 0-3 1-4 2h-1c-1 1-1 1-2 1 0-1 5-5 6-6h0c1 1 2 1 3 1h0 1c0 1-1 1-2 1l-1 1h0z" class="i"></path><path d="M596 645l1 1c-1 1-1 1-1 2 2-1 3-3 4-4v-1c1-2 2-1 4-1l-2 1h1c0 1 0 1 1 2v1l1-1c1 0 2 0 2 1l1 1c2-1 4-2 6-2h0l-3 3h1l2-1v1c-2 2-1 2-2 4h1c-1 0-2 2-2 2l-4 2v1c-3 2-8 4-13 5-2 1-5 1-7 2 0-1 5-4 6-5l-1-2v-1-2h1v-1c-1-1-1-1-1-2 3-2 3-3 4-6z" class="C"></path><path d="M597 648l1 1h-1l1 1h0l-3 1 2-3z" class="O"></path><path d="M598 650v2l-1 1c-2 1-3 2-5 3v-2h1l2-3 3-1z" class="J"></path><path d="M592 656c2-1 3-2 5-3h2 0l-2 2c-1 0-1 1-1 1l-2 2-1 1-1-2v-1z" class="L"></path><path d="M602 643h1c0 1 0 1 1 2v1l1-1c1 0 2 0 2 1l1 1c-3 1-7 2-10 5v-2h0l-1-1h1l-1-1 2-2 3-3z" class="q"></path><path d="M614 645h0l-3 3-2 1c-1 1-3 2-4 3 0 1 0 1-1 1l-1 1c-1 1-3 2-4 2h0-3s0-1 1-1l2-2h0-2l1-1c3-3 7-4 10-5 2-1 4-2 6-2z" class="I"></path><path d="M599 653c0-1 0-1 1-1l2-1 2-1c0-1 1-1 2-1v1c-1 0-1 0-1 2 0 1 0 1-1 1l-1 1c-1 1-3 2-4 2h0-3s0-1 1-1l2-2h0z" class="Y"></path><path d="M605 652c1-1 3-2 4-3l2-1h1l2-1v1c-2 2-1 2-2 4h1c-1 0-2 2-2 2l-4 2v1c-3 2-8 4-13 5-2 1-5 1-7 2 0-1 5-4 6-5l1-1 2-2h3 0c1 0 3-1 4-2l1-1c1 0 1 0 1-1z" class="E"></path><path d="M596 656h3l-2 2c-1 1-2 1-3 0l2-2z" class="G"></path><path d="M604 656c1 0 2-2 4-2 0 0 1-1 2-1l1 1-4 2c-3 2-6 3-9 3l6-3z" class="O"></path><path d="M605 652c1-1 3-2 4-3l2-1h1l2-1v1c-2 2-1 2-2 4h1c-1 0-2 2-2 2l-1-1c-1 0-2 1-2 1-2 0-3 2-4 2-2 0-5 2-7 2l2-2h0c1 0 3-1 4-2l1-1c1 0 1 0 1-1z" class="Z"></path><path d="M614 647v1c-2 2-1 2-2 4h0c-1 0-1 0-2-1 1 0 1 0 1-1s1-1 1-2l2-1z" class="K"></path><path d="M208 524l1 1c1 2 2 4 3 5 0 1 1 2 2 2 1 1 1 2 2 2 1 1 1 2 2 2-1 1-2 2-2 4h1v1c0 1 0 2 1 3l2 2h1c1 1 2 2 2 3v1h0c-1 0-1-1-2 0h1-1c1 1 1 2 2 2 0 1 1 1 1 1 0 1 1 0 2 1 1 0 1 1 1 1l1 1h0l1-1v1s1 0 1 1c1 0 2 1 2 1l1 1h0c0 1 0 1 1 2s4 1 5 2v1s-1 0-1 1-1 3-2 4l-2 1h0l1 1h-2c-2 0-4-1-6-2h-1c1 1 2 2 3 2 2 1 4 1 4 2v1c-2-1-4-1-6-1h-1 0c-1 0-2 0-3-1s-2-1-3-1c0 1 1 1 2 1 1 1 2 1 3 3-1 2 4 4 3 6v1l-7-4h0c0 2 4 4 6 5h0c0 1 0 1-1 1h-1l-4-3h0c1 2 3 2 4 3v1c-1 0-2 0-3-1v1c1 0 2 1 3 1v1c-1 0-3-1-5-1 1 2 4 2 4 3s0 1-1 1c-2 0-3-1-4-2l5 4c-2 0-4-1-5-1l3 3h-1-4c2 1 3 1 4 2l-1 1c-2-1-3-1-4-1 2 1 3 2 5 3-2 0-5-1-6 0l3 2v1h0c-2 0-2-1-3-1v1 1h2 0c0 1-1 2-1 2-1 0-1 0-2 1-2-2-3-3-4-5 0 1-1 1 0 1 0 1 1 1 0 2h0c-1 0-1 1-2 1h-1l1 1-1 1h0c-1-1-1-1-2-1v1 1l-1-2-2 1h-1-1c-1 0-1-1-2-2v-1h-1v2h-1v-1h-1v2c-1 0-1 0-2-1v2h-1c0-1 0-1-1-2v2h-1v-1h-1 0 0c0 1 0 1-1 1h0 0c-1-1 0-1-1-1v-1c-1 1 0 2-1 3-1 0-1-1-1-1v-1h0l-1-1c-1 1-1 1-1 2h0l-1-1-1 1h-1c0-1-1-1-1-2 0-2 0-2-1-4l-1 1h-1c-1 1-1 1-1 3v1h0l-1 1h-2v-1-2c4-4 4-9 4-14 0-4 1-8 2-12 0-1 0-4 1-5l3-9c1-1 2-2 2-3l4-7-1-1h1 0l1-2c0-1 1-1 2-1h0c1-1 3-2 3-4 2-2 1-4 2-6 0-1-1-3 1-4 0-1 0-3 1-4h1c1 0 1 1 2 1v-3c1 0 1-1 1-1-1 0-1-1-1-1v-1h-1v-2c1 0 1 1 1 1h1l1-2v2h1c0-1 0-1 1-1l1 1v-2c1 0 1 1 3 2l-1-3z" class="j"></path><path d="M211 584c2 1 2 2 3 3h0l-3-1v-1-1z" class="o"></path><path d="M191 571c0 1 1 5 1 6h-1c-1-1-1-3-1-5 0-1 0-1 1-1z" class="i"></path><path d="M188 568v4c1-1 1-1 1-2v-1c1 0 1 1 2 2-1 0-1 0-1 1h-1-1 0-1l-1-1v-2l2-1z" class="E"></path><path d="M211 586l-1 1s0-1-1-1c0-1 0-1-1-2h0c-1-1-1-2-1-3 1 1 2 2 4 3h0v1 1z" class="L"></path><path d="M188 563l1 1v5 1c0 1 0 1-1 2v-4c0-1-1-2 0-2v-3z" class="Y"></path><path d="M205 554c1 1 1 2 1 3 1 2 2 3 3 5h0l-1 1-1-1c-1-1-1-2-1-3l-1-1c0-1-1-2 0-4z" class="b"></path><path d="M234 570h-2c-3-1-5-3-7-6l1 1c1 0 2 1 3 2h0c2 0 4 1 6 2h1l-2 1h0z" class="o"></path><path d="M194 555s1 0 1 1v2h0c-1 1-1 2 0 4-1-1-2-1-3-1v-4h0l2-2z" class="F"></path><path d="M202 546c0-1 0-1 1-2v1h1 1v-1c1 1 1 1 2 1v1h0l1 2c-1 0-1 0-2-1 0 2 0 3-1 4v-1-1h-2v-1l-1-2z" class="c"></path><path d="M205 545v-1c1 1 1 1 2 1v1h0l1 2c-1 0-1 0-2-1h-1v-2z" class="K"></path><path d="M187 554h1c1 0 2 1 2 2 1 1 1 1 1 2-1 0-1 1-2 1l-1-1v1c0 1-1 2-2 3h0-2l4-7-1-1z" class="F"></path><path d="M192 586h-2c0-1 0-2-1-3v-1c-1-1-1-4 0-5h0c1 0 2 2 3 3h0 1v-2c0 1 0 2 1 3 0 2 1 3 1 6h-1v1h-1c0-1 0-2-1-2h0z" class="C"></path><path d="M192 586l-1-3h1c1 1 1 2 1 3l1 1v1h-1c0-1 0-2-1-2h0z" class="e"></path><path d="M199 556c1 0 1-1 2-2h1 1c0 2 1 3 1 4v-1h0v-2l1-1c-1 2 0 3 0 4l1 1c0 1 0 2 1 3l-1 1h-1-1 0-2l-3-7z" class="C"></path><path d="M204 563c-1-2-2-4-2-7h1c0 2 2 5 2 7h-1 0z" class="Q"></path><path d="M194 555s1-1 2 0l1 1v1l1 1h1c-1-1-1-2-1-3l1 1 3 7-1 1v-1l-1 1-1-1v-1h-1v2h-1v-1h-1-1v-1c-1-2-1-3 0-4h0v-2c0-1-1-1-1-1z" class="K"></path><path d="M194 555s1-1 2 0l1 1v1h0v3c-1-1-2-1-2-2h0v-2c0-1-1-1-1-1z" class="B"></path><path d="M193 578l1-1v3l1-1v-1l1-1v2c0 1 2 3 3 5 0 1 1 2 2 3 0 0 0 1 1 1l-1 1c-1 0-1-1-2-1v2h0c-1-1-1-2-2-3h-1v2h-1v-2h0c0-3-1-4-1-6-1-1-1-2-1-3z" class="i"></path><path d="M194 581h1c0 1 2 4 2 6h-2 0c0-3-1-4-1-6z" class="l"></path><path d="M212 546c1 2 2 1 3 2h0c1 2 3 5 4 6l2 1c-1 0-2 0-2-1 0 2 1 2 1 3l-1 1c0-1-1-2-3-3l1 1v1h0l1 1c-1 1-1 1-1 2l-2-2c-2-1-3-2-3-4 0 0 0-1-1-1-1-1-1-1-2-1v-1h1 1c0-2-1-3-2-4h2l1-1z" class="P"></path><path d="M215 548c1 2 3 5 4 6l2 1c-1 0-2 0-2-1 0 2 1 2 1 3l-1 1c0-1-1-2-3-3h0l-1-1c-1-1-1-2-1-2l2 1h0c0-1-1-2-1-3l-1-1 1-1z" class="b"></path><path d="M196 541c0-1-1-3 1-4l1 1 1 1v1 1h0l1 3c0-1 0-1 1-1 1 1 0 3 0 4l1 1v1l-2 1h-1-1l-1 2h0c-1 0-1-1-1-2h-2s-1 1-1 2l-2-1h0c1-1 3-2 3-4 2-2 1-4 2-6z" class="B"></path><path d="M199 540v1 6 2h-1-1c1-2 1-3 1-5 0-1 0-2 1-4z" class="X"></path><path d="M196 541c0-1-1-3 1-4l1 1 1 1v1c-1 2-1 3-1 4h-1v1c-1-2 0-3-1-4z" class="P"></path><path d="M201 587h1l1 1h1v-1-1s1 0 1 1h0l2 2v-2h1v1s1 1 1 2h0c1 1 2 1 3 1 1 1 2 3 4 3h0 1 0c2 1 3 1 4 2l-1 1c-2-1-3-1-4-1h-2c0-1-1-1-1-1l-1-1v-1h0l-1-1-2 2h0v1l-2-2s0 1-1 2l-1-1h0l1 2h-1 0c-1 0-1 0-2-1v-1c-1 0-1-1-2-2l-1 1c-1-1-1-1-1-3v-2c1 0 1 1 2 1l1-1c-1 0-1-1-1-1z" class="w"></path><path d="M207 593l-1-1v-1h0c1 0 1 0 2 1l1 1v1h0v1l-2-2z" class="E"></path><path d="M221 553l1 1h0c1-1-2-2-2-3l1-1c1 1 1 2 2 2 0 1 1 1 1 1 0 1 1 0 2 1 1 0 1 1 1 1l1 1h0l1-1v1s1 0 1 1c1 0 2 1 2 1l1 1h0c0 1 0 1 1 2s4 1 5 2v1s-1 0-1 1-1 3-2 4c-2-3-7-3-9-6l-1-1h-3c-1 0-3-3-4-4l1-1c0-1-1-1-1-3 0 1 1 1 2 1l-2-1 2-1z" class="w"></path><g class="B"><path d="M220 557v1l2 1c1 0 2-1 4 1v1c1 1 1 1 1 2h0 0l-1-1h-3c-1 0-3-3-4-4l1-1z"></path><path d="M228 556l1-1v1s1 0 1 1c1 0 2 1 2 1l1 1h0c0 1 0 1 1 2s4 1 5 2v1c-2-1-4-1-5-2-2 0-3-1-5 0l-1-1h1 1 0l-1-1h-1-1l-2-2v-1c1 0 2 1 2 1 1 1 2 1 3 1v-1c-1 0-1-1-2-2h0z"></path></g><path d="M229 562c2-1 3 0 5 0 1 1 3 1 5 2 0 0-1 0-1 1s-1 3-2 4c-2-3-7-3-9-6h0c1 0 2 1 3 1 3 1 5 1 7 1l-5-2c-1 0-2 0-3-1h0z" class="o"></path><path d="M184 562h2c-1 1-1 2-1 3 1-1 1-2 3-2v3c-1 0 0 1 0 2l-2 1v2l1 1c-1 0-1 0-1 1-1 0-1 1-1 2v1l1-1h1v7l1 1h0c0 1-1 2-1 3-1 0-1 1-1 1-1 0-1-1-2-2h0c-1-1 0-3-1-4 0-1 0-1 1-1v-4l-1 1v1c-1 0-1-1-2-1 0 2-1 4-2 7v4h-2 0l-1 3c0-4 1-8 2-12 0-1 0-4 1-5l3-9c1-1 2-2 2-3z" class="P"></path><path d="M184 562h2c-1 1-1 2-1 3h1v2l-1 1h0v2 1h0l-1 1v-3-1c1 0 1-1 1-2h-2v2c-2 1-3 4-4 6h0 0l3-9c1-1 2-2 2-3z" class="B"></path><path d="M179 574h0 0c1-2 2-5 4-6 0 1 0 2-1 4 0 1 1 1 1 1 0 1-2 3-2 4 0 2-1 4-2 7v4h-2 0l-1 3c0-4 1-8 2-12 0-1 0-4 1-5z" class="f"></path><path d="M179 584v4h-2 0c0-2 1-3 2-4z" class="D"></path><path d="M208 524l1 1c1 2 2 4 3 5 0 1 1 2 2 2 1 1 1 2 2 2 1 1 1 2 2 2-1 1-2 2-2 4h1v1c0 1 0 2 1 3l2 2h1c1 1 2 2 2 3v1h0c-1 0-1-1-2 0h1-1l-1 1c0 1 3 2 2 3h0l-1-1-2 1c-1-1-3-4-4-6h0c-1-1-2 0-3-2l-1 1h-2l-1-1h-1 0 0v-1c-1 0-1 0-2-1v1h-1-1v-1c-1 1-1 1-1 2l1 2h-1l-1-1c0-1 1-3 0-4-1 0-1 0-1 1l-1-3h0v-1-1l-1-1-1-1c0-1 0-3 1-4h1c1 0 1 1 2 1v-3c1 0 1-1 1-1-1 0-1-1-1-1v-1h-1v-2c1 0 1 1 1 1h1l1-2v2h1c0-1 0-1 1-1l1 1v-2c1 0 1 1 3 2l-1-3z" class="E"></path><path d="M213 542h0c1 0 2 1 3 1l-1 1h-1s-1-1-1-2z" class="R"></path><path d="M205 544v-3h1l1 2v2c-1 0-1 0-2-1z" class="k"></path><path d="M213 540v-2h1l1 1 1 1h1v1c-2 0-1 0-3 1 0-1-1-1-1-2z" class="C"></path><path d="M202 533h1c1-1 1-1 1-2h0 1v1l1 3v3h-1l-1-1v-1l-1 1c0-2 0-3-1-4z" class="F"></path><path d="M205 532l1 3v3h-1l-1-1c0-2 0-3 1-5z" class="t"></path><path d="M211 542l2 2s0 1 1 1c0 1 1 2 2 3h0c1 1 1 1 1 2h1v1c1 1 1 2 3 2h0l-2 1c-1-1-3-4-4-6h0c-1-1-2 0-3-2l-1-4z" class="c"></path><path d="M204 527c0-1 0-1 1-1l1 1v1c1 0 0 0 1 1 1 0 1 1 1 1v3s-1 0-1 1l-1 1-1-3v-1c0-1 0-3-1-4z" class="e"></path><path d="M200 528v-2c1 0 1 1 1 1h1l1-2v2h1c1 1 1 3 1 4h-1 0c0 1 0 1-1 2h-1s0-1-1-2c1 0 1-1 1-1-1 0-1-1-1-1v-1h-1z" class="B"></path><path d="M201 538l1-1v1h1c1 1 1 1 0 2v1c-1 1-1 3-1 5h0l1 2h-1l-1-1c0-1 1-3 0-4-1 0-1 0-1 1l-1-3 1-1c0-1 1-1 1-2z" class="P"></path><path d="M207 543h0v-2h0v-1c1 1 2 2 4 2h0l1 4-1 1h-2l-1-1h-1 0 0v-1-2z" class="F"></path><path d="M208 546l1-1v-1h1c1 1 0 2 1 3h-2l-1-1z" class="C"></path><path d="M201 531c1 1 1 2 1 2 1 1 1 2 1 4l-1 1v-1l-1 1c0 1-1 1-1 2l-1 1h0v-1-1l-1-1-1-1c0-1 0-3 1-4h1c1 0 1 1 2 1v-3z" class="I"></path><path d="M198 538c1 0 1-1 1-2 1 0 1 1 2 2 0 1-1 1-1 2l-1 1h0v-1-1l-1-1z" class="C"></path><path d="M208 524l1 1c1 2 2 4 3 5 0 1 1 2 2 2 1 1 1 2 2 2 1 1 1 2 2 2-1 1-2 2-2 4l-1-1-1-1h-1v2l-1-1-2-2h0c-1-1-1-3-1-4-1 0-1 0-1-1l1-1v-4l-1-3z" class="i"></path><path d="M214 532c1 1 1 2 2 2 1 1 1 2 2 2-1 1-2 2-2 4l-1-1c0-3 0-4-2-6l1-1z" class="B"></path><path d="M208 524l1 1c1 2 2 4 3 5 0 1 1 2 2 2l-1 1c0-1-1-2-2-2h-1l1 1v2h-1 0l-1-3v-4l-1-3z" class="e"></path><defs><linearGradient id="I" x1="203.891" y1="609.323" x2="181.856" y2="586.19" xlink:href="#B"><stop offset="0" stop-color="#070606"></stop><stop offset="1" stop-color="#252526"></stop></linearGradient></defs><path fill="url(#I)" d="M180 589c0-1 1-2 2-3v1c0 1 0 0 1 1v3h0v2c1 1 1 0 1 1h0 1c0-1 1-1 1-2 1 0 0 0 1-1h1v2l1 3h1l-1-2v-1c1-2 1-3 2-5 1 1 3 3 3 4l3 4v-2h-1v-2l-1-1 1-1-1-1h1v-2h1c1 1 1 2 2 3h0c0 2 0 2 1 3l1-1c1 1 1 2 2 2v1c1 1 1 1 2 1h0 1l-1-2h0l1 1c1-1 1-2 1-2l2 2v-1h0l2-2 1 1h0v1l1 1s1 0 1 1h2c2 1 3 2 5 3-2 0-5-1-6 0l3 2v1h0c-2 0-2-1-3-1v1 1h2 0c0 1-1 2-1 2-1 0-1 0-2 1-2-2-3-3-4-5 0 1-1 1 0 1 0 1 1 1 0 2h0c-1 0-1 1-2 1h-1l1 1-1 1h0c-1-1-1-1-2-1v1 1l-1-2-2 1h-1-1c-1 0-1-1-2-2v-1h-1v2h-1v-1h-1v2c-1 0-1 0-2-1v2h-1c0-1 0-1-1-2v2h-1v-1h-1 0 0c0 1 0 1-1 1h0 0c-1-1 0-1-1-1v-1c-1 1 0 2-1 3-1 0-1-1-1-1v-1h0l-1-1c-1 1-1 1-1 2h0l-1-1-1 1h-1c0-1-1-1-1-2 0-2 0-2-1-4l-1 1h-1c-1 1-1 1-1 3v1h0l-1 1h-2v-1-2c4-4 4-9 4-14l1-3h0 2c0 1 0 2 1 2v1-2z"></path><path d="M177 588h2c0 1 0 2 1 2v1 5c-1-1-1-2-1-3v-2c-1-1-1 0-2 0v-3z" class="V"></path><path d="M180 589c2 3 1 6 1 9l1 1c-1 1-1 2-2 2h0v-5-5-2z" class="o"></path><path d="M195 589h1v-2h1c1 1 1 2 2 3h0c0 2 0 2 1 3h0c0 1 0 1 1 2v1 1 1l-2-2v1l-1 1c0-1-1-2-1-3v-1h-1v-2l-1-1 1-1-1-1z" class="e"></path><path d="M180 589c0-1 1-2 2-3v1c0 1 0 0 1 1v3h0v2l1 1v2l1 1-1 1v4l-1-1v-2h-1l-1-1c0-3 1-6-1-9z" class="S"></path><path d="M181 598c1-3 0-6 0-9h1v2 1c1 2 0 5 1 7h-1l-1-1z" class="R"></path><path d="M182 592l1-1v2l1 1v2l1 1-1 1v4l-1-1v-2c-1-2 0-5-1-7z" class="q"></path><path d="M183 593c1 1 1 0 1 1h0 1c0-1 1-1 1-2 1 0 0 0 1-1h1v2l1 3c0 2 0 4-1 5h-1-2l-1 1v-4l1-1-1-1v-2l-1-1z" class="m"></path><path d="M185 601c0-1 0-1 1-2 0-1 0-2 1-3v3 1 1h-2z" class="E"></path><path d="M188 593l1 3c0 2 0 4-1 5h-1v-1c0-3 0-5 1-7z" class="Q"></path><path d="M190 596l-1-2v-1c1-2 1-3 2-5 1 1 3 3 3 4l3 4v3 1c-1-1-2-1-2-2h0c-1 1-1 1-1 2h0l-1-1h0-1v1l-1 1h0-1l-1 1-1-1c1-1 1-3 1-5h1z" class="C"></path><path d="M190 596v2c0 1 0 1 1 2h1l-1 1h0-1l-1 1-1-1c1-1 1-3 1-5h1z" class="V"></path><path d="M194 592l3 4v3 1c-1-1-2-1-2-2 0-2-2-4-1-6z" class="X"></path><path d="M177 603l1-1c1 2 1 2 1 4 0 1 1 1 1 2h1l1-1 1 1h0c0-1 0-1 1-2l1 1h0v1s0 1 1 1c1-1 0-2 1-3v1c1 0 0 0 1 1h0 0c1 0 1 0 1-1h0 0 1v1h1v-2c1 1 1 1 1 2h1v-2c1 1 1 1 2 1v-2h1v1h1v-2h1v1c1 1 1 2 2 2h1 1c1 1 2 1 2 2l1 3c0 3 1 6 3 8v1l1-1v1h0v2l1 1h-1v2c-1 0-1 0-1 1l-3 6c-2 6-4 12-9 16-2 2-4 3-6 5-2 1-4 3-6 5-3 1-7 3-10 2h-1c-2-1-5-2-7-3 0 1-1 1-1 1-3 2-6 3-9 3-4 1-9-1-12-3 0-1-2 0-3 0-2 0-5 0-7-1h0c-2-1-3-1-5-2-3-3-6-8-7-12 0-4 0-9 3-13s8-7 13-7c1 0 1 1 2 2h1c1 0 2 0 3-1 2-1 3-1 5-1h0c1-1 2-1 4-2h3v1c1 0 2-2 3-3 0-1 1-2 2-3 1 0 2-1 2-1l5-5 3-3c0-1 0-1 1-1v1h2l1-1h0v-1c0-2 0-2 1-3h1z" class="c"></path><path d="M161 639l1-1c1-1 2 0 3 1 0 1 0 1-1 1v-1c-1 0-1 1-1 1l-2-1z" class="D"></path><path d="M186 626c1 0 1-1 2-1s1 1 1 1h1c-1 0-1 0-1 1h-1v2c-1-1-1-2-2-3h0z" class="B"></path><path d="M159 629h3v1c-2 0-4-1-6 0l1 1c-1 1-1 1-2 1 0-1 0-1-1-1l2-1c1-1 2-1 3-1z" class="S"></path><path d="M176 629c1-1 1-2 3-2v2l-3 3-1-1c0-1 0-1 1-2z" class="K"></path><path d="M172 629l2 1h-1v3h-1 0l-1-1h-3-1l1-1h3 0l1-2z" class="Y"></path><path d="M172 629v-1h2 1v1h1c-1 1-1 1-1 2l1 1-3 1v-3h1l-2-1z" class="i"></path><path d="M191 626l1-1v-6c-1-1-1-2-1-3v-1c2 2 1 5 1 8h1c-1 1-1 2 0 3v1h0l-1 3h0c-1-1-1-3-1-4z" class="S"></path><path d="M198 623c0-1-1-3 0-4 1 1 2 4 2 6-1 1-1 2 0 4l-1 1v-1l-1-1v-5z" class="b"></path><path d="M179 617v1c0 2 1 4 0 6h-1v-1c-1 0-1 0-2 1h0-1 0c1-2 3-4 4-7z" class="Z"></path><path d="M201 625c-1-3-2-7-2-10 1 1 2 2 2 4 2 3 1 7 3 10h-1l-2-4z" class="Q"></path><path d="M157 639l2-1c0-1-1-1-1-2h0c1 0 1 1 2 1 0 1-1 2 0 3 1 0 1-1 1-1l2 1s0-1 1-1v1h0c-1 1-1 2-1 2 0 1 0 1-1 1l-1-2c-2-2-5-1-7-1v-1h3 0z" class="K"></path><path d="M167 632h1 3l1 1-1 1c-1 0-1 0-2-1h0c-1 1-2 1-2 2h0-2l-2 1-1-1c0-1 0-1 1-2 0 1 0 1 1 1 0 0 0-1 1-1h2v-1z" class="V"></path><path d="M202 613c2 4 3 8 3 13h0v3-1c-2-2-1-4-2-6-1-3-1-6-1-9z" class="f"></path><path d="M180 608h1l1-1 1 1v5h-2s-1 1-1 2h-1v-1s0-1-1-1v-1c1-1 1-1 1-2 1-1 1-1 1-2zm-45 29l1 2h-1v1c0 1-1 1-1 2-1 1-1 1-1 2v1h-2 0l-1-1v-1c1-1 1-2 2-3 1 0 0-2 1-2s1 0 2-1z" class="B"></path><path d="M195 625v-2h1 1 1v5l1 1-2 1-1 1s0 1-1 1h0v-1-6z" class="Q"></path><path d="M197 628h1l1 1-2 1-1 1 1-3z" class="D"></path><path d="M195 625v-2h1 1 1v5h-1c-1-1-1-3-1-4l-1 1z" class="v"></path><path d="M176 632l3-3h0v1c1 1 1 1 1 2v1c-1 1-1 2-2 3h-1v-1-1h-1v2h0c-1 0-1-1-2-1h-1c0-1 0-1-1-2h1l3-1z" class="R"></path><path d="M177 603l1-1c1 2 1 2 1 4 0 1 1 1 1 2s0 1-1 2c0 1 0 1-1 2h0l-1-1-1 1h0v-2c1-1 0-3 0-5 1-1 1-1 1-2z" class="C"></path><path d="M177 611c0-1 0-4 1-6v1c0 1 1 3 0 5v1l-1-1z" class="R"></path><path d="M193 623c0 2 1 6 2 8v1h0 0l-1-1-1 1v2 1c-1-1-1-1-2-1v1h-1v-3h-1v-2l-1-1h0v-2h1c0-1 0-1 1-1h0 1c0 1 0 3 1 4h0l1-3h0v-1c-1-1-1-2 0-3z" class="t"></path><path d="M190 626h0c0 1 1 6 0 6h-1v-2l-1-1h0v-2h1c0-1 0-1 1-1z" class="G"></path><path d="M153 622h3v1 1 1l2-1v2h-1-1c-2 0-2 0-3 2v1c-1 0-1 1-1 1v1-1h-2v-2c-2 0-3 0-5-1 1 0 0 0 1-1h1c1 0 2-1 2-2h0c1-1 2-1 4-2z" class="B"></path><path d="M200 607h1 1c1 1 2 1 2 2l1 3c0 3 1 6 3 8v1c0 1 0 2-1 3l-2 2h0c0-5-1-9-3-13 0-1-1-2-1-2 0-2 0-2-1-4z" class="w"></path><path d="M207 624c-2-3-3-8-3-12h1c0 3 1 6 3 8v1c0 1 0 2-1 3z" class="s"></path><path d="M137 646c-1 0-1 1-1 1l-1 1v-1-1-1c1 0 1-1 1-1 1-1 0-1 0-1l1-2c2 0 2-2 3-3 0-1 0-1-1-2h0c1-2 3 0 5-1 1 0 2-1 3-1l1 1v1l1 1c0 1 0 1 1 1v1l-4 1c-2 2-4 4-5 7 0 1-1 3-1 4 0-2 0-2-1-4h0v-1l-1-1-1 1z" class="P"></path><path d="M139 646h1c0-2-1-2-1-4h1c1-1 1-2 2-2h1l1-1c1 0 1 1 2 1-2 2-4 4-5 7 0 1-1 3-1 4 0-2 0-2-1-4h0v-1z" class="F"></path><path d="M156 623c1 0 2-2 3-3 0-1 1-2 2-3 1 0 2-1 2-1l5-5 3-3c0-1 0-1 1-1v1h2c0 1-1 2-1 3h-1v-2h-1c-1 2-2 3-3 4-2 1-3 3-5 5-1 0-1 1-2 1l1 1 1-1v1c0 1-2 3-3 4 1-1 3-3 5-3h1v-1c1-1 2-2 3-2h0l-2 4 3-3h1c-1 2-2 3-3 4 2-1 3-2 4-4l3-3c-1 4-3 6-6 9h0c-1 1-3 0-4 0l-1 1-1-1v-1h0l-2 2-1-1h-1c0 1-1 1-1 1v-2l-2 1v-1-1z" class="l"></path><path d="M165 625l2-2h0c1 1 1 1 1 2h1 0c-1 1-3 0-4 0z" class="m"></path><path d="M127 637c1-1 2-3 3-4 3-2 7-5 10-6h5c2 1 3 1 5 1v2h2v1l-1 1h1v1c-1 0-1 0-2-1-2-2-4-3-7-2-3 0-6 2-9 4 1 1 1 2 1 3-1 1-1 1-2 1s0 2-1 2-2 1-2 2l-1 1v-1l-1 2c0-1-1-3-1-3 0-1 1-2 1-3l-1-1z" class="T"></path><path d="M134 634c1 1 1 2 1 3-1 1-1 1-2 1s0 2-1 2-2 1-2 2l-1 1v-1c1-3 3-6 5-8z" class="G"></path><path d="M129 642v1l1-1c0-1 1-2 2-2-1 1-1 2-2 3v1l1 1h0 2v1h-2v1h0c1 0 2 1 2 1v1h1v2c1-1 2-2 3-4v-1l1-1 1 1v1h0c1 2 1 2 1 4v4l1 1h0c-2 1-3 1-5 0h-1-1l-1-1s-1-1-2-1c-1-1-3-3-3-4v-6l1-2z" class="k"></path><path d="M139 647c1 2 1 2 1 4v4h0l-1-1v-2c-1 0-1 0-2-1v-1c0-2 1-2 2-3z" class="B"></path><path d="M131 653h1c1 0 1-1 2-2 1 1 1 1 0 1v3h0l1-2h1 0c0 1 0 1 1 2 0-1 1-1 1-1h1l1 1h0l1 1h0c-2 1-3 1-5 0h-1-1l-1-1s-1-1-2-1v-1h0z" class="f"></path><path d="M129 642v1l1-1c0-1 1-2 2-2-1 1-1 2-2 3v1c0 1 0 3 1 4h0v3l2-1h0c0 1-1 2-2 3h0v1c-1-1-3-3-3-4v-6l1-2z" class="s"></path><path d="M182 626h0c1-1 2-1 3-2v2h1 0c1 1 1 2 2 3h0l1 1v2h1v3h1v-1c1 0 1 0 2 1v-1-2l1-1 1 1h0c1 0 1-1 1-1l1-1 2-1v1 2 1 8c0-2-1-4-2-6h0c0 1 0 4-1 5l-2-2h0c0 2 1 2 0 4h-1-1c-1 0-1 0-1 1v1l-2-1c1-1 1-1 1-2h-1-3c-1-1-2-1-3-1h0-1l-3-1v-1l-1-1h2c1-1 0-2 1-3h0c1-1 1-1 1-2 0 1 0 1-1 1v-1h0v-2c1-1 1-2 1-4z" class="C"></path><path d="M197 630l2-1v1 2 1c-1 0 0 0-1 1h0c-1-1-1-3-1-4z" class="e"></path><path d="M188 629l1 1v2 3h-1c-1-2-1-3 0-6z" class="x"></path><path d="M182 626h0c1-1 2-1 3-2v2h1 0c-1 1-1 0-1 1s-1 2-2 3v1-1-3l-1-1z" class="V"></path><path d="M181 634v2l1 1c0-1 0-1 1-1 0-1 1 0 1-1 1 0 1 0 2-1h0c1 1 0 2 2 3h1c0 1 1 2 1 3v1h-1-3c-1-1-2-1-3-1h0-1l-3-1v-1l-1-1h2c1-1 0-2 1-3z" class="P"></path><path d="M209 620v1h0v2l1 1h-1v2c-1 0-1 0-1 1l-3 6c-2 6-4 12-9 16-2 2-4 3-6 5-2 1-4 3-6 5-3 1-7 3-10 2h-1c-2-1-5-2-7-3 0 1-1 1-1 1l-1-1s1-2 2-2l-1-1v-2c0-5 1-7 3-10 3-3 5-3 8-3l1-1h2l3 1h1 0c1 0 2 0 3 1h3 1c0 1 0 1-1 2l2 1v-1c0-1 0-1 1-1h1 1c1-2 0-2 0-4h0l2 2c1-1 1-4 1-5h0c1 2 2 4 2 6v-8-1-2l1-1c-1-2-1-3 0-4h1l2 4h1 1v-3l2-2c1-1 1-2 1-3l1-1z" class="T"></path><path d="M176 640l1-1h2l3 1-1 1h-2c-1 0-1 1-1 1-2 1-3 2-4 3-1 0-2 1-2 1-1 1-1 2-2 2h-1c0-1 1-1 1-2 2-3 4-4 6-5h1v-1h-1z" class="g"></path><path d="M200 625h1l2 4v4h0c-1-1-1-3-2-4 0 3 1 7 0 10-2-2 0-5-2-7v-2l1-1c-1-2-1-3 0-4z" class="E"></path><path d="M199 632c2 2 0 5 2 7l-2 2c-1 2-2 3-3 5s-4 5-7 5v-2h1c1 0 3-2 3-4 1-1 3-2 4-4s-1-4 0-6c1 2 2 4 2 6v-8-1z" class="Q"></path><path d="M191 644v-1c0-1 0-1 1-1h1 1c1-2 0-2 0-4h0l2 2c1-1 1-4 1-5h0c-1 2 1 4 0 6s-3 3-4 4c0 2-2 4-3 4h-1v2l-5 5c-1-1-1-2-1-3v1h1c0-2 2-3 3-4l2-2-1-2h1c-1 0-1 0-2-1l2-2 2 1z" class="r"></path><path d="M189 646h2c-1 1-2 1-2 2l-1-2h1z" class="F"></path><path d="M187 645l2-2 2 1h1c0 1-1 1-1 2h-2c-1 0-1 0-2-1z" class="B"></path><path d="M182 640h1 0c1 0 2 0 3 1h3 1c0 1 0 1-1 2l-2 2c1 1 1 1 2 1h-1l1 2-2 2c-1 1-3 2-3 4h-1v-1c0 1 0 2 1 3l-3 2c-1 0-4 1-5 0h-3-1v-1c-2-1-3-2-3-4v-5h1c1 0 1-1 2-2 0 0 1-1 2-1 1-1 2-2 4-3 0 0 0-1 1-1h2l1-1z" class="c"></path><path d="M182 640h1 0v1c0 1 1 1 2 1v1l-1-1-1 1c-1 0-2-1-3-1h-2s0-1 1-1h2l1-1z" class="B"></path><path d="M183 640c1 0 2 0 3 1h3l-1 1v1c-1 1-2 2-2 3-1-1-1-2-1-3v-1c-1 0-2 0-2-1v-1z" class="C"></path><path d="M169 648h1c1 0 1-1 2-2 0 0 1-1 2-1l-3 4h1c1 0 1 0 2-1h0l-1 1c1 1 1 1 1 2h0l-1 1c1 0 2 0 3 1 2-1 3-3 5-3v1c-2 1-4 2-6 4h0 1l-2 2h0l1-2h-1l-1 1-1-1v-1c-1-1-1-2-1-3h0 0c-1 0-1 1-2 2h0v-5z" class="p"></path><path d="M190 641c0 1 0 1-1 2l-2 2c1 1 1 1 2 1h-1l1 2-2 2c-1 1-3 2-3 4h-1v-1c-1 0-1 0-1 1l-1-1v-1c-1 0-4 2-4 3h-1-1 0c2-2 4-3 6-4v-1l1-1c1-1 3-2 4-3 0-1 1-2 2-3v-1l1-1h1z" class="C"></path><path d="M188 646l1 2-2 2v-1c0-1 0-2 1-3z" class="E"></path><path d="M190 641c0 1 0 1-1 2l-2 2c-1 2-3 4-5 4h0c1-1 3-2 4-3 0-1 1-2 2-3v-1l1-1h1z" class="Z"></path><path d="M169 653h0c1-1 1-2 2-2h0 0c0 1 0 2 1 3v1l1 1 1-1h1l-1 2h0l2-2h1c0-1 3-3 4-3v1l1 1c0-1 0-1 1-1 0 1 0 2 1 3l-3 2c-1 0-4 1-5 0h-3-1v-1c-2-1-3-2-3-4z" class="s"></path><path d="M176 655h1c0-1 3-3 4-3v1l1 1c-2 1-2 1-2 3-1 0-1-1-1-1h-2c-1 0 0 0-1-1z" class="K"></path><path d="M165 659c-3 2-6 3-9 3-4 1-9-1-12-3 0-1-2 0-3 0-2 0-5 0-7-1h0c-2-1-3-1-5-2-3-3-6-8-7-12 0-4 0-9 3-13s8-7 13-7c1 0 1 1 2 2h1c1 0 2 0 3-1 2-1 3-1 5-1 0 1-1 2-2 2h-1c-1 1 0 1-1 1h-5c-3 1-7 4-10 6-1 1-2 3-3 4l1 1c0 1-1 2-1 3 0 0 1 2 1 3v6c0 1 2 3 3 4 1 0 2 1 2 1l1 1h1 1c2 1 3 1 5 0h0l-1-1v-4c0-1 1-3 1-4 1-3 3-5 5-7l4-1c1-1 3-2 5-1h1 1v1h0-3v1c2 0 5-1 7 1l1 2c1 0 1 0 1-1 0 0 0-1 1-2v2h0c1 0 1 0 2 1h2c-2 3-3 5-3 10v2l1 1c-1 0-2 2-2 2l1 1z" class="M"></path><path d="M150 639c1-1 3-2 5-1h1 1v1h0-3v1h-3l-1 1c-1 0-2 1-3 2 0 1 1 1 1 1l1 1h0v1h-1c-1 1-1 1-1 3h-2 0l-1-1h0v-3c1-1 1-2 2-3 2-1 3-2 4-3-1 1-3 1-4 2-2 1-3 3-4 5l-1 1c1-3 3-5 5-7l4-1z" class="H"></path><path d="M144 648c1-2 1-3 3-5 0 1 1 1 1 1l1 1h0v1h-1c-1 1-1 1-1 3h-2 0l-1-1h0z" class="m"></path><path d="M140 626h1c1 0 2 0 3-1 2-1 3-1 5-1 0 1-1 2-2 2h-1c-1 1 0 1-1 1h-5c-3 1-7 4-10 6-1 1-2 3-3 4-1 2-1 4-2 5h0c0-4 2-7 4-10 2-2 6-5 8-6h3z" class="C"></path><path d="M149 645c1 2 0 2 1 3 1 0 1 0 2-1 0 0 1 0 1 1h1l1-1v1h0c-1 1-1 0-1 1 1 1 1 0 2 2v1h1s0 2-1 3h0v2c1 0 2-1 3-2l-1 2h1l1-1v1 1 1h0c-3 2-6 2-9 1 0-1 1-1 0-3h0l-2 2-1-1h0c0-1 1-2 1-2l-1-1c0 1-1 1-1 1-2-1-3-2-3-4-1-1-1-3 0-4l1 1h0 2c0-2 0-2 1-3h1v-1z" class="Q"></path><path d="M156 657c1 0 2-1 3-2l-1 2h1l1-1v1 1c-1 0-1 1-2 1h-2v-1-1z" class="b"></path><path d="M149 645c1 2 0 2 1 3 1 0 1 0 2-1 0 0 1 0 1 1h1c-1 0-2 0-2 1v2l-2 1c-1 0 0-2 0-3h0l-2 2c0 1-1 1-1 2h-1l1-2h0-2 0l2-2c0-2 0-2 1-3h1v-1z" class="S"></path><path d="M154 648l1-1v1h0c-1 1-1 0-1 1 1 1 1 0 2 2v1h1s0 2-1 3h0 0l-3 4c0-3 2-4 2-7h-1c0 1-1 3-2 4 0-1 1-2 0-3-1 0 0 0-1 1 0 1-1 1-2 2 0-2 2-3 2-4 1 0 1-1 1-1v-2c0-1 1-1 2-1z" class="D"></path><path d="M151 640h3c2 0 5-1 7 1l1 2c1 0 1 0 1-1 0 0 0-1 1-2v2h0c1 0 1 0 2 1h2c-2 3-3 5-3 10v2l1 1c-1 0-2 2-2 2l-4 1v-1-1-1l-1 1h-1l1-2c-1 1-2 2-3 2v-2h0c1-1 1-3 1-3h-1v-1c-1-2-1-1-2-2 0-1 0 0 1-1h0v-1l-1 1h-1c0-1-1-1-1-1-1 1-1 1-2 1-1-1 0-1-1-3h0l-1-1s-1 0-1-1c1-1 2-2 3-2l1-1z" class="c"></path><path d="M147 643c1-1 2-2 3-2 0 1 0 2-1 4h0l-1-1s-1 0-1-1z" class="G"></path><path d="M156 651h0c1-1 1-1 2-1v1l1 1c0 1 1 1 0 2v1c-1 1-2 2-3 2v-2h0c1-1 1-3 1-3h-1v-1z" class="E"></path><path d="M158 651l1 1c-1 0-1 1-2 1v-1l1-1z" class="R"></path><path d="M160 656v-1h2l1-1 2 2v-1l1 1c-1 0-2 2-2 2l-4 1v-1-1-1z" class="t"></path><path d="M151 640h3c2 0 5-1 7 1l1 2c1 0 1 0 1-1 0 0 0-1 1-2v2h0c1 0 1 0 2 1h2c-2 3-3 5-3 10-1-2 0-3-1-4s-1-1-1-2v-2h-2c0-1 0-1-1-1v-2h-1-1c-1 0-1 0-2-1h-1s-1 0-1 1h-1c-1 0-1-1-2-2z" class="B"></path><path d="M167 189c3 2 4 5 5 8 1 2 3 3 4 5l4 9c1 2 3 5 4 7s3 5 3 8h1l5 7 1 2h2v1c2 0 1 0 3-1v1h0 1v-1-1l1-1h0 0c1-1 2-1 3-1l3-1v2 1h2 1 0 1 1c1 0 2-1 2 0h2 1v1c1 0 1-1 2 0h2 0 1v1c1 0 1 0 1-2l1 1h1s0-1 1-1c1 1 0 1 1 1v-1l1 1h1 1l6-4v2 1 1c-1 1-1 2-1 2v1l1-1h1v2h1 0c0 1 0 2 1 3l-1 1c-1 2 1 4-2 5v1 3 1h-1l2 2h0l1-1v1l1-1v3 6l-1 6v1h0v2l2-1c1-1 1-2 2-3h2c0 1-2 4-2 5 0 0 1 0 0 1v3l-1 1-2 1h0c0 1 0 2-1 2l-1-1c0 1 0 2-1 2h0 0c-1 1-3 2-4 3-1-1-1-2-1-3v-3h-1l-1 2v1c-1 1-2 1-3 1v-1c-1 2-2 2-3 3h-1l1-1c0-1 1-2 1-2v-1c-1 1-2 1-3 0v-2h-1l-1 4v-2c-1-1-1-2-1-3 0 1 0 2-1 3h0v-5-1h0l-1 2-1-1c0 1 0 2-1 4h-1l-1 1h0v4h-2-1c-1-1-1-2-3-2h-1s-1-1-1-2l-1-1h-1l1-1h1v-2l-1-1c-1 2-1 3-3 4 0 2-1 3-1 5h0l-2-2-1-2-1-1c-3-2-5-4-8-5-1 0-2-1-3-1h1v-3c0-3 0-7-1-10h-1c-1-1-2-4-2-6h0c-1-1-1-2-2-4 0-1-1-3-2-4 0 0 0 1-1 2h0v2h-1l-1-1v1-1c-1-1-1-1-1-2v-1c-1 0-1-1-1-1h-1v2h-2-1v-1l-1-1v1h-1s-1 0-1-1c-1 0-1-1-2-2v-1c-1 0-1 0-1-1l-2-2v-1h0c-1-1-2-2-4-3 0-1 1-1 1-2h2 0c1 0 1 0 2-1l-1-1c-2 1-3 0-5-1-4 0-9-1-12 1h-1v3c1 1 1 1 1 2v1h-2c0 1 1 2 0 3h0c0-4-1-8-1-11v-2-4h0c0-1 0-1-1-2l-1 1v-1c0-3 0-7 2-9l1-1h-1-1l1-2c0-1 0-1 1-1l3-2h-3c0-1 2-1 2-2h-1c0-1 1-2 2-3 0 0-1 0-1-1l1-1 1-2c1 1 3 0 4 1v-1c1-1 3-2 4-2 1-1 2-1 4-1h0 2c1 0 0 0 1 1v-2l1 1c1 1 4 2 5 3v-2l-1-1c0-1-1-2-2-2v-3l1 1s0 1 1 1h0 2 0v-1-2z" class="P"></path><path d="M209 268h0c1 1 1 1 1 2h-1v-2z" class="S"></path><path d="M207 277v-2c0-1 1-1 1-1 1 1 1 2 0 4h0l-1-1z" class="F"></path><path d="M212 276c1 2 1 3 1 4l-1 1-1-1c0-1 0-2 1-4z" class="h"></path><path d="M207 253h1c1 1 1 2 1 3h-1c-1-1-1-2-1-3z" class="I"></path><path d="M193 247v3h-1 0c-1-1-1-2-1-3h1 1z" class="D"></path><path d="M201 247h1c0 1-1 3 0 4l-1 2c-1-1-1-1-1-2s0-2 1-4z" class="e"></path><path d="M199 252c-1-1-2-1-2-2h1 1v-1c0-1 1-1 1-2h1c-1 2-1 3-1 4h-1v1z" class="B"></path><path d="M194 254v-1-1l1-1 1 3v2c-1 1-1 1-1 3h0c0-2-1-4-1-5z" class="Q"></path><path d="M212 262c0 1 1 1 0 2 0 2 1 3 0 4h0c-1-1-1-2-2-2v-1h0c1-1 2-2 2-3z" class="D"></path><path d="M202 251h0c0 2-1 3-1 5h-1l-1-4v-1h1c0 1 0 1 1 2l1-2z" class="c"></path><path d="M205 278l-2-2v-1l3 1v1c0 1 0 2 1 2l-1 1h-1v-2z" class="K"></path><path d="M221 276c0 1 0 2-1 3l-1 4v-2c-1-1-1-2 0-4 0 0 1 0 1-1h1z" class="r"></path><path d="M193 233l1 2h2v1 1l-1-1h0c-1 1-2 0-3 0h0v-2c0-1 0-1 1-1z" class="F"></path><path d="M195 259h0v1 5l-2-2h-1-1c0-1-1-1-1-2h1l1 1h0l2-1h0l1-2z" class="E"></path><path d="M172 226h0c1 0 1 1 1 1 1 1 2 1 2 1 1 1 0 1 0 2v1l-5-4h1c0-1 1-1 1-1z" class="m"></path><path d="M212 276l1-1h0c1 1 1 1 1 0l1 1c0 1 0 2-1 4h-1c0-1 0-2-1-4z" class="B"></path><path d="M171 221l1 1h1c0 1 1 2 1 3h-1c-1 0-1-1-2-1 0 1 1 1 1 2 0 0-1 0-1 1h-1-1l-1-2c1-1 1-1 1-2h2 1l-1-1v-1z" class="Y"></path><path d="M225 239c1 0 1 1 1 1l1 1v3c-1 1-2 1-4 2l-1-1c0-1 0-2 1-2h0 1c0-1 0-2 1-3v-1z" class="c"></path><path d="M223 270c-1-1-1-2-2-2 0-1-1-2-2-3v-3l1 1h1 1c0 1 0 1 1 2 0 1 0 2 1 3v1l-1 1z" class="G"></path><path d="M236 234v1c-1 1-1 2-1 2v1c1 1 1 1 1 2h-1v-1l-1 1c-1 0-1 1-1 2l-2-2 1-1h0c1-1 1-1 0-1 0-1 0-1-1-1v-1c1 0 1-1 2-1v2h1v-1c1-1 1-1 1-2h1z" class="B"></path><path d="M230 235l6-4v2 1h-1c0 1 0 1-1 2v1h-1v-2c-1 0-1 1-2 1-1 1-2 1-3 1l2-2z" class="J"></path><path d="M187 226h1l5 7c-1 0-1 0-1 1v2h0c-2-1-3-3-3-5l-1-2c-1-1-1-2-1-3z" class="g"></path><path d="M210 265h0-2-1v-3l-1-1h1c1 0 3-1 4 0l1 1c0 1-1 2-2 3h0z" class="G"></path><path d="M211 261l1 1c0 1-1 2-2 3v-2c0-1 0-1 1-2z" class="l"></path><path d="M206 250c2 1 3 1 5 1v2l1 1 1-1v2h0v1h-1 0-3c0-1 0-2-1-3h-1c0-1 0 0 1-1h0 1c-1-1-2-1-2-1l-1-1z" class="S"></path><path d="M212 256c-1 0-1-1-1-1-1 0-2-1-2-2l1-1v1h1l1 1 1-1v2h0v1h-1 0z" class="I"></path><path d="M221 252c1 1 1 1 1 2 1 1 1 3 2 4v2h-1c0-1-2-1-3-2 0-2-1-3-1-4s1-1 2-2z" class="K"></path><path d="M220 258c1-1 1-2 1-2 1 0 1 0 1 1l1 1v1l1 1h-1c0-1-2-1-3-2z" class="D"></path><path d="M206 250c0-1-1-2-1-2v-1c1-1 1-1 2-1s3 0 4 1h-1v2c0 1 1 1 1 2-2 0-3 0-5-1z" class="G"></path><path d="M193 247c-1-3-3-6-3-9h1v2c2 3 4 7 4 11l-1 1v1 1l-1-4v-3z" class="b"></path><path d="M231 236v1c1 0 1 0 1 1 1 0 1 0 0 1h0l-1 1-1 3-1-1h0 0l-2-1h0l-1-1c1-1 1-2 2-3 1 0 2 0 3-1z" class="D"></path><path d="M227 241h1l1-2h1v2c0 1-1 1-1 1h0 0l-2-1z" class="B"></path><path d="M215 260c1 0 1 1 1 1h1v-1h1v9h-1l-2-2v1l-1-1c0-2 0-4 1-7z" class="D"></path><path d="M206 277h1l1 1h0 1 1c0 2 0 4-1 6v1c-1-1-1-2-3-2h-1s-1-1-1-2l-1-1c0-1 1-1 2-2v2h1l1-1c-1 0-1-1-1-2z" class="F"></path><path d="M203 280c0-1 1-1 2-2v2h1l1-1c0 1 0 2 1 3-2 0-1 0-2-1h-2l-1-1z" class="I"></path><path d="M206 277h1l1 1h0 1v2 2l-1 1v-1c-1-1-1-2-1-3-1 0-1-1-1-2z" class="C"></path><path d="M187 246l1-1c1 1 1 2 1 3 1 1 1 2 0 3 1 0 1 0 1 1s1 1 1 1l-1 1c1 1 1 1 1 2v3h1c0 1 0 1-1 2h-1c-1-1-1-2-1-4 0-3-1-4-3-6h1 1v-1-2l-1-2z" class="D"></path><path d="M211 247h0c2 2 2 4 3 6h0 1l1-1 1 1c1 1 1 2 1 4l-1 1v-1h-2c-1-1-1-1-1-2h-1 0 0v-2l-1 1-1-1v-2c0-1-1-1-1-2v-2h1z" class="C"></path><path d="M211 247h0c1 1 1 3 1 4s1 1 1 2l-1 1-1-1v-2c0-1-1-1-1-2v-2h1z" class="k"></path><path d="M203 261c1-2 0-5 2-7v1c-1 8-1 17-5 25 0-1-1-1-1-1 2-6 4-12 4-18z" class="O"></path><path d="M238 239h0c0 1 0 2 1 3l-1 1c-1 2 1 4-2 5v1c-1-1-1-1-2-1-2 0-2 1-3 1v2 1c1 1 1 1 1 2h0-1c0-1-1-1-1-1h-1c-1 0-1 0-1-1v-1l2 1h0c0-2-1-4-2-5h0v-1c1 0 1 0 2 1l3-1h0c1 0 1 1 2 1s1 1 2 0c-1-1-1-1-1-2h-1c0-1 0-2 1-2h1l1-1v-3z" class="C"></path><path d="M196 254c2 2 3 4 3 7 0 2-1 3-1 5h0l-1 1h-1 0l-1 1v-1-2-5-1c0-2 0-2 1-3v-2z" class="D"></path><path d="M196 256v3c1 2 0 5 0 8h0l-1 1v-1-2-5-1c0-2 0-2 1-3z" class="Q"></path><path d="M196 256v3 5h0c-1-1-1-3-1-4v-1c0-2 0-2 1-3z" class="b"></path><path d="M224 235h1s0-1 1-1c1 1 0 1 1 1v-1l1 1h1 1l-2 2c-1 1-1 2-2 3 0 0 0-1-1-1v1h-2l-1 1v-2h-1l-1 1c0 1 0 2-1 3v-1c0-3 0-5 2-7h0 1v1c1 0 1 0 1-2l1 1z" class="g"></path><path d="M224 235h1s0-1 1-1c1 1 0 1 1 1v-1l1 1h1 1l-2 2c-1 1-1 2-2 3 0 0 0-1-1-1v-1c1-1 1-1 1-2l-1-1c0 1-1 1-1 2v1h-1l1-3zm0 25c1 0 1 0 2-1 1 1 1 1 2 1h1v1c0 1 0 2 1 3v3l-3 1v-1h-1l-1 2v1h-1v-1-1c-1-1-1-2-1-3-1-1-1-1-1-2 1 0 1 0 1-1 1 1 2 1 2 2h1c1-1 1-2 1-3l-3-1z" class="E"></path><path d="M224 268v-1h1 1l-1 2v1h-1v-1-1z" class="P"></path><g class="K"><path d="M230 264v3l-3 1v-1-1h2l1-2h0z"></path><path d="M224 260c1 0 1 0 2-1 1 1 1 1 2 1h1v1c0 1 0 2 1 3h0-2v1c-1 0-2 1-3 1h-1v-1l1-1h1c1-1 1-2 1-3l-3-1z"></path></g><path d="M229 261c0 1 0 2 1 3h0-2v-1c0-1 0-1 1-2z" class="D"></path><path d="M166 224c1 0 2 0 2 1l1 2h1l5 4c1 2 2 3 4 5 2 4 3 7 3 12h0v-1c-1-4-3-8-5-12l-3-3-2-1c-1-1-3-2-4-1h-1c-1-1-2-1-3-2h-1v-1h1 0l1-1h1v-1-1z" class="o"></path><path d="M166 224c1 0 2 0 2 1l1 2h-1c-1 0-1-1-2-2v-1z" class="Q"></path><path d="M226 267h1v1l3-1 1 5-1 7-1 2v-2l-1-1v-1h-1c0-2 0-2-1-3h-1c0-1 0-1-1-2h0-1s0-1-1-2h1l1-1v1h1v-1l1-2z" class="C"></path><path d="M226 267h1v1l1 1h0c1 0 1 1 1 1v2h0-1l-1-2h-1l-1-1 1-2z" class="h"></path><path d="M230 253s1 0 1 1h1 0c0-1 0-1-1-2v-1-2c1 0 1-1 3-1 1 0 1 0 2 1v3 1h-1v2s1 1 1 2-1 1-1 2 0 1-1 2h-1l-1-1v1h-1v-1l1-1v-2-1-1h-2v-2z" class="e"></path><path d="M212 234c1 0 2-1 2 0h2 1v1c1 0 1-1 2 0l-1 3c0 2 0 3-1 4-1 0-1-1-2-1h-2v-1c-1 0-1 1-2 1v-2l-1-1c1-1 1-3 1-4h1z" class="C"></path><path d="M212 234c1 0 2-1 2 0h2v1c-1 0-1 1-1 2h0-1v-2-1h0l-2 1v-1zm11 14l1-1v1c0 1 0 1 1 1h1l1 3v2c1 2 1 4 2 6h-1c-1 0-1 0-2-1-1 1-1 1-2 1h-1 1v-2c-1-1-1-3-2-4 0-1 0-1-1-2-1 0-1 0-1-1h-1v-1h0v-1c1 1 1 1 1 2h1v-3h1 1z" class="B"></path><path d="M223 248l1-1v1c0 1 0 1 1 1 0 1-1 3-1 3h-1v-4z" class="m"></path><path d="M204 232l3-1v2 1h2 1 0 1c0 1 0 3-1 4v1c0 1 0 1-1 2h-2c0 1 1 2 0 3l-1 1v-2h-1l-1 1-1-1v-3c1-1 1-3 1-3v-5z" class="E"></path><path d="M204 232l3-1v2 1h2 1 0 1c0 1 0 3-1 4h-2l-1 1h0v-1c-1-1-1-1-1-2h0-1v1h-1v-5z" class="D"></path><path d="M210 234h1c0 1 0 3-1 4h-2l-1 1h0v-1-3l2 1c1-1 1-1 1-2z" class="K"></path><path d="M224 272h0c1 1 1 1 1 2h1c1 1 1 1 1 3h1v1l1 1v2 1c-1 1-2 1-3 1v-1c-1 2-2 2-3 3h-1l1-1c0-1 1-2 1-2v-1c-1 1-2 1-3 0v-2h-1c1-1 1-2 1-3h0c0-1 1-1 2-2v-2h1z" class="i"></path><path d="M224 272v1h0v1c0 1 0 3-1 4h0v-4h0v-2h1z" class="g"></path><path d="M227 277c-1 0-1 1-2 2h0c1-2-1-4 1-5 1 1 1 1 1 3z" class="K"></path><path d="M223 274h0c0 2 0 4-1 5h-1-1c1-1 1-2 1-3h0c0-1 1-1 2-2z" class="E"></path><path d="M228 278l1 1v2 1c-1 1-2 1-3 1v-1h0s0-1 1-1c1-1 1-2 1-3z" class="P"></path><path d="M227 252l3 8h1v1h1v-1l1 1h1l1 1h1 0c-1 1-2 2-2 3l1 1c0 1-1 2 0 2v3c-1 3-2 4-2 7h0l-1 3-1 1v-3h-1l1-7-1-5v-3c-1-1-1-2-1-3v-1c-1-2-1-4-2-6v-2z" class="h"></path><path d="M234 265l1 1c0 1-1 2 0 2l-2 2-1-1c0-2 0-2 1-3l1-1z" class="P"></path><path d="M233 261h1l1 1h1 0c-1 1-2 2-2 3l-1 1-1-1c1-2 1-2 1-4z" class="B"></path><path d="M231 272v-2l1-1v12l-1 1v-3h-1l1-7z" class="J"></path><path d="M227 252l3 8c0 3 2 6 2 9l-1 1v2l-1-5v-3c-1-1-1-2-1-3v-1c-1-2-1-4-2-6v-2z" class="o"></path><path d="M199 270v-1l1 1h0c1-1 1-1 1-2v-6l2-1c0 6-2 12-4 18 0 0 1 0 1 1h0c0 2-1 3-1 5h0l-2-2-1-2-1-1v-1c-1-1-1-2-2-3 0-2 1-4 1-7l1-2v1l1-1h0 1l1-1 1 1v1 2z" class="C"></path><path d="M196 276l-1-1c0-1 2-2 2-2l2 1v1l-1 1h-2z" class="e"></path><path d="M199 279s1 0 1 1h0c0 2-1 3-1 5h0l-2-2h1c0-1 0-3 1-4z" class="J"></path><path d="M198 266l1 1v1 2 1l-1 1-1 1s-2 1-2 2l1 1v5l-1-1v-1c-1-1-1-2-2-3 0-2 1-4 1-7l1-2v1l1-1h0 1l1-1z" class="X"></path><path d="M194 269l1-2v1l1-1v1l-1 2h0l-1-1z" class="b"></path><path d="M198 266l1 1v1 2 1l-1 1c-1 0-1 1-3 1h0c1-2 1-3 1-5h0v-1h0 1l1-1z" class="G"></path><path d="M235 253l2 2h0l1-1v1l1-1v3 6l-1 6v1h0v2l2-1c1-1 1-2 2-3h2c0 1-2 4-2 5 0 0 1 0 0 1v3l-1 1-2 1h0c0 1 0 2-1 2l-1-1c0 1 0 2-1 2h0 0c-1 1-3 2-4 3-1-1-1-2-1-3l1-1 1-3h0c0-3 1-4 2-7v-3c-1 0 0-1 0-2l-1-1c0-1 1-2 2-3h0-1l-1-1c1-1 1-1 1-2s1-1 1-2-1-2-1-2v-2z" class="V"></path><path d="M238 271v-1h0v2 1l1 1h0v2h-1c0 1-1 2-1 3h-1v-4-1l1-1 1-2z" class="Z"></path><path d="M237 273l1 1c-1 1-1 1-2 1v-1l1-1z" class="X"></path><path d="M235 253l2 2h0l1-1v1l1-1v3 6l-1 6v1 1l-1 2-1 1v1 4c-1-2 0-6-1-8v-3c-1 0 0-1 0-2l-1-1c0-1 1-2 2-3h0-1l-1-1c1-1 1-1 1-2s1-1 1-2-1-2-1-2v-2z" class="v"></path><path d="M236 274v-5l1 3h0l1-1-1 2-1 1z" class="b"></path><path d="M238 255l1-1v3 6l-1 6v1 1l-1 1h0l-1-3 2-14z" class="Q"></path><path d="M187 246l1 2v2 1h-1-1c2 2 3 3 3 6 0 2 0 3 1 4 0 1 1 1 1 2h1 1l2 2v2l-1 2c0 3-1 5-1 7 1 1 1 2 2 3v1c-3-2-5-4-8-5v-4l-2-12c1-2-1-5-1-8l1 1v2h0c1-1 1-1 1-2l-1-2c-1-1-1-2 0-3h0c2 0 1 0 2-1z" class="c"></path><path d="M191 263h1 1c-1 1-1 1-1 3l-1-1v-2z" class="G"></path><path d="M189 270h1 0v2h2 0v-1-1h0c1 1 1 1 1 2l-1 2h0c1 1 1 1 1 2h0c1 1 1 2 2 3v1c-3-2-5-4-8-5v-4h1l1-1z" class="K"></path><path d="M188 271l1-1c1 1 1 2 1 4h-1-1v-1-1-1z" class="Z"></path><path d="M168 230c1-1 3 0 4 1l2 1 3 3c2 4 4 8 5 12v1h0l3 11 2 12v4c-1 0-2-1-3-1h1v-3c0-3 0-7-1-10h-1c-1-1-2-4-2-6h0c-1-1-1-2-2-4 0-1-1-3-2-4 0 0 0 1-1 2v-3l-1-1-1-1v-1c-1 0-1 0-2 1 0-1-1-1-1-2v-1h-1v-2s-1 0-1-1h0l-1-2v-1l2 2 1-1-1-1h0c-1-1-1-2-2-3-1 0-1 0-1-1l5 3-4-4z" class="E"></path><path d="M176 246c0-1 1-1 2-1 1 1 2 1 2 2v2 1c1 1 1 3 2 4l-1 1h0c-1-1-1-2-2-4 0-1-1-3-2-4 0 0 0 1-1 2v-3z" class="Y"></path><path d="M174 232l3 3-2 1c1 1 1 2 2 3v2h1v1c-1 0-1-1-2-2v-1c0-1-1-1-2-2h0l-2-4h-1c1 0 2 0 3-1h0z" class="e"></path><path d="M174 232l3 3-2 1h-1c0-2 0-2-2-3h0-1c1 0 2 0 3-1h0z" class="m"></path><path d="M168 230c1-1 3 0 4 1l2 1h0c-1 1-2 1-3 1h1l2 4h-1l-3-3v1h0c-1-1-1-2-2-3-1 0-1 0-1-1l5 3-4-4z" class="g"></path><path d="M182 248l3 11 2 12v4c-1 0-2-1-3-1h1v-3c0-3 0-7-1-10 0-4-2-9-2-13h0z" class="n"></path><path d="M162 228h0l1 1h2l1 1h0l1 1c0 1 0 1 1 1 1 1 1 2 2 3h0l1 1-1 1-2-2v1l1 2h0c0 1 1 1 1 1v2h1v1c0 1 1 1 1 2 1-1 1-1 2-1v1l1 1 1 1v3h0v2h-1l-1-1v1-1c-1-1-1-1-1-2v-1c-1 0-1-1-1-1h-1v2h-2-1v-1l-1-1v1h-1s-1 0-1-1c-1 0-1-1-2-2v-1c-1 0-1 0-1-1l-2-2v-1h0c-1-1-2-2-4-3 0-1 1-1 1-2h2 0c1 0 1 0 2-1l-1-1c-1-1-3-1-4-2h-1l1-1 6-1z" class="q"></path><path d="M160 239c1 1 2 2 3 2 0-1-2-3-2-4 1 2 3 3 5 4 0-1-3-4-3-4 2 0 3 1 4 3h1 0c0-1-1-2-2-3l-2-1v-1c2 1 3 2 5 3 0 1 1 1 1 1v2h1v1c0 1 1 1 1 2 1-1 1-1 2-1v1l1 1 1 1v3h0v2h-1l-1-1v1-1c-1-1-1-1-1-2v-1c-1 0-1-1-1-1h-1v2h-2-1v-1l-1-1v1h-1s-1 0-1-1c-1 0-1-1-2-2v-1c-1 0-1 0-1-1l-2-2v-1z" class="P"></path><path d="M167 189c3 2 4 5 5 8 1 2 3 3 4 5l4 9c1 2 3 5 4 7s3 5 3 8c0 1 0 2 1 3l1 2-1 3c-1 0-1 0-1-1h-1v1c1 1 0 1 0 2v2 4h0l-1-1v-1c-1 0-1 0-2 1v-3h-2c-1-1-1-2-2-3v-3h0l1 1v-1c0-1-2-3-3-4v-2c1-1 2 0 3-1 0-1-1-1-1-2h1c0 1 1 2 2 3 0 1 1 2 2 3 0-1 1-1 0-1 0-1-2-1-2-3v-2l-1-1c-1-2-1-3-2-5-1-3-3-6-4-9-2-1-3-4-3-6l-1 1c-1-1-2-3-3-4l-2-1v-2l-1-1c0-1-1-2-2-2v-3l1 1s0 1 1 1h0 2 0v-1-2z" class="j"></path><path d="M184 218c1 2 3 5 3 8 0 1 0 2 1 3l-6-8v-1-1-1h2z" class="S"></path><path d="M167 189c3 2 4 5 5 8 1 2 3 3 4 5l4 9c1 2 3 5 4 7h-2v1 1 1c-1-1-1-2-2-3v-1l-4-11-1 1 1 1h-1 0c-2-1-3-4-3-6l-1 1c-1-1-2-3-3-4l-2-1v-2l-1-1c0-1-1-2-2-2v-3l1 1s0 1 1 1h0 2 0v-1-2z" class="G"></path><path d="M169 195c2 1 4 4 4 6h1l-2-1h-1v1-1c-1-2-2-3-2-5z" class="D"></path><path d="M180 211c1 2 3 5 4 7h-2c0-1-1-2-1-3-1-1-1-2-2-3l1-1z" class="B"></path><path d="M167 192v-1l1-1c1 1 1 4 1 5h0c0 2 1 3 2 5h-1s-1-1-1-2l-2-1v-1c1-2 1-2 0-4h0z" class="h"></path><path d="M171 201v-1h1l2 1c0 2 0 2 1 3 0 1 1 2 1 2l-1 1 1 1h-1 0c-2-1-3-4-3-6l-1-1z" class="P"></path><path d="M165 195c0-1-1-2-2-2v-3l1 1s0 1 1 1h0 2c1 2 1 2 0 4v1l2 1c0 1 1 2 1 2h1v1l1 1-1 1c-1-1-2-3-3-4l-2-1v-2l-1-1z" class="I"></path><path d="M167 192c1 2 1 2 0 4l-2-4h2z" class="V"></path><path d="M166 203h2c1 0 1 1 2 1l1-1 1-1c0 2 1 5 3 6 1 3 3 6 4 9 1 2 1 3 2 5l1 1v2c0 2 2 2 2 3 1 0 0 0 0 1-1-1-2-2-2-3-1-1-2-2-2-3h-1c0 1 1 1 1 2-1 1-2 0-3 1-1 0-2-1-4-1h1c0-1-1-2-1-3h-1c0-1 0-2-1-2l1-1c-1-1-2-1-4-1h-4v-1c-1 0-2 1-3 1v-1h0c-2-1-5-1-7 0-1 0-3 1-5 1h0c2-2 3-2 5-3-1-1-2 0-3 0v-1s1 0 1-1c2-1 4-1 6-2h1 2v-2c1-1 2-1 3-2h1v-1c2 1 3 2 4 1l-1-1v-1h1v-1l-3-1z" class="t"></path><path d="M167 214c2 0 3 1 5 2h0-2c-1-1-2-1-4-1h0-2v-1h3z" class="J"></path><path d="M177 222v1 1 2c-1 0-2-1-4-1h1c0-1-1-2-1-3l3 1 1-1z" class="D"></path><path d="M166 216c3 0 5 1 7 3h0-1c-1-1-2-1-4-1h-4v-1-1h2z" class="J"></path><path d="M163 213l4 1h-3v1h2 0v1h-2v1c-1 0-2 1-3 1v-1l1-1h-1-1v-1s1 0 1-1c1 0 1 0 2-1z" class="O"></path><path d="M176 218h1c-1-2-3-3-4-5h1c0-1-2-2-3-3v-1h0c4 2 5 5 7 8h1c1 2 1 3 2 5-1 0-2-1-2-1l-3-3z" class="B"></path><path d="M177 222l-2-1v-3l-3-3h0c2 1 3 2 4 3l3 3s1 1 2 1l1 1v2c0 2 2 2 2 3 1 0 0 0 0 1-1-1-2-2-2-3-1-1-2-2-2-3h-1c0 1 1 1 1 2-1 1-2 0-3 1v-2-1-1z" class="R"></path><path d="M164 207h1l1 1c1 0 2 1 3 1s2 1 2 1v1c-2 0-3-1-5-1h0c1 1 3 2 5 2l3 3c-4-2-7-3-11-3v1c-1 1-1 1-2 1 0 1-1 1-1 1v1h1 1l-1 1h0c-2-1-5-1-7 0-1 0-3 1-5 1h0c2-2 3-2 5-3-1-1-2 0-3 0v-1s1 0 1-1c2-1 4-1 6-2h1 2v-2c1-1 2-1 3-2z" class="s"></path><path d="M146 216l1-1c1 0 2-1 3-1v1h0 1c1 0 2-1 3 0-2 1-3 1-5 3h0c2 0 4-1 5-1 2-1 5-1 7 0h0v1c1 0 2-1 3-1v1h4c2 0 3 0 4 1l-1 1c1 0 1 1 1 2l-1-1v1l1 1h-1-2c0 1 0 1-1 2 0-1-1-1-2-1v1 1h-1l-1 1h0-1v1h1c1 1 2 1 3 2h1l4 4-5-3-1-1h0l-1-1h-2l-1-1h0l-6 1-1 1h1c1 1 3 1 4 2-2 1-3 0-5-1-4 0-9-1-12 1h-1v3c1 1 1 1 1 2v1h-2c0 1 1 2 0 3h0c0-4-1-8-1-11v-2-4h0c0-1 0-1-1-2 0 0 1 0 1-1h0l-1-1c1 0 1-1 2-2h0v-1l1 1h1 0l3-2z" class="F"></path><path d="M150 222l2-1 1 1-1 1-1 1h1c0 1 0 1-1 1h0v1h-1-2 0l3-1v-1l-1-1-1 1-1-1c1-1 1-1 2-1z" class="t"></path><path d="M154 229h2l-1 1h1c1 1 3 1 4 2-2 1-3 0-5-1h-8c2-1 5-1 7-2z" class="Q"></path><path d="M142 227c1-1 2-2 3-2v1 1h3 0v1l-2 2h0l-2 1v-1h0v-1h-3v-1h1v-1z" class="R"></path><path d="M146 216l1-1c1 0 2-1 3-1v1h0 1c1 0 2-1 3 0-2 1-3 1-5 3h0c2 0 4-1 5-1s1 0 2 1h0c0 1 0 1-1 2h0-2l-1 1-2 1-1-1c-2 1-3 1-5 3h1 0 1l1-1 1 1v1c-1 0-2 0-3 1h0v-1c-1 0-2 1-3 2v-1c0-1 0-1 1-2h0l-1-1c0 1-1 1-1 1v1l-1-1c0-1 0-1-1-2 0 0 1 0 1-1h0l-1-1c1 0 1-1 2-2h0v-1l1 1h1 0l3-2z" class="B"></path><path d="M146 216c1 1 1 1 1 2-1 1-2 1-3 2h0c-1 1-1 1-2 1 0-1 1-2 1-3l3-2z" class="c"></path><path d="M139 222s1 0 1-1h0l-1-1c1 0 1-1 2-2h0v-1l1 1h1 0c0 1-1 2-1 3 1 0 1 0 2-1v1c-1 0-1 1-2 1s-1 1-1 1l-1 1c0-1 0-1-1-2z" class="Z"></path><path d="M154 217c2-1 5-1 7 0h0v1c1 0 2-1 3-1v1h4c2 0 3 0 4 1l-1 1c1 0 1 1 1 2l-1-1v1l1 1h-1-2c0 1 0 1-1 2 0-1-1-1-2-1v1 1h-1l-1 1h0-1v1h1c1 1 2 1 3 2h1l4 4-5-3-1-1h0l-1-1h-2l-1-1h0l-6 1h-2 0v-1h-3l-1-2h1v-1h0c1 0 1 0 1-1h-1l1-1 1-1-1-1 1-1h2 0c1-1 1-1 1-2h0c-1-1-1-1-2-1z" class="o"></path><path d="M164 221c1-1 1 0 2 0l1 1h1l-1 1h-3-1 0v1h-3-2 0v-1c1 0 3 0 4-1s1-1 2-1z" class="q"></path><path d="M164 221c1-1 1 0 2 0l1 1h-3v-1z" class="Q"></path><path d="M166 221c2-1 3-1 5 0h0v1l1 1h-1-2c0 1 0 1-1 2 0-1-1-1-2-1h-3v-1h0 1 3l1-1h-1l-1-1z" class="X"></path><path d="M155 220h1v1h1 1 0c-1 1-2 1-3 1h-1c2 1 2 1 4 1v1c-2 0-5 0-7 1 1 0 1 0 1-1h-1l1-1 1-1-1-1 1-1h2z" class="x"></path><path d="M154 217c2-1 5-1 7 0h0v1c1 0 2-1 3-1v1c-2 2-6 2-8 2h-1 0c1-1 1-1 1-2h0c-1-1-1-1-2-1z" class="G"></path><path d="M158 224h0 2 3 3v1 1h-1l-1 1h0-1v1h1c1 1 2 1 3 2h1l4 4-5-3-1-1h0l-1-1h-2l-1-1h0l-6 1h-2 0v-1h-3l-1-2h1v-1h0c2-1 5-1 7-1z" class="O"></path><path d="M158 224h2c2 1 3 1 4 2v1h-1c-1-1-3-2-4-2h0l-1-1z" class="L"></path><path d="M160 224h3 3v1 1h-1l-1 1h0v-1c-1-1-2-1-4-2z" class="O"></path><path d="M151 226c1 0 3 1 4 1 3 0 5 0 7 1l-6 1h-2 0v-1h-3l-1-2h1z" class="r"></path><path d="M157 195h0 2c1 0 0 0 1 1v-2l1 1c1 1 4 2 5 3l2 1c1 1 2 3 3 4l-1 1c-1 0-1-1-2-1h-2l3 1v1h-1v1l1 1c-1 1-2 0-4-1v1h-1c-1 1-2 1-3 2v2h-2-1c-2 1-4 1-6 2 0 1-1 1-1 1v1h-1 0v-1c-1 0-2 1-3 1l-1 1-3 2h0-1l-1-1v1h0c-1 1-1 2-2 2l1 1h0c0 1-1 1-1 1l-1 1v-1c0-3 0-7 2-9l1-1h-1-1l1-2c0-1 0-1 1-1l3-2h-3c0-1 2-1 2-2h-1c0-1 1-2 2-3 0 0-1 0-1-1l1-1 1-2c1 1 3 0 4 1v-1c1-1 3-2 4-2 1-1 2-1 4-1z" class="o"></path><path d="M149 199v-1c1-1 3-2 4-2 2 1 3 0 5 1h0-3c-1 1-3 1-3 2h0 0l-1 1h-2v-1z" class="i"></path><path d="M155 197c0 1 0 1 1 1s2 1 3 2c1 0 3 0 4 1l6 2c-3-3-5-3-8-5 1 0 3 0 4 1 1 0 1 1 2 1l1-1c1 1 2 3 3 4l-1 1c-1 0-1-1-2-1h-2s-3-2-4-2h-3v-1h-1 0v1c-1 1-1 1-2 1l1-1h-6c1 0 1-1 2-1s1 0 2-1h-3 0c0-1 2-1 3-2z" class="b"></path><path d="M156 202c1 0 1 0 2-1v-1h0 1v1h3c1 0 4 2 4 2l3 1v1h-1v1l1 1c-1 1-2 0-4-1-2 0-5 1-6 0l-1-1h-3 0c-1 0-1-1-1-1h-3-1 0l2-1 4-1z" class="p"></path><path d="M156 202c1 0 1 0 2-1v-1h0 1v1 1c1 1 2 1 3 1h-5l-1 1c-1 0-3 0-4-1l4-1z" class="f"></path><path d="M159 201h3c1 0 4 2 4 2l3 1v1h-1c-2-1-4-2-6-2-1 0-2 0-3-1v-1z" class="J"></path><path d="M155 205l3-1h1c2 0 4 1 6 1 1 0 2 0 3 1l1 1c-1 1-2 0-4-1-2 0-5 1-6 0l-1-1h-3z" class="q"></path><path d="M144 200l1-2c1 1 3 0 4 1v1h2l1-1h0 3c-1 1-1 1-2 1s-1 1-2 1h6l-1 1-4 1-2 1h0 1 3s0 1 1 1h-2c-2 1-3 2-4 2s-3 1-4 2l-1-1-1 1h-1-1l3-2h-3c0-1 2-1 2-2h-1c0-1 1-2 2-3 0 0-1 0-1-1l1-1z" class="D"></path><path d="M151 204h3s0 1 1 1h-2c-2 1-3 2-4 2s-3 1-4 2l-1-1-1 1h-1-1l3-2c1 0 2-1 4-2 1 0 2 0 3-1z" class="B"></path><path d="M155 205h3l1 1c1 1 4 0 6 0v1h-1c-1 1-2 1-3 2v2h-2-1c-2 1-4 1-6 2 0 1-1 1-1 1v1h-1 0v-1c-1 0-2 1-3 1l-1 1-3 2h0-1l-1-1v1h0c-1 1-1 2-2 2l1 1h0c0 1-1 1-1 1l-1 1v-1c0-3 0-7 2-9l1-1h-1-1l1-2c0-1 0-1 1-1h1 1l1-1 1 1c1-1 3-2 4-2s2-1 4-2h2 0z" class="L"></path><path d="M144 212l1-2h4l-1 1v1h-1 0c0 1-1 1-1 2-2 0-2-1-3 0v1c-1 0-2 1-3 1 1-1 3-2 4-4z" class="B"></path><path d="M143 209l1-1 1 1c1-1 3-2 4-2h1c1 1 3 0 4 1v1h-2l-1 1c-1 0-2 1-3 1l1-1h-4l-1 2h0c-1-1-2 0-2 0 0-1 1-2 2-3h-1z" class="F"></path><path d="M148 211c1 0 2-1 3-1 0 1 0 1 1 0 1 0 4 0 6 1-2 1-4 1-6 2 0 1-1 1-1 1v1h-1 0v-1c-1 0-2 1-3 1l-1 1-3 2h0-1l-1-1 3-2h-1v-1c1-1 1 0 3 0 0-1 1-1 1-2h0 1v-1z" class="D"></path><path d="M144 215h1c0 1-2 2-2 3h-1l-1-1 3-2z" class="S"></path><path d="M148 211c1 0 2-1 3-1 0 1 0 1 1 0 1 0 4 0 6 1-2 1-4 1-6 2 0 1-1 1-1 1v-1s-1 1-2 0c1 0 1-1 2-1h0v-1c-1 0-2 1-3 1h0v-1z" class="Z"></path><path d="M155 205h3l1 1c1 1 4 0 6 0v1h-1c-1 1-2 1-3 2v2h-2-1c-2-1-5-1-6-1-1 1-1 1-1 0l1-1h2v-1c-1-1-3 0-4-1h-1c1 0 2-1 4-2h2 0z" class="l"></path><path d="M155 205h3l1 1-2 1c-1-1-2-1-3-1l-1-1h2 0z" class="L"></path><path d="M155 209c1-1 3-1 4 0v-1h0l1-1h4c-1 1-2 1-3 2v2h-2-1c-2-1-5-1-6-1-1 1-1 1-1 0l1-1h2 1z" class="q"></path><path d="M154 209h1 2v1c-1 0-4 0-5-1h2z" class="s"></path><path d="M141 241c1-1 0-2 0-3h2v-1c0-1 0-1-1-2v-3h1c3-2 8-1 12-1 2 1 3 2 5 1l1 1c-1 1-1 1-2 1h0-2c0 1-1 1-1 2 2 1 3 2 4 3h0v1l2 2c0 1 0 1 1 1v1c1 1 1 2 2 2 0 1 1 1 1 1h1v-1l1 1v1h1 2v-2h1s0 1 1 1v1c0 1 0 1 1 2v1-1l1 1h1v-2h0c1-1 1-2 1-2 1 1 2 3 2 4 1 2 1 3 2 4h0c0 2 1 5 2 6h1c1 3 1 7 1 10v3h-1c1 0 2 1 3 1 3 1 5 3 8 5l1 1 1 2 2 2h0c0-2 1-3 1-5 2-1 2-2 3-4l1 1v2h-1l-1 1h1l1 1c0 1 1 2 1 2h1c2 0 2 1 3 2h1 2v-4h0l1-1h1c1-2 1-3 1-4l1 1 1-2h0v1 5h0c1-1 1-2 1-3 0 1 0 2 1 3v2l1-4h1v2c1 1 2 1 3 0v1s-1 1-1 2l-1 1h1c1-1 2-1 3-3v1c1 0 2 0 3-1v-1l1-2h1v3c0 1 0 2 1 3 1-1 3-2 4-3h0 0c1 0 1-1 1-2l1 1c1 0 1-1 1-2h0l2-1v1c1 1 1 2 2 2s3-1 4 0h1v-1c1 0 1 1 2 1h0 1c0 1 1 1 1 1h1c2 2 5 1 5 4l3-2 1-1 1 1 1 1c1 0 2-1 3-1v1 1h0c1 0 0 0 1 1l1-1h1c0 1 0 1-1 2h-1l1 1v1h-2l-1 1h3c1 0 1 0 2-1v1c1 0 1 0 2 1h4c1 0 1 0 2-1v1c-1 1-3 1-4 1s-1 0-2 1v-1h-1l-1 1h3 0 0 2c0 1 0 1-1 2h-1v1l-2 1-1 1c1 0 1 1 2 1v2h-1-1c1 1 1 0 2 1l1 2h1c1-1 3-1 4-1v1c0 1-1 1-1 1-1 0-1 1-2 2 1 0 2-1 2 0s0 1-1 1-1 0-2 1h0-2l1 1s1 0 1 1c2 0 2 1 3 2-1 1-1 1-2 1h0c-1 1-2 1-3 2-1 0-1 0-2-1v2l-2 1-1 1c-2 1-3 1-5 2-1 0-3 1-5 2h0c1 0 2 0 2 1h0c-1 0-2 0-3 1-2 1-4 0-6 1-1 0-2 0-3-1-2 0-3 0-5 1l-3 2h0c-1 1-3 2-3 3v1h-1v-1h-1s0 1-1 1h-2c-1 0-2 0-3 1s-4 0-5 1h-1l-2-2-3 3-1 2c-1 1-1 2-1 3h-1v-2c-1 0-2 0-3 1v1h0c-1 0-1 0-2-1v1c-1-1-2-1-3-1h0c-1-1-1-1-2-1l-1-1c0 1 0 1-1 1s-1 1-2 2h-3v2l-2 1v1h-2c-1 0-2 1-3 2h-1v-1-1-1l1-2c-3 0-5 1-7 2v-1l-1-1s0-1-1-2h-1l-1 2-3 2c-1 0-1 0-1-1-1 0-1 0-2 1l-5 1v1h0 0-2v1h-1v-1c-1-1-2-1-2-2h-1 0-2v-1-1l-1 1-2-1v-3h-2v-2h3c1-1 1-1 0-2-2 0-3-1-5-2v-1h-1-1-3v-1l1-1h-1v-1l-1-1h-2v-1h-1-3v-2 1l-1-1 1-1h1l-2-1h0c1-1 2-1 2-1 0-1 0-1-1-2l-1-1h1 1c0-1-1-2-1-3h-1v-2h-1c-1 1-1 1-2 0l-1-1c1-1 1-1 2-1h1v-1h1 0 2v-1c1 0 2-1 2-1 0-1-1-2-2-3 0 0 0-1-1-1 0 0 0 1-1 2-1-1-2-2-4-3l2-1-1-1-2-1-2 2h-1 0l-1-1h-1c0-1-1-3-1-4l-1-1c-2-1-3-2-5-2l-1-1h3v-1h-2v-1h1 1v-1h-1-2v-3h1v-1c-1 0-2 1-3 1h-1 0v-1-1-1c-1-2-1-4 0-6h-1v-1c1-1 2-1 3-1v-1h1l1 1 1-1v-1-1h1c-1-1-2-1-2-2h1c1 1 2 1 3 1h0v-2s1 0 1-1l-2-2c-1 0-2-1-3-2h-1-1l1-1s0-1 1-2c0 0 1 0 1-1h0v-1c1 0 1 0 2-1-1 0-1 0-1-1s1-1 1-2h0l1-1c1-3 4-6 5-10v-2l4 2c0 1 1 1 2 2 2-1 1-4 1-6h0l3 3c1 1 1 2 2 3v-1c0-1-3-3-3-3 0-1-1-2-1-3z" class="j"></path><path d="M207 290l2 1v3l-2-1v-3z" class="I"></path><path d="M199 285h0c1 0 2 1 3 2-1 0-1 1-2 1 1 1 1 2 1 3-1-2-1-4-2-6z" class="C"></path><path d="M258 286l3-2v3h0-2l-1 1v-2z" class="D"></path><path d="M205 288h1c0 2 0 4-1 5-1-1-1-2-1-3l1-2z" class="K"></path><path d="M201 335h0v-2c-1 0-1 0-1-1v-1-1-1c1 1 1 1 2 1 0 2 0 3-1 4v1z" class="e"></path><path d="M202 330c1 1 2 1 2 2s-1 2-1 2l-2 1v-1c1-1 1-2 1-4z" class="B"></path><path d="M268 287l1-1h1c0 1 0 1-1 2h-1c-1 1-3 1-4 1h-1 1c1-1 2-2 4-2z" class="m"></path><path d="M216 277l1-2h0v1 5h0c-1 2-1 2-1 4h-1c0-1 1-2 1-3-1-1 0-3 0-5z" class="Y"></path><path d="M252 317h0c0-1-1-1-1-1v-1h2v1c1 0 1 0 2-1 0 1 1 1 1 2-1 1-1 1-2 1s-2 0-2-1z" class="D"></path><path d="M207 290c1 0 1 0 1-1h0l1-1c1 1 0 1 1 1h1c-1 2-1 3-1 5v-1-1h-1v2-3l-2-1z" class="e"></path><path d="M268 302h2l-1 1h-2c-2 1-3 2-4 3-1 0-1 0-1-1 1-1 1-1 2-1l1-1 1-1h2 0z" class="D"></path><path d="M191 342h0c0 1 0 2 1 3h1c1-1 1-2 2-2l1 1v1h-2c-1 0-2 1-3 2h-1v-1-1-1l1-2z" class="F"></path><path d="M185 339v1h0c0 1 0 1 1 1 2 0 3 1 5 1h0 0c-3 0-5 1-7 2v-1-1c1-1-1-1-1-2 1 0 1 0 2-1z" class="B"></path><path d="M209 308v-1l1-1c0-1 0-1 1-1s1 1 1 2h2c1 0 1-1 2-1v1c-1 0-1 1-2 1v-1h0c-1 1-1 2-1 3l-1 1c0-1-1-2-1-3v-1h-1l-1 1z" class="V"></path><path d="M248 312l1 1c1 1 1 1 2 0 1 1 2 1 2 2h-2v1s1 0 1 1h0c-2 1-3 1-5 0l1-1v-4z" class="h"></path><path d="M248 312l1 1c1 1 1 1 2 0-1 2-1 3-2 3h-1v-4z" class="B"></path><path d="M220 291l1-2 1-1v-1c1 0 2 0 3 1v1s0 1 1 1c0 1-1 1-1 2h0c-1-1-2-2-2-3l-1 1c0 1 0 2-1 3v1h0l-1 1v-1-1c1-1 1-1 0-2z" class="F"></path><path d="M271 302c0-1 0-1-1-2h-2 0-1v-1c1-1 1-1 3-2h3 1l-2 1-1 1c1 0 1 1 2 1v2h-1-1z" class="S"></path><path d="M212 295l1 1-1 1h1 2 0l-1 1v2h-3c-1 0-2 1-2 1h-1c0-1 0-1 1-2v-2l1-1 2-1z" class="V"></path><path d="M210 296h1v3h-1v-3z" class="m"></path><path d="M209 308l1-1h1v1c0 1 1 2 1 3l1-1c0-1 0-2 1-3h0v1 5h-1l-1 1h0l-1-1c0-1 0-1-1-2v2h-1v-4-1z" class="I"></path><path d="M263 300h1c2 2 2 2 4 2h0-2l-1 1-1 1c-1 0-1 0-2 1 0 1 0 1 1 1l-1 1c-1 0-2 0-3 1h0-1v-2c1 0 1 0 2-1h1 0l-1-1c1-2 3-2 3-4z" class="E"></path><path d="M177 338h2 2c1 0 2 1 2 1h2c-1 1-1 1-2 1 0 1 2 1 1 2v1l-1-1s0-1-1-2h-1l-1 2-1-1-1 1c-1-1-2-1-2-2 1 0 1 0 1-1h-3 1c1-1 2-1 2-1z" class="h"></path><path d="M226 283c1 0 2 0 3-1l-1 6c-1 2-2 5-2 7 0 1 0 5-1 6v-1c-1 0-1 0-2-1v-1c1 0 1-1 2-1l-1-2 1-1c0-1 0-1 1-2v-2c0-1 1-2 1-2-1-1-1-2-1-2v-3z" class="S"></path><path d="M173 321c2 0 3 1 5 1l7 2c2 1 5 3 7 2l-2 2c-2 0-4-1-5-2-2 0-4 0-5-1s-2-1-2-1c-2-1-3-1-4-2l-1 1h-1v-1l1-1z" class="E"></path><path d="M263 294h8 0c-1 1-3 1-4 2-1 0-1 1-1 1-2 0-3 1-5 2h0c2 0 3-1 4-1 0 1 0 1-1 2h-1-3-1v-1l2-2v-1c0-1 1-1 2-2z" class="Y"></path><path d="M211 289l1-1 1 1-1 2h1c1-1 1-1 1-2l1-1v1c0 2-1 4-1 6h1v2h-2-1l1-1-1-1-1-1h-1c0-2 0-3 1-5z" class="I"></path><path d="M211 294c1 0 1-1 2-1v3l-1-1-1-1z" class="R"></path><path d="M245 321h2c1 1 1 2 2 1l1-1c1 1 1 1 1 2-1 0-2 1-3 2v1c-2 0-3 0-5 1 0-1-1-1-1-2h0l-1-1v-1c1 0 3-1 4-2z" class="S"></path><path d="M169 318l10 3 13 4v1c-2 1-5-1-7-2l-7-2c-2 0-3-1-5-1l-8-2c1-1 3-1 4-1z" class="o"></path><path d="M222 321v-9l1-1h0v-1c2 6 2 10 2 15 0 2 0 2-1 3l-1 1-1-3v-5z" class="W"></path><path d="M222 321c1 1 0 5 1 5l2-1c0 2 0 2-1 3l-1 1-1-3v-5z" class="L"></path><path d="M238 315c-1-1-1 0-1-1h2v-1h-1-2l-2-2h0l-1-1v-1l1 1c1 0 1 1 2 1 2 1 5 0 7 1l1 1h1c1 0 1-1 2-1h1v4l-1 1h-3l-1-1c-1 0-1 0-2 1-1-1-1-1-1-2h-2z" class="c"></path><path d="M238 315c1 0 1 0 2-1v-1h1l2 2h1v2l-1-1c-1 0-1 0-2 1-1-1-1-1-1-2h-2z" class="V"></path><path d="M215 295v-1-1h1c0-2 0-4 2-5h0v1h1l1-1v1 2h0c1 1 1 1 0 2v1 1l1-1c0 2 0 3-1 5v1h-3-1-2v-2l1-1h0v-2z" class="P"></path><path d="M216 297h1v3h-1c0-1 0-1-1-1l1-2z" class="G"></path><path d="M215 297h1l-1 2c1 0 1 0 1 1h-2v-2l1-1z" class="R"></path><path d="M170 327v-1h1l2-2c0 2 1 2 2 3l4 1h1l1 1 11 2v1c-3 0-5-1-7-1l-11-1s-10-3-11-3c1-1 1-1 2-1l5 1z" class="o"></path><path d="M170 327v-1h1l2-2c0 2 1 2 2 3l4 1h1l1 1-11-2z" class="B"></path><path d="M268 288l1 1v1h-2l-1 1h3c1 0 1 0 2-1v1c1 0 1 0 2 1h4c1 0 1 0 2-1v1c-1 1-3 1-4 1s-1 0-2 1v-1h-1l-1 1h3 0 0 2c0 1 0 1-1 2h-1v1h-1c-1-1-1-1-2-1v1h-1-2-2s0-1 1-1c1-1 3-1 4-2h0-8l1-1h-1s-1 0-1 1h-1v-1s1-1 2-1v-1c1 0 2 0 2-1h0l-1-1c1 0 3 0 4-1z" class="P"></path><path d="M271 291c1 0 1 0 2 1h0-3l1-1z" class="G"></path><path d="M174 330l11 1c2 0 4 1 7 1h-1c0 1-1 1-2 1 0 1 0 1 1 2 1 0 2-1 3-1v1l-1 1h0-6-1-2-1 0l-1-1c-2 0-4 0-6-1v-1s-1 0-1-1h-1 1 0l2-1c0-1-1 0-2 0v-1z" class="B"></path><path d="M183 335h2v1h-2-1 0l-1-1h2z" class="S"></path><path d="M174 332h0 2c2 0 3 0 4 1h4c1 0 1-1 2-1 0 1 1 1 0 2-1 0-2 0-3 1h-2c-2 0-4 0-6-1v-1s-1 0-1-1h-1 1z" class="R"></path><path d="M201 327c-1-4-1-9 0-13 0-1 0-2 1-2h1c1 4 1 9 1 14l5-3 3 1v1c0 2 0 4-1 6l-2-2c-1-1-3-2-5-3-1 0-2 1-2 1 0 1 0 0-1 1v-1z" class="M"></path><path d="M173 297c3 2 6 4 9 5h8 0c0 2 0 2 1 4h0c1 0 2 1 2 2h-2l1 1h1l-1 1-2-1v1 1h0l-3-1c-1-1-3-1-4-1 0 0-1 0-2-1h0l-2-1-1-1c-1 0-1 0-2-1h0 3c-1-1-1-1-1-2h0c-2-1-3-2-4-3 0-1-1-1-1-3z" class="B"></path><path d="M190 302h0c0 2 0 2 1 4h0c1 0 2 1 2 2h-2c-2 0-3-2-5-2h0c-2 0-2-1-3-1l-1-1h1c1 0 2-1 3-1l1 1h1 1l1-1v-1z" class="R"></path><path d="M209 323c4-1 9 2 13 3l1 3 1-1 1 1v1c0 2-2 3-4 3l-3 3c0-1-1-1-1-2h-1l-1 1h-1c-1 0-1-1-1-1 0-1-1-2-2-3h0c1-2 1-4 1-6v-1l-3-1z" class="H"></path><path d="M213 334c0-2 0-3 1-5v-1c-1-1-1-1 0-2 1 0 2 0 3 1s1 1 1 2h-3l1 2-2 3v1c-1 0-1-1-1-1z" class="u"></path><path d="M223 329l1-1 1 1v1c0 2-2 3-4 3l-3 3c0-1-1-1-1-2h-1l-1 1h-1v-1l2-3c2-1 4-2 7-2z" class="J"></path><path d="M223 329l1-1 1 1c-2 0-4 1-5 3h0-3l1 2h-1-1l-1 1h-1v-1l2-3c2-1 4-2 7-2z" class="D"></path><path d="M160 318h1c1 0 3 1 4 1s8 2 8 2l-1 1v1h1v1l-2 2h-1v1l-5-1c-1 0-1 0-2 1h-2c0-1-1-2-2-2h0-1c-1-1-3-1-4-1h1v-1l-4-1c1 0 1-1 2-2l6 1c0-2 0-2 1-3z" class="I"></path><path d="M169 322c1 0 1 1 2 2h0c-2 0-2 0-3-1h0l1-1z" class="F"></path><path d="M164 320c1 1 3 1 5 2l-1 1h0c-2 1-3 1-5 1v-1c1 0 1 0 1-1l-1-1h1-1l1-1z" class="B"></path><path d="M160 318h1c0 1 0 1 1 2h2l-1 1h1-1l1 1c0 1 0 1-1 1s-3-1-4-2c0-2 0-2 1-3z" class="S"></path><path d="M153 320l6 1c1 1 3 2 4 2v1c1 0 1 0 2 1-1 0-2 0-3-1v1 1h3c-1 0-1 0-2 1h-2c0-1-1-2-2-2h0-1c-1-1-3-1-4-1h1v-1l-4-1c1 0 1-1 2-2z" class="Z"></path><path d="M202 327s1-1 2-1c2 1 4 2 5 3l2 2h0c1 1 2 2 2 3 0 0 0 1 1 1h1l1-1h1c0 1 1 1 1 2l-1 2c-1 1-1 2-1 3h-1v-2c-1 0-2 0-3 1v1h0c-1 0-1 0-2-1v1c-1-1-2-1-3-1h0c-1-1-1-1-2-1l-1-1c0-1 0-2 1-3l-2-1s1-1 1-2-1-1-2-2c-1 0-1 0-2-1l1-2v1c1-1 1 0 1-1z" class="L"></path><path d="M208 336l1 1v2l1 1v1c-1-1-2-1-3-1h0l1-1-1-2 1-1z" class="p"></path><path d="M209 329l2 2h0c0 1-1 2-1 3-1-2-2-2-3-3 1-1 2-1 2-2z" class="h"></path><path d="M202 327s1-1 2-1c2 1 4 2 5 3 0 1-1 1-2 2-2-2-4-2-5-4z" class="F"></path><path d="M211 331c1 1 2 2 2 3 0 0 0 1 1 1h1l-3 4c0-2-1-3-2-5 0-1 1-2 1-3z" class="C"></path><path d="M204 332l3 3 1 1-1 1 1 2-1 1c-1-1-1-1-2-1l-1-1c0-1 0-2 1-3l-2-1s1-1 1-2z" class="I"></path><path d="M204 332l3 3-2 2v-2l-2-1s1-1 1-2z" class="g"></path><path d="M168 334h1 1 2 3c2 1 4 1 6 1l1 1h0 1c-1 1-2 1-2 2h-2-2s-1 0-2 1h-1 3c0 1 0 1-1 1 0 1 1 1 2 2l1-1 1 1-3 2c-1 0-1 0-1-1-1 0-1 0-2 1l-5 1v1h0 0-2v1h-1v-1c-1-1-2-1-2-2h-1 0-2v-1-1l-1 1-2-1v-3h-2v-2h3c1-1 1-1 0-2h2l1-1h6z" class="o"></path><path d="M161 341h1c4-1 8-1 12-2h3c0 1 0 1-1 1h-1c-2 0-4 0-6 1h0c-3 0-6 1-8 1v-1z" class="I"></path><path d="M158 339l2 1h0c1 0 1 0 1 1v1c2 0 5-1 8-1v1h-1c-1 1-3 1-5 2h0 0-2v-1-1l-1 1-2-1v-3z" class="J"></path><path d="M159 335h2l2 1h0 1c0 1 0 1 1 2v1h4c-3 0-7 0-9 1h0l-2-1h-2v-2h3c1-1 1-1 0-2z" class="Q"></path><path d="M159 335h2l2 1c-2 1-3 1-4 1 1-1 1-1 0-2z" class="O"></path><path d="M169 341h0c2-1 4-1 6-1h1c0 1 1 1 2 2l1-1 1 1-3 2c-1 0-1 0-1-1-1 0-1 0-2 1l-5 1v1h0 0-2v1h-1v-1c-1-1-2-1-2-2h-1 0c2-1 4-1 5-2h1v-1z" class="X"></path><path d="M169 341h0c2-1 4-1 6-1 0 1-1 1-2 1-2 1-3 1-5 1h1v-1z" class="q"></path><path d="M164 344c2-1 5 0 8-1h2v1l-5 1v1h0 0-2v1h-1v-1c-1-1-2-1-2-2z" class="O"></path><path d="M168 334h1 1 2 3c2 1 4 1 6 1l1 1h0 1c-1 1-2 1-2 2h-2-2c-2 0-6 0-8 1h-4v-1c-1-1-1-1-1-2h-1 0l-2-1 1-1h6z" class="F"></path><path d="M182 336h0 1c-1 1-2 1-2 2h-2v-1h0 0l3-1z" class="B"></path><path d="M172 334h3c2 1 4 1 6 1l1 1h0 0-6-5l1-2z" class="Q"></path><path d="M168 334h1 1 2l-1 2-2-1-6 1h0l-2-1 1-1h6z" class="q"></path><path d="M170 334h2l-1 2-2-1h-2 1l-1-1h2 1z" class="L"></path><path d="M274 305h1c1-1 3-1 4-1v1c0 1-1 1-1 1-1 0-1 1-2 2 1 0 2-1 2 0s0 1-1 1-1 0-2 1h0-2l1 1s1 0 1 1c2 0 2 1 3 2-1 1-1 1-2 1h0c-1 1-2 1-3 2-1 0-1 0-2-1v2l-2 1-1 1c-2 1-3 1-5 2-1 0-3 1-5 2h0c1 0 2 0 2 1h0c-1 0-2 0-3 1-2 1-4 0-6 1-1 0-2 0-3-1v-1c1-1 2-2 3-2 0-1 0-1-1-2l-1 1c-1 1-1 0-2-1h-2c1 0 3-1 4-1v1h2 0 1 2l1-1h2c1-1 1-1 2-1h1v-2h0l1-1h0l4-4h1s1-1 2-1l-1-1v-1l2-2 1 1h1v-1c1-1 2-2 3-2z" class="B"></path><path d="M261 316c1 1 1 1 3 2-1 1-1 1-1 2-2-1-2-2-3-3h0l1-1z" class="G"></path><path d="M274 311s1 0 1 1c2 0 2 1 3 2-1 1-1 1-2 1h0c-1 1-2 1-3 2-1 0-1 0-2-1-1 0-2 1-3 0l1-1h0c-1 0-1 0-2-1 0 0 2-1 3-1l1-1h1c0-1 1-1 2-1z" class="D"></path><path d="M269 315c1 0 3-1 5-2 0 0 3 1 4 1-1 1-1 1-2 1h0c-1 1-2 1-3 2-1 0-1 0-2-1-1 0-2 1-3 0l1-1z" class="r"></path><path d="M251 323h1c1 0 2-1 2-1 2 0 3-1 4-1 2-1 3-1 5-1v2c-1 0-3 1-5 2h0c1 0 2 0 2 1h0c-1 0-2 0-3 1-2 1-4 0-6 1-1 0-2 0-3-1v-1c1-1 2-2 3-2z" class="P"></path><path d="M142 320h3v1c1 1 1 1 2 1h0c2 1 3 1 4 0l4 1v1h-1c1 0 3 0 4 1h1 0c1 0 2 1 2 2h2c1 0 11 3 11 3v1c1 0 2-1 2 0l-2 1h0-1 1c0 1 1 1 1 1v1h-3-2-1-1-6l-1 1h-2c-2 0-3-1-5-2v-1h-1-1-3v-1l1-1h-1v-1l-1-1h-2v-1h-1-3v-2 1l-1-1 1-1h1l-2-1h0c1-1 2-1 2-1 0-1 0-1-1-2z" class="X"></path><path d="M157 328c1 1 2 1 3 1v1h-1-1c-1 0-1 0-2-1h1v-1z" class="t"></path><path d="M159 325c1 0 2 1 2 2v2h-1c-1 0-2 0-3-1h-2 0-1c2-1 3-1 4-1h1 0l-1-1h-3c2 0 2 0 4-1z" class="g"></path><path d="M142 324h3s1 0 1 1h1 2l3 1v1h0c-1 1-2 2-3 2l-1-1h-2v-1h-1-3v-2 1l-1-1 1-1z" class="f"></path><path d="M142 325h3v2h-3v-2z" class="O"></path><path d="M142 320h3v1c1 1 1 1 2 1h0c2 1 3 1 4 0l4 1v1h-1c1 0 3 0 4 1h1 0c-2 1-2 1-4 1-2-1-4-1-6-1h-2-1c0-1-1-1-1-1h-3 1l-2-1h0c1-1 2-1 2-1 0-1 0-1-1-2z" class="p"></path><path d="M142 320h3v1c1 1 1 1 2 1h0-4c0-1 0-1-1-2z" class="I"></path><path d="M147 325c1-1 3-1 4-1h3c1 0 3 0 4 1h1 0c-2 1-2 1-4 1-2-1-4-1-6-1h-2z" class="m"></path><path d="M163 327c1 0 11 3 11 3v1c1 0 2-1 2 0l-2 1h0-1 1c0 1 1 1 1 1v1h-3-2-1-1-6l-1 1h-2c-2 0-3-1-5-2v-1h-1c0-1 1-1 2-2 1 1 3 2 5 3h0c1 0 1 0 2-1h-2l-2-2h1 1v-1h1v-2h2z" class="r"></path><path d="M154 332c2 0 4 0 6 1 0 1 0 1 1 1h1l-1 1h-2c-2 0-3-1-5-2v-1z" class="Q"></path><path d="M168 334c-2-1-4 0-5-1v-1h2 1v-1h-1-4 0l1-1c2 0 3 0 5 1 2 0 5-1 7 1h-1 1c0 1 1 1 1 1v1h-3-2-1-1z" class="E"></path><path d="M169 334l2-2c1 0 3 1 4 1v1h-3-2-1z" class="F"></path><path d="M156 305h0c0-1-1-2-1-2v-1h2c0 1 1 1 1 2l1-1 2 1h2 1c2 1 2 2 3 3h1c0 2 1 2 2 3s2 1 2 2l-1 1-1 2h-3c-1 0-2-2-3-2-1-1-1 0-1-1-1-1-1-2 0-2l-1-1v1 3h-1c0 1 1 1 1 1h1c0 1 1 1 1 1 1 0 2 0 3 1h0c1 1 3 0 4 1s3 1 4 1l1 1h0c1 0 1 1 2 1l1 1-10-3c-1 0-3 0-4 1-1 0-3-1-4-1h-1c-1 1-1 1-1 3l-6-1c-1 1-1 2-2 2-1 1-2 1-4 0h0c-1 0-1 0-2-1v-1h-3l-1-1h1 1c0-1-1-2-1-3h-1v-2h-1c-1 1-1 1-2 0l-1-1c1-1 1-1 2-1h1v-1h1 0 2v-1c1 0 2-1 2-1 1 0 1 1 2 1l1 1h1l-4-4c0-2 1 0 2-2-1 0-1-1-2-1h0v-2c1 0 1 1 2 1 0 2 3 3 4 4h0 1c1 1 1 1 2 1 0-1 0-1 1-1v-1-1h1z" class="E"></path><path d="M161 309v3h-1c-1 0-1-1-1-1 0-1 1-1 2-2z" class="v"></path><path d="M159 308c1 0 1 0 2 1-1 1-2 1-2 2l-2-1c1-2 1-2 2-2z" class="Z"></path><path d="M162 316l7 2c-1 0-3 0-4 1-1 0-3-1-4-1h-1l-1-1h2l1-1z" class="Q"></path><path d="M156 305h0c0-1-1-2-1-2v-1h2c0 1 1 1 1 2l1-1 2 1-1 1-2 2 1 1c-1 0-1 0-2 2v-1l-2-3v-1h1z" class="I"></path><path d="M155 305h1c0 1 1 1 2 2 0 1-1 1-1 2l-2-3v-1z" class="Z"></path><path d="M163 307c2 0 4 3 6 4 1 1 1 2 2 2l-1 2c-1 0-2 0-3-1-1 0-2-2-2-3h0v-1c-1-1-2-2-2-3z" class="r"></path><path d="M164 304c2 1 2 2 3 3h1c0 2 1 2 2 3s2 1 2 2l-1 1c-1 0-1-1-2-2-2-1-4-4-6-4h-1v-1l1-2h1z" class="g"></path><path d="M169 311h0c0-1 0-1-1-1-1-1-1-2-2-3h1 0 1c0 2 1 2 2 3s2 1 2 2l-1 1c-1 0-1-1-2-2z" class="K"></path><path d="M147 312h2l1 1 1-1c1 1 2 2 3 2h0c0-1 1-1 1-1 1 2 3 2 5 3h2l-1 1h-2 0c-3-1-6-2-9-2h-2l-1-1v-2z" class="q"></path><path d="M147 312h2c0 2 0 2-1 3l-1-1v-2z" class="s"></path><path d="M145 309c1 0 1 1 2 1l1 1h1 1 0c0 1 1 1 1 1h0l-1 1-1-1h-2v2l1 1h2c3 0 6 1 9 2h0l1 1c-1 1-1 1-1 3l-6-1c-1 1-1 2-2 2-1 1-2 1-4 0h0c-1 0-1 0-2-1v-1h-3l-1-1h1 1c0-1-1-2-1-3h-1v-2h-1c-1 1-1 1-2 0l-1-1c1-1 1-1 2-1h1v-1h1 0 2v-1c1 0 2-1 2-1z" class="B"></path><path d="M142 316h2v2l-1 1c0-1-1-2-1-3zm5-2h0l1 1h2c-1 0-1 1-2 1h0c-1 0-2 0-2-1 1 0 0 0 1-1z" class="D"></path><path d="M143 319l1-1c1 1 1 1 1 2h-3l-1-1h1 1z" class="S"></path><path d="M141 311c2 0 5 1 6 1v2h0c-2-1-5-2-7-2v-1h1z" class="o"></path><path d="M145 309c1 0 1 1 2 1l1 1h1 1 0c0 1 1 1 1 1h0l-1 1-1-1h-2c-1 0-4-1-6-1h0 2v-1c1 0 2-1 2-1z" class="D"></path><path d="M150 315c3 0 6 1 9 2h0l1 1c-1 1-1 1-1 3l-6-1c-1 1-1 2-2 2-1 1-2 1-4 0h0v-1h2 2c-1-1-1-2-1-3h0l-2-2h0c1 0 1-1 2-1z" class="G"></path><path d="M148 316c3 0 4 1 6 2v1s-2-1-2 0 0 1 1 1c-1 1-1 2-2 2-1 1-2 1-4 0h0v-1h2 2c-1-1-1-2-1-3h0l-2-2h0z" class="K"></path><path d="M238 281c1 0 1-1 1-2h0l2-1v1c1 1 1 2 2 2s3-1 4 0h1v-1c1 0 1 1 2 1h0 1c0 1 1 1 1 1h1c2 2 5 1 5 4v2c-1 6-2 12-7 17-3 2-7 3-10 3-4 0-8-2-11-4-1 1-2 2-4 3-1 0-1-1-1-2-1-1 0-3 0-4 1-1 1-5 1-6 0-2 1-5 2-7l1-6v-1l1-2h1v3c0 1 0 2 1 3 1-1 3-2 4-3h0 0c1 0 1-1 1-2l1 1z" class="W"></path><path d="M238 281c1 0 1-1 1-2h0l2-1v1c1 1 1 2 2 2-1 0-1 1-2 1-2 1-3 1-5 2 1 1 2 0 3 1l-1 1h-1c-1-1-2 0-3 1-2 1-2 2-3 4l-1 1s-1-1 0-2h0l-1-1v-1c0-1 1-2 1-3 1-1 1-2 0-4v2c0 2-1 3-2 5h0l1-6v-1l1-2h1v3c0 1 0 2 1 3 1-1 3-2 4-3h0 0c1 0 1-1 1-2l1 1z" class="U"></path><path d="M238 281c1 0 1-1 1-2h0l2-1v1 2c-1 1-2 1-3 1v-1z" class="g"></path><defs><linearGradient id="J" x1="238.989" y1="303.668" x2="244.146" y2="283.741" xlink:href="#B"><stop offset="0" stop-color="#080707"></stop><stop offset="1" stop-color="#333131"></stop></linearGradient></defs><path fill="url(#J)" d="M247 284c2 0 4 0 5 1 1 0 2 1 3 2 0 4-1 10-4 13-2 3-5 4-8 5-4 0-10-1-13-3-1-1-1-2-1-3v-5c0-1 1-2 1-2l1-1c1-2 1-3 3-4 1-1 2-2 3-1h0c-2 0-3 2-4 3-1 2 0 5 1 7 1 3 3 5 6 5 1 1 5 1 6 0h0c3-2 4-4 6-6 0-2 1-5 0-7 0-1-1-2-2-2l-3-2z"></path><path d="M241 286c0-1 0-1 1-2h4 1l3 2c1 0 2 1 2 2 1 2 0 5 0 7-2 2-3 4-6 6h0c-1 1-5 1-6 0-3 0-5-2-6-5-1-2-2-5-1-7 1-1 2-3 4-3h0 1l1-1h1l1 1z" class="u"></path><path d="M241 286c0-1 0-1 1-2h4c-1 4 0 9-3 13h-1c-1-2-1-5-1-6v-5z" class="E"></path><path d="M141 241c1-1 0-2 0-3h2v-1c0-1 0-1-1-2v-3h1c3-2 8-1 12-1 2 1 3 2 5 1l1 1c-1 1-1 1-2 1h0-2c0 1-1 1-1 2 2 1 3 2 4 3h0v1l2 2c0 1 0 1 1 1v1c1 1 1 2 2 2 0 1 1 1 1 1h1v-1l1 1v1h1 2v-2h1s0 1 1 1v1c0 1 0 1 1 2v1-1l1 1h1v-2h0c1-1 1-2 1-2 1 1 2 3 2 4 1 2 1 3 2 4h0c0 2 1 5 2 6h1c1 3 1 7 1 10v3h-1c1 0 2 1 3 1 3 1 5 3 8 5l1 1 1 2 2 2c1 2 1 4 2 6 0 3 1 11-1 14h-2c-1 0-1 0-2-2v-2c-2 1-4 1-6 1h0-8c-3-1-6-3-9-5 0 2 1 2 1 3 1 1 2 2 4 3h0c0 1 0 1 1 2h-3 0c1 1 1 1 2 1l1 1 2 1h0c1 1 2 1 2 1 1 1 1 2 2 2 1 1 2 2 3 2v1h1l-1 1c0-1-2-2-3-2h-1c-1 0-1-1-2-1h0s0 1 1 1 2 1 2 2v1h-1v-1h-1-1-1 0c0-1-1-1-2-1s-1 1-2 1h-1c-1 0-1-1-3-1l1 1-1 1-1-1h-2l1-2 1-1c0-1-1-1-2-2s-2-1-2-3h-1c-1-1-1-2-3-3h-1-2l-2-1-1 1c0-1-1-1-1-2h-2v1s1 1 1 2h0-1v1 1c-1 0-1 0-1 1-1 0-1 0-2-1h-1 0c-1-1-4-2-4-4-1 0-1-1-2-1v2h0c1 0 1 1 2 1-1 2-2 0-2 2l4 4h-1l-1-1c-1 0-1-1-2-1 0-1-1-2-2-3 0 0 0-1-1-1 0 0 0 1-1 2-1-1-2-2-4-3l2-1-1-1-2-1-2 2h-1 0l-1-1h-1c0-1-1-3-1-4l-1-1c-2-1-3-2-5-2l-1-1h3v-1h-2v-1h1 1v-1h-1-2v-3h1v-1c-1 0-2 1-3 1h-1 0v-1-1-1c-1-2-1-4 0-6h-1v-1c1-1 2-1 3-1v-1h1l1 1 1-1v-1-1h1c-1-1-2-1-2-2h1c1 1 2 1 3 1h0v-2s1 0 1-1l-2-2c-1 0-2-1-3-2h-1-1l1-1s0-1 1-2c0 0 1 0 1-1h0v-1c1 0 1 0 2-1-1 0-1 0-1-1s1-1 1-2h0l1-1c1-3 4-6 5-10v-2l4 2c0 1 1 1 2 2 2-1 1-4 1-6h0l3 3c1 1 1 2 2 3v-1c0-1-3-3-3-3 0-1-1-2-1-3z" class="e"></path><path d="M144 286l-1-1 1-1 1 1 1 1v1c-1 0-1 0-2-1zm-11 9c0-1-1-2-2-2 1-1 1 0 2 0s2 1 3 1c0 1 0 1-1 2h0l-1-1h-1z" class="I"></path><path d="M129 286h1l1 1c0 1-1 1-2 2h-1c0-1-1-1-1-2 1 0 1 0 2-1z" class="D"></path><path d="M159 295l4 3c1 1 1 2 1 3h-1c-2-2-3-4-4-6z" class="k"></path><path d="M153 269c1 0 1 0 2 1h0v2s1 0 1 1h0c-3 0-3 0-5-1 1-1 2-1 2-1v-2z" class="C"></path><path d="M154 281h1v1 1l1 1c0 1 1 2 1 3s1 1 1 2h-1c-1-1-1-1-1-2s-1-2-1-3h-1v-2-1z" class="B"></path><path d="M166 277c1 1 0 5 1 7h-2l-1-1c-2-1-2-1-3-2l1-1c1 0 1 1 2 2 1 0 1 0 2-1v-4z" class="D"></path><path d="M145 296h0v-2-1h1s1 0 1 1c1 0 1 0 1 1h0v2l1 2-3-3h-1z" class="E"></path><path d="M164 304h1c0-1 1-1 0-2v-1h1c1 1 1 1 1 2 1 1 1 2 2 2v1l-1 1h-1c-1-1-1-2-3-3z" class="C"></path><path d="M175 262c-1-1-1-1-1-2v-2h-1l-1-1h1 1c1 1 1 1 1 2h1 1c-1-1-1-2-1-3h1c1 2 1 2 1 5l-1-1h-1c0 1 1 1 1 2h-1v-1l-1 1z" class="B"></path><path d="M145 253l1-1c1 1 1 2 2 2v1l-1 1v1 2c-1-1-2-1-3-2 1-1 1-2 1-4h0z" class="D"></path><path d="M133 295h1l1 1c1 1 1 2 1 4l2 2-2-1c-1 0-2-1-3-2v-1l-1-1h0 2c0-1 0-1-1-2z" class="K"></path><path d="M142 288h5v1s1 0 1 1v1 2l-1-1h-1-1c0-2-2-2-3-4z" class="B"></path><path d="M165 284h2v2l1 5s-1 0-2-1-2-2-2-3 1-2 1-3z" class="D"></path><path d="M165 284h2v2l-1 1h-1 0-1c0-1 1-2 1-3z" class="E"></path><path d="M154 254c2 1 3 2 4 3 0 1-1 1-1 1-1 1 0 1-1 2l2 2v2h0l-2-1c-1-1-2-3-3-4-1 0-2-2-4-3h1c1 1 2 2 4 2v-1h1 0c-1-1-1-2-1-3z" class="C"></path><path d="M148 254c1 1 2 1 2 2h-1c2 1 3 3 4 3 1 1 2 3 3 4l2 1c-2 0-3-2-4-3-2 0-4 1-5 0l-1-1c-1-1-1 0-1-1v-2-1l1-1v-1z" class="B"></path><path d="M127 286h1 1c-1 1-1 1-2 1 0 1 1 1 1 2 0 0-1 1-2 1 0 1 3 2 3 3-1 0-3-1-4-1h1v-1h-1-2v-3h1v-1c-1 0-2 1-3 1h-1 0v-1l1-1h3 1 2z" class="g"></path><path d="M133 253h1c0 1 2 2 2 3 1 1 1 2 2 2l-1 1-1-1h-1l1 1-1 1h-1c0 1 1 1 0 2l-3-3 1-1h0 1l-1-1c0-1-1-1-1-2-1 0-1 0-1-1 1 0 1 1 2 1h1l-1-1 1-1z" class="j"></path><path d="M125 262l2 2c1-1 1-1 1-2l1-1c1 0 2 1 2 2v1c-1-1-1-1-2-1h0c0 1 1 1 1 2 1 0 1 0 1 1h-2c-2-1-2-2-4-3l-1 1c1 1 3 2 4 2l1 1c1 1 1 2 3 2l-1 1c-1-1-2-1-3-2-1 0-2-3-4-2h-1-1l1-1s0-1 1-2c0 0 1 0 1-1z" class="Z"></path><path d="M142 305c1-1 2-2 2-3h-2c-1-1-1-3-1-4h1v1c1 1 2 0 3 0v1s0 1 1 1c-1 0-1 1-1 1v2h0c1 0 1 1 2 1-1 2-2 0-2 2l4 4h-1l-1-1c-1 0-1-1-2-1 0-1-1-2-2-3 0 0 0-1-1-1z" class="i"></path><path d="M119 279v-1c1-1 2-1 3-1 0 0 1 1 2 1s1 1 2 1h0l1 1h2c1 1 3 2 4 3l6 5h0l-1 1c0-1-1-2-2-3h0c-1 0-2-1-3-2 0-1-2-1-4-1h1c-1 0-2 0-2-1-1 0-2-2-3-2h0l-1-1v1l-1-1h-1-2-1z" class="c"></path><path d="M155 294l2 2v2l2 2v1h0v2l-1 1c0-1-1-1-1-2h-2v1s1 1 1 2h0-1c-1 0-1-1-2-1v-3l-1-1v-2c-1-1-3-2-3-2l1-1 2 2c1 1 1 3 2 4h2s1-1 0-2c0-1 0-2-1-3v-2z" class="C"></path><path d="M175 262l1-1v1h1c0-1-1-1-1-2h1l1 1c1 2 2 3 3 5h1v-1c1-1 2-2 2-4 1 3 1 7 1 10v3h-1-1l1-1c0-1 0-2-1-2l1-1c0-1 0-1-1-1-1-1-2-3-3-4l-2-2-1 1c-1 0-2-1-2-2z" class="F"></path><path d="M134 253h1c1 0 2 2 4 2 0 0 1 0 1 1 1 0 1 1 2 1 1 2 3 4 4 5h1l-1 1c-1 0-1-1-1-1-1-1-2-1-2-1h-1c0 1 0 1 1 2h-1c-1 0-1-1-2-2 0 0-1-1-1-2 0 0-1 0-1-1-1 0-1-1-2-2 0-1-2-2-2-3z" class="B"></path><path d="M145 296h1l3 3 1 1c0 1 0 2 1 3 1 0 1 1 1 2-1 0-1-1-2-1 0 0 0 1 1 1 0 1 1 1 1 2h-1 0c-1-1-4-2-4-4-1 0-1-1-2-1 0 0 0-1 1-1-1 0-1-1-1-1v-1l1-1-1-2z" class="F"></path><path d="M146 298v2l1 3c-1 0-1-1-2-1 0 0 0-1 1-1-1 0-1-1-1-1v-1l1-1z" class="C"></path><path d="M145 296h1l3 3 1 1h-4v-2l-1-2zm-12-52l4 2-1 1-3-1c0 1 0 0 1 1l1 1v1c-1 0-1 0-2-1v1c1 1 2 2 3 2v1c-1 0-1 0-2-1h-1-1 1v2l-1 1 1 1h-1c-1 0-1-1-2-1 0 1 0 1 1 1 0 1 1 1 1 2l1 1h-1 0l-1 1c-1 0-1-2-4-2h0l1-1c1-3 4-6 5-10v-2z" class="r"></path><path d="M128 256c1 0 3 2 4 1l1 1h-1 0l-1 1c-1 0-1-2-4-2h0l1-1z" class="c"></path><path d="M169 293c2 1 3 3 4 4h0c0 2 1 2 1 3 1 1 2 2 4 3h0c0 1 0 1 1 2h-3c0-2-4-4-6-5h0c-2-1-2-2-4-2l-2-2 1-1v1l1-1h3v-2z" class="j"></path><path d="M169 293c2 1 3 3 4 4h0c0 2 1 2 1 3-1 0-2-1-2-1-1-2-2-3-3-4v-2z" class="C"></path><path d="M168 247v1h1 2v-2h1s0 1 1 1v1c0 1 0 1 1 2v1c0 1 1 2 1 3h-2c-1-1-1-1-2-1-1-1-1-2-1-3h-1v3c1 1 0 3 2 5v1h-1c-1-1 0-1-1-2 0-1-1-2-2-2 0-1-1-2-1-2v-1h1 1c0-1 0-2-1-3v-1h0l1-1z" class="D"></path><path d="M170 250h2v2h1v2c-1-1-1-1-2-1-1-1-1-2-1-3z" class="B"></path><path d="M153 269c-1-1-1-2-2-2v-1c1-1 2 0 3-1 0-1-1-2-2-3 1 0 1 0 1-1l4 4v1 1h1 0c0-1 1-1 1-3v1c0 2 2 2 2 4h0c-1 0-1 0-1 1l1 1-1 1h0c-1-1-1-2-3-2h-2 0c-1-1-1-1-2-1z" class="E"></path><path d="M154 264h1c1 1 1 1 1 2h-2v-2z" class="I"></path><path d="M120 279h2 1l1 1v-1l1 1h0c1 0 2 2 3 2 0 1 1 1 2 1l-1 1-1 1v1h-1-2-1-3l-1 1v-1-1c-1-2-1-4 0-6z" class="X"></path><path d="M123 279l1 1v-1l1 1h0c1 0 2 2 3 2 0 1 1 1 2 1l-1 1-1 1v1h-1c-1-1-2-1-3-1v-1l1-1s1 1 2 1v-1c-1 0-2-1-3-1-2 0-3 1-4 0v-1h1c1 0 2-1 2-2z" class="i"></path><path d="M139 275c1 1 2 1 2 2h1c1 1 2 3 2 4h2v1 1h-1v2l-1-1-1 1 1 1h-2v-1c-3-1-4-4-7-5v-1-2h1v1h0c0 1 0 1 1 1 1 1 1 1 2 1v-1l-1-1h-1v-1c1 0 1 0 2-1h-1l1-1zm31 35l2-2-1-1 1-1 2 2h1 0l2 2h1v1l1 1h0c1 1 2 1 3 1v2h-1 0c0-1-1-1-2-1s-1 1-2 1h-1c-1 0-1-1-3-1l1 1-1 1-1-1h-2l1-2 1-1c0-1-1-1-2-2z" class="C"></path><path d="M172 312l1 1v2h-1-2l1-2 1-1z" class="h"></path><path d="M149 286v-2l-2-2h0c0-1 1-2 0-4h-1v-1h0c1 0 2 1 2 2v1h1 1v-1h0c1 0 3 1 3 2v3 3c1 1 2 1 2 2s0 1-1 1h0v2c0 1 1 2 1 2v2c-1-1-2-4-3-4s-2-1-3-2-1-1-1-2c1-1 1-1 1-2h0z" class="E"></path><path d="M149 286h1c1 1 2 2 2 4h1l-1 1-1-2c-1 0-1 1-1 1h-1c-1-1-1-1-1-2 1-1 1-1 1-2h0z" class="D"></path><path d="M129 270c2 1 3 2 4 3h0l1 1h0 2v1h2 1l-1 1h1c-1 1-1 1-2 1v1h1l1 1v1c-1 0-1 0-2-1-1 0-1 0-1-1h0v-1h-1 1v-1h0l-2-2h-1c0 1-1 2-1 2v1 1h-1 0v1c1 1 2 2 2 3v1c-1-1-3-2-4-3h-2l-1-1h0c-1 0-1-1-2-1s-2-1-2-1v-1h1l1 1 1-1v-1-1h1c-1-1-2-1-2-2h1c1 1 2 1 3 1h0v-2s1 0 1-1z" class="S"></path><path d="M126 274l4 3h0-1v3h-2l-1-1h0c-1 0-1-1-2-1s-2-1-2-1v-1h1l1 1 1-1v-1-1h1z" class="i"></path><path d="M141 241c1-1 0-2 0-3h2v-1c0-1 0-1-1-2v-3h1c3-2 8-1 12-1 2 1 3 2 5 1l1 1c-1 1-1 1-2 1h0-2c0 1-1 1-1 2 2 1 3 2 4 3h0v1l2 2c0 1 0 1 1 1v1c1 1 1 2 2 2 0 1 1 1 1 1h1v-1l1 1-1 1h0v1c1 1 1 2 1 3h-1-1v1s1 1 1 2h-2v2c-1-1-2-1-2-2-1-1-2-2-3-2l-1 1c-1 0-1 1-2 1l-1-1h1l-1-1-1 1h-1v-1c-1 0-1-1-2-1-1-1-1 0-1 0 0 1 1 1 2 2h1 0c0 1 0 2 1 3h0-1v1c-2 0-3-1-4-2 0-1-1-1-2-2-1 0-1-1-2-2l-1 1h0c0-1 1-1 1-1l-1-1v-3-1c0-1-3-3-3-3 0-1-1-2-1-3z" class="g"></path><path d="M152 243c1-1 1-1 2 0v1h-1c-1 0-1-1-1-1z" class="I"></path><path d="M144 236c1 1 2 1 3 2h1 0c1 1 1 2 2 2 1 1 2 1 2 2-2-1-4-1-5-3h-2v-1c-1-1-1-1-1-2z" class="B"></path><path d="M150 242c1 1 3 4 4 5-1 1-2 1-3 1l-1-1v-1c-1-1 0-2 0-4z" class="F"></path><path d="M155 238l2-1 3 3 2 2h-2c0-1-1-1-1-1h-1c0 1 1 1 1 2l-1-1h-3c0-1 1-1 1-1l1-1-2-2z" class="f"></path><path d="M148 237c2-1 4 0 6 0l1 1 2 2-1 1s-1 0-1 1c-2-2-4-3-7-5z" class="n"></path><path d="M144 236v-1h7v1h5c2 1 3 2 4 3h0v1l-3-3-2 1-1-1c-2 0-4-1-6 0v1h-1c-1-1-2-1-3-2z" class="Z"></path><path d="M162 242c0 1 0 1 1 1v1c1 1 1 2 2 2 0 1 1 1 1 1h1v-1l1 1-1 1h0v1c1 1 1 2 1 3h-1-1v1s1 1 1 2h-2v2c-1-1-2-1-2-2-1-1-2-2-3-2l-1 1v-1c-1 0-1-1-2-1l-1-1-1-1 1-1 1 1 1-1h2l-1-1c-1-2-2-2-3-2h0c1-1 1-2 2-3 1 1 1 1 2 1v1l1-1c-1-1-1-1-1-2h2z" class="E"></path><path d="M163 255v-2h1l1 1v-1h0v-1c-1-1-1-1-1-2 1 0 1 0 1-1h1 1c1 1 1 2 1 3h-1-1v1s1 1 1 2h-2v2c-1-1-2-1-2-2z" class="B"></path><path d="M141 241c1-1 0-2 0-3h2v-1c0-1 0-1-1-2v-3h1c3-2 8-1 12-1 2 1 3 2 5 1l1 1c-1 1-1 1-2 1h0-2c0 1-1 1-1 2h-5v-1h-7v1c0 1 0 1 1 2v1c1 1 3 3 5 3 0 2-1 3 0 4v1l1 1c2 1 4 3 5 5l-1 1h-1v-1c-1 0-1-1-2-1-1-1-1 0-1 0 0 1 1 1 2 2h1 0c0 1 0 2 1 3h0-1v1c-2 0-3-1-4-2 0-1-1-1-2-2-1 0-1-1-2-2l-1 1h0c0-1 1-1 1-1l-1-1v-3-1c0-1-3-3-3-3 0-1-1-2-1-3z" class="R"></path><path d="M151 235c2-1 4-1 6-1 0 1-1 1-1 2h-5v-1z" class="Q"></path><path d="M145 247c1 1 1 1 1 3h1 0c0 1 1 1 1 2v2c-1 0-1-1-2-2l-1 1h0c0-1 1-1 1-1l-1-1v-3-1z" class="S"></path><path d="M166 277v-3h3 1v-1-1c1 0 1 1 1 1h4 1 1 2 0 1v-1-1l1 1c0 1 0 1 1 2h0v-3h0 1c1 0 1 1 1 2l-1 1h1c1 0 2 1 3 1 3 1 5 3 8 5l1 1 1 2 2 2c1 2 1 4 2 6 0 3 1 11-1 14h-2c-1 0-1 0-2-2v-2c-2 1-4 1-6 1h0-8c-3-1-6-3-9-5h0c-1-1-2-3-4-4l-1-2-1-5v-2c-1-2 0-6-1-7z" class="N"></path><path d="M191 281c3 2 5 6 6 10 0 2 1 5-1 7l-1 1c-3 0-7 1-10 0-5 0-10-3-13-7s-3-11-2-15v-1c3 0 10-1 12 1h0 0c-3 1-7-1-10 2 1 5 2 10 5 13 2 2 5 3 8 3s6-1 8-3c0-1 0-2 1-3h0c0-2 0-4-1-5s-2-2-2-3h0z" class="c"></path><path d="M182 277h1v1c2-1 6 1 7 2l1 1h0c0 1 1 2 2 3s1 3 1 5h0c-1 1-1 2-1 3-2 2-5 3-8 3s-6-1-8-3c-3-3-4-8-5-13 3-3 7-1 10-2h0z" class="u"></path><path d="M183 278c2-1 6 1 7 2l1 1h0c-1 0-2-1-3-1-1 1-1 3-1 5-1 1-2 3-3 5 0 1 0 1-1 1l-1-1c0-4 1-8 1-12z" class="P"></path><path d="M210 624c26 2 50-11 62-35 1-3 2-6 4-10 1-4 3-9 4-14s2-11 3-17c2-21 1-42 1-64l1-83V141l-60 5c-17 3-34 6-50 12-21 7-40 18-55 35-13 15-22 35-25 55l-1 11c0 2 0 7-1 9H60l3-37 6-69 4-48c1-7 1-15 2-22 10 5 19 9 30 10 8 1 17 0 25 0h38 125 179 59 33c6 0 13-1 20-3l12-6 13 175h-34c-2-33-11-65-36-87-18-17-42-25-66-31-15-3-29-5-44-7l-25-2v150 104 83c0 26-1 52 3 78 3 18 8 39 22 52 7 7 16 12 26 15 6 1 13 1 19 1 2 0 5 0 8-1v24H208l1-21v-2h1z" class="u"></path><path d="M479 124l2 2c-1 0-1 1-1 2l-1-1v-3zM370 266c1 0 1 0 2 1l-2 2-1-2 1-1z" class="L"></path><path d="M334 490c1-2 1-3 3-3 0 1-1 2-1 2l-1 1h-1z" class="U"></path><path d="M395 531l1 4-2-1v-3h1z" class="K"></path><path d="M313 448h1c1 1 2 1 2 2v1h-2c-1-1-1-2-1-3z" class="a"></path><path d="M416 620v-1c1 0 1 0 2-1h0v2s-1 1-2 1v-1zm-43-234l2 1c0 1 0 1 1 1l-4 1 1-3z" class="I"></path><path d="M393 405s1 0 1-1v5h-2l1-2c1-1 0-1 0-2h0z" class="B"></path><path d="M312 329l2 1 1-1v3h-2l-1-1v-2z" class="I"></path><path d="M416 613c1 1 1 4 2 5h0c-1 1-1 1-2 1v1c-1-1-1-1-1-2 1-2 1-3 1-5z" class="g"></path><path d="M370 422c1-1 1-2 1-3h1 0v2c1 0 1 1 2 1h1 0-1-4z" class="M"></path><path d="M329 366h1v1 3l-1-1c-1 0-1 1-2 1h-2c2-1 3-2 4-4z" class="C"></path><path d="M412 613c0-1 0 0 1 0 0-1 0-2 1-2h0c-1 2-1 4 0 6h-1c-1-1-1-3-1-4z" class="E"></path><path d="M337 487v-1h-1c-1-1-1-1-2-1 1-1 4-1 5-2 0 2-1 2-1 4h-1z" class="T"></path><path d="M381 461c0-1 0-2-1-3h0v-1c2 0 3 1 4 2l-3 2z" class="H"></path><path d="M329 454c1 1 2 3 3 4h0v2l-4-3c0-1 1-2 1-3z" class="U"></path><path d="M357 460v1h1l1 1c-1 1-1 1-1 2h-1c-1 0-1-1-1-2s0-1 1-2z" class="h"></path><path d="M345 342v-1h1l-1 3c0 2-1 3-1 5h-1v-3c1-1 1-2 2-4z" class="x"></path><path d="M97 210c1 0 1 1 1 1l-1 1c0 1 0 2-1 3h-1l1-1h-1c-1-1-1 0-2-1h1l1-1 2-2h0z" class="M"></path><path d="M386 339c-1-1-1-2-1-3 1 0 1 0 2 1s0 3 0 5l-1-1h0v-2z" class="R"></path><path d="M353 450c0 2 0 3-1 5h-1 0 0v3l-2-2c1-1 1-2 2-3l2-3z" class="H"></path><path d="M96 205v-1-4c1 0 1 1 2 2 0 2 0 4-1 5s-1 1-1 2c0-1 0-3 1-3v-1h-1z" class="F"></path><path d="M491 123l2 2v6c0-1-1-2-1-2l-1-1c-1-1 0-4 0-5z" class="M"></path><path d="M385 458v-1-1h-1c0-1 0-1 1-2 1 0 1 1 2 1 0-2 1-2 2-3-1 1-2 5-4 6z" class="T"></path><path d="M477 122c1 1 2 1 2 2v3l1 1c-1 0-1 1-1 1-1-1-2 0-3-1l2-1v-2c0-1-1-2-1-3z" class="a"></path><path d="M334 355h0l1 1c-1 2-3 5-4 7 0-1-1-2 0-3 0-1 1-2 2-2v-1l1-2z" class="T"></path><path d="M475 119c1 1 1 2 2 3 0 1 1 2 1 3-1 1-1 1-2 1v-2h-1l1-1c-1-1-1-2-2-3h-1c0-1 0-1 1-1h1z" class="F"></path><path d="M374 214h0c0 1 1 1 1 2v3l-1 2h0l-1-2h-2c1-2 2-3 3-5z" class="H"></path><path d="M359 365s1 0 1 1c1 0 3 1 4 1v2h0c1 1 1 2 1 2-2-1-3-2-4-3l-3-3h1z" class="N"></path><path d="M376 229h0v-3c1 0 2 1 3 2v1h1c-1 1-1 2-2 3v-1-1l-2-1z" class="F"></path><path d="M561 186h1l-1 1v1h-1l1 1c1 1 2 3 2 5-2-2-4-5-5-7h2l1-1z" class="j"></path><path d="M589 220l-1-1h-2v-1l1-1 1 1 3-3 2 1-4 4h0z" class="d"></path><path d="M182 143c0-1 1-1 1-2h1l-1-1h1c1-1 2-3 2-4l2-2c-1 4-2 7-6 9h0z" class="N"></path><path d="M360 469c2 0 3 1 4 0l1 1c-2 2-3 3-4 5l-1-6z" class="F"></path><path d="M383 412l1 1h1s1 1 2 1h-3-8c2-2 4-1 7-2z" class="J"></path><path d="M179 134h0v1l1 1c1 2 1 4 0 6v1c-1-1-1 0-1-1h-1c1-1 1-2 1-3v-5zm206 247c1 1 2 1 4 1l1 1c-2 2-2 3-4 4v-3c-1-1-1-2-1-3z" class="S"></path><path d="M404 616c0 1 0 2 1 3 0 1 1 2 1 3v2l-1 1-1-1v-8z" class="T"></path><path d="M375 329c1-1 1-1 3-1 1 1 2 1 3 2-1 1-1 1-3 1h-1l-2-1v-1z" class="F"></path><path d="M415 607c1 1 1 1 1 3v3c0 2 0 3-1 5v-4c0-2 0-3-1-5l1-2z" class="m"></path><path d="M392 196c0 1 1 2 1 3l1 1-1 1v3h0v-1l-1 1v-1c-1-1-1-2-1-3v-2-1l1-1z" class="T"></path><path d="M393 203h-1v-3c0-1 0-1 1-1l1 1-1 1v3h0v-1z" class="M"></path><path d="M356 398h-3v-1c0-1 0-1 1-2 0-1 2-2 3-2s3 0 4 1c-2 0-3 0-5 2-1 0-1 0-1 1l1 1z" class="d"></path><path d="M293 264c1 0 1 1 1 2l3 6c-1 0-1 1-2 1v4h-1l-1-13z" class="C"></path><path d="M360 442h1 0c1 0 1 1 2 1l-4 3c-1 0-1-1-2-1l1-1-1-1c1-1 2-1 3-1z" class="I"></path><path d="M340 482l1-1 1 1c-1 1-1 2-1 3 0 0 1 1 1 2h-2-1-1c0-2 1-2 1-4 1 0 1-1 1-1z" class="G"></path><path d="M91 222c1-2 1-3 4-4l-5 12c0-1-1-1 0-2 0-2 0-4 1-6z" class="K"></path><path d="M359 457h1v-1h1c1 2 1 3 0 5h-1 0l-1 1-1-1h-1v-1c0-1 1-1 2-2v-1z" class="p"></path><path d="M359 458l1 1-2 2h-1v-1c0-1 1-1 2-2z" class="v"></path><path d="M81 130l1-2v5h1c-1 3-1 5-2 7h0l-1-1 1-9z" class="e"></path><path d="M383 394l1-1s0-1 1-1h0 0l3-3h1s0-1 1-1v-1c1-1 1-2 2-3h1c0 1-1 2-1 3-3 3-5 6-9 7z" class="T"></path><path d="M350 360h0l-3 7c0-1-1-1-2-1v-1h2c1-1 0-4 0-5h1 2z" class="U"></path><path d="M317 364c1-1 3-2 4-2v2c-2 0-4 5-6 7l-1-1 1-1c1-1 2-1 2-3 1-1 0-1 0-2z" class="M"></path><path d="M330 442l2 1c0 2-2 2 1 4h0v1c-2-2-5-2-7-4h1c1-1 2-1 3-1v-1z" class="Y"></path><path d="M301 414h0 3c0 1-1 1-2 1s-2 2-3 3-1 2-2 3c0-1 0-1 1-2v-2h0c-1 2-1 2-2 3 0 2-1 2-2 3 1-4 4-7 7-9z" class="a"></path><path d="M294 278c1 1 1 2 1 3h1v1h2v2c-2 1-3 2-3 3h-1v-9z" class="S"></path><path d="M405 599c1 2 1 4 1 7 0 2 1 4 0 6v2c-1 0-1 0-1 1h-1c0-5 2-10 1-16z" class="U"></path><path d="M592 148c1 4 1 9 1 13h-1c-1-2 0-4-2-6 0 0 1-1 1-2v-1s0-1 1-2v-2z" class="o"></path><path d="M370 487v-9l1 2 1 1c1 1 0 1 1 2l-1 1v3 1h-1v1h0c0-1 0-1-1-2z" class="X"></path><path d="M370 487l1-3h0c0 1 0 2 1 2v-2 3 1h-1v1h0c0-1 0-1-1-2z" class="l"></path><path d="M359 457s-1-1-1-2c0-2 0-2 2-2h3l-1 3h-1-1v1h-1z" class="g"></path><path d="M370 202c1-1 1-1 1 0 1 1 0 3 1 3h2c2 1 2 2 3 3 0 1 0 2-1 3-1-1-1-1-1-3h0l-3-2-1 1-1-5z" class="d"></path><path d="M376 386h1c1 1 3 0 4 0 1 1 1 1 2 0 1 0 2-1 3-2v3c-3 1-7 1-10 1-1 0-1 0-1-1s1-1 1-1z" class="B"></path><path d="M396 541v2c1 3 2 6 1 8-1 1-1 1-1 3v-1h-1l1-5c0-1-1-1-1-1v-4c1-1 0-1 1-2z" class="X"></path><path d="M396 541v2c0 2 1 4 0 5 0-1-1-1-1-1v-4c1-1 0-1 1-2z" class="G"></path><path d="M334 490h1c-1 2-3 5-5 7 0 0-1 1-2 1h0c0-2 1-3 3-5l3-3z" class="T"></path><path d="M412 613l-1-8 4 2-1 2v2c-1 0-1 1-1 2-1 0-1-1-1 0z" class="R"></path><path d="M348 440l3-8 1 1-1 2c0 2-1 4-1 6-1 0-2-1-2-1z" class="T"></path><path d="M363 453v1h2 0c1 1 1 2 2 3h-1v3l-1 1-3-5 1-3z" class="f"></path><path d="M366 457c-1 0-2 0-3-1 0-1 0-1 1-2h1c1 1 1 2 2 3h-1z" class="h"></path><path d="M451 134l1 1c1-1 2-2 3-2 0 1 0 2-1 3l-1 1c-1 0-1-1-2-1-2 0-5 0-7-1 1 0 2 1 3 0h1 1c1 0 1 0 2-1z" class="D"></path><path d="M294 235h1c0 3-1 7 0 9 0 1 0 2 1 2l-2 4v1-16z" class="E"></path><path d="M348 460l1-4 2 2c-1 3-2 5-2 9v2h0l-1 1v-7c0-1 0-2 1-2h-1v-1z" class="L"></path><path d="M416 610c1 1 2 1 2 2h1c0 2 1 4 1 6l-1 1h0c0 1 0 0-1 1v-2c-1-1-1-4-2-5v-3z" class="U"></path><path d="M581 228c1 0 2 0 3 1l1 2h-3l-1 1c0 1 0 3 1 4l-1 2-2-8 2-2z" class="w"></path><path d="M237 633h2v2 1 1 1c1-1 2-1 2-1 1 0 1 0 1 1-2 1-5 1-7 1l1-1c1-1 1-3 1-5z" class="O"></path><path d="M381 422c1-1 3-1 4-1l1-1c1 1 1 2 1 3v3l-1 1v2l-1-4c0-1-1-2-2-2-1-1-1-1-2-1z" class="M"></path><path d="M575 209c1 1 3 2 5 3h-1c1 1 1 2 2 2h1c2 1 2 1 4 0 1 0 1-1 2-1v1c-1 1-1 1-2 1h0c-1 0-2 1-3 1h-1v-1c-3 0-6-4-7-6z" class="s"></path><path d="M371 473c1 1 1 2 2 3v1s0 1 1 1c0 2-1 3 0 4l-1 1c-1-1 0-1-1-2l-1-1-1-2 1-2v-1-2z" class="g"></path><path d="M372 481c0-2 0-3 1-4 0 0 0 1 1 1 0 2-1 3 0 4l-1 1c-1-1 0-1-1-2z" class="h"></path><path d="M301 443l3 6c0 1 1 3 1 5v1 2h-1 0v-3c-2-2-2-4-3-7v-4z" class="T"></path><path d="M591 180l2-2 1 14-1-1c0-4-1-7-2-11z" class="D"></path><path d="M368 444c1-1 1-1 2-1s2-1 2 0c1 0 1 0 2 1h0c-1 0-1 0-1 1v3h-1c-1 0-2-1-2-2l-2-2zm15 2l1 1c0-2 1-2 2-2h1c-2 4-4 8-7 10 1-1 2-3 2-5l1-1h0c0-1-1-1-1-2l1-1z" class="G"></path><path d="M89 222h2c-1 2-1 4-1 6-1 1 0 1 0 2l-1 2v1c-1-1-1-1-3-2l1-2v-1c1-2 2-4 2-6z" class="Y"></path><path d="M87 228c2 1 2 3 2 4v1c-1-1-1-1-3-2l1-2v-1z" class="G"></path><path d="M294 333h1c1-2 2-4 4-7 0 1-1 2-1 3 1 0 1-1 2-1-2 3-5 6-6 10 0 1 0 3-1 3l1-8h0z" class="C"></path><path d="M314 421h2 1l3 3c1 0 2 1 3 1 1 1 1 2 1 2 0 1 1 2 1 3v1l-2-2v-2c-1-1-4-3-5-3l-3-2-1-1z" class="N"></path><path d="M355 347l3-6 1 1c0 1-1 2-2 2 0 1-1 1-1 2v5l2 2h-3l-1 1c0-2 0-4 1-7z" class="T"></path><path d="M377 220h0v-4c1-1 2-2 3-2s2-1 3-2c1 1 2 1 3 1v1c-3 0-7 3-8 5 0 0-1 0-1 1z" class="U"></path><path d="M408 132h1c2-1 3-1 5-2 1 0 1 1 2 1s1 1 2 1h0c2 1 3 0 4 0 0 1 1 1 1 1h-5c-4 0-7 0-10-1z" class="k"></path><path d="M372 456l2-2h1c1 0 2 1 3 2l-2 1-2 2c-1 1-1 1-1 2v-1c-1-1-1-1-2-1v-1-1l1-1z" class="K"></path><path d="M373 458l1-1 1-1c0 1 0 1 1 1h0l-2 2-1-1z" class="Z"></path><path d="M372 456l1 1v1l1 1c-1 1-1 1-1 2v-1c-1-1-1-1-2-1v-1-1l1-1z" class="I"></path><path d="M334 482l-1 1c0-1 0-1-1-1v-1l1-1c0-2 2-3 2-5-1-1-2 0-3-1 0-1 1-1 1-1h4c0 1-4 9-3 9z" class="N"></path><path d="M591 210c-1 2-2 3-3 4v-1c-1 0-1 1-2 1-2 1-2 1-4 0h-1c-1 0-1-1-2-2h1 1c2 1 3 1 6 0 1-1 2-2 3-2h1z" class="O"></path><path d="M386 374h3c1 1 1 1 1 2h1c0 1 0 2-1 3h1 1v2c-1 0-2 1-2 2l-1-1c-2 0-3 0-4-1v-1h1 1c2 0 2-1 3-2v-2c-1-2-2-1-4-2z" class="U"></path><path d="M334 439c1 1 1 1 2 3l1 1v1 1l1 1c0 1 1 2 2 2h0 0c1 1 1 1 1 2l-1 1v1h0c-1-2-1-3-2-5l-1-1h-2c0-1 0-2-1-4h1l-1-3z" class="T"></path><path d="M335 442l2 4h-2c0-1 0-2-1-4h1z" class="G"></path><path d="M385 340l1-1v2h0l1 1c0 2-1 6 0 8v2 1c-1 0-1 1-1 1h-1v-1c1-2 1-3 0-4h0l-1-2c1-3 1-5 1-7z" class="B"></path><path d="M295 273c2 1 2 1 3 2 0 2 1 3 0 4l-2 2h-1c0-1 0-2-1-3v-1h1v-4z" class="J"></path><path d="M599 247l1 11h-9l1-1c2 0 4-1 6-3 1-2 0-4 0-7h1z" class="D"></path><path d="M378 456c0 1 0 1 1 1-1 2-1 3-1 4 1 1 1 1 2 1h-1c-2 0-4 0-6-1 0-1 0-1 1-2l2-2 2-1z" class="a"></path><path d="M273 111h21l-1 1h-1l-1 1h0c-3 0-6-1-9 0h0v-2c-3 0-6 1-9 0z" class="D"></path><path d="M397 551c1 2 1 6 1 8-1 1-1 2-1 2v2c-1-1-2-2-3-2h0c1-1 2-3 2-4l-1-1s1-1 1-2c0-2 0-2 1-3z" class="t"></path><path d="M396 557l1-1c1 1-1 4 0 5v2c-1-1-2-2-3-2h0c1-1 2-3 2-4z" class="N"></path><path d="M349 636c2 1 2 1 3 0h1v1h4 1c2 1 3 1 5 1-9 0-18 1-27 0h1 10 3c0-1-1-1-2-2h1z" class="f"></path><path d="M343 349h1v6c1 4 0 8-2 12-1-1-1-2-1-4h0l1 1v-1c2-4 1-9 1-14h0z" class="U"></path><path d="M389 325c3 3 3 7 5 10 1 4 1 10 0 14v-3c1-8-2-14-7-20 1 1 2 1 3 2v1-1c0-1-1-1-1-2v-1z" class="D"></path><path d="M86 219c1-1 2 0 3 0 2 0 4-2 6-3h0v2c-3 1-3 2-4 4h-2c-1-1-2-1-3-1l-1-1 1-1z" class="C"></path><path d="M386 214h1 0c0 1-1 1-2 2h0c-1 1-3 2-4 3-1 0-1 1-2 2 0-1-1-1-2-1 0-1 1-1 1-1 1-2 5-5 8-5z" class="v"></path><path d="M294 394c0-1 1-2 1-3 0 2-1 5 0 6l2 2v1 2l-2 1v3c-1 1-1 2-1 4-1-5 0-11 0-16z" class="i"></path><path d="M85 227c0-2 0-6 1-8l-1 1 1 1c1 0 2 0 3 1 0 2-1 4-2 6v1-1c0-1-1-1-1-1h-1z" class="q"></path><path d="M86 227h0l1-1v-2c1-1 1-1 1-2h0 1c0 2-1 4-2 6v1-1c0-1-1-1-1-1z" class="J"></path><path d="M339 470v1c0 2 1 5-1 7l-4 4c-1 0 3-8 3-9 1-1 1-2 2-3zm9 0l1-1h0c2 4 4 7 5 11h0c-2-1-4-3-5-5 0-1-1-2-1-4v-1z" class="h"></path><path d="M370 397c0-1 1-2 0-3v-2h0 0l1 1c1 1 1 0 2 0 0-1 1 0 1-1 1 1 2 2 3 2h0c1 1 2 1 4 1l-11 2z" class="N"></path><path d="M325 370h2c1 0 1-1 2-1l1 1 1 2c-2 0-3 0-4 1h0l-1 1h-1l1 2h0l-2-1c-1-1-1-1-1-3l2-2z" class="E"></path><path d="M341 440c1 2 2 5 2 8 0 2 2 5 1 7h-1l-1 2v-2h0c0-5-1-10-1-14v-1z" class="O"></path><path d="M383 190c1 0 2-1 3 0l6 6-1 1c-1 0-1-1-2-2s0-2-2-2c-1 0-1-1-2-2h-1v1c1 1 1 1 2 1 1 1 1 3 1 4h-1l-3-4v-1h1l-1-2z" class="M"></path><path d="M301 442c1 0 2 1 3 1h1l1 1 1-1 1 1 1 1c-1 0-2 1-3 2h0v1l-2-2h0c-1 1-1 2 0 3l-3-6v-1z" class="R"></path><path d="M304 443h1l1 1h0v1l-2 1h0v-2-1z" class="j"></path><path d="M390 254c0 1 0 1 1 2v1 1 1h0c0 1 0 1 1 2h-1v1c-1 1-2 2-4 2h0v-2h-1c1-2 2-2 2-3 0 0 1 0 1-1v-1c0-1 0-2 1-3z" class="s"></path><path d="M272 606s2-4 3-5l3-6c3-5 6-11 8-17v1l-1 3c-1 4-3 7-4 11-1 1-1 3-1 5h-1-1c-2 1-5 8-6 8z" class="E"></path><path d="M273 131h1 2c2 1 5 0 7 0h3l-23 2c1 0 1 0 2-1h0-3-1c2 0 5 0 7-1h1 4z" class="Y"></path><path d="M75 200c1-1 1-1 1-2 1-3 2-6 4-8h0c0 1-1 2-1 4h0 0c1 0 1-1 2-1-3 4-5 7-5 12v3h0c-1 1-1 1-1 2h0c-1-3 0-7 0-10h0z" class="N"></path><path d="M393 147l1 4c1 5 1 10 1 15 0 1 0 3-1 4v1c-1-3 0-6 0-9 0-4-1-7-2-10l-1-3 1-1c0 1 0 3 1 4v-5z" class="x"></path><path d="M367 457c1-1 2-1 4 0v1 1 4h0l-2-1h-2c-1 0-1-1-1-2v-3h1z" class="C"></path><path d="M367 457c1-1 2-1 4 0v1h-3l-2 2v-3h1z" class="F"></path><path d="M313 455h0l-1-1h1l1-1h3c3 0 2-1 4-2 0 2 0 2 2 3h0l-3 3c-1 0-3-1-4-2-1 0-2 0-3-1v1z" class="M"></path><path d="M294 510c0 1 1 2 1 3 0 0 1 0 1 1-1 2-1 4 0 5l-1 1c0 1 1 2 1 3l2 1s-1 1-2 1c0 1 0 1 1 2h-1v1s0 1-1 1h0c0-1 0-2-1-3v-1h0v-15z" class="m"></path><path d="M392 130c1 0 1 1 2 2v6 13l-1-4v-4l1-2c-1-2-2-3-3-4h1l1-1-1-1c0-2-1-4 0-5z" class="G"></path><path d="M394 361v26h-2c0-1 1-2 1-3v-2c1-5 0-8-2-11h0c1 0 1-1 2-1h1v-9h0z" class="C"></path><path d="M386 374c2 1 3 0 4 2v2c-1 1-1 2-3 2h-1-1-1 0v-4l2-2z" class="E"></path><path d="M317 466c2 0 2 2 5 3l1-2 1 1h0l1 2 1 2-1 1h0c-1-1-1-2-2-2h-3l-1 2h-1l-1-1v-1c1 0 2-1 2-2-1-1-1-2-2-3h0z" class="D"></path><path d="M337 638l2-2c3-1 6-1 9 0 1 1 2 1 2 2h-3-10z" class="X"></path><path d="M334 427h0 1c1 1 1 1 2 1 2 2 3 3 3 5v1c1 2 1 4 1 6v1c-1-3-2-5-3-7s-3-4-4-7z" class="g"></path><path d="M369 260h1 1 0c2 1 1 2 3 3l-1 1c-1-1-2-2-3-2h0 0v1c-1 1-1 2-1 2l1 1-1 1-4-4 1-2 1-1h2 0z" class="U"></path><path d="M369 260h1l-1 2v1c-1-1-2-2-3-2l1-1h2 0z" class="J"></path><path d="M348 351c1-5 3-14 7-17 1 0 1 1 1 2-1 1-2 2-2 3v2h-1c-1 1-2 1-2 3l2 1c-1 0-1 0-1 1l-1 1v1l1 1h-2l-2 2z" class="I"></path><path d="M294 303v1c1 0 2 1 3 2h0l-1 1-1 1 1 3c-1 1-1 1-1 2 0 2 0 6 1 8-1 0-1 1-1 2h-1c0 1 1 5 0 6-1-2 0-6 0-8v-18z" class="G"></path><path d="M294 304c1 0 2 1 3 2h0l-1 1-1 1-1-4z" class="J"></path><path d="M292 552c0 2 0 3 1 5 0 1 1 2 3 4h-2v2l-4 3v-2c1-4 2-8 2-12z" class="B"></path><path d="M290 564c1-1 1-2 1-3s0-1 1-1l1 1h1v2l-4 3v-2z" class="M"></path><path d="M398 559c1 6 2 11 4 16-2-2-3-5-5-8-1-2-3-2-4-4h0c0-1 0-1 1-2 1 0 2 1 3 2v-2s0-1 1-2z" class="E"></path><path d="M84 203c2 0 3 0 4 1 1 0 1 1 1 2s-1 1-2 2c-1 0-1 1-1 1h-1c-1 0-2-1-3-2 0-1 1-2 1-3h0l-1-1h2z" class="e"></path><path d="M82 203h2l1 2h1 1l-2 1c0 1 0 1 1 2h1c-1 0-1 1-1 1h-1c-1 0-2-1-3-2 0-1 1-2 1-3h0l-1-1z" class="a"></path><path d="M383 255c-1-1-2-1-3 0-1 0-2 0-3 1s-1 2-1 4h-1s-1-1-1-2h0c-1-1-1-1-1-2-1-1-2-1-2-3-1-1-1-2-2-3h0v-1l1 1 1-2h0c0 3 1 5 2 7h5l1-3c1 1 1 1 2 1l1-1 1 3zm-18 98l1-1v-1l1-1h0l-1 1 1 1h1c1 2 2 4 4 5h2c1-1 1-2 2-1 0 1-1 1-2 2h-1-2v1h-1c-3-1-4-2-5-4v-2z" class="U"></path><path d="M577 110l12-3v9h-1c0-4 0-2-2-4-1 0-2-2-3-3l-5 2-1-1z" class="V"></path><path d="M72 232h1l-1 8h1v5h-1v8h0c1 1 1 2 1 3v1c-1 1-2 0-3 0 0-3 1-8 1-12l1-13z" class="B"></path><path d="M304 449c-1-1-1-2 0-3h0l2 2v-1c1 1 2 2 2 3h0c-1 1-1 4 0 6 0 0 1 1 1 2h1v1l-2-1-3-3v-1c0-2-1-4-1-5z" class="J"></path><path d="M340 336c1-2 1-5 1-7h1v5h1v-3 6 5h1v-1l1 1c-1 2-1 3-2 4l-3-10z" class="Q"></path><path d="M389 452h0c3-4 5-10 5-15v-1h1c0 2-1 4-1 6-2 6-5 12-8 18-1 1-3 3-4 5h-2 0c-1-1-1-2-1-3h1l1-1 3-2 1-1c2-1 3-5 4-6z" class="m"></path><path d="M546 173l6 5c3 3 5 6 9 8l-1 1h-2c-3-5-9-9-13-13l1-1z" class="C"></path><path d="M582 236c0 4 1 7 1 11 1 2 1 6 2 7 2 2 5 2 7 3l-1 1h-7l-3-20 1-2z" class="B"></path><path d="M363 262l1 2h1v-1l4 4 1 2 3 3c-2 1-2 2-3 3h-1c1-1 1-1 1-3h-1-1c0-1 1-1 0-2-2-1-3-4-5-4v-2h-1l1-2z" class="Y"></path><path d="M380 283h2 1v-1h-2 0 2 1c3 1 7 3 8 6 1 1 1 3 1 4-3-2-5-4-7-6l-6-3zm7-69c0-2 1-6 2-7h0l1 2v4c1 2 2 4 3 5 0 2 1 1 1 3h0c-2-2-3-3-6-4l-3-1c1-1 2-1 2-2z" class="E"></path><path d="M346 341c2-4 4-7 5-11 2-3 3-6 5-8v5c0 1 0 1-1 1l-2 2v1s0 1-1 1c-1 2-1 3-2 4s-2 3-2 4c-1 2-2 3-3 4l1-3z" class="U"></path><path d="M329 111h30c5 0 11-1 16 0-4 1-9 0-13 0-3 1-5 2-7 3v-1l-1-1h-4c-1-1-8-1-11-1l1 1h-3v-1c-2 0-5 0-6 1h-2l-1-1h1z" class="I"></path><path d="M294 369v-20c1 6 3 9 7 13-1 0-2-1-4 0l-2-3v8h-1v2z" class="C"></path><path d="M76 190c2-1 4-3 6-5 1-1 3-1 4-2l1 1c-2 2-5 4-7 6s-3 5-4 8c0 1 0 1-1 2 1-4 1-7 1-10z" class="B"></path><path d="M404 583l-1-2v-3c2 9 7 20 12 28l-3-3-2 2c-1-2-1-3-1-5l-2-7c-1-3-2-7-3-10z" class="S"></path><path d="M363 252l1-1c2 0 2 1 3 1v1h0c0 2-1 3-1 4 1 1 1 1 2 1 0 1 1 1 1 2h0-2l-1 1-1 2v1h-1l-1-2h1-1c0-1 0-3 1-4v-4l-1-2z" class="I"></path><path d="M330 434l4 5 1 3h-1c1 2 1 3 1 4l-2 1h0c-3-2-1-2-1-4l-2-1h0l1-1c1-1 0-2-1-3v-1h-1l-1-1c0-2 1-1 2-2z" class="n"></path><path d="M330 434l4 5 1 3h-1 0c-1-2-1-3-3-4h-1v-1h-1l-1-1c0-2 1-1 2-2z" class="h"></path><path d="M356 336l1 2c0 1 1 2 2 2l-1 1-3 6-1-1h-2c0-1 0-1 1-1l-2-1c0-2 1-2 2-3h1v-2c0-1 1-2 2-3z" class="F"></path><path d="M356 336l1 2-1 2h0l-2-1c0-1 1-2 2-3z" class="O"></path><path d="M353 345l-2-1c0-2 1-2 2-3h1c0 1 1 2 0 4h0-1z" class="n"></path><path d="M377 277h1c1-1 1 0 2 0l1 1c2 0 6-3 8-4l1 1v1c0 1 0 2-1 2v1c-1 0-1 0-2 1h-2-2c-1 0-4-1-5-1 0-1-1-2-1-2z" class="R"></path><path d="M488 144c-2 0-4-1-5-2 1 0 1-1 2-1l-1-2h-1c0 1-1 2-2 3-1-1-3 0-4-1 1 0 2-1 3-1 1-1 1-2 2-3v-1l2-3c1 2 2 5 3 7v1c1 0 1 1 0 1l1 1v1z" class="B"></path><path d="M368 282c1 0 1-1 2 0 1 0 1 0 2-1 3-1 7 0 11 1h-2 0 2v1h-1-2l-4 1v1c1 1 1 2 2 3l-4-1v-1c0-1 0-1-1-2-2-1-3-1-5-1v-1z" class="D"></path><path d="M395 516v6 9h-1c-1-1-1-2-2-3v-2c1 0 1 0 1-1h-1l1-1v-2l-2-1c-1-1 0-1-1-2l1-1h2v-1l1-1h1z" class="k"></path><path d="M391 518h2v-1l1-1v2c-1 2 1 5-1 7v-1-2l-2-1c-1-1 0-1-1-2l1-1z" class="O"></path><path d="M344 628h1l3 4 1 4h-1c-3-1-6-1-9 0l2-4c1-1 2-3 3-4z" class="u"></path><path d="M344 628h1l3 4h-3c-1 0-1 0-2-1l-2 1c1-1 2-3 3-4z" class="N"></path><path d="M464 633l7 1h1v4c-1 1-4 0-6 0 0-1 0-1-1-2 0 1-1 1-1 2h-6v-1l1 1v-1-1h1l1 1h1 1c1-1 1-2 1-3v-1z" class="E"></path><path d="M469 634h1s0 1 1 1c-1 2-1 2-2 3h-1l-1-3h0l2-1z" class="W"></path><path d="M298 377c0-1 1-2 2-2 1-1 2 0 3 0 0 1 1 1 1 2v1c0 1 1 2 1 2-2 2-4 2-7 1h-1v-1-1h0v-2h1z" class="e"></path><path d="M298 381h-1v-1-1h0v-2h1c0 1 0 2 2 3 1 0 2 0 3-1l1-1c0 1 1 2 1 2-2 2-4 2-7 1z" class="T"></path><path d="M391 137c1 1 2 2 3 4l-1 2v4 5c-1-1-1-3-1-4l-3-3c-1-1 0-3 0-5 0-1 0-2 1-2l1-1z" class="M"></path><path d="M389 145l1-1v-1h1c0 1 1 1 1 2l1 1v-3h0v4 5c-1-1-1-3-1-4l-3-3z" class="H"></path><path d="M195 117c2-1 3-1 5-2v3c-2 1-3 2-3 4v2l2 1v1h-1 0c0 2-1 2-1 3-1 0-2 0-2 1v-4l1-1c-1-1-1-2-2-3 0-1-1 0-1-1 0-2 1-3 2-4z" class="C"></path><path d="M335 446h2l1 1c1 2 1 3 2 5h0c1 1 1 2 2 3v2 1h0c1 0 1 0 1 1l-1-1-2 1h-1c-1-4-2-7-5-10l-1-1v-1l2-1z" class="k"></path><path d="M340 452h0c1 1 1 2 2 3v2 1h0c1 0 1 0 1 1l-1-1-2 1v-3-4z" class="q"></path><path d="M335 446h2l1 1c-1 1-2 2-4 2l-1-1v-1l2-1z" class="S"></path><path d="M312 472c2-1 2 0 4 1l1-1 1 1h1c0 5-1 11 0 16l-1 1v-4-1l-2-5c0-2-1-4-2-6l-2-2z" class="K"></path><path d="M312 472c2-1 2 0 4 1h-1c0 1 0 1 1 2h1v2c0 1 1 1 0 2l-1 1c0-2-1-4-2-6l-2-2z" class="m"></path><path d="M399 575c-2-5-4-8-9-11v-1h3 0c1 2 3 2 4 4 2 3 3 6 5 8v1l1 2h0v3l1 2h-2l-3-8z" class="M"></path><path d="M319 326c1-1 2-1 3 0 1 0 3 1 4 3h0v2h-2c-2-1-3-1-6 0-1 0-2 0-3 1v-3c0-1 2-2 4-3z" class="B"></path><path d="M319 326c1-1 2-1 3 0 1 0 3 1 4 3h-2c-1 0-1-1-2-1h0-2c-1 0 0-1 0-2h-1z" class="Y"></path><path d="M579 230c0-3-1-6-2-8s-5-7-4-9c1 2 2 3 3 5 2 2 6 1 8 1v1c0 2-2 3-3 4l-1 1v1c0 1 0 2 1 2h0l-2 2z" class="C"></path><path d="M580 226c-1-1-1-2-2-3 0-1 0-1 1-2v1c1 1 1 2 1 2v1 1z" class="h"></path><path d="M195 130c0-1 1-1 2-1l-2 3 1 1c1-1 1-1 1-2h2l2 1v-1c1 1 1 1 1 2l1 1c-1 1-1 2-1 2-1 2-2 2-4 2-1 0-2-1-3-2-1-2-1-4 0-6z" class="D"></path><path d="M199 131l2 1v-1c1 1 1 1 1 2s-1 2-3 3l-2-2c0-2 1-1 1-1 1-1 1-1 1-2z" class="J"></path><path d="M392 409h2l1 19h-1c0-5-1-9-5-12l-2-2h1l3-2h1v-2-1z" class="h"></path><path d="M392 412h1 0v7l-2-3c-1-1-2-1-3-2l3-2h1z" class="f"></path><path d="M389 274c2-2 4-4 5-8h1l-1 20v-1-2l-2-2c-2-2-2-3-2-5v-1l-1-1z" class="G"></path><path d="M390 275l1-1 1-1h0v1c0 3 1 5 2 8v1h0l-2-2c-2-2-2-3-2-5v-1z" class="U"></path><path d="M83 192v3h0v2c-2 2-4 3-4 5v3 1h1l-1 1h0-1v1l1 1c1 1 1 2 3 3v1h0-1c-3-2-4-5-5-8 0-5 2-8 5-12h0l2-1z" class="e"></path><path d="M83 192c3-2 9-3 12-3l-1 2c-1 0-1-1-3 0h0c-1 1-4 1-4 3 0 1 1 1 2 1 2 1 3 2 4 4-3-1-7-3-10-2v-2h0v-3zm217 242c1-2 0-2 2-3 1 2 1 5 3 7h0v1h1v1s0 1-1 2c1 0 1 1 2 1l-1 1-1-1h-1c-1 0-2-1-3-1h-1c0-3-1-3-2-5 1-1 1-2 2-3z" class="B"></path><path d="M301 442h-1c0-3-1-3-2-5 1-1 1-2 2-3 0 1 0 3 1 4 1 2 3 3 4 5h-1c-1 0-2-1-3-1z" class="a"></path><path d="M354 441h0c0 1 0 0 1 1 0 0 1 0 2 1h0l1 1-1 1c1 0 1 1 2 1h-2l-4 4-2 3c-1-1-1-1-2-1v-1c1-4 2-7 5-10z" class="G"></path><path d="M357 443h0l1 1-1 1c1 0 1 1 2 1h-2-1c-1 0-3 1-4 3 1-2 1-4 2-5s1-1 3-1z" class="L"></path><path d="M270 609c-1 2-2 3-2 5s1 3 0 5c-1 0-1 0-2 1v2l-1 1v-1h0-1c-1-1-2-1-4-1l-1-1c4-3 7-7 11-11z" class="C"></path><path d="M260 621c2-3 5-6 8-9l-3 10h-1c-1-1-2-1-4-1z" class="U"></path><path d="M593 191l1 1c1 2 1 4 1 6l1 19c0 2 0 5 1 7v3l-1-1h0c0-1 0-2-1-2v-1l1-1c-1-2-1-5-2-7v-2c0-2-1-4-1-5 1-3 1-5 1-8-1-2-1-4-2-6h0l1-1s0-1-1-2h1z" class="g"></path><path d="M295 406c0 2 1 2 3 3v1c1 0 1 1 2 1h1v3c-3 2-6 5-7 9v1-14c0-2 0-3 1-4z" class="P"></path><path d="M383 280h2 2c1-1 1-1 2-1v-1c1 0 1-1 1-2 0 2 0 3 2 5l2 2v2 1c1 3 1 5 1 8h-1v-1c-1-1 0-1-1-1 0-1 0-3-1-4-1-3-5-5-8-6 0-1 1-1 2-1l-3-1z" class="E"></path><path d="M384 282c0-1 1-1 2-1 3 1 4 3 6 4 2 2 2 5 2 8v1-1c-1-1 0-1-1-1 0-1 0-3-1-4-1-3-5-5-8-6z" class="N"></path><path d="M395 556l1 1c0 1-1 3-2 4h0c-1 1-1 1-1 2h-3v1c5 3 7 6 9 11-1 0-1 1-1 1h-1c-1 0-5-7-5-7-2-3-5-4-7-6h4c2-2 5-4 6-7z" class="P"></path><path d="M394 195c1 7 0 14 0 21l1 7-1 8c0 3 1 6 0 8v-11h0c-1-2 0-4 0-7h0c0-2-1-1-1-3-1-1-2-3-3-5v-4c1 0 1 0 1 1l1-1v-1l1 1v-1c0-1 0-3-1-4l1-1v1h0v-3l1-1h0v-5z" class="X"></path><path d="M390 213c1 0 2-1 2-2 2 2 1 5 1 7h0c-1-1-2-3-3-5z" class="N"></path><path d="M357 423c1-1 4-4 6-5-3 4-8 8-11 12l-1 2c-1 3-2 5-3 8v1h-1c1-4 1-8 2-12l4-6h0l1-1c1 1 2 1 3 1z" class="Y"></path><path d="M353 423l1-1c1 1 2 1 3 1-3 3-4 6-7 8 1-2 2-5 4-8h0-1z" class="n"></path><path d="M200 118c1 0 2 0 3 1l1 1 1 1v2l-1 1v-1c-1 0-1 0-2-1v4 1c-2 1-3 2-5 4 0 1 0 1-1 2l-1-1 2-3c0-1 1-1 1-3h0 1v-1l-2-1v-2c0-2 1-3 3-4z" class="H"></path><path d="M592 161h1c0 3-1 6-1 8 0 1 1 3 1 4v5l-2 2-1-2h0c-1-1-1-1-3-1 1-1 2-2 2-3l-1-1h-2s1-1 1-2l1-1c2-2 4-6 4-9z" class="B"></path><path d="M593 173v5l-2 2-1-2h1v-1c0-1 1-1 2-1v-3z" class="i"></path><path d="M375 422h3l1 1 2-1c1 0 1 0 2 1 1 0 2 1 2 2l1 4c-1 1-1 4-2 5l-1-2v-2c0-1 0-1-1-1l-1 1h0c-1 0-2 0-2 1l-1-1c0-1 0-1 1-2l-2-3c0-1 0-2-1-3h-1 0z" class="S"></path><path d="M381 422c1 0 1 0 2 1-1 0-2 0-2 1-1 0-1 0-2-1l2-1z" class="H"></path><path d="M375 422h3l1 1c1 1 1 1 2 1l-2 4-2-3c0-1 0-2-1-3h-1 0z" class="U"></path><path d="M81 128l1-16c1-2 0-4 1-5h1v1h2v1h0v1c1 0 1 1 1 2-1 0-2 0-3 1s-1 2-1 3v1h0c0 1 0 1-1 2h1l1-1h2c0 2 0 2 1 3-2 2-3 5-5 7l-1 2v-2z" class="I"></path><path d="M86 118c0 2 0 2 1 3-2 2-3 5-5 7l-1 2v-2c1-2 2-4 1-5v-2l1-1h2l1-2z" class="L"></path><path d="M208 111h9 33c-1 1-3 0-3 1l-15 1-1-1c-3 0-5 1-8 3l-1 1c-1-1-1-1-2-1h-1l-3-3c-2 0-1 0-2-1h-6z" class="i"></path><path d="M216 112c2-1 6 0 9 0-2 1-4 1-5 3h-1l-3-3z" class="L"></path><path d="M82 203l1 1h0c0 1-1 2-1 3 1 1 2 2 3 2h1 2c4 0 6-2 8-4h1v1c-1 0-1 2-1 3-3 2-6 4-9 4s-4-2-6-3l-2-2v1l-1-1v-1h1 0l1-1h-1v-1h0l3-2z" class="E"></path><path d="M360 315v-1l1 1h0l1-1v1h1c1 0 1 0 1 1h1 1c0 1-1 1-2 1l-1 1-3 1-4 3c-2 2-3 5-5 8-1 4-3 7-5 11h-1l8-18 1-1-1-4 1-1h4 0l2-2z" class="F"></path><path d="M361 315l1-1v1 2h0 2l-1 1-3 1c0-2 1-3 1-4z" class="k"></path><path d="M362 315h1c1 0 1 0 1 1h1 1c0 1-1 1-2 1h-2 0v-2z" class="I"></path><path d="M354 317h4c-2 1-3 3-4 5l-1-4 1-1z" class="J"></path><path d="M361 368c1 1 2 2 4 3v3h-2l-2 2h-1 0c-1 1-3 1-4 1v-1l2-1h-4v-1-1l-1-1 1-1c2-1 3-2 5-2l2-1z" class="E"></path><path d="M358 375s1 0 2-1h3l-2 2h-1 0c-1 1-3 1-4 1v-1l2-1z" class="d"></path><path d="M359 369c0 1-1 2-1 3h-1c-1 0-2 0-3 1l-1-1 1-1c2-1 3-2 5-2z" class="I"></path><path d="M352 346h2l1 1c-1 3-1 5-1 7v4c-2 1-2 2-3 3l-1-1h0 0l-1-2s-1 0-1-1v-5-1l2-2h2l-1-1v-1l1-1z" class="O"></path><path d="M350 360l-1-2s-1 0-1-1v-5 4l1 1h0 1v-1h2l-2 4h0 0z" class="L"></path><path d="M352 346h2l1 1c-1 3-1 5-1 7v4c-2 1-2 2-3 3l-1-1 2-4c1 0 1-1 1-2v-3c0-1-1-1-1-2l-1-1v-1l1-1z" class="E"></path><path d="M369 434l1 1c1 1 1 2 2 2h1l1 1h-1v2 1l3 2v1h-2 0c-1-1-1-1-2-1 0-1-1 0-2 0s-1 0-2 1l-1-1h-4c-1 0-1-1-2-1v-2h2l1-1v-1c1-1 3 0 4-1l1-2v-1z" class="F"></path><path d="M369 434l1 1c1 1 1 2 2 2h1l1 1h-1v2 1c-1 0-2 0-3-1-2 0-4-1-6-1v-1c1-1 3 0 4-1l1-2v-1z" class="L"></path><path d="M370 440l-1-2h1 1v2h1 1v1c-1 0-2 0-3-1z" class="H"></path><defs><linearGradient id="K" x1="365.552" y1="352.374" x2="363.653" y2="347.036" xlink:href="#B"><stop offset="0" stop-color="#222425"></stop><stop offset="1" stop-color="#444242"></stop></linearGradient></defs><path fill="url(#K)" d="M361 356s-1 0-1-1c0-2 0-4 2-6s6-5 9-5l1 1c-1 3-2 5-4 7h-1l-1-1 1-1h0l-1 1v1l-1 1v2h-2s0 1-1 1h-1z"></path><path d="M365 353v-2c1-3 4-5 6-6-1 2-3 4-4 6v1l-1-1 1-1h0l-1 1v1l-1 1z" class="H"></path><path d="M342 626h1c1 0 1 1 1 1v1c-1 1-2 3-3 4l-2 4-2 2h-1-2c-1 1-5 1-6 1-1-1-1-1-2-1 4-1 7-3 10-6 2-1 3-3 4-3 1-1 1-1 1-2l1-1z" class="C"></path><path d="M342 626h1c1 0 1 1 1 1-1 1-1 1-3 0l1-1z" class="E"></path><path d="M336 632c2-1 3-3 4-3-2 3-5 8-9 9h3c-1 1-5 1-6 1-1-1-1-1-2-1 4-1 7-3 10-6z" class="H"></path><path d="M365 321c6-3 11-4 18-1 2 1 4 3 6 5h0v1c0 1 1 1 1 2v1-1c-1-1-2-1-3-2-1 0-2-1-3-2-2-2-5-3-7-3-7-2-13 1-19 4 1-2 4-3 7-4z" class="S"></path><path d="M263 636h0 2v-3h1c-1 1-1 2 0 4h1c4 1 10-1 14 1h1 16 9c-7 1-14 1-21 1h-29c-4 0-10 0-14-1h0v-1h2l1-1h0v1h1c1-1 0-1 1-1 1 1 2 0 3 1h1v-1l2 1c2 0 7 0 9-1z" class="b"></path><path d="M246 636h0v1h1c1-1 0-1 1-1 1 1 2 0 3 1l-1 1h-7 0v-1h2l1-1z" class="l"></path><path d="M361 456h1l3 5 1-1c0 1 0 2 1 2h2l-2 1c0 1-1 2 0 3 0 1 1 2 1 3-1 0-1 1-1 2-1 0-1-1-2-1l-1-1c-1 1-2 0-4 0 1-1 1-3 0-5 0 0-1 1-2 0 0-1 0-1 1-2l1-1h0 1c1-2 1-3 0-5z" class="I"></path><path d="M360 461v3s-1 1-2 0c0-1 0-1 1-2l1-1z" class="v"></path><path d="M363 464c1 0 1 1 2 2-1 0-1 0-2 1h0c-1 0-1-1-2-1l2-2z" class="D"></path><path d="M381 395h1 0c-1 1-4 1-4 2-1 1-2 3-2 4-1 1-1 1-1 2h0c-1-1-3-1-4-2h0c-2-1-3 0-4-1-3 1-7 3-9 5v-1l2-2v-2l1-1c0-1 0 0 1 0h1c2-1 5-2 7-2l11-2z" class="F"></path><path d="M367 400c2 0 4-1 7 0l2 1c-1 1-1 1-1 2h0c-1-1-3-1-4-2h0c-2-1-3 0-4-1z" class="L"></path><path d="M295 367v-8l2 3c2-1 3 0 4 0 3 2 4 3 6 3l1 1c-4 1-9 2-12 6-1 1-1 3-2 5v-1-7-2h1z" class="B"></path><path d="M295 367v-8l2 3 2 3h-1c-1 0-1 0-1-1v1l-1 1c0 1 0 1-1 1z" class="H"></path><path d="M294 376v1c0 5 1 9 5 13 3 3 5 4 8 5-1 1-4 0-6-1s-3-2-5-4c1 3 2 5 4 7h0 0c1 2 3 2 4 3-2 0-4 1-6 0h-1v-1l-2-2c-1-1 0-4 0-6 0 1-1 2-1 3v-18z" class="C"></path><path d="M295 391v-2h0c1 0 1 1 1 2 0 2 2 4 3 6h1 0c1 2 3 2 4 3-2 0-4 1-6 0h-1v-1l-2-2c-1-1 0-4 0-6z" class="N"></path><path d="M394 171v-1 25 5h0l-1-1c0-1-1-2-1-3l-6-6c-1-1-2 0-3 0h-1 0v-1c1 0 1-1 1-1h2v-1l1 1c1 0 1 1 1 1 1 0 1 1 1 0 1 0 1 0 2-1l2-2c1-4 2-7 1-12 1-1 1-2 1-3z" class="F"></path><path d="M392 186v3l-2 1v-2l2-2z" class="S"></path><path d="M392 189l1 1v4c-1-1-3-3-3-4l2-1z" class="M"></path><path d="M304 321c5-3 12-4 17-2 1 0 2 0 2 1 3 1 6 2 7 4-6-4-13-5-20-2-4 1-7 3-10 6h0c-1 0-1 1-2 1 0-1 1-2 1-3 1-2 3-4 5-5z" class="E"></path><path d="M476 112h3v1c2 0 4 1 5 1h9l-1 1c-1 1-1 2-3 3h0l3 1c-2 1-2 0-3 0v1c1 0 1 1 2 1v-1l1 1-1 1-19-9h0v-1h4z" class="S"></path><path d="M476 112h3v1h0c1 1 2 1 4 2 1 1 3 2 4 4-5-2-10-6-15-6h0v-1h4z" class="L"></path><path d="M204 126c2 1 2 2 3 3l2 2h0l3 5h0c1 0 2 0 3-1l1-2c0 1 1 1 0 2h0v1 1 1c-4 1-7 2-11 2 1 0 2-1 2-2 1-1 1-1 1-2 0-2-1-3-2-4s-3-1-5-1v1l-2-1h-2c2-2 3-3 5-4 1 0 2 0 2-1zm46-15h23c3 1 6 0 9 0v2 2c-1 1-1 1-1 2l-1-1-1 1h0l-1 1h-1c-1 0-3-2-4-2-2-2-5-3-7-4h-4-9c-1 0-1-1-2 0h-3-1c0-1 2 0 3-1z" class="B"></path><path d="M279 117c-1-2-4-4-6-5 2 0 4 0 6 1 0 1 0 2 1 3h0l-1 1h0z" class="J"></path><path d="M294 190v-13l2 7c0 1-1 1 0 2v3l1 2h2v-1h1v-1h2v1c1 0 1 0 1 1h1 0c-2 1-4 3-6 4 0 1-1 2-1 3h-1l-1 4c-1 4 0 9 0 14h1v1h-1v7h-1v-34z" class="I"></path><path d="M302 189v1c1 0 1 0 1 1h1 0c-2 1-4 3-6 4 0 1-1 2-1 3h-1v-1h-1v-4c0-1 0-3 1-4l1 2h2v-1h1v-1h2z" class="J"></path><path d="M302 189v1c1 0 1 0 1 1h1 0c-2 1-4 3-6 4 0 1-1 2-1 3h-1v-1c1-2 2-4 4-5 0-1 1-1 1-2h-1v-1h2zm77 242c0-1 1-1 2-1h0l1-1c1 0 1 0 1 1v2l1 2v1 1c1 0 1-1 2-1l1-2h0c1 1 1 1 1 2s-1 2-1 3c-1 2-2 3-3 4l1 1 4-2-2 4h-1c-1 0-2 0-2 2l-1-1h0-1c-1 0-2-1-3-2l1-1c1 0 1 0 2-1v-1l-2-2c0-2 0-3 1-4v-1l-2-3z" class="D"></path><path d="M384 442h-1v-1l2-3h2c-1 2-2 3-3 4z" class="E"></path><path d="M493 123c1 1 2 2 3 2-2 5-2 13 1 18 2 4 5 4 7 7h0c-5-3-11-3-16-6v-1l-1-1c1 0 1-1 0-1l2-3v3l6 4-1-2c-2-3-1-8-1-12v-6-2z" class="C"></path><path d="M419 612l9 8h-1c-2-1-3-3-5-4l2 8 1 1c0 1 0 2 1 2l-1 1-1 7v2h-2c-1-2 0-3-1-5h0-3v1l-1-1c1-2 3 0 4-1v-2-1c0-1 0-1-1-2s-2-3-4-4l3 1c1 1 1 0 2 0 0-2-1-3-1-5s-1-4-1-6z" class="V"></path><path d="M424 624l1 1c0 1 0 2 1 2l-1 1v-1h-2c-1-1-1-1-1-2 1-1 1 0 2-1z" class="S"></path><path d="M377 425l2 3c-1 1-1 1-1 2l1 1 2 3v1c-1 1-1 2-1 4l2 2v1c-1 1-1 1-2 1l-1 1h0v-3c0-1 0-1-1-1 0-1-1-1-2-1v4l-3-2v-1-2h1l-1-1v-1h1l1-1 1-1-2-4c0-2 0-2 1-3 1 0 1-1 2-2z" class="T"></path><path d="M377 425l2 3c-1 1-1 1-1 2-1-1-1-2-2-3h-1c1 0 1-1 2-2z" class="m"></path><path d="M376 434l2 1c-1 1 0 1-1 1v2c-2 1-2 0-3 0l-1-1v-1h1l1-1 1-1z" class="Y"></path><path d="M75 210h0v6c1 0 2 1 2 2 1 0 2 1 3 1 0 2 0 4 1 6l2 3c-1 1-2 1-2 2h-3l1-3h0c-2 0-4 1-5 2l-1 3h-1l3-22z" class="j"></path><path d="M75 217c1 0 1 3 1 4l2 1c0 2 0 2-2 3l-2 2v-1c0-3 1-6 1-9z" class="T"></path><path d="M336 631v1c-3 3-6 5-10 6h-19-9-16-1l1-1c2 0 3-2 5-2l2-1h1v1l1 1h7 6c1 1 5 1 6 1 10 1 19 1 26-6z" class="c"></path><path d="M475 124h1v2c1 0 1 0 2-1v2l-2 1c1 1 2 0 3 1 1 2 1 5 0 6-2 2-3 3-6 4l-3-3c-1-1-1-2-1-3h0l-2-1c2 0 3-1 4-1s2-1 3-1v-3c0-1 1-2 1-3z" class="S"></path><path d="M474 130c1 0 2 1 3 2 0 1 0 2-1 3h-2c-2 0-3-2-5-2h0l-2-1c2 0 3-1 4-1s2-1 3-1z" class="N"></path><path d="M313 413h5c3 0 4 0 6 1h1l1 1c2 0 3 2 4 3 2 2 5 5 7 8h0v2c-1 0-1 0-2-1h-1 0c-5-4-8-8-13-12 0 1-1 0-2 0-4-2-10-1-14-1 2-1 5 0 6-1h2z" class="d"></path><path d="M321 415h1l9 7c2 1 3 4 5 4h1v2c-1 0-1 0-2-1h-1 0c-5-4-8-8-13-12z" class="k"></path><path d="M308 317l2-1c3-1 8-1 11-1 4 1 10 4 12 7s3 5 4 8c-1-1-3-2-4-3s0-1-1-1h0v-1c-1 0-1-1-2-1-1-2-4-3-7-4 0-1-1-1-2-1-5-2-12-1-17 2v-1h0 1c-1-1-1 0-2 0 1-2 3-2 5-3z" class="T"></path><path d="M86 231c2 1 2 1 3 2-1 5-3 9-4 13v12H73c-1 0-2 0-3-1 1 0 2 1 3 0v-1h1c3 1 7 0 9-1 2-2 2-7 2-9-1 1-2 3-3 4h-1v-1c0-2-1-2-2-3h0l1-1v-1c0-1-2-1-2-2s1-3 2-3c4 1 1 3 3 5 1 0 1 0 2-1 1-2 2-6 1-8h1c0-1 0-1-1-2 0-1 0-1-1-1l1-1z" class="i"></path><path d="M387 315c2-2 4-4 5-7l3-3c-1 3-1 6-1 9v18h0c0-1 0-2-1-4-1-3-3-7-6-9h0v-1-3z" class="B"></path><path d="M393 312v1c0 2 0 3-1 5l-3-1h-1l5-5z" class="u"></path><path d="M593 215h1c1 2 1 5 2 7l-1 1v1c1 0 1 1 1 2h0l1 1 1 7v1h-1v-2c0-1-1-3-1-4h-2c-1 0 0 1-2 1l-2-1h0l-1 1h-1v-1h-2v-2l3-7h0l4-4v-1z" class="V"></path><path d="M593 215h1c1 2 1 5 2 7l-1 1v1c1 0 1 1 1 2h0-1v-1c-1-1-1-2-3-3h0v-1l1-1c1-1 1-2 1-4l-1-1z" class="a"></path><path d="M590 229l-1-1c0-2 1-3 2-4 1 0 1 0 2 1v1 1c1 0 1 1 3 2h0-2c-1 0 0 1-2 1l-2-1z" class="U"></path><path d="M394 228h0v11c0 4 0 8 1 12h0c-3-5-6-8-11-11h0c-2-1-4-1-6-1 2 0 5-1 7-2 3-2 6-5 9-9z" class="E"></path><path d="M392 234h1v2c0 1 1 4 0 4h-1l-3-2 3-4z" class="T"></path><path d="M351 442c1-2 2-3 2-4l1-1c1-2 2-2 4-3l-3 6-1 1c-3 3-4 6-5 10v1c1 0 1 0 2 1-1 1-1 2-2 3l-1 4h0l-1-2c-1 0-2-2-3-3 1-2-1-5-1-7h1c1-1 1-2 1-3 1-1 1-3 2-4h1v-1s1 1 2 1l1 1z" class="H"></path><path d="M349 451v1c1 0 1 0 2 1-1 1-1 2-2 3l-1 4h0l-1-2 2-7z" class="g"></path><path d="M348 440s1 1 2 1l1 1c-1 0-3 2-3 2 0 1 1 1 1 3-1 0-2 3-2 4 0 2 1 2 0 3h-1c-1 0-1-1-1-1h0c-1-5 2-8 3-12h0v-1z" class="W"></path><path d="M340 112l-1-1c3 0 10 0 11 1h4l1 1v1c-5 3-8 7-11 12h0c0-1 0-1-1-2v-1l-1-1h1c-1-1-1-1 0-2v-3c-1-2-2-4-3-5z" class="E"></path><path d="M350 112h4c-2 2-4 3-6 4-2 2-3 5-4 7h-1v-2c1-3 3-6 6-7l-1-1h0c-1 1-2 2-4 3l-3-4h9z" class="H"></path><path d="M370 422h4 1 1c1 1 1 2 1 3-1 1-1 2-2 2-1 1-1 1-1 3l2 4-1 1-1 1h-1v1h-1c-1 0-1-1-2-2l-1-1h0c0-1-1-3-2-4s-2 0-3-1l1-2v-2c2 0 3-1 5-2v-1z" class="D"></path><path d="M370 422h4c-1 1-3 2-4 3v-2-1z" class="a"></path><path d="M365 427v-2c2 0 3-1 5-2v2c-2 1-4 1-5 2z" class="N"></path><path d="M369 434l2-1c1 0 2 0 3 1s-1 1 1 1l-1 1h-1v1h-1c-1 0-1-1-2-2l-1-1h0z" class="G"></path><path d="M364 429c1 1 2 0 3 1s2 3 2 4h0v1l-1 2c-1 1-3 0-4 1v1l-1 1h-2v2h0-1c-1 0-2 0-3 1h0c-1-1-2-1-2-1-1-1-1 0-1-1h0l1-1 3-6c0-1 2-2 3-3v-1h2 1v-1z" class="Q"></path><path d="M358 438c1 1 1 2 3 2v-1-2h1s1 1 1 2h0v1h-2v2h0-1c0-1-1-2-2-2v-2z" class="k"></path><path d="M361 431v-1h2v1c2 0 3 0 4 2 0 1 0 2 1 2v1h-3l-1 1c-1-1-1-1-1-2v-1h-2v-1-2z" class="C"></path><path d="M358 434c0-1 2-2 3-3v2 1h2v1c-1 0-2-1-3 0s-1 2-2 3v2c1 0 2 1 2 2-1 0-2 0-3 1h0c-1-1-2-1-2-1-1-1-1 0-1-1h0l1-1 3-6z" class="v"></path><path d="M355 440h2 0 1c1 0 2 1 2 2-1 0-2 0-3 1h0c-1-1-2-1-2-1-1-1-1 0-1-1h0l1-1z" class="D"></path><path d="M391 371c-2-1-3-3-5-4h-2-6c6-3 11-6 14-12 1-1 1-4 2-4v10h0v9h-1c-1 0-1 1-2 1h0z" class="B"></path><path d="M392 360h0c1 1 2 3 2 4-1 1-2 2-2 3h0-1c-1-1-2-1-3-3l2-1c0-1 1-2 2-3z" class="M"></path><path d="M294 449v-1s-1-1 0-2c0-2-1-5-1-7 0-1 0-1 1-1h0c0 1 1 3 0 4h0c2 6 5 17 11 19 1 1 1 1 2 1v2l1 1v1 1l3 3 1 2 2 2c-1 1-2 1-3 1l-2-3c0-1-4-4-5-5v-1l-5-5-2-4-3-9v1z" class="d"></path><path d="M305 464h2l1 1v1c-1 0-2-1-3-2z" class="G"></path><path d="M308 467l3 3c-1 1-1 1-2 1l-1-1c0-1-1-2-1-3h1z" class="U"></path><path d="M294 442c2 6 5 17 11 19 1 1 1 1 2 1v2h-2l-3-3c-3-3-5-9-6-13-1-2-2-3-2-6z" class="F"></path><path d="M294 190h0c0-8-1-17 0-26v4h1 1c0 2 0 3 1 5h0l2 4c1 2 3 4 4 6h0-1l-1 1h0l1 1c0 1 0 1-1 2 0 1 0 1-1 2h0v1h-1v1h-2l-1-2v-3c-1-1 0-1 0-2l-2-7v13z" class="H"></path><path d="M296 189v-3c-1-1 0-1 0-2 1 2 2 5 3 6v1h-2l-1-2z" class="D"></path><path d="M295 168h1c0 2 0 3 1 5h0l2 4c1 2 3 4 4 6h0-1l-1 1h0l-1 1h-1c-2-2-4-8-4-11v-6z" class="F"></path><path d="M299 177c1 2 3 4 4 6h0-1v-1h-3v-1-4z" class="K"></path><defs><linearGradient id="L" x1="317.492" y1="458.309" x2="309.398" y2="468.558" xlink:href="#B"><stop offset="0" stop-color="#1e1e1e"></stop><stop offset="1" stop-color="#393838"></stop></linearGradient></defs><path fill="url(#L)" d="M313 455v-1c1 1 2 1 3 1 1 3 3 5 5 6h0l-1 1h-2c0 1 1 1 0 2h0c-1-1 0-1-1-1l-1 1v2h1 0c1 1 1 2 2 3 0 1-1 2-2 2v1l-1 1c-2-1-2-2-4-1l-1-2-3-3v-1-1l-1-1v-2c2 0 5 0 8-1v-1c-1-1-2-3-2-5z"></path><path d="M317 471c-2 0-2-2-4-3-1 0-2-1-3-2v-1h3 0l1-1h1v3h1v-1h1 0c1 1 1 2 2 3 0 1-1 2-2 2z" class="s"></path><path d="M380 229l4-1c-2-2-3-4-4-6 2-1 4-3 7-3 1 1 4 1 4 3 1 2 1 4 0 5-1 3-5 5-8 6l-4 1c0-1 0-1-1-2 1-1 1-2 2-3z" class="B"></path><path d="M388 223c1 1 1 2 1 3-1 1-1 1-2 1h-1v-1l1-1v-2h1z" class="W"></path><path d="M386 227c-1 0-2-1-2-2-1 0-1-1-1-2 1-1 1-1 3-2l2 2h-1v2l-1 1v1z" class="M"></path><path d="M584 247h4c1-1 1-1 2-1 0 2 0 3 1 5 1-1 1-3 1-4h6c0 3 1 5 0 7-2 2-4 3-6 3-2-1-5-1-7-3-1-1-1-5-2-7h1 0z" class="u"></path><path d="M322 436l1-1c2 2 3 2 6 2h1v1c1 1 2 2 1 3l-1 1h0v1c-1 0-2 0-3 1h-1-1c-1-1-3-2-5-1l-1 1h0s0 1 1 2c-1 0-1 1-2 1s-2-1-3-2l1-1h-1 0c-2 0-3 0-4-1h0l1-1v-1-2 1c2 0 4 0 6-1 1-1 2-1 3-2 0-1 0-1 1-2v1z" class="G"></path><path d="M327 440l1 1c-1 0-1 1-1 2v1h-1v-1l-1-2s1 0 2-1z" class="i"></path><path d="M312 439v1c2 0 2 0 4 2-2 0-3 0-5 1h0l1-1v-1-2z" class="d"></path><path d="M322 436l1-1c2 2 3 2 6 2h1v1c1 1 2 2 1 3l-1 1h0v1c-1 0-2 0-3 1v-1c0-1 0-2 1-2l-1-1c-4 0-8 0-11 2-2-2-2-2-4-2 2 0 4 0 6-1 1-1 2-1 3-2 0-1 0-1 1-2v1z" class="J"></path><path d="M322 436l1-1c2 2 3 2 6 2h1v1c1 1 2 2 1 3l-1 1h0v1c-1 0-2 0-3 1v-1c0-1 0-2 1-2 0-3-4-3-6-5z" class="E"></path><path d="M377 193h5 1l3 4h0v5c-2 2-3 3-5 3s-4 0-5-2c-1 1-1 1-2 1 0-1 0-2-1-2h-1c0-1 0-2 1-2h0c1-2 2-3 3-4h1v-1-2h0z" class="J"></path><path d="M381 197c0 1 1 1 1 2l-1 2h-1c-1-2-1-2-1-4h2z" class="I"></path><path d="M381 197c-1-1-2-1-3-1l1-1h2 0 1c0 1 1 2 2 3 0 1 0 2-1 3 0 1-1 1-1 1l-1-1 1-2c0-1-1-1-1-2z" class="L"></path><path d="M376 196h1 0v4l3 3h-1v1l2 1c-2 0-4 0-5-2h0c0-1 1-3 1-4s-1-1-1-2v-1z" class="Y"></path><path d="M376 196v1c0 1 1 1 1 2s-1 3-1 4h0c-1 1-1 1-2 1 0-1 0-2-1-2h-1c0-1 0-2 1-2h0c1-2 2-3 3-4z" class="H"></path><path d="M377 193h5 1l3 4h0c-1 1-1 0-2 1-1-1-2-2-2-3h-1 0c-1 0-2-1-3 0h-1v-2h0z" class="Y"></path><path d="M377 193h4v2h0 0c-1 0-2-1-3 0h-1v-2z" class="D"></path><path d="M384 198c1-1 1 0 2-1v5c-2 2-3 3-5 3l-2-1v-1h1c1 0 1-1 2-1 0 0 1 0 1-1 1-1 1-2 1-3z" class="G"></path><path d="M374 287l4 1h2s0 1-1 2h1l1 1v-2l1-1v2l1 1 1-2c1 0 2 1 3 2v3s-1 1-1 2l-2 4h-3 0 0-1c0-2 1-2 1-3h-4 0c1-1 1-1 1-2h-1-2c-2 1-2 2-3 3h0l-2-2h1v-1l3-2c-1 0-1-1-1-1l-1-1 1-1 1-3z" class="E"></path><path d="M374 293c1 0 2-1 3-1l2 2 2 2c1 0 2-1 3-1h2 0v1l-2 4h-3 0 0-1c0-2 1-2 1-3h-4 0c1-1 1-1 1-2h-1-2c-2 1-2 2-3 3h0l-2-2h1v-1l3-2z" class="U"></path><path d="M384 295h2 0v1l-2 4h-3v-1c0-2 2-3 3-4z" class="P"></path><path d="M342 457l1-2h1c1 1 2 3 3 3l1 2h0v1h1c-1 0-1 1-1 2v7 1l-1 1h-1c0-1-1-1-1-1h-1v-1c-1 0-1 0-1 1-1 1-1 3-1 5h0c-1-1-2-1-2-2s0-2-1-3v-1c0-2 0-4 1-7v-1-1l-1-2h1l2-1 1 1c0-1 0-1-1-1h0v-1z" class="a"></path><path d="M340 462l1 1v3l-1 1v-4-1z" class="O"></path><path d="M340 459l2-1-1 3h-1l-1-2h1z" class="s"></path><path d="M342 457l1-2h1c1 1 2 3 3 3l1 2h0v1c-1 0-1-1-2-1h0c-1 0-1 0-2-1v1l-1-1c0-1 0-1-1-1h0v-1z" class="l"></path><path d="M348 461h1c-1 0-1 1-1 2v7 1l-1 1h-1c0-1-1-1-1-1h-1v-1c1-2 0-5 1-7h2c0-1 1-1 1-2z" class="M"></path><path d="M308 515l1 1c-3 2-4 5-6 8l1 1c0 1-1 2-1 3l-1 3v-2c-2 1-2 3-2 4l-1 2-2 8c0-2 0-3 1-4v-5c-1 1-2 3-3 4v1c0 2-1 3-1 5v4h0l-1 2-1-2c1-1 0-3 1-4l1-19h0v1c1 1 1 2 1 3h0c1 0 1-1 1-1v-1h1c-1-1-1-1-1-2 1 0 2-1 2-1 4-1 8-6 10-9z" class="G"></path><path d="M303 524l1 1c0 1-1 2-1 3l-1 3v-2c-2 1-2 3-2 4l-1 2-2 8c0-2 0-3 1-4v-5c-1 1-2 3-3 4v1c0 2-1 3-1 5v4c0-1 0-1-1-1 0-6 4-15 7-20l3-3h0z" class="d"></path><path d="M424 624l-2-8c2 1 3 3 5 4h1s1 0 1 1c1 0 1 0 2 1h0l1 2c0 2 0 6-1 8 0 0 1 1 2 1v1 1c1 0 1 1 2 2-2 1-8 0-10 0-1-1 0-1-1-2l1-7 1-1c-1 0-1-1-1-2l-1-1z" class="W"></path><path d="M361 394c1 0 3 1 3 2s-1 2-2 3c-1 0-1-1-1 0l-1 1v2l-2 2v1l-1 2c-1 2-4 4-5 7l-1 1h0v-3c-1 0-1 0-2 1h-1c0 1-1 1 0 2l1 1c-2 0-3 1-5 0l1-1c0-1 1-2 2-3l2-3c1-2 2-6 3-8l3 2 2-2c0-1-1-2-1-3l-1-1c0-1 0-1 1-1 2-2 3-2 5-2z" class="D"></path><path d="M360 396h1v1l-1 1h0l-1-1 1-1z" class="Q"></path><path d="M352 401l3 2c0 1 0 1-1 2v2h-1c-2 1-3 2-4 2 1-2 2-6 3-8z" class="F"></path><path d="M327 334c2-1 4-1 6 0s3 4 4 7c1 1 1 3 1 5 1 3 2 6 2 9l-1 1-2-2h0 0-3v1h0 0c0-5-2-10-4-14-1-1-2-3-4-4 2-1 2-1 2-3h-1z" class="G"></path><path d="M332 340l-1-1c0-1-1-1-1-3h1c1 0 1 0 2 1v1l-1 2z" class="C"></path><path d="M334 344h3v1 1h-1v1l-1 2v-2l1-1h-3l1-2h0z" class="D"></path><path d="M333 338c1 2 2 3 2 5l-1 1h0-1c-1-1-1-2-1-4l1-2z" class="T"></path><path d="M338 346c1 3 2 6 2 9l-1 1-2-2h0c-1-1-2-3-2-5h0l1-2v-1h1 1z" class="r"></path><path d="M336 347l1 1 1 1-1 1-1-1c1 1 1 2 2 2v3h-1 0c-1-1-2-3-2-5h0l1-2z" class="d"></path><path d="M371 459c1 0 1 0 2 1v1c2 1 4 1 6 1 0 1 0 2 1 3h0 2c-3 2-5 5-7 9 0 1-1 3-1 4-1 0-1-1-1-1v-1c-1-1-1-2-2-3v2c-2-1-3-3-4-4 0-1 0-2 1-2 0-1-1-2-1-3-1-1 0-2 0-3l2-1 2 1h0v-4z" class="G"></path><path d="M371 473h-1v-2c1 0 1 0 2-1 1 1 1 1 1 2l-2 1z" class="B"></path><path d="M369 462l2 1v1c2-1 4 1 6 1v1c-1 2-2 4-4 4-1 0-1 0-1-1v-1l1-1h-2v-1h-1c0 1-1 2-2 3h0c0-1-1-2-1-3-1-1 0-2 0-3l2-1z" class="J"></path><path d="M500 113h4v1h-1v1l4-1c3 0 6-1 9-1-7 2-13 4-18 10l-1 1-1 1c-1 0-2-1-3-2v2l-2-2v-1l1-1-1-1v1c-1 0-1-1-2-1v-1c1 0 1 1 3 0l-3-1h0c2-1 2-2 3-3l1-1v-1h7z" class="P"></path><path d="M492 121l1 1v1 2l-2-2v-1l1-1z" class="Y"></path><path d="M500 113h4v1h-1v1h-4 0v3c-3 1-4 2-6 2l-1-1-3-1h0c2-1 2-2 3-3l1-1v-1h7z" class="u"></path><path d="M350 360l1 1c1-1 1-2 3-3 0 2 3 5 5 7h-1l3 3-2 1c-2 0-3 1-5 2h-2c-1 0-2 0-3-1-2 3-3 7-4 11h0l-1-2s0-1-1-2h0c0-2 1-5 0-7h1c1-1 1-2 1-4 1 0 2 0 2 1l3-7h0z" class="I"></path><path d="M345 366c1 0 2 0 2 1l-3 12s0-1-1-2h0c0-2 1-5 0-7h1c1-1 1-2 1-4z" class="M"></path><path d="M349 370c1-3 3-6 4-9 2 2 3 3 5 4l3 3-2 1c-2 0-3 1-5 2h-2c-1 0-2 0-3-1z" class="W"></path><path d="M73 240v-1c1 0 2 1 3 2h0l2 1c0 1 2 1 2 2v1l-1 1h0c1 1 2 1 2 3v1h1c1-1 2-3 3-4 0 2 0 7-2 9-2 1-6 2-9 1h-1c0-1 0-2-1-3h0v-8h1v-5z" class="u"></path><path d="M73 240v-1c1 0 2 1 3 2h0l2 1c0 1 2 1 2 2v1l-1 1h0c-1 1-1 1-2 1l-1-1c0 1 0 1-1 1s-1-1-2-2h0v-5z" class="E"></path><path d="M73 240v-1c1 0 2 1 3 2h0c0 1 0 2-1 3 0 1-1 1-2 1h0v-5z" class="j"></path><path d="M377 297h0 4c0 1-1 1-1 3h1 0 0 3c2 1 4 0 6 1l1-1c0 1 0 1 1 1v1 1 1h0v2c0 1 0 1-1 1v2c1-1 1-2 2-3l1-1h1l-3 3c-1 3-3 5-5 7-2 1-3 1-5 0h-3l1-1h-4c3-3 8-3 11-5 1-2 1-3 1-5l-1 1-2 2h-1c-1-2-2-3-3-4h0l-1-1c-1 0-3 0-4-1 0-2 0-2 1-4z" class="C"></path><path d="M380 302h2v-1h1l1 1c1 0 2 0 4 1v1l-1 1c0-1 0-1-1-1h-1c-1 0-1 0-2-1v-1c-1 0-1 1-2 1h0l-1-1z" class="J"></path><path d="M381 303c1 0 1-1 2-1v1c1 1 1 1 2 1h1c1 0 1 0 1 1l-2 2h-1c-1-2-2-3-3-4z" class="P"></path><path d="M392 301v1 1 1h0v2c0 1 0 1-1 1v2c1-1 1-2 2-3l1-1h1l-3 3c-1 3-3 5-5 7-2 1-3 1-5 0h-3l1-1c2 0 5 0 6-1 2-2 4-3 4-6 1-2 1-3 2-6z" class="T"></path><path d="M297 306l5 3h0v2h-1c-1 0 0 0-1 1l2 1v2c2 1 3 1 5 1l1 1c-2 1-4 1-5 3 1 0 1-1 2 0h-1 0v1c-2 1-4 3-5 5-2 3-3 5-4 7h-1 0v-4c1-1 0-5 0-6h1c0-1 0-2 1-2-1-2-1-6-1-8 0-1 0-1 1-2l-1-3 1-1 1-1z" class="E"></path><path d="M295 313c2 1 3 3 5 3l1 1c-2 1-3 0-5 0v4c-1-2-1-6-1-8z" class="H"></path><path d="M294 333c1-2 1-4 2-5 1-4 3-7 7-8 1 0 1-1 2 0h-1 0v1c-2 1-4 3-5 5-2 3-3 5-4 7h-1z" class="a"></path><path d="M297 306l5 3h0v2h-1c-1 0 0 0-1 1l2 1v2h-1-1v1c-2 0-3-2-5-3 0-1 0-1 1-2l-1-3 1-1 1-1z" class="d"></path><path d="M296 311c2 1 3 3 5 4h-1v1c-2 0-3-2-5-3 0-1 0-1 1-2z" class="D"></path><path d="M297 306l5 3h0v2h-1c-1 0 0 0-1 1-2-2-3-3-4-5l1-1z" class="G"></path><path d="M366 497l4 6h2 0l1 1v-1l1 1v-1c-1-1-1-1-1-2h0v-3l3 10h1 0 1l1 2c0-1 1-1 1-2 0 1 0 2 1 2 0 2 2 3 3 4s2 1 3 1v1h0 3l1 2-1 1c1 1 0 1 1 2l2 1v2l-1 1h1c0 1 0 1-1 1v2c1 1 1 2 2 3v3l-1 2-6-12-2-3-5-5c-2-2-4-4-5-6 0-1 0-1-1-1-3-4-6-8-8-12z" class="H"></path><path d="M390 523c0-1 0-1 1-2l2 1v2l-1 1h1c0 1 0 1-1 1l-2-3z" class="B"></path><path d="M379 510c0-1 1-1 1-2 0 1 0 2 1 2 0 2 2 3 3 4s2 1 3 1v1h0 3l1 2-1 1c1 1 0 1 1 2-1 1-1 1-1 2-2-2-4-4-5-6-2-2-6-4-6-7z" class="E"></path><path d="M387 516h0 3l1 2-1 1s-1 0-1-1l-1-1h-1-1l1-1z" class="s"></path><path d="M366 497l4 6c2 2 3 3 4 5 2 1 2 2 4 3 1 1 2 3 3 4 3 3 6 5 8 9 1 1 2 2 3 4 1 1 1 2 2 3v3l-1 2-6-12-2-3-5-5c-2-2-4-4-5-6 0-1 0-1-1-1-3-4-6-8-8-12z" class="E"></path><path d="M387 350c1-1 1-2 2-3v-1c1 2 1 3 1 5 0 3-3 8-6 10-3 3-8 4-12 3-3 0-8-3-10-6l-1-2h1c1 0 1-1 1-1h2c1 2 2 3 5 4h1 1c3 1 6 0 9-2s3-5 4-8h0c1 1 1 2 0 4v1h1s0-1 1-1v-1-2z" class="e"></path><path d="M487 111h66l11 4h0c-11-3-24-3-36-3h-11l-1 1c-3 0-6 1-9 1l-4 1v-1h1v-1h-4-7v1h-9c-1 0-3-1-5-1v-1h-3l-2-1c4 0 10 1 13 0z" class="R"></path><path d="M508 112h-4v-1h13c-1 1-2 1-3 1h-6z" class="w"></path><path d="M514 112h3l-1 1c-3 0-6 1-9 1l-4 1v-1h1v-1c1-1 2-1 4-1h0 6z" class="H"></path><path d="M504 113c1-1 2-1 4-1 0 1 1 1 2 1h0-2l-1 1-4 1v-1h1v-1z" class="T"></path><path d="M479 112h5l16 1h-7v1h-9c-1 0-3-1-5-1v-1z" class="a"></path><path d="M323 454c1-1 3-1 4-1 1 1 1 1 2 1 0 1-1 2-1 3l4 3v4c-2 0-4 0-5 2v1c0 2 3 3 2 5 0 1-1 2-2 3-1-1 0-3-1-3h0l-1-2-1-2h0l-1-1-1 2c-3-1-3-3-5-3h-1v-2l1-1c1 0 0 0 1 1h0c1-1 0-1 0-2h2l1-1h0c-2-1-4-3-5-6 1 1 3 2 4 2l3-3z" class="F"></path><path d="M323 457c1 0 1 0 2 1h0c1 1 1 2 1 3l-1 1v1l-1 1c-1 1-1 2-1 3l-1 2c-3-1-3-3-5-3h-1v-2l1-1c1 0 0 0 1 1h0c1-1 0-1 0-2h2c0 1 1 2 2 2 0-1 0-1 1-2h0c0-2-1-3-1-4l1-1z" class="L"></path><path d="M323 457c1 0 1 0 2 1h0v1c-1 0-2 0-2-1v-1z" class="J"></path><path d="M577 177c-1 0-2-1-2-1v-1l3-1c3 0 6-1 9-3 0 1-1 2-1 2h2l1 1c0 1-1 2-2 3 2 0 2 0 3 1h0l1 2c1 4 2 7 2 11h-1c1 1 1 2 1 2l-1 1-1-2c-1-4-6-7-10-8l1-1v-1l-6-2c1-1 1-2 1-3z" class="S"></path><path d="M586 173h2l1 1c0 1-1 2-2 3l-1 1h0l-2-2 2-3z" class="H"></path><path d="M577 177l6 3-1 2h0l-6-2c1-1 1-2 1-3z" class="N"></path><path d="M583 180c3 1 4 2 6 4h1v3l-1-1c-2-2-4-3-7-3v-1h0l1-2z" class="a"></path><path d="M586 178l1-1c2 0 2 0 3 1h0l1 2c1 4 2 7 2 11h-1l-1-4v-2c0-4-2-5-5-7z" class="n"></path><path d="M582 183c3 0 5 1 7 3l1 1h1l1 4c1 1 1 2 1 2l-1 1-1-2c-1-4-6-7-10-8l1-1z" class="V"></path><path d="M394 111h12 18 63c-3 1-9 0-13 0l2 1h-4v1h0-3c2 1 3 2 4 4l2 2h-1c-1 0-1 0-1 1h-1c-1 1-1 2-2 2h-1v-2c0-1-1-2-1-4-2-2-4-3-7-4-4-1-8 0-12 0h-7c-2 0-4 1-6 2v-1c-1 0-2 0-3 1-2 2-6 2-7 5-1-1-2-1-2-1l-2-1-2 1v-1c4-2 8-4 12-5h-6-1v-1h-5v1c-1 1-2 1-3 1h-3c0-1-1-1-2-1 2 0 4 0 6-1h-6-15-3z" class="r"></path><path d="M422 117c2-1 9-4 11-3-2 2-6 2-7 5-1-1-2-1-2-1l-2-1z" class="O"></path><path d="M461 112c2-1 7-1 10-1l-1 1h-2 0c0 1 1 1 1 1 2 1 3 2 4 4l2 2h-1c-1 0-1 0-1 1h-1c-1 1-1 2-2 2h-1v-2c0-1-1-2-1-4-2-2-4-3-7-4z" class="B"></path><path d="M473 117l2 2h-1c-1 0-1 0-1 1h-1c-1 1-1 2-2 2h-1v-2-2c0-1 3-1 4-1h0z" class="e"></path><path d="M81 225c2 0 2 0 3 2h1 1s1 0 1 1v1l-1 2-1 1c1 0 1 0 1 1 1 1 1 1 1 2h-1c1 2 0 6-1 8-1 1-1 1-2 1-2-2 1-4-3-5-1 0-2 2-2 3l-2-1h0c-1-1-2-2-3-2v1h-1l1-8 1-3c1-1 3-2 5-2h0l-1 3h3c0-1 1-1 2-2l-2-3z" class="W"></path><path d="M81 225c2 0 2 0 3 2h1 1s1 0 1 1v1l-1 2-1 1c1 0 1 0 1 1 1 1 1 1 1 2h-1c0 1 0 2-1 3 0-1 0-1-1-1v-2c-1-1-2-2-3-2-3 0-5 1-7 3 1-2 2-4 4-6h3c0-1 1-1 2-2l-2-3z" class="B"></path><path d="M81 225c2 0 2 0 3 2h1 1s1 0 1 1v1l-1 2-1 1-1-1-1-3-2-3z" class="M"></path><path d="M81 225c2 0 2 0 3 2v3 1l-1-3-2-3z" class="H"></path><path d="M358 405c2-2 6-4 9-5 1 1 2 0 4 1h0c1 1 3 1 4 2h0v2l2 3v1h-3c2 0 5 0 7 1l2 2c-3 1-5 0-7 2-1 0-3 0-5 1 0-1-1-1-1-2l1-1v-1c-1 0-1 0-2 1 0-1-1-2-3-2h-4c-1 0 0-2-1-3h-3-1l1-2z" class="O"></path><path d="M368 407l-6-3c2-1 5-2 7-3 0 1-1 2-2 2 0 0-1 0-1 1v1h1v-1h2c1 1 1 2 2 3h-3z" class="D"></path><path d="M371 401h0c1 1 3 1 4 2h0v2l2 3v1h-3l-3-2c-1-1-1-2-2-3h-2v1h-1v-1c0-1 1-1 1-1 1 0 2-1 2-2h2z" class="E"></path><path d="M371 401c1 1 3 1 4 2h0v2c0 1-1 1-2 1 0-1-1-1-1-2s-1-2-1-3z" class="G"></path><path d="M368 407h3l3 2c2 0 5 0 7 1l2 2c-3 1-5 0-7 2-1 0-3 0-5 1 0-1-1-1-1-2l1-1v-1c-1 0-1 0-2 1 0-1-1-2-3-2h2 0 1v-1c-1 0-1-1-2-1v-1h1z" class="a"></path><path d="M367 308c1 1 3 4 5 4h2l1 2h1 4l-1 1h3c2 1 3 1 5 0v3 1h0c3 2 5 6 6 9 1 2 1 3 1 4h0c1 1 1 1 1 2l-1 1c-2-3-2-7-5-10h0c-2-2-4-4-6-5-7-3-12-2-18 1-1-2-2-2-2-3l1-1c1 0 2 0 2-1h-1-1c0-1 0-1-1-1h-1v-1c0-1 1-1 2-2 0-1 1-3 1-4 0 0 1 0 1 1l1-1z" class="N"></path><path d="M379 315h3c2 1 3 1 5 0v3 1h0l-9-3 1-1z" class="D"></path><path d="M367 308c1 1 3 4 5 4h2l1 2h1 4l-1 1-1 1c-4-1-7-1-12 0h-1-1c0-1 0-1-1-1h-1v-1c0-1 1-1 2-2 0-1 1-3 1-4 0 0 1 0 1 1l1-1z" class="G"></path><path d="M364 312h2l2 1-3 3h-1c0-1 0-1-1-1h-1v-1c0-1 1-1 2-2z" class="Q"></path><path d="M323 304h1c1 1 1 1 1 2h0l1 1c2 2 5 4 5 7h-1v2c1 0 1 0 1-1h1v1l1 4 1 2h-1c-2-3-8-6-12-7-3 0-8 0-11 1l-2 1-1-1c-2 0-3 0-5-1v-2l-2-1c1-1 0-1 1-1h1v-2l6 3 3 1c0 1 1 1 1 1 1-1 2-1 2-2v-1h1v1l1 1c2-1 3-2 5-3 1-1 1-2 2-3v-3z" class="F"></path><path d="M302 313l7 2c0 1-1 1-2 1-2 0-3 0-5-1v-2z" class="U"></path><path d="M322 313c0-1 1 0 1-1s0-1 1-1c0 0 0-1 1 0h0c1 0 2 1 3 2v1c-1 0-1 0-1 1h-1c0-1-1-1-1-1l-1-1h-2z" class="G"></path><path d="M322 313h2l1 1s1 0 1 1h1s1 1 2 1v1l1-1c1 0 1 0 1-1h1v1l1 4 1 2h-1c-2-3-8-6-12-7h2v-1h-2l1-1z" class="Y"></path><path d="M84 107c7 2 14 3 21 4h0-2v1 2c-1 1-3 1-5 3-3 1-5 4-7 6-1-1-2-1-3-2h-1c-1-1-1-1-1-3h-2l-1 1h-1c1-1 1-1 1-2h0v-1c0-1 0-2 1-3s2-1 3-1c0-1 0-2-1-2v-1h0v-1h-2v-1z" class="u"></path><path d="M84 107c7 2 14 3 21 4h0-2v1 2c-1 1-3 1-5 3-3 1-5 4-7 6-1-1-2-1-3-2 1-2 2-3 4-4v-1c1 0 2 1 3 1 2-2 4-3 5-5-4-2-9-2-14-3h0v-1h-2v-1z" class="R"></path><path d="M363 329l3-3c1-1 2-1 3 0 1 0 2 1 2 1h1c1 0 2 1 3 2h0v1l2 1h1c2 0 2 0 3-1 2 2 1 4 3 7 0 1 0 2 1 3 0 2 0 4-1 7 0-4-1-7-4-10s-7-3-11-3-8 3-10 6c-1 0-2-1-2-2l-1-2c0-1 0-2-1-2l3-1v-1h2 1 0c0-2 1-2 2-3z" class="S"></path><path d="M361 332l2 1c-1 1-2 1-4 1l-1-1v-1h2 1 0zm14-2l2 1c1 1 1 2 2 3h-1l-4-3h0l1-1h0z" class="H"></path><path d="M363 329c1 0 2 1 4 2l-4 2-2-1c0-2 1-2 2-3z" class="i"></path><path d="M363 329l3-3c1-1 2-1 3 0-1 0-1 0-1 1l1 1-2 2v1c-2-1-3-2-4-2z" class="K"></path><path d="M300 397c-2-2-3-4-4-7 2 2 3 3 5 4s5 2 6 1c1 0 2 0 2 1h0l3 3v2c1 0 1-1 1-1l1 1c0 2-1 4-3 6-2 1-3 2-4 2l-2 1c-1-1-1-2-2-2l-1 1h-2 0v2c-1 0-1-1-2-1v-1c-2-1-3-1-3-3v-3l2-1v-2h1c2 1 4 0 6 0-1-1-3-1-4-3h0 0z" class="M"></path><path d="M298 409v-1c-1-1-2-1-1-3l6 3-1 1h-2 0v2c-1 0-1-1-2-1v-1z" class="E"></path><path d="M300 397c1 0 2 1 4 2v1 2h-7v-2h1c2 1 4 0 6 0-1-1-3-1-4-3h0 0z" class="v"></path><path d="M312 399v2c1 0 1-1 1-1l1 1c0 2-1 4-3 6-2 1-3 2-4 2 0 0 0-1 1-1v-3c-1-1 0-1 0-1v-1c1 0 1 0 1-1 1 0 1 0 2-1l1-2z" class="U"></path><path d="M318 495v1c1 2 0 2 0 4-1 1-1 3-1 4h1l2-3c0-1 1-2 2-3h0c1-1 1-2 1-2v-1l2-3 1-1c1-1 1-2 2-3s1-2 2-3l1-1h0v1h0v1 1l-1 4-2 2c0 1-1 1-2 2v2c1 0 1 1 2 1-3 2-5 4-7 6l-13 11c-2 3-6 8-10 9l-2-1c0-1-1-2-1-3l1-1c-1-1-1-3 0-5 1 2 3 3 5 3 1 0 2-1 3-2h0c2-1 2-1 3-2 1-2 1-3 2-5v1c1-1 1-1 1-2h1 0c0-2 1-3 2-4 0 1 1 1 1 1 1-1 2-3 2-5l2-4z" class="a"></path><path d="M321 504l-1-1h1v-1-1c1 0 1-1 2-2v-1l2-2 1-1v2c1 0 1 1 2 1-3 2-5 4-7 6z" class="N"></path><path d="M310 507h1 0c0-2 1-3 2-4 0 1 1 1 1 1-2 5-10 16-15 18h-2 0l-1 1c0-1-1-2-1-3l1-1c-1-1-1-3 0-5 1 2 3 3 5 3 1 0 2-1 3-2h0c2-1 2-1 3-2 1-2 1-3 2-5v1c1-1 1-1 1-2z" class="C"></path><path d="M296 523c0-1-1-2-1-3l1-1c-1-1-1-3 0-5 1 2 3 3 5 3 1 0 2-1 3-2h1c-1 2-4 4-6 5l-1 1c-1 0-1 1-1 1h0l-1 1z" class="J"></path><path d="M178 127h0c1 0 1 0 1 1-1 2-1 4 0 6v5c0 1 0 2-1 3h1c0 1 0 0 1 1l-1 1c1 0 2 0 3-1h0c4-2 5-5 6-9l2-4h0 0c-1 3 0 6 2 8 1 2 2 3 4 3l2 1c-3 1-6 0-8 2-6 2-12 3-18 5-1 1-3 1-5 2h0-1l-1-1 3-1-2-2c2-1 4-2 5-4l2-3c3-3 3-8 3-13h2z" class="D"></path><path d="M189 139h1l2 2v1l-1 1h-3v-1c0-1 1-2 1-3z" class="W"></path><path d="M178 127h0c1 0 1 0 1 1-1 2-1 4 0 6v5l-1 1c-1 2-1 3-2 4-2 3-6 5-9 7h-1l-1-1 3-1-2-2c2-1 4-2 5-4l2-3c3-3 3-8 3-13h2z" class="C"></path><path d="M178 127h0c1 0 1 0 1 1-1 2-1 4 0 6v5l-1 1v-2h-1l1-11z" class="a"></path><path d="M177 138h1v2c-1 2-1 3-2 4-2 3-6 5-9 7h-1l-1-1 3-1c4-3 6-7 9-11z" class="U"></path><path d="M134 149c1 0 3 1 3 0 1 0 1-1 2-2v-1c1 1 1 2 1 3v1h2c1 0 3 1 4 2 2 1 4 1 7 3 0-1 1-1 1-2l1-1h0v1h1l1 1h1v1c-2 0-5 1-7 2s-4 3-7 4l-6 5c-1 0-2 1-3 1l1-1h0-2c-1-1-1-2-1-3v-1c1-1 1-2 1-3v-3-1h0c1-1 1-1 1-2v-1c0-1-1-2-1-3z" class="R"></path><path d="M153 155c0-1 1-1 1-2l1-1h0v1h1l1 1c-2 1-3 1-4 1z" class="U"></path><path d="M145 155c1 0 3 0 5 1l-1 1c-1 1-2 1-3 1 0-1-1-2-1-3z" class="M"></path><path d="M137 155c1-1 3-2 5-3 0 1 1 1 1 2v2 1l-1-1h-2c-1 1-1 1-2 1l-1-2z" class="W"></path><path d="M134 156l3-1 1 2c1 0 1 0 2-1h2l1 1v-1 1c0 2 0 3 1 4l-6 5c-1 0-2 1-3 1l1-1h0-2c-1-1-1-2-1-3v-1c1-1 1-2 1-3v-3z" class="T"></path><path d="M139 161c0-1 0-2 1-3 1 0 1 0 3 1v1c-1 1-3 1-4 1h0z" class="W"></path><path d="M143 159v-2c0 2 0 3 1 4l-6 5 1-2-1-3h1 0c1 0 3 0 4-1v-1z" class="D"></path><path d="M134 159c2 0 4 1 4 2l1 3-1 2c-1 0-2 1-3 1l1-1h0-2c-1-1-1-2-1-3v-1c1-1 1-2 1-3z" class="V"></path><path d="M134 159c2 0 4 1 4 2l1 3-1 2c-1 0-2 1-3 1l1-1h0v-1c1 0 1 0 2-1v-2c-1-1-2-1-3-1h0l-2 1c1-1 1-2 1-3z" class="E"></path><path d="M367 295l1-1c1 1 1 1 2 1l1 1h-1l2 2h0c1-1 1-2 3-3h2 1c0 1 0 1-1 2-1 2-1 2-1 4 1 1 3 1 4 1l1 1h0c1 1 2 2 3 4h1l2-2 1-1c0 2 0 3-1 5-3 2-8 2-11 5h-1l-1-2h-2c-2 0-4-3-5-4-1-2-1-3-1-5h0l1-4v-1h-2c1-1 2-2 2-3z" class="u"></path><path d="M367 295l1-1c1 1 1 1 2 1l1 1h-1v2c-1 2-4 4-2 6 1 1 2 3 3 4 2 1 4 2 5 4h2c1-2 0-2 1-4 1 1 1 1 2 1h2l1-1c-1 0-1-1 0-1h1l2-2 1-1c0 2 0 3-1 5-3 2-8 2-11 5h-1l-1-2h-2c-2 0-4-3-5-4-1-2-1-3-1-5h0l1-4v-1h-2c1-1 2-2 2-3z" class="W"></path><path d="M367 295l1-1c1 1 1 1 2 1l-1 1c-1 1-1 2-2 3v-1h-2c1-1 2-2 2-3z" class="k"></path><path d="M357 407h1 3c1 1 0 3 1 3h4c2 0 3 1 3 2 1-1 1-1 2-1v1l-1 1c0 1 1 1 1 2-2 0-3 1-5 2l-1 1h-2c-2 1-5 4-6 5-1 0-2 0-3-1l-1 1h0l-4 6-1-1v-1c0-1 0-1-1-1h0-1v-1c2-1 3-4 3-6 1-1 1-1 0-3l-1-1c-1-1 0-1 0-2h1c1-1 1-1 2-1v3h0l1-1c1-3 4-5 5-7z" class="M"></path><path d="M369 412c1-1 1-1 2-1v1l-1 1c0 1 1 1 1 2-2 0-3 1-5 2l-1 1c0-1 0-1 1-2-1-1-1-1-2-1l-1 1h-1l-1 1h-1c-1 1-2 1-3 2v-1c0-1 2-1 3-2s2-1 3-1l2-2h2l2-1z" class="L"></path><path d="M357 418v1c1-1 2-1 3-2h1l1-1h1l1-1c1 0 1 0 2 1-1 1-1 1-1 2h-2c-2 1-5 4-6 5-1 0-2 0-3-1v-1h0l3-3z" class="H"></path><path d="M349 416l-1-1c-1-1 0-1 0-2h1c1-1 1-1 2-1v3h0l1-1v1l1 1c0 3-2 4-3 6 0 1 0 1 1 1h2l-4 6-1-1v-1c0-1 0-1-1-1h0-1v-1c2-1 3-4 3-6 1-1 1-1 0-3z" class="a"></path><path d="M354 252v-1c3-4 11-8 16-7 2 0 5 1 7 1v-2-1l2 1c2 0 8 2 8 5l2 3c0 1 0 2 1 3-1 1-1 2-1 3v1c0 1-1 1-1 1 0 1-1 1-2 3h1v2l-2-2-2-2c-1-1 0-4 0-5l-1-3-1 1c-1 0-1 0-2-1v-3h-2c0 1-1 1-1 2h-1c-1 0-1-2-1-3-1-1-3-2-4-2s-2 1-3 2v2 3-1c-1 0-1-1-3-1l-1 1h-1l-1-1h-4 0c-1 1-2 1-3 2l-2 2c0-2 1-2 2-3z" class="B"></path><path d="M379 249c1 1 2 1 3 2v1l-1 1c-1 0-1 0-2-1v-3z" class="M"></path><path d="M362 252c1-1 2-1 2-1v-2c1-1 2-1 3-1v2 3-1c-1 0-1-1-3-1l-1 1h-1z" class="C"></path><path d="M389 251c0 1 0 2 1 3-1 1-1 2-1 3v1c0 1-1 1-1 1l-1-3v2l-2 2-1-1c2-1 2-3 3-5 0-1 1-2 2-3z" class="H"></path><path d="M132 160l1 1v2h0c0 1 0 2 1 3 0 0-1 1-1 2v1l-9 7c-2 3-5 5-8 8-6 6-11 14-16 22 1-4 2-7-1-11 0-1-1-1-2-2l-3-2 1-2c1-1 2-1 3-1v1l4-1c2 0 3 0 5-1 0-1 2-2 4-3 3-2 6-6 9-8l-1-1 3-2c4-3 6-4 8-8 1-2 1-3 2-5z" class="B"></path><path d="M95 189c1-1 2-1 3-1v1l1 1c0 1-1 1-1 2l-1 1-3-2 1-2z" class="R"></path><path d="M107 187c0 1 1 2 1 3-1 1-3 4-5 5h0c0-2 1-4 2-6-1-1-2-1-3-1 2 0 3 0 5-1z" class="T"></path><path d="M133 163h0c0 1 0 2 1 3 0 0-1 1-1 2v1l-9 7c1-1 2-3 3-4 1 0 2-1 2-1 1 0 1-1 1-2l-3 3-1 1c-1 1-2 1-3 1h0c3-3 9-6 10-11z" class="D"></path><path d="M132 160l1 1v2c-1 5-7 8-10 11l-3 2-1-1 3-2c4-3 6-4 8-8 1-2 1-3 2-5z" class="H"></path><defs><linearGradient id="M" x1="311.701" y1="363.54" x2="311.933" y2="329.006" xlink:href="#B"><stop offset="0" stop-color="#1a1a1a"></stop><stop offset="1" stop-color="#3a3939"></stop></linearGradient></defs><path fill="url(#M)" d="M317 364c-2 0-6 1-8 1h0c-1-1-2-1-3-1-4-3-7-8-8-12 0-2-1-5 0-6h1l1 2v-1c1-1 1-3 1-5v-6h1l1 2h0c1-1 1-3 2-4s1-3 2-4 3-1 5-2v1 2h-2l-1 2v1c1 0 1 1 2 0l-1 1h0s-1 0-1 1h0c-1 0-1 1-2 1l-2 4c-1 1-2 2-2 4-1 3 0 7 2 10 1 2 3 4 5 4 2 1 5 1 7 0l1-1c2 0 3-2 4-4 0 0 1-1 2-1l2-1c0 2-1 5-2 7l-3 3c-1 0-3 1-4 2z"></path><path d="M596 229c0 1 1 3 1 4v2h1v3c0 3 0 6 1 9h-1-6c0 1 0 3-1 4-1-2-1-3-1-5-1 0-1 0-2 1h-4 0-1c0-4-1-7-1-11-1-1-1-3-1-4l1-1c1 1 1 2 1 2h1c1-1 2-2 2-4h2v1h1l1-1h0l2 1c2 0 1-1 2-1h2z" class="E"></path><path d="M582 231c1 1 1 2 1 2v7c1 2 2 5 1 7h0-1c0-4-1-7-1-11-1-1-1-3-1-4l1-1z" class="H"></path><path d="M591 241c1 1 1 2 2 2s2 0 3-1c1-2 0-3 0-4v-1c1 1 1 1 2 1 0 3 0 6 1 9h-1-6s-1-1-2-1h0v-2h0l-1-1h0v-2h1v1h1v1h0v-2z" class="V"></path><path d="M596 229c0 1 1 3 1 4v2h1v3c-1 0-1 0-2-1v1c0 1 1 2 0 4-1 1-2 1-3 1s-1-1-2-2v-1l-1-1c-1 1-1 1-2 1v3h0l-2-1s-1-1-2-1v-4c2-4 6-2 9-4-1-1-1-1-1-3 2 0 1-1 2-1h2z" class="M"></path><path d="M119 175l1 1c-3 2-6 6-9 8-2 1-4 2-4 3-2 1-3 1-5 1l-4 1v-1c-1 0-2 0-3 1-3 0-9 1-12 3l-2 1h0c-1 0-1 1-2 1h0 0c0-2 1-3 1-4h0c2-2 5-4 7-6l-1-1 7-3c2 0 5-1 6-3h0c4 1 6 1 10 0v1h0c4 0 7-1 10-3z" class="W"></path><path d="M109 177v1h0c-7 2-15 3-22 6l-1-1 7-3c2 0 5-1 6-3h0c4 1 6 1 10 0z" class="G"></path><defs><linearGradient id="N" x1="376.256" y1="390.41" x2="366.1" y2="372.619" xlink:href="#B"><stop offset="0" stop-color="#151516"></stop><stop offset="1" stop-color="#302f2f"></stop></linearGradient></defs><path fill="url(#N)" d="M356 377c1 0 3 0 4-1h0 1c2 1 4 1 6 1h0c0 1 0 0 1 1 1-1 2-2 4-2l4 1c2 1 3 2 4 2 1 1 3 1 4 1h1v1c0 1 0 2 1 3-1 1-2 2-3 2-1 1-1 1-2 0-1 0-3 1-4 0h-1s-1 0-1 1l-2-1-1 3-11-1c0-1 0-2 1-3h-1v-1c-1 0-1 0-1-1l-1-1c0 1-1 2-2 3h-3c0-1 1-1 1-2h-1l-2-2 2-2 2-2z"></path><path d="M352 381l2-2 1 1h1 1 2c-2 1-3 2-4 3h-1l-2-2z" class="C"></path><path d="M370 383c1-1 2-2 4-3 1 1 3 1 3 3 0 1 0 2-1 3 0 0-1 0-1 1l-2-1c-2 0-2-1-3-3z" class="M"></path><path d="M359 380c1 0 1-1 2-1 1 1 3 2 4 3 1 0 1 0 1 1s0 1-2 1l-1 1h-1-1v-1c-1 0-1 0-1-1l-1-1c0 1-1 2-2 3h-3c0-1 1-1 1-2 1-1 2-2 4-3h0z" class="d"></path><path d="M359 380h0 1l-1 1c-1 0-2 1-2 2v1l1-1 1-1v-1 1c0 1-1 2-2 3h-3c0-1 1-1 1-2 1-1 2-2 4-3z" class="H"></path><path d="M366 383h1l1-1v1c1 1 1 1 1 3 0-1 0-1 1-1 0-1-1-1 0-2 1 2 1 3 3 3l-1 3-11-1c0-1 0-2 1-3h1l1-1c2 0 2 0 2-1z" class="i"></path><path d="M397 576h1s0-1 1-1l3 8 3 16c1 6-1 11-1 16v1 8l1 1c2 2 6 5 7 7 0 1 0 2-1 3-2 0-5 0-7-1h-1c-2 0-3 0-4-1-1 0-1 0-1-1-1-1-1-2-3-3 1 0 2-1 2-1l-1-1v-2c3-6 5-13 6-19 1-10-1-21-5-30z" class="w"></path><path d="M404 624l1 1c2 2 6 5 7 7 0 1 0 2-1 3-2 0-5 0-7-1h-1c-2 0-3 0-4-1-1 0-1 0-1-1-1-1-1-2-3-3 1 0 2-1 2-1l2-1h3c1 0 2 0 2 1 1 0 1 1 1 2l-3 2v1h1l1-1c1 0 1 1 3 1h0v-1s0-1-1-2v-2h0c-1-1-2-2-2-3v-1z" class="S"></path><path d="M399 627h3c1 1 2 1 2 2-1 2-3 2-4 3-1 0-1-1-2-1v1c-1-1-1-2-3-3 1 0 2-1 2-1l2-1z" class="J"></path><path d="M397 628l2-1c0 1-1 2-1 4v1c-1-1-1-2-3-3 1 0 2-1 2-1z" class="o"></path><path d="M359 214c1 0 2 1 3 2l1 1c1 1 1 2 1 3l2 2h2v1l1 1c2 2 5 3 7 5l2 1v1 1c1 1 1 1 1 2h-3l-13-1c-2-1-5-1-7-3h0-1l-1-1c-1-2-2-4-2-6 0-1 0-1 1-2v1h1c0-1 0-1 1-2h-1-1c-1 1-1 1-1 0v-2c-1 1-1 2-3 1l3-3 5-2h1 1z" class="B"></path><path d="M376 234l2-3v1c1 1 1 1 1 2h-3z" class="C"></path><path d="M364 229c-1 0-3 0-4-1h0l1-1h2c1 0 1-1 2-2 0 2 0 3-1 4z" class="L"></path><path d="M364 223v-1h0l-1-2v-1l1 1 2 2h2v1l1 1c-1 2-2 3-3 5h-2c1-1 1-2 1-4h0c1 0 1-1 2-1-1-1-2-1-3-1z" class="a"></path><path d="M359 214c1 0 2 1 3 2l1 1c1 1 1 2 1 3l-1-1v1l1 2h0v1 1l-2 2c-2 1-4 1-6 0s-2-3-3-4h1c0-1 0-1 1-2h-1-1c-1 1-1 1-1 0v-2c-1 1-1 2-3 1l3-3 5-2h1 1z" class="W"></path><path d="M359 214c1 0 2 1 3 2l1 1-3 1h-1v-3l-1-1h1z" class="n"></path><path d="M352 216l5-2-1 3h-3l-1 1c-1 1-1 2-3 1l3-3z" class="L"></path><path d="M79 152v-1l1-2c1 4 1 9 4 13l3 3v1 1c-1 0-1 1-1 1h-1c0-1-1-1-1-1h-1c0 1 1 2 1 3h0c1 0 1 1 2 2h1c2 1 7 1 8 2 0 2-2 2-3 3v3h1l-7 3c-1 1-3 1-4 2-2 2-4 4-6 5v-3c1-4 1-9 1-13 1-7 2-15 2-22z" class="E"></path><path d="M84 176c1 0 2-1 3-1l-5 4h-1v-2l1-1h1 1z" class="W"></path><path d="M82 179l-4 4v-4c1-2 0-2 1-4 0 1 1 2 2 2h0v2h1z" class="a"></path><path d="M81 177c0-2-2-3-3-5 1 0 1-2 1-3h0v-1-1c1 1 1 1 1 2s1 2 1 2c1 1 2 1 3 2h0-3 0c1 1 1 2 2 3h1-1-1l-1 1h0z" class="J"></path><path d="M76 187l1 1c2-2 3-4 6-6 2-2 6-3 9-5v3h1l-7 3c-1 1-3 1-4 2-2 2-4 4-6 5v-3z" class="T"></path><path d="M79 152v-1l1-2c1 4 1 9 4 13l3 3v1 1c-1 0-1 1-1 1h-1c0-1-1-1-1-1h-1c0 1 1 2 1 3h0c1 0 1 1 2 2-2-1-3-2-4-4-4-5-1-11-2-17l-1 1z" class="N"></path><path d="M532 147c1 1 2 1 3 2h2 1v1 1s0 1 1 1l1 2h-3v2l2 3 1 4 2-1 1 2v1l6 6c0 1 0 1-1 2l2 1 3 2c-1 0-2 0-3-1v1c1 0 1 1 2 2l-6-5-1 1c-10-8-20-16-32-21h0c3 1 6 2 9 0h0c3-1 4-3 7-2 1-1 2-3 3-4z" class="B"></path><path d="M532 147c1 1 2 1 3 2h2 1v1 1-1c-1 0-1 1-2 1s-2 0-3-1h-2c0 1-1 2-1 2h-1v-1c1-1 2-3 3-4z" class="C"></path><path d="M531 160c-1-1-3-2-4-3 0-2 0-3 1-4h1c3 1 5 1 8 1v2l2 3h-1c-1 1-2 0-2-1h-1c-1 1 0 3-1 4-1 0-2-1-3-2z" class="M"></path><path d="M537 156l2 3h-1c-1 1-2 0-2-1h-1v-1h0c1 0 1 0 2 1h0v-2h0z" class="N"></path><path d="M533 160l-1-1c0-1-1-2 0-2h2 1v1c-1 1 0 3-1 4-1 0-2-1-3-2h2z" class="T"></path><path d="M533 160l1-1v3c-1 0-2-1-3-2h2z" class="u"></path><path d="M536 158c0 1 1 2 2 1h1l1 4 2-1 1 2v1l6 6c0 1 0 1-1 2l2 1 3 2c-1 0-2 0-3-1v1c1 0 1 1 2 2l-6-5c-5-4-8-9-10-15z" class="a"></path><path d="M542 162l1 2v1l-1 1-2-3 2-1z" class="C"></path><defs><linearGradient id="O" x1="547.843" y1="168.101" x2="542.485" y2="168.243" xlink:href="#B"><stop offset="0" stop-color="#1d1918"></stop><stop offset="1" stop-color="#2f3131"></stop></linearGradient></defs><path fill="url(#O)" d="M543 165l6 6c0 1 0 1-1 2-2-2-4-4-6-7l1-1z"></path><path d="M345 381c1-4 2-8 4-11 1 1 2 1 3 1h2l-1 1 1 1v1 1h4l-2 1v1l-2 2-2 2 2 2h1c0 1-1 1-1 2h3c1-1 2-2 2-3l1 1c0 1 0 1 1 1v1h1c-1 1-1 2-1 3-2 3-3 3-6 4-1 0-2 2-3 3l-3 3c-1-1-2-1-3-1l-1 1h0-1-1l-1-1v-3l-1-5v-1c1 0 1 0 1-1 1-1 0-2 0-3 1-2 1-3 1-5v-1-1c1 1 1 2 1 2l1 2h0z" class="F"></path><path d="M347 384c2-2 3-4 5-6 1-1 2-2 4-2v1l-2 2-2 2c-1 1-2 2-3 4l-2-1z" class="J"></path><path d="M352 381l2 2h1c0 1-1 1-1 2h0v2h-3-1-2l1-2c1-2 2-3 3-4z" class="F"></path><path d="M350 387v-2c1-1 1-1 2-1s1 1 2 1v2h-3-1z" class="B"></path><path d="M345 381c1-4 2-8 4-11 1 1 2 1 3 1h2l-1 1c-4 3-6 7-6 12h0v3l-2-1v-5h0z" class="H"></path><path d="M343 377c1 1 1 2 1 2l1 2h0 0v5l2 1v-3l2 1-1 2c-1 3-1 6-3 8v3h0-1-1l-1-1v-3l-1-5v-1c1 0 1 0 1-1 1-1 0-2 0-3 1-2 1-3 1-5v-1-1z" class="J"></path><path d="M341 389v-1c1 0 1 0 1-1l1 2c0 2 0 4-1 5l-1-5z" class="d"></path><path d="M343 379c1 3 2 8 0 10l-1-2c1-1 0-2 0-3 1-2 1-3 1-5z" class="K"></path><path d="M347 384l2 1-1 2c-1 3-1 6-3 8h0c0-2 0-3 1-5 0-1 0-1 1-2v-1-3z" class="U"></path><path d="M359 382l1 1c0 1 0 1 1 1v1h1c-1 1-1 2-1 3-2 3-3 3-6 4-1 0-2 2-3 3l-3 3c-1-1-2-1-3-1l-1 1h0 0v-3c2-2 2-5 3-8h2 1 3v-2h0 3c1-1 2-2 2-3z" class="J"></path><path d="M354 385l3 1 4 2-7 2-2-2-1-1h0 3v-2h0z" class="m"></path><path d="M354 385l3 1c0 1 0 2-1 3-1-1-2-1-2-2v-2h0zm-4 2h1 0l1 1 2 2c-3 1-5 2-6 5l-2 2-1 1h0 0v-3c2-2 2-5 3-8h2z" class="C"></path><path d="M334 355v-1h3 0 0l2 2 1-1v4s1 0 1 1c0 0 0 2-1 3h1c0 2 0 3 1 4 0 2 1 2 1 3 1 2 0 5 0 7h0v1 1c0 2 0 3-1 5 0 1 1 2 0 3 0 1 0 1-1 1v1-1l-2-2-1-1c-1-3-4-5-6-7s-4-1-6-2l-1-2h1l1-1h0c1-1 2-1 4-1l-1-2v-3-1h-1c1-1 1-2 2-3h0c1-2 3-5 4-7l-1-1z" class="F"></path><path d="M340 366c1 3 1 7 3 11h0v1l-1-1-1 1-1-3-1-5h0c1 1 2 4 2 5v1-2h0v-1-1s0-1-1-1v-5z" class="I"></path><path d="M336 370c1-1 1-1 2 0h1l1 5h-2l-3-3 1-2z" class="N"></path><path d="M339 364l1-1h1c0 2 0 3 1 4 0 2 1 2 1 3 1 2 0 5 0 7-2-4-2-8-3-11l-1-2z" class="d"></path><path d="M334 355v-1h3 0 0l2 2 1-1v4s1 0 1 1c0 0 0 2-1 3l-1 1c-1-2-4-5-3-7v-1h-1l-1-1z" class="N"></path><path d="M334 355v-1h3 0 0l2 2v1l-1 1-2-1v-1h-1l-1-1z" class="B"></path><path d="M329 366c1-1 1-2 2-3 1 1 1 0 2 0 2 0 4 2 4 4h0l1 1v2c-1-1-1-1-2 0l-1 2v-1c-1 1-1 2-2 2h0c0-1-1-1-2-1l-1-2v-3-1h-1z" class="H"></path><path d="M338 368v2c-1-1-1-1-2 0h-1l-1-1 1-1h3z" class="n"></path><path d="M330 367c1 2 3 3 5 4-1 1-1 2-2 2h0c0-1-1-1-2-1l-1-2v-3z" class="G"></path><path d="M329 366c1-1 1-2 2-3 1 1 1 0 2 0 2 0 4 2 4 4h0v1h-2-1v-2h0-1 0v1c-2-1-2-1-3-1h-1z" class="N"></path><path d="M335 371v1l3 3h2l1 3 1-1 1 1v1c0 2 0 3-1 5 0 1 1 2 0 3 0 1 0 1-1 1v1-1l-2-2-1-1c-1-3-4-5-6-7s-4-1-6-2l-1-2h1l1-1h0c1-1 2-1 4-1 1 0 2 0 2 1h0c1 0 1-1 2-2z" class="F"></path><path d="M335 371v1l3 3 1 2h-1c-1-1-2-1-2-3h-3v-1c1 0 1-1 2-2z" class="h"></path><path d="M334 377c3 3 4 6 7 8v3l-2-2-1-1c-1-3-4-5-6-7h1 0l1-1z" class="n"></path><path d="M340 375l1 3 1-1 1 1v1c0 2 0 3-1 5 0 1 1 2 0 3 0 1 0 1-1 1v1-1-3c0-2-1-6-2-8l-1-2h2z" class="J"></path><path d="M341 378l1-1 1 1v1c0 2 0 3-1 5l-1-6z" class="G"></path><path d="M331 372c1 0 2 0 2 1h0v1l-2 1c1 1 2 2 3 2l-1 1h0-1c-2-2-4-1-6-2l-1-2h1l1-1h0c1-1 2-1 4-1z" class="D"></path><path d="M326 374c2 0 4 1 5 1 1 1 2 2 3 2l-1 1h0-1c-2-2-4-1-6-2l-1-2h1z" class="J"></path><path d="M154 110l26 1h8 10c2 0 4-1 5 0v1h1c-4 1-8 2-11 3l-2 2c-1 2-2 3-3 3s-2 0-2-1l-8 4v1c1 1 2 1 3 2 0 1-2 2-2 3v5h0c-1-2-1-4 0-6 0-1 0-1-1-1h0-2c0-1 0-2-1-3-4-7-9-9-16-11l-1 1c-1-1-1-1-1-2h-2v-1h0 1c-1 0-1 0-2-1z" class="T"></path><path d="M180 115l1-1c3-1 7 0 10 0-1 1-1 1-2 1v1c-1 1-2 1-2 1l-2 1h-1c-2-1-2-1-3-2l-1-1z" class="B"></path><path d="M173 117l1-2v-1h4l2-1v2l1 1c1 1 1 1 3 2l-2 1h-5c-2 0-3-1-4-2z" class="u"></path><path d="M154 110l26 1h8 3c-3 1-7 1-11 1-3 1-6 0-8 0h-15-2v-1h0 1c-1 0-1 0-2-1z" class="E"></path><path d="M191 114l12-2h1c-4 1-8 2-11 3l-2 2c-1 2-2 3-3 3s-2 0-2-1l-8 4v1c1 1 2 1 3 2 0 1-2 2-2 3v5h0c-1-2-1-4 0-6 0-1 0-1-1-1h0-2c0-1 0-2-1-3-4-7-9-9-16-11 4-1 10 1 14 2v2c1 1 2 2 4 2h5l2-1h1l2-1s1 0 2-1v-1c1 0 1 0 2-1z" class="R"></path><path d="M186 119c2-1 4-4 7-4l-2 2c-1 2-2 3-3 3s-2 0-2-1z" class="T"></path><path d="M552 178c-1-1-1-2-2-2v-1c1 1 2 1 3 1 2 1 7 3 9 3l1-1c1 1 1 1 2 1s2-1 3-1v-1h0l8 3 6 2v1l-1 1c4 1 9 4 10 8l1 2h0c1 2 1 4 2 6 0 3 0 5-1 8 0 1 1 3 1 5l-1-1s-1 0-1-1v-1h-1c0-1 0-1 1-1 1-2 0-7 0-8-1-3-2-4-3-6-2-3-4-4-7-5-2 0-5 0-8-1-2 0-5-1-8-1l-1 1h0c-1 0-3-1-4 0l-1-1h1v-1l1-1h-1c-4-2-6-5-9-8z" class="W"></path><path d="M562 186c1 1 3 2 4 2l-1 1h0c-1 0-3-1-4 0l-1-1h1v-1l1-1z" class="c"></path><path d="M591 192l1 2h0c1 2 1 4 2 6 0 3 0 5-1 8 0 1 1 3 1 5l-1-1s-1 0-1-1v-1h-1c0-1 0-1 1-1 1-2 0-7 0-8-1-3-2-4-3-6h1c2 3 2 4 3 7v-2h0c0-1 0-2-1-2v-1-2c-1-1-1-2-1-3z" class="d"></path><defs><linearGradient id="P" x1="569.672" y1="183.509" x2="576.485" y2="178.171" xlink:href="#B"><stop offset="0" stop-color="#363236"></stop><stop offset="1" stop-color="#414340"></stop></linearGradient></defs><path fill="url(#P)" d="M563 178c1 1 1 1 2 1s2-1 3-1v-1h0l8 3 6 2v1l-1 1c-3-1-6-2-10-3-3-1-6-1-9-2l1-1z"></path><path d="M392 387h2v12c0 2 1 4 0 5 0 1-1 1-1 1h0c0 1 1 1 0 2l-1 2v1 2h-1l-3 2h-1 0c-1 0-2-1-2-1h-1l-1-1-2-2c-2-1-5-1-7-1h3v-1l-2-3v-2c0-1 0-1 1-2 0-1 1-3 2-4 0-1 3-1 4-2h0l1-1h0c4-1 6-4 9-7z" class="D"></path><path d="M383 400c1-1 1-1 2-1 0-1 1 0 1-1 2-1 3-2 5-4l1-1c0-1 1-1 1-1 1 1 1 4 0 5h0c-1 1-2 1-2 2-2 2-6 1-7 3l-1-2h0z" class="U"></path><path d="M383 394v1c2 0 5-1 7-2v-1c2-1 3-3 4-4 0 1 0 2-1 4 0 0-1 0-1 1l-1 1c-2 2-3 3-5 4 0 1-1 0-1 1-1 0-1 0-2 1s-5 3-5 5v2l-1 1-2-3v-2c0-1 0-1 1-2 0-1 1-3 2-4 0-1 3-1 4-2h0l1-1z" class="M"></path><path d="M383 400h0l1 2h4c2 0 2-1 4-1l1 1v3h0c0 1 1 1 0 2l-1 2v1 2h-1l-3 2h-1 0c-1 0-2-1-2-1h-1l-1-1-2-2c-2-1-5-1-7-1h3v-1l1-1v-2c0-2 4-4 5-5z" class="W"></path><path d="M388 408l5-3c0 1 1 1 0 2l-1 2v1 2h-1v-2h-1-1c1 0 1 0 2-1l-1-1h-1c-1 1-1 1-1 0z" class="e"></path><path d="M381 410h1c2 0 3-2 4-3v1h2c0 1 0 1 1 0h1l1 1c-1 1-1 1-2 1h-2 0c-1 1-1 0-1 1h-2v1s0 1 1 1h-1l-1-1-2-2h0z" class="C"></path><path d="M385 413c-1 0-1-1-1-1v-1h2c0-1 0 0 1-1h0 2 1 1v2l-3 2h-1 0c-1 0-2-1-2-1z" class="c"></path><path d="M383 400h0c-1 2-4 3-4 5 0 1 0 1 1 2 1 0 2 1 3 1h0l-2 2h0 0c-2-1-5-1-7-1h3v-1l1-1v-2c0-2 4-4 5-5z" class="F"></path><path d="M311 423c0-1 1-1 1-2h2l1 1 3 2c1 0 4 2 5 3v2l2 2h0 1v-1h1 0v2c1 1 2 1 3 2-1 1-2 0-2 2l1 1c-3 0-4 0-6-2l-1 1v-1c-1 1-1 1-1 2-1 1-2 1-3 2-2 1-4 1-6 1v-1 2 1l-1 1-1-1-2 2-1-1c-1 0-1-1-2-1 1-1 1-2 1-2v-1h-1v-1h0c-2-2-2-5-3-7h1v-6c1-1 1-1 1-2 1-1 1-1 2-1 2 1 3 2 4 4h0c0-1 1-2 1-3z" class="D"></path><path d="M311 423c0-1 1-1 1-2h2l1 1c0 1-1 1-1 1h-3z" class="K"></path><path d="M306 433c0-3 0-5-1-8l3 5c1 1 1 2 3 2-2 1-2 0-2 2l-1-1c-1 1-1 1-2 1v-1z" class="f"></path><path d="M318 424c1 0 4 2 5 3v2l-1-1c-1 0-1 0-2 1h-1-1l-2-2h1v-1h1v-2z" class="G"></path><path d="M317 427v-1h1 1v1h-2z" class="C"></path><path d="M302 431h1v-6c0 3 0 5 2 8h1v1c1 0 1 0 2-1l1 1c-1 1-1 3-2 4h-2 0c-2-2-2-5-3-7z" class="n"></path><path d="M305 433h1v1c1 0 1 0 2-1l1 1c-1 1-1 3-2 4h-2 0l1-1-1-4z" class="G"></path><path d="M315 430l1 1h3 1v1c-1 1-1 1-1 2l2-1c1 0 2 1 3 2h-1l-1 1v-1c-1 1-1 1-1 2-1 1-2 1-3 2-2 1-4 1-6 1v-1h0v-1c1-3 3-5 3-8z" class="O"></path><path d="M315 437c2 0 4-1 6 0-1 1-2 1-3 2h-3 0v-2z" class="Y"></path><path d="M312 438h1c2-1 1-2 2-4 1 0 1 1 1 1-1 1-1 1-1 2v2h0 3c-2 1-4 1-6 1v-1h0v-1z" class="g"></path><path d="M315 430l1 1h3c-1 1-1 3-3 4 0 0 0-1-1-1-1 2 0 3-2 4h-1c1-3 3-5 3-8z" class="Y"></path><path d="M311 432c2-2 1-4 2-6h1c1 1 1 2 1 4 0 3-2 5-3 8v1h0v2 1l-1 1-1-1-2 2-1-1c-1 0-1-1-2-1 1-1 1-2 1-2v-1h-1v-1h2c1-1 1-3 2-4 0-2 0-1 2-2z" class="U"></path><path d="M307 438c1 0 1 0 2-1 1 0 2 1 2 1v2c-1 1-3 1-4 1-1-1-1-1-1-2h-1v-1h2z" class="u"></path><defs><linearGradient id="Q" x1="300.515" y1="386.953" x2="332.942" y2="377.057" xlink:href="#B"><stop offset="0" stop-color="#151516"></stop><stop offset="1" stop-color="#373636"></stop></linearGradient></defs><path fill="url(#Q)" d="M326 376c2 1 4 0 6 2s5 4 6 7h0c-1 0-2-1-3-1h-1 0v2h-3c-1 1-1 1-2 3-2-1-4-1-5-1l-7 1c-3 0-6-1-9-1-2 0-4 0-5-1-3-1-4-3-5-6 3 1 5 1 7-1 2-1 7-4 8-4s2 1 3 1c4 1 6 1 10 0v-1h0z"></path><path d="M311 385c-1-1-1-1-1-3 1-1 2-1 3-2 1 0 2 0 3 1l-1 1h-1v1l-2 2h-1z" class="T"></path><path d="M316 381h0l4 2c-2 1-3 2-4 3l-3 2c-1-1-2-2-2-3h1l2-2v-1h1l1-1z" class="H"></path><path d="M326 380s2-1 3-1c1 1 4 4 5 6v1h-3c-1 1-1 1-2 3-2-1-4-1-5-1l-7 1 2-3h2c1 1 0 0 1 0h0c0-1 0-1 1-2 0-2 1-3 3-4z" class="d"></path><path d="M322 386c0-1 0-1 1-2 0-2 1-3 3-4 0 1-1 1 0 2l1-1 1 1c-1 1-1 1-2 1 0 1-1 1-1 1v1 2c-1-1-2-1-3-1z" class="J"></path><path d="M325 387h1c1-1 2-1 2-2 1-1 1-1 2-1s1 1 1 2h3-3c-1 1-1 1-2 3-2-1-4-1-5-1l-7 1 2-3h2c1 1 0 0 1 0h0c1 0 2 0 3 1z" class="F"></path><path d="M363 266c2 0 3 3 5 4 1 1 0 1 0 2h1 1c0 2 0 2-1 3h1c1-1 1-2 3-3l1 1h-1c2 1 3 1 4 2v1 1h0s1 1 1 2c1 0 4 1 5 1l3 1c-1 0-2 0-2 1h-1c-4-1-8-2-11-1-1 1-1 1-2 1-1-1-1 0-2 0s-2 1-4 1c-1 0-6 2-7 3v1h-1v-1h-1c-1 1-1 2-2 3-1-1-1-1-2-3v-2-1c0-4 1-9 3-13 1-1 2-2 3-2l1-1c1-1 3-1 5-1z" class="a"></path><path d="M357 284c0-1 0-1-1-2l-1-1c0-2 0-3 1-4 0-1 1-1 2-1 0 2 0 6-1 8z" class="H"></path><path d="M368 278v-1c1 0 2 1 2 1 1 1 2 1 3 1-4 0-8 1-12 3v-1-2c1 1 1 1 2 1 1-1 2-1 2-1h2l1-1z" class="F"></path><path d="M361 276s0-1 1-1v1l1 2c1 0 3-1 4-1l1 1-1 1h-2s-1 0-2 1c-1 0-1 0-2-1h0-1l1-3z" class="D"></path><path d="M361 276h1v3h-1-1l1-3z" class="S"></path><path d="M369 272h1c0 2 0 2-1 3l-2 2c-1 0-3 1-4 1l-1-2 1-3h1c1-1 1 0 3 0 1 0 1 0 2-1z" class="M"></path><path d="M354 270c1 1 1 1 1 2h1 0l-1 1c-1 2-1 5-1 8-1 2-2 4-3 5v-2-1c0-4 1-9 3-13z" class="J"></path><path d="M360 274l1-1c1-1 1-1 2 0l-1 3v-1c-1 0-1 1-1 1l-1 3h1 0v2 1l-1 1c-2 1-2 1-3 2-1-1-1 0 0-1 1-2 1-6 1-8l2-2z" class="F"></path><path d="M360 283c-1 0-1-1-1-1 0-2 0-3 1-4v1h1 0v2 1l-1 1z" class="f"></path><path d="M373 272l1 1h-1c2 1 3 1 4 2v1 1h0s1 1 1 2h-5c-1 0-2 0-3-1 0 0-1-1-2-1v1l-1-1 2-2h1c1-1 1-2 3-3z" class="E"></path><path d="M373 272l1 1h-1 0l1 2h-1c-1 1-2 1-3 0 1-1 1-2 3-3z" class="o"></path><path d="M363 266c2 0 3 3 5 4 1 1 0 1 0 2h1c-1 1-1 1-2 1-2 0-2-1-3 0h-1c-1-1-1-1-2 0l-1 1-1-1c-1 1-1 1-2 1l-1-2h0-1c0-1 0-1-1-2 1-1 2-2 3-2l1-1c1-1 3-1 5-1z" class="L"></path><path d="M357 268l1-1v2h1c2 0 4-1 5 0 1 0 1 0 1 1s0 1-1 2c-1 0 0-1-1-1s-3 1-4 2-1 1-2 1l-1-2h0-1c0-1 0-1-1-2 1-1 2-2 3-2z" class="G"></path><path d="M357 268l1-1v2h1c-1 1-2 2-2 4v1l-1-2h0-1c0-1 0-1-1-2 1-1 2-2 3-2z" class="O"></path><path d="M203 111h5 6c1 1 0 1 2 1l3 3h1c1 0 1 0 2 1-1 2-2 4-2 6l-1 1h1v2c-1 0-1 1-1 2v1 1l2 2c-2 2-3 0-3 2v3-1l1 1v1h0-1-2v-1-1h0c1-1 0-1 0-2l-1 2c-1 1-2 1-3 1h0l-3-5h0l-2-2c-1-1-1-2-3-3 0 1-1 1-2 1v-1-4c1 1 1 1 2 1v1l1-1v-2l-1-1-1-1c-1-1-2-1-3-1v-3c-2 1-3 1-5 2l2-2h-2c0 1-1 1-2 1 0 1-1 1-2 1l2-2c3-1 7-2 11-3h-1v-1z" class="M"></path><path d="M210 116c1-1 1-1 2-1h2c0 1 1 2 0 3l-1 1h-1v-1s0-1-1-1l-1-1z" class="u"></path><path d="M204 126c1 0 1 0 1-1h0c1-1 1-2 2-2l1 1h2c-1 2-1 4-3 5-1-1-1-2-3-3z" class="U"></path><path d="M210 124c1 1 1 1 1 2l1 1c-1 3-1 3-3 4h0l-2-2c2-1 2-3 3-5z" class="F"></path><path d="M203 111h5 6c1 1 0 1 2 1l3 3-1 1c-2-1-4-3-6-3-2-1-6-1-8-1h-1v-1z" class="B"></path><path d="M212 127v3c0-1 1-1 2-1h0l1-1h2c0 1 0 3-1 4h0v1l-1 2c-1 1-2 1-3 1h0l-3-5c2-1 2-1 3-4z" class="H"></path><path d="M216 132h-4-1v-1c1 0 2 0 3-1h0c1 0 2 1 2 2h0 0z" class="W"></path><path d="M200 115h1c1-1 2-1 4-2 0 0 1 0 1 1 1 1 2 1 4 2l1 1c-1 2-4 2-6 4l-1-1-1-1c-1-1-2-1-3-1v-3z" class="j"></path><path d="M219 115h1c1 0 1 0 2 1-1 2-2 4-2 6l-1 1h1v2c-1 0-1 1-1 2v1 1l2 2c-2 2-3 0-3 2v3-1l1 1v1h0-1-2v-1-1h0c1-1 0-1 0-2v-1h0c1-1 1-3 1-4 1-1 1-2 1-4v-3c1-2 1-3 0-5l1-1z" class="C"></path><path d="M454 124h0c0-2-1-3-2-5 1-1 2 0 3-1-1-1-3-1-5-2v-1h9c2 0 3 0 4 1h1c1 0 2 1 3 1l1-1c0 2 1 3 1 4v2h1c1 0 1-1 2-2h1 1c1 1 1 2 2 3l-1 1c0 1-1 2-1 3v3c-1 0-2 1-3 1s-2 1-4 1v1c-1 1-2 1-2 3 0 1 1 3 2 4l-12-2h0c-1-1-1-1-2-1l1-1c1-1 1-2 1-3 1-1 1-2 1-3 0-2-1-4-2-6z" class="N"></path><path d="M460 129l-1-3h1l1 1v1 1c1 0 1 1 2 1-1 2-3 5-5 6-1 1-2 1-3 2h0c-1-1-1-1-2-1l1-1c1-1 1-2 1-3 1-1 1-2 1-3 0 1 1 1 1 1l3-2z" class="H"></path><path d="M460 129c0 1 0 2-1 3-1 2-3 3-5 4 1-1 1-2 1-3 1-1 1-2 1-3 0 1 1 1 1 1l3-2z" class="G"></path><path d="M463 130v-5h0l3 3 3-2c0-1 0-1 1-1v2h4v3c-1 0-2 1-3 1s-2 1-4 1v1c-1 1-2 1-2 3 0 1 1 3 2 4l-12-2c1-1 2-1 3-2 2-1 4-4 5-6z" class="B"></path><path d="M470 127h4v3c-1 0-2 1-3 1l1-1-2-3z" class="G"></path><path d="M256 621c1-1 2-1 3-1l1 1c2 0 3 0 4 1-1 3-2 6-2 8s1 4 1 6c-2 1-7 1-9 1l-2-1v1h-1c-1-1-2 0-3-1-1 0 0 0-1 1h-1v-1h0c0-1 0-1-1-2-3 0-3 0-6 1v-2h-2c0 2 0 4-1 5l-1 1-7-1h0-10v-4c11-1 20-2 29-7l9-6z" class="E"></path><path d="M234 633h2l-1 2h0l-2-1v-1h1z" class="d"></path><path d="M228 638l-1-1h-1c-1-1 0-1 0-2 1-1 3-2 5-1h1 0l-1 1c-1 1 0 1 1 2-1 1-2 0-4 1h0z" class="M"></path><path d="M237 633h-1l1-1 12-5-1 3 1 1v1l-1 4c-1 0 0 0-1 1h-1v-1h0c0-1 0-1-1-2-3 0-3 0-6 1v-2h-2z" class="N"></path><path d="M248 630l1 1v1l-1 4c-1 0 0 0-1 1h-1v-1c0-1 0-2 1-3 0-1 0-1 1-3z" class="P"></path><path d="M256 621c1-1 2-1 3-1l1 1c2 0 3 0 4 1-1 3-2 6-2 8s1 4 1 6c-2 1-7 1-9 1l-2-1v1h-1c-1-1-2 0-3-1l1-4 1-1c1 0 1 0 2 1h0l-1 1h1c1 0 1-1 2-1 0 0 0 1 1 1h1l1-2-1-1 3-3h-1l-2 2h0c0-3-1-5 0-8z" class="M"></path><path d="M257 631v5h0c-1-1-2-2-2-3h1l1-2z" class="F"></path><path d="M122 166l8-5h0c0 1-1 1-1 2v1l1 1c-2 4-4 5-8 8l-3 2c-3 2-6 3-10 3h0v-1c-4 1-6 1-10 0h0c-1 2-4 3-6 3h-1v-3c1-1 3-1 3-3-1-1-6-1-8-2h-1c-1-1-1-2-2-2h0c0-1-1-2-1-3h1s1 0 1 1h1s0-1 1-1v-1-1l-3-3c1 0 2 1 3 1h2l7 3v1h1c3 1 7 2 9 2 1-1 1-1 2-1 1 1 2 1 2 1 2-1 2-1 3-2s2-1 3-1v1c1 1 1 1 2 1 1-1 3-1 4-2z" class="W"></path><path d="M106 169c1-1 1-1 2-1 1 1 2 1 2 1 2-1 2-1 3-2s2-1 3-1v1c1 1 1 1 2 1-4 1-7 2-12 1z" class="Y"></path><path d="M84 162c1 0 2 1 3 1h2l7 3v1c-3 0-6-1-9-2l-3-3z" class="I"></path><path d="M129 164l1 1c-2 4-4 5-8 8l-3 2c-3 2-6 3-10 3h0v-1l-2-2h0l2-1c8 0 15-4 20-10z" class="C"></path><path d="M294 548v-4c0-2 1-3 1-5v-1c1-1 2-3 3-4v5c-1 1-1 2-1 4l2-8v1l2 1h3l1 1h1c1-1 2-1 4-1l4 2c1 1 3 2 4 4l1 2v4l-1 2c-1 3-3 4-6 5-3 2-8 2-12 1-3-2-5-5-6-9h0z" class="M"></path><path d="M299 535v1l2 1h3l1 1c-2 2-2 4-2 7 0 1 1 1 1 2h2v1s0 1-1 1h-1l-1 1h1 2c0 1-1 1-2 1h0-2c-1 0-3-1-3-1-2-2-2-5-2-7l2-8z" class="E"></path><path d="M304 551c0-1-2-1-3-1-1-1-2-3-2-4-1-2 0-3 1-5h1c1 2 1 2 1 4h-1v1c0 1 1 2 2 2h3 0s0 1-1 1h-1l-1 1h1 2c0 1-1 1-2 1h0z" class="L"></path><path d="M222 116l1-1c3-2 5-3 8-3l1 1h0 5l-1 1h-1s-1 0-1 1h0 3 3c-1 1-3 2-4 4h1v2h0c-1 1-1 3-2 3 0 1 2 1 2 2v1c1 2 1 3 2 4l3 1v1l-1 1h2v1c-4 0-8 1-11 1l-16 2v-1h2 1 0v-1l-1-1v1-3c0-2 1 0 3-2l-2-2v-1-1c0-1 0-2 1-2v-2h-1l1-1c0-2 1-4 2-6z" class="u"></path><path d="M231 112l1 1h0 5l-1 1h-1s-1 0-1 1h0l-2 1h0-1-1l1-1c1 0 1 0 1-1s-1 0-2-1l1-1z" class="N"></path><path d="M219 128v-1c0-1 0-2 1-2v-2h-1l1-1c1 1 3 0 4 0 0 2-1 2-2 4v-1l-3 3z" class="D"></path><path d="M227 135h3 0c-1-1-2-1-3-2s-1-2-1-3c1-2 3-3 4-5l-1 5c1 1 1 2 2 3 1 0 2 1 2 1h1 1l1-1c2 0 3 1 5 1h2v1c-4 0-8 1-11 1-2-1-4 0-5-1z" class="E"></path><path d="M235 134c-1-1-2-2-2-3-1-2 0-5 1-6l1-1c0 1 2 1 2 2v1c1 2 1 3 2 4l3 1v1l-1 1c-2 0-3-1-5-1l-1 1z" class="R"></path><path d="M237 127c1 2 1 3 2 4h-1v1l-1-1c-1 0-1-1-2-2l1-1v1h0 1v-2z" class="B"></path><path d="M219 128l3-3v1c0 3 2 6 4 8 0 1 0 1 1 1 1 1 3 0 5 1l-16 2v-1h2 1 0v-1l-1-1v1-3c0-2 1 0 3-2l-2-2v-1z" class="V"></path><path d="M590 155c2 2 1 4 2 6 0 3-2 7-4 9l-1 1c-3 2-6 3-9 3l-3 1v1s1 1 2 1c0 1 0 2-1 3l-8-3h0v1c-1 0-2 1-3 1s-1 0-2-1l-1 1c-2 0-7-2-9-3l-3-2-2-1c1-1 1-1 1-2l-6-6v-1-2h0 0c1 1 1 2 3 2h0c5 3 13 5 20 4h1l5-2 6-1h2c1-1 3-4 4-5 1 1 1 2 2 3v1l1-1 3-8z" class="u"></path><path d="M549 171l5 2v1c-1-1-1 0-2 0h-2l-2-1c1-1 1-1 1-2z" class="C"></path><path d="M590 155c2 2 1 4 2 6 0 3-2 7-4 9-1-2 1-4 1-6 0-1 0-1 1-2h1v-1-3h0-1c0 2-1 3-3 5l3-8z" class="a"></path><path d="M584 160c1 1 1 2 2 3v1-1l-2-1h0c0 1-1 2 0 3l1 2s0 1-1 1c-1 1-2 1-3 0l-2-2-1-1h0 2c1-1 3-4 4-5z" class="N"></path><path d="M554 173l9 3v2l-1 1c-2 0-7-2-9-3l-3-2h2c1 0 1-1 2 0v-1z" class="w"></path><path d="M431 622l3 2c1 1 3 2 4 2s1 0 2 1h1 1c1 1 2 1 3 2h1s1 1 2 1h0l6 2c3 1 7 0 10 1v1c0 1 0 2-1 3h-1-1l-1-1h-1v1 1l-1-1v1c-8 1-16 1-24 1l-36-1h-24c-4 1-7 1-11 0-2 0-3 0-5-1l2-1c4 2 10 1 15 1h4l5-1s1 0 1-1c4-3 8-4 11-8l1 1s-1 1-2 1c2 1 2 2 3 3 0 1 0 1 1 1 1 1 2 1 4 1h1l-1 1v1l5 2c1-1 11-1 13-1v-5h0c1 2 0 3 1 5h2v-2c1 1 0 1 1 2 2 0 8 1 10 0-1-1-1-2-2-2v-1-1c-1 0-2-1-2-1 1-2 1-6 1-8l-1-2h0z" class="p"></path><path d="M450 634h3c0 2 0 2-1 3h-2v-3z" class="L"></path><path d="M403 636l5 2c-2 1-8 0-11 0 2 0 4-1 5-1h1v-1z" class="P"></path><path d="M441 633c2 0 2 0 3 1h0c0 1 1 2 1 3h0-4s0-1-1-1c1-1 1-2 1-3z" class="H"></path><path d="M460 636h-1c0-1-1-1-2-1v1l-2-2h9c0 1 0 2-1 3h-1-1l-1-1z" class="M"></path><path d="M438 626c1 0 1 0 2 1h1 1c1 1 2 1 3 2h1s1 1 2 1h0l2 2c1 0 2 1 3 1v1h-3-1c-1 0-1 0-2 1 1 1 1 1 1 2h-1c-1 0-1-2-2-3h-1c-1-1-1-1-3-1l-3-6v-1z" class="N"></path><path d="M431 622l3 2c1 1 3 2 4 2v1l3 6c0 1 0 2-1 3v1c-1 0-1-1-2-1l-1 1h-1c-1-1-1-2-1-3l-1-1-1 1v-1c-1 0-2-1-2-1 1-2 1-6 1-8l-1-2h0z" class="C"></path><path d="M434 624c1 1 3 2 4 2v1h0c-1 0-2 0-3-1l-1-2z" class="m"></path><path d="M433 633v-1l-1-1h1 1 1v-2h0c1 0 1 1 2 3h0l1 1c0 1-1 1-1 2 0 0 0 1 1 1l-1 1h-1c-1-1-1-2-1-3l-1-1-1 1v-1z" class="f"></path><path d="M385 635c4-3 8-4 11-8l1 1s-1 1-2 1c2 1 2 2 3 3 0 1 0 1 1 1 1 1 2 1 4 1h1l-1 1v1 1h-1c-1 0-3 1-5 1h-12c-2 0-5 0-6-1l5-1s1 0 1-1z" class="c"></path><path d="M385 635h3 6 1l-2 1c-2 1-7 1-9 0 0 0 1 0 1-1z" class="T"></path><path d="M385 635c4-3 8-4 11-8l1 1s-1 1-2 1c2 1 2 2 3 3 0 1 0 1 1 1 1 1 2 1 4 1h1l-1 1c-2 0-4-1-6 0h-2-1-6-3z" class="B"></path><path d="M395 629c2 1 2 2 3 3 0 1 0 1 1 1 1 1 2 1 4 1h1l-1 1c-2 0-4-1-6 0l-4-4 2-2z" class="a"></path><path d="M344 470v1h1s1 0 1 1h1l1-1c0 2 1 3 1 4 1 2 3 4 5 5h0c1 1 1 2 2 3 0 0 1 0 1 1 1 0 0 0 1 1h0l1 1c0 1 1 2 2 3l2 2v1l2 2c1 1 1 2 1 3 2 4 5 8 8 12 1 0 1 0 1 1-3 0-5-2-7-4-2-1-4-3-6-4l-4-1c-2-1-4-1-6-1h-2c-1-1-1-1-1-2s0-2-1-4h-1l1-1c-2 0-3-1-5-2h-2c0-1 1 0 1-1l1-1 1-1-2-1h0c0-1-1-2-1-2 0-1 0-2 1-3l-1-1-1 1v-1c0-1-1-2-2-3 2-2 1-5 1-7 1 1 1 2 1 3s1 1 2 2h0c0-2 0-4 1-5 0-1 0-1 1-1z" class="h"></path><path d="M359 498c1 1 1 1 2 1l1 1v2h0l-4-1v-1c0-1 1-1 1-2z" class="G"></path><path d="M342 482l1 1h1 3l1 2v1h0c-1 1-1 1-2 1l-1-1c-1 0-1 0-1 1h-2 0c0-1-1-2-1-2 0-1 0-2 1-3z" class="D"></path><path d="M355 495l4 3c0 1-1 1-1 2v1c-2-1-4-1-6-1h0v-1l2-2h0l1-2z" class="B"></path><path d="M344 488l1 1v-1c2 0 4-1 5 0s1 1 1 2c-1 1-2 3-3 3-2 0-3-1-5-2h-2c0-1 1 0 1-1l1-1 1-1z" class="M"></path><path d="M348 471c0 2 1 3 1 4l-2 2 1 1c-1 2 0 5 1 6v1h-1 0l-1-2h-3c1-1 1-1 1-2 1-1 0-4 0-6v-3-1s1 0 1 1h1l1-1z" class="O"></path><path d="M348 471c0 2 1 3 1 4l-2 2c-1-1-1-1-1-2s-1-2-1-3v-1s1 0 1 1h1l1-1z" class="a"></path><path d="M351 490c1 0 1 0 2 1 0 2 1 3 2 4l-1 2h0l-2 2v1h0-2c-1-1-1-1-1-2s0-2-1-4h-1l1-1c1 0 2-2 3-3z" class="F"></path><path d="M349 498h0c0-1 0-2 1-2h0v1h1c0 1 0 2 1 3h0-2c-1-1-1-1-1-2z" class="C"></path><path d="M351 497c0-2 0-4 2-6 0 2 1 3 2 4l-1 2h0l-2 2v1c-1-1-1-2-1-3z" class="P"></path><path d="M344 470v1h1v1 3c0 2 1 5 0 6 0 1 0 1-1 2h-1l-1-1-1-1-1 1v-1c0-1-1-2-2-3 2-2 1-5 1-7 1 1 1 2 1 3s1 1 2 2h0c0-2 0-4 1-5 0-1 0-1 1-1z" class="I"></path><path d="M345 475c0 2 1 5 0 6 0 1 0 1-1 2h-1l1-1c-1-1-2-2-2-3h1v-1l1 1s0 1 1 1h0v-4-1z" class="t"></path><path d="M339 471c1 1 1 2 1 3s1 1 2 2h0c0 1-1 2 0 3 0 1 1 2 2 3l-1 1-1-1-1-1-1 1v-1c0-1-1-2-2-3 2-2 1-5 1-7z" class="J"></path><path d="M347 477l2-2c1 2 3 4 5 5h0c1 1 1 2 2 3 0 0 1 0 1 1 1 0 0 0 1 1h0l1 1c0 1 1 2 2 3l2 2v1l2 2c1 1 1 2 1 3 2 4 5 8 8 12h-1c-3-2-5-5-7-8-3-3-6-5-10-8-3-3-7-10-8-15l-1-1z" class="M"></path><path d="M394 442c1 2 1 2 1 3v49l-1 2-1-1h0v-2-9h-1v2h-1s-1-1-2-1c-1-1-2-1-3-1h0c-1 0-1 0-1 1-2 1-4 2-5 2h-3s-1 0-1-1c-1-1-1-2-1-4 1-3 3-6 4-9h-1c-2 1-1 2-3 1 2-4 4-7 7-9 1-2 3-4 4-5 3-6 6-12 8-18z" class="D"></path><path d="M388 468h-1-1c1-2 3-4 5-6 1 1 0 1 1 1 0 1 1 2 2 3 0 1 0 2-1 3l-2-2c-1 0-2 1-3 1z" class="T"></path><path d="M394 442c1 2 1 2 1 3-1 2-1 4-2 6 0 2-1 4-2 6-3 6-8 10-12 16h-1c-2 1-1 2-3 1 2-4 4-7 7-9 1-2 3-4 4-5 3-6 6-12 8-18z" class="L"></path><path d="M388 468c1 0 2-1 3-1l2 2v1c1 2 1 6 1 8v1l-2 2h0 1c0 2-1 2-1 3v2h-1s-1-1-2-1c-1-1-2-1-3-1h0c-1 0-1 0-1 1-2 1-4 2-5 2h-3s-1 0-1-1c-1-1-1-2-1-4h0c1-1 1-1 1-2 1-1 1-2 2-2h0v2c0 1 0 2 1 2v-2-2l1-1c1-3 3-5 5-8 1 1 2 1 3 2 0-1 0-1 1-2l-1-1z" class="U"></path><path d="M385 475c1 0 1-1 3-1 0 1 0 1-1 1l1 3-2-1-1-2z" class="L"></path><path d="M393 470c1 2 1 6 1 8v1-3h-1l-1 1h0-1l-1-1h1 1v-1l-1-1h1c1-1 1-3 1-4z" class="H"></path><path d="M388 468c1 0 2-1 3-1l2 2v1c0 1 0 3-1 4h-1c0-2 1-3 1-4v-1h-1l-1 2h-1v-2l-1-1z" class="E"></path><path d="M385 475l1 2h0v2l1 1-1 1c-1-1 0-1-1 0h-1v3h-1l1-2h-1l-1 1h-2l-1-1v-2l2-1c1 0 1-1 2-2 0-1 1-1 2-2z" class="G"></path><path d="M379 480l2-1c0 2-1 2-1 4l-1-1v-2z" class="L"></path><path d="M385 475l1 2h0v2l1 1-1 1c-1-1 0-1-1 0h-1v3h-1l1-2h-1l-1 1c0-1 1-2 2-2 0 0-1-1-1-2 1 0 1 0 1-1l-1-1h0c0-1 1-1 2-2z" class="e"></path><path d="M386 477l2 1 3 3h1 1c0 2-1 2-1 3v2h-1s-1-1-2-1c-1-1-2-1-3-1 0-1 0-1-1-2h1l-1-1h-1 1c1-1 0-1 1 0l1-1-1-1v-2h0z" class="V"></path><path d="M375 482h0c1-1 1-1 1-2 1-1 1-2 2-2h0v2c0 1 0 2 1 2l1 1h2l1-1h1l-1 2h1v-3h1l1 1h-1c1 1 1 1 1 2h0c-1 0-1 0-1 1-2 1-4 2-5 2h-3s-1 0-1-1c-1-1-1-2-1-4z" class="E"></path><path d="M566 188c3 0 6 1 8 1 3 1 6 1 8 1 3 1 5 2 7 5 1 2 2 3 3 6 0 1 1 6 0 8-1 0-1 0-1 1h0-1c-1 0-2 1-3 2-3 1-4 1-6 0h-1c-2-1-4-2-5-3h-1c-1 0-1 0-2 1v2l-2-2c-1-2-1-5-2-7-1-3-3-5-5-8h0v-1c0-2-1-4-2-5 1-1 3 0 4 0h0l1-1z" class="u"></path><path d="M573 205l3 1 1 2v1h-3c-1-1-1-3-1-4z" class="R"></path><path d="M577 208c3 2 4 1 7 1l-3 3h-1c-2-1-4-2-5-3h-1 0 3v-1z" class="D"></path><path d="M579 195c2 0 4 0 5 1v2l-1-1c-2 0-4 0-6 1-1 1-2 1-3 1h0c2-2 3-2 5-3v-1z" class="e"></path><path d="M582 204c1-1 2-1 3 0 1 0 1 1 2 1-1 2-1 2-2 3h-1c-1 0-2-1-3-2v-2h1z" class="j"></path><path d="M570 201h1l1 1v2l1 1c0 1 0 3 1 4h0c-1 0-1 0-2 1v2l-2-2c1-2 0-3 0-4-1-2-1-3 0-4v-1z" class="T"></path><path d="M589 198c0 1 0 1 1 2l1-1c0 1 0 1 1 2 0 1 1 6 0 8-1 0-1 0-1 1h0-1c-1 0-2 1-3 2-3 1-4 1-6 0l3-3h2c2-1 3-2 3-4 1-2-1-4-2-5l2-2z" class="E"></path><path d="M574 189c3 1 6 1 8 1 3 1 5 2 7 5 1 2 2 3 3 6-1-1-1-1-1-2l-1 1c-1-1-1-1-1-2l-2 2c0-1-1-2-3-2v-2c-1-1-3-1-5-1 0-1 0-2-2-3-1-1-1-1-2 0 0-1-1-2-1-3z" class="j"></path><path d="M582 190c3 1 5 2 7 5 1 2 2 3 3 6-1-1-1-1-1-2l-1 1c-1-1-1-1-1-2-2-2-3-4-6-6 0 0 0-1-1-2z" class="F"></path><path d="M566 188c3 0 6 1 8 1 0 1 1 2 1 3-1 0-1 0-2 1-1 0-2 1-3 2-1 2-2 3-1 5v1h1v1c-1 1-1 2 0 4 0 1 1 2 0 4-1-2-1-5-2-7-1-3-3-5-5-8h0v-1c0-2-1-4-2-5 1-1 3 0 4 0h0l1-1z" class="V"></path><path d="M561 189c1-1 3 0 4 0h0v3l1 1 1 1v3h0c-2 0-2-1-4-2h0v-1c0-2-1-4-2-5z" class="M"></path><path d="M566 188c3 0 6 1 8 1 0 1 1 2 1 3-1 0-1 0-2 1-1 0-2 1-3 2h-1l1-2-1-1-2 2-1-1-1-1v-3l1-1z" class="w"></path><path d="M566 193h1v-1c-1 0-1-1-1-1v-1c1-1 1-1 2-1 0 1 0 2 1 3l-2 2-1-1z" class="C"></path><path d="M568 189c2 1 3 1 4 2-1 1-1 2-2 2l-1-1c-1-1-1-2-1-3z" class="L"></path><path d="M307 149v1h1 0l2 1h0l-1 1h0c0 2 0 3-1 4l-1 1h0s2 0 2 1c1 0 3 2 3 2v1c1 1 1 2 1 3v1s1 0 1 1h0c1-1 1-1 2 0 0 1-1 3-1 5l1 4v2l-1 1c0 1 0 2-1 3v-2h-2-1l1 1s1 1 1 2v1h-2l-1-1h-2c0 1-1 1-1 2 0-1-2-1-3 0v-1h-1c-1-2-3-4-4-6l-2-4h0c-1-2-1-3-1-5h-1-1v-4-11h1v-5 4h2c0 1 0 2 1 3l3-4h1c1 0 1 0 2-1 1 0 2-1 3-1z" class="W"></path><path d="M304 183s1 0 2-1v-3c2 0 3 2 4 3h-2c0 1-1 1-1 2 0-1-2-1-3 0v-1zm5-17h1c0-1-1-2-1-3l-1-1h0l1-1h1c0 1 0 2 1 3h2v1l-1 1v1h-1-2v-1z" class="a"></path><path d="M308 172h2c0 1 1 1 2 2h1-3c-1 1-1 2-2 3h0-1v-2c1-1 1-2 1-3h0z" class="T"></path><path d="M304 171c-1-1-3-2-4-3v-2c0-1 1-2 2-3 2 0 3 0 4 1l1 1c0 1 1 1 2 1v1h-1l-3 3v1h-1z" class="B"></path><path d="M306 164l1 1c0 1 1 1 2 1v1h-1l-3 3v1h-1v-4c1-1 1-2 2-3z" class="L"></path><path d="M295 148v4h2c0 1 0 2 1 3l-1 1c-1 1-1 2-1 4 0 1 1 2 1 3v2h0c0 2 1 4 1 5 1 1 1 3 1 4l-1-2-1 1h0c-1-2-1-3-1-5h-1-1v-4-11h1v-5z" class="J"></path><path d="M297 152c0 1 0 2 1 3l-1 1c-1 1-1 2-1 4-1-2-1-6-1-8h2z" class="N"></path><path d="M313 165s1 0 1 1h0c1-1 1-1 2 0 0 1-1 3-1 5l1 4v2l-1 1c0 1 0 2-1 3v-2c0-2-1-3-1-5h-1c-1-1-2-1-2-2h-2l-3-1v-1l3-3h1 2 1v-1l1-1z" class="m"></path><path d="M313 165s1 0 1 1h0v2c1 0 1 0 1 1h-2 0c-1-1-1-1-1-2h0v-1l1-1z" class="i"></path><path d="M313 165s1 0 1 1v1l-2-1 1-1z" class="S"></path><path d="M310 170c2 0 2 0 4 2 0 1-1 1-1 1l-1 1c-1-1-2-1-2-2-1-1-1-1 0-2z" class="B"></path><path d="M308 167l1 1c0 1-1 1-1 1 1 1 1 1 2 1-1 1-1 1 0 2h-2l-3-1v-1l3-3z" class="F"></path><path d="M307 149v1h1 0l2 1h0l-1 1h0c0 2 0 3-1 4l-1 1h0c-1 1-3 2-4 2h-2c-2 2-3 4-4 6v-2c0-1-1-2-1-3 0-2 0-3 1-4l1-1 3-4h1c1 0 1 0 2-1 1 0 2-1 3-1z" class="G"></path><path d="M302 155l2 2h0v1h-3 0c0-1 1-2 1-3z" class="P"></path><path d="M304 152c0 2 0 3 1 4l-1 1-2-2c1 0 0-1 0-1l2-2z" class="I"></path><path d="M297 156l2 1c-1 2-2 4-2 6 0-1-1-2-1-3 0-2 0-3 1-4z" class="O"></path><path d="M307 149v1h1c-1 0-2 1-3 1l-1 1-2 2-3 3-2-1 1-1 3-4h1c1 0 1 0 2-1 1 0 2-1 3-1z" class="q"></path><path d="M308 150h0l2 1h0l-1 1h0c0 2 0 3-1 4l-1 1c-1-1-1-1-2-3v-2h1l-1-1c1 0 2-1 3-1z" class="O"></path><path d="M308 150h0l2 1h0l-1 1h0c-2 0-2 0-4 2h0v-2h1l-1-1c1 0 2-1 3-1z" class="v"></path><path d="M578 111l5-2c1 1 2 3 3 3 2 2 2 0 2 4h1l2 26v1l-1 1h-1v-2h-1v3l-1-2v-1l-3-6h-2l-2-2h0c-2-1-3-1-5-1l-1-1h1 0l1-1-1-1h-3 0-5c0-2 0-2 1-3h1v-2h-1v-1c-1-1-1-2-2-2s-1-1-2-1l3-1 4 2h2v-2c0-2-1-2-2-3h-2l-1-1h-1-1s-1-1-2-1h0l-11-4c2-1 3-1 5 0 6 0 12 0 19-1l1 1z" class="M"></path><path d="M577 119c0 1 0 3-1 4-1 0-1 0-1-1-1-1-1-1-1-2v-2h2 0l1 1z" class="u"></path><path d="M558 111c6 0 12 0 19-1l1 1-6 1c1 2 4 3 5 5v1h0-1 0 0c-3-3-12-6-16-7h-2z" class="B"></path><path d="M567 120l4 2c1 1 3 2 4 2 5 2 10 8 12 12l2 6h-1v3l-1-2v-1l-3-6c-1-3-4-6-6-8-1-1-2-1-3-2-2-1-3-2-5-3v1c-1 0-1 0-1 1h-1v-1c-1-1-1-2-2-2s-1-1-2-1l3-1z" class="G"></path><path d="M570 124v-1c2 1 3 2 5 3 1 1 2 1 3 2 2 2 5 5 6 8h-2l-2-2h0c-2-1-3-1-5-1l-1-1h1 0l1-1-1-1h-3 0-5c0-2 0-2 1-3h1v-2c0-1 0-1 1-1z" class="L"></path><path d="M575 132h2c1-1 0-1 2-1 1 1 1 1 1 3h0c-2-1-3-1-5-1l-1-1h1z" class="g"></path><path d="M571 126h1 1c1 1 4 2 4 4h-2-3c0-1 0-1 1-2l-2-2z" class="i"></path><path d="M570 124l1 2 2 2c-1 1-1 1-1 2h0-5c0-2 0-2 1-3h1v-2c0-1 0-1 1-1z" class="B"></path><path d="M589 116l2 26v1l-1 1h-1v-2l-2-6 1 1v1l1-1c-1-7-6-13-12-18l-1-1h1s1 1 2 0c1 0 2-2 3-2 1 4 3 6 6 9h0v-3c1-1 1-5 1-6z" class="S"></path><path d="M589 137l2 6-1 1h-1v-2l-2-6 1 1v1l1-1z" class="a"></path><path d="M578 111l5-2c1 1 2 3 3 3 2 2 2 0 2 4h1c0 1 0 5-1 6v3h0c-3-3-5-5-6-9-1 0-2 2-3 2-1 1-2 0-2 0h0v-1c-1-2-4-3-5-5l6-1z" class="W"></path><path d="M588 116h1c0 1 0 5-1 6 0-1 0-2-1-2v-2c0-1 1-1 1-2h0z" class="C"></path><path d="M436 114c2-1 4-2 6-2h7c4 0 8-1 12 0 3 1 5 2 7 4l-1 1c-1 0-2-1-3-1h-1c-1-1-2-1-4-1h-9v1c2 1 4 1 5 2-1 1-2 0-3 1 1 2 2 3 2 5h0c1 2 2 4 2 6 0 1 0 2-1 3-1 0-2 1-3 2l-1-1c-1 1-1 1-2 1h-1-1c-1 1-2 0-3 0h-2l-19-2s-1 0-1-1c3-1 6-4 8-7 0-1 0-3 1-4h1s0-3 1-3l-1-2c1-1 3-2 4-2z" class="M"></path><path d="M436 128h-1c0 1 0 1-2 1v-1c-1-1 0-3 0-4h1l1 3h1v1z" class="l"></path><path d="M442 122v2h0c0 1-2 2-3 3v1h3v1h1v1h0c-3 0-5-1-7-2v-1c1-3 4-3 6-5z" class="C"></path><path d="M442 122h1 1l2 2v2h0c-1 2-1 3-2 4l-1 2v-2-1h-1v-1h-3v-1c1-1 3-2 3-3h0v-2z" class="R"></path><path d="M443 126l1 1v3l-1 2v-2-1h-1v-1c1 0 1-1 1-2z" class="B"></path><path d="M442 124l2-1h0l1 1h-2v1 1c0 1 0 2-1 2h-3v-1c1-1 3-2 3-3h0z" class="J"></path><path d="M422 132c3-1 6-4 8-7 0-1 0-3 1-4h1 0c0 2-2 6-2 8-1 0-1 2-1 2s0 1 1 1c3 2 9 2 13 3h-1l-19-2s-1 0-1-1z" class="B"></path><defs><linearGradient id="R" x1="443.027" y1="113.856" x2="446.729" y2="126.981" xlink:href="#B"><stop offset="0" stop-color="#181718"></stop><stop offset="1" stop-color="#373736"></stop></linearGradient></defs><path fill="url(#R)" d="M444 116c-1 0-2 0-3 1-4 1 0 2-1 4-2-1-4-2-4-3v-1c1-2 3-3 5-3 2-1 5 0 7 0 3 0 8-1 11 1h-9v1c2 1 4 1 5 2-1 1-2 0-3 1 1 2 2 3 2 5h0c1 2 2 4 2 6 0 1 0 2-1 3-1 0-2 1-3 2l-1-1c-1 1-1 1-2 1h-1-1c-1 1-2 0-3 0h-2 1l1-2c0-1 0-1-1-1l1-2c1-1 1-2 2-4h0v-2l-2-2c0-1 0-2 1-3-1-1-1-1-1-3z"></path><path d="M454 124c1 2 2 4 2 6 0 1 0 2-1 3-1 0-2 1-3 2l-1-1c1 0 1-1 2-1v-1c2-2 0-6 1-8z" class="B"></path><path d="M451 129l1-1h0v3l-2 2-2 2h-1c-1 1-2 0-3 0h-2 1l1-2c1 0 3 0 4-1l3-3h0z" class="C"></path><path d="M448 126h2l1 1v2l-3 3c-1 1-3 1-4 1 0-1 0-1-1-1l1-2c1-1 1-2 2-4h0 2z" class="U"></path><path d="M446 126h2c1 0 1 0 2 1 0 1 0 1-1 2-1 0-2 0-3-1v-2h0z" class="u"></path><path d="M444 116c2 1 6 4 7 7v6h0v-2l-1-1h-2-2v-2l-2-2c0-1 0-2 1-3-1-1-1-1-1-3z" class="H"></path><path d="M445 119l3 3c0 1 0 2-1 3-1 0 0 0-1-1l-2-2c0-1 0-2 1-3z" class="u"></path><path d="M334 386v-2h0 1c1 0 2 1 3 1h0l1 1 2 2v1l1 5v3l1 1h1 1 0l1-1c1 0 2 0 3 1l-1 1c2 2 2 2 4 2-1 2-2 6-3 8l-2 3c-1 1-2 2-2 3-1 0-1-1-2-1h-1c-1 1-1 1-1 2v-1l-1-1c0 1-1 1-1 1h-1l-1-1c-1-1-2-3-3-5l-2-2h0l-3-3c-2-1-4-1-6-2l-2-1c-2 1-4 0-6-1l-1 1-1-1s0 1-1 1v-2l-3-3h2c2-1 3-1 4-1h3c1 0 2-1 4-1h1v-1c0-1-1-1-1-2h0 1c1 0 1 1 2 1v1h2c0-1 0-1-1-2h0c-1-1-1-2-2-3 1 0 3 0 5 1 1-2 1-2 2-3h3z" class="N"></path><path d="M331 389h2c1-1 2-1 3-1l1 1s-1 1-1 2c-2-1-3-2-5-2z" class="I"></path><path d="M313 396h0 2c1-1 3-1 4 0 0 0 1 1 1 2l-3-1-4-1z" class="H"></path><path d="M334 386v-2h0 1c1 0 2 1 3 1h0l1 1s-1 1-2 1l-1 1c-1 0-2 0-3 1h-2-2c1-2 1-2 2-3h3z" class="E"></path><path d="M322 396c2-1 3-1 6-2h5v1 1h0c-1 0-1 0-2 1h0-1-1-2 0c1 2 1 1 1 3-2-2-4-3-6-4zm17-10l2 2v1l1 5v3h0l-2-2-1-3-3-1c0-1 1-2 1-2l-1-1 1-1c1 0 2-1 2-1z" class="i"></path><path d="M337 387l1 1s0 1-1 1l-1-1 1-1z" class="Y"></path><path d="M339 392l1 3s-1 1-2 1v1c1 1 2 1 3 2h1v3h-4-1-2c-1 0-1-1-2-2l1-1h0 1 2c1-1 0-1-1-2v-4l3-1z" class="M"></path><path d="M340 395l2 2h0l1 1h1c0 2 0 2 1 3l1 1c0 2 0 3 1 4l-3 3h1c-1 1-1 1-2 1l-1 1-2-1-1-3c0-2-1-3-2-5h1 4v-3h-1c-1-1-2-1-3-2v-1c1 0 2-1 2-1z" class="U"></path><path d="M342 397l1 1h1c0 2 0 2 1 3l1 1c0 2 0 3 1 4l-3 3c-1-1-2-1-3-2l1-3v-1c0-2 1-4 0-6h0z" class="g"></path><path d="M342 403c1 0 1 0 2 1v1h0-2v-1-1z" class="I"></path><path d="M346 397c1 0 2 0 3 1l-1 1c2 2 2 2 4 2-1 2-2 6-3 8l-2 3c-1 1-2 2-2 3-1 0-1-1-2-1l-2-2-1-1v-1l2 1 1-1c1 0 1 0 2-1h-1l3-3c-1-1-1-2-1-4l-1-1c-1-1-1-1-1-3h1 0l1-1z" class="H"></path><path d="M346 397c1 0 2 0 3 1l-1 1h-2l-1-1 1-1z" class="N"></path><path d="M309 396h2 2l4 1 3 1h1l1-1v-1c2 1 4 2 6 4 0-2 0-1-1-3h0 2 1 1 0c0 2 0 6 2 7h1c0-1 0-1 1-2h2c1 2 2 3 2 5l1 3v1l1 1 2 2h-1c-1 1-1 1-1 2v-1l-1-1c0 1-1 1-1 1h-1l-1-1c-1-1-2-3-3-5l-2-2h0l-3-3c-2-1-4-1-6-2l-2-1c-2 1-4 0-6-1l-1 1-1-1s0 1-1 1v-2l-3-3z" class="C"></path><path d="M321 398l1-1v-1c2 1 4 2 6 4l-1 1-6-3z" class="d"></path><path d="M332 407h2c1 1 1 2 1 2l1 1 1-1 2-2 1 3v1l1 1 2 2h-1c-1 1-1 1-1 2v-1l-1-1c0 1-1 1-1 1h-1l-1-1c-1-1-2-3-3-5l-2-2z" class="Y"></path><path d="M340 411l1 1c-1 1-2 1-3 1l-1-1 1-1h2z" class="D"></path><path d="M309 396h2 2l4 1c0 1 1 2 1 2 1 1 2 1 2 1h2s1 1 2 1h-3c-2 1-4 0-6-1l-1 1-1-1s0 1-1 1v-2l-3-3z" class="E"></path><path d="M375 474c2 1 1 0 3-1h1c-1 3-3 6-4 9 0 2 0 3 1 4 0 1 1 1 1 1h3c1 0 3-1 5-2 0-1 0-1 1-1h0c1 0 2 0 3 1 1 0 2 1 2 1h1v-2h1v9 2h0l1 1 1-2v13c-1 2 0 3 0 4v5h-1l-1 1v1h-2l-1-2h-3 0v-1c-1 0-2 0-3-1s-3-2-3-4c-1 0-1-1-1-2 0 1-1 1-1 2l-1-2h-1 0-1l-3-10c-1-3-2-6-2-9h0v-1h1v-1-3l1-1 1-1c-1-1 0-2 0-4 0-1 1-3 1-4z" class="W"></path><path d="M390 494l1-2 2 1v2l-1 1-1-1-1-1z" class="B"></path><path d="M386 484c1 0 2 0 3 1h-3l-1 1c-1 1-2 2-4 2h0l-1 2c-1 0-2-1-2-1l2-2c1 0 3-1 5-2 0-1 0-1 1-1h0z" class="N"></path><path d="M379 499c1-1 2-1 3-1h1l1 1 1 2 1-1v-1c1 1 1 2 2 2l1 1h-1-2c-1 1-3 2-5 3h0l-1 3c0 1-1 1-1 2l-1-2v-3c-1-1 0-2 0-3s0-2 1-3h0z" class="D"></path><path d="M379 499c1-1 2-1 3-1v1 1c0 1 0 2-1 2h-1v-3h0-1z" class="f"></path><path d="M378 505v-1h3v1l-1 3c0 1-1 1-1 2l-1-2v-3z" class="V"></path><path d="M386 496h1c1-2 2-2 3-2l1 1 1 1 1-1h0l1 1 1-2v13c-1 2 0 3 0 4l-1-1c0-1-1-2-1-4-3-2-3-3-7-4h2 1l-1-1c-1 0-1-1-2-2v-3z" class="T"></path><path d="M386 496h1c1-2 2-2 3-2l1 1-2 2v2c-1-1-1-1-1-2 0 0-1-1-2-1z" class="v"></path><path d="M393 495h0l1 1 1-2v13c-1 2 0 3 0 4l-1-1c0-1-1-2-1-4-3-2-3-3-7-4h2 1l-1-1h1c1-1 1-2 2-3h0 0c0 3 1 6 3 9 0-4 0-8-2-11l1-1z" class="G"></path><path d="M375 474c2 1 1 0 3-1h1c-1 3-3 6-4 9 0 2 0 3 1 4 0 1 1 1 1 1h3l-2 2c-1 1-1 1-2 1l1 1 3 2c-2 2-2 3-2 5v1h1c-1 1-1 2-1 3s-1 2 0 3v3h-1 0-1l-3-10c-1-3-2-6-2-9h0v-1h1v-1-3l1-1 1-1c-1-1 0-2 0-4 0-1 1-3 1-4z" class="Z"></path><path d="M372 487h1v2c-1 1-1 0-2 0v-1h1v-1z" class="t"></path><path d="M373 483l1-1v2c-1 1 0 3-1 3h-1v-3l1-1z" class="G"></path><path d="M376 498l1-1 1 1v1h1c-1 1-1 2-1 3h-1c-1-2-1-3-1-4z" class="H"></path><path d="M377 508v-3c-1-2-2-4-2-6-1-1-1-1-1-2 1 1 1 1 1 2h0c1 1 1 2 2 3h1c0 1-1 2 0 3v3h-1z" class="L"></path><path d="M376 490l1 1 3 2c-2 2-2 3-2 5l-1-1-1 1c0-1 0-2-1-3v-2h1 1c-1-1-1-1-1-2v-1z" class="N"></path><path d="M375 474c2 1 1 0 3-1h1c-1 3-3 6-4 9 0 2 0 3 1 4 0 1 1 1 1 1h1l-4 5v-8-2c-1-1 0-2 0-4 0-1 1-3 1-4z" class="J"></path><path d="M386 502c4 1 4 2 7 4 0 2 1 3 1 4l1 1v5h-1l-1 1v1h-2l-1-2h-3 0v-1c-1 0-2 0-3-1s-3-2-3-4c-1 0-1-1-1-2l1-3h0c2-1 4-2 5-3z" class="M"></path><path d="M384 514h2v-1c1-2-1-2-1-4l2-3c2 1 3 2 4 2v1s0 1 1 2h1l1-1 1 1v5h-1l-1 1v1h-2l-1-2h-3 0v-1c-1 0-2 0-3-1z" class="n"></path><path d="M389 510h1c0 2 0 2-1 3h-2c0-2 1-2 2-3z" class="N"></path><path d="M391 508v1s0 1 1 2h1c-1 1-1 2-2 3l-2-1c1-1 1-1 1-3h-1l2-2z" class="H"></path><path d="M394 510l1 1v5h-1l-1 1v1h-2l-1-2h-3 0v-1c1 0 2-1 4-1 1-1 1-2 2-3l1-1z" class="V"></path><path d="M314 401l1-1c2 1 4 2 6 1l2 1c2 1 4 1 6 2l3 3h0l2 2c1 2 2 4 3 5l1 1h1s1 0 1-1l1 1v1c0-1 0-1 1-2h1c1 0 1 1 2 1l-1 1c2 1 3 0 5 0 1 2 1 2 0 3 0 2-1 5-3 6v1h1 0c1 0 1 0 1 1v1l1 1c-1 4-1 8-2 12-1 1-1 3-2 4 0 1 0 2-1 3h-1c0-3-1-6-2-8 0-2 0-4-1-6v-1c0-2-1-3-3-5v-2h0c-2-3-5-6-7-8-1-1-2-3-4-3l-1-1h-1c-2-1-3-1-6-1h-5-2c-1 1-4 0-6 1h-1-3 0v-3h-1v-2h0 2l1-1c1 0 1 1 2 2l2-1c1 0 2-1 4-2 2-2 3-4 3-6z" class="W"></path><path d="M334 416v-1c1 1 1 2 2 2v2-1c-2 0-2-1-2-2z" class="N"></path><path d="M334 409c1 2 2 4 3 5h0c-1 0-2 1-3 1v1-3-4z" class="H"></path><path d="M329 404l3 3h-3v1l-1 1c-1 1-1 1-2 1v-1-2-1-1c1 0 2-1 3-1z" class="N"></path><path d="M326 406l2 1c0 1 0 1 1 1l-1 1c-1 1-1 1-2 1v-1-2-1z" class="a"></path><path d="M328 407c0 1 0 1 1 1l-1 1h0c-1-1-1-1 0-2z" class="L"></path><path d="M340 434l4 4c0 2 0 6 1 7 0 1 0 2-1 3h-1c0-3-1-6-2-8 0-2 0-4-1-6z" class="a"></path><path d="M323 407l2 2h1v1l-1 1c-1 1-8 0-10 0 0-2 1-2 2-2h0c2-1 4 0 6-2z" class="f"></path><path d="M323 407l2 2-1 1-1-1h-6c2-1 4 0 6-2zm14 7l1 1h1s1 0 1-1l1 1v1h-1l-1 1c0 2 1 5 3 7h0c-1 0-1 0-2 1h0v1 4c-1-1-1-1-1-2l-3-9v-2c-1 0-1-1-2-2 1 0 2-1 3-1h0z" class="J"></path><path d="M337 414l1 1v2h-2c-1 0-1-1-2-2 1 0 2-1 3-1h0z" class="M"></path><path d="M334 415c1 0 2-1 3-1 0 1 0 2-1 3-1 0-1-1-2-2z" class="W"></path><path d="M341 416c0-1 0-1 1-2h1c1 0 1 1 2 1l-1 1c2 1 3 0 5 0 1 2 1 2 0 3 0 2-1 5-3 6v-2h-2l-1 2h0l-1-1h0c-2-2-3-5-3-7l1-1h1z" class="R"></path><path d="M342 424h0c-2-2-3-5-3-7l1-1c0 2 1 2 2 4-1 1-1 1-1 2 1 1 2 1 3 1l-1 2h0l-1-1z" class="C"></path><path d="M344 416c2 1 3 0 5 0 1 2 1 2 0 3 0 2-1 5-3 6v-2s1 0 1-1v-2h1v-2c1 0 0 0 0-1h-2v2h-1c-1-1-1-2-1-3z" class="E"></path><path d="M314 401l1-1c2 1 4 2 6 1l2 1c2 1 4 1 6 2-1 0-2 1-3 1v1 1 2h-1l-2-2c-2 2-4 1-6 2h0c-1 0-2 0-2 2h-3l-2 1 1 1h2-2c-1 1-4 0-6 1h-1-3 0v-3h-1v-2h0 2l1-1c1 0 1 1 2 2l2-1c1 0 2-1 4-2 2-2 3-4 3-6z" class="D"></path><path d="M315 408l1 1h1c-1 0-2 0-2 2h-3l-2-2 5-1z" class="J"></path><path d="M315 400c2 1 4 2 6 1l2 1c-1 0-2 0-3 1h0c-1-1-2-1-4 0h0-1v-3z" class="Q"></path><path d="M306 411l4-2 2 2-2 1 1 1h2-2l-5-1v-1z" class="T"></path><path d="M318 405l1 1 1 1h3c-2 2-4 1-6 2h0-1l-1-1 1-1h0c1-2 1-2 2-2z" class="d"></path><path d="M318 405l1 1 1 1h-4 0c1-2 1-2 2-2z" class="K"></path><path d="M301 414l5-3v1l5 1c-1 1-4 0-6 1h-1-3z" class="J"></path><path d="M323 402c2 1 4 1 6 2-1 0-2 1-3 1v1 1 2h-1l-2-2h-3l-1-1-1-1c1-1 2-1 2-2h0c1-1 2-1 3-1z" class="Y"></path><path d="M323 402c2 1 4 1 6 2-1 0-2 1-3 1v1 1c-1-2-4-3-6-4 1-1 2-1 3-1z" class="n"></path><path d="M285 582h0c2-2 2-6 4-7v1c-1 0-1 1-1 1v2l-1 1s0 1-1 2v2c-2 7-4 15-4 23 0 4 1 7 2 11 0 0 1 3 1 4s-3 4-3 5 0 2 1 2c1 2 4 2 5 4l-1 1v1c-2 0-3 2-5 2l-1 1c-4-2-10 0-14-1h-1c-1-2-1-3 0-4h-1v3h-2 0c0-2-1-4-1-6s1-5 2-8h1 0v1l1-1v-2c1-1 1-1 2-1 1-2 0-3 0-5s1-3 2-5h0c0-1 1-2 2-3 1 0 4-7 6-8h1 1c0-2 0-4 1-5 1-4 3-7 4-11z" class="W"></path><path d="M267 637c0-1 0-1 2-2 0-1 1-2 2-3l1 1c-1 1-1 1-1 2-1 1-2 1-3 1h3c1-1 1 0 2 0h3v1h6l-1 1c-4-2-10 0-14-1z" class="H"></path><path d="M278 598h1 1l-1 9h-1c-1 1-2 1-3 2h0v-4-1h0 1c1-2 1-4 2-6z" class="j"></path><path d="M275 605l1-1c1 1 2 2 2 3-1 1-2 1-3 2h0v-4z" class="a"></path><path d="M275 604v1 4 1c0 3 2 4 3 6l-1 1c-2-1-2-2-3-3v6l-2 2v-1c1-1 0-5 0-7 0-3 2-7 3-10z" class="E"></path><path d="M265 622h0v1l1-1v-2c1-1 1-1 2-1 0 1 0 2 1 3h2s1 0 1-1v1c-2 1-3 2-5 3-1 1-2 3-2 5l3 2h0c-1 1-1 1-2 1h-1v3h-2 0c0-2-1-4-1-6s1-5 2-8h1z" class="e"></path><path d="M278 598c-1 2-1 4-2 6h-1 0c-1 3-3 7-3 10 0 2 1 6 0 7 0 1-1 1-1 1h-2c-1-1-1-2-1-3 1-2 0-3 0-5s1-3 2-5h0c0-1 1-2 2-3 1 0 4-7 6-8z" class="a"></path><path d="M282 627c0 1 0 2 1 2 1 2 4 2 5 4l-1 1v1c-2 0-3 2-5 2h-6v-1h-3l9-9z" class="j"></path><path d="M287 634v1c-2 0-3 2-5 2h-6v-1l11-2z" class="N"></path><path d="M315 332c1-1 2-1 3-1 3-1 4-1 6 0h2l-1 2 1 1h1 1c0 2 0 2-2 3 2 1 3 3 4 4 2 4 4 9 4 14h-2 0c-1 1-1 1-3 1-1 2-2 4-3 5-2 1-3 2-3 3-1 0-1 1-2 0v-2l3-3c1-2 2-5 2-7l-2 1c-1 0-2 1-2 1-1 2-2 4-4 4l-1 1c-2 1-5 1-7 0-2 0-4-2-5-4-2-3-3-7-2-10 0-2 1-3 2-4l2-4c1 0 1-1 2-1h0c0-1 1-1 1-1h0l1-1c-1 1-1 0-2 0v-1l1-2h2l1 1h2z" class="W"></path><path d="M305 341c1 3 0 6 1 10h-1c-2-2 0-4-2-6 0-2 1-3 2-4z" class="O"></path><path d="M319 352h0c-1 0-1-1-2-2-1-2-2-3-3-4l1-1c2-1 4-1 6 0 0 1-1 1 0 3l-1 1c-1-2-2-3-3-4h-1v2c1 2 3 2 3 5z" class="S"></path><path d="M319 352c0-3-2-3-3-5v-2h1c1 1 2 2 3 4 2 1 2 3 2 5-1 2-2 4-4 4l-1 1 1-1c1 0 1-1 2-1l-1-1v-1c1 0 1 0 1-1h0c1-1 1-2 1-3h0-1c0-2-2-4-3-4h0l2 2v2 1z" class="N"></path><path d="M321 345l3 3 1 2c1 0 1 1 1 2l-2 1c-1 0-2 1-2 1 0-2 0-4-2-5l1-1c-1-2 0-2 0-3z" class="m"></path><path d="M321 348c1 0 2 1 2 2v2h1c1-1 0-1 1-2 1 0 1 1 1 2l-2 1c-1 0-2 1-2 1 0-2 0-4-2-5l1-1z" class="D"></path><path d="M330 341c2 4 4 9 4 14h-2 0c-1 1-1 1-3 1h-1c0-1 0-1-1-1h1 1 0c1 0 2-2 3-2 0 0 0-1 1-1 0-1-1-2-2-2 0-2-1-3-2-5 0-1 1-2 1-4z" class="M"></path><path d="M324 348c1 0 1 1 2 2h0c1 1 1 2 1 4v1c1 0 1 0 1 1h1c-1 2-2 4-3 5-2 1-3 2-3 3-1 0-1 1-2 0v-2l3-3c1-2 2-5 2-7 0-1 0-2-1-2l-1-2z" class="T"></path><path d="M328 356h1c-1 2-2 4-3 5v-2h0c1-1 1-2 2-3z" class="H"></path><path d="M315 332c1-1 2-1 3-1 3-1 4-1 6 0h2l-1 2 1 1h1 1c0 2 0 2-2 3l-4-2c-4-1-8-1-11 0l-4 2c1 0 1-1 2-1h0c0-1 1-1 1-1h0l1-1c-1 1-1 0-2 0v-1l1-2h2l1 1h2z" class="Y"></path><path d="M309 333l1-2h2l1 1c-1 1-2 1-4 1z" class="L"></path><path d="M324 331h2l-1 2 1 1h1 1c0 2 0 2-2 3l-4-2-1-2v-1h0 3v-1z" class="D"></path><path d="M353 289c1-1 1-2 2-3h1v1h1v-1c1-1 6-3 7-3 2 0 3-1 4-1v1c2 0 3 0 5 1 1 1 1 1 1 2v1l-1 3-1 1 1 1s0 1 1 1l-3 2v1l-1-1c-1 0-1 0-2-1l-1 1c0 1-1 2-2 3h2v1l-1 4h0c0 2 0 3 1 5l-1 1c0-1-1-1-1-1 0 1-1 3-1 4-1 1-2 1-2 2l-1 1h0l-1-1v1l-2 2h0-4l-1 1 1 4-1 1-8 18v1l-1-1v1h-1v-5-6 3h-1v-5c0-3 0-6 1-9v-2 1h0c0 1 1 1 1 2v-5c0-4 2-7 2-11 1-2 2-4 2-6 1-4 3-7 5-10z" class="N"></path><path d="M368 290v2l-1 1h-1l-2-2c1-1 3-1 4-1z" class="k"></path><path d="M355 300v-1c0-1 0-2 1-3h4l-2 1-3 3z" class="L"></path><path d="M364 291l2 2h-1l-1 1 1 1h2c0 1-1 2-2 3s-2 1-3 2l-1-1 1-1 1-1-3-1v-1l4-4z" class="F"></path><path d="M360 296v-1h1 3v1s0 1-1 1l-3-1z" class="q"></path><path d="M368 290c2-1 3-1 5 0l-1 1 1 1s0 1 1 1l-3 2v1l-1-1c-1 0-1 0-2-1l-1 1h-2l-1-1 1-1h1 1l1-1v-2z" class="B"></path><path d="M368 290c2-1 3-1 5 0l-1 1 1 1s0 1 1 1l-3 2 1-2c0-1-1-1-2-2-1 0-1 0-2 1v-2z" class="G"></path><path d="M360 296l3 1-1 1-1 1 1 1c-1 0-1 0-2 1l-1 1c-1 1-2 1-3 2l-1-1c-1 0-1 0-1 1-1 1-2 3-2 4-2 4-2 10-3 15 0 2 0 8-2 10v-3c2-5 1-11 3-17 0-4 3-7 4-10v-2l1-1 3-3 2-1h0z" class="I"></path><path d="M360 296l3 1-1 1-1 1c-1 1-3 2-4 2h-2-1l1-1 3-3 2-1h0z" class="Z"></path><path d="M360 296l3 1-1 1h-2l-2-1 2-1h0z" class="b"></path><path d="M347 330v3c2-2 2-8 2-10 0 1 0 1 1 2l1-1v1l1 1c0-1 1-2 1-3l-8 18v1l-1-1v1h-1v-5h1 0c0-1 0-2 1-3-1-4 0-7 0-11 0 1 0 2 1 3 0 1 0 1-1 2 0 1 0 1 1 1 0 2-1 4-1 5 1-1 1-2 2-4z" class="J"></path><path d="M345 334v6l-1 1h0v1h-1v-5h1 0c0-1 0-2 1-3z" class="x"></path><path d="M349 323c1-5 1-11 3-15l1 1 1-1v1c0 1 0 1-1 1 1 1 1 1 1 2v1 4l-1 1 1 4-1 1c0 1-1 2-1 3l-1-1v-1l-1 1c-1-1-1-1-1-2z" class="a"></path><path d="M353 289c1-1 1-2 2-3h1v1h1v-1c1-1 6-3 7-3 2 0 3-1 4-1v1h-2c-2 1-6 2-7 3l-2 2c-2 1-2 0-3 1v2c-1 2-3 3-2 6h-1c-1 1-1 3-2 4-1 4-3 8-3 12-1 3-1 7-1 10 0 4-1 7 0 11-1 1-1 2-1 3h0-1v-6 3h-1v-5c0-3 0-6 1-9v-2 1h0c0 1 1 1 1 2v-5c0-4 2-7 2-11 1-2 2-4 2-6 1-4 3-7 5-10z" class="p"></path><path d="M343 320v-2 1h0c0 1 1 1 1 2l-1 10v3h-1v-5c0-3 0-6 1-9z" class="O"></path><path d="M362 300c1-1 2-1 3-2h2v1l-1 4h0c0 2 0 3 1 5l-1 1c0-1-1-1-1-1 0 1-1 3-1 4-1 1-2 1-2 2l-1 1h0l-1-1v1l-2 2h0-4v-4-1c0-1 0-1-1-2 1 0 1 0 1-1v-1l-1 1-1-1c0-1 1-3 2-4 0-1 0-1 1-1l1 1c1-1 2-1 3-2l1-1c1-1 1-1 2-1z" class="M"></path><path d="M363 302l2-1c1 1 1 1 1 2 0 2 0 3 1 5l-1 1c0-1-1-1-1-1l-2-1c1-1 0-3 0-5z" class="F"></path><path d="M362 300c1-1 2-1 3-2h2v1l-1 4h0c0-1 0-1-1-2l-2 1h-4l1-1c1-1 1-1 2-1z" class="h"></path><path d="M363 307l2 1c0 1-1 3-1 4-1 1-2 1-2 2l-1 1h0l-1-1v1c-2 0-3 0-4-1 1-2 5-5 7-7z" class="C"></path><path d="M344 187v2c1 0 1 1 2 0 1 1 1 1 2 0 1 1 1 2 1 3h1 0v1l1 1-1 1v1c1 0 1 0 2-1h2c1-1 3-3 4-3h2 0c1-1 1-1 2-1 0 0 1 0 1 1l2-1h0c0 1 0 1 1 2h1 0c1-2 2-1 3-1l2-1 2-2h0l1 1 1 1v1l1 1h0v2 1h-1c-1 1-2 2-3 4h0c-1 0-1 1-1 2v-1l-1 1c0-1 0-1-1 0l1 5 1-1 3 2h0c0 2 0 2 1 3l-2 3h0c-1 2-2 3-3 5 0 0-1 1-1 2-1 0-2 1-2 1h-2l-2-2c0-1 0-2-1-3l-1-1c-1-1-2-2-3-2h-1-1l-5 2-3 3-1 3v-4l-1 5c-1-1-1-1-1-2l-1 1h0l-1-2h0 0v-1l-1 2v-4h0c-1-1-1 0-1-1v-3c0-1-1-1-1-2v-2c-1-1-1-2-1-3l-1-1h1c0-2 0-3-1-4h1 0c1 0 2 1 3 0v-3l-2-3v-4h2v1 1l1-1v-1l1 1c1-1 1-1 1-2h-2v-2-1z" class="M"></path><path d="M374 189h0l1 1 1 1v1l1 1h0v2 1h-1c-1 1-2 2-3 4h0v-3h-1l1-1h0l-3-3h0-1v-1h1l2-1 2-2z" class="I"></path><path d="M374 194v-2h2l1 1h0v2 1h-1 0l-2-2z" class="E"></path><path d="M374 189h0l1 1 1 1v1h-2v2l-1-1h-3 0-1v-1h1l2-1 2-2z" class="G"></path><path d="M365 191h0c0 1 0 1 1 2h1 0c1-2 2-1 3-1h-1v1h1c-1 0-2 1-2 1-1 2-1 3 0 5h0l-3 3c-1 0-2 1-3 0s-1-1-1-3v-2h0c-2 1-2 2-3 3-1 0-1-1-2-2 2-1 3-2 5-3l-1-1 3-2 2-1z" class="I"></path><path d="M363 200c2-2 3-4 5-6-1 2-1 3 0 5h0-1c-1 0-2 0-2 1h-2z" class="L"></path><path d="M361 199h0c1 1 0 1 1 2 1 0 1-1 1-1h2c0-1 1-1 2-1h1l-3 3c-1 0-2 1-3 0s-1-1-1-3z" class="K"></path><path d="M361 195c1 0 2-1 3-1-1 2-2 3-3 5h0v-2h0c-2 1-2 2-3 3-1 0-1-1-2-2 2-1 3-2 5-3z" class="n"></path><path d="M358 200c1-1 1-2 3-3h0v2c0 2 0 2 1 3s2 0 3 0l1 1c0 1 0 2-1 3h-1l1 1c1-1 2-2 2-3 1-1 1-2 2-3l1 1 1 5c0 2-1 4-3 5h0c-2 0-5-1-7-1s-3-1-4-1v2h-1l-2-1 1-1v-2h0l2-2h1v-1l-1-2 1-3z" class="W"></path><path d="M371 207l1-1 3 2h0c0 2 0 2 1 3l-2 3h0c-1 2-2 3-3 5 0 0-1 1-1 2-1 0-2 1-2 1h-2l-2-2c0-1 0-2-1-3l-1-1c-1-1-2-2-3-2h-1-1l-5 2c-1 0-1 0-2-1 0-1 0-2 1-3 1 0 1 0 2-1v-1l1 1 2 1h1v-2c1 0 2 1 4 1s5 1 7 1h0c2-1 3-3 3-5z" class="I"></path><path d="M359 214c1-1 1-2 3-2v1h0-1v1c1 1 1 1 2 1-1 0-1 1-1 1-1-1-2-2-3-2z" class="F"></path><path d="M375 208c0 2 0 2 1 3l-2 3h0-1c-1 0-1 1-2 2h0c0-2 1-4 3-5l1-3z" class="O"></path><path d="M371 207l1-1 3 2h0l-1 3h-1s-1-1-2 0h0l-2 2-1-1c2-1 3-3 3-5z" class="C"></path><path d="M363 215c1-1 1-1 2-1s2 0 3 1h2v1 1h-1-1-1l-1 1h2v1c0 1 1 1 2 2-1 0-2 1-2 1h-2l-2-2c0-1 0-2-1-3l-1-1s0-1 1-1z" class="P"></path><path d="M366 218h2v1c0 1 1 1 2 2-1 0-2 1-2 1h-2 1c0-1 0-1-1-1h0v-3z" class="O"></path><path d="M344 187v2c1 0 1 1 2 0 1 1 1 1 2 0 1 1 1 2 1 3h1 0v1l1 1-1 1v1c1 0 1 0 2-1h2c1-1 3-3 4-3h2 0c1-1 1-1 2-1 0 0 1 0 1 1l-3 2 1 1c-2 1-3 2-5 3 1 1 1 2 2 2l-1 3 1 2v1h-1l-2 2h0v2l-1 1-1-1v1c-1 1-1 1-2 1-1 1-1 2-1 3 1 1 1 1 2 1l-3 3-1 3v-4l-1 5c-1-1-1-1-1-2l-1 1h0l-1-2h0 0v-1l-1 2v-4h0c-1-1-1 0-1-1v-3c0-1-1-1-1-2v-2c-1-1-1-2-1-3l-1-1h1c0-2 0-3-1-4h1 0c1 0 2 1 3 0v-3l-2-3v-4h2v1 1l1-1v-1l1 1c1-1 1-1 1-2h-2v-2-1z" class="d"></path><path d="M348 218c0-1 0-3 1-4 0-2 0-6 1-7h1c0 2-1 3 0 5-1 1-1 2-1 3 1 1 1 1 2 1l-3 3-1 3v-4z" class="g"></path><path d="M358 192h2 0c1-1 1-1 2-1 0 0 1 0 1 1l-3 2 1 1c-2 1-3 2-5 3l-4 4-1 4-1-2c1-2 3-4 5-6l-1-1c-2 1-4 6-5 9 0-5 2-8 5-11 1-1 3-3 4-3z" class="r"></path><path d="M352 202l4-4c1 1 1 2 2 2l-1 3 1 2v1h-1l-2 2h0v2l-1 1-1-1v1c-1 1-1 1-2 1-1-2 0-3 0-5v-1l1-4z" class="N"></path><path d="M352 202l4-4c1 1 1 2 2 2l-1 3-1 1c-1 0-1 0-1-1 0 0 0-1-1-1h-2z" class="d"></path><path d="M344 187v2c1 0 1 1 2 0 1 1 1 1 2 0 1 1 1 2 1 3h1 0v1c-1 1-2 3-3 4h0-1v1c1 0 1 0 1 1 0 3-2 7-1 11v1c-1 3-1 7 0 10l-1 1h0l-1-2h0 0v-1l-1 2v-4h0c-1-1-1 0-1-1v-3c0-1-1-1-1-2v-2c-1-1-1-2-1-3l-1-1h1c0-2 0-3-1-4h1 0c1 0 2 1 3 0v-3l-2-3v-4h2v1 1l1-1v-1l1 1c1-1 1-1 1-2h-2v-2-1z" class="b"></path><path d="M343 193l1-1v-1l1 1v3 2h-1 0c0-1-1-2-2-4h1z" class="F"></path><path d="M340 201c1 0 2 1 3 0l1 2h0v-2h1c0 3 0 7-1 10v9h0v-1l-1 2v-4h0c-1-1-1 0-1-1v-3c0-1-1-1-1-2v-2c-1-1-1-2-1-3l-1-1h1c0-2 0-3-1-4h1 0z" class="O"></path><path d="M343 215c1-3-1-7 1-9h0v5 9h0v-1l-1 2v-4h0v-2z" class="L"></path><path d="M339 201h1l1 1c0 1 0 2 1 3 0 1 0 3 1 4v6 2c-1-1-1 0-1-1v-3c0-1-1-1-1-2v-2c-1-1-1-2-1-3l-1-1h1c0-2 0-3-1-4z" class="Q"></path><path d="M377 148h0c1 1 2 1 3 2 1 0 2 1 4 2l1-1 1 1h0 1c-1-1-1-1-1-2l1-1 3 3h2c1 3 2 6 2 10 0 3-1 6 0 9 0 1 0 2-1 3 1 5 0 8-1 12l-2 2c-1 1-1 1-2 1 0 1 0 0-1 0 0 0 0-1-1-1l-1-1v1h-2s0 1-1 1v1h0 1l1 2h-1v1h-1-5l-1-1v-1l-1-1-1-1h0l-2 2-2 1c-1 0-2-1-3 1h0-1c-1-1-1-1-1-2h0 0c-1-1-1-1-1-2h-1 1 3l1-4c0-2 1-4 1-5h1c1-1 1 0 2 0v-3-2l1-1c0-1 0-1 1-2 0-2-1-3 0-5 0-3-2-6-4-7l1-1h1v-1h0v-2l1-1c1-1 1-1 1-2l-1-1s-1-1 0-1h1l1-2h1l1-1z" class="M"></path><path d="M384 162c2 1 3 2 4 3 1 2 0 3-1 5l-1 1h-1s-1-1-2-1h0l1-2c-1 0-1-1-2-1 1-1 2-2 2-3v-2z" class="h"></path><path d="M384 168l1-1c0 1 0 1 1 2 0 0-1 1 0 2h-1s-1-1-2-1h0l1-2z" class="l"></path><path d="M377 159c1 1 1 1 2 1 0 1-1 1-1 2 0 0 1 3 1 4h1v-1c0-1 0-1 1-2v-2h1c1 0 1 1 2 1h0v2c0 1-1 2-2 3-1 0-1 1-2 1h-1c-2-2-4-6-4-9h2z" class="H"></path><path d="M372 156l1-1 1 2h1l-1 1 1 1c0 3 2 7 4 9h1c1 0 1-1 2-1s1 1 2 1l-1 2c-1 0-1 1-2 1-2 1-3 5-5 7-1-2 1-3 2-6h-1c0 1-1 4-2 5-1-1 0-2 0-3h-2c0-1 0-1 1-2 0-2-1-3 0-5 0-3-2-6-4-7l1-1h1v-1h0v-2z" class="I"></path><path d="M374 167c1 2 1 5 1 7h-2c0-1 0-1 1-2 0-2-1-3 0-5z" class="L"></path><path d="M375 174c0 1-1 2 0 3 1-1 2-4 2-5h1c-1 3-3 4-2 6 0 1-1 2-1 2v1l-1 2c-1 1-1 1-1 3l-1 1c1 0 1 1 2 2l-2 2-2 1c-1 0-2-1-3 1h0-1c-1-1-1-1-1-2h0 0c-1-1-1-1-1-2h-1 1 3l1-4c0-2 1-4 1-5h1c1-1 1 0 2 0v-3-2l1-1h2z" class="O"></path><path d="M372 175c1 1 1 2 1 4v1h-1v-3-2z" class="o"></path><path d="M372 187c1 0 1 1 2 2l-2 2-1-1c0-1 0-2 1-3z" class="N"></path><path d="M370 180v5s0 1-1 2l-1-2c0-2 1-4 1-5h1z" class="k"></path><path d="M364 189h3l1-4 1 2-1 4h1s0-1 1-1c0-1 0-2 1-2v2l1 1-2 1c-1 0-2-1-3 1h0-1c-1-1-1-1-1-2h0 0c-1-1-1-1-1-2h-1 1z" class="r"></path><path d="M387 170h1c1-1 1-3 1-4 0-2-1-2-2-3h0s1-1 2-1c0 1 1 1 1 1h1c1 4 1 8-1 12 0 1-1 2-1 2-1 1-2 1-3 2h-1c-2 1-4 1-5 3h0 0 0l-1 1h-1l-1-3v1c-1 0-1 0-1 1l-2 1 1-2v-1s1-1 1-2c2-2 3-6 5-7 1 0 1-1 2-1h0c1 0 2 1 2 1h1l1-1z" class="W"></path><path d="M376 178c2-2 3-6 5-7 0 2-1 3-2 5 0 0-1 0-1 1v1c0 1-1 2-1 2v1c-1 0-1 0-1 1l-2 1 1-2v-1s1-1 1-2z" class="d"></path><path d="M377 148h0c1 1 2 1 3 2 1 0 2 1 4 2l1-1 1 1h0 1c-1-1-1-1-1-2l1-1 3 3h2c1 3 2 6 2 10 0 3-1 6 0 9 0 1 0 2-1 3 1 5 0 8-1 12l-2 2c-1 1-1 1-2 1 0 1 0 0-1 0 0 0 0-1-1-1l-1-1v1h-2s0 1-1 1v1h0 1l1 2h-1v1h-1-5l-1-1v-1l-1-1-1-1h0c-1-1-1-2-2-2l1-1c0-2 0-2 1-3l2-1c0-1 0-1 1-1v-1l1 3h1l1-1h0 0 0c1-2 3-2 5-3h1c1-1 2-1 3-2 0 0 1-1 1-2 2-4 2-8 1-12-1-1-2-3-3-4-2-2-5-1-8-1h-1v2c-1 0-1 0-2-1h-2l-1-1 1-1h-1l-1-2c1-1 1-1 1-2l-1-1s-1-1 0-1h1l1-2h1l1-1z" class="D"></path><path d="M384 152c1 1 3 3 4 5-1-1-2-1-3-1h-1-2v-1l1-1 1-2z" class="F"></path><path d="M380 182h0c1-2 3-2 5-3h1c1-1 2-1 3-2l-3 4-6 1z" class="U"></path><path d="M385 151l1 1h0 1l4 6h0-1-1l-1-1c-1-2-3-4-4-5h0l1-1z" class="q"></path><path d="M389 158h1 1 0c2 1 2 2 2 4h0 1 0c0 3-1 6 0 9 0 1 0 2-1 3v-4h-1v-4l-1-3c0-1-1-3-2-5z" class="H"></path><path d="M387 152c-1-1-1-1-1-2l1-1 3 3h2c1 3 2 6 2 10h0-1 0c0-2 0-3-2-4l-4-6z" class="W"></path><path d="M392 170h1v4c1 5 0 8-1 12l-2 2c-1 1-1 1-2 1 0 1 0 0-1 0 0 0 0-1-1-1l-1-1h0v-1h2s1-1 2-1c3-3 3-11 3-15z" class="T"></path><path d="M376 149c1 1 2 1 3 2v1c-1 0-2 0-2 1h2l1 2-1 2 1 1h-1v2c-1 0-1 0-2-1h-2l-1-1 1-1h-1l-1-2c1-1 1-1 1-2l-1-1s-1-1 0-1h1l1-2h1z" class="a"></path><path d="M380 155l-1 2 1 1h-1v2c-1 0-1 0-2-1h-2l-1-1 1-1v1c2-1 3-2 5-3z" class="m"></path><path d="M375 157v1h3c0 1 0 1-1 1h-2l-1-1 1-1z" class="v"></path><path d="M374 183l2-1c0-1 0-1 1-1v-1l1 3h1l1-1h0l1 1 6 3h-2v1h0v1h-2s0 1-1 1v1h0 1l1 2h-1v1h-1-5l-1-1v-1l-1-1-1-1h0c-1-1-1-2-2-2l1-1c0-2 0-2 1-3z" class="a"></path><path d="M376 191c1 0 3 0 4 1 1 0 2 0 2 1h-5l-1-1v-1z" class="K"></path><path d="M377 186v1h2c1 2 2 2 3 3h0c-1 0-3 0-4-1s-2-1-2-2l1-1z" class="k"></path><path d="M377 185c3 0 5 1 8 2h0v1h-2s0 1-1 1v1c-1-1-2-1-3-3h-2v-1-1z" class="R"></path><path d="M378 183h1l1-1h0l1 1 6 3h-2v1c-3-1-5-2-8-2v-1l1-1z" class="L"></path><path d="M349 219c2 1 2 0 3-1v2c0 1 0 1 1 0h1 1c-1 1-1 1-1 2h-1v-1c-1 1-1 1-1 2 0 2 1 4 2 6l1 1h1 0c2 2 5 2 7 3l-1 1c0 1 0 2-1 3 1 0 2 0 2 1 0 0-1 0-2 1v1 1h0c-1 1-3 2-3 3-3 1-4 3-6 5-1 1-2 1-2 3h0 1 3c-1 1-2 1-2 3l2-2c1-1 2-1 3-2h0 4l1 1h1l1 2v4c-1 1-1 3-1 4h1-1l-1 2h1v2c-2 0-4 0-5 1l-1 1-1-1h-2l-1-1c-1 0-1-1-2-1l-1 2-1 2-1 4h-2 0c0-2 0-3-1-5v-1c-1 1-2 1-2 1-1 1 0 1-1 1s0 0-1-1c-1 0-1-1-2-2l1-1v-1c1-1 0-3 1-4 0-1 1-1 1-2v-1l-1-5v-3h0c-2-2-2-4-2-6h-2l-1 1-4-3-8-3 1-1c3 0 5-1 7-2l2-2 1 1v-1h1c1 1 1 1 2 1v-1h1 1l1-1v-3h1c0 1 0 1 1 1l2 1h0v-1c2-3 1-5 2-7l1-5v4l1-3z" class="H"></path><path d="M357 251v3h-2c-1 0-1 0-1-1 1-1 2-1 3-2z" class="M"></path><path d="M345 267v-1c1-2 1-3 1-5l1 1 1-1 1 1h0v1h-1c0 1 0 1 1 1v1c1-1 1-1 1-2v-1l1-1 1 1-1 3-1 2-1 2-1 4h-2 0c0-2 0-3-1-5v-1z" class="d"></path><path d="M347 267l1-1h2v1l-1 2-2-2z" class="a"></path><path d="M345 268l1-1h1l2 2-1 4h-2 0c0-2 0-3-1-5z" class="J"></path><path d="M358 259h2l3 3h1-1l-1 2h1v2c-2 0-4 0-5 1l-1 1-1-1h-2l-1-1c-1 0-1-1-2-1l1-3 2-2c1 0 3-1 4-1z" class="V"></path><path d="M354 265c1 0 1-1 2-2l1 2c0 1 0 1-1 2h-2l-1-1 1-1z" class="K"></path><path d="M358 259h2v2c-2 0-3 0-5 1h0l-1-2c1 0 3-1 4-1z" class="C"></path><path d="M352 262l2-2 1 2c0 1-1 2-1 2v1l-1 1c-1 0-1-1-2-1l1-3z" class="G"></path><path d="M360 259l3 3h1-1l-1 2h1v2c-2 0-4 0-5 1l-1 1-1-1c1-1 1-1 1-2h1c1-1 1-3 2-4v-2z" class="F"></path><path d="M357 251h0 4l1 1h1l1 2v4c-1 1-1 3-1 4l-3-3h-2-1-1-1c-1 0-2 0-3 1h0l-1 1h-1c0-1 1-2 1-3 1 0 2-1 3-2h1c1 0 2 0 3-1l-1-1v-3z" class="W"></path><path d="M357 251h4v3c0-1-1-1-1-1-1-1-2-2-3-2z" class="N"></path><path d="M361 251l1 1h1l1 2h-3v-3z" class="T"></path><path d="M357 235l4 2h0c1 0 2 0 2 1 0 0-1 0-2 1-5 2-12 9-14 14v1h-1v-1h-1v-1c2-1 2-4 4-6 1 0 1 0 1-1v-1l-1-2v-1c1-2 2-2 3-2l2-1h2c0-1 0-2 1-3z" class="Y"></path><path d="M354 238h2l1 1c0 1-1 1-3 2 0-1 0-1-2-2l2-1z" class="H"></path><path d="M357 235l4 2-1 1c-1 1-2 1-3 1l-1-1c0-1 0-2 1-3z" class="E"></path><path d="M352 239c2 1 2 1 2 2-1 1-2 3-4 3l-1-2v-1c1-2 2-2 3-2z" class="U"></path><path d="M341 249c0-1 0-2 1-2s2 3 3 4v1 1h1v1h1 0c0 1 1 1 1 1 0 1 0 1-1 2 0 1 0 2-1 4 0 2 0 3-1 5v1c-1 1-2 1-2 1-1 1 0 1-1 1s0 0-1-1c-1 0-1-1-2-2l1-1v-1c1-1 0-3 1-4 0-1 1-1 1-2v-1l-1-5v-3h0z" class="J"></path><path d="M342 257v1h2v1h0l1 1-1 5h-1v-2c0-2-1-3-1-5v-1z" class="I"></path><path d="M342 258c0 2 1 3 1 5s0 4-1 5h-1c-1 0-1-1-2-2l1-1v-1c1-1 0-3 1-4 0-1 1-1 1-2z" class="N"></path><path d="M341 249c0-1 0-2 1-2s2 3 3 4v1 1h1v1l-1 6-1-1h0v-1h-2v-1l-1-5v-3h0z" class="F"></path><path d="M339 239l-1-1v-1c1-1 3-2 4-2 3 0 5 0 7 1v1 2 3l1 2v1c0 1 0 1-1 1-2 2-2 5-4 6v-1c-1-1-2-4-3-4s-1 1-1 2c-2-2-2-4-2-6l2-1v-2c0-1-1-1-2-1z" class="T"></path><path d="M343 238c2 0 3-1 4 0-1 1-1 3-1 4h0v-4h-1l-1 1c1 0 1 1 1 1v3 1l-2-2v-4z" class="b"></path><path d="M339 239c1 0 3-1 4-1v4l2 2-1 1c-1 0-1 1 0 1 0 2 0 3 1 5-1-1-2-4-3-4s-1 1-1 2c-2-2-2-4-2-6l2-1v-2c0-1-1-1-2-1z" class="n"></path><path d="M339 239c1 0 3-1 4-1v4c0 1-1 3-3 4 0-2 0-2 1-4v-2c0-1-1-1-2-1z" class="K"></path><path d="M339 239l-1-1v-1c1-1 3-2 4-2 3 0 5 0 7 1v1 2 3l1 2v1c0 1 0 1-1 1l-3-4c0-1 0-3 1-4-1-1-2 0-4 0-1 0-3 1-4 1z" class="m"></path><path d="M341 232v-3h1c0 1 0 1 1 1l2 1h0l1-1h2c0 1 1 2 1 3v1 2h0c-2-1-4-1-7-1-1 0-3 1-4 2v1l1 1c1 0 2 0 2 1v2l-2 1h-2l-1 1-4-3-8-3 1-1c3 0 5-1 7-2l2-2 1 1v-1h1c1 1 1 1 2 1v-1h1 1l1-1z" class="H"></path><path d="M332 241c0-1 1-1 1-2v1c2 1 3 1 4 1l1 1c0 1 0 1-1 1l-1 1-4-3z" class="I"></path><path d="M341 232v-3h1c0 1 0 1 1 1l2 1h0l1-1h2c0 1 1 2 1 3v1c-2 0-3-1-4-1h-3l-1-1z" class="W"></path><path d="M334 233l1 1v-1h1c1 1 1 1 2 1-2 2-3 4-5 5h0c0 1-1 1-1 2l-8-3 1-1c3 0 5-1 7-2l2-2z" class="F"></path><path d="M335 234v-1h1c1 1 1 1 2 1-2 2-3 4-5 5 0-1-1-1-1-2 0 0 1-1 1-2h0c1-1 1-1 2-1z" class="G"></path><path d="M349 219c2 1 2 0 3-1v2c0 1 0 1 1 0h1 1c-1 1-1 1-1 2h-1v-1c-1 1-1 1-1 2 0 2 1 4 2 6l1 1h1 0c2 2 5 2 7 3l-1 1c0 1 0 2-1 3h0l-4-2c-1 1-1 2-1 3h-2l-2 1c-1 0-2 0-3 2v1-3-2-1h0v-2-1c0-1-1-2-1-3h-2l-1 1v-1c2-3 1-5 2-7l1-5v4l1-3z" class="J"></path><path d="M350 234v-3c-1-2-1-5-1-8v1c1 2 2 4 4 7h0c-1 0-1 1-2 1v1l-1 1z" class="I"></path><path d="M347 223l1-5v4s1 1 0 1v4c-1 1-1 2-2 3l-1 1v-1c2-3 1-5 2-7z" class="r"></path><path d="M350 234l1-1v-1c1 0 1-1 2-1v1c1 1 2 2 4 3-1 1-1 2-1 3h-2c-1 0-2-1-3-2 0 0 0-2-1-2z" class="D"></path><path d="M350 234l1-1v-1c1 0 1-1 2-1v1c0 1-1 3-1 4h-1s0-2-1-2z" class="C"></path><path d="M353 231c0-2 0-2-1-3l-1-1c1 1 2 2 3 2l1 1h1 0c2 2 5 2 7 3l-1 1c0 1 0 2-1 3h0l-4-2c-2-1-3-2-4-3v-1h0z" class="T"></path><path d="M294 449v-1l3 9 2 4 5 5v1c1 1 5 4 5 5l2 3c1 0 2 0 3-1 1 2 2 4 2 6l2 5v1 4l1-1c0 2 0 4-1 6l-2 4c0 2-1 4-2 5 0 0-1 0-1-1-1 1-2 2-2 4h0-1c0 1 0 1-1 2v-1c-1 2-1 3-2 5-1 1-1 1-3 2h0c-1 1-2 2-3 2-2 0-4-1-5-3 0-1-1-1-1-1 0-1-1-2-1-3h0v-61z" class="D"></path><path d="M297 457l2 4v2l-1-1h-1 0-1v-1c0-1 0-3 1-4z" class="S"></path><path d="M296 480l2-1 1 1 2 1v2l1-1v3h-1v-2c-2-1-4 1-6 0v-2l1-1z" class="B"></path><path d="M298 492c-1-1-1-1-2-1v1s0 1 1 1l-2 2v-2-5c1-2 2-2 3-2l-1 1 1 1h1c0 1 1 1 2 1v1h-2c-1 1-1 1-1 2z" class="M"></path><path d="M299 493c0 1 1 2 2 2-2 2-3 3-3 5l-1 2-3-1c0-1 1-2 1-3v-1c2-1 3-3 4-4z" class="O"></path><path d="M295 498l1-1v1c0 1 1 1 2 2l-1 2-3-1c0-1 1-2 1-3z" class="N"></path><path d="M299 461l5 5h-2c0 1 0 1-1 1-2 1-4 2-6 4h0v1-5c0-1 1-1 1-1 0-2 0-2 1-4h0 1l1 1v-2z" class="T"></path><path d="M299 461l5 5h-2c0 1 0 1-1 1h0l-4-5h1l1 1v-2z" class="F"></path><path d="M301 481v-1l-1-1c0-1 1-2 2-3 1 0 1 0 2 1h1c1 0 1 0 2-1h1c0 1 1 3 2 4h0v2 1l-1-1h-2c-2 0-1 0-2-1h-2v1h-1l-1 1v-2z" class="c"></path><path d="M304 477h1l2 2c-1 1-2 1-3 2l-1-1c0-1 1-1 1-2 0 0-1 0 0-1z" class="B"></path><path d="M307 476h1c0 1 1 3 2 4h0v2 1l-1-1v-1c-1-1-2-1-2-2l-2-2c1 0 1 0 2-1z" class="H"></path><path d="M304 466v1c1 1 5 4 5 5-1 2-1 3-1 4h-1c-1 1-1 1-2 1h-1c-1-1-1-1-2-1-1 1-2 2-2 3l1 1v1l-2-1-1-1-2 1h0c-1-2-1-6-1-8v-1h0c2-2 4-3 6-4 1 0 1 0 1-1h2z" class="M"></path><path d="M304 467c1 1 5 4 5 5-1 2-1 3-1 4h-1v-1c-1-2-2-4-4-6h0l1-2z" class="F"></path><path d="M304 466v1l-1 2h0c0 1 0 1-2 2-1 0-3 0-5 1l-1-1h0c2-2 4-3 6-4 1 0 1 0 1-1h2z" class="S"></path><path d="M298 486c4-1 6-1 9 0 1 1 3 1 4 2 0 0 1 0 1 1 2 1 2 2 3 3l-2 1v2 1c-1 1-1 0-2 0h-1c-1 1-2 1-3 3 0 0-1 0-1 1l-1-2s-1-1-2-1h-1l-1-1v-1c-1 0-2-1-2-2l-1-1c0-1 0-1 1-2h2v-1c-1 0-2 0-2-1h-1l-1-1 1-1z" class="u"></path><path d="M305 498h0 1 1c0-1 0-2 1-3h1c0 1 0 1 1 1h0c-1 1-2 1-3 3 0 0-1 0-1 1l-1-2zm7-9c2 1 2 2 3 3l-2 1v2-2l-1-1v-3z" class="M"></path><path d="M309 472l2 3c1 0 2 0 3-1 1 2 2 4 2 6l2 5v1 4l1-1c0 2 0 4-1 6l-2 4c0 2-1 4-2 5 0 0-1 0-1-1-1 1-2 2-2 4h0-1c0-1 1-1 1-2 1-3 1-5 0-8v-1c1 0 1 1 2 0v-1-2l2-1c-1-1-1-2-3-3 0-1-1-1-1-1l1-2h-2 0v-1c0-1 0-1-1-1l1-1v-1-2h0c-1-1-2-3-2-4s0-2 1-4z" class="n"></path><path d="M318 485v1 4l1-1c0 2 0 4-1 6l-2 4c-1-2 0-4 1-6h0c1-3 1-5 1-8z" class="X"></path><path d="M312 479l1 1c2 2 4 5 3 8 0-1-1-2-2-2 0-1-1-3-1-4-1-1-1-2-1-3z" class="h"></path><path d="M310 480v-2c1 1 1 1 2 1 0 1 0 2 1 3 0 1 1 3 1 4h-1l-1-1v1h-2 0v-1c0-1 0-1-1-1l1-1v-1-2z" class="e"></path><path d="M309 472l2 3 2 5-1-1c-1 0-1 0-2-1v2h0c-1-1-2-3-2-4s0-2 1-4z" class="P"></path><path d="M312 486v-1l1 1h1c1 0 2 1 2 2 1 1 1 3 1 5-1 2-2 4-1 6 0 2-1 4-2 5 0 0-1 0-1-1-1 1-2 2-2 4h0-1c0-1 1-1 1-2 1-3 1-5 0-8v-1c1 0 1 1 2 0v-1-2l2-1c-1-1-1-2-3-3 0-1-1-1-1-1l1-2z" class="I"></path><path d="M314 486c1 0 2 1 2 2v2h-1l-1-2c-1-1-1-1-1-2h1z" class="i"></path><path d="M313 495v-2l2-1c0 2-1 4-1 6 0 1-2 4-1 5-1 1-2 2-2 4h0-1c0-1 1-1 1-2 1-3 1-5 0-8v-1c1 0 1 1 2 0v-1z" class="H"></path><path d="M301 495v1l1 1h1c1 0 2 1 2 1l1 2c0-1 1-1 1-1 1-2 2-2 3-3h1v1c1 3 1 5 0 8 0 1-1 1-1 2s0 1-1 2v-1c-1 2-1 3-2 5-1 1-1 1-3 2h0c-1 1-2 2-3 2-2 0-4-1-5-3 0-1-1-1-1-1 0-1-1-2-1-3h0v-3h1c0-2 0-4-1-6l3 1 1-2c0-2 1-3 3-5z" class="M"></path><path d="M300 511c1-1 1-2 1-2v-1s1-1 2-1c1 1 2 2 2 3h1c0 2 0 2 1 3-1 1-1 1-3 2h0c0-1-1-1-1-1l-1-1-2-2z" class="J"></path><path d="M305 510h1c0 2 0 2 1 3-1 1-1 1-3 2h0c0-1-1-1-1-1l2-2v-2z" class="d"></path><path d="M294 501l3 1c0 1-1 3 0 4 0 3 0 4 2 6h1v-1h0l2 2 1 1s1 0 1 1h0 0c-1 1-2 2-3 2-2 0-4-1-5-3 0-1-1-1-1-1 0-1-1-2-1-3h0v-3h1c0-2 0-4-1-6z" class="a"></path><path d="M294 510v-3h1c0 3 1 5 3 7 1 1 3 1 4 1h2 0 0c-1 1-2 2-3 2-2 0-4-1-5-3 0-1-1-1-1-1 0-1-1-2-1-3h0z" class="c"></path><path d="M301 495v1l1 1h1c1 0 2 1 2 1l1 2c0-1 1-1 1-1 1-2 2-2 3-3h1v1c1 3 1 5 0 8 0 1-1 1-1 2s0 1-1 2v-1c-1-1-1-2-2-3h-1v-1c-1-1-3-2-5-2s-3 3-4 4c-1-1 0-3 0-4l1-2c0-2 1-3 3-5z" class="P"></path><path d="M310 496h1v1c1 3 1 5 0 8 0-1 0 0-1-1h0-1c-1-1-1-1-1-2l-2-2c0-1 1-1 1-1 1-2 2-2 3-3z" class="C"></path><path d="M310 496h1v1l-1 1c0 1 0 2-2 3v1l-2-2c0-1 1-1 1-1 1-2 2-2 3-3z" class="D"></path><path d="M369 145l3 3 1 1h2 0l-1 2h-1c-1 0 0 1 0 1l1 1c0 1 0 1-1 2l-1 1v2h0v1h-1l-1 1c2 1 4 4 4 7-1 2 0 3 0 5-1 1-1 1-1 2l-1 1v2 3c-1 0-1-1-2 0h-1c0 1-1 3-1 5l-1 4h-3-1 1c0 1 0 1 1 2h0l-2 1c0-1-1-1-1-1-1 0-1 0-2 1h0-2c-1 0-3 2-4 3h-2c-1 1-1 1-2 1v-1l1-1-1-1v-1h0-1c0-1 0-2-1-3-1 1-1 1-2 0-1 1-1 0-2 0v-2 1h-1v-8-1-8-5c-1-2-1-2-3-3 1-1 1-1 0-2h-2v-2-1h-2v-1l2-2 5-3c1 0 1-1 1-1-2-2-2-2-3-2h10 2c0 2 0 4 1 6h1c0-1 0-1 1-2h0c1 1 2 1 3 2h0 2c1 0 1-1 2-2 0 0-1 0-1-1h-2c0-1-1-1-2-1h1c0-1-1-2-1-3h1c1-1 2-1 4-1 1 0 4 1 5 1 1-1 1-2 1-3z" class="a"></path><path d="M366 167c1-1 0-3 0-4l3 1 2 1h0 0c2 2 2 3 2 5 0 1 0 2 1 2-1 1-1 1-1 2l-1 1v2 3c-1 0-1-1-2 0h-1c0-1 0-1-1-1 0-1 0-1-1-2-2 0-2-1-3-2l3-3-1-1v-1-1-2h0z" class="M"></path><path d="M366 167c1-1 0-3 0-4l3 1c1 2 2 3 2 6-1 1-3 2-4 2l-1-1v-1-1-2h0z" class="m"></path><path d="M366 167l3 1h0c0 1 0 1-1 2h-2v-1-2z" class="C"></path><path d="M369 145l3 3 1 1h2 0l-1 2h-1c-1 0 0 1 0 1l1 1c0 1 0 1-1 2l-1 1v2h0v1h-1l-1 1c-1-2-3-4-5-6-1-1-3-2-4-3h0l-2-2v-1c1-1 2-1 4-1 1 0 4 1 5 1 1-1 1-2 1-3z" class="Y"></path><path d="M359 148c1-1 2-1 4-1 0 1 0 2 1 2l1 1 1 1-1 1c-1-1-3-1-4-1l-2-2v-1z" class="E"></path><path d="M369 145l3 3 1 1h2 0l-1 2h-1c-1 0 0 1 0 1l1 1c0 1 0 1-1 2l-1 1-2-2c0-1-1-2-2-2 0 0-1 0-1-1h-1l-1-1-1-1c-1 0-1-1-1-2 1 0 4 1 5 1 1-1 1-2 1-3z" class="c"></path><path d="M359 155h0c1 1 1 1 2 1 0 1 1 1 1 2s0 3-1 5v2l-1 1v2h2c1-1 1 0 1-1-1 0-1 0-1-1h1l1 1h0 2 0v2 1 1l1 1-3 3c1 1 1 2 3 2l-1 1-2-1h0l-1-1h-2v1h-1v2c-1 1-3 2-5 2v1h-1v-1-3c0-1 1-2 1-2-1-2-1-2-2-2v-1h1c1-2 1-3 1-5h0c2 0 2-1 2-2v1h1l1-1c1-1 1-2 1-2 0-2-1-1-2-2l1-1h0v-1c1-1 0-1 0-2v-1c0-1 0 0-1-1 0 0 0-1 1-1z" class="H"></path><path d="M364 167h2 0v2 1 1l1 1-3 3c1 1 1 2 3 2l-1 1-2-1h0l-1-1h-2v1h-1c0-2-3-3-5-5l3-3h2s0 1 1 1h1c0-1 1-1 2-2 0 0-1-1 0-1z" class="F"></path><path d="M361 177v-1h2l1 1h0l2 1 1-1c1 1 1 1 1 2 1 0 1 0 1 1s-1 3-1 5l-1 4h-3-1 1c0 1 0 1 1 2h0l-2 1c0-1-1-1-1-1-1 0-1 0-2 1h0-2c-1 0-3 2-4 3h-2c-1 1-1 1-2 1v-1l1-1-1-1v-1h0c0-1 1-2 2-3l1-2h0c0-1 0-3 1-3v-2h1v-1c2 0 4-1 5-2v-2h1z" class="L"></path><path d="M359 183l3-1-1 2c-1 1-1 2-2 4h-1c0-1 0-3 1-4v-1z" class="k"></path><path d="M363 189c0-1-1-1-1-1 0-2 0-2 1-3h2v3l-1 1h-1z" class="T"></path><path d="M358 191l1-2 1 1c1 0 3-1 4-1 0 1 0 1 1 2h0l-2 1c0-1-1-1-1-1-1 0-1 0-2 1h0-2v-1z" class="g"></path><path d="M366 178l1 1c1 1 1 3 0 5h0c-2 0-2-1-3-1h0l-2 2-1-1 1-2h1c1-1 1-1 1-2v-1h2v-1z" class="d"></path><path d="M361 177v-1h2l1 1h0l2 1h0v1h-2v1c0 1 0 1-1 2h-1l-3 1c-1-1-3-1-4-1v-1c2 0 4-1 5-2v-2h1z" class="I"></path><path d="M361 177v-1h2l1 1h0c-1 1-2 2-3 2h0v-2z" class="C"></path><path d="M355 182c1 0 3 0 4 1v1c-1 1-1 3-1 4v1h-1v2h1v1c-1 0-3 2-4 3h-2c-1 1-1 1-2 1v-1l1-1-1-1v-1h0c0-1 1-2 2-3l1-2h0c0-1 0-3 1-3v-2h1z" class="N"></path><path d="M352 189l1 2-2 2-1-1h0c0-1 1-2 2-3z" class="O"></path><path d="M351 149h2c0 2 0 4 1 6h1c0-1 0-1 1-2h0c1 1 2 1 3 2-1 0-1 1-1 1 1 1 1 0 1 1v1c0 1 1 1 0 2v1h0l-1 1c1 1 2 0 2 2 0 0 0 1-1 2l-1 1h-1v-1c0 1 0 2-2 2h0c0 2 0 3-1 5h-1v1c1 0 1 0 2 2 0 0-1 1-1 2v3 1 2c-1 0-1 2-1 3h0l-1 2c-1 1-2 2-2 3h-1c0-1 0-2-1-3-1 1-1 1-2 0-1 1-1 0-2 0v-2 1h-1v-8-1-8-5c-1-2-1-2-3-3 1-1 1-1 0-2h-2v-2-1h-2v-1l2-2 5-3c1 0 1-1 1-1-2-2-2-2-3-2h10z" class="M"></path><path d="M355 168v-2h2c0 1 0 2-2 2h0z" class="O"></path><path d="M349 175l2-1c0-1 1-1 2-1v1l-2 2c-1-1-2-1-2-1z" class="T"></path><path d="M354 160h-1c0-2 0-2 1-3 1 1 2 1 2 3 0 1-1 2-1 3h-1v-3z" class="I"></path><path d="M350 162c1 0 2 0 3-1-1 2-3 5-4 6h-1v-1l2-4z" class="Y"></path><path d="M341 149h10-1-4v1l-2 2v1h3 0l-1 1h0c-1 0-2 1-2 1h0c0-1 0-2-1-3 1 0 1-1 1-1-2-2-2-2-3-2z" class="H"></path><path d="M351 154l3 3c-1 1-1 1-1 3h1l-1 1c-1 1-2 1-3 1v-5c0-1 0-1 1-1v-2z" class="K"></path><path d="M350 157c0-1 0-1 1-1 0 1 0 2 1 3 0 0 0 1 1 2-1 1-2 1-3 1v-5z" class="v"></path><path d="M343 152c1 1 1 2 1 3h0s1-1 2-1h0l1-1c1 0 3 1 4 1v2c-1 0-1 0-1 1v5l-2 4v1h-1-1c1 1 1 1 1 2h1c2 1 2 0 4-1 1 1 1 2 1 2-1 1-2 1-3 1s-2 2-3 3l-1 1c0-2-1-3-2-5v-2-3l-1 1c-1-2-1-2-3-3 1-1 1-1 0-2h-2v-2-1h-2v-1l2-2 5-3z" class="a"></path><path d="M346 154h0l1-1c1 0 3 1 4 1v2c-1 0-1 0-1 1-2-1-3-2-4-3z" class="g"></path><path d="M343 160h1v1c0 1 0 1 1 2h-1v2l-1 1c-1-2-1-2-3-3 1-1 1-1 0-2 1 0 2-1 3-1z" class="d"></path><path d="M343 152c1 1 1 2 1 3h0v3l-1 1s1 0 1 1h-1c-1 0-2 1-3 1h-2v-2-1h-2v-1l2-2 5-3z" class="N"></path><path d="M343 152c1 1 1 2 1 3h0v3l-1 1c0-2 0-3-1-4h0c-2 1-3 2-4 3h-2v-1l2-2 5-3z" class="Y"></path><path d="M343 166l1-1v3 2c1 2 2 3 2 5s0 3 1 4h1v-3-1h1s1 0 2 1l2-2c1 0 1 0 2 2 0 0-1 1-1 2v3 1 2c-1 0-1 2-1 3h0l-1 2c-1 1-2 2-2 3h-1c0-1 0-2-1-3-1 1-1 1-2 0-1 1-1 0-2 0v-2 1h-1v-8-1-8-5z" class="Q"></path><path d="M345 187c0-1 0-1 1-1v-1l1-1v1c1 0 1 1 1 1h0v1h-1-2z" class="f"></path><path d="M345 187h2 1c1 1 1 2 0 2-1 1-1 1-2 0s-1-1-1-2z" class="J"></path><path d="M344 170c1 2 2 3 2 5s0 3 1 4h1v-3-1h1s1 0 2 1c-1 1-1 2-2 4 0 0 0 1-1 2h0c-1-1-1-2-2-3s0-4-1-6c0-1 0-2-1-3z" class="d"></path><path d="M350 184h1c0 1 0 1 1 1v1s0 1 1 1l-1 2c-1 1-2 2-2 3h-1c0-1 0-2-1-3 1 0 1-1 0-2v-1s0-1 1-2v1l1-1z" class="E"></path><path d="M343 166l1-1v3 6 1c0 1 0 1 1 2v5c-1 2-1 3-1 5v1h-1v-8-1-8-5z" class="l"></path><path d="M353 174c1 0 1 0 2 2 0 0-1 1-1 2v3 1 2c-1 0-1 2-1 3h0c-1 0-1-1-1-1v-1c-1 0-1 0-1-1h-1 0v-1h-1 0v-3c1-2 1-3 2-4l2-2z" class="I"></path><path d="M350 184v-1c0-1 0-2 1-3 0-1 0-1 1 0l2 1v1 2c-1 0-1 2-1 3h0c-1 0-1-1-1-1v-1c-1 0-1 0-1-1h-1 0z" class="C"></path><path d="M306 282l3-1h6v2c7 0 13 1 18 6l2 2c2 3 3 6 4 9-1 1-1 4 0 5 0 1 1 1 1 1h1v2c0 1 0 3 1 5 0 5-1 9-2 14v1c-1 1-1 1-1 2h-1-1c-1-3-2-5-4-8h1l-1-2-1-4v-1h-1c0 1 0 1-1 1v-2h1c0-3-3-5-5-7l-1-1h0c0-1 0-1-1-2h-1v3c-1 1-1 2-2 3-2 1-3 2-5 3l-1-1v-1h-1v1c0 1-1 1-2 2 0 0-1 0-1-1l-3-1-6-3h0l-5-3h0c-1-1-2-2-3-2v-1h-1v-8c1-3 1-6 1-8h1 0c3-2 7-5 11-5z" class="M"></path><path d="M300 297c-1-1-2-1-3-3h1l1-1 1 1 1 3h-1z" class="J"></path><path d="M299 288c1-1 3-2 5-3 0 2 1 3 0 4h-1c-1-1-3-1-4-1zm1 6h1c0-1-1-1 0-3 0 3 2 6 3 8l2 4h-1c0-1-1-2-1-2-1-1-2-1-3-1s-2-1-2-1c1 0 1-1 1-1v-1h1l-1-3z" class="d"></path><path d="M306 282l3-1h6v2c-1 2-1 5-1 6-1-1-2 0-3 0v-1h0v-1c0-1 1-2 1-4h1c-2 0-5 0-7 1l1-1h1c-1-1-1-1-2-1z" class="I"></path><path d="M295 287h0c3-2 7-5 11-5 1 0 1 0 2 1h-1l-1 1s-1 1-2 1c-2 1-4 2-5 3-3 2-4 4-5 7h-1c1-3 1-6 1-8h1z" class="D"></path><path d="M293 295h1v1c1 1 0 3 1 4h0 3l1-1s1 1 2 1 2 0 3 1c0 0 1 1 1 2l-1 1-2 2-1 2h0s0 1 1 1l-5-3h0c-1-1-2-2-3-2v-1h-1v-8z" class="V"></path><path d="M301 300c1 0 2 0 3 1 0 0 1 1 1 2l-1 1h0l-1-1c-1 0-1 0-2-1v-1-1z" class="e"></path><path d="M293 295h1v1c1 1 0 3 1 4h0 3c-1 1-1 1-1 3 0 1 1 2 2 3s2 1 3 0l-1 2h0s0 1 1 1l-5-3h0c-1-1-2-2-3-2v-1h-1v-8z" class="a"></path><path d="M301 291h0 1c1 0 1 1 2 2v-1l4-4c1 0 2 0 3 1 1 0 2-1 3 0 2 1 3 1 5 1s3 1 5 2l1 1 1 3h-1 0-2 0l-2-2h-2v1h-1-1c-2-1-3-3-6-3l-3 3h-1c-1-1-2-1-3 0 0 1 1 2 2 3l-2 1c-1-2-3-5-3-8z" class="D"></path><path d="M311 292c3 0 4 2 6 3h1c1 1 2 2 2 3 1 2 1 3 3 5v1 3c-1 1-1 2-2 3-2 1-3 2-5 3l-1-1v-1h-1v1c0 1-1 1-2 2 0 0-1 0-1-1l-3-1-6-3h0c-1 0-1-1-1-1h0l1-2 2-2 1-1h1l-2-4 2-1c-1-1-2-2-2-3 1-1 2-1 3 0h1l3-3z" class="W"></path><path d="M308 312v-4h0c2 1 2 3 3 5l-3-1z" class="O"></path><path d="M323 307c-1 1-1 2-2 3-1 0-1-1-1-1-1-1-1-1-1-2v-1l4 1z" class="H"></path><path d="M312 297l1 1c0 2 0 2-1 4-1 1-3 1-4 1h-1v-1l1-1 1-1c1 0 1 0 1-1l2-2z" class="D"></path><path d="M311 292c3 0 4 2 6 3h-1c-2-1-2-1-3-1-1 1-2 1-2 2l1 1h0l-2 2c0 1 0 1-1 1l-1-1v-4l3-3z" class="N"></path><path d="M308 295l3-3c-1 1-1 2-1 3v1 1h2 0l-2 2c0 1 0 1-1 1l-1-1v-4z" class="H"></path><path d="M304 295c1-1 2-1 3 0h1v4l1 1-1 1-1 1v1h1-2l-2-4 2-1c-1-1-2-2-2-3z" class="O"></path><path d="M306 298c0 1 0 2 1 3h1l-1 1v1h1-2l-2-4 2-1z" class="F"></path><path d="M304 295c1-1 2-1 3 0h1v4c-3-1-1-2-2-4h-2z" class="G"></path><path d="M315 283c7 0 13 1 18 6l2 2c2 3 3 6 4 9-1 1-1 4 0 5 0 1 1 1 1 1h1v2c0 1 0 3 1 5 0 5-1 9-2 14v1c-1 1-1 1-1 2h-1-1c-1-3-2-5-4-8h1l-1-2-1-4v-1h-1c0 1 0 1-1 1v-2h1c0-3-3-5-5-7l-1-1h0c0-1 0-1-1-2h-1v-1c-2-2-2-3-3-5 0-1-1-2-2-3h1v-1h2l2 2h0 2 0 1l-1-3-1-1c-2-1-3-2-5-2s-3 0-5-1c0-1 0-4 1-6z" class="W"></path><path d="M325 293l5 5-1 2c-1-1-1-1-3-1l1-2-1-1h-1 0 1l-1-3z" class="G"></path><path d="M318 295h1v-1h2l2 2h0 2 1l1 1-1 2c2 0 2 0 3 1l1-2c2 1 3 2 4 3 1 2 2 3 2 5h-1l-4-2h-1c-1-1-4-1-5 0v2h0c0-1 0-1-1-2h-1v-1c-2-2-2-3-3-5 0-1-1-2-2-3z" class="K"></path><path d="M321 294l2 2h0v1l1 3-1 1-1-2v-1c0-1-1-2-2-3h0l1-1z" class="X"></path><path d="M326 299c2 0 2 0 3 1s2 1 3 3h-1l-3-2-2-2z" class="O"></path><path d="M330 298c2 1 3 2 4 3l-1 2h-1c-1-2-2-2-3-3l1-2z" class="m"></path><path d="M325 296h1l1 1-1 2 2 2-1 1h-1l-2-2-1-3v-1h2z" class="E"></path><path d="M325 306v-2c1-1 4-1 5 0h1l4 2h1c2 2 2 4 3 6s1 3 2 5v1-2-2l1-1c0 5-1 9-2 14v1c-1 1-1 1-1 2h-1-1c-1-3-2-5-4-8h1l-1-2-1-4v-1h-1c0 1 0 1-1 1v-2h1c0-3-3-5-5-7l-1-1z" class="W"></path><path d="M332 310v-2h1v3l-1-1z" class="N"></path><path d="M334 314c0 2-1 4-1 6l-1-4v-1h1 0s1 0 1-1z" class="T"></path><path d="M336 319v-1c3 3 2 6 3 9h1v1c-1 1-1 1-1 2h-1-1c-1-3-2-5-4-8h1c1 1 3 5 4 6-1-3-1-7-2-9z" class="o"></path><path d="M326 307l3-2 1 1h0c0 2 0 3 2 4l1 1c0 1 1 2 1 3s-1 1-1 1h0-1v1-1h-1c0 1 0 1-1 1v-2h1c0-3-3-5-5-7z" class="H"></path><path d="M336 306c2 2 2 4 3 6s1 3 2 5v1-2-2l1-1c0 5-1 9-2 14h-1c-1-3 0-6-3-9v1l-1-1 1-1v1c1-1 1-2 1-3 1-2 1-3 0-4 0-2 0-3-2-5h1z" class="d"></path><path d="M572 130h3l1 1-1 1h0-1l1 1c2 0 3 0 5 1h0l2 2h2l3 6v1l1 2v-3h1v2h1l1-1v-1l1 6v2c-1 1-1 2-1 2v1c0 1-1 2-1 2l-3 8-1 1v-1c-1-1-1-2-2-3-1 1-3 4-4 5h-2l-6 1-5 2h-1c-7 1-15-1-20-4h0c-2 0-2-1-3-2h0 0v2l-1-2-2 1-1-4-2-3v-2h3l-1-2c-1 0-1-1-1-1v-1-1h-1l1-1 1-2s0-1 1-2h0 0l1-1h1 0c2 0 2 0 4-1l-1 1c2-1 2-2 2-3h3c1-2 1-3 2-5v4l1 1c1-1 1-1 3-1 0 1 0 2 1 3h0l-3 1v1h0c2 0 2 0 3 1 1 2 1 2 0 4-1 0-1 1-2 1v1c1 0 1 1 2 1 1-1 2-2 2-3 1-1 1-2 2-3h2c1-1 1-6 0-7v-2h0c0-1 0-2 1-3h1v-1c1 0 1 0 2 1l1-2c1-1 2-1 4-2h0z" class="U"></path><path d="M564 151c1 1 1 2 1 3l-3-2c1 0 2 0 2-1z" class="G"></path><path d="M574 147c1 0 1-1 1-1 1-1 1-2 3-2v2l-1 1c-1 0-1 1-1 2l-1 1-1-1 1-1-1-1zm-2 9c2 1 3 1 5 0v1c0 2-1 2-2 3h-1 0-2v-3-1z" class="d"></path><path d="M570 155l1 1-1 1c0 2 0 2-1 3h-1s-1-1-1-2c-1-1-1-1-1-2h1c1 0 2-1 3-1z" class="L"></path><path d="M580 155h1c-2 4-5 6-9 7-1 0-2 1-3 1 2-1 4-2 5-3h1c1-1 2-1 2-3v-1c1 0 1 0 3-1z" class="I"></path><path d="M565 155c1 0 1 0 1 1s0 1 1 2v2 1c0 1 0 2-1 3h-2v-1c1-2 0-4 0-6 1-1 1-1 1-2z" class="u"></path><path d="M560 151v2 1c0 2 1 4 2 5 2 1 2 2 3 3-1 0-2-1-3-2-2-1-5-3-5-5s2-3 3-4z" class="K"></path><path d="M572 153c2 0 3 1 5 1 0-1 1-1 1-1l2 2c-2 1-2 1-3 1-2 1-3 1-5 0h-1l-1-1-1-1h1 1s0-1 1-1h0z" class="C"></path><path d="M551 155v-1-1-2c1-2 1-2 3-2-1 1-1 2 0 3h1v1c0 3 2 5 3 9h-1v-1-1c0-1-1-2-2-3-1 0-2-1-3 0 0-1-1-1-1-1v-1z" class="x"></path><path d="M578 146c0 1 1 1 1 1 1 2 0 4-1 5v1h0s-1 0-1 1c-2 0-3-1-5-1l-2-1-2-3c1 0 2 1 3 1l1 1h2 2c-1-1 0-1-1-1l1-1c0-1 0-2 1-2l1-1z" class="a"></path><path d="M588 146c0 1 1 1 1 2-1 1-1 3-1 5-1 2-2 5-4 7-1 1-3 4-4 5h-2l-6 1h0c2 0 4-1 5-2l3-3c4-5 7-9 8-15z" class="I"></path><path d="M578 144h0c1 0 1-1 1-1 3 0 5 2 7 3-1 4-3 6-5 9h-1l-2-2h0v-1c1-1 2-3 1-5 0 0-1 0-1-1v-2z" class="G"></path><path d="M588 145v-3h1v2h1l1-1v-1l1 6v2c-1 1-1 2-1 2v1c0 1-1 2-1 2l-3 8-1 1v-1c-1-1-1-2-2-3 2-2 3-5 4-7 0-2 0-4 1-5 0-1-1-1-1-2v-1z" class="C"></path><path d="M591 142l1 6v2c-1 1-1 2-1 2v1c0-3-1-6-1-9h-1 1l1-1v-1z" class="b"></path><path d="M584 160c2-2 3-5 4-7 0-2 0-4 1-5 1 3-1 8-2 11 0 2-1 3-1 4-1-1-1-2-2-3z" class="H"></path><path d="M565 134v-1c1 0 1 0 2 1-1 3-2 5-2 9h0c0 1 0 2 1 2 0 2 1 3 2 4l2 3 2 1h0c-1 0-1 1-1 1h-1-1l1 1c-1 0-2 1-3 1h-1c0-1 0-1-1-1h0v-1c0-1 0-2-1-3 0 1-1 1-2 1 0 1-1 1-2 1v-2h0l-1-2c1-1 1-2 2-3h2c1-1 1-6 0-7v-2h0c0-1 0-2 1-3h1z" class="g"></path><path d="M564 134h1c0 1 0 2-2 3h0 0c0-1 0-2 1-3z" class="R"></path><path d="M561 146h2l-2 4-1 1-1-2c1-1 1-2 2-3z" class="J"></path><path d="M565 150c1 1 2 2 2 3s-1 1-2 2v-1c0-1 0-2-1-3l1-1z" class="C"></path><path d="M561 150c1 0 2 0 3-1h0 1v1l-1 1c0 1-1 1-2 1 0 1-1 1-2 1v-2h0l1-1z" class="F"></path><path d="M570 152l2 1h0c-1 0-1 1-1 1h-1-1l1 1c-1 0-2 1-3 1h-1c0-1 0-1-1-1h0c1-1 2-1 2-2h0c1-1 2-1 3-1z" class="k"></path><path d="M551 156s1 0 1 1c1-1 2 0 3 0 1 1 2 2 2 3v1c-1 1-1 1-1 2 5 1 11 3 16 3h0l-5 2h-1c-7 1-15-1-20-4-1-2-3-3-4-5v-2h1l1 1h2c2-1 3-1 5-2z" class="C"></path><path d="M542 157h1l1 1h2c1 1 2 1 2 2h0l-2-1h-2c-1 0 0-1-2 0v-2z" class="v"></path><path d="M552 157c1-1 2 0 3 0 1 1 2 2 2 3v1c-1 1-1 1-1 2-2-1-3-1-5-3h3c0-2-1-2-2-3z" class="M"></path><path d="M572 130h3l1 1-1 1h0-1l1 1c2 0 3 0 5 1h0l2 2h2l3 6v1h-1c-3-2-6-4-10-3l-2 2c-1 1-1 3-1 4l1 1 1 1-1 1 1 1c1 0 0 0 1 1h-2-2l-1-1c-1 0-2-1-3-1-1-1-2-2-2-4-1 0-1-1-1-2h0c0-4 1-6 2-9l1-2c1-1 2-1 4-2h0z" class="W"></path><path d="M580 134c-1 0-3 1-5 1 0-1 0-1-1-1h-1v-1l1-1 1 1c2 0 3 0 5 1h0zm-12-2c1 1 1 2 1 3h0c-1 2-1 4-3 5v5c-1 0-1-1-1-2h0c0-4 1-6 2-9l1-2z" class="a"></path><path d="M584 136l3 6v1h-1c-3-2-6-4-10-3l-2 2c-1 1-1 3-1 4l1 1 1 1-1 1c-2-1-3-3-4-5 1-3 2-5 5-6l1-1 3-1c1 0 1 0 1 1l1-1h1 2z" class="Y"></path><path d="M579 136c1 1 2 2 2 3-2-1-4-1-6-1l1-1 3-1z" class="T"></path><path d="M584 136l3 6h0l-6-3c0-1-1-2-2-3 1 0 1 0 1 1l1-1h1 2z" class="N"></path><path d="M550 140c1-2 1-3 2-5v4l1 1c1-1 1-1 3-1 0 1 0 1-1 2-2 1-2 1-3 2 1 1 1 1 2 1 0 1-1 2-1 3l1 1v1c-2 0-2 0-3 2v2 1 1 1c-2 1-3 1-5 2h-2l-1-1h-1v2c1 2 3 3 4 5h0c-2 0-2-1-3-2h0 0v2l-1-2-2 1-1-4-2-3v-2h3l-1-2c-1 0-1-1-1-1v-1-1h-1l1-1 1-2s0-1 1-2h0 0l1-1h1 0c2 0 2 0 4-1l-1 1c2-1 2-2 2-3h3z" class="H"></path><path d="M553 140c1-1 1-1 3-1 0 1 0 1-1 2-2 1-2 1-3 2h0v3c-1 0-1 0-2-1 0-2 1-2 2-4 0-1 0-1 1-1z" class="I"></path><path d="M552 143c1 1 1 1 2 1 0 1-1 2-1 3l1 1v1c-2 0-2 0-3 2v2 1 1l-1-3c0-1-1-2-2-3 0 0-1 0-1-1s2-1 3-2h0v-1c1 1 1 1 2 1v-3h0z" class="G"></path><path d="M552 143c1 1 1 1 2 1 0 1-1 2-1 3l1 1h-1c-1 0-1-1-2-1-1 1-1 1-2 1v-1h1v-1h0v-1c1 1 1 1 2 1v-3h0z" class="o"></path><path d="M543 153c0 1 1 1 1 1h0 1v-2l1-1h1c1 1 2 1 3 1h0l1 3v1c-2 1-3 1-5 2h-2l-1-1v-4z" class="W"></path><path d="M542 143c2 0 2 0 4-1l-1 1c0 1 0 1-1 1 0 3-1 7-1 9v4h-1c-1-3 0-6-1-8l-1 1h-2v-1h-1l1-1 1-2s0-1 1-2h0 0l1-1h1 0z" class="Z"></path><path d="M542 143c2 0 2 0 4-1l-1 1c0 1 0 1-1 1v1c-1 0-1 1-1 1h0-1c0-1 0-1-1-1v2 2l-1 1h-2v-1h-1l1-1 1-2s0-1 1-2h0 0l1-1h1 0z" class="I"></path><path d="M541 149c1 2 0 5 1 8v2c1 2 3 3 4 5h0c-2 0-2-1-3-2h0 0v2l-1-2-2 1-1-4-2-3v-2h3l-1-2c-1 0-1-1-1-1v-1h2l1-1z" class="H"></path><path d="M538 150h2v6-2l-1-2c-1 0-1-1-1-1v-1z" class="g"></path><path d="M540 154v2c0 2 1 4 2 6l-2 1-1-4-2-3v-2h3z" class="G"></path><path d="M326 147c1-1 3 0 5 1v1c-1 1-4 2-5 4 0 1 0 1 1 2h0l2 1 1-1v-1l1 1c1 1 2 0 3 0h4l-2 2v1h2v1 2h2c1 1 1 1 0 2 2 1 2 1 3 3v5 8 1 8h1v2h2c0 1 0 1-1 2l-1-1v1l-1 1v-1-1h-2v4l2 3v3c-1 1-2 0-3 0h0-1c-1 0-1-2-1-3h-2-1l-1 1c-1-1-3-1-4-2h-7v1l-1 1v-1-1l1-2h0l-2-2h-1c-2 0-2 0-3 1h-2c0-1 0-1-1-2l-1 1v-1-1h-2v-1h-2l-5 1h-1c0-1 0-1-1-1v-1h-2 0c1-1 1-1 1-2 1-1 1-1 1-2l-1-1h0l1-1h1 0 1v1c1-1 3-1 3 0 0-1 1-1 1-2h2l1 1h2v-1c0-1-1-2-1-2l-1-1h1 2v2c1-1 1-2 1-3l1-1v-2l-1-4c0-2 1-4 1-5-1-1-1-1-2 0h0c0-1-1-1-1-1v-1c0-1 0-2-1-3v-1s-2-2-3-2c0-1-2-1-2-1h0l1-1c1-1 1-2 1-4h0l1-1h0c2-1 2-1 3-3 1 0 1 0 2 1h1 0c1 0 1 1 2 2h0c1 0 1 0 2-1l1-1c2 0 3-1 5-2z" class="L"></path><path d="M313 182h1v1h-2l-1 1h-4c0-1 1-1 1-2h2l1 1h2v-1z" class="N"></path><path d="M313 182c0-1-1-2-1-2l-1-1h1 2v2c1 1 2 2 3 4l-1 2h-1c0-1-1-1-1-2-1 0-1-1-2-1h-1l1-1h2v-1h-1z" class="T"></path><path d="M316 175v3c1 3 2 6 4 8h1c1 2 1 4 3 5l1 1c-1 0-1 0-2 1h-1c-1-1 0-1 0-2h-1v1h-1v-1c-2-1-3-1-5-2v-1c3 1 5 1 7 2l-5-5c-1-2-2-3-3-4 1-1 1-2 1-3l1-1v-2z" class="g"></path><path d="M315 189c2 1 3 1 5 2v1h1v-1h1c0 1-1 1 0 2h1c1-1 1-1 2-1l2 1-2 1v1h-1-1l-2-2h-1c-2 0-2 0-3 1h-2c0-1 0-1-1-2l-1 1v-1-1h-2v-1h-2c2 0 4 0 6-1z" class="D"></path><path d="M315 189c2 1 3 1 5 2v1c-2 0-5-1-6-1v1l-1 1v-1-1h-2v-1h-2c2 0 4 0 6-1z" class="O"></path><path d="M303 183h1v1c2 1 4 2 6 2l5 2v1c-2 1-4 1-6 1l-5 1h-1c0-1 0-1-1-1v-1h-2 0c1-1 1-1 1-2 1-1 1-1 1-2l-1-1h0l1-1h1 0z" class="F"></path><path d="M303 183h1v1c2 1 4 2 6 2l-1 1v1c-1-1-2-1-2-1-2 0-4 1-5 2h-2 0c1-1 1-1 1-2 1-1 1-1 1-2l-1-1h0l1-1h1 0z" class="b"></path><path d="M303 183h1v1c2 1 4 2 6 2l-1 1c-1-1-2-1-4-1-1-1-2 0-3-1l1-2h0z" class="Y"></path><path d="M321 186c2 1 4 3 7 4l1 2 1-1v-3h0l3 5c1 2 4 3 5 5h-2-1l-1 1c-1-1-3-1-4-2h-7v1l-1 1v-1-1l1-2h0 1 1v-1l2-1-2-1-1-1c-2-1-2-3-3-5z" class="b"></path><path d="M321 186c2 1 4 3 7 4l1 2h0c1 0 2 0 2 1h0c-2-1-3-1-5-2 0 0-1-1-2-1v1c-2-1-2-3-3-5z" class="p"></path><path d="M327 193l8 5-1 1c-1-1-3-1-4-2h-7v1l-1 1v-1-1l1-2h0 1 1v-1l2-1z" class="g"></path><path d="M324 195h1v-1l1 1s2 0 3 1h0c-2 1-3 0-5 0v-1z" class="i"></path><path d="M315 171h1c1-1 2 0 3 0h1s1 0 2 1c1 0 2 1 3 2l-1 3c0 1 1 3 0 3 1 1 1 1 1 2h1v3h1v-1h1c0 2 1 2 2 4h0v3l-1 1-1-2c-3-1-5-3-7-4h-1c-2-2-3-5-4-8v-3l-1-4z" class="M"></path><path d="M320 171s1 0 2 1c1 0 2 1 3 2l-1 3v3c-1 0-3-1-4-1v-1c1-1 0-2 0-3-1 0 0 0-1-1h0 0l-2 1h0v-1l1-1c1 0 1-1 2-1l-1-1h1z" class="U"></path><path d="M324 177c0 1 1 3 0 3 1 1 1 1 1 2h1v3h1v-1h1c0 2 1 2 2 4h0v3l-1 1-1-2c-3-1-5-3-7-4h-1l1-2c-1-1-2-3-2-4l1-1c1 0 3 1 4 1v-3z" class="M"></path><path d="M325 182h1v3l-1-1-1 2v1h2c0 1 1 1 1 2v1h1c-3-1-5-3-7-4h-1l1-2 1 1h1v-3h1c0 1 0 1 1 1v-1z" class="T"></path><path d="M324 177c0 1 1 3 0 3 1 1 1 1 1 2v1c-1 0-1 0-1-1h-1v3h-1l-1-1c-1-1-2-3-2-4l1-1c1 0 3 1 4 1v-3z" class="h"></path><path d="M324 168h1l1 1 2 1c1 1 2 1 3 1h0c1 1 2 1 4 2 1 1 2 0 3 0v4h0l1 2h2l2 1v8h1v2h2c0 1 0 1-1 2l-1-1v1l-1 1v-1-1h-2v4l2 3v3c-1 1-2 0-3 0h0-1c-1 0-1-2-1-3-1-2-4-3-5-5l-3-5c-1-2-2-2-2-4h-1v1h-1v-3h-1c0-1 0-1-1-2 1 0 0-2 0-3l1-3c-1-1-2-2-3-2v-1c0-1 1-2 2-3z" class="N"></path><path d="M334 184l2 1v1c-1 1-2 1-3 2l-1-1v-1c1 0 2-1 2-2z" class="W"></path><path d="M333 191h4c1 2 0 2 1 4v1l-5-5z" class="a"></path><path d="M340 187l1 1c-1 2 0 2 0 3v4h0l-2-1v-3c0-2 0-2 1-4z" class="O"></path><path d="M339 194l2 1h0l2 3v3c-1 1-2 0-3 0 0-2-1-3-2-5v-1h1v-1z" class="L"></path><path d="M339 194l2 1v2h-1c-1-1-1-1-1-2v-1z" class="H"></path><path d="M334 178c1 0 1 0 2 1-1 1-1 1-2 3 1 0 1 1 1 1v1h-1c0 1-1 2-2 2 0 1 0 1-1 2l2 3 5 5c1 2 2 3 2 5h0-1c-1 0-1-2-1-3-1-2-4-3-5-5l-3-5c-1-2-2-2-2-4h-1v1h-1v-3h-1c0-1 0-1-1-2 2 0 2 0 4 1h1c1 0 1 0 2 1h0l1-1v-3h2 0z" class="Z"></path><path d="M334 178c1 0 1 0 2 1-1 1-1 1-2 3 1 0 1 1 1 1v1h-1c0 1-1 2-2 2 0 1 0 1-1 2v-4l-1-1v-1h1 0l1-1v-3h2 0z" class="H"></path><path d="M335 173c1 1 2 0 3 0v4h0l1 2h2l2 1v8h1v2h2c0 1 0 1-1 2l-1-1v1l-1 1v-1-1h-2c0-1-1-1 0-3l-1-1v-1h-2c-1 0-2-2-3-2v-1s0-1-1-1c1-2 1-2 2-3-1-1-1-1-2-1 0-1-1-2-1-2v-2h0c1 0 1 0 2-1z" class="k"></path><path d="M340 180h0v2c-1 1-1 1-2 1 0-1-1-1-1-2h0l2-1h1z" class="D"></path><path d="M335 183c1 0 1-1 1-2h1c0 1 1 1 1 2 1 0 2 0 2 1v2h-2c-1 0-2-2-3-2v-1z" class="t"></path><path d="M339 179h2l2 1v8 1h-1v-3-4h-1c0-1-1-1-1-2h0l-1-1z" class="Q"></path><path d="M336 178l1-3 1-1v3h0l1 2 1 1h-1l-2 1h0-1c0 1 0 2-1 2 0 0 0-1-1-1 1-2 1-2 2-3v-1z" class="k"></path><path d="M338 177l1 2 1 1h-1-2 0c0-1 1-1 1-2v-1z" class="Z"></path><path d="M335 173c1 1 2 0 3 0v4-3l-1 1-1 3v1c-1-1-1-1-2-1 0-1-1-2-1-2v-2h0c1 0 1 0 2-1z" class="J"></path><path d="M333 174h0c1 1 2 1 3 2v2 1c-1-1-1-1-2-1 0-1-1-2-1-2v-2z" class="T"></path><path d="M324 168h1l1 1 2 1c1 1 2 1 3 1h0c1 1 2 1 4 2-1 1-1 1-2 1h0v2s1 1 1 2h0-2v3l-1 1h0c-1-1-1-1-2-1h-1c-2-1-2-1-4-1 1 0 0-2 0-3l1-3c-1-1-2-2-3-2v-1c0-1 1-2 2-3z" class="C"></path><path d="M328 170c1 1 2 1 3 1h0c-1 0-1 0-1 1v1c-1 1-1 2-2 2v-5z" class="p"></path><path d="M331 182v-2s-1-1-1-2c-1-1-1-1 0-2s1-1 3-2v2s1 1 1 2h0-2v3l-1 1z" class="J"></path><path d="M324 168h1l1 1-1 1c0 1 0 1 1 1v2l-1 1h0c-1-1-2-2-3-2v-1c0-1 1-2 2-3z" class="Y"></path><path d="M326 147c1-1 3 0 5 1v1c-1 1-4 2-5 4 0 1 0 1 1 2h0l2 1 1-1v-1l1 1c1 1 2 0 3 0h4l-2 2v1h2v1 2h2c1 1 1 1 0 2 2 1 2 1 3 3v5 8 1l-2-1h-2l-1-2h0v-4c-1 0-2 1-3 0-2-1-3-1-4-2h0c-1 0-2 0-3-1l-2-1-1-1h-1c-1 1-2 2-2 3v1c-1-1-2-1-2-1h-1c-1 0-2-1-3 0h-1c0-2 1-4 1-5-1-1-1-1-2 0h0c0-1-1-1-1-1v-1c0-1 0-2-1-3v-1s-2-2-3-2c0-1-2-1-2-1h0l1-1c1-1 1-2 1-4h0l1-1h0c2-1 2-1 3-3 1 0 1 0 2 1h1 0c1 0 1 1 2 2h0c1 0 1 0 2-1l1-1c2 0 3-1 5-2z" class="C"></path><path d="M316 165s0-1-1-1l-1-1v-1h1c1-1 2-1 4-1l-2 3-1 1z" class="K"></path><path d="M314 151c0-1 1-1 2-2 1 0 1 1 2 2h0 0l1 2-1 1v-1h-1c-1-1-2-1-3-2z" class="F"></path><path d="M331 148v1c-1 1-4 2-5 4 0 1 0 1 1 2h0l2 1c-1 1-1 3-1 4l1 2c-1 0-1 0-2-1s-1-1-1-2v-3c-1-1-1-1-2-1-1 1-1 1-1 3h0v1h1 0v2-2h0c-1 1-2 2-2 3h-1v-1-1c-1 0-1 1-2 1l1 3-1 1v-1h-2l2-3c1-3 3-6 6-8 1-2 5-3 6-5z" class="U"></path><path d="M317 164h2v1l1-1-1-3c1 0 1-1 2-1v1 1h1c0-1 1-2 2-3h0v2 4 3c-1 1-2 2-2 3v1c-1-1-2-1-2-1h-1c-1 0-2-1-3 0h-1c0-2 1-4 1-5v-1l1-1z" class="H"></path><path d="M320 166c1-1 1-2 2-3 1 0 1 1 2 2v3c-1 1-2 2-2 3v1c-1-1-2-1-2-1l-1-1c0-2 0-2 1-4z" class="K"></path><path d="M320 166c1 1 2 1 2 2s-1 1-2 2h-1c0-2 0-2 1-4z" class="S"></path><path d="M313 148c1 0 1 0 2 1h1 0c-1 1-2 1-2 2 1 1 2 1 3 2 0 2 0 3-1 5-1 1-2 2-3 2l-1 1v-1s-2-2-3-2c0-1-2-1-2-1h0l1-1c1-1 1-2 1-4h0l1-1h0c2-1 2-1 3-3z" class="O"></path><path d="M313 148c1 0 1 0 2 1h1 0c-1 1-2 1-2 2h-1-1l-1 2v3l-1 1v-1-5h0c2-1 2-1 3-3z" class="d"></path><path d="M310 151v5 1l1 1 1-1 2-3c0 2 0 4-1 6l-1 1v-1s-2-2-3-2c0-1-2-1-2-1h0l1-1c1-1 1-2 1-4h0l1-1z" class="Y"></path><path d="M330 155v-1l1 1c1 1 2 0 3 0h4l-2 2v1h2v1 2h2c1 1 1 1 0 2 2 1 2 1 3 3v5 8 1l-2-1h-2l-1-2h0v-4c-1 0-2 1-3 0-2-1-3-1-4-2h0c-1 0-2 0-3-1l-2-1-1-1h-1v-3-4-2h0-1v-1h0c0-2 0-2 1-3 1 0 1 0 2 1v3c0 1 0 1 1 2s1 1 2 1l-1-2c0-1 0-3 1-4l1-1z" class="M"></path><path d="M343 171v8 1l-2-1c1 0 1-1 1-1l-1-2v-2h2 0v-3z" class="T"></path><path d="M337 160l1-1v2h2c1 1 1 1 0 2h0c0 2 1 4 0 6h-1c0-1 0-2-1-3h-1v-1c0-1-1-1-1-2h-1c1-1 2-2 2-3z" class="J"></path><path d="M337 160l1-1v2c1 1 2 2 1 4h-1 0l-1-2h-1-1c1-1 2-2 2-3z" class="l"></path><path d="M325 168h2v-1c0-1 0-1-1-1v-1-1c1 0 1 0 1 1 2 2 3 2 5 4h0-1l1 1 1 1v-1c1 1 1 2 2 3-2-1-3-1-4-2h0c-1 0-2 0-3-1l-2-1-1-1z" class="T"></path><path d="M330 155v-1l1 1c1 1 2 0 3 0h4l-2 2v1h2v1l-1 1c0 1-1 2-2 3h0c-1 1-1 1-1 3-1 0-2-1-3-2h-1v-1l-1-1-1-2c0-1 0-3 1-4l1-1z" class="H"></path><path d="M330 155v1 3 4l-1-1-1-2c0-1 0-3 1-4l1-1z" class="r"></path><path d="M334 155h4l-2 2v1h2v1l-1 1c0 1-1 2-2 3h0l-1-1c-1-2-1-4 0-6v-1z" class="I"></path><path d="M334 155h4l-2 2v1h2v1l-1 1c-1 0-1 0-1 1l-1-1v-4h-1v-1z" class="K"></path><path d="M304 191l5-1h2v1h2v1 1l1-1c1 1 1 1 1 2h2c1-1 1-1 3-1h1l2 2h0l-1 2v1 1l1-1v-1h7c1 1 3 1 4 2l1-1h1 2c0 1 0 3 1 3 1 1 1 2 1 4h-1l1 1c0 1 0 2 1 3v2c0 1 1 1 1 2v3c0 1 0 0 1 1h0v4l1-2v1h0 0l1 2h0l1-1c0 1 0 1 1 2-1 2 0 4-2 7v1h0l-2-1c-1 0-1 0-1-1h-1v3l-1 1h-1-1v1c-1 0-1 0-2-1h-1v1l-1-1-2 2c-2 1-4 2-7 2l1-1-2-2c0-1-2 0-3 0-4 0-8 0-12-1v-2h-1l-1-3-2-1h-1v-1h1c1-2 1-4 1-6-1-2-2-3-4-4h2l1 1v-1l-1-1-3-2-2 1c-1 0-1 1-2 1h-1v1h-1c0-5-1-10 0-14l1-4h1c0-1 1-2 1-3 2-1 4-3 6-4h0z" class="u"></path><path d="M325 207c0 1 1 1 1 2v2 1h-1c-1-2 0-3 0-5z" class="L"></path><path d="M301 202c-1-2-1-2-1-4 1-1 1-1 3-1l-2 5h0z" class="T"></path><path d="M304 215l1-1h1c1 1 2 1 4 2 0 1 0 1 1 2l-6-2-1-1z" class="U"></path><path d="M325 207c-1-2-3-1-3-2h1 5 0v1c0 1-1 1-2 1v2c0-1-1-1-1-2z" class="N"></path><path d="M323 198h1c2 1 3-1 5 1 0 1 0 1-1 2s-2 1-3 1-2 0-2-1l-1-2 1-1z" class="h"></path><path d="M305 216l6 2 1 2v1c-2 0-3 0-4-1l-2 6-1 1h-1v-1h1c1-2 1-4 1-6-1-2-2-3-4-4h2l1 1v-1z" class="G"></path><path d="M303 197l1-2v1 2c1 0 1 1 1 2s1 2 3 2c1 1 1 1 3 1v-1h1v1c-1 1-1 2-3 2-1 0-3 0-4-1s-1-2-2-2v-2c-2 2 0 4-1 6h0c-1-1-1-3-1-4h0l2-5z" class="F"></path><path d="M297 198v1c0 1-1 2-1 3v5c1-1 0-4 0-5 1 2 0 5 2 7l3 4-2 1c-1 0-1 1-2 1h-1v1h-1c0-5-1-10 0-14l1-4h1z" class="N"></path><path d="M296 215c0-1 1-3 2-4v-2l3 4-2 1c-1 0-1 1-2 1h-1z" class="j"></path><path d="M304 191l5-1h2v1h2v1 1l1-1c1 1 1 1 1 2h2c1-1 1-1 3-1h1l2 2h0l-1 2v1h-1v-2s-1-1-2-1h-2v1 2c-1 2 0 5 0 7-1-1-2-3-3-3s-1 1-2 1v-1h-1v1c-2 0-2 0-3-1-2 0-3-1-3-2s0-2-1-2v-2-1h0v-1h-3v1c-1 1-2 1-3 0 2-1 4-3 6-4h0z" class="U"></path><path d="M311 193h1v-1h1v1l1-1c1 1 1 1 1 2h2c-1 2-2 4-2 6l-1-1h0v-1c-1 0-1 1-1 1h-1v-1c0-1-1-2-2-3h0 0v-1l1-1z" class="I"></path><path d="M311 193h1v-1h1v1l1-1c1 1 1 1 1 2l-1 2-3-3z" class="B"></path><path d="M304 196l2-1c1-1 2 0 4 0h0c1 1 2 2 2 3-1 1-2 1-3 2h0c0 1 0 1 1 2h1v1c-2 0-2 0-3-1-2 0-3-1-3-2s0-2-1-2v-2z" class="J"></path><path d="M309 200v-2c0-1-1-1-2-2 1-1 2-1 3-1 1 1 2 2 2 3-1 1-2 1-3 2z" class="L"></path><path d="M304 191l5-1h2v1h2v1h-1v1h-1l-1 1v1h0c-2 0-3-1-4 0l-2 1v-1h0v-1h-3v1c-1 1-2 1-3 0 2-1 4-3 6-4h0z" class="a"></path><path d="M304 191l5-1h2v1c0 1-1 1-1 1l-1-1c-1 0-4 1-5 0h0z" class="d"></path><path d="M311 191h2v1h-1v1h-1l-1 1v1h0c-2 0-3-1-4 0l-2 1v-1h0c2-2 3-2 6-3 0 0 1 0 1-1z" class="C"></path><path d="M306 226l1 1h4v-1c1-1 2-1 3-1 0 0 1 0 1-1 1 0 2 0 2-1 1 0 1-1 3-1h0c-1-1-1-1-2-1-1 1-1 1-2 1h-1c0-4-1-3-2-6-1 0-1 0-1-1 0-2-1-3-1-5 0-1 0-3 1-4h2c1 0 1 1 2 3v3h1v-3h0 1c1 2 3 3 6 4l1-1h1l3-1h1v2h2c1 0 2 1 3 1l-1 1c-2-1-3-1-5-1-3 1-4 3-6 5h0v3l-1 1v-1c-1 1-2 1-3 1-1 1-1 1-1 2h-1c0 1-1 1-2 1-2 1-4 1-5 3v1l-1 1h-1l-1-3-2-1 1-1z" class="N"></path><path d="M307 228h3v2l-1 1h-1l-1-3z" class="c"></path><path d="M314 206c1 0 1 1 2 3v3 1c1 0 1 1 2 1 1 1 1 1 1 2h0-1-1l-2 1c0-1-1-2-1-3-1-4-1-5 0-8z" class="F"></path><path d="M316 212h1v-3h0 1c1 2 3 3 6 4l1-1h1l3-1h1v2h2c1 0 2 1 3 1l-1 1c-2-1-3-1-5-1-3 1-4 3-6 5h0v3l-1 1v-1c-3-2-5-3-7-5l2-1h1 1 0c0-1 0-1-1-2-1 0-1-1-2-1v-1z" class="m"></path><path d="M317 211h2v2h-1l-1-1v-1z" class="D"></path><path d="M329 211h1v2h2c1 0 2 1 3 1l-1 1c-2-1-3-1-5-1-3 1-4 3-6 5l-1-1v-2h0c0-1 1-2 2-3l1-1h1l3-1z" class="F"></path><path d="M330 197c1 1 3 1 4 2l1-1h1 2c0 1 0 3 1 3 1 1 1 2 1 4h-1l1 1c0 1 0 2 1 3v2c0 1 1 1 1 2v3c0 1 0 0 1 1h0v4l1-2v1h0 0l1 2h0l1-1c0 1 0 1 1 2-1 2 0 4-2 7v1h0l-2-1c-1 0-1 0-1-1h-1v3l-1 1h-1c1-5 1-12 0-16-1-1-1-2-1-3-1 0-1-1-2-1 0 0 0 1-1 1h0c-1 0-2-1-3-1h-2v-2h-1v-2l2 1h1c0-1 0-1 1-1l1-1v-1c-1-1-2-1-2-1h-1v-2c1-1 1-2 3-2-2-2-3-3-4-5z" class="L"></path><path d="M340 206c0 1 0 2 1 3v3h-1l-2-4c1 0 1 0 2-1v-1z" class="Z"></path><path d="M341 209v2c0 1 1 1 1 2v3c0 1 0 0 1 1h0v4 3h-1c-1-1 0-3 0-4l-2-8h1v-3z" class="X"></path><path d="M342 220v-3h1v4 3h-1c-1-1 0-3 0-4z" class="l"></path><path d="M336 198h2c0 1 0 3 1 3 1 1 1 2 1 4h-1l1 1v1c-1 1-1 1-2 1-1-4-2-6-4-9l1-1h1z" class="k"></path><path d="M336 198h2c0 1 0 3 1 3 1 1 1 2 1 4h-1s0-1-1-2c0-2-1-3-2-5zm7 23l1-2v1h0 0l1 2h0l1-1c0 1 0 1 1 2-1 2 0 4-2 7v1h0l-2-1c-1 0-1 0-1-1v-5h1v-3z" class="n"></path><path d="M342 224h1c1 2 1 4 1 6h-1c-1 0-1 0-1-1v-5z" class="k"></path><path d="M333 204v-1h1c1 2 2 4 3 7 0 1 1 2 1 4-1 0-1-1-2-1 0 0 0 1-1 1h0c-1 0-2-1-3-1h-2v-2h-1v-2l2 1h1c0-1 0-1 1-1l1-1v-1c-1-1-2-1-2-1 0-1 0-1 1-2z" class="K"></path><path d="M333 204l2 3h-1c-1-1-2-1-2-1 0-1 0-1 1-2z" class="n"></path><path d="M335 207c1 2 1 3 1 5h0c-2-1-3-2-5-2h1c0-1 0-1 1-1l1-1v-1h1z" class="U"></path><path d="M335 214h0c1 0 1-1 1-1 1 0 1 1 2 1 0 1 0 2 1 3 1 4 1 11 0 16h-1v1c-1 0-1 0-2-1h-1v1l-1-1-2 2c-2 1-4 2-7 2l1-1-2-2c0-1-2 0-3 0-4 0-8 0-12-1v-2l1-1v-1c1-2 3-2 5-3 1 0 2 0 2-1h1c0-1 0-1 1-2 1 0 2 0 3-1v1l1-1v-3h0c2-2 3-4 6-5 2 0 3 0 5 1l1-1z" class="J"></path><path d="M332 218l1-1c2 0 3 2 4 3 0 0 0 1-1 1l-1-1h0-2c-1-1-1-2-1-2z" class="N"></path><path d="M333 226l1-1v-1l-1-1c1-1 1-2 2-3l1 1c1 2 0 4-1 5v1c-1-1-1-1-2-1z" class="K"></path><path d="M331 219h1v-1s0 1 1 2h2 0c-1 1-1 2-2 3l1 1v1l-1 1c-1 0-2 1-4 0h0 1v-1h0c1 0 1-1 2-2h0c0-1-1-2-1-3h-1l1-1z" class="H"></path><path d="M335 214h0c1 0 1-1 1-1 1 0 1 1 2 1 0 1 0 2 1 3 1 4 1 11 0 16h-1v1c-1 0-1 0-2-1h-1v1l-1-1 3-4h0 0c1-2 2-4 2-5s0-3-1-3v-2l-1-1c0-2-2-2-3-3l1-1z" class="I"></path><path d="M337 229v1c0 1-1 1-1 2h1s1 0 1 1v1c-1 0-1 0-2-1h-1v1l-1-1 3-4z" class="c"></path><path d="M323 219c2-2 3-4 6-5v1h2v4l-1 1h1c0 1 1 2 1 3h0c-1 1-1 2-2 2h0v1h-1-2c0-1-1 0-3 0v-2c-1-2-1-3-1-5h0z" class="N"></path><path d="M325 220l1-1h1l1 1-1 2c-1 0-2-1-2-2z" class="J"></path><path d="M330 225s-1-1-1-2h0 3c-1 1-1 2-2 2h0z" class="L"></path><path d="M323 219c2-2 3-4 6-5v1c-1 1-1 1-1 2v1c-1 1-1 2 0 2l-1-1h-1l-1 1h0-1c-1 1 0 2 0 4-1-2-1-3-1-5h0z" class="a"></path><path d="M322 222v1l1-1v-3c0 2 0 3 1 5v2c2 0 3-1 3 0h2 0c2 1 3 0 4 0s1 0 2 1c-1 1-2 3-4 4h-2c2 0 2 1 3 1v3c-2 1-4 2-7 2l1-1-2-2c0-1-2 0-3 0-4 0-8 0-12-1v-2l1-1v-1c1-2 3-2 5-3 1 0 2 0 2-1h1c0-1 0-1 1-2 1 0 2 0 3-1z" class="P"></path><path d="M323 228c1 0 2 0 2-1h2v1h-1c0 1 0 2-1 3l-2-2v-1z" class="D"></path><path d="M317 225h2c1 2 1 3 2 4-1 0-3-1-4-1 0 1-1 1-1 1 0-1-1-2-1-3 1 0 2 0 2-1z" class="h"></path><path d="M333 226c1 0 1 0 2 1-1 1-2 3-4 4 1-2 1-2 0-3h-1l-2 1-1-1v-1l2-1c2 1 3 0 4 0z" class="G"></path><path d="M317 225h1c0-1 0-1 1-2v1l3 1c1 1 1 2 1 3v1h-2c-1-1-1-2-2-4h-2z" class="d"></path><path d="M322 222v1l1-1v-3c0 2 0 3 1 5v2c2 0 3-1 3 0h2 0l-2 1h-2c0 1-1 1-2 1 0-1 0-2-1-3l-3-1v-1c1 0 2 0 3-1z" class="O"></path><path d="M329 231c2 0 2 1 3 1v3c-2 1-4 2-7 2l1-1-2-2c0-1-2 0-3 0l8-3z" class="T"></path><path d="M517 112h11c12 0 25 0 36 3 1 0 2 1 2 1h1 1l1 1h2c1 1 2 1 2 3v2h-2l-4-2-3 1c1 0 1 1 2 1s1 1 2 2v1h1v2h-1c-1 1-1 1-1 3h5c-2 1-3 1-4 2l-1 2c-1-1-1-1-2-1v1h-1c-1 1-1 2-1 3h0v2c1 1 1 6 0 7h-2c-1 1-1 2-2 3 0 1-1 2-2 3-1 0-1-1-2-1v-1c1 0 1-1 2-1 1-2 1-2 0-4-1-1-1-1-3-1h0v-1l3-1h0c-1-1-1-2-1-3-2 0-2 0-3 1l-1-1v-4c-1 2-1 3-2 5h-3c0 1 0 2-2 3l1-1c-2 1-2 1-4 1h0-1l-1 1h0 0c-1 1-1 2-1 2l-1 2-1 1h-2c-1-1-2-1-3-2-1 1-2 3-3 4-3-1-4 1-7 2h0c-3 2-6 1-9 0h0-1c-2 0-7-2-8-3h0c-2-3-5-3-7-7-3-5-3-13-1-18l1-1 1-1c5-6 11-8 18-10l1-1z" class="W"></path><path d="M560 142l1-1 1 1-2 2h-1c0-1 1-2 1-2z" class="U"></path><path d="M523 126c1-1 1-2 2-2l2 1 1 1h-5z" class="T"></path><path d="M498 135c1 1 2 1 3 3v3c-2-2-3-4-3-6z" class="N"></path><path d="M501 127c1 0 1 0 1 1 1 0 1 1 1 2v1 2l-1 1-1-1v-6z" class="U"></path><path d="M512 151c-1-1-2-1-3-1 0-1 1-1 1-1l-2-1v-1c2 1 8 3 8 4s-1 1-1 1l-3-1z" class="B"></path><path d="M522 153v-2l1-1c2 0 3-1 5 0h0c1-1 1-2 2-3h2c-1 1-2 3-3 4-3-1-4 1-7 2z" class="a"></path><path d="M511 131c-2 1-1 1-2 2-1-1-3-1-4-2-1-2-1-2-1-4 1 1 2 1 2 1 2 1 3 2 4 2l1 1h0z" class="m"></path><path d="M508 121c1 2 0 4 2 4-1 1-2 1-3 1 0 1-1 2-1 2s-1 0-2-1c0-1 1-2 1-3 1-1 1-2 3-3z" class="B"></path><path d="M534 138c1 1 3 1 3 3v1 1c0 1 0 3 1 5h0l-1 1h-2l1-1v-2l-1-1v-1c0-1 0-2-1-3h0v-3z" class="H"></path><path d="M508 121c3-1 5 0 8 1-1 1-1 2-2 2l-4 1c-2 0-1-2-2-4z" class="U"></path><path d="M558 125c2 3 4 8 5 12h0v2c1 1 1 6 0 7h-2 0c1 0 1 0 1-1 1 0 1-1 1-1-1-2 0-3-1-5 0 0-1 0-1-1s1-2 0-4h-1c1-1 1-1 0-2h0 0v1c-1-2-3-4-2-5v-3z" class="M"></path><path d="M547 126c1 0 1 0 2 1 2 2 3 5 3 8-1 2-1 3-2 5h-3s0-1 1-1v-2c-1-1-1-3-1-5h0c1-1 1-3 1-4l-1-2z" class="Q"></path><path d="M548 128c0 3 1 6 0 9-1-1-1-3-1-5h0c1-1 1-3 1-4z" class="G"></path><path d="M550 140l-1-1c2-4 1-8-1-12h1c2 2 3 5 3 8-1 2-1 3-2 5zm-53-16l1-1c0 4-1 8 0 12 0 2 1 4 3 6s4 4 7 6v1l2 1s-1 0-1 1c1 0 2 0 3 1-5-1-9-3-12-7-2-3-4-11-4-15l1-5z" class="D"></path><path d="M557 124c-1-1-3-3-5-4-2-2-5-3-8-4 8 0 15 2 23 4l-3 1-2-1h0c-1 1-1 1-2 1h-1-1l-1 1v1 1z" class="B"></path><path d="M533 132c0-1 0-1 1-2h0c2 0 2 0 2 1l1 2 1 1s1 1 1 2l2 2h-1v1 3h0l1 1-1 1h0 0c-1 1-1 2-1 2l-1 2h0c-1-2-1-4-1-5v-1-1c0-2-2-2-3-3h1v-1-1l-2 1v-5z" class="a"></path><path d="M539 146c0-4-1-8-3-13h1l1 1s1 1 1 2l2 2h-1v1 3h0l1 1-1 1h0 0c-1 1-1 2-1 2z" class="h"></path><path d="M497 124l-1 5c0 4 2 12 4 15 3 4 7 6 12 7l3 1h-2v1h0-1c-2 0-7-2-8-3h0c-2-3-5-3-7-7-3-5-3-13-1-18l1-1z" class="a"></path><path d="M520 145c-4 1-8 1-11 0-2-1-4-2-5-4 0-2-1-2 0-3 2-1 2-2 4-2l1 1h1l2-1c1 2-1 2 0 3 1 0 2 0 3-1l1-1h1v4 2h1l2 1v1z" class="C"></path><path d="M510 142c-2 0-3-2-4-4h0l1 1h1c0 1 1 1 1 1h0c1 1 1 1 2 1l1 1h-1-1z" class="U"></path><path d="M512 139c1 0 2 0 3-1 0 2-1 3-2 4s-2 1-3 0h1 1l-1-1c-1 0-1 0-2-1h0l2-1v1h1v-1z" class="H"></path><path d="M559 121h1c1 0 1 0 2-1h0l2 1c1 0 1 1 2 1s1 1 2 2v1h1v2h-1c-1 1-1 1-1 3h5c-2 1-3 1-4 2l-1 2c-1-1-1-1-2-1v1h-1c-1 1-1 2-1 3-1-4-3-9-5-12l-1-1v-1-1l1-1h1z" class="C"></path><path d="M564 134l-1-1s0-1 1-1v-2l1-2 1-1h2c-1 1-1 1-1 3h5c-2 1-3 1-4 2l-1 2c-1-1-1-1-2-1v1h-1z" class="F"></path><path d="M557 123v-1l1-1h1c0 1 2 3 2 3v-2l1-1h1l1 1c0 1 0 1-1 2v1h0v5l-6-7z" class="W"></path><path d="M541 127v-1l1-1c1 1 1 2 2 4l3 3c0 2 0 4 1 5v2c-1 0-1 1-1 1 0 1 0 2-2 3l1-1c-2 1-2 1-4 1h0-1l-1-1h0v-3-1h1l-2-2c0-1-1-2-1-2l-1-1-1-2c2-1 3-1 5-1v-1-2z" class="D"></path><path d="M540 139v-1h1 1c-1 1-1 1-2 1z" class="S"></path><path d="M540 142l2-1 3-3c0 3-1 3-3 5h0-1l-1-1h0z" class="Y"></path><path d="M539 136l2-1c1-1 1-2 2-3 1 1 2 3 1 4 0 1 0 2-1 2h-1-1l-2-2z" class="W"></path><path d="M541 129h1c0 1 1 2 1 3-1 1-1 2-2 3l-2 1c0-1-1-2-1-2l-1-1-1-2c2-1 3-1 5-1v-1z" class="n"></path><path d="M536 131c2-1 3-1 5-1l1 1c0 1-1 1-1 1-1 1-1 0-1 1-1 0-2 1-2 1l-1-1-1-2z" class="M"></path><path d="M541 127v-1l1-1c1 1 1 2 2 4l3 3c0 2 0 4 1 5v2c-1 0-1 1-1 1 0 1 0 2-2 3l1-1c-2 1-2 1-4 1 2-2 3-2 3-5 1-3 1-4 0-7-1-2-2-3-4-4z" class="X"></path><path d="M528 126c2 0 3 0 4-2 2 2 3 3 5 4 1-1 1-1 1-2s0-1-1-2h-2v-1c1-1 2-1 3-1 3 0 6 2 8 3l1 1 1 2c0 1 0 3-1 4h0l-3-3c-1-2-1-3-2-4l-1 1v1 2 1c-2 0-3 0-5 1 0-1 0-1-2-1h0c-1 1-1 1-1 2v5c-1 1-1 1-3 1v1c-1-1-1-1-1-2l1-2-2 2-1-1 3-3-1-1c-1 1-2 0-3 0-2-1-3-1-5-3 0-1 0-1 1-2l1-1h5z" class="S"></path><path d="M529 132h-1v-1h0v-1h-1v-1c2 1 2 2 4 2-1-1 0-1-1-2h-2 2c1 1 1 1 1 2 1 0 1 1 1 1l1 1-1 1h0-2v1l-2 2-1-1 3-3-1-1z" class="B"></path><path d="M533 132v5c-1 1-1 1-3 1v1c-1-1-1-1-1-2l1-2v-1h2 0l1-1v-1z" class="k"></path><path d="M533 132v5c-1 1-1 1-3 1l2-4 1-1v-1z" class="d"></path><path d="M516 122l1-1c-1-2-3-2-5-3 2-1 4-2 6-2 4-1 8-1 12-2h10c1 0 1 1 2 1v1h-4c-6 0-14 1-17 7 0 1 0 3 1 4-1 1-1 1-1 2l-1-1v1h-3l-1 1c-2 1-4 1-5 3v-2h0l-1-1c-1 0-2-1-4-2 0 0 1-1 1-2 1 0 2 0 3-1l4-1c1 0 1-1 2-2z" class="c"></path><path d="M516 130c-1-1-1-2-1-3 1-2 2-2 3-3h2l1-1h0c0 1 0 3 1 4-1 1-1 1-1 2l-1-1v1h-3l-1 1z" class="E"></path><path d="M521 123h0c0 1 0 3 1 4-1 1-1 1-1 2l-1-1v1h-3c2-2 3-4 4-6z" class="U"></path><path d="M517 129h3v-1l1 1c2 2 3 2 5 3 1 0 2 1 3 0l1 1-3 3 1 1 2-2-1 2c0 1 0 1 1 2l-2 2c-3 2-5 4-8 4v-1l-2-1h-1v-2-4h-1l-1 1c-1 1-2 1-3 1-1-1 1-1 0-3l-2 1v-1-1c1 0 1-1 2-1l-1-1c1-2 3-2 5-3l1-1z" class="N"></path><path d="M526 132c1 0 2 1 3 0l1 1-3 3c-1 1-1 3-3 5v-3c1-1 1-2 2-3 1 0 1 0 1-1-1 0-1-1-1-2h0z" class="J"></path><path d="M517 141c1 0 1-1 2-1l1-1c1 0 1 0 2 1s1 1 1 2c-1 1-3 1-4 1h-2v-2z" class="W"></path><path d="M527 136l1 1 2-2-1 2c0 1 0 1 1 2l-2 2c-3 2-5 4-8 4v-1l-2-1h-1 2c1 0 3 0 4-1l1-1c2-2 2-4 3-5z" class="G"></path><path d="M529 137c0 1 0 1 1 2l-2 2c0-1-1-1-1-2l2-2z" class="g"></path><path d="M294 111h20c4 0 10-1 15 0h-1l1 1h2c1-1 4-1 6-1v1h3c1 1 2 3 3 5v3c-1 1-1 1 0 2h-1l1 1c0 1 0-1 0 1 1 1 1 1 1 2 0 2-1 3-1 4-1 3-1 4-3 6-1 1-3 1-5 1-1 0-2-1-3-1l-1-1h0l-1-2v-2l-2-2s-1 1-1 2l-1 1v-1h-1v1c-2 0-3 0-3 1 0 2 0 4-1 6v1l-1 1c-1 0-1 0-2 1h1c1 2 1 3 1 5h0c1 1 1 2 1 2l-1 1c-1 1-1 1-2 1h0c-1-1-1-2-2-2h0-1c-1-1-1-1-2-1-1 2-1 2-3 3l-2-1h0-1v-1c-1 0-2 1-3 1-1 1-1 1-2 1h-1l-3 4c-1-1-1-2-1-3h-2v-4 5h-1c0-8-1-16 2-23v-1c-3 2-7 3-10 2h-3c-2 0-5 1-7 0h-2-1-4-1c-2 1-5 1-7 1h1 3 0c-1 1-1 1-2 1l-20 2v-1h-2l1-1v-1l-3-1c-1-1-1-2-2-4v-1c0-1-2-1-2-2 1 0 1-2 2-3h0v-2h-1c1-2 3-3 4-4h-3-3 0c0-1 1-1 1-1h1l1-1h-5 0l15-1h1 3c1-1 1 0 2 0h9 4c2 1 5 2 7 4 1 0 3 2 4 2h1l1-1h0l1-1 1 1c0-1 0-1 1-2v-2h0c3-1 6 0 9 0h0l1-1h1l1-1z" class="W"></path><path d="M337 120l2 1c-1 1-1 2-2 2 0 0 0-1-1-1l1-2z" class="G"></path><path d="M260 123c0-2-1-3 0-5h2v1h6 0c-2 0-5 0-7 1l1 2-2 1z" class="v"></path><path d="M339 121c1 2 3 3 3 5 0 1-1 3-1 4-1-2-1-3-2-5l-2-1v-1c1 0 1-1 2-2z" class="D"></path><path d="M262 122c1 2 2 4 5 5-1 1-2 2-3 2s-2-1-2-2c-2-2-2-2-2-4l2-1z" class="S"></path><path d="M260 114c1 0 3 1 5 2 1 0 2 2 3 3h-6v-1l-1-1-2-2 1-1z" class="C"></path><path d="M330 117c3 0 5 1 7 3l-1 2c1 0 1 1 1 1v1l-3-1h-1c0-2-1-4-3-6z" class="i"></path><path d="M334 123v-1s0-1 1-1l1 1c1 0 1 1 1 1v1l-3-1z" class="B"></path><g class="H"><path d="M336 126l-1-1c1-1 3 0 4 0 1 2 1 3 2 5l-2 2h-1l-1 1v-1s-1 0-1-1h0 0c1-1 1-1 1-2s0-1-1-2v-1z"></path><path d="M333 127h1 2 0 0c1 1 1 1 1 2s0 1-1 2h0-3v-1h-1c0 1 0 1-1 1h0c-1 1-1 2-1 2v-2-2c1-2 2-2 3-2z"></path></g><path d="M333 127h1 2 0 0c1 1 1 1 1 2s0 1-1 2h0-3c1-2 2-2 2-4h-2z" class="I"></path><path d="M329 116l1 1c2 2 3 4 3 6l-3 1c0-1 0-2-1-2 0-1 0-1-1-2l-1 1-1-1c0-1 0-2-1-2v-1h-1l5-1z" class="e"></path><path d="M267 127h0 3c1 0 4 1 6 1 0 1-1 1-1 2l-1 1h-1-4-1c-1 0-1-1-2-1l-2-1c1 0 2-1 3-2z" class="m"></path><path d="M272 130c1-1 1-1 2-1l1 1-1 1h-1l-1-1z" class="B"></path><path d="M266 130l1-1c2 0 3 0 5 1l1 1h-4-1c-1 0-1-1-2-1z" class="J"></path><path d="M294 111h20c4 0 10-1 15 0h-1l1 1c-7-1-13-1-20 0-3 0-7 1-11 1l-1-1-1 1h2l-1 1h-1c0-1 0-2-1-2h-2l1-1z" class="Q"></path><path d="M331 112c1-1 4-1 6-1v1h3c1 1 2 3 3 5v3c-1 1-1 1 0 2h-1c-2-5-6-8-11-10z" class="C"></path><path d="M337 112h3c1 1 2 3 3 5v3c-2-3-4-5-6-8z" class="M"></path><path d="M329 122c1 0 1 1 1 2l3-1h1l3 1 2 1c-1 0-3-1-4 0l1 1v1h0 0-2-1c-1 0-2 0-3 2v2l-2-2s-1 1-1 2l-1 1v-1h-1v1l-1-3-1 1v-1c1-1 1-2 2-3h0v-1c1 0 2-1 3-1 1-1 1-1 1-2z" class="d"></path><path d="M329 122c1 0 1 1 1 2-1 1-2 1-3 2 0 1 0 1-1 1s-2 2-2 2l-1 1v-1c1-1 1-2 2-3h0v-1c1 0 2-1 3-1 1-1 1-1 1-2zm-1 7c2-2 3-3 5-4 1 0 2 0 3 1v1h0 0-2-1c-1 0-2 0-3 2v2l-2-2z" class="G"></path><path d="M293 112h2c1 0 1 1 1 2h-1c-3 1-5 3-8 5l-3 3c-1 0-2 0-3-1h-1 0c0-1 0-2 1-3v-1c0-1 0-1 1-2v-2h0c3-1 6 0 9 0h0l1-1h1z" class="S"></path><path d="M281 117c0-1 0-1 1-2 1 1 1 1 2 3-2 1-2 0-3 0v-1z" class="c"></path><path d="M282 113c3-1 6 0 9 0h0c-1 1-5 3-7 4 1-1 1-2 1-2h1c-1-1-1-1-2-1 0 0-1 0-2-1z" class="d"></path><path d="M248 112h3c1-1 1 0 2 0h0c2 1 5 0 7 2l-1 1 2 2 1 1h-2c-1 2 0 3 0 5s0 2 2 4c0 1 1 2 2 2l2 1c1 0 1 1 2 1-2 1-5 1-7 1v-2c0-1-2-4-3-4v-1-3l-5-5h-3 0c0-1-1-1-1-1-1 0-1 0-2-1 1 0 1-1 2-1-3 0-6 0-9 1h-3-3 0c0-1 1-1 1-1h1l1-1h-5 0l15-1h1z" class="T"></path><path d="M249 114c3 1 7 3 8 7 1 0 1 1 1 1l-5-5h-3 0c0-1-1-1-1-1-1 0-1 0-2-1 1 0 1-1 2-1z" class="j"></path><path d="M248 112h3c1-1 1 0 2 0h0c2 1 5 0 7 2l-1 1 2 2h-1-1c-1-1-3-2-5-3l-1-1c-2-1-3-1-5-1z" class="m"></path><path d="M240 115c3-1 6-1 9-1-1 0-1 1-2 1 1 1 1 1 2 1 0 0 1 0 1 1h0 3l5 5v3 1c1 0 3 3 3 4v2h1 3 0c-1 1-1 1-2 1l-20 2v-1h-2l1-1v-1l-3-1c-1-1-1-2-2-4v-1c0-1-2-1-2-2 1 0 1-2 2-3h0v-2h-1c1-2 3-3 4-4z" class="W"></path><path d="M235 124c1 0 1-2 2-3h0 1l2-1c-1 2-3 4-3 6 0-1-2-1-2-2z" class="C"></path><path d="M247 117c1 0 2 0 3 1v7h0-2 0l1-1h0c0-3-1-5-2-7z" class="j"></path><path d="M240 115c3-1 6-1 9-1-1 0-1 1-2 1 1 1 1 1 2 1 0 0 1 0 1 1v1c-1-1-2-1-3-1-3 0-4 0-6 2l-1 1-2 1h-1v-2h-1c1-2 3-3 4-4z" class="P"></path><path d="M250 117h0 3l5 5v3 1c1 0 3 3 3 4v2h1 3 0c-1 1-1 1-2 1l-20 2v-1h5l10-1-6-5-6 2c-1 0-1 0-2-1s-1-2-1-3 0-2 1-2h5l-1 1h0 2 0v-7-1z" class="V"></path><path d="M250 117h0 3l5 5v3 1c-2-1-3-1-5-1 1 0 3 0 4 1 1 0 2 3 3 5h-1c-2-1-4-2-5-3-2-1-3-2-4-3v-7-1z" class="W"></path><path d="M283 131c5-2 12-5 17-8 3-1 5-3 7-5 7-4 15-4 22-2l-5 1h1v1c1 0 1 1 1 2l1 1 1-1c1 1 1 1 1 2s0 1-1 2c-1 0-2 1-3 1v1h0c-1 1-1 2-2 3v1l1-1 1 3c-2 0-3 0-3 1 0 2 0 4-1 6v1l-1 1c-1 0-1 0-2 1-1-1-2-3-3-3v-1l-1-1v-2-2h-1l-1-3c-1-2-2-2-3-3s-2-1-3-1h0c-1-1-2-1-3-1l-1 1c-1 0-2 1-3 1l-3 3v-1c-3 2-7 3-10 2h-3z" class="C"></path><path d="M303 123l1 1v1h-1l-1 1c-1 0-2 1-3 1l-3 3v-1c2-2 4-5 7-6z" class="O"></path><g class="U"><path d="M310 119h4c2 1 3 2 4 4l-1 1h-2v1c-1-2-1-3-1-5h0c-1-1-3-1-4-1z"></path><path d="M304 123c2-2 4-3 6-4 1 0 3 0 4 1h0c0 2 0 3 1 5v2h-2c0 2 1 4 1 6h-1l-1-3c-1-2-2-2-3-3s-2-1-3-1h0c-1-1-2-1-3-1h1v-1l-1-1h1z"></path></g><path d="M310 119c1 0 3 0 4 1h0c0 2 0 3 1 5v2h-2v-1c-1-1-1-2-1-3 0-2-1-2-2-4z" class="u"></path><path d="M303 123h1c2 1 4 1 6 1 1 1 1 2 2 3h1 0c0 2 1 4 1 6h-1l-1-3c-1-2-2-2-3-3s-2-1-3-1h0c-1-1-2-1-3-1h1v-1l-1-1z" class="K"></path><path d="M318 123v-1c1-2 2-3 4-5 1 0 2 0 3 1 1 0 1 1 1 2l1 1 1-1c1 1 1 1 1 2s0 1-1 2c-1 0-2 1-3 1v1h0c-1 1-1 2-2 3v1l1-1 1 3c-2 0-3 0-3 1 0 2 0 4-1 6v1l-1 1c-1 0-1 0-2 1-1-1-2-3-3-3v-1l-1-1v-2-2c0-2-1-4-1-6h2v-2-1h2l1-1h0z" class="L"></path><path d="M319 135l2 1v3 1c-2 0-2 0-4-1-1-1-1-2-1-4h3z" class="C"></path><path d="M318 123h0v2c0 1 0 2-1 3l1 1v2c0 1 2 2 1 3h0-2c1 1 1 1 2 1h-3c0-4-1-7 1-11l1-1z" class="G"></path><path d="M323 125h1 1v1c-1 1-1 2-2 3v1l1-1 1 3c-2 0-3 0-3 1 0 2 0 4-1 6v-3l-2-1c-1 0-1 0-2-1h2 0c1-1-1-2-1-3v-2-1l3-2h0c1 0 1 0 2-1z" class="p"></path><path d="M323 125v2h0c-1 1-1 2-1 2l-1 1v-4h0c1 0 1 0 2-1z" class="Z"></path><path d="M321 126v4h-2c0 1-1 1-1 1v-2-1l3-2z" class="X"></path><path d="M318 123v-1c1-2 2-3 4-5 1 0 2 0 3 1 1 0 1 1 1 2l1 1 1-1c1 1 1 1 1 2s0 1-1 2c-1 0-2 1-3 1v1h0v-1h-1-1c-1 1-1 1-2 1h0l-3 2v1l-1-1c1-1 1-2 1-3v-2z" class="N"></path><path d="M318 125c2 0 7-2 8-3v-1c-1 1-2 1-3 0h0c1-1 2-1 3-1l1 1 1-1c1 1 1 1 1 2s0 1-1 2c-1 0-2 1-3 1v1h0v-1h-1-1c-1 1-1 1-2 1h0l-3 2v1l-1-1c1-1 1-2 1-3z" class="h"></path><path d="M328 120c1 1 1 1 1 2s0 1-1 2v-4z" class="q"></path><path d="M303 125c1 0 2 0 3 1h0c1 0 2 0 3 1s2 1 3 3l1 3h1v2 2l1 1v1c1 0 2 2 3 3h1c1 2 1 3 1 5h0c1 1 1 2 1 2l-1 1c-1 1-1 1-2 1h0c-1-1-1-2-2-2h0-1c-1-1-1-1-2-1-1 2-1 2-3 3l-2-1h0-1v-1c-1 0-2 1-3 1-1 1-1 1-2 1h-1l-3 4c-1-1-1-2-1-3h-2v-4 5h-1c0-8-1-16 2-23l3-3c1 0 2-1 3-1l1-1z" class="m"></path><path d="M304 133c1 0 1 0 2 1s1 2 0 3c0 1 0 1-1 2v1 1h0c-1 0-2 0-2-1 0 0 0-1 1-2v-1h0l-1 1c-1 0-2 0-3-1h0l2-2 2-2z" class="D"></path><path d="M304 133c1 0 1 0 2 1s1 2 0 3c0 1 0 1-1 2v-1-2-1c0-1 0-1-1-2z" class="C"></path><path d="M299 142c0-1 1-1 2-1l1-1h1c0 1 1 1 2 1h0l-2 1c0 1 1 2 0 2-1 2-3 5-5 6v-1c-1 1-1 2-1 3h-2v-4c-1-2 0-6 1-8 0 0 2 1 2 2s0 1 1 1v-1z" class="L"></path><path d="M299 142c0-1 1-1 2-1l1-1h1c0 1 1 1 2 1h0-2v1l-1 1c-2 2-3 4-5 6 0-2 1-4 2-5l1-1v-1h-1z" class="Z"></path><path d="M303 125c1 0 2 0 3 1h0c1 0 2 0 3 1s2 1 3 3l1 3h1v2c-1 0-1 0-2 1h0l-2-1h-2s-1 0-1-1h-1c-1-1-1-1-2-1l-2 2-2 2h-3c0-2 0-3 1-4 1-2 2-3 4-4v-3l1-1z" class="d"></path><path d="M303 125c1 0 2 0 3 1h0c-1 1-3 2-4 3v-3l1-1z" class="g"></path><path d="M297 137l1-1c1 0 2-1 2-2v-1h1c1 0 1 1 1 2l-2 2h-3z" class="K"></path><path d="M309 127c1 1 2 1 3 3l1 3h1v2c-1 0-1 0-2 1h0l-2-1h-2s-1 0-1-1v-4l-1-1c0-1 1-1 1-2h1 1z" class="W"></path><path d="M309 127c1 1 2 1 3 3l1 3h-2c-1-1-1-2-2-4 0-1 0-1-1-2h1z" class="a"></path><path d="M306 134h1c0 1 1 1 1 1h2l2 1h0c1-1 1-1 2-1v2l1 1v1c1 0 2 2 3 3h1c1 2 1 3 1 5h0c1 1 1 2 1 2l-1 1c-1 1-1 1-2 1h0c-1-1-1-2-2-2h0-1c-1-1-1-1-2-1-1 2-1 2-3 3l-2-1h0-1v-1c-1 0-2 1-3 1-1 1-1 1-2 1h-1l-3 4c-1-1-1-2-1-3s0-2 1-3v1c2-1 4-4 5-6 1 0 0-1 0-2l2-1h0 0v-1-1c1-1 1-1 1-2 1-1 1-2 0-3z" class="I"></path><path d="M313 143l1-1h1l1 1c1 1 2 2 2 3s0 2-1 3h-1-1c-1-1-1-1-2-1h1c-1-1-1-2-1-2s0-1 1-1v-1l-1-1z" class="H"></path><path d="M316 143c1 1 2 2 2 3h-1c-1 0-2 1-3 1l-1-1c1-1 1-1 2-1s1-1 1-2z" class="d"></path><path d="M313 143l1 1v1c-1 0-1 1-1 1s0 1 1 2h-1c-1 2-1 2-3 3l-2-1h0-1v-1l6-6z" class="J"></path><path d="M313 146s0 1 1 2h-1c-1 2-1 2-3 3l-2-1c1-1 3-3 5-4z" class="M"></path><path d="M306 134h1c0 1 1 1 1 1h2l2 1h0c1-1 1-1 2-1v2l1 1v1c-2 3-4 4-6 6v-1-1h-1 0l1-1c-1 0-1 0-2-1h-1-1v-1-1c1-1 1-1 1-2 1-1 1-2 0-3z" class="N"></path><path d="M306 134h1c0 1 1 1 1 1l1 1c1 0 2 1 2 2s0 1-1 2h-5v-1c1-1 1-1 1-2 1-1 1-2 0-3z" class="Y"></path><path d="M306 134h1c0 1 1 1 1 1l1 1v2h-1 0c-1-1-1-1-2-1 1-1 1-2 0-3z" class="f"></path><path d="M305 141h1 1c1 1 1 1 2 1l-1 1h0 1v1 1l-3 3-4 3h-1l-3 4c-1-1-1-2-1-3s0-2 1-3v1c2-1 4-4 5-6 1 0 0-1 0-2l2-1h0 0z" class="M"></path><path d="M303 147c1 0 2 0 2 1h1l-4 3h-1s0-1 1-2c0-1 1-1 1-2z" class="J"></path><path d="M308 143h1v1 1l-3 3h-1c0-1-1-1-2-1 0-1 0-1 1-1s3-2 4-3z" class="d"></path><path d="M375 111c1 0 5-1 6 0h2 11 3 15 6c-2 1-4 1-6 1 1 0 2 0 2 1h3c1 0 2 0 3-1v-1h5v1h1 6c-4 1-8 3-12 5v1l2-1 2 1s1 0 2 1c1-3 5-3 7-5 1-1 2-1 3-1v1c-1 0-3 1-4 2l1 2c-1 0-1 3-1 3h-1c-1 1-1 3-1 4-2 3-5 6-8 7-1 0-2 1-4 0h0c-1 0-1-1-2-1s-1-1-2-1c-2 1-3 1-5 2h-1 0-4c-4 0-7-1-11-3h0l-1 1c-1 1 0 3 0 5l1 1-1 1h-1l-1 1c-1 0-1 1-1 2 0 2-1 4 0 5l3 3-1 1 1 3h-2l-3-3-1 1c0 1 0 1 1 2h-1 0l-1-1-1 1c-2-1-3-2-4-2-1-1-2-1-3-2h0l-1 1h-1 0-2l-1-1-3-3c0 1 0 2-1 3-1 0-4-1-5-1-2 0-3 0-4 1h-1c0 1 1 2 1 3h-1c1 0 2 0 2 1h2c0 1 1 1 1 1-1 1-1 2-2 2h-2 0c-1-1-2-1-3-2h0c-1 1-1 1-1 2h-1c-1-2-1-4-1-6h-2-10c1 0 1 0 3 2 0 0 0 1-1 1l-5 3h-4c-1 0-2 1-3 0l-1-1v1l-1 1-2-1h0c-1-1-1-1-1-2 1-2 4-3 5-4v-1c-2-1-4-2-5-1-2 1-3 2-5 2 0 0 0-1-1-2h0c0-2 0-3-1-5h-1c1-1 1-1 2-1l1-1v-1c1-2 1-4 1-6 0-1 1-1 3-1v-1h1v1l1-1c0-1 1-2 1-2l2 2v2l1 2h0l1 1c1 0 2 1 3 1 2 0 4 0 5-1 2-2 2-3 3-6 0-1 1-2 1-4h0c3-5 6-9 11-12 2-1 4-2 7-3 4 0 9 1 13 0z" class="W"></path><path d="M327 131v1c1 1 2 0 2 2v2 1c-1-1-2-3-3-5l1-1z" class="Y"></path><path d="M328 129l2 2v2l1 2h0v1l-1-2h-1c0-2-1-1-2-2v-1c0-1 1-2 1-2z" class="F"></path><path d="M368 138c1 1 2 1 3 2l-2 5c0 1 0 2-1 3v-4c0-1 0-2 1-3h1c-1-1-2-2-2-3z" class="U"></path><path d="M360 125l1 1c2 1 3 2 4 4h1l2 3h-3c-1-1-1-2-1-3h-2v2-2c0-2-1-3-2-5z" class="J"></path><path d="M341 149c-3-1-5-1-8-2l13 1c3 0 7-1 10 0-1 1-2 1-3 1h-2-10z" class="g"></path><path d="M319 142c1 0 2 0 3 1 0 0 0 1 1 1v1c1 1 1 1 2 0v1c2 0 4 0 5 1l1 1c-2-1-4-2-5-1-2 1-3 2-5 2 0 0 0-1-1-2h0c0-2 0-3-1-5z" class="M"></path><path d="M320 147c1 1 1 1 2 1 0-1 1-2 1-2 1 0 2 0 3 1-2 1-3 2-5 2 0 0 0-1-1-2z" class="a"></path><path d="M414 113h3c1 0 2 0 3-1v-1h5v1c-5 1-9 3-12 5-1 1 0 1-1 1h-3l5-5z" class="B"></path><defs><linearGradient id="S" x1="395.082" y1="110.657" x2="399.476" y2="120.339" xlink:href="#B"><stop offset="0" stop-color="#1a1618"></stop><stop offset="1" stop-color="#272a29"></stop></linearGradient></defs><path fill="url(#S)" d="M383 111h11 3c0 2 0 3 2 4 0 1 0 0 1 1 0 0 1 0 1 1l3 2h-1l-1 1-8-6c-1 0-3-1-4-1-2-1-5-1-7-1v-1z"></path><path d="M344 126h0c0 3 1 10-2 12-2 1-5 2-8 2-1-1-3-2-5-3h0v-1-2h1l1 2v-1l1 1c1 0 2 1 3 1 2 0 4 0 5-1 2-2 2-3 3-6 0-1 1-2 1-4z" class="C"></path><path d="M397 111h15 6c-2 1-4 1-6 1 1 0 2 0 2 1l-5 5h3c-1 0-1 0-2 1v2h-1v1c-3 0-5-1-7-2l1-1h1l-3-2c0-1-1-1-1-1-1-1-1 0-1-1-2-1-2-2-2-4z" class="E"></path><path d="M412 112c1 0 2 0 2 1l-5 5h0 0c0-2 0-2 1-3v-1c0-1 1-1 2-2z" class="H"></path><path d="M404 119c2 0 2 0 3-1v-1h1v2c1 1 1 1 1 2v1c-3 0-5-1-7-2l1-1h1z" class="e"></path><path d="M433 114c1-1 2-1 3-1v1c-1 0-3 1-4 2l1 2c-1 0-1 3-1 3h-1c-1 1-1 3-1 4-2 3-5 6-8 7-1 0-2 1-4 0h0c-1 0-1-1-2-1s-1-1-2-1c1-1 2-1 3-2h0c-2 0-3 1-4 1l-1 1c-1 0-3 0-4-1-2 0-9-3-9-4 0 0 2 0 2 1 3 1 5 2 7 2h5c1 0 3-2 4-2s4 2 5 2c1-2 3-4 2-6l1-1h0-1l2-2c1-3 5-3 7-5z" class="C"></path><path d="M418 132c1-1 2-1 4-2s5-4 6-7c1-2 1-3 2-4v-1h0-1l3-2 1 2c-1 0-1 3-1 3h-1c-1 1-1 3-1 4-2 3-5 6-8 7-1 0-2 1-4 0z" class="H"></path><path d="M346 130c1 0 2-1 3-2 1-2 4-5 6-5 3 0 5 0 7 2l-1 1-1-1c1 2 2 3 2 5v2c-1 2-2 5-4 6-2 2-6 2-9 2-1-1-3-2-3-3-1-2-1-5 0-6v-1z" class="E"></path><path d="M354 128h5v1c-1 0-1 0-2 1h-1v-2h-2z" class="T"></path><path d="M357 130c1-1 1-1 2-1 1 1 1 1 1 3v1h-1c-1-1-1-2-2-3z" class="M"></path><path d="M356 130h1c1 1 1 2 2 3h1c-1 1-3 3-5 4 1-2 1-5 1-7z" class="H"></path><path d="M353 133l1-1c-1 0-1-1-2-1v-1c1-2 1-2 2-2h2v2c0 2 0 5-1 7h0c-1 0-2-1-3-1-1-1-1-1-1-2 1-1 1-1 2-1z" class="W"></path><path d="M346 130c1 0 2-1 3-2 1-2 4-5 6-5 3 0 5 0 7 2l-1 1-1-1c-2 0-5 0-7 2-1 1-1 2-1 3v2l1 1c-1 0-1 0-2 1 0 1 0 1 1 2 1 0 2 1 3 1h-6c-2-1-3-4-3-6v-1z" class="M"></path><path d="M346 130l1-4c2-4 6-7 9-8 8-3 17-4 26 0h-4v1c-1 0-2 0-2 1-2 0-3 2-4 3h0c0 1-1 3-1 4v2 1l-1-1c-2 1-2 0-4 1h-1c-1-2-2-3-4-4l1-1c-2-2-4-2-7-2-2 0-5 3-6 5-1 1-2 2-3 2z" class="B"></path><path d="M369 118c1 2 2 3 3 5 0 1-1 3-1 4v2 1l-1-1c0-3 0-5-2-7 1-1 1-3 1-4z" class="T"></path><path d="M368 122c0-1 0-1-1-1 0 1 0 2 1 3l1 3-9-7 6-3c1 0 2 0 3 1 0 1 0 3-1 4z" class="M"></path><path d="M382 118l2 1h3l2 1 1 1h0 2l2 1c0 1 1 1 2 1l1 1 2 1c0 1 7 4 9 4 1 1 3 1 4 1l1-1c1 0 2-1 4-1h0c-1 1-2 1-3 2-2 1-3 1-5 2h-1 0-4c-4 0-7-1-11-3h0l-1 1c-1 1 0 3 0 5l1 1-1 1h-1l-1 1c-1 0-1 1-1 2 0 2-1 4 0 5l3 3-1 1 1 3h-2l-3-3-1 1c0 1 0 1 1 2h-1 0l-1-1-1 1c-2-1-3-2-4-2-1-1-2-1-3-2h0l-1 1h-1 0-2l-1-1-3-3 2-5c-1-1-2-1-3-2v-3-2l-2-3c2-1 2 0 4-1l1 1v-1-2c0-1 1-3 1-4h0c1-1 2-3 4-3 0-1 1-1 2-1v-1h4z" class="D"></path><path d="M384 120l4 6s-1 0-2-1h-2l1-1-1-1c-1 0-2-1-3-1 1-1 2-1 3-2z" class="L"></path><path d="M381 122c1 0 2 1 3 1l1 1-1 1h2c1 1 2 1 2 1h1v3h1c0 1 1 1 0 2v1h-1c-2-3-4-5-8-6 0 1 0 1-1 2h0c-1-1-1-1-1-2l-1 1-1-2h0c1-1 2-2 4-3z" class="G"></path><path d="M381 122c1 0 2 1 3 1l1 1-1 1h-5v1l-1 1-1-2h0c1-1 2-2 4-3z" class="n"></path><path d="M384 119h3l2 1 1 1h0 2l2 1c0 1 1 1 2 1l1 1 2 1c0 1 7 4 9 4 1 1 3 1 4 1l1-1c1 0 2-1 4-1h0c-1 1-2 1-3 2-2 1-3 1-5 2h-1 0-4 1c0-1-16-8-21-13zm-5 7c0 1 0 1 1 2h0c1-1 1-1 1-2 4 1 6 3 8 6h1c0 2 0 3-1 4l-1 1h-3v1c-1 0-1 1-2 1 0 1-1 0-1 1v1l-2 1c-1 0-2-1-3-1l-1-1c-1-2-1-5-2-8 1-1 1-2 2-3l2-2 1-1z" class="M"></path><path d="M379 138v-3-1c0-1 0-1 1-2s2-1 4-1c3 1 1 3 2 6h2-3v1c-1 0-1 1-2 1 0 1-1 0-1 1-1-1-2-2-3-2z" class="G"></path><path d="M379 138c2-1 2-1 4 0h2c-1 0-1 1-2 1 0 1-1 0-1 1-1-1-2-2-3-2z" class="K"></path><path d="M382 133c1 0 2-1 3 0v1c0 1 0 1-1 2v1h0c-2-1-3-2-4-3 1-1 1-1 2-1z" class="W"></path><path d="M378 118c3 1 4 1 6 2-1 1-2 1-3 2-2 1-3 2-4 3h0l1 2-2 2c-1 1-1 2-2 3 1 3 1 6 2 8l1 1c1 0 2 1 3 1l2-1v-1l4 3c0-1 0-1-1-2l1-1h3c0 2-1 4 0 5l3 3-1 1 1 3h-2l-3-3-1 1c0 1 0 1 1 2h-1 0l-1-1-1 1c-2-1-3-2-4-2-1-1-2-1-3-2h0l-1 1h-1 0-2l-1-1-3-3 2-5c-1-1-2-1-3-2v-3-2l-2-3c2-1 2 0 4-1l1 1v-1-2c0-1 1-3 1-4h0c1-1 2-3 4-3 0-1 1-1 2-1v-1z" class="m"></path><path d="M371 136c-1 0-1 0-2-1h0l1-1c1-1 2 0 3 0l-1 1-1 1z" class="q"></path><path d="M372 137c2 1 2 2 4 4v2h0c-1-1-3-1-4-2s-1-2 0-4z" class="O"></path><path d="M372 145l1-2c1 0 2 1 3 1l1 1c1 1 1 2 1 3h-1 0l-1 1h-1 0-2l-1-1v-3z" class="H"></path><path d="M372 148v-3 1c2 1 4 0 5 2l-1 1h-1 0-2l-1-1z" class="n"></path><path d="M373 134h1v-2c1 3 1 6 2 8l1 1 1 2c3 3 6 6 8 9h0l-1-1-1 1c-2-1-3-2-4-2-1-1-2-1-3-2h1c0-1 0-2-1-3v-1s-1 0-1-1h0v-2c-2-2-2-3-4-4v-1h-1l1-1 1-1z" class="Y"></path><path d="M377 144h0c1 0 2 1 2 2 1 1 4 4 6 5l-1 1c-2-1-3-2-4-2-1-1-2-1-3-2h1c0-1 0-2-1-3v-1z" class="O"></path><path d="M378 118c3 1 4 1 6 2-1 1-2 1-3 2-2 1-3 2-4 3h0c-2 3-3 6-5 9v-5c0-1 1-2 0-4v-2c1-1 2-3 4-3 0-1 1-1 2-1v-1z" class="M"></path><path d="M382 140l4 3c0-1 0-1-1-2l1-1h3c0 2-1 4 0 5l3 3-1 1 1 3h-2l-3-3-1 1c0 1 0 1 1 2h-1c-2-3-5-6-8-9l-1-2c1 0 2 1 3 1l2-1v-1z" class="H"></path><path d="M390 152v-2h0l1-1 1 3h-2z" class="N"></path><path d="M377 141c1 0 2 1 3 1h2c0 1 0 1-1 2h0 0c-1 0-1-1-2-1h-1l-1-2z" class="n"></path><path d="M386 143c0-1 0-1-1-2l1-1h3c0 2-1 4 0 5l3 3-1 1c-1-2-3-4-5-6z" class="v"></path><path d="M301 213l3 2 1 1v1l-1-1h-2c2 1 3 2 4 4 0 2 0 4-1 6h-1v1h1l2 1 1 3h1v2c4 1 8 1 12 1 1 0 3-1 3 0l2 2-1 1-1 1c2 1 5 2 8 3l4 3 1-1h2c0 2 0 4 2 6h0v3l1 5v1c0 1-1 1-1 2-1 1 0 3-1 4v1l-1 1c1 1 1 2 2 2 1 1 0 1 1 1s0 0 1-1c0 0 1 0 2-1v1c1 2 1 3 1 5h0 2l1-4 1-2 1-2c1 0 1 1 2 1l1 1h2l1 1c-1 0-2 1-3 2-2 4-3 9-3 13v1 2c1 2 1 2 2 3-2 3-4 6-5 10 0 2-1 4-2 6 0 4-2 7-2 11v5c0-1-1-1-1-2h0v-1 2c-1 3-1 6-1 9h-1c0 2 0 5-1 7-1-2-2-4-2-6h1c0-1 0-1 1-2v-1c1-5 2-9 2-14-1-2-1-4-1-5v-2h-1s-1 0-1-1c-1-1-1-4 0-5-1-3-2-6-4-9l-2-2c-5-5-11-6-18-6v-2h-6l-3 1c-4 0-8 3-11 5h0c0-1 1-2 3-3v-2h-2v-1l2-2c1-1 0-2 0-4-1-1-1-1-3-2 1 0 1-1 2-1l-3-6c0-1 0-2-1-2v-1-9-3h1v-1l2-4c-1 0-1-1-1-2-1-2 0-6 0-9h-1v-7-4h1v-7h1v-1-1h1c1 0 1-1 2-1l2-1z" class="M"></path><path d="M315 273v-1-1l1-1 1 1-2 2h0z" class="N"></path><path d="M297 272c1 1 3 2 5 3-1 1-1 1-2 1s-1 0-2-1h0c-1-1-1-1-3-2 1 0 1-1 2-1z" class="F"></path><path d="M323 264c0 1 0 2 1 2l-3 3c-1 1-3 2-4 4l-1 1-1-1 2-2c2-2 4-5 6-7z" class="I"></path><path d="M300 249h0c3-5 7-6 12-7h0 1l-1 2h-2c-1 0-1 1-2 1-2 1-4 2-6 4-1 1-1 1-2 0z" class="B"></path><path d="M323 255c1-1 2-1 3-1v2c0 1 1 1 2 1h1c0 1 1 0 1 1v1 1h-7c0 1 0 2 1 3-1-1-1-1-2-1l-1-1c1-2 1-3 2-5h0v-1z" class="L"></path><path d="M329 257c0 1 1 0 1 1v1h-4v-1h2l1-1z" class="J"></path><path d="M324 248c1 1 2 1 3 1v1h-1v1c1 0 1 1 2 1l-2 2c-1 0-2 0-3 1v1h0l-1-1c-1 0-2 1-3 1v2h-1c-1 2-3 4-4 5s-2 1-3 1c0 1 0 1-1 0l1-1c0-1 1-1 1-2v-2h1l2 2v-1c1 0 1 0 1-1 1 0 1 0 1-1l1-1c1-2 2-3 4-4 1-1 1-2 1-3l1-2z" class="N"></path><path d="M324 248c1 1 2 1 3 1v1h-1v1c1 0 1 1 2 1l-2 2c-1 0-2 0-3 1v-5l1-2z" class="G"></path><path d="M299 252l1 2 1-1 2-1c0 1 0 1 1 2 1 0 1-1 2-1 0 2 1 5 1 7h1l-2 1h-5c-2-3-2-6-2-9z" class="E"></path><path d="M301 253l2-1c0 1 0 1 1 2 0 2 1 3 1 5-1-1-3-1-4-3v-3z" class="T"></path><path d="M330 260h1c1 1 1 2 2 3 1 0 1 1 1 2l-1 1 1 1c0 1-1 1-1 1-1-1-2-1-3-2h-4-2c-1 0-1-1-1-2 1 0 1-1 1-1-1-1-1-2-1-3h7z" class="E"></path><path d="M326 266c1 0 2-1 3-1 1-1 0-2 2-1h0c1 1 1 2 2 2l1 1c0 1-1 1-1 1-1-1-2-1-3-2h-4z" class="K"></path><path d="M294 228l1 1c0 1 1 3 3 4h0c2 3 5 4 8 5h2c-6 2-8 4-12 8-1 0-1-1-1-2-1-2 0-6 0-9h-1v-7z" class="P"></path><path d="M295 235c1 1 2 1 2 2l2 1c0 1 0 1-1 2s-2 3-3 4c-1-2 0-6 0-9z" class="s"></path><path d="M298 240h-1c0-1-1-1-1-2l1-1 2 1c0 1 0 1-1 2z" class="H"></path><path d="M321 243c2 0 5 2 8 3 1 1 1 2 1 3-1 1-1 1-2 1h-1v-1c-1 0-2 0-3-1s-2-2-4-2c-1 0-2 0-3 1-1 2-1 4-3 5h0c-1-2-2-3-4-4-1 0-2 0-3 1 0 1-1 3-1 4-1 0-1 1-2 1-1-1-1-1-1-2l-2 1-1 1-1-2 1-3c1 1 1 1 2 0 2-2 4-3 6-4 1 0 1-1 2-1h2s0 1 1 1 3-1 4-1c1-1 2-1 4-1z" class="e"></path><path d="M301 213l3 2 1 1v1l-1-1h-2c2 1 3 2 4 4 0 2 0 4-1 6h-1v1h1l2 1 1 3h1v2c-2 0-6 0-8-1-1-1-1-1-2-1s-1 0-2-1v-1l-1-2v3c0 1 3 2 2 3-2-1-3-3-3-4l-1-1v-4h1v-7h1v-1-1h1c1 0 1-1 2-1l2-1z" class="T"></path><path d="M296 217v2s-1 1-1 2c-1 2 0 5 0 8l-1-1v-4h1v-7h1z" class="B"></path><path d="M296 215h1c1 1 1 1 2 1h0l-3 3v-2-1-1z" class="R"></path><path d="M299 220c2 0 3 0 4 1 1 0 1 1 1 1 1 1 0 2 0 2v1l-2-2-1-2h-1 1 0-2v-1z" class="P"></path><path d="M301 213l3 2 1 1v1l-1-1h-2c0 1-2 0-3 0h0c-1 0-1 0-2-1 1 0 1-1 2-1l2-1z" class="B"></path><path d="M299 220v1h2 0-1 1l1 2s-1 1-1 2v1l1-1h2v1 1h1l2 1 1 3h1v2c-2 0-6 0-8-1-1-1-1-1-2-1-1-1-2-2-2-3-1-2-1-3-1-5 1-2 2-2 3-3z" class="E"></path><path d="M304 227h1l2 1 1 3c-1-1-2-1-2-1-1 0-1 0-2-1v-1-1z" class="V"></path><path d="M301 221l1 2s-1 1-1 2v1l1-1h2v1 1 1h-2c-1 0-2-1-3-2v-1c0-1 1-3 2-4z" class="W"></path><path d="M321 243l3-1c1 0 2 1 3 1h0v1l3 1-1-1h1c0 1 0 2 1 2h2c0 1 0 2 1 3h1c1-1 0-1 0-1 0-2 1-2 2-3l-1-1 1-1h2c0 2 0 4 2 6h0v3l1 5v1c0 1-1 1-1 2-1 1 0 3-1 4v1l-2-1h0l-1 1h0v-2l-1-1-3-1-2-1h-1v-1-1c0-1-1 0-1-1h-1c-1 0-2 0-2-1v-2l2-2c-1 0-1-1-2-1v-1h1 1c1 0 1 0 2-1 0-1 0-2-1-3-3-1-6-3-8-3z" class="U"></path><path d="M340 249h1v3c0 1-1 1-2 1h0c0-2 0-2 1-3v-1z" class="o"></path><path d="M336 244l1-1h2c0 2 0 4 2 6h0-1l-3-4-1-1z" class="K"></path><path d="M329 246c2 1 4 3 6 5l-1 1c-1-1-4-2-6-2 1 0 1 0 2-1 0-1 0-2-1-3z" class="F"></path><path d="M335 251l3 3c1 2 1 5 2 7v3 1l-2-1h0l-1 1h0v-2l-1-1h0c1-1 1-1 2-1-1-2 0-5-2-7l-2-2 1-1z" class="f"></path><path d="M336 262h0c1-1 1-1 2-1l1 2h-2l-1-1z" class="U"></path><path d="M327 250h1c2 0 5 1 6 2l2 2c2 2 1 5 2 7-1 0-1 0-2 1h0l-3-1 2-2 1-1c0-1 0-2-1-3h-2c-2 0-3-3-5-4v1c-1 0-1-1-2-1v-1h1z" class="H"></path><path d="M328 252v-1c2 1 3 4 5 4h2c1 1 1 2 1 3l-1 1-2 2-2-1h-1v-1-1c0-1-1 0-1-1h-1c-1 0-2 0-2-1v-2l2-2z" class="u"></path><path d="M328 257h0c2 0 2 0 4-1 0 0 1 1 1 2h-3c0-1-1 0-1-1h-1z" class="M"></path><path d="M333 258l2 1-2 2-2-1h-1v-1-1h3z" class="n"></path><path d="M326 266h4c1 1 2 1 3 2l2 3 1 2c1 0 1 1 1 1l1 3v6l1 7c1 2 1 5 1 8h0l-1 2c-1-3-2-6-4-9l-2-2c-5-5-11-6-18-6v-2h-6l-3 1c-4 0-8 3-11 5h0c0-1 1-2 3-3v-2h-2v-1l2-2c1-1 0-2 0-4h0c1 1 1 1 2 1s1 0 2-1l5 2 1 1c1-1 1-1 1-2v-1h1c1 1 3 0 4-1l1-1h0l1 1 1-1c1-2 3-3 4-4l3-3h2z" class="h"></path><path d="M329 277h-2l-1-1v-2-2s1 0 2 1v1l1 3z" class="D"></path><path d="M328 274l1 1 3 3v5l-3-6-1-3z" class="s"></path><path d="M321 269h3c1 0 1 0 1 1l-1 2v1c0 1 0 2 1 3h-1s-1 0-1-1c-2-1-4-2-6-2 1-2 3-3 4-4z" class="H"></path><path d="M302 275l5 2c-1 1-1 2-1 3h-2 0l-1-1c-2 0-2 1-3 1s-2 1-2 2h-2v-1l2-2c1-1 0-2 0-4h0c1 1 1 1 2 1s1 0 2-1z" class="B"></path><path d="M315 273l1 1 1-1c2 0 4 1 6 2-1 1-2 1-3 2v1l-1 1h-1c0-1-1-1-1-2-1 0-4 1-5 0l1-2s1 0 1 1v-2l1-1h0z" class="i"></path><path d="M315 273l1 1v1c-1 1-1 1-2 1v-2l1-1h0z" class="g"></path><path d="M326 266h4c1 1 2 1 3 2l2 3 1 2h-1 0l-1 1c-1 1-2 1-3 1h-1c1-2 2-3 2-5l-3-1c-2 0-3 0-4 1 0-1 0-1-1-1h-3l3-3h2z" class="n"></path><path d="M332 270h1c0 2 0 3 1 4-1 1-2 1-3 1h-1c1-2 2-3 2-5z" class="L"></path><path d="M326 266h4v1l1 1 2 2c-1-1-2-1-2-1h-2c-2 0-3 0-4 1 0-1 0-1-1-1h-3l3-3h2z" class="J"></path><path d="M298 284c7-4 17-6 25-4 5 2 7 3 10 7v2c-5-5-11-6-18-6v-2h-6l-3 1c-4 0-8 3-11 5h0c0-1 1-2 3-3z" class="T"></path><path d="M309 281c7-2 15 1 21 4l2 2h1v2c-5-5-11-6-18-6v-2h-6z" class="K"></path><path d="M335 273h0 1c1 0 1 1 1 1l1 3v6l1 7c1 2 1 5 1 8h0c-1-4-4-7-5-11-2-1-2-3-3-4v-5l-3-3h1 1c1 0 2 0 3-1l1-1z" class="M"></path><path d="M335 273h0 1c1 0 1 1 1 1l1 3v6c-1-2-2-3-3-4 1-2 1-4 1-5l-1-1z" class="H"></path><path d="M336 273c1 0 1 1 1 1l1 3h-2v-4z" class="n"></path><path d="M332 278c1 0 2 1 2 3h0c0 1 1 1 1 2 1 1 1 1 1 2-1 1-1 2-1 2-2-1-2-3-3-4v-5z" class="U"></path><path d="M331 260l2 1 3 1 1 1v2h0l1-1h0l2 1-1 1c1 1 1 2 2 2 1 1 0 1 1 1s0 0 1-1c0 0 1 0 2-1v1c1 2 1 3 1 5h0 2l1-4 1-2 1-2c1 0 1 1 2 1l1 1h2l1 1c-1 0-2 1-3 2-2 4-3 9-3 13v1 2c1 2 1 2 2 3-2 3-4 6-5 10 0 2-1 4-2 6 0 4-2 7-2 11v5c0-1-1-1-1-2h0v-1 2c-1 3-1 6-1 9h-1c0 2 0 5-1 7-1-2-2-4-2-6h1c0-1 0-1 1-2v-1c1-5 2-9 2-14-1-2-1-4-1-5v-2h-1s-1 0-1-1c-1-1-1-4 0-5l1-2h0c0-3 0-6-1-8l-1-7v-6l-1-3s0-1-1-1l-1-2-2-3s1 0 1-1l-1-1 1-1c0-1 0-2-1-2-1-1-1-2-2-3z" class="d"></path><path d="M344 316l-1 1c0-5 0-8 3-12h0c0 4-2 7-2 11z" class="H"></path><path d="M344 297h0c1-1 1-1 2-1 0 2 0 4-1 6h0v1l-1 3h-1v-3h0c1-2 1-4 1-6z" class="M"></path><path d="M342 304h1v16c-1 3-1 6-1 9h-1c0 2 0 5-1 7-1-2-2-4-2-6h1c0-1 0-1 1-2v-1c1-5 2-9 2-14-1-2-1-4-1-5v-1l1 1h0v-4z" class="f"></path><path d="M339 290v-1l1-1 1 4 1-1c0 1 1 2 1 4h0 1v-1c0 2-1 2 0 3 0 2 0 4-1 6h0v1h-1v4h0l-1-1v1-2h-1s-1 0-1-1c-1-1-1-4 0-5l1-2h0c0-3 0-6-1-8z" class="K"></path><path d="M341 292l1-1c0 1 1 2 1 4 0 1-1 2-1 3l-1-6z" class="J"></path><path d="M340 298h0v4c1 2 1 3 1 5v1-2h-1s-1 0-1-1c-1-1-1-4 0-5l1-2z" class="L"></path><path d="M343 295h0 1v-1c0 2-1 2 0 3 0 2 0 4-1 6h0v1h-1v-6c0-1 1-2 1-3z" class="o"></path><defs><linearGradient id="T" x1="345.139" y1="274.437" x2="354.194" y2="280.811" xlink:href="#B"><stop offset="0" stop-color="#2c2b2c"></stop><stop offset="1" stop-color="#434543"></stop></linearGradient></defs><path fill="url(#T)" d="M351 265c1 0 1 1 2 1l1 1h2l1 1c-1 0-2 1-3 2-2 4-3 9-3 13l-5 18v1h-1c1-2 1-4 1-6-1 0-1 0-2 1h0c-1-1 0-1 0-3 0-1 1-2 1-4l1-6c1-3 1-7 1-10l-1-1h2l1-4 1-2 1-2z"></path><path d="M346 284c1 0 1 0 1 1l-1 7v-1l-1-1 1-6z" class="J"></path><path d="M345 290l1 1v1 4c-1 0-1 0-2 1h0c-1-1 0-1 0-3 0-1 1-2 1-4z" class="H"></path><path d="M346 273h2l-1 12c0-1 0-1-1-1 1-3 1-7 1-10l-1-1z" class="O"></path><path d="M331 260l2 1 3 1 1 1v2h0l1-1h0l2 1-1 1c1 1 1 2 2 2 1 1 0 1 1 1s0 0 1-1c0 0 1 0 2-1v1c1 2 1 3 1 5h0l1 1c0 3 0 7-1 10l-1 6c0 2-1 3-1 4v1h-1 0c0-2-1-3-1-4l-1 1-1-4-1 1v1l-1-7v-6l-1-3s0-1-1-1l-1-2-2-3s1 0 1-1l-1-1 1-1c0-1 0-2-1-2-1-1-1-2-2-3z" class="B"></path><path d="M340 288c0-2 0-2 1-3l1 6-1 1-1-4h0z" class="O"></path><path d="M340 280l1-4h1l1 2v1c-1 1-1 2-2 3h0v2l-1-4z" class="C"></path><path d="M346 273h0l1 1h-1v1c-1 1-2 3-3 4v-1l-1-2c0-2 0-2 1-3 0 0 1 1 1 2l2-2z" class="L"></path><path d="M343 273s1 1 1 2c-1 1-1 1-1 3h0l-1-2c0-2 0-2 1-3z" class="X"></path><path d="M342 269c1 0 0 0 1-1 0 0 1 0 2-1v1c1 2 1 3 1 5l-2 2c0-1-1-2-1-2l-1-1v-3h0z" class="C"></path><path d="M337 265l1-1h0l2 1-1 1c1 1 1 2 2 2 1 1 0 1 1 1h0v3l1 1c-1 1-1 1-1 3h-1l-1 4v-4c0-1 0-2-1-3 0-1 0-1-1-1v-1l1-1-1-1c0-1 0-2-1-4z" class="Y"></path><path d="M337 265l1-1h0l2 1-1 1c1 1 1 2 2 2 1 1 0 1 1 1h0c-1 1-1 2-2 3l-1-2-1-1c0-1 0-2-1-4z" class="q"></path><path d="M338 272c1 0 1 0 1 1 1 1 1 2 1 3v4l1 4v1c-1 1-1 1-1 3h0l-1 1v1l-1-7v-6l-1-3 1 1h0v-3z" class="Q"></path><path d="M340 288l-1-5v-7h1v4l1 4v1c-1 1-1 1-1 3z" class="L"></path><path d="M331 260l2 1 3 1 1 1v2h0c1 2 1 3 1 4l1 1-1 1v1 3h0l-1-1s0-1-1-1l-1-2-2-3s1 0 1-1l-1-1 1-1c0-1 0-2-1-2-1-1-1-2-2-3z" class="Z"></path><path d="M334 265c1 0 1 0 1 1s1 1 1 2 0 2-1 3l-2-3s1 0 1-1l-1-1 1-1z" class="F"></path><path d="M336 268c1 1 1 1 2 1l1 1-1 1v1 3h0l-1-1s0-1-1-1l-1-2c1-1 1-2 1-3z" class="I"></path><path d="M364 553c2-2 3-5 5-7 0 0 1 1 1 2v4l1-1c3 3 6 6 9 6 4 1 6 1 9-1 4-2 5-5 6-9 0 0 1 0 1 1l-1 5h1v1c0 1-1 2-1 2-1 3-4 5-6 7h-4c2 2 5 3 7 6 0 0 4 7 5 7 4 9 6 20 5 30-1 6-3 13-6 19v2c-3 4-7 5-11 8 0 1-1 1-1 1l-5 1h-4c-5 0-11 1-15-1l-2 1h-1-4v-1h-1c-1 1-1 1-3 0l-1-4-3-4h-1v-1s0-1-1-1h-1l2-5-1-8c1-1 1-3 1-5h1v-3h-1c1-1 0-2 1-3v-3c1 1 2 2 2 3 1 2 1 3 2 5 2 0 3 2 4 3l1 2c0 1 0 1 1 2l1-2h0l1-2-1-1v-1h5 2c1-2 1-3 0-4h0l-2-1c-2 0-3-1-5-1-1 0-1-1-2-1-2-3-4-7-5-11v2c-1-3-2-6-2-9 0-8 4-16 9-22l8-8z" class="u"></path><path d="M347 617v6c-1-1-1-2-2-3l1-2 1-1h0z" class="n"></path><path d="M387 597c-1 0-1-1-1-1v-1c2 1 4 1 4 2s0 1-1 2h-1l-1-2z" class="I"></path><path d="M349 612v-2h1 0c0 2 1 4 2 6 1 1 2 3 3 4-1 0-1 0-2 1-2-3-3-6-4-9z" class="G"></path><path d="M349 607c2 0 3 2 4 3l1 2c0 1 0 1 1 2v1c-1 0-1-1-2-1l-1 2c-1-2-2-4-2-6h0-1v2c0-2-1-3 0-5z" class="O"></path><path d="M353 610l1 2c0 1 0 1 1 2v1c-1 0-1-1-2-1-1-1-1-1-1-2s1-1 1-1v-1z" class="l"></path><path d="M361 579l1-1s0-1 1-1v1 2l1 1v1 1 1 1l-1 1v-1s0-1-1-1c0 0-1 0-2 1 0-1-1-1-1-2-1-1-1-3-1-4h1c0 1 0 1 1 2h1v-2z" class="B"></path><path d="M345 599c1 1 2 2 2 3l-1 1c0 4-1 9 1 13v1h0l-1 1-1 2v-1-1h-1v3l-1-8c1-1 1-3 1-5h1v-3h-1c1-1 0-2 1-3v-3z" class="O"></path><path d="M344 608h1v11-1h-1v3l-1-8c1-1 1-3 1-5z" class="Z"></path><path d="M356 598c-1-2 0-3 0-4 1-1 3 1 4 1l1 1v1l1 1h0s1 0 1-1c2 3 4 4 7 5h1 1l-1 2-2-1c-2-1-3-1-4-1h0-2v2l-2-1c-2 0-3-1-5-1-1 0-1-1-2-1h4c-2-2-2-2-2-3z" class="B"></path><path d="M356 598c2 2 5 3 9 4h0-2v2l-2-1c-2 0-3-1-5-1-1 0-1-1-2-1h4c-2-2-2-2-2-3z" class="U"></path><path d="M344 621v-3h1v1 1c1 1 1 2 2 3 3 6 6 10 13 13l-2 1h-1-4v-1h-1c-1 1-1 1-3 0l-1-4-3-4h-1v-1s0-1-1-1h-1l2-5z" class="h"></path><path d="M343 626l1-1v-1c1 0 1 0 1 1h1c0 1 1 2 1 2 0 1 0 1-1 1h-1-1v-1s0-1-1-1z" class="P"></path><path d="M349 629c2 1 2 3 4 4l4 4h-4v-1c-1-2-3-5-4-7z" class="W"></path><path d="M347 627c1 1 1 2 2 2 1 2 3 5 4 7h-1c-1 1-1 1-3 0l-1-4-3-4h1c1 0 1 0 1-1z" class="D"></path><path d="M368 589l2 1h2l1 1h0c2 3 1 6 3 9l-1-1c0 1-1 2-1 2v2h0v2l-1 1-2-2 1-2h-1-1c-3-1-5-2-7-5 0-1 0-4 1-6 1-1 2-1 4-2z" class="U"></path><path d="M373 591h0c0 1 1 2 0 3h-2c0-1 0-1-1-2 1-1 2-1 3-1z" class="W"></path><path d="M368 589l2 1s0 1-1 2c1 3 3 4 4 5 0 2 1 4 0 5h-1c-2-1-1-4-3-5-1-1-2 1-3 1l-1-1c1-3 1-5 3-8z" class="u"></path><path d="M364 581c2-1 2-2 4-2l1 1v1h2c1 1 1 0 2 2 0 2-1 4 0 6l-1 1h-2l-2-1c-2 1-3 1-4 2-1 2-1 5-1 6s-1 1-1 1h0l-1-1v-1l-1-1c1-1 0-3 0-4v-1l-1-1v-1c1-1 1-2 1-3h0c1-1 2-1 2-1 1 0 1 1 1 1v1l1-1v-1-1-1-1z" class="C"></path><path d="M360 585c1-1 2-1 2-1 1 0 1 1 1 1v1c1 0 1 1 1 1 0 1-1 1-2 1-1 1-1 2-2 3v-1l-1-1v-1c1-1 1-2 1-3h0z" class="N"></path><path d="M364 581c2-1 2-2 4-2l1 1v1h2v1c-1 1-1 3-2 4h-4l-1-1v-1-1-1-1z" class="H"></path><path d="M364 581c2-1 2-2 4-2l1 1c-1 1-1 2-2 3l-3 1h0v-1-1-1z" class="D"></path><defs><linearGradient id="U" x1="375.44" y1="562.006" x2="373.061" y2="548.644" xlink:href="#B"><stop offset="0" stop-color="#171617"></stop><stop offset="1" stop-color="#333"></stop></linearGradient></defs><path fill="url(#U)" d="M364 553c2-2 3-5 5-7 0 0 1 1 1 2v4l1-1c3 3 6 6 9 6 4 1 6 1 9-1 4-2 5-5 6-9 0 0 1 0 1 1l-1 5h1v1c0 1-1 2-1 2-1 3-4 5-6 7h-4l-1-1c-7-3-15-4-22-2-5 3-9 7-11 13-2 5-3 12-2 17v2c-1-3-2-6-2-9 0-8 4-16 9-22l8-8z"></path><path d="M370 548v4c0 2 1 3 3 5h-8c1-2 2-3 2-5 1-1 2-4 3-4z" class="N"></path><path d="M395 553h1v1c0 1-1 2-1 2-1 3-4 5-6 7h-4l-1-1h0c5-2 9-5 11-9z" class="U"></path><path d="M371 565l4 2h1l-2 5h1c1-1 3-2 4-3 1 2 3 5 4 7 0 2 0 4 1 6 2 0 3-1 4-2 2 1 2 3 3 4 0 2 1 6 0 7s-3 2-4 3l-1 1v1s0 1 1 1c-1 2-2 3-4 3-2 1-5 1-7 0-2-3-1-6-3-9h0l-1-1 1-1c-1-2 0-4 0-6-1-2-1-1-2-2h-2v-1l-1-1c-2 0-2 1-4 2l-1-1v-2-1c-1 0-1 1-1 1l-1 1c-1-1-1-2-1-3 3-6 5-8 11-11z" class="B"></path><path d="M367 571c0-1 0-1 1-2s2-1 3-1c1 1 0 3-1 4-1 0-1 1-2 0l-1-1z" class="U"></path><path d="M381 585h1c1 0 2 0 3-1h2c1 1 1 2 1 4l-1 1-1 1-1-1c-3-1-5-2-8-1 1-1 0-1 0-2l1-1 1 1c1 1 1 0 2 0v-1z" class="a"></path><path d="M361 579c-1-1-1-2-1-3 3-6 5-8 11-11v2 1c-1 0-2 0-3 1s-1 1-1 2l-1-1c1-1 3-2 4-3h-1l-1 1c-2 0-3 2-4 3v1h0c1 0 2 1 2 2h1 1c1 1 2 0 3 1-1 0-3 0-4 1-1 0-1 1-2 2v1c-1-1-1-2 0-3v-2-1l-1 1-1 6v-2-1c-1 0-1 1-1 1l-1 1z" class="D"></path><path d="M373 589c2 1 2 0 4-1v3c-1 0-1 1-1 2l1 1 3-3c3 0 3 1 5 3h2l-1 1v1s0 1 1 1c-1 2-2 3-4 3-2 1-5 1-7 0-2-3-1-6-3-9h0l-1-1 1-1z" class="i"></path><path d="M377 594l3-3c3 0 3 1 5 3h0v2c-1 1-2 2-3 2-2 0-3 0-4-1s-1-2-1-3z" class="M"></path><path d="M363 580l1-6 1-1v1 2c-1 1-1 2 0 3v-1c1-1 1-2 2-2 1-1 3-1 4-1 4 1 6 2 10 2 0 3-1 5 0 8v1c-1 0-1 1-2 0l-1-1-1 1c0 1 1 1 0 2h0c-2 1-2 2-4 1-1-2 0-4 0-6-1-2-1-1-2-2h-2v-1l-1-1c-2 0-2 1-4 2l-1-1z" class="W"></path><path d="M380 612c2-1 5-1 7-2 3-2 8-8 9-12h1c-1 4-2 8-4 12-1 5-6 10-10 13-2 1-3 2-5 3-1 1-2 1-3 0-1 0-1-1-1-2h-1c0 1 1 2 1 2 0 1-1 1-1 2h-2 0l-1-1h-1c-3 1-7 0-10-2l-3-1-2-2-1-1c1-1 1-1 2-1-1-1-2-3-3-4l1-2c1 0 1 1 2 1v-1l1-2h0l1-2-1-1v-1h5 2c1-2 1-3 0-4h0v-2h2 0c1 0 2 0 4 1l2 1 2 2 4 4c1 1 2 2 3 2z" class="B"></path><path d="M377 610c1 1 2 2 3 2h-1c-1 1-1 1-2 1v-3z" class="D"></path><path d="M358 622c1 1 2 1 3 2h0c-1 1-1 1-2 1l-3-1 2-2h0z" class="S"></path><path d="M373 615c-1-1-2-1-2-2l1-1s2 0 2 1c1 0 0 0 1 1h4c-2 0-4 0-6 1z" class="Y"></path><path d="M361 608h2v1h1 1c1 1 1 1 1 3h-1c-2 0-1 0-2 1h-3l1 2-3-1c-1-1-1-2-2-2h0l1-2-1-1v-1h5z" class="U"></path><path d="M357 610c1 0 2 1 4 1l4 1c-2 0-1 0-2 1h-3l1 2-3-1c-1-1-1-2-2-2h0l1-2z" class="F"></path><path d="M357 610c1 0 2 1 4 1-1 0-1 0-1 1l-1 1-1-1v2c-1-1-1-2-2-2h0l1-2z" class="h"></path><path d="M365 602c1 0 2 0 4 1l2 1v3c-1 1-1 3-2 4h-1c-1 1-1 1-2 1 0-2 0-2-1-3h-1-1v-1c1-2 1-3 0-4h0v-2h2 0z" class="W"></path><path d="M365 602c1 0 2 0 4 1h-1c0 1 0 2-1 2h0c-1-1-1-2-2-2v-1h0z" class="J"></path><path d="M368 611h0c0-1 0-2-1-3v-1h2 2c-1 1-1 3-2 4h-1z" class="T"></path><path d="M356 612c1 0 1 1 2 2l3 1c2 0 2 0 3 1v1c-1 1-3 2-3 3-1 1-2 1-3 2h0l-2 2-2-2-1-1c1-1 1-1 2-1-1-1-2-3-3-4l1-2c1 0 1 1 2 1v-1l1-2z" class="M"></path><path d="M353 621c1-1 1-1 2-1l1 1 2 1-2 2-2-2-1-1z" class="C"></path><path d="M353 621c1-1 1-1 2-1l1 1h-1l-1 1-1-1z" class="F"></path><path d="M379 614h0c1 1 3 0 4 0l1 1c0 2-1 4-2 6-2 1-3 2-5 3-1-2-1-3-3-4-1 0-2 1-3 1-1 1-2 3-3 3-2 1-5 1-7 0h0c3-1 4-3 6-5 0-1 1-2 1-3 1 0 3-1 5-1 2-1 4-1 6-1z" class="M"></path><path d="M368 624v-2h0c2 0 2 0 3-1-1 1-2 3-3 3z" class="U"></path><path d="M115 111c2-1 3-1 5 0l29-1h5c1 1 1 1 2 1h-1 0v1h2c0 1 0 1 1 2l1-1c7 2 12 4 16 11 1 1 1 2 1 3 0 5 0 10-3 13l-2 3c-1 2-3 3-5 4l2 2-3 1 1 1h1 0c-2 2-4 2-6 3-1 0-2 0-3 1v-1h-1l-1-1h-1v-1h0l-1 1c0 1-1 1-1 2-3-2-5-2-7-3-1-1-3-2-4-2h-2v-1c0-1 0-2-1-3v1c-1 1-1 2-2 2 0 1-2 0-3 0 0 1 1 2 1 3v1c0 1 0 1-1 2h0v1 3c0 1 0 2-1 3v1h0v-2l-1-1c-1 2-1 3-2 5l-1-1v-1c0-1 1-1 1-2h0l-8 5c-1 1-3 1-4 2-1 0-1 0-2-1v-1c-1 0-2 0-3 1s-1 1-3 2c0 0-1 0-2-1-1 0-1 0-2 1-2 0-6-1-9-2h-1v-1l-7-3h-2c-1 0-2-1-3-1-3-4-3-9-4-13v-10l1 1h0c1-2 1-4 2-7h-1v-5c2-2 3-5 5-7h1c1 1 2 1 3 2 2-2 4-5 7-6 2-2 4-2 5-3v-2-1h2 0 10z" class="u"></path><path d="M125 124h0c1-1 1-2 3-3 1 0 3 0 4 1-2 0-3 1-5 2h-2 0z" class="d"></path><path d="M165 141c-1-1-2-1-2-2l-1-2h2c1 0 2 1 3 2-1 1-2 1-2 2z" class="G"></path><path d="M135 128c1-1 2-3 4-3h0v2 1h1l-1 1h-1v1h-2-1v-2z" class="C"></path><path d="M142 133h2 0 3 1l1 1h0c0 1-1 2-2 2h0-1-2c0-1-1-2-2-3z" class="U"></path><path d="M116 117l13-2 1 1-2 1c-3 0-5 1-7 2l-1-1h-1c-1 0-2 0-3-1z" class="D"></path><path d="M132 122c2-1 2-1 3 0 0 1-4 3-5 4v-1c-1 1-3 0-4 0l1-1c2-1 3-2 5-2z" class="G"></path><path d="M153 126h2c1 1 2 2 4 3 1 1 2 1 3 2h-1c0 1 0 1-1 1-3-1-6-4-7-6z" class="D"></path><path d="M116 118v-1c1 1 2 1 3 1h1l1 1c-1 1-3 3-5 4v-1c-1-1-1-1-2 0v-1l1-2h1v-1z" class="Y"></path><path d="M116 118v-1c1 1 2 1 3 1l-2 2c0-1 0-1-1-1v-1z" class="E"></path><path d="M151 141v-1-2c1-1 1-1 2-1s2 1 3 1 1 0 2-1h1c0 1 1 2 0 3s-3 0-5 0h0c-1 0-1 0-1-1l-1 1v1h-1z" class="G"></path><path d="M142 126c2-1 7-1 9 0h1v1 1c-1 0-2 1-3 2v-1c-2-1-4-1-6-1-1-1-1-1-1-2z" class="I"></path><path d="M110 148l2-2c1 0 0 0 1 1-1 1-1 1-2 1v1h3c1 0 2 1 2 1l2 1-1 3h-3c-1-1-2-3-3-5l-1-1z" class="N"></path><path d="M121 119c2-1 4-2 7-2-1 0-1 1-2 1v1l-2 2c-1 1-2 0-3 1 0 1-1 1-2 2h-3v-1c2-1 4-3 5-4z" class="a"></path><path d="M165 141c0-1 1-1 2-2 1 1 1 3 1 4s-2 2-3 3h-5-5c1-1 2-1 2-1v-3l5 1c1 0 2-1 3-2z" class="S"></path><path d="M109 132h2c-2 5-3 10-1 15v1l1 1h-3l-1 1c1 1 1 1 2 1l-1 1v1h-1v2h-1-1l1-1c0-1 0-2-1-3l-1-1h-1c2-1 3-2 4-4 1-1 1-2 0-4v-1-4c0-1-1-1-1-2l1-1 2-2z" class="F"></path><path d="M109 151c-1 0-1 0-2-1l1-1h3c1 2 2 4 3 5h3l1-3v-1h2l1 1v1 2c0 1-1 1-1 1-1 0-1 1-2 1h-1-2-1c-1 0-3 1-4 0v-1c0-1-1-2-2-2v-1l1-1z" class="h"></path><path d="M109 151l2 2c0 1 0 1-1 2 0-1-1-2-2-2v-1l1-1z" class="C"></path><path d="M118 150h2l1 1v1 2c0 1-1 1-1 1v-1-2c-1 0-1 0-2 1 0 0-1 0-1 1l1-3v-1z" class="K"></path><path d="M108 153c1 0 2 1 2 2v1c1 1 3 0 4 0h1c-1 2-1 3-3 5h-3-4-1-1c1-2 4-4 6-6h-1c-1 0-1 1-2 1v-1h1v-2h1z" class="k"></path><path d="M114 156h1c-1 2-1 3-3 5h-3-4s1-1 2-1l1 1c2 0 5-4 6-5h0z" class="J"></path><path d="M149 110h5c1 1 1 1 2 1h-1 0v1h-4c-12 0-23-1-35 1-4 1-7 2-11 2 1 0 2-1 4-1l11-3 29-1z" class="P"></path><path d="M149 110h5c1 1 1 1 2 1h-1 0v1h-4c0-1-3 0-3-1l1-1z" class="C"></path><path d="M126 125c1 0 3 1 4 0v1l-1 2c1 0 2-2 4-2 0 1 1 2 1 2h1v2 1h0 0l-1-1h-2c-2 1-3 2-4 3h-1-2-1 0v-3c0-2 1-3 2-5z" class="G"></path><path d="M125 130c1-1 1-1 2-1l1 1v1h-1l-2-1z" class="h"></path><path d="M124 130h1l2 1v1 1h-2-1 0v-3z" class="E"></path><path d="M126 125c1 0 3 1 4 0v1l-1 2c-1 0-1 0-2 1-1 0-1 0-2 1h-1c0-2 1-3 2-5z" class="D"></path><path d="M125 124h2l-1 1c-1 2-2 3-2 5v3h0 1 2 1c-1 2 0 4 1 6v1h1l-1 1v2c1 1 2 1 2 1h0l-2 1v1l1 1-1 1h0c-1-2-2-4-3-5 0-1 0-2-1-2l-2-2h0-1c0-1-1-2-2-2v-4-1c1-3 2-5 5-8h0z" class="L"></path><path d="M124 130v3h0 1v1c0 1 0 1-1 2v-1c-1-2-1-3 0-5z" class="D"></path><path d="M126 138l3 3v2c1 1 2 1 2 1h0l-2 1c-1-3-3-4-4-6l1-1z" class="S"></path><path d="M125 133h2 1c-1 2 0 4 1 6v1h1l-1 1-3-3-1 1-1-3c1-1 1-1 1-2v-1z" class="K"></path><path d="M125 134l2 1h0c-1 1-1 1-1 3l-1 1-1-3c1-1 1-1 1-2z" class="E"></path><path d="M125 124h0v1l-1 1c-2 2-3 6-2 8 0 2 1 3 1 5h-1c0-1-1-2-2-2v-4-1c1-3 2-5 5-8z" class="Y"></path><path d="M140 128v-3h0 1l1 1c0 1 0 1 1 2 2 0 4 0 6 1v1c-2 1-3 2-5 3h-2c1 1 2 2 2 3h2 1l-2 2c1 1 1 2 2 2 1 1 2 1 4 1h0 1v-1l1-1c0 1 0 1 1 1 0 0 0 1 1 2h2v3s-1 0-2 1c-2-1-5-2-8-3-1-2-3-2-4-3-2-1-3-4-4-5 1-1 1-2 1-3l-2-2v-1h1l1-1z" class="R"></path><path d="M142 133h0l-1-1 1-1h0c1 0 1 0 1-1h1 3 1 1c-2 1-3 2-5 3h-2z" class="I"></path><path d="M120 137c1 0 2 1 2 2h1 0l2 2c1 0 1 1 1 2 1 1 2 3 3 5h0v2 1h-3-1-1-2c-1-1-1-2-2-3v2h-2v-7-1h-2v-2c0-1 0-1 1-2s1-1 2-1l1 1v-1z" class="Z"></path><path d="M122 145v-1c1-1 1 0 3-1 1 0 1 1 1 2h-1l-2 1v2l-1-3z" class="O"></path><path d="M117 138h1v1c1 1 2 1 3 1-1 1-1 1-2 1l-1-1c0 1-1 1-2 2v-2c0-1 0-1 1-2z" class="G"></path><path d="M120 137c1 0 2 1 2 2l2 2h-1 0c-1 0-1-1-2-1s-2 0-3-1v-1h-1c1-1 1-1 2-1l1 1v-1z" class="H"></path><path d="M118 143c2 1 2 2 3 2h1l1 3v1l-1-1-1-1h-1v1 2h-2v-7z" class="k"></path><path d="M126 145l1 3c0 1 0 1-1 3h-1-1-2c-1-1-1-2-2-3v-1h1l1 1 1 1v-1-2l2-1h1z" class="H"></path><path d="M126 145l1 3v1h-1c-1 0-1-1-2-2l1-2h1z" class="N"></path><path d="M135 130h1 2l2 2c0 1 0 2-1 3 1 1 2 4 4 5 1 1 3 1 4 3-1 0-1 0-2-1-1 0-2-1-3-1 0 1-1 1-1 1-1-1-1-1-3-1-1 0-1 0-1 1s1 2 2 3h2l1-2 1 1-1 1v5h-2v-1c0-1 0-2-1-3v1c-1 1-1 2-2 2 0 1-2 0-3 0 0-2-1-2-1-3l-1-1-1-1h0s-1 0-2-1v-2l1-1h-1v-1c-1-2-2-4-1-6 1-1 2-2 4-3h2l1 1h0 0v-1z" class="W"></path><path d="M136 130h2l2 2c0 1 0 2-1 3h0c-1-2-2-3-3-5z" class="G"></path><path d="M134 134h1c1 2 0 4 0 6h-1-1v-1-1h1c0-1 0-1-1-2l1-2z" class="C"></path><path d="M133 136c1 1 1 1 1 2h-1v1 1h1-1v1c0 1-1 2-2 3 0 0-1 0-2-1v-2l1-1c0-2 1-3 3-4z" class="D"></path><path d="M128 133c1-1 2-2 4-3l-1 2c1 1 2 1 3 2l-1 2c-2 1-3 2-3 4h-1v-1c-1-2-2-4-1-6z" class="H"></path><path d="M150 122c-5-4-9-5-15-6h-3v-1c1-1 3-1 5-1 7 0 15 0 21 3h1l-5 2v2c2 1 6 3 8 3 0 1 1 0 1 0 2-1 1-3 2-5 2 3 4 6 4 10 0 1-1 1-1 2h-1c-2 1-4 3-6 3 1-1 1-2 1-3-1-1-2-1-3-2-2-1-3-2-4-3h-2c-1-2-2-3-3-4z" class="j"></path><path d="M150 122h3c1 1 1 2 2 4h-2c-1-2-2-3-3-4z" class="B"></path><path d="M142 150v-5l1-1-1-1-1 2h-2c-1-1-2-2-2-3s0-1 1-1c2 0 2 0 3 1 0 0 1 0 1-1 1 0 2 1 3 1 1 1 1 1 2 1l8 3h5 5c1-1 3-2 3-3h3c-1 2-3 3-5 4l2 2-3 1 1 1h1 0c-2 2-4 2-6 3-1 0-2 0-3 1v-1h-1l-1-1h-1v-1h0l-1 1c0 1-1 1-1 2-3-2-5-2-7-3-1-1-3-2-4-2z" class="u"></path><path d="M168 143h3c-1 2-3 3-5 4-1 1-2 1-3 1h0c1 0 0 0 1-1h1 1c0-1 1-1 1-1h0c-3 1-4 1-7 0h5c1-1 3-2 3-3z" class="U"></path><path d="M163 148c1 0 2 0 3-1l2 2-3 1 1 1h1 0c-2 2-4 2-6 3-1 0-2 0-3 1v-1h-1l-1-1h-1v-1h0c1-1 6-3 8-3v-1z" class="w"></path><path d="M163 148c1 0 2 0 3-1l2 2-3 1-2 1s1-1 1-2h-1v-1z" class="R"></path><path d="M165 150l1 1h1 0c-2 2-4 2-6 3-1 0-2 0-3 1v-1h-1l-1-1h-1v-1h2l6-1 2-1z" class="d"></path><path d="M155 152h2l3 1c-1 0-1 1-2 1h-1l-1-1h-1v-1z" class="a"></path><path d="M131 144l1 1 1 1c0 1 1 1 1 3 0 1 1 2 1 3v1c0 1 0 1-1 2h0v1 3c0 1 0 2-1 3v1h0v-2l-1-1c-1 2-1 3-2 5l-1-1v-1c0-1 1-1 1-2h0l-8 5c-1 1-3 1-4 2-1 0-1 0-2-1v-1c-1 0-2 0-3 1s-1 1-3 2c0 0-1 0-2-1-1 0-1 0-2 1-2 0-6-1-9-2 3-1 5-1 7-1l9-2v-1-1h-1v-1c2-2 2-3 3-5h2 1c1 0 1-1 2-1 0 0 1 0 1-1v-2-1l-1-1v-2c1 1 1 2 2 3h2 1 1 3v-1-2l1-1-1-1v-1l2-1z" class="D"></path><path d="M118 158c1-1 2-1 2-2 2 1 2 1 3 2v1l-1 1h0c0 2 0 3-1 4h1v2c-1 1-3 1-4 2-1 0-1 0-2-1v-1h4v-1l-1-1v-2l2-2c-1-1-1-1-2-1 0 0 0-1-1-1z" class="K"></path><path d="M115 156h2 1v2c1 0 1 1 1 1 1 0 1 0 2 1l-2 2c-2 1-4 1-6 2v-1-1h-1v-1c2-2 2-3 3-5z" class="W"></path><path d="M120 148c1 1 1 2 2 3h2 1v2h1c0-1 1-1 1-1 0 2-1 3-1 4-1 1-1 2-2 4h0v-1h-1v-1c-1-1-1-1-3-2 0 1-1 1-2 2v-2c1 0 1-1 2-1 0 0 1 0 1-1v-2-1l-1-1v-2z" class="q"></path><path d="M121 151l1 2c1 0 1-1 2-1v3h-1l-1 1c2 1 2 1 2 3h0-1v-1c-1-1-1-1-3-2 0 1-1 1-2 2v-2c1 0 1-1 2-1 0 0 1 0 1-1v-2-1z" class="G"></path><path d="M126 156c2 0 2-1 4 0 0 0 0 1 1 1v1h1v2c-1 2-1 3-2 5l-1-1v-1c0-1 1-1 1-2h0l-8 5v-2h-1c1-1 1-2 1-4h0l1-1h1v1h0c1-2 1-3 2-4z" class="I"></path><path d="M123 159h1v1c0 2 0 3-1 4h-1-1c1-1 1-2 1-4h0l1-1z" class="i"></path><path d="M126 156c2 0 2-1 4 0 0 0 0 1 1 1v1c-1 1-1 2-3 3h-3l-1-1c1-2 1-3 2-4z" class="M"></path><path d="M131 144l1 1 1 1c0 1 1 1 1 3 0 1 1 2 1 3v1c0 1 0 1-1 2h0v1 3c0 1 0 2-1 3v1h0v-2l-1-1v-2h-1v-1c-1 0-1-1-1-1-2-1-2 0-4 0 0-1 1-2 1-4 0 0-1 0-1 1h-1v-2h1 3v-1-2l1-1-1-1v-1l2-1z" class="J"></path><path d="M131 144l1 1 1 1c0 1 1 1 1 3 0 1 1 2 1 3v1c0 1 0 1-1 2h0c-1-1-1-2-1-3s-1-2-1-3c-1-1-1-1-1-2h-1l-1-1v-1l2-1z" class="C"></path><path d="M131 144l1 1v1c-1 1-2 0-3 0v-1l2-1z" class="E"></path><path d="M129 150c2 3 3 5 3 8h-1v-1c-1 0-1-1-1-1-2-1-2 0-4 0 0-1 1-2 1-4 0 0-1 0-1 1h-1v-2h1 3v-1z" class="h"></path><path d="M127 152h2c1 2 1 3 2 5-1 0-1-1-1-1-2-1-2 0-4 0 0-1 1-2 1-4z" class="a"></path><path d="M115 111c2-1 3-1 5 0l-11 3c-2 0-3 1-4 1l-2 2c-3 1-6 2-8 5v1c3 0 5-2 8-1 4-2 8-3 13-4v1h-1l-1 2v1c1-1 1-1 2 0v1 1c-2 1-3 3-4 5v1l-1 2h-2l-2 2-1 1c0 1 1 1 1 2v4 1c1 2 1 3 0 4-1 2-2 3-4 4h1l1 1c1 1 1 2 1 3l-1 1h1v1c1 0 1-1 2-1h1c-2 2-5 4-6 6h1 1 4 3v1h1v1 1l-9 2c-2 0-4 0-7 1h-1v-1l-7-3h-2c-1 0-2-1-3-1-3-4-3-9-4-13v-10l1 1h0c1-2 1-4 2-7h-1v-5c2-2 3-5 5-7h1c1 1 2 1 3 2 2-2 4-5 7-6 2-2 4-2 5-3v-2-1h2 0 10z" class="N"></path><path d="M105 111h10l-12 3v-2-1h2 0z" class="c"></path><defs><linearGradient id="V" x1="81.624" y1="127.64" x2="90.131" y2="126.613" xlink:href="#B"><stop offset="0" stop-color="#141314"></stop><stop offset="1" stop-color="#282b29"></stop></linearGradient></defs><path fill="url(#V)" d="M87 121h1c1 1 2 1 3 2-4 3-6 6-8 10h-1v-5c2-2 3-5 5-7z"></path><path d="M80 149v-10l1 1h0c0 7 0 15 6 21l2 2h-2c-1 0-2-1-3-1-3-4-3-9-4-13z" class="i"></path><path d="M85 138c0 1 1 1 2 1v3c3 3 4 3 8 5h0 1l1 1c-1 0-2 0-3 1l-1 1h1l4 1v-1 3h-4c-1-1-1-1-2-1-2 0-2 0-4-2h-1c0 1-1 1-2 1 2 5 6 8 8 12-3-1-6-5-7-8-3-5-3-11-1-17z" class="G"></path><path d="M85 151v-8h1c0 1 1 2 2 3h0c-1 1-1 2-2 3h0l1 1c0 1-1 1-2 1z" class="H"></path><path d="M88 146l6 3-1 1h1l4 1v-1 3h-4c-1-1-1-1-2-1-2 0-2 0-4-2h-1l-1-1h0c1-1 1-2 2-3z" class="N"></path><path d="M87 150h1c2 2 2 2 4 2 1 0 1 0 2 1h4c2-1 4-1 5-3h1l1 1c1 1 1 2 1 3l-1 1h1v1c1 0 1-1 2-1h1c-2 2-5 4-6 6h1 1 4 3v1h1v1 1l-9 2c-2 0-4 0-7 1h-1v-1c-1 0-1-1-1-1 0-1-1-2-2-2-2-4-6-7-8-12 1 0 2 0 2-1z" class="E"></path><path d="M96 160h0l-2-3c-1-1 0-1 0-2v-1l1 1c1 2 2 3 2 5h0 1l1-1v2h-2s-1 0-1-1z" class="q"></path><path d="M99 159c-1-1-1-2-1-3h0c1 0 1 1 2 1 0 0 4-2 4-3l1 1h1v1c1 0 1-1 2-1h1c-2 2-5 4-6 6h1c-1 1-2 0-3 1h-1v-1h0c-1 1-2 0-3 0h2v-2z" class="H"></path><defs><linearGradient id="W" x1="95.055" y1="146.996" x2="102.689" y2="166.598" xlink:href="#B"><stop offset="0" stop-color="#c8c7c7"></stop><stop offset="1" stop-color="#f1f0f1"></stop></linearGradient></defs><path fill="url(#W)" d="M87 150h1c0 1 0 2 1 3 1 3 4 6 7 7 0 1 1 1 1 1 1 0 2 1 3 0h0v1h1c1-1 2 0 3-1h1 4 3v1h1v1 1l-9 2c-2 0-4 0-7 1h-1v-1c-1 0-1-1-1-1 0-1-1-2-2-2-2-4-6-7-8-12 1 0 2 0 2-1z"></path><path d="M103 122c4-2 8-3 13-4v1h-1l-1 2v1c1-1 1-1 2 0v1 1c-2 1-3 3-4 5v1l-1 2h-2l-2 2-1 1c0 1 1 1 1 2v4 1c1 2 1 3 0 4-1 2-2 3-4 4-1 2-3 2-5 3v-3 1l-4-1h-1l1-1c1-1 2-1 3-1l-1-1h-1 0c-4-2-5-2-8-5v-3c-1 0-2 0-2-1 1-3 3-5 4-7s3-4 4-5l2 1c2-3 5-4 8-5z" class="C"></path><path d="M104 126s0 1 1 1v2c-1 0-1-1-2-1l1-2z" class="i"></path><path d="M103 130c1 0 2 1 2 2v1c-1 1-1 0-1 1h-1v-4z" class="T"></path><path d="M107 134h-1v-3c1 0 2-1 3 0h1l-1 1-2 2z" class="V"></path><path d="M96 142h2v2c0 2-1 2-2 3h-1c0-1-1-1 0-2l1-3z" class="M"></path><path d="M87 142c0-2 0-4 1-5l1-2v1 3h0c1 2 2 3 3 4 1 0 3-1 4-1l-1 3c-1 1 0 1 0 2h0c-4-2-5-2-8-5z" class="H"></path><path d="M89 131c1-2 3-4 4-5l2 1c-4 3-6 7-6 12h0v-3-1l-1 2c-1 1-1 3-1 5v-3c-1 0-2 0-2-1 1-3 3-5 4-7z" class="J"></path><path d="M89 131c0 2-2 4-2 6v2c-1 0-2 0-2-1 1-3 3-5 4-7z" class="D"></path><path d="M114 121v1c1-1 1-1 2 0v1 1c-2 1-3 3-4 5v1c-2-2-4-4-6-5l8-4z" class="N"></path><path d="M114 122c1-1 1-1 2 0v1 1c-2 1-3 3-4 5v-1c-1-3 1-5 2-6z" class="I"></path><path d="M100 141l-1-1c-1-1-2-1-3-1h-1c-1 1-2 1-3 0s0-1 0-2l3-6c1-1 2-1 4-1h1c1-1 1-1 3 0v4h1 0c1 2 2 3 2 5l1 2v1c1 2 1 3 0 4-1 2-2 3-4 4-1 2-3 2-5 3v-3 1l-4-1h-1l1-1c1-1 2-1 3-1l2-1c1-2 1-3 1-6z" class="W"></path><path d="M102 138h1v3h-1c-1-1-1-2 0-3z" class="J"></path><path d="M104 134c1 2 2 3 2 5l-1-1c-1-1-2-2-1-4z" class="T"></path><path d="M100 141c1 2 2 3 2 6h-1-2c1-2 1-3 1-6z" class="H"></path><path d="M107 142c1 2 1 3 0 4-1 2-2 3-4 4-1 2-3 2-5 3v-3h1 1v1h1c1-1 1-1 1-2 2-1 2-2 3-3l2-1v-3z" class="d"></path><path d="M292 552c-1-1 0-3 0-4l1 2 1-2c1 4 3 7 6 9 4 1 9 1 12-1 3-1 5-2 6-5 1-1 2-1 2-1 2 0 3 1 3 2l2 1c2 2 4 4 6 7 1 0 2 1 2 2 4 4 7 9 9 14l3 12c0 1 0 2-1 3 0 2 2 6 1 8v3c-1 1 0 2-1 3h1v3h-1c0 2 0 4-1 5l1 8-2 5-1 1c0 1 0 1-1 2-1 0-2 2-4 3v-1c-7 7-16 7-26 6-1 0-5 0-6-1h-6-7l-1-1v-1h-1l-2 1v-1l1-1c-1-2-4-2-5-4-1 0-1-1-1-2s3-4 3-5-1-4-1-4c-1-4-2-7-2-11 0-8 2-16 4-23v-2c1-1 1-2 1-2l1-1v-2s0-1 1-1v-1c-2 1-2 5-4 7h0l1-3v-1c0-2 2-4 2-6 1-2 1-4 2-6l4-3v-2h2c-2-2-3-3-3-4-1-2-1-3-1-5z" class="W"></path><path d="M320 591l2 1c0 1-1 1-2 2h0v-3z" class="L"></path><path d="M327 590l2-2c1 2 1 3 0 4v1l-1-2-1-1z" class="b"></path><path d="M317 590c1 0 2-1 2-1h1c1 1 2 1 3 1 0 0 0 1 1 1h-1l-1 1-2-1-1-1v1c-1 0-1 0-2-1z" class="T"></path><path d="M321 583c1 0 1-1 1-1h2c0 2 0 2 2 3v1h0c-1 1-1 1-2 1-1-1-2-3-3-4zm-16 38c2 1 5 2 6 3l1 2c-3-1-5-1-8-3l1-2z" class="E"></path><path d="M307 576v5-1l2-1c-1 2-2 5-2 7h-1c0-1-2-2-3-3h1l1-1c1-1 1-4 2-6z" class="D"></path><path d="M327 578l1-1 1 1 2 2v2c-2 1-4 2-5 4v-1c0-1 1-2 2-3 0-1 1-2 1-3l-2-1z" class="i"></path><path d="M307 598h3c0-1 1 0 2 0v1c0 1-2 2-3 2-2 0-5-1-7-2 2 0 3 0 5-1z" class="I"></path><path d="M302 595l1 1c1 1 2 1 4 2-2 1-3 1-5 1-1 0-2-1-4-1v-2c1 0 2-1 4-1z" class="C"></path><path d="M327 624h1 1c1 1 1 1 1 2-3 1-6 1-10 1l-1-1h1c0-1 1-1 2-1l5-1z" class="j"></path><path d="M336 631h0c1-1 2-2 2-3 2-3 4-7 5-10v-5l1 8-2 5-1 1c0 1 0 1-1 2-1 0-2 2-4 3v-1z" class="L"></path><path d="M336 617h1c0 1 0 1-1 2v2c-2 2-4 3-6 5 0-1 0-1-1-2h-1-1 0l-2-1h3l1-1h1 0c2-1 3-2 5-4l1-1h0z" class="F"></path><path d="M323 576l4 2 2 1c0 1-1 2-1 3-1 1-2 2-2 3-2-1-2-1-2-3l1-1-2-3c0-1 0-1-1-2h1z" class="X"></path><path d="M323 576l4 2 2 1c0 1-1 2-1 3l-2-3c-1-1-2-1-3-1 0-1 0-1-1-2h1z" class="n"></path><path d="M333 562c4 4 7 9 9 14l3 12c0 1 0 2-1 3l-1-4c0-2 0-4-2-5v-3c-1-6-5-11-9-15 1 0 2 1 3 2h1 0c-1-1-3-2-3-4z" class="C"></path><path d="M289 634h1c1-2 3-3 4-5h0 1c2 3 5 5 8 7h1-6-7l-1-1v-1h-1z" class="B"></path><path d="M291 636v-1c0-1 3-1 4-2 2 1 6 3 8 3h1-6-7z" class="M"></path><path d="M317 570s0-1 1-2h2c1 0 1 0 1 1 1 1 1 2 1 3 0 0 1 1 1 2l-1 1 1 1h-1c1 1 1 1 1 2l2 3-1 1h-2s0 1-1 1c-1-3-1-5 0-8h0-5l1-1v-4z" class="C"></path><path d="M317 570s0-1 1-2h2c1 0 1 0 1 1 0 2 0 2-1 3s-1 1-2 0l-1-2z" class="N"></path><path d="M304 623h0c-8-7-11-18-12-28 1 2 1 3 1 5a19.81 19.81 0 0 0 11 11h0v3c0 2-1 5 1 7h0 0l-1 2z" class="P"></path><path d="M307 576c0-2 1-3 2-4s4 0 5 0c0-2 0-4 1-6 2 0 4 0 6 1 3 1 6 6 7 9v1l-1 1-4-2-1-1 1-1c0-1-1-2-1-2 0-1 0-2-1-3 0-1 0-1-1-1h-2c-1 1-1 2-1 2v4l-1 1c-3 1-6 1-7 4l-2 1v1-5z" class="B"></path><path d="M317 590c0-1-1-1-1-2-1-1 0-4 0-5h1c1 0 2 1 2 2 1 2 3 2 4 2h1c1 0 1 0 2-1 0 2 0 3 1 4l1 1 1 2 1 1c1 2 2 1 3 2 0 1-1 3-3 3-2 1-5 2-8 2-2 1-5 2-7 4h0c1-3 5-3 7-5 1-1 2-4 2-5v-4c-1 0-1-1-1-1-1 0-2 0-3-1h-1s-1 1-2 1z" class="C"></path><path d="M328 591l1 2 1 1v2l-2 1c0-1-1-1-1-1-1 0-1-1-2-1h0v-2c1-1 2-1 3-2z" class="B"></path><path d="M302 595l-4-4c-1-5 0-8 3-12l4 3-1 1h-1c1 1 3 2 3 3h1c1 1 3 2 5 3 1 0 2 0 3 1v1c-1 2-3 5-3 6v1c-1 0-2-1-2 0h-3c-2-1-3-1-4-2l-1-1z" class="E"></path><path d="M303 583c1 1 3 2 3 3v3c-2 0-2 1-3 0-1 0-2-1-2-2s1-2 2-4z" class="W"></path><path d="M303 596c1-2 2-3 3-4h4v1 2c0 1 1 2 2 2v1c-1 0-2-1-2 0h-3c-2-1-3-1-4-2z" class="N"></path><path d="M304 614h1c1 0 2 1 3 1l4-1 5 2c1 0 3 1 4 1l1-1h0 1l2 1c0 1 1 1 1 2l3 3-1 1h-3l2 1h0l-5 1c-1 0-2 0-2 1h-1l1 1c-2 0-5 1-7 1l-1-2h0l-1-2c-1-1-4-2-6-3h0 0c-2-2-1-5-1-7z" class="M"></path><path d="M323 616l2 1c0 1 1 1 1 2l3 3-1 1h-3-2c-1-2-2-3-1-5l1-2z" class="C"></path><path d="M323 616l2 1c0 1 1 1 1 2s-1 1-2 1h0l-2-1v-1l1-2z" class="P"></path><path d="M311 624l1-1h-1c0-1-1-2-1-3h1c3 1 5 2 7 5 0 0 0 1 1 1l1 1c-2 0-5 1-7 1l-1-2h0l-1-2z" class="c"></path><path d="M292 552c-1-1 0-3 0-4l1 2 1-2c1 4 3 7 6 9 4 1 9 1 12-1 3-1 5-2 6-5 1-1 2-1 2-1 2 0 3 1 3 2l2 1c2 2 4 4 6 7 1 0 2 1 2 2 0 2 2 3 3 4h0-1c-1-1-2-2-3-2l-6-3c-6-2-12-3-19 0v1l-6 1-2 1c-1-1-1-1-2-1h-3v-2h2c-2-2-3-3-3-4-1-2-1-3-1-5z" class="E"></path><path d="M321 552c1 1 2 3 3 5h-1-3c-1 0-2 0-3-1 2-1 2-3 4-4z" class="H"></path><path d="M292 552c-1-1 0-3 0-4l1 2c1 4 2 8 6 10 2 1 4 1 7 1h1v1l-6 1-2 1c-1-1-1-1-2-1h-3v-2h2c-2-2-3-3-3-4-1-2-1-3-1-5z" class="U"></path><path d="M296 561c1 1 3 2 5 2l-2 1c-1-1-1-1-2-1h-3v-2h2z" class="D"></path><defs><linearGradient id="X" x1="283.887" y1="594.033" x2="297.259" y2="595.942" xlink:href="#B"><stop offset="0" stop-color="#101010"></stop><stop offset="1" stop-color="#2c2c2d"></stop></linearGradient></defs><path fill="url(#X)" d="M301 563l6-1c-3 1-5 3-7 4-7 7-12 17-13 27-2 9-1 18 2 27l2 6s1 1 1 2-2 3-3 4v-1c0-1 1-2 0-4h-1c-1 0-1 0-1 1l-1-1c-2 0-2 0-3 1v1c-1 0-1-1-1-2s3-4 3-5-1-4-1-4c-1-4-2-7-2-11 0-8 2-16 4-23v-2c1-1 1-2 1-2l1-1v-2s0-1 1-1v-1c-2 1-2 5-4 7h0l1-3v-1c0-2 2-4 2-6 1-2 1-4 2-6l4-3h3c1 0 1 0 2 1l2-1z"></path><path d="M294 563h3c1 0 1 0 2 1-5 3-8 8-10 13-1 2-2 5-3 7v-2c1-1 1-2 1-2l1-1v-2s0-1 1-1v-1c-2 1-2 5-4 7h0l1-3v-1c0-2 2-4 2-6 1-2 1-4 2-6l4-3z" class="M"></path><path d="M294 563h3c-5 5-8 10-11 16v-1c0-2 2-4 2-6 1-2 1-4 2-6l4-3z" class="P"></path><path d="M341 579v3c2 1 2 3 2 5l1 4c0 2 2 6 1 8v3c-1 1 0 2-1 3h1v3h-1v-1c-1 0-1 0-2 1-1 0-1 3-2 4 0 1 0 2-1 3h0c0 1-1 2-2 2 0 1 1 2 0 2 0 0 0 1-1 2v-2c1-1 1-1 1-2h-1 0l-1 1c-2 2-3 3-5 4h0-1l-3-3c0-1-1-1-1-2l-2-1h-1 0l-1 1c-1 0-3-1-4-1l-5-2-4 1c-1 0-2-1-3-1h-1v-3c2 1 3 1 4 1v-1h2c2-1 4-4 5-6h0c2-2 5-3 7-4 3 0 6-1 8-2 2 0 3-2 3-3h2v-2c0-1-1-1-1-1l-1-1h0c2-1 4 0 5 0l1-1v-5c-1-1 0-3-1-4v-1h1v1l1 2v2h1c0-1-1-2 0-3v-1-3z" class="H"></path><path d="M333 608c3-1 4-2 5-4h1v6c-1 0-1 0-2-1 0 1 0 1-1 1-1 1-2 1-3 1h-1l-1-1 2-2z" class="h"></path><path d="M331 610l2-2 2 2c1 0 1-1 2-1h0c0 1 0 1-1 1-1 1-2 1-3 1h-1l-1-1z" class="D"></path><path d="M337 609c1 1 1 1 2 1v5c0 1-1 2-2 2 0 1 1 2 0 2 0 0 0 1-1 2v-2c1-1 1-1 1-2h-1v-1-1h-3-1c0-1 0-1-1-1 0-1 0-1-1-2h-1v2h0c0-1-1-2-1-3 1 0 2-1 3-1l1 1h1c1 0 2 0 3-1 1 0 1 0 1-1z" class="F"></path><path d="M336 610l1 1c0 1 0 4-1 5v-1h-3c1-1 1-2 2-3h-1l-1 1h-1v-1l1-1c1 0 2 0 3-1z" class="J"></path><path d="M337 609c1 1 1 1 2 1v5c0 1-1 2-2 2 0 1 1 2 0 2 0 0 0 1-1 2v-2c1-1 1-1 1-2h-1v-1c1-1 1-4 1-5l-1-1c1 0 1 0 1-1z" class="I"></path><path d="M339 591v1c0 5-1 9-5 12l-1 1c-1 1-1 1-2 1-1 1-3 1-5 1v-2-1h2c3-1 6-2 8-5h0c2-3 2-5 2-7l1-1z" class="m"></path><path d="M329 614h0v-2h1c1 1 1 1 1 2 1 0 1 0 1 1h1 3v1 1h0l-1 1c-2 2-3 3-5 4h0-1l-3-3c0-1-1-1-1-2s3-2 4-3z" class="W"></path><path d="M333 596h2v-2c0-1-1-1-1-1l-1-1h0c2-1 4 0 5 0 0 2 0 4-2 7h0c-2 3-5 4-8 5h-2v1h-2l-1-2-1-2c3 0 6-1 8-2 2 0 3-2 3-3z" class="U"></path><path d="M323 603l1-1v1c2 0 3 1 4 1h-2v1h-2l-1-2z" class="Y"></path><path d="M315 605c2-2 5-3 7-4l1 2 1 2h2v2h0l1 1-1 1h0c-1 0-2 0-3 1v1l-1 1c-1 0-2-1-3-1-2-2-3-3-4-6z" class="M"></path><path d="M324 605h2v2h0c-1 0-2-1-2-2z" class="k"></path><defs><linearGradient id="Y" x1="338.788" y1="597.743" x2="344.786" y2="604.243" xlink:href="#B"><stop offset="0" stop-color="#5a5759"></stop><stop offset="1" stop-color="#71716f"></stop></linearGradient></defs><path fill="url(#Y)" d="M341 582c2 1 2 3 2 5l1 4c0 2 2 6 1 8v3c-1 1 0 2-1 3h1v3h-1v-1c-1 0-1 0-2 1-1 0-1 3-2 4v-2c2-4 1-8 1-11v-17z"></path><path d="M344 605c0-1-1-2-1-3-1-4-1-7 0-11 0-1-1-3 0-4l1 4c0 2 2 6 1 8v3c-1 1 0 2-1 3z" class="L"></path><path d="M315 605h0c1 3 2 4 4 6 1 0 2 1 3 1l1-1h2 3c0 1 1 2 1 3-1 1-4 2-4 3l-2-1h-1 0l-1 1c-1 0-3-1-4-1l-5-2-4 1c-1 0-2-1-3-1h-1v-3c2 1 3 1 4 1v-1h2c2-1 4-4 5-6z" class="B"></path><path d="M319 611c1 0 2 1 3 1v1 1c0 1-1 1-1 1-1-1-2-3-2-4z" class="i"></path><path d="M323 611h2c0 1-1 2-1 3-1 1-2 1-2 2h0-1v-1h0s1 0 1-1v-1-1l1-1z" class="S"></path><path d="M312 614c1-1 2-3 4-2 1 1 1 2 1 4l-5-2zm13-3h3c0 1 1 2 1 3-1 1-4 2-4 3l-2-1h-1c0-1 1-1 2-2 0-1 1-2 1-3z" class="D"></path><path d="M339 487h1 2 0l2 1-1 1-1 1c0 1-1 0-1 1h2c2 1 3 2 5 2l-1 1h1c1 2 1 3 1 4s0 1 1 2h2c2 0 4 0 6 1l4 1c2 1 4 3 6 4 2 2 4 4 7 4 1 2 3 4 5 6l5 5 2 3 6 12 1-2 2 1v6c-1 1 0 1-1 2v4c-1 4-2 7-6 9-3 2-5 2-9 1-3 0-6-3-9-6l-1 1v-4c0-1-1-2-1-2-2 2-3 5-5 7l-8 8c-5 6-9 14-9 22 0 3 1 6 2 9v-2c1 4 3 8 5 11 1 0 1 1 2 1 2 0 3 1 5 1l2 1h0c1 1 1 2 0 4h-2-5v1l1 1-1 2h0l-1 2c-1-1-1-1-1-2l-1-2c-1-1-2-3-4-3-1-2-1-3-2-5 0-1-1-2-2-3 1-2-1-6-1-8 1-1 1-2 1-3l-3-12c-2-5-5-10-9-14 0-1-1-2-2-2-2-3-4-5-6-7l-2-1c0-1-1-2-3-2 0 0-1 0-2 1l1-2v-4l-1-2c-1-2-3-3-4-4l-4-2c-2 0-3 0-4 1h-1l-1-1h-3l-2-1v-1l1-2c0-1 0-3 2-4v2l1-3c0-1 1-2 1-3l-1-1c2-3 3-6 6-8l-1-1 13-11c2-2 4-4 7-6h0c1 0 2-1 2-1 2-2 4-5 5-7l1-1s1-1 1-2h1 1z" class="W"></path><path d="M319 536h0c1 1 1 1 1 2h-1v-1-1z" class="N"></path><path d="M323 523l2 1v1c-1 0-2 1-3 2v3-5l1-1v-1z" class="L"></path><path d="M327 532h0 0c0-1 0-1-1-2 0-1 0-2 1-3h1 1c0 1 0 2-1 3l-1 2z" class="k"></path><path d="M349 528h1c-1 2-2 4-1 7h0-1v-2c0-1-1-1-1-2s1-2 2-3z" class="U"></path><path d="M344 539c0-1 0-2 1-2 1 1 0 4 0 5s0 2-1 3c0-1-1-1-1-2 1-1 1-3 1-4z" class="x"></path><path d="M331 554l-1-1v-1c2 0 3 0 4 1 1 0 1 1 2 2-2-1-3-1-5-1z" class="N"></path><path d="M340 522c1 2 0 9 0 12l-1-2-1-2v-2h1c0-2 1-3 1-4-1-1-1 0-1-1l1-1z" class="U"></path><path d="M339 521h0l1 1-1 1c0 1 0 0 1 1 0 1-1 2-1 4h-1c-1-2-1-3-1-5 0-1 1-2 2-2z" class="M"></path><path d="M352 519v1c1 2 1 3 1 5 0 1 0 1 1 1-1 0-1 0-2-1h0c-1-3-2-4-4-5h3 1v-1z" class="K"></path><path d="M334 527v-4s0-1 1-1l1-1v-2-2s1 0 1-1h1l-1 4c0 1-1 6-3 7z" class="U"></path><path d="M341 544c0-2 0-7 1-8 1 0 0 1 1 2 0 0 1 0 1 1s0 3-1 4h0c-1 0-1 0-2 1z" class="H"></path><path d="M329 527c2-1 3-1 5 0 1 0 1 0 1 1h-2v1h-1v1h-1v-1c-1 0-1 1-2 1v1h0l-1-1c1-1 1-2 1-3z" class="G"></path><path d="M348 520c2 1 3 2 4 5h0c-2 1-2 1-2 3h-1v-1-1s1 0 0-1h-1v-2l1-1c0-1-1-1-1-2h0z" class="M"></path><path d="M316 536l3 6 1 2c-1 0-1 0-1 1l-1-2c-1-2-3-3-4-4 0 0 0-1 1-1s1-1 1-2z" class="Y"></path><path d="M318 543c0-1 0-1 1-1l1 2c-1 0-1 0-1 1l-1-2z" class="I"></path><path d="M319 545c0-1 0-1 1-1 1 3 3 6 5 9l-2-1c0-1-1-2-3-2 0 0-1 0-2 1l1-2v-4z" class="F"></path><path d="M334 527c2-1 3-6 3-7 1 0 1 0 2 1-1 0-2 1-2 2 0 2 0 3 1 5v2h-1 0s0 1-1 1h0v-1c-1-1-1-1-3-1v-1h2c0-1 0-1-1-1h0z" class="m"></path><path d="M338 515c0-1 0-3-1-4s-2-1-3-3h1c2 1 4 2 5 3h1c0 1 0 2 1 3l2 1h-5c0-1-1 0-1 0z" class="h"></path><path d="M347 550v1l1-1v-2c0-1 1-1 1-1 1-1 2-2 2-3l1-1h0c1 0 2 1 2 2s-2 4-3 4h-1c0 1-1 1-1 2s-1 2-2 2h0-1c0-1 0-2 1-3z" class="a"></path><path d="M319 524v1c1 0 1 1 1 1h1l1-1v5l1 5-4-3 1-1c0-1-1-1-1-2-1 0-2-1-3-2l1-2s1-1 2-1z" class="g"></path><path d="M319 524v1c1 0 1 1 1 1h1c-1 1-2 0-2 1v2c-1 0-2-1-3-2l1-2s1-1 2-1z" class="I"></path><path d="M345 542v2c0 1 0 2 1 3 0 1 0 2 1 3-1 1-1 2-1 3v2c-1 1-1 2-1 4h0l-1-2c0-1 0-2-1-3v-7h0c0-1 1-1 1-2h0c1-1 1-2 1-3z" class="Q"></path><path d="M344 557l1-13c0 1 0 2 1 3 0 1 0 2 1 3-1 1-1 2-1 3v2c-1 1-1 2-1 4h0l-1-2z" class="H"></path><path d="M360 518h0c1-1 1-1 2-1s1 1 2 1l2 2v1l1 1-1 2c1 4 0 6 0 10h0 0v-3h0c-1-1 0-1 0-2-1 0-1-1-1-2s0-1-1-1l-1-3c-1-1 0-1 0-2l-1-1-2-2z" class="N"></path><path d="M366 521l1 1-1 2-1-2h0l1-1z" class="h"></path><path d="M360 518h0c1-1 1-1 2-1s1 1 2 1l2 2v1l-1 1h0l-2-1-1-1-2-2z" class="I"></path><path d="M343 507c1 0 1-1 2-1 0 2 1 3 1 5 1 1 1 2 1 3h-1v1h-1-1l-2-1c-1-1-1-2-1-3h-1c1-1 1 0 1-1h1c-1-1-1-1 0-2h0c0-1 0-2 1-2v1z" class="v"></path><path d="M343 507c1 0 1-1 2-1 0 2 1 3 1 5h-1s-1-1-1-2l-1-2z" class="R"></path><path d="M340 511c1-1 1 0 1-1h1c-1-1-1-1 0-2h0c0 2 1 4 3 6v1h-1l-2-1c-1-1-1-2-1-3h-1z" class="H"></path><path d="M331 554c2 0 3 0 5 1h1c2 1 4 2 4 4 1 3 1 5 1 8h0v1c-2-6-7-10-11-14z" class="F"></path><path d="M333 529c2 0 2 0 3 1v1h0c1 0 1-1 1-1h0 1l1 2 1 2v1c-5 0-9 1-13-3l1-2 1 1h0v-1c1 0 1-1 2-1v1h1v-1h1z" class="Y"></path><path d="M338 530l1 2h0c-1 1-1 1-2 0l-1-1h0c1 0 1-1 1-1h0 1z" class="D"></path><path d="M333 529c2 0 2 0 3 1v1c0 1-1 2-3 2-1 0-3-2-4-3 1 0 1-1 2-1v1h1v-1h1z" class="M"></path><path d="M328 509c0 1 0 2-1 3v2h0l-1 1-3-3v-1h-1c-1 1-1 2-2 2v1h-1l-2 2h-2l-1 1h1l-1 1h3v1c-2 0-2 0-3 1-3 0-5 3-7 3 2-5 8-9 12-12 2 0 3-1 5-1v-1c1 0 1 1 2 1l2-1z" class="Y"></path><path d="M367 522l2 2v-1l1 1h0l1 1h1v1l1 1h1c-1 2-1 2-1 4l2 2c-1 0-1 1-2 1l-3-3c-2 1-3 3-4 5l-1 2c0-1 1-3 1-4h0c0-4 1-6 0-10l1-2z" class="I"></path><path d="M370 524h0l1 1h1v1l1 1c-1 0-2 0-3 1-1 0-1 0-1 1l-1-1v-1l1-1 1-1v-1z" class="K"></path><path d="M345 515h1v-1h1 3v1c-1 0-1 1-1 2h0l1 1h0c0 1 1 1 1 2h-3 0c-3 0-5 0-9 1h0c-1-1-1-1-2-1l1-4v-1s1-1 1 0h5 1z" class="V"></path><path d="M328 509h0c1 0 2-1 3-1h2v4l-1 1c-1 2-3 4-5 5l-2 2c-1-1-2-2-2-3l-3-3v-1c1 0 1-1 2-2h1v1l3 3 1-1h0v-2c1-1 1-2 1-3z" class="D"></path><path d="M312 523l5 2-1 2c1 1 2 2 3 2 0 1 1 1 1 2l-1 1-1-1-2 1v4h0c0 1 0 2-1 2s-1 1-1 1l-4-2v-1s1 0 1-1h1c-1 0-1 0-1-1s0-2 1-4v-1c-1-2-1-4 0-6z" class="Z"></path><path d="M316 527c1 1 2 2 3 2 0 1 1 1 1 2l-1 1-1-1c-1-1-2-1-2-2v-1-1z" class="D"></path><path d="M312 530v-1l1 1c1 1 1 2 1 3-1 0-1-1-2-1 0 1 1 2 0 3h0c-1 0-1 0-1-1s0-2 1-4z" class="L"></path><path d="M312 535c2 0 3 0 4 1h0c0 1 0 2-1 2s-1 1-1 1l-4-2v-1s1 0 1-1h1 0z" class="G"></path><path d="M312 523l5 2-1 2v1h-1l-1 1v1h-1l-1-1v1-1c-1-2-1-4 0-6z" class="r"></path><path d="M351 508h5c-2 1-1 2-1 4 1 2 3 4 5 6l2 2h-3l1 3h-1l-1-3v-1c0-1 0-1-1-2l-1-1c0 1 0 1-1 2h-1c-1 0-1 1-2 2v-1 1h-1c0-1-1-1-1-2h0l-1-1h0c0-1 0-2 1-2v-1h-3c1-1 1-2 1-2 1-2 2-3 3-4z" class="M"></path><path d="M351 508h5c-2 1-1 2-1 4 0-1-1-2-1-3h0c-2 1-2 1-3 3 0 1-1 2-1 3s2 3 2 4v1h-1c0-1-1-1-1-2h0l-1-1h0c0-1 0-2 1-2v-1h-3c1-1 1-2 1-2 1-2 2-3 3-4z" class="I"></path><path d="M341 544c1-1 1-1 2-1h0c0 1 1 1 1 2h0c0 1-1 1-1 2h0v7 3 1c0 2 1 8 0 10l-1-1c0-3 0-5-1-8 0-2-2-3-4-4 2 0 2 0 3 1h1v-3h-1-1-1-1v-1c-1-1-1-2-1-4h-1 1c-1-2-1-3-2-4l1-1 2 2 1-1h1c0 1 0 2 1 3h1v-3z" class="U"></path><path d="M343 558l-1-1v-3h1v3 1z" class="M"></path><path d="M350 528c0-2 0-2 2-3 1 1 1 1 2 1 2 0 3 0 4 2l1 1h0l1-2h0c1 0 2 0 2 1 0 2-1 5-3 6s-4 2-6 1h-3-1 0c-1-3 0-5 1-7z" class="g"></path><path d="M353 529h1c1 1 2 2 3 2h0 0l-1 2h-2c-1 0-2-1-2-2s0-1 1-2z" class="H"></path><path d="M306 527h3l1-1-1-1h-1v-1c1 0 1-1 3-1h1c-1 2-1 4 0 6v1c-1 2-1 3-1 4s0 1 1 1h-1c0 1-1 1-1 1v1c-2 0-3 0-4 1h-1l-1-1h-3l-2-1v-1l1-2c0-1 0-3 2-4v2c1 1 1 1 1 2 1-1 1-2 2-3 0-1 0-2 1-3z" class="g"></path><path d="M304 536c2-1 2-2 2-4 0 0 1-1 1-2h2 0l1 1-1 3-1 1-2 1c-1 0-1 1-2 0z" class="r"></path><path d="M309 530l1 1-1 3-1 1v-4l1-1z" class="b"></path><path d="M305 530h0c1 0 3-1 4-1h1v1h-1-2c0 1-1 2-1 2 0 2 0 3-2 4v1h-3l-2-1v-1l1-2c0-1 0-3 2-4v2c1 1 1 1 1 2 1-1 1-2 2-3z" class="D"></path><path d="M317 516l2-2h1l3 3c0 1 1 2 2 3-1 1-1 2-2 2v1 1l-1 1-1 1h-1s0-1-1-1v-1c-1 0-2 1-2 1l-5-2h-1c-2 0-2 1-3 1v1h1l1 1-1 1h-3c0-1 0-3 1-4 2 0 4-3 7-3 1-1 1-1 3-1v-1h-3l1-1h-1l1-1h2z" class="J"></path><path d="M314 520c1-1 1-1 3-1l1 1h1v1h-1c-1 0-1 0-2-1-1 0-2 1-3 0h1z" class="H"></path><path d="M315 517h-1l1-1h2c1 1 3 1 4 2 0 1 1 1 1 2h0-2v-1c-2-2-4-2-5-2z" class="m"></path><path d="M317 516l2-2h1l3 3c-1 0-1 1-2 1-1-1-3-1-4-2z" class="J"></path><path d="M323 517c0 1 1 2 2 3-1 1-1 2-2 2v1 1l-1 1-1 1h-1s0-1-1-1v-1l1-2v-2h2 0c0-1-1-1-1-2 1 0 1-1 2-1z" class="F"></path><path d="M322 520c1 1 1 2 1 4l-1 1c0-1-1-1-1-3h-1v-2h2 0z" class="r"></path><path d="M320 520h2c0 1 0 1-1 2h-1v-2z" class="g"></path><path d="M323 517c0 1 1 2 2 3-1 1-1 2-2 2v1 1c0-2 0-3-1-4 0-1-1-1-1-2 1 0 1-1 2-1z" class="b"></path><defs><linearGradient id="Z" x1="332.559" y1="496.576" x2="330.858" y2="504.293" xlink:href="#B"><stop offset="0" stop-color="#302f31"></stop><stop offset="1" stop-color="#494a46"></stop></linearGradient></defs><path fill="url(#Z)" d="M339 487h1 2 0l2 1-1 1-1 1c0 1-1 0-1 1h2c2 1 3 2 5 2l-1 1h1c1 2 1 3 1 4s0 1 1 2c-1 0-1 0-2 1 0 0-1 1-1 2-1 1-1 2-2 3-1 0-1 1-2 1v-1h-1v-2-3c-1-1-2-1-4-1-5 0-9 1-14 4-2 1-4 2-6 4-3 3-6 5-9 8l-1-1 13-11c2-2 4-4 7-6h0c1 0 2-1 2-1 2-2 4-5 5-7l1-1s1-1 1-2h1 1z"></path><path d="M340 494h1c1 1 0 2 0 4 1 1 1 2 2 3l-1 3v-3c-1-1-2-1-4-1 1 0 2 0 3-1-1 0-1-1-2-1h-3v-1l4-2h0v-1z" class="I"></path><path d="M334 496c1-1 1-1 1-2 1-1 1-2 2-2 1 1 1 1 3 2v1h0l-4 2h-2v-1z" class="B"></path><path d="M338 487h1c-1 1-1 1-1 2s1 2 2 3h2c0 2-1 4-1 6 0-2 1-3 0-4h-1c-2-1-2-1-3-2v-1c0-1 0-1-1-2 0 0 1-1 1-2h1z" class="Y"></path><path d="M339 487h1 2 0l2 1-1 1-1 1c0 1-1 0-1 1h2l-1 1h-2c-1-1-2-2-2-3s0-1 1-2z" class="H"></path><path d="M336 489c1 1 1 1 1 2v1c-1 0-1 1-2 2 0 1 0 1-1 2s-1 1-3 2l-1-1c2-2 4-5 5-7l1-1z" class="G"></path><path d="M343 491c2 1 3 2 5 2l-1 1h1c1 2 1 3 1 4s0 1 1 2c-1 0-1 0-2 1 0 0-1 1-1 2-1 1-1 2-2 3-1 0-1 1-2 1v-1h-1v-2l1-3c-1-1-1-2-2-3 0-2 1-4 1-6l1-1z" class="L"></path><path d="M343 500c1-1 1-2 2-3l1 1c1 1 2 1 3 0 0 1 0 1 1 2-1 0-1 0-2 1h-1c-1 0-1 1-2 1l-2-2z" class="r"></path><path d="M343 500l2 2c1 0 1-1 2-1h1s-1 1-1 2c-1 1-1 2-2 3-1 0-1 1-2 1v-1h-1v-2l1-3v-1z" class="S"></path><defs><linearGradient id="a" x1="385.563" y1="516.564" x2="347.158" y2="505.226" xlink:href="#B"><stop offset="0" stop-color="#d3d2d4"></stop><stop offset="1" stop-color="#fbfafb"></stop></linearGradient></defs><path fill="url(#a)" d="M350 500h2c2 0 4 0 6 1l4 1c2 1 4 3 6 4 2 2 4 4 7 4 1 2 3 4 5 6l5 5 2 3c-1 1-2 1-2 2h-1v1c-1-1-2-2-2-3-1-1-1-2-2-3 0 1-1 1-1 1 0 1 1 2 0 3l-2-1v1h-1v1c-1 1-1 1-2 1h-1l-1-1v-1h-1l-1-1h0l-1-1v1l-2-2-1-1v-1l-2-2c-1 0-1-1-2-1s-1 0-2 1h0c-2-2-4-4-5-6 0-2-1-3 1-4h-5c-1 1-2 2-3 4 0 0 0 1-1 2 0-1 0-2-1-3 0-2-1-3-1-5 1-1 1-2 2-3 0-1 1-2 1-2 1-1 1-1 2-1z"></path><path d="M362 509c2 1 5 1 7 3l-2 1c-1-1-1-2-2-2-1 1-1 2-1 3l-1-1v-2h-1v-2z" class="G"></path><path d="M385 521l2 3c-1 1-2 1-2 2h-1v1c-1-1-2-2-2-3v-1c2 0 2-1 3-2z" class="N"></path><path d="M364 514c0-1 0-2 1-3 1 0 1 1 2 2h1c0 1 0 1-1 1l-2 3-1 1c-1 0-1-1-2-1v-2c1 0 1-1 2-1z" class="B"></path><path d="M347 503c1 0 1 0 2 1-1 1-2 0-3 2 0 1 0 1 1 1 2 0 3 1 4 1-1 1-2 2-3 4 0 0 0 1-1 2 0-1 0-2-1-3 0-2-1-3-1-5 1-1 1-2 2-3z" class="U"></path><path d="M356 508l5 1h1v2h1v2l1 1c-1 0-1 1-2 1v2c-1 0-1 0-2 1h0c-2-2-4-4-5-6 0-2-1-3 1-4z" class="D"></path><path d="M361 509h1v2h1v2l1 1c-1 0-1 1-2 1-2 0-3 0-4-1v-1c1-1 2-2 3-4h0z" class="q"></path><path d="M362 511h1v2l1 1c-1 0-1 1-2 1 0-1-1-1-2-2l2-2h0z" class="S"></path><path d="M369 512l3 2c3 2 6 4 8 7 0 1-1 1-1 1 0 1 1 2 0 3l-2-1v1h-1v1c-1 1-1 1-2 1h-1l-1-1v-1h-1l-1-1h0l-1-1v1l-2-2-1-1v-1l-2-2 1-1 2-3c1 0 1 0 1-1h-1l2-1z" class="G"></path><path d="M373 520c1-1 1-1 2-1 1 1 0 2 0 3l-1-1c0-1-1-1-1-1z" class="O"></path><path d="M376 523v-2-1h1 1v2h1c0 1 1 2 0 3l-2-1h0-1v-1z" class="H"></path><path d="M369 512l3 2-1 1c1 1 1 1 2 1v1h-2 0c0-1 0-1-1-2h0l-3-1c1 0 1 0 1-1h-1l2-1z" class="D"></path><path d="M370 521c0-1 0-1 1-2l1 1h1s1 0 1 1l1 1s0 1 1 1c0 1 0 1-1 1s-1 0-2-1v-1c-1-1-2-1-3-1z" class="s"></path><path d="M367 514l3 1h0c0 2-1 3-1 4h-1s-1 0-2 1h0l-2-2 1-1 2-3z" class="L"></path><path d="M365 517c1 0 2 0 3 1h0v1s-1 0-2 1h0l-2-2 1-1z" class="N"></path><path d="M368 519h1c0 1-1 3 0 3s1-1 1-1c1 0 2 0 3 1v1c1 1 1 1 2 1s1 0 1-1h0v1h1 0v1h-1v1c-1 1-1 1-2 1h-1l-1-1v-1h-1l-1-1h0l-1-1v1l-2-2-1-1v-1h0c1-1 2-1 2-1z" class="O"></path><path d="M370 524c2 0 4 1 6 2-1 1-1 1-2 1h-1l-1-1v-1h-1l-1-1z" class="r"></path><defs><linearGradient id="b" x1="342.843" y1="573.895" x2="377.34" y2="567.797" xlink:href="#B"><stop offset="0" stop-color="#d4d3d3"></stop><stop offset="1" stop-color="#fefeff"></stop></linearGradient></defs><path fill="url(#b)" d="M349 556v-2c1 0 0 0 1-1 0 0-1 0-1-1l1-1c2 1 1 1 2 2h0l-1 1h0 0 2v-2c1-1 2-2 4-2 1-1 2-3 4-3 1-1 2-1 2-2l2-5 1 2c0-2 2-6 3-8h2c1 1 1 2 0 2l-1 3v1c-1 0-1 0-1 1h0v2h1c-1 1-1 2-1 3-2 2-3 5-5 7l-8 8c-5 6-9 14-9 22 0 3 1 6 2 9v-2c1 4 3 8 5 11 1 0 1 1 2 1 2 0 3 1 5 1l2 1h0c1 1 1 2 0 4h-2-5v1l1 1-1 2h0l-1 2c-1-1-1-1-1-2l-1-2c-1-1-2-3-4-3-1-2-1-3-2-5 0-1-1-2-2-3 1-2-1-6-1-8 1-1 1-2 1-3l-3-12c0 1 0 1 1 1v-1l-1-8v-1h0l1 1c1-2 0-8 0-10v-1-3c1 1 1 2 1 3l1 2h0c0-2 0-3 1-4v3l1 1 2-3z"></path><path d="M346 555v3l1 1-1 2v2h0c-1-1-1-3-1-4 0-2 0-3 1-4z" class="T"></path><path d="M346 563v-2c1 1 1 2 2 2 0 1 0 1 1 2-2 2-2 5-3 8v-10z" class="D"></path><path d="M369 543h1c-1 1-1 2-1 3-2 2-3 5-5 7 0-3 1-4 2-6s2-3 3-4z" class="U"></path><path d="M344 573v3c1 3 1 6 1 9v3h0l-3-12c0 1 0 1 1 1v-1c1-1 1-2 1-3z" class="o"></path><path d="M343 554c1 1 1 2 1 3l1 2c0 2 0 7-1 9v5c0 1 0 2-1 3l-1-8v-1h0l1 1c1-2 0-8 0-10v-1-3z" class="p"></path><path d="M349 556c3-1 5-1 8-2-3 3-6 7-8 11-1-1-1-1-1-2-1 0-1-1-2-2l1-2 2-3z" class="B"></path><path d="M345 588h0c0 7 3 15 8 19 1 1 2 1 3 2l1 1-1 2h0l-1 2c-1-1-1-1-1-2l-1-2c-1-1-2-3-4-3-1-2-1-3-2-5 0-1-1-2-2-3 1-2-1-6-1-8 1-1 1-2 1-3z" class="I"></path><path d="M354 612v-2h1l1 2h0l-1 2c-1-1-1-1-1-2z" class="D"></path><path d="M349 590c1 4 3 8 5 11 1 0 1 1 2 1 2 0 3 1 5 1l2 1h0c1 1 1 2 0 4h-2l-2-1c-1 0-3-1-4-2-3-3-6-9-6-13v-2z" class="Y"></path><path d="M363 604c1 1 1 2 0 4h-2l-2-1s0-1 1-2 1-1 3-1z" class="G"></path><path d="M380 521c1 1 1 2 2 3 0 1 1 2 2 3v-1h1c0-1 1-1 2-2l6 12 1-2 2 1v6c-1 1 0 1-1 2v4c-1 4-2 7-6 9-3 2-5 2-9 1-3 0-6-3-9-6l-1 1v-4c0-1-1-2-1-2 0-1 0-2 1-3h0c0-2 1-5 2-7l1-2c1 0 1-1 2-1l-2-2c0-2 0-2 1-4 1 0 1 0 2-1v-1h1v-1l2 1c1-1 0-2 0-3 0 0 1 0 1-1z" class="W"></path><path d="M372 541c1 0 2 1 4 2l2-1c1 0 0 0 0 1s-2 1-3 1h0c0-1-1-1-1-1h-3l1-2z" class="a"></path><path d="M370 543h1v1 7l-1 1v-4c0-1-1-2-1-2 0-1 0-2 1-3h0z" class="C"></path><path d="M372 536c1 0 1 1 2 2h1l-3 3-1 2v1-1h-1c0-2 1-5 2-7z" class="G"></path><path d="M384 538l1-1h3c0 1 0 2 1 3h0c-1 2-1 3-1 5v1h-2l-1-1c1 0 1 0 1-1 0-2-1-4-2-6z" class="e"></path><path d="M388 545s1 1 2 0h1c0 1 0 2-1 3 0 1-1 1-3 1v1c-3 0-7 1-9 0-1-1-2-2-2-3v-1h0 1c0 2 1 2 3 3h3l3-2v-1h2v-1z" class="B"></path><path d="M380 521c1 1 1 2 2 3 0 1 1 2 2 3v-1h1c0-1 1-1 2-2l6 12 1-2 2 1v6c-1 1 0 1-1 2-1 0-3 0-4-1l-2-2h0c-1-1-1-2-1-3h-3l-1 1c-1 0-2-1-3-1-2-1-4 1-6 1h-1c-1-1-1-2-2-2l1-2c1 0 1-1 2-1l-2-2c0-2 0-2 1-4 1 0 1 0 2-1v-1h1v-1l2 1c1-1 0-2 0-3 0 0 1 0 1-1z" class="E"></path><path d="M377 528c1 0 2 1 3 1l-2 3c-1 0-1 1-2 1h-1l-2-2h1l3-3z" class="o"></path><path d="M380 521c1 1 1 2 2 3 0 1 1 2 2 3v2c0 1 0 0-1 0v1c-1 0-2 0-3-1h0c-1 0-2-1-3-1l-3 3h-1c0-2 0-2 1-4 1 0 1 0 2-1v-1h1v-1l2 1c1-1 0-2 0-3 0 0 1 0 1-1z" class="F"></path><path d="M377 528c1-1 1-1 2-1 1 1 2 1 3 2l1 1c-1 0-2 0-3-1h0c-1 0-2-1-3-1z" class="f"></path><path d="M376 526v-1h1v-1l2 1c0 1-1 1-2 1v1h2c-1 0-1 0-2 1l-3 3h-1c0-2 0-2 1-4 1 0 1 0 2-1z" class="Q"></path><path d="M387 524l6 12 1-2 2 1v6c-1 1 0 1-1 2-1 0-3 0-4-1v-1c0-3-2-8-4-10h-1l-1 2h-1v-4-2-1h1c0-1 1-1 2-2z" class="H"></path><path d="M394 534l2 1v6c-1 1 0 1-1 2 0-2-1-5-2-7l1-2z" class="v"></path></svg>